package com.tencent.resourceloader.resclass;



import com.tencent.nk.util.exception.ResLoadFailException;

import com.tencent.wea.xlsRes.ResMasterPath;

import com.tencent.wea.xlsRes.keywords.MasterPatchConfigEnum;

import com.tencent.resourceloader.ResLoader;

import com.tencent.resourceloader.ResTable;

import com.tencent.resourceloader.ResHolder;

import org.apache.logging.log4j.LogManager;

import org.apache.logging.log4j.Logger;



import java.util.Map;

import java.util.Optional;



public class MainMaterPathConfig extends ResTable<ResMasterPath.MainMaterPathConfig> {



    private static final Logger LOGGER = LogManager.getLogger(MainMaterPathConfig.class);



    Map<MasterPatchConfigEnum, ResMasterPath.MainMaterPathConfig> internalDataMap;



    public MainMaterPathConfig() {

        internalDataMap = dataMap;

        generateKeyMetaData(ResMasterPath.MainMaterPathConfig.newBuilder());

    }



    public static MainMaterPathConfig getInstance() {

        return (MainMaterPathConfig) ResLoader.getResHolder().getResTableInstance("MainMaterPathConfig");

    }



    public static MainMaterPathConfig getInLoadingInstance(ResHolder resHolder) {

        return (MainMaterPathConfig) resHolder.allResMap.get("MainMaterPathConfig");

    }



    @Override

    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public Class getMessageClass() {

        return ResMasterPath.MainMaterPathConfig.class;

    }



    public ResMasterPath.MainMaterPathConfig get(MasterPatchConfigEnum id)

    {

        return Optional.ofNullable(internalDataMap.get(id))

        .orElse(null);

    }

}

