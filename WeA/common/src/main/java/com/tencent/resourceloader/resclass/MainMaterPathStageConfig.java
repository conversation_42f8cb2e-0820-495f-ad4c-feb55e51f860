package com.tencent.resourceloader.resclass;



import com.tencent.nk.util.exception.ResLoadFailException;

import com.tencent.wea.xlsRes.ResMasterPath;

import com.tencent.resourceloader.ResLoader;

import com.tencent.resourceloader.ResTable;

import com.tencent.resourceloader.ResHolder;

import org.apache.logging.log4j.LogManager;

import org.apache.logging.log4j.Logger;



import java.util.Map;

import java.util.Optional;



public class MainMaterPathStageConfig extends ResTable<ResMasterPath.MainMaterPathStageConfig> {



    private static final Logger LOGGER = LogManager.getLogger(MainMaterPathStageConfig.class);



    Map<Integer, ResMasterPath.MainMaterPathStageConfig> internalDataMap;



    public MainMaterPathStageConfig() {

        internalDataMap = dataMap;

        generateKeyMetaData(ResMasterPath.MainMaterPathStageConfig.newBuilder());

    }



    public static MainMaterPathStageConfig getInstance() {

        return (MainMaterPathStageConfig) ResLoader.getResHolder().getResTableInstance("MainMaterPathStageConfig");

    }



    public static MainMaterPathStageConfig getInLoadingInstance(ResHolder resHolder) {

        return (MainMaterPathStageConfig) resHolder.allResMap.get("MainMaterPathStageConfig");

    }



    @Override

    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public Class getMessageClass() {

        return ResMasterPath.MainMaterPathStageConfig.class;

    }



    public ResMasterPath.MainMaterPathStageConfig get(Integer id)

    {

        return Optional.ofNullable(internalDataMap.get(id))

        .orElse(null);

    }

}

