package com.tencent.resourceloader.resclass;



import com.tencent.nk.util.exception.ResLoadFailException;

import com.tencent.wea.xlsRes.ResMasterPath;

import com.tencent.resourceloader.ResLoader;

import com.tencent.resourceloader.ResTable;

import com.tencent.resourceloader.ResHolder;

import org.apache.logging.log4j.LogManager;

import org.apache.logging.log4j.Logger;



import java.util.Map;

import java.util.Optional;



public class MasterPathLockConfig extends ResTable<ResMasterPath.MasterPathLockConfig> {



    private static final Logger LOGGER = LogManager.getLogger(MasterPathLockConfig.class);



    Map<Integer, ResMasterPath.MasterPathLockConfig> internalDataMap;



    public MasterPathLockConfig() {

        internalDataMap = dataMap;

        generateKeyMetaData(ResMasterPath.MasterPathLockConfig.newBuilder());

    }



    public static MasterPathLockConfig getInstance() {

        return (MasterPathLockConfig) ResLoader.getResHolder().getResTableInstance("MasterPathLockConfig");

    }



    public static MasterPathLockConfig getInLoadingInstance(ResHolder resHolder) {

        return (MasterPathLockConfig) resHolder.allResMap.get("MasterPathLockConfig");

    }



    @Override

    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public Class getMessageClass() {

        return ResMasterPath.MasterPathLockConfig.class;

    }



    public ResMasterPath.MasterPathLockConfig get(Integer id)

    {

        return Optional.ofNullable(internalDataMap.get(id))

        .orElse(null);

    }

}

