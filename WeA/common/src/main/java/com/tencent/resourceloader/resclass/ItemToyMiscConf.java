package com.tencent.resourceloader.resclass;



import com.tencent.nk.util.exception.ResLoadFailException;

import com.tencent.wea.xlsRes.ResBackpackItem;

import com.tencent.resourceloader.ResLoader;

import com.tencent.resourceloader.ResTable;

import com.tencent.resourceloader.ResHolder;

import org.apache.logging.log4j.LogManager;

import org.apache.logging.log4j.Logger;



import java.util.Map;

import java.util.Optional;



public class ItemToyMiscConf extends ResTable<ResBackpackItem.ItemToyMiscConf> {



    private static final Logger LOGGER = LogManager.getLogger(ItemToyMiscConf.class);



    Map<String, ResBackpackItem.ItemToyMiscConf> internalDataMap;



    public ItemToyMiscConf() {

        internalDataMap = dataMap;

        generateKeyMetaData(ResBackpackItem.ItemToyMiscConf.newBuilder());

    }



    public static ItemToyMiscConf getInstance() {

        return (ItemToyMiscConf) ResLoader.getResHolder().getResTableInstance("ItemToyMiscConf");

    }



    public static ItemToyMiscConf getInLoadingInstance(ResHolder resHolder) {

        return (ItemToyMiscConf) resHolder.allResMap.get("ItemToyMiscConf");

    }



    @Override

    public void checker(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public void afterCheck(ResHolder resHolder) throws ResLoadFailException {

    }



    @Override

    public Class getMessageClass() {

        return ResBackpackItem.ItemToyMiscConf.class;

    }



    public ResBackpackItem.ItemToyMiscConf get(String stringId)

    {

        return Optional.ofNullable(internalDataMap.get(stringId))

        .orElse(null);

    }

}

