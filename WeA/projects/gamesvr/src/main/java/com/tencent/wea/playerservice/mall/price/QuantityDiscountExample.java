package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.mall.price.modifiers.QuantityDiscountPriceModifier;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResKeywords.CommodityModifyPriceType;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountTier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 数量折扣功能示例和验证
 * 这个类展示了如何使用数量折扣功能
 */
public class QuantityDiscountExample {
    private static final Logger LOGGER = LogManager.getLogger(QuantityDiscountExample.class);
    
    /**
     * 创建一个带有数量折扣配置的示例商品
     */
    public static MallCommodity createExampleCommodity() {
        // 创建价格阶梯
        QuantityDiscountTier tier1 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(900)  // 购买1个及以上：单价900
                .build();
        
        QuantityDiscountTier tier2 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(5)
                .setFixedPrice(800)  // 购买5个及以上：单价800
                .build();
        
        QuantityDiscountTier tier3 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(10)
                .setFixedPrice(700)  // 购买10个及以上：单价700
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier1)
                .addTiers(tier2)
                .addTiers(tier3)
                .build();
        
        return MallCommodity.newBuilder()
                .setCommodityId(1001)
                .setPrice(1000)  // 基础价格1000
                .setModifyPriceType(CommodityModifyPriceType.CMPT_QuantityDiscount)
                .setQuantityDiscountParams(params)
                .build();
    }
    
    /**
     * 验证数量折扣功能
     */
    public static void validateQuantityDiscount() {
        LOGGER.info("开始验证数量折扣功能...");
        
        try {
            // 初始化价格修改器
            PriceModifierInitializer.initialize();
            
            // 创建示例商品
            MallCommodity commodity = createExampleCommodity();
            
            // 创建价格计算器
            PriceCalculator calculator = new PriceCalculator();
            
            // 模拟玩家（这里使用null，实际使用时需要真实的Player对象）
            Player player = null; // 在实际环境中需要提供真实的Player对象
            
            // 测试不同购买数量的价格
            int[] testQuantities = {1, 3, 5, 8, 10, 15, 20};
            
            LOGGER.info("商品配置：");
            LOGGER.info("  商品ID: {}", commodity.getCommodityId());
            LOGGER.info("  基础价格: {}", commodity.getPrice());
            LOGGER.info("  价格修改类型: {}", commodity.getModifyPriceType());
            LOGGER.info("  价格阶梯数量: {}", commodity.getQuantityDiscountParams().getTiersCount());
            
            LOGGER.info("价格阶梯配置：");
            for (int i = 0; i < commodity.getQuantityDiscountParams().getTiersCount(); i++) {
                QuantityDiscountTier tier = commodity.getQuantityDiscountParams().getTiers(i);
                LOGGER.info("  阶梯{}: 购买{}个及以上，单价{}", 
                           i + 1, tier.getMinBuyCount(), tier.getFixedPrice());
            }
            
            LOGGER.info("价格计算测试：");
            for (int quantity : testQuantities) {
                try {
                    // 注意：这里player为null，在实际环境中会报错
                    // 这只是为了展示配置结构，实际使用时需要提供真实的Player对象
                    if (player != null) {
                        long finalPrice = calculator.calculateFinalPrice(
                            player, commodity, quantity, false, "test-bill");
                        LOGGER.info("  购买{}个：单价{}", quantity, finalPrice);
                    } else {
                        // 手动计算预期价格用于验证
                        long expectedPrice = calculateExpectedPrice(commodity, quantity);
                        LOGGER.info("  购买{}个：预期单价{}", quantity, expectedPrice);
                    }
                } catch (Exception e) {
                    LOGGER.error("计算购买{}个商品的价格时出错: {}", quantity, e.getMessage());
                }
            }
            
            LOGGER.info("数量折扣功能验证完成");
            
        } catch (Exception e) {
            LOGGER.error("验证数量折扣功能时出错: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 手动计算预期价格（用于验证逻辑）
     */
    private static long calculateExpectedPrice(MallCommodity commodity, int quantity) {
        QuantityDiscountParams params = commodity.getQuantityDiscountParams();
        
        // 找到适用的最高阶梯
        QuantityDiscountTier bestTier = null;
        for (QuantityDiscountTier tier : params.getTiersList()) {
            if (quantity >= tier.getMinBuyCount()) {
                if (bestTier == null || tier.getMinBuyCount() > bestTier.getMinBuyCount()) {
                    bestTier = tier;
                }
            }
        }
        
        if (bestTier != null) {
            return bestTier.getFixedPrice();
        } else {
            // 没有适用的阶梯，使用基础价格
            return commodity.getDiscountPrice() > 0 ? 
                commodity.getDiscountPrice() : commodity.getPrice();
        }
    }
    
    /**
     * 展示配置示例
     */
    public static void showConfigurationExamples() {
        LOGGER.info("=== 数量折扣配置示例 ===");
        
        LOGGER.info("示例1：基础数量折扣");
        LOGGER.info("Excel配置：");
        LOGGER.info("  commodityId: 1001");
        LOGGER.info("  price: 1000");
        LOGGER.info("  modifyPriceType: 1");
        LOGGER.info("  quantityDiscountParams: {");
        LOGGER.info("    \"tiers\": [");
        LOGGER.info("      {\"minBuyCount\": 1, \"fixedPrice\": 950},");
        LOGGER.info("      {\"minBuyCount\": 5, \"fixedPrice\": 900},");
        LOGGER.info("      {\"minBuyCount\": 10, \"fixedPrice\": 850}");
        LOGGER.info("    ]");
        LOGGER.info("  }");
        LOGGER.info("效果：购买1-4个单价950，购买5-9个单价900，购买10个及以上单价850");
        
        LOGGER.info("");
        LOGGER.info("示例2：大幅度折扣");
        LOGGER.info("Excel配置：");
        LOGGER.info("  commodityId: 1002");
        LOGGER.info("  price: 1000");
        LOGGER.info("  modifyPriceType: 1");
        LOGGER.info("  quantityDiscountParams: {");
        LOGGER.info("    \"tiers\": [");
        LOGGER.info("      {\"minBuyCount\": 1, \"fixedPrice\": 1000},");
        LOGGER.info("      {\"minBuyCount\": 10, \"fixedPrice\": 800},");
        LOGGER.info("      {\"minBuyCount\": 50, \"fixedPrice\": 600}");
        LOGGER.info("    ]");
        LOGGER.info("  }");
        LOGGER.info("效果：购买1-9个原价1000，购买10-49个单价800，购买50个及以上单价600");
    }
}
