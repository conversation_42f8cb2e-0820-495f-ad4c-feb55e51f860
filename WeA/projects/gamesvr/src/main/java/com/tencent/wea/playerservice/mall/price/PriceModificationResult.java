package com.tencent.wea.playerservice.mall.price;

/**
 * 价格修改结果
 * 包含价格修改的详细信息
 */
public class PriceModificationResult {
    private final long originalPrice;
    private final long modifiedPrice;
    private final boolean priceChanged;
    private final String reason;
    private final String modifierName;
    
    /**
     * 私有构造函数，使用静态工厂方法创建实例
     */
    private PriceModificationResult(long originalPrice, long modifiedPrice, 
                                   boolean priceChanged, String reason, String modifierName) {
        this.originalPrice = originalPrice;
        this.modifiedPrice = modifiedPrice;
        this.priceChanged = priceChanged;
        this.reason = reason;
        this.modifierName = modifierName;
    }
    
    /**
     * 创建价格已修改的结果
     * 
     * @param newPrice 修改后的价格
     * @param reason 修改原因
     * @param modifierName 修改器名称
     * @return 价格修改结果
     */
    public static PriceModificationResult changed(long newPrice, String reason, String modifierName) {
        return new PriceModificationResult(0, newPrice, true, reason, modifierName);
    }
    
    /**
     * 创建价格未修改的结果
     * 
     * @param originalPrice 原始价格
     * @return 价格修改结果
     */
    public static PriceModificationResult noChange(long originalPrice) {
        return new PriceModificationResult(originalPrice, originalPrice, false, "无修改", null);
    }
    
    /**
     * 创建价格修改结果（包含原始价格）
     * 
     * @param originalPrice 原始价格
     * @param newPrice 修改后的价格
     * @param reason 修改原因
     * @param modifierName 修改器名称
     * @return 价格修改结果
     */
    public static PriceModificationResult create(long originalPrice, long newPrice, 
                                               String reason, String modifierName) {
        boolean changed = originalPrice != newPrice;
        return new PriceModificationResult(originalPrice, newPrice, changed, reason, modifierName);
    }
    
    /**
     * 获取原始价格
     * @return 原始价格
     */
    public long getOriginalPrice() {
        return originalPrice;
    }
    
    /**
     * 获取修改后的价格
     * @return 修改后的价格
     */
    public long getModifiedPrice() {
        return modifiedPrice;
    }
    
    /**
     * 检查价格是否被修改
     * @return 价格是否被修改
     */
    public boolean isPriceChanged() {
        return priceChanged;
    }
    
    /**
     * 获取修改原因
     * @return 修改原因
     */
    public String getReason() {
        return reason;
    }
    
    /**
     * 获取修改器名称
     * @return 修改器名称
     */
    public String getModifierName() {
        return modifierName;
    }
    
    /**
     * 获取价格变化量
     * @return 价格变化量（正数表示涨价，负数表示降价）
     */
    public long getPriceChange() {
        return modifiedPrice - originalPrice;
    }
    
    /**
     * 获取价格变化百分比
     * @return 价格变化百分比（例如：-0.2表示降价20%）
     */
    public double getPriceChangePercentage() {
        if (originalPrice == 0) {
            return 0.0;
        }
        return (double) getPriceChange() / originalPrice;
    }
    
    /**
     * 检查是否为降价
     * @return 是否为降价
     */
    public boolean isDiscount() {
        return priceChanged && modifiedPrice < originalPrice;
    }
    
    /**
     * 检查是否为涨价
     * @return 是否为涨价
     */
    public boolean isPriceIncrease() {
        return priceChanged && modifiedPrice > originalPrice;
    }
    
    /**
     * 获取折扣金额（仅在降价时有效）
     * @return 折扣金额
     */
    public long getDiscountAmount() {
        return isDiscount() ? originalPrice - modifiedPrice : 0;
    }
    
    /**
     * 获取折扣百分比（仅在降价时有效）
     * @return 折扣百分比（例如：0.2表示8折）
     */
    public double getDiscountPercentage() {
        if (!isDiscount() || originalPrice == 0) {
            return 0.0;
        }
        return (double) getDiscountAmount() / originalPrice;
    }
    
    @Override
    public String toString() {
        if (!priceChanged) {
            return String.format("PriceModificationResult{price=%d, changed=false}", originalPrice);
        }
        
        return String.format("PriceModificationResult{original=%d, modified=%d, change=%d, reason='%s', modifier='%s'}", 
                           originalPrice, modifiedPrice, getPriceChange(), reason, modifierName);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PriceModificationResult that = (PriceModificationResult) o;
        
        if (originalPrice != that.originalPrice) return false;
        if (modifiedPrice != that.modifiedPrice) return false;
        if (priceChanged != that.priceChanged) return false;
        if (reason != null ? !reason.equals(that.reason) : that.reason != null) return false;
        return modifierName != null ? modifierName.equals(that.modifierName) : that.modifierName == null;
    }
    
    @Override
    public int hashCode() {
        int result = (int) (originalPrice ^ (originalPrice >>> 32));
        result = 31 * result + (int) (modifiedPrice ^ (modifiedPrice >>> 32));
        result = 31 * result + (priceChanged ? 1 : 0);
        result = 31 * result + (reason != null ? reason.hashCode() : 0);
        result = 31 * result + (modifierName != null ? modifierName.hashCode() : 0);
        return result;
    }
}
