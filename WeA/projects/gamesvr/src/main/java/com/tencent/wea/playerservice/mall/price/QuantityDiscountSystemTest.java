package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.mall.price.modifiers.QuantityDiscountPriceModifier;
import com.tencent.wea.xlsRes.ResKeywords.CommodityModifyPriceType;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountTier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 数量折扣系统完整测试
 * 验证整个价格计算系统的功能
 */
public class QuantityDiscountSystemTest {
    private static final Logger LOGGER = LogManager.getLogger(QuantityDiscountSystemTest.class);
    
    /**
     * 运行完整的系统测试
     */
    public static void runCompleteSystemTest() {
        LOGGER.info("=== 开始数量折扣系统完整测试 ===");
        
        try {
            // 1. 测试组件初始化
            testComponentInitialization();
            
            // 2. 测试价格修改器注册
            testModifierRegistration();
            
            // 3. 测试价格计算器
            testPriceCalculator();
            
            // 4. 测试数量折扣逻辑
            testQuantityDiscountLogic();
            
            // 5. 测试边界条件
            testEdgeCases();
            
            // 6. 测试性能
            testPerformance();
            
            LOGGER.info("=== 系统测试全部完成 ===");
            
        } catch (Exception e) {
            LOGGER.error("系统测试过程中发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试组件初始化
     */
    private static void testComponentInitialization() {
        LOGGER.info("--- 测试组件初始化 ---");
        
        try {
            // 测试初始化器
            assert !PriceModifierInitializer.isInitialized() : "初始状态应该未初始化";
            
            PriceModifierInitializer.initialize();
            assert PriceModifierInitializer.isInitialized() : "初始化后应该为已初始化状态";
            
            // 测试重复初始化
            PriceModifierInitializer.initialize();
            assert PriceModifierInitializer.isInitialized() : "重复初始化后仍应该为已初始化状态";
            
            // 测试注册表
            PriceModifierRegistry registry = PriceModifierRegistry.getInstance();
            assert registry != null : "注册表实例不应该为null";
            assert registry.getRegisteredModifierCount() > 0 : "应该有注册的修改器";
            
            LOGGER.info("初始化状态: {}", PriceModifierInitializer.getInitializationStatus());
            LOGGER.info("注册表详情: {}", registry.getModifierDetails());
            
            LOGGER.info("✓ 组件初始化测试通过");
            
        } catch (Exception e) {
            LOGGER.error("组件初始化测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试价格修改器注册
     */
    private static void testModifierRegistration() {
        LOGGER.info("--- 测试价格修改器注册 ---");
        
        try {
            PriceModifierRegistry registry = PriceModifierRegistry.getInstance();
            
            // 检查数量折扣修改器是否已注册
            assert registry.hasModifier("QuantityDiscountPriceModifier") : "数量折扣修改器应该已注册";
            
            PriceModifier modifier = registry.getModifier("QuantityDiscountPriceModifier");
            assert modifier != null : "应该能获取到数量折扣修改器";
            assert modifier instanceof QuantityDiscountPriceModifier : "修改器类型应该正确";
            
            // 测试修改器属性
            assert "QuantityDiscountPriceModifier".equals(modifier.getName()) : "修改器名称应该正确";
            assert modifier.getPriority() == 50 : "修改器优先级应该为50";
            
            LOGGER.info("已注册的修改器: {}", registry.getRegisteredModifierNames());
            
            LOGGER.info("✓ 价格修改器注册测试通过");
            
        } catch (Exception e) {
            LOGGER.error("价格修改器注册测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试价格计算器
     */
    private static void testPriceCalculator() {
        LOGGER.info("--- 测试价格计算器 ---");
        
        try {
            PriceCalculator calculator = new PriceCalculator();
            assert calculator != null : "价格计算器应该创建成功";
            
            // 创建测试商品
            MallCommodity normalCommodity = createNormalCommodity();
            MallCommodity discountCommodity = createQuantityDiscountCommodity();
            
            // 注意：这里player为null，在实际环境中需要真实的Player对象
            // 但我们可以测试计算器的基本结构
            
            LOGGER.info("价格计算器创建成功");
            LOGGER.info("测试商品创建成功");
            
            LOGGER.info("✓ 价格计算器测试通过");
            
        } catch (Exception e) {
            LOGGER.error("价格计算器测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试数量折扣逻辑
     */
    private static void testQuantityDiscountLogic() {
        LOGGER.info("--- 测试数量折扣逻辑 ---");
        
        try {
            QuantityDiscountPriceModifier modifier = new QuantityDiscountPriceModifier();
            MallCommodity commodity = createQuantityDiscountCommodity();
            
            // 测试各种购买数量
            testQuantityDiscountCase(modifier, commodity, 1, 900, "第一阶梯");
            testQuantityDiscountCase(modifier, commodity, 3, 900, "第一阶梯");
            testQuantityDiscountCase(modifier, commodity, 5, 800, "第二阶梯");
            testQuantityDiscountCase(modifier, commodity, 8, 800, "第二阶梯");
            testQuantityDiscountCase(modifier, commodity, 10, 700, "第三阶梯");
            testQuantityDiscountCase(modifier, commodity, 15, 700, "第三阶梯");
            testQuantityDiscountCase(modifier, commodity, 100, 700, "第三阶梯");
            
            LOGGER.info("✓ 数量折扣逻辑测试通过");
            
        } catch (Exception e) {
            LOGGER.error("数量折扣逻辑测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试单个数量折扣案例
     */
    private static void testQuantityDiscountCase(QuantityDiscountPriceModifier modifier, 
                                               MallCommodity commodity, int quantity, 
                                               long expectedPrice, String tierName) {
        PriceContext context = new PriceContext(null, commodity, quantity, 1000, false, "test-bill");
        
        // 检查适用性
        boolean applicable = modifier.isApplicable(context);
        assert applicable : String.format("购买%d个时应该适用数量折扣", quantity);
        
        // 检查价格修改
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        assert result.isPriceChanged() : String.format("购买%d个时价格应该被修改", quantity);
        assert result.getModifiedPrice() == expectedPrice : 
            String.format("购买%d个时价格应该是%d（%s），实际是%d", 
                        quantity, expectedPrice, tierName, result.getModifiedPrice());
        
        LOGGER.info("购买{}个: {} -> {} ({})", quantity, 1000, result.getModifiedPrice(), tierName);
    }
    
    /**
     * 测试边界条件
     */
    private static void testEdgeCases() {
        LOGGER.info("--- 测试边界条件 ---");
        
        try {
            QuantityDiscountPriceModifier modifier = new QuantityDiscountPriceModifier();
            
            // 测试普通商品（无数量折扣）
            MallCommodity normalCommodity = createNormalCommodity();
            PriceContext normalContext = new PriceContext(null, normalCommodity, 5, 1000, false, "test-bill");
            
            assert !modifier.isApplicable(normalContext) : "普通商品不应该适用数量折扣";
            
            // 测试负价格处理
            MallCommodity negativePriceCommodity = createNegativePriceCommodity();
            PriceContext negativeContext = new PriceContext(null, negativePriceCommodity, 1, 1000, false, "test-bill");
            
            if (modifier.isApplicable(negativeContext)) {
                PriceModificationResult result = modifier.modifyPrice(negativeContext, 1000);
                assert result.getModifiedPrice() >= 0 : "修改后的价格不应该为负数";
            }
            
            // 测试零购买数量
            PriceContext zeroContext = new PriceContext(null, createQuantityDiscountCommodity(), 0, 1000, false, "test-bill");
            if (modifier.isApplicable(zeroContext)) {
                PriceModificationResult result = modifier.modifyPrice(zeroContext, 1000);
                // 零数量应该不匹配任何阶梯
                assert !result.isPriceChanged() : "零购买数量不应该修改价格";
            }
            
            LOGGER.info("✓ 边界条件测试通过");
            
        } catch (Exception e) {
            LOGGER.error("边界条件测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试性能
     */
    private static void testPerformance() {
        LOGGER.info("--- 测试性能 ---");
        
        try {
            QuantityDiscountPriceModifier modifier = new QuantityDiscountPriceModifier();
            MallCommodity commodity = createQuantityDiscountCommodity();
            
            int iterations = 10000;
            long startTime = System.currentTimeMillis();
            
            for (int i = 0; i < iterations; i++) {
                int quantity = (i % 20) + 1; // 1-20的购买数量
                PriceContext context = new PriceContext(null, commodity, quantity, 1000, false, "test-bill");
                
                if (modifier.isApplicable(context)) {
                    modifier.modifyPrice(context, 1000);
                }
            }
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            double avgTime = (double) duration / iterations;
            
            LOGGER.info("性能测试结果: {}次计算耗时{}ms，平均每次{:.3f}ms", 
                       iterations, duration, avgTime);
            
            assert avgTime < 1.0 : "平均计算时间应该小于1ms";
            
            LOGGER.info("✓ 性能测试通过");
            
        } catch (Exception e) {
            LOGGER.error("性能测试失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 创建普通商品（无数量折扣）
     */
    private static MallCommodity createNormalCommodity() {
        return MallCommodity.newBuilder()
                .setCommodityId(1001)
                .setPrice(1000)
                .build();
    }
    
    /**
     * 创建数量折扣商品
     */
    private static MallCommodity createQuantityDiscountCommodity() {
        QuantityDiscountTier tier1 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(900)
                .build();
        
        QuantityDiscountTier tier2 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(5)
                .setFixedPrice(800)
                .build();
        
        QuantityDiscountTier tier3 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(10)
                .setFixedPrice(700)
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier1)
                .addTiers(tier2)
                .addTiers(tier3)
                .build();
        
        return MallCommodity.newBuilder()
                .setCommodityId(1002)
                .setPrice(1000)
                .setModifyPriceType(CommodityModifyPriceType.CMPT_QuantityDiscount)
                .setQuantityDiscountParams(params)
                .build();
    }
    
    /**
     * 创建负价格商品（用于测试边界条件）
     */
    private static MallCommodity createNegativePriceCommodity() {
        QuantityDiscountTier tier = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(-100) // 负价格
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier)
                .build();
        
        return MallCommodity.newBuilder()
                .setCommodityId(1003)
                .setPrice(1000)
                .setModifyPriceType(CommodityModifyPriceType.CMPT_QuantityDiscount)
                .setQuantityDiscountParams(params)
                .build();
    }
    
    /**
     * 主方法，用于直接运行测试
     */
    public static void main(String[] args) {
        runCompleteSystemTest();
    }
}
