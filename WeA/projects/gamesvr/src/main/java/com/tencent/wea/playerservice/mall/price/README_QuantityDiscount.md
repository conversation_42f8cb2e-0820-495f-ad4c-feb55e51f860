# 商品数量折扣功能使用指南

## 功能概述

商品数量折扣功能允许根据玩家购买的商品数量提供不同的价格阶梯，实现"买得越多，单价越便宜"的效果。

## 配置说明

### 1. Proto定义

在 `ResMall.proto` 中已经定义了相关的数据结构：

```protobuf
// 商品配置中的字段
message MallCommodity {
  // ... 其他字段 ...
  
  // 自定义价格修改配置
  optional CommodityModifyPriceType modifyPriceType = 118; // 价格修改类型
  
  // 使用oneof支持不同类型的价格修改参数
  oneof modifyPriceParams {
    QuantityDiscountParams quantityDiscountParams = 1001; // 阶梯价格参数
  }
}

// 数量折扣参数
message QuantityDiscountParams {
  repeated QuantityDiscountTier tiers = 1; // 数量折扣阶梯
}

// 数量折扣阶梯
message QuantityDiscountTier {
  optional int32 minBuyCount = 1; // 最小购买数量
  optional int32 fixedPrice = 2; // 阶梯价格
}
```

### 2. 枚举定义

在 `ResKeywords.proto` 中定义了价格修改类型：

```protobuf
// 商品自定义价格修改类型
enum CommodityModifyPriceType {
  CMPT_None = 0 [(name) = "无修改"];
  CMPT_QuantityDiscount = 1 [(name) = "数量折扣"];           // 根据购买数量提供折扣
}
```

## Excel配置示例

### 基础配置

| 字段名 | 示例值 | 说明 |
|--------|--------|------|
| commodityId | 1001 | 商品ID |
| price | 1000 | 基础价格 |
| modifyPriceType | 1 | 价格修改类型（1=数量折扣） |

### 数量折扣阶梯配置

在 `quantityDiscountParams` 字段中配置JSON格式的阶梯信息：

```json
{
  "tiers": [
    {
      "minBuyCount": 1,
      "fixedPrice": 900
    },
    {
      "minBuyCount": 5,
      "fixedPrice": 800
    },
    {
      "minBuyCount": 10,
      "fixedPrice": 700
    }
  ]
}
```

## 配置示例

### 示例1：基础数量折扣

```
commodityId: 1001
price: 1000
modifyPriceType: 1
quantityDiscountParams: {
  "tiers": [
    {"minBuyCount": 1, "fixedPrice": 950},
    {"minBuyCount": 5, "fixedPrice": 900},
    {"minBuyCount": 10, "fixedPrice": 850}
  ]
}
```

**效果：**
- 购买1-4个：单价950
- 购买5-9个：单价900
- 购买10个及以上：单价850

### 示例2：大幅度折扣

```
commodityId: 1002
price: 1000
modifyPriceType: 1
quantityDiscountParams: {
  "tiers": [
    {"minBuyCount": 1, "fixedPrice": 1000},
    {"minBuyCount": 10, "fixedPrice": 800},
    {"minBuyCount": 50, "fixedPrice": 600},
    {"minBuyCount": 100, "fixedPrice": 500}
  ]
}
```

**效果：**
- 购买1-9个：单价1000（原价）
- 购买10-49个：单价800（8折）
- 购买50-99个：单价600（6折）
- 购买100个及以上：单价500（5折）

### 示例3：结合折扣价

```
commodityId: 1003
price: 1000
discountPrice: 800
modifyPriceType: 1
quantityDiscountParams: {
  "tiers": [
    {"minBuyCount": 1, "fixedPrice": 750},
    {"minBuyCount": 5, "fixedPrice": 700}
  ]
}
```

**效果：**
- 基础价格：1000
- 折扣价格：800
- 数量折扣基于折扣价计算：
  - 购买1-4个：单价750
  - 购买5个及以上：单价700

## 实现原理

### 1. 核心组件

#### PriceModifier接口
定义价格修改器的基本契约：
- `getName()`: 获取修改器名称
- `getPriority()`: 获取优先级（数值越小优先级越高）
- `isApplicable()`: 检查是否适用于当前上下文
- `modifyPrice()`: 执行价格修改逻辑

#### PriceModifierRegistry注册表
管理所有价格修改器：
- 单例模式确保全局唯一
- 线程安全的修改器注册和查询
- 按优先级自动排序

#### QuantityDiscountPriceModifier
数量折扣价格修改器：
- 优先级：50（高优先级）
- 支持多阶梯价格配置
- 自动选择最适用的价格阶梯

### 2. 价格计算流程

1. **基础价格计算**：优先使用 `discountPrice`，如果没有则使用 `price`
2. **创建价格上下文**：包含玩家、商品、购买数量等信息
3. **获取适用修改器**：根据配置和上下文筛选适用的修改器
4. **按优先级应用**：按优先级顺序依次应用价格修改器
5. **返回最终价格**：经过所有修改器处理后的最终价格

### 3. 阶梯选择逻辑

- 遍历所有价格阶梯，找到满足购买数量条件的阶梯
- 选择满足条件的最高阶梯（最大的minBuyCount）
- 如果购买数量为15，阶梯为[1, 5, 10]，则选择阶梯10
- 如果没有满足条件的阶梯，不修改价格

### 4. 集成方式

#### MallManager集成
- 在构造函数中初始化价格计算器
- 添加`calculateFinalUnitPrice()`方法
- 修改购买流程中的价格计算逻辑
- 保持向后兼容性

#### 初始化流程
```java
// 在MallManager构造函数中
PriceModifierInitializer.initialize();  // 初始化修改器
priceCalculator = new PriceCalculator(); // 创建计算器实例
```

## 注意事项

### 1. 配置验证

- 确保 `modifyPriceType` 设置为 `CMPT_QuantityDiscount`（值为1）
- 确保 `quantityDiscountParams` 中至少有一个有效的阶梯
- 阶梯价格不能为负数（会自动设为0）

### 2. 性能考虑

- 价格计算器会缓存修改器实例，避免重复创建
- 阶梯查找使用线性搜索，建议阶梯数量不要过多（建议不超过10个）

### 3. 兼容性

- 如果配置错误或缺失，会自动回退到原有的价格计算逻辑
- 不会影响现有商品的价格计算

## 测试验证

### 1. 单元测试

运行 `QuantityDiscountPriceModifierTest` 验证价格修改器的基本功能。

### 2. 集成测试

运行 `MallManagerQuantityDiscountTest` 验证整个价格计算流程。

### 3. 手动测试

1. 配置测试商品
2. 使用不同数量购买商品
3. 验证价格计算是否正确

## 扩展功能

### 1. 添加新的价格修改类型

可以在 `CommodityModifyPriceType` 枚举中添加新的类型，并实现对应的价格修改器。

### 2. 复杂折扣逻辑

可以在 `QuantityDiscountPriceModifier` 中实现更复杂的折扣计算逻辑，如百分比折扣、组合折扣等。

### 3. 动态配置

可以通过配置中心实现动态调整价格阶梯，无需重启服务器。
