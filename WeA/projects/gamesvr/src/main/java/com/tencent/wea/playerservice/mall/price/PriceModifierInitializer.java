package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.mall.price.modifiers.QuantityDiscountPriceModifier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * 价格修改器初始化器
 * 负责注册所有的价格修改器
 */
public class PriceModifierInitializer {
    private static final Logger LOGGER = LogManager.getLogger(PriceModifierInitializer.class);
    
    private static volatile boolean initialized = false;
    private static final Object INIT_LOCK = new Object();
    
    /**
     * 初始化所有价格修改器
     * 使用双重检查锁定确保只初始化一次
     */
    public static void initialize() {
        if (initialized) {
            return;
        }
        
        synchronized (INIT_LOCK) {
            if (initialized) {
                return;
            }
            
            try {
                LOGGER.info("开始初始化价格修改器...");
                
                PriceModifierRegistry registry = PriceModifierRegistry.getInstance();
                
                // 注册数量折扣价格修改器（优先级最高）
                registry.registerModifier(new QuantityDiscountPriceModifier());
                LOGGER.info("已注册数量折扣价格修改器");
                
                // 可以在这里注册更多的价格修改器
                // registry.registerModifier(new VipPriceModifier());
                // registry.registerModifier(new ActivityPriceModifier());
                // registry.registerModifier(new LevelPriceModifier());
                // registry.registerModifier(new SeasonPriceModifier());
                
                initialized = true;
                LOGGER.info("价格修改器初始化完成，共注册了 {} 个修改器", 
                           registry.getRegisteredModifierCount());
                
            } catch (Exception e) {
                LOGGER.error("初始化价格修改器时发生错误: {}", e.getMessage(), e);
                throw new RuntimeException("价格修改器初始化失败", e);
            }
        }
    }
    
    /**
     * 检查是否已经初始化
     * @return 是否已初始化
     */
    public static boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 重置初始化状态（主要用于测试）
     * 注意：这个方法应该谨慎使用，通常只在测试环境中使用
     */
    public static void reset() {
        synchronized (INIT_LOCK) {
            if (initialized) {
                LOGGER.warn("重置价格修改器初始化状态");
                PriceModifierRegistry.getInstance().clear();
                initialized = false;
            }
        }
    }
    
    /**
     * 获取初始化状态信息
     * @return 初始化状态描述
     */
    public static String getInitializationStatus() {
        if (!initialized) {
            return "价格修改器尚未初始化";
        }
        
        PriceModifierRegistry registry = PriceModifierRegistry.getInstance();
        int modifierCount = registry.getRegisteredModifierCount();
        
        StringBuilder status = new StringBuilder();
        status.append("价格修改器已初始化，共注册 ").append(modifierCount).append(" 个修改器：\n");
        
        // 获取已注册的修改器列表
        for (String modifierName : registry.getRegisteredModifierNames()) {
            status.append("  - ").append(modifierName).append("\n");
        }
        
        return status.toString();
    }
}
