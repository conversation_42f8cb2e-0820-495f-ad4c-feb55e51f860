package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;

/**
 * 价格计算上下文
 * 包含价格计算所需的所有信息
 */
public class PriceContext {
    private final Player player;
    private final MallCommodity commodityConf;
    private final int buyNum;
    private final long basePrice;
    private final boolean isDirectBuy;
    private final String businessBillNo;
    
    /**
     * 构造价格计算上下文
     * 
     * @param player 玩家对象
     * @param commodityConf 商品配置
     * @param buyNum 购买数量
     * @param basePrice 基础价格
     * @param isDirectBuy 是否直购
     * @param businessBillNo 业务单号
     */
    public PriceContext(Player player, MallCommodity commodityConf, int buyNum, 
                       long basePrice, boolean isDirectBuy, String businessBillNo) {
        this.player = player;
        this.commodityConf = commodityConf;
        this.buyNum = buyNum;
        this.basePrice = basePrice;
        this.isDirectBuy = isDirectBuy;
        this.businessBillNo = businessBillNo;
    }
    
    /**
     * 获取玩家对象
     * @return 玩家对象
     */
    public Player getPlayer() {
        return player;
    }
    
    /**
     * 获取玩家UID
     * @return 玩家UID，如果player为null则返回0
     */
    public long getUid() {
        return player != null ? player.getUid() : 0L;
    }
    
    /**
     * 获取商品配置
     * @return 商品配置
     */
    public MallCommodity getCommodityConf() {
        return commodityConf;
    }
    
    /**
     * 获取商品ID
     * @return 商品ID
     */
    public int getCommodityId() {
        return commodityConf != null ? commodityConf.getCommodityId() : 0;
    }
    
    /**
     * 获取购买数量
     * @return 购买数量
     */
    public int getBuyNum() {
        return buyNum;
    }
    
    /**
     * 获取基础价格
     * @return 基础价格
     */
    public long getBasePrice() {
        return basePrice;
    }
    
    /**
     * 是否直购
     * @return 是否直购
     */
    public boolean isDirectBuy() {
        return isDirectBuy;
    }
    
    /**
     * 获取业务单号
     * @return 业务单号
     */
    public String getBusinessBillNo() {
        return businessBillNo;
    }
    
    /**
     * 获取商品的商城ID
     * @return 商城ID
     */
    public int getMallId() {
        return commodityConf != null ? commodityConf.getMallId() : 0;
    }
    
    /**
     * 获取商品的货币类型
     * @return 货币类型
     */
    public int getCoinType() {
        return commodityConf != null ? commodityConf.getCoinType() : 0;
    }
    
    /**
     * 获取商品的基础价格（从配置中）
     * @return 商品基础价格
     */
    public int getCommodityPrice() {
        return commodityConf != null ? commodityConf.getPrice() : 0;
    }
    
    /**
     * 获取商品的折扣价格（从配置中）
     * @return 商品折扣价格
     */
    public int getCommodityDiscountPrice() {
        return commodityConf != null ? commodityConf.getDiscountPrice() : 0;
    }
    
    /**
     * 检查商品是否有折扣价
     * @return 是否有折扣价
     */
    public boolean hasCommodityDiscountPrice() {
        return commodityConf != null && commodityConf.getDiscountPrice() > 0;
    }
    
    @Override
    public String toString() {
        return String.format("PriceContext{uid=%d, commodityId=%d, buyNum=%d, basePrice=%d, isDirectBuy=%s, billNo='%s'}", 
                           getUid(), getCommodityId(), buyNum, basePrice, isDirectBuy, businessBillNo);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PriceContext that = (PriceContext) o;
        
        if (buyNum != that.buyNum) return false;
        if (basePrice != that.basePrice) return false;
        if (isDirectBuy != that.isDirectBuy) return false;
        if (player != null ? !player.equals(that.player) : that.player != null) return false;
        if (commodityConf != null ? !commodityConf.equals(that.commodityConf) : that.commodityConf != null) return false;
        return businessBillNo != null ? businessBillNo.equals(that.businessBillNo) : that.businessBillNo == null;
    }
    
    @Override
    public int hashCode() {
        int result = player != null ? player.hashCode() : 0;
        result = 31 * result + (commodityConf != null ? commodityConf.hashCode() : 0);
        result = 31 * result + buyNum;
        result = 31 * result + (int) (basePrice ^ (basePrice >>> 32));
        result = 31 * result + (isDirectBuy ? 1 : 0);
        result = 31 * result + (businessBillNo != null ? businessBillNo.hashCode() : 0);
        return result;
    }
}
