package com.tencent.wea.playerservice.mall.price;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 价格修改器注册表
 * 管理所有的价格修改器，提供注册、注销和查询功能
 */
public class PriceModifierRegistry {
    private static final Logger LOGGER = LogManager.getLogger(PriceModifierRegistry.class);
    
    private static volatile PriceModifierRegistry instance;
    private static final Object INSTANCE_LOCK = new Object();
    
    // 使用ConcurrentHashMap保证线程安全
    private final Map<String, PriceModifier> modifiers = new ConcurrentHashMap<>();
    
    // 缓存按优先级排序的修改器列表
    private volatile List<PriceModifier> sortedModifiers = new ArrayList<>();
    private final Object SORT_LOCK = new Object();
    
    private PriceModifierRegistry() {
        // 私有构造函数，确保单例
    }
    
    /**
     * 获取单例实例
     * @return 注册表实例
     */
    public static PriceModifierRegistry getInstance() {
        if (instance == null) {
            synchronized (INSTANCE_LOCK) {
                if (instance == null) {
                    instance = new PriceModifierRegistry();
                }
            }
        }
        return instance;
    }
    
    /**
     * 注册价格修改器
     * @param modifier 价格修改器
     */
    public void registerModifier(PriceModifier modifier) {
        if (modifier == null) {
            LOGGER.warn("尝试注册空的价格修改器");
            return;
        }
        
        String name = modifier.getName();
        if (name == null || name.trim().isEmpty()) {
            LOGGER.warn("尝试注册名称为空的价格修改器");
            return;
        }
        
        PriceModifier existing = modifiers.put(name, modifier);
        if (existing != null) {
            LOGGER.warn("价格修改器 '{}' 被替换", name);
        } else {
            LOGGER.info("注册价格修改器: {}", name);
        }
        
        // 重新排序
        updateSortedModifiers();
    }
    
    /**
     * 注销价格修改器
     * @param modifierName 修改器名称
     */
    public void unregisterModifier(String modifierName) {
        PriceModifier removed = modifiers.remove(modifierName);
        if (removed != null) {
            LOGGER.info("注销价格修改器: {}", modifierName);
            updateSortedModifiers();
        }
    }
    
    /**
     * 获取所有适用的价格修改器
     * @param context 价格计算上下文
     * @return 适用的修改器列表，按优先级排序
     */
    public List<PriceModifier> getApplicableModifiers(PriceContext context) {
        List<PriceModifier> applicable = new ArrayList<>();
        for (PriceModifier modifier : sortedModifiers) {
            try {
                if (modifier.isApplicable(context)) {
                    applicable.add(modifier);
                }
            } catch (Exception e) {
                LOGGER.error("检查修改器 '{}' 适用性时出错: {}", 
                           modifier.getName(), e.getMessage(), e);
            }
        }
        return applicable;
    }
    
    /**
     * 获取指定名称的价格修改器
     * @param name 修改器名称
     * @return 价格修改器，如果不存在则返回null
     */
    public PriceModifier getModifier(String name) {
        return modifiers.get(name);
    }
    
    /**
     * 检查是否已注册指定名称的修改器
     * @param name 修改器名称
     * @return 是否已注册
     */
    public boolean hasModifier(String name) {
        return modifiers.containsKey(name);
    }
    
    /**
     * 获取已注册的修改器数量
     * @return 修改器数量
     */
    public int getRegisteredModifierCount() {
        return modifiers.size();
    }
    
    /**
     * 获取所有已注册的修改器名称
     * @return 修改器名称集合
     */
    public Set<String> getRegisteredModifierNames() {
        return new HashSet<>(modifiers.keySet());
    }
    
    /**
     * 清空所有注册的修改器
     * 注意：这个方法应该谨慎使用，通常只在测试环境中使用
     */
    public void clear() {
        synchronized (SORT_LOCK) {
            modifiers.clear();
            sortedModifiers = new ArrayList<>();
        }
        LOGGER.warn("清空了所有价格修改器");
    }
    
    /**
     * 更新按优先级排序的修改器列表
     */
    private void updateSortedModifiers() {
        synchronized (SORT_LOCK) {
            List<PriceModifier> newSortedList = new ArrayList<>(modifiers.values());
            
            // 按优先级排序（数值越小优先级越高）
            newSortedList.sort(Comparator.comparingInt(PriceModifier::getPriority));
            
            sortedModifiers = newSortedList;
            
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("更新价格修改器排序，当前顺序：");
                for (int i = 0; i < sortedModifiers.size(); i++) {
                    PriceModifier modifier = sortedModifiers.get(i);
                    LOGGER.debug("  {}. {} (优先级: {})", 
                               i + 1, modifier.getName(), modifier.getPriority());
                }
            }
        }
    }
    
    /**
     * 获取修改器的详细信息
     * @return 修改器信息字符串
     */
    public String getModifierDetails() {
        StringBuilder details = new StringBuilder();
        details.append("价格修改器注册表详情：\n");
        details.append("总数量: ").append(modifiers.size()).append("\n");
        
        if (!sortedModifiers.isEmpty()) {
            details.append("按优先级排序：\n");
            for (int i = 0; i < sortedModifiers.size(); i++) {
                PriceModifier modifier = sortedModifiers.get(i);
                details.append(String.format("  %d. %s (优先级: %d)\n", 
                             i + 1, modifier.getName(), modifier.getPriority()));
            }
        } else {
            details.append("暂无注册的修改器\n");
        }
        
        return details.toString();
    }
}
