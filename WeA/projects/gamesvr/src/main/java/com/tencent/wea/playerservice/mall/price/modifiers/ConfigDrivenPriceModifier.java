package com.tencent.wea.playerservice.mall.price.modifiers;

import com.tencent.wea.playerservice.mall.price.PriceContext;
import com.tencent.wea.playerservice.mall.price.PriceModificationResult;
import com.tencent.wea.playerservice.mall.price.PriceModifier;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.VipDiscountParams;
import com.tencent.wea.xlsRes.ResMall.VipDiscountTier;
import com.tencent.wea.xlsRes.ResMall.ActivityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.LevelDiscountParams;
import com.tencent.wea.xlsRes.ResMall.LevelDiscountTier;
import com.tencent.wea.xlsRes.ResMall.FirstBuyDiscountParams;
import com.tencent.wea.xlsRes.ResKeywords.ModifyPriceType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 配置驱动的价格修改器
 * 根据商品配置中的价格修改参数进行价格调整
 */
public class ConfigDrivenPriceModifier implements PriceModifier {
    private static final Logger LOGGER = LogManager.getLogger(ConfigDrivenPriceModifier.class);
    
    private static final String MODIFIER_NAME = "ConfigDrivenPriceModifier";
    private static final int PRIORITY = 60; // 配置驱动的修改器优先级中等
    
    @Override
    public String getName() {
        return MODIFIER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean isApplicable(PriceContext context) {
        MallCommodity commodity = context.getCommodityConf();
        
        // 检查是否启用价格修改
        if (!commodity.hasEnableModifyPrice() || !commodity.getEnableModifyPrice()) {
            return false;
        }
        
        // 检查是否有价格修改类型
        if (!commodity.hasModifyPriceType()) {
            return false;
        }
        
        ModifyPriceType modifyType = commodity.getModifyPriceType();
        
        // 排除数量折扣（由专门的修改器处理）
        if (modifyType == ModifyPriceType.MPT_QuantityDiscount) {
            return false;
        }
        
        // 检查是否有对应的参数配置
        return hasValidParams(commodity, modifyType);
    }
    
    @Override
    public PriceModificationResult modifyPrice(PriceContext context, long currentPrice) {
        MallCommodity commodity = context.getCommodityConf();
        ModifyPriceType modifyType = commodity.getModifyPriceType();
        
        try {
            switch (modifyType) {
                case MPT_VipDiscount:
                    return applyVipDiscount(context, currentPrice);
                case MPT_ActivityDiscount:
                    return applyActivityDiscount(context, currentPrice);
                case MPT_LevelDiscount:
                    return applyLevelDiscount(context, currentPrice);
                case MPT_FirstBuyDiscount:
                    return applyFirstBuyDiscount(context, currentPrice);
                default:
                    LOGGER.warn("Unsupported modify price type: {} for commodity {}", 
                               modifyType, commodity.getCommodityId());
                    return PriceModificationResult.noChange(currentPrice);
            }
        } catch (Exception e) {
            LOGGER.error("Error applying price modification for commodity {}: {}", 
                        commodity.getCommodityId(), e.getMessage(), e);
            return PriceModificationResult.noChange(currentPrice);
        }
    }
    
    /**
     * 检查是否有有效的参数配置
     */
    private boolean hasValidParams(MallCommodity commodity, ModifyPriceType modifyType) {
        switch (modifyType) {
            case MPT_VipDiscount:
                return commodity.hasVipDiscountParams();
            case MPT_ActivityDiscount:
                return commodity.hasActivityDiscountParams();
            case MPT_LevelDiscount:
                return commodity.hasLevelDiscountParams();
            case MPT_FirstBuyDiscount:
                return commodity.hasFirstBuyDiscountParams();
            default:
                return false;
        }
    }
    
    /**
     * 应用VIP折扣
     */
    private PriceModificationResult applyVipDiscount(PriceContext context, long currentPrice) {
        MallCommodity commodity = context.getCommodityConf();
        VipDiscountParams params = commodity.getVipDiscountParams();
        
        // 获取玩家VIP等级
        int playerVipLevel = getPlayerVipLevel(context);
        if (playerVipLevel <= 0) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 查找适用的VIP折扣阶梯
        VipDiscountTier applicableTier = findApplicableVipTier(params.getVipTiersList(), playerVipLevel);
        if (applicableTier == null) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 计算折扣后价格
        long newPrice = Math.round(currentPrice * applicableTier.getDiscountRate());
        newPrice = Math.max(0, newPrice);
        
        String reason = String.format("VIP%d折扣%.0f%%", 
                                    playerVipLevel, 
                                    (1.0 - applicableTier.getDiscountRate()) * 100);
        
        return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
    }
    
    /**
     * 应用活动折扣
     */
    private PriceModificationResult applyActivityDiscount(PriceContext context, long currentPrice) {
        MallCommodity commodity = context.getCommodityConf();
        ActivityDiscountParams params = commodity.getActivityDiscountParams();
        
        // 检查活动时间
        if (!isActivityTimeValid(params)) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 检查是否需要参与活动
        if (params.hasRequireParticipation() && params.getRequireParticipation()) {
            if (!isPlayerParticipatingActivity(context, params.getActivityId())) {
                return PriceModificationResult.noChange(currentPrice);
            }
        }
        
        // 应用活动折扣
        long newPrice = Math.round(currentPrice * params.getDiscountRate());
        newPrice = Math.max(0, newPrice);
        
        String reason = String.format("活动折扣%.0f%%", 
                                    (1.0 - params.getDiscountRate()) * 100);
        
        return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
    }
    
    /**
     * 应用等级折扣
     */
    private PriceModificationResult applyLevelDiscount(PriceContext context, long currentPrice) {
        MallCommodity commodity = context.getCommodityConf();
        LevelDiscountParams params = commodity.getLevelDiscountParams();
        
        // 获取玩家等级
        int playerLevel = getPlayerLevel(context);
        if (playerLevel <= 0) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 查找适用的等级折扣阶梯
        LevelDiscountTier applicableTier = findApplicableLevelTier(params.getLevelTiersList(), playerLevel);
        if (applicableTier == null) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 计算折扣后价格
        long newPrice = Math.round(currentPrice * applicableTier.getDiscountRate());
        newPrice = Math.max(0, newPrice);
        
        String reason = String.format("等级%d折扣%.0f%%", 
                                    playerLevel, 
                                    (1.0 - applicableTier.getDiscountRate()) * 100);
        
        return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
    }
    
    /**
     * 应用首购折扣
     */
    private PriceModificationResult applyFirstBuyDiscount(PriceContext context, long currentPrice) {
        MallCommodity commodity = context.getCommodityConf();
        FirstBuyDiscountParams params = commodity.getFirstBuyDiscountParams();
        
        // 检查是否是首次购买
        if (!isFirstTimeBuy(context, params)) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 应用首购折扣
        long newPrice = Math.round(currentPrice * params.getDiscountRate());
        newPrice = Math.max(0, newPrice);
        
        String reason = String.format("首购折扣%.0f%%", 
                                    (1.0 - params.getDiscountRate()) * 100);
        
        return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
    }
    
    // 以下是辅助方法，需要根据实际系统实现
    
    private int getPlayerVipLevel(PriceContext context) {
        // TODO: 实现获取玩家VIP等级的逻辑
        return 0;
    }
    
    private int getPlayerLevel(PriceContext context) {
        // TODO: 实现获取玩家等级的逻辑
        return 1;
    }
    
    private boolean isActivityTimeValid(ActivityDiscountParams params) {
        // TODO: 实现活动时间检查逻辑
        return true;
    }
    
    private boolean isPlayerParticipatingActivity(PriceContext context, long activityId) {
        // TODO: 实现检查玩家是否参与活动的逻辑
        return true;
    }
    
    private boolean isFirstTimeBuy(PriceContext context, FirstBuyDiscountParams params) {
        // TODO: 实现检查是否首次购买的逻辑
        return false;
    }
    
    private VipDiscountTier findApplicableVipTier(List<VipDiscountTier> tiers, int vipLevel) {
        for (VipDiscountTier tier : tiers) {
            if (vipLevel >= tier.getMinVipLevel() && 
                (tier.getMaxVipLevel() == 0 || vipLevel <= tier.getMaxVipLevel())) {
                return tier;
            }
        }
        return null;
    }
    
    private LevelDiscountTier findApplicableLevelTier(List<LevelDiscountTier> tiers, int level) {
        for (LevelDiscountTier tier : tiers) {
            if (level >= tier.getMinLevel() && 
                (tier.getMaxLevel() == 0 || level <= tier.getMaxLevel())) {
                return tier;
            }
        }
        return null;
    }
}
