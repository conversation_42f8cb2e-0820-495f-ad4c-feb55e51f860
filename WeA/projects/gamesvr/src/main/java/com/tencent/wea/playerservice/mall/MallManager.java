package com.tencent.wea.playerservice.mall;

import com.tencent.cache.Cache;
import com.tencent.cache.CacheUtil;
import com.tencent.coRedis.CoRedisCmd;
import com.tencent.condition.event.player.common.MallCommodityBuyEvent;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKCheckedException;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.exception.RpcException;
import com.tencent.nk.util.guid.BillNoIdGenerator;
import com.tencent.nk.util.random.RandomGenerator;
import com.tencent.recommend.RecommendMgr;
import com.tencent.resourceloader.resclass.ActivityMainConfig;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.ClientKVConf;
import com.tencent.resourceloader.resclass.CommercialConfModifyCfgData;
import com.tencent.resourceloader.resclass.FeatureKeySwitchConf;
import com.tencent.resourceloader.resclass.FeatureOpenConfData;
import com.tencent.resourceloader.resclass.MallCommodityConf;
import com.tencent.resourceloader.resclass.MallGiftCardConf;
import com.tencent.resourceloader.resclass.MallInfoConf;
import com.tencent.resourceloader.resclass.MidasConfData;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.NR3E3MonthCardPrivilege;
import com.tencent.resourceloader.resclass.NR3E3Treasure;
import com.tencent.resourceloader.resclass.QuickRewardConfData;
import com.tencent.resourceloader.resclass.RelationConfData;
import com.tencent.resourceloader.resclass.SceneGiftPackageConf;
import com.tencent.resourceloader.resclass.WolfKillDecorationAni;
import com.tencent.resourceloader.resclass.WolfKillDecorationReact;
import com.tencent.rpc.RpcResult;
import com.tencent.tcaplus.TcaplusErrorCode;
import com.tencent.tcaplus.dao.PlayerCommodityBuyTimesSyncDao;
import com.tencent.tcaplus.dao.PlayerPublicDao;
import com.tencent.tcaplus.dao.PlayerPublicDao.PlayerPublicAttrKey;
import com.tencent.tcaplus.dao.PlayerTableDao;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.ugcsafe.UicEnum.SUB_SCENE;
import com.tencent.wea.attr.BuyRecordStruct;
import com.tencent.wea.attr.CommodityInfo;
import com.tencent.wea.attr.CommodityRedPoint;
import com.tencent.wea.attr.ConditionGroup;
import com.tencent.wea.attr.MallGiftCard;
import com.tencent.wea.attr.MallGiveRecord;
import com.tencent.wea.attr.MallInfo;
import com.tencent.wea.attr.MallRedPoint;
import com.tencent.wea.attr.MallRedPointStatus;
import com.tencent.wea.attr.ScenePackageInfo;
import com.tencent.wea.attr.SetAttrObj;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.interaction.player.MailInteraction.MailExtraParam;
import com.tencent.wea.midas.MidasManager.ProductData;
import com.tencent.wea.midas.MidasProductUtil;
import com.tencent.wea.midas.MidasProductUtil.MidasProductParam;
import com.tencent.wea.outputcontrol.OutputModuleCtx;
import com.tencent.wea.playerservice.activity.implement.BaseActivity;
import com.tencent.wea.playerservice.bag.ChangedItems;
import com.tencent.wea.playerservice.bag.ItemChangeDetails;
import com.tencent.wea.playerservice.condition.PlayerConditionInitData;
import com.tencent.wea.playerservice.condition.group.PlayerConditionGroup;
import com.tencent.wea.playerservice.condition.group.ScenePackageConditionGroup;
import com.tencent.wea.playerservice.event.common.PlayerBuyLimitCommodityEvent;
import com.tencent.wea.playerservice.event.common.PlayerMallCommodityBuyEvent;
import com.tencent.wea.playerservice.event.common.PlayerMallCommodityBuyFinishEvent;
import com.tencent.wea.playerservice.event.common.PlayerSendGiftEvent;
import com.tencent.wea.playerservice.gamemodule.modules.PlayerModule;
import com.tencent.wea.playerservice.mail.Mail;
import com.tencent.wea.playerservice.mail.MailDao;
import com.tencent.wea.playerservice.mail.MailMgr;
import com.tencent.wea.playerservice.mall.giftchecker.BaseMallGiftChecker;
import com.tencent.wea.playerservice.mall.giftchecker.MallGiftCheckerFactory;
import com.tencent.wea.playerservice.mall.price.PriceCalculator;
import com.tencent.wea.playerservice.mall.price.PriceModifierInitializer;
import com.tencent.wea.playerservice.outputcontrol.GamesvrOutputMgr;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.recharge.MonthCardMallDiscountCheckerHandler;
import com.tencent.wea.playerservice.recharge.TimeLimitedMallDiscountCheckerHandler;
import com.tencent.wea.playerservice.reward.QuickRewardInterface;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.playerservice.ugc.handle.SensitiveFilterHandle;
import com.tencent.wea.protocol.AttrBuyRecordStruct.proto_BuyRecordStruct;
import com.tencent.wea.protocol.AttrItem;
import com.tencent.wea.protocol.AttrMallGiftCard.proto_MallGiftCard;
import com.tencent.wea.protocol.CsMall;
import com.tencent.wea.protocol.CsMall.BuyStatus;
import com.tencent.wea.protocol.CsMall.MallGetThemeShopCommodity_S2C_Msg;
import com.tencent.wea.protocol.CsMall.MallGetThemeShopDiscount_S2C_Msg;
import com.tencent.wea.protocol.CsMall.MallGiftCardChangeNtf;
import com.tencent.wea.protocol.CsMall.MallList;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.SrReceivegift.ReceiveGiftNtf.ReceivedGift;
import com.tencent.wea.protocol.SsGamesvr;
import com.tencent.wea.protocol.common.BanStatus;
import com.tencent.wea.protocol.common.BanType;
import com.tencent.wea.protocol.common.DeliverGoodsMetaData;
import com.tencent.wea.protocol.common.FriendIntimacyChangeReason;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.protocol.common.MailCommodityInfo;
import com.tencent.wea.protocol.common.MailExtraData;
import com.tencent.wea.protocol.common.MallGiftCardInfo;
import com.tencent.wea.protocol.common.MetaDataReason;
import com.tencent.wea.protocol.common.PlayerPublicInfoField;
import com.tencent.wea.protocol.common.RewardItemInfo;
import com.tencent.wea.protocol.common.RewardItemInfo.Builder;
import com.tencent.wea.redis.ReceiveGiftCache;
import com.tencent.wea.rpc.service.GameService;
import com.tencent.wea.tcaplus.TcaplusDb;
import com.tencent.wea.tcaplus.TcaplusDb.PlayerPublic;
import com.tencent.wea.tcaplus.TcaplusDbWrapper;
import com.tencent.wea.tlog.flow.TlogMacros;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResGeneral.FeatureOpenConf;
import com.tencent.wea.xlsRes.ResMall;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.SceneGiftPackageConfig;
import com.tencent.wea.xlsRes.ResMidas;
import com.tencent.wea.xlsRes.ResNR3E3MonthCardPrivilege;
import com.tencent.wea.xlsRes.ResRelation;
import com.tencent.wea.xlsRes.ResReward.QuickRewardConf;
import com.tencent.wea.xlsRes.ResReward.QuickRewardType;
import com.tencent.wea.xlsRes.ResWolfKillDecoration;
import com.tencent.wea.xlsRes.keywords.BPType;
import com.tencent.wea.xlsRes.keywords.CoinType;
import com.tencent.wea.xlsRes.keywords.CommonLimitType;
import com.tencent.wea.xlsRes.keywords.FeatureOpenType;
import com.tencent.wea.xlsRes.keywords.GameModuleId;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemExpireType;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.MailExtraType;
import com.tencent.wea.xlsRes.keywords.MallCommodityLimit;
import com.tencent.wea.xlsRes.keywords.MallGiftCheckType;
import com.tencent.wea.xlsRes.keywords.MallMidasDiscountCondition;
import com.tencent.wea.xlsRes.keywords.MallRedPointType;
import com.tencent.wea.xlsRes.keywords.MidasModuleType;
import com.tencent.wea.xlsRes.keywords.RedDotPointShow;
import com.tencent.wea.xlsRes.keywords.RelationConfEnum;
import com.tencent.wea.xlsRes.keywords.SceneGiftPackageType;
import com.tencent.wea.xlsRes.keywords.ShopType;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;

import static com.tencent.wea.xlsRes.keywords.ItemInfoExtendType.IIET_MALL_ID;

/**
 * @author:blitzzhang
 * @date:2021/9/7 17:33
 */
public class MallManager extends PlayerModule implements QuickRewardInterface {

    private static final Logger LOGGER = LogManager.getLogger(MallManager.class);
    private static final Map<MallMidasDiscountCondition, MallMidasDiscountChecker> midasCheckers = new HashMap<>();
    private static final int MALL_MAX_BUY_NUM = 99999;
    private List<Integer> mallGiveIdList = null;
    private HashSet<Integer> scenePackageConditionRegIds = new HashSet<>();
    private Map<Integer, ScenePackageConditionGroup>  scenePackageConditionMap = new HashMap<>();

    static {
        registerMidasDiscountChecker(MallMidasDiscountCondition.MMDC_MonthCard,
                MonthCardMallDiscountCheckerHandler.getInstance());
        registerMidasDiscountChecker(MallMidasDiscountCondition.MMDC_TimeLimited,
                TimeLimitedMallDiscountCheckerHandler.getInstance());
    }

    private final Map<Integer, Set<Integer>> watchCoinTypeChangeCommodityIds = new HashMap<>();
    private final MallInfo playerMallInfo;
    private final ThemeMallMgr themeMallMgr;
    private final PriceCalculator priceCalculator;

    public MallManager(Player player) {
        super(GameModuleId.GMI_MallManager, player);
        playerMallInfo = player.getUserAttr().getMallInfo();
        player.getQuickRewardMgr().registerQuickReward(this);
        themeMallMgr = new ThemeMallMgr(player);

        // 初始化价格修改器（确保只初始化一次）
        PriceModifierInitializer.initialize();

        // 创建价格计算器实例
        priceCalculator = new PriceCalculator();
    }

    public static void registerMidasDiscountChecker(MallMidasDiscountCondition c, MallMidasDiscountChecker checker) {
        midasCheckers.putIfAbsent(c, checker);
    }

    private static Collection<Integer> collectOpenedMalls() {
        Collection<ResMall.MallInfo> mallInfos = MallInfoConf.getInstance().getAll();
        ArrayList<Integer> closedMallIds = new ArrayList<>();
        long currSec = Framework.currentTimeSec();
        for (ResMall.MallInfo mallInfo : mallInfos) {
            if (mallInfo.hasOpenTime() && mallInfo.getOpenTime().getSeconds() != 0
                    && mallInfo.getOpenTime().getSeconds() > currSec) {
                continue;
            }
            if (mallInfo.hasCloseTime() && mallInfo.getCloseTime().getSeconds() != 0
                    && mallInfo.getCloseTime().getSeconds() < currSec) {
                continue;
            }
            closedMallIds.add(mallInfo.getMallId());
        }

        return closedMallIds;
    }

    public void registerScenePackageCondition(int packageId) {
        scenePackageConditionRegIds.add(packageId);
    }

    public Collection<Integer> getRegisteredScenePackageCondition() {
        return scenePackageConditionRegIds;
    }

    public void addScenePackageCondition(int packageId, ScenePackageConditionGroup conditionGroup) {
        scenePackageConditionMap.put(packageId, conditionGroup);
    }

    public ScenePackageConditionGroup removeScenePackageCondition(int packageId) {
        return scenePackageConditionMap.remove(packageId);
    }


    public List<Integer> getShopCommodityIds(ShopType shopType, boolean allowShowBeforeSale) {
        int playerRegionId = player.getUserAttr().getPlayerPublicBasicInfo().getRegisterRegionId();
        var commodityList = MallCommodityConf.getInstance()
                .getCommodityByMallIdAndRegion(shopType.getNumber(), playerRegionId);
        if (commodityList == null) {
            return null;
        }
        ArrayList<Integer> commodityIds = new ArrayList();
        for (MallCommodity commodityConf : commodityList) {
            if (isHideCommodityConf(commodityConf, allowShowBeforeSale)) {
                continue;
            }
            List<Integer> itemIds = commodityConf.getItemIdsList();
            boolean configValid = true;
            for (int itemId : itemIds) {
                Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
                if (itemConf == null) {
                    LOGGER.error("getShopCommodityList get item config error, UID:{} commodityId:{} itemId:{}",
                            player.getUid(), commodityConf.getCommodityId(), itemId);
                    configValid = false;
                    break;
                }
                if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
                    LOGGER.debug("getShopCommodityList check item version err, UID:{} commodityId:{} itemId:{}",
                            player.getUid(), commodityConf.getCommodityId(), itemId);
                    configValid = false;
                    break;
                }
            }
            if (!configValid) {
                LOGGER.debug("getShopCommodityList check item config invalid, UID:{} commodityId:{}",
                        player.getUid(), commodityConf.getCommodityId());
                continue;
            }
            if (commodityConf.getShowCondition().getConditionCount() > 0) {
                PlayerConditionGroup condition = new PlayerConditionGroup(player,
                        commodityConf.getShowCondition(), new ConditionGroup());
                condition.initConditionGroup(new PlayerConditionInitData(player));
                if (!condition.checkComplete()) {
                    condition.unregisterConditionGroup();
                    continue;
                }
                condition.unregisterConditionGroup();
            }
            commodityIds.add(commodityConf.getCommodityId());
        }
        return commodityIds;
    }

    public void clearMallRedPoints(Collection<Integer> mallIds) {
        for (int mallId : mallIds) {
            MallRedPoint mallRedPointInfo = playerMallInfo.getMallRedPointInfo(mallId);
            if (mallRedPointInfo == null) {
                continue;
            }
            for (Integer redPointType: mallRedPointInfo.getRedPointTypeInfo().keySet()) {
                mallRedPointInfo.getRedPointTypeInfo(redPointType).setStatus(RedDotPointShow.RDPS_None_VALUE).setUpdateTime(DateUtils.currentTimeSec());
            }
        }
    }

    @Nullable
    public List<MallCommodity> getShopCommodityList(ShopType shopType, boolean allowShowBeforeSale) {
        int playerRegionId = player.getUserAttr().getPlayerPublicBasicInfo().getRegisterRegionId();
        var commodityList = MallCommodityConf.getInstance()
                .getCommodityByMallIdAndRegion(shopType.getNumber(), playerRegionId);
        if (commodityList == null) {
            return null;
        }

        ArrayList<MallCommodity> ret = new ArrayList<>();

        for (MallCommodity commodityConf : commodityList) {
            if (isHideCommodityConf(commodityConf, allowShowBeforeSale)) {
                continue;
            }
            List<Integer> itemIds = commodityConf.getItemIdsList();
            boolean configValid = true;
            for (int itemId : itemIds) {
                Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
                if (itemConf == null) {
//                    LOGGER.error("getShopCommodityList get item config error, UID:{} commodityId:{} itemId:{}",
//                            player.getUid(), commodityConf.getCommodityId(), itemId);
                    configValid = false;
                    break;
                }
                if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
//                    LOGGER.debug("getShopCommodityList check item version err, UID:{} commodityId:{} itemId:{}",
//                            player.getUid(), commodityConf.getCommodityId(), itemId);
                    configValid = false;
                    break;
                }
            }
            if (!configValid) {
                LOGGER.debug("getShopCommodityList check item config invalid, UID:{} commodityId:{}",
                        player.getUid(), commodityConf.getCommodityId());
                continue;
            }
            // 商品展示条件过滤
            if (commodityConf.getShowCondition().getConditionCount() > 0) {
                PlayerConditionGroup condition = new PlayerConditionGroup(player, commodityConf.getShowCondition(),
                        new ConditionGroup());
                condition.initConditionGroup(new PlayerConditionInitData(player));
                if (!condition.checkComplete()) {
                    condition.unregisterConditionGroup();
                    continue;
                }
                condition.unregisterConditionGroup();
            }

            MallCommodity.Builder commodityInfo = fillShopCommodityInfo(commodityConf);
            if (commodityInfo != null) {
                ret.add(commodityInfo.build());
            }
        }
        return ret;
    }

    @Nullable
    public List<MallCommodity> batchGetShopCommodity(List<Integer> idList, boolean allowShowBeforeSale) {
        ArrayList<MallCommodity> ret = new ArrayList<>();

        for (int commodityId : idList) {
            MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
            if (commodityConf == null) {
                LOGGER.error("batchGetShopCommodity user:{}, invalid id:{},", player.getUid(), commodityId);
                continue;
            }

            // 玩家地区 商品分组过滤
            int playerRegionId = player.getUserAttr().getPlayerPublicBasicInfo().getRegisterRegionId();
            //LOGGER.debug("user:{}, regionId:{}, commodityId:{}", player.getUid(), playerRegionId, commodityConf.getCommodityId());
            if (!MallCommodityConf.getInstance().isInRegion(commodityConf, playerRegionId)) {
                LOGGER.debug("commodityConf isNotInRegion,user:{}, regionId:{}, commodityId:{}", player.getUid(),
                        playerRegionId, commodityId);
                continue;
            }

            if (isHideCommodityConf(commodityConf, allowShowBeforeSale)) {
                continue;
            }

            List<Integer> itemIds = commodityConf.getItemIdsList();
            boolean configValid = true;
            for (int itemId : itemIds) {
                Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
                if (itemConf == null) {
                    LOGGER.error("batchGetCommodity get item config error, UID:{} commodityId:{} itemId:{}",
                            player.getUid(), commodityConf.getCommodityId(), itemId);
                    configValid = false;
                    break;
                }
                if (!BackpackItem.getInstance().checkItemVer(itemConf, player.getClientVersion64())) {
                    LOGGER.debug("batchGetCommodity check item version err, UID:{} commodityId:{} itemId:{}",
                            player.getUid(), commodityConf.getCommodityId(), itemId);
                    configValid = false;
                    break;
                }
            }
            if (!configValid) {
                LOGGER.debug("batchGetCommodity check item config invalid, UID:{} commodityId:{}",
                        player.getUid(), commodityConf.getCommodityId());
                continue;
            }
            if (commodityConf.getShowCondition().getConditionCount() > 0) {
                PlayerConditionGroup condition = new PlayerConditionGroup(player,
                        commodityConf.getShowCondition(), new ConditionGroup());
                condition.initConditionGroup(new PlayerConditionInitData(player));
                if (!condition.checkComplete()) {
                    condition.unregisterConditionGroup();
                    continue;
                }
                condition.unregisterConditionGroup();
            }

            MallCommodity.Builder commodityInfo = fillShopCommodityInfo(commodityConf);
            if (commodityInfo != null) {
                ret.add(commodityInfo.build());
            }
        }
        return ret;
    }

    @Nullable
    private MallCommodity.Builder fillShopCommodityInfo(MallCommodity commodityConf) {
        MallCommodity.Builder ret = MallCommodity.newBuilder();
        ret.mergeFrom(commodityConf);

        if (commodityConf.hasLimitType() && commodityConf.getLimitType() != MallCommodityLimit.MCL_None) {
            ret.setBoughtNum(getCommodityBuyNum(commodityConf));
        }
        if (commodityConf.hasCanDirectBuy() && commodityConf.getCanDirectBuy()) {
            String midasProductId = createMidasProductId(commodityConf);
            ResMidas.MidasConf midasConf = MidasConfData.getInstance().get(midasProductId);
            if (midasConf == null) {
                LOGGER.error("Commodity:{} without midas productId:{}",
                        commodityConf.getCommodityId(), midasProductId);
                return null;
            }
            ret.setPrice((int) midasConf.getOriginPrice())
                    .setDiscountPrice((int) midasConf.getMidasPrice());
        }
        ret.setShowRedPoint(getShowRedPoint(commodityConf));
        return ret;
    }

    public boolean isHideCommodityConf(MallCommodity commodityConf, boolean allowShowBeforeSale) {
        if (MallCommodityConf.getInstance().isInSale(commodityConf, player.getClientVersion64()) != NKErrorCode.OK) {
            if (!allowShowBeforeSale) {
                return true;
            }

            return !MallCommodityConf.getInstance().isForShow(commodityConf, player.getClientVersion64());
        }

        return false;
    }

    private String createMidasProductId(MallCommodity conf) {
        String ret = MidasModuleType.MMT_MALL_VALUE + "_" + conf.getCommodityId();
        if (conf.hasMidasDiscountConf() && conf.getMidasDiscountConf().hasCondition() &&
                midasCheckers.containsKey(conf.getMidasDiscountConf().getCondition())) {
            boolean canDiscount = midasCheckers.get(conf.getMidasDiscountConf().getCondition())
                    .canDiscount(player, conf.getMidasDiscountConf().getCondition(),
                            conf.getMidasDiscountConf().getParamsList().toArray(new String[0]));
            if (canDiscount) {
                return ret + "_" + conf.getMidasDiscountConf().getProductIdSuffix();
            }
        }
        return ret;
    }

    private String createWolfKillMidasProductId(int itemId) {
        String ret = MidasModuleType.MMT_WOLF_KILL_VALUE + "_" + itemId;
        return ret;
    }

    /**
     * 计算商品的最终单价
     * 集成价格计算器，支持数量折扣等特殊价格逻辑
     *
     * @param commodityConf 商品配置
     * @param buyNum 购买数量
     * @param isDirectBuy 是否直购
     * @param businessBillNo 业务单号
     * @return 最终单价
     */
    private long calculateFinalUnitPrice(MallCommodity commodityConf, int buyNum, boolean isDirectBuy, String businessBillNo) {
        try {
            // 使用价格计算器计算最终价格
            long finalPrice = priceCalculator.calculateFinalPrice(player, commodityConf, buyNum, isDirectBuy, businessBillNo);

            if (LOGGER.isDebugEnabled()) {
                long basePrice = commodityConf.getDiscountPrice() > 0 ?
                    commodityConf.getDiscountPrice() : commodityConf.getPrice();
                LOGGER.debug("Price calculation: uid={}, commodityId={}, buyNum={}, basePrice={}, finalPrice={}",
                           player.getUid(), commodityConf.getCommodityId(), buyNum, basePrice, finalPrice);
            }

            return finalPrice;
        } catch (Exception e) {
            LOGGER.error("Error calculating final price for commodity {}, using base price: {}",
                        commodityConf.getCommodityId(), e.getMessage(), e);

            // 发生错误时回退到原有逻辑
            return commodityConf.getDiscountPrice() > 0 ?
                commodityConf.getDiscountPrice() : commodityConf.getPrice();
        }
    }

    /**
     * 自定义价格批量购买商品
     * @param commodityIds
     * @param buyNums
     * @param reason
     * @param productIds 米大师商品ID自定义传入
     * @param prices 活动自定义价格
     * @param deliverData 米大师发货透传参数
     */
    public void MallCommodityBatchBuyWithPrice(List<Integer> commodityIds, List<Integer> buyNums, ItemChangeReason reason,
            @Nullable List<String> productIds, @Nullable List<Long> prices, @Nullable DeliverGoodsMetaData.Builder deliverData) {
        if (commodityIds.size() != buyNums.size()) {
            NKErrorCode.InvalidParams.throwError("buyNums params size not equal");
            return;
        }
        if (productIds != null && !productIds.isEmpty() && commodityIds.size() != productIds.size()) {
            NKErrorCode.InvalidParams.throwError("productIds params size not equal");
            return;
        }
        if (prices != null && !prices.isEmpty() && commodityIds.size() != prices.size()) {
            NKErrorCode.InvalidParams.throwError("prices params size not equal");
            return;
        }
        if (!checkCanBatchBuy(commodityIds)) {
            NKErrorCode.MallCommodityCanNotBatchBuy.throwError("checkCanBatchBuy error, commodity ids:{}",
                    commodityIds);
            return;
        }
        HashMap<String, ProductData> productMap = new HashMap<>();
        Map<ResMall.MallCommodity, Integer> buyConfs = new HashMap<>();
        long totalDiamonds = 0;
        int shopType = 0;
        String businessBillNo = BillNoIdGenerator.getBusinessBillNo("mall");
        ChangedItems costItems = new ChangedItems(reason.getNumber(), String.format("%s:%s", commodityIds, buyNums));
        costItems.setBusBillNo(businessBillNo);
        for (int index = 0; index < commodityIds.size(); index++) {
            int commodityId = commodityIds.get(index);
            int buyNum = buyNums.get(index);
            if (buyNum > MALL_MAX_BUY_NUM) {
                buyNum = MALL_MAX_BUY_NUM;
            }
            MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
            if (commodityConf == null) {
                NKErrorCode.ResNotFound.throwError("MallCommodityBuy get config error, UID:{} commodityId:{} buyNum:{}",
                        player.getUid(), commodityId, buyNum);
                return;
            }
            NKErrorCode errorCode = checkCommodityBuy(commodityConf, buyNum);
            if (errorCode != NKErrorCode.OK) {
                errorCode.throwError("checkCommodityBuy error, commodityId:{} buyNum:{}", commodityId, buyNum);
                return;
            }
            errorCode = checkItemsExistBeforeBuy(commodityConf);
            if (errorCode != NKErrorCode.OK) {
                errorCode.throwError("checkItemsExistBeforeBuy error, commodityId:{} buyNum:{}", commodityId, buyNum);
                return;
            }

            if (commodityConf.getCoinType() == CoinType.CT_Diamond_VALUE) {
                String midasProductId = createMidasProductId(commodityConf);
                if (!productIds.isEmpty()) {
                    midasProductId = productIds.get(index);
                }
                if (player.getPlayerMoneyMgr().isProductIdInvalid(midasProductId)) {
                    NKErrorCode.MallCommodityWithoutMidasProduct.throwError(
                            "batchBuy invalid product id, commodityId:{}, productId:{}",
                            commodityId, midasProductId);
                    return;
                }
                NKErrorCode checkSwitchRet = player.getPlayerMoneyMgr().checkProductIdCommercialSwitch(midasProductId);
                if (checkSwitchRet.hasError()) {
                    checkSwitchRet.throwError("checkProductIdCommercialSwitch fail. productId:{}, commodityId:{}, uid:{}",
                            midasProductId, commodityId, player.getUid());
                    return;
                }
                long actPrice = player.getPlayerMoneyMgr().getProductIdPrice(midasProductId);
                if (!prices.isEmpty()) {
                    actPrice = prices.get(index);
                }
                productMap.put(midasProductId, new ProductData(midasProductId, buyNum, actPrice));
                totalDiamonds += actPrice;
            } else {
                long finalUnitPrice;
                if (!prices.isEmpty()) {
                    // 如果有自定义价格，使用自定义价格
                    finalUnitPrice = prices.get(index);
                } else {
                    // 否则使用价格计算器计算最终价格
                    finalUnitPrice = calculateFinalUnitPrice(commodityConf, buyNum, false, businessBillNo);
                }
                costItems.mergeItemInfo(commodityConf.getCoinType(), finalUnitPrice * buyNum);
                buyConfs.put(commodityConf, buyNum);
            }
            shopType = commodityConf.getMallId();
        }

        if (!productMap.isEmpty()) {
            if (deliverData == null) {
                deliverData = DeliverGoodsMetaData.newBuilder();
            }
            try {
                player.getPlayerMoneyMgr().midasDiamondPay2(productMap, deliverData.setCostDiamonds(totalDiamonds)
                        .setBusBillNo(businessBillNo).setReason(reason), reason, 0);
            } catch (Exception e) {
                LOGGER.error("midas pay failed, uid:{}, commodityIds:{}, buyNums:{}",
                        player.getUid(), commodityIds, buyNums);
            }
        } else {
            if (!player.getBagManager().isItemsEnough(costItems)) {
                NKErrorCode.MallCommodityBuyCheckCostNotEnough.throwError(
                        "batchBuy CheckItemsEnough error, UID:{} commodityId:{} buyNum:{} costItems:{}",
                        player.getUid(), commodityIds, buyNums, costItems.toString());
                return;
            }
            player.getBagManager().MinItems(costItems, shopType)
                    .throwErrorIfNotOk("batchBuy MinItems error,UID:{} commodityId:{} buyNum:{} costItems:{}",
                            player.getUid(), commodityIds, buyNums, costItems);
            MallDeliverGoods(buyConfs, businessBillNo, reason, 0 , null)
                    .throwErrorIfNotOk("batchBuy deliver goods failed, uid:{} commodityId:{} buyNum:{} costItems:{}",
                            player.getUid(), commodityIds, buyNums, costItems);
        }
    }

    // 一级货币批量购买
    public void MallCommodityBatchBuy(List<Integer> commodityIds, List<Integer> buyNums, ItemChangeReason reason) {
        MallCommodityBatchBuyWithPrice(commodityIds, buyNums, reason, null, null, null);
    }

    // 批量购买检测
    // 钻石不能跟别的货币掺杂着一起买 不能直购
    private boolean checkCanBatchBuy(List<Integer> commodityIds) {
        HashSet<CoinType> costCoinTypeSet = new HashSet<CoinType>();
        for (int commodityId : commodityIds) {
            MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
            if (commodityConf == null) {
                NKErrorCode.ResNotFound.throwError("checkCanBatchBuy get config error, UID:{} commodityId:{}",
                        player.getUid(), commodityId);
                return false;
            }
            if (commodityConf.getCanDirectBuy()) {
                return false;
            }
            costCoinTypeSet.add(CoinType.forNumber(commodityConf.getCoinType()));
        }

        return !costCoinTypeSet.contains(CoinType.CT_Diamond) || costCoinTypeSet.size() <= 1;
    }

    public NKErrorCode checkCommodityBuy(MallCommodity commodityConf, int buyNum) {
        if (buyNum <= 0) {
            LOGGER.warn("MallCommodityBuy params error, UID:{} commodityId:{} buyNum:{}",
                    player.getUid(), commodityConf.getCommodityId(), buyNum);
            return NKErrorCode.MallCommodityBuyParamsErr;
        }
        if (commodityConf.getNoBuySelf()) {
            LOGGER.error("MallCommodityBuy noBuySelf, UID:{} commodityId:{} buyNum:{}",
                    player.getUid(), commodityConf.getCommodityId(), buyNum);
            return NKErrorCode.MallCommodityCannotBuySelf;
        }
        // 七彩石配置商品下架开关
        if (isCommodityIdUrgentClose(commodityConf.getCommodityId())) {
            LOGGER.error("MallCommodityBuy isCommodityIdUrgentClose, UID:{} commodityId:{} buyNum:{}",
                    player.getUid(), commodityConf.getCommodityId(), buyNum);
            return NKErrorCode.MallCommodityBuyCannotAccess;
        }
        if (commodityConf.getBuyCondition().getConditionCount() > 0) {
            PlayerConditionGroup condition = new PlayerConditionGroup(player, commodityConf.getBuyCondition(),
                    new ConditionGroup());
            condition.initConditionGroup(new PlayerConditionInitData(player));
            if (!condition.checkComplete()) {
                condition.unregisterConditionGroup();
                return NKErrorCode.MallCommodityBuyConditionNotComplete;
            }
            condition.unregisterConditionGroup();
        }
        // 检测商品是否包含染色，染色必须有原皮永久装扮才能购买
        Set<Item_BackpackItem> outlookBelongItemsConf = MallCommodityConf.getInstance()
                .getCommodityOutlookBelongItem(commodityConf.getCommodityId());
        if (outlookBelongItemsConf != null) {
            for (Item_BackpackItem itemConf : outlookBelongItemsConf) {
                // 峡谷英雄皮肤特殊处理
                if (itemConf.getType() == ItemType.ItemType_Arena_HeroSkin) {
                    if (!player.getArenaMgr().checkHasSkinByItemId(itemConf.getId())) {
                        return NKErrorCode.MallCommodityBuyOutlookOwnedItemErr;
                    }
                    continue;
                }
                if (player.getBagManager().getItemNumByItemIdIgnoreTemp(itemConf.getId()) == 0) {
                    return NKErrorCode.MallCommodityBuyOutlookOwnedItemErr;
                }
            }
        }
		// 检测购买的商品道具拥有上限
        for (int index = 0; index < commodityConf.getItemIdsCount(); index++) {
            int itemId = commodityConf.getItemIds(index);
            // 赛季币
            if (itemId == CoinType.CT_SeasonCoin_VALUE) {
                int itemNum = commodityConf.getItemNums(index);
                long addItemNum = (long) buyNum * itemNum;
                long limitNum = player.getSeasonMgr().getSeasonCoinLimit(CoinType.CT_SeasonCoin);
                if (addItemNum > limitNum) {
                    return NKErrorCode.MallCommodityBuyOutLimit;
                }
            }
        }
        NKErrorCode errCode = checkCommodityBuyLimit(commodityConf, buyNum);
        if (errCode != NKErrorCode.OK) {
            LOGGER.debug("check mallCommodityBuyLimited , UID:{} commodityId:{} buyNum:{}", player.getUid(),
                    commodityConf.getCommodityId(), buyNum);
            return errCode;
        }
        // 检查购买后不可超过道具拥有上限
        boolean checkItemMaxOwnNum = PropertyFileReader.getRealTimeBooleanItem("mall_check_item_own_num_enable", true);
        if (checkItemMaxOwnNum && commodityConf.getItemMaxOwnNumsCount() > 0) {
            for (int index = 0;
                    index < commodityConf.getItemIdsCount() && index < commodityConf.getItemMaxOwnNumsCount();
                    index++) {
                int itemMaxOwnNum = commodityConf.getItemMaxOwnNums(index);
                if (itemMaxOwnNum == 0) {
                    continue;
                }
                int itemId = commodityConf.getItemIds(index);
                int itemNum = commodityConf.getItemNums(index);
                //if (player.getBagManager().getItemNumByItemId(itemId) + (long) buyNum * itemNum > itemMaxOwnNum) {
                if (player.getBagManager().getItemNumByItemIdIgnoreTemp(itemId) + (long) buyNum * itemNum > itemMaxOwnNum) {
                    LOGGER.warn("player {} itemId {} CheckItemMaxNum fail , cur {} buy {} limit {}", player.getUid(),
                            itemId, player.getBagManager().getItemNumByItemIdIgnoreTemp(itemId), buyNum * itemNum,
                            itemMaxOwnNum);
                    return NKErrorCode.MallCommodityBuyOutLimit;
                }
            }
        }

        // 未开启，不允许购买珍宝值
//        int treasureItemId = MiscConf.getInstance().getMiscConf().getWolfKillTreasureItemId();
//        if (treasureItemId>0){
//            for (int index = 0;
//                    index < commodityConf.getItemIdsCount();
//                    index++) {
//                int itemId = commodityConf.getItemIds(index);
//                if (itemId == treasureItemId) {
//                    if (!player.getUserAttr().getWolfKillInfo().getInitTreasureFlag()){
//                        LOGGER.warn("player {} itemId {}, wolfKill treasure not open", player.getUid(),
//                                itemId);
//                        return NKErrorCode.MallCommodityBuyConditionNotComplete;
//                    }
//                }
//
//                // 有一些特殊的商品，没达到珍宝等级不能购买，已使用限购主条件
////                if (!NR3E3Treasure.getInstance().getInteractEmojItemList().isEmpty()){
////                    if (NR3E3Treasure.getInstance().getInteractEmojItemList().contains(itemId)){
////                        if (!player.getUserAttr().getWolfKillInfo().getInitTreasureFlag()){
////                            LOGGER.warn("player {} itemId {}, wolfKill treasure not open", player.getUid(),
////                                    itemId);
////                            return NKErrorCode.MallCommodityBuyConditionNotComplete;
////                        }
////                        if (player.getUserAttr().getWolfKillInfo().getLv() < NR3E3Treasure.getInstance().getInteractEmojLevel()){
////                            LOGGER.warn("player {} itemId {}, curLv:{}, needLv:{}", player.getUid(),
////                                    itemId, player.getUserAttr().getWolfKillInfo().getLv(),NR3E3Treasure.getInstance().getInteractEmojLevel() );
////                            return NKErrorCode.MallCommodityBuyConditionNotComplete;
////                        }
////                    }
////                }
//            }
//        }

        if (player.getWolfKillMgr().getWolfKillInfo().getUid() > 0) {
            int wolfKillMonthCardLevel = player.getWolfKillMgr().getMonthCardLevel();
            ResNR3E3MonthCardPrivilege.ResNR3E3MonthCardPrivilegeItem nr3e3MonthCardConf = NR3E3MonthCardPrivilege.getInstance().get(4);
            if (nr3e3MonthCardConf != null) {
                for (int itemId : commodityConf.getItemIdsList()) {
                    for (int tempItemId : nr3e3MonthCardConf.getItemIdsList()) {
                        if (itemId == tempItemId && wolfKillMonthCardLevel <= 0) {
                            return NKErrorCode.Nr3e3MonthCardNotOpen;
                        }
                    }
                }
            }
        }

        return NKErrorCode.OK;
    }

    private static boolean isCommodityIdUrgentClose(int commodityId) {
        return FeatureKeySwitchConf.getInstance().getByTypeidAnd1Keys(FeatureOpenType.FOT_K1_ShopCommodity_VALUE, commodityId)
                != null;
    }

    // 商城购买商品
    // 直购返回1
    //
    public int MallCommodityBuy(int commodityId, int buyNum, int clientUnitPrice, boolean isDirectBy,
            ItemChangeReason reason, long subReason, List<Long> changeReservedParams) {
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            NKErrorCode.ResNotFound.throwError("MallCommodityBuy get config error, UID:{} commodityId:{} buyNum:{}",
                    player.getUid(), commodityId, buyNum);
            return 0;
        }
        if (buyNum > MALL_MAX_BUY_NUM) {
            buyNum = MALL_MAX_BUY_NUM;
        }
        NKErrorCode errorCode = checkCommodityBuy(commodityConf, buyNum);
        if (errorCode != NKErrorCode.OK) {
            errorCode.throwError("checkCommodityBuy error, commodityId:{} buyNum:{}", commodityId, buyNum);
            return 0;
        }
        errorCode = checkItemsExistBeforeBuy(commodityConf);
        if (errorCode != NKErrorCode.OK) {
            errorCode.throwError("checkItemsExistBeforeBuy error, commodityId:{} buyNum:{}", commodityId, buyNum);
            return 0;
        }

        if (commodityConf.getCanDirectBuy() && !isDirectBy) {
            NKErrorCode.InvalidParams.throwError("direct by only");
            return 0;
        }
        String businessBillNo = BillNoIdGenerator.getBusinessBillNo("mall");
        // 直购
        if (commodityConf.getCanDirectBuy() && isDirectBy) {
            String midasProductId = createMidasProductId(commodityConf);
            if (player.getPlayerMoneyMgr().isProductIdInvalid(midasProductId)) {
                NKErrorCode.MallCommodityWithoutMidasProduct
                        .throwError("MallCommodityBuy invalid midas product id:{}, commodityId:{}, uid:{}",
                                midasProductId, commodityId, player.getUid());
                return 0;
            }
            NKErrorCode checkSwitchRet = player.getPlayerMoneyMgr().checkProductIdCommercialSwitch(midasProductId);
            if (checkSwitchRet.hasError()) {
                checkSwitchRet.throwError("checkProductIdCommercialSwitch fail. productId:{}, commodityId:{}, uid:{}",
                        midasProductId, commodityId, player.getUid());
                return 0;
            }
            if (buyNum != 1) {
                NKErrorCode.InvalidParams.throwError("mall direct buy:{} num!=1, commodityId:{}, uid:{}, buyNum:{}",
                        midasProductId, commodityId, player.getUid(), buyNum);
                return 0;
            }
            player.getRechargeMgr().directBuy(MidasModuleType.MMT_MALL, midasProductId,
                    DeliverGoodsMetaData.newBuilder().setBusBillNo(businessBillNo),
                    ItemChangeReason.ICR_MallCommodityBuy);
            return 1;
        }

        long finalUnitPrice = calculateFinalUnitPrice(commodityConf, buyNum, isDirectBy, businessBillNo);
        // 一级货币购买
        if (commodityConf.getCoinType() == CoinType.CT_Diamond_VALUE) {
            String midasProductId = createMidasProductId(commodityConf);
            if (player.getPlayerMoneyMgr().isProductIdInvalid(midasProductId)) {
                NKErrorCode.MallCommodityWithoutMidasProduct
                        .throwError("MallCommodityBuy invalid midas product id:{}, commodityId:{}, uid:{}",
                                midasProductId, commodityId, player.getUid());
                return 0;
            }
            NKErrorCode checkSwitchRet = player.getPlayerMoneyMgr().checkProductIdCommercialSwitch(midasProductId);
            if (checkSwitchRet.hasError()) {
                checkSwitchRet.throwError("checkProductIdCommercialSwitch fail. productId:{}, commodityId:{}, uid:{}",
                        midasProductId, commodityId, player.getUid());
                return 0;
            }
            try {
                HashMap<String, Integer> productMap = new HashMap<>();
                productMap.put(midasProductId, buyNum);
                String params = String.valueOf(commodityConf.getMallId());

                MetaDataReason.Builder metaDataReasonBuilder = MetaDataReason.newBuilder();
                metaDataReasonBuilder.setSubReason(subReason);
                if (null != changeReservedParams) {
                    metaDataReasonBuilder.addAllChangeReservedParams(changeReservedParams);
                }

                player.getPlayerMoneyMgr().midasDiamondPay(productMap,
                        DeliverGoodsMetaData.newBuilder().setCostDiamonds((long) finalUnitPrice * buyNum)
                                .setBusBillNo(businessBillNo).setParams(params).setSubReason(metaDataReasonBuilder) , reason, commodityConf.getMallId());
            } catch (Exception e) {
                LOGGER.error("midas pay failed, uid:{}, midasProductId:{}, buyNum:{} exception:{}",
                        player.getUid(), midasProductId, buyNum, e.toString());
                player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLDIRECTBUYNTF, CsMall.MallDirectBuyNtf.newBuilder()
                        .setRet(-1).setCommodityId(commodityId).setBuyNum(buyNum));
            }
            return 2;
        }

        ChangedItems costItems = new ChangedItems(reason.getNumber(), String.format("%d:%d", commodityId, buyNum));
        costItems.setBusBillNo(businessBillNo);

        if (clientUnitPrice != 0 && finalUnitPrice != clientUnitPrice) {
            NKErrorCode.MallUnitPriceMismatch
                    .throwError("unit price mismatch, refresh commodity, commodityId:{}, sPrice{}, cPrice:{}",
                            commodityId, finalUnitPrice, clientUnitPrice);
            return 0;
        }
        costItems.mergeItemInfo(commodityConf.getCoinType(), finalUnitPrice * buyNum);
        if (!player.getBagManager().isItemsEnough(costItems)) {
            NKErrorCode.MallCommodityBuyCheckCostNotEnough.throwError(
                    "MallCommodityBuy CheckItemsEnough error, UID:{} commodityId:{} buyNum:{} costItems:{}",
                    player.getUid(), commodityId, buyNum, costItems.toString());
            return 0;
        }

        player.getBagManager().MinItems(costItems, commodityConf.getMallId())
                .throwErrorIfNotOk("MallCommodityBuy MinItems error,UID:{} commodityId:{} buyNum:{} costItems:{}",
                        player.getUid(), commodityId, buyNum, costItems);

        MallDeliverGoods(commodityConf, buyNum, businessBillNo, reason, subReason, changeReservedParams);
        return 0;
    }

    private NKErrorCode checkWolfKillCommodityBuy(int itemId, int buyNum){
        // 检测购买的商品道具拥有上限
        Item_BackpackItem backpackItem = BackpackItem.getInstance().get(itemId);
        if (backpackItem == null) {
            return NKErrorCode.ItemCanNotBuy;
        }
        // 狼人的商品上架通过道具表来指定
        if (backpackItem.getBHideEmpty()){
            return NKErrorCode.ItemCanNotBuy;
        }
        long beginTime = backpackItem.getBeginTime().getSeconds();
        long endTime = backpackItem.getEndTime().getSeconds();
        long curTime = Framework.currentTimeMillis() / 1000;
        if ((beginTime!=0 && curTime < beginTime) || (endTime!=0 && curTime > endTime)) {
            return NKErrorCode.ItemCanNotBuy;
        }
        if (player.getBagManager().getItemNumByItemId(itemId) + (long) buyNum  > backpackItem.getMaxNum()) {
            LOGGER.warn("player {} itemId {} CheckItemMaxNum fail , cur {} buy {} limit {}", player.getUid(),
                    itemId, player.getBagManager().getItemNumByItemId(itemId), buyNum ,
                    backpackItem.getMaxNum());
            return NKErrorCode.MallCommodityBuyOutLimit;
        }
        return NKErrorCode.OK;
    }


    public int WolfKillMallCommodityBuy(int itemId, int buyNum, int clientUnitPrice, boolean isDirectBy,
                                ItemChangeReason reason, long subReason, List<Long> changeReservedParams) {

        //获得价格和货币类型即可
        int coinType =0;
        int finalUnitPrice = 0;
        ResWolfKillDecoration.WolfKillResDecorationAniItem aniConf = WolfKillDecorationAni.get(itemId);
        if (aniConf == null) {
            ResWolfKillDecoration.WolfKillResDecorationReactItem reactConf = WolfKillDecorationReact.get(itemId);
            if (reactConf == null) {
                NKErrorCode.ResNotFound.throwError("WolfKillMallCommodityBuy get config error, UID:{} itemId:{} buyNum:{}",
                        player.getUid(), itemId, buyNum);
                return 0;
            }else{
                coinType = reactConf.getCoinType();
                finalUnitPrice = reactConf.getPrice();
            }
        }else{
            coinType = aniConf.getCoinType();
            finalUnitPrice = aniConf.getPrice();
            // TODO: 校验只能购买一次,背包里有就不能再次购买
        }

        if (buyNum > MALL_MAX_BUY_NUM) {
            buyNum = MALL_MAX_BUY_NUM;
        }

        NKErrorCode errorCode = checkWolfKillCommodityBuy(itemId, buyNum);
        if (errorCode != NKErrorCode.OK) {
            errorCode.throwError("checkCommodityBuy error, itemId:{} buyNum:{}", itemId, buyNum);
            return 0;
        }

        String businessBillNo = BillNoIdGenerator.getBusinessBillNo("mall");

        // 一级货币购买
        if (coinType == CoinType.CT_Diamond_VALUE) {
            String midasProductId = createWolfKillMidasProductId(itemId);
            if (player.getPlayerMoneyMgr().isProductIdInvalid(midasProductId)) {
                NKErrorCode.MallCommodityWithoutMidasProduct
                        .throwError("MallCommodityBuy invalid midas product id:{}, commodityId:{}, uid:{}",
                                midasProductId, itemId, player.getUid());
                return 0;
            }
            NKErrorCode checkSwitchRet = player.getPlayerMoneyMgr().checkProductIdCommercialSwitch(midasProductId);
            if (checkSwitchRet.hasError()) {
                checkSwitchRet.throwError("checkProductIdCommercialSwitch fail. productId:{}, commodityId:{}, uid:{}",
                        midasProductId, itemId, player.getUid());
                return 0;
            }
            try {
                HashMap<String, Integer> productMap = new HashMap<>();
                productMap.put(midasProductId, buyNum);

                StringBuilder metaDataStr = new StringBuilder(subReason + "-");
                for (Long param: changeReservedParams){
                    metaDataStr.append(param).append("-");
                }

                player.getPlayerMoneyMgr().midasDiamondPay(productMap,
                        DeliverGoodsMetaData.newBuilder().setCostDiamonds((long) finalUnitPrice * buyNum)
                                .setBusBillNo(businessBillNo).setParams(metaDataStr.toString()), reason, subReason);
            } catch (Exception e) {
                LOGGER.error("midas pay failed, uid:{}, midasProductId:{}, buyNum:{}",
                        player.getUid(), midasProductId, buyNum);
                player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLDIRECTBUYNTF, CsMall.MallDirectBuyNtf.newBuilder()
                        .setRet(-1).setCommodityId(itemId).setBuyNum(buyNum));
            }
            return 2;
        }

        ChangedItems costItems = new ChangedItems(reason.getNumber(), String.format("%d:%d", itemId, buyNum));
        costItems.setBusBillNo(businessBillNo);
        costItems.addChangeReservedParams(changeReservedParams);

        if (clientUnitPrice != 0 && finalUnitPrice != clientUnitPrice) {
            NKErrorCode.MallUnitPriceMismatch
                    .throwError("unit price mismatch, refresh commodity, commodityId:{}, sPrice{}, cPrice:{}",
                            itemId, finalUnitPrice, clientUnitPrice);
            return 0;
        }
        costItems.mergeItemInfo(coinType, finalUnitPrice * buyNum);
        if (!player.getBagManager().isItemsEnough(costItems)) {
            NKErrorCode.MallCommodityBuyCheckCostNotEnough.throwError(
                    "MallCommodityBuy CheckItemsEnough error, UID:{} commodityId:{} buyNum:{} costItems:{}",
                    player.getUid(), itemId, buyNum, costItems.toString());
            return 0;
        }

        player.getBagManager().MinItems(costItems, subReason)
                .throwErrorIfNotOk("MallCommodityBuy MinItems error,UID:{} commodityId:{} buyNum:{} costItems:{}",
                        player.getUid(), itemId, buyNum, costItems);

        WolfKillMallDeliverGoods(itemId, buyNum, businessBillNo, reason, subReason, changeReservedParams);
        return 0;
    }

    private static NKPair<ItemExpireType, Long> getCommodityExpireInfo(MallCommodity conf, int index) {
        ItemExpireType expireType = ItemExpireType.IET_RELATIVE;
        long expireTimeMs = 0L;
        if (conf.getExpireDaysCount() > 0 && conf.getExpireDays(index) > 0) {
            expireTimeMs = conf.getExpireDays(index) * DateUtils.ONE_DAY_MILLIS + DateUtils.currentTimeMillis();
        } else if (conf.getExpireTimestampsCount() > 0 && conf.getExpireTimestamps(index).getSeconds() > 0) {
            expireTimeMs = conf.getExpireTimestamps(index).getSeconds() * DateUtils.ONE_SECOND_MILLIS;
            expireType = ItemExpireType.IET_ABSOLUTE;
        }
        return new NKPair<>(expireType, expireTimeMs);
    }

    private NKPair<NKErrorCode, ItemChangeDetails> mallDeliverGoodsWithoutNtf(Map<ResMall.MallCommodity, Integer> buyConfs,
            String billNo, ItemChangeReason reason) {
        MallCommodity conf;
        Integer buyNum;
        ChangedItems addCommodity = new ChangedItems(reason.getNumber(), "");
        addCommodity.setBusBillNo(billNo);
        int shopType = 0;

        var finishEvent = new PlayerMallCommodityBuyFinishEvent(player);
        List<PlayerMallCommodityBuyEvent> eventList = new ArrayList<>();

        for (Map.Entry<ResMall.MallCommodity, Integer> buyConf : buyConfs.entrySet()) {
            conf = buyConf.getKey();
            boolean isNeedNtfMallId = !conf.getDisableNtf();
            buyNum = buyConf.getValue();
            // 七彩石配置商品下架开关
            if (isCommodityIdUrgentClose(conf.getCommodityId())) {
                LOGGER.error("mallDeliverGoodsWithoutNtf isCommodityIdUrgentClose, UID:{} commodityId:{} buyNum:{}",
                        player.getUid(), conf.getCommodityId(), buyNum);
                return new NKPair<>(NKErrorCode.MallCommodityBuyCannotAccess, null);
            }
            OutputModuleCtx outputCtx = GamesvrOutputMgr.makePlayerMallCtx(player, conf.getCommodityId(), buyNum, DateUtils.currentTimeSec());
            if (!outputCtx.canOutput()) {
                LOGGER.error("player {} task {} mallDeliverGoods error: output control check fail", player.getUid(),
                        conf.getCommodityId());
                return new NKPair<>(NKErrorCode.MallCommodityBuyOutPutCheckErr, null);
            }
            Map<Integer, Long> extraParam = new HashMap<>();
            extraParam.put(IIET_MALL_ID.getNumber(), (long)conf.getCommodityId());
            for (int i = 0; i < conf.getItemIdsCount(); i++) {
                if (conf.getItemNumsCount() <= i) {
                    continue;
                }
                int itemNum = conf.getItemNums(i);
                NKPair<ItemExpireType, Long> expireInfo = getCommodityExpireInfo(conf, i);
                addCommodity.mergeItemInfo(conf.getItemIds(i), itemNum * buyNum, expireInfo.getValue(),
                        expireInfo.getKey(),extraParam);
            }
            if (!conf.getLimitType().equals(MallCommodityLimit.MCL_None)) {
                addCommodityBuyRecord(conf, buyNum);
                outputCtx.recordOutput();
            }
            onCommodityBuy(conf, buyNum, addCommodity);
            TlogFlowMgr.sendMallFlow(player, conf, buyNum, getCommodityBuyNum(conf), billNo);
            shopType = conf.getMallId();

            // 购买指定商城的数量消息通知
            eventList.add(new PlayerMallCommodityBuyEvent(player, buyNum, shopType, conf));;
            finishEvent.add(buyNum, shopType, conf);
            MallCommodityBuyEvent event = new MallCommodityBuyEvent(player.getConditionMgr()).setShopCommodityBuy(
                    buyNum, conf);
            player.getPlayerEventManager().dispatch(event);
        }
        addCommodity.addChangeReservedParams(1);
        NKPair<NKErrorCode, ItemChangeDetails> addRet = player.getBagManager()
                .AddItems2(addCommodity, false, shopType);

        finishEvent.dispatch();
        for (PlayerMallCommodityBuyEvent event : eventList) {
            event.dispatch();
        }
        try {
            CurrentExecutorUtil.runJob(() -> {
                syncMallStatus(buyConfs.keySet(), false);
                return null;
            }, "MallDeliverGoodsSyncMallStatus", true);
        } catch (NKCheckedException e) {
            LOGGER.error("MallDeliverGoodsSyncMallStatus failed", e);
        }
        if (!addRet.getKey().isOk()) {
            LOGGER.error("MallCommodityBuy AddItems error,UID:{} buyConfs:{} addCommodity:{}",
                    player.getUid(), buyConfs, addCommodity);
        }
        // 购买成功刷新活动红点
        for (ResMall.MallCommodity commodityConf : buyConfs.keySet()) {
            checkCommodityRedPointNtf(commodityConf,false);
        }
        return addRet;
    }

    private NKErrorCode MallDeliverGoods(Map<ResMall.MallCommodity, Integer> buyConfs, String billNo,
            ItemChangeReason reason,  long subReason, List<Long> changeReservedParams) {
        MallCommodity conf;
        Integer buyNum;
        ChangedItems addCommodity = new ChangedItems(reason.getNumber(), "");
        addCommodity.setBusBillNo(billNo);
        addCommodity.addChangeReservedParams(changeReservedParams);
        int shopType = 0;
        boolean needNtf = false;

        var finishEvent = new PlayerMallCommodityBuyFinishEvent(player);
        List<PlayerMallCommodityBuyEvent> eventList = new ArrayList<>();

        for (Map.Entry<ResMall.MallCommodity, Integer> buyConf : buyConfs.entrySet()) {
            conf = buyConf.getKey();
            boolean isNeedNtfMallId = !conf.getDisableNtf();
            needNtf |= isNeedNtfMallId;
            buyNum = buyConf.getValue();
            // 七彩石配置商品下架开关
            if (isCommodityIdUrgentClose(conf.getCommodityId())) {
                LOGGER.error("MallDeliverGoods isCommodityIdUrgentClose, UID:{} commodityId:{} buyNum:{}",
                        player.getUid(), conf.getCommodityId(), buyNum);
                return NKErrorCode.MallCommodityBuyCannotAccess;
            }
            OutputModuleCtx outputCtx = GamesvrOutputMgr.makePlayerMallCtx(player, conf.getCommodityId(), buyNum, DateUtils.currentTimeSec());
            if (!outputCtx.canOutput()) {
                LOGGER.error("player {} task {} mallDeliverGoods error: output control check fail", player.getUid(),
                        conf.getCommodityId());
                return NKErrorCode.MallCommodityBuyOutPutCheckErr;
            }
            Map<Integer, Long> extraParam = new HashMap<>();
            extraParam.put(IIET_MALL_ID.getNumber(), (long)conf.getCommodityId());
            for (int i = 0; i < conf.getItemIdsCount(); i++) {
                if (conf.getItemNumsCount() <= i) {
                    continue;
                }
                int itemNum = conf.getItemNums(i);
                NKPair<ItemExpireType, Long> expireInfo = getCommodityExpireInfo(conf, i);
                addCommodity.mergeItemInfo(conf.getItemIds(i), itemNum * buyNum, expireInfo.getValue(),
                        expireInfo.getKey(),extraParam);
            }
            if (!conf.getLimitType().equals(MallCommodityLimit.MCL_None)) {
                addCommodityBuyRecord(conf, buyNum);
                outputCtx.recordOutput();
            }
            onCommodityBuy(conf, buyNum, addCommodity);
            TlogFlowMgr.sendMallFlow(player, conf, buyNum, getCommodityBuyNum(conf), billNo);
            shopType = conf.getMallId();

            // 购买指定商城的数量消息通知
            eventList.add(new PlayerMallCommodityBuyEvent(player, buyNum, shopType, conf));
            finishEvent.add(buyNum, shopType, conf);
            player.getPlayerEventManager()
                    .dispatch(new MallCommodityBuyEvent(player.getConditionMgr()).setShopCommodityBuy(buyNum, conf));
        }

        if (subReason == 0) {
            subReason = shopType;
        }

        NKPair<NKErrorCode, ItemChangeDetails> addRet = player.getBagManager()
                .AddItems2(addCommodity, needNtf, subReason);

        finishEvent.dispatch();
        for (PlayerMallCommodityBuyEvent event : eventList) {
            event.dispatch();
        }

        try {
            CurrentExecutorUtil.runJob(() -> {
                syncMallStatus(buyConfs.keySet(), false);
                return null;
            }, "MallDeliverGoodsSyncMallStatus", true);
        } catch (NKCheckedException e) {
            LOGGER.error("MallDeliverGoodsSyncMallStatus failed", e);
        }
        if (!addRet.getKey().isOk()) {
            LOGGER.error("MallCommodityBuy AddItems error,UID:{} buyConfs:{} addCommodity:{}",
                    player.getUid(), buyConfs, addCommodity);
        }
        // 购买成功刷新活动红点
        for (ResMall.MallCommodity commodityConf : buyConfs.keySet()) {
            checkCommodityRedPointNtf(commodityConf,true);
        }
        try {
            if (!needNtf) {
                for (Map.Entry<ResMall.MallCommodity, Integer> buyConf : buyConfs.entrySet()) {
                    conf = buyConf.getKey();
                    buyNum = buyConf.getValue();
                    for (int i = 0; i < conf.getItemIdsCount(); i++) {
                        if (conf.getItemNumsCount() <= i) {
                            continue;
                        }
                        int itemId = conf.getItemIds(i);
                        int itemNum = conf.getItemNums(i) * buyNum;
                        String[] itemIdParam = new String[]{String.valueOf(itemId)};
                        //获得物品数量
                        Monitor.getInstance().set.succ(MonitorId.attr_tyc_dsdbsvr_max_cost_ms, itemNum, itemIdParam);
                    }
                    //购买次数
                    String[] mallParams = new String[]{String.valueOf(conf.getMallId())};
                    Monitor.getInstance().set.succ(MonitorId.attr_tyc_dsdbsvr_max_tps, 1, mallParams);
                    //消耗物品
                    String[] coinParams = new String[]{String.valueOf(conf.getCoinType())};
                    int finalUnitPrice = conf.getDiscountPrice() > 0 ?
                            conf.getDiscountPrice() : conf.getPrice();
                    int costNum = finalUnitPrice * buyNum;
                    Monitor.getInstance().add.succ(MonitorId.attr_tyc_dsdbsvr_avg_cost_ms, costNum, coinParams);
                }

            }
        } catch (Throwable e) {
            LOGGER.error("DsBag MallCommodityBuy monitor error,UID:{} buyConfs:{} addCommodity:{}",
                    player.getUid(), buyConfs, addCommodity);
        }

        return addRet.getKey();
    }



    private NKErrorCode WolfKillMallDeliverGoods(int itemId, int itemNum, String billNo,
                                         ItemChangeReason reason, long subReason, List<Long> changeReservedParams ) {

        ChangedItems addCommodity = new ChangedItems(reason.getNumber(), "");
        addCommodity.setBusBillNo(billNo);
        addCommodity.mergeItemInfo(itemId, itemNum);
        addCommodity.addChangeReservedParams(changeReservedParams);

        NKPair<NKErrorCode, ItemChangeDetails> addRet = player.getBagManager()
                .AddItems2(addCommodity, true, subReason);

        if (!addRet.getKey().isOk()) {
            LOGGER.error("WolfKillMallCommodityBuy AddItems error,UID:{} addCommodity:{}",
                    player.getUid(), addCommodity);
        }

        return addRet.getKey();
    }


    private void onCommodityBuy(ResMall.MallCommodity commodityConf, int buyNum, ChangedItems addCommodity) {
        Set<ResMall.SceneGiftPackageConfig> pushIds = SceneGiftPackageConf.getInstance().getCommodityPushIds(commodityConf.getCommodityId());
        if (pushIds == null || pushIds.isEmpty()) {
            return;
        }
        ScenePackageMgr.onCommodityBuy(player, commodityConf, addCommodity);
    }

    private void MallDeliverGoods(ResMall.MallCommodity conf, int buyNum, String billNo, ItemChangeReason reason,
            long subReason, List<Long> changeReservedParams) {
        Map<ResMall.MallCommodity, Integer> buyConfs = new HashMap<>();
        buyConfs.put(conf, buyNum);
        MallDeliverGoods(buyConfs, billNo, reason,subReason, changeReservedParams).throwErrorIfNotOk(
                "MallDeliverGoods failed, uid:{}, conf:{}, buyNum:{}",
                player.getUid(), conf, buyNum);
    }

    public NKErrorCode MidasDeliverMallGoods(Collection<NKPair<Integer, Integer>> confsFromMidas, String billNo,
            ItemChangeReason reason, DeliverGoodsMetaData metaData) {
        Map<ResMall.MallCommodity, Integer> deliverConfs = new HashMap<>();
        for (NKPair<Integer, Integer> deliverConf : confsFromMidas) {
            ResMall.MallCommodity conf = MallCommodityConf.getInstance().get(deliverConf.getKey());
            if (conf == null) {
                LOGGER.error("MidasDeliverMallGoods mall conf null, uid:{}, comId:{}, num:{}",
                        player.getUid(), deliverConf.getKey(), deliverConf.getValue());
                continue;
            }
            // 限购检查
            NKErrorCode errCode = checkCommodityBuyCountLimit(conf, deliverConf.getValue());
            if (errCode != NKErrorCode.OK) {
                LOGGER.warn("check mallCommodityBuyLimited , UID:{} commodityId:{} buyNum:{}", player.getUid(),
                        conf.getCommodityId(), deliverConf.getValue());
                // 打tlog
                MidasProductParam productParam = MidasProductUtil.getMidasProductParam(createMidasProductId(conf),
                        deliverConf.getValue());
                player.getPlayerMoneyMgr().sendMidasDeliverFailCauseCountLimitFlow(productParam, metaData);
                continue;
            }
            deliverConfs.put(conf, deliverConf.getValue());
        }

        long subReason = 0;
        List<Long> changeReservedParams = new ArrayList<>();
        if (metaData != null ){
            subReason = metaData.getSubReason().getSubReason();
            changeReservedParams = metaData.getSubReason().getChangeReservedParamsList();
        }

        return MallDeliverGoods(deliverConfs, billNo, reason,subReason, changeReservedParams);
    }


    public NKErrorCode WolfKillMidasDeliverMallGoods(Collection<NKPair<Integer, Integer>> confsFromMidas, String billNo,
                                             ItemChangeReason reason, DeliverGoodsMetaData metaData) {
        NKErrorCode nkErrorCode = NKErrorCode.OK;
        for (NKPair<Integer, Integer> deliverConf : confsFromMidas) {
            int itemId = deliverConf.getKey();
            int itemNum = deliverConf.getValue();

            String[] metaDataList = metaData.getParams().split("-");
            long subReason = 0;
            List<Long> params = new ArrayList<>();
            if (metaDataList.length>0){
                subReason = Long.parseLong(metaDataList[0]);
                for (int i=1; i<metaDataList.length; i++){
                    params.add(Long.parseLong(metaDataList[i]));
                }
            }

            NKErrorCode nkErrorCodeTmp = WolfKillMallDeliverGoods(itemId, itemNum, billNo, reason, subReason, params);
            if (nkErrorCodeTmp!=NKErrorCode.OK){
                nkErrorCode = nkErrorCodeTmp;
            }
        }
        return nkErrorCode;
    }

    public int getCommodityBuyNum(MallCommodity commodityConf) {
        BuyRecordStruct buyRecord = playerMallInfo.getCommonMallInfo().getBoughtRecord().get(commodityConf.getCommodityId());
        if (commodityConf.getCanAccumulate()) {
            updateCanAccumulateNum(commodityConf);
            return 0;
        }
        if (buyRecord != null && !PlayerCommodityBuyTimesSyncDao.checkBuyRecordExpire(buyRecord)) {
            return buyRecord.getBuyNum();
        }
        return 0;
    }

    private BuyRecordStruct getBuyRecord(MallCommodity commodityConf) {
        BuyRecordStruct buyRecord = playerMallInfo.getCommonMallInfo().getBoughtRecord()
                .get(commodityConf.getCommodityId());
        if (buyRecord == null) {
            buyRecord = new BuyRecordStruct().setCommodityId(commodityConf.getCommodityId());
            playerMallInfo.getCommonMallInfo().getBoughtRecord().put(commodityConf.getCommodityId(), buyRecord);
        }
        return buyRecord;
    }

    private int updateCanAccumulateNum(MallCommodity commodityConf) {
        BuyRecordStruct buyRecord = getBuyRecord(commodityConf);
        if (DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis()) - DateUtils.getDayBeginTimeMs(
                buyRecord.getUpdateTimeMs()) < DateUtils.ONE_DAY_MILLIS) {
            return buyRecord.getCurrentCanBuyNum();
        }
        LOGGER.debug("updateCanAccumulateNum before debug, uid:{} currentCanBuyNum:{} updateTimeMs:{}",
                player.getUid(), buyRecord.getCurrentCanBuyNum(), buyRecord.getUpdateTimeMs());
        int addCanBuyNum = (int) (
                (DateUtils.getDayBeginTimeMs(DateUtils.currentTimeMillis()) - DateUtils.getDayBeginTimeMs(
                        Math.max(player.getRegisterTime(), buyRecord.getUpdateTimeMs()))) / DateUtils.ONE_DAY_MILLIS);
        int actAddCanBuyNum = buyRecord.getUpdateTimeMs() > 0 ? addCanBuyNum : addCanBuyNum + 1;
        int canBuyNum = buyRecord.getCurrentCanBuyNum() + actAddCanBuyNum;
        int beforeCanBuyNum = buyRecord.getCurrentCanBuyNum();
        buyRecord.setCurrentCanBuyNum(Math.min(canBuyNum, commodityConf.getAccumulateMax()))
                .setUpdateTimeMs(DateUtils.currentTimeMillis());
        LOGGER.debug(
                "updateCanAccumulateNum debug, uid:{} commodityId:{} addCanBuyNum:{} actAddCanBuyNum:{} canBuyNum:{} currentCanBuyNum:{}",
                player.getUid(), commodityConf.getCommodityId(), addCanBuyNum, actAddCanBuyNum, canBuyNum,
                buyRecord.getCurrentCanBuyNum());
        if (beforeCanBuyNum != buyRecord.getCurrentCanBuyNum() && commodityConf.getSyncDB()) {
            PlayerCommodityBuyTimesSyncDao.updateCommodityBuyRecord(player.getUid(), commodityConf, 0, buyRecord);
        }
        return buyRecord.getCurrentCanBuyNum();
    }

    //检查是否超过购买上限
    public NKErrorCode checkCommodityBuyLimit(MallCommodity commodityConf, int bugNum) {
        ResMall.MallInfo mallInfoConf = MallInfoConf.getInstance().get(commodityConf.getMallId());
        if (mallInfoConf == null) {
            return NKErrorCode.ResNotFound;
        }

        if (mallInfoConf.getShopType().equals(ShopType.ST_BPExchange)) {
            if (commodityConf.getBpLimit() > 0) {
                var info = player.getBpManager().getAttrBPPublicInfo(BPType.BT_Main);
                if (info.getPay() == 0) {
                    return NKErrorCode.PermitNotUnLock;
                }
                if (info.getLevel() < commodityConf.getBpLimit()) {
                    return NKErrorCode.PermitLevelNotEnough;
                }
            }
        }
        NKErrorCode result = MallCommodityConf.getInstance().isInSale(commodityConf, player.getClientVersion64());
        if (result != NKErrorCode.OK) {
            return result;
        }
        return checkCommodityBuyCountLimit(commodityConf, bugNum);
    }

    //检查是否超过购买数量上限
    public NKErrorCode checkCommodityBuyCountLimit(MallCommodity commodityConf, int bugNum) {
        if (!commodityConf.getLimitType().equals(MallCommodityLimit.MCL_None)
                && getCommodityBuyNum(commodityConf) + bugNum > getCommodityCanBuyNum(commodityConf)) {
            return NKErrorCode.MallCommodityBuyOutLimit;
        }

        // 主题商城购买限制检测
        if (commodityConf.hasCommodityThemeInfo() && themeMallMgr.checkThemeMallBuyLimit(commodityConf,bugNum)) {
            return NKErrorCode.ThemeMallBuyOutLimit;
        }
        return NKErrorCode.OK;
    }

    //添加购买记录
    public void addCommodityBuyRecord(MallCommodity commodityConf, int bugNum) {
        int commodityId = commodityConf.getCommodityId();
        BuyRecordStruct buyRecord = getBuyRecord(commodityConf);
        PlayerCommodityBuyTimesSyncDao.updateCommodityBuyRecord(player.getUid(),commodityConf,bugNum,buyRecord);

        // 主题商城购买限制检测
        if (commodityConf.hasCommodityThemeInfo() && commodityConf.getCommodityThemeInfo().getIsThemeBuyLimit()) {
            themeMallMgr.addThemeMallBuyNum(commodityConf,bugNum);
        }

        new PlayerBuyLimitCommodityEvent(player, commodityId, bugNum).dispatch();
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("addCommodityBuyRecord, uid:{} commodityId:{} bugNum:{}",player.getUid(),commodityId,bugNum);
        }
    }

    public void loadMallBuyRecord(@Nullable List<TcaplusDbWrapper.MallBuyTimesTable> playerCommodityBuyTimes) {
        HashSet<Integer> needSyncCommodityIds = new HashSet<>();
        for (MallCommodity mallCommodityConf : MallCommodityConf.getInstance().getArrayList()) {
            if (mallCommodityConf.getSyncDB()) {
                needSyncCommodityIds.add(mallCommodityConf.getCommodityId());
            }
        }
        // 数据库数据替换内存数据
        if (playerCommodityBuyTimes != null) {
            for (TcaplusDbWrapper.MallBuyTimesTable dbBoughtRecord : playerCommodityBuyTimes) {
                BuyRecordStruct buyRecord = playerMallInfo.getCommonMallInfo().getBoughtRecord(dbBoughtRecord.getCommodityId());
                if (buyRecord == null) {
                    buyRecord = new BuyRecordStruct().setCommodityId(dbBoughtRecord.getCommodityId());
                }
                buyRecord.mergeFromDto(dbBoughtRecord.getCommodityBuyRecord());
                needSyncCommodityIds.remove(dbBoughtRecord.getCommodityId());
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("sync buyRecord from DB ,uid:{} commodityId:{} dbBoughtRecord:{}",
                            player.getUid(), dbBoughtRecord.getCommodityId(), dbBoughtRecord.getCommodityBuyRecord().toString());
                }
            }
        }
        // 新增需要同步的商品购买次数
        if (!needSyncCommodityIds.isEmpty()) {
            try {
                CurrentExecutorUtil.runJob((Callable<Void>) () -> {
                    for (int commodityId : needSyncCommodityIds) {
                        MallCommodity mallCommodityConf = MallCommodityConf.getInstance().get(commodityId);
                        BuyRecordStruct buyRecord = getBuyRecord(mallCommodityConf);
                        proto_BuyRecordStruct.Builder buyRecordDB = proto_BuyRecordStruct.newBuilder();
                        buyRecord.copyToDb(buyRecordDB);
                        NKErrorCode result = PlayerCommodityBuyTimesSyncDao.insertPlayerCommodityBuyTimes(player.getUid(),buyRecordDB.build());
                        if (result != NKErrorCode.OK) {
                            LOGGER.error("insertPlayerCommodityBuyTimes err, uid:{} commodityId:{} result:{}", player.getUid(),commodityId,result);
                        }
                    }
                    return null;
                }, "SyncCommodityId", true);
            } catch (NKCheckedException e) {
                LOGGER.error("SyncCommodityId job run fail", e);
            }
        }
    }

    private void refreshMallBoughtRecord() {
        for (BuyRecordStruct buyRecord : playerMallInfo.getCommonMallInfo().getBoughtRecord().values()) {
            if (!PlayerCommodityBuyTimesSyncDao.checkBuyRecordExpire(buyRecord)) {
                continue;
            }
            LOGGER.debug("refreshMallBoughtRecord clear, Uid:{} record:{}", player.getUid(), buyRecord.toString());
            buyRecord.clear();
        }
    }

    private Collection<Integer> collectMallRedPoint(Set<ResMall.MallCommodity> commodityConfs) {
        ArrayList<Integer> hasRedPointMallIds = new ArrayList<>();
        for (ResMall.MallCommodity commodityConf : commodityConfs) {
            if (hasRedPointMallIds.contains(commodityConf.getMallId())) {
                continue;
            }
            boolean hasRedPoint = doesMallHaveRedPoint(commodityConf.getMallId());
            if (hasRedPoint) {
                hasRedPointMallIds.add(commodityConf.getMallId());
            }
        }
        return hasRedPointMallIds;
    }

    private Collection<MallRedPointType> collectMallRedPointsByMallId(int mallId) {
        ArrayList<MallRedPointType> redPointTypes = new ArrayList<>();
        ShopType shopType = ShopType.forNumber(mallId);
        if (shopType == null) {
            return redPointTypes;
        }
        ResMall.MallInfo mallInfoConf = MallInfoConf.getInstance().get(mallId);
        if (!mallInfoConf.getBuyRedPoint() || !mallInfoConf.getNewRedPoint() || !mallInfoConf.getResetRedPoint()) {
            return redPointTypes;
        }
        List<MallCommodity> commodityConfs = getShopCommodityList(shopType, false);
        if (commodityConfs == null) {
            return redPointTypes;
        }
        MallRedPoint mallRedPointInfo = playerMallInfo.getMallRedPointInfo(mallId);
        if (mallRedPointInfo == null) {
            mallRedPointInfo = new MallRedPoint().setMallId(mallId);
            playerMallInfo.putMallRedPointInfo(mallId,mallRedPointInfo);
        }
        for (ResMall.MallCommodity commodityConf : commodityConfs) {
            if (mallInfoConf.getBuyRedPoint() && !redPointTypes.contains(MallRedPointType.MRPT_RedPoint)
                        && getShowRedPoint(commodityConf) != 0
                        && checkItemsCanBuy(commodityConf)) {
                redPointTypes.add(MallRedPointType.MRPT_RedPoint);
                LOGGER.debug("update mall red point, uid :{} mallId:{} commodityId:{} redType:{}",
                        player.getUid(), mallId, commodityConf.getCommodityId(), MallRedPointType.MRPT_RedPoint);
            }

            if (mallInfoConf.getNewRedPoint() && !redPointTypes.contains(MallRedPointType.MRPT_NewRedPoint)
                        && commodityConf.hasNewSaleNtfTime()) {
                MallRedPointStatus redPointTypeInfo = mallRedPointInfo.getRedPointTypeInfo(
                        MallRedPointType.MRPT_NewRedPoint_VALUE);
                if (redPointTypeInfo == null) {
                    redPointTypeInfo = new MallRedPointStatus().setRedPointType(
                            MallRedPointType.MRPT_NewRedPoint_VALUE);
                    mallRedPointInfo.putRedPointTypeInfo(MallRedPointType.MRPT_NewRedPoint_VALUE, redPointTypeInfo);
                }
                if (redPointTypeInfo.getUpdateTime() < commodityConf.getNewSaleNtfTime().getSeconds()) {
                    redPointTypeInfo.setStatus(RedDotPointShow.RDPS_Show_VALUE).setUpdateTime(DateUtils.currentTimeSec());
                    redPointTypes.add(MallRedPointType.MRPT_NewRedPoint);
                    LOGGER.debug("update mall red point, uid :{} mallId:{} commodityId:{} redType:{}",
                            player.getUid(), mallId, commodityConf.getCommodityId(), MallRedPointType.MRPT_NewRedPoint);
                }
            }

            if (mallInfoConf.getResetRedPoint() && !redPointTypes.contains(MallRedPointType.MRPT_ResetBuyRedPoint)
                        && commodityConf.getResetBuyTimesRedPointShow()) {
                BuyRecordStruct buyRecord = playerMallInfo.getCommonMallInfo().getBoughtRecord().get(commodityConf.getCommodityId());
                if (!PlayerCommodityBuyTimesSyncDao.checkBuyRecordExpire(buyRecord)) {
                    continue;
                }
                MallRedPointStatus redPointTypeInfo = mallRedPointInfo.getRedPointTypeInfo(
                        MallRedPointType.MRPT_ResetBuyRedPoint_VALUE);
                if (redPointTypeInfo == null) {
                    redPointTypeInfo = new MallRedPointStatus().setRedPointType(MallRedPointType.MRPT_ResetBuyRedPoint_VALUE);
                    mallRedPointInfo.putRedPointTypeInfo(MallRedPointType.MRPT_ResetBuyRedPoint_VALUE, redPointTypeInfo);
                }
                redPointTypeInfo.setStatus(RedDotPointShow.RDPS_Show_VALUE).setUpdateTime(DateUtils.currentTimeSec());
                redPointTypes.add(MallRedPointType.MRPT_ResetBuyRedPoint);
                LOGGER.debug("update mall red point, uid :{} mallId:{} commodityId:{} redType:{}",
                        player.getUid(), mallId, commodityConf.getCommodityId(), MallRedPointType.MRPT_ResetBuyRedPoint);
            }

            if((!mallInfoConf.getBuyRedPoint() || (mallInfoConf.getBuyRedPoint() && redPointTypes.contains(MallRedPointType.MRPT_RedPoint)))
                    && (!mallInfoConf.getNewRedPoint() || (mallInfoConf.getNewRedPoint() && redPointTypes.contains(MallRedPointType.MRPT_NewRedPoint)))
                    && (!mallInfoConf.getResetRedPoint() || (mallInfoConf.getResetRedPoint() && redPointTypes.contains(MallRedPointType.MRPT_ResetBuyRedPoint)))
            ) {
                break;
            }
        }
        return redPointTypes;
    }

    public boolean doesMallHaveRedPoint(int mallId) {
        ShopType shopType = ShopType.forNumber(mallId);
        if (shopType == null) {
            return false;
        }
        List<MallCommodity> commodityConfs = getShopCommodityList(shopType, false);
        if (commodityConfs == null) {
            return false;
        }
        for (ResMall.MallCommodity commodityConf : commodityConfs) {
            if (getShowRedPoint(commodityConf) != 0
                    && commodityConf.getCoinType()==  CoinType.CT_FarmDailyExchangeCoin_VALUE) {
                // 该类型道具不需要判断玩家是拥有足够的货币，可以直接显示红点
                return true;
            }
            if (getShowRedPoint(commodityConf) != 0
                    && checkItemsCanBuy(commodityConf)) {
                return true;
            }
        }
        return false;
    }

    // 检查该商品是否可以购买
    public boolean checkItemsCanBuy(ResMall.MallCommodity commodityConf) {
        return checkCommodityBuyLimit(commodityConf,1) == NKErrorCode.OK
                && checkBuyItemsEnough(commodityConf);
    }

    // 商品当前可购买的总数
    private int getCommodityCanBuyNum(ResMall.MallCommodity commodityConf) {
        if (commodityConf.getCanAccumulate()) {
            return updateCanAccumulateNum(commodityConf);
        }
        return commodityConf.getLimitNum() + player.getWolfKillMgr().getMonthCardAddLimit(commodityConf);
    }

    private boolean checkBuyItemsEnough(ResMall.MallCommodity commodityConf) {
        if (commodityConf.getCanDirectBuy()) {
            return true;
        }
        ChangedItems costItems = new ChangedItems(ItemChangeReason.ICR_MallCommodityBuy.getNumber(), "");
        if (commodityConf.getDiscountPrice() > 0) {
            costItems.mergeItemInfo(commodityConf.getCoinType(), commodityConf.getDiscountPrice());
        } else {
            costItems.mergeItemInfo(commodityConf.getCoinType(), commodityConf.getPrice());
        }
        return player.getBagManager().isItemsEnough(costItems);
    }

    //玩家信息存储是否显示红点
    private int getShowRedPoint(ResMall.MallCommodity commodityConf) {
        CommodityRedPoint commodityRedPoint = player.getUserAttr().getMallInfo().getCommonMallInfo().getCommodityNtf()
                .get(commodityConf.getCommodityId());
        if (commodityRedPoint != null) {
            return commodityRedPoint.getShowRedPoint();
        }
        return commodityConf.getShowRedPoint();
    }

    // 刷新商城购买红点     20
    public void syncMallStatus(Set<ResMall.MallCommodity> commodityConfs, boolean withConf) {
        CsMall.MallRealTimeNtf.Builder ntf = CsMall.MallRealTimeNtf.newBuilder()
                .addMallStatuses(
                        CsMall.MallList.newBuilder()
                                .setStatusType(CsMall.MallStatusType.MST_RedPoint)
                                //.addAllMallIds(collectMallRedPoint(commodityConfs))
                                .addAllMallIds(collectAllMallRedPoint())
                                .build()
                );

        if (withConf) {
            ntf.addAllMallConfs(MallInfoConf.getInstance().getAll());
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLREALTIMENTF, ntf);
    }

    // 刷新商城购买红点
    public void syncAllMallStatus(boolean withConf) {
        CsMall.MallRealTimeNtf.Builder ntf = CsMall.MallRealTimeNtf.newBuilder()
                .addMallStatuses(
                        CsMall.MallList.newBuilder()
                                .setStatusType(CsMall.MallStatusType.MST_RedPoint)
                                .addAllMallIds(collectAllMallRedPoint())
                                .build()
                );

        if (withConf) {
            ntf.addAllMallConfs(MallInfoConf.getInstance().getAll());
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLREALTIMENTF, ntf);
    }

    private Collection<Integer> collectAllMallRedPoint() {
        ArrayList<Integer> hasRedPointMallIds = new ArrayList<>();
        for (int mallId : collectOpenedMalls()) {
            boolean hasRedPoint = doesMallHaveRedPoint(mallId);
            if (hasRedPoint) {
                hasRedPointMallIds.add(mallId);
            }
        }
        return hasRedPointMallIds;
    }

    private void refreshdailyRedPoints(){
        for (MallCommodity commodityConf : MallCommodityConf.getInstance().getArrayList()) {
            if(commodityConf.getShopSort()==0 &&  commodityConf.getShowRedPoint()==1){
                CommodityRedPoint commodityRedPoint = player.getUserAttr().getMallInfo().getCommonMallInfo().getCommodityNtf().get(commodityConf.getCommodityId());
                if(commodityRedPoint!=null){
                    commodityRedPoint.setShowRedPoint(1);
                }else {
                    commodityRedPoint=new CommodityRedPoint().setCommodityId(commodityConf.getCommodityId()).setShowRedPoint(1);
                }
                player.getUserAttr().getMallInfo().getCommonMallInfo().getCommodityNtf().put(commodityConf.getCommodityId(),commodityRedPoint);
                break;
            }

        }


    }
    private void refreshRedPoints(boolean withConf) {
        CsMall.MallRealTimeNtf.Builder ntf = CsMall.MallRealTimeNtf.newBuilder();
        MallList.Builder buyMallMsg = MallList.newBuilder()/**.setStatusType(MallRedPointType.MRPT_RedPoint)**/;
        MallList.Builder newMallMsg = CsMall.MallList.newBuilder()/**.setStatusType(MallRedPointType.MRPT_NewRedPoint)**/;
        MallList.Builder resetMallMsg = CsMall.MallList.newBuilder()/**.setStatusType(MallRedPointType.MRPT_ResetBuyRedPoint)**/;
        for (int mallId : collectOpenedMalls()) {
            ResMall.MallInfo mallInfoConf = MallInfoConf.getInstance().get(mallId);
            if (mallInfoConf == null || (!mallInfoConf.getBuyRedPoint() && !mallInfoConf.getNewRedPoint() && !mallInfoConf.getResetRedPoint())) {
                continue;
            }
            Collection<MallRedPointType> mallRedPointTypes = collectMallRedPointsByMallId(mallId);
            if (!mallRedPointTypes.isEmpty()) {
                if (mallRedPointTypes.contains(MallRedPointType.MRPT_RedPoint)) {
                    buyMallMsg.addMallIds(mallId);
                }
                if (mallRedPointTypes.contains(MallRedPointType.MRPT_NewRedPoint)) {
                    newMallMsg.addMallIds(mallId);
                }
                if (mallRedPointTypes.contains(MallRedPointType.MRPT_ResetBuyRedPoint)) {
                    resetMallMsg.addMallIds(mallId);
                }
            }
        }
        ntf.addMallStatuses(buyMallMsg).addMallStatuses(newMallMsg).addMallStatuses(resetMallMsg);
        if (withConf) {
            ntf.addAllMallConfs(MallInfoConf.getInstance().getAll());
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLREALTIMENTF, ntf);
    }

    public void setRedPointShow(int commodityId, int redPointShow) {
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            NKErrorCode.ResNotFound.throwError(
                    "setRedPointShow get commodity config error, UID:{} commodityId:{}", player.getUid(),
                    commodityId);
            return;
        }
        CommodityRedPoint commodityRedPoint = player.getUserAttr().getMallInfo().getCommonMallInfo().getCommodityNtf()
                .get(commodityId);
        if (commodityRedPoint == null) {
            commodityRedPoint = new CommodityRedPoint();
            commodityRedPoint.setCommodityId(commodityId);
            player.getUserAttr().getMallInfo().getCommonMallInfo().getCommodityNtf()
                    .put(commodityId, commodityRedPoint);
        }
        commodityRedPoint.setShowRedPoint(redPointShow);
        checkCommodityRedPointNtf(commodityConf,true);
        if (redPointShow == 0) {
            unRegisterCommodityRedPointNtfByCommodity(commodityConf);
        } else {
            registerCommodityRedPointNtfByCommodity(commodityConf);
        }
        HashSet<ResMall.MallCommodity> buyConf = new HashSet<>();
        buyConf.add(commodityConf);
        syncMallStatus(buyConf, false);
    }

    public boolean checkActivityShopTypeRedPoint(int activityId, ShopType shopType, SetAttrObj<Integer> activityFilterCommodity) {
        if (shopType == null) {
            return false;
        }
        Map<Integer, MallCommodity> commodityList = MallCommodityConf.getInstance()
                .getCommodityByMallId(shopType.getNumber());
        if (commodityList == null) {
            return false;
        }
        for (MallCommodity commodityConf : commodityList.values()) {
            if (activityFilterCommodity.contains(commodityConf.getCommodityId())) {
                continue;
            }
            if (getShowRedPoint(commodityConf) == 0) {
                continue;
            }
            if (!checkItemsCanBuy(commodityConf)) {
                continue;
            }
            LOGGER.debug("checkActivityShopTypeRedPoint uid:{} activityId:{} shopType:{} commodityId:{}",player.getUid(),activityId,shopType,commodityConf.getCommodityId());
            return true;
        }
        return false;
    }

    public void addActivityCommodityFilterCommodity(int mallId, SetAttrObj<Integer> activityFilterCommodity) {
        ShopType shopType = ShopType.forNumber(mallId);
        if (shopType == null) {
            return;
        }
        List<MallCommodity> commodityConfs = getShopCommodityList(shopType, false);
        if (commodityConfs == null) {
            return;
        }
        for (ResMall.MallCommodity commodityConf : commodityConfs) {
            if (activityFilterCommodity.contains(commodityConf.getCommodityId())) {
                continue;
            }
            if (getShowRedPoint(commodityConf) == 0) {
                continue;
            }
            if (!checkItemsCanBuy(commodityConf)) {
                continue;
            }
            activityFilterCommodity.add(commodityConf.getCommodityId());
        }
    }

    public void unRegisterCommodityRedPointNtfByCommodity(MallCommodity commodityConf) {
        if (watchCoinTypeChangeCommodityIds.containsKey(commodityConf.getCoinType())) {
            watchCoinTypeChangeCommodityIds.get(commodityConf.getCoinType()).remove(commodityConf.getCommodityId());
        }
    }

    public void registerCommodityRedPointNtfByCommodity(MallCommodity commodityConf) {
        if (getShowRedPoint(commodityConf) == 0 || commodityConf.getCanDirectBuy()) {
            return;
        }
        if (checkCommodityBuy(commodityConf, 1) != NKErrorCode.OK) {
            return;
        }
        if (!watchCoinTypeChangeCommodityIds.containsKey(commodityConf.getCoinType())) {
            watchCoinTypeChangeCommodityIds.put(commodityConf.getCoinType(), new HashSet<>());
        }
        watchCoinTypeChangeCommodityIds.get(commodityConf.getCoinType()).add(commodityConf.getCommodityId());
    }

    public void registerCommodityRedPointNtfByShopType(ShopType shopType) {
        if (shopType == null) {
            return;
        }
        Map<Integer, MallCommodity> commodityList = MallCommodityConf.getInstance()
                .getCommodityByMallId(shopType.getNumber());
        if (commodityList == null) {
            return;
        }
        for (MallCommodity commodityConf : commodityList.values()) {
            if (getShowRedPoint(commodityConf) == 0 || commodityConf.getCanDirectBuy()) {
                continue;
            }
            if (checkCommodityBuy(commodityConf, 1) != NKErrorCode.OK) {
                continue;
            }
            if (!watchCoinTypeChangeCommodityIds.containsKey(commodityConf.getCoinType())) {
                watchCoinTypeChangeCommodityIds.put(commodityConf.getCoinType(), new HashSet<>());
            }
            watchCoinTypeChangeCommodityIds.get(commodityConf.getCoinType()).add(commodityConf.getCommodityId());
        }
    }

    public void onGetItemEvent(int itemId) {
        if (watchCoinTypeChangeCommodityIds.containsKey(itemId)) {
            HashSet<Integer> shopTypeSet = new HashSet<>();
            for (int commodityId : watchCoinTypeChangeCommodityIds.get(itemId)) {
                MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
                if (commodityConf == null) {
                    continue;
                }
                shopTypeSet.add(commodityConf.getMallId());
            }
            if (!shopTypeSet.isEmpty()) {
                HashSet<Integer> checkedActivity = new HashSet<>();
                for (int shopType : shopTypeSet) {
                    Set<Integer> activityIds = ActivityMainConfig.getInstance().getActivityIdByShopType(shopType);
                    if (activityIds != null && !activityIds.isEmpty() && doesMallHaveRedPoint(shopType)) {
                        for (int activityId : activityIds) {
                            if (checkedActivity.contains(activityId)) {
                                continue;
                            }
                            BaseActivity baseActivity = player.getActivityManager().getRunningActivity(activityId);
                            if (baseActivity != null) {
                                baseActivity.checkActivityRedPoint();
                            }
                            checkedActivity.add(activityId);
                        }
                    }
                }
            }
        }
    }

    public void checkCommodityRedPointNtf(MallCommodity commodityConf, boolean setShow) {
        Set<Integer> activityIds = ActivityMainConfig.getInstance().getActivityIdByShopType(commodityConf.getMallId());
        if (activityIds != null && !activityIds.isEmpty()) {
            for (int activityId : activityIds) {
                BaseActivity baseActivity = player.getActivityManager().getRunningActivity(activityId);
                if (baseActivity != null) {
                    if (setShow) {
                        baseActivity.getActivityUnit().getClearRedDotInfo().clearFilterCommodity();
                        LOGGER.debug("checkCommodityRedPointNtf setShow clearFilterCommodity,uid:{} activityId:{}"
                                ,player.getUid(),activityId);
                    }
                    baseActivity.checkActivityRedPoint();
                }
            }
        }
    }

    public void checkNewScenePackagePush() {
        ScenePackageMgr.pushNewScenePackage(player);
    }

    public boolean checkScenePackageExpiredAndNotBuy(int pushId, int commodityId) {
        return ScenePackageMgr.checkScenePackageExpiredAndNotBuy(player, pushId, commodityId);
    }

    public boolean checkScenePackagePushedAndNotBuy(int pushId, int commodityId) {
        return ScenePackageMgr.checkScenePackagePushedAndNotBuy(player, pushId, commodityId);
    }

    public boolean checkScenePackageCanBuy(int pushId) {
        return ScenePackageMgr.checkScenePackageCanBuy(player, pushId);
    }

    public boolean checkLevelPackageCanBuy(int pushId, int commodityId) {
        return ScenePackageMgr.checkLevelPackageCanBuy(player, pushId, commodityId);
    }

    public boolean checkMultiplePackageCanBuy(int pushId, int commodityId) {
        return ScenePackageMgr.checkMultiplePackageCanBuy(player, pushId, commodityId);
    }

    public long MallCommodityBatchDemand(int commodityId, int demandNum, Long friendUid) {
        if (demandNum <= 0) {
            NKErrorCode.MallCommodityDemandParamsErr.throwError("invalid demand count");
            return 0;
        }
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null || !commodityConf.getCanGift()) {
            NKErrorCode.ResNotFound.throwError(
                    "MallCommodityBuy get config error, UID:{} commodityId:{} demandNum:{} canGift:{}",
                    player.getUid(), commodityId, demandNum, commodityConf.getCanGift());
        }
        // 有些商品只支持ios小游戏发起索取
        boolean isIosMiniGame = player.getSession().getClientInfo().isMiniGame() && player.getPlatId() == 0;
        if (commodityConf.getOnlyIosMiniGameCanDemand() && !isIosMiniGame) {
            NKErrorCode.MallDemandOnlyIosMiniGame.throwError("only ios minigame can demand, {}", commodityId);
        }
        if (commodityConf.getOnlyIosMiniGameCanDemand() && !PropertyFileReader.getRealTimeBooleanItem(
                "only_ios_minigame_demand_switch", true)) {
            NKErrorCode.MallDemandOnlyIosMiniGameSwitch.throwError("only_ios_minigame_demand_switch false, {}",
                    commodityId);
        }
        ResRelation.RelationConf demandLevelLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_DEMAND_BEGIN_LEVEL);
        if (commodityConf.getOnlyIosMiniGameCanDemand()) {
            demandLevelLimitConf = RelationConfData.getInstance()
                    .get(RelationConfEnum.RCE_MALL_DEMAND_BEGIN_LEVEL_MINIGAME);
        }
        if (player.getLevel() < demandLevelLimitConf.getValue()) {
            NKErrorCode.MallDemandBeginLevelTooLow.throwError("level is low");
        }

        // 这种类型不允许索取
        if (commodityConf.getGiftChecker().getCheckType() == MallGiftCheckType.MGCT_SelfWolfKillTreasureLevelType){
            NKErrorCode.MallDemandBeginLevelTooLow.throwError("get gift mall item {} check type {} checker error",
                        commodityConf.getCommodityId(), commodityConf.getGiftChecker().getCheckType());
        }

        NKErrorCode result = MallCommodityConf.getInstance().isInSale(commodityConf, player.getClientVersion64());
        if (result != NKErrorCode.OK) {
            result.throwError("not in sale");
            return 0;
        }
        long intimacyRelation = player.getFriendManager().getFriendIntimacy(friendUid);
        ResRelation.RelationConf demandIntimateLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_DEMAND_BEGIN_INTIMATE_NUM);
        if (commodityConf.getOnlyIosMiniGameCanDemand()) {
            demandIntimateLimitConf = RelationConfData.getInstance()
                    .get(RelationConfEnum.RCE_MALL_DEMAND_BEGIN_INTIMATE_NUM_MINIGAME);
        }
        if (intimacyRelation < demandIntimateLimitConf.getValue()) {
            NKErrorCode.MallDemandIntimateNotEnough.throwError("intimate not enough");
        }
        // tlog
        TlogFlowMgr.sendGiftAskFlow(player, friendUid, TlogMacros.GIFTASKEDSTATUS.GIFT_ASKED_BEGIN,
                commodityConf.getMallId(), commodityId, demandNum,
                commodityConf.getGiftCoinType(), commodityConf.getGiftPrice(), commodityConf.getAddIntimacy(),
                (int) intimacyRelation, "");
        if (demandNum > MALL_MAX_BUY_NUM) {
            demandNum = MALL_MAX_BUY_NUM;
        }
        if (commodityConf.getOnlyIosMiniGameCanDemand()) {
            if (!player.getFriendManager().isPlatFriend(friendUid)) {
                NKErrorCode.MallDemandIsNotFriend.throwError("demand player is not friend");
            }
        } else {
            if (!player.getFriendManager().isFriend(friendUid)) {
                NKErrorCode.MallDemandIsNotFriend.throwError("demand player is not friend");
            }
        }
        if (!checkDemandLimit(commodityConf, demandNum, friendUid)) {
            if (commodityConf.getOnlyIosMiniGameCanDemand()) {
                NKErrorCode.MallMonthDemandLimitFailed.throwError("demand limit");
            }
            NKErrorCode.MallDailyDemandLimitFailed.throwError("demand limit");
        }
        ResRelation.RelationConf relationConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_DEMAND_MAIL_TEMPLATE_ID);
        if (relationConf == null) {
            NKErrorCode.MallDemandMailSendFailed.throwError("invalid demand template mail id");
        }
        ResRelation.RelationConf demandMailLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_DEMAND_MAIL_LIMIT);
        if (demandMailLimitConf == null) {
            NKErrorCode.MallDemandMailSendFailed.throwError("invalid demand mail limit");
        }
        if (!isFriendAllowMyDemand(friendUid)) {
            NKErrorCode.MallDemandFriendNotAllowDemand.throwError("demand friend not allow demand");
        }
        try {
            CoRedisCmd<String, String> cmdForString = Cache.getCoRedisCmdForString();
            String demandMailLimitKey = CacheUtil.MallDemandMailLimit.getKey(friendUid);
            long curDemandMailCount = cmdForString.incr(demandMailLimitKey);
            if (curDemandMailCount > demandMailLimitConf.getValue()) {
                cmdForString.decr(demandMailLimitKey);
                LOGGER.info("demand mail num reach limit, curDemandMailCount {}, RCE_MALL_DEMAND_MAIL_LIMIT:{}",
                        curDemandMailCount, demandMailLimitConf.getValue());
                NKErrorCode.MallDemandMailSendFailed.throwError("demand mail num reach limit");
            }
        } catch (Exception ex) {
            LOGGER.error("mall demand redis info op fail", ex);
            NKErrorCode.MallDemandMailSendFailed.throwError("demand mail num reach limit");
        }
        List<com.tencent.wea.attr.RewardItemInfo> rewardItemList = mallCommodity2RewardItemList(commodityConf,
                demandNum);
        MailAttachmentList.Builder attachment = MailAttachmentList.newBuilder();
        attachment.setCommodityInfo(MailCommodityInfo.newBuilder().setCommodityId(commodityId)
                .setCommodityNum(demandNum).build());
        for (com.tencent.wea.attr.RewardItemInfo itemInfo : rewardItemList) {
            var mailItem = ItemInfo.newBuilder().setItemId((int) itemInfo.getId()).setItemNum(itemInfo.getNum());
            if (itemInfo.getExpireTimeMs() > 0) {
                mailItem.setExpireTimeMs(itemInfo.getExpireTimeMs());
                mailItem.setExpireType(itemInfo.getExpireType());
            }
            attachment.addList(MailAttachment.newBuilder().setItemIfo(mailItem).build());
        }
        long mailId = MailInteraction.sendTemplateMail(friendUid, relationConf.getValue(), attachment,
                player.getUid(), null, MailInteraction.TlogSendReason.mallDemand, null, null);
        if (mailId < 0) {
            LOGGER.error("MallCommodityBatchDemand send template mail failed, uid:{}, template id:{}",
                    player.getUid(), relationConf.getValue());
            NKErrorCode.MallDemandMailSendFailed.throwError("send demand mail failed");
        }
        // 增加索要次数
        if (commodityConf.getOnlyIosMiniGameCanDemand()) {
            long curTimes = Framework.currentTimeMillis();
            player.getLimitManager().addValue(CommonLimitType.CLT_IosMiniGameDemandCountLimit, 0L, 1,
                    DateUtils.getFirstDayTimeOfNextMonthTime(curTimes), curTimes);
        } else {
            MallInfo mallInfo = player.getUserAttr().getMallInfo();
            mallInfo.getMallDemandInfo().setDemandCount(mallInfo.getMallDemandInfo().getDemandCount() + 1);
        }
        // tlog
        TlogFlowMgr.sendGiftAskFlow(player, friendUid, TlogMacros.GIFTASKEDSTATUS.GIFT_ASKED_SUCC,
                commodityConf.getMallId(), commodityId, demandNum,
                commodityConf.getGiftCoinType(), commodityConf.getGiftPrice(), commodityConf.getAddIntimacy(),
                (int) intimacyRelation, "");
        return mailId;
    }

    // 默认允许索要
    private boolean isFriendAllowMyDemand(long friendUid) {
        boolean defalutRet = true; // 默认允许索要

        boolean isFeatureEnable = PropertyFileReader.getRealTimeBooleanItem("enable_feature_demand_switch", true);
        if (!isFeatureEnable) {
            return defalutRet;
        }

        Collection<Long> uidSet = Collections.singleton(friendUid);
        Collection<PlayerPublicInfoField> fieldSet = Collections.singleton(PlayerPublicInfoField.DEMAND_SWITCH);
        var infoMap = com.tencent.wea.simpleData.PlayerPublic.batchGetPlayerPublic(uidSet, fieldSet);
        if (infoMap == null || infoMap.isEmpty()) {
            LOGGER.error("get friend player public info failed, uid:{}", friendUid);
            return defalutRet;
        }
        
        var playerInfo = infoMap.get(friendUid);
        if (playerInfo == null) {
            return defalutRet;
        }

        switch (playerInfo.getDemandSwitch()) {
            case 0:
                return defalutRet;
            case 1:
                return !defalutRet;
            case 2: {
                // 亲密关系应该是双向的吧
                int intimateId = player.getIntimateManager().getFriendIntimateId(friendUid);
                LOGGER.debug("self:{}, friend:{}, intimate id:{}", player.getUid(), friendUid, intimateId);
                return intimateId > 0;
            }
            default:
                LOGGER.warn("friend:{} demand switch:{} invalid", friendUid, playerInfo.getDemandSwitch());
                return false;
        }
    }


    public void GiftRedUpdateDot(int mallId) {
        ShopType shopType = ShopType.forNumber(mallId);
        if (shopType == null) {
            return ;
        }
        List<MallCommodity> commodityConfs = getShopCommodityList(shopType, false);
        if (commodityConfs == null) {
            return ;
        }
        for (ResMall.MallCommodity commodityConf : commodityConfs) {
            CommodityRedPoint commodityRedPoint = player.getUserAttr().getMallInfo().getCommonMallInfo().getCommodityNtf().get(commodityConf.getCommodityId());
            if(commodityRedPoint!=null){
                commodityRedPoint.setShowRedPoint(0);
            }else {
                commodityRedPoint=new CommodityRedPoint().setCommodityId(commodityConf.getCommodityId()).setShowRedPoint(0);
            }
            player.getUserAttr().getMallInfo().getCommonMallInfo().getCommodityNtf().put(commodityConf.getCommodityId(),commodityRedPoint);
        }

    }


    private boolean checkDemandLimit(MallCommodity commodityConf, int demandNum, long friendUid) {
        long clientVersion = PlayerPublicDao.getClientVersionByUid(friendUid);
        NKErrorCode ret = MallCommodityConf.getInstance().isInSale(commodityConf, clientVersion);
        if (ret == NKErrorCode.MallCommodityBuyVersionErr) {
            NKErrorCode.MallCommodityDemandFriendVersionErr.throwError("friend version err");
            return false;
        }
        if (commodityConf.getOnlyIosMiniGameCanDemand()) {
            ResRelation.RelationConf demandCountLimitConf = RelationConfData.getInstance()
                    .get(RelationConfEnum.RCE_MALL_DEMAND_COUNT_LIMIT_MINIGAME);
            ResRelation.RelationConf singleDemandItemLimitConf = RelationConfData.getInstance()
                    .get(RelationConfEnum.RCE_MALL_SINGLE_DEMAND_ITEM_LIMIT_MINIGAME);
            if (demandCountLimitConf != null && singleDemandItemLimitConf != null) {
                int currentNum = (int) player.getLimitManager()
                        .getValue(CommonLimitType.CLT_IosMiniGameDemandCountLimit, 0L);
                if (currentNum >= demandCountLimitConf.getValue()) {
                    return false;
                }
                if ((commodityConf.getLimitNum() > 0 && demandNum > commodityConf.getLimitNum())
                        || demandNum > singleDemandItemLimitConf.getValue()) {
                    return false;
                }
                return true;
            }
        } else {
            ResRelation.RelationConf demandCountLimitConf = RelationConfData.getInstance()
                    .get(RelationConfEnum.RCE_MALL_DEMAND_COUNT_LIMIT);
            ResRelation.RelationConf singleDemandItemLimitConf = RelationConfData.getInstance()
                    .get(RelationConfEnum.RCE_MALL_SINGLE_DEMAND_ITEM_LIMIT);
            if (demandCountLimitConf != null && singleDemandItemLimitConf != null) {
                MallInfo mallInfo = player.getUserAttr().getMallInfo();
                if (mallInfo.getMallDemandInfo().getDemandCount() >= demandCountLimitConf.getValue()) {
                    return false;
                }
                if ((commodityConf.getLimitNum() > 0 && demandNum > commodityConf.getLimitNum())
                        || demandNum > singleDemandItemLimitConf.getValue()) {
                    return false;
                }
                return true;
            }
        }
        LOGGER.warn("checkDemandLimit failed : invalid cfg");
        return false;
    }

    private boolean checkGiveLimit(MallCommodity commodityConf, int giveNum, long friendUid) {
        long clientVersion = PlayerPublicDao.getClientVersionByUid(friendUid);
        NKErrorCode ret = MallCommodityConf.getInstance().isInSale(commodityConf, clientVersion);
        if (ret == NKErrorCode.MallCommodityBuyVersionErr) {
            NKErrorCode.MallCommodityGivenFriendVersionErr.throwError("friend version err");
            return false;
        }
        ResRelation.RelationConf giveCountLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_GIVE_COUNT_LIMIT);
        ResRelation.RelationConf singleGiveItemLimitConf =
                commodityConf.getOnlyIosMiniGameCanDemand() ? RelationConfData.getInstance()
                        .get(RelationConfEnum.RCE_MALL_SINGLE_GIVE_ITEM_LIMIT_MINIGAME) : RelationConfData.getInstance()
                        .get(RelationConfEnum.RCE_MALL_SINGLE_GIVE_ITEM_LIMIT);
        if (giveCountLimitConf != null && singleGiveItemLimitConf != null) {
            MallInfo mallInfo = player.getUserAttr().getMallInfo();
            if (mallInfo.getMallDemandInfo().getGiveCount() >= giveCountLimitConf.getValue()) {
                return false;
            }
            if ((commodityConf.getLimitNum() > 0 && giveNum > commodityConf.getLimitNum())
                    || giveNum > singleGiveItemLimitConf.getValue()) {
                return false;
            }
            return true;
        }
        LOGGER.warn("checkGiveLimit failed : invalid cfg");
        return false;
    }

    private boolean checkGiveCommodityTotalLimit(MallCommodity commodityConf, int giveNum, long friendUid) {
        ResRelation.RelationConf giveSameItemLimitConf =
                commodityConf.getOnlyIosMiniGameCanDemand() ? RelationConfData.getInstance()
                        .get(RelationConfEnum.RCE_MALL_GIVE_SAME_ITEM_LIMIT_MINIGAME) : RelationConfData.getInstance()
                        .get(RelationConfEnum.RCE_MALL_GIVE_SAME_ITEM_LIMIT);
        if (giveSameItemLimitConf != null) {
            int limit = giveSameItemLimitConf.getValue();
            MallInfo mallInfo = player.getUserAttr().getMallInfo();

            int commodityId = commodityConf.getCommodityId();
            long curCommodityNum = 0;

            for (MallGiveRecord record : mallInfo.getMallGiveRecord().values()) {
                for (CommodityInfo commodityInfo : record.getGiveCommidityList().values()) {
                    if (commodityInfo.getId() == commodityId) {
                        curCommodityNum += commodityInfo.getNum();
                    }
                }
            }

            if (curCommodityNum + giveNum > limit) {
                LOGGER.error("cur give commoidity-{} number is {} while limit is {}, cannot add {}",
                        commodityConf.getCommodityId(), curCommodityNum, limit, giveNum);
                return true;
            }
        }
        LOGGER.warn("checkGiveSameItemLimit failed : invalid cfg");
        return false;
    }

    private void dailyClearMallDemandInfo() {
        player.getUserAttr().getMallInfo().getMallDemandInfo().clear();
    }

    private int addMallGiveRecord(Long friendUid, List<com.tencent.wea.attr.RewardItemInfo> giveItemList,
            List<CommodityInfo> commodityInfoList, long mailId, long giftPreAllocMs, proto_MallGiftCard cardRecord) {
        ResRelation.RelationConf giveRecordLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_GIVE_RECORD_LIMIT);
        if (giveRecordLimitConf == null) {
            LOGGER.error("addMallGiveRecord failed : invalid limit cfg");
            return -1;
        }
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        int newGiveId = 0;
        if (mallGiveIdList.size() > 0) {
            newGiveId = mallGiveIdList.get(mallGiveIdList.size() - 1) + 1;
        }
        // 确保显示数量不超过上限
        while (mallGiveIdList.size() - getDeletedGiveRecordSize() >= giveRecordLimitConf.getValue()) {
            int mallGiveId = mallGiveIdList.get(0);
            mallInfo.getMallGiveRecord().remove(mallGiveId);
            mallGiveIdList.remove(0);
            removeGiveIdFromDeletedList(mallGiveId);
            LOGGER.debug("addMallGiveRecord show remove give id:{}, {}", player.getUid(), mallGiveId);
        }
        // 确保存储数量不超过显示数量的double, 找到giveId已被删除最小的
        if (mallGiveIdList.size() >= giveRecordLimitConf.getValue() * 2) {
            for (int i = 0; i < mallGiveIdList.size(); i++) {
                int firstDeletedGiveId = mallGiveIdList.get(i);
                if (isDeletedGiveRecord(firstDeletedGiveId)) {
                    mallInfo.getMallGiveRecord().remove(firstDeletedGiveId);
                    mallGiveIdList.remove(i);
                    removeGiveIdFromDeletedList(firstDeletedGiveId);
                    LOGGER.debug("addMallGiveRecord limit remove give id:{}, {}", player.getUid(), firstDeletedGiveId);
                    break;
                }
            }
        }
        MallGiveRecord giveRecord = new MallGiveRecord();
        giveRecord.setFriendUid(friendUid);
        giveRecord.setGiveTime(giftPreAllocMs / 1000); // 客户端用单位秒
        for (com.tencent.wea.attr.RewardItemInfo giveItem : giveItemList) {
            giveRecord.putGiveItemList(giveItem.getId(), giveItem);
        }
        for (CommodityInfo commodityInfo : commodityInfoList) {
            giveRecord.putGiveCommidityList(commodityInfo.getId(), commodityInfo);
        }
        if (cardRecord != null) {
            giveRecord.getCard().mergeFromDto(cardRecord);
        }
        mallInfo.getMallGiveRecord().put(newGiveId, giveRecord);
        mallInfo.getMallDemandInfo().setGiveCount(mallInfo.getMallDemandInfo().getGiveCount() + 1);
        mallGiveIdList.add(newGiveId);
        return newGiveId;
    }

    public NKErrorCode mallGiftCardSenderRecordResetWords(MallGiftCard card) {
        if (card.getCardType() <= 0) {
            return NKErrorCode.OK;
        }
        if (card.getWordsContent().isEmpty()) {
            return NKErrorCode.OK;
        }

        // 清空自定义语句，并置为默认配置
        card.setWordsContent("");
        var conf = MallGiftCardConf.getInstance().getByCardType(card.getCardType());
        if (conf == null || conf.getBlessingIdsCount() <= 0) {
            LOGGER.error("getByCardType failed, cardType:{}", card.getCardType());
            return NKErrorCode.ResNotFound;
        }

        card.setWordsId(conf.getBlessingIds(0));
        return NKErrorCode.OK;
    }

    public NKErrorCode mallGiftCardSenderResetWords(long receiverUid, long giftPreAllocMs) {
        List<MallGiveRecord> records = getGiveRecordsOfMallGiftCard(receiverUid, giftPreAllocMs);
        if (records.isEmpty()) {
            LOGGER.error("getGiveRecordsOfMallGiftCard failed, uid:{}, receiverUid:{}, giftId:{}",
                    player.getUid(), receiverUid, giftPreAllocMs);
            return NKErrorCode.MallGiftCardCannotFind;
        }

        for (MallGiveRecord record : records) {
            var ret = mallGiftCardSenderRecordResetWords(record.getCard());
            if (ret.hasError()) {
                LOGGER.error("mallGiftCardSenderRecordResetWords failed, ret:{}, uid:{}, receiverUid:{}, giftId:{}",
                        ret, player.getUid(), receiverUid, giftPreAllocMs);
            }
        }

        ntfMallGiftCardChange(true, null);
        return NKErrorCode.OK;
    }

    public List<MallGiveRecord> getGiveRecordsOfMallGiftCard(long friendUid, long giftPreAllocMs) {
        List<MallGiveRecord> records = new ArrayList<>();

        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        for (MallGiveRecord record : mallInfo.getMallGiveRecord().values()) {
            if (record.getFriendUid() == friendUid && record.getGiveTime() == giftPreAllocMs / 1000
                    && record.getCard().getCardType() > 0) {    // 必须是赠礼卡
                records.add(record);
            }
        }
        return records;
    }

    private MallGiveRecord getRecordByGiveId(int giveId) {
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        return mallInfo.getMallGiveRecord(giveId);
    }

    private boolean isDeletedGiveRecord(int giveId) {
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        return mallInfo.getMallDeletedGiveRecord().contains(giveId);
    }

    private void addGiveRecordToDeletedList(int deleteGiveId) {
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        mallInfo.getMallDeletedGiveRecord().add(deleteGiveId);
    }

    private int getDeletedGiveRecordSize() {
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        return mallInfo.getMallDeletedGiveRecord().size();
    }

    private void removeGiveIdFromDeletedList(int deleteGiveId) {
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        mallInfo.removeMallDeletedGiveRecord(deleteGiveId);
    }

    public void MallGiveRecordList(CsMall.MallGiveRecordList_S2C_Msg.Builder rspMsg) {
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        for (int i = mallGiveIdList.size() - 1; i >= 0; i--) {
            int mallGiveId = mallGiveIdList.get(i);
            if (isDeletedGiveRecord(mallGiveId)) {
                continue;
            }
            rspMsg.addGiveRecord(mallInfo.getMallGiveRecord().get(mallGiveIdList.get(i)).getCopyCsBuilder());
        }
    }

    public NKErrorCode MallDeleteGiveRecord(List<Integer> deleteList) {
        LOGGER.debug("MallDeleteGiveRecord player:{}, deleteIdList:{}", player.getUid(), deleteList);
        for (Integer deleteGiveId : deleteList) {
            if (isDeletedGiveRecord(deleteGiveId)) {
                LOGGER.info("MallDeleteGiveRecord has delete failed:{}, {}", player.getUid(), deleteGiveId);
                return NKErrorCode.MallGiveRecrodNotExist;
            }
            if (getRecordByGiveId(deleteGiveId) == null) {
                LOGGER.info("MallDeleteGiveRecord record not exist failed:{}, {}", player.getUid(), deleteGiveId);
                return NKErrorCode.MallGiveRecrodNotExist;
            }
        }
        for (Integer deleteGiveId : deleteList) {
            addGiveRecordToDeletedList(deleteGiveId);
        }
        return NKErrorCode.OK;
    }

    public int MallCommodityBatchGive(int commodityId, int giveNum, Long friendUid,
            int clientUnitPrice, boolean isDirectBy, int giveSource, MallGiftCardInfo card) {
        if (giveNum <= 0) {
            NKErrorCode.MallCommodityGiveParamsErr.throwError("invalid give count");
            return 0;
        }
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null || !commodityConf.getCanGift()) {
            NKErrorCode.ResNotFound.throwError(
                    "MallCommodityBatchGive get config error, UID:{} commodityId:{} giveNum:{} canGift:{}",
                    player.getUid(), commodityId, giveNum, commodityConf.getCanGift());
            return 0;
        }
        if (commodityConf.getOnlyIosMiniGameCanDemand() && !PropertyFileReader.getRealTimeBooleanItem(
                "only_ios_minigame_demand_switch", true)) {
            NKErrorCode.MallGiveOnlyIosMiniGameSwitch.throwError("only_ios_minigame_demand_switch false, {}",
                    commodityId);
        }
        ResRelation.RelationConf giveCountLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_GIVE_BEGIN_LEVEL);
        if (player.getLevel() < giveCountLimitConf.getValue()) {
            NKErrorCode.MallGiveBeginLevelTooLow.throwError("level is low");
        }
        NKErrorCode result = MallCommodityConf.getInstance().isInSale(commodityConf, player.getClientVersion64());
        if (result != NKErrorCode.OK) {
            result.throwError("not in sale");
            return 0;
        }
        if (!player.getFriendManager().isFriend(friendUid)) {
            LOGGER.error("MallCommodityBatchGive not friend, {}, {}", player.getUid(), friendUid);
            NKErrorCode.FriendManagerIllegalFriendRelation.throwError("mall batch give not friend,{},{}",
                    player.getUid(), friendUid);
            return 0;
        }
        long intimacyRelation = player.getFriendManager().getFriendIntimacy(friendUid);
        // 生日送礼条件检测
        player.getBirthdayMgr().birthdayGiftSendCheck(card);
        // tlog
        TlogFlowMgr.sendGiftSendFlow(player, friendUid, commodityConf.getMallId(), commodityId, giveNum,
                commodityConf.getGiftCoinType(), commodityConf.getGiftPrice(), commodityConf.getAddIntimacy(),
                (int) intimacyRelation, giveSource, TlogMacros.GIFTSENDSTATUS.GIFT_SEND_BEGIN, "", "", 0L, 0, 0, card);
        if (giveNum > MALL_MAX_BUY_NUM) {
            giveNum = MALL_MAX_BUY_NUM;
        }
        // 赠送不需要检测
//        NKErrorCode errorCode = checkCommodityBuy(commodityConf, giveNum);
//        if (errorCode != NKErrorCode.OK) {
//            errorCode.throwError("checkCommodityBuy error, commodityId:{} giveNum:{}", commodityId, giveNum);
//            return 0;
//        }
        // 七彩石配置商品下架开关
        if (isCommodityIdUrgentClose(commodityConf.getCommodityId())) {
            LOGGER.error("MallCommodityBatchGive isCommodityIdUrgentClose, UID:{} commodityId:{} buyNum:{}",
                    player.getUid(), commodityConf.getCommodityId(), giveNum);
            NKErrorCode.MallCommodityBuyCannotAccess.throwError("commodity urgent close");
        }
//        if (commodityConf.getBuyCondition().getConditionCount() > 0) {
//            PlayerConditionGroup condition = new PlayerConditionGroup(player, commodityConf.getBuyCondition(),
//                    new ConditionGroup());
//            condition.initConditionGroup(new PlayerConditionInitData(player));
//            if (!condition.checkComplete()) {
//                condition.unregisterConditionGroup();
//                NKErrorCode.MallCommodityBuyConditionNotComplete.throwError("invalid buy condition");;
//            }
//            condition.unregisterConditionGroup();
//        }

        // 检查赠送的条件
        mallGiveConditionCheck(commodityConf, friendUid);

        checkReceiverCanReceive(friendUid, commodityConf);

        if (commodityConf.getCanDirectBuy() == true && isDirectBy == false) {
            NKErrorCode.InvalidParams.throwError("direct by only");
            return 0;
        }
        ResRelation.RelationConf relationConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_GIVE_MAIL_TEMPLATE_ID);
        if (relationConf == null) {
            NKErrorCode.MallDemandMailSendFailed.throwError("invalid give template mail id");
            return 0;
        }
        if (!checkGiveLimit(commodityConf, giveNum, friendUid)) {
            NKErrorCode.MallDailyGiveLimitFailed.throwError("give limit");
        }

        if (checkGiveCommodityTotalLimit(commodityConf, giveNum, friendUid)) {
            NKErrorCode.MallGiveCommodityTotalOutLimit.throwError("give commodity total limit");
        }

        String giveMailLimitKey = CacheUtil.MallGiveMailLimit.getKey(friendUid);
        ResRelation.RelationConf giveMailLimitConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_GIVE_MAIL_LIMIT);
        // 设置对方赠送邮箱上限 // 这里改成通用方法. 贺卡邮件和赠礼邮件共用
        MailMgr.mailLimitCheck(giveMailLimitConf, giveMailLimitKey);

        // 敏感词检测需要一个预先标记id，不需要全局唯一。为减少id浪费且减少存储，目前使用毫秒时间戳作为标记，结合双方uid可以供安全侧唯一定位赠礼数据
        long giftPreAllocMs = DateUtils.currentTimeMillis();
        boolean hasCard = card.getCardType() > 0;
        if (hasCard) {
            card = checkMallGiftCard(card, commodityConf, giveNum, friendUid, giftPreAllocMs);
        }

        String businessBillNo = BillNoIdGenerator.getBusinessBillNo("mall");
        // 直购
        if (commodityConf.getCanDirectBuy() == true && isDirectBy) {
            String midasProductId = createMidasGiveProductId(commodityConf);
            if (player.getPlayerMoneyMgr().isProductIdInvalid(midasProductId)) {
                NKErrorCode.MallCommodityWithoutMidasProduct
                        .throwError("MallCommodityBatchGive invalid midas product id:{}, commodityId:{}, uid:{}",
                                midasProductId, commodityId, player.getUid());
                return 0;
            }
            NKErrorCode checkSwitchRet = player.getPlayerMoneyMgr().checkProductIdCommercialSwitch(midasProductId);
            if (checkSwitchRet.hasError()) {
                checkSwitchRet.throwError("checkProductIdCommercialSwitch fail. productId:{}, commodityId:{}, uid:{}",
                        midasProductId, commodityId, player.getUid());
                return 0;
            }
            if (giveNum != 1) {
                NKErrorCode.InvalidParams.throwError("mall direct buy:{} num!=1, commodityId:{}, uid:{}, giveNum:{}",
                        midasProductId, commodityId, player.getUid(), giveNum);
                return 0;
            }
            var metadata = DeliverGoodsMetaData.newBuilder().setBusBillNo(businessBillNo).setGiveFriendUid(friendUid)
                    .setTimes(giftPreAllocMs).setMallGiftCard(card);
            player.getRechargeMgr().directBuy(MidasModuleType.MMT_MALL_GIVE, midasProductId, metadata,
                    ItemChangeReason.ICR_MallCommodityBuy);
            return 1;
        }
        int finalUnitPrice = commodityConf.getGiftPrice();
        // 一级货币购买
        if (commodityConf.getGiftCoinType() == CoinType.CT_Diamond_VALUE) {
            String midasProductId = createMidasGiveProductId(commodityConf);
            if (player.getPlayerMoneyMgr().isProductIdInvalid(midasProductId)) {
                NKErrorCode.MallCommodityWithoutMidasProduct
                        .throwError("MallCommodityBatchGive invalid midas product id:{}, commodityId:{}, uid:{}",
                                midasProductId, commodityId, player.getUid());
                return 0;
            }
            NKErrorCode checkSwitchRet = player.getPlayerMoneyMgr().checkProductIdCommercialSwitch(midasProductId);
            if (checkSwitchRet.hasError()) {
                checkSwitchRet.throwError("checkProductIdCommercialSwitch fail. productId:{}, commodityId:{}, uid:{}",
                        midasProductId, commodityId, player.getUid());
                return 0;
            }
            try {
                HashMap<String, Integer> productMap = new HashMap<>();
                productMap.put(midasProductId, giveNum);
                var metadata = DeliverGoodsMetaData.newBuilder().setCostDiamonds((long) finalUnitPrice * giveNum)
                        .setBusBillNo(businessBillNo).setGiveFriendUid(friendUid).setTimes(giftPreAllocMs).setMallGiftCard(card);
                player.getPlayerMoneyMgr().midasDiamondPay(productMap, metadata, ItemChangeReason.ICR_MallCommodityBuy);
            } catch (Exception e) {
                LOGGER.error("midas pay failed, uid:{}, midasProductId:{}, giveNum:{}",
                        player.getUid(), midasProductId, giveNum);
            }

            return 2;
        }

        ChangedItems costItems = new ChangedItems(ItemChangeReason.ICR_MallCommodityBuy.getNumber(),
                String.format("%d:%d", commodityId, giveNum));
        costItems.setBusBillNo(businessBillNo);

        if (clientUnitPrice != 0 && finalUnitPrice != clientUnitPrice) {
            NKErrorCode.MallUnitPriceMismatch
                    .throwError("unit price mismatch, refresh commodity, commodityId:{}, sPrice{}, cPrice:{}",
                            commodityId, finalUnitPrice, clientUnitPrice);
            return 0;
        }
        costItems.mergeItemInfo(commodityConf.getGiftCoinType(), finalUnitPrice * giveNum);
        if (!player.getBagManager().isItemsEnough(costItems)) {
            NKErrorCode.MallCommodityBuyCheckCostNotEnough.throwError(
                    "MallCommodityBuy CheckItemsEnough error, UID:{} commodityId:{} buyNum:{} costItems:{}",
                    player.getUid(), commodityId, giveNum, costItems.toString());
            return 0;
        }

        // 检查是否足够扣款
        if (!player.getBagManager().isItemsEnough(costItems)) {
            NKErrorCode.ChangeNewCostItemNotEnough
                    .throwError("mall give lack of items, uid:{} costItems:{}",
                            player.getUid(), costItems.toString());
        }

        // 设置对方赠送邮箱上限 // 这里改成通用方法. 贺卡邮件和赠礼邮件共用
        MailMgr.mailLimitSet(giveMailLimitConf, giveMailLimitKey);

        // 扣款
        player.getBagManager().MinItems(costItems, commodityConf.getMallId())
                .throwErrorIfNotOk("MallCommodityBuy MinItems error,UID:{} commodityId:{} buyNum:{} costItems:{}",
                        player.getUid(), commodityId, giveNum, costItems);

        List<com.tencent.wea.attr.RewardItemInfo> rewardItemList = mallCommodity2RewardItemList(commodityConf, giveNum);
        // 邮件发送道具
        MailAttachmentList.Builder attachment = MailAttachmentList.newBuilder();
        for (com.tencent.wea.attr.RewardItemInfo itemInfo : rewardItemList) {
            var mailItem = ItemInfo.newBuilder().setItemId((int) itemInfo.getId()).setItemNum(itemInfo.getNum());
            if (itemInfo.getExpireTimeMs() > 0) {
                mailItem.setExpireTimeMs(itemInfo.getExpireTimeMs());
                mailItem.setExpireType(itemInfo.getExpireType());
            }
            attachment.addList(MailAttachment.newBuilder().setItemIfo(mailItem).build());
        }

        List<String> contentFmtArgs = new ArrayList<>();
        contentFmtArgs.add(player.getName());

        MailExtraParam extraParam = makeMailExtraParam(card);
        long mailId = MailInteraction.sendTemplateMail(friendUid, relationConf.getValue(), attachment,
                player.getUid(), extraParam, MailInteraction.TlogSendReason.mallGive, null, contentFmtArgs, giftPreAllocMs);
        if (mailId < 0) {
            LOGGER.error("MallCommodityBatchGive send template mail failed, uid:{}, template id:{}",
                    player.getUid(), relationConf.getValue());
            NKErrorCode.MallGiveMailSendFailed.throwError("send give mail failed");
        }

        // Redis记录收件人尚未收取赠礼信息
        CommodityInfo commodityInfo = new CommodityInfo().setId(commodityId).setNum(giveNum);
        ReceiveGiftCache.addMailGift(friendUid, mailId, rewardItemList, commodityInfo);

        // 增加亲密度
        try {
            player.getFriendManager().increaseFriendIntimacy(friendUid, commodityConf.getAddIntimacy() * giveNum,
                    DateUtils.currentTimeMillis(), FriendIntimacyChangeReason.RUR_MALL_GIVE);
        } catch (Exception e) {
            LOGGER.error("increaseFriendIntimacy fail, {},{},{}", player.getUid(), friendUid,
                    commodityConf.getAddIntimacy() * giveNum);
        }

        // 增加赠送记录
        List<CommodityInfo> commodityInfoList = Collections.singletonList(commodityInfo);
        proto_MallGiftCard cardRecord = makeCardRecord(card);
        int giveId = addMallGiveRecord(friendUid, rewardItemList, commodityInfoList, mailId, giftPreAllocMs, cardRecord);

        sendMallGiveDeliverGoodsNotify(friendUid, commodityId, giveNum);

        new PlayerSendGiftEvent(player).dispatch();

        // 生日送礼
        player.getBirthdayMgr().birthdayGiftSend(friendUid, card, commodityInfoList, giftPreAllocMs);

        // tlog
        TlogFlowMgr.sendGiftSendFlow(player, friendUid, commodityConf.getMallId(), commodityId, giveNum,
                commodityConf.getGiftCoinType(), commodityConf.getGiftPrice(), commodityConf.getAddIntimacy(),
                (int) intimacyRelation, giveSource, TlogMacros.GIFTSENDSTATUS.GIFT_SEND_SUCC, "", businessBillNo,
                mailId, giveId, giftPreAllocMs, card);
        sendSecTlog(card, friendUid, giftPreAllocMs);
        return 0;
    }

    // 外观类商品赠送时可以选择赠礼卡。注：旧客户端可能不支持
    public boolean canMallGiftUseCard(int commodityId) {
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            return false;
        }
/*
        // 判断malltype是否在给定列表里
        int[] mallIdList = {6, 7, 8, 9, 10, 11, 12, 39, 43};    // 给定商城类型
        for (int mallId : mallIdList) {
            if (commodityConf.getMallId() == mallId) {
                return true;
            }
        }
*/
        // 判断物品类型是否在给定列表里
        List<Integer> itemTypes = ClientKVConf.getInstance().getMallGiftCardItemTypes();
        for (var itemId : commodityConf.getItemIdsList()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf == null) {
                continue;
            }

            int itemType = itemConf.getType().getNumber();
            if (itemType > 0 && itemTypes.contains(itemType)) {
                return true;
            }
        }

        return false;
    }

    public boolean isMallGiftCardEnable() {
        FeatureOpenConf featureOpenConf = FeatureOpenConfData.getInstance()
                .get(FeatureOpenType.FOT_MallGiftCard, player.getClientVersion64());
        return featureOpenConf != null && featureOpenConf.getIsShow();
    }

    private MallGiftCardInfo checkMallGiftCard(MallGiftCardInfo card, MallCommodity commodityConf, int giveNum,
            long friendUid, long giftPreAllocMs) {
        if (card.getCardType() <= 0) {
            LOGGER.debug("invalid cardType:{}", card.getCardType());
            return card;
        }

        if (!isMallGiftCardEnable()) {
            LOGGER.debug("mall gift card feature off");
            card = MallGiftCardInfo.getDefaultInstance();   // 强行清空，按照无赠礼卡流程执行
            return card;
        }

        var cardConf = MallGiftCardConf.getInstance().getByCardType(card.getCardType());
        if (cardConf == null) {
            NKErrorCode.MallGiftCardParamWrong.throwError("card type:{} no conf", card.getCardType());
            return card;
        }

        if (!canMallGiftUseCard(commodityConf.getCommodityId())) {
            NKErrorCode.MallGiftCannotUseCard.throwError("give commodity {} cannot use card",
                    commodityConf.getCommodityId());
        }

        if (card.getWordsId() <= 0 && card.getWordsContent().isEmpty()) {
            NKErrorCode.MallGiftCardParamWrong.throwError("card words param empty");
        }

        if (checkMallGiftCardIsBanned(player)) {
            NKErrorCode.MallGiftCardIsBanned.throwError("player is ban use card");
        }

        if (card.getWordsId() > 0) {
            if (!cardConf.getBlessingIdsList().contains(card.getWordsId())) {
                NKErrorCode.MallGiftCardParamWrong.throwError("card type:{} no wordsId:{}",
                        card.getCardType(), card.getWordsId());
            }
        } else if (!card.getWordsContent().isEmpty()) {
            // TODO wordsId length config
            int wordsLen = card.getWordsContent().length();
            if (wordsLen > 256) {
                NKErrorCode.MallGiftCardWordsWrong.throwError("card type:{} words too long:{}",
                        card.getCardType(), wordsLen);
            }
            if (player.getPlayerMoneyMgr().metaDataStringWrong(card.getWordsContent())) {
                // 强行移除*号
                String replacedStr = card.getWordsContent().replaceAll("\\*", "");
                card = MallGiftCardInfo.newBuilder(card).setWordsContent(replacedStr).build();
            }
            // 敏感词检测
            HashMap<String, String> extraData = new HashMap<>();
            extraData.put("gift_id", String.valueOf(giftPreAllocMs));
            extraData.put("receiver_uid", String.valueOf(friendUid));
            SensitiveFilterHandle.SensitiveFilter(player, card.getWordsContent(), SUB_SCENE.MALL_GIFT_CARD_WORDS,
                    extraData);
            // 再次检测，避免错误调tss接口把敏感词替换成*了
            if (player.getPlayerMoneyMgr().metaDataStringWrong(card.getWordsContent())) {
                NKErrorCode.MallGiftCardWordsWrong.throwError("metaData contain ‘*’");
            }
        }

        return card;
    }

    private boolean checkMallGiftCardIsBanned(Player player) {
        BanStatus banStatus = player.getBanStatus(BanType.BT_MallGiftCard);
        if (banStatus != null && banStatus.getBanBefore() > DateUtils.currentTimeMillis()) {
            LOGGER.debug("player:{} is ban use card : {}", player.getUid(), banStatus);
            player.sendBanInfoNtf(BanType.BT_MallGiftCard, banStatus.getBanBefore(), NKErrorCode.MallGiftCardIsBanned,
                    banStatus.getBanReason(), true);
            return true;
        }
        return false;
    }

    private MailExtraParam makeMailExtraParam(MallGiftCardInfo card) {
        if (card.getCardType() > 0) {
            MailExtraParam extraParam = new MailExtraParam();
            extraParam.extraType = MailExtraType.MET_MALL_GIFT_CARD;
            extraParam.extraData = MailExtraData.newBuilder().setMallGiftCard(card).build();
            return extraParam;
        }
        return null;
    }

    public void ntfMallGiftCardChange(boolean needRefreshGiveRecord, List<Long> mailIds) {
        var ntf = MallGiftCardChangeNtf.newBuilder().setNeedRefreshGiveRecord(needRefreshGiveRecord);
        if (mailIds != null && !mailIds.isEmpty()) {
            ntf.addAllNeedRefreshMailIds(mailIds);
        }
        player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLGIFTCARDCHANGENTF, ntf);
    }

    // 返回值第二个参数表示是否有字段变化需要更新db
    public NKPair<NKErrorCode, Boolean> mallGiftCardReceiverMailResetWords(MallGiftCardInfo.Builder card) {
        NKPair<NKErrorCode, Boolean> defalutRet = new NKPair<>(NKErrorCode.OK, false);

        if (card.getCardType() <= 0) {
            return defalutRet;
        }
        if (card.getWordsContent().isEmpty()) {
            return defalutRet;
        }

        // 清空自定义语句，并置为默认配置
        card.setWordsContent("");
        var conf = MallGiftCardConf.getInstance().getByCardType(card.getCardType());
        if (conf == null || conf.getBlessingIdsCount() <= 0) {
            LOGGER.error("getByCardType failed, cardType:{}", card.getCardType());
            return new NKPair<>(NKErrorCode.ResNotFound, true);
        }

        card.setWordsId(conf.getBlessingIds(0));
        return new NKPair<>(NKErrorCode.OK, true);
    }

    public NKErrorCode mallGiftCardReceiverResetWords(long senderUid, long giftPreAllocMs) {
        List<Mail> mails = player.getMailManager().getMailsByPredicate(mail -> {
            // 必须在 MailDao.BRIEF_FIELDS 字段里，因为mail缓存基本都是getBrief不包含EXTRA_FIELDS
            return mail.getMailExtraType() == MailExtraType.MET_MALL_GIFT_CARD && mail.getSendTime() == giftPreAllocMs
                    && mail.getOwnerUid() == player.getUid() && mail.getSenderUid() == senderUid;
        });
        if (mails.isEmpty()) {
            LOGGER.error("getMailsByPredicate failed, uid:{}, senderUid:{}, giftId:{}",
                    player.getUid(), senderUid, giftPreAllocMs);
            return NKErrorCode.MallGiftCardCannotFind;
        }

        List<Long> chgdMailIds = new ArrayList<>();
        for (Mail mailBrief : mails) {
            Mail mail = player.getMailManager().getCompleteMail(mailBrief.getMailId());
            if (mail == null) {
                LOGGER.error("getCompleteMail failed, uid:{}, senderUid:{}, giftId:{}",
                        player.getUid(), senderUid, giftPreAllocMs);
                continue;
            }

            var card = mail.getExtraDataBuilder().getMallGiftCardBuilder();
            var ret = mallGiftCardReceiverMailResetWords(card);
            if (ret.getKey().hasError()) {
                LOGGER.error("mallGiftCardReceiverMailResetWords failed, ret:{}, uid:{}, senderUid:{}, giftId:{}",
                        ret, player.getUid(), senderUid, giftPreAllocMs);
            }
            if (ret.getValue()) {
                TcaplusErrorCode err = MailDao.updateExtraData(mail);
                if (!err.noError()) {
                    LOGGER.error("updateExtraData failed, ret:{}, uid:{}, senderUid:{}, giftId:{}",
                            ret, player.getUid(), senderUid, giftPreAllocMs);
                } else {
                    chgdMailIds.add(mail.getMailId());
                }
            }
        }

        ntfMallGiftCardChange(false, chgdMailIds);
        return NKErrorCode.OK;
    }

    private proto_MallGiftCard makeCardRecord(MallGiftCardInfo card) {
        if (card.getCardType() <= 0) {
            return null;
        }

        return proto_MallGiftCard.newBuilder().setCardType(card.getCardType()).setWordsId(card.getWordsId())
                .setWordsContent(card.getWordsContent()).build();
    }

    private void sendSecTlog(MallGiftCardInfo card, long friendUid, long giftPreAllocMs) {
        if (card.getCardType() <= 0) {
            return;
        }

        if (card.getWordsId() <= 0 && !card.getWordsContent().isEmpty()) {
            // 安全侧敏感词检测用
            TlogFlowMgr.sendSecSNSFlowForMallGiftCard(player, friendUid, giftPreAllocMs, card.getWordsContent());
        }
    }

    public static @NotNull Map<Integer, Long> getItemsCannotRepeat(MallCommodity commodityConf) {
        Map<Integer, Long> checkItems = new HashMap<>();
        for (int i = 0; i < commodityConf.getItemIdsCount() && i < commodityConf.getItemNumsCount(); i++) {
            int itemId = commodityConf.getItemIds(i);
            int itemNum = commodityConf.getItemNums(i);
            if (itemNum <= 0) {
                continue;
            }

            Item_BackpackItem backpackItem = BackpackItem.getInstance().get(itemId);
            if (backpackItem == null) {
                continue;
            }
            // 当前需求：只检查限制为1的道具赠送
            if (backpackItem.getMaxNum() == 1 || commodityConf.getCumuRecvNumMax() == 1) {
                checkItems.put(itemId, backpackItem.getMaxNum());
            }
        }
        return checkItems;
    }

    public static @NotNull Map<Integer, Long> getItemsCannotRepeat(int commodityId) {
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            return new HashMap<>();
        }
        return getItemsCannotRepeat(commodityConf);
    }

    private NKErrorCode checkItemsExistBeforeBuy(MallCommodity commodityConf) {
        Map<Integer, Long> checkItems = getItemsCannotRepeat(commodityConf);
        if (checkItems.isEmpty()) {
            return NKErrorCode.OK;
        }

        if (ReceiveGiftCache.hasGifts(player.getUid(), checkItems.keySet())) {
            LOGGER.debug("uid {} commodity {} hasGifts in ReceiveGiftCache",
                    player.getUid(), commodityConf.getCommodityId());
            return NKErrorCode.MallGiftSelfAlreadyHave;
        }
        return NKErrorCode.OK;
    }

    private void checkReceiverCanReceive(long friendUid, MallCommodity commodityConf) {
        Map<Integer, Long> checkItems = getItemsCannotRepeat(commodityConf);
        if (checkItems.isEmpty()) {
            return;
        }
        if (ReceiveGiftCache.hasGifts(friendUid, checkItems.keySet())) {
            NKErrorCode.MallGiftTargetAlreadyHave.throwError(
                    "uid {} friend {} commodity {} hasGifts", player.getUid(), friendUid, commodityConf.getCommodityId());
            return;
        }

        boolean mallGiftCheckOnline = PropertyFileReader.getBooleanItem("mall_gift_check_online", true);
        if (mallGiftCheckOnline) {
            SsGamesvr.RpcGetPlayerHasItemReq.Builder rpcReq = SsGamesvr.RpcGetPlayerHasItemReq.newBuilder()
                    .setUid(friendUid).addAllItemIds(checkItems.keySet())
                    .setCommodityId(commodityConf.getCommodityId());
            RpcResult<SsGamesvr.RpcGetPlayerHasItemRes.Builder> rpcResult = null;
            boolean handleOnline = false;
            try {
                GameService gameService = GameService.get();
                rpcResult = gameService.rpcGetPlayerHasItem(rpcReq);
                handleOnline = true;
            } catch (NKRuntimeException re) {
                LOGGER.debug("uid {} friend {} send commodity runtimeException {}", player.getUid(), friendUid, re);
            } catch (Exception e) {
                LOGGER.error("uid {} friend {} commodity {} rpcGetPlayerHasItem exception", player.getUid(), friendUid, commodityConf.getCommodityId(), e);
            }
            if (handleOnline && rpcResult != null && rpcResult.getRet() == NKErrorCode.OK.getValue() &&
                    rpcResult.getData() != null) {
                if (rpcResult.getData().getItemIdsCount() != 0) {
                    NKErrorCode code = NKErrorCode.MallGiftTargetAlreadyHave;
                    code.throwError("uid {} friend {} commodity {} hasItem {}",
                            player.getUid(), friendUid, commodityConf.getCommodityId(), rpcResult.getData().getItemIdsList()
                    );
                }
                return;
            }
        }

        // 处理离线情况
        TcaplusDb.Player dbPlayer = PlayerTableDao.getTcaplusPlayer(friendUid);
        if (dbPlayer == null) {
            LOGGER.error("getTcaplusPlayer {} failed", friendUid);
            NKErrorCode.UserSimpleNotFound.throwError("uid {} not found", friendUid);
            return;
        }

        Set<Integer> hasItemIds = new HashSet<>();
        for (AttrItem.proto_Item item: dbPlayer.getUserAttr().getItemInfo().getItemList()) {
            if (checkItems.containsKey(item.getItemId())) {
                hasItemIds.add(item.getItemId());
            }
        }
        for (AttrItem.proto_Item item: dbPlayer.getChangedUserAttr().getItemInfo().getItemList()) {
            if (checkItems.containsKey(item.getItemId())) {
                hasItemIds.add(item.getItemId());
            }
        }
        if (!hasItemIds.isEmpty()) {
            NKErrorCode.MallGiftTargetAlreadyHave.throwError(
                    "uid {} friend {} commodity {} hasItem {}",
                    player.getUid(), friendUid, commodityConf.getCommodityId(), hasItemIds
            );
        }

        // 判断特殊商品的受赠记录
        if (commodityConf.getCumuRecvNumMax() == 1) {
            boolean hasRecvCommodity = false;
            if (dbPlayer.getUserAttr().getMallInfo().getRecvLimitRecordsList().stream()
                    .anyMatch(x -> x.getId() == commodityConf.getCommodityId())) {
                hasRecvCommodity = true;
            }
            if (dbPlayer.getChangedUserAttr().getMallInfo().getRecvLimitRecordsList().stream()
                    .anyMatch(x -> x.getId() == commodityConf.getCommodityId())) {
                hasRecvCommodity = true;
            }
            if (hasRecvCommodity) {
                NKErrorCode.MallGiftTargetEverRecv.throwError("uid {} friend {} commodity {} ever recv",
                        player.getUid(), friendUid, commodityConf.getCommodityId());
            }
        }
    }

    public boolean hasRecvLimitRecord(int commodityId) {
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            return false;
        }
        if (commodityConf.getCumuRecvNumMax() != 1) {
            return false;
        }

        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        return mallInfo.getRecvLimitRecords().containsKey(commodityId);
    }

    // 仅特殊限定的商品才记录，否则数据会过多膨胀
    public void setRecvLimitRecord(int commodityId) {
        MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
        if (commodityConf == null) {
            return;
        }
        if (commodityConf.getCumuRecvNumMax() != 1) {
            return;
        }

        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        if (!mallInfo.getRecvLimitRecords().containsKey(commodityId)) {
            mallInfo.getRecvLimitRecords().put(commodityId, new CommodityInfo().setId(commodityId).setNum(1));
        }
    }

    // onRecv:标记当前场景是否领取赠礼
    public void delGiftCache(List<Long> mailIds, boolean onRecv) {
        List<ReceivedGift> delInfos = ReceiveGiftCache.delMailGifts(player.getUid(), mailIds);

        for (ReceivedGift gift : delInfos) {
            if (onRecv) {
                int commodityId = gift.getCommodity().getId();
                if (commodityId > 0) {
                    setRecvLimitRecord(commodityId);
                }
            }
        }
    }

    private void sendRpcMallDemandSuccessNotify(long friendUid, int commodityId, int giveCount, long mailId) {
        SsGamesvr.RpcMallDemandSuccessNtfReq.Builder req = SsGamesvr.RpcMallDemandSuccessNtfReq.newBuilder()
                .setPlayerUid(friendUid)
                .setFriendUId(player.getUid())
                .setCommodityId(commodityId)
                .setGiveCount(giveCount)
                .setMailId(mailId);
        try {
            GameService.get().rpcMallDemandSuccessNtf(req);
        } catch (RpcException e) {
            LOGGER.error("rpcMallDemandSuccessNtf error, {}, {}", friendUid, commodityId);
        }
    }

    public void sendMallDemandSuccessNotify(long friendUid, int commodityId, int giveCount, long mailId) {
        CsMall.MallDemandDeliverGoodsNtf.Builder notify = CsMall.MallDemandDeliverGoodsNtf.newBuilder()
                .setFriendUid(friendUid)
                .setCommodityId(commodityId)
                .setGiveCount(giveCount)
                .setMailId(mailId);

        player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLDEMANDDELIVERGOODSNTF, notify);
    }

    private void sendMallGiveDeliverGoodsNotify(long friendUid, int commodityId, int giveCount) {
        CsMall.MallGiveDeliverGoodsNtf.Builder notify = CsMall.MallGiveDeliverGoodsNtf.newBuilder()
                .setFriendUid(friendUid)
                .setCommodityId(commodityId)
                .setGiveCount(giveCount);

        player.sendNtfMsg(MsgTypes.MSG_TYPE_MALLGIVEDELIVERGOODSNTF, notify);
    }

    /**
     * 通过商城赠送道具检查
     * @param commodityConf     商品配置
     * @param targetPlayerId    目标玩家UID
     */
    private void mallGiveConditionCheck(MallCommodity commodityConf, long targetPlayerId) {
        if (!commodityConf.hasGiftChecker() || commodityConf.getGiftChecker().getCheckType() == MallGiftCheckType.MGCT_None) {
            return;
        }

        if (commodityConf.getGiftChecker().getCheckType() == MallGiftCheckType.MGCT_SelfWolfKillTreasureLevelType){
            if (player.getUserAttr().getWolfKillInfo().getLv()<NR3E3Treasure.getInstance().getInteractEmojLevel()){
                NKErrorCode.MallGiftGetCheckerError.throwError("get gift mall item {} check type {} checker error",
                        commodityConf.getCommodityId(), commodityConf.getGiftChecker().getCheckType());
            }
            return;
        }

        ResMall.MallCommodityGiftChecker giftCheckConf = commodityConf.getGiftChecker();
        BaseMallGiftChecker checker = MallGiftCheckerFactory.newMallGiftChecker(giftCheckConf.getCheckType(), player, giftCheckConf.getCheckParamList());
        if (null == checker) {
            NKErrorCode.MallGiftGetCheckerError.throwError("get gift mall item {} check type {} checker error",
                    commodityConf.getCommodityId(), giftCheckConf.getCheckType());
        }

        Map<Long, PlayerPublic> playerPublicMap = PlayerPublicDao.batchGetPlayerPublicMap(Collections.singletonList(targetPlayerId),
                PlayerPublicAttrKey.PublicProfile, PlayerPublicAttrKey.PublicEquipments, PlayerPublicAttrKey.PublicGameData,
                PlayerPublicAttrKey.PublicGameSettings, PlayerPublicAttrKey.PublicBasicInfo);
        if (null == playerPublicMap || playerPublicMap.isEmpty()) {
            NKErrorCode.MallGiftGetPlayerPublicInfoError.throwError("get gift mall item {} target {} public info failed",
                    commodityConf.getCommodityId(), targetPlayerId);
        }

        checker.checkSatisfy(commodityConf, playerPublicMap.get(targetPlayerId));
    }

    private List<com.tencent.wea.attr.RewardItemInfo> mallCommodity2RewardItemList(
            MallCommodity commodityConf, int buyNum) {
        List<com.tencent.wea.attr.RewardItemInfo> rewardItemList = new ArrayList<>();
        for (int i = 0; i < commodityConf.getItemIdsCount(); i++) {
            if (commodityConf.getItemNumsCount() <= i) {
                continue;
            }
            int itemNum = commodityConf.getItemNums(i);
            var reward = new com.tencent.wea.attr.RewardItemInfo()
                    .setId(commodityConf.getItemIds(i)).setNum(itemNum * buyNum);
            if (commodityConf.getExpireDaysCount() > 0 && commodityConf.getExpireDays(i) > 0) {
                reward.setExpireTimeMs(commodityConf.getExpireDays(i) * DateUtils.ONE_DAY_MILLIS);
                reward.setExpireType(ItemExpireType.IET_RELATIVE_VALUE);
            } else if (commodityConf.getExpireTimestampsCount() > 0
                    && commodityConf.getExpireTimestamps(i).getSeconds() > 0) {
                reward.setExpireTimeMs(commodityConf.getExpireTimestamps(i).getSeconds() * 1000);
                reward.setExpireType(ItemExpireType.IET_ABSOLUTE_VALUE);
            }
            rewardItemList.add(reward);
        }
        return rewardItemList;
    }

    public NKErrorCode MidasDeliverGiveMallGoods(Collection<NKPair<Integer, Integer>> confsFromMidas,
            String billNo, Long friendUid, long giftPreAllocMs, MallGiftCardInfo card) {
        Map<ResMall.MallCommodity, Integer> deliverConfs = new HashMap<>();
        for (NKPair<Integer, Integer> deliverConf : confsFromMidas) {
            ResMall.MallCommodity conf = MallCommodityConf.getInstance().get(deliverConf.getKey());
            if (conf == null) {
                LOGGER.error("MidasDeliverMallGoods mall conf null, uid:{}, comId:{}, num:{}",
                        player.getUid(), deliverConf.getKey(), deliverConf.getValue());
                continue;
            }
            deliverConfs.put(conf, deliverConf.getValue());
        }
        return MallDeliverGiveGoods(deliverConfs, billNo, friendUid, giftPreAllocMs, card);
    }

    private NKErrorCode MallDeliverGiveGoods(Map<MallCommodity, Integer> buyConfs, String billNo,
            Long friendUid, long giftPreAllocMs, MallGiftCardInfo card) {
        MallCommodity conf = null;
        Integer buyNum = 0;
        long intimacy = 0L;
        int giveSource = TlogMacros.GIFTSENDSOURCE.GIFT_SEND_FROM_SELF;
        List<com.tencent.wea.attr.RewardItemInfo> totalRewardList = new ArrayList<>();
        Map<Integer, Integer> commodityInfo = new HashMap<>();
        for (Map.Entry<ResMall.MallCommodity, Integer> buyConf : buyConfs.entrySet()) {
            conf = buyConf.getKey();
            buyNum = buyConf.getValue();
            List<com.tencent.wea.attr.RewardItemInfo> rewardItemList = mallCommodity2RewardItemList(conf, buyNum);
            totalRewardList.addAll(rewardItemList);
            intimacy += conf.getAddIntimacy() * buyNum;
            Integer commodityNum = commodityInfo.get(commodityInfo.get(conf.getCommodityId()));
            if (commodityNum == null) {
                commodityNum = 0;
            }
            commodityInfo.put(conf.getCommodityId(), commodityNum + buyNum);
        }
        List<CommodityInfo> commodityList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> elem : commodityInfo.entrySet()) {
            commodityList.add(new CommodityInfo().setId(elem.getKey()).setNum(elem.getValue()));
        }
        ResRelation.RelationConf relationConf = RelationConfData.getInstance()
                .get(RelationConfEnum.RCE_MALL_GIVE_MAIL_TEMPLATE_ID);
        if (relationConf == null) {
            LOGGER.error("invalid cfg");
            return NKErrorCode.MallDemandMailSendFailed;
        }

        try {
            // 设置对方赠送邮箱上限
            CoRedisCmd<String, String> cmdForString = Cache.getCoRedisCmdForString();
            String giveMailLimitKey = CacheUtil.MallGiveMailLimit.getKey(friendUid);
            cmdForString.incr(giveMailLimitKey);
        } catch (Exception ex) {
            LOGGER.error("mall give redis info op fail", ex);
            NKErrorCode.MallGiveMailSendFailed.throwError("give mail num reach limit");
        } finally {
            long intimacyRelation = player.getFriendManager().getFriendIntimacy(friendUid);
            // 邮件发送道具
            MailAttachmentList.Builder attachment = MailAttachmentList.newBuilder();
            for (com.tencent.wea.attr.RewardItemInfo itemInfo : totalRewardList) {
                var mailItem = ItemInfo.newBuilder().setItemId((int) itemInfo.getId()).setItemNum(itemInfo.getNum());
                if (itemInfo.getExpireTimeMs() > 0) {
                    mailItem.setExpireTimeMs(itemInfo.getExpireTimeMs());
                    mailItem.setExpireType(itemInfo.getExpireType());
                }
                attachment.addList(MailAttachment.newBuilder().setItemIfo(mailItem).build());
            }

            MailExtraParam extraParam = makeMailExtraParam(card);
            long mailId = MailInteraction.sendTemplateMail(friendUid, relationConf.getValue(), attachment,
                    player.getUid(), extraParam, MailInteraction.TlogSendReason.mallGive, null, null, giftPreAllocMs);
            if (mailId < 0) {
                LOGGER.error("MallDeliverGiveGoods send template mail failed, uid:{}, template id:{}",
                        player.getUid(), relationConf.getValue());
                // tlog
                TlogFlowMgr.sendGiftSendFlow(player, friendUid, conf.getMallId(), conf.getCommodityId(), buyNum,
                        conf.getGiftCoinType(), conf.getGiftPrice(), (int) intimacy, (int) intimacyRelation, giveSource,
                        TlogMacros.GIFTSENDSTATUS.GIFT_SEND_FAIL, "" + NKErrorCode.MallGiveMailSendFailed.getValue(),
                        billNo, 0L, 0, 0, card);
                NKErrorCode.MallGiveMailSendFailed.throwError("send give mail failed");
            }

            // Redis记录收件人尚未收取赠礼信息
            CommodityInfo commodity = !commodityList.isEmpty() ? commodityList.get(0) : new CommodityInfo();
            ReceiveGiftCache.addMailGift(friendUid, mailId, totalRewardList, commodity);

            // 增加亲密度
            try {
                player.getFriendManager().increaseFriendIntimacy(friendUid, intimacy,
                        DateUtils.currentTimeMillis(), FriendIntimacyChangeReason.RUR_MALL_GIVE);
            } catch (Exception e) {
                LOGGER.error("increaseFriendIntimacy fail, {},{},{}", player.getUid(), friendUid, intimacy);
            }

            // 增加赠送记录
            proto_MallGiftCard cardRecord = makeCardRecord(card);
            int giveId = addMallGiveRecord(friendUid, totalRewardList, commodityList, mailId, giftPreAllocMs, cardRecord);

            if (conf.getOnlyIosMiniGameCanDemand()) {
                sendRpcMallDemandSuccessNotify(friendUid, conf.getCommodityId(), buyNum, mailId);
            }
            sendMallGiveDeliverGoodsNotify(friendUid, conf.getCommodityId(), buyNum);

            player.getMallWishListMgr().onCommodityOwnChange(List.of(conf.getCommodityId()));
            new PlayerSendGiftEvent(player).dispatch();
            // 生日送礼
            player.getBirthdayMgr().birthdayGiftSend(friendUid, card, commodityList, giftPreAllocMs);

            // tlog
            TlogFlowMgr.sendGiftSendFlow(player, friendUid, conf.getMallId(), conf.getCommodityId(), buyNum,
                    conf.getGiftCoinType(), conf.getGiftPrice(), (int) intimacy, (int) intimacyRelation,
                    giveSource, TlogMacros.GIFTSENDSTATUS.GIFT_SEND_SUCC, "", billNo, mailId, giveId, giftPreAllocMs, card);
            sendSecTlog(card, friendUid, giftPreAllocMs);
        }
        return NKErrorCode.OK;
    }

    private String createMidasGiveProductId(MallCommodity conf) {
        String ret = MidasModuleType.MMT_MALL_GIVE_VALUE + "_" + conf.getCommodityId();
        if (conf.hasMidasDiscountConf() && conf.getMidasDiscountConf().hasCondition() &&
                midasCheckers.containsKey(conf.getMidasDiscountConf().getCondition())) {
            boolean canDiscount = midasCheckers.get(conf.getMidasDiscountConf().getCondition())
                    .canDiscount(player, conf.getMidasDiscountConf().getCondition(),
                            conf.getMidasDiscountConf().getParamsList().toArray(new String[0]));
            if (canDiscount) {
                return ret + "_" + conf.getMidasDiscountConf().getProductIdSuffix();
            }
        }
        return ret;
    }

    public List<CsMall.SceneLevelPackageInfo> getLevelScenePackageInfo(int id) {
        SceneGiftPackageConfig scenePackageConf = SceneGiftPackageConf.getInstance().get(id);
        if (scenePackageConf == null) {
            NKErrorCode.InvalidParams.throwError("id can not find config");
            return null;
        }
        if (scenePackageConf.getType() != SceneGiftPackageType.SGPT_LevelBuy) {
            NKErrorCode.InvalidParams.throwError("id is not SGPT_LevelBuy config");
            return null;
        }
        if (!ScenePackageMgr.checkScenePackagePushedAndNoExpired(player,scenePackageConf)) {
            NKErrorCode.InvalidParams.throwError("id is not on sale");
            return null;
        }
        ScenePackageInfo scenePackageInfo = player.getUserAttr().getMallInfo().getScenePackageInfo(scenePackageConf.getId());
        int currentBuyIndex = 0;
        if (scenePackageInfo != null) {
            currentBuyIndex = scenePackageInfo.getBuyedCommodityId();
        }
        List<CsMall.SceneLevelPackageInfo> sceneLevelPackageInfoList = new ArrayList<>();
        for (int index = 0; index < scenePackageConf.getCommodityIdsCount(); index++) {
            int commodityId = scenePackageConf.getCommodityIds(index);
            CsMall.SceneLevelPackageInfo.Builder sceneLevelPackageInfo = CsMall.SceneLevelPackageInfo.newBuilder();
            sceneLevelPackageInfo.setCommodityId(commodityId);
            sceneLevelPackageInfo.setStatus(BuyStatus.BS_Default);
            if (currentBuyIndex > commodityId) {
                sceneLevelPackageInfo.setStatus(BuyStatus.BS_AlreadyBuy);
            } else {
                MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
                if (commodityConf.getShowCondition().getConditionCount() > 0) {
                    PlayerConditionGroup condition = new PlayerConditionGroup(player, commodityConf.getShowCondition(),
                            new ConditionGroup());
                    condition.initConditionGroup(new PlayerConditionInitData(player));
                    if (!condition.checkComplete()) {
                        sceneLevelPackageInfo.setStatus(BuyStatus.BS_CanNotBuy);
                    } else {
                        sceneLevelPackageInfo.setStatus(BuyStatus.BS_CanBuy);
                    }
                    condition.unregisterConditionGroup();
                }
            }
            sceneLevelPackageInfoList.add(sceneLevelPackageInfo.build());
        }
        return sceneLevelPackageInfoList;
    }

    /**
     * 玩家注册时, 本模块内部的注册相关逻辑
     * cd
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void prepareRegister() throws NKCheckedException {

    }

    /**
     * 玩家注册时, 本模块跨模块的注册相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void onRegister() throws NKCheckedException {

    }

    /**
     * 玩家注册后, 本模块后续的注册相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     */
    @Override
    public void afterRegister() throws NKCheckedException {

    }

    /**
     * 玩家加载时, 本模块内部的加载相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void prepareLoad() throws NKCheckedException {

    }

    /**
     * 玩家加载时, 本模块跨模块的加载相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void onLoad() throws NKCheckedException {

    }

    /**
     * 玩家加载后, 本模块后续的加载相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     */
    @Override
    public void afterLoad() {
        MallInfo mallInfo = player.getUserAttr().getMallInfo();
        mallGiveIdList = new ArrayList<>(mallInfo.getMallGiveRecord().keySet());
        Collections.sort(mallGiveIdList);
    }

    /**
     * 玩家登录时, 本模块内部的登录相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void prepareLogin() throws NKCheckedException {
        RecommendMgr.getInstance().busiRecommendReq(player.getOpenId(),player.getUid());
    }

    /**
     * 玩家登录时, 本模块跨模块的登录相关逻辑
     *
     * @throws NKCheckedException 抛出异常将终止调用其他模块的该接口
     */
    @Override
    public void onLogin() throws NKCheckedException {
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLogin(boolean todayFirstLogin) {
//        refreshRedPoints(true);
        syncAllMallStatus(true);
        refreshMallBoughtRecord();
        ScenePackageMgr.init(player);
        LOGGER.debug("afterLogin uid:{}",player.getUid());
    }

    /**
     * 玩家登录后, 本模块后续的登录相关逻辑, 在 afterlogin 后执行
     * 抛出异常不会影响调用其他模块的该接口
     * 注意! 该过程将另启协程以异步的方式执行
     *
     * @param todayFirstLogin
     */
    @Override
    public void afterLoginFinish(boolean todayFirstLogin) {

    }

    /**
     * 玩家登出时, 本模块的登出相关逻辑
     * 抛出异常不会影响调用其他模块的该接口
     */
    @Override
    public void onLogout() {

    }

    /**
     * 凌晨刷新逻辑
     * 只要上次刷新时间小于今天开始时间，就会产生一次刷新
     * 抛出异常不会影响调用其他模块的该接口
     */
    @Override
    public void onMidNight() {
        refreshdailyRedPoints();
        syncAllMallStatus(true);
        refreshMallBoughtRecord();
        dailyClearMallDemandInfo();
        ScenePackageMgr.init(player);
        checkNewScenePackagePush();
    }

    @Override
    public void onDayFiveStart() {
        refreshRedPoints(true);
        refreshMallBoughtRecord();
    }

    @Override
    public void onEveryHourStart() {
        ScenePackageMgr.init(player);
    }

    @Override
    public void onReload() {
        ScenePackageMgr.init(player);
        ntfCommercialConfInfo();
    }

    private long getOnTimeDelayMs() {
        if (player.getUserAttr().getBasicInfo().getOnTimeDelayMs() == 0) {
            int rand = RandomGenerator.getInstance().nextInt(120000);
            player.getUserAttr().getBasicInfo().setOnTimeDelayMs(rand);
            return rand;
        }
        return player.getUserAttr().getBasicInfo().getOnTimeDelayMs();
    }

    /**
     * 每周刷新逻辑
     * 只要上次刷新时间本周开始时间，就会产生一次刷新
     * 抛出异常不会影响调用其他模块的该接口
     */
    @Override
    public void onWeekStart() {

    }

    // 七彩石快速修改商品投放配置字段，同步信息给客户端
    public String commercialConfInfoVersion;

    public String getCommercialConfInfoVersion() {
        return commercialConfInfoVersion;
    }

    public void setCommercialConfInfoVersion(String infoVersion) {
        commercialConfInfoVersion = infoVersion;
    }

    public void ntfCommercialConfInfo() {
        if (!PropertyFileReader.getRealTimeBooleanItem("commercial_info_enable_change_ntf", true)) {
            LOGGER.debug("commercial_info_enable_change_ntf is false");
            return;
        }

        var csInfo = CommercialConfModifyCfgData.getInstance().getCsConfs();
        if (StringUtils.equals(csInfo.getInfoVersion(), commercialConfInfoVersion)) {
            LOGGER.debug("conf version:{}, my version:{} is same. uid:{}", csInfo.getInfoVersion(), commercialConfInfoVersion, player.getUid());
            return;
        }

        commercialConfInfoVersion = csInfo.getInfoVersion();
        LOGGER.debug("sendNtfMsg,version:{}. uid:{}", csInfo.getInfoVersion(), player.getUid());
        player.sendNtfMsg(MsgTypes.MSG_TYPE_QUICKCOMMERCIALCONFINFONTF, CommercialConfModifyCfgData.getInstance().getSerializedCsNtf());
    }

    @Override
    public QuickRewardType getQuickRewardType() {
        return QuickRewardType.QRT_MALL;
    }

    @Override
    public Map<Integer, List<Builder>> getQuickRewardItemList() {
        Map<Integer, List<RewardItemInfo.Builder>> confItemMap = new HashMap<>();
        List<QuickRewardConf> confList = QuickRewardConfData.getInstance().getConfListByType(getQuickRewardType());
        if (confList.isEmpty()) {
            return confItemMap;
        }
        for (QuickRewardConf conf : confList) {
            List<RewardItemInfo.Builder> rewardItemList = new ArrayList<>();
            for (int commodityId : conf.getIdListList()) {
                MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
                if (commodityConf == null) {
                    LOGGER.warn("player {} getQuickRewardItemList get commodityId:{} not exist",
                            player.getUid(), commodityId);
                    continue;
                }
                // 不是免费的不能领
                if (commodityConf.getPrice() != 0) {
                    LOGGER.warn("player {} getQuickRewardItemList get commodityId:{} not free",
                            player.getUid(), commodityId);
                    continue;
                }
                // 检查是否能购买
                NKErrorCode errorCode = checkCommodityBuy(commodityConf, 1);
                if (errorCode.hasError()) {
                    continue;
                }
                for (int i = 0; i < commodityConf.getItemIdsCount(); i++) {
                    if (commodityConf.getItemNumsCount() <= i) {
                        continue;
                    }
                    RewardItemInfo.Builder rewardItem = RewardItemInfo.newBuilder()
                            .setItemId(commodityConf.getItemIds(i))
                            .setRewardItemNum(commodityConf.getItemNums(i));
                    rewardItemList.add(rewardItem);
                }
            }
            confItemMap.put(conf.getId(), rewardItemList);
        }
        return confItemMap;
    }

    @Override
    public ItemChangeDetails receiveQuickReward() {
        List<QuickRewardConf> confList = QuickRewardConfData.getInstance().getConfListByType(getQuickRewardType());
        if (confList.isEmpty()) {
            return null;
        }
        Map<ResMall.MallCommodity, Integer> buyConfList = new HashMap<>();

        for (QuickRewardConf conf : confList) {
            List<RewardItemInfo.Builder> rewardItemList = new ArrayList<>();
            for (int commodityId : conf.getIdListList()) {
                MallCommodity commodityConf = MallCommodityConf.getInstance().get(commodityId);
                if (commodityConf == null) {
                    LOGGER.warn("player {} getQuickRewardItemList get commodityId:{} not exist",
                            player.getUid(), commodityId);
                    continue;
                }
                // 不是免费的不能领
                if (commodityConf.getPrice() != 0) {
                    LOGGER.warn("player {} getQuickRewardItemList get commodityId:{} not free",
                            player.getUid(), commodityId);
                    continue;
                }
                // 检查是否能购买
                NKErrorCode errorCode = checkCommodityBuy(commodityConf, 1);
                if (errorCode.hasError()) {
                    continue;
                }
                buyConfList.put(commodityConf, 1);
            }
        }
        NKPair<NKErrorCode, ItemChangeDetails> ret = mallDeliverGoodsWithoutNtf(buyConfList, "", ItemChangeReason.ICR_MallCommodityBuy);
        return ret.getValue();
    }

    public void getThemeCommodityList(int themeId, MallGetThemeShopCommodity_S2C_Msg.Builder rspMsg) {
        themeMallMgr.getThemeCommodityList(themeId, rspMsg);
    }

    public void drawThemeMallDiscount(int themeId, MallGetThemeShopDiscount_S2C_Msg.Builder rspMsg) {
        themeMallMgr.drawThemeMallDiscount(themeId);
    }
}
