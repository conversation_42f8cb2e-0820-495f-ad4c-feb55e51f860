package com.tencent.wea.playerservice.mall.price;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 价格计算详情
 * 包含完整的价格计算过程和结果
 */
public class PriceCalculationDetail {
    private final long originalPrice;
    private final long finalPrice;
    private final List<PriceModificationResult> modifications;
    
    /**
     * 构造价格计算详情
     * 
     * @param originalPrice 原始价格
     * @param finalPrice 最终价格
     * @param modifications 价格修改记录列表
     */
    public PriceCalculationDetail(long originalPrice, long finalPrice, 
                                 List<PriceModificationResult> modifications) {
        this.originalPrice = originalPrice;
        this.finalPrice = finalPrice;
        this.modifications = modifications != null ? 
            new ArrayList<>(modifications) : new ArrayList<>();
    }
    
    /**
     * 获取原始价格
     * @return 原始价格
     */
    public long getOriginalPrice() {
        return originalPrice;
    }
    
    /**
     * 获取最终价格
     * @return 最终价格
     */
    public long getFinalPrice() {
        return finalPrice;
    }
    
    /**
     * 获取价格修改记录列表
     * @return 不可修改的价格修改记录列表
     */
    public List<PriceModificationResult> getModifications() {
        return Collections.unmodifiableList(modifications);
    }
    
    /**
     * 检查价格是否被修改
     * @return 价格是否被修改
     */
    public boolean isPriceChanged() {
        return originalPrice != finalPrice;
    }
    
    /**
     * 获取总价格变化量
     * @return 总价格变化量
     */
    public long getTotalPriceChange() {
        return finalPrice - originalPrice;
    }
    
    /**
     * 获取价格减少量（仅在降价时有效）
     * @return 价格减少量
     */
    public long getPriceReduction() {
        return Math.max(0, originalPrice - finalPrice);
    }
    
    /**
     * 获取价格增加量（仅在涨价时有效）
     * @return 价格增加量
     */
    public long getPriceIncrease() {
        return Math.max(0, finalPrice - originalPrice);
    }
    
    /**
     * 获取总折扣百分比
     * @return 总折扣百分比（例如：0.2表示8折）
     */
    public double getTotalDiscountPercentage() {
        if (originalPrice == 0 || finalPrice >= originalPrice) {
            return 0.0;
        }
        return (double) (originalPrice - finalPrice) / originalPrice;
    }
    
    /**
     * 获取应用的修改器数量
     * @return 应用的修改器数量
     */
    public int getAppliedModifierCount() {
        return (int) modifications.stream()
                .filter(PriceModificationResult::isPriceChanged)
                .count();
    }
    
    /**
     * 获取所有应用的修改器名称
     * @return 应用的修改器名称列表
     */
    public List<String> getAppliedModifierNames() {
        List<String> names = new ArrayList<>();
        for (PriceModificationResult modification : modifications) {
            if (modification.isPriceChanged() && modification.getModifierName() != null) {
                names.add(modification.getModifierName());
            }
        }
        return names;
    }
    
    /**
     * 检查是否应用了指定的修改器
     * @param modifierName 修改器名称
     * @return 是否应用了该修改器
     */
    public boolean hasAppliedModifier(String modifierName) {
        return modifications.stream()
                .anyMatch(mod -> mod.isPriceChanged() && 
                         modifierName.equals(mod.getModifierName()));
    }
    
    /**
     * 获取指定修改器的修改结果
     * @param modifierName 修改器名称
     * @return 修改结果，如果没有找到则返回null
     */
    public PriceModificationResult getModificationByModifier(String modifierName) {
        return modifications.stream()
                .filter(mod -> modifierName.equals(mod.getModifierName()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取计算过程的详细描述
     * @return 计算过程描述
     */
    public String getCalculationSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("价格计算详情：原价 %d → 最终价 %d", originalPrice, finalPrice));
        
        if (!isPriceChanged()) {
            summary.append("（无修改）");
            return summary.toString();
        }
        
        summary.append(String.format("（变化 %+d）\n", getTotalPriceChange()));
        
        if (modifications.isEmpty()) {
            summary.append("无修改器应用");
        } else {
            summary.append("修改器应用过程：\n");
            long currentPrice = originalPrice;
            
            for (int i = 0; i < modifications.size(); i++) {
                PriceModificationResult mod = modifications.get(i);
                summary.append(String.format("  %d. %s: %d", 
                             i + 1, 
                             mod.getModifierName() != null ? mod.getModifierName() : "未知修改器",
                             currentPrice));
                
                if (mod.isPriceChanged()) {
                    summary.append(String.format(" → %d (%s)", 
                                 mod.getModifiedPrice(), mod.getReason()));
                    currentPrice = mod.getModifiedPrice();
                } else {
                    summary.append(" (无修改)");
                }
                
                if (i < modifications.size() - 1) {
                    summary.append("\n");
                }
            }
        }
        
        return summary.toString();
    }
    
    @Override
    public String toString() {
        return String.format("PriceCalculationDetail{original=%d, final=%d, changed=%s, modifiers=%d}", 
                           originalPrice, finalPrice, isPriceChanged(), getAppliedModifierCount());
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        PriceCalculationDetail that = (PriceCalculationDetail) o;
        
        if (originalPrice != that.originalPrice) return false;
        if (finalPrice != that.finalPrice) return false;
        return modifications.equals(that.modifications);
    }
    
    @Override
    public int hashCode() {
        int result = (int) (originalPrice ^ (originalPrice >>> 32));
        result = 31 * result + (int) (finalPrice ^ (finalPrice >>> 32));
        result = 31 * result + modifications.hashCode();
        return result;
    }
}
