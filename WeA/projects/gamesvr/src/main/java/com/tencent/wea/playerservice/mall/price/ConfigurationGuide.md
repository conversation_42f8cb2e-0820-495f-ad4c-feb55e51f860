# 商品价格扩展系统配置指南

## 概述

本文档介绍如何配置和使用商品价格扩展系统，特别是数量折扣功能的配置方法。

## 配置字段说明

### 基础配置字段

在 `MallCommodity` 消息中新增的字段：

```protobuf
// 自定义价格修改配置
optional ModifyPriceType modifyPriceType = 118; // 价格修改类型
optional bool enableModifyPrice = 119; // 是否启用价格修改

// 使用oneof支持不同类型的价格修改参数
oneof modifyPriceParams {
  QuantityDiscountParams quantityDiscountParams = 120; // 数量折扣参数
  VipDiscountParams vipDiscountParams = 121; // VIP折扣参数
  ActivityDiscountParams activityDiscountParams = 122; // 活动折扣参数
  // ... 其他参数类型
}
```

### 价格修改类型枚举

```protobuf
enum ModifyPriceType {
  MPT_None = 0 [(name) = "无修改"];
  MPT_QuantityDiscount = 1 [(name) = "数量折扣"];
  MPT_VipDiscount = 2 [(name) = "VIP折扣"];
  MPT_ActivityDiscount = 3 [(name) = "活动折扣"];
  MPT_LevelDiscount = 4 [(name) = "等级折扣"];
  MPT_SeasonDiscount = 5 [(name) = "赛季折扣"];
  MPT_TimeDiscount = 6 [(name) = "时间段折扣"];
  MPT_FirstBuyDiscount = 7 [(name) = "首购折扣"];
  MPT_BundleDiscount = 8 [(name) = "套装折扣"];
  MPT_Custom = 99 [(name) = "自定义逻辑"];
}
```

## 数量折扣配置详解

### 基本配置

要启用数量折扣，需要设置以下字段：

1. `enableModifyPrice = true` - 启用价格修改
2. `modifyPriceType = MPT_QuantityDiscount` - 设置为数量折扣类型
3. `quantityDiscountParams` - 配置数量折扣参数

### 数量折扣参数结构

```protobuf
message QuantityDiscountParams {
  repeated QuantityDiscountTier tiers = 1; // 数量折扣阶梯
}

message QuantityDiscountTier {
  optional int32 minQuantity = 1; // 最小购买数量
  optional int32 maxQuantity = 2; // 最大购买数量（0表示无上限）
  optional double discountRate = 3; // 折扣率（0.8表示8折）
  optional int32 fixedPrice = 4; // 固定价格（如果设置则忽略折扣率）
}
```

### 配置示例

#### 示例1：阶梯折扣

```json
{
  "commodityId": 12345,
  "price": 1000,
  "enableModifyPrice": true,
  "modifyPriceType": "MPT_QuantityDiscount",
  "quantityDiscountParams": {
    "tiers": [
      {
        "minQuantity": 2,
        "maxQuantity": 2,
        "discountRate": 0.95
      },
      {
        "minQuantity": 3,
        "maxQuantity": 5,
        "discountRate": 0.90
      },
      {
        "minQuantity": 6,
        "maxQuantity": 0,
        "discountRate": 0.85
      }
    ]
  }
}
```

效果：
- 购买1件：原价1000
- 购买2件：95折，每件950
- 购买3-5件：90折，每件900
- 购买6件以上：85折，每件850

#### 示例2：固定价格

```json
{
  "commodityId": 12346,
  "price": 1000,
  "enableModifyPrice": true,
  "modifyPriceType": "MPT_QuantityDiscount",
  "quantityDiscountParams": {
    "tiers": [
      {
        "minQuantity": 10,
        "maxQuantity": 0,
        "fixedPrice": 500
      }
    ]
  }
}
```

效果：
- 购买1-9件：原价1000
- 购买10件以上：固定价格500

#### 示例3：复合折扣

```json
{
  "commodityId": 12347,
  "price": 1000,
  "enableModifyPrice": true,
  "modifyPriceType": "MPT_QuantityDiscount",
  "quantityDiscountParams": {
    "tiers": [
      {
        "minQuantity": 5,
        "maxQuantity": 9,
        "discountRate": 0.90
      },
      {
        "minQuantity": 10,
        "maxQuantity": 19,
        "fixedPrice": 800
      },
      {
        "minQuantity": 20,
        "maxQuantity": 0,
        "fixedPrice": 600
      }
    ]
  }
}
```

效果：
- 购买1-4件：原价1000
- 购买5-9件：90折，每件900
- 购买10-19件：固定价格800
- 购买20件以上：固定价格600

## 配置注意事项

### 1. 阶梯重叠处理

当多个阶梯的数量范围重叠时，系统会选择最优的阶梯：
- 优先选择最小数量要求更高的阶梯
- 如果最小数量相同，选择折扣更大的阶梯
- 优先选择固定价格而非折扣率

### 2. 数据验证

- `discountRate` 应在 0.0 到 1.0 之间
- `minQuantity` 必须大于 0
- `maxQuantity` 为 0 表示无上限
- `fixedPrice` 优先级高于 `discountRate`

### 3. 性能考虑

- 阶梯数量不宜过多（建议不超过10个）
- 避免过于复杂的重叠配置
- 合理设置缓存策略

## 其他价格修改类型

### VIP折扣配置

```json
{
  "modifyPriceType": "MPT_VipDiscount",
  "vipDiscountParams": {
    "vipTiers": [
      {
        "minVipLevel": 1,
        "maxVipLevel": 3,
        "discountRate": 0.95
      },
      {
        "minVipLevel": 4,
        "maxVipLevel": 0,
        "discountRate": 0.90
      }
    ],
    "stackWithOther": false
  }
}
```

### 活动折扣配置

```json
{
  "modifyPriceType": "MPT_ActivityDiscount",
  "activityDiscountParams": {
    "activityId": 12345,
    "discountRate": 0.80,
    "startTime": "2024-01-01T00:00:00Z",
    "endTime": "2024-01-31T23:59:59Z",
    "requireParticipation": true
  }
}
```

## 测试建议

1. **基础功能测试**：验证各个阶梯的折扣计算
2. **边界测试**：测试边界数量的折扣应用
3. **异常测试**：测试无效配置的处理
4. **性能测试**：测试大量商品的价格计算性能
5. **集成测试**：测试与现有商城系统的集成

## 故障排查

### 常见问题

1. **折扣未生效**
   - 检查 `enableModifyPrice` 是否为 true
   - 检查 `modifyPriceType` 是否正确
   - 检查是否有对应的参数配置

2. **折扣计算错误**
   - 检查阶梯配置是否正确
   - 检查数量范围是否有重叠
   - 查看日志中的详细计算过程

3. **性能问题**
   - 检查阶梯数量是否过多
   - 考虑启用缓存
   - 优化配置结构

### 调试日志

启用调试日志可以查看详细的价格计算过程：

```
DEBUG - Quantity discount applied: uid=123456789, commodityId=12345, quantity=5, 
        originalPrice=1000, newPrice=900, tier=[3-5], discount=0.9
```
