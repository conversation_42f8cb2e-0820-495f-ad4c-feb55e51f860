package com.tencent.wea.playerservice.bag;

import com.tencent.eventbuspro.SubscribeEvent;
import com.tencent.eventcenter.EventConsumer;
import com.tencent.nk.commonframework.Framework;
import com.tencent.nk.util.NKErrorCode;
import com.tencent.nk.util.NKPair;
import com.tencent.nk.util.exception.NKRuntimeException;
import com.tencent.nk.util.guid.ItemIdGenerator;
import com.tencent.resourceloader.resclass.ActivityMianGanData;
import com.tencent.resourceloader.resclass.BackpackItem;
import com.tencent.resourceloader.resclass.MiscConf;
import com.tencent.resourceloader.resclass.SeasonConfData;
import com.tencent.resourceloader.resclass.SuitSeasonInfoData;
import com.tencent.resourceloader.resclass.SuitThemedInfoData;
import com.tencent.timiCoroutine.CurrentExecutorUtil;
import com.tencent.timiutil.monitor.Monitor;
import com.tencent.timiutil.monitor.MonitorId;
import com.tencent.timiutil.property.PropertyFileReader;
import com.tencent.timiutil.time.DateUtils;
import com.tencent.util.VersionUtil;
import com.tencent.wea.attr.AppearanceRoadGather;
import com.tencent.wea.attr.HistoryItemTypeInfo;
import com.tencent.wea.attr.HistoryItemUsedInfo;
import com.tencent.wea.attr.Item;
import com.tencent.wea.attr.ItemPackageLimit;
import com.tencent.wea.interaction.player.MailInteraction;
import com.tencent.wea.playerservice.bag.ChangedItems.ChangeItem;
import com.tencent.wea.playerservice.bag.handler.TradingCardBaseHandler;
import com.tencent.wea.playerservice.event.common.PlayerGetItemEvent;
import com.tencent.wea.playerservice.event.common.PlayerItemExpireRemoveEvent;
import com.tencent.wea.playerservice.event.common.PlayerSeasonChangeFinishEvent;
import com.tencent.wea.playerservice.event.common.PlayerUseItemEvent;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.playerservice.tlog.TlogFlowMgr;
import com.tencent.wea.protocol.CsBag;
import com.tencent.wea.protocol.CsBag.BagItemRemoveNtf;
import com.tencent.wea.protocol.MsgTypes;
import com.tencent.wea.protocol.common.ItemArray;
import com.tencent.wea.protocol.common.ItemInfo;
import com.tencent.wea.protocol.common.MailAttachment;
import com.tencent.wea.protocol.common.MailAttachmentList;
import com.tencent.wea.tlog.flow.TlogMacros.ADDORREDUCE;
import com.tencent.wea.xlsRes.ResActivityFunPara;
import com.tencent.wea.xlsRes.ResBackpackItem.Item_BackpackItem;
import com.tencent.wea.xlsRes.ResCommon;
import com.tencent.wea.xlsRes.ResSeason.SeasonConf;
import com.tencent.wea.xlsRes.keywords.DressGatherType;
import com.tencent.wea.xlsRes.keywords.EventRouterType;
import com.tencent.wea.xlsRes.keywords.ItemChangeReason;
import com.tencent.wea.xlsRes.keywords.ItemExpireType;
import com.tencent.wea.xlsRes.keywords.ItemType;
import com.tencent.wea.xlsRes.keywords.ItemUseTypeOperate;
import com.tencent.wea.xlsRes.keywords.PackageType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.tencent.wea.wolfKill.Util.isWolfKillAniItem;

/**
 * 道具管理器
 *
 * @author:blitzzhang
 * @date:2021/8/18 18:41
 */
public class ItemManager implements EventConsumer {

    private static final int ITEM_EXPIRE_REPLACE_MAIL_TEMEPLATE_ID  = 3;

    private static final Logger LOGGER = LogManager.getLogger(ItemManager.class);
    private static final int DEFAULT_GRIDS_LIMIT = 3000;
    private final HashMap<Integer, Set<Long>> itemId2UUIDs;
    private final com.tencent.wea.attr.ItemInfoDb itemInfoDb;
    private final Player player;

    public ItemManager(Player player) {
        this.player = player;
        itemInfoDb = player.getUserAttr().getItemInfo();
        itemId2UUIDs = new HashMap<>();
        initItemId2UUIDs();
        player.getEventSwitch().register(new OnSeasonChange());
        player.getEventSwitch().register(new OnGetItemGatherCheck());
    }

    public void initItemId2UUIDs() {
        itemId2UUIDs.clear();
        for (Item item : itemInfoDb.getItem().values()) {
            addItemUUIDs(item.getItemId(), item.getId());
        }
    }


    public boolean updateItemNumByUUID(long itemUUID, int updateNum, String billNo, int reason) {
        Item item = getItemByUUID(itemUUID);
        if (item == null) {
            return false;
        }

        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            return false;
        }

        long beforeNum = item.getNumber();
        if (updateNum >= 0) {
            if (item.getNumber() + updateNum > itemConf.getStackedNum()) {
                return false;
            }
            item.setNumber(item.getNumber() + updateNum);
        } else {
            if (item.getNumber() + updateNum > 0) {
                item.setNumber(item.getNumber() + updateNum);
            } else if (item.getNumber() + updateNum == 0) {
                item.setNumber(0);
                remove(item);
            } else {
                return false;
            }
        }
        var tlogData = new TlogData();
        tlogData.setBusBillNo(billNo);
        tlogData.setChangeReason(reason);
        TlogFlowMgr.sendItemFlow(player, itemConf.getType().getNumber(), itemConf.getId(), Math.abs(updateNum), item.getExpireMs(),
                beforeNum, GetItemNumByItemId(item.getItemId()), ADDORREDUCE.REDUCE, item.getLessorUid(), tlogData);
        return true;
    }

    private NKPair<NKErrorCode, NKPair<Item_BackpackItem, Item>> getUseItem(long itemUUID, long num) {
        Item item = getItemByUUID(itemUUID);

        if (item == null || num < 0 || item.getNumber() < num) {
            if (item == null) {
                LOGGER.error("UseItem params error, uid:{} itemUUID:{} num:{}",
                        player.getUid(), itemUUID, num);
                return new NKPair<>(NKErrorCode.ItemNotExist, null);
            } else {
                LOGGER.error("UseItem params error, uid:{} itemUUID:{} num:{} item:{}",
                        player.getUid(), itemUUID, num, item);
                return new NKPair<>(NKErrorCode.InvalidParams, null);
            }
        }
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            LOGGER.error("getItemConfig error , uid:{} itemUUID :{} itemId:{}",
                    player.getUid(), itemUUID, item.getItemId());
            return new NKPair<>(NKErrorCode.ResNotFound, null);
        }

        if (isItemVersionInvalid(itemConf)) {
            return new NKPair<>(NKErrorCode.ItemVerNotSupport, null);
        }

        if (isExpired(item)) {
            LOGGER.error("item is expire, uid:{} itemUUID:{} itemId:{} expireTime:{}",
                    player.getUid(), itemUUID, item.getItemId(), item.getExpireMs());
            return new NKPair<>(NKErrorCode.ItemIsExpire, null);
        }

        return new NKPair<>(NKErrorCode.OK, new NKPair<>(itemConf, item));
    }

    public NKErrorCode useItem(long itemUUID, int num, List<Long> params, int reason, long subReason) {
        NKPair<NKErrorCode, NKPair<Item_BackpackItem, Item>> getItemRet = getUseItem(itemUUID, num);

        if (getItemRet.getKey().hasError()) {
            return getItemRet.getKey();
        }

        Item_BackpackItem itemConf = getItemRet.getValue().getKey();
        Item item = getItemRet.getValue().getValue();

        ItemUseHandler handler = ItemUseHandler.getHandler(itemConf);
        if (handler == null) {
            LOGGER.error("no handler found of itemConf:{}", itemConf);
            return NKErrorCode.ItemUnknownItemType;
        }
        return handler.use(itemConf, player, item, num, params, reason, subReason);
    }

    public NKErrorCode useItem(ItemInfo itemInfo, List<Long> params, int reason, long subReason) {
        NKPair<NKErrorCode, NKPair<Item_BackpackItem, Item>> getItemRet = getUseItem(itemInfo.getUuid(), itemInfo.getItemNum());
        if (getItemRet.getKey().hasError()) {
            return getItemRet.getKey();
        }
        Item_BackpackItem itemConf = getItemRet.getValue().getKey();
        if (ItemUseTypeOperate.IUTO_AMS_YumiScore != itemConf.getUseType()) {
            return useItem(itemInfo.getUuid(), (int) itemInfo.getItemNum(), params, reason, subReason);
        }
        Item item = getItemRet.getValue().getValue();
        ItemUseHandler handler = ItemUseHandler.getHandler(itemConf);
        if (handler == null) {
            LOGGER.error("no handler found of itemConf:{}", itemConf);
            return NKErrorCode.ItemUnknownItemType;
        }
        return handler.use(itemInfo, itemConf, player, item, params, reason, subReason);
    }

    //道具使用
    public NKErrorCode useItem(long itemUUID, int num, List<Long> params) {
        NKPair<NKErrorCode, NKPair<Item_BackpackItem, Item>> getItemRet = getUseItem(itemUUID, num);

        if (getItemRet.getKey().hasError()) {
            return getItemRet.getKey();
        }

        Item_BackpackItem itemConf = getItemRet.getValue().getKey();
        Item item = getItemRet.getValue().getValue();

        ItemUseHandler handler = ItemUseHandler.getHandler(itemConf);
        if (handler == null) {
            LOGGER.error("no handler found of itemConf:{}", itemConf);
            return NKErrorCode.ItemUnknownItemType;
        }
        try {
            return handler.use(itemConf, player, item, num, params);
        } catch (NKRuntimeException e) {
            LOGGER.error("use item NKRuntimeException, uid:{} itemId:{} e:", player.getUid(), itemConf.getId(), e);
            return e.getEnumErrCode();
        } catch (NullPointerException e) {
            LOGGER.error("use item NullPointerException, uid:{} itemId:{} e:", player.getUid(), itemConf.getId(), e);
            return NKErrorCode.UnknownError;
        } catch (IllegalArgumentException e) {
            LOGGER.error("use item InvalidParams, uid:{} itemId:{} e:", player.getUid(), itemConf.getId(), e);
            return NKErrorCode.InvalidParams;
        } catch (Exception e) {
            LOGGER.error("use item exception, uid:{} itemId:{} e:", player.getUid(), itemConf.getId(), e);
        }
        return NKErrorCode.UnknownError;
    }

    // 农场道具激活
    public NKErrorCode farmActiveItem(long itemUUID, int num, List<Long> params) {
        Item item = getItemByUUID(itemUUID);
        if (item == null || num < 0 || item.getNumber() < num) {
            if (item == null) {
                LOGGER.error("ActiveItem params error, uid:{} itemUUID:{} num:{}",
                        player.getUid(), itemUUID, num);
                return NKErrorCode.ItemNotExist;
            } else {
                LOGGER.error("ActiveItem params error, uid:{} itemUUID:{} num:{} item:{}",
                        player.getUid(), itemUUID, num, item);
                return NKErrorCode.InvalidParams;
            }
        }
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            LOGGER.error("getItemConfig error , uid:{} itemUUID :{} itemId:{}",
                    player.getUid(), itemUUID, item.getItemId());
            return NKErrorCode.ResNotFound;
        }

        if (isItemVersionInvalid(itemConf)) {
            return NKErrorCode.ItemVerNotSupport;
        }

        if (isExpired(item)) {
            LOGGER.error("item is expire, uid:{} itemUUID:{} itemId:{} expireTime:{}",
                    player.getUid(), itemUUID, item.getItemId(), item.getExpireMs());
            return NKErrorCode.ItemIsExpire;
        }

        ItemUseHandler handler = ItemUseHandler.getHandler(itemConf);
        if (handler == null) {
            LOGGER.error("no handler found of itemConf:" + itemConf);
            return NKErrorCode.ItemUnknownItemType;
        }

        NKErrorCode ret = handler.farmActive(itemConf, player, item, num, params);
        return ret;
    }

    public NKErrorCode checkUseItem(long itemUUID, int itemId, int num, List<Long> params) {
        Item item = getItemByUUID(itemUUID);

        if (item == null || num < 0 || item.getNumber() < num || item.getItemId() != itemId) {
            if (item == null) {
                LOGGER.error("item not exist, uid:{} itemUUID:{} num:{}",
                        player.getUid(), itemUUID, num);
                return NKErrorCode.ItemNotExist;
            } else {
                LOGGER.error("UseItem params error, uid:{} itemUUID:{} itemId:{} num:{} item:{}",
                        player.getUid(), itemUUID, itemId, num, item);
                return NKErrorCode.InvalidParams;
            }
        }
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
        if (itemConf == null) {
            LOGGER.error("getItemConfig error , uid:{} itemUUID :{} itemId:{}",
                    player.getUid(), itemUUID, item.getItemId());
            return NKErrorCode.ResNotFound;
        }

        if (isItemVersionInvalid(itemConf)) {
            return NKErrorCode.ItemVerNotSupport;
        }

        if (isExpired(item)) {
            LOGGER.error("item is expire, uid:{} itemUUID:{} itemId:{} expireTime:{}",
                    player.getUid(), itemUUID, item.getItemId(), item.getExpireMs());
            return NKErrorCode.ItemIsExpire;
        }

        ItemUseHandler handler = ItemUseHandler.getHandler(itemConf);
        if (handler == null) {
            LOGGER.error("no handler found of itemConf:" + itemConf);
            return NKErrorCode.ItemUnknownItemType;
        }

        return NKErrorCode.OK;
    }

    public boolean isItemVersionInvalid(Item_BackpackItem itemConf) {
        if (itemConf == null) {
            return true;
        }

        if (itemConf.hasLowVer() && VersionUtil.versionLT(player.getClientVersion64(), itemConf.getLowVer())) {
            LOGGER.debug("item {} check low version fail, player ver {} , config ver {}", itemConf.getId(),
                    VersionUtil.decodeClientVersion(player.getClientVersion64()), itemConf.getLowVer());
            return true;
        }

        if (itemConf.hasHighVer() && VersionUtil.versionGT(player.getClientVersion64(), itemConf.getHighVer())) {
            LOGGER.debug("item {} check high version fail, player ver {} , config ver {}", itemConf.getId(),
                    VersionUtil.decodeClientVersion(player.getClientVersion64()), itemConf.getHighVer());
            return true;
        }

        return false;
    }


    public boolean isBagGirdsExceedLimit(ChangedItems addItems, Map<Integer, Item_BackpackItem> confMap) {
        int needGrids = 0;
        for (ChangeItem itemInfo : addItems.getChangeItems()) {
            Item_BackpackItem itemConf = confMap.get(itemInfo.getSrcItemId());
            if (itemConf.getType() == ItemType.ItemType_Currency) {
                continue;
            }
            if (itemConf.getStackedNum() > 0) {
                needGrids += itemInfo.getSrcItemNum() / itemConf.getStackedNum();
            } else {
                needGrids++;
            }
        }
        if (needGrids == 0) {
            return false;
        }
        int gridsLimit = getGridsLimit();
        if (this.itemInfoDb.getItemSize() + needGrids > getGridsWarnLimit()) {
            Monitor.getInstance().add.total(MonitorId.attr_player_bag_grids_limit_warn,1);
        }
        return this.itemInfoDb.getItemSize() + needGrids > gridsLimit;
    }

    private static int getGridsLimit() {
        int gridsLimit = MiscConf.getInstance().getMiscConf().getPlayerBackpackGridLimit();
        if (gridsLimit < DEFAULT_GRIDS_LIMIT) {
            gridsLimit = DEFAULT_GRIDS_LIMIT;
        }
        return gridsLimit;
    }

    private static int getGridsWarnLimit() {
        return PropertyFileReader.getRealTimeIntItem("bag_grids_warning_limit", 2500);
    }

    public int getAvailableGrids() {
        return getGridsLimit() - this.itemInfoDb.getItemSize();
    }

    public int getItmSize() {
        return itemInfoDb.getItemSize();
    }


    private void addItemUUIDs(int itemId, long itemUUID) {
        if (!itemId2UUIDs.containsKey(itemId)) {
            itemId2UUIDs.put(itemId, new HashSet<>());
        }
        itemId2UUIDs.get(itemId).add(itemUUID);
    }

    public Item getItemByUUID(long itemUUID) {
        return itemInfoDb.getItem(itemUUID);
    }

    private void remove(Item item) {
        if (itemId2UUIDs.containsKey(item.getItemId())) {
            itemId2UUIDs.get(item.getItemId()).remove(item.getId());
        }
        itemInfoDb.removeItem(item.getId());
        player.getItemEquipManager().onItemRemoved(item);
        // 道具删除Ntf
        player.sendNtfMsg(MsgTypes.MSG_TYPE_BAGITEMREMOVENTF,
                CsBag.BagItemRemoveNtf.newBuilder().addItemUuid(item.getId()).addItemId(item.getItemId()));
    }

    private void removeMulti(Collection<Item> items) {
        if (items.isEmpty()) {
            return;
        }
        BagItemRemoveNtf.Builder msg = BagItemRemoveNtf.newBuilder();
        for (Item item: items) {
            if (itemId2UUIDs.containsKey(item.getItemId())) {
                itemId2UUIDs.get(item.getItemId()).remove(item.getId());
            }
            itemInfoDb.removeItem(item.getId());
            player.getItemEquipManager().onItemRemoved(item);
            msg.addItemUuid(item.getId()).addItemId(item.getItemId());
        }
        // 道具删除Ntf
        player.sendNtfMsg(MsgTypes.MSG_TYPE_BAGITEMREMOVENTF, msg);
    }

    //CreateNewItem 创建新道具
    public Item CreateNewItem(Item_BackpackItem itemConf, long itemNum, long expireTimeMs, int expireType,
            long lessorUid) {
        long itemUUID = ItemIdGenerator.getInstance().allocGuid();
        Item item = new Item()
                .setId(itemUUID)
                .setItemId(itemConf.getId())
                .setNumber(itemNum)
                .setExpireMs(expireTimeMs)
                .setGetTimeMs(Framework.currentTimeMillis())
                .setUpdateMs(Framework.currentTimeMillis())
                .setExpireType(expireType)
                .setLessorUid(lessorUid);
        itemInfoDb.putItem(itemUUID, item);
        addItemUUIDs(itemConf.getId(), itemUUID);
        LOGGER.debug("CreateNewItem success debug, uid:{} itemId:{} itemUUID:{} itemNum:{} expireTimeMs:{}",
                player.getUid(), itemConf.getId(), itemUUID, itemNum, expireTimeMs);
        Monitor.getInstance().add.total(MonitorId.attr_create_item_new, 1);
        return item;
    }

    public HashMap<Long, Item> getItemsByItemId(int itemId) {
        HashMap<Long, Item> items = new HashMap<Long, Item>();
        if (itemId2UUIDs.containsKey(itemId)) {
            for (long itemUUID : itemId2UUIDs.get(itemId)) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null || isExpired(item)) {
                    if (item == null) {
                        LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                                player.getUid(), itemId, itemUUID);
                    }
                    continue;
                }
                items.put(itemUUID, item);
            }
        }
        return items;
    }

    /**
     *
     * @param itemTypes 道具类型
     * @param withTemp 是否包含临时道具
     * @return
     */
    public HashMap<Integer,ArrayList<Item>> getItemListByItemTypeWithType(Collection<Integer> itemTypes, boolean withTemp) {
        HashMap<Integer,ArrayList<Item>> items = new HashMap<>();
        for (int itemId: itemId2UUIDs.keySet()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf != null && itemTypes.contains(itemConf.getType().getNumber())) {
                if (!items.containsKey(itemConf.getType().getNumber())) {
                    items.put(itemConf.getType().getNumber(), new ArrayList<>());
                }
                if (!itemId2UUIDs.get(itemId).isEmpty()) {
                    for (long itemUUID : itemId2UUIDs.get(itemId)) {
                        var item = getItemByUUID(itemUUID);
                        if (item == null) {
                            LOGGER.error("get item err, uid:{} uuid:{}",player.getUid(),itemUUID);
                            continue;
                        }
                        if (!withTemp && hasExpireTime(item)) {
                            continue;
                        }
                        items.get(itemConf.getType().getNumber()).add(item);
                    }
                }
            }
        }
        return items;
    }

    public ArrayList<Integer> getItemsByItemType(HashSet<ItemType> itemTypes) {
       ArrayList<Integer> items = new ArrayList<Integer>(); 
       for (int itemId: itemId2UUIDs.keySet()) { 
           Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
           if (itemConf != null && itemTypes.contains(itemConf.getType())) {
               if (!itemId2UUIDs.get(itemId).isEmpty()) {
                   items.add(itemId); 
               }
           }
       }
       return items;
    }

    public Map<ItemType, List<Integer>> getItemsByItemTypeWithType(HashSet<ItemType> itemTypes) {
        Map<ItemType, List<Integer>> mapItemType = new HashMap<>();
        for (int itemId: itemId2UUIDs.keySet()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf != null && itemTypes.contains(itemConf.getType())) {
                if (!itemId2UUIDs.get(itemId).isEmpty()) {
                    List<Integer> items = mapItemType.computeIfAbsent(itemConf.getType(), k -> new ArrayList<>());
                    items.add(itemId);
                }
            }
        }
        return mapItemType;
    }

    public Map<ItemType, List<Integer>> getItemsByItemTypeWithTypeExcludeItems(HashSet<ItemType> itemTypes, List<Integer> excludeItemIds) {
        Map<ItemType, List<Integer>> mapItemType = new HashMap<>();
        for (int itemId: itemId2UUIDs.keySet()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf != null && itemTypes.contains(itemConf.getType())) {
                if (!itemId2UUIDs.get(itemId).isEmpty() && !excludeItemIds.contains(itemId)) {
                    List<Integer> items = mapItemType.computeIfAbsent(itemConf.getType(), k -> new ArrayList<>());
                    items.add(itemId);
                }
            }
        }
        return mapItemType;
    }

    // 增加排除限时道具
    public Map<ItemType, List<Integer>> getItemsByItemTypeWithTypeExcludeItemsAndExcludeExpired(HashSet<ItemType> itemTypes, List<Integer> excludeItemIds) {
        Map<ItemType, List<Integer>> mapItemType = new HashMap<>();
        for (int itemId: itemId2UUIDs.keySet()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
            if (itemConf != null && itemTypes.contains(itemConf.getType())) {
                if (!itemId2UUIDs.get(itemId).isEmpty() && !excludeItemIds.contains(itemId)) {
                    HashMap<Long, Item> itemsByItemId = getItemsByItemId(itemId);
                    // 判断限时道具
                    boolean isExpireItem = false;
                    for (Item item: itemsByItemId.values()) {
                        if ( item.getExpireMs() > 0){
                            // 因为限时道具不会有多个，所以直接可以判定为限时道具
                            isExpireItem = true;
                            break;
                        }
                    }
                    if (!isExpireItem){
                        List<Integer> items = mapItemType.computeIfAbsent(itemConf.getType(), k -> new ArrayList<>());
                        items.add(itemId);
                    }
                }
            }
        }
        return mapItemType;
    }

    private boolean checkCanStack(Item_BackpackItem itemConf, Item item, long expireTimeMs, int expireType) {
        if (itemConf.getStackedNum() <= 1) {
            return false;
        }
        if (item.getNumber() >= itemConf.getStackedNum()) {
            return false;
        }
        if (expireTimeMs > 0 && expireType != item.getExpireType()) {
            return false;
        }
        return item.getExpireMs() == expireTimeMs;
    }

    public ItemArray.Builder addItem(Item_BackpackItem itemConf, ItemInfo itemInfo) {
        long itemNum = itemInfo.getItemNum();
        long expireTimeMs = itemInfo.getExpireTimeMs();
        int expireType = itemInfo.getExpireType();
        ItemArray.Builder changeItems = ItemArray.newBuilder();
        if (itemNum <= 0) {
            return changeItems;
        }
        if (itemConf.getSeasonExpire()) {
            expireType = ItemExpireType.IET_SEASON_VALUE;
            SeasonConf seasonConf = SeasonConfData.getInstance().getCurrSeasonConf();
            if (seasonConf == null) {
                LOGGER.error("can not find current season conf, uid:{} item:{}", player.getUid(),itemInfo.toString());
//                NKErrorCode.SeasonCanNotFindCurrentSeason.throwError("can not find current season conf");
                return changeItems;
            }
            expireTimeMs = seasonConf.getSeasonId();
        }

        if (itemConf.getType() == ItemType.ItemType_CardBag || itemConf.getType() == ItemType.ItemType_Card) {
            expireTimeMs = TradingCardBaseHandler.getExpireTimeMs(itemConf);
            if (expireTimeMs > 0) {
                LOGGER.debug("set card expireTimeMs, uid:{} item:{}", player.getUid(), itemInfo.getItemId());
            }
        }

        try {
            ResActivityFunPara.ActivityMianGanConfig mianganConfig = ActivityMianGanData.getInstance().getConfigById(itemConf.getId());
            if (mianganConfig != null) {
                expireTimeMs = mianganConfig.getEndTime().getSeconds() * 1000L;
                LOGGER.debug("set miangan  expireTimeMs, uid:{} item:{}", player.getUid(), itemInfo.getItemId());
            }
        } catch (Exception e) {
            LOGGER.error("miangan item time config error ", e);
        }

        // 2023-05-13 当前版本限时道具配置可拥有上限必须为1
//        if (expireTimeMs != 0 && itemConf.getMaxNum() != 1) {
//            LOGGER.debug("invalid expire item config, item id:{} has expire time but max num:{} !=1",
//                    itemConf.getId(), itemConf.getMaxNum());
//            Monitor.getInstance().add.total(MonitorId.attr_expire_item_conf_err, 1, new String[]{String.valueOf(itemConf.getId())});
//        }

        if (itemConf.getMaxNum() == 1 && GetItemNumByItemId(itemConf.getId()) < 0) {
            ArrayList<Item> needRemoveItems = new ArrayList<Item>();
            for (long itemUUID : itemId2UUIDs.get(itemConf.getId())) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null) {
                    LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                            player.getUid(), itemConf.getId(), itemUUID);
                    continue;
                }
                item.addNumber(itemNum);
                changeItems.addItems(
                        itemInfo.toBuilder().setUuid(item.getId()).setLessorUid(item.getLessorUid())
                                .setItemType(itemConf.getType().getNumber()));
                itemNum = 0;
                if (item.getNumber() == 0) {
                    needRemoveItems.add(item);
                }
                break;
            }
            removeMulti(needRemoveItems);
            return changeItems;
        }

        // 续期逻辑
        if (itemConf.getMaxNum() == 1 && itemId2UUIDs.containsKey(itemConf.getId()) && !itemId2UUIDs.get(itemConf.getId()).isEmpty()) {
            for (long itemUUID : itemId2UUIDs.get(itemConf.getId())) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null) {
                    LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                            player.getUid(), itemConf.getId(), itemUUID);
                    continue;
                }
                if (!hasExpireTime(item)) {
                    LOGGER.debug("item dos not hasExpireTime, uid:{} itemId:{} itemUUID:{}",
                            player.getUid(), itemConf.getId(), itemUUID);
                    continue;
                }
                if (expireTimeMs == 0) {
                    item.setExpireMs(expireTimeMs);
                } else {
                    if (expireType == ItemExpireType.IET_RELATIVE_VALUE) {
                        if (itemConf.getType()==ItemType.ItemType_NR3E_Identity
                                && item.getLessorUid() == 0 && itemInfo.getLessorUid() != 0) {
                            if (expireTimeMs > item.getExpireMs()) {
                                long addExpireTime = (expireTimeMs - DateUtils.currentTimeMillis()) * itemNum;
                                item.setExpireMs(DateUtils.currentTimeMillis() + addExpireTime);
                                item.setLessorUid(itemInfo.getLessorUid());
                            }
                        } else {
                            long addExpireTime = (expireTimeMs - DateUtils.currentTimeMillis()) * itemNum;
                            if (isExpired(item)) {
                                item.setExpireMs(DateUtils.currentTimeMillis() + addExpireTime);
                            } else {
                                item.addExpireMs(addExpireTime);
                            }
                        }
                    } else {
                        if (expireTimeMs > item.getExpireMs()) {
                            item.setExpireMs(expireTimeMs);
                        }
                    }
                }

                // 狼人动画等道具的普通租借逻辑不一样，单独支持。优先要显示租借，所以即使来了限时，也不会抹去租借信息
                if (isWolfKillAniItem(itemConf.getType().getNumber()) || itemConf.getType()==ItemType.ItemType_NR3E_Identity){
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("rent_item player in WolfKill {} item {}-{} get not rent item, remove lessor {} info, item:{}， itemInfo:{}",
                                player.getUid(), item.getId(), item.getItemId(), item.getLessorUid(), item.toString(), itemInfo.toString());
                    }
                }else{
                    // 获得了新的非租借的道具，将之前租借的信息抹去
                    if (item.getLessorUid() > 0L && itemInfo.getLessorUid() <= 0L) {
                        if (LOGGER.isDebugEnabled()) {
                            LOGGER.debug("rent_item player {} item {}-{} get not rent item, remove lessor {} info",
                                    player.getUid(), item.getId(), item.getItemId(), item.getLessorUid());
                        }
                        item.setLessorUid(0L);
                    }
                }

                // 狼人动画允许租借。 根据时长来决定lessorUid。而不是简单的覆盖
                long lessorUid = item.getLessorUid();
                // 当用绝对时间，覆盖掉原来的时间。那么就把lessorUid换掉，否则还是老的lessorUid
                if (expireTimeMs == item.getExpireMs() && expireType == ItemExpireType.IET_ABSOLUTE_VALUE) {
                    LOGGER.debug("IET_ABSOLUTE_VALUE add expire item replace lessorUid, item:{} itemInfo:{}",
                            item.toString(), itemInfo.toString());
                    lessorUid = itemInfo.getLessorUid();
                    item.setLessorUid(lessorUid);
                }
                // 大王皮肤再次获得时，需要红点
                boolean isNew = false;
                if (itemConf.getType() == ItemType.ItemType_Chase_BossSkin
                        || itemConf.getType() == ItemType.ItemType_Chase_PropSkin) {
                    isNew = true;
                }
                changeItems.addItems(
                        itemInfo.toBuilder().setUuid(item.getId()).setItemNum(itemNum).setExpireType(expireType)
                                .setLessorUid(lessorUid).setItemType(itemConf.getType().getNumber()).setIsNew(isNew));
                itemNum = 0;
                if (!hasExpireTime(item)) {//已变为永久道具了
                    return changeItems;
                }
            }
            if (itemNum > 0) { // 实际没有获得 也要有反馈显示
                changeItems.addItems(itemInfo.toBuilder().setItemType(itemConf.getType().getNumber()));
                LOGGER.debug("add expire item err has permanent already, uid:{} itemInfo:{}",player.getUid(), itemInfo.toString());
            }
            return changeItems;
        }

        if (itemConf.getMaxNum() == 1 && GetItemNumByItemId(itemConf.getId()) == 0) {
            if (expireTimeMs > 0) {
                if (expireType == ItemExpireType.IET_RELATIVE_VALUE) {
                    long addExpireTime = (expireTimeMs - DateUtils.currentTimeMillis()) * itemNum;
                    expireTimeMs = DateUtils.currentTimeMillis() + addExpireTime;
                }
                itemNum = 0;
            } else {
                itemNum -= 1;
            }
            Item item = CreateNewItem(itemConf, 1, expireTimeMs, expireType, itemInfo.getLessorUid());
            changeItems.addItems(
                    itemInfo.toBuilder().setUuid(item.getId()).setItemNum(item.getNumber()).setExpireType(expireType)
                            .setExpireTimeMs(item.getExpireMs()).setIsNew(true).setLessorUid(item.getLessorUid())
                            .setItemType(itemConf.getType().getNumber()));
            if (itemNum == 0) {
                return changeItems;
            } else {
                LOGGER.warn("add item max num == 1 left itemNum:{} > 0,uid:{} itemId:{} item:{}",
                        itemNum, player.getUid(), itemConf.getId(), item);
            }
        }

        int stackedNum = itemConf.getStackedNum() == 0 ? 1 : itemConf.getStackedNum();
        if (itemId2UUIDs.containsKey(itemConf.getId())) {
            for (long itemUUID : itemId2UUIDs.get(itemConf.getId())) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null || !checkCanStack(itemConf, item, expireTimeMs, expireType)) {
                    if (item == null) {
                        LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                                player.getUid(), itemConf.getId(), itemUUID);
                    }
                    continue;
                }
                long beforeNum = item.getNumber();
                if (item.getNumber() + itemNum <= stackedNum) {
                    item.setNumber(item.getNumber() + itemNum);
                    changeItems.addItems(itemInfo.toBuilder().setUuid(item.getId()).setItemNum(itemNum)
                            .setItemType(itemConf.getType().getNumber()));
                    itemNum = 0;
                } else {
                    item.setNumber(stackedNum);
                    itemNum -= stackedNum - beforeNum;
                    changeItems.addItems(itemInfo.toBuilder().setUuid(item.getId()).setItemNum(stackedNum - beforeNum)
                            .setExpireType(expireType).setItemType(itemConf.getType().getNumber()));
                }
                if (itemNum <= 0) {
                    return changeItems;
                }
            }
        }

        // 有效期相同, 可以堆叠
        while (itemNum > 0) {
            if (itemNum >= stackedNum) {
                /* 租借情况不允许堆叠，所以整理时不需要处理租借方 */
                Item item = CreateNewItem(itemConf, stackedNum, expireTimeMs, expireType, 0);
                itemNum -= stackedNum;
                changeItems.addItems(
                        itemInfo.toBuilder().setUuid(item.getId()).setItemNum(stackedNum).setExpireType(expireType)
                                .setIsNew(true).setItemType(itemConf.getType().getNumber()));
            } else {
                Item item = CreateNewItem(itemConf, itemNum, expireTimeMs, expireType, 0);
                changeItems.addItems(
                        itemInfo.toBuilder().setUuid(item.getId()).setItemNum(itemNum).setExpireType(expireType)
                                .setIsNew(true).setItemType(itemConf.getType().getNumber()));
                return changeItems;
            }
        }
        return changeItems;
    }

    public boolean minItem(Item_BackpackItem itemConf, long itemNum) {
        if (!itemId2UUIDs.containsKey(itemConf.getId())) {
            return false;
        }
        ArrayList<Item> needRemoveItems = new ArrayList<Item>();
        for (long itemUUID : itemId2UUIDs.get(itemConf.getId())) {
            Item item = itemInfoDb.getItem(itemUUID);
            if (isExpired(item)) {
//                needRemoveItems.add(item); 过期道具有专门的接口清理
                continue;
            }
            if (item.getNumber() > itemNum) {
                item.setNumber(item.getNumber() - itemNum);
                LOGGER.debug("dispatchUseItemEvent uid:{}",player.getUid());
                updateHistoryItemUsedInfo(item.getItemId(), itemNum, item.getExpireMs());
                dispatchUseItemEvent(item.getItemId(), (int) itemNum);
                itemNum = 0;
            } else {
                itemNum -= item.getNumber();
                LOGGER.debug("dispatchUseItemEvent uid:{}",player.getUid());
                updateHistoryItemUsedInfo(item.getItemId(), item.getNumber(), item.getExpireMs());
                dispatchUseItemEvent(item.getItemId(), (int) item.getNumber());
                item.setNumber(0);
                needRemoveItems.add(item);
            }
        }
        removeMulti(needRemoveItems);
        if (itemNum > 0) {
            LOGGER.warn("minItem itemId:{} itemNum > 0 {}",itemConf.getId(), itemNum);
            return false;
        }
        return true;
    }

    // 其他地方不要调用!!!
    public boolean minItemForCheckOwnedMax(Item_BackpackItem itemConf, long itemNum, TlogData tlogData) {
        if (!itemId2UUIDs.containsKey(itemConf.getId())) {
            return false;
        }
        long beforeNum = getItemNumByItemIdIgnoreTemp(itemConf.getId());
        ArrayList<Item> needRemoveItems = new ArrayList<Item>();
        for (long itemUUID : itemId2UUIDs.get(itemConf.getId())) {
            Item item = itemInfoDb.getItem(itemUUID);
            if (isExpired(item)) {
//                needRemoveItems.add(item); 过期道具有专门的接口清理
                continue;
            }
            if (hasExpireTime(item)) { // 临时道具不做扣除
                continue;
            }
            if (item.getNumber() > itemNum) {
                item.setNumber(item.getNumber() - itemNum);
                LOGGER.debug("dispatchUseItemEvent uid:{}", player.getUid());
                updateHistoryItemUsedInfo(item.getItemId(), itemNum, item.getExpireMs());
                dispatchUseItemEvent(item.getItemId(), (int) itemNum);
                itemNum = 0;
            } else {
                itemNum -= item.getNumber();
                LOGGER.debug("dispatchUseItemEvent uid:{}", player.getUid());
                updateHistoryItemUsedInfo(item.getItemId(), item.getNumber(), item.getExpireMs());
                dispatchUseItemEvent(item.getItemId(), (int) item.getNumber());
                item.setNumber(0);
                needRemoveItems.add(item);
            }
        }
        removeMulti(needRemoveItems);
        if (itemNum > 0) {
            LOGGER.warn("minItemForCheckOwnedMax itemId:{} itemNum > 0 {}", itemConf.getId(), itemNum);
            return false;
        }
        TlogFlowMgr.sendItemFlow(player, itemConf.getType().getNumber(), itemConf.getId(),
                itemNum, 0, beforeNum, getItemNumByItemIdIgnoreTemp(itemConf.getId()), ADDORREDUCE.REDUCE,
                0, tlogData);
        return true;
    }

    public boolean minItemForIdip(Item_BackpackItem itemConf, long itemNum) {
        if (!itemId2UUIDs.containsKey(itemConf.getId()) || itemId2UUIDs.get(itemConf.getId()).isEmpty()) {
            Item item = CreateNewItem(itemConf, -itemNum, 0, ItemExpireType.IET_RELATIVE_VALUE, 0);
            return true;
        }
        ArrayList<Item> needRemoveItems = new ArrayList<Item>();
        Item lastItem = null;
        for (long itemUUID : itemId2UUIDs.get(itemConf.getId())) {
            Item item = itemInfoDb.getItem(itemUUID);
            if (isExpired(item)) {
//                needRemoveItems.add(item); 过期道具有专门的接口清理
                continue;
            }
            if (item.getNumber() > itemNum) {
                item.setNumber(item.getNumber() - itemNum);
                LOGGER.debug("dispatchUseItemEvent uid:{}",player.getUid());
                updateHistoryItemUsedInfo(item.getItemId(), itemNum, item.getExpireMs());
                dispatchUseItemEvent(item.getItemId(), (int) itemNum);
                itemNum = 0;
            } else {
                itemNum -= item.getNumber();
                LOGGER.debug("dispatchUseItemEvent uid:{}",player.getUid());
                updateHistoryItemUsedInfo(item.getItemId(), item.getNumber(), item.getExpireMs());
                dispatchUseItemEvent(item.getItemId(), (int) item.getNumber());
                item.setNumber(0);
                needRemoveItems.add(item);
            }
            lastItem = item;
        }
        if (lastItem != null && itemNum > 0) {
            lastItem.setNumber(-itemNum);
            needRemoveItems.remove(lastItem);
        }
        removeMulti(needRemoveItems);
        return true;
    }

    public void updateHistoryItemUsedInfo(int itemId, long itemNum, long expireMs) {
        // 只记录配置的道具ID
        List<Integer> needRecordUsedItemIds = MiscConf.getInstance().getMiscConf().getNeedRecordUsedCountItemIdsList();
        if (!needRecordUsedItemIds.contains(itemId)) {
            return;
        }
        HistoryItemUsedInfo historyItemUsedInfo = itemInfoDb.getHistoryItemUsed(itemId);
        if (historyItemUsedInfo == null) {
            historyItemUsedInfo = new HistoryItemUsedInfo().setItemId(itemId);
            itemInfoDb.putHistoryItemUsed(itemId, historyItemUsedInfo);
        }
        if (expireMs > 0) {
            historyItemUsedInfo.addTempNum(itemNum);
        } else {
            historyItemUsedInfo.addNum(itemNum);
        }
    }

    long getItemExpireByUUID(long itemUUID) {
        if (itemUUID == 0) {
            return 0L;
        }
        var item = getItemByUUID(itemUUID);
        if (item == null) {
            return 0L;
        }
        return item.getExpireMs();
    }

    public long GetItemNumByItemId(int itemId) {
        long totalNum = 0L;
        if (itemId2UUIDs.containsKey(itemId)) {
            for (long itemUUID : itemId2UUIDs.get(itemId)) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null || isExpired(item)) {
                    if (item == null) {
                        LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                                player.getUid(), itemId, itemUUID);
                    }
                    continue;
                }
                totalNum += item.getNumber();
            }
        }
        return totalNum;
    }

    //获得道具数量忽略临时道具
    public long getItemNumByItemIdIgnoreTemp(int itemId) {
        long totalNum = 0L;
        if (itemId2UUIDs.containsKey(itemId)) {
            for (long itemUUID : itemId2UUIDs.get(itemId)) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null || isExpired(item)) {
                    if (item == null) {
                        LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                                player.getUid(), itemId, itemUUID);
                    }
                    continue;
                }
                if (item.getExpireMs() > 0) {
                    continue;
                }
                totalNum += item.getNumber();
            }
        }
        return totalNum;
    }

    public boolean isExpired(Item item) {
        if (item.getExpireType() == ItemExpireType.IET_SEASON_VALUE) { // 赛季过期道具
            SeasonConf seasonConf = SeasonConfData.getInstance().getCurrSeasonConf();
            if (seasonConf == null) {
                return true;
            }
            return seasonConf.getSeasonId() != item.getExpireMs();
        }
        return item.getExpireMs() > 0 && item.getExpireMs() < DateUtils.currentTimeMillis();
    }

    public static boolean isExpired(ItemInfo item) {
        if (item.getExpireType() == ItemExpireType.IET_SEASON_VALUE) { // 赛季过期道具
            SeasonConf seasonConf = SeasonConfData.getInstance().getCurrSeasonConf();
            if (seasonConf == null) {
                return true;
            }
            return seasonConf.getSeasonId() != item.getExpireTimeMs();
        }
        return item.getExpireTimeMs() > 0 && item.getExpireTimeMs() < DateUtils.currentTimeMillis();
    }

    public boolean hasExpireTime(Item item) {
        return item.getExpireMs() != 0;
    }

    //GmCommand
    public void clearBagItem(int itemId) {
        ArrayList<Item> removeList = new ArrayList<>();
        if (itemId == 0) {
            for (Collection<Long> itemUUIDs : itemId2UUIDs.values()) {
                for (long itemUUID : itemUUIDs) {
                    Item item = itemInfoDb.getItem(itemUUID);
                    if (item == null) {
                        LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                                player.getUid(), item.getItemId(), itemUUID);
                        continue;
                    }
                    removeList.add(item);
                }
            }
        }
        if (itemId2UUIDs.containsKey(itemId)) {
            for (long itemUUID : itemId2UUIDs.get(itemId)) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null) {
                    LOGGER.error("itemInfoDb getItem error, uid:{} itemId:{} itemUUID:{}",
                            player.getUid(), itemId, itemUUID);
                    continue;
                }
                removeList.add(item);
            }
        }
        removeMulti(removeList);
    }

    private void dispatchUseItemEvent(int itemId, int itemNum) {
        ItemInfo itemInfo = ItemInfo.newBuilder()
                .setItemId(itemId)
                .setItemNum(itemNum)
                .build();
        new PlayerUseItemEvent(player).setItemInfo(itemInfo).dispatch();
//        player.getPlayerEventManager().dispatch(new UseItemEvent(player.getConditionMgr()));
    }

    public List<Item> delExpiredItemId(int itemId) {
        ArrayList<Item> expiredItems = new ArrayList<>();
        if (itemId2UUIDs.containsKey(itemId)) {
            for (long itemUUID : itemId2UUIDs.get(itemId)) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null) {
                    LOGGER.error("itemInfoDb getItem error, uid:{} itemUUID:{}",
                            player.getUid(), itemUUID);
                    continue;
                }
                if (isExpired(item) || item.getNumber() == 0) {
                    expiredItems.add(item);
                }
            }
        }
        if (!expiredItems.isEmpty()) {
            delExpiredItems(expiredItems);
        }
        return expiredItems;
    }

    public List<Item> delExpiredItems() {
        ArrayList<Item> expiredItems = new ArrayList<>();
        for (Collection<Long> itemUUIDs : itemId2UUIDs.values()) {
            for (long itemUUID : itemUUIDs) {
                Item item = itemInfoDb.getItem(itemUUID);
                if (item == null) {
                    LOGGER.error("itemInfoDb getItem error, uid:{} itemUUID:{}",
                            player.getUid(), itemUUID);
                    continue;
                }
                if (isExpired(item) || item.getNumber() == 0) {
                    expiredItems.add(item);
                }
            }
        }
        delExpiredItems(expiredItems);
        return expiredItems;
    }

    public void delExpiredItems(Collection<Item> expiredItems) {
        removeMulti(expiredItems);
        new PlayerItemExpireRemoveEvent(player).addExpireItems(expiredItems).dispatch();
        for (Item item : expiredItems) {
            var tlogData = new TlogData();
            tlogData.setChangeReason(ItemChangeReason.ICR_ItemExpire_VALUE);
            ItemType itemType = BackpackItem.getInstance().get(item.getItemId()).getType();
            TlogFlowMgr.sendItemFlow(player, itemType.getNumber(), item.getItemId(), item.getNumber(), item.getExpireMs(),
                    item.getNumber(), 0, ADDORREDUCE.REDUCE, item.getLessorUid(), tlogData);
            LOGGER.debug("delExpiredItems debug, uid:{}, itemUUID:{} itemId:{} itemNum:{}",
                    player.getUid(), item.getId(), item.getItemId(), item.getNumber());
        }
    }

    public void updateHistoryItemTypeInfo(ItemInfo itemInfo) {
        ItemType itemType = BackpackItem.getInstance().get(itemInfo.getItemId()).getType();
        HistoryItemTypeInfo historyItemTypeInfo = itemInfoDb.getHistoryItemType(itemType);
        if (historyItemTypeInfo == null) {
            historyItemTypeInfo = new HistoryItemTypeInfo().setType(itemType);
            itemInfoDb.putHistoryItemType(itemType, historyItemTypeInfo);
        }
        if (itemInfo.getExpireTimeMs() > 0) {
            historyItemTypeInfo.addTempNum(itemInfo.getItemNum());
        } else {
            historyItemTypeInfo.addNum(itemInfo.getItemNum());
        }
    }

    public long getHistoryItemTypeNum(ItemType itemType) {
        HistoryItemTypeInfo historyItemTypeInfo = itemInfoDb.getHistoryItemType(itemType);
        if (historyItemTypeInfo == null) {
            return 0;
        }
        return historyItemTypeInfo.getNum();
    }

    public ArrayList<Long> getPickPackagePickTimes(int itemId) {
        Item_BackpackItem itemConf = BackpackItem.getInstance().get(itemId);
        if (itemConf == null) {
            return null;
        }
        if (!itemConf.getPackageConf().getPackageType().equals(PackageType.PackageType_Pick)) {
            return null;
        }
        ArrayList<Long> pickedNums = new ArrayList<Long>(
                itemConf.getPackageConf().getPackagePickConf().getPickLimitCount());
        ItemPackageLimit itemPackageLimit = player.getUserAttr().getItemPackageLimit(itemId);
        if (itemPackageLimit == null) {
            return pickedNums;
        }
        for (int limitIndex = 0; limitIndex < itemConf.getPackageConf().getPackagePickConf().getPickLimitCount();limitIndex++) {
            int limitNum = itemConf.getPackageConf().getPackagePickConf().getPickLimit(limitIndex);
            if (limitNum == 0) {
                pickedNums.add(0L);
                continue;
            }
            int limitItemId = itemConf.getPackageConf().getItemIds(limitIndex);
            if (itemPackageLimit.getLimitInfo().containsKey((long) limitItemId)) {
                pickedNums.add(itemPackageLimit.getLimitInfo().get((long) limitItemId).getValue());
            } else {
                pickedNums.add(0L);
            }

        }
        return pickedNums;
    }
    
    public Map<ItemType,Long> calcItemNumByType() {
        HashMap<ItemType, Long> itemTypeCount = new HashMap<ItemType, Long>();
        for (Item item : itemInfoDb.getItem().values()) {
            if (isExpired(item) || hasExpireTime(item)) {
                continue;
            }
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
            if (itemConf == null) {
                continue;
            }
            itemTypeCount.put(itemConf.getType(),itemTypeCount.getOrDefault(itemConf.getType(),0L) + item.getNumber());
        }
        return itemTypeCount;
    }

    @SubscribeEvent(routers = EventRouterType.ERT_PlayerItemExpire)
    protected NKErrorCode onEvent(PlayerItemExpireRemoveEvent event) throws NKRuntimeException {
        LOGGER.debug("PlayerItemExpireRemoveEvent onEvent, Uid:{} event:{}", player.getUid(),event.toString());
        boolean isContainFriendOnlineReminder = false;
        for (NKPair<Integer,Long> item: event.getExpireItems()) {
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getKey());
            if (itemConf == null) {
                LOGGER.error("PlayerItemExpireRemoveEvent get item conf err, Uid:{} itemId:{}", player.getUid(),
                        item.getKey());
                continue;
            }
            if (itemConf.getType() == ItemType.ItemType_Chase_BossSkin
                    || itemConf.getType() == ItemType.ItemType_Chase_PropSkin) {
                player.getChaseMgr().remChaseDressItem(itemConf);
            }
            if (itemConf.getType() == ItemType.ItemType_FriendOnlineReminder) {
                isContainFriendOnlineReminder = true;
            }

            if (itemConf.getExpiredReplaceItemCount() == 0) {
                continue;
            }
            MailAttachmentList.Builder att = MailAttachmentList.newBuilder();
            for (ResCommon.Item replaceItem : itemConf.getExpiredReplaceItemList()) {
                att.addList(MailAttachment.newBuilder().setItemIfo(
                        ItemInfo.newBuilder().setItemId(replaceItem.getItemId())
                                .setItemNum(replaceItem.getItemNum() * item.getValue()).build()));
            }
            List<String> titleFmtArgs = new ArrayList<>();
            List<String> contentFmtArgs = new ArrayList<>();
            titleFmtArgs.add(itemConf.getName());
            contentFmtArgs.add(itemConf.getName());
            if (!att.getListList().isEmpty()) {
                // 道具过期替换邮件
                long ret = MailInteraction.sendTemplateMail(player.getUid(), ITEM_EXPIRE_REPLACE_MAIL_TEMEPLATE_ID, att,
                        0,
                        null, MailInteraction.TlogSendReason.itemExpired, titleFmtArgs, contentFmtArgs);
                if (ret < 0) {
                    LOGGER.error("settle season send template mail failed, uid:{}, itemId:{} itemNum:{}, e:",
                            player.getUid(), item.getKey(), item.getValue());
                }
            }
        }
        if (isContainFriendOnlineReminder) {
            try {
                player.getFriendManager().refreshIntimateOnlineNoticeDressItem();
            } catch (Exception e) {
                LOGGER.error("refreshIntimateOnlineNoticeDressItem error {} ,e:", player.getUid(), e);
            }

        }
        return NKErrorCode.OK;
    }

    @Override
    public boolean isDestroyed() {
        return false;
    }

    /**
     * 登录时自动使用特定类型的道具
     * 检查玩家拥有的道具，如果道具类型为 ItemType_AutoUse 或者配置了 autoUse 字段为 true，则自动使用
     */
    private void autoUseItemsOnLogin() {
        // 遍历所有道具
        for (Item item : itemInfoDb.getItem().values()) {
            if (item == null || item.getNumber() <= 0) {
                continue;
            }

            // 检查道具是否过期
            if (isExpired(item)) {
                continue;
            }

            // 获取道具配置
            Item_BackpackItem itemConf = BackpackItem.getInstance().get(item.getItemId());
            if (itemConf == null) {
                continue;
            }

            if (VersionUtil.checkClientVersion(itemConf.getLowVer(), itemConf.getHighVer(),
                    player.getClientVersion64())) {
                continue;
            }

            // 检查道具类型是否为 ItemType_AutoUse 检查道具配置中的 autoUse 字段
            if (itemConf.getType() == ItemType.ItemType_AutoUse || itemConf.getAutoUse()) {
                // 准备使用参数
                List<Long> paramList = new ArrayList<>();
                itemConf.getUseParamList().forEach(k -> paramList.add((long) k));

                // 异步使用道具，避免阻塞登录流程
                try {
                    CurrentExecutorUtil.runJob(() -> {
                        NKErrorCode result = useItem(item.getId(),
                                Math.toIntExact(item.getNumber()),
                                paramList,
                                ItemChangeReason.ICR_ItemUse.getNumber(),
                                item.getItemId());

                        if (result.isOk()) {
                            LOGGER.info("Login auto use item success, uid:{} itemUUID:{} itemId:{} num:{}",
                                    player.getUid(), item.getId(), item.getItemId(), item.getNumber());
                        } else {
                            LOGGER.warn("Login auto use item failed, uid:{} itemUUID:{} itemId:{} num:{} error:{}",
                                    player.getUid(), item.getId(), item.getItemId(), item.getNumber(), result);
                        }
                        return null;
                    }, "autoUseItemOnLogin", false);
                } catch (Exception e) {
                    LOGGER.error("Login auto use item exception, uid:{} itemUUID:{} itemId:{} error:",
                            player.getUid(), item.getId(), item.getItemId(), e);
                }
            }
        }
    }

    public void afterLogin() {
        try {
            // 登录时自动使用特定类型的道具
            autoUseItemsOnLogin();
        } catch (Exception e) {
            LOGGER.error("autoUseItemsOnLogin exception, uid:{} error:", player.getUid(), e);
        }
    }

    private class OnSeasonChange implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerSeasonChangeFinish)
        private NKErrorCode onEvent(PlayerSeasonChangeFinishEvent event) throws NKRuntimeException {
            LOGGER.info("on receive season change event: {}", event.getNewSeasonId());
            List<Integer> useItems = MiscConf.getInstance().getMiscConf().getSeasonChangeAutoUseItemsList();
            for (int itemId : useItems) {
                HashMap<Long, Item> items = getItemsByItemId(itemId);
                if (!items.isEmpty()) {
                    for (Item item : items.values()) {
                        if (isExpired(item)) {
                            continue;
                        }
                        LOGGER.info("OnSeasonChange useItem, uid:{} itemUUID:{} itemId:{} num:{}",
                                player.getUid(), item.getId(), itemId, item.getNumber());
                        useItem(item.getId(), Math.toIntExact(item.getNumber()),null);
                    }
                }
            }
            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }

    // 获取itemIdsList中已经存在的ItemId
    public List<Integer> getHasItems(List<Integer> itemIdsList) {
        Set<Integer> checkItemIds = new HashSet<>(itemIdsList);
        List<Integer> hasItemIds = new ArrayList<>();
        for (int itemId: itemId2UUIDs.keySet()) {
            if (itemId2UUIDs.get(itemId).isEmpty()) {
                continue;
            }
            if (checkItemIds.contains(itemId)) {
                hasItemIds.add(itemId);
            }
        }
        return hasItemIds;
    }

    private class OnGetItemGatherCheck implements EventConsumer {

        @SubscribeEvent(routers = EventRouterType.ERT_ConsumerPlayerGetItem)
        private NKErrorCode onEvent(PlayerGetItemEvent event) throws NKRuntimeException {
           for (ItemInfo itemInfo : event.getChangeItemInfoList().getItemsList()) {
               Map<DressGatherType, Set<Integer>> gatherCheckList = SuitSeasonInfoData.getInstance()
                       .getDressGatherCheckList(itemInfo.getItemId());
               if (gatherCheckList != null) {
                   gatherCheckList.forEach((type,checkList) -> {
                        if (!checkList.isEmpty()) {
                           var gatheredAttr = player.getUserAttr().getAppearanceRoad().getGather(type.getNumber());
                            if (gatheredAttr == null) {
                                gatheredAttr = new AppearanceRoadGather().setType(type.getNumber());
                                player.getUserAttr().getAppearanceRoad().putGather(type.getNumber(), gatheredAttr);
                            }
                            for (int id : checkList) {
                                if (type == DressGatherType.DGT_SEASON) {
                                    ArrayList<Integer> itemIds = SuitSeasonInfoData.getInstance().getSeasonDressItemIds(id);
                                    if (itemIds == null || itemIds.isEmpty()) {
                                        continue;
                                    }
                                    if (!checkLackItem(player,itemIds)) {
                                        gatheredAttr.addCollected(id);
                                    }
                                } else if (type == DressGatherType.DGT_THEME) {
                                    ArrayList<Integer> itemIds = SuitSeasonInfoData.getInstance().getThemeDressItemIds(id);
                                    if (itemIds == null || itemIds.isEmpty()) {
                                        continue;
                                    }
                                    if (!checkLackItem(player,itemIds)) {
                                        gatheredAttr.addCollected(id);
                                    }
                                }
                            }
                        }
                   });
               }
           }
            return NKErrorCode.OK;
        }

        @Override
        public boolean isDestroyed() {
            return false;
        }
    }

    private static boolean checkLackItem(Player player, ArrayList<Integer> itemIds) {
        for (int itemId : itemIds) {
            if (player.getItemManager().getItemNumByItemIdIgnoreTemp(itemId) == 0) {
                return true;
            }
        }
        return false;
    }

    public void calcGatherUpIds() {
        var seasonGathered = player.getUserAttr().getAppearanceRoad().getGather(DressGatherType.DGT_SEASON.getNumber());
        if (seasonGathered == null) {
            seasonGathered = new AppearanceRoadGather();
            seasonGathered.setType(DressGatherType.DGT_SEASON.getNumber());
            player.getUserAttr().getAppearanceRoad().putGather(DressGatherType.DGT_SEASON.getNumber(), seasonGathered);
        } else {
            seasonGathered.clearCollected();
        }
        var seasonIds = SuitSeasonInfoData.getInstance().getAllSeasonIds();
        for (int seasonId : seasonIds) {
            ArrayList<Integer> itemIds = SuitSeasonInfoData.getInstance().getSeasonDressItemIds(seasonId);
            if (itemIds == null || itemIds.isEmpty()) {
                continue;
            }
            if (!checkLackItem(player,itemIds)) {
                seasonGathered.addCollected(seasonId);
            }
        }

        var themeGathered = player.getUserAttr().getAppearanceRoad().getGather(DressGatherType.DGT_THEME.getNumber());
        if (themeGathered == null) {
            themeGathered = new AppearanceRoadGather();
            themeGathered.setType(DressGatherType.DGT_THEME.getNumber());
            player.getUserAttr().getAppearanceRoad().putGather(DressGatherType.DGT_THEME.getNumber(), themeGathered);
        } else {
            themeGathered.clearCollected();
        }
        var themeIds = SuitThemedInfoData.getInstance().getAllThemeIds();
        for (int themeId : themeIds) {
            ArrayList<Integer> itemIds = SuitSeasonInfoData.getInstance().getThemeDressItemIds(themeId);
            if (itemIds == null || itemIds.isEmpty()) {
                continue;
            }
            if (!checkLackItem(player,itemIds)) {
                themeGathered.addCollected(themeId);
            }
        }
    }
}
