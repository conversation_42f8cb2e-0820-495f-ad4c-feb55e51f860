package com.tencent.wea.playerservice.mall.price;

/**
 * 价格修改器接口
 * 用于实现各种特殊的价格修改逻辑
 * 
 * 实现此接口的类可以对商品价格进行自定义修改，例如：
 * - 数量折扣
 * - VIP折扣
 * - 活动折扣
 * - 等级折扣
 * - 季节性折扣
 * 等等
 */
public interface PriceModifier {
    
    /**
     * 获取修改器名称
     * 用于标识和日志记录
     * 
     * @return 修改器名称，不能为null或空字符串
     */
    String getName();
    
    /**
     * 获取修改器优先级
     * 数值越小优先级越高，优先级高的修改器会先被应用
     * 
     * 建议的优先级范围：
     * - 1-50: 高优先级（如数量折扣、配置驱动的折扣）
     * - 51-100: 中优先级（如VIP折扣、活动折扣）
     * - 101-200: 低优先级（如等级折扣、季节折扣）
     * - 201+: 最低优先级（如默认折扣）
     * 
     * @return 优先级数值
     */
    int getPriority();
    
    /**
     * 检查是否适用于当前上下文
     * 
     * 此方法用于判断当前修改器是否应该对给定的价格计算上下文生效。
     * 例如：
     * - 数量折扣修改器检查商品是否配置了数量折扣
     * - VIP折扣修改器检查玩家是否为VIP
     * - 活动折扣修改器检查当前是否有相关活动
     * 
     * @param context 价格计算上下文，包含玩家、商品、购买数量等信息
     * @return true表示此修改器适用于当前上下文，false表示不适用
     */
    boolean isApplicable(PriceContext context);
    
    /**
     * 修改价格
     * 
     * 根据修改器的逻辑对当前价格进行修改。
     * 注意：
     * - 只有在isApplicable返回true时才会调用此方法
     * - 修改器应该基于currentPrice进行计算，而不是基于商品的原始价格
     * - 如果不需要修改价格，应该返回PriceModificationResult.noChange(currentPrice)
     * - 修改后的价格不应该为负数
     * 
     * @param context 价格计算上下文
     * @param currentPrice 当前价格（可能已经被其他修改器修改过）
     * @return 价格修改结果，包含修改后的价格和修改原因
     */
    PriceModificationResult modifyPrice(PriceContext context, long currentPrice);
}
