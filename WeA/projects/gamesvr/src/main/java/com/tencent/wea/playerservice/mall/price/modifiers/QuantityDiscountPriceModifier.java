package com.tencent.wea.playerservice.mall.price.modifiers;

import com.tencent.wea.playerservice.mall.price.PriceContext;
import com.tencent.wea.playerservice.mall.price.PriceModificationResult;
import com.tencent.wea.playerservice.mall.price.PriceModifier;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountTier;
import com.tencent.wea.xlsRes.ResKeywords.ModifyPriceType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 数量折扣价格修改器
 * 根据购买数量提供阶梯式价格折扣
 */
public class QuantityDiscountPriceModifier implements PriceModifier {
    private static final Logger LOGGER = LogManager.getLogger(QuantityDiscountPriceModifier.class);
    
    private static final String MODIFIER_NAME = "QuantityDiscountPriceModifier";
    private static final int PRIORITY = 50; // 数量折扣优先级较高
    
    @Override
    public String getName() {
        return MODIFIER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean isApplicable(PriceContext context) {
        MallCommodity commodity = context.getCommodityConf();
        
        // 检查是否启用价格修改
        if (!commodity.hasEnableModifyPrice() || !commodity.getEnableModifyPrice()) {
            return false;
        }
        
        // 检查是否是数量折扣类型
        if (!commodity.hasModifyPriceType() || 
            commodity.getModifyPriceType() != ModifyPriceType.MPT_QuantityDiscount) {
            return false;
        }
        
        // 检查是否有数量折扣参数
        if (!commodity.hasQuantityDiscountParams()) {
            LOGGER.warn("Commodity {} has quantity discount type but no parameters", 
                       commodity.getCommodityId());
            return false;
        }
        
        // 检查购买数量是否大于1（数量为1时通常不需要折扣）
        return context.getBuyNum() > 0;
    }
    
    @Override
    public PriceModificationResult modifyPrice(PriceContext context, long currentPrice) {
        MallCommodity commodity = context.getCommodityConf();
        QuantityDiscountParams params = commodity.getQuantityDiscountParams();
        int buyNum = context.getBuyNum();
        
        // 查找适用的折扣阶梯
        QuantityDiscountTier applicableTier = findApplicableTier(params.getTiersList(), buyNum);
        
        if (applicableTier == null) {
            LOGGER.debug("No applicable quantity discount tier found for commodity {} with quantity {}", 
                        commodity.getCommodityId(), buyNum);
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 计算新价格
        long newPrice = calculateDiscountedPrice(currentPrice, applicableTier);
        
        if (newPrice == currentPrice) {
            return PriceModificationResult.noChange(currentPrice);
        }
        
        // 构建折扣说明
        String reason = buildDiscountReason(applicableTier, buyNum);
        
        LOGGER.debug("Quantity discount applied: uid={}, commodityId={}, quantity={}, " +
                    "originalPrice={}, newPrice={}, tier=[{}-{}], discount={}", 
                    context.getUid(), commodity.getCommodityId(), buyNum,
                    currentPrice, newPrice, 
                    applicableTier.getMinQuantity(), 
                    applicableTier.getMaxQuantity() > 0 ? applicableTier.getMaxQuantity() : "∞",
                    applicableTier.hasDiscountRate() ? applicableTier.getDiscountRate() : "固定价格");
        
        return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
    }
    
    /**
     * 查找适用的折扣阶梯
     * @param tiers 折扣阶梯列表
     * @param quantity 购买数量
     * @return 适用的折扣阶梯，如果没有则返回null
     */
    private QuantityDiscountTier findApplicableTier(List<QuantityDiscountTier> tiers, int quantity) {
        QuantityDiscountTier bestTier = null;
        
        for (QuantityDiscountTier tier : tiers) {
            // 检查数量是否在阶梯范围内
            if (quantity >= tier.getMinQuantity()) {
                // 检查最大数量限制（0表示无上限）
                if (tier.getMaxQuantity() == 0 || quantity <= tier.getMaxQuantity()) {
                    // 选择最优的阶梯（通常是折扣最大的）
                    if (bestTier == null || isBetterTier(tier, bestTier)) {
                        bestTier = tier;
                    }
                }
            }
        }
        
        return bestTier;
    }
    
    /**
     * 判断是否是更好的折扣阶梯
     * @param tier1 阶梯1
     * @param tier2 阶梯2
     * @return tier1是否比tier2更好
     */
    private boolean isBetterTier(QuantityDiscountTier tier1, QuantityDiscountTier tier2) {
        // 优先选择最小数量要求更高的阶梯（更精确的匹配）
        if (tier1.getMinQuantity() != tier2.getMinQuantity()) {
            return tier1.getMinQuantity() > tier2.getMinQuantity();
        }
        
        // 如果最小数量相同，选择折扣更大的
        if (tier1.hasDiscountRate() && tier2.hasDiscountRate()) {
            return tier1.getDiscountRate() < tier2.getDiscountRate(); // 折扣率越小，折扣越大
        }
        
        // 如果有固定价格，优先选择固定价格
        if (tier1.hasFixedPrice() && !tier2.hasFixedPrice()) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 计算折扣后的价格
     * @param originalPrice 原价
     * @param tier 折扣阶梯
     * @return 折扣后的价格
     */
    private long calculateDiscountedPrice(long originalPrice, QuantityDiscountTier tier) {
        // 如果设置了固定价格，直接使用固定价格
        if (tier.hasFixedPrice() && tier.getFixedPrice() > 0) {
            return tier.getFixedPrice();
        }
        
        // 使用折扣率计算
        if (tier.hasDiscountRate() && tier.getDiscountRate() > 0) {
            double discountRate = tier.getDiscountRate();
            
            // 确保折扣率在合理范围内
            if (discountRate > 1.0) {
                LOGGER.warn("Invalid discount rate {} for quantity discount, using 1.0", discountRate);
                discountRate = 1.0;
            }
            
            long newPrice = Math.round(originalPrice * discountRate);
            
            // 确保价格不为负数
            return Math.max(0, newPrice);
        }
        
        // 如果没有有效的折扣配置，返回原价
        LOGGER.warn("No valid discount configuration in tier: {}", tier);
        return originalPrice;
    }
    
    /**
     * 构建折扣说明
     * @param tier 折扣阶梯
     * @param quantity 购买数量
     * @return 折扣说明
     */
    private String buildDiscountReason(QuantityDiscountTier tier, int quantity) {
        StringBuilder reason = new StringBuilder();
        reason.append("数量折扣");
        
        // 添加数量范围说明
        if (tier.getMaxQuantity() > 0) {
            reason.append(String.format("[%d-%d件]", tier.getMinQuantity(), tier.getMaxQuantity()));
        } else {
            reason.append(String.format("[%d件以上]", tier.getMinQuantity()));
        }
        
        // 添加折扣说明
        if (tier.hasFixedPrice()) {
            reason.append(String.format("固定价格%d", tier.getFixedPrice()));
        } else if (tier.hasDiscountRate()) {
            double discount = (1.0 - tier.getDiscountRate()) * 100;
            reason.append(String.format("%.0f%%折扣", discount));
        }
        
        return reason.toString();
    }
}
