package com.tencent.wea.playerservice.mall.price.modifiers;

import com.tencent.wea.playerservice.mall.price.PriceContext;
import com.tencent.wea.playerservice.mall.price.PriceModificationResult;
import com.tencent.wea.playerservice.mall.price.PriceModifier;
import com.tencent.wea.xlsRes.ResKeywords.CommodityModifyPriceType;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountTier;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;

/**
 * 数量折扣价格修改器
 * 根据购买数量提供阶梯价格折扣
 */
public class QuantityDiscountPriceModifier implements PriceModifier {
    private static final Logger LOGGER = LogManager.getLogger(QuantityDiscountPriceModifier.class);
    
    private static final String MODIFIER_NAME = "QuantityDiscountPriceModifier";
    private static final int PRIORITY = 50; // 高优先级，确保数量折扣优先应用
    
    @Override
    public String getName() {
        return MODIFIER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean isApplicable(PriceContext context) {
        MallCommodity commodity = context.getCommodityConf();
        
        // 检查是否配置了价格修改类型
        if (!commodity.hasModifyPriceType()) {
            return false;
        }
        
        // 检查是否是数量折扣类型
        if (commodity.getModifyPriceType() != CommodityModifyPriceType.CMPT_QuantityDiscount) {
            return false;
        }
        
        // 检查是否有数量折扣参数配置
        if (!commodity.hasQuantityDiscountParams()) {
            LOGGER.warn("Commodity {} has quantity discount type but no quantityDiscountParams", 
                       commodity.getCommodityId());
            return false;
        }
        
        QuantityDiscountParams params = commodity.getQuantityDiscountParams();
        if (params.getTiersList().isEmpty()) {
            LOGGER.warn("Commodity {} has empty quantity discount tiers", 
                       commodity.getCommodityId());
            return false;
        }
        
        return true;
    }
    
    @Override
    public PriceModificationResult modifyPrice(PriceContext context, long currentPrice) {
        MallCommodity commodity = context.getCommodityConf();
        int buyNum = context.getBuyNum();
        
        try {
            QuantityDiscountParams params = commodity.getQuantityDiscountParams();
            List<QuantityDiscountTier> tiers = params.getTiersList();
            
            // 找到适用的价格阶梯
            QuantityDiscountTier applicableTier = findApplicableTier(tiers, buyNum);
            
            if (applicableTier == null) {
                // 没有找到适用的阶梯，使用原价
                LOGGER.debug("No applicable tier found for commodity {} with buyNum {}", 
                           commodity.getCommodityId(), buyNum);
                return PriceModificationResult.noChange(currentPrice);
            }
            
            long newPrice = applicableTier.getFixedPrice();
            
            // 确保价格不为负数
            if (newPrice < 0) {
                LOGGER.warn("Negative price {} for commodity {} with buyNum {}, using 0", 
                           newPrice, commodity.getCommodityId(), buyNum);
                newPrice = 0;
            }
            
            if (newPrice == currentPrice) {
                return PriceModificationResult.noChange(currentPrice);
            }
            
            String reason = String.format("数量折扣：购买%d个，单价%d", buyNum, newPrice);
            
            LOGGER.debug("Quantity discount applied: uid={}, commodityId={}, buyNum={}, " +
                        "originalPrice={}, newPrice={}, tier={}", 
                        context.getUid(), commodity.getCommodityId(), buyNum, 
                        currentPrice, newPrice, applicableTier.getMinBuyCount());
            
            return PriceModificationResult.changed(newPrice, reason, MODIFIER_NAME);
            
        } catch (Exception e) {
            LOGGER.error("Error applying quantity discount for commodity {}: {}", 
                        commodity.getCommodityId(), e.getMessage(), e);
            return PriceModificationResult.noChange(currentPrice);
        }
    }
    
    /**
     * 找到适用的价格阶梯
     * 选择满足购买数量条件的最高阶梯
     * 
     * @param tiers 价格阶梯列表
     * @param buyNum 购买数量
     * @return 适用的价格阶梯，如果没有找到则返回null
     */
    private QuantityDiscountTier findApplicableTier(List<QuantityDiscountTier> tiers, int buyNum) {
        QuantityDiscountTier bestTier = null;
        
        for (QuantityDiscountTier tier : tiers) {
            // 检查购买数量是否满足阶梯条件
            if (buyNum >= tier.getMinBuyCount()) {
                // 选择最高的阶梯（最大的minBuyCount）
                if (bestTier == null || tier.getMinBuyCount() > bestTier.getMinBuyCount()) {
                    bestTier = tier;
                }
            }
        }
        
        return bestTier;
    }
}
