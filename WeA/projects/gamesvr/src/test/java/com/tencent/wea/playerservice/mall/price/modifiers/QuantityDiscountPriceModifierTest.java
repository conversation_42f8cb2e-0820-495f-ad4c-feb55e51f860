package com.tencent.wea.playerservice.mall.price.modifiers;

import com.tencent.wea.playerservice.mall.price.PriceContext;
import com.tencent.wea.playerservice.mall.price.PriceModificationResult;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResKeywords.CommodityModifyPriceType;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountTier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 数量折扣价格修改器测试
 */
class QuantityDiscountPriceModifierTest {

    @Mock
    private Player mockPlayer;
    
    @Mock
    private MallCommodity mockCommodity;
    
    private QuantityDiscountPriceModifier modifier;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        modifier = new QuantityDiscountPriceModifier();
        
        // 设置基本的mock行为
        when(mockPlayer.getUid()).thenReturn(12345L);
    }
    
    @Test
    void testGetName() {
        assertEquals("QuantityDiscountPriceModifier", modifier.getName());
    }
    
    @Test
    void testGetPriority() {
        assertEquals(50, modifier.getPriority());
    }
    
    @Test
    void testIsApplicable_NoModifyPriceType() {
        // 测试没有价格修改类型的情况
        when(mockCommodity.hasModifyPriceType()).thenReturn(false);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        assertFalse(modifier.isApplicable(context));
    }
    
    @Test
    void testIsApplicable_WrongModifyPriceType() {
        // 测试错误的价格修改类型
        when(mockCommodity.hasModifyPriceType()).thenReturn(true);
        when(mockCommodity.getModifyPriceType()).thenReturn(CommodityModifyPriceType.CMPT_None);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        assertFalse(modifier.isApplicable(context));
    }
    
    @Test
    void testIsApplicable_NoQuantityDiscountParams() {
        // 测试没有数量折扣参数的情况
        when(mockCommodity.hasModifyPriceType()).thenReturn(true);
        when(mockCommodity.getModifyPriceType()).thenReturn(CommodityModifyPriceType.CMPT_QuantityDiscount);
        when(mockCommodity.hasQuantityDiscountParams()).thenReturn(false);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        assertFalse(modifier.isApplicable(context));
    }
    
    @Test
    void testIsApplicable_EmptyTiers() {
        // 测试空的价格阶梯
        when(mockCommodity.hasModifyPriceType()).thenReturn(true);
        when(mockCommodity.getModifyPriceType()).thenReturn(CommodityModifyPriceType.CMPT_QuantityDiscount);
        when(mockCommodity.hasQuantityDiscountParams()).thenReturn(true);
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder().build();
        when(mockCommodity.getQuantityDiscountParams()).thenReturn(params);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        assertFalse(modifier.isApplicable(context));
    }
    
    @Test
    void testIsApplicable_ValidConfiguration() {
        // 测试有效配置
        when(mockCommodity.hasModifyPriceType()).thenReturn(true);
        when(mockCommodity.getModifyPriceType()).thenReturn(CommodityModifyPriceType.CMPT_QuantityDiscount);
        when(mockCommodity.hasQuantityDiscountParams()).thenReturn(true);
        when(mockCommodity.getCommodityId()).thenReturn(1001);
        
        QuantityDiscountTier tier = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(800)
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier)
                .build();
        when(mockCommodity.getQuantityDiscountParams()).thenReturn(params);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        assertTrue(modifier.isApplicable(context));
    }
    
    @Test
    void testModifyPrice_SingleTier() {
        // 测试单个价格阶梯
        setupValidCommodity();
        
        QuantityDiscountTier tier = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(800)
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier)
                .build();
        when(mockCommodity.getQuantityDiscountParams()).thenReturn(params);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertTrue(result.isPriceChanged());
        assertEquals(800, result.getModifiedPrice());
        assertTrue(result.getReason().contains("数量折扣"));
    }
    
    @Test
    void testModifyPrice_MultipleTiers() {
        // 测试多个价格阶梯
        setupValidCommodity();
        
        QuantityDiscountTier tier1 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(900)
                .build();
        
        QuantityDiscountTier tier2 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(5)
                .setFixedPrice(800)
                .build();
        
        QuantityDiscountTier tier3 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(10)
                .setFixedPrice(700)
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier1)
                .addTiers(tier2)
                .addTiers(tier3)
                .build();
        when(mockCommodity.getQuantityDiscountParams()).thenReturn(params);
        
        // 测试购买1个 - 应该使用tier1
        PriceContext context1 = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        PriceModificationResult result1 = modifier.modifyPrice(context1, 1000);
        assertEquals(900, result1.getModifiedPrice());
        
        // 测试购买5个 - 应该使用tier2
        PriceContext context5 = new PriceContext(mockPlayer, mockCommodity, 5, 1000, false, "test-bill");
        PriceModificationResult result5 = modifier.modifyPrice(context5, 1000);
        assertEquals(800, result5.getModifiedPrice());
        
        // 测试购买10个 - 应该使用tier3
        PriceContext context10 = new PriceContext(mockPlayer, mockCommodity, 10, 1000, false, "test-bill");
        PriceModificationResult result10 = modifier.modifyPrice(context10, 1000);
        assertEquals(700, result10.getModifiedPrice());
        
        // 测试购买15个 - 应该使用tier3（最高阶梯）
        PriceContext context15 = new PriceContext(mockPlayer, mockCommodity, 15, 1000, false, "test-bill");
        PriceModificationResult result15 = modifier.modifyPrice(context15, 1000);
        assertEquals(700, result15.getModifiedPrice());
    }
    
    @Test
    void testModifyPrice_NoApplicableTier() {
        // 测试没有适用的价格阶梯
        setupValidCommodity();
        
        QuantityDiscountTier tier = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(10)
                .setFixedPrice(800)
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier)
                .build();
        when(mockCommodity.getQuantityDiscountParams()).thenReturn(params);
        
        // 购买数量小于最小阶梯
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 5, 1000, false, "test-bill");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertFalse(result.isPriceChanged());
        assertEquals(1000, result.getModifiedPrice());
    }
    
    @Test
    void testModifyPrice_SamePriceAsOriginal() {
        // 测试计算出的价格与原价相同
        setupValidCommodity();
        
        QuantityDiscountTier tier = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(1000) // 与原价相同
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier)
                .build();
        when(mockCommodity.getQuantityDiscountParams()).thenReturn(params);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertFalse(result.isPriceChanged());
        assertEquals(1000, result.getModifiedPrice());
    }
    
    @Test
    void testModifyPrice_NegativePrice() {
        // 测试负价格处理
        setupValidCommodity();
        
        QuantityDiscountTier tier = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(-100) // 负价格
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier)
                .build();
        when(mockCommodity.getQuantityDiscountParams()).thenReturn(params);
        
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test-bill");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertTrue(result.isPriceChanged());
        assertEquals(0, result.getModifiedPrice()); // 负价格应该被设为0
    }
    
    private void setupValidCommodity() {
        when(mockCommodity.hasModifyPriceType()).thenReturn(true);
        when(mockCommodity.getModifyPriceType()).thenReturn(CommodityModifyPriceType.CMPT_QuantityDiscount);
        when(mockCommodity.hasQuantityDiscountParams()).thenReturn(true);
        when(mockCommodity.getCommodityId()).thenReturn(1001);
    }
}
