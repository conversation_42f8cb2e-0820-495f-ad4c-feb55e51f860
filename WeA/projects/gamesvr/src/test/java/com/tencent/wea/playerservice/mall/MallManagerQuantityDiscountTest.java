package com.tencent.wea.playerservice.mall;

import com.tencent.wea.playerservice.mall.price.PriceCalculator;
import com.tencent.wea.playerservice.mall.price.PriceModifierInitializer;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResKeywords.CommodityModifyPriceType;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountTier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MallManager数量折扣集成测试
 */
class MallManagerQuantityDiscountTest {

    @Mock
    private Player mockPlayer;
    
    private PriceCalculator priceCalculator;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 初始化价格修改器
        PriceModifierInitializer.initialize();
        
        // 创建价格计算器
        priceCalculator = new PriceCalculator();
        
        // 设置基本的mock行为
        when(mockPlayer.getUid()).thenReturn(12345L);
    }
    
    @Test
    void testQuantityDiscountPriceCalculation() {
        // 创建带有数量折扣配置的商品
        MallCommodity commodity = createQuantityDiscountCommodity();
        
        // 测试不同购买数量的价格计算
        
        // 购买1个 - 基础价格1000，应该使用第一阶梯900
        long price1 = priceCalculator.calculateFinalPrice(mockPlayer, commodity, 1, false, "test-bill");
        assertEquals(900, price1, "购买1个应该使用第一阶梯价格900");
        
        // 购买5个 - 应该使用第二阶梯800
        long price5 = priceCalculator.calculateFinalPrice(mockPlayer, commodity, 5, false, "test-bill");
        assertEquals(800, price5, "购买5个应该使用第二阶梯价格800");
        
        // 购买10个 - 应该使用第三阶梯700
        long price10 = priceCalculator.calculateFinalPrice(mockPlayer, commodity, 10, false, "test-bill");
        assertEquals(700, price10, "购买10个应该使用第三阶梯价格700");
        
        // 购买20个 - 应该使用最高阶梯700
        long price20 = priceCalculator.calculateFinalPrice(mockPlayer, commodity, 20, false, "test-bill");
        assertEquals(700, price20, "购买20个应该使用最高阶梯价格700");
    }
    
    @Test
    void testNormalCommodityPriceCalculation() {
        // 创建普通商品（没有数量折扣）
        MallCommodity normalCommodity = createNormalCommodity();
        
        // 测试普通商品的价格计算
        long price1 = priceCalculator.calculateFinalPrice(mockPlayer, normalCommodity, 1, false, "test-bill");
        assertEquals(1000, price1, "普通商品应该使用基础价格");
        
        long price10 = priceCalculator.calculateFinalPrice(mockPlayer, normalCommodity, 10, false, "test-bill");
        assertEquals(1000, price10, "普通商品不论购买多少都应该使用基础价格");
    }
    
    @Test
    void testDiscountPriceCommodity() {
        // 创建有折扣价的商品
        MallCommodity discountCommodity = createDiscountCommodity();
        
        // 测试折扣价商品的价格计算
        long price = priceCalculator.calculateFinalPrice(mockPlayer, discountCommodity, 1, false, "test-bill");
        assertEquals(800, price, "应该使用折扣价作为基础价格");
    }
    
    @Test
    void testQuantityDiscountWithDiscountPrice() {
        // 创建既有折扣价又有数量折扣的商品
        MallCommodity commodity = createQuantityDiscountWithDiscountPriceCommodity();
        
        // 测试价格计算 - 数量折扣应该基于折扣价计算
        long price1 = priceCalculator.calculateFinalPrice(mockPlayer, commodity, 1, false, "test-bill");
        assertEquals(720, price1, "数量折扣应该基于折扣价800计算，第一阶梯720");
        
        long price5 = priceCalculator.calculateFinalPrice(mockPlayer, commodity, 5, false, "test-bill");
        assertEquals(640, price5, "购买5个应该使用第二阶梯640");
    }
    
    @Test
    void testInvalidQuantityDiscountConfiguration() {
        // 创建无效配置的商品
        MallCommodity invalidCommodity = createInvalidQuantityDiscountCommodity();
        
        // 测试无效配置时应该回退到基础价格
        long price = priceCalculator.calculateFinalPrice(mockPlayer, invalidCommodity, 5, false, "test-bill");
        assertEquals(1000, price, "无效配置应该回退到基础价格");
    }
    
    private MallCommodity createQuantityDiscountCommodity() {
        // 创建价格阶梯
        QuantityDiscountTier tier1 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(900)
                .build();
        
        QuantityDiscountTier tier2 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(5)
                .setFixedPrice(800)
                .build();
        
        QuantityDiscountTier tier3 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(10)
                .setFixedPrice(700)
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier1)
                .addTiers(tier2)
                .addTiers(tier3)
                .build();
        
        return MallCommodity.newBuilder()
                .setCommodityId(1001)
                .setPrice(1000)
                .setModifyPriceType(CommodityModifyPriceType.CMPT_QuantityDiscount)
                .setQuantityDiscountParams(params)
                .build();
    }
    
    private MallCommodity createNormalCommodity() {
        return MallCommodity.newBuilder()
                .setCommodityId(1002)
                .setPrice(1000)
                .build();
    }
    
    private MallCommodity createDiscountCommodity() {
        return MallCommodity.newBuilder()
                .setCommodityId(1003)
                .setPrice(1000)
                .setDiscountPrice(800)
                .build();
    }
    
    private MallCommodity createQuantityDiscountWithDiscountPriceCommodity() {
        // 创建价格阶梯（基于折扣价800）
        QuantityDiscountTier tier1 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(1)
                .setFixedPrice(720) // 800的9折
                .build();
        
        QuantityDiscountTier tier2 = QuantityDiscountTier.newBuilder()
                .setMinBuyCount(5)
                .setFixedPrice(640) // 800的8折
                .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
                .addTiers(tier1)
                .addTiers(tier2)
                .build();
        
        return MallCommodity.newBuilder()
                .setCommodityId(1004)
                .setPrice(1000)
                .setDiscountPrice(800)
                .setModifyPriceType(CommodityModifyPriceType.CMPT_QuantityDiscount)
                .setQuantityDiscountParams(params)
                .build();
    }
    
    private MallCommodity createInvalidQuantityDiscountCommodity() {
        // 创建有数量折扣类型但没有参数的商品
        return MallCommodity.newBuilder()
                .setCommodityId(1005)
                .setPrice(1000)
                .setModifyPriceType(CommodityModifyPriceType.CMPT_QuantityDiscount)
                // 故意不设置quantityDiscountParams
                .build();
    }
}
