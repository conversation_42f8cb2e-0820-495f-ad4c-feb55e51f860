package com.tencent.wea.playerservice.mall.price;

import com.tencent.wea.playerservice.mall.price.modifiers.QuantityDiscountPriceModifier;
import com.tencent.wea.playerservice.player.Player;
import com.tencent.wea.xlsRes.ResMall.MallCommodity;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountParams;
import com.tencent.wea.xlsRes.ResMall.QuantityDiscountTier;
import com.tencent.wea.xlsRes.ResKeywords.ModifyPriceType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 数量折扣价格修改器单元测试
 */
public class QuantityDiscountPriceModifierTest {
    
    @Mock
    private Player mockPlayer;
    
    @Mock
    private MallCommodity.Builder mockCommodityBuilder;
    
    private QuantityDiscountPriceModifier modifier;
    private MallCommodity mockCommodity;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        modifier = new QuantityDiscountPriceModifier();
        
        // 创建模拟商品
        mockCommodity = createMockCommodity();
        
        // 设置模拟玩家
        when(mockPlayer.getUid()).thenReturn(123456789L);
    }
    
    @Test
    void testModifierBasicProperties() {
        assertEquals("QuantityDiscountPriceModifier", modifier.getName());
        assertEquals(50, modifier.getPriority());
    }
    
    @Test
    void testNotApplicableWhenModifyPriceDisabled() {
        // 创建未启用价格修改的商品
        MallCommodity commodity = MallCommodity.newBuilder()
            .setCommodityId(12345)
            .setEnableModifyPrice(false)
            .build();
        
        PriceContext context = new PriceContext(mockPlayer, commodity, 5, 1000, false, "test");
        
        assertFalse(modifier.isApplicable(context));
    }
    
    @Test
    void testNotApplicableWhenNotQuantityDiscountType() {
        // 创建非数量折扣类型的商品
        MallCommodity commodity = MallCommodity.newBuilder()
            .setCommodityId(12345)
            .setEnableModifyPrice(true)
            .setModifyPriceType(ModifyPriceType.MPT_VipDiscount)
            .build();
        
        PriceContext context = new PriceContext(mockPlayer, commodity, 5, 1000, false, "test");
        
        assertFalse(modifier.isApplicable(context));
    }
    
    @Test
    void testNotApplicableWhenNoQuantityDiscountParams() {
        // 创建没有数量折扣参数的商品
        MallCommodity commodity = MallCommodity.newBuilder()
            .setCommodityId(12345)
            .setEnableModifyPrice(true)
            .setModifyPriceType(ModifyPriceType.MPT_QuantityDiscount)
            .build();
        
        PriceContext context = new PriceContext(mockPlayer, commodity, 5, 1000, false, "test");
        
        assertFalse(modifier.isApplicable(context));
    }
    
    @Test
    void testApplicableWithValidConfiguration() {
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 5, 1000, false, "test");
        
        assertTrue(modifier.isApplicable(context));
    }
    
    @Test
    void testQuantityDiscountTier1() {
        // 测试购买1-2件的折扣
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 2, 1000, false, "test");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertTrue(result.isPriceChanged());
        assertEquals(950, result.getModifiedPrice()); // 95折
        assertTrue(result.getReason().contains("数量折扣"));
        assertTrue(result.getReason().contains("5%折扣"));
    }
    
    @Test
    void testQuantityDiscountTier2() {
        // 测试购买3-5件的折扣
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 4, 1000, false, "test");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertTrue(result.isPriceChanged());
        assertEquals(900, result.getModifiedPrice()); // 90折
        assertTrue(result.getReason().contains("数量折扣"));
        assertTrue(result.getReason().contains("10%折扣"));
    }
    
    @Test
    void testQuantityDiscountTier3() {
        // 测试购买6件以上的折扣
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 10, 1000, false, "test");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertTrue(result.isPriceChanged());
        assertEquals(850, result.getModifiedPrice()); // 85折
        assertTrue(result.getReason().contains("数量折扣"));
        assertTrue(result.getReason().contains("15%折扣"));
    }
    
    @Test
    void testFixedPriceDiscount() {
        // 创建有固定价格的商品
        QuantityDiscountTier fixedPriceTier = QuantityDiscountTier.newBuilder()
            .setMinQuantity(10)
            .setMaxQuantity(0) // 无上限
            .setFixedPrice(500) // 固定价格500
            .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
            .addTiers(fixedPriceTier)
            .build();
        
        MallCommodity commodity = MallCommodity.newBuilder()
            .setCommodityId(12345)
            .setEnableModifyPrice(true)
            .setModifyPriceType(ModifyPriceType.MPT_QuantityDiscount)
            .setQuantityDiscountParams(params)
            .build();
        
        PriceContext context = new PriceContext(mockPlayer, commodity, 15, 1000, false, "test");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertTrue(result.isPriceChanged());
        assertEquals(500, result.getModifiedPrice()); // 固定价格500
        assertTrue(result.getReason().contains("固定价格500"));
    }
    
    @Test
    void testNoApplicableTier() {
        // 测试没有适用阶梯的情况（购买数量太少）
        PriceContext context = new PriceContext(mockPlayer, mockCommodity, 1, 1000, false, "test");
        
        // 由于我们的测试数据最小数量是2，购买1件应该没有折扣
        // 但是isApplicable会返回true，因为buyNum > 0
        // modifyPrice会返回noChange
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertFalse(result.isPriceChanged());
        assertEquals(1000, result.getModifiedPrice());
    }
    
    @Test
    void testZeroPriceProtection() {
        // 测试价格保护（确保价格不为负数）
        QuantityDiscountTier extremeTier = QuantityDiscountTier.newBuilder()
            .setMinQuantity(1)
            .setMaxQuantity(0)
            .setDiscountRate(0.0) // 100%折扣
            .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
            .addTiers(extremeTier)
            .build();
        
        MallCommodity commodity = MallCommodity.newBuilder()
            .setCommodityId(12345)
            .setEnableModifyPrice(true)
            .setModifyPriceType(ModifyPriceType.MPT_QuantityDiscount)
            .setQuantityDiscountParams(params)
            .build();
        
        PriceContext context = new PriceContext(mockPlayer, commodity, 5, 1000, false, "test");
        
        PriceModificationResult result = modifier.modifyPrice(context, 1000);
        
        assertTrue(result.isPriceChanged());
        assertEquals(0, result.getModifiedPrice()); // 价格为0，不为负数
    }
    
    /**
     * 创建模拟商品配置
     */
    private MallCommodity createMockCommodity() {
        // 创建数量折扣阶梯
        QuantityDiscountTier tier1 = QuantityDiscountTier.newBuilder()
            .setMinQuantity(2)
            .setMaxQuantity(2)
            .setDiscountRate(0.95) // 95折
            .build();
        
        QuantityDiscountTier tier2 = QuantityDiscountTier.newBuilder()
            .setMinQuantity(3)
            .setMaxQuantity(5)
            .setDiscountRate(0.90) // 90折
            .build();
        
        QuantityDiscountTier tier3 = QuantityDiscountTier.newBuilder()
            .setMinQuantity(6)
            .setMaxQuantity(0) // 无上限
            .setDiscountRate(0.85) // 85折
            .build();
        
        QuantityDiscountParams params = QuantityDiscountParams.newBuilder()
            .addAllTiers(Arrays.asList(tier1, tier2, tier3))
            .build();
        
        return MallCommodity.newBuilder()
            .setCommodityId(12345)
            .setPrice(1000)
            .setEnableModifyPrice(true)
            .setModifyPriceType(ModifyPriceType.MPT_QuantityDiscount)
            .setQuantityDiscountParams(params)
            .build();
    }
}
