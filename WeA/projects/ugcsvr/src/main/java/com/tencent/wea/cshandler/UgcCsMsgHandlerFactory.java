// generated by tools, DO NOT MODIFY ANYTHING
package com.tencent.wea.cshandler;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import com.tencent.wea.cshandler.handler.*;
import com.tencent.wea.protocol.*;

import java.util.HashMap;
import java.util.Map;

/**
 * maintain the msg handlers
 */
public class UgcCsMsgHandlerFactory {
    private static final Map<Integer, AbstractUsClientRequestHandler> MSG_HANDLERS = new HashMap<>();
    private static final Map<Integer, Parser<? extends Message>> MSG_ID_PARSERS_FORWARD = new HashMap<>();

     
    public static final int MSG_TYPE_UGCFORWARDTEST_C2S_MSG = 2111;
     
    public static final int MSG_TYPE_UGCAPPRECIATEGETMAPS_C2S_MSG = 2284;
     
    public static final int MSG_TYPE_UGCAPPRECIATEMAP_C2S_MSG = 2286;
     
    public static final int MSG_TYPE_UGCMATCHLOBBYDETAILGETONE_C2S_MSG = 2431;
     
    public static final int MSG_TYPE_UGCAPPLYCOVERURL_C2S_MSG = 2652;
     
    public static final int MSG_TYPE_UGCMAPEXTRACONFIGEDIT_C2S_MSG = 3778;
     
    public static final int MSG_TYPE_UGCMAPEXTRACONFIGGET_C2S_MSG = 3780;
     
    public static final int MSG_TYPE_UGCGETCREATORBADGE_C2S_MSG = 4228;
     
    public static final int MSG_TYPE_UGCGETCREATORHOMEPAGE_C2S_MSG = 4233;
     
    public static final int MSG_TYPE_UGCMATCHLOBBYDETAILEXMULTI_C2S_MSG = 4238;
     
    public static final int MSG_TYPE_UGCMATCHLOBBYDETAILEXCHANGE_C2S_MSG = 4240;
     
    public static final int MSG_TYPE_UGCMATCHLOBBYSUMMERY_C2S_MSG = 4822;
     
    public static final int MSG_TYPE_UGCMATCHLOBBYSUMMERYSPECIFIED_C2S_MSG = 4830;

    static {
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCFORWARDTEST_C2S_MSG, CsUgcForward.UgcForwardTest_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCAPPRECIATEGETMAPS_C2S_MSG, CsUgcAppreciate.UgcAppreciateGetMaps_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCAPPRECIATEMAP_C2S_MSG, CsUgcAppreciate.UgcAppreciateMap_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCMATCHLOBBYDETAILGETONE_C2S_MSG, CsUgc.UgcMatchLobbyDetailGetOne_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCAPPLYCOVERURL_C2S_MSG, CsUgc.UgcApplyCoverUrl_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCMAPEXTRACONFIGEDIT_C2S_MSG, CsUgc.UgcMapExtraConfigEdit_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCMAPEXTRACONFIGGET_C2S_MSG, CsUgc.UgcMapExtraConfigGet_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCGETCREATORBADGE_C2S_MSG, CsUgc.UgcGetCreatorBadge_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCGETCREATORHOMEPAGE_C2S_MSG, CsUgc.UgcGetCreatorHomePage_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCMATCHLOBBYDETAILEXMULTI_C2S_MSG, CsUgc.UgcMatchLobbyDetailExMulti_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCMATCHLOBBYDETAILEXCHANGE_C2S_MSG, CsUgc.UgcMatchLobbyDetailExChange_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCMATCHLOBBYSUMMERY_C2S_MSG, CsUgc.UgcMatchLobbySummery_C2S_Msg.parser());
     
           MSG_ID_PARSERS_FORWARD.put(MSG_TYPE_UGCMATCHLOBBYSUMMERYSPECIFIED_C2S_MSG, CsUgc.UgcMatchLobbySummerySpecified_C2S_Msg.parser());

    
           MSG_HANDLERS.put(MSG_TYPE_UGCFORWARDTEST_C2S_MSG, new Ugc_UgcForwardTestMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCAPPRECIATEGETMAPS_C2S_MSG, new Ugc_UgcAppreciateGetMapsMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCAPPRECIATEMAP_C2S_MSG, new Ugc_UgcAppreciateMapMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCMATCHLOBBYDETAILGETONE_C2S_MSG, new Ugc_UgcMatchLobbyDetailGetOneMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCAPPLYCOVERURL_C2S_MSG, new Ugc_UgcApplyCoverUrlMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCMAPEXTRACONFIGEDIT_C2S_MSG, new Ugc_UgcMapExtraConfigEditMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCMAPEXTRACONFIGGET_C2S_MSG, new Ugc_UgcMapExtraConfigGetMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCGETCREATORBADGE_C2S_MSG, new Ugc_UgcGetCreatorBadgeMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCGETCREATORHOMEPAGE_C2S_MSG, new Ugc_UgcGetCreatorHomePageMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCMATCHLOBBYDETAILEXMULTI_C2S_MSG, new Ugc_UgcMatchLobbyDetailExMultiMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCMATCHLOBBYDETAILEXCHANGE_C2S_MSG, new Ugc_UgcMatchLobbyDetailExChangeMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCMATCHLOBBYSUMMERY_C2S_MSG, new Ugc_UgcMatchLobbySummeryMsgHandler());
    
           MSG_HANDLERS.put(MSG_TYPE_UGCMATCHLOBBYSUMMERYSPECIFIED_C2S_MSG, new Ugc_UgcMatchLobbySummerySpecifiedMsgHandler());
    }

    // message serial function
    public static Message parseFrom(int type, byte[] data, int offset, int length) throws InvalidProtocolBufferException {
        Parser<? extends Message> parser = MSG_ID_PARSERS_FORWARD.get(type);
        if (parser == null) {
            throw new IllegalArgumentException(String.format("invalid msg type %d", type));
        }
        return parser.parseFrom(data, offset, length);
    }

    /**
     *  get msg handler
     * @param type
     * @return
     */
    public static AbstractUsClientRequestHandler getMsgHandler(int type) {
           return MSG_HANDLERS.get(type);
    }
}
