

@CookAttr +root # 农场数据
   cookId               int64                     1    +brief       #
   cookBasicInfo        CookBasicInfo             2    +farmOwner   # 餐厅核心信息
   cookVisitorInfo      XiaoWoVisitorInfo         3                 # 餐厅访客信息，这里复用了xiaowo的结构，很遗憾，名字不容易改了
   cookDsInfo           XiaoWoDsInfo              4    +nocs        # 餐厅DS信息，这里复用了xiaowo的结构，很遗憾，名字不容易改了
   cookBusinessInfo     CookBusinessInfo          5    +farmOwner   # 餐厅营业信息
   roomInfo             map<FarmRoom>             6                 # 餐厅房间信息
   itemInfo             map<FarmRoomItem>         7                 # 房间物品信息
   cookSafeInfo         CookSafeInfo              12   +farmOwner   # 农场餐厅安全信息
   currentRoomId        int64                     13                # 餐厅当前房间id
   customerInfo         CookCustomerInfo          15                # 餐厅顾客信息 (是否有多房间问题：顾客、员工、餐桌等都要重新考虑)
   sysEmployeeInfo      CookSysEmployeeInfo       17   +farmOwner   # 系统员工信息
   hiredInfo            CookHiredInfo             18   +farmOwner   # 主人的雇佣信息
   dishLevelInfo        map<DishLevel>            19   +farmOwner   # 菜品等级信息
   cookCommentInfo      CookCommentInfo           21                # 点评信息
   visitantInfo         CookVisitantInfo          22   +farmOwner   # 贵宾预约等信息
   offlineIncomeInfo    CookOfflineIncomeInfo     23   +farmOwner   # 离线收益信息
   cookClientCloud      map<FarmClientCache>      24   +farmOwner   # 农场的客户端KV存储
   floatingScreenInfo   CookFloatingScreenInfo    25                # 餐厅流动显示屏信息
   cookInteractInfo     HouseInteractInfo         26                # 家具交互信息
   layoutInfo           map<LayoutInfo>           27   +farmOwner   # 方案简要信息

@CookBasicInfo
   name                string   1
   createTime          int64    2
   instruction         string   3
   lastRefreshTimeMs   int64    7    +nocs   # 上次刷新时间
   level               int32    9            # 餐厅等级
   likeNum             int64    10           # 餐厅点赞数
   leaveTimeMs         int64    11           # 上次离开餐厅时间

@CookBusinessInfo
   state                    int32               1           # 餐厅状态  0休息中  1营业中 2休息中 3加班中 参考CookStateType
   openTime                 int64               2           # 营业开始时间
   menu                     Menu                3           # 开店菜单
   effectiveCustomerFlow    double              5           # 有效客流量 离线结算使用
   coinGain                 int64               6           # 本次开店收益的农场币
   farmExpGain              int64               7           # 本次开店收益的农场经验
   likeGain                 int64               8           # 本次开店收益的点赞数
   customerFlow             int32               9           # 餐厅客流量 给客户端看的
   lastCalculateTime        int64               10  +nocs   # 上次计算状态的时间，用来维持状态机
   commentScoreHistory      map<KvFI>           11  +nocs   # 本次开店的评分记录 流水用 key是分数 value是数量
   serveVisitantNum         int32               12  +nocs   # 本次开店招待贵宾数量

# 主人的雇主信息
@CookEmployer
    UID         int64       1 +key           # 雇主的uid
    startTime   int64       2                # 被雇佣的时间（秒）
    income      int64       3 +noFarmVisitor # 当前累计收入

# 主人的雇佣信息
@CookHiredInfo
    employer            map<CookEmployer>                1                # 主人当前被哪些人雇佣
    hiredHistory        map<CookHiredHistoryItem>        2 +noFarmVisitor # 主人被雇佣的历史记录
    hireFriendsHistory  map<CookHireFriendsHistoryItem>  3 +noFarmVisitor # 主人雇佣好友的历史记录
    hisID               int32                            4 +nocs          # 历史记录id累加生成器


# 主人被雇佣的历史记录条目
@CookHiredHistoryItem
    id          int32       1 +key  # 唯一的历史id，无意义，因为会被多个好友同时雇佣，因此没有用startTime当key
    UID         int64       2   # 雇主的uid
    startTime   int64       3   # 被雇佣的时间（秒）
    endTime     int64       4   # 结束时间（秒）
    income      int64       5   # 被雇佣收入

# 主人雇佣他人的历史记录条目
@CookHireFriendsHistoryItem
    startTime   int64       1 +key  # 雇佣的时间（秒）
    UID         int64       2   # 好友的uid
    endTime     int64       3   # 结束时间（秒）
    expend      int64       4   # 雇佣支付的报酬


@CookPublicInfo
   cookVisitorCount             int32                       1      # 餐厅人数
   roomCount                    int32                       2      # 餐厅房间数
   cookSafeInfo                 CookSafeInfo                3      # 餐厅安全信息
   # cookCanStealTimeInfo         map<CanStealTime>           4      # 废弃

@CookCustomerInfo
    idAdder           int64                 1               # id累加器
    customerList      map<CookCustomer>     2               # 餐厅顾客信息 (是否有多房间问题：顾客、员工、餐桌等都要重新考虑)
    lastCustomerArriveTimeMs    int64       3               # 最后一个顾客到达时间
    avatarCdMap       map<KvIL>             4  +nocs        # avatarId->上次出现时间

@CookCustomer
   id            int64   1   +key
   arriveTimeMs  int64   2          # 到店时间 是指这个人在客户端刷出来的时间。 毫秒
   type          int32   3          # 暴击类型 FarmCookCustomerType
   avatarId      int32   4
   willComment   bool    5          # 这个顾客会不会点评
   food          int32   6          # 预订的菜
   name          string  7

# 系统雇员相关信息
@CookSysEmployeeInfo
    employeeList              map<CookSysEmployee>          1 # 系统雇员列表
    recruitmentMarket         CookRecruitmentMarket         2 # 招聘市场
    initOriginEmployeeFlag    int32                         3 # 原始系统员工Flag
    recycleEmployeeList       map<CookSysEmployee>          4 # 员工垃圾桶

@CookEmployeeAbility
    exclusive           int64               1 # 专属
    speed               int64               2 # 速度
    charm               int64               3 # 魅力

# 员工通用信息
@CookEmployeeBasicInfo
    name        string      1        # 名字
    state       int32       2        # 状态(FarmCookEmployeeState)
    createTime  int64       3        # 创建时间
    pos         string      4        # 位置
    recycleTime int64       5        # 回收时间

@CookEmployeeJobInfo
    job                 int32                   1           # 职位
    level               int32                   2           # 等级
    quality             int32                   3           # 品质
    ability             CookEmployeeAbility     4           # 能力值

# 系统雇员
@CookSysEmployee
    id                  int64                   1   +key    # 员工uid
    avatarId            int32                   2           # AvatarID
    basicInfo           CookEmployeeBasicInfo   3           # 通用信息
    jobInfo             CookEmployeeJobInfo     4           # 职业信息

# 招聘市场
@CookRecruitmentMarketRefreshProtect
    quality             int32               1    +key   # 品质
    value               int32               2           # 保底值
    needValue           int32               3           # 所需保底值
    exclusive           int32               4           # 专属属性保底
    speed               int32               5           # 速度属性保底
    charm               int32               6           # 魅力属性保底

@CookRecruitmentMarket
    candidates                 map<CookCandidate>                           1               # 应聘者列表
    todayRefreshCount          int32                                        2               # 今天刷新次数
    qualityProtect             map<CookRecruitmentMarketRefreshProtect>     3               # 保底
    isInit                     bool                                         4       +nocs   # 是否初始化过
    highCandidates             map<CookCandidate>                           5               # 高级应聘者列表
    isHighCandidateInit        bool                                         6       +nocs   # 高级应聘市场是否初始化过

@CookCandidate
    idx                 int32                   1   +key    # 人才市场idx
    avatarId            int32                   2           # AvatarID
    name                string                  3           # 名字
    jobInfo             CookEmployeeJobInfo     4           # 职业信息
    price               int64                   5           # 价格
    isHired             bool                    6           # 是否已雇佣

# 好友雇员信息
@CookFriendEmployeeInfo
    staff   map<CookGreeter>  1

# 迎宾员
@CookGreeter
   id           int32                   1   +key   # 员工id
   basicInfo    CookEmployeeBasicInfo   2          # 通用信息
   friendUID    int64                   3          # 员工来自某个好友（雇佣好友功能）
   incomeBuff   int32                   4          # 雇佣时的收入加成
   wages        int64                   5          # 好友的工资


@CookSafeInfo
    putDown                       CookPunish         1
    floatingScreenForbidden       CookPunish         2      # 流动显示屏
    commentReplyForbidden         CookPunish         3      # 点评回复

@CookPunish
    enable              bool               1
    endTime             int64              2
    reason              string             3

# 菜品等级
@DishLevel
    type               int32               1   +key    # id
    level              int32               2           # 等级
    exp                int64               3           # 经验

# 当前备菜
@Menu
    dishes             set<int32>      1          # 当前备菜

# 点评信息
@CookCommentInfo
    totalNumber                             int32                             1          # 点评总数
    newNumber                               int32                             2          # 新点评数量
    lastPullId                              int64                             3          # 上次拉取的id
    lastOnlineCommentTimeMs                 int64                             4          # 上次在线点评时间
    lastOfflineCommentTimeMs                int64                             5          # 上次离线点评时间
    cookCommentScoreInfo                    CookCommentScoreInfo              6          # 评分信息
    lastUnCommentCustomerArriveTimeMs       int64                             7          # 最后一个待点评顾客的到达时间
    unCommentCustomers                      set<int64>                        8  +nocs   # 待点评顾客的id
    dayRecord                               map<CookCommentReplyRecord>       9          # 每日回复发送记录
    dayCommentNumber                        int32                             10         # 每日已生成的顾客中需要点评的数量
    commentAndReplyNumber                   int32                             11         # 点评和回复总数(大池子)
    commentWithPlayerReplyNumber            int32                             12         # 有玩家回复的点评及其回复总数(小池子)
    commentNumberInfo                       map<CookCommentNumberInfo>        13         # 每条点评的信息
    commentPhotoNameIdPoll                  string                            14         # 点评照片id池(1可授权 0已授权未使用 2已使用)
    photoNameIdInfo                         map<CookCommentPhotoNameIdInfo>   15         # 点评照片id信息

# 评分信息
@CookCommentScoreInfo
    number                    int32                   1          # 有效点评数量
    totalScore                float                   2          # 总分值
    avgScore                  float                   3          # 平均分
    lastAvgScoreWhenExit      float                   4          # 主人上次离开餐厅时候的平均分。

# 点评回复每日记录
@CookCommentReplyRecord
    cookId                    int64                          1 +key     # 回复的玩家uid
    recordCount               int32                          2          # 回复的次数

# 点评回复数量信息
@CookCommentNumberInfo
    commentId                 int64                          1 +key     # 点评id
    playerReplyNum            int32                          2          # 玩家回复数量
    customerReplyNum          int32                          3          # 顾客回复数量
    score                     float                          4          # 点评分值
    photoNameId               int32                          5          # 照片id（放在属性里，防止redis删成功但是返回超时问题）

# 点评照片cosId信息
@CookCommentPhotoNameIdInfo
    id                        int32                          1 +key     # cosId
    lastAuthTimeMs            int64                          2          # 上次授权时间

@CookVisitantInfo
    visitants                map<CookVisitant>               1           # 所有等待中的贵宾
    prebookInfo              CookVisitantPrebookInfo         2           # 贵宾预约相关
    stealInfo                CookVisitantStealInfo           3           # 贵宾偷取相关
    prebookList              set<int32>                      4           # 贵宾可预约列表（根据等级刷新）
    currentGroupInfo         CookVisitantCurrentGroupInfo    5           # 贵宾当前组相关
    lastPrebookGroupId       int32                           6           # 上一次预约的组id

# 餐厅贵宾信息
@CookVisitant
    uniqueId                    int64               1   +key    # 唯一ID
    groupId                     int32               3           # 贵宾组ID
    source                      int32               4           # 来源 1预约2偷取
    protectEndTimeMs            int64               6           # 保护结束时间戳（毫秒）
    state                       int32               7           # 状态枚举 FarmCookVisitantStateEnum
    gainCoin                    int64               8           # 金币收益
    customer                    CookCustomer        9           ####继承顾客属性####
    serveTimeMs                 int64               10          # 接待时间（毫秒）
    ignoredUIDs                 set<int64>          11          # 忽略提醒的玩家
    quality                     int32               12          # 暴击类型 1普通2小暴击3大暴击

# 餐厅贵宾预约相关
@CookVisitantPrebookInfo
    groupId                     int32               1           # 预约中的贵宾组配置id
    prebookTimeMs               int64               2           # 预约时间戳（毫秒）
    arriveTimeMs                int64               3           # 到达时间戳（毫秒）
    protectEndTimeMs            int64               4           # 保护结束时间戳（毫秒）
    visitantNum                 int32               5           # 预计贵宾数量
    gainCoin                    int64               6           # 预计每位贵宾的农场币收益

# 餐厅贵宾偷取相关
@CookVisitantStealInfo
    lastStealTime               map<KvLL>           1           # 玩家uid->上次偷取时间

# 餐厅贵宾当前组相关
@CookVisitantCurrentGroupInfo
    currentGroupId              int32               1           # 当前组的组ID
    currentGroupArriveTimeMs    int64               2           # 当前组的到达时间
    currentGroupServedNum       int32               3           # 当前组的接待贵宾数量
    currentGroupStolenNum       int32               4           # 当前组的被偷贵宾数量

# 餐厅离线收益相关
@CookOfflineIncomeInfo
    coin                int64                       2           # 存储的农场币
    farmExp             int64                       3           # 存储的农场经验
    like                int64                       4           # 存储的点赞数
    storageBenefitTime  int64                       5           # 存储的离线收益所花的时间

# 餐厅流动显示屏信息
@CookFloatingScreenInfo
    content             string                      1           # 显示屏自定义内容

