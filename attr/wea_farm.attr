# 养殖物
@Crop
    confId                  int32                                           1          # 类型ID
    lastUpdateTime          int64                                           2          # 生命周期 - 上次数据更新时间
    growValue               int32                                           3          # 生命周期 - 生长值
    careValue               int32                                           4          # 生命周期 - 照料值
    ripeQuality             com.tencent.wea.xlsRes.FarmCropRipeQuality      5          # 产量 - 成熟质量
    totalItems              map<HarvestItem>                                6          # 产量 - 全部收获物
    leftItems               map<HarvestItem>                                7          # 产量 - 剩余收获物
    stolenCount             int32                                           8          # 偷菜 - 被偷次数，成熟后祈愿暴击重置
    stolenProtect           bool                                            9          # 偷菜 - 是否开启被偷保护
    stolenUIDs              set<int64>                                      10         # 偷菜 - 被哪些人偷取过，成熟后祈愿暴击重置
    fertilizedUIDs          set<int64>                                      11         # 祈愿 - 被哪些人祈愿过，成熟后祈愿暴击重置
    fertilizedCount         int32                                           12         # 祈愿 - 祈愿次数
    totalStolenCount        int32                                           13         # 偷菜 - 累计被偷次数
    cropMonthlyPassInfo     CropMonthlyPassInfo                             14         # 月卡相关
    cropClientInfo          CropClientInfo                                  15         # 客户端相关
    ignoredUIDs             set<int64>                                      16  +nocs  # 取消提醒 - 被哪些人取消提醒过，成熟后施肥暴击重置
    weatherInfo             CropWeatherInfo                                 17         # 天气相关
    specialCropInfo         SpecialCropInfo                                 18         # 特殊养殖物相关
    ripeQualityMul          float                                           19         # 产量 - 暴击倍率
    normalQualityItems      map<HarvestItem>                                20         # 祈愿 - 记录普通成熟的收获物数量 用于计算祈愿丰收和大丰收
    magicInfo               CropMagicInfo                                   21         # 仙术相关
    protectEndTime          int64                                           22         # 保护结束时间(综合下雨和仙术的保护)

@CropMagicInfo
    recovered                                   bool                        1          # 被仙术恢复过产量
    speedUpProtectEndTime                       int64                       2          # 仙术加速成熟保护结束时间
    delayAddMaxGrowValue                        int32                       3          # 仙术时间延迟增加的最大生长值
    delayAddCanEncourageNeedGrowValue           int32                       4          # 仙术时间延迟增加的可照料所需生长值


@SpecialCropInfo
    vileplumeInfo           VileplumeInfo                                   1          # 霸王花相关


@VileplumeInfo
    level                   int32                                           1          # 等级（霸王花）
    exp                     int64                                           2          # 经验（霸王花）
    protectEndTime          int64                                           3          # 保护结束时间（霸王花）
    eatenFlag               bool                                            4          # 是否被霸王花吃过（其他作物）
    eatCoinNumTotal         int64                                           5          # 吞食金币总量（霸王花）
    eatCoinNumUnconverted   int64                                           6          # 吞食金币未转化量（霸王花）
    eatenStartTimeMs        int64                                           7          # 开始被吞食的毫秒时间戳（其他作物）
    eatenItems              map<HarvestItem>                                8          # 被吞食的东西（其他作物）
    protectCanceled         bool                                            9          # 保护时间被取消（霸王花）
    eatExpTotal             int64                                           10         # 吞食经验总量（霸王花）


@CropWeatherInfo
    rainEffectEndTime       int64                                           1          # 下雨影响结束时间
    rainProtectEndTime      int64                                           2          # 下雨保护结束时间
    rainProtectCanceled     bool                                            3          # 下雨保护被取消

# 客户端信息
@CropClientInfo
    feedFlag                bool                                            1          # 喂食标记


# 养殖物月卡信息
@CropMonthlyPassInfo
    effective               bool                                            1          # 生效

# 收获物
@HarvestItem
    itemId             int32                1   +key    # 道具配置ID
    itemNum            int64                2           # 道具数量

# 物品
@FarmItem
    uuid               int64                1   +key    # 道具UUID
    itemId             int32                2           # 道具配置ID
    itemNum            int64                3           # 道具数量
    para               int64                4           # 中间参数

# 建筑
@Building
    confId             int32                1   +key
    level              int32                2
    firstFlag          bool                 3
    skinId             int32                4   # 生效的皮肤ID
    skinForm           int32                5   # 生效的皮肤形态
    livingVillagerId   int32                6   # 当前入驻的村民ID

@BuildingSkin
    id                 int32                1   +key
    expireMs           int64                2           # 过期时间

# 地格
@Grid
    id                 int32                1   +key
    level              int32                2   +farmOwner
    crop               Crop                 3           # 地格里的养殖物
    lastOpType         int32                4           # 上次地块操作类型
    lastOperator       int64                5           # 上次地块操作者
    lastOpTimeMs       int64                6           # 上次地块操作时间(毫秒)
    lastOpSource       int32                7           # 上次地块操作来源
    lastOpAppoint      int32                8           # 上次地块操作指定（1为指定 0为附属）
    expLevel           int32                9   +farmOwner        # 经验强化等级

# 背包
@FarmItemInfo
    item               map<FarmItem>        1           # 背包中的物品
    money              map<FarmMoney>       2           # 背包中的货币

@FarmMoney
    itemId             int32                1   +key    # 货币ID
    itemNum            int64                2           # 货币数量

@StealingRecord
    friendId           int64                1   +key    # 最近来偷菜的好友ID
    updateTime         int64                2           # 偷菜时间

@EvictRecord
    friendId           int64                1   +key    # 最近驱逐的好友ID
    updateTime         int64                2           # 驱逐时间
    reason             int32                3           # 驱逐原因

@BadGuysRedDot
    friendId           int64                1   +key    # 好友ID
    gotTime            int64                2           # 最近拉取时间

@DoGoodersRedDot
    friendId           int64                1   +key    # 好友ID
    gotTime            int64                2           # 最近拉取时间

@PetEvictRedDot
    friendId           int64                1   +key    # 好友ID
    gotTime            int64                2           # 最近拉取时间

@PetFeedRedDot
    friendId           int64                1   +key    # 好友ID
    gotTime            int64                2           # 最近拉取时间

@BlockFertilize
    friendId           int64                1   +key    # 好友ID
    blockTime          int64                2           # 设置祈福屏蔽时间

@BlockCanSteal
    friendId           int64                1   +key    # 好友ID
    blockTime          int64                2           # 设置屏蔽时间

@FarmStealingInfo
    stealingRecord           map<StealingRecord>   1           # 最近被偷信息
    evictRecord              map<EvictRecord>      2           # 最近驱逐信息
    badGuysRedDot            map<BadGuysRedDot>    3           # 坏人偷取详情拉取时间
    strangerRecommendTime    int64                 4           # 上次进入陌生人推荐列表时间
    doGoodersRedDot          map<DoGoodersRedDot>  5           # 好人施肥详情拉取时间
    todayBeFertilizedCount   int32                 6           # 今日被施肥次数
    lastBeFertilizedTime     int64                 7           # 上次被施肥时间
    petEvictRedDot           map<PetEvictRedDot>   8           # 宠物驱逐详情拉取时间
    blockFertilize           map<BlockFertilize>   9           # 祈愿屏蔽
    petFeedRedDot            map<PetFeedRedDot>    10          # 宠物喂食详情拉取时间
    blockCanSteal            map<BlockCanSteal>    11          # 可偷提醒屏蔽
    cloudTaxRedDotTime       int64                 13          # 云游税详情拉取时间
    canFertilizedCategory    int32                 14          # 哪些类型可以被祈福 0无限制 -1不允许 其它参见FarmCropCategory


# 参考common.HeadFrame的使用
@FarmGiftHeadFrame
  dressUpType   int32   1
  itemUUID      int64   2
  itemId        int32   3


@FarmGift
    giftID64    int64   1  +key             # 礼物唯一ID
    itemID      int32   2                   # 道具ID
    itemCount   int64   3                   # 道具数量
    uid         int64   4                   # 送礼者uid
    msg         string  5                   # 留言
    time        int64   6                   # 时间
    billNo      string  7 +nocs             # 流水号
    pos         string  8                   # 坐标
    msgBak      string  9 +nocs             # 当留言被屏蔽，msg会被替换为****，原留言在这里备份
    openId      string  10                  # 送礼者openid
    name        string  11                  # 送礼者名字
    face        string  12                  # 送礼者头像
    headFrame FarmGiftHeadFrame 13          # 送礼者头像框
    skinID      int32   14                  # 皮肤道具ID
    expirtTime  int64   15                  # 过期时间 会删除
    bp          string  16                  # 指定bp 活动礼物才使用
    giftType    int32   17                  # 礼物盒类型 common.FarmGiftType
    critical    int32   18                  # 暴击类型 common.FarmGiftCritical
    ratio       float   19                  # 倍数


@FarmGiftReserveFlag
    billNo  string 1 +key
    addTime int64   2

@FarmSendHistory
    target int64 1 +key # 送给谁
    giftCount int64 2 # 几次

@FarmGiftRecord
    index       int64     1 +key  # 客户端可以按这个排序
    pickTime    int64     2       # 拾取时间戳（秒）
    gift        FarmGift  3       # 礼物内容

# https://doc.weixin.qq.com/sheet/e3_AGcAcwacAAYTwQUck1AS56VK06QLK
@FarmGiftList
    gifts                   map<FarmGift>               1                  # 收到的礼物
    lastRecvTime            int64                       5                  # 最后一次收到礼物时间
    recvTimes               int32                       6                  # 收礼次数（lastRecvTime早于上一个8点，则recvTimes无论是多少，都显示成0）
    lastSendGift            int64                       2                  # 最后一次送礼物的时间
    sendTimes               int32                       3                  # 送礼物的次数（如lastSendGift早于上一个8点，则sendTimes无论是多少，都显示成0）
    sendUids                int64[]                     4                  # 送礼记录（如lastSendGift早于上一个8点，则一律认为sendUids是空的）
    giftReserved            map<FarmGiftReserveFlag>    9  +nocs           # 礼物占位
    history                 map<FarmSendHistory>        10                 # 今日送礼记录（如lastSendGift早于上一个8点，则一律认为history是空的）
    lastSendTimeBak         int64                       8  +nocs           # 备份的最后一次送礼时间
    lastActivityCheckTime   int64                       11 +nocs           # 最后一次检查时间 （活动）
    record                  map<FarmGiftRecord>         12                 # 礼物收取记录
    giftPickIndex           int64                       13 +nocs           # 礼物收取计数器

# 养殖物等级
@CropLevel
    type               int32                1   +key    # id
    level              int32                2           # 等级
    exp                int64                3           # 经验
    levelRewardGot     map<KvII>            4           # 领取过哪些等级的奖励

 # 养殖物祈福保底
 @CropFertilizeGuaranteed
     type                    int32         1   +key    # id
     noBtoSTimes    int32         2           # 多少次没出金色
     noBtoGTimes    int32         3           # 多少次没出彩色
     noStoGTimes    int32         4           # 多少次没由金变彩


# 建筑
@FarmBuildingInfo
    buildings          map<Building>        1           # 建筑
    buildingSkins      map<BuildingSkin>    2           # 拥有的建筑皮肤

# 建筑
@FarmOwnerInfo
    isOnline           bool                 1

# 事件数据
@FarmCustomData
    dataKey         string              1 +key
    data            map<FarmEventValue> 2 # 数据

# 事件信息，来自gamesvr同步
@FarmEventSync
    events          map<FarmEvent>           1       # 当前触发中的事件
    globalCD        int64                    2 +nocs # 事件的全局触发CD
    farmTimer       FarmTimer                3 +nocs # 事件定时器
    evtDelCloud     int32[]                  4 +nocs # 近期被删除的自动结束事件（云游商人）

@Seller
    uid     int64   1 +key
    money   int64   2

@CloudTax
    tax              string      1 +nocs # 本次出现收的总税
    taxLeftStr       string      2 +nocs
    taxLeft          int64       3       # 主人还没收走的税
    seller           map<Seller> 4       # 卖家记录

@CloudNpc
    eid              int32       1 +key
    series           int32       2        # 事件系列
    evtID            int32       3        # 事件ID
    itemID           int32       4        # 所需道具类型
    itemNum          int64       5        # 所需道具总量
    itemGot          int64       6        # 已收集道具数量
    endTimeMs        int64       7        # 结束时间
    ratio            float       8        # 倍率
    itemNumPerPlayer int64       12       # 玩家单次最大量
    cloudTax         CloudTax    13       # 税务信息




# 云游商人
@FarmCloudmarket
    cloudNpc    map<CloudNpc>           1 # 云游商人


@FarmHotSpringVisitor
    uid           int64   1   +key  # 访客uid

@HSBuffHistory
    farmID  int64 1 +key
    timeMs  int64[] 2

@FarmHotBuffHistory
    hotBuffMap  map<HSBuffHistory> 1

@FarmHotSpringBuffSource
    uid         int64   1                  # 来源uid
    name        string  2                  # 来源名字


@FarmHotSpring
    enterTime       int64                       1                # 我最后一次进入温泉的时间
    expRewardTimes  int32                       2                # 我本周已经获得几次经验奖励
    lastGetBuffTime int64                       3                # 我最后一次获得buff的时间
    visitors        map<FarmHotSpringVisitor>   4                # 当前farm里进入温泉的访客
    buffs           int32[]                     5                # 当前farm提供哪些buff
    curHotSpringUid int64                       7                # 我在谁家的温泉
    lastRefreshMs   int64                       8                # 温泉最后一次刷新时间
    hotType         int32                       9                # 温泉类型
    grade           int32                       10               # 品质：common.HotSpringGrade
    curBuffs        int32[]                     11               # 上次从温泉获得的buff
    hotBuffHistory  FarmHotBuffHistory          12 +nocs         # 温泉buff历史，每天刷掉
    resetTime       int64                       13               # 温泉经验重置时间
    nextResetTime   int64                       14               # 温泉经验下次重置时间，当客户端的serverNow >= nextResetTime，则认为可以重新获得经验
    nextBuffResetTime   int64                   15               # 温泉buff下次重置时间，当客户端的serverNow >= nextResetTime，则认为可以重新获得buff
    buffSource      FarmHotSpringBuffSource     16               # 温泉buff来源




@FarmAttr +root # 农场数据
    farmId                             int64                                  1   +brief                        #
    farmBasicInfo                      FarmBasicInfo                          2   +farmOwner                    # 农场核心信息
    FarmVisitorInfo                    XiaoWoVisitorInfo                      3                                 # 农场访客信息，这里复用了xiaowo的结构，很遗憾，名字不容易改了
    FarmDsInfo                         XiaoWoDsInfo                           4   +nocs                         # 农场DS信息，这里复用了xiaowo的结构，很遗憾，名字不容易改了
    farmItemInfo                       FarmItemInfo                           5   +farmOwner,+noFarmVisitor     # 农场背包信息
    mapInfo                            map<Grid>                              6                                 # 已解锁的地格 farmOwner在二级字段
    buildingInfo                       FarmBuildingInfo                       7   +farmOwner                    # 建筑
    cropLevelInfo                      map<CropLevel>                         8   +farmOwner                    # 养殖物等级信息
    farmStealingInfo                   FarmStealingInfo                       9   +farmOwner                    # 农场偷菜信息
    farmOwnerInfo                      FarmOwnerInfo                          10                                # 农场主人信息 （在不在线）
    farmSafeInfo                       FarmSafeInfo                           11  +farmOwner                    # 农场安全信息
    farmClientCloud                    map<FarmClientCache>                   12                                # 农场的客户端KV存储
    farmStatisticInfo                  FarmStatisticInfo                      13                                # 农场统计信息
    liuYanMessageInfo                  FarmLiuYanMessageInfo                  14  +farmOwner                    # 留言板信息
    farmWelcomeInfo                    FarmWelcomeInfo                        15                                # 农场寄语信息
    farmMonthlyPass                    FarmMonthlyPass                        16                                # 同步自player的月卡信息，以player为准
    farmBlockedFriends                 map<FarmBlockedFriend>                 17                                # 农场拉黑好友
    farmFishPoolInfo                   FarmFishPoolInfo                       18   +farmOwner                   # 农场鱼塘信息
    handBookInfo                       map<FarmHandBook>                      19   +farmOwner                   # 作物图鉴
    fishCardLevel                      map<FarmFishCardLevel>                 20   +farmOwner                   # 鱼卡等级
    farmFishBowlInfo                   map<FarmFishBowl>                      21                                # 鱼缸内容
    farmGiftList                       FarmGiftList                           22   +farmOwner                   # 礼物信息
    farmBuffInfo                       FarmBuffInfo                           23   +farmOwner                   # 农场buff
    farmFishingGuaranteed              map<KvII>                              24   +noFarmVisitor               # 废弃
    farmNewPlayerInfo                  FarmNewPlayerInfo                      25                                # 农场新手信息
    #farmFishingGuaranteedNew          map<KvII>                              26   +noFarmVisitor               # 废弃
    farmFishingGuaranteedInfo          FarmFishingGuaranteedInfo              27   +farmOwner,+noFarmVisitor    # 农场钓鱼保底数据
    farmAquarium                       map<FarmAquarium>                      28                                # 水族箱
    farmPetInfo                        FarmPetInfo                            29   +farmOwner                   # 农场宠物信息
    houseItemStatistics                HouseItemStatistics                    30   +farmOwner                   # 小屋家具统计信息
    farmCollection                     FarmCollection                         31   +farmOwner                   # 收藏品信息
    farmSceneDrop                      FarmSceneDrop                          32   +farmOwner                   # 场景掉落
    farmEventSync                      FarmEventSync                          33                                # 同步自gamesvr
    farmWeatherInfo                    FarmWeatherInfo                        34   +farmOwner                   # 农场天气信息
    farmCropStatisticInfo              FarmCropStatisticInfo                  35                                # 农场作物统计信息
    farmVillagerInfo                   FarmVillagerInfo                       37   +farmOwner                   # 农场村民信息
    farmFishingReport                  FarmFishingReport                      39   +farmOwner,+noFarmVisitor    # 钓鱼月报周报
    farmTalentInfo                     FarmTalentInfo                         40   +farmOwner                   # 农场天赋
    taskInfo                           FarmTaskInfo                           41   +farmOwner,+noFarmVisitor    # 农场任务信息
    farmCloudmarket                    FarmCloudmarket                        42                                # 云游商人
    farmTlogRequiredFields             FarmTlogRequiredFields                 43   +nocs                        # 流水公参
    redPacketInfo                      XiaoWoRedPacketInfo                    44                                # 红包信息
    hotSpring                          FarmHotSpring                          45   +farmOwner                   # 温泉
    cropFertilizeGuaranteed            map<CropFertilizeGuaranteed>           47                                # 养殖物祈福保底
    farmMagic                          FarmMagic                              48   +farmOwner                   # 仙术
    farmGodFigure                      FarmGodFigure                          49                                # 农场神像功能
    farmExternalOperateInfo            FarmExternalOperateInfo                50                                # 废弃
    bannedSellItems                    map<BannedItemInfo>                    51   +farmOwner,+noFarmVisitor    # 农场背包物品禁售信息
    farmModuleOpenTimeInfo             FarmModuleOpenTimeInfo                 54   +farmOwner,+noFarmVisitor    # 农场模块开放时间信息(新)
    stolenStat                         StolenStat                             55   +nocs                        # 被盗统计
    npcFarmInfo                        NpcFarmInfo                            56   +farmOwner,+noFarmVisitor    # 我的小红狐农场
    farmKirin                          FarmKirin                              57   +farmOwner                   # 仙麒麟
    farmHotInfo                        FarmHotInfo                            58   +nocs                        # 农场热度
    farmPartyInfo                      FarmPartyInfo                          59   +farmOwner                   # 农场派对信息
    farmCookViewInfo                   FarmCookViewInfo                       60                                # 农场餐厅视图
    farmSignature                      FarmSignature                          61   +farmOwner,+noFarmVisitor    # 签名信息
    farmStatusViewInfo                 FarmStatusViewInfo                     62   +noFarmVisitor               # 农场状态视图
    farmReport                         map<FarmReport>                        64   +farmOwner,+noFarmVisitor    # 周报信息
    furnitureTimeInfo                  map<FurnitureTimeInfo>                 65   +farmOwner,+noFarmVisitor    # 家具时间信息(客户端排序显示用)

@FarmSignature
    lastChangeMs        int64                       1           # 上次修改时间(毫秒)
    readInfos           map<FarmSignatureRead>      2           # 24小时内的阅读时间

@FarmSignatureRead
    uid                 int64                       1   +key
    readMs              int64                       2           # 阅读时间(毫秒)

@StolenStat
    incrId              int64                       1           # 自增id
    records             map<StolenInfo>             2
    cdExpireMs          int64                       3           # CD到期毫秒
    currCompensation    int64                       4           # 当前待补偿农场币
    nextCalcMs          int64                       5           # 下一次计算补偿的时间(削峰)
    newFlag             bool                        6           # 有新被盗

@StolenInfo
    id                  int64                       1   +key
    timeMs              int64                       2           # 偷取时间
    loseFarmCoin        int64                       3           # 损失折合农场币
    itemIdList          int32[]                     4           # 被盗itemId数组
    itemNumList         int64[]                     5           # 被盗itemNum数组

@NpcFarmSchedule
    lastCropReadySec         int64       1   # 最后一次作物就绪（成熟、可偷）
    lastAnimalReadySec       int64       2   # 最后一次动物就绪
    lastFishReadySec         int64       3   # 最后一次鱼就绪
    nextCropBeginSec         int64       4   # 下一次开始种植
    nextAnimalBeginSec       int64       5   # 下一次动物开始
    nextFishBeginSec         int64       6   # 下一次鱼开始
    nextCropReadySec         int64       7   # 下一次作物就绪（成熟、可偷）
    nextAnimalReadySec       int64       8   # 下一次动物就绪
    nextFishReadySec         int64       9   # 下一次鱼就绪
    stealCDSec               int64       10  # （废弃）****（废弃）
    lastVisitSec             int64       11  # 上一次有人到访时间
    lastRefreshSec           int64       12  # 上一次刷新时间
    fishRefresh              int32       13  # 鱼缸是否要刷新
    lastMessageUpdate        int64       14  # 上一次寄语刷新
    nextFertilizeSec         int64       15  +nocs # 下次小红狐磕头时间


@NpcFarmBasicInfo
    npcStage                  int32                     1  # NPC农场的阶段，新手0 或 正式农场1
    level                     int32                     2  # 等级
    canStealTime              int64                     4  # 对主人的最早可偷时间
    messageID                 int32                     5  # 当前用的哪条寄语
    remarkName                string                    6  # 社交面板小红狐备注名
    bePinned                  bool                      7  # 社交面板小红狐是否置顶
    ignoreStealTime           int64                     8  # (废弃)


@NpcFarmInfo
    npcFarmSchedule     NpcFarmSchedule     1  +nocs     # 日程安排（不下发）
    npcFarmBasicInfo    NpcFarmBasicInfo    2            # 我的小红狐农场基本信息


@FarmGodFigure
    lastPrayingTime           int64                         1           # 上一次抽奖的unix秒
    nextBigTime               uint32                        2  +nocs    # 下一个大吉还要许愿几次
    currPrayTimes             uint32                        3  +nocs    # 目前已经许愿几次了

@FarmBasicInfo
    name                      string                        1
    createTime                int64                         2
    instruction               string                        3
    version                   string                        4
    svrId                     int32                         5   +nocs
    exp                       int64                         6
    lastRefreshTimeMs         int64                         7   +nocs  # 上次刷新时间
    openId                    string                        8   # 玩家openId 废弃 取farm的去
    hasHouse                  bool                          10
    hasVillage                bool                          11
    farmSignature             string                        12  # 农场个性签名
    visitorListTag            int32                         13  # 访客列表标记（0可以展示）
    relatedFarmID             int64                         14  # 关联的农场ID。如果这是一个NPC农场，这里会记录玩家的农场ID，反之……
    enterPermission           int32                         15  # 进入权限(0=所有人,1=仅好友)
    lastLoginTimeMs           int64                         16  +nocs # 上次登录登录时间
    hasCook                   bool                          17
    farmFriendNum             int32                         18
    farmSignatureEXP          int64                         19  # 农场签名有效期
    boolKV                    int64[]                       20  +nocs # 可自动扩展的按位存储的标记

@FarmPublicInfo
    farmLevel                 int32                         1          # 农场等级
    farmVisitorCount          int32                         2          # 农场人数
    canStealTimeInfo          map<CanStealTime>             3          # 可偷菜时间信息
    farmSafeInfo              FarmSafeInfo                  4          # 农场安全信息
    totalLiuYanMessageNumber  int32                         5          # 留言总数
    farmMonthCardEndTime      int64                         6          # 农场月卡到期时间
    farmBuildingSkins         map<BuildingSkin>             7          # 建筑皮肤
    farmSignature             string                        8          # 农场个性签名
    farmPartyInfo             FarmPartyPublicInfo           9          # 农场派对信息
    lastLoginTimeMs           int64                         10         # 上次登录登录时间
    lastChangeSignatureMs     int64                         11         # 上次修改签名的时间
    cookCanStealTimeInfo      map<CanStealTime>             12         # 餐厅可偷时间信息
    farmSignatureEXP          int64                         13         # 农场签名有效期
    farmPetClothing           map<PetClothing>              14         # 在场宠物和服饰

@CanStealTime
    playerUID                 int64                         1   +key   # 玩家UID(对于偷过的人为UID,没偷过的用-1)
    canStealTime              int64                         2          # 可偷菜时间

@FarmSafeInfo
    putDown                   FarmPunish                    1
    giftSendingForbidden      FarmPunish                    2   # 送礼限制
    hotRatio                  float                         3   # 农场热度修正
    hotRatioEndTime           int64                         4   # 农场热度修正过期时间
    partyPublishForbidden     FarmPunish                    5   # 派对发布限制


@FarmPunish
    enable                    bool                          1
    endTime                   int64                         2
    reason                    string                        3

@FarmClientCache
    K                         string                      1  +key
    V                         string                      2

@FarmStatisticInfo
    dailyAddFarmExp           int64                       1       # （废弃）每日获得的农场经验
    dailyAddFarmCoin          int64                       2       # （废弃）每日获得的农场金币
    dailyStealCorpTimes       int32                       3       # 每日偷菜增加道具次数
    lastUpdateTime            int64                       4       # 上次统计时间
    lastExpWarningTime        int64                       5       # （废弃）上次经验预警时间（如果再次预警距离上次不足7天 则触发告警）
    lastCoinWarningTime       int64                       6       # （废弃）上次金币预警时间（如果再次预警距离上次不足7天 则触发告警）

@FarmLiuYanMessageInfo
    totalNumber int32   1# 留言总数
    newNumber   int32   2# 新留言数量
    lastPullId  int64   3# 上次拉取的id
    permission  int32   4# 留言权限(0=所有人,1=仅好友)

@FarmWelcomeInfo
    content     string  1# 农场寄语内容
    bannedTime  int64   2# 禁止设置农场寄语截止时间

@FarmBlockedFriend
    uid                   int64               1  +key     # 拉黑好友的uid
    updateTime            int64               2           # 更新时间

@FarmFishPoolInfo
    state                   int32                                           1          # 状态 FarmFishPoolState
    plantInfo               FishPoolPlantInfo                               2          # 养殖数据
    ripeInfo                FishPoolRipeInfo                                3          # 成熟数据
    produceInfo             FishPoolProduceInfo                             4          # 产出数据
    stealInfo               FishPoolStealInfo                               5          # 偷菜数据

@FishPoolPlantInfo
    layer                   int32                                           2          # 养殖 - 层次
    bait                    int32                                           3          # 养殖 - 鱼饵类型
    baitCount               int32                                           4          # 养殖 - 鱼饵数量
    period                  int32                                           5          # 养殖 - 养殖周期
    startTime               int64                                           6          # 养殖 - 开始时间
    baitPrice               int64                                           7          # 养殖 - 花了多少钱 用磷虾的时候无效

@FishPoolRipeInfo
    ripeTime                int64                                           7          # 养殖成熟时间
    ripeQuality             int32                                           8          # 成熟质量 FarmCropRipeQuality
    ripeWeekend             bool                                            9          # 成熟周末暴击
    fishCardLevelAverageOverPurpleWhenRipe     float                        10         # 鱼塘成熟的时候，鱼卡的平均等级 只算紫鱼和以上

@FishPoolProduceInfo
    totalFishingCount       int32                                           10         # 产出 - 全部钓鱼次数
    leftFishingCount        int32                                           11         # 产出 - 剩余钓鱼次数
    FishingRecords          map<FarmFishingRecord>                          12  +nocs  # 产出 - 产出结果记录

@FishPoolStealInfo
    stolenCount             int32                                           13         # 炸鱼 - 被偷次数
    stealProtectCanceled    bool                                            14         # 炸鱼 - 是否提前关闭炸鱼保护
    stolenUIDs              map<KvLL>                                       15         # 炸鱼 - 被哪些人偷过 每人偷了几次
    ignoredUIDs             set<int64>                                      16         # 取消提醒 - 被哪些人取消提醒过
    stealFailCount          map<KvLL>                                       17         # 炸鱼 - 每个人的炸鱼失败次数 保底用

@FarmFishingRecord
    idx                             int32                                   1   +key   #
    startTimeStamp                  int64                                   2          # 抛竿时间/炸鱼时间
    playerUid                       int64                                   3          # 操作者uid
    state                           int32                                   4          # 0准备 1成功 2失败 FarmFishRecordState
    fishType                        int32                                   5          # 鱼类型
    itemGet                         map<HarvestItem>                        6          # 获得产物
    startScore                      int64                                   7          # 初始分
    finalScore                      int64                                   8          # 最终分
    luckyHook                       bool                                    9          # 幸运勾
    ripeWeekend                     bool                                    10         # 周末暴击
    fishCardLevel                   int32                                   11         # 鱼卡等级
    newRecord                       bool                                    12         # 新记录
    isFishCardPack                  bool                                    13         # 是不是钓出鱼卡包
    newFish                         bool                                    14         # 新鱼
    luckyForCardPack                bool                                    15         # 幸运钩对鱼卡包有没有提升 客户端显示用
    fishPrice                       int64                                   16         # 鱼的价格
    fishCardPackExp                 int32                                   17         # 卡包给的熟练度
    fishCardPackCoin                int64                                   18         # 卡包给的熟练度溢出的钱
    farmExp                         int32                                   19         # 获得农场经验


@FarmHandBook
    category                int32                           1   +key    # 作物类别 FarmCropCategroy
    handBookRecords         map<FarmHandBookRecord>         2           # 类别独立图鉴
    awardGot                set<int32>                      3           # 已经领取的奖励

@FarmHandBookRecord
    type                    int32           1   +key    # 作物id
    maxScore                int64           2           # 最高分值

@FarmFishCardLevel
    type               int32                1   +key    # id
    level              int32                2           # 等级
    exp                int64                3           # 经验
    levelUpTime        int64                4           # 升级级的时间点

@FarmFishBowl
    id                  int32               1   +key    # id
    itemId              int32               2           # 物品id
    billNo              string              3           # 操作订单id

@FarmAquarium
    id                  int32                       1   +key    # 水族馆模式打开了
    tags                map<FarmAquariumTag>        2           # 鱼位
    lastResetTime       int64                       3           # 上次重置时间
    lastGetExpTime      int64                       4           # 上次取经验时间

@FarmAquariumTag
    scale               int32                       1   +key    # 大小
    seats               map<FarmAquariumSeat>       2           # 鱼位

@FarmAquariumSeat
    idx                 int32               1   +key    #
    scale               int32               2
    itemId              int32               3           # 物品id
    billNo              string              4           # 操作订单id
    benefitStartTime    int64               5           # 收益开始计算的时间
    trackId             int32               6           # 客户端轨迹id
    quality             int32               7           # 品质
    qualityUsedSec      int64               8           # 当前品质使用了多少 秒

@FarmBuff
    buffId              int32       1 +key  # buffId
    startTimeSec        int64       2       # 开启时间
    endTimeSec          int64       3       # 结束时间

@FarmBuffList
    sourceUid   string          1 +key  # 来源Uid
    buffs       map<FarmBuff>   2       # buff列表

@FarmBuffEffect
    effectId  int32       1 +key # buff效果Id
    value     double      2      # buff值

@FarmBuffInfo
    selfBuffList                      map<FarmBuffList>        1        # 玩家自己的农场Buff
    selfBuffEffectList                map<FarmBuffEffect>      2        # 玩家自己的农场buff效果（缓存用）
    selfBuffEffectNextFreshTimeSec    int64                    3        # buff效果下一次刷新时间（秒）
    selfBuffHotUpdateTimeMs           int64                    4  +nocs # 玩家自己的农场Buff刷新时间戳（支持热更）

@FarmNewPlayerInfo
    newPlayerSpeedCropRecords      map<FarmNewPlayerSpeedCropRecord>     1    # 新手加速作物记录
    cropConnectionUpdated          bool                                  2    # 是否是新版本的地格联通（不同等级也可以联通）
    redFoxStealTeach               int32                                 3    # 小红狐偷菜教学标记(0:未标记(新老账号),1:前置新手未完成(新账号),2:前置新手已完成可偷菜(新账号),3:已偷菜(新账号),4:不用偷菜(老帐号))
    animalFeedFlagUpdated          bool                                  4    # 动物饲料堆标记更新过
    farmStatusViewUpdated          bool                                  5    # 农场状态视图更新过


@FarmNewPlayerSpeedCropRecord
    cropId                    int32                         1   +key
    plantCnt                  int64                         2
    speedCnt                  int64                         3

@FarmFishingGuaranteedInfo
    FishTypeGuaranteed              map<KvII>                                   1   # 每条鱼出现概率保底 对每条鱼做独立保底 整数 这个不用，只维护给客户端显示
    QualityGuaranteed               map<KvII>                                   2   # 每条鱼钓起质量A的保底 对每条鱼做独立保底
    QualitySGuaranteed              map<KvII>                                   3   # 每条鱼钓起质量S的保底 对每条鱼做独立保底
    QualitySSGuaranteed             map<KvII>                                   4   # 每条鱼钓起质量SS的保底 对每条鱼做独立保底
    FishPriceGuaranteed             map<FishPriceRecord>                        5   # 钓鱼价值的保底，对每个鱼饵每层做独立保底
    ActivityFishGuaranteed          map<ActivityFishGuaranteedRecord>           6   # 活动鱼的保底，对每个活动做独立保底
    QualitySSSGuaranteed            map<KvII>                                   7   # 每条鱼钓起质量SSS的保底 对每条鱼做独立保底
    FishTypeGuaranteedFloat         map<KvIF>                                   8   # 每条鱼出现概率保底 对每条鱼做独立保底 浮点数 就用这个

@FishPriceRecord
    k                           int32                   1 +key  # 鱼饵id*1000+层级id
    layer                       int32                   2
    bait                        int32                   3
    values                      int64[]                 4       # 鱼价格的list

@ActivityFishGuaranteedRecord
    activityId                  int32                   1 +key  # 活动id
    cnt                         int32                   2       # 计数
    guaranteed                  bool                    3       # （废弃）

@FarmPetInfo
    pets                                    map<FarmPet>               1           # 所有宠物狗信息
    summonedPetId                           int32                      2           # 当前在场的宠物狗ID
    houseKeepingDisable                     bool                       3           # 宠物狗看家护院是否关闭
    foodValue                               int32                      4           # 宠物狗饱腹值停留值
    petModuleOpenTimeMs                     int64                      5           # 宠物狗模块开启时间(DS同步)
    favorInfo                               PetFavorInfo               6    +nocs  # 宠物狗好感度相关信息
    giftInfo                                PetGiftInfo                7           # 宠物狗礼物相关信息
    fertilizeInfo                           PetFertilizeInfo           8    +nocs  # 宠物狗祈愿相关信息
    clothingInfo                            PetClothingInfo            9           # 服装相关信息
    securityInfo                            PetSecurityInfo            10          # 安全相关信息
    cats                                    map<FarmPet>               101         # 所有宠物猫信息
    summonedCatId                           int32                      102         # 当前在场的宠物猫ID
    catFoodValue                            int32                      103         # 宠物猫饱腹值停留值
    catFavorInfo                            PetFavorInfo               104  +nocs  # 宠物猫好感度相关信息
    catGiftInfo                             PetGiftInfo                105         # 宠物猫礼物相关信息
    wildCats                                map<FarmWildCat>           106         # 所有野生猫信息
    wildCatId                               int32                      107         # 当前在场的野生猫ID
    wildCatFavorInfo                        PetFavorInfo               108         # 野生猫好感度相关信息
    wildCatRefreshInfo                      WildCatRefreshInfo         109         # 野生猫刷新相关信息
    catFishingInfo                          CatFishingInfo             110         # 宠物猫钓鱼相关信息

@PetFavorInfo
    interactAddFavorSum                     int32                      1          # 互动增加好感度累计
    interactAddFavorSumLastResetTime        int64                      2          # 互动增加好感度累计的上次重置时间
    lastFeedAddFavorTime                    int64                      3          # 上次喂食增加好感度的时间
    hungryDurationSum                       int32                      4          # 饥饿时间累计
    lastCheckHungryTime                     int64                      5          # 上次饥饿检查的时间


@PetGiftInfo
    prepared                                bool                       1          # 是否准备好
    items                                   map<PetGiftItem>           2          # 具体道具
    todayGiftCount                          int32                      3          # 今日收礼次数
    todayGiftCountLastResetTime             int64                      4          # 今日收礼次数上次重置时间
    lastGiftTime                            int64                      5          # 上次收礼时间

@PetGiftItem
    itemId                                  int32                      1   +key   # 道具id
    itemNum                                 int64                      2          # 道具数量


@PetFertilizeInfo
    fertilizeHistoryMap                     map<FertilizeHistory>      1          # 祈愿历史
    fertilizeTraceIdCounter                 int32                      2          # 祈愿追踪id计数器


@FertilizeHistory
    traceId                                 int32                      1   +key   # 追踪id 用来给宠物追踪
    operatorUid                             int64                      2          # 祈愿者
    recordTime                              int64                      3          # 祈愿时间
    recordBytes                             bytes                      4          # 祈愿记录序列化
    gridId                                  int32                      5          # 祈愿地格

@PetClothingInfo
    ownedClothing                           map<PetOwnedClothing>        1        # 拥有的服装

@PetOwnedClothing
    id                                      int32                      1   +key   # 服装id
    obtainTime                              int64                      2          # 获得时间

@PetSecurityInfo
    bannedEndTime                           int64                      1          # 处罚结束时间


@FarmPet
    id                           int32                      1   +key   # 配置id
    feedValue                    int32                      2          # 饱食度 - 数值
    feedValueLastUpdateTime      int64                      3          # 饱食度 - 数次上次更新时间
    clientCache                  map<PetClientCache>        4          # 客户端用
    obtainTime                   int64                      5          # 获得时间
    favorValue                   int32                      6          # 好感度 - 当前值
    favorValueHistory            int32                      7          # 好感度 - 历史最高值
    favorFullGiftStatus          int32                      8          # 好感度 - 满好感度礼物状态 对应com.tencent.wea.xlsRes.FarmPetFullFavorGiftStatus
    favorFullGiftItems           map<PetGiftItem>           9          # 好感度 - 满好感度礼物道具
    name                         string                     10         # 名字 - 名字
    lastChangeNameTime           int64                      11         # 名字 - 上次改名时间
    wearClothing                 map<PetWearClothing>       12         # 穿着 - 服装


@PetWearClothing
    clothingType                 com.tencent.wea.xlsRes.FarmPetClothingType      1   +key   # 服装类型
    clothingId                   int32                                           2          # 服装id


@PetClientCache
    k                            string                     1   +key   # 键
    v                            string                     2          # 值



@FarmWildCat
    id                           int32                      1   +key   # 配置id
    favorValue                   int32                      3          # 好感度 - 当前值
    favorValueHistory            int32                      4          # 好感度 - 历史最高值
    name                         string                     5          # 名字 - 名字
    arriveTime                   int64                      6          # 本次到达时间(秒)
    leaveTime                    int64                      7          # 本次离开时间(秒)
    canTame                      bool                       8          # 是否可驯化


@WildCatRefreshInfo
    lastWildCatLeaveTime         int64                      1          # 上次野猫离开时间
    nextRefreshCD                int64                      2          # 上次野猫离开后的随机冷却时间


@CatFishingInfo
    lastFishingReceiveTime       int64                      1          # 上次猫捕鱼的接收时间
    nextFishingCD                int64                      2          # 上次猫捕鱼后的随机冷却时间
    isReady                      bool                       3          # 猫捕鱼是否就绪


@FarmCollectionHandbook
    id                          int32                           1    +key   # 图鉴Id
    obtainTime                  int64                           2           # 获得时间
    isAwardFurniture            bool                            3           # 是否领取奖励家具

@FarmCollection
    handbooks                   map<FarmCollectionHandbook>     1           # 收藏品图鉴

@FarmSceneDropFarmItem
    itemId                      int32                           1    +key   # 道具ID
    itemNum                     int32                           2           # 道具数量

@FarmSceneDropInfo
    uid                         int64                           1    +key   # 掉落uid
    locatedFarmId               int64                           2           # 所在农场uid
    ownerId                     int64                           3           # 拥有者uid
    farmItems                   map<FarmSceneDropFarmItem>      4           # 掉落的农场道具
    expireTimeSec               int64                           6           # 过期时间
    billNo                      string                          7           # 操作订单id
    placeType                   int32                           8           # 所在场所 农场、小屋
    inFarmPos                   string                          9           # 农场坐标
    inHousePos                  int32                           10          # 小屋坐标
    dropType                    int32                           11          # 掉落类型
    inCook                      int32                           12          # 餐厅

@FarmSceneDrop
    selfDrops                   map<FarmSceneDropInfo>          1           # 农场属于自己的场景掉落物，别人不可见

@FarmWeatherInfo
    weatherId                   int32                           1           # 当前 - 天气id
    weatherEndTime              int64                           2           # 当前 - 天气结束时间
    firstRainWeatherFlag        bool                            3           # 统计 - 是否触发过首次下雨
    weatherBeginTime            int64                           4           # 当前 - 天气开始时间
    rainProtectCanceled         bool                            5           # 当前 - 本次下雨是否取消保护
    weatherTriggeredCount       map<KvLL>                       6           # 统计 - 天气触发次数计数
    lightningProtectEndTime     int64                           7           # 当前 - 本次打雷保护结束时间

@FarmCropStatisticInfo
    cropWaterAddCareAmount1                     int32           1           # （废弃）
    cropWaterAddCareAmount2                     int32           2           # （废弃）
    cropWaterAddCareAmount                      int32           3           # 作物浇水补充量总计
    cropWaterAddCareMeetCount                   int32           4           # 作物浇水补充量达标次数
    cropWaterAddCareMeetCountLastResetTime      int64           5           # 作物浇水补充量达标次数上次重置时间

@HouseItemStatistics
    itemInfo                     map<HouseItemInfo>              1     # 小屋家具数量信息
    cookItemInfo                 map<HouseItemInfo>              2     # 餐厅家具数量信息

@HouseItemInfo
    confId                       int32                      1 +key     # 配置id
    number                       int64                      2          # 数量

@FarmVillagerInfo
    villagers                  map<FarmVillager>         1             # 已入驻村民
    lastRefreshTime            int64                     2             # 上次刷新时间（毫秒）
    dailyRefreshTime           int64                     3             # (已废弃)村民每日数据上次刷新时间（毫秒）
    leavingVillagers           map<FarmLeavingVillager>  4  +nocs      # 离开的村民
    waitingVillager            FarmWaitingVillager       5             # 待入驻村民
    lastRefreshedVillagerId    int32                     7  +nocs      # 上次刷出的村民
    gift                       VillagerGiftInfo          8             # 村民礼物信息
    notFirstRefresh            bool                      10  +nocs     # 不是首次刷新村民
    lastWaitingRefreshTime     int64                     11  +nocs     # 上一个码头村民刷出时间（毫秒）

@FarmVillager
    id                           int32                      1 +key     # 配置id
    clientCache                  map<VillagerClientCache>   2          # 客户端存储
    obtainTime                   int64                      3          # 获得时间（毫秒）
    favorValue                   int32                      4          # 当前好感度
    villaId                      int32                      5          # 居住的民宅id  =buildingId
    presentGift                  VillagerPresentGiftInfo    6          # 当前显示的送礼信息
    acceptGift                   VillagerAcceptGiftInfo     7          # （废弃）
    birthTime                    int64                      8          # 首次刷出时间（毫秒）
    dailyFavor                   VillagerDailyFavorInfo     9          # 每日好感度信息
    lastWalletRefreshTime        int64                      11         # 上次钱包刷新时间
    fullFavorGiftState           int32                      12         # 满好感度礼物状态 对应xlsRes.FarmVillagerGiftState
    wallet                       int64                      13         # 村民钱包
    triggeredGift                map<VillagerTriggeredGift> 14         # 村民已触发的礼物队列
    festivalGift                 VillagerFestivalGiftInfo   15         # 节日礼物信息
    changedBPIdx                 int32                      16         # 换色BP索引
    everAcceptHobbyGift          bool                       17         # 曾经收到过喜好的礼物
    receivedExpFavor             int32                      18         # 已领经验奖励的好感度
    nowPosBuildingId             int32                      21         # 当前处于什么建筑里 0就是在农场里
    controlClient                string                     22         # 当前被什么客户端管理着 要么是playerUid 要么是ds的sessionId
    historyStayTime              int64                      23         # 历史入驻多少时间(毫秒)
    stayStaticPeriod             int32                      24         # 累计入驻满几个统计周期（玩家进村民屋时刷新）
    hide                         bool                       25         # 客户端是否不显示

@VillagerClientCache
    k                            string                     1 +key     # 键
    v                            string                     2          # 值

@VillagerGiftInfo
    lastNormalGiftRefreshTime    int64        1    # 上次村民日常送礼刷新时间（刷出后即可判定CD时间）
    lastOnDemandGiftRefreshTime  int64        3    # 上次村民按需送礼刷出时间（刷出后即可判定CD时间）
    lastOnDemandGiftPresentTime  int64        4    # 上次村民按需送礼送出时间（废弃，之前是送出后才可判定CD时间）
    farmWallet                   int64        6    # 农场钱包金额
    walletLastSettleTime         int64        7    # 按需礼包钱包上次结算时间
    todayPresentGiftCount        int32        8    # 今日村民已送礼总次数
    todayAcceptGiftCount         int32        9    # 今日村民已收礼总次数
    onDemandGiftCDHour           int32        10   # 村民按需礼物刷新CD小时
    lastNormalGiftTrigTime       int64        11   # 上次日常礼物触发时间（毫秒）
    lastOnDemandGiftTrigTime     int64        12   # 上次按需礼物触发时间（毫秒）
    shennongGiftTrigedLv         int32        13   # 已经触发的神农礼物等级

@VillagerPresentGiftInfo
    prepared                                bool                       1          # 是否准备好
    items                                   map<VillagerGiftItem>      2          # 具体道具
    lastGiftTime                            int64                      5          # 礼物准备好时间（毫秒）
    giftType                                int32                      6          # 礼物类型 对应xlsRes.FarmVillagerGiftType
    giftId                                  int32                      7          # 节日礼物对应的礼物Id（其它类型礼物填0）

@VillagerTriggeredGift
   giftType                               int32                      1  +key    # 礼物类型 对应xlsRes.FarmVillagerGiftType
   trigTime                               int64                      2          # 礼物准备好时间（毫秒）
   items                                  map<VillagerGiftItem>      3          # 具体道具
   giftId                                 int32                      4          # 节日礼物对应的礼物Id（其它类型礼物填0）

@VillagerFestivalGiftInfo
    historyFestivalGift        map<HistoryFestivalGift>   1      # 历史节日礼物信息

@HistoryFestivalGift
    giftId           int32       1   +key  # 已触发的节日礼物ID
    trigTime         int64       2         # 礼物触发时间（毫秒）

@VillagerGiftItem
    itemId                                  int32                      1   +key   # 道具id
    itemNum                                 int64                      2          # 道具数量

@VillagerAcceptGiftInfo
     todayGiftCount                          int32                      3          # （废弃）
     lastGiftTime                            int64                      5          # （废弃）

@VillagerDailyFavorInfo
    interActAdd                       map<InterActAddFavor>      1          # 今日互动增加好感度

@InterActAddFavor
    actId                int32            1  +key    # 互动Id
    addFavor             int32            2          # 已经增加的好感度

@FarmWaitingVillager
    id                           int32                      1          # 配置id
    appearTime                   int64                      2          # 刷出时间（毫秒）
    returning                    bool                       3          # 是否属于回流
    changedBPIdx                 int32                      4          # 村民换色BP索引
    disappearReason              int32                      5          # 消失原因 对应FarmWaitingVillagerDisappearReason
    favorValue                   int32                      6          # 当前好感度

@FarmLeavingVillager
    id                           int32                      1   +key   # 配置id
    favorValue                   int32                      2          # 当前好感度
    leavingTime                  int64                      3          # 离开时间（毫秒）
    birthTime                    int64                      4          # 首次刷出时间（毫秒）
    fullFavorGiftState           int32                      5          # 满好感度礼物状态 对应xlsRes.FarmVillagerGiftState
    everAcceptHobbyGift          bool                       6          # 曾经收到过喜好的礼物
    historyStayTime              int64                      8          # 历史入驻多少时间（毫秒）
    receivedExpFavor             int32                      9          # 已领经验奖励的好感度

@FarmFishingReport
    weekReport                  map<FarmFishingReportUnit>          1
    monthReport                 map<FarmFishingReportUnit>          2

@FarmFishingReportUnit
    startTimeStamp               int64                          1   +key    # 这个报告的开始时间戳
    reportUnitByLayer    map<FarmFishingReportUnitByLayer>      2

@FarmFishingReportUnitByLayer
    layer               int32                                   1   +key    # 民宅id，对应building中confId
    reportUnitByBait    map<FarmFishingReportUnitByBait>        2
    fishCardLevelAverageOverPurpleBefore     float              3         # 开始的时候，鱼卡的平均等级 只算紫鱼和以上
    fishCardLevelAverageOverPurpleAfter      float              4         # 结束的时候，鱼卡的平均等级 只算紫鱼和以上

@FarmFishingReportUnitByBait
    bait                    int32                     1   +key    # 鱼饵id
    baitNum                 int32                     2           # 鱼饵数量
    fishingSuccessCount     int32                     3           # 杆数
    beStolenCount           int32                     4           # 被偷数
    fishPrice               int64                     5           # 鱼的总价值
    fishCardExp             int32                     6           # 熟练度增加
    fishCardExpireCoin      int64                     7           # 熟练度溢出的币
    farmExp                 int32                     8           # 农场经验
    orangeCount             int32                     9           # 橙鱼数
    crownCount              int32                     10          # 皇冠鱼数
    fishCount               int32                     11          # 钓起的鱼的数量
    baitPrice               int64                     12          # 鱼饵价格
    talentFishCount         int32                     13          # 天赋珍宝鱼的数量

@FarmTalentInfo
    talents                map<FarmTalentUnit>        1

@FarmTalentUnit
    id                      int32                   1 +key
    level                   int32                   2
    unlockTime              int64                   3

@FarmTaskInfo
    runningTask          map<FarmTask>                1                       # 运行中的任务
    finishTask           map<FarmTaskFinish>          2                       # 完成的任务

@FarmTask
    id                      int32                                        1   +key    # 任务配置id
    status                  int32                                        2           # 任务状态
    timeInfo                FarmTaskTimeInfo                             3   +nocs   # 时间信息
    completeConditionInfo   FarmTaskConditionInfo                        4           # 完成条件信息
    pos                     string                                       5           # 触发任务时NPC的pos

@FarmTaskTimeInfo
   triggerTime             int64               1             # 任务触发时间
   initTime                int64               2             # 任务初始化时间
   completeTime            int64               3             # 任务完成时间

@FarmTaskConditionInfo
    type        int32         1 +key  # 条件类型
    currentVal  int64         2       # 条件的当前值

@FarmTaskFinish
  id              int32               1                     # 当前任务链最新结束的id
  finishTime      int64               2                     # 结束时间
  chainHeadId     int32               3   +key              # 任务链开始(任务链唯一标识)

@FarmTlogRequiredFields
  platID int32   1
  npc int32   4
  appId string   5
  level int32   6
  ServerIp string   7
  iSequence string   8
  telecomOper string   9
  network string   10
  clientIP string   11
  clientVersion string   12
  vClientIPV6 string   13
  clientPlat int32   14
  seasonId int32   15
  country string   16
  province string   17
  city string   18
  district string   19
  ReservePara string[]   20 #只能填充10个

@FarmMagicMpInfo
  mp                      int64             1          # 蓝条
  mpLastRecoverTime       int64             2          # 上一次蓝条回复时间

@FarmMagicExInfo
  isEquip       bool                  1          # 是否装备（装备类仙术特有）

@FarmMagicInfo
  id            int32                 1 +key     # 仙术ID
  lastUseTime   int64                 2          # 上一次使用时间（秒）
  mpInfo        FarmMagicMpInfo       3          # 蓝条
  exInfo        FarmMagicExInfo       4          # 扩展属性
  expire        int64                 5          # 过期时间（秒）

@FarmMagic
  magics        map<FarmMagicInfo>            1        # 仙术

@BannedItemInfo
    itemId                                  int32                      1   +key   # 道具id
    bannedTime                              int64                      2          # 禁售开始时间（秒）

# 农场外部操作相关
@FarmExternalOperateInfo
    opCntThisWeek                 int32           1       # 本周已操作次数
    lastOpCntResetTimeMs          int64           2       # 上次操作次数重置时间(毫秒)

# 农场模块开放时间
@FarmModuleOpenTime
    moduleId                      int32                        1  +key # 模块id
    openTime                      int64                        2       # 开放时间

# 农场活动起止时间
@FarmActivityOpenTime
    activityId                    int32                        1  +key # 活动id
    beginTime                     int64                        2       # 开始时间
    endTime                       int64                        3       # 结束时间

@FarmModuleOpenTimeInfo
    moduleOpenTimeMap             map<FarmModuleOpenTime>      1       # 模块开放时间
    activityOpenTimeMap           map<FarmActivityOpenTime>    2       # 活动起止时间

@FarmKirin
    level                         int32                        1       # 等级
    state                         int32                        2       # 状态 FarmKirinState
    incubateStartTime             int64                        3       # 孵化开始时间
    incubateEndTime               int64                        4       # 孵化结束时间
    mana                          int32                        5       # 仙力
    collectFarmExp                int64                        6       # 收集的经验
    isCanEvo                      bool                         7       # 是否能进化

# 农场热度值
@FarmHotValue
    type                      int32                       1  +key   # 热度类型
    hotCount                  int64                       2         # 热度值

# 农场热度值统计信息
@FarmHotValueStatisticInfo
    timeKey                   int64                       1  +key   # 时间戳（秒）
    timeDimension             int32                       2         # 时间维度（废弃）
    hotValue                  map<FarmHotValue>           3         # 热度值

# 农场热度信息
@FarmHotInfo
    hotValueStatisticInfo     map<FarmHotValueStatisticInfo>    1         # 热度值(key是时间戳)

# 农场派对公开信息
@FarmPartyPublicInfo
    content                           string                1       # 派对简介
    url                               string                2       # 派对封面url
    image                             CosImage              3       # 派对封面
    isOpen                            int32                 4       # 是否开放
    scene                             int32                 5       # 开启派对的场景 FarmPartyScene
    expireTime                        int64                 6       # 过期时间(秒)
    lastPublishTimeMills              int64                 7       # 上次发布时间(毫秒)
    visitorCount                      int32                 8       # 派对参与人数

# 派对Tlog字段
@FarmPartyTlogRequiredFields
    maxHotValue                       int64                 1       # 派对期间最大热度值
    maxVisitorNum                     int32                 2       # 派对期间最大参与人数
    visitorUIDs                       set<int64>            3       # 哪些人进入过本次派对场景(累计参与人数)

# 农场派对信息
@FarmPartyInfo
    farmPartyPublicInfo               FarmPartyPublicInfo               1       # 派对公开信息
    lastNickName                      string                            2       # 上次更新到ES的昵称
    lastIsInParty                     int32                             3       # 主人是否在派对
    lastHotValue                      int64                             4       # 上次更新到ES的热度值
    lastHotSafeRatio                  float                             5       # 上次更新到ES的热度权重
    lastVersionGroup                  string                            6       # 上次更新到ES的版本号
    lastUpdateTime                    int64                             7       # 上次更新时间(秒)
    expireTime                        int64                             8       # 过期时间(秒)(废弃)
    lastPublishTimeMills              int64                             9       # 上次发布时间(毫秒)(废弃)
    lastIsWhiteList                   int32                             10      # 上次是否是白名单
    farmPartyTlogRequiredFields       FarmPartyTlogRequiredFields       11      # 派对log
    lastVisitorCount                  int32                             12      # 上次更新到ES的派对参与人数
    lastFarmLevel                     int32                             13      # 上次更新到ES的农场等级

@FarmCookViewInfo
    canStealTimeMap               map<KvLL>                 1       # 可偷时间数据(不考虑取消本次提醒)
    floatingScreenInfo            CookFloatingScreenInfo    2       # 餐厅流动显示屏信息
    canStealTimeMapForRemind      map<KvLL>                 3       # 可偷时间数据(考虑取消本次提醒)

@FarmStatusViewInfo
    cropRipeTime                        int64                   1       # [作物]最近成熟-时间
    cropRipeType                        int32                   2       # [作物]最近成熟-类型
    normalCropRipeTime                  int64                   3       # [作物]最近成熟常规-时间
    normalCropRipeType                  int32                   4       # [作物]最近成熟常规-类型
    cropDryTime                         int64                   5       # [作物]最近干涸-时间
    cropDryType                         int32                   6       # [作物]最近干涸-类型
    animalRipeTime                      int64                   7       # [动物]最近成熟-时间
    animalRipeType                      int32                   8       # [动物]最近成熟-类型
    animalHungryTime                    int64                   9       # [动物]最近饥饿-时间
    animalHungryType                    int32                   10      # [动物]最近饥饿-类型
    animalCanEncourageTime              int64                   11      # [动物]最近待产-时间
    animalCanEncourageType              int32                   12      # [动物]最近待产-类型
    fishRipeTime                        int64                   13      # [鱼塘]成熟时间
    fishProtectTime                     int64                   14      # [鱼塘]保护结束时间
    fishPoolLayer                       int32                   15      # [鱼塘]层数
    machineRipeTime                     int64                   16      # [加工器]最近成熟时间
    machineRipeType                     int32                   17      # [加工器]最近成熟类型
    aquariumRipeTime                    int64                   18      # [水族箱]攒满时间
    collectionDropTime                  int64                   19      # [收藏品]掉落时间
    collectionDropInFarmTime            int64                   20      # [收藏品]掉落时间(仅农场内)
    godFigureCanPrayTime                int64                   21      # [神像]可祈福时间
    magicSkillMpFullTime                int64                   22      # [仙术]蓝条满时间
    kirinIncubateFinishTime             int64                   23      # [麒麟]孵化完成时间
    kirinCanCollectManaTime             int64                   24      # [麒麟]可采集仙力时间
    cookVisitantCanServeTime            int64                   25      # [餐厅]贵宾可邀请时间
    cookVisitantCanPrebookTime          int64                   26      # [餐厅]贵宾可预约时间
    cookCanOpenTime                     int64                   27      # [餐厅]可备菜时间
    cookVisitantProtectEndTime          int64                   28      # [餐厅]贵宾保护到期时间

@FarmReport
    reportType        int32                        1  +key   # 报告类型
    dayReport         map<FarmReportInfo>          2
    weekReport        map<FarmReportInfo>          3
    monthReport       map<FarmReportInfo>          4

@FarmReportInfo
     startTimeStamp        int64                       1  +key  # 这个报告的开始时间戳
     reportData            map<FarmReportData>         2
     endTimeStamp          int64                       3        # 月报中记录月结束时间点（日报周报不记录）

@FarmReportData
     category               int32               1   +key    # 细分类型
     reportType             int32               2           # 报告类型，便于客户端识别oneof
     farmReportUnit         FarmReportUnit      3

@FarmReportUnit +oneof  # 各种周报数据
    farmStealReportUnit    FarmStealReportUnit  1   # 我的偷菜数据

@FarmStealReportUnit
    stealNum               int32                1           # 偷取总次数
    coinGain               int64                2           # 偷取总价值
    orangeFishCount        int32                3           # 鱼分类中橙鱼数（其它分类不记录）
    crownFishCount         int32                4           # 鱼分类中皇冠鱼数（其它分类不记录）

@PetClothing
    petId                  int32                    1   +key    # 宠物id
    wearClothing           map<PetWearClothing>     2           # 服饰

@FurnitureTimeInfo
    confId                 int32                    1   +key    # 家具配置id
    updateTime             int64                    2           # 上次更新的时间戳