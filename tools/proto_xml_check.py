import json
import subprocess
import re
import argparse
import os
import requests
import logging
import uuid
import xml.etree.ElementTree as ET
import datetime
import sys

sys.path.append("timipy")
print("cwd:", os.getcwd())
print("sys.path:", sys.path)
import Redis_set
from tapd_func import TAPDUtil

redis_conn = None


def delay(seconds):
    start = datetime.datetime.now()
    end = start + datetime.timedelta(seconds=seconds)
    while datetime.datetime.now() < end:
        pass


def send_http(error):
    logKey = str(uuid.uuid4())
    request_timeout = 5
    robot_lot_utils_url = "http://zplan.woa.com/api/alert/add"  # 请填写你的URL
    params = {
        "targetRobotURL": "null",  # 需要更改
        "logKey": logKey,  # 需要更改
        "env": "proto_compatibility_check",  # 需要更改
        "logContent": error,  # 需要更改
        "logCount": "1",  # 需要更改
        "appendInfo": "appendInfo",  # 需要更改
        "atInfo": "atInfo",  # 需要更改
        "jsonContent": "jsonContent",  # 需要更改
        "needClaim": "needClaim",  # 需要更改
        "clLogSnsURL": "clLogSnsURL",  # 需要更改
        "gitBranch": "develop",  # 需要更改
        "compileTime": "compileTime",  # 需要更改
        "hasSpecificOwner": "hasSpecificOwner",  # 需要更改
    }

    try:
        response = requests.post(
            url=robot_lot_utils_url,
            data=params,
            timeout=request_timeout)

        if response.status_code != 200 and response.status_code != 302:
            logging.error(
                f"sendToLogUtils http get error, url: {robot_lot_utils_url}, statusCode: {response.status_code}")
        else:
            logging.debug(
                f"sendToLogUtils successfully, url: {robot_lot_utils_url}, statusCode: {response.status_code}")
    except Exception as e:
        logging.error(f"request exception: {str(e)}")


def git_blame(file_path, line_number):
    # 执行git blame命令
    git_blame_command = f"git blame {file_path} -L {line_number},{line_number}"
    process = subprocess.Popen(git_blame_command.split(), stdout=subprocess.PIPE)
    output, error = process.communicate()

    if error:
        print(f"发生错误: {error}")
    else:
        return output.decode()


def git_checkout_force(branch):
    cmd = ['git', 'checkout', '-f', branch]
    try:
        result = subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        print(f"Switched to branch {branch} successfully.")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Error switching branch: {e.stderr}")


def find_line_number(file_path, text):
    with open(file_path, 'r', encoding='utf-8') as file:
        for num, line in enumerate(file, 1):
            if text in line:
                return num
    return None


# 检测错误写入Json文件
def data2json(json_data, json_output_file):
    # 写入或追加JSON数据
    with open(json_output_file, 'w', encoding='utf-8') as file:
        json.dump(json_data, file, ensure_ascii=False, indent=4)


def json2html(json_file, html_file):
    with open(json_file, 'r', encoding='utf-8') as file:
        data = json.load(file)

        # 检查errors_xml和errors_proto字段是否存在
    errors = data.get('errors', [])

    # 根据是否存在字段构建HTML内容
    html_content = "<html><body><h1>变更检查错误报告</h1><table style='width:80%;border:1px solid black;'>"
    count = 1
    for error_list in errors:  # 假设errors是一个列表的列表
        for error in error_list:  # 遍历每个错误列表中的错误字符串
            if count == 1:
                html_content += "<tr>"
            html_content += f"<td>{error}</td>"
            if count == 3:
                count = 0
                html_content += "</tr>"
            count += 1
    html_content += "</table></body></html>"

    with open(html_file, 'w', encoding='utf-8') as file:
        file.write(html_content)


def gen_html(html_file):
    html_content = "<html><body><h1>变更检查错误报告</h1><table style='width:80%;border:1px solid black;'>"
    html_content += "</table></body></html>"

    with open(html_file, 'w', encoding='utf-8') as file:
        file.write(html_content)


def compare_commits_files_by_name(commit1, commit2):
    # 构造Git命令
    command = ["git", "diff", "--name-only", commit1 + ".." + commit2]

    # 调用Git命令并获取输出
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()

    # 检查是否有错误输出
    if stderr:
        print(f"Error: {stderr.decode()}")
        return []

    # 解析diff输出并筛选包含特定文件名的路径,需要增加路径可以在这里加
    specific_filenames = ['tcaplus_db.xml', 'tcaplus_db.proto', 'tlog.xml', '/server/proto/', 'protos/','excel/basic/proto/ResKeywords.proto']
    # specific_filenames = ['tcaplus_db.xml']
    filtered_file_paths = []
    for line in stdout.decode().splitlines():
        file_path = line.strip()
        if any(filename in file_path for filename in specific_filenames):
            filtered_file_paths.append(file_path)

    return filtered_file_paths


def compare_commits_file_changes(commit1, commit2, file_path):
    # 构造Git命令，使用标准格式获取文件的具体修改内容
    command = ["git", "diff", commit1 + ".." + commit2, "--", file_path]

    # 调用Git命令并获取输出
    process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()

    # 检查是否有错误输出
    if stderr:
        print(f"Error: {stderr.decode()}")
        return None

        # 返回修改内容的输出
    return stdout.decode()


# 修改行处理，避免注释修改导致问题
def clean_line(line):
    pos = line.rfind('//')
    if pos != -1:
        line = line[:pos]
    pos = line.rfind('[')
    if pos != -1:
        line = line[:pos]
    line = line.strip()
    line = line.replace('//', '')
    line = line.replace(' ', '')
    line = line.replace('\t', '')
    line = line.replace('+', '')
    line = line.replace('-', '')
    line = line.replace(';', '')
    return line


# proto文件的兼容性要求
# 1、不要修改已经存在的任何字段编码值
# 2、新增字段必须为optional或repeated，否则无法保证新老程序在互相传递消息时的消息兼容性。
# 3、禁止删除任何已有字段，包括optional和repeated类型的字段
# 4、新增字段标签号可以不连续但不能重复。
def check_file_changes_proto(file_changes, filename, file_path):
    # 用于记录不符合规则的行和原因
    error = []
    update_line = []
    # 分割file_changes为行列表
    lines = file_changes.split('\n')

    # 遍历每一行
    for index, line in enumerate(lines, start=1):  # enumerate从1开始计数，模拟行号
        # 检查是否是修改行（以+或-开头）
        if line.startswith(('+', '-')):
            # 检查删除行（以-开头）
            if re.match(r'^-[^-]', line):
                # 检查前一行和后一行（如果存在）
                if index > 1:
                    prev_line = lines[index - 2]  # 前一行（因为index从1开始，所以减2）
                else:
                    prev_line = None  # 如果没有前一行，则为None
                if index < len(lines):
                    next_line = lines[index]  # 后一行（当前行，因为我们要检查它是否也修改了）
                else:
                    next_line = None  # 如果没有后一行，则为None

                # 将删除行及其前后行（如果存在）添加到错误中
                error_message = "检测到删除或修改行内容"
                error.append((line, error_message, prev_line, next_line))
            else:
                cleaned_string = clean_line(line)
                update_line.append(cleaned_string)

                # 检查结束后，打印所有不符合规则的行和原因
    errors = []
    for line, reason, prev_line, next_line in error:
        # 忽略注释修改
        cleaned_string = clean_line(line)
        cleaned_string = cleaned_string.replace('//', '')
        if cleaned_string not in update_line:
            if len(cleaned_string) == 0:
                continue
            find_line = line.replace('-', '')
            line_number = find_line_number(file_path, find_line)
            # git_blame(file_path, line_number)
            # print("find number", line_number)
            errors.append(f"[Error!][{file_path}]")
            # errors.append(f"line{line_number}")
            # errors.append(f"{line}")
            errors.append(f"不符合规则: {reason}")
            errors.append(f"{git_blame(file_path, line_number)}")
            print(f"[Error!][{file_path}]不符合规则: {reason}")
            print(f"[Error!][{file_path}]不合规：     {line} ")
    # 如果存在不符合规则的行，则返回False；否则返回True
    return errors


# xml文件的兼容性要求
# 1、metalib字段的version值必须修改，且大于原先的值
# 2、所有字段里的version值不允许被修改
# 3、不允许删除任何字段，允许修改字段的version
def check_file_changes_xml(file_changes, file_path):
    error = []

    # 规则1：检查<metalib>标签的version属性
    metalib_pattern = re.compile(r'<metalib tagsetversion="1" name="tcaplus_tb" version="(\d+)"')
    old_version = None
    new_version = None
    old_line = ""
    for line in file_changes.split('\n'):
        if line.startswith('-'):
            match = metalib_pattern.search(line)
            if match:
                old_version = int(match.group(1))
                old_line = line.replace('-', '')
        elif line.startswith('+'):
            match = metalib_pattern.search(line)
            if match:
                new_version = int(match.group(1))
    if old_version is None and new_version is None:
        print("未修改metalib版本信息")
    elif old_version is None or new_version is None:
        line_number = find_line_number(file_path, old_line)
        error.append(f"[Error!][{file_path}]")
        error.append("新增修改中未找到完整的metalib版本信息")
        error.append(git_blame(file_path, line_number).replace('<', ''))
    elif new_version <= old_version:
        line_number = find_line_number(file_path, old_line)
        error.append(f"[Error!][{file_path}]")
        # error.append("None")
        error.append("新增的metalib版本号不大于删除的版本号")
        error.append(git_blame(file_path, line_number).replace('<', ''))

    # 规则2：检查entry行的匹配和删除
    entry_pattern = re.compile(r'<entry name="([^"]+)" type="([^"]+)" version="(\d+)" />')
    entries_to_delete = {}
    entries_to_delete_line = {}
    entries_to_add = {}
    entries_to_add_line = {}

    for line in file_changes.split('\n'):
        if line.startswith('-'):
            match = entry_pattern.search(line)
            if match:
                name, type_, version = match.groups()
                entries_to_delete[(name, type_)] = version
                entries_to_delete_line[(name, type_)] = line.replace('-', '')
        elif line.startswith('+'):
            match = entry_pattern.search(line)
            if match:
                name, type_, version = match.groups()
                entries_to_add[(name, type_)] = version
                entries_to_add_line[((name, type_))] = line.replace('+', '')

    # 检查删除的行是否有对应的新增行
    for (name, type_) in entries_to_delete:
        if (name, type_) not in entries_to_add:
            line_number = find_line_number(file_path, entries_to_delete_line[(name, type_)])
            error.append(f"[Error!][{file_path}]")
            error.append("删除的行中没有对应的新增行")
            error.append(git_blame(file_path, line_number).replace('<', ''))
        if entries_to_add[(name, type_)] != entries_to_delete[(name, type_)]:
            line_number = find_line_number(file_path, entries_to_delete_line[(name, type_)])
            error.append(f"[Error!][{file_path}]")
            error.append("现有字段版本号不允许修改")
            error.append(git_blame(file_path, line_number).replace('<', ''))

    # 规则3：检查新增entry行的version字段，version必须小于等于<metalib>的version值，且大于现网发布版本的值
    for (name, type_), version in entries_to_add.items():
        if new_version and int(version) > int(new_version):
            line_number = find_line_number(file_path, entries_to_add_line[(name, type_)])
            error.append(f"[Error!][{file_path}]")
            # error.append(f" name: {name}, type : {type_}")
            error.append(f"version字段值 {version} 不小于等于<metalib>的version值 {new_version}\n" +
                         f"name: {name}, type : {type_}")
            error.append(git_blame(file_path, line_number).replace('<', ''))
        if new_version and int(version) < int(old_version) and int(version) != 1:
            line_number = find_line_number(file_path, entries_to_add_line[(name, type_)])
            error.append(f"[Error!][{file_path}]")
            # error.append(f" name: {name}, type : {type_}")
            error.append(f"version字段值 {version} 不大于等于<metalib>的version值 {new_version}\n" +
                         f" name: {name}, type : {type_}")
            error.append(git_blame(file_path, line_number).replace('<', ''))
    for reason in error:
        print(f"{reason}")
        print("\n")
    return error


# tlog文件的兼容性要求
# 1、新增字段必须在struct结构的最后，不允许在字段中间添加
# 规则1的实现逻辑可以看if主要通过遍历增加行来进行判断，可能存在误判，目前的测试用例没发现问题，可以自行检查一下
# 2、允许修改字段的size和type但不允许修改name和desc字段
# 3、不允许删除任何字段，如果需要修改字段的name和desc请创建一个新字段
def check_file_changes_tlog(file_changes, file_path):
    error = []
    entry_pattern = re.compile(r'desc="([^"]+)" name="([^"]+)"')

    # 提取所有删除和添加的entry
    deleted_entries = {}
    added_entries = []  # 使用列表来存储增加行的内容，方便后续检查
    add_lines = []

    lines = file_changes.split('\n')
    for i, line in enumerate(lines):
        if line.startswith('-'):
            match = entry_pattern.search(line)
            if match:
                desc, name = match.groups()
                deleted_entries[(desc, name)] = line
        elif line.startswith('+'):
            match = entry_pattern.search(line)
            if match:
                desc, name = match.groups()
                added_entries.append((line, i))  # 存储增加行及其索引
    # 检查每个增加的entry的上下文
    for added_line, line_index in added_entries:
        # 检查上一行是否合法
        prev_line = lines[line_index - 1] if line_index > 0 else None
        if prev_line and not prev_line.startswith('+') and not (
                prev_line.strip().endswith('</struct>') and not prev_line.startswith('+')) and not (
                prev_line.strip().endswith(' </macrosgroup>') and not prev_line.startswith('+')):
            # 检查下一行是否合法
            next_line = lines[line_index + 1] if line_index + 1 < len(lines) else None
            if next_line and not (next_line.startswith('+') or (
                    next_line.strip().endswith('</struct>') and not next_line.startswith('+')) or (
                                          next_line.strip().endswith('</macrosgroup>') and not next_line.startswith(
                                      '+'))):
                # 检查是否有对应的删除行
                match = entry_pattern.search(added_line)
                if match:
                    desc, name = match.groups()
                    if (desc, name) not in deleted_entries:
                        line_number = find_line_number(file_path, added_line.replace('+', ''))
                        error.append(f"[Error!][{file_path}]")
                        error.append("修改或增加不在struct的最后")
                        error.append(f"{git_blame(file_path, line_number).replace('<', '').replace('>', '')}")
                else:
                    error.append(f"[Error!][{file_path}]")
                    error.append("格式错误，无法提取desc和name")
                    error.append(f"{git_blame(file_path, line_index).replace('<', '').replace('>', '')}")
    for line, i in added_entries:
        add_lines.append(line.replace('+', ''))
    for (name, desc), deleted_line in deleted_entries.items():
        if deleted_line.replace('-', '') not in add_lines:
            line_number = find_line_number(file_path, deleted_line.replace('-', ''))
            error.append(f"[Error!][{file_path}]")
            error.append("存在非法删除或修改操作")
            error.append(f"{git_blame(file_path, line_number).replace('<', '').replace('>', '')}")
    for errors in error:
        print(errors)
    # 如果有错误，返回错误列表；否则返回空列表
    return error


def tcaplus_length_check(file_path):
    error = []
    with open(file_path, 'r', encoding='utf-8') as f:
        for i, line in enumerate(f, 1):
            matches = re.findall(r'name="([^"]*)"', line)
            for match in matches:
                if len(match) >= 32:
                    error.append(f"[Error!][{file_path}]")
                    error.append("表名必须小于32个字符")
                    error.append(git_blame(file_path, i).replace('<', ''))
    return error


def resconfig_pbinname_check(file_path):
    error = []
    file_object = open(file_path)
    ori_xml = file_object.read()
    file_object.close()
    pro_xml = ori_xml.replace("utf-8", "gb2312")
    root = ET.fromstring(pro_xml)

    # 打印根元素的标签
    for child in root:
        pbinname_set = set()
        pbinname = child.attrib['pbinname']
        if pbinname in pbinname_set:
            error.append(f"[Error!][{file_path}]")
            error.append("pbinname重复")
            with open(file_path, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f, 1):
                    if pbinname in line:
                        error.append(git_blame(file_path, i).replace('<', ''))
        pbinname_set.add(pbinname)
    return error


# 获取分支最新的commit
def get_latest_commit(branch_name):
    # 获取分支最新的commit
    result = subprocess.run(['git', 'rev-parse', branch_name], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    if result.returncode != 0:
        # 输出错误信息
        print(result.stderr.decode('utf-8').strip())
        print(f"无法获取分支{branch_name}的最新commit")
    return result.stdout.decode('utf-8').strip()


# 校验xml文件id是否一致
def check_xmlid(commit_list1, filepath, branch_list):
    commit_map = {}
    commit_map_list = []
    error = []
    filename = os.path.basename(filepath)
    for commit_id in commit_list1:
        git_checkout_force(commit_id)
        delay(1)
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()  # 读取文件内容
            pattern = re.compile(r'<entry.*?id="(.*?)".*?name="(.*?)".*?/>')
            matches = pattern.findall(content)
            for match in matches:
                id, name = match
                commit_map[id] = name
        commit_map_list.append(commit_map.copy())
        matches.clear()
        commit_map.clear()
    first_commit_map = commit_map_list[0]
    for id, name in first_commit_map.items():
        # 检查其他三个commit_map中是否存在相同的id且name一致
        for i in range(1, 4):
            other_map = commit_map_list[i].copy()
            if (id in other_map) and (other_map[id] != name):
                error.append(filename)
                error.append(f"{branch_list[0]}提交冲突[id]{id}[name]{name}")
                error.append(f"与{branch_list[i]}[id]{id}[name]{other_map[id]}")
            other_map.clear()
    return error


from typing import List


def extract_commit_and_line(data, new_error_list, branch):
    for sublist in data:
        # 确保长度是3的倍数
        if len(sublist) % 3 != 0:
            continue

        for i in range(0, len(sublist), 3):
            blame_line = sublist[i + 2]

            # 提取commit号
            parts = blame_line.split(' ', 1)
            if len(parts) < 2:
                continue
            commit = parts[0]

            # 找第一个右括号')'的位置，内容从它后面开始
            right_paren_index = blame_line.find(')')
            if right_paren_index == -1:
                continue
            line_text = blame_line[right_paren_index + 1:].strip()

            # 提取文件名
            # 先用正则提取中括号内的路径（取最后一个中括号内的内容）
            matches = re.findall(r'\[([^\[\]]+)\]', sublist[0])
            if not matches:
                proto_filename = sublist[0]  # 如果没匹配到，保留原值
            else:
                # 取最后一个中括号内的内容
                proto_path = matches[-1]
                # 提取文件名
                proto_filename = os.path.basename(proto_path)

            if redis_handle(proto_filename, commit + line_text + branch):
                new_error_list.append([sublist[i],sublist[i+1],sublist[i+2]])

def redis_init():
    global redis_conn
    if redis_conn is not None:
        return redis_conn
    try:
        redis_conn = Redis_set.Redis_cil(3)
    except Exception as e:
        # 捕获其他初始化异常（如连接超时、认证失败等）
        print(f"Redis initialization failed: {str(e)}")
        sys.exit(1)
    return redis_conn


def redis_handle(proto, text):
    global redis_conn
    error_new_list = []
    return redis_conn.redis_get_proto_commit(proto, text)

def get_current_branch():
    try:
        # 调用 git rev-parse --abbrev-ref HEAD 获取当前分支名
        branch = subprocess.check_output(
            ['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
            stderr=subprocess.DEVNULL,
            universal_newlines=True
        ).strip()
        return branch
    except subprocess.CalledProcessError:
        return None  # 不是 git 仓库或出错


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='检查两个git commit之间的差异或两个分支的最新commit')
    group = parser.add_mutually_exclusive_group(required=False)
    parser.add_argument('-s', '--is_send', type=bool,
                        help='警告上报开关', default=False)
    parser.add_argument('-x', '--xml_idcheck', type=bool,
                        help='xml的id检查', default=False)
    parser.add_argument('-ab', '--add_bug', type=bool,
                        help='自动提bug单功能', default=False)
    parser.add_argument('-v', '--version', help='自动提单的发布版本')
    parser.add_argument('-xb', '--xbranches', nargs=2, metavar=('XBRANCH1', 'XBRANCH2'),
                        help='输入两个分支名，和xml文件id对比，一般是版本DEV和RELEASE')
    group.add_argument('-b', '--branches', nargs=2, metavar=('BRANCH1', 'BRANCH2'),
                       help='输入两个分支名，获取这两个分支的最新commit')
    group.add_argument('-c', '--commits', nargs=2, metavar=('COMMIT1', 'COMMIT2'),
                       help='输入两个git commit哈希值')
    parser.add_argument('-a', '--whitelist', nargs='+', help='输入文件名白名单')
    args = parser.parse_args()
    white_file_list = []
    commit1 = "0"
    commit2 = "0"
    global branch1
    if args.whitelist:
        white_file_list = args.whitelist
    if args.branches:
        branch1, branch2 = args.branches
        commit1 = get_latest_commit(branch1)
        commit2 = get_latest_commit(branch2)
        print(f"commit1:{commit1}")
        print(f"commit2:{commit2}")
    elif args.commits:
        commit1, commit2 = args.commits
    else:
        # 获取用户输入的两个commit哈希值
        print("先输入较旧的commit哈希值再输入较新的commit哈希值，如果需要直接用分支名对比，可以使用-b命令")
        commit1 = input("请输入第一个较旧commit的哈希值: ")
        commit2 = input("请输入第二个较新commit的哈希值: ")

    # 比较两个commit并筛选包含特定文件名的路径
    filtered_file_paths = compare_commits_files_by_name(commit1, commit2)

    # 存放错误的JSON和html
    split_commit1 = commit1[:6]
    split_commit2 = commit2[:6]

    new_path = "./tools/detection_result"  # 这里指的是你希望创建的文件夹的完整路径
    if not os.path.exists(new_path):
        os.makedirs(new_path)
    json_error_file = f'./tools/detection_result/detection_result.json'
    html_error_file = f'./tools/detection_result/detection_result.html'
    json_error_file_xml = f'./tools/detection_result/detection_result_xml.json'
    html_error_file_xml = f'./tools/detection_result/detection_result_xml.html'
    json_error_file_new = f'./tools/detection_result/detection_result_new.json'
    html_error_file_new = f'./tools/detection_result/detection_result_new.html'
    # 提单开关
    add_bug = args.add_bug
    # 提单版本
    bug_version = args.version
    # 上报警告库开关
    is_send = args.is_send
    xml_idcheck = args.xml_idcheck

    # xml_id检查开关

    # errors存放所有的违规警告
    error = []
    errors = []
    errors_xml = []
    new_errors = []
    xmlid_iserror = False
    cur_branch = get_current_branch()

    # 先检查tcaplus的表名
    if not xml_idcheck:
        # checkout_commit(commit1)
        git_checkout_force(branch1)
        tcaplus_path = "protos/db_proto/proto/tcaplus_db.xml"
        error = tcaplus_length_check(tcaplus_path)
        errors.append(error)
        resconfig_path = "excel/proto/resConfig.xml"
        error = resconfig_pbinname_check(resconfig_path)
        errors.append(error)

    # redis已经统一生成了,这部分逻辑不做改动
    if xml_idcheck:
        branch3, branch4 = args.xbranches
        print("branch3" + branch3 + "branch4" + branch4)
        commit3 = get_latest_commit(branch3)
        commit4 = get_latest_commit(branch4)
        commit5 = get_latest_commit("develop")
        # checkout_commit(commit1)
        path_list = {"protos/cs_proto/cs_msg_types.xml", "protos/ss_proto/rpc_msg_types.xml",
                     "protos/msg_types/ds_msg_types.xml"}
        # 目标分支删除检测，不允许删除任何行字段
        for file_path in path_list:
            file_changes = compare_commits_file_changes(commit1, commit2, file_path)
            filename = os.path.basename(file_path)
            error = check_file_changes_proto(file_changes, filename, file_path)
            errors.append(error)
            if error:
                xmlid_iserror = True
            elif error:
                print(f"[Info]  {filename}满足兼容性要求")
        # id 匹配性检测，develop DEV和Release分支交叉检测
        commit_list1 = []
        commit_list1.append(commit2)
        commit_list1.append(commit3)
        commit_list1.append(commit4)
        commit_list1.append(commit5)
        print(commit_list1)
        for file_path in path_list:
            error = check_xmlid(commit_list1, file_path, branch_list=[branch2, branch3, branch4, "develop"])
            errors.append(error)
            if error:
                xmlid_iserror = True
            elif error:
                print(f"[Info]  {filename}满足兼容性要求")

    # 打印筛选出的文件路径
    elif filtered_file_paths:
        # checkout_commit(commit1)
        git_checkout_force(branch1)
        for path in filtered_file_paths:
            print("找到的文件路径" + path)
            if "protos/event/event.xml" in path:
                continue
            # 比较不同文件的异同点
            # TODO 这里的几个xml也要用逻辑检查
            if (
                    path == "protos/event/event_global_cfg.xml" or path == "protos/event/event.xml" or path == "protos/event/submode/letsgo_event.xml"):
                continue
            try:
                file_changes = compare_commits_file_changes(commit1, commit2, path)
                print(file_changes)
            except Exception as e:
                print(f"Warning: compare_commits_file_changes 出错，跳过该次，错误信息: {e}")
                continue
            filename = os.path.basename(path)
            if filename in white_file_list:
                continue
            lines = file_changes.split('\n')
            if "+++ /dev/null" in lines:
                print(f"[Info]  {filename} has been Delete!")
                continue
            if not file_changes:
                print("在两个commit之间没有找到需要检查的文件修改内容。")
            # 检查路径是否以.proto结尾
            if path.endswith(
                    '.proto') or path == "protos/ss_proto/rpc_msg_types.xml" or path == "protos/cs_proto/cs_msg_types.xml" or path == "protos/sr_proto/sr_msg_types.xml":
                # 提取文件名（不包括路径）
                error = check_file_changes_proto(file_changes, filename, path)
                if path.endswith('.proto'):
                    errors.append(error)
                else:
                    errors_xml.append(error)
                if not error:
                    print(f"[Info]  {filename}满足兼容性要求")
            elif path == "protos/db_proto/proto/tcaplus_db.xml":
                error = check_file_changes_xml(file_changes, path)
                errors_xml.append(error)
                if not error:
                    print("[Info]  tcaplus_db.xml文件满足兼容性要求")
            elif path == "protos/tlog_proto/tlog.xml":
                error = check_file_changes_tlog(file_changes, path)
                errors_xml.append(error)
                if not error:
                    print("[Info]  xml文件满足兼容性要求")
    else:
        print("在两个commit之间没有找到包含指定文件名的文件。")
    # 检查完成，检查结果处理
    if errors or errors_xml:
        redis_cil = redis_init()
        if is_send:
            for error_lines in errors:
                if error_lines:
                    print(', '.join(error_lines))
                    send_http(', '.join(error_lines))
            for error_lines in errors_xml:
                if error_lines:
                    print(', '.join(error_lines))
                    send_http(', '.join(error_lines))
        if errors:
            extract_commit_and_line(errors, new_errors, branch2)
            json_data = {'errors': errors}
            data2json(json_data, json_error_file)
            json2html(json_error_file, html_error_file)
        else:
            gen_html(html_error_file)
        if errors_xml:
            extract_commit_and_line(errors_xml, new_errors, branch2)
            json_data_xml = {'errors': errors_xml}
            data2json(json_data_xml, json_error_file_xml)
            json2html(json_error_file_xml, html_error_file_xml)
        else:
            gen_html(html_error_file_xml)

        if new_errors:
            print(f"new errors: {new_errors}")
            json_data_new = {'errors': new_errors}
            data2json(json_data_new, json_error_file_new)
            json2html(json_error_file_new, html_error_file_new)
            # try:
                # 自动提单功能
            if add_bug and bug_version:
                print("开始tapd提单切换分支")
                git_checkout_force(cur_branch)
                tapd = TAPDUtil()
                tapd.add_bug_split(new_errors, bug_version, args.branches)
            # except Exception as e:
            #     logging.error(f"errors add_bug exception: {str(e)}")
            #     print('[Exception]' + str(e))
        else:
            gen_html(html_error_file_new)
        print("json和HTML文件已生产")
        if xmlid_iserror:
            exit(1)
