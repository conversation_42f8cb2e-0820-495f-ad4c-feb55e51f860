import json
import requests
from collections import defaultdict
import subprocess
import difflib

# TAPD开发者账号
config = {
    "tapd_api_user": "JKLuoTools",
    "tapd_api_password": "B9A26AD4-B693-D1D4-856B-1D5C2A1E2712",
    "tapd_workspace_id": 20427612
}


def get_compare_view(branch1, branch2, file_path, baseline):
    '''
    得到diff的html显示内容
    :param file_path:
    :param baseline: 基本html
    :param branch1:
    :param branch2:
    :return:
    '''
    oldfileline = git_show_content(branch1, file_path).splitlines()
    newfileline = git_show_content(branch2, file_path).splitlines()

    # todo: 把标题改成两次的commit号
    html_diff = difflib.HtmlDiff().make_file(oldfileline, newfileline, "OldContent", "NewContent",
                                             context=True, numlines=5).replace("ISO-8859-1", "utf-8")

    # 无差异过滤
    if html_diff.find("No Differences Found") != -1:
        return None
    # 将差异写入html文件
    # with open("tools/detection_result/comparison.html", "w", encoding="utf8") as f:
    #     f.write(html_diff)

    # 增量显示方法 太长了不考虑
    # html_diff2 = difflib.ndiff(oldfileline, newfileline)

    description = baseline
    # 获取diff视图
    body_start = html_diff.find('<body>')
    if body_start != -1:
        description = html_diff[:body_start] + baseline + html_diff[body_start:]

    return description


def git_show_diff(commit, file_path):
    # 执行git命令
    git_command = f"git show {commit} -- {file_path}"
    process = subprocess.Popen(git_command.split(), stdout=subprocess.PIPE)
    output, error = process.communicate()

    if error:
        print(f"发生错误: {error}")
    else:
        return output.decode()


def git_show_content(commit, file_path):
    # 执行git命令
    git_command = f"git show {commit}:{file_path}"
    process = subprocess.Popen(git_command.split(), stdout=subprocess.PIPE)
    output, error = process.communicate()

    if error:
        print(f"发生错误: {error}")
    else:
        return output.decode()


def get_file_diff(commit, file_path):
    # 用最新的commit id 和 本次提交bug的commit id，获取文件修改内容
    # 构造Git命令，使用标准格式获取文件的具体修改内容
    command = ["git", "diff", commit, "--", file_path]

    # 调用Git命令并获取输出
    result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)

    if result.returncode != 0:
        # 输出错误信息
        print(result.stderr.decode('utf-8').strip())
        return None
    return result.stdout.decode('utf-8').strip()

def append_error(errors_dict, file_path, reason, blame, owner):
    new_reason_blame = reason + "     " + blame
    new_owner = owner.strip()

    if file_path not in errors_dict:
        errors_dict[file_path] = (new_reason_blame, new_owner)
    else:
        old_reason_blame, old_owner = errors_dict[file_path]

        # 追加 reason+blame
        combined_reason_blame = old_reason_blame + new_reason_blame

        # 处理 owner 去重追加
        old_owners_set = set(o.strip() for o in old_owner.split(';') if o.strip())
        old_owners_set.add(new_owner)
        combined_owner = ';'.join(sorted(old_owners_set))  # sorted 可选，保证顺序一致

        errors_dict[file_path] = (combined_reason_blame, combined_owner)

class TAPDUtil:
    def __init__(self, auth=None):
        if auth is not None:
            self.auth = auth

        self.auth = (config["tapd_api_user"], config["tapd_api_password"])

    def add_bug_split(self, errors, bug_version, branches):
        '''
        解析拆分check得到的error信息，拆分提单
        :param branches: 比较分支
        :param bug_version: 发布版本
        :param errors: check得到的error信息list
        :return:
        '''
        # 创建二级嵌套的defaultdict
        errors_dict = defaultdict(lambda: defaultdict(dict))

        for error_list in errors:
            if len(error_list) == 0:
                continue
            # 每个error都是单独文件
            for i in range(0, len(error_list), 3):
                # error列表内容三个为一组
                error_group = error_list[i:i + 3]
                file_path = error_list[0].split('[')[-1].split(']')[0]
                reason = error_group[1]
                blame = error_group[2]
                commithash = blame.split(' ')[0].strip()
                if not commithash or commithash.strip() == '':
                    continue
                owner = blame.split('(')[1].split(' ')[0]
                # 空key 数据结构处理
                append_error(errors_dict, file_path, reason, blame, owner)
            # break
        print(f"errors_dict: {errors_dict}")
        for key,value in errors_dict.items():
            if value[1] == '':
                continue
            owners = value[1].split(';')  # 根据分号拆分多个处理人
            for owner in owners:
                owner = owner.strip()  # 去除首尾空白
                if not owner:
                    continue
                data = {
                    "file_path": key,
                    "blame": value[0],
                    # 构造内容
                    "workspace_id": config["tapd_workspace_id"],  # 项目ID
                    "title": "【ERROR 兼容检查自动提单】" + key + f" {branches[0]} <=> {branches[1]} ",
                    # 缺陷标题 加上 协议兼容关键字
                    "priority_label": "high",  # 优先级,也可以在这限定死 high
                    "severity": "serious",  # 严重程度,也可以在这限定死 serious
                    "current_owner": owner,  # 处理人
                    "version_report": bug_version,  # 发现版本
                }
            # 提单
                self.add_bug(data, branches)

                # debug使用
                # bugs = self.get_bug(data)
                # if bugs or len(bugs) > 0:  # 存在过滤
                #     print(f"{data['file_path']} {data['commit']},已经提过bug >> bugId: {bugs[0]['Bug']['id']}")
                #     return None
                # else: print(f"{data['file_path']} {data['commit']} 提单成功")

    # @staticmethod 考虑静态方法的话auth不太优
    def add_bug(self, data, branches):
        '''
        创建缺陷
        :param data:
        :param branches:
        :return:
        '''
        # 如果这个commit已经提过bug了，就不用再提了
        # bugs = self.get_bug(data, branches)
        # if bugs or len(bugs) > 0:  # 存在过滤
        #     print(
        #         f"{data['file_path']} {branches[0]} <=> {branches[1]} ,已经提过bug >> bugId: {bugs[0]['Bug']['id']}")
        #     return None

        # 要get一下发布id
        release = self.get_new_releases(data)
        # 先检查release是否为空，再检查是否有数据
        if not release or not release[0].get("Release", {}).get("id"):
            return None
        release_id = int(release[0]["Release"]["id"])

        # 要get一下迭代id
        iteration = self.get_iterations(data)
        if not iteration or not iteration[0].get("Iteration", {}).get("id"):
            return None
        iteration_id = iteration[0]["Iteration"]["id"]

        # 要get一下自定义字段id
        # custom_fields = self.getBugCustomFieldsSettings(data)
        # if not custom_fields or not custom_fields[0].get("CustomFields", {}).get("id"):
        #     return None
        # custom_fields_id = custom_fields[0]["CustomFields"]["id"]

        branch1, branch2 = branches
        blame_html = data['blame'].replace('\n', '<br>')
        baseline = f'''
        <h1>变更检查错误报告</h1>
        <div>
            <p>此单为新功能提交，未出现此行的兼容性检查单可全部忽略，目前没有拒绝权限，如果没问题点击已解决,同时在评论区评论该协议所属模块方便QA回归测试 </p>
            <p>比较分支: {branch1} <=> {branch2} </p>
            <p>文件路径: {data["file_path"]}</p>
            <p>错误原因和提交信息: {blame_html}</p>
        </div>
        <h2><span class="fas fa-external-link-alt"></span>解决方案参考：<a href="https://iwiki.woa.com/p/4010317476" target="_blank">https://iwiki.woa.com/p/4010317476</a></h2>
        <h2>差异内容</h2>
        '''

        description = get_compare_view(branch1, branch2, data["file_path"], baseline)
        if description is None:
            print(f"无差异！{branch1} <=> {branch2} : [{data['file_path']}]")
            return

        params = {
            "workspace_id": data["workspace_id"],  # 项目ID
            "title": data["title"],  # 缺陷标题 加上 协议兼容关键字
            "description": description,  # 详细描述
            "priority_label": data["priority_label"],  # 优先级,也可以在这限定死 high
            "severity": data["severity"],  # 严重程度,也可以在这限定死 serious
            "iteration_id": iteration_id,  # 迭代ID
            "release_id": release_id,  # 发布计划
            "version_report": data["version_report"],  # 发现版本
            # "current_owner": "jinkunluo",
            "current_owner": f"{data['current_owner']}",  # 处理人
            "cus_Owner": f"{data['current_owner']}",  # 自定义字段-负责人
            # "template_id" : 1,             #模板ID ,后续可以考虑标准流程
            "reporter": "DevOpsTool",  # 报告人
            "te": "sheenaliu",
            # "reporter": config["tapd_api_user"],  # 报告人
        }
        # 创建过程
        url = f'http://apiv2.tapd.woa.com/bugs'
        # 接口+commit号
        response = requests.post(url, data=params, auth=self.auth)  # post要使用json参数
        if response.status_code == 200:
            result = response.json()
            print(f"bug创建成功! {result['data']['Bug']['title']}:{result['data']['Bug']['id']}")
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None

    def get_bug(self, data, branches, status=''):
        url = f'http://apiv2.tapd.woa.com/bugs'
        title = data["file_path"] + f" {branches[0]} <=> {branches[1]} "
        params = {
            "workspace_id": data["workspace_id"],
            "title": title.strip(),  # 支持模糊匹配
        }
        if status:
            params['status'] = status

        response = requests.get(url, params=params, auth=self.auth)
        if response.status_code == 200:
            result = response.json()
            # print(data)
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None

    def get_new_releases(self, data):
        url = f'http://apiv2.tapd.woa.com/new_releases'
        params = {
            "workspace_id": data["workspace_id"],
            "name": data["version_report"],
            "limit": 200,
        }
        # count = self.get_new_releases_count(data)
        response = requests.get(url, params=params, auth=self.auth)
        if response.status_code == 200:
            result = response.json()
            # print(data)
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None

    def get_new_releases_count(self, data):
        url = f'http://apiv2.tapd.woa.com/new_releases/count'
        params = {
            "workspace_id": data["workspace_id"],
        }
        response = requests.get(url, params=params, auth=self.auth)
        if response.status_code == 200:
            result = response.json()
            # print(data)
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None

    def get_iterations(self, data):
        url = f'http://apiv2.tapd.woa.com/iterations'
        params = {
            "workspace_id": data["workspace_id"],
            "name": f'{data["version_report"]}版本迭代',
            "limit": 200,
        }
        response = requests.get(url, params=params, auth=self.auth)
        if response.status_code == 200:
            result = response.json()
            # print(data)
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None

    # 获取自定义字段
    def getBugCustomFieldsSettings(self, data):
        url = f'http://apiv2.tapd.woa.com/bugs/custom_fields_settings'
        params = {
            "workspace_id": data["workspace_id"],
        }
        response = requests.get(url, params=params, auth=self.auth)
        if response.status_code == 200:
            result = response.json()
            # print(data)
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None

    # 返回缺陷所有字段及候选值(枚举值)
    def get_fields_info(self, data):
        url = f'http://apiv2.tapd.woa.com/bugs/get_fields_info'
        params = {
            "workspace_id": data["workspace_id"],
        }
        response = requests.get(url, params=params, auth=self.auth)
        if response.status_code == 200:
            result = response.json()
            # print(data)
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None

    # 获取未处理缺陷
    def get_unhandled_bug(self):
        url = f'http://apiv2.tapd.woa.com/bugs'
        params = {
            "workspace_id": config["tapd_workspace_id"],
            "title": "【ERROR 兼容检查自动提单】",
            "status": "new",
        }

        response = requests.get(url, params=params, auth=self.auth)
        if response.status_code == 200:
            result = response.json()
            # print(data)
            return result['data']
        else:
            print('请求失败，状态码：', response.text)
        return None


if __name__ == '__main__':
    # 可外接延伸
    # import json
    # with open("config.json", "r", encoding="utf-8") as f:
    #     config = json.load(f)
    TAPD_AUTH = (config["tapd_api_user"], config["tapd_api_password"])
    TAPD_WORKSPACE = config["tapd_workspace_id"]
    tapd = TAPDUtil()

    bug_data = {
        "workspace_id": TAPD_WORKSPACE,  # 项目ID
        "title": "文件名 + commit号 + 问题",  # 缺陷标题 加上 协议兼容关键字
        "description": "把error给他",  # 详细描述
        "priority_label": "high",  # 优先级
        "severity": "serious",  # 严重程度
        "reason": "存在非法删除或修改操作",
        "release_id": 1020427612002038331,  # 发布计划,得用原story单拿一下
        "version_report": "S10",  # 发现版本
        "current_owner": "jinkunluo;jankinye",  # 处理人
        # "template_id" : 1,             #模板ID ,后续可以考虑标准流程
        "reporter": "jinkunluo;jankinye",  # 报告人
        "file_path": "excel/server/proto/ResActivity.proto",
        "commit": "76e37a9597e6 ",
    }

    # get_compare_view("8ee2bead9241", "excel/server/proto/ResArenaSkill.proto", "")

    # with open("./tools/detection_result/detection_result.json", "r", encoding="utf-8") as f:
    #     data = json.load(f)
    #     tapd.add_bug_split(data["errors"], bug_data["version_report"], ['origin/s10-dev', 'origin/develop'])
    # print(tapd.get_unhandled_bug())
    # print(tapd.getBugCustomFieldsSettings(bug_data))
    # print(tapd.get_new_releases(bug_data))
    # print(tapd.get_bug(bug_data))
    # print(tapd.get_iterations(bug_data))
    branchs = ["s12-release","develop"]
    print(tapd.add_bug(bug_data,branchs))
    # field_status = tapd.get_field_status("bug", 124123487, CUSTOM_FIELD_NAME, TAPD_WORKSPACE)
    # print(field_status)
