syntax = "proto2";

package com.tencent.wea.protocol;

option java_multiple_files = true;
option java_package = "com.tencent.wea.protocol.common";

import "player_info.proto";

enum EventSendType {
  Default = 0;
  RPC = 1;
  MQ = 2;
}

// 事件基础数据
message EventBaseData {
  oneof data {
    int32 intValue = 1;
    int64 longValue = 2;
    string strValue = 3;
  }
}

// 序列化后的事件数据
message EventSerialData {
  optional int32 eventType = 1;         // 事件类型
  map<int32, bytes> eventData = 2;      // 事件数据
  optional int64 createTimeMs = 3;      // 事件创建时间
}

// 事件额外信息
message EventExtraData {
  optional BasePlayerActivityInfo playerActivityInfo = 1; // 玩家活动信息
}

// 批量发送的事件数据
message EventBatchSendData {
  repeated EventSerialData eventSerialData = 1;  // 事件列表
  optional EventExtraData eventExtraInfo = 2;    // 事件额外信息
  optional string eventUuid = 3;                 // 本次事件发送的唯一ID信息
  optional string topic = 4;                     // 事件所属topic
  optional int64 sendTimeMs = 5;                 // 发送时间
}

// 事件数据id
enum EventDataId {
  EDT_Unknown = 0;
  EDT_Battle = 1;               // 对局数据
  EDT_MatchType = 2;            // 匹配类型
  EDT_MatchModeId = 3;          // 匹配类型组
  EDT_GameModeType = 4;         // 游戏模式类型
  EDT_ShareId = 5;              // 分享id
  EDT_LevelData = 6;            // 关卡数据
  EDT_TaskId = 7;               // 任务id
  EDT_TaskRepeatNum = 8;        // 任务完成次数
  EDT_DrawRaffleFinishData = 9; // 抽奖完成
  EDT_Intimacy = 10;            // 亲密度
  EDT_CupsNum = 11;             // 奖杯数
  EDT_ItemArray = 12;           // 道具列表数据
  EDT_ItemInfo = 13;            // 道具数据
  EDT_PLayUgcMapCommon = 14;    // 游玩UGC地图数据
  EDT_ChatType = 15;            // 聊天类型
  EDT_ContentKey = 16;          // 聊天消息内容Key
  EDT_ClientUiJump = 17;        // 客户端跳转
  EDT_ShopCommodityBuy = 18;    // 购买商品
  EDT_ActivityId = 19;          // 活动id
  EDT_ConnoisseurScore = 20;    // 鉴赏家积分
  EDT_FarmBuilding = 21;        // 农场建筑类型&等级
  EDT_FarmGetFish = 22;         // 农场获得鱼(包括钓鱼和偷鱼)
  EDT_BindPhone = 23;           // 绑定手机号
  EDT_FarmCrop = 24;            // 星农场养植物类别 & 星农场养植物特殊类型
  EDT_OutfitStyleId = 26;       // 服装的风格
  EDT_FarmGrid = 27;            // 农场地块数据
  EDT_FarmCropWater = 28;       // 星宝农场浇水
  EDT_FarmCreateTimeMs = 29;    // 农场创建
  EDT_PlayerRaffle = 30;         // 玩家抽奖
  EDT_ItemArray_Second = 31;         // 道具列表数据
  EDT_QuizId = 32;              // 题组id
  EDT_QuestionId = 33;          // 题目id
  EDT_PlayerLobbyMiniGameType = 34; //大厅迷你游戏类型
  EDT_CostTimeMs = 35;          //耗时
  EDT_BookOfFriendFarmSendGift = 36;  // 花房活动农场赠礼
  EDT_FarmPetFavorUpdate = 37;  // 农场宠物好感度变化
  EDT_FarmPetNum = 38;          // 农场拥有宠物数量
  EDT_FarmPetClothingNum = 39;  // 农场拥有宠物服装数量
  EDT_FarmPetFavor = 40;        // 农场单个宠物好感度
  EDT_ReportCompleteTaskTaskId = 41;  // 客户端上报任务完成-任务id
  EDT_FarmCropRipeQuality = 42; // 星农场养植物成熟品质
  EDT_TeamMemberCount = 43;
  EDT_WolfKillRoleInfoTotalTimes = 44;  //狼人对局数
  EDT_WolfNewPlayer = 45;     //狼人新玩家标记
  EDT_WolfNewPlayerChange = 46;     //狼人新玩家标记是否由0变1
  EDT_FarmVillagerFavorUpdate = 50;  // 农场村民好感度变化
  EDT_FarmVillagerNum = 51;          // 农场拥有村民数量
  EDT_FarmVillagerFavor = 52;        // 农场单个村民好感度
  EDT_ReturningFriendCount = 53;     // 回归好友数量
  EDT_FriendType = 54;               // 好友类型， 1好友，2回归好友
  EDT_FarmCropSpecialFlag = 55;      //星农场养植物特殊类型
  EDT_FarmCropCategory = 56;         //星农场养植物类别
  EDT_RaffleId = 57;                // 抽奖奖池组ID
  EDT_RaffleCategoryId = 58;        // 抽奖奖池组ID
  EDT_RafflePoolId = 59;            // 抽奖奖池ID
  EDT_RaffleBeforeData = 60;          // 抽奖起始信息
  EDT_RaffleAfterData = 61;           // 抽奖结束信息
  EDT_RaffleRewardData = 62;          // 已废弃
  EDT_ET_IncensePrayed = 63;          // 钱王祠香炉祈愿
  EDT_WolfKillCumulativeRoleInfoPoints = 65;           // 狼人专精
  EDT_UgcCommunityOpType = 66;           // 种草社区操作类型
  EDT_RaffleConfirmedRewardIds = 67;           // 抽奖抽中的奖励ID序列
  EDT_FarmBatchOpNum = 70;           // 农场批量操作数量
  EDT_BPInfo = 71;                   // BP信息
  EDT_ScoreActivityAddScore = 72;     //积分活动中新增的积分
  EDT_FarmHotSpringData = 73;         // 温泉交互类型
  EDT_IsXiaowoOwnerData = 74;
  EDT_TreeOpData = 75;
  EDT_LoginPlat = 80;                 // 登录平台
  EDT_MapId = 81;                     // 地图id
  EDT_LobbyData = 82;                 // 广场数据
  EDT_RegisterTime = 83;              // 注册时间
  EDT_FarmFishCardLevel = 84;         // 鱼熟练度等级
  EDT_FarmCoinType = 85;              // 星农场货币类型
  EDT_FarmCropType = 86;              // 星农场养植物类型
  EDT_FarmCropBatchOpNum = 87;        // 星农场批量操作数量
  EDT_FarmEventData = 88;             // 农场事件数据
  EDT_GameSkillId = 89;               // 对局技巧id
  EDT_GameSkillGrade = 90;               // 对局技巧分数
  EDT_GameModel = 91;
  EDT_GameTimesStatData = 100;         // 玩法次数统计信息
  EDT_Uid = 101;
  EDT_PlayerLaunchGiftOrExchangeCards = 102;  //玩家发起赠送或交换卡牌
  EDT_PlayerFinishGiftOrExchangeCards = 103;  //玩家完成赠送或交换卡牌
  EDT_UpgradeCheckInManualData = 104; // 升级打卡手册信息
  EDT_ChaseIdentityMasteryAddScore = 105; // 大王专精增加的分数
  EDT_CardTradeType = 106;             //卡牌交易类型
  EDT_CardShareChannel = 107;          //卡牌交易渠道
  EDT_RankNo = 108; // 排名名次
  EDT_GameplayFeatureId = 109;  // 玩法id
  EDT_GameplayEventData = 110;  // 玩法事件
  EDT_MonthCardId = 111;        // 月卡id
  EDT_MonthCardExpireTimeMs = 112;  // 月卡过期时间戳
  EDT_MonthCardCumDays = 113;     // 月卡累计开通天数
  EDT_VideoId = 114;     // 视频id
  EDT_TotalCups = 115;//总奖杯数
  EDT_FashionLevel = 116;//时尚之路等级
  EDT_GeoLevel = 117;//行政等级
  EDT_ActivityRestaurantThemedFoodSet = 118;     // 集齐美食套数
  EDT_ActivityRestaurantThemedCollectFood = 119;     // 收集菜品
  EDT_VoteCount = 120;     // 投票张数
  EDT_FarmDragonTeamPoints = 121;//农场组队小队积分
  EDT_VideoDuration = 122;     // 观看视频时长（分钟）
  EDT_CupsProgress = 123;             // 奖杯征程进度
  EDT_CupsCycle = 124;             // 奖杯周目
  EDT_FarmCookLevel = 137; // 农场餐厅等级
  EDT_FarmCookScore = 138; // 农场餐厅评分
  EDT_FarmCookUsingSpecialFurnitureNum = 139; // 农场餐厅使用中的功能性家具数量
  EDT_FarmCookUsingEmployeeNum = 140; // 农场餐厅工作中的员工数量
  EDT_ChaseActorId              = 141;  //大王角色Id
  EDT_ChaseAddIdentityProficiency = 142;  //大王增加身份熟练度
  EDT_ChaseBiographyId = 143;//大王解锁的传记ID
}

// 事件
enum EventTypeId {
  // 系统通用 1000-1499
  ET_PlayerLogin = 1000;            // 登录
  ET_PlayerOnline = 1001;           // 在线
  ET_PlayerOnMidnight = 1002;       // 跨天
  ET_PlayerLogout = 1003;           // 登出
  ET_FinishBattle = 1004;           // 完成对局
  ET_ShareGame = 1005;              // 分享
  ET_TaskComplete = 1006;           // 任务完成
  ET_TaskFinish = 1007;             // 任务结束
  ET_DrawRaffleFinish = 1008;	      // 抽奖完成
  ET_IntimacyAdd = 1009;            // 获得亲密度
  ET_PlayerLeave = 1010;            // 玩家离开
  ET_CupsAdd = 1011;                // 获得奖杯
  ET_PlayerGetItem = 1012;          // 获取道具
  ET_PlayerChatMsgSend = 1013;      // 聊天消息发送
  ET_ClientUiJump = 1014;           // 客户端跳转
  ET_PlayerMallCommodityBuy = 1015; // 商品购买
  ET_ActivityAppointment = 1016;    // 活动预约
  ET_BindPhone = 1017;              // 绑定手机号
  ET_PlayerRaffle = 1018;           // 抽奖
  ET_PlayerFinishLobbyMiniGame = 1019; //玩家大厅小游戏事件
  ET_PlayerRaffleDraw = 1020;           // 抽奖抽取
  ET_PlayerRaffleConfirm = 1021;        // 抽奖确认
  ET_PlayerLevelUp = 1022;              // 升级
  ET_PlayerHeartBeat = 1023;            // 心跳
  ET_BPChangeLevel = 1024;          // BP等级变动
  ET_PlayerIntimateRelationSend = 1025; //申请亲密关系
  ET_PlayerMailGiftReward = 1026;    //领取赠送礼物
  ET_PlayerHotResWhiteListChange = 1027;    //HotResWhiteList白名单变更
  ET_LaunchGiftOrExchangeCards = 1028;  //玩家发起赠送或交换X次卡牌
  ET_FinishGiftOrExchangeCards = 1029;  //玩家完成赠送或交换X次卡牌
  ET_PlayerBattleLike = 1030; // 对局结算点赞
  ET_AddGameFriend = 1031; // 添加游戏好友
  ET_DeleteGameFriend = 1032; // 删除游戏好友
  ET_IceBreakOperatorSystemLimit = 1033; //破冰礼包wx ios 限制
  ET_IceBreakMissionLimit = 1034; //破冰礼包任务限制
  ET_PlayerViewVideo = 1035;   // 观看视频
  ET_PlayerOnloadTime = 1036;  // 玩家登入时间
  ET_TaskRewardCompensate = 1037;  // 任务奖励补领
  ET_FinishBattleWolfTeam = 1038;           //狼人组队活动与小队成员完成对局
  ET_CupsProgress = 1040;                // 奖杯周目进度
  ET_PlayerCostCurrency = 1041;       //玩家消耗货币x个
  ET_TaskProgressChange = 1042;       //玩家任务改变

  // 活动相关 1500-1999
  ET_CreateOutfit = 1500;              // 生成服装
  ET_GetOutfit = 1501;                 // 获取服装
  ET_ChangeOutfit = 1502;              // 换装
  ET_UseOutfitItem = 1503;              // 使用换装道具
  ET_FinishQuiz = 1504;                 // 完成答题
  ET_FinishQuestion = 1505;             // 完成指定题目id答题
  ET_BookOfFriendFarmSendGift = 1506;   // 花房活动农场赠礼
  ET_TeamMemberCount = 1507;
  ET_WolfKillRoleInfoTotalTimes = 1508; // 狼人对局数
  ET_ReturningFriend = 1509;            // 回归好友
  ET_StaringPlayer = 1510;              // 点赞好友(点赞的动作)
  ET_IncensePrayed = 1511;              //钱王祠香炉祈愿
  ET_WolfKillCumulativeRoleInfoPoints = 1513;// 狼人专精点数
  ET_WholeGameProcessPoint = 1514;      // 全服进度积分
  ET_RequestLuckStarAssist = 1516;      // 邀请福星助力
  ET_GiveOrRequestSlip = 1517;      // 赠与或者邀请福签
  ET_AddScoreActivityScoreEvent = 1518; //积分活动增加积分
  ET_AddScoreActivityTaskScoreEvent = 1519;//积分活动任务监听
  ET_PlayerVoteMoba = 1520;              //峡谷彩蛋局玩家投票
  ET_PasswordCodeUse = 1525;      // 口令码使用事件
  ET_PasswordCodeBeUsed = 1526;      // 口令码被使用事件
  ET_UpgradeCheckInManual = 1527;       // 升级打卡手册
  ET_WolfReturnReceiveInvitation = 1528;       // 接收狼人召回活动邀请
  ET_FashionLevelChangeEvent = 1529;
  ET_PlayerJoinClubTimeRefresh = 1530;        //玩家加入社团时间刷新
  ET_ActivityRestaurantThemedFoodSet = 1531;    // 美食套数
  ET_ActivityRestaurantThemedCollectFood = 1532;// 收集菜品
  ET_ActivityFlashRaceCheeringVote = 1533;// 闪电赛助威投票
  ET_ActivityMemberPointsUpdate = 1534;// 活动服小队队员积分更新
  ET_UpgradeFarmDailyAwardBigAward = 1535 ; // 农场天天领
  ET_PlayerTradingCardHasReward = 1536;   //卡牌奖杯循环条有奖励
  ET_PlayerReadChaseIdentityBiography = 1537; // 玩家阅读大王身份传记
  // 广场相关 2000-2099
  ET_PlayerLobbyData = 2000;              // 大厅数据上报

  // 对局相关 2100-2199
  ET_BattleData = 2100;         // 对局数据
  ET_FinishLevel = 2101;        // 完成对局
  ET_FinishSkill = 2102;        // 完成技巧
  ET_FinishSkillGrade = 2103;   // 技巧分数

  // 小窝/农场 2200-2299
  ET_FarmCropWater = 2200;      // 星宝农场浇水
  ET_FarmBuildingLevelUp = 2201;// 农场建筑升级
  ET_FarmFishing = 2202;        // 农场钓鱼
  ET_FarmStealingFish = 2203;   // 农场偷鱼/农场炸鱼
  ET_FarmCropPlant = 2204;      // 农场播种
  ET_FarmGridNum = 2205;        // 农场地块
  ET_FarmCreate = 2206;         // 农场创建
  ET_SendFarmGift = 2207;       // 农场赠送
  ET_LeaveFarmMessage = 2208;   // 农场留言
  ET_FarmPetInteract = 2209;    // 农场宠物互动
  ET_FarmPetFavorUpdate = 2210; // 农场宠物好感度变化
  ET_FarmPetNum = 2211;         // 农场宠物拥有数量
  ET_FarmPetClothingNum = 2212; // 农场宠物服装拥有数量
  ET_FarmReceivePetGift = 2213; // 农场收到宠物礼物
  ET_ReportCompleteTask = 2214; // 玩家上报完成任务
  ET_FarmCropHarvest = 2215;    // 星农场进行X次收获
  ET_FarmVillagerFavorUpdate = 2220;    // 农场村民好感度变化
  ET_FarmVillagerNum = 2221;            // 农场村民拥有数量
  ET_FarmVillagerAcceptGift = 2222;     // 农场村民收到礼物
  ET_FarmVillagerPresentGift = 2223;    // 农场村民赠送礼物
  ET_FarmCropFertilizeNum = 2224;       // 农场施肥X次
  ET_FarmRoomCropMachinePutIn = 2225;   // 农场小屋加工器加工
  ET_PlayerFarmRoomVisitOther = 2226;   // 农场拜访其他人的小屋
  ET_PlayerFarmSteal = 2227;            // 星农场偷菜
  ET_FarmHotSpring = 2228;              // 温泉交互事件
  ET_PlayerFarmVisitOther = 2229;       // 星农场拜访其他人
  ET_FarmFishCardLevelUpEvent = 2230;   // 农场鱼卡等级提升
  ET_PlayerFarmFishHandBookUpdate = 2231; // 解锁X种鱼的图鉴
  ET_PlayerFarmCropBaiHuaQiFang = 2232; // 星农场养植物百花齐放
  ET_PlayerFarmCropShiQuanShiMei = 2233;// 星农场养植物十全十美
  ET_PlayerFarmEvict = 2234;            // 星农场驱逐
  ET_PlayerFarmFriend = 2235;           // 星农场好友
  ET_PlayerFarmGridLevel = 2236;        // 星农场地块等级
  ET_PlayerFarmCoin = 2237;             // 星农场货币
  ET_PlayerFarmBuildingSell = 2238;     // 星农场售卖
  ET_PlayerFarmCropMenuSelect = 2239;   // 星农场播种菜单选择作物
  ET_PlayerFarmCropLevel = 2240;        // 星农场养植物等级
  ET_PlayerFarmingHandbookLevel = 2241; // 星农场种田图鉴等级/星农场种植图鉴等级
  ET_PlayerWaterObtainCrop = 2242;      // 为农作物浇水并获得幼苗
  ET_PlayerHarvestCrop = 2243;          // 在星家园种植农作物并收获
  ET_PlayerWaterObtainGoldCrop = 2244;  // 为农作物浇水并获得特级幼苗
  ET_FarmCropEncourage = 2245;          // 星农场养植物鼓励
  ET_PlayerFarmObtainFishCardPack = 2246; // 农场获得鱼卡包
  ET_FarmPartyEnter = 2247; // 农场参与派对
  ET_FarmPartyPublish = 2248; // 农场开启派对
  ET_FarmCookOpen = 2249;               // 农场餐厅-营业
  ET_FarmCookGetComment = 2250;         // 农场餐厅-查看点评
  ET_FarmCookVisitantPrebook = 2251;    // 农场餐厅-预约
  ET_FarmCookLevel = 2259; // 农场餐厅等级
  ET_FarmCookScore = 2260; // 农场餐厅评分
  ET_FarmCookVisitantServe = 2261; // 农场餐厅贵宾接待
  ET_FarmCookVisitantSteal = 2262; // 农场餐厅贵宾拉走
  ET_FarmCookUsingSpecialFurnitureNum = 2263; // 农场餐厅使用中的功能性家具数量
  ET_FarmCookUsingEmployeeNum = 2264; // 农场餐厅工作中的员工数量
  ET_FarmCookEmployeeMarketRefresh = 2265; // 农场餐厅刷新员工市场


  // UGC  2300-2499
  ET_PlayUgcMapCommon = 2300;   // 游玩UGC地图
  ET_UgcAppreciate = 2301;      // 地图鉴赏家
  ET_PlayerUgcCommunityOp = 2302;  // 种草社区操作

  // 副玩法通用 2500-2999
  ET_DeliverGamePlayItem = 2500;    // 副玩法道具发货
  ET_GameplayData = 2501;           // 副玩法事件
  ET_MonthCardTimeChange = 2502;    // 月卡时间变化

  //arena 5000-5999
  ET_GameTimesStat = 5000;  // 玩法次数统计信息事件

  //chase 6000-6999
  ET_ChaseIdentityMasteryScoreChange = 6000;  // 大王身份专精分数变化
  ET_ChaseIdentityWeekRankSettlement = 6001;  // 大王身份专精排行榜每周结算
}