syntax = "proto2";

package com.tencent.wea.protocol;

option java_multiple_files = true;
option java_package = "com.tencent.wea.protocol.common";

import "ResKeywords.proto";
import "common.proto";
import "base_common.proto";
import "attr_SeasonFashion.proto";
import "attr_PlayerRankGeoInfo.proto";
import "attr_PlayerGameTime.proto";
import "attr_PlatPrivilegesInfo.proto";
import "attr_XiaowoUgcMapMetaInfo.proto";
import "attr_XiaoWoSafeInfo.proto";
import "attr_FarmSafeInfo.proto";
import "attr_HouseSafeInfo.proto";
import "attr_IntimateRelationMotionAttr.proto";
import "attr_CookSafeInfo.proto";
import "attr_PlayerPublicProfileInfo.proto";
import "attr_PlayerPublicEquipments.proto";
import "attr_CosImage.proto";
import "attr_IntimatePlayerInfo.proto";
import "attr_QualifyingInfo.proto";
import "attr_CanStealTime.proto";
import "attr_RoomExtraInfo.proto";
import "attr_StickFriendInfo.proto";
import "attr_KvLL.proto";

import "attr_ClubBriefData.proto";
import "attr_HomePageActionShowInfo.proto";
import "attr_FittingSlots.proto";
import "attr_AlbumInfo.proto";
import "attr_AttrRecentActivity.proto";
import "attr_MallWishListPublic.proto";
import "attr_DressUpDetailInfo.proto";
import "attr_PlayerStatusDetails.proto";
import "attr_BPPublicInfo.proto";
import "attr_BuildingSkin.proto";
import "attr_TradingCardCollectionInfos.proto";
import "attr_TradingCardCollectionCardInfos.proto";
import "attr_FarmPartyPublicInfo.proto";
import "attr_VillagerHouseVisitorInfo.proto";
import "attr_AppearanceRoadShowInfo.proto";
import "attr_StarPPlayerAttr.proto";
import "attr_ModPublicInfo.proto";
import "attr_KvII.proto";
import "attr_PetClothing.proto";
import "attr_AttrDisplayBoardUgc.proto";

import "google/protobuf/descriptor.proto";

extend google.protobuf.EnumValueOptions {
  optional string attrKeyName = 1000003;
  optional xlsRes.QualifyType qualifyType = 1000004;
  repeated string publicInfoField = 1000005;
}

enum PlayerPublicInfoField {
  None = 0;
  //ProfileInfo
  UID = 1[(attrKeyName) = "PublicProfile", (publicInfoField) = "uid"];
  SHORT_UID = 75[(attrKeyName) = "PublicProfile", (publicInfoField) = "shortUid"];
  OPENID = 2[(attrKeyName) = "PublicProfile", (publicInfoField) = "openId", (publicInfoField) = "platId"];
  NAME = 3[(attrKeyName) = "PublicProfile", (publicInfoField) = "name"];
  PROFILE = 4[(attrKeyName) = "PublicProfile", (publicInfoField) = "profile"];
  GENDER = 5[(attrKeyName) = "PublicProfile", (publicInfoField) = "gender"];
  LEVEL = 6[(attrKeyName) = "PublicProfile", (publicInfoField) = "level"];
  VIP_LEVEL = 7[(attrKeyName) = "PublicProfile", (publicInfoField) = "vipLevel", (publicInfoField) = "vipExp"];
  LABELS = 8[(attrKeyName) = "PublicProfile", (publicInfoField) = "labels"];
  PERSONALITY_STATE = 9[(attrKeyName) = "PublicProfile"];
  CREATOR_ID = 73[(attrKeyName) = "PublicProfile", (publicInfoField) = "creatorId"];
  IS_NEW_START = 10[(attrKeyName) = "PublicBasicInfo"];
  EXP = 71[(attrKeyName) = "PublicProfile"];
  LOGOUT_TIME_MS = 72[(attrKeyName) = "PublicBasicInfo", (publicInfoField) = "logoutTimeMs"];
  PLAT_PRIVILEGES = 74[(attrKeyName) = "PublicProfile", (publicInfoField) = "platPrivileges"];
  INTIMATE_PLAYER_INFO = 18[(attrKeyName) = "PublicProfile"];
  LOCATION = 76[(attrKeyName) = "PublicProfile"];
  CLUB_INFO = 77[(attrKeyName) = "PublicProfile", (publicInfoField) = "clubIds", (publicInfoField) = "clubIdsSet"]; // 社团信息
  RETURNING = 78[(attrKeyName) = "PublicProfile", (publicInfoField) = "returning", (publicInfoField) = "returnExpiredSec"];  // 回归标识
  RETURN_EXPIRED_SEC = 79[(attrKeyName) = "PublicProfile"]; // 回归过期时间戳
  CATCHPHRASE = 80[(attrKeyName) = "PublicProfile"];
  CUPS_NUM = 5000[(attrKeyName) = "PublicProfile", (publicInfoField) = "cupsNum"]; // 奖杯数
  STICK_FRIENDS = 5001[(attrKeyName) = "PublicProfile", (publicInfoField) = "stickFriends"];
  BIRTHDAY_MONTH_DAY = 5002[(attrKeyName) = "PublicProfile", (publicInfoField) = "birthdayMonthDay"]; // 生日
  CUPS_CYCLE = 5003[(attrKeyName) = "PublicProfile", (publicInfoField) = "cupsCycle"]; // 奖杯周目
  CUPS_TOTAL = 5004[(attrKeyName) = "PublicProfile", (publicInfoField) = "cupsTotal"]; // 总奖杯
  //GameData
  QUALIFY_DEGREE_INFO = 11[(attrKeyName) = "PublicGameData", (publicInfoField) = "qualifyingInfo"];
  HISTORY_MAX_QUALIFY_DEGREE_INFO = 12[(attrKeyName) = "PublicHistoryData"];
  PLAYER_GAME_TIMES = 13[(attrKeyName) = "PublicGameData", (publicInfoField) = "playerGameTimes"];
  PLAYER_GEO_INFO = 14[(attrKeyName) = "PublicGameData", (publicInfoField) = "playerGeoInfo"];
  WIN_NUM = 15[(attrKeyName) = "PublicGameData"];
  CURRENT_MAX_QUALIFY_DEGREE_INFO = 16[(attrKeyName) = "PublicGameData"];
  MONTH_CARD = 17[(attrKeyName) = "PublicGameData", (publicInfoField) = "monthCard", (publicInfoField) = "monthCardExpireTimeMs"];
  RANK_GEO_INFO = 19[(attrKeyName) = "PublicGameData"];
  HISTORY_QUALIFY_DEGREE_INFO = 20[(attrKeyName) = "PublicHistoryData"];
  PERMIT_INFO = 105[(attrKeyName) = "PublicGameData"];
  SHOW_QUALIFY_TYPE = 106[(attrKeyName) = "PublicGameData", (publicInfoField) = "showQualifyType"];
  BP_INFO = 109[(attrKeyName) = "PublicGameData"];
  COMMONLY_USED_HERO = 110[(attrKeyName) = "PublicGameData"];
  FASHION_LV = 111[(attrKeyName) = "PublicProfile"];
  CARD_COLLECTION_INFO = 112[(attrKeyName) = "PublicGameData", (publicInfoField) = "tradingCardCollectionInfos"];
  FARM_RETURNING = 113[(attrKeyName) = "PublicProfile"];
  FARM_SQUAD_ACTIVITY_LUCKY_FLAG = 114 [(attrKeyName) = "PublicGameData", (publicInfoField) = "farmSquadActivityLuckyFlag"];
  INTIMATE_MOTION = 115[(attrKeyName) = "PublicGameData", (publicInfoField) = "intimateMotion"];
  CARD_COLLECTION_CARD_INFO = 116[(attrKeyName) = "PublicGameData", (publicInfoField) = "TradingCardCollectionCardInfos"];
  APPEARANCE_ROAD_SHOW_INFO = 117[(attrKeyName) = "PublicProfile"];
  SHOW_SUB_HISTORY_MAX_QUALIFY_TYPE = 118[(attrKeyName) = "PublicGameData", (publicInfoField) = "showSubHistoryMaxQualifyType"];

  //LiveStatus
  PLAYER_STATE = 21[(attrKeyName) = "PublicLiveStatus", (publicInfoField) = "playerState", (publicInfoField) = "battleMatchType", (publicInfoField) = "battleJoinTs", (publicInfoField) = "hidePlayerStatus", (publicInfoField) = "hidePlayerStatusTime"];
  ROOM_STATE = 22[(attrKeyName) = "PublicLiveStatus"];
  ROOM_ID = 23[(attrKeyName) = "PublicLiveStatus"];
  ROOM_FULL_STATE = 24[(attrKeyName) = "PublicLiveStatus"];
  LAST_KEEP_ALIVE_TIME = 25[(attrKeyName) = "PublicLiveStatus", (publicInfoField) = "lastKeepAliveTime"];
  ROOM_EXTRA_INFO = 26[(attrKeyName) = "PublicLiveStatus", (publicInfoField) = "roomExtraInfo"];
  BATTLE_UGC_ID = 27[(attrKeyName) = "PublicLiveStatus"];
  NEED_NTF_LOGIN = 28[(publicInfoField) = "needNtfLogin"];
  PLAYER_STATUS_DETAILS = 29 [(publicInfoField) = "statusDetails"];

  //SceneData
  LOBBY_SCENE_ID = 31[(attrKeyName) = "PublicSceneData"];
  LOBBY_MAP_ID = 32[(attrKeyName) = "PublicSceneData", (publicInfoField) = "lobbyMapId"];
  CURRENT_XIAO_WO_ID = 33[(attrKeyName) = "PublicSceneData"];
  CURRENT_FARM_ID = 34[(attrKeyName) = "PublicSceneData", (publicInfoField) = "currentFarmId"];
  CURRENT_HOUSE_ID = 35[(attrKeyName) = "PublicSceneData", (publicInfoField) = "currentHouseId"];
  CURRENT_FARM_TYPE = 36[(attrKeyName) = "PublicSceneData", (publicInfoField) = "currentFarmType"];
  CURRENT_RICH_BOARD_ID = 37[(attrKeyName) = "PublicSceneData", (publicInfoField) = "currentRichBoardId"];
  CURRENT_COOK_ID = 38[(attrKeyName) = "PublicSceneData", (publicInfoField) = "currentCookId"];

  //Equipments
  FASHION_VALUE = 41[(attrKeyName) = "PublicEquipments"];
  DRESSUP_INFOS = 42[(attrKeyName) = "PublicEquipments"];
  DRESS_ITEM_INFOS = 43[(attrKeyName) = "PublicEquipments", (publicInfoField) = "dressItemInfos"];
  FASHION_VALUES = 44[(attrKeyName) = "PublicEquipments"];
  DRESS_COUNT = 45[(attrKeyName) = "PublicEquipments"];
  ACTIVE_SUIT_BOOK = 46[(attrKeyName) = "PublicEquipments"];
  FITTING_SLOTS = 47[(attrKeyName) = "FittingSlots"];
  PROFILE_THEME = 48[(attrKeyName) = "PublicEquipments"];
  DRESSUP_DETAIL_INFO = 49[(attrKeyName) = "PublicEquipments"];
  DISPLAY_BOARD_INFO = 50[(attrKeyName) = "PublicEquipments"];

  //GameSettings
  BENEFIT_CARD_ENABLE = 51[(attrKeyName) = "PublicGameSettings"];
  PRIVILEGE_SWITCH = 52[(attrKeyName) = "PublicGameSettings"];
  PRIVILEGE_LEVEL = 53[(attrKeyName) = "PublicGameSettings"];
  HIDE_QUALIFYING_INFO = 54[(attrKeyName) = "PublicGameSettings"];
  HIDE_PERSONALP_ROFILE = 55[(attrKeyName) = "PublicGameSettings", (publicInfoField) = "hidePersonalProfile"];
  HIDE_BATTLE_HISTORY = 56[(attrKeyName) = "PublicGameSettings"];
  HIDE_SUIT_BOOK = 57[(attrKeyName) = "PublicGameSettings"];
  HIDE_UGC_INFO = 58[(attrKeyName) = "PublicGameSettings"];
  HIDE_QQ_FRIEND_REQ = 59[(attrKeyName) = "PublicGameSettings", (publicInfoField) = "hideQQFriendReq"];
  HIDE_LOCATION = 60[(attrKeyName) = "PublicGameSettings"];
  HIDE_INTIMATE_RELATION_TAB = 261[(attrKeyName) = "PublicGameSettings"];
  HIDE_TITLE = 262[(attrKeyName) = "PublicGameSettings"];
  HIDE_PERSONALITY_STATE = 263[(attrKeyName) = "PublicGameSettings"];
  HEAD_PUBLIC_INFO_TYPE = 264[(attrKeyName) = "PublicGameSettings"];
  HIDE_CLUB_INFO = 265[(attrKeyName) = "PublicGameSettings"];
  NEED_ACTION_CONFIRM = 266[(attrKeyName) = "PublicGameSettings"];
  SHOW_SEASON_FASHION = 267 [(attrKeyName) = "PublicGameSettings"]; // 是否展示赛季时尚手册
  INTIMATE_ONLINE_NOTICE = 268 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "intimateOnlineNotice"];// 亲密好友上线提醒
  HIDE_ROOM_EXTRA_INFO = 269 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "hideRoomExtraInfo"];
  HIDE_PROFILE_TO_FRIEND = 270 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "hideProfileToFriend"];
  HIDE_PROFILE_TO_STRANGER = 271 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "hideProfileToStranger"];
  DEMAND_SWITCH = 272 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "demandSwitch"]; // 允许索要(0-默认允许;1-不允许被索要)
  SHOW_CUPS = 273 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "showCups"]; // 展示奖杯(false:展示 true:隐藏)
  STRANGER_FOLLOW = 274 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "strangerFollow"]; // 陌生人跟随是否跟随
  SHOW_CUSTOM_ACTION_SELECT_FRIEND_INFO = 275 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "showCustomActionSelectFriendInfo"]; // 陌生人跟随是否跟随
  HIDE_ARENA_BATTLE_HISTORY = 276 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "hideArenaBattleHistory"];
  HIDE_ENTERTAINMENT_QUALIFYING_INFO = 277[(attrKeyName) = "PublicGameSettings"];
  PHOTO_LIBRARY_DESCRIPTION = 278 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "photoLibraryDescription"];//相册访问权限
  BIRTHDAY_VISIBLE_RANGE = 279 [(attrKeyName) = "PublicGameSettings", (publicInfoField) = "birthdayVisibleRange"]; // 生日可见范围
  PROFILE_SHOW_COLLECTION = 284 [(attrKeyName) = "PublicGameSettings"]; // 个人信息展示项
  CLIENT_VERSION_64 = 285[(attrKeyName) = "PublicGameSettings"]; // 客户端版本号
  CURRENT_ACTION_STATE = 286[(attrKeyName) = "PublicGameSettings"]; // 当前动作状态
  HIDE_SUB_HISTORY_MAX_QUALIFY_INFO = 287[(attrKeyName) = "PublicGameSettings"]; // 隐藏副玩法历史最高段位
  PROFILE_TOP_INFO = 288[(attrKeyName) = "PublicGameSettings"]; // 个人信息界面置顶信息

  //Other
  FRIEND_NICKNAME = 61;
  IS_ONLINE = 62;
  INTIMACT = 63;
  IS_FOLLOWEE = 64;
  FANS_NUM = 65;  // 实时粉丝数
  UGC_LV = 66;
  RECENT_ACTIVITY = 67 [(attrKeyName) = "recentActivity", (publicInfoField) = "recentActivity"];// 近期互动
  MALL_WISH_LIST = 68 [(attrKeyName) = "mallWishList", (publicInfoField) = "mallWishList"];// 近期互动

  // 相册
  ALBUM_PIC = 70 [(attrKeyName) = "albumInfo"];

  //小窝
  HAS_XIAOWO = 81 [(attrKeyName) = "XiaowoPublicInfo"];  // 有没有小窝
  XIAOWO_NAME = 82 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝名
  XIAOWO_LEVEL = 83 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝等级
  XIAOWO_BEAUTY = 84 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝美观度
  XIAOWO_LIKE = 85 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝点赞数
  XIAOWO_STAR = 86 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝收藏数
  XIAOWO_VISITOR_COUNT = 87 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝人数
  XIAOWO_VISITOR_TOTAL_COUNT = 88 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝访问数
  XIAOWO_PUBID = 89 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝cos
  XIAOWO_INSTRUCTION = 90 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝介绍
  XIAOWO_TEMPLATEID = 91 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝模板
  XIAOWO_ATHOME = 92 [(attrKeyName) = "XiaowoPublicInfo"];  // 是不是在自己小窝
  XIAOWO_SAVETIME = 93 [(attrKeyName) = "XiaowoPublicInfo"];  // 保存时间
  XIAOWO_CREATETIME = 94 [(attrKeyName) = "XiaowoPublicInfo"];  // 创建时间
  XIAOWO_EDITID = 95 [(attrKeyName) = "XiaowoPublicInfo"];  // 草稿ID
  XIAOWO_METADATA = 96 [(attrKeyName) = "XiaowoPublicInfo"];  //
  XIAOWO_BUCKET = 97 [(attrKeyName) = "XiaowoPublicInfo"];  // 草稿ID
  XIAOWO_REGION = 98 [(attrKeyName) = "XiaowoPublicInfo"];  //
  XIAOWO_SAFE_INFO = 99 [(attrKeyName) = "XiaowoPublicInfo"];  // 安全信息
  XIAOWO_IMAGE = 100 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝封面
  XIAOWO_ATXIAOWO = 101 [(attrKeyName) = "PublicProfile", (publicInfoField) = "xiaoWoAtXiaowo"];  // 是否在小窝内（包括自己的和别人的）
  XIAOWO_VERSION = 102 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝版本号
  XIAOWO_CANWATER = 103 [(attrKeyName) = "XiaowoPublicInfo"];  // 能否浇水
  XIAOWO_TOTAL_LIUYAN_MESSAGE_NUMBER = 104 [(attrKeyName) = "XiaowoPublicInfo"];  // 留言总数
  XIAOWO_SHARE_COUNT = 107 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝分享数
  XIAOWO_LIUYAN_MESSAGE_PERMISSION = 108 [(attrKeyName) = "XiaowoPublicInfo"];  // 小窝留言权限
  // party
  PARTY_OPEN = 150 [(attrKeyName) = "PartyInfo"];
  PARTY_CONTENT = 151 [(attrKeyName) = "PartyInfo"];
  PARTY_URL = 152 [(attrKeyName) = "PartyInfo"];
  PARTY_COS_BUCKET = 153 [(attrKeyName) = "PartyInfo"];
  PARTY_COS_REGION = 154 [(attrKeyName) = "PartyInfo"];
  PARTY_COS_PATH = 155 [(attrKeyName) = "PartyInfo"];
  PARTY_COS_MD5 = 156 [(attrKeyName) = "PartyInfo"];
  PARTY_URL_VERSION = 157 [(attrKeyName) = "PartyInfo"];
  // BasicInfo
  ACCOUNT_TYPE = 200[(attrKeyName) = "PublicBasicInfo", (publicInfoField) = "accountType"];
  AUTH_TYPE = 201[(attrKeyName) = "PublicProfile", (publicInfoField) = "authType"];
  AUTH_DESC = 202[(attrKeyName) = "PublicProfile", (publicInfoField) = "authDesc"];
  SVR_ID = 203[(attrKeyName) = "PublicBasicInfo", (publicInfoField) = "svrId"];
  // KungFuPanda
  KUNG_FU_PANDA_RACING_COST_TIME_MS = 204[(attrKeyName) = "PublicProfile"];
  // HomepageAction
  HOME_PAGE_ACTION_CONFIG = 205[(attrKeyName) = "PublicProfile"];
  ACCOUNT_STATE = 206[(attrKeyName) = "PublicBasicInfo"];
  REGISTER_TIME_MS = 207[(attrKeyName) = "PublicBasicInfo"];

  //farm
  HAS_FARM = 400 [(attrKeyName) = "FarmPublicInfo"];  // 有没有Farm
  FARM_LEVEL = 401 [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmLevel"];  // Farm等级
  FARM_VISITOR_COUNT = 402 [(attrKeyName) = "FarmPublicInfo"];  // Farm人数
  FARM_CAN_STEAL_TIME = 403 [(attrKeyName) = "FarmPublicInfo"];  // Farm可以偷菜的时间点
  FARM_SAFE_INFO = 404 [(attrKeyName) = "FarmPublicInfo"];  // Farm安全信息
  FARM_TOTAL_LIUYAN_MESSAGE_NUMBER = 405 [(attrKeyName) = "FarmPublicInfo"];  // 留言总数
  FARM_ATFARM = 406 [(attrKeyName) = "PublicProfile", (publicInfoField) = "atFarm"];  // 是否在农场（包括自己的和别人的）
  FARM_MONTHCARD_ENDTIME = 407 [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmMonthCardEndTime"];  // 农场月卡到期时间
  HAS_HOUSE = 408 [(attrKeyName) = "HousePublicInfo"];  // 有没有农场小屋
//  HOUSE_LEVEL = 409 [(attrKeyName) = "HousePublicInfo"];  // 农场小屋等级
  HOUSE_VISITOR_COUNT = 409 [(attrKeyName) = "HousePublicInfo"];  // 农场小屋人数
  HOUSE_ATHOUSE = 410 [(attrKeyName) = "PublicProfile", (publicInfoField) = "atHouse"];  // 是否在农场小屋（包括自己的和别人的）
  HOUSE_SAFE_INFO = 412 [(attrKeyName) = "HousePublicInfo"];  // 安全信息
  FARM_CHECKEDLEAVE = 413 [(attrKeyName) = "PublicSceneData", (publicInfoField) = "farmCheckedLeave"];  // 农场是否检测到离线
  FARM_BUILDING_SKINS = 414 [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmBuildingSkins"]; // 农场建筑皮肤
  FARM_SIGNATURE = 415 [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmSignature"]; // 农场个性签名
  FARM_PARTY_INFO = 416 [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmPartyInfo"]; // 农场派对信息
  VILLAGER_HOUSE_VISITOR_INFO = 417 [(attrKeyName) = "VillagePublicInfo", (publicInfoField) = "villagerHouseVisitorInfos"]; // 农场村民小屋访客信息
  FARM_SIGNATURE_CHANGE_MS = 418  [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmSignatureChangeMs"]; // 农场村民小屋访客信息
  FARM_SIGNATURE_EXP = 419 [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmSignatureEXP"]; // 农场个性签名有效期
  FARM_PET_CLOTHING = 420 [(attrKeyName) = "FarmPublicInfo", (publicInfoField) = "farmPetClothing"]; // 农场宠物和服装

  // coc
  COC_PROSPERITY_DETAIL = 501 [(attrKeyName) = "CocPlayerPublicInfo"]; // coc 繁荣度详情
  COC_CUPS_SCORE = 502 [(attrKeyName) = "CocPlayerPublicInfo"]; // coc 奖杯积分
  COC_MAX_DAILY_RECEIVED_FRIEND_DONATION = 503 [(attrKeyName) = "CocPlayerPublicInfo"]; // coc 今日收到援助达到上限
  COC_GAME_REGISTERED = 504 [(attrKeyName) = "CocPlayerPublicInfo"]; // 是否注册过coc玩法

  // rich
  RICH_ATRICH = 601 [(attrKeyName) = "PublicProfile", (publicInfoField) = "atRich"];  // 是否在大富翁（包括自己的和别人的）

  // cook
  HAS_COOK = 520 [(attrKeyName) = "CookPublicInfo"];  // 有没有农场餐厅
  COOK_VISITOR_COUNT = 521 [(attrKeyName) = "CookPublicInfo"];  // 农场餐厅人数
  COOK_ATCOOK = 522 [(attrKeyName) = "PublicProfile", (publicInfoField) = "atCook"];  // 是否在农场餐厅（包括自己的和别人的）
  COOK_SAFE_INFO = 523 [(attrKeyName) = "CookPublicInfo"];  // 安全信息
  //手持乐队最高分
  DANCE_HIGHEST_SCORE = 701 [(attrKeyName) = "DancePublicInfo"]; // 乐队最高分

  // 主副玩法段位
  QUALIFY_DEGREE_INFO_ALL = 1000[(attrKeyName) = "PublicGameData"];

  QUALIFY_DEGREE_INFO_MAIN = 1001[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Main];
  QUALIFY_DEGREE_INFO_WEREWOLF = 1002[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Werewolf];
  QUALIFY_DEGREE_INFO_HIDE_AND_SEEK = 1003[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HideAndSeek];
  QUALIFY_DEGREE_INFO_ANIMAL = 1004[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Animal];
  QUALIFY_DEGREE_INFO_JS_RACING = 1005[(attrKeyName) = "PublicGameData", (qualifyType) = QT_JSRacing];
  QUALIFY_DEGREE_INFO_FPS_GUN = 1006[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_Gun];
  QUALIFY_DEGREE_INFO_DBD_NORMAL = 1007[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Normal];
  QUALIFY_DEGREE_INFO_DBD_GHOST = 1008[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Ghost];
  QUALIFY_DEGREE_INFO_FPS_BR_GAME = 1009[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_BrGame];
  QUALIFY_DEGREE_INFO_LIGHTNING = 1010[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Lightning];
  QUALIFY_DEGREE_INFO_DOLLS_RUN = 1011[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DollsRun];
  QUALIFY_DEGREE_INFO_ARENA = 1012[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Arena];
  QUALIFY_DEGREE_INFO_BUBBLE = 1013[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Bubble];
  QUALIFY_DEGREE_INFO_HOK = 1014[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HOK];
  QUALIFY_DEGREE_INFO_StarP = 1015[(attrKeyName) = "PublicGameData", (qualifyType) = QT_StarP];

  QUALIFY_DEGREE_INFO_MAX = 1999;   // 区间划分

  // 主副玩法当前最高段位
  CURRENT_MAX_QUALIFY_DEGREE_INFO_ALL = 2000[(attrKeyName) = "PublicGameData"];

  CURRENT_MAX_QUALIFY_DEGREE_INFO_MAIN = 2001[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Main];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_WEREWOLF = 2002[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Werewolf];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_HIDE_AND_SEEK = 2003[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HideAndSeek];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_ANIMAL = 2004[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Animal];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_JS_RACING = 2005[(attrKeyName) = "PublicGameData", (qualifyType) = QT_JSRacing];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_FPS_GUN = 2006[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_Gun];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_DBD_NORMAL = 2007[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Normal];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_DBD_GHOST = 2008[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Ghost];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_FPS_BR_GAME = 2009[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_BrGame];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_LIGHTNING = 2010[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Lightning];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_DOLLS_RUN = 2011[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DollsRun];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_ARENA = 2012[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Arena];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_BUBBLE = 2013[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Bubble];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_HOK = 2014[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HOK];
  CURRENT_MAX_QUALIFY_DEGREE_INFO_StarP = 2015[(attrKeyName) = "PublicGameData", (qualifyType) = QT_StarP];

  CURRENT_MAX_QUALIFY_DEGREE_INFO_MAX = 2999;   // 区间划分

  // 主副玩法历史最高段位
  HISTORY_MAX_QUALIFY_DEGREE_INFO_ALL = 3000[(attrKeyName) = "PublicGameData"];

  HISTORY_MAX_QUALIFY_DEGREE_INFO_MAIN = 3001[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Main];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_WEREWOLF = 3002[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Werewolf];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_HIDE_AND_SEEK = 3003[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HideAndSeek];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_ANIMAL = 3004[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Animal];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_JS_RACING = 3005[(attrKeyName) = "PublicGameData", (qualifyType) = QT_JSRacing];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_FPS_GUN = 3006[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_Gun];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_DBD_NORMAL = 3007[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Normal];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_DBD_GHOST = 3008[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Ghost];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_FPS_BR_GAME = 3009[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_BrGame];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_LIGHTNING = 3010[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Lightning];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_DOLLS_RUN = 3011[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DollsRun];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_ARENA = 3012[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Arena];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_BUBBLE = 3013[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Bubble];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_HOK = 3014[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HOK];
  HISTORY_MAX_QUALIFY_DEGREE_INFO_StarP = 3015[(attrKeyName) = "PublicGameData", (qualifyType) = QT_StarP];

  HISTORY_MAX_QUALIFY_DEGREE_INFO_MAX = 3999;   // 区间划分

  // 主副玩法历史段位
  HISTORY_QUALIFY_DEGREE_INFO_ALL = 4000[(attrKeyName) = "PublicGameData"];

  HISTORY_QUALIFY_DEGREE_INFO_MAIN = 4001[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Main];
  HISTORY_QUALIFY_DEGREE_INFO_WEREWOLF = 4002[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Werewolf];
  HISTORY_QUALIFY_DEGREE_INFO_HIDE_AND_SEEK = 4003[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HideAndSeek];
  HISTORY_QUALIFY_DEGREE_INFO_ANIMAL = 4004[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Animal];
  HISTORY_QUALIFY_DEGREE_INFO_JS_RACING = 4005[(attrKeyName) = "PublicGameData", (qualifyType) = QT_JSRacing];
  HISTORY_QUALIFY_DEGREE_INFO_FPS_GUN = 4006[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_Gun];
  HISTORY_QUALIFY_DEGREE_INFO_DBD_NORMAL = 4007[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Normal];
  HISTORY_QUALIFY_DEGREE_INFO_DBD_GHOST = 4008[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DBD_Ghost];
  HISTORY_QUALIFY_DEGREE_INFO_FPS_BR_GAME = 4009[(attrKeyName) = "PublicGameData", (qualifyType) = QT_FPS_BrGame];
  HISTORY_QUALIFY_DEGREE_INFO_LIGHTNING = 4010[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Lightning];
  HISTORY_QUALIFY_DEGREE_INFO_DOLLS_RUN = 4011[(attrKeyName) = "PublicGameData", (qualifyType) = QT_DollsRun];
  HISTORY_QUALIFY_DEGREE_INFO_ARENA = 4012[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Arena];
  HISTORY_QUALIFY_DEGREE_INFO_BUBBLE = 4013[(attrKeyName) = "PublicGameData", (qualifyType) = QT_Bubble];
  HISTORY_QUALIFY_DEGREE_INFO_HOK = 4014[(attrKeyName) = "PublicGameData", (qualifyType) = QT_HOK];
  HISTORY_QUALIFY_DEGREE_INFO_StarP = 4015[(attrKeyName) = "PublicGameData", (qualifyType) = QT_StarP];

  // 副玩法public数据
  MOD_PUBLIC_INFO = 4020 [(attrKeyName) = "ModPublicInfo", (publicInfoField) = "modPublicInfo"];

  HISTORY_QUALIFY_DEGREE_INFO_MAX = 4999;   // 区间划分
  // starp
  STARP_PLAYER = 250 [(attrKeyName) = "StarPlayerInfo"];  // 啾灵角色账号
  STARP_APPLY  = 251 [(attrKeyName) = "StarPlayerInfo"];  // 啾灵申请信息
  STARP_ROLE_INFO = 252 [(attrKeyName) = "StarPPublicUserInfo"]; // 最新的啾灵角色公开信息
  STARP_MISC_INFO = 253 [(attrKeyName) = "StarPMiscUserInfo"]; // 最新的啾灵角色杂项信息
  STARP_ROLE_INFO2 = 254 [(attrKeyName) = "StarPPublicUserInfo2"]; // 最新的啾灵角色公开信息
}

enum EHeadPublicInfoType {
  EHShow_None = 0;    // 什么都不展示
  EHShow_Title = 1;
  EHShow_PersonalityState = 2;
}


message PlayerPublicInfo {
  //ProfileInfo
  optional int64 uid = 1;
  optional string openId = 2;
  optional string name = 3;
  optional string profile = 4;
  optional int32 gender = 5;
  optional int32 level = 6;
  optional int32 vipLevel = 7;
  repeated int32 labels = 8;
  optional int32 personalityState = 9;
  optional bool isNewStar = 10;
  optional int64 exp = 71;
  optional int64 logoutTimeMs = 72;
  optional int64 creatorId = 73;
  optional com.tencent.wea.protocol.proto_PlatPrivilegesInfo platPrivileges = 74; // 平台特权信息
  optional int64 shortUid = 75;
  optional com.tencent.wea.protocol.proto_PlayerRankGeoInfo location = 76;
  repeated int64 clubIds = 77; // 公会ID
  optional bool returning = 78; // 回归标识
  optional int64 returnExpiredSec = 79; // 回归过期时间戳
  optional string catchphrase = 80;
  optional bool clubIdsSet = 105; // 设置了公会ID字段
  optional int32 platId = 106;
  optional int32 vipExp = 109;  // Vip经验值
  optional int32 cupsNum = 110; // 总奖杯数
  optional int32 birthdayMonthDay = 111; // 生日: 生日月*100+生日日
  optional proto_KvLL farmReturning = 113; // 农场回流 (0/1) => expireTimeSec
  repeated proto_AppearanceRoadShowInfo appearanceRoadShowInfo = 117;
  optional int32 cupsCycle = 120; // 当前选择周目
  optional int32 cupsTotal = 121; // 总奖杯
  //GameData
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingInfo = 11;
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo historyMaxQualifyingInfo = 12; //历史达到最高
  repeated com.tencent.wea.protocol.proto_PlayerGameTime playerGameTimes = 13;
  optional com.tencent.wea.protocol.proto_PlayerRankGeoInfo playerGeoInfo = 14;
  optional int32 winNum = 15;
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo currentMaxQualifyingInfo = 16; // 当前赛季最高
  repeated string monthCard = 17; //月卡
  optional int64 monthCardExpireTimeMs = 160; // 月卡失效时间，最后失效的月卡
  repeated com.tencent.wea.protocol.proto_IntimatePlayerInfo intimatePlayerInfo = 18; // 亲密好友列表
  optional com.tencent.wea.protocol.proto_PlayerRankGeoInfo rankGeoRegion = 19; // 地区排行榜归属地
  repeated com.tencent.wea.protocol.proto_QualifyingInfo historyQualifyingInfo = 20; // 历史段位信息
  optional int64 permitId = 161;    // 通行证ID
  optional int32 permitType = 162;  // 通行证类型
  optional int32 showQualifyType = 163; // 副玩法段位展示
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo showQualifyInfo = 164; // 副玩法段位(本赛季最高)
  repeated proto_BPPublicInfo bpInfo = 165;    // BP信息
  optional proto_TradingCardCollectionInfos tradingCardCollectionInfos = 166; // 交易卡信息
  optional bool farmSquadActivityLuckyFlag = 167; // 农场小队幸运标记
  repeated proto_IntimateRelationMotionAttr intimateMotion = 168; // 亲密关系动作
  optional proto_TradingCardCollectionCardInfos tradingCardCollectionCardInfos = 169; // 交易卡信息
  optional int32 showSubHistoryMaxQualifyType = 170; // 副玩法历史最高段位展示
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo showSubHistoryMaxQualifyInfo = 171; // 副玩法历史最高段位（包括当前）

  //LiveStatus
  optional com.tencent.wea.xlsRes.PlayerStateType playerState = 21;
  optional com.tencent.wea.xlsRes.RoomStateType roomState = 22;
  optional int64 roomId = 23;
  optional bool roomFullState = 24;
  optional int64 lastKeepAliveTime = 25;
  optional int32 battleMatchType = 26; // 进入对局状态的玩法ID
  optional int64 battleJoinTs = 27; // 进入对局状态的时间戳
  optional com.tencent.wea.protocol.proto_RoomExtraInfo roomExtraInfo = 28; // 房间额外信息
  optional int64 battleUgcId = 29;
  optional bool needNtfLogin = 30;
  optional proto_PlayerStatusDetails statusDetails = 2101;

  //SceneData
  optional int64 lobbySceneId = 31;
  optional int32 lobbyMapId = 32;
  optional int64 currentXiaoWoId = 33; //当前所在的小窝ID
  optional int64 currentFarmId = 34; //当前所在的农场ID
  optional int64 currentHouseId = 35; //当前所在的农场小屋ID
  optional int32 currentFarmType = 36; //当前所在的农场小屋类型 common.FarmType
  optional int64 currentCookId = 37; //当前所在的农场餐厅ID

  //Equipments
  optional int32 fashionValue = 41;
  repeated int32 dressUpInfos = 42; //装扮信息
  repeated EquipItemInfo dressItemInfos = 43;//穿戴信息(铭牌、称号、头像框等)
  repeated com.tencent.wea.protocol.proto_SeasonFashion fashionValues = 44;
  optional int32 dressCount = 45;
  repeated int32 activeSuitBook = 46;
  optional int32 currFittingSlotId = 47;
  repeated PublicFittingSlotInfo publicFittingSlotInfo = 48;
  optional int32 profileTheme = 49;
  repeated proto_DressUpDetailInfo dressUpDetailInfos = 50;
  repeated ActiveSuitBookByType activeSuitBookByType = 4001;
  optional DisplayBoardInfo displayBoardInfo = 112;

  //GameSettings
  optional bool benefitCardEnable = 51;
  optional int32 privilegeSwitch = 52; //特权开关  0-隐藏按钮，1-不响应点击，2-响应点击  未启用
  optional int32 privilegeLevel = 53; //特权等级  红钻等级: 0-代表未开通，1-开通低档，2-开通中
  optional bool hideQualifyingInfo = 54; //隐藏段位信息
  optional bool hidePersonalProfile = 55; //个人信息隐藏-个人信息
  optional bool hideBattleHistory = 56; //个人信息隐藏-历史战机
  optional bool hideSuitBook = 57; //个人信息隐藏-套装图鉴
  optional bool hideUgcInfo = 58; //个人信息隐藏-UGC
  optional bool hideQQFriendReq = 59; //拒绝QQ好友请求
  optional bool hideLocation = 60; //个人信息隐藏-定位
  optional bool hideIntimateRelationTab = 261; //个人信息隐藏-亲密关系页签
  optional bool hideTitle = 262; //个人信息隐藏-称号
  optional bool hidePersonalityState = 263; //个人信息隐藏-心情
  optional int32 headPublicInfoType = 264;    //头顶个人展示的是称号还是心情 EHeadPublicInfoType 枚举值
  optional bool hideClubInfo = 265; // 个人信息隐藏-社团
  optional bool hidePlayerStatus = 266; // 个人信息隐藏-隐身
  optional bool needActionConfirm = 267; // 双人动作确认
  optional int64 hidePlayerStatusTime = 268; // 个人信息隐藏-隐身时间
  optional bool showSeasonFashion = 269;  // 展示赛季时尚手册开关 # 废弃
  optional com.tencent.wea.xlsRes.FeatureShowStatus seasonFashionShowStatus = 270;  // 赛季时尚手册展示状态
  optional com.tencent.wea.xlsRes.FeatureOpenStatus intimateOnlineNotice = 271;     // 亲密好友上线提醒状态
  optional bool hideRoomExtraInfo = 272; // 隐藏房间额外信息
  optional bool notSearchedByUid = 273; // 不被UID搜索到
  optional bool hideProfileToFriend = 274; // 对好友隐藏个人信息
  optional bool hideProfileToStranger = 275; // 对陌生人隐藏个人信息
  optional int32 demandSwitch = 276;  // 允许索要选项(0-默认允许;1-不允许;2-仅允许亲密)
  optional bool showCups = 277; // 展示奖杯
  optional bool strangerFollow = 278; // 陌生人跟随
  optional bool showCustomActionSelectFriendInfo = 279; // 是否开启双人动作展示好友信息的开关
  optional bool hideArenaBattleHistory = 280; // arena历史战绩隐藏
  optional bool hideEntertainmentQualifyInfo = 281; // 隐藏娱乐玩法段位
  optional int32 photoLibraryDescription = 282; //相册访问权限(0-默认允许;1-不允许;2-仅允许亲密)
  optional com.tencent.wea.xlsRes.BirthdayVisibleRange birthdayVisibleRange = 283;     // 生日可见范围
  repeated int32 profileShowCollection = 284; // 个人信息展示项
  optional int64 clientVersion64 = 285; //客户端版本号;
  optional int64 currentActionState = 286; //当前动作状态(0-默认;1-正在拍照);
  optional bool hideSubHistoryMaxQualifyInfo = 287; //隐藏副玩法历史最高段位
  repeated proto_KvII profileTopInfo = 288; //个人信息界面置顶信息

  //Other
  optional string friendNickname = 61; // 好友昵称
  optional bool isOnline = 62; // 好友在线状态
  optional int64 intimacy = 63; // 好友亲密度
  optional bool isFollowee = 64; // 是否已关注
  optional int64 fansNum = 65;  // 实时粉丝数
  optional int32 ugcLv = 66; // ugc等级
  repeated com.tencent.wea.protocol.proto_ClubBriefData clubInfo = 67; // 公会I信息，待删除
  optional string friendRemarkName = 68; // 好友备注名

  //小窝
  optional bool hasXiaoWo = 81;  // 有没有小窝
  optional string xiaoWoName = 82;  // 小窝名
  optional int32 xiaoWoLevel = 83;  // 小窝等级
  optional int64 xiaoWoBeauty = 84;  // 小窝美观度
  optional int64 xiaoWoLike = 85;  // 小窝点赞数
  optional int64 xiaoWoStar = 86;  // 小窝收藏数
  optional int32 xiaoWoVisitorCount = 87;  // 小窝人数
  optional int64 xiaoWoVisitTotalCount = 88;  // 小窝访问数
  optional int64 xiaoWoPubID = 89;
  optional string xiaoWoInstruction = 90;  // 小窝介绍
  optional int32 xiaoWoTemplateId = 91;  // 小窝模板
  optional bool xiaoWoAtHome = 92;  // 在不在小窝
  optional int64 saveTime = 93;         // 保存时间
  optional int64 createTime = 94;       //创建时间
  optional int64 xiaoWoEditID = 95;  // 小窝草稿
  repeated com.tencent.wea.protocol.proto_XiaowoUgcMapMetaInfo UgcMapMetaInfo = 96;
  optional string bucket = 97;
  optional string region = 98;
  optional com.tencent.wea.protocol.proto_XiaoWoSafeInfo xiaoWoSafeInfo = 99;
  optional com.tencent.wea.protocol.proto_CosImage xiaoWoImage = 100;
  optional bool xiaoWoAtXiaowo = 101;  // 在不在小窝
  optional string xiaowoVersion = 102; // 小窝版本号
  optional bool xiaowoCanWater = 103; // 是否可浇水
  optional int32 xiaowoTotalLiuYanMessageNumber = 104; // 留言总数
  optional int64 xiaowoShareCount = 107; // 小窝分享数
  optional int64 xiaowoLiuYanMessagePermission = 108; // 小窝留言权限
  // party
  optional int32 partyOpen = 150; // 派对是否开启
  optional string partyContent = 151; // 
  optional string partyURL = 152;
  optional string partyCosBucket = 153;
  optional string partyCosRegion = 154;
  optional string partyCosPath = 155;
  optional string partyCosMD5 = 156;
  optional string partyURLVersion = 157;

  // BasicInfo
  optional com.tencent.wea.xlsRes.TconndApiAccount accountType = 200;   // 账号类型
  optional com.tencent.wea.xlsRes.UgcAuthType authType = 201;
  optional string authDesc = 202;
  optional int32 svrId = 203;
  optional int32 accountState = 206;
  optional int64 registerTimeMs = 207;  // 注册时间

  // KungFuPanda
  optional int32 kungFuPandaRacingCostTimeMs = 204;

  // homepageAction
  optional com.tencent.wea.protocol.proto_HomePageActionShowInfo homepageActionConf = 205;

  // starp
  repeated int64 playerOwnedStarPId = 220; // 创建的啾灵世界索引信息
  repeated int64 playerStaredStarPId = 221; // 收藏的啾灵世界索引信息
  optional proto_StarPPlayerAttr StarPlayerInfo = 222; // 啾灵-玩家基础数据
  optional StarPPublicUserInfo starPPublicUserInfo = 223; // 啾灵玩家最新的公开信息
  optional StarPMiscUserInfo starPMiscUserInfo = 224; // 啾灵玩家最新的杂项信息
  optional StarPPublicUserInfo2 starPPublicUserInfo2 = 225; // 啾灵玩家最新的公开信息

  repeated com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingTypeInfo = 301;
  repeated com.tencent.wea.xlsRes.QualifyingDegreeInfo historyMaxQualifyingTypeInfo = 302; //历史达到最高
  repeated com.tencent.wea.xlsRes.QualifyingDegreeInfo currentMaxQualifyingTypeInfo = 303; // 当前赛季最高
  repeated QualifyTypeInfoHistory historyQualifyingTypeInfo = 304; // 历史段位信息

  //farm
  optional bool hasFarm = 400;
  optional int32 farmLevel = 401;
  optional int32 farmVisitorCount = 402;
  optional int64 farmCanStealTime = 403;
  optional com.tencent.wea.protocol.proto_FarmSafeInfo farmSafeInfo = 404;
  map<int64, com.tencent.wea.protocol.proto_CanStealTime> canStealTimeInfo = 405;
  optional int32 farmTotalLiuYanMessageNumber = 406; // 留言总数
  optional bool atFarm = 407;  // 在不在农场
  optional int64 farmMonthCardEndTime = 408; // 农场月卡到期时间
  optional bool hasHouse = 409;
  optional int32 houseVisitorCount = 410;
  optional bool atHouse = 411;  // 在不在农场
  optional com.tencent.wea.protocol.proto_HouseSafeInfo houseSafeInfo = 412;
  optional bool farmCheckedLeave = 413; // 农场检测到离线
  map<int32, com.tencent.wea.protocol.proto_BuildingSkin> farmBuildingSkins = 414; // 农场建筑皮肤
  optional string farmSignature = 415;  // 农场个性签名
  optional com.tencent.wea.protocol.proto_FarmPartyPublicInfo farmPartyInfo = 416;  // 农场派对信息
  map<int32, com.tencent.wea.protocol.proto_VillagerHouseVisitorInfo> villagerHouseVisitorInfos = 417; // 农场村民小屋访客信息
  optional int64 farmSignatureChangeMs = 418;
  optional int64 farmSignatureEXP = 419;

  // album
  optional com.tencent.wea.protocol.proto_AlbumInfo albumInfo = 420; // 相册

  optional com.tencent.wea.protocol.proto_AttrRecentActivity recentActivity = 421; // 近期活跃数据
  map <int32,com.tencent.wea.protocol.proto_StickFriendInfo> stickFriends = 422; //好友置顶信息
  optional int32 commonlyUsedHeroId = 423;
  optional com.tencent.wea.protocol.proto_MallWishListPublic mallWishList = 424; // 商城心愿单
  optional int32 fashionLv = 425;       // 时尚之路等级
  optional int32 fashionSubLv = 426;    // 时尚之路小等级

  // coc
  map<int32, int32> cocProsperityDetail = 501; // coc 繁荣度详情 moduleId -> prosperityValue (moduleId见ResKeywords.COCProsperityModuleId)
  optional int32 cocCupsScore = 502; // coc奖杯积分
  optional bool cocMaxDailyReceivedFriendDonation = 503; // 今日收到援助达到上限
  optional bool cocGameRegistered = 504;    // 是否注册过coc玩法

  // cook
  optional bool hasCook = 520;
  optional int32 cookVisitorCount = 521;
  optional bool atCook = 522;
  optional com.tencent.wea.protocol.proto_CookSafeInfo cookSafeInfo = 523;
  // farm
  map<int32, com.tencent.wea.protocol.proto_PetClothing> farmPetClothing = 550; // 农场宠物皮肤
  // map<int64, com.tencent.wea.protocol.proto_CanStealTime> cookCanStealTimeInfo = 601; // 废弃
  //手持乐队
  optional int32 danceHighestScore = 605;    //乐队个人最高分记录

  // 副玩法 public 数据
  optional com.tencent.wea.protocol.proto_ModPublicInfo modPublicInfo = 650;
}

message QualifyTypeInfoHistory {
  optional int32 qualifyType = 1;
  repeated proto_QualifyingInfo info = 2;

}

// 活动请求基础信息
message BasePlayerActivityInfo {
  optional int32 regionId = 1;              // 地区ID
  optional int32 channelId = 2;             // 渠道ID
  optional int32 accountType = 3;           // 账号类型
  optional string regChannelDis = 4;        // 注册分发渠道
  optional int32 activityABTestGroupId = 5; // 活动ABTest分组ID
  optional string openId = 6;               // OpenID信息
  optional int64 clientVersion64 = 7;       // 客户端版本信息
  optional int64 registerTime = 8;          // 注册时间
  optional int64 shortUid = 9;              // 玩家短ID
  optional int32 platId = 10;               // 平台id
  optional int32 cloudGameType = 11;        // 云游戏类型
  optional int64 farmCreationTimeSec = 12;  // 农场创建时间
  optional int32 wolfNewPlayer = 13;        // 值为1时狼人新玩家
  optional int32 wolfNewPlayerTotalTimes = 14;  //狼人新玩家对局数
  repeated KeyValueInt32 allAbtest  = 15;   // 所有ABTest分组
}

message ActiveSuitBookByType {
  optional int32 appearanceRoadType = 1; // 时尚之路图鉴类型
  repeated int32 activeSuitBook = 2; // 激活图鉴ID
}

message DisplayBoardInfo {
  optional int32 itemId = 1;                    // 道具配置id
  optional string displayBoardContent = 2;      // 内容
  optional proto_AttrDisplayBoardUgc ugcMapMetaInfo = 3;
}