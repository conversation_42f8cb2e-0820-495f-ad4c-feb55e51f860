syntax = "proto2";

package com.tencent.wea.protocol;

option java_package = "com.tencent.wea.protocol.common";

import "ResKeywords.proto";
import "common.proto";
import "base_common.proto";

enum QuitBattleCode {
  QUIT_BATTLE_CODE_INITIATIVE                   = 0; // 主动断线

  QUIT_BATTLE_CODE_LEAVE                        = 1; // 断线重连，玩家主动放弃

  QUIT_BATTLE_CODE_FINISH_QUIT                  = 2; // 到达终点，回合未结束玩家主动退出

  QUIT_BATTLE_CODE_NORMAL                       = 3; // 正常

  QUIT_BATTLE_CODE_KICKED                       = 4; // 被踢

  QUIT_BATTLE_CODE_ACTIVE_LEAVE                 = 5; // 未到达终点，回合已结束玩家主动退出

  QUIT_BATTLE_CODE_MULTITEST_LEADER_QUIT        = 6; // 多人测试，房主退出房间后成员被踢出局内

  QUIT_BATTLE_CODE_KICK_FROM_ROOM               = 7; // 被踢出房间，同时踢出DS

  QUIT_BATTLE_CODE_MID_JOIN_FAIL                = 8;  // 中途加入失败被踢出

  QUIT_BATTLE_CODE_QUIT_WHEN_ENABLE_MID_JOIN    = 9;  // 中途退出当开启中途加入的时候，踢出ds

  QUIT_BATTLE_CODE_KICKED_WHEN_NOT_IN_BATTLE    = 10;  // 只在ds中不在gs中，踢出ds

  QUIT_BATTLE_CODE_RES_VERSION_CONFLICT         = 11;  // 配置版本不一致，需要更新

  QUIT_BATTLE_CODE_ENTER_SP_PVE                 = 12; // 离开啾灵世界进入副本DS

  QUIT_BATTLE_CODE_MAX_WAIT_RECONNCET_TIME_QUIT = 13;   // ds检测玩家切后台最长等待重连时间间隔被踢

  QUIT_BATTLE_CODE_REQUEST_CROSS_DS             = 14; // 请求ds跨服登录

  QUIT_BATTLE_CODE_SP_DS_MULTI_WRITES           = 15; // SP DS发生多写时，踢人

  QUIT_BATTLE_CODE_SP_DS_STORE_INCONSISTENT     = 16; // store 标记不一致

  QUIT_BATTLE_CODE_SP_DS_EXP_NOT_STORE          = 17; // SP DS 经验不落地，踢人

  QUIT_BATTLE_CODE_SP_DS_KICK_AFK_PLAYER        = 18; // SP DS 踢掉挂机人员

  QUIT_BATTLE_CODE_SCENE_CHANGE_SUCC            = 19; // 场景切换成功

  QUIT_BATTLE_CODE_OFFLINE_KICK                 = 20; // 掉线从DS里踢掉

  QUIT_BATTLE_CODE_VERSION_ERROR                = -1; // 版本错误
}

enum DelDSCode {
  DEL_BATTLE_DS_CODE_FORCE_END_UGC_TEST = 1;   // 房主解散房间，强制结束UGC测试
  DEL_BATTLE_DS_CODE_FORCE_OVER_MAX_BATTLE_TIME = 2;   // 超出最大超时时间
}

message BattleEventContext {
  optional com.tencent.wea.xlsRes.BattleEventType sub_event_type = 1;
  optional int64 event_value = 2;
}

enum BattleResultCode {
  BATTLE_RESULT_CODE_WIN = 0; // 胜

  BATTLE_RESULT_CODE_FAIL = 1; // 负

  BATTLE_RESULT_CODE_DRAW = 2; // 平
}

message CommonPlayerBattleResult {
  optional int64 uid = 1; // 玩家uid
  optional int32 result = 2; // 胜、负、平 参考BattleResultCode
}

message PlayerBattleExtraData {
  optional int64 battleId = 1;  // 对局id
  optional int32 cupsNum = 2;    // 奖杯数量
}

message DSNR3E3WereWolfInfo {
  optional int32 sideID = 1;  //阵营id
  optional int32 identityID = 2;  //身份id
  repeated int32 unlockVocationId = 3; // 狼人杀可使用的职业列表
  optional int32 equipAttackAni = 4; // 装备的攻击动画
  optional int32 equipReportAni = 5; // 装备的报告动画
}

message DSNR3E3WolfKillInfo {
  repeated int32 unlockVocationIdList = 1; // 狼人杀可使用的职业列表
  optional int32 equipAttackAni = 2; // 装备的攻击动画
  optional int32 equipReportAni = 3; // 装备的报告动画
  repeated int32 equipEmoji = 4; //装备的表情
  optional int32 equipSpecialSpeak = 5; // 装备的报告动画
  repeated int32 equipAttackAniNew = 6; //随机动画，所以改成list，不随机此字段为一个值
  repeated int32 equipReportAniNew = 7; //随机动画，所以改成list，不随机此字段为一个值
  repeated int32 equipMvpAniNew = 8; //随机动画，所以改成list，不随机此字段为一个值
  optional int32 equipMvpAni = 9; // 装备的MVP动画
  repeated int32 equipTreasure = 10; //装备的珍宝
  optional int32 treasureLevel = 11; //珍宝等级
  repeated int32 robotCannotUseVocationIdList = 12; // 狼人杀机器人屏蔽的职业
  optional int32 hyperCoreScore = 13; //超凡分
  repeated int32 shieldVocationIdList = 14; // 狼人杀屏蔽的职业列表
  optional int32 brawlLastSelected = 15; // 狼人杀大乱斗上局所选项
  optional int32 monthCardLevel = 16; // 狼人杀月卡等级
  optional int64 monthCardEndTs = 17; // 狼人杀月卡过期时间
}

message DSCompetitionData{
  optional  int32 round = 3; //场次
//  optional  int32 score = 4; //积分
//  optional  int32 rank = 5; // 排名
//  map<int32, int32> levelIDMap = 6; // 玩过的
//  map<int32, int32> vocationIDMap = 7; // 玩过的
//  map<int32, int32> sideIDMap = 8; //玩过的
  repeated int32 vocationIDs = 9; //候选的3个身份
}
message ArenaBattleRobotInfo {
  optional int32 configId = 1; //获取AI头像等信息，对应A_AI名称头像表.xlsx的id
}


message ArenaBattleChatInfo {
  optional int64 groupKeyId = 1; //聊天的groupKeyId
}

message ArenaBattlePlayerInfo {
  optional ArenaBattleRobotInfo robotInfo = 1;     // 机器人信息
  optional ArenaBattleChatInfo  chatInfo  = 2;     // 聊天信息
  optional int32 newBeeSingleABTestFlag   = 3;     // 单人新手优化，0 不做优化， 1 优化
  optional string heroStarDataJson = 4;           // 峡谷星数据：heroId -> ArenaHeroStarData
  optional int32 heroStarDisable   = 5;           // 主目标系统是否禁用，0：开启 1：禁用
  optional string heroDataJson = 6;             // 峡谷星数据 e.g：{ 1001:｛ "ceData":{k->v,k->v,...}, "usedCount": 123 ｝}
}


// create_game_session时下发
message BattlePublicInfo {
  optional int64 uid = 1;         // 唯一ID
  optional bool isRobot = 2;      // 是否是机器人
  optional int32 side = 3;        // 阵营
  optional string name = 4;       // 玩家当前的名字
  optional string face = 5;       // 头像
  optional string skin = 6;       // 皮肤描述
  optional string avatarInfo = 7; // avatar信息
  optional int64 roomId = 8;      // 组队房间ID
  optional string dsToken = 9;    // dsToken认证
  optional int32 gender = 10;// 性别
  optional int32 difficulty = 11;// 机器人难度
  optional int32 qualifyingIntegral = 12;// 段位积分
  optional int32 degreeType = 13;// 段位id
  optional int32 degreeID = 14;// 小段位id
  repeated int32 dressUpItems = 15; // 穿戴时装
  optional int32 fashionValue = 16; // 时尚分
  optional int32 robotType = 17; // 机器人类型 1 ai训练机器人 2 普通机器人
  optional int64 matchID = 18; // 匹配ID
  optional int32 matchTypeBattleModeDataCnt = 19;  // 当前玩法玩家BattleModeData数据长度(该玩法最近的战斗场次)
  optional int32 privilegeLevel = 20; // 特权标记
  repeated int32 RecentCampRole = 21; // 特色玩法历史战绩角色ID
  optional int32 matchTypeMMR = 22;        // 模式对应的mmr分
  repeated int32 fashionScore = 23; // 潮流度分数信息
  optional bool isLeader     = 24;      // 是否是leader
  repeated int64 followPlayerUuids =25; // 导播进入的时候，带上关注的玩家，用于第一视角
  optional int32 secondaryNewbieRelatedBattleModeCnt = 26;  // 类似matchTypeBattleModeDataCnt 但是副玩法新手前k局关联的玩法
  optional bool isMidJoin = 27;  // 是否中途加入
  optional int32 vocationId = 28;  // 职业，一些副玩法需要设置职业
  repeated int32 dressUpItemStatus = 29;//装扮状态 dressUpItems 一一对应
  optional DSNR3E3WereWolfInfo wereWolfInfo = 30; //狼人杀信息
  optional int32 continueWinTimes = 31; //连胜场数
  optional int32 reqWarmRoundType = 32;            // 队伍请求的温暖类型 见WarmRoundType
  optional int32 seatIndex = 33;                    // 座位序号
  optional HeadFrame headFrame = 34; // 头像框【废弃】
  optional NamePlate namePlate = 35; // 铭牌【废弃】
  optional string headFrameJson = 36; // 头像框Json
  optional string namePlateJson = 37; // 铭牌Json
  optional string openId = 38; // openid
  optional int32 ShuttleId = 39;  //星梭 ID
  optional int32 ShuttleGameplayType = 40; //星梭类型
  optional int32 heatPowerRank = 41;//热力值的排名

  repeated int32 preparationItems = 42;// 备战道具
  optional bool isBigPackage = 43;//是否为大包

  // 副玩法段位信息 区别于QualifyingDegreeInfo
  // key(参见QualifyType) -> val(QualifyingDegreeInfo) 映射信息
  map<int32, com.tencent.wea.xlsRes.QualifyingDegreeInfo> secondaryGameplayQualifyingInfos = 44;
  optional string secondaryGameplayQualifyingInfosJson = 45;


  optional bool hokUserFightDirectFlag = 46;//hok玩法是否防御塔攻击前几局弱引导
  optional int32 level = 48; // 等级
  optional string clientInfoJson = 49; // 客户端信息Json
  optional string publicGameSettingJson = 50; //透传的玩家设置json

  optional com.tencent.wea.protocol.KVArray kvArray = 70;  // 自定义kv
  optional int64 battleStartTime = 71; // 毫秒时间，服务器值不一定是局内的开始时间
  optional int32 midJoinType = 72;  // 中途加入类型 MidJoinBattleType 如果类型 MJBT_Scene_Change isMidJoin将会是false

  repeated string wolfkillAdminWhiteModuleIds = 103; // 狼人杀白名单配置
  optional DSNR3E3WolfKillInfo wolfKillInfo = 104; //狼人杀的其他配置
  optional string aiLabWRMatchInfoJson = 105;  //ailab温暖局下发的匹配信息 同步用于构建GM信息
  optional DSCompetitionData competitionData = 106; //赛事的配置
  repeated string aiLabChaseWRMatchInfoJson = 107;  //ailab温暖局下发的匹配信息 同步用于构建GM信息 chase玩法
  optional ArenaBattlePlayerInfo arenaInfo = 201;//arena玩法玩家信息
  optional string arenaHeroRankJson = 202;        // 峡谷英雄战力等数据 e.g：{ 1001:｛ "ceData":{k->v,k->v,...}, "usedCount": 123 ｝}
  optional ChaseBattlePlayerInfo chaseInfo = 203; // 大王信息[废弃]
  optional string chaseInfoJson = 204; // 大王信息json:ChaseBattlePlayerInfo
}

message ChaseBattlePlayerInfo{
  optional int32 newBieGuide = 1;  // 新手指引, 1开启，0关闭
}

message BattleAvatarInfo {
  required int64 uid = 1;     // 玩家唯一ID
}

message LevelDropItemInfo {
  optional int32 item_id = 1;
  optional int32 item_count = 2;
  optional int32 limit_count = 3;
  optional int32 current_count = 4;
  repeated com.tencent.wea.protocol.KeyValueInt32 addition = 5;       // 奖励加成
  optional int32 cost_count = 6;
  optional int32 base_count = 7; // 进行加成和上限计算前的基础值
  optional int32 week_limit_count = 8; // 周上限值
  optional bool has_reached_week_limit = 9; // 是否达到周上限
  optional int32 day_limit_count = 10; // 日上限值
  optional bool has_reached_day_limit = 11; // 是否达到日上限
}

message PlayerBaseProfileInfo {
  //昵称
  optional string nickname = 1;
  //性别  1-男 2-女 0-未知
  optional int32 gender = 2;
  //头像url (maybe)
  optional string profile = 3;
  //个性签名
  optional string signature = 4;
  //等级
  optional int32 level = 5;
  //经验
  optional int64 exp = 6;
  //地区
  optional string location = 7;
  //称号
  optional string title = 8;
  //战队名
  optional string teamName = 9;
  //战队身份
  optional string teamTitle = 10;
  //关注数
  optional int32 following = 11;
  //皮肤
  optional int32 dressCount = 12;
  //游戏次数
  optional int32 modTimes = 13;
  //特权开关  0-隐藏按钮，1-不响应点击，2-响应点击
  optional int32 privilegeSwitch = 14;
  //特权等级  红钻等级: 0-代表未开通，1-开通低档，2-开通中档，3-开通高档
  optional int32 privilegeLevel = 15;
  //装扮信息
  repeated int32 dressUpInfos = 16;
  //avatar信息
  optional string platAvatarInfo = 17;
  //各游戏公共数据, 二进制存储, 各自解析, gamesvr不解析
  optional bytes specModInfo = 18;
  //状态信息
  optional com.tencent.wea.xlsRes.RoomStatus roomStatus = 19;
  //40寸头像url
  optional string profile40 = 20;
  //100寸头像url
  optional string profile100 = 21;
  //玩家openId
  optional string openId = 22;
  //拒绝好友申请
  optional bool hideFriendReq = 23;
  //平台ID
  optional int32 platId = 24;
  //64位客户端版本号, 用以取代以前的32位版本号
  optional int64 clientVersion64 = 25;
  //段位
  optional com.tencent.wea.xlsRes.QualifyingDegreeType degreeType = 26;
  //小段位ID
  optional int32 degreeID = 27;
  //小段位星级
  optional int32 degreeStar = 28;
  //玩家时尚分
  optional int32 fashionValue = 29;
}

message LobbyPosition {
  optional int32 x = 1;
  optional int32 y = 2;
  optional int32 z = 3;
}

message LobbyEasterEgg {
  optional int32 item_id = 1;
  optional int32 slot_id = 2;
  optional int64 created_time = 3;
}

message LobbyPlayerInfo {
  optional int64 uuid = 1; // 玩家uuid
  optional string authToken = 2; // 玩家auth token
  optional LobbyPosition pos = 3; // 玩家位置
  optional int32 chat_room_id = 4;  // 所在私服聊天室
  repeated LobbyEasterEgg eggList = 5;  // 彩蛋
  optional com.tencent.wea.xlsRes.LobbyPlayerState status = 6; // 玩家状态
}

message LetsGoBattleDetailTlogData {
  optional string deathPlace = 1; //死亡地点 x,y;x,y;
  optional string batTime = 2; //板子通过秒数 1:s;2:s;
}

// 局内事件
message LetsGoLevelBattleEvent {
  optional int32 level = 1; //关卡ID
  repeated BattleEventContext level_events = 2;
  optional int32 game_mode = 3;             // 模式
  optional LetsGoBattleDetailTlogData tlog_data = 4;  // 局内tlog需求信息
  repeated LevelDropItemInfo drop_item_info = 5;  // 局内掉落
  optional int32 levelIndex = 6; // 当前关卡是第几关
  optional bool randEvent = 7; // 当前关卡是否触发随机事件
  repeated BattleSkillContext skill_data = 8; // 当前关卡技巧信息
  optional int32 skill_grade = 9; // 技巧总分数
}

message BattleSkillContext{
  optional int32 skill_id = 1; //技巧id
  optional int32 skill_count = 2; //技巧使用次数
}

message LetsGoBattleDetailData {
  repeated LetsGoLevelBattleEvent letsgo_events = 1;  // 局内关卡事件
  repeated BattleEventContext global_events = 2; // 局内全局事件
  optional int64 battleId = 3;
  optional int32 matchType = 4;
  optional int64 battleEndTime = 5;
  optional int64 battleStartTime = 6;
  optional bool isBattleFinished = 7;  // 是否是战斗结束的EndBattleRequest(结束时填充true, 中途推出为false)
  optional bool hasQuitSettlement = 8; // 已经退出并发送结算
  repeated LevelDropItemInfo fpsDropItemInfo = 9; // fps各模式局内掉落
  repeated FpsReturnItemInfo fpsReturnItemInfo = 10; // 撤离模式返还给装备来源玩家的掉落信息
  repeated BattlePlayerRankInfo battlePlayerRankInfo = 11; // 用于更新玩家排行榜
  optional string keyBattleData = 12; // 狼人杀局内全局事件
  optional bool isReputationScoreIllegal = 13; // 信誉分违规标记
  optional bool ignoreFinishBattleEventTask = 14; // 不算完成对局,只针对PlayerFinishBattle触发完成的任务
  optional int32 gameModeType = 15; // GameModeType
  optional int32 wolfKillPassiveTime = 16; // 狼人杀总挂机时长
  optional string CustomDefineData = 17; // 自定义结算信息, 以json格式传输
  optional UgcBattleMultiLevelData ugcBattleMultiLevelData = 18;  // 多场景数据
  repeated int32 ReputationBehaviorTypeId = 19; //违规事件id
  optional int32 offlineTime = 20; // 中途离线时长
  optional string arenaHighlightJson = 22;// arena pk高光信息
}

// 撤离模式返还给装备来源玩家的掉落信息
message FpsReturnItemInfo {
  optional int64 uid = 1; // 玩家uid
  repeated LevelDropItemInfo itemInfo = 2; // 道具信息
  repeated int32 equipItemId = 3; // 装备组信息
}


message BattlePlayerRankInfo {
  optional int32 rankId = 1;
  optional int32 score  = 2;
  optional int32 addition = 3;

}

message ChampionScoreGradeInfo {
  optional int64 uid = 1;
  optional int32 score = 2;
  optional com.tencent.wea.xlsRes.MatchGrade matchGrade = 3;
}

message ChampionTeamInfo {
  optional int64 roomId = 1;
  repeated ChampionScoreGradeInfo championTeamPlayer = 2;
  optional int64 mvpUid = 3;
}

message LobbyChatRoomInfo {
  optional ChatGroupKey groupKey = 1;
}

// 配置表查询
message ResTableRow {
  optional string table = 1;    // 配置名称
  optional string key = 2;     // 键
  optional bytes row = 3;      // 值
}

enum ExitLobbyCode {
  EXIT_LOBBY_CODE_OFFLINE = 0;                    // 正常离线

  EXIT_LOBBY_CODE_MATCH_SUCC = 1;                 // 匹配成功, 离开大厅

  EXIT_LOBBY_CODE_ENTER_OTHER_PLAYER_LOBBY = 2;   // 进入其他大厅
  
  EXIT_LOBBY_CODE_ENTER_XIAOWO = 3;               // 进入小窝

  EXIT_LOBBY_CODE_LOBBY_STATE_INVALID = 4;        // 大厅状态错误

  EXIT_LOBBY_CODE_ENTER_TIMEOUT = 5;              // 大厅进入超时

  EXIT_LOBBY_PLAYER_READY_FAIL = 6;               // 玩家准备操作失败

  EXIT_LOBBY_CODE_ENTER_HUD = 7;                  // 进入HUD界面
  EXIT_LOBBY_CODE_ENTER_STARP = 8;               // 进入啾灵世界
}

// DS Tlog类型
enum BattleDsTlogType {
  BDTT_MAP_ACTION_FLOW = 1;
  BDTT_PLAYER_BATTLE_POS_FLOW = 2;
  BDTT_SUSPECT_ROUND_DETAIL_FLOW = 3;
  BDTT_DOLL_RUN_ROUND_DETAIL_FLOW = 4;
  BDTT_HIDE_N_SEEK_ROUND_DETAIL_FLOW = 5;
  BDTT_LOBBY_ACTION_FLOW = 6;
  BDTT_MAP_COMMON_ACTION_FLOW = 7;
  BDTT_UGC_MAP_ACTION_FLOW = 8;
  BDTT_NEWBIE_MAP_ACTION_FLOW = 9;
  BDTT_FPS_MAP_ACTION_FLOW = 10;
  BDTT_BIO_ROUND_DETAIL_FLOW = 11;
  BDTT_WEAPON_ROUND_DETAIL_FLOW = 12;
  BDTT_DDP_ROUND_DETAIL_FLOW = 13;
  BDTT_UGC_EDITOR_TRIAL_FLOW = 14;
  BDTT_FRONT_LINE_ROUND_DETAIL_FLOW = 15;
  BDTT_PLAYER_MAP_EVALUATE_Flow = 16;
  BDTT_SEC_ROUND_START_FLOW = 17;     // 安全流水：游戏对局开始
  BDTT_SEC_ROUND_END_FLOW = 18;       // 安全流水：游戏对局结束
  BDTT_SEC_ITEM_GET_FLOW = 19;        // 安全流水：对局道具获得流水
  BDTT_SEC_ROUND_DETAIL = 20;         // 安全流水：对局（轮）过程统计
  BDTT_SEC_GAME_SAFE_DATA = 21;       // 安全流水：轻特征上报数据
  BDTT_RACING_ROUND_DETAIL_FLOW = 22;
  BDTT_BATTLE_ROYALE_ROUND_DETAIL_FLOW = 23;
  BDTT_BIO_FIGHT_ROUND_DETAIL_FLOW = 24;
  BDTT_SEC_VERIFY_FLOW = 25;          // 安全流水：服务端校验结果日志
  BDTT_MULTI_UGC_MAP_ACTION_FLOW = 26;
  BDTT_TYC_ROUND_DETAIL_FLOW = 27;
  BDTT_SEC_ROUND_DETAIL_START = 28;   // 安全流水：对局（轮）开始
  BDTT_TYCTD_ROUND_DETAIL_FLOW = 29;
  BDTT_TYCTDS_ROUND_DETAIL_FLOW = 30;
  BDTT_GAME_FRAME_WORK_FLOW = 31;
  BDTT_CHASE_ROUND_DETAIL_FLOW = 32;
  BDTT_BOUNTY_ROUND_DETAIL_FLOW = 33;
  BDTT_DODGE_BALL_ROUND_DETAIL_FLOW = 34;
  BDTT_EVACUATE_ROUND_DETAIL_FLOW = 35;
  BDTT_CHASE_SURRENDER_FLOW = 37;
  
  BDTT_RG_READY_FLOW = 38;
  BDTT_RG_ROUND_DETAIL_FLOW = 39;
  BDTT_RG_ITEM_FLOW = 40;
  BDTT_RG_TASK_FLOW = 41;
  BDTT_RG_MARK_FLOW = 42;
  BDTT_RG_MARK_BOARD_FLOW = 43;
  BDTT_RG_TALENT_ACTIVATE_FLOW = 44;

  BDTT_ARENA_HERO_ATTRIBUTES_FLOW = 45;
  BDTT_ARENA_ECONOMY_FLOW = 46;
  BDTT_ARENA_CARD_FLOW = 47;
  BDTT_ARENA_ADD_POINT_FLOW = 48;
  BDTT_ARENA_ROUND_RESULT_FLOW = 49;
  BDTT_ARENA_ROUND_DETAIL_FLOW = 50;
  BDTT_UGC_MALL_FLOW = 51;
  BDTT_UGC_MONEY_FLOW = 52;
  BDTT_UGC_ITEM_FLOW = 53;
  BDTT_ARENA_BR_ROUND_RESULT_FLOW = 54;
  BDTT_ARENA_BR_ROUND_DETAIL_FLOW = 55;
  BDTT_SUSPECT_EMOTICON_USE_FLOW = 56;
  BDTT_SUSPECT_BATTLE_BEGIN_FLOW = 57;
  BDTT_UGC_CODING_FLOW = 58;
  BDTT_SEC_CANYON_3V3_ROUND_DETAIL_FLOW = 59;         // 安全流水：峡谷3v3细节流水
  BDTT_SEC_CANYON_EAT_CHICKEN_ROUND_DETAIL_FLOW = 60; // 安全流水：峡谷吃鸡玩法细节流水
  BDTT_SEC_AFK_FLOW = 61;                             // 安全流水：挂机相关流水
  BDTT_ARENA_HANG_UP_FLOW = 62;
  BDTT_ARENA_BR_CARD_FLOW = 63;
  BDTT_ARENA_BATTLE_BEGIN_FLOW = 64;
  BDTT_UGC_TRIGGER_EXAMINE_FLOW = 65;
  BDTT_UGC_TRIGGER_GROUP_OP_FLOW = 66;
  BDTT_SEC_CANYON_5V5_ROUND_DETAIL_FLOW = 67;         // 安全流水：峡谷5v5细节流水
  BDTT_ARENA_BAN_PICK_FLOW = 68;
  BDTT_UGC_SCRIPT_FLOW = 69;                          // UGC API日志
  BDTT_ARENA_FOOT_BALL_ROUND_RESULT_FLOW = 70;         // 峡谷足球对局结果流水
  BDTT_OGCAI_CONTROL_FLOW = 71; // 天天晋级赛AI托管流水

  // 比赛狼人杀
  BDTT_SUSPECT_EVENT_FLOW = 77;

  // 塔防兽人区间110 --- 130
  BDTT_OMD_ROUND_DETAIL_FLOW = 110;

  // HOK 相关
  BDTT_HOK_FIGHT_MONSTER_FLOW = 131;
  BDTT_HOK_FIGHT_SOLDIER_FLOW = 132;
  BDTT_HOK_FIGHT_TOWER_FLOW = 133;
  BDTT_HOK_SKILL_FLOW = 134;
  BDTT_HOK_BATTLE_RESULT_FLOW = 135;
  BDTT_HOK_TOWER_DESTROY_FLOW = 136;
  BDTT_HOK_SELECT_CARD_FLOW = 137;
  BDTT_ARENA_TD_ROUND_DETAIL_FLOW = 138;
  BDTT_ARENA_TD_ROUND_RESULT_FLOW = 139;
  BDTT_ARENA_TD_ECONOMY_FLOW = 140;
  BDTT_ARENA_TD_TURRET_FLOW = 141;
  BDTT_HOK_NEWBIE_TASK_FLOW = 142;
  BDTT_ARENA_SURRENDER_FLOW = 143;
  BDTT_HOK_SETTING_FLOW = 144;
  BDTT_ARENA_TD_HERO_ATTRIBUTES_FLOW = 145;
  BDTT_ARENA_TD_CARD_FLOW = 146;
  BDTT_ARENA_TD_RIDING_FLOW = 147;

  // MayDay
  BDTT_MAY_DAY_ROUND_BEGIN_FLOW = 151;
  BDTT_MAY_DAY_ROUND_DETAIL_FLOW = 152;
  BDTT_MAY_DAY_ROUND_RESULT_FLOW = 153;
  BDTT_MAY_DAY_MEETING_FLOW = 154;
  // FPS
  BDTT_DS_PERFORMANCE_FLOW = 161;
  // BandWidth
  BDTT_DS_BANDWIDTH_FLOW = 162;

  // Arena HotZone
  BDTT_HOT_ZONE_ECONOMY_FLOW = 170;
  BDTT_HOT_ZONE_CARD_FLOW = 171;
  BDTT_HOT_ZONE_ROUND_RESULT_FLOW = 172;
  BDTT_HOT_ZONE_ROUND_DETAIL_FLOW = 173;
  BDTT_SEC_CANYON_3V3OP_ROUND_DETAIL_FLOW = 174;    // 安全流水：峡谷占地盘细节流水
  UGC_STATIC_BATCH_LOAD_FLOW = 175;   // ds ugc静态合批加载信息(已废弃)

  BDTT_CHASE_STATUS_FLOW = 176;   // 大王局内状态流水

  // 静态合批
  BDTT_UGC_MAP_LOAD_STATIC_BATCH_INFO_FLOW = 201;

  BDTT_SUSPECT_DUEL_FLOW = 210; //非常嫌疑人决斗流水
  BDTT_SUSPECT_TREASURE_FLOW = 211; //非常嫌疑人寻宝流水

  // id 1001 Arena峡谷玩法开始⬇️
  BDTT_ARENA_SOLO_KILL_FLOW = 1001;   // 峡谷模式单杀流水

  BDTT_ARENA_RESERVED_FLOW = 1100; // 峡谷模式结束
  // id 1100 Arena峡谷玩法结束⬆️
}

// DataFlowGameplayType 对应C++的ECurrentGameplayType
enum DataFlowGameplayType {
  DFGT_None = 0;
  DFGT_MAIN = 1; // 天天晋级赛
  DFGT_UGC = 2; // UGC
  DFGT_DDP = 3; // 动物派对
  DFGT_BIO_CHASE = 4; // 生化追击
  DFGT_GUN = 5; // 武器大师
  DFGT_HIDE_AND_SEEK = 6; // 躲猫猫
  DFGT_WERE_WOLF = 7; // 非常嫌疑人
  DFGT_DOLLS_RUN = 8; // 谁是杀手
  DFGT_LOBBY = 9; // 大厅
  DFGT_UGC_EDITOR = 10; // UGC编辑器
  DFGT_GUN_GAME_TDM = 11; // FPS团竞(枪战团队冲锋玩法)
  DFGT_NEWBIE = 12; // 新手引导
  DFGT_GUN_GAME_BPM = 13; // FPS团竞(枪战团队爆破玩法)
  DFGT_BATTLE_ROYALE_GAME = 14; // FPS(BR大逃杀玩法)
  DFGT_JS = 15; // 极限竞速
  DFGT_TYCOON = 17; // 塔防大亨
  DFGT_GUN_GAME_KC = 18; // FPS团竞(枪战团队赏金玩法)
  DFGT_CHASE = 19; // 追逐
  DFGT_DDB = 20; // 躲避球
  DFGT_DF = 21; // 撤离
  DFGT_DND = 22;
  DFGT_OMD = 23;
  DFGT_ROGUE_LIKE = 24; // 肉鸽
  DFGT_ARENA = 25;
  DFGT_MOBA_TOWER = 27; // moba塔防
  DFGT_HOK = 28; // moba 5v5
  DFGT_MAY_DAY = 29;
  DFGT_STAR_P = 30; // 星灵玩法
  DFGT_FARM = 40; // Farm,Cook,House
}

// 大厅配置同步的类型 废弃
enum LobbyActivityInfoFieldType {
  LAIFT_NONE = 0;
  LAIFT_RedEnvelopeRuleSettingData = 1;  // redEnvelopeRuleSettingData
  LAIFT_RedEnvelopeActivityData = 2;     // redEnvelopeActivityData
  LAIFT_RedEnvelopeSpawnerData = 3;     // redEnvelopeSpawnerData
}

// 单个玩家ds日志级别
message DSLogCtrlPlayerInfo {
  optional int64 playerUid = 1;
  optional int32 dsLogLevel = 2;
}

// ds日志控制
message DSLogCtrlInfo {
  optional int32 gameDsLogLevel = 1;  // 默认为0, 不输出日志
  repeated DSLogCtrlPlayerInfo playerDsLogList = 2; // 单个玩家ds日志级别
}

// DS Monitor类型
enum BattleDsMonitorType {
  BDMT_LOCALDS_RESULT = 1; //拉起成功或者失败, //1 成功 0 失败
  BDMT_LOCALDS_ROBOT = 2; //AI人数
  BDMT_LOCALDS_CONCURRENCY = 3; //请求并发数
  BDMT_LOCALDS_REQUESTTIME = 4; //请求耗时
}

message ResTableKeys {
  optional string table = 1;      //  表名
  repeated string keys = 2;       //
}

enum BattleMidJoinType {
  BMJT_PullPlayerOnMatch = 1;  // 匹配要人
  BMJT_EnableMidJoinOnMatch = 2;  // 匹配允许加人
  BMJT_DisableMidJoinOnMatch = 3;  // 匹配不允许加人
  BMJT_EnableMidJoinOnInvite = 4;  // 邀请允许加人
  BMJT_DisableMidJoinOnInvite = 5;  // 邀请不允许加人
}

enum ArenaHeroInfoChangeReason {
  AHICR_Invalid = 0; // 无效值
  AHICR_HeroUnlock = 1; // 英雄解锁
  AHICR_HeroSkin = 2; // 皮肤更改
}

// 自定义结算信息
message BattleCustomResult{
  optional int32 custom_type = 1;
  optional int32 custom_int_value = 2;
  optional string custom_string_value = 3;
}

message StreamAudienceSimpleInfo {
  optional string head_url = 1;  // 观众url
  optional string nick_name = 2; // 观众昵称
}

enum BattleSceneChangeResultCode {
  BSCRC_OK = 0;  // 成功
  BSCRC_BattleNotExist = 1;  // 对局不存在了
  BSCRC_SceneNotExist = 2;  // 请求场景不存在了
  BSCRC_MemberLimit = 3;  // 人数超限
  BSCRC_CreateDSError = 4;  // 拉起失败
  BSCRC_SceneJoinFail = 5;  // 加入目标场景失败-用于回到当前场景
  BSCRC_SceneJoinMainSceneFail = 6;  // 加入主场景失败-说明此时主场景已经被销毁了
  BSCRC_MemberNotExist = 7;  // 玩家不存在
  BSCRC_PlayerStatusError = 8;  // 玩家状态异常
  BRCRC_PlayerSceneError = 9;  // 玩家已经不在当前场景
  BRCRC_SceneChangeNotEnable = 10;  // 不允许进行场景跳转
  BRCRC_SceneChangUniqueStatusError = 11;  // 唯一场景状态异常
}
