syntax = "proto2";
package com.tencent.wea.protocol;
option java_package = "com.tencent.wea.tcaplus.db";
import "base_common.proto";
import "common.proto";
import "g6_common.proto";
import "attr_DsUserDBInfo.proto";
import "attr_LuckyStarInfo.proto";
import "ResKeywords.proto";
import "competition_common.proto";
import "attr_XiaoWoPlayerLiuYanMessageInfo.proto";
import "attr_XiaoWoLiuYanMessageInfo.proto";
import "attr_FarmPlayerLiuYanMessageInfo.proto";
import "attr_FarmLiuYanMessageInfo.proto";
import "attr_AnimalHandbookAnimalInfo.proto";
import "attr_AttrOnDueDateCardData.proto";
import "google/protobuf/descriptor.proto";

// 标识离线期间收到的指令在重新登录时执行的阶段， 玩家在线时候收到的指令仍然会立即处理
enum InteractionExeStage {
  IES_Invalid = 0;
  IES_AfterLogin = 10;           // 离线期间收到的指令会在PlayerModule::afterLogin执行前执行完
  IES_AfterLoginFinish = 20;     // 离线期间收到的指令会在PlayerModule::afterLoginFinish执行前执行完
  IES_AfterLoginFinishDone = 30; // 离线期间收到的指令会在PlayerModule::afterLoginFinish执行完成后再执行
}
extend google.protobuf.EnumValueOptions {
  optional InteractionExeStage exe_stage = 11051559;
}

// Player Interaction Instruction
// player交互指令
// PII_NORMAL_INSTRUCTION_START ~ PII_NORMAL_INSTRUCTION_END 在线和离线玩家都可以收到指令
// PII_ONLINE_ONLY_INSTRUCTION_START ~ PII_ONLINE_ONLY_INSTRUCTION_END 仅在线玩家会收到指令
// PII_OFFLINE_ONLY_INSTRUCTION_START ~ PII_OFFLINE_ONLY_INSTRUCTION_END 存储在unhandled中, 不自动执行
// PII_UNSAFE_INSTRUCTION_START ~ PII_UNSAFE_INSTRUCTION_END 在线和离线玩家都可以收到指令, 但不保证完全可靠(可能执行多次)
enum PlayerInteractionInstruction {
  PII_INVALID = 0;

  PII_NORMAL_INSTRUCTION_START = 1;
  // 以下定义在线和离线玩家都可以收到指令
  PII_DS_SETTLEMENT = 2;
  PII_FRAME_SETTLEMENT = 3;
  PII_INVITEE_REGISTER = 4;
  PII_UPDATE_LOBBY_INFO = 7;
  PII_UPDATE_SCENE_INFO = 8;
  PII_CHANGE_SUB_RANKS = 9;
  PII_FINISH_LEVEL = 10;
  PII_MODIFY_ITEM = 11;                             // 修改道具
  PII_MODIFY_TASK_PROGRESS = 12;                    // 修改任务进度
  PII_MODIFY_BP_DATA = 13;                          // 修改bp数据
  PII_MODIFY_PLAYER_INFO = 14;                      // 修改玩家信息
  PII_UGC_OPERATE_MAP = 15;                         // ugc操作地图
  PII_BATTLE_END_EX_SETTLEMENT = 17;                // 战斗结束时额外的结算信息(不同于PII_DS_SETTLEMENT 此时有全部排名等信息)
  PII_BATTLE_LIKE = 18;                             // 对局结算点赞
  PII_MODIFY_FRIEND_RELATION = 19;                  // 修改好友亲密度
  PII_MODIFY_VIP_EXP = 20;                          // 修改vip经验
  PII_MODIFY_MONTH_CARD = 21;                       // 修改月卡信息
  PII_SET_USER_LABEL = 22;                          // 设置用户标签
  PII_RELATION_AGREE = 23;                          // 关系链同意/拒绝申请
  PII_RELATION_APPLY = 24;                          // 关系链申请
  PII_INTIMACY_CHANGE = 25;                         // 亲密度变化
  PII_MODIFY_PLAYER_REGION = 26;                    // 修改玩家地区
  PII_PROCESS_GUIDE_TASK = 27;                      // 新手引导任务处理
  PII_RELATION_REMOVE = 28;                         // 关系链移除
  PII_UGC_TASK_PLAY_COUNT = 29;                     // ugc地图游玩量变化（游玩人数）
  PII_SEND_QA_INVEST_REWARD = 30;                   // 问卷调查奖励
  PII_SNS_INVITATION = 31;                          // 分享邀请
  PII_SEND_OPEN_LUCKY_MONEY_REWARD = 32;            // 打开好友超级红包奖励
  PII_CLEAR_PARTY = 33;
  PII_SET_DS_REPLAY_RECORD_INFO = 34;               //设置玩家开启局内录像
  PII_RELATION_APPLY_SELF_INSERT = 35;              // 给申请人自己的（申请人不在线的情况）
  PII_OFFLINE_SAVE_DS_DB_INFO = 36;                 // 玩家离线后，存储DS的需要落地的属性
  PII_DEL_LAYOUT = 37;                              // 删除玩家方案
  PII_RECRUITE_CONFIRM = 38;                        // 确认招募
  PII_RECRUITE_SIGN = 39;                           // 招募缔约
  PII_CLUB_CHANGE = 40;                             // 社团变动
  PII_ADD_WARM_ROUND = 41;                          // 加一局温暖局
  PII_MATCH_ISOLATE = 42;                           // 设置匹配隔离
  PII_OPEN_RED_PACKET = 43;                         // 打开春节红包
  PII_DEDUCT_LUCKY_STAR = 44;                       // 扣除福星卡
  PII_ADD_LUCKY_STAR = 45;                          // 增加福星卡
  PII_RED_ENVELOPE_RAIN_ADD_EXTRA_OPP = 46;         // 增加红包雨额外打开机会
  PII_AFTER_RECV_RED_PACKET = 47;                   // 领取春节红包
  PII_CLUB_SUBSCRIPTION = 48;                       // 社团订阅事件
  PII_RED_PACKET_DESTROY = 49;                      // 红包销毁
  PII_PATCH_RED_PACKET = 50;                        // 修改红包信息
  PII_OFF_END_CONSUME_NOTIFY = 51;                  // 端外消费通知
  PII_MODIFY_XIAOWO_LIU_YAN_MESSAGE_INFO = 52;      // 修改家园留言信息
  PII_AMS_ITEM_SEND = 53;                           // AMS道具发送
  PII_FPS_RETURN_ITEM = 54;                         // 撤离模式返还道具
  PII_SEND_ITEM_INTO_BAG = 55;                      // 发送道具进背包
  PII_GIVE_INTER_SERVER_GIFT = 56;                  // 赠送好友一元幸启
  PII_HELP_KUNG_FU_PANDA_RACING = 57;               // 助力功夫熊猫竞速
  PII_SET_COMMON_USER_LABEL = 58;                   // 设置用户标签-通用
  PII_QUALIFYING_SETTLEMENT = 59;                   // 段位结算
  PII_SYNC_GAME_TV_REWARD_STATUS = 60;              // 同步电视台奖励信息
  PII_MODIFY_FARM_LIU_YAN_MESSAGE_INFO = 61;        // 修改农场留言信息
  PII_CHAT_CONTROL = 62;                            // 聊天控制
  PII_MODIFY_RECHARGE_LEVEL = 63;                  // 氪条修改
  PII_WERE_WOLD_CONSUME_ITEM = 64;                  // 狼人杀指定阵营身份扣除道具
  PII_MODIFY_REPUTATION_SCORE = 65;                 // 玩家信誉分修改
  PII_REPORT_REPUTATION_SCORE_BEHAVIOR = 66;        // 上报玩家信誉分行为
  PII_MODIFY_FARM_MONTH_CARD = 67;                  // 修改农场月卡
  PII_RECRUITE_LOGIN = 68;                           // 招募登录事件
  PII_ClUB_SIMPLE_NOTIFY = 69;                      // 社团简单通知
  PII_WISH_ACTIVITY_HELP_BIND = 70;                 // 心愿活动助力绑定
  PII_ACTIVITY_SVR_REWARD = 71;                     // 活动服务通知发奖
  PII_DEDUCT_ANIMAL_HANDBOOK_ITEM = 72;             // 扣除动物图鉴
  PII_ADD_ANIMAL_HANDBOOK_ITEM = 73;                // 增加动物图鉴
  PII_TRAIN_CAMP_ADD_SCORE = 74;                    // 特训营活动加分
  PII_ARENA_CARD = 75;                              // Arena卡牌
  PII_LUCKY_FRIEND_APPLY_TASK = 76;                 // 幸运好友邀请参加任务
  PII_OMD_LOGIN              = 77;                  // OMD兽人登录
  PII_OMD_ENTER_LOBBY_LOGIN  = 78;                  // OMD兽人进入大厅
  PII_WOLFKILL_ROLEINFO_SETTLEMENT = 79;            // 狼人专精结算
  PII_LUCKY_FRIEND_OPERATE_TASK_APPLY = 81;         // 幸运好友处理任务邀请
  PII_MODIFY_CHARGE_PROGRESS = 82;                  // 修改氪条
  PII_MODIFY_SEASON_FASHION_EQUIP_BOOK = 83;        // IDIP修改时尚手册
  PII_IDIP_DELIVER_MIDAS_PRODUCT = 84;              // IDIP补发米大师商品
  PII_ARENA_TIP = 85;                               // moba打赏通知,客态
  PII_VERBAL_VIOLATION_REPUTATION_SCORE_PUNISH = 86;// 言语违规信誉分处罚(安全侧触发)
  PII_VERBAL_VIOLATION_REPUTATION_SCORE_UN_PUNISH = 87; // 言语违规信誉分误处罚回撤并补分(安全侧触发)
  PII_PIC_LIKE = 88 [(exe_stage) = IES_AfterLoginFinishDone];    //玩家相册点赞
  PII_SUPER_CORE_RANK_ACTIVITY_SCORE_MODIFY = 89;        // 超核活动玩家排名分数修改
  PII_MALL_GIFT_CARD_RESET_WORDS = 90;              // IDIP重置赠礼卡祝福语
  PII_BIRTHDAY_BLESSING = 91;                       // 生日祝福赠送数据
  PII_BIRTHDAY_CARD = 92;                           // 生日贺卡数据
  PII_BIRTHDAY_GIFT = 93;                           // 生日赠礼数据
  PII_LUCKY_STAR_NORMAL_ASSIST = 95;              // 新春福星普通助力
  PII_LUCKY_STAR_SPECIFY_ASSIST = 96;           // 新春福星助力
  PII_LUCKY_STAR_GIVE_SLIP = 97;           // 赠送福签
  PII_LUCKY_STAR_RECEIVE_SLIP = 98;           // 接收
  PII_BIRTHDAY_CARD_RESET_WORDS = 99;               // IDIP重置生日贺卡祝福语
  PII_BIRTHDAY_CARD_ON_DUE_DATE_SEND = 100;         // 生日贺卡到期送
  PII_WOLFKILL_VOCATION_SHARE = 101;         // 共享身份
  PII_WOLFKILL_ANI_SHARE = 102;          // 共享动画
  PII_PIC_AT_TARGET = 103 [(exe_stage) = IES_AfterLoginFinishDone];    //玩家相册图片At
  PII_PIC_DEL_TARGET_AT_INFO_DEL = 104[(exe_stage) = IES_AfterLoginFinishDone];    //通知目标图片已移除,移除被该图片@的记录
  PII_TASK_PASSWORD_CODE_USE = 106;         // 口令码任务使用同步
  PII_CHASE_SETTLEMENT = 80;            // 大王专精结算
  PII_FARM_DRAGON_TEAM_POINTS_UPDATE = 107;//农场组队活动积分更新
  PII_DISPLAY_BOARD_RESET_WORDS = 108;               // IDIP清空推图宣言
  PII_AQ_RESET_ITA_BAG = 109;               // IDIP清空痛包吧唧

  //=========== 啾灵SP ===============
  PII_STARP_ENTER_CANCEL        = 200;               // 啾灵未开战处罚
  PII_STARP_QUIT_BATTLE         = 201;               // 啾灵副本退出通知
  //=========== 啾灵SP ===============

  PII_NORMAL_INSTRUCTION_END = 10000000;

  /* ----------------------------------------------- */

  PII_ONLINE_ONLY_INSTRUCTION_START = 10000001;
  // 以下定义仅在线玩家会收到指令
  PII_REMIND_NEW_MAIL = 10000002;
  PII_SEND_NOTICE = 10000003;
  PII_SEND_CHARGE_FLOW = 10000004;
  PII_REMIND_SQUAD_UPDATE = 10000005; // 小队数据需要更新
  // PII_SEND_QA_INVEST_REWARD = 10000006; // 问卷调查奖励
  PII_FOLLOW_PLAYER = 10000007;
  PII_SQUAD_REFRESH = 10000008; // 小队数据刷新
  PII_SEND_BROADCAST_NOTICE = 10000009; // 发送广播notice

  PII_ONLINE_ONLY_INSTRUCTION_END = 20000000;

  /* ----------------------------------------------- */

  PII_OFFLINE_ONLY_INSTRUCTION_START = 20000001;
  // 以下定义仅存储指令, 存储在unhandled中, 不自动执行.
  PII_OFFLINE_ONLY_INSTRUCTION_BATTLE = 20000002;
  PII_OFFLINE_ONLY_INSTRUCTION_CHAT_SEND = 20000003; //
  PII_OFFLINE_ONLY_INSTRUCTION_BIRTHDAY_CARD_ON_DUE_DATE_SEND = 20000004; // 生日贺卡到期送

  PII_OFFLINE_ONLY_INSTRUCTION_END = 30000000;


  /* ----------------------------------------------- */
  PII_UNSAFE_INSTRUCTION_START = 30000001;
  // 以下定义在线和离线玩家都可以收到指令, 但不保证完全可靠(可能执行多次)

  PII_UNSAFE_INSTRUCTION_END = 40000000;

  /* ----------------------------------------------- */
  PII_OFFLINE_INSTRUCTION_START = 40000001;
  // 以下定义会存储在db中, 无论玩家是否在线, 都不通知玩家立即执行. 待下次登陆时执行

  PII_CLIENT_LOG_COLORING = 40000002;
  PII_OFFLINE_INSTRUCTION_END = 50000000;
}

message PiiRemindNewMailParams {
  required int64 mailId = 1;
  optional bytes dbMail = 2;
}

message PiiInviteeRegisterParams {
  optional int64 inviteeUid = 1;
  optional int64 registerTimeMs = 2;
  optional int32 level = 3;
  optional string deviceId = 4;
  optional int32 activityType = 5;
  optional int32 activityId = 6;
  optional bool isRecall = 7; // 标记是回流用户
  optional bool isNew = 8; // 是否是新增操作
}

message MMRSettlementInfo {
  optional int32 score_changed = 1;
  optional int32 mmr_type = 2;
  repeated int32 level_camp_types = 3;      // BET_LEVEL_CAMP_TYPE 部分mmr依赖这个数据
}

message WarmRoundSettlementInfo {
  optional bool is_warm_round = 1 [deprecated = true];              // 废弃 用warm_round_type
  optional int32 battle_level_cnt = 2;
  optional bool is_guide_warm_round = 3;        // 主玩法新手温暖局
  optional WarmRoundType warm_round_type = 4;   // 温暖局类型
}

message PiiDsSettlementParams {
  optional int64 battle_id = 1;
  optional int32 result = 2;
  optional int32 game_id = 3;
  optional int32 mode_id = 4;
  optional LetsGoBattleDetailData detail_data = 5;
  repeated MemberBaseInfo battle_member = 6; // 同玩玩家uid
  repeated LevelDropItemInfo item_list = 7; // 掉落道具
  optional int64 battle_end_time = 8; // 对局结束时间
  repeated int64 room_member = 9;
  repeated int64 side_member = 10; // 阵营玩家信息
  optional int64 room_id = 11;
  repeated int64 champion_member = 12; // 冠军玩家信息
  optional ChampionTeamInfo championTeamInfo = 13; // 冠军队伍玩家得分信息
  optional int32 match_type_id = 14;
  // optional MMRSettlementInfo mmr_settlement = 15; // 废弃
  optional WarmRoundSettlementInfo warm_round_settlement = 16; //温暖局结算信息
  optional int64 battle_create_time = 17; // 对局创建时间
  optional int32 fashion_rank = 18;
  optional CommonPlayerBattleResult battle_result = 19;
  optional int64 ugcId = 20;
  optional int32 roomInfoID = 21;  // 对应的匹配房间类型id
  optional int32 mapSource = 22;
  optional CompetitionSettlementInfo competition_settlement = 23; // 赛事结算信息
  // 目前只有在主副玩法并且是单人模式的情况下才会有, 并且这个列表排除了机器人
  repeated RoomMemberBattleSettlementInfo room_member_infos = 24;
  optional int64 teg_game_id = 25;                                // aiLab缀合的关联gameId
  optional AlgoInfo recommendMapAlgoInfo = 26; // UGC结伴同玩，推荐地图的标识
  repeated int64 observe_players = 27; //观战玩家Uid列表
  optional string fromCollectionId = 28;
  optional int32 logicMapSource = 29;
  optional BattleSettlementExtraInfo settlementExtraInfo = 30;
  optional int32 aiScriptId = 31;    // 对局中ai的剧本ID
  optional int64 mapPoolId = 32;
  repeated BattlePlayerCoMatchInfo coMatchInfoList = 33; // 初始的同阵营玩家的同游匹配信息，提前退出的玩家以及机器人默认拒绝
  repeated BattleCustomResult customResults = 35;
  optional string levelRoundABTestTag = 36;
  optional MatchDynamicConfigData matchDynamicConfigData = 37;
  optional UgcMultiRoundScoreSettlementInfo ugcMultiRoundScoreSettlementInfo = 38; // 多轮地图结算数据
  repeated MatchTypeABTestInfo matchTypeABTestInfo = 39; // 玩法实验信息
  optional MemberInBattleExtraList memberInBattleExtraList = 40;  // 开局后的玩家信息
}

message PiiFrameSettlementParams {
  optional int32 gameTime = 1; // 总的游戏场次
  optional int32 stars = 2; // 总的星数
  optional int32 fullCombo = 3; // 总的全连接数
  optional int32 mode = 4;
  optional int64 battleStartTime = 5;
  optional int64 battleEndTime = 6;
  optional bool isTeamMatch = 7;
  optional string songId = 8;
  optional int32 difficult = 9;
  optional int32 gameResult = 10;
  optional int32 score = 11;
  repeated int64 teamPlayerId = 12;
  optional int32 starCountByOneGame = 13; // 单局的星数
}

message PiiSendNoticeParams {
  optional PlayerNoticeMsgType type = 1;    // 通知消息类型
  repeated int64 numVal = 2;             // 扩展参数
  repeated string strVal = 3;            // 扩展参数
  repeated string data              = 4;  // 消息数据
}
message PiiSendBattleEndNtfParams {
  optional bytes msg = 1;  // RpcBattleEndDataNtfReq 通知消息类型
}

message PiiSendClubParams {
  optional PlayerClubMsgType type = 1;  // 通知消息类型
  optional int64 cid = 2;
  optional ClubBasicInfo basicInfo = 3;
  optional int32 res = 4;
}

message PiiSendChatParams {
  optional ChatGroupKey groupKey = 1;
  optional GeneralChatMsg msg = 2;
}

message PiiUpdateLobbyInfoParams {
  optional int64 lobbyId = 1;
  optional int32 mapId = 2;
  optional int64 ugcId = 3;
  optional int32 ugcSafeStatus = 4;
  optional int64 lobbyCreatorUid = 5;
}

message PiiUpdateSceneInfoParams {
  optional int32 mapId = 1;
  optional int64 sceneId = 2;
  optional bool isLobby = 3;  // true表示当前场景是大厅，会更新gamesvr中玩家lobbyId
  optional int64 roundId = 4; // 当前对局id
}

message PiiSendChargeFlowParams {
  optional int32  amount = 1;  // 充值金额
  optional string payItem = 2; // payitem字符串
}

message PiiRemindSquadUpdate {
  optional int64 squadId = 1; // 小队id
}

message PiiChangeSubRanksParams {
  optional int32 fromSize = 1;
  optional int32 toSize = 2;
  repeated int32 rankIds = 3;

  repeated int32 success = 10;
  repeated int32 failed = 11;
  optional int64 ts = 12;
}

message PiiDsLevelFinishParams {
  optional int64 battle_id = 1;
  optional int32 matchType = 2;
  optional int32 levelId = 3;
  optional int32 levelIndex = 4;
  repeated BattleEventContext context = 5;
  optional int32 battlePlayerCount = 6;
  optional int32 competitionSeason = 7;
  optional int32 competitionGameType = 8;
  repeated int64 battlePlayers = 9;
  optional FreshPlayerRanksInfo ranksInfo = 10;
  repeated int64 teamPlayers = 11;
  repeated int64 roomMember = 12;    // 组队玩家UID
  optional int64 ugcId = 13;
  optional int32 warmRoundType = 14;
  optional CompetitionBattleData competitionBattleData = 15;
  optional MemberBaseInfo battlePlayer = 16;
  optional int32 gameModeType = 17;
  optional UgcLevelSettlementInfo ugcSettlementInfo = 18;  //ugc结算信息
  optional string levelRoundABTestTag = 19;
  optional int32 aiDifficultyTestType = 20;                // ai难度实验tag类型
  optional bool randEvent = 21;                // 是否触发随机事件
  repeated BattleSkillContext skillData = 22; // 当前关卡技巧信息
  optional int32 skillGrade = 23; // 技巧总分数
}

// 修改道具
message PiiModifyItemParams {
  optional uint32 OperateType = 1;          // 操作类型, 0: 扣除; 1: 赠送
  repeated ItemInfo ItemList = 2;           // 道具列表
  optional uint32 Source = 3;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 4;               // 流水号
}

// 修改任务进度
message PiiModifyTaskProgressParams {
  optional uint32 TaskId = 1;               // 任务id
  optional uint64 Progress = 2;             // 任务进度
  optional uint32 Source = 3;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 4;               // 流水号
  repeated uint32 TaskIdList = 5;           // 任务id列表(分号分隔)
  repeated uint64 ProgressDiffList= 6;      // 任务进度差值(分号分隔) (+:增加进度值 -:减少进度值(最少只能减到0))
  repeated uint64 ProgressTotalList = 7;    // 任务进度总值(分号分隔)
}

// 修改bp数据
message PiiModifyBpDataParams {
  optional uint32 DataType = 1;             // 数据类型, 0: bp经验; 1: bp资格
  optional int32 BpExpModifyValue = 2;      // bp经验修改值
  optional uint32 BpLevelOperateType = 3;   // bp资格操作类型, 0: 发放bp资格; 1: 回收bp资格
  optional uint32 BpLevelType = 4;          // bp资格类型, 0: 普通; 1: 进阶; 2: 荣耀
  optional uint32 Source = 5;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;               // 流水号
}

message PiiSendQAInvestRewardParams {
  optional int32  qaInvestId = 1;  // 问卷ID
}

message PiiFollowPlayerParams {
  optional int64 followerUid = 1;
  optional int32 type = 2;  // 0:关注，1:取消关注
}

// 修改玩家信息
message PiiModifyPlayerInfoParams {
  optional uint32 ChangeType = 1;           // 修改类型, 见枚举IdipPlayerInfoModifyType所示
  optional string ChangeContent = 2;        // 修改后内容
  optional uint32 Source = 3;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 4;               // 流水号
}

// ugc地图操作类型
enum UgcMapOperateType {
  UGC_MAP_OPERATE_TYPE_REMOVE = 1;          // 地图移除(IDIP)
  UGC_MAP_OPERATE_TYPE_PLAYER_OP = 2;       // 玩家操作
}

message PiiUgcOperateMapParams {
  optional uint64 uid = 1;                  // 玩家uid
  optional uint64 ugcId = 2;                // 地图id
  optional string reason = 3;               // 原因
  optional string source = 4;               // 来源
  optional string reportStr = 5;            // 举报字符串
  optional int32 mapType = 6;               // 地图类型 UgcInstanceType
  optional uint32 IdipSource = 7;           // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 8;               // 流水号
  optional uint32 operateType = 9;          // 操作类型, 详情见枚举UgcMapOperateType所
  optional uint64 operatorUid = 10;         // 操作者uid
  optional int32 afterOpCount = 11;         // 操作后的数量
  optional int32 ugcOpType = 12;            // 地图操作类型，operateType=UGC_MAP_OPERATE_TYPE_PLAYER_OP 时使用，详情见枚举UgcOpType
}

// 在战斗结束前 额外的结算信息(与PiiDsSettlementParams不同, 此时战斗已经结束, 有战斗最终的排名等数据)
message PiiBattleEndExSettlementParams {
  optional int64 battleId = 1;
  optional MMRSettlementInfo mmrSettlement = 2; // mmr结算信息
  optional int64 matchID = 3;   // 匹配生成的uuid 用于tlog追踪
  optional int32 matchTypeId = 4;  // 玩法表id
  repeated int32 playerEnteredLevelIds = 5; // 关卡id 就是tlog的地图id
}

// 修改好友亲密度
message PiiModifyFriendRelationParams {
  optional string FriendOpenId = 1;         // 好友Openid
  optional uint32 FriendPlatId = 2;         // 好友平台（1-安卓，0-ios）
  optional int64 FriendUid = 3;             // 好友Uid
  optional int32 Relation = 4;              // 亲密度修改值（正增负减）
  optional uint32 Source = 5;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;               // 流水号
  optional bool IsUnilateral = 7;           // 是否单边补发
}

// 修改vip经验
message PiiModifyPlayerVipExpParams {
  optional int32 VipExp = 1;                // vip经验修改值（正增负减）
  optional uint32 Source = 2;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 3;               // 流水号
}

// 修改月卡信息
message PiiModifyPlayerMonthCardParams {
  optional string Id = 1;                   // 月卡配置id
  optional int32 ExpireTime = 2;            // 月卡剩余天数修改值（正增负减）
  optional uint32 Source = 3;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 4;               // 流水号
}

// 设置用户标签
message PiiSetUserLabelParams {
  optional int32 UserLabel = 1;             // 用户标签
  optional uint32 Source = 2;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 3;               // 流水号
}

// 新手引导任务处理
message PiiProcessGuideTaskParams {
  optional uint32 GuideTaskId = 1;          // 新手引导任务id
  optional uint32 Source = 2;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 3;               // 流水号
}

message PiiRelationAgreeParams {
  optional int64 req_uid = 1;
  optional com.tencent.wea.xlsRes.RelationTypeEnum relationType = 2;
  optional bool agree = 3;
  optional int64 agreeTime = 4;
}

message PiiRelationApplyParams {
  optional int64 req_uid = 1;
  optional com.tencent.wea.xlsRes.RelationTypeEnum relationType = 2;
  optional com.tencent.wea.xlsRes.RelationApplyType opType = 3;
  optional RelationMsgExtraInfo extraInfo = 4;
  optional int64 applyTime = 5;
}

message PiiIntimacyChangeParams {
  optional int64 req_uid = 1;
  optional int64 changeIntimacy = 2;
  optional FriendIntimacyChangeReason reason = 3;
  optional int64 time = 4;                            // 亲密度变更的时间
}

// 修改玩家地区
message PiiModifyPlayerRegionParams {
  optional int32 regionId = 1;                         // 地区id, D_地区配置表.xlsx
}

message PiiUgcMapPlayParams {
  optional int64 ugcId = 1;
  optional int64 playerCount = 2;
}

message PiiMallGiftCardResetWordsParams {
  optional bool isReceiver = 1; // 标记属主是发送方(0)还是接收方(1)
  optional int64 otherUid = 2;  // 对方uid
  optional int64 giftId = 3;    // 赠礼卡id
}

message PiiBirthdayCardResetWordsParams {
  optional bool isReceiver = 1; // 标记属主是发送方(0)还是接收方(1)
  optional int64 otherUid = 2;  // 对方uid
  optional string cardId = 3;    // 贺卡id
}

message PiiDisplayBoardResetWordsParams {
  optional int32 itemType = 1;  // 道具类型
  optional bool isResetUgcMap = 2; // 是否清空UGC地图
}

message PiiWolfKillTreasureItemInfo {
  optional int32 itemId = 1;
  optional int64 itemNum =2;
  optional int64 sharePlayerUid = 3;
  optional int64 expiredTime = 4;
}

message PiiWolfKillTreasureParams {
  repeated PiiWolfKillTreasureItemInfo items = 1;
  optional int64 expiredTime = 2;
  optional int64 playerUid = 3;
}

// 关系移除操作类型
enum RelationRemoveOperateType {
  RELATION_REMOVE_OPERATE_TYPE_USER = 1;        // 用户操作
  RELATION_REMOVE_OPERATE_TYPE_IDIP = 2;        // IDIP操作
}

// 关系移除
message PiiRelationRemoveParams {
  optional int64 req_uid = 1;                                         // 请求uid
  optional com.tencent.wea.xlsRes.RelationTypeEnum relationType = 2;  // 关系类型
  optional uint32 source = 3;                                         // 操作来源, 详情见枚举RelationRemoveOperateType所示
  optional int64 removeTime = 4;
}

message PiiSnsInvitationParams {
  optional int64 invitation_id = 1;
  optional int32 cfg_id = 2;
  optional int64 invitee_id = 3;
  optional int64 accept_ms = 4;
}

// 打开超级红包奖励
message PiiOpenLuckyMoneyRewardParams {
  optional int64  luckyMoneyId = 1;  // 红包ID
  repeated RewardItemInfo itemList = 2; // 红包奖励道具
  optional int64 sharePlayerUid = 3; // 红包分享者UID
  optional int32 activityId = 4;    // 活动ID
}

message PiiOpenRedPacketParams {
  optional int64 packetUuid = 1;  // 红包UUID
  repeated RewardItemInfo itemList = 2; // DEPRECATED: 红包奖励道具
  optional int64 receiverUid = 3; // 红包接收者UID
  optional int32 activityId = 4; // DEPRECATED: 活动ID
  optional int64 recvTimeMs = 5; // 红包接收时间
  optional int32 placeType = 6;  // 红包位置类型
  optional int64 placeId = 7;    // 红包位置ID
}

message PiiPatchRedPacketParams {
  optional int64 packetUuid = 1;  // 红包UUID
  optional int64 receiverUid = 2; // 红包接收者UID
  optional string replyMsg = 3;   // 答谢消息
}

message PiiAfterRecvRedPacketParams {
  optional int64 packetUuid = 1;  // 红包UUID
  repeated RewardItemInfo itemList = 2; // 红包奖励道具
  optional int64 receiverUid = 3; // 红包接收者UID
  optional int32 packetId = 4;  // 红包id
}

message PiiSetDsReplayRecordInfoParams {
    optional int32 recordCount = 1; //记录局数
}

message PiiSquadRefreshParams {
  optional int32 squadType = 1; // ActivityType:ATFriendSquad = 6;ATSquad = 15;
  optional int32 activityId = 2; // 活动id
  optional int64 squadId = 3; // 小队id
}

message PiiSquadTaskCompleteParams {
  optional int32 activityId = 1;      // 活动id
  optional int32 activityType = 2;    // 参考: enum ActivityType
  optional int64 squadId = 3;         // 小队id
  optional int64 squadMemberUid = 4;  // 队员uid
  optional int32 taskId = 5;          // ActivityType:ATFriendSquad = 6;ATSquad = 15;
  optional int64 epochMillis = 6;     // 完成任务的时间戳毫秒
}

message PiiDelLayoutParams {
  optional int64 layoutId = 1; //方案id
  optional uint32 Source = 2;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 3;               // 流水号
}

message PiiRecruiteConfirmParams {
  optional int32 actId = 1;         // 活动id
  optional int32 type = 2;          // 招募类型
  optional string callOpenid = 3;   // 招募者openId
  optional int32 callPlatId = 4;    // 招募者平台
  optional int64 callUid = 5;       // 招募者角色uid
  optional int64 callTime = 6;      // 触发时间, sec
}

message PiiRecruiteSignParams {
  optional int32 actId = 1;         // 活动id
  optional int32 type = 2;          // 招募类型
  optional string echoOpenid = 3;   // 响应者openId
  optional int32 echoPlatId = 4;    // 招募者平台
  optional int64 echoUid = 5;       // 招募者角色uid
  optional int64 signingTime = 6;   // 缔约时间
  optional string charName = 7;     // 角色名称
  optional string platName = 8;     // 平台昵称
  optional string picUrl = 9;       // 头像url
}

message PiiRecruiteLoginParams {
  optional int32 actId = 1;         // 活动id
  optional int32 type = 2;          // 招募类型
  optional int64 echoUid = 3;       // 招募者角色uid
  optional int64 loginTime = 4;     // 登陆时间
}

message PiiBattleLikeParams {
  optional int64 battleId = 1;
  optional int64 srcUid = 2;
  optional int32 likeType = 3;
  optional int32 contentId = 4;
  optional int32 matchType = 5;
}

enum PiiClubChangeType {
  PCCT_ClubDissolve = 1;
  PCCT_ClubApprove = 2;
  PCCT_ClubDenied = 3;
  PCCT_ClubKick = 4;
  PCCT_ClubModify = 5;
  PCCT_ClubQuit = 6;
  PCCT_ClubExempt = 7;          // 太久不活跃被免除管理员
  PCCT_ClubExemptTransfer = 8;  // 因原管理员被免除而被任命管理员
  PCCT_ClubBecomeManager = 9;   // 被任命管理员
  PCCT_ClubRemoveManager = 10;  // 取消任命管理员
  PCCT_ClubBecomeOwner = 11;    // 被任命团长
  PCCT_ClubSyncQuit = 12;       // 社团同步
}

message PiiClubChangeParams {
  optional int32 changeType = 1;          // 类型
  optional ClubBasicInfo basicInfo = 2;   // 社团信息
}

message PiiClubSubscriptionParams {
  optional int64 clubId = 1;              // 社团ID
  optional ClubEventEntry eventEntry = 2; // 社团事件详情
}

message PiiAddWarmRoundParams {
  optional int32 matchTypeId = 1;   //
  optional uint32 source = 2;               // 渠道号，由前端调用方自动生成，无需填写
  optional string serial = 3;               // 流水号
}

message PiiMatchIsolateParams {
  optional uint32 time = 1;                                   //（秒）
  optional uint32 ailevel = 2;                                //（ai局的ai难度等级）
  optional string reason = 3;                                 //（提示语string）
  optional uint32 type = 4;                                   //（1为黑作弊池，2为手游助手模拟器池，3为第三方模拟器池）
}

message PiiDeductLuckyStarParams {
  optional int64 uniqueId = 1;                        // 唯一id
  optional int32 activityId = 2;                      // 活动id
}

message PiiAddLuckyStarParams {
  optional proto_LuckyStarInfo starInfo = 1;     // 福星卡信息
  optional int32 activityId = 2;                   // 活动id
}

message PiiRedEnvelopeRainAddExtraOppParams {
  optional int64 clickerUid = 1;            // 点击者id
  optional int32 actId = 2;                 // 活动id
  optional int64 clickDayZeroSec = 3;       // 点击时当天零点时间戳
}

message PiiRedPacketDestroyParams {
  optional int64 packetUuid = 1;  // 红包UUID
}

// 端外消费通知消息
message PiiOffEndConsumeNotifyParams {
  optional string OrderId = 1;                                // 订单id
  optional string GoodsId = 2;                                // h5侧直购id
  optional uint32 BuyNum = 3;                                 // 购买数量
  optional uint32 Source = 4;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 5;                                 // 流水号
  optional int64 consumeTime = 6;
  optional uint32 idipApiType = 7;                     // 0为老的充值接口，1为新的充值星钻接口
}

message PiiModifyRechargeLevelParams{
  optional uint32 OperateType = 1;                            // 0:更新氪条并删除已领奖状态到当前氪条进度  1:删除氪条已领奖状态 2:增加氪条已领奖状态
  repeated uint32 LevelIdList = 2;
}

message PiiModifyXiaoWoLiuYanMessageInfoParams {
  optional XiaoWoLiuYanMessageNtfType type = 1; // 留言信息类型
  optional proto_XiaoWoPlayerLiuYanMessageInfo liuYanMessageInfo = 2; // 玩家家园留言信息
  optional proto_XiaoWoLiuYanMessageInfo xiaowoLiuYanMessageInfo = 3; // 家园留言信息
  optional uint32 Source = 4;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 5;               // 流水号
}

message PiiModifyFarmLiuYanMessageInfoParams {
  optional FarmLiuYanMessageNtfType type = 1; // 留言信息类型
  optional proto_FarmPlayerLiuYanMessageInfo liuYanMessageInfo = 2; // 玩家家园留言信息
  optional proto_FarmLiuYanMessageInfo farmLiuYanMessageInfo = 3; // 家园留言信息
  optional uint32 Source = 4;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 5;               // 流水号
}

message PiiAMSItemSendParams {
  optional AMSItemParam req = 1;                              // 请求
  optional AMSItemResult resp = 2;                            // 响应
}

message PiiFarmMonthCardParams {
  optional int32 mode = 1;
  optional int64 time = 2;
  optional uint32 Source = 3;
  optional string Serial = 4;
  optional int64 startTime = 5;
}

message PiiFpsReturnItemParams {
  optional int64 battleId = 1;
  optional int32 matchType = 2;
  repeated LevelDropItemInfo itemInfo = 3; // 道具信息
  optional int64 teammatePlayerUid = 4; // 撤离的队友uid
  optional string teammatePlayerName = 5; // 撤离的队友名字
  optional int64 evacuateTimeMs = 6;  // 队友撤离时间
  repeated int32 equipItemId = 7; // 装备组信息
}

// 发给登录时OMD的信息
message PiiOmdLoginParams {
  optional int32 msgType = 1;
  optional int32 para1 = 2;
  optional int32 para2 = 3;
  optional string para3 = 4;
  optional string para4 = 5;
  optional int64 para5 = 6;
}


// OMD进入lobby大厅的消息
message PiiOmdEnterLobbyParams {
  optional int32 msgType = 1;
  optional int32 para1 = 2;
  optional int32 para2 = 3;
  optional string para3 = 4;
  optional string para4 = 5;
  optional int64 para5 = 6;
}

// 发送道具进背包消息
message PiiSendItemIntoBagParams {
  repeated ItemInfo ItemList = 1;           // 道具列表
  optional uint32 Source = 2;               // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 3;               // 流水号
  optional uint32 withNtf = 4;              // 是否需要通知客户端   0:不需要 1:需要
}

message PiiGiveInterServerGiftParams {
  optional int32 activityId = 1;
}

// 助力功夫熊猫竞速
message PiiHelpKungFuPandaRacingParams {
  optional int32 helpTimeMs = 1;  // 助力时间
  optional int64 updateTimeMs = 2; // 更新时间
  optional int64 helperUid = 3; // 助力者UID
  optional int32 activityId = 4;    // 活动ID
}

// 设置用户标签-通用
message PiiSetCommonUserLabelParams {
  optional int32 id = 1;                    // 用户标签ID
  repeated int64 numVals = 2;               // 标签值,数字类型
  repeated string strVals = 3;              // 标签值,字符类型
  optional int64 ExpiredTime = 101;         // 默认为0，非0表示有效截止时间戳（秒）
  optional uint32 Source = 102;             // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 103;             // 流水号
}

// 同步电视台奖励信息
message PiiSyncGameTvRewardStatusParams {
  optional int32 hasReward = 1;          // 是否有奖励
  optional int64 rewardExpireTime = 2;  // 奖励过期时间
}

enum PiiChatControlType {
  CTP_NA = 0;                   // 默认类型
  CTP_DELETE = 1001;            // 删除
  CTP_CREATE = 1002;            // 创建
}

// 聊天控制
message PiiChatControlParams {
  optional ChatGroupKey chatKey = 1;          // 频道类型
  optional int32 ctrlType = 2;      
  repeated int64 params = 3;
}

// 狼人杀消费道具信息
message PiiWereWolfConsumeItemParams {
  optional int64 battleID = 1;
  repeated WereWolfSideIdentityItemInfo consumeItemInfo = 2; //消费的道具信息
  optional int32 matchTypeId = 3;
  optional WereWolfSideIdentityInfo wereWolfSideIdentityInfo = 4; // 狼人杀阵营身份信息
}

// 修改玩家信誉分
message PiiModifyReputationScoreParams {
  optional int32 ModeId = 1;                                  // 玩法id
  optional int32 ScoreId = 2;                                 // 分数组id
  optional int32 ModifyScore = 3;                             // 修改分数
  optional int64 BattleId = 4;                                // 对局id
  optional int32 BehaviorId = 5;                              // 行为id
  optional uint32 Source = 6;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                                 // 流水号
}

// 上报玩家信誉分行为数据
message PiiReportReputationScoreBehaviorParams {
  optional PlayerReputationScoreBehaviorReportInfo info = 1;  // 玩家信誉分行为数据
  optional int64 reportTime = 2;                              // 上报时间
  repeated int64 teamMemberUidList = 3;                       // 队伍成员uid列表
}

message PiiArenaCardParams {
  optional ArenaCardEventData eventData = 1;  // Arena卡牌事件
}

// 动物图鉴活动图鉴赠送相关
message PiiDeductAnimalHandbookItemParams {
  optional int32 activityId = 1;            // 活动id
  optional int64 uniqueId = 2;              // 图鉴唯一id
  optional PlayerColdData receiverInfo = 3; // 领取人信息
  optional int64 getTimeMs = 4;             // 图鉴被领取时间
}

message PiiAddAnimalHandbookItemParams {
  optional int32 activityId = 1;                          // 活动id
  optional proto_AnimalHandbookAnimalInfo animalInfo = 2; // 动物图鉴信息
  optional PlayerColdData giverInfo = 3;               // 赠送人信息
}

message PiiClubSimpleNtfParams {
  enum NtfType {
    CET_Invalid = 0;
    CET_WeekSettleBubble = 1;
  }
  optional int64 clubId = 1; // 社团ID
  optional int32 type = 2; // 通知类型
  optional int64 longValue = 3;
  optional string stringValue = 4;
  repeated int64 longList = 5;
  repeated string stringList = 6;
}

message PiiTrainCampAddScore {
  optional int64 assistUid = 1;
  optional int32 itemId = 2; // 废弃
  optional int32 itemNum = 3; // 废弃
  repeated ItemInfo scoreItem = 4; // 积分道具
}

message PiiClientLogColoringParams {
  optional int64 uid = 1;
  optional int32 type = 2;          // 0:取消账号日志染色, 1:账号日志着色, 2:客户端立即上次日志
  optional int64 stainId = 3;
  optional int64 beginTimeMs = 4;   // 生效时间(unix时间戳, 毫秒)
  optional int64 endTimeMs = 5;     // 失效时间(unix时间戳, 毫秒)
  optional int32 logLevel = 6;
}

// 发送玩家广播notice
message PiiSendBroadcastNoticeParams {
  optional int32 templateId = 1;	// 模板id
  repeated string params = 2;    // 格式化参数
  optional int32 type = 3;      // 广播类型
  optional int32 canReplace = 4;  // 能否顶替当前广播
  optional int32 canQueue = 5;  // 是否进入当前广播队列
}

// 幸运好友邀请好友做任务
message PiiLuckyFriendApplyTaskParams {
  optional int64 friendUid = 1;     // 邀请方UID
  optional int32 taskId = 2;        // 任务ID
  optional int64 expireTimeMs = 3;  // 失效时间
  optional int64 taskUniqueId = 4;  // 任务唯一ID
  optional bool apply = 5;          // 是否申请
}

// 幸运好友处理好友申请
message PiiLuckyFriendOperateApplyParam {
  optional int64 friendUid = 1;
  optional int32 taskId = 2;
  optional int64 taskUniqueId = 3;
  optional bool agree = 4;
  optional int64 operateTimeMs = 5;
}

message PiiModifyChargeProgressParams {
  optional int64 ChangeNum = 1;                                 // 氪条修改金额：分
  optional int64 TaskChangeNum = 2;                                 // 任务修改金额：分
  optional string TaskTime = 3;                                 // 任务统计时间,默认是当前时间
}

//PII_MODIFY_SEASON_FASHION_EQUIP_BOOK
message PiiModifySeasonFashionEquipBookParams {
  optional int32 seasonId = 1;
  repeated int32 itemIds = 2;
}

// idip补发米大师商品
message PiiIdipDeliverMidasProductParams {
  optional string productId = 1;                                // 商品ID
  optional uint32 productNum = 2;                                 // 数量
  optional int32 activityId = 3;
}

// moba打赏
message PiiArenaTipParams {
  optional int64 fromUid = 1;
  optional int32 item_id = 2; // 打赏道具ID
  optional int32 count = 3;
  optional int32 factionType = 4; // 身份类型 ref ArenaTipFactionType
  optional int64 battleId = 5; // 对局ID
  optional int32 matchType = 6; // 玩法ID
}

// 言语违规信誉分处罚信息
message PiiVerbalViolationReputationScorePunishParams {
  optional int32 ModeId = 1;								                  // 玩法id
  optional int64 BattleId = 2;								                // 对局id
  optional int64 Time = 3;									                  // 发生时间
  optional int32 Label = 4;									                  // 违规标签
  optional int32 Type = 5;									                  // 违规类型, 1: 文字违规; 2: 语音违规
  optional uint32 Source = 6;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 7;                                 // 流水号
}

// 言语违规信誉分误处罚回撤并补分信息
message PiiVerbalViolationReputationScoreUnPunishParams {
  optional int32 ModeId = 1;								                  // 玩法id
  optional int64 BattleId = 2;								                // 对局id
  optional uint32 Score = 3;                                  // 补分数值
  optional int64 Time = 4;                                    // 发生时间
  optional uint32 Source = 5;                                 // 渠道号，由前端调用方自动生成，无需填写
  optional string Serial = 6;                                 // 流水号
}

message PiiPicLikeParams {
  required string picKey = 1;//图片key
  required int64 sourceUid = 2;//照片来源玩家uid
  required int64 uid = 3;//点赞操作玩家uid
  required int64 time = 4;//点赞时间
}

// 生日祝福赠送数据
message PiiBirthdayBlessingParams {
  optional int64 senderUid = 1;                     // 赠送者 uid
  optional int64 sendEpochMillis = 2;               // 赠送时间戳毫秒
  optional int32 birthdayMonthDay = 3;              // 被赠送者生日(生日月*100+生日日)
  optional int32 blessingConfigId = 4;              // 祝福语配置表id
}

// 生日贺卡数据
message PiiBirthdayCardParams {
  optional int64 senderUid = 1;                     // 赠送者 uid
  optional int64 sendEpochMillis = 2;               // 赠送时间戳毫秒
  optional int32 birthdayMonthDay = 3;              // 被赠送者生日(生日月*100+生日日)
  optional BirthdayCardData birthdayCardData = 4;   // 贺卡数据
}

// 生日赠礼数据
message PiiBirthdayGiftParams {
  optional int64 senderUid = 1;                     // 赠送者 uid
  optional int64 sendEpochMillis = 2;               // 赠送时间戳毫秒
  optional int32 birthdayMonthDay = 3;              // 被赠送者生日(生日月*100+生日日)
  optional MallGiftCardInfo card = 4;               // 赠礼卡信息
  optional IntKVArray items = 5;                    // 商品
}

// 超核活动玩家排名分数修改
message PiiSuperCoreRankActivityScoreModifyParams {
  optional string Serial = 1;                       // 流水号
  optional int32 ActivityId = 2;                    // 活动id
  optional int32 ModifyType = 3;                    // 修改的类型
  optional int32 InitTotalRmbYuan = 4;              // 初始时充值yuan
  repeated ItemInfo CostItems = 5;                  // 消费的道具数量
}

message PiiLuckStarNormalAssistParams{
  optional int32 activityId = 1;                    // 活动id
  optional string uniqueId = 2;                      // 助力id
  optional int64 assistUid = 3;                     // 助力者id
  optional int64 time = 4;                          // 助力时间
  optional int32 assistType = 5;                    // 助力类型
}

message PiiLuckStarSpecifyAssistParams{
  optional int32 activityId = 1;                    // 活动id
  optional string uniqueId = 2;                      // 助力id
  optional int64 assistUid = 3;                     // 助力者id
  optional int64 time = 4;                          // 助力时间
  optional int32 assistType = 5;                    // 助力类型
}

message PiiLuckStarGiveSlipParams{
  optional int32 activityId = 1;                    // 活动id
  optional string uniqueId = 2;                      // 赠送、索要id
  optional int64 giveUid = 3;                       // 赠送人
  optional int64 receiveUid = 4;                    // 接收人人
  optional int64 bizTime = 5;                       // 业务时间
  optional string bizType = 6;                      // 交易类型
  optional int32 slipId = 7;
}

message PiiLuckStarReceiveSlipParams{
  optional int32 activityId = 1;                    // 活动id
  optional string uniqueId = 2;                      // 赠送、索要id
  optional int64 giveUid = 3;                       // 赠送人
  optional int64 receiveUid = 4;                    // 接收人人
  optional int64 bizTime = 5;                       // 业务时间
  optional string bizType = 6;                      // 交易类型
  optional int32 slipId = 7;
}

message PiiPicAtTargetParams {
  required string picKey = 1;//图片key
  required int64 sourceUid = 2;//照片来源玩家uid
  required int64 uid = 3;//at目标玩家uid
  required int64 time = 4;//at时间
}

message PiiPicDelTargetPicAtDelParams {
  required string picKey = 1;//移除图片key
  required int64 sourceUid = 2;//照片来源玩家uid
}

message PiiTaskPasswordCodeUseParams{
  optional int32 activityId = 1;                    // 活动id
  optional int32 taskId = 2;                      // 口令码任务id
  optional int64 useUid = 3;                       // 口令码使用人
  optional int64 giveUid = 4;                    // 口令码提供人
  optional int64 bizTime = 5;                       // 业务时间
  optional string bizType = 6;                      // 业务类型
  optional string passwordCode = 7;                // 口令码
}


// 拒绝/未响应 确认进入啾灵副本
message StarPEnterCancelParams {
  optional int64 cancelTimeMs = 1;  // 取消时间
  optional int32 cancelTimes  = 2;  // 取消次数
  optional int32 cancelReason = 3;  // 取消原因
  optional int32 matchType    = 4;  // 玩法模式
}

// SP啾灵退出副本通知
message StarPQuitBattleParams {
  optional int32 matchType    = 1;  // 玩法模式
  optional int64 battleId     = 2;  // battleId
  optional int32 quitReason   = 3;  // 退出原因
  optional int32 endCode      = 4;  // 退出码
  optional int64 quitTime     = 5;  // 时间戳(ms)

  optional string desc        = 10;  // 描述
}

message PiiFarmDragonTeamPointUpdateParams{
  optional int64 teamPoints = 1;//小队积分
  optional int64 updateMemberUid = 2;//触发更新的小队成员
  optional int64 teamId = 3;
}

message PiiResetItaBagParams {
  optional int32 itemId = 1; // 痛包itemId
}

message PlayerInteractionData {
  required PlayerInteractionInstruction instruction = 1;
  optional int64 id = 2;
  optional PiiRemindNewMailParams remindNewMailParams = 3;
  optional PiiDsSettlementParams dsSettlementParams = 4;
  optional PiiSendNoticeParams sendNoticeParams = 5;
  optional PiiFrameSettlementParams frameSettlementParams = 6;
  optional PiiSendBattleEndNtfParams frameBattleEndNtf = 7;
  optional PiiInviteeRegisterParams inviteeRegisterParams = 8;
  optional PiiSendChatParams chatInteractionParams = 10;
  
  optional PiiSendClubParams clubInteractParams = 11;
  optional PiiUpdateLobbyInfoParams updateLobbyInfoParams = 12;
  optional PiiUpdateSceneInfoParams updateSceneInfoParams = 13;
  optional PiiSendChargeFlowParams sendChargeFlowParams = 14;
  optional PiiRemindSquadUpdate remindSquadUpdateParams = 15;
  optional PiiChangeSubRanksParams changeSubRanksParams = 16;
  optional PiiDsLevelFinishParams dsLevelFinishParams = 17;
  optional PiiModifyItemParams modifyItemParams = 18;
  optional PiiModifyTaskProgressParams modifyTaskProgressParams = 19;
  optional PiiModifyBpDataParams modifyBpDataParams = 20;

  optional PiiSendQAInvestRewardParams qaInvestRewardParams = 21;
  optional PiiModifyPlayerInfoParams modifyPlayerInfoParams = 22;
  optional PiiUgcOperateMapParams ugcOperateMapParams = 23;
  optional PiiFollowPlayerParams  followPlayerParams = 24;
  optional PiiBattleEndExSettlementParams battleEndExSettlementParams = 25;
  optional PiiModifyFriendRelationParams modifyFriendRelationParams = 26;
  optional PiiModifyPlayerVipExpParams modifyPlayerVipExpParams = 27;
  optional PiiModifyPlayerMonthCardParams modifyPlayerMonthCardParams = 28;
  optional PiiSetUserLabelParams setUserLabelParams = 29;
  optional PiiProcessGuideTaskParams processGuideTaskParams = 30;

  optional PiiRelationApplyParams relationApplyParams = 31;
  optional PiiIntimacyChangeParams intimacyChangeParams = 32;
  optional PiiModifyPlayerRegionParams modifyPlayerRegionMsgParams = 33;
  optional PiiRelationAgreeParams relationAgreeParams = 34;
  optional PiiRelationRemoveParams relationRemoveParams = 35;
  optional PiiUgcMapPlayParams ugcMapPlayParams = 36;
  optional PiiSnsInvitationParams snsInvitationParams = 37;
  optional PiiOpenLuckyMoneyRewardParams openLuckyMoneyRewardParams = 38;
  optional PiiSetDsReplayRecordInfoParams setDsReplayRecordInfoParams = 39;
  optional PiiSquadRefreshParams squadRefreshParams = 40;

  optional proto_DsUserDBInfo dsUserDBInfo = 41;
  optional PiiDelLayoutParams delLayoutParams = 42;
  optional PiiRecruiteConfirmParams recruiteConfirmParams = 43;
  optional PiiRecruiteSignParams recruiteSignParams = 44;
  optional PiiClubChangeParams clubChangeParams = 45;
  optional PiiAddWarmRoundParams addWarmRoundParams = 46;
  optional PiiBattleLikeParams battleLikeParams = 47;
  optional PiiMatchIsolateParams matchIsolateParams = 48;
  optional PiiOpenRedPacketParams openRedPacketParams = 49;
  optional PiiDeductLuckyStarParams deductLuckyStarParams = 50;

  optional PiiAddLuckyStarParams addLuckyStarParams = 51;
  optional PiiRedEnvelopeRainAddExtraOppParams redEnvelopeRainAddExtraOppParams = 52;
  optional PiiAfterRecvRedPacketParams afterRecvRedPacketParams = 53;
  optional PiiClubSubscriptionParams clubSubscriptionParams = 54;
  optional PiiRedPacketDestroyParams redPacketDestroyParams = 55;
  optional PiiPatchRedPacketParams patchRedPacketParams = 56;
  optional PiiOffEndConsumeNotifyParams offEndConsumeNotifyParams = 57;
  optional PiiModifyXiaoWoLiuYanMessageInfoParams xiaoWoLiuYanMessageInfoParams = 58;
  optional PiiAMSItemSendParams amsItemSendParam = 59;
  optional PiiFpsReturnItemParams fpsReturnItemParams = 60;

  optional PiiSendItemIntoBagParams sendItemIntoBagParams = 61;
  optional PiiGiveInterServerGiftParams giveInterServerGiftParams = 62;
  optional PiiHelpKungFuPandaRacingParams helpKungFuPandaRacingParams = 63;
  optional PiiSetCommonUserLabelParams setCommonUserLabelParams = 64;
  optional PiiSyncGameTvRewardStatusParams syncGameTvRewardStatusParams = 65;
  optional PiiModifyFarmLiuYanMessageInfoParams farmLiuYanMessageInfoParams = 66;

  optional PiiChatControlParams chatControlParams = 67;
  optional PiiModifyRechargeLevelParams piiModifyRechargeLevelParams = 68;
  optional PiiWereWolfConsumeItemParams piiWereWolfConsumeItemParams = 69;
  optional PiiModifyReputationScoreParams piiModifyReputationScoreParams = 70;
  optional PiiReportReputationScoreBehaviorParams piiReportReputationScoreBehaviroParams = 71;
  optional PiiFarmMonthCardParams farmMonthCardParams = 72;
  optional PiiRecruiteLoginParams recruiteLoginParams = 73;
  optional PiiClubSimpleNtfParams clubSimpleNtfParams = 74;
  optional WishActivityHelpBindParams wishActivityHelpBindParams = 75;
  optional PiiActivitySvrRewardParams activitySvrRewardParams = 76;
  optional PiiDeductAnimalHandbookItemParams deductAnimalHandbookItemParams = 77;
  optional PiiAddAnimalHandbookItemParams addAnimalHandbookItemParams = 78;
  optional PiiTrainCampAddScore trainCampAddScore = 79;
  optional PiiSendBroadcastNoticeParams sendBroadcastNoticeParams = 80;
  optional PiiClientLogColoringParams clientLogColoringParams = 81;
  optional PiiArenaCardParams arenaCardParams = 82;
  optional PiiLuckyFriendApplyTaskParams luckyFriendApplyTaskParams = 83;
  optional PiiOmdLoginParams omdLoginParams = 84;
  optional PiiOmdEnterLobbyParams omdEnterLobbyParams = 85;
  optional PiiLuckyFriendOperateApplyParam luckyFriendOperateApplyParams = 87;
  optional PiiModifyChargeProgressParams modifyChargeProgressParams = 88;
  optional PiiModifySeasonFashionEquipBookParams modifySeasonFashionEquipBookParams = 89;
  optional PiiIdipDeliverMidasProductParams idipDeliverMidasProductParams = 90;
  optional PiiArenaTipParams arenaTipParams = 91;
  optional PiiVerbalViolationReputationScorePunishParams verbalViolationReputationScorePunishParams = 92;
  optional PiiVerbalViolationReputationScoreUnPunishParams verbalViolationReputationScoreUnPunishParams = 93;
  optional PiiPicLikeParams picLikeParams = 94;
  optional PiiSuperCoreRankActivityScoreModifyParams superCoreRankActivityScoreModifyParams = 95;
  optional PiiMallGiftCardResetWordsParams mallGiftCardResetWordsParams = 96;
  optional PiiBirthdayBlessingParams birthdayBlessing = 97;
  optional proto_AttrOnDueDateCardData birthdayCard = 98;
  optional PiiBirthdayGiftParams birthdayGift = 99;
  optional proto_AttrOnDueDateCardData birthdayCardOnDueDate = 100;
  optional PiiLuckStarNormalAssistParams luckStarNormalAssistParams = 101;
  optional PiiLuckStarSpecifyAssistParams luckStarSpecifyAssistParams = 102;
  optional PiiLuckStarGiveSlipParams luckStarGiveSlipParams = 103;
  optional PiiLuckStarReceiveSlipParams luckStarReceiveSlipParams = 104;
  optional PiiBirthdayCardResetWordsParams birthdayCardResetWords = 105;
  optional PiiWolfKillTreasureParams wolfKillTreasureParams = 106;
  optional PiiPicAtTargetParams picAtTargetParams = 107;
  optional PiiPicDelTargetPicAtDelParams picDelTargetPicAtDelParams = 108;
  optional PiiTaskPasswordCodeUseParams taskPasswordCodeUseParams = 109;
  optional PiiFarmDragonTeamPointUpdateParams farmDragonTeamPointUpdateParams = 110;
  optional PiiDisplayBoardResetWordsParams displayBoardResetWords = 111;
  optional PiiResetItaBagParams resetItaBagParams = 112;

  // ===========啾灵SP 200-500 =================
  optional StarPEnterCancelParams starPEnterCancelParams = 200;
  optional StarPQuitBattleParams starPQuitBattleParams    = 201;
  // ===========啾灵SP =========================
}
