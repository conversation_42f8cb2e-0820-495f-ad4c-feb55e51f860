syntax = "proto2";

package com.tencent.wea.protocol;

option java_package = "com.tencent.wea.tcaplus";

import "base_common.proto";
import "common.proto";
import "ResKeywords.proto";
import "g6_common.proto";
import "db_kv_store.proto";
import "player_interaction.proto";
import "server_interaction.proto";
import "universal_store.proto";
import "tcaplus_option.proto";
import "competition_common.proto";
import "letsgo_common.proto";

option (tcaplus_table_id_min) = 0;
option (tcaplus_table_id_max) = 500;

import "attr_TradingCardInfo.proto";
import "attr_WildCardInfo.proto";
import "attr_PlayerPublicProfileInfo.proto";
import "attr_PlayerPublicSummaryInfo.proto";
import "attr_PlayerPublicEquipments.proto";
import "attr_PlayerPublicGameData.proto";
import "attr_PlayerPublicGameSettings.proto";
import "attr_PlayerPublicHistoryData.proto";
import "attr_PlayerPublicLiveStatus.proto";
import "attr_PlayerPublicSceneData.proto";
import "attr_IdipTaskInfo.proto";
import "attr_UserAttr.proto";
import "attr_XiaowoAttr.proto";
import "attr_FarmAttr.proto";
import "attr_HouseAttr.proto";
import "attr_VillageAttr.proto";
import "attr_CookAttr.proto";
import "attr_PlayerPublicBasicInfo.proto";
import "attr_MatchStaticsDb.proto";
import "attr_PlayerRankGeoInfo.proto";
import "attr_PartyInfo.proto";
import "attr_XiaowoPublicInfo.proto";
import "attr_FarmPublicInfo.proto";
import "attr_HousePublicInfo.proto";
import "attr_CookPublicInfo.proto";
import "attr_PlayerSnsInvitationData.proto";
import "attr_XiaoWoLayoutInfo.proto";
import "attr_XiaoWoLayoutPublishRecord.proto";
import "attr_DsUserDBInfo.proto";
import "attr_ActivitySquadData.proto";
import "attr_FittingSlots.proto";
import "attr_UgcGrowUpInfo.proto";
import "attr_AlbumInfo.proto";
import "attr_UserActivityAttr.proto";
import "attr_AttrRecentActivity.proto";
import "attr_MallWishListPublic.proto";
import "attr_BuyRecordStruct.proto";
import "attr_SquadMember.proto";
import "attr_SnsAttr.proto";
import "attr_ArenaGameInfo.proto";
import "attr_CocUserAttr.proto";
import "attr_AiNpcAttr.proto";

import "attr_HouseLayoutInfo.proto";
import "attr_StarPDsCommonDBUserData.proto";
import "attr_StarPDsMapPosCommonDBUserData.proto";
import "attr_StarPDsPlayerCommonDBUserData.proto";
import "attr_StarPDsWorldCommonDBUserData.proto";
import "attr_StarPDsGuildDBUserData.proto";
import "ResMailTemplate.proto";
import "ResNotice.proto";
import "ResGameSetting.proto";
import "ResTradingCard.proto";
import "attr_AlbumLikeHisInfo.proto";
import "coc_common.proto";
import "attr_SeasonReview.proto";
import "ainpc_common.proto";
import "attr_AlbumExtInfo.proto";
import "attr_ChaseGameData.proto";
import "attr_VillagePublicInfo.proto";
import "attr_ModPublicInfo.proto";

import "attr_StarPAttr.proto";
import "attr_StarPPlayerAttr.proto";
import "attr_StarPPublicInfo.proto";
import "attr_StarPItemUserDataUnion.proto";
import "attr_StarPDsGuildGVGData.proto";
import "attr_StarPMailExtraData.proto";
import "attr_StarPMailArgsValues.proto";
import "attr_StarPMailAttachments.proto";
import "attr_StarPBaseGroupSimpleInfo.proto";
import "attr_StarPBaseGroupDataInfo.proto";
import "attr_StarPBaseGroupMemberDataInfo.proto";
import "attr_StarPBaseGroupApplicationData.proto";
import "attr_StarPBaseGroupInvitationData.proto";
import "attr_StarPGsCommonDbInfo.proto";
import "attr_StarPGsCommonDbInfoUnion.proto";
import "attr_ChangePlatMidasDiff.proto";
import "attr_Money.proto";
import "attr_ChaseIdentityBattlePerformanceDatas.proto";

// 回档玩家数据需要考虑的表
// Player, PlayerPublic, PlayerMail, PlayerInteractionTable
// TODO 是否回档 PlayerUgc*


message Server {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "serverId,worldId";
  option (tcaplus_index) = "index1:worldId";
  option (tcaplus_splitkey) = "worldId";
  required int32 serverId = 1;
  required int32 worldId = 2;
  optional int32 isRunning = 3; // 是否运行中
  optional int64 runCount = 4; // 服务器起停计数
  optional int64 updateTime = 5; // 最后更新时间
}

message Metadata {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "worldId,type,uuid";
  option (tcaplus_splitkey) = "uuid";
  required int32 worldId = 1;
  required int32 type = 2;
  required int64 uuid = 3;
  optional int32 serverId = 4; // 加载服务名称
  optional int64 lockExpire = 5; // 记录锁定过期时间
  optional int64 runCount = 6; // 加载时的服务器起停计数
}

message DirIpBlackList {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ip";
  option (tcaplus_index) = "index1:ip";
  option (tcaplus_splitkey) = "ip";
  required string ip = 1;
  optional int64 createTime = 2;
  optional int32 duration = 3;
  optional string reason = 4;
}
//openid to uid
message OpenIdToUid {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Openid,PlatId";
  option (tcaplus_index) = "index1:Openid";
  option (tcaplus_splitkey) = "Openid";
  required string Openid = 1;
  required int32 PlatId = 2;  //ios:0, android:1
  optional int64 Uid = 3;
  optional int32 Zoneid = 5;
  optional int64 CreateTime = 6;
  optional int32 Deleted = 7; // 账号状态复用该字段, 详情见枚举AccountState所示
  optional int64 LoginTime = 8;
  optional int64 DeletedTime = 9; //合服时打入该标记位
  optional int32 isRegisterFini = 10;
  optional int32 accountType = 11[(tcaplus_field_version) = 185];
  optional int64 CreatorId = 12[(tcaplus_field_version) = 190];  //大区Id
  optional int32 TransferStatus = 13[(tcaplus_field_version) = 410]; //（0-未转区，1-转区中，2-转区成功，3-转区失败）
  optional int64 TransferInterval = 14[(tcaplus_field_version) = 410]; //上次转区时间
}

message OpenIdToCreatorId {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Openid,PlatId,worldId";
  option (tcaplus_index) = "index1:Openid|index2:Openid,PlatId";
  option (tcaplus_splitkey) = "Openid";
  required string Openid = 1;
  required int32 PlatId = 2;  //ios:0, android:1
  required int32 worldId = 3;
  optional int64 Uid = 5;       // 用户id
  optional int64 CreatorId = 6;  //创作者Id
}


// player uid
message PlayerPublic {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid";
  option (tcaplus_splitkey) = "Uid";
  required int64 Uid = 1;
  optional proto_PlayerPublicProfileInfo PublicProfile = 2;
  optional proto_PlayerPublicSummaryInfo PublicSummary = 3;
  optional proto_IdipTaskInfo idipTaskInfo = 4[(tcaplus_field_version) = 86];
  optional proto_PlayerPublicEquipments PublicEquipments = 5[(tcaplus_field_version) = 92];
  optional proto_PlayerPublicGameData  PublicGameData = 6[(tcaplus_field_version) = 92];
  optional proto_PlayerPublicGameSettings PublicGameSettings = 7[(tcaplus_field_version) = 92];
  optional proto_PlayerPublicHistoryData  PublicHistoryData = 8[(tcaplus_field_version) = 92];
  optional proto_PlayerPublicLiveStatus PublicLiveStatus = 9[(tcaplus_field_version) = 92];
  optional proto_PlayerPublicSceneData PublicSceneData = 10[(tcaplus_field_version) = 92];
  optional proto_PlayerPublicBasicInfo PublicBasicInfo = 11[(tcaplus_field_version) = 156];
  optional proto_MatchStaticsDb MatchStaticsInfo = 12 [deprecated = true,(tcaplus_field_version) = 165];  // 废弃
  optional proto_PartyInfo PartyInfo = 13[(tcaplus_field_version) = 217];
  optional proto_XiaowoPublicInfo XiaowoPublicInfo = 14[(tcaplus_field_version) = 217]; // 这个玩家的家园public信息，由xiaowosvr维护，不在player属性上
  optional proto_FittingSlots FittingSlots = 16[deprecated = true, (tcaplus_field_version) = 319]; // 废弃 该数据已经在PublicEquipments字段中
  optional proto_FarmPublicInfo FarmPublicInfo = 15[(tcaplus_field_version) = 326]; // 这个玩家的农场public信息，由farmsvr维护，不在player属性上
  optional proto_UgcGrowUpInfo UgcGrowUpInfo = 17[(tcaplus_field_version) = 333];
  optional proto_AlbumInfo albumInfo = 18[(tcaplus_field_version) = 336]; // 玩家相册信息
  optional proto_AttrRecentActivity recentActivity = 19[(tcaplus_field_version) = 355]; // 近期活跃数据
  optional proto_HousePublicInfo HousePublicInfo = 20[(tcaplus_field_version) = 372]; // 这个玩家的农场小屋public信息，由farmsvr维护，不在player属性上
  optional proto_MallWishListPublic mallWishList = 21[(tcaplus_field_version) = 393]; // 商城心愿单
  optional CocPlayerPublicInfo CocPlayerPublicInfo = 22[(tcaplus_field_version) = 394]; // 这个玩家的coc玩法public信息，由cocsvr维护，不在player属性上
  optional proto_ChaseGameData ChaseGameData = 23[(tcaplus_field_version) = 402];
  optional proto_CookPublicInfo CookPublicInfo = 24 [(tcaplus_field_version) = 414]; // 这个玩家的农场餐厅public信息，由farmsvr维护，不在player属性上
  optional proto_VillagePublicInfo VillagePublicInfo = 25[(tcaplus_field_version) = 415]; // 这个玩家的农场村庄public信息，由farmsvr维护，不在player属性上
  optional proto_StarPPlayerAttr StarPlayerInfo    = 26[(tcaplus_field_version) = 417]; // 这个玩家的啾灵public信息，starpaccountsvr负责写入，不在player属性上
  optional StarPPublicUserInfo StarPPublicUserInfo = 27[(tcaplus_field_version) = 417]; // 最新啾灵角色数据,gamesvr负责写入
  optional StarPMiscUserInfo StarPMiscUserInfo = 28[(tcaplus_field_version) = 417]; // 啾灵玩家最新的杂项信息,由StarPAccountSvr维护
  optional StarPPublicUserInfo2 StarPPublicUserInfo2 = 29[(tcaplus_field_version) = 417]; // 最新啾灵角色数据，仅java侧写入
  optional proto_ModPublicInfo ModPublicInfo = 30[(tcaplus_field_version) = 417];
}

message OnlineMonitor {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "timekey,gameappid,gsid";
  option (tcaplus_index) = "index1:timekey,gameappid,gsid";
  option (tcaplus_splitkey) = "timekey";
  required int64 timekey = 1;
  required string gameappid = 2 [(string_size) = 255];
  required string gsid = 3 [(string_size) = 255];
  optional int32 zoneareaid = 4;
  optional int64 onlinecntios = 5;
  optional int64 onlinecntandroid = 6;
}

//TODO 逻辑移到redis后删除
message KVStoreTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "WorldZoneKey,key";
  option (tcaplus_index) = "index1:WorldZoneKey";
  option (tcaplus_splitkey) = "WorldZoneKey";
  required int64 WorldZoneKey = 1;
  required string key = 2;
  required KVStoreValue value = 3;
}

message GuidKey {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Key,Type";
  option (tcaplus_splitkey) = "Type";
  required int32 Key = 1;
  required int32 Type = 2;
  required int64 FreeId = 4 [default = 0];
}

message Guid32Key {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Key";
  option (tcaplus_splitkey) = "Key";
  required string Key = 1;
  required int32 FreeId = 2 [default = 0];
}

message PlayerOnlineTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid";
  option (tcaplus_splitkey) = "Uid";
  required int64 Uid = 1; // 玩家uid
  optional string Openid = 2;
  optional int64 LastLoginTime = 3; // 上次登录时间 废弃
  optional int64 LoginLockTime = 4; // 登陆锁 废弃
  optional int32 svrId = 6; // 玩家当前所在的服ID
  optional int64 LastKeepAliveTime = 7[(tcaplus_field_version) = 94]; // 保活时间
  optional int32 LastSvrId = 8[(tcaplus_field_version) = 157]; // 玩家上次登陆的服ID
  optional int32 playerState = 9[(tcaplus_field_version) = 235]; // 玩家在线状态(对应PlayerStateType，目前只有在线和离线状态)
  // LastSvrId对应的服务器版本号, dirsvr根据此版本来做gamesvr选择. gamesvr通过
  // Player表做最终数据安全保证
  optional int64 LastSvrVersion = 10[(tcaplus_field_version) = 260];
}

// player uid
message PlayerInteractionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Id,Uid";
  option (tcaplus_index) = "index1:Uid";
  option (tcaplus_splitkey) = "Uid";
  required int64 Id = 1;
  required int64 Uid = 2; // dest uid
  optional int64 Src = 3;
  optional PlayerInteractionData body = 4;
  optional int64 RecvTime = 5;
}

message PlayerNickNameTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Name";
  option (tcaplus_index) = "index1:Name";
  option (tcaplus_splitkey) = "Name";
  required string Name = 1;
  required int64 Uid = 2; // 玩家uid
  optional int64 UpdateTime = 3;
}

//message RelationMsgTable {
//  option (tcaplus_customattr2) = "TableType=GENERIC";
//  option (tcaplus_primarykey) = "Uid,Type";
//  option (tcaplus_splitkey) = "Uid";
//  option (tcaplus_index) = "index1:Uid";
//  required int64 Uid = 1; // 玩家uid
//  required int32 Type = 2; // 类型
//  optional RelationMsg msg = 3; // 好友消息内容
//}

// player uid
message RelationTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid1,Uid2,Type";
  option (tcaplus_splitkey) = "Uid1";
  option (tcaplus_index) = "index1:Uid1,Type|index2:Uid1,Uid2|index3:Uid1";
  required int64 Uid1 = 1;
  required int64 Uid2 = 2;
  required int32 Type = 3; // 类型
  optional int64 addTime = 4;
  optional int64 intimacy = 5; // 亲密度
  optional string updateTime = 6[(tcaplus_field_version) = 180]; // 流水需求:更新时间
  optional int64 platId = 7[(tcaplus_field_version) = 180];    // 流水需求:平台id
  optional string openId = 8[(tcaplus_field_version) = 180];   // 流水需求:openid
  optional string friendOpenId = 9[(tcaplus_field_version) = 180]; // 流水需求:好友openid
  optional int32 friendPlatId = 10[(tcaplus_field_version) = 180]; // 流水需求:好友平台id
  // 这两个reason实际上是指通过哪个场景添加的好友, 但字段名字改起来DB重建麻烦
  optional int32 Reason = 11[(tcaplus_field_version) = 182];
  optional int32 SubReason = 12[(tcaplus_field_version) = 182];
  optional int32 intimateId = 13[(tcaplus_field_version) = 188]; // 亲密关系类型
  optional string friendRemarkName = 14[(string_size) = 256, (tcaplus_field_version) = 291]; // 游戏好友备注名称
  optional int32 togetherBattleCount = 15[(tcaplus_field_version) = 317]; // 共同开黑次数
  optional int64 recentInteractTs = 16[(tcaplus_field_version) = 317]; // 最近互动时间
  optional int32 followMallWishList = 17[(tcaplus_field_version) = 393];  // Uid1是否关注Uid2的商城心愿单
  optional int64 mallWishListReadTs = 18[(tcaplus_field_version) = 393]; // Uid1对Uid2的商城心愿单已读时间戳(毫秒)
  //optional int64 privateChatReadSeq = 19[(tcaplus_field_version) = 409]; // 私聊已读序号
  //optional GeneralChatMsg latestPrivateChatMsg = 20[(tcaplus_field_version) = 409]; // 最新私聊消息
}

message PlatFriendTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid";
  option (tcaplus_splitkey) = "Uid";
  required int64 Uid = 1;
  optional PlatFriendList list = 2;
}

// player uid
message PlayerMail {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid,Id";
  option (tcaplus_splitkey) = "Uid";
  option (tcaplus_index) = "index1:Uid";
  required uint64 Id = 1;
  required int64 Uid = 2;
  required uint32 Status = 3;
  optional int64 SenderId = 4;
  optional string SenderName = 5[(tcaplus_field_version) = 143];
  optional uint64 SendTime = 6;
  optional string Title = 7;
  optional MailAttachmentList Attachments = 8;
  optional MailContent Content = 9;
  optional MailContent Url = 10[(tcaplus_field_version) = 144];
  optional uint64 ExpireTime = 11;
  optional MailSource SourceInfo = 12;
  optional MailExtraData ExtraData = 13[(tcaplus_field_version) = 320];
}

message CommonNumericAttr {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uuid,AttrType,AttrKey,TimeKey";
  option (tcaplus_splitkey) = "Uuid";
  option (tcaplus_index) = "index_uid_query:Uuid|index_type_query:Uuid,AttrType,TimeKey|index_flash_query:Uuid,AttrType,AttrKey";
  required int64 Uuid = 1;
  required int64 AttrType = 2;
  required int64 AttrKey = 3;
  required int64 TimeKey = 4;
  optional int64 AttrValue = 5;
}

message ChatGroup {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Id,SubId";
  option (tcaplus_splitkey) = "";
  optional int32 Type = 1;
  optional int64 Id = 2;
  optional int64 SubId = 3;
  optional string Name = 4;
  optional uint64 CreateTime = 5;
  optional int64 CreateUid = 6;
  optional ChatGroupUserList UserList = 7;
  optional uint64 FirstMsgSeqId = 8;
  optional uint64 LastMsgSeqId = 9;
}

// 存储的是一个玩家
message ChatSession {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid,GroupId,GroupSubId";
  option (tcaplus_index) = "index1:Uid";
  option (tcaplus_splitkey) = "Uid";
  optional int64 Uid = 1;
  optional int64 GroupId = 2;
  optional int64 GroupSubId = 3;
  optional int32 GroupType = 4;
  optional uint64 LastReadMsgSeqId = 5;
  optional int32 isShield = 6; // 是否屏蔽
  optional int32 isSticky = 7; // 是否置顶
}

message ChatMsg {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "GroupId,GroupSubId,SeqId";
  option (tcaplus_index) = "index1:GroupId,GroupSubId";
  option (tcaplus_splitkey) = "GroupId,GroupSubId";
  optional int32 GroupType = 1;
  optional int64 GroupId = 2;
  optional int64 GroupSubId = 3;
  optional uint64 SeqId = 4;
  optional int64 SenderUid = 5;
  optional uint64 SendTime = 6;
  optional ChatMsgData Content = 7;
  optional PlayerColdData SenderSnapshot = 8[(tcaplus_field_version) = 62]; // 玩家发消息视的快照信息
  optional int32 SafetyCheckPassFlag = 9[(tcaplus_field_version) = 71]; // 安全检查通过标记 废弃
}

// 存储@消息简要信息
message ChatMsgAidInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "GroupType,GroupId,GroupSubId,SeqId";
  option (tcaplus_index) = "index1:GroupType,GroupId,GroupSubId";
  option (tcaplus_splitkey) = "GroupId";
  optional int32 GroupType = 1;
  optional int64 GroupId = 2;
  optional int64 GroupSubId = 3;
  optional uint64 SeqId = 4;
  optional ChatAidInfo aidInfo = 5;
}


message BattleRoomInfo {// ds 结束后销毁
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "battleId";
  option (tcaplus_splitkey) = "battleId";
  required int64 battleId = 1;
  optional MemberInfoList memList = 2;
  optional int32 battleStatus = 3;
  optional BattleExtraInfo extraInfo = 4;   //ds游戏扩展数据
  optional BattleChatGroupKey groupKey = 5[(tcaplus_field_version) = 15];
  optional MatchRuleInfo matchRule = 6[(tcaplus_field_version) = 48];
  optional KeyValueInfoList resultInfo = 7[(tcaplus_field_version) = 49]; //result info
  optional BattleFrameExtraInfo frameExtraInfo = 8[(tcaplus_field_version) = 49]; //帧同步游戏扩展数据
  optional int64 createtime = 9[(tcaplus_field_version) = 49];
  optional int64 endtime = 10[(tcaplus_field_version) = 49];
  optional int64 curStateStartTime = 11[(tcaplus_field_version) = 50];
  optional int32 isWarmRound = 12[(tcaplus_field_version) = 193];    // 是否是温暖局 废弃
  optional BattleLevelRoundInfo levelRoundInfo = 13[(tcaplus_field_version) = 204];
  optional int64 lastHeartbeatTime = 14[(tcaplus_field_version) = 204];
  optional int32 warmRoundTypeInt = 15[(tcaplus_field_version) = 223];    // 温暖局类型数字值
  optional int32 migrateCount = 16[(tcaplus_field_version) = 245];   //迁移次数
  optional int64 matchedTimeMs = 17[(tcaplus_field_version) = 285];   // 匹配成功时的时间戳
  optional MetaAiGetMatchSuccData metaAiGetMatchData = 18[(tcaplus_field_version) = 285];  // ai lab缀合局额外信息
  optional MemberInfoList observerList = 19[(tcaplus_field_version) = 298];    // 废弃
  optional BattleRoomMidJoinShowInfo roomMidJoinShowInfo = 20[(tcaplus_field_version) = 365];   //对局中途加入展示信息
  optional RoomUgcInfo ugcInfo = 21 [(tcaplus_field_version) = 399]; // 对局所需的ugc信息
  optional MidJoinStatus midJoinStatus = 22 [(tcaplus_field_version) = 416];  // 中途加入显示信息
  optional MemberLeaveList memberLeaveList = 23 [(tcaplus_field_version) = 423];  // 中途退出离开的玩家信息，不包括中途加入的玩家
}

message DSPlayerCustomData {// ds 结束后销毁
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "battleId,playerUid,customId";
  option (tcaplus_index) = "index1:battleId|index2:playerUid,customId";
  option (tcaplus_splitkey) = "battleId";
  required int64 battleId = 1;
  required int64 playerUid = 2;
  required int64 customId = 3;
  optional string customData = 4;
}

message BattleSceneInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "battleId";
  option (tcaplus_splitkey) = "battleId";
  required int64 battleId = 1;
  optional BattleSceneBaseList sceneList = 2;  // 场景列表
  optional MemberInfoList memList = 3;  // 玩家列表
  optional BattleSceneBriefDB briefInfo = 4;  // 场景信息
  optional BattleSceneStatusDB battleSceneStatus = 5 [(tcaplus_field_version) = 422];
  optional MemberLeaveList memberLeaveList = 6 [(tcaplus_field_version) = 423];  // 中途退出离开的玩家信息，不包括中途加入的玩家
}

message StarPBattleRoomInfo {// ds 结束后销毁
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "battleId";
  option (tcaplus_splitkey) = "battleId";
  required int64 battleId = 1;
  optional MemberInfoList memList = 2;
  optional int32 battleStatus = 3;
  optional BattleExtraInfo extraInfo = 4;   //ds游戏扩展数据
  optional BattleChatGroupKey groupKey = 5[(tcaplus_field_version) = 15];
  optional MatchRuleInfo matchRule = 6[(tcaplus_field_version) = 48];
  optional KeyValueInfoList resultInfo = 7[(tcaplus_field_version) = 49]; //result info
  optional BattleFrameExtraInfo frameExtraInfo = 8[(tcaplus_field_version) = 49]; //帧同步游戏扩展数据
  optional int64 createtime = 9[(tcaplus_field_version) = 49];
  optional int64 endtime = 10[(tcaplus_field_version) = 49];
  optional int64 curStateStartTime = 11[(tcaplus_field_version) = 50];
  optional int32 isWarmRound = 12[(tcaplus_field_version) = 193];    // 是否是温暖局 废弃
  optional BattleLevelRoundInfo levelRoundInfo = 13[(tcaplus_field_version) = 204];
  optional int64 lastHeartbeatTime = 14[(tcaplus_field_version) = 204];
  optional int32 warmRoundTypeInt = 15[(tcaplus_field_version) = 223];    // 温暖局类型数字值
  optional int32 migrateCount = 16[(tcaplus_field_version) = 245];   //迁移次数
  optional int64 matchedTimeMs = 17[(tcaplus_field_version) = 285];   // 匹配成功时的时间戳
  optional MetaAiGetMatchSuccData metaAiGetMatchData = 18[(tcaplus_field_version) = 285];  // ai lab缀合局额外信息
  optional MemberInfoList observerList = 19[(tcaplus_field_version) = 298];    // 废弃
}

message MatchRoomInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "roomId";
  option (tcaplus_splitkey) = "roomId";
  required int64 roomId = 1;
  optional int64 leaderId = 2;
  optional MemberInfoList memList = 3;
  optional int32 roomStatus = 4;                          // 废弃
  optional MatchRuleInfo matchRule = 5;
  optional int32 teamType = 6;
  optional ChatGroupKey chatGroupKey = 7[(tcaplus_field_version) = 15];
  optional int64 battleId = 8[(tcaplus_field_version) = 47];
  optional KeyValueInfoList keyValueInfoMap = 9[(tcaplus_field_version) = 47];
  optional KeyValueInfoList beInvitedMap = 10[(tcaplus_field_version) = 47];
  optional KeyValueInfoList want2JoinPlayerInfoMap = 11[(tcaplus_field_version) = 47];
  optional int64 curStateStartTime = 12[(tcaplus_field_version) = 47];                  // 废弃
  optional int64 activeTime = 13[(tcaplus_field_version) = 47];
  optional int32 matchSvrID = 14[(tcaplus_field_version) = 60];
  optional KeyValueInfoList kickPlayerList = 15[(tcaplus_field_version) = 78];
  optional int32 roomType = 16 [(tcaplus_field_version) = 154];                                               // common.proto中的RoomType
  optional RoomState state = 17 [(tcaplus_field_version) = 154];              // 当前状态信息，便于节点恢复
  optional RoomDisplayInfo displayInfo = 18 [(tcaplus_field_version) = 154];  // 外显信息
  optional int32 maxMemberLimit = 19 [(tcaplus_field_version) = 154];         // 最大人数限制
  optional RoomUgcInfo ugcInfo = 20 [(tcaplus_field_version) = 154];          // ugc附加信息
  optional CompetitionElimRoomInfo compElimRoomInfo = 21 [(tcaplus_field_version) = 235]; // 赛事-淘汰赛房间信息
  optional int32 roomVersion = 22[(tcaplus_field_version) = 251]; //用来做数据兼容
  optional RoomCreateOptions roomCreateOpts = 23[(tcaplus_field_version) = 273]; // 创建参数（内含初始设置，后续存储当前设置）
  optional int32 LastSvrId = 24[(tcaplus_field_version) = 274]; // 玩家上次登陆的服ID
  optional int32 battleCount = 25[(tcaplus_field_version) = 290]; //打的局数
  optional int32 maxObserverLimit = 26[(tcaplus_field_version) = 297];         // 最大观战
  optional MemberInfoList observerList = 27[(tcaplus_field_version) = 299];    // ob位玩家的信息 参考memList
  optional RoomTeamBattleBroadcastInfo teamBattleBroadcastInfo = 28[(tcaplus_field_version) = 328]; // 战斗播报相关存储
  optional MidJoinStatus midJoinStatus = 29[(tcaplus_field_version) = 416]; // 中途加入相关设置
}

message CommonQueueTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid,Type,Id";
  option (tcaplus_splitkey) = "Uid";
  option (tcaplus_index) = "index1:Uid,Type";
  required int64 Uid = 1;
  required int32 Type = 2;
  required int64 Id = 3;
  optional int64 RealId = 4;
  optional UniversalData Data = 5;
}

message GlobalMgrDistLock {//全局分布式锁表
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "lockKey,lockType";
  option (tcaplus_splitkey) = "lockKey";
  required int32 lockType = 1;          //锁类型
  optional int64 ownerID = 2;          //拥有者id, 区分谁抢到锁了
  required int64 expireTime = 3;    //超时时间
  optional int64 lockKey = 4;          //具体哪个key
}


message CacheLockDB { //全局分布式锁表
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "lockType, lockKey, lockKey2";
  option (tcaplus_splitkey) = "lockKey";
  required int32 lockType = 1;        //锁类型
  optional int64 ownerID = 2;         //拥有者id, 区分谁抢到锁了
  required int64 expireTime = 3;      //超时时间
  optional int64 lockKey = 4;         //具体哪个key
  optional int64 lockKey2 = 5;        //具体哪个key
}

// 个人历史战绩
message BattleHistory {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=100";
  option (tcaplus_primarykey) = "type,uuid";
  option (tcaplus_splitkey) = "uuid";
  optional int32 type = 1; // matchRecordType
  optional int64 uuid = 2; // uid 玩家ID
  optional CommonPlayerBattleResult commonBattleResult = 3[(tcaplus_field_version) = 185];
  optional LetsGoBattleDetailData battleDetailData = 4[(tcaplus_field_version) = 185];
  optional BattleHistoryExtraInfo battleHistoryExtraInfo = 5[(tcaplus_field_version) = 229];
  optional BattleMemberRecord battleMemberRecord = 6[(tcaplus_field_version) = 281];
}

// 对局结算才保存的数据
message BattleHistorySettlementData {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=110";
  option (tcaplus_primarykey) = "type,uuid";
  option (tcaplus_splitkey) = "uuid";
  optional int32 type = 1; // matchRecordType
  optional int64 uuid = 2; // uid 玩家ID
  optional BattleSettlementData battleSettlementData = 3[(tcaplus_field_version) = 359]; // 对局结算才保存的数据
}

// 战斗记录
message GlobalBattleRecord {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid";
  option (tcaplus_splitkey) = "Uid";
  required int64 Uid = 1;
  optional bytes record = 2[(tcaplus_field_version) = 16];
}

message Player {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid";
  option (tcaplus_splitkey) = "Uid";
  required int64 Uid = 1;
  optional int32 Platid = 2; //0ios 1android
  optional string Openid = 3;
  optional int32 Level = 4;
  optional proto_UserAttr UserAttr = 5;
  optional proto_UserAttr ChangedUserAttr = 6;
  optional int32 ChangeTimes = 7 ;
  optional int64 LoginCheckTime = 8;

  // 上次登录的服务器(gamesvr)版本, 如果当前服务器版本小于此版本(只考虑major和
  // minor), 应该禁止在当前服务器登录, 避免数据安全问题. 这里没有使用string保存
  // 版本, 版本号规则应当固化(在db和tbuspp属性都会记录)
  optional int64 LastLoginSvrVersion = 9[(tcaplus_field_version) = 260];
}

// 玩家活动数据 By:ActivitySvr
message PlayerActivity {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid";
  option (tcaplus_splitkey) = "Uid";
  required int64 Uid = 1;							// 玩家Uid
  optional proto_UserActivityAttr ActivityData = 2; // 玩家活动数据
}

// TODO server表合并
message ServerInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "namespace,serverType,name";
  option (tcaplus_splitkey) = "namespace";
  option (tcaplus_index) = "index_namespace:namespace|index_server_type:namespace,serverType";
  required string name = 1;
  required string namespace = 2;
  required string serverType = 3;
  required int32 status = 4;
  optional int64 registerTime = 5;
  optional int64 lastHeartbeatTime = 6;
  optional string loadConfigVersion = 7;
  optional int64 loadConfigTime = 8;
  optional string loadResourceVersion = 9;
  optional int64 loadResourceTime = 10;
  optional string configVersion = 11;
  optional string resourceVersion = 12;
  optional int32 totalOnline = 13;
  optional int32 maxOnline = 14;
  optional int32 weight = 15;
  optional int32 port = 16;
}

message RoutingTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,uuid";
  option (tcaplus_splitkey) = "uuid";
  required int32 type = 1;
  required int64 uuid = 2;
  required int64 delIndex = 3;    // 淘汰策略索引, 需创建全局二级索引
  optional int32 svrId = 4;       // 路由结果
  optional int64 createTime = 5;  // 创建时间
  optional int64 modifyTime = 6;
}

message SrvInteraction {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "BusId,Id";
  option (tcaplus_splitkey) = "BusId";
  option (tcaplus_index) = "index_query:BusId";
  required int32 BusId = 1;
  required int64 Id = 2;
  required SrvInteractParams Params = 3;
  required int64 CreateTime = 4[(tcaplus_field_version) = 46];
}

//idip封禁表，zoneid&uid=0表示全区封禁，uid=0表示
//todo 是否需要zoneid,Platid的维度，以及按openid封禁，Uid解封，哪个为准
message IdipBanInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid";
  option (tcaplus_index) = "index1:Uid";
  option (tcaplus_splitkey) = "Uid";
  optional int64 Uid = 1;
  optional int64 CreateTime = 2;
  optional BanInfo banInfo = 3;
}

// TODO server表合并
message StatefullSvrPayload{
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,svrId";
  option (tcaplus_index) = "index_type:type";
  option (tcaplus_splitkey) = "type";
  optional int32 svrId = 1;
  optional int32 type = 2;
  optional int32 payload = 3;
  optional int64 lastReportTime = 5;
}

// 服务负载信息 已废弃
message ServerLoadInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,svrId,subType";
  option (tcaplus_index) = "index1:type|index2:type,svrId";
  option (tcaplus_splitkey) = "type";
  optional int32 type = 1;            // 负载类型
  optional int32 svrId = 2;           // 服务id
  optional int32 subType = 3;         // 负载子类型
  optional int32 load = 4;            // 负载数值
  optional int64 lastUpdateTime = 6;  // 上次更新时间
}


// 服务负载信息
message SvrLoadInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,svrId,subType";
  option (tcaplus_index) = "index1:type|index2:type,svrId";
  option (tcaplus_splitkey) = "type";
  optional int32 type = 1;            // 负载类型
  optional int32 svrId = 2;          // 服务id
  optional string subType = 3;         // 负载子类型
  optional int32 load = 4;            // 负载数值
  optional int64 lastUpdateTime = 6;  // 上次更新时间
}

message NotRepeatedName {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "createName,moduleType";
  option (tcaplus_splitkey) = "createName";
  optional string createName = 1;
  optional int32 moduleType = 2;
  optional string extraInfo = 3;
}

message LobbyInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "lobbyId";
  option (tcaplus_splitkey) = "lobbyId";
  required int64 lobbyId = 1;
  optional int32 mapId = 2; // 废弃
  optional ChatGroupKey chatGroupKey = 3;
  optional int64 dsaInstanceID = 4;
  optional string dsAddr = 5[(tcaplus_field_version) = 132];
  optional LobbyExtraInfo extraInfo = 6[(tcaplus_field_version) = 171];
  optional IntArray mapDynamicShowList = 7[(tcaplus_field_version) = 227];
  optional PlacedObjectInfo placedObjectInfo = 8[(tcaplus_field_version) = 269]; // 放置物
  optional int32 mapInstanceId = 9 [(tcaplus_field_version) = 389]; //mapId的规则变动，改用这个字段存储
  optional int64 ugcId = 10 [(tcaplus_field_version) = 390]; //ugcId，自测UGC大厅图专用
  optional int64 creatorUid = 11 [(tcaplus_field_version) = 390]; //创建大厅的玩家
  optional int64 creatorExitMsTime = 12 [(tcaplus_field_version) = 390]; //创建大厅的玩家上次退出大厅的时间
}

message UgcBrief {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,ugcId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";

  optional int64 creatorId = 1[(tcaplus_field_version) = 212];
  optional int64 ugcId = 2[(tcaplus_field_version) = 212];  // 类型
  optional string name = 3[(tcaplus_field_version) = 212];  // 地图名称
  optional int32 templateId = 4[(tcaplus_field_version) = 212]; //模板id
  optional int32 saveType = 5[(tcaplus_field_version) = 212]; //保存类型
  optional int32 saveCount = 6[(tcaplus_field_version) = 212]; // 保存次数
  optional int32 isDelete = 7[(tcaplus_field_version) = 212]; // 1删 0有效
  optional int64 createTime = 8[(tcaplus_field_version) = 212]; //创建时间
  optional int64 expireTime = 9[(tcaplus_field_version) = 212]; //过期时间
  optional UgcMdList mdList = 10[(tcaplus_field_version) = 212]; //md5信息
  optional int64 oldUgcId = 11[(tcaplus_field_version) = 212]; //旧的ugcId
  optional string desc = 12[(tcaplus_field_version) = 212]; //描述
  optional string tags = 13[(tcaplus_field_version) = 212]; //tags
  optional int32 reportStatus = 14[(tcaplus_field_version) = 212];  //审核状态
  optional int64 rejectTime = 15[(tcaplus_field_version) = 212];  //销毁时间 驳回后
  optional int32 editorSec = 16[(tcaplus_field_version) = 212];  //编辑时长
  optional int32 mapType = 17[(tcaplus_field_version) = 212];  //地图类型 1:普通地图 2：共创 UgcMapType
  optional int32 isCollect = 18[(tcaplus_field_version) = 212];  // 是否收藏
  optional int64 collectTime = 19[(tcaplus_field_version) = 212]; // 收藏时间
  optional UgcExtraInfo extraInfo = 20[(tcaplus_field_version) = 212];   // 额外信息
  optional string bucket = 21[(tcaplus_field_version) = 212]; //bucket
  optional UgcGroupIdList ugcGroupIdList = 22[(tcaplus_field_version) = 212];
  optional string ugcVersion = 23[(tcaplus_field_version) = 212];         //地图版本号
  optional string clientVersion = 24[(tcaplus_field_version) = 212];      //客户端版本号
  optional UgcMapMetaSaveInfo saveInfo = 25[(tcaplus_field_version) = 212];
  optional UgcMapMetaList metaList = 26[(tcaplus_field_version) = 212];   //更新自核列表
  optional string difficulty = 27[(tcaplus_field_version) = 212];   //地图难度
  optional UgcCommonMapInfo commMap = 28[(tcaplus_field_version) = 212];//地图通用信息
  //********************废弃****************************
  optional UgcBaseList mapList = 29[(tcaplus_field_version) = 212];//地图信息
  optional UgcBaseList groupList = 30[(tcaplus_field_version) = 212];//组合信息
  optional UgcBaseList homeList = 31[(tcaplus_field_version) = 212];//小窝信息
  optional UgcCoCreateList coCreateList = 32[(tcaplus_field_version) = 212];//共创信息
  //********************废弃****************************
  optional UgcLayerList layers = 33[(tcaplus_field_version) = 230];
  optional UgcEditorList editors = 34[(tcaplus_field_version) = 230];
  optional int64 updateTime = 35[(tcaplus_field_version) = 230];//更新时间
  optional UgcPublishDescInfo publishInfo = 36[(tcaplus_field_version) = 236];
  optional int64 secFlags = 37[(tcaplus_field_version) = 242];  // 地图安全标记
  optional UgcMapMgrInfo mgrInfo = 38[(tcaplus_field_version) = 253];// 地图管理信息（主要来自管理端操作，玩家不会设置）目前只给了锁定状态使用
  optional int32 ugcResType= 39[(tcaplus_field_version) = 261];       // 资源类型
  optional int32 resCategory = 40[(tcaplus_field_version) = 261];     // 资源大类别
  optional int32 resSubCategory = 41[(tcaplus_field_version) = 261];  // 资源小类别
  optional string resLabels = 42[(tcaplus_field_version) = 261];      // 资源标签
  optional int32 disableMultiTest = 43[(tcaplus_field_version) = 286]; //封禁多人测试
  optional int64 publishTime = 44[(tcaplus_field_version) = 301];     //发布时间
  optional ArrayLong resIdList = 45[(tcaplus_field_version) = 304];     //引用资产id
  optional int32 publishGoodsStatus = 46[(tcaplus_field_version) = 309];  // 创建星钻商品能力开启  表示作者可以创建星钻商品   enum UgcMapPublishGoodsStatus
  optional int32 buyGoodsStatus = 47[(tcaplus_field_version) = 309];    // 购买商品能力开启  可能由于二次修改地图商业化审核不过而被关闭， enum  UgcMapBuyGoodsStatus
  optional int32 isResPubCosPath = 48[(tcaplus_field_version) = 321];  // 私有资源是否在pub路径
  optional UgcCosInfo cover = 49[(tcaplus_field_version) = 321];       //多封面缩略图
  optional int32 modelType = 50[(tcaplus_field_version) = 331];  //当前地图模式
  optional ArrayLong banResIdList = 51[(tcaplus_field_version) = 355]; // 封禁资产id列表,最多不超过50个
  optional int32 curLayerId = 52[(tcaplus_field_version) = 358]; // 当前所在图层id
  optional int32 hasPublishGoodsRecord = 53[(tcaplus_field_version) = 363];   // 是否成功发布过星钻商品 0 没发过 1 发过 enum DataStoreSaveType
  optional UgcAchievementMap ugcAchievement = 54[(tcaplus_field_version) = 379];  // 成就列表
  optional int32 achievementVerId = 55[(tcaplus_field_version) = 379];
  optional int32 evaluationStatus = 56[(tcaplus_field_version) = 394];  // 地图评估状态 UgcMapEvaluationStatusType
  optional int32 evaluationStatusReport = 57[(tcaplus_field_version) = 396];  // 地图审核状态上报状态 UgcMapEvaluationStatusType
  optional UgcMapExtraConfigIndexMap extraConfigIndexMap = 58[(tcaplus_field_version) = 397];  // 额外配置索引
  optional int32 extraConfigIndexVerId = 59[(tcaplus_field_version) = 397];  // 额外配置版本id
  optional MapLoadingInfo mapLoading = 60[(tcaplus_field_version) = 401];  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 61[(tcaplus_field_version) = 401];  // 乐园自定义封面
  optional MapCoverVideoInfoSaved videoInfo = 62[(tcaplus_field_version) = 406];  // 视频封面
}

message BillTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid,Type,BillNo,Seq";
  option (tcaplus_index) = "index1:Uid,Type,BillNo|index2:Uid,Type,Seq|index3:Uid,Type";
  option (tcaplus_splitkey) = "Uid,Type";
  optional int64 Uid = 1;
  optional int32 Type = 2;
  optional string BillNo = 3;
  optional int64 Seq = 4;

  optional int32 Status = 5;
  optional int64 CreateTime = 6;
  optional BillInfo BillInfo = 7;
}

message BillSeq{
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid,Type";
  option (tcaplus_splitkey) = "Uid";
  optional int64 Uid = 1;
  optional int32 Type = 2;
  optional int64 Seq = 4;
}

message ActivityInviteActive {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "activityId,inviteesUid";
  option (tcaplus_splitkey) = "inviteesUid";
  optional int32 activityId = 1;
  optional int64 inviteesUid = 2;
  optional int64 inviterUid = 3;
}

message ActivitySquadTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "squadId";
  option (tcaplus_splitkey) = "squadId";

  optional int64 squadId = 1; // 小队id
  optional int32 activityNo = 2;  //活动期数
  optional MemberInfoList memberList = 3;  //成员列表
  optional SquadDailyTaskInfo dailyTaskInfo = 4; // 每日任务
  optional SquadTaskGroupInfo achievementTaskInfo = 5; // 成就任务
}

message MultiPlayerSquadDigRecordArray {
  repeated MultiPlayerSquadDigRecord digRecords = 1; // 挖掘记录
}

message MultiPlayerSquadTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "squadId";
  option (tcaplus_splitkey) = "squadId";

  optional int64 squadId = 1; // 小队id
  optional int32 activityId = 2; // 活动id
  optional int32 activityNo = 3;  //活动期数
  optional ArrayLong memberUidList = 4;  //成员Uid列表
  optional int64 lastUpdateTimestampMs = 5; // 最后一次更新时间
  optional MultiPlayerSquadDigRecordArray recordArray = 6[(tcaplus_field_version) = 219]; // 挖掘记录
}

message UgcMapSeason {
  optional int32 seasonId = 1; // 赛季ID
  optional int64 seasonExp = 2; // 赛季增加的工匠值
}

message UgcMapHistorySeason {
  repeated UgcMapSeason SeasonExp = 1; // 历史赛季地图的工匠值
  optional int64 expDaily = 2;
  optional int64 expWeekly = 3;
  optional int64 expSeason = 4;
  optional int64 expHistory = 5;
}

message UgcDbItemInfo{
  optional string itemId = 1;        // 物品ID
  optional int32 itemNum = 2;       // 物品数量
}

message UgcDbGoodsInfo{
  optional string goodsId = 1;    // 商品ID
  optional int32 uintPrice = 2;          // 价格
  repeated UgcDbItemInfo itemInfo = 3;  // 商品信息
}

message UgcDBGoodsList {
  repeated UgcDbGoodsInfo ugcGoodsInfo = 2;    // 上架商品信息
}
message UgcPublishGoodsInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId";
  option (tcaplus_splitkey) = "ugcId";

  optional int64 ugcId = 1;  //地图唯一id
  optional UgcDBGoodsList ugcGoodsList = 2[(tcaplus_field_version) = 303];    // 上架商品信息列表
}

message UgcRankList {
  repeated UgcRankInfo rankInfo = 1;      // 排行榜基本信息
}

message UgcPublish {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId";
  option (tcaplus_splitkey) = "ugcId";

  optional int64 ugcId = 1;  //地图唯一id
  optional int64 creatorId = 2;
  optional string name = 3;  //地图名称
  optional string desc = 4; //模板id
  optional UgcTypeList type = 5; //保存类型
  optional int32 isDelete = 6; // 1删 0有效 (废弃)
  optional int64 createTime = 7; //创建时间
  optional UgcMdList mdList = 8; //crcList
  optional string bucket = 9; //bucket
  optional int64 oldUgcId = 10; //旧的ugcid
  optional int32 templateId = 11; //模板id
  optional string editorName = 12; //编辑者的名字
  optional string tags = 13; //tags
  optional int32 reviewStatus = 14; //审核状态
  optional string editorAvatar = 15; //作者头像
  optional int64 ugcExp = 16; // 当前赛季地图的工匠值
  optional int32 likeCount = 17; //点赞数量
  optional int32 collectCount = 18; //收藏数量
  optional UgcMapHistorySeason mapHistorySeasonExp = 19; // 历史赛季地图的工匠值
  optional uint64 mapExpUpdateTime = 20; // 废弃
  optional int32 seasonId = 21; //当前赛季ID
  optional int32 playCount = 22; //废弃
  optional int32 pointsNumber = 23; //房间人数
  optional int32 ugcInstanceType = 24; //实例类型
  optional UgcGroupIdList ugcGroupIdList = 25;
  optional int32 editorSec = 26;  //编辑时长
  optional string openId = 27; //作者openid
  optional UgcExtraInfo extraInfo = 28;   // 额外信息
  optional int32 isOfficial = 29;  // 是否是官方发布
  optional UgcBestRecord wxRecord = 30; //微信区
  optional UgcBestRecord qqRecord = 31; //qq区
  optional UgcPlayRecord wxPlayRecord = 32; //微信区游玩记录
  optional UgcPlayRecord qqPlayRecord = 33; //qq区游玩记录
  optional int64 totalUgcExp = 34; // 地图的总工匠值
  optional int32 platId = 35;//废弃(2025/2/20)
  optional int32 useCount = 36[(tcaplus_field_version) = 190]; //使用数量（组合被使用数量，地图过审后统计）
  optional string ugcVersion = 37[(tcaplus_field_version) = 200];
  optional string clientVersion = 38[(tcaplus_field_version) = 200];
  optional UgcMapMetaList metaList = 39[(tcaplus_field_version) = 202];   //更新自核列表
  optional string difficulty = 40[(tcaplus_field_version) = 203];   //地图难度
  optional UgcCommonMapInfo commMap = 41[(tcaplus_field_version) = 205];//地图通用信息
  optional int32 shareCount = 42[(tcaplus_field_version) = 208]; //分享数量
  optional UgcMapMgrInfo mgrInfo = 43[(tcaplus_field_version) = 218];    // 管理端可修改的
  optional UgcPublishDescInfo descInfo = 44[(tcaplus_field_version) = 226];

  optional string takeoffReason = 45[(tcaplus_field_version) = 226];
  optional UgcLayerList layers = 46[(tcaplus_field_version) = 226];
  optional string reviewReason = 47[(tcaplus_field_version) = 226];//审核通过原因
  optional UgcEditorList editors = 48[(tcaplus_field_version) = 226];
  optional int64 updateTime = 49[(tcaplus_field_version) = 226];//更新时间
  optional int64 playSec = 50[(tcaplus_field_version) = 228];  //游玩总时长

  optional int32 occupancyValue = 51[(tcaplus_field_version) = 231]; //地图占用值
  optional int32 propertyScore = 52[(tcaplus_field_version) = 233]; //性能评分
  optional int32 actorCount = 53[(tcaplus_field_version) = 233]; //actor数量
  optional int32 useCountPrePublish = 54[(tcaplus_field_version) = 234]; // 使用数量（组合被使用数量，地图发布后就统计，此时地图未过审）
  optional int32 ugcResType= 55[(tcaplus_field_version) = 261];       // 资源类型
  optional int32 resCategory = 56[(tcaplus_field_version) = 261];     // 资源大类别
  optional int32 resSubCategory = 57[(tcaplus_field_version) = 261];  // 资源小类别
  optional string resLabels = 58[(tcaplus_field_version) = 261];      // 资源标签
  optional int32 resBagRefCount = 59[(tcaplus_field_version) = 268];        // 被加入资源库的次数
  optional int64 passTotalSec = 60[(tcaplus_field_version) = 277];        //通关总时长
  optional int32 passTotalCount = 61[(tcaplus_field_version) = 277];        //通关次数
  optional int64 publishTime = 62[(tcaplus_field_version) = 300];          //发布时间
  optional int32 publishGoodsStatus = 63[(tcaplus_field_version) = 302];  // 创建星钻商品能力开启  表示作者可以创建星钻商品   enum UgcMapPublishGoodsStatus
  optional int32 bugGoodsStatus = 64[(tcaplus_field_version) = 302];    // 废弃
  optional ArrayLong resIdList = 65[(tcaplus_field_version) = 304];     //引用资产id
  optional int32 buyGoodsStatus = 66[(tcaplus_field_version) = 312];    // 购买商品能力开启  可能由于二次修改地图商业化审核不过而被关闭， enum  UgcMapBuyGoodsStatus
  optional int32 danMuCnt = 67[(tcaplus_field_version) = 321];  // 弹幕数量
  optional UgcMapLabelScore labelScore = 68[(tcaplus_field_version) = 321];  // 标签评分
  optional int32 isResPrivate = 69[(tcaplus_field_version) = 321]; // 是否是私有资源
  optional int32 isResPubCosPath = 70[(tcaplus_field_version) = 321]; // 私有资源是否在pub路径
  optional int32 isResPrivateDelete = 71[(tcaplus_field_version) = 321]; // 私有资产是否已删除
  optional UgcCosInfo cover = 72[(tcaplus_field_version) = 321];       //多封面缩略图
  optional UgcRankList rankList = 73[(tcaplus_field_version) = 326]; // 排行榜相关配置信息

  optional int64 blackPlayUv = 75[(tcaplus_field_version) = 340];
  optional int64 blackLikeCount = 76[(tcaplus_field_version) = 340];
  optional int64 blackPlaySec = 77[(tcaplus_field_version) = 340];
  optional ArrayLong banResIdList = 78[(tcaplus_field_version) = 355]; // 封禁资产id列表,最多不超过50个
  optional int32 applyTakeOff = 79[(tcaplus_field_version) = 360]; // 是否申请下架中, 1申请中
  optional string applyTakeOffReason = 80[(tcaplus_field_version) = 361];
  optional int32 hasPublishGoodsRecord = 81[(tcaplus_field_version) = 362];   // 是否成功发布过星钻商品 0 没发过 1 发过 enum DataStoreSaveType
  optional UgcAppPublishCommonInfo ugcAppCommonInfo = 82[(tcaplus_field_version) = 368];  // 独立app publish表通用信息
  optional UgcDatastoreAccessedPermissionInfo dataStoreAccessedPermissionInfo = 83[(tcaplus_field_version) = 377]; //可访问当前地图的UGC地图信息列表
  optional UgcAchievementMap ugcAchievement = 84[(tcaplus_field_version) = 379];  // 成就列表
  optional UgcVersionPassRecordData versionPassRecordData = 85[(tcaplus_field_version) = 391];  // 版本下通关记录数据
  optional int32 evaluationStatus = 86[(tcaplus_field_version) = 394];  // 地图评估状态
  optional UgcMapExtraConfigIndexMap extraConfigIndexMap = 87[(tcaplus_field_version) = 397];  // 额外配置索引
  optional int64 versionPassTotalSec = 88[(tcaplus_field_version) = 398];  // 版本下通关总时长, 独立于版本下通关记录数据, 便于使用inc函数进行数据累加
  optional int32 versionPassTotalCount = 89[(tcaplus_field_version) = 398];  // 版本下通关总次数, 独立于版本下通关记录数据, 便于使用inc函数进行数据累加
  optional MapLoadingInfo mapLoading = 90[(tcaplus_field_version) = 401];  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 91[(tcaplus_field_version) = 401];  // 乐园自定义封面
  optional MapCoverVideoInfoSaved videoInfo = 92[(tcaplus_field_version) = 406];  // 视频封面
  optional UgcPublishInputParam publishParam = 93[(tcaplus_field_version) = 412]; // 发布输入参数
  // 这里需要一定要从95开始了，因为线上表的序号影响，一定注意。

}

// 留言
message MessageSlip{
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Id";
  option (tcaplus_splitkey) = "Id";
  optional uint64 Id = 1;             // 留言id
  optional int64 Uid = 2;             // 留言者uid
  optional uint32 Status = 3;         // 留言状态
  optional string slipDigest = 4;     // 留言摘要
  optional string slipContent = 5;    // 留言内容
  optional uint64 createdTime = 6;    // 留言时间
  optional int64 favourCount = 7;     // 留言点赞数
  optional int64 commentCount = 8;    // 留言评论数
}

// 留言评论
message MessageComment {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=1000";
  option (tcaplus_primarykey) = "slipId";
  option (tcaplus_splitkey) = "slipId";
  optional int64 slipId = 1;              // 留言id
  optional uint64 Id = 2;                 // 评论id
  optional int64 uuid = 3;                // 评论者uid
  optional string commentContent = 4;     // 评论内容
  optional uint64 createdTime = 5;        // 评论时间
}

// 留言点赞
message MessageFavour {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=1000";
  option (tcaplus_primarykey) = "slipId";
  option (tcaplus_splitkey) = "slipId";
  optional int64 slipId = 1;              // 留言id
  optional int64 uuid = 2;                // 点赞者uid
  optional uint64 createdTime = 3;        // 点赞时间
}

message UgcPlayerSeason {
  optional int32 seasonId = 1; // 赛季ID
  optional int64 seasonExp = 3; // 赛季工匠值(TOP2工匠值地图的工匠值之和)
}

message UgcPlayerHistorySeason {
  repeated UgcPlayerSeason SeasonExp = 1; // 历史赛季玩家的工匠值
}

message UgcLvInfo {
  optional int32 expLv = 1; // 玩家工匠值能达到几级
  optional int32 mapCountLv = 2; // 发布地图的数量能达到几级
  optional int32 playerCountLv = 3; // 每个地图的游玩人数能达到几级
  optional int32 createMapCountLv = 4; // 创建地图的数量能达到几级
  optional int32 hasInit = 5;
}

message DressItemInfos {
  repeated PlayerDressItemInfo dressItemInfos = 1;
}

message UgcAccountInfo {
  optional com.tencent.wea.xlsRes.UgcAuthType ugcAuthType = 1;
  optional int32 draftCap = 2;
  optional int32 publishedCap = 3;
  optional string ugcAuthDesc = 4;
}

message UgcPlayerInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId";
  option (tcaplus_splitkey) = "creatorId";
  optional int64 creatorId = 1;     // creatorId
  optional UgcTasks ugcTasks = 2;   // ugc工匠任务
  optional int64 ugcExp = 3; // 玩家总工匠值(所有已发布TOP5工匠值地图的工匠值之和)
  optional int32 ugcLv = 4; // 玩家工匠等级(对应宫颈登记配置表里面的唯一id)
  optional int64 ugcSeasonExp = 5;  // 废弃
  optional UgcPlayerHistorySeason playerHistorySeasonExp = 6; // 玩家历史赛季的工匠值
  optional int32 seasonId = 7; // 废弃
  optional int32 fansCount = 8; //粉丝数量
  optional int32 subCount = 9; //订阅数量
  optional int32 beLikeCount = 10; //被点赞数量
  optional int32 playCount = 11; // 游玩数量
  optional int32 publishCount = 12; //发布数量
  optional int32 passCount = 13; //通关数量
  optional string nickname = 14; //昵称
  optional int32 gender = 15; //性别  1-男 2-女 0-未知
  optional string profile = 16; //头像url
  optional uint64 shortUid = 17; //短uid
  optional UgcCollect mapCollect = 18; //收藏
  optional UgcCollect groupCollect = 19; //组合收藏
  optional UgcSub fans = 20; //粉丝
  optional UgcSub subs = 21; //订阅
  optional UgcLvInfo ugcLvInfo = 22; // 工匠值等级相关数据
  optional int32 ugcExpFlag = 23; //这个值不为0的时候,表示玩家的工匠值要重新计算
  optional string openId = 24;
  optional int32 platId = 25;
  optional IntArray dressIds = 26;
  optional int32 loginType = 27; // 表示是手Q还是微信
  optional proto_PlayerRankGeoInfo rankGeoInfo = 28;
  optional int64 uid = 29;     // uid
  optional DressItemInfos dressItemInfos = 30[(tcaplus_field_version) = 199];
  optional PlayerGameSettings gameSettings = 31[(tcaplus_field_version) = 199];
  optional int32 qualifiedMapCount = 32[(tcaplus_field_version) = 202]; // 达标地图数
  optional UgcPlayerCommonInfo commonInfo = 33[(tcaplus_field_version) = 205];//通用数据
  optional int32 peakUgcExp = 34[(tcaplus_field_version) = 209];
  optional UgcAccountInfo accountInfo = 35[(tcaplus_field_version) = 212];
  optional int64 ugcExpUpdateMs = 36[(tcaplus_field_version) = 265];
  optional int32 trustworthy = 37[(tcaplus_field_version) = 300];   //0:不是 1:是
  optional int64 bitFlag = 38[(tcaplus_field_version) = 315];  //处理标记
  optional int32 accountType = 39[(tcaplus_field_version) = 334];  //账号状态
  optional int32 accountSource = 40[(tcaplus_field_version) = 335];  // 账号来源, 详情见枚举UgcAccountSourceType所示
  optional int32 appOnline = 41[(tcaplus_field_version) = 392];  // app账号在线状态 0:离线 1:在线
  optional int32 createCount = 42[(tcaplus_field_version) = 403]; //作为协作者发布数量
  optional UgcCreatorHomePageInfo homePageInfo = 43[(tcaplus_field_version) = 406]; //创作者主页
}

message UgcPlayerManagementInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId";
  option (tcaplus_splitkey) = "creatorId";
  optional int64 creatorId = 1;             // creatorId
  optional int32 source = 2;             // 来源 0 ⾃动注册；1 官⽅邀请
  optional string maintenanceState = 3;  //维护状态, ⼈⼯可编辑字段(如张三建联维护)
  optional int32 certificationState = 4; //官⽅认证（⽤户⼿动申请，官⽅进⾏审核，参考微博加V）
  optional int32 creatorType = 5;        //创作者培养类型；0 潜⼒作者；1-创作者-普通；2 创作者-核⼼；3 签约作者
  optional int32 blackList = 6;          //创作者状态 0 正常， 1 ⿊名单⽤户
  optional int32 fansCount = 7;          //废弃不用了
  optional int32 subCount = 8;           //废弃不用了
}

message PlayerUgcGiveLike {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,ugcId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";
  optional int64 creatorId = 1;         // creatorId
  optional int64 ugcId = 2;          // 地图id
  optional int64 createTime = 3;     // 时间
  optional int32 status = 4;         // 状态  0正常1删
  optional int32 mapType = 5;        // UgcInstanceType
  optional int32 ugcResType = 6[(tcaplus_field_version) = 263];     // UgcResType
}

message PlayerUgcCollect {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,ugcId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";
  optional int64 creatorId = 1;         // creatorId
  optional int64 ugcId = 2;          // 地图id
  optional int64 createTime = 3;     // 时间
  optional int32 status = 4;         // 状态  0正常1删
  optional int32 mapType = 5;        // UgcInstanceType
  optional int32 ugcResType = 6[(tcaplus_field_version) = 263];     // UgcResType
}

message PlayerUgcSub {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,subId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";
  optional int64 creatorId = 1;         // creatorId
  optional int64 subId = 2;          // 被订阅者id
  optional int32 isTop = 3;          // 是否置顶
  optional int64 topTime = 4;        // 置顶时间
  optional int64 createTime = 5;     //创建时间
}

message PlayerUgcFans {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,fanId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";
  optional int64 creatorId = 1;          // creatorId
  optional int64 fanId = 2;           // 粉丝id
  optional int64 createTime = 3;      // 创建时间
}

//作废
message PlayerUgcFansList {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=1000";
  option (tcaplus_primarykey) = "creatorId";
  option (tcaplus_splitkey) = "creatorId";
  optional int64 creatorId = 1;          // creatorId
  optional int64 fanId = 2;           // 粉丝id
  optional int64 createTime = 3;      // 创建时间
}


message UgcCoCreateBrief {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId";
  option (tcaplus_splitkey) = "ugcId";
  option (tcaplus_index) = "index_type:ugcId";

  optional int64 ugcId = 1;
  optional UgcEditorList member = 2[(tcaplus_field_version) = 216]; //共创者
  //*****************废弃***********************
  optional UgcBaseBrief brief = 3[(tcaplus_field_version) = 216]; //基础信息
  //*******************************************
  optional string name = 4[(tcaplus_field_version) = 230];  //地图名称
  optional int32 templateId = 5[(tcaplus_field_version) = 230]; //模板id
  optional int32 saveType = 6[(tcaplus_field_version) = 230]; //保存类型
  optional int32 saveCount = 7[(tcaplus_field_version) = 230]; // 保存次数
  optional int64 createTime = 8[(tcaplus_field_version) = 230]; //创建时间
  optional int64 expireTime = 9[(tcaplus_field_version) = 230]; //过期时间
  optional UgcMdList mdList = 10[(tcaplus_field_version) = 230]; //md5信息
  optional int64 oldUgcId = 11[(tcaplus_field_version) = 230]; //旧的ugcId
  optional string desc = 12[(tcaplus_field_version) = 230]; //描述
  optional string tags = 13[(tcaplus_field_version) = 230]; //tags
  optional int32 reportStatus = 14[(tcaplus_field_version) = 230];  //审核状态
  optional int64 rejectTime = 15[(tcaplus_field_version) = 230];  //销毁时间 驳回后
  optional int32 editorSec = 16[(tcaplus_field_version) = 230];  //编辑时长
  optional int32 mapType = 17[(tcaplus_field_version) = 230];  //地图类型 1:普通地图 2：共创 UgcMapType
  optional int32 isCollect = 18[(tcaplus_field_version) = 230];  // 是否收藏
  optional int64 collectTime = 19[(tcaplus_field_version) = 230]; // 收藏时间
  optional UgcExtraInfo extraInfo = 20[(tcaplus_field_version) = 230];   // 额外信息
  optional string bucket = 21[(tcaplus_field_version) = 230]; //bucket
  optional UgcGroupIdList ugcGroupIdList = 22[(tcaplus_field_version) = 230];
  optional string ugcVersion = 23[(tcaplus_field_version) = 230];         //地图版本号
  optional string clientVersion = 24[(tcaplus_field_version) = 230];      //客户端版本号
  optional UgcMapMetaSaveInfo saveInfo = 25[(tcaplus_field_version) = 230];
  optional UgcMapMetaList metaList = 26[(tcaplus_field_version) = 230];   //更新自核列表
  optional string difficulty = 27[(tcaplus_field_version) = 230];   //地图难度
  optional UgcCommonMapInfo commMap = 28[(tcaplus_field_version) = 230];//地图通用信息
  optional UgcLayerList layers = 29[(tcaplus_field_version) = 230];
  optional int64 updateTime = 30[(tcaplus_field_version) = 230];//更新时间
  optional UgcPublishDescInfo publishInfo = 31[(tcaplus_field_version) = 236];
  optional int64 secFlags = 32[(tcaplus_field_version) = 242];  // 地图安全标记
  optional int32 disableMultiTest = 33[(tcaplus_field_version) = 287]; // 是否允许多人测试
  optional ArrayLong resIdList = 34[(tcaplus_field_version) = 304];     //引用资产id
  optional int64 publishTime = 35[(tcaplus_field_version) = 307];     //发布时间
  optional int32 publishGoodsStatus = 36[(tcaplus_field_version) = 309];  // 创建星钻商品能力开启  表示作者可以创建星钻商品   enum UgcMapPublishGoodsStatus
  optional int32 buyGoodsStatus = 37[(tcaplus_field_version) = 309];    // 购买商品能力开启  可能由于二次修改地图商业化审核不过而被关闭， enum  UgcMapBuyGoodsStatus
  optional UgcCosInfo cover = 38[(tcaplus_field_version) = 321];       //多封面缩略图
  optional int32 modelType = 39[(tcaplus_field_version) = 332];  //当前地图模式
  optional int32 curLayerId = 40[(tcaplus_field_version) = 358]; // 当前所在图层id
  optional int32 hasPublishGoodsRecord = 41[(tcaplus_field_version) = 363];   // 是否成功发布过星钻商品 0 没发过 1 发过 enum DataStoreSaveType
  optional UgcAchievementMap ugcAchievement = 42[(tcaplus_field_version) = 379];  // 成就列表
  optional int32 achievementVerId = 43[(tcaplus_field_version) = 379];
  optional int32 evaluationStatus = 44[(tcaplus_field_version) = 394];  // 地图评估状态
  optional int32 evaluationStatusReport = 45[(tcaplus_field_version) = 396];  // 地图审核状态上报状态 UgcMapEvaluationStatusType
  optional UgcMapExtraConfigIndexMap extraConfigIndexMap = 46[(tcaplus_field_version) = 397];  // 额外配置索引
  optional int32 extraConfigIndexVerId = 47[(tcaplus_field_version) = 397];  // 额外配置版本id
  optional MapLoadingInfo mapLoading = 48[(tcaplus_field_version) = 401];  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 49[(tcaplus_field_version) = 401];  // 乐园自定义封面
  optional UgcMapMgrInfo mgrInfo = 50[(tcaplus_field_version) = 402];// 地图管理信息（主要来自管理端操作，玩家不会设置）目前只给了锁定状态使用
  optional MapCoverVideoInfoSaved videoInfo = 51[(tcaplus_field_version) = 406];  // 视频封面
  optional CodingDataInfo codingData = 52[(tcaplus_field_version) = 421];	// 共创多人编辑-扣叮模式保存的扣叮数据
}

// 游玩过表  废弃
message MapPlayedList {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=2000";
  option (tcaplus_primarykey) = "creatorId";
  option (tcaplus_splitkey) = "creatorId";
  optional int64 creatorId = 1;          // creatorId
  optional int64 mapId = 2;           // 地图id
}

message DBStepInfo {
  optional int32 stepId = 1; // 阶段ID
  optional int64 recommendMapId = 2; // 推荐的地图ID
  repeated int64 usedMapIds = 3; // 使用过的地图ID列表
  repeated int64 backupMapIds = 4; // 备份的地图ID列表
  optional int64 curMapId = 5; // 当前正在使用的地图ID
  optional int64 battleId = 6; // 开局最后一个用的battleId
  optional bool isPass = 7; // 是否通关
  optional int32 failCount = 8; // 失败次数
  optional int64 startTime = 9; // 开局时间
  optional int32 changeMapCount = 10; // 更换地图的次数
  optional AlgoInfo info = 11;
}

message DBDailyStageStepList {
  repeated DBStepInfo stepInfos = 1; // 地图信息列表
}

message UgcDailyStage {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";

  optional int64 uid = 1;
  optional DBDailyStageStepList stepInfos = 2;  //各个阶段信息
  optional int32 resetCount = 3; // 已经手动重置的次数
  optional int64 lastDayResetTime = 4;  // 上次日重置的时间
  optional int64 roundId = 5;  // 轮次Id
}

message SeqEncryptRandomSegmentInfo {//段映射信息
  optional int64 sourceStartSeq = 1; //源段的起始ID
  optional int64 targetStartSeq = 2; //目标段的起始ID
}

message SeqEncryptRandomSegmentParam {//分段参数
  repeated SeqEncryptRandomSegmentInfo seqSegmentStartList = 1; //每一段开始序号的列表
}

message SeqEncryptScaleOffsetParam {//先缩放再偏移
  optional int64 scale = 1; //缩放比例
  optional int64 offset = 2; //偏移值
}

message SeqEncryptBitMoveInfo {//bitMove信息
  optional int32 sourceBit = 1;
  optional int32 targetBit = 2;
}

message SeqEncryptBitMoveParam {//移位加密参数
  repeated SeqEncryptBitMoveInfo bitMoveList = 1;
}

message SeqEncryptParams {//所有的加密参数
  optional SeqEncryptRandomSegmentParam randomSegmentParam = 1;
  optional SeqEncryptScaleOffsetParam scaleOffsetParam = 2; //缩放
  optional SeqEncryptBitMoveParam bitMoveParam = 3; //位移的信息
  optional int32 version = 4; //版本号，用于对加密参数做修正
}

message SeqRuleTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "seqType";
  option (tcaplus_splitkey) = "seqType";
  option (tcaplus_index) = "index_type:seqType";
  optional int32 seqType = 1; //序号类型
  optional int64 minSeq = 2; //最小序号
  optional int64 maxSeq = 3; //最大序号
  optional int64 increaseSeq = 4; //服务进程每次申请时给的一段序号的数量，每次申请curSeq加increaseSeq
  optional int64 seqScale = 5; //已废弃
  optional int64 seqOffset = 6; //已废弃
  optional int64 curSeq = 7; //当前已分配给服务进程的最大序号
  optional int32 encryptType = 8; //已废弃
  optional string encryptKey = 9; //已废弃
  optional int64 expireTime = 10; //0表示不检查记录使用时间是否超时，一般当记录分配后永远不会被回收时才能设置为0.否则需要设置，当心跳超过这个时间后，就会回收使用的序号
  optional SeqEncryptParams encryptParams = 11[(tcaplus_field_version) = 179]; //加密的类型
}

message SeqSegmentUseFlags {
  repeated bool flags = 1;
}

message SeqSegmentTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "seqType,startSeqId";
  option (tcaplus_splitkey) = "seqType";
  option (tcaplus_index) = "index_type:seqType,startSeqId";
  optional int32 seqType = 1; //序号类型
  optional int64 startSeqId = 2; //段开始序号
  optional int64 endSeqId = 3; //段结束序号
  optional SeqSegmentUseFlags useFlags = 4; //段开始序号后每个序号的使用标记
  optional int64 createTime = 5; //段开始使用的时间
  optional int64 useUpTime = 6; //段用完的时间
  optional int64 lockCheckTime = 7; //正在被某个进程检测
}

message SeqTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "seqType,seqId";
  option (tcaplus_splitkey) = "seqId";
  option (tcaplus_index) = "index_type:seqType,seqId";
  optional int32 seqType = 1;
  optional int64 seqId = 2; //序号Id
  optional string desc = 3; //序号使用方设置的信息
  optional int64 createTime = 4; //序号第一次被使用的时间
  optional string reqSvr = 5;
  optional string allocSvr = 6;
  optional int64 useTime = 7; //序号被使用的时间
  optional int64 lastHeartbeatTime = 8; //被使用序号最后心跳更新时间，需要SeqRuleTable里设置了expireTime才需要心跳续期
  optional int64 freeTime = 9; //序号被释放的时间
  optional int64 cacheTime = 10; //被缓存的时间，扫描记录时会根据这个时间来决定是否再次被缓存
  optional int32 encryptVersion = 11[(tcaplus_field_version) = 183]; //创建序号时使用的加密版本
}

// 游玩表
message PlayerUgcPlay {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,ugcId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";
  optional int64 creatorId = 1;          // creatorId
  optional int64 ugcId = 2;           // 地图id
  optional int64 isPass = 3;          // 是否通关
  optional int64 createTime = 4;      // 游玩时间
  optional int32 sec = 5;             //秒数
  optional string avatar = 6;         //玩家头像
  optional string nickName = 7;       //玩家昵称
  optional int64 playTime = 8[(tcaplus_field_version) = 357];      // 游玩时间
  optional int64 lastEndBattleTime = 9[(tcaplus_field_version) = 378];  // 上次对局结束时间
}

// 全局信息表
message GlobalInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "typeId";
  option (tcaplus_splitkey) = "typeId";
  option (tcaplus_index) = "index_type:typeId";
  optional uint32 typeId = 1;               // 信息类型id, 详情见枚举GlobalInfoType所示
  optional int64 updateTime = 2;            // 数据更新时间
  optional GlobalInfo globalInfo = 3;       // 全局信息
}

// 账号注销任务表
message AccountCancelTaskTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "openId,platId";
  option (tcaplus_splitkey) = "openId";
  option (tcaplus_index) = "index1:openId,platId|index2:openId";
  optional string openId = 1;                 // 注销账号openid
  optional int32 platId = 2;                  // 注销账号platid
  optional int64 uid = 3;                     // 注销账号uid
  optional int32 state = 4;                   // 账号注销任务状态, 详情见枚举AccountCancelTaskState所示
  optional int32 cleanStage = 5;              // 注销账号数据清理阶段
  optional int64 cancelTime = 6;              // 注销账号实际注销时间
  optional int64 cleanCompleteTime = 7;       // 注销账号数据清理完成时间
  optional int64 updateTime = 8;              // 账号注销任务数据更新时
}

// 多语言配置 list
message MultiLanguageConfList {
  repeated MultiLanguageConf multiLanguageConf = 1;
}

// 多语言配置
message MultiLanguageConf {
  optional uint32 useType = 1;                            // 使用类型 enum MultiLanguageConfUseType
  repeated LanguageKv languageKv = 2;
}

// 语言kv
message LanguageKv {
  optional int32 language = 1;                            // 语言id
  optional string content = 2;                            // 内容
}

// 多语言配置表
message MultiLanguageConfTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  option (tcaplus_index) = "index_type:id";
  optional int64 id = 1;                                          // 配置唯一id
  optional MultiLanguageConfList multiLanguageConfList = 2;       // 多语言配置
  optional int64 createTime = 3;                                  // 数据创建时间
}

// 小窝表
message XiaoWo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  //  optional XiaoWoBasicInfo XiaoWoBasicInfo = 2;// 小窝核心信息
  //  optional XiaoWoVisitorInfo XiaoWoVisitorInfo = 3;// 小窝访客信息
  //  // optional XiaoWoMoneyTreeInfo XiaoWoMoneyTreeInfo = 4;// 小窝摇钱树信息
  //  optional XiaoWoLikeInfo XiaoWoLikeInfo = 5; // 小窝点赞信息
  //  optional XiaoWoLevelInfo XiaoWoLevelInfo = 6;// 小窝等级信息
  //  optional XiaoWoStarInfo XiaoWoStarInfo = 7;// 小窝收藏信息
  //  optional XiaoWoInteractInfo XiaoWoInteractInfo = 8;// 小窝交互信息
  //  optional XiaoWoDsInfo XiaoWoDsInfo = 9; // 小窝DS信息
  //  optional XiaoWoHotInfo XiaoWoHotInfo = 10; // 小窝热度信息
  //  optional XiaoWoSafeInfo XiaoWoSafeInfo = 11; // 小窝安全信息
  optional proto_XiaowoAttr XiaowoAttr = 12[(tcaplus_field_version) = 192];
  optional proto_XiaowoAttr ChangedXiaowoAttr = 13[(tcaplus_field_version) = 192];
}

// 分享活动表
message SnsInvitationTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "invitationId";
  option (tcaplus_splitkey) = "invitationId";
  option (tcaplus_index) = "index_type:invitationId";
  optional int64 invitationId = 1;
  optional int32 cfgId = 2;
  optional proto_PlayerSnsInvitationData data = 3;
  optional int64 inviterId = 4;
  optional int32 inviteeNum = 5;
  optional int64 lastInviteeTs = 6;
}

// 全服邮件信息
message GlobalMailData {
  optional com.tencent.wea.xlsRes.GlobalMailConfData confData = 1;    // 全服邮件数据
  optional GlobalMailExtraData extraData = 2;                         // 全服邮件额外数据
  optional int32 globalMailAttachType = 3;                            // 附件类型, 参见 GlobalMailAttachType
  optional MailAttachmentList attachments = 4;
}

// 全服邮件信息表
message GlobalMailInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,worldId,id";
  option (tcaplus_splitkey) = "type";
  option (tcaplus_index) = "index_type1:type|index_type2:type,worldId|index_type3:type,worldId,id";
  optional uint32 type = 1;           // 用于GetByPartKey使用, 占位信息
  optional uint32 worldId = 2;        // 区服id
  optional uint64 id = 3;             // 全服邮件id
  optional uint64 currentTime = 4;    // 数据创建时间
  optional GlobalMailData data = 5;   // 全服邮件数据
}

// 邀请注册关系表
message InviteRegisterTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "openId";
  option (tcaplus_splitkey) = "openId";
  option (tcaplus_index) = "index_type:openId";
  optional string openId = 1;
  optional int64 inviterUid = 2;
  optional string inviterOpenId = 3;
  optional int64 createTimeMs = 4;
  // 通过哪种类型的活动邀请的, 有些活动非游戏侧的, 不会在表里配置, 直接通过类型
  // 就可以决定. 值见枚举ActivityType
  optional int32 activityType = 5[(tcaplus_field_version) = 241];
  optional int32 activityId = 6[(tcaplus_field_version) = 241];
}

// 翻译数据
message DBTranslationData {
  optional int32 type = 1;        // 翻译类型 0.作品名 1.作品描述 2.局内文本
  optional int32 id = 2;          // 局内文本标识ID, 客户端定位到局内的文本位置
  optional int32 state = 3;        // 翻译状态 0.未翻译 1.已翻译 2.翻译中 3.翻译失败
  optional int32 translationTime = 4;    // 翻译时间
  optional int32 tssState = 5;      // 敏感词状态 0.未检测 1.已检测 2.检测未通过
  optional string content = 6;      // 翻译后文本
  optional int32 index = 7;        // 客户端索引(局内文本)
}

// 翻译数据 list
message DBTranslationDataList {
  repeated DBTranslationData dbTranslationData = 1;
}

// UGC局内翻译表
message UGCTranslationDataInGameTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  option (tcaplus_index) = "index_type:id";
  optional string id = 1;                    // 翻译ID 由作品id+语言id拼接而来
  optional int64 ugcId = 2[(tcaplus_field_version) = 212];                    // 地图唯一id
  optional DBTranslationDataList dbTranslationDataList = 3;    // 翻译数据
  optional int32 dataState = 4;                  // 数据状态 0.未拉取到 1.已拉取到
}

// UGC局外翻译表
message UGCTranslationDataOutGameTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  option (tcaplus_index) = "index_type:id";
  optional string id = 1;                    // 翻译ID 由作品id+语言id拼接而来
  optional int64 ugcId = 2[(tcaplus_field_version) = 212];                    // 地图唯一id
  optional DBTranslationDataList dbTranslationDataList = 3;  // 翻译数据
}

message BattleInfoDSCLoadDataTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  option (tcaplus_index) = "index_type:id";
  optional int32 id = 1;  // tbusid
  optional BattleDSCLoadData battleDSCLoadData = 2;    //battlesvr温暖局 非温暖局数量以及dsc统计信息
  optional BattleWarmNormalInfo battleWarmNormalInfo = 3;    //battlesvr温暖局 非温暖局数量
}

message StarPBattleInfoDSCLoadDataTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  option (tcaplus_index) = "index_type:id";
  optional int32 id = 1;  // tbusid
  optional BattleDSCLoadData battleDSCLoadData = 2;    //battlesvr温暖局 非温暖局数量以及dsc统计信息
  optional BattleWarmNormalInfo battleWarmNormalInfo = 3;    //battlesvr温暖局 非温暖局数量
}

//删除记录表
message DeleteBriefRecord {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,ugcId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";

  optional int64 creatorId = 1;
  optional int64 ugcId = 2;  // 类型
  optional int32 instance = 3;  //类型  UgcInstance
  optional string bucket = 4; //bucket
  optional UgcMdList mdList = 5; //md5信息
  optional UgcCommonMapInfo commMap = 6;//地图通用信息
  optional UgcLayerList layers = 7;
  optional string extraParams = 8[(string_size) = 256000];  // 额外参数
  /*
  optional string name = 8;  // 地图名称
  optional int32 templateId = 9; //模板id
  optional int32 saveType = 10; //保存类型
  optional int32 saveCount = 11; // 保存次数
  optional int64 createTime = 12; //创建时间
  optional int64 expireTime = 13; //过期时间
  optional int64 oldUgcId = 14; //旧的ugcId
  optional string desc = 15; //描述
  optional string tags = 16; //tags
  optional int32 reportStatus = 17;  //审核状态
  optional int64 rejectTime = 18;  //销毁时间 驳回后
  optional int32 editorSec = 19;  //编辑时长
  optional int32 isCollect = 20;  // 是否收藏
  optional int64 collectTime = 21; // 收藏时间
  optional UgcExtraInfo extraInfo = 22;   // 额外信息
  optional UgcGroupIdList ugcGroupIdList = 23;
  optional string ugcVersion = 24;         //地图版本号
  optional string clientVersion = 25;      //客户端版本号
  optional UgcMapMetaSaveInfo saveInfo = 26;
  optional UgcMapMetaList metaList = 27;   //更新自核列表
  optional string difficulty = 28;   //地图难度
  optional UgcEditorList editors = 29;
  optional int64 updateTime = 30;//更新时间
  optional UgcPublishDescInfo publishInfo = 31;
 */
}

// idip发货流水表
message IdipSendMailFlowTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "serialNo";
  option (tcaplus_splitkey) = "serialNo";
  option (tcaplus_index) = "index_type1:serialNo";
  optional string serialNo = 1;       // ams发货序列号
  optional string openId = 2;         // 用户账号openid
  optional int32 platId = 3;          // 用户账号平台id
  optional int64 uid = 4;             // 用户账号uid
  optional uint64 currentTime = 5;    // 数据创建时间
  optional uint32 source = 6[(tcaplus_field_version) = 291];         // 流水来源, 1: 道具发货; 2: 端外消费
}

// 样板间管理器表
message SampleRoomAllocatorTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,confId,groupId";
  option (tcaplus_splitkey) = "type";
  option (tcaplus_index) = "index_type:type";
  optional int32 type = 1;                    // 样板间类型，普通样板间是0，防止出现其他类型的样板间（ugc样板间）
  optional int64 confId = 2;                    // 样板间配置id
  optional int64 groupId = 3;                    // 兼容组id
  optional SampleRoomAllocator sampleRoomAllocatorData = 4;  // 管理数据
}

// 小窝方案表
message XiaoWoLayoutTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "layoutId";
  option (tcaplus_splitkey) = "layoutId";
  option (tcaplus_index) = "index_type:layoutId";
  optional int64 layoutId = 1;                    // 方案id
  optional proto_XiaoWoLayoutInfo layoutInfo = 2;  // 方案数据
  optional int64 creatorId = 3;
  optional uint64 saveTime = 4; // 记录时间
}

// 小窝方案发布历史记录表
message XiaoWoLayoutPublishRecordTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "recordId,uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 recordId = 1;                    // 记录id
  optional proto_XiaoWoLayoutPublishRecord recordInfo = 2;  // 记录数据
  optional int64 uid = 3;
  optional uint64 saveTime = 4; // 记录时间
}

// 召集令
message PlayerActivityInteractionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "openId,platId,actId,seq";
  option (tcaplus_splitkey) = "openId,platId";
  option (tcaplus_index) = "index_act:openId,platId,actId|index_user:openId,platId";

  optional string openId = 1; // 账号id
  optional int32 platId = 2; // 平台id
  optional int32 actId = 3; // 活动id
  optional int64 seq = 4; // 交互id
  optional int64 updateTime = 5; // 更新时间
  optional PlayerInteractionData data = 6; // 交互信息
  optional int64 src = 7; // 源
}

// 以玩家uid为索引的Interaction表
message ActivityInteractionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,actId,seq";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_act:uid,actId|index_user:uid";

  optional int64 uid = 1; // 角色
  optional int32 actId = 2; // 活动id
  optional int64 seq = 3; // 交互id
  optional int64 updateTime = 4; // 更新时间
  optional PlayerInteractionData data = 5; // 交互信息
  optional int64 src = 6; // 源
}

// 狼人杀
message WolfKillScoreRecord {
  optional int64 dt = 1;
  optional int32 deltaScore = 2;
  optional int32 totalScore = 3;
  optional int32 reason = 4;
}

message WolfKillScoreRecordArray {
  repeated WolfKillScoreRecord records = 1;
}

message WolfKillSingleReport {
  optional int64 dt = 1;
  optional int64 uid = 2;
  optional int64 battleId = 3;
}

message WolfKillSingleReportArray {
  repeated WolfKillSingleReport singleReports = 1;
}

message WolfKillReport {
  optional int64 dt = 1;
}

message WolfKillReportArray {
  repeated WolfKillReport reports = 1;
}

message WolfKillRoadToMaster {
  //  optional int32 score = 1;
  repeated  WolfKillRoadToMasterReward reward = 2;
}

message WolfKillRoadToMasterReward {
  optional int32 roadToMasterId = 1;
  optional int32 recv = 3; // 是否领取
}

message WolfKillRoleInfo {
  optional int32 roleType = 1;
  optional int32 points = 2;
  optional int32 totalTimes = 3;
  optional int32 winTimes = 4;
  optional int32 mvpTimes = 5;
}

message WolfKillRoleInfoArray {
  repeated WolfKillRoleInfo roleInfos = 1;
}

message WolfKillPassiveReport {
  optional int64 battleId = 1;
  optional int32 passiveTime = 2;
  optional bool isHasSub = 3;
  optional int64 dt = 4;
  repeated int64 playerList = 5;
}

message WolfKillPassiveReportArray {
  repeated WolfKillPassiveReport reports = 1;
}

message WolfKillCampRepeatWin {
  optional int32 campType = 1;
  optional int32 winTime = 2;
}

message WolfKillCampRepeatWinArray {
  repeated WolfKillCampRepeatWin campRepeatWins = 1;
}

message WolfKillComeBackReward {
  optional int32 id = 1;
  optional bool isHasGet = 2;
  repeated int32 itemList = 3;
}

message WolfKillComeBack {
  optional int64 openTs = 1;
  optional int32 unlockCount = 2;
  optional int32 finishCount = 3;
  repeated WolfKillComeBackReward rewardList = 4;
  optional int64 endTs = 5;
}

message WolfKillViolation {
  optional int64 battleId = 1;
  optional int64 ts = 2;
}

message WolfKillViolationArray {
  repeated WolfKillViolation violations = 1;
}

message WolfKillIdentityItemRecord {
  optional int32 identityID = 1;
  optional int64 ts = 2;
}

message WolfKillIdentityItemRecordArray {
  repeated WolfKillIdentityItemRecord records = 1; // 身份最近使用记录（最大保留30天，100个）
  map<int32, int64> lastUseMs = 2; // 上次使用时间
}

message WolfKillShieldVocation {
  optional int32 campType = 1;
  repeated int32 vocationList = 2;
}

message WolfKillShieldVocationArray {
  repeated WolfKillShieldVocation data = 1;
  optional int32 level = 2;
}

message WolfKillMonthCard {
  optional int32 totalDay = 1;
  optional int64 endTs = 2;
  optional int32 freeGiftNum = 3;
  optional int64 getFreeGiftTs = 4;
}

message WolfKillTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";

  required int64 uid = 1;
  optional int32 reputationScore = 2;
  optional WolfKillScoreRecordArray scoreRecord = 3;
  optional WolfKillSingleReportArray singleReportRecord = 4[(tcaplus_field_version) = 306];
  optional WolfKillReportArray reportRecord = 5[(tcaplus_field_version) = 306];
  optional WolfKillRoadToMaster roadToMaster = 6[(tcaplus_field_version) = 351];
  optional WolfKillRoleInfoArray roleInfo = 7[(tcaplus_field_version) = 363];
  optional int32 feedbackCount = 8[(tcaplus_field_version) = 367];
  optional int32 seasonRecvCount = 9[(tcaplus_field_version) = 369];
  optional WolfKillPassiveReportArray passiveReport = 10[(tcaplus_field_version) = 376];
  optional int32 actionScore = 11[(tcaplus_field_version) = 380];
  optional int32 actionScoreInit = 12[(tcaplus_field_version) = 381];
  optional int32 repeatWin = 13[(tcaplus_field_version) = 384];
  optional WolfKillCampRepeatWinArray campRepeatWin = 14[(tcaplus_field_version) = 384];
  optional int32 wolfNewPlayer = 15[(tcaplus_field_version) = 385];
  optional WolfKillComeBack wolfKillComeBack = 16[(tcaplus_field_version) = 386];
  optional int64 latestTs = 17[(tcaplus_field_version) = 387];
  optional WolfKillViolationArray wolfKillViolation = 18[(tcaplus_field_version) = 389];
  optional WolfKillIdentityItemRecordArray identityItemRecord = 19[(tcaplus_field_version) = 411];
  optional WolfKillShieldVocationArray shieldVocation = 20[(tcaplus_field_version) = 411];
  optional WolfKillMonthCard monthCard = 21[(tcaplus_field_version) = 420];
}

message ClubDBBasicInfo {
  repeated int32 labels = 1;          // 社团标签
  optional bool requireApprove = 2;   // 加入需要审批
  optional bool ownerHomeCommon = 3;  // 团长小窝作为公共小窝
  optional string creatorOpenId = 4;
  optional bool denyApply = 5;        // 拒绝所有申请
  optional ChatGroupKey chatGroupKey = 6;
  repeated UserShareMapEntry mapShare = 7;
  optional int64 heatWeek = 8;
  repeated MemberSubscribeInfo subscribeInfo = 9;
  optional int64 lastModifyMs = 10;
  optional int32 dailyModifyCount = 11;
  optional int32 geoLastModifySeason = 12;
  optional bool geoEnabled = 13;
  optional PlayerRankGeoInfo geoInfo = 14;
  optional int64 lastWeekHeat = 15;
  optional bool banHeatRank = 16;
  optional int64 realHeat = 17;
  optional int64 lastWeekRealHeat = 18;
  optional int64 lastDayResetTime = 19;	// 上次日重置时间
  repeated int32 oldLabels = 20;          // 社团标签(旧) 标签合并前的

  message UserShareMapEntry {
    optional int64 uid = 1;
    repeated int64 sharedMapId = 2;
  }

  message MemberSubscribeInfo {
    optional int64 uid = 1;
    repeated ClubEventEntry entries = 2; // 当前的订阅
    repeated ClubEventEntry history = 3; // 已经下发过的记录（用于去重）
  }
}

message ClubChallengeActivityInfo {
  optional int32 challengeId = 1;
  repeated StarLightInfo starLightInfo = 2;
  optional int32 totalStarLight = 3;
}

message ClubChallengeInfo {
  repeated ClubChallengeActivityInfo clubChallengeActivityInfo = 1;
}

message OneActivePlayerInfo {
  optional int64 uid = 1;	// 活跃玩家的UID信息
}

message TodayActivePlayerInfo {
  repeated OneActivePlayerInfo info = 1;	// 活跃玩家的UID信息
}

// 社团信息表
message ClubInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "clubId";
  option (tcaplus_splitkey) = "clubId";
  option (tcaplus_index) = "index_type:clubId";
  optional int64 clubId = 1;
  optional int64 creatorId = 2;
  optional int64 createTime = 3;
  optional int64 instanceId = 4;
  optional string clubName = 5[(tcaplus_field_version) = 250];
  optional int32 clubIcon = 6[(tcaplus_field_version) = 250];
  optional int32 status = 7[(tcaplus_field_version) = 250];
  optional string msdkId = 8[(tcaplus_field_version) = 250];
  optional string title = 9[(tcaplus_field_version) = 250];
  optional int32 heat = 10[(tcaplus_field_version) = 250];
  optional int64 ownerUid = 11[(tcaplus_field_version) = 250];
  optional int32 memberCount = 12[(tcaplus_field_version) = 250];
  optional int32 boyCount = 13[(tcaplus_field_version) = 250];
  optional int32 girlCount = 14[(tcaplus_field_version) = 250];
  optional string brief = 18[(tcaplus_field_version) = 252];
  optional ClubDBBasicInfo basicInfo = 15;
  optional ClubMemberInfo memberInfo = 16;
  optional ClubExtraInfo extraInfo = 17;
  optional ClubMemberInfo applicantInfo = 19[(tcaplus_field_version) = 254];
  optional string msdkAreaId = 20[(tcaplus_field_version) = 255];
  optional string msdkZoneId = 21[(tcaplus_field_version) = 255];
  optional string msdkGroupName = 22[(tcaplus_field_version) = 255];
  optional string creatorOpenId = 23[(tcaplus_field_version) = 255];
  optional string ownerOpenId = 24[(tcaplus_field_version) = 255];
  optional int64 instanceLease = 25[(tcaplus_field_version) = 257];
  optional string labels = 26[(tcaplus_field_version) = 305]; // 平台导出使用
  optional int64 shortId = 27[(tcaplus_field_version) = 331];
  optional ClubChallengeInfo challengeInfo = 28[(db_large_blob)=true, (tcaplus_field_version) = 357];// 社团挑战相关
  optional TodayActivePlayerInfo todayActivePlayerInfo = 29[(tcaplus_field_version) = 373];// 社团挑战相关
  optional ClubActiveScoreInfo activeScoreInfo = 30[(tcaplus_field_version) = 404];  // 社团活跃评分相关
  optional int32 autoJoinNum = 31[(tcaplus_field_version) = 405];  // 一键加入人数
}

// 社团日志表
message ClubLogTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "logId,clubId,logDay";
  option (tcaplus_splitkey) = "clubId";
  option (tcaplus_index) = "index_club:clubId|index_club_day:clubId,logDay|index_club_day_id:clubId,logDay,logId";
  optional int64 logId = 1;
  optional int64 clubId = 2;
  optional int32 logDay = 3;  // 记录日志时间距离日志起始时间经过的天数
  optional int64 logTime = 4;
  optional int32 logType = 5;
  optional int32 logClass = 6;
  optional ClubLogItem logItem = 7;
}

// 玩家的DS中落地属性表
message UserDsDBTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,matchType,slotIdx";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid,matchType,slotIdx";
  optional int64 uid = 1;                         // 存档uid
  optional int32 matchType = 2;                   // 存档matchType
  optional proto_DsUserDBInfo DsUserDBInfo = 3;
  optional int32 slotIdx = 4[(tcaplus_field_version) = 257];					  // 存档位置
  optional int64 updateVersion = 5[(tcaplus_field_version) = 291];					  // 修改版本号（不能越改越小）
  optional int64 battleId = 6[(tcaplus_field_version) = 291];					  // DS战斗id
  optional int64 createTime = 7[(tcaplus_field_version) = 291];					  // 上次创建或修改时间
}

// 跑马灯通知表（废弃）
message MarqueeNoticeTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,noticeId";
  option (tcaplus_splitkey) = "type";
  option (tcaplus_index) = "index_type1:type|index_type2:type,noticeId";
  optional uint32 noticeId = 1;              // 公告id
  optional uint32 areaId = 2;                // 大区（废弃）
  optional uint32 platId = 3;                // 平台 0:IOS 1:Android 2:PC 3:MAC 4:ALL
  optional uint32 partition = 4;             // 小区（废弃）
  optional string imageBackground = 5;       // 样式
  optional uint32 repeatedCnt = 6;           // 轮播次数
  optional uint32 timeInterval = 7;          // 时间间隔
  optional string content = 8;               // 内容
  optional uint64 beginTime = 9;             // 开始时间
  optional uint64 endTime = 10;              // 结束时间
  optional uint32 type = 11[(tcaplus_field_version) = 259];                 // 用于GetByPartKey使用， 占位信息
}

// 跑马灯通知内容
message MarqueeNoticeInfo {
  optional com.tencent.wea.xlsRes.PreWarnMarqueeNoticeConf confData = 1;
}

// 跑马灯通知内容表
message MarqueeNoticeInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,noticeId";
  option (tcaplus_splitkey) = "type";
  option (tcaplus_index) = "index_type1:type|index_type2:type,noticeId";
  optional uint32 type = 1;                  // 用于GetByPartKey使用， 占位信息
  optional uint64 noticeId = 2;              // 公告id
  optional MarqueeNoticeInfo data = 3[(tcaplus_field_version) = 267];       // 配置数据
}

// UGC 资源brief表
message UgcResBrief {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,resType,ugcId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId|index_type2:creatorId,resType";

  optional int64 creatorId = 1;         // 创作者id
  optional int64 ugcId = 2;             // ugcId
  optional int32 resType = 3;           // 资源类型
  optional int32 saveType = 4;          // 保存类型
  optional string name = 5;             // 名称
  optional string desc = 6;             // 描述
  optional int64 createTime = 7;        // 创建时间
  optional UgcMdList mdList = 8;        // metainfo 信息
  optional int64 oldUgcId = 9;          // 草稿ugcId
  optional int32 reportStatus = 10;     // 审核状态
  optional int32 isCollect = 11;        // 是否收藏
  optional int64 collectTime = 12;      // 收藏时间
  optional UgcExtraInfo extraInfo = 13; // 额外信息
  optional string bucket = 14;          // bucket
  optional string ugcVersion = 15;      // ugc版本号
  optional string clientVersion = 16;   // 客户端版本号
  optional int32 resCategory = 17;      // 资源大类别
  optional int32 resSubCategory = 18;   // 资源小类别
  optional string resLabels = 19;       // 资源标签
  optional int32 instanceType = 20;     // 地图类型
  optional int64 updateTime = 21;       //更新时间
  optional UgcPublishDescInfo publishInfo = 22;  // 其它描述信息
  optional int32 isResPubCosPath = 23[(tcaplus_field_version) = 321];  // 私有资源是否在pub路径
  optional int32 realGroupResType = 24[(tcaplus_field_version) = 324]; // 模组类型的真实的资源类型 只针对 UgcResTypeGroup >3的模组类型资源
}

// UGC 资源背包(资源库)
message UgcResBag {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,resType";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";
  optional int64 creatorId = 1;          // 创作者id
  optional int32 resType = 2;            // 资源类型
  optional UgcResBagList resource = 3;   // 数据
  optional int64 lastSyncTime = 4;       // 上次和管理端的同步时间
}

message RedPacketSentRecvDetail {
  optional int64 receiverUid = 1;        // 拾取红包玩家的uid
  optional int64 recvTimeMs = 2;         // 拾取时间
}

message RedPacketData {
  optional int32 packetId = 1;           // 红包配置id
  optional int64 sendTimeMs = 2;         // 红包发放时间
  optional int64 senderUid = 3;          // 发送红包玩家的uid
  repeated RedPacketSentRecvDetail recvDetails = 4;  // 拾取红包玩家的uid
}

// 红包信息表
message RedPacketTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "packetUuid";
  option (tcaplus_splitkey) = "packetUuid";
  option (tcaplus_index) = "index_type:packetUuid";
  optional int64 packetUuid = 1;         // 红包uuid
  optional RedPacketData data = 2;       // 红包数据
  optional int32 placeType = 3[(tcaplus_field_version) = 284];         // enum RedPacketPositionType
  optional int64 placeId = 4[(tcaplus_field_version) = 284];           // 大厅/小窝ID
  optional ObjectPosition position = 5[(tcaplus_field_version) = 284]; // 坐标
}

// 红包雨Opp Interaction计数
message RedEnvelopeRainOppTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "playerUid";
  option (tcaplus_splitkey) = "playerUid";
  option (tcaplus_index) = "index_type:playerUid";
  optional int64 playerUid = 1;         // 操作人uid
  optional int64 dayZeroSec = 2;        // 记录日零点时间戳
  optional int32 oppCnt = 3;            // 插入的数据个数
}

// 广播队列
message MsgQueueChannel {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=1023";
  option (tcaplus_primarykey) = "topic";
  option (tcaplus_splitkey) = "topic";
  optional string topic = 1;            // topic name
  optional bytes content = 2;
  optional int64 src = 3;               // 数据来源
  optional int64 createTime = 4;        // 更新时间
}

message MsgQueueSubscriber {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "topic,id";
  option (tcaplus_splitkey) = "topic";

  optional string topic = 1;              // topic name
  optional int64 id = 2;                  // 订阅身份ID
  optional int32 channelCursor = 3;       // 订阅者消费游标
  optional int32 channelVersion = 4;      // 订阅者消费的版本
}

message AIGCHistoryTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,type";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;         // uid
  optional int32 type = 2;        // 类型
  optional AigcHistoryRecord record = 3;
}

message ClubIdList {
  repeated int64 clubIds = 1;
}

message PlayerToClubTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;
  optional ClubIdList joinedList = 2;
  optional ClubIdList appliedList = 3;
}

// 通用产出模块key
message GeneralOutputModuleKey {
  optional int32 moduleType = 1;  // enum GeneralOutputModuleType
  optional int32 intKey1 = 2;
  optional int32 intKey2 = 3;
  optional int32 intKey3 = 4;
  optional int64 longKey1 = 5;
  optional string strKey1 = 6;
}

// 单个产出key的产出信息记录
message GeneralOutputRecord {
  optional GeneralOutputModuleKey moduleKey = 1;
  repeated PeriodStat periods = 2;  // 兼容多周期的计数。一般只需记录当前周期

  // 单个周期的计数信息
  message PeriodStat {
    optional int64 periodKey = 1;  // 本周期的时间key，一般为周期的开始时间
    optional int32 count = 2; // 周期内的计数
    optional int64 recordTime = 3;  // 周期内的最新记录时间
  }
}

// 单个产出模块的信息
message GeneralOutputModule {
  optional int32 moduleType = 1;  // enum GeneralOutputModuleType, 按 GeneralOutputModuleKey 里的 moduleType 区分模块
  repeated GeneralOutputRecord records = 2;  // 单个模块下的所有产出key的信息
}

message GeneralOutputInfo {
  repeated GeneralOutputModule modules = 1; // 不同的产出模块
}

// 通用产出控制（因splittablekey错误已废弃）
message GeneralOutputControl {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "infoType,uid1,uid2";
  option (tcaplus_splitkey) = "infoType";
  option (tcaplus_index) = "index_type:infoType|index_uid1:infoType,uid1";
  required int32 infoType = 1; // enum GeneralOutputInfoType
  required int64 uid1 = 2;
  required int64 uid2 = 3;
  optional GeneralOutputInfo info = 4;
}

// 通用产出控制新表
message GeneralOutputControlTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "infoType,uid1,uid2";
  option (tcaplus_splitkey) = "uid1";
  option (tcaplus_index) = "index1:uid1|index2:infoType,uid1";
  required int32 infoType = 1; // enum GeneralOutputInfoType
  required int64 uid1 = 2;  // 作为分表因子的数据优先放在uid1
  required int64 uid2 = 3;
  optional GeneralOutputInfo info = 4;
}

message UltramanSquadScore {
  optional int64 uid = 1;
  optional int64 timestamp = 2;
  optional int32 scoreDaySt = 3;
  optional int32 scoreYDaySt = 4;
}

message ScoreData {
  repeated UltramanSquadScore data = 1;
}

// 奥特曼小队任务
message UltramanSquadTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "teamId,type";
  option (tcaplus_splitkey) = "teamId";
  option (tcaplus_index) = "index_type:teamId";
  optional int32 teamId = 1;
  optional int64 leaderUid = 2;
  optional ArrayLong memberList = 3;
  optional ScoreData data = 4;
  optional int32 teamScore = 5;
  optional KVArray kvArray = 6;
  optional int32 type = 7;
}


message UgcCollectionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,collectionId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_creator:creatorId";
  optional int64 creatorId = 1;
  optional string collectionId = 2;
  optional UgcCollectionFace face = 3;
  optional UgcCollectionInfo info = 4;
  optional UgcCollectionMaps maps = 5;
  optional UgcCollectionAdminData adminData = 6;  // 管理端独占
  optional int64 status = 7;                      // 合集不可恢复, 暂不启用

  optional int64 playTimesUv = 1001;
  optional int64 playTimesPv = 1002;
  optional int64 collectTimes = 1003;
}

message GameSettingImage {
  repeated com.tencent.wea.xlsRes.GameSettingConf conf = 1;
}

// 玩家游戏设置保存，预留一个idx字段
message PlayerGameSettingTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,idx";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid,idx|index2:uid";
  optional int64 uid = 1;
  optional int32 idx = 2;
  optional GameSettingImage image = 3;
}

message DataStoreBaseInfo {
  optional int64 totalCostDiamonds = 1;  // 累计消耗星钻
}

message UgcDataStore {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId,uid,saveId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid|index2:uid,ugcId";
  optional int64 ugcId = 1;        // 作品id
  optional int64 uid = 2;          // uid
  optional int32 saveId = 3;       // 存档编号 // 0
  optional string saveName = 4;    // 存档名
  optional int64 createTime = 5;   // 存档创建时间
  optional int64 updateTime = 6;   // 更新时间

  optional UgcDataStorePaidItemData paidItem = 7;                  // 获得的付费道具列表
  optional UgcDataStoreBillNoData recentBillNo = 8;                // 订单号数据
  optional UgcDataStoreBagData bag = 9 [(db_large_blob)=true];     // 背包数据
  optional UgcDataStoreTaskData task = 10 [(db_large_blob)=true];  // 任务数据
  optional UgcDataStoreAttrData attr = 11 [(db_large_blob)=true];  // 属性数据
  optional bytes customKv1 = 12 [(db_large_blob)=true];            // 通用kv
  optional bytes customKv2 = 13 [(db_large_blob)=true];            // 通用kv
  optional bytes customKv3 = 14 [(db_large_blob)=true];            // 通用kv
  optional UgcDataStoreShopData shop = 15 [(db_large_blob)=true, (tcaplus_field_version) = 356];  // 商店数据
  optional int64 dataVersion = 16 [(tcaplus_field_version) = 359];                // 版本号
  optional DataStoreBaseInfo baseInfo = 17 [(tcaplus_field_version) = 366];  // 基本信息 有什么字段塞这里面
}

message UgcPlayerPublic {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId,uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid";
  optional int64 ugcId = 1;        // 作品id
  optional int64 uid = 2;          // Uid
  optional int64 createTime = 3;   // 存档创建时间
  optional int64 updateTime = 4;   // 更新时间
  optional UgcPlayerPublicAttrsData attrsData = 5; //玩家的互动数据存档
  optional UgcPlayerPublicArrayAttrsData arrayAttrsData = 6; //玩家的互动列表存档
  optional UgcPlayerPublicDsData dsData = 7; //玩家所在的DS信息
}

//资产id、地图关联表
message UgcResAssociation {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "resId,ugcId";
  option (tcaplus_splitkey) = "resId";
  option (tcaplus_index) = "index:resId";
  optional int64 resId = 1;
  optional int64 ugcId = 2;
  optional int64 creatorId = 3;
  optional int32 status = 4;
  optional int32 isPrivate = 5;
}

// 弹幕存储
message DanMuTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "danMuId,blockId,recordId,danMuType";
  option (tcaplus_splitkey) = "danMuId";
  option (tcaplus_index) = "index1:danMuId,blockId,danMuType|index2:danMuId,recordId,danMuType|index3:danMuId,danMuType";
  optional int64 danMuId = 1;  // 数据id(ugc中使用地图id)
  optional int64 blockId = 2;  // 区块id
  optional int64 recordId = 3;  // 弹幕id，弹幕唯一索引
  optional int32 danMuType = 4;  // 弹幕类型
  optional DanMuSort danMuSort = 5;  // 弹幕自定义排序规则
  optional DanMuDBAttrInfo danMuDBAttrInfo = 6;  // 弹幕内容
  optional int64 timestamp = 7;  // 发送时间戳
  optional int64 uid = 8;  // 玩家id
  optional int32 like = 9;  // 弹幕点赞信息
}

// 弹幕点赞
message DanMuOptTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "danMuId,blockId,recordId,danMuType,optType,fromUid";
  option (tcaplus_splitkey) = "danMuId";
  option (tcaplus_index) = "index1:danMuId,blockId,danMuType|index2:danMuId,blockId,recordId,danMuType|index3:danMuId,blockId,recordId,danMuType,optType";
  optional int64 danMuId = 1;  // 数据id(ugc中使用地图id)
  optional int64 blockId = 2;  // 区块id
  optional int64 recordId = 3;  // 弹幕id，弹幕唯一索引
  optional int32 danMuType = 4;  // 弹幕类型
  optional int32 optType = 5;  // 操作类型
  optional int64 fromUid = 6;  // 弹幕自定义排序规则
  optional int32 optValue = 7;  // 弹幕内容
}

message ActivityCommonSquadTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "squadId";
  option (tcaplus_splitkey) = "squadId";
  option (tcaplus_index) = "index_type:squadId";
  optional int64 squadId = 1;
  optional int32 activityId = 2;
  optional int64 leaderUid = 3;
  optional ArraySquadMember memberList = 4;
  optional proto_ActivitySquadData data = 5;
  optional LongArray kickUidList = 6[(tcaplus_field_version) = 375];
  optional int64 createTimeMs = 7[(tcaplus_field_version) = 393];
}

message ActivityPlayerTeamTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  option (tcaplus_index) = "index_type:id";

  optional int64 id = 1;                     // 队伍ID
  optional int32 activityId = 2;             // 归属活动
  optional int64 activityEndTime = 3;        // 活动结束时间
  optional int64 createUid = 4;              // 创建者Uid,用于服务路由,不可改变;
  optional int64 createTimeMs = 5;           // 队伍创建时间
  optional int64 leaderUid = 6;              // 队长ID,用于审批权限判断;
  optional ArraySquadMember memberList = 7;  // 成员信息
  optional proto_ActivitySquadData data = 8; // 活动自定义数据
  optional int32 canRemove = 9; 		     // 回收标记 0.不回收 非0.回收
}

message PlayerMapLabelScore {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,ugcId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index1:creatorId";
  optional int64 creatorId = 1;
  optional int64 ugcId = 2;
  optional UgcMapLabelScore info = 3;
}

message UniqueCheckTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,type,uniqueId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid,type";
  optional int64 uid = 1;
  optional int32 type = 2;
  optional string uniqueId = 3;
  optional int64 value = 4;
  optional UniversalData extraData = 5;
}

message PlayerUgcBuyPartnerTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index1:creatorId";
  optional int64 creatorId = 1;
  optional string resOfferId = 2; // 业务资源应用id
  optional string resOfferName = 3; // 资源应用名称
  optional string resOfferLogo = 4; // 资源应用图标
  optional string sandboxTdeaSecretId = 5;
  optional string sandboxTdeaSecretKey = 6;
  optional string tdeaSecretId = 7;
  optional string tdeaSecretKey = 8;
  optional string sandboxSecretKey = 9;
  optional string secretKey = 10;
  optional MidasZoneAndServerData zoneAndSvr = 11;
}

// 农场表
message Farm {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  optional proto_FarmAttr FarmAttr = 12;
  optional proto_FarmAttr ChangedFarmAttr = 13;
}

// 玩家信誉分信息表
message PlayerReputationScoreTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,scoreId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type1:uid,scoreId|index_type2:uid";
  optional int64 uid = 1;										// 玩家uid
  optional int32 scoreId = 2;									// 分数组id(一些玩法映射到一个分数组， 公用同一套信誉分数据)
  optional int64 updateTime = 3;								// 玩家信誉分信息更新时间
  optional int32 reputationScore = 4;                           // 当前分数组id下的信誉分
  optional PlayerPlayModeReputationScoreInfo scoreInfo = 5;		// 玩家信誉分信息
}

message ClubColdRankTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "clubId,type";
  option (tcaplus_splitkey) = "clubId";
  option (tcaplus_index) = "index_type1:clubId|index_type2:clubId,type";

  optional int64 clubId = 1;
  optional int32 type = 2;
  optional ClubHistoryRankInfo coldRankInfo = 3;    // 历史数据，会积累很大，不常用则不拉取
  optional ClubRankInfo lastRankInfo = 4;           // 最后一次结算数据，为空或者时间未更新则说明未上榜
}

// 独立app用户信息表
message AppUserInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "infoKey,typeId";
  option (tcaplus_splitkey) = "infoKey";
  option (tcaplus_index) = "index_type:infoKey,typeId";
  optional string infoKey = 1;                // 信息key
  optional uint32 typeId = 2;                 // 信息类型id, 详情见枚举AppUserInfoType所示
  optional int64 updateTime = 3;              // 数据更新时间
  optional AppUserInfo appUserInfo = 4;       // 独立app用户信息
}

// 玩家商品购买次数同步表
message MallBuyTimesTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,commodityId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type1:uid|index_type2:uid,commodityId";
  optional int64 uid = 1;
  optional int32 commodityId = 2;
  optional proto_BuyRecordStruct commodityBuyRecord = 3;
}

message MatchBattlePlayerRecord {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "playId";
  option (tcaplus_splitkey) = "playId";
  optional int64 playId = 1;  // 战斗id
  optional int64 playTime = 2;  // 战斗时间
  optional MatchBattlePlayerRecordDetail detail = 3;  // 成员信息
}

message PlayerSnsTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;
  optional proto_SnsAttr snsAttr = 2;
  optional int32 instanceId = 11;
  optional int64 instanceLease = 12;
}

// StarP的DSCommon数据表
message StarPDsCommonDBTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "commonId,starPId,commonDBType,commonDBSubType,id,instId";
  option (tcaplus_splitkey) = "commonId";
  option (tcaplus_index) = "index_type:commonId,starPId,commonDBType,commonDBSubType";
  optional int64 commonId = 1;                            // commonId,即玩家的则为uid或工会的则为工会ID或世界产生的则为0
  optional int64 starPId = 2;                             // 啾灵世界ID
  optional int32 commonDBType = 3;                        // 数据类型
  optional int32 commonDBSubType = 4;                     // 默认0,对于背包的数据,这个字段填写玩家出生或复活时生成的背包ID
  optional int32 id = 5;                                  // ID
  optional int64 instId = 6;                              // 实例ID
  optional int64 lastUpdateTime = 7;                      // 最近更新时间
  optional proto_StarPDsCommonDBUserData userData = 8;    // 自定义数据
}

// SP玩家的通用数据
message StarPDsPlayerCommonDBTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,starPId,playerCommonDBType";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid,starPId";
  optional int64 uid = 1;                                       // 玩家uid
  optional int64 starPId = 2;                                   // 啾灵世界ID
  optional int32 playerCommonDBType = 3;                        // 数据类型
  optional int64 lastUpdateTime = 4;                            // 最近更新时间
  optional proto_StarPDsPlayerCommonDBUserData userData = 5;    // 自定义数据
}

// SP世界的通用数据
message StarPDsWorldCommonDBTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,worldCommonDBType";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index_type:starPId";
  optional int64 starPId = 2;                                   // 啾灵世界ID
  optional int32 worldCommonDBType = 3;                         // 数据类型
  optional int64 lastUpdateTime = 4;                            // 最近更新时间
  optional proto_StarPDsWorldCommonDBUserData userData = 5;     // 自定义数据
}

// 地图上和位置相关的一些数据, 支持拉取 某个世界某个区域某种类型(starPId,posId,mapPosCommonDBType) 的位置关联数据
message StarPDsMapPosCommonDBTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,posId,mapPosCommonDBType,commonId";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index1:starPId,posId|index2:starPId,posId,mapPosCommonDBType";
  optional int32 posId = 1;                                     // 所在区域或地图id
  optional int64 commonId = 2;                                  // commonId,不同数据类型有自己的commonId的规范
  optional int64 starPId = 3;                                   // 啾灵世界ID
  optional int32 mapPosCommonDBType = 4;                        // 地图上位置的数据类型
  optional int64 lastUpdateTime = 5;                            // 最近更新时间
  optional proto_StarPDsMapPosCommonDBUserData userData = 6;    // 自定义数据
}

message AppreciateMapTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,mapId,timeIdx";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_time:creatorId,timeIdx|index_creator:creatorId|index_map:creatorId,mapId";
  optional int64 creatorId = 1;
  optional int64 mapId = 2;
  optional int64 timeIdx = 3;
  optional AppreciateMapInfo info = 4;      // 待鉴赏地图的信息
  optional AppreciateMapRecord record = 5;  // 玩家的鉴赏数据
}

message TrainingCampTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,activityId,activityTime";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid,activityId,activityTime";
  optional int64 uid = 1;
  optional int32 activityId = 2;
  optional int64 activityTime = 3;
  optional TrainingCampSportsmanData data = 4; // 运动员数据
  optional TrainingCampAssistData assisted = 5; // 助力数据
  optional TrainingCampAssistData beAssisted = 6; // 被助力数据
}

message TrainingAssistDetailArray {
  repeated TrainingAssistDetail detail = 1;  // 详情数据
}

// 培养活动数据
message TrainingActivityTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,activityId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid,activityId";
  optional int64 uid = 1;
  optional int32 activityId = 2;
  optional TrainingAllObjcetData data = 3; // 训练对象数据
  optional TrainingAssistData assisted = 4; // 助力数据
  optional TrainingAssistData beAssisted = 5; // 被助力数据
  optional TrainingAssistDetailArray assistDetail = 6; // 助力详细信息
  optional TrainingAssistDetailArray beAssistDetail = 7; // 被助力详细信息
}

message FriendHistoryInteractDataTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Uid1,Uid2";
  option (tcaplus_splitkey) = "Uid1";
  option (tcaplus_index) = "index1:Uid1|index2:Uid1,Uid2";
  required int64 Uid1 = 1;
  required int64 Uid2 = 2;
  optional int64 UpdateTime = 3;        // 更新时间
  optional int32 TeamBattleCnt = 4;     // 组队战斗次数
  optional int32 ChatCnt = 5;           // 聊天次数
  optional int32 SendGoldCoinCnt = 6;   // 赠送印章次数
  optional int32 VisitFarmCnt = 7;      // 拜访农场次数
  optional int32 VisitXiaoWoCnt = 8;    // 拜访家园次数
}

message ArenaTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  optional proto_ArenaGameInfo Arena = 2;
  optional int32 instanceId = 4;
  optional int64 instanceLease = 5;
}

// 峡谷英雄统计数据表
message ArenaHeroStatisticsTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "group,heroId,qualifyId,degreeId";
  option (tcaplus_splitkey) = "heroId";
  option (tcaplus_index) = "index_group_hero_qualify_degree:group,heroId,qualifyId,degreeId";
  optional int32 group = 1;                                             // ArenaCombatEffectivenessGroupType (ResKeywords.proto)
  optional int32 heroId = 2;                                            // 英雄ID（J_Arena英雄解锁表）
  optional int32 qualifyId = 3;                                         // P_排位段位&保分配置_Moba 大段位
  optional int32 degreeId = 4;                                          // P_排位段位&保分配置_Moba 小段位ID
  optional int64 updateTimeMs = 5;                                      // 数据更新时间戳
  optional string topPercentileList = 6;                                // 对局表现分统计数据，从前10%分位数到100%分位数，逗号分隔的十个数
}

// 峡谷英雄战斗统计数据表
message ArenaHeroBattleStatisticsTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "heroId,group,qualifyId,degreeId";
  option (tcaplus_splitkey) = "heroId";
  option (tcaplus_index) = "index_hero_group_qualify_degree:heroId,group,qualifyId,degreeId";
  optional int32 heroId = 1; // 英雄Id
  optional int32 group = 2; // ArenaCombatEffectivenessGroupType (ResKeywords.proto)
  optional int32 qualifyId = 3; // P_排位段位&保分配置_MoBa 大段位Id
  optional int32 degreeId = 4; // P_排位段位&保分配置_MoBa 小段位Id
  optional int32 winRate = 5; // 胜率 (百分位后2位小数；例如 12.34% 对应 1234)
  optional int32 appearanceRate = 6; // 出场率 (百分位后2位小数；例如 12.34% 对应 1234)
  optional int32 banRate = 7; // ban率 (百分位后2位小数；例如 12.34% 对应 1234)
  optional string bluePopularCard = 8; // cardId:appearanceRate
  optional string purplePopularCard = 9; // cardId:appearanceRate
  optional string orangePopularCard = 10; // cardId:appearanceRate
}

message UgcMultiVersionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId,mulVersion";
  option (tcaplus_splitkey) = "ugcId";
  option (tcaplus_index) = "index_type:ugcId";
  optional int64 ugcId = 1;
  optional int64 mulVersion = 2;
  optional int64 updateTime = 3;        // 创建时间
  optional int32 status = 4;            //当前版本状态
  optional int64 creatorId = 5;         //creatorId
  optional int32 saveType = 6;       //存档类型
  optional UgcMdList mdList = 7;      //当前版本信息
  optional UgcCosInfo coverList = 8;   //当前版本封面图信息
  optional UgcLayerList layerList = 9;    //多场景信息
  optional ArrayLong resIdList = 10;     //引用资产id
  optional UgcDBGoodsList ugcGoodsList = 11;
  optional int32 ugcInstanceType = 12; //实例类型
  optional string desc = 13; //描述
  optional string tags = 14; //tags
  optional UgcPublishDescInfo descInfo = 15;
  optional int32 pointsNumber = 16; //房间人数
  optional string clientVersion = 17;      //客户端版本号
  optional int32 buyGoodsStatus = 18;    // 购买商品能力开启  可能由于二次修改地图商业化审核不过而被关闭， enum  UgcMapBuyGoodsStatus
  optional int32 templateId = 19; //模板id
  optional int32 editorSec = 20;  //编辑时长
  optional UgcExtraInfo extraInfo = 21;   // 额外信息
  optional UgcGroupIdList ugcGroupIdList = 22;
  optional UgcRankList rankList = 23; // 排行榜相关配置信息
  optional int32 occupancyValue = 51; //地图占用值
  optional int32 propertyScore = 52; //性能评分
  optional int32 actorCount = 53; //actor数量
  optional UgcAchievementMap ugcAchievement = 54[(tcaplus_field_version) = 379];
  optional UgcBestRecord wxRecord = 55[(tcaplus_field_version) = 382]; //微信区
  optional UgcBestRecord qqRecord = 56[(tcaplus_field_version) = 382]; //qq区
  optional UgcVersionPassRecordData versionPassRecordData = 57[(tcaplus_field_version) = 391]; //版本下通关记录数据
  optional int32 evaluationStatus = 58[(tcaplus_field_version) = 394];  // 地图评估状态
  optional UgcMapExtraConfigIndexMap extraConfigIndexMap = 59[(tcaplus_field_version) = 397];  // 额外配置索引
  optional UgcEditorList publishEditors = 60[(tcaplus_field_version) = 400];  // 拥有发布态草稿地图共创权限的共创者
  optional MapLoadingInfo mapLoading = 61[(tcaplus_field_version) = 401];  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 62[(tcaplus_field_version) = 401];  // 乐园自定义封面
  optional UgcPublishInputParam publishParam = 63[(tcaplus_field_version) = 412]; // 发布输入参数
}

// 农场小屋表
message House {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  optional proto_HouseAttr HouseAttr = 12;
  optional proto_HouseAttr ChangedHouseAttr = 13;
}

// 农场表
message Village {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  optional proto_VillageAttr VillageAttr = 12[(db_large_blob) = true];
  optional proto_VillageAttr ChangedVillageAttr = 13[(db_large_blob) = true];
}

// 农场蓝图
message HouseLayoutTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "layoutId";
  option (tcaplus_splitkey) = "layoutId";
  option (tcaplus_index) = "index_type:layoutId";
  optional int64 layoutId = 1;                    // 方案id
  optional proto_HouseLayoutInfo layoutInfo = 2;  // 方案数据
  optional int64 creatorId = 3;
  optional uint64 saveTime = 4; // 记录时间
}

message UgcBlobDataTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId,dataType";
  option (tcaplus_splitkey) = "ugcId,dataType";
  option (tcaplus_index) = "index_type:ugcId,dataType";
  optional int64 ugcId = 1;
  optional int32 dataType = 2;
  optional string blobData = 3;
}

// 玩家额外信息表(适用于存储玩家下不适合放在userattr里面存储的比较大的结构体数据)
message PlayerExtraInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             		  // 用户uid
  optional int64 updateTime = 2;			      // 数据更新时间
  optional proto_AlbumInfo albumInfo = 3;	  // 个人相册信息
  optional proto_AlbumLikeHisInfo albumLike = 4[(tcaplus_field_version) = 383];//个人相册点赞历史信息
  optional proto_AlbumExtInfo albumExtInfo = 5[(tcaplus_field_version) = 401];//个人相册扩展信息
}

// 需要考虑多版本 草稿与发布
message UgcAchievementTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId,achId,verId";
  option (tcaplus_splitkey) = "ugcId";
  option (tcaplus_index) = "index1:ugcId,achId|index2:ugcId,verId";
  optional int64 ugcId = 1;  // 地图id
  optional int32 achId = 2;  // 成就id
  optional int32 verId = 3;  // 版本id
  optional UgcAchievementConf conf = 4;  // 配置 只有类型为配置的时候会填充
}

message TradingCardCollectionData {
  repeated proto_TradingCardInfo cardList = 1;
  optional int64 lastUpdateTimeMs = 3;                  // 上次更新时间
  repeated int32 rewardedDeckIdList = 4;                // 已领奖的卡组
  optional bool reward = 5;                            // 卡集是否已领奖
}

// 集卡系统
message TradingCardHistoryTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,collectionId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;                       // 玩家uid
  optional int32 collectionId = 2;              // 卡集id
  optional TradingCardCollectionData data = 3;  // 卡集数据
}

// 交易请求
message TradingCardTradeTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  optional int64 id = 1;                        // 交换id
  optional TradingCardTradeInfo data = 2;       // 交易数据
}

// 集卡指令
message TradingCardInteractionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id,destUid";
  option (tcaplus_index) = "index1:destUid";
  option (tcaplus_splitkey) = "destUid";
  optional int64 id = 1;                          // 指令id
  optional int64 destUid = 2;                     // 目标uid
  optional TradingCardInteractionData data = 3;   // 数据
}

// 集卡交易记录
message TradingCardTradeRecordTable {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=100";
  option (tcaplus_primarykey) = "uid,collectionId,tradeType";
  option (tcaplus_splitkey) = "uid";
  optional int64 uid = 1;                       // 玩家uid
  optional int32 collectionId = 2;              // 卡集id
  optional int32 tradeType = 3;                 // 交易类型
  optional int64 tradeId = 4;                   // 交易id
  optional TradingCardTradeInfo storeInfo = 5;  // 交易结束后保存信息
}

// ds展示用数据存储
// 只适用于仅展示的数据，不能以此数据作为正常数据来源，若必须使用需要进行校验
// 这里的数据来自客户端单局及ds内，所以数据安全，正确性无法完全保证
// 这里数据按一级字段更新，一级字段包乐观锁若某级数据版本错误不去影响其他字段
message UgcShowDataStoreTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ugcId,uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 ugcId = 1;        // 作品id
  optional int64 uid = 2;          // uid
  optional int64 createTime = 3;   // 创建时间

  optional UgcShowDataStoreWrapper achWrapper = 4;  // 属性
}

//玩家关卡评价
message PlayerEstimationTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,levelId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;                        // uid
  optional int32 levelId = 2;                    // 关卡id
  optional int32 EstimationOptions = 3;          // 踩中赞
  optional string OptionsTextArray = 4;          // 踩中赞对应的文本
  optional string SelfEstimationText = 5;        // 自输入评价
}
// coc player表
message CocPlayer {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  optional proto_CocUserAttr cocUserAttr = 12;
  optional proto_CocUserAttr changedCocUserAttr = 13;
}


message UgcMapExtraConfigTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uniqueId,cfgType,verId,cfgId";
  option (tcaplus_splitkey) = "uniqueId";
  option (tcaplus_index) = "index1:uniqueId,cfgType|index2:uniqueId,verId";
  optional int64 uniqueId = 1;  // 唯一id
  optional int32 cfgType = 2;  // 配置类型
  optional int32 cfgId = 3;  // 配置id
  optional int32 verId = 4;  // 版本id
  // 所有配置的包装信息
  optional UgcMapExtraConfigWrapper wrapper = 5;  // 配置包装信息
}

//赛季回顾
message SeasonReviewTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,seasonId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;         // 玩家uid
  optional int32 seasonId = 2;    // 赛季id
  optional SeasonReviewBasicData basicData = 3;   // 来源于已有属性
  optional proto_SeasonReview eventData  = 4;      // 带进度的事件数据
  optional int64 updateTimeMs = 6;
}

//赛季回顾TLog支持数据
message SeasonReviewTLogTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,seasonId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;         // 玩家uid
  optional int32 seasonId = 2;    // 赛季id
  optional int64 manorVisitMostFriendUid = 3;                 // 访问我的星家园次数最多的好友
  optional int64 socialGiftSendCnt = 4;                         // 当前赛季玩家赠礼次数
  optional int64 socialTeamMostFriendUid = 5;                  // 当赛季组队次数最多的好友
  optional int64 farmLevel = 6;                                   // 赛季末的等级
  optional int64 farmMaxBalance = 7;                             // 当前赛季农场最大农场币数量最大记录
  optional int64 farmVisitCnt = 8;                               // 当前赛季访问好友农场次数
  optional int64 farmMostHarvestCropId = 9;                    // 当前赛季在我的农场收获次数最多的作物id
  optional int64 farmCoinReduce = 10;                            // 当前赛季消耗的农场币数量
  optional int64 farmCoinAdd = 11;                               // 当前赛季获得的农场币数量
  optional int64 farmCommonHarvestCnt = 12;                     // 当前赛季农场进行收获的次数（含动植物）
  optional int64 farmSmallHarvestCnt = 13;                      // 当前赛季农场收获时的丰收次数（含动植物）
  optional int64 farmGreatHarvestCnt = 14;                      // 当前赛季农场收获时的大丰收次数（含动植物）
  optional int64 farmStealCnt = 15;                              // 当前赛季玩家进行偷菜的次数  （动植物+钓鱼）
  optional int64 farmActDays = 16;                               // 当赛季玩家进入农场的天数
  optional int64 ugcCreatorLevelTitle = 17;                     // 当前赛季的造梦师等级
  optional int64 ugcGoldTrophyCnt = 18;                         // 当前赛季的金奖杯数量
  optional int64 ugcSilverTrophyCnt = 19;                       // 当前赛季的银奖杯数量
  optional int64 ugcCopperTrophyCnt = 20;                       // 当前赛季的铜奖杯数量
  optional int64 ugcAddSubscribeCnt = 21;                       // 当前赛季我的新增订阅数
  optional int64 ugcAddFansCnt = 22;                            // 当前赛季我的新增粉丝数
  optional int64 ugcAddLikeCnt = 23;                            // 当前赛季我的新增点赞数
  optional int64 ugcParadeScore = 24;                            // 当前赛季我的挑战积分
  optional int64 ugcEnjoyMapCnt = 25;                           // 当前赛季游玩的地图总数量
  optional int64 ugcReleaseMapCnt = 26;                         // 当前赛季我发布的地图总数量
  optional int64 ugcPassMapCnt = 27;                            // 当前赛季我通关的地图总数量
  optional int64 ugcMostPlayerReleaseMapId = 28;               // 当前赛季我发布的地图中玩家(游玩次数+点赞次数)最多的地图
  optional int64 ugcMostPlayerEnjoyMapId = 29;                 // 当前赛季所有地图中玩家游玩(次数+点赞)次数最多的地图
  optional int64 ugcMostEnjoyMapId = 30;                        // 当前赛季我游玩数量最多的地图
  optional int64 updateTimeMs = 31;

}



// 分享礼包详细结构
message ShareGiftPlayerTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id";
  option (tcaplus_splitkey) = "id";
  optional int64 id = 1;                        // 分享礼包id
  optional ShareGiftBaseInfo data = 2;             // 分享礼包数据
}

// 分享礼包历史记录
message ShareGiftPlayerHistoryTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,type,historyType,shareGiftUid,createMs";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid,type,historyType";
  optional int64 uid = 1;
  optional int32 type = 2;  //分享礼包类别
  optional int32 historyType = 3; //历史记录类别
  optional int64 shareGiftUid = 4;//礼包id
  optional int64 createMs = 5;//创建时间
  optional int64 showExpireTimeMs = 6;//过期时间
  optional int64 dbExpireTimeMs = 7;//tcaplus过期时间
}

// 历史点击过发现的玩家-精准引导发现系统功能用
message DiscoverClickedFoundTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;
  optional int32 flag = 2;
}

// ainpc基础表
message AiNpcTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;
  optional proto_AiNpcAttr aiNpcAttr = 2;
  optional int32 instanceId = 11;
  optional int64 instanceLease = 12;
}

// ainpc聊天记录
message AiNpcChatHistoryTable {
  option (tcaplus_customattr2) = "TableType=LIST;ListNum=500";
  option (tcaplus_primarykey) = "uid,sessionId";
  option (tcaplus_splitkey) = "uid";
  optional int64 uid = 1;                       // 玩家uid
  optional int64 sessionId = 2;                 // 会话id(单聊为npcId,群聊为groupId)
  optional AiNpcChatRecord chatInfo = 3;        // 聊天记录
}

// 转区账号信息
message TransferAccountInfo {
  optional int32 platId = 1;                  // 转区账号原始platid
  optional int32 targetPlatId = 2;            // 转区账号目标platid
  optional int64 uid = 3;                     // 转区账号uid
}

message TransferAccountInfoArray {
  repeated TransferAccountInfo transferAccountInfo = 1;
}

// 账号转区任务表
message AccountTransferTaskTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "openId";
  option (tcaplus_splitkey) = "openId";
  option (tcaplus_index) = "index1:openId";
  optional string openId = 1;                 // 转区账号openid
  optional int64 uid = 2;                     // 发起转区申请的uid
  optional int32 state = 3;                   // 账号转区任务状态, 详情见枚举AccountTransferTaskState所示
  optional TransferAccountInfoArray transferAccountInfoArray = 4; // 转区账号信息列表
  optional int32 transferStage = 5;              // 转区账号数据修改阶段
  optional int64 transferTime = 6;              // 转区账号数据修改阶段完成时间
  optional int64 transferStartTime = 7;         // 转区账号实际开始时间
  optional int64 transferCompleteTime = 8;       // 转区账号实际结束时间
  optional int32 svrId = 9;                    // 转区登录锁svrId
}

message PlayerActivityInteractionData {
  repeated PlayerActivityInteractionTable playerActivityInteractionTable = 1;	// PlayerActivityInteractionTable表备份
}

// 账号转区备份表
message AccountTransferBackUpTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  optional int64 backUpTime = 2; // 备份时间,14天内为上一次失败重试，14天后是下一次转区
  optional OpenIdToUid openIdToUid = 3; //OpenIdToUid表备份
  optional OpenIdToCreatorId openIdToCreatorId = 4; //OpenIdToCreatorId表备份
  optional PlayerActivityInteractionData playerActivityInteractionData = 5; //PlayerActivityInteractionTable表备份数据
  optional Player player = 6[(tcaplus_field_version) = 418]; //玩家数据备份
  optional PlayerPublic playerPublic = 7[(tcaplus_field_version) = 418]; //玩家公共数据备份
  optional IdipBanInfo idipBanInfo = 8[(tcaplus_field_version) = 418]; //idip封禁信息备份
//  optional RelationTable relationTable = 9; //关系表备份
//  optional UgcPlayerInfo ugcPlayerInfo = 10; //玩家UGC数据备份
}

// 大王身份专精统计数据表
message ChaseIdentityStatisticsTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "identityId,qualifyId,degreeId";
  option (tcaplus_splitkey) = "identityId";
  option (tcaplus_index) = "index_identityId_qualify_degree:identityId,qualifyId,degreeId";
  optional int32 identityId = 1;                                            // 专精ID（Z_大王别抓我身份专精分表）
  optional int32 qualifyId = 2;                                         // P_排位段位&保分配置_chase_大王别抓我 大段位
  optional int32 degreeId = 3;                                          // P_排位段位&保分配置_chase_大王别抓我 小段位ID
  optional int64 updateTimeMs = 4;                                      // 数据更新时间戳
  optional string topPercentileList = 5;                                // 对局表现分统计数据，从前10%分位数到100%分位数，逗号分隔的十个数
}

// Ugc创作者徽章
message UgcCreatorBadgeTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "creatorId,badgeId";
  option (tcaplus_splitkey) = "creatorId";
  option (tcaplus_index) = "index_type:creatorId";
  optional int64 creatorId = 1;
  optional int32 badgeId = 2;
  optional int64 addTime = 3;
}
message AnalyzeDataTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "dataKey,module";
  option (tcaplus_splitkey) = "dataKey";
  option (tcaplus_index) = "index_type:dataKey";

  optional string dataKey = 1;   // 数据key
  optional string module = 2;    // 模块
  optional string dataJson = 3[(string_size) = 128000];// 数据json
  optional int64 updateTime = 4; // 更新时间
  optional bytes protoData = 5[(tcaplus_field_version) = 413]; // 对应proto的字节数组

}

message FarmDailyAwardsBuyCountTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;         // 玩家uid
  optional int64 itemId = 2;    // 道具id
  optional int32 nums = 3;    // 单次购买数       // 废弃
  optional int32 farmDailyAwardsBuyCount = 4;   // 农场天天领总购买数
  optional int64 updateTimeMs = 5;
}

// 农场餐厅表
message Cook {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid = 1;             // uid
  optional proto_CookAttr CookAttr = 12;
  optional proto_CookAttr ChangedCookAttr = 13;
}

//FBX 任务表
message FBXInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "fbxId";
  option (tcaplus_splitkey) = "fbxId";
  option (tcaplus_index) = "index_type:fbxId";
  optional int64 fbxId = 1;             // fbxId
  optional string md5 = 2;
  optional int64 creatorId = 3;
  optional string param = 4;
  optional string bucket = 5;
  optional FBXInfo infos = 6;
}
// 啾灵玩家账号表-外围
message StarPPlayerTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type:uid";
  optional int64 uid                                = 1; // 玩家uid
  optional proto_StarPPlayerAttr playerAttr         = 2; // 玩家账号数据
  optional proto_StarPPlayerAttr changedPlayerAttr  = 3; // 玩家账号数据
}

// 啾灵世界表-外围
message StarPTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index_type:starPId";
  optional int64 starPId                    = 1;  // starPId
  optional proto_StarPAttr starPAttr        = 2;
  optional proto_StarPAttr changedStarPAttr = 3;	//支持增量写入, 暂时不支持可退化为全量
}

// SP公会数据
message StarPDsGuildTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,guildId";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index_type:starPId";
  optional int64 starPId = 1;                                   // 啾灵世界ID
  optional int64 guildId = 2;                                   // 工会Id
  optional int64 lastUpdateTime = 3;                            // 最近更新时间
  optional proto_StarPDsGuildDBUserData guildData = 4;
}

// SP公会成员数据
message StarPDsGuildMemberTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,guildId,uid";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index_type:starPId,uid";
  optional int64 starPId = 1;                                   // 啾灵世界ID
  optional int64 guildId = 2;                                   // 工会Id
  optional int64 uid = 3;                                       // 玩家id
  optional int64 lastUpdateTime = 4;                            // 最近更新时间
}

// SP全局公会名字表
message StarPGuildNameTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "guildName";
  option (tcaplus_splitkey) = "guildName";
  option (tcaplus_index) = "index1:guildName";
  optional string guildName = 1;                                // 公会名
  optional int32 isAllocated = 2;                               // 废弃
  optional int32 suffix = 3;                                    // 废弃
  optional int64 starPId = 4;                                   // 公会所属房间
  optional int64 guildId = 5;                                   // 公会ID
}

message StarPDsGuildGVGTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,guildId";
  option (tcaplus_splitkey) = "starPId";
  optional int64 starPId = 1;                                   // 啾灵世界ID
  optional int64 guildId = 2;                                   // 工会Id
  optional proto_StarPDsGuildGVGData guildGVGData = 3;          // 工会GVG相关数据
}

// SP房间简略信息
message StarPPublicInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index_type:starPId";
  optional int64 starPId = 1;      // 啾灵世界ID
  optional proto_StarPPublicInfo publicInfo = 2; // 房间简略信息
}

// SP物品数据表
message StarPDsItemTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,backPackId,id,instId";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index_type:starPId,backPackId";

  optional int64 starPId = 1;                             // 世界ID
  optional int64 backPackId = 2;                          // 背包ID
  optional int32 id = 3;                                  // 物品ID
  optional int64 instId = 4;                              // 物品实例ID
  optional int64 lastUpdateTime = 5;                      // 上次创建或修改时间
  optional int32 itemType = 6;                            // 背包物品类型
  optional int32 backPackPos = 7;                         // 物品在背包里的格子位置
  optional int64 num = 8;                                 // 物品数量
  optional int32 period = 9;                              // 物品有效时间
  optional int64 obtainTime = 10;                         // 获得时间
  optional proto_StarPItemUserDataUnion itemData = 11;    // 自定义数据
  optional int32 isLock = 12;                             // 是否加锁
}


// SP 离线消息表
message StarPInteractionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "id,type,uuid";
  option (tcaplus_index) = "index1:type,uuid";
  option (tcaplus_splitkey) = "uuid";

  required int64 id   = 1; // 唯一自增id
  required int64 type = 2; // db类型(维度),参考枚举common#StarPInteractionType
  required int64 uuid = 3; // 根据db类型动态变化(玩家roleId/工会id/other id)
  optional SPInteractionData body = 4; // 消息体
  optional int64 recvTime = 5;
}

message IdipDebugdsSerialTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,typeId,serialId";
  option (tcaplus_index) = "index1:starPId,typeId,serialId";
  option (tcaplus_splitkey) = "starPId";

  required int64 starPId = 1;
  required int64 typeId = 2;
  required string serialId = 3;
  optional int64 recvTime = 4;
}

// SP 邮件
message StarPMailTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "ownerId,starPId,mailId";
  option (tcaplus_splitkey) = "ownerId";
  option (tcaplus_index) = "index1:ownerId|index2:ownerId,starPId|index3:ownerId,starPId,mailId";
  required string ownerId = 1;                          // 所有者ID，可能是角色ID，账号，...
  required int64 starPId = 2;                           // SP 世界ID, 当这个字段为0时，表示这个邮件在ownerId的所有SP世界中共享
  required int32 mailType = 3;                          // 邮件类型 enum StarPMailType
  required string mailId = 4;                           // 邮件（实例）ID
  required string cfgId = 5;                            // 邮件配置ID
  required int32 status = 6;                            // 邮件状态 enum StarPMailStatus
  required int32 sourceType = 7;                        // 邮件来源 enum StarPMailSourceType
  required int64 sendTime = 8;                          // 邮件创建（UTC）时间
  required int64 expireTime = 9;                        // 邮件过期（UTC）时间
  optional string customTitle = 10;                     // 邮件自定义标题
  optional int32 senderType = 11;                       // 邮件发送者类型
  optional int64 senderId = 12;                         // 邮件发送者ID
  optional string senderName = 13;                      // 邮件发送者名字
  optional string customContent= 14[(string_size) = 65536]; // 邮件自定义内容
  optional proto_StarPMailArgsValues argsValues = 15;   // 邮件参数值
  optional proto_StarPMailAttachments attachments = 16; // 邮件附件
  optional int32 isSending = 17;                        // 邮件是否在发送中，用于发送幂等性检查，逻辑不用管这个值
  optional string hintId = 18;                          // 线索ID，用于幂等检测
  required proto_StarPMailExtraData extraData = 100;    // 邮件额外数据
}

// SP邮件邮件线索表
message StarPMailHintTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "hintId";
  option (tcaplus_splitkey) = "hintId";
  option (tcaplus_index) = "index_type1:hintId";
  required string hintId = 1;
  required string ownerId = 2;
  required int64 starPId = 3;
  required int32 mailType = 4;
  required string mailId = 5;
  required int64 sendTime = 6;
}

// 玩家组织映射表 记录uid->BaseGroupid
message StarPUid2BaseGroupTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,baseGroupType";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type1:uid";
  required int64 uid = 1;                         // 账号uid
  required int32 baseGroupType = 2;               // 组织类型
  required int64 baseGroupId = 3;                 // 组织Id
  optional proto_StarPBaseGroupSimpleInfo baseGroupSimpleInfo = 4;  // 组织简明信息
  required int32 status = 5;                      // 当前状态
  optional int64 lastStatusTime = 6 [(tcaplus_field_version) = 386];              // 最近一次变更状态的时间
}

// 组织表
message StarPBaseGroupTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "baseGroupId";
  option (tcaplus_splitkey) = "baseGroupId";
  option (tcaplus_index) = "index_type1:baseGroupId";
  required int64 baseGroupId = 1;                 // 组织Id
  required int32 baseGroupType = 2;               // 组织类型
  optional proto_StarPBaseGroupDataInfo dataInfo = 3;  // 组织信息
}

// 组织成员表
message StarPBaseGroupMemberTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "baseGroupId,uid";
  option (tcaplus_splitkey) = "baseGroupId";
  option (tcaplus_index) = "index_type1:baseGroupId";
  required int64 baseGroupId = 1;                 // 组织Id
  required int32 baseGroupType = 2;               // 组织类型
  required int64 uid = 3;                         // 账号uid
  optional proto_StarPBaseGroupMemberDataInfo memberDataInfo = 5; // 成员信息
}

// 玩家组织邀请和申请信息
message StarPUidBaseGroupInvitedTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,baseGroupType";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index_type1:uid";
  required int64 uid = 1;                         // 账号uid
  required int32 baseGroupType = 2;               // 组织类型
  optional proto_StarPBaseGroupApplicationData applicationData  = 3; // 申请信息
  optional proto_StarPBaseGroupInvitationData invitationData    = 4; // 邀请信息
}

// 组织
message StarPBaseGroupNameTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "baseGroupName,baseGroupType";
  option (tcaplus_splitkey) = "baseGroupName";
  option (tcaplus_index) = "index_type1:baseGroupName";
  required string baseGroupName = 1;               // 组织名字
  required int32 baseGroupType = 2;                // 组织类型
  required int64 id = 3;
  required int32 isAllocated = 4;
}

// Ds数据存储(仅仅与dsId相关, dsId可以是长生命周期ds, 也可以是短生命周期ds, 对于长生命周期ds来说， dsId不与gameSessionId绑定)
message DsDbInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "dsId,slotId,slotKey";
  option (tcaplus_splitkey) = "dsId";
  option (tcaplus_index) = "index1:dsId|index2:dsId,slotId|index3:dsId,slotKey";
  required int64 dsId = 1;          //battleid、lobbyid等
  optional int64 slotId = 2;        //自定义key, 整型
  optional string slotKey = 3;      //自定义key, 字符串
  optional int32 dsType = 4;        //识别数据是否为自己的, 避免误读
  optional int32 matchType = 5;     //识别数据是否为自己的, 避免误读
  optional int64 dsVersion = 6;     //ds版本号, 禁止低版本ds存档和加载
  optional bytes dsDbAttr = 7;      //全量写入
  optional bytes changedDsDbAttr = 8;	//支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 9;  // 上次创建或修改时间
  optional int32 serverId = 10;
}

// Ds玩家数据存储(属于玩家的自身数据部分, 与matchType相关的数据, 即使dsId不同或者销毁, 数据依旧保留)
message DsUserDbInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uid,matchType,dsType,slotId";
  option (tcaplus_splitkey) = "uid";
  option (tcaplus_index) = "index1:uid,matchType|index2:uid,matchType,dsType";
  required int64 uid = 1;                // 存档uid
  required int32 matchType = 2;         // 识别数据是否为自己的, 避免误读
  optional int64 slotId = 3;					  // 如果玩家数据与dsId绑定就填dsId(battleid、lobbyid等), 如果仅仅与玩法绑定，填0就行
  optional int64 dsVersion = 4;					  // ds版本号, 禁止低版本ds存档和加载
  optional bytes userDsDbAttr = 5;        //全量写入
  optional bytes changedUserDsDbAttr = 6; //支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 7;					  // 上次创建或修改时间
  optional int32 serverId = 8;
  optional int32 dsType = 9;        //识别数据是否为自己的, 避免误读
}

message DsCommonDbInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonNoCacheTable {
	option (tcaplus_customattr2) = "TableType=GENERIC";
	option (tcaplus_primarykey) = "id,type,extend1,extend2";
	option (tcaplus_index) = "index1:id,type|index2:id,type,extend1|index3:id,type,extend1,extend2";
	option (tcaplus_splitkey) = "id";
	required int64 id 						= 1;         // 玩家uid, starpId等
	optional int64 type 					= 2;		 // 类型  (common.proto中的StarPCommonNoCacheType)
	optional int64 extend1					= 3;		 // 扩展字段1
	optional int64 extend2					= 4;		 // 扩展字段2
	optional bytes commonDsNoCacheAttr 		= 5;         // 存储数据
}

message DelUserNoticeInfo {
    optional int64               starPId   = 1;           // 啾灵世界ID
    optional int64               delUid    = 2;           // 被删除用户uid
    optional int64               optUid    = 3;           // 操作用户uid
    optional int32               optTime   = 4;           // 操作时间秒
    optional string              sessionId = 5;           // 操作时的Ds sessionid，如果当时不在DS房间中可以为空
}

// StarP事件通知内容
message StarPNoticeInfo {
  optional  DelUserNoticeInfo delUserNoticeInfo = 1;     // 用户角色删除事件通知信息
}

// StarP事件通知内容表
message StarPEventNoticeTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "starPId,uid,eventTypeId,updateTimeMs";
  option (tcaplus_splitkey) = "starPId";
  option (tcaplus_index) = "index_type1:starPId|index_type2:starPId,uid|index_type3:starPId,uid,eventTypeId|index_type4:starPId,uid,eventTypeId,updateTimeMs";
  required int64 starPId = 1;                             // 世界ID
  optional int64 uid = 2;                                 // 用户id
  optional int32 eventTypeId = 3;                         // 事件类型id，1 delUser; 2 xx
  optional int64 updateTimeMs = 4;                        // 上次创建或修改时间
  optional StarPNoticeInfo data = 5;                      // 事件数据
}
// DSR 会话数据
message DsrGameSession {
  required string gameSessionId = 1;    // 单局会话id
  optional bool notifyFailed = 2;       // 是否通知失败
  optional int32 subscriber = 3;        // 订阅者地址
  optional string aliasId = 4;          // 单局alias id
  optional int32 status = 5;            // 状态
  optional int64 createTimeSec = 6;     // 创建时间戳秒数
  optional string dsaServiceName = 7;   // 所在DSA的ServiceName
  optional uint64 dsaServiceId = 8;     // 所在DSA的ServiceId
}

// DSR DS 会话数据
message DsrDsGameSessionData {
  repeated DsrGameSession sessions = 1; // 会话列表
}

// DSR DS会话表
message DsrDsGameSessionTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "dsId";
  option (tcaplus_splitkey) = "dsId";
  required string dsId = 1;                       // DS id，对于啾灵是worldId，对于主玩法对局就是battleId
  required DsrDsGameSessionData sessionData = 2;  // 会话数据
}

message StarPGlobalAchieveDataTable{
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "achieveId";
  option (tcaplus_splitkey) = "achieveId";
  required int32 achieveId = 1;
  optional int64 finishCnt = 2;
  optional int64 firstFinishCnt = 3;
}

// clone表中uid的映射, 该表只在tcaplus starpclone分区中使用
message StarPCloneUidMap{
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "srcStarPId,srcUid";
  option (tcaplus_splitkey) = "srcStarPId";
  option (tcaplus_index) = "index1:srcStarPId|index2:srcStarPId,srcUid";
  required int64 srcStarPId = 1; // 模板号
  required int64 srcUid = 2;     // 模板号下的uid
  required int64 mapStarPId = 3; // 模板号对应的starpid
  required int64 mapUid = 4;     // 模板号该uid对应的uid
}

// 存档自定义名字到模板号的定义
message StarPNameTemplateMap{
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "savedName";
  option (tcaplus_splitkey) = "savedName";
  option (tcaplus_index) = "index1:savedName";
  required string savedName = 1; // 模板的名字
  required int64 templateId = 2; // 模板号
}

message StarPRoomInfo {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "roomId";
  option (tcaplus_splitkey) = "roomId";
  required int64 roomId = 1;
  optional int64 leaderId = 2;
  optional MemberInfoList memList = 3;
  optional int32 roomStatus = 4;                          // 废弃
  optional MatchRuleInfo matchRule = 5;
  optional int32 teamType = 6;
  optional ChatGroupKey chatGroupKey = 7;
  optional int64 battleId = 8;
  optional KeyValueInfoList keyValueInfoMap = 9;
  optional KeyValueInfoList beInvitedMap = 10;
  optional KeyValueInfoList want2JoinPlayerInfoMap = 11;
  optional int64 curStateStartTime = 12;                  // 废弃
  optional int64 activeTime = 13;
  optional int32 matchSvrID = 14;
  optional KeyValueInfoList kickPlayerList = 15;
  optional int32 roomType = 16;                                               // common.proto中的RoomType
  optional RoomState state = 17;              // 当前状态信息，便于节点恢复
  optional RoomDisplayInfo displayInfo = 18;  // 外显信息
  optional int32 maxMemberLimit = 19;         // 最大人数限制
  optional RoomUgcInfo ugcInfo = 20;          // ugc附加信息
  optional CompetitionElimRoomInfo compElimRoomInfo = 21; // 赛事-淘汰赛房间信息
  optional int32 roomVersion = 22; //用来做数据兼容
  optional RoomCreateOptions roomCreateOpts = 23; // 创建参数（内含初始设置，后续存储当前设置）
  optional int32 LastSvrId = 24; // 玩家上次登陆的服ID
  optional int32 battleCount = 25; //打的局数
  optional int32 maxObserverLimit = 26;         // 最大观战
  optional MemberInfoList observerList = 27;    // ob位玩家的信息 参考memList
  optional RoomTeamBattleBroadcastInfo teamBattleBroadcastInfo = 28; // 战斗播报相关存储
}

// DsCommonDbInfoTable1 - DsCommonDbInfoTable10 是SP专用
message DsCommonDbInfoTable1 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable2 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable3 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable4 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable5 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable6 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable7 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable8 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable9 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message DsCommonDbInfoTable10 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "key1,key2,key3,key4,key5,key6,key7,key8";
  option (tcaplus_splitkey) = "key1";
  option (tcaplus_index) = "index1:key1|index2:key1,key2|index3:key1,key2,key3|index4:key1,key2,key3,key4|index5:key1,key2,key3,key4,key5|index6:key1,key2,key3,key4,key5,key6|index7:key1,key2,key3,key4,key5,key6,key7";
  required int64 key1 = 1;         // 主key, 保持负载均衡
  optional string key2 = 2;
  optional int64 key3 = 3;
  optional int64 key4 = 4;
  optional int64 key5 = 5;
  optional int64 key6 = 6;
  optional int64 key7 = 7;
  optional int64 key8 = 8;
  optional int64 dsVersion = 9;            // ds版本号, 禁止低版本ds存档和加载
  optional bytes commonDsDbAttr = 10;        // 全量写入
  optional bytes changedCommonDsDbAttr = 11; // 支持增量写入, 暂时不支持可退化为全量
  optional int64 updateTimeMs = 12;					  // 上次创建或修改时间
  optional int32 serverId = 13;
}

message GeneralDataTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "type,key1,key2,key3,key4";
  option (tcaplus_splitkey) = "type,key1";
  option (tcaplus_index) = "index1:type,key1|index2:type,key1,key2|index3:type,key1,key2,key3|index4:type,key1,key2,key3,key4";
  required int32 type = 1;
  required int64 key1 = 2;
  required int64 key2 = 3;
  required int64 key3 = 4;
  required int64 key4 = 5;
  optional bytes GeneralData = 6;
}

message SPGsCommonDbInfoTable {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

// SPGsCommonDbInfoTable1 - SPGsCommonDbInfoTable10 是SPGS(Java侧)专用
message SPGsCommonDbInfoTable1 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable2 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable3 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable4 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable5 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable6 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable7 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable8 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable9 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message SPGsCommonDbInfoTable10 {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "uId,roleId,moduleType";
  option (tcaplus_splitkey) = "uId";
  option (tcaplus_index) = "index1:uId|index2:uId,roleId|index3:uId,roleId,moduleType";
  required int64 uId                                          = 1;      // 主key,Uid
  optional int64 roleId                                       = 2;      // roleid,如果是账号数据可为空
  optional int32 moduleType                                   = 3;      // 代表此条数据类型,enum StarPGsCommonDbType
  optional proto_StarPGsCommonDbInfoUnion gsCommonDbInfoUnion = 4;      // oneof数据
  optional int64 updateTimeMs                                 = 5;			// 上次创建或修改时间
}

message StarPGuildPublic {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "GuildId";
  option (tcaplus_splitkey) = "GuildId";
  required int64 GuildId = 1;
  optional StarPGuildPublicInfo PublicInfo = 2[(tcaplus_field_version) = 1];
}

message ChangePlatMidasDiff {
  option (tcaplus_customattr2) = "TableType=GENERIC";
  option (tcaplus_primarykey) = "Openid,PlatId";
  option (tcaplus_index) = "index1:Openid|index2:Openid,PlatId";
  option (tcaplus_splitkey) = "Openid";
  required string Openid = 1;
  required int32 PlatId = 2;  //ios:0, android:1
  optional proto_ChangePlatMidasDiff diffInfo = 3;
  optional proto_Money money = 4[(tcaplus_field_version) = 419];
}

message ChaseIdentityBattlePerformance {
    option (tcaplus_customattr2) = "TableType=GENERIC";
    option (tcaplus_primarykey) = "Uid";
    option (tcaplus_splitkey) = "Uid";
    required int64 Uid = 1;
    optional proto_ChaseIdentityBattlePerformanceDatas battlePerformance = 2;
}
