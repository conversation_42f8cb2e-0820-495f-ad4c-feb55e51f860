syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "common.proto";

message UgcPlayerSubReq {
  optional uint64 uid = 1;
  map<uint64, UgcPlayerInfo> player = 2;
}

message UgcPlayerFanReq {
  optional uint64 uid = 1;
  map<uint64, UgcPlayerInfo> player = 2;
}

message UgcPlayerInfo {
  optional int64 uid = 1;        // uid
  optional int32  isTop = 2;     // 是否置顶
  optional int64  topTime = 3;   // 置顶时间
}

message UgcPlayerInfoReq {
  optional uint64 uid = 1;
  optional uint64 ugcExp = 2;
  optional uint32 ugcLv = 3;
  optional uint64 ugcSeasonExp = 4;
}

message UgcPassInfoReq {
  optional UgcBestRecord bestRecord = 1;
}

message UgcDataReq {
  optional string data = 1;
}

message UgcGroupListReq {
  repeated int64 groupIds = 1;
}

message UgcPlayerOperateReq {
  optional bool isLike = 1;
  optional bool isCollect = 2;
  optional bool isSub = 3;
}

message UgcCreatorDailyReportReq {
  optional UgcDailyReport dailyReport = 1;
}

message UgcSharedProfileReq {
  optional UgcSharedProfile profile = 1;
}

message UgcThirdPartyTaskInfo { // 废弃
  optional ThirdPartyActivityParam taskInfo = 1;
}

message UgcThirdPartyTaskInfoReq {
  optional ThirdPartyActivityParam taskInfo = 1;
}

message UgcMatchLobbyMapBriefInfoReq {
  optional int64 ugcId = 1;
  optional int64 openTime = 2;
  optional int64 closeTime = 3;
  optional int32 roomInfoId = 4;  // 游戏模式id
  optional bool isTop = 5;
}

message UgcMatchLobbyMapBriefInfoAllReq {
  map<int64, UgcMatchLobbyMapBriefInfo> briefInfos = 1;  // 废弃
  optional int32 lobbyShowCnt = 2;  // 显示个数
  optional int32 lobbyShowType = 3;  // 展示规则
  map<int64, UgcMatchMapBriefFromRemote> remoteCfg = 4;  // 原始配置
}

// 资源社区推荐页信息
message UgcResHomePageCacheReq {
  optional bool hasBanner = 1;                  // 是否有活动横幅
  optional UgcResHomePagBannerInfo banner = 2;  // 横幅信息
  optional UgcResHomePageSet resSet = 3;        // 资源集合(合集)
  optional AlgoInfo algoInfo = 4;                   // 推荐算法信息
}

message UgcResBriefKeyCache {
  optional int32 resType = 1;      // 资源类型
  optional int64 ugcId = 2;        // ugcId
  optional int32 savePosition = 3;     // SavePosition
  optional int64 createTime = 4;        //创建时间
}

// 资源brief表key
message UgcResBriefKeyCacheReq {
  repeated UgcResBriefKeyCache keys = 1;
}

message UgcMulTestSaveMetaReq {
  repeated UgcMapMetaInfo info = 1; //cos
  optional uint32 pointNumber = 2; // 出生点人数
  optional string bucket = 3; // bucket信息
  optional int64 creatorId = 4; // 作者信息
  repeated BattleCamp camps = 5;
  optional string mapKey = 6;
  optional UgcLayerList layers = 7;// 图层数据
  optional UgcPublishInputParam publishInputParam = 8;
}

message PlatHotResConfigData {
  optional int32 opt = 1;  // 操作类型 0:增改,1:删
  optional string json = 2;  // 配置的json串
}

// 配置更新请求
message PlatHotResConfigReq {
  optional string name = 1;  // 配置名
  optional bool full = 2;  // 是否是全量推，如果是全量推，data中操作类型会默认为增
  repeated PlatHotResConfigData data = 3;  // 推送的配置
}

// 请求管理端同步配置
message PlatHotResConfigSyncReq {
  optional string env = 1;  // 环境信息 如 idc_test7 (是否有对应环境配置，用于管理端判断，若没有则推默认的)
  optional string name = 2;  // 配置名
}

// 请求管理端同步配置
message PlatHotResConfigSyncRes {
  optional int32 code = 1;  // 如果获取到默认配置就返回0，否则返回其他错误码（如果有对应环境配置，这里不能返回0）
  optional string desc = 2;  // 描述信息
  optional string env = 3;  // 环境信息 如 idc_test7 (是否有对应环境配置，用于管理端判断，若没有则推默认的)
  optional PlatHotResConfigReq config = 4;  // 同步的配置信息
}

message BlackMarketStoreReq {
  optional BlackMarketData data = 1;
}

message UgcShowDataSnapshotReq {
  optional UgcShowDataSnapshot snapShot = 1;
}

message UgcMapEvaluationInfoReq {
  repeated UgcMapEvaluationInfo evaluationInfo = 1;
}
