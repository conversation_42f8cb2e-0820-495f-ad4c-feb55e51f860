syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "ResKeywords.proto";
import "base_common.proto";
import "common.proto";
import "ResMall.proto";
import "player_info.proto";
import "attr_XiaoWoComponent.proto";
import "ResUgcNewYear.proto";
import "ResActivity.proto";
import "attr_UgcCoPlayInfo.proto";
import "attr_UgcBadgeInfo.proto";
import "attr_CosImage.proto";

//ugc 列表
message UgcList_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int32 isCreate = 1; //共创  (弃用)
  optional int32 mapType = 2;  //地图类型 UgcInstanceType
  optional int32 page = 3;  //当前页数 s11版本底包填加 需要兼容原来版本
}

message UgcList_S2C_Msg {
  repeated UgcItem maps = 1; //ugc列表
  optional int32 releaseLimit = 2; //发布地图上限数量
  optional int32 draftsLimit = 3; //草稿箱地图上限数量
  optional UgcKeyInfo keyInfo = 4;
  optional int32 mapType = 5;  //地图类型 UgcInstanceType

  optional int32 recycleLimit = 6; //回收站上限数量
  optional int32 coCreatDraftsLimit = 7; //联合共创草稿箱数量上限

  optional int64 secStartTime = 8; //旧地图更新 开始时间
  optional int64 secEndTime = 9; //旧地图更新 结束时间
  optional int32 trustworthy = 10; //0:不是 1:是
  optional int32 allPage = 11;     //总页数
}

//创建ugc地图
message UgcCreate_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string name = 1;  //名称
  optional int32 templateId = 2; //模板id
  optional int32 saveType = 3; //保存类型 (弃用)
  optional int32 isCreate = 4; //共创   (弃用)
  optional int32 mapType = 5;  //地图类型 UgcInstanceType
  optional UgcExtraInfo extraInfo = 6; //额外信息
  optional string ugcVersion = 7; // 当前ugc版本号
  optional int32 teamMode = 8;    // 分队模式, 仅供tlog
  optional string ruleTag = 9;    // 规则标签, 仅供tlog
  optional bool isLuaCoding = 10;    // 是否使用Lua编程字段
  optional string traceStr = 11;    //转换统一模板  旧ugcId_templateId
}

message UgcCreate_S2C_Msg {
  optional int32 result = 1; // 0:成功
  optional int64 ugcId = 2;  //id
  optional string bucket = 3;  //bucket
  optional string region = 4;
  optional int32 mapType = 5;  //地图类型 UgcInstanceType
  optional UgcKeyInfo info = 6;  //返回具体信息
  optional UgcLayerList layer = 7;  //图层信息
}
//删除地图
message UgcDelete_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //地图id
  optional int32 saveType = 2;  // 1:草稿箱 2:发布(未启用) 3:回收站  (弃用)
  optional int32 isCreate = 3; //共创  (弃用)
  optional int32 mapType = 4;  //地图类型 UgcInstanceType
}

message UgcDelete_S2C_Msg {
  optional int32 result = 1; // 0:成功
  optional int64 expireTime = 2; //过期时间
  optional int32 mapType = 3;  //地图类型 UgcInstanceType
}

// Ugc关卡保存
message SaveWork_C2S_Msg {
  option (ugc_app_forward) = true;
  optional uint64 uid = 1;
  optional uint64 ugcId = 2; // 地图id
  repeated UgcMapMetaInfo info = 3; // 作品内容
  optional int32 editorSec = 4; // 编辑时长
  repeated int64 groupIds = 5; // 组合id   M7弃用
  optional bool isInPublish = 6; // 是否处于发布流程, 用于区别草稿态的保存（目前仅组合使用）
  optional int32 mapType = 7;  // 地图类型 UgcInstanceType
  optional string ugcVersion = 8; // 当前ugc版本号
  optional bool isAutoSave = 9;     // 是否自动保存  (废弃)
  optional int32 difficulty = 10;     // 地图评级
  optional UgcGroupIdList publishGroups = 11; // 地图中使用到的已发布组合
  optional int32 saveType = 12; // common 对应 SaveType 枚举
  optional CreateStruct create = 13; // 建档结构
  optional bool isCheckPng = 14; // 是否审核图片 如果true 客户端等ntf  false 直接s2c处理
  repeated int64 resIds = 15; // 地图中使用到的资产
  optional UgcMapSetData mapSet = 16;  //设置里面数据
  optional UgcResType ugcResType = 17;     // 资源类型
  optional int32 templateId = 18;     // templateId
  repeated UgcAchievementIndex achievementIndexList = 19;  // 成就列表
  optional int32 curLayerId = 20;     // 当前所在场景id
  repeated UgcMapExtraConfigIndex extraConfigIndexList = 21;  // 额外配置列表
  optional MapLoadingInfo mapLoading = 22;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 23;  // 乐园自定义封面
  optional UgcLayerList layers = 24;// 图层数据
  optional bool isResetCodingData = 25;	// 是否重置扣叮数据
  optional CodingDataInfo codingData = 26;	// 当前已更新扣叮数据
}

message SaveWork_S2C_Msg {
  optional int32 ret = 1; // 返回结果
  optional bool isCheckPng = 2; // 是否审核图片 如果true 客户端等ntf  false 直接s2c处理
}

//复制ugc地图
message UgcCopy_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string name = 1;  //名称
  optional int64 ugcId = 2; //复制地图id
  optional int64 copyId = 3; //复制新id
  repeated UgcMapMetaInfo metaInfo = 4;
  optional string ugcVersion = 5; // 当前ugc版本号
}

message UgcCopy_S2C_Msg {
  optional int32 result = 1; // 0:成功
  optional int64 ugcId = 2;  //新地图id
}

//恢复地图 从回收站
message UgcRegain_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //地图id
  optional int32 isCreate = 2; //共创  (弃用)
  optional int32 mapType = 3;  //地图类型 UgcInstanceType
}

message UgcRegain_S2C_Msg {
  optional int32 result = 1; // 0:成功
}

// 发布列表恢复，cos 上传成功后返回服务器结果
message UgcPublishRegain_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //地图id
  repeated UgcMapMetaInfo info = 2; // 作品内容
  optional string name = 3; //name
}

message UgcPublishRegain_S2C_Msg {
  optional int32 result = 1; // 0:成功
}

message SaveWorkData {
  repeated UgcMapMetaInfo info = 1; // 作品内容
  optional int64 editTimeMillis = 2; // 编辑耗时(毫秒)
  optional int32 templateID = 3;
  optional XiaowoPublishType publishType = 4;
  optional int64 layoutID = 5;
}

// 发布地图前的预检查和下面的发布是一套流程
message UgcPublishPreCheck_C2S_Msg{
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //地图id
  optional bool isUpdate = 2;   // 地图是否发布更新
  repeated UgcEditorInfo editors = 3; //作者
}

message UgcPublishPreCheck_S2C_Msg{
  optional int32 result = 1;

}

message UgcBuyGoods_C2S_Msg{   // 对局中购买
    optional int64 seqId = 1;
    optional int64 ugcId = 2;
    optional string goodsId = 3;
    optional uint32 buyNum = 4;
    optional int32 uintPrice = 5;     // 单价
    optional string itemId = 6;       // 发货的道具 校验用
    optional uint32 itemNum = 7;      // 发货的道具数量 校验用
    optional string checkCode = 8;    // 用于校验作弊的校验码

    optional int64 gameSessionId = 99;    // gameSessionId
    optional bool isInAiTakeOverProcess = 100;  // 是否处于ai接管流程中
}
message UgcBuyGoods_S2C_Msg{
}
message UgcBuyGoodsResultNtf{   // 购买发货回调下发
    optional int64 ugcId = 1;
    optional string goodsId = 2;
    optional uint32 buyNum = 3;
    optional int64 seqId = 4;
    optional int32 errCode = 5;   // 0:成功 非0 失败
}

// 批量申请goodsId
message UgcApplyGoodsId_C2S_Msg{
    optional int32 num = 1;    // 最多不超过100个  暂时策划暂时限定
}
message UgcApplyGoodsId_S2C_Msg{
    repeated string goodsId = 1;    // 返回的申请的商品ID列表
}

message UgcActivePublishGoods_C2S_Msg{   // 请求激活发布商品功能
    optional int64 ugcId = 1;    // ugcID
}
message UgcActivePublishGoods_S2C_Msg{
    optional UgcMapPublishGoodsStatus status = 1;   // 发布商品状态
}


//发布地图
message UgcPublish_C2S_Msg {
  optional int64 ugcId = 1;  //地图id
  optional string name = 2;  //名字
  optional string desc = 3;  //描述
  repeated UgcMapMetaInfo metaInfo = 4;
  repeated int32 type = 5;  //类型
  optional int64 publishId = 6;
  repeated string tags = 7;  //tag
  optional int32 isCreate = 8; //共创  (弃用)
  optional int32 mapType = 9;  //地图类型 UgcInstanceType
  repeated string contentString = 10;//客户端使用字符串,透传给安全 废弃
  optional int32 pointsNumber = 11;  //出生点人数

  optional string mapPreview = 12; //地图预览图
  optional int32 occupancyValue = 13; //地图占用值
  optional int32 background = 14; //地图背景
  optional int32 mapRule = 15;    //地图规则
  optional int32 timeLimit = 16; //游戏时长
  optional int32 isCamMove = 17; //游戏内相机移动开关
  optional int32 target = 18;    //游戏目标（积分赛需设置积分目标，其他模式默认为0）

  optional string ugcVersion = 19; // 当前ugc版本号
  optional bool isUpdate = 20;     // 地图是否更新
  optional int32 metaId = 21; // 当前ugc版本号
  repeated uint32 goldTopics = 22;
  repeated string blueTopics = 23;
  repeated BattleCamp camps = 24;     // 地图阵营信息
  repeated SceneItem items = 25; // 场景道具
  repeated UgcEditorInfo editors = 26; //作者
  repeated VictoryArea victoryArea = 27;//胜利区域
  optional UgcExtraInfo extraInfo = 28; // 额外信息
  optional SaveWorkData saveWorkData = 29; // 保存草稿的信息
  optional int32 IsBlockVoice = 30; //是否屏蔽角色语音
  optional int32 IsBlockPlay = 31; //是否屏蔽音乐播放
  optional int32 propertyScore = 32; //性能评分
  optional int32 actorCount = 33; //actor数量
  optional int32 aesthetic = 34; // 美观度
  repeated proto_XiaoWoComponent component = 35; // 星元件
  optional bool isCampsOpen = 36; //阵营是否开启
  optional bool isEditable = 37; //是否可编程 （单纯记录 策划需求不明确 不与tags一起存放）
  optional bool isOpenHealth = 38; //是否打开生命值
  repeated int64 resIds = 39;     //地图中使用到的资产id
  repeated UgcGoodsInfo ugcGoodsInfo = 40;    // 上架商品信息
  optional bool isOpenSave = 41;  //是否开启存档
  optional bool isDanMuUnable = 42;  // 是否打开弹幕
  repeated MapCoverInfo covers = 43;   //多封面缩略图
  optional UgcGroupIdList ugcGroupIdList = 44; // 地图中使用到的发布组合
  optional bool isAllowMidJoin = 45;  // 是否允许中途加入
  repeated UgcRankInfo ugcRankInfo = 46;    // 排行榜信息 最多3个
  optional UgcMapSetData mapSet = 47;  //设置里面数据
  optional bool finishMultiTest = 48; //进行过多人测试后保存
  repeated SpawnPointInfo spawnPointInfos = 49; // 出生点信息
  optional int32 dataStoreSaveType = 50; // 数据存储类型 ENUM DataStoreSaveType
  optional bool isContainDiyArms = 51;  // 是否包含自制装备
  optional int32 playRuleOMD = 52;  // 兽人类型
  repeated UgcAchievementIndex achievementIndexList = 53;  // 成就列表
  optional OmdLevelType omdLevel = 54;  // 兽人关卡难度
  optional bool isLuaCoding = 55;    // 是否使用Lua编程字段
  optional bool isResetPassRecord = 56;  // 是否重置通关记录, 针对竞速类地图/兽人ugc地图生效
  optional string qqMusicInfo = 57; // qq音乐存储在服务器 废弃
  optional UgcPublishInputParam publishParam = 58;// 发布参数统一放在这里
}



message VictoryArea {
  optional int64 id = 1;
  optional UVector origin = 2;    //盒子中心点
  optional UVector boxExtent = 3;//盒子尺寸 half
}

message UgcPublish_S2C_Msg {
  optional int32 result = 1;  // 0:成功
  optional int32 mapType = 2;  //地图类型 UgcInstanceType
  optional int64 ugcId = 3;   //发布地图id
}

//下架地图
message UgcTakeOff_C2S_Msg {
  optional int64 ugcId = 1;  //地图id
  optional int32 isCreate = 2; //共创  (弃用)
  optional int32 mapType = 3;  //地图类型 UgcInstanceType
}

message UgcTakeOff_S2C_Msg {
  optional int32 result = 1; // 0:成功
  optional int32 mapType = 2;  //地图类型 UgcInstanceType
}

//下架地图
message UgcTakeOffApply_C2S_Msg {
  optional int64 ugcId = 1;  //地图id
  optional string reason = 2;
}

message UgcTakeOffApply_S2C_Msg {
  optional int32 result = 1; // 0:成功
}

message UgcApplyKeyInfo_C2S_Msg {
  option (ugc_app_forward) = true;
  optional ApplyType applyType = 1;  //申请类型，例如发布地图等等
  optional ApplyReason applyReason = 2;
  optional int64 mapId = 3;//暂时不动，后面统一使用objectId
  repeated int64 objectId = 4;  //通用唯一id列表，携带申请的id，例如组合，携带组合id
  optional bool isUpdate = 5;  //是否更新
  optional UgcInstanceType instanceType = 6; // map类型
}

message UgcApplyKeyInfo_S2C_Msg {
  optional int32 result = 1;
  optional UgcKeyInfo info = 2;  //返回具体信息
  optional UgcMapMetaInfo metaInfo = 3;
  optional int64 publishId = 4;
  optional int64 copyId = 5;
  optional int64 uniqueId = 6;//草稿唯一id
  optional string bucket = 7;
  optional string region = 8;
  map<int32, int64> idMap = 9;
  optional string filePath = 10; // cos文件路径
  optional AigcObjInfo aigcInfo = 11;
}

message AigcObjInfo {
  optional bool hasAiImageHistory = 1;
  optional bool hasAiImageRecently = 2; // 废弃
}

//设置
message UgcMapSet_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int32 musicSize = 1;  //音乐
  optional int32 soundSize = 2;  //音响
  optional int32 editAngle = 3;  //视角
  optional int32 netDisplay = 4;  //网络视角
  optional int32 operateAid = 5;  //操作辅助
}

message UgcMapSet_S2C_Msg {
  optional int32 result = 1; // 0:成功
}


//发布列表
message UgcPublishList_C2S_Msg {
  optional int32 page = 1; //页数
  optional int32 mapType = 2;  //地图类型 UgcInstanceType
}

message UgcPublishList_S2C_Msg {
  optional int32 result = 1; // 0:成功
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
}

// Ugc打开关卡
message GetSaveWork_C2S_Msg {
  option (ugc_app_forward) = true;
  optional uint64 ugcId = 1; // 作品id
  optional int32 type = 2; //  1:草稿箱 2:发布 3:回收站 4：单人游玩
  optional UgcInstanceType instanceType = 3;
}

message GetSaveWork_S2C_Msg {
  optional int32 ret = 1; // 返回结果
  optional UgcKeyInfo keyInfo = 2;
  optional string bucket = 3;
  optional string region = 4;
  optional int64 ugcId = 5; // 地图id
  optional int32 type = 6; // 1:草稿箱 2:发布 3:回收站 4：单人游玩
  optional int32 ugcLv = 7; // 工匠值等级
  repeated int64 groupIds = 8; // 违规的组合id
  optional int64 lastSaveTime = 9; // 上次保存时间
  optional string ugcVersion = 10; // 当前ugc版本号
  optional string clientVersion = 11; //当前客户端版本号
  optional int32 saveType = 12; // 地图保存位置
  optional UgcMapMetaInfo metaInfo = 13;
  repeated int64 layerId = 14;
  optional int64 creatorId = 15; //玩家creatorId
  repeated UgcMapMetaInfo covers = 16;   //多封面缩略图
  repeated UgcAchievementIndex achievementIndexList = 17;  // 成就列表
  optional int32 curLayerId = 18; //当前所在图层
  repeated UgcMapMetaInfo metaInfos = 19;  //所有的场景信息
  repeated UgcLayerInfo layerList = 20; //图层
  optional int32 difficulty = 21;     // 地图评级
  optional MapLoadingInfo mapLoading = 22;  // 自定义loading
  optional MapLobbyCoverInfo lobbyCover = 23;  // 乐园自定义封面
}

// Ugc点赞
message UgcOperate_C2S_Msg {
  option (ugc_app_forward) = true;
  optional uint64 ugcId = 1; // 作品id
  optional UgcOpType opType = 2; // 对应 common.proto 里面UgcOpType
  optional int32 gameSource = 3; //1.游廊 2.闯关 3.个人信息页中的UGC内容Tab 4.创作者中心
  optional int32 canvasId = 4;
  optional string tabName = 5;
  optional int32 exposePos = 6;
  optional string battleId = 7;

  optional AlgoInfo info = 8;
  optional string recId = 9;
  optional string expTag = 10;

}

message UgcOperate_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

// Ugc 取消
message UgcOperateCancel_C2S_Msg {
  option (ugc_app_forward) = true;
  optional uint64 ugcId = 1; // 作品id
  optional UgcOpType opType = 2; // 对应 common.proto 里面UgcOpType
  optional int32 gameSource = 3; //1.游廊 2.闯关 3.个人信息页中的UGC内容Tab 4.创作者中心
  optional int32 canvasId = 4;
  optional string tabName = 5;
  optional int32 exposePos = 6;
  optional string battleId = 7;
  optional AlgoInfo info = 8;
  optional string recId = 9;
  optional string expTag = 10;
}

message UgcOperateCancel_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

//地图筛选
message UgcMapScreen_C2S_Msg {
  optional UgcMapScreenType type = 1;
  optional int32 page = 2;
  optional int32 MapType = 3;
  optional string tag = 4;          // M7开始废弃
  repeated uint32 tags = 5;         // M7的标签
  repeated uint32 goldTopics = 6;   // 金色话题(废弃)
  repeated string blueTopics = 7;   // 蓝色话题(废弃)
  optional bool needRedDot = 8;
  optional bool isChangeTag = 9;  // 是否切换页签
  optional UgcMapScreenServerData serverData = 10;
  optional UgcRecommendGameInfo gameInfo = 11;  // UgcBattleSettlementRecommend结算推荐参数
  optional TopicInfo topicInfo = 12;  // 话题推荐接口
  optional int32 sortType = 13; // 排序类型
}

message UgcMapScreen_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
  repeated UgcMapScreenType redDotType = 5;
  optional UgcMapScreenServerData serverData = 6;
  repeated HotPlayList typeList = 7;
  optional int32 hasMorePage = 8; //1:表示还有下一页,0没有下一页
  repeated UgcHomePageRecommendData data = 9;
  optional int32 subscribeStatus = 10; // 0=未订阅；1=已订阅
}

message UgcMapScreenServerData {
  repeated int64 mapId = 1;
  optional AlgoInfo info = 2;
  optional int32 hasMorePage = 3;
}

//类型筛选
message UgcMapType_C2S_Msg {
  optional int32 type = 1;
  optional string tag = 2;
  optional int32 page = 3;
}

message UgcMapType_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
}

//搜索
message UgcMapSearch_C2S_Msg {
  optional string name = 1;
  optional int32 page = 2;
  repeated int32 tags = 3;        // 废弃使用 wenhuazheng@20231009
  optional int32 searchType = 4;  // 搜索类型, 0:地图 1:话题 2:综合 3:作者 4:合集
  optional string sessionId = 5;  // 上下文
  optional int32 source = 6;  // 1.主动 2.历史 3.热搜 4.推荐
}

message UgcMapSearch_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
  optional PublishStruct recommendMaps = 5; //推荐地图 保底
  optional SearchType searchType = 6;  // 搜索类型,  0:地图 1:话题 2:综合 3:作者
  map<int32, int32>  countMap = 7;   //数量
  optional TopicInfoItem topicInfo = 8;  //精准话题
  optional string keyWord = 9;     //搜索关键词
  repeated TopicInfoItem topicItem = 10; //话题列表
  repeated EditorInfoItem editorItem = 11; //作者列表
  optional EditorInfoItem editorInfo = 12;  //精准作者
  optional string sessionId = 13;  // 上下文
  repeated UgcCollectionBrief collections = 14; // 合集
}

//发布修改
message UgcMapPublishModify_C2S_Msg {
  optional int64 ugcId = 1;
  optional string name = 2;
  optional string desc = 3;
  repeated string tags = 4;
}

message UgcMapPublishModify_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

message UgcOpSubscribe_C2S_Msg {
  optional int64 uid = 1;
  optional int32 type = 2; //0:订阅 1：取消订阅
  optional int64 creatorId = 3;
  optional int32 canvasId = 4;
}

message UgcOpSubscribe_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

// Ugc 我的订阅
message UgcMySubscribe_C2S_Msg {
  optional int32 page = 1;   //page
  optional int32 sceneType = 2; // 0默认 1请求红点  2星世界订阅列表
}

message UgcMySubscribe_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated UgcOpPlayerInfo infos = 2; //订阅玩家列表
  optional int32 hasMorePage = 3; //1:表示还有下一页,0没有下一页
  optional int32 subCount = 4;
  optional int32 maxSubCount = 5;
}

// Ugc 我的粉丝
message UgcMyFans_C2S_Msg {
  optional int32 page = 1;   //page
}

message UgcMyFans_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated UgcOpPlayerInfo infos = 2; //粉丝列表
  optional int32 hasMorePage = 3; //1:表示还有下一页,0没有下一页
}

// Ugc 推荐订阅
message UgcRecommendSubscribe_C2S_Msg {
}

message UgcRecommendSubscribe_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated UgcOpPlayerInfo infos = 2; //订阅玩家列表
  optional AlgoInfo info = 3;
}

// Ugc 发布界面详情
message UgcPublishDetails_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;   //ugcId
}

message UgcPublishDetails_S2C_Msg {
  optional int32 result = 1;    //返回结果
  optional MapDetails details = 2; //详情
}


enum ObjectOperate {
  Favorite = 1;   //收藏
  UnFavorite = 2; //取消收藏
  GiveLike = 3;   //点赞
  UnGiveLike = 4; //取消点赞
}

enum ObjectType {
  Object = 1; //普通物件
  GroupObject = 2;//资源
}

//创建组合(废弃，走 UgcCreate_C2S_Msg )
message UgcCreateGroupObject_C2S_Msg {
  optional string name = 1;
}

message UgcCreateGroupObject_S2C_Msg {
  optional int32 result = 1;    //返回结果
  optional int64 objectId = 2;
  optional string bucket = 3;
  optional string region = 4;
}

// 删除组合(废弃，走 UgcResDelete_C2S_Msg )
message UgcDeleteGroup_C2S_Msg {
  optional int64 ugcId = 1;  //组合id
}

message UgcDeleteGroup_S2C_Msg {
  optional int32 result = 1; // 0:成功
}

// 发布组合(废弃，走 UgcResCreate_C2S_Msg )
message UgcPublishGroup_C2S_Msg {
  optional int64 ugcId = 1;  //组合id
  optional string name = 2;  //名字
  optional string desc = 3;  //描述
  repeated UgcMapMetaInfo metaInfo = 4;
  optional int64 publishId = 5;
  repeated string tags = 6;  //tag
  optional UgcExtraInfo extraInfo = 7; //额外信息
  repeated SceneItem items = 8; // 场景里使用到的小窝家具
}

message UgcPublishGroup_S2C_Msg {
  optional int32 result = 1; // 0:成功
}

// 拉取组合列表(我的)(废弃，走 UgcResGetMyList_C2S_Msg )
message UgcListForObject_C2S_Msg {
  repeated ObjectType type = 1;  // 1普通物件 2组合 .获取全部就都传入进来
}

message UgcListForObject_S2C_Msg {
  optional int32 result = 1;    //返回结果
  repeated int64 objects = 2;         // 普通物件 废弃
  repeated int64 composeObject = 3;   // 组合对象 废弃
  repeated UgcListForObject groupObject = 4; //组合对象 废弃
  optional UgcKeyInfo keyInfo = 5;
  repeated UgcItem groups = 6; // 组合列表
}

// 拉取组合列表(社区)(废弃，走 UgcResCommunityGet_C2S_Msg )
message UgcScreenForObject_C2S_Msg {
  optional UgcMapScreenType type = 1;  // 拉取策略
  optional int32 page = 2;
  optional int32 MapType = 3;
  optional string tag = 4;
  repeated int32 multiTags = 5;   // 多标签
}

message UgcScreenForObject_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
}

// 下架组合(废弃，走 UgcResTakeOff_C2S_Msg )
message UgcTakeOffForObject_C2S_Msg {
  optional int64 ugcId = 1;    // 组合id
}

message UgcTakeOffForObject_S2C_Msg {
  optional int32 result = 1;    // 返回结果
  optional int64 ugcId = 2;    // 组合id
}

// 组合详情(废弃，走 UgcResGetPublishDetail_C2S_Msg )
message UgcPublishDetailsForObject_C2S_Msg {
  optional int64 ugcId = 1;   //ugcId
}

message UgcPublishDetailsForObject_S2C_Msg {
  optional int32 result = 1;    //返回结果
  optional MapDetails details = 2; //详情
  optional int64 ugcId = 3;   //ugcId
}

// ugc 对象操作
message UgcObjectOperate_C2S_Msg {
  option (ugc_app_forward) = true;
  optional ObjectType type = 1;  // 1普通物件 2组合
  optional ObjectOperate operate = 2;
  optional int64 objectId = 3;
  optional UgcMapMetaInfo metaInfo = 4; //上传后需要携带给我
  optional string name = 5;//废弃
  optional AlgoInfo algoInfo = 6;
}

message UgcObjectOperate_S2C_Msg {
  optional int32 result = 1;    //返回结果
  optional ObjectType type = 2;  // 透传 输入参数
  optional int64 objectId = 3;
}

message UgcListForObject{
  optional int64 objects = 1;         // 普通物件
  optional UgcMapMetaInfo metaInfo = 2;
  optional string name = 3;
  optional string bucket = 4;
  optional string region = 5;
}

// 获取组合收藏列表(废弃，走 UgcResGetCollect_C2S_Msg )
message UgcCollectForObject_C2S_Msg {
  optional int32 page = 1;
}

message UgcCollectForObject_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;  // 收藏的发布组合
  repeated int64 ids = 3;  // 客户端本地的组件，只存储一个id(弃用)
  optional UgcKeyInfo keyInfo = 4;
  repeated UgcItem selfItems = 5; // 收藏的未发布的自制组合
  repeated UgcGroupCommon groupCommons = 6; // 组件收藏信息
}

// 判断组合社区白名单和其它信息
message UgcCheckWhitelistForObject_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 uid = 1;
}

message UgcCheckWhitelistForObject_S2C_Msg{
  optional bool isInWhitelist = 1;  // 是否在创作者白名单
  optional int32 releaseLimit = 2;     // 发布组合上限数量
  optional int32 draftsLimit = 3;      // 我的组合上限数量
  optional bool isGroupCommunityOpen = 4; // 组件社区是否开放
}

// 拉取官方组合
message UgcGroupOfficalList_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int32 page = 1;   // 页号
}

message UgcGroupOfficalList_S2C_Msg {
  optional int32 result = 1;         // 返回结果 0=成功
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
}

// 搜索组合(废弃，走 UgcResCommunitySearch_C2S_Msg )
message UgcGroupSearch_C2S_Msg {
  optional string name = 1;  // 模糊名
  optional int32 page = 2;   // 页号
  repeated int32 tags = 3;   // 标签
}

message UgcGroupSearch_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
}

message UgcGroupBatchGetPublish_C2S_Msg {
  option (ugc_app_forward) = true;
  repeated int64 ugcIds = 1;
}

message UgcGroupBatchGetPublish_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
}

// 客户端弹窗等状态扭转
message UgcStageChange_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;                  // ugcId
  optional UgcStageChangeType type = 2;      // 类型
  optional UgcInstanceType mapType = 3;      // 地图类型
  optional UgcResType resType = 4;           // 资源类型
}

message UgcStageChange_S2C_Msg {
  optional int32 result = 1;       // 结果 0=success
}

//置顶
message UgcOpSubTop_C2S_Msg {
  optional int64 uid = 1; // uid
  optional int32 type = 2; // 0:置顶 1：取消置顶
  optional int64 creatorId = 3;
}

message UgcOpSubTop_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

//单人通关开局
message UgcSingleStageStart_C2S_Msg {
  option (ugc_app_forward) = true;
  optional SingleStageType singleType = 1; //单人闯关类型
  optional int32 stepId = 2; // 第几个阶段(1~5) //singleType:ST_Daily 填充
  optional int64 mapId = 3; // 地图id //singleType:ST_Normal 填充
  optional string recId = 4; // singleType:ST_Normal 填充
  optional string expTag = 5;// singleType:ST_Normal 填充
  optional int32 canvasID = 6;//singleType:ST_Normal\ST_Daily 填充
  optional int32 tabName = 7;//废弃
  optional int32 exposePos = 8;//singleType:ST_Normal 填充
  optional string tabDesc = 9;// singleType:ST_Normal 填充
  optional int32 gameSource = 10; //1.游廊 2.闯关 3.个人信息页中的UGC内容Tab 4.创作者中心  5.星图推荐 8.跑图集星
  optional int32 starId = 11; // 1:星球1,其他:星球2
  optional int32 lsRiskTips = 12; // 是否忽视风险提示(仍要游玩)
  optional bool isShareChannel = 13; // 是否是分享渠道拉起的
  optional string sourceModule = 14;
  optional string activityId = 15;  // 创作赛（王者、美团、腾讯视频）
  optional string themeName = 16; // 所属主题名称
  optional string SearchID = 17; // 搜索id
  optional string fromCollectionId = 18;  // 从哪个合集进入
  optional int32 logicMapSource = 19;     // 地图从哪个界面进入的, 用于业务逻辑
  optional string SubTabName = 20;
  optional int32 lobbyType = 21;  // 带上来的大厅类型
  optional int64 lobbyMapId = 22;  // 带上来的大厅id
}

message UgcSingleStageStart_S2C_Msg {
  optional SingleStageType singleType = 1; //单人闯关类型
  optional UgcDailyStageStepInfo stepInfo = 2; // 阶段信息
  optional UgcKeyInfo keyInfo = 3;
  optional int64 battleId = 4;//battleId,这一局结束的时候要用到
  optional string bucket = 5;
  optional string region = 6;
  optional int64 mapId = 7; // 地图id
  optional AlgoInfo info = 8;
  optional UgcMapMetaInfo metaInfo = 9;
  repeated int64 separateFactor = 10;     // 分包因子 (分包下载功能使用)
  optional int32 templateId = 11; // 地图模板id
  optional int32 ugcAchievementSize = 12;  // 成就的个数
}

//单人通关结算
message UgcSinglePassLevel_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 passUid = 1; // 通关者uid singleType:ST_Normal\ST_Daily 填充
  optional int32 passSec = 2; // 通关毫秒数 singleType:ST_Normal\ST_Daily 填充
  optional int64 ugcId = 3; // 通关地图id singleType:ST_Normal\ST_Daily 填充
  optional bool isPass = 4; // 是否通关 singleType:ST_Normal\ST_Daily 填充
  optional SingleStageType singleType = 5; //单人闯关类型
  optional int64 battleId = 6; //singleType:ST_Normal\ST_Daily 填充
  optional int32 stepId = 7; //singleType:ST_Daily 填充
  optional int32 failReason = 8;// 失败的原因 1：未通关、2：主动退出、3：掉线不返回对局退出，4：更换地图，5：生命耗尽，6：复位 -1000:客户端失败 singleType:ST_Normal\ST_Daily 填充
  optional string recId = 9; //singleType:ST_Normal\ST_Daily 填充
  optional string expTag = 10;//singleType:ST_Normal\ST_Daily 填充
  optional int32 canvasID = 11;//singleType:ST_Normal\ST_Daily 填充
  optional int32 tabName = 12;//废弃
  optional int32 exposePos = 13;//singleType:ST_Normal 填充
  optional string tabDesc = 14;// singleType:ST_Normal 填充
  optional int32 gameSource = 15; //1.游廊 2.闯关 3.个人信息页中的UGC内容Tab 4.创作者中心 5.星图推荐
  optional int32 deathNum = 16; // 死亡次数
  optional int32 destinationPointId = 17;
  optional string endPosition = 18;
  optional string sourceModule = 19;
  optional string activityId = 20;  // 创作赛（王者、美团、腾讯视频）
  optional string themeName = 21; // 所属主题名称
  optional string SearchID = 22; // 搜索id
  optional string fromCollectionId = 23;  // 从哪个合集进入
  optional int32 logicMapSource = 24;     // 地图从哪个界面进入的, 用于业务逻辑
  optional string SubTabName = 25;
  optional UgcBattleMultiLevelData ugcMultiLevelData = 26;  // 多场景结算数据
  optional int32 lobbyType = 27;  // 带上来的大厅类型
  optional int64 lobbyMapId = 28;  // 带上来的大厅id
}

message UgcSinglePassLevel_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional int64 exp = 2;//经验
  optional int32 level = 3;//等级
  optional int32 passSec = 4; // 通关毫秒数
  optional bool isPass = 5; // 是否通关
  optional int32 stepId = 6; // 阶段信息
  optional SingleStageType singleType = 7; //单人闯关类型
  optional int64 battleId = 8; //
  optional bool isBestRecord = 9; // 是否打破这个地图的最佳记录
  optional ItemArray rewardItems = 10; // 通关奖励(非关卡掉落)
}

// 更换地图
message UgcDailyStageChangeMap_C2S_Msg {
  optional int32 stepId = 2; // 第几个阶段(1~5)
  optional int32 costTime = 3; // 消耗毫秒数
}

message UgcDailyStageChangeMap_S2C_Msg {
  optional UgcDailyStageStepInfo stepInfo = 1; // 阶段信息
  optional UgcKeyInfo keyInfo = 2;
  optional AlgoInfo info = 4;
  repeated int64 separateFactor = 5;     // 分包因子 (分包下载功能使用)
  optional int32 templateId = 6;
}

// 重置次数
message UgcDailyStageReset_C2S_Msg {
  optional int32 data = 1; //站位字段
}

message UgcDailyStageReset_S2C_Msg {
  optional UgcDailyStageResetInfo resetInfo = 1; //重置信息
}

// 获取日闯关信息
message GetUgcDailyStageInfo_C2S_Msg {
  optional int32 data = 1; //站位字段
}

message GetUgcDailyStageInfo_S2C_Msg {
  repeated UgcDailyStageStepInfo stepInfo = 1; // 阶段信息列表
  optional UgcDailyStageResetInfo resetInfo = 2; //重置信息
  optional UgcKeyInfo keyInfo = 3;
  repeated com.tencent.wea.xlsRes.MallCommodity shopCommodityList = 4;
  repeated int32 canBuyCommodityId = 5;
}

// 扣除生命值
message UgcDelLifeItem_C2S_Msg {
  optional int64 battleId = 1;
  optional int64 mapId = 2;
}

message UgcDelLifeItem_S2C_Msg {
  optional int64 battleId = 1;
  optional int64 mapId = 2;
}

// 新版巡游-星海探险
message GetUgcStarWorldInfo_C2S_Msg {

}
message GetUgcStarWorldInfo_S2C_Msg {
  optional int32 stepId = 1; // 今日第几轮
  optional UgcStarWorldStepInfo firstStarInfo = 2; // 星球1信息
  optional UgcStarWorldStepInfo secondStarInfo = 3; // 星球2信息
  optional int32 rewardItemId = 4;  // 每日道具id
  optional int32 rewardItemNum = 5; // 每日道具已获得
  optional int32 rewardItemMaxNum = 6;  // 每日道具最多获得
  repeated com.tencent.wea.xlsRes.MallCommodity shopCommodityList = 8;
  repeated int32 canBuyCommodityId = 9;
  optional int32 rewardItemAllNum = 10;  // 已获得总积分
}

// 更换地图
message UgcStarWorldChangeMap_C2S_Msg {
  optional int32 stepId = 1;
  optional int32 starId = 2; // 1:星球1,其他:星球2
  optional int32 costTime = 3; // 消耗毫秒数
  optional int64 ugcId = 4; // 当前的地图id
  optional int32 deathNum = 5; // 死亡次数
}

message UgcStarWorldChangeMap_S2C_Msg {
  optional UgcDailyStageStepInfo stepInfo = 1; // 阶段信息
  optional UgcKeyInfo keyInfo = 2;
  optional AlgoInfo info = 4;
  optional int32 changeCount = 5; // 当前星球已修改地图次数
  repeated int64 separateFactor = 6;     // 分包因子 (分包下载功能使用)
  optional int32 templateId = 7;
}
//----------------------ugc房间相关开始---------------------------------------

message UgcCreateRoom_C2S_Msg {
  optional string name = 1;  //房间名称
  optional string pwd = 2;   //房间密码
  optional bool  isWatch = 3; //是否让观战  false 不可以 true 可以
  optional int64 ugcId = 4;  //ugcId
  optional int32 memberLimit = 5;  //房间人数
  optional int32 gameSource = 6;  // 1.乐园星图, 2.星海巡游, 3.个人信息, 5.星图推荐 8.跑图集星
  optional RoomSetting roomSetting = 7; // 房间设置
  optional string sourceModule = 8;
  optional string activityId = 9;  // 创作赛（王者、美团、腾讯视频）
  optional string themeName = 10; // 所属主题名称
  optional int32 matchTypeId = 11; // 玩法id，用于区分不同的玩法场景
  optional int64 ugcLayerId = 12;  // ugc图层Id，用来锁定图层，ugc测试房间需要设置这个值
  optional string fromCollectionId = 13;  // 从哪个合集进入
  optional int32 logicMapSource = 14;     // 地图从哪个界面进入的, 用于业务逻辑
  optional string SubTabName = 15;
  optional string searchID = 17; // 搜索id
  optional MatchRuleClientInfo clientInfo = 18;  // 客户端透传
}

message UgcCreateRoom_S2C_Msg {
  optional int32 result = 1;  //结果
}


message UgcRoomChangeMap_C2S_Msg {
  optional int64 roomId = 1;      //房间Id
  optional int64 mapId = 2; // 地图id
  optional RoomSetting roomSetting = 3; // 房间设置
}

message UgcRoomChangeMap_S2C_Msg {
  optional int32 result = 1;  //结果
}


message UgcRoomPlayerRecommendMap_C2S_Msg {
  optional int64 roomId = 1;
  optional int64 mapId = 2; //ugc地图ID
}
message UgcRoomPlayerRecommendMap_S2C_Msg {
}

message UgcRoomPlayerRecommendMapNtf {
  optional PlayerPublicInfo recommendPlayerInfo = 1; //推荐人的信息
  optional UgcMapShowInfo ugcMapInfo = 2; //推荐的UGC地图信息
  optional int32 intimacy = 3; //推荐玩家跟自己的好友亲密度
}

message UgcRoomGetPlayerRecommendMaps_C2S_Msg { //拉取推荐地图信息
  optional int64 roomId = 1;
}
message UgcRoomGetPlayerRecommendMaps_S2C_Msg {
  repeated UgcRoomPlayerRecommendMapInfo recommendInfos = 1; //UGC房间成员推荐地图列表
}

message UgcRoomGetOfficalRecommendMaps_C2S_Msg { //拉取推荐地图信息
  optional int64 roomId = 1;
  optional string tab_id = 2; //页签名，如果传空，拉取所有
  optional int32 page = 3; //拉取第几页的数据

}
message UgcRoomGetOfficalRecommendMaps_S2C_Msg {
  repeated UgcRoomOfficalRecommendMapInfo recommendInfos = 1; //UGC房间地图列表
  repeated UgcRoomOfficalRecommendTabInfo tabInfos = 2; //页签信息，每次请求都返回
  optional AlgoInfo info = 3;
  optional int32 hasMorePage = 4; //1:表示还有下一页，0比没有下一页
  optional int32 page = 5; //请求的页数
}
message UgcRoomList_C2S_Msg{
  optional int64 ugcId = 1;  //地图id
  optional int32 page = 2;   //页面
}

message UgcRoomList_S2C_Msg{
  repeated RoomInfo rooms = 1;
  optional int32 result = 2;
}

enum RecListType {
  RLT_Map = 0; // 地图
  RLT_Type = 1; // 类型
  RLT_Tag = 2; // 标签
}

message UgcRoomRecommendList_C2S_Msg {
  optional int64 ugcId = 1;  //地图id
  optional int32 num = 2; // 数量
  optional RecListType listType = 3; // 列表类型
  optional int32 mapType = 4; // 地图类型
  optional string tag = 5; // 地图标签
  optional int32 source = 6;  // 入口来源  RoomRecommendListSource
}

message UgcRoomRecommendList_S2C_Msg {
  optional UniversalRoomArray roomList = 1; // 房间列表
}

message RoomInfo{
  optional int64 roomId = 1;      //房间Id
  optional string name = 2;       //房主昵称
  optional string avatar = 3;     //房主头像
  optional string roomName = 4;   //房间名字
  optional int32 playerCount = 5; //人数
  optional bool isFriend = 6;   //是否好友    true:好友
  optional bool isPwd = 7;      //是否有密码  true:带
  optional RoomBriefInfo roomBriefInfo = 8; // 房间简要信息
  optional UgcBriefInfo ugcBriefInfo = 9; // ugc简要信息
}

message UgcJoinRoom_C2S_Msg{
  optional int64 roomId = 1;      //房间Id( 0 代表快速加入)
  optional string pwd = 2;        //房间密码
}

message UgcJoinRoom_S2C_Msg{
  optional int32 result = 1;       //结果
  optional int64 ugcId = 2;           //地图id
  optional string name = 3;           //房主昵称
  optional int64 roomId = 4;          //房间Id
  optional int32 templateId = 5;      //地图id
  optional int32 playerCount = 6;     //人数
  optional bool isPwd = 7;            //是否有密码  true:带
  optional int32 watchCount = 8;      //观战人数
  optional int64 expireTime = 9;      //到期时间
  repeated roomPlayerInfo player = 10; //房间人物信息
}

message UgcQuickJoin_C2S_Msg {
  optional int64 ugcId = 1; // ugc地图id，不指定则在所有的地图中随机
  repeated int64 priorityIdcZoneId = 2; // 优先idcZoneId 列表
}

message UgcQuickJoin_S2C_Msg {
  optional int32 result = 1;       //结果
  optional int64 roomId = 2; // 房间Id
  optional int64 targetIdcZoneId = 3;  // targetIdcZoneId
}

message roomPlayerInfo{
  optional string name = 1;       //房主昵称
  optional string avatar = 2;     //房主头像
  optional bool isRoomer = 3;     //是否房主    true:是
  optional int32 type = 4;       //0：不是好友 1：是 2：自己
}

message UgcRoomPositionUpdate_C2S_Msg {
  optional int64 roomId = 1; // 房间Id
  optional int32 targetPosition = 2; // 目标位置
}

message UgcRoomPositionUpdate_S2C_Msg {
  optional int32 result = 1;       //结果
}

message UgcExitRoom_C2S_Msg{

}

message UgcExitRoom_S2C_Msg{
  optional int32 result = 1;  //结果
}

message UgcRoomPreStart_C2S_Msg {

}

message UgcRoomPreStart_S2C_Msg {
  optional int32 result = 1;  //结果
}

//----------------------ugc房间相关结束---------------------------------------

message CommonSave {
  optional string key = 1;  //注意key长度要求在20以内
  optional string value = 2;//注意value长度要求在20以内，如果有需求请联系
}

message UgcCommonSave_C2S_Msg {
  repeated CommonSave save = 1;//注意数组大小不能超过20个，如果有需求请联系
}

message UgcCommonSave_S2C_Msg{
  optional int32 result = 1;  //结果
}

message UgcGetCommonSave_C2S_Msg {
}

message UgcGetCommonSave_S2C_Msg{
  optional int32 result = 1;  //结果
  repeated CommonSave save = 2;//注意数组大小不能超过20个，如果有需求请联系
}

//获取协议签署状态
message GetUgcAccept_C2S_Msg{
}

message GetUgcAccept_S2C_Msg {
  optional int32 result = 1;    //结果
  optional string acceptVersion = 2;  //已接收版本号
  optional string curVersion = 3;     //当前版本号
}

message UgcAccept_C2S_Msg{
  optional string acceptVersion = 1;  //接收版本号
}

message UgcAccept_S2C_Msg {
  optional int32 result = 1;    //结果
}

//----------------------ugc共创协议 start-------------------------------------
//获取邀请码
message GetUgcCoCreateInviteCode_C2S_Msg{
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //ugcId
}

message GetUgcCoCreateInviteCode_S2C_Msg {
  optional int32 result = 1;       //结果
  optional string inviteCode = 2;  //邀请码
}

//----------------------ugc共创协议 end---------------------------------------

message UgcModifyName_C2S_Msg{
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //ugcId
  optional string name = 2;  //name
}

message UgcModifyName_S2C_Msg {
  optional int32 result = 1;    //结果
}

// 获取玩家ugc的个人信息
message UgcGetUgcPlayerInfo_C2S_Msg {
  repeated int64 uidList = 1; // uid
  repeated int64 creatorIdList = 2;
}

message UgcGetUgcPlayerInfo_S2C_Msg {
  repeated UgcPlayerProfile ugcPlayerProfileList = 1; // 返回结果
}

enum PageType {
  SubscribePlayerMaps = 1;   //订阅的人的地图
  PlayedMaps = 2; //游玩过的地图
  PublishMaps = 3; //发布的地图
  CollectMaps = 4; // 收藏的地图
}

// 获取订阅的人、游玩过的地图们
message UgcGetMaps_C2S_Msg {
  optional int64 uid = 1; // uid
  optional int32 page = 2; //第几页(0页开始)
  optional PageType pageType = 3;// 类型
  optional int64 creatorId = 4;
  optional GetMapsSortType sortType = 5;
  optional int32 sceneType = 6; // 0默认 1星世界订阅作者地图列表
  optional int32 sortMapType = 7;  // 对应UGCMapType, 目前只支持:兽人(36)
  optional int32 pointsNumber = 8;  // 地图人数, 目前只支持:兽人(36)
  optional int32 mapFilterType = 9; // 生效类型:PlayedMaps   0全部 1存档 2通关
}

message UgcGetMaps_S2C_Msg {
  optional int32 hasMorePage = 1; //1:表示还有下一页,0没有下一页
  repeated PublishItem ugcMapProfileList = 2; // 返回结果
  repeated int64 collectMapIdList = 3; // 返回结果里面收藏的地图id列表
  optional UgcKeyInfo keyInfo = 4;
  optional int32 count = 5;   // 生效类型:CollectMaps
  optional int32 maxCount = 6;    // 生效类型:CollectMaps
}

message UgcCoverCheck_C2S_Msg {
  optional int64 ugcId = 1; //实体id
  optional string version = 2;
}

message UgcCoverCheck_S2C_Msg {
  optional int32 result = 1;
}

// 地图状态有变化
message UgcMapInfoNtf {
  optional int64 mapId = 1; // 地图ID
  optional SafeStatus mapStatus = 2; // 地图状态
}


// Ugc地图下载
message UgcDownload_C2S_Msg {
  option (ugc_app_forward) = true;
  optional uint64 ugcId = 1; // 作品id
  optional bool isMulTest = 2; // 是否多人测试
}

message UgcDownload_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional UgcKeyInfo keyInfo = 2;
  optional string bucket = 3;
  optional string region = 4;
  optional int64 ugcId = 5; // 地图id
}

message UgcGenAiImage_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string prompt = 1; //玩家输入的提示词
  repeated int32 StyleId = 2; // 模型id [0,1,2]
  optional bool isDefaultPrompt = 3; // 是否官方主题
}

message UgcGenAiImage_S2C_Msg {
}

message  UgcAiImageGenSucNtf{
  repeated string imagePath = 1;
  optional UgcKeyInfo keyInfo = 2;
  optional string bucket = 3;  //bucket
  optional string region = 4;
  optional int32 errorCode = 5;
  optional string errorMsg = 6;
}

message UgcSaveResultNtf {
  optional int64 ugcId = 1;         // 实体id
  optional int32 result = 2;        // 检测结果
}

// 封面图检测结果ntf消息
message UgcCoverCheckResultNtf {
  optional int64 ugcId = 1;         // 实体id
  optional int32 result = 2;        // 检测结
}

message UgcAiChangeColor_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string theme = 1; //玩家输入的提示词
  repeated int32  ids = 2;
  optional UgcAiChangeColorNativeInfo nativeInfo = 3;
  optional string board = 4;  // 玩家输入的色板，json string
}
message UgcAiChangeColor_S2C_Msg {
}

message UgcAiChangeColorResultNtf{
  repeated string results = 1;
  optional UgcKeyInfo keyInfo = 2;
  optional string bucket = 3;  //bucket
  optional string region = 4;
  optional int32 errorCode = 5;
  optional string errorMsg = 6;
}

//弃用 统一用SaveWork_C2S_Msg
message UgcModifyPublishMeta_C2S_Msg {
  optional int64 ugcId = 1;         // ugcId
  repeated UgcMapMetaInfo info = 2; // 作品内容
  optional int32 editorSec = 3;     // 编辑时长
  repeated int64 groupIds = 4;      // 组合id
  //  optional string ugcVersion = 5;   // 当前ugc版本号
  optional bool isAutoSave = 6;     // 是否自动保存
}

message UgcModifyPublishMeta_S2C_Msg {
  optional int32 result = 1;        // 结果
}

//更新记录
message UgcPublishMetaRecord_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;         // ugcId
  optional int32 layerId = 2;       // 当前场景id
}

message UgcPublishMetaRecord_S2C_Msg {
  optional int32 result = 1;           // 结果
  repeated UgcMapMetaMap record = 2;   // (废弃)

  repeated UgcMetaOperateRecord saveRecord = 3;     // 自动保存
  repeated UgcMetaOperateRecord updateRecord = 4;     //更新记录

}

//更新
message UgcUpdatePublishMeta_C2S_Msg {
  optional int64 ugcId = 1;         // ugcId
  optional int64 metaId = 2;        // 更新已审核过后的地图信息
  repeated UgcMapMetaInfo info = 3; // 地图信息
}

message UgcUpdatePublishMeta_S2C_Msg {
  optional int32 result = 1;        // 结果
  optional string bucket = 2;       //buck信息
  optional string region = 3;
  repeated UgcMapMetaInfo info = 4; // 地图信息
  optional UgcKeyInfo keyInfo = 5;
}


message UgcGetCreatorInfo_C2S_Msg {
  optional UgcCreatorInfo req = 1;
}

message UgcGetCreatorInfo_S2C_Msg {
  optional UgcCreatorInfo res = 1;
}

//----------------------ugc共创协议 start-------------------------------------
//编辑
message UgcCoCreateEditor_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //ugcId
  optional int64 layerId = 2;    //layerId
  optional int32 editMode = 3;		// 编辑模式, 见枚举UgcMapEditMode所示
}

message UgcCoCreateEditor_S2C_Msg {
  optional int32 result = 1;    //结果
}

//退出编辑
message UgcCoCreateExitEditor_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //ugcId
  optional int64 layerId = 2;    //layerId
  optional int32 editMode = 3;		// 编辑模式, 见枚举UgcMapEditMode所示
}

message UgcCoCreateExitEditor_S2C_Msg {
  optional int32 result = 1;    //结果
}

//编辑ntf
message UgcCoCreateExitEditorNtf {
  optional int64 ugcId = 1;  //ugcId
  optional int64 layerId = 2;    //layerId
  optional string nickName = 3;  //昵称
  optional int64 uid = 4;  //uid
  repeated EditorItemInfo itemInfos = 5;	// 共创地图当前编辑者列表
}

//申请图层id
message ApplyUgcLayerId_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //ugcId
  optional int32 count = 2;  //图层数量 ，服务器检验count必须为1
  optional UgcInstanceType instanceType = 3; // 实例类型
  optional VersionType version = 4; // VersionType 兼容字段 可以不填写
  optional string layerName = 5;    // 图层名字
  optional string desc = 6;
  optional int32 pointNumber = 7;
  optional int64 templateId = 8;
}

message ApplyUgcLayerId_S2C_Msg {
  optional int32 result = 1;      //结果
  repeated int32 layerIds = 2;    //图层id集合 ，新建的图层id集合
  optional UgcLayerList layers = 3;// 图层数据
}


//切换图层id
message ChangeUgcLayerId_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //ugcId
  optional int32 layerId = 2;  // 切换场景id
  optional UgcInstanceType instanceType = 3; // 实例类型
  optional VersionType version = 4; // VersionType
}

message ChangeUgcLayerId_S2C_Msg {
  optional int32 result = 1;        //结果
  optional int32 curLayerId = 2;    // 当前图层id
}

//删除图层
message RemoveUgcLayer_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  //ugcId
  optional int32 layerId = 2;  // 场景id
  optional UgcInstanceType instanceType = 3; // 实例类型
}

message RemoveUgcLayer_S2C_Msg {
  optional int32 result = 1;      //结果
  optional int32 layerId = 2;     //删除图层id
  optional UgcLayerList layers = 3;// 图层数据
}


//授权图层
message AccreditLayerByCreatorId_C2S_Msg {
  optional int64 ugcId = 1;      //ugcId
  optional int64 layerId = 2;    //layerId
  optional int64 creatorId = 3;  //creatorId
}

message AccreditLayerByCreatorId_S2C_Msg {
  optional int32 result = 1;      //结果
}

message UgcCoCreateEditorHeart_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;      //ugcId
  optional int64 layerId = 2;    //layerId
  optional int32 editMode = 3;		// 编辑模式, 见枚举UgcMapEditMode所示
}

message UgcCoCreateEditorHeart_S2C_Msg {
  optional int32 result = 1;    //结果
}

// 邀请
message UgcInviteCoCreateMap_C2S_Msg{
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;         // ugcId
  optional int64 inviteUid = 2;
}
message UgcInviteCoCreateMap_S2C_Msg{
}
// 邀请ntf
message UgcCoCreateInviteNtf {
  optional PlayerPublicInfo inviterInfo = 1;
  optional int64 ugcId = 2;// 地图id
  optional int32 intimacy = 3; // 亲密度
}
// 回复邀请
message UgcCoCreateInviteReply_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;// 地图id
  optional int64 inviterUid = 2; // 邀请人uid
  optional InvitationType resType = 3; // 回应类型
}
message UgcCoCreateInviteReply_S2C_Msg {
}
// 踢除
message UgcRemoveCoCreator_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;// 地图id
  optional int64 removeUid = 2;
}
message UgcRemoveCoCreator_S2C_Msg {
}
// 主动退出
message UgcQuitCoCreateMap_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;// 地图id
}
message UgcQuitCoCreateMap_S2C_Msg {
}
// 成员变动通知
message UgcCoCreatorModifyNtf {
  optional EditorItemInfo member = 1;
  optional int64 ugcId = 2;
  optional ModifyType modify = 3;
}
//----------------------ugc共创协议 end---------------------------------------

message UgcGetAllPresetTopics_C2S_Msg {

}

message UgcGetAllPresetTopics_S2C_Msg {
  repeated UgcMapTopic topics = 1;
  optional string icon = 2;
  optional string introduction = 3;
}

//首页推荐
message UgcHomePageRecommend_C2S_Msg {
  optional bool isIgnoreCache = 1;
  optional int32 tabId = 2;
  optional UgcHomePageRecommendScene scene = 3;
  optional string abTestInfo = 4;
}

message UgcHomePageRecommend_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated UgcHomePageRecommendData data = 2;
  optional UgcKeyInfo keyInfo = 3;
  repeated UgcHomePageRecommendModuleInfo moduleInfo = 4;
}

message UgcHomePageThemeAllMaps_C2S_Msg {
  optional string themeId = 1;  // UHPRT_Theme
  optional UgcHomePageRecommendType type = 2;
  optional int32 tabId = 3;
  optional UgcHomePageRecommendScene scene = 4;
}

message UgcHomePageThemeAllMaps_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional UgcHomePageRecommendData data = 2;
  optional UgcKeyInfo keyInfo = 3;
}

message UgcRecommendSet_C2S_Msg {
  optional int32 setType = 1; // 集合类型 UgcRecommendTypeSet
  optional string set_id = 2; // 第二个版本数据，注意：使用这个就不用赋值setType了，他和setType只会生效一个
  optional int32 page = 3; // 当前页数
  optional string ver = 4;//version old is "". new is "1.0.1"
  optional int32 source = 5; // the source for UgcRecommendSetSource
}

message UgcRecommendSet_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional UgcRecommendDataSet data = 2;
  optional int32 hasMorePage = 3; //1:表示还有下一页,0没有下一页
}


message UgcGetUserFeedbackData_C2S_Msg {
  optional com.tencent.wea.xlsRes.UgcFeedbackDataType type = 1;
}

message UgcGetUserFeedbackData_S2C_Msg {
  optional com.tencent.wea.xlsRes.UgcFeedbackDataType type = 1;
  optional UgcDailyReport dailyReport = 2;
}

message UgcRedPointNtf {
  optional com.tencent.wea.xlsRes.UgcRedPointType type = 1;
}

message UgcLvChangeNtf {
  optional uint32 level = 1;
}

//叮一下
message UgcCoCreateDing_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;// 地图id
  optional int64 creatorId = 2;// 作者
  optional int64 layerId = 3;// 图层id
}
message UgcCoCreateDing_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

/* 叮ntf */
message UgcCoCreatorDingNtf {
  optional int64 creatorId = 1;  //creatorId
  optional string nickName = 2;  //昵称
  optional int64 uid = 3;  //uid
  optional UgcEditorType editorType = 4;  //身份
}

message UgcCoCreateWhite_C2S_Msg {
  option (ugc_app_forward) = true;
}

message UgcCoCreateWhite_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

message UgcLayerIsApply_C2S_Msg {
  optional int64 ugcId = 1;// 地图id
  optional int64 layerId = 2;// 图层id
}

message UgcLayerIsApply_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

message UgcGetHotPlaying_C2S_Msg {
  optional int32 page = 1; // 0为起始页
  repeated UgcHotPlayingMapInfo map_list = 2; // 好友在玩地图
}

message UgcGetHotPlaying_S2C_Msg {
  repeated UgcHotPlayingStoryInfo story_list = 1; // 好友动态。page=0时才有
  repeated UgcHotPlayingMapInfo map_list = 2; // 好友在玩地图
  optional AlgoInfo info = 3;
  optional UgcKeyInfo keyInfo = 4;
  optional int32 page = 5;
}

message UgcSvrTest_C2S_Msg{
  optional bytes content = 1;
  optional int32 type = 2;
}

message UgcSvrTest_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

// 获取发布地图时玩法标签置顶的内容
message UgcGetPublishTags_C2S_Msg {
}

message UgcGetPublishTags_S2C_Msg {
  optional string tagMsg = 1;    // 置顶显示的文本
  repeated int32 tags = 2;       // 激励标签
}

message ClientClickPanel_C2S_Msg {
  optional com.tencent.wea.xlsRes.ClientPanelName panel = 1;
}
message ClientClickPanel_S2C_Msg {
}

message UgcSingleLevelGetDressInfo_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int32 templateId = 1;
}

message UgcSingleLevelGetDressInfo_S2C_Msg {
  optional int32 templateId = 1;
  repeated int32 dressUpItems = 2;
}

message UgcGenAiVoice_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int32 workFlow = 1; // 1.tts  2.对话  3.tts+对话
  optional string queryText = 2; // 玩家输入的文本
  optional UgcAiTTSParam ttsParam = 3;  // 老平台tts参数
  optional int32 ttsPlat = 4;  // tts平台：1.新平台 0.老平台
  optional UgcNewAiTTSParam newTtsParam = 5;   // 新平台tts参数
}

message UgcGenAiVoice_S2C_Msg {
}

message UgcAiVoiceGenSucNtf{
  repeated string queryText = 1;
  repeated string cosPath = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional string bucket = 4;  //bucket
  optional string region = 5;
  optional int32 errorCode = 6;
  optional string errorMsg = 7;
  optional UgcAiTTSParam ttsParam = 8;
  optional int32 workFlow = 9;
  optional int32 ttsPlat = 10;  // tts平台：1.新平台 0.老平台
  optional UgcNewAiTTSParam newTtsParam = 11;   // 新平台tts参数
}

message UgcGenAiAnicap_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string roleId = 1;
  optional string moviePath = 2;
  optional int32 genQuality = 3;
  optional UgcAiMovieParam movieParam = 4;
}

message UgcGenAiAnicap_S2C_Msg {
}

message UgcAiAnicapGenSucNtf{
  optional string roleId = 1;
  optional string animPath = 2;
  optional string resultPath = 3;
  optional UgcKeyInfo keyInfo = 4;
  optional string bucket = 5;
  optional string region = 6;
  optional int32 errorCode = 7;
  optional string errorMsg = 8;
  optional string moviePath = 9;
  optional int32 genQuality = 10;
}

message UgcAiAnicapQueueNtf {
  optional string roleId = 1;
  optional string moviePath = 2;
  optional int32 queueTime = 3;
}

message UgcAnicapCancelQueue_C2S_Msg {
  optional string roleId = 1;
  optional string moviePath = 2;
}

message UgcAnicapCancelQueue_S2C_Msg {
  optional string roleId = 1;
  optional string moviePath = 2;
}

message UgcGenAiAnswer_C2S_Msg {
  optional int32 type = 1;
  optional string roleid = 2;
  optional string query = 3;
  optional proto_CosImage image = 4; // 预览图cos
  optional bool isQuickReply = 5;  // 是否快捷回复, 用于tlog
  optional KVArray kvArray = 100;
}

message UgcGenAiAnswer_S2C_Msg {
  optional int64 chatId = 1;
}

message UgcAiChangeActionState_C2S_Msg {
  optional int32 state = 1;
  optional int32 type = 2; // 1进入 2退出
  optional string roleid = 3;
}

message UgcAiChangeActionState_S2C_Msg {
  optional int32 state = 1;
}

message UgcAiImageCheck_C2S_Msg {
  optional proto_CosImage image = 1; // 预览图cos
  optional ApplyReason applyReason = 2;
}

message UgcAiImageCheck_S2C_Msg {
  optional int32 result = 1;
  optional UgcKeyInfo info = 2;  //返回具体信息
  optional string bucket = 7;
  optional string region = 8;
}

// 回复改为流式传输,需要client判断requestId,同id的为同一句回复
// 非full的消息response可能不完整, 最后一个full消息有完整的response
// 相同requestId后一个的answer是包含之前的answer文字的, client可以直接替换
message UgcAiAnswerGenSucNtf{
  optional int32 type = 1;
  optional string answer = 2;       // 经过安检的回复,client需使用这个字段,不要直接用response中的原文
  optional int32 errorCode = 3;
  optional string errorMsg = 4;
  optional string roleid = 5;
  optional string query = 6;
  optional string requestId = 7;    // 唯一id
  optional string sessionId = 8;
  optional string roundId = 9;
  optional ToneChatResponseContent response = 10;
  optional bool full = 11;    // 是否整句
  optional int64 chatId = 12;
}

message GetCommonGuideStep_C2S_Msg {
  optional com.tencent.wea.xlsRes.CommonGuideType type = 1;
}

message GetCommonGuideStep_S2S_Msg {
  optional int64 currentStep = 1;
}

message FinishCommonGuideStep_C2S_Msg {
  optional com.tencent.wea.xlsRes.CommonGuideType type = 1;
}

message FinishCommonGuideStep_S2C_Msg {
  optional int64 currentStep = 1;
}

message UgcGetCollectStarActivityMap_C2S_Msg {
}

message UgcGetCollectStarActivityMap_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem mapList = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional int64 refreshMapTime = 4;
  optional int32 todayBattleRewardNum = 5; // 今日局内获得
  optional bool isMapRefresh = 6; // 地图刷新
}

//搜索前置
message UgcMapSearchFront_C2S_Msg {
}

message UgcMapSearchFront_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated string hotContent = 2;  //热搜内容（固定4条热搜）(废弃)
  optional PublishStruct hotMaps = 3; //热搜地图（固定4张地图）
  optional PublishStruct likeMaps = 4; //喜欢地图（固定4张地图）
  optional UgcKeyInfo keyInfo = 5;
  optional string hotKeyWord = 6;     //默认热搜关键词(废弃)

  repeated HotContent contents = 7;    //热搜内容（固定4条热搜）
  optional HotContent keyWord = 8;    //默认热搜关键词
  repeated TopicInfoItem topics = 9;    //热搜话题列表
}

//话题详情
message UgcSearchTopicDetail_C2S_Msg {
  optional string topicName = 1;
  optional int32 page = 2;
  optional TopicTyp topicType = 3;
  optional int32 source = 4;  // 6：搜索结果话题 7：首页热门话题搜索

}

message UgcSearchTopicDetail_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional TopicInfoItem topicInfo = 2;  //话题名称
  optional PublishStruct topicMaps = 3; //话题地图
  optional UgcKeyInfo keyInfo = 4;
}

message UgcStarWorldRedDot_C2S_Msg {
  optional bool redDot = 1;   // true需要显示红点，false取消红点
}
message UgcStarWorldRedDot_S2C_Msg {
  optional int32 result = 1; // 返回结果
}

message UgcMatchLobbyDetail_C2S_Msg {

}

message UgcMatchLobbyDetail_S2C_Msg {
  optional int32 errCode = 1;
  repeated UgcMatchLobbyMapBrief pubBriefs = 2;
  optional UgcKeyInfo keyInfo = 3;
}

// 废弃
message UgcMatchRoomUgcIdChange_C2S_Msg {
  optional int32 roomId = 1;
  optional int32 ugcId = 2;
  optional int32 roomInfoId = 3;
}

message UgcMatchRoomUgcIdChange_S2C_Msg {
  optional int32 errCode = 1;
}

// 地图是否可以匹配
message UgcMatchRoomUgcIdValidCheck_C2S_Msg {
  optional int64 ugcId = 1;
  optional int64 ugcCfgId = 2;
}

message UgcMatchRoomUgcIdValidCheck_S2C_Msg {
  optional int32 errCode = 1;
  optional int32 roomInfoId = 2;
  optional int64 openTime = 3;
  optional int64 closeTime = 4;
}

message UgcRoomLobbyMap_C2S_Msg {
  optional UgcRoomLobbyMapType type = 1;
  optional int32 page = 2;
  optional int32 MapType = 3;
  optional uint32 tag = 4;  // 地图标签
  optional UgcMapScreenServerData serverData = 5;  // 使用客户端的缓存地图来拉取数据
}

message UgcRoomLobbyMap_S2C_Msg {
  optional int32 errCode = 1;
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional AlgoInfo info = 4;
  repeated HotPlayList typeList = 5;
  optional int32 hasMorePage = 6; //1:表示还有下一页,0没有下一页 只给收藏用
  optional int32 page = 7;  // 请求的页数
  optional UgcMapScreenServerData serverData = 8;  // 使用客户端的缓存地图来拉取数据
}

enum UgcTextLawfulSubScene {
  SUB_SCENE_CUSTOM_ACTIONS_NAME = 155;          // 自定义动作命名
}

message UgcTextLawful_C2S_Msg {
  optional string text = 1;
  optional UgcTextLawfulSubScene subScene = 2;
}

message UgcTextLawful_S2C_Msg {
  optional bool result = 1;
}

message VideoExamineFailedNtf {
  optional string moviePath = 1;
  optional int32 retCode = 2;
  optional string errMsg = 3;
  optional int32 result = 4;
  optional string resultDesc = 5;
}

// ******************  资源相关协议 start  **********************************
// 资源社区——首页推荐
message UgcResHomePageRecommend_C2S_Msg {
  option (ugc_app_forward) = true;
}
message UgcResHomePageRecommend_S2C_Msg {
  optional int32 result = 1;                     // 结果
  optional UgcResHomePageInfo homePageData = 2;  // 首页推荐信息
}
// 资源社区——首页推荐中集合的获取更多
message UgcResHomePageRecommedMoreSet_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int32 index = 1;                      // 集合index
}
message UgcResHomePageRecommedMoreSet_S2C_Msg {
  optional int32 result = 1;                    // 结果
  repeated PublishItem items = 2;               // 资源信息
  optional UgcKeyInfo keyInfo = 3;              // cos info
  optional AlgoInfo info = 4;                   // 推荐算法信息
}
// 资源社区——搜索
message UgcResCommunitySearch_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string name = 1;            // 搜索 字符串/资源id
  optional int32 page = 2;             // 页号
  optional bool searchAllType = 3;     // 是否在全部中搜索(是的情况下忽略 resType)
  optional UgcResType resType = 4;     // 资源类型(searchAllType=false的情况才有效)
  optional string sessionId = 5;       // 搜索链路透传的id(由管理端生成，不切页签时需要全链路传递)
  optional int32 searchType = 6;       // 搜索类型  1= 主动搜索, 2= 历史词搜索, 3= 热搜词搜索, 4= 推荐词搜索, 5= 话题详情搜索
  optional bool isGetAppData = 7;      // 是否是拉取独立App端数据
  optional bool isOnlyAI = 8;          // 是否只搜索AI模组
}
message UgcResCommunitySearch_S2C_Msg {
  optional int32 result = 1;             // 结果
  repeated PublishItem items = 2;        // 列表
  optional UgcKeyInfo keyInfo = 3;       // cos key
  optional bool isFoundId = 4;           // 是否命中了资源id
  optional PublishItem specifyItem = 5;  // 命中的资源
  optional string sessionId = 6;
  optional AlgoInfo info = 7;            // 推荐算法信息
}
// 资源社区——拉取列表
message UgcResCommunityGet_C2S_Msg {
  option (ugc_app_forward) = true;
  optional UgcResType resType = 1;             // 资源类型(必填)
  optional int32 page = 2;                     // 页号(必填)
  optional UgcMapScreenType recommedType = 3;  // 推荐类型(必填)
  optional int32 category = 4;                 // 大类别(可选)
  optional int32 subCategory = 5;              // 小类别(可选，需携带大类别才有效)
  repeated int32 labels = 6;                   // 标签(可选，需携带大类别才有效)
}
message UgcResCommunityGet_S2C_Msg {
  optional int32 result = 1;             // 结果
  repeated PublishItem items = 2;        // 列表
  optional UgcKeyInfo keyInfo = 3;       // cos key
  optional AlgoInfo algoInfo = 4;        // 推荐算法信息
}
// 资源背包——加入资源背包
message UgcResBagAdd_C2S_Msg {
  option (ugc_app_forward) = true;
  optional UgcResType resType = 1;
  optional int64 ugcId = 2;
}
message UgcResBagAdd_S2C_Msg {
  optional int32 result = 1;
}
// 资源背包——从资源背包批量移除
message UgcResBagDelete_C2S_Msg {
  option (ugc_app_forward) = true;
  optional UgcResType resType = 1;
  repeated int64 ugcId = 2;
}
message UgcResBagDelete_S2C_Msg {
  optional int32 result = 1;          // 结果
  repeated int64 succIds = 2;         // 成功的id
  repeated int64 failIds = 3;         // 失败的id
}
// 资源背包——拉取资源背包
message UgcResBagGet_C2S_Msg {
  option (ugc_app_forward) = true;
  optional UgcResType resType = 1;    // 资源类型
  optional int32 page = 2;            // 页号
  optional int32 category = 3;        // 大类别(可选)
  repeated int32 labels = 4;          // 标签(可选)
  optional bool isGetAppData = 5;     // 是否是拉取独立App端数据
}
message UgcResBagGet_S2C_Msg {
  optional int32 result = 1;             // 结果
  repeated PublishItem items = 2;        // 列表
  optional UgcKeyInfo keyInfo = 3;       // cos key
  optional int32 count = 4;              // 该类型数量
  optional bool hasMore = 5;             // 是否还有下一页
  optional int32 capacity = 6;           // 该类资源背包上限
}
// 资源背包——搜索资源背包（跨资源类别搜索）
message UgcResBagSearch_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string name = 1;            // 搜索字符串
  optional int32 page = 2;             // 页号
  optional bool searchAllType = 3;     // 是否在全部中搜索(是的情况下忽略 resType,category,labels)
  optional UgcResType resType = 4;     // 资源类型(searchAllType=false的情况才有效)
  optional int32 category = 5;         // 大类别(可选，searchAllType=false的情况才有效)
  repeated int32 labels = 6;           // 标签(可选，searchAllType=false 且 携带大类别才有效)
  optional int32 scene = 7;            // 入口场景
  optional string tabName = 8;         // 标签名
  optional bool isGetAppData = 9;      // 是否是拉取独立App端数据
}
message UgcResBagSearch_S2C_Msg {
  optional int32 result = 1;             // 结果
  repeated PublishItem items = 2;        // 列表
  optional UgcKeyInfo keyInfo = 3;       // cos key
  optional bool hasMore = 5;             // 是否还有下一页
}
// 资源操作——创建资源
message UgcResCreate_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string name = 1;          // 名称
  optional UgcResType resType = 2;   // 资源类型
  optional UgcExtraInfo extraInfo = 3; //额外信息
  optional bool isResPubCosPath = 4; // 私有资源是否在pub路径
}
message UgcResCreate_S2C_Msg {
  optional int32 result = 1;   // 0:成功
  optional int64 ugcId = 2;    // 草稿id
  optional string bucket = 3;  // cos 桶名
  optional string region = 4;  // cos-region
  optional UgcKeyInfo keyInfo = 5;       // cos key
  optional string cosCoverPath = 6;  // cos cover 路径
  optional string cosPbinPath = 7;  // cos pbin 路径
}
// 资源操作——发布资源
message UgcResPublish_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;              // 草稿id
  optional string name = 2;              // 名字
  optional string desc = 3;              // 描述
  repeated UgcMapMetaInfo metaInfo = 4;  // metaInfo 信息
  optional int64 publishId = 5;          // 发布id
  optional int32 category = 6;           // 大类别
  optional int32 subCategory = 7;        // 小类别
  repeated int32 labels = 8;             // 标签
  optional UgcExtraInfo extraInfo = 9;   //额外信息
  optional UgcResType ugcResType = 10;    // 资源类型
  repeated uint32 goldTopics = 11;       // 金色话题
  repeated string blueTopics = 12;       // 蓝色话题
}
message UgcResPublish_S2C_Msg {
  optional int32 result = 1; // 0:成功
}
// 资源操作——拉取我的资源列表
message UgcResGetMyList_C2S_Msg {
  option (ugc_app_forward) = true;
  // 指定类型拉取  保留repeated能力，实际应该只填一种。兼容旧协议，不填表示拉取模组类型的数据
  repeated UgcResType resType = 1;
  optional bool isGetAppData = 2;       // 是否是拉取独立App端数据
  optional int32 page = 3;  // 页数
}
message UgcResGetMyList_S2C_Msg {
  optional int32 result = 1;          // 返回结果
  optional UgcKeyInfo keyInfo = 2;    // cos key
  repeated UgcItem groups = 3;        // 列表
  optional int32 allCount = 4;        // 总数量
  optional bool hasMore = 5;          // 是否还有下一页
}
// 资源操作——删除草稿资源
message UgcResDelete_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  // id
  optional UgcResType ugcResType = 2;     // 资源类型
}
message UgcResDelete_S2C_Msg {
  optional int32 result = 1; // 结果
  optional int64 ugcId = 2;  // id
}
// 资源操作——下架发布资源
message UgcResTakeOff_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  // id
  optional UgcResType ugcResType = 2;     // 资源类型
}
message UgcResTakeOff_S2C_Msg {
  optional int32 result = 1; // 结果
  optional int64 ugcId = 2;  // id
}
// 资源操作——点赞/收藏/取消点赞/取消收藏
message UgcResOperate_C2S_Msg {
  option (ugc_app_forward) = true;
  optional ObjectType type = 1;        // 1普通物件 2资源
  optional ObjectOperate operate = 2;
  optional int64 ugcId = 3;
  optional AlgoInfo algoInfo = 4;
  optional UgcResType ugcResType = 5;
}
message UgcResOperate_S2C_Msg {
  optional int32 result = 1; // 结果
}
// 资源操作——修改已发布信息
message UgcResModifyPublish_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;                 // id
  optional UgcResModifyPubParam param = 2;  // 修改参数
}
message UgcResModifyPublish_S2C_Msg {
  optional int32 result = 1; // 结果
}
// 资源操作——拉取收藏
message UgcResGetCollect_C2S_Msg {
  option (ugc_app_forward) = true;
  optional bool isGetAppData = 1;           // 是否是拉取独立App端数据
}
message UgcResGetCollect_S2C_Msg {
  optional int32 result = 1;                // 返回结果
  repeated UgcItem selfItems = 2;           // 收藏的草稿资源
  repeated PublishItem items = 3;           // 收藏的发布资源
  repeated UgcGroupCommon groupCommons = 4; // 组件收藏信息
  optional UgcKeyInfo keyInfo = 5;
}
// 资源操作——获取话题
message UgcResGetTopic_C2S_Msg {
  option (ugc_app_forward) = true;
}
message UgcResGetTopic_S2C_Msg {
  repeated UgcMapTopic topics = 1;
  optional string introduction = 2;
}
// 资源操作——获取详情
message UgcResGetPublishDetail_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;               // id
  optional UgcResType ugcResType = 2;     // 资源类型
}
message UgcResGetPublishDetail_S2C_Msg {
  optional int32 result = 1;        //返回结果
  optional MapDetails details = 2;  //详情
  optional int64 ugcId = 3;         //ugcId
  optional PublishItem item = 4;    //发布信息
}
// 资源操作——私有资产数据兼容——M10: 私有资产数据挪到pub目录
message UgcResPrivateAdapt_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  repeated UgcMapMetaInfo metaInfo = 2;  // metaInfo 信息
  optional UgcResType ugcResType = 3;     // 资源类型
}

message UgcResPrivateAdapt_S2C_Msg {
  optional int32 result = 1;        //返回结果
}
// ******************  资源相关协议 end  **********************************

message UGCSearchSuggestion_C2S_Msg {
  optional SearchType searchType = 1;
  optional string inputText = 2;
}

message UGCSearchSuggestion_S2C_Msg {
  optional int32 result = 1;
  repeated string keyWord = 2;
  optional AlgoInfo info = 3;
  optional string inputText = 4;
}

message UgcAiGenModule_C2S_Msg {
  option (ugc_app_forward) = true;
  optional string setting = 4;  // 建筑生成配置，json string
  optional int32 typeId = 5;
  optional int64 GenId = 6;		// 唯一标识使用
}
message UgcAiGenModule_S2C_Msg {
}

message UgcAiGenModuleResultNtf{
  repeated string results = 1;
  optional UgcKeyInfo keyInfo = 2;
  optional string bucket = 3;  //bucket
  optional string region = 4;
  optional int32 errorCode = 5;
  optional string errorMsg = 6;
  optional int64 GenId = 7;		// 唯一标识使用
}

message UgcNewYearActivity_C2S_Msg {
  repeated int64 mapIds = 1;          // 废弃
  optional bool needConfs = 2;
  repeated int32 tabIds = 3;
}

message UgcNewYearActivity_S2C_Msg {
  repeated PublishItem mapInfos = 1;  // 废弃
  repeated com.tencent.wea.xlsRes.UgcNewYearConf confs = 2;
  repeated com.tencent.wea.xlsRes.UGC_ActivityCollection activityCollections = 3;
  repeated int64 steps = 4;
  repeated com.tencent.wea.xlsRes.ActivityMainConfig activityConfs = 5;
  repeated bool passed = 6;
  optional bool activityClosed = 7;
  optional string reserved = 8;
}

message UgcMulTestSaveMeta_C2S_Msg {
  optional uint64 ugcId = 1; // 地图id
  repeated UgcMapMetaInfo info = 2; //cos
  optional uint32 pointNumber = 3; // 出生点人数
  repeated BattleCamp camps = 4;
  optional UgcLayerList layers = 5;// 图层数据
  optional UgcPublishInputParam publishInputParam = 6;// 发布参数统一放在这里
}

message UgcMulTestSaveMeta_S2C_Msg {
  optional int32 result = 1;
}

message UgcAigcUse_C2S_Msg{
  option (ugc_app_forward) = true;
  optional AigcType aigcType = 1;
  optional AigcHistoryData use = 2;
  optional int32 op = 3; // 0使用 1预览
}

message UgcAigcUse_S2C_Msg {
  optional string results = 1;
  optional AigcHistoryData add = 2;
  optional AigcHistoryData remove = 3;
}

message UgcAigcDelHistory_C2S_Msg{
  option (ugc_app_forward) = true;
  optional AigcType aigcType = 1;
  optional string delFile = 2;
  optional int32 op = 3; // 0最近使用 1全部
}

message UgcAigcDelHistory_S2C_Msg {
  optional string results = 1;
  optional AigcHistoryData remove = 2;
}

message UgcAigcGetHistory_C2S_Msg{
  option (ugc_app_forward) = true;
  optional AigcType aigcType = 1;
  optional int32 op = 2; // 0最近使用 1全部
}

message UgcAigcGetHistory_S2C_Msg {
  optional string results = 1;
  repeated AigcHistoryData data = 2;
  optional UgcKeyInfo keyInfo = 3;
  optional string bucket = 4;  //bucket
  optional string region = 5;
}
message UgcMapGroupList_C2S_Msg {
  optional uint64 ugcId = 1; // 地图id
}

message UgcMapGroupList_S2C_Msg {
  optional int32 result = 1;
  optional UgcGroupIdList groupIds = 2; // 地图中使用到的发布组合
}

message UgcBatchPublishMap_C2S_Msg {
  repeated int64 ugcIds = 1;
  optional string fromCollectionId = 2;
  optional int32 source = 3;  // 客户端请求来源,UgcTableBatchGetSource
}

message UgcBatchPublishMap_S2C_Msg {
  optional int32 result = 1; // 返回结果
  repeated PublishItem items = 2;
  optional UgcKeyInfo keyInfo = 3;
}

message UgcMatchLobbyDetailEx_C2S_Msg {
  optional int32 type = 1;  // 0:主界面匹配
}

message UgcMatchLobbyDetailEx_S2C_Msg {
  optional int32 errCode = 1;
  optional int32 type = 2;
  repeated UgcMatchLobbyMapBrief pubBriefs = 3;
  optional UgcKeyInfo keyInfo = 4;
}

message UgcMatchLobbyDetailExReq {
  optional int32 type = 1;  // 0:主界面匹配, 1:小游戏匹配
  optional int32 lastPageId = 2;  // 上一次拉到的页id,UgcMatchLobbyDetailExMulti的pageId透传
  optional int64 lastSeed = 3;  // 上一次服务使用种子，UgcMatchLobbyDetailExMulti的seed透传
  optional int64 lastTime = 4;  // 上一次获取的时间，UgcMatchLobbyDetailExMulti的time透传
}

message UgcMatchLobbyDetailExMulti_C2S_Msg {
  option (forward_to_ugc) = true;
  repeated UgcMatchLobbyDetailExReq infoList = 1;
}

message UgcMatchLobbyDetailExRes {
  repeated UgcMatchLobbyMapBrief pubBriefs = 1;
  optional int32 type = 2; // 0:主界面匹配, 1:小游戏匹配
  optional int32 pageId = 3;  // 当前页id
  optional int64 seed = 4;  // 服务使用种子
  optional int64 time = 5;  // 获取时间
}

message UgcMatchLobbyDetailExMulti_S2C_Msg {
  optional int32 errCode = 1;
  repeated UgcMatchLobbyDetailExRes detailList = 2;
  optional UgcMatchLobbyMapConfig config = 3;
}

message UgcMatchLobbyDetailExChange_C2S_Msg {
  option (forward_to_ugc) = true;
  optional UgcMatchLobbyDetailExReq info = 1;  // 请求参数
}

message UgcMatchLobbyDetailExChange_S2C_Msg {
  optional int32 errCode = 1;
  optional UgcMatchLobbyDetailExRes detail = 2;  // 结果响应
}

message UgcMatchLobbySummeryPage {
  optional int32 pageId = 1;  // 当前页id，1开始
  optional int64 seed = 2;  // 种子 请透传
  optional int64 time = 3;  // 时间 请透传
  optional bool hasNext = 4;  // 是否还有下一页
}

message UgcMatchLobbySummeryReq {
  optional int32 modeId = 1;  // 玩法模式
  optional UgcMatchLobbySummeryPage page = 2;  // 页参数
}

message UgcMatchLobbySummeryRes {
  optional int32 modeId = 1; // 玩法模式
  optional UgcMatchLobbySummeryPage page = 2;  // 页参数
  repeated UgcMatchLobbySummery summery = 3;  // 结果信息
}

// 星世界玩法选择界面配置拉取接口
message UgcMatchLobbySummery_C2S_Msg {
  option (forward_to_ugc) = true;
  optional UgcMatchLobbySummeryReq req = 1;  // 页面拉取参数
}

message UgcMatchLobbySummery_S2C_Msg {
  optional int32 errCode = 1;
  optional UgcMatchLobbySummeryRes res = 2;  // 结果响应
  optional UgcMatchLobbyMapConfig config = 3;  // 配置信息
}

// 星世界玩法选择界面配置指定模式id拉取接口
// 用于首页没有mode玩法模式的情况下的配置拉取
// 小游戏专区不允许使用这个接口，因为改区排序由服务生成，无法指定
message UgcMatchLobbySummerySpecified_C2S_Msg {
  option (forward_to_ugc) = true;
  repeated int32 modeTypeIdList = 1;  // 一次请求不允许超过20个 多的请多次请求
}

message UgcMatchLobbySummerySpecified_S2C_Msg {
  optional int32 errCode = 1;
  repeated UgcMatchLobbySummery summery = 2;  // 结果响应
  optional UgcMatchLobbyMapConfig config = 3;  // 配置信息
}

message UgcMatchLobbySummerySpecifiedByMapId {
  optional int64 ugcId = 1;
  optional int32 matchTypeId = 2;
}

message UgcMatchLobbySummerySpecifiedByMapId_C2S_Msg {
  option (forward_to_ugc) = true;
  repeated UgcMatchLobbySummerySpecifiedByMapId mapIds = 1;  // 一次请求不允许超过20个 包括多图和单图的id
}

message UgcMatchLobbySummerySpecifiedByMapId_S2C_Msg {
  optional int32 errCode = 1;
  repeated UgcMatchLobbySummery summery = 2;  // 结果响应
  optional UgcMatchLobbyMapConfig config = 3;  // 配置信息
}


message UgcMatchLobbyDetailPlayRecord_C2S_Msg {

}

message UgcMatchLobbyDetailPlayRecord_S2C_Msg {
  optional int32 errCode = 1;
  repeated UgcMatchRecordSummary recordList = 2;
}

message UgcPreAudit_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  optional string urlName = 2;
  optional string version = 3;
  optional bool isAigc = 4;			// 是否是aigc场景图片
  optional int32 sceneId = 5;			//审核场景
}

message UgcPreAudit_S2C_Msg {
  optional int32 result = 1;
}

//图片检测结果ntf消息
message UgcPreAuditResultNtf {
  optional int64 ugcId = 1;         // 实体id
  optional int32 result = 2;        // 检测结果
  optional string urlName = 3;      // 透传 c2s里面的name
  optional bool isAigc = 4;			// 是否是aigc场景图片
}

message UgcStarWorldNavigationBar_C2S_Msg {
  optional string navBarId = 1;  // 导航栏id，第一级不填，其他的子级使用该结构id
}

message UgcStarWorldNavigationBar_S2C_Msg {
  optional int32 errCode = 1;  // 错误码
  repeated UgcStarWorldNavPageCfg navBar = 2;  // 只给两级的导航栏配置
}

message GetUgcCoPlayInfo_C2S_Msg {
}

message GetUgcCoPlayInfo_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional UgcCoPlayInfo ugcCoPlayInfo = 2; // UGC同游记录
}

message UgcNeedDownRes_C2S_Msg{
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  repeated int64 needResId = 2;
  optional UgcTableType tableType = 3;  //ugcId 读取表的类型
  optional bool isCarryRule = 4;        //是否需要封禁列表 false 不需要
  optional int64 editorCreatorId = 5;        //作者creatorId
}

message UgcNeedDownRes_S2C_Msg{
  optional int32 result = 1;
  optional int64 ugcId = 2;
  repeated UgcResourceInfo info = 3;  //需要下载的资产
  repeated int64 ruleId = 4;  //违规或者下架的资产id
  optional UgcKeyInfo keyInfo = 5;
  optional string extraParam = 6;  //预留
}

// 获取ugc上架星钻商品签署的协议版本
message UgcGetPublishGoodsProtoVersion_C2S_Msg {
}

message UgcGetPublishGoodsProtoVersion_S2C_Msg {
  optional int32 version = 1;
}

message UgcSetPublishGoodsProtoVersion_C2S_Msg {
    optional int32 version = 1;
}

message UgcSetPublishGoodsProtoVersion_S2C_Msg {
}

message UgcFastSlot_C2S_Msg{
  optional int32 fastCount = 1;  //快捷键  4键 6键
  repeated UgcFastKey slots = 2;  //快捷内容
}

message UgcFastSlot_S2C_Msg{
  optional int32 result = 1;
}

message UgcMapLabelScore_C2S_Msg{
  optional int64 ugcId = 1;
  repeated UgcMapLabelScoreClientInfo labelScore = 2;
  optional int32 gameSource = 3;
  optional string battleId = 4;
  optional int32 battleType = 5;
  optional int32 result = 6;
  optional int32 rank = 7;
  optional string feedBack = 8; // 最新评论
  optional string addFeedBack = 9;  // 追加评论
}
message UgcMapLabelScore_S2C_Msg{
  optional int32 result = 1;
}
message UgcMapLabelScoreClientInfo {
  optional int32 label = 1;
  optional int32 score = 2;
}

message UgcGetPlayerMapLabelScore_C2S_Msg{
  optional int64 ugcId = 1;
}
message UgcGetPlayerMapLabelScore_S2C_Msg{
  optional int32 result = 1;
  repeated UgcMapLabelScoreClientInfo labelScore = 2;
  optional string feedBack = 3; // 最新评论
}

message UgcMapDownloadInfo_C2S_Msg {
  optional UgcDownloadType ugcDownloadType = 1;
  optional int64 ugcId = 2;
}

message UgcMapDownloadInfo_S2C_Msg {
  optional int32 result = 1;
  optional UgcDownloadBaseData baseData = 2;
  optional UgcDownloadData downloadData = 3;
}

message UgcDeleteCover_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  repeated string coverId = 2;
}

message UgcDeleteCover_S2C_Msg {
  optional int32 result = 1;
  repeated MapCoverInfo coverList = 2;
}

//草稿箱上传缩略图信息
message UgcUploadCover_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  repeated MapCoverInfo coverList = 2;
}

message UgcUploadCover_S2C_Msg {
  optional int32 result = 1;
}

// 地图loading图
// 必须是白名单，或者等级足够开了权限
message UgcUploadLoading_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  optional MapLoadingInfo mapLoading = 2;
}

message UgcUploadLoading_S2C_Msg {
  optional int32 result = 1;
}

// 乐园相关自定义数据
// 必须是白名单
message UgcUploadLobbyCover_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  optional MapLobbyCoverInfo lobbyCover = 2;
}

message UgcUploadLobbyCover_S2C_Msg {
  optional int32 result = 1;
}

// 获取UGC排行榜数据
message FetchUgcTopRank_C2S_Msg {
  optional int64 mapId = 1;    // 地图ID
  optional int64 rankId = 2;   // 排行榜全局ID
  optional int32 from_index = 3;   // 拉取的起始index
  optional int32 count = 4;      // 拉取的数量
  optional bool without_info = 5;   // 是否需要玩家信息
  optional int32 type = 6;    // 0:全服  1:好友排行榜
}

message FetchUgcTopRank_S2C_Msg {
  repeated PlayerRankInfo rank = 1;
  optional PlayerRankInfo self_rank = 2;
  repeated PlayerPublicInfo info = 3;
  optional PlayerPublicInfo self_info = 4;
  optional int64 timeStamp = 5;   // 拉取时候的时间戳
  optional int32 friendNum = 6;
}

// 批量申请rankid
message UgcApplyRankId_C2S_Msg{
    optional int32 num = 1;    // 最多不超过3个  暂时策划暂时限定
}
message UgcApplyRankId_S2C_Msg{
    repeated int64 rankId = 1;    // 返回的申请的rank ID列表
}

// 更新UGC排行榜数据
message UpdateUgcRank_C2S_Msg {
  optional int64 mapId = 1;   //mapId
  optional int64 rankId = 2;   //rankId
  optional int32 score = 3;    // 上报的分数
  repeated int32 extraScores = 4;   // 额外分数
}

message UpdateUgcRank_S2C_Msg {
  optional int32 result = 1;    //返回结果
}

// 批量更新UGC排行榜数据
message BatchUpdateUgcRank_C2S_Msg {
  optional int64 mapId = 1;
  repeated UgcUpdateRankInfo updateRankItems = 2;
}

message BatchUpdateUgcRank_S2C_Msg {
  optional int32 result = 1;    //返回结果
}

// 获取自己的排行榜数据
message GetUgcRankAppointPlayer_C2S_Msg {
  optional int64 mapId = 1;    // 地图ID
  optional int64 rankId = 2;   // 排行榜全局ID
  optional int64 uid = 3;    // 玩家ID
  optional int32 type = 4;    // 0:全服  1:好友排行榜
}

message GetUgcRankAppointPlayer_S2C_Msg {
  optional PlayerRankInfo rank = 1;
}

// 删除自己的排行榜数据
message DeleteUgcRankEntryByPlayer_C2S_Msg {
  optional int64 mapId = 1;    // 地图ID
  optional int64 rankId = 2;   // 排行榜全局ID
  optional int64 uid = 3; // 玩家ID
}

message DeleteUgcRankEntryByPlayer_S2C_Msg {
  optional int32 result = 1;    //返回结果
}

// 获取指定名次的数据
message GetUgcRankAppointRankNo_C2S_Msg {
  optional int64 mapId = 1;    // 地图ID
  optional int64 rankId = 2;   // 排行榜全局ID
  optional int32 rankNo = 3;     // 名次
  optional int32 type = 4;    // 0:全服  1:好友排行榜
}

message GetUgcRankAppointRankNo_S2C_Msg {
  optional PlayerRankInfo rank = 1;
}

// 批量获取排行榜上 playerpublic 信息 指定mapid
message BatchGetUgcRankPlayerPublicInfo_C2S_Msg {
    optional int64 mapId = 1;  // 地图ID
    optional int64 rankId = 2; // 排行榜ID  主要帮助我更好的找到缓存
    repeated int64 uids = 3;  // uids 限定最多10个
}

message BatchGetUgcRankPlayerPublicInfo_S2C_Msg {
   repeated PlayerPublicInfo info = 1;
}

//草稿箱设置缩略图信息
message UgcSetUpCover_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
  repeated MapCoverInfo covers = 2;
}

message UgcSetUpCover_S2C_Msg {
  optional int32 result = 1;
}

//草稿箱设置缩略图列表
message UgcCoverList_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;
}

message UgcCoverList_S2C_Msg {
  optional int32 result = 1;
  repeated MapCoverInfo covers = 2;
  optional string bucket = 3;  //bucket
  optional string region = 4;
}

message UgcGetBadge_C2S_Msg{
  optional int64 uid = 1;
}
message UgcGetBadge_S2C_Msg{
  repeated proto_UgcBadgeInfo  badgeInfo = 1;
}

message UgcGetBadgeDetail_C2S_Msg{
  optional int32 badgeId = 1;
}
message UgcGetBadgeDetail_S2C_Msg{
  optional int32 badgeId = 1;
  optional int64 playerCount = 2;
  optional float playerRatio = 3;
}

message UgcGetBadgeNtf {
  optional proto_UgcBadgeInfo  badgeInfo = 1;
}

// ugc唯一id类型枚举
enum UgcUniqueIdType {
  UGC_UNIQUE_ID_TYPE_MAGIC_PIC = 1;		// 魔法图片
}

// ugc申请唯一id请求
message UgcApplyUniqueId_C2S_Msg {
  option (ugc_app_forward) = true;
  optional uint32 type = 1;				// 唯一id类型, 见枚举UgcUniqueIdType所示
  optional int32 num = 2;				// 申请数量
}

// ugc申请唯一id响应
message UgcApplyUniqueId_S2C_Msg {
  repeated int64 uniqueIds = 1;			// 唯一id列表
}

// 地图copy进度
message UgcCopyMapProgressNtf {
  optional int64 mapId = 1; // 地图ID
  optional int32 progress = 2; // 地图进度
}

// 获取ugc匹配记录信息
message UgcGetMatchRecord_C2S_Msg {
}

message UgcGetMatchRecord_S2C_Msg {
  optional int32 errCode = 1;
  repeated UgcMatchRecordSummary recordList = 2;
}

// 获取ugc匹配记录详细信息
message UgcGetMatchRecordDetail_C2S_Msg {
  optional int64 playId = 1;
}

message UgcGetMatchRecordDetail_S2C_Msg {
  optional int32 errCode = 1;
  optional MatchBattlePlayerRecordDetail recordDetail = 2;
}

message UgcGetPlayMapFriends_C2S_Msg {
  optional int64 mapId = 1;
}

message UgcGetPlayMapFriends_S2C_Msg {
  optional int32 result = 1;
  repeated FriendUgcInfo friendUgcInfos = 2;
}

message UgcGetCommunityUnreadCount_C2S_Msg {
}

message UgcGetCommunityUnreadCount_S2C_Msg {
  optional int32 result = 1;
  optional int32 totalCount = 2;
  optional bool normalRedDot = 3;
}

message UgcMatchLobbyDetailGetOne_C2S_Msg {
  option (forward_to_ugc) = true;
  optional int32 type = 1;  // 1:触发盒, 2:拉取多图匹配, 3:玩法选择页面
  optional int64 cfgId = 2;  // 地图的id
  optional int32 modeId = 3;  // 如果type是3 这里需要填写 玩法模式
}

message UgcMatchLobbyDetailGetOne_S2C_Msg {
  optional int32 errCode = 1;
  optional int32 type = 2;
  optional UgcMatchLobbyMapBrief pubBriefs = 3;
}

message UgcApplyCoverUrl_C2S_Msg {
  option (forward_to_ugc) = true;
  repeated int64 ugcId = 1;  // 地图的id
}

message UgcApplyCoverUrl_S2C_Msg {
  optional int32 errCode = 1;
  repeated UgcMapUrlInfo info = 2;
}

/**
 * mark-glueli
 * 配置采用ugcId,achId,verId三个字段控制
 * 草稿表，已发布草稿 中均会带有ugcId, 每个brief都有自己的ugcId
 * 已发布的地图里面会存储上草稿里面对应的配置id
 * 更新草稿表时只能更新草稿表对应ugcId的配置
 * 更新已发布草稿时只能更新已发布草稿对应的ugcId配置
 * 每更新一次配置会生成一个新的verId来存储配置（待更新）
 */
// 更新配置信息
message UgcAchievementConfigEdit_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  // 当前地图id （如果修改的成就id不是当前地图的，会拷贝一封并更新当前地图存储）
  optional int32 achId = 3;  // 当前成就的id
  optional UgcAchievementConf conf = 4;  // 配置数据
}

message UgcAchievementConfigEdit_S2C_Msg {
  optional int32 errCode = 1;
  optional UgcAchievementIndex ret = 2;
}

// 拉取配置信息
message UgcAchievementConfigGet_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  // 地图id
  optional int32 pageId = 2;  // 页id
}

message UgcAchievementConfigGet_S2C_Msg {
  optional int32 errCode = 1;
  optional int32 pageAll = 2;  // 总的页数
  repeated UgcAchievementData data = 3;  // 成就的数据
}

// 地图额外配置
message UgcMapExtraConfigEdit_C2S_Msg {
  option (forward_to_ugc) = true;
  option (ugc_app_forward) = true;
  optional int64 ugcId = 1;  // 地图id
  optional int32 cfgType = 2;  // 数据类型 UgcMapExtraConfigType
  optional int32 cfgId = 3;  // 需要拉的配置id 默认0 如果没有多个配置
  optional UgcMapExtraConfigWrapper config = 4;  // 配置内容
}

message UgcMapExtraConfigEdit_S2C_Msg {
  optional int32 errCode = 1;
  optional UgcMapExtraConfigIndex index = 2;  // 配置索引，用于草稿保存和发布
}

// 拉取额外配置
message UgcMapExtraConfigGet_C2S_Msg {
  option (forward_to_ugc) = true;
  option (ugc_app_forward) = true;
  optional UgcMapExtraConfigIndex index = 1;  // 配置索引
}

message UgcMapExtraConfigGet_S2C_Msg {
  optional int32 errCode = 1;
  optional UgcMapExtraConfigIndex index = 2;  // 配置索引
  optional UgcMapExtraConfigWrapper config = 3;  // 配置内容
}

//修改图层信息
message UpdateUgcLayerName_C2S_Msg {
  optional int64 ugcId = 1;  //ugcId
  optional int32 layerId = 2;  // 切换场景id
  optional string layerName = 3; //图层名称
  optional string desc = 4;
  optional int32 pointNumber = 5;
  optional int64 templateId = 6;
  optional int32 layerType = 7;
  repeated UgcLayerInfo infos = 8; //兼容 如果layerId=0; 直接用infos结构
}

message UpdateUgcLayerName_S2C_Msg {
  optional int32 result = 1;        //结果
}

// 打开星世界时发这个协议
message UgcOpenStartWorld_C2S_Msg {

}
message UgcOpenStartWorld_S2C_Msg {
  optional int64 lastOpenTimeMs = 1;
}

// 发起地图评估
message UgcInitiateEvaluation_C2S_Msg {
  optional int64 ugcId = 1;  // 地图id
}

message UgcInitiateEvaluation_S2C_Msg {
  optional int32 errCode = 1;
  optional int64 ugcId = 2;  // 地图id，ec为0才有
  optional int32 evaluationStatus = 3;  // 审核状态，ec为0才有
}

// 发布态草稿共创地图权限操作请求
message UgcOperatePublishCoCreateMap_C2S_Msg {
  optional int64 ugcId = 1;              // 地图id
  optional int64 targetUid = 2;          // 目标用户uid(联创者uid)
  optional int64 targetCreatorId = 3;    // 目标用户creatorId(联创者creatorId)
  optional int32 operateType = 4;        // 操作类型, 详情见枚举PublishCoCreateMapOperateType所示
}

// 发布态草稿共创地图权限操作请求
message UgcOperatePublishCoCreateMap_S2C_Msg {

}

// 获取创作者徽章
message UgcGetCreatorBadge_C2S_Msg {
  option (forward_to_ugc) = true;
  optional int64 creatorId = 1;
}
message UgcGetCreatorBadge_S2C_Msg {
  optional UgcCreatorBadgeInfo badgeInfo = 1; // 废弃
  repeated UgcCreatorBadgeDetailInfo badgeInfos = 2;
}

// 设置创作者主页
message UgcSetCreatorHomePage_C2S_Msg {
  repeated UgcCreatorHomePageOpType opType = 1;
  optional string creatorMessage = 2;
  repeated int32 tags = 3;
  repeated int64 publishMaps = 4;
  repeated int32 badges = 5;
}
message UgcSetCreatorHomePage_S2C_Msg {
}
// 获取创作者主页信息
message UgcGetCreatorHomePage_C2S_Msg {
  option (forward_to_ugc) = true;
  optional int64 creatorId = 1;
}
message UgcGetCreatorHomePage_S2C_Msg {
  optional UgcPlayerProfile profile = 1;
  optional string creatorMessage = 2;
  repeated int32 tags = 3;
  repeated PublishItem publishMaps = 4;
  repeated UgcCreatorBadgeDetailInfo badges = 5;
  optional bool isFollowee = 6; // 是否已关注
  optional int64 fansNum = 7;  // 实时粉丝数
}

// 允许组队，中途加入的快速加入
message UgcQuickJoinWithMidJoin_C2S_Msg {
  optional int64 ugcId = 1; // ugc地图id，不指定则在所有的地图中随机
  optional bool wentMidJoin = 2;  // 是否想要中途加入到某个队伍
}

message UgcQuickJoinWithMidJoin_S2C_Msg {
  optional int32 result = 1;
}

// 快速加入结果通知
message UgcQuickJoinWithMidJoinResultNtf {
  optional int32 errCode = 1;
  optional int64 roomId = 2;  // 加入的房间id
}


message UgcHomePageGetLikePlayHotTag_C2S_Msg {
}

message UgcHomePageGetLikePlayHotTag_S2C_Msg {
  optional int32 result = 1;
  repeated int32 hotTagIds = 2; // 热门标签
  repeated int32 setTagIds = 3; // 玩家设置的标签
  optional int32 todayModifyCount = 4;  // 今天已修改标签次数
  optional int32 dailyModifyCountLimit = 5; // 每天最多修改标签次数
}

message UgcHomePageSetLikePlayHotTag_C2S_Msg {
  repeated int32 tagIds = 1;
}
message UgcHomePageSetLikePlayHotTag_S2C_Msg {
  optional int32 result = 1;
  optional int32 todayModifyCount = 2;
  optional int32 dailyModifyCountLimit = 3;
}

// ugc共创多人编辑申请请求
// ugc共创多人编辑申请请求
message UgcCoCreateMultiEditApply_C2S_Msg {
  optional int64 ugcId = 1;                     // ugc地图id
}

// ugc共创多人编辑申请响应
message UgcCoCreateMultiEditApply_S2C_Msg {
  optional int32 result = 1;                    // 申请操作处理结果
  repeated int64 editingCreatorIds = 2;         // 正在编辑的creatorId列表
}

// ugc共创多人编辑申请通知(发送给审批者)
message UgcCoCreateMultiEditApplyNtf {
  optional int64 ugcId = 1;                     // ugc地图id
  optional int64 applyUid = 2;                  // 申请者uid
  optional int64 applyCreatorId = 3;            // 申请者creatorId
  optional PlayerPublicInfo applyUserInfo = 4;  // 申请者用户信息
}

// ugc共创多人编辑申请答复请求
message UgcCoCreateMultiEditReply_C2S_Msg {
  optional int64 ugcId = 1;                     // ugc地图id
  optional int64 applyUid = 2;                  // 申请者uid
  optional int64 applyCreatorId = 3;            // 申请者creatorId
  optional int32 replyResult = 4;	              // 申请结果, 见枚举CoCreateMultiEditReplyResult所示
  optional int32 rejectReason = 5;	            // 拒绝理由, 见枚举CoCreateMultiEditRejectReason所示
  optional int32 editMode = 6;		              // 编辑模式, 见枚举UgcMapEditMode所示
}

// ugc共创多人编辑申请答复响应
message UgcCoCreateMultiEditReply_S2C_Msg {

}

// ugc共创多人编辑申请答复通知(发送给申请者)
message UgcCoCreateMultiEditReplyNtf {
  optional int64 ugcId = 1;                     // ugc地图id
  optional int64 replyUid = 2;                  // 申请回复者uid
  optional int64 replyCreatorId = 3;            // 申请回复者creatorId
  optional int32 replyResult = 4;               // 申请结果, 见枚举CoCreateMultiEditReplyResult所示
  optional int32 rejectReason = 5;              // 拒绝理由, 见枚举CoCreateMultiEditRejectReason所示
  optional int32 editMode = 6;                  // 编辑模式, 见枚举UgcMapEditMode所示
}

// ugc共创多人编辑答复后拒绝进入请求
message UgcCoCreateMultiEditRejectEnter_C2S_Msg {
  optional int64 ugcId = 1;                     // ugc地图id
  optional int64 replyUid = 2;                  // 申请回复者uid
  optional int64 replyCreatorId = 3;            // 申请回复者creatorId
}

// ugc共创多人编辑答复后拒绝进入响应
message UgcCoCreateMultiEditRejectEnter_S2C_Msg {

}

// ugc共创多人编辑答复后拒绝进入通知(发送给审批者)
message UgcCoCreateMultiEditRejectEnterNtf {
  optional int64 ugcId = 1;                     // ugc地图id
  optional int64 rejectUid = 2;                 // 拒绝者uid
  optional int64 rejectCreatorId = 3;           // 拒绝者creatorId
  optional PlayerPublicInfo rejectUserInfo = 4; // 拒绝者用户信息
}

// ugc共创多人编辑扣叮数据保存请求
message UgcCoCreateMultiEditCodingData_C2S_Msg {
  optional int64 ugcId = 1;                     // ugc地图id
  optional CodingDataInfo data = 2;             // 扣叮数据
}

// ugc共创多人编辑扣叮数据保存响应
message UgcCoCreateMultiEditCodingData_S2C_Msg {

}

// ugc共创多人编辑数据更新通知
message UgcCoCreateMultiEditDataUpdateNtf {
  optional int64 ugcId = 1;                     // ugc地图id
  optional int32 editMode = 2;                  // 编辑模式, 标识哪个编辑模式的数据已更新
  optional CodingDataInfo data = 3;              // 扣叮数据
}
message UgcSubscribeRecommendPage_C2S_Msg {
  optional int32 op = 1;  // 0=取消订阅；1=订阅
  optional int32 type = 2; // ERecommendPageType
}
message UgcSubscribeRecommendPage_S2C_Msg {
  optional int32 result = 1;
}

// 获取星世界入口气泡
message UgcGetStarWorldEntranceBubble_C2S_Msg {

}
message UgcGetStarWorldEntranceBubble_S2C_Msg {
  optional int32 bubbleType = 1; // UgcStarWorldEntranceBubbleType
  optional int64 expireTimeMs = 2;  // 有效期过期时间, 0表示永驻
  optional int32 bubbleId = 3;  // 气泡id,运营气泡会有
  optional int32 tipsType = 4;  // 普通红点样式、文字气泡样式、仅图标样式
  optional string tipsText = 5; // 气泡文本
  optional string tipsIcon = 6; // 气泡图标
  optional int32 tipsIconType = 7;  // 图标类型，道具ID 或者 预置通用图标
  optional string tipsBackground = 8; // 背景底色
  optional string jumpId = 9; // 跳转id
  optional string jumpParam = 10; // 跳转参数
}
// 消除星世界入口气泡,消除规则依赖玩家查看的才需要消除，如运营气泡
message UgcRemoveStarWorldEntranceBubble_C2S_Msg {
  optional int32 bubbleType = 1; // UgcStarWorldEntranceBubbleType
  optional int32 bubbleId = 2;
}
message UgcRemoveStarWorldEntranceBubble_S2C_Msg {

}
