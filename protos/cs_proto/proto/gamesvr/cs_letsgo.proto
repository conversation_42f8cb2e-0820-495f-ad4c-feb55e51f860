syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "g6_common.proto";
import "letsgo_common.proto";
import "ResKeywords.proto";
import "base_common.proto";
import "common.proto";
import "competition_common.proto";
import "ResGameSetting.proto";
import "attr_RoguelikeMark.proto";
import "attr_GameProtectionSpecificSwitch.proto";
import "attr_ABTestSetting.proto";
import "attr_KvII.proto";
import "attr_GameGearSettings.proto";

message LetsGoTest_C2S_Msg {
  optional int32 num = 2;
}

message LetsGoTest_S2C_Msg {
  optional int32 res_code = 1;  // 错误码 0 成功  其他失败
}

message LetsGoGetPlayerProfile_C2S_Msg {
  optional int64 uid = 1;       // 不传参表示自己
}

message LetsGoGetPlayerProfile_S2C_Msg {
  optional string nickname = 1;           // 昵称(公共)
  optional string profile = 2;            // 头像url (公共)
  optional string signature = 3;          // 个性签名(公共)
  // repeated SimpleVisitor visitors = 4;
  // optional ModAvatar avatar = 5;
  // optional bool isYourself = 6;
  repeated int32 currDresses = 7;         // 当前服装
  // optional int32 modTimes = 8;           // 游戏次数
  // optional int32 achievedTimes = 9;       // 达标次数
  // optional int32 dressCounts = 10;        // 动物服数

  // optional int32 privilegeSwitch = 11;    // 特权开关  0-隐藏按钮，1-不响应点击，2-响应点击
  // optional int32 privilegeLevel = 12;     // 特权等级  红钻等级: 0-代表未开通，1-开通低档，2-开通中档，3-开通高档

  optional int32 gender = 13;             // 性别  1-男 2-女 0-未知
  optional string openId = 14;
  // optional int32 championTimes = 15;      // 夺冠次数
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo curSeasonMaxQualifying = 17;  // 当前赛季最高段位信息
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo historyMaxQualifying = 18;  // 历史赛季最大段位信息
  repeated EquipItemInfo equipItems = 19; // 装备信息
  // optional com.tencent.wea.xlsRes.RoomStatus roomStatus = 20;   // 状态信息
  // optional int64 roomId = 21;             // 房间id
  // optional bool roomIsFull = 22;             // 房间是否已满
  // optional bool isOnline = 23;            // 是否在线
  repeated KeyValueInt32 timesInfo = 24;  // 各种次数 PlayerGameTimeType => value
  optional int64 uid = 25;
  optional int32 level = 26;
  optional int64 exp = 27;
  optional int32 vipLevel = 28;
  optional com.tencent.wea.xlsRes.UgcAuthType ugcAuthType = 29;
}


// 获取战斗历史记录快照
message LetsGoGetBattleSnapshotRecord_C2S_Msg {
  optional int32 min_index = 1;
  optional int32 max_index = 2;
  optional int64 uid = 3;
  optional int32 type = 4;
  optional int32 type2 = 5;   // 0-全部，1-匹配，2-房间
}

message LetsGoGetBattleSnapshotRecord_S2C_Msg {
  optional int32 index = 1; // 当前返回的记录的起始index, index从0开始计
  optional int32 count = 2; // 当前返回的记录条数
  optional int32 total = 3; // 总记录条数
  repeated LetsGoSnapshotRecord snapshot_list = 4;
}

// 获取战斗详情
message LetsGoGetBattleDetailRecord_C2S_Msg {
  required int64 id = 1;          // 战斗记录ID
}

message LetsGoGetBattleDetailRecord_S2C_Msg {
  required int32 error = 1;
  optional LetsGoHistoryRecord detail_record = 2;
}


message LetsGoSettings {
  optional bool simplifySpecialEffects = 1;     // 简化局内特效
  optional bool hideNick = 2;                   // 简化局内昵称
  optional bool hideFriendReq = 3;              // 屏蔽好友申请
  optional bool rejectStrangerChatMsg = 4;      // 拒绝接收陌生人的私聊消息
  optional bool hideEmoji = 5;                  // 局内隐藏表情
  optional bool hideRoomInvitation = 6;         // 屏蔽组队邀请
  optional bool hideQualifyingInfo = 7;         // 隐藏段位开关
  optional bool hidePersonalProfile = 8;        // 个人信息隐藏-个人信息
  optional bool hideBattleHistory = 9;          // 个人信息隐藏-历史战绩
  optional bool hideSuitBook = 10;              // 个人信息隐藏-套装图鉴
  optional bool hideUgcInfo = 11;               // 个人信息隐藏-UGC
  optional bool hideLobbyInvitation = 12;       // 屏蔽大厅邀请
  optional int32 clientLanguage = 13;           // 客户端语言
  optional bool hideIntimateRelation = 14;      // 隐藏亲密关系
  optional bool hideIntimateRelationTab = 15;   // 个人信息隐藏-亲密关系
  optional bool hideLocation = 16;              // 个人信息隐藏-定位
  optional bool hideQQFriendReq = 17;           // 屏蔽 QQ 好友申请
  optional bool allowRecommendInfoCollect = 18; // 是否允许微信好友获取你的游戏动态信息的设定
  optional bool hideClubInfo = 19;              // 隐藏社团信息
  optional bool hideTitle = 20;                 // 个人信息隐藏-称号
  optional bool hidePersonalityState = 21;      // 个人信息隐藏-心情
  optional int32 headPublicInfoType = 22;       // 头顶个人展示的是称号还是心情  EHeadPublicInfoType 枚举值
  optional bool hidePlayerStatus = 23;          // 个人信息隐藏-隐身
  optional bool hideWorldLobbyChat = 24;        // 隐藏世界、广场聊天
  optional bool needActionConfirm = 25;         // 双人动作确认优化
  optional bool hideReservation = 26;           // 屏蔽预约邀请
  optional bool showSeasonFashion = 27;         // 展示赛季时尚手册开关 # 废弃
  optional com.tencent.wea.xlsRes.FeatureShowStatus seasonFashionShowStatus = 28; // 赛季时尚手册展示
  optional com.tencent.wea.xlsRes.FeatureOpenStatus intimateOnlineNotice = 29;    // 亲密好友上线提醒
  optional bool hideRoomExtraInfo = 30;         // 隐藏组队详细信息
  optional bool gameProtectionMainSwitch = 31;  // 星宝防护总开关
  optional proto_GameProtectionSpecificSwitch gameProtectionSpecificSwitch = 32;  // 星宝防护具体开关内容
  optional bool notAllowRecommendFriends = 33;  // 不允许推荐好友
  optional bool notSearchedByUid = 34;          // 不被UID搜索到
  optional bool hideProfileToFriend = 35;       // 对好友隐藏个人信息
  optional bool hideProfileToStranger = 36;     // 对陌生人隐藏个人信息
  optional int32 demandSwitch = 37;              // 允许索要选项(0-默认允许;1-不允许;2-仅允许亲密)
  optional int32 arenaTargetSelectionStrategy = 38;    // moba游戏中优先攻击的目标，0 血量百分比最低 1 血量绝对值最低 2 距离最近单位
  optional bool arenaIsAutoNormalAttack = 39;    // moba游戏中是否进行自动攻击
  optional int32 arenaVoiceType = 40; // moba游戏中的语音播报类型
  optional int32 lobbyModeType = 41; // 启动大厅模式类型，广场、组队秀
  optional bool showCups = 42;            // 展示奖杯
  optional bool strangerFollow = 43;            // 陌生人跟随
  optional proto_ABTestSetting lobbyModeTypeSettingForABTest = 44; // 启动大厅模式类型，广场、组队秀，仅ab实验下使用
  optional bool showCustomActionSelectFriendInfo = 45;            // 是否开启双人动作展示好友信息的开关
  optional int32 arenaCameraControlType = 46;            // moba游戏中的相机模式
  optional bool hideArenaBattleHistory = 47;          // arena历史战绩隐藏
  optional bool hideEntertainmentQualifyInfo = 48;          // 隐藏娱乐玩法段位
  optional int32 photoLibraryDescription = 49;      //相册访问权限
  optional com.tencent.wea.xlsRes.BirthdayVisibleRange birthdayVisibleRange = 50;     // 生日可见范围
  optional int32 currentActionState = 51;      //相册访问权限
  optional bool offAinpcChatPush = 52;        // 好好鸭智能推送
  optional bool hideFashionScore = 53;        //隐藏时尚分展示
  optional bool hideSubHistoryMaxQualifyInfo = 54;          // 隐藏副玩法历史最高段位
  repeated proto_KvII profileTopInfo = 55;          // 个人信息界面置顶信息
}

// 设置
message LetsGoSetSettings_C2S_Msg {
  optional LetsGoSettings settings = 1;
}

message LetsGoSetSettings_S2C_Msg {

}

message LetsGoSettingsChangeNtf {
}


//获取设置
message LetsGoGetSettings_C2S_Msg {

}

message LetsGoGetSettings_S2C_Msg {
  optional LetsGoSettings settings = 1;
}

message PlayModeUnlockNtf {
  repeated int32 matchTypeIds = 1;
}

message BattleDetailedScore {
  message KeyScore {
    optional int32 key = 1;
    optional int32 score = 2;
  }
  message LevelPerfResult {
    optional com.tencent.wea.xlsRes.LevelType levelType = 1;
    optional KeyScore killScore = 2; // key: 击杀数量
    optional int32 aliveScore = 3; // key: 无
    optional KeyScore rankScore = 4; // key: 排名
    optional int32 winScore = 5;
    optional int32 eventScore = 6; // 动物派对事件得分
    optional int32 totalScore = 7; // 总分
    optional KeyScore saveScore = 8; // key: 救援得分
  }
  message PerfResult {
    optional int32 totalScore = 1;
    repeated LevelPerfResult results = 2;
  }

  message PassResult {
    optional bool win = 1;
    optional int32 passScore = 2;
    optional int32 levelIndex = 3;
  }

  optional int32 totalScore = 1;
  optional bool detailed = 2; // 如果为空，则不填充perfResult和passResult
  optional PerfResult perfResult = 3;
  optional PassResult passResult = 4;
}

message BattleRecommendFriendInfo {
  optional int64 uid = 1;
  optional int32 reason = 2; // FriendAddMotivationType
  optional string reason_extra = 3; // 可能有多个参数,以;分隔
  optional string rcmd_info = 5; // 推荐参数信息
  optional string ab_test_info = 6; // 实验参数信息
  optional string reason_text = 7 ; // 推荐理由文本
}

message ClubChallengeSettlementInfo{
  optional int32 activityId = 1;
  optional int32 starLight = 2;
  optional ItemArray extraReward = 3; // 额外结算奖励
}

message HeatSettlement{
  optional int32 behaviorId = 1;//玩家触发的行为ID ，0：没有触发违规行为
  optional int64 heatValue = 2;//当前热力值
  optional int32 winScore = 3;//胜负分
  optional int32 personScore = 4;//个人分
  optional int32 mvpScore = 5;//map分
}

message LetsGoBattleSettlementNtf {
  optional int64 battle_id = 1;
  optional int32 result = 2;
  optional int32 game_id = 3;
  optional int32 mode_id = 4;
  optional LetsGoBattleDetailData detail_data = 5;
  repeated MemberBaseInfo battleMember = 6; // 同玩玩家uid
  repeated LevelDropItemInfo itemList = 7; // 掉落道具
  // optional int32 championTimes = 8; // 冠军奖杯获得数量
  optional QualifyingSettlement qualifyingSettlement = 9;
  optional int32 isGiveUp = 10;  // 是否中途放弃 0否1是
  repeated int64 sideMemberUids = 11;  // 同阵营玩家信息
  optional ChampionTeamInfo championTeamInfo = 12; // 冠军队伍得分信息
  repeated int64 championMemberUids = 13;  // 冠军队伍玩家信息
  optional int32 selfScore = 14; // 自己得分
  optional com.tencent.wea.xlsRes.MatchGrade selfGrade = 15; // 自己成绩
  repeated int64 roomMemberUids = 16;  // 玩家组队信息
  optional PublishItem ugcMapInfo = 17;
  optional UgcKeyInfo keyInfo = 18;
  optional MatchRuleInfo rule_info = 19; // 对局玩法详细信息
  optional int32 warmType = 20; // 1:warmRound 2:guideWarmRound
  optional BattleDetailedScore detailedScore = 21; // 对局得分详细信息
  optional bool isBestRecord = 22; // 是否最佳记录
  optional CompetitionSettlementInfo competition_settlement = 23; // 赛事结算信息
  // 下面两个字段目前只有在主副玩法并且是单人模式的情况下才会有
  // 队伍成员评分组队信息, 这个列表中可能包含自己, 不要做假设
  repeated RoomMemberBattleSettlementInfo roomMembers = 24;
  repeated BattleRecommendFriendInfo recommendPlayers = 25;
  optional int64 observedPlayerUid = 26; //被观战uid，如果这个玩家id大于0，说明结算数据是被观战玩家的
  optional RoguelikeSettlementInfo roguelikeSettlementInfo = 27;  //肉鸽玩法结算信息
  repeated RelationSettlementInfo relationSettlementInfo = 28; // 好友关系结算
  optional WereWolfRoleInfoPointsInfo wereWolfRoleInfoPointsInfo = 29; //狼人杀精炼点数信息
  optional AddCupsDetailInfo addCupsDetailInfo = 30; // 增加奖杯详细信息
  optional ClubChallengeSettlementInfo clubChallengeSettlementInfo = 31; // 社团挑战相关
  optional int32 iAAConfId = 32; // 结算IAA配置ID 0则不弹出
  repeated BattlePlayerCoMatchInfo coMatchInfoList = 33; // 初始的同阵营玩家的同游匹配信息，提前退出的玩家以及机器人默认拒绝
  repeated BattleCustomResult customResult = 34;
  optional ArenaHeroCeSettlementInfo arenaHeroCeSettlementInfo = 35;  // 峡谷英雄战力结算信息
  optional WereWolfRepeatWinInfo wereWolfRepeatWinInfo = 36; //狼人杀连胜信息
  optional HeatSettlement heatSettlement = 37;//热力值结算信息
  optional MatchDynamicConfigData matchDynamicConfigData = 38;
  optional UgcMultiRoundScoreSettlementInfo ugcMultiRoundScoreSettlementInfo = 39; // 多轮地图结算数据
  optional ArenaHeroStarInfo arenaHeroStarInfo = 40;  // 主目标系统英雄峡谷星信息
  optional ChaseIdentitySettlementInfo chaseIdentitySettlementInfo = 41;  // 大王专精结算信息
  optional ArenaSettlementInfo arenaSettlementInfo = 42;  // arena结算信息
}

message ArenaSettlementInfo {
  optional int32 arenaDailyVictoryNum = 1;//arena每日胜利场次(结算时显示每日胜利宝箱进度)
  optional string arenaHighlightJson = 2;// arena pk高光信息
}

message ChaseIdentitySettlementInfo {
  optional int32 identityId = 1;
  optional int32 oldScore = 2;
  optional int32 scoreChange = 3;
  optional int32 scoreLimit = 4;
  optional ChaseIdentitySettlementGmInfo gmInfo = 5;  // GM用数据，正式环境不会下发
  optional ChaseIdentityProficiencySettlementInfo chaseIdentityProficiencySettlementInfo = 6;  // 身份熟练度结算
}

message ChaseIdentitySettlementGmInfo {
  optional bool isGiveUp = 1;                     // 是否中途退出
  optional int32 teamRank = 2;                    // 队伍名次
  optional int32 mvpValue = 3;                // 英雄MVP分
  optional int32 mvpValueRank = 4;            // 英雄MVP分对局排名
  optional int32 mvpValueGlobalPercentile = 5;// 英雄MVP分全局百分位排名（前百分之）
  optional int32 activePointOld = 6;              // 对局前活跃系数（百分定点数）
  optional int32 activePointNew = 7;              // 对局后活跃系数（百分定点数）
  optional int32 teamBattleScore = 8;             // 场次分
  optional int32 battleScoreLowAcc = 9;           // 场次分低专精加速
  optional int32 lowAccPercentage = 10;           // 低专精加速百分比
  optional int32 teamPerfScore = 11;              // 队伍表现分
  optional int32 battlePerfScore = 12;        // 对局表现分（MVP分对局排名）
  optional int32 globalPerfScore = 13;        // 全局表现分（MVP分全局百分位排名）
  optional int32 perfScoreLowAcc = 15;            // 表现分低专精加速
  optional int32 perfScoreProtection = 16;        // 表现分低分保护分数
  optional int32 perfScoreLimit = 17;           // 表现分上限扣除分数
  optional int32 battleScoreLimit = 18;           // 场次分上限扣除分数
  optional int32 battleScoreOld = 19;             // 对局前场次分
  optional int32 battleScoreNew = 20;             // 对局后场次分
  optional int32 perfScoreOld = 21;               // 对局前表现分
  optional int32 perfScoreNew = 22;               // 对局后表现分
}

message ChaseIdentityProficiencySettlementInfo {
  optional int32 identityId = 1;    //身份ID
  optional int32 oldProficiency = 2;      // 原本熟练度
  optional int32 newProficiency = 3;      // 新熟练度
}

message ArenaHeroStarInfo {
  optional int32 heroId = 1;
  optional int32 oldStar = 2;         // 结算前英雄峡谷星
  optional int32 starChange = 3;      // 峡谷星变化
  optional int32 baseStar = 4;        // 基础分
  optional int32 perfStar = 5;        // 表现分
  optional int32 teamAcc = 6;         // 组队加成
  optional int32 qualifyAcc = 7;      // 段位加成
  optional ArenaHeroStarGMInfo gmInfo = 8;  // GM用数据，正式环境不会下发
}

message ArenaHeroStarGMInfo {
  optional bool isGiveUp = 1;                     // 是否中途退出
  optional int32 teamRank = 2;                    // 队伍名次
  optional int32 heroMvpValue = 3;                // 英雄MVP分
  optional int32 battleTime = 4;                  // 对局时长（毫秒）
  optional bool isTeamMatch = 5;                  // 是否组队
  optional int32 battleTimeFactor = 6;            // 对局时长系数（百分定点数）
  optional int32 teamFactor = 7;                  // 组队系数（百分定点数）
  optional int32 baseScore = 8;                   // 基础分（对局时长分，百分定点数）
  optional int32 rankConst = 9;                   // 队伍名次常数
  optional int32 rankFactor = 10;                 // 队伍名次系数（百分定点数）
  optional int32 mvpValueFactor = 11;             // MVP分系数（百分定点数）
  optional int32 perfScore = 12;                  // 表现分（百分定点数）
  optional int32 degreeFactor = 13;               // 段位系数（百分定点数）
  optional int32 regulationFactor = 14;           // 调控系数（百分定点数）
  optional int32 constantStar = 15;               // 固定增加奖章数
  optional int32 starOld = 16;                    // 对局前英雄峡谷星奖章数
  optional int32 starNew = 17;                    // 对局后英雄峡谷星奖章数
  optional int32 levelOld = 18;                   // 对局前英雄熟练等级
  optional int32 levelNew = 19;                   // 对局后英雄熟练等级
  optional bool isAFK = 20;                       // 是否挂机
  optional int32 failureFactor = 21;              // 失败系数（百分定点数）
}

message ArenaHeroCeSettlementInfo {
  optional int32 heroId = 1;
  optional int32 oldScore = 2;        // 结算前英雄战力（百分定点数）
  optional int32 scoreChange = 3;     // 战力值变化（百分定点数）不含战力加速
  optional int32 accScoreChange = 4;  // 战力值加速部分（百分定点数）
  optional ArenaHeroCeSettlementGMInfo gmInfo = 5;  // GM用数据，正式环境不会下发
  optional int32 eventAddScore = 6;   //事件添加的战力分（百分比点数）
}

message ArenaHeroCeSettlementGMInfo {
  optional bool isGiveUp = 1;                     // 是否中途退出
  optional int32 teamRank = 2;                    // 队伍名次
  optional int32 heroMvpValue = 3;                // 英雄MVP分
  optional int32 heroMvpValueRank = 4;            // 英雄MVP分对局排名
  optional int32 heroMvpValueGlobalPercentile = 5;// 英雄MVP分全局百分位排名（前百分之）
  optional int32 activePointOld = 6;              // 对局前活跃系数（百分定点数）
  optional int32 activePointNew = 7;              // 对局后活跃系数（百分定点数）
  optional int32 teamBattleScore = 8;             // 战力队伍场次分
  optional int32 battleScoreAcc = 9;              // 战力场次分加速
  optional int32 battleScoreLowAcc = 10;          // 战力场次分低战力加速
  optional int32 teamPerfScore = 11;              // 战力队伍表现分
  optional int32 heroBattlePerfScore = 12;        // 战力英雄对局表现分（MVP分对局排名）
  optional int32 heroGlobalPerfScore = 13;        // 战力英雄全局表现分（MVP分全局百分位排名）
  optional int32 perfScoreAcc = 14;               // 战力表现分加速
  optional int32 perfScoreLowAcc = 15;            // 战力表现分低战力加速
  optional bool  perfScoreProtection = 16;        // 是否触发低战力分保护
  optional int32 accPercentage = 17;              // 战力加速百分比
  optional int32 lowAccPercentage = 18;           // 低战力加速百分比
  optional int32 battleScoreOld = 19;             // 对局前战力场次分
  optional int32 battleScoreNew = 20;             // 对局后战力场次分
  optional int32 perfScoreOld = 21;               // 对局前战力表现分
  optional int32 perfScoreNew = 22;               // 对局后战力表现分
  optional int32 perfScoreDiscountPercent = 23;   // 表现分折扣百分比
}

message RelationSettlementInfo {
  optional int64 friendUid = 1;
  optional int32 intimateId = 2; // 亲密好友关系
  optional int32 intimacy = 3; // 增加亲密度
}

message RoguelikeSettlementInfo {
  repeated proto_RoguelikeMark marks = 1; // 印记列表
  optional int32 talentCount = 2; // 天赋点数
  optional int32 markMaterial = 3; // 印记货币
}

//获取奖励
message LetsGoGetQualifyingReward_C2S_Msg {
  repeated com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingDegreeList = 1;
}

message LetsGoGetQualifyingReward_S2C_Msg {
  repeated ItemInfo items = 1;
}

message QualifyingSettlement {
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo oldQualifyingInfo = 1;  // 结算前段位信息
  optional int32 changeIntegral = 2;    // 积分变化
  repeated QualifyingScoreInfo scoreInfo = 3;   // 个人排名，key名次，value分数，index关卡序列
  //  repeated IdValueInfo teamRankDetail = 4;   // 团队排名
  //  repeated IdValueInfo extraDetail = 5;    // 额外得分, key为id，value分数， values第一个值为当前已完成次数
  optional int32 integralProtect = 6;     // 积分保护
  optional int32 degreeProtect = 7;     // 段位保护
  optional int32 firstDegreeProtect = 8;     // 晋级成功首局段位保护
  optional int32 isNewMaxDegree = 9;  // 首次解锁段位 0否1是
  optional int32 maxDegreeProtect = 10; // 达到最大段位保护，扣到最大段位的最高分
  optional PlayerRankInfo oldRankInfo = 11; // 王者段位积分排行
  optional PlayerRankInfo rankInfo = 12; // 王者段位积分排行
  optional int32 errorCode = 13; // errorCode
  repeated QualifyingProtectedScoreInfo protectedScoreInfo = 14;   // 保分
  optional int32 iAAConfId = 15; // 结算IAA配置ID 0则不弹出
}

message QualifyingProtectedScoreInfo {
  optional QualifyingProtectedScoreType type = 1;
  optional IdValueInfo scoreInfo = 2;
}

enum QualifyingProtectedScoreType {
  QPST_None = 0;
  QPST_Champion = 1; // 夺冠奖励
  QPST_Level = 2; // 关卡名次
  QPST_Eliminate = 3; // 关卡淘汰
  QPST_WinStreak = 4; // 连胜
  QPST_FinalLevelRank = 5; // 终局排名
  QPST_Protected = 6; // 排位分抵扣(消耗)
  QPST_Additional = 7; // 排位分加成(消耗)
  QPST_FinalDimension = 8; // 全局维度
  QPST_LevelDimension = 9; // 关卡维度保分
}

message QualifyingScoreInfo {
  optional QualifyingScoreType type = 1;
  optional IdValueInfo scoreInfo = 2;
}

enum QualifyingScoreType{
  QST_None = 0;
  QST_Champion = 1; // 夺冠奖励 通关积分-夺冠分数
  QST_Rank = 2; // 关卡名次 关卡积分-玩家排名得分
  QST_LevelPass = 3; // 过关奖励 通关积分-轮次淘汰分
  QST_FirstDegreeUp = 4; // 今日首次晋级
  QST_LevelPerformance = 5; // 玩法奖励
  QST_FriendCooperation = 6; // 好友合作奖励
  QST_DailyFirstWin = 7; // 每日首胜奖励
  QST_IntegralCommonProtectItem = 8; // 排位赛通用保护券
  QST_IntegralTeamProtectItem = 9; // 排位赛组队保护券
  QST_IntegralCommonAdditionalItem = 10; // 排位赛通用升星券
  QST_IntegralTeamAdditionalItem = 11; // 排位赛组队升星券
  QST_TeamRankActivityProtect = 12; // 组队排位活动不掉分
  QST_TeamRankActivityAdditional = 13; // 组队排位活动加分
  QST_ProtectedScoreAdditional = 14; // 保分加分
  QST_ProtectedScoreProtected = 15; // 保分抵扣
  QST_ReturingCooperation = 16; // 和回归好友组队奖励
  QST_ReturingPrivilege = 17; // 回归玩家特权奖励
  QST_FinalLevelRank = 18; // 终局排名
  QST_LevelDimensionScore = 19; // 关卡维度积分
  QST_Draw = 20; // 平局积分奖励 通关积分-平局分数
  QST_LevelKill = 21; // 关卡击杀 关卡积分-击杀得分
  QST_LevelSurvive = 22; // 关卡存活 关卡积分-存活
  QST_LevelTeamRank = 23; // 关卡队伍排名 关卡积分-队伍排名得分
  QST_ContinueWin = 24; // 连胜得分
  QST_TeamRankActivityMultiScore = 25; // 组队排位活动比例加分
  QST_RandomEvent = 26; // 对局触发随机事件加分
}

message SeasonQualifyingSettlement {
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifyingInfo = 1;  // 结算前段位信息
  optional int32 seasonID = 2;    // 赛季
}

message LetsGoSeasonSettlementNtf {
  optional SeasonQualifyingSettlement oldQualifyingInfo = 1;     // 结算前段位信息
  optional SeasonQualifyingSettlement inheritQualifyingInfo = 3; // 继承的段位信息
}

message LetsGoSeasonBatchSettlementNtf {
  repeated LetsGoSeasonSettlementNtf seasonSettlementInfo = 1;   // 副玩法赛季结算(批量)
}

//打开奖励列表
message LetsGoOpenQualifyingList_C2S_Msg {
  optional int32 openType = 1; // 默认排位奖励 0
}

message LetsGoOpenQualifyingList_S2C_Msg {
  optional int32 res_code = 1; //0 成功
}

// 上报客户端本地存储的需要服务端代记流水的设置项
message LetsGoClientUploadSettings_C2S_Msg {
  repeated KeyValueInfo settings = 1;
}

message LetsGoClientUploadSettings_S2C_Msg {

}

message LetsGoRecommendMatchType_C2S_Msg {

}

message LetsGoRecommendMatchType_S2C_Msg {
  repeated int32 matchTypeIds = 1;
  optional string recid = 2;
  optional string expTag = 3;
}

message LetsGoWolfKillScoreRecord {
  optional int64 dt = 1;
  optional int32 deltaScore = 2;
  optional int32 totalScore = 3;
  optional int32 reason = 4;
}

message LetsGoGetWolfKillInfo_C2S_Msg {

}

message LetsGoGetWolfKillInfo_S2C_Msg {
  optional int32 reputationScore = 1;
  repeated LetsGoWolfKillScoreRecord scoreRecord = 2;
}

message LetsGoGetWolfKillReputationScore_C2S_Msg {

}

message LetsGoGetWolfKillReputationScore_S2C_Msg {
  optional int32 reputationScore = 1;
}

message LetsGoWolfKillRoleInfo {
  optional int32 roleType = 1;
  optional int32 points = 2;
  optional int32 totalTimes = 3;
  optional int32 winTimes = 4;
  optional int32 mvpTimes = 5;
}

message LetsGoGetWolfKillRoleInfo_C2S_Msg {

}

message LetsGoGetWolfKillRoleInfo_S2C_Msg {
  repeated LetsGoWolfKillRoleInfo roleInfos = 1;
}

message GameTvStatusChangeNtf {
  optional string liveStatus = 1; // 直播状态
  optional string gameTvEntry = 2; // 电视台入口
  optional string androidGameTvSwitch = 3; // android初始化开关
  optional string iosGameTvSwitch = 4; // ios初始化开关
  optional bool openTvStationHall = 5; // 是否开放电视台（大厅入口）
  optional bool openTvStationPopup = 6; // 是否开放电视台（拍脸弹窗）
  repeated GameTvBanCondition banCondition = 7; // 关闭条件
}

message GameTvBanCondition {
  optional int32 day = 1;
  optional string startTime = 2;
  optional string endTime = 3;
  optional int32 rank = 4;
}

message UploadGameSetting_C2S_Msg {
  repeated com.tencent.wea.xlsRes.GameSettingConf conf = 1;
}

message UploadGameSetting_S2C_Msg {

}

message DownloadGameSetting_C2S_Msg {

}

message DownloadGameSetting_S2C_Msg {
  repeated com.tencent.wea.xlsRes.GameSettingConf conf = 1;
}

message GameSetting {
  optional string key = 1;
  optional string value = 2;
  optional string extra = 3;
}

message PlayerGameSettingTlog_C2S_Msg {
  optional int32 scene = 1;
  repeated GameSetting setting = 2;
}

message PlayerGameSettingTlog_S2C_Msg {

}

message RecommendFriendsCDSet_C2S_Msg {
}

message RecommendFriendsCDSet_S2C_Msg {
}

// 特殊游戏场景切换上报
message GameSceneReport_C2S_Msg {
  optional int32 scene = 1; // 场景id
}

message GameSceneReport_S2C_Msg {

}

message GetGlobalRecentPlayRecord_C2S_Msg {
  optional int32 count = 1;
  optional int32 offset = 2;
  optional int32 source = 3; // 请求来源: 0hud界面  1玩法自动下载
}
message GetGlobalRecentPlayRecord_S2C_Msg {
  repeated RecentPlayRecordData recordData = 1;
}

message CompetitionEntranceStatusChangeNtf {
  optional bool is_open = 1;
  map<int32, int32> season_map = 2; // compType -> season
}

message UpdatePlayerGearSetting_C2S_Msg {
  optional proto_GameGearSettings settings = 1;
}

message UpdatePlayerGearSetting_S2C_Msg {
}
