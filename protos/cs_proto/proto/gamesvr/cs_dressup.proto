syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "ResKeywords.proto";
import "attr_AppearanceRoadShowInfo.proto";
import "attr_AttrDisplayBoardInfo.proto";
import "attr_ItaBagInfo.proto";
import "common.proto";
import "cs_ugc.proto";

// 穿戴切换
message DressUpToggle_C2S_Msg {
  optional int32 dressUpId = 1;
  optional DressUpToggleType toggleType = 2;
}

// 脱下
message DressUpToggle_S2C_Msg {
  optional int32 dressUpId = 1;
  optional DressUpToggleType toggleType = 2;
}

enum DressUpToggleType {
  DUT_ToggleOn = 0;
  DUT_ToggleOff = 1;
}

// 装扮过期check
message DressUpCheckExpire_C2S_Msg {
}

// 装扮过期check
message DressUpCheckExpire_S2C_Msg {
}

//装扮过期通知
message DressUpExpireNtf {
  optional int32 dressUpId = 1;
}

//装扮过期通知
message DressUpChangeNtf {
  optional int32 oldDressUpId = 1;
  optional int32 currentDressUpId = 2;
  optional bool isExpire = 3;
}

message DressUpShowList_C2S_Msg {

}

message DressUpShowList_S2C_Msg {
  repeated int32 dressUpId = 1;
}

//装扮道具穿戴
message DressUpItem_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 targetPos = 2;
}

message DressUpItem_S2C_Msg {
  optional int64 itemUUID = 1;
  optional int32 targetPos = 2;
}


//随机转换
message DressUpChangeRandomState_C2S_Msg {
  optional com.tencent.wea.xlsRes.ItemType itemType = 1;
  optional bool isRandom = 2;
  optional int64 itemUUID = 3;//不随机时传穿戴道具的UUID
}

message DressUpChangeRandomState_S2C_Msg {
  optional com.tencent.wea.xlsRes.ItemType itemType = 1;
  optional bool isRandom = 2;
  optional int64 itemUUID = 3;
}

//装扮激活
message DressUpActive_C2S_Msg {
  optional int32 dressUpId = 1;
}

message DressUpActive_S2C_Msg {
  optional int32 dressUpId = 1;
}

message DressInfo {
  optional int32 itemType = 1;
  optional int64 itemUuid = 2;
}

// 槽位保存
message FittingSlotSave_C2S_Msg {
  optional int32 slotId = 1;
  repeated DressInfo dresses = 2;
}

message FittingSlotSave_S2C_Msg {
  optional int32 slotId = 1;
}

// 槽位选择
message FittingSlotSelect_C2S_Msg {
  optional int32 slotId = 1;
}

message FittingSlotSelect_S2C_Msg {
  optional int32 slotIdInUse = 1;
}

message FittingSlotShow_C2S_Msg {
  optional int32 slotId = 1;
  optional bool execOrRevoke = 2;
}

message FittingSlotShow_S2C_Msg {
}

// 穿戴称号、铭牌、头像框
message FittingSingleItem_C2S_Msg {
  required int64 itemUUID = 1; // 传当前穿戴的UUID表示脱掉
}

message FittingSingleItem_S2C_Msg {
  required int64 itemUUID = 1;
}

message FittingItemStatusChange_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 status = 2;
}


message FittingItemStatusChange_S2C_Msg {
  optional int64 itemUUID = 1;
  optional int32 status = 2;
}

message UpdateItemInfoDbItemShowStatus_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 showStatus = 2;
}


message UpdateItemInfoDbItemShowStatus_S2C_Msg {
  optional int64 itemUUID = 1;
  optional int32 showStatus = 2;
}

message AppearanceRoadGetLvAward_C2S_Msg {
  optional int32 lv = 1;
  optional int32 subLv = 2;
}

message AppearanceRoadGetLvAward_S2C_Msg {

}

message AppearanceRoadGetGatherAward_C2S_Msg {
  optional int32 type = 1; // 1赛季，2主题
  optional int32 id = 2;
}

message AppearanceRoadGetGatherAward_S2C_Msg {

}

message SetAppearanceRoadShow_C2S_Msg {
  repeated proto_AppearanceRoadShowInfo appearanceRoadShowInfo = 1;
}

message SetAppearanceRoadShow_S2C_Msg {

}

// 二次元装扮描边设置
message AnimeDressOutlineSet_C2S_Msg {
  optional int32 animeDressOutline = 1;
}

message AnimeDressOutlineSet_S2C_Msg {
}

// 保存展板内容(不改变展板的打开状态)
message DisplayBoardSave_C2S_Msg {
  optional int64 itemUuid = 1;                  // itemUuid
  optional string displayBoardContent = 2;      // 内容
  optional int64 ugcId = 3;                     // ugc地图id
  optional UgcCreateRoom_C2S_Msg ugcCreateRoom = 4;
}

message DisplayBoardSave_S2C_Msg {
  optional proto_AttrDisplayBoardInfo displayBoardInfo = 1;
}

// 推图展板清空
message DisplayBoardClear_C2S_Msg {
}

message DisplayBoardClear_S2C_Msg {
  optional proto_AttrDisplayBoardInfo displayBoardInfo = 1;
}

// 推图展板打开/取消
message DisplayBoardOpen_C2S_Msg {
  optional bool isOpen = 5;                     // 是否打开
}

message DisplayBoardOpen_S2C_Msg {
  optional proto_AttrDisplayBoardInfo displayBoardInfo = 1;
  optional int32 result = 2;					// NKErrorCode
}

// 历史设置
message DisplayBoardHistorySetting_C2S_Msg {
  optional int32 pageIndex = 1;     // 页数从1开始, 每页数量固定10调. 时间从最近到最远
}

message DisplayBoardHistorySetting_S2C_Msg {// message RpcGetUgcPublishMapReq
  repeated PublishItem mapInfos = 1;
  optional UgcKeyInfo keyInfo = 2;
  optional bool hasMorePage = 3;
}

// 推图展板更新
message DisplayBoardUpdateNtf {
  optional proto_AttrDisplayBoardInfo displayBoardInfo = 1;
}


// 痛包装备吧唧
message ItaBagBadgeEquip_C2S_Msg {
  optional proto_ItaBagInfo itaBagInfo = 1;
}

message ItaBagBadgeEquip_S2C_Msg {
  
}