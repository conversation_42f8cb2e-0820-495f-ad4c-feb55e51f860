syntax = "proto2";

option cc_generic_services = false;
package com.tencent.wea.protocol;

import "common.proto";
import "ResKeywords.proto";
import "attr_ChaseIdentityRankItem.proto";

//装扮道具穿戴
message ChaseDressUpItem_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 actorId = 2;
  optional int32 opType = 3; // 0传 1脱
}
message ChaseDressUpItem_S2C_Msg {
}

message ChaseBatchGetGameData_C2S_Msg {
  repeated ChaseBatchGetPlayerList players = 1;
  repeated PlayerChasePublicInfoField fields = 2;
}
message ChaseBatchGetGameData_S2C_Msg {
  repeated ChasePlayerGameData gameData = 1;
}

enum PlayerChasePublicInfoField {
  PCPIF_None = 0;
  PCPIF_IDENTITY_ID = 1;
  PCPIF_IDENTITY_LASTWEEKBESTRANK = 2;
}

message ChaseBatchGetPlayerList {
  optional int64 uid = 1;
  repeated ChaseBatchGetPlayerActorList actors = 2;
}
message ChaseBatchGetPlayerActorList {
  optional int32 actorId = 1;
  optional int32 actorType = 2; // 0星宝  1暗星
}
message ChasePlayerGameData{
  optional int64 uid = 1;
  repeated ChasePlayerActorDetail detail = 2;
}
message ChasePlayerActorDetail {
  optional int32 actorId = 1;
  optional int32 actorType = 2;
  optional int32 identityId = 3;
  optional proto_ChaseIdentityRankItem lastWeekBestRank = 4;
}

// 设置
message ChaseSetSettings_C2S_Msg {
  optional int32 newBieGuide = 1;  // 新手指引, 0开启，1关闭
}
message ChaseSetSettings_S2C_Msg {
}

// 拉取角色熟练度数据

message ChaseGetIdentityProficiencyInfo_C2S_Msg {
  repeated int32 actorIds = 1; //角色ID
}

message ChaseGetIdentityProficiencyInfo_S2C_Msg {
  repeated ChaseIdentityProficiencyInfo info = 1;
}

message ChaseIdentityProficiencyInfo {
  optional int32 actorId = 1;
  repeated int32 unlockBiography = 2; //已解锁的角色小传
  optional int32 proficiency = 3;     //熟练度
  repeated int32 climeProficiencyRewards = 4; //已领取的身份进度奖励
}

message ChaseIdentityBattlePerformance_C2S_Msg {
  repeated int32 actorIds = 1; //角色ID
}

//拉取角色对局表现
message ChaseIdentityBattlePerformance_S2C_Msg {
  repeated ChaseIdentityBattlePerformanceInfo info = 1;
}

message ChaseIdentityBattlePerformanceInfo {
  optional int32 actorId = 1;
  repeated ChaseIdentityBattlePerformance battlePerformance = 2;
}

message ChaseIdentityBattlePerformance {
  optional int32 type = 1;
  optional int64 value = 2;
}

//领取角色进度奖励
message ChaseIdentityDrawProficiencyProgressReward_C2S_Msg {
  optional int32 actorId = 1;
  repeated int32 rewardId = 2; //奖励ID
}

message ChaseIdentityDrawProficiencyProgressReward_S2C_Msg {
  repeated ChaseIdentityProficiencyInfo info = 1;
}

//阅读角色小传
message ChaseIdentityReadBiography_C2S_Msg {
  optional int32 actorId = 1;
  optional int32 biographyId = 2; //小传ID
}

message ChaseIdentityReadBiography_S2C_Msg {

}