syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "ResKeywords.proto";
import "ResCommon.proto";
import "base_common.proto";
import "common.proto";
import "ResActivity.proto";
import "attr_LuckyMoneyActivity.proto";
import "attr_ActivityRedDot.proto";
import "attr_SquadItemInfo.proto";
import "attr_ScratchOffTicketsActivity.proto";
import "attr_ScratchOffTicketInfo.proto";
import "attr_LuckyStarInfo.proto";
import "attr_UseItemShareRecord.proto";
import "attr_ItemChangeRecord.proto";
import "attr_UpgradeCheckInInfo.proto";
import "attr_KungFuPandaHelpData.proto";
import "attr_ActivitySquadDetail.proto";
import "attr_FlyingChessActivityPosInfo.proto";
import "player_info.proto";
import "attr_AnimalHandbookAnimalInfo.proto";
import "ResActivityLotteryDraw.proto";
import "attr_ThemeAdventureRewardUpgradeData.proto";
import "attr_ActivityDetail.proto";
import "attr_FindPartnerQuestionAnswer.proto";
import "attr_AttrInflateRedPacketData.proto";

// 请求活动数据请求
message ActivityGetAllInfo_C2S_Msg {
  repeated int32 activityIds = 1;	// 活动ID列表,如果为空则下发全量活动数据
}

// 请求活动数据返回
message ActivityGetAllInfo_S2C_Msg {
  repeated int32 activityIds = 1;     // 活动ID列表
  optional bytes allActivityInfo = 2; // 活动数据 By:CsAllPlayerActivityInfo
}

// 领取活动奖励请求
message ActivityReceiveRewards_C2S_Msg {
  optional int32 activityId = 1;	   // 活动ID
  optional int32 type = 2;	           // 领取奖励的类型 0.任务奖励 1.活动奖励
  repeated ReceiveInfo rewardInfo = 3; // 奖励详细信息
  optional int64 seqId = 4;		       // 序列ID
  optional bool useItemQuitFinish = 6;    // 是否使用道具快速完成
}

// 领取活动奖励返回
message ActivityReceiveRewards_S2C_Msg {
  optional int32 result = 1;		   // 奖励领取结果
  optional int32 activityId = 2;	   // 活动ID
  optional int32 type = 3;	           // 领取奖励的类型 0.任务奖励 1.活动奖励
  repeated ReceiveInfo rewardInfo = 4; // 奖励详细信息
  optional int64 seqId = 5;		       // 序列ID
  optional bool useItemQuitFinish = 6;    // 是否使用道具快速完成
}

// 领取活动奖励结果通知
message ActivityReceiveRewardsNtf {
  optional int32 result = 1;		   // 奖励领取结果
  optional int32 activityId = 2;	   // 活动ID
  optional int32 type = 3;	           // 领取奖励的类型 0.任务奖励 1.活动奖励
  repeated ReceiveInfo rewardInfo = 4; // 奖励详细信息
  optional int64 seqId = 5;		       // 序列ID
}

// 活动数据更新通知
message ActivityInfoUpdateNtf {
  optional bytes allActivityInfo = 1; // 活动数据 By:CsAllPlayerActivityInfo
}

// 活动通用请求
message ActivityGeneral_C2S_Msg {
  optional int32 msgType = 1;		// 协议类型 By: ActivityGeneralMsgType
  optional bytes reqInfo = 2;		// 请求信息 By: ActivityGeneralReqInfo
  optional int64 seqId = 3;		    // 序列ID
  optional int32 activityId = 4;	// 活动ID
}

// 活动通用返回
message ActivityGeneral_S2C_Msg {
  optional int64 uid = 1;
  optional int32 result = 2;		// 请求结果
  optional int32 msgType = 3;		// 协议类型
  optional bytes rspInfo = 4;		// 返回信息 By: ActivityGeneralRspInfo
  optional int64 seqId = 5;		    // 序列ID
  optional int32 activityId = 6;	// 活动ID
  repeated int32 curRunActivityIds = 7;	// 玩家当前身上所有生效的活动Id
}

// 活动通用请求结果通知
message ActivityGeneralNtf {
  optional int32 result = 1;		// 请求结果
  optional int32 msgType = 2;		// 协议类型 ActivityGeneralNtyMsgType
  optional bytes rspInfo = 3;		// 返回信息 By: ActivityGeneralNtyInfo
  optional int64 seqId = 4;		    // 序列ID 废弃
  optional int32 activityId = 5;	// 活动ID
}

message ExchangeItemInfo{
  optional int64 itemId = 1; // 奖励对应的道具id
  optional int64 exchangeItemNum = 2; // 奖励对应的道具数量
}

message DKGiftItemInfo {
  optional int32 item_id = 1;
  optional int32 item_num = 2;
  optional int32 item_price = 3;
}

message DKGiftInfo {
  optional int64 id = 1;
  optional int32 type = 2;
  optional int32 time_type = 3;
  repeated DKGiftItemInfo item_info = 4;
  optional int32 ori_price = 5;
  optional int32 cur_price = 6;
  optional string title = 7; // 主标题 url图片地址
  optional string subtitle = 8; // 副标题文本
  optional int64 start_time = 9;
  optional int64 end_time = 10;
  optional int32 order = 11;  // 礼包显示顺序
  optional int32 max_buy_count = 12;  // 最大购买次数
  optional int32 cur_buy_count = 13;  // 当前购买次数
  optional string name = 14; // tab页名字
}

message DKGiftInfoNtf {
  repeated DKGiftInfo info = 1;
}

message DKGiftInfo_C2S_Msg {
}

message DKGiftInfo_S2C_Msg {
}

message DKBuyGift_C2S_Msg {
  optional int64 gift_id = 1;
}

message DKBuyGift_S2C_Msg {
  repeated DKGiftItemInfo item_info = 1;
  optional int32 cur_buy_count = 2; //当前礼包的已购买次数
}

// 实时干预获取平台好友可分享列表
message PlatShareActiveList_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message PlatShareFriendInfo {
  optional uint64 uid = 1;
  optional bool isSecret = 2;
  optional string openId = 3;
  optional string name = 4;
  optional string profile40 = 5;
}

message PlatShareActiveList_S2C_Msg {
  optional int32 activity_id = 1; // 活动id
  repeated PlatShareFriendInfo shareInfo = 2;
  optional ItemArray rewardInfo = 3;
  optional com.tencent.wea.xlsRes.RewardStatus rewardStatus = 4;
}

// 客户端发送成功
message PlatShareActiveSend_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  repeated uint64 inviteesUid = 2;
  repeated string friendsOpenId = 3;
}

message PlatShareActiveSend_S2C_Msg {
  optional int32 activity_id = 1; // 活动id
}


// 领奖
message PlatShareActiveReward_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message PlatShareActiveReward_S2C_Msg {
  optional int32 activity_id = 1; // 活动id
  optional ItemArray rewardInfo = 2;
}

//拉取活动中心列表, 包含各个活动红点
message ActivityListAll_C2S_Msg {
}

message ActivityRedDotInfo {
  optional int32 activity_id = 1;
  optional bool reddot_show = 2;
  optional bool is_new = 3;         //上新红点
  repeated proto_ActivityRedDot clickRedDotInfo = 4;  //点击消失红点
  optional com.tencent.wea.xlsRes.ActivityMainConfig activityConfig = 5; //拍脸下发专用
  optional bool showInCenter = 6; //是否是活动中心活动
}

//全量下发
message ActivityListAll_S2C_Msg {
  repeated com.tencent.wea.xlsRes.ActivityMainConfig activityLabel = 1; // 活动列表
  optional string recid = 2;  // 推荐信息
  optional string expTags = 3;  // 实验信息
}

// 根据活动类型返回进行中的活动
message ActivityGetActivityByType_C2S_Msg{
  optional com.tencent.wea.xlsRes.ActivityType activityType = 1;
  optional com.tencent.wea.xlsRes.ActivityNameType activityNameType = 2;
}

message ActivityGetActivityByType_S2C_Msg
{
  optional com.tencent.wea.xlsRes.ActivityMainConfig activity = 1;
}

//活动红点变化通知
message ActivityReadDotNtf {
  repeated ActivityRedDotInfo redDotShow = 1;   // 活动红点
}

//点击活动页签, 取消新活动红点
message ActivityClickRedDot_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional com.tencent.wea.xlsRes.ActivityRedDotType redDotType = 2;  // 红点类型
  optional int64 redDotId = 3;    // 红点ID(活动自定义)
}

message ActivityClickRedDot_S2C_Msg {
}

// 活动当前信息通知
message ActivityInfoNtf {
  optional com.tencent.wea.xlsRes.ActivityStatusType status = 1; // 活动状态
  optional int32 activity_id = 2; // 活动id
}

// 超级红包活动 - 分享红包列表查看
message ListLuckyMoney_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message ListLuckyMoney_S2C_Msg {
  optional proto_LuckyMoneyActivity luckyMoneyActivity = 1; //分享红包预览
}

// 超级红包活动 - 分享红包
message ShareLuckyMoney_C2S_Msg {
  optional int32 lucky_money_index = 1; // 红包编号
  optional int32 activity_id = 2; // 活动id
}

message ShareLuckyMoney_S2C_Msg {
  optional int64 lucky_money_id = 1;    // 红包唯一id
}

// 领取红包奖励(自己分享或者好友分享)
message GetLuckyMoneyReward_C2S_Msg {
  optional int64 lucky_money_id = 1;    // 红包唯一id
  optional int32 activity_id = 2; // 活动id
}

message LuckyMoneyReward {
  optional int64 player_uid = 1;        // 玩家uid
  repeated RewardItemInfo reward = 2;   // 红包奖励
  optional string nick_name = 3;        // 玩家昵称
}

message GetLuckyMoneyReward_S2C_Msg {
  repeated RewardItemInfo reward = 1;                     // 红包奖励
  optional int64 shareTimeMs = 2;                         // 首次分享时间
}

// 查看红包详情
message GetLuckyMoneyDetailInfo_C2S_Msg {
  optional int64 lucky_money_id = 1;    // 红包唯一id
  optional int32 activity_id = 2; // 活动id
}

message GetLuckyMoneyDetailInfo_S2C_Msg {
  repeated RewardItemInfo selfReward = 1;                 // 自己的红包奖励
  repeated LuckyMoneyReward friendRewardList = 2;         // 好友领取的奖励
}

// 元梦新星
message DreamNewStarTaskList_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message DreamNewStarInfo {
  optional uint64 uid = 1;
  optional string openId = 2;
  optional string name = 3;
  optional int32 level = 4;
  optional string profile = 5;
  optional int32 gender = 6;
  repeated int32 dressUpItems = 7;
  optional int64 registerTimeMs = 8; // 拉新为注册时间，召回为签约时间
  optional bool isRecall = 9; // 是否回流用户
}

message DreamNewStarTaskList_S2C_Msg {
  optional int32 activityId = 1; // 活动id
  repeated DreamNewStarInfo dreamNewStar = 2;
}

// 理财银行
// 查看信息
message CheckInInfo {
  optional int32 weekDay = 1; // 周几
  optional com.tencent.wea.xlsRes.CheckInState state = 2; // 状态
  optional RewardConfInfo rewardConf = 3; // 奖励配置
}
message WealthBankGetInfo_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}
message WealthBankGetInfo_S2C_Msg {
  repeated CheckInInfo checkInDays = 1; // 签到状态
  optional int32 deposit = 2; // 存款
  optional int32 makeUpTimes = 3; // 剩余补签次数
}

// 签到
message WealthBankCheckIn_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 weekDay = 2; // 周几
  optional bool makeUp = 3; // 是否补签
}
message WealthBankCheckIn_S2C_Msg {
  optional int32 deposit = 1; // 最新存款数量
  optional int32 makeUpTimes = 2; // 最新剩余补签次数
}

// 领取存款
message WealthBankReceiveDeposit_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}
message WealthBankReceiveDeposit_S2C_Msg {
  optional int32 deposit = 1; // 最新存款数量
}

// 获取已打卡列表
message CheckInPlanGetInfo_C2S_Msg {
  optional int32 activityId = 1;
}
message CheckInPlanGetInfo_S2C_Msg {
  repeated int32 dayList = 1; // 已打卡列表
  repeated int32 rewardedList = 2; // 已获得奖励
  optional int32 canCheckDay = 4; // 可打卡的天数id
  optional int32 canDrawRewardNum = 5; // 可抽奖次数
  optional int32 canMakeUpTimes = 6; // 可补签次数
}

// 打卡
message CheckInPlanCheckIn_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 day = 2; // 第几天
  optional bool makeUp = 3; // 是否补签
}
message CheckInPlanCheckIn_S2C_Msg {
  optional int32 canDrawRewardNum = 1; // 可抽奖次数
  optional int32 canMakeUpTimes = 2; // 可补签次数
}

// 抽奖
message CheckInPlanDraw_C2S_Msg {
  optional int32 activityId = 1;
}
message CheckInPlanDraw_S2C_Msg {
  optional int32 canDrawRewardNum = 1; // 可抽奖次数
  optional int32 rewardId = 2;
}

// 一元幸启
// 获取信息
message InterServerGiftGetInfo_C2S_Msg {
  optional int32 activityId = 1;
}
message InterServerGiftGetInfo_S2C_Msg {
  repeated int32 unlockPieces = 1; // 解锁的拼图
  optional int64 totalProgress = 2; // 全服进度
  repeated int32 receivedProgressReward = 3; // 已领进度奖励
  optional int32 buyNum = 4; // 已购买次数
  optional bool receivedFinalReward = 5; // 已领取终极大奖
}

// 购买拼图
message InterServerGiftBuy_C2S_Msg {
  optional int32 activityId = 1;
  optional int64 friendUid = 2;
}
message InterServerGiftBuy_S2C_Msg {

}

// 解锁的拼图通知
message InterServerGiftUnlockNtf {
  repeated int32 unlockPieces = 1; // 解锁的拼图
}

// 领取奖励
message InterServerGiftGetReward_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 progressRewardId = 2;
  optional int32 finalRewardId = 3;
}
message InterServerGiftGetReward_S2C_Msg {

}

message DigTreasureOfMultiPlayerSquad_C2S_Msg {
  optional int64 squadId = 1; // 小队uid
  optional int32 index = 2; // 宝藏位置
  optional int32 activityId = 3; // 活动id
}

message DigTreasureOfMultiPlayerSquad_S2C_Msg {
  optional proto_SquadItemInfo treasureInfo = 1; // 挖开的宝藏信息
}

message TakeawayGetRewardBox_C2S_Msg {
  optional int32 activityId = 1;
}

message TakeawayGetRewardBox_S2C_Msg {
}

message TakeawayStartUnlockBox_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 boxId = 2;
}

message TakeawayStartUnlockBox_S2C_Msg {
}

message TakeawayGetSharingReward_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 count = 2;
}

message TakeawayGetSharingReward_S2C_Msg {
}
//设置烟花显示文本
message SetFireworksText_C2S_Msg {
  optional string showText = 1; //设置的文本内容
}
message SetFireworksText_S2C_Msg {
  optional int32 ret = 1; //返回码
  optional string showText = 2; //要设置的文本内容
}

//使用烟花物品
message UseFireworksItem_C2S_Msg {
  optional int32 fireworkCfgId = 1; //配表Id
  optional IntVector3 playerPos = 2; //玩家位置
  optional int32 fireworkColorCfgId = 3; //烟花颜色配表id
}
message UseFireworksItem_S2C_Msg {
  optional int32 ret = 1; //返回码
  optional int32 fireworkCfgId = 2; //配表Id
  optional string showText = 3; //显示的文字
  optional IntVector3 playerPos = 4; //玩家位置
  optional string msg = 5; //当ret码是-10200008时，客户端读这个显示提示
  optional int64 limitEndTime = 6; //封禁结束时间(unixtime毫秒时间)
  optional int32 fireworkColorCfgId = 7; //烟花颜色配表id
}

//使用烟花物品通知
message UseFireworksNtf {
  optional int32 fireworkCfgId = 1; //使用的物品
  optional string showText = 2; //显示的文字
  optional IntVector3 playerPos = 3; //玩家位置
  optional int64 playerUid = 4; //放烟花玩家的uid
  optional string playerName = 5; //放烟花玩家的名字
  optional int32 fireworkColorCfgId = 6; //烟花颜色配表id
}

//烟花活动设置的文本
message GetFireworksInfo_C2S_Msg {
}
message GetFireworksInfo_S2C_Msg {
   optional int32 ret = 1; //返回码
  optional string showText = 2; //显示的文字
}

// 红包雨 玩家捡红包 c2s
message RedEnvelopRainTake_C2S_Msg {
  optional int32 actId = 1;       // 活动id
  optional int32 turnId = 2;      // 轮次id
  optional int32 cnt = 3;    // 捡到的个数
}

// 红包雨 玩家捡红包 s2c
message RedEnvelopRainTake_S2C_Msg {
}

// 红包雨 玩家开红包 c2s
message RedEnvelopRainOpen_C2S_Msg {
  optional int32 actId = 1;       // 活动id
}

// 红包雨 玩家开红包 s2c
message RedEnvelopRainOpen_S2C_Msg {
}

// 红包雨 玩家开红包 结果通知 开红包涉及idip发货, 和cs协议一起排队 无法同步返回
message RedEnvelopRainOpenResultNtf {
  optional int32 errCode = 1;
  repeated RewardItemInfo rewardItems = 2;      // 获得的道具
}

// 红包雨 奖励详情
message RedEnvelopRainRewardDetail {
  optional int64 ts = 2;                // 获取时间
  optional int32 amsStatus = 3;         // ams状态 -1 3次补发失败 0 成功 1 补发中 2 异步发货中
  repeated RewardItemInfo rewardItems = 4;  // 以道具id显示的奖励
}

// 红包雨 奖励查询 c2s
message RedEnvelopRainQueryReward_C2S_Msg {
  optional int32 actId = 1;               // 活动id
  optional int32 pageNow = 4;             // 当前页(从1开始)
}

// 红包雨 奖励查询 s2c
message RedEnvelopRainQueryReward_S2C_Msg {
  repeated RedEnvelopRainRewardDetail rewardDetails = 1;      // 获得的道具
  optional int32 totalCnt = 2;                                // 总奖励数
  optional int32 pageNow = 3;                                 // 当前页(从1开始)
  optional int32 pageSize = 4;                                // 页大小
}

// 红包雨 设置红点 c2s
message RedEnvelopeRainSetRedPoint_C2S_Msg {
  optional int32 actId = 1;                        // 活动id
  optional int32 newVal = 2;                       // 新的值
}

// 红包雨 设置红点 s2c
message RedEnvelopeRainSetRedPoint_S2C_Msg {
}

// 红包雨 分享了活动 c2s
message RedEnvelopeRainShared_C2S_Msg {
  optional int32 actId = 1;                        // 活动id
}

// 红包雨 分享了活动 s2c
message RedEnvelopeRainShared_S2C_Msg {
}

// 红包雨 点了其他人的分享 c2s
message RedEnvelopeRainClickShareLink_C2S_Msg {
  optional int32 actId = 1;
  optional int64 sharerUid = 2;
}

// 红包雨 点了其他人的分享 s2c
message RedEnvelopeRainClickShareLink_S2C_Msg {
}

// 红包雨 有人点了自己的分享 ntf
message RedEnvelopeRainSharedLinkClickedNtf {
  optional int32 actId = 1;                        // 活动id
}

message SquadRefreshNtf {
  optional int64 squadId = 1; //小队id
  optional int32 squadType= 2; //  ActivityType:ATFriendSquad(友情小队) = 6;ATSquad(多人小队) = 15;
  optional int32 activityId = 3; // 活动id
  optional bytes teamData = 4;	  // 活动服发送的小队信息,需要客户端反序列化
}


message SuperLinearActivityConfig_C2S_Msg {
  optional int32 actId = 1;               // 活动id
}

message SuperLinearActivityConfig_S2C_Msg {
  optional int32 actId = 1;               // 活动id
  repeated com.tencent.wea.xlsRes.ActivitySuperLinearRedeemConfig configList = 2;
}


// 游园会前进
message SuperLinearDraw_C2S_Msg {
  optional int32 actId = 1;               // 活动id
}

message SuperLinearDraw_S2C_Msg {
  optional int32 actId = 1;               // 活动id
  optional int32 addValue = 2;            // 增加多少米
}


// 游园会进度奖励领取
message SuperLinearGetReward_C2S_Msg {
  optional int32 actId = 1;               // 活动id
  optional int32 targetValue = 2;         // 奖励id
}

message SuperLinearGetReward_S2C_Msg {
  optional int32 actId = 1;               // 活动id
  optional int32 targetValue = 2;         // 奖励id
}

message LuckyBalloonShoot_C2S_Msg {
  optional int32 activityId = 1;
}

message LuckyBalloonShoot_S2C_Msg {

}

// 领取开学返利签到奖励
message GetTimeLimitedCheckInReward_C2S_Msg {
  optional int32 activityId = 1;    // 活动ID
  optional int32 configIndex = 2;   // 时间段配置索引，从0开始
  optional int32 checkInDayCnt = 3; // 连续签到几天的奖励
  optional bool isMakeup = 4;       // 是否补签
}

message GetTimeLimitedCheckInReward_S2C_Msg {

}

// 刮刮乐活动 - 查看刮刮乐
message ListScratchOffTickets_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message ListScratchOffTickets_S2C_Msg {
  optional proto_ScratchOffTicketsActivity scratchOffTicketsActivity = 1; //查看刮刮乐
}

// 刮刮乐活动 - 开始刮(此时随机奖励)
message BeginScratchOffTickets_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 ticketId = 2;  // 刮刮乐id(第几个)
}

message BeginScratchOffTickets_S2C_Msg {
  optional int32 ticketId = 1;  // 刮刮乐id(第几个)
  repeated RewardItemInfo baseRewards = 2;    // 基础奖励
  repeated RewardItemInfo higherRewards = 3;  // 高级奖励
  optional bool isOpenHigher = 4; // 是否已升级高级奖励
}

// 刮刮乐活动 - 获取刮刮乐奖励(不随机, 纯领奖)
message GetScratchOffTicketsReward_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 ticketId = 2;  // 刮刮乐id(第几个)
}

message GetScratchOffTicketsReward_S2C_Msg {
  optional int32 ticketId = 1;  // 刮刮乐id(第几个)
  optional proto_ScratchOffTicketInfo ticketInfo = 2; // 刮刮乐信息
}

// 刮刮乐活动 - 升级高级
message UpgradeHigherScratchOffTickets_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message UpgradeHigherScratchOffTickets_S2C_Msg {

}

message ScratchOffTicketsUpgradeNtf {
  optional int32 activityId = 1;
}

// 春节发红包 玩家发红包 c2s
message RedPacketSend_C2S_Msg {
  optional int32 activityId = 1;          // 活动id
  optional int32 itemId = 2;              // 道具id
  optional int64 itemUuid = 3;            // 道具uuid
  optional int64 senderUid = 4;           // 发送玩家的uid
  optional IntVector3 pos = 5;            // 红包位置
  repeated int64 params = 6;
}

// 春节发红包 玩家发红包 s2c
message RedPacketSend_S2C_Msg {
  optional int64 packetUuid = 1;    // 所发红包UUID
}

// 春节发红包 玩家开红包 c2s
message RedPacketOpen_C2S_Msg {
  optional int32 activityId = 1;          // 活动id
  optional int64 packetUuid = 2;          // 红包uuid
  optional int64 receiverUid = 3;         // 接收玩家的uid
}

// 春节发红包 玩家开红包 s2c
message RedPacketOpen_S2C_Msg {
}

// 春节发红包 玩家开红包 结果通知
message RedPacketOpenResultNtf {
  optional int32 errCode = 1;
  repeated RewardItemInfo rewardItems = 2;       // 获得的道具
  optional PlayerColdData senderSnapshot = 3;    // 发送玩家的信息
  optional int64 packetUuid = 4;                 // 红包uuid
}

// 春节发红包 我收到的红包详情
message RedPacketRecvDetail {
  optional int64 packetUuid = 1;                 // 红包uuid
  optional string packetName = 2;                // 红包的名称
  optional int64 ts = 3;                         // 拾取时间
  optional PlayerColdData senderSnapshot = 4;    // 发送玩家的信息
  repeated RewardItemInfo rewardItems = 5;       // 以道具id显示的奖励
  optional string replyMsg = 6;                  // 答谢消息
  optional RedPacketPositionType placeType = 7;  // 红包位置类型
  optional string placeName = 8;                 // 红包位置名称
}

// 春节发红包 我发出的红包 具体拾取情况
message RedPacketSentRecvDetail {
  optional PlayerColdData receiverSnapshot = 1;  // 拾取玩家的信息
  optional int64 ts = 2;                         // 拾取时间
  repeated RewardItemInfo rewardItems = 3;       // 以道具id显示的奖励
  optional string replyMsg = 4;                  // 答谢消息
  optional RedPacketPositionType placeType = 5;  // 红包位置类型
  optional string placeName = 6;                 // 红包位置名称
}

// 春节发红包 我发出的红包详情
message RedPacketSentDetail {
  optional int64 packetUuid = 1;                 // 红包id
  optional string packetName = 2;                // 红包的名称
  optional int64 ts = 3;                         // 发放时间
  optional int32 recvCount = 4;                  // 已拾取的数量
  optional int32 totalCount = 5;                 // 红包总数量
  repeated RedPacketSentRecvDetail details = 6;  // 红包拾取详情
  optional bool isDirty = 7;                     // 是否有变更
}

// 春节发红包 我的红包查询 c2s
message RedPacketQuery_C2S_Msg {
  optional int32 activityId = 1;                 // 活动id
}

// 春节发红包 我的红包查询 s2c
message RedPacketQuery_S2C_Msg {
  repeated RedPacketRecvDetail recvDetails = 1;  // 收到的红包列表
  repeated RedPacketSentDetail sentDetails = 2;  // 发出的红包列表
  optional bool isSentDetailsDirty = 3;          // DEPRECATED: 发送列表是否有变更
}

// 春节发红包 清除红点 c2s
message RedPacketClickRedDot_C2S_Msg {
  optional int32 activityId = 1;                 // 活动id
}

// 春节发红包 清除红点 s2c
message RedPacketClickRedDot_S2C_Msg {
}

// 春节发红包 查询单个红包 c2s
message RedPacketQuerySingle_C2S_Msg {
  optional int64 packetUuid = 1;                 // 红包uuid
}

// 春节发红包 查询单个红包 s2c
message RedPacketQuerySingle_S2C_Msg {
  optional int64 packetUuid = 1;                 // 红包uuid
  optional int32 packetId = 2;                   // 红包配置id
  optional int64 sendTimeMs = 3;                 // 红包发放时间
  optional int64 senderUid = 4;                  // 发送红包玩家的uid
  optional RedPacketPositionType placeType = 5;  // 红包位置类型
  optional int64 placeId = 6;                    // 大厅/小窝ID
  optional ObjectPosition position = 7;          // 坐标
}

// 春节发红包 玩家分享红包 c2s
message RedPacketShare_C2S_Msg {
  optional int32 activityId = 1;          // 活动id
}

// 春节发红包 玩家分享红包 s2c
message RedPacketShare_S2C_Msg {
  optional string shareUrl = 1; //  分享链接
}

// 春节发红包 附加信息 c2s
message RedPacketPatchInfo_C2S_Msg {
  optional int64 packetUuid = 1;  // 红包uuid
  optional string replyMsg = 2;   // 答谢消息
}

// 春节发红包 附加信息 s2c
message RedPacketPatchInfo_S2C_Msg {
}

// 红包放置的位置类型
enum RedPacketPositionType {
  RPPT_INVALID = 0;
  RPPT_LOBBY = 1;   // 在广场
  RPPT_XIAOWO = 2;  // 在小窝
  RPPT_FARM = 3;  // 在农场
  RPPT_FARMHOUSE = 4;  // 在农场小屋
}

// 春节发红包 根据分享ID获取红包信息 c2s
message RedPacketGetByShareId_C2S_Msg {
  optional int64 shareId = 1;   // 分享id
}

// 春节发红包 根据分享ID获取红包信息 s2c
message RedPacketGetByShareId_S2C_Msg {
  optional int32 packetId = 1;                  // 红包配置ID
  optional int64 packetUuid = 2;                // 红包UUID
  optional RedPacketPositionType placeType = 3; // 红包位置类型
  optional int64 placeId = 4;                   // 大厅/小窝ID
  optional ObjectPosition position = 5;         // 红包位置信息
}

// 春节发红包 跳转到红包场景
message RedPacketEnterScene_C2S_Msg {
  optional int64 packetUuid = 1;                 // 红包uuid
}
message RedPacketEnterScene_S2C_Msg {
  optional RedPacketPositionType placeType = 1;  // 红包位置类型
  optional int64 placeId = 2;                    // 大厅/小窝ID
  optional ObjectPosition position = 3;          // 坐标
}

// 积攒福气-助力好友
message ConfirmAssistFriend_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int64 friendUid = 2;   // 好友uid
}

message ConfirmAssistFriend_S2C_Msg {
  repeated RewardItemInfo reward = 1;
}

// 积攒福气-获取累计登录奖励
message GetAccumulateBlessingsReward_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int32 loginCumulative = 2; // 累计登录天数
}

message GetAccumulateBlessingsReward_S2C_Msg {
  repeated RewardItemInfo reward = 1;
}

// 刷新返回在线时间
message GetAccumulateBlessingsOnlineTime_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
}

message GetAccumulateBlessingsOnlineTime_S2C_Msg {
  optional int64 onlineTimeMs = 1;
  optional int32 blessingValue = 2; // 福气值
}

// 奥特曼主题活动-获取全服收集进度数据
message UltramanThemeCollectionProgress_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message UltramanThemeCollectionProgress_S2C_Msg {
  optional int64 collectionProgress = 1; // 当前的收集进度
  optional int64 lastCollectionProgress = 2; // 上一次记录的收集进度
}

// 奥特曼主题活动-获取个人收集进度
message UltramanThemeSelfCollectionProgress_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 itemId = 2;
}

message UltramanThemeSelfCollectionProgress_S2C_Msg {
  optional int32 selfCollectionProgress = 1;
}

// 个人进度
message UltramanThemeAddSelfCollection_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 itemId = 2;
  optional int32 num = 3;
}

message UltramanThemeAddSelfCollection_S2C_Msg {
}

// 奥特曼主题活动-领取进度奖励
message UltramanThemeReward_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 rewardId = 2; // 奖励id
}

message UltramanThemeReward_S2C_Msg {
  repeated RewardItemInfo reward = 1;
}

// 奥特曼活动状态通知
message UltramanThemeStatusNtf {
  optional com.tencent.wea.xlsRes.ActivityStatusType status = 1; // 活动状态
}

// 奥特曼主题活动-剧情访问
message UltramanThemeActivateStory_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 storyId = 2; // 剧情id
}

message UltramanThemeActivateStory_S2C_Msg {
  optional bool hasVisited = 1; // 是否访问过
}

// 奥特曼主题活动-小队任务
enum TeamTaskType {
  TTT_CreateTeam = 1;   // 创建队伍
  TTT_InvitePlayer = 2; // 邀请入队
  TTT_ExitTeam = 3;     // 离开队伍
  TTT_TransferLead = 4; // 转移队长
  TTT_KickoutPlayer = 5;// 踢出队伍
  TTT_MemberInfo = 6;   // 队员信息同步
  TTT_TeamInfo = 7;     // 队伍信息同步
  TTT_JoinTeam = 8;     // 加入队伍
}

message TeamMemberInfo {
  optional string profile = 1;          // 头像
  optional int32 gender = 2;            // 性别
  optional int32 friendshipVal = 3;     // 友情值
  optional string name = 4;             // 名字
  optional bool isCaptain = 5;          // 是否是队长
  optional bool canKickout = 8;         // 能否踢出队伍
  optional bool canQuit = 9;            // 能否退出队伍
  optional int64 player = 10;           // 玩家uid
}

message TeamInfo {
  optional int32 teamId = 1;            // 小队id
  optional bool isInTeam = 2;           // 是否在队
  optional int32 teamFriendShipVal = 3; // 小队友情值
  repeated TeamMemberInfo members = 4;  // 小队成员
}

// 创建队伍
message UltramanThemeAcitvityTeam_C2S_Msg {
  optional int32 activityId = 1;    // 活动id
  optional TeamTaskType type = 2;   // 类型
  optional int64 player = 3;        // 邀请/踢出/转移
}

message UltramanThemeAcitvityTeam_S2C_Msg {
  repeated int64 members = 1;       // 队员信息同步（废弃）
  optional TeamInfo info = 2;       // 队伍信息同步
  optional int64 player = 3;        // 当前玩家
}

// 福星手账簿
// 解锁福星
message LuckyStarUnlockStar_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
}
message LuckyStarUnlockStar_S2C_Msg {
  optional proto_LuckyStarInfo starInfo = 1; // 福星信息
  optional ItemInfo rewardInfo = 2; // 奖励
}

// 获取手账簿信息
message LuckyStarGetDetailInfo_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
}
message LuckyStarGetDetailInfo_S2C_Msg {
  optional int32 roundNum = 1; // 第几阶段
  repeated proto_LuckyStarInfo starInfoList = 2; // 福星详细信息
  optional bool isRewarded = 3; // 是否已领奖
  optional bool newRequiredStar = 4; // 有别人赠送的索要的福星卡
}

// 生成索要福星卡
message LuckyStarGenerateRequireStar_C2S_Msg {
  optional int32 starId = 1; // 福星id
  optional int32 activityId = 2;  // 活动id
}
message LuckyStarGenerateRequireStar_S2C_Msg {
  optional string requireId = 1; // 索要id
}

// 赠送福星卡
message LuckyStarGiveStar_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional string requireId = 2; // 索要id
  optional int64 uniqueId = 3; // 福星卡唯一id
}
message LuckyStarGiveStar_S2C_Msg {

}

// 生成赠送福星卡
message LuckyStarGenerateGiveStar_C2S_Msg {
  optional int64 uniqueId = 1; // 福星卡唯一id
  optional int32 activityId = 2;  // 活动id
}
message LuckyStarGenerateGiveStar_S2C_Msg {
  optional string giveId = 1; // 赠送id
}

// 领取合成奖励
message LuckyStarGetReward_C2S_Msg {
  optional int32 roundNum = 1; // 第几阶段
  optional int32 activityId = 2;  // 活动id
}
message LuckyStarGetReward_S2C_Msg {

}

// 领取福星卡
message LuckyStarReceiveStar_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional string giveId = 2; // 赠送id
}
message LuckyStarReceiveStar_S2C_Msg {

}

// 福星卡赠送信息
message LuckyStarGiveInfo {
  optional proto_LuckyStarInfo starInfo = 1; // 福星卡详细信息
  optional PlayerColdData giverInfo = 2; // 赠送人信息
}

// 福星卡索要信息
message LuckyStarRequireInfo {
  optional int32 starId = 1; // 福星id
  optional PlayerColdData claimantInfo = 2; // 索要人信息
}

// 获取赠送信息
message LuckyStarGetGiveInfo_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional string giveId = 2; // 赠送id
}
message LuckyStarGetGiveInfo_S2C_Msg {
  optional LuckyStarGiveInfo giveInfo = 1; // 赠送信息
}

// 获取索要信息
message LuckyStarGetRequireInfo_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional string requireId = 2; // 赠送id
}
message LuckyStarGetRequireInfo_S2C_Msg {
  optional LuckyStarRequireInfo requireInfo = 1; // 赠送信息
}

// 获取索要结果
message LuckyStarGetRequireResult_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
}
message LuckyStarGetRequireResult_S2C_Msg {
  repeated LuckyStarGiveInfo giveInfoList = 1; // 福星卡赠送信息
}

message TimeLimitedCheckInActivityUnlockNtf {
  optional int32 activityId = 1;
}

message GetActivityConfig_C2S_Msg {
  repeated int32 activityIds = 1;
}

message GetActivityConfig_S2C_Msg {
  repeated com.tencent.wea.xlsRes.ActivityMainConfig activityConfs = 1;
}

// 集福活动数据变更通知
message SpringBlessingCollectionDataChangeNtf {
  optional string dummy = 1;
}

// 领取集福活动福卡奖励
message GetSpringBlessingCollectionCardReward_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 blessingCardId = 2;
}

message GetSpringBlessingCollectionCardReward_S2C_Msg {
  optional bool cdKeyReward = 1;  // 是否CDKey奖励
}

// 领取集福活动任务奖励
message GetSpringBlessingCollectionTaskReward_C2S_Msg {
  optional int32 activityId = 1;
  optional string dummy = 2;
}

message GetSpringBlessingCollectionTaskReward_S2C_Msg {

}

// 福字铺抽取福字
message LotterySpringBlessingCard_C2S_Msg {
  optional com.tencent.wea.xlsRes.BlessingCardSourceType sourceType = 1;
  optional int32 activityId = 2;
  optional int32 sponsorId = 3;   // 赞助商ID，在赞助商出抽取福字是需要上传
}

message LotterySpringBlessingCard_S2C_Msg {
  optional int32 cardId = 1;    // 抽到的福卡ID
}

// 给其他玩家赠送福卡
message GiveSpringBlessingCard_C2S_Msg {
  optional int64 targetId = 1;    // 对方玩家uid
  optional int32 cardId = 2;      // 赠送的福卡ID
  optional int32 activityId = 3;  // 活动ID
}

message GiveSpringBlessingCard_S2C_Msg {
  optional string dummy = 1;
}

// 财神祈福活动进行祈福
message SpringPray_C2S_Msg {
}

message SpringPray_S2C_Msg {

}

// 脑力活动通知
message IntellectualActiviyNtf {
  optional int32 activityId = 1;
  optional IntellectualActivityOperation type = 2;
  optional string info = 3;
  optional string data = 4;
}

// 玩家被赠送福字通知
message ReceiveSpringBlessingCardNtf {
  optional int64 sourcePlayerUid = 1;   // 赠送者
  optional int32 blessingCardId = 2;    // 福字ID
}

// 回归活动一次性奖励领取
message GetReturnActivityReward_C2S_Msg {
  optional int32 optionalRewardIndex = 1; // 可选奖励index
}

message GetReturnActivityReward_S2C_Msg {
  repeated int32 itemId = 1;
  repeated int32 itemNum = 2;
}

// 通知回归活动开始
message ReturningActiviyStartNtf {
  repeated int32 itemId = 1;
  repeated int32 itemNum = 2;
  repeated ReturningOptionalReward optionalReward = 3; // 可选奖励
}

message ReturningOptionalReward {
  optional int32 index = 1;
  repeated com.tencent.wea.xlsRes.RewardConf rewardInfo = 2;
}

message ReturnActivityEndNtf {
}

// 购买付费签到活动门票
message BuyReturnChargeSignInActivityTicket_C2S_Msg {
  optional int32 activityId = 1;
}

message BuyReturnChargeSignInActivityTicket_S2C_Msg {
}

message BuyReturnChargeSignInActivityTicketNtf {
  optional int32 activityId = 1;
}

message ReturnActivityAttrChangeNtf {
}

message ReturnActivityRefreshRecommendMatchType_C2S_Msg {
}

message ReturnActivityRefreshRecommendMatchType_S2C_Msg {
}

message ReturnActivityBuyChargeGiftTicket_C2S_Msg {
}

message ReturnActivityBuyChargeGiftTicket_S2C_Msg {
}

message ReturnActivityBuyChargeGiftTicketNtf {
}

message ReturnRefreshJumpToSignInAfterRoundFlag_C2S_Msg {
  optional bool value = 1;
}

message ReturnRefreshJumpToSignInAfterRoundFlag_S2C_Msg {
}


message ReturnActivityGenericNtf { // 预留
  optional int32 ntfType = 1;
  optional int64 longParam1 = 2;
  optional int64 longParam2 = 3;
  optional int64 longParam3 = 4;
  optional string stringParam = 5;
}

// 使用道具分享活动 - 查看分享列表
message ListUseItemShareRecord_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message ListUseItemShareRecord_S2C_Msg {
  repeated proto_UseItemShareRecord useItemShareRecordList = 1; //查看分享列表
}

// 使用道具分享活动 - 查看道具变动列表
message ListUseItemChangeRecord_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message ListUseItemChangeRecord_S2C_Msg {
  repeated proto_ItemChangeRecord itemChangeRecordList = 1; //查看道具变动列表
}

// 开学返利活动数据变化通知
message TimeLimitedCheckInActivityDataChangeNtf {

}

// 升级版打卡手册活动 - 领取打卡奖励
message GetCheckInManualReward_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 checkInIndex = 2;  // 打卡天数(第几个)
}

message GetCheckInManualReward_S2C_Msg {
  optional int32 checkInIndex = 1;  // 打卡天数(第几个)
  optional proto_UpgradeCheckInInfo checkInInfo = 2; // 打卡信息
}

// 升级版打卡手册活动 - 解锁高级
message UpgradeCheckInManual_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  //optional int32 buyNums = 2;     //解锁几周即(购买几张农场天天领权益卡)
}

message UpgradeCheckInManual_S2C_Msg {

}

message CheckInManualUpgradeNtf {
  optional int32 activityId = 1;
}

message UpgradeFarmDailyAwardNtf {
  optional int32 activityId = 1;
  optional int32 buyNums = 2;
}

//查看星级等级(即购买过的总次数)
message GetCheckInManualFarmDailyBuys_C2S_Msg {
}
message GetCheckInManualFarmDailyBuys_S2C_Msg {
  optional int32 buyNums = 1;     //购买次数
}

// 不放回抽奖活动 - 抽奖
message ActivityLotteryDraw_C2S_Msg {
  optional int32 activityId = 1;
}

message ActivityLotteryDraw_S2C_Msg {
  optional int32 totalDrawCount = 1; // 奖池抽取次数
  optional int32 rewardId = 2; // 抽中的奖励ID
  optional int32 rewardGetCount = 3; // 抽中奖励获取次数
}

// 获取可赠送好友列表
message InterServerGiftGetFriendList_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message InterServerGiftGetFriendList_S2C_Msg {
  repeated int64 uid = 1;
}

// 赠送前校验好友是否购买
message InterServerGiftCheckFriendBuy_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int64 uid = 2;
}

message InterServerGiftCheckFriendBuy_S2C_Msg {
  optional bool alreadyBuy = 1;
}

// 功夫熊猫活动 - 投喂
message FeedKungFuPanda_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 feedNoodleCount = 2; // 投喂汤面数量
}

message FeedKungFuPanda_S2C_Msg {
  optional int32 feedingCount = 1; // 已投喂次数
}

// 功夫熊猫活动 - 上报竞速结果
message ReportRacingKungFuPandaResult_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 racingCostTimeMs = 2;  // 竞速耗时
}

message ReportRacingKungFuPandaResult_S2C_Msg {

}

// 功夫熊猫活动 - 获取助力列表
message ListKungFuPandaHelpData_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message ListKungFuPandaHelpData_S2C_Msg {
  repeated proto_KungFuPandaHelpData helpList = 1;
}

// ============ 多人活动协议
// 获取多人活动小队信息
message ActivityGetMultiPlayerSquadInfo_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message ActivityGetMultiPlayerSquadInfo_S2C_Msg {
  optional proto_ActivitySquadDetail squadDetail = 1;  //GameSvr活动小队数据
  optional bytes teamData = 2;	  // 活动服发送的小队信息,需要客户端反序列化 by:proto_ActivitySquadDetail
}

// 获取多人活动小队是否满员
message ActivityGetMultiPlayerSquadIsFull_C2S_Msg {
  optional int32 activityId = 1;    // 活动id
  optional int64 squadId = 2;       // 小队id
}

message ActivityGetMultiPlayerSquadIsFull_S2C_Msg {
  optional bool isFull = 1;
}

// 加入多人活动小队
message ActivityMultiPlayerSquadJoin_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int64 groupId = 2; // 团队id
  optional int32 joinReason = 3;  // 加入来源 ActivityMultiPlayerSquadJoinReason
}

message ActivityMultiPlayerSquadJoin_S2C_Msg {
  optional proto_ActivitySquadDetail squadDetail = 1;
}

// 特殊活动的独立逻辑
// 四人成团小队保存合影
message ActivityMultiPlayerSquadSaveGroupPhoto_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int64 groupId = 2; // 团队id
  optional string groupName = 3; // 团队名称
  optional string groupPhotoUrl = 4; // 团队合影地址
}

message ActivityMultiPlayerSquadSaveGroupPhoto_S2C_Msg {
  optional proto_ActivitySquadDetail squadDetail = 1;
}

message ActivityTakePhoto_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message ActivityTakePhoto_S2C_Msg {

}

message ActivityGetRecommendPlayerList_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  repeated com.tencent.wea.protocol.PlayerPublicInfoField fields = 2; // 需要的玩家信息属性字段集合
}

message PlayerRecommendInfo {
  optional com.tencent.wea.protocol.PlayerPublicInfo basicPlayerInfo = 1; // 基础玩家信息，通过field查询的结果
  optional int64 lastRelatedTime = 2; // 上次关联的时间
  optional bool isPlatFriend = 3; // 是否是平台好友
  optional bool isGameFriend = 4; // 是否是游戏好友
}

message ActivityGetRecommendPlayerList_S2C_Msg {
  repeated PlayerRecommendInfo recommendPlayerList = 1;
}

message ActivityMarkPlayerInvited_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int64 uid = 2; // 已邀请的玩家uid
}

message ActivityMarkPlayerInvited_S2C_Msg {

}

// 接收小队进度性奖励
message ActivitySquadAcquireReward_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 rewardId = 2; // 奖励id
}

message ActivitySquadAcquireReward_S2C_Msg {

}

// 农场组队活动给摇钱树浇水
message ActivitySquadFarmWateringTree_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message ActivitySquadFarmWateringTree_S2C_Msg {

}

// 打开最终的宝箱
message ActivitySquadOpenFinalTreasure_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message ActivitySquadOpenFinalTreasure_S2C_Msg {

}

// 从小队中踢出目标玩家
message ActivitySquadKickMember_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int64 uid = 2; // 目标踢出的玩家uid
}

message ActivitySquadKickMember_S2C_Msg {
  optional proto_ActivitySquadDetail squadDetail = 1; // 返回最新的小队数据
}

// ============ 多人活动协议

message StickerGetReward_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 chapterId = 2;
  optional int32 positionId = 3;          // 章节不填
  optional int32 rewardType = 4;          // 1:普通 2:连线 3:章节
}

message StickerGetReward_S2C_Msg {

}

// 气泡配置
message CurBubbleConfigStateQuery_C2S_Msg {
}

message CurBubbleConfigStateQuery_S2C_Msg {
  optional int32 id = 1;
}

// 设置当前气泡访问
message CurBubbleConfigHasVisited_C2S_Msg {
  optional int32 id = 1;
}

message CurBubbleConfigHasVisited_S2C_Msg {

}

// 领取热身赛-星力运动会活动对局次数奖励
message GetCompetitionWarmUpGameTimesReward_C2S_Msg {
  optional int32 activityId = 1;    // 活动ID
  optional int32 stageId = 2;       // 阶段ID
  optional int32 gameTimes = 3;     // 对局次数
}

message GetCompetitionWarmUpGameTimesReward_S2C_Msg {
  optional string dummy = 1;
}

// 领取热身赛-星力运动会活动积分奖励
message GetCompetitionWarmUpScoreReward_C2S_Msg {
  optional int32 activityId = 1;    // 活动ID
  optional int32 stageId = 2;       // 阶段ID
  optional int32 rewardIndex = 3;   // 奖励索引ID
}

message GetCompetitionWarmUpScoreReward_S2C_Msg {
  optional string dummy = 1;
}

// 领取情报站进度奖励（废弃）
message GetIntelligenceStationReward_C2S_Msg {
  optional int32 activityId = 1;    // 活动ID
  optional int32 value = 2;         // 进度值
}

message GetIntelligenceStationReward_S2C_Msg {
  repeated RewardItemInfo reward = 1; // 奖励
}

// 情报站变更
message IntelligenceStationChangeNtf {
  optional int32 value = 1; // 当前进度
}

// 活动数据变更通知
message ActivityDataChangeNtf {
  optional com.tencent.wea.xlsRes.ActivityType activityType = 1;
  optional int32 activityId = 2;
}

// 订阅qq机器人
message SubscribeQQRobot_C2S_Msg {

}

message SubscribeQQRobot_S2C_Msg {

}
// 限时娱乐推荐领奖
message GetRecommendMatchTypeReward_C2S_Msg {
  optional int32 taskId = 1;    // 任务id
}

message GetRecommendMatchTypeReward_S2C_Msg {
}

message RecommendMatchTypeTaskNtf {

}


//点击推荐tips
message ClickNewRecommendMatchType_C2S_Msg {

}

message ClickNewRecommendMatchType_S2C_Msg {

}

message NewRecommendMatchTypeChangeNtf {
  optional bool newRecommend = 1;
}

// 走格子 飞行棋
// 获取初始代币
message ActivityFlyingChessGetFreeCoin_C2S_Msg {
  optional int32 activityId = 1;
}
message ActivityFlyingChessGetFreeCoin_S2C_Msg {
}
// 行进请求
message ActivityFlyingChessRun_C2S_Msg {
  optional int32 activityId = 1;
}

message ActivityFlyingChessRun_S2C_Msg {
  optional int32 runStep = 1; // 丢出来的步子数
  message PosInfo {
    optional proto_FlyingChessActivityPosInfo pos = 1;
    optional bool hasGetBoxReward = 2;
    optional int32 runType = 3;
  }
  repeated PosInfo posInfo = 2; // 本次行走经过的格子信息
}

// 获取圈奖励
message ActivityFlyingChessRoundRewardGet_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 roundId = 2; // 圈奖励ID
}

message ActivityFlyingChessRoundRewardGet_S2C_Msg {
  repeated int32 roundId = 1;
  repeated RewardItemInfo reward = 2; // 奖励
}

// 大奖获得请求
// 改为主动领取
message ActivityFlyingChessBigRewardGet_C2S_Msg {
  optional int32 activityId = 1;
}

message ActivityFlyingChessBigRewardGet_S2C_Msg {
  repeated RewardItemInfo reward = 1; // 奖励
}

message ActivityFlyingChessBigRewardGetNtf {
  optional int32 activityId = 1;
  repeated RewardItemInfo reward = 2; // 奖励
}

// 社团挑战
// 社团成员贡献列表请求
message ActivityClubChallengeStarLightInfo_C2S_Msg
{
  optional int32 activityId = 1;
}
message ActivityClubChallengeStarLightInfo_S2C_Msg
{
}
message ActivityClubChallengeStarLightInfoNtf
{
  message PlayerStarLightInfo{
    optional uint64 uid = 1;
    optional int32 starLight = 2;
  }
  repeated PlayerStarLightInfo playerStarLightInfo = 1;
}
// 标记玩法使用社团挑战模式
message ActivityClubChallengeMarkMatchType_C2S_Msg
{
  optional int32 activityId = 1;
  optional int32 matchTypeId = 2; // 玩法ID
  optional bool isOn = 3; // 是否勾选
}
message ActivityClubChallengeMarkMatchType_S2C_Msg
{
}
// 领取档位奖励请求
message ActivityClubChallengeAward_C2S_Msg
{
  optional int32 activityId = 1;
  optional int32 rewardId = 2 ;
}
message ActivityClubChallengeAward_S2C_Msg
{
}
// 社团挑战end

// 同步打卡信息
message ActivityHYNSyncInfoNtf {
  optional bool isRewarded = 1;  // 是否领过奖
  optional int32 checkInTimes = 2;  // 打卡的次数
  repeated int32 checkInIds = 3;  // 打卡的详细id
  optional int32 needCheckInTimes = 4;  // 当天需要的打卡次数
  repeated RewardItemInfo reward = 5;  // 目前的奖励
}

// 打卡上报
message ActivityHYNCheckIn_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 navigationActId = 2;  // 打卡的导航页活动ID
}

message ActivityHYNCheckIn_S2C_Msg {
  optional int32 errCode = 1;
}

// 领取当天的奖励
message ActivityHYNRecvReward_C2S_Msg {
  optional int32 activityId = 1;
}

message ActivityHYNRecvReward_S2C_Msg {
  optional int32 errCode = 1;
  repeated RewardItemInfo reward = 2;  // 获得的奖励信息
}

// 半周年预热活动
message ActivityHYWarmUpDayInfo {
  optional int32 day = 1;              // 是第几天
  optional bool isMakeUp = 2;          // 是否为补签
  optional bool isRewarded = 3;        // 是否领过分享奖励
  optional int64 checkInTimeStamp = 4; // 打卡时间戳
}

message ActivityHYWarmUpInfoNtf {
  optional int32 makeUpTimes = 1;                // 当前补签次数
  repeated ActivityHYWarmUpDayInfo dayInfo = 2;  // 已打卡天数的信息
  repeated int32 rewardedList = 3;               // 已领奖的打卡进度
  optional int32 activityId = 4;
}

message ActivityHYWarmUpCheckIn_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 day = 2;   // 第几天
  optional bool makeUp = 3; // 是否补签
}

message ActivityHYWarmUpCheckIn_S2C_Msg {
}

enum HYWarmupRewardType {
  daily = 1;     // 每日分享奖励
  progress = 2;  // 累计打卡进度奖励
  bigReward = 3; // 最终分享大奖
}

message ActivityHYWarmUpRecvReward_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 type = 2;  // 使用HYWarmupRewardType中的值
  optional int32 day = 3;
}

message ActivityHYWarmUpRecvReward_S2C_Msg {
  repeated RewardItemInfo reward = 1;
}

// 音乐订单活动
// 完成订单
message ActivityMusicOrderCompleteOrder_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 orderId = 2;
}

message ActivityMusicOrderCompleteOrder_S2C_Msg {

}
// 重置订单
message ActivityMusicOrderResetOrder_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 orderId = 2;
}

message ActivityMusicOrderResetOrder_S2C_Msg {

}
// 领取累计完成订单任务奖励
message ActivityMusicOrderGetTaskReward_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 taskId = 2;
}

message ActivityMusicOrderGetTaskReward_S2C_Msg {

}
// 获取订单活动数据
message ActivityMusicOrderGetOrderList_C2S_Msg {
  optional int32 activityId = 1;
}

// 订单中某个音符的持有和需要数量数据
message ActivityMusicOrderNoteData {
  optional int32 noteId = 1;
  optional int32 hasNum = 2;
  optional int32 requireNum = 3;
}

// 任务状态数据
message ActivityMusicOrderTaskData {
  optional int32 taskId = 1;
  optional int32 orderNum = 2;            // 需求完成订单数量
  repeated RewardItemInfo rewardList = 3; // 任务奖励
}

// 订单状态数据
message ActivityMusicOrderOrderData {
  optional int32 orderId = 1;
  optional xlsRes.MusicOrderStateType state = 2;
  repeated ActivityMusicOrderNoteData notes = 3;
}

message ActivityMusicOrderGetOrderList_S2C_Msg {
  repeated ActivityMusicOrderOrderData orders = 1;
  optional int32 remainResetOrderCount = 2; // 当日剩余订单重置次数
  repeated ActivityMusicOrderTaskData tasks = 3;
}
// 订单状态更新通知
message ActivityMusicOrderUpdateNtf {
  optional int32 activityId = 1;
  repeated ActivityMusicOrderOrderData orders = 2;
  optional int32 remainResetOrderCount = 3; // 当日剩余订单重置次数
  optional bool isFullData = 4; // 是否是全量订单数据下发，true：刷新所有订单数据；false：更新对应ID的订单数据
  repeated ActivityMusicOrderTaskData tasks = 5;
}

// 使用已有音符（消耗数量有服务器配置控制）交换目标音符（不能和消耗音符相同）
message ActivityMusicOrderNoteExchange_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 consumeNoteId = 2;
  optional int32 targetNoteId = 3;
  optional int32 targetNum = 4; // 要兑换的个数
}

message ActivityMusicOrderNoteExchange_S2C_Msg {

}

// 动物图鉴活动开始
// 具体图鉴信息
message AnimalHandbookItemInfo {
  optional int64 uniqueId = 1;  // 动物图鉴唯一id
  optional bool canGive = 2;    // 是否可赠送
}
// 动物信息
message AnimalHandbookAnimalInfo {
  optional int32 animalId = 1;
  repeated AnimalHandbookItemInfo animalItems = 2;
}
// 物种信息
message AnimalHandbookSpeciesInfo {
  optional int32 speciesId = 1;
  repeated AnimalHandbookAnimalInfo animals = 2;
  optional bool isRewarded = 3; // 是否已领图鉴奖励
}
// 图鉴赠送信息
message AnimalHandbookGiveInfo {
  optional proto_AnimalHandbookAnimalInfo animalInfo = 1; // 动物图鉴详细信息
  optional PlayerColdData playerInfo = 2; // 赠送人/获赠人信息
}
// 获取图鉴信息
message AnimalHandbookGetHandbookInfo_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
}
message AnimalHandbookGetHandbookInfo_S2C_Msg {
  repeated AnimalHandbookSpeciesInfo species = 1; // 根据物种组织的动物详细信息
  optional bool isPrizeRewarded = 2; // 是否已领终极奖励
}
// 聚集地信息
message AnimalHandbookColonyInfo {
  optional int32 colonyId = 1;                        // 聚集地id
  optional xlsRes.AnimalHandbookStateType state = 2;  // 聚集地状态
  optional int32 speciesId = 3;                       // 物种id (仅在state为AHST_Trapping或AHST_Captured时有效)
  optional int64 completeTimeMs = 4;                  // 诱捕完成时间 (仅在state为AHST_Trapping或AHST_Captured时有效)
  optional int32 animalId = 5;                        // 动物id (仅在state为AHST_Captured时有效)
  optional bool  isFreeGet = 6;                       // 是否可以免费领取
}
// 获取聚集地信息
message AnimalHandbookGetColonyState_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int32 colonyId = 2;    // 聚集地id
}
message AnimalHandbookGetColonyState_S2C_Msg {
  repeated AnimalHandbookColonyInfo colonies = 1; // 聚集地信息
}
message AnimalHandbookGetColonyStateNtf {
  optional int32 activityId = 1;                  // 活动id
  repeated AnimalHandbookColonyInfo colonies = 2; // 聚集地信息
  optional bool isFullData = 3;                   // true: 全量数据; false: 部分更新
}
// 开始诱捕
message AnimalHandbookStartTrapping_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int32 colonyId = 2;    // 聚集地id
}
message AnimalHandbookStartTrapping_S2C_Msg {
  optional AnimalHandbookColonyInfo colony = 1; // 聚集地信息
}
// 完成捕获
message AnimalHandbookCaptureAnimal_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int32 colonyId = 2;    // 聚集地id
}
message AnimalHandbookCaptureAnimal_S2C_Msg {
  optional int32 animalId = 1;                    // 动物id
  optional AnimalHandbookItemInfo animalItem = 2; // 获得的图鉴
}
// 生成赠送动物图鉴信息
message AnimalHandbookGenerateGiveAnimalInfo_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int64 uniqueId = 2;    // 动物图鉴唯一id
}
message AnimalHandbookGenerateGiveAnimalInfo_S2C_Msg {
  optional string giveId = 1;
}
message AnimalHandbookGetSpeciesCompleteReward_C2S_Msg{
  optional int32 activityId = 1;  // 活动id
  optional int32 speciesId = 2;   // 物种id
}
message AnimalHandbookGetSpeciesCompleteReward_S2C_Msg{
}
message AnimalHandbookGetUltimatePrize_C2S_Msg{
  optional int32 activityId = 1;  // 活动id
}
message AnimalHandbookGetUltimatePrize_S2C_Msg{
}
message AnimalHandbookGetGiveAndReceiveHistory_C2S_Msg{
  optional int32 activityId = 1;  // 活动id
}
message AnimalHandbookGetGiveAndReceiveHistory_S2C_Msg{
  repeated AnimalHandbookGiveInfo giveHistory = 1;
  repeated AnimalHandbookGiveInfo receiveHistory = 2;
}
message AnimalHandbookAnimalItemGetNtf{
  optional proto_AnimalHandbookAnimalInfo animalInfo = 1; // 动物图鉴详细信息
  optional PlayerColdData giverInfo = 2;                  // 赠送人信息（只有收取别人赠送的图鉴才有）
  optional int64 uid = 3;                                 // 图鉴获得人uid
}
message AnimalHandbookAnimalItemRemoveNtf{
  optional int64 uniqueId = 1; // 动物图鉴唯一id
  optional int32 activityId = 2;  // 活动id
}
// 动物图鉴活动结束

// 娱乐向导任务领奖
message GetEntertainmentGuideTaskReward_C2S_Msg {
  optional int32 taskId = 1;    // 任务id
}

message GetEntertainmentGuideTaskReward_S2C_Msg {
}

message EntertainmentGuideNtf {
}

// 向服务器拉取娱乐向导任务
message GetEntertainmentGuideTaskConfV2_C2S_Msg {
}
message GetEntertainmentGuideTaskConfV2_S2C_Msg {
  repeated EntertainmentGuideConfV2 conf = 1;
}

message EntertainmentGuideConfV2 {
  optional int32 SystemId = 1;
  repeated string guideTaskGroup = 2; //任务组
  optional int64 showBeginTime = 3;
  optional int64 showEndTime = 4;
  optional bool bRewardShow = 5;
  repeated EntertainmentGuideConf guideConf = 6;
  optional string stickerItemId = 7;
  optional int32  duration = 8;
}

message EntertainmentGuideConf {
  optional int32 id = 1;
  optional string name = 2; // 小铺名称
  repeated EntertainmentMatchTypeConf matchType = 3;
  optional string stickersName = 4; // 贴纸名称
  optional string stickerIcon = 5; // 贴纸图片
}

message EntertainmentGuideTimeInfo {
  optional int32 id = 1;
  optional string name = 2; // 小铺名称
  repeated EntertainmentMatchTypeConf matchType = 3;
  optional string stickersName = 4; // 贴纸名称
  optional string stickerIcon = 5; // 贴纸图片
}

message EntertainmentMatchTypeConf {
  optional int32 id = 1; // 玩法
  optional int32 unlockCondition = 2; // 打卡条件
  repeated int32 taskId = 3; // 玩法任务
  optional int32 sortIndex = 4; // 排序
  repeated string tipsText = 5;
  repeated string tipsIcon = 6;
  optional string labelText = 7;
  optional string labelBackground = 8;
  optional int32 jumpId = 9; // 跳转id
}

// 一键清除红点
message ClearAllRedDot_C2S_Msg {
  optional int32 type = 1; // 0:全量  1:筛选
}

message ClearAllRedDot_S2C_Msg {

}

// pixui 红点上报
message PixuiRedDotReport_C2S_Msg {
  optional int32 mod = 1; // 0:全量更新 1:新增 2:手动删除
  repeated int32 id = 2; //
}

message PixuiRedDotReport_S2C_Msg {

}

message PixuiRedDotInfo {
  optional int32 id = 1;
  optional bool isShow = 2;
}

// pixui 红点通知
message PixuiRedDotNtf {
  repeated PixuiRedDotInfo info = 1; // 下发pixui红点
}

// 版本前瞻活动订阅
message UpdateForesightActivitySubscribe_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 subscribeTaskId = 2; // 订阅任务id
}

message UpdateForesightActivitySubscribe_S2C_Msg {
}

message RecruitOrderRewardParam {
  optional int32 type = 1; // 类型
  optional int64 peerUid = 2; // 目标uid
  repeated int32 rewardIndex = 3; // 奖励索引
}

message ReturnActivitySetTaskReward_C2S_Msg {
  optional int32 taskId = 1;
  optional int32 itemId = 2;
  optional int32 itemNum = 3;
}

message ReturnActivitySetTaskReward_S2C_Msg {
}

//发送设置日历提醒奖励
message ReturnActivityMarkCalendar_C2S_Msg {
}

message ReturnActivityMarkCalendar_S2C_Msg {
}

//领取回归手册离线奖励
message ReturnActivityOfflineReward_C2S_Msg {
}

message ReturnActivityOfflineReward_S2C_Msg {  
}

message RecruitOrderRewardReceive_C2S_Msg {
  optional int32 actId = 1; // 活动id
  repeated RecruitOrderRewardParam param = 2;
  optional int32 awardType = 3; // 领奖类型: 0.主态领奖 1.客态领奖
}

message RecruitOrderRewardReceive_S2C_Msg {
}

message RecruitOrderCodeQuery_C2S_Msg {
  optional int32 actId = 1; // 活动id
  optional string idCode = 2; // 
}

message RecruitOrderCodeQuery_S2C_Msg {
  optional int64 uid = 1;
}

message RecruitOrderConfirm_C2S_Msg {
  optional int32 actId = 1; // 活动id
  optional string idCode = 2; //
  optional int64 uid = 3; 
}

message RecruitOrderConfirm_S2C_Msg {
  optional int64 uid = 1;
}

message RecruitOrderRaffleConfirm_C2S_Msg {
  optional int32 actId = 1; // 活动id
}

message RecruitOrderRaffleConfirm_S2C_Msg {
  repeated RecruitRaffleInfo raffleInfo = 1;
}

message RecruitRaffleInfo {
  optional int32 index = 1;
  optional int32 rewardId = 2;
  optional int64 drawnTs = 3;
  optional bool hasAddress = 4;
}

message RecruitOrderRaffleAddressConfirm_C2S_Msg {
  optional int32 actId = 1;
  optional int32 index = 2;
  optional int32 rewardId = 3;
  optional bool hasAddress = 4;
}

message RecruitOrderRaffleAddressConfirm_S2C_Msg {

}

message ActivityBuyMidasProduct_C2S_Msg {
  optional string param = 1;
  optional int32 activityId = 2;
  optional string productID = 3;
}

message ActivityBuyMidasProduct_S2C_Msg {
}

message ActivityBuyMidasProductNtf {
  optional string param = 1;
  optional int32 activityId = 2;
  optional string productID = 3;
}

message ActivityLotteryCfg_C2S_Msg {
  optional int32 actId = 1; // 活动ID
}
message ActivityLotteryCfg_S2C_Msg {
  optional int32 actId = 1; // 活动ID
  repeated com.tencent.wea.xlsRes.ActivityLotteryDrawRewardCfg rewards = 2; // 奖池
  repeated com.tencent.wea.xlsRes.ActivityLotteryDrawCostCfg costs = 3; // 消耗
}

// 星界奇遇获得盲盒通知
message ThemeAdventureRewardNtf {
  repeated proto_ThemeAdventureRewardUpgradeData rewardData = 1;
}

// 星界奇遇打开盲盒 # 废弃，走活动通用消息 ThemeAdventureOpenMysteryBoxReqInfo
message ThemeAdventureOpenMysteryBox_C2S_Msg {
  optional int32 activityId = 1;
  optional int64 mysteryBoxId = 2;
}

message ThemeAdventureOpenMysteryBox_S2C_Msg {

}

// 星界奇遇获取每日任务配置
message ThemeActivityGetDailyTaskReward_C2S_Msg {
  optional int32 activityId = 1;
}

message ThemeActivityGetDailyTaskReward_S2C_Msg {
  repeated com.tencent.wea.xlsRes.ThemeAdventureGameConf gameConfList = 1;
}

message ActBookOfFriendsExpense_C2S_Msg {
  optional int32 actId = 1; // 活动ID
  optional int64 peerUid = 2; // 助力对象
}

message ActBookOfFriendsExpense_S2C_Msg {

}

message ActBookOfFriendsExpenseNtf {
  optional int32 actId = 1; // 活动ID
  optional int64 peerUid = 2; // 助力对象
  optional int32 result = 3; // 助力结果 
  optional int32 type = 4; // 用户类型
  optional BookOfFriendsReportData report = 5; // 助力报告
}

// 预约活动
message AppointmentActivityMake_C2S_Msg {
  optional int32 activityId = 1;
}

message AppointmentActivityMake_S2C_Msg {
}

// 获取预约活动的详情信息
message AppointmentActivityGetInfo_C2S_Msg {
  optional int32 activityId = 1;
}

message AppointmentActivityGetInfo_S2C_Msg {
  optional bool appointmentMade = 1; // 是否已预约
  optional proto_ActivityDetail details = 2; // 详情
}

// 获取减负活动总览信息
message BurdenReduceTaskOverviewActivityGetInfo_C2S_Msg {
  optional int32 activityId = 1;
}

message BurdenReduceTaskGroup {
  optional int32 type = 1; // 任务所属的类型，参考BurdenReduceTaskResourceType的取值
  repeated int32 taskIdList = 2; // 减负的任务id列表
  optional int32 toDoCnt = 3; // 待完成计数
}

message TaskGroupOverviewInfo {
  repeated BurdenReduceTaskGroup taskGroupList = 1; // 减负任务组
  optional int32 totalFinishCnt = 2; // 累计完成计数
}

message BurdenReduceTaskOverviewActivityGetInfo_S2C_Msg {
  optional TaskGroupOverviewInfo overviewInfo = 1;
}

// 祈福牌活动-祈福牌信息
message ActivityPrayerCardInfo {
  optional int32 prayerCardId = 1;  	// 祈福牌id
  optional int64 unlockTime = 2;  	    // 解锁时间(取自配置表, 透传给客户端)
  optional int64 cardCount = 3;   	    // 祈福牌数量(⇐背包道具数量)
  optional bool isLooked = 4;  	        // 是否已经查看
}

// 祈福牌活动-赠送记录
message ActivityPrayerCardGiveRecord {
  optional int64 uid = 1;               // 赠送的玩家uid
  optional int32 prayerCardId = 2;  	// 赠送的祈福牌id
  optional int64 giveEpochSecs = 3;  	// 赠送时间戳(秒)
}

// 祈福牌活动-祈福牌奖励道具信息
message ActivityPrayerCardRewardItemInfo {
  optional int32 rewardItemId = 1;  	  // 奖励道具id
  optional int64 curTimeMs = 2;   	    // 当天日期
  optional int32 maxNum = 3;            // 当天上限
  optional int64 nextDayTimeMs = 4;     // 下一天日期
  optional int32 unlockNum = 5;  	      // 下一次解锁的数量
  optional int32 totalNum = 6;          // 获得的奖励道具总数量
}

// 祈福牌活动-信息-req
message ActivityPrayerCardInfo_C2S_Msg {
  optional int32 activityId = 1;        // 活动id
}

// 祈福牌活动-信息-rsp
message ActivityPrayerCardInfo_S2C_Msg {
  repeated ActivityPrayerCardInfo prayerCardInfo = 1;  	// 祈福牌信息
  repeated ActivityPrayerCardGiveRecord giveRecord = 2;	// 赠送记录
  repeated ActivityPrayerCardRewardItemInfo rewardItemInfo = 3;     // 奖励道具信息
}

// 祈福牌活动-赠送-req
message ActivityPrayerCardGive_C2S_Msg {
  optional int32 activityId = 1;        // 活动id
  optional int64 uid = 2;  	            // 赠送的玩家uid
  optional int64 itemUUID = 3;          // 道具UUID (⇌ 赠送的祈福牌id)
}

// 祈福牌活动-赠送-rsp
message ActivityPrayerCardGive_S2C_Msg {
  optional int32 prayerCardId = 2;  	// 赠送的祈福牌id
}

// 祈福牌活动-查看(领奖)-req
message ActivityPrayerCardLook_C2S_Msg {
  optional int32 activityId = 1;        // 活动id
  optional int32 prayerCardId = 2;  	// 查看的祈福牌id
}

// 祈福牌活动-查看(领奖)-rsp
message ActivityPrayerCardLook_S2C_Msg {
  optional int32 prayerCardId = 2;  	// 查看的祈福牌id
}

message PrayerCardRewardReachLimitNtf {
  optional int32 activityId = 1; // 活动id
  optional int64 nextDayTimeMs = 2;  // 下一天日期
  optional int32 nextDayNum = 3;  // 下一天可获得的奖励道具数量
  optional int32 totalNum = 4;  // 获得的奖励道具总数量
  optional int32 maxNum = 5;  // 总上限
}

message ActivityRecommendRecharge_C2S_Msg {
  optional int32 activityId = 1;
}

message ActivityRecommendRecharge_S2C_Msg {
  optional int32 activityId = 1;
  repeated com.tencent.wea.xlsRes.ActivityRechargeRecommendConfig configList = 2;
}

message ActivityWeekendIceBrokenInfo_C2S_Msg {
  optional int32 activityId = 1;
}

message ActivityWeekendIceBrokenInfo_S2C_Msg {
  optional int64 leftTimeSec = 1; // 剩余开奖倒计时
  optional string rewardKey = 2; // 开奖号码
  optional int32 rewardItemId = 3; // 中奖道具ID
  optional bool rewardNtf = 4; // 开奖-首次进入
  optional int32 rewardState = 5; // 0:未购买 1:未开奖 2:已中奖
  repeated string bigWardNames = 6; // 中大奖名单
  optional int32 activityId = 7;
  optional int32 rewardItemNum = 8; // 中奖道具数量
}

message GetQualifiedActivityMarqueeId_C2S_Msg {

}

message GetQualifiedActivityMarqueeId_S2C_Msg {
  repeated int32 activityMarqueeIds = 1;
}

// 幸运免单活动查询最终获奖名单
message LuckyRebateActivityGetResult_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 level = 2; // 奖励等级
  optional int32 startIndex = 3; // 起始index
  optional int32 cnt = 4; // 数量
  repeated com.tencent.wea.protocol.PlayerPublicInfoField fields = 5; // 需要的玩家信息属性字段集合
}

message LuckyRebateActivityGetResult_S2C_Msg {
  repeated com.tencent.wea.protocol.PlayerPublicInfo players = 1;
  optional int32 level = 2; // 奖励等级 回传便于客户端定位列表
  optional int32 startIndex = 3; // 起始index 回传便于客户端定位列表
  optional bool isEnd = 4; // 是否结束
}


//柯南活动新增
//获取抽卡结果
message KonanGetDrawResult_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int32 colonyId = 2;    // 聚集地id
}
message KonanGetDrawResult_S2C_Msg {
  optional AnimalHandbookColonyInfo colony = 1; // 聚集地信息
  optional int32 animalId = 2;                    // 动物id
  optional AnimalHandbookItemInfo animalItem = 3; // 获得的图鉴
}


//购物车查询
message WerewolfFullReduceActivityCart_C2S_Msg{
  //根据 uid 查询购物车
  optional int32 activityId = 1;    // 活动id
}
message WerewolfFullReduceActivityCart_S2C_Msg{

  repeated WerewolfFullReducedCartData werewolfFullReducedCartData = 1;

}

//更改购物车   （添加/修改/删除   统一重新插入数值）
message UpdateWerewolfFullReduceActivityCart_C2S_Msg{
  repeated WerewolfFullReducedCartData werewolfFullReducedCartData = 1;
  optional int32 activityId = 2;    // 活动id
}
message UpdateWerewolfFullReduceActivityCart_S2C_Msg{
  optional int32 result = 1;		// 请求结果
}

// 狼人满减活动  下单- req
message WerewolfFullReduceActivityCartOrder_C2S_Msg {

  repeated WerewolfFullReducedCartData werewolfFullReducedCartData= 1;
  optional int32 couponID = 2;
  optional int32 activityId = 3;    // 活动id
}

// 狼人满减活动  下单- rsp
message WerewolfFullReduceActivityCartOrder_S2C_Msg  {

  optional int32 result = 1;		// 请求结果
}

message WerewolfFullReducedCartData{
  optional int32  commodityId = 1;                 // 商品id
  optional int32  itemNums = 2;                    //商品数量
}


//请求引导-发现配置查询
message GuideConfigDiscoverStatusQuery_C2S_Msg{
}

//返回待引导配置(列表长度为0表示木有)
message GuideConfigDiscoverStatusQuery_S2C_Msg{
  repeated int32 configId = 1;//待处理的引导发现配置id
}

//请求标记引导发现
message GuideConfigDiscoverStatusUpdate_C2S_Msg{
  optional int32 signConfigId = 1;//标记发现配置id
}

//返回待引导配置(列表长度为0表示木有)
message GuideConfigDiscoverStatusUpdate_S2C_Msg{
  repeated int32 configId = 1;//待处理的引导发现配置id
}

// 匹配搭子答题
message FindPartnerAnswerQuestion_C2S_Msg {
  optional int32 questionId = 1;            // 问题id
  optional int32 choiceId = 2;              // 选项id
}
message FindPartnerAnswerQuestion_S2C_Msg {

}

// 获取题目列表
message FindPartnerGetQuestions_C2S_Msg {

}
message FindPartnerGetQuestions_S2C_Msg {
  repeated proto_FindPartnerQuestionAnswer questionList = 1;  // 题目和回答列表
}

// 小队任务额外信息
message SquadTaskExtraInfo_C2S_Msg {
  optional int32 activityId = 1;
  repeated int32 taskId = 2; // 任务id
}

// 每个条件的完成信息
message SquadTaskCompleteInfo {
  optional int32 id = 1;
  repeated int64 valueList = 2; // 已完成的小队成员uid
}

message SquadTaskExtraInfos {
  optional int32 taskId = 1; // 任务id
  repeated SquadTaskCompleteInfo squadTaskExtraInfo = 2;    // 每个条件的完成信息
}

message SquadTaskExtraInfo_S2C_Msg {
  repeated SquadTaskExtraInfos squadTaskExtraInfo = 1;    // 每个条件的完成信息
}

// 膨胀爆红包-退出多人活动小队
message InflateRedPacketQuit_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int64 squadId = 2;     // 小队id
}

message InflateRedPacketQuit_S2C_Msg {
  optional proto_ActivitySquadDetail squadDetail = 1; // 退出后自己创建的单人小队
}

// 膨胀爆红包-踢出多人活动小队
message InflateRedPacketKickOut_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  optional int64 squadId = 2;     // 小队id
  optional int64 uid = 3;         // 队员uid
}

message InflateRedPacketKickOut_S2C_Msg {
  optional proto_ActivitySquadDetail squadDetail = 1; // 返回最新的小队数据
}

// 膨胀爆红包-膨胀
message InflateRedPacketInflate_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  repeated int32 money = 2;       // 红包金额
}

message InflateRedPacketInflate_S2C_Msg {
}

// 膨胀爆红包-领取
message InflateRedPacketReceive_C2S_Msg {
  optional int32 activityId = 1;  // 活动id
  repeated int32 money = 2;       // 红包金额
}

message InflateRedPacketReceive_S2C_Msg {
}

// 膨胀爆红包-如果客户端当前打开了活动页面则重新请求小队数据
message InflateRedPacketUpdateNtf {
  optional proto_AttrInflateRedPacketData inflateRedPacket = 1;
}

// Moba周年庆领抽奖
message AnniversaryMobaDrawReward_C2S_Msg {
  optional int32 activityId = 1;
}

message AnniversaryMobaDrawReward_S2C_Msg {
  optional int32 activityId = 1;
  optional int32 rewardIndex = 2;
}

// Moba周年庆领虞姬
message AnniversaryMobaGetReward_C2S_Msg {
  optional int32 activityId = 1;
  optional string rewardKey = 2;
}

message AnniversaryMobaGetReward_S2C_Msg {

}

// Moba周年庆领虞姬被领取次数
message AnniversaryMobaGetRewardTimes_C2S_Msg {
  optional int32 activityId = 1;
}

message AnniversaryMobaGetRewardTimes_S2C_Msg {
  optional int32 activityId = 1;
  optional int32 rewardTimes = 2;
}

// moba组队抽红包抽取
message MobaSquadDrawRedPacketDraw_C2S_Msg {
  optional int32 activityId = 1;
}

message MobaSquadDrawRedPacketDraw_S2C_Msg {
}

//组队抽红包刷新
message MobaSquadDrawRedPacketRefreshNtf {
  optional proto_ActivityDetail details = 1;
}

message SummonFortuneSlip_C2S_Msg {
  optional int32 activityId = 1;
}

message SummonFortuneSlip_S2C_Msg {
  optional int32 activityId = 1;
  optional ItemInfo slip = 2;
  optional ItemArray rewardInfo = 3;
}

message ReceiveCollectionReward_C2S_Msg {
  optional int32 activityId = 1;
}

message ReceiveCollectionReward_S2C_Msg {
  optional int32 activityId = 1;
  optional ItemArray rewardInfo = 2;
}

// 获取福星好友列表
message GetLuckyStarFriendList_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message GetLuckyStarFriendList_S2C_Msg {
  repeated int64 uid = 1;           // 玩家id (客户端二次向服务器查询头像/昵称等信息)
}

// 查询助力记录（非定向）
message GetLuckyStarAssistRecordList_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message GetLuckyStarAssistRecordList_S2C_Msg {
  repeated LuckyStarAssistRecord record = 1;
}

message LuckyStarAssistRecord {
  optional int64 assistUid = 1;      // 助理人Id
  optional int64 assistTime = 2;     // 助力时间
  optional int32 assistType = 3;     // 助力类型 0:普通助力1：福星助力
  optional int64 itemId = 4;         // 助力奖品
  optional int64 itemNum = 5;        // 助力奖品数量
}

// 邀请普通好友助力（非定向）
message LuckyStarRequestNormalAssist_C2S_Msg {
  optional int32 activityId = 1; // 活动id
}

message LuckyStarRequestNormalAssist_S2C_Msg {
  optional string uniqueId = 1;       // 非定向助力唯一id
}

// 普通好友助力（非定向）
message LuckyStarNormalAssist_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional string uniqueId = 2;   // 非定向助力唯一id
}

message LuckyStarNormalAssist_S2C_Msg {
}

// 邀请福星好友助力（定向）
message LuckyStarRequestSpecifyAssist_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int64 requestAssistUid = 2;   // 定向助力唯一请求Id
}

message LuckyStarRequestSpecifyAssist_S2C_Msg {
  repeated int64 uid = 1;
  optional string uniqueId = 2;   // 定向助力唯一id
}

// 福星好友助力（定向）
message LuckyStarSpecifyAssist_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional string uniqueId = 2;   // 定向助力唯一id
}

message LuckyStarSpecifyAssist_S2C_Msg {
}

// 福星赠送福签（非定向）
message LuckyStarGiveSlip_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 slipId = 2; // 福签Id
}

message LuckyStarGiveSlip_S2C_Msg {
  optional string uniqueId = 1;
}

// 福星索要福签（非定向）
message LuckyStarRequestSlip_C2S_Msg {
  optional int32 activityId = 1; // 活动id
  optional int32 slipId = 2; // 福签Id
}

message LuckyStarRequestSlip_S2C_Msg {
  optional string uniqueId = 1;
}

message QueryLuckStarCount_C2S_Msg {
  optional int32 activityId = 1;
}

message QueryLuckStarCount_S2C_Msg {
  optional int32 activityId = 1;
  optional int64 assistCount = 2;
  optional int64 beAssistedCount = 3;
  optional int64 giveCount = 4;
  optional int64 requestCount = 5;
}

// 拼图手动揭开
message PuzzleUncover_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 puzzleId = 2;
  repeated int32 indices = 3;
}

message PuzzleUncover_S2C_Msg {
  optional int32 puzzleId = 1;
  repeated int32 indices = 2;
}

// 拼图全部揭开
message PuzzleUncoverAll_C2S_Msg {
  optional int32 activityId = 1;
  optional int32 puzzleId = 2;
}

message PuzzleUncoverAll_S2C_Msg {
  optional int32 puzzleId = 1;
  repeated int32 indices = 2;
}

// 特色玩法活动页签排序
message ActivityBIOrderParam {
  optional string openid = 1;
  optional string roleid = 2;
  optional int32 req_ts = 3;
  optional string entry_type = 4;
  repeated ActivityBIOrderTag tags = 5;
}

message ActivityBIOrderResult {
  optional string recid = 1;
  repeated ActivityBIOrderTag tags = 2;
  optional bool ignored = 3;
  repeated string exp_tag = 4;
}


message ActivityBIOrderTag {
  optional string id = 1;
  repeated ActivityBIOrderItem items = 2;
}

message ActivityBIOrderItem {
  optional string id = 1;
  optional string type = 2;
  optional int32 published_at = 3;
}

// 农场天天领活动 - 领取打卡奖励
message GetFarmDailyReward_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 checkInIndex = 2;  // 打卡天数(第几个)
}

message GetFarmDailyReward_S2C_Msg {
  optional int32 checkInIndex = 1;  // 打卡天数(第几个)
  optional proto_UpgradeCheckInInfo checkInInfo = 2; // 打卡信息
}

// 升级农场天天领活动 - 解锁高级
message UpgradeFarmDailyReward_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 buyNums = 2;     //解锁几周即(购买几张农场天天领权益卡)
  optional int32  commodityId = 3;    // 商品id
}

message UpgradeFarmDailyReward_S2C_Msg {

}

//在活动页面解锁高级
message UpgradeInFarmDailyActivityPage_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message UpgradeInFarmDailyActivityPage_S2C_Msg {
}

// 一元抽奖活动 - 领取打卡奖励
message GetWeekendGiftReward_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
  optional int32 checkInIndex = 2;  // 打卡天数(第几个)
}

message GetWeekendGiftReward_S2C_Msg {
  optional int32 checkInIndex = 1;  // 打卡天数(第几个)
}

// 开启活动周末礼包 - 解锁高级
message UpgradeWeekendGift_C2S_Msg {
  optional int32 activity_id = 1; // 活动id
}

message UpgradeWeekendGift_S2C_Msg {

}

//购买周末礼包成功
message WeekendGiftBuySuccessNtf {
  optional int32 activityId = 1;
}

//领取周末礼包成功
message WeekendGiftCheckInNtf {
  optional int32 activityId = 1;
}