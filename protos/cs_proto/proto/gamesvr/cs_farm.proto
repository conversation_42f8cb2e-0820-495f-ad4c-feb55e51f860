syntax = "proto2";

option cc_generic_services = false;

package com.tencent.wea.protocol;

import "base_common.proto";
import "common.proto";
import "attr_FarmAttr.proto";
import "attr_FarmItem.proto";
import "ResKeywords.proto";
import "attr_FarmWelcomeInfo.proto";
import "attr_FarmFishingRecord.proto";
import "attr_PetWearClothing.proto";
import "attr_FarmModuleOpenTime.proto";
import "attr_CosImage.proto";

// 创建
message FarmCreate_C2S_Msg {
  optional ActivityCookie actCookie = 1; // 为一些活动透传参数逻辑，目前有个农场助力活动，从端外发起
}

message FarmCreate_S2C_Msg {
}

// 进入
message FarmEnter_C2S_Msg {
  optional int64 uid = 1; // 谁的Farm
  optional int32 localDsId = 2;
  optional string cliVersion = 3; // 客户端版本号
  optional bool forceCreateDS = 4; // 当没有DS的时候，是否强制创建DS，即使一个人也创建DS（只在测试环境生效）
  optional bool autoReconnect = 5; // 是否自动重连
  optional int32 source = 6; // 进入来源（客户端入口）
  optional bool forceEnter = 7; // 强制进入小窝，忽略兼容性检查，并在ds创建失败后使用默认ds
  optional string rcmdInfo = 8; // 社交推荐参数信息
  optional string abTestInfo = 9; // 社交实验参数信息
  optional string tabName = 10; // 社交Tab名称
  optional bool cropConnectionUpdate = 11; // 养殖物联通性更新
}

message FarmEnter_S2C_Msg {
  optional DSCreateState dsState = 1; // 本次进入是否创建了新DS
  optional FarmFunctionOpenTime farmFunctionOpenTime = 2; // (废弃)
}

// 老版本用 这里以后不再新增开启时间字段 20240806移到FarmFetchFunctionOpenTime协议
message FarmFunctionOpenTime {
  optional int64 animalOpenTime = 1;
  optional int64 monthlyPassOpenTime = 2;
  optional int64 uavOpenTime = 3;
  optional int64 fertilizeOpenTime = 4;
  optional int64 fishOpenTime = 5;
  optional int64 fishCardOpenTime = 6;
  optional int64 buildingSkinOpenTime = 7;
  optional int64 giftOpenTime = 8;
  optional int64 houseOpenTime = 9;
  optional int64 aquariumOpenTime = 10;
}

// 离开
message FarmExit_C2S_Msg {
  optional int32 clientReason = 1; // 客户端退小窝原因
}

message FarmExit_S2C_Msg {
}

message FarmKickNtf {
  optional uint64 farmId = 1;
  optional FarmKickReason reason = 2;
}

message FarmDSInfoNtf {
  optional int32 groupID = 1;
  optional string dsAddr = 2;
  optional string desModInfo = 3;// ds关卡序列
  optional string dsAuthToken = 5;   // dsToken认证
  optional string aliasId = 6;
  optional string dsaServiceName = 7;
  optional int64 dsaServiceId = 9;
  optional string fleetId = 10;
  optional string gameSessID = 11;
}

message FarmAttrNtf {
  optional int64 farmId = 1;
  optional proto_FarmAttr farmAttr = 2;
  optional bool isFull = 3;
}

message FarmOwnerAttrNtf {
  optional int64 farmId = 1;
  optional proto_FarmAttr farmAttr = 2;
  optional bool isFull = 3;
}

message FarmTest_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int64 uid = 1;
}

message FarmTest_S2C_Msg {
  optional int64 uid = 1;
}

// 注意：operator的类名要跟枚举名相同
// 注意：operator的类名要跟枚举名相同
// 注意：operator的类名要跟枚举名相同
enum FarmOpType {
  FOT_UNKNOWN = 0;
  FOT_Plant = 1; // 种植（作物、动物）
  FOT_Care = 2; // 照料（作物浇水、动物喂食）
  FOT_Harvest = 3; // 收获（作物、动物）
  FOT_Destroy = 4; // 销毁（作物铲除、动物收起）
  FOT_Unlock = 5; // 解锁地格
  FOT_LevelUp = 6; // 升级地格
  FOT_Test = 7;
  FOT_Steal = 8; // 偷取
  FOT_Fertilize = 9; // 祈愿
  FOT_Encourage = 10; // 动物助产
  FOT_PlantFish = 11; // 养鱼
  FOT_DestroyFish = 12; // 取消养鱼
  FOT_FishingStart = 13; // 钓鱼开始（抛竿）
  FOT_FishingEnd = 14; // 钓鱼结束（结算）
  FOT_StealFish = 15; // 偷鱼
  FOT_CancelFishProtect = 16; // 取消炸鱼保护
  FOT_FertilizeAquarium = 17; // 给鱼缸祈愿
  FOT_PetFertilize = 18; // 宠物祈愿
  FOT_LevelUpExp = 19; // 升级地格星级（经验地）
  FOT_CancelCropRainProtect = 20; // 取消作物保护
  FOT_CancelVileProtect = 21; // 取消霸王花保护
  FOT_CancelLightningProtect = 22; // 取消打雷保护
}
// 注意：operator的类名要跟枚举名相同
// 注意：operator的类名要跟枚举名相同
// 注意：operator的类名要跟枚举名相同

message PlantOpData {
  repeated int32 gridIds = 1; // 地块id
  optional int32 cropType = 2; // 作物类型
  optional bool autoAll = 3; // 自动操作全部同类地格
}

message PlantResult {
  map<int32, int64> costItems = 1;
}

message PlantOpRsp {
  map<int32, PlantResult> results = 1; // 地块id -> 种植结果
}

message CareOpData {
  repeated int32 gridIds = 1;
  optional int32 expectedAddCareValue = 2; // 废弃
  optional bool autoAll = 3; // 自动操作全部同类地格
  optional bool autoAllWaitCropLevelUp = 4; // 自动操作全部同类地格的时候要等待熟练度升级
}

message CareResult {
  optional int32 shortenTime = 1;
  map<int32, int64> costItems = 2;
  optional int32 cropType = 3;
}

message CareOpRsp {
  map<int32, CareResult> results = 1; // 地块id -> 照料结果
  optional bool itemNotEnough = 2; // 批量操作时是否有农场币不足
}

message HarvestOpData {
  repeated int32 gridIds = 1;
  optional bool autoAll = 3; // 自动操作全部同类地格
  optional bool autoPlantVile = 4; // 自动种植霸王花
}

message HarvestResult {
  map<int32, int64> items = 1;
  optional int32 addFarmExp = 2;
  optional com.tencent.wea.xlsRes.FarmCropRipeQuality ripeQuality = 3;
  optional int32 cropType = 4;
  optional int32 kirinMana = 5;
}

message HarvestOpRsp {
  map<int32, HarvestResult> results = 1; // 地块id -> 收获结果
  optional bool waitVileEat = 2; // 是否需要等待霸王花吞食
}

message DestroyOpData {
  repeated int32 gridIds = 1;
  optional bool autoBatch = 2; // 自动操作相连地格（作物动物）
  optional int32 autoReplaceAnimalType = 3; // 自动收一些动物
}

message DestroyResult {
  map<int32, int64> items = 1;
  optional int32 cropType = 2;
}

message DestroyOpRsp {
  map<int32, DestroyResult> results = 1; // 地块id -> 销毁结果
  optional bool skipRipe = 2; // 有跳过已成熟作物
}

message UnlockOpData {
  optional int32 gridId = 1;
}


message LevelUpOpData {
  optional int32 gridId = 1;
}

message LevelUpExpOpData {
  optional int32 gridId = 1;
}

message TestOpData {
  optional int32 gridId = 1;
}

message StealOpData {
  optional int32 gridId = 1;
  optional bool needCheckPlatFriend = 2;
  optional string thiefOpenId = 3;
}

message StealOpRsp {
  map<int32, int64> items = 1;
  optional int32 cropType = 2;
  optional xlsRes.FarmCropRipeQuality quality = 3;
  optional bool npcFarmStolenFinished = 4;
}

message FertilizeBuffData {
  optional double cropSilverBumperRateIncP = 1;
  optional double cropGoldBumperRateIncP = 2;
  optional double animalSilverBumperRateIncP = 3;
  optional double animalGoldBumperRateIncP = 4;
}

message FertilizeOpData {
  optional int32 gridId = 1;
  optional bool needCheckPlatFriend = 2;
  optional int32 relationType = 3; // 关系
  optional FertilizeBuffData operatorBuff = 4;
}

message FertilizeOpRsp {
  map<int32, com.tencent.wea.xlsRes.FarmCropRipeQuality> items = 1;
  optional int32 traceId = 2; // 宠物追踪id
}

message PetFertilizeOpData {
  optional int32 gridId = 1; // 地块id
  optional int32 traceId = 2; // 追踪id
}

message PetFertilizeOpRsp {
  map<int32, com.tencent.wea.xlsRes.FarmCropRipeQuality> items = 1;
}

message EncourageOpData {
  repeated int32 gridIds = 1;
  optional bool autoAll = 2; // 自动操作全部同类地格
  optional bool autoAllWaitCropLevelUp = 3; // 自动操作全部同类地格的时候要等待熟练度升级
}

message EncourageOpRsp {
  map<int32, EncourageResult> results = 1; // 地块id -> 鼓励结果
}

message EncourageResult {
  optional int32 shortenTime = 1; // 鼓励缩短时间
  optional int32 cropType = 2;
}

message PlantFishOpData {
  optional int32 period = 1;
  optional int32 bait = 2;
  optional int32 layer = 3;
}

message PlantFishOpRsp {
  map<int32, int64> costItems = 1;
}

message DestroyFishOpData {
}

message DestroyFishOpRsp {
  map<int32, int64> returnItems = 1;
}

message FishingStartOpData {
  optional bool noFishCardPack = 2; // 前10次不给鱼卡
}

message FishingStartOpRsp {
  optional proto_FarmFishingRecord fishingRecord = 1;
}

message FishingEndOpData {
  optional int32 idx = 1;
  optional bool success = 2;
}

message FishingEndOpRsp {
  optional proto_FarmFishingRecord fishingRecord = 1;
  optional FishCardUnit fishCard = 2;
  optional int32 exp = 3;
  optional string billNo = 4;
}

message CancelFishProtectOpData {
}

message StealFishOpData {
  optional bool needCheckPlatFriend = 1; // 服务器透传用字段 客户端不填
  map<int32, int32> fishLevel = 2; // 服务器透传用字段 客户端不填
  map<int32, int64> fishMaxScore = 3; // 服务器透传用字段 客户端不填
  map<int32, double> buffValue = 4; // 服务器透传用字段 客户端不填
  optional int32 farmLevel = 5;
  optional bool noFishCardPack = 6; // 前10次不给鱼卡
}

message StealFishOpRsp {
  optional proto_FarmFishingRecord fishingRecord = 1;
  optional bool success = 2;
  optional string billNo = 3;
}

// 请求数据
message FertilizeAquariumOpData {
  optional bool needCheckPlatFriend = 2;
  optional int32 relationType = 3; // 关系
  optional FertilizeBuffData operatorBuff = 4;
}

message FertilizeAquariumOpRsp {
  map<int32, IntIntMap> result = 1; // 祈愿结果 key是scale 和 idx
}

message CancelCropRainProtectOpData {
  optional int32 gridId = 1; // 指定取消某个地格的雨天保护效果
  optional bool cancelAll = 2; // 是否取消全部地格以及本次下雨的雨天保护效果
}

message CancelVileProtectOpData {
  optional int32 gridId = 1; // 取消某个霸王花的成熟保护
}

message CancelLightningProtectOpData {
}

message OpData {
  optional PlantOpData plantOpData = 1; // 种植相关的参数
  optional CareOpData careOpData = 2; // 照料相关的参数
  optional HarvestOpData harvestOpData = 3; // 收获相关的参数
  optional DestroyOpData destroyOpData = 4; // 销毁相关的参数
  optional UnlockOpData unlockOpData = 5; // 解锁参数
  optional LevelUpOpData levelUpOpData = 6; // 土地升级
  optional TestOpData testOpData = 7;
  optional StealOpData stealOpData = 8;  // 偷菜参数
  optional FertilizeOpData fertilizeOpData = 9; // 施肥参数
  optional EncourageOpData encourageOpData = 10; // 鼓励参数
  optional PlantFishOpData plantFishOpData = 11; // 养鱼参数
  optional DestroyFishOpData destroyFishOpData = 12; // 取消养鱼参数
  optional FishingStartOpData fishingStartOpData = 13; // 开始钓鱼参数
  optional FishingEndOpData fishingEndOpData = 14; // 结算钓鱼参数
  optional StealFishOpData stealFishOpData = 15; // 偷鱼参数
  optional CancelFishProtectOpData cancelFishProtectOpData = 16; // 取消偷鱼保护
  optional FertilizeAquariumOpData fertilizeAquariumOpData = 17;
  optional PetFertilizeOpData petFertilizeOpData = 18; // 宠物祈愿参数
  optional LevelUpExpOpData levelUpExpOpData = 19; // 土地经验升级
  optional CancelCropRainProtectOpData cancelCropRainProtectOpData = 20; // 取消作物雨天保护参数
  optional CancelVileProtectOpData cancelVileProtectOpData = 21; // 取消霸王花保护参数
  optional CancelLightningProtectOpData cancelLightningProtectOpData = 22; // 取消打雷保护参数
}
message UnlockOpRsp {
}
message LevelUpOpRsp {
}
message LevelUpExpOpRsp {
}
message CancelCropRainProtectOpRsp {
}
message CancelVileProtectOpRsp {
}
message CancelLightningProtectOpRsp {
}
message TestOpRsp {
  optional string result = 1;
}
// 返回的数据
message OpRsp {
  optional PlantOpRsp plantOpRsp = 1;
  optional CareOpRsp careOpRsp = 2;
  optional HarvestOpRsp harvestOpRsp = 3;
  optional DestroyOpRsp destroyOpRsp = 4;
  optional UnlockOpRsp unlockOpRsp = 5;
  optional LevelUpOpRsp levelUpOpRsp = 6;
  optional TestOpRsp testOpRsp = 7;
  optional StealOpRsp stealOpRsp = 8;
  optional FertilizeOpRsp fertilizeOpRsp = 9;
  optional EncourageOpRsp encourageOpRsp = 10;
  optional PlantFishOpRsp plantFishOpRsp = 11;
  optional DestroyFishOpRsp destroyFishOpRsp = 12; // 取消养鱼
  optional FishingStartOpRsp fishingStartOpRsp = 13; // 开始钓鱼
  optional FishingEndOpRsp fishingEndOpRsp = 14; // 结算钓鱼
  optional StealFishOpRsp stealFishOpRsp = 15; // 偷鱼
  optional FertilizeAquariumOpRsp fertilizeAquariumOpRsp = 17;
  optional PetFertilizeOpRsp petFertilizeOpRsp = 18; // 宠物祈愿
  optional LevelUpExpOpRsp levelUpExpOpRsp = 19;
  optional CancelCropRainProtectOpRsp cancelCropRainProtectOpRsp = 20; // 取消作物雨天保护
  optional CancelVileProtectOpRsp cancelVileProtectOpRsp = 21; // 取消霸王花保护
  optional CancelLightningProtectOpRsp cancelLightningProtectOpRsp = 22; // 取消打雷保护
}

message FarmOp_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int64 uid = 1;
  optional FarmOpType opType = 2;
  optional OpData opData = 3;
  optional int32 opSource = 4; // 操作来源
  optional string pos = 5;// 操作时的坐标
}

// 农场操作来源
enum FarmOpSource {
  FOS_PLAYER = 0; // 玩家
  FOS_UAV = 1; // 无人机
  FOS_PET = 2; // 宠物
  FOS_WEATHER = 3; // 天气
  FOS_NPC = 4; // NPC 水神
  FOS_VILE = 5; // 霸王花
  FOS_SYSTEM = 6; // 系统
  FOS_VEHICLE = 7; // 载具
  FOS_EXTERNAL = 8; // 外部工具
  FOS_REDFOX = 9; // 小红狐
  FOS_MAGIC = 10; // 仙术
}

message FarmOp_S2C_Msg {
  optional OpRsp opRsp = 1;
}

message FarmBuildingLevelUp_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 confId = 1;
}

message FarmBuildingLevelUp_S2C_Msg {

}

message FarmBuildingSell_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 confId = 1;
  map<int32, int32> sellItems = 2;
}

message FarmBuildingSell_S2C_Msg {

}

message FarmBuildingBatchSellItems {
  map<int32, int32> sellItems = 1;
}

message FarmBuildingBatchSell_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  map<int32, FarmBuildingBatchSellItems> data = 1;
}

message FarmBuildingBatchSell_S2C_Msg {

}

message FarmBuildingFirstFlag_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 confId = 1;
}

message FarmBuildingFirstFlag_S2C_Msg {

}

message FarmBuildingSetSkin_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 confId = 1;
  optional int32 skinId = 2;
  optional int32 fixSkinId = 3; // 服务器字段
  optional int32 skinForm = 4; // 形态
  optional int64 expireMs = 5; // 超时
}

message FarmBuildingSetSkin_S2C_Msg {

}

message FarmForwardToDS_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  optional int32 type = 1;
  optional string detail = 2;
}

message FarmForwardToDS_S2C_Msg {
  optional int32 result = 1;
  optional int64 motionStartTime = 2;
}

enum FarmRecordListType {
  FRLT_STEAL = 0; // 偷菜
  FRLT_FERTILIZE = 1; // 施肥
  FRLT_ALL = 2; // 全部
  FRLT_PET_EVICT = 3; // 宠物驱逐
  FRLT_PET_FEED = 4; // 宠物喂食
  FRLT_CLOUDTAX = 5; // 云游商人税收
}

message FarmBadGuysList_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional bool isHitList = 1;  // 是否点击记录列表，如是服务器需要记录点击时间
  optional int32 listType = 2;  // 参考FarmRecordListType
}

message FarmBadGuyInfo {
  optional int64 uid = 1;
  optional int64 updateTime = 2; // 最后一次偷取时间
  optional int64 detailGotTime = 3; //上次拉取详情时间
}

message FarmDoGooderInfo {
  optional int64 uid = 1;
  optional int64 updateTime = 2; // 最后一次施肥时间
  optional int64 detailGotTime = 3; //上次拉取详情时间
  optional int32 withPetId = 4; // 追随的宠物的id
}

message FarmPetEvictPlayerInfo {
  optional int64 uid = 1;
  optional int64 updateTime = 2; // 最后一次被宠物驱逐时间
  optional int64 detailGotTime = 3; // 上次拉取详情时间
  optional int32 petId = 4; // 宠物id
}

message FarmPetFeedPlayerInfo {
  optional int64 uid = 1;
  optional int64 updateTime = 2; // 最后一次被宠物驱逐时间
  optional int64 detailGotTime = 3; // 上次拉取详情时间
  optional int32 petId = 4; // 宠物喂食
}

message FarmCloudTaxPlayerInfo {
  optional int32 playerCount = 1; // 多少玩家来出售
  optional int64 priceTotal = 2; // 总售价
  optional int64 taxLeft = 3; // 税收还没领
  optional int64 updateTime = 4; // 时间
  optional int64 detailGotTime = 5; // 上次拉取详情时间
}

message FarmBadGuysList_S2C_Msg {
  repeated FarmBadGuyInfo badGuys = 2;
  repeated FarmDoGooderInfo doGooders = 3;
  repeated FarmPetEvictPlayerInfo petEvictPlayers = 4;
  repeated FarmPetFeedPlayerInfo petFeedPlayers = 5;
  repeated FarmCloudTaxPlayerInfo cloudTax = 6;
}

message FarmBadGuyStealingDetail_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int64 uid = 1;
}

message FarmBadGuyStealingDetail_S2C_Msg {
  repeated FarmStealingCropRecord stealingDetails = 1;
}

message FarmDoGooderDetail_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int64 uid = 1;
}

message FarmDoGooderDetail_S2C_Msg {
  repeated FarmFertilizeRecord fertilizeDetails = 1;
}

message FarmStealingMyRecord_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmStealingMyRecord_S2C_Msg {
  repeated FarmStealingMyRecord stealingMyRecords = 1;
}

message FarmPetEvictDetail_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int64 uid = 1;
}

message FarmPetEvictDetail_S2C_Msg {
  repeated FarmPetEvictRecord petEvictDetails = 1;
}

message FarmPetFeedDetail_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int64 uid = 1;
}

message FarmPetFeedDetail_S2C_Msg {
  repeated FarmPetFeedRecord petFeedDetails = 1;
}

message FarmPetCatFishing_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmPetCatFishing_S2C_Msg {
  repeated proto_FarmFishingRecord fishingRecord = 1; // 钓到的鱼
}

message FarmStrangerList_C2S_Msg {
}

message FarmStrangerList_S2C_Msg {
  repeated FarmStrangerItem strangers = 1;
}

message FarmEvictBadGuy_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int64 uid = 1;  // 被踢人的uid
}

message FarmEvictBadGuy_S2C_Msg {
}

//获得农场道具NTF
message FarmGetItemsNtf {
  repeated proto_FarmItem items = 1;
  optional int32 reason = 2;  // 道具获取的原因 枚举ItemChangeReason
}

message FarmCropMenuSelect_C2S_Msg {
  optional int32 cropType = 1;
}

message FarmCropMenuSelect_S2C_Msg {
}

message FarmSetClientKV_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional string K = 1;
  optional string V = 2; // 如果V是空的，那就是删除key
}

message FarmSetClientKV_S2C_Msg {
}

message FarmSetWelcomeInfo_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional proto_FarmWelcomeInfo farmWelcomeInfo = 1;
}

message FarmSetWelcomeInfo_S2C_Msg {

}

// 发送一条留言或者回复
message FarmSendLiuYanMessage_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional string content = 1;
  optional uint64 liuYanMessageId = 2;
  optional MemberBaseInfo senderInfo = 3; // 发送者个人信息
  optional int32 friendStatus = 4; // for svr 好友状态(0:未设置，1:是游戏好友，10:是平台好友，11:游戏好友和平台好友)
}

message FarmSendLiuYanMessage_S2C_Msg {
  optional FarmLiuYanMessageOrReplySendResult sendResult = 1;
}

message FarmLiuYanMessageOrReplySendResult {
  optional int64 id = 1;
  optional string content = 2;
  optional int64 sendTime = 3;
  optional int64 uid = 4;// 玩家id
  optional string nickname = 5;     //昵称
  optional string profile = 6;       //头像url (maybe)
  optional HeadFrame headFrame = 7; // 头像框
}

// 删除一条留言或者回复
message FarmDeleteLiuYanMessage_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional uint64 id = 1;
  optional bool isChosen = 2;
  optional int64 farmId = 3;
}

message FarmDeleteLiuYanMessage_S2C_Msg {

}

// 拉取当前小窝的留言
message FarmGetLiuYanMessage_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int64 farmId = 1;
  optional int32 currentNumber = 2;
  optional int32 number = 3;
}

message FarmGetLiuYanMessage_S2C_Msg {
  repeated XiaoWoLiuYanMessage liuYanMessages = 1;
  optional int64 lastPullId = 2;
}

message FarmReceiveNewLiuYanMessageNtf {
  optional int32 number = 1;
}

message FarmGetFarmDynamicInfo_C2S_Msg {
  repeated uint64 uids = 1;
}

message FarmGetFarmDynamicInfo_S2C_Msg {
  map<uint64, FarmDynamicInfo> farmDynamicInfos = 1;  // 玩家uid -> 农场动态信息
}

// 农场个性签名
message FarmChangeSignature_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional string farmSignature = 1; // 农场个性签名
  optional int32 expiryDays = 2;     // 0代表永久, 其余代表天数.  当前值域[0,7]
}

message FarmChangeSignature_S2C_Msg {
}

// Subscribe monthly membership/access/pass 订阅月卡
message FarmSubMonPass_C2S_Msg {
  optional ForwardToFarmCtx ctx = 1; // 客户端不用赋值
  optional int32 count = 2; // 购买几份
}

message FarmSubMonPass_S2C_Msg {

}

enum FarmTagType {
  FTT_PIN = 0; // 置顶
  FTT_CANCELPIN = 1; // 取消置顶
  FTT_BLOCK = 2; // 拉黑
  FTT_CANCELBLOCK = 3; // 取消拉黑
  FTT_IGNORE_STEAL_REMIND_ONCE = 4; // 取消本次偷菜提醒
  FTT_BLOCK_FERTILIZE = 5; // 屏蔽祈福
  FTT_CANCEL_BLOCK_FERTILIZE = 6; // 屏蔽祈福取消
  FTT_SET_REMARK = 7; // 设置备注
  FTT_BLOCK_CANSTEAL = 8; // 屏蔽可偷提醒
  FTT_CANCEL_BLOCK_CANSTEAL = 9; // 取消屏蔽可偷提醒
}

message FarmTagFriend_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 tagType = 1;  //参考FarmTagType
  repeated uint64 uids = 2;
}

message FarmTagFriend_S2C_Msg {

}

message FarmSellAnimal_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 cropType = 1;  // 类型
  optional int64 cnt = 2; // 数量
}

message FarmSellAnimal_S2C_Msg {
}

// 废弃
message FarmQueryIsWhite_C2S_Msg {
  optional com.tencent.wea.xlsRes.FarmWhiteListType type = 1;
}

// 废弃
message FarmQueryIsWhite_S2C_Msg {
  optional bool isWhite = 1;
}

message FarmMonthCardNtf {
}

// 废弃
message FarmQueryFunctionOpenTime_C2S_Msg {
}

// 废弃
message FarmQueryFunctionOpenTime_S2C_Msg {
  optional int64 animalOpenTime = 1;
  optional int64 monthlyPassOpenTime = 2;
  optional int64 uavOpenTime = 3;
  optional int64 fertilizeOpenTime = 4;
}

message FishCardUnit {
  optional int32 fishId = 1;
  optional int32 count = 2; // 获得的鱼卡经验 其实应该叫exp 历史遗留问题
  optional int64 nowExp = 3;
  optional int32 nowLevel = 4;
  map<int32, int64> itemsReturn = 5; // 满经验会返还农场币
  optional int64 oldExp = 6;
  optional int32 oldLevel = 7;
  optional int32 expireExp = 8; // 溢出转化的经验量
  optional int32 fishCardNum = 9; // 获得的鱼卡数量
}

message FishCardPackOpenResult {
  repeated FishCardUnit FishCards = 1;
  optional int32 cardPackId = 2;
}

message FarmFishCardPackOpenNtf {
  repeated FishCardPackOpenResult cardPackResults = 1;
  optional string billNo = 2;
  optional int32 reason = 3; // enum ItemChangeReason
}

message FarmSetFishBowl_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 bowlId = 1;
  optional int32 itemId = 2; // 填0就是取出来
}

message FarmSetFishBowl_S2C_Msg {
}

message FarmFishBowlUnlock_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 bowlId = 1;
}

message FarmFishBowlUnlock_S2C_Msg {
}

message FarmOpenAquariumMode_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmOpenAquariumMode_S2C_Msg {
  map<int32, int64> returnItem = 1; // 物品返还 key是itemId
}

message FarmResetAquarium_C2S_Msg {// 重置
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmResetAquarium_S2C_Msg {
  map<int32, int64> returnItem = 1; // 物品返还 key是itemId
  map<int32, IntLongMap> returnBenefit = 2; // 收益金币返还 key是scale 和 idx
  optional int32 farmExp = 3;
}

message FarmUnlockAquariumSeat_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 scale = 1;
}

message FarmUnlockAquariumSeat_S2C_Msg {
}

message FarmSetAquarium_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 scale = 1;
  optional int32 idx = 2; // 鱼位
  optional int32 itemId = 3; // 填0就是取出来
  optional int32 trackId = 4; // 客户端轨迹id
}

message FarmSetAquarium_S2C_Msg {
  map<int32, IntLongMap> returnBenefit = 1; // 收益金币返还 key是scale 和 idx
  optional int32 farmExp = 2;
}

message FarmGetAquariumBenefit_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmGetAquariumBenefit_S2C_Msg {
  map<int32, IntLongMap> returnBenefit = 1; // 收益金币返还 key是scale 和 idx
  optional int32 farmExp = 2;
}

message FarmExchangeCoin_C2S_Msg {
  optional int32 count = 1; // 兑换几个
}


message FarmExchangeCoin_S2C_Msg {}

message FarmSocialDataNtf {
  optional int64 lastFertilizeTime = 1; //玩家上次施肥时间
  optional int32 todayFertilizeCount = 2; //玩家当日施肥次数
}

// 送礼物
message FarmSendGift_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional FarmGift gift = 1;
  optional int64 targetFarm = 2; // 送礼的目标农场（现在请填写当前农场ID）
}

message FarmSendGift_S2C_Msg {
  optional int64 giftID64 = 1;
  optional int32 skinID = 2;
  optional int32 todayTimes = 3;
  optional int32 todayTotalTimes = 4;
}

// 拾取礼物
message FarmPickGift_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int64 giftID64 = 1;
}

message FarmPickGift_S2C_Msg {
  optional int64 giftID64 = 1;
  map<int32, int32> itemToPlayer = 2; // 给玩家背包的道具 key是itemId
  optional string billNo = 3;
}

message FarmHandbookAwardGet_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 category = 1; // 图鉴奖励目录，3是鱼 FarmCropCategroy枚举
  optional int32 id = 2;  // 图鉴奖励id，在鱼的情况下对应的是层数
}

message FarmHandbookAwardGet_S2C_Msg {
}

enum FarmStatusRemindTypeForVisitor {
  FSRFV_CanSteal = 0; // 可偷
}

// 农场状态提醒类型
enum FarmStatusRemindType {
  FSR_CropRipe = 0; // 作物成熟
  FSR_CropDry = 1; // 作物干涸
  FSR_AnimalRipe = 2; // 动物成熟
  FSR_AnimalHungry = 3; // 动物饥饿
  FSR_AnimalCanEncourage = 4; // 动物可鼓励
  FSR_FishRipe = 5; // 鱼塘成熟
  FSR_MachineRipe = 6; // 加工器成熟
  FSR_AquariumRipe = 7; // 水族箱成熟
  FSR_CollectionDrop = 8; // 收藏品掉落
  FSR_CollectionDropInFarm = 9; // 收藏品掉落（仅农场）
  FSR_FishProtectEnd = 10; // 鱼塘保护到期时间
  FSR_GodFigureCanPray = 11; // 神像可祈福
  FSR_MagicSkillMpFull = 12; // 仙术蓝条满
  FSR_KirinIncubateFinish = 13; // 麒麟孵化完成
  FSR_KirinCanCollectMana = 14; // 麒麟可采集仙力
  FSR_CookVisitantCanServe = 15; // 餐厅贵宾可邀请
  FSR_CookVisitantCanPrebook = 16; // 餐厅贵宾可预约
  FSR_CookCanOpen = 17; // 餐厅可备菜
}

// 农场状态提醒通知
message FarmStatusRemindNtf {
  map<int32, int64> farmStatusRemindTimeMap = 1; // 类型 -> 时间戳 0表示不可达
}

message RainBowBuffIconInfo {
  optional int32 id = 1;
  optional int64 startTimeSec = 2;
  optional int64 endTimeSec = 3;
  optional string iconColor = 4;
  optional string iconText = 5;
}

// 农场七彩石Buff
message FarmRainBowBuffIconNtf {
  map<int32, RainBowBuffIconInfo> buffIconInfos = 1; // id -> IconInfo
}

message FarmFetchFunctionOpenTime_C2S_Msg {
}

message FarmFetchFunctionOpenTime_S2C_Msg {
  optional int64 animalOpenTime = 1;
  optional int64 monthlyPassOpenTime = 2;
  optional int64 uavOpenTime = 3;
  optional int64 fertilizeOpenTime = 4;
  optional int64 fishOpenTime = 5;
  optional int64 fishCardOpenTime = 6;
  optional int64 buildingSkinOpenTime = 7;
  optional int64 giftOpenTime = 8;
  optional int64 houseOpenTime = 9;
  optional int64 aquariumOpenTime = 10;
  optional int64 petOpenTime = 11;
  optional int64 collectionOpenTime = 12;
  optional int64 talentOpenTime = 13;
  optional int64 fishingReportOpenTime = 14;
  optional int64 redFoxNpcOpenTime = 15;
  optional int64 villagerOpenTime = 16;
  optional int64 petMenuOpenTime = 17;
  optional int64 farmVehicleOpenTime = 18;
  optional int64 farmTalentVileOpenTime = 19;
  optional int64 farmFishMergeOpenTime = 20;
  optional int64 farmFurnitureClickSelectOpenTime = 21;
  optional int64 farmHotspringOpenTime = 23;
  optional int64 farmGodFigureOpenTime = 24;
  optional int64 farmActRedPacketOpenTime = 25;
  optional int64 farmSignatureOpenTime = 26;  // 农场签名总开关
  optional int64 farmSignatureStrangerOpenTime = 27;  // 陌生人界面农场签名显示开关
  optional int64 farmCattleOpenTime = 28; // 奶牛NPC开启事件
  optional int64 cookOpenTime = 29;
  map<int32, FarmActivityTime> activityTimeInfo = 10000; // 枚举见FarmActivityTimeEnum
  map<string, int64> farmBuildingSkinOpenTime = 20000; // 建筑皮肤开启时间
  map<string, int64> farmTalentsOpenTime = 200001; // 天赋行开关
  map<string, int64> farmStatueMagicsOpenTime = 200002; // 仙术行开关
}

message FarmActivityTime {
  optional int64 beginTime = 1;
  optional int64 endTime = 2;
}

// 农场宠物喂食(狗+猫)
message FarmPetFeed_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int64 checkNum = 1;
  optional bool needCheckPlatFriend = 2;
}

message FarmPetFeed_S2C_Msg {
}

// 农场宠物看家护院开关(狗)
message FarmPetHouseKeepingSwitch_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional bool turnOff = 1; // 0开启 1关闭
}

message FarmPetHouseKeepingSwitch_S2C_Msg {

}

// 农场宠物客户端缓存(狗/猫)
message FarmPetSetClientCache_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  optional int64 farmId = 1;
  optional int32 petId = 2;
  optional string k = 3; // 键
  optional string v = 4; // 值为空就是删除
  optional int32 petCategory = 5; // 0狗1猫
}

message FarmPetSetClientCache_S2C_Msg {

}

// 宠物驱逐(狗)
message FarmPetEvict_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int64 uid = 1;  // 被踢人的uid
  optional int32 petId = 2; // 踢人的宠物id
}

message FarmPetEvict_S2C_Msg {
}

// 宠物交互(狗/猫)
message FarmPetInteract_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 id = 1; // 交互id
  optional int32 petCategory = 2; // 0狗1猫
  optional bool isWild = 3; // 是否野猫
}

message FarmPetInteract_S2C_Msg {

}

enum FarmPetGiftType {
  FPGT_Normal = 0; // 普通礼物
  FPGT_FullFavor = 1; // 满好感度礼物
}

// 宠物送礼(狗/猫)
message FarmPetGiftReceive_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 giftType = 1; // 礼物类型
  optional int32 petCategory = 2; // 0狗1猫
}

message FarmPetGiftReceive_S2C_Msg {
  map<int32, int64> items = 1; // 获得的东西
}

// 宠物改名(狗/猫)
message FarmPetChangeName_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 petId = 1; // 目标宠物id
  optional string name = 2; // 宠物名称
  optional int32 petCategory = 3; // 0狗1猫
}

message FarmPetChangeName_S2C_Msg {
}

// 宠物切换(狗/猫)
message FarmPetSummon_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 petId = 1; // 想切换的宠物id 如果是0就是收回
  optional int32 petCategory = 2; // 0狗1猫
}

message FarmPetSummon_S2C_Msg {
}

// 宠物装扮(狗/猫)
message FarmPetDressUp_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 petId = 1; // 目标宠物id
  repeated int32 clothingIds = 2; // 服装id列表
  optional int32 petCategory = 3; // 0狗1猫
}

message FarmPetDressUp_S2C_Msg {
}


message FarmPetTame_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 petId = 1; // 目标宠物id
}

message FarmPetTame_S2C_Msg {
}

message FarmSceneDropPick_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 type = 1; // 掉落类型
  optional int64 sceneDropUid = 2;  //
}

message FarmSceneDropPick_S2C_Msg {
  optional int32 itemChangeReason = 1;
  map<int32, int32> itemToPlayer = 2; // 给玩家背包的道具 key是itemId
  optional string billNo = 3;
}

message FarmEventItem {
  optional int32 itemID = 1;
  optional int64 count = 2;
}

message FarmEndEvent_C2S_Msg {
  optional int32 series = 1; // 只需要提供系列，因为同一时间一个系列只有一个事件执行中
}

message FarmEndEvent_S2C_Msg {
  repeated FarmEventItem items = 1;
  optional int32 reason = 2;
}

message FarmWeatherEffectNtf {
  map<int32, FarmWeatherEffectInfo> effectInfo = 1; // 地格id -> 缩短生长时间
  map<int32, com.tencent.wea.xlsRes.FarmCropRipeQuality> effectFertilize = 2; // 地格id -> 祈愿结果
}

message FarmWeatherEffectInfo {
  optional int32 shortenTime = 1; // 缩短的生长时间
}

message FarmWeatherTrigger_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 weatherId = 1; // 天气id
}

message FarmWeatherTrigger_S2C_Msg {
}

message FarmLightningStrikeReport_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
}

message FarmLightningStrikeReport_S2C_Msg {

}

message FarmFishPoolRecord_C2S_Msg {
}

message FarmFishPoolRecord_S2C_Msg {
  repeated FarmFishPoolRecord farmFishPoolRecords = 1;
}

message FarmEventGetReward_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 series = 1;
  optional int32 exp = 2; // 废弃
  optional int32 coin = 3; // 废弃
  optional int32 rewardType = 4; // 0经验 1农场币
  optional int64 exp64 = 5; // 客户端不用填
  optional int64 coin64 = 6; // 客户端不用填
}

message FarmEventGetReward_S2C_Msg {
  optional int32 exp = 1; // 废弃
  optional int32 coin = 2; // 废弃
  optional int64 exp64 = 3;
  optional int64 coin64 = 4;
}

message FarmEventSaveData_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 series = 1;
  optional string data = 2;
  optional int32 evtUID = 3; // 客户端不填
}

message FarmEventSaveData_S2C_Msg {

}

message FarmSelectPool_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 series = 1; // 给哪个事件
  optional int32 poolID = 2; // 选择哪个池子
  optional int64 endtime = 3; // cd结束时间，客户端不填
  optional int32 fishType = 4; // 0选择鱼，1选择卡包
}

message FarmSelectPool_S2C_Msg {
  repeated proto_FarmFishingRecord fishingRecord = 2; // 钓到的鱼
  optional int32 card = 3; // 钓到的卡包
  optional int32 cardNum = 4; // 钓到的卡包数量
}

message FarmNPCFishNtf {
  repeated proto_FarmFishingRecord fishingRecord = 1;
}

// 村民交互
message FarmVillagerInteract_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 villagerId = 1;
  optional int32 id = 2; // 交互id
}

message FarmVillagerInteract_S2C_Msg {

}

// 村民数据客户端缓存
message FarmVillagerSetClientCache_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int64 farmId = 1;
  optional int32 villagerId = 2;
  optional string k = 3; // 键
  optional string v = 4; // 值为空就是删除
}

message FarmVillagerSetClientCache_S2C_Msg {

}

// 村民给玩家送礼
message FarmVillagerPresentGift_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 villagerId = 1;
  optional int32 giftType = 2; // 礼物类型  FarmVillagerGiftType
}

message FarmVillagerPresentGift_S2C_Msg {
  map<int32, int64> items = 1; // 获得的东西 礼物道具ID->礼物数量
}

// 玩家给村民送礼
message FarmVillagerAcceptGift_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 villagerId = 1;
  map<int32, int64> items = 2; // 玩家送给村民的东西 礼物道具ID->礼物数量
}

message FarmVillagerAcceptGift_S2C_Msg {
    
}

// 邀请村民入驻
message FarmVillagerSummon_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 VillagerId = 1; // 邀请的村民ID
}

message FarmVillagerSummon_S2C_Msg {
}

// 村民请离
message FarmVillagerDeport_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 VillagerId = 1; // 村民ID
}

message FarmVillagerDeport_S2C_Msg {
}



message FarmTalentLevelUp_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 talentId = 1;
}

message FarmTalentLevelUp_S2C_Msg {
  map<int32, int64> items = 1; // 获得的东西
}

message FarmGetCropLevelReward_C2S_Msg {
  optional int32 cropType = 1;
  optional int32 level = 2;
}

message FarmGetCropLevelReward_S2C_Msg {
  map<int32, int64> items = 1; // 获得的东西
}

message FarmTaskGetReward_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 taskId = 1; // 任务ID
}

message FarmTaskRewardInfo {
  repeated int32 itemId = 1;
  repeated int64 itemNum = 2;
}

message FarmTaskGetReward_S2C_Msg {
  optional FarmTaskRewardInfo info = 1;
}

message FarmTaskSubmitItem_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 taskId = 1; // 任务ID
  optional int32 itemId = 2; // 提交的道具ID
}

message FarmTaskSubmitItem_S2C_Msg {
  optional FarmTaskRewardInfo info = 1;
}
message FarmTaskStatusNtf {
  optional int32 taskId = 1;
  optional int32 status = 2;
}

message FarmTaskConditionChange_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 condition = 1;
}

message FarmTaskConditionChange_S2C_Msg {
}

message FarmEventCommitItem_C2S_Msg {
  optional int32 series = 1;

  optional int32 itemID = 2; // 客户端不用填
  optional int32 itemCount = 3; // 客户端不用填
  optional int32 coin = 4; // 客户端不用填
  optional int32 exp = 5; // 客户端不用填
}

message FarmEventCommitItem_S2C_Msg {
  repeated FarmEventItem items = 1;
  optional int32 exp = 2;
  optional int32 coin = 3;
  optional int32 reason = 4;
}

message FarmTaskSetCanTrigger_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 taskId = 1;
  optional string pos = 2;
}

message FarmTaskSetCanTrigger_S2C_Msg {
  optional FarmTaskRewardInfo info = 1;
}

message FarmTaskFinishRemainTasks_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 taskId = 1;
}

message FarmTaskFinishRemainTasks_S2C_Msg {
  optional FarmTaskRewardInfo info = 1;
}

enum FarmRedFoxStateChangeEnum {
  FRFSC_Unknown = 0;
  FRFSC_MarkNewPlayer = 1;// 标记为小红狐新玩家，前置新手未完成 属性状态0->1
  FRFSC_NewPlayerPreDone = 2;// 小红狐新玩家前置新手任务完成，可做偷菜操作 属性状态1->2
  FRFSC_MarkNewPlayerSteal = 3;// 新玩家小红狐偷菜操作 属性状态2->3
  FRFSC_MarkOldPlayer = 4;// 标记为小红狐老玩家 属性状态0->4
}

message FarmTaskAddStealCropForRedFox_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 cmd = 1;// FarmRedFoxStateChangeEnum
}
message FarmTaskAddStealCropForRedFox_S2C_Msg {
}

message FarmFishMerge_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 itemId = 1;
}
message FarmFishMerge_S2C_Msg {
  optional int32 itemId = 1;
  optional int32 score = 2;
}

message FarmCommitItemToCloud_C2S_Msg {
  optional int32 series = 1;          // 哪个事件（填系列）
  optional int32 itemID = 2;          // 提交什么道具
  optional int64 itemCount = 3;       // 提交几个
}
message FarmCommitItemToCloud_S2C_Msg {
  optional int64 coin = 1; // 获得的农场币
}

// 领取好感度经验奖励
message FarmVillagerGetFavorExp_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 VillagerId = 1; // 村民ID
}

message FarmVillagerGetFavorExp_S2C_Msg {
  optional int64 addExp = 1;
}

message FarmVillagerSwitchScene_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  optional int32 VillagerId = 1; // 村民ID
  optional int32 dstBuildingId = 2; // 目的建筑ID(农场对应的BuildingId为0)
}

message FarmVillagerSwitchScene_S2C_Msg {
}
// 进入温泉
message FarmEnterHotSpring_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
}

message FarmEnterHotSpring_S2C_Msg {
}

// 离开温泉
message FarmLeaveHotSpring_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
}

message FarmLeaveHotSpring_S2C_Msg {
}

// 获取buff
message FarmGetHotSpringBuff_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_CURR_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 level = 1;
}

message FarmGetHotSpringBuff_S2C_Msg {
}

// 查询npc cd
message FarmQueryNpcCD_C2S_Msg {
  repeated int32 npcSerialIds = 1;  // npc系列id
}

message FarmQueryNpcCD_S2C_Msg {
  repeated int32 npcCDs = 1;        // 对应枚举FarmNpcCD
  repeated int32 costList = 2;      // 消耗召唤道具的数量列表
}

// 立即召唤npc
message FarmInvokeNpc_C2S_Msg {
  optional int32 npcSerialId = 1;
  optional bool ladderCost = 2;     // 应用分阶段不同消耗
}

message FarmInvokeNpc_S2C_Msg {
  optional bool withRain = 1;
}

// 神像许愿
message FarmPrayToGodFigure_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 type = 1;        // 许愿类型， 枚举FarmPrayToGodFigureType
}
message FarmPrayToGodFigure_S2C_Msg {
  optional int32 result = 1;      // 许愿结果， 枚举FarmPrayToGodFigureResult
  map<int32, int64> gotItems = 2; // 获得的东西
  optional string billNo = 3;
  optional int64 farmCoin = 4;
  optional int64 farmExp = 5;
}

// 神像升级 废弃
message FarmUpgradeGodFigure_C2S_Msg {

}
message FarmUpgradeGodFigure_S2C_Msg {

}

// 仙术
message FarmMagicUse_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 id = 1; // 仙术ID
  optional int32 targetType = 2; // 作用目标类型 枚举FarmMagicUsageTargetType
  optional int32 arg = 3; // 使用参数
  optional int32 effectArg = 4; // 效果参数
  repeated int32 options = 5; // 选项
}

message FarmMagicUse_S2C_Msg {

}

message FarmMagicShortenHarvestTimeNtf {
  optional int32 id = 1; // 仙术
  optional int32 targetType = 2; // 枚举FarmMagicEffectTargetType
  optional int32 arg = 3; // 使用参数
  map<int64, int64> result = 4; // key 田ID/加工器ID
}

message FarmMagicHarvestInfo {
  map<int32, int64> harvestItemInfos = 1;
}

message FarmMagicRestoreHarvestResult {
  map<int32, FarmMagicHarvestInfo> items = 1;
}

message FarmMagicDelayHarvestTimeResult {
  map<int64, int64> items = 1;
}

message FarmMagicEffectResult {
  optional FarmMagicRestoreHarvestResult restoreHarvestResult = 1;
  optional FarmMagicDelayHarvestTimeResult delayHarvestTimeResult = 2;
}

message FarmMagicEffectNtf {
  optional int32 id = 1; // 仙术
  optional int32 targetType = 2; // 枚举FarmMagicEffectTargetType
  optional int32 arg = 3; // 使用参数
  optional int32 effectType = 4; // 枚举FarmMagicEffectType
  optional int32 effectArg = 5;
  optional FarmMagicEffectResult result = 6;
}

message FarmMagicEquip_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 id = 1; // 仙术ID
  optional bool isEquip = 2; // 是否装备还是卸下
}

message FarmMagicEquip_S2C_Msg {

}

message FarmMagicUseRecoverItem_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 id = 1; // 仙术ID
  optional int32 itemId = 2; // 道具ID
  optional int64 costNum = 3; // 花费数量
}

message FarmMagicUseRecoverItem_S2C_Msg {

}

message FarmSetVisitorListTag_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 tag = 1; // 访客列表属性： 1=可展示 参见VisitorListTagType
}

message FarmSetVisitorListTag_S2C_Msg {
}

message FarmHotSpringTickNtf {
  optional int64 exp = 1; // 经验
}

message FarmItemBanSell_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional bool isBan = 1; // true禁售  false取消禁售
  repeated int32 itemIds = 2; // 道具配置ID
}

message FarmItemBanSell_S2C_Msg {
}

message FarmQueryRipeTimeBeforeRain_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 series =1;
  optional int32 evtID = 2;
}

message FarmQueryRipeTimeBeforeRain_S2C_Msg {
  optional int64 ripeTime = 1;
}

message FarmKirinLevelUp_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmKirinLevelUp_S2C_Msg {

}

message FarmKirinIncubate_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmKirinIncubate_S2C_Msg {

}

message FarmKirinCollect_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmKirinCollect_S2C_Msg {
  optional int64 obtainFarmExp = 1;
}

message FarmDelGiftRecord_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int64 index = 1; // 删除哪一个记录
}

message FarmDelGiftRecord_S2C_Msg {

}

// ES索引信息
message ESIndexInfo {
  optional string moduleName = 1; // 模块名称(envName + moduleName = indexName)
  optional string settings = 2; // 索引设置信息
}

// 新增派对文档 for es
message FarmPartyInfo {
  optional int64 accountID = 1; // 派对发布人id
  optional string content = 2; // 派对简介
  optional int32 isOpen = 3; // 派对是否开放
  optional int32 scene = 4; // 派对场景 FarmPartyScene
  optional string nickName = 5; // 发布人昵称
  optional int32 isInParty = 6; // 主人是否在派对场景
  optional int64 hotValue = 7; // 热度值
  optional float hotSafeRatio = 8; // 热度权重
  optional string versionGroup = 9; // 版本号
  optional int64 updateTime = 10; // 更新时间(秒)
  optional int64 expireTime = 11; // 过期时间(秒)
  optional int32 isWhiteList = 12; // 是否白名单
  optional int32 farmLevel = 13; // 农场等级
  optional int32 visitorCount = 14; // 派对参与人数
  optional float score = 15; // 排序分值
}

// 更新派对文档 for es
message UpdateFarmPartyInfo {
  optional string nickName = 1; // 发布人昵称
  optional int32 isInParty = 2; // 主人是否在派对
  optional int64 hotValue = 3; // 热度值
  optional float hotSafeRatio = 4; // 热度权重
  optional string versionGroup = 5; // 版本号
  optional int64 updateTime = 6; // 更新时间(秒)
  optional int32 isWhiteList = 7; // 是否白名单
  optional int32 farmLevel = 8; // 农场等级
  optional int32 visitorCount = 9; // 派对参与人数
}

// 更新派对开放信息 for es
message UpdateFarmPartyOpenInfo {
  optional int32 isOpen = 1; // 派对开放状态
  optional int64 updateTime = 2; // 更新时间(秒)
}

// 发布派对
message FarmPartyPublish_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional string content = 1; // 派对简介
  optional string partyImageURL = 2; // 预览图url
  optional proto_CosImage image = 3; // 预览图cos
  optional int32 scene = 4; // 发布场景 FarmPartyScene
}

// 发布派对
message FarmPartyPublish_S2C_Msg {
  optional int64 expireTimeSec = 1; // 过期时间
}

// 关闭派对
message FarmPartyClose_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
}

// 关闭派对
message FarmPartyClose_S2C_Msg {

}

// 派对排序规则
enum FarmPartySortRule {
  FPSR_Unknown = 0;
  FPSR_Default = 1; // 默认(随机)
  FPSR_Hot = 2; // 热度优先
}

// 获取派对列表
message FarmGetPartyList_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional string paginationID = 1; // PIT
  optional FarmPartySortRule sortRule = 2; // 排序规则
  optional int32 pageOffset = 3; // 分页用 偏移量
  optional int32 pageSize = 4; // 分页用 页大小
  optional string searchStr = 5; // 搜索
  optional int32 maxHot = 6; // 全服最大热度值
}

// 获取派对列表
message FarmGetPartyList_S2C_Msg {
  repeated FarmPartyInfo partyInfo = 1; // 派对信息
  optional string paginationID = 2; // 分页查询 pit ID
  optional int32 maxHot = 3; // 分页查询 max hot
}
// 设置留言权限：所有人、仅好友
message FarmSetLiuYanMessagePermission_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 permission = 1; // 权限：0=所有人，1=仅好友
  optional int32 type = 2; // 类型：1=留言权限，2=农场进入权限
}

message FarmSetLiuYanMessagePermission_S2C_Msg {

}

message FarmBatchReadSignature_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  repeated int64 uidList = 1;
}

message FarmBatchReadSignature_S2C_Msg {

}

message FarmTagRedFox_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 tagType = 1;  //参考FarmTagType
  optional string remarkName = 2; // 设置备注时的备注名
}

message FarmTagRedFox_S2C_Msg {

}

message FarmGMCommand_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  option (farm_gen_gamesvr_handler) = true;
  optional int32 type = 1;
  repeated string params = 2;
}

message FarmGMCommand_S2C_Msg {
  optional int32 code = 1;
  optional string data = 2;
  optional bytes bin = 3;
}

message FarmGetNewItemNtf {
  optional int32 itemId = 1;   // 农场道具配置ID
}

// 转发到自己的农场
message SelfFarmCsForwardTest_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional string param = 1;
}

message SelfFarmCsForwardTest_S2C_Msg {
  optional string rsp = 1;
}

// 不打forward_type标记, 默认转发到当前的农场
message CurrFarmCsForwardTest_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  optional string param = 1;
}

message CurrFarmCsForwardTest_S2C_Msg {
  optional string rsp = 1;
}


message FarmGetCloudTax_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 series = 1;
}

message FarmGetCloudTax_S2C_Msg {
  optional int64 coin = 1; // 获得的税收
}

message FarmSetBeFertilizedType_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
  optional int32 canFertilizedCategory = 1; // 哪些类型可以被祈福 0无限制 -1不允许 其它参见FarmCropCategory
}

message FarmSetBeFertilizedType_S2C_Msg {
}

// 当玩家打开神像界面
message FarmOpenGodUI_C2S_Msg {

}

message FarmOpenGodUI_S2C_Msg {
  optional int32 commitTimes = 1; // 本周提交几次云游商人了
  optional int32 leftTimes = 2; // 本周还可以提交几次云游商人
}

message FarmBadGuyCloudTaxDetail_C2S_Msg {
  option (forward_to_str) = "ST_FarmServer";
  option (farm_forward_dst) = FFD_SELF_FARM;
}

message FarmBadGuyCloudTaxDetail_S2C_Msg {
  repeated FarmCloudTaxRecord details = 1;
}

message FarmCanStealNtf {
  optional int64 canStealTime = 1;
}
