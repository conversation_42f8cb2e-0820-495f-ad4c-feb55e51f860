syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "common.proto";
import "cs_head.proto";
import "attr_PlayerPublicEquipments.proto";
import "attr_UgcMapSetInfo.proto";
import "attr_FittingSlots.proto";
import "attr_ItemInfoDb.proto";
import "attr_XlsWhiteList.proto";

enum PlatCommon_C2S_Cmd
{
  CmdNone = 0;

  // 安全中心
  Report = 1001;
  // app数据上报
  AppDataReport = 1002;
  // QQ ARK JSON
  QueryQQArkJson = 1003;

  // 数据上报
  ShareFlow = 1101;
  SendSmsCaptcha = 1102; // 发送验证码
  // QQ音乐
  QQMusicLogin = 2001;
  QQMusicLogout = 2002;
  QQMusicCheckLogin = 2003;
  QQMusicGetUserVIPFlagStatus = 2004;
  QQMusicBatchGetSongDetail = 2005;
  QQMusicGetSongsInSonglist = 2006;
  QQMusicSearchSong = 2007;
  QQMusicAddSongToSonglist = 2008;
  QQMusicRemoveSongFromSonglist = 2009;
  QQMusicReportListen = 2010;
  QQMusicGetSonglistSquare = 2011;
  QQMusicGetRecentlyPlayMusic = 2012;
  QQMusicGetIndividualRadio = 2013;
  CloudMusicGetSongs = 2101;
  CloudMusicBatchGetSongDetail = 2102;
  CloudMusicGetSongUrl = 2103;
  CloudMusicSearchSong = 2104;

  // 管理端
  GetPlatConfig = 2105;
  GetUserFeedbackDataList = 2106;
  GetPlatConfigV2 = 2107;
  BatchGetPlatConfig = 2108;
  GetUgcVersionContent = 2109;
  GetUgcVersionContentList = 2110;
  GetUgcVersionReddot = 2111;
  GetNonGameFriends = 2112;
  GetActiveTabNoticeConf = 2113;
  GetUGCGuideBanner = 2116;

  // 3D模型
  SubmitAIGenerate3DTask = 2201;
  GetAIGenerate3DTaskList = 2202;
  GetAIGenerate3DTaskDetail = 2203;
  GetAIGenerate3DTaskAdditional = 2204;
  DeleteAIGenerate3DTask = 2205;
  GetAIGenerate3DConfigs = 2206;
  AIGenerate3D2Res = 2207;
  RetryAIGenerate3DTask = 2208;
  GetAIGenerate3DRedDot = 2209;
  HideAIGenerate3DRedDot = 2210;

  // 腾讯视频
  TXVideoLogin = 2501;
  TXVideoLogout = 2502;
  TXVideoCheckLogin = 2503;
  TXVideoGetUserVIPStatus = 2504;
  TXVideoGetTypes = 2505;
  TXVideoGetCList = 2506;
  TXVideoGetCDetail = 2507;
  TXVideoSearchC = 2508;
  QueryLiveStream = 2509;

  // 创作者中心-元梦学院
  YMXYGetTutorial = 3001;
  YMXYGetTutorials = 3002;
  YMXYGetCategoryTutorials = 3003;
  YMXYGetRecommendTutorials = 3004;
  YMXYViewTutorial = 3005;
  YMXYGetViewTutorialList = 3006;
  YMXYLikeTutorial = 3007;
  YMXYGetLikeTutorialList = 3008;
  YMXYCollectTutorial = 3009;
  YMXYUselessTutorial = 3010;
  YMXYSearch = 3011;
  YMXYGetSearchSuggestions = 3012;
  YMXYGetGuide = 3013;
  YMXYViewTutorialFinish = 3015;
  YMXYGetCourseTutorials = 3016;

  // 结算中心
  OMIncomeIndex = 3101;// 首页
  OMBindAccount = 3102;// 绑定账号
  OMGetBindAccount = 3103;// 获取绑定的账号
  OMGetBankList = 3104;// 获取卡列表
  OMGetProvinceList = 3105;// 获取省市
  OMWithdraw = 3106;// 提现
  OMGetWithdrawInfo = 3107;// 获取提现信息
  OMGetTransactionList = 3108;// 历史记录
  OMAgreement = 3109;// 同意协议
  OMExchangeDiamond = 3110;// 兑换
  OMUnBindAccount = 3111;// 兑换
  OMGetUserIncomeList = 3112;//
  OMExchangeStart = 3113;//
  OMUgcIncomeDetail = 3114;//
  OMUgcIncomeIndex = 3115;//
  OMUgcContractSigning = 3116;// 提交内购合同
  OMGetUgcContractSigning = 3117;// 查询内购合同
  OMApplyIncentive = 3118;// 申请加入激励计划
  OMGetIncentiveIndex = 3119;// 获取激励计划数据

  // 切片玩法
  ChannelGameEnd = 3200;//  游戏结束回调
  ChannelGameStart = 3201; // 游戏启动

  ChannelGameReport = 3202; // 游戏上报
  ChannelGameGetPopupEvent = 3203; // 微端弹窗配置

  // 数据中心
  DCIndex = 3300;// 数据中心首页
  DCMapDetail = 3301;// 数据中心地图详情页
  DCCreatorWeekly = 3302;
  DCFansIndex = 3303;
  DCMapList = 3304;
  DCGetCharts = 3305;
  DCGetFaithfulFans = 3306;
  DCIndexV2 = 3307;
  DCMapDetailV2 = 3308;

  // 活动
  ActivityApi = 3401; // 星途活动 api
  GetCreatorBaseInfo = 3402; // 拉创作者基本信息
  GetCreatorIndex = 3403; // 创作者首页
  GetTeachingList = 3404;
  GetNoticeDetail = 3405;
  GetNoticeList = 3406;
  LikeNotice = 3407;
  ViewNotice = 3408;
  GetCreatorCenterReddot = 3409;// 创作者中心红点
  ReportCreatorCenterReddot = 3410;// 上报创作者中心红点 == 消除
  GetTemplateSubStatus = 3411;// 拉取订阅状态
  GetBiRecommendResult = 3413;// 获取bi推荐的数据
  // 元元助手
  YYChatCheckText = 4001;
  YYChat = 4002;
  YYChatHistory = 4003;

  // 平台媒体房间
  PlatCreateRoom = 5001;
  PlatDestroyRoom = 5002;
  PlatEnterRoom = 5003;
  PlatLeaveRoom = 5004;
  PlatRecoverRoom = 5005;
  PlatDoRoomCmd = 5006;
  PlatGetRoomData = 5007;
  PlatWxAppGetRoomInfo = 5008;
  PlatShareBattleRoomInfo = 5009;
  PlatYJKHCheckTicket = 5010;

  // 分享
  ShareCreateShareCode = 6001;
  ShareGetShareCodeInfo = 6002;
  ShareSignQQShareArk = 6003;
  ShareSendQQShareArk = 6004;
  ShareSignQQPicArk = 6005;
  ShareSignAndSendQQTeamShareArk = 6006;
  ShareGetRegisteredFriends = 6007;
  ShareGetCloudGameUrl = 6008;
  ShareGetSceneGroupInfo = 6009;
  ShareAddQQBotFriend = 6010;
  ShareIsQQBotFriend = 6011;
  ShareGetMatchTypeShareInfo = 6012;
  ShareCheckWxSphReserveStatus = 6013;
  ShareReserveWxSph = 6014;

  GuideGetQQUserProfile = 6015; // 新手引导获取用户标签

  // 实验
  TABGetOpenNoticeConfig =  6101;

  // 灵感中心
  LGZXRecommendMapInspireList = 7001;   // 获取推荐地图灵感
  LGZXTagMapInspireList = 7002;         // 获取对应标签地图灵感
  LGZXAISceneInspireList = 7003;        // 获取ai场景灵感
  LGZXActivityConfig = 7004;            // 获取活动数据
  LGZXSetCollectMap = 7005;             // 收藏/取消收藏地图
  LGZXSetCollectAISceneInspire = 7006;  // 收藏/取消收藏ai场景灵感
  LGZXSetFollowCreator = 7007;          // 关注/取消关注创作者
  LGZXBatchGepMapInspiresById = 7008;   // 通过id获取地图灵感信息
  CreatorActivityGetHotMap = 7009;      // 创作者激励活动
  LGZXMapInspireList = 7010;            // 获取地图灵感

  LGZXV2CollectTopic = 7019; // v2话题收藏
  LGZXV2GetTopicCollectInfo = 7042; // v2查询是否收藏

  UgcParkHotActivityInfo = 7030;        // 获取活动热门信息
  PlayerContestGetActivityInfo = 7031;  // 运动会获取活动信息
  PlayerContestStartPlayUgc = 7032;     // 运动会开始游玩地图
  PlayerContestGetUgcPlayerRank = 7033; // 运动会获取好友排行榜
  PlayerContestReward = 7034;           // 运动会领奖
  GetUserInfo = 7035;
  TaskGetDiscoverPageActivity = 7040;   // 获取发现页活动数据
  TaskGetUmgActivityInfo = 7041;        // 获取umg活动数据
  GetCommonUploadPresignedurl = 7050;
  UgcBugReport = 7051;
  UgcResearchGetResearchUrl = 7052;

  // 创作者排行榜
  GetCreatorTopKRankList = 8001;      // 获取前K排行榜
  GetCreatorAroundRankList = 8002;    // 获取用户前后K排行榜
  GetUgcCommonRankInfo = 8003;        // ugc地图自定义排行榜
  DelUserFromUgcCommonRank = 8004;    // 删除ugc地图自定义排行榜用户记录

  // 全服地区排行榜
  GetRankByUser = 8100;
  GetRankTopK = 8101;
  UpdateRankByUser = 8102;
  DeleteRankByUser = 8103;
  DeleteRank = 8104;
  MoveRankByUser = 8105;
  BatchUpdateRankByUser = 8106;
  GetRankByScore = 8107;

  // WorkFlow星妹期刊
  GetMagazine = 8200;
  MagazineReport = 8201;
  MagazineTakeReward = 8202;
  MagezineLike = 8203;
  LobbyConfig = 8204;
  // Static
  CheckMapNeedDefaultCover = 8300;    // wx小游戏分享获取预设封面

  // 脑力挑战
  IqJoin = 9001;                      // 报名接口
  IqReady = 9002;                     // 候场接口
  IqCommit = 9003;                    // 答题接口
  GetIqReadyUser = 9004;              // 拉取候场用户列表接口
  GetIqIndex = 9005;              // 领取活动信息
  GetIqBattleInfo = 9006;              // 拉取战绩
  ReceiveIqAssistanceReward = 9007;              // 领取道具
  GetIqProps = 9008;              // 拉取道具列表
  UseIqProps = 9009;              // 使用道具
  ReceiveIqDefaultReward = 9010; // 兜底奖励
  GetIqResult = 9011;             // 拉取结果
  GetIqAssistance = 9012;             // 拉取结果
  GetMapAdventureIndex = 9013; // 首页
  MapAdventureLottery = 9014;// 抽奖
  ReceiveMapAdventureScore = 9015; // 领取积分
  GetMapAdventureMapList = 9016; // 拉取地图列表
  GetMapAdventureRewardList = 9017; // 拉取奖励列表
  UgcActivityGetBPShort = 9028;// bp简版
  UgcActivityGetBPIcon = 9029;// bpicon
  CurrentTriggerPrecisionActivity = 9030; // 精细化活动
  GetUGCEntranceBubble = 9033; // 获取入口气泡

  UploadLogReport = 10001; // 上报日志
  // 海外独有协议
  OverseaReviewMap = 20001; // 管理员通过审核


  GetRecommendMapList = 31002;
  // 独立APP协议
  APPGetBottomTabs = 1000001;

  AppGetMyPageInfo = 1001001;
  AppGetBindedGameRole = 1001002;
  AppGetAllGameRoles = 1001003;
  AppBindGameRole = 1001004;
  AppUnbindGameRole = 1001005;
  AppGetUserInfo = 1001006;
  AppGetAccountStatus = 1001007;
  AppCheckEduToken = 1001009;
  AppLogOut = 1001010;
  AppBindPlatFormAccount = 1001011;
  AppUnBindPlatFormAccount = 1001012;

  GetCoCreatedInviteCode = 1001013;
  CancelCoCreatedInviteCode = 1001016;
  JoinCoCreated = 1001014;
  BatchGetAppUserInfo = 1001015;
  GetMinigameCollection = 1001017;

  // 造梦空间协议
  AppUgcDraftList = 1002001;
  AppCopyMap = 1002002;
  AppCopyProgress = 1002003;
  GroupWhiteList = 1002004;
  // APP任务协议
  GetRewardActivityList = 1002101;
  GetRewardActivityDetail = 1002102;
  TaskCommit = 1002103;
  TakeReward = 1002104;

  // APP活动需求协议
  SendSmsCode = 1001008;
  GetEDUActivityInfo = 1003001;
  CreateEDUActivitySignUp = 1003002;
  GetEDUActivitySignUp = 1003003;
  UpdateEDUActivitySignUp = 1003004;
  DeleteEDUActivitySignUp = 1003005;
  UpdateEDUActivitySubmit = 1003006;
  GetEDUActivityCourses = 1003007;
  GetEDUQuizSet = 1003008;
  SubmitEDUQuizSetAnswer = 1003009;
  GetEDUActivityBannerList = 1003010;
  GetPrizeInfo = 1003011;
  GetWorksShowInfo = 1003012;
  BatchGetEDUMapTemplateInfo = 1003013;
  GetEDUPageTabList = 1003014;
  GetEDUFeaturedMapTabList = 1003015;
  GetEDUFeaturedMapList = 1003016;
  GetEDUStarMapList = 1003017;
  GetEDUPageBlockList = 1003018;
  GetEDUCourseInfo = 1003019;
  EDUSearch = 1003020;
  BatchGetEDUFeaturedMapInfo = 1003023;
}

// 通用协议
message PlatCommon_C2S_Msg {
  optional uint32 cmd = 1; // 平台CMD枚举值: PlatCommon_C2S_Cmd
  optional bytes req_body = 2; //业务包体
  optional int32 serialization_type = 3; // 序列化类型；0-PB; 2-JSON
  optional string channel_info = 4; // msdk channel info
  optional string version = 5; // 客户端版本号
  optional uint32 compress_type = 6; // 0-无压缩；1-gzip
  optional string appid = 7; // 透传appid

  // 客户端信息
  optional int32 client_source = 101; // 客户端来源 0=元梦主端 1=PC专业版客户端 2=PC版本客户端
}

message PlatCommon_S2C_Msg {
  optional int32 err_code = 1; // 错误码，0为成功
  optional string err_msg = 2; // 错误码描述信息
  optional bytes rsp_body = 3; // 业务回包
  optional uint32 compress_type = 4; // 0-无压缩；1-gzip
  optional int64 timestamp = 5; // 服务器时间(unix时间戳,单位秒)
}

// 独立APP通用协议
message AppCommon_C2S_Msg {
  optional uint32 cmd = 1; // 平台CMD枚举值。0-VerifyLogin；其他业务接口参考配表
  optional bytes req_body = 2; // 业务包体
  optional int32 serialization_type = 3; // 序列化类型；0-PB; 2-JSON
  optional string token = 4; // 用户令牌。业务接口必传
  optional string appid = 5; //当前主体的appid

  // 客户端信息
  optional string client_version = 101; // 客户端版本号
  optional string channel_info = 102; // 渠道信息
  optional bool is_game_cmd = 103; // 透传给游戏的cmd
  optional CSHeader cs_header = 104; // 客户端信息
  optional uint32 client_plat = 105; // 客户端平台 PC = 1;Android  = 2;Iphone = 3;ipad = 4;WinPhone=5
  optional string channel_version = 106; // 渠道版本
  optional uint32 login_plat = 107; // 1-高清模拟器; 2-云游戏；0-普通手机包
}

message AppCommon_S2C_Msg {
  optional int32 err_code = 1; // 错误码，0为成功
  optional string err_msg = 2; // 错误码描述信息
  optional bytes rsp_body = 3; // 业务回包
  optional uint32 compress_type = 4; // 0-无压缩；1-gzip
  optional int64 timestamp = 5; // 服务器时间(unix时间戳,单位秒)
  optional int64 timestamp_milli = 6; // 服务器时间(unix时间戳,单位毫秒)

  optional CSHeader cs_header = 101; // 客户端信息
}

// VerifyLogin cmd=0
message VerifyLoginReq {
  optional int32 channel_type = 1; // 0 -> APP端MSDK; 1 -> ue编辑器假登录态; 2 -> msdk手机号登录
  optional int32 area = 2; // 1-微信; 2-手Q
  optional int32 plat_id = 3; // 0 -> iOS; 1 -> 安卓; 2 -> pc端扫码登录(即web登录态)
  optional string open_id = 4; // gopenID
  optional string access_token = 5; // 用户access token
  optional string channel_open_id = 6; // 微信/QQ OpenID
  optional uint32 msdk_env = 7; // 1 -> 测试; 2 -> 正式
  optional string device_id = 8; // 设备ID，安卓记录的是安卓ID，IOS记录的是IDFV（IDFA）
  optional string binded_telephone = 9; // 手机号
}

message AppUserBaseInfo {
  optional int64 creator_id = 1;
  optional uint32 agree_status = 2; // 协议签署状态
  optional int64 game_creator_id = 3;
  optional int32 channel_type = 4;  // 0 -> APP端MSDK; 1 -> ue编辑器假登录态; 2 -> msdk手机号登录
  optional string env = 5; // 环境参数
}

message AppUserLoginBanInfo {
  optional bool is_ban = 1;
  optional int64 unban_time = 2;
  optional string reason = 3;
}

message VerifyLoginRsp {
  optional string token = 1;
  optional AppUserBaseInfo base_info = 2; // app用户基础信息 登录时下发
  optional Im im = 3; // 腾讯云im数据
  optional AppUserLoginBanInfo app_login_ban_info = 4;  // app登录封禁信息
  optional AppUserLoginBanInfo game_login_ban_info = 5; // game登录封禁信息
}

message Im {
  optional uint32 app_id = 1;
  optional string sign = 2;
}

enum ImMessageType {
  ImMessageTypeNone = 0;
  ImMessageTypeGameNtf = 1;
}

message ImMessage {
  optional uint32 msg_type = 1; // 消息类型
  optional bytes msg_body = 2;
}

// 传给平台服务器时放到Header的参数
message PlatCommonHeader {
  optional uint64 uid = 1;
  optional string open_id = 2;
  optional uint32 area = 3;
  optional uint32 partition = 4;
  optional string env = 5;
  optional string gopen_id = 6;
  optional uint64 timestamp = 7;

  optional uint32 plat_id = 8;
  optional uint32 login_type = 9;
}

// 业务协议

// 举报
enum ReportType {
  EReportTypeNone = 0;
  EReportTypeChatData = 1;    // 聊天消息举报
  EReportTypeUserInfo = 2;    // 个人信息举报
  EReportTypeBattleData = 3;  // 游戏行为举报
  EReportTypeRoomData = 7;    // 房间举报
  EReportTypeUgcMap = 8;      // ugc地图举报
  EReportTypeGroup = 9;       // 组合举报
  EReportTypeXiaowo = 10;     // 小窝家园举报
  EReportTypeClub = 11;       // 社团举报
  EReportTypeMultiTest = 12;  // 多人测试举报
  EReportTypeFarmPet = 22; // 农场宠物举报
}

message CollectionData {
    optional string collection_id = 1;          // 地图合集id
    optional string collection_name = 2;        // 地图合集名称
    optional string collection_desc = 3;        // 地图合集描述
    optional string collection_cover = 4;       // 地图合集封面
    optional uint64 uid = 5;                    // 地图合集作者uid
    optional string nick = 6;                   // 地图合集作者头像
}

message PhotoAlbumData {
    optional uint64 uid = 1;
    optional string nick = 2;
    optional string pickey = 3;
    optional string url = 4;
    optional string head_url = 5;     // 被举报者头像url
}

message ReportFarmMsgBoardData {
    optional uint64 farm_id = 1;                // 农场id
    optional string msg_id = 2;                 // 寄语/留言消息id
    optional string msg_content = 3;            // 寄语/留言文本内容
    optional uint64 uid = 4;                    // 留言者uid
    optional string nick = 5;                   // 留言者昵称
    optional string head_url = 6;               // 留言者头像
    optional string homeownerid = 7;            // 家园主人openid

    optional uint32 content_type = 8;           // 星级评分举报填充-留言0 回复1
    optional string reply_id = 9;               // 星级评分举报填充-回复id
}

message ReportFarmGiftData {
    optional uint64 farm_id = 1;                // 农场id
    optional string gift_id = 2;                // 礼物id
    optional string msg_content = 3;            // 礼物文案(非空)
    optional uint64 uid = 4;                    // 送礼者uid
    optional string nick = 5;                   // 送礼者昵称
    optional string head_url = 6;               // 送礼者头像
    optional string homeownerid = 7;            // 农场主人openid
    optional uint64 homeowner_uid = 8;          // 农场主人uid
}



message ReportCatchphraseData {
    optional uint64 uid = 1;                 // 个人信息的uid
    optional string nick = 2;                // 个人信息的昵称
    optional string head_url = 3;            // 个人信息的头像
    optional string catchphrase = 4;         // 个人信息的个性签名
}

message ReportCodingTemplateData {
    optional uint64 template_id = 1;         // 编程模板id
    optional string template_name = 2;       // 编程模板名称
    optional string template_desc = 3;       // 编程模板描述
    optional string template_cover_url = 4;  // 编程模板封面
    optional uint64 uid = 5;                 // 作者uid
    optional string nick = 6;                // 作者昵称
    optional string head_url = 7;            // 作者头像
}

message ReportFarmHouseData {
    optional uint64 farm_id = 1;                // 农场id
    optional string homeownerid = 2;            // 农场主人openid
    optional uint64 homeowner_uid = 3;          // 农场主人uid
}

message ReportFarmPetData {
    optional uint64 farm_id = 1;                // 农场id
    optional string homeownerid = 2;            // 农场主人openid
    optional uint64 homeowner_uid = 3;          // 农场主人uid
    optional string homeowener_headurl = 4;     // 农场主人头像

    optional string pet_id = 5;                 // 宠物id
    optional string pet_name = 6;               // 宠物名称
}

message ReportGreetingCardData {
    optional string card_id = 1;             // 贺卡id
    optional string card_content = 2;        // 贺卡内容
    optional uint64 uid = 3;                 // 送贺卡者uid
    optional string nick = 4;                // 送贺卡者昵称
    optional string head_url = 5;            // 送贺卡者头像
}

message ReportFarmSignData {
    optional uint64 farm_id = 1;                // 农场id
    optional uint64 uid = 2;                    // 签名者uid
    optional string nick = 3;                   // 签名者昵称
    optional string head_url = 4;               // 签名者头像
    optional string farm_signature = 5;         // 签名内容
}

message ReportCreatorHomeData {
    optional uint64 uid = 1;                 // 个人信息的uid
    optional string nick = 2;                // 个人信息的昵称
    optional string head_url = 3;            // 个人信息的头像
    optional string creator_signature = 4;   // 签名内容
}

message ReportFarmPartyData {
    optional uint64 farm_id = 1;                // 农场id
    optional string homeownerid = 2;            // 农场主人openid
    optional uint64 homeowner_uid = 3;          // 农场主人uid
    optional string homeowener_headurl = 4;     // 农场主人头像

    optional string party_url = 5;              // 农场派对封面图
    optional string party_desc = 6;             // 农场派对简介
    optional string reported_name = 7;          // 昵称
}

message ReportFarmRestaurantData {
    optional uint64 farm_id = 1;                // 农场id
    optional uint64 homeowner_uid = 2;          // 农场主人uid
    optional string screen_content = 3;         // 流动显示屏
    optional string nick = 4;                   // 农场主人昵称
}

message ReportDisplayBoardData {
    optional uint64 uid = 1;                    // 被举报者uid
    optional string head_url = 2;               // 被举报者头像
    optional string nick = 3;                   // 被举报者昵称

    optional string display_board_pic = 4;      // 展板封面图
    optional string display_board_content = 5;  // 展板宣言
    optional uint64 map_id = 6;                 // 展板推荐地图id
    optional string propsid = 7;                // 道具id
}

message ReportReq {
  optional uint32 type = 1;           // 参考ReportType 1, 聊天消息举报; 2,个人信息举报; 3, 游戏行为举报; 7:房间举报 8, ugc地图举报;
                                      // 9,组合举报; 10,小窝家园举报; 11,社团举报
  repeated uint32 content_id_list = 2;// 废弃
  optional string text = 3;           // 举报内容，备注等
  repeated string image_list = 4;     // 附加的截图列表
  optional ReportSceneInfo report_scene_info = 5;       // 举报场景信息
  optional string informant_user_nick = 6;              // 举报者昵称

  optional ReportUserInfoData user_info_data = 10;     // 个人信息数据
  optional ReportChatData chat_data = 11;              // 聊天消息数据
  optional ReportBattleData battle_data = 12;          // 游戏行为数据
  optional ReportUgcMapData ugc_map_data = 13;         // ugc地图数据
  optional ReportRoomData room_data = 14;              // 房间举报
  optional ReportGroupData group_data = 15;            // 组合举报
  optional ReportXiaowoData xiaowo_data = 16;          // 小窝举报
  optional ReportClubData club_data = 17;              // 社团举报
  optional ReportMsgBoardData msgboard_data = 18;      // 小窝留言板举报
  optional CollectionData collection_data = 19;        // 合集举报
  optional PhotoAlbumData photoalbum_data = 20;        // 个人相册举报
  optional ReportFarmMsgBoardData farm_msg_data = 21;  // 农场寄语/留言举报
  optional ReportCatchphraseData catchphrase_data = 22; // 个性签名举报
  optional ReportCodingTemplateData template_data = 23; // 编程模板举报
  optional ReportFarmGiftData  farm_gift_data = 24;     // 农场送礼举报
  optional ReportFarmHouseData farm_house_data = 25;    // 农场小窝举报
  optional ReportFarmPetData farm_pet_data = 26;        // 农场宠物名举报
  optional ReportGreetingCardData greeting_card_data = 27; // 贺卡举报
  optional ReportFarmSignData farm_sign_data = 28;      // 农场签名举报
  optional ReportCreatorHomeData creator_home_data = 29; // 创作者主页举报
  optional ReportFarmPartyData farm_party_data = 30;     // 农场派对板举报
  optional ReportFarmRestaurantData farm_restaurant_data = 31; // 农场餐厅举报
  optional ReportDisplayBoardData display_board_data = 32; // 推图板数据
}

message ReportRsp {
}


message ReportSceneInfo {

  optional uint32 report_category = 1;    // 举报大类
  optional uint32 report_entrance = 2;    // 举报入口
  repeated uint32 report_reason = 3;      // 举报原因
}


message ReportChatData {
  optional uint64 from_uid = 1;            //  聊天消息发送人uid
  optional string chat_id = 2;             //  聊天消息id
  optional string chat_data = 3;           //  聊天消息内容
  optional string from_user_head_url = 4;  //  聊天消息发送人头像
  optional string from_user_nick = 5;      //  头像
}
message ReportUserInfoData {
  optional uint64 uid = 1;                 // 个人信息的uid
  optional string nick = 2;                // 个人信息的昵称
  optional string head_url = 3;            // 个人信息的头像
  // 推图展板场景
  optional  string display_board_pic = 4;      // 展板封面图
  optional  string display_board_content = 5;  // 展板宣言
  optional  uint64 map_id = 6;                 // 展板推荐地图id
  optional  string propsid = 7;                // 道具id
  // 痛包场景
  optional  string tongbao_pic_url = 8;        // 痛包图片url
}

message ReportBattleData {
  optional string battle_id = 1;           // 对局id
  optional uint64 uid = 2;                 // 异常用户uid
  optional string head_url = 3;            // 举报【头像违规】时，须将被举报者头像url
  optional uint64 battle_time = 4;         // 对局时间
  optional string nick = 5;                // 异常用户昵称
  optional uint32 match_typeid = 6;        // 对局玩法id
}

message ReportUgcMapData {
  optional uint64 map_id = 1;              // ugc地图id
  optional uint64 uid = 2;                 // ugc创作者uid
  optional string map_name = 3;            // 地图名称
  optional string map_desc = 4;            // 地图简介
  optional string map_cover_url = 5;       // 地图封面图
  optional string nick = 6;                // ugc创作者昵称
  optional string head_url = 7;            // 组合创作者头像

  optional string map_instruction_title = 8;    // 地图说明标题
  optional string map_instruction_content = 9;  // 地图说明内容
  repeated string map_instruction_pics = 10;    // 地图说明图片

  repeated string map_loading_pics = 11;        // 自定义loading图
}

message ReportRoomData {
  optional string room_id = 1;             // 房间id
  optional uint64 uid = 2;                 // 房间创建者uid
  optional string room_name = 3;           // 房间名称
  optional string room_pic_url = 4;       // 举报【封面涉黄】【封面涉政】【封面涉及辱骂】时，另需将封面图url上报至pic_url字段
  optional string nick = 5;                // 房间创建者昵称
  optional string room_desc = 6;           // 房间描述
}

message ReportGroupData {
  optional uint64 group_id = 1;            // 组合id
  optional string group_name = 2;          // 组合的名称
  optional string group_desc = 3;          // 组合的描述
  optional string group_cover_url = 4;     // 组合封面
  optional uint64 uid = 5;                 // 组合创作者uid
  optional string nick = 6;                // 组合创作者昵称
  optional string head_url = 7;            // 组合创作者头像
}

message ReportXiaowoData {
  optional uint64 xiaowo_id = 1;              // 小窝id
  optional string xiaowo_name = 2;            // 小窝名称
  optional string xiaowo_desc = 3;            // 小窝简介
  optional string xiaowo_cover_url = 4;       // 小窝封面图
  optional uint64 uid = 5;                    // 小窝创作者uid
  optional string nick = 6;                   // 小窝创作者昵称
  optional string head_url = 7;               // 小窝创作者头像
  optional string welcome_msg = 8;            // 欢迎词文本内容
}

message ReportMsgBoardData {
  optional uint64 xiaowo_id = 1;              // 小窝id
  optional string msg_id = 2;                 // 留言消息id
  optional string msg_content = 3;            // 留言板文本内容
  optional uint64 uid = 4;                    // 留言者uid
  optional string nick = 5;                   // 留言者昵称
  optional string head_url = 6;               // 留言者头像
  optional string homeownerid = 7;            // 家园主人openid
}

message ReportClubData {
  optional string club_id = 1;                // 社团id
  optional string club_name = 2;              // 社团名称
  optional string club_declaration = 3;       // 社团宣言
  optional string head_url = 4;               // 社团团长头像
  optional uint64 uid = 5;                    // 社团团长uid
  optional string nick = 6;                   // 社团团长昵称
}


// 编辑器自定义数据上报 CMD=1002
message AppDataReportReq {
    optional uint64 map_id = 1;              // 地图id
    optional uint64 user_id = 2;             // 用户id
    optional string edu_user_id = 3;         // 用户教育号账号id
    optional string event_id = 4;            // 事件id
    optional string extra_data = 5;          // 自定义字段
}

message AppDataReportRsp {
}

// 获取QQ ARK JSON CMD=1003
message QueryQQArkJsonReq {
    optional int32 ark_type = 1;             // 1：前端静态 2：后端静态
    optional string scene_id = 2;            // 绿洲申请场景id
    map<string, string> static_params = 3;   // 扩展字段
    optional string to_openid = 4;           // 目标gopenid
}

message QueryQQArkJsonRsp {
    optional int32 ret = 1;
    optional string msg = 2;
    optional string data = 3;
}

// 分享数据上报 CMD=1101
message ShareFlowReq {
    optional int32 share_id = 1;         // 分享场景id
    optional int32 op_type = 2;          // 操作类型，1. 发送消息，2. 组件分享
    optional int32 share_type = 3;       // 分享类型
    optional int32 share_channel = 4;    // 分享渠道，1. QQ好友， 2. QQ空间， 3. 微信好友， 4. 微信朋友圈
    optional int32 is_success = 5;       // 是否成功
    optional string extra_json = 6;      // 额外信息
}

message ShareFlowRsp {
}

// QQ音乐登录 CMD=2001
message QQMusicLoginReq {
  optional QQMusicLoginType login_type = 2; // 登录类型

  // code
  optional string code = 1; // 登录态code

  // idtoken
  optional string open_id = 3;
  optional string open_token = 4;
  optional string expire_time = 5;
}

message QQMusicLoginRsp {
  optional QQMusicUserInfo user_info = 1; // 用户信息
  optional string open_id = 2;
  optional string open_token = 3;
}

// QQ音乐退出登录 CMD=2002
message QQMusicLogoutReq {
}

message QQMusicLogoutRsp {}

// QQ音乐检查授权状态 CMD=2003
message QQMusicCheckLoginReq {
}

message QQMusicCheckLoginRsp {
  optional bool is_login = 1; // 是否已登录

  optional string qqmusic_key = 2;
  repeated Song default_songs = 4; // 大厅场景默认播放的歌曲列表
  optional QQMusicUserInfo user_info = 5; // 用户信息
  repeated Song ugc_default_songs = 6; // UGC场景默认播放的歌曲列表
  optional string open_id = 7;
  optional string open_token = 8;
  optional string vip_url = 9; // 开通VIP的链接
}

// QQ音乐获取用户开通绿钻的状态 CMD=2004
message QQMusicGetUserVIPFlagStatusReq {
}

message QQMusicGetUserVIPFlagStatusRsp {
  optional int32 green_vip_flag = 1; // 绿钻标识，1:是 0:否
  optional uint64 green_vip_end_time = 2; // 绿钻结束时间戳（单位：秒）
  optional int32 vip_type = 3; // 0-非会员；1-绿钻；2-超级会员
}

// QQ音乐批量获取歌曲详情 CMD=2005
message QQMusicBatchGetSongDetailReq {
  message Extra {
    optional bool need_replace_production_music = 1; // 是否需要替换成默认的罐头音乐
    optional int64 ugc_id = 2; // 已发布的UGC地图ID。有白名单的地图不替换罐头音乐
  }

  repeated string song_ids = 1; // 歌曲ID列表
  optional ESongSource source = 2; // QQ音乐 or UGC音乐
  optional Extra extra = 3;
}

message QQMusicBatchGetSongDetailRsp {
  repeated Song songs = 1; // 歌曲详情列表
}

// QQ音乐获取某个歌单的所有歌曲 CMD=2006
message QQMusicGetSongsInSonglistReq {
  optional string songlist_id = 1; // 歌单ID
  optional uint32 offset = 2; // 偏移量，默认为0
  optional uint32 limit = 3;  // 列表数量，默认为10
  optional ESongSource source = 4; // QQ音乐 or UGC音乐
}

message QQMusicGetSongsInSonglistRsp {
  repeated Song songs = 1; // 歌曲列表，其中不包括播放链接、图片链接等
  optional uint32 offset = 2; // 偏移量
  optional bool has_more = 3; // 是否还有更多
}

// QQ音乐搜索歌曲 CMD=2007
message QQMusicSearchSongReq {
  optional string key_word = 1; // 搜索关键字
  optional uint32 offset = 2; // 偏移量，默认为0
  optional uint32 limit = 3;  // 列表数量，默认为10
  optional ESongSource source = 4; // QQ音乐 or UGC音乐
}

message QQMusicSearchSongRsp {
  repeated Song songs = 1; // 歌曲信息
  optional uint32 offset = 2; // 偏移量
  optional bool has_more = 3; // 是否还有更多
}

// QQ音乐添加歌曲到QQ音乐歌单 CMD=2008
message QQMusicAddSongToSonglistReq {
  optional string songlist_id = 1; // 歌单ID
  optional string song_id = 2; // 歌曲ID
}

message QQMusicAddSongToSonglistRsp {
  optional string songlist_id = 1; // 歌单ID
}

// 从QQ音乐歌单删除歌曲 CMD=2009
message QQMusicRemoveSongFromSonglistReq {
  optional string songlist_id = 1; // 歌单ID
  optional string song_id = 2; // 歌曲ID
}

message QQMusicRemoveSongFromSonglistRsp {
  optional string songlist_id = 1; // 歌单ID
}

// QQ音乐上报听歌流水 CMD=2010
message QQMusicReportListenReq {
  optional uint32 media_type = 1;   // 多媒体类型，取值 5:直播,4:伴奏,3:有声音频,2:mv,1:歌曲
  optional string media_id = 2;     // 媒体ID
  optional uint32 play_time = 3;    // 歌曲播放时长，单位：秒
  optional uint32 play_quality = 4; // 多媒体播放品质，取值：2:SQ,1:HQ,0:standard
  optional uint32 from = 5;         // 多媒体播放来源，取值 7:搜索，6:语音点播，5:专辑，4:电台，3:排行榜，2:歌单，1:我喜欢
  optional ESongSource source = 6;  // QQ音乐 or UGC音乐
  optional string device_id = 7;    // 设备ID
}

message QQMusicReportListenRsp {}

// QQ音乐获取歌单广场歌单 CMD=2011
message QQMusicGetSonglistSquareReq {
  message Extra {
    optional int32 need_block_qqmusic_songlists = 1; // 是否需要屏蔽QQ音乐的歌单。1：需要屏蔽，2：不屏蔽。0/不传：按照场景判断
  }

  optional ESongSource source = 1; // 大厅/ugc
  optional Extra extra = 2;
}

message QQMusicGetSonglistSquareRsp {
  repeated Songlist songlists = 1;
}

// QQ音乐获取最近播放 CMD=2012
message QQMusicGetRecentlyPlayMusicReq {
  optional uint32 offset = 1; // 偏移量，默认为0
  optional uint32 limit = 2;  // 列表数量，默认为10
}

message QQMusicGetRecentlyPlayMusicRsp {
  repeated Song songs = 1; // 歌曲列表
  optional uint32 offset = 2; // 偏移量
  optional bool has_more = 3; // 是否还有更多
}

// QQ音乐个人电台 CMD=2013
message QQMusicGetIndividualRadioReq {
}

message QQMusicGetIndividualRadioRsp {
  repeated Song songs = 1;
}

// UGC音乐云获取曲库 CMD=2101
message CloudMusicGetSongsReq {
  optional uint32 offset = 1; // 偏移量，默认为0
  optional uint32 limit = 2;  // 列表数量，默认为10
}

message CloudMusicGetSongsRsp {
  repeated Song songs = 1; // 歌曲信息
  optional uint32 offset = 2; // 偏移量
  optional bool has_more = 3; // 是否还有更多
}

// UGC音乐云获取歌曲详情 CMD=2102
message CloudMusicBatchGetSongDetailReq {
  repeated string song_ids = 1; // 歌曲ID列表
}

message CloudMusicBatchGetSongDetailRsp {
  repeated Song songs = 1; // 歌曲详情列表
}

// UGC音乐云获取歌曲播放链接 CMD=2103
message CloudMusicGetSongUrlReq {
  optional string song_id = 1; // 歌曲ID
}

message CloudMusicGetSongUrlRsp {
  optional string song_url = 1; // 歌曲URL
}

// UGC音乐云搜索歌曲 CMD=2104
message CloudMusicSearchSongReq {
  optional string key_word = 1; // 搜索关键字
  optional uint32 offset = 2; // 偏移量，默认为0
  optional uint32 limit = 3;  // 列表数量，默认为10
}

message CloudMusicSearchSongRsp {
  repeated Song songs = 1; // 歌曲信息
  optional uint32 offset = 2; // 偏移量
  optional bool has_more = 3; // 是否还有更多
}


// QQ音乐登录授权方式枚举值
enum QQMusicLoginType {
  EQQMusicLoginTypeCode = 0;
  EQQMusicLoginTypeIDToken = 1;
}

enum ESongSource {
  E_SONG_SOURCE_QQMUSIC = 0; // QQ音乐
  E_SONG_SOURCE_CLOUD_MUSIC = 1; // UGC音乐云
  E_SONG_SOURCE_HOME = 2; // 家园
  E_SONG_SOURCE_JS = 3; // 竞速玩法
}

enum ESongFrom {
  E_SONG_FROM_QQMUSIC = 0; // QQ音乐
  E_SONG_FROM_PRODUCTION_MUSIC = 1; // 罐头音乐
}

message QQMusicUserInfo {
  optional string head_url = 1; // 头像
  optional string nickname = 2; // 昵称
}

// 用户歌单
message Songlist {
  optional string id = 1; // 歌单ID
  optional string title = 2; // 歌单title
  optional string pic = 3; // 歌单图片
  optional string creator_name = 6; // 创作者名称
  optional string creator_pic = 7;  // 创作者图片
}

// 歌曲信息
message Song {
  optional string id = 1;                // 歌曲ID
  optional string name = 2;              // 歌曲名
  optional string title = 3;             // 歌曲标题
  optional int32 vip = 5;                // 是否为绿钻歌曲
  optional Singer singer = 6;            // 歌手信息
  optional Album album = 7;              // 专辑信息
  optional string play_url = 8;          // 流畅品质流媒体url
  optional uint32 play_time = 12;        // 播放总时间
  optional int32 playable = 13;          // 1：表示能播放，0：表示不能播放
  optional int32 hot = 16;              // 是否喜欢 1：喜欢，0：不喜欢
  optional ESongSource source = 17;     // 音乐来源
  optional int32 from = 18;             // 音乐上传来源 ESongFrom
  optional string report_id = 19;       // 上报id
}

// 歌手信息
message Singer {
  optional string name = 2; // 歌手名
  optional string pic = 3; // 歌手图片
}

// 专辑信息
message Album {
  optional string name = 2; // 专辑名
  optional string pic = 3; // 专辑图片
}

enum UGCKvType {
  UGCNone = 0;
  UGCActivity = 1; // ugc活动中心
  UGCDomainWhiteList = 2; // 域名白名单
  UGCH5Url = 3; // h5域名
  EnvName = 4; // envname
  RecommendMode = 5; // 推荐模式
  Iq = 6; // 脑力达人
  QQMiniGame = 7; // QQ小游戏
  IdipArea = 8; // idip area
}

// 配置信息
message MsgKvItem {
  optional int32 type = 1;
  optional string value = 2;
}

message GetPlatConfigReq {
  optional int32 type = 1; //
  optional int32 source = 2;// 0 端 1 云端
  optional string version = 3; // 客户端版本号
}

message GetPlatConfigRsp {
  repeated MsgKvItem list = 1;
}

// ======== 腾讯视频 =========
// 登录 CMD=2501
message TXVideoLoginReq {
  optional string open_id = 1;
  optional string access_token = 2;
  optional uint32 channel_type = 3; // 0-QQ; 1-微信; 2-客户端授权
  optional string guid = 4;
  optional string client_auth_info = 5; // 客户端授权信息
}

message TXVideoLoginRsp {
  optional TXVideoUserInfo user_info = 1; // 用户信息
  optional TXVideoLoginInfo login_info = 2; // 登录态信息
}

// 登出 CMD=2502
message TXVideoLogoutReq {
  optional string guid = 1;
}

message TXVideoLogoutRsp {}

// 检查登录 CMD=2503
message TXVideoCheckLoginReq {
  optional string open_id = 1;
  optional string access_token = 2;
  optional uint32 channel_type = 3; // 0-QQ; 1-微信
  optional string guid = 4;
}

message TXVideoCheckLoginRsp {
  optional bool is_login = 1; // true-已登录
  optional TXVideoUserInfo user_info = 2; // 用户信息
  optional TXVideoLoginInfo login_info = 3; // 登录态信息
}

// 获取腾讯视频会员状态 CMD=2504
message TXVideoGetUserVIPStatusReq {}

message TXVideoGetUserVIPStatusRsp {
  optional int32 vip_flag = 1; // 1-是会员
}

// 获取大分类下的二级分类列表 CMD=2505
message TXVideoGetTypesReq {
  optional uint32 ctype = 1; // 大分类
}

message TXVideoGetTypesRsp {
  repeated TXVideoCSecondaryTypeInfo secondary_type_list = 1; // 次级分类列表
  repeated TXVideoQueryTypeInfo query_type_list = 2; // 查询类型列表
}

message TXVideoQueryTypeInfo {
  optional uint32 id = 1; // 0-推荐；1-最新；2-好评；3-免费
  optional string name = 2;
}

// 获取专辑列表 CMD=2506
message TXVideoGetCListReq {
  optional uint32 ctype = 1;          // 大分类。0-推荐
  optional uint32 query_type = 2;     // 0-推荐；1-最新；2-好评
  optional uint32 secondary_type = 3; // 次级分类ID。0-全部；1-其他

  optional uint32 limit = 4;          // 数量
  optional uint32 offset = 5;         // 偏移量

  optional bool check_live = 6; // 是否仅拉取直播中状态
}

message TXVideoGetCListRsp {
  repeated TXVideoCInfo c_list = 1;
  optional uint32 offset = 2; // 当前偏移量
  optional bool has_live = 3; // 是否显示直播中
}

// 获取专辑详情 CMD=2507
message TXVideoGetCDetailReq {
  optional string cid = 1;
  optional uint32 limit = 2;
  optional uint32 offset = 3;
}

message TXVideoGetCDetailRsp {
  optional TXVideoCInfo c_info = 1;
  repeated TXVideoVInfo v_list = 2;
}

// 搜索专辑 CMD=2508
message TXVideoSearchCReq {
  optional string key_word = 1; // 搜索词
  optional uint32 offset = 2; // 偏移量，默认为0
  optional uint32 limit = 3;  // 列表数量，默认为10
}

message TXVideoSearchCRsp {
  repeated TXVideoCInfo c_list = 1; // 搜索结果
  optional uint32 offset = 2; // 偏移量，默认为0
  optional uint32 total_num = 3; // 总数
}

// 用户信息
message TXVideoUserInfo {
  optional string head_url = 1; // 头像
  optional string nickname = 2; // 昵称
  optional uint32 vip_flag = 3; // 1-是会员
  optional uint32 user_channel_type = 4; // 0-未知；1-QQ；2-微信
  optional bool is_game_account = 5; // 是否为游戏账号对应的腾讯视频账号
  optional string video_openid = 6; // 腾讯视频账号open id
  optional string video_appid = 7; // 腾讯视频在手Q或微信的appid
}

message TXVideoCSecondaryTypeInfo {
  optional uint32 id = 1;
  optional string name = 2;
}

// 腾讯视频登录态
message TXVideoLoginInfo {
  optional int64 video_userid = 1; // 腾讯视频登录态ID
  optional string video_session_key = 2; // 腾讯视频登录态串
  optional uint32 expire_time = 3; // 票据过期时间（秒）
}

// 媒资专辑信息
message TXVideoCInfo {
  optional string cid = 1; // 专辑ID
  optional string cover_url = 2; // 封面URL
  optional string name = 3; // 名称
  optional string desc = 4; // 精彩看点推荐语
  optional uint32 ctype = 5; // 媒资一级分类
  optional uint32 vip_status = 6; // VIP状态。0-免费观看；1-VIP
  optional string episode_updated = 7; // 剧集更新状态（更新至xx集/全xx集）
  optional uint32 v_num = 8; // 视频列表长度
  optional TXVideoVInfo default_v_info = 9; // 默认播放的视频信息
  optional uint32 resource_type = 10; // 0-腾讯视频专辑；1-自定义节目单
  optional uint32 private_free_status = 11; // 是否限免状态。0-非限免；1-限免

  optional uint32 live_status = 100; // 0-非直播；1-直播中
  optional int64 live_end_time = 101; // 直播结束时间戳
}

enum EVideoResourceType {
  EVideoResourceTypeTXVideo = 0; // 腾讯视频
  EVideoResourceTypeCDN = 1;     // CDN地址
  EVideoResourceTypeRTMP = 2;    // RTMP直播
  EVideoResourceTypePic = 3;     // 单图
  EVideoResourceTypePicList = 4; // 多图轮播
}

// 视频信息
message TXVideoVInfo {
  optional uint32 resource_type = 10;   //EVideoResourceType

  optional string vid = 1; // 视频ID (for EVideoResourceTypeTXVideo)
  optional string cid = 2; // (for EVideoResourceTypeTXVideo)
  optional string name = 3; // 视频名称
  optional string desc = 4; // 视频内容简单提示文字
  optional uint32 play_time = 5; // 视频时长
  optional uint32 ctype = 6; // 媒资一级分类 (for EVideoResourceTypeTXVideo)
  optional uint32 episode = 7; // 视频集数
  optional uint32 playable = 8; // 1：表示能播放，0：表示不能播放
  optional uint32 vip_status = 9; // VIP状态。0-免费；1-VIP (for EVideoResourceTypeTXVideo)
  optional uint32 video_category = 11; // 0-正片；1-预告
  optional uint32 private_free_status = 12; // 是否限免状态。0-非限免；1-限免
  optional uint32 pay_status = 13; // 付费状态

  optional string cdn_address = 101; // CDN地址 (for EVideoResourceTypeCDN)
  optional uint32 frame = 102; // 帧率
  repeated ClarityConf clarity_conf = 103; // 多清晰度列表配置

  repeated CDNAddress cdn_address_list = 201; // 单图cdn地址 / 多图轮播，图片地址列表
  optional uint32 switch_time = 202; // 多图轮播停留时间（秒）
  optional uint32 switch_speed = 203; // 多图轮播速度（秒）
}

message ClarityConf {
  optional string clarity = 1; // 清晰度外显 480P/720P/1080P等
  optional CDNAddress address = 2; // 视频地址
  optional bool is_default = 3; // 是否为默认配置
  repeated int32 default_device_levels = 4 [packed=true]; // 默认的机型档位列表
  repeated int32 support_device_levels = 5 [packed=true]; // 支持的机型档位列表
}

message CDNAddress {
  optional string cdn_url = 1;
  optional string filename = 2;
  optional string dir = 3;
  optional uint32 frame = 4; // 视频帧率
}

enum EQueryLiveStreamFromScene {
  EQueryLiveStreamFromSceneLobby = 0; // 大厅
}

message QueryLiveStreamReq {
  optional uint32 from_scene = 1;  // 来源场景id EQueryLiveStreamFromScene
  repeated string screen_id = 2;   // 屏幕id，为空获取全部
}

message QueryLiveStreamRsp {
  repeated ScreenLiveInfo screen_live_list = 1; // 播放屏幕列表
}

message ScreenLiveInfo {
  optional string screen_id = 1;                            // 屏幕ID
  repeated LiveProgramInfo live_program_list = 2;  // 节目单列表
  optional TXVideoVInfo default_v_info = 3; // 当无节目在播时，默认播放的内容
  optional string settings = 4; // 大屏设置。json string
}

message LiveProgramInfo {
  optional TXVideoCInfo c_info = 1;          // 节目单信息
  repeated TXVideoVInfo v_list = 2; // 节目列表
  optional LiveProgramPlaySettings play_settings = 3;  // 播放设置
}

enum ELiveProgramPlayMode {
  ELiveProgramPlayModeOnce = 0; // 列表顺序播放一次
  ELiveProgramPlayModeLoop = 1; // 列表循环
}

message LiveProgramPlaySettings {
  optional uint64 begin_at = 1; // 开始播放时间
  optional uint64 end_at = 2;   // 结束播放时间
  optional uint32 play_mode = 3; // 播放模式 ELiveProgramPlayMode
  optional uint32 interval = 4; // 单个视频播放间隔（秒）
}

// ======== 元梦学院 =========

// 教程

message Tutorial {
  // 教程 ID
  optional string id = 1;
  // 教程标题
  optional string title = 2;
  // 教程类型id
  optional string type_id = 3;
  // 教程类型名称 如官方教程、大神教程等
  optional string type_name = 4;
  // 教程标签id
  optional string tag_id = 5;
  // 教程标签名称，如入门课程、进阶课程等
  optional string tag_name = 6;
  // 教程封面图 URL
  optional string cover_url = 7;
  // 0-视频类型; 1-图文类型
  optional int32 content_type = 8;
  // 教程内容
  optional string content = 9;
  // 视频地址
  optional string video_url = 10;
  // 视频时长
  optional uint32 video_duration = 11;
  // 发布时间
  optional int64 publish_time = 12;
  // 运营数据信息
  optional OperationalData op_data = 13;
  // 教程作者信息
  optional AuthorInfo author_info = 14;
  // 教程简介
  optional string introduction = 15;
  // 边学边练地图模板列表
  repeated EDUMapTemplate map_template_list = 16;
}

// 运营数据信息
message OperationalData {
  // 点击量
  optional string click_count = 1;
  // 点赞数
  optional string like_count = 2;
  // 收藏数
  optional string collect_count = 3;
  // 分享数
  optional string share_count = 4;
  // 是否置顶
  optional bool is_top = 5;
  // 是否为热门
  optional bool is_hot = 6;
  // 是否为新发布
  optional bool is_new = 7;
  // 是否已点赞
  optional bool has_liked = 8;
  // 是否已收藏
  optional bool has_collected = 9;
  // 是否对我没用
  optional bool is_useless = 10;
  // 学习状态。0-不展示；1-未学完；2-学习中；3-完成学习 EUserTutorialStudyStatus
  optional int32 study_status = 11;
  // 已学习时长(秒)，当study_status=EUserTutorialStudyStatusStudying时返回
  optional int32 study_duration = 12;
  // 独立APP主题课下的该教程学习状态
  optional int32 edu_course_study_status = 13;
  // 独立APP主题课下的该教程已学习时长(秒)
  optional int32 edu_course_study_duration = 14;
}

enum EUserTutorialStudyStatus {
  EUserTutorialStudyStatusNone = 0;
  EUserTutorialStudyStatusNotBegin = 1;
  EUserTutorialStudyStatusStudying = 2;
  EUserTutorialStudyStatusFinished = 3;
}

// 教程作者信息
message AuthorInfo {
  // 作者ID
  optional string id = 1;
  // 作者昵称
  optional string nickname = 2;
  // 作者头像
  optional string avatar_url = 3;
}

// YMXYGetTutorial CMD=3001
message YMXYGetTutorialReq {
  // 教程ID
  optional string id = 1;
  optional string language = 2;
}

message YMXYGetTutorialRsp {
  // 教程信息
  optional Tutorial tutorial_info = 1;
}

// YMXYGetTutorials CMD=3002
message YMXYGetTutorialsReq {
  // 标签id
  optional string tag_id = 1;
  // 类型id
  optional string type_id = 2;
  // 分页大小，每页最多返回的记录数
  optional int32 page_size = 3;
  // 当前页码，从1开始计数
  optional int32 current_page = 4;

  optional string language = 5;
}

message YMXYGetTutorialsRsp {
  repeated TutorialTagGroup tag_group = 1;
}

message TutorialTagGroup {
  optional string tag_id = 1;
  optional string tag_name = 2;
  repeated TutorialTypeGroup type_group = 3;
}

message TutorialTypeGroup {
  optional string type_id = 1;
  optional string type_name = 2;
  repeated Tutorial tutorial_list = 3;
  // 总记录数
  optional int32 total = 4;
  // 分页大小
  optional int32 page_size = 5;
  // 当前页码
  optional int32 current_page = 6;
  // 是否还有下一页
  optional bool is_finished = 7;
}

// YMXYGetCategoryTutorials CMD=3003
message YMXYGetCategoryTutorialsReq {
  // 标签id
  optional string tag_id = 1;
  // 类型id
  optional string type_id = 2;
  // 分页大小，每页最多返回的记录数
  optional int32 page_size = 3;
  // 当前页码，从1开始计数
  optional int32 current_page = 4;

  optional string language = 5;
}

message YMXYGetCategoryTutorialsRsp {
  optional TutorialTypeGroup group = 1;
}

// 获取推荐视频 YMXYGetRecommendTutorials CMD=3004
message YMXYGetRecommendTutorialsReq {
  optional string language = 1;
}

message YMXYGetRecommendTutorialsRsp {
  repeated Tutorial recommend_tutorial_list = 1;
}

// 观看教程 YMXYViewTutorial CMD=3005
message YMXYViewTutorialReq {
  // 教程ID
  optional string id = 1;
}

message YMXYViewTutorialRsp {
  optional string cnt = 1;
}

// YMXYGetViewTutorialList CMD=3006
message YMXYGetViewTutorialListReq {
  // 分页大小，每页最多返回的记录数
  optional int32 page_size = 1;
  // 当前页码，从1开始计数
  optional int32 current_page = 2;

  optional string language = 3;
}

message YMXYGetViewTutorialListRsp {
  // 教程列表
  repeated Tutorial tutorial_list = 1;
  // 总数
  optional int32 total = 2;
  // 是否还有下一页
  optional bool is_finished = 3;
}

// 点赞教程 YMXYLikeTutorial CMD=3007
message YMXYLikeTutorialReq {
  // 教程ID
  optional string id = 1;
  // false-点赞；true-取消点赞
  optional bool is_cancel = 2;
}

message YMXYLikeTutorialRsp {
  optional string cnt = 1;
}

// YMXYGetLikeTutorialList CMD=3008
message YMXYGetLikeTutorialListReq {
  // 分页大小，每页最多返回的记录数
  optional int32 page_size = 1;
  // 当前页码，从1开始计数
  optional int32 current_page = 2;

  optional string language = 3;
}

message YMXYGetLikeTutorialListRsp {
  // 教程列表
  repeated Tutorial tutorial_list = 1;
  // 总数
  optional int32 total = 2;
  // 是否还有下一页
  optional bool is_finished = 3;
}

// 收藏教程 YMXYCollectTutorial CMD=3009
message YMXYCollectTutorialReq {
  // 教程ID
  optional string id = 1;
  // false-收藏；true-取消收藏
  optional bool is_cancel = 2;
}

message YMXYCollectTutorialRsp {}

// 对我没用 教程 YMXYUselessTutorial CMD=3010
message YMXYUselessTutorialReq {
  // 教程ID
  optional string id = 1;
  // false-点击对我没用；true-取消对我没用
  optional bool is_cancel = 2;
}

message YMXYUselessTutorialRsp {}

// YMXYSearch CMD=3011
message YMXYSearchReq {
  // 搜索词
  optional string key_text = 1;
  // 不填则所有模块组都搜
  optional string module_group_name = 2;
  // 分页大小，每页最多返回的记录数
  optional int32 page_size = 3;
  // 当前页码，从1开始计数
  optional int32 current_page = 4;

  optional string language = 5;
}

message YMXYSearchRsp {
  optional string toast = 1; // 元元为你找到以下搜索结果
  repeated IndexModule module_list = 2;
}

// YMXYGetSearchSuggestions CMD=3012
message YMXYGetSearchSuggestionsReq {
  optional string input = 1;
}

message YMXYGetSearchSuggestionsRsp {
  repeated string suggestions = 1;
}

// 获取工坊指南 CMD=3013
message YMXYGetGuideReq {}

message YMXYGetGuideRsp {
  optional bool need_guide = 1;
  repeated string guide_image_list = 2;
}

// 获取创作者协议 CMD=3014
message YMXYGetDeveloperProtocolReq {
  optional string language = 1;
}

message YMXYGetDeveloperProtocolRsp {
  optional string content = 1;
  optional string version = 2;
}


// 结束观看教程 CMD=3015
message YMXYViewTutorialFinishReq {
  optional string id = 1; // 教程ID
  optional int32 duration = 2; // 学习教程的时长（单位：秒）
  optional string edu_course_id = 3; // 主题课ID
}

message YMXYViewTutorialFinishRsp {}

// 获取造梦系列课 CMD=3016
message YMXYGetCourseTutorialsReq {
  optional string course_id = 1; // 课程ID，不填则后台返回默认课程
  optional int32 page_size = 2;  // 分页大小，每页最多返回的记录数
  optional int32 current_page = 3;  // 当前页码，从1开始计数
}

message YMXYGetCourseTutorialsRsp {
  optional string course_id = 1; // 课程ID
  optional int32 page_size = 2;
  optional int32 current_page = 3;
  optional bool is_finished = 4;  // 是否还有下一页

  optional string course_name = 11; // 课程名
  optional string course_cover = 12; // 课程封面

  optional string course_study_user_cnt = 21; // 已有多少人参与学习

  repeated Tutorial tutorial_list = 101; // 教程列表（当前页）
  optional int32 tutorial_total = 102; // 课程下所有教程的数量
  optional int32 study_tutorial_cnt = 103; // 课程下已学完的教程的数量
  optional Tutorial next_tutorial = 104; // 开始学习：下一节教程

  message CourseBase {
    optional string course_id = 1;
    optional string course_name = 2;
  }

  repeated CourseBase all_course_base_list = 201; // 所有的课程列表，当输入的course_id为空时返回
}

message IndexModule {
  // 模块组名称：官方教程/游戏地图/活动
  optional string name = 1;
  // 模块样式类型：0-横划卡
  optional int32 type = 2;
  // 模块列表
  repeated IndexModuleDetail detail_list = 3;
  // 总记录数
  optional int32 total = 4;
  // 是否还有下一页
  optional bool is_finished = 5;
}

enum IndexModuleDetailType {
  TUTORIAL_TYPE = 0;
  GAMEMAP_TYPE = 1;
  // ACTIVITY_TYPE = 2;
}

message IndexModuleDetail {
  // 属于哪个模块组
  optional IndexModuleDetailType type = 1;
  // 教程信息
  optional Tutorial tutorial = 2;
  // 活动信息
  // Activity activity = 3;
}


message OMIncomeIndexReq{
}

message OMIncome {
  optional string name = 1; // 地图 or 组合名称
  repeated string tags = 2; // 标签列表
  repeated string datas = 3; // 从左到右，游玩、收藏、点赞
  optional uint32 type = 4; // 0 地图 1 组合（暂不支持）
  optional string cover = 5; // 封面
  optional uint32 amount = 6; // 交易金额
}

message OMIncomeIndexRsp{
  repeated OMIncome list = 1;
  repeated uint32 amount_list = 2;    // 从左到右，可提现、新增收益、累积收益
}

message OMBindAccountReq {
  optional string id_card = 1; // 身份证
  optional string name = 2; // 姓名
  optional uint32 type = 3; // 渠道 1为银行卡，2为微信支付, 3为QQ钱包
  optional OMBank bank_info = 4; // 银行数据
}

message OMBank {
  optional string name = 1; // 银行名称
  optional string account_num = 2; // 账号
  optional string province = 3; // 银行所在省份
  optional string city = 4; // 银行所在市
  optional string icon = 5; // icon
}

message OMBindAccountRsp {
}


message OMWithdrawReq {
  optional uint32 amount = 1; // 提现金额
}

message OMWithdrawRsp {
}

message OMGetTransactionListReq {
  optional string page_info = 1;
  optional int32 type = 2; // 0 全部 1 发放 -1 提现
}


message OMTransaction {
  optional int32 type = 1;  // 1 发放 -1 提现
  optional string id = 2; // 交易id
  optional uint32 amount = 3; // 交易金额
  optional string create_time = 4; // 创建时间
  optional string name = 5; // 项目名称
  optional uint32 status = 6; // 交易状态 0 提现中 1 提现成功 2 提现失败 3 退款中 4 退款失败
}


message GetTransactionListRsp {
  repeated OMTransaction list = 1; // 列表数据
  optional string page_info = 2; // 翻页数据
}


message OMGetBankListReq {
  // optional string keyword = 1;// 银行昵称
}


message OMGetBankListRsp {
  repeated OMBank list = 1; // 这里不返回省市
}

message OMGetProvinceListReq {

}

message OMProvince {
  optional string name = 1;
  repeated string city_list = 2;
}

message OMGetProvinceListRsp {
  repeated OMProvince list = 1; // 省市列表
}


message OMGetBindAccountReq {
}

message OMGetBindAccountRsp {
  optional uint32 type = 1; // 渠道 1为银行卡，2为微信支付, 3为QQ钱包
  optional OMBank bank_info = 2; // 银行数据
  repeated uint32 support_type = 3; // 支持绑定的渠道
  optional uint32 bind_status = 4; // 绑定状态 0 审核中 1 审核通过
}


message OMGetWithdrawInfoReq {
}

message OMGetWithdrawInfoRsp {
  optional uint32 amount = 1;    // 可提现金额
  optional uint32 max_amount = 2;    // 最大提现金额
  optional uint32 min_amount = 3;    // 最小提现金额
  optional uint32 frequency = 4; // 可提现次数
}


message AgreementReq {
  optional string activity_id = 1;// 一期可以不填
}

message AgreementRsp {
}


// ======== 元元助手 ========

// 安全检查 CMD=4001
message YYChatCheckTextReq {
  optional string content = 1;
}

message YYChatCheckTextRsp {
  optional string content = 1;
}

// 聊天 CMD=4002
message YYChatReq {
  optional string content = 1;
}

message YYChatRsp {
  repeated YYChatMsg msg_list = 1;
}

enum EYYChatMsgType {
  EYYChatMsgTypeText = 0;         // 文本
  EYYChatMsgTypeTutorial = 1;     // 教程
  EYYChatMsgTypeMap = 2;          // 地图
  EYYChatMsgTypeMapTemplate = 3;  // 灵感模板
  EYYChatMsgTypeCreator = 4;      // 创作者
  EYYChatMsgTypeCreatorRank = 5;  // 创作者排行榜
}

enum EYYChatMsgRole {
  EYYChatMsgRoleUser = 0; // 用户
  EYYChatMsgRoleYY = 1; // 元元
}

message YYChatMsg {
  optional uint32 type = 1; // EYYChatMsgType
  optional uint32 role = 2; // EYYChatMsgRole
  optional int64 timestamp = 3; // 时间戳

  // 消息内容
  optional string text = 1000;                         // type==0时读取
  repeated Tutorial tutorial_list = 1001;              // type==1时读取
  repeated YYChatMap map_list = 1002;                  // type==2时读取
  repeated YYChatMapTemplate map_template_list = 1003; // type==3时读取
  repeated YYChatCreator creator_list = 1004;          // type==4时读取
  optional YYChatCreatorRank creator_rank = 1005;      // type==5时读取
}

// 地图
message YYChatMap {
  optional string id = 1;                // 地图ID
  optional string title = 2;             // 地图标题
  optional string cover = 3;             // 地图封面
  optional string creator_avatar = 4;    // 地图作者头像
  optional string creator_nickname = 5;  // 地图作者昵称
  optional string play_num = 6;          // 地图游玩次数
  optional string like_num = 7;          // 地图点赞次数
  optional string type_name = 8;         // 地图类型名称（竞速）
  optional string jump_schema = 9;       // 跳转链接
}

// 灵感模板
message YYChatMapTemplate {
}

// 创作者
message YYChatCreator {
  optional string id = 1;          // 创作者ID
  optional string avatar = 2;      // 创作者头像
  optional string nickname = 3;    // 创作者昵称
  optional uint32 gender = 4;      // 0-未设置；1-男；2-女
  optional string jump_schema = 5; // 跳转链接
}

// 排行榜
message YYChatCreatorRank {
  optional string text = 1;                          // 文本
  repeated YYChatCreatorRankLink rank_link_list = 2; // 排行榜链接列表
}

message YYChatCreatorRankLink {
  optional string name = 1;        // 名称
  optional string jump_schema = 2; // 链接
}

// 聊天记录 CMD=4003
message YYChatHistoryReq {
  optional int64 offset_timestamp = 1; // 时间戳偏移量，填0则从最新的开始
}

message YYChatHistoryRsp {
  repeated YYChatMsg msg_list = 1;
  optional int64 offset_timestamp = 2; // 下次请求时传上
  optional bool has_more = 3;          // 是否还有下一页
}



message ChannelGameStartReq {
  optional string uid = 1; // 渠道用户id
  optional string token = 2; // 渠道用户token
  optional string map_id = 3;// 地图id
  optional string ext_info = 4; // 透传参数
  optional string source = 5; // 渠道
}

message ChannelGameStartRsp {
  optional string battle_id = 1; // 对局id
  optional string gopenid = 2; // gopenid
  optional string openid = 3; // openid
  optional string ext_info = 4; // 透传参数
  optional string game_token = 5; // 游戏校验参数
}

enum ChannelGameStatus {
  Finish = 0; // 完成
  NotFinish = 1; // 未完成
}

message ChannelGameBattleInfo {
  optional uint32 status = 1; // 对局状态 ChannelGameStatus
  optional uint32 score = 2; // 分数，当前是完成用时，单位s
}

message ChannelGameEndReq {
  optional string uid = 1; // 渠道用户id
  optional string token = 2; // 渠道用户token
  optional string map_id = 3;// 地图id
  optional string battle_id = 4; // 对局id
  optional string game_token = 5; // 游戏校验参数
  optional ChannelGameBattleInfo battle_info = 6; // 对局数据 json串，游戏定义
}

message ChannelGameEndRsp {
}

message DCIndexReq {

}


message DCMapInfo {
  optional string name = 1; // 地图 or 组合名称
  repeated string tags = 2; // 标签列表
  repeated string datas = 3; // 从左到右，游玩、收藏、点赞
  optional uint32 type = 4; // 地图类型
  optional string cover = 5; // 封面
  optional string publish_time = 6; // 发布时间
  optional string map_id = 7; // 地图id
  optional string desc = 8; // 描述
}

message DCIndexRsp {
  // 累积发布作品数、累计粉丝、累计游玩、累计点赞、累计收藏
  repeated string datas = 1; // 数据列表
  repeated DCMapInfo list = 2; // 地图列表
}

message DCMapDetailReq {
  optional string map_id = 1; // 地图id
}

message DCMapDetailRsp {
  optional DCMapInfo map = 1; // 地图数据
  message BestRecord {
    optional string avatar = 1; // 头像
    optional string nickname = 2; // 昵称
    optional uint32 score = 3;// 记录
    optional uint32 type = 4; // 2 qq 1 wx
  }

  repeated BestRecord bests = 2; // 最佳记录
  repeated string datas = 3;//其他数据 通关率、平均死亡次数、有效游玩人数、失败人数
}

// 小窝推送接口消息类型
enum PlatXiaoWoDataNtfType {
  PLAT_XIAOWO_NTFTYPE_NONE = 0;
  PLAT_XIAOWO_NTFTYPE_ROOMMSG = 1;
}

// ======== 房间协议 ========
// ---------------------------------------- room_common.proto ----------------
// 房间类型
enum PlatRoomType {
  ROOM_TYPE_BASE = 0; // 基础语音房间
  ROOM_TYPE_MEDIA_VOD_STREAM_TXVIDEO = 1;  // 腾讯视频点播房间
  ROOM_TYPE_MEDIA_VOD_STREAM_QQMUSIC = 2;  // QQ音乐点播房间
}

// 房间子场景
enum PlatRoomTypeSubScene {
  ROOM_TYPE_SUB_SCENE_HOME = 0;   // 星家园场景
  ROOM_TYPE_SUB_SCENE_FARM = 1;   // 星农场场景
}

enum RoomSpeakerStatus {
  SPEAKER_STATUS_ON = 0;      // 麦位可用
  SPEAKER_STATUS_CLOSE = 1;   // 麦位关闭
  SPEAKER_STATUS_LOCK = 2;    // 麦位不可用
}

enum RoomSpeakerVoiceStatus {
  SPEAKER_VOICE_STATUS_ON = 0;    // 麦位声音 开
  SPEAKER_VOICE_STATUS_OFF = 1;   // 麦位声音 关
  SPEAKER_VOICE_STATUS_BAN = 2;   // 麦位声音 被禁
}

// 房间状态
enum PlatRoomStatus {
  ROOM_STATUS_CLOSE = 0;
  ROOM_STATUS_PLAYING = 1;
  ROOM_STATUS_DESTROY = 2;
}

enum OutSpeakingReason {
  OUT_SPEAKING_REASON_NONE = 0;           // 没有原因，主动下麦
  OUT_SPEAKING_REASON_KICK_BY_OWNER = 1;  // 被房主踢下麦
  OUT_SPEAKING_REASON_TIMEOUT = 2;        // 超时下麦
}

// 房间消息
enum RoomMsgType {
  ROOM_MSG_TYPE_NONE = 0; // 占位
  ROOM_MSG_TYPE_SYSTEM_TEXT_MSG = 1;    // 房间产生的系统文本消息
}
enum RoomSource {
  ROOM_SOURCE_NONE = 0;         // 占位
  ROOM_SOURCE_ENTER_ROOM = 1;   // 进房请求触发
}
message RoomUser {
  optional uint64 uin = 1;
  optional string open_id = 2;
  optional string name = 3;                // 透传给客户端时 赋值， 用户呢称 需要
  optional string icon = 4;                // 透传给客户端时 赋值， 用户头像
  optional int64 active_time_in_ms = 100;  // 后台字段，暂时不输出客户端
}

// ---------------------------------------- media_stream_room.proto ----------------
enum MediaStreamState {
  MEDIA_STREAM_OFF = 0;   // 关
  MEDIA_STREAM_ON = 1;    // 开
  MEDIA_STREAM_PAUSE = 2; // 暂停状态 --点播模式才会出现
}
enum MediaStreamActionType {
  MEDIA_STREAM_ACTION_PAUSE = 0;
  MEDIA_STREAM_ACTION_CONTINUE = 1;
}
enum MediaStreamSwitchType {
  MEDIA_STREAM_SWITCH_BY_TIMEOUT = 0;
  MEDIA_STREAM_SWITCH_BY_OWNER = 1;
}
message TXVideoVodStreamRoomConf {
}
message QQMusicVodStreamRoomConf {
}
message MediaRoomConf {
  optional TXVideoVodStreamRoomConf txvideo_vod_stream_room_conf = 1;
  optional QQMusicVodStreamRoomConf qqmusic_vod_stream_room_conf = 2;
}
message TXVideoVodStreamRoomDataInfo {
}
message QQMusicVodStreamRoomDataInfo {
}
message MediaRoomDataInfo {
  optional TXVideoVodStreamRoomDataInfo txvideo_vod_stream_room_data_info = 1;
  optional QQMusicVodStreamRoomDataInfo qqmusic_vod_stream_room_data_info = 2;
}
message TXVideoVodStreamInfo {
  optional TXVideoVInfo cur_vid = 1;               // 腾讯视频播放相关信息
  optional TXVideoVInfo next_vid = 2;              // 下一集播放的 腾讯视频播放相关信息; 为空表示没有下一集了
  optional TXVideoCInfo c_info = 3;                // 专辑名称
  optional uint32 stream_state = 5;                // MediaStreamState
  optional uint32 play_time = 4;                   // 已播放时间, 既视频的第N秒
  optional uint32 begin_time = 6;                  // 当前vid的播放时间
  optional uint32 pause_time = 7;                  // 暂停时间
  optional string tv_id = 8;                       // 电视ID
  optional uint32 switch_type = 9;                 // 切换类型 enum MediaStreamSwitchType
}
message QQMusicRoomSongInfo {
  optional string song_id = 1;
}
message QQMusicVodStreamInfo {
  optional Song cur_song = 1;                                  // 音乐信息
  optional uint32 stream_state = 2;                            // MediaStreamState
  optional uint32 play_time = 3;                               // 已播放时间, 既视频的第N秒
  optional uint32 begin_time = 4;                              // 当前vid的播放时间
  optional uint32 pause_time = 5;                               // 暂停时间
  repeated QQMusicRoomSongInfo song_info_list = 6;             // 房间的播放列表
  optional uint32 switch_type = 7;                 // 切换类型 enum MediaStreamSwitchType
}
message MediaRoomRouteInfo {
  optional TXVideoVodStreamInfo txvideo_vod_stream_info = 1;   // ROOM_TYPE_MEDIA_VOD_STREAM_TXVIDEO
  optional QQMusicVodStreamInfo qqmusic_vod_stream_info = 2;   // ROOM_TYPE_MEDIA_VOD_STREAM_QQMUSIC
}
message TXVideoVodStreamOnReq {
  optional string cid = 1;     //专辑id
  optional string vid = 2;     //当前播放的vid
  optional uint32 play_time = 3;   // 播放时间, 已播放时间, 既视频的第N秒
  optional string tv_id = 4;       // 电视ID
}
message QQMusicVodStreamOnReq {
  optional QQMusicRoomSongInfo song_info = 1;     // qq音乐id
}
message MediaStreamOnReq {
  optional TXVideoVodStreamOnReq txvideo_vod_stream_on_req = 1;
  optional QQMusicVodStreamOnReq qqmusic_vod_stream_on_req = 2;
}
message MediaStreamOffReq {
}
message MediaStreamPauseReq {
  optional uint32 type = 1;        // 0暂停, 1继续
  optional uint32 play_time = 2;   // 播放时间, 已播放时间, 既视频的第N秒
}
message MediaStreamDragReq {
  optional uint32 play_time = 1;   // 播放时间, 已播放时间, 既视频的第N秒
}
message QQMusicVodStreamListAddReq {
  repeated QQMusicRoomSongInfo song_info_list = 1;
}
message MediaStreamListAddReq {
  optional QQMusicVodStreamListAddReq qqmusic_vod_stream_list_add_req = 2;
}
message QQMusicVodStreamListDelReq {
  repeated QQMusicRoomSongInfo song_info_list = 1;
}
message MediaStreamListDelReq {
  optional QQMusicVodStreamListDelReq qqmusic_vod_stream_list_del_req = 2;
}
message QQMusicVodStreamListClearReq {
}
message MediaStreamListClearReq {
  optional QQMusicVodStreamListClearReq qqmusic_vod_stream_list_clear_req = 2;
}

// ---------------------------------------- battle_room.proto ----------------
message Battle {
	enum BattleRoomStateType {
    BATTLE_ROOM_STATE_TYPE_WAIT = 0;                // 组队中
    BATTLE_ROOM_STATE_TYPE_MATCHING = 1;            // 匹配中
    BATTLE_ROOM_STATE_TYPE_BATTLE = 2;              // 对局中
    BATTLE_ROOM_STATE_TYPE_DISBAND = 100;           // 解散
	}
	enum BattleRoomType {
    BATTLE_ROOM_TYPE_DEFAULT = 0;
    BATTLE_ROOM_TYPE_CUSTOM = 1;
    BATTLE_ROOM_TYPE_UGC_CUSTOM = 3;
	}
  message Grade {
	  optional uint32 grade_value = 1;
	  optional uint32 grade_star_value = 2;
	  optional string grade_name = 3;
	  optional string grade_icon = 4;
  }
  message GameMode {
	  optional uint32 mode_id = 1;
	  optional string mode_name = 2;
	  optional string mode_img = 3;
	  optional uint32 play_id = 4;
	  optional uint32 play_name = 5;
	  optional string play_img = 6;
  }
  message GameMap {
	  optional uint64 map_id = 1;
  }
  message GameTag {
	  optional uint32 tag_id = 1;
	  optional string tag_name = 2;
  }
  message TeamMember {
	  optional uint64 uid = 1;
	  optional string open_id = 2;
	  optional uint32 login_type = 3;
	  optional uint32 plat_id = 4;
	  optional uint32 idip_area = 5;
	  optional string head_url = 6;
	  optional string nick_name = 7;
	  optional uint32 gender = 8;
	  optional bool is_team_leader = 9;
	  optional uint32 ready_status = 10;
	  optional Grade grade = 11;
	  optional uint32 position = 12;
	  optional string ext_data = 13;
  }
  message TeamMemberList {
	  repeated TeamMember team_member_list = 1;
	  optional uint32 camp_id = 2;
  }
  message RoomInfo {
	  optional uint64 room_id = 1;
	  optional GameMode game_mode = 2;
	  optional GameMap game_map = 3;
	  repeated GameTag tag_list = 4;
	  repeated TeamMemberList team_member_list = 5;
	  optional uint32 max_player_num = 6;
	  optional uint32 min_player_num = 7;
	  optional uint32 max_player_num_per_team = 8;
	  optional uint32 min_player_num_per_team = 9;
	  optional Grade max_grade = 10;
	  optional Grade min_grade = 11;
	  optional uint32 game_status = 12;
	  optional uint32 room_type = 13;
	  optional uint64 seq = 100;
  }
}
message BaseBattleRoomConf {
}
message LiveBattleRoomConf {
}
message BattleRoomConf {
    optional BaseBattleRoomConf base_battle_room_conf = 1;
    optional LiveBattleRoomConf live_battle_room_conf = 2;
}
message BaseBattleRoomDataInfo {
}
message LiveBattleRoomDataInfo {
}
message BattleRoomDataInfo {
    optional BaseBattleRoomDataInfo base_battle_room_data_info = 1;
    optional LiveBattleRoomDataInfo live_battle_room_data_info = 2;
}
message BaseBattleRoomRouteInfo {
  optional Battle.RoomInfo battle_room_info = 1;
	optional Battle.RoomInfo backup_battle_room_info = 2;
}
message LiveBattleRoomRouteInfo {
    optional uint32 status = 1;
    optional string ilink_token = 2;
    optional uint64 uid = 3;
}
message BattleRoomRouteInfo {
    optional BaseBattleRoomRouteInfo base_battle_room_route_info = 1;
    optional LiveBattleRoomRouteInfo live_battle_room_route_info = 2;
}
// ---------------------------------------- room.proto ----------------

message BaseRoomDataInfo {
  optional string room_id = 1;               // 房间id
  optional int32 room_type = 2;              // RoomType
  optional RoomUser create_user = 3;         // 创建人
  optional uint64 create_time = 4;           // 创建时间（ms）
  optional string item_id = 5;               // 关联的物品/商品id
  optional string auto_create_unique_id = 6; // 自动创建的唯一标识
  optional string env = 7;                   // 环境
  optional int32 sub_scene = 8;              // 子场景 PlatRoomTypeSubScene
  optional int32 server_idx = 100;           // 下标。后台字段
}

// 存储一些房间静态信息, 一般无法变更，通常是创建时候需要填写的
message RoomDataInfo {
  optional BaseRoomDataInfo base_room_data_info = 1;
  optional MediaRoomDataInfo media_room_data_info = 2;
  optional BattleRoomDataInfo battle_room_data_info = 3;
}

message BaseRoomRouteInfo {
  optional RoomUser owner_user = 4;  // 当前房主
  optional uint32 status = 5;        // RoomStatus
}

// 存储房间的动态信息，期间会不断更新的数据
message RoomRouteInfo {
  optional BaseRoomRouteInfo base_room_route_info = 1;
  optional MediaRoomRouteInfo media_room_route_info = 2;
  optional BattleRoomRouteInfo battle_room_route_info = 3;
}

message BaseRoomConf {
  optional int32 get_data_pull_time = 1;  // 房间数据轮询时间间隔, 单位秒
  optional int32 heartbeat_timeout = 2;   // 心跳超时时间
  optional int32 max_room_user_size = 3;  // 房间可容纳最大人数
  optional int32 speaker_heartbeat_timeout = 4; // 麦上用户超时时间
  optional int32 check_timeout_time = 5;        // 检查超时的间隔时间
}

// 房间配置信息
message RoomConf {
  optional BaseRoomConf base_room_conf = 1;
  optional MediaRoomConf media_room_conf = 2;
  optional BattleRoomConf battle_room_conf = 3;
}

message RoomDetail {
  optional RoomDataInfo room_data_info = 1;
  optional RoomRouteInfo room_route_info = 2;
  optional RoomConf room_conf = 3;
  optional int32 version = 100;  // 数据版本号，每次更新会自增。
}

message RoomSpeakerBaseDataInfo {
  optional int32 pos = 1;  //  麦位序号
}

// 房间麦位用户数据 (静态)
message RoomSpeakerDataInfo {
  optional RoomSpeakerBaseDataInfo room_speaker_base_data_info = 1;  // 房间麦位用户基础数据
  // optional RoomSpeakerMediaDataInfo room_speaker_media_data_info = 2;    // 视频房间麦位用户数据
}

message RoomSpeakerBaseRouteInfo {
  optional int32 status = 1;        //  麦位状态 RoomSpeakerStatus
  optional int32 voice_status = 2;  //  麦位声音状态  RoomSpeakerVoiceStatus
}

// 房间麦位用户路由信息 (动态)
message RoomSpeakerRouteInfo {
  optional RoomSpeakerBaseRouteInfo room_speaker_base_route_info = 1;  // 房间麦位用户基础数据
  // optional RoomSpeakerMediaRouteInfo room_speaker_media_route_info = 2;    // 视频房间麦位用户数据
}

message RoomSpeakerInfo {
  optional RoomUser user = 1;                                 // 麦位用户
  optional RoomSpeakerDataInfo room_speaker_data_info = 2;    // 用户麦位数据
  optional RoomSpeakerRouteInfo room_speaker_route_info = 3;  // 用户麦位路由信息
}

message RoomSpeakerInfoList {
  repeated RoomSpeakerInfo room_speaker_info_list = 1;
  optional int32 version = 100;  // 数据版本号，每次更新会自增。
}

enum S2CPushRoomMsgType {
  S2C_PUSH_ROOM_MSG_TYPE_NONE = 0;
  S2C_PUSH_ROOM_MSG_TYPE_ROOM_DESTROY = 1;
  S2C_PUSH_ROOM_MSG_TYPE_ROOM_UPDATE = 2;
  S2C_PUSH_ROOM_MSG_TYPE_QQMUSIC_ROOM_CREATE = 3;         // qq音乐房间开播消息，单独给小窝内的所有人员进行推送
}

message S2CPushRoomMsg {
  optional uint32 msg_type = 1;             // S2CPushRoomMsgType
  optional RoomDataInfo room_data_info = 2; // 房间信息，主要是房间id 或者其他必要信息
  optional uint64 push_time_ms = 3;         // 触发推送时间 ms
}

// ---------------------------------------- room_cmd.proto ----------------

message InSpeakingCmdReq {
  optional RoomSpeakerInfo room_speaker_info = 1;
}

message OutSpeakingCmdReq {
  optional RoomSpeakerInfo room_speaker_info = 1;
  optional int32 reason = 2;  // OutSpeakingReason
}

message SwitchVoiceCmdReq {
  optional RoomSpeakerInfo room_speaker_info = 1;
}

// 房间二级命令
enum RoomCmdType {
  ROOM_CMD_TYPE_NONE = 0;            // 占位
  ROOM_CMD_TYPE_IN_SPEAKING = 103;   // 上麦
  ROOM_CMD_TYPE_OUT_SPEAKING = 104;  // 下麦
  ROOM_CMD_TYPE_SWITCH_VOICE = 105;  // 开关麦

  // media room
  ROOM_CMD_TYPE_MEDIA_STREAM_ON = 200;       // 开播
  ROOM_CMD_TYPE_MEDIA_STREAM_OFF = 201;      // 关播
  ROOM_CMD_TYPE_MEDIA_STREAM_PAUSE = 202;    // 暂停
  ROOM_CMD_TYPE_MEDIA_STREAM_DRAG = 203;     // 拖动
  ROOM_CMD_TYPE_MEDIA_STREAM_LIST_ADD = 204;      // 添加媒体资源
  ROOM_CMD_TYPE_MEDIA_STREAM_LIST_DEL = 205;      // 删除媒体资源
  ROOM_CMD_TYPE_MEDIA_STREAM_LIST_CLEAR = 206;    // 媒体列表清空
}

message RoomCmd {
  optional int32 room_cmd_type = 1;                           // RoomCmdType
  optional InSpeakingCmdReq room_in_speaking_cmd = 103;
  optional OutSpeakingCmdReq room_out_speaking_cmd = 104;
  optional SwitchVoiceCmdReq switch_voice_cmd = 105;

  optional MediaStreamOnReq stream_on_cmd = 200;
  optional MediaStreamOffReq stream_off_cmd = 201;
  optional MediaStreamPauseReq stream_pause_cmd = 202;
  optional MediaStreamDragReq stream_drag_cmd = 203;          // ROOM_ACTION_TYPE_MEDIA_STREAM_DRAG
  optional MediaStreamListAddReq stream_list_add_cmd = 204;
  optional MediaStreamListDelReq stream_list_del_cmd = 205;
  optional MediaStreamListClearReq stream_list_clear_cmd = 206;
}

// 房间动作通知，用于数据更新，不可丢失
enum RoomActionType {
  ROOM_ACTION_TYPE_NONE = 0;                         // 占位
  ROOM_ACTION_TYPE_ROOM_ROUTE_UPDATE = 100;          // 房间路由更新
  ROOM_ACTION_TYPE_ROOM_SPEAKER_ROUTE_UPDATE = 101;  // 用户路由更新
  ROOM_ACTION_TYPE_ROOM_DISMISS = 102;               // 房间解散
  ROOM_ACTION_TYPE_IN_SPEAKING = 103;                // 上麦
  ROOM_ACTION_TYPE_OUT_SPEAKING = 104;               // 下麦
  ROOM_ACTION_TYPE_SWITCH_VOICE = 105;               // 开关麦
  ROOM_ACTION_TYPE_MEDIA_STREAM_DRAG = 203;          // 拖动
}

message MediaStreamDragAction {
  optional RoomDetail room_detail = 1;
}

message RoomRouteUpdateAction {
  optional RoomDetail room_detail = 1;
}

message RoomSpeakerRouteUpdateAction {
  optional RoomSpeakerInfoList room_speaker_info_list = 1;
}

message InSpeakingAction {
  optional RoomSpeakerInfoList room_speaker_info_list = 1;
}

message OutSpeakingAction {
  optional RoomSpeakerInfoList room_speaker_info_list = 1;
  optional int32 reason = 2;  // OutSpeakingReason
}

message RoomDismissAction {
  optional RoomDetail room_detail = 1;
}

message SwitchVoiceAction {
  optional RoomSpeakerInfoList room_speaker_info_list = 1;
}

message RoomActionList {
  repeated RoomAction room_action_list = 1;
}

message RoomAction {
  optional uint64 action_id = 1;
  optional int32 version = 2;           // 数据版本号，每次更新会自增。
  optional int32 room_action_type = 3;  // RoomActionType
  optional RoomRouteUpdateAction room_route_update_action = 100;
  optional RoomSpeakerRouteUpdateAction room_speaker_route_update_action = 101;
  optional RoomDismissAction room_dismiss_action = 102;
  optional InSpeakingAction in_speaking_action = 103;
  optional OutSpeakingAction out_speaking_action = 104;
  optional SwitchVoiceAction switch_voice_action = 105;
  optional MediaStreamDragAction media_stream_drag_action = 203;
}

message SystemTextMsg {
  optional string text = 1;
}

message RoomMsg {
  optional uint64 msg_id = 1;
  optional int32 room_msg_type = 2;  // RoomMsgType
  optional SystemTextMsg system_text_msg = 3;
}

message RoomMsgList {
  repeated RoomMsg room_msg_list = 1;
}

// ---------------------------------------- room_server.proto ----------------
// 创建房间
// 创建时候，填 room_type
// 和对应类型房间需要的参数
message CreateRoomReq {
  optional RoomDataInfo room_data_info = 1;
  optional RoomUser user = 2;         // 请求人
  optional RoomUser owner_user = 3;   // 请求发起人和房主不一样，需要特别指定房主的时候使用
}
message CreateRoomRsp {
  optional RoomDetail room_detail = 1;
  optional RoomSpeakerInfoList room_speaker_info_list = 2;
}

// 销毁房间
// 填room_id 就可以，房主才可以发起的操作
message DestroyRoomReq {
  optional RoomDataInfo room_data_info = 1;
  optional RoomUser user = 2;
}
message DestroyRoomRsp {
}

// 加入房间
// 填room_id 就可以
message EnterRoomReq {
  optional RoomDataInfo room_data_info = 1;
  optional RoomUser user = 2;
  optional RoomUser owner_user = 3;   // 用于进房时候，需要自动创建且指定房主的情况
  optional RoomCmd room_cmd = 4;
}
message EnterRoomRsp {
  optional RoomDetail room_detail = 1;
  optional RoomSpeakerInfoList room_speaker_info_list = 2;
  optional uint64 max_action_id = 3;
}

// 退出房间
// 填room_id 就可以
message LeaveRoomReq {
  optional RoomDataInfo room_data_info = 1;
  optional RoomUser user = 2;
}
message LeaveRoomRsp {
}

// 恢复房间
// 填room_id 就可以
message RecoverRoomReq {
  optional RoomDataInfo room_data_info = 1;
  optional RoomUser user = 2;
}
message RecoverRoomRsp {
  optional RoomDetail room_detail = 1;
  optional RoomSpeakerInfoList room_speaker_info_list = 2;
  optional uint64 max_action_id = 3;
}

// 房间命令
message DoRoomCmdReq {
  optional RoomDataInfo room_data_info = 1;
  optional RoomUser user = 2;
  optional RoomCmd room_cmd = 3;
  optional uint32 source = 4;          // RoomSource
}
message DoRoomCmdRsp {
  optional RoomDetail room_detail = 1;
  optional RoomSpeakerInfoList room_speaker_info_list = 2;
  optional uint64 max_action_id = 3;
}

// 定时拉取房间数据/心跳，房间数据轮询时间间隔从进房请求返回的配置获取
message GetRoomDataReq {
  optional RoomDataInfo room_data_info = 1;
  optional RoomUser user = 2;
  optional uint64 cur_action_id = 3;
  optional uint64 cur_msg_id = 4;              // 首页请求不需要填，返回最近N条消息
}
message GetRoomDataRsp {
  optional bool has_more_action = 1;
  optional RoomActionList room_action_list = 2;
  optional bool has_more_msg = 3;
  optional RoomMsgList room_msg_list = 4;
}

// 微信小程序获取对局房间数据
message WxAppGetRoomInfoReq {
  optional string battle_room_id = 1;
  optional string battle_room_type = 2;
  optional string battle_invite_uid = 3;
}
message WxAppGetRoomInfoRsp {
  optional RoomDetail room_detail = 1;
}
// 客户端调用分享通知接口
message ShareBattleRoomInfoReq {
  optional string battle_room_id = 1;
  optional string battle_room_type = 2;
  optional string battle_invite_uid = 3;
}
message ShareBattleRoomInfoRsp {
  optional string activity_id = 1;
  optional string env = 2;
  optional string ext = 3;
  optional int32 idip_area = 4; // 正式环境 idip
}
// livelink 抖音验票接口
message YJKHCheckTicketReq {
  optional string ticket = 1;           // 票据，从抖音侧获取
  optional uint32 user_type = 2;        // 用户类型，从抖音侧获取
  optional string game = 3;             // 游戏代号
  optional string open_id = 4;          // gopenid
  optional string role_id = 5;          // 角色id uid
  optional uint32 partition = 6;        // 元梦没有，不填或者填0
  optional uint32 area = 7;             // idip area
  optional uint32 plat = 8;             // plat id
  // optional uint32 plat_play_info_id = 9; // plat_play_info_id
  optional string plat_play_info_id = 10; // plat_play_info_id
}

message YJKHCheckTicketRsp {
  optional int32 ret_code = 1;         // 抖音错误码， 非0表示错误
  optional string room_extra = 2;      // 主播组队后主动上报，在粉丝验票的时候，会回传，方便游戏把粉丝拉进主播的队伍里
  optional uint32 expire_seconds = 3;  // 抖音票据有效期，单位为秒，默认是14天
}

// 分享

// 分享类型
enum ShareCodeType {
  ShareCodeTypeShareUgc = 0;    // ugc分享
  ShareCodeTypeShareRoom = 1;   // 房间组队分享
  ShareCodeTypeFriendCode = 2;  // 好友码
  ShareCodeTypeGroupCode = 3;   // 社团码
}

// 获取分享口令
message ShareCreateShareCodeReq {
  optional string jump_url = 1;
  optional string redirect_url = 2; // h5跳转链接
  optional uint32 type = 3;
  optional uint32 ttl = 4;  // 废弃
}

message ShareCreateShareCodeRsp {
  optional string share_code = 1;
  optional string share_url = 2;
}

// 解析分享口令
message ShareGetShareCodeInfoReq {
  optional string share_code = 1;
  optional string source = 2;  // 来源
  optional uint32 not_record = 3;  // 0 -> 记录用于分享数据统计; 1 -> 不记录
}

message ShareGetShareCodeInfoRsp {
  optional string jump_url = 1;
  optional string redirect_url = 2; // h5跳转链接
  optional uint32 type = 3;
}

// ark签名
message ShareSignQQShareArkReq {
  optional string access_token = 1;
  optional string ark = 2;
}

message ShareSignQQShareArkRsp {
  optional string sign_ark = 1;
}

// 后台发送ark
message ShareSendQQShareArkReq {
  optional string access_token = 1;
  optional string fopenid = 2;
  optional string sopenid = 3;
  optional string ark = 4;
}

message ShareSendQQShareArkRsp {
  optional uint32 code = 1;
  optional string message = 2;
}

// ark签名-带图片格式
message ShareSignQQPicArkReq {
  optional string access_token = 1;
  optional string title = 2;
  optional string desc = 3;
  optional string image = 4;
  optional string image_jump_url = 5;
  optional string button_text = 6;
  optional string button_url = 7;
  optional string tag = 8;
}

message ShareSignQQPicArkRsp {
  optional string sign_ark = 1;
}

// ark签名并发送组队ark
message ShareSignAndSendQQTeamShareArkReq {
  optional string access_token = 1;
  optional string fopenid = 2;
  optional string sopenid = 3;
  optional string room_id = 4;
  optional string room_type = 5;
  optional string ext_info = 6;
  optional string title = 7;
  optional string cover = 8;
}

message ShareSignAndSendQQTeamShareArkRsp {
  optional string sign_ark = 1;
}

// 获取已注册好友
message ShareGetRegisteredFriendsReq {
  optional string access_token = 1;
}

message ShareRegisteredFriend {
  optional string openid = 1;  // gopenid
  optional string nick_name = 2;
  optional string avatar = 3;
}

message ShareGetRegisteredFriendsRsp {
  repeated ShareRegisteredFriend friend_list = 1;
}

message GetMatchTypeShareInfoReq {
  optional uint32 match_type = 1; // 玩法id
}

message GetMatchTypeShareInfoRsp {
  optional string text = 1; //分享文案
}

// 获取云游地址
message ShareGetCloudGameUrlReq {
  optional string game_data = 1;
}

// 获取云游地址
message ShareGetCloudGameUrlRsp {
  optional string url = 1;
}

message ShareGetSceneGroupInfoReq {
  optional uint32 scene = 1; // 1 -> 星团; 2 -> 粉丝群
  optional int64 id = 2;    // 星团场景 传 社团id; 粉丝群场景 传 创作者id
  optional string name = 3;  // 传群名字 需要保存，后续解绑等场景需要用到
}

message ShareGetSceneGroupInfoRsp {
  optional string group_unionid = 1;
  optional string group_unionname = 2;
  optional string zoneid = 3;
  optional string areaid = 4;
}

message ShareAddQQBotFriendReq {
  optional string access_token = 1;
}

message ShareAddQQBotFriendRsp {
  optional uint32 code = 1;
  optional string message = 2;
}

message ShareIsQQBotFriendReq {
  optional string access_token = 1;
}

message ShareIsQQBotFriendRsp {
  optional bool is_friend = 1;
}

// 校验微信视频号关注订阅状态
message ShareCheckWxSphReserveStatusReq {
  optional string access_token = 1;
}

message ShareCheckWxSphReserveStatusRsp {
  optional int32 code = 1;
  optional string message = 2;
  optional int32 status = 3;  // 0 -> 未关注视频号，未订阅开播通知; 1 -> 已关注视频号，未订阅开播通知; 2 -> 已关注视频号，已订阅开播通知
}

// 校验关注订阅微信视频号
message ShareReserveWxSphReq {
  optional string access_token = 1;
  optional int32 op_type = 2;  // 1 -> 关注并订阅; 2 -> 取消订阅
}

message ShareReserveWxSphRsp {
  optional int32 code = 1;
  optional string message = 2;
}

message GuideGetQQUserProfileReq {
}

message GuideGetQQUserProfileRsp {
  optional uint32 interest = 1;
  optional uint32 gender = 2;
  optional int32 age = 3;
}

message TABGetOpenNoticeConfigReq {
  optional string source = 1;
}

message TABGetOpenNoticeConfigRsp {
  optional bool in_test = 1;
  optional string abtest_info = 2;
}
// 灵感中心

message LGZXGetRecommendMapInspireListReq {
  // 当前页码，从1开始计数
  optional int32 current_page = 1;
}

message LGZXMapInspireMataInfo {
  optional int32 size = 1;
  optional string msg = 2; //md5
  optional int32 msg_type = 3;       // 消息类型, 详情见UgcMapMetaInfoMsgType所示
  optional int32 process_type = 4;   // 处理类型, 详情见枚举UgcMapMetaInfoProcessType所示
}

message LGZXMapInspire {
  optional string id = 1;                  // 地图ID
  optional string title = 2;               // 地图标题
  optional string cover = 3;               // 地图封面
  optional string creator_avatar = 4;      // 地图作者头像
  optional string creator_nickname = 5;    // 地图作者昵称
  optional string play_num = 6;            // 地图游玩次数
  optional string like_num = 7;            // 地图点赞次数
  optional uint32 map_type = 8;            // 地图类型
  optional string jump_schema = 9;         // 跳转链接
  repeated string tag_list = 10;  // 标签列表
  optional string tag_url = 11;            // 左上tag图
  optional uint32 creator_relation = 12;   // 创作者关系: 0 -> --; 1 -> 游戏好友; 2 -> 本人;
  repeated LGZXMapInspireMataInfo metainfo = 13;
  optional string creator_uid = 14;        // 地图作者uid
  optional uint64 best_record = 15;        // 最佳纪录
  optional string best_record_player_avatar = 16;
  optional string best_record_player_nickname = 17;
  optional string pass_rate = 18;
  optional string desc = 19;               // 地图描述
  optional uint64 publish_at = 20;         // 发布时间
  optional bool map_is_collected = 21;     // 地图是否被收藏
  optional bool creator_is_followed = 22;  // 创作者是否被关注
  optional string creator_fans_num = 23;   // 创作者粉丝数
  optional string collect_num = 24;        // 地图收藏数
}

message LGZXGetRecommendMapInspireListRsp {
  // 地图列表
  repeated LGZXMapInspire map_list = 1;
}

message LGZXBatchGepMapInspiresByIdReq {
  repeated string map_ids = 1;
}

message LGZXBatchGepMapInspiresByIdRsp {
  repeated LGZXMapInspire map_list = 1;
}

message LGZXGetTagMapInspireListReq {
  // 标签ID
  optional uint32 tag_id = 1;
  // 当前页码，从1开始计数
  optional int32 current_page = 2;
}

message LGZXMapInspireTagDesc {
  // 标签ID
  optional uint32 tag_id = 1;
  // 标签名字
  optional string tag = 2;
  // 标签描述
  optional string desc = 3;
  // 标签图片链接
  optional string tag_img = 4;
  // 标签总发布地图数
  optional string total_published_map_num = 5;
}

message LGZXGetTagMapInspireListRsp {
  // 地图列表
  repeated LGZXMapInspire map_list = 1;
  // 标签描述
  optional LGZXMapInspireTagDesc tag_desc = 2;
  optional bool has_more = 3;
}

message LGZXGetMapInspireListReq {
  // ID
  optional uint32 id = 1;
  // 当前页码，从1开始计数
  optional int32 current_page = 2;
  // 类型: 1 -> 标签; 2 -> 主题
  optional uint32 type = 3;
  // 子类型
  optional uint32 sub_type = 4;
}

message LGZXGetMapInspireListRsp {
  message LGZXMapInspireSubTypeDesc {
    // ID
    optional uint32 id = 1;
    // 名字
    optional string name = 2;
    // 描述
    optional string desc = 3;
    // 图片链接
    optional string img = 4;
    // 总发布地图数
    optional string total_published_map_num = 5;
    // 图片链接
    optional string jump_url = 6;
  }

  // 地图列表
  repeated LGZXMapInspire map_list = 1;
  // 标签描述
  optional LGZXMapInspireSubTypeDesc desc = 2;
  optional bool has_more = 3;
  optional uint32 type = 4;
  optional uint32 sub_type = 5;
  repeated LGZXMapInspireSubTypeDesc sub_type_list = 6; // 只有sub_type为0的第一页返回
}

message LGZXGetAISceneInspireListReq {
  // 分页大小，每页最多返回的记录数
  optional int32 page_size = 1;
  // 当前页码，从1开始计数
  optional int32 current_page = 2;
  // 标签ID
  optional int32 tag_id = 3;
}

message LGZXAISceneInspire {
  // 名字
  optional string name = 1;
  // 图片链接
  optional string img_url = 2;
  // 是否已收藏
  optional bool has_collected = 3;
  // id
  optional string id = 4;
}

message LGZXAISceneInspireTag {
  optional int32 tag_id = 1;
  optional string name = 2;
}

message LGZXGetAISceneInspireListRsp {
  // 灵感列表
  repeated LGZXAISceneInspire inspire_list = 1;
  // 总数
  optional int32 total = 2;
  // 是否还有下一页
  optional bool is_finished = 3;
  // 标签列表
  repeated LGZXAISceneInspireTag tag_list = 4;
}

message LGZXGetActivityConfigReq {
}

message LGZXActivity {
  optional uint32 acivity_type = 1;
  optional string jump_schema = 2;
  optional string activity_img = 3;
  optional string activity_title = 4;
  optional string activity_desc = 5;
}

message LGZXGetActivityConfigRsp {
  repeated LGZXActivity list = 1;
}

message LGZXSetCollectMapReq {
  optional string mapid = 1;
  optional bool collect = 2;
  optional int32 canvas_id = 3;
  optional string tab_name = 4;
}

message LGZXSetCollectMapRsp {
  optional int32 res = 1;
}

message LGZXSetCollectAISceneInspireReq {
  optional string sceneid = 1;
  optional bool collect = 2;
}

message LGZXSetCollectAISceneInspireRsp {
  optional int32 res = 1;
}

message LGZXSetFollowCreatorReq {
  optional string creator_uid = 1;
  optional bool follow = 2;
  optional int32 canvas_id = 3;
  optional string tab_name = 4;
}

message LGZXSetFollowCreatorRsp {
  optional int32 res = 1;
}

message CreatorActivityGetHotMapReq {
  optional uint32 type = 1;  // 1 竞速; 2 生存
}

message CreatorActivityHotMap {
  optional string id = 1;                  // 地图ID
  optional string title = 2;               // 地图标题
  optional string cover = 3;               // 地图封面
  optional string creator_avatar = 4;      // 地图作者头像
  optional string creator_nickname = 5;    // 地图作者昵称
  optional string like_num = 6;            // 地图点赞次数
  repeated string tags = 7;       // 地图类型
}

message CreatorActivityGetHotMapRsp {
  optional uint32 total = 1;
  repeated CreatorActivityHotMap map_list = 2;
  optional bool is_end = 3;  // 活动是否结束
  repeated CreatorActivityHotMap my_map_list = 4;
  optional string map_amount = 5;
  optional uint32 type = 6;  // 请求type原样返回
}

message UgcParkHotActivityInfoReq {
  optional string activity_id = 1;
}

message UgcParkHotActivityInfoRsp {
  optional string activity_id = 1;
  optional string name = 2;
  optional uint32 start_at = 3;
  optional uint32 end_at = 4;
  optional uint32 show_type = 5;  // 0 -> 不带热门标签; 1 -> 带热门标签
}

message TaskGetDiscoverPageActivityReq {
}

message TaskActivity {
  optional string activity_id = 1;
  optional string name = 2;
  optional string desc = 3;
  optional string tab_id = 4;
  optional string sub_tab_id = 5;
  optional string order_index = 6;
  optional string umg_name = 7;
  optional uint32 start_at = 8;
  optional uint32 end_at = 9;
  optional uint32 reddot = 10;
}

message TaskGetDiscoverPageActivityRsp {
  repeated TaskActivity activity_list = 1;
}

message TaskGetUmgActivityInfoReq {
  optional string activity_id = 1;
}

message TaskActivityBaseInfo {
  optional string name = 1;
  optional uint32 start_at = 2;
  optional uint32 end_at = 3;
  optional string bg_url = 4;
}

enum RewardType {
  REWARD_TYPE_NONE = 0;
  REWARD_TYPE_GAMEITEM = 1;
  REWARD_TYPE_CDKEY = 2;
  REWARD_TYPE_LOTTERY_TIMES = 3;
}

message TaskReward {
  optional uint32 type = 1;
  optional string item_id = 2;
  optional string item_icon = 3;
  optional string item_name = 4;
  optional uint32 amount = 5;
  optional string cdkey = 6;
}

message TaskInfo {
  optional string task_id = 1;
  optional string name = 2;
  optional string desc = 3;
  optional uint32 target = 4;
  optional uint32 progress = 5;
  optional uint32 status = 6;
  optional string jump_arg = 7;
  repeated TaskReward rewards = 8;
}

message TaskGetUmgActivityInfoRsp {
  optional string activity_id = 1;
  optional string umg_name = 2;
  optional TaskActivityBaseInfo base_info = 3;
  repeated TaskInfo task_list = 4;
}

// 创作者排行榜协议
// 创作者排行榜 榜单类型
enum EnumCreatorRankType {
  CREATOR_RANK_NONE = 0;
  CREATOR_RANK_ALL_SEASON = 1;            // 全服排行榜-赛季榜
  CREATOR_RANK_ALL_TOTAL = 2;             // 全服排行榜-历史总榜
  CREATOR_RANK_FRIEND_SEASON = 3;         // 好友排行榜-赛季榜
  CREATOR_RANK_FRIEND_TOTAL = 4;          // 好友排行榜-历史总榜
  CREATOR_RANK_AREA_SEASON = 5;           // (废弃) 地区排行榜-赛季榜
  CREATOR_RANK_AREA_TOTAL = 6;            // (废弃) 地区排行榜-历史总榜
  CREATOR_RANK_WEEK_HOT = 7;              // 全服排行榜-周热门排行榜
  CREATOR_RANK_REALTIME_HOT = 8;          // 全服排行榜-实时热门排行榜
  CREATOR_RANK_MAP_RANKING = 9;           // (废弃) 地图排行榜
  CREATOR_RANK_FRIEND_WEEK_HOT = 10;      // 好友排行榜-周热门排行榜
  CREATOR_RANK_FRIEND_REALTIME_HOT = 11;  // 好友排行榜-实时热门排行榜
  CREATOR_RANK_GROUP = 12;                // 全服排行榜-组合排行榜
  CREATOR_RANK_FRIEND_GROUP = 13;         // 好友排行榜-组合排行榜
  PLAYER_RANK_MAP_PASS = 14;              // 玩家通关榜
  PLAYER_RANK_FRIEND_MAP_PASS = 15;       // 玩家通关榜-好友
  PLAYER_RANK_PARADE_MAP_PASS = 16;       // 玩家星海巡游通关榜
  PLAYER_RANK_PARADE_FRIEND_MAP_PASS = 17;// 玩家星海巡游通关榜-好友
  PLAYER_RANK_GOOD_MAP_PASS = 18;         // 玩家星海好图通关榜
  PLAYER_RANK_GOOD_FRIEND_MAP_PASS = 19;  // 玩家星海好图通关榜-好友
}


// 创作者/地图 类型
enum EnumCreatorOrMapType {
  ALL_TYPES = 0;         // 全部类型
  RACING = 1;            // 竞速
  SURVIVE = 2;           // 生存
  TEAM_POINTS = 3;       // 团队积分
  INDIVIDUAL_POINTS = 4; // 个人积分
  MUSIC = 5;             // 音乐
  CASUAL = 6;            // 休闲
}

message CreatorRankInfo {
  optional string id = 1;              // uid
  optional string nick_name = 2;       // 昵称
  optional string avatar_url = 3;      // 头像
  optional uint64 growth_value = 4;    // 筑梦成长值
  optional uint32 rank = 5;            // 排名, rank=0 表示排名9999, 超出榜单显示范围
  optional string rank_logo_url = 6;   // 排名角标
  optional string full_avatar_url = 7; // avatar 形象 (已废弃)
  optional string creator_uid = 8;     // 创作者UID
  repeated int32 dress_up_infos = 9;   // 创作者装扮信息
  optional bool is_friend = 10;        // 是否好友
  optional int32 up_and_down = 11;     // 0 表示不展示, 1上升，2下降
  optional int32 creator_degree_lv = 12;         // 成长值段位id
  optional string open_id = 13;            // open id
  optional int32 login_type = 14;          // 登录类型（ 1 微信、2 qq)  参考TconndApiAccount
}

message PlayerCommonRankInfo {
  optional uint64 uid = 1;               //  玩家uid
  optional int64 score = 2;              //  榜单分数
  optional uint32 rank = 3;              //  榜单排名, rank为0，表示未上榜
  optional bool is_friend = 4;           //  是否好友
  optional string nick_name = 5;         //  昵称
  optional string avatar_url = 6;        //    头像
  optional int32 up_and_down = 11;       // 0 表示相等或者不展示, 1上升，2下降
  optional uint64 update_time_ms = 12;   // 上报时间
}

message GetCreatorTopKRankListReq {
  optional EnumCreatorRankType rank_type = 1;          // 榜单类型，已废弃
  optional EnumCreatorOrMapType map_type = 2;          // 地图类型，已废弃
  optional string creator_area = 3;                    // 创作者地区
  optional uint64 uid = 4;                             // 玩家uid
  optional int32  season_id = 5;                       // 赛季id
  optional string env = 6;                             // 请求游戏环境
  optional bool user_info_not_need = 7;                // 是否返回排行榜用户信息， 默认返回，填true不返回
  optional uint32 top_count = 8;                       // 返回的记录数
  optional uint64 creator_uid = 9;             // 请求者的 creator uid
  optional uint32 login_type = 10;             // 登录类型

  optional int32 int32_rank_type = 11;          // 榜单类型
  optional int32 int32_map_type = 12;          // 地图类型
}

// 获取前K排行榜 -- 响应
message GetCreatorTopKRankListRsp {
  optional EnumCreatorRankType rank_type = 1;           // 榜单类型
  repeated CreatorRankInfo creator_rank_info_list = 2;  // 榜单列表创作者信息
  optional CreatorRankInfo self_rank_info = 3;          // 当前用户榜单信息, rank=0 表示 排名超出显示范围，显示9999
  optional int32 int32_rank_type = 4;
}

enum UgcCommonRankQueryType {
  UGC_COMMON_RANK_QUERY_TYPE_TOPK = 0;      // player_rank_info_list 返回topK列表
  UGC_COMMON_RANK_QUERY_TYPE_RANK = 1;      // player_rank_info_list返回rank名次的玩家数据
  UGC_COMMON_RANK_QUERY_TYPE_UID = 2;       // player_rank_info_list返回uid指定玩家数据
}

message GetUgcCommonRankInfoReq {
  optional uint64 rank_id = 1;                 // 排行榜id
  optional uint32 top_count = 2;               // 返回的记录数
  optional uint64 uid = 3;                     // 玩家uid （端内选填，h5查询必填）
  optional string env = 4;                     // 环境 （端内选填，h5查询必填）
  optional uint32 rank = 5;                    // 排名支持 1-100
  optional uint32 type = 6;                    // enum UgcCommonRankQueryType
  optional bool is_friend_rank = 7;            // 是否好友排行榜
  optional uint64 snapshort_timestamp = 8;     // 快照毫秒时间戳, 0则去当前排行榜, 非0则根据该时间戳换算对应的榜单
}

message GetUgcCommonRankInfoRsp {
  repeated PlayerCommonRankInfo player_rank_info_list = 1; // 榜单列表信息
  optional PlayerCommonRankInfo self_rank_info = 2;                 // 当前用户榜单信息, rank=0 表示未上榜
}

message DelUserFromUgcCommonRankReq {
  optional uint64 rank_id = 1;                 // 排行榜id
  optional uint64 uid = 2;                     // 玩家uid
}

message DelUserFromUgcCommonRankRsp {
  optional bool update_cache = 1;              // 设置成true表示需要更新缓存 (删除到前十名的玩家)
}

// 获取用户前后K排行榜 -- 请求
message GetCreatorAroundRankListReq {
  optional EnumCreatorRankType rank_type = 1;         // 榜单类型
  optional EnumCreatorOrMapType map_type = 2;         // 地图类型
  optional string creator_area = 3;                   // 创作者地区
  optional uint64 uid = 4;                            // 玩家uid
  optional int32  season_id = 5;                      // 赛季id
  optional string env = 6;                            // 请求游戏环境
  optional bool user_info_not_need = 7;               // 是否返回排行榜用户信息， 默认返回，填true不返回
  optional uint64 creator_uid = 8;             // 请求者的 creator uid
}

// 获取用户前后K排行榜 -- 响应
message GetCreatorAroundRankListRsp {
  optional EnumCreatorRankType rank_type = 1;             // 榜单类型
  repeated CreatorRankInfo creator_rank_info_list = 2;    // 榜单列表创作者信息
  optional int32 self_location = 3;                       // 当前用户在返回列表中位置, self_location=-1 表示排名9999, 超出榜单显示范围
}

// 拉取用户日常事件请求
message GetUserFeedbackDataListReq {

}
message FeedbackData {
  optional uint32 id = 1; // 1 日报 2 等级
  optional uint32 sort = 2; // 排序 按数字递增

  optional Daily daily = 3;
  optional LevelUp level_up = 4;

  message Daily {
    optional string play_num = 1;
    optional string fans_num = 2;
    optional string like_num = 3;
    optional string collect_num = 4;
  }

  message LevelUp {
    optional uint32 level = 1;
    optional string desc = 2;
  }
}

message GetUserFeedbackDataListRsp {
  repeated FeedbackData list = 1;
}


message ChannelGameReportReq {
  optional string eventName = 1; // 事件名称 对局事件 CloudGamePlay 时长事件 CloudGameTime
  optional EventValue eventValue = 2; // 事件详情数据
}

message EventValue {
    optional string BattleID = 1;
    optional string MapID = 2;
    optional string ChannelID = 3;
    optional string UID = 4;
    optional string AvatarStr = 5;
    optional int32 Result = 6;
    optional int32 BattleTime = 7;
    optional int32 ItemID = 8;
    optional string Token = 10;
    optional string Type = 11; // qq or wx
    optional string Appid = 12;// 渠道appid
    optional string Unionid = 13;
    optional string ClueToken = 14;
    optional string ReqId = 15;
    optional string ClickId = 16;
    optional string Callback = 17;
    optional string MinigameChannel = 18;
    optional uint32 PlatId = 19;
    optional string OpenId = 20;
    optional string Trackid = 21;
    optional string QQ = 22;
    optional string QQVerison = 23;
}
message ChannelGameReportRsp {
}


message ChannelGameGetPopupEventReq {
  optional string channel_id = 1; // 当前用户渠道号
}

message ChannelGameGetPopupEventRsp {
    message Popup {
      optional string event_type = 1; // gamelogin 非首日登录 gameplay 当天总对局数 gametime 当天游玩时长
      optional string desc = 2; // 弹窗描述
      optional bool force = 3; //是否强制弹窗
      optional uint32 condition = 4; // 值
      optional string award_url = 5; // 奖励图片
    }

    optional uint32 game_time = 1; // 当天游玩时长
    optional uint32 game_play = 2; // 当天游戏对局
    repeated Popup list = 3; // 弹窗数据
    optional string jump_url = 4; // 跳转地址
}

message OverseaReviewMapReq {
  optional uint64 map_id = 1;
  optional uint32 result = 2;     // 审核结果 1通过 2不通过
}

message OverseaReviewMapRsp {

}

// 700 - 800
enum IqServerErrCode {
  ERR_IQSERVER_NONE = 0;
  ERR_IQSERVER_INVALID_PARAMS = 700;      // 参数异常
  ERR_IQSERVER_NOT_IN_JOIN_TIME = 701;    // 不在报名时间内
  ERR_IQSERVER_NOT_IN_WAIT_TIME = 702;    // 不在候场时间内
  ERR_IQSERVER_NOT_JOIN_ACTIVITY = 703;   // 查不到报名记录
  ERR_IQSERVER_NOT_READY_ACTIVITY = 704;  // 查不到候场记录
  ERR_IQSERVER_NOT_IN_RESULT_TIME = 705;  // 未到或者超出结算时间
  ERR_IQSERVER_INVALID_RESULT_USER = 706; // 非结算用户, 没有上报最后一题的用户
}

message IqJoinReq {
  optional uint32 activity_id = 1;
}
message IqJoinRsp {
}
message IqAnswer {
  optional uint32 answer_id = 1;   // 答案ID
  optional string content = 2;     // 答案内容
  optional bool is_right = 3;      // 是否正确答案
}
message IqQuestion {
  optional uint32 question_id = 1;         // 问题id
  optional string content = 2;             // 问题内容
  repeated IqAnswer list = 3;              // 答案列表
}
message IqReadyReq {
  optional uint32 activity_id = 1;
}
message IqPlayerNum {
  optional uint32 player_num = 1;          // 总人数
  optional uint32 last_player_num = 2;     // 剩余人数
}
message IqReadyRsp {
  optional uint32 activity_id = 1;          // 活动ID
  repeated IqQuestion list = 2;             // 问题列表
  optional uint64 begin_commit_time = 3;    // 开始答题时间戳 (秒)
  optional uint32 ready_commit_time = 4;    // 准备答题时间（毫秒）
  optional uint32 commit_time = 5;          // 答题时间（毫秒）
  optional uint32 settle_time = 6;          // 结算时间（毫秒) （结算读条时间 = 准备答题时间  结算时间）
  repeated IqReward iq_reward = 7;                    // 活动道具列表，打气和复活两种，没有返回0。
  optional IqDefaultReward iq_default_reward = 8;     // 端内默认道具
  optional uint32 answer_show_time = 9;      // 答案展示时间（毫秒）
  repeated IqPlayerNum player_num_list = 10;
}
message IqCommitReq {
  optional uint32 activity_id = 1;          // 活动ID
  optional uint32 question_id = 2;          // 问题ID
  optional uint32 answer_id = 3;            // 答案ID
  optional uint64 seq = 4;                  // 提交请求的序列号，每轮答题，都重新计数。
}
message IqCommitRsp {
}
message IqJoinUser {
  optional uint64 uid = 1;
}
message GetIqReadyUserReq {
  optional uint32 activity_id = 1;
  optional uint64 last_score = 2;  // 初始值为0，后续用上一次请求的返回值赋值
}
message GetIqReadyUserRsp {
  repeated IqJoinUser list = 1;
  optional uint32 pull_time = 2;   // 定时拉取的时间间隔（秒）
  optional uint64 score = 3;       // 传递给下一次请求的 last_score 字段
}
message GetIqResultReq {
  optional uint32 activity_id = 1;             // 活动id
}
message GetIqResultRsp {
  optional uint32 activity_id = 1;             // 活动id
  optional uint32 user_num = 2;                // 胜出总人数
  optional uint32 bonus = 3;                   // 金额（分）或者红包个数
  optional uint32 bonus_type = 4;              // 0 星运红包, 1 现金
}


message GetIqAssistanceReq {
    optional uint32 type = 1; // 1 打气 2 复活
    // optional uint32 assistance_type = 2; // 0我助力的 1他人给我助力的
}

message GetIqAssistanceRsp {
    message Item {
        optional string avatar = 1;
        optional string nick = 2;
        optional string time = 3;
        optional uint32 score = 4; // 助力值
    }

    repeated Item self = 1; // 我助力的
    repeated Item others = 2; // 他人助力我的
}


message GetIqPropsReq {
}

message IqReward {
    optional uint32 max = 1;
    optional uint32 received = 2;
    optional uint32 current = 3;
    optional uint32 type = 4; // 道具类型 1 打气 2 复活
}

message IqAssistance {
    message Rewrad {
        optional uint32 received = 1; // 0 未领取 1 已领取
        optional uint32 progress = 2; // 所需进度
        optional uint32 count = 3; // 奖励数量

    }
    optional uint32 progress = 1; // 当前进度
    optional uint32 type = 2; // 道具类型 1 打气 2 复活
    repeated Rewrad list = 3; // 奖励列表
}

message GetIqPropsRsp {
    repeated IqReward list = 1;
    repeated IqAssistance assistance_list = 2; // 奖励列表
}


message UseIqPropsReq {
    optional uint32 type = 1; // 道具类型 1 打气 2 复活
    optional uint64 seq = 2;  // 提交请求的序列号，每轮答题，都重新计数。
    optional uint32 activity_id = 3;
    optional uint32 question_id = 4;          // 问题ID
    optional uint32 answer_id = 5;            // 正确答案ID
}

message UseIqPropsRsp {
    optional uint32 current = 1; // 当前剩余个数
}

message ReceiveIqAssistanceRewardReq {
    optional uint32 type = 1; // 道具类型 1 打气 2 复活
    optional uint32 index = 2; // 第几个奖励
}

message ReceiveIqAssistanceRewardRsp {
    optional uint32 current = 1; // 当前剩余个数
}


message GetIqBattleInfoReq {
}

message GetIqBattleInfoRsp {
    repeated string datas = 1; // 获胜场次、胜率、 累计奖金

    message Item {
        optional string name = 1; // 场次
        optional string pass_rate =2 ; // 通过率
        optional string bonus = 3; // 奖池
        optional uint32 bonus_type = 5; // 奖金类型 0 星运奖金 1 现金
        optional string win_bonus = 4; // 获得的奖金
    }

    repeated Item list = 2; // 获奖数据
}

message IqDefaultReward {
    optional string item_id = 1; // 道具id
    optional uint32 count = 2; // 道具数量
}

message GetIqIndexReq {
}
message GetIqIndexRsp {
    message Item {
        optional string name = 1; // 标题
        optional string desc = 2; // 描述
        optional uint64 begin_join_time = 3; // 开始报名时间
        optional uint64 end_join_time = 4; // 结束报名时间
        optional uint64 begin_ready_time = 5; // 开始候场时间
        optional uint64 begin_commit_time = 6; // 开始答题时间
        optional uint32 ready_commit_time = 7; // 准备答题时间（单位毫秒）
        optional uint32 commit_time = 8; // 答题时间（单位毫秒）
        optional uint32 settle_time = 9; // 最后结算时间（单位毫秒）
        optional string bonus = 10; // 奖金(格式化后)
        optional string theme = 11 ; // 场次主题
        optional bool is_join =  12; // 是否已经报名参赛
        optional uint32 activity_id = 13; // 活动 id
        optional IqDefaultReward default_reward = 14; //兜底奖励
        optional uint64 end_time = 15; // 活动结束时间
        optional uint32 answer_show_time = 16; // 答案展示时间（单位毫秒）
        optional uint32 bonus_type = 17; // 奖励类型 0 星运奖金 1 现金
      }

    repeated Item list = 1; // 当天场次列表
    optional uint64 begin_time = 2; // 活动开始时间
    optional uint64 end_time = 3; // 活动结束时间
    repeated string share_url_list = 4; // 分享地址
}


message ReceiveIqDefaultRewardReq {
    optional uint32 activity_id = 1; // 活动 id
}

message ReceiveIqDefaultRewardRsp {
}


message RpcAppNtfReq {
  optional int64 app_creator_id = 1;
  optional uint32 cmd = 2;
  optional bytes req_body = 3;
}

message RpcAppNtfRsp {
  optional int32 err_code = 1;
  optional string err_msg = 2;
  optional bytes rsp_body = 3;
}


message GetPlatConfigV2Req {
  optional int32 source = 1;// 0 端 1 云端
  optional string version = 2; // 客户端版本号
  optional string md5 = 3; // 客户端配置md5
}

message GetPlatConfigV2Rsp {
  repeated MsgKvItem list = 1;
  optional bool only_key = 2; // 只返回 key
  optional string md5 = 3; // 当前配置md5
  optional bool dynamic_update = 4; // 0 全量， 1 增量
}


message BatchGetPlatConfigReq {
  optional int32 source = 1;// 0 端 1 云端
  optional string version = 2; // 客户端版本号
  repeated int32 keys = 3;
}

message BatchGetPlatConfigRsp {
  repeated MsgKvItem list = 1;
}

// 提交内购合同签署
message UgcContractSigningReq {
    optional string id_card = 1; // 身份证
    optional string name = 2; // 姓名
}

message UgcContractSigningRsp {
}

// 查询状态
message GetUgcContractSigningReq {
}

message GetUgcContractSigningRsp {
    optional uint32 status = 1; // 0 未签署 1 审核中 2 签署失败 3 签署成功
}


message APPGetBottomTabsReq {
  optional int32 device_type = 1; // 1=Phone; 2=Pad; 3=PC
}

message APPGetBottomTabsRsp {
  message BottomTab {
    message Config {
      message PageInfo {
        optional string umg_name = 1; // UMG名

        optional string pixui_appid = 11; // pixui appid

        optional string params = 101; // 透传参数
      }

      optional string name = 1; // Tab名称
      optional string selected_icon = 2; // 选中态icon
      optional string unselected_icon = 3; // 未选中态icon

      optional PageInfo page_info = 11; // 页面配置
    }

    optional string id = 1; // 底Tab ID
    optional Config config = 2; // 底Tab配置
  }

  repeated BottomTab list = 1; // 底Tab列表
  optional string default_tab_id = 2; // 默认的底TabID
}

message GameNtf {
  optional uint32 cmd = 2;
  optional bytes req_body = 3;
}
// AppGetMyPageInfo = 1001001;
message GetMyPageInfoReq {
}

message AppUserProfileInfo {
  optional int64 creator_id = 1;
  optional string display_uid = 2;  // 用于展示的uid 实际上就是gopenid
  optional string nickname = 3;
  optional string avatar = 4;
}

message GetMyPageInfoRsp {
  optional AppUserBaseInfo base_info = 1;
  optional AppUserProfileInfo user_info = 2;
  optional string txc_user_data = 3;
}

// AppGetBindedGameRole = 1001002;
message GetBindedGameRoleReq {
}

message GameRole {
  optional int64 creator_id = 1;
  optional string short_uid = 2;
  optional string nickname = 3;
  optional string avatar = 4;
  optional string area = 5;
  optional AppUserLoginBanInfo login_ban_info = 6; // 登录封禁信息
}

message GetBindedGameRoleRsp {
  optional GameRole role = 1;
}

// AppGetAllGameRoles = 1001003;
message GetAllGameRolesReq {
}

message GetAllGameRolesRsp {
  repeated GameRole role_list = 1;
}

// AppBindGameRole = 1001004;
message BindGameRoleReq {
  optional int64 creator_id = 1;
}

message BindGameRoleRsp {
}

// AppUnbindGameRole = 1001005;
message UnbindGameRoleReq {
  optional int64 creator_id = 1;
}

message UnbindGameRoleRsp {
}

// AppGetUserInfo = 1001006;
message AppGetUserInfoReq {
}

message AppGetUserInfoRsp {
  optional string nickname = 1;
  optional string avatar = 2;
  optional proto_PlayerPublicEquipments game_equipment = 3;
  optional proto_UgcMapSetInfo ugc_map_set_info = 4;
  optional proto_FittingSlots fitting_slots = 5;
  repeated int32 guide_tasks = 6 [packed=true];
  optional proto_ItemInfoDb item_info = 7;
  repeated MsgKvItem cache_list = 8;
  repeated proto_XlsWhiteList user_attr_white_list = 9;
  optional string game_gopenid = 10;                         // 元梦游戏侧gopenid
  optional uint32 register_login_type = 11;                  // 注册时的账号类型 1:wx 2:qq 11:手机号
  optional uint32 binded_platform_account_type = 12;         // 手机注册账号绑定的wx/qq平台账号类型 1wx 2手Q 0未绑定
  optional string binded_platform_account_nick = 13;         // 手机注册账号绑定的平台账号昵称
  optional string binded_platform_account_headurl = 14;      // 手机注册账号绑定的平台账号头像
}

// AppGetAccountStatus = 1001007;
message AppGetAccountStatusReq {
  optional string gopenid = 1;
  optional string access_token = 2;
}

message AppGetAccountStatusRsp {
  optional uint32 account_status = 1; // 账号状态 1: 处于注销冷静期中; 2: 取消注销; 3: 已注销
  optional int64 account_status_update_time = 2;  // 账号状态更新时间
}

// AppCheckEduToken = 1001009;
message AppCheckEduTokenReq {
  optional string edu_token = 1;
}

message AppCheckEduTokenRsp {
  optional string gopenid = 1;
  optional string access_token = 2;
  optional uint32 register_status = 3; // 0 -> 未注册，需要弹用户信息授权窗口; 1 -> 已注册
}

// AppLogOut = 1001010
message AppLogOutReq {
}

message AppLogOutRsp {
}

// 独立app手机号绑定平台账号
// AppBindPlatFormAccount = 1001011;
message AppBindPlatformAccountReq {
  optional string binded_platform_openid = 1;               // app手机注册账号绑定得wx/qq平台账号的openid信息
  optional string binded_platform_accesstoken = 2;          // app手机注册账号绑定的wx/qq平台账号的access_token
  optional uint32 binded_platform_account_type = 3;         // app手机注册账号待绑定的wx/qq平台账号类型 1wx 2手Q 0未绑定
  optional string binded_platform_account_nick = 4;         // app手机注册账号待绑定的平台账号昵称
  optional string binded_platform_account_headurl = 5;      // app手机注册账号待绑定的平台账号头像
}

message AppBindPlatformAccountRsp {
}

// 独立app手机号解绑平台账号
// AppUnBindPlatFormAccount = 1001012;
message AppUnBindPlatformAccountReq {
  optional string openid = 1;                               // 当前登录账号的openid
  optional string accesstoken = 2;                          // 当前登录账号的token
  optional uint32 account_type = 3;                         // 当前登录账号的账号类型 1wx 2手Q 11手机号
}

message AppUnBindPlatformAccountRsp {
}

// AppUgcDraftList = 1002001;
message AppUgcDraftListReq {
  optional int64 creator_id = 1;
}

message AppUgcDraftListRsp {
  message AppUgcItem {
    optional int64 ugc_id = 1;
    optional int32 progress_code = 2;
  }
  repeated UgcItem maps = 1;
  optional int32 drafts_limit = 2;
  repeated AppUgcItem app_ugc_item_data = 3;
  optional UgcKeyInfo key_info = 4;
}

// AppCopyMap = 1002002;

enum AppCopyMapCopyType {
  APP_COPY_MAP_COPY_TYPE_NONE = 0;
  APP_COPY_MAP_COPY_TYPE_APP_TO_UGC = 1;
  APP_COPY_MAP_COPY_TYPE_UGC_TO_APP = 2;
}

message AppCopyMapReq {
  optional int64 creator_id = 1;
  optional int64 ugc_id = 2;
  optional int32 copy_type = 3;
  optional int64 app_creator_id = 4;
  optional string copied_map_name = 5;
}
message AppCopyMapRsp {
}

// AppCopyProgress = 1002003;
message AppCopyProgressReq {
  optional int64 creator_id = 1;
  repeated int64 ugc_id = 2;
}
enum AppCopyProgressCode {
  APP_COPY_PROGRESS_CODE_NONE = 0;           // 未开始
  APP_COPY_PROGRESS_CODE_PROGRESSING = 1;    // 进行中
  APP_COPY_PROGRESS_CODE_DONE = 2;           // 已完成
}
message AppCopyProgressRsp {
  message CopyProgressResult {
    optional int64 ugc_id = 1;
    optional int32 progress_code = 2;
  }
  repeated CopyProgressResult result_list = 1;
}

// GroupWhiteList = 1002004;
message CheckWhiteListReq {
  optional string service_name = 1;
  repeated string open_id_list = 2;
}
message CheckWhiteListRsp {
  repeated string white_open_id_list = 1;
  optional bool self_result = 2;
}

// GetRewardActivityList = 1002101;
message GetRewardActivityListReq {
}

message GetRewardActivityListRsp {
    message ActivityDesc {
        optional uint32 id = 1;
        optional string title_h5 = 2;
        optional string title = 3;
        optional bool push_onoff = 4;
        optional string push_img = 5;
        optional bool tag_onoff = 6;
        optional string tag_img = 7;
        optional string share_url = 8;
        optional uint64 start_time = 9;
        optional uint64 end_time = 10;
        optional string rules = 11;
    }
    repeated ActivityDesc activity_desc_list = 1;
}

// GetRewardActivityList = 1002102;
message GetRewardActivityDetailReq {
  optional uint32 activity_id = 1;
}

message GetRewardActivityDetailRsp {
  message CheckDoneData {
      optional uint32 curr_count = 1;          // 当前完成进度
      optional uint32 finish_count = 2;        // 总进度
      optional uint64 last_update_time = 3;    // 最后一次任务更新时间 秒 (判断今天是否已登录)
      optional uint64 received_time = 4;       // 奖励领取时间 秒 (判断是否已领取奖励)
  }
  message RewardDesc {
      optional string reward_name = 1;
      optional string reward_icon = 2;
      optional uint32 reward_num = 3;
  }
  message TaskDesc {
      optional uint32 task_id = 1;
      optional string task_name = 2;
      optional string task_icon = 3;
      optional string task_desc = 4;
      optional uint32 task_type = 5;
      optional string remarks = 6;
      optional CheckDoneData check_done_data = 7;
      repeated RewardDesc reward_desc_list = 8;
  }
  message TaskGroupDesc {
      optional uint32 group_id = 1;
      optional string group_name = 2;
      repeated TaskDesc task_desc_list = 3;
  }
  optional string title_h5 = 1;
  optional string title = 2;
  optional uint64 start_time = 3;      // 时间戳 秒
  optional uint64 end_time = 4;        // 时间戳 秒
  optional string rules = 5;
  repeated TaskGroupDesc task_group_desc_list = 6;
}
// TaskCommit = 1002103;
message TaskCommitReq {
  optional uint32 activity_id = 1;
  optional uint32 task_id = 2;
}
message TaskCommitRsp {
}
// TakeReward = 1002104;
message TakeRewardReq {
  optional uint32 activity_id = 1;
  optional uint32 task_id = 2;
}
message TakeRewardRsp {
}

// GetCommonUploadPresignedUrl CMD=7050
message GetCommonUploadPresignedUrlReq {
  optional bool custom_cors = 1;
}

message GetCommonUploadPresignedUrlRsp {
  message PresignedUrl {
    optional string url = 1;
    optional string upload_url = 2;
    optional int64 expired_at = 3;
  }

  optional uint32 code = 1;
  optional string message = 2;
  optional PresignedUrl presigned_url = 3;
}

/*
 独立APP EDU活动相关（中轴线需求）
*/

// 发送验证码 CMD=1001008
message SendSmsCodeReq {
  optional string phone = 1; // 手机号
}

message SendSmsCodeRsp {
  optional int32 err_code = 1;
  optional string err_message = 2;
}

// 获取活动详情 CMD=1003001
message GetEDUActivityInfoReq {
  optional string id = 1; // 活动ID
}

message GetEDUActivityInfoRsp {
  message UserStatus {
    optional bool is_signed_up = 1; // 是否已报名
    optional uint64 submitted_map_id = 2; // 参赛提交地图ID
    optional string submitted_map_name = 3; // 参赛提交地图名
    optional string submitted_map_cover = 4; // 参赛提交地图封面
    optional int64 map_submit_time = 5; //参赛地图提交时间
    optional int64 map_update_time = 6; //参赛地图修改时间
    optional string submitted_video = 7; // 参赛提交视频
    optional int64 video_submit_time = 8; //参赛视频提交时间
    optional int64 video_update_time = 9; //参赛视频修改时间

  }
  message FormParams {
    repeated string required_fields = 1; // 必填字段
    repeated string optional_fields = 2; // 非必填字段
    repeated string groups = 3; //参赛组别选项
  }

  optional string id = 1; // 活动ID
  optional string title = 2; // 活动标题
  optional string cover = 3; // 活动KV封面
  optional bool enable_sign_up = 4; // 是否开启报名
  optional int64 sign_up_begin_time = 5; // 报名开始时间
  optional int64 sign_up_end_time = 6; // 报名结束时间
  optional int64 submit_begin_time = 7; // 作品提交开始时间
  optional int64 submit_end_time = 8; // 作品提交结束时间
  optional bool show_activity_content = 9; // 活动公告开关
  optional string activity_content = 10; // 活动公告内容
  optional bool show_activity_courses = 11; // 教程课程tab开关
  optional bool show_my_activity = 12; // 我的活动tab开关
  optional FormParams form_params = 13; // 报名表参数
  repeated EDUMapTemplate map_template_list = 14; // 模版列表
  optional int64 cert_start_time = 15; // 证书发放开始时间
  optional bool show_works = 16; // 作品展tab开关
  optional string h5_domain = 17; // H5域名
  optional UserStatus user_status = 101; // 用户报名参赛状态
}

// 活动报名
message EDUActivitySignUpInfo {
  message IDCard {
    optional int32 type = 1; // 证件类型 0=身份证 1=护照 2=港澳身份证
    optional string id_card_no = 2; // 证件号
  }

  message SchoolInfo {
    optional string country = 1; // 国家
    optional string province = 2; // 省
    optional string city = 3; // 市
    optional string district = 4; // 区/县
    optional string school = 5; // 学校名称
  }

  optional string student_name = 1; // 学生姓名
  optional string phone = 2; // 手机号
  optional IDCard id_card_no = 3; // 证件号
  optional string group = 4; // 组别
  optional SchoolInfo school = 5; // 学校
  optional string teacher_name = 6; // 指导教师姓名
  optional string teacher_work_unit = 7; // 指导教师所在单位
  optional string teacher_phone = 8; // 指导教师手机号
  optional string phone_no_verify = 9; // 手机号（无校验）
  optional string student_id = 10; // 学生学号
}

// 报名 CMD=1003002
message CreateEDUActivitySignUpReq {
  optional string id = 1; // 活动ID
  optional EDUActivitySignUpInfo info = 2; // 报名信息
  optional string verify_code = 3; // 验证码
}

message CreateEDUActivitySignUpRsp {
  optional int32 err_code = 1;
  optional string err_message = 2;
}

// 获取报名详情 CMD=1003003
message GetEDUActivitySignUpReq {
  optional string id = 1; // 活动ID
}

message GetEDUActivitySignUpRsp {
  optional EDUActivitySignUpInfo info = 1;
}

// 更新报名信息 CMD=1003004
message UpdateEDUActivitySignUpReq {
  optional string id = 1; // 活动ID
  optional EDUActivitySignUpInfo info = 2; // 报名信息
  optional string verify_code = 3; // 验证码，修改手机号时传
}

message UpdateEDUActivitySignUpRsp {
  optional int32 err_code = 1;
  optional string err_message = 2;
}

// 删除报名 CMD=1003005
message DeleteEDUActivitySignUpReq {
  optional string id = 1; // 活动ID
}

message DeleteEDUActivitySignUpRsp {}

// 更新参赛地图、参赛视频 CMD=1003006
message UpdateEDUActivitySubmitReq {
  optional string id = 1; // 活动ID
  optional int32 cmd_type = 2; // 0=更新地图，1=更新视频
  optional uint64 map_id = 3; // 地图ID
  optional string map_name = 4; // 参赛提交地图名
  optional string map_cover = 5; // 参赛提交地图封面
  optional string video = 6; // 视频链接
}

message UpdateEDUActivitySubmitRsp {}

// 活动课程

message EDUCourse {
  message Chapter {
    optional EDUCourseChapter chapter_info = 1; // 章节信息
  }

  optional string id = 1; // 课程ID
  optional string name = 2; // 课程名称
  optional string cover = 3; // 课程封面
  optional string desc = 4; // 课程介绍
  repeated Chapter chapters = 5; // 课程章节
  optional int32 chapter_dependency_type = 6; // 章节依赖类型，0=不依赖，1=需要前一章节所有任务集为完成状态则解锁下一章
  optional string small_cover = 7; // 小封面图
  optional string desc_richtext = 8; // 课程介绍富文本
  optional string middle_cover = 9; // 中封面图
  optional string outline = 10; // 课程大纲
  optional string study_target = 11; // 学习目标
  optional string prerequisites = 12; // 预备知识
  optional uint32 unit_cnt = 13; // 学习单元总数量
  optional uint32 user_learned_unit_cnt = 14; // 用户当前已学习单元数量
  optional string study_user_cnt = 15; // 已学习用户数量
  optional string name_font_color = 16; // 课程名称字体颜色
}

message EDUCourseChapter {
  message Unit {
    message QuizSet {
      optional EDUQuizSet quiz_set_info = 1; // 任务集信息
      optional int32 pass_state = 2; // 通过状态。0=未作答；1=正确；2=错误
      optional string state_text = 3; // 状态文本（待提交/未作答/正确率等等）
      optional string pass_remark = 4; // 备注（多少正确率才通过）
    }

    optional int32 unit_type = 1; // 单元类型，0=教程，1=quiz
    optional Tutorial tutorial = 2; // 教程
    optional QuizSet quiz_set = 3; // 任务集
  }

  optional string id = 1; // 章节ID
  optional string name = 2; // 章节名称
  repeated Unit units = 3; // 单元列表
}

message EDUQuizSet {
  optional string id = 1; // 任务集ID
  optional string name = 2; // 任务集名称
  optional int32 type = 3; // 任务集类型。0=选择题集；1=问答题集；2=创作题集
  repeated EDUQuiz quiz_list = 4; // 任务题目列表
}

message EDUQuiz {
  optional string id = 1; // 任务题目ID
  optional string name = 2; // 任务题目名称
  optional int32 type = 3; // 任务类型。0=选择题；1=问答题；2=创作题
  optional string stem_content = 4; // 题干内容

  optional EDUQuizChoice choice = 5; // 选择题
  optional EDUQuizQA qa = 6; // 问答题
  optional EDUQuizCreate create = 7; // 创作题
}

enum EEduQuizAnswerPassState {
  EDU_QUIZ_ANSWER_PASS_STATE_UNKNOWN = 0; // 未作答
  EDU_QUIZ_ANSWER_PASS_STATE_PASS = 1; // 通过
  EDU_QUIZ_ANSWER_PASS_STATE_FAIL = 2; // 未通过
}

// 选择题
message EDUQuizChoice {
  message Choice {
    optional string id = 1; // 选项ID
    optional string text = 2; // 选项文本
    optional string pic = 3; // 选项配图
  }

  message UserData {
    repeated string choice_ids = 1; // 选项ID列表
  }

  optional int32 answer_type = 1; // 答题方式。0=单选题
  repeated Choice choices = 2; // 选项列表
  repeated string correct_choice_ids = 3; // 正确选项ID列表
  optional string answer_text = 4; // 答案文本

  optional UserData user_data = 5; // 用户数据
  optional int32 pass_state = 6; // 通过状态。0=未作答；1=正确；2=错误
}

// 问答题
message EDUQuizQA {
  message UserData {
    optional string content = 1; // 内容文本
    repeated string pic_list = 2; // 图片列表
  }

  optional int32 answer_type = 1; // 答题方式。0=图文作答（图片1张上限）
  optional UserData user_data = 2; // 用户数据
  optional int32 pass_state = 3; // 通过状态。0=未作答；1=正确；2=错误
}

// 创作题
message EDUQuizCreate {
  message UserData {
    optional uint64 map_id = 1; // 地图ID
    optional string name = 2; // 地图名称
    optional string cover = 3; // 地图封面
    optional int64 timestamp = 4; // 提交时间戳
  }

  repeated int32 answer_type_list = 1; // 答题方式。0=草稿箱选择；1=空白创作；2=模板创作
  repeated EDUMapTemplate map_template_list = 2; // 模板列表
  optional UserData user_data = 3; // 用户数据
  optional int32 pass_state = 4; // 通过状态。0=未作答；1=正确；2=错误
}

// 获取活动课程列表 CMD=1003007
message GetEDUActivityCoursesReq {
  optional string id = 1; // 活动ID
}

message GetEDUActivityCoursesRsp {
  message Course {
    optional EDUCourse course_info = 1; // 课程信息
    optional int32 permission_type = 2; // 课程权限要求。0=免费可用；1=报名可用
  }

  repeated Course courses = 1; // 课程列表
  optional int64 learn_begin_time = 2; // 学习开始时间
  optional int64 learn_end_time = 3; // 学习结束时间
}

// 获取任务集详情 CMD=1003008

message GetEDUQuizSetReq {
  optional string activity_id = 1; // 活动ID
  optional string course_id = 2; // 课程ID
  optional string course_chapter_id = 3; // 课程章节ID
  optional string quiz_set_id = 4; // 任务集ID
  optional bool need_correct_answer = 5; // 是否展示正确答案（仅选择题）
}

message GetEDUQuizSetRsp {
  optional EDUQuizSet quiz_set_info = 1; // 任务集信息
  optional int32 pass_state = 2; // 通过状态。0=未作答；1=正确；2=错误
  optional string state_text = 3; // 状态文本（待提交/未作答/正确率等等）
  optional string pass_remark = 4; // 备注（多少正确率才通过）
}

// 提交任务集作答信息 CMD=1003009
message SubmitEDUQuizSetAnswerReq {
  optional string activity_id = 1; // 活动ID
  optional string course_id = 2; // 课程ID
  optional string course_chapter_id = 3; // 课程章节ID
  optional string quiz_set_id = 4; // 任务集ID
  repeated EDUQuiz quiz_answers = 5; // 答题信息列表(每个quiz里填id和对应的user_data即可)
}

message SubmitEDUQuizSetAnswerRsp {
  optional EDUQuizSet quiz_set_info = 1; // 任务集信息
  optional int32 pass_state = 2; // 通过状态。0=未作答；1=正确；2=错误
  optional string state_text = 3; // 状态文本（待提交/未作答/正确率等等）
  optional string pass_remark = 4; // 备注（多少正确率才通过）
}

// 地图模板
message EDUMapTemplate {
  optional string id = 1; // 模板ID
  optional string name = 2; // 模板名称
  optional string desc = 3; // 模板描述
  optional string cover = 4; // 模板封面
}

// 获取活动banner列表 CMD=1003010
message GetEDUActivityBannerListReq {}

message GetEDUActivityBannerListRsp {
  message Banner {
    optional string id = 1; // banner ID
    optional string title = 2; // 标题
    optional string cover = 3; // 封面
    optional string url = 4; // 跳转链接
    repeated int64 plat_ids = 5; // 上线平台
  }

  repeated Banner banner_list = 1;
}


// cmd 1003011
message GetPrizeInfoReq {
  optional string activity_id = 1; // 活动ID
  optional int32 platform = 2; // 0-手机 1-pad 2-pc
}

message GetPrizeInfoRsp {
  message Prize {
    optional string prize_name = 1; // 奖项名称
    optional string template_id = 2; // 模版id
    optional string cert_number = 3; // 证书编号

    optional int32 cert_channel_type = 10; // 证书渠道类型。0=扣叮渠道；1=第三方渠道
    optional string cert_image = 11; // 证书图片
    optional string cert_jump_url = 12; // 证书跳转链接
  }
  repeated Prize prize_list = 1; // 奖项列表
  optional string name = 2; //姓名
  optional string school = 3; // 学校
  optional string group = 4; // 参赛组别
  optional string teacher = 5; // 教师
}
// 1003012
message GetWorksShowInfoReq {
  optional string activity_id = 1; // 活动ID
}
message GetWorksShowInfoRsp {
  message Category {
    message Work {
      optional string map_id = 1; // 地图id
      optional int32 type = 2; // 展示类型 1-专家推荐 2-用户作品
      optional string name = 3; // 作品名称
      optional string author_name = 4; // 作者名称
      optional string description = 5; // 简介/点评
      optional bool can_download = 6; // 支持下载
      optional bool can_play = 7; //支持体验
    }
    optional string name = 1; // 类别名
    optional int64 index = 2; // 排序下标
    repeated Work work_list = 3; // 作品列表
  }
  optional string content = 1; // 作品展公告
  repeated Category list = 2; // 分类作品
}



message GetUgcVersionReddotReq {
  optional uint64 last_click_time = 1;
  optional uint32 source = 2; //0 ugc 1 大王
}


message GetUgcVersionReddotRsp {
  optional uint32 reddot = 1; // 大于0就显示红点
}

message ReportCreatorCenterReddotReq {
  optional uint32 id = 1;
}

message ReportCreatorCenterReddotRsp {

}

message GetCreatorCenterReddotReq {
  optional uint32 type = 1; // 0 查询是否有红点 1 拉红点列表
}

message GetCreatorCenterReddotRsp {
  optional uint32 reddot = 1; // 大于0就显示红点

  message Reddot {
    optional string tab = 1; //格式：造梦学院/造梦系列课/造梦进修课
    optional string anchor_point = 2; // 锚点id，活动id或者素材id
    optional uint32 id = 3; // 红点id，上报时间后用
  }

  repeated Reddot list = 2; // 仅在1的时候返回
}


// 客户端上传日志通知
message UploadLogReportReq {
  optional int64 stain_id = 1; // 染色序列号
  optional string url = 2; // 日志文件地址
  optional int64 start_time = 3; // 日志开始时间
  optional int64 end_time = 4; // 日志结束时间
}

message UploadLogReportRsp{}

message UgcBugReportReq {
  optional uint64 ugc_id = 1; // 大王这里直接填写关卡id
  optional string report_type = 2;
  optional string report_detail = 3;
  repeated string report_imgs = 4;

  optional uint32 source = 5; // 来源 0 ugc 1 大王
  optional uint32 modeid = 6; // 模式id
  optional string battle_id = 7; // 对局id
  optional string ext_info = 8; // 扩展参数：json格式{"ai":0} ai枚举 0无ai，1己方有ai 2对方有ai 3都有ai
}

message UgcBugReportRsp {
}

message UgcResearchGetResearchUrlReq {
  optional uint32 type = 1; // 0 ugc 1 大王
}

message UgcResearchGetResearchUrlRsp {
  optional uint64 start_at = 1;
  optional uint64 end_at = 2;
  optional string url = 3;
}

// 全服地区排行榜协议
message PlatRankData {
  message Region {
    optional uint32 level = 1;       // 行政级别
    optional uint32 code = 2;        // 行政编码
  }
  optional string rank_id = 1;
  optional uint32 rank_size = 2;  // 上榜单人数
  optional PlatRankData.Region region = 3;
  repeated PlayerCommonRankInfo rank_list = 4; // 排行榜数据
}

message GetRankByUserReq {
  optional string rank_id = 1;
  optional uint64 uid = 2;
  optional PlatRankData.Region region = 3;
  optional bool no_cache = 4;          // 不走缓存，一般调试使用。
  optional bool fast_query = 5;        // 快速查询，排名1000以内
}

message GetRankByUserRsp {
  optional PlatRankData rank_data = 1;
}

message GetRankByScoreReq {
  optional string rank_id = 1;
  optional PlatRankData.Region region = 2;
  optional uint32 score = 3;
  optional uint32 count = 4;
  optional bool no_cache = 5;
  optional bool fast_query = 6;        // 快速查询，分数在排名1000以内
}

message GetRankByScoreRsp {
  optional PlatRankData rank_data = 1;
}

message GetRankTopKReq {
  optional string rank_id = 1;
  optional uint32 start_rank = 2;          // 从1开始
  optional uint32 count = 3;
  optional PlatRankData.Region region = 4;
}

message GetRankTopKRsp {
  optional PlatRankData rank_data = 1;
}

message UpdateRankByUserReq {
  optional string rank_id = 1;
  optional uint64 uid = 2;
  repeated PlatRankData.Region region = 3;
  optional uint32 score = 4;
  optional uint64 update_time_ms = 5;              // 毫秒
  optional uint64 req_id = 6;
}

message UpdateRankByUserRsp {
}

message BatchUpdateRankByUserReq {
  repeated UpdateRankByUserReq req_list = 1;
}

message BatchUpdateRankByUserRsp {
  repeated uint64 failed_req_id_list = 1;
}

message DeleteRankByUserReq {
  optional string rank_id = 1;
  optional uint64 uid = 2;
  repeated PlatRankData.Region region = 3;
}

message DeleteRankByUserRsp {
}

message DeleteRankReq {
  optional string rank_id = 1;
}

message DeleteRankRsp {
}

message MoveRankByUserReq {
  optional string rank_id = 1;
  optional uint64 uid = 3;
  optional uint32 score = 4;
  optional uint64 update_time_ms = 5;       // 毫秒
  repeated PlatRankData.Region from_region_list = 6;
  repeated PlatRankData.Region to_region_list = 7;
}

message MoveRankByUserRsp {
}


message GetNonGameFriendsReq {
  optional uint32 page = 1; // 1开始计数，目前不生效，默认返回300，最多也只有300
}


message GetNonGameFriendsRsp{
    message User {
        optional string openid = 1; // openid
        optional string nick = 2; // 昵称
        optional string avatar = 3; // 头像
    }

    repeated User list = 1; // 非同玩好友的用户列表
}

// GetActiveTabNoticeConf cmd=2113
message GetActiveTabNoticeConfReq {}

message ActiveTabNoticeConf {
  message NoticeConf {
    optional int32 type = 1; // 0=图片；1=文字
    optional string text_conf = 2; // 文字内容
    optional string image_conf = 3; // 图片链接
    optional string title = 4; // 标题
  }

  message JumpConf {
    optional int32 type = 1; // 0=无需跳转；2=游戏内活动ID；3=H5页面；4=跳转pixui活动；5=星世界无极跳转
    optional string schema = 2; // 跳转schema
  }

  message DeliverUserConf {
    message Params {
      optional string param = 1; // 参数 whiteregchannels/blackregchannels/whiteversions/blackversions/blackopenids/platform/mobile/degree/level/registerTimeLimit
      optional string op = 2; // 运算逻辑 EQ/NEQ/GTE/LTE/GT/LT/IN/NOTIN/RANGE
      optional string value = 3; // 参数内容
    }

    optional int32 type = 1; // 0=所有用户；1=自定义投放范围
    optional string op = 2; // AND / OR
    repeated Params params = 3; // 参数条件列表
  }

  message ReddotConf {
    optional bool enable = 1; // 是否触发红点
    optional int32 show_days = 2; // 红点展示天数 输入“1”表示首次红点，“0“表示无红点，其他数字表示每日红点的持续天数（例如：输入“4”表示红点每日出现，并持续4天）
  }

  message NonAppShareConf {
    optional bool enable = 1; // 是否启动通用弹窗或toast提示
    repeated string channels = 2; // 启用通用弹窗的渠道号列表
  }

  message UIDPackageInfo {
    optional int32 type = 1; // 0=全量用户（不配置号码包）；1=白名单号码包；2=黑名单号码包
    optional string package_id = 2; // 号码包ID

    optional int32 hit_result = 10; // 0=未命中；1=命中
  }

  optional string id = 1;
  optional string title = 3; // 正标题
  optional string sub_title = 4; // 副标题
  optional int32 activity_tab_id = 6; // 引用《H_活动中心配置》表-活动页签sheet-页签ID
  optional int64 begin_time = 7; // 活动开始时间
  optional int64 end_time = 8; // 活动结束时间
  optional NoticeConf notice_conf = 9; // 公告配置
  optional int32 seq = 10; // 公告排序 顺序决定该活动在对应一级页签内的展示排列顺序，顺序越小越靠前。
  optional JumpConf jump_conf = 11; // 跳转配置
  optional int32 screen_type = 12; // 端内浏览器横竖屏设置 可选择端内MSDK浏览器打开方式
  optional int32 dailyshowtimes = 13; // 每日展示次数
  optional int32 haveshowmaxtimeslifetime = 14; // 历史展示次数
  optional DeliverUserConf deliver_user_conf = 15; // 活动投放用户配置
  optional ReddotConf reddot_conf = 16; // 红点配置
  optional NonAppShareConf non_app_share_conf = 17; // 非APP端分享跳转支持
  optional UIDPackageInfo uid_package_info = 18; // 用户号码包信息
}

message GetActiveTabNoticeConfRsp {
  repeated ActiveTabNoticeConf active_tab_notice_conf = 1;
}

// GetUGCGuideBanner 运营横条&造梦空间气泡 cmd=2116
message GetUGCGuideBannerReq {
  optional int32 need_guide_banner = 1; // 是否拉取运营横条 0=不拉取 1=拉取
  optional int32 need_editor_bubble = 2; // 是否拉取造梦空间气泡
}

message GetUGCGuideBannerRsp {
  message UGCGuideBanner {
    optional string id = 1; // 运营横条ID
    optional string text = 2; // 运营横条文案
    optional int32 jump_id = 3; // 跳转ID
    optional string jump_param = 4; // 跳转链接参数
    optional int32 show_timeout = 5; // 展示时长（秒）
  }

  message EditorBubble {
    optional string id = 1; // 气泡ID
    optional int32 type = 2; // 气泡类型。0=主题赛上新，1=编辑器更新，2=造梦学院上新，3=百万奖金
    optional int32 show_timeout = 3; // 展示时长（秒）
    optional UGCGuideBanner banner = 4; // 点击气泡后展示的横条信息
  }

  optional UGCGuideBanner guide_banner = 1; // 运营横条
  optional EditorBubble editor_bubble = 2; // 造梦空间气泡
}

// 模型生成红点 cmd=2209
message GetAIGenerate3DRedDotReq {}

message GetAIGenerate3DRedDotRsp {
  optional int32 reddot = 1; // 0=不展示，1=模型生成中，2=模型生成完成
}

// 消除模型生成红点 cmd=2210
message HideAIGenerate3DRedDotReq {
  optional int32 reddot = 1;
}

message HideAIGenerate3DRedDotRsp {}

// 获取地图模板信息
message BatchGetEDUMapTemplateInfoReq {
  repeated string template_ids = 1; // 模板ID列表
}

message BatchGetEDUMapTemplateInfoRsp {
  repeated EDUMapTemplate template_list = 1; // 模板列表
}

// 收藏话题
message LGZXV2CollectTopicReq {
  optional int32 id = 1; // 话题id
  optional string name = 2; // 话题名称
  optional int32 cmd_type = 3; // 0-收藏 1-取消收藏
  optional int32 source = 4; // 1-话题搜索 2-灵感中心
  optional uint32 topic_type = 5; // 话题类型 2-蓝色 1-金色
}

message LGZXV2CollectTopicRsp {
}

message LGZXV2GetTopicCollectInfoReq {
    optional int32 id = 1; // 话题id
    optional string name = 2; // 话题名称
    optional uint32 topic_type = 3; // 话题类型 2-蓝色 1-金色
}

message LGZXV2GetTopicCollectInfoRsp {
  optional int32 collect_num = 1; // 话题收藏数
  optional bool has_collected = 2; // 是否已收藏
  optional uint64 hot_score = 3; // 热度值
}

// 学院页Tab 1003014
message GetEDUPageTabListReq {}

message GetEDUPageTabListRsp {
  message Tab {
    optional string id = 1; // Tab ID
    optional string name = 2; // Tab名称
  }

  repeated Tab tab_list = 1; // Tab列表
}

// 地图详情
message EDUFeaturedMapInfo {
  message CreatorInfo {
    optional string id = 1; // 创作者ID
    optional string name = 2; // 创作者名称
    optional string head_url = 3; // 创作者头像
  }

  optional string name = 1; // 地图名称
  repeated string cover_list = 2; // 地图封面
  optional string desc = 3; // 地图描述
  repeated string play_tag_list = 4; // 地图玩法标签
  optional string gold_topic = 5; // 金色话题
  repeated string blue_topic_list = 6; // 蓝色话题
  optional CreatorInfo creator = 7; // 创作者信息
  repeated CreatorInfo co_creator_list = 8; // 联合创作者列表
  repeated string subject_list = 9; // 学科标签列表
  repeated string tutorial_id_list = 10; // 教程ID列表

  optional int64 play_cnt = 101; // 游玩次数
  optional int64 like_cnt = 102; // 点赞次数
}

// 获取精选地图tab列表 1003015
message GetEDUFeaturedMapTabListReq {}

message GetEDUFeaturedMapTabListRsp {
  message Tab {
    optional string id = 1; // tab id
    optional string name = 2; // tab名称
  }

  repeated Tab tab_list = 1; // 分类tab列表（不包含【全部】）
}

// 获取精选地图列表 1003016
message GetEDUFeaturedMapListReq {
  optional string tab_id = 1; // tab id。不填则表示全部
  optional int32 limit = 2; // 数量
  optional int32 offset = 3; // 当前偏移量
}

message GetEDUFeaturedMapListRsp {
  message MapInfo {
    optional string id = 1; // 地图ID
    optional EDUFeaturedMapInfo info = 2; // 地图信息
  }

  repeated MapInfo map_list = 1; // 地图列表
  optional int32 offset = 2; // 当前偏移量
  optional int32 total = 3; // 总数
}

// 获取星图馆地图列表 1003017
message GetEDUStarMapListReq {
  optional int32 limit = 1; // 数量
  optional int32 offset = 2; // 当前偏移量
}

message GetEDUStarMapListRsp {
  message MapInfo {
    optional string id = 1; // 地图ID
    optional EDUFeaturedMapInfo info = 2; // 地图信息
    optional string play_video_url = 3; // 游玩视频url
    optional string comment_icon = 4; // 评价icon
    optional string comment_text = 5; // 评价文案
  }

  repeated MapInfo map_list = 1; // 地图列表
  optional int32 offset = 2; // 当前偏移量
  optional int32 total = 3; // 总数
}

// -------------------------- 板块页相关 ----------------------------

// 学院页课程首页板块配置
enum EDUPageBlockType {
  EDUPageBlockTypeNone = 0;
  EDUPageBlockTypeActivityBannerList = 1; // 活动banner列表
  EDUPageBlockTypeStandardGuideCourseList = 2; // 萌新必看
  EDUPageBlockTypeFeaturedCourseList = 3; // 精品课程列表
  EDUPageBlockTypeHotKVList = 4; // 热门KV列表
  EDUPageBlockTypeCourseCollection = 5; // 主题课合集
}

message EDUPageBlock {
  optional string id = 1; // 板块id
  optional string title = 2; // 板块标题
  optional string sub_title = 3; // 副标题
  optional int32 type = 4; // 板块类型 EDUPageBlockType
  optional bool show_has_more = 5; // 是否展示【更多】

  message Content {
    message ActivityBannerList {
      message Banner {
        optional string id = 1; // banner ID
        optional string title = 2; // 标题
        optional string cover = 3; // 封面
        optional string url = 4; // 跳转链接
      }

      repeated Banner banner_list = 1; // banner列表
    }

    optional ActivityBannerList activity_banner_list = 1; // 活动banner列表 EDUPageBlockTypeActivityBannerList

    message StandardGuideCourseList {
      repeated EDUCourse course_list = 1; // 萌新必看课程列表
    }

    optional StandardGuideCourseList standard_guide_course_list = 2; // 萌新必看 EDUPageBlockTypeStandardGuideCourseList

    message FeaturedCourseList {
      repeated EDUCourse course_list = 1; // 精品课程列表
    }

    optional FeaturedCourseList featured_course_list = 3; // 精品课程列表 EDUPageBlockTypeFeaturedCourseList

    message HotKVList {
      message KV {
        optional int32 type = 1; // 0=跳转链接kv；1=课程信息kv
        optional string kv_image_url = 2; // KV图片
        optional string jump_url = 3; // 跳转链接 type==0时读
        optional EDUCourse course = 4; // 课程信息 type==1时读
      }

      repeated KV kv_list = 1; // 热门KV列表
    }

    optional HotKVList hot_kv_list = 4; // 热门KV列表 EDUPageBlockTypeHotKVList

    message CourseCollection {
      repeated EDUCourse course_list = 1; // 主题课列表
    }

    optional CourseCollection course_collection = 5; // 主题课合集 EDUPageBlockTypeCourseCollection
  }

  optional Content content = 1001; // 板块内容
}

// 获取板块列表 1003018
message GetEDUPageBlockListReq {
  optional string page_id = 1; // 页面id
  optional string platform = 2; // 平台（AndroidMobile/iPhone/AndroidTablet/iPad/desktop）
}

message GetEDUPageBlockListRsp {
  repeated EDUPageBlock block_list = 1; // 板块列表
}

// 获取APP主题课程详情 1003019
message GetEDUCourseInfoReq {
  optional string course_id = 1; // 课程ID
  optional string activity_id = 2; // 活动ID（学院首页场景不填）
  optional bool without_chapters = 3; // 是否不返回章节列表
}

message GetEDUCourseInfoRsp {
  optional EDUCourse course_info = 1; // 课程信息
}

// 复合搜索 1003020
message EDUSearchReq {
  optional string key_word = 1; // 搜索关键词
  optional int32 source = 2; // 0=综合；1=教程；2=主题课
}

message EDUSearchRsp {
  repeated Tutorial tutorial_list = 1; // 教程列表
  repeated EDUCourse course_list = 2; // 课程列表
}

// 设置本次学习的课程
message SetEDULearnCourseReq {
  optional string course_id = 1; // 课程ID
  optional string activity_id = 2; // 活动ID，可能为空
}

message SetEDULearnCourseRsp {}

// 获取前一次学习的课程
message GetEDULastLearnCourseReq {}

message GetEDULastLearnCourseRsp {
  optional EDUCourse course_info = 1; // 前一次学习的课程。可能为空
  optional string activity_id = 2; // 活动ID，可能为空
}

message GetTemplateSubStatusReq {
  repeated string template_ids = 1;
  optional bool need_openlink = 2;
}

message GetTemplateSubStatusRsp {
  optional uint32 status= 1; // 0 未订阅 1 已经订阅
  optional string openlink = 2; // 跳转微信的地址，10分钟内有效
}

// 获取精选地图详情 cmd=1003023
message BatchGetEDUFeaturedMapInfoReq {
  repeated string map_ids = 1; // 地图ID列表
}

message BatchGetEDUFeaturedMapInfoRsp {
  message MapInfo {
    optional string id = 1; // 地图ID
    optional EDUFeaturedMapInfo info = 2; // 地图信息
  }

  repeated MapInfo map_list = 1; // 地图列表
}

message GetMagazineReq {

}
message GetMagazineRsp {
    message Content {
        optional uint32 id = 1;
        optional string tag = 2;
        optional string title = 3;
        optional string comment = 4;
        optional string video_url = 5;
        optional uint64 start_time = 6;
        optional string plat_nick = 7;
        optional string plat_head_url = 8;
        optional string show_url = 9;
        optional uint64 like_num = 10;
        optional string plat = 11;
        optional uint64 like_time = 12;  // 已经点赞
        optional string video_cover_url = 13;  // 视频封面
    }
    message Module {
        optional uint32 module_id = 1;
        optional string main_title = 2;
        optional string sub_title = 3;
        repeated Content list = 4;
    }
    message Reward {
      optional string reward_name = 1;
      optional string reward_icon = 2;
      optional uint32 reward_num = 3;
    }
    optional string id = 1;
    optional uint32 period = 2;
    optional uint64 start_time = 3;
    optional string main_title = 4;
    optional string guide_content = 5;
    optional string reward_name = 6;          // 废弃
    optional string reward_icon = 7;          // 废弃
    optional uint32 reward_num = 8;           // 废弃
    optional uint32 reward_seconds = 9;
    optional uint64 reward_report_time = 10;  // 已经上报，代表可领取状态
    optional uint64 reward_update_time = 11;  // 已经更新，代表已经领取
    repeated Module list = 12;
    repeated Reward reward_list = 13; // 奖励列表
}

message MagazineReportReq {
    optional string id = 1;
}
message MagazineReportRsp {

}
message MagazineTakeRewardReq {
    optional string id = 1;
}
message MagazineTakeRewardRsp  {

}
message MagezineLikeReq {
    optional uint32 module_id = 1;
    optional uint32 content_id = 2;   // 笔记ID
}
message MagezineLikeRsp {

}


message GetCoCreatedInviteCodeReq {
  optional uint64 map_id = 1;
}

message GetCoCreatedInviteCodeRsp {
  optional string code = 1;
}

message CancelCoCreatedInviteCodeReq {
}

message CancelCoCreatedInviteCodeRsp {
}

message JoinCoCreatedReq {
  optional string code = 1;
}

message JoinCoCreatedRsp {
  optional uint64 map_id = 1;
}

message BatchGetAppUserInfoReq {
  repeated string gopenid = 1;
  repeated uint64 creator_id = 2;
}

message PlatAppUserInfo {
    optional string gopenid = 1;
    optional uint32 login_type =2;
    optional string openid = 3;
    optional int64 creator_id = 4;
    optional uint32 agree_status = 5;
    optional string game_gopenid = 6;
    optional string nickname = 7;
    optional string avatar = 8;
    optional uint32 gender = 9;
    optional int64 binded_game_uid = 10;
    optional uint32 binded_game_plat_id = 11;
    optional int64 binded_game_creator_id = 12;
    optional uint32 online = 13; // 0:离线 1:在线
}

message BatchGetAppUserInfoRsp {
  map<string, PlatAppUserInfo> user_info = 1;
}


message GetRecommendMapListReq {
    optional string keyword = 1;
    optional uint32 tag_id = 2; // 玩法标签
    optional uint32 type = 3; // 0 搜索 1 好友在玩 2 热门地图
}

message GetRecommendMapListRsp {
  repeated uint64 map_id_list = 1[packed=true];
}

message GetMinigameCollectionReq {
  optional string appid = 1;
  optional string ugc_id = 2; // 当前游玩的UGC ID
}

message GetMinigameCollectionRsp {
  repeated MinigameUGCMapInfo list = 1;
}

// 创作者主页地图数据
message MinigameUGCMapInfo {
  optional string id = 1; // 地图ID
  optional string title = 2; // 地图名称
  optional string cover_url = 3; // 地图封面
  optional string desc = 4; // 描述
  repeated string tags = 5; // 标签列表
  optional uint32 type = 6; // 类型
  optional string type_desc = 11; // 类型

  optional MinigameUGCMapData data_overview = 7;
  optional MinigameUserData creator_data = 8; // 只会有ID、头像、昵称

  optional bool is_played = 9; // 我是否玩过
  optional bool is_collected = 10; // 是否收藏
  optional uint32 status = 12 ; //状态 非2则为下架
  optional uint64 create_time = 13; // 创建时间
  optional string cover_large = 14; // 地图封面大图
}

message MinigameUserData {
  optional string id = 1;
  optional string nick_name = 2;
  optional string avatar_url = 3;
  optional bool is_official_creator = 4;
  optional bool hide_ugc_info = 5;
  optional bool is_friend = 6;
  optional bool is_sub = 7;
  optional string creator_id = 8;// 创作者id
  optional string short_uid = 9;// 短的uid
}
message MinigameUGCMapData {
  optional string total_played_num = 1; // 累计游玩
  optional string total_liked_num = 2; // 累计赞
  optional string total_favour_num = 3; // 累计收藏
}

message UgcActivityGetBPShortReq {
}

message UgcActivityGetBPShortRsp {
    message Item {
        optional uint32 status = 1; // 0 已领取 1 未领取 2 已解锁 3 未解锁
        optional uint32 score = 2;
        optional uint32 item_id = 3;
    }

    repeated Item items = 1;
}

message UgcActivityGetBPIconReq {
}

message UgcActivityGetBPIconRsp {
    optional uint32 id = 1;
    optional uint64 begin_time = 2;
    optional uint64 end_time = 3;
    optional uint32 type = 4; // 1未领取奖励 2配置 上面字段仅在2情况下返回
    optional uint32 item_id = 5;
}


message CheckMapNeedDefaultCoverReq {
    optional uint64 map_id = 1;
}

message CheckMapNeedDefaultCoverRsp {
    optional string default_cover = 1;
}

message GetBiRecommendResultReq {
  optional string abid = 1; // 实验组id
  optional uint32 type = 2; // 1 发现页 2 ugc玩法选择
}

message GetBiRecommendResultRsp {
  repeated string item_list = 1;
}


message GetCurrentTriggerPrecisionActivityReq {
}

message GetCurrentTriggerPrecisionActivityRsp {
  optional string id = 1; // 当前触发的活动id
  optional uint64 begin_time = 2;
  optional uint64 end_time = 3;
  optional uint32 reddot = 4; // 大于0就显示红点
}


message GetUGCEntranceBubbleReq {}

message GetUGCEntranceBubbleRsp {
    optional UGCEntranceBubblePlat bubble = 1;
}

// 星世界入口气泡
message UGCEntranceBubblePlat {
  optional int32 bubble_type = 1; // 气泡类型 UgcStarWorldEntranceBubbleType
  optional int32 bubble_id = 2; // 气泡ID
  optional int64 expire_time = 3; // 过期时间
  optional int32 tips_type = 4; // 样式
  optional string tips_text = 5; // 气泡文本
  optional string tips_icon = 6; // 气泡图标
  optional int32 tips_icon_type = 7; // 气泡图标类型
  optional string tips_background = 8; // 气泡背景底色
  optional string jump_id = 9; // 跳转ID
  optional string jump_params = 10; // 跳转参数
}
