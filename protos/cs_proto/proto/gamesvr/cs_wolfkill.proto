syntax = "proto2";

option cc_generic_services = false;
package com.tencent.wea.protocol;

import "g6_common.proto";
import "common.proto";
import "ResKeywords.proto";

// 获取大师之路的奖励
message WolfkillToMasterReward_C2S_Msg {
}

message WolfkillToMasterRewardItem {
  optional int32 to_master_id = 1;
  optional int32 recv = 2; //是否已经领取
}

message WolfkillToMasterReward_S2C_Msg {
  optional int32 score = 1;
  optional int32 rank = 2;
  repeated WolfkillToMasterRewardItem reward_list = 3;
}

// 领取大师之路的奖励
message WolfkillToMasterRewardRecv_C2S_Msg {
    optional int32 to_master_id = 1;
}

enum WolfkillToMasterRewardRecvResult {
  RECV_SUCCUSS = 0;
  RECV_NO_CONFIG = 1; // 领取id错误
  RECV_RECVED = 2; // 已经领取过
  RECV_LOCK = 3; // 没达到领取条件
}

message <PERSON>killToMasterRewardRecv_S2C_Msg {
  optional int32 result = 1;
}


//装扮道具穿戴
message WolfKillBagDressUpItem_C2S_Msg {
  optional int64 itemUUID = 1;
  optional int32 targetPos = 2;
  optional int64 combinationId = 3;
  optional com.tencent.wea.xlsRes.InteractionType type = 4;
}

message WolfKillBagDressUpItem_S2C_Msg {
  optional int64 itemUUID = 1;
  optional int32 targetPos = 2;
  optional int64 combinationId = 3;
  optional com.tencent.wea.xlsRes.InteractionType type = 4;
}


// 赛季奖励领取
message WolfKillSeasonRewardRecv_C2S_Msg {
}

enum WolfKillSeasonRewardRecvResult {
  SEASON_RECV_SUCCUSS = 0;
  SEASON_RECV_EXCEED = 1; // 当天领取超限
  SEASON_RECV_FAIL = 2; // 领取失败
}

message WolfKillSeasonRewardRecv_S2C_Msg {
  optional int32 result = 1; // 见枚举WolfKillSeasonRewardRecvResult
  optional int32 item_id = 2;
  optional int32 item_num = 3;
  optional int32 recv_num = 4; // 当天领取次数
  optional int32 recv_total = 5; // 当天领取总次数
}


enum WolfKillTimeLimitedEvent {
  EVENT_NONE = 0;
  EVENT_EASTER_EGG = 1;    // 彩蛋局
  EVENT_OP_ACTIVITY = 2;   // 运营活动1
  EVENT_LIVE = 3;          // 直播
  EVENT_OP_ACTIVITY2 = 4;  // 运营活动2
  EVENT_OP_NEW_USER = 5;  // 新手礼
}

enum WolfKillEventJumpType {
  EVENT_JT_UNKNOWN = 0;
  EVENT_JT_ACTIVITY = 1; // 活动
  EVENT_JT_COMMON = 2; // 通用跳转
}

message WolfKillTimeLimitedItem {
  optional int32 timeLimitEvent = 1; // 活动类型 WolfKillTimeLimitedEvent
  optional int64 startTime = 2; // 活动开始时间
  optional int64 endTime = 3; // 活动结束时间
  optional int32 jumpID = 4; // 跳转ID
  optional string tipText = 5; // 标签文本
  optional int32 jumpType = 6; // 跳转类型
  optional string pic = 7; // 图片
  optional int32 package_type = 8; // 分包类型
}

// 获取狼人限时活动
message WolfKillRoomTimeLimitStatus_C2S_Msg {
}

message WolfKillRoomTimeLimitStatus_S2C_Msg {
  repeated int32 timeLimitEvent = 1; // 废弃
  repeated WolfKillTimeLimitedItem timeLimited = 2;
}

// 获取狼人杀公告
message WolfKillAnnouncementEntity {
  optional string title = 1;
  optional string content = 2;
  optional bool is_up = 3;
}

message WolfKillAnnouncement_C2S_Msg {
}

message WolfKillAnnouncement_S2C_Msg {
  optional string version = 1;
  repeated WolfKillAnnouncementEntity announce = 2;
  optional bool topic_season = 3;
}

message WolfKillGetFeedbackCount_C2S_Msg {

}

message WolfKillGetFeedbackCount_S2C_Msg {
  optional int32 count = 1;
}

message WolfKillAddFeedback_C2S_Msg {
  optional int32 type = 1;
  optional string content = 2;
  optional int32 graphysics_quality = 3;
}

message WolfKillAddFeedback_S2C_Msg {
  optional int32 count = 1;
}

message WolfKillNewUserTips_C2S_Msg {
  optional int32 event_id = 1;
}

message WolfKillNewUserTips_S2C_Msg {
  optional int32 event_id = 1;
  optional int32 count = 2; // 此次提交后的count
}

message WolfKillFaceEntity {
  optional int32 id = 1;            // id
  optional string describe = 2;     // 文本描述
  optional int32 order = 3;         // 排序
  optional int32 jump_id = 4;       // 跳转id
  optional string pic_url = 5;      // 拍脸图连接
}

message WolfKillFace_C2S_Msg {

}

message WolfKillFace_S2C_Msg {
  repeated WolfKillFaceEntity face = 1;
}

message WolfKillComeBackRewardItem {
  optional int32 item_id = 1;
  optional int32 item_amount = 2;
}

message WolfKillComeBackReward {
  optional int32 id = 1; // 奖励id
  repeated WolfKillComeBackRewardItem select_list = 2; // 奖品选择列表
  optional bool is_has_get = 3; // 是否已领
}

message WolfKillComeBackInfo {
  optional int64 open_ts = 1; // 系统开始时间
  optional int64 end_ts = 2; // 系统结束时间
  optional int32 unlock_count = 3; // 解锁数量
  optional int32 finish_count = 4; // 完成数量
  repeated WolfKillComeBackReward reward_list = 5; // 奖励信息
}

// 获取回归系统信息
message WolfKillGetComeBackInfo_C2S_Msg {

}

message WolfKillGetComeBackInfo_S2C_Msg {
  optional bool is_open = 1;  // 系统是否开启
  optional WolfKillComeBackInfo info = 2; // 系统信息
}

// 获取回归系统奖励
message WolfKillGetComeBackReward_C2S_Msg {
  optional int32 id = 1;  // 奖励id
  optional int32 index = 2; // 奖品索引
}

message WolfKillGetComeBackReward_S2C_Msg {
  optional int32 id = 1;
  optional int32 index = 2;
}


// 珍宝系统使用
message WolfKillTreasureUse_C2S_Msg {
  optional int32 id = 1;
  optional bool  in_use = 2; // false 代表禁用，true代表使用
}

// 珍宝系统使用
message WolfKillTreasureUse_S2C_Msg {
  optional int32 result = 1;  //0 代表成功，其他代表失败
}

// 狼人珍宝新已读，这里没用通用的“新”功能，是因为还需要等级返回
message WolfKillTreasureRead_C2S_Msg {
}

// 狼人珍宝新已读
message WolfKillTreasureRead_S2C_Msg {
}

// 狼人新的珍宝新已读，这里没用通用的“新”功能，是因为没使用物品
message WolfKillTreasureLevelRead_C2S_Msg {
  optional int32 unlock_id = 1;
}

// 狼人珍宝新已读
message WolfKillTreasureLevelRead_S2C_Msg {
}

// 刷新等级，等级新开放的时候涉及到等级变更
message WolfKillTreasureRefreshLevel_C2S_Msg {
}

// 珍宝系统使用
message WolfKillTreasureRefreshLevel_S2C_Msg {
  optional int32 result = 1;  //0 代表无更新，1代表有更新
}


message WolfKillVocationInfo{
  optional int32 id = 1;
  optional int32 Preparations_DefaultUnLock = 2;
  optional int32 Preparations_DefaultShow = 3;
  optional int32 Identity_ItemUnlock = 4;
  optional string LowestVersion = 5;
  optional string HighestVersion = 6;
  optional string StartTime = 7;
  optional string EndTime = 8;
  optional int32 useTimes = 9; // 身份卡使用次数
  optional int64 lastUseMs = 10; // 上次身份卡使用时间（毫秒）
}

// 狼人身份解锁
message WolfKillVocation_C2S_Msg {
}

// 狼人身份解锁
message WolfKillVocation_S2C_Msg {
  repeated WolfKillVocationInfo vocation_list = 1;
}


message WolfKillLevelInfo{
  optional int32 Id = 1;	//关卡id
  optional string LowestVersion = 25;
  optional string HighestVersion = 26;
  repeated string ExcludeVersions = 40;
}


message WolfKillLevel_C2S_Msg {
}

message WolfKillLevel_S2C_Msg {
  repeated WolfKillLevelInfo level_info = 1;
}


// 狼人珍宝系统数值
message WolfKillTreasureData_C2S_Msg {
}


message NR3E3TreasureUnlockItem {
  optional int32 id = 1;
  repeated int32 items = 4;
  repeated int32 itemNums = 5;
  optional int32 canUse = 6;
  optional int32 canDayUse = 7;
  optional int32 canWeekUse = 9;
  repeated int32 giveItems = 10; // 使用时赠送。目前仅支持每周使用
  repeated int32 giveItemNums = 11; // 使用时赠送
  optional int32 shareFlag = 12; // 1 共享职业，2. 共享动画
  optional int32 shareNum = 13; // 共享人数
  optional int32 shareDays = 14; // 共享天数
}

message NR3E3TreasureItem{
  optional int32 level = 1;
  optional int32 upgradeNum = 2;
  repeated NR3E3TreasureUnlockItem treasure = 3;
  optional string minVersion = 4;
  optional string startTime = 5; //采用字符串格式为【HH:MM:SS】表达
}

// 狼人珍宝系统数值
message WolfKillTreasureData_S2C_Msg {
  repeated NR3E3TreasureItem treasure_list = 1;
}


// 狼人身份共享，新获得的身份在下次会自动共享
message WolfKillTreasureShare_C2S_Msg {
  optional  int32 shareType = 1; // 1身份共享，2动画共享
  optional  int64 playerUid = 2; // 分享给用户uid
  optional  int32 sharePos = 3; // 共享的位置，为1,2,3，最大不能超过配置的值
}


enum WolfKillTreasureShareResult {
  WOLFKILL_TREASURE_SHARE_SUCCUSS = 0;
  WOLFKILL_TREASURE_SHARE_POS_ERROR = 1; // 该位置已经分享过
  WOLFKILL_TREASURE_SHARE_USERREPEAT_ERROR = 2; // 该用户已经被自己分享过
  WOLFKILL_TREASURE_SHARE_LV_ERROR = 3; // 不具备改功能
  WOLFKILL_TREASURE_SHARE_LOW_VERSION_ERROR = 4; // 对方版本过低
}


// 狼人身份共享
message WolfKillTreasureShare_S2C_Msg {
  optional  int32 result = 1; // 见枚举WolfKillTreasureShareResult。0代表成功，1为该位置已经分享过，2为该用户已经被自己分享过，3为功能为开通
}

message WolfKillShieldVocation {
  optional int32 campType = 1;
  repeated int32 vocationList = 2;
}

// 屏蔽狼人身份
message WolfKillShieldVocation_C2S_Msg {
  repeated WolfKillShieldVocation data = 1;
}

// 屏蔽狼人身份
message WolfKillShieldVocation_S2C_Msg {
  repeated WolfKillShieldVocation data = 1;
}

// 获取狼人屏蔽身份
message GetWolfKillShieldVocation_C2S_Msg {
}

// 获取狼人屏蔽身份
message GetWolfKillShieldVocation_S2C_Msg {
  repeated WolfKillShieldVocation data = 1;
  optional int32 level = 2;
}

message WolfKillMonthCardInfo {
  optional int32 totalDay = 1; // 累计开通天数
  optional int64 endTs = 2; // 结束时间
  optional int32 freeGiftNum = 3; // 当天可领取的免费礼包个数
}

// 获取狼人月卡信息
message GetWolfKillMonthCardInfo_C2S_Msg {
}

message GetWolfKillMonthCardInfo_S2C_Msg {
  optional WolfKillMonthCardInfo info = 1;
}

// 开通月卡
message OpenWolfKillMonth_C2S_Msg {
}

message OpenWolfKillMonth_S2C_Msg {
}

// 狼人月卡信息推送
message WolfKillMonthCardNtf {
  optional WolfKillMonthCardInfo info = 1;
}

// 领取免费礼包
message GetWolfKillMonthFreeGift_C2S_Msg {
}

message GetWolfKillMonthFreeGift_S2C_Msg {
  optional int32 freeGiftNum = 1;
  repeated RewardItemInfo rewardList = 2;
}

// 获取mmr
message GetWolfKillMMRScore_C2S_Msg {
}

message GetWolfKillMMRScore_S2C_Msg {
  optional int32 score = 1;
}