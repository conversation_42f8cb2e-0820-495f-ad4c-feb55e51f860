syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "base_common.proto";
import "common.proto";
import "ResKeywords.proto";
import "attr_BattleInfo.proto";
import "attr_ChatGroupKey.proto";
import "attr_LobbyInfo.proto";
import "attr_CompetitionBasicInfo.proto";

message BattleReportModData_C2S_Msg{
  optional int64 battle_id = 1;    // 
  optional string mod_data = 2;   // 
  optional int32 score = 3;   // 
  repeated KeyValueInfo KeyValueInfo = 4;
}

message BattleReportModData_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败
}

message BattleModDataLogin_C2S_Msg{
  optional int64 battle_id = 1;    // 需要登陆的战场
  optional string md5 = 2; // 客户端文件md5
}

message BattleModDataLogin_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败
  optional int32 preFrame = 2;         //上次下线的节点
  optional BattleFrameData frame_data = 3; // 初始化的数据
  repeated MemberBaseInfo memberList = 4; // 初始化的数据
  optional MemberBaseInfo selfInfo = 5; // 初始化的数据
  optional int64 svr_time = 6;
  optional string desModInfo = 7; //songid:id;diff:diff
  optional ResourceFileContent fileData = 8;// 在md5不一致时 会带上服务器的文件信息
  repeated MemberBaseInfo npcList = 9; // npc列表，针对特殊模式
}

message BattleRequestModData_C2S_Msg{
  optional int64 battle_id = 1;      // 需要登陆的战场
  optional int32 frame_start = 2;    // 主动请求的帧开头 [start,end]
  optional int32 frame_end = 3;      // 主动请求的帧结尾 -1 全都要
}

message BattleRequestModData_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败 
}

message BattleRequestModDataNtf{
  optional int64 battle_id = 1;            // 发送数据的玩家
  repeated BattleFrameData frame_datas = 3; // 一堆帧数据，太多会分包
}

message BattleSyncModDataNtf{
  optional int64 battle_id = 1;            // 发送数据的玩家
  optional BattleFrameData frame_data = 3; // 当前帧的数据
}

message BattleRequestModEnd_C2S_Msg{
  optional int64  battle_id = 1;    // 
  optional string mod_data = 2;    // 
  optional int32  score = 3;        // 
  repeated KeyValueInfo KeyValueInfo = 4;
}

message BattleRequestModEnd_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败 
}

message BattleSyncModTime_C2S_Msg{
  optional int64  battle_id = 1;    // 
  optional int64  clt_time = 2;
}

message BattleSyncModTime_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败 
  optional int64 clt_time = 2;
  optional int64 svr_time = 3;
}

message BattlePlayerReadyMod_C2S_Msg{
  optional int64  battle_id = 1;    // 
  optional int64 clt_time = 2;
}

message BattlePlayerReadyMod_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败 
  optional int64 clt_time = 2;
  optional int64 svr_time = 3;
}

message BattleSeedDateNtf{
  optional int64 battle_id = 1; // 发送数据的玩家
  optional int64 seed_data = 2; // 随机种子数据
}

message BattleModEndNtf{
  optional int64 battle_id = 1; // 战场id
  repeated BattleEndData end_data = 2; // 结束数据
  optional int32 isBreakRecord = 4;// 标志位 破纪录1
  optional BattleFrameData frame_end_data = 5; // 结束数据
  optional ItemArray items = 6; // 结算道具
}

message BattleModStartNtf{
  optional int64 battle_id = 1; // 发送数据的玩家
  optional int64 svr_time = 2; 
}

message QuitBattleNtf {
  optional int64 battle_id = 1; //退出的战场
  optional int32 quit_code = 2; // 参考QuitBattleCode
}

message BattleGiveUpMod_C2S_Msg{
  optional int64 battle_id = 1;      // 需要登陆的战场
  optional int64 starPId = 2;      // 放弃进入的sp世界ID
}

message BattleGiveUpMod_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败
}

message BattleGetOngoingDsAddr_C2S_Msg{
  optional int64 starPId  = 1;
}

message BattleGetOngoingDsAddr_S2C_Msg{
  optional string dsAddr = 1;       // 进行中的对战的ds地址
  optional string desModInfo = 2;   // 进行中的对战的ds关卡序列
  optional string dsAuthToken = 3;   // dsToken认证
  optional proto_ChatGroupKey chatGroupKey = 4; // 局内聊天key
  optional proto_ChatGroupKey sideChatGroupKey = 5; // 阵营聊天key
  optional UgcBriefInfo ugcBriefInfo = 6; // ugc对局的附加简要信息--废弃
  optional int32 matchType = 7; // 玩法id
  optional proto_CompetitionBasicInfo competitionBasicInfo = 8; // 赛事信息
  optional int64 battleId = 9;
  optional int64 starPId  = 10;
  optional int32 result = 11; //  0成功
  optional string sessionId = 12;   // ds房间sessionid
  optional int32 tabType = 13;   // ds房间abtest标签
  optional int32 errorCode = 14;
  optional int64 sceneId = 15;  //
  optional int32 pakType = 16; // 对局创建时所使用的包类型，如果是0，则是老逻辑，不区分包类型-废弃
  repeated int32 pakGroupIdList = 17; // 对局创建时所使用的分包id列表，为空则为老逻辑
}

message BattleExitNtf{
  optional int64 battle_id = 1;
  optional int32 end_code = 2;
}

message BattleSendExpression_C2S_Msg{
  optional int32 expressionID = 1;
}

message BattleSendExpression_S2C_Msg{
  optional int32 result = 1;         //0成功 其他失败
}

message BattleExpressionNtf{
  repeated ExpressionInfo expList = 2;
}

message BattleDsInfo {
  optional proto_BattleInfo battleInfo    = 1;
}

message LobbyDsInfo {
  optional proto_LobbyInfo lobbyInfo  = 1;
  optional ChatGroupKey groupKey      = 2;
  optional bool reconnect             = 3; // 是否重连之前的ds
}

message DsJumpInfo {
  optional DsBasicInfo dsBasicInfo      = 1; // ds 信息
  oneof DsInfo {
    LobbyDsInfo   lobbyDsInfo           = 2;
    BattleDsInfo  battleDsInfo          = 3;
  }
}

message DsBasicInfo {
  optional int64  id            = 1; //标识一局ds， battleId 或者 lobbyId
  optional string dsAddr        = 2; //ds地址信息
  optional string dsAuthToken   = 3; //DS认证Token
}

message DsJumpNtf {
  optional int32 result                   = 1; // 错误码，0：成功
  optional BattleCreateReason jumpReason  = 2; // 跳转原因
  optional DsJumpInfo dsJumpInfo          = 3; // 跳转信息
}

enum RecvRewardType{
  UnKown = 0;
  ShocksQualifyingReward = 1;
  SettlementSeason = 2;
}

message RecvRewardListNtf{
  optional int32 rewardType = 1;
  repeated ItemInfo items = 2;
  optional int32 needRecv = 3;// 0 不需要 1 需要领取 
}


message SetBattlePlayerClientInfo_C2S_Msg { //在局内，需要客户端自行检测同步给客户端的属性跟客户端维护的属性是否一致，不一致走这个协议同步下
  optional int64 battleId = 1;                                //战斗id
  optional BattlePlayerClientInfo battlePlayerClientInfo = 2; //这里玩家的uid不用传
}

message SetBattlePlayerClientInfo_S2C_Msg {
  
}
message BattlePlayerClientInfoModifyNtf { //数据修改的通知有延迟1秒
  optional int64 battleId = 1;                                 //战斗id,客户端可用于校验
  repeated BattlePlayerClientInfo battlePlayerClientInfos = 2; //有属性变更的战斗玩家客户端属性
}

// 战斗招募
message BattleRecruitPublish_C2S_Msg {
  optional int64 battle_id = 1; // 战场Id
  optional int32 availableTime = 2; //发布招募有效时间
  optional com.tencent.wea.xlsRes.QualifyingDegreeInfo qualifying_low_limit = 3; // 最低的段位限制
  optional int32 topic_id = 4; // 避免字符串被篡改

}

message BattleRecruitPublish_S2C_Msg {
  optional int32 result = 1;         //0成功 其他失败
}


// 取消战斗招募
message BattleRecruitUnPublish_C2S_Msg {
  optional int64 battle_id = 1; // 战场Id
}


message BattleRecruitUnPublish_S2C_Msg {
  optional int32 result = 1;         //0成功 其他失败
}

// 战斗招募加入
message BattleDirectJoin_C2S_Msg {
  optional int64 battle_id = 1; // 战场Id
  optional int32 fillBackType = 2; // 回填类型
  optional MatchRuleInfo matchRuleInfo = 3; // 匹配类型，可以不带
  optional RoomRecruit recruit = 4;  //
  optional int32 param1 = 5; //预留字段
  optional string pwd = 6; //密码

}

message BattleDirectJoin_S2C_Msg {
    optional int32 result = 1;
}

// 局内通用广播信息
message BattleCommonBroadcast_C2S_Msg {
  optional int64 battleId = 1;
  optional BattleBroadcastInfo broadcastInfo = 2;
}

message BattleCommonBroadcast_S2C_Msg {

}

message BattleBroadcastInfoNtf {
  optional int64 battleId = 1;
  optional BattleBroadcastInfo broadcastInfo = 2;
}

// 获取局内玩家的显示信息
message BattleMemberListGet_C2S_Msg {
  optional int64 battleId = 1;
}

message BattleMemberListGet_S2C_Msg {
  optional int32 result = 1;
  repeated MemberBaseInfo memberList = 2;  // 成员信息
  repeated BattleCamp ugcCamps = 3; // ugc地图阵营信息，如果为空则代表混战
}

// 对局队友同游提议
message BattlePartnerCoMatchProposal_C2S_Msg {
  optional int64 battleId = 1; // 对局id
}

message BattlePartnerCoMatchProposal_S2C_Msg {

}

message BattlePartnerCoMatchInfoNtf {
  optional int64 battleId = 1; // 对局id
  repeated BattlePlayerCoMatchInfo coMatchInfoList = 2; // 所有同阵营玩家的同游匹配信息
  optional int64 proposedTimeMs = 3; // 首次提出的时间，废弃
  optional int64 coMatchId = 4; // 最终达成一致时的
  optional int64 proposedEndTimeMs = 5; // 提议结束时间
  optional int64 battleSettlementStartTimeMs = 6; // 对局最终结算开始的时间
}

message BattlePartnerCoMatchConfirm_C2S_Msg {
  optional int64 battleId = 1; // 对局id
  optional bool agree = 2; // 是否同意 废弃
  optional int32 agreeFlag = 3; // 0:取消 1:同意 2:拒绝
}

message BattlePartnerCoMatchConfirm_S2C_Msg {

}

// 退出结算界面
message BattleEndSettlement_C2S_Msg {
  optional int64 battleId = 1; // 房间id
}

message BattleEndSettlement_S2C_Msg {

}

// 查询副玩法新手温暖局对应模式的战斗次数(包括关联的玩法模式)
message QuerySecondaryNewbieRelatedMatchTypeBattleCnt_C2S_Msg {
  repeated int32 matchTypeIds = 1;
}

message QuerySecondaryNewbieRelatedMatchTypeBattleCnt_S2C_Msg {
  map<int32, int32> matchTypeCntMap = 1;
}

message ExitBattleNtf {
  optional int32 errorCode = 1;
}

// 玩家准备进行场景切换
message BattleSceneSwitchNtf {
  optional int64 battleId = 1;  // 场景ID
  optional int64 sceneId = 2;  // 当前场景ID
  optional int64 targetSceneId = 3;  // 目标场景ID
}
