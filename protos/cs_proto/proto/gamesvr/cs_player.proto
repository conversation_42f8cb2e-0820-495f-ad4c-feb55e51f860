syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;
import "ResKeywords.proto";
import "common.proto";
import "base_common.proto";
import "attr_base.proto";
import "attr_RootAttr.proto";
import "attr_XiaoWoInfo.proto";
import "attr_PlayerGrayRuleData.proto";
import "game_common.proto";
import "attr_LevelRecordData.proto";
import "ResQAInvest.proto";
import "ResMatch.proto";
import "attr_PakDownloadInfo.proto";
import "attr_LobbyInfo.proto";

message AccountUnBind_C2S_Msg {
  required int32 accountType = 1;
}

message AccountUnBind_S2C_Msg {
  optional int32 accountType = 1; //成功解绑的账号类型
}

message GetAccountBindInfo_C2S_Msg {
}

message GetAccountBindInfo_S2C_Msg {
  repeated MsdkBindInfo bindList = 1; //绑定列表
}

message MsdkBindInfo {
  optional int32 accountType = 1; //账户类型 enum TconndApiAccount
  optional string userName = 2;   //用户名或昵称
  optional string pictureUrl = 3;  //头像URL地址
  optional int32  timestamp = 4;   //与该账号绑定的Unix时间戳
}

message UnimplementedMsg_S2C_Msg {
}

message Login_C2S_Msg {
  required string openid = 1; //玩家实际openid
  required int32 platId = 2; //ios:0 android:1 windows:2 mac:3
  optional int64 uid = 3; //希望登陆的uid
  optional string deviceId = 4; //设备ID海外没imei
  optional string accessToken = 6; //鉴权Token
  optional int64 registerUid = 7; // 服务器填写的注册用的uid, 大于0表示是注册
  optional string debugPortraitURL = 8; // 君主头像URL，仅对PC有效
  optional string clientVersion = 9; //客户端版本
  optional string systemHardware = 10; //移动终端机型 例:vivox9
  optional string telecomOper = 11; //运营商 例: chinaUnicom
  optional string netWork = 12; //3g,wifi,2g
  optional int32 channel = 13; //渠道号
  optional string resVersion = 15; //资源号
  repeated int32 recommendZone = 16; //注册时推荐的zoneid
  optional int32 deviceMemory = 17; //设备内存大小 M
  optional string systemSoftware = 18; //操作系统版本 例:ios13.4.1
  optional com.tencent.wea.xlsRes.DeviceLevel deviceLevel = 19;
  optional bool reconnect = 20;
  optional bytes safeSdkData = 21; //客户端上报的从安全sdk接口获取的数据
  optional string imei = 22; //android
  optional string idfa = 23; //ios
  optional string firebaseToken = 24; //firebase push token
  optional int32 modId = 25; //游戏类型id
  optional string oaid = 26; //匿名设备标识符,安卓上报(正确值为16/36/64位)(报原始信息,不要加密)
  optional string caid = 27; //匿名设备标识符,IOS上报(正确值为32位)(报原始信息,不要加密)  旧caid
  optional string payToken = 28;// qq专用payToken,wx accessToken 统一赋值到payToken
  optional string payPf = 29;// 平台信息
  optional string payPfKey = 30;// 平台token
  optional RegisterInitInfo registerInfo = 31; // 基础信息
  optional LoginStatus loginStatus = 32;  // 登陆态
  optional bool checkVersion = 33;  // 是否检测版本号, 服务器做保护仅在非线上环境有效
  optional string DSVersion = 34; //ds版本
  optional string editorInfo = 35; //云编辑信息
  optional string userAgent = 36; //用户浏览器代理信息
  optional string cpuHardware = 37; //手机型号，黑名单过滤用
  optional int32 platPrivileges = 38; // 平台特权情况, 详情见枚举PlatPrivilegesType所示
  optional int32 cloudGameType = 39; // 0不是,1为先锋云游戏, 2为微信小游戏  3为qq小游戏
  optional int32 isGameMatrix = 40; // 1为先锋云游戏，0不是
  optional string regChannelDis = 41; // 注册分发渠道
  optional int32 language = 42;     // 客户端当前使用语言, 海外场景使用
  // 微信小游戏信息
  optional string wxgame_clue_token = 43;       // 游戏启动参数（巨量投放）
  optional string wxgame_req_id = 44;           // 游戏启动参数请求ID（巨量投放）
  optional string wxgame_click_id = 45;         // 点击ID（AMS）
  optional string wxgame_callback = 46;         // 回传ID（快手）
  optional string wxgame_minigame_channel = 47; // 小游戏投放渠道号
  optional string wxgame_wx_openid = 48;        // 微信小游戏appid下的唯一用户标识
  optional string wxgame_unionid = 49;          // 唯一用户标识
  optional int64 loginWaitBeginTimeMs = 50;     // 排队等待开始时间
  optional int32 devicePlatform = 51;   // 客户端上报的包体运行平台，0: invalid, 1: ios, 2: android, 3: pc, 4: mac
  optional string trackId = 52;                 // B站买量标识
  optional bool isSimulate = 53;       // 是否是模拟器
  optional int32 adultType = 54;      // 成年状态，0-未知 1-未成年 2- 已成年
  optional string jqCallInfo = 55; // scheme鉴权附加信息
  optional string ios_caid = 56; //新caid
  optional int32 isVa = 57;   //手q isva (0 - 否, 1 - va)
  optional int32 mobileGearLevel = 58;   //手机档位 low = 1;//低 middle = 2;//中 high = 3;//高 super = 4;//高档
  message InviterInfo{
    optional int64 inviterUid = 1;
    optional int32 activityId = 2;
  }
  optional InviterInfo inviterInfo = 59; // 邀请者信息
  optional int32 isRealName = 60; //(实名认证状态，0-未实名认证1-已实名认证)。
  optional string gpu = 61; //设备gpu型号
  optional string cpu = 62; //设备cpu型号
  optional string qqNumber = 63; //qq号
  // 召集令招募相关 begin
  message RecruitInfo {
    optional string callOpenid = 1; // 招募者openid
    optional int32 callPlatId = 2; // 招募者平台ID
    optional int64 callUid = 3; // 招募者UID
    optional int32 actId = 4; // 活动ID
    optional string appointedOpenId = 5; // 指定openid，如果有表示是回流招募
    optional int32 appointedPlatId = 6; // 指定平台
  }
  optional RecruitInfo recruitInfo = 64;
  // 召集令招募相关 end
  optional com.tencent.wea.xlsRes.PlayerLoginPlat loginPlat = 65;  // 玩家登录平台
  optional int32 clientDeviceType = 66; // 仅做日志上报，客户端设备类型，PC = 1;Android  = 2;Iphone = 3;Ipad = 4;WinPhone=5
  repeated ABTestClientFilterLabel abTestClientFilterLabel = 67; // ab实验客户端过滤标签

  repeated KVEntry playLabel = 100; // 多玩法相关标签, 
  repeated GamePlayInfo playInfo = 101; // 多玩法信息
  optional bool isOptimizedLogin = 102; // 客户端不需要赋值，只是服务器用来判断是否是登录优化后的请求
  optional PlayerGameMatrixExtraData gameMatrixExtraData = 103; // 云游特殊信息
  optional string deeplink = 104;//用户启动微信小游戏的链路统计
}

message Login_S2C_Msg {
  required Login_C2S_Msg req = 2; //req
  optional int32 zoneId = 3;
  optional int32 heartBeatSeconds = 7; // 心跳频率（单位：秒）
  optional int64 uid = 8;
  optional RequiredVersion requiredVersion = 12;
  optional int32 zoneOpenTimeSec = 13; //服务器开服时间戳(秒)
  repeated FeatureEnableInfo featureEnableArray = 15;
  optional bool hasWaterMark = 16;
  optional int32 grayFlag = 17; //是否是开局灰度用户
  optional string midasZoneId = 18; // midas支付分区
  optional string reviewRedirectAddr = 19; // 审核服重定向地址
  optional int32 idipArea = 20; // idip area, 用于账号注销、活动、福袋等场景
  optional string ipAddr = 21; // 客户端IP地址
  optional int32 partition = 22;    // 分区, 海外业务使
  optional string idipAreaEnv = 23; // idip area环境, idip测试环境取值为test, idip正式环境取值为formal
  optional int32 serverInsid = 24;  // 当前服务的实例id
  optional TssLightFeatureData lightFeatureData = 25; // 轻特征数据
  optional bool isBusiness = 26; // 是否正式环境
  optional int32 testIdipArea = 27; // idip平台测试环境idip area
  optional int32 formalIdipArea = 28;   // idip平台正式环境idip area
  repeated FeatureOpenItem items = 29;
}

message ABTestClientFilterLabel {
  optional int32 labelType = 1;  // 标签类型
  repeated string labelValue = 2; // 标签值
}

message LoginDone_C2S_Msg {

}

message LoginDone_S2C_Msg {

}

message GetFreeFlowInfo_C2S_Msg {
    optional int32 requestId = 1; // 请求ID
}

message GetFreeFlowInfo_S2C_Msg {
    optional int32 freeFlow = 1; // 0 - 不是免流用户 1 - 是免流用户
    optional string prefix = 2; // 用于拼接的前缀
    optional int32 requestId = 3; // 请求ID
}

message TssLightFeatureData {
  optional string featureName = 1;
  optional bytes featureData = 2;
  optional int64 featureDataCRC = 3;
}

message RegisterInitInfo{
  optional string nickName = 1; // 注册昵称
  optional int32 gender = 2;  // 注册性别
  optional int32 age = 3; // 年龄
}

enum LoginStatus {
  LS_Login = 1;
  LS_ReLogin = 2;
  LS_ReDirect = 3;
}

message RequiredVersion {
  optional string resourceUpdateAppVersion = 1; // 策划配表更新要求的最低app版本号
  optional string resourceUpdateResVersion = 2; // 策划配表更新要求的最低res版本号
}

enum FeatureEnableType {
  FET_Fog = 1;
  FET_Weather = 2;
}

message FeatureEnableInfo {
  optional FeatureEnableType type = 1;
  optional bool enable = 2;
}

message RootAttrNtf {
  optional proto_RootAttr rootAttr = 1;
}

message PlayerXiaowoAttrNtf {
  optional proto_XiaoWoInfo xiaoWoInfo = 1;
}

message KickPlayerNtf {
  optional string reason = 1;
  optional int32 sid = 2; // session id
  optional int32 errorCode = 3;
  // BanLogin == -10001007 封号
  // IdipOperating == -1000004 数据修复临时踢线
  // DBDataNotExist == -11 服务器内部错误
  // DBOpFailed == -12 服务器内部错误
  // DBDataNotExist == -11 服务器内部错误
  // ServerClose == -13 服务器停服维护
  // KickForRollBack == -10001013 服务器内部错误
  // KeepAliveFail == -10001022 服务器内部错误
  // ConcurrentLoginSSO == -10001006 服务器内部错误
  // ConcurrentLogin == -10001001 并发登录
}

message PlayerBanInfoNtf {
  optional BanType banType = 1;
  optional int32 sid = 2; // session id
  optional int32 errorCode = 3;
  optional int64 banBefore = 4;
  optional string reason = 5;
  optional bool show = 6; // 是否弹框
}


enum MatchOpType {
  MOT_Begin = 1; // 开始匹配
  MOT_Query = 2; // 查询匹配结果
  MOT_Cancel = 3; // 取消匹配
}

message BanLoginInfo {
  optional int64 banBefore = 1;
  optional string reason = 2;
  optional int32 lordLv = 3;
}

message IdipBanInfo {
  optional BanType banType = 1;
  oneof BanInfo {
    BanLoginInfo banLoginInfo = 2;
  }
}

// 心跳
message HeartBeat_C2S_Msg {
}

message HeartBeat_S2C_Msg {
  optional uint64 timestamp = 1;
  optional string resVersion = 2; // 服务器当前资源版本号
}

// gm指令
message GMCommand_C2S_Msg {
  optional int32 id = 1;
  optional string cmd_name = 2;
  repeated string param = 3;
}

message GMCommand_S2C_Msg {
  optional int32 result = 1;
  optional string msg = 2;
}

message GMParam {
  optional string desc = 1;
  optional string default = 2;
}

message GMCmdConf {
  optional int32 id = 1;
  optional string handler = 2;
  optional string desc = 3;
  repeated GMParam param = 4;
}

message GMAllCmdInfoNtf {
  repeated GMCmdConf conf = 1;
}

message ResFileUpdateInfo {
  optional string fileName = 1;
  optional string currMd5 = 2;
  repeated string prevMd5 = 3;
}

message ResFileUpdateNtf {
  optional string fullUrlPattern = 1;
  optional string diffUrlPattern = 2;
  repeated ResFileUpdateInfo filesInfoList = 3;
}

//开始订阅
message AttrRemoveTags_C2S_Msg {
  repeated com.tencent.wea.AttrTagType tags = 1;
  optional com.tencent.wea.AttrTagTarget target = 2;
}

message AttrRemoveTags_S2C_Msg {
  repeated com.tencent.wea.AttrTagType tags = 1;
}

//取消订阅
message AttrAddTags_C2S_Msg {
  repeated com.tencent.wea.AttrTagType tags = 1;
  optional com.tencent.wea.AttrTagTarget target = 2;
}

message AttrAddTags_S2C_Msg {
}

message UploadLogNtf {
  optional int32 reserved = 1;
}

message PlayerMiscElem {
  optional com.tencent.wea.xlsRes.MiscNtfKey key = 1;
  optional string value = 2;
  optional int32 miscKey = 3; // MiscKey，用int便于线上热更扩展
}

enum MiscKey {
  MK_Club_Msg_Interval = 0; // 社团聊天实时cd
}

message PlayerMiscNtf {
  repeated PlayerMiscElem miscElemArray = 1;
}

// 服务调试弹框
message DebugMessageNtf {
  optional string message = 1;
}

message ModErrorNtf {
  optional int32 error = 1;
}

message PlayerNoticeMsgNtf {
  optional PlayerNoticeMsgType type = 1;   // 通知类型
  optional string notice = 2;             // 通知内容
  repeated string params = 3;              // 通知文本的fmt参数
  optional bool msgQueued = 4;            // 是否排队展示
}

message GetOpenIdFromUid_C2S_Msg {
  optional int64 uid = 1;
}
message GetOpenIdFromUid_S2C_Msg {
  optional string openId = 1;
}

message UploadButtonClickFlow_C2S_Msg{
  optional int32 buttonType = 1;
  optional int32 paramValue1 = 2;
  optional int32 paramValue2 = 3;
  optional string paramValue3 = 4;
}

message UploadButtonClickFlow_S2C_Msg{
  optional int32 reserved = 1;
}

message FeatureUnlockItem {
  optional com.tencent.wea.xlsRes.FeatureUnlockType feature_type = 1;
  optional int32 is_open = 2;
  optional int32 feature_typeValue = 3;
}

message ListFeatureUnlock_C2S_Msg {

}
message ListFeatureUnlock_S2C_Msg {
  repeated FeatureUnlockItem feature_list = 1;
}

message FeatureUnlockNtf {
  repeated FeatureUnlockItem unlock_info = 1;
}

message FeatureOpenItem {
  optional com.tencent.wea.xlsRes.FeatureOpenType type = 1; // 优先使用typeid字段，避免服务器无法识别新增枚举
  optional bool isShow = 2;
  optional bool isDeveloping = 3;
  optional int32 typeid = 4;  // 兼容枚举类型
}

message ListFeatureOpen_C2S_Msg {
  option (ugc_app_forward) = true;
}
message ListFeatureOpen_S2C_Msg {
  repeated FeatureOpenItem items = 1;
}

message FeatureOpenNtf {
  repeated FeatureOpenItem items = 1;
}

message PlayerAfterLoginNtf {
  optional int32 reserved = 1;
  optional DsOngoingAddrInfo dsInfo = 2;
  optional ModPlayerData modPlayerData = 3;
}


message GetZplanCurrencyBalance_C2S_Msg {
  optional int32 recharge = 1;  // 充值金额，充值成功后填充，用作校验使用
}

message GetZplanCurrencyBalance_S2C_Msg {

}

// 游戏内换装成功后的回调, 通知服务器去zplan平台拉取最新装扮信息
message GetZplanProfileInfo_C2S_Msg {
}

message GetZplanProfileInfo_S2C_Msg {
}


//防沉迷执行上报
message AntiAddictReport_C2S_Msg {
  optional string traceId = 1; //ntf中取
  optional string ruleName = 2; //ntf中取
  optional int64 time = 3; //操作完成的时间戳，单位s
}

message AntiAddictReport_S2C_Msg {
  optional int32 reserved = 1;
}

message AntiAddictNtf {
  optional string traceId = 1;
  repeated AntiAddictInstruction instructions = 2;
}

message GetSpecReward_C2S_Msg {
  optional int32 rewardType = 1;
}

message GetSpecReward_S2C_Msg {
  optional int32 rewardItemId = 1;  // 礼包道具id
}


message ClientSetCache_C2S_Msg {
  optional int32 id = 1; // key
  optional string val = 2; // value
  optional int64 expireTime = 3; // 过期时间 单位秒
}

message ClientSetCache_S2C_Msg {
}

message ClientDelCache_C2S_Msg {
  optional int32 id = 1; // key
}

message ClientDelCache_S2C_Msg {
}

message UnLockMatchTypeNtf {
  repeated int32 matchTypeId = 1;
}

message GetUidFromOpenId_C2S_Msg {
  repeated string openId = 1;
}
message GetUidFromOpenId_S2C_Msg {
  repeated OpenIdAndUid openIdToUidList = 1;
}


// 外链跳转登录参数传递
message LoginFromBacklinksParams_C2S_Msg {
  required int32 platId = 1; //ios:0 android:1 windows:2
  required com.tencent.wea.xlsRes.BacklinksType backlinksType = 2; //外链类型
  required string srcOpenId = 3; // 来源玩家openId
  required string messageExt = 4; //游戏自定义透传字段
}

message LoginFromBacklinksParams_S2C_Msg {
  required int32 platId = 1; //ios:0 android:1 windows:2
  required com.tencent.wea.xlsRes.BacklinksType backlinksType = 2; //外链类型
  required string srcOpenId = 3; // 来源玩家openId
  required string messageExt = 4; //游戏自定义透传字段
}

// 获取关卡的记录数据
message PlayerGetRecordItemForLevel_C2S_Msg {
  optional int32 levelId = 1; // 关卡id
  optional int32 playId = 2; // 玩法id，不填时兼容旧逻辑，返回旧的数据
}

message PlayerGetRecordItemForLevel_S2C_Msg {
  optional proto_LevelRecordData lifeRecord = 1;    // 生涯记录
  optional proto_LevelRecordData seasonRecord = 2;  // 赛季记录
  optional proto_LevelRecordData jsSingleCircleRecord = 3;  // 单圈生涯记录
}

// 批量获取多个关卡的记录数据
message PlayerBatchGetRecordItemForLevel_C2S_Msg {
  repeated int32 levelIds = 1; // 关卡id列表
  optional int32 playId = 2; // 玩法id，不填时兼容旧逻辑，返回旧的数据
}

message PlayerLevelRecordDetails {
  optional int32 levelId = 1;
  optional proto_LevelRecordData lifeRecord = 2;    // 生涯记录
  optional proto_LevelRecordData seasonRecord = 3;  // 赛季记录
  optional proto_LevelRecordData jsSingleCircleRecord = 4;  // 单圈生涯记录
}

message PlayerBatchGetRecordItemForLevel_S2C_Msg {
  repeated PlayerLevelRecordDetails recordList = 1; // 玩家关卡记录细节
}

message QAInvestStatusNtf {
  optional int32 investId = 1;    // 问卷ID
  optional com.tencent.wea.xlsRes.QAInvestStatus investStatus = 2;  // 问卷状态
  optional com.tencent.wea.xlsRes.QAInvest nextQAInvest = 3; // 下一个问卷
}

message QAInvestConfigListNtf {
  optional int32 callbackId = 1;  // 回调索引
  repeated com.tencent.wea.xlsRes.QAInvest uncompletedQAInvestList = 2;  // 待完成问卷列表
}

message CheckPlayerNickName_C2S_Msg {
  optional string nick_name = 1; //玩家昵称
}

message CheckPlayerNickName_S2C_Msg {
  optional int32 result = 1; // 0 - 正常
}

message CreateRole_C2S_Msg {
  optional string nickName = 1;
  optional int32 gender = 2;
  repeated int32 itemIds = 3;
}

message CreateRole_S2C_Msg {

}

message ChangeDefaultDress_C2S_Msg {
  repeated int32 itemIds = 1;
  optional bool isGiveUp = 2;
  optional int32 gender = 3;
  optional string nickname = 4;
}

message ChangeDefaultDress_S2C_Msg {

}

message PlayerPray_C2S_Msg {

}

message PlayerPray_S2C_Msg {

}

message PlayerPrayShare_C2S_Msg {

}

message PlayerPrayShare_S2C_Msg {

}



message RewardSnsShare_C2S_Msg {
  optional int32 share_id = 1;    // 分享配置表id
}

message RewardSnsShare_S2C_Msg {
  optional int32 result = 1;
  optional int32 canGetShareRewardDaily = 2;
}

// 客户端上传IDC网络延迟信息请求
message NetworkInfoUpload_C2S_Msg {
  optional IDCNetworkInfo info = 1;
}

// 客户端上传IDC网络延迟信息响应
message NetworkInfoUpload_S2C_Msg {
}

// IDC Ping服务器的列表
message IDCPingSvrListNtf {
  repeated IDCPingSvrInfo svrList = 1;
}

// ABTest分组信息拉取
enum ABTestType {
  ABTT_NEWBIE_BATTLE_TYPE = 5;  // 主玩法新手引导实验
  ABTT_BATTLE_WARMLOSE = 6; // 连败温暖实验
  ABTT_ACTIVITY_DISCOVER = 7; // 活动发现实验
  ABTT_UGC_HOME_RECOMMAND = 8; // UGC新手引导图实验
  ABTT_RETURNING_USER_WARMROUND = 9;  // 回流用户温暖局实验
  ABTT_UGC_DAILY_PICKS = 10;  // UGC每日精选
  ABTT_NEWBIE_ADULT_AND_PERIOD = 11;  //  主玩法新手, 成年和时段实验
  ABTT_RETURN_ACTIVITY = 12; // 回流活动
  ABTT_RETURNING_USER_WARMROUND_V2 = 13;  // V2回流用户温暖局实验
  ABTT_IAA = 14; // IAA
  ABTT_AINPC_GAGADUCK = 17;   // 智能NPC（好好鸭）
  ABTT_TEAMSHOW_ENTRANCE_VA = 18; // VA版本组队秀入口
  ABTT_TEAMSHOW_ENTRANCE_APP = 19; // APP版本组队秀入口
  ABTT_TEAMSHOW_ENTRANCE_CloudShot = 20; // 云切片版本组队秀入口
  ABTT_UGC_FAMOUS_MAP = 21; //UGC-星世界-名图堂改版实验
  ABTT_UGC_COLLECTION_PAGE = 22; //UGC-星世界-合集改版
  ABTT_NEWBIE_FARM_PAGE = 23; //手Q小游戏新手承接页面实验
  ABTT_NEWBIE_SKIP_ROLE = 24; //次留优化专项
  ABTT_SURVIVAL_LEVEL_RATIO = 25; //生存关投放比例实验
  ABTT_NEWBIE_REWARD = 26;                // 新手奖励
  ABTT_MOBA_5V5_NEWBIE = 27;            // 新模式Moba_5v5实验
  ABTT_HUDMODEL_SELECT = 29;            //次留hudabtest实验
  ABTT_MOBA_5V5_NEWBIE_V2 = 30;            // 新模式Moba_5v5实验V2
  ABTT_NEWBIE_ENTER_HUD = 31;   // 新用户玩法引导
  ABTT_NEWBIT_CHOOSE_CHACRACTER = 32; // 次留优化，新手选角不同界面实验
  ABTT_NEWBIE_ENTER_HUD_V2 = 33;  //  新用户玩法引导V2
  ABTT_NEWBIE_ROLE_ENTER_FARM = 34; // 次留优化专项 —— app新手创角后，直接进农场实验
  ABTT_RANDOM_EVENT = 35;   // 玩法加入随机事件实验
  ABTT_PROMOTION_COMPETITION_THREE_ROUND = 36;   // 天天晋级赛三轮制实验
  ABTT_PROMOTION_COMPETITION_ONE_ROUND = 37;   // 天天晋级赛一轮大乱斗实验
  ABTT_NEWBIE_SEVEN_REWARD = 38;   // 次留优化，强化新手登录奖励
  ABTT_NEWBIE_ENTER_HUD_V3 = 39;  //  新用户玩法引导V3
  ABTT_NEWBIE_NEW_DANJU = 40;  //  微信小游戏新用户单局天天晋级赛实验
  ABTT_NEWBIE_ENTER_HUD_Change = 41;  //  云游Hud新增任务目标
  ABTT_HOK_TEAM_NEWBIE = 42;  //  moba_5v5组队新手局实验
  ABTT_HOK_NEWBIE_SPECIAL_RULE = 43;  //  moba_5v5单人新手局优化实验
  ABTT_NEWBIE_ENTER_HUD_Change2 = 44;  //  元梦云游HUD改造实验
  ABTT_FIRST_PAGE_CHANGE = 45;  // 星世界首页改版
  ABTT_ARENA_NOVICE_CHOOSES_HERO_V2 = 46; // arena 新手选择英雄实验
  ABTT_MODELSELECT_UPDATE_FEEDBACK_V1 = 47;  //  玩法选择-玩法更新内容强反馈
  ABTT_MODELSELECT_CHANGE = 48;  //  玩法选择整体优化
  ABTT_STARP_WORLD = 49; //啾灵大世界配置实验
  ABTT_BI_HUD_INFO = 50; //新版hud轮播接入BI
  ABTT_NEWBIE_SKIP_ROLE_New = 51; //端内跳过创角实验v250428
  ABTT_RANDOM_EVENT_V2 = 52;   // 玩法加入随机事件实验二期
  ABTT_CLOUD_RESOLUTION = 53;   // 云游修改玩家分辨率实验
  ABTT_LEVEL_UP_CHALLENGE = 54; // 重返农场-冲级挑战
  ABTT_ACTIVITY_RECOMMEND = 55;   // 发现系统活动推荐实验
  ABTT_CHASE_NEWBIE_FIXED_MAP = 56;    // 大王别抓我新手局固定地图实验
  ABTT_CHASE_NEWBIE_FIXED_ROLE = 57;   // 大王别抓我新手局固定角色实验
  ABTT_NEW_SELECTED_PROMOTION = 58;   // 新用户默认选中的晋级赛模式
  ABTT_NEW_SELECTED_SCENE = 59;   // 新用户首次进入场景差异实验
  ABTT_NEW_FACE_7TH = 60;   // 新用户7日签到拍脸时机
  ABTT_CHASE_NEWBIE_GUIDE = 61;   // 大王别抓我新手指引实验

  ABTT_NEWBIE_FIRST_PLAZA = 63;  //新玩家首先进入广场的实验
}

message ABTestInfo {
  optional int32 abtest_type = 1; // 参考ABTestType的取值
  optional int64 abtest_id = 2;
  optional string abtest_group_key = 3;
}

message ABTestInfoBatchQuery_C2S_Msg {
  repeated int32 abtest_types = 1;
}
message ABTestInfoBatchQuery_S2C_Msg {
  repeated ABTestInfo abtest_infos = 1;
}

message ABTestInfoQuery_C2S_Msg {
  optional int32 abtest_type = 1;
}
message ABTestInfoQuery_S2C_Msg {
  optional int64 abtest_id = 1;
  optional string abtest_group_key = 2;
}

message ABTestInfoNtf {
  optional int32 abtest_type = 1; // 参考ABTestType的取值
  optional int64 abtest_id = 2;
  optional string abtest_group_key = 3;
}

message GetRandomNickname_C2S_Msg {

}

message GetRandomNickname_S2C_Msg {
  optional string nickName = 1;
}

// 获取账号状态请求
message GetAccountState_C2S_Msg {

}

// 获取账号状态响应
message GetAccountState_S2C_Msg {
  optional uint32 account_state = 1;    // 账号状态, 详情见枚举AccountState所示
}

// 检测客户端随机的一批昵称是否可用
message CheckRandomNicknames_C2S_Msg {
  repeated string nicknames = 1; // 送检昵称
}

message CheckRandomNicknames_S2C_Msg {
  repeated string availableNicknames = 2; // 可用昵称
}

// 获取PingSvr信息请求
message GetPingSvrInfo_C2S_Msg {
}

// 获取PingSvr信息返回
message GetPingSvrInfo_S2C_Msg{
  repeated CSPingSvrInfo info = 1;  // pingSvr信息
}

// 获取翻译信息请求
message GetTranslateInfo_C2S_Msg {
  optional int32 version = 1;     // 版本号
  optional int32 language = 2;    // 获取语言
}

// 获取翻译信息返回
message GetTranslateInfo_S2C_Msg{
  optional int32 version = 1;                 // 版本号(服务器)
  optional int32 language = 2;                // 语言
  repeated CSTranslateInfo translateInfo = 3; // 翻译内容
}

// 取消家长授权请求
message WithdrawParentAuth_C2S_Msg {
}

// 取消家长授权返回
message WithdrawParentAuth_S2C_Msg {
  optional int32 ret = 1;
}

// 取消家长授权推送
message WithdrawParentAuthNtf {
  optional int32 ret = 1;
  optional string msg = 2;
  optional string traceId = 3;
}

// 查询家长授权状态请求
message QueryParentAuthStatus_C2S_Msg {
}

// 查询家长授权状态返回
message QueryParentAuthStatus_S2C_Msg {
  optional int32 ret = 1;
}

// 查询家长授权状态推送
message QueryParentAuthStatusNtf {
  optional int32 ret = 1;
  optional string msg = 2;
  optional int32 status = 3;
  optional string traceId = 4;
}

// 刷新玩家登录态信息
message RefreshPlayerPayInfo_C2S_Msg{
  optional string accessToken = 1; //鉴权Token
  optional string payToken = 2;// qq专用payToken,wx accessToken 统一赋值到payToken
  optional string payPf = 3;// 平台信息
  optional string payPfKey = 4;// 平台token
  optional string wxCode = 5;  // wx小游戏临时登录凭证
}
message RefreshPlayerPayInfo_S2C_Msg{
}

// 通知客户端刷新登陆态
message RefreshPlayerPayInfoNtf{

}

enum PlatNicknameSource {
  PNS_UNKNOWN = 0;
  PNS_ACCOUNT = 1;
  PNS_PREFAB = 2;
}

message AvailablePlatNicknameNtf {
  optional string availablePlatNickname = 1;     // 平台昵称
  optional PlatNicknameSource source = 2;
}

// 平台特权ntf消息
message PlatPrivilegesNtf {
  optional int32 platPrivileges = 1;    // 平台特权情况, 详情见枚举PlatPrivilegesType所示
  optional int64 expireTimeMs = 2;      // 平台特权过期时间
}

// 用户福袋信息ntf
message PlayerBlessBagInfoNtf {
  optional int64 bagId = 1;             // 福袋id
  optional int64 expireTimeMs = 2;      // 福袋分享过期时间
}

message SingleBattleKeepAlive_C2S_Msg {
  option (ugc_app_forward) = true;
  optional int32 matchType = 1; // 0为ugc， 其他为玩法id
}
message SingleBattleKeepAlive_S2C_Msg{
  optional int32 ret = 1;
}
// 客户端单机开局
message SingleBattleStart_C2S_Msg {
  optional int32 matchType = 1;
}
message SingleBattleStart_S2C_Msg {
  optional int64 battleId = 1;//battleId,这一局结束的时候要用到
}

// 客户端单机结算
message SingleBattleSettlement_C2S_Msg {
  optional int64 battleId = 1;
  optional int32 battleResult = 2;  // 0胜 1负 2平
}
message SingleBattleSettlement_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional int32 battleResult = 2;  // 0胜 1负 2平
}

// 客户端单机分数结算
message PlayerDanceScore_C2S_Msg {
  optional int32 score = 1;
}
message PlayerDanceScore_S2C_Msg {
  optional int32 result = 1; // 返回结果
  optional int32 score = 2;
}

message AsaIadInfo {
  optional string attribution = 1;  // 是否从AppStore搜索广告 0或1
  optional string orgID = 2;  // 广告系列组ID
  optional string orgName = 3;  // 广告系列组名称
  optional string campaignID = 4;   // 广告系列ID
  optional string campaignName = 5; // 广告系列名称
  optional string adgroupID = 6;    // 广告组ID
  optional string adgroupName = 7;  // 广告组名称
  optional string keywordID = 8;    // 搜索关键词ID
  optional string keyword = 9;      // 搜索关键词
  optional string keywordMatchtype = 10;    // 广告关键词匹配类型
  optional string adId = 11;        // adid
  optional string creativesetName = 12;   // CreativeSet名称
  optional string clickDate = 13;   // 广告点击时间
  optional string purchaseDate = 14;    // 用户首次下载时间
  optional string conversionDate = 15;    // 用户下载时间
  optional string conversionType = 16;    // 用户类型
  optional string countryOrRegion = 17;   // 用户所属国家或地区
}

message UploadAsaIadInfo_C2S_Msg {
  optional AsaIadInfo asaIadInfo = 1;
}

message UploadAsaIadInfo_S2C_Msg {

}

message TlogDataReport_C2S_Msg {
  optional TlogFlowType type = 1 [deprecated = true];
  optional string data = 2; // json string: {"BattleID":1, ...}
  optional int32 tlogType = 3; // BattleDsTlogType
}

message TlogDataReport_S2C_Msg {

}

message GameliveTaskRefresh_C2S_Msg {
  optional int32 status = 1; // game live status , 0:defautl 1:as author 2:as audience
}

message GameliveTaskRefresh_S2C_Msg {
  
}

message UpdatePlayerLocation_C2S_Msg {
  optional com.tencent.wea.xlsRes.RankGeoRegion region = 1;
  optional int32  province = 2;
  optional int32  city = 3;
  optional int32  town = 4;
  optional string detail = 5;
}

message UpdatePlayerLocation_S2C_Msg {
}

// 获取玩法日历配置数据
message GetPlayerCalendarConfig_C2S_Msg {
}


message GetPlayCalendarConfig_S2C_Msg {
  optional int32 playId = 1;    // 玩法id
  optional int64 startTime = 2;  // 开始时间
  optional int64 endTime = 3;  // 结束时间
  optional int64 preShowTime = 4;  // 展示开始时间
}

message GetPlayerCalendarConfig_S2C_Msg {
  optional int32 id = 1;    // id
  optional int32 playId = 2;    // 玩法id
  optional int64 startTime = 3;  // 开始时间
  optional int64 endTime = 4;  // 结束时间
  optional int64 preShowTime = 5;  // 展示开始时间
  repeated com.tencent.wea.xlsRes.MatchDate calendarConfigList = 6;
}

// qq群应用开始一个组队任务
message QQGroupStartTeamTask_C2S_Msg {
  optional string taskId = 1;
  optional int64 expiredTimeMs = 2; // 可指定过期时间，否则按照一个小时
}

message QQGroupStartTeamTask_S2C_Msg {
}

message PlatPrivilegesProcess_C2S_Msg {
  optional int32 platPrivileges = 1; // 平台特权情况, 详情见枚举PlatPrivilegesType所示
}

message PlatPrivilegesProcess_S2C_Msg {
}

// 玩家拜年数据上报请求
message PlayerPlayNewYearCall_C2S_Msg {
  optional int64 operateTime = 1;      // 玩家拜年操作时间
}

// 玩家拜年数据上报响应
message PlayerPlayNewYearCall_S2C_Msg {
}

// 玩家专属拍脸上报请求(拍脸次数+1)
message SpecialFaceReport_C2S_Msg {
  optional int32 faceId = 1;      // 拍脸id
  optional int64 expireTime = 2; // 过期时间戳-秒
  optional int32 todayNotShow = 3; // 0-继续展示，1-今天不展示
}

// 玩家专属拍脸上报相应
message SpecialFaceReport_S2C_Msg {
  optional int32 faceId = 1;  // 拍脸id
  optional int32 faceCount = 2;  // 已拍脸次数
}

message UserWhiteListCheck_C2S_Msg {
  optional int32 type = 1;  // 参考HotResWhiteListType
}

message UserWhiteListCheck_S2C_Msg {
  optional int32 result = 1;  // 0 是
  optional int32 type = 2;  // 参考HotResWhiteListType
}

// 组队推荐角色请求 好友部分客户端自行处理，本消息处理其他部分
message TeamRecommendRole_C2S_Msg{
  repeated int64 uids = 1; // 已经推荐过的id列表
}

message TeamRecommendRole_S2C_Msg {
  optional int32 type = 1; // 类型 ref TeamRecommendRoleType
  optional int64 uid = 2;
}

message TeamRecommendRoleNtf {
  optional int32 type = 1; // 类型 ref TeamRecommendRoleType
  optional int64 uid = 2;
}

message SendRoomMemberToMemberNtf_C2S_Msg {
  optional RoomMemberToMemberNtfType ntfType = 1; //发送通知的类型
  optional int64 targetUid = 2; //发送到的目标玩家
  optional int64 roomId = 3; //房间ID
}
message SendRoomMemberToMemberNtf_S2C_Msg {

}
message RoomMemberToMemberNtf {
  optional RoomMemberToMemberNtfType ntfType = 1; //发送通知的类型
  optional int64 senderUid = 2; //发送者的uid
  optional int64 roomId = 3; //房间id
}

message GetPlayerGrayTagInfo_C2S_Msg {
}

message GetPlayerGrayTagInfo_S2C_Msg {
  repeated proto_PlayerGrayRuleData ruleInfos = 1; //生效的规则
  repeated PlayerGrayTagInfo tagInfos = 2;  //当前生效的标签
}

message ReportNewPlayerGuide_C2S_Msg {
  optional int64 key = 1; // 引导id
  optional int64 value = 2; // 引导完成次数
  optional int64 timeStamp = 3; //时间戳
}

message ReportNewPlayerGuide_S2C_Msg {

}

message GetUserCloudInfo_C2S_Msg {

}

message GetUserCloudInfo_S2C_Msg {
  optional int64 firstCloudLoginTime = 1; // 首次云游戏登录时间
}

// 拉取信誉分变化列表请求
message GetReputationScoreChangeList_C2S_Msg {
  optional int32 scoreId = 1;						  // 信誉分分数id
  optional bool isAll = 2;                            // 是否需要所有信誉分分数id下的信誉分信息
}

// 信誉分变化信息列表
message ReputationScoreChangeList {
  optional int32 scoreId = 1;                         // 信誉分分数id
  optional int32 reputationScore = 2;          // 当前信誉分
  repeated PlayerReputationScoreRecord records = 3;   // 信誉分变化记录列表
}

// 拉取信誉分变化列表响应
message GetReputationScoreChangeList_S2C_Msg {
  repeated ReputationScoreChangeList changeLists = 1; // 信誉分分数id下的信誉分信息列表
  optional bool reputationScoreIsOpen = 2;     // 信誉分开关
}

message ReportIAA_C2S_Msg {
  optional int32 id = 1;
  repeated int32 param = 2;       // IAA参数
  optional int64 seqId = 3;       // 序列号，方便定位异步操作
  repeated int64 longParam = 4;   // IAA参数
}

message ReportIAA_S2C_Msg {

}

message ReportIAANtf {
  optional int32 id = 1;
  repeated int32 param = 2;       // IAA参数
  optional int64 seqId = 3;       // 序列号，方便定位异步操作

  optional int32 reason = 4;      // IAAReason
  optional int32 result = 5;      // IAA处理结果
}

message GetIAAList_C2S_Msg {
  optional int32 checkType = 1;
  optional int32 adId = 2;
  optional string adUnitId = 3;
  repeated int32 adParams = 4;
}

message GetIAAList_S2C_Msg {
  repeated IAAInfo info = 1;
  optional int32 checkType = 2;
  optional int32 adId = 3;
  optional string adUnitId = 4;
  repeated int32 adParams = 5;
}

message IAAInfo {
  optional int32 id = 1;
  optional int64 beginSec = 2;
  optional int64 endSec = 3;
}

// 玩家信誉分功能开关ntf消息
message PlayerReputationScoreFunctionSwitchNtf {
  optional bool reputationScoreIsOpen = 1;     // 信誉分开关
}

message IsInAdminWhiteList_C2S_Msg {
  repeated string moduleIds = 1;
}

message IsInAdminWhiteList_S2C_Msg {
  repeated bool results = 1;
}

// 开启二级密码
message OpenSecondaryPassword_C2S_Msg {
  optional string password = 1;
  optional string passwordConfirmed = 2;
}

message OpenSecondaryPassword_S2C_Msg {
}

// 修改二级密码
message ChangeSecondaryPassword_C2S_Msg {
  optional string oldPassword = 1;
  optional string newPassword = 2;
  optional string newPasswordConfirmed = 3;
}

message ChangeSecondaryPassword_S2C_Msg {
}

// 关闭二级密码
message CloseSecondaryPassword_C2S_Msg {
  optional int32 operate = 1;  // 1-关闭 2-强制关闭 3-撤销强制关闭
  optional string password = 2;
}

message CloseSecondaryPassword_S2C_Msg {
}

// 开启免密操作
message SecondaryPasswordLessOperate_C2S_Msg {
  optional int32 operate = 1;  // 1-开启 2-关闭
  optional string password = 2;
}

message SecondaryPasswordLessOperate_S2C_Msg {
}

// 验证二级密码
message VerifySecondaryPassword_C2S_Msg {
  optional string password = 1;
  optional string msgName = 2;
}

message VerifySecondaryPassword_S2C_Msg {
}

// 多玩法信息同步请求
message GamePlayInfoSync_C2S_Msg {
  optional string clientVersion = 1;            // 客户端二进制版本号 
  repeated GamePlayInfo playInfo = 2;           // 多玩法信息
}

message GamePlayInfoSync_S2C_Msg {

}

// 分包数据上报
message GamePakInfoReport_C2S_Msg {
  repeated proto_PakDownloadInfo allPakDownloadInfoList = 1; // 客户端当前的全量分包数据列表，服务器覆盖式更新
}

message GamePakInfoReport_S2C_Msg {

}

message GamePakDetailInfoReport_C2S_Msg {
  repeated proto_PakDownloadInfo allPakDownloadInfoList = 1; // 客户端当前的全量分包数据列表，服务器覆盖式更新
}

message GamePakDetailInfoReport_S2C_Msg {
  
}

message IAAForceGuideAppNtf {

}

message IAAForceGuideAppReset_C2S_Msg {

}

message IAAForceGuideAppReset_S2C_Msg {

}

message SetTeamShowBackgroundTheme_C2S_Msg {
  optional int32 teamShowBackgroundTheme = 1; // 组队秀背景主题
}

message SetTeamShowBackgroundTheme_S2C_Msg {

}

message QqCloudGameAuth_C2S_Msg {
  optional int32 agree = 1; // 1同意, 2拒绝
}

message QqCloudGameAuth_S2C_Msg {

}
message QqCloudGameNeedAuthNtf {

}

// 信誉分变化信息推送消息
message ReputationScoreChangeInfoNtf {
  optional int32 scoreId = 1;             // 信誉分分数组id
  optional int32 reputationScore = 2;			// 当前信誉分
  optional int32 deltaScore = 3;				  // 本次变化分数
  optional int32 modeId = 4;					    // 玩法id
  optional int64 battleId = 5;					  // 对局id
}

//关卡评价
// 请求玩家所有的关卡评价记录
message GetAllInfoForLevelEstimation_C2S_Msg {

}

// 请求返回数据返回
message GetAllInfoForLevelEstimation_S2C_Msg {
  //关卡id
  repeated int32 levelId = 1;

}

// 请求玩家对应的某一个关卡评价记录
message GetInfoForLevelEstimation_C2S_Msg {

  optional int32 levelId = 1;
}

// 请求返回数据返回
message GetInfoForLevelEstimation_S2C_Msg {

  optional int32 levelId = 1;
  //踩中赞
  optional int32 estimationOptions = 2;
  //踩中赞对应选择的文本数组
  repeated int32 optionsTextArray =3;
  //自评价内容
  optional string reviewsText = 4;

}



// 进行评价
message PlayerLevelEstimation_C2S_Msg {

  optional int32 levelId = 1;
  optional int32 estimationOptions = 2;
  repeated int32 optionsTextArray = 3;
  optional string reviewsText = 4;
}
// 进行评价
message PlayerLevelEstimation_S2C_Msg {
  optional int32 result = 1;		// 请求结果
  optional int32 levelId = 2;
  optional int32 estimationOptions = 3;
  repeated int32 optionsTextArray = 4;
  optional string reviewsText = 5;
}

// 推送给客户端的玩家反外挂初始化信息
message PlayerAntiCheatInfoInitNtf {
  optional bool initFlag = 1;     // 初始化标记
}

// 玩家反外挂信息上报请求
message PlayerAntiCheatInfoReport_C2S_Msg {
  optional bytes antiData = 1;      // 反外挂安全数据
}

// 玩家反外挂信息上报响应
message PlayerAntiCheatInfoReport_S2C_Msg {

}

// 推送给客户端的玩家反外挂信息消息
message PlayerAntiCheatInfoToClientNtf {
  optional bytes antiData = 1;      // 反外挂安全数据
}


message UpdatedReadyBattleOrnamentationBagInfo_C2S_Msg {
  required int64 ItemUUID = 1;                       // 道具
  optional string itemType = 2;                       // 道具类型
}

message UpdatedReadyBattleOrnamentationBagInfo_S2C_Msg {
  
}
// 提示用户给予设备权限
message DeviceHasNoPermissionNtf {

}

// 提示用户只能登录一台设备
message DeviceIdCheckErrorNtf {

}

// 提示用户因为ip变更遭到账号封禁
message IPHasChangedNtf {

}

// 提示用户ip属于封禁地区
message IPBannedNtf {

}

// 提示用户模拟器禁止登录
message SimulateLimitNtf {

}

// 玩家特定加载文本推送统计
message PlayerTargetedLoadingTextPushCount_C2S_Msg {

}

message PlayerTargetedLoadingTextPushCount_S2C_Msg {

}

//新增亲密关系标签更改
message PlayerIntimateRelationRedPoint_C2S_Msg {
  required int64 uid = 1;           // 好友id
}

message PlayerIntimateRelationRedPoint_S2C_Msg {

}

//玩家进入大厅状态稳定
message PlayerHallStableState_C2S_Msg {

}

message PlayerHallStableState_S2C_Msg {

}

//修改限时体验道具红点
message UpdatePlayerLimitExperienceRedPoint_C2S_Msg {

}

message UpdatePlayerLimitExperienceRedPoint_S2C_Msg {

}


// 发送请求BI推荐信息
message GetBiHudRecommendMapId_C2S_Msg {
  optional int32 type = 1; // 1 发现页 2 ugc玩法选择
}

// 发送请求BI推荐信息
message GetBiHudRecommendMapId_S2C_Msg {
  repeated string mapInfo = 1;          // 推荐信息
}

message GetWeekTopPlayGroupId_C2S_Msg {
  optional int32 beforeWeekNums = 1;
}

message GetWeekTopPlayGroupId_S2C_Msg {
  optional int32 groupId = 1;
}

message GetDayPlayedGroupId_C2S_Msg {
  optional int32 beforeDayNums = 1;
}

message GetDayPlayedGroupId_S2C_Msg {
  repeated int32 groupId = 1;
  repeated int64 LastPlayTime = 2;      //玩法Pak最近一次游玩时间
}

// 强制打开的玩法id
message AddForceOpenMatchType_C2S_Msg {
  optional int32 match_type_id = 1;
}

message AddForceOpenMatchType_S2C_Msg {

}

// 领取社区入口奖励
message GetCommunityEntryReward_C2S_Msg {
  optional int32 id = 1;  // 暂时默认为1，如果后续有多个社区入口可用此id区分
}

message GetCommunityEntryReward_S2C_Msg {
  optional int32 result = 1;
}
