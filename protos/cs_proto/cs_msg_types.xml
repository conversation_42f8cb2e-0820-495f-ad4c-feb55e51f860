<root>
    <entry id="107" name="UnimplementedMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="108" name="Login_C2S_Msg" serverName="gamesvr"/>
    <entry id="109" name="Login_S2C_Msg" serverName="gamesvr"/>
    <entry id="110" name="DirectSceneEnter_C2S_Msg" serverName="scenesvr"/>
    <entry id="111" name="DirectSceneEnter_S2C_Msg" serverName="scenesvr"/>
    <entry id="112" name="DirectSceneExit_C2S_Msg" serverName="scenesvr"/>
    <entry id="113" name="DirectSceneExit_S2C_Msg" serverName="scenesvr"/>
    <entry id="114" name="DirectSceneReportData_C2S_Msg" serverName="scenesvr"/>
    <entry id="115" name="DirectSceneReportData_S2C_Msg" serverName="scenesvr"/>
    <entry id="116" name="DirectSceneDataSyncNtf" serverName="scenesvr"/>
    <entry id="117" name="DirectSceneJumpNtf" serverName="scenesvr"/>
    <entry id="118" name="DirectSceneDataNtf" serverName="scenesvr"/>
    <entry id="119" name="DirectScenePlayerHeartbeat_C2S_Msg" serverName="scenesvr"/>
    <entry id="120" name="DirectScenePlayerHeartbeat_S2C_Msg" serverName="scenesvr"/>
    <entry id="121" name="DirectSceneReconnect_C2S_Msg" serverName="scenesvr"/>
    <entry id="122" name="DirectSceneReconnect_S2C_Msg" serverName="scenesvr"/>
    <entry id="123" name="DirectSceneReportAction_C2S_Msg" serverName="scenesvr"/>
    <entry id="124" name="DirectSceneReportAction_S2C_Msg" serverName="scenesvr"/>
    <entry id="125" name="DirectScenePlayInfoNtf" serverName="scenesvr"/>
    <entry id="126" name="DirectSceneReportLevelData_C2S_Msg" serverName="scenesvr"/>
    <entry id="127" name="DirectSceneReportLevelData_S2C_Msg" serverName="scenesvr"/>
    <entry id="128" name="DirectSceneLevelDataNtf" serverName="scenesvr"/>
    <entry id="129" name="SceneEntityPrivateAttrNtf" serverName="scenesvr"/>
    <entry id="130" name="DirectSceneReady_C2S_Msg" serverName="scenesvr"/>
    <entry id="131" name="DirectSceneReady_S2C_Msg" serverName="scenesvr"/>
    <entry id="132" name="DirectSceneBorrowLock_C2S_Msg" serverName="scenesvr"/>
    <entry id="133" name="DirectSceneBorrowLock_S2C_Msg" serverName="scenesvr"/>
    <entry id="134" name="DirectSceneBorrowUnlock_C2S_Msg" serverName="scenesvr"/>
    <entry id="135" name="DirectSceneBorrowUnlock_S2C_Msg" serverName="scenesvr"/>
    <entry id="136" name="DirectSceneReportPlayResult_C2S_Msg" serverName="scenesvr"/>
    <entry id="137" name="DirectSceneReportPlayResult_S2C_Msg" serverName="scenesvr"/>
    <entry id="138" name="DressUpToggle_C2S_Msg" serverName="gamesvr"/>
    <entry id="139" name="DressUpToggle_S2C_Msg" serverName="gamesvr"/>
    <entry id="140" name="DressUpCheckExpire_C2S_Msg" serverName="gamesvr"/>
    <entry id="141" name="DressUpCheckExpire_S2C_Msg" serverName="gamesvr"/>
    <entry id="142" name="DressUpExpireNtf" serverName="gamesvr"/>
    <entry id="143" name="DressUpChangeNtf" serverName="gamesvr"/>
    <entry id="144" name="DressUpShowList_C2S_Msg" serverName="gamesvr"/>
    <entry id="145" name="DressUpShowList_S2C_Msg" serverName="gamesvr"/>
    <entry id="146" name="DressUpItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="147" name="DressUpItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="148" name="DressUpChangeRandomState_C2S_Msg" serverName="gamesvr"/>
    <entry id="149" name="DressUpChangeRandomState_S2C_Msg" serverName="gamesvr"/>
    <entry id="150" name="DressUpActive_C2S_Msg" serverName="gamesvr"/>
    <entry id="151" name="DressUpActive_S2C_Msg" serverName="gamesvr"/>
    <entry id="152" name="StageAchievementRewardNtf" serverName="gamesvr"/>
    <entry id="153" name="GetStageAchievementReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="154" name="GetStageAchievementReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="155" name="GetAchievementLabel_C2S_Msg" serverName="gamesvr"/>
    <entry id="156" name="GetAchievementLabel_S2C_Msg" serverName="gamesvr"/>
    <entry id="157" name="AcceptTask_C2S_Msg" serverName="gamesvr"/>
    <entry id="158" name="AcceptTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="159" name="RewardTask_C2S_Msg" serverName="gamesvr"/>
    <entry id="160" name="RewardTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="161" name="TaskRewardNtf" serverName="gamesvr"/>
    <entry id="162" name="CompleteGuideTask_C2S_Msg" serverName="gamesvr"/>
    <entry id="163" name="CompleteGuideTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="164" name="StartDsGuide_C2S_Msg" serverName="gamesvr"/>
    <entry id="165" name="StartDsGuide_S2C_Msg" serverName="gamesvr"/>
    <entry id="166" name="EndDsGuide_C2S_Msg" serverName="gamesvr"/>
    <entry id="167" name="EndDsGuide_S2C_Msg" serverName="gamesvr"/>
    <entry id="168" name="GetTomorrowReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="169" name="GetTomorrowReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="170" name="RootAttrNtf" serverName="gamesvr"/>
    <entry id="171" name="KickPlayerNtf" serverName="gamesvr"/>
    <entry id="172" name="HeartBeat_C2S_Msg" serverName="gamesvr"/>
    <entry id="173" name="HeartBeat_S2C_Msg" serverName="gamesvr"/>
    <entry id="174" name="GMCommand_C2S_Msg" serverName="gamesvr"/>
    <entry id="175" name="GMCommand_S2C_Msg" serverName="gamesvr"/>
    <entry id="176" name="GMAllCmdInfoNtf" serverName="gamesvr"/>
    <entry id="177" name="ResFileUpdateNtf" serverName="gamesvr"/>
    <entry id="178" name="AttrRemoveTags_C2S_Msg" serverName="gamesvr"/>
    <entry id="179" name="AttrRemoveTags_S2C_Msg" serverName="gamesvr"/>
    <entry id="180" name="AttrAddTags_C2S_Msg" serverName="gamesvr"/>
    <entry id="181" name="AttrAddTags_S2C_Msg" serverName="gamesvr"/>
    <entry id="182" name="UploadLogNtf" serverName="gamesvr"/>
    <entry id="183" name="PlayerMiscNtf" serverName="gamesvr"/>
    <entry id="184" name="DebugMessageNtf" serverName="gamesvr"/>
    <entry id="185" name="ModErrorNtf" serverName="gamesvr"/>
    <entry id="186" name="RequestSongAchList_C2S_Msg" serverName="gamesvr"/>
    <entry id="187" name="RequestSongAchList_S2C_Msg" serverName="gamesvr"/>
    <entry id="188" name="PlayerNoticeMsgNtf" serverName="gamesvr"/>
    <entry id="189" name="GetOpenIdFromUid_C2S_Msg" serverName="gamesvr"/>
    <entry id="190" name="GetOpenIdFromUid_S2C_Msg" serverName="gamesvr"/>
    <entry id="191" name="UploadButtonClickFlow_C2S_Msg" serverName="gamesvr"/>
    <entry id="192" name="UploadButtonClickFlow_S2C_Msg" serverName="gamesvr"/>
    <entry id="193" name="ListFeatureOpen_C2S_Msg" serverName="gamesvr"/>
    <entry id="194" name="ListFeatureOpen_S2C_Msg" serverName="gamesvr"/>
    <entry id="195" name="FeatureOpenNtf" serverName="gamesvr"/>
    <entry id="196" name="PlayerAfterLoginNtf" serverName="gamesvr"/>
    <entry id="197" name="GetZplanCurrencyBalance_C2S_Msg" serverName="gamesvr"/>
    <entry id="198" name="GetZplanCurrencyBalance_S2C_Msg" serverName="gamesvr"/>
    <entry id="199" name="GetZplanProfileInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="200" name="GetZplanProfileInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="201" name="UploadVoiceStatusTlogFlow_C2S_Msg" serverName="gamesvr"/>
    <entry id="202" name="UploadVoiceStatusTlogFlow_S2C_Msg" serverName="gamesvr"/>
    <entry id="203" name="UpdateMidasToken_C2S_Msg" serverName="gamesvr"/>
    <entry id="204" name="UpdateMidasToken_S2C_Msg" serverName="gamesvr"/>
    <entry id="205" name="AntiAddictReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="206" name="AntiAddictReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="207" name="AntiAddictNtf" serverName="gamesvr"/>
    <entry id="208" name="GetSpecReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="209" name="GetSpecReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="210" name="RoomCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="211" name="RoomCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="212" name="RoomInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="213" name="RoomInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="214" name="RoomInvitationReplyCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="215" name="RoomInvitationReplyCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="216" name="RoomLeave_C2S_Msg" serverName="gamesvr"/>
    <entry id="217" name="RoomLeave_S2C_Msg" serverName="gamesvr"/>
    <entry id="218" name="RoomKick_C2S_Msg" serverName="gamesvr"/>
    <entry id="219" name="RoomKick_S2C_Msg" serverName="gamesvr"/>
    <entry id="220" name="RoomStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="221" name="RoomStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="222" name="RoomCancelStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="223" name="RoomCancelStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="224" name="RoomReadyStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="225" name="RoomReadyStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="226" name="MatchSuccNtf" serverName="gamesvr"/>
    <entry id="227" name="RoomInvitationNtf" serverName="gamesvr"/>
    <entry id="228" name="RoomMemberModifyNtf" serverName="gamesvr"/>
    <entry id="229" name="RoomModifyModMode_C2S_Msg" serverName="gamesvr"/>
    <entry id="230" name="RoomModifyModMode_S2C_Msg" serverName="gamesvr"/>
    <entry id="231" name="RoomCancelResultNtf" serverName="gamesvr"/>
    <entry id="232" name="BattleReConnectNtf" serverName="gamesvr"/>
    <entry id="233" name="RoomWantToJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="234" name="RoomWantToJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="235" name="RoomWantToJoinNtf" serverName="gamesvr"/>
    <entry id="236" name="RoomReplayWantToJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="237" name="RoomReplayWantToJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="238" name="RoomStartBattleNtf" serverName="gamesvr"/>
    <entry id="239" name="RoomListModMode_C2S_Msg" serverName="gamesvr"/>
    <entry id="240" name="RoomListModMode_S2C_Msg" serverName="gamesvr"/>
    <entry id="241" name="RoomCommonNtf" serverName="gamesvr"/>
    <entry id="242" name="RoomJoinFromQQ_C2S_Msg" serverName="gamesvr"/>
    <entry id="243" name="RoomJoinFromQQ_S2C_Msg" serverName="gamesvr"/>
    <entry id="244" name="RoomDirectJoinFromRecruit_C2S_Msg" serverName="gamesvr"/>
    <entry id="245" name="RoomDirectJoinFromRecruit_S2C_Msg" serverName="gamesvr"/>
    <entry id="246" name="MatchSceneCreateSuccNtf" serverName="gamesvr"/>
    <entry id="247" name="MatchJoinSuccNtf" serverName="gamesvr"/>
    <entry id="248" name="MallBuyMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="249" name="MallBuyMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="250" name="DressUpLimitMallPanelMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="251" name="DressUpLimitMallPanelMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="252" name="MallPanelMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="253" name="MallPanelMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="254" name="ClubList_C2S_Msg" serverName="gamesvr"/>
    <entry id="255" name="ClubList_S2C_Msg" serverName="gamesvr"/>
    <entry id="256" name="ClubCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="257" name="ClubCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="258" name="ClubInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="259" name="ClubInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="260" name="ClubJoinInfoNtf" serverName="gamesvr"/>
    <entry id="261" name="ClubJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="262" name="ClubJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="263" name="ClubModify_C2S_Msg" serverName="gamesvr"/>
    <entry id="264" name="ClubModify_S2C_Msg" serverName="gamesvr"/>
    <entry id="265" name="ClubModifyPosition_C2S_Msg" serverName="gamesvr"/>
    <entry id="266" name="ClubModifyPosition_S2C_Msg" serverName="gamesvr"/>
    <entry id="267" name="ClubApplyList_C2S_Msg" serverName="gamesvr"/>
    <entry id="268" name="ClubApplyList_S2C_Msg" serverName="gamesvr"/>
    <entry id="269" name="ClubQuit_C2S_Msg" serverName="gamesvr"/>
    <entry id="270" name="ClubQuit_S2C_Msg" serverName="gamesvr"/>
    <entry id="271" name="ClubQuitNtf" serverName="gamesvr"/>
    <entry id="272" name="ClubKickOut_C2S_Msg" serverName="gamesvr"/>
    <entry id="273" name="ClubKickOut_S2C_Msg" serverName="gamesvr"/>
    <entry id="274" name="ClubDissolve_C2S_Msg" serverName="gamesvr"/>
    <entry id="275" name="ClubDissolve_S2C_Msg" serverName="gamesvr"/>
    <entry id="276" name="ClubCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="277" name="ClubCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="278" name="ClubSetUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="279" name="ClubSetUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="280" name="ActivityRefreshNtf" serverName="gamesvr"/>
    <entry id="281" name="ActivityGetAllRedDotShow_C2S_Msg" serverName="gamesvr"/>
    <entry id="282" name="ActivityGetAllRedDotShow_S2C_Msg" serverName="gamesvr"/>
    <entry id="283" name="ActivityGetAllLabel_C2S_Msg" serverName="gamesvr"/>
    <entry id="284" name="ActivityGetAllLabel_S2C_Msg" serverName="gamesvr"/>
    <entry id="285" name="ActivityGetAllTaskInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="286" name="ActivityGetAllTaskInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="287" name="ActivityGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="288" name="ActivityGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="289" name="ActivityGetAllReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="290" name="ActivityGetAllReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="291" name="SignActivityNtf" serverName="gamesvr"/>
    <entry id="292" name="DKGiftInfoNtf" serverName="gamesvr"/>
    <entry id="293" name="DKGiftInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="294" name="DKGiftInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="295" name="DKBuyGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="296" name="DKBuyGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="297" name="MailInfosNtf" serverName="gamesvr"/>
    <entry id="298" name="SendMail_C2S_Msg" serverName="gamesvr"/>
    <entry id="299" name="SendMail_S2C_Msg" serverName="gamesvr"/>
    <entry id="300" name="RecvMail_C2S_Msg" serverName="gamesvr"/>
    <entry id="301" name="RecvMail_S2C_Msg" serverName="gamesvr"/>
    <entry id="302" name="ReadMail_C2S_Msg" serverName="gamesvr"/>
    <entry id="303" name="ReadMail_S2C_Msg" serverName="gamesvr"/>
    <entry id="304" name="GetAttachments_C2S_Msg" serverName="gamesvr"/>
    <entry id="305" name="GetAttachments_S2C_Msg" serverName="gamesvr"/>
    <entry id="306" name="DelMail_C2S_Msg" serverName="gamesvr"/>
    <entry id="307" name="DelMail_S2C_Msg" serverName="gamesvr"/>
    <entry id="308" name="GetVisitorSnapshots_C2S_Msg" serverName="gamesvr"/>
    <entry id="309" name="GetVisitorSnapshots_S2C_Msg" serverName="gamesvr"/>
    <entry id="310" name="ChangeBasicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="311" name="ChangeBasicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="312" name="ChangeFollowingStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="313" name="ChangeFollowingStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="314" name="GetFollowers_C2S_Msg" serverName="gamesvr"/>
    <entry id="315" name="GetFollowers_S2C_Msg" serverName="gamesvr"/>
    <entry id="316" name="GetFollowings_C2S_Msg" serverName="gamesvr"/>
    <entry id="317" name="GetFollowings_S2C_Msg" serverName="gamesvr"/>
    <entry id="318" name="StarPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="319" name="StarPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="320" name="ChangeAvatar_C2S_Msg" serverName="gamesvr"/>
    <entry id="321" name="ChangeAvatar_S2C_Msg" serverName="gamesvr"/>
    <entry id="322" name="ChangeShowAvatarType_C2S_Msg" serverName="gamesvr"/>
    <entry id="323" name="ChangeShowAvatarType_S2C_Msg" serverName="gamesvr"/>
    <entry id="324" name="DeliverResource_C2S_Msg" serverName="gamesvr"/>
    <entry id="325" name="DeliverResource_S2C_Msg" serverName="gamesvr"/>
    <entry id="326" name="GetResourceMd5_C2S_Msg" serverName="gamesvr"/>
    <entry id="327" name="GetResourceMd5_S2C_Msg" serverName="gamesvr"/>
    <entry id="328" name="ClientKVConfNtf" serverName="gamesvr"/>
    <entry id="329" name="SceneEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="330" name="SceneEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="331" name="SceneExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="332" name="SceneExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="333" name="SceneEnterNtf" serverName="gamesvr"/>
    <entry id="334" name="SceneExitNtf" serverName="gamesvr"/>
    <entry id="335" name="SceneReportData_C2S_Msg" serverName="gamesvr"/>
    <entry id="336" name="SceneReportData_S2C_Msg" serverName="gamesvr"/>
    <entry id="337" name="SceneDataSyncNtf" serverName="gamesvr"/>
    <entry id="338" name="SceneReportAction_C2S_Msg" serverName="gamesvr"/>
    <entry id="339" name="SceneReportAction_S2C_Msg" serverName="gamesvr"/>
    <entry id="340" name="SceneGetServer_C2S_Msg" serverName="gamesvr"/>
    <entry id="341" name="SceneGetServer_S2C_Msg" serverName="gamesvr"/>
    <entry id="342" name="SceneGetOngoingInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="343" name="SceneGetOngoingInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="344" name="SceneBaseDataNtf" serverName="gamesvr"/>
    <entry id="345" name="SceneReconnectNtf" serverName="gamesvr"/>
    <entry id="346" name="SceneInteractActionInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="347" name="SceneInteractActionInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="348" name="SceneInteractActionAccept_C2S_Msg" serverName="gamesvr"/>
    <entry id="349" name="SceneInteractActionAccept_S2C_Msg" serverName="gamesvr"/>
    <entry id="350" name="SceneInteractActionCancel_C2S_Msg" serverName="gamesvr"/>
    <entry id="351" name="SceneInteractActionCancel_S2C_Msg" serverName="gamesvr"/>
    <entry id="352" name="SceneInteractActionActive_C2S_Msg" serverName="gamesvr"/>
    <entry id="353" name="SceneInteractActionActive_S2C_Msg" serverName="gamesvr"/>
    <entry id="354" name="SceneInteractActionInviteNtf" serverName="gamesvr"/>
    <entry id="355" name="SceneInteractActionAcceptNtf" serverName="gamesvr"/>
    <entry id="356" name="SceneInteractActionActiveNtf" serverName="gamesvr"/>
    <entry id="357" name="SceneInteractActionCancelNtf" serverName="gamesvr"/>
    <entry id="358" name="SceneSettlementNtf" serverName="gamesvr"/>
    <entry id="359" name="SceneSettlement_C2S_Msg" serverName="gamesvr"/>
    <entry id="360" name="SceneSettlement_S2C_Msg" serverName="gamesvr"/>
    <entry id="361" name="ErrorCodeNtf" serverName="gamesvr"/>
    <entry id="362" name="ReportEvent_C2S_Msg" serverName="gamesvr"/>
    <entry id="363" name="ReportEvent_S2C_Msg" serverName="gamesvr"/>
    <entry id="364" name="LetsGoTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="365" name="LetsGoTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="366" name="LetsGoGetPlayerProfile_C2S_Msg" serverName="gamesvr"/>
    <entry id="367" name="LetsGoGetPlayerProfile_S2C_Msg" serverName="gamesvr"/>
    <entry id="368" name="LetsGoGetBattleSnapshotRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="369" name="LetsGoGetBattleSnapshotRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="370" name="LetsGoGetBattleDetailRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="371" name="LetsGoGetBattleDetailRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="372" name="LetsGoSetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="373" name="LetsGoSetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="374" name="LetsGoGetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="375" name="LetsGoGetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="376" name="RoomModModeUnlockNtf" serverName="gamesvr"/>
    <entry id="377" name="LetsGoBattleSettlementNtf" serverName="gamesvr"/>
    <entry id="378" name="LetsGoGetQualifyingReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="379" name="LetsGoGetQualifyingReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="380" name="LetsGoSeasonSettlementNtf" serverName="gamesvr"/>
    <entry id="381" name="LetsGoOpenQualifyingList_C2S_Msg" serverName="gamesvr"/>
    <entry id="382" name="LetsGoOpenQualifyingList_S2C_Msg" serverName="gamesvr"/>
    <entry id="383" name="LoginNoticeNtf" serverName="gamesvr"/>
    <entry id="384" name="PermitRefreshNtf_S2C_Msg" serverName="gamesvr"/>
    <entry id="385" name="PermitGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="386" name="PermitGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="387" name="PermitTaskComplete_C2S_Msg" serverName="gamesvr"/>
    <entry id="388" name="PermitTaskComplete_S2C_Msg" serverName="gamesvr"/>
    <entry id="389" name="PermitLevelReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="390" name="PermitLevelReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="391" name="PermitAllLevelReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="392" name="PermitAllLevelReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="393" name="PermitInfoNtf" serverName="gamesvr"/>
    <entry id="394" name="BattleReportModData_C2S_Msg" serverName="gamesvr"/>
    <entry id="395" name="BattleReportModData_S2C_Msg" serverName="gamesvr"/>
    <entry id="396" name="BattleModDataLogin_C2S_Msg" serverName="gamesvr"/>
    <entry id="397" name="BattleModDataLogin_S2C_Msg" serverName="gamesvr"/>
    <entry id="398" name="BattleRequestModData_C2S_Msg" serverName="gamesvr"/>
    <entry id="399" name="BattleRequestModData_S2C_Msg" serverName="gamesvr"/>
    <entry id="400" name="BattleRequestModDataNtf" serverName="gamesvr"/>
    <entry id="401" name="BattleSyncModDataNtf" serverName="gamesvr"/>
    <entry id="402" name="BattleRequestModEnd_C2S_Msg" serverName="gamesvr"/>
    <entry id="403" name="BattleRequestModEnd_S2C_Msg" serverName="gamesvr"/>
    <entry id="404" name="BattleSyncModTime_C2S_Msg" serverName="gamesvr"/>
    <entry id="405" name="BattleSyncModTime_S2C_Msg" serverName="gamesvr"/>
    <entry id="406" name="BattlePlayerReadyMod_C2S_Msg" serverName="gamesvr"/>
    <entry id="407" name="BattlePlayerReadyMod_S2C_Msg" serverName="gamesvr"/>
    <entry id="408" name="BattleSeedDateNtf" serverName="gamesvr"/>
    <entry id="409" name="BattleModEndNtf" serverName="gamesvr"/>
    <entry id="410" name="BattleModStartNtf" serverName="gamesvr"/>
    <entry id="411" name="BattleGiveUpMod_C2S_Msg" serverName="gamesvr"/>
    <entry id="412" name="BattleGiveUpMod_S2C_Msg" serverName="gamesvr"/>
    <entry id="413" name="BattleGetOngoingDsAddr_C2S_Msg" serverName="gamesvr"/>
    <entry id="414" name="BattleGetOngoingDsAddr_S2C_Msg" serverName="gamesvr"/>
    <entry id="415" name="BattleSongAchNtf" serverName="gamesvr"/>
    <entry id="416" name="BattleExitNtf" serverName="gamesvr"/>
    <entry id="417" name="BattleSendExpression_C2S_Msg" serverName="gamesvr"/>
    <entry id="418" name="BattleSendExpression_S2C_Msg" serverName="gamesvr"/>
    <entry id="419" name="BattleExpressionNtf" serverName="gamesvr"/>
    <entry id="420" name="RecvRewardListNtf" serverName="gamesvr"/>
    <entry id="421" name="GetRankTopNPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="422" name="GetRankTopNPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="423" name="GetPlayerRankInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="424" name="GetPlayerRankInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="425" name="GetFriendRankTopNPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="426" name="GetFriendRankTopNPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="427" name="GetSceneRankTopNPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="428" name="GetSceneRankTopNPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="429" name="AddFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="430" name="AddFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="431" name="RemoveFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="432" name="RemoveFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="433" name="AgreeFriendApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="434" name="AgreeFriendApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="435" name="DenyFriendApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="436" name="DenyFriendApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="437" name="GetFriendApplyList_C2S_Msg" serverName="gamesvr"/>
    <entry id="438" name="GetFriendApplyList_S2C_Msg" serverName="gamesvr"/>
    <entry id="439" name="SearchFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="440" name="SearchFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="441" name="FriendApplyNtf" serverName="gamesvr"/>
    <entry id="442" name="FriendAgreeNtf" serverName="gamesvr"/>
    <entry id="443" name="FriendDenyNtf" serverName="gamesvr"/>
    <entry id="444" name="RemoveRecommendFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="445" name="RemoveRecommendFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="446" name="ModFriendInfoNtf" serverName="gamesvr"/>
    <entry id="447" name="PlatFriendInfoNtf" serverName="gamesvr"/>
    <entry id="448" name="FriendRemoveNtf" serverName="gamesvr"/>
    <entry id="449" name="RecentPlayInfoNtf" serverName="gamesvr"/>
    <entry id="450" name="UpdateFriendHotData_C2S_Msg" serverName="gamesvr"/>
    <entry id="451" name="UpdateFriendHotData_S2C_Msg" serverName="gamesvr"/>
    <entry id="452" name="GiveFriendGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="453" name="GiveFriendGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="454" name="GetRecommendationFriends_C2S_Msg" serverName="gamesvr"/>
    <entry id="455" name="GetRecommendationFriends_S2C_Msg" serverName="gamesvr"/>
    <entry id="456" name="AddBlack_C2S_Msg" serverName="gamesvr"/>
    <entry id="457" name="AddBlack_S2C_Msg" serverName="gamesvr"/>
    <entry id="458" name="RemoveBlack_C2S_Msg" serverName="gamesvr"/>
    <entry id="459" name="RemoveBlack_S2C_Msg" serverName="gamesvr"/>
    <entry id="460" name="BlackInfoNtf" serverName="gamesvr"/>
    <entry id="461" name="AddCouples_C2S_Msg" serverName="gamesvr"/>
    <entry id="462" name="AddCouples_S2C_Msg" serverName="gamesvr"/>
    <entry id="463" name="RemoveCouples_C2S_Msg" serverName="gamesvr"/>
    <entry id="464" name="RemoveCouples_S2C_Msg" serverName="gamesvr"/>
    <entry id="465" name="AgreeCouplesApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="466" name="AgreeCouplesApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="467" name="DenyCouplesApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="468" name="DenyCouplesApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="469" name="CouplesApplyNtf" serverName="gamesvr"/>
    <entry id="470" name="CouplesAgreeNtf" serverName="gamesvr"/>
    <entry id="471" name="CouplesDenyNtf" serverName="gamesvr"/>
    <entry id="472" name="UpdateSceneNeighborHotData_C2S_Msg" serverName="gamesvr"/>
    <entry id="473" name="UpdateSceneNeighborHotData_S2C_Msg" serverName="gamesvr"/>
    <entry id="474" name="SceneNeighborInfoNtf" serverName="gamesvr"/>
    <entry id="475" name="QQInvity_C2S_Msg" serverName="gamesvr"/>
    <entry id="476" name="QQInvity_S2C_Msg" serverName="gamesvr"/>
    <entry id="477" name="AddFriendNoticeServer_C2S_Msg" serverName="gamesvr"/>
    <entry id="478" name="AddFriendNoticeServer_S2C_Msg" serverName="gamesvr"/>
    <entry id="479" name="ChatSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="480" name="ChatSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="481" name="ChatOnInput_C2S_Msg" serverName="gamesvr"/>
    <entry id="482" name="ChatOnInput_S2C_Msg" serverName="gamesvr"/>
    <entry id="483" name="ChatGroupOperationNtf" serverName="gamesvr"/>
    <entry id="484" name="GetTailMessages_C2S_Msg" serverName="gamesvr"/>
    <entry id="485" name="GetTailMessages_S2C_Msg" serverName="gamesvr"/>
    <entry id="486" name="GetPreMessages_C2S_Msg" serverName="gamesvr"/>
    <entry id="487" name="GetPreMessages_S2C_Msg" serverName="gamesvr"/>
    <entry id="488" name="ChatPull_C2S_Msg" serverName="gamesvr"/>
    <entry id="489" name="ChatPull_S2C_Msg" serverName="gamesvr"/>
    <entry id="490" name="ChatMsgNtf" serverName="gamesvr"/>
    <entry id="491" name="ChatMsgRedDotNtf" serverName="gamesvr"/>
    <entry id="492" name="ChatClearNotRead_C2S_Msg" serverName="gamesvr"/>
    <entry id="493" name="ChatClearNotRead_S2C_Msg" serverName="gamesvr"/>
    <entry id="494" name="ChatRemoveRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="495" name="ChatRemoveRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="496" name="GroupChatMemModifyNtf" serverName="gamesvr"/>
    <entry id="497" name="ChatCreateGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="498" name="ChatCreateGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="499" name="ChatQuit_C2S_Msg" serverName="gamesvr"/>
    <entry id="500" name="ChatQuit_S2C_Msg" serverName="gamesvr"/>
    <entry id="501" name="ChatGetQuickTextList_C2S_Msg" serverName="gamesvr"/>
    <entry id="502" name="ChatGetQuickTextList_S2C_Msg" serverName="gamesvr"/>
    <entry id="503" name="ChatReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="504" name="ChatReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="505" name="ChatRoomRecruit_C2S_Msg" serverName="gamesvr"/>
    <entry id="506" name="ChatRoomRecruit_S2C_Msg" serverName="gamesvr"/>
    <entry id="507" name="ChatChannelSwitch_C2S_Msg" serverName="gamesvr"/>
    <entry id="508" name="ChatChannelSwitch_S2C_Msg" serverName="gamesvr"/>
    <entry id="509" name="BagUseItemMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="510" name="BagUseItemMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="511" name="BagSellItemMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="512" name="BagSellItemMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="513" name="BagMoveItemMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="514" name="BagMoveItemMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="515" name="BagDestroyItemMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="516" name="BagDestroyItemMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="517" name="CheckEquipItemsExpireMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="518" name="CheckEquipItemsExpireMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="519" name="BagRewardNtfFinishMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="520" name="BagRewardNtfFinishMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="521" name="BagItemNumUpToMaxNtf" serverName="gamesvr"/>
    <entry id="522" name="BagCommonGetItemsNtf" serverName="gamesvr"/>
    <entry id="523" name="BagItemNumUpToMaxReplaceNtf" serverName="gamesvr"/>
    <entry id="524" name="PvEGetEntryList_C2S_Msg" serverName="gamesvr"/>
    <entry id="525" name="PvEGetEntryList_S2C_Msg" serverName="gamesvr"/>
    <entry id="526" name="PvEGetEntryInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="527" name="PvEGetEntryInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="528" name="PvEGetChapterInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="529" name="PvEGetChapterInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="530" name="PvEGetStageInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="531" name="PvEGetStageInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="532" name="PvEClearRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="533" name="PvEClearRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="534" name="PvEGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="535" name="PvEGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="536" name="PvEGetEntryProcessReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="537" name="PvEGetEntryProcessReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="538" name="PvEGetChapterRowColumnReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="539" name="PvEGetChapterRowColumnReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="540" name="PvEStartStage_C2S_Msg" serverName="gamesvr"/>
    <entry id="541" name="PvEStartStage_S2C_Msg" serverName="gamesvr"/>
    <entry id="542" name="DeliverResources_C2S_Msg" serverName="dirsvr"/>
    <entry id="543" name="DeliverResources_S2C_Msg" serverName="dirsvr"/>
    <entry id="544" name="DirGetPlayerInfo_C2S_Msg" serverName="dirsvr"/>
    <entry id="545" name="DirGetPlayerInfo_S2C_Msg" serverName="dirsvr"/>
    <entry id="546" name="DevEnvResInfoNtf" serverName="dirsvr"/>
    <entry id="547" name="ExpAndLevelChangeNtf" serverName="gamesvr"/>
    <entry id="548" name="TeamRecruitPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="549" name="TeamRecruitPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="550" name="TeamRecruitQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="551" name="TeamRecruitQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="552" name="TeamRecruitJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="553" name="TeamRecruitJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="554" name="ClientSetCache_C2S_Msg" serverName="gamesvr"/>
    <entry id="555" name="ClientSetCache_S2C_Msg" serverName="gamesvr"/>
    <entry id="556" name="ClientDelCache_C2S_Msg" serverName="gamesvr"/>
    <entry id="557" name="ClientDelCache_S2C_Msg" serverName="gamesvr"/>
    <entry id="558" name="GetTask_C2S_Msg" serverName="gamesvr"/>
    <entry id="559" name="GetTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="560" name="TaskChangeNtf" serverName="gamesvr"/>
    <entry id="561" name="ChangePlayerRankGeoInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="562" name="ChangePlayerRankGeoInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="563" name="GetGeoRankTopNPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="564" name="GetGeoRankTopNPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="565" name="AddRelation_C2S_Msg" serverName="gamesvr"/>
    <entry id="566" name="AddRelation_S2C_Msg" serverName="gamesvr"/>
    <entry id="567" name="RemoveRelation_C2S_Msg" serverName="gamesvr"/>
    <entry id="568" name="RemoveRelation_S2C_Msg" serverName="gamesvr"/>
    <entry id="569" name="AgreeRelation_C2S_Msg" serverName="gamesvr"/>
    <entry id="570" name="AgreeRelation_S2C_Msg" serverName="gamesvr"/>
    <entry id="571" name="DenyRelation_C2S_Msg" serverName="gamesvr"/>
    <entry id="572" name="DenyRelation_S2C_Msg" serverName="gamesvr"/>
    <entry id="573" name="LobbyGetDsServer_C2S_Msg" serverName="gamesvr"/>
    <entry id="574" name="LobbyGetDsServer_S2C_Msg" serverName="gamesvr"/>
    <entry id="575" name="LobbyJumpNtf" serverName="gamesvr"/>
    <entry id="576" name="SearchPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="577" name="SearchPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="578" name="SeasonInfoNtf" serverName="gamesvr"/>
    <entry id="579" name="RefreshSeasonPanel_C2S_Msg" serverName="gamesvr"/>
    <entry id="580" name="RefreshSeasonPanel_S2C_Msg" serverName="gamesvr"/>
    <entry id="581" name="GetPlayerColdData_C2S_Msg" serverName="gamesvr"/>
    <entry id="582" name="GetPlayerColdData_S2C_Msg" serverName="gamesvr"/>
    <entry id="583" name="PermitUnlock_C2S_Msg" serverName="gamesvr"/>
    <entry id="584" name="PermitUnlock_S2C_Msg" serverName="gamesvr"/>
    <entry id="585" name="PlayModeUnlockNtf" serverName="gamesvr"/>
    <entry id="586" name="RoomStartNtf" serverName="gamesvr"/>
    <entry id="587" name="RoomConfirmStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="588" name="RoomStartMatchCancelNtf" serverName="gamesvr"/>
    <entry id="589" name="RoomConfirmStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="590" name="RoomLeaderTransit_C2S_Msg" serverName="gamesvr"/>
    <entry id="591" name="RoomLeaderTransit_S2C_Msg" serverName="gamesvr"/>
    <entry id="592" name="RoomDisband_C2S_Msg" serverName="gamesvr"/>
    <entry id="593" name="RoomDisband_S2C_Msg" serverName="gamesvr"/>
    <entry id="594" name="RoomLobbyGather_C2S_Msg" serverName="gamesvr"/>
    <entry id="595" name="RoomLobbyGather_S2C_Msg" serverName="gamesvr"/>
    <entry id="596" name="RoomLobbyGatherNtf" serverName="gamesvr"/>
    <entry id="597" name="RoomLobbyTransit_C2S_Msg" serverName="gamesvr"/>
    <entry id="598" name="RoomLobbyTransit_S2C_Msg" serverName="gamesvr"/>
    <entry id="599" name="DeletePlayerRankGeoInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="600" name="DeletePlayerRankGeoInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="601" name="PlayerCoinChangeNtf" serverName="gamesvr"/>
    <entry id="602" name="PlayerItemChangeNtf" serverName="gamesvr"/>
    <entry id="603" name="RelationApplyAddNtf" serverName="gamesvr"/>
    <entry id="604" name="RelationApplyRemoveNtf" serverName="gamesvr"/>
    <entry id="605" name="RelationAddNtf" serverName="gamesvr"/>
    <entry id="606" name="RelationRemoveNtf" serverName="gamesvr"/>
    <entry id="607" name="TeamRecruitQuickJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="608" name="TeamRecruitQuickJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="609" name="PermitBuyLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="610" name="PermitBuyLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="611" name="PermitUnlockLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="612" name="UnLockMatchTypeNtf" serverName="gamesvr"/>
    <entry id="613" name="SendGoldCoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="614" name="SendGoldCoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="615" name="UgcList_C2S_Msg" serverName="gamesvr"/>
    <entry id="616" name="UgcList_S2C_Msg" serverName="gamesvr"/>
    <entry id="617" name="UgcCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="618" name="UgcCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="619" name="UgcDelete_C2S_Msg" serverName="gamesvr"/>
    <entry id="620" name="UgcDelete_S2C_Msg" serverName="gamesvr"/>
    <entry id="621" name="SaveWork_C2S_Msg" serverName="gamesvr"/>
    <entry id="622" name="SaveWork_S2C_Msg" serverName="gamesvr"/>
    <entry id="623" name="GetSaveWork_C2S_Msg" serverName="gamesvr"/>
    <entry id="624" name="GetSaveWork_S2C_Msg" serverName="gamesvr"/>
    <entry id="625" name="DrawRaffle_C2S_Msg" serverName="gamesvr"/>
    <entry id="626" name="DrawRaffle_S2C_Msg" serverName="gamesvr"/>
    <entry id="627" name="PlatShareActiveList_C2S_Msg" serverName="gamesvr"/>
    <entry id="628" name="PlatShareActiveList_S2C_Msg" serverName="gamesvr"/>
    <entry id="629" name="PlatShareActiveSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="630" name="PlatShareActiveSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="631" name="PlatShareActiveReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="632" name="PlatShareActiveReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="633" name="ActivityListAll_C2S_Msg" serverName="gamesvr"/>
    <entry id="634" name="ActivityListAll_S2C_Msg" serverName="gamesvr"/>
    <entry id="635" name="ActivityReadDotNtf" serverName="gamesvr"/>
    <entry id="636" name="ActivityClickRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="637" name="ActivityClickRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="638" name="ListLuckyMoney_C2S_Msg" serverName="gamesvr"/>
    <entry id="639" name="ListLuckyMoney_S2C_Msg" serverName="gamesvr"/>
    <entry id="640" name="ShareLuckyMoney_C2S_Msg" serverName="gamesvr"/>
    <entry id="641" name="ShareLuckyMoney_S2C_Msg" serverName="gamesvr"/>
    <entry id="642" name="LoginFromBacklinksParams_C2S_Msg" serverName="gamesvr"/>
    <entry id="643" name="LoginFromBacklinksParams_S2C_Msg" serverName="gamesvr"/>
    <entry id="644" name="GetLuckyMoneyReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="645" name="GetLuckyMoneyReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="646" name="GetLuckyMoneyDetailInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="647" name="GetLuckyMoneyDetailInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="648" name="GetRechargeConf_C2S_Msg" serverName="gamesvr"/>
    <entry id="649" name="GetRechargeConf_S2C_Msg" serverName="gamesvr"/>
    <entry id="650" name="RechargeConfNtf" serverName="gamesvr"/>
    <entry id="651" name="GetPlayerColdDataByOpenId_C2S_Msg" serverName="gamesvr"/>
    <entry id="652" name="GetPlayerColdDataByOpenId_S2C_Msg" serverName="gamesvr"/>
    <entry id="653" name="GetUidFromOpenId_C2S_Msg" serverName="gamesvr"/>
    <entry id="654" name="GetUidFromOpenId_S2C_Msg" serverName="gamesvr"/>
    <entry id="655" name="MallGetShopCommodity_C2S_Msg" serverName="gamesvr"/>
    <entry id="656" name="MallGetShopCommodity_S2C_Msg" serverName="gamesvr"/>
    <entry id="657" name="DreamNewStarTaskList_C2S_Msg" serverName="gamesvr"/>
    <entry id="658" name="DreamNewStarTaskList_S2C_Msg" serverName="gamesvr"/>
    <entry id="659" name="FittingSlotSave_C2S_Msg" serverName="gamesvr"/>
    <entry id="660" name="FittingSlotSave_S2C_Msg" serverName="gamesvr"/>
    <entry id="661" name="FittingSlotSelect_C2S_Msg" serverName="gamesvr"/>
    <entry id="662" name="FittingSlotSelect_S2C_Msg" serverName="gamesvr"/>
    <entry id="663" name="BagDressUpItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="664" name="BagDressUpItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="665" name="MallBatchGetCommodity_C2S_Msg" serverName="gamesvr"/>
    <entry id="666" name="MallBatchGetCommodity_S2C_Msg" serverName="gamesvr"/>
    <entry id="667" name="Recharge_C2S_Msg" serverName="gamesvr"/>
    <entry id="668" name="Recharge_S2C_Msg" serverName="gamesvr"/>
    <entry id="669" name="UgcCopy_C2S_Msg" serverName="gamesvr"/>
    <entry id="670" name="UgcCopy_S2C_Msg" serverName="gamesvr"/>
    <entry id="671" name="UgcMapModify_C2S_Msg" serverName="gamesvr"/>
    <entry id="672" name="UgcMapModify_S2C_Msg" serverName="gamesvr"/>
    <entry id="673" name="UgcRegain_C2S_Msg" serverName="gamesvr"/>
    <entry id="674" name="UgcRegain_S2C_Msg" serverName="gamesvr"/>
    <entry id="675" name="UgcPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="676" name="UgcPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="677" name="UgcTakeOff_C2S_Msg" serverName="gamesvr"/>
    <entry id="678" name="UgcTakeOff_S2C_Msg" serverName="gamesvr"/>
    <entry id="679" name="GetRechargeLevelReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="680" name="GetRechargeLevelReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="681" name="WealthBankCheckIn_C2S_Msg" serverName="gamesvr"/>
    <entry id="682" name="WealthBankCheckIn_S2C_Msg" serverName="gamesvr"/>
    <entry id="683" name="WealthBankGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="684" name="WealthBankGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="685" name="WealthBankReceiveDeposit_C2S_Msg" serverName="gamesvr"/>
    <entry id="686" name="WealthBankReceiveDeposit_S2C_Msg" serverName="gamesvr"/>
    <entry id="687" name="SquadGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="688" name="SquadGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="689" name="SquadJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="690" name="SquadJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="691" name="SquadTaskInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="692" name="SquadTaskInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="693" name="RechargeLvChangeNtf" serverName="gamesvr"/>
    <entry id="694" name="UgcApplyKeyInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="695" name="UgcMapSet_C2S_Msg" serverName="gamesvr"/>
    <entry id="696" name="UgcMapSet_S2C_Msg" serverName="gamesvr"/>
    <entry id="699" name="MidasBuyGoodsNtf" serverName="gamesvr"/>
    <entry id="700" name="RechargeDepositPurchase_C2S_Msg" serverName="gamesvr"/>
    <entry id="701" name="RechargeDepositPurchase_S2C_Msg" serverName="gamesvr"/>
    <entry id="702" name="RechargeDepositPurchaseNtf" serverName="gamesvr"/>
    <entry id="703" name="RechargeDepositGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="704" name="RechargeDepositGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="705" name="RechargeDepositFetchPrice_C2S_Msg" serverName="gamesvr"/>
    <entry id="706" name="RechargeDepositFetchPrice_S2C_Msg" serverName="gamesvr"/>
    <entry id="707" name="RechargeDepositConfigNtf" serverName="gamesvr"/>
    <entry id="708" name="BagCheckItemExpire_C2S_Msg" serverName="gamesvr"/>
    <entry id="709" name="BagCheckItemExpire_S2C_Msg" serverName="gamesvr"/>
    <entry id="710" name="FirstChargeTaskInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="711" name="FirstChargeTaskInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="712" name="UgcApplyKeyInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="713" name="UgcPublishList_C2S_Msg" serverName="gamesvr"/>
    <entry id="714" name="UgcPublishList_S2C_Msg" serverName="gamesvr"/>
    <entry id="715" name="GaveLike_C2S_Msg" serverName="gamesvr"/>
    <entry id="716" name="GaveLike_S2C_Msg" serverName="gamesvr"/>
    <entry id="717" name="Collect_C2S_Msg" serverName="gamesvr"/>
    <entry id="718" name="Collect_S2C_Msg" serverName="gamesvr"/>
    <entry id="719" name="Subscribe_C2S_Msg" serverName="gamesvr"/>
    <entry id="720" name="Subscribe_S2C_Msg" serverName="gamesvr"/>
    <entry id="721" name="MallBatchBuyMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="722" name="MallBatchBuyMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="723" name="MallDirectBuyNtf" serverName="gamesvr"/>
    <entry id="724" name="MallRealTimeNtf" serverName="gamesvr"/>
    <entry id="725" name="GetMessageSlipList_C2S_Msg" serverName="gamesvr"/>
    <entry id="726" name="GetMessageSlipList_S2C_Msg" serverName="gamesvr"/>
    <entry id="727" name="AddMessageSlip_C2S_Msg" serverName="gamesvr"/>
    <entry id="728" name="AddMessageSlip_S2C_Msg" serverName="gamesvr"/>
    <entry id="729" name="DeleteMessageSlip_C2S_Msg" serverName="gamesvr"/>
    <entry id="730" name="DeleteMessageSlip_S2C_Msg" serverName="gamesvr"/>
    <entry id="731" name="GetMessageSlipDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="732" name="GetMessageSlipDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="733" name="GetMessageCommentList_C2S_Msg" serverName="gamesvr"/>
    <entry id="734" name="GetMessageCommentList_S2C_Msg" serverName="gamesvr"/>
    <entry id="735" name="AddMessageComment_C2S_Msg" serverName="gamesvr"/>
    <entry id="736" name="AddMessageComment_S2C_Msg" serverName="gamesvr"/>
    <entry id="737" name="DeleteMessageComment_C2S_Msg" serverName="gamesvr"/>
    <entry id="738" name="DeleteMessageComment_S2C_Msg" serverName="gamesvr"/>
    <entry id="739" name="GetMessageFavourList_C2S_Msg" serverName="gamesvr"/>
    <entry id="740" name="GetMessageFavourList_S2C_Msg" serverName="gamesvr"/>
    <entry id="741" name="AddMessageFavour_C2S_Msg" serverName="gamesvr"/>
    <entry id="742" name="AddMessageFavour_S2C_Msg" serverName="gamesvr"/>
    <entry id="743" name="DeleteMessageFavour_C2S_Msg" serverName="gamesvr"/>
    <entry id="744" name="DeleteMessageFavour_S2C_Msg" serverName="gamesvr"/>
    <entry id="745" name="MessageSlipRedDotNtf" serverName="gamesvr"/>
    <entry id="746" name="MidasPayResult_C2S_Msg" serverName="gamesvr"/>
    <entry id="747" name="MidasPayResult_S2C_Msg" serverName="gamesvr"/>
    <entry id="748" name="UgcOperate_C2S_Msg" serverName="gamesvr"/>
    <entry id="749" name="UgcOperate_S2C_Msg" serverName="gamesvr"/>
    <entry id="750" name="UgcOperateCancel_C2S_Msg" serverName="gamesvr"/>
    <entry id="751" name="UgcOperateCancel_S2C_Msg" serverName="gamesvr"/>
    <entry id="752" name="ClickMessageSlipRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="753" name="ClickMessageSlipRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="754" name="MatchProcessNtf" serverName="gamesvr"/>
    <entry id="755" name="UgcMapScreen_C2S_Msg" serverName="gamesvr"/>
    <entry id="756" name="UgcMapScreen_S2C_Msg" serverName="gamesvr"/>
    <entry id="757" name="UgcMapType_C2S_Msg" serverName="gamesvr"/>
    <entry id="758" name="UgcMapType_S2C_Msg" serverName="gamesvr"/>
    <entry id="759" name="UgcMapSearch_C2S_Msg" serverName="gamesvr"/>
    <entry id="760" name="UgcMapSearch_S2C_Msg" serverName="gamesvr"/>
    <entry id="761" name="UgcMapPublishModify_C2S_Msg" serverName="gamesvr"/>
    <entry id="762" name="UgcMapPublishModify_S2C_Msg" serverName="gamesvr"/>
    <entry id="763" name="GetMailBriefs_C2S_Msg" serverName="gamesvr"/>
    <entry id="764" name="GetMailBriefs_S2C_Msg" serverName="gamesvr"/>
    <entry id="766" name="MailBriefsNtf" serverName="gamesvr"/>
    <entry id="767" name="MailStateNtf" serverName="gamesvr"/>
    <entry id="768" name="ChangeName_C2S_Msg" serverName="gamesvr"/>
    <entry id="769" name="ChangeName_S2C_Msg" serverName="gamesvr"/>
    <entry id="770" name="FittingSingleItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="771" name="FittingSingleItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="772" name="MailDelNtf" serverName="gamesvr"/>
    <entry id="773" name="MallCommoditySetRedPointShow_C2S_Msg" serverName="gamesvr"/>
    <entry id="774" name="MallCommoditySetRedPointShow_S2C_Msg" serverName="gamesvr"/>
    <entry id="775" name="UgcOpSubscribe_C2S_Msg" serverName="gamesvr"/>
    <entry id="776" name="UgcOpSubscribe_S2C_Msg" serverName="gamesvr"/>
    <entry id="777" name="UgcMySubscribe_C2S_Msg" serverName="gamesvr"/>
    <entry id="778" name="UgcMySubscribe_S2C_Msg" serverName="gamesvr"/>
    <entry id="779" name="UgcMyFans_C2S_Msg" serverName="gamesvr"/>
    <entry id="780" name="UgcMyFans_S2C_Msg" serverName="gamesvr"/>
    <entry id="781" name="UgcPublishDetails_C2S_Msg" serverName="gamesvr"/>
    <entry id="782" name="UgcPublishDetails_S2C_Msg" serverName="gamesvr"/>
    <entry id="783" name="RechargeMonthCard_C2S_Msg" serverName="gamesvr"/>
    <entry id="784" name="RechargeMonthCard_S2C_Msg" serverName="gamesvr"/>
    <entry id="787" name="ReceiveMonthCardDailyItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="788" name="ReceiveMonthCardDailyItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="789" name="MonthCardDeliverGoodsNtf" serverName="gamesvr"/>
    <entry id="790" name="UgcObjectOperate_C2S_Msg" serverName="gamesvr"/>
    <entry id="791" name="UgcObjectOperate_S2C_Msg" serverName="gamesvr"/>
    <entry id="792" name="UgcListForObject_C2S_Msg" serverName="gamesvr"/>
    <entry id="793" name="UgcListForObject_S2C_Msg" serverName="gamesvr"/>
    <entry id="794" name="UgcPublishRegain_C2S_Msg" serverName="gamesvr"/>
    <entry id="795" name="UgcPublishRegain_S2C_Msg" serverName="gamesvr"/>
    <entry id="796" name="UgcOpSubTop_C2S_Msg" serverName="gamesvr"/>
    <entry id="797" name="UgcOpSubTop_S2C_Msg" serverName="gamesvr"/>
    <entry id="798" name="CheckInPlanGetMakeUpTicket_C2S_Msg" serverName="gamesvr"/>
    <entry id="799" name="CheckInPlanGetMakeUpTicket_S2C_Msg" serverName="gamesvr"/>
    <entry id="800" name="CheckInPlanGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="801" name="CheckInPlanGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="802" name="CheckInPlanCheckIn_C2S_Msg" serverName="gamesvr"/>
    <entry id="803" name="CheckInPlanCheckIn_S2C_Msg" serverName="gamesvr"/>
    <entry id="804" name="CheckInPlanDraw_C2S_Msg" serverName="gamesvr"/>
    <entry id="805" name="CheckInPlanDraw_S2C_Msg" serverName="gamesvr"/>
    <entry id="806" name="UgcCreateGroupObject_C2S_Msg" serverName="gamesvr"/>
    <entry id="807" name="UgcCreateGroupObject_S2C_Msg" serverName="gamesvr"/>
    <entry id="808" name="UgcSinglePassLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="809" name="UgcSinglePassLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="810" name="UgcCreateRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="811" name="UgcCreateRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="812" name="UgcRoomList_C2S_Msg" serverName="gamesvr"/>
    <entry id="813" name="UgcRoomList_S2C_Msg" serverName="gamesvr"/>
    <entry id="814" name="UgcJoinRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="815" name="UgcJoinRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="816" name="UgcExitRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="817" name="UgcExitRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="818" name="PlayerGetRecordItemForLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="819" name="PlayerGetRecordItemForLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="820" name="PlatCommon_C2S_Msg" serverName="gamesvr"/>
    <entry id="821" name="PlatCommon_S2C_Msg" serverName="gamesvr"/>
    <entry id="822" name="UgcAccept_C2S_Msg" serverName="gamesvr"/>
    <entry id="823" name="UgcAccept_S2C_Msg" serverName="gamesvr"/>
    <entry id="824" name="BatchGetPlayerPublicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="825" name="BatchGetPlayerPublicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="826" name="DirCheckPlayerNickName_C2S_Msg" serverName="dirsvr"/>
    <entry id="827" name="DirCheckPlayerNickName_S2C_Msg" serverName="dirsvr"/>
    <entry id="828" name="LobbyEnterPlayerDs_C2S_Msg" serverName="gamesvr"/>
    <entry id="829" name="LobbyEnterPlayerDs_S2C_Msg" serverName="gamesvr"/>
    <entry id="830" name="RelationStateNtf" serverName="gamesvr"/>
    <entry id="831" name="FetchTopRank_C2S_Msg" serverName="gamesvr"/>
    <entry id="832" name="FetchTopRank_S2C_Msg" serverName="gamesvr"/>
    <entry id="833" name="UgcCommonSave_C2S_Msg" serverName="gamesvr"/>
    <entry id="834" name="UgcCommonSave_S2C_Msg" serverName="gamesvr"/>
    <entry id="835" name="UgcGetCommonSave_C2S_Msg" serverName="gamesvr"/>
    <entry id="836" name="UgcGetCommonSave_S2C_Msg" serverName="gamesvr"/>
    <entry id="837" name="LobbyRandomEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="838" name="LobbyRandomEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="839" name="ChangeLabelInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="840" name="ChangeLabelInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="841" name="ChangeStateInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="842" name="ChangeStateInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="843" name="getUgcAccept_C2S_Msg" serverName="gamesvr"/>
    <entry id="844" name="getUgcAccept_S2C_Msg" serverName="gamesvr"/>
    <entry id="845" name="GetUgcCoCreateInviteCode_C2S_Msg" serverName="gamesvr"/>
    <entry id="846" name="GetUgcCoCreateInviteCode_S2C_Msg" serverName="gamesvr"/>
    <entry id="847" name="UgcCoCreateEditor_C2S_Msg" serverName="gamesvr"/>
    <entry id="848" name="UgcCoCreateEditor_S2C_Msg" serverName="gamesvr"/>
    <entry id="849" name="UgcCoCreateExitEditor_C2S_Msg" serverName="gamesvr"/>
    <entry id="850" name="UgcCoCreateExitEditor_S2C_Msg" serverName="gamesvr"/>
    <entry id="851" name="LobbyExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="852" name="LobbyExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="853" name="QAInvestStatusNtf" serverName="gamesvr"/>
    <entry id="854" name="QAInvestConfigListNtf" serverName="gamesvr"/>
    <entry id="855" name="UgcModifyName_C2S_Msg" serverName="gamesvr"/>
    <entry id="856" name="UgcModifyName_S2C_Msg" serverName="gamesvr"/>
    <entry id="857" name="UploadGuideStep_C2S_Msg" serverName="gamesvr"/>
    <entry id="858" name="UploadGuideStep_S2C_Msg" serverName="gamesvr"/>
    <entry id="859" name="GetUgcAccept_S2C_Msg" serverName="gamesvr"/>
    <entry id="860" name="GetUgcAccept_C2S_Msg" serverName="gamesvr"/>
    <entry id="861" name="SensitiveFilter_C2S_Msg" serverName="gamesvr"/>
    <entry id="862" name="SensitiveFilter_S2C_Msg" serverName="gamesvr"/>
    <entry id="863" name="PermitUnlockLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="864" name="GetRecommendFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="865" name="GetRecommendFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="866" name="UgcGetUgcPlayerInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="867" name="UgcGetUgcPlayerInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="868" name="UgcGetMaps_C2S_Msg" serverName="gamesvr"/>
    <entry id="869" name="UgcGetMaps_S2C_Msg" serverName="gamesvr"/>
    <entry id="870" name="PlayerBanInfoNtf" serverName="gamesvr"/>
    <entry id="871" name="FollowPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="872" name="FollowPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="873" name="CancelFollowPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="874" name="CancelFollowPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="875" name="ResourceChangeNtf" serverName="gamesvr"/>
    <entry id="876" name="UgcRoomPositionUpdate_C2S_Msg" serverName="gamesvr"/>
    <entry id="877" name="UgcRoomPositionUpdate_S2C_Msg" serverName="gamesvr"/>
    <entry id="878" name="UgcSingleStageStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="879" name="UgcSingleStageStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="880" name="UgcDailyStageChangeMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="881" name="UgcDailyStageChangeMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="882" name="UgcDailyStageReset_C2S_Msg" serverName="gamesvr"/>
    <entry id="883" name="UgcDailyStageReset_S2C_Msg" serverName="gamesvr"/>
    <entry id="884" name="GetUgcDailyStageInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="885" name="GetUgcDailyStageInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="886" name="UgcDelLifeItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="887" name="UgcDelLifeItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="888" name="UgcRoomPreStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="889" name="UgcRoomPreStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="890" name="SelectStarterItems_C2S_Msg" serverName="gamesvr"/>
    <entry id="891" name="SelectStarterItems_S2C_Msg" serverName="gamesvr"/>
    <entry id="892" name="UgcDeleteGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="893" name="UgcDeleteGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="894" name="UgcPublishGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="895" name="UgcPublishGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="896" name="UgcScreenForObject_C2S_Msg" serverName="gamesvr"/>
    <entry id="897" name="UgcScreenForObject_S2C_Msg" serverName="gamesvr"/>
    <entry id="898" name="UgcTakeOffForObject_C2S_Msg" serverName="gamesvr"/>
    <entry id="899" name="UgcTakeOffForObject_S2C_Msg" serverName="gamesvr"/>
    <entry id="900" name="UgcPublishDetailsForObject_C2S_Msg" serverName="gamesvr"/>
    <entry id="901" name="UgcPublishDetailsForObject_S2C_Msg" serverName="gamesvr"/>
    <entry id="902" name="CheckPlayerNickName_C2S_Msg" serverName="gamesvr"/>
    <entry id="903" name="CheckPlayerNickName_S2C_Msg" serverName="gamesvr"/>
    <entry id="904" name="UgcQuickJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="905" name="UgcQuickJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="906" name="PlayerProfileChangeNtf" serverName="gamesvr"/>
    <entry id="907" name="UgcCollectForObject_C2S_Msg" serverName="gamesvr"/>
    <entry id="908" name="UgcCollectForObject_S2C_Msg" serverName="gamesvr"/>
    <entry id="909" name="UgcCheckWhitelistForObject_C2S_Msg" serverName="gamesvr"/>
    <entry id="910" name="UgcCheckWhitelistForObject_S2C_Msg" serverName="gamesvr"/>
    <entry id="911" name="CreateRole_C2S_Msg" serverName="gamesvr"/>
    <entry id="912" name="CreateRole_S2C_Msg" serverName="gamesvr"/>
    <entry id="913" name="LobbyInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="914" name="LobbyInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="915" name="LobbyReplyInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="916" name="LobbyReplyInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="917" name="LobbyInviteNtf" serverName="gamesvr"/>
    <entry id="918" name="LobbyInviteReplyNtf" serverName="gamesvr"/>
    <entry id="919" name="UgcCoverCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="920" name="UgcCoverCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="921" name="UgcMapInfoNtf" serverName="gamesvr"/>
    <entry id="922" name="LetsGoClientUploadSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="923" name="LetsGoClientUploadSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="924" name="PlayerPray_C2S_Msg" serverName="gamesvr"/>
    <entry id="925" name="PlayerPray_S2C_Msg" serverName="gamesvr"/>
    <entry id="926" name="ChatGroupKeyGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="927" name="ChatGroupKeyGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="928" name="GetForwardMessages_C2S_Msg" serverName="gamesvr"/>
    <entry id="929" name="GetForwardMessages_S2C_Msg" serverName="gamesvr"/>
    <entry id="930" name="ChangeRelation_C2S_Msg" serverName="gamesvr"/>
    <entry id="931" name="ChangeRelation_S2C_Msg" serverName="gamesvr"/>
    <entry id="932" name="RewardSnsShare_C2S_Msg" serverName="gamesvr"/>
    <entry id="933" name="RewardSnsShare_S2C_Msg" serverName="gamesvr"/>
    <entry id="934" name="UgcGroupOfficalList_C2S_Msg" serverName="gamesvr"/>
    <entry id="935" name="UgcGroupOfficalList_S2C_Msg" serverName="gamesvr"/>
    <entry id="936" name="UgcGroupSearch_C2S_Msg" serverName="gamesvr"/>
    <entry id="937" name="UgcGroupSearch_S2C_Msg" serverName="gamesvr"/>
    <entry id="938" name="UgcStageChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="939" name="UgcStageChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="940" name="NetworkInfoUpload_C2S_Msg" serverName="gamesvr"/>
    <entry id="941" name="NetworkInfoUpload_S2C_Msg" serverName="gamesvr"/>
    <entry id="942" name="ChangeGender_C2S_Msg" serverName="gamesvr"/>
    <entry id="943" name="ChangeGender_S2C_Msg" serverName="gamesvr"/>
    <entry id="944" name="GetUnlockedSlotId_C2S_Msg" serverName="gamesvr"/>
    <entry id="945" name="GetUnlockedSlotId_S2C_Msg" serverName="gamesvr"/>
    <entry id="946" name="AccountUnBind_C2S_Msg" serverName="gamesvr"/>
    <entry id="947" name="AccountUnBind_S2C_Msg" serverName="gamesvr"/>
    <entry id="948" name="GetAccountBindInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="949" name="GetAccountBindInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="950" name="RoomRecommendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="951" name="RoomRecommendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="952" name="RoomQuickJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="953" name="RoomQuickJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="954" name="RoomInfoQueryByRoomNo_C2S_Msg" serverName="gamesvr"/>
    <entry id="955" name="RoomInfoQueryByRoomNo_S2C_Msg" serverName="gamesvr"/>
    <entry id="956" name="RoomJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="957" name="RoomPositionExchange_C2S_Msg" serverName="gamesvr"/>
    <entry id="958" name="RoomPositionExchange_S2C_Msg" serverName="gamesvr"/>
    <entry id="959" name="RoomPositionExchangeNtf" serverName="gamesvr"/>
    <entry id="960" name="RoomPositionExchangeReply_C2S_Msg" serverName="gamesvr"/>
    <entry id="961" name="ABTestInfoQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="962" name="ABTestInfoQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="963" name="GetItemPackagePickPickedNum_C2S_Msg" serverName="gamesvr"/>
    <entry id="964" name="GetItemPackagePickPickedNum_S2C_Msg" serverName="gamesvr"/>
    <entry id="965" name="IDCPingSvrListNtf" serverName="gamesvr"/>
    <entry id="966" name="RelationChangeNtf" serverName="gamesvr"/>
    <entry id="967" name="RoomInfoChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="968" name="RoomInfoChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="969" name="RoomReady_C2S_Msg" serverName="gamesvr"/>
    <entry id="970" name="RoomReady_S2C_Msg" serverName="gamesvr"/>
    <entry id="971" name="RoomStateCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="972" name="RoomStateCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="973" name="RelationIntimacyNtf" serverName="gamesvr"/>
    <entry id="974" name="UgcDownload_C2S_Msg" serverName="gamesvr"/>
    <entry id="975" name="UgcDownload_S2C_Msg" serverName="gamesvr"/>
    <entry id="976" name="RoomMapDownloadStat_C2S_Msg" serverName="gamesvr"/>
    <entry id="977" name="RoomMapDownloadStat_S2C_Msg" serverName="gamesvr"/>
    <entry id="978" name="RoomMapDownloadStatNtf" serverName="gamesvr"/>
    <entry id="979" name="RoomPositionExchangeReply_S2C_Msg" serverName="gamesvr"/>
    <entry id="980" name="GetAccountState_C2S_Msg" serverName="gamesvr"/>
    <entry id="981" name="GetAccountState_S2C_Msg" serverName="gamesvr"/>
    <entry id="982" name="RoomAddRobot_C2S_Msg" serverName="gamesvr"/>
    <entry id="983" name="RoomAddRobot_S2C_Msg" serverName="gamesvr"/>
    <entry id="984" name="RoomRemoveRobot_C2S_Msg" serverName="gamesvr"/>
    <entry id="985" name="RoomRemoveRobot_S2C_Msg" serverName="gamesvr"/>
    <entry id="986" name="RoomJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="987" name="CheckRandomNicknames_C2S_Msg" serverName="gamesvr"/>
    <entry id="988" name="CheckRandomNicknames_S2C_Msg" serverName="gamesvr"/>
    <entry id="989" name="ListFeatureUnlock_C2S_Msg" serverName="gamesvr"/>
    <entry id="990" name="ListFeatureUnlock_S2C_Msg" serverName="gamesvr"/>
    <entry id="991" name="FeatureUnlockNtf" serverName="gamesvr"/>
    <entry id="992" name="GetRandomNickname_C2S_Msg" serverName="gamesvr"/>
    <entry id="993" name="GetRandomNickname_S2C_Msg" serverName="gamesvr"/>
    <entry id="994" name="UgcGenAiImage_C2S_Msg" serverName="gamesvr"/>
    <entry id="995" name="UgcGenAiImage_S2C_Msg" serverName="gamesvr"/>
    <entry id="996" name="UgcAiImageGenSucNtf" serverName="gamesvr"/>
    <entry id="997" name="UgcSaveResultNtf" serverName="gamesvr"/>
    <entry id="998" name="UgcCoverCheckResultNtf" serverName="gamesvr"/>
    <entry id="999" name="UgcAiChangeColor_C2S_Msg" serverName="gamesvr"/>
    <entry id="1000" name="UgcAiChangeColor_S2C_Msg" serverName="gamesvr"/>
    <entry id="1001" name="UgcAiChangeColorResultNtf" serverName="gamesvr"/>
    <entry id="1002" name="UgcDownLoadPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="1003" name="UgcDownLoadPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="1004" name="UgcModifyPublishMeta_C2S_Msg" serverName="gamesvr"/>
    <entry id="1005" name="UgcModifyPublishMeta_S2C_Msg" serverName="gamesvr"/>
    <entry id="1006" name="UgcUpdatePublishMeta_C2S_Msg" serverName="gamesvr"/>
    <entry id="1007" name="UgcUpdatePublishMeta_S2C_Msg" serverName="gamesvr"/>
    <entry id="1008" name="UgcPublishMetaRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="1009" name="UgcPublishMetaRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="1010" name="UgcRoomChangeMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1011" name="UgcRoomChangeMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1012" name="BulletinPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="1013" name="BulletinPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="1014" name="BulletinOpen_C2S_Msg" serverName="gamesvr"/>
    <entry id="1015" name="BulletinOpen_S2C_Msg" serverName="gamesvr"/>
    <entry id="1016" name="GetBulletinList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1017" name="GetBulletinList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1018" name="PartyInfoNtf" serverName="gamesvr"/>
    <entry id="1019" name="XiaoWoWatering_C2S_Msg" serverName="gamesvr"/>
    <entry id="1020" name="XiaoWoWatering_S2C_Msg" serverName="gamesvr"/>
    <entry id="1021" name="XiaoWoShake_C2S_Msg" serverName="gamesvr"/>
    <entry id="1022" name="XiaoWoShake_S2C_Msg" serverName="gamesvr"/>
    <entry id="1023" name="XiaoWoEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="1024" name="XiaoWoEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="1025" name="XiaoWoExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="1026" name="XiaoWoExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="1027" name="XiaoWoLike_C2S_Msg" serverName="gamesvr"/>
    <entry id="1028" name="XiaoWoLike_S2C_Msg" serverName="gamesvr"/>
    <entry id="1029" name="XiaoWoStar_C2S_Msg" serverName="gamesvr"/>
    <entry id="1030" name="XiaoWoStar_S2C_Msg" serverName="gamesvr"/>
    <entry id="1031" name="XiaoWoLevelUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="1032" name="XiaoWoLevelUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="1033" name="XiaoWoInfoNtf" serverName="gamesvr"/>
    <entry id="1034" name="XiaoWoItemInteract_C2S_Msg" serverName="gamesvr"/>
    <entry id="1035" name="XiaoWoItemInteract_S2C_Msg" serverName="gamesvr"/>
    <entry id="1036" name="BagCommonGetItemsNtfCommit_C2S_Msg" serverName="gamesvr"/>
    <entry id="1037" name="BagCommonGetItemsNtfCommit_S2C_Msg" serverName="gamesvr"/>
    <entry id="1038" name="GetUgcStarWorldInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1039" name="GetUgcStarWorldInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1040" name="AvailablePlatNicknameNtf" serverName="gamesvr"/>
    <entry id="1041" name="PlayerBlessBagInfoNtf" serverName="gamesvr"/>
    <entry id="1042" name="PlatPrivilegesNtf" serverName="gamesvr"/>
    <entry id="1043" name="InterServerPuzzleGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1044" name="InterServerPuzzleGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1045" name="InterServerPuzzleBuy_C2S_Msg" serverName="gamesvr"/>
    <entry id="1046" name="InterServerPuzzleBuy_S2C_Msg" serverName="gamesvr"/>
    <entry id="1047" name="InterServerPuzzleUnlockNtf" serverName="gamesvr"/>
    <entry id="1048" name="InterServerPuzzleGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1049" name="InterServerPuzzleGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1050" name="InterServerGiftGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1051" name="InterServerGiftGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1052" name="InterServerGiftBuy_C2S_Msg" serverName="gamesvr"/>
    <entry id="1053" name="InterServerGiftBuy_S2C_Msg" serverName="gamesvr"/>
    <entry id="1054" name="InterServerGiftUnlockNtf" serverName="gamesvr"/>
    <entry id="1055" name="InterServerGiftGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1056" name="InterServerGiftGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1057" name="UgcGetCreatorInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1058" name="UgcGetCreatorInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1059" name="GetRaffleAward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1060" name="GetRaffleAward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1061" name="RefreshPlayerPayInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1062" name="RefreshPlayerPayInfoNtf" serverName="gamesvr"/>
    <entry id="1063" name="RoomMapDownloadReminder_C2S_Msg" serverName="gamesvr"/>
    <entry id="1064" name="RoomMapDownloadReminder_S2C_Msg" serverName="gamesvr"/>
    <entry id="1065" name="RoomMapDownloadReminderNtf" serverName="gamesvr"/>
    <entry id="1066" name="UgcRecommendSubscribe_C2S_Msg" serverName="gamesvr"/>
    <entry id="1067" name="UgcRecommendSubscribe_S2C_Msg" serverName="gamesvr"/>
    <entry id="1068" name="UgcRoomRecommendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1069" name="UgcRoomRecommendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1070" name="XiaoWoItemLock_C2S_Msg" serverName="gamesvr"/>
    <entry id="1071" name="XiaoWoItemLock_S2C_Msg" serverName="gamesvr"/>
    <entry id="1072" name="XiaoWoDSInfoNtf" serverName="gamesvr"/>
    <entry id="1073" name="GetChatGroupMemberList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1074" name="GetChatGroupMemberList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1075" name="UpdateMailState_C2S_Msg" serverName="gamesvr"/>
    <entry id="1076" name="UpdateMailState_S2C_Msg" serverName="gamesvr"/>
    <entry id="1077" name="GetPingSvrInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1078" name="GetPingSvrInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1079" name="GetTranslateInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1080" name="GetTranslateInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1081" name="WithdrawParentAuth_C2S_Msg" serverName="gamesvr"/>
    <entry id="1082" name="WithdrawParentAuth_S2C_Msg" serverName="gamesvr"/>
    <entry id="1083" name="WithdrawParentAuthNtf" serverName="gamesvr"/>
    <entry id="1084" name="QueryParentAuthStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="1085" name="QueryParentAuthStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="1086" name="QueryParentAuthStatusNtf" serverName="gamesvr"/>
    <entry id="1087" name="RefreshPlayerPayInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1088" name="XiaoWoPlatDataNtf" serverName="gamesvr"/>
    <entry id="1089" name="XiaoWoHotReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="1090" name="XiaoWoHotReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="1091" name="XiaoWoPickupDrop_C2S_Msg" serverName="gamesvr"/>
    <entry id="1092" name="XiaoWoPickupDrop_S2C_Msg" serverName="gamesvr"/>
    <entry id="1093" name="XiaoWoBuyFurniture_C2S_Msg" serverName="gamesvr"/>
    <entry id="1094" name="XiaoWoBuyFurniture_S2C_Msg" serverName="gamesvr"/>
    <entry id="1095" name="XiaoWoExchangeCoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="1096" name="XiaoWoExchangeCoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="1097" name="XiaoWoKickNtf" serverName="gamesvr"/>
    <entry id="1098" name="XiaoWoSetClientKV_C2S_Msg" serverName="gamesvr"/>
    <entry id="1099" name="XiaoWoSetClientKV_S2C_Msg" serverName="gamesvr"/>
    <entry id="1100" name="SingleBattleKeepAlive_C2S_Msg" serverName="gamesvr"/>
    <entry id="1101" name="SingleBattleKeepAlive_S2C_Msg" serverName="gamesvr"/>
    <entry id="1102" name="PilotInfoNtf" serverName="gamesvr"/>
    <entry id="1103" name="SceneGiftPackagePushNtf" serverName="gamesvr"/>
    <entry id="1104" name="DigTreasureOfMultiPlayerSquad_C2S_Msg" serverName="gamesvr"/>
    <entry id="1105" name="DigTreasureOfMultiPlayerSquad_S2C_Msg" serverName="gamesvr"/>
    <entry id="1106" name="SquadMultiPlayerGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1107" name="SquadMultiPlayerGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1108" name="SquadMultiPlayerJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="1109" name="SquadMultiPlayerJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="1110" name="UgcCoCreateExitEditorNtf" serverName="gamesvr"/>
    <entry id="1111" name="ApplyUgcLayerId_C2S_Msg" serverName="gamesvr"/>
    <entry id="1112" name="ApplyUgcLayerId_S2C_Msg" serverName="gamesvr"/>
    <entry id="1113" name="AccreditLayerByCreatorId_C2S_Msg" serverName="gamesvr"/>
    <entry id="1114" name="AccreditLayerByCreatorId_S2C_Msg" serverName="gamesvr"/>
    <entry id="1115" name="UgcCoCreateEditorHeart_C2S_Msg" serverName="gamesvr"/>
    <entry id="1116" name="UgcCoCreateEditorHeart_S2C_Msg" serverName="gamesvr"/>
    <entry id="1117" name="UgcInviteCoCreateMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1118" name="UgcInviteCoCreateMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1119" name="UgcCoCreateInviteNtf" serverName="gamesvr"/>
    <entry id="1120" name="UgcCoCreateInviteReply_C2S_Msg" serverName="gamesvr"/>
    <entry id="1121" name="UgcCoCreateInviteReply_S2C_Msg" serverName="gamesvr"/>
    <entry id="1122" name="UgcRemoveCoCreator_C2S_Msg" serverName="gamesvr"/>
    <entry id="1123" name="UgcRemoveCoCreator_S2C_Msg" serverName="gamesvr"/>
    <entry id="1124" name="UgcQuitCoCreateMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1125" name="UgcQuitCoCreateMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1126" name="UgcCoCreatorModifyNtf" serverName="gamesvr"/>
    <entry id="1127" name="UgcGroupBatchGetPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="1128" name="UgcGroupBatchGetPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="1129" name="MallGetShopCommodityIds_C2S_Msg" serverName="gamesvr"/>
    <entry id="1130" name="MallGetShopCommodityIds_S2C_Msg" serverName="gamesvr"/>
    <entry id="1131" name="RoomJoinFromShare_C2S_Msg" serverName="gamesvr"/>
    <entry id="1132" name="RoomJoinFromShare_S2C_Msg" serverName="gamesvr"/>
    <entry id="1133" name="FireworksActivityStartNtf" serverName="gamesvr"/>
    <entry id="1134" name="FireworksActivityEndNtf" serverName="gamesvr"/>
    <entry id="1135" name="SetFireworksText_C2S_Msg" serverName="gamesvr"/>
    <entry id="1136" name="SetFireworksText_S2C_Msg" serverName="gamesvr"/>
    <entry id="1137" name="UseFireworksItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="1138" name="UseFireworksItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="1139" name="UseFireworksNtf" serverName="gamesvr"/>
    <entry id="1140" name="TakeawayGetRewardBox_C2S_Msg" serverName="gamesvr"/>
    <entry id="1141" name="TakeawayGetRewardBox_S2C_Msg" serverName="gamesvr"/>
    <entry id="1142" name="TakeawayStartUnlockBox_C2S_Msg" serverName="gamesvr"/>
    <entry id="1143" name="TakeawayStartUnlockBox_S2C_Msg" serverName="gamesvr"/>
    <entry id="1144" name="TakeawayGetSharingReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1145" name="TakeawayGetSharingReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1146" name="BagUseInteractItemMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="1147" name="BagUseInteractItemMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="1148" name="UploadAsaIadInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1149" name="UploadAsaIadInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1150" name="RaffleFreeDrawAcquiredNtf" serverName="gamesvr"/>
    <entry id="1151" name="RaffleFreeDrawDeliveredNtf" serverName="gamesvr"/>
    <entry id="1152" name="FetchAroundRank_C2S_Msg" serverName="gamesvr"/>
    <entry id="1153" name="FetchAroundRank_S2C_Msg" serverName="gamesvr"/>
    <entry id="1154" name="ReportShare_C2S_Msg" serverName="gamesvr"/>
    <entry id="1155" name="ReportShare_S2C_Msg" serverName="gamesvr"/>
    <entry id="1156" name="UgcStarWorldChangeMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1157" name="UgcStarWorldChangeMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1158" name="UgcGetAllPresetTopics_C2S_Msg" serverName="gamesvr"/>
    <entry id="1159" name="UgcGetAllPresetTopics_S2C_Msg" serverName="gamesvr"/>
    <entry id="1160" name="UgcHomePageRecommend_C2S_Msg" serverName="gamesvr"/>
    <entry id="1161" name="UgcHomePageRecommend_S2C_Msg" serverName="gamesvr"/>
    <entry id="1162" name="UgcGetUserFeedbackData_C2S_Msg" serverName="gamesvr"/>
    <entry id="1163" name="UgcGetUserFeedbackData_S2C_Msg" serverName="gamesvr"/>
    <entry id="1164" name="UgcRedPointNtf" serverName="gamesvr"/>
    <entry id="1165" name="UgcLvChangeNtf" serverName="gamesvr"/>
    <entry id="1166" name="UgcCoCreateDing_C2S_Msg" serverName="gamesvr"/>
    <entry id="1167" name="UgcCoCreateDing_S2C_Msg" serverName="gamesvr"/>
    <entry id="1168" name="UgcCoCreatorDingNtf" serverName="gamesvr"/>
    <entry id="1169" name="UgcHomePageThemeAllMaps_C2S_Msg" serverName="gamesvr"/>
    <entry id="1170" name="UgcHomePageThemeAllMaps_S2C_Msg" serverName="gamesvr"/>
    <entry id="1171" name="UgcCoCreateWhite_C2S_Msg" serverName="gamesvr"/>
    <entry id="1172" name="UgcCoCreateWhite_S2C_Msg" serverName="gamesvr"/>
    <entry id="1173" name="UgcLayerIsApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="1174" name="UgcLayerIsApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="1175" name="AchievementGetList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1176" name="AchievementGetList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1177" name="AchievementReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1178" name="AchievementReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1179" name="AchievementRedDotNtf" serverName="gamesvr"/>
    <entry id="1180" name="AchievementStatusNtf" serverName="gamesvr"/>
    <entry id="1181" name="GetFireworksInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1182" name="GetFireworksInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1183" name="RedEnvelopRainTake_C2S_Msg" serverName="gamesvr"/>
    <entry id="1184" name="SceneGetPlayerSceneId_C2S_Msg" serverName="gamesvr"/>
    <entry id="1185" name="SceneGetPlayerSceneId_S2C_Msg" serverName="gamesvr"/>
    <entry id="1186" name="RedEnvelopRainOpen_S2C_Msg" serverName="gamesvr"/>
    <entry id="1187" name="RedEnvelopRainOpenResultNtf" serverName="gamesvr"/>
    <entry id="1188" name="RedEnvelopRainQueryReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1189" name="RedEnvelopRainQueryReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1190" name="ClientResPatchNtf" serverName="gamesvr"/>
    <entry id="1191" name="ResVersionSync_C2S_Msg" serverName="gamesvr"/>
    <entry id="1192" name="ResVersionSync_S2C_Msg" serverName="gamesvr"/>
    <entry id="1193" name="GetAttachmentsAndDelete_C2S_Msg" serverName="gamesvr"/>
    <entry id="1194" name="GetAttachmentsAndDelete_S2C_Msg" serverName="gamesvr"/>
    <entry id="1195" name="MallBatchGiveMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="1196" name="MallBatchGiveMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="1197" name="MallBatchDemandMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="1198" name="MallBatchDemandMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="1199" name="MallGiveRecordList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1200" name="MallGiveRecordList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1201" name="SnsInvitationCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="1202" name="SnsInvitationCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="1203" name="SnsInvitationAccept_C2S_Msg" serverName="gamesvr"/>
    <entry id="1204" name="SnsInvitationAccept_S2C_Msg" serverName="gamesvr"/>
    <entry id="1205" name="SnsInvitationRefreshCode_C2S_Msg" serverName="gamesvr"/>
    <entry id="1206" name="SnsInvitationRefreshCode_S2C_Msg" serverName="gamesvr"/>
    <entry id="1207" name="ReportCompleteTask_C2S_Msg" serverName="gamesvr"/>
    <entry id="1208" name="ReportCompleteTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="1209" name="TlogDataReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="1210" name="TlogDataReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="1211" name="UgcGetHotPlaying_C2S_Msg" serverName="gamesvr"/>
    <entry id="1212" name="UgcGetHotPlaying_S2C_Msg" serverName="gamesvr"/>
    <entry id="1213" name="XiaoWoForwardToDS_C2S_Msg" serverName="gamesvr"/>
    <entry id="1214" name="XiaoWoForwardToDS_S2C_Msg" serverName="gamesvr"/>
    <entry id="1215" name="XiaowoSetItemDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="1216" name="XiaowoSetItemDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="1217" name="XiaoWoSetInstructionAndImage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1218" name="XiaoWoSetInstructionAndImage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1219" name="XiaoWoAttrNtf" serverName="gamesvr"/>
    <entry id="1220" name="SceneInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="1221" name="SceneInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="1222" name="SceneReplyInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="1223" name="SceneReplyInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="1224" name="SceneInviteNtf" serverName="gamesvr"/>
    <entry id="1225" name="SceneInviteReplyNtf" serverName="gamesvr"/>
    <entry id="1226" name="EnterPlayerXiaoWo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1227" name="EnterPlayerXiaoWo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1228" name="RedEnvelopRainTake_S2C_Msg" serverName="gamesvr"/>
    <entry id="1229" name="RedEnvelopRainOpen_C2S_Msg" serverName="gamesvr"/>
    <entry id="1230" name="XiaowoGetEditMetaInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1231" name="XiaowoGetEditMetaInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1232" name="ChatSetQQSyncStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="1233" name="ChatSetQQSyncStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="1234" name="ChatGetQQSyncStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="1235" name="ChatGetQQSyncStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="1236" name="SearchPlayerUid_C2S_Msg" serverName="gamesvr"/>
    <entry id="1237" name="SearchPlayerUid_S2C_Msg" serverName="gamesvr"/>
    <entry id="1238" name="ModifyPlayerLobbyNpcInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1239" name="ModifyPlayerLobbyNpcInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1240" name="GameliveTaskRefresh_C2S_Msg" serverName="gamesvr"/>
    <entry id="1241" name="GameliveTaskRefresh_S2C_Msg" serverName="gamesvr"/>
    <entry id="1242" name="UpdatePlayerLocation_C2S_Msg" serverName="gamesvr"/>
    <entry id="1243" name="UpdatePlayerLocation_S2C_Msg" serverName="gamesvr"/>
    <entry id="1244" name="FriendIntimacyChangeNtf" serverName="gamesvr"/>
    <entry id="1245" name="TeamJoinRoomConfirmNtf" serverName="gamesvr"/>
    <entry id="1246" name="RoomTeamConfirmJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="1247" name="RoomTeamConfirmJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="1248" name="RoomPlayerReadyReminder_C2S_Msg" serverName="gamesvr"/>
    <entry id="1249" name="RoomPlayerReadyReminder_S2C_Msg" serverName="gamesvr"/>
    <entry id="1250" name="RoomPlayerReadyReminderNtf" serverName="gamesvr"/>
    <entry id="1251" name="CompetitionCommon_C2S_Msg" serverName="gamesvr"/>
    <entry id="1252" name="CompetitionCommon_S2C_Msg" serverName="gamesvr"/>
    <entry id="1253" name="UgcSvrTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="1254" name="UgcSvrTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="1255" name="TeamJoinRoomCancelNtf" serverName="gamesvr"/>
    <entry id="1256" name="GetLobbyInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1257" name="GetLobbyInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1258" name="GetPlayCalendarConfig_C2S_Msg" serverName="gamesvr"/>
    <entry id="1259" name="GetPlayCalendarConfig_S2C_Msg" serverName="gamesvr"/>
    <entry id="1260" name="QueryTranslationData_C2S_Msg" serverName="gamesvr"/>
    <entry id="1261" name="QueryTranslationData_S2C_Msg" serverName="gamesvr"/>
    <entry id="1262" name="NtfTranslationData_S2C_Msg" serverName="gamesvr"/>
    <entry id="1263" name="GetPlayerCalendarConfig_C2S_Msg" serverName="gamesvr"/>
    <entry id="1264" name="GetPlayerCalendarConfig_S2C_Msg" serverName="gamesvr"/>
    <entry id="1265" name="SquadRefreshNtf" serverName="gamesvr"/>
    <entry id="1266" name="QQGroupStartTeamTask_C2S_Msg" serverName="gamesvr"/>
    <entry id="1267" name="QQGroupStartTeamTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="1268" name="UgcGetPublishTags_C2S_Msg" serverName="gamesvr"/>
    <entry id="1269" name="UgcGetPublishTags_S2C_Msg" serverName="gamesvr"/>
    <entry id="1270" name="LobbyEnvelopSettingDataNtf" serverName="gamesvr"/>
    <entry id="1271" name="LetsGoRecommendMatchType_C2S_Msg" serverName="gamesvr"/>
    <entry id="1272" name="LetsGoRecommendMatchType_S2C_Msg" serverName="gamesvr"/>
    <entry id="1273" name="BagItemRemoveNtf" serverName="gamesvr"/>
    <entry id="1274" name="MarqueeNoticeNtf" serverName="gamesvr"/>
    <entry id="1275" name="ClientClickPanel_C2S_Msg" serverName="gamesvr"/>
    <entry id="1276" name="ClientClickPanel_S2C_Msg" serverName="gamesvr"/>
    <entry id="1277" name="UgcSingleLevelGetDressInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1278" name="UgcSingleLevelGetDressInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1279" name="XiaowoTreeInfoNtf" serverName="gamesvr"/>
    <entry id="1280" name="CompetitionCommonNtf" serverName="gamesvr"/>
    <entry id="1281" name="PlatPrivilegesProcess_C2S_Msg" serverName="gamesvr"/>
    <entry id="1282" name="PlatPrivilegesProcess_S2C_Msg" serverName="gamesvr"/>
    <entry id="1283" name="BattleDsJumpNtf" serverName="gamesvr"/>
    <entry id="1284" name="DsJumpNtf" serverName="gamesvr"/>
    <entry id="1285" name="XiaoWoSampleRoomAlloc_C2S_Msg" serverName="gamesvr"/>
    <entry id="1286" name="XiaoWoSampleRoomAlloc_S2C_Msg" serverName="gamesvr"/>
    <entry id="1287" name="XiaoWoSaveLayout_C2S_Msg" serverName="gamesvr"/>
    <entry id="1288" name="XiaoWoSaveLayout_S2C_Msg" serverName="gamesvr"/>
    <entry id="1289" name="XiaoWoGetLayoutList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1290" name="XiaoWoGetLayoutList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1291" name="XiaoWoInitFurnitureGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="1292" name="XiaoWoInitFurnitureGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="1293" name="LobbyInfoNtf" serverName="gamesvr"/>
    <entry id="1294" name="SuperLinearDraw_C2S_Msg" serverName="gamesvr"/>
    <entry id="1295" name="SuperLinearDraw_S2C_Msg" serverName="gamesvr"/>
    <entry id="1296" name="SuperLinearGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1297" name="SuperLinearGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1298" name="GetCommonGuideStep_C2S_Msg" serverName="gamesvr"/>
    <entry id="1299" name="FinishCommonGuideStep_C2S_Msg" serverName="gamesvr"/>
    <entry id="1300" name="FinishCommonGuideStep_S2C_Msg" serverName="gamesvr"/>
    <entry id="1301" name="PlayerPlayNewYearCall_C2S_Msg" serverName="gamesvr"/>
    <entry id="1302" name="PlayerPlayNewYearCall_S2C_Msg" serverName="gamesvr"/>
    <entry id="1303" name="UgcGetCollectStarActivityMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1304" name="UgcGetCollectStarActivityMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1305" name="UgcGenAiVoice_C2S_Msg" serverName="gamesvr"/>
    <entry id="1306" name="UgcGenAiVoice_S2C_Msg" serverName="gamesvr"/>
    <entry id="1307" name="UgcAiVoiceGenSucNtf" serverName="gamesvr"/>
    <entry id="1308" name="UgcGenAiAnicap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1309" name="UgcGenAiAnicap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1310" name="UgcAiAnicapGenSucNtf" serverName="gamesvr"/>
    <entry id="1311" name="UgcGenAiAnswer_C2S_Msg" serverName="gamesvr"/>
    <entry id="1312" name="UgcGenAiAnswer_S2C_Msg" serverName="gamesvr"/>
    <entry id="1313" name="UgcAiAnswerGenSucNtf" serverName="gamesvr"/>
    <entry id="1314" name="RedEnvelopeRainSetRedPoint_C2S_Msg" serverName="gamesvr"/>
    <entry id="1315" name="RedEnvelopeRainSetRedPoint_S2C_Msg" serverName="gamesvr"/>
    <entry id="1316" name="LuckyBalloonShoot_C2S_Msg" serverName="gamesvr"/>
    <entry id="1317" name="LuckyBalloonShoot_S2C_Msg" serverName="gamesvr"/>
    <entry id="1318" name="UgcMapSearchFront_C2S_Msg" serverName="gamesvr"/>
    <entry id="1319" name="UgcMapSearchFront_S2C_Msg" serverName="gamesvr"/>
    <entry id="1320" name="UgcSearchTopicDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="1321" name="UgcSearchTopicDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="1322" name="FpsSetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="1323" name="FpsSetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="1324" name="FpsGetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="1325" name="FpsGetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="1326" name="GetTimeLimitedCheckInReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1327" name="GetTimeLimitedCheckInReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1328" name="ListScratchOffTickets_C2S_Msg" serverName="gamesvr"/>
    <entry id="1329" name="ListScratchOffTickets_S2C_Msg" serverName="gamesvr"/>
    <entry id="1330" name="BeginScratchOffTickets_C2S_Msg" serverName="gamesvr"/>
    <entry id="1331" name="BeginScratchOffTickets_S2C_Msg" serverName="gamesvr"/>
    <entry id="1332" name="GetScratchOffTicketsReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1333" name="GetScratchOffTicketsReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1334" name="UpgradeHigherScratchOffTickets_C2S_Msg" serverName="gamesvr"/>
    <entry id="1335" name="UpgradeHigherScratchOffTickets_S2C_Msg" serverName="gamesvr"/>
    <entry id="1336" name="ClubMemberLiveInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1337" name="ClubMemberLiveInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1338" name="RedPacketOpen_C2S_Msg" serverName="gamesvr"/>
    <entry id="1339" name="RedPacketOpen_S2C_Msg" serverName="gamesvr"/>
    <entry id="1340" name="RedPacketOpenResultNtf" serverName="gamesvr"/>
    <entry id="1341" name="RedPacketQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="1342" name="RedPacketQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="1343" name="ConfirmAssistFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="1344" name="ConfirmAssistFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="1345" name="GetAccumulateBlessingsReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1346" name="GetAccumulateBlessingsReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1347" name="StarPlayerNtf" serverName="gamesvr"/>
    <entry id="1348" name="RaffleBIDiscountReceivedNtf" serverName="gamesvr"/>
    <entry id="1349" name="UltramanThemeCollectionProgress_C2S_Msg" serverName="gamesvr"/>
    <entry id="1350" name="UltramanThemeCollectionProgress_S2C_Msg" serverName="gamesvr"/>
    <entry id="1351" name="UltramanThemeReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1352" name="UltramanThemeReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1353" name="LuckyStarUnlockStar_C2S_Msg" serverName="gamesvr"/>
    <entry id="1354" name="LuckyStarUnlockStar_S2C_Msg" serverName="gamesvr"/>
    <entry id="1355" name="LuckyStarGetDetailInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1356" name="LuckyStarGetDetailInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1357" name="LuckyStarGiveStar_C2S_Msg" serverName="gamesvr"/>
    <entry id="1358" name="LuckyStarGiveStar_S2C_Msg" serverName="gamesvr"/>
    <entry id="1359" name="LuckyStarReceiveStar_C2S_Msg" serverName="gamesvr"/>
    <entry id="1360" name="LuckyStarReceiveStar_S2C_Msg" serverName="gamesvr"/>
    <entry id="1361" name="LuckyStarGenerateRequireStar_C2S_Msg" serverName="gamesvr"/>
    <entry id="1362" name="LuckyStarGenerateRequireStar_S2C_Msg" serverName="gamesvr"/>
    <entry id="1363" name="LuckyStarGenerateGiveStar_C2S_Msg" serverName="gamesvr"/>
    <entry id="1364" name="LuckyStarGenerateGiveStar_S2C_Msg" serverName="gamesvr"/>
    <entry id="1365" name="UgcPublishPreCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="1366" name="UgcPublishPreCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="1367" name="TimeLimitedCheckInActivityUnlockNtf" serverName="gamesvr"/>
    <entry id="1368" name="UgcStarWorldRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="1369" name="UgcStarWorldRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="1370" name="ClubMemberShareMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1371" name="ClubMemberShareMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1372" name="ClubAdminShareMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1373" name="ClubAdminShareMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1374" name="ClubNewApplicantNtf" serverName="gamesvr"/>
    <entry id="1375" name="XiaoWoCropWater_C2S_Msg" serverName="gamesvr"/>
    <entry id="1376" name="XiaoWoCropWater_S2C_Msg" serverName="gamesvr"/>
    <entry id="1377" name="XiaoWoCropHarvest_C2S_Msg" serverName="gamesvr"/>
    <entry id="1378" name="XiaoWoCropHarvest_S2C_Msg" serverName="gamesvr"/>
    <entry id="1379" name="LuckyStarGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1380" name="LuckyStarGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1381" name="LetsGoGetWolfKillInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1382" name="LetsGoGetWolfKillInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1383" name="LetsGoGetWolfKillReputationScore_C2S_Msg" serverName="gamesvr"/>
    <entry id="1384" name="LetsGoGetWolfKillReputationScore_S2C_Msg" serverName="gamesvr"/>
    <entry id="1385" name="InviteFriendLogin_C2S_Msg" serverName="gamesvr"/>
    <entry id="1386" name="InviteFriendLogin_S2C_Msg" serverName="gamesvr"/>
    <entry id="1387" name="UgcMatchLobbyDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="1388" name="UgcMatchLobbyDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="1389" name="UgcMatchRoomUgcIdChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="1390" name="UgcMatchRoomUgcIdChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="1391" name="UgcMatchRoomUgcIdValidCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="1392" name="UgcMatchRoomUgcIdValidCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="1393" name="UgcRoomLobbyMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1394" name="UgcRoomLobbyMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1395" name="UgcAiAnicapQueueNtf" serverName="gamesvr"/>
    <entry id="1396" name="UgcAnicapCancelQueue_C2S_Msg" serverName="gamesvr"/>
    <entry id="1397" name="UgcAnicapCancelQueue_S2C_Msg" serverName="gamesvr"/>
    <entry id="1398" name="LuckStarReceiveNtf" serverName="gamesvr"/>
    <entry id="1399" name="ClubGetShareMapList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1400" name="ClubGetShareMapList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1401" name="UgcResHomePageRecommend_C2S_Msg" serverName="gamesvr"/>
    <entry id="1402" name="UgcResHomePageRecommend_S2C_Msg" serverName="gamesvr"/>
    <entry id="1403" name="UgcResHomePageRecommedMoreSet_C2S_Msg" serverName="gamesvr"/>
    <entry id="1404" name="UgcResHomePageRecommedMoreSet_S2C_Msg" serverName="gamesvr"/>
    <entry id="1405" name="UgcResCommunitySearch_C2S_Msg" serverName="gamesvr"/>
    <entry id="1406" name="UgcResCommunitySearch_S2C_Msg" serverName="gamesvr"/>
    <entry id="1407" name="UgcResCommunityGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="1408" name="UgcResCommunityGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="1409" name="UgcResBagAdd_C2S_Msg" serverName="gamesvr"/>
    <entry id="1410" name="UgcResBagAdd_S2C_Msg" serverName="gamesvr"/>
    <entry id="1411" name="UgcResBagDelete_C2S_Msg" serverName="gamesvr"/>
    <entry id="1412" name="UgcResBagDelete_S2C_Msg" serverName="gamesvr"/>
    <entry id="1413" name="UgcResBagGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="1414" name="UgcResBagGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="1415" name="UgcResBagSearch_C2S_Msg" serverName="gamesvr"/>
    <entry id="1416" name="UgcResBagSearch_S2C_Msg" serverName="gamesvr"/>
    <entry id="1417" name="UgcResCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="1418" name="UgcResCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="1419" name="UgcResPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="1420" name="UgcResPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="1421" name="UgcResGetMyList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1422" name="UgcResGetMyList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1423" name="UgcResDelete_C2S_Msg" serverName="gamesvr"/>
    <entry id="1424" name="UgcResDelete_S2C_Msg" serverName="gamesvr"/>
    <entry id="1425" name="UgcResTakeOff_C2S_Msg" serverName="gamesvr"/>
    <entry id="1426" name="UgcResTakeOff_S2C_Msg" serverName="gamesvr"/>
    <entry id="1427" name="UgcResOperate_C2S_Msg" serverName="gamesvr"/>
    <entry id="1428" name="UgcResOperate_S2C_Msg" serverName="gamesvr"/>
    <entry id="1429" name="UgcResModifyPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="1430" name="UgcResModifyPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="1431" name="UgcResGetCollect_C2S_Msg" serverName="gamesvr"/>
    <entry id="1432" name="UgcResGetCollect_S2C_Msg" serverName="gamesvr"/>
    <entry id="1433" name="UgcResGetTopic_C2S_Msg" serverName="gamesvr"/>
    <entry id="1434" name="UgcResGetTopic_S2C_Msg" serverName="gamesvr"/>
    <entry id="1435" name="UgcResGetPublishDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="1436" name="UgcResGetPublishDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="1437" name="RedPacketSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="1438" name="RedPacketSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="1439" name="LuckyStarReceiveNtf" serverName="gamesvr"/>
    <entry id="1440" name="UgcTextLawful_C2S_Msg" serverName="gamesvr"/>
    <entry id="1441" name="UgcTextLawful_S2C_Msg" serverName="gamesvr"/>
    <entry id="1442" name="SpecialFaceReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="1443" name="SpecialFaceReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="1444" name="ReputationScoreNotEnoughNtf" serverName="gamesvr"/>
    <entry id="1445" name="GetRecentBattlePlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="1446" name="GetRecentBattlePlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="1447" name="CancelMarqueeNoticeNtf" serverName="gamesvr"/>
    <entry id="1448" name="VideoExamineFailedNtf" serverName="gamesvr"/>
    <entry id="1449" name="ClubSearch_C2S_Msg" serverName="gamesvr"/>
    <entry id="1450" name="ClubSearch_S2C_Msg" serverName="gamesvr"/>
    <entry id="1451" name="LuckyStarGetGiveInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1452" name="LuckyStarGetGiveInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1453" name="LuckyStarGetRequireResult_C2S_Msg" serverName="gamesvr"/>
    <entry id="1454" name="LuckyStarGetRequireResult_S2C_Msg" serverName="gamesvr"/>
    <entry id="1455" name="RedEnvelopeRainShared_C2S_Msg" serverName="gamesvr"/>
    <entry id="1456" name="RedEnvelopeRainShared_S2C_Msg" serverName="gamesvr"/>
    <entry id="1457" name="RedEnvelopeRainClickShareLink_C2S_Msg" serverName="gamesvr"/>
    <entry id="1458" name="RedEnvelopeRainClickShareLink_S2C_Msg" serverName="gamesvr"/>
    <entry id="1459" name="RedEnvelopeRainSharedLinkClickedNtf" serverName="gamesvr"/>
    <entry id="1460" name="LuckyStarGetRequireInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1461" name="LuckyStarGetRequireInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1462" name="UGCSearchSuggestion_C2S_Msg" serverName="gamesvr"/>
    <entry id="1463" name="UGCSearchSuggestion_S2C_Msg" serverName="gamesvr"/>
    <entry id="1464" name="RoomMapStateSync_C2S_Msg" serverName="gamesvr"/>
    <entry id="1465" name="RoomMapStateSync_S2C_Msg" serverName="gamesvr"/>
    <entry id="1466" name="RoomMapStateSyncNtf" serverName="gamesvr"/>
    <entry id="1467" name="UltramanThemeStatusNtf" serverName="gamesvr"/>
    <entry id="1468" name="UgcAiGenModule_C2S_Msg" serverName="gamesvr"/>
    <entry id="1469" name="UgcAiGenModule_S2C_Msg" serverName="gamesvr"/>
    <entry id="1470" name="UgcAiGenModuleResultNtf" serverName="gamesvr"/>
    <entry id="1471" name="ClubTextCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="1472" name="ClubTextCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="1473" name="LobbyGetPlayerRecommendLabel_C2S_Msg" serverName="gamesvr"/>
    <entry id="1474" name="LobbyGetPlayerRecommendLabel_S2C_Msg" serverName="gamesvr"/>
    <entry id="1475" name="UgcNewYearActivity_C2S_Msg" serverName="gamesvr"/>
    <entry id="1476" name="UgcNewYearActivity_S2C_Msg" serverName="gamesvr"/>
    <entry id="1477" name="UltramanThemeActivateStory_C2S_Msg" serverName="gamesvr"/>
    <entry id="1478" name="UltramanThemeActivateStory_S2C_Msg" serverName="gamesvr"/>
    <entry id="1479" name="GetActivityConfig_C2S_Msg" serverName="gamesvr"/>
    <entry id="1480" name="GetActivityConfig_S2C_Msg" serverName="gamesvr"/>
    <entry id="1481" name="XiaoWoGetSampleRoomList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1482" name="XiaoWoGetSampleRoomList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1483" name="UgcAigcGetHistory_C2S_Msg" serverName="gamesvr"/>
    <entry id="1484" name="UgcAigcGetHistory_S2C_Msg" serverName="gamesvr"/>
    <entry id="1485" name="UgcAigcUse_C2S_Msg" serverName="gamesvr"/>
    <entry id="1486" name="UgcAigcUse_S2C_Msg" serverName="gamesvr"/>
    <entry id="1487" name="UgcMulTestSaveMeta_C2S_Msg" serverName="gamesvr"/>
    <entry id="1488" name="UgcMulTestSaveMeta_S2C_Msg" serverName="gamesvr"/>
    <entry id="1489" name="ActivityInfoNtf" serverName="gamesvr"/>
    <entry id="1490" name="LBSUpdate_C2S_Msg" serverName="gamesvr"/>
    <entry id="1491" name="LBSUpdate_S2C_Msg" serverName="gamesvr"/>
    <entry id="1492" name="LBSGetNearby_C2S_Msg" serverName="gamesvr"/>
    <entry id="1493" name="LBSGetNearby_S2C_Msg" serverName="gamesvr"/>
    <entry id="1494" name="LBSGetNearbyNtf" serverName="gamesvr"/>
    <entry id="1495" name="LBSDelete_C2S_Msg" serverName="gamesvr"/>
    <entry id="1496" name="LBSDelete_S2C_Msg" serverName="gamesvr"/>
    <entry id="1497" name="LetsGoSettingsChangeNtf" serverName="gamesvr"/>
    <entry id="1498" name="UgcMapGroupList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1499" name="UgcMapGroupList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1500" name="SpringBlessingCollectionDataChangeNtf" serverName="gamesvr"/>
    <entry id="1501" name="GetSpringBlessingCollectionCardReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1502" name="GetSpringBlessingCollectionCardReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1503" name="GetSpringBlessingCollectionTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1504" name="GetSpringBlessingCollectionTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1505" name="ScratchOffTicketsUpgradeNtf" serverName="gamesvr"/>
    <entry id="1506" name="GameTvStatusChangeNtf" serverName="gamesvr"/>
    <entry id="1507" name="XiaoWoCropWaterRecordGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="1508" name="XiaoWoCropWaterRecordGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="1509" name="XiaoWoFarmingHandbookAwardGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="1510" name="XiaoWoFarmingHandbookAwardGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="1511" name="RedPacketShare_C2S_Msg" serverName="gamesvr"/>
    <entry id="1512" name="RedPacketShare_S2C_Msg" serverName="gamesvr"/>
    <entry id="1513" name="RedPacketGetByShareId_C2S_Msg" serverName="gamesvr"/>
    <entry id="1514" name="RedPacketGetByShareId_S2C_Msg" serverName="gamesvr"/>
    <entry id="1515" name="UgcBatchPublishMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1516" name="UgcBatchPublishMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1517" name="XiaoWoSaveLayoutPublishRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="1518" name="XiaoWoSaveLayoutPublishRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="1519" name="LotterySpringBlessingCard_C2S_Msg" serverName="gamesvr"/>
    <entry id="1520" name="LotterySpringBlessingCard_S2C_Msg" serverName="gamesvr"/>
    <entry id="1521" name="GiveSpringBlessingCard_C2S_Msg" serverName="gamesvr"/>
    <entry id="1522" name="GiveSpringBlessingCard_S2C_Msg" serverName="gamesvr"/>
    <entry id="1523" name="UgcAigcDelHistory_C2S_Msg" serverName="gamesvr"/>
    <entry id="1524" name="UgcAigcDelHistory_S2C_Msg" serverName="gamesvr"/>
    <entry id="1525" name="SpringPray_C2S_Msg" serverName="gamesvr"/>
    <entry id="1526" name="SpringPray_S2C_Msg" serverName="gamesvr"/>
    <entry id="1527" name="PlayerPrayShare_C2S_Msg" serverName="gamesvr"/>
    <entry id="1528" name="PlayerPrayShare_S2C_Msg" serverName="gamesvr"/>
    <entry id="1529" name="NewYearPilotInfoNtf" serverName="gamesvr"/>
    <entry id="1530" name="IntellectualActiviyNtf" serverName="gamesvr"/>
    <entry id="1531" name="ReceiveSpringBlessingCardNtf" serverName="gamesvr"/>
    <entry id="1532" name="GetXiaowoRecomList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1533" name="GetXiaowoRecomList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1534" name="FpsGetActivityCoinInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1535" name="FpsGetActivityCoinInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1536" name="ClubPinShareMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="1537" name="ClubPinShareMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="1538" name="RedPacketQuerySingle_C2S_Msg" serverName="gamesvr"/>
    <entry id="1539" name="RedPacketQuerySingle_S2C_Msg" serverName="gamesvr"/>
    <entry id="1540" name="UltramanThemeAcitvityTeam_C2S_Msg" serverName="gamesvr"/>
    <entry id="1541" name="UltramanThemeAcitvityTeam_S2C_Msg" serverName="gamesvr"/>
    <entry id="1542" name="RedPacketPatchInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1543" name="RedPacketPatchInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1544" name="TYCFpsSetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="1545" name="TYCFpsSetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="1546" name="TYCFpsGetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="1547" name="TYCFpsGetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="1548" name="GetReturnActivityReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1549" name="GetReturnActivityReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1550" name="ReturningActiviyStartNtf" serverName="gamesvr"/>
    <entry id="1551" name="UltramanThemeSelfCollectionProgress_C2S_Msg" serverName="gamesvr"/>
    <entry id="1552" name="UltramanThemeSelfCollectionProgress_S2C_Msg" serverName="gamesvr"/>
    <entry id="1553" name="GetAccumulateBlessingsOnlineTime_C2S_Msg" serverName="gamesvr"/>
    <entry id="1554" name="GetAccumulateBlessingsOnlineTime_S2C_Msg" serverName="gamesvr"/>
    <entry id="1555" name="FpsGetDropLimitInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1556" name="FpsGetDropLimitInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1557" name="RedPacketEnterScene_C2S_Msg" serverName="gamesvr"/>
    <entry id="1558" name="RedPacketEnterScene_S2C_Msg" serverName="gamesvr"/>
    <entry id="1559" name="PlayerXiaowoAttrNtf" serverName="gamesvr"/>
    <entry id="1560" name="RedPacketClickRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="1561" name="RedPacketClickRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="1562" name="ListUseItemShareRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="1563" name="ListUseItemShareRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="1564" name="ListUseItemChangeRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="1565" name="ListUseItemChangeRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="1566" name="QuitBattleNtf" serverName="gamesvr"/>
    <entry id="1567" name="ReturnActivityEndNtf" serverName="gamesvr"/>
    <entry id="1568" name="ReturnActivityGenericNtf" serverName="gamesvr"/>
    <entry id="1569" name="ClubRedDotClear_C2S_Msg" serverName="gamesvr"/>
    <entry id="1570" name="ClubRedDotClear_S2C_Msg" serverName="gamesvr"/>
    <entry id="1571" name="UltramanThemeAddSelfCollection_C2S_Msg" serverName="gamesvr"/>
    <entry id="1572" name="UltramanThemeAddSelfCollection_S2C_Msg" serverName="gamesvr"/>
    <entry id="1573" name="TimeLimitedCheckInActivityDataChangeNtf" serverName="gamesvr"/>
    <entry id="1574" name="FetchTopRankByScore_C2S_Msg" serverName="gamesvr"/>
    <entry id="1575" name="FetchTopRankByScore_S2C_Msg" serverName="gamesvr"/>
    <entry id="1576" name="GetRaffleList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1577" name="GetRaffleList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1578" name="MatchTypeList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1579" name="MatchTypeList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1580" name="SetFriendRemarkName_C2S_Msg" serverName="gamesvr"/>
    <entry id="1581" name="SetFriendRemarkName_S2C_Msg" serverName="gamesvr"/>
    <entry id="1582" name="GetRankTabRankIdList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1583" name="GetRankTabRankIdList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1584" name="StickFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="1585" name="StickFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="1586" name="XiaoWoSetWelcomeInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1587" name="XiaoWoSetWelcomeInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1588" name="XiaoWoSendLiuYanMessage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1589" name="XiaoWoSendLiuYanMessage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1590" name="XiaoWoDeleteLiuYanMessage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1591" name="XiaoWoDeleteLiuYanMessage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1592" name="XiaoWoGetLiuYanMessage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1593" name="XiaoWoGetLiuYanMessage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1594" name="XiaoWoGetLiuYanMessageReply_C2S_Msg" serverName="gamesvr"/>
    <entry id="1595" name="XiaoWoGetLiuYanMessageReply_S2C_Msg" serverName="gamesvr"/>
    <entry id="1596" name="XiaoWoChoiceLiuYanMessage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1597" name="XiaoWoChoiceLiuYanMessage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1598" name="XiaoWoReceiveNewLiuYanMessageNtf" serverName="gamesvr"/>
    <entry id="1599" name="UgcCollectionCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="1600" name="UgcCollectionCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="1601" name="UgcCollectionModify_C2S_Msg" serverName="gamesvr"/>
    <entry id="1602" name="UgcCollectionModify_S2C_Msg" serverName="gamesvr"/>
    <entry id="1603" name="UgcCollectionGetBriefs_C2S_Msg" serverName="gamesvr"/>
    <entry id="1604" name="UgcCollectionGetBriefs_S2C_Msg" serverName="gamesvr"/>
    <entry id="1605" name="UgcCollectionOperate_C2S_Msg" serverName="gamesvr"/>
    <entry id="1606" name="UgcCollectionOperate_S2C_Msg" serverName="gamesvr"/>
    <entry id="1607" name="UgcCollectionSearch_C2S_Msg" serverName="gamesvr"/>
    <entry id="1608" name="UgcCollectionSearch_S2C_Msg" serverName="gamesvr"/>
    <entry id="1609" name="UgcCollectionGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1610" name="UgcCollectionGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1611" name="UploadGameSetting_C2S_Msg" serverName="gamesvr"/>
    <entry id="1612" name="UploadGameSetting_S2C_Msg" serverName="gamesvr"/>
    <entry id="1613" name="DownloadGameSetting_C2S_Msg" serverName="gamesvr"/>
    <entry id="1614" name="DownloadGameSetting_S2C_Msg" serverName="gamesvr"/>
    <entry id="1615" name="GetSeasonFashionEquipBook_C2S_Msg" serverName="gamesvr"/>
    <entry id="1616" name="GetSeasonFashionEquipBook_S2C_Msg" serverName="gamesvr"/>
    <entry id="1617" name="ClubGetRecommendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1618" name="ClubGetRecommendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1619" name="SceneLevelPackage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1620" name="SceneLevelPackage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1621" name="RoomReservation_C2S_Msg" serverName="gamesvr"/>
    <entry id="1622" name="RoomReservation_S2C_Msg" serverName="gamesvr"/>
    <entry id="1623" name="RoomReservationNtf" serverName="gamesvr"/>
    <entry id="1624" name="RoomReservationResponse_C2S_Msg" serverName="gamesvr"/>
    <entry id="1625" name="RoomReservationResponse_S2C_Msg" serverName="gamesvr"/>
    <entry id="1626" name="RoomReservationResponseNtf" serverName="gamesvr"/>
    <entry id="1627" name="ChangeShowQualifyType_C2S_Msg" serverName="gamesvr"/>
    <entry id="1628" name="ChangeShowQualifyType_S2C_Msg" serverName="gamesvr"/>
    <entry id="1629" name="BuyReturnChargeSignInActivityTicket_C2S_Msg" serverName="gamesvr"/>
    <entry id="1630" name="BuyReturnChargeSignInActivityTicket_S2C_Msg" serverName="gamesvr"/>
    <entry id="1631" name="BuyReturnChargeSignInActivityTicketNtf" serverName="gamesvr"/>
    <entry id="1632" name="UgcMatchLobbyDetailEx_C2S_Msg" serverName="gamesvr"/>
    <entry id="1633" name="UgcMatchLobbyDetailEx_S2C_Msg" serverName="gamesvr"/>
    <entry id="1634" name="GetCheckInManualReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1635" name="GetCheckInManualReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1636" name="UpgradeCheckInManual_C2S_Msg" serverName="gamesvr"/>
    <entry id="1637" name="UpgradeCheckInManual_S2C_Msg" serverName="gamesvr"/>
    <entry id="1638" name="CheckInManualUpgradeNtf" serverName="gamesvr"/>
    <entry id="1639" name="ClubMSDKReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="1640" name="ClubMSDKReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="1641" name="XiaoWoIsWhiteListAccountNtf" serverName="gamesvr"/>
    <entry id="1642" name="UgcBugGoods_C2S_Msg" serverName="gamesvr"/>
    <entry id="1643" name="UgcBugGoods_S2C_Msg" serverName="gamesvr"/>
    <entry id="1644" name="UgcBuyGoodsResultNtf" serverName="gamesvr"/>
    <entry id="1645" name="UgcActivePublishGoods_C2S_Msg" serverName="gamesvr"/>
    <entry id="1646" name="UgcActivePublishGoods_S2C_Msg" serverName="gamesvr"/>
    <entry id="1647" name="UserWhiteListCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="1648" name="UserWhiteListCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="1649" name="ClubBatchGetBasicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1650" name="ClubBatchGetBasicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1651" name="UgcApplyGoodsId_C2S_Msg" serverName="gamesvr"/>
    <entry id="1652" name="UgcApplyGoodsId_S2C_Msg" serverName="gamesvr"/>
    <entry id="1653" name="UgcPreAudit_C2S_Msg" serverName="gamesvr"/>
    <entry id="1654" name="UgcPreAudit_S2C_Msg" serverName="gamesvr"/>
    <entry id="1655" name="UgcPreAuditResultNtf" serverName="gamesvr"/>
    <entry id="1656" name="ClubSearchByTag_C2S_Msg" serverName="gamesvr"/>
    <entry id="1657" name="ClubSearchByTag_S2C_Msg" serverName="gamesvr"/>
    <entry id="1658" name="UgcStarWorldNavigationBar_C2S_Msg" serverName="gamesvr"/>
    <entry id="1659" name="UgcStarWorldNavigationBar_S2C_Msg" serverName="gamesvr"/>
    <entry id="1660" name="ActivityLotteryDraw_C2S_Msg" serverName="gamesvr"/>
    <entry id="1661" name="ActivityLotteryDraw_S2C_Msg" serverName="gamesvr"/>
    <entry id="1662" name="InterServerGiftGetFriendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1663" name="InterServerGiftGetFriendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1664" name="InterServerGiftCheckFriendBuy_C2S_Msg" serverName="gamesvr"/>
    <entry id="1665" name="InterServerGiftCheckFriendBuy_S2C_Msg" serverName="gamesvr"/>
    <entry id="1666" name="ReturnRefreshJumpToSignInAfterRoundFlag_C2S_Msg" serverName="gamesvr"/>
    <entry id="1667" name="ReturnRefreshJumpToSignInAfterRoundFlag_S2C_Msg" serverName="gamesvr"/>
    <entry id="1668" name="RoomMapVote_C2S_Msg" serverName="gamesvr"/>
    <entry id="1669" name="RoomMapVote_S2C_Msg" serverName="gamesvr"/>
    <entry id="1670" name="GetUgcCoPlayInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1671" name="GetUgcCoPlayInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1672" name="UgcBuyGoods_C2S_Msg" serverName="gamesvr"/>
    <entry id="1673" name="UgcBuyGoods_S2C_Msg" serverName="gamesvr"/>
    <entry id="1674" name="UgcNeedDownRes_C2S_Msg" serverName="gamesvr"/>
    <entry id="1675" name="UgcNeedDownRes_S2C_Msg" serverName="gamesvr"/>
    <entry id="1676" name="UgcGetDataStoreData_C2S_Msg" serverName="gamesvr"/>
    <entry id="1677" name="UgcGetDataStoreData_S2C_Msg" serverName="gamesvr"/>
    <entry id="1678" name="NewActivityPilotInfoNtf" serverName="gamesvr"/>
    <entry id="1679" name="RoomEndSettlement_C2S_Msg" serverName="gamesvr"/>
    <entry id="1680" name="RoomEndSettlement_S2C_Msg" serverName="gamesvr"/>
    <entry id="1681" name="UgcCollectionReserved1_C2S_Msg" serverName="gamesvr"/>
    <entry id="1682" name="UgcCollectionReserved1_S2C_Msg" serverName="gamesvr"/>
    <entry id="1683" name="UgcGetPublishGoodsProtoVersion_C2S_Msg" serverName="gamesvr"/>
    <entry id="1684" name="UgcGetPublishGoodsProtoVersion_S2C_Msg" serverName="gamesvr"/>
    <entry id="1685" name="UgcSetPublishGoodsProtoVersion_C2S_Msg" serverName="gamesvr"/>
    <entry id="1686" name="UgcSetPublishGoodsProtoVersion_S2C_Msg" serverName="gamesvr"/>
    <entry id="1687" name="PlayerGameSettingTlog_C2S_Msg" serverName="gamesvr"/>
    <entry id="1688" name="PlayerGameSettingTlog_S2C_Msg" serverName="gamesvr"/>
    <entry id="1689" name="RoomNeedNtfWhenAllMemberReadyForMatch_C2S_Msg" serverName="gamesvr"/>
    <entry id="1690" name="RoomNeedNtfWhenAllMemberReadyForMatch_S2C_Msg" serverName="gamesvr"/>
    <entry id="1691" name="RoomAllMemberReadyForMatchNtf" serverName="gamesvr"/>
    <entry id="1692" name="RoomOperationNtf" serverName="gamesvr"/>
    <entry id="1693" name="FeedKungFuPanda_C2S_Msg" serverName="gamesvr"/>
    <entry id="1694" name="FeedKungFuPanda_S2C_Msg" serverName="gamesvr"/>
    <entry id="1695" name="ReportRacingKungFuPandaResult_C2S_Msg" serverName="gamesvr"/>
    <entry id="1696" name="ReportRacingKungFuPandaResult_S2C_Msg" serverName="gamesvr"/>
    <entry id="1697" name="ListKungFuPandaHelpData_C2S_Msg" serverName="gamesvr"/>
    <entry id="1698" name="ListKungFuPandaHelpData_S2C_Msg" serverName="gamesvr"/>
    <entry id="1699" name="CheckGiveMailAttachment_C2S_Msg" serverName="gamesvr"/>
    <entry id="1700" name="CheckGiveMailAttachment_S2C_Msg" serverName="gamesvr"/>
    <entry id="1701" name="LobbyGetMapInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1702" name="LobbyGetMapInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1703" name="MallGiveDeliverGoodsNtf" serverName="gamesvr"/>
    <entry id="1704" name="LobbyGetListInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1705" name="LobbyGetListInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1706" name="RoomCommonBroadcast_C2S_Msg" serverName="gamesvr"/>
    <entry id="1707" name="RoomCommonBroadcast_S2C_Msg" serverName="gamesvr"/>
    <entry id="1708" name="RoomBroadcastInfoNtf" serverName="gamesvr"/>
    <entry id="1709" name="GetConcertStarInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1710" name="GetConcertStarInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1711" name="GenerateConcertTicket_C2S_Msg" serverName="gamesvr"/>
    <entry id="1712" name="GenerateConcertTicket_S2C_Msg" serverName="gamesvr"/>
    <entry id="1713" name="GetConcertTicketInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1714" name="GetConcertTicketInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1730" name="TeamRecommendRole_C2S_Msg" serverName="gamesvr"/>
    <entry id="1731" name="TeamRecommendRole_S2C_Msg" serverName="gamesvr"/>
    <entry id="1732" name="GetPlayerGrayTagInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1733" name="GetPlayerGrayTagInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1750" name="UgcCollectionRecommendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1751" name="UgcCollectionRecommendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1752" name="UgcCollectionExcellent_C2S_Msg" serverName="gamesvr"/>
    <entry id="1753" name="UgcCollectionExcellent_S2C_Msg" serverName="gamesvr"/>
    <entry id="1802" name="TeamRecommendRoleNtf" serverName="gamesvr"/>
    <entry id="1803" name="BagUnlockSlotId_C2S_Msg" serverName="gamesvr"/>
    <entry id="1804" name="BagUnlockSlotId_S2C_Msg" serverName="gamesvr"/>
    <entry id="1805" name="PlayerSettingHomePage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1806" name="PlayerSettingHomePage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1807" name="ActivityGetMultiPlayerSquadInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1808" name="ActivityGetMultiPlayerSquadInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1809" name="ActivityMultiPlayerSquadJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="1810" name="ActivityMultiPlayerSquadJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="1811" name="ActivityMultiPlayerSquadSaveGroupPhoto_C2S_Msg" serverName="gamesvr"/>
    <entry id="1812" name="ActivityMultiPlayerSquadSaveGroupPhoto_S2C_Msg" serverName="gamesvr"/>
    <entry id="1817" name="SetBattlePlayerClientInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1818" name="SetBattlePlayerClientInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1819" name="BattlePlayerClientInfoModifyNtf" serverName="gamesvr"/>
    <entry id="1820" name="SendRoomMemberToMemberNtf_C2S_Msg" serverName="gamesvr"/>
    <entry id="1821" name="SendRoomMemberToMemberNtf_S2C_Msg" serverName="gamesvr"/>
    <entry id="1822" name="RoomMemberToMemberNtf" serverName="gamesvr"/>
    <entry id="1823" name="FittingSlotShow_C2S_Msg" serverName="gamesvr"/>
    <entry id="1824" name="FittingSlotShow_S2C_Msg" serverName="gamesvr"/>
    <entry id="1825" name="StickerGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1826" name="StickerGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1827" name="CurBubbleConfigStateQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="1828" name="CurBubbleConfigStateQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="1829" name="CurBubbleConfigHasVisited_C2S_Msg" serverName="gamesvr"/>
    <entry id="1830" name="CurBubbleConfigHasVisited_S2C_Msg" serverName="gamesvr"/>
    <entry id="1831" name="GetCompetitionWarmUpGameTimesReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1832" name="GetCompetitionWarmUpGameTimesReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1833" name="GetCompetitionWarmUpScoreReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1834" name="GetCompetitionWarmUpScoreReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1835" name="BagSetInteractionCombination_C2S_Msg" serverName="gamesvr"/>
    <entry id="1836" name="BagSetInteractionCombination_S2C_Msg" serverName="gamesvr"/>
    <entry id="1837" name="BagDelInteractionCombination_C2S_Msg" serverName="gamesvr"/>
    <entry id="1838" name="BagDelInteractionCombination_S2C_Msg" serverName="gamesvr"/>
    <entry id="1839" name="ChatSendClubGroupInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="1840" name="ChatSendClubGroupInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="1841" name="DanMuSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="1842" name="DanMuSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="1843" name="DanMuSendNtf" serverName="gamesvr"/>
    <entry id="1844" name="DanMuDelete_C2S_Msg" serverName="gamesvr"/>
    <entry id="1845" name="DanMuDelete_S2C_Msg" serverName="gamesvr"/>
    <entry id="1846" name="DanMuDeleteNtf" serverName="gamesvr"/>
    <entry id="1847" name="DanMuDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="1848" name="DanMuDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="1849" name="DanMuLike_C2S_Msg" serverName="gamesvr"/>
    <entry id="1850" name="DanMuLike_S2C_Msg" serverName="gamesvr"/>
    <entry id="1851" name="DanMuLikeNtf" serverName="gamesvr"/>
    <entry id="1852" name="DanMuTipOff_C2S_Msg" serverName="gamesvr"/>
    <entry id="1853" name="DanMuTipOff_S2C_Msg" serverName="gamesvr"/>
    <entry id="1854" name="DanMuDetailFlush_C2S_Msg" serverName="gamesvr"/>
    <entry id="1855" name="DanMuDetailFlush_S2C_Msg" serverName="gamesvr"/>
    <entry id="1856" name="FarmCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="1857" name="FarmCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="1858" name="FarmEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="1859" name="FarmEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="1860" name="FarmExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="1861" name="FarmExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="1862" name="FarmKickNtf" serverName="gamesvr"/>
    <entry id="1863" name="FarmDSInfoNtf" serverName="gamesvr"/>
    <entry id="1864" name="FarmAttrNtf" serverName="gamesvr"/>
    <entry id="1865" name="FarmOwnerAttrNtf" serverName="gamesvr"/>
    <entry id="1866" name="FarmTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="1867" name="FarmTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="1868" name="FarmOp_C2S_Msg" serverName="gamesvr"/>
    <entry id="1869" name="FarmOp_S2C_Msg" serverName="gamesvr"/>
    <entry id="1870" name="FarmBuildingLevelUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="1871" name="FarmBuildingLevelUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="1872" name="FarmBuildingSell_C2S_Msg" serverName="gamesvr"/>
    <entry id="1873" name="FarmBuildingSell_S2C_Msg" serverName="gamesvr"/>
    <entry id="1874" name="FarmBuildingUnlock_C2S_Msg" serverName="gamesvr"/>
    <entry id="1875" name="FarmBuildingUnlock_S2C_Msg" serverName="gamesvr"/>
    <entry id="1876" name="FarmForwardToDS_C2S_Msg" serverName="gamesvr"/>
    <entry id="1877" name="FarmForwardToDS_S2C_Msg" serverName="gamesvr"/>
    <entry id="1878" name="FarmBadGuysList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1879" name="FarmBadGuysList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1880" name="FarmBadGuyStealingDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="1881" name="FarmBadGuyStealingDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="1882" name="FarmStrangerList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1883" name="FarmStrangerList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1884" name="FarmEvictBadGuy_C2S_Msg" serverName="gamesvr"/>
    <entry id="1885" name="FarmEvictBadGuy_S2C_Msg" serverName="gamesvr"/>
    <entry id="1886" name="FarmGetItemsNtf" serverName="gamesvr"/>
    <entry id="1887" name="FarmCropMenuSelect_C2S_Msg" serverName="gamesvr"/>
    <entry id="1888" name="FarmCropMenuSelect_S2C_Msg" serverName="gamesvr"/>
    <entry id="1889" name="FarmSetClientKV_C2S_Msg" serverName="gamesvr"/>
    <entry id="1890" name="FarmSetClientKV_S2C_Msg" serverName="gamesvr"/>
    <entry id="1891" name="RoguelikeQuickSettlement_C2S_Msg" serverName="gamesvr"/>
    <entry id="1892" name="RoguelikeQuickSettlement_S2C_Msg" serverName="gamesvr"/>
    <entry id="1893" name="RoguelikeQuickSettlementNtf" serverName="gamesvr"/>
    <entry id="1894" name="LetsGoSeasonBatchSettlementNtf" serverName="gamesvr"/>
    <entry id="1895" name="MallSeasonShopTabMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="1896" name="MallSeasonShopTabMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="1897" name="PlayerBatchGetRecordItemForLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="1898" name="PlayerBatchGetRecordItemForLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="1899" name="GetQuickRewardList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1900" name="GetQuickRewardList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1901" name="ReceiveQuickReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1902" name="ReceiveQuickReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1903" name="DSInvitationReplyCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="1904" name="DSInvitationReplyCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="1905" name="RoomMidJoinBattleFailNtf" serverName="gamesvr"/>
    <entry id="1906" name="GetSeasonFashionBattleData_C2S_Msg" serverName="gamesvr"/>
    <entry id="1907" name="GetSeasonFashionBattleData_S2C_Msg" serverName="gamesvr"/>
    <entry id="1908" name="GetSeasonFashionGeneralBattleData_C2S_Msg" serverName="gamesvr"/>
    <entry id="1909" name="GetSeasonFashionGeneralBattleData_S2C_Msg" serverName="gamesvr"/>
    <entry id="1910" name="StreamToken_C2S_Msg" serverName="gamesvr"/>
    <entry id="1911" name="StreamToken_S2C_Msg" serverName="gamesvr"/>
    <entry id="1912" name="UgcResPrivateAdapt_C2S_Msg" serverName="gamesvr"/>
    <entry id="1913" name="UgcResPrivateAdapt_S2C_Msg" serverName="gamesvr"/>
    <entry id="1914" name="UgcFastSlot_C2S_Msg" serverName="gamesvr"/>
    <entry id="1915" name="UgcFastSlot_S2C_Msg" serverName="gamesvr"/>
    <entry id="1916" name="UgcMapLabelScore_C2S_Msg" serverName="gamesvr"/>
    <entry id="1917" name="UgcMapLabelScore_S2C_Msg" serverName="gamesvr"/>
    <entry id="1918" name="UgcGetPlayerMapLabelScore_C2S_Msg" serverName="gamesvr"/>
    <entry id="1919" name="UgcGetPlayerMapLabelScore_S2C_Msg" serverName="gamesvr"/>
    <entry id="1920" name="UgcMapDownloadInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1921" name="UgcMapDownloadInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1922" name="UgcDeleteCover_C2S_Msg" serverName="gamesvr"/>
    <entry id="1923" name="UgcDeleteCover_S2C_Msg" serverName="gamesvr"/>
    <entry id="1924" name="UgcUploadCover_C2S_Msg" serverName="gamesvr"/>
    <entry id="1925" name="UgcUploadCover_S2C_Msg" serverName="gamesvr"/>
    <entry id="1926" name="FetchUgcTopRank_C2S_Msg" serverName="gamesvr"/>
    <entry id="1927" name="FetchUgcTopRank_S2C_Msg" serverName="gamesvr"/>
    <entry id="1928" name="UgcApplyRankId_C2S_Msg" serverName="gamesvr"/>
    <entry id="1929" name="UgcApplyRankId_S2C_Msg" serverName="gamesvr"/>
    <entry id="1930" name="UpdateUgcRank_C2S_Msg" serverName="gamesvr"/>
    <entry id="1931" name="UpdateUgcRank_S2C_Msg" serverName="gamesvr"/>
    <entry id="1932" name="GetUgcRankAppointPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="1933" name="GetUgcRankAppointPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="1934" name="DeleteUgcRankEntryByPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="1935" name="DeleteUgcRankEntryByPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="1936" name="GetUgcRankAppointRankNo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1937" name="GetUgcRankAppointRankNo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1938" name="BatchGetUgcRankPlayerPublicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1939" name="BatchGetUgcRankPlayerPublicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1940" name="UgcSetUpCover_C2S_Msg" serverName="gamesvr"/>
    <entry id="1941" name="UgcSetUpCover_S2C_Msg" serverName="gamesvr"/>
    <entry id="1942" name="UgcCoverList_C2S_Msg" serverName="gamesvr"/>
    <entry id="1943" name="UgcCoverList_S2C_Msg" serverName="gamesvr"/>
    <entry id="1944" name="XiaoWoShare_C2S_Msg" serverName="gamesvr"/>
    <entry id="1945" name="XiaoWoShare_S2C_Msg" serverName="gamesvr"/>
    <entry id="1946" name="XiaoWoGetVerAndGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="1947" name="XiaoWoGetVerAndGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="1948" name="StreamNpcChat_C2S_Msg" serverName="streamsvr"/>
    <entry id="1949" name="StreamNpcChat_S2C_Msg" serverName="streamsvr"/>
    <entry id="1950" name="StreamErrorNtf" serverName="streamsvr"/>
    <entry id="1951" name="StreamNpcChatResponseNtf" serverName="streamsvr"/>
    <entry id="1952" name="StreamLLMCheckResultNtf" serverName="streamsvr"/>
    <entry id="1953" name="StreamLogin_C2S_Msg" serverName="streamsvr"/>
    <entry id="1954" name="StreamLogin_S2C_Msg" serverName="streamsvr"/>
    <entry id="1955" name="GetLevelUGCInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1956" name="GetLevelUGCInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1957" name="ActivityTakePhoto_C2S_Msg" serverName="gamesvr"/>
    <entry id="1958" name="ActivityTakePhoto_S2C_Msg" serverName="gamesvr"/>
    <entry id="1959" name="ChangeProfileThemeMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="1960" name="ChangeProfileThemeMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="1961" name="ActivityDataChangeNtf" serverName="gamesvr"/>
    <entry id="1962" name="SubscribeQQRobot_C2S_Msg" serverName="gamesvr"/>
    <entry id="1963" name="SubscribeQQRobot_S2C_Msg" serverName="gamesvr"/>
    <entry id="1964" name="AppCommon_C2S_Msg" serverName="gamesvr"/>
    <entry id="1965" name="AppCommon_S2C_Msg" serverName="gamesvr"/>
    <entry id="1966" name="GameNtf" serverName="gamesvr"/>
    <entry id="1967" name="GetUserCloudInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1968" name="GetUserCloudInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1969" name="FarmSendLiuYanMessage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1970" name="FarmSendLiuYanMessage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1971" name="FarmDeleteLiuYanMessage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1972" name="FarmDeleteLiuYanMessage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1973" name="FarmGetLiuYanMessage_C2S_Msg" serverName="gamesvr"/>
    <entry id="1974" name="FarmGetLiuYanMessage_S2C_Msg" serverName="gamesvr"/>
    <entry id="1975" name="FarmReceiveNewLiuYanMessageNtf" serverName="gamesvr"/>
    <entry id="1976" name="FarmSetWelcomeInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="1977" name="FarmSetWelcomeInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="1978" name="GetRecommendMatchTypeReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="1979" name="GetRecommendMatchTypeReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="1980" name="RecommendMatchTypeTaskNtf" serverName="gamesvr"/>
    <entry id="1981" name="IntimateRelationOnlineNoticeRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="1982" name="IntimateRelationOnlineNoticeRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="1983" name="RejectIntimateRelationOnlineNotice_C2S_Msg" serverName="gamesvr"/>
    <entry id="1984" name="RejectIntimateRelationOnlineNotice_S2C_Msg" serverName="gamesvr"/>
    <entry id="1985" name="UgcGetBadge_C2S_Msg" serverName="gamesvr"/>
    <entry id="1986" name="UgcGetBadge_S2C_Msg" serverName="gamesvr"/>
    <entry id="1987" name="UgcGetBadgeDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="1988" name="UgcGetBadgeDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="1989" name="UgcCopyMapProgressNtf" serverName="gamesvr"/>
    <entry id="1990" name="ClickNewRecommendMatchType_C2S_Msg" serverName="gamesvr"/>
    <entry id="1991" name="ClickNewRecommendMatchType_S2C_Msg" serverName="gamesvr"/>
    <entry id="1992" name="NewRecommendMatchTypeChangeNtf" serverName="gamesvr"/>
    <entry id="1993" name="IntimateRelationGuideNtf" serverName="gamesvr"/>
    <entry id="1994" name="DelayIntimateRelationGuide_C2S_Msg" serverName="gamesvr"/>
    <entry id="1995" name="DelayIntimateRelationGuide_S2C_Msg" serverName="gamesvr"/>
    <entry id="1996" name="PlayerAlbumPictureAdd_C2S_Msg" serverName="gamesvr"/>
    <entry id="1997" name="PlayerAlbumPictureAdd_S2C_Msg" serverName="gamesvr"/>
    <entry id="1998" name="PlayerAlbumPictureDel_C2S_Msg" serverName="gamesvr"/>
    <entry id="1999" name="PlayerAlbumPictureDel_S2C_Msg" serverName="gamesvr"/>
    <entry id="2000" name="DressItemStatusChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="2001" name="DressItemStatusChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="2002" name="ChatControl_C2S_Msg" serverName="gamesvr"/>
    <entry id="2003" name="ChatControl_S2C_Msg" serverName="gamesvr"/>
    <entry id="2004" name="DirGetFreeFlowInfo_C2S_Msg" serverName="dirsvr"/>
    <entry id="2005" name="DirGetFreeFlowInfo_S2C_Msg" serverName="dirsvr"/>
    <entry id="2006" name="AchievementGetCompleteStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="2007" name="AchievementGetCompleteStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="2008" name="ActivityGetAllInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2009" name="ActivityGetAllInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2010" name="ActivityReceiveRewards_C2S_Msg" serverName="gamesvr"/>
    <entry id="2011" name="ActivityReceiveRewards_S2C_Msg" serverName="gamesvr"/>
    <entry id="2012" name="ActivityInfoUpdateNtf" serverName="gamesvr"/>
    <entry id="2013" name="ReturnActivityAttrChangeNtf" serverName="gamesvr"/>
    <entry id="2014" name="ReturnActivityRefreshRecommendMatchType_C2S_Msg" serverName="gamesvr"/>
    <entry id="2015" name="ReturnActivityRefreshRecommendMatchType_S2C_Msg" serverName="gamesvr"/>
    <entry id="2016" name="ReturnActivityBuyChargeGiftTicket_C2S_Msg" serverName="gamesvr"/>
    <entry id="2017" name="ReturnActivityBuyChargeGiftTicket_S2C_Msg" serverName="gamesvr"/>
    <entry id="2018" name="ReturnActivityBuyChargeGiftTicketNtf" serverName="gamesvr"/>
    <entry id="2019" name="ActivityGetRecommendPlayerList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2020" name="ActivityGetRecommendPlayerList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2021" name="GetIntelligenceStationReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2022" name="GetIntelligenceStationReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2023" name="IntelligenceStationChangeNtf" serverName="gamesvr"/>
    <entry id="2024" name="ActivityFlyingChessGetFreeCoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="2025" name="ActivityFlyingChessGetFreeCoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="2026" name="ActivityFlyingChessRun_C2S_Msg" serverName="gamesvr"/>
    <entry id="2027" name="ActivityFlyingChessRun_S2C_Msg" serverName="gamesvr"/>
    <entry id="2028" name="ActivityFlyingChessRoundRewardGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2029" name="ActivityFlyingChessRoundRewardGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2030" name="ActivityFlyingChessBigRewardGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2031" name="ActivityFlyingChessBigRewardGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2032" name="ActivityFlyingChessBigRewardGetNtf" serverName="gamesvr"/>
    <entry id="2033" name="ActivityHYNSyncInfoNtf" serverName="gamesvr"/>
    <entry id="2034" name="ActivityHYNCheckIn_C2S_Msg" serverName="gamesvr"/>
    <entry id="2035" name="ActivityHYNCheckIn_S2C_Msg" serverName="gamesvr"/>
    <entry id="2036" name="ActivityHYNRecvReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2037" name="ActivityHYNRecvReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2038" name="ActivityHYWarmUpInfoNtf" serverName="gamesvr"/>
    <entry id="2039" name="ActivityHYWarmUpCheckIn_C2S_Msg" serverName="gamesvr"/>
    <entry id="2040" name="ActivityHYWarmUpCheckIn_S2C_Msg" serverName="gamesvr"/>
    <entry id="2041" name="ActivityHYWarmUpRecvReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2042" name="ActivityHYWarmUpRecvReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2043" name="ActivityMusicOrderCompleteOrder_C2S_Msg" serverName="gamesvr"/>
    <entry id="2044" name="ActivityMusicOrderCompleteOrder_S2C_Msg" serverName="gamesvr"/>
    <entry id="2045" name="ActivityMusicOrderResetOrder_C2S_Msg" serverName="gamesvr"/>
    <entry id="2046" name="ActivityMusicOrderResetOrder_S2C_Msg" serverName="gamesvr"/>
    <entry id="2047" name="ActivityMusicOrderGetTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2048" name="ActivityMusicOrderGetTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2049" name="ActivityMusicOrderGetOrderList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2050" name="ActivityMusicOrderGetOrderList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2051" name="ActivityMusicOrderUpdateNtf" serverName="gamesvr"/>
    <entry id="2052" name="GetEntertainmentGuideTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2053" name="GetEntertainmentGuideTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2054" name="EntertainmentGuideNtf" serverName="gamesvr"/>
    <entry id="2055" name="FarmBuildingFirstFlag_C2S_Msg" serverName="gamesvr"/>
    <entry id="2056" name="FarmBuildingFirstFlag_S2C_Msg" serverName="gamesvr"/>
    <entry id="2057" name="FarmDoGooderDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="2058" name="FarmDoGooderDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="2059" name="FarmGetFarmDynamicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2060" name="FarmGetFarmDynamicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2061" name="FarmSubMonPass_C2S_Msg" serverName="gamesvr"/>
    <entry id="2062" name="FarmSubMonPass_S2C_Msg" serverName="gamesvr"/>
    <entry id="2063" name="FarmTagFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="2064" name="FarmTagFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="2065" name="FarmSellAnimal_C2S_Msg" serverName="gamesvr"/>
    <entry id="2066" name="FarmSellAnimal_S2C_Msg" serverName="gamesvr"/>
    <entry id="2067" name="FarmQueryIsWhite_C2S_Msg" serverName="gamesvr"/>
    <entry id="2068" name="FarmQueryIsWhite_S2C_Msg" serverName="gamesvr"/>
    <entry id="2069" name="FarmMonthCardNtf" serverName="gamesvr"/>
    <entry id="2070" name="ChangeCatchphrase_C2S_Msg" serverName="gamesvr"/>
    <entry id="2071" name="ChangeCatchphrase_S2C_Msg" serverName="gamesvr"/>
    <entry id="2072" name="RecommendFriendsCDSet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2073" name="RecommendFriendsCDSet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2074" name="GetFreeFlowInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2075" name="GetFreeFlowInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2076" name="ReportNewPlayerGuide_C2S_Msg" serverName="gamesvr"/>
    <entry id="2077" name="ReportNewPlayerGuide_S2C_Msg" serverName="gamesvr"/>
    <entry id="2078" name="GetReputationScoreChangeList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2079" name="GetReputationScoreChangeList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2080" name="ReportIAA_C2S_Msg" serverName="gamesvr"/>
    <entry id="2081" name="ReportIAA_S2C_Msg" serverName="gamesvr"/>
    <entry id="2082" name="ReportIAANtf" serverName="gamesvr"/>
    <entry id="2083" name="GetIAAList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2084" name="GetIAAList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2085" name="PlayerReputationScoreFunctionSwitchNtf" serverName="gamesvr"/>
    <entry id="2086" name="IsInAdminWhiteList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2087" name="IsInAdminWhiteList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2088" name="RaffleShareReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="2089" name="RaffleShareReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="2090" name="RecommendFriendsByRecentBattle_C2S_Msg" serverName="gamesvr"/>
    <entry id="2091" name="RecommendFriendsByRecentBattle_S2C_Msg" serverName="gamesvr"/>
    <entry id="2092" name="GetFriendCanRentFashionList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2093" name="GetFriendCanRentFashionList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2094" name="RentFriendFashion_C2S_Msg" serverName="gamesvr"/>
    <entry id="2095" name="RentFriendFashion_S2C_Msg" serverName="gamesvr"/>
    <entry id="2096" name="DeliverHotResResource_C2S_Msg" serverName="gamesvr"/>
    <entry id="2097" name="DeliverHotResResource_S2C_Msg" serverName="gamesvr"/>
    <entry id="2098" name="TeamRecruitQueryByPlayGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="2099" name="TeamRecruitQueryByPlayGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="2100" name="TeamRecruitQuickJoinByPlayGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="2101" name="TeamRecruitQuickJoinByPlayGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="2102" name="RoomMiniGameInvitationNtf" serverName="gamesvr"/>
    <entry id="2103" name="BatchUpdateUgcRank_C2S_Msg" serverName="gamesvr"/>
    <entry id="2104" name="BatchUpdateUgcRank_S2C_Msg" serverName="gamesvr"/>
    <entry id="2105" name="UgcApplyUniqueId_C2S_Msg" serverName="gamesvr"/>
    <entry id="2106" name="UgcApplyUniqueId_S2C_Msg" serverName="gamesvr"/>
    <entry id="2107" name="UgcGetMatchRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="2108" name="UgcGetMatchRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="2109" name="UgcGetMatchRecordDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="2110" name="UgcGetMatchRecordDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="2111" name="UgcForwardTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="2112" name="UgcForwardTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="2113" name="XiaoWoModifyMd5_C2S_Msg" serverName="gamesvr"/>
    <entry id="2114" name="XiaoWoModifyMd5_S2C_Msg" serverName="gamesvr"/>
    <entry id="2115" name="XiaoWoEnterPrepare_C2S_Msg" serverName="gamesvr"/>
    <entry id="2116" name="XiaoWoEnterPrepare_S2C_Msg" serverName="gamesvr"/>
    <entry id="2117" name="XiaoWoSetLiuYanMessagePermission_C2S_Msg" serverName="gamesvr"/>
    <entry id="2118" name="XiaoWoSetLiuYanMessagePermission_S2C_Msg" serverName="gamesvr"/>
    <entry id="2119" name="FarmSocialDataNtf" serverName="gamesvr"/>
    <entry id="2120" name="ActivityReceiveRewardsNtf" serverName="gamesvr"/>
    <entry id="2121" name="ActivityGeneral_C2S_Msg" serverName="gamesvr"/>
    <entry id="2122" name="ActivityGeneral_S2C_Msg" serverName="gamesvr"/>
    <entry id="2123" name="ActivityGeneralNtf" serverName="gamesvr"/>
    <entry id="2124" name="ActivityGetActivityByType_C2S_Msg" serverName="gamesvr"/>
    <entry id="2125" name="ActivityGetActivityByType_S2C_Msg" serverName="gamesvr"/>
    <entry id="2126" name="ActivityMusicOrderNoteExchange_C2S_Msg" serverName="gamesvr"/>
    <entry id="2127" name="ActivityMusicOrderNoteExchange_S2C_Msg" serverName="gamesvr"/>
    <entry id="2134" name="LetsGoGetWolfKillRoleInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2135" name="LetsGoGetWolfKillRoleInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2136" name="MallBuyWolfKillMsg_C2S_Msg" serverName="gamesvr"/>
    <entry id="2137" name="MallBuyWolfKillMsg_S2C_Msg" serverName="gamesvr"/>
    <entry id="2148" name="ClearAllRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="2149" name="ClearAllRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="2150" name="WolfkillToMasterRewardRecv_C2S_Msg" serverName="gamesvr"/>
    <entry id="2151" name="WolfkillToMasterRewardRecv_S2C_Msg" serverName="gamesvr"/>
    <entry id="2152" name="RoomLbsQuickJoinByPin_C2S_Msg" serverName="gamesvr"/>
    <entry id="2153" name="RoomLbsQuickJoinByPin_S2C_Msg" serverName="gamesvr"/>
    <entry id="2156" name="ChangeSuitRandom_C2S_Msg" serverName="gamesvr"/>
    <entry id="2157" name="ChangeSuitRandom_S2C_Msg" serverName="gamesvr"/>
    <entry id="2159" name="SetDataStoreByFunc_C2S_Msg" serverName="gamesvr"/>
    <entry id="2160" name="SetDataStoreByFunc_S2C_Msg" serverName="gamesvr"/>
    <entry id="2161" name="SetChangeDataStore_C2S_Msg" serverName="gamesvr"/>
    <entry id="2162" name="SetChangeDataStore_S2C_Msg" serverName="gamesvr"/>
    <entry id="2163" name="SetDataStoreByKey_C2S_Msg" serverName="gamesvr"/>
    <entry id="2164" name="SetDataStoreByKey_S2C_Msg" serverName="gamesvr"/>
    <entry id="2165" name="GetDataStoreByKey_C2S_Msg" serverName="gamesvr"/>
    <entry id="2166" name="GetDataStoreByKey_S2C_Msg" serverName="gamesvr"/>
    <entry id="2167" name="BatchSetDataStoreByKey_C2S_Msg" serverName="gamesvr"/>
    <entry id="2168" name="BatchSetDataStoreByKey_S2C_Msg" serverName="gamesvr"/>
    <entry id="2169" name="BatchGetDataStoreByKey_C2S_Msg" serverName="gamesvr"/>
    <entry id="2170" name="BatchGetDataStoreByKey_S2C_Msg" serverName="gamesvr"/>
    <entry id="2171" name="PixuiRedDotReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="2172" name="PixuiRedDotReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="2173" name="PixuiRedDotNtf" serverName="gamesvr"/>
    <entry id="2174" name="ActivityClubChallengeMarkMatchType_C2S_Msg" serverName="gamesvr"/>
    <entry id="2175" name="ActivityClubChallengeMarkMatchType_S2C_Msg" serverName="gamesvr"/>
    <entry id="2176" name="ActivityClubChallengeAward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2177" name="ActivityClubChallengeAward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2178" name="ActivityClubChallengeStarLightInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2179" name="ActivityClubChallengeStarLightInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2180" name="WolfKillBagDressUpItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="2181" name="WolfKillBagDressUpItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="2182" name="AnimalHandbookGetHandbookInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2183" name="AnimalHandbookGetHandbookInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2184" name="AnimalHandbookGetColonyState_C2S_Msg" serverName="gamesvr"/>
    <entry id="2185" name="AnimalHandbookGetColonyState_S2C_Msg" serverName="gamesvr"/>
    <entry id="2186" name="AnimalHandbookGetColonyStateNtf" serverName="gamesvr"/>
    <entry id="2187" name="AnimalHandbookStartTrapping_C2S_Msg" serverName="gamesvr"/>
    <entry id="2188" name="AnimalHandbookStartTrapping_S2C_Msg" serverName="gamesvr"/>
    <entry id="2189" name="AnimalHandbookCaptureAnimal_C2S_Msg" serverName="gamesvr"/>
    <entry id="2190" name="AnimalHandbookCaptureAnimal_S2C_Msg" serverName="gamesvr"/>
    <entry id="2191" name="AnimalHandbookGenerateGiveAnimalInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2192" name="AnimalHandbookGenerateGiveAnimalInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2193" name="ResGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2194" name="ResGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2195" name="OpenSecondaryPassword_C2S_Msg" serverName="gamesvr"/>
    <entry id="2196" name="OpenSecondaryPassword_S2C_Msg" serverName="gamesvr"/>
    <entry id="2197" name="ChangeSecondaryPassword_C2S_Msg" serverName="gamesvr"/>
    <entry id="2198" name="ChangeSecondaryPassword_S2C_Msg" serverName="gamesvr"/>
    <entry id="2199" name="CloseSecondaryPassword_C2S_Msg" serverName="gamesvr"/>
    <entry id="2200" name="CloseSecondaryPassword_S2C_Msg" serverName="gamesvr"/>
    <entry id="2201" name="SecondaryPasswordLessOperate_C2S_Msg" serverName="gamesvr"/>
    <entry id="2202" name="SecondaryPasswordLessOperate_S2C_Msg" serverName="gamesvr"/>
    <entry id="2203" name="VerifySecondaryPassword_C2S_Msg" serverName="gamesvr"/>
    <entry id="2204" name="VerifySecondaryPassword_S2C_Msg" serverName="gamesvr"/>
    <entry id="2205" name="ChangeSlotRandomState_C2S_Msg" serverName="gamesvr"/>
    <entry id="2206" name="ChangeSlotRandomState_S2C_Msg" serverName="gamesvr"/>
    <entry id="2207" name="GamePlayPufferInfoNtf" serverName="gamesvr"/>
    <entry id="2208" name="GamePlayResPatchNtf" serverName="gamesvr"/>
    <entry id="2210" name="ChangeUgcLayerId_C2S_Msg" serverName="gamesvr"/>
    <entry id="2211" name="ChangeUgcLayerId_S2C_Msg" serverName="gamesvr"/>
    <entry id="2212" name="ActivityClubChallengeStarLightInfoNtf" serverName="gamesvr"/>
    <entry id="2213" name="UgcGetPlayMapFriends_C2S_Msg" serverName="gamesvr"/>
    <entry id="2214" name="UgcGetPlayMapFriends_S2C_Msg" serverName="gamesvr"/>
    <entry id="2217" name="RemoveUgcLayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="2218" name="RemoveUgcLayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="2219" name="AnimalHandbookGetSpeciesCompleteReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2220" name="AnimalHandbookGetSpeciesCompleteReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2221" name="AnimalHandbookGetUltimatePrize_C2S_Msg" serverName="gamesvr"/>
    <entry id="2222" name="AnimalHandbookGetUltimatePrize_S2C_Msg" serverName="gamesvr"/>
    <entry id="2223" name="RemoveWeekSettleBubble_C2S_Msg" serverName="gamesvr"/>
    <entry id="2224" name="RemoveWeekSettleBubble_S2C_Msg" serverName="gamesvr"/>
    <entry id="2225" name="WeekSettleBubbleNtf" serverName="gamesvr"/>
    <entry id="2226" name="StrangerRecommendLabel_C2S_Msg" serverName="gamesvr"/>
    <entry id="2227" name="StrangerRecommendLabel_S2C_Msg" serverName="gamesvr"/>
    <entry id="2228" name="AcceptBPAllRewards_C2S_Msg" serverName="gamesvr"/>
    <entry id="2229" name="AcceptBPAllRewards_S2C_Msg" serverName="gamesvr"/>
    <entry id="2230" name="UpgradeBPPay_C2S_Msg" serverName="gamesvr"/>
    <entry id="2231" name="UpgradeBPPay_S2C_Msg" serverName="gamesvr"/>
    <entry id="2232" name="PurchaseBPLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="2233" name="PurchaseBPLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="2234" name="ListBPSummary_C2S_Msg" serverName="gamesvr"/>
    <entry id="2235" name="ListBPSummary_S2C_Msg" serverName="gamesvr"/>
    <entry id="2236" name="ListBPPreview_C2S_Msg" serverName="gamesvr"/>
    <entry id="2237" name="ListBPPreview_S2C_Msg" serverName="gamesvr"/>
    <entry id="2247" name="AnimalHandbookGetGiveAndReceiveHistory_C2S_Msg" serverName="gamesvr"/>
    <entry id="2248" name="AnimalHandbookGetGiveAndReceiveHistory_S2C_Msg" serverName="gamesvr"/>
    <entry id="2249" name="AnimalHandbookAnimalItemGetNtf" serverName="gamesvr"/>
    <entry id="2250" name="BroadcastNoticeNtf" serverName="gamesvr"/>
    <entry id="2251" name="UpdateForesightActivitySubscribe_C2S_Msg" serverName="gamesvr"/>
    <entry id="2252" name="UpdateForesightActivitySubscribe_S2C_Msg" serverName="gamesvr"/>
    <entry id="2253" name="ActivityMarkPlayerInvited_C2S_Msg" serverName="gamesvr"/>
    <entry id="2254" name="ActivityMarkPlayerInvited_S2C_Msg" serverName="gamesvr"/>
    <entry id="2255" name="DeleteGiveRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="2256" name="DeleteGiveRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="2257" name="OperateAddIntimateRecommendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2258" name="OperateAddIntimateRecommendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2259" name="GetIntimateRelationMotion_C2S_Msg" serverName="gamesvr"/>
    <entry id="2260" name="GetIntimateRelationMotion_S2C_Msg" serverName="gamesvr"/>
    <entry id="2261" name="SetIntimateRelationMotion_C2S_Msg" serverName="gamesvr"/>
    <entry id="2262" name="SetIntimateRelationMotion_S2C_Msg" serverName="gamesvr"/>
    <entry id="2263" name="GetFriendDailyInteractData_C2S_Msg" serverName="gamesvr"/>
    <entry id="2264" name="GetFriendDailyInteractData_S2C_Msg" serverName="gamesvr"/>
    <entry id="2265" name="CupsChangeNtf" serverName="gamesvr"/>
    <entry id="2266" name="CupsItemAddNtf" serverName="gamesvr"/>
    <entry id="2267" name="FarmExchangeCoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="2268" name="FarmExchangeCoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="2269" name="FarmSendGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="2270" name="FarmSendGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="2271" name="FarmPickGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="2272" name="FarmPickGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="2273" name="FarmHandbookAwardGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2274" name="FarmHandbookAwardGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2277" name="BPBriefNtf" serverName="gamesvr"/>
    <entry id="2278" name="FetchBPLevelRewards_C2S_Msg" serverName="gamesvr"/>
    <entry id="2279" name="FetchBPLevelRewards_S2C_Msg" serverName="gamesvr"/>
    <entry id="2280" name="AcceptBPRewards_C2S_Msg" serverName="gamesvr"/>
    <entry id="2281" name="AcceptBPRewards_S2C_Msg" serverName="gamesvr"/>
    <entry id="2282" name="UgcAppreciateGetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="2283" name="UgcAppreciateGetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="2284" name="UgcAppreciateGetMaps_C2S_Msg" serverName="gamesvr"/>
    <entry id="2285" name="UgcAppreciateGetMaps_S2C_Msg" serverName="gamesvr"/>
    <entry id="2286" name="UgcAppreciateMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="2287" name="UgcAppreciateMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="2288" name="WolfkillToMasterReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2289" name="WolfkillToMasterReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2290" name="GamePlayInfoSync_C2S_Msg" serverName="gamesvr"/>
    <entry id="2291" name="GamePlayInfoSync_S2C_Msg" serverName="gamesvr"/>
    <entry id="2292" name="FarmBuildingSetSkin_C2S_Msg" serverName="gamesvr"/>
    <entry id="2293" name="FarmBuildingSetSkin_S2C_Msg" serverName="gamesvr"/>
    <entry id="2294" name="FarmQueryFunctionOpenTime_C2S_Msg" serverName="gamesvr"/>
    <entry id="2295" name="FarmQueryFunctionOpenTime_S2C_Msg" serverName="gamesvr"/>
    <entry id="2296" name="FarmFishCardPackOpenNtf" serverName="gamesvr"/>
    <entry id="2297" name="FarmSetFishBowl_C2S_Msg" serverName="gamesvr"/>
    <entry id="2298" name="FarmSetFishBowl_S2C_Msg" serverName="gamesvr"/>
    <entry id="2299" name="FarmFishBowlUnlock_C2S_Msg" serverName="gamesvr"/>
    <entry id="2300" name="FarmFishBowlUnlock_S2C_Msg" serverName="gamesvr"/>
    <entry id="2301" name="DataStoreKvFragmentNtf" serverName="gamesvr"/>
    <entry id="2302" name="GetDataStoreByFunc_C2S_Msg" serverName="gamesvr"/>
    <entry id="2303" name="GetDataStoreByFunc_S2C_Msg" serverName="gamesvr"/>
    <entry id="2304" name="UploadClientLogNtf" serverName="gamesvr"/>
    <entry id="2305" name="FarmStatusRemindNtf" serverName="gamesvr"/>
    <entry id="2306" name="RaffleChooseReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2307" name="RaffleChooseReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2308" name="RaffleListReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2309" name="RaffleListReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2310" name="GetArenaGameInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2311" name="GetArenaGameInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2312" name="ArenaSelectFrame_C2S_Msg" serverName="gamesvr"/>
    <entry id="2313" name="ArenaSelectFrame_S2C_Msg" serverName="gamesvr"/>
    <entry id="2314" name="ArenaSelectVoiceStyle_C2S_Msg" serverName="gamesvr"/>
    <entry id="2315" name="ArenaSelectVoiceStyle_S2C_Msg" serverName="gamesvr"/>
    <entry id="2316" name="ArenaSelectEquip_C2S_Msg" serverName="gamesvr"/>
    <entry id="2317" name="ArenaSelectEquip_S2C_Msg" serverName="gamesvr"/>
    <entry id="2318" name="ArenaGetItemsNtf" serverName="gamesvr"/>
    <entry id="2319" name="ArenaHeroInfoNtf" serverName="gamesvr"/>
    <entry id="2320" name="ArenaCardPackResultNtf" serverName="gamesvr"/>
    <entry id="2321" name="ArenaCardPackResultCommit_C2S_Msg" serverName="gamesvr"/>
    <entry id="2322" name="ArenaCardPackResultCommit_S2C_Msg" serverName="gamesvr"/>
    <entry id="2323" name="ArenaBuyHero_C2S_Msg" serverName="gamesvr"/>
    <entry id="2324" name="ArenaBuyHero_S2C_Msg" serverName="gamesvr"/>
    <entry id="2325" name="ArenaSelectCard_C2S_Msg" serverName="gamesvr"/>
    <entry id="2326" name="ArenaSelectCard_S2C_Msg" serverName="gamesvr"/>
    <entry id="2327" name="ArenaAddNewCardNtf" serverName="gamesvr"/>
    <entry id="2328" name="UgcPlayerPublicSyncStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="2329" name="UgcPlayerPublicSyncStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="2330" name="UgcPlayerPublicSyncEnd_C2S_Msg" serverName="gamesvr"/>
    <entry id="2331" name="UgcPlayerPublicSyncEnd_S2C_Msg" serverName="gamesvr"/>
    <entry id="2332" name="UgcPlayerPublicHeartbeat_C2S_Msg" serverName="gamesvr"/>
    <entry id="2333" name="UgcPlayerPublicHeartbeat_S2C_Msg" serverName="gamesvr"/>
    <entry id="2334" name="GetUgcPlayerPublicAllAttrs_C2S_Msg" serverName="gamesvr"/>
    <entry id="2335" name="GetUgcPlayerPublicAllAttrs_S2C_Msg" serverName="gamesvr"/>
    <entry id="2336" name="ModifyUgcPlayerPublicAttr_C2S_Msg" serverName="gamesvr"/>
    <entry id="2337" name="ModifyUgcPlayerPublicAttr_S2C_Msg" serverName="gamesvr"/>
    <entry id="2338" name="GetUgcPlayerPublicArrayAttr_C2S_Msg" serverName="gamesvr"/>
    <entry id="2339" name="GetUgcPlayerPublicArrayAttr_S2C_Msg" serverName="gamesvr"/>
    <entry id="2340" name="AppendUgcPlayerPublicArrayAttr_C2S_Msg" serverName="gamesvr"/>
    <entry id="2341" name="AppendUgcPlayerPublicArrayAttr_S2C_Msg" serverName="gamesvr"/>
    <entry id="2342" name="BatchGetUgcPlayerPublicAttrs_C2S_Msg" serverName="gamesvr"/>
    <entry id="2343" name="BatchGetUgcPlayerPublicAttrs_S2C_Msg" serverName="gamesvr"/>
    <entry id="2344" name="TeamSetBackgroundTheme_C2S_Msg" serverName="gamesvr"/>
    <entry id="2345" name="TeamSetBackgroundTheme_S2C_Msg" serverName="gamesvr"/>
    <entry id="2346" name="ReceiveCupsStageReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2347" name="ReceiveCupsStageReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2348" name="AnimalHandbookAnimalItemRemoveNtf" serverName="gamesvr"/>
    <entry id="2349" name="WolfKillRoomTimeLimitStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="2350" name="WolfKillRoomTimeLimitStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="2351" name="UgcPlayerPublicAttrModifyNtf" serverName="gamesvr"/>
    <entry id="2352" name="UgcPlayerPublicHeartbeatTimeoutNtf" serverName="gamesvr"/>
    <entry id="2353" name="RecruitOrderRewardReceive_C2S_Msg" serverName="gamesvr"/>
    <entry id="2354" name="RecruitOrderRewardReceive_S2C_Msg" serverName="gamesvr"/>
    <entry id="2355" name="AiNpcFeedBack_C2S_Msg" serverName="gamesvr"/>
    <entry id="2356" name="AiNpcFeedBack_S2C_Msg" serverName="gamesvr"/>
    <entry id="2357" name="LuckyFriendSelectTask_C2S_Msg" serverName="gamesvr"/>
    <entry id="2358" name="LuckyFriendSelectTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="2359" name="LuckyFriendApplyFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="2360" name="LuckyFriendApplyFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="2361" name="LuckyFriendOperateFriendTaskApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="2362" name="LuckyFriendOperateFriendTaskApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="2363" name="LuckyFriendNoticeTask_S2C_Msg" serverName="gamesvr"/>
    <entry id="2364" name="LuckyFriendGetTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2365" name="LuckyFriendGetTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2366" name="FarmRainBowBuffIconNtf" serverName="gamesvr"/>
    <entry id="2367" name="GameSceneReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="2368" name="GameSceneReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="2369" name="SuperLinearActivityConfig_C2S_Msg" serverName="gamesvr"/>
    <entry id="2370" name="SuperLinearActivityConfig_S2C_Msg" serverName="gamesvr"/>
    <entry id="2371" name="UgcTakeOffApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="2372" name="UgcTakeOffApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="2373" name="HideAndSeekLevelPropData_C2S_Msg" serverName="gamesvr"/>
    <entry id="2374" name="HideAndSeekLevelPropData_S2C_Msg" serverName="gamesvr"/>
    <entry id="2375" name="UnlockIntimateRelationExtraCnt_C2S_Msg" serverName="gamesvr"/>
    <entry id="2376" name="UnlockIntimateRelationExtraCnt_S2C_Msg" serverName="gamesvr"/>
    <entry id="2377" name="LuckyFriendTaskNotice_C2S_Msg" serverName="gamesvr"/>
    <entry id="2378" name="LuckyFriendTaskNotice_S2C_Msg" serverName="gamesvr"/>
    <entry id="2379" name="UgcGetBadgeNtf" serverName="gamesvr"/>
    <entry id="2380" name="BattleRecruitPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="2381" name="BattleRecruitPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="2382" name="BattleRecruitUnPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="2383" name="BattleRecruitUnPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="2384" name="BattleDirectJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="2385" name="BattleDirectJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="2386" name="ArenaHeroUnlockData_C2S_Msg" serverName="gamesvr"/>
    <entry id="2387" name="ArenaHeroUnlockData_S2C_Msg" serverName="gamesvr"/>
    <entry id="2390" name="UgcGetCommunityUnreadCount_C2S_Msg" serverName="gamesvr"/>
    <entry id="2391" name="UgcGetCommunityUnreadCount_S2C_Msg" serverName="gamesvr"/>
    <entry id="2392" name="ActivityLotteryCfg_C2S_Msg" serverName="gamesvr"/>
    <entry id="2393" name="ActivityLotteryCfg_S2C_Msg" serverName="gamesvr"/>
    <entry id="2394" name="GetRechargeLevelInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2395" name="GetRechargeLevelInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2396" name="GetMonthCardRewardsInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2397" name="GetMonthCardRewardsInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2398" name="ReturnActivitySetTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2399" name="ReturnActivitySetTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2400" name="ReturnActivityMarkCalendar_C2S_Msg" serverName="gamesvr"/>
    <entry id="2401" name="ReturnActivityMarkCalendar_S2C_Msg" serverName="gamesvr"/>
    <entry id="2402" name="LuckyFriendApplyTaskNtf" serverName="gamesvr"/>
    <entry id="2403" name="LuckyFriendTaskStartNtf" serverName="gamesvr"/>
    <entry id="2404" name="ChangeDefaultDress_C2S_Msg" serverName="gamesvr"/>
    <entry id="2405" name="ChangeDefaultDress_S2C_Msg" serverName="gamesvr"/>
    <entry id="2406" name="LuckyFriendTaskNtf" serverName="gamesvr"/>
    <entry id="2407" name="ActivityBuyMidasProduct_C2S_Msg" serverName="gamesvr"/>
    <entry id="2408" name="ActivityBuyMidasProduct_S2C_Msg" serverName="gamesvr"/>
    <entry id="2409" name="ActivityBuyMidasProductNtf" serverName="gamesvr"/>
    <entry id="2410" name="WolfKillAnnouncement_C2S_Msg" serverName="gamesvr"/>
    <entry id="2411" name="WolfKillAnnouncement_S2C_Msg" serverName="gamesvr"/>
    <entry id="2412" name="WolfKillGetFeedbackCount_C2S_Msg" serverName="gamesvr"/>
    <entry id="2413" name="WolfKillGetFeedbackCount_S2C_Msg" serverName="gamesvr"/>
    <entry id="2414" name="WolfKillAddFeedback_C2S_Msg" serverName="gamesvr"/>
    <entry id="2415" name="WolfKillAddFeedback_S2C_Msg" serverName="gamesvr"/>
    <entry id="2416" name="ClubLog_C2S_Msg" serverName="gamesvr"/>
    <entry id="2417" name="ClubLog_S2C_Msg" serverName="gamesvr"/>
    <entry id="2418" name="ResGetV2_C2S_Msg" serverName="gamesvr"/>
    <entry id="2419" name="ResGetV2_S2C_Msg" serverName="gamesvr"/>
    <entry id="2420" name="ResGetV2Ntf" serverName="gamesvr"/>
    <entry id="2421" name="ThemeAdventureRewardNtf" serverName="gamesvr"/>
    <entry id="2422" name="ThemeAdventureOpenMysteryBox_C2S_Msg" serverName="gamesvr"/>
    <entry id="2423" name="ThemeAdventureOpenMysteryBox_S2C_Msg" serverName="gamesvr"/>
    <entry id="2424" name="ThemeActivityGetDailyTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="2425" name="ThemeActivityGetDailyTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="2426" name="RaffleGainFreeDraw_C2S_Msg" serverName="gamesvr"/>
    <entry id="2427" name="RaffleGainFreeDraw_S2C_Msg" serverName="gamesvr"/>
    <entry id="2428" name="ActBookOfFriendsExpense_C2S_Msg" serverName="gamesvr"/>
    <entry id="2429" name="ActBookOfFriendsExpense_S2C_Msg" serverName="gamesvr"/>
    <entry id="2430" name="ActBookOfFriendsExpenseNtf" serverName="gamesvr"/>
    <entry id="2431" name="UgcMatchLobbyDetailGetOne_C2S_Msg" serverName="gamesvr"/>
    <entry id="2432" name="UgcMatchLobbyDetailGetOne_S2C_Msg" serverName="gamesvr"/>
    <entry id="2433" name="BattleRecruitJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="2434" name="BattleRecruitJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="2435" name="BIRecommendScenePackage_C2S_Msg" serverName="gamesvr"/>
    <entry id="2436" name="BIRecommendScenePackage_S2C_Msg" serverName="gamesvr"/>
    <entry id="2635" name="WolfKillSeasonRewardRecv_C2S_Msg" serverName="gamesvr"/>
    <entry id="2636" name="WolfKillSeasonRewardRecv_S2C_Msg" serverName="gamesvr"/>
    <entry id="2637" name="AppointmentActivityGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2638" name="AppointmentActivityGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2639" name="BurdenReduceTaskOverviewActivityGetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2640" name="BurdenReduceTaskOverviewActivityGetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2641" name="AppointmentActivityMake_C2S_Msg" serverName="gamesvr"/>
    <entry id="2642" name="AppointmentActivityMake_S2C_Msg" serverName="gamesvr"/>
    <entry id="2643" name="ActivityRecommendRecharge_C2S_Msg" serverName="gamesvr"/>
    <entry id="2644" name="ActivityRecommendRecharge_S2C_Msg" serverName="gamesvr"/>
    <entry id="2645" name="ABTestInfoNtf" serverName="gamesvr"/>
    <entry id="2646" name="BattleCommonBroadcast_C2S_Msg" serverName="gamesvr"/>
    <entry id="2647" name="BattleCommonBroadcast_S2C_Msg" serverName="gamesvr"/>
    <entry id="2648" name="BattleBroadcastInfoNtf" serverName="gamesvr"/>
    <entry id="2649" name="QuickCommercialConfInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2650" name="QuickCommercialConfInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2651" name="QuickCommercialConfInfoNtf" serverName="gamesvr"/>
    <entry id="2652" name="UgcApplyCoverUrl_C2S_Msg" serverName="gamesvr"/>
    <entry id="2653" name="UgcApplyCoverUrl_S2C_Msg" serverName="gamesvr"/>
    <entry id="2654" name="BattleMemberListGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2655" name="BattleMemberListGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2656" name="GetFollowerList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2657" name="GetFollowerList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2658" name="RecruitOrderConfirm_C2S_Msg" serverName="gamesvr"/>
    <entry id="2659" name="RecruitOrderConfirm_S2C_Msg" serverName="gamesvr"/>
    <entry id="2660" name="ArenaSelectHeroSkin_C2S_Msg" serverName="gamesvr"/>
    <entry id="2661" name="ArenaSelectHeroSkin_S2C_Msg" serverName="gamesvr"/>
    <entry id="2668" name="RecruitOrderCodeQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="2669" name="RecruitOrderCodeQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="2672" name="GamePakInfoReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="2673" name="GamePakInfoReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="2676" name="ArenaForwardCSMsgTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="2677" name="ArenaForwardCSMsgTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="2680" name="IAAForceGuideAppNtf" serverName="gamesvr"/>
    <entry id="2681" name="IAAForceGuideAppReset_C2S_Msg" serverName="gamesvr"/>
    <entry id="2682" name="IAAForceGuideAppReset_S2C_Msg" serverName="gamesvr"/>
    <entry id="2683" name="SetTeamShowBackgroundTheme_C2S_Msg" serverName="gamesvr"/>
    <entry id="2684" name="SetTeamShowBackgroundTheme_S2C_Msg" serverName="gamesvr"/>
    <entry id="2685" name="NewFollowerNtf" serverName="gamesvr"/>
    <entry id="2704" name="HouseCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="2705" name="HouseCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="2706" name="HouseEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="2707" name="HouseEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="2708" name="HouseExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="2709" name="HouseExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="2710" name="HouseKickNtf" serverName="gamesvr"/>
    <entry id="2711" name="HouseDSInfoNtf" serverName="gamesvr"/>
    <entry id="2712" name="HouseAttrNtf" serverName="gamesvr"/>
    <entry id="2713" name="HouseOwnerAttrNtf" serverName="gamesvr"/>
    <entry id="2714" name="HouseDecorate_C2S_Msg" serverName="gamesvr"/>
    <entry id="2715" name="HouseDecorate_S2C_Msg" serverName="gamesvr"/>
    <entry id="2716" name="HouseExtend_C2S_Msg" serverName="gamesvr"/>
    <entry id="2717" name="HouseExtend_S2C_Msg" serverName="gamesvr"/>
    <entry id="2718" name="HouseForwardToDS_C2S_Msg" serverName="gamesvr"/>
    <entry id="2719" name="HouseForwardToDS_S2C_Msg" serverName="gamesvr"/>
    <entry id="2722" name="HouseFurnitureBuy_C2S_Msg" serverName="gamesvr"/>
    <entry id="2723" name="HouseFurnitureBuy_S2C_Msg" serverName="gamesvr"/>
    <entry id="2724" name="HouseFurnitureSell_C2S_Msg" serverName="gamesvr"/>
    <entry id="2725" name="HouseFurnitureSell_S2C_Msg" serverName="gamesvr"/>
    <entry id="2729" name="HouseCropMachinePutIn_C2S_Msg" serverName="gamesvr"/>
    <entry id="2730" name="HouseCropMachinePutIn_S2C_Msg" serverName="gamesvr"/>
    <entry id="2731" name="HouseCropMachineTakeOut_C2S_Msg" serverName="gamesvr"/>
    <entry id="2732" name="HouseCropMachineTakeOut_S2C_Msg" serverName="gamesvr"/>
    <entry id="2733" name="FarmOpenAquariumMode_C2S_Msg" serverName="gamesvr"/>
    <entry id="2734" name="FarmOpenAquariumMode_S2C_Msg" serverName="gamesvr"/>
    <entry id="2735" name="FarmFetchFunctionOpenTime_C2S_Msg" serverName="gamesvr"/>
    <entry id="2736" name="FarmFetchFunctionOpenTime_S2C_Msg" serverName="gamesvr"/>
    <entry id="2737" name="FarmUnlockAquariumSeat_C2S_Msg" serverName="gamesvr"/>
    <entry id="2738" name="FarmUnlockAquariumSeat_S2C_Msg" serverName="gamesvr"/>
    <entry id="2739" name="FarmSetAquarium_C2S_Msg" serverName="gamesvr"/>
    <entry id="2740" name="FarmSetAquarium_S2C_Msg" serverName="gamesvr"/>
    <entry id="2741" name="FarmGetAquariumBenefit_C2S_Msg" serverName="gamesvr"/>
    <entry id="2742" name="FarmGetAquariumBenefit_S2C_Msg" serverName="gamesvr"/>
    <entry id="2743" name="HouseRecommendStranger_C2S_Msg" serverName="gamesvr"/>
    <entry id="2744" name="HouseRecommendStranger_S2C_Msg" serverName="gamesvr"/>
    <entry id="2745" name="BattlePartnerCoMatchProposal_C2S_Msg" serverName="gamesvr"/>
    <entry id="2746" name="BattlePartnerCoMatchProposal_S2C_Msg" serverName="gamesvr"/>
    <entry id="2747" name="BattlePartnerCoMatchInfoNtf" serverName="gamesvr"/>
    <entry id="2748" name="BattlePartnerCoMatchConfirm_C2S_Msg" serverName="gamesvr"/>
    <entry id="2749" name="BattlePartnerCoMatchConfirm_S2C_Msg" serverName="gamesvr"/>
    <entry id="2750" name="FarmPetEvictDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="2751" name="FarmPetEvictDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="2752" name="FarmPetEvict_C2S_Msg" serverName="gamesvr"/>
    <entry id="2753" name="FarmPetEvict_S2C_Msg" serverName="gamesvr"/>
    <entry id="2754" name="TestRaffle_C2S_Msg" serverName="gamesvr"/>
    <entry id="2755" name="TestRaffle_S2C_Msg" serverName="gamesvr"/>
    <entry id="2756" name="ConfirmTestRaffle_C2S_Msg" serverName="gamesvr"/>
    <entry id="2757" name="ConfirmTestRaffle_S2C_Msg" serverName="gamesvr"/>
    <entry id="2758" name="QqCloudGameAuth_C2S_Msg" serverName="gamesvr"/>
    <entry id="2759" name="QqCloudGameAuth_S2C_Msg" serverName="gamesvr"/>
    <entry id="2760" name="QqCloudGameNeedAuthNtf" serverName="gamesvr"/>
    <entry id="2761" name="RechargeLuckyTurntable_C2S_Msg" serverName="gamesvr"/>
    <entry id="2762" name="RechargeLuckyTurntable_S2C_Msg" serverName="gamesvr"/>
    <entry id="2765" name="RechargeLuckyTurntableRewardNtf" serverName="gamesvr"/>
    <entry id="2766" name="JoinCommunityChannel_C2S_Msg" serverName="gamesvr"/>
    <entry id="2767" name="JoinCommunityChannel_S2C_Msg" serverName="gamesvr"/>
    <entry id="2768" name="QuitCommunityChannel_C2S_Msg" serverName="gamesvr"/>
    <entry id="2769" name="QuitCommunityChannel_S2C_Msg" serverName="gamesvr"/>
    <entry id="2770" name="StickCommunityChannel_C2S_Msg" serverName="gamesvr"/>
    <entry id="2771" name="StickCommunityChannel_S2C_Msg" serverName="gamesvr"/>
    <entry id="2772" name="SelectIconCommunityChannel_C2S_Msg" serverName="gamesvr"/>
    <entry id="2773" name="SelectIconCommunityChannel_S2C_Msg" serverName="gamesvr"/>
    <entry id="2774" name="CommunityChannelEntranceNoShow_C2S_Msg" serverName="gamesvr"/>
    <entry id="2775" name="CommunityChannelEntranceNoShow_S2C_Msg" serverName="gamesvr"/>
    <entry id="2782" name="FarmBuildingBatchSell_C2S_Msg" serverName="gamesvr"/>
    <entry id="2783" name="FarmBuildingBatchSell_S2C_Msg" serverName="gamesvr"/>
    <entry id="2787" name="WolfKillNewUserTips_C2S_Msg" serverName="gamesvr"/>
    <entry id="2788" name="WolfKillNewUserTips_S2C_Msg" serverName="gamesvr"/>
    <entry id="2795" name="PlayDetailPageRoomQueryByPlayGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="2796" name="PlayDetailPageRoomQueryByPlayGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="2799" name="UgcAchievementConfigEdit_C2S_Msg" serverName="gamesvr"/>
    <entry id="2800" name="UgcAchievementConfigEdit_S2C_Msg" serverName="gamesvr"/>
    <entry id="2801" name="UgcAchievementConfigGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2802" name="UgcAchievementConfigGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2807" name="PlayerAlbumPictureGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2808" name="PlayerAlbumPictureGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2809" name="PlayerAlbumPictureFormatConvert_C2S_Msg" serverName="gamesvr"/>
    <entry id="2810" name="PlayerAlbumPictureFormatConvert_S2C_Msg" serverName="gamesvr"/>
    <entry id="2811" name="ActivityWeekendIceBrokenInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2812" name="ActivityWeekendIceBrokenInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2813" name="MallDemandDeliverGoodsNtf" serverName="gamesvr"/>
    <entry id="2814" name="FarmSceneDropPick_C2S_Msg" serverName="gamesvr"/>
    <entry id="2815" name="FarmSceneDropPick_S2C_Msg" serverName="gamesvr"/>
    <entry id="2816" name="FarmPetFeed_C2S_Msg" serverName="gamesvr"/>
    <entry id="2817" name="FarmPetFeed_S2C_Msg" serverName="gamesvr"/>
    <entry id="2818" name="FarmPetHouseKeepingSwitch_C2S_Msg" serverName="gamesvr"/>
    <entry id="2819" name="FarmPetHouseKeepingSwitch_S2C_Msg" serverName="gamesvr"/>
    <entry id="2820" name="FarmPetSetClientCache_C2S_Msg" serverName="gamesvr"/>
    <entry id="2821" name="FarmPetSetClientCache_S2C_Msg" serverName="gamesvr"/>
    <entry id="2822" name="BattleEndSettlement_C2S_Msg" serverName="gamesvr"/>
    <entry id="2823" name="BattleEndSettlement_S2C_Msg" serverName="gamesvr"/>
    <entry id="2824" name="RoomCoMatchReady_C2S_Msg" serverName="gamesvr"/>
    <entry id="2825" name="RoomCoMatchReady_S2C_Msg" serverName="gamesvr"/>
    <entry id="2826" name="HouseGetCurrentRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="2827" name="HouseGetCurrentRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="2837" name="EditShowData_C2S_Msg" serverName="gamesvr"/>
    <entry id="2838" name="EditShowData_S2C_Msg" serverName="gamesvr"/>
    <entry id="2839" name="HouseSetClientKV_C2S_Msg" serverName="gamesvr"/>
    <entry id="2840" name="HouseSetClientKV_S2C_Msg" serverName="gamesvr"/>
    <entry id="2842" name="HouseItemInteract_C2S_Msg" serverName="gamesvr"/>
    <entry id="2843" name="HouseItemInteract_S2C_Msg" serverName="gamesvr"/>
    <entry id="2848" name="FarmEndEvent_C2S_Msg" serverName="gamesvr"/>
    <entry id="2849" name="FarmEndEvent_S2C_Msg" serverName="gamesvr"/>
    <entry id="2854" name="AiNpcDressSave_C2S_Msg" serverName="gamesvr"/>
    <entry id="2855" name="AiNpcDressSave_S2C_Msg" serverName="gamesvr"/>
    <entry id="2860" name="ReputationScoreChangeInfoNtf" serverName="gamesvr"/>
    <entry id="2861" name="GetTargetUgcPlayerData_C2S_Msg" serverName="gamesvr"/>
    <entry id="2862" name="GetTargetUgcPlayerData_S2C_Msg" serverName="gamesvr"/>
    <entry id="2863" name="PlayerAlbumPictureOperateNtf" serverName="gamesvr"/>
    <entry id="2864" name="DecomposeItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="2865" name="DecomposeItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="2872" name="ArenaSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="2873" name="ArenaSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="2874" name="GetQualifiedActivityMarqueeId_C2S_Msg" serverName="gamesvr"/>
    <entry id="2875" name="GetQualifiedActivityMarqueeId_S2C_Msg" serverName="gamesvr"/>
    <entry id="2876" name="GetShowData_C2S_Msg" serverName="gamesvr"/>
    <entry id="2877" name="GetShowData_S2C_Msg" serverName="gamesvr"/>
    <entry id="2878" name="ActivityPrayerCardInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="2879" name="ActivityPrayerCardInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="2880" name="ActivityPrayerCardGive_C2S_Msg" serverName="gamesvr"/>
    <entry id="2881" name="ActivityPrayerCardGive_S2C_Msg" serverName="gamesvr"/>
    <entry id="2882" name="ActivityPrayerCardLook_C2S_Msg" serverName="gamesvr"/>
    <entry id="2883" name="ActivityPrayerCardLook_S2C_Msg" serverName="gamesvr"/>
    <entry id="2884" name="FarmWeatherEffectNtf" serverName="gamesvr"/>
    <entry id="2886" name="FarmWeatherTrigger_C2S_Msg" serverName="gamesvr"/>
    <entry id="2887" name="FarmWeatherTrigger_S2C_Msg" serverName="gamesvr"/>
    <entry id="2888" name="RefreshRaffle_C2S_Msg" serverName="gamesvr"/>
    <entry id="2889" name="RefreshRaffle_S2C_Msg" serverName="gamesvr"/>
    <entry id="2890" name="RefreshRaffleNtf" serverName="gamesvr"/>
    <entry id="2893" name="FarmPetInteract_C2S_Msg" serverName="gamesvr"/>
    <entry id="2894" name="FarmPetInteract_S2C_Msg" serverName="gamesvr"/>
    <entry id="2895" name="FarmPetFertilize_C2S_Msg" serverName="gamesvr"/>
    <entry id="2896" name="FarmPetFertilize_S2C_Msg" serverName="gamesvr"/>
    <entry id="2901" name="PlayerLevelEstimation_C2S_Msg" serverName="gamesvr"/>
    <entry id="2902" name="PlayerLevelEstimation_S2C_Msg" serverName="gamesvr"/>
    <entry id="2903" name="PlayerPictureLike_C2S_Msg" serverName="gamesvr"/>
    <entry id="2904" name="PlayerPictureLike_S2C_Msg" serverName="gamesvr"/>
    <entry id="2905" name="PlayerPictureLikeHis_C2S_Msg" serverName="gamesvr"/>
    <entry id="2906" name="PlayerPictureLikeHis_S2C_Msg" serverName="gamesvr"/>
    <entry id="2907" name="FarmLightningStrikeReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="2908" name="FarmLightningStrikeReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="2909" name="UpdateFavoriteItems_C2S_Msg" serverName="gamesvr"/>
    <entry id="2910" name="UpdateFavoriteItems_S2C_Msg" serverName="gamesvr"/>
    <entry id="2911" name="UgcRecommendSet_C2S_Msg" serverName="gamesvr"/>
    <entry id="2912" name="UgcRecommendSet_S2C_Msg" serverName="gamesvr"/>
    <entry id="2913" name="GetAllInfoForLevelEstimation_C2S_Msg" serverName="gamesvr"/>
    <entry id="2914" name="GetAllInfoForLevelEstimation_S2C_Msg" serverName="gamesvr"/>
    <entry id="2915" name="GetInfoForLevelEstimation_C2S_Msg" serverName="gamesvr"/>
    <entry id="2916" name="GetInfoForLevelEstimation_S2C_Msg" serverName="gamesvr"/>
    <entry id="2917" name="FarmPetGiftReceive_C2S_Msg" serverName="gamesvr"/>
    <entry id="2918" name="FarmPetGiftReceive_S2C_Msg" serverName="gamesvr"/>
    <entry id="2924" name="RechargeActivityList_C2S_Msg" serverName="gamesvr"/>
    <entry id="2925" name="RechargeActivityList_S2C_Msg" serverName="gamesvr"/>
    <entry id="2968" name="FarmResetAquarium_C2S_Msg" serverName="gamesvr"/>
    <entry id="2969" name="FarmResetAquarium_S2C_Msg" serverName="gamesvr"/>
    <entry id="2970" name="StreamUgcAiEditAssistantChat_C2S_Msg" serverName="streamsvr"/>
    <entry id="2971" name="StreamUgcAiEditAssistantChat_S2C_Msg" serverName="streamsvr"/>
    <entry id="2973" name="StreamUgcAiEditAssistantClose_C2S_Msg" serverName="streamsvr"/>
    <entry id="2974" name="StreamUgcAiEditAssistantClose_S2C_Msg" serverName="streamsvr"/>
    <entry id="2975" name="StreamUgcAiEditAssistantCloseResultNtf" serverName="streamsvr"/>
    <entry id="2977" name="StreamUgcAiEditAssistantChatResultNtf" serverName="streamsvr"/>
    <entry id="2978" name="BIRecBanner_C2S_Msg" serverName="gamesvr"/>
    <entry id="2979" name="BIRecBanner_S2C_Msg" serverName="gamesvr"/>
    <entry id="2980" name="Exchange_C2S_Msg" serverName="gamesvr"/>
    <entry id="2981" name="Exchange_S2C_Msg" serverName="gamesvr"/>
    <entry id="2982" name="StreamAiEditAssistantInitResultNtf" serverName="streamsvr"/>
    <entry id="2983" name="StreamUgcAiEditAssistantInit_C2S_Msg" serverName="streamsvr"/>
    <entry id="2984" name="StreamUgcAiEditAssistantInit_S2C_Msg" serverName="streamsvr"/>
    <entry id="2985" name="FarmPetChangeName_C2S_Msg" serverName="gamesvr"/>
    <entry id="2986" name="FarmPetChangeName_S2C_Msg" serverName="gamesvr"/>
    <entry id="2987" name="FarmPetSummon_C2S_Msg" serverName="gamesvr"/>
    <entry id="2988" name="FarmPetSummon_S2C_Msg" serverName="gamesvr"/>
    <entry id="2989" name="FarmFishPoolRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="2990" name="FarmFishPoolRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="2991" name="RoomConfirmEnterNtf" serverName="gamesvr"/>
    <entry id="2992" name="RoomConfirmEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="2993" name="RoomConfirmEnterCancelNtf" serverName="gamesvr"/>
    <entry id="2994" name="RoomStarPInviteEnterGame_C2S_Msg" serverName="gamesvr"/>
    <entry id="2995" name="RoomStarPInviteEnterGame_S2C_Msg" serverName="gamesvr"/>
    <entry id="2996" name="RoomStarPInviteEnterGameAnswer_C2S_Msg" serverName="gamesvr"/>
    <entry id="2997" name="RoomCoMatchInfoNtf" serverName="gamesvr"/>
    <entry id="2998" name="RoomCoMatchPropose_C2S_Msg" serverName="gamesvr"/>
    <entry id="2999" name="RoomCoMatchPropose_S2C_Msg" serverName="gamesvr"/>
    <entry id="3000" name="RoomCoMatchConfirm_C2S_Msg" serverName="gamesvr"/>
    <entry id="3001" name="RoomCoMatchConfirm_S2C_Msg" serverName="gamesvr"/>
    <entry id="3002" name="PlayerAlbumPictureCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="3003" name="PlayerAlbumPictureCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="3004" name="FarmPetDressUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3005" name="FarmPetDressUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3006" name="CommunityChannelGetHotTopicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3007" name="CommunityChannelGetHotTopicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3008" name="CommunityChannelThumbsUpHotTopic_C2S_Msg" serverName="gamesvr"/>
    <entry id="3009" name="CommunityChannelThumbsUpHotTopic_S2C_Msg" serverName="gamesvr"/>
    <entry id="3014" name="LoginDone_C2S_Msg" serverName="gamesvr"/>
    <entry id="3015" name="LoginDone_S2C_Msg" serverName="gamesvr"/>
    <entry id="3016" name="LuckyRebateActivityGetResult_C2S_Msg" serverName="gamesvr"/>
    <entry id="3017" name="LuckyRebateActivityGetResult_S2C_Msg" serverName="gamesvr"/>
    <entry id="3018" name="FarmEventGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3019" name="FarmEventGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3020" name="UpdateUgcLayerName_C2S_Msg" serverName="gamesvr"/>
    <entry id="3021" name="UpdateUgcLayerName_S2C_Msg" serverName="gamesvr"/>
    <entry id="3028" name="PlayerAlbumPictureLikeOperateNtf" serverName="gamesvr"/>
    <entry id="3029" name="PlayerPictureLikeCount_C2S_Msg" serverName="gamesvr"/>
    <entry id="3030" name="PlayerPictureLikeCount_S2C_Msg" serverName="gamesvr"/>
    <entry id="3031" name="FarmEventSaveData_C2S_Msg" serverName="gamesvr"/>
    <entry id="3032" name="FarmEventSaveData_S2C_Msg" serverName="gamesvr"/>
    <entry id="3034" name="WolfKillFace_C2S_Msg" serverName="gamesvr"/>
    <entry id="3035" name="WolfKillFace_S2C_Msg" serverName="gamesvr"/>
    <entry id="3036" name="FarmSelectPool_C2S_Msg" serverName="gamesvr"/>
    <entry id="3037" name="FarmSelectPool_S2C_Msg" serverName="gamesvr"/>
    <entry id="3040" name="PlayerBatchPictureLikeCountGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="3041" name="PlayerBatchPictureLikeCountGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="3044" name="StarPGuideStepFlow_C2S_Msg" serverName="gamesvr"/>
    <entry id="3045" name="StarPGuideStepFlow_S2C_Msg" serverName="gamesvr"/>
    <entry id="3046" name="FarmTalentLevelUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3047" name="FarmTalentLevelUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3048" name="FarmVillagerInteract_C2S_Msg" serverName="gamesvr"/>
    <entry id="3049" name="FarmVillagerInteract_S2C_Msg" serverName="gamesvr"/>
    <entry id="3050" name="FarmVillagerSetClientCache_C2S_Msg" serverName="gamesvr"/>
    <entry id="3051" name="FarmVillagerSetClientCache_S2C_Msg" serverName="gamesvr"/>
    <entry id="3058" name="PlaySwitch_C2S_Msg" serverName="gamesvr"/>
    <entry id="3059" name="PlaySwitch_S2C_Msg" serverName="gamesvr"/>
    <entry id="3060" name="FarmVillagerPresentGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="3061" name="FarmVillagerPresentGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="3062" name="FarmVillagerAcceptGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="3063" name="FarmVillagerAcceptGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="3064" name="GetEntertainmentGuideTaskConfV2_C2S_Msg" serverName="gamesvr"/>
    <entry id="3065" name="GetEntertainmentGuideTaskConfV2_S2C_Msg" serverName="gamesvr"/>
    <entry id="3066" name="FarmGetCropLevelReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3067" name="FarmGetCropLevelReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3068" name="StarPSetPreDelTime_C2S_Msg" serverName="gamesvr"/>
    <entry id="3069" name="StarPSetPreDelTime_S2C_Msg" serverName="gamesvr"/>
    <entry id="3070" name="KonanGetDrawResult_C2S_Msg" serverName="gamesvr"/>
    <entry id="3071" name="KonanGetDrawResult_S2C_Msg" serverName="gamesvr"/>
    <entry id="3072" name="FarmTaskGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3073" name="FarmTaskGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3074" name="FarmTaskSubmitItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="3075" name="FarmTaskSubmitItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="3078" name="FarmNPCFishNtfNtf" serverName="gamesvr"/>
    <entry id="3079" name="WerewolfFullReduceActivityCart_C2S_Msg" serverName="gamesvr"/>
    <entry id="3080" name="WerewolfFullReduceActivityCart_S2C_Msg" serverName="gamesvr"/>
    <entry id="3081" name="UpdateWerewolfFullReduceActivityCart_C2S_Msg" serverName="gamesvr"/>
    <entry id="3082" name="UpdateWerewolfFullReduceActivityCart_S2C_Msg" serverName="gamesvr"/>
    <entry id="3083" name="WerewolfFullReduceActivityCartOrder_C2S_Msg" serverName="gamesvr"/>
    <entry id="3084" name="WerewolfFullReduceActivityCartOrder_S2C_Msg" serverName="gamesvr"/>
    <entry id="3085" name="FarmNPCFishNtf" serverName="gamesvr"/>
    <entry id="3086" name="StarPAdventureGetBaseInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3087" name="StarPAdventureGetBaseInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3088" name="StarPAdventureGetDayReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3089" name="StarPAdventureGetDayReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3090" name="StarPStartRL_C2S_Msg" serverName="gamesvr"/>
    <entry id="3091" name="StarPStartRL_S2C_Msg" serverName="gamesvr"/>
    <entry id="3092" name="StarPQueryVisitInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3093" name="StarPQueryVisitInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3094" name="StarPPlayerDoLottery_C2S_Msg" serverName="gamesvr"/>
    <entry id="3095" name="StarPPlayerDoLottery_S2C_Msg" serverName="gamesvr"/>
    <entry id="3096" name="StarPPlayerGetLotteryInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3097" name="StarPPlayerGetLotteryInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3098" name="StarPGetFunctionControl_C2S_Msg" serverName="gamesvr"/>
    <entry id="3099" name="StarPGetFunctionControl_S2C_Msg" serverName="gamesvr"/>
    <entry id="3100" name="FarmVillagerSummon_C2S_Msg" serverName="gamesvr"/>
    <entry id="3101" name="FarmVillagerSummon_S2C_Msg" serverName="gamesvr"/>
    <entry id="3102" name="FarmVillagerDeport_C2S_Msg" serverName="gamesvr"/>
    <entry id="3103" name="FarmVillagerDeport_S2C_Msg" serverName="gamesvr"/>
    <entry id="3104" name="FarmTaskStatusNtf" serverName="gamesvr"/>
    <entry id="3105" name="LobbyReportMiniGameResult_C2S_Msg" serverName="gamesvr"/>
    <entry id="3106" name="LobbyReportMiniGameResult_S2C_Msg" serverName="gamesvr"/>
    <entry id="3107" name="FarmTaskConditionChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="3108" name="FarmTaskConditionChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="3109" name="StarPPlayerShopInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3110" name="StarPPlayerShopInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3111" name="StarPPlayerShopBuy_C2S_Msg" serverName="gamesvr"/>
    <entry id="3112" name="StarPPlayerShopBuy_S2C_Msg" serverName="gamesvr"/>
    <entry id="3115" name="FarmEventCommitItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="3116" name="FarmEventCommitItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="3120" name="ArenaAttrNtf" serverName="gamesvr"/>
    <entry id="3121" name="PlayerSettingShowAnimationButton_C2S_Msg" serverName="gamesvr"/>
    <entry id="3122" name="PlayerSettingShowAnimationButton_S2C_Msg" serverName="gamesvr"/>
    <entry id="3123" name="FarmTaskSetCanTrigger_C2S_Msg" serverName="gamesvr"/>
    <entry id="3124" name="FarmTaskSetCanTrigger_S2C_Msg" serverName="gamesvr"/>
    <entry id="3128" name="HOKSetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="3129" name="HOKSetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="3130" name="HOKGetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="3131" name="HOKGetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="3132" name="RoomConfirmEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="3133" name="RoomStarPInviteEnterGameAnswer_S2C_Msg" serverName="gamesvr"/>
    <entry id="3134" name="RoomStarPInviteEnterGameAnswerNtf" serverName="gamesvr"/>
    <entry id="3135" name="RoomStarPInviteEnterGameNtf" serverName="gamesvr"/>
    <entry id="3136" name="RoomStarPEnterGameNtf" serverName="gamesvr"/>
    <entry id="3137" name="RoomStarPConfirmEnterProgressNtf" serverName="gamesvr"/>
    <entry id="3138" name="RoomSPBroadcastInfoNtf" serverName="gamesvr"/>
    <entry id="3139" name="StarPEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="3140" name="StarPEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="3141" name="StarPRoleStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="3142" name="StarPRoleStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="3143" name="StarPExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="3144" name="StarPExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="3145" name="StarPCreateOfficial_C2S_Msg" serverName="gamesvr"/>
    <entry id="3146" name="StarPCreateOfficial_S2C_Msg" serverName="gamesvr"/>
    <entry id="3147" name="StarPCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="3148" name="StarPPrepareMigrateDsNtf_S2C_Msg" serverName="gamesvr"/>
    <entry id="3149" name="StarPAttrNtf" serverName="gamesvr"/>
    <entry id="3150" name="StarPCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="3151" name="BatchGetStarPInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3152" name="BatchGetStarPInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3153" name="StarPApplyAdminRole_C2S_Msg" serverName="gamesvr"/>
    <entry id="3154" name="StarPApplyAdminRole_S2C_Msg" serverName="gamesvr"/>
    <entry id="3155" name="StarPDSInfoNtf" serverName="gamesvr"/>
    <entry id="3156" name="StarPApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="3157" name="StarPApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="3158" name="StarPPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="3159" name="StarPPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="3160" name="StarPPlayerNtf" serverName="gamesvr"/>
    <entry id="3161" name="StarPOperateApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="3162" name="StarPOperateApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="3163" name="GetStarPInfoList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3164" name="GetStarPInfoList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3165" name="GetNearbyStarPInfoList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3166" name="GetNearbyStarPInfoList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3167" name="GetStarPDetailInfoList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3168" name="GetStarPDetailInfoList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3169" name="StarPApplyNtf" serverName="gamesvr"/>
    <entry id="3170" name="StarPApplyResultNtf" serverName="gamesvr"/>
    <entry id="3171" name="StarPModifyRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="3172" name="StarPModifyRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="3173" name="StarPGetMyRole_C2S_Msg" serverName="gamesvr"/>
    <entry id="3174" name="StarPGetMyRole_S2C_Msg" serverName="gamesvr"/>
    <entry id="3175" name="StarPBanEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="3176" name="StarPBanEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="3177" name="StarPDelUser_C2S_Msg" serverName="gamesvr"/>
    <entry id="3178" name="StarPDelUser_S2C_Msg" serverName="gamesvr"/>
    <entry id="3179" name="StarPDelRoleByself_C2S_Msg" serverName="gamesvr"/>
    <entry id="3180" name="StarPDelRoleByself_S2C_Msg" serverName="gamesvr"/>
    <entry id="3181" name="StarPAdminTransfer_C2S_Msg" serverName="gamesvr"/>
    <entry id="3182" name="StarPAdminTransfer_S2C_Msg" serverName="gamesvr"/>
    <entry id="3183" name="StarPAblerToApplyAdminNtf" serverName="gamesvr"/>
    <entry id="3184" name="StarPCheckAblerToApplyAdmin_C2S_Msg" serverName="gamesvr"/>
    <entry id="3185" name="StarPCheckAblerToApplyAdmin_S2C_Msg" serverName="gamesvr"/>
    <entry id="3186" name="StarPVoteForDelUser_C2S_Msg" serverName="gamesvr"/>
    <entry id="3187" name="StarPVoteForDelUser_S2C_Msg" serverName="gamesvr"/>
    <entry id="3188" name="StarPInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="3189" name="StarPInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="3190" name="StarPRejectInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="3191" name="StarPRejectInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="3192" name="StarPInviteNtf" serverName="gamesvr"/>
    <entry id="3193" name="StarPQueryPos_C2S_Msg" serverName="gamesvr"/>
    <entry id="3194" name="StarPQueryPos_S2C_Msg" serverName="gamesvr"/>
    <entry id="3195" name="StarPLoadDsCommonDbInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3196" name="StarPLoadDsCommonDbInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3197" name="StarPAddplayerResultNtf" serverName="gamesvr"/>
    <entry id="3198" name="StarPGetPlayerWorldInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3199" name="StarPGetPlayerWorldInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3200" name="StarPChatGroupKeyNtf" serverName="gamesvr"/>
    <entry id="3201" name="DefaultStarPIdChangeNtf" serverName="gamesvr"/>
    <entry id="3202" name="StarPAccountLoadDsCommonDbInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3203" name="StarPAccountLoadDsCommonDbInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3204" name="StarPExitDsNtf" serverName="gamesvr"/>
    <entry id="3205" name="StarPGetAchieveGlobalData_C2S_Msg" serverName="gamesvr"/>
    <entry id="3206" name="StarPGetAchieveGlobalData_S2C_Msg" serverName="gamesvr"/>
    <entry id="3207" name="GetOfficialStarPRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="3208" name="GetOfficialStarPRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="3209" name="TryQuickEnterOfficialStarPRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="3210" name="TryQuickEnterOfficialStarPRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="3211" name="StarPGetDressUpInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3212" name="StarPGetDressUpInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3213" name="StarPCreateDressUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3214" name="StarPCreateDressUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3215" name="StarPSaveDressUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3216" name="StarPSaveDressUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3217" name="StarPShipAction_C2S_Msg" serverName="gamesvr"/>
    <entry id="3218" name="StarPShipAction_S2C_Msg" serverName="gamesvr"/>
    <entry id="3219" name="BatchGetDsCommonDbAccountLevelData_C2S_Msg" serverName="gamesvr"/>
    <entry id="3220" name="BatchGetDsCommonDbAccountLevelData_S2C_Msg" serverName="gamesvr"/>
    <entry id="3221" name="StarPVisitorGetAvailSp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3222" name="StarPVisitorGetAvailSp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3223" name="StarPSetGuide_C2S_Msg" serverName="gamesvr"/>
    <entry id="3224" name="StarPSetGuide_S2C_Msg" serverName="gamesvr"/>
    <entry id="3225" name="StarPGetGuide_C2S_Msg" serverName="gamesvr"/>
    <entry id="3226" name="StarPGetGuide_S2C_Msg" serverName="gamesvr"/>
    <entry id="3227" name="RoomTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="3228" name="RoomTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="3230" name="QueryQualifyTypesSeasonSettlementMailReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3231" name="QueryQualifyTypesSeasonSettlementMailReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3232" name="FarmSetConnectCropNewVersion_C2S_Msg" serverName="gamesvr"/>
    <entry id="3233" name="FarmSetConnectCropNewVersion_S2C_Msg" serverName="gamesvr"/>
    <entry id="3234" name="TradingCardGetCollectionList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3235" name="TradingCardGetCollectionList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3236" name="TradingCardGetCollectionDetailInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3237" name="TradingCardGetCollectionDetailInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3238" name="TradingCardGetNtf" serverName="gamesvr"/>
    <entry id="3239" name="TradingCardGetTradeHistory_C2S_Msg" serverName="gamesvr"/>
    <entry id="3240" name="TradingCardGetTradeHistory_S2C_Msg" serverName="gamesvr"/>
    <entry id="3241" name="TradingCardCreateRequireTrade_C2S_Msg" serverName="gamesvr"/>
    <entry id="3242" name="TradingCardCreateRequireTrade_S2C_Msg" serverName="gamesvr"/>
    <entry id="3243" name="TradingCardCompleteRequireTrade_C2S_Msg" serverName="gamesvr"/>
    <entry id="3244" name="TradingCardCompleteRequireTrade_S2C_Msg" serverName="gamesvr"/>
    <entry id="3245" name="TradingCardCreateGiveTrade_C2S_Msg" serverName="gamesvr"/>
    <entry id="3246" name="TradingCardCreateGiveTrade_S2C_Msg" serverName="gamesvr"/>
    <entry id="3247" name="TradingCardCompleteGiveTrade_C2S_Msg" serverName="gamesvr"/>
    <entry id="3248" name="TradingCardCompleteGiveTrade_S2C_Msg" serverName="gamesvr"/>
    <entry id="3249" name="TradingCardCreateExchangeTrade_C2S_Msg" serverName="gamesvr"/>
    <entry id="3250" name="TradingCardCreateExchangeTrade_S2C_Msg" serverName="gamesvr"/>
    <entry id="3251" name="TradingCardCompleteExchangeTrade_C2S_Msg" serverName="gamesvr"/>
    <entry id="3252" name="TradingCardCompleteExchangeTrade_S2C_Msg" serverName="gamesvr"/>
    <entry id="3255" name="ArenaGetSnap_C2S_Msg" serverName="gamesvr"/>
    <entry id="3256" name="ArenaGetSnap_S2C_Msg" serverName="gamesvr"/>
    <entry id="3259" name="ArenaSelectShowSkin_C2S_Msg" serverName="gamesvr"/>
    <entry id="3260" name="ArenaSelectShowSkin_S2C_Msg" serverName="gamesvr"/>
    <entry id="3261" name="ArenaSelectHeadPic_C2S_Msg" serverName="gamesvr"/>
    <entry id="3262" name="ArenaSelectHeadPic_S2C_Msg" serverName="gamesvr"/>
    <entry id="3263" name="TradingCardExchangeWildCard_C2S_Msg" serverName="gamesvr"/>
    <entry id="3264" name="TradingCardExchangeWildCard_S2C_Msg" serverName="gamesvr"/>
    <entry id="3265" name="FarmTaskAddStealCropForRedFox_C2S_Msg" serverName="gamesvr"/>
    <entry id="3266" name="FarmTaskAddStealCropForRedFox_S2C_Msg" serverName="gamesvr"/>
    <entry id="3269" name="DeviceHasNoPermissionNtf" serverName="gamesvr"/>
    <entry id="3270" name="DeviceIdCheckErrorNtf" serverName="gamesvr"/>
    <entry id="3271" name="IPHasChangedNtf" serverName="gamesvr"/>
    <entry id="3272" name="IPBannedNtf" serverName="gamesvr"/>
    <entry id="3273" name="StarPGetAllRolePetInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3274" name="StarPGetAllRolePetInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3275" name="StarPGetPetFormation_C2S_Msg" serverName="gamesvr"/>
    <entry id="3276" name="StarPGetPetFormation_S2C_Msg" serverName="gamesvr"/>
    <entry id="3277" name="StarPSetPetFormation_C2S_Msg" serverName="gamesvr"/>
    <entry id="3278" name="StarPSetPetFormation_S2C_Msg" serverName="gamesvr"/>
    <entry id="3279" name="StarPShockRewardList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3280" name="StarPShockRewardList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3281" name="StarPGetShockReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3282" name="StarPGetShockReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3283" name="StarPSeasonRewardList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3284" name="StarPSeasonRewardList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3285" name="StarPGetSeasonReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3286" name="StarPGetSeasonReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3287" name="StarPPvpGetOverallInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3288" name="StarPPvpGetOverallInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3289" name="StarPPvpMatchPenaltyStatusNtf" serverName="gamesvr"/>
    <entry id="3290" name="StarPGetOrnamentAndAmuletInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3291" name="StarPGetOrnamentAndAmuletInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3292" name="ArenaGetCommonlyUsedHero_C2S_Msg" serverName="gamesvr"/>
    <entry id="3293" name="ArenaGetCommonlyUsedHero_S2C_Msg" serverName="gamesvr"/>
    <entry id="3294" name="UgcOpenStartWorld_C2S_Msg" serverName="gamesvr"/>
    <entry id="3295" name="UgcOpenStartWorld_S2C_Msg" serverName="gamesvr"/>
    <entry id="3296" name="RejectTestRaffle_C2S_Msg" serverName="gamesvr"/>
    <entry id="3297" name="RejectTestRaffle_S2C_Msg" serverName="gamesvr"/>
    <entry id="3300" name="MallGetThemeShopCommodity_C2S_Msg" serverName="gamesvr"/>
    <entry id="3301" name="MallGetThemeShopCommodity_S2C_Msg" serverName="gamesvr"/>
    <entry id="3302" name="MallGetThemeShopDiscount_C2S_Msg" serverName="gamesvr"/>
    <entry id="3303" name="MallGetThemeShopDiscount_S2C_Msg" serverName="gamesvr"/>
    <entry id="3304" name="SetIntimateOnlineNoticeBox_C2S_Msg" serverName="gamesvr"/>
    <entry id="3305" name="SetIntimateOnlineNoticeBox_S2C_Msg" serverName="gamesvr"/>
    <entry id="3306" name="PlayerAntiCheatInfoInitNtf" serverName="gamesvr"/>
    <entry id="3307" name="PlayerAntiCheatInfoReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="3308" name="PlayerAntiCheatInfoReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="3309" name="PlayerAntiCheatInfoToClientNtf" serverName="gamesvr"/>
    <entry id="3311" name="FarmFishMerge_C2S_Msg" serverName="gamesvr"/>
    <entry id="3312" name="FarmFishMerge_S2C_Msg" serverName="gamesvr"/>
    <entry id="3314" name="CookCreate_C2S_Msg" serverName="gamesvr"/>
    <entry id="3315" name="CookCreate_S2C_Msg" serverName="gamesvr"/>
    <entry id="3316" name="CookEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="3317" name="CookEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="3318" name="CookExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="3319" name="CookExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="3320" name="CookKickNtf" serverName="gamesvr"/>
    <entry id="3321" name="CookDSInfoNtf" serverName="gamesvr"/>
    <entry id="3322" name="CookAttrNtf" serverName="gamesvr"/>
    <entry id="3323" name="CookOwnerAttrNtf" serverName="gamesvr"/>
    <entry id="3324" name="CookForwardToDS_C2S_Msg" serverName="gamesvr"/>
    <entry id="3325" name="CookForwardToDS_S2C_Msg" serverName="gamesvr"/>
    <entry id="3326" name="CookGetOfflineIncome_C2S_Msg" serverName="gamesvr"/>
    <entry id="3327" name="CookGetOfflineIncome_S2C_Msg" serverName="gamesvr"/>
    <entry id="3328" name="CookDoSettlement_C2S_Msg" serverName="gamesvr"/>
    <entry id="3329" name="CookDoSettlement_S2C_Msg" serverName="gamesvr"/>
    <entry id="3330" name="CookHireFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="3331" name="CookHireFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="3332" name="CookFireFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="3333" name="CookFireFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="3334" name="CookPoachFriend_C2S_Msg" serverName="gamesvr"/>
    <entry id="3335" name="CookPoachFriend_S2C_Msg" serverName="gamesvr"/>
    <entry id="3338" name="CookOpen_C2S_Msg" serverName="gamesvr"/>
    <entry id="3339" name="CookOpen_S2C_Msg" serverName="gamesvr"/>
    <entry id="3340" name="CookClose_C2S_Msg" serverName="gamesvr"/>
    <entry id="3341" name="CookClose_S2C_Msg" serverName="gamesvr"/>
    <entry id="3344" name="RafflePickInventory_C2S_Msg" serverName="gamesvr"/>
    <entry id="3345" name="RafflePickInventory_S2C_Msg" serverName="gamesvr"/>
    <entry id="3346" name="RewardRetrievalInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3347" name="RewardRetrievalInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3348" name="RewardRetrievalRetrieve_C2S_Msg" serverName="gamesvr"/>
    <entry id="3349" name="RewardRetrievalRetrieve_S2C_Msg" serverName="gamesvr"/>
    <entry id="3350" name="RewardRetrievalUpdateNtf" serverName="gamesvr"/>
    <entry id="3351" name="RewardRetrievalClearRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="3352" name="RewardRetrievalClearRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="3353" name="ChangeMallWishList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3354" name="ChangeMallWishList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3355" name="FollowOtherMallWishList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3356" name="FollowOtherMallWishList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3357" name="WolfKillGetComeBackInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3358" name="WolfKillGetComeBackInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3359" name="WolfKillGetComeBackReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3360" name="WolfKillGetComeBackReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3361" name="FittingItemStatusChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="3362" name="FittingItemStatusChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="3363" name="TradingCardShareToChat_C2S_Msg" serverName="gamesvr"/>
    <entry id="3364" name="TradingCardShareToChat_S2C_Msg" serverName="gamesvr"/>
    <entry id="3433" name="GuideConfigDiscoverStatusQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="3434" name="GuideConfigDiscoverStatusQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="3435" name="GuideConfigDiscoverStatusUpdate_S2C_Msg" serverName="gamesvr"/>
    <entry id="3436" name="GuideConfigDiscoverStatusUpdate_C2S_Msg" serverName="gamesvr"/>
    <entry id="3437" name="StarPPVPGetDailyReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3438" name="StarPPVPGetDailyReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3439" name="FarmCommitItemToCloud_C2S_Msg" serverName="gamesvr"/>
    <entry id="3440" name="FarmCommitItemToCloud_S2C_Msg" serverName="gamesvr"/>
    <entry id="3441" name="TradingCardGetTradeInfoListCache_C2S_Msg" serverName="gamesvr"/>
    <entry id="3442" name="TradingCardGetTradeInfoListCache_S2C_Msg" serverName="gamesvr"/>
    <entry id="3443" name="TradingCardGetTradeInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3444" name="TradingCardGetTradeInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3449" name="GeneralReadDotNtf" serverName="gamesvr"/>
    <entry id="3450" name="ClickGeneralRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="3451" name="ClickGeneralRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="3468" name="CookSendComment_C2S_Msg" serverName="gamesvr"/>
    <entry id="3469" name="CookSendComment_S2C_Msg" serverName="gamesvr"/>
    <entry id="3470" name="TradingCardClearRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="3471" name="TradingCardClearRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="3472" name="TradingCardRedDotChangeNtf" serverName="gamesvr"/>
    <entry id="3473" name="CustomRoomList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3474" name="CustomRoomList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3475" name="CustomRoomRoundLevelList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3476" name="CustomRoomRoundLevelList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3477" name="CookDeleteComment_C2S_Msg" serverName="gamesvr"/>
    <entry id="3478" name="CookDeleteComment_S2C_Msg" serverName="gamesvr"/>
    <entry id="3479" name="CookGetComment_C2S_Msg" serverName="gamesvr"/>
    <entry id="3480" name="CookGetComment_S2C_Msg" serverName="gamesvr"/>
    <entry id="3481" name="CookReceiveNewCommentNtf" serverName="gamesvr"/>
    <entry id="3482" name="CookVisitantPrebook_C2S_Msg" serverName="gamesvr"/>
    <entry id="3483" name="CookVisitantPrebook_S2C_Msg" serverName="gamesvr"/>
    <entry id="3491" name="CookVisitantSteal_C2S_Msg" serverName="gamesvr"/>
    <entry id="3492" name="CookVisitantSteal_S2C_Msg" serverName="gamesvr"/>
    <entry id="3493" name="SetMood_C2S_Msg" serverName="gamesvr"/>
    <entry id="3494" name="SetMood_S2C_Msg" serverName="gamesvr"/>
    <entry id="3495" name="TradingCardDrawCycleCupsReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3496" name="TradingCardDrawCycleCupsReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3497" name="AiNpcLicense_C2S_Msg" serverName="gamesvr"/>
    <entry id="3498" name="AiNpcLicense_S2C_Msg" serverName="gamesvr"/>
    <entry id="3499" name="PlayerEnterCocGameScene_C2S_Msg" serverName="gamesvr"/>
    <entry id="3500" name="PlayerEnterCocGameScene_S2C_Msg" serverName="gamesvr"/>
    <entry id="3501" name="PlayerExitCocGameScene_C2S_Msg" serverName="gamesvr"/>
    <entry id="3502" name="PlayerExitCocGameScene_S2C_Msg" serverName="gamesvr"/>
    <entry id="3503" name="CocBuildBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3504" name="CocBuildBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3505" name="CocUpgradeBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3506" name="CocUpgradeBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3507" name="CocFastFinishBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3508" name="CocFastFinishBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3509" name="CocCancelBuildBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3510" name="CocCancelBuildBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3511" name="CocCancelUpgradeBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3512" name="CocCancelUpgradeBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3513" name="CocDemolishBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3514" name="CocDemolishBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3515" name="CocMoveBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3516" name="CocMoveBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3517" name="CocStartMatch_C2S_Msg" serverName="gamesvr"/>
    <entry id="3518" name="CocStartMatch_S2C_Msg" serverName="gamesvr"/>
    <entry id="3519" name="CocStartFight_C2S_Msg" serverName="gamesvr"/>
    <entry id="3520" name="CocStartFight_S2C_Msg" serverName="gamesvr"/>
    <entry id="3521" name="CocFightingDeltaInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3522" name="CocFightingDeltaInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3523" name="CocFightSettle_C2S_Msg" serverName="gamesvr"/>
    <entry id="3524" name="CocFightSettle_S2C_Msg" serverName="gamesvr"/>
    <entry id="3525" name="CocQueryMatchPlayerInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3526" name="CocQueryMatchPlayerInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3527" name="CocUpgradeScience_C2S_Msg" serverName="gamesvr"/>
    <entry id="3528" name="CocUpgradeScience_S2C_Msg" serverName="gamesvr"/>
    <entry id="3529" name="CocCompleteScience_C2S_Msg" serverName="gamesvr"/>
    <entry id="3530" name="CocCompleteScience_S2C_Msg" serverName="gamesvr"/>
    <entry id="3531" name="CocSoldierTraining_C2S_Msg" serverName="gamesvr"/>
    <entry id="3532" name="CocSoldierTraining_S2C_Msg" serverName="gamesvr"/>
    <entry id="3533" name="CocSoldierModify_C2S_Msg" serverName="gamesvr"/>
    <entry id="3534" name="CocSoldierModify_S2C_Msg" serverName="gamesvr"/>
    <entry id="3535" name="CocSoldierPreset_C2S_Msg" serverName="gamesvr"/>
    <entry id="3536" name="CocSoldierPreset_S2C_Msg" serverName="gamesvr"/>
    <entry id="3537" name="CocSoldierUsePreset_C2S_Msg" serverName="gamesvr"/>
    <entry id="3538" name="CocSoldierUsePreset_S2C_Msg" serverName="gamesvr"/>
    <entry id="3539" name="CocCollectResource_C2S_Msg" serverName="gamesvr"/>
    <entry id="3540" name="CocCollectResource_S2C_Msg" serverName="gamesvr"/>
    <entry id="3541" name="CocDuanWeiReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3542" name="CocDuanWeiReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3543" name="CocTreasureBoxOperate_C2S_Msg" serverName="gamesvr"/>
    <entry id="3544" name="CocTreasureBoxOperate_S2C_Msg" serverName="gamesvr"/>
    <entry id="3545" name="CocUpResourceEfficiency_C2S_Msg" serverName="gamesvr"/>
    <entry id="3546" name="CocUpResourceEfficiency_S2C_Msg" serverName="gamesvr"/>
    <entry id="3547" name="CocMarkDialog_C2S_Msg" serverName="gamesvr"/>
    <entry id="3548" name="CocMarkDialog_S2C_Msg" serverName="gamesvr"/>
    <entry id="3555" name="CocUnlockMapPrimaryRegion_C2S_Msg" serverName="gamesvr"/>
    <entry id="3556" name="CocUnlockMapPrimaryRegion_S2C_Msg" serverName="gamesvr"/>
    <entry id="3557" name="CocUnlockMapMinorRegion_C2S_Msg" serverName="gamesvr"/>
    <entry id="3558" name="CocUnlockMapMinorRegion_S2C_Msg" serverName="gamesvr"/>
    <entry id="3559" name="CocGetXingbaoCaptureReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3560" name="CocGetXingbaoCaptureReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3561" name="CocPlayerNumPropDetailsNtf" serverName="gamesvr"/>
    <entry id="3562" name="CocReleaseXingbao_C2S_Msg" serverName="gamesvr"/>
    <entry id="3563" name="CocReleaseXingbao_S2C_Msg" serverName="gamesvr"/>
    <entry id="3564" name="CocGetOfflineEventOnLoadComplete_C2S_Msg" serverName="gamesvr"/>
    <entry id="3565" name="CocGetOfflineEventOnLoadComplete_S2C_Msg" serverName="gamesvr"/>
    <entry id="3566" name="CocUnlockVillager_C2S_Msg" serverName="gamesvr"/>
    <entry id="3567" name="CocUnlockVillager_S2C_Msg" serverName="gamesvr"/>
    <entry id="3568" name="CocAppointOfficialPosForVillager_C2S_Msg" serverName="gamesvr"/>
    <entry id="3569" name="CocAppointOfficialPosForVillager_S2C_Msg" serverName="gamesvr"/>
    <entry id="3570" name="CocStartVillagerDialogue_C2S_Msg" serverName="gamesvr"/>
    <entry id="3571" name="CocStartVillagerDialogue_S2C_Msg" serverName="gamesvr"/>
    <entry id="3572" name="CocMarkVillagerDialogueFinishKeyStep_C2S_Msg" serverName="gamesvr"/>
    <entry id="3573" name="CocMarkVillagerDialogueFinishKeyStep_S2C_Msg" serverName="gamesvr"/>
    <entry id="3574" name="CocCompleteVillagerDialogue_C2S_Msg" serverName="gamesvr"/>
    <entry id="3575" name="CocCompleteVillagerDialogue_S2C_Msg" serverName="gamesvr"/>
    <entry id="3576" name="CocVillagerChangeDress_C2S_Msg" serverName="gamesvr"/>
    <entry id="3577" name="CocVillagerChangeDress_S2C_Msg" serverName="gamesvr"/>
    <entry id="3578" name="CocModifyVillagerName_C2S_Msg" serverName="gamesvr"/>
    <entry id="3579" name="CocModifyVillagerName_S2C_Msg" serverName="gamesvr"/>
    <entry id="3580" name="CocAcceptVillagerDailyFavorReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3581" name="CocAcceptVillagerDailyFavorReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3582" name="CocEnterOtherScene_C2S_Msg" serverName="gamesvr"/>
    <entry id="3583" name="CocEnterOtherScene_S2C_Msg" serverName="gamesvr"/>
    <entry id="3584" name="CocBuyCocMonthCard_C2S_Msg" serverName="gamesvr"/>
    <entry id="3585" name="CocBuyCocMonthCard_S2C_Msg" serverName="gamesvr"/>
    <entry id="3586" name="CocMonthCardDailyReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3587" name="CocMonthCardDailyReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3588" name="CocGetTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3589" name="CocGetTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3590" name="CocRewardNtf" serverName="gamesvr"/>
    <entry id="3591" name="CocBatchUpgradeBuilding_C2S_Msg" serverName="gamesvr"/>
    <entry id="3592" name="CocBatchUpgradeBuilding_S2C_Msg" serverName="gamesvr"/>
    <entry id="3595" name="CocStartFightBattle_C2S_Msg" serverName="gamesvr"/>
    <entry id="3596" name="CocStartFightBattle_S2C_Msg" serverName="gamesvr"/>
    <entry id="3597" name="CocSettleBattleGame_C2S_Msg" serverName="gamesvr"/>
    <entry id="3598" name="CocSettleBattleGame_S2C_Msg" serverName="gamesvr"/>
    <entry id="3599" name="MarkCocDefenseMailHasFoughtBack_C2S_Msg" serverName="gamesvr"/>
    <entry id="3600" name="MarkCocDefenseMailHasFoughtBack_S2C_Msg" serverName="gamesvr"/>
    <entry id="3607" name="AppearanceRoadGetGatherAward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3608" name="AppearanceRoadGetGatherAward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3609" name="AppearanceRoadGetLvAward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3610" name="AppearanceRoadGetLvAward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3611" name="StarPVisitSameWorld_C2S_Msg" serverName="gamesvr"/>
    <entry id="3612" name="StarPVisitSameWorld_S2C_Msg" serverName="gamesvr"/>
    <entry id="3613" name="AiNpcCheckText_C2S_Msg" serverName="gamesvr"/>
    <entry id="3614" name="AiNpcCheckText_S2C_Msg" serverName="gamesvr"/>
    <entry id="3615" name="AiNpcSubmitTempPalProfile_C2S_Msg" serverName="gamesvr"/>
    <entry id="3616" name="AiNpcSubmitTempPalProfile_S2C_Msg" serverName="gamesvr"/>
    <entry id="3617" name="AiNpcSubmitPlayerProfile_C2S_Msg" serverName="gamesvr"/>
    <entry id="3618" name="AiNpcSubmitPlayerProfile_S2C_Msg" serverName="gamesvr"/>
    <entry id="3619" name="AiNpcGenCustomPal_C2S_Msg" serverName="gamesvr"/>
    <entry id="3620" name="AiNpcGenCustomPal_S2C_Msg" serverName="gamesvr"/>
    <entry id="3621" name="AiNpcFinishGuide_C2S_Msg" serverName="gamesvr"/>
    <entry id="3622" name="AiNpcFinishGuide_S2C_Msg" serverName="gamesvr"/>
    <entry id="3623" name="WolfKillTreasureUse_C2S_Msg" serverName="gamesvr"/>
    <entry id="3624" name="WolfKillTreasureUse_S2C_Msg" serverName="gamesvr"/>
    <entry id="3625" name="WolfKillVocation_C2S_Msg" serverName="gamesvr"/>
    <entry id="3626" name="WolfKillVocation_S2C_Msg" serverName="gamesvr"/>
    <entry id="3627" name="CocGetFriendGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="3628" name="CocGetFriendGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="3629" name="CocSendFriendDonation_C2S_Msg" serverName="gamesvr"/>
    <entry id="3630" name="CocSendFriendDonation_S2C_Msg" serverName="gamesvr"/>
    <entry id="3631" name="CocReceiveFriendDonationNtf" serverName="gamesvr"/>
    <entry id="3632" name="CocAllocBattleGame_C2S_Msg" serverName="gamesvr"/>
    <entry id="3633" name="CocAllocBattleGame_S2C_Msg" serverName="gamesvr"/>
    <entry id="3634" name="CocAppointFriendVillager_C2S_Msg" serverName="gamesvr"/>
    <entry id="3635" name="CocAppointFriendVillager_S2C_Msg" serverName="gamesvr"/>
    <entry id="3636" name="CocVisitFriendHome_C2S_Msg" serverName="gamesvr"/>
    <entry id="3637" name="CocVisitFriendHome_S2C_Msg" serverName="gamesvr"/>
    <entry id="3638" name="FarmVillagerGetFavorExp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3639" name="FarmVillagerGetFavorExp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3640" name="GiveMailAttachmentsNtf" serverName="gamesvr"/>
    <entry id="3641" name="CocPrisonAssign_C2S_Msg" serverName="gamesvr"/>
    <entry id="3642" name="CocPrisonAssign_S2C_Msg" serverName="gamesvr"/>
    <entry id="3643" name="AnniversaryMobaDrawReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3644" name="AnniversaryMobaGetReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3645" name="AnniversaryMobaGetReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3646" name="AnniversaryMobaDrawReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3649" name="WolfKillTreasureRead_C2S_Msg" serverName="gamesvr"/>
    <entry id="3650" name="WolfKillTreasureRead_S2C_Msg" serverName="gamesvr"/>
    <entry id="3651" name="ArenaTipRewardDetailsNtf" serverName="gamesvr"/>
    <entry id="3652" name="CocDonationAccelerate_C2S_Msg" serverName="gamesvr"/>
    <entry id="3653" name="CocDonationAccelerate_S2C_Msg" serverName="gamesvr"/>
    <entry id="3654" name="CocSetBattleBuildingAttackMode_C2S_Msg" serverName="gamesvr"/>
    <entry id="3655" name="CocSetBattleBuildingAttackMode_S2C_Msg" serverName="gamesvr"/>
    <entry id="3656" name="AiNpcMoodChangeNtf" serverName="gamesvr"/>
    <entry id="3661" name="CocRemoveAfterReleaseXingBao_C2S_Msg" serverName="gamesvr"/>
    <entry id="3662" name="CocRemoveAfterReleaseXingBao_S2C_Msg" serverName="gamesvr"/>
    <entry id="3663" name="WolfKillTreasureLevelRead_C2S_Msg" serverName="gamesvr"/>
    <entry id="3664" name="WolfKillTreasureLevelRead_S2C_Msg" serverName="gamesvr"/>
    <entry id="3669" name="WolfKillTreasureRefreshLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="3670" name="WolfKillTreasureRefreshLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="3671" name="AnniversaryMobaGetRewardTimes_C2S_Msg" serverName="gamesvr"/>
    <entry id="3672" name="AnniversaryMobaGetRewardTimes_S2C_Msg" serverName="gamesvr"/>
    <entry id="3673" name="StarPCreateRole_C2S_Msg" serverName="gamesvr"/>
    <entry id="3674" name="StarPCreateRole_S2C_Msg" serverName="gamesvr"/>
    <entry id="3675" name="StarPDeleteRole_C2S_Msg" serverName="gamesvr"/>
    <entry id="3676" name="StarPDeleteRole_S2C_Msg" serverName="gamesvr"/>
    <entry id="3677" name="StarPGetRoleList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3678" name="StarPGetRoleList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3679" name="ChatBatchSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="3680" name="ChatBatchSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="3682" name="FarmVillagerSwitchScene_C2S_Msg" serverName="gamesvr"/>
    <entry id="3683" name="FarmVillagerSwitchScene_S2C_Msg" serverName="gamesvr"/>
    <entry id="3686" name="ArenaEnterPrepareInterface_C2S_Msg" serverName="gamesvr"/>
    <entry id="3687" name="ArenaEnterPrepareInterface_S2C_Msg" serverName="gamesvr"/>
    <entry id="3688" name="FarmEnterHotSpring_C2S_Msg" serverName="gamesvr"/>
    <entry id="3689" name="FarmEnterHotSpring_S2C_Msg" serverName="gamesvr"/>
    <entry id="3690" name="FarmLeaveHotSpring_C2S_Msg" serverName="gamesvr"/>
    <entry id="3691" name="FarmLeaveHotSpring_S2C_Msg" serverName="gamesvr"/>
    <entry id="3692" name="FarmGetHotSpringBuff_C2S_Msg" serverName="gamesvr"/>
    <entry id="3693" name="FarmGetHotSpringBuff_S2C_Msg" serverName="gamesvr"/>
    <entry id="3696" name="FarmChangeSignature_C2S_Msg" serverName="gamesvr"/>
    <entry id="3697" name="FarmChangeSignature_S2C_Msg" serverName="gamesvr"/>
    <entry id="3698" name="MobaSquadDrawRedPacketDraw_C2S_Msg" serverName="gamesvr"/>
    <entry id="3699" name="MobaSquadDrawRedPacketDraw_S2C_Msg" serverName="gamesvr"/>
    <entry id="3702" name="StreamDsGeneral_C2S_Msg" serverName="streamsvr"/>
    <entry id="3703" name="StreamDsGeneral_S2C_Msg" serverName="streamsvr"/>
    <entry id="3704" name="ActivityNewYearSign_C2S_Msg" serverName="gamesvr"/>
    <entry id="3705" name="ActivityNewYearSign_S2C_Msg" serverName="gamesvr"/>
    <entry id="3706" name="LobbyListFind_C2S_Msg" serverName="gamesvr"/>
    <entry id="3707" name="LobbyListFind_S2C_Msg" serverName="gamesvr"/>
    <entry id="3708" name="GetDailyBattleOrnamentationInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3709" name="GetDailyBattleOrnamentationInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3710" name="UpdatedDailyBattleOrnamentationInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3711" name="UpdatedDailyBattleOrnamentationInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3714" name="FarmQueryNpcCD_C2S_Msg" serverName="gamesvr"/>
    <entry id="3715" name="FarmQueryNpcCD_S2C_Msg" serverName="gamesvr"/>
    <entry id="3716" name="FarmInvokeNpc_C2S_Msg" serverName="gamesvr"/>
    <entry id="3717" name="FarmInvokeNpc_S2C_Msg" serverName="gamesvr"/>
    <entry id="3718" name="FarmPrayToGodFigure_C2S_Msg" serverName="gamesvr"/>
    <entry id="3719" name="FarmPrayToGodFigure_S2C_Msg" serverName="gamesvr"/>
    <entry id="3720" name="FarmUpgradeGodFigure_C2S_Msg" serverName="gamesvr"/>
    <entry id="3721" name="FarmUpgradeGodFigure_S2C_Msg" serverName="gamesvr"/>
    <entry id="3722" name="UgcInitiateEvaluation_C2S_Msg" serverName="gamesvr"/>
    <entry id="3723" name="UgcInitiateEvaluation_S2C_Msg" serverName="gamesvr"/>
    <entry id="3727" name="AmsRewardInfoNtf" serverName="gamesvr"/>
    <entry id="3729" name="MallGiftCardChangeNtf" serverName="gamesvr"/>
    <entry id="3730" name="FarmMagicUse_C2S_Msg" serverName="gamesvr"/>
    <entry id="3731" name="FarmMagicUse_S2C_Msg" serverName="gamesvr"/>
    <entry id="3732" name="FarmMagicShortenHarvestTimeNtf" serverName="gamesvr"/>
    <entry id="3733" name="FarmMagicEquip_C2S_Msg" serverName="gamesvr"/>
    <entry id="3734" name="FarmMagicEquip_S2C_Msg" serverName="gamesvr"/>
    <entry id="3735" name="LobbyMatchResultNtf" serverName="gamesvr"/>
    <entry id="3736" name="LobbyMatchCancelResultNtf" serverName="gamesvr"/>
    <entry id="3739" name="FarmSetVisitorListTag_C2S_Msg" serverName="gamesvr"/>
    <entry id="3740" name="FarmSetVisitorListTag_S2C_Msg" serverName="gamesvr"/>
    <entry id="3741" name="HouseSetVisitorListTag_C2S_Msg" serverName="gamesvr"/>
    <entry id="3742" name="HouseSetVisitorListTag_S2C_Msg" serverName="gamesvr"/>
    <entry id="3743" name="XiaoWoSetVisitorListTag_C2S_Msg" serverName="gamesvr"/>
    <entry id="3744" name="XiaoWoSetVisitorListTag_S2C_Msg" serverName="gamesvr"/>
    <entry id="3745" name="StarPSetChatPetIconID_C2S_Msg" serverName="gamesvr"/>
    <entry id="3746" name="StarPSetChatPetIconID_S2C_Msg" serverName="gamesvr"/>
    <entry id="3749" name="ReadOtherMallWishList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3750" name="ReadOtherMallWishList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3751" name="StarPSetChatCupIconID_C2S_Msg" serverName="gamesvr"/>
    <entry id="3752" name="StarPSetChatCupIconID_S2C_Msg" serverName="gamesvr"/>
    <entry id="3755" name="ClearMonthCardExpiredRedPoint_C2S_Msg" serverName="gamesvr"/>
    <entry id="3756" name="ClearMonthCardExpiredRedPoint_S2C_Msg" serverName="gamesvr"/>
    <entry id="3757" name="MonthCardRedPointNtf" serverName="gamesvr"/>
    <entry id="3758" name="CookLevelUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3759" name="CookLevelUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3760" name="CookSendCommentPhoto_C2S_Msg" serverName="gamesvr"/>
    <entry id="3761" name="CookSendCommentPhoto_S2C_Msg" serverName="gamesvr"/>
    <entry id="3770" name="RpcTestConfigSvr_C2S_Msg" serverName="configsvr"/>
    <entry id="3771" name="RpcTestConfigSvr_S2C_Msg" serverName="configsvr"/>
    <entry id="3772" name="FindPartnerAnswerQuestion_C2S_Msg" serverName="gamesvr"/>
    <entry id="3773" name="FindPartnerAnswerQuestion_S2C_Msg" serverName="gamesvr"/>
    <entry id="3774" name="FindPartnerGetQuestions_C2S_Msg" serverName="gamesvr"/>
    <entry id="3775" name="FindPartnerGetQuestions_S2C_Msg" serverName="gamesvr"/>
    <entry id="3778" name="UgcMapExtraConfigEdit_C2S_Msg" serverName="gamesvr"/>
    <entry id="3779" name="UgcMapExtraConfigEdit_S2C_Msg" serverName="gamesvr"/>
    <entry id="3780" name="UgcMapExtraConfigGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="3781" name="UgcMapExtraConfigGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="3782" name="UpdatedReadyBattleOrnamentationBagInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3783" name="UpdatedReadyBattleOrnamentationBagInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3787" name="ActivitySquadAcquireReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3788" name="ActivitySquadAcquireReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3789" name="ActivitySquadOpenFinalTreasure_C2S_Msg" serverName="gamesvr"/>
    <entry id="3790" name="ActivitySquadOpenFinalTreasure_S2C_Msg" serverName="gamesvr"/>
    <entry id="3791" name="GetPlayPufferInfo_C2S_Msg" serverName="configsvr"/>
    <entry id="3792" name="GetPlayPufferInfo_S2C_Msg" serverName="configsvr"/>
    <entry id="3793" name="GetSeasonReviewInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3794" name="GetSeasonReviewInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3795" name="MallRedPointClear_C2S_Msg" serverName="gamesvr"/>
    <entry id="3796" name="MallRedPointClear_S2C_Msg" serverName="gamesvr"/>
    <entry id="3797" name="SummonFortuneSlip_C2S_Msg" serverName="gamesvr"/>
    <entry id="3798" name="SummonFortuneSlip_S2C_Msg" serverName="gamesvr"/>
    <entry id="3799" name="ReceiveCollectionReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3800" name="ReceiveCollectionReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3801" name="GetLuckyStarFriendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3802" name="GetLuckyStarFriendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3803" name="GetLuckyStarAssistRecordList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3804" name="GetLuckyStarAssistRecordList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3805" name="LuckyStarRequestNormalAssist_C2S_Msg" serverName="gamesvr"/>
    <entry id="3806" name="LuckyStarRequestNormalAssist_S2C_Msg" serverName="gamesvr"/>
    <entry id="3807" name="LuckyStarNormalAssist_C2S_Msg" serverName="gamesvr"/>
    <entry id="3808" name="LuckyStarNormalAssist_S2C_Msg" serverName="gamesvr"/>
    <entry id="3809" name="LuckyStarRequestSpecifyAssist_C2S_Msg" serverName="gamesvr"/>
    <entry id="3810" name="LuckyStarRequestSpecifyAssist_S2C_Msg" serverName="gamesvr"/>
    <entry id="3811" name="LuckyStarSpecifyAssist_C2S_Msg" serverName="gamesvr"/>
    <entry id="3812" name="LuckyStarSpecifyAssist_S2C_Msg" serverName="gamesvr"/>
    <entry id="3813" name="LuckyStarGiveSlip_C2S_Msg" serverName="gamesvr"/>
    <entry id="3814" name="LuckyStarGiveSlip_S2C_Msg" serverName="gamesvr"/>
    <entry id="3815" name="LuckyStarRequestSlip_C2S_Msg" serverName="gamesvr"/>
    <entry id="3816" name="LuckyStarRequestSlip_S2C_Msg" serverName="gamesvr"/>
    <entry id="3817" name="ScoreGuidePopUpNtf" serverName="gamesvr"/>
    <entry id="3818" name="ScoreGuidePopUpProcess_C2S_Msg" serverName="gamesvr"/>
    <entry id="3819" name="ScoreGuidePopUpProcess_S2C_Msg" serverName="gamesvr"/>
    <entry id="3820" name="TradingCardExchangeReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3821" name="TradingCardExchangeReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3822" name="WolfKillTreasureData_C2S_Msg" serverName="gamesvr"/>
    <entry id="3823" name="WolfKillTreasureData_S2C_Msg" serverName="gamesvr"/>
    <entry id="3824" name="BirthdayInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3825" name="BirthdayInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3826" name="BirthdayChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="3827" name="BirthdayChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="3828" name="BirthdayBlessingRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="3829" name="BirthdayBlessingRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="3830" name="BirthdayBlessingRecordRedDotNtf" serverName="gamesvr"/>
    <entry id="3831" name="BirthdayBlessingRecordClearRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="3832" name="BirthdayBlessingRecordClearRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="3833" name="BirthdayCardSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="3834" name="BirthdayCardSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="3835" name="BirthdayBlessingSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="3836" name="BirthdayBlessingSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="3837" name="BirthdayCardItemAddNtf" serverName="gamesvr"/>
    <entry id="3838" name="BirthdayCardItemDelete_C2S_Msg" serverName="gamesvr"/>
    <entry id="3839" name="BirthdayCardItemDelete_S2C_Msg" serverName="gamesvr"/>
    <entry id="3840" name="BirthdayOfficialWelfareGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="3841" name="BirthdayOfficialWelfareGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="3842" name="GMRespDataNtf" serverName="gamesvr"/>
    <entry id="3843" name="RafflePurchaseSubItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="3844" name="RafflePurchaseSubItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="3845" name="TradingCardDrawNoviceReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3846" name="TradingCardDrawNoviceReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3847" name="GetLobbyMatchHistoryList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3848" name="GetLobbyMatchHistoryList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3849" name="RecruitOrderRaffleConfirm_C2S_Msg" serverName="gamesvr"/>
    <entry id="3850" name="RecruitOrderRaffleConfirm_S2C_Msg" serverName="gamesvr"/>
    <entry id="3851" name="RecruitOrderRaffleAddressConfirm_C2S_Msg" serverName="gamesvr"/>
    <entry id="3852" name="RecruitOrderRaffleAddressConfirm_S2C_Msg" serverName="gamesvr"/>
    <entry id="3853" name="ShareGiftReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3854" name="ShareGiftReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3855" name="ShareHistoryGet_C2S_Msg" serverName="gamesvr"/>
    <entry id="3856" name="ShareHistoryGet_S2C_Msg" serverName="gamesvr"/>
    <entry id="3859" name="ActivitySquadKickMember_C2S_Msg" serverName="gamesvr"/>
    <entry id="3860" name="ActivitySquadKickMember_S2C_Msg" serverName="gamesvr"/>
    <entry id="3861" name="BatchGetChatShareGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="3862" name="BatchGetChatShareGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="3863" name="StarPRoleSelect_C2S_Msg" serverName="gamesvr"/>
    <entry id="3864" name="StarPRoleSelect_S2C_Msg" serverName="gamesvr"/>
    <entry id="3867" name="TradingCardShowNoviceReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3868" name="TradingCardShowNoviceReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3869" name="BPExpChangeNtf" serverName="gamesvr"/>
    <entry id="3870" name="CookSetClientKV_C2S_Msg" serverName="gamesvr"/>
    <entry id="3871" name="CookSetClientKV_S2C_Msg" serverName="gamesvr"/>
    <entry id="3872" name="ActivitySquadFarmWateringTree_C2S_Msg" serverName="gamesvr"/>
    <entry id="3873" name="ActivitySquadFarmWateringTree_S2C_Msg" serverName="gamesvr"/>
    <entry id="3874" name="TradingCardGetActivity_C2S_Msg" serverName="gamesvr"/>
    <entry id="3875" name="TradingCardGetActivity_S2C_Msg" serverName="gamesvr"/>
    <entry id="3876" name="ArenaGetHeroBattleStatistic_C2S_Msg" serverName="gamesvr"/>
    <entry id="3877" name="ArenaGetHeroBattleStatistic_S2C_Msg" serverName="gamesvr"/>
    <entry id="3878" name="BirthdayRemindNtf" serverName="gamesvr"/>
    <entry id="3881" name="FarmHotSpringTickNtf" serverName="gamesvr"/>
    <entry id="3882" name="SetSprayPaint_C2S_Msg" serverName="gamesvr"/>
    <entry id="3883" name="SetSprayPaint_S2C_Msg" serverName="gamesvr"/>
    <entry id="3884" name="UseSprayPaint_C2S_Msg" serverName="gamesvr"/>
    <entry id="3885" name="UseSprayPaint_S2C_Msg" serverName="gamesvr"/>
    <entry id="3886" name="CookEmployeeOp_C2S_Msg" serverName="gamesvr"/>
    <entry id="3887" name="CookEmployeeOp_S2C_Msg" serverName="gamesvr"/>
    <entry id="3889" name="BirthdayCardChangeNtf" serverName="gamesvr"/>
    <entry id="3896" name="QueryLuckStarCount_C2S_Msg" serverName="gamesvr"/>
    <entry id="3897" name="QueryLuckStarCount_S2C_Msg" serverName="gamesvr"/>
    <entry id="3898" name="StarPRecruitQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="3899" name="StarPRecruitQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="3900" name="StarPRecruitPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="3901" name="StarPRecruitPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="3904" name="FetchBatchSnapshotInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3905" name="FetchBatchSnapshotInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3906" name="BirthdayCardItemUpdateNtf" serverName="gamesvr"/>
    <entry id="3907" name="BirthdayBlessingRecordUpdateNtf" serverName="gamesvr"/>
    <entry id="3908" name="RoomGetCurRoundInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3909" name="RoomGetCurRoundInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3910" name="RoomRoundInfoNtf" serverName="gamesvr"/>
    <entry id="3911" name="RoomUgcMultiRoundCoinUse_C2S_Msg" serverName="gamesvr"/>
    <entry id="3912" name="RoomUgcMultiRoundCoinUse_S2C_Msg" serverName="gamesvr"/>
    <entry id="3913" name="ActivityFarmBuffWishSupport_C2S_Msg" serverName="gamesvr"/>
    <entry id="3914" name="ActivityFarmBuffWishSupport_S2C_Msg" serverName="gamesvr"/>
    <entry id="3915" name="ActivityFarmBuffWishInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3916" name="ActivityFarmBuffWishInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3917" name="ActivityFarmBuffWishSupportList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3918" name="ActivityFarmBuffWishSupportList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3921" name="PrayerCardRewardReachLimitNtf" serverName="gamesvr"/>
    <entry id="3922" name="BatchGetLatestMessageExtraInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3923" name="BatchGetLatestMessageExtraInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3924" name="ActivityFarmBuffWishReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3925" name="ActivityFarmBuffWishReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3928" name="BirthdayRemindInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3929" name="BirthdayRemindInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3930" name="BirthdayRemindClear_C2S_Msg" serverName="gamesvr"/>
    <entry id="3931" name="BirthdayRemindClear_S2C_Msg" serverName="gamesvr"/>
    <entry id="3932" name="CookVisitantServe_C2S_Msg" serverName="gamesvr"/>
    <entry id="3933" name="CookVisitantServe_S2C_Msg" serverName="gamesvr"/>
    <entry id="3936" name="BPDoUpgradeNtf" serverName="gamesvr"/>
    <entry id="3943" name="AMSItemResultNtf" serverName="gamesvr"/>
    <entry id="3944" name="StarPSubmitAssistOrder_C2S_Msg" serverName="gamesvr"/>
    <entry id="3945" name="StarPSubmitAssistOrder_S2C_Msg" serverName="gamesvr"/>
    <entry id="3946" name="StarPFindAssistOrder_C2S_Msg" serverName="gamesvr"/>
    <entry id="3947" name="StarPFindAssistOrder_S2C_Msg" serverName="gamesvr"/>
    <entry id="3948" name="StarPGetAssistOrderList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3949" name="StarPGetAssistOrderList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3950" name="StarPLikeAssistPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="3951" name="StarPLikeAssistPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="3954" name="FarmItemBanSell_C2S_Msg" serverName="gamesvr"/>
    <entry id="3955" name="FarmItemBanSell_S2C_Msg" serverName="gamesvr"/>
    <entry id="3956" name="HouseSwitchRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="3957" name="HouseSwitchRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="3964" name="CookSetFloatingScreenContent_C2S_Msg" serverName="gamesvr"/>
    <entry id="3965" name="CookSetFloatingScreenContent_S2C_Msg" serverName="gamesvr"/>
    <entry id="3968" name="MobaSquadDrawRedPacketRefreshNtf" serverName="gamesvr"/>
    <entry id="3969" name="ActivityFarmReturningTaskInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="3970" name="ActivityFarmReturningTaskInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="3971" name="ActivityFarmReturningTaskReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="3972" name="ActivityFarmReturningTaskReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="3974" name="StarPAssistOrderStatusNtf" serverName="gamesvr"/>
    <entry id="3977" name="GetGlobalRecentPlayRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="3978" name="GetGlobalRecentPlayRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="3979" name="FarmMagicEffectNtf" serverName="gamesvr"/>
    <entry id="3980" name="StarPJoinRecruit_C2S_Msg" serverName="gamesvr"/>
    <entry id="3981" name="StarPJoinRecruit_S2C_Msg" serverName="gamesvr"/>
    <entry id="3983" name="StarPQueryGroupApplication_C2S_Msg" serverName="gamesvr"/>
    <entry id="3984" name="StarPQueryGroupApplication_S2C_Msg" serverName="gamesvr"/>
    <entry id="3985" name="StarPInviteJoinGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="3986" name="StarPInviteJoinGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="3987" name="StarPQueryGroupInvitation_C2S_Msg" serverName="gamesvr"/>
    <entry id="3988" name="StarPQueryGroupInvitation_S2C_Msg" serverName="gamesvr"/>
    <entry id="3989" name="StarPApplyJoinGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="3990" name="StarPApplyJoinGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="3991" name="StarPAcceptInvitation_C2S_Msg" serverName="gamesvr"/>
    <entry id="3992" name="StarPAcceptInvitation_S2C_Msg" serverName="gamesvr"/>
    <entry id="3993" name="StarPAcceptApplication_C2S_Msg" serverName="gamesvr"/>
    <entry id="3994" name="StarPAcceptApplication_S2C_Msg" serverName="gamesvr"/>
    <entry id="3995" name="StarPGetMyContactRecordList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3996" name="StarPGetMyContactRecordList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3997" name="StarPRecommendGroupList_C2S_Msg" serverName="gamesvr"/>
    <entry id="3998" name="StarPRecommendGroupList_S2C_Msg" serverName="gamesvr"/>
    <entry id="3999" name="StarPGroupMemberList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4000" name="StarPGroupMemberList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4003" name="StarPGetPlayerWearItemInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4004" name="StarPGetPlayerWearItemInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4005" name="StarPGetGroupInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4006" name="StarPGetGroupInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4009" name="ArenaHeroStarRewardHeroLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="4010" name="ArenaHeroStarRewardHeroLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="4011" name="ArenaHeroStarRewardGeneralLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="4012" name="ArenaHeroStarRewardGeneralLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="4017" name="SetPackTag_C2S_Msg" serverName="gamesvr"/>
    <entry id="4018" name="SetPackTag_S2C_Msg" serverName="gamesvr"/>
    <entry id="4023" name="StarPGroupSaveSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="4024" name="StarPGroupSaveSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="4025" name="StarPKickGroupMember_C2S_Msg" serverName="gamesvr"/>
    <entry id="4026" name="StarPKickGroupMember_S2C_Msg" serverName="gamesvr"/>
    <entry id="4027" name="HouseSaveLayout_C2S_Msg" serverName="gamesvr"/>
    <entry id="4028" name="HouseSaveLayout_S2C_Msg" serverName="gamesvr"/>
    <entry id="4029" name="HouseGetLayoutList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4030" name="HouseGetLayoutList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4031" name="HouseDelLayout_C2S_Msg" serverName="gamesvr"/>
    <entry id="4032" name="HouseDelLayout_S2C_Msg" serverName="gamesvr"/>
    <entry id="4033" name="StarPSetJoinType_C2S_Msg" serverName="gamesvr"/>
    <entry id="4034" name="StarPSetJoinType_S2C_Msg" serverName="gamesvr"/>
    <entry id="4035" name="StarPSetTitle_C2S_Msg" serverName="gamesvr"/>
    <entry id="4036" name="StarPSetTitle_S2C_Msg" serverName="gamesvr"/>
    <entry id="4037" name="StarPGroupAttrNtf" serverName="gamesvr"/>
    <entry id="4038" name="HouseFurnitureBuyReplace_C2S_Msg" serverName="gamesvr"/>
    <entry id="4039" name="HouseFurnitureBuyReplace_S2C_Msg" serverName="gamesvr"/>
    <entry id="4040" name="FetchBatchInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4041" name="FetchBatchInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4044" name="FetchRankNoByScore_C2S_Msg" serverName="gamesvr"/>
    <entry id="4045" name="FetchRankNoByScore_S2C_Msg" serverName="gamesvr"/>
    <entry id="4046" name="FarmPetFeedDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="4047" name="FarmPetFeedDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="4054" name="FarmQueryRipeTimeBeforeRain_C2S_Msg" serverName="gamesvr"/>
    <entry id="4055" name="FarmQueryRipeTimeBeforeRain_S2C_Msg" serverName="gamesvr"/>
    <entry id="4056" name="UgcOperatePublishCoCreateMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="4057" name="UgcOperatePublishCoCreateMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="4058" name="PressGmAddItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="4059" name="PressGmAddItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="4060" name="HouseGetLayoutDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="4061" name="HouseGetLayoutDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="4062" name="HouseLayoutApplyCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="4063" name="HouseLayoutApplyCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="4064" name="ExchangeShard_C2S_Msg" serverName="gamesvr"/>
    <entry id="4065" name="ExchangeShard_S2C_Msg" serverName="gamesvr"/>
    <entry id="4066" name="StarPBatchGetGuildPublicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4067" name="StarPBatchGetGuildPublicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4068" name="StarPPetTradeUpdateWish_C2S_Msg" serverName="gamesvr"/>
    <entry id="4069" name="StarPPetTradeUpdateWish_S2C_Msg" serverName="gamesvr"/>
    <entry id="4070" name="StarPPetTradeCancelWish_C2S_Msg" serverName="gamesvr"/>
    <entry id="4071" name="StarPPetTradeCancelWish_S2C_Msg" serverName="gamesvr"/>
    <entry id="4072" name="StarPPetTradeFocusPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="4073" name="StarPUpdatePetTradeWish_S2C_Msg" serverName="gamesvr"/>
    <entry id="4074" name="StarPPetTradeCancelFocusPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="4075" name="StarPPetTradeCancelFocusPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="4076" name="StarPPetTradeGetFocusPlayer_C2S_Msg" serverName="gamesvr"/>
    <entry id="4077" name="StarPPetTradeGetFocusPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="4078" name="FarmKirinLevelUp_C2S_Msg" serverName="gamesvr"/>
    <entry id="4079" name="FarmKirinLevelUp_S2C_Msg" serverName="gamesvr"/>
    <entry id="4080" name="FarmKirinIncubate_C2S_Msg" serverName="gamesvr"/>
    <entry id="4081" name="FarmKirinIncubate_S2C_Msg" serverName="gamesvr"/>
    <entry id="4082" name="FarmKirinCollect_C2S_Msg" serverName="gamesvr"/>
    <entry id="4083" name="FarmKirinCollect_S2C_Msg" serverName="gamesvr"/>
    <entry id="4084" name="FarmDelGiftRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="4085" name="FarmDelGiftRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="4086" name="FarmPartyPublish_C2S_Msg" serverName="gamesvr"/>
    <entry id="4087" name="FarmPartyPublish_S2C_Msg" serverName="gamesvr"/>
    <entry id="4089" name="UgcUploadLoading_C2S_Msg" serverName="gamesvr"/>
    <entry id="4090" name="UgcUploadLoading_S2C_Msg" serverName="gamesvr"/>
    <entry id="4091" name="UgcUploadLobbyCover_C2S_Msg" serverName="gamesvr"/>
    <entry id="4092" name="UgcUploadLobbyCover_S2C_Msg" serverName="gamesvr"/>
    <entry id="4093" name="UpdateProfileShowCollection_C2S_Msg" serverName="gamesvr"/>
    <entry id="4094" name="UpdateProfileShowCollection_S2C_Msg" serverName="gamesvr"/>
    <entry id="4095" name="AiNpcAddChatRecordNtf" serverName="gamesvr"/>
    <entry id="4096" name="AiNpcDelChatRecordNtf" serverName="gamesvr"/>
    <entry id="4097" name="AiNpcGetChatRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="4098" name="AiNpcGetChatRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="4101" name="StarPSocInteractionRatio_C2S_Msg" serverName="gamesvr"/>
    <entry id="4102" name="StarPSocInteractionRatio_S2C_Msg" serverName="gamesvr"/>
    <entry id="4103" name="StarPFriendIntimacyStateNtf" serverName="gamesvr"/>
    <entry id="4105" name="StarPCardSend_C2S_Msg" serverName="gamesvr"/>
    <entry id="4106" name="StarPCardSend_S2C_Msg" serverName="gamesvr"/>
    <entry id="4107" name="StarPCardHandle_C2S_Msg" serverName="gamesvr"/>
    <entry id="4108" name="ChatGetStarPModuleInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4109" name="ChatGetStarPModuleInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4110" name="ChatGetAllModuleInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4111" name="ChatGetAllModuleInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4112" name="StarPCardHandle_S2C_Msg" serverName="gamesvr"/>
    <entry id="4113" name="WolfKillTreasureShare_C2S_Msg" serverName="gamesvr"/>
    <entry id="4114" name="WolfKillTreasureShare_S2C_Msg" serverName="gamesvr"/>
    <entry id="4115" name="ArenaCardHotInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4116" name="ArenaCardHotInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4117" name="ArenaArenaCardHotInfoNtf" serverName="gamesvr"/>
    <entry id="4127" name="PlayerBatchPictureSetting_C2S_Msg" serverName="gamesvr"/>
    <entry id="4128" name="PlayerBatchPictureSetting_S2C_Msg" serverName="gamesvr"/>
    <entry id="4129" name="PlayerAlbumPicBatchAtTarget_C2S_Msg" serverName="gamesvr"/>
    <entry id="4130" name="PlayerAlbumPicBatchAtTarget_S2C_Msg" serverName="gamesvr"/>
    <entry id="4131" name="ChatModuleInfoChangeNtf" serverName="gamesvr"/>
    <entry id="4132" name="ChatModuleInfoNtf" serverName="gamesvr"/>
    <entry id="4135" name="CookVisitantCancelProtect_C2S_Msg" serverName="gamesvr"/>
    <entry id="4136" name="CookVisitantCancelProtect_S2C_Msg" serverName="gamesvr"/>
    <entry id="4137" name="ChaseDressUpItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="4138" name="ChasDressUpItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="4148" name="FarmPartyClose_C2S_Msg" serverName="gamesvr"/>
    <entry id="4149" name="FarmPartyClose_S2C_Msg" serverName="gamesvr"/>
    <entry id="4158" name="FarmGetPartyList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4159" name="FarmGetPartyList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4160" name="PlatCommonConfig_C2S_Msg" serverName="configsvr"/>
    <entry id="4161" name="PlatCommonConfig_S2C_Msg" serverName="configsvr"/>
    <entry id="4162" name="ChaseDressUpItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="4171" name="StarPGroupJoinChatGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="4172" name="StarPGroupJoinChatGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="4173" name="StarPGroupGetPetEgg_C2S_Msg" serverName="gamesvr"/>
    <entry id="4174" name="StarPGroupGetPetEgg_S2C_Msg" serverName="gamesvr"/>
    <entry id="4177" name="ArenaSeasonStatInfoNtf" serverName="gamesvr"/>
    <entry id="4178" name="ArenaSeasonStatInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4179" name="ArenaSeasonStatInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4181" name="CookSetEffectiveCustomerFlow_C2S_Msg" serverName="gamesvr"/>
    <entry id="4182" name="CookSetEffectiveCustomerFlow_S2C_Msg" serverName="gamesvr"/>
    <entry id="4183" name="PlayerAlbumPicTargetAtNtf" serverName="gamesvr"/>
    <entry id="4190" name="PuzzleUncover_C2S_Msg" serverName="gamesvr"/>
    <entry id="4191" name="PuzzleUncover_S2C_Msg" serverName="gamesvr"/>
    <entry id="4192" name="PuzzleUncoverAll_C2S_Msg" serverName="gamesvr"/>
    <entry id="4193" name="PuzzleUncoverAll_S2C_Msg" serverName="gamesvr"/>
    <entry id="4196" name="SquadTaskExtraInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4197" name="SquadTaskExtraInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4200" name="ChaseBatchGetGameData_C2S_Msg" serverName="gamesvr"/>
    <entry id="4201" name="ChaseBatchGetGameData_S2C_Msg" serverName="gamesvr"/>
    <entry id="4202" name="FarmMagicUseRecoverItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="4203" name="FarmMagicUseRecoverItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="4204" name="StarPGetGuildCardDetailInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4205" name="StarPGetGuildCardDetailInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4206" name="StarPGMClearRole_C2S_Msg" serverName="gamesvr"/>
    <entry id="4207" name="StarPGMClearRole_S2C_Msg" serverName="gamesvr"/>
    <entry id="4210" name="FarmSetLiuYanMessagePermission_C2S_Msg" serverName="gamesvr"/>
    <entry id="4211" name="FarmSetLiuYanMessagePermission_S2C_Msg" serverName="gamesvr"/>
    <entry id="4212" name="GetTaskPassWordCode_C2S_Msg" serverName="gamesvr"/>
    <entry id="4213" name="GetTaskPassWordCode_S2C_Msg" serverName="gamesvr"/>
    <entry id="4214" name="UseTaskPassWordCode_C2S_Msg" serverName="gamesvr"/>
    <entry id="4215" name="UseTaskPassWordCode_S2C_Msg" serverName="gamesvr"/>
    <entry id="4216" name="PlayerAlbumPictureEdit_C2S_Msg" serverName="gamesvr"/>
    <entry id="4217" name="PlayerAlbumPictureEdit_S2C_Msg" serverName="gamesvr"/>
    <entry id="4218" name="PlayerAlbumPictureTextCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="4219" name="PlayerAlbumPictureText_S2C_Msg" serverName="gamesvr"/>
    <entry id="4221" name="CookCalculateDishIncome_C2S_Msg" serverName="gamesvr"/>
    <entry id="4222" name="CookCalculateDishIncome_S2C_Msg" serverName="gamesvr"/>
    <entry id="4223" name="CookCalculateCookOpenIncome_C2S_Msg" serverName="gamesvr"/>
    <entry id="4224" name="CookCalculateCookOpenIncome_S2C_Msg" serverName="gamesvr"/>
    <entry id="4225" name="PlayerAlbumPictureTextCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="4226" name="StarPJoinGuildSuccNtf" serverName="gamesvr"/>
    <entry id="4227" name="HouseRoomInfoNtf" serverName="gamesvr"/>
    <entry id="4228" name="UgcGetCreatorBadge_C2S_Msg" serverName="gamesvr"/>
    <entry id="4229" name="UgcGetCreatorBadge_S2C_Msg" serverName="gamesvr"/>
    <entry id="4230" name="UgcActiveCreatorBadgeNtf" serverName="gamesvr"/>
    <entry id="4231" name="UgcSetCreatorHomePage_C2S_Msg" serverName="gamesvr"/>
    <entry id="4232" name="UgcSetCreatorHomePage_S2C_Msg" serverName="gamesvr"/>
    <entry id="4233" name="UgcGetCreatorHomePage_C2S_Msg" serverName="gamesvr"/>
    <entry id="4234" name="UgcGetCreatorHomePage_S2C_Msg" serverName="gamesvr"/>
    <entry id="4238" name="UgcMatchLobbyDetailExMulti_C2S_Msg" serverName="gamesvr"/>
    <entry id="4239" name="UgcMatchLobbyDetailExMulti_S2C_Msg" serverName="gamesvr"/>
    <entry id="4240" name="UgcMatchLobbyDetailExChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="4241" name="UgcMatchLobbyDetailExChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="4242" name="UgcMatchLobbyDetailPlayRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="4243" name="UgcMatchLobbyDetailPlayRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="4244" name="CocGMCommand_C2S_Msg" serverName="gamesvr"/>
    <entry id="4245" name="CocGMCommand_S2C_Msg" serverName="gamesvr"/>
    <entry id="4252" name="PlayerAlbumPicInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4253" name="PlayerAlbumPicInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4255" name="FarmPetTame_C2S_Msg" serverName="gamesvr"/>
    <entry id="4256" name="FarmPetTame_S2C_Msg" serverName="gamesvr"/>
    <entry id="4259" name="CookSendCommentFood_C2S_Msg" serverName="gamesvr"/>
    <entry id="4260" name="CookSendCommentFood_S2C_Msg" serverName="gamesvr"/>
    <entry id="4263" name="GetSeasonReviewRedPointInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4264" name="GetSeasonReviewRedPointInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4265" name="UpdatedSeasonReviewRedPointInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4266" name="UpdatedSeasonReviewRedPointInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4278" name="CookFetchVisitantInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4279" name="CookFetchVisitantInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4284" name="FarmPetCatFishing_C2S_Msg" serverName="gamesvr"/>
    <entry id="4285" name="FarmPetCatFishing_S2C_Msg" serverName="gamesvr"/>
    <entry id="4288" name="PlayerTargetedLoadingTextPushCount_C2S_Msg" serverName="gamesvr"/>
    <entry id="4289" name="PlayerTargetedLoadingTextPushCount_S2C_Msg" serverName="gamesvr"/>
    <entry id="4292" name="CocBuildingRevamp_C2S_Msg" serverName="gamesvr"/>
    <entry id="4293" name="CocBuildingRevamp_S2C_Msg" serverName="gamesvr"/>
    <entry id="4294" name="ClientSetDSParam_C2S_Msg" serverName="gamesvr"/>
    <entry id="4295" name="ClientSetDSParam_S2C_Msg" serverName="gamesvr"/>
    <entry id="4296" name="CocBuildingAssignVillager_C2S_Msg" serverName="gamesvr"/>
    <entry id="4297" name="CocBuildingAssignVillager_S2C_Msg" serverName="gamesvr"/>
    <entry id="4298" name="CocBuildingUnassignVillager_C2S_Msg" serverName="gamesvr"/>
    <entry id="4299" name="CocBuildingUnassignVillager_S2C_Msg" serverName="gamesvr"/>
    <entry id="4300" name="FarmBatchReadSignature_C2S_Msg" serverName="gamesvr"/>
    <entry id="4301" name="FarmBatchReadSignature_S2C_Msg" serverName="gamesvr"/>
    <entry id="4302" name="ReturnActivityOfflineReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4303" name="ReturnActivityOfflineReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4305" name="ActivityGetMultiPlayerSquadIsFull_C2S_Msg" serverName="gamesvr"/>
    <entry id="4306" name="ActivityGetMultiPlayerSquadIsFull_S2C_Msg" serverName="gamesvr"/>
    <entry id="4307" name="OpenGiftPackageAnimationNtf" serverName="gamesvr"/>
    <entry id="4318" name="GameModeReturnInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4319" name="GameModeReturnInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4320" name="GameModeReturnStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="4321" name="GameModeReturnStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="4322" name="GameModeReturnStateChangeNtf" serverName="gamesvr"/>
    <entry id="4323" name="GameModeReturnFinishNtf" serverName="gamesvr"/>
    <entry id="4324" name="ChangeTaskOptionReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4325" name="ChangeTaskOptionReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4332" name="AchievementSortGetList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4333" name="AchievementSortGetList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4336" name="Nr3e8Common_C2S_Msg" serverName="gamesvr"/>
    <entry id="4337" name="Nr3e8Common_S2C_Msg" serverName="gamesvr"/>
    <entry id="4338" name="Nr3e8BoardEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="4339" name="Nr3e8BoardEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="4340" name="Nr3e8BoardExit_C2S_Msg" serverName="gamesvr"/>
    <entry id="4341" name="Nr3e8BoardExit_S2C_Msg" serverName="gamesvr"/>
    <entry id="4342" name="Nr3e8CommonNtf" serverName="gamesvr"/>
    <entry id="4343" name="Nr3e8TaskRewardRecv_C2S_Msg" serverName="gamesvr"/>
    <entry id="4344" name="Nr3e8TaskRewardRecv_S2C_Msg" serverName="gamesvr"/>
    <entry id="4345" name="Nr3e8TaskWeekActivityRewardRecv_C2S_Msg" serverName="gamesvr"/>
    <entry id="4346" name="Nr3e8TaskWeekActivityRewardRecv_S2C_Msg" serverName="gamesvr"/>
    <entry id="4347" name="Nr3e8TaskRead_C2S_Msg" serverName="gamesvr"/>
    <entry id="4348" name="Nr3e8TaskRead_S2C_Msg" serverName="gamesvr"/>
    <entry id="4349" name="WolfKillShieldVocation_C2S_Msg" serverName="gamesvr"/>
    <entry id="4350" name="WolfKillShieldVocation_S2C_Msg" serverName="gamesvr"/>
    <entry id="4351" name="GetWolfKillShieldVocation_C2S_Msg" serverName="gamesvr"/>
    <entry id="4352" name="GetWolfKillShieldVocation_S2C_Msg" serverName="gamesvr"/>
    <entry id="4353" name="GetArenaBlockInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4354" name="GetArenaBlockInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4355" name="CookEvictBadGuy_C2S_Msg" serverName="gamesvr"/>
    <entry id="4356" name="CookEvictBadGuy_S2C_Msg" serverName="gamesvr"/>
    <entry id="4361" name="GameModeReturnOpenedSlapFace_C2S_Msg" serverName="gamesvr"/>
    <entry id="4362" name="GameModeReturnOpenedSlapFace_S2C_Msg" serverName="gamesvr"/>
    <entry id="4363" name="FarmTagRedFox_C2S_Msg" serverName="gamesvr"/>
    <entry id="4364" name="FarmTagRedFox_S2C_Msg" serverName="gamesvr"/>
    <entry id="4365" name="FarmTaskFinishRemainTasks_C2S_Msg" serverName="gamesvr"/>
    <entry id="4366" name="FarmTaskFinishRemainTasks_S2C_Msg" serverName="gamesvr"/>
    <entry id="4391" name="SetAppearanceRoadShow_C2S_Msg" serverName="gamesvr"/>
    <entry id="4392" name="SetAppearanceRoadShow_S2C_Msg" serverName="gamesvr"/>
    <entry id="4393" name="SingleBattleStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="4394" name="SingleBattleStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="4395" name="SingleBattleSettlement_C2S_Msg" serverName="gamesvr"/>
    <entry id="4396" name="SingleBattleSettlement_S2C_Msg" serverName="gamesvr"/>
    <entry id="4397" name="UgcQuickJoinWithMidJoin_C2S_Msg" serverName="gamesvr"/>
    <entry id="4398" name="UgcQuickJoinWithMidJoin_S2C_Msg" serverName="gamesvr"/>
    <entry id="4399" name="UgcQuickJoinWithMidJoinResultNtf" serverName="gamesvr"/>
    <entry id="4406" name="GameModeReturnClearRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="4407" name="GameModeReturnClearRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="4408" name="FarmGMCommand_C2S_Msg" serverName="gamesvr"/>
    <entry id="4409" name="FarmGMCommand_S2C_Msg" serverName="gamesvr"/>
    <entry id="4412" name="CocForwardTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="4413" name="CocForwardTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="4414" name="GetCheckInManualFarmDailyBuys_C2S_Msg" serverName="gamesvr"/>
    <entry id="4415" name="GetCheckInManualFarmDailyBuys_S2C_Msg" serverName="gamesvr"/>
    <entry id="4417" name="CocForwardOneWayTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="4418" name="CocForwardOneWayTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="4419" name="CocDSInfoNtf" serverName="gamesvr"/>
    <entry id="4422" name="CocAttrNtf" serverName="gamesvr"/>
    <entry id="4426" name="QuerySecondaryNewbieRelatedMatchTypeBattleCnt_C2S_Msg" serverName="gamesvr"/>
    <entry id="4427" name="QuerySecondaryNewbieRelatedMatchTypeBattleCnt_S2C_Msg" serverName="gamesvr"/>
    <entry id="4428" name="CocHomeKickNtf" serverName="gamesvr"/>
    <entry id="4429" name="CocUpgradeHallOfVillager_C2S_Msg" serverName="gamesvr"/>
    <entry id="4430" name="CocUpgradeHallOfVillager_S2C_Msg" serverName="gamesvr"/>
    <entry id="4431" name="CocAccelerateUpgradingHallOfVillager_C2S_Msg" serverName="gamesvr"/>
    <entry id="4432" name="CocAccelerateUpgradingHallOfVillager_S2C_Msg" serverName="gamesvr"/>
    <entry id="4433" name="CocPayUnlockStudentPosition_C2S_Msg" serverName="gamesvr"/>
    <entry id="4434" name="CocPayUnlockStudentPosition_S2C_Msg" serverName="gamesvr"/>
    <entry id="4435" name="CocPutStudentIn_C2S_Msg" serverName="gamesvr"/>
    <entry id="4436" name="CocPutStudentIn_S2C_Msg" serverName="gamesvr"/>
    <entry id="4439" name="BagDressUpSetting_S2C_Msg" serverName="gamesvr"/>
    <entry id="4456" name="UgcHomePageGetLikePlayHotTag_C2S_Msg" serverName="gamesvr"/>
    <entry id="4457" name="UgcHomePageGetLikePlayHotTag_S2C_Msg" serverName="gamesvr"/>
    <entry id="4458" name="UgcHomePageSetLikePlayHotTag_C2S_Msg" serverName="gamesvr"/>
    <entry id="4459" name="UgcHomePageSetLikePlayHotTag_S2C_Msg" serverName="gamesvr"/>
    <entry id="4460" name="PlayerIntimateRelationRedPoint_C2S_Msg" serverName="gamesvr"/>
    <entry id="4461" name="PlayerIntimateRelationRedPoint_S2C_Msg" serverName="gamesvr"/>
    <entry id="4462" name="CocForwardCallerTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="4463" name="CocForwardCallerTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="4464" name="BagDressUpSetting_C2S_Msg" serverName="gamesvr"/>
    <entry id="4465" name="GiftRedUpdateDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="4466" name="GiftRedUpdateDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="4467" name="ChatBindModule_C2S_Msg" serverName="gamesvr"/>
    <entry id="4468" name="ChatBindModule_S2C_Msg" serverName="gamesvr"/>
    <entry id="4469" name="ChatUnbindModule_C2S_Msg" serverName="gamesvr"/>
    <entry id="4470" name="ChatUnbindModule_S2C_Msg" serverName="gamesvr"/>
    <entry id="4471" name="InflateRedPacketQuit_C2S_Msg" serverName="gamesvr"/>
    <entry id="4472" name="InflateRedPacketQuit_S2C_Msg" serverName="gamesvr"/>
    <entry id="4473" name="InflateRedPacketKickOut_C2S_Msg" serverName="gamesvr"/>
    <entry id="4474" name="InflateRedPacketKickOut_S2C_Msg" serverName="gamesvr"/>
    <entry id="4475" name="InflateRedPacketInflate_C2S_Msg" serverName="gamesvr"/>
    <entry id="4476" name="InflateRedPacketInflate_S2C_Msg" serverName="gamesvr"/>
    <entry id="4477" name="InflateRedPacketReceive_C2S_Msg" serverName="gamesvr"/>
    <entry id="4478" name="InflateRedPacketReceive_S2C_Msg" serverName="gamesvr"/>
    <entry id="4479" name="InflateRedPacketUpdateNtf" serverName="gamesvr"/>
    <entry id="4480" name="MallActivityInflateRedPacketPurchase_C2S_Msg" serverName="gamesvr"/>
    <entry id="4481" name="MallActivityInflateRedPacketPurchase_S2C_Msg" serverName="gamesvr"/>
    <entry id="4484" name="PlayerHallStableState_C2S_Msg" serverName="gamesvr"/>
    <entry id="4485" name="PlayerHallStableState_S2C_Msg" serverName="gamesvr"/>
    <entry id="4486" name="GetWolfKillMonthCardInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4487" name="GetWolfKillMonthCardInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4488" name="OpenWolfKillMonth_C2S_Msg" serverName="gamesvr"/>
    <entry id="4489" name="OpenWolfKillMonth_S2C_Msg" serverName="gamesvr"/>
    <entry id="4490" name="GetWolfKillMonthFreeGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="4491" name="GetWolfKillMonthFreeGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="4508" name="WolfKillMonthCardNtf" serverName="gamesvr"/>
    <entry id="4509" name="MatchRecommendList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4510" name="MatchRecommendList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4511" name="SimulateLimitNtf" serverName="gamesvr"/>
    <entry id="4512" name="StarPChooseInitEquipSuit_C2S_Msg" serverName="gamesvr"/>
    <entry id="4513" name="StarPChooseInitEquipSuit_S2C_Msg" serverName="gamesvr"/>
    <entry id="4514" name="StarPGetUnlockData_C2S_Msg" serverName="gamesvr"/>
    <entry id="4515" name="StarPGetUnlockData_S2C_Msg" serverName="gamesvr"/>
    <entry id="4516" name="StarPGetChatGroupKey_C2S_Msg" serverName="gamesvr"/>
    <entry id="4517" name="StarPGetChatGroupKey_S2C_Msg" serverName="gamesvr"/>
    <entry id="4518" name="StarPPetTradeFocusPlayer_S2C_Msg" serverName="gamesvr"/>
    <entry id="4519" name="StarPTipsNtf" serverName="gamesvr"/>
    <entry id="4520" name="StarPJoinGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="4521" name="StarPJoinGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="4522" name="StarPLeaveGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="4523" name="StarPLeaveGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="4524" name="StarPDismissGroup_C2S_Msg" serverName="gamesvr"/>
    <entry id="4525" name="StarPDismissGroup_S2C_Msg" serverName="gamesvr"/>
    <entry id="4526" name="StarPPvpStartFailNtf" serverName="gamesvr"/>
    <entry id="4541" name="UpdatePlayerLimitExperienceRedPoint_C2S_Msg" serverName="gamesvr"/>
    <entry id="4542" name="UpdatePlayerLimitExperienceRedPoint_S2C_Msg" serverName="gamesvr"/>
    <entry id="4557" name="FarmGetNewItemNtf" serverName="gamesvr"/>
    <entry id="4558" name="GetBiHudRecommendMapId_C2S_Msg" serverName="gamesvr"/>
    <entry id="4559" name="GetBiHudRecommendMapId_S2C_Msg" serverName="gamesvr"/>
    <entry id="4560" name="GetFarmDailyReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4561" name="GetFarmDailyReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4562" name="UpgradeFarmDailyReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4563" name="UpgradeFarmDailyReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4566" name="TeamRecruitPublishWithModeType_C2S_Msg" serverName="gamesvr"/>
    <entry id="4567" name="TeamRecruitPublishWithModeType_S2C_Msg" serverName="gamesvr"/>
    <entry id="4568" name="TeamRecruitQueryByModeType_C2S_Msg" serverName="gamesvr"/>
    <entry id="4569" name="TeamRecruitQueryByModeType_S2C_Msg" serverName="gamesvr"/>
    <entry id="4575" name="FarmGetCloudTax_C2S_Msg" serverName="gamesvr"/>
    <entry id="4576" name="FarmGetCloudTax_S2C_Msg" serverName="gamesvr"/>
    <entry id="4577" name="UgcRoomGetPlayerRecommendMaps_C2S_Msg" serverName="gamesvr"/>
    <entry id="4578" name="UgcRoomGetPlayerRecommendMaps_S2C_Msg" serverName="gamesvr"/>
    <entry id="4579" name="UgcRoomGetOfficalRecommendMaps_C2S_Msg" serverName="gamesvr"/>
    <entry id="4580" name="UgcRoomGetOfficalRecommendMaps_S2C_Msg" serverName="gamesvr"/>
    <entry id="4582" name="FarmStealingMyRecord_C2S_Msg" serverName="gamesvr"/>
    <entry id="4583" name="FarmStealingMyRecord_S2C_Msg" serverName="gamesvr"/>
    <entry id="4611" name="UpgradeFarmDailyAwardNtf" serverName="gamesvr"/>
    <entry id="4616" name="FarmSetBeFertilizedType_C2S_Msg" serverName="gamesvr"/>
    <entry id="4617" name="FarmSetBeFertilizedType_S2C_Msg" serverName="gamesvr"/>
    <entry id="4618" name="UpgradeInFarmDailyActivityPage_C2S_Msg" serverName="gamesvr"/>
    <entry id="4619" name="UpgradeInFarmDailyActivityPage_S2C_Msg" serverName="gamesvr"/>
    <entry id="4620" name="UgcCoCreateMultiEditApply_C2S_Msg" serverName="gamesvr"/>
    <entry id="4621" name="UgcCoCreateMultiEditApply_S2C_Msg" serverName="gamesvr"/>
    <entry id="4622" name="UgcCoCreateMultiEditApplyNtf" serverName="gamesvr"/>
    <entry id="4623" name="UgcCoCreateMultiEditReply_C2S_Msg" serverName="gamesvr"/>
    <entry id="4624" name="UgcCoCreateMultiEditReply_S2C_Msg" serverName="gamesvr"/>
    <entry id="4625" name="UgcCoCreateMultiEditReplyNtf" serverName="gamesvr"/>
    <entry id="4626" name="UgcCoCreateMultiEditRejectEnter_C2S_Msg" serverName="gamesvr"/>
    <entry id="4627" name="UgcCoCreateMultiEditRejectEnter_S2C_Msg" serverName="gamesvr"/>
    <entry id="4628" name="UgcCoCreateMultiEditRejectEnterNtf" serverName="gamesvr"/>
    <entry id="4629" name="GamePakDetailInfoReport_C2S_Msg" serverName="gamesvr"/>
    <entry id="4630" name="GamePakDetailInfoReport_S2C_Msg" serverName="gamesvr"/>
    <entry id="4633" name="FarmOpenGodUI_C2S_Msg" serverName="gamesvr"/>
    <entry id="4634" name="FarmOpenGodUI_S2C_Msg" serverName="gamesvr"/>
    <entry id="4635" name="UgcCoCreateMultiEditCodingData_C2S_Msg" serverName="gamesvr"/>
    <entry id="4636" name="UgcCoCreateMultiEditCodingData_S2C_Msg" serverName="gamesvr"/>
    <entry id="4637" name="UgcCoCreateMultiEditDataUpdateNtf" serverName="gamesvr"/>
    <entry id="4642" name="ChatClearAllNotRead_C2S_Msg" serverName="gamesvr"/>
    <entry id="4643" name="ChatClearAllNotRead_S2C_Msg" serverName="gamesvr"/>
    <entry id="4647" name="WolfKillLevel_C2S_Msg" serverName="gamesvr"/>
    <entry id="4648" name="WolfKillLevel_S2C_Msg" serverName="gamesvr"/>
    <entry id="4649" name="PlayerDanceScore_C2S_Msg" serverName="gamesvr"/>
    <entry id="4650" name="PlayerDanceScore_S2C_Msg" serverName="gamesvr"/>
    <entry id="4653" name="UgcAiChangeActionState_C2S_Msg" serverName="gamesvr"/>
    <entry id="4654" name="UgcAiChangeActionState_S2C_Msg" serverName="gamesvr"/>
    <entry id="4655" name="RoomCancelPreStart_C2S_Msg" serverName="gamesvr"/>
    <entry id="4656" name="RoomCancelPreStart_S2C_Msg" serverName="gamesvr"/>
    <entry id="4657" name="AiNpcChatPushNoticeNtf" serverName="gamesvr"/>
    <entry id="4658" name="BattleSceneSwitchNtf" serverName="gamesvr"/>
    <entry id="4659" name="ExitBattleNtf" serverName="gamesvr"/>
    <entry id="4660" name="FlashRaceCheeringAi_C2S_Msg" serverName="gamesvr"/>
    <entry id="4661" name="FlashRaceCheeringAi_S2C_Msg" serverName="gamesvr"/>
    <entry id="4662" name="FlashRaceCheeringVoteOption_C2S_Msg" serverName="gamesvr"/>
    <entry id="4663" name="FlashRaceCheeringVoteOption_S2C_Msg" serverName="gamesvr"/>
    <entry id="4665" name="UgcSubscribeRecommendPage_C2S_Msg" serverName="gamesvr"/>
    <entry id="4666" name="UgcSubscribeRecommendPage_S2C_Msg" serverName="gamesvr"/>
    <entry id="4667" name="FarmBadGuyCloudTaxDetail_C2S_Msg" serverName="gamesvr"/>
    <entry id="4668" name="FarmBadGuyCloudTaxDetail_S2C_Msg" serverName="gamesvr"/>
    <entry id="4687" name="ActivityLevelUpChallengeCrit_C2S_Msg" serverName="gamesvr"/>
    <entry id="4688" name="ActivityLevelUpChallengeCrit_S2C_Msg" serverName="gamesvr"/>
    <entry id="4711" name="CompetitionEntranceStatusChangeNtf" serverName="gamesvr"/>
    <entry id="4712" name="HouseBuySampleRoom_C2S_Msg" serverName="gamesvr"/>
    <entry id="4713" name="HouseBuySampleRoom_S2C_Msg" serverName="gamesvr"/>
    <entry id="4714" name="GetWolfKillMMRScore_C2S_Msg" serverName="gamesvr"/>
    <entry id="4715" name="GetWolfKillMMRScore_S2C_Msg" serverName="gamesvr"/>
    <entry id="4716" name="ABTestInfoBatchQuery_C2S_Msg" serverName="gamesvr"/>
    <entry id="4717" name="ABTestInfoBatchQuery_S2C_Msg" serverName="gamesvr"/>
    <entry id="4718" name="FashionSkillUse_C2S_Msg" serverName="gamesvr"/>
    <entry id="4719" name="FashionSkillUse_S2C_Msg" serverName="gamesvr"/>
    <entry id="4735" name="HOKShowAiInvite_C2S_Msg" serverName="gamesvr"/>
    <entry id="4736" name="HOKShowAiInvite_S2C_Msg" serverName="gamesvr"/>
    <entry id="4737" name="UgcRoomPlayerRecommendMap_C2S_Msg" serverName="gamesvr"/>
    <entry id="4738" name="UgcRoomPlayerRecommendMap_S2C_Msg" serverName="gamesvr"/>
    <entry id="4739" name="UgcRoomPlayerRecommendMapNtf" serverName="gamesvr"/>
    <entry id="4740" name="UgcGetStarWorldEntranceBubble_C2S_Msg" serverName="gamesvr"/>
    <entry id="4741" name="UgcGetStarWorldEntranceBubble_S2C_Msg" serverName="gamesvr"/>
    <entry id="4742" name="UgcRemoveStarWorldEntranceBubble_C2S_Msg" serverName="gamesvr"/>
    <entry id="4743" name="UgcRemoveStarWorldEntranceBubble_S2C_Msg" serverName="gamesvr"/>
    <entry id="4746" name="MobaFootballGoalBroadcastNtf" serverName="gamesvr"/>
    <entry id="4751" name="CupsCycleOpenNtf" serverName="gamesvr"/>
    <entry id="4752" name="CupsCycleUnLockNtf" serverName="gamesvr"/>
    <entry id="4753" name="ChangeCupsCycle_C2S_Msg" serverName="gamesvr"/>
    <entry id="4754" name="ChangeCupsCycle_S2C_Msg" serverName="gamesvr"/>
    <entry id="4755" name="UpdatePlayerGearSetting_C2S_Msg" serverName="gamesvr"/>
    <entry id="4756" name="UpdatePlayerGearSetting_S2C_Msg" serverName="gamesvr"/>
    <entry id="4759" name="FarmCanStealNtf" serverName="gamesvr"/>
    <entry id="4760" name="UgcAiImageCheck_C2S_Msg" serverName="gamesvr"/>
    <entry id="4761" name="UgcAiImageCheck_S2C_Msg" serverName="gamesvr"/>
    <entry id="4762" name="ItemChangeNtf" serverName="gamesvr"/>
    <entry id="4763" name="HOKShowAiInviteNtf" serverName="gamesvr"/>
    <entry id="4766" name="CupsCycleBeginOpenNtf" serverName="gamesvr"/>
    <entry id="4767" name="CupsCycleClickRedDot_C2S_Msg" serverName="gamesvr"/>
    <entry id="4768" name="CupsCycleClickRedDot_S2C_Msg" serverName="gamesvr"/>
    <entry id="4773" name="AnimeDressOutlineSet_C2S_Msg" serverName="gamesvr"/>
    <entry id="4774" name="AnimeDressOutlineSet_S2C_Msg" serverName="gamesvr"/>
    <entry id="4775" name="ArenaCardSuitInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4776" name="ArenaCardSuitInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4777" name="ArenaCardSuitInfoNtf" serverName="gamesvr"/>
    <entry id="4779" name="VehicleAccessoriesItem_C2S_Msg" serverName="gamesvr"/>
    <entry id="4780" name="VehicleAccessoriesItem_S2C_Msg" serverName="gamesvr"/>
    <entry id="4783" name="RoomRecommendListWithModeType_C2S_Msg" serverName="gamesvr"/>
    <entry id="4784" name="RoomRecommendListWithModeType_S2C_Msg" serverName="gamesvr"/>
    <entry id="4785" name="ClientLogEndNtf" serverName="gamesvr"/>
    <entry id="4786" name="ConfigSvrGM_C2S_Msg" serverName="configsvr"/>
    <entry id="4787" name="ConfigSvrGM_S2C_Msg" serverName="configsvr"/>
    <entry id="4794" name="GameModeReturnDrawProgressReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4795" name="GameModeReturnDrawProgressReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4796" name="GameModeRewardDrawInnReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4797" name="GameModeRewardDrawInnReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4808" name="MailPurchased_C2S_Msg" serverName="gamesvr"/>
    <entry id="4809" name="MailPurchased_S2C_Msg" serverName="gamesvr"/>
    <entry id="4810" name="MailBuyInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4811" name="MailBuyInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4812" name="GameModeReturnBISort_C2S_Msg" serverName="gamesvr"/>
    <entry id="4813" name="GameModeReturnBISort_S2C_Msg" serverName="gamesvr"/>
    <entry id="4815" name="GetWeekendGiftReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4816" name="GetWeekendGiftReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4817" name="UpgradeWeekendGift_C2S_Msg" serverName="gamesvr"/>
    <entry id="4818" name="UpgradeWeekendGift_S2C_Msg" serverName="gamesvr"/>
    <entry id="4819" name="RoomModifyMatchingTeamInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4820" name="RoomModifyMatchingTeamInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4821" name="RoomModifyMatchingTeamInfoResultNtf" serverName="gamesvr"/>
    <entry id="4822" name="UgcMatchLobbySummery_C2S_Msg" serverName="gamesvr"/>
    <entry id="4823" name="UgcMatchLobbySummery_S2C_Msg" serverName="gamesvr"/>
    <entry id="4824" name="GetWeekTopPlayGroupId_C2S_Msg" serverName="gamesvr"/>
    <entry id="4825" name="GetWeekTopPlayGroupId_S2C_Msg" serverName="gamesvr"/>
    <entry id="4826" name="GetDayPlayedGroupId_C2S_Msg" serverName="gamesvr"/>
    <entry id="4827" name="GetDayPlayedGroupId_S2C_Msg" serverName="gamesvr"/>
    <entry id="4828" name="ChaseSetSettings_C2S_Msg" serverName="gamesvr"/>
    <entry id="4829" name="ChaseSetSettings_S2C_Msg" serverName="gamesvr"/>
    <entry id="4830" name="UgcMatchLobbySummerySpecified_C2S_Msg" serverName="gamesvr"/>
    <entry id="4831" name="UgcMatchLobbySummerySpecified_S2C_Msg" serverName="gamesvr"/>
    <entry id="4834" name="DisplayBoardClear_C2S_Msg" serverName="gamesvr"/>
    <entry id="4835" name="DisplayBoardClear_S2C_Msg" serverName="gamesvr"/>
    <entry id="4836" name="DisplayBoardHistorySetting_C2S_Msg" serverName="gamesvr"/>
    <entry id="4837" name="DisplayBoardHistorySetting_S2C_Msg" serverName="gamesvr"/>
    <entry id="4838" name="DisplayBoardUpdateNtf" serverName="gamesvr"/>
    <entry id="4839" name="DisplayBoardNewRedDotRemove_C2S_Msg" serverName="gamesvr"/>
    <entry id="4840" name="DisplayBoardNewRedDotRemove_S2C_Msg" serverName="gamesvr"/>
    <entry id="4845" name="AddForceOpenMatchType_C2S_Msg" serverName="gamesvr"/>
    <entry id="4846" name="AddForceOpenMatchType_S2C_Msg" serverName="gamesvr"/>
    <entry id="4847" name="SelfFarmCsForwardTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="4848" name="SelfFarmCsForwardTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="4849" name="CurrFarmCsForwardTest_C2S_Msg" serverName="gamesvr"/>
    <entry id="4850" name="CurrFarmCsForwardTest_S2C_Msg" serverName="gamesvr"/>
    <entry id="4851" name="ActivityTreasureHuntExcavate_C2S_Msg" serverName="gamesvr"/>
    <entry id="4852" name="ActivityTreasureHuntExcavate_S2C_Msg" serverName="gamesvr"/>
    <entry id="4853" name="ActivityTreasureHuntSell_C2S_Msg" serverName="gamesvr"/>
    <entry id="4854" name="ActivityTreasureHuntSell_S2C_Msg" serverName="gamesvr"/>
    <entry id="4855" name="ActivityTreasureHuntAttrUpdateNtf" serverName="gamesvr"/>
    <entry id="4856" name="GetMasterPatchInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4857" name="GetMasterPatchInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4858" name="ReceiveMasterPatchReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4859" name="MasterPatchUnLockNtf" serverName="gamesvr"/>
    <entry id="4860" name="AddPicToPicWall_C2S_Msg" serverName="gamesvr"/>
    <entry id="4861" name="AddPicToPicWall_S2C_Msg" serverName="gamesvr"/>
    <entry id="4862" name="DelPicFromPicWall_C2S_Msg" serverName="gamesvr"/>
    <entry id="4863" name="DelPicFromPicWall_S2C_Msg" serverName="gamesvr"/>
    <entry id="4864" name="PlayerPicWallLike_C2S_Msg" serverName="gamesvr"/>
    <entry id="4865" name="PlayerPicWallLike_S2C_Msg" serverName="gamesvr"/>
    <entry id="4866" name="GetPicWallTopLikeList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4867" name="GetPicWallTopLikeList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4868" name="GetPicWallLatestList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4869" name="GetPicWallLatestList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4871" name="ReceiveMasterPatchReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4872" name="DisplayBoardSave_C2S_Msg" serverName="gamesvr"/>
    <entry id="4873" name="DisplayBoardSave_S2C_Msg" serverName="gamesvr"/>
    <entry id="4874" name="DisplayBoardOpen_C2S_Msg" serverName="gamesvr"/>
    <entry id="4875" name="DisplayBoardOpen_S2C_Msg" serverName="gamesvr"/>
    <entry id="4878" name="ItaBagBadgeEquip_C2S_Msg" serverName="gamesvr"/>
    <entry id="4879" name="ItaBagBadgeEquip_S2C_Msg" serverName="gamesvr"/>
    <entry id="4882" name="DressUpDetailInfoChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="4883" name="DressUpDetailInfoChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="4884" name="UpdateItemInfoDbItemShowStatus_C2S_Msg" serverName="gamesvr"/>
    <entry id="4885" name="UpdateItemInfoDbItemShowStatus_S2C_Msg" serverName="gamesvr"/>
    <entry id="4886" name="DressItemShowStatusChange_C2S_Msg" serverName="gamesvr"/>
    <entry id="4887" name="DressItemShowStatusChange_S2C_Msg" serverName="gamesvr"/>
    <entry id="4910" name="GetPicWallMyPicList_C2S_Msg" serverName="gamesvr"/>
    <entry id="4911" name="GetPicWallMyPicList_S2C_Msg" serverName="gamesvr"/>
    <entry id="4914" name="NewTradingCardGetNtf" serverName="gamesvr"/>
    <entry id="4915" name="ChaseGetIdentityProficiencyInfo_C2S_Msg" serverName="gamesvr"/>
    <entry id="4916" name="ChaseGetIdentityProficiencyInfo_S2C_Msg" serverName="gamesvr"/>
    <entry id="4917" name="ChaseIdentityBattlePerformance_C2S_Msg" serverName="gamesvr"/>
    <entry id="4918" name="ChaseIdentityBattlePerformance_S2C_Msg" serverName="gamesvr"/>
    <entry id="4919" name="ChaseIdentityDrawProficiencyProgressReward_C2S_Msg" serverName="gamesvr"/>
    <entry id="4920" name="ChaseIdentityDrawProficiencyProgressReward_S2C_Msg" serverName="gamesvr"/>
    <entry id="4921" name="MasterPatchChangeNtf" serverName="gamesvr"/>
    <entry id="4935" name="ChaseIdentityReadBiography_C2S_Msg" serverName="gamesvr"/>
    <entry id="4936" name="ChaseIdentityReadBiography_S2C_Msg" serverName="gamesvr"/>
    <entry id="4947" name="WeekendGiftBuySuccessNtf" serverName="gamesvr"/>
    <entry id="4948" name="WeekendGiftCheckInNtf" serverName="gamesvr"/>
</root>