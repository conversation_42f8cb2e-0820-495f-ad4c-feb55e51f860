syntax = "proto3";
package competition;

import "irpc_field_option.proto";
option java_package = "com.tencent.wea.g6.irpc.proto.competition";

import "common.proto";
import "g6_common.proto";
import "rich_common.proto";
import "ds_roomsvr.proto";
import "competition_common.proto";
import "ResNR3E1LevelProp.proto";

// 赛事通用协议
message CompetitionCommonReq {
  uint32 cmd = 1; // CMD枚举值: CompetitionCommonC2SCmd
  bytes req_body = 2; //业务包体
  CompetitionHeader header = 3;
}

message CompetitionCommonRes {
  uint32 cmd = 1; // CMD枚举值: CompetitionCommonC2SCmd
  int32 err_code = 2; // 错误码，0为成功
  string err_msg = 3; // 错误码描述信息
  bytes rsp_body = 4; // 业务回包
  int64 timestamp = 5; // 服务器时间(unix时间戳,单位毫秒)
  int64 trace_id = 6;
}

// Header参数
message CompetitionHeader {
  uint64 uid = 1;
  string open_id = 2;
  uint32 area = 3;
  uint32 partition = 4;
  string env = 5;
  string gopen_id = 6;
  uint64 timestamp = 7;
  uint32 plat_id = 8;
  uint32 login_type = 9;
  string device_id = 10;
  com.tencent.wea.protocol.MemberBaseInfo member_base_info = 100; // 用户基本信息
  int64 team_id = 101;
  int64 room_id = 102;
  int64 short_uid = 103;
  int64 board_id = 104;
  // 关系链数据，部分协议才有数据，比如加入副玩法房间
  repeated int64 game_friend_uids = 110; // 游戏好友
  repeated int64 plat_friend_uids = 111; // 平台好友
  repeated int64 friend_uids = 112; // 全部好友
}

//////////// ss业务协议 //////////////////
//////////// 匹配相关
message PrelimStartMatchReq {
  int64 room_id = 1; // 房间id
  com.tencent.wea.protocol.TeamData team_data = 3; // 组队信息
  int32 custom_comp_id = 20; // 定制赛事比赛id
}

message PrelimStartMatchRes {
  int32 result = 1;         //0成功 其他失败
  int32 svr_id = 2;         //match svrid
}

// 海选取消匹配
message PrelimCancelMatchReq {
  int32 server_id = 1;  //match服列表
  int64 room_id = 2;    //房间id
  int64 leader_id = 3;
  int32 match_type = 4;
  int64 current_time = 5;
  int64 cancel_uid = 6;
  int64 match_id = 7;
  int32 room_info_id = 8;
  int32 custom_comp_id = 20; // 定制赛事比赛id
}

message PrelimCancelMatchRes {
  int32 result = 1;         //0成功 其他失败
}

///////////// 结算相关
// 赛事结算
message CompetitionSettlementReq {
  int64 uid = 1;
  int64 room_id = 2;
  int32 fashion_rank = 3;
//  int32 level_type = 4;
  com.tencent.wea.protocol.MemberBaseInfo member_base_info = 9;
  com.tencent.wea.protocol.ChampionTeamInfo champion_team_info = 10; // 冠军队伍玩家得分信息
  com.tencent.wea.protocol.CompetitionBattleData competition_data = 11;
  com.tencent.wea.protocol.CommonPlayerBattleResult battle_result = 12;
  com.tencent.wea.protocol.LetsGoBattleDetailData detail_data = 13;
}

message CompetitionSettlementRes {
  com.tencent.wea.protocol.CompetitionSettlementInfo competition_settlement = 1; // 赛事结算信息
}

message EndBattleIdRank {
  int64 id = 1;   // 可以是玩家uid, 阵营sideId
  int32 rank = 2;
}

message EndBattleLevelRanks {
  repeated EndBattleIdRank playerRanks = 2;  // 局内玩家的排名
  repeated EndBattleIdRank sideRanks = 3;    // 局内阵营的排名
}

// 战斗结束时, 额外填充的结算信息
message BattleFinishedExResult {
  bool isBattleFinished = 1;  // 是否是战斗结束的EndBattleRequest(结束时填充true, 中途推出为false)
  repeated EndBattleLevelRanks levelRanks = 2;  // 每一局的排名
//  DSStats dsStats = 3; //DS质量统计数据，只有当isBattleFinished为true的时候，需要设置
}

// battle结束时通知赛事
message CompetitionEndBattleReq {
  int64 battle_id = 1; //[(field_ds_session_id) = true];
//  repeated PlayerBattleResult results = 2;
  repeated int64 championUids = 3;
  BattleFinishedExResult battleFinishedExResult = 4;
  repeated com.tencent.wea.protocol.MemberBaseInfo champion_members = 5; // 已废弃
  map<int64, com.tencent.wea.protocol.MemberBaseInfo> battle_players = 6;
  com.tencent.wea.protocol.CompetitionBattleData competition_data = 10;
}

message CompetitionEndBattleRes {

}


// level结束时通知赛事
message CompetitionEndLevelReq {
  int64 battle_id = 1;
  int32 match_type = 2;
  int32 level_id = 3;
  int32 at_round = 4;
  repeated ds_roomsvr.PlayerLevelResult results = 5;
  com.tencent.wea.protocol.CompetitionBattleData competition_data = 10;
}

message CompetitionEndLevelRes {

}

// 淘汰赛房间开始
message ElimRoomStartReq {
  com.tencent.wea.protocol.CompetitionElimRoomInfo room = 1;
  int32 level_duration = 2;

  map<int64, int32> in_room_players = 11; // 开始时在房间的玩家
}

message ElimRoomStartRes {

}

// 淘汰赛房间开始
message ElimRoomPlayerKickOffReq {
  com.tencent.wea.protocol.CompetitionElimRoomInfo room = 1;
  repeated int64 player_list = 2;
  repeated int64 in_room_player_list = 3;
}

message ElimRoomPlayerKickOffRes {

}

// 获取赛事时间信息
message GetCompetitionTimeInfoReq {
  repeated int64 in_battle_players = 1; // 对局中玩家uid
  int64 battle_id = 2;
}

message GetCompetitionTimeInfoRes {
  int32 season = 1; // 赛事届数
  int32 start_time = 2;
  int32 cur_stage = 3;
  string competition_name = 4;
  map<int64, bool> has_enroll_player = 5; // 已报名赛事玩家
}

// 赛事大厅入口是否打开
message IsCompetitionEntranceOpenReq {
}

message IsCompetitionEntranceOpenRes {
  bool is_open = 1;
  map<int32, int32> season_map = 2; // compType -> season
}

message WolfkillGlobalConfigData{
  map<string,string> param_config = 1;
}

message WolfkillRoomConfigData{
  int64 room_id = 1;
  map<int64,int32> vocation_config = 2;
  map<string,string> param_config = 3;
}

// 注意大小写
message WolfKillTimeLimitedEvent{
  int32 event_id = 1;
  int64 start_time = 2;
  int64 end_time = 3;
  int32 event_type = 4;
  int64 tip_start_time = 5;
  int64 tip_end_time = 6;
  string tip_text = 7;
  int32 jump_id = 8;
  int32 jump_type = 9;
  string pic = 10;
  int32 package_type = 11;
}

enum WolfKillSeasonType{
  WOLFKILL_SEASONTYPE_NONE = 0;
  WOLFKILL_SEASONTYPE_SEASON = 1;
  WOLFKILL_SEASONTYPE_ACTIVITY = 2;
}

message WolfKillSeasonTimeLimited{
  int32 id = 1;
  int64 start_time = 2;
  int64 end_time = 3;
  int32 season = 4;
  string season_name = 5;
  bool season_reward_open = 6;
  int32 season_type = 7;
}

message WolfKillAnnouncementEntity {
  string title = 1;
  string content = 2;
  bool is_up = 3;
}

message WolfKillAnnouncement{
  int32 id = 1;
  string version = 2;
  repeated WolfKillAnnouncementEntity announce = 3;
  bool topic_season = 4;
  int64 start_ts = 5;
  int64 end_ts = 6;
}

message WolfKillFace{
  int32 id = 1;
  string describe = 2;
  int32 order = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  int32 jump_id = 6;
  string pic_url = 7;
  int32 min_role_info_point = 8;
}

message WolfkillConfigReq{
}

message WolfkillConfigRes{
  map<string,string> global_param_config = 1;
  map<int64, WolfkillRoomConfigData> room_config = 2;
  repeated  WolfKillTimeLimitedEvent time_limited_event = 3;
}

message WolfKillVocationTime{
  int32 vocation_id = 1;
  int64 start_time = 2;
  int64 end_time = 3;
}

message WolKillHyperCore{
  int32 id = 1;
  int32 match_type_id = 2;
  int32 weight = 3;
}

message NR3ERealTimeConfigReq{
}

message FeatureCompWolfKillVocationItem {
  int32 side_id = 1;
  repeated int32 vocation_id = 2;
}

message FeatureCompWolfKillVocation {
  int32 group_id = 1;
  repeated FeatureCompWolfKillVocationItem  priority_list = 2;
  repeated FeatureCompWolfKillVocationItem  secondary_list = 3;
  int32 player_num = 4;
  int32 weight = 5;
}

message FeatureCompWolfSideConfig{
  int32 wolf_side = 1;
  int32 civilians_side = 2;
  int32 neutral_side = 3;
}


message NR3ERealTimeConfigRes{
  // 狼人杀配置
  map<string,string> global_param_config = 1;
  map<int64, WolfkillRoomConfigData> room_config = 2;
  repeated  WolfKillTimeLimitedEvent time_limited_event = 3;
  repeated  WolfKillSeasonTimeLimited season_time_limited = 4;
  repeated  WolfKillAnnouncement announcement = 5;
  repeated  WolfKillFace face = 6;
  repeated  WolfKillVocationTime vocation_time = 7;
  repeated  WolKillHyperCore hyper_core = 8;
  // 狼人主题机制
  bytes table_NR3EThemeEventConfigData = 51;
  bytes table_NR3EThemeEventItemConfigData = 52;
  bytes table_NR3EThemeEventRefreshRulesData = 53;
  map<string, string> nr3e_theme_event_whitelist = 54;

  // 躲猫猫配置
  bytes table_NR3E1LevelPropData = 101;
  bytes table_NR3E1LevelPropRefreshData = 102;

  // NR3E8配置
  map<string, string> nr3e8_const_map = 301;
  
  // 狼人赛事
  repeated FeatureCompWolfKillVocation feature_comp_vocation_list = 200;
  repeated int32 feature_comp_point_levels = 201;
  repeated int32 feature_comp_peak_levels = 202;
  map<int32, FeatureCompWolfSideConfig> feature_comp_side_config = 203;
  int32 feature_comp_point_duration = 204; // 积分赛时长
}

enum RetFlagRichPlayer {
  PRF_OK = 0;
  PRF_ONLINE_OVERTIME = 1;
}

// 玩家关系链数据
message RelationData {
  repeated int64 game_friend_uids = 1; // 游戏好友
  repeated int64 plat_friend_uids = 2; // 平台好友
  repeated int64 friend_uids = 10; // 全部好友
}

message RichBoardTlogInfo {
  int32 city_level = 1; // 城市等级
  int64 money_num = 2; // 金币数
  int32 dice_num = 3; // 骰子数
  int32 shield_num = 4; // 盾牌数
  int32 friend_num = 5; // 好友数
  string building_str = 11; // 建筑信息
  string buff_effect_str = 12; // buff效果信息
}

// NR3E8玩家在线
message RichPlayerOnlineReq {
  int64 pre_board_id = 1;
  int64 cur_board_id = 2;
  int32 from = 10; // 来源 Nr3e8BoardEnterFrom
  int32 enter_source = 11; // 入口 Nr3e8BoardEnterSource
  int64 last_login_ms = 12; // 上次登录游戏时间
  com.tencent.wea.protocol.MemberBaseInfo member_base_info = 100; // 用户基本信息
  RelationData relation = 101; // 关系链数据

  int64 uid = 1000;
}
message RichPlayerOnlineRes {
  int32 res = 1;
  RichBoardTlogInfo tlog_info = 2;
}

// NR3E8玩家离线
message RichPlayerOfflineReq {
  int64 cur_board_id = 1;
  bool is_manual_exit = 2;
  int64 uid = 1000;
}
message RichPlayerOfflineRes {
  int32 res = 1;
  RichBoardTlogInfo tlog_info = 2;
}

// NR3E8玩家心跳
message RichPlayerHeartBeatReq {
  option (irpc_one_way) = true;
  int64 uid = 1000;
}

message RichPlayerHeartBeatRes {

}

message RichItem {
  int32 item_id = 1; // RichItemId
  int64 item_num = 2;
  int64 item_max = 3;
}

// NR3E8玩家添加物品
message RichPlayerAddItemReq {
  int64 uid = 1000;
  repeated RichItem add_items = 1; // 添加物品
  int32 reason = 2; // RichItemChangeReason
  int32 sub_reason = 3;
  string bill_no = 50; // 流水号
}

message RichPlayerAddItemRes {
}

// NR3E8购买商品
message RichMallBuyReq {
  int64 uid = 1000;
  int32 commodity_id = 1;
  int32 commodity_num = 2;
  string bill_no = 3;
}

message RichMallBuyRes {

}

// 玩家更新member基础信息
message RichPlayerSyncMemberBaseReq {
  option (irpc_one_way) = true;
  int64 uid = 1000;
  com.tencent.wea.protocol.BoardMemberBasic member_basic = 1;
  int32 change_type = 100;
}

message RichPlayerSyncMemberBaseRes {

}

service CompetitionIrpcProxy {
  // 客户端请求统一处理
  rpc AccessC2S (CompetitionCommonReq) returns (CompetitionCommonRes) {}

  // 海选发起匹配
  rpc PrelimStartMatch (PrelimStartMatchReq) returns (PrelimStartMatchRes) {}
  // 海选取消匹配
  rpc PrelimCancelMatch (PrelimCancelMatchReq) returns (PrelimCancelMatchRes) {}

  // 赛事结算
  rpc CompetitionSettlement (CompetitionSettlementReq) returns (CompetitionSettlementRes) {}
  // battle结束时通知赛事
  rpc CompetitionEndBattle (CompetitionEndBattleReq) returns (CompetitionEndBattleRes) {}
  // level结束时通知赛事
  rpc CompetitionEndLevel (CompetitionEndLevelReq) returns (CompetitionEndLevelRes) {}
  // 淘汰赛房间开始
  rpc ElimRoomStart (ElimRoomStartReq) returns (ElimRoomStartRes) {}
  // 获取赛事时间信息
  rpc GetCompetitionTimeInfo (GetCompetitionTimeInfoReq) returns (GetCompetitionTimeInfoRes) {}
  // 淘汰赛踢掉离线玩家
  rpc ElimRoomPlayerKickOff (ElimRoomPlayerKickOffReq) returns (ElimRoomPlayerKickOffRes) {}
  // 赛事大厅入口是否打开
  rpc IsCompetitionEntranceOpen (IsCompetitionEntranceOpenReq) returns (IsCompetitionEntranceOpenRes) {}

  // 获取狼人的配置信息（废弃）
  rpc GetWolfkillConfig (WolfkillConfigReq) returns (WolfkillConfigRes) {}
  // 获取nr3e副玩法实时配置信息
  rpc GetNR3ERealTimeConfig (NR3ERealTimeConfigReq) returns (NR3ERealTimeConfigRes) {}

  // ----- NR3E8大富翁 -----
  // NR3E8玩家在线
  rpc RichPlayerOnline (RichPlayerOnlineReq) returns (RichPlayerOnlineRes) {}
  // NR3E8玩家离线
  rpc RichPlayerOffline (RichPlayerOfflineReq) returns (RichPlayerOfflineRes) {}
  // NR3E8玩家心跳
  rpc RichPlayerHeartBeat (RichPlayerHeartBeatReq) returns (RichPlayerHeartBeatRes) {}
  // NR3E8玩家添加物品
  rpc RichPlayerAddItem (RichPlayerAddItemReq) returns (RichPlayerAddItemRes) {}
  // NR3E8购买商店商品
  rpc RichMallBuy (RichMallBuyReq) returns (RichMallBuyRes) {}
  // 玩家更新member基础信息
  rpc RichPlayerSyncMemberBase (RichPlayerSyncMemberBaseReq) returns (RichPlayerSyncMemberBaseRes) {}
}
