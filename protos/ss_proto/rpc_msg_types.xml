<root>
  <entry id="1" name="HelloReq" serverName="gamesvr"/>
  <entry id="2" name="HelloRes" serverName="gamesvr"/>
  <entry id="3" name="PingPongReq" serverName="noserver"/>
  <entry id="4" name="PingPongRes" serverName="noserver"/>
  <entry id="5" name="KickOffReq" serverName="noserver"/>
  <entry id="6" name="KickOffRes" serverName="noserver"/>
  <entry id="7" name="LockReq" serverName="locksvr"/>
  <entry id="8" name="LockRes" serverName="locksvr"/>
  <entry id="9" name="CancelLockReq" serverName="locksvr"/>
  <entry id="10" name="CancelLockRes" serverName="locksvr"/>
  <entry id="11" name="MetaDataReportReq" serverName="proxysvr"/>
  <entry id="12" name="MetaDataReportRes" serverName="proxysvr"/>
  <entry id="13" name="SubscribeShareDataReq" serverName="noserver"/>
  <entry id="14" name="SubscribeShareDataRes" serverName="noserver"/>
  <entry id="15" name="BatchSubscribeShareDataReq" serverName="noserver"/>
  <entry id="16" name="BatchSubscribeShareDataRes" serverName="noserver"/>
  <entry id="17" name="UnsubscribeShareDataReq" serverName="noserver"/>
  <entry id="18" name="UnsubscribeShareDataRes" serverName="noserver"/>
  <entry id="19" name="TestReq" serverName="noserver"/>
  <entry id="20" name="TestRes" serverName="noserver"/>
  <entry id="21" name="ShareDataHeartBeatReq" serverName="noserver"/>
  <entry id="22" name="ShareDataHeartBeatRes" serverName="noserver"/>
  <entry id="23" name="SyncShareDataChangeReq" serverName="noserver"/>
  <entry id="24" name="SyncShareDataChangeRes" serverName="noserver"/>
  <entry id="25" name="ShareDataSyncRemoveSubscriberReq" serverName="noserver"/>
  <entry id="26" name="ShareDataSyncRemoveSubscriberRes" serverName="noserver"/>
  <entry id="27" name="TestMatchSvrReq" serverName="matchsvr"/>
  <entry id="28" name="TestMatchSvrRes" serverName="matchsvr"/>
  <entry id="29" name="ProxyTestReq" serverName="proxysvr"/>
  <entry id="30" name="ProxyTestRes" serverName="proxysvr"/>
  <entry id="31" name="RpcMatchSuccReq" serverName="gamesvr"/>
  <entry id="32" name="RpcMatchSuccRes" serverName="gamesvr"/>
  <entry id="33" name="RpcMatchReq" serverName="matchsvr"/>
  <entry id="34" name="RpcMatchRes" serverName="matchsvr"/>
  <entry id="35" name="RpcCancelMatchReq" serverName="matchsvr"/>
  <entry id="36" name="RpcCancelMatchRes" serverName="matchsvr"/>
  <entry id="37" name="TestRoomSvrReq" serverName="roomsvr"/>
  <entry id="38" name="TestRoomSvrRes" serverName="roomsvr"/>
  <entry id="39" name="RpcCreateTeamReq" serverName="roomsvr"/>
  <entry id="40" name="RpcJoinTeamReq" serverName="roomsvr"/>
  <entry id="41" name="RpcCreateTeamRes" serverName="roomsvr"/>
  <entry id="42" name="RpcJoinTeamRes" serverName="roomsvr"/>
  <entry id="43" name="RpcExitTeamReq" serverName="roomsvr"/>
  <entry id="44" name="RpcExitTeamRes" serverName="roomsvr"/>
  <entry id="45" name="RpcReadyMatchReq" serverName="roomsvr"/>
  <entry id="46" name="RpcReadyMatchRes" serverName="roomsvr"/>
  <entry id="47" name="RpcKickReq" serverName="roomsvr"/>
  <entry id="48" name="RpcKickRes" serverName="roomsvr"/>
  <entry id="49" name="RpcStartMatchReq" serverName="roomsvr"/>
  <entry id="50" name="RpcStartMatchRes" serverName="roomsvr"/>
  <entry id="51" name="RpcCreateRoomReq" serverName="roomsvr"/>
  <entry id="52" name="RpcCreateRoomRes" serverName="roomsvr"/>
  <entry id="53" name="RpcJoinRoomReq" serverName="roomsvr"/>
  <entry id="54" name="RpcJoinRoomRes" serverName="roomsvr"/>
  <entry id="55" name="RpcExitRoomReq" serverName="roomsvr"/>
  <entry id="56" name="RpcExitRoomRes" serverName="roomsvr"/>
  <entry id="57" name="RpcKickPlayerReq" serverName="roomsvr"/>
  <entry id="58" name="RpcKickPlayerRes" serverName="roomsvr"/>
  <entry id="59" name="RpcRoomMatchSuccReq" serverName="roomsvr"/>
  <entry id="60" name="RpcRoomMatchSuccRes" serverName="roomsvr"/>
  <entry id="61" name="RpcKickLoginPlayerReq" serverName="gamesvr"/>
  <entry id="62" name="RpcKickLoginPlayerRes" serverName="gamesvr"/>
  <entry id="63" name="RpcCreateBattleReq" serverName="battlesvr"/>
  <entry id="64" name="RpcCreateBattleRes" serverName="battlesvr"/>
  <entry id="65" name="RpcMatchResultReq" serverName="roomsvr"/>
  <entry id="66" name="RpcMatchResultRes" serverName="roomsvr"/>
  <entry id="67" name="RpcConfirmBattleReq" serverName="battlesvr"/>
  <entry id="68" name="RpcConfirmBattleRes" serverName="battlesvr"/>
  <entry id="69" name="RpcInterPlayerInteractionReq" serverName="gamesvr"/>
  <entry id="70" name="RpcInterPlayerInteractionRes" serverName="gamesvr"/>
  <entry id="71" name="RpcUpdateTopRankReq" serverName="ranksvr"/>
  <entry id="72" name="RpcUpdateTopRankRes" serverName="ranksvr"/>
  <entry id="73" name="TestRankSvrReq" serverName="ranksvr"/>
  <entry id="74" name="TestRankSvrRes" serverName="ranksvr"/>
  <entry id="75" name="RpcGetTopRankReq" serverName="ranksvr"/>
  <entry id="76" name="RpcGetTopRankRes" serverName="ranksvr"/>
  <entry id="77" name="RpcGetOneTopRankReq" serverName="ranksvr"/>
  <entry id="78" name="RpcGetOneTopRankRes" serverName="ranksvr"/>
  <entry id="79" name="RpcClearTopRankReq" serverName="ranksvr"/>
  <entry id="80" name="RpcClearTopRankRes" serverName="ranksvr"/>
  <entry id="81" name="RpcGetOneUserRankReq" serverName="ranksvr"/>
  <entry id="82" name="RpcGetOneUserRankRes" serverName="ranksvr"/>
  <entry id="83" name="TestChatSvrReq" serverName="chatsvr"/>
  <entry id="84" name="TestChatSvrRes" serverName="chatsvr"/>
  <entry id="85" name="RpcChatSendReq" serverName="chatsvr"/>
  <entry id="86" name="RpcChatSendRes" serverName="chatsvr"/>
  <entry id="87" name="RpcChatMsgReq" serverName="gamesvr"/>
  <entry id="88" name="RpcChatMsgReqRes" serverName="gamesvr"/>
  <entry id="89" name="RpcChatMsgRes" serverName="gamesvr"/>
  <entry id="90" name="RpcChatMsgNtfReq" serverName="gamesvr"/>
  <entry id="91" name="RpcChatMsgNtfRes" serverName="gamesvr"/>
  <entry id="92" name="RpcAllocRoomIDReq" serverName="roomsvr"/>
  <entry id="93" name="RpcAllocRoomIDRes" serverName="roomsvr"/>
  <entry id="94" name="RpcAllocBattleIDReq" serverName="battlesvr"/>
  <entry id="95" name="RpcAllocBattleIDRes" serverName="battlesvr"/>
  <entry id="96" name="AddFriendReq" serverName="gamesvr"/>
  <entry id="97" name="AddFriendRes" serverName="gamesvr"/>
  <entry id="98" name="AgreeFriendApplyReq" serverName="gamesvr"/>
  <entry id="99" name="AgreeFriendApplyRes" serverName="gamesvr"/>
  <entry id="100" name="DenyFriendApplyReq" serverName="gamesvr"/>
  <entry id="101" name="DenyFriendApplyRes" serverName="gamesvr"/>
  <entry id="102" name="RemoveFriendReq" serverName="gamesvr"/>
  <entry id="103" name="RemoveFriendRes" serverName="gamesvr"/>
  <entry id="104" name="RpcChatMsgPullReq" serverName="chatsvr"/>
  <entry id="105" name="RpcChatMsgPullRes" serverName="chatsvr"/>
  <entry id="106" name="RpcGroupChatModifyNtfReq" serverName="chatsvr"/>
  <entry id="107" name="RpcGroupChatModifyNtfRes" serverName="chatsvr"/>
  <entry id="108" name="RpcCreateGroupChatReq" serverName="chatsvr"/>
  <entry id="109" name="RpcCreateGroupChatRes" serverName="chatsvr"/>
  <entry id="110" name="RpcBattleCreateGroupChatReq" serverName="roomsvr"/>
  <entry id="111" name="RpcBattleCreateGroupChatRes" serverName="roomsvr"/>
  <entry id="112" name="RpcEndBattleReq" serverName="gamesvr"/>
  <entry id="113" name="RpcEndBattleRes" serverName="gamesvr"/>
  <entry id="114" name="RpcBattleRoomCreateGroupChatReq" serverName="battlesvr"/>
  <entry id="115" name="RpcBattleRoomCreateGroupChatRes" serverName="battlesvr"/>
  <entry id="116" name="RpcMatchRoomCreateGroupChatReq" serverName="roomsvr"/>
  <entry id="117" name="RpcMatchRoomCreateGroupChatRes" serverName="roomsvr"/>
  <entry id="118" name="RpcRoomInvitationMsgNtfReq" serverName="gamesvr"/>
  <entry id="119" name="RpcRoomInvitationMsgNtfRes" serverName="gamesvr"/>
  <entry id="120" name="RpcRoomInvitationResultReq" serverName="roomsvr"/>
  <entry id="121" name="RpcRoomInvitationResultRes" serverName="roomsvr"/>
  <entry id="122" name="RpcRoomInvitationReq" serverName="roomsvr"/>
  <entry id="123" name="RpcRoomInvitationRes" serverName="roomsvr"/>
  <entry id="124" name="RpcRoomMemberModifyNtfReq" serverName="gamesvr"/>
  <entry id="125" name="RpcRoomMemberModifyNtfRes" serverName="gamesvr"/>
  <entry id="126" name="RpcAddPlayerCacheReq" serverName="ranksvr"/>
  <entry id="127" name="RpcAddPlayerCacheRes" serverName="ranksvr"/>
  <entry id="128" name="RpcRemovePlayerCacheReq" serverName="ranksvr"/>
  <entry id="129" name="RpcRemovePlayerCacheRes" serverName="ranksvr"/>
  <entry id="130" name="RpcGetPlayerCacheReq" serverName="ranksvr"/>
  <entry id="131" name="RpcGetPlayerCacheRes" serverName="ranksvr"/>
  <entry id="132" name="RpcAddPlayerFansCacheReq" serverName="ranksvr"/>
  <entry id="133" name="RpcAddPlayerFansCacheRes" serverName="ranksvr"/>
  <entry id="134" name="RpcRemovePlayerFansCacheReq" serverName="ranksvr"/>
  <entry id="135" name="RpcRemovePlayerFansCacheRes" serverName="ranksvr"/>
  <entry id="136" name="RpcGetPlayerFansCacheReq" serverName="ranksvr"/>
  <entry id="137" name="RpcGetPlayerFansCacheRes" serverName="ranksvr"/>
  <entry id="138" name="GlobalServicesActionReq" serverName="noserver"/>
  <entry id="139" name="GlobalServicesActionRes" serverName="noserver"/>
  <entry id="140" name="ServiceNodeHearbeatReq" serverName="globalmgrsvr"/>
  <entry id="141" name="ServiceNodeHearbeatRes" serverName="globalmgrsvr"/>
  <entry id="142" name="ClientNodeHearbeatReq" serverName="globalmgrsvr"/>
  <entry id="143" name="ClientNodeHearbeatRes" serverName="globalmgrsvr"/>
  <entry id="144" name="ServiceMasterVoteReq" serverName="globalmgrsvr"/>
  <entry id="145" name="ServiceMasterVoteRes" serverName="globalmgrsvr"/>
  <entry id="146" name="RpcRoomPlayerOfflineReq" serverName="roomsvr"/>
  <entry id="147" name="RpcRoomPlayerOfflineRes" serverName="roomsvr"/>
  <entry id="148" name="RpcRoomModifyGameModeReq" serverName="roomsvr"/>
  <entry id="149" name="RpcRoomModifyGameModeRes" serverName="roomsvr"/>
  <entry id="150" name="RpcRoomPlayerOnlineReq" serverName="roomsvr"/>
  <entry id="151" name="RpcRoomPlayerOnlineRes" serverName="roomsvr"/>
  <entry id="152" name="AddCouplesReq" serverName="gamesvr"/>
  <entry id="153" name="AddCouplesRes" serverName="gamesvr"/>
  <entry id="154" name="AgreeCouplesApplyReq" serverName="gamesvr"/>
  <entry id="155" name="AgreeCouplesApplyRes" serverName="gamesvr"/>
  <entry id="156" name="DenyCouplesApplyReq" serverName="gamesvr"/>
  <entry id="157" name="DenyCouplesApplyRes" serverName="gamesvr"/>
  <entry id="158" name="RemoveCouplesReq" serverName="gamesvr"/>
  <entry id="159" name="RemoveCouplesRes" serverName="gamesvr"/>
  <entry id="160" name="RpcMatchCancelReq" serverName="roomsvr"/>
  <entry id="161" name="RpcMatchCancelRes" serverName="roomsvr"/>
  <entry id="162" name="RpcMatchCancelResultReq" serverName="roomsvr"/>
  <entry id="163" name="RpcMatchCancelResultRes" serverName="roomsvr"/>
  <entry id="164" name="RpcBattleCreateResultReq" serverName="roomsvr"/>
  <entry id="165" name="RpcBattleCreateResultRes" serverName="roomsvr"/>
  <entry id="166" name="RpcRoomCancelResultNtfReq" serverName="gamesvr"/>
  <entry id="167" name="RpcRoomCancelResultNtfRes" serverName="gamesvr"/>
  <entry id="168" name="RpcEndBattleMsgReq" serverName="roomsvr"/>
  <entry id="169" name="RpcEndBattleMsgRes" serverName="roomsvr"/>
  <entry id="170" name="RpcTestStateRouteReq" serverName="roomsvr"/>
  <entry id="171" name="RpcTestStateRouteRes" serverName="roomsvr"/>
  <entry id="172" name="RpcSharedInviteNtfReq" serverName="gamesvr"/>
  <entry id="173" name="RpcSharedInviteNtfRes" serverName="gamesvr"/>
  <entry id="174" name="RpcGetPlayerHotDataReq" serverName="gamesvr"/>
  <entry id="175" name="RpcGetPlayerHotDataRes" serverName="gamesvr"/>
  <entry id="176" name="AddBlackReq" serverName="gamesvr"/>
  <entry id="177" name="AddBlackRes" serverName="gamesvr"/>
  <entry id="178" name="RemoveBlackReq" serverName="gamesvr"/>
  <entry id="179" name="RemoveBlackRes" serverName="gamesvr"/>
  <entry id="180" name="RpcBattlePlayerOnlineReq" serverName="battlesvr"/>
  <entry id="181" name="RpcBattlePlayerOnlineRes" serverName="battlesvr"/>
  <entry id="182" name="RpcBattlePlayerOfflineReq" serverName="battlesvr"/>
  <entry id="183" name="RpcBattlePlayerOfflineRes" serverName="battlesvr"/>
  <entry id="184" name="RpcWantToJoinReq" serverName="gamesvr"/>
  <entry id="185" name="RpcWantToJoinRes" serverName="gamesvr"/>
  <entry id="186" name="RpcCheckCanJoinRoomReq" serverName="roomsvr"/>
  <entry id="187" name="RpcCheckCanJoinRoomRes" serverName="roomsvr"/>
  <entry id="188" name="RpcReplayWantToJoinReq" serverName="roomsvr"/>
  <entry id="189" name="RpcReplayWantToJoinRes" serverName="roomsvr"/>
  <entry id="190" name="RpcWantToJoinResultReq" serverName="gamesvr"/>
  <entry id="191" name="RpcWantToJoinResultRes" serverName="gamesvr"/>
  <entry id="192" name="RpcBattlePlayerLoginReq" serverName="battlesvr"/>
  <entry id="193" name="RpcBattlePlayerLoginRes" serverName="battlesvr"/>
  <entry id="194" name="RpcBattleReportGameDataReq" serverName="battlesvr"/>
  <entry id="195" name="RpcBattleReportGameDataRes" serverName="battlesvr"/>
  <entry id="196" name="RpcBattleRequestGameDataReq" serverName="battlesvr"/>
  <entry id="197" name="RpcBattleRequestGameDataRes" serverName="battlesvr"/>
  <entry id="198" name="RpcBattleGameDateNtfReq" serverName="gamesvr"/>
  <entry id="199" name="RpcBattleGameDateNtfRes" serverName="gamesvr"/>
  <entry id="200" name="RpcBattleSyncGameDateNtfReq" serverName="gamesvr"/>
  <entry id="201" name="RpcBattleSyncGameDateNtfRes" serverName="gamesvr"/>
  <entry id="202" name="RpcBattleGetGameDataReq" serverName="battlesvr"/>
  <entry id="203" name="RpcBattleGetGameDataRes" serverName="battlesvr"/>
  <entry id="204" name="RpcStartBattleReq" serverName="gamesvr"/>
  <entry id="205" name="RpcStartBattleRes" serverName="gamesvr"/>
  <entry id="206" name="RpcStartBattleNtfReq" serverName="gamesvr"/>
  <entry id="207" name="RpcStartBattleNtfRes" serverName="gamesvr"/>
  <entry id="208" name="RpcBattleReportGameEndReq" serverName="battlesvr"/>
  <entry id="209" name="RpcBattleReportGameEndRes" serverName="battlesvr"/>
  <entry id="210" name="RpcBattleSeedDateNtfReq" serverName="gamesvr"/>
  <entry id="211" name="RpcBattleSeedDateNtfRes" serverName="gamesvr"/>
  <entry id="212" name="RpcBattleEndDataNtfReq" serverName="gamesvr"/>
  <entry id="213" name="RpcBattleEndDataNtfRes" serverName="gamesvr"/>
  <entry id="214" name="RpcGMCommandReq" serverName="battlesvr"/>
  <entry id="215" name="RpcGMCommandRes" serverName="battlesvr"/>
  <entry id="216" name="RpcSyncGameTimeReq" serverName="battlesvr"/>
  <entry id="217" name="RpcSyncGameTimeRes" serverName="battlesvr"/>
  <entry id="218" name="RpcBattlePlayerReadyReq" serverName="battlesvr"/>
  <entry id="219" name="RpcBattlePlayerReadyRes" serverName="battlesvr"/>
  <entry id="220" name="RpcBattleQuitReq" serverName="battlesvr"/>
  <entry id="221" name="RpcBattleQuitRes" serverName="battlesvr"/>
  <entry id="222" name="RpcAddMemberToGroupChatReq" serverName="chatsvr"/>
  <entry id="223" name="RpcAddMemberToGroupChatRes" serverName="chatsvr"/>
  <entry id="224" name="RpcRemoveMemberFromGroupChatReq" serverName="chatsvr"/>
  <entry id="225" name="RpcRemoveMemberFromGroupChatRes" serverName="chatsvr"/>
  <entry id="226" name="RpcDisbandGroupChatReq" serverName="chatsvr"/>
  <entry id="227" name="RpcDisbandGroupChatRes" serverName="chatsvr"/>
  <entry id="228" name="RpcCreateChatGroupReq" serverName="gamesvr"/>
  <entry id="229" name="RpcCreateChatGroupRes" serverName="gamesvr"/>
  <entry id="230" name="RpcGiveUpBattleReq" serverName="gamesvr"/>
  <entry id="231" name="RpcGiveUpBattleRes" serverName="gamesvr"/>
  <entry id="232" name="RpcBattleGameStartNtfReq" serverName="gamesvr"/>
  <entry id="233" name="RpcBattleGameStartNtfRes" serverName="gamesvr"/>
  <entry id="234" name="RpcRoomPlayerUpdateMemberBaseInfoReq" serverName="roomsvr"/>
  <entry id="235" name="RpcRoomPlayerUpdateMemberBaseInfoRes" serverName="roomsvr"/>
  <entry id="236" name="RpcQuitBattleRoomReq" serverName="roomsvr"/>
  <entry id="237" name="RpcQuitBattleRoomRes" serverName="roomsvr"/>
  <entry id="238" name="RpcRoomPlayerSetBattleReq" serverName="roomsvr"/>
  <entry id="239" name="RpcRoomPlayerSetBattleRes" serverName="roomsvr"/>
  <entry id="240" name="RpcRoomSvrLockPlayerReq" serverName="gamesvr"/>
  <entry id="241" name="RpcRoomSvrLockPlayerRes" serverName="gamesvr"/>
  <entry id="242" name="RpcPrepareToJoinRoomReq" serverName="gamesvr"/>
  <entry id="243" name="RpcPrepareToJoinRoomRes" serverName="gamesvr"/>
  <entry id="244" name="RpcDsGuideReq" serverName="battlesvr"/>
  <entry id="245" name="RpcDsGuideRes" serverName="battlesvr"/>
  <entry id="246" name="RpcReportGameOnlineReq" serverName="dirsvr"/>
  <entry id="247" name="RpcReportGameOnlineRes" serverName="dirsvr"/>
  <entry id="248" name="RpcRoomCommonNtfReq" serverName="gamesvr"/>
  <entry id="249" name="RpcRoomCommonNtfRes" serverName="gamesvr"/>
  <entry id="250" name="RoutingDataReportReq" serverName="proxysvr"/>
  <entry id="251" name="RoutingDataReportRes" serverName="proxysvr"/>
  <entry id="252" name="RpcCheckDsStatusReq" serverName="battlesvr"/>
  <entry id="253" name="RpcCheckDsStatusRes" serverName="battlesvr"/>
  <entry id="254" name="DelCachedRoutingReq" serverName="proxysvr"/>
  <entry id="255" name="DelCachedRoutingRes" serverName="proxysvr"/>
  <entry id="256" name="ServerInteractReq" serverName="proxysvr"/>
  <entry id="257" name="ServerInteractRes" serverName="proxysvr"/>
  <entry id="258" name="RoomInteractReq" serverName="roomsvr"/>
  <entry id="259" name="RoomInteractRes" serverName="roomsvr"/>
  <entry id="260" name="RoomSvrInteractReq" serverName="roomsvr"/>
  <entry id="261" name="RoomSvrInteractRes" serverName="roomsvr"/>
  <entry id="262" name="ProxySvrInteractReq" serverName="proxysvr"/>
  <entry id="263" name="ProxySvrInteractRes" serverName="proxysvr"/>
  <entry id="264" name="IdipRpcByZoneIdReq" serverName="any"/>
  <entry id="265" name="IdipRpcByZoneIdRes" serverName="any"/>
  <entry id="266" name="IdipRpcByUuidReq" serverName="any"/>
  <entry id="267" name="IdipRpcByUuidRes" serverName="any"/>
  <entry id="268" name="IdipRpcReq" serverName="any"/>
  <entry id="269" name="IdipRpcRes" serverName="any"/>
  <entry id="270" name="BattleSvrInteractReq" serverName="battlesvr"/>
  <entry id="271" name="BattleSvrInteractRes" serverName="battlesvr"/>
  <entry id="272" name="RpcDetermineJoinRingReq" serverName="gamesvr"/>
  <entry id="273" name="RpcDetermineJoinRingRes" serverName="gamesvr"/>
  <entry id="274" name="RpcInviteeLockInviterReq" serverName="gamesvr"/>
  <entry id="275" name="RpcInviteeLockInviterRes" serverName="gamesvr"/>
  <entry id="276" name="RpcGetBattleInfoReq" serverName="battlesvr"/>
  <entry id="277" name="RpcGetBattleInfoRes" serverName="battlesvr"/>
  <entry id="278" name="RpcBattleSendExpressionReq" serverName="battlesvr"/>
  <entry id="279" name="RpcBattleSendExpressionRes" serverName="battlesvr"/>
  <entry id="280" name="RpcExpressionNtfReq" serverName="gamesvr"/>
  <entry id="281" name="RpcExpressionNtfRes" serverName="gamesvr"/>
  <entry id="282" name="RpcGetLimitValueReq" serverName="gamesvr"/>
  <entry id="283" name="RpcGetLimitValueRes" serverName="gamesvr"/>
  <entry id="284" name="RpcTailChatMsgPullReq" serverName="chatsvr"/>
  <entry id="285" name="RpcTailChatMsgPullRes" serverName="chatsvr"/>
  <entry id="286" name="RpcPreChatMsgPullReq" serverName="chatsvr"/>
  <entry id="287" name="RpcPreChatMsgPullRes" serverName="chatsvr"/>
  <entry id="288" name="RpcChatGroupOperationNtfReq" serverName="gamesvr"/>
  <entry id="289" name="RpcChatGroupOperationNtfRes" serverName="gamesvr"/>
  <entry id="290" name="RpcPvEStartStageReq" serverName="gamesvr"/>
  <entry id="291" name="RpcPvEStartStageRes" serverName="gamesvr"/>
  <entry id="292" name="RpcPvEFinishStageReq" serverName="gamesvr"/>
  <entry id="293" name="RpcPvEFinishStageRes" serverName="gamesvr"/>
  <entry id="294" name="RpcLeftChatMsgPullReq" serverName="chatsvr"/>
  <entry id="295" name="RpcLeftChatMsgPullRes" serverName="chatsvr"/>
  <entry id="296" name="RpcChatMsgReadReq" serverName="chatsvr"/>
  <entry id="297" name="RpcChatMsgReadRes" serverName="chatsvr"/>
  <entry id="298" name="RpcChatRemoveMsgRecordReq" serverName="chatsvr"/>
  <entry id="299" name="RpcChatRemoveMsgRecordRes" serverName="chatsvr"/>
  <entry id="300" name="RpcSyncScenePlayerInfoReq" serverName="gamesvr"/>
  <entry id="301" name="RpcSyncScenePlayerInfoRes" serverName="gamesvr"/>
  <entry id="302" name="RpcEnterSceneNtfReq" serverName="gamesvr"/>
  <entry id="303" name="RpcEnterSceneNtfRes" serverName="gamesvr"/>
  <entry id="304" name="RpcSceneEntityModifyNtfReq" serverName="gamesvr"/>
  <entry id="305" name="RpcSceneEntityModifyNtfRes" serverName="gamesvr"/>
  <entry id="306" name="RpcExitSceneNtfReq" serverName="gamesvr"/>
  <entry id="307" name="RpcExitSceneNtfRes" serverName="gamesvr"/>
  <entry id="308" name="RpcSyncSceneDataReq" serverName="gamesvr"/>
  <entry id="309" name="RpcSyncSceneDataRes" serverName="gamesvr"/>
  <entry id="310" name="RpcJumpSceneNtfReq" serverName="gamesvr"/>
  <entry id="311" name="RpcJumpSceneNtfRes" serverName="gamesvr"/>
  <entry id="312" name="RpcSceneBaseDataNtfReq" serverName="gamesvr"/>
  <entry id="313" name="RpcSceneBaseDataNtfRes" serverName="gamesvr"/>
  <entry id="314" name="RpcSceneFindReq" serverName="scenesvr"/>
  <entry id="315" name="RpcSceneFindRes" serverName="scenesvr"/>
  <entry id="316" name="RpcSceneReconnectReq" serverName="scenesvr"/>
  <entry id="317" name="RpcSceneReconnectRes" serverName="scenesvr"/>
  <entry id="318" name="RpcSceneExistReq" serverName="scenesvr"/>
  <entry id="319" name="RpcSceneExistRes" serverName="scenesvr"/>
  <entry id="320" name="RpcSceneEnterReq" serverName="scenesvr"/>
  <entry id="321" name="RpcSceneEnterRes" serverName="scenesvr"/>
  <entry id="322" name="RpcScenePlayerOfflineReq" serverName="scenesvr"/>
  <entry id="323" name="RpcScenePlayerOfflineRes" serverName="scenesvr"/>
  <entry id="324" name="RpcSceneReportDataReq" serverName="scenesvr"/>
  <entry id="325" name="RpcSceneReportDataRes" serverName="scenesvr"/>
  <entry id="326" name="RpcSceneExitReq" serverName="scenesvr"/>
  <entry id="327" name="RpcSceneExitRes" serverName="scenesvr"/>
  <entry id="328" name="RpcSceneManageReq" serverName="scenesvr"/>
  <entry id="329" name="RpcSceneManageRes" serverName="scenesvr"/>
  <entry id="330" name="RpcSceneReportActionReq" serverName="scenesvr"/>
  <entry id="331" name="RpcSceneReportActionRes" serverName="scenesvr"/>
  <entry id="332" name="RpcSceneCreateReq" serverName="scenesvr"/>
  <entry id="333" name="RpcSceneCreateRes" serverName="scenesvr"/>
  <entry id="334" name="RpcSceneGMCommandReq" serverName="scenesvr"/>
  <entry id="335" name="RpcSceneGMCommandRes" serverName="scenesvr"/>
  <entry id="336" name="SceneSvrInteractReq" serverName="scenesvr"/>
  <entry id="337" name="SceneSvrInteractRes" serverName="scenesvr"/>
  <entry id="338" name="RpcSceneGetNeighborReq" serverName="scenesvr"/>
  <entry id="339" name="RpcSceneGetNeighborRes" serverName="scenesvr"/>
  <entry id="340" name="RpcScenePlayerModifyNtfReq" serverName="scenesvr"/>
  <entry id="341" name="RpcScenePlayerModifyNtfRes" serverName="scenesvr"/>
  <entry id="342" name="TestClubSvrReq" serverName="clubsvr"/>
  <entry id="343" name="TestClubSvrRes" serverName="clubsvr"/>
  <entry id="344" name="RpcApplyClubListReq" serverName="clubsvr"/>
  <entry id="345" name="RpcApplyClubListRes" serverName="clubsvr"/>
  <entry id="346" name="RpcCreateClubReq" serverName="clubsvr"/>
  <entry id="347" name="RpcCreateClubRes" serverName="clubsvr"/>
  <entry id="348" name="RpcLoginOnReq" serverName="clubsvr"/>
  <entry id="349" name="RpcLoginOnRes" serverName="clubsvr"/>
  <entry id="350" name="RpcLoginOutReq" serverName="clubsvr"/>
  <entry id="351" name="RpcLoginOutRes" serverName="clubsvr"/>
  <entry id="352" name="RpcJoinClubReq" serverName="clubsvr"/>
  <entry id="353" name="RpcJoinClubRes" serverName="clubsvr"/>
  <entry id="354" name="RpcGetClubReq" serverName="clubsvr"/>
  <entry id="355" name="RpcGetClubRes" serverName="clubsvr"/>
  <entry id="356" name="RpcCheckClubHandleReq" serverName="clubsvr"/>
  <entry id="357" name="RpcCheckClubHandleRes" serverName="clubsvr"/>
  <entry id="358" name="RpcModifyIdentityClubReq" serverName="clubsvr"/>
  <entry id="359" name="RpcModifyIdentityClubRes" serverName="clubsvr"/>
  <entry id="360" name="RpcModifyInfoClubReq" serverName="clubsvr"/>
  <entry id="361" name="RpcModifyInfoClubRes" serverName="clubsvr"/>
  <entry id="362" name="RpcQuitClubReq" serverName="clubsvr"/>
  <entry id="363" name="RpcQuitClubRes" serverName="clubsvr"/>
  <entry id="364" name="RpcDissolveReq" serverName="clubsvr"/>
  <entry id="365" name="RpcDissolveRes" serverName="clubsvr"/>
  <entry id="366" name="RpcApplyListReq" serverName="clubsvr"/>
  <entry id="367" name="RpcApplyListRes" serverName="clubsvr"/>
  <entry id="368" name="RpcKickOutReq" serverName="clubsvr"/>
  <entry id="369" name="RpcKickOutRes" serverName="clubsvr"/>
  <entry id="370" name="RpcSetUpReq" serverName="clubsvr"/>
  <entry id="371" name="RpcSetUpRes" serverName="clubsvr"/>
  <entry id="372" name="RpcClubSvrInteractReq" serverName="clubsvr"/>
  <entry id="373" name="RpcClubSvrInteractRes" serverName="clubsvr"/>
  <entry id="374" name="RpcAddClubExpReq" serverName="clubsvr"/>
  <entry id="375" name="RpcAddClubExpRes" serverName="clubsvr"/>
  <entry id="376" name="RpcUpdateQualifyingReq" serverName="clubsvr"/>
  <entry id="377" name="RpcUpdateQualifyingRes" serverName="clubsvr"/>
  <entry id="378" name="RpcClubClearApplyPlayerReq" serverName="clubsvr"/>
  <entry id="379" name="RpcClubClearApplyPlayerRes" serverName="clubsvr"/>
  <entry id="380" name="RpcClubKickOutNtfReq" serverName="gamesvr"/>
  <entry id="381" name="RpcClubKickOutNtfRes" serverName="gamesvr"/>
  <entry id="382" name="RpcClubDissolveNtfReq" serverName="gamesvr"/>
  <entry id="383" name="RpcClubDissolveNtfRes" serverName="gamesvr"/>
  <entry id="384" name="RpcClubJoinNtfReq" serverName="gamesvr"/>
  <entry id="385" name="RpcClubJoinNtfRes" serverName="gamesvr"/>
  <entry id="386" name="RpcCreateCLubCostReq" serverName="gamesvr"/>
  <entry id="387" name="RpcCreateCLubCostRes" serverName="gamesvr"/>
  <entry id="388" name="RpcClubDissolveApplyNtfReq" serverName="gamesvr"/>
  <entry id="389" name="RpcClubDissolveApplyNtfRes" serverName="gamesvr"/>
  <entry id="390" name="RpcSceneCanJoinReq" serverName="scenesvr"/>
  <entry id="391" name="RpcSceneCanJoinRes" serverName="scenesvr"/>
  <entry id="392" name="RpcGetRoomMemberBaseInfoReq" serverName="roomsvr"/>
  <entry id="393" name="RpcGetRoomMemberBaseInfoRes" serverName="roomsvr"/>
  <entry id="394" name="RpcSceneInteractActionAcceptReq" serverName="gamesvr"/>
  <entry id="395" name="RpcSceneInteractActionAcceptRes" serverName="gamesvr"/>
  <entry id="396" name="RpcSceneInteractActionActiveReq" serverName="gamesvr"/>
  <entry id="397" name="RpcSceneInteractActionActiveRes" serverName="gamesvr"/>
  <entry id="398" name="RpcSceneSyncExtraDataReq" serverName="scenesvr"/>
  <entry id="399" name="RpcSceneSyncExtraDataRes" serverName="scenesvr"/>
  <entry id="400" name="RpcCheckRoomLeaderReq" serverName="roomsvr"/>
  <entry id="401" name="RpcCheckRoomLeaderRes" serverName="roomsvr"/>
  <entry id="402" name="RpcUpdatePlayerUidCacheReq" serverName="gamesvr"/>
  <entry id="403" name="RpcUpdatePlayerUidCacheRes" serverName="gamesvr"/>
  <entry id="404" name="RpcSceneSettlementReq" serverName="gamesvr"/>
  <entry id="405" name="RpcSceneSettlementRes" serverName="gamesvr"/>
  <entry id="406" name="RpcMatchSceneCreateResultReq" serverName="roomsvr"/>
  <entry id="407" name="RpcMatchSceneCreateResultRes" serverName="roomsvr"/>
  <entry id="408" name="RpcMatchJoinResultReq" serverName="roomsvr"/>
  <entry id="409" name="RpcMatchJoinResultRes" serverName="roomsvr"/>
  <entry id="410" name="RpcMatchSceneCreateSuccReq" serverName="gamesvr"/>
  <entry id="411" name="RpcMatchSceneCreateSuccRes" serverName="gamesvr"/>
  <entry id="412" name="RpcMatchJoinSuccReq" serverName="gamesvr"/>
  <entry id="413" name="RpcMatchJoinSuccRes" serverName="gamesvr"/>
  <entry id="414" name="RpcSceneInteractActionInviteReq" serverName="gamesvr"/>
  <entry id="415" name="RpcSceneInteractActionInviteRes" serverName="gamesvr"/>
  <entry id="416" name="RpcSceneGetRoundSceneIdReq" serverName="scenesvr"/>
  <entry id="417" name="RpcSceneGetRoundSceneIdRes" serverName="scenesvr"/>
  <entry id="418" name="RpcSceneGetRoundSceneReq" serverName="scenesvr"/>
  <entry id="419" name="RpcSceneGetRoundSceneRes" serverName="scenesvr"/>
  <entry id="420" name="RpcSceneInteractActionCancelReq" serverName="gamesvr"/>
  <entry id="421" name="RpcSceneInteractActionCancelRes" serverName="gamesvr"/>
  <entry id="422" name="RpcSceneGetCurrentSceneIdReq" serverName="gamesvr"/>
  <entry id="423" name="RpcSceneGetCurrentSceneIdRes" serverName="gamesvr"/>
  <entry id="424" name="RpcEndRoundMsgReq" serverName="roomsvr"/>
  <entry id="425" name="RpcEndRoundMsgRes" serverName="roomsvr"/>
  <entry id="426" name="RpcScenePlayerActionReq" serverName="gamesvr"/>
  <entry id="427" name="RpcScenePlayerActionRes" serverName="gamesvr"/>
  <entry id="428" name="RpcSceneTryJoinReq" serverName="scenesvr"/>
  <entry id="429" name="RpcSceneTryJoinRes" serverName="scenesvr"/>
  <entry id="430" name="RpcSceneCanReconnectReq" serverName="scenesvr"/>
  <entry id="431" name="RpcSceneCanReconnectRes" serverName="scenesvr"/>
  <entry id="432" name="RpcScenePlayerHallCoinRewardReq" serverName="gamesvr"/>
  <entry id="433" name="RpcScenePlayerHallCoinRewardRes" serverName="gamesvr"/>
  <entry id="434" name="RpcSyncPlayerInfoToSceneReq" serverName="scenesvr"/>
  <entry id="435" name="RpcSyncPlayerInfoToSceneRes" serverName="scenesvr"/>
  <entry id="436" name="RpcSceneGetRoundSettlementReq" serverName="scenesvr"/>
  <entry id="437" name="RpcSceneGetRoundSettlementRes" serverName="scenesvr"/>
  <entry id="438" name="RpcPlayerJumpToSceneReq" serverName="scenesvr"/>
  <entry id="439" name="RpcPlayerJumpToSceneRes" serverName="scenesvr"/>
  <entry id="440" name="RpcNotifyPlayerOnlineReq" serverName="gamesvr"/>
  <entry id="441" name="RpcNotifyPlayerOnlineRes" serverName="gamesvr"/>
  <entry id="442" name="RpcNotifyPlayerOnlineStatusReq" serverName="gamesvr"/>
  <entry id="443" name="RpcNotifyPlayerOnlineStatusRes" serverName="gamesvr"/>
  <entry id="444" name="RpcRoomLobbyGatherReq" serverName="roomsvr"/>
  <entry id="445" name="RpcRoomLobbyGatherRes" serverName="roomsvr"/>
  <entry id="446" name="RpcRoomLobbyGatherNtfReq" serverName="gamesvr"/>
  <entry id="447" name="RpcRoomLobbyGatherNtfRes" serverName="gamesvr"/>
  <entry id="448" name="RpcRoomLeaderTransitReq" serverName="roomsvr"/>
  <entry id="449" name="RpcRoomLeaderTransitRes" serverName="roomsvr"/>
  <entry id="450" name="RpcRoomDisbandReq" serverName="roomsvr"/>
  <entry id="451" name="RpcRoomDisbandRes" serverName="roomsvr"/>
  <entry id="452" name="RpcRoomStartMatchNtfReq" serverName="gamesvr"/>
  <entry id="453" name="RpcRoomStartMatchNtfRes" serverName="gamesvr"/>
  <entry id="454" name="RpcConfirmStartMatchReq" serverName="roomsvr"/>
  <entry id="455" name="RpcConfirmStartMatchRes" serverName="roomsvr"/>
  <entry id="456" name="RpcRoomStartMatchCancelNtfReq" serverName="gamesvr"/>
  <entry id="457" name="RpcRoomStartMatchCancelNtfRes" serverName="gamesvr"/>
  <entry id="458" name="RpcRoomRecruitPublishReq" serverName="roomsvr"/>
  <entry id="459" name="RpcRoomRecruitPublishRes" serverName="roomsvr"/>
  <entry id="460" name="RpcLobbyJumpNtfReq" serverName="gamesvr"/>
  <entry id="461" name="RpcLobbyJumpNtfRes" serverName="gamesvr"/>
  <entry id="462" name="RpcGetPlayerLobbyIdReq" serverName="gamesvr"/>
  <entry id="463" name="RpcGetPlayerLobbyIdRes" serverName="gamesvr"/>
  <entry id="464" name="LobbySvrInteractReq" serverName="lobbysvr"/>
  <entry id="465" name="LobbySvrInteractRes" serverName="lobbysvr"/>
  <entry id="466" name="RpcLobbyFindReq" serverName="lobbysvr"/>
  <entry id="467" name="RpcLobbyFindRes" serverName="lobbysvr"/>
  <entry id="468" name="RpcLobbyReconnectReq" serverName="lobbysvr"/>
  <entry id="469" name="RpcLobbyReconnectRes" serverName="lobbysvr"/>
  <entry id="470" name="RpcLobbyEnterReq" serverName="lobbysvr"/>
  <entry id="471" name="RpcLobbyEnterRes" serverName="lobbysvr"/>
  <entry id="472" name="RpcLobbyPlayerOfflineReq" serverName="lobbysvr"/>
  <entry id="473" name="RpcLobbyPlayerOfflineRes" serverName="lobbysvr"/>
  <entry id="474" name="RpcLobbyExitReq" serverName="lobbysvr"/>
  <entry id="475" name="RpcLobbyExitRes" serverName="lobbysvr"/>
  <entry id="476" name="RpcLobbyManageReq" serverName="lobbysvr"/>
  <entry id="477" name="RpcLobbyManageRes" serverName="lobbysvr"/>
  <entry id="478" name="RpcLobbyGMCommandReq" serverName="lobbysvr"/>
  <entry id="479" name="RpcLobbyGMCommandRes" serverName="lobbysvr"/>
  <entry id="480" name="RpcLobbyGetNeighborReq" serverName="lobbysvr"/>
  <entry id="481" name="RpcLobbyGetNeighborRes" serverName="lobbysvr"/>
  <entry id="482" name="RpcLobbyTryJoinReq" serverName="lobbysvr"/>
  <entry id="483" name="RpcLobbyTryJoinRes" serverName="lobbysvr"/>
  <entry id="484" name="RpcPlayerJumpToLobbyReq" serverName="lobbysvr"/>
  <entry id="485" name="RpcPlayerJumpToLobbyRes" serverName="lobbysvr"/>
  <entry id="486" name="RpcFetchTopRankReq" serverName="ranksvr"/>
  <entry id="487" name="RpcFetchTopRankRes" serverName="ranksvr"/>
  <entry id="488" name="RpcChangeSubRanksReq" serverName="ranksvr"/>
  <entry id="489" name="RpcChangeSubRanksRes" serverName="ranksvr"/>
  <entry id="490" name="RpcLobbyOfflineReq" serverName="lobbysvr"/>
  <entry id="491" name="RpcLobbyOfflineRes" serverName="lobbysvr"/>
  <entry id="492" name="RpcRoomRecruitQueryReq" serverName="roomsvr"/>
  <entry id="493" name="RpcRoomRecruitQueryRes" serverName="roomsvr"/>
  <entry id="494" name="RpcRoomRecruitJoinReq" serverName="roomsvr"/>
  <entry id="495" name="RpcRoomRecruitJoinRes" serverName="roomsvr"/>
  <entry id="496" name="RpcRoomRecruitQuickJoinReq" serverName="roomsvr"/>
  <entry id="497" name="RpcRoomRecruitQuickJoinRes" serverName="roomsvr"/>
  <entry id="498" name="RpcDeliverGoodsReq" serverName="gamesvr"/>
  <entry id="499" name="RpcDeliverGoodsRes" serverName="gamesvr"/>
  <entry id="500" name="RpcSquadCreateReq" serverName="roomsvr"/>
  <entry id="501" name="RpcSquadCreateRes" serverName="roomsvr"/>
  <entry id="502" name="RpcSquadJoinReq" serverName="roomsvr"/>
  <entry id="503" name="RpcSquadJoinRes" serverName="roomsvr"/>
  <entry id="504" name="RpcSquadUpdateMemberInfoReq" serverName="roomsvr"/>
  <entry id="505" name="RpcSquadUpdateMemberInfoRes" serverName="roomsvr"/>
  <entry id="506" name="RpcSquadUpdateTaskStateReq" serverName="roomsvr"/>
  <entry id="507" name="RpcSquadUpdateTaskStateRes" serverName="roomsvr"/>
  <entry id="508" name="RpcSquadPreQuitReq" serverName="roomsvr"/>
  <entry id="509" name="RpcSquadPreQuitRes" serverName="roomsvr"/>
  <entry id="510" name="RpcSquadConfirmQuitReq" serverName="roomsvr"/>
  <entry id="511" name="RpcSquadConfirmQuitRes" serverName="roomsvr"/>
  <entry id="512" name="RpcSquadInfoReq" serverName="roomsvr"/>
  <entry id="513" name="RpcSquadInfoRes" serverName="roomsvr"/>
  <entry id="514" name="RpcMatchGMCommandReq" serverName="matchsvr"/>
  <entry id="515" name="RpcMatchGMCommandRes" serverName="matchsvr"/>
  <entry id="516" name="TestUgcSvrReq" serverName="ugcsvr"/>
  <entry id="517" name="TestUgcSvrRes" serverName="ugcsvr"/>
  <entry id="518" name="RpcApplyUgcKeyReq" serverName="ugcsvr"/>
  <entry id="519" name="RpcApplyUgcKeyRes" serverName="ugcsvr"/>
  <entry id="520" name="RpcApplyBucketReq" serverName="ugcsvr"/>
  <entry id="521" name="RpcApplyBucketRes" serverName="ugcsvr"/>
  <entry id="522" name="RpcMapPublishReq" serverName="ugcsvr"/>
  <entry id="523" name="RpcMapPublishRes" serverName="ugcsvr"/>
  <entry id="524" name="RpcMapPublishListReq" serverName="ugcsvr"/>
  <entry id="525" name="RpcMapPublishListRes" serverName="ugcsvr"/>
  <entry id="526" name="RpcAllocateLobbyReq" serverName="gamesvr"/>
  <entry id="527" name="RpcAllocateLobbyRes" serverName="gamesvr"/>
  <entry id="528" name="RpcMapReportReq" serverName="ugcplatsvr"/>
  <entry id="529" name="RpcMapReportRes" serverName="ugcplatsvr"/>
  <entry id="530" name="RpcMessageSlipOpReq" serverName="gamesvr"/>
  <entry id="531" name="RpcMessageSlipOpRes" serverName="gamesvr"/>
  <entry id="532" name="RpcTopRankRedisRefreshReq" serverName="ranksvr"/>
  <entry id="533" name="RpcTopRankRedisRefreshAndFetchReq" serverName="ranksvr"/>
  <entry id="534" name="RpcTopRankRedisRefreshAndFetchRes" serverName="ranksvr"/>
  <entry id="535" name="RpcRemoveTopRankReq" serverName="ranksvr"/>
  <entry id="536" name="RpcRemoveTopRankRes" serverName="ranksvr"/>
  <entry id="537" name="RpcRemoveOneUserTopRankReq" serverName="ranksvr"/>
  <entry id="538" name="RpcBatchChangeSubRanksReq" serverName="ranksvr"/>
  <entry id="539" name="RpcBatchChangeSubRanksRes" serverName="ranksvr"/>
  <entry id="540" name="RpcMapCheckMd5Req" serverName="ugcsvr"/>
  <entry id="541" name="RpcMapCheckMd5Res" serverName="ugcsvr"/>
  <entry id="542" name="RpcOperateMapReq" serverName="ugcsvr"/>
  <entry id="543" name="RpcOperateMapRes" serverName="ugcsvr"/>
  <entry id="544" name="RpcGetLevelInfoReq" serverName="ugcsvr"/>
  <entry id="545" name="RpcGetLevelInfoRes" serverName="ugcsvr"/>
  <entry id="546" name="RpcGetUgcPlayerInfoReq" serverName="ugcsvr"/>
  <entry id="547" name="RpcGetUgcPlayerInfoRes" serverName="ugcsvr"/>
  <entry id="548" name="RpcMatchProcessNtfReq" serverName="gamesvr"/>
  <entry id="549" name="RpcMatchProcessNtfRes" serverName="gamesvr"/>
  <entry id="550" name="RpcMapScreenReq" serverName="ugcsvr"/>
  <entry id="551" name="RpcMapScreenRes" serverName="ugcsvr"/>
  <entry id="552" name="RpcMapTakeOffReq" serverName="ugcsvr"/>
  <entry id="553" name="RpcMapTakeOffRes" serverName="ugcsvr"/>
  <entry id="554" name="RpcMapPlayerReportReq" serverName="ugcplatsvr"/>
  <entry id="555" name="RpcMapPlayerReportRes" serverName="ugcplatsvr"/>
  <entry id="556" name="RpcRoomReportReq" serverName="ugcplatsvr"/>
  <entry id="557" name="RpcRoomReportRes" serverName="ugcplatsvr"/>
  <entry id="558" name="UgcPlayerDataReportReq" serverName="ugcplatsvr"/>
  <entry id="559" name="UgcPlayerDataReportRes" serverName="ugcplatsvr"/>
  <entry id="560" name="UgcServletMsgReq" serverName="ugcplatsvr"/>
  <entry id="561" name="UgcServletMsgRes" serverName="ugcplatsvr"/>
  <entry id="562" name="RpcApplyPageListReq" serverName="ugcsvr"/>
  <entry id="563" name="RpcApplyPageListRes" serverName="ugcsvr"/>
  <entry id="564" name="RpcSearchMapReq" serverName="ugcsvr"/>
  <entry id="565" name="RpcSearchMapRes" serverName="ugcsvr"/>
  <entry id="566" name="RpcMapModifyReq" serverName="ugcsvr"/>
  <entry id="567" name="RpcMapModifyRes" serverName="ugcsvr"/>
  <entry id="568" name="UgcPublishDetailsReq" serverName="ugcsvr"/>
  <entry id="569" name="UgcPublishDetailsRes" serverName="ugcsvr"/>
  <entry id="570" name="RpcUgcPublishDetailsReq" serverName="ugcsvr"/>
  <entry id="571" name="RpcUgcPublishDetailsRes" serverName="ugcsvr"/>
  <entry id="572" name="RpcApplyMapDataReq" serverName="ugcsvr"/>
  <entry id="573" name="RpcApplyMapDataRes" serverName="ugcsvr"/>
  <entry id="574" name="RpcSearchByTypeReq" serverName="ugcsvr"/>
  <entry id="575" name="RpcSearchByTypeRes" serverName="ugcsvr"/>
  <entry id="576" name="TestUgcPlatSvrReq" serverName="ugcplatsvr"/>
  <entry id="577" name="TestUgcPlatSvrRes" serverName="ugcplatsvr"/>
  <entry id="578" name="RpcCreatorReportReq" serverName="ugcplatsvr"/>
  <entry id="579" name="RpcCreatorReportRes" serverName="ugcplatsvr"/>
  <entry id="580" name="RpcUpdateUgcExpReq" serverName="ugcsvr"/>
  <entry id="581" name="RpcUpdateUgcExpRes" serverName="ugcsvr"/>
  <entry id="582" name="RpcGetMapUgcExpReq" serverName="ugcsvr"/>
  <entry id="583" name="RpcGetMapUgcExpRes" serverName="ugcsvr"/>
  <entry id="584" name="RpcOpSubTopReq" serverName="ugcsvr"/>
  <entry id="585" name="RpcOpSubTopRes" serverName="ugcsvr"/>
  <entry id="586" name="RpcOpSubHandlerReq" serverName="ugcsvr"/>
  <entry id="587" name="RpcOpSubHandlerRes" serverName="ugcsvr"/>
  <entry id="588" name="RpcOperateCancelMapReq" serverName="ugcsvr"/>
  <entry id="589" name="RpcOperateCancelMapRes" serverName="ugcsvr"/>
  <entry id="590" name="RpcUgcGetPageListMsgReq" serverName="ugcplatsvr"/>
  <entry id="591" name="RpcUgcGetPageListMsgRes" serverName="ugcplatsvr"/>
  <entry id="592" name="RpcSearchReq" serverName="ugcplatsvr"/>
  <entry id="593" name="RpcSearchRes" serverName="ugcplatsvr"/>
  <entry id="594" name="RpcMapUgcBestRecordReq" serverName="ugcsvr"/>
  <entry id="595" name="RpcMapUgcBestRecordRes" serverName="ugcsvr"/>
  <entry id="596" name="RpcLobbyChangeReq" serverName="lobbysvr"/>
  <entry id="597" name="RpcLobbyChangeRes" serverName="lobbysvr"/>
  <entry id="598" name="RpcMapUgcBaseInfoReq" serverName="ugcsvr"/>
  <entry id="599" name="RpcMapUgcBaseInfoRes" serverName="ugcsvr"/>
  <entry id="600" name="RpcGetUgcPlayerProfileReq" serverName="ugcsvr"/>
  <entry id="601" name="RpcGetUgcPlayerProfileRes" serverName="ugcsvr"/>
  <entry id="602" name="RpcGetSubscribePlayerMapsReq" serverName="ugcsvr"/>
  <entry id="603" name="RpcGetSubscribePlayerMapsRes" serverName="ugcsvr"/>
  <entry id="604" name="RpcGetPlayedMapsReq" serverName="ugcsvr"/>
  <entry id="605" name="RpcGetPlayedMapsRes" serverName="ugcsvr"/>
  <entry id="606" name="RpcUgcGetUserRankReq" serverName="ugcplatsvr"/>
  <entry id="607" name="RpcUgcGetUserRankRes" serverName="ugcplatsvr"/>
  <entry id="608" name="RpcUgcFetchRankReq" serverName="ugcplatsvr"/>
  <entry id="609" name="RpcUgcFetchRankRes" serverName="ugcplatsvr"/>
  <entry id="610" name="RpcUpdateAndGetResultTopRankReq" serverName="ranksvr"/>
  <entry id="611" name="RpcUpdateAndGetResultTopRankRes" serverName="ranksvr"/>
  <entry id="612" name="RpcChangeSubRanksAndGetResultReq" serverName="ranksvr"/>
  <entry id="613" name="RpcChangeSubRanksAndGetResultRes" serverName="ranksvr"/>
  <entry id="614" name="RpcRoomPositionUpdateReq" serverName="roomsvr"/>
  <entry id="615" name="RpcRoomPositionUpdateRes" serverName="roomsvr"/>
  <entry id="616" name="RpcUgcRoomListReq" serverName="roomsvr"/>
  <entry id="617" name="RpcUgcRoomListRes" serverName="roomsvr"/>
  <entry id="618" name="RpcRecordPlayerTlogReq" serverName="gamesvr"/>
  <entry id="619" name="RpcRecordPlayerTlogRes" serverName="gamesvr"/>
  <entry id="620" name="RpcUgcRoomPreStartReq" serverName="roomsvr"/>
  <entry id="621" name="RpcUgcRoomPreStartRes" serverName="roomsvr"/>
  <entry id="622" name="RpcGetDailyMapsReq" serverName="ugcplatsvr"/>
  <entry id="623" name="RpcGetDailyMapsRes" serverName="ugcplatsvr"/>
  <entry id="624" name="RpcUgcSingleStageStartReq" serverName="ugcsvr"/>
  <entry id="625" name="RpcUgcSingleStageStartRes" serverName="ugcsvr"/>
  <entry id="626" name="RpcUgcSingleStageEndReq" serverName="ugcsvr"/>
  <entry id="627" name="RpcUgcSingleStageEndRes" serverName="ugcsvr"/>
  <entry id="628" name="RpcUgcDailyStageChangeMapReq" serverName="ugcsvr"/>
  <entry id="629" name="RpcUgcDailyStageChangeMapRes" serverName="ugcsvr"/>
  <entry id="630" name="RpcUgcDailyStageResetReq" serverName="ugcsvr"/>
  <entry id="631" name="RpcUgcDailyStageResetRes" serverName="ugcsvr"/>
  <entry id="632" name="RpcGetUgcDailyStageInfoReq" serverName="ugcsvr"/>
  <entry id="633" name="RpcGetUgcDailyStageInfoRes" serverName="ugcsvr"/>
  <entry id="634" name="RpcUgcRoomQuickJoinReq" serverName="roomsvr"/>
  <entry id="635" name="RpcUgcRoomQuickJoinRes" serverName="roomsvr"/>
  <entry id="636" name="RpcAllocSeqReq" serverName="seqsvr"/>
  <entry id="637" name="RpcAllocSeqRes" serverName="seqsvr"/>
  <entry id="638" name="RpcFreeSeqReq" serverName="seqsvr"/>
  <entry id="639" name="RpcFreeSeqRes" serverName="seqsvr"/>
  <entry id="640" name="RpcQuerySeqInfoReq" serverName="seqsvr"/>
  <entry id="641" name="RpcQuerySeqInfoRes" serverName="seqsvr"/>
  <entry id="642" name="RpcSeqHeartbeatReq" serverName="seqsvr"/>
  <entry id="643" name="RpcSeqHeartbeatRes" serverName="seqsvr"/>
  <entry id="644" name="RpcUgcIsInWhiteListReq" serverName="ugcplatsvr"/>
  <entry id="645" name="RpcUgcIsInWhiteListRes" serverName="ugcplatsvr"/>
  <entry id="646" name="RpcUgcDailyCheckResetReq" serverName="ugcsvr"/>
  <entry id="647" name="RpcUgcDailyCheckResetRes" serverName="ugcsvr"/>
  <entry id="648" name="RpcLobbyInviteNtfReq" serverName="gamesvr"/>
  <entry id="649" name="RpcLobbyInviteReplyNtfReq" serverName="gamesvr"/>
  <entry id="650" name="RpcLobbyInviteNtfRes" serverName="gamesvr"/>
  <entry id="651" name="RpcLobbyInviteReplyNtfRes" serverName="gamesvr"/>
  <entry id="652" name="RpcUgcLoginNtfReq" serverName="ugcsvr"/>
  <entry id="653" name="RpcNotifyIntimacyChangeReq" serverName="gamesvr"/>
  <entry id="654" name="RpcNotifyIntimacyChangeRes" serverName="gamesvr"/>
  <entry id="655" name="RpcUgcMapStatusChangeNtfReq" serverName="gamesvr"/>
  <entry id="656" name="RpcUgcLvChangeNtfReq" serverName="gamesvr"/>
  <entry id="657" name="RpcUgcCreateMapNtfReq" serverName="ugcsvr"/>
  <entry id="658" name="RpcNotifyRelationMsgReq" serverName="gamesvr"/>
  <entry id="659" name="RpcNotifyRelationMsgRes" serverName="gamesvr"/>
  <entry id="660" name="RpcUgcDailyResetNtfReq" serverName="gamesvr"/>
  <entry id="661" name="RpcCosCopyReq" serverName="ugcsvr"/>
  <entry id="662" name="RpcCosCopyRes" serverName="ugcsvr"/>
  <entry id="663" name="RpcRoomRecruitInfoReq" serverName="roomsvr"/>
  <entry id="664" name="RpcRoomRecruitInfoRes" serverName="roomsvr"/>
  <entry id="665" name="RpcKickPlayerLogoutReq" serverName="gamesvr"/>
  <entry id="666" name="RpcUgcDailyResetNtfRes" serverName="gamesvr"/>
  <entry id="667" name="RpcGetUgcPicCosUrlReq" serverName="ugcsvr"/>
  <entry id="668" name="RpcGetUgcPicCosUrlRes" serverName="ugcsvr"/>
  <entry id="669" name="RpcRoomModifyDisplayInfoReq" serverName="roomsvr"/>
  <entry id="670" name="RpcRoomModifyDisplayInfoRes" serverName="roomsvr"/>
  <entry id="671" name="RpcForwardChatMsgPullReq" serverName="chatsvr"/>
  <entry id="672" name="RpcForwardChatMsgPullRes" serverName="chatsvr"/>
  <entry id="673" name="RpcCleanUserChatReq" serverName="chatsvr"/>
  <entry id="674" name="RpcRoomModifyInfoReq" serverName="roomsvr"/>
  <entry id="675" name="RpcRoomModifyInfoRes" serverName="roomsvr"/>
  <entry id="676" name="RpcCleanUserChatRes" serverName="chatsvr"/>
  <entry id="677" name="RpcNotifyRelationApplyReq" serverName="gamesvr"/>
  <entry id="678" name="RpcNotifyRelationApplyRes" serverName="gamesvr"/>
  <entry id="679" name="RpcNotifyRelationAgreeReq" serverName="gamesvr"/>
  <entry id="680" name="RpcNotifyRelationAgreeRes" serverName="gamesvr"/>
  <entry id="681" name="RpcRoomChangeInfoReq" serverName="roomsvr"/>
  <entry id="682" name="RpcRoomChangeInfoRes" serverName="roomsvr"/>
  <entry id="683" name="RpcRoomStateCheckReq" serverName="roomsvr"/>
  <entry id="684" name="RpcRoomStateCheckRes" serverName="roomsvr"/>
  <entry id="685" name="RpcRoomPositionExchangeNtfReq" serverName="gamesvr"/>
  <entry id="686" name="RpcRoomPositionExchangeNtfRes" serverName="gamesvr"/>
  <entry id="687" name="RpcRemoveOneUserTopRankRes" serverName="ranksvr"/>
  <entry id="688" name="RpcSyncBlackListInfoReq" serverName="dirsvr"/>
  <entry id="689" name="RpcSyncBlackListInfoRes" serverName="dirsvr"/>
  <entry id="690" name="RpcSyncRemoveBlackListInfoReq" serverName="dirsvr"/>
  <entry id="691" name="RpcSyncRemoveBlackListInfoRes" serverName="dirsvr"/>
  <entry id="692" name="RpcRoomAddRobotReq" serverName="roomsvr"/>
  <entry id="693" name="RpcRoomAddRobotRes" serverName="roomsvr"/>
  <entry id="694" name="RpcRoomRemoveRobotReq" serverName="roomsvr"/>
  <entry id="695" name="RpcRoomRemoveRobotRes" serverName="roomsvr"/>
  <entry id="696" name="RpcRoomBroadcastChannelReq" serverName="roomsvr"/>
  <entry id="697" name="RpcRoomBroadcastChannelRes" serverName="roomsvr"/>
  <entry id="698" name="RpcRoomBroadcastInfoReq" serverName="gamesvr"/>
  <entry id="699" name="RpcRoomBroadcastInfoRes" serverName="gamesvr"/>
  <entry id="700" name="RpcRoomMapDownloadProcessBroadcastReq" serverName="roomsvr"/>
  <entry id="701" name="RpcRoomMapDownloadProcessBroadcastRes" serverName="roomsvr"/>
  <entry id="702" name="RpcUgcGetRelationDataReq" serverName="gamesvr"/>
  <entry id="703" name="RpcUgcGetRelationDataRes" serverName="gamesvr"/>
  <entry id="704" name="RpcUgcGetPlayerPublicDataReq" serverName="gamesvr"/>
  <entry id="705" name="RpcUgcGetPlayerPublicDataRes" serverName="gamesvr"/>
  <entry id="706" name="TestRouteSvrReq" serverName="routesvr"/>
  <entry id="707" name="TestRouteSvrRes" serverName="routesvr"/>
  <entry id="708" name="RpcApplyInfoReq" serverName="ugcsvr"/>
  <entry id="709" name="RpcApplyInfoRes" serverName="ugcsvr"/>
  <entry id="710" name="RpcGetUgcPublishMapReq" serverName="ugcsvr"/>
  <entry id="711" name="RpcGetUgcPublishMapRes" serverName="ugcsvr"/>
  <entry id="712" name="RpcUgcInfoNtfReq" serverName="ugcsvr"/>
  <entry id="713" name="RpcSaveLevelInfoReq" serverName="ugcsvr"/>
  <entry id="714" name="RpcSaveLevelInfoRes" serverName="ugcsvr"/>
  <entry id="715" name="RpcCreateUgcEntityReq" serverName="ugcsvr"/>
  <entry id="716" name="RpcCreateUgcEntityRes" serverName="ugcsvr"/>
  <entry id="717" name="RpcCopyUgcEntityReq" serverName="ugcsvr"/>
  <entry id="718" name="RpcCopyUgcEntityRes" serverName="ugcsvr"/>
  <entry id="719" name="RpcModifyPublishReq" serverName="ugcsvr"/>
  <entry id="720" name="RpcModifyPublishRes" serverName="ugcsvr"/>
  <entry id="721" name="RpcModifyUgcBaseInfoReq" serverName="ugcsvr"/>
  <entry id="722" name="RpcModifyUgcBaseInfoRes" serverName="ugcsvr"/>
  <entry id="723" name="RpcUgcEntityListReq" serverName="ugcsvr"/>
  <entry id="724" name="RpcUgcEntityListRes" serverName="ugcsvr"/>
  <entry id="725" name="RpcNickPromptReq" serverName="ugcsvr"/>
  <entry id="726" name="RpcNickPromptRes" serverName="ugcsvr"/>
  <entry id="727" name="RpcUseNickReq" serverName="ugcsvr"/>
  <entry id="728" name="RpcUseNickRes" serverName="ugcsvr"/>
  <entry id="729" name="RpcNickAvailableReq" serverName="ugcsvr"/>
  <entry id="730" name="RpcNickAvailableRes" serverName="ugcsvr"/>
  <entry id="731" name="RpcGMSetMapInfoReq" serverName="ugcsvr"/>
  <entry id="732" name="RpcGMSetMapInfoRes" serverName="ugcsvr"/>
  <entry id="733" name="RpcUpdatePlayerUgcExpAndUgcLvNtfReq" serverName="ugcsvr"/>
  <entry id="734" name="RpcIdipModifyMapInfoReq" serverName="ugcsvr"/>
  <entry id="735" name="RpcIdipModifyMapInfoRes" serverName="ugcsvr"/>
  <entry id="736" name="RpcChangeUgcPublishNameReq" serverName="ugcsvr"/>
  <entry id="737" name="RpcChangeUgcPublishNameRes" serverName="ugcsvr"/>
  <entry id="738" name="RpcGMClearPlayerUgcExpReq" serverName="ugcsvr"/>
  <entry id="739" name="RpcGMClearPlayerUgcExpRes" serverName="ugcsvr"/>
  <entry id="740" name="RpcGMClearPlayerReq" serverName="ugcsvr"/>
  <entry id="741" name="RpcGMClearPlayerRes" serverName="ugcsvr"/>
  <entry id="742" name="RpcUgcStageChangeReq" serverName="ugcsvr"/>
  <entry id="743" name="RpcUgcStageChangeRes" serverName="ugcsvr"/>
  <entry id="744" name="RpcDownLoadPublishMapReq" serverName="ugcsvr"/>
  <entry id="745" name="RpcDownLoadPublishMapRes" serverName="ugcsvr"/>
  <entry id="746" name="RpcUgcGetGroupCommunityInfoReq" serverName="ugcsvr"/>
  <entry id="747" name="RpcUgcGetGroupCommunityInfoRes" serverName="ugcsvr"/>
  <entry id="748" name="RpcUgcSetGroupCommuntiyOpenReq" serverName="ugcsvr"/>
  <entry id="749" name="RpcUgcSetGroupCommuntiyOpenRes" serverName="ugcsvr"/>
  <entry id="750" name="RpcDeleteNickNameReq" serverName="ugcsvr"/>
  <entry id="751" name="RpcDeleteNickNameRes" serverName="ugcsvr"/>
  <entry id="752" name="RpcMatchHeartBeatReq" serverName="matchsvr"/>
  <entry id="753" name="RpcMatchHeartBeatRes" serverName="matchsvr"/>
  <entry id="754" name="RpcDownLoadPublishKeyInfoReq" serverName="ugcsvr"/>
  <entry id="755" name="RpcDownLoadPublishKeyInfoRes" serverName="ugcsvr"/>
  <entry id="756" name="RpcRoomInfoReq" serverName="roomsvr"/>
  <entry id="757" name="RpcRoomInfoRes" serverName="roomsvr"/>
  <entry id="758" name="RpcGetCollectMapsReq" serverName="ugcsvr"/>
  <entry id="759" name="RpcGetCollectMapsRes" serverName="ugcsvr"/>
  <entry id="760" name="RpcUgcUpdatePublishMetaReq" serverName="ugcsvr"/>
  <entry id="761" name="RpcUgcUpdatePublishMetaRes" serverName="ugcsvr"/>
  <entry id="762" name="RpcGetOpPlayerReq" serverName="ugcsvr"/>
  <entry id="763" name="RpcGetOpPlayerRes" serverName="ugcsvr"/>
  <entry id="764" name="RpcGetPublishMapReq" serverName="ugcsvr"/>
  <entry id="765" name="RpcGetPublishMapRes" serverName="ugcsvr"/>
  <entry id="766" name="RpcGetRecommendSubsReq" serverName="ugcplatsvr"/>
  <entry id="767" name="RpcGetRecommendSubsRes" serverName="ugcplatsvr"/>
  <entry id="768" name="RpcGetGroupMemberListReq" serverName="chatsvr"/>
  <entry id="769" name="RpcGetGroupMemberListRes" serverName="chatsvr"/>
  <entry id="770" name="RpcRoomMapDownloadReminderReq" serverName="roomsvr"/>
  <entry id="771" name="RpcRoomMapDownloadReminderRes" serverName="roomsvr"/>
  <entry id="772" name="RpcLobbyAllocReq" serverName="lobbysvr"/>
  <entry id="773" name="RpcLobbyAllocRes" serverName="lobbysvr"/>
  <entry id="774" name="RpcLobbyReallocSvrReq" serverName="lobbyallocsvr"/>
  <entry id="775" name="RpcLobbyReallocSvrRes" serverName="lobbyallocsvr"/>
  <entry id="776" name="migrateVersionGroupLobbyReq" serverName="lobbysvr"/>
  <entry id="777" name="migrateVersionGroupLobbyRes" serverName="lobbysvr"/>
  <entry id="778" name="RpcMigrateVersionGroupLobbyReq" serverName="lobbysvr"/>
  <entry id="779" name="RpcMigrateVersionGroupLobbyRes" serverName="lobbysvr"/>
  <entry id="780" name="RpcTlogTopRankReq" serverName="ranksvr"/>
  <entry id="781" name="RpcTlogTopRankRes" serverName="ranksvr"/>
  <entry id="782" name="RpcGmBatchUpdateMapDescReq" serverName="ugcsvr"/>
  <entry id="783" name="RpcGmBatchUpdateMapDescRes" serverName="ugcsvr"/>
  <entry id="784" name="RpcGmUpdateUgcMapStatusReq" serverName="ugcsvr"/>
  <entry id="785" name="RpcGmUpdateUgcMapStatusRes" serverName="ugcsvr"/>
  <entry id="786" name="TestMidasSvrReq" serverName="midassvr"/>
  <entry id="787" name="TestMidasSvrRes" serverName="midassvr"/>
  <entry id="788" name="RpcMidasChargeFlowReq" serverName="midassvr"/>
  <entry id="789" name="RpcMidasChargeFlowRes" serverName="midassvr"/>
  <entry id="790" name="IdipRpcByHashKeyReq" serverName="any"/>
  <entry id="791" name="IdipRpcByHashKeyRes" serverName="any"/>
  <entry id="794" name="RpcGetCreatorInfoReq" serverName="ugcsvr"/>
  <entry id="795" name="RpcGetCreatorInfoRes" serverName="ugcsvr"/>
  <entry id="796" name="RpcXiaoWoInfoNtfReq" serverName="gamesvr"/>
  <entry id="797" name="RpcXiaoWoInfoNtfRes" serverName="gamesvr"/>
  <entry id="798" name="RpcXiaoWoDsInfoNtfReq" serverName="gamesvr"/>
  <entry id="799" name="RpcXiaoWoDsInfoNtfRes" serverName="gamesvr"/>
  <entry id="800" name="RpcXiaoWoKickNtfReq" serverName="gamesvr"/>
  <entry id="801" name="RpcXiaoWoAttrNtfReq" serverName="gamesvr"/>
  <entry id="802" name="RpcBulletinPublishReq" serverName="gamesvr"/>
  <entry id="803" name="RpcBulletinPublishRes" serverName="gamesvr"/>
  <entry id="804" name="RpcXiaoWoPlatNtfReq" serverName="gamesvr"/>
  <entry id="805" name="RpcPartyInfoNtfReq" serverName="gamesvr"/>
  <entry id="806" name="XiaowoSvrInteractReq" serverName="xiaowosvr"/>
  <entry id="807" name="XiaowoSvrInteractRes" serverName="xiaowosvr"/>
  <entry id="808" name="RpcWateringReq" serverName="xiaowosvr"/>
  <entry id="809" name="RpcWateringRes" serverName="xiaowosvr"/>
  <entry id="810" name="RpcShakeReq" serverName="xiaowosvr"/>
  <entry id="811" name="RpcShakeRes" serverName="xiaowosvr"/>
  <entry id="812" name="RpcPickupItemReq" serverName="xiaowosvr"/>
  <entry id="813" name="RpcPickupItemRes" serverName="xiaowosvr"/>
  <entry id="814" name="RpcXiaoWoCreateReq" serverName="xiaowosvr"/>
  <entry id="815" name="RpcXiaoWoCreateRes" serverName="xiaowosvr"/>
  <entry id="816" name="RpcXiaoWoPublishReq" serverName="xiaowosvr"/>
  <entry id="817" name="RpcXiaoWoPublishRes" serverName="xiaowosvr"/>
  <entry id="818" name="RpcXiaoWoEnterReq" serverName="xiaowosvr"/>
  <entry id="819" name="RpcXiaoWoEnterRes" serverName="xiaowosvr"/>
  <entry id="820" name="RpcXiaoWoExitReq" serverName="xiaowosvr"/>
  <entry id="821" name="RpcXiaoWoExitRes" serverName="xiaowosvr"/>
  <entry id="822" name="RpcXiaoWoLikeReq" serverName="xiaowosvr"/>
  <entry id="823" name="RpcXiaoWoLikeRes" serverName="xiaowosvr"/>
  <entry id="824" name="RpcXiaoWoStarReq" serverName="xiaowosvr"/>
  <entry id="825" name="RpcXiaoWoStarRes" serverName="xiaowosvr"/>
  <entry id="826" name="RpcXiaoWoLevelUpReq" serverName="xiaowosvr"/>
  <entry id="827" name="RpcXiaoWoLevelUpRes" serverName="xiaowosvr"/>
  <entry id="828" name="RpcXiaoWoItemInteractReq" serverName="xiaowosvr"/>
  <entry id="829" name="RpcXiaoWoItemInteractRes" serverName="xiaowosvr"/>
  <entry id="830" name="RpcXiaoWoItemLockReq" serverName="xiaowosvr"/>
  <entry id="831" name="RpcXiaoWoItemLockRes" serverName="xiaowosvr"/>
  <entry id="832" name="RpcXiaoWoAddHotReq" serverName="xiaowosvr"/>
  <entry id="833" name="RpcXiaoWoAddHotRes" serverName="xiaowosvr"/>
  <entry id="834" name="RpcXiaoWoGetHotReq" serverName="xiaowosvr"/>
  <entry id="835" name="RpcXiaoWoGetHotRes" serverName="xiaowosvr"/>
  <entry id="836" name="RpcXiaoWoGetVisitorsReq" serverName="xiaowosvr"/>
  <entry id="837" name="RpcXiaoWoGetVisitorsRes" serverName="xiaowosvr"/>
  <entry id="838" name="RpcXiaoWoHeartBeatReq" serverName="xiaowosvr"/>
  <entry id="839" name="RpcXiaoWoHeartBeatRes" serverName="xiaowosvr"/>
  <entry id="840" name="RpcGetXiaowoInfoReq" serverName="xiaowosvr"/>
  <entry id="841" name="RpcGetXiaowoInfoRes" serverName="xiaowosvr"/>
  <entry id="842" name="RpcAddPlantExpReq" serverName="xiaowosvr"/>
  <entry id="843" name="RpcAddPlantExpRes" serverName="xiaowosvr"/>
  <entry id="844" name="RpcPartyInfoToVisitorReq" serverName="xiaowosvr"/>
  <entry id="845" name="RpcPartyInfoToVisitorRes" serverName="xiaowosvr"/>
  <entry id="846" name="RpcXiaowoGMCmdReq" serverName="xiaowosvr"/>
  <entry id="847" name="RpcXiaowoGMCmdRes" serverName="xiaowosvr"/>
  <entry id="848" name="RpcXiaowoSetClientCacheReq" serverName="xiaowosvr"/>
  <entry id="849" name="RpcXiaowoSetClientCacheRes" serverName="xiaowosvr"/>
  <entry id="850" name="RpcCoCreateInviteReq" serverName="gamesvr"/>
  <entry id="851" name="RpcCoCreateInviteRes" serverName="gamesvr"/>
  <entry id="852" name="SyncUgcMapTopicsReq" serverName="ugcplatsvr"/>
  <entry id="853" name="SyncUgcMapTopicsRes" serverName="ugcplatsvr"/>
  <entry id="854" name="RpcCmdClearMetaInfoReq" serverName="ugcsvr"/>
  <entry id="855" name="RpcCmdClearMetaInfoRes" serverName="ugcsvr"/>
  <entry id="856" name="RpcCreateCoCreateUgcReq" serverName="ugcsvr"/>
  <entry id="857" name="RpcCreateCoCreateUgcRes" serverName="ugcsvr"/>
  <entry id="858" name="RpcApplyCoCreateLayerReq" serverName="ugcsvr"/>
  <entry id="859" name="RpcApplyCoCreateLayerRes" serverName="ugcsvr"/>
  <entry id="860" name="RpcAccreditLayerReq" serverName="ugcsvr"/>
  <entry id="861" name="RpcAccreditLayerRes" serverName="ugcsvr"/>
  <entry id="862" name="RpcDeleteCoCreateMapReq" serverName="ugcsvr"/>
  <entry id="863" name="RpcDeleteCoCreateMapRes" serverName="ugcsvr"/>
  <entry id="864" name="RpcGetCoCreateMapInfoReq" serverName="ugcsvr"/>
  <entry id="865" name="RpcGetCoCreateMapInfoRes" serverName="ugcsvr"/>
  <entry id="866" name="RpcJoinCoCreateMapReq" serverName="ugcsvr"/>
  <entry id="867" name="RpcJoinCoCreateMapRes" serverName="ugcsvr"/>
  <entry id="868" name="RpcRemoveCoCreatorReq" serverName="ugcsvr"/>
  <entry id="869" name="RpcRemoveCoCreatorRes" serverName="ugcsvr"/>
  <entry id="870" name="RpcUpdateUgcDataReq" serverName="ugcsvr"/>
  <entry id="871" name="RpcUpdateUgcDataRes" serverName="ugcsvr"/>
  <entry id="872" name="RpcCoCreatorModifyReq" serverName="gamesvr"/>
  <entry id="873" name="RpcCoCreatorModifyRes" serverName="gamesvr"/>
  <entry id="874" name="RpcUpdateCreatorInfoReq" serverName="ugcsvr"/>
  <entry id="875" name="RpcUpdateCreatorInfoRes" serverName="ugcsvr"/>
  <entry id="876" name="RpcUpdateUgcMapInfoReq" serverName="ugcsvr"/>
  <entry id="877" name="RpcUpdateUgcMapInfoRes" serverName="ugcsvr"/>
  <entry id="878" name="RpcUpdateUgcDataWithCausalConsistencyReq" serverName="ugcsvr"/>
  <entry id="879" name="RpcUpdateUgcDataWithCausalConsistencyRes" serverName="ugcsvr"/>
  <entry id="880" name="RpcUpdateCoCreateTeamReq" serverName="ugcsvr"/>
  <entry id="881" name="RpcUpdateCoCreateTeamRes" serverName="ugcsvr"/>
  <entry id="882" name="RpcMultiPlayerSquadCreateReq" serverName="roomsvr"/>
  <entry id="883" name="RpcMultiPlayerSquadCreateRes" serverName="roomsvr"/>
  <entry id="884" name="RpcMultiPlayerSquadRefreshReq" serverName="roomsvr"/>
  <entry id="885" name="RpcMultiPlayerSquadRefreshRes" serverName="roomsvr"/>
  <entry id="886" name="RpcMultiPlayerSquadJoinReq" serverName="roomsvr"/>
  <entry id="887" name="RpcMultiPlayerSquadJoinRes" serverName="roomsvr"/>
  <entry id="888" name="RpcMultiPlayerSquadDigTreasureReq" serverName="roomsvr"/>
  <entry id="889" name="RpcMultiPlayerSquadDigTreasureRes" serverName="roomsvr"/>
  <entry id="890" name="RpcMultiPlayerSquadExpireReq" serverName="roomsvr"/>
  <entry id="891" name="RpcMultiPlayerSquadExpireRes" serverName="roomsvr"/>
  <entry id="892" name="RpcUgcCreatorInfoChangeNtfReq" serverName="gamesvr"/>
  <entry id="893" name="RpcGetUserOperateMapListReq" serverName="ugcplatsvr"/>
  <entry id="894" name="RpcGetUserOperateMapListRes" serverName="ugcplatsvr"/>
  <entry id="895" name="RpcGetUserOperateMapReq" serverName="ugcsvr"/>
  <entry id="896" name="RpcGetUserOperateMapRes" serverName="ugcsvr"/>
  <entry id="897" name="RpcCoCreateEditorReq" serverName="ugcsvr"/>
  <entry id="898" name="RpcCoCreateEditorRes" serverName="ugcsvr"/>
  <entry id="899" name="RpcApplyLayerReq" serverName="ugcsvr"/>
  <entry id="900" name="RpcApplyLayerRes" serverName="ugcsvr"/>
  <entry id="901" name="RpcGetHomeRecommendReq" serverName="ugcplatsvr"/>
  <entry id="902" name="RpcGetHomeRecommendRes" serverName="ugcplatsvr"/>
  <entry id="903" name="RpcGetHomePageRecommendReq" serverName="ugcsvr"/>
  <entry id="904" name="RpcGetHomePageRecommendRes" serverName="ugcsvr"/>
  <entry id="905" name="RpcGetHomePageThemeRecommendReq" serverName="ugcsvr"/>
  <entry id="906" name="RpcGetHomePageThemeRecommendRes" serverName="ugcsvr"/>
  <entry id="907" name="RpcPlayerStreamSwitchReq" serverName="gamesvr"/>
  <entry id="908" name="RpcPlayerStreamSwitchRes" serverName="gamesvr"/>
  <entry id="909" name="RpcPlatRoomInfoReq" serverName="roomsvr"/>
  <entry id="910" name="RpcPlatRoomInfoRes" serverName="roomsvr"/>
  <entry id="911" name="SyncPlatRoomInfoReq" serverName="ugcplatsvr"/>
  <entry id="912" name="SyncPlatRoomInfoRes" serverName="ugcplatsvr"/>
  <entry id="913" name="RpcPlayerUseFireworksItemReq" serverName="lobbysvr"/>
  <entry id="914" name="RpcPlayerUseFireworksItemRes" serverName="lobbysvr"/>
  <entry id="915" name="RpcPlayerUseFireworksItemNtfReq" serverName="gamesvr"/>
  <entry id="916" name="RpcPlayerUseFireworksItemNtfRes" serverName="gamesvr"/>
  <entry id="917" name="RpcCoCreateInfoReq" serverName="ugcsvr"/>
  <entry id="918" name="RpcCoCreateInfoRes" serverName="ugcsvr"/>
  <entry id="919" name="RpcDeleteBriefReq" serverName="ugcsvr"/>
  <entry id="920" name="RpcDeleteBriefRes" serverName="ugcsvr"/>
  <entry id="921" name="RpcReportDataNtfReq" serverName="ugcsvr"/>
  <entry id="922" name="RpcGetHotPlayingUGCMapListReq" serverName="ugcplatsvr"/>
  <entry id="923" name="RpcGetHotPlayingUGCMapListRes" serverName="ugcplatsvr"/>
  <entry id="924" name="RpcMigrateReq" serverName="xiaowosvr"/>
  <entry id="925" name="RpcMigrateRes" serverName="xiaowosvr"/>
  <entry id="926" name="RpcXiaoWoForwardToDSReq" serverName="xiaowosvr"/>
  <entry id="927" name="RpcXiaoWoForwardToDSRes" serverName="xiaowosvr"/>
  <entry id="928" name="RpcXiaowoSetItemDetailReq" serverName="xiaowosvr"/>
  <entry id="929" name="RpcXiaowoSetItemDetailRes" serverName="xiaowosvr"/>
  <entry id="930" name="RpcXiaoWoSetInstructionAndImageReq" serverName="xiaowosvr"/>
  <entry id="931" name="RpcXiaoWoSetInstructionAndImageRes" serverName="xiaowosvr"/>
  <entry id="932" name="RpcXiaowoSyncPlayerInfoReq" serverName="xiaowosvr"/>
  <entry id="933" name="RpcXiaowoSyncPlayerInfoRes" serverName="xiaowosvr"/>
  <entry id="934" name="RpcGetPlayerXiaoWoIdReq" serverName="gamesvr"/>
  <entry id="935" name="RpcGetPlayerXiaoWoIdRes" serverName="gamesvr"/>
  <entry id="936" name="RpcSceneInviteNtfReq" serverName="gamesvr"/>
  <entry id="937" name="RpcSceneInviteNtfRes" serverName="gamesvr"/>
  <entry id="938" name="RpcSceneInviteReplyNtfReq" serverName="gamesvr"/>
  <entry id="939" name="RpcSceneInviteReplyNtfRes" serverName="gamesvr"/>
  <entry id="940" name="RpcXiaoWoEventNtfReq" serverName="gamesvr"/>
  <entry id="941" name="RpcXiaoWoOwnerInfoReq" serverName="xiaowosvr"/>
  <entry id="942" name="RpcXiaoWoOwnerInfoRes" serverName="xiaowosvr"/>
  <entry id="943" name="RpcXiaowoGetEditMetaInfoReq" serverName="xiaowosvr"/>
  <entry id="944" name="RpcXiaowoGetEditMetaInfoRes" serverName="xiaowosvr"/>
  <entry id="945" name="RpcXiaowoSaveEditMetaInfoReq" serverName="xiaowosvr"/>
  <entry id="946" name="RpcXiaowoSaveEditMetaInfoRes" serverName="xiaowosvr"/>
  <entry id="947" name="RpcXiaowoHotNtfReq" serverName="gamesvr"/>
  <entry id="948" name="RpcTeamJoinRoomConfirmNtfReq" serverName="gamesvr"/>
  <entry id="949" name="RpcReportTlogDataNtfReq" serverName="gamesvr"/>
  <entry id="950" name="RpcUgcCoCreateEditorReq" serverName="gamesvr"/>
  <entry id="951" name="RpcUgcCoCreatorDingReq" serverName="gamesvr"/>
  <entry id="952" name="RpcRoomPreJoinMemberNoticeReq" serverName="roomsvr"/>
  <entry id="953" name="RpcRoomPreJoinMemberNoticeRes" serverName="roomsvr"/>
  <entry id="954" name="RpcRoomPlayerReadyReminderReq" serverName="roomsvr"/>
  <entry id="955" name="RpcRoomPlayerReadyReminderRes" serverName="roomsvr"/>
  <entry id="956" name="RpcPlayerInfoReportNtfReq" serverName="ugcplatsvr"/>
  <entry id="957" name="RpcBattleInfoReportNtfReq" serverName="ugcplatsvr"/>
  <entry id="958" name="RpcLayerIdIsApplyReq" serverName="ugcsvr"/>
  <entry id="959" name="RpcLayerIdIsApplyRes" serverName="ugcsvr"/>
  <entry id="960" name="RpcUgcGetHotPlayingReq" serverName="ugcsvr"/>
  <entry id="961" name="RpcUgcGetHotPlayingRes" serverName="ugcsvr"/>
  <entry id="962" name="RpcSyncChatMsgFromQQReq" serverName="ugcplatsvr"/>
  <entry id="963" name="RpcSyncChatMsgFromQQRes" serverName="ugcplatsvr"/>
  <entry id="964" name="RpcUgcCoCreateEditorHeartReq" serverName="ugcsvr"/>
  <entry id="965" name="RpcUgcCoCreateEditorHeartRes" serverName="ugcsvr"/>
  <entry id="966" name="RpcUgcCoCreateDingReq" serverName="ugcsvr"/>
  <entry id="967" name="RpcUgcCoCreateDingRes" serverName="ugcsvr"/>
  <entry id="968" name="RpcCoCreateExitEditorReq" serverName="ugcsvr"/>
  <entry id="969" name="RpcCoCreateExitEditorRes" serverName="ugcsvr"/>
  <entry id="970" name="RpcRoomPreJoinNoticeConfirmReq" serverName="roomsvr"/>
  <entry id="971" name="RpcRoomPreJoinNoticeConfirmRes" serverName="roomsvr"/>
  <entry id="972" name="RpcRoomInnerBatchJoinReq" serverName="roomsvr"/>
  <entry id="973" name="RpcRoomInnerBatchJoinRes" serverName="roomsvr"/>
  <entry id="974" name="RpcPlayerInfoReportNtfRes" serverName="ugcplatsvr"/>
  <entry id="975" name="RpcBattleDsLogCtrlReq" serverName="battlesvr"/>
  <entry id="976" name="RpcBattleDsLogCtrlRes" serverName="battlesvr"/>
  <entry id="977" name="RpcLobbyDsLogCtrlReq" serverName="lobbysvr"/>
  <entry id="978" name="RpcLobbyDsLogCtrlRes" serverName="lobbysvr"/>
  <entry id="979" name="RpcCompetitionEnrollSubMoneyReq" serverName="gamesvr"/>
  <entry id="980" name="RpcCompetitionEnrollSubMoneyRes" serverName="gamesvr"/>
  <entry id="981" name="RpcGetRecommendAddFriendListReq" serverName="ugcplatsvr"/>
  <entry id="982" name="RpcGetRecommendAddFriendListRes" serverName="ugcplatsvr"/>
  <entry id="983" name="RpcGetRecommendAddFriendReasonReq" serverName="ugcplatsvr"/>
  <entry id="984" name="RpcGetRecommendAddFriendReasonRes" serverName="ugcplatsvr"/>
  <entry id="985" name="RpcGmSetCreatorInfoReq" serverName="ugcsvr"/>
  <entry id="986" name="RpcGmSetCreatorInfoRes" serverName="ugcsvr"/>
  <entry id="987" name="RpcUgcSvrTestReq" serverName="ugcsvr"/>
  <entry id="988" name="RpcUgcSvrTestRes" serverName="ugcsvr"/>
  <entry id="989" name="RpcGmUpdateMapInfoReq" serverName="ugcsvr"/>
  <entry id="990" name="RpcGmUpdateMapInfoRes" serverName="ugcsvr"/>
  <entry id="993" name="RpcMapUgcStartBattleReq" serverName="ugcsvr"/>
  <entry id="994" name="RpcMapUgcStartBattleRes" serverName="ugcsvr"/>
  <entry id="995" name="RpcPublishSecretInfoReq" serverName="ugcsvr"/>
  <entry id="996" name="RpcPublishSecretInfoRes" serverName="ugcsvr"/>
  <entry id="997" name="RpcPlatAddFriendReq" serverName="gamesvr"/>
  <entry id="998" name="RpcPlatAddFriendRes" serverName="gamesvr"/>
  <entry id="999" name="RpcTranslateReq" serverName="translatesvr"/>
  <entry id="1000" name="RpcTranslateRes" serverName="translatesvr"/>
  <entry id="1001" name="RpcQueryTranslateDataReq" serverName="translatesvr"/>
  <entry id="1002" name="RpcQueryTranslateDataRes" serverName="translatesvr"/>
  <entry id="1003" name="RpcQueryTranslateDataNtf" serverName="translatesvr"/>
  <entry id="1004" name="RpcModifTranslateDataReq" serverName="translatesvr"/>
  <entry id="1005" name="RpcModifTranslateDataRes" serverName="translatesvr"/>
  <entry id="1006" name="RpcDeleteTranslateDataReq" serverName="translatesvr"/>
  <entry id="1007" name="RpcDeleteTranslateDataRes" serverName="translatesvr"/>
  <entry id="1008" name="RpcTriggerPassiveTranslateReq" serverName="translatesvr"/>
  <entry id="1009" name="RpcTriggerPassiveTranslateRes" serverName="translatesvr"/>
  <entry id="1010" name="RpcTriggerPassiveTssReq" serverName="translatesvr"/>
  <entry id="1011" name="RpcTriggerPassiveTssRes" serverName="translatesvr"/>
  <entry id="1012" name="QueryUgcTextDataReq" serverName="noserver"/>
  <entry id="1013" name="RpcUgcMapShowLimitNtfReq" serverName="ugcplatsvr"/>
  <entry id="1014" name="RpcTopRankRedisResizeZSetReq" serverName="ranksvr"/>
  <entry id="1015" name="RpcGetLobbyInfoReq" serverName="lobbysvr"/>
  <entry id="1016" name="RpcGetLobbyInfoRes" serverName="lobbysvr"/>
  <entry id="1017" name="RpcGetRecommendBattleFriendListReq" serverName="ugcplatsvr"/>
  <entry id="1018" name="RpcGetRecommendBattleFriendListRes" serverName="ugcplatsvr"/>
  <entry id="1019" name="RpcChatSendFromQQReq" serverName="chatsvr"/>
  <entry id="1020" name="RpcChatSendFromQQRes" serverName="chatsvr"/>
  <entry id="1021" name="RpcCompetitionSendMailReq" serverName="gamesvr"/>
  <entry id="1022" name="RpcCompetitionSendMailRes" serverName="gamesvr"/>
  <entry id="1023" name="RpcCompetitionAddItemReq" serverName="gamesvr"/>
  <entry id="1024" name="RpcCompetitionAddItemRes" serverName="gamesvr"/>
  <entry id="1025" name="RpcGmUpdatePlayerInfoReq" serverName="ugcsvr"/>
  <entry id="1026" name="RpcGmUpdatePlayerInfoRes" serverName="ugcsvr"/>
  <entry id="1027" name="RpcGetExcitationTagsReq" serverName="ugcplatsvr"/>
  <entry id="1028" name="RpcGetExcitationTagsRes" serverName="ugcplatsvr"/>
  <entry id="1029" name="RpcBroadcastMsgReq" serverName="gamesvr"/>
  <entry id="1030" name="RpcDscAllocReq" serverName="dscallocsvr"/>
  <entry id="1031" name="RpcDscAllocRes" serverName="dscallocsvr"/>
  <entry id="1033" name="RpcXiaowoTreeInfoNtfReq" serverName="gamesvr"/>
  <entry id="1034" name="RpcXiaoWoVerifyNtfReq" serverName="gamesvr"/>
  <entry id="1035" name="RpcXiaoWoVerifyNtfRes" serverName="gamesvr"/>
  <entry id="1036" name="RpcXiaoWoVerifyResultNtfReq" serverName="xiaowosvr"/>
  <entry id="1037" name="RpcXiaoWoVerifyResultNtfRes" serverName="xiaowosvr"/>
  <entry id="1038" name="RpcCompetitionCommonNtfReq" serverName="gamesvr"/>
  <entry id="1039" name="RpcCompetitionPromoteEventReq" serverName="gamesvr"/>
  <entry id="1040" name="RpcGetPublishPlayInfoReq" serverName="ugcsvr"/>
  <entry id="1041" name="RpcGetPublishPlayInfoRes" serverName="ugcsvr"/>
  <entry id="1042" name="RpcRoomGMCommandReq" serverName="roomsvr"/>
  <entry id="1043" name="RpcRoomGMCommandRes" serverName="roomsvr"/>
  <entry id="1044" name="RpcDoMapBanOptReq" serverName="ugcsvr"/>
  <entry id="1045" name="RpcDoMapBanOptRes" serverName="ugcsvr"/>
  <entry id="1046" name="RpcExitPlayerLobbyReq" serverName="gamesvr"/>
  <entry id="1047" name="RpcExitPlayerLobbyRes" serverName="gamesvr"/>
  <entry id="1048" name="RpcExitPlayerLastSceneReq" serverName="gamesvr"/>
  <entry id="1049" name="RpcExitPlayerLastSceneRes" serverName="gamesvr"/>
  <entry id="1050" name="RpcIdipOperateMapReq" serverName="ugcsvr"/>
  <entry id="1051" name="RpcIdipOperateMapRes" serverName="ugcsvr"/>
  <entry id="1052" name="RpcPreJoinWithTeamReq" serverName="roomsvr"/>
  <entry id="1053" name="RpcPreJoinWithTeamRes" serverName="roomsvr"/>
  <entry id="1054" name="RpcJoinBattleReq" serverName="battlesvr"/>
  <entry id="1055" name="RpcJoinBattleRes" serverName="battlesvr"/>
  <entry id="1056" name="RpcRoomMidJoinFriendBattleSvrReq" serverName="gamesvr"/>
  <entry id="1057" name="RpcRoomMidJoinFriendBattleSvrRes" serverName="gamesvr"/>
  <entry id="1058" name="RpcBattleSvrCheckAliveReq" serverName="battlesvr"/>
  <entry id="1059" name="RpcBattleSvrCheckAliveRes" serverName="battlesvr"/>
  <entry id="1060" name="RpcBattleSvrHeartbeatReq" serverName="tycoonsvr"/>
  <entry id="1061" name="RpcBattleSvrHeartbeatRes" serverName="tycoonsvr"/>
  <entry id="1062" name="RpcSampleRoomAllocReq" serverName="sampleroomsvr"/>
  <entry id="1063" name="RpcSampleRoomAllocRes" serverName="sampleroomsvr"/>
  <entry id="1064" name="RpcSampleRoomReportReq" serverName="sampleroomsvr"/>
  <entry id="1065" name="RpcXiaowoCreateSampleRoomReq" serverName="xiaowosvr"/>
  <entry id="1066" name="RpcXiaowoCreateSampleRoomRes" serverName="xiaowosvr"/>
  <entry id="1069" name="RpcXiaoWoGetLayoutListReq" serverName="xiaowosvr"/>
  <entry id="1070" name="RpcXiaoWoGetLayoutListRes" serverName="xiaowosvr"/>
  <entry id="1071" name="RpcLobbyInfoNtfReq" serverName="gamesvr"/>
  <entry id="1072" name="RpcLobbyInfoNtfRes" serverName="gamesvr"/>
  <entry id="1073" name="RpcUgcPlatSvrGmReq" serverName="ugcsvr"/>
  <entry id="1074" name="TestAigcSvrReq" serverName="aigcsvr"/>
  <entry id="1075" name="TestAigcSvrRes" serverName="aigcsvr"/>
  <entry id="1076" name="RpcAigcGenVoiceNtfReq" serverName="aigcsvr"/>
  <entry id="1077" name="RpcAigcGenVoiceResNtfReq" serverName="gamesvr"/>
  <entry id="1078" name="RpcAigcGenImageNtfReq" serverName="aigcsvr"/>
  <entry id="1079" name="RpcAigcGenImageResNtfReq" serverName="gamesvr"/>
  <entry id="1080" name="RpcAigcChangeColorNtfReq" serverName="aigcsvr"/>
  <entry id="1081" name="RpcAigcChangeColorResNtfReq" serverName="gamesvr"/>
  <entry id="1082" name="RpcAigcGenAnicapNtfReq" serverName="aigcsvr"/>
  <entry id="1083" name="RpcAigcGenAnicapResNtfReq" serverName="gamesvr"/>
  <entry id="1084" name="RpcAigcGenAnswerNtfReq" serverName="aigcsvr"/>
  <entry id="1085" name="RpcAigcGenAnswerResNtfReq" serverName="gamesvr"/>
  <entry id="1086" name="RpcTopRankFetchReq" serverName="ranksvr"/>
  <entry id="1087" name="RpcTopRankFetchRes" serverName="ranksvr"/>
  <entry id="1088" name="RpcXiaoWoMoneyTreeDropReturnReq" serverName="gamesvr"/>
  <entry id="1089" name="RpcConfirmAssistFriendReq" serverName="gamesvr"/>
  <entry id="1090" name="RpcConfirmAssistFriendRes" serverName="gamesvr"/>
  <entry id="1091" name="RpcGetCollectStarActivityInfoReq" serverName="ugcplatsvr"/>
  <entry id="1092" name="RpcGetCollectStarActivityInfoRes" serverName="ugcplatsvr"/>
  <entry id="1093" name="RpcPlatUgcMapSearchFrontReq" serverName="ugcplatsvr"/>
  <entry id="1094" name="RpcPlatUgcMapSearchFrontRes" serverName="ugcplatsvr"/>
  <entry id="1095" name="RpcSearchInstanceReq" serverName="ugcplatsvr"/>
  <entry id="1096" name="RpcSearchInstanceRes" serverName="ugcplatsvr"/>
  <entry id="1097" name="RpcGetCollectStarActivityMapReq" serverName="ugcsvr"/>
  <entry id="1098" name="RpcGetCollectStarActivityMapRes" serverName="ugcsvr"/>
  <entry id="1099" name="RpcUgcMapSearchFrontRes" serverName="ugcsvr"/>
  <entry id="1100" name="RpcUgcSearchInstanceReq" serverName="ugcsvr"/>
  <entry id="1101" name="RpcUgcSearchInstanceRes" serverName="ugcsvr"/>
  <entry id="1102" name="RpcUgcMapSearchFrontReq" serverName="ugcsvr"/>
  <entry id="1103" name="RpcUgcTopicDetailReq" serverName="ugcsvr"/>
  <entry id="1104" name="RpcUgcTopicDetailRes" serverName="ugcsvr"/>
  <entry id="1105" name="RpcPlatUgcSearchTopicReq" serverName="ugcplatsvr"/>
  <entry id="1106" name="RpcPlatUgcSearchTopicRes" serverName="ugcplatsvr"/>
  <entry id="1107" name="RpcMapPublishCheckReq" serverName="ugcsvr"/>
  <entry id="1108" name="RpcMapPublishCheckRes" serverName="ugcsvr"/>
  <entry id="1109" name="RpcClubGetMemberDetailReq" serverName="clubsvr"/>
  <entry id="1110" name="RpcClubGetMemberDetailRes" serverName="clubsvr"/>
  <entry id="1111" name="RpcClubUpdateMemberDetailNtfReq" serverName="clubsvr"/>
  <entry id="1112" name="RpcPlatUgcGetSceneGroupInfoReq" serverName="ugcplatsvr"/>
  <entry id="1113" name="RpcPlatUgcGetSceneGroupInfoRes" serverName="ugcplatsvr"/>
  <entry id="1114" name="RpcRegionGroupChatReq" serverName="chatsvr"/>
  <entry id="1115" name="RpcRegionGroupChatRes" serverName="chatsvr"/>
  <entry id="1116" name="RpcClubNewApplyNtfReq" serverName="gamesvr"/>
  <entry id="1117" name="RpcClubShareMapReq" serverName="clubsvr"/>
  <entry id="1118" name="RpcClubShareMapRes" serverName="clubsvr"/>
  <entry id="1119" name="RpcGetUgcRemoteConfigReq" serverName="ugcplatsvr"/>
  <entry id="1120" name="RpcGetUgcRemoteConfigRes" serverName="ugcplatsvr"/>
  <entry id="1121" name="RpcXiaoWoCropWaterReq" serverName="xiaowosvr"/>
  <entry id="1122" name="RpcXiaoWoCropWaterRes" serverName="xiaowosvr"/>
  <entry id="1123" name="RpcXiaoWoCropHarvestReq" serverName="xiaowosvr"/>
  <entry id="1124" name="RpcXiaoWoCropHarvestRes" serverName="xiaowosvr"/>
  <entry id="1125" name="RpcDsUserDBInfoSaveReq" serverName="dsdbsvr"/>
  <entry id="1126" name="RpcDsUserDBInfoSaveRes" serverName="dsdbsvr"/>
  <entry id="1127" name="RpcDsUserDBInfoGetReq" serverName="dsdbsvr"/>
  <entry id="1128" name="RpcDsUserDBInfoGetRes" serverName="dsdbsvr"/>
  <entry id="1129" name="RpcSuccessInviteFriendLoginReq" serverName="gamesvr"/>
  <entry id="1130" name="RpcSuccessInviteFriendLoginRes" serverName="gamesvr"/>
  <entry id="1131" name="RpcUgcMatchLobbyDetailReq" serverName="ugcsvr"/>
  <entry id="1132" name="RpcUgcMatchLobbyDetailRes" serverName="ugcsvr"/>
  <entry id="1133" name="RpcUgcPlatMatchRecommendListReq" serverName="ugcsvr"/>
  <entry id="1134" name="RpcUgcPlatMatchRecommendListRes" serverName="ugcsvr"/>
  <entry id="1135" name="RpcUgcMatchUgcIdCheckReq" serverName="ugcsvr"/>
  <entry id="1136" name="RpcUgcMatchUgcIdCheckRes" serverName="ugcsvr"/>
  <entry id="1137" name="RpcUgcRoomRecommendListReq" serverName="ugcsvr"/>
  <entry id="1138" name="RpcUgcRoomRecommendListRes" serverName="ugcsvr"/>
  <entry id="1139" name="RpcAigcAnicapCancelQueueNtfReq" serverName="aigcsvr"/>
  <entry id="1140" name="RpcTestAddMsgReq" serverName="ugcsvr"/>
  <entry id="1141" name="RpcTestAddMsgRes" serverName="ugcsvr"/>
  <entry id="1142" name="RpcClubGetShareMapReq" serverName="clubsvr"/>
  <entry id="1143" name="RpcClubGetShareMapRes" serverName="clubsvr"/>
  <entry id="1144" name="RpcRegionClubPiiReq" serverName="gamesvr"/>
  <entry id="1145" name="RpcRegionClubPiiRes" serverName="gamesvr"/>
  <entry id="1146" name="RpcClubNameOperateReq" serverName="ugcplatsvr"/>
  <entry id="1147" name="RpcClubNameOperateRes" serverName="ugcplatsvr"/>
  <entry id="1148" name="RpcUgcBattleSettlementMapRecommendReq" serverName="ugcplatsvr"/>
  <entry id="1149" name="RpcUgcBattleSettlementMapRecommendRes" serverName="ugcplatsvr"/>
  <entry id="1150" name="RpcCrossDBReq" serverName="gamesvr"/>
  <entry id="1151" name="RpcCrossDBRes" serverName="gamesvr"/>
  <entry id="1152" name="RpcBatchGetPlayerPublicInfoReq" serverName="gamesvr"/>
  <entry id="1153" name="RpcBatchGetPlayerPublicInfoRes" serverName="gamesvr"/>
  <entry id="1154" name="RpcPlatResHomePageRecommendReq" serverName="ugcplatsvr"/>
  <entry id="1155" name="RpcPlatResHomePageRecommendRes" serverName="ugcplatsvr"/>
  <entry id="1156" name="RpcResHomePageRecommendReq" serverName="ugcsvr"/>
  <entry id="1157" name="RpcResHomePageRecommendRes" serverName="ugcsvr"/>
  <entry id="1158" name="RpcResHomePageRecommedMoreSetReq" serverName="ugcsvr"/>
  <entry id="1159" name="RpcResHomePageRecommedMoreSetRes" serverName="ugcsvr"/>
  <entry id="1160" name="RpcUgcResBagAddReq" serverName="ugcsvr"/>
  <entry id="1161" name="RpcUgcResBagAddRes" serverName="ugcsvr"/>
  <entry id="1162" name="RpcUgcResBagDeleteReq" serverName="ugcsvr"/>
  <entry id="1163" name="RpcUgcResBagDeleteRes" serverName="ugcsvr"/>
  <entry id="1164" name="RpcUgcResBagGetReq" serverName="ugcsvr"/>
  <entry id="1165" name="RpcUgcResBagGetRes" serverName="ugcsvr"/>
  <entry id="1166" name="RpcUgcResBagSearchReq" serverName="ugcsvr"/>
  <entry id="1167" name="RpcUgcResBagSearchRes" serverName="ugcsvr"/>
  <entry id="1168" name="RpcTestAddMsgV2Req" serverName="ugcsvr"/>
  <entry id="1169" name="RpcTestAddMsgV2Res" serverName="ugcsvr"/>
  <entry id="1170" name="RpcDirectorJoinBattleReq" serverName="battlesvr"/>
  <entry id="1171" name="RpcDirectorJoinBattleRes" serverName="battlesvr"/>
  <entry id="1172" name="RpcReputationScoreNotEnoughNtfReq" serverName="gamesvr"/>
  <entry id="1173" name="RpcReputationScoreNotEnoughNtfRes" serverName="gamesvr"/>
  <entry id="1174" name="RpcUgcResBagGetOrSearchReq" serverName="ugcplatsvr"/>
  <entry id="1175" name="RpcUgcResBagGetOrSearchRes" serverName="ugcplatsvr"/>
  <entry id="1176" name="RpcPlatResCommunitySearchReq" serverName="ugcplatsvr"/>
  <entry id="1177" name="RpcPlatResCommunitySearchRes" serverName="ugcplatsvr"/>
  <entry id="1178" name="RpcUgcResCommunitySearchReq" serverName="ugcsvr"/>
  <entry id="1179" name="RpcUgcResCommunitySearchRes" serverName="ugcsvr"/>
  <entry id="1180" name="RpcVideoExamineNtfReq" serverName="aigcsvr"/>
  <entry id="1181" name="RpcVideoExamineResponseNtfReq" serverName="gamesvr"/>
  <entry id="1182" name="RpcMigrateDsReq" serverName="battlesvr"/>
  <entry id="1183" name="RpcMigrateDsRes" serverName="battlesvr"/>
  <entry id="1184" name="RpcClubForwardReq" serverName="clubsvr"/>
  <entry id="1185" name="RpcClubForwardRes" serverName="clubsvr"/>
  <entry id="1186" name="RpcCreateRedPacketReq" serverName="lobbysvr"/>
  <entry id="1187" name="RpcCreateRedPacketRes" serverName="lobbysvr"/>
  <entry id="1188" name="RpcOpenRedPacketReq" serverName="lobbysvr"/>
  <entry id="1189" name="RpcOpenRedPacketRes" serverName="lobbysvr"/>
  <entry id="1190" name="RpcLobbyCreateRedPacketReq" serverName="lobbysvr"/>
  <entry id="1191" name="RpcLobbyCreateRedPacketRes" serverName="lobbysvr"/>
  <entry id="1192" name="RpcLobbyOpenRedPacketReq" serverName="lobbysvr"/>
  <entry id="1193" name="RpcLobbyOpenRedPacketRes" serverName="lobbysvr"/>
  <entry id="1194" name="RpcClubHeatSetReq" serverName="clubsvr"/>
  <entry id="1195" name="RpcClubHeatSetRes" serverName="clubsvr"/>
  <entry id="1196" name="RpcClubPlaceHolderReq" serverName="clubsvr"/>
  <entry id="1197" name="RpcClubPlaceHolderRes" serverName="clubsvr"/>
  <entry id="1198" name="RpcXiaoWoCreateRedPacketReq" serverName="xiaowosvr"/>
  <entry id="1199" name="RpcXiaoWoCreateRedPacketRes" serverName="xiaowosvr"/>
  <entry id="1200" name="RpcXiaoWoOpenRedPacketReq" serverName="xiaowosvr"/>
  <entry id="1201" name="RpcXiaoWoOpenRedPacketRes" serverName="xiaowosvr"/>
  <entry id="1202" name="RpcPlatSearchSuggestionReq" serverName="ugcplatsvr"/>
  <entry id="1203" name="RpcPlatSearchSuggestionRes" serverName="ugcplatsvr"/>
  <entry id="1204" name="RpcUGCSearchSuggestionReq" serverName="ugcsvr"/>
  <entry id="1205" name="RpcUGCSearchSuggestionRes" serverName="ugcsvr"/>
  <entry id="1206" name="RpcRoomHeartBeatReq" serverName="roomsvr"/>
  <entry id="1207" name="RpcRoomHeartBeatRes" serverName="roomsvr"/>
  <entry id="1208" name="RpcUgcGetRecommendSubsReq" serverName="ugcsvr"/>
  <entry id="1209" name="RpcUgcGetRecommendSubsRes" serverName="ugcsvr"/>
  <entry id="1210" name="RpcRoomMapStateSyncReq" serverName="roomsvr"/>
  <entry id="1211" name="RpcRoomMapStateSyncRes" serverName="roomsvr"/>
  <entry id="1212" name="RpcGetSaveRecordReq" serverName="ugcsvr"/>
  <entry id="1213" name="RpcGetSaveRecordRes" serverName="ugcsvr"/>
  <entry id="1214" name="RpcWarmDataOperateReq" serverName="ugcsvr"/>
  <entry id="1215" name="RpcWarmDataOperateRes" serverName="ugcsvr"/>
  <entry id="1216" name="RpcDirectorJoinRoomReq" serverName="roomsvr"/>
  <entry id="1217" name="RpcDirectorJoinRoomRes" serverName="roomsvr"/>
  <entry id="1218" name="RpcDirectorExitRoomReq" serverName="roomsvr"/>
  <entry id="1219" name="RpcDirectorExitRoomRes" serverName="roomsvr"/>
  <entry id="1220" name="RpcMigrateOneDsReq" serverName="battlesvr"/>
  <entry id="1221" name="RpcForceTriggerMigrateReq" serverName="tycoonsvr"/>
  <entry id="1222" name="RpcForceTriggerMigrateRes" serverName="tycoonsvr"/>
  <entry id="1223" name="RpcMigrateOneDsRes" serverName="battlesvr"/>
  <entry id="1224" name="RpcTycCmdReq" serverName="battlesvr"/>
  <entry id="1225" name="RpcTycCmdRes" serverName="battlesvr"/>
  <entry id="1226" name="RpcLBSReportReq" serverName="lbssvr"/>
  <entry id="1227" name="RpcLBSReportRes" serverName="lbssvr"/>
  <entry id="1228" name="RpcLBSRemoveReq" serverName="lbssvr"/>
  <entry id="1229" name="RpcLBSRemoveRes" serverName="lbssvr"/>
  <entry id="1230" name="RpcLBSSearchReq" serverName="lbssvr"/>
  <entry id="1231" name="RpcLBSSearchRes" serverName="lbssvr"/>
  <entry id="1232" name="RpcLBSGetUsersReq" serverName="lbssvr"/>
  <entry id="1233" name="RpcLBSGetUsersRes" serverName="lbssvr"/>
  <entry id="1234" name="RpcLBSClearReq" serverName="lbssvr"/>
  <entry id="1235" name="RpcLBSClearRes" serverName="lbssvr"/>
  <entry id="1236" name="RpcUgcMapGroupListReq" serverName="ugcsvr"/>
  <entry id="1237" name="RpcUgcMapGroupListRes" serverName="ugcsvr"/>
  <entry id="1238" name="RpcPlayerNoticeMsgNtfReq" serverName="gamesvr"/>
  <entry id="1239" name="RpcRedPacketDestroyNtfReq" serverName="gamesvr"/>
  <entry id="1240" name="RpcBattleDelDsReq" serverName="battlesvr"/>
  <entry id="1241" name="RpcBattleDelDsRes" serverName="battlesvr"/>
  <entry id="1242" name="RpcClubSubscribeEventReq" serverName="clubsvr"/>
  <entry id="1243" name="RpcClubSubscribeEventRes" serverName="clubsvr"/>
  <entry id="1244" name="RpcUgcMulTestSaveMetaReq" serverName="ugcsvr"/>
  <entry id="1245" name="RpcUgcMulTestSaveMetaRes" serverName="ugcsvr"/>
  <entry id="1246" name="RpcUgcTestAddTestPlayerListReq" serverName="gamesvr"/>
  <entry id="1247" name="RpcUgcTestAddTestPlayerListRes" serverName="gamesvr"/>
  <entry id="1248" name="RpcXiaoWoLayoutPublishRecordSaveReq" serverName="xiaowosvr"/>
  <entry id="1249" name="RpcXiaoWoLayoutPublishRecordSaveRes" serverName="xiaowosvr"/>
  <entry id="1250" name="RpcGiveSpringBlessingCardReq" serverName="gamesvr"/>
  <entry id="1251" name="RpcClubStatisticReq" serverName="clubsvr"/>
  <entry id="1252" name="RpcClubStatisticRes" serverName="clubsvr"/>
  <entry id="1253" name="RpcIntellectualActivityReq" serverName="gamesvr"/>
  <entry id="1254" name="RpcUgcMapHandlerReq" serverName="ugcsvr"/>
  <entry id="1255" name="RpcUgcMapHandlerRes" serverName="ugcsvr"/>
  <entry id="1256" name="RpcQuitBattleNtfReq" serverName="gamesvr"/>
  <entry id="1257" name="RpcQuitBattleNtfRes" serverName="gamesvr"/>
  <entry id="1258" name="RpcIntellectualActivityCommonReq" serverName="gamesvr"/>
  <entry id="1259" name="RpcClubCancelUgcCollectReq" serverName="clubsvr"/>
  <entry id="1260" name="RpcClubCancelUgcCollectRes" serverName="clubsvr"/>
  <entry id="1261" name="RpcMatchingResultReq" serverName="roomsvr"/>
  <entry id="1262" name="RpcMatchingResultRes" serverName="roomsvr"/>
  <entry id="1263" name="OperateCmdOnDsReq" serverName="battlesvr"/>
  <entry id="1264" name="OperateCmdOnDsRes" serverName="battlesvr"/>
  <entry id="1265" name="RpcGSItemChangeReq" serverName="battlesvr"/>
  <entry id="1266" name="RpcGSItemChangeRes" serverName="battlesvr"/>
  <entry id="1267" name="RpcAigcTtsCallbackNtfReq" serverName="aigcsvr"/>
  <entry id="1268" name="RpcXiaoWoReceiveNewLiuYanMessageNtfReq" serverName="gamesvr"/>
  <entry id="1269" name="RpcXiaoWoReceiveNewLiuYanMessageNtfRes" serverName="gamesvr"/>
  <entry id="1270" name="RpcXiaowoSetWelcomeInfoReq" serverName="xiaowosvr"/>
  <entry id="1271" name="RpcXiaowoSetWelcomeInfoRes" serverName="xiaowosvr"/>
  <entry id="1272" name="RpcXiaowoSendLiuYanMessageReq" serverName="xiaowosvr"/>
  <entry id="1273" name="RpcXiaowoSendLiuYanMessageRes" serverName="xiaowosvr"/>
  <entry id="1274" name="RpcXiaowoChoiceLiuYanMessageReq" serverName="xiaowosvr"/>
  <entry id="1275" name="RpcXiaowoChoiceLiuYanMessageRes" serverName="xiaowosvr"/>
  <entry id="1276" name="RpcXiaoWoDeleteLiuYanMessageReq" serverName="xiaowosvr"/>
  <entry id="1277" name="RpcXiaoWoDeleteLiuYanMessageRes" serverName="xiaowosvr"/>
  <entry id="1278" name="RpcXiaoWoGetLiuYanMessageReq" serverName="xiaowosvr"/>
  <entry id="1279" name="RpcXiaoWoGetLiuYanMessageRes" serverName="xiaowosvr"/>
  <entry id="1280" name="RpcChatRemindNoticeNtfReq" serverName="gamesvr"/>
  <entry id="1281" name="RpcUgcInstanceVersionReq" serverName="ugcsvr"/>
  <entry id="1282" name="RpcUgcInstanceVersionRes" serverName="ugcsvr"/>
  <entry id="1283" name="RpcUgcCollectionUpdateReq" serverName="ugcsvr"/>
  <entry id="1284" name="RpcUgcCollectionUpdateRes" serverName="ugcsvr"/>
  <entry id="1285" name="RpcRoomReservationNtfReq" serverName="gamesvr"/>
  <entry id="1286" name="RpcRoomReservationNtfRes" serverName="gamesvr"/>
  <entry id="1287" name="RpcRoomReservationResponseNtfReq" serverName="gamesvr"/>
  <entry id="1288" name="RpcRoomReservationResponseNtfRes" serverName="gamesvr"/>
  <entry id="1289" name="RpcUgcMatchLobbyDetailExReq" serverName="ugcsvr"/>
  <entry id="1290" name="RpcUgcMatchLobbyDetailExRes" serverName="ugcsvr"/>
  <entry id="1291" name="RpcClubMSDKReportNtfReq" serverName="clubsvr"/>
  <entry id="1292" name="RpcUgcModifyPublishConfigReq" serverName="ugcsvr"/>
  <entry id="1293" name="RpcUgcModifyPublishConfigRes" serverName="ugcsvr"/>
  <entry id="1294" name="RpcUgcCollectionOpReq" serverName="ugcsvr"/>
  <entry id="1295" name="RpcUgcCollectionOpRes" serverName="ugcsvr"/>
  <entry id="1296" name="RpcMapActivePublishGoodsReq" serverName="ugcplatsvr"/>
  <entry id="1297" name="RpcMapActivePublishGoodsRes" serverName="ugcplatsvr"/>
  <entry id="1298" name="RpcCheckGoodsIdReq" serverName="ugcsvr"/>
  <entry id="1299" name="RpcCheckGoodsIdRes" serverName="ugcsvr"/>
  <entry id="1300" name="RpcActivePublishGoodsReq" serverName="ugcsvr"/>
  <entry id="1301" name="RpcActivePublishGoodsRes" serverName="ugcsvr"/>
  <entry id="1302" name="RpcClubBatchGetBasicInfoReq" serverName="clubsvr"/>
  <entry id="1303" name="RpcClubBatchGetBasicInfoRes" serverName="clubsvr"/>
  <entry id="1304" name="RpcClubNeedSyncNtfReq" serverName="clubsvr"/>
  <entry id="1305" name="RpcClubSyncNtfReq" serverName="gamesvr"/>
  <entry id="1306" name="RpcUgcDataStorePaidItemAddReq" serverName="ugcdatastoresvr"/>
  <entry id="1307" name="RpcUgcDataStorePaidItemAddRes" serverName="ugcdatastoresvr"/>
  <entry id="1308" name="RpcGetBattleDSInstIdReq" serverName="battlesvr"/>
  <entry id="1309" name="RpcGetBattleDSInstIdRes" serverName="battlesvr"/>
  <entry id="1310" name="RpcUgcDataStoreGetPlayerDataReq" serverName="ugcdatastoresvr"/>
  <entry id="1311" name="RpcUgcDataStoreGetPlayerDataRes" serverName="ugcdatastoresvr"/>
  <entry id="1312" name="RpcUgcGetGoodsListReq" serverName="ugcsvr"/>
  <entry id="1313" name="RpcUgcGetGoodsListRes" serverName="ugcsvr"/>
  <entry id="1314" name="RpcCheckWolfKillReportReq" serverName="battlesvr"/>
  <entry id="1315" name="RpcCheckWolfKillReportRes" serverName="battlesvr"/>
  <entry id="1316" name="RpcDsUpdateBackpackReq" serverName="dsdbsvr"/>
  <entry id="1317" name="RpcDsUpdateBackpackRes" serverName="dsdbsvr"/>
  <entry id="1318" name="RpcWolfKillBeReportReq" serverName="gamesvr"/>
  <entry id="1319" name="RpcWolfKillBeReportRes" serverName="gamesvr"/>
  <entry id="1320" name="RpcUgcCoPlayRecordNtfReq" serverName="gamesvr"/>
  <entry id="1321" name="RpcForwardBattleSettlementNtfReq" serverName="gamesvr"/>
  <entry id="1322" name="RpcRoomMapVoteReq" serverName="roomsvr"/>
  <entry id="1323" name="RpcRoomMapVoteRes" serverName="roomsvr"/>
  <entry id="1324" name="RpcRoomPlayerEndSettlementReq" serverName="roomsvr"/>
  <entry id="1325" name="RpcRoomPlayerEndSettlementRes" serverName="roomsvr"/>
  <entry id="1326" name="RpcRoomPlayerSetInSettlementReq" serverName="roomsvr"/>
  <entry id="1327" name="RpcClubGetRecommendListReq" serverName="ugcplatsvr"/>
  <entry id="1328" name="RpcClubGetRecommendListRes" serverName="ugcplatsvr"/>
  <entry id="1329" name="RpcClubSearchByTagReq" serverName="ugcplatsvr"/>
  <entry id="1330" name="RpcClubSearchByTagRes" serverName="ugcplatsvr"/>
  <entry id="1331" name="RpcGetUgcCoPlayRecommendMapListReq" serverName="ugcplatsvr"/>
  <entry id="1332" name="RpcGetUgcCoPlayRecommendMapListRes" serverName="ugcplatsvr"/>
  <entry id="1333" name="RpcUgcCollectionReportReq" serverName="ugcplatsvr"/>
  <entry id="1334" name="RpcUgcCollectionReportRes" serverName="ugcplatsvr"/>
  <entry id="1335" name="RpcWolfKillReportReq" serverName="ugcsvr"/>
  <entry id="1336" name="RpcWolfKillReportRes" serverName="ugcsvr"/>
  <entry id="1337" name="RpcUgcCollectionGetBriefsReq" serverName="ugcsvr"/>
  <entry id="1338" name="RpcUgcCollectionGetBriefsRes" serverName="ugcsvr"/>
  <entry id="1339" name="UgcResNeedDownReq" serverName="ugcsvr"/>
  <entry id="1340" name="UgcResNeedDownRes" serverName="ugcsvr"/>
  <entry id="1341" name="RpcUgcBuyGoodsAccountFlowReportReq" serverName="ugcplatsvr"/>
  <entry id="1342" name="RpcUgcBuyGoodsAccountFlowReportRes" serverName="ugcplatsvr"/>
  <entry id="1343" name="RpcGetPlayerFriendListForIdipReq" serverName="gamesvr"/>
  <entry id="1344" name="RpcGetPlayerFriendListForIdipRes" serverName="gamesvr"/>
  <entry id="1345" name="RpcCheckGoodsValidReq" serverName="ugcsvr"/>
  <entry id="1346" name="RpcCheckGoodsValidRes" serverName="ugcsvr"/>
  <entry id="1347" name="RpcRoomAllMemberReadyForMatchNtfReq" serverName="gamesvr"/>
  <entry id="1348" name="RpcRoomNeedNtfWhenAllMemberReadyForMatchReq" serverName="roomsvr"/>
  <entry id="1349" name="RpcRoomNeedNtfWhenAllMemberReadyForMatchRes" serverName="roomsvr"/>
  <entry id="1350" name="RpcRoomOperationNtfReq" serverName="gamesvr"/>
  <entry id="1388" name="RpcLobbyGetOnlinePlayersReq" serverName="lobbysvr"/>
  <entry id="1389" name="RpcLobbyGetOnlinePlayersRes" serverName="lobbysvr"/>
  <entry id="1390" name="RpcRoomSettlementDataReq" serverName="roomsvr"/>
  <entry id="1391" name="RpcUgcCollectionReportNtfReq" serverName="ugcplatsvr"/>
  <entry id="1392" name="RpcUgcCollectionReportNtfRes" serverName="ugcplatsvr"/>
  <entry id="1393" name="RpcUgcCollectionSearchReq" serverName="ugcplatsvr"/>
  <entry id="1394" name="RpcUgcCollectionSearchRes" serverName="ugcplatsvr"/>
  <entry id="1395" name="RpcUgcCollectionGovRecommendReq" serverName="ugcplatsvr"/>
  <entry id="1396" name="RpcUgcCollectionGovRecommendRes" serverName="ugcplatsvr"/>
  <entry id="1397" name="RpcUgcCollectionRecommendReq" serverName="ugcplatsvr"/>
  <entry id="1398" name="RpcUgcCollectionRecommendRes" serverName="ugcplatsvr"/>
  <entry id="1399" name="RpcUgcCollectionGovRecommendListReq" serverName="ugcsvr"/>
  <entry id="1400" name="RpcUgcCollectionGovRecommendListRes" serverName="ugcsvr"/>
  <entry id="1401" name="RpcUgcCollectionRecommendListReq" serverName="ugcsvr"/>
  <entry id="1402" name="RpcUgcCollectionRecommendListRes" serverName="ugcsvr"/>
  <entry id="1403" name="RpcUgcCollectionSearchListReq" serverName="ugcsvr"/>
  <entry id="1404" name="RpcUgcCollectionSearchListRes" serverName="ugcsvr"/>
  <entry id="1406" name="RpcGetPlayerHasItemReq" serverName="gamesvr"/>
  <entry id="1407" name="RpcGetPlayerHasItemRes" serverName="gamesvr"/>
  <entry id="1411" name="RpcSetBattlePlayerClientInfoReq" serverName="battlesvr"/>
  <entry id="1412" name="RpcSetBattlePlayerClientInfoRes" serverName="battlesvr"/>
  <entry id="1413" name="RpcBattlePlayerClientInfoModifyNtfReq" serverName="gamesvr"/>
  <entry id="1414" name="RpcSendRoomMemberToMemberNtfToPlayerReq" serverName="gamesvr"/>
  <entry id="1415" name="RpcSendRoomMemberToMemberNtfReq" serverName="roomsvr"/>
  <entry id="1416" name="RpcSendRoomMemberToMemberNtfRes" serverName="roomsvr"/>
  <entry id="1417" name="RpcGetRecommendIntimacyListReq" serverName="ugcplatsvr"/>
  <entry id="1418" name="RpcGetRecommendIntimacyListRes" serverName="ugcplatsvr"/>
  <entry id="1419" name="RpcRoomMidJoinBattleReq" serverName="battlesvr"/>
  <entry id="1420" name="RpcRoomMidJoinBattleRes" serverName="battlesvr"/>
  <entry id="1421" name="RpcMatchFillBackResultReq" serverName="battlesvr"/>
  <entry id="1422" name="RpcMatchFillBackResultRes" serverName="battlesvr"/>
  <entry id="1423" name="RpcTeamsJoinBattleReq" serverName="battlesvr"/>
  <entry id="1424" name="RpcTeamsJoinBattleRes" serverName="battlesvr"/>
  <entry id="1425" name="RpcDanMuSendReq" serverName="danmusvr"/>
  <entry id="1426" name="RpcDanMuSendRes" serverName="danmusvr"/>
  <entry id="1427" name="RpcDanMuDeleteReq" serverName="danmusvr"/>
  <entry id="1428" name="RpcDanMuDeleteRes" serverName="danmusvr"/>
  <entry id="1429" name="RpcDanMuDetailReq" serverName="danmusvr"/>
  <entry id="1430" name="RpcDanMuDetailRes" serverName="danmusvr"/>
  <entry id="1431" name="RpcDanMuLikeReq" serverName="danmusvr"/>
  <entry id="1432" name="RpcDanMuLikeRes" serverName="danmusvr"/>
  <entry id="1433" name="RpcDanMuTipOffReq" serverName="danmusvr"/>
  <entry id="1434" name="RpcDanMuTipOffRes" serverName="danmusvr"/>
  <entry id="1435" name="RpcDanMuClearReq" serverName="danmusvr"/>
  <entry id="1436" name="RpcDanMuClearRes" serverName="danmusvr"/>
  <entry id="1437" name="RpcDanMuDetailFlushReq" serverName="danmusvr"/>
  <entry id="1438" name="RpcDanMuDetailFlushRes" serverName="danmusvr"/>
  <entry id="1439" name="RpcDanMuOptRegionReq" serverName="danmusvr"/>
  <entry id="1440" name="RpcDanMuOptRegionRes" serverName="danmusvr"/>
  <entry id="1441" name="RpcFarmMigrateReq" serverName="farmsvr"/>
  <entry id="1442" name="RpcFarmMigrateRes" serverName="farmsvr"/>
  <entry id="1443" name="RpcFarmCsForwardReq" serverName="farmsvr"/>
  <entry id="1444" name="RpcFarmCsForwardRes" serverName="farmsvr"/>
  <entry id="1445" name="RpcFarmHeartBeatReq" serverName="farmsvr"/>
  <entry id="1446" name="RpcFarmHeartBeatRes" serverName="farmsvr"/>
  <entry id="1447" name="RpcFarmGMCmdReq" serverName="farmsvr"/>
  <entry id="1448" name="RpcFarmGMCmdRes" serverName="farmsvr"/>
  <entry id="1449" name="RpcFarmCreateReq" serverName="farmsvr"/>
  <entry id="1450" name="RpcFarmCreateRes" serverName="farmsvr"/>
  <entry id="1451" name="RpcFarmEnterReq" serverName="farmsvr"/>
  <entry id="1452" name="RpcFarmEnterRes" serverName="farmsvr"/>
  <entry id="1453" name="RpcFarmExitReq" serverName="farmsvr"/>
  <entry id="1454" name="RpcFarmExitRes" serverName="farmsvr"/>
  <entry id="1455" name="RpcFarmSyncPlayerInfoReq" serverName="farmsvr"/>
  <entry id="1456" name="RpcFarmSyncPlayerInfoRes" serverName="farmsvr"/>
  <entry id="1457" name="RpcFarmStealingCorpNtfReq" serverName="farmsvr"/>
  <entry id="1458" name="RpcFarmStealingCorpNtfRes" serverName="farmsvr"/>
  <entry id="1459" name="RpcFarmOwnerLoginLogoutReq" serverName="farmsvr"/>
  <entry id="1460" name="RpcFarmOwnerLoginLogoutRes" serverName="farmsvr"/>
  <entry id="1461" name="RpcGetFarmInfoReq" serverName="farmsvr"/>
  <entry id="1462" name="RpcGetFarmInfoRes" serverName="farmsvr"/>
  <entry id="1463" name="RpcDanMuSendNtfReq" serverName="gamesvr"/>
  <entry id="1464" name="RpcDanMuDeleteNtfReq" serverName="gamesvr"/>
  <entry id="1465" name="RpcDanMuLikeNtfReq" serverName="gamesvr"/>
  <entry id="1466" name="RpcStreamLoginReq" serverName="gamesvr"/>
  <entry id="1467" name="RpcStreamLoginRes" serverName="gamesvr"/>
  <entry id="1468" name="RpcStreamNpcChatReq" serverName="gamesvr"/>
  <entry id="1469" name="RpcStreamNpcChatRes" serverName="gamesvr"/>
  <entry id="1470" name="RpcRoomMidJoinBattleFailNtfReq" serverName="gamesvr"/>
  <entry id="1471" name="RpcStreamLLMCheckReq" serverName="gamesvr"/>
  <entry id="1472" name="RpcStreamLLMCheckRes" serverName="gamesvr"/>
  <entry id="1473" name="RpcFarmsvrCsNtfForwardReq" serverName="gamesvr"/>
  <entry id="1474" name="RpcFarmKickNtfReq" serverName="gamesvr"/>
  <entry id="1475" name="RpcFarmEventNtfReq" serverName="gamesvr"/>
  <entry id="1476" name="RpcGetUgcBuyPartnerInfoReq" serverName="gamesvr"/>
  <entry id="1477" name="RpcGetUgcBuyPartnerInfoRes" serverName="gamesvr"/>
  <entry id="1478" name="RpcTopRankCoordinationReq" serverName="ranksvr"/>
  <entry id="1479" name="RpcTopRankCoordinationRes" serverName="ranksvr"/>
  <entry id="1480" name="RpcGetRoomBattleInfoReq" serverName="roomsvr"/>
  <entry id="1481" name="RpcGetRoomBattleInfoRes" serverName="roomsvr"/>
  <entry id="1482" name="RoomDirectJoinBattleReq" serverName="roomsvr"/>
  <entry id="1483" name="RoomDirectJoinBattleRes" serverName="roomsvr"/>
  <entry id="1484" name="TestStreamSvrReq" serverName="streamsvr"/>
  <entry id="1485" name="TestStreamSvrRes" serverName="streamsvr"/>
  <entry id="1486" name="RpcKickStreamPlayerNtfReq" serverName="streamsvr"/>
  <entry id="1487" name="RpcFetchUgcTopRankReq" serverName="ugcdatastoresvr"/>
  <entry id="1488" name="RpcFetchUgcTopRankRes" serverName="ugcdatastoresvr"/>
  <entry id="1489" name="RpcBatchGetUgcRankPlayerPublicReq" serverName="ugcdatastoresvr"/>
  <entry id="1490" name="RpcBatchGetUgcRankPlayerPublicRes" serverName="ugcdatastoresvr"/>
  <entry id="1491" name="RpcUpdateUgcMapRankReq" serverName="ugcdatastoresvr"/>
  <entry id="1492" name="RpcUpdateUgcMapRankRes" serverName="ugcdatastoresvr"/>
  <entry id="1493" name="RpcGetUgcRankAppointPlayerReq" serverName="ugcdatastoresvr"/>
  <entry id="1494" name="RpcGetUgcRankAppointPlayerRes" serverName="ugcdatastoresvr"/>
  <entry id="1495" name="RpcDeleteUgcRankEntryByPlayerReq" serverName="ugcdatastoresvr"/>
  <entry id="1496" name="RpcDeleteUgcRankEntryByPlayerRes" serverName="ugcdatastoresvr"/>
  <entry id="1497" name="RpcGetUgcRankAppointRankNoReq" serverName="ugcdatastoresvr"/>
  <entry id="1498" name="RpcGetUgcRankAppointRankNoRes" serverName="ugcdatastoresvr"/>
  <entry id="1499" name="RpcReportDanMuSendReq" serverName="ugcplatsvr"/>
  <entry id="1500" name="RpcReportDanMuSendRes" serverName="ugcplatsvr"/>
  <entry id="1501" name="RpcReportDanMuDeleteReq" serverName="ugcplatsvr"/>
  <entry id="1502" name="RpcReportDanMuDeleteRes" serverName="ugcplatsvr"/>
  <entry id="1503" name="RpcReportDanMuLikeReq" serverName="ugcplatsvr"/>
  <entry id="1504" name="RpcReportDanMuLikeRes" serverName="ugcplatsvr"/>
  <entry id="1505" name="RpcReportDanMuClearReq" serverName="ugcplatsvr"/>
  <entry id="1506" name="RpcReportDanMuClearRes" serverName="ugcplatsvr"/>
  <entry id="1507" name="RpcXiaowoReportCoverReq" serverName="ugcplatsvr"/>
  <entry id="1508" name="RpcXiaowoReportCoverRes" serverName="ugcplatsvr"/>
  <entry id="1509" name="RpcFarmGetStrangerListReq" serverName="ugcplatsvr"/>
  <entry id="1510" name="RpcFarmGetStrangerListRes" serverName="ugcplatsvr"/>
  <entry id="1511" name="RpcUgcDanMuSetInfoReq" serverName="ugcsvr"/>
  <entry id="1512" name="RpcUgcDanMuSetInfoRes" serverName="ugcsvr"/>
  <entry id="1513" name="RpcUgcDanMuInfoUpdateReq" serverName="ugcsvr"/>
  <entry id="1514" name="RpcUgcUpdateMapLabelScoreReq" serverName="ugcsvr"/>
  <entry id="1515" name="RpcUgcUpdateMapLabelScoreRes" serverName="ugcsvr"/>
  <entry id="1516" name="RpcUgcMapDownloadInfoReq" serverName="ugcsvr"/>
  <entry id="1517" name="RpcUgcMapDownloadInfoRes" serverName="ugcsvr"/>
  <entry id="1518" name="RpcUgcResPrivateAdaptReq" serverName="ugcsvr"/>
  <entry id="1519" name="RpcUgcResPrivateAdaptRes" serverName="ugcsvr"/>
  <entry id="1520" name="RpcUgcResMyListReq" serverName="ugcsvr"/>
  <entry id="1521" name="RpcUgcResMyListRes" serverName="ugcsvr"/>
  <entry id="1522" name="RpcUgcInfoSaveReq" serverName="ugcsvr"/>
  <entry id="1523" name="RpcUgcInfoSaveRes" serverName="ugcsvr"/>
  <entry id="1524" name="RpcUgcDeleteCoverReq" serverName="ugcsvr"/>
  <entry id="1525" name="RpcUgcDeleteCoverRes" serverName="ugcsvr"/>
  <entry id="1526" name="RpcUgcSetUpCoverReq" serverName="ugcsvr"/>
  <entry id="1527" name="RpcUgcSetUpCoverRes" serverName="ugcsvr"/>
  <entry id="1528" name="RpcUgcCoverListReq" serverName="ugcsvr"/>
  <entry id="1529" name="RpcUgcCoverListRes" serverName="ugcsvr"/>
  <entry id="1530" name="RpcUgcCheckSingleRankUpdateReq" serverName="ugcsvr"/>
  <entry id="1531" name="RpcUgcCheckSingleRankUpdateRes" serverName="ugcsvr"/>
  <entry id="1532" name="RpcSetUgcMapRankReq" serverName="ugcsvr"/>
  <entry id="1533" name="RpcSetUgcMapRankRes" serverName="ugcsvr"/>
  <entry id="1534" name="RpcXiaoWoShareReq" serverName="xiaowosvr"/>
  <entry id="1535" name="RpcXiaoWoShareRes" serverName="xiaowosvr"/>
  <entry id="1536" name="RpcXiaoWoGetVerAndGroupReq" serverName="xiaowosvr"/>
  <entry id="1537" name="RpcXiaoWoGetVerAndGroupRes" serverName="xiaowosvr"/>
  <entry id="1538" name="CacheLockPreemptReq" serverName="noserver"/>
  <entry id="1539" name="CacheLockPreemptRes" serverName="noserver"/>
  <entry id="1540" name="RpcSignOutPlayerReq" serverName="ugcsvr"/>
  <entry id="1541" name="RpcSignOutPlayerRes" serverName="ugcsvr"/>
  <entry id="1542" name="RpcQuerySubscribeQQRobotReq" serverName="ugcplatsvr"/>
  <entry id="1543" name="RpcQuerySubscribeQQRobotRes" serverName="ugcplatsvr"/>
  <entry id="1544" name="RpcDoSubscribeQQRobotReq" serverName="ugcplatsvr"/>
  <entry id="1545" name="RpcDoSubscribeQQRobotRes" serverName="ugcplatsvr"/>
  <entry id="1546" name="RpcGetUserCloudInfoReq" serverName="ugcplatsvr"/>
  <entry id="1547" name="RpcGetUserCloudInfoRes" serverName="ugcplatsvr"/>
  <entry id="1548" name="RpcFarmReceiveNewLiuYanMessageNtfReq" serverName="gamesvr"/>
  <entry id="1549" name="RpcFarmReceiveNewLiuYanMessageNtfRes" serverName="gamesvr"/>
  <entry id="1550" name="RpcFarmSetWelcomeInfoReq" serverName="farmsvr"/>
  <entry id="1551" name="RpcFarmSetWelcomeInfoRes" serverName="farmsvr"/>
  <entry id="1621" name="RpcRoomAskToJoinReq" serverName="gamesvr"/>
  <entry id="1622" name="RpcRoomAskToJoinRes" serverName="gamesvr"/>
  <entry id="1623" name="RpcNoticePlayerDoJoinRoomReq" serverName="gamesvr"/>
  <entry id="1624" name="RpcNoticePlayerDoJoinRoomRes" serverName="gamesvr"/>
  <entry id="1625" name="RpcApplyAppCreatorIdReq" serverName="gamesvr"/>
  <entry id="1626" name="RpcApplyAppCreatorIdRes" serverName="gamesvr"/>
  <entry id="1627" name="RpcAppCommonReq" serverName="ugcappsvr"/>
  <entry id="1628" name="RpcAppCommonRes" serverName="ugcappsvr"/>
  <entry id="1629" name="RpcUgcAppApplyCreatorIdReq" serverName="ugcappsvr"/>
  <entry id="1630" name="RpcUgcAppApplyCreatorIdRes" serverName="ugcappsvr"/>
  <entry id="1631" name="RpcUgcAppAccountBindingNtfReq" serverName="ugcappsvr"/>
  <entry id="1632" name="RpcUgcAppAccountBindingNtfRes" serverName="ugcappsvr"/>
  <entry id="1633" name="RpcUgcAppUserInfoSyncNtfReq" serverName="ugcappsvr"/>
  <entry id="1634" name="RpcUgcAppUserInfoSyncNtfRes" serverName="ugcappsvr"/>
  <entry id="1635" name="RpcUgcAppCopyMapReq" serverName="ugcappsvr"/>
  <entry id="1636" name="RpcUgcAppCopyMapRes" serverName="ugcappsvr"/>
  <entry id="1637" name="RpcAppNtfReq" serverName="ugcplatsvr"/>
  <entry id="1638" name="RpcAppNtfRes" serverName="ugcplatsvr"/>
  <entry id="1639" name="UgcCopyMapMsgReq" serverName="ugcplatsvr"/>
  <entry id="1640" name="UgcCopyMapMsgRes" serverName="ugcplatsvr"/>
  <entry id="1641" name="UgcCopyProgressMsgReq" serverName="ugcplatsvr"/>
  <entry id="1642" name="UgcCopyProgressMsgRes" serverName="ugcplatsvr"/>
  <entry id="1643" name="RpcAppApplyCreatorIdReq" serverName="ugcsvr"/>
  <entry id="1644" name="RpcAppApplyCreatorIdRes" serverName="ugcsvr"/>
  <entry id="1645" name="RpcAppUserInfoSyncNtfReq" serverName="ugcsvr"/>
  <entry id="1646" name="RpcAppUserInfoSyncNtfRes" serverName="ugcsvr"/>
  <entry id="1647" name="RpcBattleSyncPlayerInfoReq" serverName="battlesvr"/>
  <entry id="1648" name="RpcBattleSyncPlayerInfoRes" serverName="battlesvr"/>
  <entry id="1649" name="PlatformSynBanListReq" serverName="ugcsvr"/>
  <entry id="1650" name="PlatformSynBanListRes" serverName="ugcsvr"/>
  <entry id="1651" name="RpcCsForwardReq" serverName="ugcsvr"/>
  <entry id="1652" name="RpcCsForwardRes" serverName="ugcsvr"/>
  <entry id="1653" name="UgcAdminForwardReq" serverName="ugcplatsvr"/>
  <entry id="1654" name="UgcAdminForwardRes" serverName="ugcplatsvr"/>
  <entry id="1655" name="AdminMsgTestReq" serverName="ugcsvr"/>
  <entry id="1656" name="AdminMsgTestRes" serverName="ugcsvr"/>
  <entry id="1657" name="RpcPlayerReputationScoreReportCheckReq" serverName="battlesvr"/>
  <entry id="1658" name="RpcPlayerReputationScoreReportCheckRes" serverName="battlesvr"/>
  <entry id="1659" name="RpcClubRankSettlementNtfReq" serverName="clubsvr"/>
  <entry id="1660" name="RpcClubRankSettlementInternalNtfReq" serverName="clubsvr"/>
  <entry id="1661" name="RpcFarmGetVisitorsReq" serverName="farmsvr"/>
  <entry id="1662" name="RpcFarmGetVisitorsRes" serverName="farmsvr"/>
  <entry id="1663" name="RpcModifyFarmMothCardReq" serverName="farmsvr"/>
  <entry id="1664" name="RpcModifyFarmMothCardRes" serverName="farmsvr"/>
  <entry id="1665" name="RpcGmReq" serverName="gamesvr"/>
  <entry id="1666" name="RpcGmRes" serverName="gamesvr"/>
  <entry id="1667" name="RpcGetPlayerRoomInfoReq" serverName="gamesvr"/>
  <entry id="1668" name="RpcGetPlayerRoomInfoRes" serverName="gamesvr"/>
  <entry id="1669" name="RpcNoticeTeamPartnerJoinMiniGameReq" serverName="gamesvr"/>
  <entry id="1670" name="RpcNoticeTeamPartnerJoinMiniGameRes" serverName="gamesvr"/>
  <entry id="1671" name="RpcMiniGameNoticeInfoNtf" serverName="gamesvr"/>
  <entry id="1672" name="RpcActivityInfoNtfReq" serverName="gamesvr"/>
  <entry id="1673" name="RpcActivityInfoNtfRes" serverName="gamesvr"/>
  <entry id="1674" name="RpcActivityOfRewardReq" serverName="gamesvr"/>
  <entry id="1675" name="RpcActivityOfRewardRes" serverName="gamesvr"/>
  <entry id="1676" name="RpcPlayerReputationScoreBeReportedReq" serverName="gamesvr"/>
  <entry id="1677" name="RpcPlayerReputationScoreBeReportedRes" serverName="gamesvr"/>
  <entry id="1678" name="RpcMatchReputationScoreNoEnoughNtfReq" serverName="gamesvr"/>
  <entry id="1679" name="RpcMatchReputationScoreNoEnoughNtfRes" serverName="gamesvr"/>
  <entry id="1680" name="RpcPlayerTaskChangeNtfReq" serverName="gamesvr"/>
  <entry id="1681" name="RpcPlayerTaskChangeNtfRes" serverName="gamesvr"/>
  <entry id="1682" name="RpcUgcMatchBattleRecordNtfReq" serverName="gamesvr"/>
  <entry id="1683" name="RpcLobbyServerGMCommandReq" serverName="lobbysvr"/>
  <entry id="1684" name="RpcLobbyServerGMCommandRes" serverName="lobbysvr"/>
  <entry id="1685" name="RpcRoomMidJoinBattleResultReq" serverName="roomsvr"/>
  <entry id="1686" name="RpcRoomMidJoinBattleResultRes" serverName="roomsvr"/>
  <entry id="1687" name="RpcGetAchievementCompleteStatusReq" serverName="ugcplatsvr"/>
  <entry id="1688" name="RpcGetAchievementCompleteStatusRes" serverName="ugcplatsvr"/>
  <entry id="1689" name="GMSetUgcMatchMapReq" serverName="ugcplatsvr"/>
  <entry id="1690" name="GMSetUgcMatchMapRes" serverName="ugcplatsvr"/>
  <entry id="1691" name="RpcPlayerActivityInfoReq" serverName="activitysvr"/>
  <entry id="1692" name="RpcPlayerActivityInfoRes" serverName="activitysvr"/>
  <entry id="1693" name="RpcBatchSetUgcMapRankReq" serverName="ugcsvr"/>
  <entry id="1694" name="RpcBatchSetUgcMapRankRes" serverName="ugcsvr"/>
  <entry id="1695" name="RpcActivityReceiveRewardsReq" serverName="activitysvr"/>
  <entry id="1696" name="RpcActivityReceiveRewardsRes" serverName="activitysvr"/>
  <entry id="1697" name="RpcGMUpdatePublishTimeReq" serverName="ugcsvr"/>
  <entry id="1698" name="RpcGMUpdatePublishTimeRes" serverName="ugcsvr"/>
  <entry id="1699" name="SendServerEventReq" serverName="activitysvr"/>
  <entry id="1700" name="SendServerEventRes" serverName="activitysvr"/>
  <entry id="1701" name="RpcMatchFillBackCancelResultReq" serverName="battlesvr"/>
  <entry id="1702" name="RpcMatchFillBackCancelResultRes" serverName="battlesvr"/>
  <entry id="1703" name="RpcGetCosUrlByUgcIdsReq" serverName="ugcsvr"/>
  <entry id="1704" name="RpcGetCosUrlByUgcIdsRes" serverName="ugcsvr"/>
  <entry id="1705" name="RpcXiaoWoModifyMd5Req" serverName="xiaowosvr"/>
  <entry id="1706" name="RpcXiaoWoModifyMd5Res" serverName="xiaowosvr"/>
  <entry id="1707" name="RpcXiaoWoEnterPrepareReq" serverName="xiaowosvr"/>
  <entry id="1708" name="RpcXiaoWoEnterPrepareRes" serverName="xiaowosvr"/>
  <entry id="1709" name="RpcXiaoWoSetLiuYanPermissionReq" serverName="xiaowosvr"/>
  <entry id="1710" name="RpcXiaoWoSetLiuYanPermissionRes" serverName="xiaowosvr"/>
  <entry id="1711" name="RpcXiaoWoGetLiuYanPermissionReq" serverName="xiaowosvr"/>
  <entry id="1712" name="RpcXiaoWoGetLiuYanPermissionRes" serverName="xiaowosvr"/>
  <entry id="1713" name="RpcGetMapRankListReq" serverName="ugcsvr"/>
  <entry id="1714" name="RpcGetMapRankListRes" serverName="ugcsvr"/>
  <entry id="1715" name="RpcPlayerLoginReq" serverName="activitysvr"/>
  <entry id="1716" name="RpcPlayerLoginRes" serverName="activitysvr"/>
  <entry id="1717" name="RpcActivityGeneralReq" serverName="activitysvr"/>
  <entry id="1718" name="RpcActivityGeneralRes" serverName="activitysvr"/>
  <entry id="1719" name="RpcActivityReceiveRewardsNtfReq" serverName="gamesvr"/>
  <entry id="1720" name="RpcActivityReceiveRewardsNtfRes" serverName="gamesvr"/>
  <entry id="1721" name="RpcActivityGeneralNtfReq" serverName="gamesvr"/>
  <entry id="1722" name="RpcActivityGeneralNtfRes" serverName="gamesvr"/>
  <entry id="1723" name="RpcF2FRoomCreateReq" serverName="lbssvr"/>
  <entry id="1724" name="RpcF2FRoomCreateRes" serverName="lbssvr"/>
  <entry id="1725" name="RpcF2FRoomSearchReq" serverName="lbssvr"/>
  <entry id="1726" name="RpcF2FRoomSearchRes" serverName="lbssvr"/>
  <entry id="1727" name="RpcF2FRoomRenewReq" serverName="lbssvr"/>
  <entry id="1728" name="RpcF2FRoomRenewRes" serverName="lbssvr"/>
  <entry id="1729" name="RpcF2FRoomRemoveReq" serverName="lbssvr"/>
  <entry id="1730" name="RpcF2FRoomRemoveRes" serverName="lbssvr"/>
  <entry id="1731" name="RpcUgcVideoExamineResponseNtfReq" serverName="ugcappsvr"/>
  <entry id="1732" name="RpcUgcAigcGenAnicapResNtfReq" serverName="ugcappsvr"/>
  <entry id="1733" name="RpcUgcAigcGenVoiceResNtfReq" serverName="ugcappsvr"/>
  <entry id="1734" name="RpcWolfKillReportNtfReq" serverName="gamesvr"/>
  <entry id="1735" name="RpcUgcAigcGenImageResNtfReq" serverName="ugcappsvr"/>
  <entry id="1736" name="RpcAigcGenModuleNtfReq" serverName="aigcsvr"/>
  <entry id="1737" name="RpcAigcGenMagicPicNtfReq" serverName="aigcsvr"/>
  <entry id="1738" name="RpcAigcGetHistoryReq" serverName="aigcsvr"/>
  <entry id="1739" name="RpcAigcGetHistoryRes" serverName="aigcsvr"/>
  <entry id="1740" name="RpcAigcUseHistoryReq" serverName="aigcsvr"/>
  <entry id="1741" name="RpcAigcUseHistoryRes" serverName="aigcsvr"/>
  <entry id="1742" name="RpcAigcDelHistoryReq" serverName="aigcsvr"/>
  <entry id="1743" name="RpcAigcDelHistoryRes" serverName="aigcsvr"/>
  <entry id="1745" name="RpcBattlePlayerChangeSlotReq" serverName="battlesvr"/>
  <entry id="1746" name="RpcBattlePlayerChangeSlotRes" serverName="battlesvr"/>
  <entry id="1747" name="UgcAppTlogReportReq" serverName="ugcplatsvr"/>
  <entry id="1748" name="UgcAppTlogReportRes" serverName="ugcplatsvr"/>
  <entry id="1751" name="RpcFarmFriendBeTagNtfReq" serverName="farmsvr"/>
  <entry id="1752" name="RpcSearchForUidReq" serverName="gamesvr"/>
  <entry id="1753" name="RpcSearchForUidRes" serverName="gamesvr"/>
  <entry id="1754" name="RpcUgcMatchMapBriefFromRemoteConfigReq" serverName="ugcsvr"/>
  <entry id="1755" name="RpcUgcMatchMapBriefFromRemoteConfigRes" serverName="ugcsvr"/>
  <entry id="1756" name="RpcFarmBeTagNtfReq" serverName="gamesvr"/>
  <entry id="1767" name="RpcUgcdatastoreCsForwardReq" serverName="ugcdatastoresvr"/>
  <entry id="1768" name="RpcUgcdatastoreCsForwardRes" serverName="ugcdatastoresvr"/>
  <entry id="1772" name="TestSnsSvrReq" serverName="snssvr"/>
  <entry id="1773" name="TestSnsSvrRes" serverName="snssvr"/>
  <entry id="1774" name="RpcRelationOpNtfReq" serverName="snssvr"/>
  <entry id="1775" name="RpcGetRelationsReq" serverName="snssvr"/>
  <entry id="1776" name="RpcGetRelationsRes" serverName="snssvr"/>
  <entry id="1777" name="RpcSnsRedirectReq" serverName="snssvr"/>
  <entry id="1778" name="RpcSnsRedirectRes" serverName="snssvr"/>
  <entry id="1779" name="RpcSnsGetInteractiveInfoReq" serverName="snssvr"/>
  <entry id="1780" name="RpcSnsGetInteractiveInfoRes" serverName="snssvr"/>
  <entry id="1781" name="RpcSnsSetInteractiveInfoReq" serverName="snssvr"/>
  <entry id="1782" name="RpcSnsSetInteractiveInfoRes" serverName="snssvr"/>
  <entry id="1784" name="RpcCompetitionTextCheckReq" serverName="gamesvr"/>
  <entry id="1785" name="RpcCompetitionTextCheckRes" serverName="gamesvr"/>
  <entry id="1786" name="RpcSyncFriendInteractHistoryReq" serverName="gamesvr"/>
  <entry id="1787" name="RpcAddWishValReq" serverName="activitysvr"/>
  <entry id="1788" name="RpcAddWishValRes" serverName="activitysvr"/>
  <entry id="1789" name="RpcIdipModifyTaskProgressReq" serverName="activitysvr"/>
  <entry id="1790" name="RpcIdipModifyTaskProgressRes" serverName="activitysvr"/>
  <entry id="1791" name="RpcNtfUpdateRunActivityIdsReq" serverName="gamesvr"/>
  <entry id="1792" name="RpcMatchingResultRoomResetReq" serverName="roomsvr"/>
  <entry id="1793" name="RpcClubWeekSettlementInternalNtfReq" serverName="clubsvr"/>
  <entry id="1794" name="RpcClubGetMemberExistNumReq" serverName="clubsvr"/>
  <entry id="1795" name="RpcClubGetMemberExistNumRes" serverName="clubsvr"/>
  <entry id="1796" name="RpcActivityClearRedDotReq" serverName="activitysvr"/>
  <entry id="1797" name="RpcActivityClearRedDotRes" serverName="activitysvr"/>
  <entry id="1798" name="RpcActivityReadDotNtfReq" serverName="gamesvr"/>
  <entry id="1799" name="RpcNtfDeleteItemReq" serverName="gamesvr"/>
  <entry id="1800" name="RpcNtfDeleteItemRes" serverName="gamesvr"/>
  <entry id="1801" name="LeaseRedirectReq" serverName="noserver"/>
  <entry id="1802" name="LeaseRedirectRes" serverName="noserver"/>
  <entry id="1803" name="TransmitGMCommandWithTargetUIDReq" serverName="noserver"/>
  <entry id="1804" name="TransmitGMCommandWithTargetUIDRes" serverName="noserver"/>
  <entry id="1805" name="RpcPlayerHeartbeatReq" serverName="activitysvr"/>
  <entry id="1806" name="RpcPlayerHeartbeatRes" serverName="activitysvr"/>
  <entry id="1807" name="RpcClubCacheRealHeatInternalNtfReq" serverName="clubsvr"/>
  <entry id="1813" name="RpcCacheHeartbeatToMasterReq" serverName="cachesvr"/>
  <entry id="1814" name="RpcCacheHeartbeatToMasterRes" serverName="cachesvr"/>
  <entry id="1815" name="RpcGetCacheFromMasterReq" serverName="cachesvr"/>
  <entry id="1816" name="RpcGetCacheFromMasterRes" serverName="cachesvr"/>
  <entry id="1818" name="RpcClientLogColoringReq" serverName="gamesvr"/>
  <entry id="1819" name="RpcClientLogColoringRes" serverName="gamesvr"/>
  <entry id="1820" name="RpcFarmAddItemReq" serverName="farmsvr"/>
  <entry id="1821" name="RpcFarmAddItemRes" serverName="farmsvr"/>
  <entry id="1822" name="RpcRecvFarmGiftReq" serverName="farmsvr"/>
  <entry id="1823" name="RpcRecvFarmGiftRes" serverName="farmsvr"/>
  <entry id="1824" name="RpcFarmGiftCheckReq" serverName="farmsvr"/>
  <entry id="1825" name="RpcFarmGiftCheckRes" serverName="farmsvr"/>
  <entry id="1826" name="RpcDataStoreKvFragmentNtfReq" serverName="gamesvr"/>
  <entry id="1827" name="RpcDataStoreKvFragmentNtfRes" serverName="gamesvr"/>
  <entry id="1828" name="RpcArenaCardEventNtfReq" serverName="gamesvr"/>
  <entry id="1829" name="RpcArenaAddEquipReq" serverName="arenasvr"/>
  <entry id="1830" name="RpcArenaAddEquipRes" serverName="arenasvr"/>
  <entry id="1831" name="RpcArenaUseCardPackCheckReq" serverName="arenasvr"/>
  <entry id="1832" name="RpcArenaUseCardPackCheckRes" serverName="arenasvr"/>
  <entry id="1833" name="RpcArenaUseCardPackReq" serverName="arenasvr"/>
  <entry id="1834" name="RpcArenaUseCardPackRes" serverName="arenasvr"/>
  <entry id="1835" name="RpcArenaGetGameInfoReq" serverName="arenasvr"/>
  <entry id="1836" name="RpcArenaGetGameInfoRes" serverName="arenasvr"/>
  <entry id="1837" name="RpcArenaAddHeroReq" serverName="arenasvr"/>
  <entry id="1838" name="RpcArenaAddHeroRes" serverName="arenasvr"/>
  <entry id="1839" name="RpcArenaCheckHasHeroReq" serverName="arenasvr"/>
  <entry id="1840" name="RpcArenaCheckHasHeroRes" serverName="arenasvr"/>
  <entry id="1841" name="RpcArenaSelectFrameReq" serverName="arenasvr"/>
  <entry id="1842" name="RpcArenaSelectFrameRes" serverName="arenasvr"/>
  <entry id="1843" name="RpcArenaSelectVoiceStyleReq" serverName="arenasvr"/>
  <entry id="1844" name="RpcArenaSelectVoiceStyleRes" serverName="arenasvr"/>
  <entry id="1845" name="RpcArenaSelectEquipReq" serverName="arenasvr"/>
  <entry id="1846" name="RpcArenaSelectEquipRes" serverName="arenasvr"/>
  <entry id="1847" name="RpcArenaGmUnlockCardReq" serverName="arenasvr"/>
  <entry id="1848" name="RpcArenaGmUnlockCardRes" serverName="arenasvr"/>
  <entry id="1849" name="RpcArenaSelectCardReq" serverName="arenasvr"/>
  <entry id="1850" name="RpcArenaSelectCardRes" serverName="arenasvr"/>
  <entry id="1856" name="RpcCommonCsForwardReq" serverName="noserver"/>
  <entry id="1857" name="RpcCommonCsForwardRes" serverName="noserver"/>
  <entry id="1858" name="RpcAigcNpcFeedBackReq" serverName="aigcsvr"/>
  <entry id="1859" name="RpcAigcNpcFeedBackRes" serverName="aigcsvr"/>
  <entry id="1860" name="RpcBatchGetCacheDataReq" serverName="cachesvr"/>
  <entry id="1861" name="RpcBatchGetCacheDataRes" serverName="cachesvr"/>
  <entry id="1862" name="RpcSlaveCacheLoadFromMasterReq" serverName="cachesvr"/>
  <entry id="1863" name="RpcSlaveCacheLoadFromMasterRes" serverName="cachesvr"/>
  <entry id="1864" name="RpcSlaveCacheHeartbeatToMasterReq" serverName="cachesvr"/>
  <entry id="1865" name="RpcSlaveCacheHeartbeatToMasterRes" serverName="cachesvr"/>
  <entry id="1866" name="RpcSlaveCacheRemoveNtfToMasterReq" serverName="cachesvr"/>
  <entry id="1867" name="RpcSlaveCacheRemoveNtfToMasterRes" serverName="cachesvr"/>
  <entry id="1868" name="RpcModifyCacheReq" serverName="cachesvr"/>
  <entry id="1869" name="RpcModifyCacheRes" serverName="cachesvr"/>
  <entry id="1870" name="RpcMasterNtfModifyToSlaveReq" serverName="cachesvr"/>
  <entry id="1871" name="RpcMasterNtfModifyToSlaveRes" serverName="cachesvr"/>
  <entry id="1872" name="RpcMasterNtfRemoveToSlaveReq" serverName="cachesvr"/>
  <entry id="1873" name="RpcMasterNtfRemoveToSlaveRes" serverName="cachesvr"/>
  <entry id="1874" name="RpcUgcPlayerPublicAttrModifyNtfReq" serverName="gamesvr"/>
  <entry id="1875" name="RpcUgcPlayerPublicAttrModifyNtfRes" serverName="gamesvr"/>
  <entry id="1876" name="RpcUgcPlayerPublicHeartbeatTimeoutNtfReq" serverName="gamesvr"/>
  <entry id="1877" name="RpcUgcPlayerPublicHeartbeatTimeoutNtfRes" serverName="gamesvr"/>
  <entry id="1878" name="RpcUgcCreateRoleReq" serverName="gamesvr"/>
  <entry id="1879" name="RpcUgcCreateRoleRes" serverName="gamesvr"/>
  <entry id="1880" name="RpcLuckyFriendApplyTaskReq" serverName="gamesvr"/>
  <entry id="1881" name="RpcLuckyFriendApplyTaskRes" serverName="gamesvr"/>
  <entry id="1882" name="RpcLuckyFriendOperateTaskApplyReq" serverName="gamesvr"/>
  <entry id="1883" name="RpcLuckyFriendOperateTaskApplyRes" serverName="gamesvr"/>
  <entry id="1884" name="RpcLuckyFriendNoticeTaskReq" serverName="gamesvr"/>
  <entry id="1885" name="RpcLuckyFriendNoticeTaskRes" serverName="gamesvr"/>
  <entry id="1886" name="RpcKickCacheReq" serverName="cachesvr"/>
  <entry id="1887" name="RpcKickCacheRes" serverName="cachesvr"/>
  <entry id="1891" name="RpcMapTakeOffApplyReq" serverName="ugcsvr"/>
  <entry id="1892" name="RpcMapTakeOffApplyRes" serverName="ugcsvr"/>
  <entry id="1893" name="RpcGetMapSettingReq" serverName="ugcsvr"/>
  <entry id="1894" name="RpcGetMapSettingRes" serverName="ugcsvr"/>
  <entry id="1895" name="RpcClubChallengeInfoReq" serverName="clubsvr"/>
  <entry id="1896" name="RpcClubChallengeInfoRes" serverName="clubsvr"/>
  <entry id="1897" name="RpcClubChallengeCanReportReq" serverName="clubsvr"/>
  <entry id="1898" name="RpcClubChallengeCanReportRes" serverName="clubsvr"/>
  <entry id="1899" name="RpcClubChallengeReportReq" serverName="clubsvr"/>
  <entry id="1900" name="RpcClubChallengeReportRes" serverName="clubsvr"/>
  <entry id="1901" name="RpcBatchGetPlayMapTimeReq" serverName="ugcsvr"/>
  <entry id="1902" name="RpcBatchGetPlayMapTimeRes" serverName="ugcsvr"/>
  <entry id="1903" name="TestAsyncSvrReq" serverName="asyncsvr"/>
  <entry id="1904" name="TestAsyncSvrRes" serverName="asyncsvr"/>
  <entry id="1905" name="RpcChangeUgcLayerIdReq" serverName="ugcsvr"/>
  <entry id="1906" name="RpcChangeUgcLayerIdRes" serverName="ugcsvr"/>
  <entry id="1907" name="RpcRemoveUgcLayerReq" serverName="ugcsvr"/>
  <entry id="1908" name="RpcRemoveUgcLayerRes" serverName="ugcsvr"/>
  <entry id="1909" name="UgcFansChangeReq" serverName="asyncsvr"/>
  <entry id="1910" name="UgcFansChangeRes" serverName="asyncsvr"/>
  <entry id="1911" name="RpcMatchFillBackResultRoomReq" serverName="roomsvr"/>
  <entry id="1912" name="RpcMatchFillBackResultRoomRes" serverName="roomsvr"/>
  <entry id="1913" name="RpcMatchFillBackCancelResultRoomReq" serverName="roomsvr"/>
  <entry id="1914" name="RpcMatchFillBackCancelResultRoomRes" serverName="roomsvr"/>
  <entry id="1915" name="RpcBackFarmGiftReq" serverName="farmsvr"/>
  <entry id="1916" name="RpcBackFarmGiftRes" serverName="farmsvr"/>
  <entry id="1917" name="RpcBattleRecruitPublishReq" serverName="battlesvr"/>
  <entry id="1918" name="RpcBattleRecruitPublishRes" serverName="battlesvr"/>
  <entry id="1919" name="RpcGetBattleMatchInfoReq" serverName="battlesvr"/>
  <entry id="1920" name="RpcGetBattleMatchInfoRes" serverName="battlesvr"/>
  <entry id="1921" name="RpcGetBattlePublicRoomInfoReq" serverName="battlesvr"/>
  <entry id="1922" name="RpcGetBattlePublicRoomInfoRes" serverName="battlesvr"/>
  <entry id="1923" name="RpcGetItemCountReq" serverName="gamesvr"/>
  <entry id="1924" name="RpcGetItemCountRes" serverName="gamesvr"/>
  <entry id="1925" name="RpcArenaHeroInfoNtfReq" serverName="gamesvr"/>
  <entry id="1926" name="RpcArenaGetRecommendCardReq" serverName="ugcplatsvr"/>
  <entry id="1927" name="RpcArenaGetRecommendCardRes" serverName="ugcplatsvr"/>
  <entry id="1928" name="RpcArenaHeroInfoChangeNtfReq" serverName="gamesvr"/>
  <entry id="1929" name="RpcArenaGetAllUnlockedHeroInfoReq" serverName="arenasvr"/>
  <entry id="1930" name="RpcArenaGetAllUnlockedHeroInfoRes" serverName="arenasvr"/>
  <entry id="1931" name="RpcArenaLockHeroReq" serverName="arenasvr"/>
  <entry id="1932" name="RpcArenaLockHeroRes" serverName="arenasvr"/>
  <entry id="1933" name="TransmitGMCommandWithServerIdReq" serverName="noserver"/>
  <entry id="1934" name="TransmitGMCommandWithServerIdRes" serverName="noserver"/>
  <entry id="1935" name="RpcFarmBuffAddReq" serverName="farmsvr"/>
  <entry id="1936" name="RpcFarmBuffAddRes" serverName="farmsvr"/>
  <entry id="1937" name="RpcFarmBuffDelReq" serverName="farmsvr"/>
  <entry id="1938" name="RpcFarmBuffDelRes" serverName="farmsvr"/>
  <entry id="1941" name="ActivityGroupingReturnInfoReq" serverName="common_proto"/>
  <entry id="1942" name="ActivityGroupingReturnInfoRes" serverName="common_proto"/>
  <entry id="1943" name="ActivityGroupingReturnRewardReq" serverName="common_proto"/>
  <entry id="1944" name="ActivityGroupingReturnRewardRes" serverName="common_proto"/>
  <entry id="1945" name="RpcGetQuickRewardListReq" serverName="activitysvr"/>
  <entry id="1946" name="RpcGetQuickRewardListRes" serverName="activitysvr"/>
  <entry id="1947" name="RpcReceiveQuickRewardReq" serverName="activitysvr"/>
  <entry id="1948" name="RpcReceiveQuickRewardRes" serverName="activitysvr"/>
  <entry id="1949" name="RpcSlaveHeartbeatToMasterReq" serverName="cachesvr"/>
  <entry id="1950" name="RpcSlaveHeartbeatToMasterRes" serverName="cachesvr"/>
  <entry id="1951" name="RpcMasterHeartbeatToSlaveReq" serverName="cachesvr"/>
  <entry id="1952" name="RpcMasterHeartbeatToSlaveRes" serverName="cachesvr"/>
  <entry id="1953" name="RpcGetClubLogReq" serverName="clubsvr"/>
  <entry id="1954" name="RpcGetClubLogRes" serverName="clubsvr"/>
  <entry id="1955" name="RpcBookOfFriendsExpenseReq" serverName="activitysvr"/>
  <entry id="1956" name="RpcBookOfFriendsExpenseRes" serverName="activitysvr"/>
  <entry id="1957" name="RpcBookOfFriendsIncomeReq" serverName="activitysvr"/>
  <entry id="1958" name="RpcBookOfFriendsIncomeRes" serverName="activitysvr"/>
  <entry id="1959" name="RpcActivityCommonNtfReq" serverName="gamesvr"/>
  <entry id="1960" name="RpcProfileTestReq" serverName="profilesvr"/>
  <entry id="1961" name="RpcProfileTestRes" serverName="profilesvr"/>
  <entry id="1962" name="RpcProfileSyncPlayerPublicReq" serverName="profilesvr"/>
  <entry id="1963" name="RpcProfileSyncPlayerPublicRes" serverName="profilesvr"/>
  <entry id="1964" name="RpcSnsSyncClientPlayerPublicReq" serverName="snssvr"/>
  <entry id="1965" name="RpcSnsSyncClientPlayerPublicRes" serverName="snssvr"/>
  <entry id="1966" name="RpcSnsDestDispatchPlayerPublicReq" serverName="snssvr"/>
  <entry id="1967" name="RpcSnsDestDispatchPlayerPublicRes" serverName="snssvr"/>
  <entry id="1968" name="RpcDispatchPlayerPublicReq" serverName="noserver"/>
  <entry id="1969" name="RpcDispatchPlayerPublicRes" serverName="noserver"/>
  <entry id="1970" name="RpcGetUgcMatchModeSortInfoReq" serverName="ugcsvr"/>
  <entry id="1971" name="RpcGetUgcMatchModeSortInfoRes" serverName="ugcsvr"/>
  <entry id="1972" name="RpcPushFarmActivityNoticeReq" serverName="ugcplatsvr"/>
  <entry id="1973" name="RpcPushFarmActivityNoticeRes" serverName="ugcplatsvr"/>
  <entry id="1974" name="RpcActivityGMCmdReq" serverName="activitysvr"/>
  <entry id="1975" name="RpcActivityGMCmdRes" serverName="activitysvr"/>
  <entry id="1976" name="RpcMigrateDscRegionReq" serverName="battlesvr"/>
  <entry id="1977" name="RpcMigrateDscRegionRes" serverName="battlesvr"/>
  <entry id="1978" name="RpcMigrateDscRegionResultNtfReq" serverName="tycoonsvr"/>
  <entry id="1979" name="RpcMigrateDscRegionResultNtfRes" serverName="tycoonsvr"/>
  <entry id="2176" name="RpcFriendsBroadcastReq" serverName="gamesvr"/>
  <entry id="2177" name="RpcSnsFriendsBroadcastReq" serverName="snssvr"/>
  <entry id="2179" name="RpcFriendsBroadcastTestReq" serverName="gamesvr"/>
  <entry id="2180" name="CsForwardHashKeyReq" serverName="noserver"/>
  <entry id="2181" name="CsForwardHashKeyRes" serverName="noserver"/>
  <entry id="2182" name="CsForwardDestServReq" serverName="noserver"/>
  <entry id="2183" name="CsForwardDestServRes" serverName="noserver"/>
  <entry id="2184" name="RpcBattleCommonBroadcastInfoReq" serverName="battlesvr"/>
  <entry id="2185" name="RpcBattleCommonBroadcastInfoRes" serverName="battlesvr"/>
  <entry id="2186" name="RpcBattleCommonBroadcastInfoNtfReq" serverName="gamesvr"/>
  <entry id="2187" name="RpcBattleMemberListGetReq" serverName="battlesvr"/>
  <entry id="2188" name="RpcBattleMemberListGetRes" serverName="battlesvr"/>
  <entry id="2191" name="RpcArenaSelectHeroSkinReq" serverName="arenasvr"/>
  <entry id="2192" name="RpcArenaSelectHeroSkinRes" serverName="arenasvr"/>
  <entry id="2193" name="RpcGetPlayerPlayMapsTimeReq" serverName="ugcsvr"/>
  <entry id="2194" name="RpcGetPlayerPlayMapsTimeRes" serverName="ugcsvr"/>
  <entry id="2196" name="RpcGmSendCsToastReq" serverName="gamesvr"/>
  <entry id="2197" name="RpcGroupingReturnKickNtfReq" serverName="activitysvr"/>
  <entry id="2202" name="RpcArenaCsForwardReq" serverName="arenasvr"/>
  <entry id="2203" name="RpcArenaCsForwardRes" serverName="arenasvr"/>
  <entry id="2204" name="RpcStreamLogoutReq" serverName="gamesvr"/>
  <entry id="2237" name="RpcHouseCreateReq" serverName="farmsvr"/>
  <entry id="2238" name="RpcHouseCreateRes" serverName="farmsvr"/>
  <entry id="2239" name="RpcHouseEnterReq" serverName="farmsvr"/>
  <entry id="2240" name="RpcHouseEnterRes" serverName="farmsvr"/>
  <entry id="2241" name="RpcHouseExitReq" serverName="farmsvr"/>
  <entry id="2242" name="RpcHouseExitRes" serverName="farmsvr"/>
  <entry id="2243" name="RpcHouseHeartBeatReq" serverName="farmsvr"/>
  <entry id="2244" name="RpcHouseHeartBeatRes" serverName="farmsvr"/>
  <entry id="2245" name="RpcHouseOwnerLoginLogoutReq" serverName="farmsvr"/>
  <entry id="2246" name="RpcHouseOwnerLoginLogoutRes" serverName="farmsvr"/>
  <entry id="2247" name="RpcHouseSyncPlayerInfoReq" serverName="farmsvr"/>
  <entry id="2248" name="RpcHouseSyncPlayerInfoRes" serverName="farmsvr"/>
  <entry id="2249" name="RpcHouseKickNtfReq" serverName="gamesvr"/>
  <entry id="2250" name="RpcHouseEventNtfReq" serverName="gamesvr"/>
  <entry id="2251" name="RpcHouseVisitorInfoReq" serverName="farmsvr"/>
  <entry id="2252" name="RpcHouseVisitorInfoRes" serverName="farmsvr"/>
  <entry id="2253" name="RpcBindPhoneReq" serverName="gamesvr"/>
  <entry id="2254" name="RpcBindPhoneRes" serverName="gamesvr"/>
  <entry id="2255" name="RpcNr3e8CommonNtfReq" serverName="gamesvr"/>
  <entry id="2257" name="RpcHouseRoomOnLeaveNtfReq" serverName="farmsvr"/>
  <entry id="2258" name="RpcBattlePartnerCoMatchProposeReq" serverName="battlesvr"/>
  <entry id="2259" name="RpcBattlePartnerCoMatchProposeRes" serverName="battlesvr"/>
  <entry id="2260" name="RpcBattlePartnerCoMatchConfirmReq" serverName="battlesvr"/>
  <entry id="2261" name="RpcBattlePartnerCoMatchConfirmRes" serverName="battlesvr"/>
  <entry id="2262" name="RpcBattlePartnerCoMatchInfoNtfReq" serverName="gamesvr"/>
  <entry id="2263" name="RpcArenaSavePlayerSelectReq" serverName="arenasvr"/>
  <entry id="2264" name="RpcArenaSavePlayerSelectRes" serverName="arenasvr"/>
  <entry id="2265" name="RpcCheckActivityRedPointReq" serverName="activitysvr"/>
  <entry id="2274" name="RpcUgcAchievementConfigEditReq" serverName="ugcsvr"/>
  <entry id="2275" name="RpcUgcAchievementConfigEditRes" serverName="ugcsvr"/>
  <entry id="2276" name="RpcUgcAchievementConfigGetReq" serverName="ugcsvr"/>
  <entry id="2277" name="RpcUgcAchievementConfigGetRes" serverName="ugcsvr"/>
  <entry id="2278" name="RpcMallDemandSuccessNtfReq" serverName="gamesvr"/>
  <entry id="2281" name="ACTDanceOutfitGenerationReq" serverName="common_proto"/>
  <entry id="2283" name="ACTDanceOutfitChangeReq" serverName="common_proto"/>
  <entry id="2285" name="ACTDanceOutfitCancelReq" serverName="common_proto"/>
  <entry id="2291" name="RpcGetShowDataReq" serverName="ugcdatastoresvr"/>
  <entry id="2295" name="RpcGetShowDataRes" serverName="ugcdatastoresvr"/>
  <entry id="2296" name="RpcEditShowDataReq" serverName="ugcdatastoresvr"/>
  <entry id="2297" name="RpcEditShowDataRes" serverName="ugcdatastoresvr"/>
  <entry id="2301" name="ACTDanceOutfitCollectReq" serverName="common_proto"/>
  <entry id="2305" name="RpcCoMatchPlayerReadyReq" serverName="roomsvr"/>
  <entry id="2306" name="RpcCoMatchPlayerReadyRes" serverName="roomsvr"/>
  <entry id="2311" name="RpcRoomMarkCoMatchReq" serverName="roomsvr"/>
  <entry id="2312" name="RpcRoomStateChangeByCoMatchReq" serverName="roomsvr"/>
  <entry id="2313" name="RpcRoomStateChangeByCoMatchRes" serverName="roomsvr"/>
  <entry id="2314" name="RpcDsActivityInfoUpdateNtfReq" serverName="gamesvr"/>
  <entry id="2323" name="RpcCheckKeyWordLockReq" serverName="gamesvr"/>
  <entry id="2324" name="RpcCheckKeyWordLockRes" serverName="gamesvr"/>
  <entry id="2325" name="RpcUnlockCardReq" serverName="gamesvr"/>
  <entry id="2327" name="RpcUnlockCardRes" serverName="gamesvr"/>
  <entry id="2328" name="RpcCheckCardLockReq" serverName="gamesvr"/>
  <entry id="2329" name="RpcCheckCardLockRes" serverName="gamesvr"/>
  <entry id="2330" name="RpcRoomJoinMatchUnitReq" serverName="roomsvr"/>
  <entry id="2331" name="RpcRoomJoinMatchUnitRes" serverName="roomsvr"/>
  <entry id="2332" name="RpcAddItemReq" serverName="gamesvr"/>
  <entry id="2333" name="RpcAddItemRes" serverName="gamesvr"/>
  <entry id="2334" name="RpcAddPrayerCardReq" serverName="gamesvr"/>
  <entry id="2335" name="RpcAddPrayerCardRes" serverName="gamesvr"/>
  <entry id="2336" name="RpcWolfKillReportCommunicateReq" serverName="gamesvr"/>
  <entry id="2337" name="RpcWolfKillBeReportCommunicateRes" serverName="gamesvr"/>
  <entry id="2338" name="RpcWolfKillReportPassiveReq" serverName="gamesvr"/>
  <entry id="2339" name="RpcWolfKillBeReportPassiveRes" serverName="gamesvr"/>
  <entry id="2340" name="RpcWolfKillBeReportCommunicateReq" serverName="gamesvr"/>
  <entry id="2341" name="RpcWolfKillBeReportPassiveReq" serverName="gamesvr"/>
  <entry id="2342" name="RpcUgcGetHallOfFameMapListReq" serverName="ugcplatsvr"/>
  <entry id="2343" name="RpcUgcGetHallOfFameMapListRes" serverName="ugcplatsvr"/>
  <entry id="2344" name="RpcArenaUpdateCombatEffectivenessReq" serverName="arenasvr"/>
  <entry id="2345" name="RpcArenaUpdateCombatEffectivenessRes" serverName="arenasvr"/>
  <entry id="2346" name="RpcGetRecommendSetReq" serverName="ugcplatsvr"/>
  <entry id="2347" name="RpcGetRecommendSetRes" serverName="ugcplatsvr"/>
  <entry id="2348" name="RpcEnterLevelNtfReq" serverName="gamesvr"/>
  <entry id="2349" name="RpcEnterLevelNtfRes" serverName="gamesvr"/>
  <entry id="2350" name="RpcGetShowDataSnapshotReq" serverName="ugcdatastoresvr"/>
  <entry id="2351" name="RpcGetShowDataSnapshotRes" serverName="ugcdatastoresvr"/>
  <entry id="2400" name="RpcSyncInfoNtfReq" serverName="farmsvr"/>
  <entry id="2401" name="RpcEndEventNtfReq" serverName="farmsvr"/>
  <entry id="2551" name="RpcFarmDispatchEventCondNtfReq" serverName="gamesvr"/>
  <entry id="2552" name="RpcTriggerEventNtfReq" serverName="farmsvr"/>
  <entry id="2553" name="RpcFarmClearMutexIDNtfReq" serverName="gamesvr"/>
  <entry id="2554" name="GmUgcUpdatePlayerDataReq" serverName="ugcsvr"/>
  <entry id="2555" name="GmUgcUpdatePlayerDataRes" serverName="ugcsvr"/>
  <entry id="2556" name="RpcTextTssCheckReq" serverName="gamesvr"/>
  <entry id="2557" name="RpcTextTssCheckRes" serverName="gamesvr"/>
  <entry id="2558" name="RpcUgcCommunityReq" serverName="ugcplatsvr"/>
  <entry id="2559" name="RpcUgcCommunityRes" serverName="ugcplatsvr"/>
  <entry id="2560" name="RpcUgcPlayerCommunityReq" serverName="ugcplatsvr"/>
  <entry id="2561" name="RpcUgcPlayerCommunityRes" serverName="ugcplatsvr"/>
  <entry id="2562" name="RpcBattleConfirmEnterReq" serverName="battlesvr"/>
  <entry id="2563" name="RpcBattleConfirmEnterRes" serverName="battlesvr"/>
  <entry id="2564" name="RpcStarPKickPlayerNtfReq" serverName="battlesvr"/>
  <entry id="2565" name="RpcStarPStartGVGMatchReq" serverName="starpmatchsvr"/>
  <entry id="2566" name="RpcStarPStartGVGMatchRes" serverName="starpmatchsvr"/>
  <entry id="2567" name="RpcStarPCancelGVGMatchReq" serverName="starpmatchsvr"/>
  <entry id="2568" name="RpcStarPCancelGVGMatchRes" serverName="starpmatchsvr"/>
  <entry id="2569" name="RpcStarPGVGDataSyncReq" serverName="starpmatchsvr"/>
  <entry id="2570" name="RpcStarPGVGDataSyncRes" serverName="starpmatchsvr"/>
  <entry id="2571" name="RpcTestStarpRoomMatchReq" serverName="starproommatchsvr"/>
  <entry id="2572" name="RpcTestStarpRoomMatchRes" serverName="starproommatchsvr"/>
  <entry id="2573" name="RpcStarPDsCommonDBSaveReq" serverName="dsdbsvr"/>
  <entry id="2574" name="RpcStarPDsCommonDBSaveRes" serverName="dsdbsvr"/>
  <entry id="2575" name="RpcStarPDsCommonDBGetByPartKeyReq" serverName="dsdbsvr"/>
  <entry id="2576" name="RpcStarPDsCommonDBGetByPartKeyRes" serverName="dsdbsvr"/>
  <entry id="2577" name="RpcStarPGetCommonDBPlayerReq" serverName="dsdbsvr"/>
  <entry id="2578" name="RpcStarPGetCommonDBPlayerRes" serverName="dsdbsvr"/>
  <entry id="2579" name="RpcStarPWorldCreateReq" serverName="starpsvr"/>
  <entry id="2580" name="RpcStarPWorldCreateRes" serverName="starpsvr"/>
  <entry id="2581" name="RpcStarPApplyAdminRoleReq" serverName="starpsvr"/>
  <entry id="2582" name="RpcStarPApplyAdminRoleRes" serverName="starpsvr"/>
  <entry id="2583" name="RpcStarPWorldApplyReq" serverName="starpsvr"/>
  <entry id="2584" name="RpcStarPWorldApplyRes" serverName="starpsvr"/>
  <entry id="2585" name="RpcStarPPublishReq" serverName="starpsvr"/>
  <entry id="2586" name="RpcStarPPublishRes" serverName="starpsvr"/>
  <entry id="2587" name="RpcStarPOpApplyReq" serverName="starpsvr"/>
  <entry id="2588" name="RpcStarPOpApplyRes" serverName="starpsvr"/>
  <entry id="2589" name="RpcGetStarPListReq" serverName="starpsvr"/>
  <entry id="2590" name="RpcGetStarPListRes" serverName="starpsvr"/>
  <entry id="2591" name="RpcGetStarPNearbyListReq" serverName="starpsvr"/>
  <entry id="2592" name="RpcGetStarPNearbyListRes" serverName="starpsvr"/>
  <entry id="2593" name="RpcStarPModifyRoomReq" serverName="starpsvr"/>
  <entry id="2594" name="RpcStarPModifyRoomRes" serverName="starpsvr"/>
  <entry id="2595" name="RpcStarPBanEnterReq" serverName="starpsvr"/>
  <entry id="2596" name="RpcStarPBanEnterRes" serverName="starpsvr"/>
  <entry id="2597" name="RpcStarPDelUserReq" serverName="starpsvr"/>
  <entry id="2598" name="RpcStarPDelUserRes" serverName="starpsvr"/>
  <entry id="2599" name="RpcStarPDelRoleByselfReq" serverName="starpsvr"/>
  <entry id="2600" name="RpcStarPDelRoleByselfRes" serverName="starpsvr"/>
  <entry id="2601" name="RpcStarPAdminTransferReq" serverName="starpsvr"/>
  <entry id="2602" name="RpcStarPAdminTransferRes" serverName="starpsvr"/>
  <entry id="2603" name="RpcStarPWorldEnterReq" serverName="starpsvr"/>
  <entry id="2604" name="RpcStarPWorldEnterRes" serverName="starpsvr"/>
  <entry id="2605" name="RpcStarPWorldExitReq" serverName="starpsvr"/>
  <entry id="2606" name="RpcStarPWorldExitRes" serverName="starpsvr"/>
  <entry id="2607" name="RpcStarPGetStarPAttrReq" serverName="starpsvr"/>
  <entry id="2608" name="RpcStarPGetStarPAttrRes" serverName="starpsvr"/>
  <entry id="2609" name="RpcStarPGetStarPPublicInfoReq" serverName="starpsvr"/>
  <entry id="2610" name="RpcStarPGetStarPPublicInfoRes" serverName="starpsvr"/>
  <entry id="2611" name="RpcStarPVoteForDelUserReq" serverName="starpsvr"/>
  <entry id="2612" name="RpcStarPVoteForDelUserRes" serverName="starpsvr"/>
  <entry id="2613" name="RpcStarPGetTeamUserInfoReq" serverName="starpsvr"/>
  <entry id="2614" name="RpcStarPGetTeamUserInfoRes" serverName="starpsvr"/>
  <entry id="2615" name="RpcStarPInviteReq" serverName="starpsvr"/>
  <entry id="2616" name="RpcStarPInviteRes" serverName="starpsvr"/>
  <entry id="2617" name="RpcStarPRejectInviteReq" serverName="starpsvr"/>
  <entry id="2618" name="RpcStarPRejectInviteRes" serverName="starpsvr"/>
  <entry id="2619" name="RpcStarPGetOngoingBattleReq" serverName="starpsvr"/>
  <entry id="2620" name="RpcStarPGetOngoingBattleRes" serverName="starpsvr"/>
  <entry id="2621" name="RpcStarPCheckUserIsOnlineReq" serverName="starpsvr"/>
  <entry id="2622" name="RpcStarPCheckUserIsOnlineRes" serverName="starpsvr"/>
  <entry id="2623" name="RpcStarPGetPlayerWorldInfoReq" serverName="starpsvr"/>
  <entry id="2624" name="RpcStarPGetPlayerWorldInfoRes" serverName="starpsvr"/>
  <entry id="2625" name="RpcStarPTeamChangeReq" serverName="starpsvr"/>
  <entry id="2626" name="RpcStarPTeamChangeRes" serverName="starpsvr"/>
  <entry id="2627" name="RpcStarPGiveUpGameReq" serverName="starpsvr"/>
  <entry id="2628" name="RpcIdipModifyDataReq" serverName="starpsvr"/>
  <entry id="2629" name="RpcIdipModifyDataRes" serverName="starpsvr"/>
  <entry id="2630" name="RpcIdipCheckStarPDsOnlineReq" serverName="starpsvr"/>
  <entry id="2631" name="RpcIdipCheckStarPDsOnlineRes" serverName="starpsvr"/>
  <entry id="2632" name="RpcIdipAddMailReq" serverName="starpsvr"/>
  <entry id="2633" name="RpcIdipAddMailRes" serverName="starpsvr"/>
  <entry id="2634" name="RpcGetStarPOfficialListReq" serverName="starpsvr"/>
  <entry id="2635" name="RpcGetStarPOfficialListRes" serverName="starpsvr"/>
  <entry id="2636" name="RpcGetQuickEnterStarPOfficialListReq" serverName="starpsvr"/>
  <entry id="2637" name="RpcGetQuickEnterStarPOfficialListRes" serverName="starpsvr"/>
  <entry id="2638" name="RpcStarPGmHookReq" serverName="starpsvr"/>
  <entry id="2639" name="RpcStarPGmHookRes" serverName="starpsvr"/>
  <entry id="2640" name="RpcStarPVisitorGetAvailSpReq" serverName="starpsvr"/>
  <entry id="2641" name="RpcStarPVisitorGetAvailSpRes" serverName="starpsvr"/>
  <entry id="2642" name="RpcRoomConfirmEnterNtfReq" serverName="gamesvr"/>
  <entry id="2643" name="RpcRoomConfirmEnterNtfRes" serverName="gamesvr"/>
  <entry id="2644" name="RpcStarPDsInfoNtfReq" serverName="gamesvr"/>
  <entry id="2645" name="RpcStarPDsInfoNtfRes" serverName="gamesvr"/>
  <entry id="2646" name="RpcStarPPlayerNtfReq" serverName="gamesvr"/>
  <entry id="2647" name="RpcStarPPlayerNtfRes" serverName="gamesvr"/>
  <entry id="2648" name="RpcStarPApplyNtfReq" serverName="gamesvr"/>
  <entry id="2649" name="RpcStarPApplyResultNtfReq" serverName="gamesvr"/>
  <entry id="2650" name="RpcStarPAblerToApplyAdminNtfReq" serverName="gamesvr"/>
  <entry id="2651" name="RpcStarPPrepareMigrateDsNtfReq" serverName="gamesvr"/>
  <entry id="2652" name="RpcStarPAttrNtfReq" serverName="gamesvr"/>
  <entry id="2653" name="RpcStarPVoteForDelUserNtfReq" serverName="gamesvr"/>
  <entry id="2654" name="RpcStarPUpdateUserInfoReq" serverName="gamesvr"/>
  <entry id="2655" name="RpcStarPUpdateUserInfoRes" serverName="gamesvr"/>
  <entry id="2656" name="RpcRoomConfirmEnterProgressNtfReq" serverName="gamesvr"/>
  <entry id="2657" name="RpcRoomConfirmEnterProgressNtfRes" serverName="gamesvr"/>
  <entry id="2658" name="RpcStarPEnterGameNtfReq" serverName="gamesvr"/>
  <entry id="2659" name="RpcStarPInviteEnterGameReq" serverName="gamesvr"/>
  <entry id="2660" name="RpcStarPInviteEnterGameAnswerNtfReq" serverName="gamesvr"/>
  <entry id="2661" name="RpcStarPInviteNtfReq" serverName="gamesvr"/>
  <entry id="2662" name="RpcAddplayerResultNtfReq" serverName="gamesvr"/>
  <entry id="2663" name="RpcStarPChatChannelNtfReq" serverName="gamesvr"/>
  <entry id="2664" name="RpcStarPChatGroupKeyNtfReq" serverName="gamesvr"/>
  <entry id="2665" name="RpcTouchedStarPModeNtfReq" serverName="gamesvr"/>
  <entry id="2666" name="RpcStarPExitDsNtfReq" serverName="gamesvr"/>
  <entry id="2667" name="RpcStarPEnterDsNtfReq" serverName="gamesvr"/>
  <entry id="2668" name="RpcStarPDSGetPlayerPublicInfoReq" serverName="gamesvr"/>
  <entry id="2669" name="RpcStarPDSGetPlayerPublicInfoRes" serverName="gamesvr"/>
  <entry id="2670" name="RpcReplaceDsCommonDbInfoReq" serverName="spdbsvr"/>
  <entry id="2671" name="RpcReplaceDsCommonDbInfoRes" serverName="spdbsvr"/>
  <entry id="2672" name="RpcGetGlobalAchieveDataReq" serverName="spdbsvr"/>
  <entry id="2673" name="RpcGetGlobalAchieveDataRes" serverName="spdbsvr"/>
  <entry id="2674" name="RpcLoadDsCommonDbInfoReq" serverName="spdbsvr"/>
  <entry id="2675" name="RpcLoadDsCommonDbInfoRes" serverName="spdbsvr"/>
  <entry id="2676" name="RpcLoadDsCommonDbAccountLevelInfoReq" serverName="spdbsvr"/>
  <entry id="2677" name="RpcLoadDsCommonDbAccountLevelInfoRes" serverName="spdbsvr"/>
  <entry id="2678" name="RpcStarPMatchSvrReportToMgrReq" serverName="starpmatchmgrsvr"/>
  <entry id="2679" name="RpcStarPMatchSvrReportToMgrRes" serverName="starpmatchmgrsvr"/>
  <entry id="2680" name="RpcConfirmEnterReq" serverName="roomsvr"/>
  <entry id="2681" name="RpcConfirmEnterRes" serverName="roomsvr"/>
  <entry id="2682" name="RpcRoomStarPInviteEnterGameReq" serverName="roomsvr"/>
  <entry id="2683" name="RpcRoomStarPInviteEnterGameRes" serverName="roomsvr"/>
  <entry id="2684" name="RpcRoomStarPConfirmProgressReq" serverName="roomsvr"/>
  <entry id="2685" name="RpcRoomStarPConfirmProgressRes" serverName="roomsvr"/>
  <entry id="2686" name="RpcGetGameSessionReq" serverName="dsrecoverysvr"/>
  <entry id="2687" name="RpcGetGameSessionRes" serverName="dsrecoverysvr"/>
  <entry id="2688" name="RpcLockCreateGameSessionReq" serverName="dsrecoverysvr"/>
  <entry id="2689" name="RpcLockCreateGameSessionRes" serverName="dsrecoverysvr"/>
  <entry id="2690" name="RpcModifySubscriberReq" serverName="dsrecoverysvr"/>
  <entry id="2691" name="RpcModifySubscriberRes" serverName="dsrecoverysvr"/>
  <entry id="2692" name="RpcMigrateGameSessionReq" serverName="dsrecoverysvr"/>
  <entry id="2693" name="RpcMigrateGameSessionRes" serverName="dsrecoverysvr"/>
  <entry id="2694" name="RpcGetStarPUserAttrReq" serverName="starpaccountsvr"/>
  <entry id="2695" name="RpcGetStarPUserAttrRes" serverName="starpaccountsvr"/>
  <entry id="2696" name="RpcStarPUserEnterReq" serverName="starpaccountsvr"/>
  <entry id="2697" name="RpcStarPUserEnterRes" serverName="starpaccountsvr"/>
  <entry id="2698" name="RpcTcpPlayerLogoutNtfReq" serverName="starpaccountsvr"/>
  <entry id="2699" name="RpcTcpPlayerLogoutNtfRes" serverName="starpaccountsvr"/>
  <entry id="2700" name="RpcStarPUpdateUserReq" serverName="starpaccountsvr"/>
  <entry id="2701" name="RpcStarPUpdateUserRes" serverName="starpaccountsvr"/>
  <entry id="2702" name="RpcStarPRecordApplyReq" serverName="starpaccountsvr"/>
  <entry id="2703" name="RpcStarPRecordApplyRes" serverName="starpaccountsvr"/>
  <entry id="2704" name="RpcStarPDelApplyReq" serverName="starpaccountsvr"/>
  <entry id="2705" name="RpcStarPDelApplyRes" serverName="starpaccountsvr"/>
  <entry id="2706" name="RpcStarPCheckUserReq" serverName="starpaccountsvr"/>
  <entry id="2707" name="RpcStarPCheckUserRes" serverName="starpaccountsvr"/>
  <entry id="2708" name="RpcStarPCheckAblerToApplyAdminReq" serverName="starpaccountsvr"/>
  <entry id="2709" name="RpcStarPCheckAblerToApplyAdminRes" serverName="starpaccountsvr"/>
  <entry id="2710" name="RpcStarPCheckUserNDayNotOnlineReq" serverName="starpaccountsvr"/>
  <entry id="2711" name="RpcStarPCheckUserNDayNotOnlineRes" serverName="starpaccountsvr"/>
  <entry id="2712" name="RpcStarUpdateInviteInfoReq" serverName="starpaccountsvr"/>
  <entry id="2713" name="RpcStarUpdateInviteInfoRes" serverName="starpaccountsvr"/>
  <entry id="2714" name="RpcStarPCheckUserCanBeDelReq" serverName="starpaccountsvr"/>
  <entry id="2715" name="RpcStarPCheckUserCanBeDelRes" serverName="starpaccountsvr"/>
  <entry id="2716" name="RpcStarPTryWorldCreateReq" serverName="starpaccountsvr"/>
  <entry id="2717" name="RpcStarPTryWorldCreateRes" serverName="starpaccountsvr"/>
  <entry id="2718" name="RpcStarPGetPlayerWorldInfosReq" serverName="starpaccountsvr"/>
  <entry id="2719" name="RpcStarPGetPlayerWorldInfosRes" serverName="starpaccountsvr"/>
  <entry id="2720" name="RpcEnterYuanMengNtfReq" serverName="starpaccountsvr"/>
  <entry id="2721" name="RpcExitYuanMengNtfReq" serverName="starpaccountsvr"/>
  <entry id="2722" name="RpcCheckExceptionChatGroupKeyNtfReq" serverName="starpaccountsvr"/>
  <entry id="2723" name="RpcIdipModifyLockReq" serverName="starpaccountsvr"/>
  <entry id="2724" name="RpcIdipModifyLockRes" serverName="starpaccountsvr"/>
  <entry id="2725" name="RpcCheckPlayerIsOnlineReq" serverName="starpaccountsvr"/>
  <entry id="2726" name="RpcCheckPlayerIsOnlineRes" serverName="starpaccountsvr"/>
  <entry id="2727" name="RpcLoadDsCommonDbInfoTableReq" serverName="starpaccountsvr"/>
  <entry id="2728" name="RpcLoadDsCommonDbInfoTableRes" serverName="starpaccountsvr"/>
  <entry id="2729" name="RpcStarPRoleStatusReq" serverName="starpaccountsvr"/>
  <entry id="2730" name="RpcStarPRoleStatusRes" serverName="starpaccountsvr"/>
  <entry id="2731" name="RpcPlayerInheritReq" serverName="starpaccountsvr"/>
  <entry id="2732" name="RpcPlayerInheritRes" serverName="starpaccountsvr"/>
  <entry id="2733" name="RpcStarPGetDressUpInfoReq" serverName="starpaccountsvr"/>
  <entry id="2734" name="RpcStarPGetDressUpInfoRes" serverName="starpaccountsvr"/>
  <entry id="2735" name="RpcStarPCreateDressUpReq" serverName="starpaccountsvr"/>
  <entry id="2736" name="RpcStarPCreateDressUpRes" serverName="starpaccountsvr"/>
  <entry id="2737" name="RpcStarPCreateDressUpFinishNtfReq" serverName="starpaccountsvr"/>
  <entry id="2738" name="RpcStarPSaveDressUpReq" serverName="starpaccountsvr"/>
  <entry id="2739" name="RpcStarPSaveDressUpRes" serverName="starpaccountsvr"/>
  <entry id="2740" name="RpcStarPOnChangeFittingSlotNtfReq" serverName="starpaccountsvr"/>
  <entry id="2741" name="RpcStarPAddVisitsNtfReq" serverName="starpaccountsvr"/>
  <entry id="2742" name="RpcStarPSetGuideStepReq" serverName="starpaccountsvr"/>
  <entry id="2743" name="RpcStarPSetGuideStepRes" serverName="starpaccountsvr"/>
  <entry id="2744" name="RpcStarPGetGuideStepReq" serverName="starpaccountsvr"/>
  <entry id="2745" name="RpcStarPGetGuideStepRes" serverName="starpaccountsvr"/>
  <entry id="2746" name="TestGetMessageReq" serverName="starpbattlesvr"/>
  <entry id="2747" name="TestGetMessageRes" serverName="starpbattlesvr"/>
  <entry id="2748" name="RpcStarPGmBattleReq" serverName="starpbattlesvr"/>
  <entry id="2749" name="RpcStarPGmBattleRes" serverName="starpbattlesvr"/>
  <entry id="2750" name="RpcDebugdsModifyReq" serverName="debugdsmgrsvr"/>
  <entry id="2751" name="RpcDebugdsModifyRes" serverName="debugdsmgrsvr"/>
  <entry id="2752" name="RpcTestRoomReq" serverName="starproomsvr"/>
  <entry id="2753" name="RpcTestRoomRes" serverName="starproomsvr"/>
  <entry id="2754" name="RpcRoomPlayerStarPInfoUpdateReq" serverName="starproomsvr"/>
  <entry id="2755" name="DsrDsEventReq" serverName="noserver"/>
  <entry id="2756" name="DsrDsEventRes" serverName="noserver"/>
  <entry id="2757" name="DsrCreateGameSessionResultReq" serverName="noserver"/>
  <entry id="2758" name="DsrCreateGameSessionResultRes" serverName="noserver"/>
  <entry id="2759" name="RpcReportPlayerDsOfflineNtfReq" serverName="noserver"/>
  <entry id="2760" name="RpcReportPlayerDsOfflineNtfRes" serverName="noserver"/>
  <entry id="2761" name="RpcBattlePartnerCoMatchResultSyncReq" serverName="battlesvr"/>
  <entry id="2762" name="RpcBattlePartnerCoMatchResultSyncRes" serverName="battlesvr"/>
  <entry id="2763" name="RpcRoomCoMatchInfoNtfReq" serverName="gamesvr"/>
  <entry id="2764" name="RpcRoomCoMatchProposeReq" serverName="roomsvr"/>
  <entry id="2765" name="RpcRoomCoMatchProposeRes" serverName="roomsvr"/>
  <entry id="2766" name="RpcRoomCoMatchConfirmReq" serverName="roomsvr"/>
  <entry id="2767" name="RpcRoomCoMatchConfirmRes" serverName="roomsvr"/>
  <entry id="2768" name="RpcStarPDelRoleReq" serverName="starpsvr"/>
  <entry id="2769" name="RpcStarPDelRoleRes" serverName="starpsvr"/>
  <entry id="2770" name="RpcGmStarPWorldApplyReq" serverName="starpsvr"/>
  <entry id="2771" name="RpcGmStarPWorldApplyRes" serverName="starpsvr"/>
  <entry id="2772" name="RpcStarpUpdateNearbyRoomReq" serverName="starpsvr"/>
  <entry id="2773" name="RpcStarpUpdateNearbyRoomRes" serverName="starpsvr"/>
  <entry id="2774" name="RpcNtfMailChangReq" serverName="starpsvr"/>
  <entry id="2775" name="RpcNtfMailChangRes" serverName="starpsvr"/>
  <entry id="2776" name="RpcStarGetRLInfoReq" serverName="starpsvr"/>
  <entry id="2777" name="RpcStarGetRLInfoRes" serverName="starpsvr"/>
  <entry id="2778" name="RpcStarPGMCmdReq" serverName="starpsvr"/>
  <entry id="2779" name="RpcStarPGMCmdRes" serverName="starpsvr"/>
  <entry id="2780" name="RpcSendMailReq" serverName="starpmailsvr"/>
  <entry id="2781" name="RpcSendMailRes" serverName="starpmailsvr"/>
  <entry id="2782" name="RpcStarpTryUpdateNearbyRoomReq" serverName="starpaccountsvr"/>
  <entry id="2783" name="RpcStarpTryUpdateNearbyRoomRes" serverName="starpaccountsvr"/>
  <entry id="2784" name="RpcStarPInheritFinishReq" serverName="starpaccountsvr"/>
  <entry id="2785" name="RpcStarPInheritFinishRes" serverName="starpaccountsvr"/>
  <entry id="2786" name="RpcStarPAccountGMCmdReq" serverName="starpaccountsvr"/>
  <entry id="2787" name="RpcStarPAccountGMCmdRes" serverName="starpaccountsvr"/>
  <entry id="2788" name="RpcStarPSetCheckLeaderOfflineTimeReq" serverName="starpaccountsvr"/>
  <entry id="2789" name="RpcStarPSetCheckLeaderOfflineTimeRes" serverName="starpaccountsvr"/>
  <entry id="2790" name="RpcStarPUpdateLeaderOfflineTimeReq" serverName="starpaccountsvr"/>
  <entry id="2791" name="RpcStarPUpdateLeaderOfflineTimeRes" serverName="starpaccountsvr"/>
  <entry id="2792" name="RpcUpdateUgcLayerNameReq" serverName="ugcsvr"/>
  <entry id="2793" name="RpcUpdateUgcLayerNameRes" serverName="ugcsvr"/>
  <entry id="2794" name="RpcGetItemCountListReq" serverName="gamesvr"/>
  <entry id="2795" name="RpcGetItemCountListRes" serverName="gamesvr"/>
  <entry id="2796" name="RpcFixFarmActiveItemsReq" serverName="farmsvr"/>
  <entry id="2797" name="RpcFixFarmActiveItemsRes" serverName="farmsvr"/>
  <entry id="2798" name="RpcStarPSetPreDelTimeReq" serverName="starpsvr"/>
  <entry id="2799" name="RpcStarPSetPreDelTimeRes" serverName="starpsvr"/>
  <entry id="2801" name="RpcFarmNPCFishNtfNtfReq" serverName="gamesvr"/>
  <entry id="2802" name="RpcFarmNPCFishNtfReq" serverName="gamesvr"/>
  <entry id="2803" name="RpcStarPVisitorGetFullInfoReq" serverName="starpsvr"/>
  <entry id="2804" name="RpcStarPVisitorGetFullInfoRes" serverName="starpsvr"/>
  <entry id="2805" name="RpcStarPLotterySendRewardReq" serverName="gamesvr"/>
  <entry id="2806" name="RpcStarPLotterySendRewardRes" serverName="gamesvr"/>
  <entry id="2807" name="RpcStarPLotteryReviseRewardReq" serverName="gamesvr"/>
  <entry id="2808" name="RpcStarPLotteryReviseRewardRes" serverName="gamesvr"/>
  <entry id="2809" name="RpcAdventureGetBaseInfoReq" serverName="starpgamesvr"/>
  <entry id="2810" name="RpcAdventureGetBaseInfoRes" serverName="starpgamesvr"/>
  <entry id="2811" name="RpcAdventureGetDayRewardReq" serverName="starpgamesvr"/>
  <entry id="2812" name="RpcAdventureGetDayRewardRes" serverName="starpgamesvr"/>
  <entry id="2813" name="StarPPlayerDoLotteryReq" serverName="starpgamesvr"/>
  <entry id="2814" name="StarPPlayerDoLotteryRes" serverName="starpgamesvr"/>
  <entry id="2815" name="StarPPlayerGetLotteryInfoReq" serverName="starpgamesvr"/>
  <entry id="2816" name="StarPPlayerGetLotteryInfoRes" serverName="starpgamesvr"/>
  <entry id="2817" name="RpcSaveDsCommonDbInfoReq" serverName="spdbsvr"/>
  <entry id="2818" name="RpcSaveDsCommonDbInfoRes" serverName="spdbsvr"/>
  <entry id="2819" name="RpcDeleteDsCommonDbInfoReq" serverName="spdbsvr"/>
  <entry id="2820" name="RpcDeleteDsCommonDbInfoRes" serverName="spdbsvr"/>
  <entry id="2821" name="RpcStarPStartRLBattleReq" serverName="starpbattlesvr"/>
  <entry id="2822" name="RpcStarPStartRLBattleRes" serverName="starpbattlesvr"/>
  <entry id="2823" name="RpcGMSPWorldDumpReq" serverName="debugdsmgrsvr"/>
  <entry id="2824" name="RpcGMSPWorldDumpRes" serverName="debugdsmgrsvr"/>
  <entry id="2825" name="RpcGMSPWorldLoadReq" serverName="debugdsmgrsvr"/>
  <entry id="2826" name="RpcGMSPWorldLoadRes" serverName="debugdsmgrsvr"/>
  <entry id="2827" name="FarmFriendSendGiftReq" serverName="activitysvr"/>
  <entry id="2828" name="FarmFriendSendGiftRes" serverName="activitysvr"/>
  <entry id="2829" name="RpcStarPGetFunctionControlReq" serverName="starpaccountsvr"/>
  <entry id="2830" name="RpcStarPGetFunctionControlRes" serverName="starpaccountsvr"/>
  <entry id="2832" name="RpcTrainingTrainObjectReq" serverName="activitysvr"/>
  <entry id="2833" name="RpcTrainingTrainObjectRes" serverName="activitysvr"/>
  <entry id="2834" name="RpcArenaGameInfoSyncNtfReq" serverName="gamesvr"/>
  <entry id="2835" name="QueryPlayerUgcExpReq" serverName="ugcsvr"/>
  <entry id="2836" name="QueryPlayerUgcExpRes" serverName="ugcsvr"/>
  <entry id="2837" name="GmDeleteUgcRepeatedNameReq" serverName="ugcsvr"/>
  <entry id="2838" name="GmDeleteUgcRepeatedNameRes" serverName="ugcsvr"/>
  <entry id="2841" name="RpcStarPGetLastSelfDelUserMessageReq" serverName="starpaccountsvr"/>
  <entry id="2842" name="RpcStarPGetLastSelfDelUserMessageRes" serverName="starpaccountsvr"/>
  <entry id="2843" name="RpcStarPSetLastSelfDelUserMessageReq" serverName="starpaccountsvr"/>
  <entry id="2844" name="RpcStarPSetLastSelfDelUserMessageRes" serverName="starpaccountsvr"/>
  <entry id="2845" name="RpcArenaGMCmdReq" serverName="arenasvr"/>
  <entry id="2846" name="RpcArenaGMCmdRes" serverName="arenasvr"/>
  <entry id="2849" name="RpcArenaStatAddNtfReq" serverName="arenasvr"/>
  <entry id="2851" name="RpcArenaLoginProcessNtfReq" serverName="arenasvr"/>
  <entry id="2852" name="RpcArenaUnlockSkinNtfReq" serverName="arenasvr"/>
  <entry id="2857" name="RpcInhertPlayerCommonDbInfoRes" serverName="starpaccountsvr"/>
  <entry id="2858" name="RpcStarPGetAllRolePetInfoReq" serverName="starpaccountsvr"/>
  <entry id="2859" name="RpcStarPGetAllRolePetInfoRes" serverName="starpaccountsvr"/>
  <entry id="2860" name="RpcStarPGetPetFormationReq" serverName="starpaccountsvr"/>
  <entry id="2861" name="RpcStarPGetPetFormationRes" serverName="starpaccountsvr"/>
  <entry id="2862" name="RpcStarPSetPetFormationReq" serverName="starpaccountsvr"/>
  <entry id="2863" name="RpcStarPSetPetFormationRes" serverName="starpaccountsvr"/>
  <entry id="2864" name="RpcStarPGetOrnamentAndAmuletInfoReq" serverName="starpaccountsvr"/>
  <entry id="2865" name="RpcStarPGetOrnamentAndAmuletInfoRes" serverName="starpaccountsvr"/>
  <entry id="2866" name="RpcClubResetLabelsReq" serverName="clubsvr"/>
  <entry id="2867" name="RpcClubResetLabelsRes" serverName="clubsvr"/>
  <entry id="2870" name="RpcTaskConditionNtfReq" serverName="farmsvr"/>
  <entry id="2872" name="RpcUgcCoverCheckLimitReq" serverName="ugcsvr"/>
  <entry id="2873" name="RpcUgcCoverCheckLimitRes" serverName="ugcsvr"/>
  <entry id="2874" name="RpcUgcUploadCoverInfoReq" serverName="ugcsvr"/>
  <entry id="2875" name="RpcUgcUploadCoverInfoRes" serverName="ugcsvr"/>
  <entry id="2876" name="RpcUgcUploadCoverNotifyReq" serverName="ugcsvr"/>
  <entry id="2877" name="RpcUgcUploadCoverNotifyRes" serverName="ugcsvr"/>
  <entry id="2878" name="RpcCookCreateReq" serverName="farmsvr"/>
  <entry id="2879" name="RpcCookCreateRes" serverName="farmsvr"/>
  <entry id="2880" name="RpcCookEnterReq" serverName="farmsvr"/>
  <entry id="2881" name="RpcCookEnterRes" serverName="farmsvr"/>
  <entry id="2882" name="RpcCookExitReq" serverName="farmsvr"/>
  <entry id="2883" name="RpcCookExitRes" serverName="farmsvr"/>
  <entry id="2884" name="RpcCookHeartBeatReq" serverName="farmsvr"/>
  <entry id="2885" name="RpcCookHeartBeatRes" serverName="farmsvr"/>
  <entry id="2886" name="RpcCookOwnerLoginLogoutReq" serverName="farmsvr"/>
  <entry id="2887" name="RpcCookOwnerLoginLogoutRes" serverName="farmsvr"/>
  <entry id="2888" name="RpcCookSyncPlayerInfoReq" serverName="farmsvr"/>
  <entry id="2889" name="RpcCookSyncPlayerInfoRes" serverName="farmsvr"/>
  <entry id="2890" name="RpcCookHireFinishReq" serverName="farmsvr"/>
  <entry id="2891" name="RpcCookHireFinishRes" serverName="farmsvr"/>
  <entry id="2892" name="RpcCookKickNtfReq" serverName="gamesvr"/>
  <entry id="2893" name="RpcArenaGetProfileTagReq" serverName="ugcplatsvr"/>
  <entry id="2894" name="RpcArenaGetProfileTagRes" serverName="ugcplatsvr"/>
  <entry id="2901" name="RpcTradingCardInteractionReq" serverName="gamesvr"/>
  <entry id="2902" name="RpcTradingCardInteractionRes" serverName="gamesvr"/>
  <entry id="2904" name="RpcCommitItemToCloudReq" serverName="farmsvr"/>
  <entry id="2905" name="RpcCommitItemToCloudRes" serverName="farmsvr"/>
  <entry id="2910" name="RpcStarPUpdateMoeMessageReq" serverName="starpaccountsvr"/>
  <entry id="2911" name="RpcStarPUpdateMoeMessageRes" serverName="starpaccountsvr"/>
  <entry id="2912" name="RpcStarPWorldAdminMoeMessageChangeReq" serverName="starpsvr"/>
  <entry id="2913" name="RpcStarPWorldAdminMoeMessageChangeRes" serverName="starpsvr"/>
  <entry id="2917" name="TestGuildMessageRes" serverName="starpguildsvr"/>
  <entry id="2918" name="TestGuildMessageReq" serverName="starpguildsvr"/>
  <entry id="2919" name="RpcStarPPlayerShopBuyReq" serverName="starpgamesvr"/>
  <entry id="2920" name="RpcStarPPlayerShopBuyRes" serverName="starpgamesvr"/>
  <entry id="2921" name="RpcStarPPlayerShopInfoReq" serverName="starpgamesvr"/>
  <entry id="2922" name="RpcStarPPlayerShopInfoRes" serverName="starpgamesvr"/>
  <entry id="2923" name="RpcStarPGetMoeMessageReq" serverName="starpaccountsvr"/>
  <entry id="2924" name="RpcStarPGetMoeMessageRes" serverName="starpaccountsvr"/>
  <entry id="2925" name="RpcCookVisitantStealNtfReq" serverName="farmsvr"/>
  <entry id="2926" name="RpcSetMoodPosReq" serverName="arenasvr"/>
  <entry id="2927" name="RpcSetMoodPosRes" serverName="arenasvr"/>
  <entry id="2930" name="RpcGetTestUgcListInfoReq" serverName="ugcsvr"/>
  <entry id="2931" name="RpcGetTestUgcListInfoRes" serverName="ugcsvr"/>
  <entry id="2936" name="RpcStarPGetVisitsReq" serverName="starpaccountsvr"/>
  <entry id="2937" name="RpcStarPGetVisitsRes" serverName="starpaccountsvr"/>
  <entry id="2938" name="RpcStarPVisitSameWorldReq" serverName="starpsvr"/>
  <entry id="2939" name="RpcStarPVisitSameWorldRes" serverName="starpsvr"/>
  <entry id="2940" name="AddItemReq" serverName="cocsvr"/>
  <entry id="2941" name="AddItemRes" serverName="cocsvr"/>
  <entry id="2942" name="CocPushPlayerMsgNonBlockingReq" serverName="gamesvr"/>
  <entry id="2943" name="CocPushPlayerMsgNonBlockingRes" serverName="gamesvr"/>
  <entry id="2944" name="CocPushPlayerMsgBlockingReq" serverName="gamesvr"/>
  <entry id="2945" name="CocPushPlayerMsgBlockingRes" serverName="gamesvr"/>
  <entry id="2946" name="CocSyncPlayerStatusReq" serverName="gamesvr"/>
  <entry id="2947" name="CocSyncPlayerStatusRes" serverName="gamesvr"/>
  <entry id="2948" name="CocProsperityValueChangeNotifyReq" serverName="gamesvr"/>
  <entry id="2949" name="CocProsperityValueChangeNotifyRes" serverName="gamesvr"/>
  <entry id="2950" name="CocCupsScoreChangeNotifyReq" serverName="gamesvr"/>
  <entry id="2951" name="LocalServiceCallReq" serverName="noserver"/>
  <entry id="2952" name="LocalServiceCallRes" serverName="noserver"/>
  <entry id="2953" name="CocCupsScoreChangeNotifyRes" serverName="gamesvr"/>
  <entry id="2954" name="GetPlayerAllFriendUidReq" serverName="gamesvr"/>
  <entry id="2955" name="GetPlayerAllFriendUidRes" serverName="gamesvr"/>
  <entry id="2956" name="CocFriendGiftQueryReq" serverName="gamesvr"/>
  <entry id="2957" name="CocFriendGiftQueryRes" serverName="gamesvr"/>
  <entry id="2960" name="RpcChatGroupSendSystemMessageReq" serverName="starpsvr"/>
  <entry id="2961" name="RpcChatGroupSendSystemMessageRes" serverName="starpsvr"/>
  <entry id="2964" name="RpcStarPVerifyGuildNameReq" serverName="gamesvr"/>
  <entry id="2965" name="RpcStarPVerifyGuildNameRes" serverName="gamesvr"/>
  <entry id="2966" name="RpcStarPDsReleaseGuildNameReq" serverName="gamesvr"/>
  <entry id="2967" name="RpcStarPDsReleaseGuildNameRes" serverName="gamesvr"/>
  <entry id="2968" name="RpcFarmCreateRedPacketReq" serverName="farmsvr"/>
  <entry id="2969" name="RpcFarmCreateRedPacketRes" serverName="farmsvr"/>
  <entry id="2970" name="RpcFarmOpenRedPacketReq" serverName="farmsvr"/>
  <entry id="2971" name="RpcFarmOpenRedPacketRes" serverName="farmsvr"/>
  <entry id="2972" name="RpcStarPUpdateGuildInfoReq" serverName="starpaccountsvr"/>
  <entry id="2973" name="RpcStarPUpdateGuildInfoRes" serverName="starpaccountsvr"/>
  <entry id="2974" name="RpcQueryCardTradeInfoReq" serverName="gamesvr"/>
  <entry id="2975" name="RpcQueryCardTradeInfoRes" serverName="gamesvr"/>
  <entry id="2977" name="RpcReceiveTradingCardReq" serverName="gamesvr"/>
  <entry id="2978" name="RpcReceiveTradingCardRes" serverName="gamesvr"/>
  <entry id="2979" name="RpcStarPCreateRoleReq" serverName="starpaccountsvr"/>
  <entry id="2980" name="RpcStarPCreateRoleRes" serverName="starpaccountsvr"/>
  <entry id="2983" name="RpcUgcJoinCocreateReq" serverName="ugcappsvr"/>
  <entry id="2984" name="RpcUgcJoinCocreateRes" serverName="ugcappsvr"/>
  <entry id="2986" name="RpcGameMatchResultReq" serverName="gamesvr"/>
  <entry id="2987" name="RpcGameMatchResultRes" serverName="gamesvr"/>
  <entry id="2988" name="RpcGameMatchCancelResultReq" serverName="gamesvr"/>
  <entry id="2989" name="RpcGameMatchCancelResultRes" serverName="gamesvr"/>
  <entry id="2998" name="RpcApiGetPlayerPublicInfoReq" serverName="api_gamesvr"/>
  <entry id="2999" name="RpcApiGetPlayerPublicInfoRes" serverName="api_gamesvr"/>
  <entry id="3001" name="RpcStarPDeleteRoleReq" serverName="starpaccountsvr"/>
  <entry id="3002" name="RpcStarPDeleteRoleRes" serverName="starpaccountsvr"/>
  <entry id="3004" name="RpcAigcNpcModifyPalReq" serverName="aigcsvr"/>
  <entry id="3005" name="RpcAigcNpcModifyPalRes" serverName="aigcsvr"/>
  <entry id="3006" name="RpcFindTestUgcListInfoReq" serverName="ugcsvr"/>
  <entry id="3007" name="RpcFindTestUgcListInfoRes" serverName="ugcsvr"/>
  <entry id="3008" name="RpcApiSubGameplayEventReq" serverName="api_gamesvr"/>
  <entry id="3009" name="RpcApiSubGameplayEventRes" serverName="api_gamesvr"/>
  <entry id="3010" name="RpcFarmLeaveHotSpringNtfReq" serverName="farmsvr"/>
  <entry id="3013" name="RpcMapInitiateEvaluationReq" serverName="ugcsvr"/>
  <entry id="3014" name="RpcMapInitiateEvaluationRes" serverName="ugcsvr"/>
  <entry id="3015" name="RpcStarPGetLastRoleReq" serverName="starpaccountsvr"/>
  <entry id="3016" name="RpcStarPGetLastRoleRes" serverName="starpaccountsvr"/>
  <entry id="3021" name="RpcFarmExternalOperateReq" serverName="farmsvr"/>
  <entry id="3022" name="RpcFarmExternalOperateRes" serverName="farmsvr"/>
  <entry id="3023" name="RpcFarmGetHotSpringBuffNtfReq" serverName="farmsvr"/>
  <entry id="3025" name="RpcXiaoWoSetVisitorListTagReq" serverName="xiaowosvr"/>
  <entry id="3026" name="RpcXiaoWoSetVisitorListTagRes" serverName="xiaowosvr"/>
  <entry id="3027" name="RpcESCreateReq" serverName="asyncsvr"/>
  <entry id="3028" name="RpcESCreateRes" serverName="asyncsvr"/>
  <entry id="3029" name="RpcESUpdateReq" serverName="asyncsvr"/>
  <entry id="3030" name="RpcESUpdateRes" serverName="asyncsvr"/>
  <entry id="3031" name="RpcESSearchReq" serverName="asyncsvr"/>
  <entry id="3032" name="RpcESSearchRes" serverName="asyncsvr"/>
  <entry id="3033" name="RpcESDeleteReq" serverName="asyncsvr"/>
  <entry id="3034" name="RpcESDeleteRes" serverName="asyncsvr"/>
  <entry id="3037" name="RpcStarPGetPetDexReq" serverName="starpaccountsvr"/>
  <entry id="3038" name="RpcStarPGetPetDexRes" serverName="starpaccountsvr"/>
  <entry id="3039" name="RpcStarPGetAchievementDataReq" serverName="starpaccountsvr"/>
  <entry id="3040" name="RpcStarPGetAchievementDataRes" serverName="starpaccountsvr"/>
  <entry id="3041" name="RpcTestConfigSvrReq" serverName="configsvr"/>
  <entry id="3042" name="RpcTestConfigSvrRes" serverName="configsvr"/>
  <entry id="3047" name="RpcGmModifyAdventureInfoReq" serverName="starpgamesvr"/>
  <entry id="3048" name="RpcGmModifyAdventureInfoRes" serverName="starpgamesvr"/>
  <entry id="3051" name="RpcLobbyFindPartnerAllocReq" serverName="lobbysvr"/>
  <entry id="3052" name="RpcLobbyFindPartnerAllocRes" serverName="lobbysvr"/>
  <entry id="3056" name="RpcExtraConfigEditReq" serverName="ugcsvr"/>
  <entry id="3057" name="RpcExtraConfigEditRes" serverName="ugcsvr"/>
  <entry id="3058" name="RpcExtraConfigGetReq" serverName="ugcsvr"/>
  <entry id="3059" name="RpcExtraConfigGetRes" serverName="ugcsvr"/>
  <entry id="3060" name="RpcFarmEnterHotSpringNtfReq" serverName="farmsvr"/>
  <entry id="3063" name="RpcCocEventNtfReq" serverName="gamesvr"/>
  <entry id="3064" name="RpcBatchGetPlayerInfoReq" serverName="starpsvr"/>
  <entry id="3065" name="RpcBatchGetPlayerInfoRes" serverName="starpsvr"/>
  <entry id="3066" name="RpcTerminateLobbyReq" serverName="lobbysvr"/>
  <entry id="3067" name="RpcTerminateLobbyRes" serverName="lobbysvr"/>
  <entry id="3070" name="RpcUgcCommunityOpReportReq" serverName="gamesvr"/>
  <entry id="3071" name="RpcUgcCommunityOpReportRes" serverName="gamesvr"/>
  <entry id="3072" name="RpcStarPRoleChatInfoNtfReq" serverName="gamesvr"/>
  <entry id="3073" name="RpcStarPSynchronizeReq" serverName="starpsvr"/>
  <entry id="3074" name="RpcStarPSynchronizeRes" serverName="starpsvr"/>
  <entry id="3075" name="CocSyncPlayerAttrSnap2GsReq" serverName="gamesvr"/>
  <entry id="3076" name="CocSyncPlayerAttrSnap2GsRes" serverName="gamesvr"/>
  <entry id="3077" name="TestGroupMessageReq" serverName="starpgroupsvr"/>
  <entry id="3078" name="TestGroupMessageRes" serverName="starpgroupsvr"/>
  <entry id="3079" name="RpcStarPUpdateGroupInfoReq" serverName="starpaccountsvr"/>
  <entry id="3080" name="RpcStarPUpdateGroupInfoRes" serverName="starpaccountsvr"/>
  <entry id="3081" name="RpcStarPVerifyGroupNameReq" serverName="gamesvr"/>
  <entry id="3082" name="RpcStarPVerifyGroupNameRes" serverName="gamesvr"/>
  <entry id="3083" name="RpcStarPDsReleaseGroupNameReq" serverName="gamesvr"/>
  <entry id="3084" name="RpcStarPDsReleaseGroupNameRes" serverName="gamesvr"/>
  <entry id="3085" name="RpcFarmHotSpringAchievementNtfReq" serverName="gamesvr"/>
  <entry id="3086" name="RpcChatCurInfoNtfReq" serverName="gamesvr"/>
  <entry id="3089" name="RpcNotifyStreamInteractionCmdReq" serverName="gamesvr"/>
  <entry id="3090" name="RpcNotifyStreamInteractionCmdRes" serverName="gamesvr"/>
  <entry id="3091" name="RpcGeneralForwardCsNtfReq" serverName="api_gamesvr"/>
  <entry id="3092" name="RpcStarPRoleSelectReq" serverName="starpaccountsvr"/>
  <entry id="3093" name="RpcStarPRoleSelectRes" serverName="starpaccountsvr"/>
  <entry id="3094" name="RpcStarPGetRoleListReq" serverName="starpaccountsvr"/>
  <entry id="3095" name="RpcStarPGetRoleListRes" serverName="starpaccountsvr"/>
  <entry id="3098" name="RpcFarmHotSpringTickNtfReq" serverName="gamesvr"/>
  <entry id="3099" name="RpcSetSprayPaintReq" serverName="arenasvr"/>
  <entry id="3100" name="RpcSetSprayPaintRes" serverName="arenasvr"/>
  <entry id="3101" name="RpcMapUgcBaseInfoBatchReq" serverName="ugcsvr"/>
  <entry id="3102" name="RpcMapUgcBaseInfoBatchRes" serverName="ugcsvr"/>
  <entry id="3103" name="RpcFarmVisitorNtfReq" serverName="farmsvr"/>
  <entry id="3105" name="RpcStarPUpdateUserOneWayReq" serverName="starpaccountsvr"/>
  <entry id="3108" name="RpcStarPGetAllStarPWorldInfoReq" serverName="starpaccountsvr"/>
  <entry id="3109" name="RpcStarPGetAllStarPWorldInfoRes" serverName="starpaccountsvr"/>
  <entry id="3112" name="RpcStarPCheckAblerToApplyAdminFlowReq" serverName="starpaccountsvr"/>
  <entry id="3114" name="NtfPlayerSupportSuccessReq" serverName="gamesvr"/>
  <entry id="3115" name="RpcStarPCheckUserNDayNotOnline4GMLostTimerReq" serverName="starpaccountsvr"/>
  <entry id="3117" name="RpcStarPCheckUserNDayNotOnline4GMLostTimerResultNtfReq" serverName="starpsvr"/>
  <entry id="3124" name="RpcGetUgcPublicPlayerDataAttrsReq" serverName="ugcdatastoresvr"/>
  <entry id="3125" name="RpcGetUgcPublicPlayerDataAttrsRes" serverName="ugcdatastoresvr"/>
  <entry id="3128" name="RpcApiMallCommodityBuyReq" serverName="api_gamesvr"/>
  <entry id="3129" name="RpcApiMallCommodityBuyRes" serverName="api_gamesvr"/>
  <entry id="3132" name="RpcApiCheckMoneyReq" serverName="api_gamesvr"/>
  <entry id="3133" name="RpcApiCheckMoneyRes" serverName="api_gamesvr"/>
  <entry id="3134" name="RpcApiGetMoneyReq" serverName="api_gamesvr"/>
  <entry id="3135" name="RpcApiGetMoneyRes" serverName="api_gamesvr"/>
  <entry id="3136" name="RpcApiIncreaseMoneyReq" serverName="api_gamesvr"/>
  <entry id="3137" name="RpcApiIncreaseMoneyRes" serverName="api_gamesvr"/>
  <entry id="3140" name="RpcApiMinItemReq" serverName="api_gamesvr"/>
  <entry id="3141" name="RpcApiMinItemRes" serverName="api_gamesvr"/>
  <entry id="3142" name="RpcApiAddItemReq" serverName="api_gamesvr"/>
  <entry id="3143" name="RpcApiAddItemRes" serverName="api_gamesvr"/>
  <entry id="3144" name="RpcApiGetItemReq" serverName="api_gamesvr"/>
  <entry id="3145" name="RpcApiGetItemRes" serverName="api_gamesvr"/>
  <entry id="3146" name="RpcApiGetActivityScoreReq" serverName="api_gamesvr"/>
  <entry id="3147" name="RpcApiGetActivityScoreRes" serverName="api_gamesvr"/>
  <entry id="3148" name="RpcApiGetHotRankReq" serverName="api_gamesvr"/>
  <entry id="3149" name="RpcApiGetHotRankRes" serverName="api_gamesvr"/>
  <entry id="3152" name="RpcApiSyncHotRankReq" serverName="api_gamesvr"/>
  <entry id="3153" name="RpcApiSyncHotRankRes" serverName="api_gamesvr"/>
  <entry id="3154" name="RpcApiGetItemsByItemTypeReq" serverName="api_gamesvr"/>
  <entry id="3155" name="RpcApiGetItemsByItemTypeRes" serverName="api_gamesvr"/>
  <entry id="3156" name="RpcRoomRoundInfoNtfReq" serverName="gamesvr"/>
  <entry id="3157" name="RpcRoomGetCurRoundInfoReq" serverName="roomsvr"/>
  <entry id="3158" name="RpcRoomGetCurRoundInfoRes" serverName="roomsvr"/>
  <entry id="3159" name="RpcUgcRoomMultiRoundCoinUseReq" serverName="roomsvr"/>
  <entry id="3160" name="RpcUgcRoomMultiRoundCoinUseRes" serverName="roomsvr"/>
  <entry id="3162" name="RpcStarPGroupWorldEnterReq" serverName="starpgroupsvr"/>
  <entry id="3163" name="RpcStarPGroupWorldEnterRes" serverName="starpgroupsvr"/>
  <entry id="3164" name="RpcStarPGetItemInfoReq" serverName="starpinfosvr"/>
  <entry id="3165" name="RpcStarPGetItemInfoRes" serverName="starpinfosvr"/>
  <entry id="3166" name="RpcStarPCardSendReq" serverName="starpgamesvr"/>
  <entry id="3170" name="RpcStarPSubmitAssistOrderReq" serverName="starpgamesvr"/>
  <entry id="3171" name="RpcStarPSubmitAssistOrderRes" serverName="starpgamesvr"/>
  <entry id="3174" name="RpcStarPGetAssistOrderListReq" serverName="starpgamesvr"/>
  <entry id="3175" name="RpcStarPGetAssistOrderListRes" serverName="starpgamesvr"/>
  <entry id="3176" name="RpcStarPLikeAssistPlayerReq" serverName="starpgamesvr"/>
  <entry id="3177" name="RpcStarPLikeAssistPlayerRes" serverName="starpgamesvr"/>
  <entry id="3180" name="RpcStarPUpdateAssistOrderInfoReq" serverName="starpaccountsvr"/>
  <entry id="3181" name="RpcStarPUpdateAssistOrderInfoRes" serverName="starpaccountsvr"/>
  <entry id="3182" name="RpcUpdateUgcPublicPlayerDataAttrReq" serverName="ugcdatastoresvr"/>
  <entry id="3183" name="RpcUpdateUgcPublicPlayerDataAttrRes" serverName="ugcdatastoresvr"/>
  <entry id="3188" name="RpcStarPSendCardChatMsgReq" serverName="gamesvr"/>
  <entry id="3191" name="StarPCreateGroupReq" serverName="starpgroupsvr"/>
  <entry id="3192" name="StarPCreateGroupRes" serverName="starpgroupsvr"/>
  <entry id="3193" name="StarPJoinGroupReq" serverName="starpgroupsvr"/>
  <entry id="3194" name="StarPJoinGroupRes" serverName="starpgroupsvr"/>
  <entry id="3195" name="StarPLeaveGroupReq" serverName="starpgroupsvr"/>
  <entry id="3196" name="StarPLeaveGroupRes" serverName="starpgroupsvr"/>
  <entry id="3197" name="StarPDismissGroupReq" serverName="starpgroupsvr"/>
  <entry id="3198" name="StarPDismissGroupRes" serverName="starpgroupsvr"/>
  <entry id="3199" name="StarPGuildCreateReq" serverName="starpguildsvr"/>
  <entry id="3200" name="StarPGuildCreateRes" serverName="starpguildsvr"/>
  <entry id="3201" name="StarPGuildJoinReq" serverName="starpguildsvr"/>
  <entry id="3202" name="StarPGuildJoinRes" serverName="starpguildsvr"/>
  <entry id="3203" name="StarPGuildLeaveReq" serverName="starpguildsvr"/>
  <entry id="3204" name="StarPGuildLeaveRes" serverName="starpguildsvr"/>
  <entry id="3205" name="StarPGuildDismissReq" serverName="starpguildsvr"/>
  <entry id="3206" name="StarPGuildDismissRes" serverName="starpguildsvr"/>
  <entry id="3207" name="RpcFarmConvertNPCFarmReq" serverName="farmsvr"/>
  <entry id="3208" name="RpcFarmConvertNPCFarmRes" serverName="farmsvr"/>
  <entry id="3209" name="RpcStarPChooseInitEquipReq" serverName="starpaccountsvr"/>
  <entry id="3210" name="RpcStarPChooseInitEquipRes" serverName="starpaccountsvr"/>
  <entry id="3211" name="RpcStarPQueryUid2BaseGroupReq" serverName="starpgamesvr"/>
  <entry id="3212" name="RpcStarPQueryUid2BaseGroupRes" serverName="starpgamesvr"/>
  <entry id="3213" name="RpcStarPCreateUid2BaseGroupReq" serverName="starpgamesvr"/>
  <entry id="3214" name="RpcStarPCreateUid2BaseGroupRes" serverName="starpgamesvr"/>
  <entry id="3215" name="RpcStarPUpdateUid2BaseGroupReq" serverName="starpgamesvr"/>
  <entry id="3216" name="RpcStarPUpdateUid2BaseGroupRes" serverName="starpgamesvr"/>
  <entry id="3217" name="RpcFarmCreateNPCFarmNtfReq" serverName="gamesvr"/>
  <entry id="3218" name="RpcFarmNPCFarmCreatedNtfReq" serverName="farmsvr"/>
  <entry id="3219" name="StarPGuildKickReq" serverName="starpguildsvr"/>
  <entry id="3220" name="StarPGuildKickRes" serverName="starpguildsvr"/>
  <entry id="3221" name="RpcStarPFindAssistOrderMaterialReq" serverName="starpgamesvr"/>
  <entry id="3222" name="RpcStarPFindAssistOrderMaterialRes" serverName="starpgamesvr"/>
  <entry id="3223" name="RpcStarPDeleteUid2BaseGroupReq" serverName="starpgamesvr"/>
  <entry id="3224" name="RpcStarPDeleteUid2BaseGroupRes" serverName="starpgamesvr"/>
  <entry id="3225" name="RpcBatchGetUgcPublishDetailsReq" serverName="ugcsvr"/>
  <entry id="3226" name="RpcBatchGetUgcPublishDetailsRes" serverName="ugcsvr"/>
  <entry id="3227" name="RpcUgcMatchLobbyDetailGetAllReq" serverName="ugcsvr"/>
  <entry id="3228" name="RpcUgcMatchLobbyDetailGetAllRes" serverName="ugcsvr"/>
  <entry id="3229" name="StarPGuildRoomKickReq" serverName="starpguildsvr"/>
  <entry id="3230" name="StarPGuildRoomKickRes" serverName="starpguildsvr"/>
  <entry id="3231" name="RpcArenaHeroStarInfoUpdateNtfReq" serverName="arenasvr"/>
  <entry id="3232" name="RpcStarPGetRecruitInfoReq" serverName="starpaccountsvr"/>
  <entry id="3233" name="RpcStarPGetRecruitInfoRes" serverName="starpaccountsvr"/>
  <entry id="3234" name="RpcStarPGroupUserEnterReq" serverName="starpaccountsvr"/>
  <entry id="3235" name="RpcStarPGroupUserEnterRes" serverName="starpaccountsvr"/>
  <entry id="3236" name="RpcAigcNpcRoleIdUpdateNtfReq" serverName="streamsvr"/>
  <entry id="3238" name="RpcStarPGroupWorldExitReq" serverName="starpgroupsvr"/>
  <entry id="3239" name="RpcStarPGroupWorldExitRes" serverName="starpgroupsvr"/>
  <entry id="3244" name="RpcStarPRoleExitReq" serverName="starpsvr"/>
  <entry id="3245" name="RpcStarPRoleExitRes" serverName="starpsvr"/>
  <entry id="3246" name="RpcStarPGetRoleWorldListReq" serverName="starpaccountsvr"/>
  <entry id="3247" name="RpcStarPGetRoleWorldListRes" serverName="starpaccountsvr"/>
  <entry id="3248" name="RpcApiGetCommonlyUsedHeroIdReq" serverName="api_gamesvr"/>
  <entry id="3249" name="RpcApiGetCommonlyUsedHeroIdRes" serverName="api_gamesvr"/>
  <entry id="3250" name="RpcApiSetCommonlyUsedHeroIdReq" serverName="api_gamesvr"/>
  <entry id="3251" name="RpcApiSetCommonlyUsedHeroIdRes" serverName="api_gamesvr"/>
  <entry id="3252" name="RpcApiSendEventReq" serverName="noserver"/>
  <entry id="3253" name="RpcApiSendEventRes" serverName="noserver"/>
  <entry id="3254" name="RpcStarPTestEnterGroupReq" serverName="starpgamesvr"/>
  <entry id="3255" name="RpcStarPTestEnterGroupRes" serverName="starpgamesvr"/>
  <entry id="3256" name="StarPApplyJoinGroupReq" serverName="starpgroupsvr"/>
  <entry id="3257" name="StarPApplyJoinGroupRes" serverName="starpgroupsvr"/>
  <entry id="3258" name="StarPQueryGroupApplicationReq" serverName="starpgroupsvr"/>
  <entry id="3259" name="StarPQueryGroupApplicationRes" serverName="starpgroupsvr"/>
  <entry id="3260" name="StarPQueryGroupInvitationReq" serverName="starpgroupsvr"/>
  <entry id="3261" name="StarPQueryGroupInvitationRes" serverName="starpgroupsvr"/>
  <entry id="3262" name="StarPAcceptInvitationReq" serverName="starpgroupsvr"/>
  <entry id="3263" name="StarPAcceptInvitationRes" serverName="starpgroupsvr"/>
  <entry id="3264" name="StarPInviteJoinGroupReq" serverName="starpgroupsvr"/>
  <entry id="3265" name="StarPInviteJoinGroupRes" serverName="starpgroupsvr"/>
  <entry id="3266" name="StarPAcceptApplicationReq" serverName="starpgroupsvr"/>
  <entry id="3267" name="StarPAcceptApplicationRes" serverName="starpgroupsvr"/>
  <entry id="3268" name="RpcStarPQueryGroupApplicationReq" serverName="starpgamesvr"/>
  <entry id="3269" name="RpcStarPQueryGroupApplicationRes" serverName="starpgamesvr"/>
  <entry id="3270" name="RpcStarPQueryGroupInvitationReq" serverName="starpgamesvr"/>
  <entry id="3271" name="RpcStarPQueryGroupInvitationRes" serverName="starpgamesvr"/>
  <entry id="3272" name="RpcStarPGroupInsertApplicationReq" serverName="starpgamesvr"/>
  <entry id="3273" name="RpcStarPGroupInsertApplicationRes" serverName="starpgamesvr"/>
  <entry id="3274" name="RpcStarPGroupInsertInvitationReq" serverName="starpgamesvr"/>
  <entry id="3275" name="RpcStarPGroupInsertInvitationRes" serverName="starpgamesvr"/>
  <entry id="3276" name="RpcStarPUpdateAssistMaterialInfoReq" serverName="starpaccountsvr"/>
  <entry id="3277" name="RpcStarPUpdateAssistMaterialInfoRes" serverName="starpaccountsvr"/>
  <entry id="3278" name="RpcStarPAssistOrderStatusNtfReq" serverName="gamesvr"/>
  <entry id="3279" name="RpcStarPChatGroupInfoNtfReq" serverName="gamesvr"/>
  <entry id="3280" name="RpcSPRoomRecruitJoinReq" serverName="starproomsvr"/>
  <entry id="3281" name="RpcSPRoomRecruitJoinRes" serverName="starproomsvr"/>
  <entry id="3282" name="RpcStarPGroupClearInvitationReq" serverName="starpgamesvr"/>
  <entry id="3283" name="RpcStarPGroupClearInvitationRes" serverName="starpgamesvr"/>
  <entry id="3284" name="RpcFarmRefreshNpcFarmNtfReq" serverName="farmsvr"/>
  <entry id="3291" name="RpcStarPGetWearItemInfoReq" serverName="starpinfosvr"/>
  <entry id="3292" name="RpcStarPGetWearItemInfoRes" serverName="starpinfosvr"/>
  <entry id="3293" name="RpcApiSsetArenaHeadFrameReq" serverName="api_gamesvr"/>
  <entry id="3294" name="RpcApiSsetArenaHeadFrameRes" serverName="api_gamesvr"/>
  <entry id="3295" name="RpcStarPGroupRecommendReq" serverName="starpgroupsvr"/>
  <entry id="3296" name="RpcStarPGroupRecommendRes" serverName="starpgroupsvr"/>
  <entry id="3297" name="RpcStarPGroupMemberListReq" serverName="starpgroupsvr"/>
  <entry id="3298" name="RpcStarPGroupMemberListRes" serverName="starpgroupsvr"/>
  <entry id="3299" name="RpcApiGetPlayerGameTimesStatReq" serverName="api_gamesvr"/>
  <entry id="3300" name="RpcApiGetPlayerGameTimesStatRes" serverName="api_gamesvr"/>
  <entry id="3303" name="RpcStarPGetGroupInfoReq" serverName="starpgroupsvr"/>
  <entry id="3304" name="RpcStarPGetGroupInfoRes" serverName="starpgroupsvr"/>
  <entry id="3305" name="RpcApiGetPlayerArenaGameTimesStatReq" serverName="api_gamesvr"/>
  <entry id="3306" name="RpcApiGetPlayerArenaGameTimesStatRes" serverName="api_gamesvr"/>
  <entry id="3307" name="RpcApiAddPlayerArenaGameTimesStatReq" serverName="api_gamesvr"/>
  <entry id="3308" name="RpcApiAddPlayerArenaGameTimesStatRes" serverName="api_gamesvr"/>
  <entry id="3309" name="RpcStarPGuildInsertApplicationReq" serverName="starpgamesvr"/>
  <entry id="3310" name="RpcStarPGuildInsertApplicationRes" serverName="starpgamesvr"/>
  <entry id="3311" name="RpcStarPGuildInsertInvitationReq" serverName="starpgamesvr"/>
  <entry id="3312" name="RpcStarPGuildInsertInvitationRes" serverName="starpgamesvr"/>
  <entry id="3316" name="RpcStarPGuildClearApplicationRes" serverName="starpgamesvr"/>
  <entry id="3317" name="RpcTriggerDsConsumeInteractionReq" serverName="starpsvr"/>
  <entry id="3318" name="RpcTriggerDsConsumeInteractionRes" serverName="starpsvr"/>
  <entry id="3319" name="TestAiNpcSvrReq" serverName="ainpcsvr"/>
  <entry id="3320" name="TestAiNpcSvrRes" serverName="ainpcsvr"/>
  <entry id="3325" name="RpcStarPAssistOrderChangeNtfReq" serverName="gamesvr"/>
  <entry id="3326" name="RpcStarPGetMyContactRecordListReq" serverName="starpinfosvr"/>
  <entry id="3327" name="RpcStarPGetMyContactRecordListRes" serverName="starpinfosvr"/>
  <entry id="3328" name="RpcFarmNpcFarmRefreshEndNtfReq" serverName="farmsvr"/>
  <entry id="3329" name="RpcStarPGroupSaveSettingsReq" serverName="starpgroupsvr"/>
  <entry id="3330" name="RpcStarPGroupSaveSettingsRes" serverName="starpgroupsvr"/>
  <entry id="3331" name="RpcApiGetBattleInfoReq" serverName="api_gamesvr"/>
  <entry id="3332" name="RpcApiGetBattleInfoRes" serverName="api_gamesvr"/>
  <entry id="3333" name="RpcArenaHeroStarRewardLevelReq" serverName="arenasvr"/>
  <entry id="3334" name="RpcArenaHeroStarRewardLevelRes" serverName="arenasvr"/>
  <entry id="3335" name="RpcStarPGroupGmReq" serverName="starpgroupsvr"/>
  <entry id="3336" name="RpcStarPGroupGmRes" serverName="starpgroupsvr"/>
  <entry id="3337" name="RpcStarPKickGroupMemberReq" serverName="starpgroupsvr"/>
  <entry id="3338" name="RpcStarPKickGroupMemberRes" serverName="starpgroupsvr"/>
  <entry id="3339" name="RpcStarPCheckFunctionControlReq" serverName="starpaccountsvr"/>
  <entry id="3340" name="RpcStarPCheckFunctionControlRes" serverName="starpaccountsvr"/>
  <entry id="3341" name="RpcStarPGroupAttrNtfReq" serverName="gamesvr"/>
  <entry id="3342" name="RpcStarPGetMaxTerminalLevelReq" serverName="starpaccountsvr"/>
  <entry id="3343" name="RpcStarPGetMaxTerminalLevelRes" serverName="starpaccountsvr"/>
  <entry id="3347" name="RpcStarPGroupCheckUserIsOnlineReq" serverName="starpgroupsvr"/>
  <entry id="3348" name="RpcStarPGroupCheckUserIsOnlineRes" serverName="starpgroupsvr"/>
  <entry id="3349" name="RpcFarmPetFeedCostReq" serverName="farmsvr"/>
  <entry id="3350" name="RpcStarPGroupGetOngoingBattleReq" serverName="starpgroupsvr"/>
  <entry id="3351" name="RpcStarPGroupGetOngoingBattleRes" serverName="starpgroupsvr"/>
  <entry id="3352" name="RpcFarmRefreshNpcFarmReq" serverName="farmsvr"/>
  <entry id="3353" name="RpcFarmRefreshNpcFarmRes" serverName="farmsvr"/>
  <entry id="3354" name="RpcStarPGroupBroadcastGmReq" serverName="starpgroupsvr"/>
  <entry id="3355" name="RpcStarPGroupBroadcastGmRes" serverName="starpgroupsvr"/>
  <entry id="3356" name="RpcArenaUnlockCardReq" serverName="arenasvr"/>
  <entry id="3357" name="RpcArenaUnlockCardRes" serverName="arenasvr"/>
  <entry id="3358" name="RpcAiNpcChatNtfReq" serverName="ainpcsvr"/>
  <entry id="3359" name="RpcStarPSetJoinTypeReq" serverName="starpgroupsvr"/>
  <entry id="3360" name="RpcStarPSetJoinTypeRes" serverName="starpgroupsvr"/>
  <entry id="3361" name="RpcStarPSetTitleReq" serverName="starpgroupsvr"/>
  <entry id="3362" name="RpcStarPSetTitleRes" serverName="starpgroupsvr"/>
  <entry id="3363" name="RpcAiNpcLoginNtfReq" serverName="ainpcsvr"/>
  <entry id="3364" name="RpcAiNpcSyncNpcDataNtfReq" serverName="ainpcsvr"/>
  <entry id="3365" name="RpcAiNpcAddRoundReq" serverName="gamesvr"/>
  <entry id="3366" name="RpcAiNpcAddRoundRes" serverName="gamesvr"/>
  <entry id="3367" name="RpcAiNpcLogoutNtfReq" serverName="ainpcsvr"/>
  <entry id="3368" name="RpcAiNpcAddRoundNtfReq" serverName="ainpcsvr"/>
  <entry id="3369" name="RpcStarPWorldApplySizeReq" serverName="starpsvr"/>
  <entry id="3370" name="RpcStarPWorldApplySizeRes" serverName="starpsvr"/>
  <entry id="3371" name="RpcGetGuildIdsInRoomReq" serverName="starpsvr"/>
  <entry id="3372" name="RpcGetGuildIdsInRoomRes" serverName="starpsvr"/>
  <entry id="3377" name="StarPGetGuildBriefInfoReq" serverName="starpguildsvr"/>
  <entry id="3378" name="StarPGetGuildBriefInfoRes" serverName="starpguildsvr"/>
  <entry id="3379" name="RpcStarPGetAccountWorldListReq" serverName="starpaccountsvr"/>
  <entry id="3380" name="RpcStarPGetAccountWorldListRes" serverName="starpaccountsvr"/>
  <entry id="3381" name="RpcOperatePublishCoCreateMapReq" serverName="ugcsvr"/>
  <entry id="3382" name="RpcOperatePublishCoCreateMapRes" serverName="ugcsvr"/>
  <entry id="3383" name="RpcTopRankFetchByScoreReq" serverName="ranksvr"/>
  <entry id="3384" name="RpcTopRankFetchByScoreRes" serverName="ranksvr"/>
  <entry id="3385" name="RpcStarPGMCommandReq" serverName="starpgamesvr"/>
  <entry id="3386" name="RpcStarPGMCommandRes" serverName="starpgamesvr"/>
  <entry id="3388" name="RpcPressGmAddItemReq" serverName="starpsvr"/>
  <entry id="3395" name="RpcStarPQueryAccountWorldPermissionReq" serverName="starpaccountsvr"/>
  <entry id="3396" name="RpcStarPQueryAccountWorldPermissionRes" serverName="starpaccountsvr"/>
  <entry id="3397" name="RpcStarPPetTradeFocusPlayerReq" serverName="starpgamesvr"/>
  <entry id="3398" name="RpcStarPPetTradeFocusPlayerRes" serverName="starpgamesvr"/>
  <entry id="3399" name="RpcStarPPetTradeCancelFocusPlayerReq" serverName="starpgamesvr"/>
  <entry id="3400" name="RpcStarPPetTradeCancelFocusPlayerRes" serverName="starpgamesvr"/>
  <entry id="3401" name="RpcStarPPetTradeGetFocusPlayerReq" serverName="starpgamesvr"/>
  <entry id="3402" name="RpcStarPPetTradeGetFocusPlayerRes" serverName="starpgamesvr"/>
  <entry id="3403" name="RpcStarPPetTradeUpdateWishReq" serverName="starpaccountsvr"/>
  <entry id="3404" name="RpcStarPPetTradeUpdateWishRes" serverName="starpaccountsvr"/>
  <entry id="3405" name="RpcStarPPetTradeCancelWishReq" serverName="starpaccountsvr"/>
  <entry id="3406" name="RpcStarPPetTradeCancelWishRes" serverName="starpaccountsvr"/>
  <entry id="3407" name="RpcGMStarPDeleteAllReq" serverName="starpsvr"/>
  <entry id="3408" name="RpcGMStarPDeleteAllRes" serverName="starpsvr"/>
  <entry id="3409" name="TestAnalyzeSvrReq" serverName="analyzesvr"/>
  <entry id="3410" name="TestAnalyzeSvrRes" serverName="analyzesvr"/>
  <entry id="3412" name="RpcUgcSendMailProcessReq" serverName="gamesvr"/>
  <entry id="3413" name="RpcUgcSendMailProcessRes" serverName="gamesvr"/>
  <entry id="3414" name="RpcStarPCheckInviteCdReq" serverName="starpaccountsvr"/>
  <entry id="3415" name="RpcStarPCheckInviteCdRes" serverName="starpaccountsvr"/>
  <entry id="3416" name="CocQueryCommonItemCountReq" serverName="gamesvr"/>
  <entry id="3418" name="RpcAiNpcAddChatRecordNtfReq" serverName="ainpcsvr"/>
  <entry id="3419" name="RpcNtfGuildMemberSelfApplyReq" serverName="starpsvr"/>
  <entry id="3420" name="RpcNtfGuildMemberSelfApplyRes" serverName="starpsvr"/>
  <entry id="3421" name="RpcAiNpcDelChatRecordNtfReq" serverName="ainpcsvr"/>
  <entry id="3422" name="CocQueryCommonItemCountRes" serverName="gamesvr"/>
  <entry id="3423" name="RpcStarPMaterialAssistCountReq" serverName="starpaccountsvr"/>
  <entry id="3424" name="RpcStarPMaterialAssistCountRes" serverName="starpaccountsvr"/>
  <entry id="3425" name="RpcStarPUpdateMaterialAssistCountReq" serverName="starpgroupsvr"/>
  <entry id="3426" name="RpcStarPUpdateMaterialAssistCountRes" serverName="starpgroupsvr"/>
  <entry id="3427" name="RpcAinpcCsForwardReq" serverName="ainpcsvr"/>
  <entry id="3428" name="RpcAinpcCsForwardRes" serverName="ainpcsvr"/>
  <entry id="3429" name="RpcStarPCardHandleReq" serverName="starpgamesvr"/>
  <entry id="3433" name="RpcStarPGetFriendInfoReq" serverName="gamesvr"/>
  <entry id="3434" name="RpcStarPGetFriendInfoRes" serverName="gamesvr"/>
  <entry id="3435" name="RpcStarPAddDailyMsgIntimacyReq" serverName="starpgamesvr"/>
  <entry id="3436" name="RpcStarPAddDailyMsgIntimacyRes" serverName="starpgamesvr"/>
  <entry id="3437" name="RpcStarPRelationChangeReq" serverName="starpgamesvr"/>
  <entry id="3438" name="RpcStarPRelationChangeRes" serverName="starpgamesvr"/>
  <entry id="3439" name="CocConsumeCommonItemReq" serverName="gamesvr"/>
  <entry id="3440" name="CocConsumeCommonItemRes" serverName="gamesvr"/>
  <entry id="3443" name="RpcStarPUpdatePetTradeCountReq" serverName="starpgroupsvr"/>
  <entry id="3444" name="RpcStarPUpdatePetTradeCountRes" serverName="starpgroupsvr"/>
  <entry id="3445" name="RpcStarPSendCardChatMsgRes" serverName="gamesvr"/>
  <entry id="3446" name="RpcCardFillDetailReq" serverName="starpsvr"/>
  <entry id="3447" name="RpcCardFillDetailRes" serverName="starpsvr"/>
  <entry id="3448" name="RpcCardHandleReq" serverName="starpsvr"/>
  <entry id="3449" name="RpcCardHandleRes" serverName="starpsvr"/>
  <entry id="3450" name="RpcStarPGuildRemoveApplicationReq" serverName="starpgamesvr"/>
  <entry id="3451" name="RpcStarPGuildRemoveApplicationRes" serverName="starpgamesvr"/>
  <entry id="3452" name="RpcStarPGuildRemoveInvitationReq" serverName="starpgamesvr"/>
  <entry id="3453" name="RpcStarPGuildRemoveInvitationRes" serverName="starpgamesvr"/>
  <entry id="3454" name="RpcStarPChangeFriendIntimacyReq" serverName="starpgamesvr"/>
  <entry id="3455" name="RpcStarPChangeFriendIntimacyRes" serverName="starpgamesvr"/>
  <entry id="3456" name="RpcStarPDeleteFriendIntimacyReq" serverName="starpgamesvr"/>
  <entry id="3457" name="RpcStarPDeleteFriendIntimacyRes" serverName="starpgamesvr"/>
  <entry id="3458" name="RpcStarPFriendIntimacyChangeNtfReq" serverName="gamesvr"/>
  <entry id="3459" name="RpcGmStarPSetAccountMaxLevelReq" serverName="starpaccountsvr"/>
  <entry id="3460" name="RpcGmStarPSetAccountMaxLevelRes" serverName="starpaccountsvr"/>
  <entry id="3461" name="RpcStarPGetFriendIntimacyReq" serverName="starpinfosvr"/>
  <entry id="3462" name="RpcStarPGetFriendIntimacyRes" serverName="starpinfosvr"/>
  <entry id="3463" name="RpcGmStarPPressAddItemReq" serverName="starpaccountsvr"/>
  <entry id="3464" name="RpcStarPSocInteractionRatioGetInfoReq" serverName="gamesvr"/>
  <entry id="3465" name="RpcStarPSocInteractionRatioGetInfoRes" serverName="gamesvr"/>
  <entry id="3466" name="RpcStarPPetTradeGetWishReq" serverName="starpaccountsvr"/>
  <entry id="3467" name="RpcStarPPetTradeGetWishRes" serverName="starpaccountsvr"/>
  <entry id="3468" name="RpcStarPCheckInformationLawfulReq" serverName="gamesvr"/>
  <entry id="3469" name="RpcStarPCheckInformationLawfulRes" serverName="gamesvr"/>
  <entry id="3470" name="RpcStarPTipsNtfReq" serverName="gamesvr"/>
  <entry id="3474" name="CocAddCommonItemReq" serverName="gamesvr"/>
  <entry id="3475" name="CocAddCommonItemRes" serverName="gamesvr"/>
  <entry id="3478" name="RpcStarpCardGroupHandleReq" serverName="starpgroupsvr"/>
  <entry id="3479" name="RpcStarpCardGroupHandleRes" serverName="starpgroupsvr"/>
  <entry id="3482" name="RpcStarPGroupUpdateChatGroupReq" serverName="starpaccountsvr"/>
  <entry id="3483" name="RpcStarPGroupUpdateChatGroupRes" serverName="starpaccountsvr"/>
  <entry id="3484" name="RpcStarPChatGroupGetPetEggReq" serverName="starpgroupsvr"/>
  <entry id="3485" name="RpcStarPChatGroupGetPetEggRes" serverName="starpgroupsvr"/>
  <entry id="3486" name="StarPRemoveGuildApplicationReq" serverName="starpguildsvr"/>
  <entry id="3487" name="StarPRemoveGuildApplicationRes" serverName="starpguildsvr"/>
  <entry id="3488" name="StarPRemoveGuildInviteReq" serverName="starpguildsvr"/>
  <entry id="3489" name="StarPRemoveGuildInviteRes" serverName="starpguildsvr"/>
  <entry id="3490" name="RpcArenaGetHeroReLiInfoReq" serverName="arenasvr"/>
  <entry id="3491" name="RpcArenaGetHeroReLiInfoRes" serverName="arenasvr"/>
  <entry id="3492" name="ArenaHeroReLiDataNtfReq" serverName="gamesvr"/>
  <entry id="3493" name="ArenaHeroReLiDataNtfRes" serverName="gamesvr"/>
  <entry id="3494" name="RpcSetPushRandomEventStateReq" serverName="arenasvr"/>
  <entry id="3495" name="RpcSetPushRandomEventStateRes" serverName="arenasvr"/>
  <entry id="3498" name="StarPGuildUpdatePublicInfoReq" serverName="starpguildsvr"/>
  <entry id="3499" name="RpcAiNpcGMCmdReq" serverName="ainpcsvr"/>
  <entry id="3500" name="RpcAiNpcGMCmdRes" serverName="ainpcsvr"/>
  <entry id="3501" name="RpcStarPDeductFriendIntimacyOnDayResetReq" serverName="starpgamesvr"/>
  <entry id="3502" name="RpcStarPDeductFriendIntimacyOnDayResetRes" serverName="starpgamesvr"/>
  <entry id="3503" name="PullAnalyzeDataReq" serverName="analyzesvr"/>
  <entry id="3504" name="PullAnalyzeDataRes" serverName="analyzesvr"/>
  <entry id="3505" name="RpcStarPPlayerGetFriendIntimacyReq" serverName="starpgamesvr"/>
  <entry id="3506" name="RpcStarPPlayerGetFriendIntimacyRes" serverName="starpgamesvr"/>
  <entry id="3509" name="StarPGetGroupMemberUidsReq" serverName="starpgroupsvr"/>
  <entry id="3510" name="StarPGetGroupMemberUidsRes" serverName="starpgroupsvr"/>
  <entry id="3511" name="RpcBatchUgcCoCreateEditorReq" serverName="gamesvr"/>
  <entry id="3513" name="RpcGetCardReLiInfoReq" serverName="arenasvr"/>
  <entry id="3514" name="RpcGetCardReLiInfoRes" serverName="arenasvr"/>
  <entry id="3516" name="ArenaPushMsgNonBlockingReq" serverName="gamesvr"/>
  <entry id="3517" name="ArenaPushMsgNonBlockingRes" serverName="gamesvr"/>
  <entry id="3518" name="RpcBattleRegionTestReq" serverName="battlesvr"/>
  <entry id="3519" name="RpcBattleRegionTestRes" serverName="battlesvr"/>
  <entry id="3523" name="RpcUgcEntityListByInstanceReq" serverName="ugcsvr"/>
  <entry id="3524" name="RpcUgcEntityListByInstanceRes" serverName="ugcsvr"/>
  <entry id="3526" name="ArenaPushMsgBlockingReq" serverName="gamesvr"/>
  <entry id="3527" name="ArenaPushMsgBlockingRes" serverName="gamesvr"/>
  <entry id="3529" name="RpcStarPSocInteractionRatioReq" serverName="starpgamesvr"/>
  <entry id="3530" name="RpcStarPSocInteractionRatioRes" serverName="starpgamesvr"/>
  <entry id="3535" name="StarPInviteGuildReq" serverName="starpguildsvr"/>
  <entry id="3536" name="StarPInviteGuildRes" serverName="starpguildsvr"/>
  <entry id="3537" name="RpcStarPGetChatInfoReq" serverName="gamesvr"/>
  <entry id="3538" name="RpcStarPGetChatInfoRes" serverName="gamesvr"/>
  <entry id="3539" name="RpcFarmReportHotNtfReq" serverName="farmsvr"/>
  <entry id="3540" name="RpcStarPGMClearRoleReq" serverName="starpaccountsvr"/>
  <entry id="3541" name="RpcStarPGMClearRoleRes" serverName="starpaccountsvr"/>
  <entry id="3542" name="RpcStarPUpdateFriendIntimacyNtfReq" serverName="starpinfosvr"/>
  <entry id="3543" name="RpcApiGetAccumRechargeInfoReq" serverName="api_gamesvr"/>
  <entry id="3544" name="RpcApiGetAccumRechargeInfoRes" serverName="api_gamesvr"/>
  <entry id="3545" name="RpcApiExpireMoneyReq" serverName="api_gamesvr"/>
  <entry id="3546" name="RpcApiExpireMoneyRes" serverName="api_gamesvr"/>
  <entry id="3547" name="RpcApiClearItemReq" serverName="api_gamesvr"/>
  <entry id="3548" name="RpcApiClearItemRes" serverName="api_gamesvr"/>
  <entry id="3549" name="RpcApiGetPlayerProfileInfoReq" serverName="api_gamesvr"/>
  <entry id="3550" name="RpcApiGetPlayerProfileInfoRes" serverName="api_gamesvr"/>
  <entry id="3551" name="RpcGmStarPModifyWorldInfoReq" serverName="starpaccountsvr"/>
  <entry id="3552" name="RpcApiSendChatMessageReq" serverName="api_gamesvr"/>
  <entry id="3553" name="RpcApiSendChatMessageRes" serverName="api_gamesvr"/>
  <entry id="3555" name="RpcStarPWorldSynchronizeReq" serverName="starpaccountsvr"/>
  <entry id="3556" name="RpcApiGetClubInfoReq" serverName="api_gamesvr"/>
  <entry id="3557" name="RpcApiGetClubInfoRes" serverName="api_gamesvr"/>
  <entry id="3558" name="RpcApiGetPlayerRankInfoReq" serverName="api_gamesvr"/>
  <entry id="3559" name="RpcApiGetPlayerRankInfoRes" serverName="api_gamesvr"/>
  <entry id="3560" name="RpcApiRefreshRankScoreReq" serverName="api_gamesvr"/>
  <entry id="3561" name="RpcApiRefreshRankScoreRes" serverName="api_gamesvr"/>
  <entry id="3564" name="RpcApiGetNextDrawPriceReq" serverName="api_gamesvr"/>
  <entry id="3565" name="RpcApiGetNextDrawPriceRes" serverName="api_gamesvr"/>
  <entry id="3566" name="RpcUgcMatchConfigPageReq" serverName="ugcsvr"/>
  <entry id="3567" name="RpcUgcMatchConfigPageRes" serverName="ugcsvr"/>
  <entry id="3568" name="RpcApiGetGrandRewardCountReq" serverName="api_gamesvr"/>
  <entry id="3569" name="RpcApiGetGrandRewardCountRes" serverName="api_gamesvr"/>
  <entry id="3570" name="StarUpdateGroupInfoReq" serverName="starpgroupsvr"/>
  <entry id="3571" name="StarUpdateGroupInfoRes" serverName="starpgroupsvr"/>
  <entry id="3572" name="RpcNR3E8EventReq" serverName="gamesvr"/>
  <entry id="3573" name="RpcNR3E8EventRes" serverName="gamesvr"/>
  <entry id="3574" name="RpcAddFriendIntimacyReq" serverName="gamesvr"/>
  <entry id="3575" name="RpcAddFriendIntimacyRes" serverName="gamesvr"/>
  <entry id="3577" name="RpcStarPPvpStartFailNtfReq" serverName="gamesvr"/>
  <entry id="3578" name="PullAnalyzeProtoDataReq" serverName="analyzesvr"/>
  <entry id="3579" name="PullAnalyzeProtoDataRes" serverName="analyzesvr"/>
  <entry id="3580" name="RpcActiveUgcCreatorBadgeReq" serverName="ugcsvr"/>
  <entry id="3581" name="RpcActiveUgcCreatorBadgeRes" serverName="ugcsvr"/>
  <entry id="3582" name="RpcStarPGuildUpdateStatusReq" serverName="starpgamesvr"/>
  <entry id="3583" name="RpcStarPGuildUpdateStatusRes" serverName="starpgamesvr"/>
  <entry id="3584" name="StarPUpdatePlayerStatusTimeReq" serverName="starpguildsvr"/>
  <entry id="3585" name="StarPUpdatePlayerStatusTimeRes" serverName="starpguildsvr"/>
  <entry id="3586" name="GMStarPSwitchMemberStateReq" serverName="starpgroupsvr"/>
  <entry id="3587" name="GMStarPSwitchMemberStateRes" serverName="starpgroupsvr"/>
  <entry id="3588" name="GMStarPSwitchDSStateReq" serverName="starpgroupsvr"/>
  <entry id="3589" name="GMStarPSwitchDSStateRes" serverName="starpgroupsvr"/>
  <entry id="3594" name="RpcSPDsEnterJoinRecruitReq" serverName="starproomsvr"/>
  <entry id="3595" name="RpcSetCreatorHomePageReq" serverName="ugcsvr"/>
  <entry id="3596" name="RpcSetCreatorHomePageRes" serverName="ugcsvr"/>
  <entry id="3597" name="RpcSelectActiveClubReq" serverName="clubsvr"/>
  <entry id="3598" name="RpcSelectActiveClubRes" serverName="clubsvr"/>
  <entry id="3599" name="RpcModifyClubScoreReq" serverName="clubsvr"/>
  <entry id="3600" name="RpcModifyClubScoreRes" serverName="clubsvr"/>
  <entry id="3611" name="RpcChangeCoverToolReq" serverName="ugcsvr"/>
  <entry id="3612" name="RpcChangeCoverToolRes" serverName="ugcsvr"/>
  <entry id="3634" name="CocClearModeInfoReq" serverName="gamesvr"/>
  <entry id="3635" name="CocClearModeInfoRes" serverName="gamesvr"/>
  <entry id="3636" name="RpcApiIsOnlineReq" serverName="api_gamesvr"/>
  <entry id="3637" name="RpcApiIsOnlineRes" serverName="api_gamesvr"/>
  <entry id="3638" name="RpcApiIsInBattleReq" serverName="api_gamesvr"/>
  <entry id="3639" name="RpcApiIsInBattleRes" serverName="api_gamesvr"/>
  <entry id="3651" name="StarPGetGuildMemberUidsReq" serverName="starpguildsvr"/>
  <entry id="3652" name="StarPGetGuildMemberUidsRes" serverName="starpguildsvr"/>
  <entry id="3660" name="RpcRoleTransferZoneUgcPlayerInfoModifyReq" serverName="ugcsvr"/>
  <entry id="3661" name="RpcRoleTransferZoneUgcPlayerInfoModifyRes" serverName="ugcsvr"/>
  <entry id="3662" name="RpcIdipModifyCreatorHomePageMessageReq" serverName="ugcsvr"/>
  <entry id="3663" name="RpcIdipModifyCreatorHomePageMessageRes" serverName="ugcsvr"/>
  <entry id="3666" name="RpcRoleTransferZoneOpenIdToCreatorIdQueryReq" serverName="ugcsvr"/>
  <entry id="3667" name="RpcRoleTransferZoneOpenIdToCreatorIdQueryRes" serverName="ugcsvr"/>
  <entry id="3668" name="RpcRoleTransferZoneOpenIdToCreatorIdModifyReq" serverName="ugcsvr"/>
  <entry id="3669" name="RpcRoleTransferZoneOpenIdToCreatorIdModifyRes" serverName="ugcsvr"/>
  <entry id="3676" name="RpcUgcUploadFileInfoReq" serverName="ugcsvr"/>
  <entry id="3677" name="RpcUgcUploadFileInfoRes" serverName="ugcsvr"/>
  <entry id="3678" name="RpcUgcUploadFileNotifyReq" serverName="ugcsvr"/>
  <entry id="3679" name="RpcUgcUploadFileNotifyRes" serverName="ugcsvr"/>
  <entry id="3686" name="CocGmClearGameServerInfoReq" serverName="gamesvr"/>
  <entry id="3687" name="CocGmClearGameServerInfoRes" serverName="gamesvr"/>
  <entry id="3688" name="RpcApiOperationAttrDataReq" serverName="api_gamesvr"/>
  <entry id="3689" name="RpcApiOperationAttrDataRes" serverName="api_gamesvr"/>
  <entry id="3695" name="RpcArenaStatAddNtfRes" serverName="arenasvr"/>
  <entry id="3698" name="RpcApiGetPlayerFriendUidListReq" serverName="api_gamesvr"/>
  <entry id="3699" name="RpcApiGetPlayerFriendUidListRes" serverName="api_gamesvr"/>
  <entry id="3709" name="RpcGetWolfLastBattleTimeReq" serverName="gamesvr"/>
  <entry id="3710" name="RpcGetWolfLastBattleTimeRes" serverName="gamesvr"/>
  <entry id="3711" name="RpcGetPlayerHasFriendReq" serverName="gamesvr"/>
  <entry id="3712" name="RpcGetPlayerHasFriendRes" serverName="gamesvr"/>
  <entry id="3713" name="RpcWolfReturnEventReq" serverName="activitysvr"/>
  <entry id="3714" name="RpcWolfReturnEventRes" serverName="activitysvr"/>
  <entry id="3726" name="RpcSendWechatRobotMessageReq" serverName="ugcplatsvr"/>
  <entry id="3727" name="RpcSendWechatRobotMessageRes" serverName="ugcplatsvr"/>
  <entry id="3728" name="GetLoadCacheDataAbilityReq" serverName="gamesvr"/>
  <entry id="3729" name="GetLoadCacheDataAbilityRes" serverName="gamesvr"/>
  <entry id="3734" name="RpcForwardEventMQReq" serverName="noserver"/>
  <entry id="3735" name="RpcForwardEventMQRes" serverName="noserver"/>
  <entry id="3744" name="RpcApiGetPlayerHasFriendReq" serverName="api_gamesvr"/>
  <entry id="3745" name="RpcApiGetPlayerHasFriendRes" serverName="api_gamesvr"/>
  <entry id="3747" name="RpcQueryActTeamInfoReq" serverName="activitysvr"/>
  <entry id="3748" name="RpcQueryActTeamInfoRes" serverName="activitysvr"/>
  <entry id="3749" name="RpcJoinActTeamReq" serverName="activitysvr"/>
  <entry id="3750" name="RpcJoinActTeamRes" serverName="activitysvr"/>
  <entry id="3751" name="RpcPlayerApplyJoinActTeamReq" serverName="activitysvr"/>
  <entry id="3752" name="RpcPlayerApplyJoinActTeamRes" serverName="activitysvr"/>
  <entry id="3762" name="RpcRoleTransferKickPlayerReq" serverName="activitysvr"/>
  <entry id="3763" name="RpcRoleTransferKickPlayerRes" serverName="activitysvr"/>
  <entry id="3764" name="RpcRoleTransferActivityDataUpdateReq" serverName="activitysvr"/>
  <entry id="3765" name="RpcRoleTransferActivityDataUpdateRes" serverName="activitysvr"/>
  <entry id="3770" name="ReportGameplayEventDataReq" serverName="gamesvr"/>
  <entry id="3771" name="ReportGameplayEventDataRes" serverName="gamesvr"/>
  <entry id="3775" name="RpcApiReportGameplayEventDataReq" serverName="api_gamesvr"/>
  <entry id="3776" name="RpcApiReportGameplayEventDataRes" serverName="api_gamesvr"/>
  <entry id="3777" name="RpcApiGetMonthCardInfoReq" serverName="api_gamesvr"/>
  <entry id="3778" name="RpcApiGetMonthCardInfoRes" serverName="api_gamesvr"/>
  <entry id="3782" name="RpcGetArenaBlockInfoReq" serverName="arenasvr"/>
  <entry id="3783" name="RpcGetArenaBlockInfoRes" serverName="arenasvr"/>
  <entry id="3791" name="RpcActivityTeamUpdateNtfReq" serverName="gamesvr"/>
  <entry id="3792" name="TestHttpSvrReq" serverName="httpsvr"/>
  <entry id="3793" name="TestHttpSvrRes" serverName="httpsvr"/>
  <entry id="3800" name="ModHeartBeatNtyReq" serverName="noserver"/>
  <entry id="3805" name="RpcLobbyPlayerHeartbeatReq" serverName="lobbysvr"/>
  <entry id="3806" name="RpcLobbyPlayerHeartbeatRes" serverName="lobbysvr"/>
  <entry id="3808" name="HttpContactReq" serverName="httpsvr"/>
  <entry id="3809" name="HttpContactRes" serverName="httpsvr"/>
  <entry id="3811" name="RpcGetHomeRecommendDetailReq" serverName="ugcplatsvr"/>
  <entry id="3812" name="RpcGetHomeRecommendDetailRes" serverName="ugcplatsvr"/>
  <entry id="3815" name="RpcRoomMidJoinStatusChangeNtfReq" serverName="roomsvr"/>
  <entry id="3818" name="RpcBatchAccountTransferTaskReq" serverName="gamesvr"/>
  <entry id="3819" name="RpcBatchAccountTransferTaskRes" serverName="gamesvr"/>
  <entry id="3820" name="RpcRoomMidJoinStatusChangeSyncReq" serverName="battlesvr"/>
  <entry id="3825" name="RpcTeamQuickJoinConfirmDoneNtfReq" serverName="gamesvr"/>
  <entry id="3842" name="RpcBatchBagMinusItemsReq" serverName="gamesvr"/>
  <entry id="3843" name="RpcBatchBagMinusItemsRes" serverName="gamesvr"/>
  <entry id="3844" name="RpcBatchBagAddItemsReq" serverName="gamesvr"/>
  <entry id="3845" name="RpcBatchBagAddItemsRes" serverName="gamesvr"/>
  <entry id="3846" name="RpcAllocIdReq" serverName="gamesvr"/>
  <entry id="3847" name="RpcAllocIdRes" serverName="gamesvr"/>
  <entry id="3852" name="RpcArenaGameInfoLoginSyncFinishNtfReq" serverName="gamesvr"/>
  <entry id="3864" name="RpcNoticeFoodBeReceivedReq" serverName="activitysvr"/>
  <entry id="3865" name="RpcNoticeFoodBeReceivedRes" serverName="activitysvr"/>
  <entry id="3866" name="FarmGetHotSpringBuffSourceResNtfReq" serverName="farmsvr"/>
  <entry id="3867" name="FarmGetHotSpringBuffSourceReqNtfReq" serverName="farmsvr"/>
  <entry id="3870" name="RpcGetHomeRecommendHotTagsReq" serverName="ugcplatsvr"/>
  <entry id="3871" name="RpcGetHomeRecommendHotTagsRes" serverName="ugcplatsvr"/>
  <entry id="3877" name="RpcAddDailyVictoryNumReq" serverName="arenasvr"/>
  <entry id="3878" name="RpcAddDailyVictoryNumRes" serverName="arenasvr"/>
  <entry id="3879" name="RpcUgcFBXAnalyzeNotifyReq" serverName="ugcsvr"/>
  <entry id="3880" name="RpcUgcFBXAnalyzeNotifyRes" serverName="ugcsvr"/>
  <entry id="3883" name="RpcAddUgcLayerReq" serverName="ugcsvr"/>
  <entry id="3884" name="RpcAddUgcLayerRes" serverName="ugcsvr"/>
  <entry id="3893" name="RpcCookGetCommentPhotoNameReq" serverName="farmsvr"/>
  <entry id="3894" name="RpcCookGetCommentPhotoNameRes" serverName="farmsvr"/>
  <entry id="3897" name="RpcGmStarPCardReq" serverName="starpgamesvr"/>
  <entry id="3898" name="RpcStarPJoinGuildNtfReq" serverName="gamesvr"/>
  <entry id="3899" name="RpcStarPJoinGuildNtfRes" serverName="gamesvr"/>
  <entry id="3900" name="RpcDebugdsStopForRecoveryReq" serverName="debugdsmgrsvr"/>
  <entry id="3901" name="RpcDebugdsStopForRecoveryRes" serverName="debugdsmgrsvr"/>
  <entry id="3908" name="IdipRpcMessageByHashKeyReq" serverName="any"/>
  <entry id="3909" name="IdipRpcMessageByHashKeyRes" serverName="any"/>
  <entry id="3910" name="IdipRpcMessageReq" serverName="any"/>
  <entry id="3911" name="IdipRpcMessageRes" serverName="any"/>
  <entry id="3923" name="ModPlayerOnlineStateNtyReq" serverName="noserver"/>
  <entry id="3938" name="RpcTeamQuickJoinFinishNtfReq" serverName="roomsvr"/>
  <entry id="3948" name="RpcSetSendRewardsStateReq" serverName="arenasvr"/>
  <entry id="3949" name="RpcSetSendRewardsStateRes" serverName="arenasvr"/>
  <entry id="3981" name="ModPlayerLoadReq" serverName="noserver"/>
  <entry id="3982" name="ModPlayerLoadRes" serverName="noserver"/>
  <entry id="3993" name="RpcUgcRoomPlayerRecommendMapReq" serverName="roomsvr"/>
  <entry id="3994" name="RpcUgcRoomPlayerRecommendMapRes" serverName="roomsvr"/>
  <entry id="3995" name="RpcUgcRoomGetPlayerRecommendMapsReq" serverName="roomsvr"/>
  <entry id="3996" name="RpcUgcRoomGetPlayerRecommendMapsRes" serverName="roomsvr"/>
  <entry id="3997" name="RpcUgcRoomPlayerRecommendMapNtfReq" serverName="gamesvr"/>
  <entry id="3998" name="RpcUgcRoomPlayerRecommendMapNtfRes" serverName="gamesvr"/>
  <entry id="3999" name="RpcUgcRoomGetOfficalRecommendMapsReq" serverName="roomsvr"/>
  <entry id="4000" name="RpcUgcRoomGetOfficalRecommendMapsRes" serverName="roomsvr"/>
  <entry id="4001" name="RpcObserverDsInfoNtfReq" serverName="gamesvr"/>
  <entry id="4002" name="RpcObserverDsInfoNtfRes" serverName="gamesvr"/>
  <entry id="4003" name="RpcSendGetBiHudRecommendMessageReq" serverName="ugcplatsvr"/>
  <entry id="4004" name="RpcSendGetBiHudRecommendMessageRes" serverName="ugcplatsvr"/>
  <entry id="4015" name="RpcRoomRecruitPublishWithModeTypeReq" serverName="roomsvr"/>
  <entry id="4016" name="RpcRoomRecruitPublishWithModeTypeRes" serverName="roomsvr"/>
  <entry id="4020" name="RpcAddCloudTaxNtfReq" serverName="farmsvr"/>
  <entry id="4032" name="RpcUgcCoCreateMultiEditApplyReq" serverName="gamesvr"/>
  <entry id="4033" name="RpcUgcCoCreateMultiEditApplyRes" serverName="gamesvr"/>
  <entry id="4034" name="RpcUgcCoCreateMultiEditReplyReq" serverName="gamesvr"/>
  <entry id="4035" name="RpcUgcCoCreateMultiEditReplyRes" serverName="gamesvr"/>
  <entry id="4036" name="RpcUgcCoCreateMultiEditRejectEnterReq" serverName="gamesvr"/>
  <entry id="4037" name="RpcUgcCoCreateMultiEditRejectEnterRes" serverName="gamesvr"/>
  <entry id="4038" name="RpcUgcCoCreateMultiEditDataUpdateReq" serverName="gamesvr"/>
  <entry id="4039" name="RpcUgcCoCreateMultiEditDataUpdateRes" serverName="gamesvr"/>
  <entry id="4040" name="RpcUgcCoCreateMultiEditCodingDataReq" serverName="ugcsvr"/>
  <entry id="4041" name="RpcUgcCoCreateMultiEditCodingDataRes" serverName="ugcsvr"/>
  <entry id="4042" name="RpcUgcCoCreateMultiEditApplyOccupyReq" serverName="ugcsvr"/>
  <entry id="4043" name="RpcUgcCoCreateMultiEditApplyOccupyRes" serverName="ugcsvr"/>
  <entry id="4061" name="RpcRoomCancelPreStartReq" serverName="roomsvr"/>
  <entry id="4062" name="RpcRoomCancelPreStartRes" serverName="roomsvr"/>
  <entry id="4063" name="RpcAiNpcChatPushProcessNtfReq" serverName="ainpcsvr"/>
  <entry id="4069" name="RpcBattleSceneSwitchNtfReq" serverName="gamesvr"/>
  <entry id="4076" name="RpcSubscribeRecommendPageReq" serverName="ugcplatsvr"/>
  <entry id="4077" name="RpcSubscribeRecommendPageRes" serverName="ugcplatsvr"/>
  <entry id="4099" name="RpcCreateGameSessionResultEventNtfReq" serverName="battlesvr"/>
  <entry id="4100" name="RpcGameSessionEndEventNtfReq" serverName="battlesvr"/>
  <entry id="4110" name="RpcMobaFootballGoalBroadcastNtfReq" serverName="gamesvr"/>
  <entry id="4111" name="RpcGMChangeBattleSceneReq" serverName="battlesvr"/>
  <entry id="4112" name="RpcUgcRoomOfficalRecommendListReq" serverName="ugcsvr"/>
  <entry id="4113" name="RpcUgcRoomOfficalRecommendListRes" serverName="ugcsvr"/>
  <entry id="4114" name="RpcUgcRoomGetOfficalRecommendListReq" serverName="ugcplatsvr"/>
  <entry id="4115" name="RpcUgcRoomGetOfficalRecommendListRes" serverName="ugcplatsvr"/>
  <entry id="4116" name="RpcGetUGCEntranceBubblesReq" serverName="ugcplatsvr"/>
  <entry id="4117" name="RpcGetUGCEntranceBubblesRes" serverName="ugcplatsvr"/>
  <entry id="4118" name="RpcRemoveUGCEntranceBubblesReq" serverName="ugcplatsvr"/>
  <entry id="4119" name="RpcRemoveUGCEntranceBubblesRes" serverName="ugcplatsvr"/>
  <entry id="4122" name="RpcSpGameSvrHeartBeatNtfReq" serverName="starpgamesvr"/>
  <entry id="4123" name="RpcSpGameSvrHeartBeatNtfRes" serverName="starpgamesvr"/>
  <entry id="4126" name="RpcArenaGetBattleRecordsReq" serverName="gamesvr"/>
  <entry id="4127" name="RpcArenaGetBattleRecordsRes" serverName="gamesvr"/>
  <entry id="4130" name="RpcCreateGameSessionResultEventReq" serverName="battlesvr"/>
  <entry id="4131" name="RpcCreateGameSessionResultEventRes" serverName="battlesvr"/>
  <entry id="4132" name="RpcGameSessionEndEventReq" serverName="battlesvr"/>
  <entry id="4133" name="RpcGameSessionEndEventRes" serverName="battlesvr"/>
  <entry id="4134" name="RpcGetCardSuitInfoReq" serverName="arenasvr"/>
  <entry id="4135" name="RpcGetCardSuitInfoRes" serverName="arenasvr"/>
  <entry id="4138" name="RpcAllocUniqueIdReq" serverName="gamesvr"/>
  <entry id="4139" name="RpcAllocUniqueIdRes" serverName="gamesvr"/>
  <entry id="4142" name="RpcModifyMatchTeamInfoReq" serverName="matchsvr"/>
  <entry id="4143" name="RpcModifyMatchTeamInfoRes" serverName="matchsvr"/>
  <entry id="4158" name="RpcRoomModifyMatchingTeamInfoResultNtfReq" serverName="roomsvr"/>
  <entry id="4159" name="RpcGameModifyMatchingTeamInfoResultNtfReq" serverName="gamesvr"/>
  <entry id="4162" name="RpcRoomModifyMatchingTeamInfoReq" serverName="roomsvr"/>
  <entry id="4163" name="RpcRoomModifyMatchingTeamInfoRes" serverName="roomsvr"/>
  <entry id="4173" name="RpcUgcApplyKeyInfoReq" serverName="ugcsvr"/>
  <entry id="4174" name="RpcUgcApplyKeyInfoRes" serverName="ugcsvr"/>
  <entry id="4186" name="RpcFarmCreateGameSessionResultEventNtfReq" serverName="farmsvr"/>
  <entry id="4187" name="RpcFarmGameSessionEndEventNtfReq" serverName="farmsvr"/>
  <entry id="4191" name="RpcGetUgcRankInfoWithTimeNoCacheReq" serverName="ugcdatastoresvr"/>
  <entry id="4192" name="RpcGetUgcRankInfoWithTimeNoCacheRes" serverName="ugcdatastoresvr"/>
</root>
