syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "common.proto";
import "base_common.proto";
import "ss_head.proto";
import "ss_common.proto";
import "plat_common.proto";
import "arena_sdk_share.proto";
import "ResKeywords.proto";

message TestUgcPlatSvrReq {
  optional int32 testid = 1;
}

message TestUgcPlatSvrRes {
  optional int32 result = 1;
}

enum EventType {
  Create = 20001;
  Update = 20002;
  Delete = 20003;
  Cover = 20004;
  ResDraft = 20005;
  UpdateBasicInfo = 20006; // 更新基础信息(管理端不依赖这个类型的上报数据来更新地图状态)

  Like = 10001;     //点赞
  Cancel_like = 10002; //取消点赞
  Collect = 10003;//收藏
  Cancel_collect = 10004;
  Visit = 10005;//访问
  Play = 10006;//游玩
  Share = 10007; //分享
  Complain = 10008; //举报
  Use = 10009; //使用，地图过审后统计（组合）
  PublishUse = 10010; //使用，地图发布后统计（组合）
  Refer = 10011;   // 资源被加入资源库
  Cancel_refer = 10012; // 资源被移出资源库
  Label_Score = 10013; // 标签分数更新
  Rank_Update = 10014; // Ugc排行榜上报
  Map_Group_Notice = 10015; // 违规资产重新计算通知
  Warm_Data_Update = 10016;  // 温暖数据更新

  Login = 30001;
  Logout = 30002;
  Follow = 30003;//关注
  CancelFollow = 30004;//取消关注
  CreatorLevelUpdate = 30005;//成长值更新
  RemoveAccount = 30006; // 注销账号

  BattleEnd = 40001;

  PlayerInfo_Login = 50001;  //玩家登录
  PlayerInfo_Logout = 50002;  //玩家登出
  PlayerInfo_Lbs = 50003;   // 玩家LBS信息更新
  PlayerInfo_Intimacy = 50004;  // 玩家亲密度更新
  PlayerInfo_DressUp = 50005;   // 换装
  PlayerInfo_BindRelation = 50006;   // 绑定亲密关系
  PlayerInfo_UnbindRelation = 50007;  // 解除亲密关系
  PlayerInfo_ItemInfo = 50008;  // 用户道具更新

  BattleInfo_Start = 60001;  // 对局开始
  BattleInfo_End = 60002;    // 对局结束

  ET_UgcCollectionCreate = 70001;     // 创作者创建合集
  ET_UgcCollectionUpdate = 70002;     // 创作者更新合集
  ET_UgcCollectionDelete = 70003;     // 创作者删除合集
  ET_UgcCollectionDataChange = 70004; // 合集数据变更(游玩数, 收藏数等)

  PlayerInfo_FarmEnterExit = 80001; // 农场进出
  PlayerInfo_FarmLeaveMessage = 80002; // 农场留言
  PlayerInfo_FarmSteal = 80003; // 农场偷菜
  PlayerInfo_FarmFertilize = 80004; // 农场祈愿
  PlayerInfo_FarmCropStatus = 80005; // 农场养殖物状态
  PlayerInfo_FarmGift = 80006; // 农场送礼
  PlayerInfo_FarmFish = 80007; // 农场鱼获上报
  PlayerInfo_FarmHarvest = 80008; // 农场收获（作物动物加工器）
  PlayerInfo_FarmHotSpring = 80009; // 农场泡温泉
  PlayerInfo_FarmCloudMarket = 80010; // 农场云游商人
  PlayerInfo_FarmBuff = 80011; // 农场Buff
  PlayerInfo_FarmHotSpringRefresh = 80012; // 农场温泉刷新
}

enum ReportSource {
  Map = 0; //地图
  Composition = 1; //组合

}


message RpcMapReportReq {
  optional int64 creatorId = 1;
  optional int64 mapId = 2;
  optional int32 eventType = 3; // 协议改造 枚举值切换至整数值 详情参考EventType
  optional int64 eventTime = 4;
  optional ReportMapBaseData baseData = 5;
  optional ReportMapCreatorData creatorData = 6;
  optional string bucket = 7;
  optional int32 source = 8;
  optional int32 idipArea = 9;
  repeated ReportVersionInfoData versionData = 10;
  repeated int32 ruleTags = 11;
  repeated int32 playTags = 12;
  repeated UgcMapTopic goldTopics = 13;
  repeated UgcMapTopic blueTopics = 14;
  repeated UgcEditorInfo coCreators = 15;
  repeated UgcEditorInfo signedCreators = 16;
  optional int32 teamMode = 17;
  optional string mapDataUrl = 18;
  repeated UgcGoodsInfo goodsInfo = 19;   // 商品信息列表
  repeated UgcRankInfo rankInfo = 20;      // 排行榜基本信息
  optional UgcDatastoreAccessedPermissionInfo datastoreAccessedPermissionInfo = 21; //可访问当前地图的UGC地图信息列表
}

message RpcMapReportRes {
  optional int32 result = 1;
}

message VideoCoverInfoReport {
  optional string videoUrl = 1;
}

message ReportMapBaseData {
  optional int64 mapId = 1;
  optional string name = 2;
  optional string set = 3;
  optional int32 reviewStatus = 4;
  optional int32 type = 6;
  repeated int32 tagIds = 7;
  optional string desc = 8;
  repeated int64 groupIds = 9;
  optional int64 oldMapId = 10;
  optional int32 deviceLevel = 11;
  optional int64 curVersion = 12;
  optional int64 auditVersion = 13;
  optional int32 occupancyValue = 14; //地图占用值
  optional int32 propertyScore = 15; //性能评分
  optional int32 actorCount = 16; //actor数量
  optional UgcMdList mdList = 17;
  optional int32 ugcResType = 18;      // 资源类型
  optional int32 resCategory = 19;     // 资源大类
  optional int32 resSubCategory = 20;  // 资源子类
  repeated int32 resLabels = 21;       // 资源标签
  optional bool isEditable = 22; //是否可编程 （单纯记录 策划需求不明确 不与tags一起存放）
  optional bool isOpenHealth = 23;  // 是否打开生命值
  optional int32 publishGoodsStatus = 24;  // 星钻总开关状态
  optional int32 buyGoodsStatus = 25;    // 单次星钻购买审核状态
  optional bool isDanMuUnable = 26;  // 是否打开弹幕
  optional bool isAllowMidJoin = 27;  // 是否中途加入
  optional int32 modelType = 28;  // 当前地图模式
  optional int32 dimLabelId = 29;
  optional int32 comLabelId = 30;
  optional bool isAiGen = 31; // 是否是AI生成的
  optional bool isApplyTakeOff = 32; // 是否删除下架中
  optional string applyTakeOffReason = 33;
  optional bool isContainDiyArms = 34;  // 是否包含自制装备
  optional UgcDatastoreAccessedPermissionInfo dataStoreAccessedPermissionInfo = 35; //可访问当前地图的UGC地图信息列表
  optional int32 source = 36;  //地图来源
  optional bool isLuaCoding = 37;  //是否用lua编程
  optional int32 evaluationStatus = 38;  // 地图评估状态
  optional int64 lastResetPlayDataTime = 39; // 上一次重置游玩数据时间，毫秒
  optional int64 lastResetPlayDataVersion = 40;  // 上一次重置游玩数据的版本号
  optional VideoCoverInfoReport videoCoverInfo = 41;  // 视频封面
  optional bool isUsingLua = 42;  //是否编程元件地图
}

message ReportMapCreatorData {
  optional string creatorName = 1;
  optional string openId = 2;
  optional int64  templateId = 3;
  optional int32 createDuration = 4;
  optional int64 createAt = 5;
  optional int64 publishAt = 6;
  optional int64 updateAt = 7;
  optional int32 updateDuration = 8;
  optional int64 offlineAt = 9;
  optional string offlineReason = 10;
  optional int32 platId = 11;
  optional int32 loginType = 13;
  optional string secretKey = 14;
  optional int64 uid = 15;
  optional string mapDataMd5 = 16;            // md5
}

message ReportVersionMapInstruction {
  optional string text = 1;
  repeated string pic_list = 2;
  repeated string text_list = 3;
}

message ReportMapLoading {
  optional string pic_url = 1;  // 图片
  optional bool use_official = 2;  // 是否使用官方
}

message ReportVersionInfoData {
  optional int64 version = 1;                 // 版本号
  optional int32 status = 2;                  // 地图状态
  optional int32 template_id = 3;             // 使用模板ID
  optional int64 publish_at = 4;              // 发布时间（ms）
  optional int64 update_at = 5;               // 更新时间 (ms)
  optional int32 update_duration = 6;         // 更新耗时
  optional string map_data_url = 7;           // 地址
  optional string map_data_md5 = 8;           // md5
  optional string cover = 9;                  // 封面url
  repeated string coverList = 10;             // 多封面列表,
  optional int32 mainCoverIndex = 11;         // 主封面下标
  repeated int64 resIds = 12;                 // 引用的资产id
  repeated LayerBaseInfo layerInfo = 13;      // 引用的资产id
  optional ReportVersionMapInstruction map_instruction = 14;  // 地图说明
  optional ReportMapLoading mapLoading = 15;  // 主场景自定义loading
  optional string descUrl = 16;
  optional string levelResUrl = 17;
}

message RpcCreatorReportReq {
  option (region_msg) = true;
  optional int64 creatorId = 1;
  optional int32 eventType = 3;// 协议改造 枚举值切换至整数值 详情参考EventType
  optional int64 eventTime = 4;
  optional string openId = 5;
  optional string nickname = 6;
  optional string headUrl = 7;
  optional string lbs = 8;
  optional int32 creatorLevel = 9;
  optional string creatorLevelTitle = 10;
  optional int64 fansNum = 11;
  optional int32 map_num = 12;
  optional int32 source = 13; // 来源1:gameSvr,2:ugcSvr
  optional int32 platId = 14;
  optional int32 isCreator = 15; // ⽤户是否创作者 0：否 1：是
  optional int32 gender = 16;
  optional int32 login_type = 18;
  optional int32 worldId = 19[(field_region_key) = true];
  optional int64 uid = 20;
  optional int32 idipArea = 21;
  optional int64 ugcExp = 22;
  optional int32 instanceType = 23; // 地图、组合
  optional int64 ugcId = 24;
  optional bool isRankBanned = 25; // 排行榜是否被封禁
  optional int32 season_map_level = 26;
  optional int32 season_id = 27;
  optional int64 short_uid = 28; // 短序号id gamesvr---》管理端
  optional int64 draft_box_limit = 29; //  草稿箱
  optional int64 publish_box_limit = 30; //草稿箱已发布
  optional int32 playerReturnType = 31; // 0否 1回流玩家
}

message RpcCreatorReportRes {
  optional int32 result = 1;
}

message RpcMapPlayerReportReq {
  option (region_msg) = true;
  optional int64 creatorId = 1; // 游玩玩家creatorId
  optional int32 eventType = 3;// 协议改造 枚举值切换至整数值 详情参考EventType
  optional int64 eventTime = 4;
  optional string openId = 5;
  optional int64 mapId = 6;
  optional uint64 complainNum = 15;
  optional int32 platId = 18;
  optional int32 source = 19;
  optional int32 loginType = 21;
  optional UgcPlayerMapData playerMapData = 22;
  optional int64 uid = 23;
  optional int32 gameSource = 24;
  optional bool isPass = 25;
  optional int32 playDuration = 26;
  repeated int64 mapIds = 27;
  optional UgcResParam ugcResParam = 28;
  optional bool isMidJoin = 29;
  optional int32 operateSvrSource = 30;     // 操作服务来源, 见枚举OperateSvrSourceType所示
  optional int64 editorCreatorId = 31;  // 地图作者creatorId
  optional int64 appCreatorId = 32; // 游玩玩家独立app的creatorId
  optional string appOpenId = 33;// 游玩玩家独立app的OpenId
  optional int32 reqFrom = 34;  // 事件来源，0游戏，1独立app
  optional bool isWarm = 35;  // 是否是温暖数据
  repeated int32 subEventTypes = 36;  // 子事件类型
  optional int32 multiRoundScore = 37;  // 多轮次地图当前积分
}

message RpcMapPlayerReportRes {
  optional int32 result = 1;
}

message RpcRoomReportReq {
  optional int64 battle_id = 1 [(field_hash_key) = true];
  optional int32 eventType = 2;// 协议改造 枚举值切换至整数值 详情参考EventType
  optional int64 eventTime = 3;
  optional UgcBattleData battleData = 4;
}

message RpcRoomReportRes {
  optional int32 result = 1;
}

message UgcBattleData {
  optional int32 battleType = 1;//竞速
  repeated UgcBattlePlayerData mapData = 2; //每个地图的玩家结果
}

message UgcBattlePlayerData {
  optional int64 uid = 1;
  optional string openId = 2;
  optional int32 playerDuration = 3;
  optional int32 rank = 4;
  optional int32 score = 5;
  optional bool isPass = 6;
  optional int32 mapId = 7;
  optional int32 platId = 8;
}

message UgcPlayerDataReportReq {
  optional int64 uid = 1 [(field_hash_key) = true];
  optional int64 mapId = 2;
  optional int32 eventType = 3;// 协议改造 枚举值切换至整数值 详情参考EventType
  optional int64 eventTime = 4;
}

message UgcPlayerDataReportRes {
  optional int32 result = 1;
}

message UgcServletMsgReq {
  optional string cmd = 1;
  optional string body = 2;
}

message UgcServletMsgRes {
  optional int32 result = 1;
  optional string content = 2;
}

message RpcUgcGetPageListMsgReq {
  optional int64 uid = 1;
  optional string openId = 2;
  optional UgcRecommendType recommendType = 3;
  optional int32 page = 4;
  optional int32 type = 5;
  optional int32 tag = 6;
  optional int32 isAdult = 7;
  optional string lbs = 8;
  optional int32 isGuide = 9; //是否是新手
  optional int32 source = 10; //0地图,1组合
  repeated int32 multiTags = 11;  // 多标签，目前只有组合使用
  optional int32 deviceLevel = 12;  // 机型
  optional UgcResParam ugcResParam = 13; // 资源参数
  optional TopicInfo topic = 14; //话题
}

message RpcUgcGetPageListMsgRes {
  optional int32 result = 1;
  repeated PageMapInfo mapIdList = 2;
  optional AlgoInfo info = 3;
  repeated HotPlayList typeList = 4;

}

message RpcUgcRoomGetOfficalRecommendListReq {
  optional string tab_id = 1;  // tab
  optional int32 page = 2;
  optional int64 uid = 3;
  optional string openId = 4;
}

message RpcUgcRoomGetOfficalRecommendListRes {
  optional int32 result = 1;
  repeated UgcRoomOfficalRecommendMapIdInfo recommendInfos = 2; //UGC房间地图列表
  repeated UgcRoomOfficalRecommendTabInfo tabInfos = 3; //页签信息，每次请求都返回
  optional AlgoInfo info = 4;
  optional int32 hasMorePage = 5; //1:表示还有下一页，0比没有下一页
}


message RpcSearchReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int32 page = 3;
  optional string keyWord = 4;
  optional int32 source = 5;
  repeated int32 tags = 6;
  optional int64 uid = 7;
  optional UgcResParam ugcResParam = 8; // 资源参数
}

message RpcSearchRes {
  optional int32 result = 1;
  repeated int64 mapIdList = 2;
  optional AlgoInfo info = 3;
}

message RpcUgcGetUserRankReq {
}

message RpcUgcGetUserRankRes {
}

message RpcUgcFetchRankReq {
  option (region_msg) = true;
  optional UgcCreatorRankId rankId = 1;
}

message RpcUgcFetchRankRes {
  optional int32 result = 1;
  repeated TopRankInfo ranks = 2;
}

message RpcGetDailyMapsReq {
  option (region_msg) = true;
  optional int64 uid = 1;
  optional string openId = 2;
  optional int64 roundId = 3;
  optional int32 stepId = 4;
  optional bool isChangeMap = 5;
  optional int32 failCount = 6;
  optional SingleStageType singleType = 7; //单人闯关类型
  optional string clientVersion = 8;
}

message RpcGetDailyMapsRes {
  optional int32 result = 1;
  optional int64 recommendMapId = 2; // 推荐的地图ID
  repeated int64 backupMapIds = 3; // 备份的地图ID列表
  optional AlgoInfo info = 4;
}

message RpcGetRecommendSubsReq {
  option (region_msg) = true;
  optional int64 uid = 1;
  optional string openId = 2;
  optional int64 creatorId = 7;
}

message RpcGetRecommendSubsRes {
  optional int32 result = 1;
  repeated UgcOpPlayerInfo infos = 2; //废弃
  optional AlgoInfo info = 3;
  repeated int64 recommendCreatorIds = 4;
}

// 是否在创作者白名单
message RpcUgcIsInWhiteListReq {
  optional int64 uid = 1;
  optional string openId = 2;
  optional int64 creatorId = 3;
}

message RpcUgcIsInWhiteListRes {
  optional int32 result = 1;
  optional bool isInWhiteList = 2;
}

message UgcCreatorRankListData {
  optional int32 code = 1;
  optional string msg = 2;
  optional UgcCreatorRankList data = 3;
}

message SyncUgcMapTopicsReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int32 source = 3;
}

message SyncUgcMapTopicsRes {
  optional int32 result = 1;
  optional PlatSyncMapTopicsData data = 2;
}

// 从操作过的集合中获取地图
message RpcGetUserOperateMapListReq {
  optional uint64 creatorId = 1 [(field_hash_key) = true];
  optional uint64 uid = 2;
  optional string openId = 3;
  optional UgcInstanceType mapType = 4;
  optional UgcOpType opType = 5;
  optional string keyword = 6;
  repeated int32 tags = 7;
  optional int32 page = 8;
  optional UgcResParam ugcResParam = 9; // 资源参数
}

message RpcGetUserOperateMapListRes {
  optional int32 result = 1;
  repeated int64 mapIdList = 2;
  optional bool hasMore = 3;
}

message RpcGetHomeRecommendReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional bool needGuide = 3;
  optional int32 abtestId = 4;
  optional int32 tabId = 5;
  optional int32 loginPlat = 6;  // 玩家登陆平台,对应PlayerLoginPlat
  optional string abTestInfo = 7;
  repeated int32 likedTagIdList = 8; // 玩家选择的标签id列表
}

message RpcGetHomeRecommendRes {
  optional int32 result = 1;
  optional UgcHomeRecommendOverviewInfo info = 2;
}

message RpcGetHomeRecommendDetailReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int32 page_type = 3; // 0=每周必玩/1=每月必玩/2=精选合集
  optional int32 order_type = 4; // 排序类型
  optional int32 page = 5; // 页数
}

message RpcGetHomeRecommendDetailRes {
  optional int32 result = 1;
  repeated UgcHomeRecommendItemInfo item_list = 2;
  optional RecommendAlgoInfo algo_info = 3;
  optional bool has_more = 4; // 是否还有下一页
  optional int32 subscribeStatus = 5; // 0=未订阅；1=已订阅
}

message RpcGetHomeRecommendHotTagsReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openId = 2;
}

message RpcGetHomeRecommendHotTagsRes {
  optional int32 result = 1;
  optional UgcHomeRecommendHotTagsData info = 2;
}

message RpcGetRecommendSetReq {
  option (region_msg) = true;
  option (rpc_call_timeout_seconds) = 3;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string setType = 2;

}

message RpcGetRecommendSetRes {
  optional int32 result = 1;
  optional UgcRecommendSetInfo info = 2;
}

message SyncPlatRoomInfoReq {
  option (rpc_one_way) = true;
  option (region_msg) = true;
  optional int64 roomId = 1 [(field_hash_key) = true];
  optional RoomInfo roomInfo = 2;
  optional int32 noticeType = 3; // plat_common.proto:RoomNoticeType
}

message SyncPlatRoomInfoRes {

}

message RpcGetHotPlayingUGCMapListReq{
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int32 page = 3;
}

message RpcGetHotPlayingUGCMapListRes {
  optional int32 result = 1;
  optional AlgoInfo algo_info = 2;
  repeated UgcHotPlayingStoryInfo story_list = 3; // 好友动态。page=-1时才有
  repeated UgcHotPlayingMapInfo map_list = 4; // 好友在玩地图
}

message RpcPlayerInfoReportNtfReq {
  option (region_msg) = true;
  option (rpc_one_way) = true;
  optional int64 uid = 1;
  optional int32 event_type = 2;// 协议改造 枚举值切换至整数值 详情参考EventType
  optional PlayerReportLbsInfo lbs_info = 3;   // lbs信息，50003必传
  optional PlayerReportIntimacyInfo intimacy_info = 4;  // 亲密度信息，50004必传
  repeated int64 uid_list = 5;    // 合并发送login/logout
  optional PlayerReportFarmEnterExitInfo farm_enter_exit_info = 6; // 80001农场进出信息
  optional PlayerReportFarmLeaveMessageInfo farm_leave_message_info = 7; // 80002农场留言信息
  optional PlayerReportFarmStealInfo farm_steal_info = 8; // 80003农场偷菜信息
  optional PlayerReportFarmFertilizeInfo farm_fertilize_info = 9; // 80004农场祈愿信息
  optional PlayerReportFarmCropStatusInfo farm_crop_status_info = 10; // 80005农场养殖物状态信息
  optional PlayerReportFarmGiftInfo farm_gift_info = 11; // 80006农场送礼信息
  optional PlayerReportFarmFishInfo farm_fish_info = 12; // 80007农场鱼获信息
  optional PlayerReportFarmHarvestInfo farm_harvest_info = 13; // 80008农场收获信息（作物动物加工器）
  optional PlayerReportFarmHotSpringInfo farm_hot_spring_info = 14; // 80009农场泡温泉信息
  optional PlayerReportFarmCloudMarketInfo farm_cloud_market_info = 15; // 80010农场云游商人信息
  optional PlayerReportFarmBuffInfo farm_buff_info = 16; // 80011农场Buff信息
  optional PlayerReportFarmHotSpringRefreshInfo farm_hot_spring_refresh_info = 17; // 80012农场温泉刷新信息
  optional PlayerReportItemInfo item_info = 18; // 道具数据更新
}

message RpcPlayerInfoReportNtfRes {
  optional int32 result = 1;
}

message RpcBattleInfoReportNtfReq {
  option (region_msg) = true;
  option (rpc_one_way) = true;
  optional int64 battle_id = 1 [(field_hash_key) = true];      // 对局id
  optional int32 event_type = 2;// 协议改造 枚举值切换至整数值 详情参考EventType
  optional int32 battle_type = 3;       // 对局玩法
  repeated PlayerReportBattleInfo player_list = 4;       // 对局玩家列表
  optional BattleReportResult battle_result = 5;     // 对局结果，60002必填
  repeated int32 level_id_list = 6;     // 对局内所有关卡id，60002必填
}

message RpcGetRecommendAddFriendListReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true]; // 申请人uid
  optional string openId = 2;
  optional int64 sessionId = 3; // 算法在同会话内尽量保证去重
  optional int32 limit = 4; // 最多应答条数
  optional int32 gender = 5; // 性别
  optional int32 scene_type = 101; // 场景类型。0=好友默认推荐列表；1=UGC地图社交控件陌生人推荐列表
  optional int32 svr_id = 6; // 服务器小区ID
  optional uint64 map_id = 7; // 地图ID
}

message RpcGetRecommendAddFriendListRes {
  optional int32 result = 1;
  optional AlgoRecommendFriendInfo info = 2;
}

message RpcGetRecommendAddFriendReasonReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true]; // 申请人uid
  optional string openId = 2;
  optional int32 module = 3;     // 一级场景，枚举值
  optional int32 subModule = 4; // 二级场景，枚举值
  optional int64 dstUid = 5;    // 被申请人uid
}

message RpcGetRecommendAddFriendReasonRes {
  optional int32 result = 1;
  optional AlgoRecommendAddFriendReasonInfo recommendInfo = 2;
}

message RpcGetRecommendIntimacyListReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true]; // 申请人uid
  optional string openId = 2;
}

message RpcGetRecommendIntimacyListRes {
  message RecItem {
    optional int64 uid = 1;
    optional int32 relationship = 2;  // 关系标签, 1: 兄弟, 2: 闺蜜, 3: 密友
    repeated int32 intimacy_delta = 3; // 最近n天每日亲密度变化值列表
  }

  optional int32 result = 1;
  optional RecommendAlgoInfo algo_info = 2;
  optional int64 updateTimeMs = 3;  // 算法侧亲密度更新时间戳（如果时间戳为N日，则最新一天的新密度为N-1日数据）
  repeated RecItem rec_list = 4;
}

message RpcGetAchievementCompleteStatusReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true]; // 申请人uid
  optional string openId = 2;
  repeated string task_ids = 3;  // 成就任务id列表
}

message RpcGetAchievementCompleteStatusRes {
  message Task {
    optional string task_id = 1; // 成就任务id
    optional int64 achievement_num = 2;  // 成就任务完成人数
  }

  optional int32 result = 1;
  optional int64 total = 2; // 玩家总数
  repeated Task tasks = 3;  // 成就任务完成数据
}

message RpcUgcMapShowLimitNtfReq {
  option (rpc_one_way) = true;
  optional int64 ugcId = 1 [(field_hash_key) = true];
  optional int32 conditionId = 2;
  optional int64 versionId = 3;
}

message RpcGetRecommendBattleFriendListReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string open_id = 2;
  optional int64 battle_id = 3;
  optional int32 battle_type = 4;
  repeated int64 uid_list = 5;
}

message RpcGetRecommendBattleFriendListRes {
  optional int32 result = 1;
  optional AlgoInfo algo_info = 2;
  repeated AlgoRecommendFriendItem rec_list = 3;
}

message RpcSyncChatMsgFromQQReq {
  option (region_msg) = true;

  message ChatMsg {
    optional ChatMsgType msg_type = 1; // 消息类型
    optional string text = 2;
    optional string rich_text = 3; // 富文本
  }

  optional uint64 sender_uid = 1; // 发送人的 uid
  optional uint64 receiver_uid = 2; // 接收人的 uid
  optional ChatMsg msg = 3; // 消息内容
  optional uint32 area = 4; // worldId，服
}

message RpcSyncChatMsgFromQQRes {
  optional int32 result = 1;
  optional string msg = 2;
}

// 获取赛季激励置顶的标签信息
message RpcGetExcitationTagsReq {
  option (region_msg) = true;
}

message RpcGetExcitationTagsRes {
  optional int32 result = 1;
  optional UgcExcitationTags tagInfo = 2;    // 激励标签
}

//搜索前置页面
message RpcPlatUgcMapSearchFrontReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
  optional int32 deviceLevel = 4; // 机型评级 DeviceLevel
}

//搜索前置页面 跟管理端结构一样 方便序列化
message RpcPlatUgcMapSearchFrontRes {
  optional int32 result = 1;
  optional HotResult hot_result = 2; // 热搜榜
  optional HotMapResult hot_map_result = 3; // 热搜地图
  optional RecommendMapResult recommend_map_result = 4; // 猜你喜欢
  optional HotTopicResult hot_topic_list = 5; //热门话题列表
}

message HotTopicResult{
  repeated UGCSearchTopicInfo hot_topic_list = 1; // 热门话题列表
}

message Hot {
  optional string text = 1; // 热搜文字
  optional bool is_new = 2; //是否为【新】
  optional bool is_hot = 3; //是否为【热】
}

message HotResult {
  repeated Hot hot_list = 1;
  optional Hot suggest_hot = 2; // 默认显示在搜索框内的热搜词，跟hot_list不重复
}

message RecommendAlgoInfo {
  optional string recid = 1; // 推荐ID
  optional string exp_tag = 2; // 实验标签
}

message HotMap {
  optional uint64 map_id = 1; // 地图ID
  optional string hot_score = 2; // 地图热度/文字
}

message HotMapResult {
  repeated HotMap hot_map_list = 1;
  optional RecommendAlgoInfo algo_info = 2; // 推荐算法信息
}

message RecommendMap {
  optional uint64 map_id = 1; // 地图ID
}



message RecommendMapResult {
  repeated RecommendMap recommend_map_list = 1;
  optional RecommendAlgoInfo algo_info = 2; // 推荐算法信息
}

message RpcSearchInstanceReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
  optional string keyWord = 4;
  optional int32 page = 5;
  optional int32 searchType = 6;
  optional string sessionId = 7;
}

message RpcSearchInstanceRes {
  optional int32 result = 1;
  optional RecommendAlgoInfo algoInfo = 2; // 推荐算法信息
  optional MixResult mixResult = 3; // 综合搜索列表
  optional MapResult map_result = 4; // 地图搜索列表
  optional CreatorResult creator_result = 5; // 创作者列表
  optional TopicResult topic_result = 6; // 话题列表
  optional string sessionId = 7;
  optional CollectionResult collection_result = 8;
}

message MixMap {
  optional uint64 map_id = 1;
}

message UGCSearchTopicInfo {
  optional string topic_name = 1; // 话题名称
  optional uint64 like_cnt = 2; // 点赞数
  optional uint64 play_cnt = 3; // 游玩数
  optional uint64 map_cnt = 4; // 地图数
  optional uint32 topic_type = 5; // 0-蓝色话题；1-金色话题
  optional string jump_url = 6; //跳转链接
}

message MixResult {
  repeated MixMap mix_map_list = 1;
  optional UGCSearchTopicInfo topic_info = 2; // 当key_word为#开头时，如果精确命中话题，则展示话题详情入口
  optional uint64 creator_id = 3; // 如果key_word精确命中创作者，则返回创作者ID
  repeated MixMap recommend_mix_map_list = 4; // 如果没有搜索到任何地图，则兜底展示一些推荐地图，此时也没有下一页
}

message MapInfo{
  optional uint64 map_id = 1; // 地图ID
}

message MapResult {
  repeated MapInfo map_list = 1; // 地图列表
  optional uint32 cnt = 2; // 总数
  repeated MixMap recommend_mix_map_list = 3; // 如果没有搜索到任何地图，则兜底展示一些推荐地图，此时也没有下一页
}

message Creator {
  optional uint64 creator_id = 1; // 创作者ID（非UID）
}

message CreatorResult {
  repeated Creator creator_list = 1; // 创作者列表
  optional uint32 cnt = 2; // 总数
}

message TopicResult {
  repeated UGCSearchTopicInfo topic_list = 1;
  optional uint32 cnt = 2; // 总数
}

message CollectionResult {
  message Collection {
    optional string collection_id = 1;            // 合集ID
  }
  repeated Collection collection_list = 1;        // 合集列表
}

//话题详情
message RpcPlatUgcSearchTopicReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
  optional string topicName = 4;
  optional int32 page = 5;
  optional int32 topicType = 6;
}

//话题详情 跟管理端结构一样 方便序列化
message RpcPlatUgcSearchTopicRes {
  optional int32 result = 1;
  optional UGCSearchTopicInfo topic_info = 2; // page=0时返回
  repeated MapInfo map_list = 3; // 地图列表
  optional RecommendAlgoInfo algo_info = 4; // 推荐算法信息
}

message RpcPlatUgcGetSceneGroupInfoReq {
  option (region_msg) = true;
  optional int64 clubId = 1 [(field_hash_key) = true];
  optional uint32 scene = 2; // 1 星团 2 粉丝群
  optional string name = 3;
  optional string openId = 4;
  optional int64 uid = 5;
}

message RpcPlatUgcGetSceneGroupInfoRes {
  optional int32 result = 1;
  optional string msg = 2;
  optional string groupUnionId = 3;
  optional string groupUnionName = 4;
  optional string zoneId = 5;
  optional string areaId = 6;
}

message RpcGetUgcRemoteConfigReq {
  option (region_msg) = true;
  repeated com.tencent.wea.xlsRes.UgcRemoteConfigType types = 1;
}

message RpcGetUgcRemoteConfigRes {
  optional int32 result = 1;
  optional UgcRemoteConfig config = 2;
}

message RpcUgcBattleSettlementMapRecommendReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
  optional UgcRecommendGameInfo gameInfo = 4;
  optional int32 deviceLevel = 5; // 机型评级 DeviceLevel
}
message RpcUgcBattleSettlementMapRecommendRes {
  optional int32 result = 1;
  repeated UgcRecommendMap rcmd_map_list = 2;
  optional MgrAlgoInfo algo_info = 3; // 推荐算法信息
}

message RpcClubNameOperateReq {
  option (region_msg) = true;
  optional int64 routeId = 1 [(field_hash_key) = true];
  optional string clubName = 2;
  optional int64 clubId = 3;
  optional int32 type = 4;// 协议改造 枚举值切换至整数值 详情参考
  enum OperateType {
    OT_Add = 1;
    OT_Remove = 2;
    OT_GetExtraInfo = 3;
  }
  optional bool remove = 9; // 废弃
}

message RpcClubNameOperateRes {
  optional int32 result = 1;
  optional string extraInfo = 2;
}

// 资源社区首页推荐
message RpcPlatResHomePageRecommendReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
}

message RpcPlatResHomePageRecommendRes {
  optional int32 result = 1;                    // 结果
  optional bool hasBanner = 2;                  // 是否有横幅信息
  optional UgcResHomePagBannerInfo bannerInfo = 3;   // 横幅信息
  repeated UgcResHomePageSetIds resSet = 4;      // 资源Id集合(合集)
  optional AlgoInfo alogInfo = 5;                   // 推荐算法信息
}

// 资源背包拉取/搜索
message RpcUgcResBagGetOrSearchReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 page = 2;                     // 页号
  optional UgcResParam ugcResParam = 3;        // 参数
  optional string keyword = 4;                 // 关键词 搜索时用
  optional int64 uid = 5;                      // uid
  optional string openId = 6;                  // openid
}

message RpcUgcResBagGetOrSearchRes {
  optional int32 result = 1;      // 结果
  repeated int64 ugcIds = 2;      // 列表
  optional bool hasMore = 3;      // 是否还有
}

// 资源社区搜索
message RpcPlatResCommunitySearchReq {
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int32 page = 2;
  optional UgcResParam ugcResParam = 3; // 资源参数
  optional string keyword = 4;          // 搜索关键词
  optional int64 uid = 5;               // uid
  optional string openId = 6;           // openid
  optional string sessionId = 7;
}

message RpcPlatResCommunitySearchRes {
  optional int32 result = 1;
  repeated int64 mapIdList = 2;
  optional AlgoInfo info = 3;
  optional string sessionId = 4;
}

message RpcPlatSearchSuggestionReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
  optional SearchType searchType = 4;
  optional string inputText = 5;
}

message RpcPlatSearchSuggestionRes {
  optional int32 result = 1;
  repeated string keyWord = 2;
  optional AlgoInfo info = 3;
}

message IntellectualActivity {
  optional int32 activityId = 1;    // 活动场次id
  optional int32 type = 2;          // 1~5
  repeated int64 players = 3;       // 玩家列表
  optional int32 playerNum = 4;     // 实时剩余人数
}

message RpcMapActivePublishGoodsReq {  // 申请激活发布商品能力
  optional int64 mapId = 1;
}

message RpcMapActivePublishGoodsRes {
  optional int32 result = 1;
}

message RpcClubGetRecommendListReq {
  option (region_msg) = true;
  optional int64 uid = 1[(field_hash_key) = true];
  optional string openId = 2;
  optional string worldId = 3;
}

message RpcClubGetRecommendListRes {
  optional int32 result = 1;
  repeated int32 tags = 2;
  repeated RecommendInfo clubs = 3; // 废弃
  optional string algo = 4;
  optional AlgoInfo algoInfo = 5;
  repeated Entry entries = 6;

  message RecommendInfo {
    optional int32 reason = 1;
    repeated int64 clubIds = 2;
  }

  message Entry {
    optional int32 reason = 1;
    optional int64 clubId = 2;
  }
}

message RpcClubSearchByTagReq {
  option (region_msg) = true;
  optional int64 uid = 1[(field_hash_key) = true];
  repeated int32 tags = 2;
  optional string openId = 3;
  optional string worldId = 4;
}

message RpcClubSearchByTagRes {
  optional int32 result = 1;
  repeated int64 clubIds = 2; //废弃
  repeated SearchEntry entries = 3;
  optional string algo = 4;
  optional AlgoInfo algoInfo = 5;

  message SearchEntry {
    optional int64 clubId = 1;
    optional int32 reason = 2;
  }
}

message RpcGetUgcCoPlayRecommendMapListReq {
  option (region_msg) = true;
  optional int64 roomId = 1;
  optional string sessionId = 2;                     //用来区分同房间的不同推荐，不同的round推荐隔离
  repeated UgcCoPlayRecommendUserInfo userList = 3;
  repeated UgcCoPlayUsedMap usedMapList = 4;    //已经使用过的地图信息
  optional int32 recommendCount = 5;            //需要推荐的UGC地图数量
}

message RpcGetUgcCoPlayRecommendMapListRes {
  optional int32 result = 1;
  repeated UgcCoPlayRecommendMap recommendMapList = 2; // 推荐的地图ID
  optional AlgoInfo info = 3;
}
message RpcUgcCollectionReportReq {
  optional string collectionId = 1;
  optional CollectionReport report = 2;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcCollectionReportRes {
  optional int32 result = 1;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcBuyGoodsAccountFlowReportReq {
  optional string body = 1; // 废弃
  repeated KeyValueInfo kv = 2;
}

message RpcUgcBuyGoodsAccountFlowReportRes {
  optional int32 result = 1;
}

message RpcReportDanMuSendReq {
  option (region_msg) = true;
  optional int32 danMuType = 1;  // 弹幕类型
  optional int64 danMuId = 2 [(field_hash_key) = true];  // 地图id
  optional DanMuInfo danMuInfo = 3;  // 弹幕信息
}

message RpcReportDanMuSendRes {
  optional int32 result = 1;
}

message RpcReportDanMuDeleteReq {
  option (region_msg) = true;
  optional int32 danMuType = 1;  // 弹幕类型
  optional int64 danMuId = 2 [(field_hash_key) = true];  // 地图id
  optional DanMuRecordInfo danMuRecordInfo = 3;  // 弹幕记录信息
}

message RpcReportDanMuDeleteRes {
  optional int32 result = 1;
}

message RpcReportDanMuLikeReq {
  option (region_msg) = true;
  optional int32 danMuType = 1;  // 弹幕类型
  optional int64 danMuId = 2 [(field_hash_key) = true];  // 地图id
  optional DanMuRecordInfo danMuRecordInfo = 3;  // 弹幕记录信息
  optional int64 uid = 4;  // 点赞
}

message RpcReportDanMuLikeRes {
  optional int32 result = 1;
}

message RpcReportDanMuClearReq {
  option (region_msg) = true;
  optional int32 danMuType = 1;  // 弹幕类型
  optional int64 danMuId = 2 [(field_hash_key) = true];  // 地图id
}

message RpcReportDanMuClearRes {
  optional int32 result = 1;
}

message RpcUgcCollectionReportNtfReq {
  option (rpc_one_way) = true;
  option (region_msg) = true;
  optional string collectionId = 1;
  optional CollectionReport report = 2;

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcCollectionReportNtfRes {

}


// 合集搜索
message RpcUgcCollectionSearchReq {
  optional int64 operator = 1 [(field_hash_key) = true];
  optional string words = 2;
  optional int32 type = 3;
  optional uint32 pageNo = 4; // 从0开始

  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcCollectionSearchRes {
  optional int32 result = 1;
  repeated Collection collections = 2;

  message Collection {
    optional string id = 1;
  }
}

// 合集官方精品推荐
message RpcUgcCollectionGovRecommendReq {
  optional int64 operator = 1 [(field_hash_key) = true];
  optional string openId = 2;
  optional int64 uid = 3;
  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;
}

message RpcUgcCollectionGovRecommendRes {
  optional int32 result = 1;
  repeated Collection top = 2;                      // 置顶合集
  repeated Collection other = 3;                    // 其他精品合集

  message Collection {
    optional string collection_id = 1;              // 合集ID
  }
}

// 合集个性化推荐
message RpcUgcCollectionRecommendReq {
  optional int64 operator = 1 [(field_hash_key) = true];
  optional uint32 type = 2;                         // 推荐列表类型。0-合集广场
  repeated uint32 tagIds = 3;                       // 标签ID，传0则不筛选tag
  optional uint32 page = 4;                         // 页号，0表示第一页。
  optional string openId = 5;
  optional int64 uid = 6;
}

message RpcUgcCollectionRecommendRes {
  optional int32 result = 1;
  repeated Collection list = 2;
  optional RecommendAlgoInfo algo_info = 3;
  optional bool has_more = 4;

  message Collection {
    optional string collection_id = 1;              // 合集ID
    optional uint32 rcmd_pos = 2;                   // 推荐接口给的位置。回流参考
  }
}

message RpcXiaowoReportCoverReq {
  option (region_msg) = true;
  optional int64 uid = 1;
  optional int64 mapId = 2;
  optional string name = 3;
  optional int32 coverType = 4;
  optional string cover = 5;
  optional int32 idipArea = 6;
}

message RpcXiaowoReportCoverRes {
  optional int32 result = 1;
}

message RpcFarmGetStrangerListReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string open_id = 2;
  optional int32 limit = 3;
  optional string client_version = 4;
}

message RpcFarmGetStrangerListRes {
  optional int32 result = 1;
  optional AlgoFarmStrangerData info = 2;
}

message GMMultiUgcInfo {
  optional int64 coverId = 1;  // 封面地图
  optional int32 pointNumber = 2;  // 出生点
  repeated int32 campIds = 3;  // 阵营信息
  repeated int64 ugcIds = 4;
  optional string compName = 5;
}

message GMMatchUgcInfo {
  optional int64 ugcId = 1;
  optional int32 specifiedRoomId = 2;
}

message GMSetUgcMatchMapReq {
  option (region_msg) = true;
  /* 废弃 */
  repeated int64 matchUgcIds = 1;  // 精确匹配地图
  repeated int64 lobbyUgcIds = 2;  // 星世界推荐匹配地图
  repeated int64 plazaUgcIds = 3;  // 广场匹配地图
  map<int64, GMMultiUgcInfo> multiMatchUgcIds = 4;  // 一起来玩 合集地图
  repeated GMMatchUgcInfo matchUgc = 5;
  repeated GMMatchUgcInfo lobbyUgc = 6;
  repeated GMMatchUgcInfo plazaUgc = 7;
  /* 使用字段 */
  optional int32 lobbyShowCnt = 8;  // 显示个数
  optional int32 lobbyShowType = 9;  // 显示规则
  map<int64, UgcMatchMapBriefFromRemote> remoteCfg = 10;  // 原始配置
}

message GMSetUgcMatchMapRes {
  optional int32 result = 1;
}

message UgcAdminForwardReq {
  option (region_msg) = true;
  optional com.tencent.wea.xlsRes.UgcCommandUrlType urlType = 1;
  optional UgcAdminForwardHeader forwardHeader = 2;
  optional string reqBody = 3;
}

message UgcAdminForwardRes {
  optional int32 nkErrorCode = 1;
  optional string nkErrorMsg = 2;
  optional string rspBody = 3;
}

// 查询订阅qq机器人
message RpcQuerySubscribeQQRobotReq {
  option (region_msg) = true;
  optional string accessToken = 1;
  optional int64 uid = 2 [(field_hash_key) = true];
  optional string open_id = 3;
}

message RpcQuerySubscribeQQRobotRes {
  optional bool isFriend = 1;
  optional int32 result = 2;
}

// 执行订阅qq机器人
message RpcDoSubscribeQQRobotReq {
  option (region_msg) = true;
  optional string accessToken = 1;
  optional int64 uid = 2 [(field_hash_key) = true];
  optional string open_id = 3;
}

message RpcDoSubscribeQQRobotRes {
  optional int32 result = 1;
}

message RpcGetUserCloudInfoReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string open_id = 2;

}

message RpcGetUserCloudInfoRes {
  optional int32 result = 1;
  optional int64 firstCloudLoginTime = 2; // 首次登录云游戏时间
}

// 给管理端APP 发 NTF
message RpcAppNtfReq {
  option (rpc_one_way) = true;
  option (region_msg) = true;
  optional int32 cmd = 1;
  optional bytes req_body = 2;
  optional int64 app_creator_id = 3;
  optional string openid = 4;
}

message RpcAppNtfRes {
}

message UgcCopyMapMsgReq {
  optional string cmd = 1;
  optional string body = 2;
}

message UgcCopyMapMsgRes {
  optional int32 result = 1;
  optional string content = 2;
}

message UgcCopyProgressMsgReq {
  optional string cmd = 1;
  optional string body = 2;
}

message UgcCopyProgressMsgRes {
  optional int32 result = 1;
}

// 独立app tlog上报请求
message UgcAppTlogReportReq {
  optional string tlog_name = 1;
  optional uint64 tlog_time_ms = 2;
  map<string, string> tlog_map = 3;
  optional string openid = 4;
  optional uint64 creator_id = 5;
}

// 独立app tlog上报响应
message UgcAppTlogReportRes {
  optional int32 result = 1;
}

message RpcArenaGetRecommendCardReq {
  option (region_msg) = true;
  option (rpc_call_timeout_seconds) = 2; // 这个请求局内使用，实时性很强，超过2s直接超时走保底流程
  optional int64 uid = 1[(field_hash_key) = true];
  optional string openId = 2;
  optional string worldId = 3;
  optional ArenaGetRecommendCardReqData data = 4;
}

message RpcArenaGetRecommendCardRes {
  optional int32 result = 1;
  optional ArenaGetRecommendCardRspData data = 2;
}

message RpcPushFarmActivityNoticeReq {
  option (region_msg) = true;
  option (rpc_one_way) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openId = 2; 
  optional string accessToken = 3; // 推送token
  optional int64 target = 4;
  optional uint32 msgType = 5; // l 通知类型: 1 -> 助力成功通知; 2 -> 可领奖通知
  optional int64 credit = 6; // 助力值
  optional string rewardName = 7; // 奖励名称
}

message RpcPushFarmActivityNoticeRes {
  optional int32 result = 1;
}

message RpcUgcGetHallOfFameMapListReq {
  option (region_msg) = true;
  optional int64 creatorId = 1 [(field_hash_key) = true];
  optional int64 uid = 2;
  optional string openId = 3;
}
message RpcUgcGetHallOfFameMapListRes {
  optional int32 result = 1;
  repeated HallOfFameMap map_list = 2;
  optional int32 subscribeStatus = 3; // 0=未订阅；1=已订阅
}

// 种草社区
message RpcUgcPlayerCommunityReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional string openId = 2;
  // 对应原有种草社区的请求路径 /api/{method}
  optional string method = 3;
  // 原有种草社区协议的param的json
  optional string params = 4;
}

message RpcUgcPlayerCommunityRes {
  optional int32 result = 1;
  // 原有种草社区响应的json
  optional string data = 2;
}

message LayerBaseInfo{
  optional int32 layerId = 1;
  optional string layerName = 2;
  optional string mapDataUrl = 3;
  optional string mapDataMd5 = 4;
  optional ReportMapLoading reportMapLoading = 5;
}

message RpcArenaGetProfileTagReq {
  option (region_msg) = true;
  optional int64 uid = 1[(field_hash_key) = true];
  optional string openId = 2;
  optional string worldId = 3;
  optional ArenaGetProfileTagReqData data = 4;
}

message RpcArenaGetProfileTagRes {
  optional int32 result = 1;
  optional ArenaGetProfileTagRspData data = 2;
}

// 发送企微群机器人消息请求
message RpcSendWechatRobotMessageReq {
  option (region_msg) = true;
  optional string robotKey = 1;       // 机器人key
  optional string chatId = 2;         // 聊天id
  optional string content = 3;        // 消息内容
  repeated string mentionedList = 4;  // 被@人的企微名称列表
}

// 发送企微群机器人消息响应
message RpcSendWechatRobotMessageRes {
  optional int32 result = 1;          // 处理结果
}

// 发送请求BI推荐地图
message RpcSendGetBiHudRecommendMessageReq {
  option (region_msg) = true;
  optional int64 uid = 1[(field_hash_key) = true];
  optional string abId = 2;
  optional int32 type = 3; // 1 发现页 2 ugc玩法选择
}

// 发送请求BI推荐地图
message RpcSendGetBiHudRecommendMessageRes {
  optional int32 result = 1;
  repeated string mapInfo = 2;          // 推荐信息
}


message RpcSubscribeRecommendPageReq {
  option (region_msg) = true;
  optional int64 uid = 1[(field_hash_key) = true];
  optional string openId = 2;
  optional int32 op = 3;  // 0=取消订阅；1=订阅
  optional int32 type = 4; // ERecommendPageType
}
message RpcSubscribeRecommendPageRes {
  optional int32 result = 1;
}

message RpcGetUGCEntranceBubblesReq {
  option (region_msg) = true;
  optional int64 uid = 1[(field_hash_key) = true];
  optional string openId = 2;
}
message RpcGetUGCEntranceBubblesRes {
  optional int32 result = 1;
  optional UgcEntranceBubblesData data = 2;
  optional UgcDailyReport dailyReport = 3;
}

message RpcRemoveUGCEntranceBubblesReq {
  option (region_msg) = true;
  optional int64 uid = 1[(field_hash_key) = true];
  optional string openId = 2;
  optional int32 bubble_type = 3; // 气泡类型 UgcStarWorldEntranceBubbleType
  optional int32 bubble_id = 4; // 气泡ID
}
message RpcRemoveUGCEntranceBubblesRes {
  optional int32 result = 1;
}

// ugc资产创建请求(for管理端调用)
message UgcResCreateReqInfo {
  optional int64 uid = 1;               // 用户uid
  optional int64 creatorId = 2;         // 用户creatorId
  optional string name = 3;             // 资产名称
  optional int32 resType = 4;           // 资产类型
  optional UgcExtraInfo extraInfo = 5;  // 资产额外信息
  optional bool isResPubCosPath = 6;    // 私有资源是否在pub路径
}

// ugc资产保存数据请求(for管理端调用)
message UgcResSaveWorkReqInfo {
  optional uint64 uid = 1;                    // 用户uid
  optional uint64 creatorId = 2;              // 用户creatorId
  optional uint64 ugcId = 3;                  // 地图ugcId
  repeated UgcMapMetaInfo info = 4;           // 作品内容
  optional int32 editorSec = 5;               // 编辑时长
  optional int32 mapType = 6;                 // 地图类型
  optional string ugcVersion = 7;             // 版本号
  optional int32 difficulty = 8;              // 难度
  optional int32 saveType = 9;                // 保存类型
  optional bool isCheckPng = 10;              // 是否检查png
  optional UgcMapSetData mapSet = 11;         // 设置里面数据
  optional int32 ugcResType = 12;             // 资源类型
  optional int32 curLayerId = 13;             // 当前图层id
  optional MapLoadingInfo mapLoading = 14;    // 地图加载信息
  optional MapLobbyCoverInfo lobbyCover = 15; // 乐园自定义封面
}

// ugc资产申请key请求(for管理端调用)
message UgcResApplyKeyInfo {
  optional uint64 uid = 1;          // 用户uid
  optional uint64 creatorId = 2;    // 用户creatorId
  optional uint64 ugcId = 3;        // 地图ugcId
  optional int32 instanceType = 4;  // map类型
  optional int32 ugcResType = 5;    // 资产类型
}
