syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "common.proto";
import "ss_head.proto";
import "ResKeywords.proto";
import "ResActivity.proto";
import "event.proto";
import "base_common.proto";

import "cs_head.proto";
import "attr_FarmFishingRecord.proto";
import "attr_FarmFishCardLevel.proto";
import "attr_FarmHandBookRecord.proto";
import "attr_ActivityRedDot.proto";
import "attr_FarmRoomItem.proto";
import "attr_StarPPlayerIntimacyGeneralDBInfo.proto";

import "player_info.proto";

message PingPongReq {
  optional int32 src = 1;
  optional int32 dst = 2 [(field_dest_serv) = true];
}

message PingPongRes {
}

message KickOffReq {
  optional MetaDataType type = 1;
  optional int64 uuid = 2;
  optional int32 addr = 3 [(field_dest_serv) = true];
}

message KickOffRes {
  optional int32 result = 1;
}

message TestReq {
  option (rpc_call_timeout_seconds) = 3;
  optional int64 uid = 1;
  optional string body = 2;
}

message TestRes {
  optional int64 uid = 1;
  optional string body = 2;
}

enum ShareDataType {
  SHARE_DATA_TYPE_NONE = 0;
  SHARE_DATA_TYPE_ALLIANCE = 1;
  SHARE_DATA_TYPE_GSALLIANCE = 2;
}
message ShareData {
  optional int64 sequenceId = 1;
  //oneof{
  //optional proto_AllianceAttr allianceAttr = 2;
  //}
}

message SubscribeShareDataReq {
  optional int32 dstServer = 1 [(field_dest_serv) = true];
  optional int32 objectType = 2; // 协议改造 枚举值切换至整数值 详情参考ShareDataType
  optional int64 objectId = 3;
  optional int32 srcServer = 4;
}

message SubscribeShareDataRes {
  optional ShareData item = 1;
}

message BatchSubscribeShareDataReq {
  optional int32 dstServer = 1 [(field_dest_serv) = true];
  optional int32 objectType = 2; // 协议改造 枚举值切换至整数值 详情参考ShareDataType
  repeated int64 objectId = 3;
  optional int32 srcServer = 4;
}

message BatchSubscribeShareDataRes {
  repeated ShareData items = 1;
}

message UnsubscribeShareDataReq {
  optional int32 dstServer = 1 [(field_dest_serv) = true];
  optional int32 objectType = 2;// 协议改造 枚举值切换至整数值 详情参考ShareDataType
  optional int64 objectId = 3;
  optional int32 srcServer = 4;
}

message UnsubscribeShareDataRes {
}

message ShareDataHeartBeatReq {
  optional int32 dstServer = 1 [(field_dest_serv) = true];
  optional int32 objectType = 2;// 协议改造 枚举值切换至整数值 详情参考ShareDataType
  optional int64 objectId = 3;
  optional int64 sequenceId = 4;
  optional int32 srcServer = 5;
}

message ShareDataHeartBeatRes {
}

message SyncShareDataChangeReq {
  optional int32 dstServer = 1 [(field_dest_serv) = true];
  optional int32 objectType = 2;// 协议改造 枚举值切换至整数值 详情参考ShareDataType
  optional int64 objectId = 3;
  optional ShareData dirtyData = 4;
  optional int32 srcServer = 5;
}

message SyncShareDataChangeRes {
}

message ShareDataSyncRemoveSubscriberReq {
  optional int32 dstServer = 1 [(field_dest_serv) = true];
  optional int32 objectType = 2;// 协议改造 枚举值切换至整数值 详情参考ShareDataType
  optional int64 objectId = 3;
}

message ShareDataSyncRemoveSubscriberRes {
}

//globalsvrmgr

enum ServiceNodeStatus {
  SVRNODE_INIT = 1;         //启动状态
  SVRNODE_NORMAL = 2;       //正常可对外提供服务状态
  SVRNODE_MIGRATING = 3;    //迁移状态
  SVRNODE_MIGRATEFINI = 4;    //迁移完成状态
  SVRNODE_OFFLINE = 5;      //下线状态
}

enum ClientNodeStatus {
  CLINODE_INIT = 1;
  CLINODE_NORMAL = 2;
  CLINODE_OFFLINE = 3;
}

enum GlobalServiceAction {
  SVRVICES_ACTION_NORMAL = 1;       //正常状态
  SVRVICES_ACTION_NEXTVER = 2;      //广播下一版路由信息
  SVRVICES_ACTION_MIGRATE = 3;      //迁移
  SVRVICES_ACTION_MIGRATEFINI = 4;    //迁移完成
  SVRVICES_ACTION_ROUTEUPDATE = 5;    //路由更新
  SVRVICES_ACTION_MIGRATEOUT = 6;    //节点迁出
}

message ServiceNodeRouteInfo {
  required int32 virtualCnt = 1;    //虚节点个数
  required int32 busID = 2;       //busID
}

message ObserverNodeInfo {
  required int32 version = 1;    //版本号
  required int32 serverType = 2;  //观察的服务节点类型
}

message ServicesNodeInfo {
  required int32 version = 1;             //版本号
  repeated ServiceNodeRouteInfo routeList = 2;
}

message GlobalServicesActionReq {
  optional int32 dstServer = 1 [(field_dest_serv) = true];
  optional int32 action = 2;       //具体操作 // 协议改造 枚举值切换至整数值 详情参考GlobalServiceAction
  required int32 version = 3;
  optional ServicesNodeInfo changedInfo = 4;      //变更信息
  optional int64 key = 5;      //迁出节点key
}

message GlobalServicesActionRes {
  required int32 result = 1;    //错误码
}

enum GlobalServiceRole {
  GSR_SALAVE = 1;         //从节点角色
  GSR_MASTER = 2;         //主节点角色
}

//globalsvrmgr end

message PlayerBattleInfo {
  optional int64 uid = 1;
  optional string dsAuthToken = 2;
  optional int32 side = 3;
}

enum LobbyJumpReasonCode {
  LobbyJumpReasonCode_NORMAL = 0;           // 分配, 并通知客户端跳转进大厅
  LobbyJumpReasonCode_RETURN = 1;           // 预分配, 不通知客户端跳转，需要同步lobbyid
  LobbyJumpReasonCode_Merge = 2;            // 合并，并通知客户端跳转进大厅，
  LobbyJumpReasonCode_Migrate = 3;          // 大厅迁移
  LobbyJumpReasonCode_Reconnect = 4;        // 重连
  LobbyJumpReasonCode_IslandReturnOnTime = 5;     // 分大厅定点结束返回
  LobbyJumpReasonCode_LogicMigrateToMainLobby = 6;     // 满足一定条件，强制迁移到主大厅
  LobbyJumpReasonCode_HeartbeatTimeout = 7; //自测UGC大厅超时时迁移到默认大厅
  LobbyJumpReasonCode_RuntimeTooLong = 8;   //自测UGC大厅运行到最大时间，迁移到默认大厅
}
//globalsvrmgr end

//seqsvr
enum SeqType {//涉及存档，不能随便改
  USER_SEQ = 11; //玩家短序号
  //DISCARD_ROOM_SEQ = 12; //房间短序号
  SNS_INVITATION_SEQ = 13; // 邀请码短序号
  USER_SEQ_QQ = 14; //QQ区玩家序号
  USER_SEQ_WX = 15; //微信区玩家区号
  ROOM_SEQ = 16;    //房间短号
  CONCERT_TICKET_ID = 17; //演唱会门票
  CLUB_SEQ = 18; //社团短ID
  WEEKEND_ICE_BROKEN_SEQ = 19; //破冰活动抽签号
  ANNIVERSARY_MOBA_SEQ = 20; //Moba周年庆送虞姬

  TEST_1 = 21; //测试1
  TEST_2 = 22; //测试2
  TEST_3 = 23; //测试3
  TEST_4 = 24; //测试4
  TEST_5 = 25; //测试5
  TEST_6 = 26; //测试6
  TEST_7 = 27; //测试7
  TEST_8 = 28; //测试8
}
enum SeqEncryptType {
  NONE = 1;
  RANDOM_SEGMENT = 2; //随机分段
  SHUFFLE = 3; //混淆
  RANDOM_SEGMENT_AND_SHUFFLE = 4; //随机分段加混淆
}


message SeqInfo {
  optional int32 seqType = 1; // 协议改造 枚举值切换至整数值 详情参考SeqType
  optional int64 seqId = 2;
  optional string desc = 3;
  optional int64 useTime = 4;
}

//seq end

// 管理端数据请求
message UgcTextData {
  optional string content = 1;
  optional int32 id = 2;       // id不为空， 是actorinfo里面的文本
  optional int32 type = 3;     // id为空，type 和 index 不为空， 是文本控件，按钮控件，组合控件里面的文本
  optional int32 index = 4;
}

message QueryUgcTextData {
  optional int32 code = 1; // 0成功, 1表示获取不到数据，-1表示其他服务器错误。1可重试，-1不需要重试
  optional string msg = 2;
  optional uint64 map_id = 3;
  optional uint64 version = 4;
  repeated UgcTextData text_data_list = 5;
  optional uint32 total_size = 6; // 总条数，text_data_list 可能因为limit size的限制，没有全部返回
}

message PlayerTLogMatchBegin {
  optional int64 matchID = 1;
  optional int32 battleType = 2;
  optional int32 matchRule = 3;
  optional int32 warmType = 4;
  optional int32 mmr = 5;
  optional int32 teamMMR = 6;
  optional int32 warmPoint = 7;
  optional int64 teamID = 8;
  optional int32 teamNum = 9;
  repeated int64 memberList = 10;
  optional int64 ugcID = 11;
  optional int32 side = 12;
  optional string roguelikeMatchDifficulty = 13;
  optional int32 selectSideId = 14;
  optional int32 sideRoleID = 15;
  repeated int32 mapID = 16;
  repeated int64 mapPoolList = 21;
  optional int32 virtualRoomInfoId = 22;
  optional string robotSourceStr = 23; // 机器人来源字符串
}

message PlayerTLogMatchResult {
  optional int64 matchID = 1;
  optional int32 battleType = 2;
  optional int32 matchTime = 3;
  optional int32 matchResult = 4;
  optional int32 aiNum = 5;
  optional int32 totalPlayerNum = 6;
  optional int32 warmType = 7;
  optional int32 matchRule = 8;
  optional int32 mmr = 9;
  optional int32 teamMMR = 10;
  optional int64 teamID = 11;
  optional int32 teamNum = 12;
  repeated int64 memberList = 13;
  repeated int64 campList = 14;
  optional int64 ugcID = 15;
  optional int32 side = 16;
  optional string roguelikeMatchDifficulty = 17;
  optional int32 selectSideId = 18;
  optional int32 sideRoleID = 19;
  repeated int32 mapID = 20;
  repeated int64 mapPoolList = 21;
  optional int32 virtualRoomInfoId = 22;
  optional int64 battleId = 23;
  optional string robotSourceStr = 24; // 机器人来源字符串
}

message PlayerTLogUGCTwosomeReadyResult {
  optional int64 roomID = 1;
  optional int64 ugcID = 2;
  optional int32 battleCount = 3;
  optional int32 readyResult = 4;
  optional string readyErrorMsg = 5;
  optional AlgoInfo algoInfo = 6;
}

message PlayerTLogLobbyFlow {
  optional string lobbyID = 1;   //大厅ID
  optional int32 flowType = 2;   //流水类型
  optional int32 enterType = 3;  //进入大厅方式
  optional string leavePos = 4;  //离开时坐标位置
  optional int32 stayTime = 5;   //停留
  optional string mapID = 6;     //UGC广场地图id
  optional int32 lobbyType = 7;  //大厅类型
}

// 账号注销任务状态
enum AccountCancelTaskState {
  ACCOUNT_CANCEL_TASK_STATE_IN_PROGRESS = 0;  // 进行中状态
  ACCOUNT_CANCEL_TASK_STATE_COMPLETED = 1;    // 已完成状态
}

// 账号转区任务状态
enum AccountTransferTaskState {
  ACCOUNT_TRANSFER_TASK_STATE_IN_PROGRESS = 0;  // 进行中状态
  ACCOUNT_TRANSFER_TASK_STATE_COMPLETED = 1;    // 已完成状态
}

message UpdateCreatorInfoParam {
  optional uint64 creator_uid = 1;
  optional int32 type = 2;
  optional uint32 auth_type = 3;
  optional string auth_desc = 4;
  optional uint32 draft_box_limit = 5;
  optional uint32 publish_box_limit = 6;
  optional uint32 edit_unlimit_start_time = 7;
  optional uint32 edit_unlimit_end_time = 8;
}

message GoodsInfo {
  optional string goods_id = 1;
  optional int32 unit_price = 2;
  repeated ItemInfo item_list = 3;
  message ItemInfo{
    optional string item_id = 1;
    optional int32 item_num = 2;
  }
}
message DatastoreAccessedPermissionUpdateInfo {
  optional int32 updateType = 33; //操作类型，1：添加， 2：删除
  optional int64 canAccessUgcMapId = 34; //可访问当前地图的ugc地图ID 
}

message UgcMapEvaluationStatusParam {
  optional int32 evaluation_status = 1;  // 审核状态 UgcMapEvaluationStatusType
  // ... 后续结构
}

message UpdateUgcMapInfoParam {
  optional int64 map_id = 1;
  optional int32 type = 2; // 变更类型  UgcPublishChangeType
  repeated int64 version_list = 3;
  optional int32 status = 4;    // 游戏侧地图状态
  optional int32 wf_status = 5;     // 管理端审核流状态
  repeated int32 tag_ids = 6;       // 地图标签
  optional int32 source = 7;   // 地图类型
  optional string desc = 8;        // 地图描述
  optional string name = 9;        // 地图名称
  optional int32 gm = 10;           // 是否官方组合
  optional int32 loading_template_id = 11; // 地图loading模板id
  optional int64 signed_creator = 12;
  optional int32 share_limit = 13;  // 地图分享限制状态 0 不限制，1限制
  optional UgcManagementInfoParam management_info = 14;
  optional int32 privateState = 15; // 私创状态 0 否 1是
  optional int32 lockState = 16; // 锁定状态 0:否 1:因为推荐位即将生效而锁定 2:因为推荐位已生效而锁定
  optional int32 mapMark = 17; // 地图标记id
  optional int64 ugcNewYearStep = 18;
  optional int32 publish_goods_status = 19;
  optional int32 buy_goods_status = 20;
  repeated GoodsInfo goods_info_list = 21;
  repeated UgcEditorInfo display_creator_list = 22;
  message UgcEditorInfo {
    optional int64 creatorId = 1;
    optional int32 type = 2;
    optional int64 uid = 3;
  }
  optional int32 label_id = 23; // 待修改的目标标签，type为15生效
  optional int32 delta_score = 24;  // 变更的分数，type为15生效
  optional string recommend_main_cover = 25; // 封面url，type为16生效
  optional int32  recommend_main_cover_index = 26; // 封面下标，type为16生效
  optional int32 dim_label_id = 27; // 待修改的目标维度标签id
  optional int32 com_label_id = 28; // 待修改的目标综合标签id
  optional string prize_id = 29;
  optional string prize_info = 30;
  optional int32 apply_delete = 31;
  optional int32 req_from = 32;   //0:默认 1:管理端操作 2:安全审核
  optional DatastoreAccessedPermissionUpdateInfo datastoreAccessedPermissionUpdateInfo = 33;
  optional UgcMapEvaluationStatusParam ugc_map_evaluation = 34;  // 地图评估状态
  repeated UgcBGMInfo bgmInfo = 35; // ugc背景音乐设置，最大5个，数组索引表示顺序
}

enum UgcPublishChangeType {
  UPCT_ChangeStatus = 1;     // 更新地图status
  UPCT_ChangeTag = 2;        // 更新地图标签
  UPCT_ChangeName = 3;       // 更新地图md
  UPCT_ChangeLoadingBG = 4;  // 地图loading模板
  UPCT_CoBranded = 5;        // 入选联名
  UPCT_UnCoBranded = 6;      // 取消联名
  UPCT_ChangeShareBan = 7;   // 更新地图分享限制状态
  UPCT_UpdateManagementInfo = 8;
  UPCT_ChangePrivateState = 9; // 更新私创状态
  UPCT_ChangeLockState = 10; // 更新锁定状态
  UPCT_ChangeMapMark = 11; // 更新地图标记
  UPCT_ChangeUgcNewYearStep = 12; // 更新ugc新年活动地图进度
  UPCT_ChangeBuyGoodsStatus = 13; // 更新当前星钻商品审核状态
  UPCT_ChangeDisplayCreators = 14;// 更新显示的创作者
  UPCT_ChangeMapLabelScore = 15;// 更新地图评分
  UPCT_ChangeMapMainCover = 16;// 修改主封面
  UPCT_ChangeMapLabel = 17;// 更改地图评分标签
  UPCT_RewardLabel = 18;// 更改地图获奖信息
  UPCT_ChangeTakeOffApply = 19;// 启用或者取消删除申请
  UPCT_VersionRevert = 20;// 审核不通过，版本打回
  UPCT_UpdateDatastoreAccessedPermission = 21;  // 修改地图存档的被访问权限
  UPCT_UgcMapEvaluationStatus = 22;  // ugc地图评估
  UPCT_ChangeUgcBGMInfo = 23;  // 修改地图qq音乐信息
}

enum UgcUpdateType {
  UUT_Invalid = 0;
  UUT_Tcaplus = 1;
}

enum UgcTcaplusTableName {
  UTTN_Invalid = 0;
  UTTN_UgcPlayerInfo = 1;
  UTTN_UgcPublish = 2;
  UTTN_UgcBrief = 3;
}

message season_info {
  optional int32 season_id = 1;
  optional uint32 start_time = 2;
  optional uint32 end_time = 3;
}

message GetCurrentSeasonIdRet {
  optional int32 code = 1;
  optional string msg = 2;
  optional int32 season_id = 3;
  repeated season_info history_season_list = 4;
}

message PlatSyncMapTopicsData {
  repeated UgcMapTopic topics = 1;
  optional string topic_guide_text = 2;
}

message PlatSyncMapTopicsRet {
  optional int32 code = 1;
  optional string msg = 2;
  optional PlatSyncMapTopicsData data = 3;
}

message XiaowoTlogInfo {
  optional int32 level = 1;
  optional int32 partyStatus = 2;
  optional int32 hotValue = 3;
  optional int64 totalLikeCount = 4;
  optional int64 totalStarCount = 5;
  optional int64 totalVisitCount = 6;
  optional string moneyTreeListStr = 7;
  optional string mapId = 8;
  optional string mapVersion = 9;
}

message PlayerReportItemInfo {
  optional uint32 item_id = 1;     // 道具id
  optional uint64 total = 2;    // 个数
}

message PlayerReportLbsInfo {
  optional string lbs = 1;     // 老lbs地址
  optional string new_lbs = 2;    // 新lbs地址
}

message PlayerReportIntimacyInfo {
  optional int64 uid = 1;    // 亲密对象uid
  optional uint64 intimacy = 2;   // 亲密度
  optional uint32 relation = 3;   // 亲密关系
}

message PlayerReportFarmEnterExitInfo {
  optional int32 enter_or_exit = 1; // 1进入自己农场/2离开自己农场
  optional int32 farm_level = 2; // 农场等级
  optional int64 farm_coin_num = 3; // 农场币数量
  optional int64 farm_month_card_expire_time = 4; // 农场月卡到期时间
  optional int32 farm_collection_num = 5; // 农场藏品数量
  optional int32 farm_pet_id = 6; // 宠物id
  optional int64 farm_pet_hungry_time = 7; // 宠物饥饿时间
  optional bool farm_pet_housekeeping_enable = 8; // 宠物看家护院是否开启
  optional bool farm_has_villager_gift = 9; // 居民礼物未领取
  optional int64 farm_magic_speed_full_time = 10; // 时光跳跃仙术满蓝时间
}

message PlayerReportFarmLeaveMessageInfo {
  optional int64 operator_uid = 1; // 留言者uid
  optional string content = 2; // 留言内容
}

message PlayerReportFarmStealInfo {
  optional int64 operatorUid = 1; // 偷菜的人
  repeated FarmItem stolen_items = 2; // 被偷的东西
  optional int64 sell_price = 3; // 预估售价
  optional int32 cook_visitant_avatar_id = 4; // 贵宾avatarId
  optional int32 steal_category = 100; // 参考FarmCropCategory
  optional int32 cost_cnt = 101; // 消耗偷取次数
}

message FarmItem {
  optional int32 item_id = 1;
  optional int64 item_num = 2;
}

message PlayerReportFarmFertilizeInfo {
  optional int64 operator_uid = 1; // 祈愿的人
  optional int32 before_ripe_quality = 2; // 祈愿前的情况 0未知1普通2丰收3大丰收
  optional int32 after_ripe_quality = 3; // 祈愿后的情况 0未知1普通2丰收3大丰收
  optional int32 crop_type = 4; // 养殖物类型
}

message PlayerReportFarmCropStatusInfo {
  optional int64 crop_dry_time_sec = 1; // 最近的干涸作物-时间
  optional int64 crop_ripe_time_sec = 2; // 最近的成熟作物-时间
  optional int64 animal_hungry_time_sec = 3; // 最近的饥饿动物-时间
  optional int64 animal_ripe_time_sec = 4; // 最近的成熟动物-时间
  optional int64 animal_can_encourage_time_sec = 5; // 最近的可助产动物-时间
  optional int32 dry_crop_type = 6; // 最近的干涸作物-类型
  optional int32 ripe_crop_type = 7; // 最近的成熟作物-类型
  optional int32 hungry_animal_type = 8; // 最近的饥饿动物-类型
  optional int32 ripe_animal_type = 9; // 最近的成熟动物-类型
  optional int32 can_encourage_animal_type = 10; // 最近的可助产动物-类型
  optional int64 fish_ripe_time_sec = 11; // 鱼塘可钓时间
  optional int64 machine_ripe_time_sec = 12; // 最近的成熟加工器-时间
  optional int64 aquarium_ripe_time_sec = 13; // 最近的成熟水族箱-时间
  optional int32 ripe_machine_type = 14; // 最近的成熟加工器-类型
  optional int32 fish_pool_layer = 15; // 鱼塘深度
  optional int64 fish_protect_time_sec = 16; // 鱼塘保护到期时间
  optional int64 cook_visitant_can_prebook_time = 17; // 餐厅可预约时间
  optional int64 cook_visitant_can_serve_time = 18; // 餐厅贵宾可接待时间
  optional int64 cook_visitant_protect_end_time = 19; // 餐厅贵宾保护到期时间
  optional int64 cook_can_open_time = 20; // 餐厅可备菜时间
}

message PlayerReportFarmGiftInfo {
  optional int64 operator_uid = 1; // 送礼的人
  repeated FarmItem gift_items = 2; // 送的东西
}

message PlayerReportFarmFishInfo {
  optional int32 fish_type = 1; // 鱼类型
  optional int32 fish_quality = 2; // 鱼品质
  optional float fish_weight = 3; // 鱼重量
}

message PlayerReportFarmHarvestInfo {
  optional int32 crop_type = 1;
  repeated FarmItem harvest_items = 2; // 收获的东西
  optional int32 ripe_quality = 3; // 收获品质
}

message PlayerReportFarmHotSpringInfo {
}

message PlayerReportFarmHotSpringRefreshInfo {
  optional int32 type = 1;
  optional int32 grade = 2;
  repeated int32 buffs = 3;
}

message PlayerReportFarmCloudMarketInfo {
  optional int32 item_id = 1; // 收的道具类型
  optional int64 item_num = 2; // 收的道具数量
  optional float ratio = 3; // 倍率
  optional int64 end_time_ms = 4; // 结束时间 毫秒
}

message PlayerReportFarmBuffInfo {
  optional int32 buff_id = 1;
  optional int64 start_time_sec = 2; // 开始时间
  optional int64 end_time_sec = 3; // 结束时间
  optional string buff_source_uid = 4;
}

message PlayerReportBattleInfo {
  optional int64 uid = 1;    // 玩家uid
  optional int64 room_id = 2;         // 房间id
}

message BattleReportResult {
  optional int64 uid = 1;        // 玩家uid
  repeated BattleReportLevelResult level_battle_result = 2;    // 每个关卡的结算结果
  optional int32 battle_time = 3;      // 对局时长
  optional int32 is_mvp = 4;     // 是否mvp
  optional int32 match_grade = 5;  // 对局评价
}

message BattleReportLevelResult {
  optional int32 level_id = 1;    // 关卡id
  optional int32 result = 2;     // 对局结果 0:胜 1:负 2:平
  optional int32 rank = 3;       // 排名
  optional int32 score = 4;      // 得分
  optional int32 camp_side = 5;     // 阵营
}

// 翻译数据
message TranslationData {
  optional int32 type = 1;      // 翻译类型 0.作品名 1.作品描述 2.局内文本
  optional int32 id = 2;        // 局内文本标识ID, 客户端定位到局内的文本位置
  optional string content = 3;    // 翻译后文本
}

// 玩家信用分数据
message PlayerCreditScoreData {
  optional uint32 mtime = 1;              // 评估时间
  optional uint32 score = 2;              // 通用信用分
  optional uint32 tag_black = 3;          // 黑产标签等级
  optional uint32 tag_ugc = 4;            // ugc标签等级
  optional uint32 tag_ugc_politics = 5;   // 涉政标签等级
}

// 获取玩家信用分回包, http接口回包使用
message PlayerCreditScoreResponse {
  optional int32 ret = 1;                 // 返回结果, 0正常, 非0异常
  optional PlayerCreditScoreData data = 2;// 数据包
}

// 信用限制信息
message CreditLimitMsg {
  optional uint32 threshold_lo = 1;       // 通用信用分限制下界
  optional uint32 threshold_hi = 2;       // 通用信用分限制上界
  optional uint32 is_tag_used = 3;        // 命中分数区间是否需要进一步判断标签限制，need_tag=1时返回
  optional string tag_type = 4;           // 标签类型
  optional uint32 tag_hi = 5;             // 标签区间上界
  optional uint32 tag_lo = 6;             // 标签区间下界
  optional uint32 group_id = 7;           // 命中所有维度限制的用户群体编号
}

// 信用限制消息
message CreditLimitMsgList {
  repeated CreditLimitMsg limit_msg = 1;  // 限制消息
}

// 信用限制数据
message CreditLimitData {
  map<string, CreditLimitMsgList> limit_msg_map = 1; // scene_id->限制列表的映射关系
}

// 获取信用限制数据回包, http接口回包使用
message CreditLimitResponse {
  optional int32 ret = 1;                 // 返回结果, 0正常, 非0异常
  optional CreditLimitData data = 2;      // 数据包
}

message GetH5PrefabNicknameRetParam {
  optional int32 ret = 1;
  optional int32 iRet = 2;
  optional string sMsg = 3;
  optional jDataParam jData = 4;
  message jDataParam {
    optional int32 iRet = 1;
    optional string sMsg = 2;
    optional int32 iHasName = 3;
    optional string sUserName = 4;
  }
}

message CheckH5PrefabNicknameRetParam {
  optional int32 ret = 1;
  optional int32 iRet = 2;
  optional string sMsg = 3;
  optional jDataParam jData = 4;
  message jDataParam {
    optional int32 iRet = 1;
    optional string sMsg = 2;
    optional string sUserName = 3;  // 弃用
    optional int32 iExist = 4;      // 弃用
    optional string sUserNames = 5;
    optional string sExists = 6;
  }
}

message PrivReportSyncActivePointParam {
  optional int32 ret = 1;
  optional int32 iRet = 2;
  optional jDataParam jData = 3;
  message jDataParam {
    optional int32 iRet = 1;
    optional string sMsg = 2;
  }
  optional string sAmsSerial = 4;
  optional float fReqUseTime = 5;
}

enum AigcGameType {
  GEN_IMAGE_GAME_TYPE = 1400300;
  CHANGE_COLOR_GAME_TYPE = 1400301;
  GEN_VOICE_GAME_TYPE = 1400303;
  GEN_ANICAP_GAME_TYPE = 1400304;
  GEN_ANSWER_GAME_TYPE = 1400305;
  GEN_MODULE_GAME_TYPE = 1400306;
  GEN_MAGIC_PIC_GAME_TYPE = 1400307;  // 魔法图片
  AI_EDIT_ASSISTANT = 1400309;        // ai编辑助手
}

message UgcManagementInfoParam {
  optional string name = 1;
  optional string desc = 2;
  repeated int32 tag_ids = 3;
  repeated Topic topics = 4;
  optional bool clear_topic = 5;
  message Topic {
    optional uint32 topic_id = 1;
    optional string topic_name = 2;
  }
}

// 资源社区推荐页合集信息——id列表
message UgcResHomePageSetIds {
  optional int32 index = 1;      // 编号
  optional string name = 2;      // 合集名称
  optional string desc = 3;      // 合集描述
  repeated int64 ids = 4;        // 集合ids
  optional int32 showNum = 5;    // 合集展示数量
}


// --------视频审核----------- //
message ExamineAccount {
  optional string account = 1;
  optional uint32 area_id = 2;     // 可不填
  optional uint32 world_id = 3;    // 可不填
  optional uint32 plat_id = 4;
  optional string role_name = 5;
  optional string role_id = 6;
  optional int32 role_level = 7;
}

message ExamineAccountV2 {
  optional string account_id = 1;
  optional int32 account_type = 2;   // 1：QQ，2：微信openid，3：手机号码，4：QQ openid，601：其它
  optional int32 plat_id = 3;
  optional int32 game_id = 4;      // *********
  optional int32 world_id = 5;    // 可不填
  optional int32 area_id = 6;     // 可不填
  optional string role_id = 7;    // 可不填
  optional string role_name = 8;    // 可不填

}

message VideoExamineTask {
  optional int32 scene_id = 1;
  optional ExamineAccount account = 2;
  optional string url = 3;                   // 视频url
  optional uint32 media_type = 4;            // 填2
  optional string callback_data = 5;         // 回调内容(RpcAigcGenAnicapNtfReq的json字串)
  optional string callback = 6;              // 回调url
}

message VideoExamineRequest {
  repeated VideoExamineTask tasks = 1;
}

message VideoExamineRequestV2 {
  optional uint32 scene_id = 1;
  optional uint32 machine_check_timeout = 2;    // 请求超时时长，单位分钟
  optional ExamineAccountV2 account = 3;
  repeated string url = 4;                   // 视频url
  optional string callback_data = 6;         // 回调内容(RpcAigcGenAnicapNtfReq的json字串)
  optional string callback = 7;              // 作废
  optional string seed = 8;
  optional uint32 media_type = 9;            // 媒体类型，1：文本、2：图片、3：视频、4：音频. 这里填3
  optional string callback_url = 10;         // 回调url
}

message ExamineResponeData {
  optional int32 err_code = 1;
  optional string err_msg = 2;
}

message VideoExamineResponse {
  optional int32 err_code = 1;
  optional string err_msg = 2;
  repeated ExamineResponeData data = 3;
}

message ExamineResponeDataV2 {
  optional string task_id = 1;
}

message VideoExamineResponseV2 {
  optional int32 err_code = 1;
  optional string err_msg = 2;
  optional ExamineResponeDataV2 data = 3;
}

message ExamineFrameDetail {
  optional string frame_url = 1;
  optional string frame_type = 2;
  optional uint32 frame_offset_time = 3;
}

message ExamineResultDetail {
  optional int32 label = 1;
  repeated int32 sub_labels = 2;
  optional string desc = 3;
  repeated ExamineFrameDetail frames = 4;
}

message ExamineCheckResult {
  optional int32 check_result = 1;        // 0: 审核通过  1: 审核不通过
  optional int32 label = 2;
  optional string check_desc = 3;
  repeated ExamineResultDetail result_details = 4;
}

message ExamineCallbackContent {
  optional int32 err_code = 1;
  optional string err_msg = 2;
  optional string task_id = 3;
  optional string callback_data = 4;     // RpcAigcGenAnicapNtfReq的json字串
  optional ExamineCheckResult data = 5;
}

message VideoExamineCallback {
  optional string check_sum = 1;
  optional string content = 2;        // ExamineCallbackContent的json字串
}

message ExamineResultDetailV2 {
  optional uint32 label = 1;
  repeated uint32 sub_labels = 2;
  optional string desc = 3;
}

message ExamineCheckResultV2 {
  optional string url = 1;
  repeated ExamineResultDetailV2 result_details = 2;
}

message ExamineCallbackContentV2 {
  optional int32 err_code = 1;
  optional string err_msg = 2;
  optional string task_id = 3;
  optional int32 scene_id = 4;
  optional uint32 machine_check_timeout = 5;
  optional int32 check_type = 6;
  optional ExamineAccountV2 account = 7;
  optional int32 check_result = 8;
  optional string check_desc = 9;
  optional uint32 label = 10;
  repeated ExamineCheckResultV2 check_ret_details = 11;
  optional string callback_data = 13;     // RpcAigcGenAnicapNtfReq的json字串
  optional string callback_url = 14;
}

message VideoExamineCallbackV2 {
  optional string check_sum = 1;
  optional int32 check_type = 2;
  optional string content = 3;        // ExamineCallbackContentV2的json字串
}
// --------视频审核----------- //

message MQEventBroadcast {
  optional string topic = 1;          // 发布主题
  optional int64 createTime = 2;      // 创建时间
  optional int64 src = 3;             // 消息来源
  optional int32 msgType = 4;         // 消息类型
  optional bytes body = 5;            // 消息体
  optional int32 nice = 6;            // 动态优先级
}

message AdminModifyCollectionParam {
  optional string collection_id = 1;
  optional int32 type = 2;                    // 1删除, 2修改
  optional int32 status = 3;
  repeated int32 admin_tag_ids = 4;           // 废弃
  optional string admin_desc = 5;             // 废弃
  repeated CollectionAdminTag admin_tags = 6;



  optional string reserved1 = 1001;
  optional string reserved2 = 1002;
  optional string reserved3 = 1003;

  optional int64 playTimesUv = 1004;
  optional int64 playTimesPv = 1005;
  optional int64 collectTimes = 1006;
}

message PublishGoodsParam {
  optional int64 creator_id = 1;
  repeated GoodsInfo goods_info_list = 2;
}

message UgcCommunityOpReportParam {
  optional int64 uid = 1;
  optional int32 opType = 2;  //  1 发布笔记
}

message StreamPlayerData {
  optional string openid = 1;
  optional int64 uid = 2;
  optional string name = 3;
  optional string token = 4;
  optional int32 gender = 5;
  optional string ugcCommonBucket = 6;  // ugc服务器使用bucket, ai编辑助手场景使用
  optional int64 creatorId = 7;         // ugc创作者id, ai编辑助手场景使用
  map<string, int64> aigcNpcRoleId = 8;  // ai伙伴roleid
}

message AigcNpcPalLabNpcInfo {
  optional string name = 1;
  optional string birthday = 2;
  optional string identity = 3;
  repeated string personality = 4;
  optional string self_reference = 5;
  optional string player_nickname = 6;
  optional string title = 7;
  optional string wish = 8;
}

message AigcNpcPalLabUserInfo {
  optional string name = 1;
  optional string gender = 2;
  optional string city = 3;
  optional string birthday = 4;
  repeated string favorite_game_mode = 5;
  optional string mbti = 6;
}

message AiNpcTodScene {
  optional string tod_scene_id = 1;    //address_book（通讯录聊天类型）
  optional bool init_var = 2;      //触发初始化动态数据，保留字段，写死填false
}

message GetPlayerAbTestParam {
  optional int64 uid = 1;
  optional int32 test_type = 2;
}

message CocGsMainBuildingEventData {
  optional int32 level = 1;
}

message FarmCropPlantEventData {
  optional int32 cropType = 1;
  optional int32 num = 2;
}

message FarmCropWaterEventData {
  optional int32 cropType = 1; // 废弃
  message CareInfo {
    optional int32 cropType = 1;
  }
  repeated CareInfo careInfo = 2;
}

message FarmCropFertilizeEventData {
  optional int32 cropType = 1;
  optional xlsRes.FarmCropRipeQuality beforeQuality = 2;
  optional xlsRes.FarmCropRipeQuality newQuality = 3;
  optional int64 farmId = 4;
}

message FarmCropEncourageEventData {
  optional int32 cropType = 1; // 废弃
  message EncourageInfo {
    optional int32 cropType = 1;
  }
  repeated EncourageInfo encourageInfo = 2;
}

message FarmCropHarvestEventData {
  message HarvestInfo {
    optional int32 cropType = 1;
    optional xlsRes.FarmCropRipeQuality quality = 2;
  }
  repeated HarvestInfo harvestInfo = 1;
  optional int32 smallCriticalNum = 2; // 废弃
  optional int32 bigCriticalNum = 3; // 废弃
}

message FarmCropLevelEventData {
  map<int32, int32> data = 1; // [cropType]level
}

message FarmBuildingLevelEventData {
  map<int32, int32> data = 1;// [confId]level
  optional bool isLevelUp = 2;
}

message FarmBuildingSellEventData {
  optional int64 price = 1; // 一级货币
}

message FarmCoinNumData {
  optional int64 curNum = 1;
  optional int64 obtainNum = 2;
  optional int64 costNum = 3;
  optional int32 reason = 4;
}

message FarmCoinEventData {
  map<int32, FarmCoinNumData> data = 1;
}

message FarmGridNumEventData {
  optional int32 num = 1;
  repeated int32 gridIds = 2;
}

message FarmGridLevelEventData {
  optional int32 level = 1;
  optional int32 gridId = 2;
}

message FarmStealEventData {
  optional int32 cropType = 1;
  optional xlsRes.FarmCropRipeQuality quality = 2;
}

message FarmFriendEventData {
  optional int32 num = 1;
}

message FarmVisitOtherEventData {

}

message FarmEvictEventData {

}

message FarmCropShiQuanShiMeiEventData {
  optional xlsRes.FarmCropCategory cropCategory = 1;
}

message FarmCropBaiHuaQiFangEventData {
  optional xlsRes.FarmCropCategory cropCategory = 1;
}

message FarmFishingEventData {
  optional proto_FarmFishingRecord fishingRecord = 1; // 钓鱼结果
}

message FarmStealingFishEventData {
  optional proto_FarmFishingRecord fishingRecord = 1; // 偷鱼结果
}

message FarmFishHandBookUpdateEventData {
  map<int32, proto_FarmHandBookRecord> handBookRecords = 1; // 鱼id -> 鱼图鉴信息
}

message FarmFishCardLevelUpEventData {
  map<int32, proto_FarmFishCardLevel> fishCardLevelInfo = 1; // 鱼id -> 鱼卡等级信息
}

message FarmObtainFishCardPackEventData {
  optional int64 num = 1; // 数量
}

message FarmObtainCollectionEventData {
  optional int32 num = 1;
}

message FarmPetFavorUpdateEventData {
  map<int32, int32> petFavorMap = 1;
}

message FarmPetNumEventData {
  optional int32 num = 1;
}

message FarmPetClothingNumEventData {
  optional int32 num = 1;
}

message FarmVillagerFavorUpdateEventData {
  map<int32, int32> villagerFavorMap = 1;
}

message FarmVillagerNumEventData {
  optional int32 num = 1;
}

message FarmCookOpenEventData {

}

message FarmCookGetCommentEventData {

}

message FarmCookVisitantPrebookEventData {

}

// 农场餐厅事件数据, 目前的 3 个 EventData 不需要数据, 后续需要数据的话再添加
message FarmCookEventData {
  oneof eventData {
    FarmCookOpenEventData farmCookOpenEventData = 1;
    FarmCookGetCommentEventData farmCookGetCommentEventData = 2;
    FarmCookVisitantPrebookEventData farmCookVisitantPrebookEventData = 3;
  }
}

enum DsrDsEventType {
  DDET_UNKNOWN = -1;       // 未知

  DDET_ABNORMAL = 1;       // DS异常
  DDET_OVERTIME = 2;       // 存活超时
  DDET_MIGRATE = 3;        // 迁移
  DDET_UPGRADE = 4;        // 升级
  DDET_DSA_ABNORMAL = 5;   // DSA异常
  DDET_NOT_FOUND = 6;      // DSA上没有查到该GameSession，可能已经停止或者异常销毁
  DDET_PID_NOT_EXIST = 7;  // 检查pid发现进程不存在，一般是进程崩溃+
  DDET_REGULAR_ENTER = 8;  // 玩家进入初始拉起
  DDET_MEM_LIMIT_EXCEED = 9;  // DS内存超出正常范围
  DDET_GM_MIGRATE = 10;  // gm指令触发迁移
  DDET_UPGRADE_SPECIFIC = 11;  // 升级指定GameSession
  DDET_HEALTH_CHECK_TIMEOUT = 12; // 心跳超时
  DDET_UPGRATE_BY_CLIENT = 13; // 客户端触发更新
}

// DSR通知DS事件
message DsrDsEventReq {
  optional int32  src = 1;
  optional int32  dst = 2 [(field_dest_serv) = true];
  optional int32  type = 3;           // 事件类型：DsrDsEventType
  optional string gameSessionId = 4;  // 单局会话id
  optional string dsId = 5;           // DS id，对于啾灵是worldId，对于主玩法对局就是battleId
  optional string param1 = 6;         // 事件参数1
  optional string param2 = 7;         // 事件参数2
  optional string param3 = 8;         // 事件参数3
}

message DsrDsEventRes {
  optional int32 result = 1;
  optional string gameSessionId = 2;  // 迁移目标会话id
}

// DSR通知创建GameSession结果
message DsrCreateGameSessionResultReq {
  optional int32 src = 1;
  optional int32 dst = 2 [(field_dest_serv) = true];
  optional string gameSessionId = 3;
  optional string dsId = 4;                     // DS id，对于啾灵是worldId，对于主玩法对局就是battleId
  optional int32 result = 5;                    // 参考 CreateGameSessionResult
  optional string gameSessionPassBackData = 6;  // 可填业务自定义数据，由服务端请求CreateGameSession时传入
  // 失败情况下，以下字段不赋值
  optional string aliasId = 7;
  optional string dsaServiceName = 8;
  optional uint64 dsaServiceId = 9;
  optional string fleetId = 10;
  map<string, string> dsUserData = 11;          // 可填业务自定义数据，由DS调用DSSDK的ActivateGameSession()时传入
}

message DsrCreateGameSessionResultRes {
  optional int32 result = 1;
}

message FarmCookLevelEventData {
  optional int32 level = 1;
}

message FarmCookScoreEventData {
  optional int32 score = 1; // 显示分数*10
  optional int32 cookLv = 2;
}

message FarmCookUsingSpecialFurnitureNumEventData {
  optional int32 num = 1;
}

message FarmCookUsingEmployeeNumEventData {
  optional int32 num = 1;
}

// 分布缓存抢读写凭证
message CacheLockPreemptReq {
  optional int32 dst = 1 [(field_dest_serv) = true];
  optional int32 type = 2;    // 业务类型
  optional int64 key = 3 [(field_dest_serv_hash_key) = true];     // 业务对应的唯一key
  optional int64 key2 = 4;    // 业务对应的唯一key2
  optional int64 svrVersion = 5; //请求方的版本号
}


message CacheLockPreemptRes {
  optional int32 type = 1;    // 业务类型
  optional int64 key = 2;     // 业务对应的唯一key
  optional int32 ret = 3;   // 返回码，说明抢锁的实际结果
  optional int64 key2 = 4;  // 业务对应的唯一key2
  optional int64 svrVersion = 5; //目标方的版本号
}

// 活动事件枚举
enum PlayerActivityEventEnum {
  Online = 1;		// 在线 
  PaySuccess = 2;	// 支付成功
  RAFFLE = 3;		// 祈愿
}

// 活动事件参数
message ActivityEventPara {
  map<string, int64> intPara = 1;	// int类型参数
  map<string, string> strPara = 2;	// str类型参数
}

// SS通信使用的红点结构
message SSActivityRedDotInfo {
  optional int32 activity_id = 1;
  optional bool reddot_show = 2;
  optional bool is_new = 3;         					//上新红点
  repeated proto_ActivityRedDot clickRedDotInfo = 4; //点击消失红点
  optional com.tencent.wea.xlsRes.ActivityMainConfig activityConfig = 5; //拍脸下发专用
  optional bool showInCenter = 6;  //是否是活动中心活动
}

// 活动服务Gm参数结构
message ActivityGMParam {
  oneof Data {
    int64 i64 = 1;     // i64 i32 bool
    uint64 u64 = 2;    // u64 u32
    string str = 3;
    double f64 = 4;    // f32 f64
    bytes byt = 5;
  }
}

message LeaseRedirectReq {
  optional int32 svrId = 1 [(field_dest_serv) = true];
  optional int64 uid = 2;
  optional int32 msgType = 3;
  optional bytes req = 4;
  optional string method = 5;
}

message LeaseRedirectRes {
  optional int32 result = 1;
  optional bytes res = 2;
}

enum CacheType {
  CT_UgcPlayerPublicInfo = 1; //UGC玩法的玩家离线可修改数据
  CT_PlayerPublicInfo = 2; //玩家PlayerPublic数据
}


message BatchGetCacheData {
  optional int64 uuid = 1;
  repeated string attrValues = 2; //获取的属性值列表,业务层需要把空字符串""当成没有这个属性处理。
}

message CacheInfoInMaster {
  optional int32 masterSvrId = 1; //如果是0，那么就不要缓存数据到slave
  map<string, string> attrs = 2; //缓存数据
  optional int32 dbVersion = 3; //当前db版本,暂时没用
  optional int32 syncVersion = 4; //同步版本号，用来校验数据同步是否一致
}

// 透传客户端GM指令到指定目标
message TransmitGMCommandWithTargetUIDReq {
  optional int64 uid = 1 [(field_hash_key) = true];   //目标uid
  optional string cmd_name = 2;
  repeated string param = 3;
}

message TransmitGMCommandWithTargetUIDRes {
  optional int32 result = 1;
}

message TransmitGMCommandWithServerIdReq {
  optional int32 svrId = 1 [(field_dest_serv) = true];
  optional string cmd_name = 2;
  repeated string param = 3;
}

message TransmitGMCommandWithServerIdRes {
  optional int32 result = 1;
}

message RpcCommonCsForwardReq {
  optional int64 hashKey = 1 [(field_hash_key) = true];
  optional CSHeader csHeader = 2;
  optional bytes csBody = 3;
  optional CsCommonForwardHeader forwardHeader  = 4;
}

message RpcCommonCsForwardRes {
  optional int32 nkErrorCode = 1;
  optional string nkErrorMsg = 2;
  optional CSHeader csHeader = 3;
  optional bytes scRsp = 4;
}

// 操作服务来源类型
enum OperateSvrSourceType {
  OSS_TYPE_GAME_SVR = 0;        // 来源-gamesvr
  OSS_TYPE_UGCAPP_SVR = 1;      // 来源-ugcappsvr
}

message RpcDispatchPlayerPublicReq {
  option (rpc_one_way) = true;
  optional int32 serverId = 1 [(field_dest_serv) = true];
  repeated PlayerPublicInfo playerChangeInfo = 2; // necessaryFiled + changedFiled
}

message RpcDispatchPlayerPublicRes {
    
}

message FarmRoomFurnitureBuyEventData {
  optional int32 furnitureId = 1;
  optional int64 number = 2;
}

message FarmRoomCropMachinePutInEventData {
  optional int32 itemId = 1;
}

message FarmRoomCropMachineHarvestEventData {
  optional int32 itemId = 1;
  optional xlsRes.FarmCropRipeQuality farmCropRipeQuality = 2;
}

message FarmRoomCropMachineSellEventData {
  repeated FarmItem farmItemList = 1;
}

message FarmRoomFurnitureArrangeEventData {
  repeated proto_FarmRoomItem farmRoomItemList = 1;
}

message FarmRoomFurnitureArrangeSingleEventData {
  optional int32 itemId = 1;
  optional int64 itemNum = 2;
}

message FarmRoomExtendEventData {

}

message FarmRoomFurnitureChangeColorEventData {

}

message FarmRoomVisitOtherEventData {

}


message CsForwardHeader {
  optional int64 uid = 1;
  optional int64 hashKey = 2;
  optional TlogRequiredFields tlogRequiredFields = 3;
}

message CsForwardHashKeyReq {
  // option (region_msg) = true;
  optional int32 destServType = 1 [(field_server_type) = true];
  optional int64 hashKey = 2 [(field_hash_key) = true];
  optional CSHeader csHeader = 3;
  optional bytes csBody = 4;
  optional CsForwardHeader forwardHeader  = 5;
  optional bytes additional = 6;
}

message CsForwardHashKeyRes {
  optional int32 nkErrorCode = 1;
  optional string nkErrorMsg = 2;
  optional CSHeader csHeader = 3;
  optional bytes scRsp = 4;
}

message CsForwardDestServReq {
  option (region_msg) = true;
  optional int32 destServ = 1 [(field_dest_serv) = true];
  optional CSHeader csHeader = 2;
  optional bytes csBody = 3;
  optional CsForwardHeader forwardHeader  = 4;
}

message CsForwardDestServRes {
  optional int32 nkErrorCode = 1;
  optional string nkErrorMsg = 2;
  optional CSHeader csHeader = 3;
  optional bytes scRsp = 4;
}

// 跨服务事件请求
message RpcApiSendEventReq {
  option (rpc_one_way) = true;
  optional int32 destSvrType = 1 [(field_server_type) = true];  // 目标服务器ID by: WeAServerType
  optional int64 hashKey = 2 [(field_hash_key) = true];	        // uid, roomId 等等
  optional string uuid = 3 ;                                    // 事件唯一ID,用于重试和链路追踪
  optional EventBatchSendData data = 4;                         // 事件内容
}

// 跨服务事件返回(废弃: RpcApiSendEventReq修改蹭单向RPC 故此回包结构体废弃)
message RpcApiSendEventRes {
  optional int32 res = 1;	                                   // 事件处理结果
}

// MQ跨服务事件转发
message RpcForwardEventMQReq {
  optional int32 destSvrType = 1 [(field_server_type) = true];  // 目标服务器ID by: WeAServerType
  optional int64 hashKey = 2 [(field_hash_key) = true];	        // uid, roomId 等等
  optional string messageId = 3 ;                               // 消息的messageId
  optional EventBatchSendData data = 4;                         // 事件内容
}

message RpcForwardEventMQRes {

}

message DscRegionMigrateInfo {
  optional string dscRegion = 1;
  optional int32 totalCnt = 2;
  optional int32 successCnt = 3;
  optional int32 failedCnt = 4;
}

enum ActivityWhiteListType {
  AWLT_Unknown = 0;
  AWLT_SuperCore = 1;
}

// LbsService使用的位置
message LbsPosition {
  optional int32 lon = 1;     // 经度
  optional int32 lat = 2;     // 纬度
}

// Lbs GeoTree存储的叶子节点指示器
message LbsGeoTreeLeafNodePointer {
  optional int32 min = 1;
  optional int32 max = 2;
}

// Lbs Geo叶子节点存储的具体业务信息
message LbsGeoNodeHoldData {
  optional LbsPosition pos = 1;       // 存储的业务dataId实际位置
  optional int32 type = 2;            // 业务类型
  optional int64 dataId = 3;
}

// Lbs Geo树节点
message LbsGeoTreeNode {
  optional int32 objId = 1;
  optional int32 fatherObjId = 2;
  optional int32 leftSonObjId = 3;
  optional int32 rightSonObjId = 4;
  optional int32 geoHashValue = 5;
  optional LbsGeoTreeLeafNodePointer leftPointer = 6;         // 辅助空间记录左右最小最大节点
  optional LbsGeoTreeLeafNodePointer rightPointer = 7;
  optional int32 leafPriorLeafNode = 8;                       // 叶子节点前一个叶子
  optional int32 leafNextLeafNode = 9;
  repeated LbsGeoNodeHoldData geoDatas = 10;
  optional LbsPosition selfPos = 11;                          // 节点中心位置
}

// Lbs树本身
message LbsGeoTree {
  repeated LbsGeoTreeNode nodes = 1;
  repeated int32 freeNodes = 2;             // 空闲节点也需要保存
  optional int32 rootNodeObjId = 3;         // 根结点
  optional int32 minLeafNodeObjId = 4;      // 最小叶子节点(也可以不存)
}

message LobbyNtfSimpleInfo {
  optional int64 lobbyId = 1;
  optional int32 mapId = 2;
  optional int64 ugcId = 3;
  optional int32 ugcSafeStatus = 4;
  optional int64 lobbyCreatorUid = 5;
}

message ComItem {
  optional int32 itemId = 1;
  optional int32 itemNum = 2;
  optional string billNo = 3;
  optional ComItemType type = 4;
  optional int64 itemNumLong = 5; // 替代itemNum
}

enum ComItemType {
  CIT_INVALID = 0;
  CIT_MAIN = 1;
  CIT_FARM = 2;
}

message LocalServiceCallParam {
  optional int32 index = 1;
  optional bytes param = 2;
  optional string clazz = 3;
}

// 透传local service调用
message LocalServiceCallReq {
  optional MetaDataType type = 1 [(field_meta_type) = true];
  optional int64 uuid = 2 [(field_meta_uuid) = true];
  optional string clazz = 3;
  optional string method = 4;
  //    repeated bytes params = 5; // 废弃
  repeated LocalServiceCallParam params = 6;
  optional bool loaded = 7; // 只从内存中获取(ifPresent)
  optional bool isOneWay = 8 [(field_dyn_oneway) = true]; // field_dyn_oneway仅可用于LocalServiceCallReq和LocalServiceServerCallReq
}

message LocalServiceCallRes {
  optional string clazz = 1;
  optional string method = 2;
  //    optional bytes result = 3; // 废弃
  optional LocalServiceCallParam result = 4;
}

// [UDP连接]啾灵用户DS下线, starpsvr需要广播通知到各starp侧业务进程
message RpcReportPlayerDsOfflineNtfReq {
  option (rpc_one_way) = true;
  optional int64  uid = 1 [(field_hash_key) = true];  // 玩家uid
  optional int64  starPWorldId = 2;                            // 啾灵世界id
  optional int64  quitCode     = 3;                            // 退出quitCode,参考QuitBattleCode
  map<string, string> callback_data = 4;
  optional int64 roleId = 5;
}

message RpcReportPlayerDsOfflineNtfRes {

}

message FriendIntimacyInfo {
  optional int64 friendUid = 1;
  optional proto_StarPPlayerIntimacyGeneralDBInfo playerIntimacyGeneralDBInfo = 2; // 亲密度信息
}