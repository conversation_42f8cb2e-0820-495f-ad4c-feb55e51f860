syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "common.proto";
import "ss_common.proto";
import "ss_head.proto";
import "player_info.proto";
import "cs_head.proto";
import "base_common.proto";

import "attr_StarPDsCommonDbKey.proto";
import "attr_StarPDsCommonDbInfo.proto";

// ugc商店内购发货通知
message RpcUgcDataStorePaidItemAddReq {
  optional int64 ugcId = 1;
  optional int64 uid = 2 [(field_hash_key) = true];
  optional int64 battleId = 3;  // battleId
  optional string goodsId = 4;  // 商品ID
  map<string, int32> items = 5; // 物品 id/num
  optional string billNum = 7;  // 订单号
  optional string checkCode = 8; // 校验是否存在作弊的问题
  optional string busBillNum = 9;  // 业务自己的跟踪订单号
  optional int64 costDiamonds = 10; // 消费的星钻
}

message RpcUgcDataStorePaidItemAddRes {
  optional int32 result = 1;
}

// gamesvr 请求玩家数据
message RpcUgcDataStoreGetPlayerDataReq {
    option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];
  optional int64 ugcId = 2;
  optional bool isNeedFragment = 3; // 是否需要分包
  optional int32 worldId = 4 [(field_region_key) = true];
}

message RpcUgcDataStoreGetPlayerDataRes {
  optional UgcPlayerDataStoreData playerData = 1;
  optional int32 fragmentNum = 2; // 分包数量

}

message RpcGetUgcPublicPlayerDataAttrsReq {
  optional int64 ugcId = 1;
  optional int64 uid = 2 [(field_hash_key) = true];
  repeated string keys = 3; //要查询的key  
}
message RpcGetUgcPublicPlayerDataAttrsRes {
    map<string, int64> attrs = 1;
}

message RpcUpdateUgcPublicPlayerDataAttrReq {
  optional int64 ugcId = 1;
  optional int64 uid = 2 [(field_hash_key) = true];
  optional string attrName = 3; //要修改的属性
  optional int64 attrValue = 4; //要改成的值
  optional UgcPlayerPublicAttrRange range = 5; //限定的上下限，0、0表示不限制
}

message RpcUpdateUgcPublicPlayerDataAttrRes {
}

// 获取UGC排行榜数据
message RpcFetchUgcTopRankReq {
    optional int64 hashKey = 99 [(field_hash_key) = true];   // hashkey 一般等于 rankId  除非有个别地图非常非常火 支持打散
    optional int64 mapId = 1;
    optional int64 rankId = 2;   // 排行榜全局ID
    optional int64 uid = 3;      // 玩家ID
    optional int32 from_index = 4;   // 拉取的起始index
    optional int32 count = 5;      // 拉取的数量
    optional bool without_info = 6;   // 是否需要玩家信息
    optional int32 type = 7;    // 0:全服  1:好友排行榜
}

message RpcFetchUgcTopRankRes {
    repeated PlayerRankInfo rank = 1;
    optional PlayerRankInfo self_rank = 2;
    repeated PlayerPublicInfo info = 3;
    optional PlayerPublicInfo self_info = 4;
    optional int64 timeStamp = 5;
    optional int32 friendNum = 6;
}

// 批量获取某个排行榜的指定玩家的 playerpublic
message RpcBatchGetUgcRankPlayerPublicReq {
    optional int64 hashKey = 99 [(field_hash_key) = true];   // hashkey 一般等于 rankId  除非有个别地图非常非常火 支持打散
    optional int64 mapId = 1;
    optional int64 rankId = 2;   // 排行榜全局ID
    repeated int64 uids = 3;  // uids
}

message RpcBatchGetUgcRankPlayerPublicRes {
    repeated PlayerPublicInfo info = 3;
}

// 上报UGC排行榜数据
message RpcUpdateUgcMapRankReq {
    optional int64 mapId = 1;   //mapId
    optional int64 rankId = 2;   //rankId
    optional int64 uid = 3 [(field_hash_key) = true];
    optional int32 score = 4;    // 上报的分数
    repeated int32 extraScores = 5;   // 额外分数
}

message RpcUpdateUgcMapRankRes {
    optional int32 result = 1;    //返回结果
}


// 获取指定uid的排行榜数据
message RpcGetUgcRankAppointPlayerReq {
    optional int64 hashKey = 99 [(field_hash_key) = true];   // hashkey 一般等于 rankId  除非有个别地图非常非常火 支持打散
  optional int64 mapId = 1;    // 地图ID
  optional int64 rankId = 2;   // 排行榜全局ID
  optional int64 uid = 3;
  optional int32 type = 4;    // 0:全服  1:好友排行榜
  optional int64 friendUid = 5;
}

message RpcGetUgcRankAppointPlayerRes {
  optional PlayerRankInfo rank = 1;
}

// 删除自己的排行榜数据
message RpcDeleteUgcRankEntryByPlayerReq {
  optional int64 mapId = 1;    // 地图ID
  optional int64 rankId = 2;   // 排行榜全局ID
  optional int64 uid = 3 [(field_hash_key) = true];
}

message RpcDeleteUgcRankEntryByPlayerRes {
  optional int32 result = 1;    //返回结果
}

// 获取指定名次的数据
message RpcGetUgcRankAppointRankNoReq {
    optional int64 hashKey = 99 [(field_hash_key) = true];   // hashkey 一般等于 rankId  除非有个别地图非常非常火 支持打散
  optional int64 mapId = 1;    // 地图ID
  optional int64 rankId = 2;   // 排行榜全局ID
  optional int32 rankNo = 3;     // 名次
  optional int32 type = 4;    // 0:全服  1:好友排行榜
  optional int64 uid = 5;
}

message RpcGetUgcRankAppointRankNoRes {
  optional PlayerRankInfo rank = 1;
}

message RpcUgcdatastoreCsForwardReq {
    option (region_msg) = true;
  optional int64 hashKey = 1 [(field_hash_key) = true];
  optional CSHeader csHeader = 2;
  optional bytes csBody = 3;
  optional CsCommonForwardHeader forwardHeader  = 4;
  optional int32 worldId = 5 [(field_region_key) = true];
}

message RpcUgcdatastoreCsForwardRes {
  optional int32 nkErrorCode = 1;
  optional string nkErrorMsg = 2;
  optional CSHeader csHeader = 3;
  optional bytes scRsp = 4;
}

/* 展示数据接口 */

// 单个玩家数据拉取
message RpcGetShowDataReq {
  option (region_msg) = true;
  optional int64 uid = 1 [(field_hash_key) = true];  // 玩家id hashkey 防止某些地图过热
  optional int64 ugcId = 2;  // 地图id

  repeated UgcShowDataStoreWrapper wrappers = 3;  // 请求的数据类型,以及是否指定key
  optional int32 worldId = 4[(field_region_key) = true];
}

message RpcGetShowDataRes {
  optional int32 errCode = 1;  // 错误码
  repeated UgcShowDataStoreWrapper wrappers = 2;  // 玩家的数据
}

// 单个玩家数据更新
message RpcEditShowDataReq {
  optional int64 uid = 1 [(field_hash_key) = true];  // 玩家id hashkey 防止某些地图过热
  optional int64 ugcId = 2;  // 地图id

  optional UgcShowDataStoreOperate operate = 3;  // 需要操作的结构
  optional bool isFinalEdit = 10; // ds对局结束前的变更
  optional int64 battleId = 11;  // 对局battleId
}

message RpcEditShowDataRes {
  optional int32 errCode = 1;  // 错误码
  optional UgcShowDataStoreWrapper wrapper = 2;  // 操作后的数据
}

message RpcGetShowDataSnapshotReq {
  optional int64 uid = 1 [(field_hash_key) = true];  // 玩家id hashkey 防止某些地图过热
  optional int64 ugcId = 2;  // 地图id
}

message RpcGetShowDataSnapshotRes {
  optional int32 errCode = 1;
  optional UgcShowDataSnapshot snapShot = 2;
}

/* 展示数据接口 */

// 获取指定时间(对应天数)的玩家排行榜信息请求(不使用cache)
message RpcGetUgcRankInfoWithTimeNoCacheReq {
  optional int64 hashKey = 1 [(field_hash_key) = true];
  optional int64 mapId = 2;               // 地图ID
  optional int64 rankId = 3;              // 排行榜ID
  optional int64 uid = 4;                 // 玩家ID
  optional int32 from_index = 5;          // 拉取的起始index
  optional int32 count = 6;               // 拉取的数量
  optional bool without_info = 7;         // 是否需要玩家信息
  optional int32 type = 8;                // 0:全服  1:好友排行榜
  optional int64 time = 9;                // 时间戳
}

// 获取指定时间(对应天数)的玩家排行榜信息请求(不使用cache)
message RpcGetUgcRankInfoWithTimeNoCacheRes {
  optional int32 result = 1;              // 0:成功 1:失败
  repeated PlayerRankInfo rankList = 2;   // 排行榜列表
  optional PlayerRankInfo self_rank = 3;  // 自己的排名
  repeated PlayerPublicInfo info = 4;     // 玩家信息列表
  optional PlayerPublicInfo self_info = 5;// 自己的信息
  optional int64 timeStamp = 6;           // 时间戳
  optional int32 friendNum = 7;           // 好友数量
}
