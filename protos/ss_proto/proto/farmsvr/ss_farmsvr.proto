syntax = "proto2";

option cc_generic_services = false;

package com.tencent.wea.protocol;

import "common.proto";
import "ResKeywords.proto";
import "ss_head.proto";
import "ss_common.proto";
import "attr_PlayerPublicProfileInfo.proto";
import "attr_PlayerPublicEquipments.proto";
import "attr_FarmItem.proto";
import "attr_FarmAttr.proto";
import  "attr_FarmWelcomeInfo.proto";
import "attr_FarmEventValue.proto";
import "attr_FarmEvent.proto";
import "attr_FarmCustomData.proto";
import "attr_FarmHotSpring.proto";
import "attr_CookEmployer.proto";
import "ResFarmTask.proto";
import "attr_CookVisitant.proto";
import "attr_FarmHotSpringBuffSource.proto";

// 数据迁移
message RpcFarmMigrateReq {
  required int32 dest = 1 [(field_dest_serv) = true];
  optional int64 farmId = 2 [(field_dest_serv_hash_key) = true];
}

message RpcFarmMigrateRes {

}

message RpcFarmCsForwardReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional int32 csCmdId = 3;
  optional bytes binData = 4;
  optional TlogRequiredFields tlogRequiredFields = 5;
  optional ForwardToFarmCtx ctx = 6; // 同步信息
}

message RpcFarmCsForwardRes {
  optional int32 csCmdId = 1;
  optional bytes binData = 2;
}

message RpcFarmHeartBeatReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional int64 clientVer = 3;
  optional ForwardToFarmCtx ctx = 4;
}

message RpcFarmHeartBeatRes {
}

message FarmGMParam {
  oneof Data {
    int64 i64 = 1; // i64 i32 bool
    uint64 u64 = 2; // u64 u32
    string str = 3;
    double f64 = 4; // f32 f64
    bytes byt = 5;
  }
}

message RpcFarmGMCmdReq {
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional int64 operatorID = 2;
  optional int32 cmdID = 3;
  repeated FarmGMParam params = 4;
}

message RpcFarmGMCmdRes {
  repeated FarmGMParam ret = 4;
}

// 创建
message RpcFarmCreateReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int32 templateId = 2;
  optional int64 version = 3; // 玩家版本号
  optional TlogRequiredFields tlogRequiredFields = 4;
  optional int32 initType = 5; // 初始类型
  optional int64 ownerFarmID = 6; // 如果是NPC农场，这里会填入主人的农场ID
}

message RpcFarmCreateRes {
}

// 进入
message RpcFarmEnterReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional com.tencent.wea.protocol.proto_PlayerPublicProfileInfo playerPublicProfileInfo = 3;   //玩家可公开个人信息
  optional com.tencent.wea.protocol.proto_PlayerPublicEquipments playerPublicEquipments = 4;  //玩家可公开装扮信息
  optional int32 localDsId = 5;
  optional string playerOpenId = 6;
  optional int64 cliVer = 7;
  optional bool forceCreateDS = 8; // 当没有DS的时候，是否强制创建DS，即使一个人也创建DS（只在测试环境生效）
  optional int32 leftWaterRewardTimes = 9; // 废弃
  optional bool forceEnter = 10; // 废弃
  optional ForwardToFarmCtx ctx = 11; // 同步月卡信息
  repeated int64 blackUids = 12;
  optional bool cropConnectionUpdate = 13; // 养殖物联通性更新
  optional int64 ownerUID = 14; // 如果是npc农场主人进入，则会赋值
  optional int32 npcStage = 15; // 如果是npc农场主人进入，则代表npc农场阶段
  optional TlogRequiredFields tlogRequiredFields = 16; // tlog公参
  optional int32 source = 17; // 进入来源（客户端入口）
  optional int32 friendStatus = 18; // 好友状态(0:未设置，1:是游戏好友，10:是平台好友，11:游戏好友和平台好友)
  optional int32 newbieState = 19;
}

message RpcFarmEnterRes {
  optional DSCreateState dsState = 1; // 本次进入是否创建了新DS
  optional int32 farmLevel = 2;
  optional bool isFarmMonthlyPass = 3; // 农场主是否月卡
  optional int32 petFavor = 4;
  optional FarmPartyEnterTlogCtx partyEnterCtx = 5; // 农场派对参与tlog上下文
}

// 离开
message RpcFarmExitReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional string playerOpenId = 3;
}

message RpcFarmExitRes {
  optional int32 farmLevel = 2;
  optional bool isFarmMonthlyPass = 3; // 农场主是否月卡
  optional bool isPetHouseKeepingEnable = 4; // 宠物是否开启看家护院
  optional int32 petFeedValue = 5; // 主人家宠物剩余饱腹值
  optional int32 petFavor = 6;
  optional FarmPartyLeaveTlogCtx partyLeaveCtx = 7; // 农场派对离开tlog上下文
}

message RpcFarmSyncPlayerInfoReq {
  optional int64 player_uid = 1;
  optional int64 farm_id = 2 [(field_hash_key) = true];
  optional com.tencent.wea.protocol.proto_PlayerPublicProfileInfo playerPublicProfileInfo = 3;   //玩家可公开个人信息
  optional com.tencent.wea.protocol.proto_PlayerPublicEquipments playerPublicEquipments = 4;   //玩家可公开装扮信息
}

message RpcFarmSyncPlayerInfoRes {
}

message RpcFarmStealingCorpNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  repeated com.tencent.wea.protocol.proto_FarmItem getItems = 2;
  optional int64 fromFarmId = 3;
  optional string billNo = 4;
  map<int32, int64> fishScore = 5;
  optional string beStolenOpenid = 6;
  optional int32 ripeQuality = 7; //地块成熟类型
  optional bool isStealFishFail = 8;  // 是否偷鱼失败
  optional int32 cropCategory = 9;
}

message RpcFarmStealingCorpNtfRes {
}

message RpcFarmOwnerLoginLogoutReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional bool login = 2;
  optional int64 cliVer = 3;
  optional ForwardToFarmCtx ctx = 4; // 同步信息
  optional TlogRequiredFields tlogRequiredFields = 5;
}

message RpcFarmOwnerLoginLogoutRes {
}

enum GetFarmInfoFields { // 枚举值和wea_farm.attr字段编号一致
  farmInfoUnknown = 0;
  farmItemInfo = 5; //道具信息
  farmMapInfo = 6; //地格信息
  farmBuildingInfo = 7; //建筑信息
  farmCropLevelInfo = 8; //养殖物等级信息
  farmHandBookInfo = 19; // 鱼图鉴等级
  farmFishCardLevel = 20; // 鱼卡等级
  farmCollectionInfo = 21; // 收藏品信息
  farmPetInfo = 22; // 宠物信息
  farmVillagerInfo = 37; // 村民信息
  farmStolenStat = 55; // 被盗信息
  farmHotInfo = 58; // 热度信息
  farmPartyInfo = 59; // 派对信息
}

message RpcGetFarmInfoReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  repeated int32 fields = 2;// 协议改造 枚举值切换至整数值 详情参考GetFarmInfoFields
}

message RpcGetFarmInfoRes {
  optional proto_FarmAttr attr = 1;
}

// 访客
message RpcFarmGetVisitorsReq {
  option (region_msg) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int32 worldId = 2[(field_region_key) = true];
}

message RpcFarmGetVisitorsRes {
  repeated int64 visitors = 1;
}

message RpcModifyFarmMothCardReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int32 mode = 2;
  optional int64 time = 3;
  optional int64 startTime = 4;
}

message RpcModifyFarmMothCardRes {

}

// 农场加道具
message RpcFarmAddItemReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int32 itemId = 2;
  optional int64 itemNum = 3;
  optional string billno = 4;
  optional int64 expireMs = 5;
}

message RpcFarmAddItemRes {
}

message RpcRecvFarmGiftReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 fromUID = 2; // 谁送的
  optional FarmGift gift = 3;
  optional TlogRequiredFields tlogRequiredFields = 4;
}

message RpcRecvFarmGiftRes {
  optional int64 giftID64 = 1;
}

message RpcBackFarmGiftReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional FarmGift gift = 2;
  optional TlogRequiredFields tlogRequiredFields = 3;
  optional int64 targetID = 4;
  optional int32 giftCount = 5;
}

message RpcBackFarmGiftRes {

}

message RpcFarmGiftCheckReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional string billno = 2;
}

message RpcFarmGiftCheckRes {
  optional int32 giftNum = 1;
}

message RpcFarmFriendBeTagNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 friendId = 2;
  optional int32 tagType = 3;
}

message FarmBuffArg {
  optional int32 buffId = 1;
  optional int64 startTimeSec = 2;
  optional int64 endTimeSec = 3;
}

message FarmBuffArgs {
  optional string key = 1;
  repeated FarmBuffArg buffArgs = 2;
}

message RpcFarmBuffAddReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional string source = 2; // 来源 FarmBuffConf.Source
  repeated FarmBuffArgs buffArgs = 3;
}

message RpcFarmBuffAddRes {

}

message RpcFarmBuffDelReq {
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional string source = 2; // 来源 FarmBuffConf.Source
  repeated FarmBuffArgs buffArgs = 3;
}

message RpcFarmBuffDelRes {

}

// 创建
message RpcHouseCreateReq {
  optional int64 houseId = 1 [(field_hash_key) = true];
  optional int32 templateId = 2;
  optional int64 version = 3; // 玩家版本号
}

message RpcHouseCreateRes {

}

// 进入
message RpcHouseEnterReq {
  optional int64 houseId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional com.tencent.wea.protocol.proto_PlayerPublicProfileInfo playerPublicProfileInfo = 3;   //玩家可公开个人信息
  optional com.tencent.wea.protocol.proto_PlayerPublicEquipments playerPublicEquipments = 4;  //玩家可公开装扮信息
  optional int32 localDsId = 5;
  optional string playerOpenId = 6;
  optional int64 cliVer = 7;
  optional bool forceCreateDS = 8; // 当没有DS的时候，是否强制创建DS，即使一个人也创建DS（只在测试环境生效）
  optional int32 leftWaterRewardTimes = 9; // 废弃
  optional bool forceEnter = 10; // 废弃
  repeated int64 blackUids = 11;
  optional int32 buildingId = 12; // house的buildingId
  optional int64 roomId = 13; // house的roomId
  optional TlogRequiredFields tlogRequiredFields = 14; // tlog公参
  optional int32 source = 15; // 进入来源（客户端入口）
  optional int32 friendStatus = 16; // 好友状态(0:未设置，1:是游戏好友，10:是平台好友，11:游戏好友和平台好友)
}

message RpcHouseEnterRes {
  optional DSCreateState dsState = 1; // 本次进入是否创建了新DS
  optional int32 farmLevel = 2;
  optional int32 livingVillagerId = 3;
  optional FarmPartyEnterTlogCtx partyEnterCtx = 4; // 农场派对参与tlog上下文
}

// 离开
message RpcHouseExitReq {
  optional int64 houseId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional string playerOpenId = 3;
  optional int32 clientReason = 4;
  optional int32 buildingId = 5; // house的buildingId
  optional int64 roomId = 6; // house的roomId
}

message RpcHouseExitRes {
  optional int32 farmLevel = 2;
  optional int32 livingVillagerId = 3;
  optional FarmPartyLeaveTlogCtx partyLeaveCtx = 4; // 农场派对离开tlog上下文
}

message RpcHouseHeartBeatReq {
  option (rpc_one_way) = true;
  optional int64 houseId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional int64 clientVer = 3;
  optional ForwardToHouseCtx ctx = 4;
  optional int32 buildingId = 5; // house的buildingId
}

message RpcHouseHeartBeatRes {
}

message RpcHouseOwnerLoginLogoutReq {
  optional int64 houseId = 1 [(field_hash_key) = true];
  optional bool login = 2;
  optional int64 cliVer = 3;
}

message RpcHouseOwnerLoginLogoutRes {
}

message RpcHouseSyncPlayerInfoReq {
  optional int64 player_uid = 1;
  optional int64 house_id = 2 [(field_hash_key) = true];
  optional com.tencent.wea.protocol.proto_PlayerPublicProfileInfo playerPublicProfileInfo = 3;   //玩家可公开个人信息
  optional com.tencent.wea.protocol.proto_PlayerPublicEquipments playerPublicEquipments = 4;   //玩家可公开装扮信息
  optional int32 buildingId = 5; // house的buildingId
  optional int64 roomId = 6; // house的roomId
}

message RpcHouseSyncPlayerInfoRes {
}

message RpcHouseVisitorInfoReq {
  optional int64 houseId = 1 [(field_hash_key) = true];
}

message RpcHouseVisitorInfoRes {
  optional int32 visitorNum = 1;
  optional bool atHome = 2;
}

message RpcHouseRoomOnLeaveNtfReq {
  option (rpc_one_way) = true;
  optional int64 houseId = 1 [(field_hash_key) = true];
  optional int32 clientReason = 2;
  optional int32 buildingId = 3; // house的buildingId
  optional int64 roomId = 4; // house的roomId
}


message RpcTriggerEventNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional proto_FarmEvent event = 2;
}


message RpcSyncInfoNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional ForwardToFarmCtx ctx = 2; // 同步信息
}

message RpcEndEventNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional int32 evtUid = 2;
  optional proto_FarmCustomData crossEventDat = 3;
  optional int32 seriesID = 4;
}

message RpcFixFarmActiveItemsReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  repeated int32 farmActiveItemIds = 2;
}

message RpcFixFarmActiveItemsRes {
}
// 创建餐厅
message RpcCookCreateReq {
  optional int64 cookId = 1 [(field_hash_key) = true];
  optional int32 templateId = 2;
  optional int64 version = 3; // 玩家版本号
}

message RpcCookCreateRes {

}

// 进入
message RpcCookEnterReq {
  optional int64 cookId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional com.tencent.wea.protocol.proto_PlayerPublicProfileInfo playerPublicProfileInfo = 3;   //玩家可公开个人信息
  optional com.tencent.wea.protocol.proto_PlayerPublicEquipments playerPublicEquipments = 4;  //玩家可公开装扮信息
  optional int32 localDsId = 5;
  optional string playerOpenId = 6;
  optional int64 cliVer = 7;
  optional bool forceCreateDS = 8; // 当没有DS的时候，是否强制创建DS，即使一个人也创建DS（只在测试环境生效）
  optional int32 leftWaterRewardTimes = 9; // 剩余浇水产出次数
  optional bool forceEnter = 10; // 强制进入小窝，忽略兼容性检查，并在ds创建失败后使用默认ds
  repeated int64 blackUids = 11;
  optional TlogRequiredFields tlogRequiredFields = 12; // tlog公参
  optional int32 source = 13; // 进入来源（客户端入口）
  optional int32 friendStatus = 14; // 好友状态(0:未设置，1:是游戏好友，10:是平台好友，11:游戏好友和平台好友)
}

message RpcCookEnterRes {
  optional DSCreateState dsState = 1; // 本次进入是否创建了新DS
  optional int32 farmLevel = 2;
  optional FarmPartyEnterTlogCtx partyEnterCtx = 3; // 农场派对参与tlog上下文
  optional int32 cookLevel = 4;
  optional bool isOpen = 5;
  optional CookConditionInfo cookConditionInfo = 6; // 餐厅成就任务条件信息
}

message CookConditionInfo {
  optional int32 level = 1;
  optional int32 score = 2;
  optional int32 specialFurnitureNum = 3;
  optional int32 workingEmployeeNum = 4;
}

// 离开
message RpcCookExitReq {
  optional int64 cookId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional string playerOpenId = 3;
  optional int32 clientReason = 4;
}

message RpcCookExitRes {
  optional int32 farmLevel = 1;
  optional FarmPartyLeaveTlogCtx partyLeaveCtx = 2; // 农场派对离开tlog上下文
  optional int32 cookLevel = 3;
  optional bool isOpen = 4;
}

message RpcCookHeartBeatReq {
  option (rpc_one_way) = true;
  optional int64 cookId = 1 [(field_hash_key) = true];
  optional int64 playerUid = 2;
  optional int64 clientVer = 3;
  optional ForwardToCookCtx ctx = 4;
}

message RpcCookHeartBeatRes {
}

message RpcCookOwnerLoginLogoutReq {
  optional int64 cookId = 1 [(field_hash_key) = true];
  optional bool login = 2;
  optional int64 cliVer = 3;
}

message RpcCookOwnerLoginLogoutRes {
}

message RpcCookSyncPlayerInfoReq {
  optional int64 player_uid = 1;
  optional int64 cook_id = 2 [(field_hash_key) = true];
  optional com.tencent.wea.protocol.proto_PlayerPublicProfileInfo playerPublicProfileInfo = 3;   //玩家可公开个人信息
  optional com.tencent.wea.protocol.proto_PlayerPublicEquipments playerPublicEquipments = 4;   //玩家可公开装扮信息
}

message RpcCookSyncPlayerInfoRes {
}

message RpcCookHireFinishReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional com.tencent.wea.protocol.proto_CookEmployer employer = 2;
}

message RpcCookHireFinishRes {

}

message FarmTaskEventValue {
  optional int32 id = 1;
  optional int64 iv = 2;
  optional string sv = 3;
}

message RpcTaskConditionNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional int32 conditionType = 2;// 条件类型
  optional int64 value = 3;// 条件值
}

message RpcCommitItemToCloudReq {
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional int32 series = 2;          // 哪个事件（填系列）
  optional int32 itemID = 3;          // 提交什么道具
  optional int64 itemCount = 4;       // 提交几个
  optional int32 step = 5;            // 步骤
  optional int64 playerID = 6;        // 玩家ID
  optional string billNo = 7;         // 流水号
  optional int32 eid = 8;             // 事件唯一id
  optional int32 eventID = 11;        // 事件ID tlog
  optional TlogRequiredFields tlogRequiredFields = 12; // tlog
  optional float ratio = 13;          // 倍率 tlog
  optional int64 itemCountLeft = 14;      // npc还能再接受几个 tlog
  optional int32 farmLevel = 15;      // npc的农场等级 tlog
}
message RpcCommitItemToCloudRes {
  optional int64 coin = 1; // 获得的农场币
  optional int32 eid = 2; // 事件唯一id
  optional int64 unitPrice = 3; // 单个价格
  optional int32 eventID = 4;
  optional float ratio = 5;          // 倍率 tlog
  optional int64 itemCountLeft = 6;      // npc还能再接受几个 tlog
  optional int32 farmLevel = 7;      // npc的农场等级 tlog
}

enum FarmRedPacketModel {
  FRPM_Unknown = 0;
  FRPM_Farm = 1;
  FRPM_House = 2;
}

// 创建红包
message RpcFarmCreateRedPacketReq {
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional int64 uuid = 2; // 红包uuid
  optional int32 packetId = 3; // 红包配置id
  optional int64 senderId = 4; // 发送发uid
  optional ObjectPosition position = 5; // 坐标
  optional int32 totalCount = 6; // 红包总数量
  optional string senderName = 7; // 发送人名字
  optional int32 model = 8;// 红包作用的模块,FarmRedPacketModel
}
message RpcFarmCreateRedPacketRes {

}

// 开红包
message RpcFarmOpenRedPacketReq {
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional int64 uuid = 2; // 红包uuid
  optional int32 receiveCount = 3; // 已领取数量
  optional int32 model = 8;// 红包作用的模块,FarmRedPacketModel
}

message RpcFarmOpenRedPacketRes {

}

// 进入温泉
message RpcFarmEnterHotSpringNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 hotSpringUid = 2;
  optional proto_FarmHotSpring hotSpring = 3;
  optional int32 hotLevel = 4;
}

// 离开温泉
message RpcFarmLeaveHotSpringNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 hotSpringUid = 2;
  optional proto_FarmHotSpring hotSpring = 3;
  optional int32 hotLevel = 4;
}

// 农场外部操作类型
enum FarmExternalOperateType {
  FEOT_Unknown = 0;
  FEOT_Test = 1;                  // 测试
  FEOT_HarvestCrop = 2;           // 收获作物
  FEOT_HarvestAnimal = 3;         // 收获动物
  FEOT_HarvestFishPool = 4;       // 收获鱼塘
  FEOT_HarvestMachine = 5;        // 收获加工器
  FEOT_QueryItems = 6;            // 查询仓库物品
  FEOT_SellItems = 7;             // 售卖仓库物品
  FEOT_QueryFarmInfo = 8;         // 查询农场信息
  FEOT_QueryFarmCanStealInfo = 9; // 查询农场可偷信息
  FEOT_ReopenCook = 10;           // 补食材并收款
  FEOT_ServeVisitant = 11;        // 接待贵宾并预约
}


message RpcFarmExternalOperateReq {
  option (region_msg) = true;
  option (forward_admin_call) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional uint32 opType = 2;     // 操作类型 枚举见FarmExternalFarmOperateType
  optional FarmExternalOperateData opData = 3;
  optional int32 worldId = 4 [(field_region_key) = true];
}

message FarmExternalOperateData {
  message HarvestCropData {
  }
  message HarvestAnimalData {
  }
  message SellItemsData {
    map<int32, int64> items = 1;
  }
  message QueryFarmCanStealInfoData {
    optional int64 playerUid = 1; // 玩家uid
  }
  optional HarvestCropData harvestCropData = 1;
  optional HarvestAnimalData harvestAnimalData = 2;
  optional SellItemsData sellItemsData = 7;
  optional QueryFarmCanStealInfoData queryFarmCanStealInfoData = 8;
}

message RpcFarmExternalOperateRes {
  message HarvestCropRes { // 收获作物返回
    optional bool itemNotEnough = 1; // 是否有农场币不足
    optional int32 harvestNum = 2; // 收获数量
    optional int32 plantNum = 3; // 种植数量
  }
  message HarvestAnimalRes { // 收获动物返回
    optional bool itemNotEnough = 1; // 是否有农场币不足
    optional int32 careNum = 2; // 喂食数量
    optional int32 encourageNum = 3; // 助产数量
    optional int32 harvestNum = 4; // 收获数量
  }
  message HarvestFishPoolRes { // 收获鱼塘返回
    optional int32 fishingNum = 1; // 钓鱼次数
    optional bool itemNotEnough = 2; // 是否有农场币不足
    optional int32 bait = 3; // 鱼饵类型
  }
  message HarvestCropMachineRes { // 收获加工器返回
    optional int32 takeOutNum = 1;
    optional int32 putInNum = 2;
    optional bool itemNotEnough = 3;
  }
  message FarmItem {
    optional int32 itemId = 1;     // 道具id
    optional int64 itemNum = 2;    // 道具数量
    optional int64 price = 3;      // 单价
    optional int32 cropLv = 4;     // 熟练度等级
    optional bool isCropMaxLv = 5; // 熟练度是否满级
  }
  message FarmItemMap {
    map<int32, FarmItem> items = 1;
  }
  message QueryItemsRes { // 查询仓库物品返回
    map<int32, FarmItemMap> itemsByType = 1; // 道具类型 -> 道具
  }
  message SellItemsRes { // 售卖仓库物品返回
    map<int32, int64> obtainItems = 1; // 售卖获得道具
  }
  message QueryFarmInfoRes { // 查询农场基础信息返回
    optional int32 farmLv = 1; // 农场等级
    optional int64 coinNum = 2; // 农场币数量
    optional int32 collectionNum = 3; // 收藏品数量
    optional int32 petId = 4; // 宠物id
    optional int64 petHungryTime = 5; // 宠物饥饿时间
    optional bool petHouseKeeping = 6; // 宠物看家护院开启
    optional int64 monthCardExpireTime = 7; // 月卡到期时间
    optional PlayerReportFarmCropStatusInfo cropInfo = 1000; // 作物动物鱼塘等状态信息
  }
  message QueryFarmCanStealInfoRes { // 查询农场可偷信息
    optional bool canSteal = 1; // 是否可偷
    optional bool isFarmBlocked = 2; // 是否被屏蔽
    map<int32, int32> canStealCropNum = 3; // 可偷养殖物数量 类型->数量
    optional int32 fishingPoolLayer = 4; // 可偷鱼塘层数
  }
  message CookReopenRes {// 收款并补食材
    optional int64 consumeCoin = 1;
    optional int64 coinGain = 2;
    optional int64 FarmExpGain = 3;
    optional int64 CookLikeGain = 4;
  }
  message CookServeVisitantRes {
    optional int64 coin = 1;
    optional int32 groupId = 2;
  }
  optional HarvestCropRes harvestCropRes = 1;
  optional HarvestAnimalRes harvestAnimalRes = 2;
  optional HarvestFishPoolRes harvestFishPoolRes = 3;
  optional HarvestCropMachineRes harvestCropMachineRes = 4;
  optional QueryItemsRes queryItemsRes = 5;
  optional SellItemsRes sellItemsRes = 6;
  optional QueryFarmInfoRes queryFarmInfoRes = 7;
  optional QueryFarmCanStealInfoRes queryFarmCanStealInfoRes = 8;
  optional CookReopenRes cookReopenRes = 9;
  optional CookServeVisitantRes cookServeVisitantRes = 10;
}

// 获得温泉buff
message RpcFarmGetHotSpringBuffNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  repeated int32 buffID = 2;
  optional int32 level = 3;
  optional int32 type = 4;
  optional int32 grade = 5;
  optional int64 buffFromFarmId = 6;
  optional string buffFromName = 7;
}

// 给农场的visitor家里一个必要的通知
message RpcFarmVisitorNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 fromFarmID = 2;
}

// 将新手小红狐农场，转化为正式小红狐农场
message RpcFarmConvertNPCFarmReq {
  optional int64 farmID = 1 [(field_hash_key) = true]; // npc农场id
  optional int64 npcFarmID = 2; // 废弃
  optional int64 ownerID = 3; // 玩家农场id
}

message RpcFarmConvertNPCFarmRes {

}

message RpcFarmNPCFarmCreatedNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 relatedFarmID = 2;
  optional int32 stage = 3;
}

message RpcFarmRefreshNpcFarmNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int32 enter = 2;
  optional proto_FarmAttr ownerAttr = 3;  // 携带主农场属性到小红狐农场
  optional bool cowTaskFinished = 4; // 废弃
  optional int64 cowTaskFinishedTime = 5; // 奶牛任务完成时间
  optional int32 farmLevel = 6; // 农场等级
  optional bool redFoxTaskFinished = 7;
}

message RpcFarmNpcFarmRefreshEndNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 nextCanStealTimeSec = 2;
}

message RpcFarmRefreshNpcFarmReq {
    optional int64 farmID = 1 [(field_hash_key) = true];
    optional int32 enter = 2;
}

message RpcFarmRefreshNpcFarmRes {
}

message RpcFarmPetFeedCostReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 costNum = 2;
}

message RpcFarmReportHotNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int32 type = 2; // 类型 FarmHotType
  optional int64 times = 3; // 次数
}

//// 餐厅贵宾偷取检查（到自己农场检查还有没有空位等等）
//message RpcCookVisitantStealCheckReq {
//  optional int64 farmID = 1 [(field_hash_key) = true];
//}
//
//message RpcCookVisitantStealCheckRes {
//  optional bool canSteal = 1;
//}

// 餐厅贵宾偷取通知（将偷到的贵宾发回自己农场）
message RpcCookVisitantStealNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmID = 1 [(field_hash_key) = true];
  optional proto_CookVisitant visitant = 2;
  optional int64 friendUid = 3; // 偷取的农场好友uid
}
// 获取餐厅评论照片名
message RpcCookGetCommentPhotoNameReq {
  optional int64 cookId = 1 [(field_hash_key) = true];
}

message RpcCookGetCommentPhotoNameRes {
  optional int32 fileNameId = 1; // 可以文件名Id
}

message RpcAddCloudTaxNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional int64 total = 2;
  optional int32 series = 3;
  optional int64 sellerUID = 4;
}

message RpcFarmCreateGameSessionResultEventNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional bytes binData = 2; // CreateGameSessionResultEventRequest
}

message RpcFarmGameSessionEndEventNtfReq {
  option (rpc_one_way) = true;
  optional int64 farmId = 1 [(field_hash_key) = true];
  optional bytes binData = 2; // GameSessionEndEventRequest
}