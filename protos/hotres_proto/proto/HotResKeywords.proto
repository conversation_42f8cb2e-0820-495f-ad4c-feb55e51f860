syntax = "proto2";
option cc_generic_services = false;
option java_multiple_files = true;

package com.tencent.wea.hotRes;

enum HotResWhiteListType {
  HRWLT_UNKNOWN = 0;
  HRWLT_UgcCreateMap = 1;   // ugc大厅地图新建
  HRWLT_UgcEditUnlimit = 2; // 重编辑可信创作者
  HRWLT_UgcAllowCreateGroup = 3;  // 一键拉群
  HRWLT_UgcDanMu = 4;  // Ugc弹幕白名单
  HRWLT_UgcGlobalTime = 5;  //重编辑全局时间
  HRWLT_UgcMapBuysGoods = 6; // 内购白名单
  HRWLT_UgcMapBuysGoodsAudit = 7; // 内购审核白名单
  HRWLT_UgcLogEnable = 8; // 日志开关
  HRWLT_UgcMidJoinViable = 9;  // 是否允许设置中途加入
  HRWLT_UgcCoding = 10;       // coding 白名单
  HRWLT_NewScene = 11;  // 新场景占位
  HRWLT_Ugc2Ogc = 12;       // ogc特殊需求
  HRWLT_FreeFlowTest = 13;       // 免流测试白名单
  HRWLT_UgcAppreciate = 14;      // 地图鉴赏家白名单
  HRWLT_HotResWhiteListCfg = 15;  //星游寻宝白名单
  RWLT_UgcEditMap = 16; // 地图可重编辑白名单
  RWLT_UgcEditLockMap = 17; // 锁定地图可重编辑白名单
  RWLT_UgcNoTssCheck = 18; // 地图不进行敏感词检测白名单
  RWLT_UgcStarWord_Entrance = 19; // 星世界入口图标迭代和气泡功能 白名单
  RWLT_UGC_MAP_IAA = 20;          // UGC地图IAA白名单

  HRWLT_UgcStarWorldNav_Start = 10001;  // 星世界导航栏开始
  HRWLT_UgcStarWorldNav_End = 11001;  // 星世界导航栏结束


  // ugc条件白名单
  HRWLT_UgcConditionWhiteList_20001 = 20001;  // 扣叮学员
  HRWLT_UgcConditionWhiteList_20002 = 20002;  // 叠速之星
  HRWLT_UgcConditionWhiteList_20003 = 20003;  // 好星人
  HRWLT_UgcConditionWhiteList_20004 = 20004;  // 千万星合约入场券
  HRWLT_UgcConditionWhiteList_20005 = 20005;  // 星宝守护者
  HRWLT_UgcConditionWhiteList_20006 = 20006;  // 元梦守艺人
  HRWLT_UgcConditionWhiteList_20007 = 20007;  // 元梦旅游大使
  HRWLT_UgcConditionWhiteList_20008 = 20008;  // 造梦课代表
  HRWLT_UgcConditionWhiteList_20009 = 20009;  // 闪亮之星
  HRWLT_UgcConditionWhiteList_20010 = 20010;  // 2024年度影响力
  HRWLT_UgcConditionWhiteList_20011 = 20011;  // 2024优秀造梦师
  HRWLT_UgcConditionWhiteList_20012 = 20012;  // 校园明星
  HRWLT_UgcConditionWhiteList_20013 = 20013;  // 校园新星
}
