syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_DressUpDetailInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.DressUpDetailInfo";
    option (wea_attr_key) = "itemId";
    // dressUpInfos中的道具ID
    optional int32 itemId = 1;
    // 道具状态
    optional int32 status = 2;
    // 道具个人信息展示状态
    optional int32 showStatus = 3;
    // 痛包吧唧装扮Url
    optional string itaBagBadgeUrl = 4;
}