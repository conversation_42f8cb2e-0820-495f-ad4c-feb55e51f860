syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "ResKeywords.proto";
import "attr_GameplayAddItemBillNo.proto";
import "attr_GameplayItemSet.proto";
import "attr_HistoryItemTypeInfo.proto";
import "attr_HistoryItemUsedInfo.proto";
import "attr_Item.proto";
import "attr_ItemDetailInfo.proto";

message proto_ItemInfoDb {
    option (wea_attr_cls) = "com.tencent.wea.attr.ItemInfoDb";
    // 背包中的物品
    repeated proto_Item item = 1;
    repeated int64 item_deleted = 2001;
    optional bool item_is_cleared = 4001;
    repeated proto_Item observingItem = 2;
    repeated int64 observingItem_deleted = 2002;
    optional bool observingItem_is_cleared = 4002;
    // 历史累计不同类型道具数量
    repeated proto_HistoryItemTypeInfo historyItemType = 3;
    repeated com.tencent.wea.xlsRes.ItemType historyItemType_deleted = 2003;
    optional bool historyItemType_is_cleared = 4003;
    // 历史累计道具ID使用数量
    repeated proto_HistoryItemUsedInfo historyItemUsed = 4;
    repeated int32 historyItemUsed_deleted = 2004;
    optional bool historyItemUsed_is_cleared = 4004;
    // 副玩法未发放道具暂存
    repeated proto_GameplayItemSet gameplayTempItem = 5;
    repeated int32 gameplayTempItem_deleted = 2005;
    optional bool gameplayTempItem_is_cleared = 4005;
    // 副玩法请求添加道具订单号
    repeated proto_GameplayAddItemBillNo gameplayAddItemBillNo = 6;
    repeated string gameplayAddItemBillNo_deleted = 2006;
    optional bool gameplayAddItemBillNo_is_cleared = 4006;
    // 道具展示状态
    repeated proto_ItemDetailInfo itemDetail = 7;
    repeated int64 itemDetail_deleted = 2007;
    optional bool itemDetail_is_cleared = 4007;
}