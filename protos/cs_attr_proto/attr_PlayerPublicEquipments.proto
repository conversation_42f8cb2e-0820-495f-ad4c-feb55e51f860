syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "ResKeywords.proto";
import "attr_AttrDisplayBoardInfo.proto";
import "attr_DressItemInfo.proto";
import "attr_DressUpDetailInfo.proto";
import "attr_EquipDressInfo.proto";
import "attr_EquipItemInfo.proto";
import "attr_QualifyingDailyRankInfo.proto";
import "attr_SeasonFashion.proto";

message proto_PlayerPublicEquipments {
    option (wea_attr_cls) = "com.tencent.wea.attr.PlayerPublicEquipments";
    // 皮肤
    optional int32 dressCount = 1;
    // 装扮信息
    repeated int32 dressUpInfos = 2;
    repeated int32 dressUpInfos_deleted = 2002;
    optional bool dressUpInfos_is_cleared = 4002;
    // 穿戴信息(时装、特效、表情等)-废弃
    repeated proto_EquipDressInfo equipDressInfo = 3;
    repeated com.tencent.wea.xlsRes.ItemType equipDressInfo_deleted = 2003;
    optional bool equipDressInfo_is_cleared = 4003;
    // 装备道具信息-废弃
    repeated proto_EquipItemInfo equipItemInfo = 4;
    repeated com.tencent.wea.xlsRes.ItemType equipItemInfo_deleted = 2004;
    optional bool equipItemInfo_is_cleared = 4004;
    // 当前赛季时尚度
    optional int32 fashionValue = 5;
    // 穿戴信息(铭牌、称号、头像框等)
    repeated proto_DressItemInfo dressItemInfo = 6;
    repeated com.tencent.wea.xlsRes.ItemType dressItemInfo_deleted = 2006;
    optional bool dressItemInfo_is_cleared = 4006;
    // 全部赛季的时尚度
    repeated proto_SeasonFashion fashionValues = 7;
    repeated int32 fashionValues_deleted = 2007;
    optional bool fashionValues_is_cleared = 4007;
    // 套装图鉴
    repeated int32 activeSuitBook = 8;
    repeated int32 activeSuitBook_deleted = 2008;
    optional bool activeSuitBook_is_cleared = 4008;
    // 备用装扮信息 slotId = 5
    repeated int32 backupDressUpInfos = 9;
    repeated int32 backupDressUpInfos_deleted = 2009;
    optional bool backupDressUpInfos_is_cleared = 4009;
    // 初始化赛季时尚手册时间
    optional int64 initSeasonFashionSuitTimeMs = 10;
    // 背景图
    optional int32 profileTheme = 11;
    // 装扮信息dressUpInfos的详细信息
    repeated proto_DressUpDetailInfo dressUpDetailInfos = 12;
    repeated int32 dressUpDetailInfos_deleted = 2012;
    optional bool dressUpDetailInfos_is_cleared = 4012;
    // 对局时尚分展示
    repeated int32 fashionScores = 13;
    optional bool fashionScores_is_cleared = 2013;
    // 组队秀背景图
    optional int32 teamShowTheme = 14;
    // 装扮值
    optional int32 dressUpValue = 15;
    // 每日段位排名
    repeated proto_QualifyingDailyRankInfo qualifyDailyRankInfos = 16;
    repeated int32 qualifyDailyRankInfos_deleted = 2016;
    optional bool qualifyDailyRankInfos_is_cleared = 4016;
    // 备战装饰背包信息
    repeated proto_DressItemInfo readyBattleBagInfo = 17;
    repeated com.tencent.wea.xlsRes.ItemType readyBattleBagInfo_deleted = 2017;
    optional bool readyBattleBagInfo_is_cleared = 4017;
    // 二次元装扮描边 0/1
    optional int32 animeDressOutline = 18;
    // 推图展板
    optional proto_AttrDisplayBoardInfo displayBoardInfo = 19;
}