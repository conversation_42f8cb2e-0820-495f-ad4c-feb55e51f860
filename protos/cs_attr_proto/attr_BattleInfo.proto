syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_BattleMiscInfo.proto";
import "attr_ChatGroupKey.proto";
import "attr_CompetitionBasicInfo.proto";

message proto_BattleInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.BattleInfo";
    // 战场id(dsSessionId)
    optional int64 battleid = 1;
    // ds地址信息
    optional string dsAddr = 2;
    // 阵营id
    optional int32 side = 3;
    // DS认证Token
    optional string dsAuthToken = 4;
    // 玩家uid
    optional int64 uid = 5;
    // 音游 songid:1;diff:1
    optional string desModInfo = 6;
    // 所有人聊天
    optional proto_ChatGroupKey globalChatGroupKey = 7;
    // 阵营聊天
    optional proto_ChatGroupKey sideChatGroupKey = 8;
    // 玩法id
    optional int32 matchType = 9;
    // AI Control数据类型
    optional int32 aiGameType = 10;
    // 赛事信息
    optional proto_CompetitionBasicInfo competitionBasicInfo = 11;
    // dsa_service_id
    optional int64 dsaInstanceID = 12;
    // 杂项信息
    optional proto_BattleMiscInfo miscInfo = 13;
    // 关联对局的roomId
    optional int64 relatedRoomId = 14;
    // 场景ID
    optional int64 sceneId = 15;
    // 独立ds情况下的dsSessionId
    optional int64 dsSessionId = 16;
    // 是否导播
    optional int32 robotType = 17;
    // 对局创建时使用的包类型，为0则未指定-废弃
    optional int32 pakType = 18;
    // 对局创建时使用的分包id列表，为空则为老逻辑
    repeated int32 pakGroupIdList = 19;
    repeated int32 pakGroupIdList_deleted = 2019;
    optional bool pakGroupIdList_is_cleared = 4019;
}