syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";

message proto_TreasureHuntHistoryData {
    option (wea_attr_cls) = "com.tencent.wea.attr.TreasureHuntHistoryData";
    option (wea_attr_key) = "excavateNum";
    // key
    optional int32 excavateNum = 1;
    // 道具id
    optional int32 itemId = 2;
    // 道具数量
    optional int64 itemCnt = 3;
    // 时间戳秒
    optional int64 excavateEpochSecs = 4;
}