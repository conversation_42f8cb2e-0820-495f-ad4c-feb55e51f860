syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_CookVisitant.proto";
import "attr_CookVisitantCurrentGroupInfo.proto";
import "attr_CookVisitantPrebookInfo.proto";
import "attr_CookVisitantStealInfo.proto";

message proto_CookVisitantInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.CookVisitantInfo";
    // 所有等待中的贵宾
    repeated proto_CookVisitant visitants = 1;
    repeated int64 visitants_deleted = 2001;
    optional bool visitants_is_cleared = 4001;
    // 贵宾预约相关
    optional proto_CookVisitantPrebookInfo prebookInfo = 2;
    // 贵宾偷取相关
    optional proto_CookVisitantStealInfo stealInfo = 3;
    // 贵宾可预约列表（根据等级刷新）
    repeated int32 prebookList = 4;
    repeated int32 prebookList_deleted = 2004;
    optional bool prebookList_is_cleared = 4004;
    // 贵宾当前组相关
    optional proto_CookVisitantCurrentGroupInfo currentGroupInfo = 5;
    // 上一次预约的组id
    optional int32 lastPrebookGroupId = 6;
}