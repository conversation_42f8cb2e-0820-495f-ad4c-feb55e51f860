syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_CatFishingInfo.proto";
import "attr_FarmPet.proto";
import "attr_FarmWildCat.proto";
import "attr_PetClothingInfo.proto";
import "attr_PetFavorInfo.proto";
import "attr_PetFertilizeInfo.proto";
import "attr_PetGiftInfo.proto";
import "attr_PetSecurityInfo.proto";
import "attr_WildCatRefreshInfo.proto";

message proto_FarmPetInfo {
    option (wea_attr_cls) = "com.tencent.wea.attr.FarmPetInfo";
    // 所有宠物狗信息
    repeated proto_FarmPet pets = 1;
    repeated int32 pets_deleted = 2001;
    optional bool pets_is_cleared = 4001;
    // 当前在场的宠物狗ID
    optional int32 summonedPetId = 2;
    // 宠物狗看家护院是否关闭
    optional bool houseKeepingDisable = 3;
    // 宠物狗饱腹值停留值
    optional int32 foodValue = 4;
    // 宠物狗模块开启时间(DS同步)
    optional int64 petModuleOpenTimeMs = 5;
    // 宠物狗好感度相关信息
    optional proto_PetFavorInfo favorInfo = 6;
    optional bool favorInfo_deleted = 2006;
    // 宠物狗礼物相关信息
    optional proto_PetGiftInfo giftInfo = 7;
    // 宠物狗祈愿相关信息
    optional proto_PetFertilizeInfo fertilizeInfo = 8;
    optional bool fertilizeInfo_deleted = 2008;
    // 服装相关信息
    optional proto_PetClothingInfo clothingInfo = 9;
    // 安全相关信息
    optional proto_PetSecurityInfo securityInfo = 10;
    // 所有宠物猫信息
    repeated proto_FarmPet cats = 101;
    repeated int32 cats_deleted = 2101;
    optional bool cats_is_cleared = 4101;
    // 当前在场的宠物猫ID
    optional int32 summonedCatId = 102;
    // 宠物猫饱腹值停留值
    optional int32 catFoodValue = 103;
    // 宠物猫好感度相关信息
    optional proto_PetFavorInfo catFavorInfo = 104;
    optional bool catFavorInfo_deleted = 2104;
    // 宠物猫礼物相关信息
    optional proto_PetGiftInfo catGiftInfo = 105;
    // 所有野生猫信息
    repeated proto_FarmWildCat wildCats = 106;
    repeated int32 wildCats_deleted = 2106;
    optional bool wildCats_is_cleared = 4106;
    // 当前在场的野生猫ID
    optional int32 wildCatId = 107;
    // 野生猫好感度相关信息
    optional proto_PetFavorInfo wildCatFavorInfo = 108;
    // 野生猫刷新相关信息
    optional proto_WildCatRefreshInfo wildCatRefreshInfo = 109;
    // 宠物猫钓鱼相关信息
    optional proto_CatFishingInfo catFishingInfo = 110;
}