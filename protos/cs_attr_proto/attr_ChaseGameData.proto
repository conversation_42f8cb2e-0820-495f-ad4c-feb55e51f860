syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_ChaseDressItemInfo.proto";
import "attr_ChaseIdentityProficiencyInfo.proto";
import "attr_ChaseIdentitySpecialization.proto";

message proto_ChaseGameData {
    option (wea_attr_cls) = "com.tencent.wea.attr.ChaseGameData";
    // 星宝装扮
    repeated proto_ChaseDressItemInfo normalDressItem = 1;
    repeated int32 normalDressItem_deleted = 2001;
    optional bool normalDressItem_is_cleared = 4001;
    // 暗星装扮
    repeated proto_ChaseDressItemInfo bossDressItem = 2;
    repeated int32 bossDressItem_deleted = 2002;
    optional bool bossDressItem_is_cleared = 4002;
    // 大王身份专精
    optional proto_ChaseIdentitySpecialization identitySpecialization = 3;
    // 大王身份数量度
    optional proto_ChaseIdentityProficiencyInfo chaseIdentityProficiencyInfo = 4;
}