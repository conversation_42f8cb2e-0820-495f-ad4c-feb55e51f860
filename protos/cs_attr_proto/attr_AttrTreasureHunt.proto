syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.protocol;

import "attr_base.proto";
import "attr_TreasureHuntHistoryData.proto";

message proto_AttrTreasureHunt {
    option (wea_attr_cls) = "com.tencent.wea.attr.AttrTreasureHunt";
    // 挖宝历史(最大30条)
    repeated proto_TreasureHuntHistoryData treasureHuntHistory = 1;
    repeated int32 treasureHuntHistory_deleted = 2001;
    optional bool treasureHuntHistory_is_cleared = 4001;
    // 已抽次数
    optional int32 lotteryDrawNum = 2;
    // 挖掘次数
    optional int32 excavateNum = 3;
}