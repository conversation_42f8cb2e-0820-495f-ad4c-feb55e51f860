-- This file is automatically generated

local TableMap = {
	SPBuildTypeConfigTable = {
		SPBuildTypeConfigDataSubTables = {
			"BuildTypeConfigData",
		},
	},
	MiscStarpTable = {
		GMCmdConfSubTables = {
			"GMCmdConfForLetsGoStarp",
		},
		ServerCmdConfSubTables = {
			"ServerCmdConfStarp",
		},
	},
	GeneralStarPTable = {
		GamePlayUpdateConfSubTables = {
			"GamePlayUpdateConf_StarP",
		},
		VersionCompBattleConfSubTables = {
			"VersionCompBattleConfData_starp",
		},
		VersionCompFeatureConfSubTables = {
			"VersionCompFeatureConf_StarP",
		},
	},
	LevelInfoStarpTable = {
		LevelInfoDataSubTables = {
			"LevelInfoData_SPGame",
		},
	},
	LetsGoDeviceCommandInfoStarpTable = {
		DeviceProfileCommandDataSubTables = {
			"PWDeviceFPSCommandAndroidStarp",
			"PWDeviceFPSCommandIOSStarp",
			"PWDeviceFPSCommandWindowsStarp",
			"PWDeviceProfileCommandAndroidStarp",
			"PWDeviceProfileCommandIOSStarp",
			"PWDeviceProfileCommandWindowsStarp",
			"PWDeviceScreenPercentageCommandAndroidStarp",
			"PWDeviceScreenPercentageCommandIOSStarp",
			"PWDeviceScreenPercentageCommandWindowsStarp",
		},
		SPDeviceCorrectCommandDataSubTables = {
			"SPDeviceCorrectCommandIOS",
		},
	},
	LetsGoDeviceProfileInfoStarpTable = {
		DeviceProfileInfoDataSubTables = {
			"PWDeviceFPSInfoAndroidStarp",
			"PWDeviceFPSInfoIOSStarp",
			"PWDeviceFPSInfoWindows",
			"PWDeviceProfileInfoAndroidStarp",
			"PWDeviceProfileInfoIOSStarp",
			"PWDeviceProfileInfoWindowsStarp",
			"PWDeviceScreenPercentageInfoAndroidStarp",
			"PWDeviceScreenPercentageInfoIOSStarp",
			"PWDeviceScreenPercentageInfoWindows",
		},
	},
	ProtectedScoreStarpTable = {
		ProtectedScoreAdditionalDataSubTables = {
			"ProtectedScoreAdditionalData_starp_pvp",
		},
		ProtectedScoreDataSubTables = {
			"ProtectedScoreData_starp_pvp",
		},
		ProtectedScoreWinDataSubTables = {
			"ProtectedScoreWinData_starp_pvp",
		},
		ProtectedScoreWinStreakDataSubTables = {
			"ProtectedScoreWinStreakData_starp_pvp",
		},
	},
	QualifyingStarpTable = {
		DegreeTypeDataSubTables = {
			"QDTDegreeTypeData_starp_pvp",
		},
		EspecialIntegralDataSubTables = {
			"QDTEspecialIntegralData_starp_pvp",
		},
		QualifyingInheritDataSubTables = {
			"QDTQualifyingInheritData_starp_pvp",
		},
		SeasonCfgDataSubTables = {
			"QDTSeasonCfgData_starp_pvp",
		},
		QualifyingLevelDimensionConditionDataSubTables = {
			"QualifyingLevelDimensionConditionData_starp_pvp",
		},
		QualifyingLevelDimensionScoreDataSubTables = {
			"QualifyingLevelDimensionScoreData_starp_pvp",
		},
		RankKingDegreeDataSubTables = {
			"RankKingDegreeData_starp_pvp",
		},
	},
	ReportStarpTable = {
		ReportContentConfSubTables = {
			"ReportContentDataStarp",
		},
		ReportEntryConfSubTables = {
			"ReportEntryConfDataStarp",
		},
	},
	SPCaptureDataTable = {
		ResCaptureGuaranteedSubTables = {
			"ResCaptureGuaranteed",
		},
		ResCaptureLevelSuppressSubTables = {
			"ResCaptureLevelSuppress",
		},
		ResCaptureProbabilityGrowthSubTables = {
			"ResCaptureProbabilityGrowth",
		},
		ResCaptureProbabilityScalingSubTables = {
			"ResCaptureProbabilityScaling",
		},
		ResCharacterCapturePowerSubTables = {
			"ResCharacterCapturePower",
		},
		ResNewCaptureLevelSuppressSubTables = {
			"ResNewCaptureLevelSuppress",
		},
		ResSPCaptureBallConfigSubTables = {
			"ResSPCaptureBallConfig",
		},
	},
	SPDamageLevelSuppressTable = {
		ResDamageLevelSuppressSubTables = {
			"ResDamageLevelSuppress",
		},
	},
	SPElementDamageEffectTable = {
		ResElementDamageEffectPVESubTables = {
			"ResElementDamageEffectPVE",
		},
		ResElementDamageEffectPVPSubTables = {
			"ResElementDamageEffectPVP",
		},
		ResElementTenacityEffectPVESubTables = {
			"ResElementTenacityEffectPVE",
		},
		ResElementTenacityEffectPVPSubTables = {
			"ResElementTenacityEffectPVP",
		},
	},
	SPBusinessFuncSwitchTable = {
		SPBusinessFuncSwitchSubTables = {
			"ResSPBusinessFuncSwitch",
		},
	},
	SPConstantDataTable = {
		SPConstantDataSubTables = {
			"ResSPConstantData",
		},
		SPSvrConstantDataSubTables = {
			"SPSvrConstantData",
		},
	},
	SPPvpTable = {
		SPPvpBasicConfSubTables = {
			"ResSPPvpBasicConf",
		},
	},
	SPResourceBalanceRuleTable = {
		SPResourceBalanceRuleSubTables = {
			"ResSPResourceBalanceRule",
		},
	},
	SPTaskActiveRewardTable = {
		ResSPTaskActiveRewardSubTables = {
			"ResSPTaskActiveRewardConf",
		},
	},
	SPTaskConditionSkipTable = {
		ResSPTaskConditionSkipSubTables = {
			"ResSPTaskConditionSkipConf",
		},
	},
	SPTaskGroupTable = {
		ResSPTaskGroupSubTables = {
			"ResSPTaskGroupConf",
		},
	},
	SPTaskGroupTypeTable = {
		ResSPTaskGroupTypeSubTables = {
			"ResSPTaskGroupTypeConf",
		},
	},
	SPTimeRefreshRuleTable = {
		ResSPTimeRefreshRuleSubTables = {
			"ResSPTimeRefreshRule",
		},
	},
	SPTlogCheckerStarpTable = {
		SPTlogCheckerSubTables = {
			"ResSPTlogChecker",
		},
	},
	SPAbilityActionTable = {
		SPAbilityActionDataSubTables = {
			"SPAbilityActionData",
		},
	},
	SPAbilityActionParamStarpTable = {
		SPAbilityActionParamSubTables = {
			"SPAbilityActionParam",
		},
	},
	SPAbilityConditionTable = {
		SPAbilityConditionDataSubTables = {
			"SPAbilityConditionData",
		},
	},
	SPAbilityConsumeTable = {
		SPAbilityConsumeDataSubTables = {
			"SPAbilityConsumeData",
		},
	},
	SPAbilityDataTable = {
		SPAbilitySubTables = {
			"SPAbilityData",
		},
		SPSkillPositionSubTables = {
			"SPSkillPositionData",
		},
	},
	SPAbilityEventTable = {
		SPAbilityEventDataSubTables = {
			"SPAbilityEventData",
		},
	},
	SPAchievementTable = {
		SPAchievementConfSubTables = {
			"SPAchievementConf",
		},
		SPAdventureLevelSubTables = {
			"SPAdventureLevel",
		},
		SPAdventureShopSubTables = {
			"SPAdventureShop",
		},
		SPAdventureShopSubPageSubTables = {
			"SPAdventureShopSubPage",
		},
		SPRewardPointsSubTables = {
			"SPRewardPoints",
		},
		SPTrophyInfoSubTables = {
			"SPTrophyInfo",
		},
	},
	SPAdventureGroupTable = {
		SPAdventureGroupSpeedUpSubTables = {
			"SPAdventureGroup",
		},
	},
	SPAffixDataTable = {
		SPAffixDataSubTables = {
			"SPAffixData",
		},
	},
	SPPetTable = {
		SPAfterLeavingCombatResetAttributeListSubTables = {
			"SPAfterLeavingCombatResetAttributeList",
		},
		SPCaptureExpSubTables = {
			"SPCaptureExpCfg",
		},
		SPCaptureExpGroupSubTables = {
			"SPCaptureExpGroup",
		},
		SPFlyPetWorkSlotSubTables = {
			"SPFlyPetWorkSlot",
		},
		SPMoveAreaConfigSubTables = {
			"SPMoveAreaConfig",
		},
		SPPetSubTables = {
			"SPPet",
		},
		SPPetAISubTables = {
			"SPPetAI",
		},
		SPPetActiveSkillPoolSubTables = {
			"SPPetActiveSkillPool",
		},
		SPPetAffinitySubTables = {
			"SPPetAffinity",
		},
		SPPetAffinityTemplateSubTables = {
			"SPPetAffinityTemplate",
		},
		SPPetBTSubTables = {
			"SPPetBT",
		},
		SPPetBodyTypeSubTables = {
			"SPPetBodyType",
		},
		SPPetElementDetailSubTables = {
			"SPPetElementDetail",
		},
		SPPetFormSubTables = {
			"SPPetForm",
		},
		SPPetHungerSubTables = {
			"SPPetHunger",
		},
		SPPetInteractiveBehaviorSubTables = {
			"SPPetInteractiveBehavior",
		},
		SPPetPassiveSkillDetailSubTables = {
			"SPPetPassiveSkillDetail",
		},
		SPPetPassiveSkillMutexSubTables = {
			"SPPetPassiveSkillMutex",
		},
		SPPetPassiveSkillPoolSubTables = {
			"SPPetPassiveSkillPool",
		},
		SPPetPotentialAttrSubTables = {
			"SPPetPotentialAttr",
		},
		SPPetPotentialPoolSubTables = {
			"SPPetPotentialPool",
		},
		SPPetRarityEffectSubTables = {
			"SPPetRarityEffect",
		},
		SPPetRarityPoolSubTables = {
			"SPPetRarityPool",
		},
		SPPetSOCBehaviorSubTables = {
			"SPPetSOCBehavior",
		},
		SPPetSOCConifgSubTables = {
			"SPPetSOCConifg",
		},
		SPPetStatusEffectSubTables = {
			"SPPetStatusEffect",
		},
		SPPetStatusEffectConditionSubTables = {
			"SPPetStatusEffectCondition",
		},
		SPPetTenacitySubTables = {
			"SPPetTenacity",
		},
		SPPetTypeSubTables = {
			"SPPetType",
		},
		SPPetWorkSkillDetailSubTables = {
			"SPPetWorkSkillDetail",
		},
		SPPetWorldJobSubTables = {
			"SPPetWorldJob",
		},
		SPPetWorldJobTypeSubTables = {
			"SPPetWorldJobType",
		},
		SPSkillGroupSubTables = {
			"SPSkillGroup",
		},
		SPTransportDataSubTables = {
			"SPTransportData",
		},
	},
	SPAirWallTable = {
		SPAirWallSubTables = {
			"SPAirWall",
		},
	},
	SPChatTable = {
		SPAssistQuickTextConfDataSubTables = {
			"SPAssistQuickTextConfData",
		},
		SPQuickTextConfDataSubTables = {
			"SPQuickTextConfDataLobby",
		},
	},
	SPAvatarConfigTable = {
		SPAvatarPartInGameConfigSubTables = {
			"SPAvatarPartInGameConfig",
		},
		SPAvatarPartPreviewConfigSubTables = {
			"SPAvatarPartPreviewConfig",
		},
		SPAvatarSuitInGameConfigSubTables = {
			"SPAvatarSuitInGameConfig",
		},
		SPAvatarSuitPreviewConfigSubTables = {
			"SPAvatarSuitPreviewConfig",
		},
	},
	SPInteractUITable = {
		ResSPBInteractUISubTables = {
			"SPBInteractUI",
		},
	},
	SPAttrBalanceTable = {
		SPBalanceDatabaseSubTables = {
			"SPBalanceDatabase",
		},
		SPBalanceIndexDataSubTables = {
			"SPBalanceIndexData",
		},
		SPPassiveBuffSwitchesSubTables = {
			"SPPassiveBuffSwitches",
		},
	},
	SPBattleTalentTable = {
		SPBattleTalentSubTables = {
			"SPBattleTalent",
		},
		SPBattleTalentElementEffectSubTables = {
			"SPBattleTalentElementEffect",
		},
		SPBattleTalentElementWeaponSubTables = {
			"SPBattleTalentElementWeapon",
		},
		SPBattleTalentEnchantSubTables = {
			"SPBattleTalentEnchant",
		},
		SPBattleTalentGroupSubTables = {
			"SPBattleTalentGroup",
		},
		SPBattleTalentPositionSubTables = {
			"SPBattleTalentPosition",
		},
		SPBattleTalentWeaponInlaySubTables = {
			"SPBattleTalentWeaponInlay",
		},
	},
	SPBreedTable = {
		SPBreedAbilityInheritSubTables = {
			"SPBreedAbilityInherit",
		},
		SPBreedAbilityMutationSubTables = {
			"SPBreedAbilityMutation",
		},
		SPBreedAbilityNumSubTables = {
			"SPBreedAbilityNum",
		},
		SPBreedBuildItemConfigSubTables = {
			"SPBreedBuildItemConfig",
		},
		SPBreedCombinationSubTables = {
			"SPBreedCombination",
		},
	},
	SPBroadcastStarpTable = {
		SPBroadcastDataSubTables = {
			"SPBroadcastData",
		},
	},
	SPBroadcastTemplateStarpTable = {
		SPBroadcastTemplateDataSubTables = {
			"SPBroadcastTemplateData",
		},
	},
	SPBuffDataTable = {
		SPBuffDataSubTables = {
			"SPBuffData",
		},
		SPBuffData_ABTestSubTables = {
			"SPBuffData_ABTest",
		},
	},
	SPBuffFlowExtraInfoTable = {
		SPBuffFlowExtraInfoSubTables = {
			"SPBuffFlowExtraInfo",
		},
	},
	SPBuildFunctionTable = {
		SPBuildBaseViewSubTables = {
			"SPBuildBaseView",
		},
		SPBuildBattleSubTables = {
			"SPBuildBattle",
		},
		SPBuildBattleBulletSubTables = {
			"SPBuildBattleBullet",
		},
		SPBuildElecGeneratorSubTables = {
			"SPBuildElecGenerator",
		},
		SPBuildFarmSubTables = {
			"SPBuildFarm",
		},
		SPBuildFarmCropSubTables = {
			"SPBuildFarmCrop",
		},
		SPBuildFarmCropStateSubTables = {
			"SPBuildFarmCropState",
		},
		SPBuildFarmVegetealSubTables = {
			"SPBuildFarmVegeteal",
		},
		SPBuildFormulaSubTables = {
			"SPBuildFormula",
		},
		SPBuildGuildCheckSubTables = {
			"SPBuildGuildCheck",
		},
		SPBuildLightSubTables = {
			"SPBuildLight",
		},
		SPBuildMakeSubTables = {
			"SPBuildMake",
		},
		SPBuildMakeEquipSubTables = {
			"SPBuildMakeEquip",
		},
		SPBuildMakeFoodSubTables = {
			"SPBuildMakeFood",
		},
		SPBuildMakeFoodProcessSubTables = {
			"SPBuildMakeFoodProcess",
		},
		SPBuildPastureSubTables = {
			"SPBuildPasture",
		},
		SPBuildPastureDropSubTables = {
			"SPBuildPastureDrop",
		},
		SPBuildPetRelaxSubTables = {
			"SPBuildPetRelax",
		},
		SPBuildPetSleepSubTables = {
			"SPBuildPetSleep",
		},
		SPBuildPlanterSubTables = {
			"SPBuildPlanter",
		},
		SPBuildPlayerSleepSubTables = {
			"SPBuildPlayerSleep",
		},
		SPBuildProductSubTables = {
			"SPBuildProduct",
		},
		SPBuildStorageSubTables = {
			"SPBuildStorage",
		},
		SPBuildTechSubTables = {
			"SPBuildTech",
		},
		SPBuildTempControlSubTables = {
			"SPBuildTempControl",
		},
		SPBuildTempControlEffectSubTables = {
			"SPBuildTempControlEffect",
		},
	},
	SPBuildConfigTable = {
		SPBuildConfigDataSubTables = {
			"SPBuildConfigData",
		},
		SPBuildTerminalCondSubTables = {
			"SPBuildTerminalCond",
		},
		SPBuildTerminalLevelSubTables = {
			"SPBuildTerminalLevel",
		},
		SPTerminalWorkStatusSubTables = {
			"SPTerminalWorkStatus",
		},
	},
	SPLogSettingConfigTable = {
		SPCLogSettingConfSubTables = {
			"SPCLogSettingConf",
		},
		SPLogSettingConfSubTables = {
			"SPLogSettingConf",
		},
	},
	SPUITipTable = {
		SPCaptureTipSubTables = {
			"SPCaptureTip",
		},
		SPCommonUITipSubTables = {
			"SPCommonUITip",
		},
		SPMainUITipSubTables = {
			"SPMainUITip",
		},
	},
	SPCardTable = {
		SPCardSubTables = {
			"SPCard",
		},
	},
	SPCareerIdolStoryInfoDataTable = {
		SPCareerIdolStoryInfoDataSubTables = {
			"SPCareerIdolStoryInfoData",
		},
	},
	SPCharacterWorkTable = {
		SPCharacterWorkSubTables = {
			"SPCharacterWork",
		},
	},
	SPChoppableConfigDataStarpTable = {
		SPChoppableConfigDataSubTables = {
			"SPChoppableConfigData",
		},
	},
	SPClimbSpeedStarpTable = {
		SPClimbSpeedSubTables = {
			"SPClimbSpeed",
		},
	},
	SPCombatAttributeStarpTable = {
		SPCombatAttributeSubTables = {
			"SPCombatAttribute",
		},
		SPSecondaryAttributeConvertSubTables = {
			"SPSecondaryAttributeConvert",
		},
	},
	SPCombatEffectCalcTable = {
		SPCombatEffectCalcSubTables = {
			"SPCombatEffectCalcConfig",
		},
	},
	SPConditionCombinationTable = {
		SPConditionCombinationConfSubTables = {
			"SPConditionCombinationConf",
		},
	},
	SPConfigUpdateLevelTable = {
		SPConfigUpdateLevelDataSubTables = {
			"SPConfigUpdateLevelData",
		},
	},
	SPCreateTeamConfTable = {
		SPCreateTeamConfSubTables = {
			"SPCreateTeamConf",
		},
	},
	SPDamageConfigTable = {
		SPDamageAttenuationConfigSubTables = {
			"SPDamageAttenuationConfig",
		},
		SPDamageConfigSubTables = {
			"SPDamageConfig",
		},
		SPDamageConfig_ABTestSubTables = {
			"SPDamageConfig_ABTest",
		},
		SPDamageTypeConfigSubTables = {
			"SPDamageTypeConfig",
		},
	},
	SPDamageDistanceAttenuationTable = {
		SPDamageDistanceAttenuationSubTables = {
			"SPDamageDistanceAttenuation",
		},
	},
	SPDeviceCommandInfoTable = {
		SPDeviceCorrectCommandDataSubTables = {
			"SPDeviceCorrectCommandAndroid",
			"SPDeviceCorrectCommandWindows",
		},
	},
	SPDressUpItemTable = {
		SPDressUpItemSubTables = {
			"SPDressUpItem",
		},
	},
	SPDropBagTable = {
		SPDropBagSubTables = {
			"SPDropBag",
		},
	},
	SPDungeonDataStarpTable = {
		SPDungeonDataSubTables = {
			"SPDungeonData",
		},
	},
	SPDungeonLinkPointTable = {
		SPDungeonLinkPointSubTables = {
			"SPDungeonLinkPoint",
		},
	},
	SPDungeonStepTable = {
		SPDungeonStepDataSubTables = {
			"SPDungeonStepData",
		},
	},
	SPValueChangeTable = {
		SPElectricLevelSubTables = {
			"SPElectricLevel",
		},
	},
	SPEncounterDataTable = {
		SPEncounterDataSubTables = {
			"SPEncounterData",
		},
		SPOWMissionDataSubTables = {
			"SPOWMissionData",
		},
	},
	SPEquipmentSetConfigTable = {
		SPEquipmentAttributeConfigSubTables = {
			"SPEquipmentAttributeConfig",
		},
		SPEquipmentFenceSubTables = {
			"SPEquipmentFence",
		},
		SPEquipmentFormulaIdConfigSubTables = {
			"SPEquipmentFormulaIdConfig",
		},
		SPEquipmentItemConfigSubTables = {
			"SPEquipmentItemConfig",
		},
		SPEquipmentPartSubTables = {
			"SPEquipmentPart",
		},
		SPEquipmentSetConfigSubTables = {
			"SPEquipmentSetConfig",
		},
		SPEquipmentSetEffectConfigSubTables = {
			"SPEquipmentSetEffectConfig",
		},
		SPEquipmentSlotConfigSubTables = {
			"SPEquipmentSlotConfig",
		},
		SPEquipmentSortConfigSubTables = {
			"SPEquipmentSortConfig",
		},
	},
	SPEquipmentSetGradeDefineTable = {
		SPEquipmentSetGradeDefineSubTables = {
			"SPEquipmentSetGradeDefine",
		},
	},
	SPEquipmentSetUnlockDefineTable = {
		SPEquipmentSetUnlockDefineSubTables = {
			"SPEquipmentSetUnlockDefine",
		},
	},
	SPExperienceLimitTable = {
		SPExperienceLimitConfigSubTables = {
			"SPExperienceLimitConfig",
		},
	},
	SPExperienceLimitDefineTable = {
		SPExperienceLimitDefineSubTables = {
			"SPExperienceLimitDefine",
		},
	},
	SPExperienceWhiteListTable = {
		SPExperienceWhiteListConfigSubTables = {
			"SPExperienceWhiteListConfig",
		},
	},
	SPFactionRelationTable = {
		SPFactionRelationSubTables = {
			"SPFactionRelation",
		},
	},
	SPFocusActorTypeConfigDataTable = {
		SPFocusActorTypeConfigDataSubTables = {
			"SPFocusActorTypeConfigData",
		},
	},
	SPFriendIntimacyTable = {
		SPFriendIntimacyActionTypeSubTables = {
			"SPFriendIntimacyActionType",
		},
		SPFriendIntimacyDeductionSubTables = {
			"SPFriendIntimacyDeduction",
		},
		SPFriendIntimacyLevelSubTables = {
			"SPFriendIntimacyLevel",
		},
	},
	SPFriendSkillDataTable = {
		SPFriendSkillDataSubTables = {
			"SPFriendSkillData",
		},
	},
	SPFriendSkillEnergyTable = {
		SPFriendSkillEnergySubTables = {
			"SPFriendSkillEnergy",
		},
		SPFriendSkillEnergyCostSubTables = {
			"SPFriendSkillEnergyCost",
		},
	},
	SPFriendSkillMappingTable = {
		SPFriendSkillMappingSubTables = {
			"SPFriendSkillMapping",
		},
	},
	SPFunctionControlTable = {
		SPFunctionControlSubTables = {
			"SPFunctionControl",
		},
	},
	SPGPOCageTable = {
		SPGPOCageSubTables = {
			"SPGPOCage",
		},
	},
	SPGPOInteractionTargetTable = {
		SPGPOInteractionTargetSubTables = {
			"SPGPOInteractionTarget",
		},
	},
	SPGPOItemsTable = {
		SPGPOItemsSubTables = {
			"SPGPOItems",
		},
	},
	SPGPONPCTypeObjectsTable = {
		SPGPONPCTypeObjectsSubTables = {
			"SPGPONPCTypeObjects",
		},
	},
	SPGPOTreasureChestTable = {
		SPGPOTreasureChestSubTables = {
			"SPGPOTreasureChest",
		},
	},
	SPGameArmorTable = {
		SPGameArmorSubTables = {
			"SPGameArmor",
		},
		SPGameArmorAttributeSubTables = {
			"SPGameArmorAttribute",
		},
		SPGameArmorSetEffectSubTables = {
			"SPGameArmorSetEffect",
		},
		SPGameArmorSortSubTables = {
			"SPGameArmorSort",
		},
	},
	SPGameItemTable = {
		SPGameBackpackSubTables = {
			"SPGameBackpack",
		},
		SPGameClothingItemSubTables = {
			"SPGameClothingItem",
		},
		SPGameDropBagTreasureSubTables = {
			"SPGameDropBagTreasure",
		},
		SPGameFoodSubTables = {
			"SPGameFood",
		},
		SPGameGlobalSubTables = {
			"SPGameGlobal",
		},
		SPGameImportantItemSubTables = {
			"SPGameImportantItem",
		},
		SPGameItemSummarySubTables = {
			"SPGameItemSummary",
		},
		SPGameItemTypeSubTables = {
			"SPGameItemType",
		},
		SPGameMedicineSubTables = {
			"SPGameMedicine",
		},
		SPGamePetSubTables = {
			"SPGamePet",
		},
		SPGameSkillFruitSubTables = {
			"SPGameSkillFruit",
		},
		SPGameWeaponSubTables = {
			"SPGameWeapon",
		},
		SPGameXingShouBallSubTables = {
			"SPGameXingShouBall",
		},
		SPGameXingShouItemSubTables = {
			"SPGameXingShouItem",
		},
	},
	SPGameOrnamentStarpTable = {
		SPGameBackpackFilterUILayoutSubTables = {
			"SPGameBackpackFilterUILayout",
		},
		SPGameEquipFenceSubTables = {
			"SPGameEquipFence",
		},
		SPGameEquipSlotSubTables = {
			"SPGameEquipSlot",
		},
		SPGameOrnamentSubTables = {
			"SPGameOrnament",
		},
		SPGameOrnamentAttributeSubTables = {
			"SPGameOrnamentAttribute",
		},
		SPGameOrnamentDecomposeSubTables = {
			"SPGameOrnamentDecompose",
		},
		SPGameOrnamentEffectSubTables = {
			"SPGameOrnamentEffect",
		},
		SPGameOrnamentPoolSubTables = {
			"SPGameOrnamentPool",
		},
		SPGameOrnamentPoolDetailSubTables = {
			"SPGameOrnamentPoolDetail",
		},
		SPGameOrnamentSetSubTables = {
			"SPGameOrnamentSet",
		},
		SPGameOrnamentSetEffectSubTables = {
			"SPGameOrnamentSetEffect",
		},
		SPGameOrnamentSortSubTables = {
			"SPGameOrnamentSort",
		},
		SPGameOrnamentTypeSubTables = {
			"SPGameOrnamentType",
		},
	},
	SPGameGachaTable = {
		SPGameGachaBuffSubTables = {
			"SPGameGachaBuff",
		},
		SPGameGachaItemSubTables = {
			"SPGameGachaItem",
		},
		SPGameGachaOnetimeRewardSubTables = {
			"SPGameGachaOnetimeReward",
		},
		SPGameGachaRewardSubTables = {
			"SPGameGachaReward",
		},
		SPGameGachaSortSubTables = {
			"SPGameGachaSort",
		},
	},
	SPGuideConfigTable = {
		SPGameGuideDebugInfoSubTables = {
			"SPGameGuideDebugInfo",
		},
		SPGameHelperSubTables = {
			"SPGameHelper",
		},
		SPGuideBubbleConfigSubTables = {
			"SPGuideBubbleConfig",
		},
		SPGuideConfigSubTables = {
			"SPGuideConfig",
		},
		SPGuideWindowConfigSubTables = {
			"SPGuideWindowConfig",
		},
		SPItemGetPopupConfigSubTables = {
			"SPItemGetPopupConfig",
		},
	},
	SPGameMapTable = {
		SPGameMapIconSubTables = {
			"SPGameMapIcon",
		},
		SPGameMapIconTypeSummarySubTables = {
			"SPGameMapIconTypeSummary",
		},
		SPGameStaticIconSubTables = {
			"SPGameStaticIcon",
		},
		SPGameStaticVisibleSubTables = {
			"SPGameStaticVisible",
		},
	},
	SPGiftPackageTable = {
		SPGiftPackageDataSubTables = {
			"SPGiftPackageConf",
		},
	},
	SPGlidingDataTable = {
		SPGlidingDataSubTables = {
			"SPGlidingData",
		},
	},
	SPGodStatueStoryInfoDataTable = {
		SPGodStatueStoryInfoDataSubTables = {
			"SPGodStatueStoryInfoData",
		},
	},
	SPGroupTable = {
		SPGroupActivityOutputSubTables = {
			"SPGroupActivityOutput",
		},
		SPGroupGachaConfigSubTables = {
			"SPGroupGachaConfig",
		},
		SPGroupIconSubTables = {
			"SPGroupIcon",
		},
		SPGroupLevelSubTables = {
			"SPGroupLevel",
		},
		SPGroupAttrSubTables = {
			"SPGroupAttr",
		},
	},
	SPGrowthPathConfigTable = {
		SPGrowthPathChapterSubTables = {
			"SPGrowthPathChapterConf",
		},
		SPGrowthPathChildMissionSubTables = {
			"SPGrowthPathChildMissionConf",
		},
		SPGrowthPathGeneralSubTables = {
			"SPGrowthPathGeneralConf",
		},
		SPGrowthPathMissionSubTables = {
			"SPGrowthPathMissionConf",
		},
	},
	SPGuildConfigTable = {
		SPGuildEfficiencyConfigSubTables = {
			"SPGuildEfficiencyConfig",
		},
		SPGuildHeadIconConfigSubTables = {
			"SPGuildHeadIconConfig",
		},
		SPGuildIconSubTables = {
			"SPGuildIcon",
		},
		SPGuildJoinCdConfigSubTables = {
			"SPGuildJoinCdConfig",
		},
		SPGuildMapIconConfigSubTables = {
			"SPGuildMapIconConfig",
		},
		SPGuildMaterialConfigSubTables = {
			"SPGuildMaterialConfig",
		},
		SPGuildNewsConfigSubTables = {
			"SPGuildNewsConfig",
		},
		SPGuildParamConfigSubTables = {
			"SPGuildParamConfig",
		},
		SPGuildSystemMessageConfigSubTables = {
			"SPGuildSystemMessageConfig",
		},
		SPGuildTitleConfigSubTables = {
			"SPGuildTitleConfig",
		},
	},
	SPHallGuideConfStarpTable = {
		SPHallGuideConfSubTables = {
			"SPHallGuideConf",
		},
	},
	SPHitConfigTable = {
		SPHitConfigSubTables = {
			"SPHitConfig",
		},
	},
	SPMapQualityTable = {
		SPImpactFoliageSettingSubTables = {
			"SPImpactFoliageSetting",
		},
		SPMapQualitySubTables = {
			"SPMapQuality",
		},
	},
	SPInteract3DUITable = {
		ResSPInteract3DUISubTables = {
			"SPInteract3DUI",
		},
		ResSPInteractProgressUISubTables = {
			"SPInteractProgressUI",
		},
	},
	SPInvasionLevelConfigTable = {
		SPInvasionLevelConfigDataSubTables = {
			"SPInvasionLevelConfigData",
		},
	},
	SPItemAcquisitionPathTable = {
		SPItemAcquisitionPathConfSubTables = {
			"SPItemAcquisitionPathConf",
		},
	},
	SPMonsterRangePositionTable = {
		SPItemMonsterRangePositionConfSubTables = {
			"SPItemMonsterRangePositionConf",
		},
	},
	SPLearningDiagramLimitDataTable = {
		SPLearningDiagramLimitDataSubTables = {
			"SPLearningDiagramLimitData",
		},
	},
	SPLevelTable = {
		SPLevelSubTables = {
			"SPLevel",
		},
	},
	SPLevelSequenceConfigTable = {
		SPLevelSequenceConfigDataSubTables = {
			"SPLevelSequenceConfigData",
		},
	},
	SPLevelUpAddTechPointTable = {
		SPLevelUpAddTechPointSubTables = {
			"SPLevelUpAddTechPoint",
		},
	},
	SPLocationTraceTable = {
		SPLocationTraceSubTables = {
			"SPLocationTrace",
		},
	},
	SPLotteryTable = {
		SPLotteryPoolSubTables = {
			"SPLotteryPool",
		},
		SPLotteryRewardSubTables = {
			"SPLotteryReward",
		},
	},
	SPMailConfigTable = {
		SPMailConfigSubTables = {
			"SPMailConfig",
		},
		SPTimedMailConfigSubTables = {
			"SPTimedMailConfig",
		},
	},
	SPMailTabConfigTable = {
		SPMailTabConfigSubTables = {
			"SPMailTabConfig",
		},
	},
	SPMainProcessTlogConfigTable = {
		SPMainProcessTlogGroupConfigSubTables = {
			"SPMainProcessTlog",
		},
	},
	SPMainProcessTlogDefineTable = {
		SPMainProcessTlogDefineSubTables = {
			"SPMainProcessTlogDefine",
		},
	},
	SPMainUITipsTable = {
		SPMainUISequentialGroupSubTables = {
			"SPMainUISequentialGroup",
		},
		SPMainUITipsSubTables = {
			"SPMainUITips",
		},
	},
	SPMapTable = {
		SPMapSubTables = {
			"SPMap",
		},
	},
	SPPerformanceSettingsTable = {
		SPMapPerfSettingSubTables = {
			"SPMapPerfSettingData",
		},
		SPPerfSettingTypeSubTables = {
			"SPPerfSettingTypeData",
		},
	},
	SPBaseAttributeTable = {
		SPMonsterBaseAttributeSubTables = {
			"SPMonsterBaseAttribute",
		},
		SPPetBaseAttributeSubTables = {
			"SPPetBaseAttribute",
		},
		SPPlayerBaseAttributeSubTables = {
			"SPPlayerBaseAttribute",
		},
	},
	SPMonsterDynamicAttributeTable = {
		SPMonsterDynamicAttributeSubTables = {
			"SPMonsterDynamicAttribute",
		},
		SPMonsterDynamicAttributeFactorSubTables = {
			"SPMonsterDynamicAttributeFactor",
		},
	},
	SPMonsterPoolDataTable = {
		SPMonsterPoolDataSubTables = {
			"SPMonsterPoolData",
		},
	},
	SPMonsterVoiceTable = {
		SPMonsterVoiceSubTables = {
			"SPMonsterVoice",
		},
	},
	SPNPCConfigDataTable = {
		SPNPCConfigDataSubTables = {
			"SPNPCConfigData",
		},
	},
	SPOWScenarioTriggerConfigStarpTable = {
		SPOWScenarioTriggerConfigDataSubTables = {
			"SPOWScenarioTriggerConfigData",
		},
	},
	SPOfflineSOCStarpTable = {
		SPOfflineSOCSubTables = {
			"SPOfflineSOC",
		},
		SPOfflineSOCPetStatusSubTables = {
			"SPOfflineSOCPetStatus",
		},
		SPOfflineSOCSatietySubTables = {
			"SPOfflineSOCSatiety",
		},
	},
	SPPCKeyConfigDataTable = {
		SPPCKeyActionNameDataSubTables = {
			"SPPCKeyActionNameData",
		},
		SPPCKeyGroupDataSubTables = {
			"SPPCKeyGroupData",
		},
		SPPCKeyIconDataSubTables = {
			"SPPCKeyIconData",
		},
	},
	SPPVPAutoChessTable = {
		SPPVPAutoChessMapSubTables = {
			"SPPVPAutoChessMap",
		},
	},
	SPPVPGlobalTable = {
		SPPVPGlobalSubTables = {
			"SPPVPGlobal",
		},
	},
	SPPVPPetTable = {
		SPPVPPetSubTables = {
			"SPPVPPet",
		},
		SPPVPPetBodyScaleSubTables = {
			"SPPVPPetBodyScale",
		},
		SPPVPPetJobSubTables = {
			"SPPVPPetJob",
		},
		SPPVPPetPresetsSubTables = {
			"SPPVPPetPresets",
		},
		SPPVPPetTypeSubTables = {
			"SPPVPPetType",
		},
	},
	SPPVPPetLevelAttributeTable = {
		SPPVPPetLevelAttributeSubTables = {
			"SPPVPPetLevelAttribute",
		},
		SPPVPPetLevelAttribute_ABTestSubTables = {
			"SPPVPPetLevelAttribute_ABTest",
		},
		SPPVPPetMonsterLevelAttributeSubTables = {
			"SPPVPPetMonsterLevelAttribute",
		},
		SPPVPPetMonsterLevelAttribute_ABTestSubTables = {
			"SPPVPPetMonsterLevelAttribute_ABTest",
		},
		SPPVPPetStarLevelAttributeSubTables = {
			"SPPVPPetStarLevelAttribute",
		},
	},
	SPPanelTable = {
		SPPanelSubTables = {
			"SPPanel",
		},
	},
	SPPassiveAbilityDataTable = {
		SPPassiveAbilitySubTables = {
			"SPPassiveAbilityData",
		},
	},
	SPPassiveSkillsConfigTable = {
		SPPassiveSkillsConfSubTables = {
			"SPPassiveSkillsConf",
		},
	},
	SPPetAmuletTable = {
		SPPetAmuletSubTables = {
			"SPPetAmulet",
		},
		SPPetAmuletUpgradeSubTables = {
			"SPPetAmuletUpgrade",
		},
	},
	SPPetClientNetConfigTable = {
		SPPetClientNetConfigSubTables = {
			"SPPetClientNetConfig",
		},
	},
	SPPetIllustratedAwardTable = {
		SPPetIllustratedAwardSubTables = {
			"SPPetIllustratedAward",
		},
	},
	SPPetInteractionConfigTable = {
		SPPetInteractionConfigSubTables = {
			"SPPetInteractionConfig",
		},
	},
	SPPetLevelAttributeStarpTable = {
		SPPetLevelAttributeSubTables = {
			"SPPetLevelAttribute",
		},
		SPPetLevelAttribute_ABTestSubTables = {
			"SPPetLevelAttribute_ABTest",
		},
		SPPetMonsterLevelAttributeSubTables = {
			"SPPetMonsterLevelAttribute",
		},
		SPPetMonsterLevelAttribute_ABTestSubTables = {
			"SPPetMonsterLevelAttribute_ABTest",
		},
		SPPetStarLevelAttributeSubTables = {
			"SPPetStarLevelAttribute",
		},
	},
	SPPetPrefebTable = {
		SPPetPrefebSubTables = {
			"SPPetPrefeb",
		},
	},
	SPPetRideTable = {
		SPPetRideBasicInfoSubTables = {
			"SPPetRideBasicInfo",
		},
		SPPetRideMoveSubTables = {
			"SPPetRideMove",
		},
		SPPetRidePoseSubTables = {
			"SPPetRidePose",
		},
		SPPetRideShapeSubTables = {
			"SPPetRideShape",
		},
	},
	SPPetRotationControlConfigTable = {
		SPPetRotationControlConfigSubTables = {
			"SPPetRotationControlConfig",
		},
	},
	SPStatusStarpTable = {
		SPPetStatusSubTables = {
			"SPPetStatus",
		},
		SPPlayerStatusSubTables = {
			"SPPlayerStatus",
		},
	},
	SPPhysicsMatAttrDataTable = {
		SPPhysicsMatAttrDataSubTables = {
			"SPPhysicsMatAttrData",
		},
	},
	SPPreloadTable = {
		SPPreloadPathSubTables = {
			"SPPreloadPath",
		},
		SPSeedDSPreloadPathSubTables = {
			"SPSeedDSPreloadPath",
		},
	},
	SPProfessionConfigStarpTable = {
		SPProfessionSubTables = {
			"SPProfessionConf_common",
		},
		SPProfessionGenreSubTables = {
			"SPProfessionGenreConf",
		},
	},
	SPProtectedScoreTable = {
		SPProtectedScoreLevelDimensionDataSubTables = {
			"SPProtectedScoreLevelDimensionData_starp_pvp",
		},
	},
	SPPuzzleMatrixConfigTable = {
		SPPuzzleMatrixConfigSubTables = {
			"SPPuzzleMatrixConfig",
		},
	},
	SPQualifyingTable = {
		SPQualifyingIntegralDataSubTables = {
			"SPQualifyingIntegralData_starp_pvp",
		},
		SPSeasonQualifyingMailSubTables = {
			"SPSeasonQualifyingMailData_starp_pvp",
		},
		SPShocksQualifyingRewardDataSubTables = {
			"SPShocksQualifyingRewardData_starp_pvp",
		},
	},
	SPTutorialConfigTable = {
		SPQuickSearchConfigSubTables = {
			"SPQuickSearchConfig",
		},
		SPTutorialCategoryConfigSubTables = {
			"SPTutorialCategoryConfig",
		},
		SPTutorialConfigSubTables = {
			"SPTutorialConfig",
		},
		SPTutorialDetailConfigSubTables = {
			"SPTutorialDetailConfig",
		},
	},
	SPResourceAICreatorTable = {
		SPResourceAICreatorSubTables = {
			"SPResourceAICreator",
		},
	},
	SPResourceAirwallStarpTable = {
		SPResourceAirwallSubTables = {
			"SPResourceAirwall",
		},
	},
	SPResourceControlTable = {
		SPResourceControlConfSubTables = {
			"SPResourceControl",
		},
	},
	SPResourceGPOTable = {
		SPResourceGPOSubTables = {
			"SPResourceGPO",
		},
	},
	SPResourcePatrolStarpTable = {
		SPResourcePatrolSubTables = {
			"SPResourcePatrol",
		},
	},
	SPResourceSTPTable = {
		SPResourceSTPSubTables = {
			"SPResourceSTP",
		},
	},
	SPResourceVirtualPointTable = {
		SPResourceVirtualPointSubTables = {
			"SPResourceVirtualPoint",
		},
	},
	SPResourceZoneTable = {
		SPResourceZoneSubTables = {
			"SPResourceZone",
		},
	},
	SPRiverPolygonConfigDataTable = {
		SPRiverPolygonConfigDataSubTables = {
			"SPRiverPolygonConfigData",
		},
	},
	SPRoleLevelBaseAttributeTable = {
		SPRoleLevelBaseAttributeSubTables = {
			"SPRoleLevelBaseAttribute",
		},
	},
	SPSOCAIBuildTaskConfigTable = {
		SPSOCAIBuildTaskSubTables = {
			"SPSOCAIBuildTask",
		},
	},
	SPSettingConfigTable = {
		SPSettingConfSubTables = {
			"SPSettingConfig",
		},
	},
	SPSettingPCConfigTable = {
		SPSettingPCConfSubTables = {
			"SPSettingPCConfig",
		},
	},
	SPShipFunctionControlTable = {
		SPShipFunctionControlSubTables = {
			"SPShipFunctionControl",
		},
	},
	SPShopConfigTable = {
		SPShopConfigSubTables = {
			"SPShopConfig",
		},
	},
	SPShopDetailsConfigTable = {
		SPShopDetailsConfigSubTables = {
			"SPShopDetailsConfig",
		},
	},
	SPSimpleActionTable = {
		SPSimpleCondActionPairSubTables = {
			"SPSimpleCondActionPair",
		},
	},
	SPSkillComboDataTable = {
		SPSkillComboDataSubTables = {
			"SPSkillComboData",
		},
	},
	SPSkinVoiceBankMapConfigDataTable = {
		SPSkinVoiceBankMapConfigDataSubTables = {
			"SPSkinVoiceBankMapConfigData",
		},
	},
	SPSnapConfigTable = {
		SPSnapConfigDataSubTables = {
			"SPSnapConfigData",
		},
	},
	SPSocInteractionRatioTable = {
		SPSocInteractionRatioConfSubTables = {
			"SPSocInteractionRatio",
		},
	},
	SPSoundVolumeConfigDataTable = {
		SPSoundVolumeConfigDataSubTables = {
			"SPSoundVolumeConfigData",
		},
	},
	SPStaminaStarpTable = {
		SPStaminaCostSubTables = {
			"SPStaminaCost",
		},
		SPStaminaSumSubTables = {
			"SPStaminaSum",
		},
	},
	SPStarEggConfigDataTable = {
		SPStarEggConfigDataSubTables = {
			"SPStarEggConfigData",
		},
		SPStarEggFirstIncubateDataSubTables = {
			"SPStarEggFirstIncubateData",
		},
		SPStarEggFlashDataSubTables = {
			"SPStarEggFlashData",
		},
		SPStarEggGroupDataSubTables = {
			"SPStarEggGroupData",
		},
		SPStarEggRandomPoolDataSubTables = {
			"SPStarEggRandomPoolData",
		},
		SPStarEggRandomWeightDataSubTables = {
			"SPStarEggRandomWeightData",
		},
		SPStarEggTemperatureDataSubTables = {
			"SPStarEggTemperatureData",
		},
	},
	SPTagTable = {
		SPStateToTagSubTables = {
			"SPStateToTag",
		},
		SPTagNameSubTables = {
			"SPTagName",
		},
		SPTagToStateSubTables = {
			"SPTagToState",
		},
		SPTagToTagSubTables = {
			"SPTagToTag",
		},
	},
	SPStoryLineConfigTable = {
		SPStoryLineCondConfigSubTables = {
			"SPStoryLineCondConfig",
		},
		SPStoryLineConfigSubTables = {
			"SPStoryLineConfig",
		},
		SPStoryLineRandomGroupConfigSubTables = {
			"SPStoryLineRandomGroupConfig",
		},
		SPStoryLineSwitcherConfigSubTables = {
			"SPStoryLineSwitcherConfig",
		},
	},
	SPAbilitySubPackageTable = {
		SPSubPackageDataSubTables = {
			"SPSubPackageData",
		},
	},
	SPSummonConfigStarpTable = {
		SPSummonConfigSubTables = {
			"SPSummonConfig",
		},
	},
	SPTalentStarpTable = {
		SPTalentInfoSubTables = {
			"SPTalentInfo",
		},
		SPTalentTreeSubTables = {
			"SPTalentTree",
		},
		SPTalentAbilityInfoSubTables = {
			"SPalentAbilityInfo",
		},
	},
	SPTargetIndicatorTable = {
		SPTargetIndicatorSubTables = {
			"SPTargetIndicator",
		},
	},
	SPTaskTable = {
		SPTaskConfSubTables = {
			"SPTaskConf_common",
		},
	},
	SPTechGroupConfigTable = {
		SPTechGroupConfigDataSubTables = {
			"SPTechGroupConfigData",
		},
	},
	SPTechPointConfigTable = {
		SPTechPointConfigDataSubTables = {
			"SPTechPointConfigData",
		},
	},
	SPTechTreeConfigTable = {
		SPTechTreeConfigDataSubTables = {
			"SPTechTreeConfigData",
		},
	},
	SPTradingPostStarpTable = {
		SPTradingPostLevelDataSubTables = {
			"SPTradingPostLevelData",
		},
		SPTradingPostOrderDataSubTables = {
			"SPTradingPostOrderData",
		},
		SPTradingPostOrderRefreshCDDataSubTables = {
			"SPTradingPostOrderRefreshCDData",
		},
		SPTradingPostOrderRefreshDataSubTables = {
			"SPTradingPostOrderRefreshData",
		},
		SPTradingPostTagDataSubTables = {
			"SPTradingPostTagData",
		},
	},
	SPTreasureChestConfigDataTable = {
		SPTreasureChestConfigDataSubTables = {
			"SPTreasureChestConfigData",
		},
		SPTreasureChestRandomPoolDataSubTables = {
			"SPTreasureChestRandomPoolData",
		},
		SPTreasureChestRandomWeightDataSubTables = {
			"SPTreasureChestRandomWeightData",
		},
	},
	SPTreeStaticMeshScaleDataTable = {
		TreeStaticMeshScaleDataSubTables = {
			"SPTreeStaticMeshScaleData",
		},
	},
	SPTriggerTlogConfigTable = {
		SPTriggerTlogConfigSubTables = {
			"SPTriggerTlogConfig",
		},
	},
	SPVideoConfigTable = {
		SPVideoConfigSubTables = {
			"SPVideoConfig",
		},
	},
	FPSWeaponStarPTable = {
		FPSWeaponClientConfSubTables = {
			"SPWeaponClientConfDataStarP",
		},
		WeaponEnergySubTables = {
			"SPWeaponEnergyStarP",
		},
		FPSWeaponPropertyConfSubTables = {
			"SPWeaponPropertyConfDataStarP",
		},
		FPSWeaponRenovationConfSubTables = {
			"SPWeaponRenovationConfDataStarP",
		},
		FPSWeaponUnlockInfoSubTables = {
			"SPWeaponUnlockInfoStarP",
		},
	},
	SPWorldCareerIdolConfigDataTable = {
		SPWorldCareerIdolConfigDataSubTables = {
			"SPWorldCareerIdolConfigData",
		},
	},
	SPWorldCareerIdolPointDataTable = {
		SPWorldCareerIdolPointDataSubTables = {
			"SPWorldCareerIdolPointData",
		},
	},
	SPWorldChoppablePointDataTable = {
		SPWorldChoppablePointDataSubTables = {
			"SPWorldChoppablePointData",
			"SPWorldChoppablePointData_PCG",
			"SPWorldChoppablePointData_Tree",
		},
	},
	SPWorldChoppableTreeSMDataTable = {
		SPWorldChoppableTreeSMDataSubTables = {
			"SPWorldChoppableTreeSMData",
		},
	},
	SPWorldFireBeadPointDataTable = {
		SPWorldFireBeadPointDataSubTables = {
			"SPWorldFireBeadPointData",
		},
	},
	SPWorldGodStatuePointDataTable = {
		SPWorldGodStatuePointDataSubTables = {
			"SPWorldGodStatuePointData",
		},
	},
	SPWorldIllegalBuildRegionPointDataTable = {
		SPWorldIllegalBuildRegionPointDataSubTables = {
			"SPWorldIllegalBuildRegionPointData",
		},
	},
	SPWorldLandTemplePointDataStarpTable = {
		SPWorldLandTemplePointDataSubTables = {
			"SPWorldLandTemplePointData",
		},
	},
	SPWorldLeafEffectDataTable = {
		SPWorldLeafEffectPointDataSubTables = {
			"SPWorldLeafEffectPointData",
		},
	},
	SPWorldLevelTable = {
		SPWorldLevelSubTables = {
			"SPWorldLevel",
		},
	},
	SPWorldMazeConfigStarpTable = {
		SPWorldMazeConfigDataSubTables = {
			"SPWorldMazeConfigData",
		},
	},
	SPWorldMazeEntranceStarpTable = {
		SPWorldMazeEntranceDataSubTables = {
			"SPWorldMazeEntranceData",
		},
	},
	SPWorldMonsterLevelRangeDataTable = {
		SPWorldMonsterLevelRangeDataSubTables = {
			"SPWorldMonsterLevelRangeData",
		},
	},
	SPWorldMonsterPointDataTable = {
		SPWorldMonsterPointDataSubTables = {
			"SPWorldMonsterPointData",
		},
	},
	SPWorldMonsterTimeTypeDataStarpTable = {
		SPWorldMonsterTimeTypeDataSubTables = {
			"SPWorldMonsterTimeTypeData",
		},
	},
	SPWorldNPCPointDataTable = {
		SPWorldNPCPointDataSubTables = {
			"SPWorldNPCPointData",
		},
	},
	SPWorldPOIRegionPointDataTable = {
		SPWorldPOIRegionPointDataSubTables = {
			"SPWorldPOIRegionPointData",
		},
	},
	SPWorldPVPEntranceStarpTable = {
		SPWorldPVPEntranceDataSubTables = {
			"SPWorldPVPEntranceData",
		},
	},
	SPWorldPickablesDataTable = {
		SPWorldPickablesPointDataSubTables = {
			"SPWorldPickablesPointData",
			"SPWorldPickablesPointData_PCG",
		},
		SPWorldPickablesPoolDataSubTables = {
			"SPWorldPickablesPoolData",
		},
	},
	SPWorldPropsPointDataTable = {
		SPWorldPropsPointDataSubTables = {
			"SPWorldPropsPointData",
			"SPWorldPropsPointData_PCG",
		},
	},
	SPWorldPropsPoolDataTable = {
		SPWorldPropsPoolDataSubTables = {
			"SPWorldPropsPoolData",
		},
	},
	SPWorldRLConfigStarpTable = {
		SPWorldRLAbilityLibraryDataSubTables = {
			"SPWorldRLAbilityLibraryData",
		},
		SPWorldRLAbilityRandomTemplateDataSubTables = {
			"SPWorldRLAbilityRandomTemplateData",
		},
		SPWorldRLAbilityTriadDataSubTables = {
			"SPWorldRLAbilityTriadData",
		},
		SPWorldRLChapterCompleteBuffDataSubTables = {
			"SPWorldRLChapterCompleteBuffData",
		},
		SPWorldRLChapterDataSubTables = {
			"SPWorldRLChapterData",
		},
		SPWorldRLLinearLevelDataSubTables = {
			"SPWorldRLLinearLevelData",
		},
		SPWorldRLPeriodAlternateDataSubTables = {
			"SPWorldRLPeriodAlternateData",
		},
		SPWorldRLPeriodLevelDataSubTables = {
			"SPWorldRLPeriodLevelData",
		},
	},
	SPWorldRebornPointDataTable = {
		SPWorldRebornPointDataSubTables = {
			"SPWorldRebornPointData",
		},
	},
	SPWorldRegionDataTable = {
		SPWorldRegionDataSubTables = {
			"SPWorldRegionData",
		},
	},
	SPWorldRegionDefineTable = {
		SPWorldRegionDefineSubTables = {
			"SPWorldRegionDefine",
		},
	},
	SPWorldRiverPolygonPointDataTable = {
		SPWorldRiverPolygonPointDataSubTables = {
			"SPWorldRiverPolygonPointData",
		},
	},
	SPWorldSeaAudioDataTable = {
		SPWorldSeaAudioDataSubTables = {
			"SPWorldSeaAudioData",
		},
	},
	SPWorldSkillFruitTreeDataTable = {
		SPWorldSkillFruitConfigDataSubTables = {
			"SPWorldSkillFruitConfigData",
		},
		SPWorldSkillFruitTreePointDataSubTables = {
			"SPWorldSkillFruitTreePointData",
		},
	},
	SPWorldSoundVolumePointDataTable = {
		SPWorldSoundVolumePointDataSubTables = {
			"SPWorldSoundVolumePointData",
		},
	},
	SPWorldStarDiaryPointDataTable = {
		SPWorldStarDiaryPointDataSubTables = {
			"SPWorldStarDiaryPointData",
		},
	},
	SPWorldStarEggPointDataTable = {
		SPWorldStarEggPointDataSubTables = {
			"SPWorldStarEggPointData",
			"SPWorldStarEggPointData_PCG",
		},
	},
	SPWorldStreamablePOIConfigTable = {
		SPWorldStreamablePOIConfigSubTables = {
			"SPWorldStreamablePOIConfig",
		},
	},
	SPWorldStreamablePOIPreloadAssetBundleConfigTable = {
		SPWorldStreamablePOIPreloadAssetBundleConfigSubTables = {
			"SPWorldStreamablePOIPreloadAssetBundleConfig",
		},
	},
	SPWorldStreamablePOIPreloadAssetPathConfigTable = {
		SPWorldStreamablePOIPreloadAssetPathConfigSubTables = {
			"SPWorldStreamablePOIPreloadAssetPathConfig",
		},
	},
	SPWorldStreamablePOIPreloadDataTable = {
		SPWorldStreamablePOIPreloadDataSubTables = {
			"SPWorldStreamablePOIPreloadData",
		},
	},
	SPWorldTeamPVEConfigStarpTable = {
		SPWorldTeamPVEConfigDataSubTables = {
			"SPWorldTeamPVEConfigData",
		},
	},
	SPWorldTeamPVEEntranceStarpTable = {
		SPWorldTeamPVEEntranceDataSubTables = {
			"SPWorldTeamPVEEntranceData",
		},
	},
	SPWorldTeleportPointDataStarpTable = {
		SPWorldTeleportPointDataSubTables = {
			"SPWorldTeleportPointData",
		},
	},
	SPWorldTemperatureDefineTable = {
		SPWorldTemperatureDefineSubTables = {
			"SPWorldTemperatureDefine",
		},
	},
	SPWorldTowerConfigStarpTable = {
		SPWorldTowerConfigDataSubTables = {
			"SPWorldTowerConfigData",
		},
	},
	SPWorldTowerEntranceStarpTable = {
		SPWorldTowerEntranceDataSubTables = {
			"SPWorldTowerEntranceData",
		},
	},
	SPWorldTreasureChestPointDataTable = {
		SPWorldTreasureChestPointDataSubTables = {
			"SPWorldTreasureChestPointData",
			"SPWorldTreasureChestPointData_PCG",
		},
	},
	SPWorldUnderlandConfigStarpTable = {
		SPWorldUnderlandConfigDataSubTables = {
			"SPWorldUnderlandConfigData",
		},
	},
	SPWorldUnderlandEntranceStarpTable = {
		SPWorldUnderlandEntranceDataSubTables = {
			"SPWorldUnderlandEntranceData",
		},
	},
	TextStarpTable = {
		ClientServerTextConfDataSubTables = {
			"ServerTextConfData_mainStarp",
		},
	},
	TextTable = {
		ClientServerTextConfDataSubTables = {
			"ServerTextConfData_starp",
		},
	},
	StarPCommonDbWhiteListConfTable = {
		StarPCommonDbWhiteListConfSubTables = {
			"StarPCommonDbWhiteListConf",
		},
	},
	StarPEnumConfTable = {
		StarPEnumConfSubTables = {
			"StarPEnumConf",
		},
	},
	StarPLoadingPicConfTable = {
		StarPLoadingPicConfSubTables = {
			"StarPLoadingPicConf",
		},
	},
	MatchStarpTable = {
		MatchRuleRangeDataSubTables = {
			"StarPMatchRuleRangeData_SPGame",
		},
		MatchTypeDataSubTables = {
			"StarPMatchTypeData_SPGame",
		},
		MatchRuleDataSubTables = {
			"MatchRuleData_SP",
			"MatchRuleData_starp",
		},
		MatchRoomInfoDataSubTables = {
			"MatchRoomInfoData_SP",
			"MatchRoomInfoData_starp",
		},
		MatchDynamicTeamRobotsDataSubTables = {
			"MatchDynamicTeamRobotsData_starp",
		},
		MatchDynamicMaxTimeoutDataSubTables = {
			"MatchDynamicMaxTimeoutData_starp",
		},
		MatchSimulatorDynamicMaxTimeoutDataSubTables = {
			"MatchSimulatorDynamicMaxTimeoutData_starp",
		},
		MatchSpecialRoundRobotSideDiffVirtualRoomInfoDataSubTables = {
			"MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_starp",
		},
		MatchDimensionDataSubTables = {
			"MatchDimensionData_SP",
		},
	},
	SPModeTable = {
		StarPModeConfSubTables = {
			"StarPModeConf",
		},
	},
	StarPPicConfTable = {
		StarPPicConfSubTables = {
			"StarPPicConf",
		},
	},
	StarPRoomConfTable = {
		StarPRoomConfSubTables = {
			"StarPRoomConf",
		},
	},
	StarPRoomNumConfTable = {
		StarPRoomNumConfSubTables = {
			"StarPRoomNumConf",
		},
	},
	StarPSysConfStarpTable = {
		StarPSysConfSubTables = {
			"StarPSysConf",
		},
	},
	TextLuaStarpTable = {
		TextEntryDataSubTables = {
			"TextEntryData_StarP",
		},
	},
	TlogAnalyseStarpTable = {
		TlogAnalyseConfSubTables = {
			"TlogAnalyseConf_StarP",
		},
	},
}

return TableMap
