import os, re, json
from openpyxl import load_workbook
from openpyxl.worksheet.worksheet import Worksheet

script_dir = os.path.dirname(os.path.abspath(__file__))
xls_dir = os.path.join(script_dir, "../xls")
cdn_json = os.path.join(script_dir, "cdnjson.json")
cdnjson_output = os.path.join(script_dir, "cdnjson_output.json")

split_chars = [";", ",", "|"]

class CheckCDNImage():
    def __init__(self):
        self.cdn_img_map = {}
        self.resource_map = {}
        self.load_cdn_json()
        self.reload_resource_map()

    def is_english_string(self, var):
        pattern = r'^[a-zA-Z0-9_:]+$'
        return bool(re.match(pattern, var))
    
    def remove_cdn_prefix(self, s):
        if s.startswith("CDN:"):
            return s.replace("CDN:", "", 1)
        return s
    
    def load_cdn_json(self):
        with open(cdn_json, "r", encoding="utf-8") as f:
            cdn_json_info = json.load(f)
            for image_info in cdn_json_info["ImageList"]:
                if "name" in image_info:
                    img_name = os.path.splitext(image_info["name"])[0]
                    self.cdn_img_map[img_name] = True

    def reload_resource_map(self):
        self.resource_map = {}

        file_full_paths = []
        for parent, _, file_names in os.walk(xls_dir):
            for file_name in file_names:
                if not (file_name.endswith(".xlsx") or file_name.endswith(".xlsm")):
                    continue
                file_full_paths.append(os.path.join(parent, file_name))

        total_file_count = len(file_full_paths)
        now_file_count = 0    
        for file_path in file_full_paths:
            print(f"Loading {now_file_count}/{total_file_count} {file_path}")
            now_file_count += 1
            rel_path = os.path.relpath(file_path, xls_dir)
            try:
                wb = load_workbook(file_path, data_only=True)
                for sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    if not isinstance(ws, Worksheet):
                        continue
                    for row in ws.iter_rows(min_row=4):
                        for cell in row:
                            if not isinstance(cell.value, str):
                                continue
                            if not self.is_english_string(cell.value):
                                continue
                            current_split_char = None
                            for split_char in split_chars:
                                if split_char in cell.value:
                                    current_split_char = split_char
                                    break
                            if current_split_char:
                                split_values = cell.value.split(current_split_char)
                            else:
                                split_values = [cell.value]
                            for split_value in split_values:
                                split_value = self.remove_cdn_prefix(split_value)
                                if self.cdn_img_map.get(split_value):
                                    finded_str = f"{rel_path}, {sheet_name}, {cell.column_letter}列, {cell.row}行, 单元格内容: {cell.value}, 表头：{ws.cell(3, cell.column).value}"
                                    self.resource_map[split_value] = finded_str
                                    print("find!", split_value, finded_str)
            except:
                pass            

        with open(cdn_json, "r", encoding="utf-8") as f:
            cdn_json_info = json.load(f)
            for image_info in cdn_json_info["ImageList"]:
                if "name" in image_info:
                    img_name = os.path.splitext(image_info["name"])[0]
                    if img_name in self.resource_map:
                        image_info["finded"] = self.resource_map[img_name]
            with open(cdnjson_output, 'w', encoding='utf-8') as file:
                json.dump(cdn_json_info, file, indent=4, ensure_ascii=False)

if __name__ == "__main__":
    cci = CheckCDNImage()