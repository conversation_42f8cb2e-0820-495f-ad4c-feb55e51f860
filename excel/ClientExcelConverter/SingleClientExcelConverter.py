#!/usr/bin/python
# -*- coding: utf-8 -*-

import multiprocessing
import os
import sys
from collections import OrderedDict
from subprocess import Popen

sys.path.append(os.getcwd())
import conv2pb

#CONFIG_FILE_PATH = os.path.join(os.path.dirname(os.path.realpath(__file__)), "ClientExcelConverter.json")
os.chdir(os.path.dirname(os.path.dirname(os.path.realpath(__file__))))


# args (targetExcelPath, k_GameName, is_client, is_Internal, is_Export_ZH_Excel)
if __name__ == "__main__":

    k_GameName = ""
    targetExcelPath = ""
    is_client = True
    is_Internal = False
    is_Export_ZH_Excel = False
    if len(sys.argv) > 5:
        homePath = sys.argv[1]
        targetExcelPath = sys.argv[2]
        k_GameName = sys.argv[3]
        is_client = sys.argv[4] == "1"
        is_Internal = sys.argv[5] == "1"
        is_Export_ZH_Excel = sys.argv[6] == "1"

        print("Params {0} {1} {2} {3} {4} {5}".format(homePath, targetExcelPath, k_GameName, is_client, is_Internal, is_Export_ZH_Excel))

        has_svn = False
        process_log_list = None
        info = {targetExcelPath: []}

        conv2pb.IsInternal = is_Internal
        conv2pb.IsExport_ZH_Excel = is_Export_ZH_Excel
        conv2pb.main(homePath, info, has_svn, is_client, k_GameName, process_log_list, disable_full_check = True, enable_obselete_pbin_check = False)