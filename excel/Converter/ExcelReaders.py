import copy
import datetime
import os

import Logger
import ConverterUtility
from ExcelGroup import ExcelGroup, ExcelDesc
import hashlib
import python_calamine
from convExcelReader import ConvLoadExcel
from convMisc import ConvIsValidExcel
import ToolSettings

from TranslationTool import Consts as TranslationConsts

_LOG_TAG = "ExcelReaders"


class ExcelReaders:
    _DefaultReaders = None

    ## in class begin
    class ConvertRule:
        def __init__(self):
            self.ProtoFileName = None
            self.ProtoMessageType = None
            self.BinaryFileName = None
            self.ConvertAction = None

        def __str__(self):
            return f"{self.ConvertAction}({self.ProtoFileName},{self.ProtoMessageType},{self.BinaryFileName})"

    class Excel:
        def __init__(self):
            self.ExcelDesc = ExcelDesc(self)
            self.OriginCSVPath = None
            self._Sheets = None
            self._calamineBook = None
            self._can_convert_sheet_names = None

        def _CreateExtentSheets(self):
            sheet_names = self._GetSheetNames()
            self._Sheets = {}
            if sheet_names is None:
                return

            for sheet_name in sheet_names:
                extend_sheet = ExcelReaders.ExtendSheet(sheet_name, self._GetCalamineSheet)
                if extend_sheet.GetConvertRule() is None:
                    continue
                self._Sheets[sheet_name] = extend_sheet

        def GetSheet(self, sheet_name):
            if self._Sheets is None:
                self._CreateExtentSheets()
            return self._Sheets.get(sheet_name)

        def _GetSheetNames(self):
            return self._ReadSheetNameByCalamine()

        def GetSheetNames(self):
            if self._can_convert_sheet_names is None:
                sheet_names = self._GetSheetNames()
                self._can_convert_sheet_names = []
                for sheet_name in sheet_names:
                    extend_sheet = self.GetSheet(sheet_name)
                    if extend_sheet is not None and extend_sheet.GetConvertRule() is not None:
                        self._can_convert_sheet_names.append(sheet_name)
            return self._can_convert_sheet_names

        def _ReadExcelByCalamine(self):
            if self._calamineBook is None:
                self._calamineBook = ConvLoadExcel(self.ExcelDesc.ExcelPath)
                # python_calamine.load_workbook(self.ExcelDesc.ExcelPath)

        def _GetCalamineSheet(self, sheet_name):
            self._ReadExcelByCalamine()
            if self._calamineBook is None:
                return None

            return self._calamineBook.SheetByName(sheet_name)

        def _ReadSheetNameByCalamine(self):
            self._ReadExcelByCalamine()
            return list(self._calamineBook.SheetNames)

    class ExtendSheet:
        FIELD_NAME_ROW = 2
        DATA_START_ROW = 3

        def __init__(self, sheet_name, get_calamine_sheet_func):
            self._calamine_sheet_func = get_calamine_sheet_func
            self._raw_calamine_sheet = None
            self._raw_calamine_sheet_data = None
            self._raw_trans_sheet_data = {}
            self.SheetName = sheet_name
            self._override_data = {}
            self._convert_tags = None
            self._convert_to_keys = None
            self._is_trans = False
            self._nrows = None
            self._ncols = None
            self._CheckIsTransSheet()

        @property
        def _sheet(self):
            return self._calamine_sheet

        @property
        def _calamine_sheet(self):
            if self._raw_calamine_sheet is None:
                self._raw_calamine_sheet = self._calamine_sheet_func(self.SheetName)

            return self._raw_calamine_sheet

        @property
        def _calamine_sheet_data(self):
            if self._raw_calamine_sheet_data is None:
                self._raw_calamine_sheet_data = self._calamine_sheet.ReadData()
            return self._raw_calamine_sheet_data

        def _CheckIsTransSheet(self):
            if self._sheet.convertRule is None:
                return False

            trans_sheet_data = {}

            def __ParseValue(data):
                if isinstance(data, datetime.time):
                    # excel中以一天为比例, 转化为分钟
                    total_minutes = data.hour * 60 + data.minute + data.second / 60
                    data = total_minutes / 1440  # 24 * 60
                elif isinstance(data, datetime.timedelta):
                    # excel中以一天为比例, 转化为分钟
                    data = data.total_seconds() / (60 * 1440)
                elif isinstance(data, str):
                    # 回车字符替换
                    data = data.replace("_x000D_", "")
                    data = data.replace("\r", "")
                    data = data.replace("_x000B_", "")

                return data

            def trans_sheet():
                old_rows = self._raw_nrows
                old_cols = self._raw_ncols

                for col in range(0, old_cols):
                    row_data = trans_sheet_data.get(col + 1, {})
                    trans_sheet_data[col + 1] = row_data
                    for row in range(1, old_rows):
                        t2 = self._raw_cell_value(row, col)
                        row_data[row - 1] = __ParseValue(t2)

                if self._raw_nrows > 0:
                    trans_sheet_data[0] = self._raw_calamine_sheet_data[0]
                self._override_data = trans_sheet_data
                self._raw_trans_sheet_data.update(self._override_data)
                self._ncols = old_rows - 1
                self._nrows = old_cols + 1

            is_trans = self._sheet.convertRule.ConvertAction == "convertTrans"

            if is_trans:
                trans_sheet()
            self._is_trans = is_trans

        def _TransRowCol(self, row, col):
            return row, col
            # 第0行不进行转置，返回原始row， col
            # if row == 0: return [row, col]
            # else:
            #  row = origin_row + 1, col = origin_row - 1
            #--> origin_row = col + 1, origin_col = row - 1
            if not self._is_trans:
                return [row, col]

            if row == 0:
                return [row, col]

            return [col + 1, row - 1]

        def IsTransSheet(self):
            return self._is_trans

        def cell_value(self, row, col):
            row, col = self._TransRowCol(row, col)
            override_row = self._override_data.get(row)
            data = None
            if override_row is not None:
                data = override_row.get(col)

            if data is not None:
                return data

            if row >= self._raw_nrows or col >= self._raw_ncols:
                return ""

            data = self._raw_cell_value(row, col)

            if isinstance(data, datetime.time):
                # excel中以一天为比例, 转化为分钟
                total_minutes = data.hour * 60 + data.minute + data.second / 60
                data = total_minutes / 1440  # 24 * 60
            elif isinstance(data, datetime.timedelta):
                # excel中以一天为比例, 转化为分钟
                data = data.total_seconds() / (60 * 1440)
            elif isinstance(data, str):
                # 回车字符替换
                data = data.replace("_x000D_", "")
                data = data.replace("\r", "")
                data = data.replace("_x000B_", "")
                self._ModifyCellValue(row, col, data)
            return data

        def IsDataTimeType(self, row, col):
            row, col = self._TransRowCol(row, col)
            if self._is_trans:
                sheet_data = self._raw_trans_sheet_data
                data = sheet_data[row][col]
            else:
                data = self._raw_cell_value(row, col)
            if isinstance(data, datetime.time) or isinstance(data, datetime.timedelta):
                return True

            return False

        def IsNumber(self, row, col):
            row, col = self._TransRowCol(row, col)
            data = None
            if self._is_trans:
                sheet_data = self._raw_trans_sheet_data
                data = sheet_data[row][col]
            else:
                data = self._raw_cell_value(row, col)
            return isinstance(data, float) or isinstance(data, int)

        def _raw_cell_value(self, row, col):
            da = self._calamine_sheet_data
            va = da[row][col]
            return va

        def ModifyCellValue(self, row, col, value):
            row, col = self._TransRowCol(row, col)
            self._ModifyCellValue(row, col, value)

        def _ModifyCellValue(self, row, col, value):
            if row >= self.nrows:
                self._nrows = row + 1

            if col >= self.ncols:
                self._ncols = col + 1
            row_data = self._override_data.get(row)
            if row_data is None:
                row_data = {}
                self._override_data[row] = row_data
            row_data[col] = value

        def GetSize(self):
            return self._TransRowCol(self.nrows, self.ncols)

        @property
        def nrows(self):
            if self._nrows is not None:
                return self._nrows
            return self._raw_nrows

        @property
        def _raw_nrows(self):
            return self._calamine_sheet.nrows

        @property
        def ncols(self):
            if self._ncols is not None:
                return self._ncols
            return self._raw_ncols

        @property
        def _raw_ncols(self):
            return self._calamine_sheet.ncols

        def GetConvertRule(self):
            return self._sheet.convertRule

        @property
        def ConvertRule(self):
            return self.GetConvertRule()

        def GetConvertTags(self):
            return self._sheet.convertTags

        def GetConvertToKeys(self):
            return self._sheet.convertToKeys

    ## in class end

    def __init__(self, tool_settings, feature_names):
        self._excel_map = {}
        self._excel_group_map = {}
        self._res_convert_map_list = []
        self._tool_settings = tool_settings
        self._feature_names = list(feature_names)
        self._Init()

    def GetExcels(self, excel_path_lsit):
        pass

    def ClearExcelCache(self, excel_path_lsit):
        pass

    def _Init(self):
        self.OnFilterFeatureChange(self._feature_names)

    def IsValidExcel(self, excel_path):
        base_name = os.path.basename(excel_path)
        return ConvIsValidExcel(base_name)

    def _OpenExcel(self, excel_path):
        pass

    def _CreateExcel(self, excel_path, feature_name):
        excel = ExcelReaders.Excel()
        excel.ExcelDesc.FeatureName = feature_name
        excel.ExcelDesc.ExcelPath = excel_path
        excel.ExcelDesc.ExcelName = os.path.basename(excel_path)
        excel.ExcelDesc.RelativeExcelPath = os.path.relpath(excel.ExcelDesc.ExcelPath,
                                                            ConverterUtility.GetFeatureRootPath(feature_name))
        feature_tool_setting = ToolSettings.InitToolSettings().GetFeatureToolSetting(feature_name)
        excel.ExcelDesc.RelativeExcelDirExcelPath = os.path.relpath(excel.ExcelDesc.ExcelPath,
                                                                    feature_tool_setting.EXCEL_DIR)

        return excel

    def _GetExcelMD5(self, excel_path):
        md5_hash = hashlib.md5()
        with open(excel_path, "rb") as f:
            # 逐块读取文件内容
            for byte_block in iter(lambda: f.read(4096), b""):
                md5_hash.update(byte_block)
        return md5_hash.hexdigest()

    def _IsExcelChange(self, excel_path):
        return True

    def _IsCSV(self, excel_path):
        return excel_path.endswith(".csv")

    def _ConvertCSV2XLSX(self, excel_path):
        return excel_path

    def OnFilterFeatureChange(self, feature_names):
        for feature_name in feature_names:
            feature_settings = self._tool_settings.GetFeatureToolSetting(feature_name)
            if feature_settings is None:
                continue

            xls_folder = getattr(feature_settings, 'EXCEL_XLS_DIR', None)
            if xls_folder is None:
                Logger.LogWarning(_LOG_TAG, f"featuer: {feature_name} 未配置XLS目录（EXCEL_XLS_DIR）")
                continue

            feature_root_path = ConverterUtility.GetFeatureRootPath(feature_name)
            abs_xls_folder = xls_folder
            excel_group = self._excel_group_map.get(feature_name)
            if excel_group is None:
                excel_group = ExcelGroup(feature_name)
                self._excel_group_map[feature_name] = excel_group

            for root, dirs, files in os.walk(abs_xls_folder):
                for file in files:
                    abs_path = os.path.join(root, file)
                    if not self.IsValidExcel(abs_path):
                        if file.endswith(".xlsx"):
                            Logger.LogWarning(_LOG_TAG, "skip: " + abs_path)
                        continue

                    if os.path.normpath(TranslationConsts.TRANSLATION_EXCEL_FILE_NAME) in os.path.normpath(abs_path):
                        Logger.LogWarning(_LOG_TAG, "auto translation file skip: " + abs_path)
                        continue

                    if not self._IsExcelChange(abs_path):
                        continue

                    target_file = os.path.abspath(abs_path)
                    is_csv = self._IsCSV(abs_path)
                    if is_csv:
                        target_file = self._ConvertCSV2XLSX(abs_path)

                    excel = self._CreateExcel(target_file, feature_name)
                    if is_csv:
                        excel.OriginCSVPath = abs_path

                    self._excel_map[target_file] = excel
                    excel_group.ExcelDescList.append(excel.ExcelDesc)

    def _CheckAndCreateExcel(self, abs_path, feature_name="main"):
        if not self.IsValidExcel(abs_path):
            return

        if not self._IsExcelChange(abs_path):
            return

        target_file = os.path.abspath(abs_path)
        is_csv = self._IsCSV(abs_path)
        if is_csv:
            target_file = self._ConvertCSV2XLSX(abs_path)

        excel = self._CreateExcel(target_file, feature_name)
        if is_csv:
            excel.OriginCSVPath = abs_path
        excel.GetSheetNames()
        return excel

    def GetAllExcelGroups(self):
        return self._excel_group_map

    # 将别的ExcelReaders创建的ExcelGroup转成当前的
    def TransExcelGroup(self, excel_group):
        trans_ex_group = ExcelGroup(excel_group.FeatureName)
        for excel_desc in excel_group.ExcelDescList:
            excel_path = excel_desc.ExcelPath
            excel = self._excel_map[excel_path]
            excel.ExcelDesc.SelectedAllSheetsToConvert()
            need_converts = excel_desc.GetNeedConvertSheets()
            for sheet_name, need_convert in need_converts.items():
                if need_convert:
                    excel.ExcelDesc.SetIsNeedConvert(sheet_name, True)
            trans_ex_group.ExcelDescList.append(excel.ExcelDesc)

        return trans_ex_group

    # info = { excel_name: [sheet_name1, sheet_name2...]...}
    def GetExcelGroupByExcelPath(self, feature_name, info):
        trans_ex_group = ExcelGroup(feature_name)
        feature_setting = self._tool_settings.GetFeatureToolSetting(feature_name)
        feature_root_dir = feature_setting.EXCEL_DIR
        for excel_path, sheet_list in info.items():
            abs_path = os.path.abspath(os.path.join(feature_root_dir, excel_path))
            excel = self._excel_map.get(abs_path)
            if excel is None:
                continue
            for sheet_name in sheet_list:
                excel.ExcelDesc.SetIsNeedConvert(sheet_name, True)
            trans_ex_group.ExcelDescList.append(excel.ExcelDesc)

        return trans_ex_group

    def GetFeatureExcelGroup(self, feature_name):
        return self._excel_group_map.get(feature_name)

    def GetExcelExtendSheetListByMessage(self, feature_name, proto_message_type_name):
        attr_name = "__cache_proto_message_type_sheets"
        if hasattr(self, attr_name):
            cache_proto_message_type_sheets = getattr(self, attr_name)
        else:
            cache_proto_message_type_sheets = {}
            setattr(self, attr_name, cache_proto_message_type_sheets)

        sheet_list = cache_proto_message_type_sheets.get(proto_message_type_name)
        if sheet_list is not None:
            return sheet_list

        extend_sheet_list = []
        excel_group = self._excel_group_map[feature_name]
        for excel_desc in excel_group.ExcelDescList:
            sheet_names = excel_desc.GetSheetNames()
            for sheet_name in sheet_names:
                sheet = excel_desc.GetSheet(sheet_name)
                conv_rule = sheet.GetConvertRule()
                if conv_rule is None:
                    continue
                if conv_rule.ProtoMessageType == proto_message_type_name:
                    extend_sheet = {"extend_sheet": sheet, "excel_desc": excel_desc}
                    extend_sheet_list.append(extend_sheet)

        cache_proto_message_type_sheets[proto_message_type_name] = extend_sheet_list
        return extend_sheet_list

    def AppendExcelToConvertGroupByProtoMessageType(self, excel_group: ExcelGroup, feature_name,
                                                    proto_message_type_name):
        new_excel_adds = {}
        sheet_lists = self.GetExcelExtendSheetListByMessage(feature_name, proto_message_type_name)
        already_excel_desc = {}
        for excel_desc in excel_group.ExcelDescList:
            already_excel_desc[excel_desc.ExcelPath] = excel_desc

        for sheet_info in sheet_lists:
            extend_sheet = sheet_info["extend_sheet"]
            t_excel_desc = sheet_info["excel_desc"]
            excel_desc = already_excel_desc.get(t_excel_desc.ExcelPath)
            if excel_desc is None:
                excel_group.ExcelDescList.append(t_excel_desc)
                already_excel_desc[t_excel_desc.ExcelPath] = t_excel_desc
                excel_desc = t_excel_desc
                excel_desc.ClearSelecteds()

            before_select = excel_desc.IsSheetSelectToConvert(extend_sheet.SheetName)
            excel_desc.SetIsNeedConvert(extend_sheet.SheetName, True)
            if not before_select:
                rpath = os.path.normpath(t_excel_desc.RelativeExcelDirExcelPath)
                info = new_excel_adds.get(rpath, [])
                new_excel_adds[rpath] = info
                info.append(extend_sheet.SheetName)

        return new_excel_adds

    def AppendExeclToExcelInfoAsSameProtoMessageType(self, infos, feature_name):
        feature_setting = self._tool_settings.GetFeatureToolSetting(feature_name)
        feature_root_dir = feature_setting.EXCEL_DIR
        feature_excel_path = os.path.join(feature_root_dir, "excel")
        all_proto_message_type = {}
        for excel_rpath in infos:
            abs_path = os.path.abspath(os.path.join(feature_root_dir, excel_rpath))
            excel = self._excel_map.get(abs_path)
            if excel is None:
                continue

            sheet_names = excel.ExcelDesc.GetSheetNames()
            for sheet_name in sheet_names:
                extend_sheet = excel.ExcelDesc.GetSheet(sheet_name)
                if extend_sheet is None:
                    continue
                rule = extend_sheet.GetConvertRule()
                if rule is None:
                    continue
                all_proto_message_type[rule.ProtoMessageType] = True

        excel_group = self._excel_group_map[feature_name]

        for excel_desc in excel_group.ExcelDescList:
            excel_rpath = os.path.relpath(excel_desc.ExcelPath, feature_excel_path)
            sheet_names = excel_desc.GetSheetNames()
            for sheet_name in sheet_names:
                sheet = excel_desc.GetSheet(sheet_name)
                conv_rule = sheet.GetConvertRule()
                if conv_rule is None:
                    continue
                if conv_rule.ProtoMessageType in all_proto_message_type:
                    excel_info = infos.get(excel_desc.ExcelPath)
                    if excel_info is None:
                        excel_info = []
                        infos[excel_rpath] = excel_info
                    if sheet_name not in excel_info:
                        excel_info.append(sheet_name)

    @staticmethod
    def MergeExcelGroup(excel_group_l: ExcelGroup, excel_group_r: ExcelGroup):
        already_excel_desc = {}
        for excel_desc in excel_group_l.ExcelDescList:
            already_excel_desc[excel_desc.ExcelPath] = excel_desc

        for excel_desc in excel_group_r.ExcelDescList:
            if excel_desc.ExcelPath not in already_excel_desc:
                already_excel_desc[excel_desc.ExcelPath] = excel_desc
                excel_group_l.ExcelDescList.append(excel_desc)

    def AppendExcelToExcelGroupByInfos(self, excel_group, feature_name, infos):
        feature_setting = self._tool_settings.GetFeatureToolSetting(feature_name)
        feature_root_dir = feature_setting.EXCEL_DIR

        feature_excel_group = self.GetFeatureExcelGroup(feature_name)

        already_excel_desc = {}
        for excel_desc in excel_group.ExcelDescList:
            already_excel_desc[excel_desc.ExcelPath] = excel_desc

        add_infos = {}
        for excel_path, sheet_list in infos.items():
            abs_path = os.path.abspath(os.path.join(feature_root_dir, excel_path))
            excel_desc = already_excel_desc.get(abs_path)
            if excel_desc is None:
                excel = self._excel_map.get(abs_path)
                if excel is None:
                    Logger.LogError(self.__class__.__name__, f"找不到Excel: {abs_path}")
                    continue
                excel_desc = excel.ExcelDesc
                already_excel_desc[excel_desc.ExcelPath] = excel_desc
                excel_group.ExcelDescList.append(excel_desc)
                excel_desc.ClearSelecteds()

            if len(sheet_list) == 0:
                excel_desc.SelectedAllSheetsToConvert()
                need_converts = excel_desc.GetNeedConvertSheets()
                sheet_list = need_converts.keys()

            for sheet_name in sheet_list:
                before_select = excel_desc.IsSheetSelectToConvert(sheet_name)
                excel_desc.SetIsNeedConvert(sheet_name, True)
                if not before_select:
                    rpath = os.path.normpath(excel_desc.RelativeExcelDirExcelPath)
                    info = add_infos.get(rpath, [])
                    add_infos[rpath] = info
                    info.append(sheet_name)

        return add_infos

    def GetExcel(self, excel_abs_path):
        return self._excel_map.get(excel_abs_path)

    @classmethod
    def GetDefaultExcelReader(cls):
        if cls._DefaultReaders is None:
            all_feature_names = ConverterUtility.CollectAllFeatureDir().keys()
            cls._DefaultReaders = ExcelReaders(ToolSettings.InitToolSettings(), all_feature_names)

        return cls._DefaultReaders

    @staticmethod
    def AddSheetToConvertInfo(info, rpath, sheet_name=None):
        _sheet_names = info.get(rpath)
        if _sheet_names is None:
            _sheet_names = []
            info[rpath] = _sheet_names
            if sheet_name is None :
                return True

        if sheet_name not in _sheet_names:
            _sheet_names.append(sheet_name)
            return True

        return False

    @staticmethod
    def ExcelGroupAsConvertInfo(excel_group, ignore_no_convert=False):
        info = {}
        for excel_desc in excel_group.ExcelDescList:
            sheet_names = excel_desc.GetNeedConvertSheets().keys()
            if ignore_no_convert:
                sheet_names = excel_desc.GetSheetNames()

            for sheet_name in sheet_names:
                ExcelReaders.AddSheetToConvertInfo(info, excel_desc.RelativeExcelDirExcelPath, sheet_name)

        return info
