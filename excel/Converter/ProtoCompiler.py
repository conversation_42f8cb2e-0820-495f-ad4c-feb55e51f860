import importlib
import sys

import convUtils
import convMisc
import genCsPb
import os
import Logger
import ConverterUtility
import re
import shutil
import traceback
import ToolSettings

NO_CLI_TAG = "@noCli"
NO_SVR_TAG = "@noSvr"

_LOG_TAG = "ProtoCompiler"


class ProtoModule:
    def __init__(self):
        self.RawModule = None
        self.ProtoFilePath = None
        self.ProtoFileName = None


class ProtoCompiler:
    _CacheProtoCompilerDir = {}
    _proto_module_map = {}
    _proto_parse_map = {}

    def __init__(self, tool_settings, feature_name):
        self._proto_file_md5 = {}
        self._tool_settings = tool_settings
        self._feature_tool_settings = tool_settings.GetFeatureToolSetting(feature_name)
        self._feature_name = feature_name

        self._Init()

    def _Init(self):
        pass

    def CompileFeatureProtoFiles(self, proto_info_list, blackboard):
        filter_proto_info_list = []
        for sheet_proto_info in proto_info_list:
            proto_file_info = self.GetProtoFileInfo(sheet_proto_info.ConvertRule.ProtoFileName)
            if proto_file_info is None:
                Logger.LogWarning(_LOG_TAG,
                              f"proto: {sheet_proto_info.ConvertRule.ProtoFileName} 未找到 Excel: {sheet_proto_info.ExcelDesc.ExcelName} Sheet: {sheet_proto_info.SheetName} ")
                continue

            if not ConverterUtility.IsLetsGoFeature(proto_file_info.mod) and proto_file_info.mod != blackboard.FeatureName:
                Logger.LogError(_LOG_TAG,
                                  f"proto: {sheet_proto_info.ConvertRule.ProtoFileName} 配置表不能跨玩法引用proto，当前玩法:{blackboard.FeatureName} proto 所在玩法：{proto_file_info.mod} Excel: {sheet_proto_info.ExcelDesc.ExcelName} Sheet: {sheet_proto_info.SheetName} ")
                continue


            RET_TAG = self.__ParseProtoToServerOrClient(proto_file_info, blackboard.RuntimeOptions.IsClient)
            if RET_TAG == NO_CLI_TAG:
                Logger.LogWarning(_LOG_TAG,
                                  f"proto: {sheet_proto_info.ConvertRule.ProtoMessageType} 被标注为：{NO_CLI_TAG} Excel: {sheet_proto_info.ExcelDesc.ExcelName} Sheet: {sheet_proto_info.SheetName} 不进行客户端导表")
                continue
            elif RET_TAG == NO_SVR_TAG:
                Logger.LogWarning(_LOG_TAG,
                                  f"proto: {sheet_proto_info.ConvertRule.ProtoMessageType} 被标注为：{NO_SVR_TAG} Excel: {sheet_proto_info.ExcelDesc.ExcelName} Sheet: {sheet_proto_info.SheetName} 不进行服务器导表")
                continue
            filter_proto_info_list.append(sheet_proto_info)

        need_compiles, need_compiles_dict = self._CollectAllNeedCompiles(filter_proto_info_list, blackboard)
        has_compile_feature = {}
        for feature_name, proto_file_info_list in need_compiles.items():
            self._CompileFeatureProtos(blackboard, proto_file_info_list, feature_name)
            has_compile_feature[feature_name] = True

        if blackboard.FeatureName not in has_compile_feature:
            self._CompileFeatureProtos(blackboard, {}, blackboard.FeatureName)


        for sheet_proto_info in filter_proto_info_list:
            proto_file_info = self.GetProtoFileInfo(sheet_proto_info.ConvertRule.ProtoFileName)
            raw_module = self._ImportProtoModule(proto_file_info, **blackboard.RuntimeOptions.Options)
            if raw_module is None:
                Logger.LogException(_LOG_TAG,
                                    f"proto: {sheet_proto_info.ConvertRule.ProtoMessageType} Excel: {sheet_proto_info.ExcelDesc.ExcelName} Sheet: {sheet_proto_info.SheetName} 编译Proto失败")
                return

            if raw_module.__dict__.get(sheet_proto_info.ConvertRule.ProtoMessageType) is None:
                if blackboard.RuntimeOptions.IsClient:
                    Logger.LogWarning(_LOG_TAG,
                                      f"proto: {sheet_proto_info.ConvertRule.ProtoMessageType} 未找到，可能被被标注为：{NO_CLI_TAG} Excel: {sheet_proto_info.ExcelDesc.ExcelName} Sheet: {sheet_proto_info.SheetName} 不进行客户端导表")
                    continue
                else:
                    Logger.LogWarning(_LOG_TAG,
                                      f"proto: {sheet_proto_info.ConvertRule.ProtoMessageType} 未找到 可能被被标注为：{NO_SVR_TAG} Excel: {sheet_proto_info.ExcelDesc.ExcelName} Sheet: {sheet_proto_info.SheetName} 不进行服务器导表")
                    continue

            sheet_proto_info.ProtoModule = ProtoModule()
            sheet_proto_info.ProtoModule.ProtoFileName = os.path.basename(proto_file_info.path)
            sheet_proto_info.ProtoModule.ProtoFilePath = sheet_proto_info.ProtoFilePath
            sheet_proto_info.ProtoModule.RawModule = raw_module

    @classmethod
    def _ImportProtoModule(cls, proto_file_info, **kwargs):
        proto_file_path = proto_file_info.path
        raw_module = cls._proto_module_map.get(proto_file_path)
        if raw_module is not None:
            return raw_module
        proto_file_name = os.path.basename(proto_file_path)
        proto_feature_name = proto_file_info.mod
        if proto_file_info.isBasic() and proto_feature_name is None:
            proto_feature_name = ConverterUtility.MAIN_FEATURE_NAME.lower()
        elif proto_feature_name is None:
            raise Exception(f"could not find feature_name by {proto_file_path}")
        base_name_no_ex = os.path.splitext(proto_file_name)[0]
        kwargs['mod'] = proto_feature_name
        python_path = convUtils.getPythonDumpPath(**kwargs)
        py_path = os.path.join(python_path, f'{base_name_no_ex}')
        Logger.PerformanceTagBegin("_LoadRawProtoModule_{}".format(proto_file_name))
        raw_module = cls._LoadRawProtoModule(py_path)
        Logger.PerformanceTagEnd("_LoadRawProtoModule_{}".format(proto_file_name))
        cls._proto_module_map[proto_file_path] = raw_module
        return raw_module

    def _CompileFeatureProtos(self, blackboard, need_compiles, feature_name):
        feature_tool_settings = blackboard.ToolSettings.GetFeatureToolSetting(feature_name)
        proto_dir = feature_tool_settings.CLIENT_PROTO_DIR
        if not blackboard.RuntimeOptions.IsClient:
            proto_dir = feature_tool_settings.SERVER_PROTO_DIR
        compile_options = blackboard.RuntimeOptions.MergeOptions(blackboard.RuntimeOptions.Options,
                                                                 mod=feature_name)
        self.CompileProtoDir(proto_dir, **compile_options)

    def _CollectAllNeedCompiles(self, proto_info_list, blackboard):
        need_compiles = {}
        need_compiles_dict = {}
        for sheet_proto_info in proto_info_list:
            proto_file_info = self.GetProtoFileInfo(sheet_proto_info.ConvertRule.ProtoFileName)
            self._CollectNeedCompiles(blackboard, proto_file_info, need_compiles, need_compiles_dict)
        return need_compiles, need_compiles_dict

    def _CollectNeedCompiles(self, blackboard, proto_file_info, need_compiles, need_compiles_dict):
        self._AddDependToQueue(blackboard, proto_file_info, need_compiles, need_compiles_dict)

    def _AddDependToQueue(self, blackboard, proto_file_info, need_compiles, need_compiles_dict):
        if proto_file_info.isBasic():
            return

        proto_file_path = proto_file_info.path
        with open(proto_file_path, 'r', encoding='utf-8') as f:
            imports = re.findall(r'^\s*import\s+"(?P<proto>.*)"\s*;', f.read(), re.MULTILINE)
            for imp in imports:
                imp_file_info = self.GetProtoFileInfo(imp)
                if imp_file_info is None:
                    raise Exception(f"could not found imported:{imp} by {proto_file_path}")
                if imp_file_info.path not in self._proto_module_map:
                    if imp_file_info.path not in need_compiles_dict:
                        self._AddDependToQueue(blackboard, imp_file_info, need_compiles, need_compiles_dict)
        if proto_file_path not in need_compiles_dict:
            mod = proto_file_info.mod
            if mod is None and proto_file_info.isBasic():
                mod = ConverterUtility.MAIN_FEATURE_NAME
            feature_protos = need_compiles.get(mod)
            if feature_protos is None:
                feature_protos = []
                need_compiles[mod] = feature_protos

            feature_protos.append(proto_file_info)
            need_compiles_dict[proto_file_path] = True

    @classmethod
    def _ParseProtoToServerOrClient(cls, proto_file_info):
        proto_file_path = proto_file_info.path
        if proto_file_info.isBasic():
            return

        proto_feature_name = proto_file_info.mod
        if proto_file_info.isBasic() and proto_feature_name is None:
            proto_feature_name = ConverterUtility.MAIN_FEATURE_NAME.lower()
        elif proto_feature_name is None:
            raise Exception(f"could not find feature_name by {proto_file_path}")

        ret_list = cls._proto_parse_map.get(proto_file_path)
        if ret_list is None:
            proto_file_name = os.path.basename(proto_file_path)
            Logger.PerformanceTagBegin("compile_module_with_import_{}".format(proto_file_name))
            ret_list = genCsPb.compile_module_with_import(proto_file_name, proto_feature_name, False, False)
            Logger.PerformanceTagEnd("compile_module_with_import_{}".format(proto_file_name))
            cls._proto_parse_map[proto_file_path] = ret_list
        return ret_list

    def __ParseProtoToServerOrClient(self, proto_file_info, is_client):
        ret_list = self._ParseProtoToServerOrClient(proto_file_info)
        if is_client:
            if NO_CLI_TAG in ret_list:
                return NO_CLI_TAG
        else:
            if NO_SVR_TAG in ret_list:
                return NO_SVR_TAG
        return None

    @staticmethod
    def _LoadRawProtoModule(py_path):
        dir_name = os.path.dirname(py_path)
        base_name = os.path.basename(py_path)
        module_name = os.path.splitext(base_name)[0] + '_pb2'
        Logger.Log(_LOG_TAG, "_LoadRawProtoModule_: " + module_name + " path: " + dir_name)
        return convMisc.ConvImportModule([dir_name], module_name)

    @classmethod
    def GetProtoFileInfo(cls, proto_file_name):
        proto_file_name = os.path.basename(proto_file_name)
        proto_path_info = convUtils.getProtoInfoByName(proto_file_name)
        if proto_path_info is None:
            Logger.LogWarning(_LOG_TAG, f"未找到{proto_file_name}")
            return None
        return proto_path_info

    @classmethod
    def ParseAllFeatureProtos(cls, feature_name):
        tool_setting = ToolSettings.InitToolSettings()
        feature_tool_setting = tool_setting.GetFeatureToolSetting(feature_name)

        for (path, _, filenames) in os.walk(feature_tool_setting.EXCEL_PROTO_DIR):
            for file in filenames:
                if not file.endswith('.proto'):
                    continue
                proto_file_info = cls.GetProtoFileInfo(file)
                cls._ParseProtoToServerOrClient(proto_file_info)

    @classmethod
    def CompileProtoDir(cls, proto_dir, **kwargs):
        norm_path = os.path.abspath(proto_dir)
        norm_path = os.path.normpath(norm_path)
        if norm_path in cls._CacheProtoCompilerDir:
            return

        convUtils.compileBatchOfDirProto(proto_dir, **kwargs)
        cls._CacheProtoCompilerDir[norm_path] = True

    @classmethod
    def ParseAllProtoToServerOrClient(cls):
        tool_setting = ToolSettings.InitToolSettings()
        tool_setting.FlushModsToConvSetting()
        all_feature_tool_setting = tool_setting.GetAllFeatureToolSettings()
        for feature_name, _ in all_feature_tool_setting.items():
            cls.ParseAllFeatureProtos(feature_name)


def GParseAllProtoToServerOrClient():
    ProtoCompiler.ParseAllProtoToServerOrClient()

def GParseFeatureProtoToServerOrClient(FeatureName):
    ProtoCompiler.ParseAllFeatureProtos(FeatureName)
