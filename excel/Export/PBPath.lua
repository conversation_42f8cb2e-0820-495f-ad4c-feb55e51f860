-- This file is automatically generated

local PBPath = {
	Basic = {
		"ResKeywords.pb",
		"descriptor.pb",
		"timestamp.pb",
	},
	Base = {
		"arena_sdk_share.pb",
		"base_common.pb",
		"cs_head.pb",
		"g6_base_common.pb",
		"irpc.pb",
		"irpc_field_option.pb",
		"ss_head.pb",
		"descriptor.pb",
	},
	Common = {
		"ainpc_common.pb",
		"coc_common.pb",
		"common.pb",
		"competition_common.pb",
		"event.pb",
		"g6_common.pb",
		"game_common.pb",
		"letsgo_common.pb",
		"plat_common.pb",
		"player_info.pb",
		"resource.pb",
		"rich_common.pb",
		"stochastic.pb",
		"descriptor.pb",
	},
	Attr = {
		"attr_ABTestInfo.pb",
		"attr_ABTestSetting.pb",
		"attr_Accessories.pb",
		"attr_AccumulateBlessingsInfo.pb",
		"attr_AchievementCompleteInfo.pb",
		"attr_AchievementInfo.pb",
		"attr_AchievementStageCompleteInfo.pb",
		"attr_ActiveSuitTypeBook.pb",
		"attr_Activity.pb",
		"attr_ActivityAddNewAidInfo.pb",
		"attr_ActivityCenter.pb",
		"attr_ActivityDetail.pb",
		"attr_ActivityFarmSquadData.pb",
		"attr_ActivityFindPartnerData.pb",
		"attr_ActivityFinished.pb",
		"attr_ActivityFishGuaranteedRecord.pb",
		"attr_ActivityFishTypeGuaranteedRecord.pb",
		"attr_ActivityGroupPhotoData.pb",
		"attr_ActivityHistoryData.pb",
		"attr_ActivityHistoryPlusData.pb",
		"attr_ActivityInflateRedPacketData.pb",
		"attr_ActivityLabel.pb",
		"attr_ActivityModule.pb",
		"attr_ActivityModuleData.pb",
		"attr_ActivityPlayerRewardHistory.pb",
		"attr_ActivityPuzzleData.pb",
		"attr_ActivityPuzzleInfo.pb",
		"attr_ActivityRaffle.pb",
		"attr_ActivityRaffleInfo.pb",
		"attr_ActivityRedDot.pb",
		"attr_ActivityRewardDetail.pb",
		"attr_ActivitySquadData.pb",
		"attr_ActivitySquadDetail.pb",
		"attr_ActivitySquadInfo.pb",
		"attr_ActivityStatus.pb",
		"attr_ActivityTaskCompleteInfo.pb",
		"attr_ActivityTrophyData.pb",
		"attr_ActivityTrophyTaskCompleteData.pb",
		"attr_ActivityTwoPeopleSquadData.pb",
		"attr_ActivityUnit.pb",
		"attr_ActivityWolfKillSquadTrophyData.pb",
		"attr_AiChatInfo.pb",
		"attr_AiChatSession.pb",
		"attr_AigcCountLimit.pb",
		"attr_AigcNpcModifyTime.pb",
		"attr_AigcNpcPlayerProfile.pb",
		"attr_AigcNpcProfile.pb",
		"attr_AigcNpcPuzzleInfo.pb",
		"attr_AiNpcAttr.pb",
		"attr_AiNpcData.pb",
		"attr_AiNpcFriendChatData.pb",
		"attr_AiNpcFriendRandomChatData.pb",
		"attr_AiNpcPushRateData.pb",
		"attr_AlbumExtInfo.pb",
		"attr_AlbumInfo.pb",
		"attr_AlbumLikeHisInfo.pb",
		"attr_AlbumLimitConditionInfo.pb",
		"attr_AlbumLimitInfo.pb",
		"attr_AlbumPicAtTargetInfo.pb",
		"attr_AlbumPicExtInfo.pb",
		"attr_AlbumPicInfo.pb",
		"attr_AlbumPicLikeHisInfo.pb",
		"attr_AlbumPicNewLikeHis.pb",
		"attr_AlbumPicTargetAtInfo.pb",
		"attr_AlbumPicWallInfo.pb",
		"attr_AlbumWallPicLike.pb",
		"attr_AlgoInfo.pb",
		"attr_AlgoRecommendMatchInfo.pb",
		"attr_AllFriendInteractAttr.pb",
		"attr_AllIntimateRelationAttr.pb",
		"attr_AmusementParkData.pb",
		"attr_AmusementParkLimit.pb",
		"attr_AnimalHandbookActivityAttr.pb",
		"attr_AnimalHandbookAnimalGetInfo.pb",
		"attr_AnimalHandbookAnimalInfo.pb",
		"attr_AnimalHandbookColonyInfo.pb",
		"attr_AnimalHandbookGiveHistory.pb",
		"attr_AnimalHandbookItemInfo.pb",
		"attr_AnimalHandbookPlayerInfo.pb",
		"attr_AnimalHandbookReceiveHistory.pb",
		"attr_AnniversaryMoba.pb",
		"attr_AppearanceRoad.pb",
		"attr_AppearanceRoadGather.pb",
		"attr_AppearanceRoadLv.pb",
		"attr_AppearanceRoadShowInfo.pb",
		"attr_ApplyData.pb",
		"attr_ArenaCardGroupInfo.pb",
		"attr_ArenaCardPackBoxUpgradeHistory.pb",
		"attr_ArenaCardPackBoxUpgradeRecord.pb",
		"attr_ArenaCardPackContentHistory.pb",
		"attr_ArenaCardPackDrawHistory.pb",
		"attr_ArenaCardPackRuleHistory.pb",
		"attr_ArenaCardPackSeqRecord.pb",
		"attr_ArenaCardRegulationRecords.pb",
		"attr_ArenaDailyVictoryRecord.pb",
		"attr_ArenaGameData.pb",
		"attr_ArenaGameInfo.pb",
		"attr_ArenaHeroCombatEffectiveness.pb",
		"attr_ArenaHeroCombatEffectivenessData.pb",
		"attr_ArenaHeroCombatEffectivenessDataItem.pb",
		"attr_ArenaHeroDynamicStat.pb",
		"attr_ArenaHeroInfo.pb",
		"attr_ArenaHeroPresets.pb",
		"attr_ArenaHeroPresetsSummonerSkill.pb",
		"attr_ArenaHeroRankItem.pb",
		"attr_ArenaHeroSeasonStat.pb",
		"attr_ArenaHeroStarGeneralInfo.pb",
		"attr_ArenaHeroStarInfo.pb",
		"attr_ArenaHeroStat.pb",
		"attr_ArenaHeroStatInfo.pb",
		"attr_ArenaHeroStatKV.pb",
		"attr_ArenaHeroUnlockData.pb",
		"attr_ArenaHeroUnlockDataForCondition.pb",
		"attr_ArenaMatchStat.pb",
		"attr_ArenaMoodSetting.pb",
		"attr_ArenaPeakHistory.pb",
		"attr_ArenaPeakInfo.pb",
		"attr_ArenaPeakSeasonInfo.pb",
		"attr_ArenaPlayerProfile.pb",
		"attr_ArenaSeasonSettlementData.pb",
		"attr_ArenaSeasonStat.pb",
		"attr_ArenaSettings.pb",
		"attr_ArenaSevenDaysLoginActivity.pb",
		"attr_ArenaSprayPaintSetting.pb",
		"attr_ArenaStat.pb",
		"attr_ArenaStatKV.pb",
		"attr_ArenaTeamStatKV.pb",
		"attr_ArenaTipInfo.pb",
		"attr_AttrBirthdayBasicData.pb",
		"attr_AttrBirthdayBlessingRecord.pb",
		"attr_AttrBirthdayCardData.pb",
		"attr_AttrBirthdayCardItemData.pb",
		"attr_AttrBirthdayData.pb",
		"attr_AttrBirthdayOfficialWelfare.pb",
		"attr_AttrBirthdayRemindData.pb",
		"attr_AttrBirthdayToFriend.pb",
		"attr_AttrBirthdayToFriendData.pb",
		"attr_AttrClubInfo.pb",
		"attr_AttrCustomRoomInfo.pb",
		"attr_AttrDisplayBoardData.pb",
		"attr_AttrDisplayBoardInfo.pb",
		"attr_AttrDisplayBoardUgc.pb",
		"attr_AttrInflateRedPacketData.pb",
		"attr_AttrLevelUpChallenge.pb",
		"attr_AttrOnDueDateCardData.pb",
		"attr_AttRoomMemberClientInfo.pb",
		"attr_AttrPermitInfo.pb",
		"attr_AttrRecentActivity.pb",
		"attr_AttrRestaurantThemed.pb",
		"attr_AttrRewardRetrievalData.pb",
		"attr_AttrRewardRetrievalIdRedDot.pb",
		"attr_AttrRewardRetrievalInfo.pb",
		"attr_AttrRoomInfo.pb",
		"attr_AttrSceneInfo.pb",
		"attr_AttrTeamInfo.pb",
		"attr_AttrTreasureHunt.pb",
		"attr_AttrTwoPeopleSquadData.pb",
		"attr_AttrUgcMapMetaInfo.pb",
		"attr_BadGuysRedDot.pb",
		"attr_Bag.pb",
		"attr_BagInfoDb.pb",
		"attr_BannedItemInfo.pb",
		"attr_base.pb",
		"attr_BasicInfo.pb",
		"attr_BattleInfo.pb",
		"attr_BattleLevelRoundData.pb",
		"attr_BattleMiscInfo.pb",
		"attr_BattleModeData.pb",
		"attr_BattleResultData.pb",
		"attr_BattleSettlementMVPInfo.pb",
		"attr_BenefitCardInfo.pb",
		"attr_BindAccountInfo.pb",
		"attr_BirthdayInfo.pb",
		"attr_BlockCanSteal.pb",
		"attr_BlockFertilize.pb",
		"attr_BookOfFriends.pb",
		"attr_BookOfFriendsContract.pb",
		"attr_BookOfFriendsReport.pb",
		"attr_BookOfFriendsTaskInfo.pb",
		"attr_BookOfFriendsTypeInfo.pb",
		"attr_BPInfo.pb",
		"attr_BPPublicInfo.pb",
		"attr_BPTaskInfo.pb",
		"attr_BubbleConfig.pb",
		"attr_Building.pb",
		"attr_BuildingSkin.pb",
		"attr_BuyOrderInfo.pb",
		"attr_BuyRecordStruct.pb",
		"attr_CanStealTime.pb",
		"attr_CaptureShadowData.pb",
		"attr_CardCollectionInfo.pb",
		"attr_CardCollectionInfos.pb",
		"attr_CatFishingInfo.pb",
		"attr_ChallengeLevelinfo.pb",
		"attr_ChampionCountInfo.pb",
		"attr_ChangePlatMidasDiff.pb",
		"attr_ChaseDressItemInfo.pb",
		"attr_ChaseGameData.pb",
		"attr_ChaseGameDataNoPublic.pb",
		"attr_ChaseGameSettings.pb",
		"attr_ChaseIdentityBattlePerformance.pb",
		"attr_ChaseIdentityBattlePerformanceData.pb",
		"attr_ChaseIdentityBattlePerformanceDatas.pb",
		"attr_ChaseIdentityPerformance.pb",
		"attr_ChaseIdentityPerformances.pb",
		"attr_ChaseIdentityProficiencyData.pb",
		"attr_ChaseIdentityProficiencyInfo.pb",
		"attr_ChaseIdentityRankItem.pb",
		"attr_ChaseIdentitySpecialization.pb",
		"attr_ChaseIdentitySpecializationData.pb",
		"attr_ChaseMatchTypeBattleRecord.pb",
		"attr_ChaseSideStatics.pb",
		"attr_ChaseTaskResult.pb",
		"attr_ChatChannel.pb",
		"attr_ChatGroupKey.pb",
		"attr_ChatRedDots.pb",
		"attr_CheckerboardGridInfo.pb",
		"attr_CheckerboardInfo.pb",
		"attr_CheckInInfo.pb",
		"attr_CheckInPlanActivity.pb",
		"attr_ClearRedDotInfo.pb",
		"attr_ClientCache.pb",
		"attr_ClientLogColoring.pb",
		"attr_ClientPakInfo.pb",
		"attr_CloudNpc.pb",
		"attr_CloudTax.pb",
		"attr_ClubBriefData.pb",
		"attr_ClubChallengeActivity.pb",
		"attr_ClubIdentityData.pb",
		"attr_ClubInviteSendFrequencyAttr.pb",
		"attr_ClubNotifyJoinRankAttr.pb",
		"attr_ClubWeekSettleShareAttr.pb",
		"attr_CocActivityData.pb",
		"attr_CocActivityInfo.pb",
		"attr_CocBaseCampExtraInfo.pb",
		"attr_CocBattleBuildingAttackModeEntry.pb",
		"attr_CocBattleSummaryInfo.pb",
		"attr_CocBattleUuid.pb",
		"attr_CocBuilding.pb",
		"attr_CocBuildingAssignedVillagerInfo.pb",
		"attr_CocBuildingAssociatedVillagerInfo.pb",
		"attr_CocBuildingExtraInfo.pb",
		"attr_CocBuildingRevampInfo.pb",
		"attr_CocCardInfo.pb",
		"attr_CocCityBuildingTimeLimitQueue.pb",
		"attr_CocClientNumPropCacheInfo.pb",
		"attr_CocCurrentBattleInfo.pb",
		"attr_CocDataNumPropAttr.pb",
		"attr_CocDataNumPropIdAndSrcMap.pb",
		"attr_CocDataNumPropKeyFactor.pb",
		"attr_CocDataNumPropSrcFactor.pb",
		"attr_CocDefenceInfo.pb",
		"attr_CocDialogEntry.pb",
		"attr_CocDialogGroup.pb",
		"attr_CocDialogInfo.pb",
		"attr_CocFeatureInfo.pb",
		"attr_CocFeatureUnlock.pb",
		"attr_CocFightDeltaInfo.pb",
		"attr_CocFightInfo.pb",
		"attr_CocFightSettleSoldierInfo.pb",
		"attr_CocFriendDonationInfo.pb",
		"attr_CocFriendDonationRecord.pb",
		"attr_CocFriendGift.pb",
		"attr_CocFriendGiftInfo.pb",
		"attr_CocFriendInfo.pb",
		"attr_CocFriendVillager.pb",
		"attr_CocGoldenMineExtraInfo.pb",
		"attr_CocGoldenStoreExtraInfo.pb",
		"attr_CocHallOfVillagerExtraInfo.pb",
		"attr_CocHallOfVillagerStudentInfo.pb",
		"attr_CocHallOfVillagerTraineeInfo.pb",
		"attr_CocImpression.pb",
		"attr_CocImpressionItem.pb",
		"attr_CocItem.pb",
		"attr_CocItemInfo.pb",
		"attr_CocLevelDefenseCurGameInfo.pb",
		"attr_CocLevelDefenseFinishLevelInfo.pb",
		"attr_CocLevelDefenseInfo.pb",
		"attr_CocMatchInfo.pb",
		"attr_CocModeAttrSnap.pb",
		"attr_CocModeInfo.pb",
		"attr_CocMonthCardInfo.pb",
		"attr_CocNumPropItem.pb",
		"attr_CocOfflineBuildingUpgradedEventData.pb",
		"attr_CocOfflineDefenceBattleEventData.pb",
		"attr_CocOfflineEventData.pb",
		"attr_CocOfflineEventInfo.pb",
		"attr_CocOfflineEventItem.pb",
		"attr_CocOfflineProsperityValueChangeEventData.pb",
		"attr_CocPlayerBuildInfo.pb",
		"attr_CocPlayerCityBuildingInfo.pb",
		"attr_CocPlayerMapRegionInfo.pb",
		"attr_CocPresetSoldier.pb",
		"attr_CocPrisonExtraInfo.pb",
		"attr_CocPrisonWorkInfo.pb",
		"attr_CocProsperityInfo.pb",
		"attr_CocProsperityValueEntry.pb",
		"attr_CocPVECampaignMode.pb",
		"attr_CocPVEChallengeMode.pb",
		"attr_CocPVECurrentGameInfo.pb",
		"attr_CocPVEGameInfo.pb",
		"attr_CocPVEStageCommonInfo.pb",
		"attr_CocPVEStageStarInfo.pb",
		"attr_CocPVEStageStarUnit.pb",
		"attr_CocRankingScore.pb",
		"attr_CocRecoverPropAttr.pb",
		"attr_CocRecoverPropInfo.pb",
		"attr_CocResourceCollectRecord.pb",
		"attr_CocResourceInfo.pb",
		"attr_CocResourceUnit.pb",
		"attr_CocResProducerExtraInfo.pb",
		"attr_CocResStoreExtraInfo.pb",
		"attr_CocScience.pb",
		"attr_CocScienceInfo.pb",
		"attr_CocSingleImpressionValue.pb",
		"attr_CocSoldierInfo.pb",
		"attr_CocSoldierLevel.pb",
		"attr_CocSoldierUnit.pb",
		"attr_CocSourceImpression.pb",
		"attr_CocTask.pb",
		"attr_CocTaskCompleted.pb",
		"attr_CocTaskInfo.pb",
		"attr_CocTrainingHistory.pb",
		"attr_CocTrainingHistoryLevel.pb",
		"attr_CocTrainingHistoryRecord.pb",
		"attr_CocTrainingQueue.pb",
		"attr_CocTrainingSoldierUnit.pb",
		"attr_CocTreasureBox.pb",
		"attr_CocTreasureBoxInfo.pb",
		"attr_CocUserAttr.pb",
		"attr_CocUserBasicInfo.pb",
		"attr_CocVillager.pb",
		"attr_CocVillagerDialogueInfo.pb",
		"attr_CocVillagerDress.pb",
		"attr_CocVillagerInfo.pb",
		"attr_CocVillagerWaitOnCondition.pb",
		"attr_CocXingbaoInfo.pb",
		"attr_CocXingbaoUnit.pb",
		"attr_CoinInfo.pb",
		"attr_CollectionInfo.pb",
		"attr_CollectionInfos.pb",
		"attr_CombinationInteraction.pb",
		"attr_CombinationItems.pb",
		"attr_CombinationSettings.pb",
		"attr_CommodityInfo.pb",
		"attr_CommodityRedPoint.pb",
		"attr_CommonArrayList.pb",
		"attr_CommonConditionGroup.pb",
		"attr_CommonGiftBuyInfo.pb",
		"attr_CommonLimitInfo.pb",
		"attr_CommonMallInfo.pb",
		"attr_CommunityChannelHotTopicInfo.pb",
		"attr_CommunityChannelIconInfo.pb",
		"attr_CommunityChannelInfo.pb",
		"attr_CommunityEntryRewardInfo.pb",
		"attr_CompetitionBasicInfo.pb",
		"attr_CompetitionWarmUpActivityAttr.pb",
		"attr_CompetitionWarmUpStageAttr.pb",
		"attr_ConanIpActiveData.pb",
		"attr_ConanWarmupActivityData.pb",
		"attr_Condition.pb",
		"attr_ConditionGroup.pb",
		"attr_ConditionGroupInfo.pb",
		"attr_CookAllGreeter.pb",
		"attr_CookAttr.pb",
		"attr_CookBasicInfo.pb",
		"attr_CookBusinessInfo.pb",
		"attr_CookCandidate.pb",
		"attr_CookCommentInfo.pb",
		"attr_CookCommentNumberInfo.pb",
		"attr_CookCommentPhotoNameIdInfo.pb",
		"attr_CookCommentReplyRecord.pb",
		"attr_CookCommentScoreInfo.pb",
		"attr_CookCustomer.pb",
		"attr_CookCustomerInfo.pb",
		"attr_CookDiningTable.pb",
		"attr_CookEmployeeAbility.pb",
		"attr_CookEmployeeBasicInfo.pb",
		"attr_CookEmployeeJobInfo.pb",
		"attr_CookEmployer.pb",
		"attr_CookFloatingScreenInfo.pb",
		"attr_CookFriendEmployeeInfo.pb",
		"attr_CookGreeter.pb",
		"attr_CookHiredHistory.pb",
		"attr_CookHiredHistoryItem.pb",
		"attr_CookHiredInfo.pb",
		"attr_CookHireFriendsHistoryItem.pb",
		"attr_CookInfo.pb",
		"attr_CookOfflineIncomeInfo.pb",
		"attr_CookOwnerInfo.pb",
		"attr_CookPublicInfo.pb",
		"attr_CookPunish.pb",
		"attr_CookRecruitmentMarket.pb",
		"attr_CookRecruitmentMarketRefreshProtect.pb",
		"attr_CookSafeInfo.pb",
		"attr_CookStaffBasic.pb",
		"attr_CookStaffInfo.pb",
		"attr_CookSysEmployee.pb",
		"attr_CookSysEmployeeInfo.pb",
		"attr_CookVisitant.pb",
		"attr_CookVisitantCurrentGroupInfo.pb",
		"attr_CookVisitantInfo.pb",
		"attr_CookVisitantPrebookInfo.pb",
		"attr_CookVisitantStealInfo.pb",
		"attr_CookWorker.pb",
		"attr_CookWorkerAbility.pb",
		"attr_CookWorkerCandidate.pb",
		"attr_CookWorkerCandidateInfo.pb",
		"attr_CookWorkerInfo.pb",
		"attr_CosImage.pb",
		"attr_Crop.pb",
		"attr_CropCanWaterInterval.pb",
		"attr_CropClientInfo.pb",
		"attr_CropFertilizeGuaranteed.pb",
		"attr_CropInfo.pb",
		"attr_CropLevel.pb",
		"attr_CropMachineContain.pb",
		"attr_CropMachineInfo.pb",
		"attr_CropMagicInfo.pb",
		"attr_CropMonthlyPassInfo.pb",
		"attr_CropWeatherInfo.pb",
		"attr_CupsInfo.pb",
		"attr_CupsRedClick.pb",
		"attr_DailyIntimacyData.pb",
		"attr_DailySumBuyTimesInfo.pb",
		"attr_DailyVictoryRecord.pb",
		"attr_DanceData.pb",
		"attr_DanceOutfitData.pb",
		"attr_DBAchievementInfo.pb",
		"attr_DBApplyRelation.pb",
		"attr_DBPlayerHotData.pb",
		"attr_DBRelation.pb",
		"attr_DBRemoveInfo.pb",
		"attr_DBStepInfo.pb",
		"attr_DepositFutureItem.pb",
		"attr_DepositGift.pb",
		"attr_DepositSnapshot.pb",
		"attr_DfStoreFreeTimes.pb",
		"attr_DfUserDBInfo.pb",
		"attr_Dish.pb",
		"attr_DishLevel.pb",
		"attr_DisplayBoardHistoryData.pb",
		"attr_DoGoodersRedDot.pb",
		"attr_DoubleDiamondActivityData.pb",
		"attr_DoubleDiamondLevelData.pb",
		"attr_DressInfo.pb",
		"attr_DressItemInfo.pb",
		"attr_DressOutfitCollectHistory.pb",
		"attr_DressUpDetailInfo.pb",
		"attr_DropData.pb",
		"attr_DropItemList.pb",
		"attr_DsCommonDbInfoResult.pb",
		"attr_DSItem.pb",
		"attr_DSOwnerBackpackInfo.pb",
		"attr_DSUserBackpackInfo.pb",
		"attr_DsUserDBInfo.pb",
		"attr_DsUserDBInfoUnion.pb",
		"attr_ElvesData.pb",
		"attr_EntertainmentGuide.pb",
		"attr_EntertainmentGuideTask.pb",
		"attr_EntertainmentMatchType.pb",
		"attr_EntertainmentShop.pb",
		"attr_EntityProfileInfo.pb",
		"attr_EquipCollections.pb",
		"attr_EquipDressInfo.pb",
		"attr_EquipItemInfo.pb",
		"attr_EquipPosInfo.pb",
		"attr_EvictRecord.pb",
		"attr_ExchangeCenter.pb",
		"attr_ExchangePair.pb",
		"attr_ExtCardBagOutPutData.pb",
		"attr_Farm.pb",
		"attr_FarmActivityOpenTime.pb",
		"attr_FarmAnwserData.pb",
		"attr_FarmAquarium.pb",
		"attr_FarmAquariumSeat.pb",
		"attr_FarmAquariumTag.pb",
		"attr_FarmAttr.pb",
		"attr_FarmBasicInfo.pb",
		"attr_FarmBlockedFriend.pb",
		"attr_FarmBlockFriend.pb",
		"attr_FarmBuff.pb",
		"attr_FarmBuffEffect.pb",
		"attr_FarmBuffInfo.pb",
		"attr_FarmBuffList.pb",
		"attr_FarmBuffs.pb",
		"attr_FarmBuffWish.pb",
		"attr_FarmBuildingInfo.pb",
		"attr_FarmBuildingObtainExpRecord.pb",
		"attr_FarmBuildingObtainMainExpRecord.pb",
		"attr_FarmClientCache.pb",
		"attr_FarmCloudmarket.pb",
		"attr_FarmCollection.pb",
		"attr_FarmCollectionHandbook.pb",
		"attr_FarmCookViewInfo.pb",
		"attr_FarmCropStatisticInfo.pb",
		"attr_FarmCustomData.pb",
		"attr_FarmDailyAwardActivity.pb",
		"attr_FarmDragonData.pb",
		"attr_FarmEvent.pb",
		"attr_FarmEventCD.pb",
		"attr_FarmEventHistory.pb",
		"attr_FarmEventInfo.pb",
		"attr_FarmEventSeriesStep.pb",
		"attr_FarmEventSync.pb",
		"attr_FarmEventValue.pb",
		"attr_FarmEvictInfo.pb",
		"attr_FarmEvtNotTrigger.pb",
		"attr_FarmEvtSelfCD.pb",
		"attr_FarmEvtSharedCD.pb",
		"attr_FarmExternalOperateInfo.pb",
		"attr_FarmFishBowl.pb",
		"attr_FarmFishCardLevel.pb",
		"attr_FarmFishingGuaranteedInfo.pb",
		"attr_FarmFishingInfo.pb",
		"attr_FarmFishingRecord.pb",
		"attr_FarmFishingReport.pb",
		"attr_FarmFishingReportUnit.pb",
		"attr_FarmFishingReportUnitByBait.pb",
		"attr_FarmFishingReportUnitByLayer.pb",
		"attr_FarmFishPoolInfo.pb",
		"attr_FarmGift.pb",
		"attr_FarmGiftHeadFrame.pb",
		"attr_FarmGiftList.pb",
		"attr_FarmGiftRecord.pb",
		"attr_FarmGiftReserveFlag.pb",
		"attr_FarmGodFigure.pb",
		"attr_FarmHandBook.pb",
		"attr_FarmHandBookRecord.pb",
		"attr_FarmHotBuffHistory.pb",
		"attr_FarmHotInfo.pb",
		"attr_FarmHotSpring.pb",
		"attr_FarmHotSpringBuffSource.pb",
		"attr_FarmHotSpringVisitor.pb",
		"attr_FarmHotValue.pb",
		"attr_FarmHotValueStatisticInfo.pb",
		"attr_FarmInfo.pb",
		"attr_FarmingInfo.pb",
		"attr_FarmItem.pb",
		"attr_FarmItemInfo.pb",
		"attr_FarmKirin.pb",
		"attr_FarmLeavingVillager.pb",
		"attr_FarmLiuYanMessageInfo.pb",
		"attr_FarmLiuYanMessageRecord.pb",
		"attr_FarmMagic.pb",
		"attr_FarmMagicExInfo.pb",
		"attr_FarmMagicInfo.pb",
		"attr_FarmMagicMpInfo.pb",
		"attr_FarmModuleOpenTime.pb",
		"attr_FarmModuleOpenTimeInfo.pb",
		"attr_FarmMoney.pb",
		"attr_FarmMonthlyPass.pb",
		"attr_FarmNewPlayerInfo.pb",
		"attr_FarmNewPlayerSpeedCropRecord.pb",
		"attr_FarmObtainMainExpRecord.pb",
		"attr_FarmOwnerInfo.pb",
		"attr_FarmPartyInfo.pb",
		"attr_FarmPartyPublicInfo.pb",
		"attr_FarmPartyTlogRequiredFields.pb",
		"attr_FarmPet.pb",
		"attr_FarmPetInfo.pb",
		"attr_FarmPinFriend.pb",
		"attr_FarmPlayerLiuYanMessageInfo.pb",
		"attr_FarmPublicInfo.pb",
		"attr_FarmPunish.pb",
		"attr_FarmReport.pb",
		"attr_FarmReportData.pb",
		"attr_FarmReportInfo.pb",
		"attr_FarmReportUnit.pb",
		"attr_FarmReturningInfo.pb",
		"attr_FarmReturningTask.pb",
		"attr_FarmRoom.pb",
		"attr_FarmRoomExtendInfo.pb",
		"attr_FarmRoomGrid.pb",
		"attr_FarmRoomItem.pb",
		"attr_FarmSafeInfo.pb",
		"attr_FarmSceneDrop.pb",
		"attr_FarmSceneDropFarmItem.pb",
		"attr_FarmSceneDropInfo.pb",
		"attr_FarmSceneDropInHousePos.pb",
		"attr_FarmSendHistory.pb",
		"attr_FarmSignature.pb",
		"attr_FarmSignatureRead.pb",
		"attr_FarmSocialInfo.pb",
		"attr_FarmStatisticInfo.pb",
		"attr_FarmStatusViewInfo.pb",
		"attr_FarmStealingInfo.pb",
		"attr_FarmStealReportUnit.pb",
		"attr_FarmTalentInfo.pb",
		"attr_FarmTalentUnit.pb",
		"attr_FarmTask.pb",
		"attr_FarmTaskConditionInfo.pb",
		"attr_FarmTaskFinish.pb",
		"attr_FarmTaskInfo.pb",
		"attr_FarmTaskTimeInfo.pb",
		"attr_FarmTimer.pb",
		"attr_FarmTlogRequiredFields.pb",
		"attr_FarmVillager.pb",
		"attr_FarmVillagerInfo.pb",
		"attr_FarmWaitingVillager.pb",
		"attr_FarmWeatherInfo.pb",
		"attr_FarmWeekendInfo.pb",
		"attr_FarmWelcomeInfo.pb",
		"attr_FarmWildCat.pb",
		"attr_FashionFundData.pb",
		"attr_FeatureIntegrationData.pb",
		"attr_FertilizeHistory.pb",
		"attr_FindPartnerData.pb",
		"attr_FindPartnerQuestionAnswer.pb",
		"attr_FireworksInfo.pb",
		"attr_FishingActivityRecord.pb",
		"attr_FishingActivityRecordOfRank.pb",
		"attr_FishingFameActivityData.pb",
		"attr_FishingFameRecord.pb",
		"attr_FishingHallOfFameActivityData.pb",
		"attr_FishPoolPlantInfo.pb",
		"attr_FishPoolProduceInfo.pb",
		"attr_FishPoolRipeInfo.pb",
		"attr_FishPoolStealInfo.pb",
		"attr_FishPriceRecord.pb",
		"attr_FittingSlot.pb",
		"attr_FittingSlots.pb",
		"attr_FlashRaceCheeringData.pb",
		"attr_FlyingChessActivityInfo.pb",
		"attr_FlyingChessActivityPosInfo.pb",
		"attr_FoodFestivalData.pb",
		"attr_FpsSettings.pb",
		"attr_FpsWeaponUnLockInfo.pb",
		"attr_FpsWeaponUnLockUserDBInfo.pb",
		"attr_FriendInteractAttr.pb",
		"attr_FriendInteractDetailAttr.pb",
		"attr_FriendRecommendInfo.pb",
		"attr_FrozenCardInfo.pb",
		"attr_FunctionControl.pb",
		"attr_FurnitureTimeInfo.pb",
		"attr_GameGearSettings.pb",
		"attr_GameModeReturnCompleteRecord.pb",
		"attr_GameModeReturnData.pb",
		"attr_GameModeReturnRunningData.pb",
		"attr_GameModeReturnTriggerTag.pb",
		"attr_GameplayAddItemBillNo.pb",
		"attr_GameplayEventSerialId.pb",
		"attr_GameplayItem.pb",
		"attr_GameplayItemSet.pb",
		"attr_GameProtectionSpecificSwitch.pb",
		"attr_GameTimesData.pb",
		"attr_GameTimesStatData.pb",
		"attr_GameTimesStatistics.pb",
		"attr_GameTvInfo.pb",
		"attr_GameTypeDataItem.pb",
		"attr_GeneralRedDotInfo.pb",
		"attr_GiftPackageRandomRecord.pb",
		"attr_GiftRandomGroupInfo.pb",
		"attr_GiftRandomIndexInfo.pb",
		"attr_GoodReward.pb",
		"attr_Grid.pb",
		"attr_GroupingReturnData.pb",
		"attr_GuidedDiscoverInfo.pb",
		"attr_GuideInfo.pb",
		"attr_GuideStatistics.pb",
		"attr_GuideTaskExtraInfo.pb",
		"attr_HalfYearNavigationActivity.pb",
		"attr_HalfYearNavigationDayInfo.pb",
		"attr_HalfYearWarmUpActivity.pb",
		"attr_HalfYearWarmUpDayInfo.pb",
		"attr_HarvestItem.pb",
		"attr_HeatDetail.pb",
		"attr_HideFashionScoreSetting.pb",
		"attr_HistoryFestivalGift.pb",
		"attr_HistoryItemTypeInfo.pb",
		"attr_HistoryItemUsedInfo.pb",
		"attr_HOKABTestInfo.pb",
		"attr_HOKAIInviteDailyData.pb",
		"attr_HOKAIInviteInfo.pb",
		"attr_HOKAIInviteShowInviteData.pb",
		"attr_HOKAttrInfo.pb",
		"attr_HOKExpandSetting.pb",
		"attr_HOKMobaTagInfo.pb",
		"attr_HOKSettings.pb",
		"attr_HOKXiaoBaiWarmRoundABTestInfo.pb",
		"attr_HomePageActionInfo.pb",
		"attr_HomePageActionShowInfo.pb",
		"attr_HotTopicInfo.pb",
		"attr_HourChargeInfo.pb",
		"attr_HouseAttr.pb",
		"attr_HouseBasicInfo.pb",
		"attr_HouseClientCache.pb",
		"attr_HouseFixTagInfo.pb",
		"attr_HouseInfo.pb",
		"attr_HouseInteractInfo.pb",
		"attr_HouseItemInfo.pb",
		"attr_HouseItemInteract.pb",
		"attr_HouseItemMessage.pb",
		"attr_HouseItemStatistics.pb",
		"attr_HouseLayoutInfo.pb",
		"attr_HouseOwnerInfo.pb",
		"attr_HousePublicInfo.pb",
		"attr_HousePunish.pb",
		"attr_HouseSafeInfo.pb",
		"attr_HSBuffHistory.pb",
		"attr_HYNCheckInInfo.pb",
		"attr_IdcNetworkInfoDb.pb",
		"attr_IdcNetworkRecordDb.pb",
		"attr_IdentityQualifyingSeasonInfo.pb",
		"attr_IdipStatistics.pb",
		"attr_IdipTask.pb",
		"attr_IdipTaskInfo.pb",
		"attr_InflateRedPacketMemberData.pb",
		"attr_InflateRedPacketMoney.pb",
		"attr_InheritMsg.pb",
		"attr_Int32Map.pb",
		"attr_Int32Set.pb",
		"attr_IntellectualActivity.pb",
		"attr_IntelligenceStationData.pb",
		"attr_InterActAddFavor.pb",
		"attr_Interaction.pb",
		"attr_InteractionInfo.pb",
		"attr_InterServerGiftActivity.pb",
		"attr_IntimatePlayerInfo.pb",
		"attr_IntimateRelationGuideAttr.pb",
		"attr_IntimateRelationGuideDetail.pb",
		"attr_IntimateRelationInfo.pb",
		"attr_IntimateRelationMotionAttr.pb",
		"attr_IntimateRelationOnlineNoticeAttr.pb",
		"attr_IntimateRelationOnlineNoticeDetailAttr.pb",
		"attr_IntimateRelationOnlineNoticeDressItemAttr.pb",
		"attr_IntimateRelationOnlineNoticeSingleDressItem.pb",
		"attr_InviteeInfo.pb",
		"attr_ItaBagInfo.pb",
		"attr_Item.pb",
		"attr_ItemChangeRecord.pb",
		"attr_ItemDetailInfo.pb",
		"attr_ItemInfoDb.pb",
		"attr_ItemInteract.pb",
		"attr_ItemLock.pb",
		"attr_ItemPackageLimit.pb",
		"attr_ItemShowStatus.pb",
		"attr_KeyValStr.pb",
		"attr_KungFuPandaData.pb",
		"attr_KungFuPandaHelpData.pb",
		"attr_KvFI.pb",
		"attr_KvIF.pb",
		"attr_KvII.pb",
		"attr_KvIL.pb",
		"attr_KvLL.pb",
		"attr_KvLStr.pb",
		"attr_KvSS.pb",
		"attr_LabelInfo.pb",
		"attr_LastDressOutLookInfo.pb",
		"attr_LayoutIDInfo.pb",
		"attr_LayoutInfo.pb",
		"attr_LetsGoSpecPlayerPublicInfo.pb",
		"attr_LevelAchievementInfo.pb",
		"attr_LevelIllustration.pb",
		"attr_LevelInfo.pb",
		"attr_LevelRecord.pb",
		"attr_LevelRecordByPlay.pb",
		"attr_LevelRecordData.pb",
		"attr_LevelRecordDetailItem.pb",
		"attr_LevelRecordDetails.pb",
		"attr_LevelScore.pb",
		"attr_LimitConditionGroup.pb",
		"attr_LimitInfoStruct.pb",
		"attr_LimitTimeExperienceItem.pb",
		"attr_LiuYanMessageRecord.pb",
		"attr_LiveLinkAttrInfo.pb",
		"attr_LobbyChangeColorInfo.pb",
		"attr_LobbyEggFindKindCountInfo.pb",
		"attr_LobbyInfo.pb",
		"attr_LobbyLobbyMatchHistoryInfo.pb",
		"attr_LobbyMatchInfo.pb",
		"attr_LobbyNpcInfo.pb",
		"attr_LobbyTaskInfo.pb",
		"attr_LockChar.pb",
		"attr_LoginFeatureData.pb",
		"attr_LotteryData.pb",
		"attr_LotteryDrawActivity.pb",
		"attr_LotteryDrawActivityRewardInfo.pb",
		"attr_LotteryDrawInfo.pb",
		"attr_LuckyBalloonData.pb",
		"attr_LuckyBalloonItem.pb",
		"attr_LuckyFriendApplyAttr.pb",
		"attr_LuckyFriendAttr.pb",
		"attr_LuckyFriendTaskAttr.pb",
		"attr_LuckyMoneyActivity.pb",
		"attr_LuckyMoneyInfo.pb",
		"attr_LuckyRebateActivityData.pb",
		"attr_LuckyStarActivity.pb",
		"attr_LuckyStarInfo.pb",
		"attr_LuckyTurntableActivityData.pb",
		"attr_LuckyTurntableRoundInfo.pb",
		"attr_MailCache.pb",
		"attr_MainCups.pb",
		"attr_MainCupsCycle.pb",
		"attr_MainMasterInfo.pb",
		"attr_MainMasterPath.pb",
		"attr_MallDemandInfo.pb",
		"attr_MallGiftCard.pb",
		"attr_MallGiveRecord.pb",
		"attr_MallInfo.pb",
		"attr_MallRedPoint.pb",
		"attr_MallRedPointStatus.pb",
		"attr_MallWishCommodity.pb",
		"attr_MallWishListPublic.pb",
		"attr_MapEntityPublicInfo.pb",
		"attr_MapStarPShop.pb",
		"attr_MarqueeNoticeInfo.pb",
		"attr_MarqueNoticeInfoItem.pb",
		"attr_MasterPatchData.pb",
		"attr_MasterPathData.pb",
		"attr_MatchIsolateInfoDb.pb",
		"attr_MatchStaticsDb.pb",
		"attr_MatchTypeHistory.pb",
		"attr_MatchUnlockInfo.pb",
		"attr_MaydayUserDBInfo.pb",
		"attr_MaydayUserInfIdentityInfo.pb",
		"attr_MaydayUserInfPosInfo.pb",
		"attr_MaydayUserInfRoomInfo.pb",
		"attr_Menu.pb",
		"attr_MessageSlipRedDotInfo.pb",
		"attr_MidasChargeInfo.pb",
		"attr_MidasLockInfo.pb",
		"attr_MidasPresentFailOrder.pb",
		"attr_MinesweeperActivity.pb",
		"attr_MMRScoreDb.pb",
		"attr_MMRScoresInfoDb.pb",
		"attr_MobaChallengeData.pb",
		"attr_MobaRandomVote.pb",
		"attr_MobaSquadDrawRedPacketDailyTaskRecord.pb",
		"attr_MobaSquadDrawRedPacketData.pb",
		"attr_MobaSquadDrawRedPacketSquadInfo.pb",
		"attr_MobaSquadDrawRedPacketSquadTask.pb",
		"attr_ModData.pb",
		"attr_ModeCups.pb",
		"attr_ModNote.pb",
		"attr_ModPublicInfo.pb",
		"attr_ModSettings.pb",
		"attr_ModuleRedDotInfo.pb",
		"attr_Money.pb",
		"attr_MoneyTree.pb",
		"attr_MoneyTreeDropItems.pb",
		"attr_MoneyTreeHistory.pb",
		"attr_MonopolyActivityData.pb",
		"attr_MonopolyCumulativeData.pb",
		"attr_MonopolyDrawData.pb",
		"attr_MonthCardInfo.pb",
		"attr_MultiPlayerSquadInfo.pb",
		"attr_MusicOrderActivityAttr.pb",
		"attr_MusicOrderDailyInfo.pb",
		"attr_MusicOrderModeNoDropInfo.pb",
		"attr_MusicOrderNoteInfo.pb",
		"attr_MusicOrderOrderInfo.pb",
		"attr_MusicOrderResetCountInfo.pb",
		"attr_MyCookInfo.pb",
		"attr_MyFarmInfo.pb",
		"attr_MyHouseInfo.pb",
		"attr_MyRichBoardInfo.pb",
		"attr_MyXiaoWoInfo.pb",
		"attr_NewbieNPCFarmInfo.pb",
		"attr_NewbieTaskInfo.pb",
		"attr_NewCardCollectionRedDot.pb",
		"attr_NewYearPilotInfo.pb",
		"attr_NewYearSign.pb",
		"attr_NicknameCheckNote.pb",
		"attr_NpcFarmBasicInfo.pb",
		"attr_NpcFarmInfo.pb",
		"attr_NpcFarmSchedule.pb",
		"attr_NR3E8DBTaskInfo.pb",
		"attr_NR3E8RichInfo.pb",
		"attr_NR3E8TaskInfo.pb",
		"attr_NR3E8WeekActivityInfo.pb",
		"attr_OneDollarRaffleData.pb",
		"attr_OptionalRewardInfo.pb",
		"attr_OutfitHistoryData.pb",
		"attr_P2PChatGroup.pb",
		"attr_P2PChatInfo.pb",
		"attr_PaidUnlockActivityData.pb",
		"attr_PakDownloadInfo.pb",
		"attr_PartyInfo.pb",
		"attr_PasswordCodeData.pb",
		"attr_PayOrderInfo.pb",
		"attr_PersonalityStateInfo.pb",
		"attr_PetClientCache.pb",
		"attr_PetClothing.pb",
		"attr_PetClothingInfo.pb",
		"attr_PetEvictRedDot.pb",
		"attr_PetFavorInfo.pb",
		"attr_PetFeedRedDot.pb",
		"attr_PetFertilizeInfo.pb",
		"attr_PetGiftInfo.pb",
		"attr_PetGiftItem.pb",
		"attr_PetOwnedClothing.pb",
		"attr_PetSecurityInfo.pb",
		"attr_PetWearClothing.pb",
		"attr_PickFactionActivityData.pb",
		"attr_PickFactionRecord.pb",
		"attr_PicLikeCountInfo.pb",
		"attr_PicLikeHisInfo.pb",
		"attr_PicLikeInfo.pb",
		"attr_PixuiRedDotInfo.pb",
		"attr_PlacedObjectPosition.pb",
		"attr_Placeholder.pb",
		"attr_PlaceholderUnit.pb",
		"attr_PlatPrivilegesInfo.pb",
		"attr_PlayerBlessBagInfo.pb",
		"attr_PlayerClubChallengeBattleInfo.pb",
		"attr_PlayerClubChallengeReportInfo.pb",
		"attr_PlayerClubRecord.pb",
		"attr_PlayerDepositInfo.pb",
		"attr_PlayerEventData.pb",
		"attr_PlayerGameActionInfos.pb",
		"attr_PlayerGamePlay.pb",
		"attr_PlayerGameTime.pb",
		"attr_PlayerGrayRuleData.pb",
		"attr_PlayerGrayTagInfo.pb",
		"attr_PlayerGrayTagsInfo.pb",
		"attr_PlayerHugOther.pb",
		"attr_PlayerHugOthersInfo.pb",
		"attr_PlayerIAAData.pb",
		"attr_PlayerIAAInfo.pb",
		"attr_PlayerIAAStat.pb",
		"attr_PlayerIdipInfo.pb",
		"attr_PlayerLevelEstimation.pb",
		"attr_PlayerPakPlayRecord.pb",
		"attr_PlayerProfileInfo.pb",
		"attr_PlayerPublicBasicInfo.pb",
		"attr_PlayerPublicBattleInfo.pb",
		"attr_PlayerPublicEquipments.pb",
		"attr_PlayerPublicFriendData.pb",
		"attr_PlayerPublicGameData.pb",
		"attr_PlayerPublicGameSettings.pb",
		"attr_PlayerPublicHistoryData.pb",
		"attr_PlayerPublicLiveStatus.pb",
		"attr_PlayerPublicProfileInfo.pb",
		"attr_PlayerPublicSceneData.pb",
		"attr_PlayerPublicSummaryInfo.pb",
		"attr_PlayerPushTopic.pb",
		"attr_PlayerRaffleGroupInfo.pb",
		"attr_PlayerRaffleInfo.pb",
		"attr_PlayerRankData.pb",
		"attr_PlayerRankGeoInfo.pb",
		"attr_PlayerRankSettlement.pb",
		"attr_PlayerRecruiteInfo.pb",
		"attr_PlayerSnsAcceptedRecord.pb",
		"attr_PlayerSnsAcceptInfo.pb",
		"attr_PlayerSnsInvitationActivityData.pb",
		"attr_PlayerSnsInvitationBrief.pb",
		"attr_PlayerSnsInvitationData.pb",
		"attr_PlayerSnsInvitationInfo.pb",
		"attr_PlayerSnsInvitationRaffleData.pb",
		"attr_PlayerStatisticInfo.pb",
		"attr_PlayerStatusDetail.pb",
		"attr_PlayerStatusDetails.pb",
		"attr_PlayerUgcLobbyInfo.pb",
		"attr_PlayTopicTaskInfo.pb",
		"attr_PositionCollections.pb",
		"attr_PrayerCardActivityData.pb",
		"attr_PrayerCardFriendDailyGive.pb",
		"attr_PrayerCardGiveRecord.pb",
		"attr_PrayerCardInfo.pb",
		"attr_PrayerCardRewardActivityData.pb",
		"attr_PrayerCardRewardItemInfo.pb",
		"attr_PrayInfo.pb",
		"attr_PublicChatInfo.pb",
		"attr_PublicCookInfo.pb",
		"attr_PublicData.pb",
		"attr_PublicFarmInfo.pb",
		"attr_PublicHouseInfo.pb",
		"attr_PublicLobbyInfo.pb",
		"attr_PublicRichInfo.pb",
		"attr_PublicXiaoWoInfo.pb",
		"attr_PushTopicHandle.pb",
		"attr_QAInvestInfo.pb",
		"attr_QAInvestTag.pb",
		"attr_QingShuangTrialData.pb",
		"attr_QQApplicationInfo.pb",
		"attr_QQTeamInfo.pb",
		"attr_QualifyingBattleRecord.pb",
		"attr_QualifyingDailyRankInfo.pb",
		"attr_QualifyingDetailInfo.pb",
		"attr_QualifyingExtraIntegralInfo.pb",
		"attr_QualifyingInfo.pb",
		"attr_QualifyingTypeInfo.pb",
		"attr_QuizData.pb",
		"attr_QuizQuestionData.pb",
		"attr_RacingRankRewardRecord.pb",
		"attr_RaffleBIDiscount.pb",
		"attr_RaffleBISeq.pb",
		"attr_RaffleCommonCost.pb",
		"attr_RaffleCommonData.pb",
		"attr_RaffleCommonInfo.pb",
		"attr_RaffleCommonKeyVal.pb",
		"attr_RaffleCost.pb",
		"attr_RaffleFreeDiscount.pb",
		"attr_RaffleFreeTicket.pb",
		"attr_RaffleGuaranteeRecord.pb",
		"attr_RaffleInventoryItem.pb",
		"attr_RafflePoolCommonInfo.pb",
		"attr_RafflePoolGuaranteeCounter.pb",
		"attr_RafflePoolGuaranteeCounters.pb",
		"attr_RafflePoolPurchaseCounter.pb",
		"attr_RafflePoolPurchaseCounters.pb",
		"attr_RafflePoolStash.pb",
		"attr_RafflePurchaseCommonInfo.pb",
		"attr_RafflePurchaseRecord.pb",
		"attr_RaffleRewardCommonInfo.pb",
		"attr_RaffleRewardGroupCommonInfo.pb",
		"attr_RaffleRewardGroupRecord.pb",
		"attr_RaffleRewardRecord.pb",
		"attr_RaffleRewardStashItem.pb",
		"attr_RaffleRewardSubItem.pb",
		"attr_RaffleRewardSubstitute.pb",
		"attr_RaffleSlot.pb",
		"attr_RaffleStash.pb",
		"attr_RandomCollections.pb",
		"attr_RankHistory.pb",
		"attr_RankHistoryItem.pb",
		"attr_RankInfoItem.pb",
		"attr_RankInfoReportStatus.pb",
		"attr_RankInfoSubItem.pb",
		"attr_RecentIntimacyData.pb",
		"attr_RechargeInfo.pb",
		"attr_RecommendFriendInfo.pb",
		"attr_RecommendMatchTask.pb",
		"attr_RecommendMatchType.pb",
		"attr_RecommendMatchTypeTask.pb",
		"attr_RecommendRefreshInfo.pb",
		"attr_RecruiteEntry.pb",
		"attr_RecruiteRaffleInfo.pb",
		"attr_RecScenePackageInfo.pb",
		"attr_RedEnvelopeRainActInfo.pb",
		"attr_RedEnvelopeRainActivities.pb",
		"attr_RedEnvelopeRainReceivedCnt.pb",
		"attr_RedEnvelopRainShareInfo.pb",
		"attr_RedPacket.pb",
		"attr_RedPacketActivity.pb",
		"attr_RedPacketRecvDetail.pb",
		"attr_RedPacketSentDetail.pb",
		"attr_RedPacketSentRecvDetail.pb",
		"attr_RelationInfo.pb",
		"attr_RelationMapInfo.pb",
		"attr_RelationPlayerNameData.pb",
		"attr_RestaurantThemedFood.pb",
		"attr_RestaurantThemedNpc.pb",
		"attr_RestaurantThemedReceiveHistory.pb",
		"attr_RestaurantThemedReceiveRecord.pb",
		"attr_RestaurantThemedShareRecord.pb",
		"attr_ReturnActivity.pb",
		"attr_ReturnActivityDailyReward.pb",
		"attr_ReturnActivityPrivilege.pb",
		"attr_ReturnActivityRewardOptionalTask.pb",
		"attr_ReturningActInfo.pb",
		"attr_ReturningInfoDb.pb",
		"attr_ReturnTaskInfo.pb",
		"attr_RewardCompensateInfo.pb",
		"attr_RewardCompensateItem.pb",
		"attr_RewardCompensateTask.pb",
		"attr_RewardCompensateTaskStatus.pb",
		"attr_RewardComponent.pb",
		"attr_RewardCostItem.pb",
		"attr_RewardGiftHistory.pb",
		"attr_RewardItemInfo.pb",
		"attr_RewardNtfInfo.pb",
		"attr_RewardStatusInfo.pb",
		"attr_RoguelikeCommonProp.pb",
		"attr_RoguelikeCustomQQMusicInfo.pb",
		"attr_RoguelikeEndLessSaveInfo.pb",
		"attr_RoguelikeEndlessSavePropsInfo.pb",
		"attr_RoguelikeEndlessSaveSpecialBuffInfo.pb",
		"attr_RoguelikeExtraInfo.pb",
		"attr_RoguelikeGetPropsData.pb",
		"attr_RoguelikeInfo.pb",
		"attr_RoguelikeKillMonsterData.pb",
		"attr_RoguelikeMark.pb",
		"attr_RoguelikeMarkBoard.pb",
		"attr_RoguelikeMarkData.pb",
		"attr_RoguelikeMarkEntry.pb",
		"attr_RoguelikeMarkInBoardInfo.pb",
		"attr_RoguelikeMonsterInfo.pb",
		"attr_RoguelikePassLevelInfo.pb",
		"attr_RoguelikePropInfo.pb",
		"attr_RoguelikeRankData.pb",
		"attr_RoguelikeRankRecord.pb",
		"attr_RoguelikeSeasonData.pb",
		"attr_RoguelikeTalentData.pb",
		"attr_RoguelikeTask.pb",
		"attr_RoguelikeTaskData.pb",
		"attr_RoguelikeUnlockTalent.pb",
		"attr_RoguelikeUserDBInfo.pb",
		"attr_RoguelikeUseSkillData.pb",
		"attr_RoguelikeWeaponDamageData.pb",
		"attr_RoguelikeWeaponInfo.pb",
		"attr_RoleChatInfo.pb",
		"attr_RoomExtraInfo.pb",
		"attr_RootAttr.pb",
		"attr_SafetyCheck.pb",
		"attr_SceneCollection.pb",
		"attr_SceneCustomData.pb",
		"attr_SceneEntityAttr.pb",
		"attr_SceneEntityCommon.pb",
		"attr_SceneEntityData.pb",
		"attr_SceneEntityPosition.pb",
		"attr_SceneEntityPublicInfo.pb",
		"attr_SceneInfo.pb",
		"attr_SceneInteractActionInfo.pb",
		"attr_ScenePackageInfo.pb",
		"attr_ScenePlayerBasicInfo.pb",
		"attr_ScenePlayerPublicInfo.pb",
		"attr_ScoreGuideData.pb",
		"attr_ScratchOffTicketInfo.pb",
		"attr_ScratchOffTicketsActivity.pb",
		"attr_SeasonFashion.pb",
		"attr_SeasonFashionBattleData.pb",
		"attr_SeasonFashionBattleDataDetail.pb",
		"attr_SeasonFashionEquipBook.pb",
		"attr_SeasonFashionQualifyMaxDegreeInfo.pb",
		"attr_SeasonInfo.pb",
		"attr_SeasonMail.pb",
		"attr_SeasonModeCups.pb",
		"attr_SeasonReview.pb",
		"attr_SeasonReviewEventProgressData.pb",
		"attr_SeasonReviewRedPointInfo.pb",
		"attr_SecondaryPassword.pb",
		"attr_SelfFavourInfo.pb",
		"attr_SelfMessageSlip.pb",
		"attr_SelfTradeInfo.pb",
		"attr_Seller.pb",
		"attr_ShareActiveActivity.pb",
		"attr_ShareChatTime.pb",
		"attr_ShareGiftHistory.pb",
		"attr_ShareGiftInfo.pb",
		"attr_ShareLimitInfo.pb",
		"attr_ShareTypeGiftInfo.pb",
		"attr_ShocksDegreeIDInfo.pb",
		"attr_ShocksRewardInfo.pb",
		"attr_SingleStageInfo.pb",
		"attr_SnsAttr.pb",
		"attr_SnsInteractiveInfo.pb",
		"attr_SnsKVData.pb",
		"attr_SnsKVNumberData.pb",
		"attr_SnsKVStringData.pb",
		"attr_SnsKVValue.pb",
		"attr_SnsRelationInfo.pb",
		"attr_SnsShareReward.pb",
		"attr_SParPGroupMemberContributeInfo.pb",
		"attr_SpecialBattleData.pb",
		"attr_SpecialCropInfo.pb",
		"attr_SpecialFace.pb",
		"attr_SpecialFurniture.pb",
		"attr_SpecReward.pb",
		"attr_SpecRewardInfo.pb",
		"attr_SPEquipAddionEffect.pb",
		"attr_SPEquipmentPlanData.pb",
		"attr_SPEquipmentPlanItem.pb",
		"attr_SPEquipmentPlanPlayerInfoList.pb",
		"attr_SpIconInfo.pb",
		"attr_SpringBlessingCollectionAttr.pb",
		"attr_SpringBlessingCollectionCardInfo.pb",
		"attr_SpringBlessingGetCdKeyAttr.pb",
		"attr_SpringPrayActivityAttr.pb",
		"attr_SpringSlipAssistRecord.pb",
		"attr_SpringSlipData.pb",
		"attr_SpringSlipRewardInfo.pb",
		"attr_SpringSlipRewardLimit.pb",
		"attr_SpringSlipTradeRecord.pb",
		"attr_SPSendMailOptionalParams.pb",
		"attr_SpWorldChatInfo.pb",
		"attr_SquadActivityHistoryData.pb",
		"attr_SquadActivityHistoryPlusData.pb",
		"attr_SquadItemCntInfo.pb",
		"attr_SquadItemInfo.pb",
		"attr_SquadMember.pb",
		"attr_StarPAbility.pb",
		"attr_StarPAccountAchievement.pb",
		"attr_StarPAccountBriefData.pb",
		"attr_StarPAccountData.pb",
		"attr_StarPAccountMailExtraData.pb",
		"attr_StarPAccountOneRLTowerPve.pb",
		"attr_StarPAccountOneTeamPve.pb",
		"attr_StarPAccountPower.pb",
		"attr_StarPAccountRLTowerData.pb",
		"attr_StarPAccountTeamPveData.pb",
		"attr_StarPAchievementData.pb",
		"attr_StarPAdventureBaseInfo.pb",
		"attr_StarPAIInfo.pb",
		"attr_StarPAllPlayerContactRecord.pb",
		"attr_StarPAllStatueData.pb",
		"attr_StarPApply.pb",
		"attr_StarPAssistMaterialInfo.pb",
		"attr_StarPAssistMaterialSimpleInfo.pb",
		"attr_StarPAssistOrderGeneralDBInfo.pb",
		"attr_StarPAssistOrderInfo.pb",
		"attr_StarPAssistOrderSimpleInfo.pb",
		"attr_StarPAssistUserDetailInfo.pb",
		"attr_StarPAssistUserInfo.pb",
		"attr_StarPAssitItemInfo.pb",
		"attr_StarPAttr.pb",
		"attr_StarPAwardInfo.pb",
		"attr_StarPBackpack.pb",
		"attr_StarPBackPackId.pb",
		"attr_StarpBackpackSizeAdd.pb",
		"attr_StarpBackpackSizeInfo.pb",
		"attr_StarpBackpackSizePair.pb",
		"attr_StarPBanInfo.pb",
		"attr_StarPBaseGroupApplicationData.pb",
		"attr_StarPBaseGroupApplicationDataUnion.pb",
		"attr_StarPBaseGroupApplicationUnion.pb",
		"attr_StarPBaseGroupDataInfo.pb",
		"attr_StarPBaseGroupDataInfoUnion.pb",
		"attr_StarPBaseGroupInvitationData.pb",
		"attr_StarPBaseGroupInvitationDataUnion.pb",
		"attr_StarPBaseGroupInvitationUnion.pb",
		"attr_StarPBaseGroupMemberDataInfo.pb",
		"attr_StarPBaseGroupMemberDataInfoUnion.pb",
		"attr_StarPBaseGroupSimpleInfo.pb",
		"attr_StarPBaseGroupSimpleInfoUnion.pb",
		"attr_StarPBaseInfo.pb",
		"attr_StarPBasePve.pb",
		"attr_StarPBasicInfo.pb",
		"attr_StarPBlessDropInfo.pb",
		"attr_StarPBlessRecord.pb",
		"attr_StarPBlessStatueData.pb",
		"attr_StarPBreedEggsParentsHistory.pb",
		"attr_StarPBreedEggsParentsHistoryInfo.pb",
		"attr_StarPBuff.pb",
		"attr_StarPBuild.pb",
		"attr_StarPBuildInteractData.pb",
		"attr_StarPCardDetail.pb",
		"attr_StarPCardDetailGroup.pb",
		"attr_StarPCardDetailGuild.pb",
		"attr_StarPCardDetailPetTrade.pb",
		"attr_StarPCardDetailWorld.pb",
		"attr_StarPCardInfo.pb",
		"attr_StarPCatchBoss.pb",
		"attr_StarPCatchPetDayInfo.pb",
		"attr_StarPCatchPetInfo.pb",
		"attr_StarPCatchPetOrder.pb",
		"attr_StarPCatchPetOrderLast.pb",
		"attr_StarPChatInfo.pb",
		"attr_StarPClimbTower.pb",
		"attr_StarPCommonBasePve.pb",
		"attr_StarPContactGiftRecord.pb",
		"attr_StarPContactRecordUnion.pb",
		"attr_StarPCrop.pb",
		"attr_StarPDefaultDressUpInfo.pb",
		"attr_StarPDelInfo.pb",
		"attr_StarPDormancyBreedInfo.pb",
		"attr_StarPDressUpInfo.pb",
		"attr_StarPDropGuaranteeBagInfo.pb",
		"attr_StarPDropGuaranteeBagInfoItem.pb",
		"attr_StarPDropGuaranteeInfo.pb",
		"attr_StarPDropSpawnFromParam.pb",
		"attr_StarPDsAddr.pb",
		"attr_StarPDsCommonDbInfo.pb",
		"attr_StarPDsCommonDbInfoResult.pb",
		"attr_StarPDsCommonDbKey.pb",
		"attr_StarPDsCommonDBRecordInfo.pb",
		"attr_StarPDsCommonDBUserData.pb",
		"attr_StarPDsCommonDBUserDataUnion.pb",
		"attr_StarPDsFightData.pb",
		"attr_StarPDSFriendIntimacyInfo.pb",
		"attr_StarPDSFriendIntimacyMap.pb",
		"attr_StarPDsGroupApplication.pb",
		"attr_StarPDsGroupApplicationData.pb",
		"attr_StarPDsGroupDBInvasionData.pb",
		"attr_StarPDsGroupDBTradeStationData.pb",
		"attr_StarPDsGroupDBUserData.pb",
		"attr_StarPDsGroupInvitation.pb",
		"attr_StarPDsGroupInvitationData.pb",
		"attr_StarPDsGroupMemberDBPetInfo.pb",
		"attr_StarPDsGroupMemberDBUserData.pb",
		"attr_StarPDsGroupNews.pb",
		"attr_StarPDsGroupNewsData.pb",
		"attr_StarPDsGroupPlayerData.pb",
		"attr_StarPDsGroupTerminalData.pb",
		"attr_StarPDsGroupTerminalKey.pb",
		"attr_StarPDsGuildApplication.pb",
		"attr_StarPDsGuildApplicationData.pb",
		"attr_StarpDsGuildCopied.pb",
		"attr_StarPDsGuildDBInvasionData.pb",
		"attr_StarPDsGuildDBTradeStationData.pb",
		"attr_StarPDsGuildDBUserData.pb",
		"attr_StarPDsGuildGVGData.pb",
		"attr_StarPDsGuildGVGOffline.pb",
		"attr_StarPDsGuildInvitation.pb",
		"attr_StarPDsGuildInvitationData.pb",
		"attr_StarPDsGuildMemberDBPetInfo.pb",
		"attr_StarPDsGuildMemberDBUserData.pb",
		"attr_StarPDsGuildNews.pb",
		"attr_StarPDsGuildNewsData.pb",
		"attr_StarpDsGuildOneCopied.pb",
		"attr_StarPDsGuildTerminalData.pb",
		"attr_StarPDsGuildTerminalKey.pb",
		"attr_StarPDsInfo.pb",
		"attr_StarPDsMapPosCommonDBUserData.pb",
		"attr_StarPDsMapPosCommonDBUserDataUnion.pb",
		"attr_StarPDsMapPosDataInfo.pb",
		"attr_StarPDsMapPosDataInfoUnion.pb",
		"attr_StarPDsNoFloatPosData.pb",
		"attr_StarPDSNotifyGroupData.pb",
		"attr_StarPDsPBMapPosData.pb",
		"attr_StarPDsPersonalBaseData.pb",
		"attr_StarPDsPetExchangeData.pb",
		"attr_StarPDsPetExchangeDetailInfo.pb",
		"attr_StarPDsPlayerCommonDBUserData.pb",
		"attr_StarPDsPlayerCommonDBUserDataUnion.pb",
		"attr_StarPDsPlayerShopBuybackItemInfo.pb",
		"attr_StarPDsPlayerShopBuybackItemTimeInfo.pb",
		"attr_StarPDsPlayerShopInfo.pb",
		"attr_StarPDsPlayerShopInfos.pb",
		"attr_StarPDsPlayerShopItemInfo.pb",
		"attr_StarPDsPlayerShopItemLimitInfo.pb",
		"attr_StarPDsSeedBoxData.pb",
		"attr_StarPDsSelfTerminalData.pb",
		"attr_StarPDsSelfTerminalKey.pb",
		"attr_StarPDSTimeOffset.pb",
		"attr_StarPDsTradeStationData.pb",
		"attr_StarPDsWorldBoxSettingData.pb",
		"attr_StarPDsWorldCommonDBUserData.pb",
		"attr_StarPDsWorldCommonDBUserDataUnion.pb",
		"attr_StarPDsWorldTimeInfo.pb",
		"attr_StarPEggUserData.pb",
		"attr_StarPEquip.pb",
		"attr_StarPEquipCreateInfo.pb",
		"attr_StarPEquipUnlockInfo.pb",
		"attr_StarPEquipUserData.pb",
		"attr_StarPExchangeOrderInfo.pb",
		"attr_StarPField.pb",
		"attr_StarPFriendInteractData.pb",
		"attr_StarPFriendIntimacyActionDailyInfo.pb",
		"attr_StarPFriendIntimacyInfo.pb",
		"attr_StarPGeoPoint.pb",
		"attr_StarPGiftData.pb",
		"attr_StarPGiftRecord.pb",
		"attr_StarPGlobalAchieveData.pb",
		"attr_StarPGodStatusBaseInfo.pb",
		"attr_StarPGroupActivePointInfo.pb",
		"attr_StarPGroupApplicationData.pb",
		"attr_StarPGroupApplicationList.pb",
		"attr_StarPGroupChatInfo.pb",
		"attr_StarPGroupContributeInfo.pb",
		"attr_StarPGroupData.pb",
		"attr_StarPGroupInvitationData.pb",
		"attr_StarPGroupInvitationList.pb",
		"attr_StarPGroupLevelUpTimeData.pb",
		"attr_StarPGroupLevelUpTimeList.pb",
		"attr_StarPGroupMemberActivePointRecord.pb",
		"attr_StarPGroupMemberContributeInfo.pb",
		"attr_StarPGroupMemberData.pb",
		"attr_StarPGroupPlayerContributeInfoMap.pb",
		"attr_StarPGroupSimpleData.pb",
		"attr_StarPGsCommonDbInfo.pb",
		"attr_StarPGsCommonDbInfoResult.pb",
		"attr_StarPGsCommonDbInfoUnion.pb",
		"attr_StarPGsCommonDbKey.pb",
		"attr_StarPGuideCheckResources.pb",
		"attr_StarPGuideSave.pb",
		"attr_StarPGuildActiveData.pb",
		"attr_StarPGuildApplicationData.pb",
		"attr_StarPGuildApplicationList.pb",
		"attr_StarPGuildData.pb",
		"attr_StarpGuildDrop.pb",
		"attr_StarPGuildInvitationData.pb",
		"attr_StarPGuildInvitationList.pb",
		"attr_StarPGuildMemberData.pb",
		"attr_StarPGuildSimpleData.pb",
		"attr_StarpGuildTermItemCopied.pb",
		"attr_StarpGuildTermItemCopiedList.pb",
		"attr_StarPGuildWishStatueData.pb",
		"attr_StarPHarvestItem.pb",
		"attr_StarPHatchPetInfo.pb",
		"attr_StarPHatchPetRecord.pb",
		"attr_StarPHelperInfoList.pb",
		"attr_StarPHelperTriggerInfo.pb",
		"attr_StarPInfo.pb",
		"attr_StarPInteractInfo.pb",
		"attr_StarPInteractPoint.pb",
		"attr_StarPInvitedHistory.pb",
		"attr_StarPInvitedHistoryItem.pb",
		"attr_StarPInvitedMapInfo.pb",
		"attr_StarPInvitedRecord.pb",
		"attr_StarPInvitedRecordList.pb",
		"attr_StarPInviteMeMapInfo.pb",
		"attr_StarPInviteMeRecord.pb",
		"attr_StarPInviteMeRecordList.pb",
		"attr_StarPItem.pb",
		"attr_StarPItemBrief.pb",
		"attr_StarPItemDropInfo.pb",
		"attr_StarPItemIdx.pb",
		"attr_StarPItemUserDataUnion.pb",
		"attr_StarPKeyValue.pb",
		"attr_StarPlayerPShop.pb",
		"attr_StarPLoginSession.pb",
		"attr_StarPMail.pb",
		"attr_StarPMailArgsValues.pb",
		"attr_StarPMailArgValue.pb",
		"attr_StarPMailAttachments.pb",
		"attr_StarPMailExtraData.pb",
		"attr_StarPMailExtraInfoUnion.pb",
		"attr_StarPMailItem.pb",
		"attr_StarPMailKey.pb",
		"attr_StarPMailState.pb",
		"attr_StarPMapIconInfo.pb",
		"attr_StarPMapInfo.pb",
		"attr_StarPMapPosBuilding.pb",
		"attr_StarPMapPosBuildingListInfo.pb",
		"attr_StarPMapPosDeathDrop.pb",
		"attr_StarPMapPosDropItem.pb",
		"attr_StarPMapPosDropPet.pb",
		"attr_StarPMapPosPoi.pb",
		"attr_StarPMapPosPoiOneData.pb",
		"attr_StarPMapPosStrongPoint.pb",
		"attr_StarPMapTag.pb",
		"attr_StarPMember.pb",
		"attr_StarPMemberInfo.pb",
		"attr_StarPMemberPublicBrief.pb",
		"attr_StarPMemberPublicBriefInfo.pb",
		"attr_StarPMigrateSnapshot.pb",
		"attr_StarPNearbyWorld.pb",
		"attr_StarPNotificationInfo.pb",
		"attr_StarPOfflineMailTaskData.pb",
		"attr_StarPOfflineSOCClosedBehavior.pb",
		"attr_StarPOfflineSOCData.pb",
		"attr_StarPOfflineSOCDispatchInfo.pb",
		"attr_StarPOfflineSOCItemData.pb",
		"attr_StarPOfflineSOCItemRateInfo.pb",
		"attr_StarPOfflineTaskDataUnion.pb",
		"attr_StarPOfflineTaskInfo.pb",
		"attr_StarPOneAbilityCDInfo.pb",
		"attr_StarPOneBasePve.pb",
		"attr_StarPOneBattlePet.pb",
		"attr_StarPOneBehaviorData.pb",
		"attr_StarPOneBuffInfo.pb",
		"attr_StarPOneCommonBasePve.pb",
		"attr_StarPOneContactRecord.pb",
		"attr_StarPOnePlayerSocInteractionRatioData.pb",
		"attr_StarPOneTeamPve.pb",
		"attr_StarPOneTeamPveStatus.pb",
		"attr_StarPOwnerInfo.pb",
		"attr_StarPPassiveSkil.pb",
		"attr_StarpPersonalDrop.pb",
		"attr_StarPPet.pb",
		"attr_StarPPetAmulet.pb",
		"attr_StarPPetAttrInfo.pb",
		"attr_StarPPetDexInfo.pb",
		"attr_StarPPetPos.pb",
		"attr_StarPPetSendOrderIdx.pb",
		"attr_StarPPetTeamFirstPosInfo.pb",
		"attr_StarPPetTradeFocusInfo.pb",
		"attr_StarPPetTradeInfo.pb",
		"attr_StarPPetTradeOrderInfo.pb",
		"attr_StarPPetTradeWishInfo.pb",
		"attr_StarPPetUserData.pb",
		"attr_StarPPetWishInfo.pb",
		"attr_StarPPlayerAchievement.pb",
		"attr_StarPPlayerAssisOrderInfo.pb",
		"attr_StarPPlayerAssistOrderInfo.pb",
		"attr_StarPPlayerAttr.pb",
		"attr_StarPPlayerBackCDStatusInfo.pb",
		"attr_StarPPlayerBaseInfo.pb",
		"attr_StarPPlayerBit.pb",
		"attr_StarPPlayerBitInfo.pb",
		"attr_StarPPlayerBriefData.pb",
		"attr_StarPPlayerCatchBossInfo.pb",
		"attr_StarPPlayerCatchPet.pb",
		"attr_StarPPlayerCatchPetInfo.pb",
		"attr_StarPPlayerCondition.pb",
		"attr_StarPPlayerContactRecord.pb",
		"attr_StarPPlayerEnergyInfo.pb",
		"attr_StarPPlayerEquip.pb",
		"attr_StarPPlayerForceMailExtraData.pb",
		"attr_StarPPlayerFriendSkillData.pb",
		"attr_StarPPlayerFunctionControl.pb",
		"attr_StarPPlayerGroupInfo.pb",
		"attr_StarPPlayerGrowthPathGroup.pb",
		"attr_StarPPlayerGrowthPathMission.pb",
		"attr_StarPPlayerGrowthPathSignInReward.pb",
		"attr_StarPPlayerGrowthPathSignInRewardInfo.pb",
		"attr_StarPPlayerIllustratedAwardInfo.pb",
		"attr_StarPPlayerInfo.pb",
		"attr_StarPPlayerIntimacyGeneralDBInfo.pb",
		"attr_StarPPlayerIntimacyInfo.pb",
		"attr_StarPPlayerLastLevel.pb",
		"attr_StarPPlayerLineConditionInfos.pb",
		"attr_StarPPlayerLotteryGuaranteeInfo.pb",
		"attr_StarPPlayerLotteryInfo.pb",
		"attr_StarPPlayerLotteryRecord.pb",
		"attr_StarPPlayerMailExtraData.pb",
		"attr_StarPPlayerMountEquip.pb",
		"attr_StarPPlayerOrderDbInfo.pb",
		"attr_StarPPlayerPetFeedInfo.pb",
		"attr_StarPPlayerPetOrderData.pb",
		"attr_StarPPlayerPetTradeData.pb",
		"attr_StarPPlayerPoiState.pb",
		"attr_StarPPlayerPShop.pb",
		"attr_StarPPlayerPveData.pb",
		"attr_StarPPlayerPvpDailyData.pb",
		"attr_StarPPlayerRLTowerPveData.pb",
		"attr_StarPPlayerRoleAttr.pb",
		"attr_StarPPlayerSetting.pb",
		"attr_StarPPlayerSettingPC.pb",
		"attr_StarPPlayerSocInteractionRatioData.pb",
		"attr_StarPPlayerStatisticsInfo.pb",
		"attr_StarPPlayerStoryLineInfo.pb",
		"attr_StarPPlayerTalentApplyPoint.pb",
		"attr_StarPPlayerTalentData.pb",
		"attr_StarPPlayerTalentProject.pb",
		"attr_StarPPlayerTask.pb",
		"attr_StarPPlayerTaskBaseInfo.pb",
		"attr_StarPPlayerTaskGroup.pb",
		"attr_StarPPlayerTaskGroupInfo.pb",
		"attr_StarPPlayerTaskInfo.pb",
		"attr_StarPPlayerTeamPveData.pb",
		"attr_StarPPlayerTech.pb",
		"attr_StarPPlayerWorldBaseInfo.pb",
		"attr_StarPPlayMapBlockInfo.pb",
		"attr_StarPPlayMapInfo.pb",
		"attr_StarPPos.pb",
		"attr_StarPPrepareMigrateDsInfo.pb",
		"attr_StarPPublicInfo.pb",
		"attr_StarPPveFormationPetDetailInfo.pb",
		"attr_StarPPveFormationPetInfo.pb",
		"attr_StarPPvpFormationInfo.pb",
		"attr_StarPPvpUserData.pb",
		"attr_StarPQualifyingTypeInfo.pb",
		"attr_StarPReceiveInfo.pb",
		"attr_StarPRecentlyCompletedAchievements.pb",
		"attr_StarPRecentTeammate.pb",
		"attr_StarPRecentTeammateInfo.pb",
		"attr_StarPRecoveryWorldData.pb",
		"attr_StarPResourceBalanceTable.pb",
		"attr_StarPResourceControl.pb",
		"attr_StarPResourceData.pb",
		"attr_StarPResourceItem.pb",
		"attr_StarPResourceTableData.pb",
		"attr_StarPRLInfo.pb",
		"attr_StarPRole.pb",
		"attr_StarPRoleAttr.pb",
		"attr_StarPRot.pb",
		"attr_StarPSeed.pb",
		"attr_StarPSeedBox.pb",
		"attr_StarPShocksDegreeIDInfo.pb",
		"attr_StarPShocksRewardInfo.pb",
		"attr_StarPShop.pb",
		"attr_StarPShopItemRecord.pb",
		"attr_StarPSimpleItemUserData.pb",
		"attr_StarPSkill.pb",
		"attr_StarPSOCDormancyData.pb",
		"attr_StarPSocInteractionData.pb",
		"attr_StarPSocInteractRecord.pb",
		"attr_StarPStarInfo.pb",
		"attr_StarPStatisticsInfo.pb",
		"attr_StarPStatusInfo.pb",
		"attr_StarPStorylineRewardData.pb",
		"attr_StarPStoryRewardInfo.pb",
		"attr_StarPTaskSeq.pb",
		"attr_StarPTaskSeqInfo.pb",
		"attr_StarPTaskSeqWhite.pb",
		"attr_StarpTeamDrop.pb",
		"attr_StarPTech.pb",
		"attr_StarPTechInfo.pb",
		"attr_StarPTechStatus.pb",
		"attr_StarPTerminalLevel.pb",
		"attr_StarPThingKey.pb",
		"attr_StarPTimeRefresh.pb",
		"attr_StarPTowerAbilityInfo.pb",
		"attr_StarPTradeOrder.pb",
		"attr_StarPTrader.pb",
		"attr_StarPTutorialEntryInfo.pb",
		"attr_StarPTutorialInfoList.pb",
		"attr_StarPUidMember.pb",
		"attr_StarPUidMemberInfo.pb",
		"attr_StarPUnLockedEquip.pb",
		"attr_StarPUser.pb",
		"attr_StarPUserDressUpInfo.pb",
		"attr_StarPUserPet.pb",
		"attr_StarPUserWorldBaseInfo.pb",
		"attr_StarPUserWorldInfo.pb",
		"attr_StarPVisitorBehaviorRecord.pb",
		"attr_StarPVisitorCropsData.pb",
		"attr_StarPVisitorData.pb",
		"attr_StarPVisitorItemData.pb",
		"attr_StarPVisitorTempData.pb",
		"attr_StarPVisitorTemporyRecord.pb",
		"attr_StarPVisitRecord.pb",
		"attr_StarPWeaponUserData.pb",
		"attr_StarPWishStatueData.pb",
		"attr_StarPWorldChatGroup.pb",
		"attr_StarPWorldDSLastCatchPet.pb",
		"attr_StarPWorldIndex.pb",
		"attr_StarPWorldLastRole.pb",
		"attr_StarPWorldLevelBaseInfo.pb",
		"attr_StarPWorldLevelCurGuildInfo.pb",
		"attr_StarPWorldLevelCurGuildMemberInfo.pb",
		"attr_StarPWorldLevelCurRankList.pb",
		"attr_StarPWorldLevelData.pb",
		"attr_StarPWorldLevelGuildInfo.pb",
		"attr_StarPWorldLevelRankList.pb",
		"attr_StarPWorldLevelUpRecord.pb",
		"attr_StarPWorldOrnamentInfo.pb",
		"attr_StarPWorldPetInfo.pb",
		"attr_StarPWorldPlayerInfo.pb",
		"attr_StarPWorldPveData.pb",
		"attr_StarPWorldRoleInfo.pb",
		"attr_StarPWorldTimeRefresh.pb",
		"attr_StarWorldDetailInfo.pb",
		"attr_StarWorldStepInfo.pb",
		"attr_StatCluster.pb",
		"attr_StatGraph.pb",
		"attr_StealingRecord.pb",
		"attr_StickerChapterData.pb",
		"attr_StickerData.pb",
		"attr_StickFriendInfo.pb",
		"attr_StolenInfo.pb",
		"attr_StolenStat.pb",
		"attr_StreamSetting.pb",
		"attr_StrStrPair.pb",
		"attr_SubReasonData.pb",
		"attr_SubscribeQqRobt.pb",
		"attr_SummerFlashMobData.pb",
		"attr_SummerNavigationBaJiReward.pb",
		"attr_SummerNavigationBarData.pb",
		"attr_SummerVacationBPData.pb",
		"attr_SuperCoreRankActivityData.pb",
		"attr_SuperCoreRankActivityItemConsumeData.pb",
		"attr_SuperLinearRedeem.pb",
		"attr_TakeawayActivity.pb",
		"attr_TakeawayInviteeInfo.pb",
		"attr_TargetEquipInfo.pb",
		"attr_Task.pb",
		"attr_TaskCompleteInfo.pb",
		"attr_TaskExtraInfo.pb",
		"attr_TaskFinish.pb",
		"attr_TaskInfo.pb",
		"attr_TaskLifeTime.pb",
		"attr_TaskRefreshTime.pb",
		"attr_TaskTimeInfo.pb",
		"attr_TeamRankActivity.pb",
		"attr_TeamRankInfo.pb",
		"attr_TestActTeamData.pb",
		"attr_ThemeAdventureDailyTaskData.pb",
		"attr_ThemeAdventureData.pb",
		"attr_ThemeAdventureRewardUpgradeData.pb",
		"attr_ThemeMallInfo.pb",
		"attr_TimeLimitedCheckInActivity.pb",
		"attr_TimeLimitedCheckInAttr.pb",
		"attr_Timeslot.pb",
		"attr_TradingCardBubbleSendInfo.pb",
		"attr_TradingCardClearRedDot.pb",
		"attr_TradingCardCollectionCardInfo.pb",
		"attr_TradingCardCollectionCardInfos.pb",
		"attr_TradingCardCollectionInfo.pb",
		"attr_TradingCardCollectionInfos.pb",
		"attr_TradingCardCollectionRedDot.pb",
		"attr_TradingCardCollectionTag.pb",
		"attr_TradingCardCycleCup.pb",
		"attr_TradingCardCycleCupStageRecord.pb",
		"attr_TradingCardData.pb",
		"attr_TradingCardExchange.pb",
		"attr_TradingCardExchangeInfo.pb",
		"attr_TradingCardInfo.pb",
		"attr_TradingCardNoviceRewardInfo.pb",
		"attr_TradingCardRankData.pb",
		"attr_TradingCardRecentInfo.pb",
		"attr_TradingCardRedDot.pb",
		"attr_TradingCardRewardFlag.pb",
		"attr_TravelingDogData.pb",
		"attr_TravelingDogInfoData.pb",
		"attr_TreasureHuntHistoryData.pb",
		"attr_TreasureLevelUpData.pb",
		"attr_TrophyTaskCompleteInfo.pb",
		"attr_TwoPeopleSquadMemberData.pb",
		"attr_TwoPeopleSquadTaskCompleteData.pb",
		"attr_TwoPeopleSquadTaskData.pb",
		"attr_TYCFpsSettings.pb",
		"attr_TycoonBuildingItem.pb",
		"attr_TycoonUserBasicInfo.pb",
		"attr_TycoonUserBuildingInfo.pb",
		"attr_TycoonUserCurrencyInfo.pb",
		"attr_TycoonUserDBInfo.pb",
		"attr_TycoonUserMonsterInfo.pb",
		"attr_TycoonUserStatInfo.pb",
		"attr_UgcAccountInfo.pb",
		"attr_UgcActivityInfo.pb",
		"attr_UgcBadgeInfo.pb",
		"attr_UgcBuyGoodsDeliverFailOrder.pb",
		"attr_UgcBuyGoodsInfo.pb",
		"attr_UgcCollection.pb",
		"attr_UgcCollectionDigest.pb",
		"attr_UgcCommonInfo.pb",
		"attr_UgcCoPlayInfo.pb",
		"attr_UgcCoPlayRecord.pb",
		"attr_UgcCreativeTaskInfo.pb",
		"attr_UgcDailyStage.pb",
		"attr_UgcDanMu.pb",
		"attr_UgcDeliverGoodsInfo.pb",
		"attr_UgcGroup.pb",
		"attr_UgcGroupActorList.pb",
		"attr_UgcGroupIdList.pb",
		"attr_UgcGrowUpInfo.pb",
		"attr_UgcIdMap.pb",
		"attr_UgcMapCreatorInfo.pb",
		"attr_UgcMapInfo.pb",
		"attr_UgcMapLoadingInfo.pb",
		"attr_UgcMapSetInfo.pb",
		"attr_UgcMatchInfo.pb",
		"attr_UgcMatchRecord.pb",
		"attr_UgcMiniGamePlayInfo.pb",
		"attr_UgcOperate.pb",
		"attr_UgcOpInfo.pb",
		"attr_UgcSave.pb",
		"attr_UgcSlotInfo.pb",
		"attr_UgcSlotKeyInfo.pb",
		"attr_UgcStarWorld.pb",
		"attr_UgcTaskDeduplication.pb",
		"attr_UgcTaskInfo.pb",
		"attr_UgcTestPlayerInfo.pb",
		"attr_UID.pb",
		"attr_UltramanThemeActivity.pb",
		"attr_UltramanThemeTeam.pb",
		"attr_UpgradeAwardInfo.pb",
		"attr_UpgradeCheckInInfo.pb",
		"attr_UpgradeCheckInManualActivity.pb",
		"attr_UseItemShareActivity.pb",
		"attr_UseItemShareRecord.pb",
		"attr_UserActivityAttr.pb",
		"attr_UserAllSeasonNoteBookAttr.pb",
		"attr_UserAttr.pb",
		"attr_UserConcertData.pb",
		"attr_UserConcertTicketInfo.pb",
		"attr_UserLabel.pb",
		"attr_UserSeasonNoteBookAttr.pb",
		"attr_VariableData.pb",
		"attr_VileplumeInfo.pb",
		"attr_VillageAttr.pb",
		"attr_VillageBasicInfo.pb",
		"attr_VillagePublicInfo.pb",
		"attr_VillagerAcceptGiftInfo.pb",
		"attr_VillagerClientCache.pb",
		"attr_VillagerDailyFavorInfo.pb",
		"attr_VillagerFestivalGiftInfo.pb",
		"attr_VillagerGiftInfo.pb",
		"attr_VillagerGiftItem.pb",
		"attr_VillagerHouse.pb",
		"attr_VillagerHouseVisitorInfo.pb",
		"attr_VillagerPresentGiftInfo.pb",
		"attr_VillagerTriggeredGift.pb",
		"attr_VisitCookInfo.pb",
		"attr_VisitFarmInfo.pb",
		"attr_VisitFarmTlogInfo.pb",
		"attr_VisitHouseInfo.pb",
		"attr_VisitHouseTlogInfo.pb",
		"attr_VisitorFarmPartyInfo.pb",
		"attr_VisitRichBoardInfo.pb",
		"attr_VisitRichTlogInfo.pb",
		"attr_VisitXiaoWoInfo.pb",
		"attr_WaitBattleInfo.pb",
		"attr_WallPaper.pb",
		"attr_WarmRoundBattleResultDb.pb",
		"attr_WarmRoundInfoDb.pb",
		"attr_WarmRoundReturningInfoDb.pb",
		"attr_WarmRoundScoreChangeDb.pb",
		"attr_WarmRoundScoreDb.pb",
		"attr_WarmRoundTriggeredDb.pb",
		"attr_WaterReward.pb",
		"attr_WaterRewardItem.pb",
		"attr_WealthBankActivity.pb",
		"attr_WeekenIceBrokenData.pb",
		"attr_WeeklyTaskExtraInfo.pb",
		"attr_WeekPakPlayRecord.pb",
		"attr_WelfareAerospaceTechEdData.pb",
		"attr_WelfareData.pb",
		"attr_WelfareHistoryBill.pb",
		"attr_WerewolfFullReducedCartDataAttr.pb",
		"attr_WerewolfFullReducedCartDataMap.pb",
		"attr_WerewolfFullReducedConf.pb",
		"attr_WerewolfFullReducedConfData.pb",
		"attr_WhiteInfo.pb",
		"attr_WhiteListInfo.pb",
		"attr_WildCardInfo.pb",
		"attr_WildCatRefreshInfo.pb",
		"attr_WishActivityData.pb",
		"attr_WishAwardData.pb",
		"attr_WishAwardRecordData.pb",
		"attr_WishingRecord.pb",
		"attr_WishingTreeActivityData.pb",
		"attr_WishRecordData.pb",
		"attr_WishTaskData.pb",
		"attr_WolfHistoryData.pb",
		"attr_WolfKillInfo.pb",
		"attr_WolfKillNewUserTipsInfo.pb",
		"attr_WolfKillRewardItem.pb",
		"attr_WolfKillTreasureEquipInfo.pb",
		"attr_WolfKillTreasureRentFromInfo.pb",
		"attr_WolfKillTreasureRentHighVersionItemInfo.pb",
		"attr_WolfKillTreasureRentInfo.pb",
		"attr_WolfReturnData.pb",
		"attr_WolfTeamAwardRecordData.pb",
		"attr_WolfTeamChestData.pb",
		"attr_WorldStarPBackPackId.pb",
		"attr_XiaowoAttr.pb",
		"attr_XiaoWoBasicInfo.pb",
		"attr_XiaoWoClientCache.pb",
		"attr_XiaoWoComponent.pb",
		"attr_XiaoWoDsAddr.pb",
		"attr_XiaoWoDsInfo.pb",
		"attr_XiaoWoFarmingInfo.pb",
		"attr_XiaoWoHandbookInfo.pb",
		"attr_XiaoWoHandbookItem.pb",
		"attr_XiaoWoHotInfo.pb",
		"attr_XiaoWoInfo.pb",
		"attr_XiaoWoInteractInfo.pb",
		"attr_XiaoWoLayoutInfo.pb",
		"attr_XiaoWoLayoutPublishRecord.pb",
		"attr_XiaoWoLayoutPublishRecordIndexInfo.pb",
		"attr_XiaoWoLevelInfo.pb",
		"attr_XiaoWoLikeInfo.pb",
		"attr_XiaoWoLiuYanMessageInfo.pb",
		"attr_XiaoWoLockInfo.pb",
		"attr_XiaoWoMinuteHotInfo.pb",
		"attr_XiaoWoMoneyTreeInfo.pb",
		"attr_XiaoWoOwnerInfo.pb",
		"attr_XiaoWoPlayerLiuYanMessageInfo.pb",
		"attr_XiaowoPublicInfo.pb",
		"attr_XiaoWoPunish.pb",
		"attr_XiaoWoPunishSaveInfo.pb",
		"attr_XiaoWoRedPacketInfo.pb",
		"attr_XiaoWoSafeInfo.pb",
		"attr_XiaoWoSampleRoomInfo.pb",
		"attr_XiaoWoShareInfo.pb",
		"attr_XiaoWoStarInfo.pb",
		"attr_XiaowoUgcMapMetaInfo.pb",
		"attr_XiaoWoVisitor.pb",
		"attr_XiaoWoVisitorInfo.pb",
		"attr_XiaoWoWelcomeInfo.pb",
		"attr_XlsWhiteList.pb",
	},
	irpc = {
		"competition.pb",
		"dsa_public.pb",
		"dsc.pb",
		"dsc_for_dev.pb",
		"ds_arena.pb",
		"ds_cocsvr.pb",
		"ds_common.pb",
		"ds_dsdbsvr.pb",
		"ds_event_subscriber.pb",
		"ds_farm.pb",
		"ds_gate.pb",
		"ds_lobby.pb",
		"ds_player.pb",
		"ds_region.pb",
		"ds_roguelike.pb",
		"ds_roomsvr.pb",
		"ds_snssvr.pb",
		"ds_spdbsvr.pb",
		"ds_starp.pb",
		"ds_starpaccountsvr.pb",
		"ds_starpbattlesvr.pb",
		"ds_starpgamesvr.pb",
		"ds_starpgroup.pb",
		"ds_starpguild.pb",
		"ds_starpmailsvr.pb",
		"ds_starpplayer.pb",
		"ds_ugcdatastoresvr.pb",
		"ds_ugcsvr.pb",
		"ds_xiaowo.pb",
		"hello_ds.pb",
		"hello_gs.pb",
		"sd_arena.pb",
		"sd_battle.pb",
		"sd_cocsvr.pb",
		"sd_common.pb",
		"sd_competition.pb",
		"sd_debugds.pb",
		"sd_farm.pb",
		"sd_lobby.pb",
		"sd_player.pb",
		"sd_starp.pb",
		"sd_starpbattle.pb",
		"sd_starpgroup.pb",
		"sd_starpmailsvr.pb",
		"sd_streamsvr.pb",
		"sd_ugcdatastoresvr.pb",
		"sd_xiaowo.pb",
	},
	CS = {
		"cs_config.pb",
		"cs_plat_common_config.pb",
		"cs_account.pb",
		"cs_resources.pb",
		"cs_achievement.pb",
		"cs_activity.pb",
		"cs_activity2.pb",
		"cs_ainpc.pb",
		"cs_album.pb",
		"cs_ams.pb",
		"cs_arena.pb",
		"cs_bag.pb",
		"cs_battle.pb",
		"cs_birthday.pb",
		"cs_bp.pb",
		"cs_chase.pb",
		"cs_chat.pb",
		"cs_club.pb",
		"cs_coc.pb",
		"cs_common.pb",
		"cs_competition.pb",
		"cs_competition_custom.pb",
		"cs_competition_feature.pb",
		"cs_competition_normal.pb",
		"cs_concert.pb",
		"cs_cook.pb",
		"cs_cups.pb",
		"cs_danmu.pb",
		"cs_dressup.pb",
		"cs_event.pb",
		"cs_farm.pb",
		"cs_flash_cheer_ai.pb",
		"cs_fps.pb",
		"cs_gamemodereturn.pb",
		"cs_hideandseek.pb",
		"cs_hok.pb",
		"cs_house.pb",
		"cs_information.pb",
		"cs_lbs.pb",
		"cs_letsgo.pb",
		"cs_lobby.pb",
		"cs_mail.pb",
		"cs_mall.pb",
		"cs_master_path.pb",
		"cs_match.pb",
		"cs_message.pb",
		"cs_notice.pb",
		"cs_party.pb",
		"cs_permit.pb",
		"cs_pilot.pb",
		"cs_plat_common.pb",
		"cs_player.pb",
		"cs_qualify.pb",
		"cs_raffle.pb",
		"cs_rank.pb",
		"cs_recharge.pb",
		"cs_reddot.pb",
		"cs_relation.pb",
		"cs_resource.pb",
		"cs_reward.pb",
		"cs_rich.pb",
		"cs_room.pb",
		"cs_scene.pb",
		"cs_season.pb",
		"cs_sensitive.pb",
		"cs_share.pb",
		"cs_sharegift.pb",
		"cs_starp.pb",
		"cs_starpgame.pb",
		"cs_starproom.pb",
		"cs_stream.pb",
		"cs_task.pb",
		"cs_trading_card.pb",
		"cs_tyc.pb",
		"cs_ugc.pb",
		"cs_ugcdatastore.pb",
		"cs_ugc_appreciate.pb",
		"cs_ugc_collection.pb",
		"cs_ugc_forward.pb",
		"cs_ugc_translate.pb",
		"cs_wolfkill.pb",
		"cs_xiaowo.pb",
		"cs_sp_capturepetbroadcast.pb",
		"cs_clientcustomdata.pb",
		"cs_directscene.pb",
		"cs_aigc_stream.pb",
		"cs_ds_stream.pb",
		"cs_login_stream.pb",
		"cs_ugc_aiassistant.pb",
		"cs_ugc_stream.pb",
	},
	DS = {
		"cs_sp_ability.pb",
		"cs_sp_adventure.pb",
		"cs_sp_amulet.pb",
		"cs_sp_assist.pb",
		"cs_sp_attribute.pb",
		"cs_sp_base_pve.pb",
		"cs_sp_breed.pb",
		"cs_sp_building.pb",
		"cs_sp_cdnslienceupdate.pb",
		"cs_sp_common.pb",
		"cs_sp_debug.pb",
		"cs_sp_demo.pb",
		"cs_sp_dpsInfo.pb",
		"cs_sp_dungeonactivation.pb",
		"cs_sp_energy.pb",
		"cs_sp_equip.pb",
		"cs_sp_expLimit.pb",
		"cs_sp_friendachieve.pb",
		"cs_sp_friendIntimacy.pb",
		"cs_sp_functioncontrol.pb",
		"cs_sp_group.pb",
		"cs_sp_growthpath.pb",
		"cs_sp_guide.pb",
		"cs_sp_guild.pb",
		"cs_sp_helper.pb",
		"cs_sp_interact.pb",
		"cs_sp_invasion.pb",
		"cs_sp_invasiondbg.pb",
		"cs_sp_item.pb",
		"cs_sp_learningDiagram.pb",
		"cs_sp_mail.pb",
		"cs_sp_mainProcessTlog.pb",
		"cs_sp_map.pb",
		"cs_sp_migrateState.pb",
		"cs_sp_monster.pb",
		"cs_sp_normal.pb",
		"cs_sp_offlineProduct.pb",
		"cs_sp_pet.pb",
		"cs_sp_petTrade.pb",
		"cs_sp_player.pb",
		"cs_sp_profession.pb",
		"cs_sp_prop.pb",
		"cs_sp_pvp.pb",
		"cs_sp_report.pb",
		"cs_sp_setting.pb",
		"cs_sp_settle.pb",
		"cs_sp_shop.pb",
		"cs_sp_status.pb",
		"cs_sp_storyline.pb",
		"cs_sp_task.pb",
		"cs_sp_teambroadcast.pb",
		"cs_sp_terminal.pb",
		"cs_sp_time.pb",
		"cs_sp_tradingPost.pb",
		"cs_sp_tutorial.pb",
		"cs_sp_visit.pb",
		"cs_sp_worldLevel.pb",
	},
	Excel = {
		"ResAbilityConfig.pb",
		"ResAchievement.pb",
		"ResAchievementSort.pb",
		"ResActivity.pb",
		"ResActivityAnimalHandbook.pb",
		"ResActivityBannerJumpStyle.pb",
		"ResActivityBookOfFriends.pb",
		"ResActivityCaptureShadow.pb",
		"ResActivityCheckInPlan.pb",
		"ResActivityClubChallenge.pb",
		"ResActivityComponent.pb",
		"ResActivityConan.pb",
		"ResActivityDoubleTeam.pb",
		"ResActivityElfForest.pb",
		"ResActivityFlashRaceCheering.pb",
		"ResActivityFlyingChess.pb",
		"ResActivityFridayCollection.pb",
		"ResActivityFunPara.pb",
		"ResActivityGrowthPath.pb",
		"ResActivityHeroSpine.pb",
		"ResActivityInflateRedPacket.pb",
		"ResActivityInterServerGift.pb",
		"ResActivityLeagueHeroes.pb",
		"ResActivityLotteryDraw.pb",
		"ResActivityMainConfig.pb",
		"ResActivityMarquee.pb",
		"ResActivityMinesweeper.pb",
		"ResActivityMobaRandomVote.pb",
		"ResActivityMobaSquadDrawRedPackage.pb",
		"ResActivityMonopoly.pb",
		"ResActivityMusicOrder.pb",
		"ResActivityQingShuangTrial.pb",
		"ResActivityRecruiteOrder.pb",
		"ResActivityRestaurantThemed.pb",
		"ResActivitySquad.pb",
		"ResActivitySuperCoreRank.pb",
		"ResActivityTeam.pb",
		"ResActivityTwoPeopleSquad.pb",
		"ResActivityWishesCameTrue.pb",
		"ResActivityWolfKillSquadTrophy.pb",
		"ResActivityWolfReturn.pb",
		"ResAddQQFriendWhiteList.pb",
		"ResAdministration.pb",
		"ResAgreements.pb",
		"ResAIAbility.pb",
		"ResAIAssistantErrorCodeConfig.pb",
		"ResAIGC.pb",
		"ResAIGCNPC.pb",
		"ResAIInfo.pb",
		"ResAIModes.pb",
		"ResAIScript.pb",
		"ResAISuitInfo.pb",
		"ResAlbum.pb",
		"ResAMSItem.pb",
		"ResAmusementParkActivity.pb",
		"ResAppearanceCatalog.pb",
		"ResAreaPropRep.pb",
		"ResArenaABTestGroup.pb",
		"ResArenaAIConfig.pb",
		"ResArenaAttribute.pb",
		"ResArenaAttributeHOKOverride.pb",
		"ResArenaAttrTest.pb",
		"ResArenaAvatarSkill.pb",
		"ResArenaBanPick.pb",
		"ResArenaBuff.pb",
		"ResArenaBulletSet.pb",
		"ResArenaCard.pb",
		"ResArenaCardControl.pb",
		"ResArenaCardPack.pb",
		"ResArenaCardRegulation.pb",
		"ResArenaCardSuitBuff.pb",
		"ResArenaCardTagWeight.pb",
		"ResArenaCombatEffectiveness.pb",
		"ResArenaCommon.pb",
		"ResArenaCommonAttributeBar.pb",
		"ResArenaDailyVictoryChest.pb",
		"ResArenaDamageEffect.pb",
		"ResArenaDamageEffectHOKOverride.pb",
		"ResArenaEnemyStack.pb",
		"ResArenaGift.pb",
		"ResArenaGlobal.pb",
		"ResArenaGuideHero.pb",
		"ResArenaHeroCard.pb",
		"ResArenaHeroCardHot.pb",
		"ResArenaHeroCardReplace.pb",
		"ResArenaHeroDeraultCard.pb",
		"ResArenaHeroOpen.pb",
		"ResArenaHeroStar.pb",
		"ResArenaHeroTrain.pb",
		"ResArenaHeroUnlock.pb",
		"ResArenaHighlight.pb",
		"ResArenaHit.pb",
		"ResArenaHOKBannedAndReport.pb",
		"ResArenaHUDButton.pb",
		"ResArenaIndicator.pb",
		"ResArenaInteractMark.pb",
		"ResArenaLevelPool.pb",
		"ResArenaLimitedTimeFreeHero.pb",
		"ResArenaMagicField.pb",
		"ResArenaMatchGroup.pb",
		"ResArenaMisc.pb",
		"ResArenaMonster.pb",
		"ResArenaMysteryChest.pb",
		"ResArenaNoStackUI.pb",
		"ResArenaNotice.pb",
		"ResArenaPaidFeature.pb",
		"ResArenaPreparations.pb",
		"ResArenaQuickMessage.pb",
		"ResArenaRandomEvent.pb",
		"ResArenaRegister.pb",
		"ResArenaRound.pb",
		"ResArenaShardMall.pb",
		"ResArenaShowEffect.pb",
		"ResArenaSign.pb",
		"ResArenaSkill.pb",
		"ResArenaSkinEffect.pb",
		"ResArenaSpecialMatch.pb",
		"ResArenaStackCount.pb",
		"ResArenaStrategy.pb",
		"ResArenaSummonAIConfig.pb",
		"ResArenaTagDefines.pb",
		"ResArenaTip.pb",
		"ResArenaVoice.pb",
		"ResArenaWeapon.pb",
		"ResArenaWeaponSkin.pb",
		"ResArenaWish.pb",
		"ResAvatarUsageRecord.pb",
		"ResBackpack.pb",
		"ResBackpackItem.pb",
		"ResBackpackItemClient.pb",
		"ResBackpackItemEffect.pb",
		"ResBackpackItemTypeDef.pb",
		"ResBackpackItem_UGC.pb",
		"ResBackpackTag.pb",
		"ResBandConcert.pb",
		"ResBattle.pb",
		"ResBattlePass.pb",
		"ResBI.pb",
		"ResBioChaseRoleCfg.pb",
		"ResBioChaseRoleCfg_Fight.pb",
		"ResBioChaseRuleCfg.pb",
		"ResBioChaseSkillCfg.pb",
		"ResBioChaseSkillCfg_Fight.pb",
		"ResBirthday.pb",
		"ResBlockingWord.pb",
		"ResBpmGameProps.pb",
		"ResBrainiac.pb",
		"ResBrGameProps.pb",
		"ResBroadcastSetting.pb",
		"ResBSEvent.pb",
		"ResBSGlobal.pb",
		"ResBSHeroList.pb",
		"ResBSMapParam.pb",
		"ResBSPotion.pb",
		"ResBurdenReduceTask.pb",
		"ResCameraFilter.pb",
		"ResCaughtPenguinCfg.pb",
		"ResCDNImageDownloadPriority.pb",
		"ResChannelEntrance.pb",
		"ResCharAnimSpeedUp.pb",
		"ResChaseAnimSkin.pb",
		"ResChaseBossAction.pb",
		"ResChaseBossDecoration.pb",
		"ResChaseBossSkinConfig.pb",
		"ResChaseCameraSetting.pb",
		"ResChaseComprehensiveScoreConfig.pb",
		"ResChaseHealTeammates.pb",
		"ResChaseIDMastery.pb",
		"ResChaseInLevelTarget.pb",
		"ResChaseMapInlevel.pb",
		"ResChaseMaxHP.pb",
		"ResChaseMinimapPoint.pb",
		"ResChaseNewbieAbTest.pb",
		"ResChaseProgression.pb",
		"ResChasePropSkinConfig.pb",
		"ResChaseQuickChatMsg.pb",
		"ResChaseSituationScore.pb",
		"ResChaseSkinParticleConfig.pb",
		"ResChaseSkinReverse.pb",
		"ResChaseSpeedUpToEnd.pb",
		"ResChaseTagCountLimit.pb",
		"ResChat.pb",
		"ResChatAction.pb",
		"ResChunkDownloadSpeed.pb",
		"ResChunkDownloadTask.pb",
		"ResChunkDownloadWhiteList.pb",
		"ResChunkGroup.pb",
		"ResCleaner.pb",
		"ResClientCommon.pb",
		"ResClientMainPanelSwitch.pb",
		"ResClientNetConfig.pb",
		"ResClub.pb",
		"ResClubLog.pb",
		"ResCOCAttribute.pb",
		"ResCOCBattle.pb",
		"ResCOCBuff.pb",
		"ResCOCBuild.pb",
		"ResCOCBullet.pb",
		"ResCOCCommon.pb",
		"ResCOCDamageEffect.pb",
		"ResCOCDialogue.pb",
		"ResCOCGeneral.pb",
		"ResCocGM.pb",
		"ResCOCGuideMisc.pb",
		"ResCOCGuideReset.pb",
		"ResCOCHeroAttribute.pb",
		"ResCOCHeroSkill.pb",
		"ResCOCItem.pb",
		"ResCOCJumpConfig.pb",
		"ResCOCLevel.pb",
		"ResCOCLevelInfo.pb",
		"ResCOCMap.pb",
		"ResCOCMatch.pb",
		"ResCOCMisc.pb",
		"ResCOCMonster.pb",
		"ResCocMonthCard.pb",
		"ResCOCObstacle.pb",
		"ResCOCPrison.pb",
		"ResCOCProjectile.pb",
		"ResCOCProsperity.pb",
		"ResCOCPVEMode.pb",
		"ResCOCReward.pb",
		"ResCOCScience.pb",
		"ResCOCSkill.pb",
		"ResCOCSoldier.pb",
		"ResCOCSound.pb",
		"ResCOCStroller.pb",
		"ResCOCTask.pb",
		"ResCOCTeam.pb",
		"ResCOCTrigger.pb",
		"ResCOCVersionCompat.pb",
		"ResCOCVillager.pb",
		"ResCommon.pb",
		"ResCommunityEntrace.pb",
		"ResCommunityLevelLoad.pb",
		"ResCommunityPet.pb",
		"ResCompetitionReward.pb",
		"ResConanQuiz.pb",
		"ResConcert.pb",
		"ResCondition.pb",
		"ResConditionLogic.pb",
		"ResCookSysConf.pb",
		"ResCups.pb",
		"ResCustomAnim.pb",
		"ResCustomConfig.pb",
		"ResCustomerService.pb",
		"ResCustomKeyMatchType.pb",
		"ResCustomModelingMapConfig.pb",
		"ResDanMu.pb",
		"ResDDPMapConfig.pb",
		"ResDDPSkillConfig.pb",
		"ResDevice.pb",
		"ResDeviceSwitchSceneQualityTip.pb",
		"ResDfGameStore.pb",
		"ResDialogue.pb",
		"ResDiscoveryGuidanceConf.pb",
		"ResDisplayBoard.pb",
		"ResDownloadFile.pb",
		"ResDressUp.pb",
		"ResDropConfig.pb",
		"ResDsConfig.pb",
		"ResElementCommon.pb",
		"ResEveryonePlayingDot.pb",
		"ResExcludedPoisonCircle.pb",
		"ResExtraMall.pb",
		"ResFaction.pb",
		"ResFarmActivity.pb",
		"ResFarmActivityGift.pb",
		"ResFarmBuff.pb",
		"ResFarmBuilding.pb",
		"ResFarmBuildingDecoration.pb",
		"ResFarmCollection.pb",
		"ResFarmCookAvatar.pb",
		"ResFarmCookDish.pb",
		"ResFarmCookEmployee.pb",
		"ResFarmCookFloatingScreen.pb",
		"ResFarmCookLevel.pb",
		"ResFarmCookLine.pb",
		"ResFarmCookVisitant.pb",
		"ResFarmCrop.pb",
		"ResFarmCropMachine.pb",
		"ResFarmDialogContent.pb",
		"ResFarmEvent.pb",
		"ResFarmFish.pb",
		"ResFarmHot.pb",
		"ResFarmHotSpring.pb",
		"ResFarmItem.pb",
		"ResFarmKirin.pb",
		"ResFarmLevel.pb",
		"ResFarmLevelTitle.pb",
		"ResFarmMagic.pb",
		"ResFarmMonthcardDecoration.pb",
		"ResFarmNPC.pb",
		"ResFarmOperation.pb",
		"ResFarmPet.pb",
		"ResFarmRainbowConfig.pb",
		"ResFarmRoom.pb",
		"ResFarmSampleRoom.pb",
		"ResFarmSysConf.pb",
		"ResFarmTalent.pb",
		"ResFarmTask.pb",
		"ResFarmVillager.pb",
		"ResFarmWarning.pb",
		"ResFarmWeather.pb",
		"ResFarmWhiteListConfig.pb",
		"ResFashionScoreCalc.pb",
		"ResFBGlobal.pb",
		"ResFBHeroList.pb",
		"ResFeatureIntegration.pb",
		"ResFinalAbilityConfig.pb",
		"ResFinalAccountRewardConf.pb",
		"ResFishingHallOfFame.pb",
		"ResFix.pb",
		"ResFPSSpecialSkinOffset.pb",
		"ResFPSWeapon.pb",
		"ResFPSWeaponAttachment.pb",
		"ResFPSWeaponSkinIP.pb",
		"ResGameAgreementRule.pb",
		"ResGameLiveTask.pb",
		"ResGameModeReturn.pb",
		"ResGameOptimizeSetting.pb",
		"ResGamePlayChasingLightCfg.pb",
		"ResGameRuleConfig.pb",
		"ResGameSetting.pb",
		"ResGCParam.pb",
		"ResGeneral.pb",
		"ResGeneralOutput.pb",
		"ResGiftPackage.pb",
		"ResGiveCard.pb",
		"ResGPM.pb",
		"ResGuidanceRule.pb",
		"ResGuideApp.pb",
		"ResGunGame.pb",
		"ResHOKAI.pb",
		"ResHOKBattleFOV.pb",
		"ResHOKBuffAreaTrigger.pb",
		"ResHOKCard.pb",
		"ResHOKCardRole.pb",
		"ResHOKCardShop.pb",
		"ResHOKCpuDebug.pb",
		"ResHOKGlobal.pb",
		"ResHOKGoldExp.pb",
		"ResHOKHero.pb",
		"ResHOKHeroBalance.pb",
		"ResHOKHeroLevelUp.pb",
		"ResHOKHeroRebirth.pb",
		"ResHOKHeroRobot.pb",
		"ResHOKHeroUnlock.pb",
		"ResHokIcon.pb",
		"ResHOKKuaKua.pb",
		"ResHokMisc.pb",
		"ResHOKMonsterLayout.pb",
		"ResHOKMonsterLevel.pb",
		"ResHOKOverrideArenaBuff.pb",
		"ResHOKOverrideArenaDamageEffect.pb",
		"ResHOKPlayerGuideTask.pb",
		"ResHOKRiftPower.pb",
		"ResHOKSeasonFeatureSwitch.pb",
		"ResHOKSkillOverride.pb",
		"ResHOKSoldierWave.pb",
		"ResHOKTowerAtkGrow.pb",
		"ResHotBuyUMGShowCondition.pb",
		"ResHotZone.pb",
		"ResIAA.pb",
		"ResIdipDatamoreConfig.pb",
		"ResImpressions.pb",
		"ResIndicator.pb",
		"ResInitialPoisonCircle.pb",
		"ResInLevelEvent.pb",
		"ResInteractionAnimation.pb",
		"ResInteractiveActor.pb",
		"ResInviteActivityDisplay.pb",
		"ResJSReliableAIConfig.pb",
		"ResJumpConfig.pb",
		"ResLanguages.pb",
		"ResLetsGoDeviceProfileInfo.pb",
		"ResLetsGoMoments.pb",
		"ResLetsGoShopTag.pb",
		"ResLevel.pb",
		"ResLevelDrop.pb",
		"ResLevelDropArena.pb",
		"ResLevelDuration.pb",
		"ResLevelInfo.pb",
		"ResLevelModule.pb",
		"ResLevelRoundRandomRule.pb",
		"ResLevelTechniqueBroadcast.pb",
		"ResLightningGameInfo.pb",
		"ResLimitedExperienceItem.pb",
		"ResLiveLink.pb",
		"ResLoadingText.pb",
		"ResLobby.pb",
		"ResLobbyIconSwitch.pb",
		"ResLobbyIconSwitchJoy.pb",
		"ResLobbyJumpMsgBox.pb",
		"ResLobbyNav.pb",
		"ResLobbyRightUp.pb",
		"ResLoginBackground.pb",
		"ResLoginResource.pb",
		"ResLoginSpineEffect.pb",
		"ResLuckyRebateActivity.pb",
		"ResLuckyTurntable.pb",
		"ResMailIni.pb",
		"ResMailTemplate.pb",
		"ResMainGameInfo.pb",
		"ResMainGameSurvive.pb",
		"ResMall.pb",
		"ResMallMobaItemTips.pb",
		"ResMasterPath.pb",
		"ResMatch.pb",
		"ResMatchABTest.pb",
		"ResMatchActivityType.pb",
		"ResMatchControl.pb",
		"ResMatchLevelRecord.pb",
		"ResMatchMMR.pb",
		"ResMatchPreparation.pb",
		"ResMatchTypeGroup.pb",
		"ResMatchWarmRound.pb",
		"ResMatchWarmScore.pb",
		"ResMeetingInteraction.pb",
		"ResMidas.pb",
		"ResMiniGames.pb",
		"ResMiniGamesActivity.pb",
		"ResMisc.pb",
		"ResMobaPeakTournament.pb",
		"ResMoneyTree.pb",
		"ResMonster.pb",
		"ResMoodPos.pb",
		"ResNewActivityPilot.pb",
		"ResNewBattlePass.pb",
		"ResNewHeroTrainConf.pb",
		"ResNewYearPilot.pb",
		"ResNewYearWishes.pb",
		"ResNickname.pb",
		"ResNicknameWidth.pb",
		"ResNotice.pb",
		"ResNoviceRewardConf.pb",
		"ResNPC.pb",
		"ResNPCFarmConf.pb",
		"ResNR3E1Decoration.pb",
		"ResNR3E1LevelProp.pb",
		"ResNR3E1MapModule.pb",
		"ResNR3E3MonthCardBuyLimit.pb",
		"ResNR3E3MonthCardClientDisplay.pb",
		"ResNR3E3MonthCardFreeGift.pb",
		"ResNR3E3MonthCardLevel.pb",
		"ResNR3E3MonthCardPrivilege.pb",
		"ResNR3E3MonthCardRechargeTab.pb",
		"ResNR3E3SpecialSkeletal.pb",
		"ResNR3E3Tips.pb",
		"ResNR3E3Treasure.pb",
		"ResNR3E3Vocation.pb",
		"ResNR3E8.pb",
		"ResNR3EGameConfig.pb",
		"ResNR3EHighlightMoment.pb",
		"ResNR3EThemeEventConfig.pb",
		"ResNR3ETreasureHuntCampShopConfig.pb",
		"ResNR3ETreasureHuntNPCChatConfig.pb",
		"ResOMDLevelInfo.pb",
		"ResOMDLevelMonsters.pb",
		"ResOMDLevelRules.pb",
		"ResOMDMisc.pb",
		"ResOMDMonster.pb",
		"ResOMDOrcpedia.pb",
		"ResOMDProps.pb",
		"ResOMDRating.pb",
		"ResOMDStore.pb",
		"ResOMDSupply.pb",
		"ResOMDWeapon.pb",
		"ResOMDWeaponAttr.pb",
		"ResOMDWeaponSkin.pb",
		"ResOMDWeaponSuitSkin.pb",
		"ResOMDWeaponUpgrade.pb",
		"ResPCKeyBindConf.pb",
		"ResPerfWeight.pb",
		"ResPermit.pb",
		"ResPersonality.pb",
		"ResPiano.pb",
		"ResPilot.pb",
		"ResPlaceableActor.pb",
		"ResPlayerBillboard.pb",
		"ResPlayerGrayRule.pb",
		"ResPlayerGuide.pb",
		"ResPlayerLevel.pb",
		"ResPlayerUgcLevel.pb",
		"ResPlazaFacilityJump.pb",
		"ResPlot.pb",
		"ResPlotDialogue.pb",
		"ResPoisonCircle.pb",
		"ResPoisonCircleTips.pb",
		"ResPray.pb",
		"ResPreCachedConfig.pb",
		"ResPreparations.pb",
		"ResPreview.pb",
		"ResProfileTheme.pb",
		"ResPropConfig.pb",
		"ResProtectedScore.pb",
		"ResPublicChat.pb",
		"ResPushFace.pb",
		"ResPushTopic.pb",
		"ResQAInvest.pb",
		"ResQQGames.pb",
		"ResQqGroupTeamPlayConfig.pb",
		"ResQQWXLoginErrorCode.pb",
		"ResQRCode.pb",
		"ResQualifying.pb",
		"ResQuickEntrance.pb",
		"ResQuickInteract.pb",
		"ResQuiz.pb",
		"ResRaffle.pb",
		"ResRanking.pb",
		"ResRecharge.pb",
		"ResRechargeArenaTab.pb",
		"ResRechargeAvatarBuy.pb",
		"ResRechargeFarmTab.pb",
		"ResRecommend.pb",
		"ResRecruitTopic.pb",
		"ResRedDot.pb",
		"ResRedDotShow.pb",
		"ResRedEnvelope.pb",
		"ResRegional.pb",
		"ResRelation.pb",
		"ResReport.pb",
		"ResReputation.pb",
		"ResResource.pb",
		"ResReturningUser.pb",
		"ResReward.pb",
		"ResRewardRetrievalConf.pb",
		"ResRoguelikeGamePlay.pb",
		"ResRoguelikeLoadingMap.pb",
		"ResRoguelikeMarkGenData.pb",
		"ResRoguelikeMonsterInfo.pb",
		"ResRoguelikeProps.pb",
		"ResRoguelikeSkill.pb",
		"ResRoguelikeTalent.pb",
		"ResRoguelikeTask.pb",
		"ResRotatingScreen.pb",
		"ResScene.pb",
		"ResSceneFlowData.pb",
		"ResScenePerformanceData.pb",
		"ResSeason.pb",
		"ResSelfState.pb",
		"ResSensitiveFilter.pb",
		"ResServer.pb",
		"ResServerIdipArea.pb",
		"ResServerKvConfig.pb",
		"ResServerText.pb",
		"ResServerVip.pb",
		"ResServerWhite.pb",
		"ResSettingRights.pb",
		"ResShootBuff.pb",
		"ResShootEvent.pb",
		"ResShuttle.pb",
		"ResSnsInvitation.pb",
		"ResSnsShare.pb",
		"ResSocialPerformance.pb",
		"ResSortPlayerInfo.pb",
		"ResSound.pb",
		"ResSpecialAvatarPropScale.pb",
		"ResSpecReward.pb",
		"ResSpeedCompensationConfig.pb",
		"ResSpineEmoji.pb",
		"ResSpringRedPacket.pb",
		"ResSpringSlip.pb",
		"ResStorageCleanConfig.pb",
		"ResSuit.pb",
		"ResSummerVacationBPBuff.pb",
		"ResSuperCore.pb",
		"ResSystemChunkGroupDependency.pb",
		"ResTask.pb",
		"ResTaskPictureUrl.pb",
		"ResTDMisc.pb",
		"ResTDMonster.pb",
		"ResTDSMisc.pb",
		"ResTDSMonster.pb",
		"ResTDSWeaponLevelUpConf.pb",
		"ResTeamPhoto.pb",
		"ResTerrain.pb",
		"ResTestHotResOver.pb",
		"ResText.pb",
		"ResTextClient.pb",
		"ResTextConversion.pb",
		"ResTextLua.pb",
		"ResTextLuaNR3E.pb",
		"ResTextLuaRelation.pb",
		"ResTextLuaXiaowo.pb",
		"ResTextRuleDesc.pb",
		"ResThirdPartyEnv.pb",
		"ResTlogAnalyse.pb",
		"ResTradingCard.pb",
		"ResTranslationData.pb",
		"ResTYCBodyArmor.pb",
		"ResTYCBuff.pb",
		"ResTYCBullet.pb",
		"ResTYCDrone.pb",
		"ResTYCDrop.pb",
		"ResTYCItem.pb",
		"ResTYCMapPara.pb",
		"ResTYCMisc.pb",
		"ResTYCMonster.pb",
		"ResTYCNormalBuilding.pb",
		"ResTYCProp.pb",
		"ResTYCRating.pb",
		"ResTYCReincarnate.pb",
		"ResTYCScientist.pb",
		"ResTYCSkill.pb",
		"ResTYCSkin.pb",
		"ResTYCStore.pb",
		"ResTYCTower.pb",
		"ResTYCTrap.pb",
		"ResTYCTrigger.pb",
		"ResTYCVehicle.pb",
		"ResTYCWeapon.pb",
		"ResTYCWeaponAttr.pb",
		"ResTYCWeaponSkin.pb",
		"ResUGCAchievement.pb",
		"ResUGCAIBuild.pb",
		"ResUGCAigc.pb",
		"ResUGCAIInfo.pb",
		"ResUGCAIMagicGraph.pb",
		"ResUGCAIModule.pb",
		"ResUGCAudio.pb",
		"ResUGCAudioTab.pb",
		"ResUgcBulletSelector.pb",
		"ResUGCCameraPreset.pb",
		"ResUGCCameraTemplateSetting.pb",
		"ResUgcCollection.pb",
		"ResUgcCreatorHomePage.pb",
		"ResUGCCustomEquip.pb",
		"ResUGCDialogue.pb",
		"ResUGCEditor.pb",
		"ResUGCEditorSkill.pb",
		"ResUGCEditorSkillConfig.pb",
		"ResUGCEditorUI.pb",
		"ResUgcEntranceBubble.pb",
		"ResUgcExpData.pb",
		"ResUgcForbiddenTopics.pb",
		"ResUGCGuideTips.pb",
		"ResUgcImageDisplay.pb",
		"ResUGCLobbyNavIcons.pb",
		"ResUgcMap.pb",
		"ResUgcMatchRoom.pb",
		"ResUgcMgr.pb",
		"ResUGCMontage.pb",
		"ResUgcMusicFloor.pb",
		"ResUgcNewYear.pb",
		"ResUGCNPCAppearance.pb",
		"ResUGCNPCInfo.pb",
		"ResUGCOfiicialAnim.pb",
		"ResUGCOMDAbilityCustom.pb",
		"ResUGCOMDDropPool.pb",
		"ResUGCOMDLevelPath.pb",
		"ResUGCPACustomAttribute.pb",
		"ResUGCParticle.pb",
		"ResUgcParticleMaterial.pb",
		"ResUgcParticleParamNameToIndex.pb",
		"ResUGCParticleTab.pb",
		"ResUgcPathConfig.pb",
		"ResUGCPlacableItem.pb",
		"ResUGCProgramPropItem.pb",
		"ResUgcPropItem.pb",
		"ResUgcQuickChat.pb",
		"ResUGCRanking.pb",
		"ResUgcResConf.pb",
		"ResUgcRocketBulletSelector.pb",
		"ResUGCRoundGameProps.pb",
		"ResUGCSetting.pb",
		"ResUGCSetting_AbilityProp.pb",
		"ResUGCSkillIndicator.pb",
		"ResUGCSkillStateEffectMutex.pb",
		"ResUgcStarWorld.pb",
		"ResUGCTextHigh.pb",
		"ResUGCWeaponSkinIP.pb",
		"ResUgcWhiteList.pb",
		"ResUltraman.pb",
		"ResUniversalPreparation.pb",
		"ResUpdateForesight.pb",
		"ResUserLabel.pb",
		"ResVAFarmMoreGamePlayConfig.pb",
		"ResViewOptionSetting.pb",
		"ResWelfare.pb",
		"ResWhiteList.pb",
		"ResWishingTree.pb",
		"ResWndSubTab.pb",
		"ResWolfKillComeBack.pb",
		"ResWolfKillDecoration.pb",
		"ResWolfKillRoadToMaster.pb",
		"ResWolfKillRoleInfoBase.pb",
		"ResWolfKillRoleInfoRate.pb",
		"ResWolfKillSeasonReward.pb",
		"ResWolfTeamChest.pb",
		"ResWorkLevelData.pb",
		"ResWWXGame.pb",
		"ResWXHud.pb",
		"ResWXSubscription.pb",
		"ResXiaowoFarming.pb",
		"ResXiaowoHot.pb",
		"ResXiaowoLv.pb",
		"ResXiaowoMap.pb",
		"ResXiaowoPiano.pb",
		"ResXiaowoProps.pb",
		"ResXiaowoSysConf.pb",
	},
}

return PBPath