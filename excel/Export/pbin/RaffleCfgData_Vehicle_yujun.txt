com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_yujun.xlsx sheet:奖池-载具卡池
rows {
  poolId: 8000101
  coinType: 3
  oneDraw {
    type: RPT_PointTiered
    ptPrice {
      prices: 3
      prices: 6
      prices: 12
      prices: 18
      prices: 30
      points: 0
      points: 1
      points: 2
      points: 3
      points: 4
      originalPrices: 3
      originalPrices: 6
      originalPrices: 12
      originalPrices: 18
      originalPrices: 30
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
    minorGRewardGroup: 1
    minorGRewardWeight: 100
    minorGRewardPeriod: 3
  }
  maxLimit: 9999999
  mallVoucherId: 224
  hasLuckyRule: true
}
rows {
  poolId: 8000102
  coinType: 3
  oneDraw {
    type: RPT_PointTiered
    ptPrice {
      prices: 58
      prices: 88
      points: 5
      points: 6
      originalPrices: 58
      originalPrices: 88
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
    minorGRewardGroup: 1
    minorGRewardWeight: 100
    minorGRewardPeriod: 3
  }
  maxLimit: 9999999
  hasLuckyRule: true
}
rows {
  poolId: 8000104
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000105
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000106
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000107
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000108
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000109
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000110
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000601
  coinType: 3
  oneDraw {
    type: RPT_PointTiered
    ptPrice {
      prices: 3
      prices: 6
      prices: 12
      prices: 18
      prices: 30
      points: 0
      points: 1
      points: 2
      points: 3
      points: 4
      originalPrices: 3
      originalPrices: 6
      originalPrices: 12
      originalPrices: 18
      originalPrices: 30
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
    minorGRewardGroup: 1
    minorGRewardWeight: 100
    minorGRewardPeriod: 3
  }
  maxLimit: 9999999
  mallVoucherId: 224
  hasLuckyRule: true
}
rows {
  poolId: 8000602
  coinType: 3
  oneDraw {
    type: RPT_PointTiered
    ptPrice {
      prices: 58
      prices: 88
      points: 5
      points: 6
      originalPrices: 58
      originalPrices: 88
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
    minorGRewardGroup: 1
    minorGRewardWeight: 100
    minorGRewardPeriod: 3
  }
  maxLimit: 9999999
  hasLuckyRule: true
}
rows {
  poolId: 8000604
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000605
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000606
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000607
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000608
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000609
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
rows {
  poolId: 8000610
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  maxLimit: 9999999
  dispersion {
    rewardRefreshPeriod: 100
  }
  hasLuckyRule: true
}
