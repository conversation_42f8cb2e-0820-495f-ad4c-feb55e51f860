com.tencent.wea.xlsRes.table_UGCEditorBagTab
excel/xls/U_UGC编辑器.xlsx sheet:BagTab
rows {
  id: 1
  name: "积木"
  icon_normal: "T_UGC_Bag_Icon_Tab_Block"
  icon_hover: "T_UGC_Bag_Icon_Tab_Block"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  fileName: "UGCActorInfo_Construction"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 2
  name: "玩法"
  icon_normal: "T_UGC_Bag_Icon_Tab_PlayMethod"
  icon_hover: "T_UGC_Bag_Icon_Tab_PlayMethod"
  DefalutShowByType: "0:1,400:0"
  isShowInFold: 1
  order: 3
  fileName: "UGCActorInfo_Stratagem"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 3
  name: "自然"
  icon_normal: "T_UGC_Bag_Icon_Tab_Nature"
  icon_hover: "T_UGC_Bag_Icon_Tab_Nature"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  fileName: "UGCActorInfo_Nature"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 4
  name: "建筑"
  icon_normal: "T_UGC_Bag_Icon_Tab_Build"
  icon_hover: "T_UGC_Bag_Icon_Tab_Build"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  fileName: "UGCActorInfo_Building"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 5
  name: "装扮"
  icon_normal: "T_UGC_Bag_Icon_Dressup"
  icon_hover: "T_UGC_Bag_Icon_Dressup"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 7
  fileName: "UGCActorInfo_Decorate"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 6
  name: "生活"
  icon_normal: "T_UGC_Bag_Icon_Tab_Indoor"
  icon_hover: "T_UGC_Bag_Icon_Tab_Indoor"
  DefalutShowByType: "0:0,400:1"
  isShowInFold: 1
  order: 6
  fileName: "UGCActorInfo_Furniture"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 7
  name: "资源"
  icon_normal: "T_UGC_Bag_Icon_Tab_Group"
  icon_hover: "T_UGC_Bag_Icon_Tab_Group"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 10
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 8
  name: "收藏"
  icon_normal: "T_UGC_Bag_Icon_Tab_Collect"
  icon_hover: "T_UGC_Bag_Icon_Tab_Collect"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 11
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 9
  parentTabId: 1
  name: "基础"
  icon_normal: "T_UGC_Bag_Icon_Basic"
  icon_hover: "T_UGC_Bag_Icon_Basic"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 10
  parentTabId: 1
  name: "柱体"
  icon_normal: "T_UGC_Bag_Icon_Cylinder"
  icon_hover: "T_UGC_Bag_Icon_Cylinder"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 11
  parentTabId: 1
  name: "台锥"
  icon_normal: "T_UGC_Bag_Icon_Cones"
  icon_hover: "T_UGC_Bag_Icon_Cones"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 12
  parentTabId: 1
  name: "弧形"
  icon_normal: "T_UGC_Bag_Icon_Cambered"
  icon_hover: "T_UGC_Bag_Icon_Cambered"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 13
  parentTabId: 1
  name: "特殊"
  icon_normal: "T_UGC_Bag_Icon_Special"
  icon_hover: "T_UGC_Bag_Icon_Special"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 14
  parentTabId: 2
  name: "流程"
  icon_normal: "T_UGC_Bag_Icon_Process"
  icon_hover: "T_UGC_Bag_Icon_Process"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 15
  parentTabId: 2
  name: "机关"
  icon_normal: "T_UGC_Bag_Icon_Stratagem"
  icon_hover: "T_UGC_Bag_Icon_Stratagem"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 16
  parentTabId: 2
  name: "逻辑"
  icon_normal: "T_UGC_Bag_Icon_Logic"
  icon_hover: "T_UGC_Bag_Icon_Logic"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 17
  parentTabId: 2
  name: "功能"
  icon_normal: "T_UGC_Bag_Icon_Function"
  icon_hover: "T_UGC_Bag_Icon_Function"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 18
  parentTabId: 2
  name: "物理"
  icon_normal: "T_UGC_Bag_Icon_Physics"
  icon_hover: "T_UGC_Bag_Icon_Physics"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 89
  parentTabId: 2
  name: "乐园"
  icon_normal: "T_UGC_Bag_Icon_Park"
  icon_hover: "T_UGC_Bag_Icon_Park"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 6
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 31
  parentTabId: 3
  name: "摇钱树"
  icon_normal: "T_UGC_Bag_Icon_CashCow"
  icon_hover: "T_UGC_Bag_Icon_CashCow"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 1
  gameMode: 400
  isShowInPrefab: 1
}
rows {
  id: 19
  parentTabId: 3
  name: "花草"
  icon_normal: "T_UGC_Bag_Icon_Vegetation"
  icon_hover: "T_UGC_Bag_Icon_Vegetation"
  DefalutShowByType: "0:1,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 20
  parentTabId: 3
  name: "树木"
  icon_normal: "T_UGC_Bag_Icon_Tree"
  icon_hover: "T_UGC_Bag_Icon_Tree"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 21
  parentTabId: 3
  name: "山水"
  icon_normal: "T_UGC_Bag_Icon_Iandscape"
  icon_hover: "T_UGC_Bag_Icon_Iandscape"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 6
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 22
  parentTabId: 3
  name: "气象"
  icon_normal: "T_UGC_Bag_Icon_Weather"
  icon_hover: "T_UGC_Bag_Icon_Weather"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 7
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 23
  parentTabId: 3
  name: "动物"
  icon_normal: "T_UGC_Bag_Icon_Animal"
  icon_hover: "T_UGC_Bag_Icon_Animal"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 8
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 25
  parentTabId: 4
  name: "地面"
  icon_normal: "T_UGC_Bag_Icon_Road"
  icon_hover: "T_UGC_Bag_Icon_Road"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 26
  parentTabId: 4
  name: "栏杆"
  icon_normal: "T_UGC_Bag_Icon_Handrail"
  icon_hover: "T_UGC_Bag_Icon_Handrail"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 27
  parentTabId: 4
  name: "门窗"
  icon_normal: "T_UGC_Bag_Icon_Window"
  icon_hover: "T_UGC_Bag_Icon_Window"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 28
  parentTabId: 4
  name: "屋顶"
  icon_normal: "T_UGC_Bag_Icon_Roof"
  icon_hover: "T_UGC_Bag_Icon_Roof"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 24
  parentTabId: 4
  name: "建筑物"
  icon_normal: "T_UGC_Bag_Icon_Facilities"
  icon_hover: "T_UGC_Bag_Icon_Facilities"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 6
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 29
  parentTabId: 5
  name: "装饰"
  icon_normal: "T_UGC_Bag_Icon_Decoration"
  icon_hover: "T_UGC_Bag_Icon_Decoration"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 30
  parentTabId: 5
  name: "特效"
  icon_normal: "T_UGC_Bag_Icon_Aura"
  icon_hover: "T_UGC_Bag_Icon_Aura"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 48
  parentTabId: 5
  name: "标识"
  icon_normal: "T_UGC_Bag_Icon_Carpet"
  icon_hover: "T_UGC_Bag_Icon_Carpet"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 32
  parentTabId: 6
  name: "床椅"
  icon_normal: "T_UGC_Bag_Icon_Seat"
  icon_hover: "T_UGC_Bag_Icon_Seat"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 33
  parentTabId: 6
  name: "桌柜"
  icon_normal: "T_UGC_Bag_Icon_Desk"
  icon_hover: "T_UGC_Bag_Icon_Desk"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 34
  parentTabId: 6
  name: "清洁"
  icon_normal: "T_UGC_Bag_Icon_Clean"
  icon_hover: "T_UGC_Bag_Icon_Clean"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 35
  parentTabId: 6
  name: "烹饪"
  icon_normal: "T_UGC_Bag_Icon_Kitchen"
  icon_hover: "T_UGC_Bag_Icon_Kitchen"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 36
  parentTabId: 6
  name: "娱乐"
  icon_normal: "T_UGC_Bag_Icon_Amusement"
  icon_hover: "T_UGC_Bag_Icon_Amusement"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 6
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 37
  parentTabId: 6
  name: "照明"
  icon_normal: "T_UGC_Bag_Icon_Illumination"
  icon_hover: "T_UGC_Bag_Icon_Illumination"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 7
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 38
  parentTabId: 7
  name: "我的"
  icon_normal: "T_UGC_Bag_Icon_Mine"
  icon_hover: "T_UGC_Bag_Icon_Mine"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 39
  parentTabId: 7
  name: "示例"
  icon_normal: "T_UGC_Bag_Icon_Sample"
  icon_hover: "T_UGC_Bag_Icon_Sample"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: -1
  isShowInPrefab: 0
}
rows {
  id: 40
  parentTabId: 7
  name: "社区"
  icon_normal: "T_UGC_Bag_Icon_Community"
  icon_hover: "T_UGC_Bag_Icon_Community"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: -1
  isShowInPrefab: 0
}
rows {
  id: 41
  parentTabId: 8
  name: "全部"
  icon_normal: "T_UGC_Bag_Icon_All"
  icon_hover: "T_UGC_Bag_Icon_All"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 42
  parentTabId: 8
  name: "我的"
  icon_normal: "T_UGC_Bag_Icon_Element"
  icon_hover: "T_UGC_Bag_Icon_Element"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 2
  gameMode: -1
  isShowInPrefab: 0
}
rows {
  id: 43
  parentTabId: 8
  name: "模组"
  icon_normal: "T_UGC_Bag_Icon_Module"
  icon_hover: "T_UGC_Bag_Icon_Module"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 3
  gameMode: -1
  isShowInPrefab: 0
}
rows {
  id: 44
  name: "搜索"
  icon_normal: "T_UGC_Bag_Icon_Tab_Collect"
  icon_hover: "T_UGC_Bag_Icon_Tab_Collect"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 0
  order: 9
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 45
  parentTabId: 44
  name: "全部"
  icon_normal: "T_UGC_Bag_Icon_All"
  icon_hover: "T_UGC_Bag_Icon_All"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 46
  parentTabId: 44
  name: "我的"
  icon_normal: "T_UGC_Bag_Icon_Element"
  icon_hover: "T_UGC_Bag_Icon_Element"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: -1
  isShowInPrefab: 1
}
rows {
  id: 47
  parentTabId: 44
  name: "社区"
  icon_normal: "T_UGC_Bag_Icon_Module"
  icon_hover: "T_UGC_Bag_Icon_Module"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 49
  name: "生物"
  icon_normal: "T_UGC_Bag_Icon_Tab_Character"
  icon_hover: "T_UGC_Bag_Icon_Tab_Character"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 7
  fileName: "UGCActorInfo_Character"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 50
  parentTabId: 49
  name: "星宝"
  icon_normal: "T_UGC_Bag_Icon_StarBaby"
  icon_hover: "T_UGC_Bag_Icon_StarBaby"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 51
  parentTabId: 2
  name: "载具"
  icon_normal: "T_UGC_Bag_Icon_Steer"
  icon_hover: "T_UGC_Bag_Icon_Steer"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 6
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 52
  parentTabId: 3
  name: "种植"
  icon_normal: "T_UGC_Bag_Icon_FarmSeed"
  icon_hover: "T_UGC_Bag_Icon_FarmSeed"
  DefalutShowByType: "0:0,400:1"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 53
  parentTabId: 8
  name: "元件"
  icon_normal: "T_UGC_Bag_Icon_Element"
  icon_hover: "T_UGC_Bag_Icon_Element"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 54
  parentTabId: 44
  name: "元件"
  icon_normal: "T_UGC_Bag_Icon_Element"
  icon_hover: "T_UGC_Bag_Icon_Element"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 55
  parentTabId: 8
  name: "资源"
  icon_normal: "T_UGC_Bag_Icon_Tab_Group"
  icon_hover: "T_UGC_Bag_Icon_Tab_Group"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 56
  parentTabId: 44
  name: "资源"
  icon_normal: "T_UGC_Bag_Icon_Tab_Group"
  icon_hover: "T_UGC_Bag_Icon_Tab_Group"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 57
  parentTabId: 7
  name: "模组"
  icon_normal: "T_UGC_Bag_Icon_Module"
  icon_hover: "T_UGC_Bag_Icon_Module"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 58
  parentTabId: 7
  name: "人物"
  icon_normal: "T_UGC_Bag_Icon_Tab_Character"
  icon_hover: "T_UGC_Bag_Icon_Tab_Character"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 59
  parentTabId: 7
  name: "载具"
  icon_normal: "T_UGC_Bag_Icon_Steer"
  icon_hover: "T_UGC_Bag_Icon_Steer"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 6
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 60
  parentTabId: 7
  name: "武器"
  icon_normal: "T_UGC_Bag_Icon_Firearm"
  icon_hover: "T_UGC_Bag_Icon_Firearm"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 7
  gameMode: 32
  gameMode: 41
  isShowInPrefab: 0
}
rows {
  id: 61
  parentTabId: 3
  name: "果实"
  icon_normal: "T_UGC_Bag_Icon_FarmFruit"
  icon_hover: "T_UGC_Bag_Icon_FarmFruit"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 62
  name: "自定义"
  icon_normal: "T_UGC_Bag_Icon_Tab_Custom_First"
  icon_hover: "T_UGC_Bag_Icon_Tab_Custom_First"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 9
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 63
  parentTabId: 62
  name: "预设"
  icon_normal: "T_UGC_Bag_Icon_Tab_Custom_First"
  icon_hover: "T_UGC_Bag_Icon_Tab_Custom_First"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 64
  parentTabId: 2
  name: "相机"
  icon_normal: "T_UGC_Bag_Icon_Camera"
  icon_hover: "T_UGC_Bag_Icon_Camera"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 8
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 65
  parentTabId: 1
  name: "造型"
  icon_normal: "T_UGC_Bag_Icon_Modeling"
  icon_hover: "T_UGC_Bag_Icon_Modeling"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 6
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 66
  parentTabId: 6
  name: "手持"
  icon_normal: "T_UGC_Bag_Icon_Handheld"
  icon_hover: "T_UGC_Bag_Icon_Handheld"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 8
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 67
  parentTabId: 62
  name: "施工中"
  icon_normal: "T_UGC_Bag_Icon_Homemade"
  icon_hover: "T_UGC_Bag_Icon_Homemade"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 68
  parentTabId: 49
  name: "施工中"
  icon_normal: "T_UGC_Bag_Icon_Homemade"
  icon_hover: "T_UGC_Bag_Icon_Homemade"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 69
  name: "测试"
  icon_normal: "T_UGC_Bag_Icon_Else"
  icon_hover: "T_UGC_Bag_Icon_Else"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 10
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 70
  parentTabId: 69
  name: "测试"
  icon_normal: "T_UGC_Bag_Icon_Else"
  icon_hover: "T_UGC_Bag_Icon_Else"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 71
  parentTabId: 7
  name: "装备"
  icon_normal: "T_UGC_Bag_Icon_Equipment"
  icon_hover: "T_UGC_Bag_Icon_Equipment"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 8
  gameMode: 32
  gameMode: 41
  isShowInPrefab: 0
}
rows {
  id: 72
  parentTabId: 7
  name: "道具"
  icon_normal: "T_UGC_Bag_Icon_ModelProps"
  icon_hover: "T_UGC_Bag_Icon_ModelProps"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 9
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 73
  parentTabId: 49
  name: "暗星"
  icon_normal: "T_UGC_Bag_Icon_StarBaby"
  icon_hover: "T_UGC_Bag_Icon_StarBaby"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 74
  parentTabId: 69
  name: "内部"
  icon_normal: "T_UGC_Bag_Icon_Else"
  icon_hover: "T_UGC_Bag_Icon_Else"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 75
  parentTabId: 69
  name: "乐园"
  icon_normal: "T_UGC_Bag_Icon_Else"
  icon_hover: "T_UGC_Bag_Icon_Else"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 76
  parentTabId: 69
  name: "测试4"
  icon_normal: "T_UGC_Bag_Icon_Else"
  icon_hover: "T_UGC_Bag_Icon_Else"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 77
  parentTabId: 69
  name: "智能"
  icon_normal: "T_UGC_Bag_Icon_Else"
  icon_hover: "T_UGC_Bag_Icon_Else"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 5
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 78
  parentTabId: 5
  name: "段位"
  icon_normal: "T_UGC_Bag_Icon_Trophy"
  icon_hover: "T_UGC_Bag_Icon_Trophy"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 79
  parentTabId: 44
  name: "AI模组"
  icon_normal: "T_UGC_Bag_Icon_AI"
  icon_hover: "T_UGC_Bag_Icon_AI"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 80
  parentTabId: 49
  name: "兽人"
  icon_normal: "T_UGC_Bag_Icon_StarBaby"
  icon_hover: "T_UGC_Bag_Icon_StarBaby"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 81
  name: "塔防"
  icon_normal: "T_UGCCategoryIcon_TowerDefence_A"
  icon_hover: "T_UGCCategoryIcon_TowerDefence_A"
  DefalutShowByType: "36:1"
  isShowInFold: 1
  order: 1
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 82
  parentTabId: 81
  name: "塔防"
  icon_normal: "T_UGCCategoryIcon_TowerDefence_B"
  icon_hover: "T_UGCCategoryIcon_TowerDefence_B"
  DefalutShowByType: "36:1"
  isShowInFold: 1
  order: 1
  gameMode: 36
  isShowInPrefab: 0
}
rows {
  id: 83
  parentTabId: 49
  name: "四足"
  icon_normal: "T_UGC_Bag_Icon_StarBaby"
  icon_hover: "T_UGC_Bag_Icon_StarBaby"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 4
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 84
  name: "AI助手推荐"
  icon_normal: "T_UGC_Bag_Icon_Tab_Collect"
  icon_hover: "T_UGC_Bag_Icon_Tab_Collect"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 0
  order: 9
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 1
}
rows {
  id: 85
  parentTabId: 84
  name: "全部"
  icon_normal: "T_UGC_Bag_Icon_All"
  icon_hover: "T_UGC_Bag_Icon_All"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 86
  parentTabId: 84
  name: "元件"
  icon_normal: "T_UGC_Bag_Icon_Element"
  icon_hover: "T_UGC_Bag_Icon_Element"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 87
  parentTabId: 84
  name: "社区"
  icon_normal: "T_UGC_Bag_Icon_Module"
  icon_hover: "T_UGC_Bag_Icon_Module"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 3
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  isShowInPrefab: 0
}
rows {
  id: 88
  parentTabId: 1
  name: "智能"
  icon_normal: "T_UGC_Bag_Icon_AI"
  icon_hover: "T_UGC_Bag_Icon_AI"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 7
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 500
}
rows {
  id: 90
  parentTabId: 7
  name: "编程"
  icon_normal: "T_UGC_Bag_Icon_Module"
  icon_hover: "T_UGC_Bag_Icon_Module"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 10
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 500
}
rows {
  id: 91
  name: "模型"
  icon_normal: "T_UGC_Bag_Icon_Tab_Indoor"
  icon_hover: "T_UGC_Bag_Icon_Tab_Indoor"
  DefalutShowByType: "0:0,400:1"
  isShowInFold: 1
  order: 8
  fileName: "UGCActorInfo_AIGCModel"
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 92
  parentTabId: 91
  name: "我的"
  icon_normal: "T_UGC_Bag_Icon_Mine"
  icon_hover: "T_UGC_Bag_Icon_Mine"
  DefalutShowByType: "0:1,400:1"
  isShowInFold: 1
  order: 1
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
rows {
  id: 93
  parentTabId: 91
  name: "基础"
  icon_normal: "T_UGC_Bag_Icon_Cylinder"
  icon_hover: "T_UGC_Bag_Icon_Cylinder"
  DefalutShowByType: "0:0,400:0"
  isShowInFold: 1
  order: 2
  gameMode: 1
  gameMode: 2
  gameMode: 29
  gameMode: 32
  gameMode: 41
  gameMode: 400
  gameMode: 500
  gameMode: 36
  isShowInPrefab: 1
}
