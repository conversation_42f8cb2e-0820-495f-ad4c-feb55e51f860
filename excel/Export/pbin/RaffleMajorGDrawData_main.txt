com.tencent.wea.xlsRes.table_RaffleMajorGDrawData
excel/xls/C_抽奖奖池_主表.xlsx sheet:大保底抽数掉率
rows {
  id: 30001
  poolId: 3000
  draw: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏饰品</>"
}
rows {
  id: 30002
  poolId: 3000
  draw: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 30003
  poolId: 3000
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypePurple>非凡时装</>"
}
rows {
  id: 30004
  poolId: 3000
  draw: 90
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 30005
  poolId: 3000
  draw: 140
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏时装</>"
}
rows {
  id: 30006
  poolId: 3000
  draw: 141
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  noSkip: true
}
rows {
  id: 30007
  poolId: 3000
  draw: 300
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 4
  poolId: 13
  draw: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 70001
  poolId: 7000
  draw: 15
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 50001
  poolId: 5000
  draw: 2
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 50002
  poolId: 51031
  draw: 3
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 300021
  poolId: 30002
  draw: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏饰品</>"
}
rows {
  id: 300022
  poolId: 30002
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypePurple>非凡时装</>"
}
rows {
  id: 300023
  poolId: 30002
  draw: 90
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 300024
  poolId: 30002
  draw: 140
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏时装</>"
}
rows {
  id: 300025
  poolId: 30002
  draw: 300
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 500301
  poolId: 5003
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 500401
  poolId: 5004
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 500501
  poolId: 5005
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 530101
  poolId: 5301
  draw: 240
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 530201
  poolId: 5302
  draw: 240
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 50003
  poolId: 51061
  draw: 3
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 300031
  poolId: 30003
  draw: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏饰品</>"
}
rows {
  id: 300032
  poolId: 30003
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypePurple>非凡时装</>"
}
rows {
  id: 300033
  poolId: 30003
  draw: 90
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 300034
  poolId: 30003
  draw: 140
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏时装</>"
}
rows {
  id: 300035
  poolId: 30003
  draw: 300
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5107000
  poolId: 51070
  draw: 3
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5108000
  poolId: 51080
  draw: 3
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5016001
  poolId: 5016
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5016002
  poolId: 5016
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 300041
  poolId: 30004
  draw: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏饰品</>"
}
rows {
  id: 300043
  poolId: 30004
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypePurple>非凡时装</>"
}
rows {
  id: 300044
  poolId: 30004
  draw: 90
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 300045
  poolId: 30004
  draw: 140
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏时装</>"
}
rows {
  id: 300047
  poolId: 30004
  draw: 300
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5016003
  poolId: 5019
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5016004
  poolId: 5019
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  noSkip: true
}
rows {
  id: 5016005
  poolId: 5020
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5016006
  poolId: 5020
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  noSkip: true
}
rows {
  id: 5016007
  poolId: 5021
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5016008
  poolId: 5021
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  noSkip: true
}
rows {
  id: 5501001
  poolId: 55010
  draw: 10
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501002
  poolId: 55010
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501003
  poolId: 55010
  draw: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501004
  poolId: 55010
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501005
  poolId: 55010
  draw: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501006
  poolId: 55010
  draw: 110
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501007
  poolId: 55010
  draw: 140
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501008
  poolId: 55010
  draw: 170
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501009
  poolId: 55010
  draw: 210
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501010
  poolId: 55010
  draw: 250
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5501101
  poolId: 55011
  draw: 50
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5501102
  poolId: 55011
  draw: 80
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 5501103
  poolId: 55011
  draw: 120
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 5404001
  poolId: 5404
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5405001
  poolId: 5405
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 300048
  poolId: 5303
  draw: 280
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 502501
  poolId: 5025
  draw: 10
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 502601
  poolId: 5026
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 502602
  poolId: 5026
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 502701
  poolId: 5027
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 502801
  poolId: 5028
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 502901
  poolId: 5029
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 300051
  poolId: 30005
  draw: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏饰品</>"
}
rows {
  id: 300052
  poolId: 30005
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypePurple>非凡时装</>"
}
rows {
  id: 300053
  poolId: 30005
  draw: 90
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 300054
  poolId: 30005
  draw: 140
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏时装</>"
}
rows {
  id: 300055
  poolId: 30005
  draw: 300
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 503001
  poolId: 5030
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 503101
  poolId: 5031
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 503201
  poolId: 5032
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5116000
  poolId: 51160
  draw: 3
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5117000
  poolId: 51170
  draw: 3
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5502001
  poolId: 55020
  draw: 10
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502002
  poolId: 55020
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502003
  poolId: 55020
  draw: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502004
  poolId: 55020
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502005
  poolId: 55020
  draw: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502006
  poolId: 55020
  draw: 110
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502007
  poolId: 55020
  draw: 140
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502008
  poolId: 55020
  draw: 170
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502009
  poolId: 55020
  draw: 210
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502010
  poolId: 55020
  draw: 250
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5502011
  poolId: 55021
  draw: 50
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5502012
  poolId: 55021
  draw: 80
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 5502013
  poolId: 55021
  draw: 120
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 503701
  poolId: 5037
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 503801
  poolId: 5038
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5700107
  poolId: 57001
  draw: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5700105
  poolId: 57001
  draw: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5700103
  poolId: 57001
  draw: 60
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5700106
  poolId: 57001
  draw: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5700101
  poolId: 57001
  draw: 150
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5700207
  poolId: 57002
  draw: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5700203
  poolId: 57002
  draw: 60
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5700206
  poolId: 57002
  draw: 120
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 300071
  poolId: 30007
  draw: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏饰品</>"
}
rows {
  id: 300072
  poolId: 30007
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypePurple>非凡时装</>"
}
rows {
  id: 300073
  poolId: 30007
  draw: 90
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 300074
  poolId: 30007
  draw: 140
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  text: "再祈愿<DRYellow2>%s</>次后必得<DRTypeOrange>臻藏时装</>"
}
rows {
  id: 300075
  poolId: 30007
  draw: 300
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 511410
  poolId: 51141
  draw: 5
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 511510
  poolId: 51151
  draw: 5
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 5151155
  poolId: 51155
  draw: 10
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 5151156
  poolId: 51156
  draw: 10
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 5151157
  poolId: 51157
  draw: 10
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 5151158
  poolId: 51158
  draw: 10
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 5151159
  poolId: 51159
  draw: 10
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 530501
  poolId: 5305
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 530502
  poolId: 5305
  draw: 80
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 504301
  poolId: 5043
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 504401
  poolId: 5044
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 8005001
  poolId: 8005
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 504803
  poolId: 5306
  draw: 280
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 530401
  poolId: 5304
  draw: 49
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 530402
  poolId: 5304
  draw: 70
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  noSkip: true
}
rows {
  id: 530403
  poolId: 5304
  draw: 95
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  noSkip: true
}
rows {
  id: 600001
  poolId: 6000
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 600002
  poolId: 6000
  draw: 75
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 600003
  poolId: 6000
  draw: 165
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 600004
  poolId: 6000
  draw: 210
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 505301
  poolId: 5053
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 505401
  poolId: 5054
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 530102
  poolId: 5307
  draw: 240
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 50004
  poolId: 5055
  draw: 2
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 504101
  poolId: 5041
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 505801
  poolId: 5058
  draw: 2
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5503001
  poolId: 55030
  draw: 10
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503002
  poolId: 55030
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503003
  poolId: 55030
  draw: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503004
  poolId: 55030
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503005
  poolId: 55030
  draw: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503006
  poolId: 55030
  draw: 110
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503007
  poolId: 55030
  draw: 140
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503008
  poolId: 55030
  draw: 170
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503009
  poolId: 55030
  draw: 210
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503010
  poolId: 55030
  draw: 250
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5503011
  poolId: 55031
  draw: 50
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5503012
  poolId: 55031
  draw: 80
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 5503013
  poolId: 55031
  draw: 120
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 530801
  poolId: 5308
  draw: 49
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 530802
  poolId: 5308
  draw: 75
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  noSkip: true
}
rows {
  id: 530803
  poolId: 5308
  draw: 95
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  noSkip: true
}
rows {
  id: 505901
  poolId: 5059
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 506001
  poolId: 5060
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 506201
  poolId: 5062
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 506301
  poolId: 5063
  draw: 5
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
