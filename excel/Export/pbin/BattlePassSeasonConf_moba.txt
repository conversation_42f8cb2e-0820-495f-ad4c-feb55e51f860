com.tencent.wea.xlsRes.table_BattlePassSeasonConf
excel/xls/B_BP通行证_moba.xlsx sheet:赛季配置
rows {
  id: 3001
  type: BT_Moba
  expItemId: 1015
  title: "节日特惠通行证"
  time {
    beginTime {
      seconds: 1726156800
    }
    endTime {
      seconds: 1727107199
    }
  }
  lowestVersion: "0.0.0.1"
  task {
    dailyGroupId: 30001
    dailyNum: 1
    weekGroupIds: 30002
    weekGroupIds: 30003
    weekGroupIds: 30004
    weekGroupIds: 30005
    weekGroupIds: 30006
    dailyFixGroupId: 30000
  }
  free {
    enable: true
    totalRewards {
      itemId: 860081
      itemNum: 1
    }
    totalRewards {
      itemId: 6
      itemNum: 160
    }
    totalRewards {
      itemId: 200008
      itemNum: 10
    }
    totalRewards {
      itemId: 3541
      itemNum: 320
    }
    totalRewards {
      itemId: 711119
      itemNum: 1
    }
    totalRewards {
      itemId: 300101
      itemNum: 12
    }
    totalRewards {
      itemId: 200006
      itemNum: 6
    }
    totalRewards {
      itemId: 711118
      itemNum: 1
    }
    totalRewards {
      itemId: 711116
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 4
    }
    totalRewards {
      itemId: 711120
      itemNum: 1
    }
    totalRewards {
      itemId: 711130
      itemNum: 1
    }
    totalRewards {
      itemId: 301121
      itemNum: 1
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 620484
      itemNum: 1
    }
    totalRewards {
      itemId: 200008
      itemNum: 36
    }
    totalRewards {
      itemId: 3541
      itemNum: 1120
    }
    totalRewards {
      itemId: 711079
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 12
    }
    totalRewards {
      itemId: 200006
      itemNum: 28
    }
    totalRewards {
      itemId: 840149
      itemNum: 1
    }
    totalRewards {
      itemId: 711077
      itemNum: 1
    }
    totalRewards {
      itemId: 820105
      itemNum: 1
    }
    totalRewards {
      itemId: 300103
      itemNum: 4
    }
    totalRewards {
      itemId: 711083
      itemNum: 1
    }
    totalRewards {
      itemId: 870020
      itemNum: 1
    }
    totalRewards {
      itemId: 711082
      itemNum: 1
    }
    totalRewards {
      itemId: 403560
      itemNum: 1
    }
    highlightRewards {
      itemId: 620484
      itemNum: 1
    }
    highlightRewards {
      itemId: 200008
      itemNum: 36
    }
    highlightRewards {
      itemId: 3541
      itemNum: 1120
    }
    highlightRewards {
      itemId: 711079
      itemNum: 1
    }
    highlightRewards {
      itemId: 300102
      itemNum: 12
    }
    highlightRewards {
      itemId: 200006
      itemNum: 28
    }
    highlightRewards {
      itemId: 840149
      itemNum: 1
    }
    highlightRewards {
      itemId: 711077
      itemNum: 1
    }
    highlightRewards {
      itemId: 820105
      itemNum: 1
    }
    highlightRewards {
      itemId: 300103
      itemNum: 4
    }
    highlightRewards {
      itemId: 711083
      itemNum: 1
    }
    highlightRewards {
      itemId: 870020
      itemNum: 1
    }
    highlightRewards {
      itemId: 711082
      itemNum: 1
    }
    highlightRewards {
      itemId: 403560
      itemNum: 1
    }
    price {
      coinType: 1
      price: 198
      midas: "18_3_1"
    }
    replacement {
      itemId: 6
      itemNum: 200
    }
  }
  level {
    coinType: 1
    price: 20
    midas: "18_3_4"
  }
  grandRewardId: 301121
  expiredMailId: 57
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Wolf_S1_Main"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_NR3E3_S1_Show_Moba"
  totalShowViewPath: "UI_NewBattlePass_Wolf_S1_TotalShow"
  mainViewCameraTag: "Camera_Arena_Decorate"
  totalShowViewCameraTag: "Camera_Arena_Decorate_Total"
  rewardViewPath: "UI_NewBattlePass_Wolf_S1_Reward"
  taskViewPath: "UI_NewBattlePass_Wolf_S1_TaskPage"
  helpId: 165
  midTitle1: "节日限定特惠"
  midTitle2: "峡谷通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级"
  titleIcon: "T_BattlePass_Icon_PremiumPass"
  normalBattlePassId: 102016
  gloryBattlePassId: 102017
  isShow: true
}
rows {
  id: 3002
  type: BT_Moba
  expItemId: 1015
  title: "峡谷相逢通行证"
  time {
    beginTime {
      seconds: 1729785600
    }
    endTime {
      seconds: 1732809599
    }
  }
  lowestVersion: "1.3.26.26"
  task {
    dailyGroupId: 30011
    dailyNum: 1
    weekGroupIds: 30012
    weekGroupIds: 30013
    weekGroupIds: 30014
    weekGroupIds: 30015
    weekGroupIds: 30016
    dailyFixGroupId: 30010
  }
  free {
    enable: true
    totalRewards {
      itemId: 711117
      itemNum: 1
    }
    totalRewards {
      itemId: 3541
      itemNum: 320
    }
    totalRewards {
      itemId: 200008
      itemNum: 8
    }
    totalRewards {
      itemId: 711128
      itemNum: 1
    }
    totalRewards {
      itemId: 300101
      itemNum: 12
    }
    totalRewards {
      itemId: 6
      itemNum: 160
    }
    totalRewards {
      itemId: 200006
      itemNum: 8
    }
    totalRewards {
      itemId: 711126
      itemNum: 1
    }
    totalRewards {
      itemId: 711115
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 4
    }
    totalRewards {
      itemId: 711122
      itemNum: 1
    }
    totalRewards {
      itemId: 711123
      itemNum: 1
    }
    totalRewards {
      itemId: 301126
      itemNum: 1
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 620562
      itemNum: 1
    }
    totalRewards {
      itemId: 200008
      itemNum: 32
    }
    totalRewards {
      itemId: 3541
      itemNum: 1120
    }
    totalRewards {
      itemId: 711090
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 12
    }
    totalRewards {
      itemId: 200006
      itemNum: 32
    }
    totalRewards {
      itemId: 711091
      itemNum: 1
    }
    totalRewards {
      itemId: 640064
      itemNum: 1
    }
    totalRewards {
      itemId: 711084
      itemNum: 1
    }
    totalRewards {
      itemId: 300103
      itemNum: 4
    }
    totalRewards {
      itemId: 711081
      itemNum: 1
    }
    totalRewards {
      itemId: 711088
      itemNum: 1
    }
    totalRewards {
      itemId: 711096
      itemNum: 1
    }
    totalRewards {
      itemId: 300208
      itemNum: 1
    }
    highlightRewards {
      itemId: 620562
      itemNum: 1
    }
    highlightRewards {
      itemId: 200008
      itemNum: 32
    }
    highlightRewards {
      itemId: 3541
      itemNum: 1120
    }
    highlightRewards {
      itemId: 711090
      itemNum: 1
    }
    highlightRewards {
      itemId: 300102
      itemNum: 12
    }
    highlightRewards {
      itemId: 200006
      itemNum: 32
    }
    highlightRewards {
      itemId: 711091
      itemNum: 1
    }
    highlightRewards {
      itemId: 640064
      itemNum: 1
    }
    highlightRewards {
      itemId: 711084
      itemNum: 1
    }
    highlightRewards {
      itemId: 300103
      itemNum: 4
    }
    highlightRewards {
      itemId: 711081
      itemNum: 1
    }
    highlightRewards {
      itemId: 711088
      itemNum: 1
    }
    highlightRewards {
      itemId: 711096
      itemNum: 1
    }
    highlightRewards {
      itemId: 300208
      itemNum: 1
    }
    price {
      coinType: 1
      price: 198
      midas: "18_3_1"
    }
    replacement {
      itemId: 6
      itemNum: 200
    }
  }
  level {
    coinType: 1
    price: 20
    midas: "18_3_4"
  }
  grandRewardId: 301126
  expiredMailId: 57
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Wolf_S1_Main"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_NR3E3_S1_Show_Moba"
  totalShowViewPath: "UI_NewBattlePass_Wolf_S1_TotalShow"
  mainViewCameraTag: "Camera_Arena_Decorate"
  totalShowViewCameraTag: "Camera_Arena_Decorate_Total"
  rewardViewPath: "UI_NewBattlePass_Wolf_S1_Reward"
  taskViewPath: "UI_NewBattlePass_Wolf_S1_TaskPage"
  helpId: 165
  midTitle1: "峡谷相逢"
  midTitle2: "通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级"
  titleIcon: "T_BattlePass_Icon_PremiumPass"
  normalBattlePassId: 102023
  gloryBattlePassId: 102024
  isShow: true
}
rows {
  id: 3003
  type: BT_Moba
  expItemId: 1015
  title: "峡谷相逢通行证"
  time {
    beginTime {
      seconds: 1732809600
    }
    endTime {
      seconds: 1732895999
    }
  }
  lowestVersion: "1.3.36.1"
  task {
    dailyGroupId: 30021
    dailyNum: 1
    weekGroupIds: 30022
    weekGroupIds: 30023
    weekGroupIds: 30024
    weekGroupIds: 30025
    weekGroupIds: 30026
    weekGroupIds: 30027
    weekGroupIds: 30028
    dailyFixGroupId: 30020
  }
  free {
    enable: true
    totalRewards {
      itemId: 711207
      itemNum: 1
    }
    totalRewards {
      itemId: 6
      itemNum: 160
    }
    totalRewards {
      itemId: 200008
      itemNum: 8
    }
    totalRewards {
      itemId: 3541
      itemNum: 370
    }
    totalRewards {
      itemId: 260003
      itemNum: 1
    }
    totalRewards {
      itemId: 300101
      itemNum: 17
    }
    totalRewards {
      itemId: 200006
      itemNum: 8
    }
    totalRewards {
      itemId: 711230
      itemNum: 1
    }
    totalRewards {
      itemId: 711229
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 4
    }
    totalRewards {
      itemId: 270003
      itemNum: 1
    }
    totalRewards {
      itemId: 711208
      itemNum: 1
    }
    totalRewards {
      itemId: 301141
      itemNum: 1
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 300209
      itemNum: 1
    }
    totalRewards {
      itemId: 200008
      itemNum: 32
    }
    totalRewards {
      itemId: 3541
      itemNum: 1120
    }
    totalRewards {
      itemId: 260004
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 12
    }
    totalRewards {
      itemId: 200006
      itemNum: 32
    }
    totalRewards {
      itemId: 301121
      itemNum: 1
    }
    totalRewards {
      itemId: 300210
      itemNum: 1
    }
    totalRewards {
      itemId: 711202
      itemNum: 1
    }
    totalRewards {
      itemId: 300103
      itemNum: 4
    }
    totalRewards {
      itemId: 270004
      itemNum: 1
    }
    totalRewards {
      itemId: 300211
      itemNum: 1
    }
    totalRewards {
      itemId: 711209
      itemNum: 1
    }
    totalRewards {
      itemId: 300212
      itemNum: 1
    }
    highlightRewards {
      itemId: 300209
      itemNum: 1
    }
    highlightRewards {
      itemId: 200008
      itemNum: 32
    }
    highlightRewards {
      itemId: 3541
      itemNum: 1120
    }
    highlightRewards {
      itemId: 260004
      itemNum: 1
    }
    highlightRewards {
      itemId: 300102
      itemNum: 12
    }
    highlightRewards {
      itemId: 200006
      itemNum: 32
    }
    highlightRewards {
      itemId: 301121
      itemNum: 1
    }
    highlightRewards {
      itemId: 300210
      itemNum: 1
    }
    highlightRewards {
      itemId: 711202
      itemNum: 1
    }
    highlightRewards {
      itemId: 300103
      itemNum: 4
    }
    highlightRewards {
      itemId: 270004
      itemNum: 1
    }
    highlightRewards {
      itemId: 300211
      itemNum: 1
    }
    highlightRewards {
      itemId: 711209
      itemNum: 1
    }
    highlightRewards {
      itemId: 300212
      itemNum: 1
    }
    price {
      coinType: 1
      price: 198
      midas: "18_3_1"
    }
    replacement {
      itemId: 6
      itemNum: 200
    }
  }
  level {
    coinType: 1
    price: 20
    midas: "18_3_4"
  }
  grandRewardId: 301141
  expiredMailId: 57
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Wolf_S1_Main"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_NR3E3_S1_Show_Moba"
  totalShowViewPath: "UI_NewBattlePass_Wolf_S1_TotalShow"
  mainViewCameraTag: "Camera_Arena_Decorate"
  totalShowViewCameraTag: "Camera_Arena_Decorate_Total"
  rewardViewPath: "UI_NewBattlePass_Wolf_S1_Reward"
  taskViewPath: "UI_NewBattlePass_Wolf_S1_TaskPage"
  helpId: 165
  midTitle1: "峡谷相逢"
  midTitle2: "第9赛季通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级"
  titleIcon: "T_BattlePass_Icon_PremiumPass"
  normalBattlePassId: 102025
  gloryBattlePassId: 102026
  isShow: true
}
rows {
  id: 3004
  type: BT_Moba
  expItemId: 1015
  title: "峡谷相逢通行证"
  time {
    beginTime {
      seconds: 1737043200
    }
    endTime {
      seconds: 1741881599
    }
  }
  lowestVersion: "1.3.68.1"
  task {
    dailyNum: 1
    weekGroupIds: 30032
    weekGroupIds: 30033
    weekGroupIds: 30034
    weekGroupIds: 30035
    weekGroupIds: 30036
    weekGroupIds: 30037
    weekGroupIds: 30038
    weekGroupIds: 30039
    dailyFixGroupId: 30030
  }
  free {
    enable: true
    totalRewards {
      itemId: 14
      itemNum: 14
    }
    totalRewards {
      itemId: 6
      itemNum: 160
    }
    totalRewards {
      itemId: 200008
      itemNum: 8
    }
    totalRewards {
      itemId: 3541
      itemNum: 710
    }
    totalRewards {
      itemId: 260007
      itemNum: 1
    }
    totalRewards {
      itemId: 300101
      itemNum: 30
    }
    totalRewards {
      itemId: 200006
      itemNum: 8
    }
    totalRewards {
      itemId: 711226
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 4
    }
    totalRewards {
      itemId: 270007
      itemNum: 1
    }
    totalRewards {
      itemId: 711283
      itemNum: 1
    }
    totalRewards {
      itemId: 301145
      itemNum: 1
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 320214
      itemNum: 1
    }
    totalRewards {
      itemId: 200008
      itemNum: 32
    }
    totalRewards {
      itemId: 3541
      itemNum: 2760
    }
    totalRewards {
      itemId: 260008
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 30
    }
    totalRewards {
      itemId: 200006
      itemNum: 32
    }
    totalRewards {
      itemId: 330093
      itemNum: 1
    }
    totalRewards {
      itemId: 320215
      itemNum: 1
    }
    totalRewards {
      itemId: 711310
      itemNum: 1
    }
    totalRewards {
      itemId: 300103
      itemNum: 4
    }
    totalRewards {
      itemId: 270008
      itemNum: 1
    }
    totalRewards {
      itemId: 320216
      itemNum: 1
    }
    totalRewards {
      itemId: 711291
      itemNum: 1
    }
    totalRewards {
      itemId: 320217
      itemNum: 1
    }
    highlightRewards {
      itemId: 320214
      itemNum: 1
    }
    highlightRewards {
      itemId: 260008
      itemNum: 1
    }
    highlightRewards {
      itemId: 330093
      itemNum: 1
    }
    highlightRewards {
      itemId: 270008
      itemNum: 1
    }
    highlightRewards {
      itemId: 320215
      itemNum: 1
    }
    highlightRewards {
      itemId: 711310
      itemNum: 1
    }
    highlightRewards {
      itemId: 320216
      itemNum: 1
    }
    highlightRewards {
      itemId: 711291
      itemNum: 1
    }
    highlightRewards {
      itemId: 320217
      itemNum: 1
    }
    price {
      coinType: 1
      price: 198
      midas: "18_3_1"
    }
    replacement {
      itemId: 6
      itemNum: 200
    }
  }
  payed {
    enable: true
    highlightRewards {
      itemId: 320214
      itemNum: 1
    }
    highlightRewards {
      itemId: 260008
      itemNum: 1
    }
    highlightRewards {
      itemId: 330093
      itemNum: 1
    }
    highlightRewards {
      itemId: 270008
      itemNum: 1
    }
    highlightRewards {
      itemId: 320215
      itemNum: 1
    }
    highlightRewards {
      itemId: 711310
      itemNum: 1
    }
    highlightRewards {
      itemId: 320216
      itemNum: 1
    }
    highlightRewards {
      itemId: 711291
      itemNum: 1
    }
    highlightRewards {
      itemId: 320217
      itemNum: 1
    }
    extraRewards {
      itemId: 711304
      itemNum: 1
    }
    extraRewards {
      itemId: 270009
      itemNum: 1
    }
    extraRewards {
      itemId: 1015
      itemNum: 1000
    }
    price {
      coinType: 1
      price: 596
      origin: 800
      midas: "18_3_2"
    }
    replacement {
      itemId: 6
      itemNum: 600
    }
  }
  upgrade {
    range {
      key: 1
      value: 2
    }
    price {
      coinType: 1
      price: 398
      midas: "18_3_3"
    }
    replacement {
      itemId: 6
      itemNum: 400
    }
  }
  level {
    coinType: 1
    price: 20
    midas: "18_3_4"
  }
  grandRewardId: 301145
  expiredMailId: 57
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Wolf_S1_Main"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_NR3E3_S1_Show_Moba02"
  totalShowViewPath: "UI_NewBattlePass_Wolf_S1_TotalShow"
  mainViewCameraTag: "Camera_Arena_Decorate"
  totalShowViewCameraTag: "Camera_Arena_Decorate_Total"
  rewardViewPath: "UI_NewBattlePass_Wolf_S1_Reward"
  taskViewPath: "UI_NewBattlePass_Wolf_S1_TaskPage"
  helpId: 165
  midTitle1: "峡谷相逢"
  midTitle2: "第10赛季通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级"
  titleIcon: "T_BattlePass_Icon_PremiumPass"
  normalBattlePassId: 102028
  gloryBattlePassId: 102029
  isShow: true
  expWeekLimit: 700
}
rows {
  id: 3005
  type: BT_Moba
  expItemId: 1015
  title: "峡谷相逢通行证"
  time {
    beginTime {
      seconds: 1741881600
    }
    endTime {
      seconds: 1746115199
    }
  }
  lowestVersion: "1.3.78.1"
  task {
    dailyNum: 1
    weekGroupIds: 30042
    weekGroupIds: 30043
    weekGroupIds: 30044
    weekGroupIds: 30045
    weekGroupIds: 30046
    weekGroupIds: 30047
    weekGroupIds: 30048
    dailyFixGroupId: 30040
  }
  free {
    enable: true
    totalRewards {
      itemId: 6
      itemNum: 160
    }
    totalRewards {
      itemId: 200008
      itemNum: 8
    }
    totalRewards {
      itemId: 3541
      itemNum: 620
    }
    totalRewards {
      itemId: 711131
      itemNum: 1
    }
    totalRewards {
      itemId: 300101
      itemNum: 24
    }
    totalRewards {
      itemId: 260012
      itemNum: 1
    }
    totalRewards {
      itemId: 711121
      itemNum: 1
    }
    totalRewards {
      itemId: 711336
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 4
    }
    totalRewards {
      itemId: 270013
      itemNum: 1
    }
    totalRewards {
      itemId: 711343
      itemNum: 1
    }
    totalRewards {
      itemId: 301100
      itemNum: 1
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 320221
      itemNum: 1
    }
    totalRewards {
      itemId: 200008
      itemNum: 32
    }
    totalRewards {
      itemId: 3541
      itemNum: 2400
    }
    totalRewards {
      itemId: 260013
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 24
    }
    totalRewards {
      itemId: 200006
      itemNum: 32
    }
    totalRewards {
      itemId: 330101
      itemNum: 1
    }
    totalRewards {
      itemId: 320220
      itemNum: 1
    }
    totalRewards {
      itemId: 711346
      itemNum: 1
    }
    totalRewards {
      itemId: 300103
      itemNum: 4
    }
    totalRewards {
      itemId: 270014
      itemNum: 1
    }
    totalRewards {
      itemId: 320219
      itemNum: 1
    }
    totalRewards {
      itemId: 711359
      itemNum: 1
    }
    totalRewards {
      itemId: 320222
      itemNum: 1
    }
    highlightRewards {
      itemId: 320221
      itemNum: 1
    }
    highlightRewards {
      itemId: 260013
      itemNum: 1
    }
    highlightRewards {
      itemId: 330101
      itemNum: 1
    }
    highlightRewards {
      itemId: 320220
      itemNum: 1
    }
    highlightRewards {
      itemId: 711346
      itemNum: 1
    }
    highlightRewards {
      itemId: 270014
      itemNum: 1
    }
    highlightRewards {
      itemId: 320219
      itemNum: 1
    }
    highlightRewards {
      itemId: 711359
      itemNum: 1
    }
    highlightRewards {
      itemId: 320222
      itemNum: 1
    }
    price {
      coinType: 1
      price: 198
      midas: "18_3_1"
    }
    replacement {
      itemId: 6
      itemNum: 200
    }
  }
  payed {
    enable: true
    highlightRewards {
      itemId: 320221
      itemNum: 1
    }
    highlightRewards {
      itemId: 260013
      itemNum: 1
    }
    highlightRewards {
      itemId: 330101
      itemNum: 1
    }
    highlightRewards {
      itemId: 320220
      itemNum: 1
    }
    highlightRewards {
      itemId: 711346
      itemNum: 1
    }
    highlightRewards {
      itemId: 270014
      itemNum: 1
    }
    highlightRewards {
      itemId: 320219
      itemNum: 1
    }
    highlightRewards {
      itemId: 711359
      itemNum: 1
    }
    highlightRewards {
      itemId: 320222
      itemNum: 1
    }
    extraRewards {
      itemId: 711337
      itemNum: 1
    }
    extraRewards {
      itemId: 260014
      itemNum: 1
    }
    extraRewards {
      itemId: 1015
      itemNum: 1000
    }
    price {
      coinType: 1
      price: 596
      origin: 800
      midas: "18_3_2"
    }
    replacement {
      itemId: 6
      itemNum: 600
    }
  }
  upgrade {
    range {
      key: 1
      value: 2
    }
    price {
      coinType: 1
      price: 398
      midas: "18_3_3"
    }
    replacement {
      itemId: 6
      itemNum: 400
    }
  }
  level {
    coinType: 1
    price: 20
    midas: "18_3_4"
  }
  grandRewardId: 301100
  expiredMailId: 57
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Wolf_S1_Main"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_NR3E3_S1_Show_Moba"
  totalShowViewPath: "UI_NewBattlePass_Wolf_S1_TotalShow"
  mainViewCameraTag: "Camera_Arena_Decorate"
  totalShowViewCameraTag: "Camera_Arena_Decorate_Total"
  rewardViewPath: "UI_NewBattlePass_Wolf_S1_Reward"
  taskViewPath: "UI_NewBattlePass_Wolf_S1_TaskPage"
  helpId: 165
  midTitle1: "峡谷相逢"
  midTitle2: "第11赛季通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级"
  titleIcon: "T_BattlePass_Icon_PremiumPass"
  normalBattlePassId: 102034
  gloryBattlePassId: 102035
  isShow: true
  expWeekLimit: 700
}
rows {
  id: 3006
  type: BT_Moba
  expItemId: 1015
  title: "峡谷相逢通行证"
  time {
    beginTime {
      seconds: 1746115200
    }
    endTime {
      seconds: 1750953599
    }
  }
  lowestVersion: "1.3.88.1"
  task {
    dailyNum: 1
    weekGroupIds: 30052
    weekGroupIds: 30053
    weekGroupIds: 30054
    weekGroupIds: 30055
    weekGroupIds: 30056
    weekGroupIds: 30057
    weekGroupIds: 30058
    dailyFixGroupId: 30050
  }
  free {
    enable: true
    totalRewards {
      itemId: 6
      itemNum: 160
    }
    totalRewards {
      itemId: 200008
      itemNum: 8
    }
    totalRewards {
      itemId: 3541
      itemNum: 730
    }
    totalRewards {
      itemId: 300101
      itemNum: 2
    }
    totalRewards {
      itemId: 711419
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 4
    }
    totalRewards {
      itemId: 260021
      itemNum: 1
    }
    totalRewards {
      itemId: 711422
      itemNum: 1
    }
    totalRewards {
      itemId: 711423
      itemNum: 1
    }
    totalRewards {
      itemId: 270019
      itemNum: 1
    }
    totalRewards {
      itemId: 711426
      itemNum: 1
    }
    totalRewards {
      itemId: 301140
      itemNum: 1
    }
    totalRewards {
      itemId: 304001
      itemNum: 280
    }
  }
  payed {
    enable: true
    totalRewards {
      itemId: 320224
      itemNum: 1
    }
    totalRewards {
      itemId: 200008
      itemNum: 32
    }
    totalRewards {
      itemId: 3541
      itemNum: 2760
    }
    totalRewards {
      itemId: 260020
      itemNum: 1
    }
    totalRewards {
      itemId: 300102
      itemNum: 2
    }
    totalRewards {
      itemId: 200006
      itemNum: 32
    }
    totalRewards {
      itemId: 330102
      itemNum: 1
    }
    totalRewards {
      itemId: 320225
      itemNum: 1
    }
    totalRewards {
      itemId: 302005
      itemNum: 1
    }
    totalRewards {
      itemId: 300103
      itemNum: 4
    }
    totalRewards {
      itemId: 270018
      itemNum: 1
    }
    totalRewards {
      itemId: 320226
      itemNum: 1
    }
    totalRewards {
      itemId: 711454
      itemNum: 1
    }
    totalRewards {
      itemId: 320223
      itemNum: 1
    }
    totalRewards {
      itemId: 304001
      itemNum: 1120
    }
    highlightRewards {
      itemId: 320224
      itemNum: 1
    }
    highlightRewards {
      itemId: 260020
      itemNum: 1
    }
    highlightRewards {
      itemId: 320225
      itemNum: 1
    }
    highlightRewards {
      itemId: 302005
      itemNum: 1
    }
    highlightRewards {
      itemId: 300103
      itemNum: 4
    }
    highlightRewards {
      itemId: 270018
      itemNum: 1
    }
    highlightRewards {
      itemId: 320226
      itemNum: 1
    }
    highlightRewards {
      itemId: 711454
      itemNum: 1
    }
    highlightRewards {
      itemId: 320223
      itemNum: 1
    }
    price {
      coinType: 1
      price: 198
      midas: "18_3_1"
    }
    replacement {
      itemId: 6
      itemNum: 200
    }
  }
  payed {
    enable: true
    highlightRewards {
      itemId: 320224
      itemNum: 1
    }
    highlightRewards {
      itemId: 260020
      itemNum: 1
    }
    highlightRewards {
      itemId: 320225
      itemNum: 1
    }
    highlightRewards {
      itemId: 302005
      itemNum: 1
    }
    highlightRewards {
      itemId: 300103
      itemNum: 4
    }
    highlightRewards {
      itemId: 270018
      itemNum: 1
    }
    highlightRewards {
      itemId: 320226
      itemNum: 1
    }
    highlightRewards {
      itemId: 711454
      itemNum: 1
    }
    highlightRewards {
      itemId: 320223
      itemNum: 1
    }
    extraRewards {
      itemId: 711458
      itemNum: 1
    }
    extraRewards {
      itemId: 260019
      itemNum: 1
    }
    extraRewards {
      itemId: 1015
      itemNum: 1000
    }
    price {
      coinType: 1
      price: 596
      origin: 800
      midas: "18_3_2"
    }
    replacement {
      itemId: 6
      itemNum: 600
    }
  }
  upgrade {
    range {
      key: 1
      value: 2
    }
    price {
      coinType: 1
      price: 398
      midas: "18_3_3"
    }
    replacement {
      itemId: 6
      itemNum: 400
    }
  }
  level {
    coinType: 1
    price: 20
    midas: "18_3_4"
  }
  grandRewardId: 301140
  expiredMailId: 57
  unlockViewPath: "UI_NewBattlePass_Wolf_S1_Unlock"
  unlockBtnShow: "1,300"
  mainViewPath: "UI_NewBattlePass_Wolf_S1_Main"
  discountBottomDes: "45,300,18"
  showViewPaths: "UI_NewBattlePass_NR3E3_S1_Show_Moba02"
  totalShowViewPath: "UI_NewBattlePass_Wolf_S1_TotalShow"
  mainViewCameraTag: "Camera_Arena_Decorate"
  totalShowViewCameraTag: "Camera_Arena_Decorate_Total"
  rewardViewPath: "UI_NewBattlePass_Wolf_S1_Reward"
  taskViewPath: "UI_NewBattlePass_Wolf_S1_TaskPage"
  helpId: 165
  midTitle1: "峡谷相逢"
  midTitle2: "第12赛季通行证"
  unlockNormalTitle: "高级通行证"
  unlockNormalDes: "达到指定等级可领取"
  unlockSuperTitle: "豪华通行证"
  unlockSuperDes: "包含所有高级奖励+立升10级"
  titleIcon: "T_BattlePass_Icon_PremiumPass"
  normalBattlePassId: 102036
  gloryBattlePassId: 102037
  isShow: true
  expWeekLimit: 700
  normalToGloryBattlePassId: 102038
}
