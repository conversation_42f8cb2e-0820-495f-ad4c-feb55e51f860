com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_主表.xlsx sheet:奖池-至臻卡池
rows {
  poolId: 5301
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 240
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 120
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 203
}
rows {
  poolId: 5302
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 240
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 120
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 203
}
rows {
  poolId: 5303
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      first {
        text: "免费"
        ratio: 0.0
      }
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 280
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 150
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 211
}
rows {
  poolId: 5304
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 35
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
}
rows {
  poolId: 5305
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 3
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 15
      first {
        text: "6折"
        ratio: 0.6
      }
    }
    draw: 5
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 300
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardGroup: 3
    tenthGRewardWeight: 1
    tenthGRewardWeight: 20000
    tenthGRewardWeight: 80000
  }
  maxLimit: 999999
  dispersion {
    rewardRefreshPeriod: 60
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 216
}
rows {
  poolId: 5306
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      first {
        text: "免费"
        ratio: 0.0
      }
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 280
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 150
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 211
}
rows {
  poolId: 5307
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 240
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 120
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 203
}
rows {
  poolId: 5308
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 35
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  hasLuckyRule: true
}
rows {
  poolId: 5309
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      first {
        text: "免费"
        ratio: 0.0
      }
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 280
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardWeight: 1
    tenthGRewardWeight: 99999
  }
  dispersion {
    rewardRefreshPeriod: 150
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 1000
  }
  mallVoucherId: 211
}
