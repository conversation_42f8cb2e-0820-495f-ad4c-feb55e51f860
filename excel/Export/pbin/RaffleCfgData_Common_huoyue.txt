com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_活跃.xlsx sheet:奖池-赛季金币活跃卡池
rows {
  poolId: 10006
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 20000001
  refresh: RRT_Permanent
  coinType: 2071
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1500
    minorGRewardGroup: 10
    minorGRewardWeight: 1
    minorGRewardPeriod: 30
  }
  reason: 363
  creditItemId: 3407
  hasLuckyRule: false
}
rows {
  poolId: 20000002
  refresh: RRT_Permanent
  coinType: 3458
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 7
    minorGRewardWeight: 1
    minorGRewardPeriod: 5
  }
  reason: 363
  creditItemId: 3457
  hasLuckyRule: false
}
rows {
  poolId: 10007
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 10008
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 10009
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 10010
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 20000003
  refresh: RRT_Permanent
  coinType: 3700
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  reason: 363
  creditItemId: 3710
  hasLuckyRule: false
}
rows {
  poolId: 20000004
  refresh: RRT_Permanent
  coinType: 3700
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 15
  }
  reason: 363
  creditItemId: 3710
  hasLuckyRule: false
}
rows {
  poolId: 20000005
  refresh: RRT_Permanent
  coinType: 3700
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 15
  }
  reason: 363
  creditItemId: 3710
  hasLuckyRule: false
}
rows {
  poolId: 20000006
  refresh: RRT_Permanent
  coinType: 3700
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 15
  }
  reason: 363
  creditItemId: 3710
  hasLuckyRule: false
}
rows {
  poolId: 20000007
  refresh: RRT_Permanent
  coinType: 3700
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 30
  }
  reason: 363
  creditItemId: 3710
  hasLuckyRule: false
}
rows {
  poolId: 20000008
  refresh: RRT_Permanent
  coinType: 3463
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  reason: 363
  creditItemId: 3159
  hasLuckyRule: false
}
rows {
  poolId: 10011
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 20000009
  refresh: RRT_Permanent
  coinType: 3463
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  reason: 363
  creditItemId: 3159
  hasLuckyRule: false
}
rows {
  poolId: 10012
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
