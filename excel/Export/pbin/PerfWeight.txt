com.tencent.wea.xlsRes.table_PerfWeightData
excel/xls/X_性能开销权重表.xlsx sheet:性能开销权重
rows {
  Id: 640021
  PerfWeight: 49
  PerfGroupID: 1
  ReachPerfLimitTips: "当前场景的手持物额外效果已达到最大数量限制，稍后再尝试"
}
rows {
  Id: 640031
  PerfWeight: 34
  PerfGroupID: 1
  ReachPerfLimitTips: "当前场景的手持物额外效果已达到最大数量限制，稍后再尝试"
}
rows {
  Id: 640075
  PerfWeight: 34
  PerfGroupID: 1
  ReachPerfLimitTips: "当前场景的手持物额外效果已达到最大数量限制，稍后再尝试"
}
rows {
  Id: 640123
  PerfWeight: 34
  PerfGroupID: 1
  ReachPerfLimitTips: "当前场景的手持物额外效果已达到最大数量限制，稍后再尝试"
}
rows {
  Id: 640158
  PerfWeight: 34
  PerfGroupID: 1
  ReachPerfLimitTips: "当前场景的手持物额外效果已达到最大数量限制，稍后再尝试"
}
rows {
  Id: 750001
  PerfWeight: 16
  PerfGroupID: 2
  ReachPerfLimitTips: "当前场景的环绕物效果已达到最大数量限制，稍后再尝试"
}
rows {
  Id: 750002
  PerfWeight: 34
  PerfGroupID: 2
  ReachPerfLimitTips: "当前场景的环绕物效果已达到最大数量限制，稍后再尝试"
}
