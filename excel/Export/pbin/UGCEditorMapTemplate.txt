com.tencent.wea.xlsRes.table_UGCEditorMapTemplate
excel/xls/U_UGC编辑器.xlsx sheet:UgcMapTemplate
rows {
  TemplateId: 21
  Name: "跑酷模板"
  Type: Topspeed
  Cover: "CDN:21"
  Test: false
  GameCamParam: "0,0,300,-30,180,0"
  GodCamParam: "0,0,300,-45,180,0"
  Description: "发挥创造力，尝试做出自己的跑酷！"
  MapTag: 1
  TemplateSortIndex: 1
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 8
  Name: "空白模板"
  Type: Topspeed
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 1
  TemplateSortIndex: 2
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 1
  Name: "基础模板"
  Type: Topspeed
  Cover: "CDN:1"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在现有基础上尽情创作吧！"
  MapTag: 1
  TemplateSortIndex: 3
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 66
  Name: "海滨派对"
  Type: Topspeed
  Cover: "CDN:66"
  Test: false
  GameCamParam: "15435,4577,9237,0,10,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "尽情享受海滩派对的乐趣。【初级模板】包含海岛、海底两个场景的景观"
  MapTag: 2
  TemplateSortIndex: 4
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 65
  Name: "夏日田野"
  Type: Topspeed
  Cover: "CDN:65"
  Test: false
  GameCamParam: "-3,-387,743,5,163,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "清新的空气中弥漫着田野的芬芳。【初级模板】包含小清新风格的景观"
  MapTag: 2
  TemplateSortIndex: 5
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 62
  Name: "浮岚暖翠"
  Type: Topspeed
  Cover: "CDN:62"
  Test: false
  GameCamParam: "5700,6200,1050,0,200,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "青绿丛中绽芳华。【初级模板】包含国风场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 6
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 63
  Name: "丛林秘境"
  Type: Topspeed
  Cover: "CDN:63"
  Test: false
  GameCamParam: "100,5250,1250,0,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "深绿之境，自然缱绻。【初级模板】包含森林场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 7
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 60
  Name: "寒山飞雪"
  Type: Topspeed
  Cover: "CDN:60"
  Test: false
  GameCamParam: "-8000,-4100,1900,0,200,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "银装素裹，天地飞花。【初级模板】包含冬季场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 8
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 61
  Name: "荒漠峡谷"
  Type: Topspeed
  Cover: "CDN:61"
  Test: false
  GameCamParam: "4350,-3450,1400,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "沙丘连绵，黄沙漫天。【初级模板】包含沙漠峡谷场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 9
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 22
  Name: "人生之路"
  Type: Topspeed
  Cover: "CDN:22"
  Test: false
  GameCamParam: "-15450,450,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "一路生长，一路历练。【高级模板】包含文本叙事、场景转换、怪物追逐等子案例"
  MapTag: 8
  TemplateSortIndex: 10
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 23
  Name: "星宝空间站"
  Type: Topspeed
  Cover: "CDN:23"
  Test: false
  GameCamParam: "-4000,3000,2300,-15,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "星宝空间站等你来解密。【高级模板】包含任务指引、密码门、推箱等子案例"
  MapTag: 7
  TemplateSortIndex: 11
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 2
  Name: "梦幻城堡"
  Type: Topspeed
  Cover: "CDN:2"
  Test: false
  GameCamParam: "2415,23414,600,0,-90,0"
  GodCamParam: "2415,23414,600,0,-90,0"
  Description: "少女心爆棚啦！【初级模板】介绍观赏图的基础技巧"
  MapTag: 2
  TemplateSortIndex: 12
  OpenTag: 1
  Remarks: "竞速_官方"
  TextReview: 3
}
rows {
  TemplateId: 15
  Name: "火之意志"
  Type: Topspeed
  Cover: "CDN:15"
  Test: false
  GameCamParam: "2800,200,5250,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "喵呜酱"
  Description: "完成专属任务吧，燃烧，火之意志！"
  MapTag: 1
  TemplateSortIndex: 1
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 16
  Name: "地铁跑酷"
  Type: Topspeed
  Cover: "CDN:16"
  Test: false
  GameCamParam: "2200,-8000,3000,-30,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "鳕鱼帕尼尼"
  Description: "在元梦里也要全力奔跑，小心被抓到哦~"
  MapTag: 1
  TemplateSortIndex: 2
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 17
  Name: "危机降临"
  Type: Topspeed
  Cover: "CDN:17"
  Test: false
  GameCamParam: "-2500,3200,2200,-30,-45,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "SiriusLord"
  Description: "危机降临，躲避舔食者和追逐者的围追堵截，逃出警察局吧。"
  MapTag: 7
  TemplateSortIndex: 3
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 18
  Name: "召唤师名场面"
  Type: Topspeed
  Cover: "CDN:18"
  Test: false
  GameCamParam: "5300,3600,5400,-30,225,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "一拳大师"
  Description: "在召唤师峡谷内见证一个又一个名场面吧，收集英雄信物，找回最初的感动。"
  MapTag: 2
  TemplateSortIndex: 4
  OpenTag: 1
  Remarks: "竞速_社区(挂起)"
  TextReview: 3
}
rows {
  TemplateId: 19
  Name: "浪漫之旅"
  Type: Topspeed
  Cover: "CDN:19"
  Test: false
  GameCamParam: "7250,-50,650,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "叮叮当当当"
  Description: "和许墨一起享受浪漫的时光。"
  MapTag: 2
  TemplateSortIndex: 5
  OpenTag: 1
  Remarks: "竞速_社区(挂起)"
  TextReview: 3
}
rows {
  TemplateId: 20
  Name: "QQ吃豆大战"
  Type: Topspeed
  Cover: "CDN:20"
  Test: false
  GameCamParam: "-2500,-200,10000,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "焦糖绵绵冰"
  Description: "在QQ迷宫里穿梭，获取表情包和小豆子，赢得胜利吧！"
  MapTag: 10
  TemplateSortIndex: 6
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 30
  Name: "星宝大亨"
  Type: Topspeed
  Cover: "CDN:30"
  Test: false
  GameCamParam: "0,0,0,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "德玛西亚之星"
  Description: "来星世界建造属于自己城市吧~"
  MapTag: 10
  TemplateSortIndex: 7
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 91
  Name: "大三巴牌坊"
  Type: Topspeed
  Cover: "CDN:91"
  Test: false
  GameCamParam: "12830,4078,1730,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "kinghuaaa"
  Description: "大三巴牌坊是澳门的标志性建筑物之一，同时也为“澳门八景”之一。"
  MapTag: 2
  TemplateSortIndex: 8
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 92
  Name: "维多利亚港"
  Type: Topspeed
  Cover: "CDN:92"
  Test: false
  GameCamParam: "27750,23800,3000,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "kinghuaaa"
  Description: "维多利亚港一直主导香港经济和旅游业发展，是香港成为国际大城市的关键之一！"
  MapTag: 2
  TemplateSortIndex: 9
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 93
  Name: "越秀公园"
  Type: Topspeed
  Cover: "CDN:93"
  Test: false
  GameCamParam: "35350,-32600,1200,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "一朵宝"
  Description: "越秀公园的主体越秀山是一座有深厚文化底蕴的名山，早在秦汉时期，就是广州的风景名胜地。"
  MapTag: 2
  TemplateSortIndex: 10
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 28
  Name: "百年紫禁城-午门"
  Type: Topspeed
  Cover: "CDN:28"
  Test: false
  GameCamParam: "54904,5548,-67700,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "京城探子&高启明"
  Description: "穿越六百年时空，探秘华夏盛世的宫廷传奇！"
  MapTag: 2
  TemplateSortIndex: 11
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 29
  Name: "百年紫禁城-太和门广场"
  Type: Topspeed
  Cover: "CDN:29"
  Test: false
  GameCamParam: "-5655,6131,-67858,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "京城探子&高启明"
  Description: "穿越六百年时空，探秘华夏盛世的宫廷传奇！"
  MapTag: 2
  TemplateSortIndex: 12
  OpenTag: 1
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 64
  Name: "星莱坞摄影棚"
  Type: Topspeed
  Cover: "CDN:64"
  Test: false
  GameCamParam: "-13850,-3550,2500,0,270,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "各种风格的拍摄场景等你来体验，快来设计和拍摄美美的外观和照片吧~"
  TemplateSortIndex: 1
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 9
  Name: "创意玩法/物理方块"
  Type: Topspeed
  Cover: "CDN:9"
  Test: false
  GameCamParam: "-35350,-650,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "欢乐电玩城等你来探索~使用物理方块实现的小游戏玩法。"
  TemplateSortIndex: 2
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 3
  Name: "模拟载具"
  Type: Topspeed
  Cover: "CDN:3"
  Test: false
  GameCamParam: "-21130,11800,10051,0,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "各式各样的载具等你来挑~"
  TemplateSortIndex: 3
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 27
  Name: "创意玩法/音乐触发盒"
  Type: Topspeed
  Cover: "CDN:27"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "来听音乐吧！音乐触发盒使用介绍。"
  TemplateSortIndex: 4
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 46
  Name: "粒子特效"
  Type: Topspeed
  Cover: "CDN:46"
  Test: false
  GameCamParam: "1100,0,4100,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "粒子发射器功能与效果展示。"
  TemplateSortIndex: 5
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 50
  Name: "特效库"
  Type: Topspeed
  Cover: "CDN:50"
  Test: false
  GameCamParam: "41400,-500,0,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "预制特效和添加运动展示。"
  TemplateSortIndex: 6
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 6
  Name: "海洋主题景观"
  Type: Topspeed
  Cover: "CDN:6"
  Test: false
  GameCamParam: "4672,0,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "主题场景。谁住在可爱的小屋中呢？"
  TemplateSortIndex: 7
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 7
  Name: "甜品主题景观"
  Type: Topspeed
  Cover: "CDN:7"
  Test: false
  GameCamParam: "4622,-120,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "主题场景。甜蜜的故事正等待续写。"
  TemplateSortIndex: 8
  OpenTag: 1
  Remarks: "竞速_演示"
  TextReview: 3
}
rows {
  TemplateId: 51
  Name: "空白模板"
  Type: Survival
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 203
  TemplateSortIndex: 1
  OpenTag: 2
  Remarks: "生存_官方"
  TextReview: 3
}
rows {
  TemplateId: 11
  Name: "悬空危境"
  Type: Survival
  Cover: "CDN:11"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "地图悬浮于高空，需小心行走，巧妙避开危险，一步失误即掉入无底深渊。"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 2
  Remarks: "生存_官方"
  TextReview: 3
}
rows {
  TemplateId: 12
  Name: "瀑中飞板"
  Type: Survival
  Cover: "CDN:12"
  Test: false
  GameCamParam: "-250,0,850,-20,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "巨大瀑布间错落飞板，需灵活跳跃躲避危险，探索隐藏在水幕背后的奥秘。"
  MapTag: 203
  TemplateSortIndex: 3
  OpenTag: 2
  Remarks: "生存_官方"
  TextReview: 3
}
rows {
  TemplateId: 13
  Name: "惊险铁串"
  Type: Survival
  Cover: "CDN:52"
  Test: false
  GameCamParam: "6400,955,5872,0,30,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "巨大瀑布间错落飞板，需灵活跳跃躲避危险，探索隐藏在水幕背后的奥秘。"
  MapTag: 203
  OpenTag: 2
  Remarks: "生存_官方"
  TextReview: 3
}
rows {
  TemplateId: 14
  Name: "山水劫境"
  Type: Survival
  Cover: "CDN:14"
  Test: false
  GameCamParam: "400,-530,490,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在转瞬即逝的山水画卷中，体验高处不胜寒的惊险刺激。"
  MapTag: 203
  TemplateSortIndex: 4
  OpenTag: 2
  Remarks: "生存_官方"
  TextReview: 3
}
rows {
  TemplateId: 56
  Name: "决战莲台"
  Type: Survival
  Cover: "CDN:56"
  Test: false
  GameCamParam: "1000,-3250,1950,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "荷叶会消失，努力成为最后站在荷叶上的人吧！"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 2
  Remarks: "生存_官方"
  TextReview: 3
}
rows {
  TemplateId: 52
  Name: "惊险铁串"
  Type: Survival
  Cover: "CDN:52"
  Test: false
  GameCamParam: "500,1150,850,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "四面困厄，巨大铁柱突袭，需灵敏应对不断涌现的致命威胁。"
  MapTag: 203
  TemplateSortIndex: 1
  OpenTag: 2
  Remarks: "生存_社区"
  TextReview: 3
}
rows {
  TemplateId: 55
  Name: "谷底对决"
  Type: Survival
  Cover: "CDN:55"
  Test: false
  GameCamParam: "500,-650,3400,-15,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "在九宫格之间闪转腾挪，避免踏空落入谷底。"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 2
  Remarks: "生存_社区(挂起)"
  TextReview: 3
}
rows {
  TemplateId: 45
  Name: "空白团竞模板"
  Type: GunGameBPMGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 302
  TemplateSortIndex: 1
  OpenTag: 32
  Remarks: "射击_官方_团队"
  TextReview: 3
}
rows {
  TemplateId: 44
  Name: "基础团竞模板"
  Type: GunGameBPMGame
  Cover: "CDN:44"
  Test: false
  GameCamParam: "-2500,-950,900,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含枪械生成器和掩体的模板进行创作，可以用来创作经典的冲锋竞技、一命对决、推车等玩法"
  MapTag: 302
  TemplateSortIndex: 2
  OpenTag: 32
  Remarks: "射击_官方_团队"
  TextReview: 3
}
rows {
  TemplateId: 40
  Name: "派对邮轮"
  Type: GunGameBPMGame
  Cover: "CDN:40"
  Test: false
  GameCamParam: "400,4550,2500,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "豪华游艇盛宴，在欢庆氛围中展开激战，隐藏在歌舞间的精彩对决。"
  MapTag: 302
  TemplateSortIndex: 5
  OpenTag: 32
  Remarks: "射击_官方_团队"
  TextReview: 3
}
rows {
  TemplateId: 53
  Name: "空白混战模板"
  Type: GunGameBPMGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 32
  Remarks: "射击_官方_混战"
  TextReview: 3
}
rows {
  TemplateId: 42
  Name: "冲锋竞技-边陲小镇"
  Type: GunGameBPMGame
  Cover: "CDN:42"
  Test: false
  GameCamParam: "2850,-200,1200,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "在边陲小镇进行角逐，巧妙利用地形，制定战术。"
  MapTag: 302
  TemplateSortIndex: 1
  OpenTag: 32
  Remarks: "射击_社区_团队"
  TextReview: 3
}
rows {
  TemplateId: 43
  Name: "冲锋竞技-开星广场"
  Type: GunGameBPMGame
  Cover: "CDN:43"
  Test: false
  GameCamParam: "4800,1600,1100,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "充满节日氛围的小镇，既有欢乐又有惊喜。"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 32
  Remarks: "射击_社区_混战"
  TextReview: 3
}
rows {
  TemplateId: 47
  Name: "基础爆破模版"
  Type: GunGameBPMGame
  Cover: "CDN:47"
  Test: false
  GameCamParam: "2400,-400,1050,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用爆破基础模板，来制作爆破玩法地图吧。"
  MapTag: 302
  TemplateSortIndex: 3
  OpenTag: 32
  Remarks: "射击_官方_团队"
  TextReview: 3
}
rows {
  TemplateId: 48
  Name: "沙城争锋"
  Type: GunGameBPMGame
  Cover: "CDN:48"
  Test: false
  GameCamParam: "1750,-2800,900,-15,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在被沙尘掩盖的古城中展开激烈的争夺，成为沙城的主宰。"
  MapTag: 302
  TemplateSortIndex: 6
  OpenTag: 32
  Remarks: "射击_官方_团队"
  TextReview: 3
}
rows {
  TemplateId: 49
  Name: "空白冒险模板"
  Type: GunGameBPMGame
  Cover: "CDN:49"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含怪物示例的模板进行创作，可以用来创作打怪、塔防、合作冒险等玩法"
  MapTag: 306
  TemplateSortIndex: 4
  OpenTag: 32
  Remarks: "射击_官方_团队"
  TextReview: 3
}
rows {
  TemplateId: 57
  Name: "射击玩法-玩家能力演示"
  Type: GunGameBPMGame
  Cover: "CDN:57"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "体验射击地图中，玩家移动、跳跃以及射击等各项能力"
  TemplateSortIndex: 1
  OpenTag: 32
  Remarks: "射击_演示"
  TextReview: 3
}
rows {
  TemplateId: 58
  Name: "基础占点模版"
  Type: GunGameBPMGame
  Cover: "CDN:58"
  Test: false
  GameCamParam: "2150,0,1200,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含据点元件的占点基础模板，来制作占点玩法地图吧。"
  MapTag: 302
  TemplateSortIndex: 7
  OpenTag: 32
  Remarks: "射击_官方_团队"
  TextReview: 3
}
rows {
  TemplateId: 59
  Name: "火车角斗场"
  Type: GunGameBPMGame
  Cover: "CDN:59"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "在火车站收集物资，爬上车厢，和其他星宝一决胜负吧！"
  MapTag: 307
  TemplateSortIndex: 1
  OpenTag: 32
  Remarks: "射击_官方_混战"
  TextReview: 3
}
rows {
  TemplateId: 34
  Name: "爆破个人"
  Type: GunGameBPMGame
  Cover: "CDN:40"
  Test: false
  GameCamParam: "400,4550,2500,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  OpenTag: 32
  Remarks: "暂未匹配(挂起)"
  TextReview: 3
}
rows {
  TemplateId: 5
  Name: "机关GYM"
  Type: Topspeed
  Cover: "CDN:1"
  Test: true
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  OpenTag: 1
  Remarks: "健身房测试关"
  TextReview: 3
}
rows {
  TemplateId: 24
  Name: "道具GYM"
  Type: Topspeed
  Cover: "CDN:1"
  Test: true
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  OpenTag: 1
  Remarks: "健身房测试关"
  TextReview: 3
}
rows {
  TemplateId: 25
  Name: "运动单元GYM"
  Type: Topspeed
  Cover: "CDN:1"
  Test: true
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  OpenTag: 1
  Remarks: "健身房测试关"
  TextReview: 3
}
rows {
  TemplateId: 26
  Name: "逻辑元件GYM"
  Type: Topspeed
  Cover: "CDN:1"
  Test: true
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  OpenTag: 1
  Remarks: "健身房测试关"
  TextReview: 3
}
rows {
  TemplateId: 10
  Name: "逻辑元件GYM-2"
  Type: Topspeed
  Cover: "CDN:1"
  Test: true
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  OpenTag: 1
  Remarks: "健身房测试关"
  TextReview: 3
}
rows {
  TemplateId: 82
  Name: "塔防大亨"
  Type: TYCGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 401
  TemplateSortIndex: 1
  OpenTag: 30
  Remarks: "大亨_官方"
  TextReview: 3
}
rows {
  TemplateId: 83
  Name: "塔防大亨"
  Type: TYCGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 401
  TemplateSortIndex: 1
  OpenTag: 30
  Remarks: "大亨_社区"
  TextReview: 3
}
rows {
  TemplateId: 101
  Name: "空白乐园模板"
  Type: UGCLobby
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 12
  TemplateSortIndex: 1
  OpenTag: 500
  Remarks: "乐园_官方"
  TextReview: 3
}
rows {
  TemplateId: 102
  Name: "乐园"
  Type: UGCLobby
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 12
  TemplateSortIndex: 1
  OpenTag: 500
  Remarks: "乐园_社区"
  TextReview: 3
}
rows {
  TemplateId: 4101
  Name: "综合"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 1
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "综合_官方"
  TextReview: 3
}
rows {
  TemplateId: 41021
  Name: "跑酷模板"
  Type: UGCRoundGame
  Cover: "CDN:21"
  Test: false
  GameCamParam: "0,0,300,-30,180,0"
  GodCamParam: "0,0,300,-45,180,0"
  Description: "发挥创造力，尝试做出自己的跑酷！"
  MapTag: 1
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42021
}
rows {
  TemplateId: 41008
  Name: "空白模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 1
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42008
}
rows {
  TemplateId: 41001
  Name: "基础模板"
  Type: UGCRoundGame
  Cover: "CDN:1"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在现有基础上尽情创作吧！"
  MapTag: 1
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42001
}
rows {
  TemplateId: 41066
  Name: "海滨派对"
  Type: UGCRoundGame
  Cover: "CDN:66"
  Test: false
  GameCamParam: "15435,4577,9237,0,10,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "尽情享受海滩派对的乐趣。【初级模板】包含海岛、海底两个场景的景观"
  MapTag: 2
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42066
}
rows {
  TemplateId: 41065
  Name: "夏日田野"
  Type: UGCRoundGame
  Cover: "CDN:65"
  Test: false
  GameCamParam: "-3,-387,743,5,163,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "清新的空气中弥漫着田野的芬芳。【初级模板】包含小清新风格的景观"
  MapTag: 2
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42065
}
rows {
  TemplateId: 41062
  Name: "浮岚暖翠"
  Type: UGCRoundGame
  Cover: "CDN:62"
  Test: false
  GameCamParam: "5700,6200,1050,0,200,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "青绿丛中绽芳华。【初级模板】包含国风场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42062
}
rows {
  TemplateId: 41063
  Name: "丛林秘境"
  Type: UGCRoundGame
  Cover: "CDN:63"
  Test: false
  GameCamParam: "100,5250,1250,0,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "深绿之境，自然缱绻。【初级模板】包含森林场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42063
}
rows {
  TemplateId: 41060
  Name: "寒山飞雪"
  Type: UGCRoundGame
  Cover: "CDN:60"
  Test: false
  GameCamParam: "-8000,-4100,1900,0,200,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "银装素裹，天地飞花。【初级模板】包含冬季场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 8
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42060
}
rows {
  TemplateId: 41061
  Name: "荒漠峡谷"
  Type: UGCRoundGame
  Cover: "CDN:61"
  Test: false
  GameCamParam: "4350,-3450,1400,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "沙丘连绵，黄沙漫天。【初级模板】包含沙漠峡谷场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 9
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42061
}
rows {
  TemplateId: 41022
  Name: "人生之路"
  Type: UGCRoundGame
  Cover: "CDN:22"
  Test: false
  GameCamParam: "-15450,450,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "一路生长，一路历练。【高级模板】包含文本叙事、场景转换、怪物追逐等子案例"
  MapTag: 8
  TemplateSortIndex: 10
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42022
}
rows {
  TemplateId: 41023
  Name: "星宝空间站"
  Type: UGCRoundGame
  Cover: "CDN:23"
  Test: false
  GameCamParam: "-4000,3000,2300,-15,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "星宝空间站等你来解密。【高级模板】包含任务指引、密码门、推箱等子案例"
  MapTag: 7
  TemplateSortIndex: 11
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42023
}
rows {
  TemplateId: 41002
  Name: "梦幻城堡"
  Type: UGCRoundGame
  Cover: "CDN:2"
  Test: false
  GameCamParam: "2415,23414,600,0,-90,0"
  GodCamParam: "2415,23414,600,0,-90,0"
  Description: "少女心爆棚啦！【初级模板】介绍观赏图的基础技巧"
  MapTag: 2
  TemplateSortIndex: 12
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42002
}
rows {
  TemplateId: 41015
  Name: "火之意志"
  Type: UGCRoundGame
  Cover: "CDN:15"
  Test: false
  GameCamParam: "2800,200,5250,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "喵呜酱"
  Description: "完成专属任务吧，燃烧，火之意志！"
  MapTag: 1
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42015
}
rows {
  TemplateId: 41016
  Name: "地铁跑酷"
  Type: UGCRoundGame
  Cover: "CDN:16"
  Test: false
  GameCamParam: "2200,-8000,3000,-30,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "鳕鱼帕尼尼"
  Description: "在元梦里也要全力奔跑，小心被抓到哦~"
  MapTag: 1
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42016
}
rows {
  TemplateId: 41017
  Name: "危机降临"
  Type: UGCRoundGame
  Cover: "CDN:17"
  Test: false
  GameCamParam: "-2500,3200,2200,-30,-45,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "SiriusLord"
  Description: "危机降临，躲避舔食者和追逐者的围追堵截，逃出警察局吧。"
  MapTag: 7
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42017
}
rows {
  TemplateId: 41018
  Name: "召唤师名场面"
  Type: UGCRoundGame
  Cover: "CDN:18"
  Test: false
  GameCamParam: "5300,3600,5400,-30,225,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "一拳大师"
  Description: "在召唤师峡谷内见证一个又一个名场面吧，收集英雄信物，找回最初的感动。"
  MapTag: 2
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "竞速_社区(挂起)"
  TextReview: 3
}
rows {
  TemplateId: 41019
  Name: "浪漫之旅"
  Type: UGCRoundGame
  Cover: "CDN:19"
  Test: false
  GameCamParam: "7250,-50,650,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "叮叮当当当"
  Description: "和许墨一起享受浪漫的时光。"
  MapTag: 2
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42019
}
rows {
  TemplateId: 41020
  Name: "QQ吃豆大战"
  Type: UGCRoundGame
  Cover: "CDN:20"
  Test: false
  GameCamParam: "-2500,-200,10000,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "焦糖绵绵冰"
  Description: "在QQ迷宫里穿梭，获取表情包和小豆子，赢得胜利吧！"
  MapTag: 10
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42020
}
rows {
  TemplateId: 41032
  Name: "机关牢狱大逃脱"
  Type: UGCRoundGame
  Cover: "CDN:41032"
  Test: false
  GameCamParam: "-18557,645,467,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "yyee"
  Description: "来证明你是最强的逃脱大师，书写属于你的传奇故事！"
  MapTag: 306
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 41024
  Name: "长跑酷玩法模板"
  Type: UGCRoundGame
  Cover: "CDN:41024"
  Test: false
  GameCamParam: "8320,1640,-5252,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "每一步都在改写生存法则——你的奔跑，就是新纪元的进化史诗！"
  MapTag: 1
  TemplateSortIndex: 8
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42024
}
rows {
  TemplateId: 41025
  Name: "唯途向穹1"
  Type: UGCRoundGame
  Cover: "CDN:41025"
  Test: false
  GameCamParam: "7954,-1576,548,0,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "仰首见星，垂首无路。【初级模板】包含垂直跑酷设计技巧，关卡主体结构为上下等大"
  MapTag: 1
  TemplateSortIndex: 9
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 41026
  Name: "唯途向穹2"
  Type: UGCRoundGame
  Cover: "CDN:41026"
  Test: false
  GameCamParam: "3910,-782,327,0,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "仰首见星，垂首无路。【初级模板】包含垂直跑酷设计技巧，关卡主体结构为上大下小"
  MapTag: 1
  TemplateSortIndex: 10
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
}
rows {
  TemplateId: 41031
  Name: "每秒+1模板"
  Type: UGCRoundGame
  Cover: "CDN:41031"
  Test: false
  GameCamParam: "-18395,-15526,212,0,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "点击屏幕，释放你的力量！面对更强怪物，成为元梦拳王!"
  MapTag: 14
  TemplateSortIndex: 11
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42031
}
rows {
  TemplateId: 41010
  Name: "我要盖豪宅"
  Type: UGCRoundGame
  Cover: "CDN:41010"
  Test: false
  GameCamParam: "-24797,-59033,-13327,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "来清爽的海岛亲手盖个度假豪宅吧。在这个愉快的时光，我要我的大HOUSE！"
  MapTag: 10
  TemplateSortIndex: 12
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42010
}
rows {
  TemplateId: 41096
  Name: "棋牌类视角模板"
  Type: UGCRoundGame
  Cover: "CDN:41096"
  Test: false
  GameCamParam: "2000,-3500,250,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "智谋对决，乐趣无穷，棋牌游戏带你体验策略与运气的完美融合！"
  MapTag: 12
  TemplateSortIndex: 13
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42096
}
rows {
  TemplateId: 41030
  Name: "星宝大亨"
  Type: UGCRoundGame
  Cover: "CDN:30"
  Test: false
  GameCamParam: "0,0,0,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "德玛西亚之星"
  Description: "来星世界建造属于自己城市吧~"
  MapTag: 10
  TemplateSortIndex: 14
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42030
}
rows {
  TemplateId: 41091
  Name: "大三巴牌坊"
  Type: UGCRoundGame
  Cover: "CDN:91"
  Test: false
  GameCamParam: "12830,4078,1730,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "kinghuaaa"
  Description: "大三巴牌坊是澳门的标志性建筑物之一，同时也为“澳门八景”之一。"
  MapTag: 2
  TemplateSortIndex: 15
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42091
}
rows {
  TemplateId: 41092
  Name: "维多利亚港"
  Type: UGCRoundGame
  Cover: "CDN:92"
  Test: false
  GameCamParam: "27750,23800,3000,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "kinghuaaa"
  Description: "维多利亚港一直主导香港经济和旅游业发展，是香港成为国际大城市的关键之一！"
  MapTag: 2
  TemplateSortIndex: 16
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42092
}
rows {
  TemplateId: 41093
  Name: "越秀公园"
  Type: UGCRoundGame
  Cover: "CDN:93"
  Test: false
  GameCamParam: "35350,-32600,1200,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "一朵宝"
  Description: "越秀公园的主体越秀山是一座有深厚文化底蕴的名山，早在秦汉时期，就是广州的风景名胜地。"
  MapTag: 2
  TemplateSortIndex: 17
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42093
}
rows {
  TemplateId: 41028
  Name: "百年紫禁城-午门"
  Type: UGCRoundGame
  Cover: "CDN:28"
  Test: false
  GameCamParam: "54904,5548,-67700,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "京城探子&高启明"
  Description: "穿越六百年时空，探秘华夏盛世的宫廷传奇！"
  MapTag: 2
  TemplateSortIndex: 18
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42028
}
rows {
  TemplateId: 41029
  Name: "百年紫禁城-太和门广场"
  Type: UGCRoundGame
  Cover: "CDN:29"
  Test: false
  GameCamParam: "-5655,6131,-67858,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "京城探子&高启明"
  Description: "穿越六百年时空，探秘华夏盛世的宫廷传奇！"
  MapTag: 2
  TemplateSortIndex: 19
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42029
}
rows {
  TemplateId: 41068
  Name: "花花的手账"
  Type: UGCRoundGame
  Cover: "CDN:41068"
  Test: false
  GameCamParam: "24538,-2053,-7158,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "兔兔大魔王"
  Description: "使用花花的手账同款模版，设计故事分支，演绎属于你自己的故事吧！"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42068
}
rows {
  TemplateId: 41069
  Name: "心理健康表"
  Type: UGCRoundGame
  Cover: "CDN:41069"
  Test: false
  GameCamParam: "-55289,4695,-30014,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "小吆西"
  Description: "这是一张可附带问卷的地图模板，你可以自定义你的关卡，引导玩家完成问卷。"
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42069
}
rows {
  TemplateId: 41070
  Name: "友谊梦境奇妙夜"
  Type: UGCRoundGame
  Cover: "CDN:41070"
  Test: false
  GameCamParam: "-46676,72910,7885,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "棕棕"
  Description: "使用友谊梦境奇妙夜同款模版，快速设计机关，轻松创作双人游戏。"
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42070
}
rows {
  TemplateId: 41097
  Name: "互动激励系统"
  Type: UGCRoundGame
  Cover: "CDN:41097"
  Test: false
  GameCamParam: "2929,26,561,5,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "了解如何使用扣叮，制作内置签到、每日任务、成就系统、抽奖系统、奖励系统、排行榜和随机事件等。"
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42097
}
rows {
  TemplateId: 41095
  Name: "网络连接恢复"
  Type: UGCRoundGame
  Cover: "CDN:41095"
  Test: false
  GameCamParam: "836,762,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "了解如何使用扣叮，在网络连接恢复后，正确还原界面与相机信息。"
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42095
}
rows {
  TemplateId: 41094
  Name: "创意玩法/爱消除"
  Type: UGCRoundGame
  Cover: "CDN:41094"
  Test: false
  GameCamParam: "-319,-882,338,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "消除烦恼，乐享精彩，一起加入三消的世界，让快乐一触即发!"
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42094
}
rows {
  TemplateId: 41064
  Name: "星莱坞摄影棚"
  Type: UGCRoundGame
  Cover: "CDN:64"
  Test: false
  GameCamParam: "-13850,-3550,2500,0,270,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "各种风格的拍摄场景等你来体验，快来设计和拍摄美美的外观和照片吧~"
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42064
}
rows {
  TemplateId: 41009
  Name: "创意玩法/物理方块"
  Type: UGCRoundGame
  Cover: "CDN:9"
  Test: false
  GameCamParam: "-35350,-650,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "欢乐电玩城等你来探索~使用物理方块实现的小游戏玩法。"
  TemplateSortIndex: 8
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42009
}
rows {
  TemplateId: 41003
  Name: "模拟载具"
  Type: UGCRoundGame
  Cover: "CDN:3"
  Test: false
  GameCamParam: "-21130,11800,10051,0,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "各式各样的载具等你来挑~"
  TemplateSortIndex: 9
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42003
}
rows {
  TemplateId: 41027
  Name: "创意玩法/音乐触发盒"
  Type: UGCRoundGame
  Cover: "CDN:27"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "来听音乐吧！音乐触发盒使用介绍。"
  TemplateSortIndex: 10
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42027
}
rows {
  TemplateId: 41046
  Name: "粒子特效"
  Type: UGCRoundGame
  Cover: "CDN:46"
  Test: false
  GameCamParam: "1100,0,4100,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "粒子发射器功能与效果展示。"
  TemplateSortIndex: 11
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42046
}
rows {
  TemplateId: 41050
  Name: "特效库"
  Type: UGCRoundGame
  Cover: "CDN:50"
  Test: false
  GameCamParam: "41400,-500,0,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "预制特效和添加运动展示。"
  TemplateSortIndex: 12
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42050
}
rows {
  TemplateId: 41006
  Name: "海洋主题景观"
  Type: UGCRoundGame
  Cover: "CDN:6"
  Test: false
  GameCamParam: "4672,0,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "主题场景。谁住在可爱的小屋中呢？"
  TemplateSortIndex: 13
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42006
}
rows {
  TemplateId: 41007
  Name: "甜品主题景观"
  Type: UGCRoundGame
  Cover: "CDN:7"
  Test: false
  GameCamParam: "4622,-120,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "主题场景。甜蜜的故事正等待续写。"
  TemplateSortIndex: 14
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42007
}
rows {
  TemplateId: 41051
  Name: "空白模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 203
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42051
}
rows {
  TemplateId: 41011
  Name: "悬空危境"
  Type: UGCRoundGame
  Cover: "CDN:11"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "地图悬浮于高空，需小心行走，巧妙避开危险，一步失误即掉入无底深渊。"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42011
}
rows {
  TemplateId: 41012
  Name: "瀑中飞板"
  Type: UGCRoundGame
  Cover: "CDN:12"
  Test: false
  GameCamParam: "-250,0,850,-20,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "巨大瀑布间错落飞板，需灵活跳跃躲避危险，探索隐藏在水幕背后的奥秘。"
  MapTag: 203
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42012
}
rows {
  TemplateId: 41013
  Name: "惊险铁串"
  Type: UGCRoundGame
  Cover: "CDN:52"
  Test: false
  GameCamParam: "6400,955,5872,0,30,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "巨大瀑布间错落飞板，需灵活跳跃躲避危险，探索隐藏在水幕背后的奥秘。"
  MapTag: 203
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42013
}
rows {
  TemplateId: 41014
  Name: "山水劫境"
  Type: UGCRoundGame
  Cover: "CDN:14"
  Test: false
  GameCamParam: "400,-530,490,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在转瞬即逝的山水画卷中，体验高处不胜寒的惊险刺激。"
  MapTag: 203
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42014
}
rows {
  TemplateId: 41056
  Name: "决战莲台"
  Type: UGCRoundGame
  Cover: "CDN:56"
  Test: false
  GameCamParam: "1000,-3250,1950,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "荷叶会消失，努力成为最后站在荷叶上的人吧！"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  RelevantLevelTemplateId: 42056
}
rows {
  TemplateId: 41052
  Name: "惊险铁串"
  Type: UGCRoundGame
  Cover: "CDN:52"
  Test: false
  GameCamParam: "500,1150,850,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "四面困厄，巨大铁柱突袭，需灵敏应对不断涌现的致命威胁。"
  MapTag: 203
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "生存_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42052
}
rows {
  TemplateId: 41055
  Name: "谷底对决"
  Type: UGCRoundGame
  Cover: "CDN:55"
  Test: false
  GameCamParam: "500,-650,3400,-15,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "在九宫格之间闪转腾挪，避免踏空落入谷底。"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "生存_社区(挂起)"
  TextReview: 3
}
rows {
  TemplateId: 41045
  Name: "空白团竞模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 302
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42045
}
rows {
  TemplateId: 41044
  Name: "基础团竞模板"
  Type: UGCRoundGame
  Cover: "CDN:44"
  Test: false
  GameCamParam: "-2500,-950,900,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含枪械生成器和掩体的模板进行创作，可以用来创作经典的冲锋竞技、一命对决、推车等玩法"
  MapTag: 302
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42044
}
rows {
  TemplateId: 41040
  Name: "派对邮轮"
  Type: UGCRoundGame
  Cover: "CDN:40"
  Test: false
  GameCamParam: "400,4550,2500,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "豪华游艇盛宴，在欢庆氛围中展开激战，隐藏在歌舞间的精彩对决。"
  MapTag: 302
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42040
}
rows {
  TemplateId: 41053
  Name: "空白混战模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_官方_混战"
  TextReview: 3
  RelevantLevelTemplateId: 42053
}
rows {
  TemplateId: 41042
  Name: "冲锋竞技-边陲小镇"
  Type: UGCRoundGame
  Cover: "CDN:42"
  Test: false
  GameCamParam: "2850,-200,1200,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "在边陲小镇进行角逐，巧妙利用地形，制定战术。"
  MapTag: 302
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_社区_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42042
}
rows {
  TemplateId: 41043
  Name: "冲锋竞技-开星广场"
  Type: UGCRoundGame
  Cover: "CDN:43"
  Test: false
  GameCamParam: "4800,1600,1100,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "充满节日氛围的小镇，既有欢乐又有惊喜。"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_社区_混战"
  TextReview: 3
  RelevantLevelTemplateId: 42043
}
rows {
  TemplateId: 41071
  Name: "团队竞技-孤岛行动"
  Type: UGCRoundGame
  Cover: "CDN:41071"
  Test: false
  GameCamParam: "7499,73,4118,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "孤岛绝境，一枪破局！"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42071
}
rows {
  TemplateId: 41054
  Name: "武器大师-玩法模板"
  Type: UGCRoundGame
  Cover: "CDN:41054"
  Test: false
  GameCamParam: "7055,-458,4046,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "准备好潜入这片古老的东方秘境，让战斗在山水间绽放吧！"
  MapTag: 305
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "射击_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42054
}
rows {
  TemplateId: 41067
  Name: "团队竞技-狙击空城"
  Type: UGCRoundGame
  Cover: "CDN:41067"
  Test: false
  GameCamParam: "6114,-22168,6428,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在农场世界中，一场惊心动魄的射击大战即将拉开帷幕！"
  MapTag: 305
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "射击_社区"
  TextReview: 3
  RelevantLevelTemplateId: 42067
}
rows {
  TemplateId: 41047
  Name: "基础爆破模版"
  Type: UGCRoundGame
  Cover: "CDN:47"
  Test: false
  GameCamParam: "2400,-400,1050,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用爆破基础模板，来制作爆破玩法地图吧。"
  MapTag: 302
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42047
}
rows {
  TemplateId: 41048
  Name: "沙城争锋"
  Type: UGCRoundGame
  Cover: "CDN:48"
  Test: false
  GameCamParam: "1750,-2800,900,-15,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在被沙尘掩盖的古城中展开激烈的争夺，成为沙城的主宰。"
  MapTag: 302
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42048
}
rows {
  TemplateId: 41049
  Name: "空白冒险模板"
  Type: UGCRoundGame
  Cover: "CDN:49"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含怪物示例的模板进行创作，可以用来创作打怪、塔防、合作冒险等玩法"
  MapTag: 306
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42049
}
rows {
  TemplateId: 41057
  Name: "射击玩法-玩家能力演示"
  Type: UGCRoundGame
  Cover: "CDN:57"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "体验射击地图中，玩家移动、跳跃以及射击等各项能力"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_演示"
  TextReview: 3
  RelevantLevelTemplateId: 42057
}
rows {
  TemplateId: 41058
  Name: "基础占点模版"
  Type: UGCRoundGame
  Cover: "CDN:58"
  Test: false
  GameCamParam: "2150,0,1200,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含据点元件的占点基础模板，来制作占点玩法地图吧。"
  MapTag: 302
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  RelevantLevelTemplateId: 42058
}
rows {
  TemplateId: 41059
  Name: "火车角斗场"
  Type: UGCRoundGame
  Cover: "CDN:59"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "在火车站收集物资，爬上车厢，和其他星宝一决胜负吧！"
  MapTag: 307
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_官方_混战"
  TextReview: 3
  RelevantLevelTemplateId: 42059
}
rows {
  TemplateId: 42021
  Name: "跑酷模板"
  Type: UGCRoundGame
  Cover: "CDN:21"
  Test: false
  GameCamParam: "0,0,300,-30,180,0"
  GodCamParam: "0,0,300,-45,180,0"
  Description: "发挥创造力，尝试做出自己的跑酷！"
  MapTag: 1
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41021
}
rows {
  TemplateId: 42008
  Name: "空白模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 1
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41008
}
rows {
  TemplateId: 42001
  Name: "基础模板"
  Type: UGCRoundGame
  Cover: "CDN:1"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在现有基础上尽情创作吧！"
  MapTag: 1
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41001
}
rows {
  TemplateId: 42066
  Name: "海滨派对"
  Type: UGCRoundGame
  Cover: "CDN:66"
  Test: false
  GameCamParam: "15435,4577,9237,0,10,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "尽情享受海滩派对的乐趣。【初级模板】包含海岛、海底两个场景的景观"
  MapTag: 2
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41066
}
rows {
  TemplateId: 42065
  Name: "夏日田野"
  Type: UGCRoundGame
  Cover: "CDN:65"
  Test: false
  GameCamParam: "-3,-387,743,5,163,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "清新的空气中弥漫着田野的芬芳。【初级模板】包含小清新风格的景观"
  MapTag: 2
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41065
}
rows {
  TemplateId: 42062
  Name: "浮岚暖翠"
  Type: UGCRoundGame
  Cover: "CDN:62"
  Test: false
  GameCamParam: "5700,6200,1050,0,200,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "青绿丛中绽芳华。【初级模板】包含国风场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41062
}
rows {
  TemplateId: 42063
  Name: "丛林秘境"
  Type: UGCRoundGame
  Cover: "CDN:63"
  Test: false
  GameCamParam: "100,5250,1250,0,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "深绿之境，自然缱绻。【初级模板】包含森林场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41063
}
rows {
  TemplateId: 42060
  Name: "寒山飞雪"
  Type: UGCRoundGame
  Cover: "CDN:60"
  Test: false
  GameCamParam: "-8000,-4100,1900,0,200,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "银装素裹，天地飞花。【初级模板】包含冬季场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 8
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41060
}
rows {
  TemplateId: 42061
  Name: "荒漠峡谷"
  Type: UGCRoundGame
  Cover: "CDN:61"
  Test: false
  GameCamParam: "4350,-3450,1400,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "沙丘连绵，黄沙漫天。【初级模板】包含沙漠峡谷场景的地形和景观"
  MapTag: 2
  TemplateSortIndex: 9
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41061
}
rows {
  TemplateId: 42022
  Name: "人生之路"
  Type: UGCRoundGame
  Cover: "CDN:22"
  Test: false
  GameCamParam: "-15450,450,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "一路生长，一路历练。【高级模板】包含文本叙事、场景转换、怪物追逐等子案例"
  MapTag: 8
  TemplateSortIndex: 10
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41022
}
rows {
  TemplateId: 42023
  Name: "星宝空间站"
  Type: UGCRoundGame
  Cover: "CDN:23"
  Test: false
  GameCamParam: "-4000,3000,2300,-15,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "星宝空间站等你来解密。【高级模板】包含任务指引、密码门、推箱等子案例"
  MapTag: 7
  TemplateSortIndex: 11
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41023
}
rows {
  TemplateId: 42002
  Name: "梦幻城堡"
  Type: UGCRoundGame
  Cover: "CDN:2"
  Test: false
  GameCamParam: "2415,23414,600,0,-90,0"
  GodCamParam: "2415,23414,600,0,-90,0"
  Description: "少女心爆棚啦！【初级模板】介绍观赏图的基础技巧"
  MapTag: 2
  TemplateSortIndex: 12
  OpenTag: 41
  Remarks: "竞速_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41002
}
rows {
  TemplateId: 42015
  Name: "火之意志"
  Type: UGCRoundGame
  Cover: "CDN:15"
  Test: false
  GameCamParam: "2800,200,5250,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "喵呜酱"
  Description: "完成专属任务吧，燃烧，火之意志！"
  MapTag: 1
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41015
}
rows {
  TemplateId: 42016
  Name: "地铁跑酷"
  Type: UGCRoundGame
  Cover: "CDN:16"
  Test: false
  GameCamParam: "2200,-8000,3000,-30,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "鳕鱼帕尼尼"
  Description: "在元梦里也要全力奔跑，小心被抓到哦~"
  MapTag: 1
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41016
}
rows {
  TemplateId: 42017
  Name: "危机降临"
  Type: UGCRoundGame
  Cover: "CDN:17"
  Test: false
  GameCamParam: "-2500,3200,2200,-30,-45,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "SiriusLord"
  Description: "危机降临，躲避舔食者和追逐者的围追堵截，逃出警察局吧。"
  MapTag: 7
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41017
}
rows {
  TemplateId: 42019
  Name: "浪漫之旅"
  Type: UGCRoundGame
  Cover: "CDN:19"
  Test: false
  GameCamParam: "7250,-50,650,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "叮叮当当当"
  Description: "和许墨一起享受浪漫的时光。"
  MapTag: 2
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41019
}
rows {
  TemplateId: 42020
  Name: "QQ吃豆大战"
  Type: UGCRoundGame
  Cover: "CDN:20"
  Test: false
  GameCamParam: "-2500,-200,10000,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "焦糖绵绵冰"
  Description: "在QQ迷宫里穿梭，获取表情包和小豆子，赢得胜利吧！"
  MapTag: 10
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41020
}
rows {
  TemplateId: 42024
  Name: "长跑酷玩法模板"
  Type: UGCRoundGame
  Cover: "CDN:41024"
  Test: false
  GameCamParam: "8320,1640,-5252,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "每一步都在改写生存法则——你的奔跑，就是新纪元的进化史诗！"
  MapTag: 1
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41024
}
rows {
  TemplateId: 42031
  Name: "每秒+1模板"
  Type: UGCRoundGame
  Cover: "CDN:41031"
  Test: false
  GameCamParam: "-18395,-15526,212,0,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "点击屏幕，释放你的力量！面对更强怪物，成为元梦拳王!"
  MapTag: 14
  TemplateSortIndex: 8
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41031
}
rows {
  TemplateId: 42010
  Name: "我要盖豪宅"
  Type: UGCRoundGame
  Cover: "CDN:41010"
  Test: false
  GameCamParam: "-24797,-59033,-13327,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "来清爽的海岛亲手盖个度假豪宅吧。在这个愉快的时光，我要我的大HOUSE！"
  MapTag: 10
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41010
}
rows {
  TemplateId: 42096
  Name: "棋牌类视角模板"
  Type: UGCRoundGame
  Cover: "CDN:41096"
  Test: false
  GameCamParam: "2000,-3500,250,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "智谋对决，乐趣无穷，棋牌游戏带你体验策略与运气的完美融合！"
  MapTag: 12
  TemplateSortIndex: 8
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41096
}
rows {
  TemplateId: 42030
  Name: "星宝大亨"
  Type: UGCRoundGame
  Cover: "CDN:30"
  Test: false
  GameCamParam: "0,0,0,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "德玛西亚之星"
  Description: "来星世界建造属于自己城市吧~"
  MapTag: 10
  TemplateSortIndex: 9
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41030
}
rows {
  TemplateId: 42091
  Name: "大三巴牌坊"
  Type: UGCRoundGame
  Cover: "CDN:91"
  Test: false
  GameCamParam: "12830,4078,1730,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "kinghuaaa"
  Description: "大三巴牌坊是澳门的标志性建筑物之一，同时也为“澳门八景”之一。"
  MapTag: 2
  TemplateSortIndex: 10
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41091
}
rows {
  TemplateId: 42092
  Name: "维多利亚港"
  Type: UGCRoundGame
  Cover: "CDN:92"
  Test: false
  GameCamParam: "27750,23800,3000,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "kinghuaaa"
  Description: "维多利亚港一直主导香港经济和旅游业发展，是香港成为国际大城市的关键之一！"
  MapTag: 2
  TemplateSortIndex: 11
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41092
}
rows {
  TemplateId: 42093
  Name: "越秀公园"
  Type: UGCRoundGame
  Cover: "CDN:93"
  Test: false
  GameCamParam: "35350,-32600,1200,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "一朵宝"
  Description: "越秀公园的主体越秀山是一座有深厚文化底蕴的名山，早在秦汉时期，就是广州的风景名胜地。"
  MapTag: 2
  TemplateSortIndex: 12
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41093
}
rows {
  TemplateId: 42028
  Name: "百年紫禁城-午门"
  Type: UGCRoundGame
  Cover: "CDN:28"
  Test: false
  GameCamParam: "54904,5548,-67700,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "京城探子&高启明"
  Description: "穿越六百年时空，探秘华夏盛世的宫廷传奇！"
  MapTag: 2
  TemplateSortIndex: 13
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41028
}
rows {
  TemplateId: 42029
  Name: "百年紫禁城-太和门广场"
  Type: UGCRoundGame
  Cover: "CDN:29"
  Test: false
  GameCamParam: "-5655,6131,-67858,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "京城探子&高启明"
  Description: "穿越六百年时空，探秘华夏盛世的宫廷传奇！"
  MapTag: 2
  TemplateSortIndex: 14
  OpenTag: 41
  Remarks: "竞速_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41029
}
rows {
  TemplateId: 42068
  Name: "花花的手账"
  Type: UGCRoundGame
  Cover: "CDN:41068"
  Test: false
  GameCamParam: "24538,-2053,-7158,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "兔兔大魔王"
  Description: "使用花花的手账同款模版，设计故事分支，演绎属于你自己的故事吧！"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41068
}
rows {
  TemplateId: 42069
  Name: "心理健康表"
  Type: UGCRoundGame
  Cover: "CDN:41069"
  Test: false
  GameCamParam: "-55289,4695,-30014,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "小吆西"
  Description: "这是一张可附带问卷的地图模板，你可以自定义你的关卡，引导玩家完成问卷。"
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41069
}
rows {
  TemplateId: 42070
  Name: "友谊梦境奇妙夜"
  Type: UGCRoundGame
  Cover: "CDN:41070"
  Test: false
  GameCamParam: "-46676,72910,7885,0,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "棕棕"
  Description: "使用友谊梦境奇妙夜同款模版，快速设计机关，轻松创作双人游戏。"
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41070
}
rows {
  TemplateId: 42097
  Name: "互动激励系统"
  Type: UGCRoundGame
  Cover: "CDN:41097"
  Test: false
  GameCamParam: "2929,26,561,5,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "了解如何使用扣叮，制作内置签到、每日任务、成就系统、抽奖系统、奖励系统、排行榜和随机事件等。"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41097
}
rows {
  TemplateId: 42094
  Name: "创意玩法/爱消除"
  Type: UGCRoundGame
  Cover: "CDN:41094"
  Test: false
  GameCamParam: "-319,-882,338,5,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "消除烦恼，乐享精彩，一起加入三消的世界，让快乐一触即发!"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41094
}
rows {
  TemplateId: 42095
  Name: "网络连接恢复"
  Type: UGCRoundGame
  Cover: "CDN:41095"
  Test: false
  GameCamParam: "836,762,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "了解如何使用扣叮，在网络连接恢复后，正确还原界面与相机信息。"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41095
}
rows {
  TemplateId: 42064
  Name: "星莱坞摄影棚"
  Type: UGCRoundGame
  Cover: "CDN:64"
  Test: false
  GameCamParam: "-13850,-3550,2500,0,270,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "各种风格的拍摄场景等你来体验，快来设计和拍摄美美的外观和照片吧~"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41064
}
rows {
  TemplateId: 42009
  Name: "创意玩法/物理方块"
  Type: UGCRoundGame
  Cover: "CDN:9"
  Test: false
  GameCamParam: "-35350,-650,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "欢乐电玩城等你来探索~使用物理方块实现的小游戏玩法。"
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41009
}
rows {
  TemplateId: 42003
  Name: "模拟载具"
  Type: UGCRoundGame
  Cover: "CDN:3"
  Test: false
  GameCamParam: "-21130,11800,10051,0,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "各式各样的载具等你来挑~"
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41003
}
rows {
  TemplateId: 42027
  Name: "创意玩法/音乐触发盒"
  Type: UGCRoundGame
  Cover: "CDN:27"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "来听音乐吧！音乐触发盒使用介绍。"
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41027
}
rows {
  TemplateId: 42046
  Name: "粒子特效"
  Type: UGCRoundGame
  Cover: "CDN:46"
  Test: false
  GameCamParam: "1100,0,4100,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "粒子发射器功能与效果展示。"
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41046
}
rows {
  TemplateId: 42050
  Name: "特效库"
  Type: UGCRoundGame
  Cover: "CDN:50"
  Test: false
  GameCamParam: "41400,-500,0,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "预制特效和添加运动展示。"
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41050
}
rows {
  TemplateId: 42006
  Name: "海洋主题景观"
  Type: UGCRoundGame
  Cover: "CDN:6"
  Test: false
  GameCamParam: "4672,0,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "主题场景。谁住在可爱的小屋中呢？"
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41006
}
rows {
  TemplateId: 42007
  Name: "甜品主题景观"
  Type: UGCRoundGame
  Cover: "CDN:7"
  Test: false
  GameCamParam: "4622,-120,200,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "主题场景。甜蜜的故事正等待续写。"
  TemplateSortIndex: 8
  OpenTag: 41
  Remarks: "竞速_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41007
}
rows {
  TemplateId: 42051
  Name: "空白模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 203
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41051
}
rows {
  TemplateId: 42011
  Name: "悬空危境"
  Type: UGCRoundGame
  Cover: "CDN:11"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "地图悬浮于高空，需小心行走，巧妙避开危险，一步失误即掉入无底深渊。"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41011
}
rows {
  TemplateId: 42012
  Name: "瀑中飞板"
  Type: UGCRoundGame
  Cover: "CDN:12"
  Test: false
  GameCamParam: "-250,0,850,-20,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "巨大瀑布间错落飞板，需灵活跳跃躲避危险，探索隐藏在水幕背后的奥秘。"
  MapTag: 203
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41012
}
rows {
  TemplateId: 42013
  Name: "惊险铁串"
  Type: UGCRoundGame
  Cover: "CDN:52"
  Test: false
  GameCamParam: "6400,955,5872,0,30,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "巨大瀑布间错落飞板，需灵活跳跃躲避危险，探索隐藏在水幕背后的奥秘。"
  MapTag: 203
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41013
}
rows {
  TemplateId: 42014
  Name: "山水劫境"
  Type: UGCRoundGame
  Cover: "CDN:14"
  Test: false
  GameCamParam: "400,-530,490,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在转瞬即逝的山水画卷中，体验高处不胜寒的惊险刺激。"
  MapTag: 203
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41014
}
rows {
  TemplateId: 42056
  Name: "决战莲台"
  Type: UGCRoundGame
  Cover: "CDN:56"
  Test: false
  GameCamParam: "1000,-3250,1950,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "荷叶会消失，努力成为最后站在荷叶上的人吧！"
  MapTag: 203
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "生存_官方"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41056
}
rows {
  TemplateId: 42052
  Name: "惊险铁串"
  Type: UGCRoundGame
  Cover: "CDN:52"
  Test: false
  GameCamParam: "500,1150,850,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "四面困厄，巨大铁柱突袭，需灵敏应对不断涌现的致命威胁。"
  MapTag: 203
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "生存_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41052
}
rows {
  TemplateId: 42045
  Name: "空白团竞模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 302
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41045
}
rows {
  TemplateId: 42044
  Name: "基础团竞模板"
  Type: UGCRoundGame
  Cover: "CDN:44"
  Test: false
  GameCamParam: "-2500,-950,900,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含枪械生成器和掩体的模板进行创作，可以用来创作经典的冲锋竞技、一命对决、推车等玩法"
  MapTag: 302
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41044
}
rows {
  TemplateId: 42040
  Name: "派对邮轮"
  Type: UGCRoundGame
  Cover: "CDN:40"
  Test: false
  GameCamParam: "400,4550,2500,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "豪华游艇盛宴，在欢庆氛围中展开激战，隐藏在歌舞间的精彩对决。"
  MapTag: 302
  TemplateSortIndex: 5
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41040
}
rows {
  TemplateId: 42053
  Name: "空白混战模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_官方_混战"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41053
}
rows {
  TemplateId: 42042
  Name: "冲锋竞技-边陲小镇"
  Type: UGCRoundGame
  Cover: "CDN:42"
  Test: false
  GameCamParam: "2850,-200,1200,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "在边陲小镇进行角逐，巧妙利用地形，制定战术。"
  MapTag: 302
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_社区_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41042
}
rows {
  TemplateId: 42043
  Name: "冲锋竞技-开星广场"
  Type: UGCRoundGame
  Cover: "CDN:43"
  Test: false
  GameCamParam: "4800,1600,1100,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  CreatorName: "软饭硬吃"
  Description: "充满节日氛围的小镇，既有欢乐又有惊喜。"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_社区_混战"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41043
}
rows {
  TemplateId: 42071
  Name: "团队竞技-孤岛行动"
  Type: UGCRoundGame
  Cover: "CDN:41071"
  Test: false
  GameCamParam: "7499,73,4118,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "孤岛绝境，一枪破局！"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41071
}
rows {
  TemplateId: 42054
  Name: "武器大师-玩法模板"
  Type: UGCRoundGame
  Cover: "CDN:41054"
  Test: false
  GameCamParam: "7055,-458,4046,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "准备好潜入这片古老的东方秘境，让战斗在山水间绽放吧！"
  MapTag: 305
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41054
}
rows {
  TemplateId: 42067
  Name: "团队竞技-狙击空城"
  Type: UGCRoundGame
  Cover: "CDN:41067"
  Test: false
  GameCamParam: "6114,-22168,6428,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在农场世界中，一场惊心动魄的射击大战即将拉开帷幕！"
  MapTag: 305
  TemplateSortIndex: 2
  OpenTag: 41
  Remarks: "射击_社区"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41067
}
rows {
  TemplateId: 42047
  Name: "基础爆破模版"
  Type: UGCRoundGame
  Cover: "CDN:47"
  Test: false
  GameCamParam: "2400,-400,1050,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用爆破基础模板，来制作爆破玩法地图吧。"
  MapTag: 302
  TemplateSortIndex: 3
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41047
}
rows {
  TemplateId: 42048
  Name: "沙城争锋"
  Type: UGCRoundGame
  Cover: "CDN:48"
  Test: false
  GameCamParam: "1750,-2800,900,-15,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "在被沙尘掩盖的古城中展开激烈的争夺，成为沙城的主宰。"
  MapTag: 302
  TemplateSortIndex: 6
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41048
}
rows {
  TemplateId: 42049
  Name: "空白冒险模板"
  Type: UGCRoundGame
  Cover: "CDN:49"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含怪物示例的模板进行创作，可以用来创作打怪、塔防、合作冒险等玩法"
  MapTag: 306
  TemplateSortIndex: 4
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41049
}
rows {
  TemplateId: 42057
  Name: "射击玩法-玩家能力演示"
  Type: UGCRoundGame
  Cover: "CDN:57"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "体验射击地图中，玩家移动、跳跃以及射击等各项能力"
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_演示"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41057
}
rows {
  TemplateId: 42058
  Name: "基础占点模版"
  Type: UGCRoundGame
  Cover: "CDN:58"
  Test: false
  GameCamParam: "2150,0,1200,-15,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "使用包含据点元件的占点基础模板，来制作占点玩法地图吧。"
  MapTag: 302
  TemplateSortIndex: 7
  OpenTag: 41
  Remarks: "射击_官方_团队"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41058
}
rows {
  TemplateId: 42059
  Name: "火车角斗场"
  Type: UGCRoundGame
  Cover: "CDN:59"
  Test: false
  GameCamParam: "-450,0,600,-30,0,0"
  GodCamParam: "0,0,0,-45,0,0"
  Description: "在火车站收集物资，爬上车厢，和其他星宝一决胜负吧！"
  MapTag: 307
  TemplateSortIndex: 1
  OpenTag: 41
  Remarks: "射击_官方_混战"
  TextReview: 3
  IsMultiLevel: true
  RelevantLevelTemplateId: 41059
}
rows {
  TemplateId: 410101
  Name: "空白乐园模板"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 12
  TemplateSortIndex: 1
  OpenTag: 500
  Remarks: "乐园_官方"
  TextReview: 3
}
rows {
  TemplateId: 410102
  Name: "乐园"
  Type: UGCRoundGame
  Cover: "CDN:8"
  Test: false
  GameCamParam: "0,0,0,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 12
  TemplateSortIndex: 1
  OpenTag: 500
  Remarks: "乐园_社区"
  TextReview: 3
}
rows {
  TemplateId: 36031
  Name: "空白模板"
  Type: OMDGame
  Cover: "CDN:36031"
  Test: false
  GameCamParam: "1068,-43,442,-30,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 1
  OpenTag: 36
  Remarks: "塔防_官方"
  TextReview: 3
}
rows {
  TemplateId: 36032
  Name: "基础模板-主厅"
  Type: OMDGame
  Cover: "CDN:36032"
  Test: false
  GameCamParam: "4204,-2917,-666,0,180,0"
  GodCamParam: "4204,-2917,-666,0,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 2
  OpenTag: 36
  Remarks: "塔防_官方"
  TextReview: 3
}
rows {
  TemplateId: 36033
  Name: "基础模板-冰雪码头"
  Type: OMDGame
  Cover: "CDN:36033"
  Test: false
  GameCamParam: "904,59,209,0,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 3
  OpenTag: 36
  Remarks: "塔防_官方"
  TextReview: 3
}
rows {
  TemplateId: 36001
  Name: "基础模板-北翼"
  Type: OMDGame
  Cover: "CDN:36001"
  Test: false
  GameCamParam: "0,789,0,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 1
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36002
  Name: "基础模板-侧门"
  Type: OMDGame
  Cover: "CDN:36002"
  Test: false
  GameCamParam: "-2350,-3787,129,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 2
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36003
  Name: "基础模板-分裂阶梯"
  Type: OMDGame
  Cover: "CDN:36003"
  Test: false
  GameCamParam: "0,-290,115,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 3
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36004
  Name: "基础模板-封闭营房"
  Type: OMDGame
  Cover: "CDN:36004"
  Test: false
  GameCamParam: "298,487,150,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 4
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36005
  Name: "基础模板-悬崖峭壁"
  Type: OMDGame
  Cover: "CDN:36005"
  Test: false
  GameCamParam: "-542,77,200,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 5
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36006
  Name: "基础模板-前庭"
  Type: OMDGame
  Cover: "CDN:36006"
  Test: false
  GameCamParam: "20,5496,-200,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 6
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36007
  Name: "基础模板-大师庭院"
  Type: OMDGame
  Cover: "CDN:36007"
  Test: false
  GameCamParam: "694,10,150,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 7
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36008
  Name: "基础模板-泥泞壁"
  Type: OMDGame
  Cover: "CDN:36008"
  Test: false
  GameCamParam: "-978,1517,716,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 8
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36009
  Name: "基础模板-法师塔"
  Type: OMDGame
  Cover: "CDN:36009"
  Test: false
  GameCamParam: "-218,6781,132,-10,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 9
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36010
  Name: "基础模板-废弃通道"
  Type: OMDGame
  Cover: "CDN:36010"
  Test: false
  GameCamParam: "0,753,150,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 10
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36011
  Name: "基础模板-秘境深谷"
  Type: OMDGame
  Cover: "CDN:36011"
  Test: false
  GameCamParam: "0,0,200,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 11
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36012
  Name: "基础模板-绿洲前哨"
  Type: OMDGame
  Cover: "CDN:36012"
  Test: false
  GameCamParam: "-4133,62,-717,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 12
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36013
  Name: "基础模板-沙漠之墙"
  Type: OMDGame
  Cover: "CDN:36013"
  Test: false
  GameCamParam: "-565,-3598,246,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 13
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36027
  Name: "基础模板-教团神殿"
  Type: OMDGame
  Cover: "CDN:36027"
  Test: false
  GameCamParam: "0,-5599,1084,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 14
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36028
  Name: "基础模板-教团飞地"
  Type: OMDGame
  Cover: "CDN:36028"
  Test: false
  GameCamParam: "0,789,0,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "从零开始，发挥你的想象力吧！"
  MapTag: 15
  TemplateSortIndex: 15
  OpenTag: 36
  Remarks: "塔防_官方（下架）"
  TextReview: 3
}
rows {
  TemplateId: 36014
  Name: "北翼"
  Type: OMDGame
  Cover: "CDN:36001"
  Test: false
  GameCamParam: "0,789,0,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 1
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36015
  Name: "侧门"
  Type: OMDGame
  Cover: "CDN:36002"
  Test: false
  GameCamParam: "-2350,-3787,129,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 2
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36016
  Name: "分裂阶梯"
  Type: OMDGame
  Cover: "CDN:36003"
  Test: false
  GameCamParam: "0,-290,115,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 3
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36017
  Name: "封闭营房"
  Type: OMDGame
  Cover: "CDN:36004"
  Test: false
  GameCamParam: "298,487,150,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 4
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36018
  Name: "悬崖峭壁"
  Type: OMDGame
  Cover: "CDN:36005"
  Test: false
  GameCamParam: "-542,77,200,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 5
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36019
  Name: "前庭"
  Type: OMDGame
  Cover: "CDN:36006"
  Test: false
  GameCamParam: "20,5496,-200,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 6
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36020
  Name: "大师庭院"
  Type: OMDGame
  Cover: "CDN:36007"
  Test: false
  GameCamParam: "694,10,150,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 7
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36021
  Name: "泥泞壁"
  Type: OMDGame
  Cover: "CDN:36008"
  Test: false
  GameCamParam: "-978,1517,716,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 8
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36022
  Name: "法师塔"
  Type: OMDGame
  Cover: "CDN:36009"
  Test: false
  GameCamParam: "-218,6781,132,-10,180,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 9
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36023
  Name: "废弃通道"
  Type: OMDGame
  Cover: "CDN:36010"
  Test: false
  GameCamParam: "0,753,150,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 10
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36024
  Name: "秘境深谷"
  Type: OMDGame
  Cover: "CDN:36011"
  Test: false
  GameCamParam: "0,0,200,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 11
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36025
  Name: "绿洲前哨"
  Type: OMDGame
  Cover: "CDN:36012"
  Test: false
  GameCamParam: "-4133,62,-717,-10,0,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 12
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36026
  Name: "沙漠之墙"
  Type: OMDGame
  Cover: "CDN:36013"
  Test: false
  GameCamParam: "-565,-3598,246,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 13
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36029
  Name: "教团神殿"
  Type: OMDGame
  Cover: "CDN:36027"
  Test: false
  GameCamParam: "0,-5599,1084,-10,90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 14
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
rows {
  TemplateId: 36030
  Name: "教团飞地"
  Type: OMDGame
  Cover: "CDN:36028"
  Test: false
  GameCamParam: "0,789,0,-10,-90,0"
  GodCamParam: "0,0,0,-45,180,0"
  Description: "兽人塔防，造塔打怪，坚持到底"
  MapTag: 15
  TemplateSortIndex: 15
  OpenTag: 36
  Remarks: "塔防_演示"
  TextReview: 3
}
