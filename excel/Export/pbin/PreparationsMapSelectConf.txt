com.tencent.wea.xlsRes.table_PreparationsMapSelectConfData
excel/xls/B_备战.xlsx sheet:选地图
rows {
  id: 1
  ModeId: 105
  LevelId: 50301
  LevelName: "古宅[8人]"
  LevelIcon: "CDN:T_loading_Checkpoint_50301"
  Sequence: 1
  ModeName: "古宅[8人]"
  ModeIcon: "T_E3_Prepare_Icon_Yard"
  ModeDesc: "古宅（8人）是规则较简单的娱乐局，适合入门学习以及喜欢简单轻松氛围的朋友们~\n古宅（8人）固定是<GoldYellow>古宅地图</>，没有<GoldYellow>中立阵营</>。"
  LevelIcon2: "CDN:T_loading_Checkpoint_50301"
  ModeSequence: 6
  ReadyID: 1
}
rows {
  id: 2
  ModeId: 113
  LevelId: 0
  LevelName: "休闲-随机"
  LevelIcon: "CDN:T_E3_Prepare_Img_Map_001"
  Sequence: 2
  ModeName: "休闲模式[10人]"
  ModeIcon: "T_E3_Prepare_Icon_Relaxation"
  LevelName2: "随机地图"
  ModeDesc: "来一场轻松休闲的娱乐对局吧！\n休闲模式采用经典的玩法规则，同时，你可以<GoldYellow>自选地图</>哦~"
  LevelIcon2: "CDN:T_E3_Prepare_Img_Map_001"
  ModeSequence: 2
  LevelSequence: 1
  ReadyID: 1
}
rows {
  id: 3
  ModeId: 113
  LevelId: 50301
  LevelName: "休闲-古宅"
  LevelIcon: "CDN:T_loading_Checkpoint_50301"
  Sequence: 4
  LevelName2: "古宅"
  LevelIcon2: "CDN:T_loading_Checkpoint_50301"
  LevelSequence: 2
  ReadyID: 1
}
rows {
  id: 4
  ModeId: 113
  LevelId: 50302
  LevelName: "休闲-暗夜堡"
  LevelIcon: "CDN:T_loading_Checkpoint_50302"
  Sequence: 5
  LevelName2: "暗夜堡"
  LevelIcon2: "CDN:T_loading_Checkpoint_50302"
  LevelSequence: 98
  ReadyID: 1
}
rows {
  id: 5
  ModeId: 113
  LevelId: 50303
  LevelName: "休闲-遗迹"
  LevelIcon: "CDN:T_loading_Checkpoint_50303"
  Sequence: 6
  LevelName2: "遗迹"
  LevelIcon2: "CDN:T_loading_Checkpoint_50303"
  LevelSequence: 99
  ReadyID: 1
}
rows {
  id: 6
  ModeId: 151
  LevelId: 0
  LevelName: "排位赛"
  LevelIcon: "CDN:T_E3_Prepare_Img_Map_001"
  Sequence: 7
  ModeName: "排位赛"
  ModeIcon: "T_E3_Prepare_Icon_Rank"
  ModeDesc: "在排位赛中，你可以赢取段位分数，提升【谁是狼人】的专属段位。加油，努力上分吧！\n排位赛只支持<GoldYellow>单人</>进行匹配，地图是<GoldYellow>随机</>的。"
  LevelIcon2: "CDN:T_E3_Prepare_Img_RankedSeason"
  ModeSequence: 4
  ReadyID: 1
}
rows {
  id: 7
  ModeId: 113
  LevelId: 50304
  LevelName: "休闲-学校"
  LevelIcon: "CDN:T_loading_Checkpoint_50304"
  Sequence: 8
  LevelName2: "学校"
  LevelIcon2: "CDN:T_loading_Checkpoint_50304"
  LevelSequence: 97
  ReadyID: 1
}
rows {
  id: 8
  ModeId: 113
  LevelId: 50305
  LevelName: "休闲-月球"
  LevelIcon: "CDN:T_loading_Checkpoint_50305"
  Sequence: 9
  LevelName2: "月球"
  LevelIcon2: "CDN:T_loading_Checkpoint_50305"
  LevelSequence: 96
  ReadyID: 1
}
rows {
  id: 9
  ModeId: 106
  LevelId: 0
  LevelName: "单人休闲"
  LevelIcon: "CDN:T_E3_Prepare_Img_Map_002"
  Sequence: 3
  ModeName: "单人休闲[12人]"
  ModeIcon: "T_E3_Prepare_Icon_Single"
  ModeDesc: "单人休闲模式为12人娱乐对局，只允许<GoldYellow>单人</>进行匹配，地图是<GoldYellow>随机</>的。"
  LevelIcon2: "CDN:T_E3_Prepare_Img_SingleMode"
  ModeSequence: 5
  ReadyID: 1
}
rows {
  id: 10
  ModeId: 109
  LevelId: 0
  LevelName: "双人模式"
  LevelIcon: "CDN:T_E3_Prepare_Img_Map_002"
  Sequence: 10
  ModeName: "双人模式"
  ModeIcon: "T_E3_Prepare_Icon_Double"
  ModeDesc: "在双人模式中，基本游戏规则不变。每2名玩家会成为一个小队，小队内可以有更多的协作与交流。\n双人模式<GoldYellow>（10人）</>支持<GoldYellow>单人或双人组队</>参与，<GoldYellow>地图是随机</>的。"
  LevelIcon2: "CDN:T_E3_Prepare_Img_DoubleMode"
  ModeSequence: 3
  ReadyID: 1
}
rows {
  id: 11
  ModeId: 113
  LevelId: 50306
  LevelName: "休闲-游乐场"
  LevelIcon: "CDN:T_loading_Checkpoint_50306"
  Sequence: 11
  LevelName2: "游乐场"
  LevelIcon2: "CDN:T_loading_Checkpoint_50306"
  LevelSequence: 95
  ReadyID: 1
}
rows {
  id: 12
  ModeId: 113
  LevelId: 50307
  LevelName: "休闲-牛牛街"
  LevelIcon: "CDN:T_loading_Checkpoint_50307"
  Sequence: 12
  LevelName2: "牛牛街"
  LevelIcon2: "CDN:T_loading_Checkpoint_50307"
  LevelSequence: 94
  ReadyID: 1
}
rows {
  id: 13
  ModeId: 113
  LevelId: 50308
  LevelName: "休闲-冰湖村"
  LevelIcon: "CDN:T_loading_Checkpoint_50308"
  Sequence: 13
  LevelName2: "冰湖村"
  LevelIcon2: "CDN:T_loading_Checkpoint_50308"
  LevelSequence: 93
  ReadyID: 1
}
rows {
  id: 14
  ModeId: 113
  LevelId: 50309
  LevelName: "休闲-精灵谷"
  LevelIcon: "CDN:T_loading_Checkpoint_50309"
  Sequence: 14
  LevelName2: "精灵谷"
  LevelIcon2: "CDN:T_loading_Checkpoint_50309"
  LevelSequence: 92
  ReadyID: 1
}
rows {
  id: 15
  ModeId: 112
  LevelId: 0
  LevelName: "秘境寻宝"
  LevelIcon: "CDN:T_E3_Prepare_Img_TreasureHunt"
  Sequence: 15
  ModeName: "秘境寻宝"
  ModeIcon: "T_E3_Prepare_Icon_TreasureHunt"
  LevelName2: "秘境寻宝"
  ModeDesc: "寻宝小队出发啦！在该模式中，平民们的任务是<GoldYellow>搜寻并上交各种宝物</>，同时，地图设施、可用身份等少量机制调整。\n玩法在每日<GoldYellow>19:00-24:00</>限时开放。"
  LevelIcon2: "CDN:T_E3_Prepare_Img_TreasureHunt"
  ModeSequence: 1
  ReadyID: 1
}
rows {
  id: 102
  ModeId: 5600
  LevelId: 0
  LevelName: "峡谷3v3"
  LevelIcon: "CDN:CT_Arena_Prepare_Img_3v3_small"
  Sequence: 13
  ModeName: "峡谷3v3"
  ModeIcon: "T_LetsGo_Arena_Prepare_Icon_Casual"
  LevelName2: "峡谷3v3"
  ModeDesc: "三人组队并肩竞技，决战峡谷之巅！"
  LevelIcon2: "CDN:CT_Arena_Prepare_Img_3v3_small"
  ModeSequence: 1
  LevelSequence: 1
  ReadyID: 3
  activityStyleParam: "1,117;1,118;1,119;4,120;4,104"
}
rows {
  id: 103
  ModeId: 6006
  LevelId: 0
  LevelName: "峡谷吃鸡"
  LevelIcon: "CDN:CT_Arena_Prepare_Img_BattleRoyale_small"
  Sequence: 13
  ModeName: "峡谷吃鸡"
  ModeIcon: "T_LetsGo_BS_Prepare_Icon_BS3v3"
  LevelName2: "峡谷吃鸡"
  ModeDesc: "双人配合五个阵营，争夺精彩刺激的竞技赛冠军！"
  LevelIcon2: "CDN:CT_Arena_Prepare_Img_BattleRoyale_small"
  ModeSequence: 4
  LevelSequence: 4
  ReadyID: 3
  activityStyleParam: "1,117;1,118;1,119;4,120;4,104"
  endTime: "2025-2-9 23:59:59"
}
rows {
  id: 104
  ModeId: 5700
  LevelId: 0
  LevelName: "峡谷占地盘"
  LevelIcon: "CDN:CT_Arena_Prepare_Img_HotZone_small"
  Sequence: 13
  ModeName: "峡谷占地盘"
  ModeIcon: "T_LetsGo_Arena_Prepare_Icon_HotZone"
  LevelName2: "峡谷占地盘"
  ModeDesc: "6名星宝将组成2支3人小队进行竞技对抗!"
  LevelIcon2: "CDN:CT_Arena_Prepare_Img_HotZone_small"
  ModeSequence: 5
  LevelSequence: 5
  ReadyID: 3
  activityStyleParam: "1,117;1,118;1,119;4,120;4,104"
}
rows {
  id: 105
  ModeId: 6300
  LevelId: 0
  LevelName: "峡谷足球"
  LevelIcon: "CDN:CT_Arena_Prepare_Img_5v5_small"
  Sequence: 13
  ModeName: "峡谷足球"
  ModeIcon: "T_LetsGo_Arena_Prepare_Icon_Football"
  LevelName2: "峡谷足球"
  ModeDesc: "5人小队对抗，在绿茵场展现热血与球技！谁将问鼎大力神杯！"
  LevelIcon2: "CDN:CT_Arena_Prepare_Img_5v5_small"
  ModeSequence: 3
  LevelSequence: 3
  ReadyID: 3
  activityStyleParam: "1,117;1,118;1,119;4,120;4,104"
}
rows {
  id: 112
  ModeId: 6101
  LevelId: 0
  LevelName: "峡谷5v5"
  LevelIcon: "CDN:CT_Arena_Prepare_Img_5v5_small"
  Sequence: 13
  ModeName: "峡谷5v5"
  ModeIcon: "T_LetsGo_Arena_Prepare_Icon_Moba5v5"
  LevelName2: "峡谷5v5"
  ModeDesc: "经典的5v5峡谷对决，率先击破敌方水晶，即可获得胜利！"
  LevelIcon2: "CDN:CT_Arena_Prepare_Img_5v5_small"
  ModeSequence: 2
  LevelSequence: 2
  ReadyID: 3
  activityStyleParam: "1,117;1,118;1,119;4,120;4,104"
}
rows {
  id: 201
  ModeId: 350
  LevelId: 0
  LevelName: "大王别抓我单王休闲"
  ModeName: "大王别抓我单王休闲"
  LevelName2: "大王别抓我单王休闲"
  activityStyleParam: "1,507;1,508;1,509;1,510;1,511"
}
rows {
  id: 202
  ModeId: 352
  LevelId: 0
  LevelName: "大王别抓我单王排位"
  ModeName: "大王别抓我单王排位"
  LevelName2: "大王别抓我单王排位"
  activityStyleParam: "1,507;1,508;1,509;1,510;1,511"
}
rows {
  id: 203
  ModeId: 353
  LevelId: 0
  LevelName: "大王别抓我三王休闲"
  ModeName: "大王别抓我三王休闲"
  LevelName2: "大王别抓我三王休闲"
  activityStyleParam: "1,507;1,508;1,509;1,510;1,511"
}
rows {
  id: 204
  ModeId: 354
  LevelId: 0
  LevelName: "大王别抓我三王排位"
  ModeName: "大王别抓我三王排位"
  LevelName2: "大王别抓我三王排位"
  activityStyleParam: "1,507;1,508;1,509;1,510;1,511"
}
