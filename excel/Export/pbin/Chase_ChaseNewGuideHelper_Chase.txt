com.tencent.wea.xlsRes.table_ChaseNewGuideHelper_Chase
excel/xls/Chase/D_大王新手局引导条件.xlsx sheet:Sheet1
rows {
  ID: 1
  StartConditions: "{{c_InTaskIds={ids = {1, 3,23,25}}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearStarChat\"}, {func=\"ShowGuideTips\", data={title=\"搜寻星路仪\", text=\"跟随指引，前往搜寻星路仪\"}}}"
  Priority: 5
  AICommand: "{Cmd=\"\"}"
  OpenMatch: 353
  SideId: 0
}
rows {
  ID: 2
  StartConditions: "{{c_InTaskIds={ids = {2,24}, isVaild=\"NoTangsengPeople\"}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByTangSengTan\"}, {func=\"ShowGuideTips\", data={title=\"获取#唐僧#法宝\", text=\"跟随指引，前往获取法宝化身#唐僧#\"}}}"
  Priority: 7
  AICommand: "{Cmd=\"\"}"
  OpenMatch: 353
  SideId: 0
}
rows {
  ID: 3
  StartConditions: "{{c_InTaskIds={ids = {4,26}}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearDoor\"}, {func=\"ShowGuideTips\", data={title=\"前往传送门\", text=\"跟随指引，前往开启传送门\"}}}"
  Priority: 8
  OpenMatch: 353
  SideId: 0
}
rows {
  ID: 4
  StartConditions: "{{c_InTaskIds={ids = {5,27}, isVaild=\"MyIsTangSeng\"}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearDoor\"}, {func=\"ShowGuideTips\", data={title=\"化身#唐僧#逃离\", text=\"跟随指引，前往传送门逃离\"}}}"
  Priority: 8
  OpenMatch: 353
  SideId: 0
}
rows {
  ID: 5
  StartConditions: "{{c_InTaskIds={ids = {5,27}, isVaild=\"OtherIsTangSeng\"}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearTangsengPlayer\"}, {func=\"ShowGuideTips\", data={title=\"护送#唐僧#逃离\", text=\"跟随指引，前往#唐僧#身边护送\"}}}"
  Priority: 4
  OpenMatch: 353
  SideId: 0
}
rows {
  ID: 6
  StartConditions: "{{c_MyPlayer_BossResponseLevel={2,3}}}"
  CustomGuideDisplay: "{{func=\"ShowNearBossArrow\"}, {func=\"ShowGuideTips\", data={title=\"暗星在附近\", text=\"暗星在附近了，快远离\"}}}"
  Priority: 9
  OpenMatch: 353
  SideId: 0
}
rows {
  ID: 7
  StartConditions: "{c_Player_InAltar_And_ResponseLevel={ResponseLevels=1}}"
  CustomGuideDisplay: "{{func=\"DoConditionFunc\", data={className=\"c_Player_InAltar_And_ResponseLevel\", funcName=\"GetNearCharacter\"}}, {func=\"ShowGuideTips\", data={title=\"去救援\", text=\"队友被放在祭坛上了，去救援\"}}}"
  Priority: 3
  OpenMatch: 353
  SideId: 0
}
rows {
  ID: 8
  StartConditions: "{{c_InTaskIds={ids = {6,28}}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearRepairingOrNearStarChat\"}, {func=\"ShowGuideTips\", data={title=\"阻止搜寻星路仪\", text=\"跟随指引，前往未修复星路仪寻找星宝\"}}}"
  Priority: 5
  OpenMatch: 353
  SideId: 1
}
rows {
  ID: 9
  StartConditions: "{{c_InTaskIds={ids = {9,31}, isVaild=\"NoTangsengPeople\"}, c_Player_ToTangsengTanSize=80}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByTangSengTan\"}, {func=\"ShowGuideTips\", data={title=\"阻止化生#唐僧#\", text=\"跟随指引，前往阻止星宝化身#唐僧#\"}}}"
  Priority: 6
  OpenMatch: 353
  SideId: 1
}
rows {
  ID: 10
  StartConditions: "{{c_InTaskIds={ids = {7,29}, isVaild=\"HasTangsengPeople\"}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearTangsengPlayer\"}, {func=\"ShowGuideTips\", data={title=\"前往击倒#唐僧#\", text=\"跟随指引，前往攻击化身#唐僧#玩家\"}}}"
  Priority: 9
  OpenMatch: 353
  SideId: 1
}
rows {
  ID: 11
  StartConditions: "{{c_InTaskIds={ids = {10,32}, isVaild=\"HasTangsengPeople\"}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearTangsengPlayer\"}, {func=\"ShowGuideTips\", data={title=\"阻止#唐僧#逃离\", text=\"跟随指引，阻止#唐僧#逃离\"}}}"
  Priority: 9
  OpenMatch: 353
  SideId: 1
}
rows {
  ID: 12
  StartConditions: "{{c_InTaskIds={ids = {8,30}}, c_Player_ToDoorSize=80}}"
  CustomGuideDisplay: "{{func=\"DoConditionFunc\", data={className=\"c_Player_ToDoorSize\", funcName=\"GetTargetDoor\"}}, {func=\"ShowGuideTips\", data={title=\"阻止逃离\", text=\"跟随指引，前往阻止星宝打开大门\"}}}"
  Priority: 8
  OpenMatch: 353
  SideId: 1
}
rows {
  ID: 13
  StartConditions: "{{c_MyBoss_PlayerResponseLevel={2,3}}}"
  CustomGuideDisplay: "{{func=\"DoConditionFunc\", data={className=\"c_MyBoss_PlayerResponseLevel\", funcName=\"GetTargetPlayerCharacter\"}}, {func=\"ShowGuideTips\", data={title=\"追击星宝\", text=\"星宝在附近了，追击并尝试攻击星宝\"}}}"
  Priority: 7
  OpenMatch: 353
  SideId: 1
}
rows {
  ID: 14
  StartConditions: "{{c_MyBoss_Catched_Player=1}}"
  CustomGuideDisplay: "{{func=\"ShowNearCanInAlter\"}, {func=\"ShowGuideTips\", data={title=\"放逐星宝\", text=\"前往最近祭坛放逐星宝\"}}}"
  Priority: 10
  OpenMatch: 353
  SideId: 1
}
rows {
  ID: 15
  StartConditions: "{{c_InTaskIds={ids = {11,17}}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearStarChat\"}, {func=\"ShowGuideTips\", data={title=\"解锁星路仪\", text=\"跟随指引，前往搜寻星路仪\"}}}"
  Priority: 5
  AICommand: "{Cmd=\"\"}"
  OpenMatch: 350
  SideId: 0
}
rows {
  ID: 16
  StartConditions: "{{c_InTaskIds={ids = {12,18}}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearDoor\"}, {func=\"ShowGuideTips\", data={title=\"前往传送门\", text=\"跟随指引，前往开启传送门\"}}}"
  Priority: 8
  OpenMatch: 350
  SideId: 0
}
rows {
  ID: 17
  StartConditions: "{{c_InTaskIds={ids = 13,19}}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearDoor\"}, {func=\"ShowGuideTips\", data={title=\"化身#唐僧#逃离\", text=\"跟随指引，前往传送门逃离\"}}}"
  Priority: 8
  OpenMatch: 350
  SideId: 0
}
rows {
  ID: 18
  StartConditions: "{{c_MyPlayer_BossResponseLevel={2,3}}}"
  CustomGuideDisplay: "{{func=\"ShowNearBossArrow\"}, {func=\"ShowGuideTips\", data={title=\"暗星在附近\", text=\"暗星在附近了，快远离\"}}}"
  Priority: 9
  OpenMatch: 350
  SideId: 0
}
rows {
  ID: 19
  StartConditions: "{c_Player_InAltar_And_ResponseLevel={ResponseLevels=1}}"
  CustomGuideDisplay: "{{func=\"DoConditionFunc\", data={className=\"c_Player_InAltar_And_ResponseLevel\", funcName=\"GetNearCharacter\"}}, {func=\"ShowGuideTips\", data={title=\"去救援\", text=\"队友被放在祭坛上了，去救援\"}}}"
  Priority: 3
  OpenMatch: 350
  SideId: 0
}
rows {
  ID: 20
  StartConditions: "{{c_InTaskIds={ids = {14,20}}}}"
  CustomGuideDisplay: "{{func=\"ShowGuideLineByNearRepairingOrNearStarChat\"}, {func=\"ShowGuideTips\", data={title=\"阻止搜寻星路仪\", text=\"跟随指引，前往未修复星路仪寻找星宝\"}}}"
  Priority: 5
  OpenMatch: 350
  SideId: 1
}
rows {
  ID: 21
  StartConditions: "{{c_InTaskIds={ids = {15,21}}, c_Player_ToDoorSize=80}}"
  CustomGuideDisplay: "{{func=\"DoConditionFunc\", data={className=\"c_Player_ToDoorSize\", funcName=\"GetTargetDoor\"}}, {func=\"ShowGuideTips\", data={title=\"阻止打开大门\", text=\"跟随指引，前往阻止星宝打开大门\"}}}"
  Priority: 8
  OpenMatch: 350
  SideId: 1
}
rows {
  ID: 22
  StartConditions: "{{c_InTaskIds={ids = {16,22}}, c_Player_ToDoorSize=80}}"
  CustomGuideDisplay: "{{func=\"DoConditionFunc\", data={className=\"c_Player_ToDoorSize\", funcName=\"GetTargetDoor\"}}, {func=\"ShowGuideTips\", data={title=\"阻止星宝前往大门逃离\", text=\"跟随指引，阻止星宝前往大门逃离\"}}}"
  Priority: 8
  OpenMatch: 350
  SideId: 1
}
rows {
  ID: 23
  StartConditions: "{{c_MyBoss_PlayerResponseLevel={2,3}}}"
  CustomGuideDisplay: "{{func=\"DoConditionFunc\", data={className=\"c_MyBoss_PlayerResponseLevel\", funcName=\"GetTargetPlayerCharacter\"}}, {func=\"ShowGuideTips\", data={title=\"追击星宝\", text=\"星宝在附近了，追击并尝试攻击星宝\"}}}"
  Priority: 7
  OpenMatch: 350
  SideId: 1
}
rows {
  ID: 24
  StartConditions: "{{c_MyBoss_Catched_Player=1}}"
  CustomGuideDisplay: "{{func=\"ShowNearCanInAlter\"}, {func=\"ShowGuideTips\", data={title=\"放逐星宝\", text=\"前往最近祭坛放逐星宝\"}}}"
  Priority: 10
  OpenMatch: 350
  SideId: 1
}
