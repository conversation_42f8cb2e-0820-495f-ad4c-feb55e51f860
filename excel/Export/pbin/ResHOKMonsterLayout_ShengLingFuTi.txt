com.tencent.wea.xlsRes.table_ResHOKMonsterLayout
excel/xls/HOK/B_HOK_野怪刷新表.xlsx sheet:野怪刷新配置-生灵附体
rows {
  id: 101001
  type: 1
  name: "两只的野怪"
  groupId: 1000
  sideId: 10
  monsterList {
    monsterId: 801000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 13
  rebirthTime: 72
  rebirthCount: 0
  layoutSideId: 1
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 101004
  type: 1
  name: "一只的野怪"
  groupId: 1003
  sideId: 10
  monsterList {
    monsterId: 808000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 13
  rebirthTime: 72
  rebirthCount: 0
  layoutSideId: 1
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 101005
  type: 1
  name: "两只的野怪"
  groupId: 1004
  sideId: 10
  monsterList {
    monsterId: 801000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 13
  rebirthTime: 72
  rebirthCount: 0
  layoutSideId: 0
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 101008
  type: 1
  name: "一只的野怪"
  groupId: 1007
  sideId: 10
  monsterList {
    monsterId: 808000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 13
  rebirthTime: 72
  rebirthCount: 0
  layoutSideId: 0
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 201001
  type: 5
  name: "蓝buff-红方"
  groupId: 1010
  sideId: 10
  monsterList {
    monsterId: 803000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 10
  rebirthTime: 75
  rebirthCount: 0
  showLeftTime: 15
  layoutSideId: 1
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 301001
  type: 4
  name: "红buff-红方"
  groupId: 1011
  sideId: 10
  monsterList {
    monsterId: 802000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 10
  rebirthTime: 75
  rebirthCount: 0
  showLeftTime: 15
  layoutSideId: 1
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 401001
  type: 5
  name: "蓝buff-蓝方"
  groupId: 1012
  sideId: 10
  monsterList {
    monsterId: 803000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 10
  rebirthTime: 75
  rebirthCount: 0
  showLeftTime: 15
  layoutSideId: 0
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 501001
  type: 4
  name: "红buff-蓝方"
  groupId: 1013
  sideId: 10
  monsterList {
    monsterId: 802000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 10
  rebirthTime: 75
  rebirthCount: 0
  showLeftTime: 15
  layoutSideId: 0
  advanceRebirthCount: 0
  animBirthDuration: 1
}
rows {
  id: 601001
  type: 3
  name: "强化、变异空间之灵"
  groupId: 1014
  sideId: 10
  monsterList {
    monsterId: 813000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 150
  rebirthTime: 90
  rebirthCount: 2
  showLeftTime: 15
  layoutSideId: 1
  advanceFirstBirthTime: 300
  advanceRebirthTime: 90
  advanceRebirthCount: 2
  advanceMonsterList {
    monsterId: 814000
    birthIndex: 1
  }
  advancePrepareTime: 10
  animBirthDuration: 6
  billboardShowLeftTime: 40
}
rows {
  id: 701001
  type: 2
  name: "强化、变异红隼"
  groupId: 1015
  sideId: 10
  monsterList {
    monsterId: 810000
    birthIndex: 1
    pathIdx: -1
  }
  firstBirthTime: 150
  rebirthTime: 90
  rebirthCount: 2
  showLeftTime: 15
  layoutSideId: 0
  advanceFirstBirthTime: 300
  advanceRebirthTime: 90
  advanceRebirthCount: 2
  advanceMonsterList {
    monsterId: 811000
    birthIndex: 1
  }
  advancePrepareTime: 10
  animBirthDuration: 6
  billboardShowLeftTime: 40
}
rows {
  id: 801001
  type: 6
  name: "发育路-初级红隼"
  groupId: 1016
  sideId: 10
  monsterList {
    monsterId: 809000
    birthIndex: 0
    pathIdx: 0
  }
  firstBirthTime: 30
  rebirthTime: 40
  rebirthCount: 3
  showLeftTime: 15
  layoutSideId: 1
  disappearTimestamp: 150
  animBirthDuration: 1
  billboardShowLeftTime: 40
}
rows {
  id: 901001
  type: 7
  name: "对抗路-初级空间之灵"
  groupId: 1017
  sideId: 10
  monsterList {
    monsterId: 812000
    birthIndex: 0
    pathIdx: 0
  }
  firstBirthTime: 30
  rebirthTime: 40
  rebirthCount: 3
  showLeftTime: 15
  layoutSideId: 0
  disappearTimestamp: 150
  animBirthDuration: 1
  billboardShowLeftTime: 40
}
