com.tencent.wea.xlsRes.table_CardBagConfigData
excel/xls/K_卡牌.xlsx sheet:卡包配置
rows {
  id: 290001
  baseWeightPool: 101
  drawLimitTime: 1
  guaranteeTimes: 0
  guaranteeWeightPool: 101001
  tradingCardCollectionId: 101
  activityBuff: 0
  tradingCardDeckIdList: 10102
  tradingCardDeckIdList: 10105
  tradingCardDeckIdList: 10107
  tradingCardDeckIdList: 10110
}
rows {
  id: 290002
  baseWeightPool: 102
  drawLimitTime: 2
  guaranteeTimes: 1
  guaranteeParam: 2
  guaranteeWeightPool: 102001
  tradingCardCollectionId: 101
  activityBuff: 0
  tradingCardDeckIdList: 10102
  tradingCardDeckIdList: 10105
  tradingCardDeckIdList: 10107
  tradingCardDeckIdList: 10110
}
rows {
  id: 290003
  baseWeightPool: 103
  drawLimitTime: 3
  guaranteeTimes: 1
  guaranteeParam: 3
  guaranteeWeightPool: 103001
  tradingCardCollectionId: 101
  activityBuff: 0
  tradingCardDeckIdList: 10102
  tradingCardDeckIdList: 10105
  tradingCardDeckIdList: 10107
  tradingCardDeckIdList: 10110
}
rows {
  id: 290004
  baseWeightPool: 104
  drawLimitTime: 4
  guaranteeTimes: 1
  guaranteeParam: 4
  guaranteeWeightPool: 104001
  tradingCardCollectionId: 101
  activityBuff: 0
  tradingCardDeckIdList: 10102
  tradingCardDeckIdList: 10105
  tradingCardDeckIdList: 10107
  tradingCardDeckIdList: 10110
}
rows {
  id: 290005
  baseWeightPool: 105
  drawLimitTime: 5
  guaranteeTimes: 1
  guaranteeParam: 5
  guaranteeWeightPool: 105001
  tradingCardCollectionId: 101
  activityBuff: 0
  tradingCardDeckIdList: 10102
  tradingCardDeckIdList: 10105
  tradingCardDeckIdList: 10107
  tradingCardDeckIdList: 10110
}
rows {
  id: 290011
  baseWeightPool: 201
  drawLimitTime: 1
  guaranteeTimes: 0
  guaranteeWeightPool: 201001
  tradingCardCollectionId: 102
  activityBuff: 20
  tradingCardDeckIdList: 10201
  tradingCardDeckIdList: 10202
  tradingCardDeckIdList: 10203
  tradingCardDeckIdList: 10204
  tradingCardDeckIdList: 10205
  tradingCardDeckIdList: 10206
  tradingCardDeckIdList: 10207
  tradingCardDeckIdList: 10208
  tradingCardDeckIdList: 10209
  tradingCardDeckIdList: 10210
  tradingCardDeckIdList: 10211
  tradingCardDeckIdList: 10212
}
rows {
  id: 290012
  baseWeightPool: 202
  drawLimitTime: 2
  guaranteeTimes: 1
  guaranteeParam: 2
  guaranteeWeightPool: 202001
  tradingCardCollectionId: 102
  activityBuff: 20
  tradingCardDeckIdList: 10201
  tradingCardDeckIdList: 10202
  tradingCardDeckIdList: 10203
  tradingCardDeckIdList: 10204
  tradingCardDeckIdList: 10205
  tradingCardDeckIdList: 10206
  tradingCardDeckIdList: 10207
  tradingCardDeckIdList: 10208
  tradingCardDeckIdList: 10209
  tradingCardDeckIdList: 10210
  tradingCardDeckIdList: 10211
  tradingCardDeckIdList: 10212
}
rows {
  id: 290013
  baseWeightPool: 203
  drawLimitTime: 3
  guaranteeTimes: 2
  guaranteeParam: 3
  guaranteeWeightPool: 203001
  tradingCardCollectionId: 102
  activityBuff: 20
  tradingCardDeckIdList: 10201
  tradingCardDeckIdList: 10202
  tradingCardDeckIdList: 10203
  tradingCardDeckIdList: 10204
  tradingCardDeckIdList: 10205
  tradingCardDeckIdList: 10206
  tradingCardDeckIdList: 10207
  tradingCardDeckIdList: 10208
  tradingCardDeckIdList: 10209
  tradingCardDeckIdList: 10210
  tradingCardDeckIdList: 10211
  tradingCardDeckIdList: 10212
}
rows {
  id: 290014
  baseWeightPool: 204
  drawLimitTime: 4
  guaranteeTimes: 2
  guaranteeParam: 4
  guaranteeWeightPool: 204001
  tradingCardCollectionId: 102
  activityBuff: 20
  tradingCardDeckIdList: 10201
  tradingCardDeckIdList: 10202
  tradingCardDeckIdList: 10203
  tradingCardDeckIdList: 10204
  tradingCardDeckIdList: 10205
  tradingCardDeckIdList: 10206
  tradingCardDeckIdList: 10207
  tradingCardDeckIdList: 10208
  tradingCardDeckIdList: 10209
  tradingCardDeckIdList: 10210
  tradingCardDeckIdList: 10211
  tradingCardDeckIdList: 10212
}
rows {
  id: 290015
  baseWeightPool: 205
  drawLimitTime: 5
  guaranteeTimes: 2
  guaranteeParam: 5
  guaranteeWeightPool: 205001
  tradingCardCollectionId: 102
  activityBuff: 20
  tradingCardDeckIdList: 10201
  tradingCardDeckIdList: 10202
  tradingCardDeckIdList: 10203
  tradingCardDeckIdList: 10204
  tradingCardDeckIdList: 10205
  tradingCardDeckIdList: 10206
  tradingCardDeckIdList: 10207
  tradingCardDeckIdList: 10208
  tradingCardDeckIdList: 10209
  tradingCardDeckIdList: 10210
  tradingCardDeckIdList: 10211
  tradingCardDeckIdList: 10212
}
rows {
  id: 290021
  baseWeightPool: 301
  drawLimitTime: 1
  guaranteeTimes: 0
  guaranteeWeightPool: 301001
  tradingCardCollectionId: 103
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10301
  tradingCardDeckIdList: 10302
  tradingCardDeckIdList: 10303
  tradingCardDeckIdList: 10304
  tradingCardDeckIdList: 10305
  tradingCardDeckIdList: 10306
  tradingCardDeckIdList: 10307
  tradingCardDeckIdList: 10308
  tradingCardDeckIdList: 10309
  tradingCardDeckIdList: 10310
  tradingCardDeckIdList: 10311
  tradingCardDeckIdList: 10312
}
rows {
  id: 290022
  baseWeightPool: 302
  drawLimitTime: 2
  guaranteeTimes: 1
  guaranteeParam: 2
  guaranteeWeightPool: 302001
  tradingCardCollectionId: 103
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10301
  tradingCardDeckIdList: 10302
  tradingCardDeckIdList: 10303
  tradingCardDeckIdList: 10304
  tradingCardDeckIdList: 10305
  tradingCardDeckIdList: 10306
  tradingCardDeckIdList: 10307
  tradingCardDeckIdList: 10308
  tradingCardDeckIdList: 10309
  tradingCardDeckIdList: 10310
  tradingCardDeckIdList: 10311
  tradingCardDeckIdList: 10312
}
rows {
  id: 290023
  baseWeightPool: 303
  drawLimitTime: 3
  guaranteeTimes: 1
  guaranteeParam: 3
  guaranteeWeightPool: 303001
  tradingCardCollectionId: 103
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10301
  tradingCardDeckIdList: 10302
  tradingCardDeckIdList: 10303
  tradingCardDeckIdList: 10304
  tradingCardDeckIdList: 10305
  tradingCardDeckIdList: 10306
  tradingCardDeckIdList: 10307
  tradingCardDeckIdList: 10308
  tradingCardDeckIdList: 10309
  tradingCardDeckIdList: 10310
  tradingCardDeckIdList: 10311
  tradingCardDeckIdList: 10312
}
rows {
  id: 290024
  baseWeightPool: 304
  drawLimitTime: 4
  guaranteeTimes: 1
  guaranteeParam: 4
  guaranteeWeightPool: 304001
  tradingCardCollectionId: 103
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10301
  tradingCardDeckIdList: 10302
  tradingCardDeckIdList: 10303
  tradingCardDeckIdList: 10304
  tradingCardDeckIdList: 10305
  tradingCardDeckIdList: 10306
  tradingCardDeckIdList: 10307
  tradingCardDeckIdList: 10308
  tradingCardDeckIdList: 10309
  tradingCardDeckIdList: 10310
  tradingCardDeckIdList: 10311
  tradingCardDeckIdList: 10312
}
rows {
  id: 290025
  baseWeightPool: 305
  drawLimitTime: 5
  guaranteeTimes: 1
  guaranteeParam: 5
  guaranteeWeightPool: 305001
  tradingCardCollectionId: 103
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10301
  tradingCardDeckIdList: 10302
  tradingCardDeckIdList: 10303
  tradingCardDeckIdList: 10304
  tradingCardDeckIdList: 10305
  tradingCardDeckIdList: 10306
  tradingCardDeckIdList: 10307
  tradingCardDeckIdList: 10308
  tradingCardDeckIdList: 10309
  tradingCardDeckIdList: 10310
  tradingCardDeckIdList: 10311
  tradingCardDeckIdList: 10312
}
rows {
  id: 290031
  baseWeightPool: 401
  drawLimitTime: 1
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 401001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10401
  tradingCardDeckIdList: 10402
  tradingCardDeckIdList: 10403
}
rows {
  id: 290032
  baseWeightPool: 402
  drawLimitTime: 2
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 402001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10401
  tradingCardDeckIdList: 10402
  tradingCardDeckIdList: 10403
}
rows {
  id: 290033
  baseWeightPool: 403
  drawLimitTime: 3
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 403001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10401
  tradingCardDeckIdList: 10402
  tradingCardDeckIdList: 10403
}
rows {
  id: 290034
  baseWeightPool: 404
  drawLimitTime: 4
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 404001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10401
  tradingCardDeckIdList: 10402
  tradingCardDeckIdList: 10403
}
rows {
  id: 290035
  baseWeightPool: 405
  drawLimitTime: 5
  guaranteeTimes: 1
  guaranteeParam: 5
  guaranteeWeightPool: 405001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10401
  tradingCardDeckIdList: 10402
  tradingCardDeckIdList: 10403
}
rows {
  id: 290041
  baseWeightPool: 501
  drawLimitTime: 1
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 501001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10404
  tradingCardDeckIdList: 10405
  tradingCardDeckIdList: 10406
  tradingCardDeckIdList: 10407
  tradingCardDeckIdList: 10408
}
rows {
  id: 290042
  baseWeightPool: 502
  drawLimitTime: 2
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 502001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10404
  tradingCardDeckIdList: 10405
  tradingCardDeckIdList: 10406
  tradingCardDeckIdList: 10407
  tradingCardDeckIdList: 10408
}
rows {
  id: 290043
  baseWeightPool: 503
  drawLimitTime: 3
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 503001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10404
  tradingCardDeckIdList: 10405
  tradingCardDeckIdList: 10406
  tradingCardDeckIdList: 10407
  tradingCardDeckIdList: 10408
}
rows {
  id: 290044
  baseWeightPool: 504
  drawLimitTime: 4
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 504001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10404
  tradingCardDeckIdList: 10405
  tradingCardDeckIdList: 10406
  tradingCardDeckIdList: 10407
  tradingCardDeckIdList: 10408
}
rows {
  id: 290045
  baseWeightPool: 505
  drawLimitTime: 5
  guaranteeTimes: 1
  guaranteeParam: 5
  guaranteeWeightPool: 505001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10404
  tradingCardDeckIdList: 10405
  tradingCardDeckIdList: 10406
  tradingCardDeckIdList: 10407
  tradingCardDeckIdList: 10408
}
rows {
  id: 290051
  baseWeightPool: 601
  drawLimitTime: 1
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 601001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10409
  tradingCardDeckIdList: 10410
}
rows {
  id: 290052
  baseWeightPool: 602
  drawLimitTime: 2
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 602001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10409
  tradingCardDeckIdList: 10410
}
rows {
  id: 290053
  baseWeightPool: 603
  drawLimitTime: 3
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 603001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10409
  tradingCardDeckIdList: 10410
}
rows {
  id: 290054
  baseWeightPool: 604
  drawLimitTime: 4
  guaranteeTimes: 0
  guaranteeParam: 4
  guaranteeWeightPool: 604001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10409
  tradingCardDeckIdList: 10410
}
rows {
  id: 290055
  baseWeightPool: 605
  drawLimitTime: 5
  guaranteeTimes: 1
  guaranteeParam: 5
  guaranteeWeightPool: 605001
  tradingCardCollectionId: 104
  activityBuff: 50
  guaranteeBaseSolo: 1
  tradingCardDeckIdList: 10409
  tradingCardDeckIdList: 10410
}
