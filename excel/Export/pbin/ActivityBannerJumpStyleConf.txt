com.tencent.wea.xlsRes.table_ActivityBannerJumpStyleConf
excel/xls/W_玩法入口活动跳转-banner样式.xlsx sheet:样式
rows {
  id: 1
  jumpOpenTime {
    seconds: 1721318400
  }
  jumpCloseTime {
    seconds: 1724947199
  }
  jumpId: 340
  jumpTitle: "峡谷3v3畅爽激斗中！"
  jumpText: "完成对局，免费领非凡皮肤绛天战甲！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/07/15/paFEn0f8.astc"
  redDotType: 30
}
rows {
  id: 2
  jumpOpenTime {
    seconds: 1720713600
  }
  jumpCloseTime {
    seconds: 1722441600
  }
  jumpId: 4
  jumpTitle: "峡谷3v3模式上线！"
  jumpText: "英雄集结，完成比赛得永久英雄！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/07/15/9KGkgGPa.astc"
  redDotType: 30
}
rows {
  id: 3
  jumpOpenTime {
    seconds: 1723176000
  }
  jumpCloseTime {
    seconds: 1724947199
  }
  jumpId: 810
  jumpTitle: "峡谷排位赛"
  jumpText: "参与峡谷排位赛，免费领绛天之翼！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/08/06/7429M8VR.astc"
  redDotType: 30
}
rows {
  id: 4
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 1729180799
  }
  jumpId: 10666
  jumpTitle: "排位送李白"
  jumpText: "达到最强峡谷星，领取联动时装李白！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/08/21/zKwOKbYn.astc"
  redDotType: 30
}
rows {
  id: 5
  jumpOpenTime {
    seconds: 1725552000
  }
  jumpCloseTime {
    seconds: 1726156799
  }
  jumpId: 10669
  jumpTitle: "英雄周试练"
  jumpText: "来峡谷试练英雄，领取幻梦币！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/07/15/9KGkgGPa.astc"
  redDotType: 30
}
rows {
  id: 6
  jumpOpenTime {
    seconds: 1726329600
  }
  jumpCloseTime {
    seconds: 1726588799
  }
  jumpId: 10671
  jumpTitle: "开黑不掉分"
  jumpText: "中秋开黑不掉分！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/05/PVTpXOTb.png"
  redDotType: 30
}
rows {
  id: 7
  jumpOpenTime {
    seconds: 1726588800
  }
  jumpCloseTime {
    seconds: 1727366399
  }
  jumpId: 10671
  jumpTitle: "团战领好礼"
  jumpText: "免费领紫色背饰！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/05/PVTpXOTb.png"
  redDotType: 30
}
rows {
  id: 8
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 1729180799
  }
  jumpId: 10678
  jumpTitle: "七日首胜礼"
  jumpText: "领取李白手持武器：青莲剑！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/07/15/9KGkgGPa.astc"
  redDotType: 30
}
rows {
  id: 9
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 1727711999
  }
  jumpId: 10679
  jumpTitle: "上分预热"
  jumpText: "每日对局领大量峡谷币！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/18/QScj9H4t.png"
  redDotType: 30
}
rows {
  id: 10
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 1728316799
  }
  jumpId: 10680
  jumpTitle: "来峡谷开黑"
  jumpText: "开黑排位积分翻倍！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/18/QScj9H4t.png"
  redDotType: 30
}
rows {
  id: 101
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 1729180799
  }
  jumpId: 50103
  jumpTitle: "精灵之森赛季祈愿！"
  jumpText: "瑶和孙策新皮肤上线！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/02/nSymYs6f.png"
  jumpParams: "3100101"
}
rows {
  id: 102
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 1729180799
  }
  jumpId: 10666
  jumpTitle: "排位送李白"
  jumpText: "领取联动时装李白！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/02/7mHfhJa0.png"
  redDotType: 30
}
rows {
  id: 103
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 4884854399
  }
  jumpId: 10666
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/07/15/9KGkgGPa.astc"
}
rows {
  id: 104
  jumpOpenTime {
    seconds: 1724947200
  }
  jumpCloseTime {
    seconds: 4884854399
  }
  jumpId: 50102
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_104.astc"
}
rows {
  id: 105
  jumpOpenTime {
    seconds: 1726156800
  }
  jumpCloseTime {
    seconds: 1732809599
  }
  jumpId: 50103
  jumpTitle: "桂月清平"
  jumpText: "李白新皮肤上线！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/09/0AbI4sov.png"
  jumpParams: "3100103"
}
rows {
  id: 106
  jumpOpenTime {
    seconds: 1726848000
  }
  jumpCloseTime {
    seconds: 1728143999
  }
  jumpId: 50103
  jumpTitle: "峡谷幻梦"
  jumpText: "峡谷幻梦上线新皮肤！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/12/Hl8pNzIk.png"
  jumpParams: "3100104"
}
rows {
  id: 107
  jumpOpenTime {
    seconds: 1727712000
  }
  jumpCloseTime {
    seconds: 1730390399
  }
  jumpId: 50103
  jumpTitle: "千都三彩"
  jumpText: "赵云新皮肤上线！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/23/sSAUjUEL.png"
  jumpParams: "3100106"
}
rows {
  id: 108
  jumpOpenTime {
    seconds: 1727366400
  }
  jumpCloseTime {
    seconds: 1729180799
  }
  jumpId: 10686
  jumpTitle: "峡谷大事件"
  jumpText: "峡谷上新不停歇！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/2024/09/24/O9mPIdym.png"
}
rows {
  id: 109
  jumpOpenTime {
    seconds: 1729180800
  }
  jumpCloseTime {
    seconds: 1732809599
  }
  jumpId: 50103
  jumpTitle: "星语星愿赛季祈愿"
  jumpText: "鲁班新皮肤上线！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_109.astc"
  jumpParams: "3100107"
}
rows {
  id: 110
  jumpOpenTime {
    seconds: 1729180800
  }
  jumpCloseTime {
    seconds: 1732809599
  }
  jumpId: 10666
  jumpTitle: "峡谷排位赛"
  jumpText: "上分领取妲己"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_110.astc"
  redDotType: 30
}
rows {
  id: 111
  jumpOpenTime {
    seconds: 1732809600
  }
  jumpCloseTime {
    seconds: 1733414399
  }
  jumpId: 10667
  jumpTitle: "峡谷新体验"
  jumpText: "全新卡牌羁绊"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_111.astc"
  redDotType: 30
}
rows {
  id: 112
  jumpOpenTime {
    seconds: 1732809600
  }
  jumpCloseTime {
    seconds: 1737043199
  }
  jumpId: 10666
  jumpTitle: "峡谷排位赛"
  jumpText: "上分领取马可"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_112.astc"
  redDotType: 30
}
rows {
  id: 113
  jumpOpenTime {
    seconds: 1732809600
  }
  jumpCloseTime {
    seconds: 1735228799
  }
  jumpId: 10678
  jumpTitle: "峡谷5v5"
  jumpText: "送全新蓝装"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_113.astc"
  redDotType: 30
}
rows {
  id: 114
  jumpOpenTime {
    seconds: 1735228800
  }
  jumpCloseTime {
    seconds: 1736697599
  }
  jumpId: 10670
  jumpTitle: "周年送英雄"
  jumpText: "周年送虞姬"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_114.astc"
  redDotType: 30
}
rows {
  id: 115
  jumpOpenTime {
    seconds: 1735228800
  }
  jumpCloseTime {
    seconds: 1735833599
  }
  jumpId: 10671
  jumpTitle: "5v5排位"
  jumpText: "送表情送宝箱"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_115.astc"
  redDotType: 30
}
rows {
  id: 116
  jumpOpenTime {
    seconds: 1737043200
  }
  jumpCloseTime {
    seconds: 1741881599
  }
  jumpId: 10671
  jumpTitle: "峡谷排位赛"
  jumpText: "上分领取亚瑟"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_116.astc"
  redDotType: 30
}
rows {
  id: 117
  jumpOpenTime {
    seconds: 1741190400
  }
  jumpCloseTime {
    seconds: 1746115199
  }
  jumpId: 10671
  jumpTitle: "峡谷排位赛"
  jumpText: "上分领取程咬金"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_117.astc"
  redDotType: 30
}
rows {
  id: 401
  jumpOpenTime {
    seconds: 1729872000
  }
  jumpCloseTime {
    seconds: 1735660799
  }
  jumpId: 50103
  jumpTitle: "星光剧场"
  jumpText: "小乔新皮肤上线"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_401.astc"
  jumpParams: "3100301"
}
rows {
  id: 402
  jumpOpenTime {
    seconds: 1730995200
  }
  jumpCloseTime {
    seconds: 1735660799
  }
  jumpId: 50103
  jumpTitle: "永恒之舞"
  jumpText: "马可波罗新皮肤上线"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_402.astc"
  jumpParams: "3100108"
}
rows {
  id: 403
  jumpOpenTime {
    seconds: 1732809600
  }
  jumpCloseTime {
    seconds: 1737043199
  }
  jumpId: 50103
  jumpTitle: "赛季祈愿"
  jumpText: "貂蝉新皮肤上线"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_403.astc"
  jumpParams: "3100109"
}
rows {
  id: 404
  jumpOpenTime {
    seconds: 1737043200
  }
  jumpCloseTime {
    seconds: 1741881599
  }
  jumpId: 50103
  jumpTitle: "赛季祈愿"
  jumpText: "杨玉环新皮肤上线"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_404.astc"
  jumpParams: "3100110"
}
rows {
  id: 501
  jumpOpenTime {
    seconds: 1740585600
  }
  jumpCloseTime {
    seconds: 1742486399
  }
  jumpId: 581
  jumpTitle: "新皮肤系统"
  jumpText: "送拉弥娅全新皮肤"
  bannerUrl: "https://image-client.ymzx.qq.com/cdn_img/CN/Mobile/High/T_Chase_ActivityBanner_01.astc"
  redDotType: 30
}
rows {
  id: 120
  jumpOpenTime {
    seconds: 1748534400
  }
  jumpCloseTime {
    seconds: 4902998399
  }
  jumpId: 1066
  jumpTitle: "商城"
  jumpText: "亚瑟超值福利！"
  bannerUrl: "https://image-manage.ymzx.qq.com/wuji/client/materials/moba_116.astc"
  redDotType: 30
}
rows {
  id: 118
  jumpOpenTime {
    seconds: 1746115200
  }
  jumpCloseTime {
    seconds: 1750953599
  }
  jumpId: 10680
  jumpTitle: "峡谷排位赛"
  jumpText: "领晴霜时装"
  bannerUrl: "https://image-client.ymzx.qq.com/cdn_img/CN/Mobile/High/CT_Arena_Banner_118.astc"
  redDotType: 30
}
