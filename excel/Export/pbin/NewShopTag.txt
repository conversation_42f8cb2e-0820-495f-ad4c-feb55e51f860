com.tencent.wea.xlsRes.table_NewShopTag
excel/xls/S_商城_商品.xlsx sheet:新商城标签（新）
rows {
  id: 110000
  shopType: 16
  name: "推荐"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 110000
  defaultSelect: 1
  subViewName: "UI_NewMall_HotSubView"
  mallFirstTagSort: 1
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 120000
  shopType: 0
  name: "福利"
  firstTag: 120000
  defaultSelect: 0
  mallFirstTagSort: 2
  bIsHideByMiniGame: true
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 120100
  shopType: 0
  name: "充值送脚印"
  firstTag: 120000
  secondTag: 14
  defaultSelect: 0
  subViewName: "UI_Recharge_ShuttleView"
  openFun: "IsShowShuttleView"
  redDotType: 217
  currencyCfg: 1
  activityId: 10065
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 2
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120200
  shopType: 0
  name: "充值返利"
  firstTag: 120000
  secondTag: 9
  defaultSelect: 0
  subViewName: "UI_SecondChargeRebate_Main"
  openFun: "IsShowSecondChargeRebateMain"
  redDotType: 130
  currencyCfg: 1
  activityId: 10069
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 7
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120300
  shopType: 0
  name: "一元购背包"
  firstTag: 120000
  secondTag: 3
  defaultSelect: 0
  subViewName: "UI_LuckBuy_MainView_Penguin"
  openFun: "IsShowLuckBuy"
  getShowHelpRuleId: "GetLuckBuyHelpRuleId"
  redDotType: 79
  currencyCfg: 1
  activityId: 10001
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 13
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120400
  shopType: 0
  name: "新年充值送"
  firstTag: 120000
  secondTag: 4
  defaultSelect: 0
  subViewName: "UI_ChargeRebate_Main"
  openFun: "IsShowChargeRebateMain"
  redDotType: 52
  currencyCfg: 1
  activityId: 10039
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 12
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120500
  shopType: 0
  name: "春季回馈"
  firstTag: 120000
  secondTag: 5
  defaultSelect: 0
  subViewName: "UI_Recharge_OpeningRebate"
  openFun: "IsShowOpeningRebate"
  getShowHelpRuleId: "GetOpeningRebateHelpRuleId"
  redDotType: 101
  currencyCfg: 1
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 11
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120600
  shopType: 0
  name: "噗噗登录礼"
  firstTag: 120000
  secondTag: 6
  defaultSelect: 0
  subViewName: "UI_OMD_BattlePass_Reward"
  openFun: "IsShowUpgradeCheckInManual"
  redDotType: 133
  currencyCfg: 2
  currencyCfg: 3
  activityId: 10008
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 10
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120700
  shopType: 0
  name: "充值送狡狡龙"
  firstTag: 120000
  secondTag: 7
  defaultSelect: 0
  subViewName: "UI_Recharge_BlueOutfit"
  openFun: "IsShowBlueOutfit"
  getShowHelpRuleId: "GetBlueOutfitRuleId"
  currencyCfg: 1
  activityId: 10054
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 9
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120800
  shopType: 0
  name: "星宝会员"
  firstTag: 120000
  secondTag: 8
  defaultSelect: 0
  subViewName: "UI_Recharge_MonthCard"
  openFun: "IsShowMonthCardRecharge"
  getShowHelpRuleId: "GetMonthCardHelpRuleId"
  redDotType: 50
  currencyCfg: 1
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 8
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 120900
  shopType: 0
  name: "幸运转盘"
  firstTag: 120000
  secondTag: 2
  defaultSelect: 0
  subViewName: "UI_Recharge_LuckyTurntable"
  openFun: "IsShowLuckTurntable"
  getShowHelpRuleId: "GetLuckyTurntableHelpRuleId"
  redDotType: 173
  currencyCfg: 1
  currencyCfg: 3
  activityId: 10043
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 14
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121000
  shopType: 0
  name: "周末幸运星"
  firstTag: 120000
  secondTag: 10
  defaultSelect: 1
  subViewName: "UI_WeekendLuckyStar_MainView"
  openFun: "IsShowLuckyStarView"
  getShowHelpRuleId: "GetWeekendLuckyStarHelpRuleId"
  activityId: 10020
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 16
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121100
  shopType: 0
  name: "赛季预购"
  firstTag: 120000
  secondTag: 11
  defaultSelect: 0
  subViewName: "UI_ValuePreorder06_MainView"
  openFun: "IsShowSeasonPreOrder"
  redDotType: 175
  activityId: 10067
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 17
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121200
  shopType: 0
  name: "燎原之心"
  firstTag: 120000
  secondTag: 12
  defaultSelect: 0
  subViewName: "UI_Arena_Mall_HotAvatarBuy"
  openTime {
    seconds: 1727798400
  }
  closeTime {
    seconds: 1729612799
  }
  openFun: "IsShowAvatarBuy"
  redDotType: 236
  currencyCfg: 3541
  currencyCfg: 1
  activityId: 10019
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 4
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121300
  shopType: 0
  name: "和平守望"
  firstTag: 120000
  secondTag: 13
  defaultSelect: 0
  subViewName: "UI_Arena_Mall_HotAvatarBuy"
  openTime {
    seconds: 1729872000
  }
  closeTime {
    seconds: 1731254399
  }
  openFun: "IsShowAvatarBuy"
  redDotType: 236
  currencyCfg: 3541
  currencyCfg: 1
  activityId: 10025
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 3
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121400
  shopType: 0
  name: "消费返还"
  firstTag: 120000
  secondTag: 1
  defaultSelect: 0
  subViewName: "UI_LuckyFree_MainView"
  openFun: "IsShowLuckyFree"
  getShowHelpRuleId: "GetLuckyFreeHelpRuleId"
  redDotType: 176
  currencyCfg: 3
  activityId: 18901
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 15
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121500
  shopType: 0
  name: "满减大福利"
  firstTag: 120000
  secondTag: 15
  defaultSelect: 0
  subViewName: "UI_Recharge_Werewolf_Promotion"
  openTime {
    seconds: 1749139200
  }
  closeTime {
    seconds: 1750607999
  }
  pakGroup: 20003
  openFun: "IsShowWerewolfPromotion"
  getShowHelpRuleId: "GetRechargeActivityRuleId"
  redDotType: 239
  currencyCfg: 1
  activityId: 10066
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 1
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121600
  shopType: 0
  name: "冰雪赐福"
  firstTag: 120000
  secondTag: 16
  defaultSelect: 0
  subViewName: "UI_SnowBless_MainView"
  openTime {
    seconds: 1702483200
  }
  closeTime {
    seconds: 1767196799
  }
  openFun: "IsShowByVersionAndTime"
  getShowHelpRuleId: "GetRechargeActivityRuleId"
  redDotType: 242
  currencyCfg: 1
  activityId: 10028
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 1
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 121700
  shopType: 0
  name: "爆红包测试"
  firstTag: 120000
  secondTag: 17
  defaultSelect: 0
  subViewName: "UI_InflateHongBao_MainView"
  openFun: "IsShowInflateHongBao"
  redDotType: 386
  currencyCfg: 1
  activityId: 10059
  bIsShowBG: true
  bIsHideByMiniGame: true
  secondSeq: 1
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 130000
  shopType: 0
  name: "礼包"
  firstTag: 130000
  defaultSelect: 0
  redDotType: 47
  mallFirstTagSort: 3
  bIsHideByMiniGame: true
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 130100
  shopType: 0
  name: "日常礼包"
  firstTag: 130000
  secondTag: 2
  defaultSelect: 0
  subViewName: "UI_Recharge_CutGift"
  openFun: "IsShowCutGift"
  redDotType: 178
  currencyCfg: 1
  bIsShowBG: true
  bIsHideByMiniGame: true
  giftMallId: 20
  secondSeq: 3
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 130200
  shopType: 0
  name: "限时礼包"
  firstTag: 130000
  secondTag: 1
  defaultSelect: 1
  subViewName: "UI_Recharge_OtherGift"
  openFun: "IsShowCutGift"
  redDotType: 178
  currencyCfg: 1
  bIsShowBG: true
  bIsHideByMiniGame: true
  giftMallId: 157
  secondSeq: 2
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 130300
  shopType: 0
  name: "特色礼包"
  firstTag: 130000
  secondTag: 3
  defaultSelect: 0
  subViewName: "UI_Recharge_OtherGift"
  openFun: "IsShowCutGift"
  redDotType: 178
  currencyCfg: 1
  bIsShowBG: true
  bIsHideByMiniGame: true
  giftMallId: 158
  secondSeq: 1
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 140000
  shopType: 0
  name: "首充"
  firstTag: 140000
  defaultSelect: 0
  subViewName: "UI_Recharge_FirstChargeSubView"
  openFun: "IsShowFirstCharge"
  getShowHelpRuleId: "GetFirstChargeHelpRuleId"
  redDotType: 43
  currencyCfg: 1
  mallFirstTagSort: 4
  bIsHideByMiniGame: true
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 150000
  shopType: 0
  name: "星愿招财猫"
  firstTag: 150000
  defaultSelect: 0
  subViewName: "UI_Recharge_SeasonRecharge"
  openFun: "IsShowSeasonRecharge"
  redDotType: 45
  currencyCfg: 1
  currencyCfg: 2
  mallFirstTagSort: 5
  bIsShowBG: true
  bIsHideByMiniGame: true
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 160000
  shopType: 0
  name: "商城"
  firstTag: 160000
  defaultSelect: 0
  mallFirstTagSort: 6
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160100
  shopType: 0
  name: "装扮"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 160000
  secondTag: 1
  defaultSelect: 1
  subViewName: "UI_NewMall_AvatarSubView"
  secondSeq: 8
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 160101
  shopType: 6
  name: "套装"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_09"
  secondSelectTagIcon: "T_Shop_Icon_Selected_01"
  secondTag: 160100
  thirdTag: 1
  defaultSelect: 1
  itemType: "ItemType_Suit"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160102
  shopType: 7
  name: "上装"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_08"
  secondSelectTagIcon: "T_Shop_Icon_Selected_02"
  secondTag: 160100
  thirdTag: 6
  defaultSelect: 0
  itemType: "ItemType_UpperGarment"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160103
  shopType: 8
  name: "下装"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_07"
  secondSelectTagIcon: "T_Shop_Icon_Selected_03"
  secondTag: 160100
  thirdTag: 7
  defaultSelect: 0
  itemType: "ItemType_LowerGarment"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160104
  shopType: 9
  name: "手套"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_06"
  secondSelectTagIcon: "T_Shop_Icon_Selected_04"
  secondTag: 160100
  thirdTag: 8
  defaultSelect: 0
  itemType: "ItemType_Gloves"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160105
  shopType: 10
  name: "面饰"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_04"
  secondSelectTagIcon: "T_Shop_Icon_Selected_07"
  secondTag: 160100
  thirdTag: 2
  defaultSelect: 0
  itemType: "ItemType_FaceOrnament"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160106
  shopType: 11
  name: "头饰"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_10"
  secondSelectTagIcon: "T_Shop_Icon_Selected_05"
  secondTag: 160100
  thirdTag: 3
  defaultSelect: 0
  itemType: "ItemType_HeadWear"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160107
  shopType: 12
  name: "背饰"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_03"
  secondSelectTagIcon: "T_Shop_Icon_Selected_06"
  secondTag: 160100
  thirdTag: 4
  defaultSelect: 0
  itemType: "ItemType_BackOrnament"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160108
  shopType: 26
  name: "脸部"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_11"
  secondSelectTagIcon: "T_Shop_Icon_Selected_11"
  secondTag: 160100
  thirdTag: 11
  defaultSelect: 0
  itemType: "ItemType_Face"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160109
  shopType: 43
  name: "手持物"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_14"
  secondSelectTagIcon: "T_Shop_Icon_Selected_14"
  secondTag: 160100
  thirdTag: 5
  defaultSelect: 0
  itemType: "ItemType_HandOrnament"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160110
  shopType: 45
  name: "脚印"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Streaking"
  secondSelectTagIcon: "T_Shop_Icon_Streaking_Selected"
  secondTag: 160100
  thirdTag: 9
  defaultSelect: 0
  itemType: "ItemType_FootPrint"
  showConditionGroup {
    condition {
      conditionType: 2
    }
  }
}
rows {
  id: 160111
  shopType: 46
  name: "登场秀"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Broadcast"
  secondSelectTagIcon: "T_Shop_Icon_Broadcast_Selected"
  secondTag: 160100
  thirdTag: 10
  defaultSelect: 0
  itemType: "ItemType_Velarium"
  showConditionGroup {
    condition {
      conditionType: 2
    }
  }
}
rows {
  id: 160200
  shopType: 0
  name: "互动"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 160000
  secondTag: 2
  defaultSelect: 0
  subViewName: "UI_NewMall_InteractionSubView"
  secondSeq: 5
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 160201
  shopType: 13
  name: "表情"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Face_01"
  secondSelectTagIcon: "T_Shop_Icon_Face_01_Selected"
  secondTag: 160200
  thirdTag: 1
  defaultSelect: 1
  itemType: "ItemType_Emoji"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160202
  shopType: 14
  name: "单人动作"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Action"
  secondSelectTagIcon: "T_Shop_Icon_Action_Selected"
  secondTag: 160200
  thirdTag: 2
  defaultSelect: 0
  itemType: "ItemType_Action1P"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160203
  shopType: 35
  name: "双人动作"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_DoubleAction"
  secondSelectTagIcon: "T_Shop_Icon_DoubleAction_Selected"
  secondTag: 160200
  thirdTag: 3
  defaultSelect: 0
  itemType: "ItemType_Action2P"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160204
  shopType: 27
  name: "互动道具"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_12"
  secondSelectTagIcon: "T_Shop_Icon_Selected_12"
  secondTag: 160200
  thirdTag: 4
  defaultSelect: 0
  itemType: "ItemType_InteractiveProp"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160300
  shopType: 15
  name: "道具"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 160000
  secondTag: 3
  defaultSelect: 0
  subViewName: "UI_NewMall_PropSubView"
  itemType: "ItemType_Common"
  secondSeq: 4
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 160400
  shopType: 0
  name: "超值礼包"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 160000
  secondTag: 4
  defaultSelect: 0
  subViewName: "UI_NewMall_AvatarGiftSubView"
  secondSeq: 6
  isShowInGiftMode: 0
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160401
  shopType: 21
  name: "极速飞驰"
  firstTag: 160000
  secondTagIcon: "T_AvatarGiftSubView_Img_Theme02"
  secondSelectTagIcon: "T_Shop_Icon_Action_Selected"
  secondTag: 160400
  thirdTag: 1
  defaultSelect: 0
  subViewName: "UI_Mall_MultiGiftSubView"
  subTitle: "青春不设限勇敢去赢"
  showConditionGroup {
    condition {
      conditionType: 2
    }
  }
}
rows {
  id: 160402
  shopType: 21
  name: "天生歌姬"
  firstTag: 160000
  secondTagIcon: "T_AvatarGiftSubView_Img_Theme01"
  secondSelectTagIcon: "T_Shop_Icon_Action_Selected"
  secondTag: 160400
  thirdTag: 2
  defaultSelect: 0
  subViewName: "UI_Mall_SingleGiftSubView"
  showConditionGroup {
    condition {
      conditionType: 2
    }
  }
}
rows {
  id: 160403
  shopType: 21
  name: "海精灵"
  firstTag: 160000
  secondTagIcon: "T_AvatarGiftSubView_Img_Theme02"
  secondSelectTagIcon: "T_Shop_Icon_Action_Selected"
  secondTag: 160400
  thirdTag: 3
  defaultSelect: 0
  subViewName: "UI_Mall_SingleGiftSubView"
  subTitle: "海精灵测试"
  showConditionGroup {
    condition {
      conditionType: 2
    }
  }
}
rows {
  id: 160404
  shopType: 21
  name: "森林童话"
  firstTag: 160000
  secondTagIcon: "T_AvatarGiftSubView_Img_Theme02"
  secondSelectTagIcon: "T_Shop_Icon_Action_Selected"
  secondTag: 160400
  thirdTag: 4
  defaultSelect: 1
  subViewName: "UI_Mall_ExclusiveGiftSubView"
  subTitle: "开启奇幻森林冒险"
  pakGroup: 1100074
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160405
  shopType: 21
  name: "幻术礼包"
  firstTag: 160000
  secondTagIcon: "T_AvatarGiftSubView_Img_Theme02"
  secondSelectTagIcon: "T_Shop_Icon_Action_Selected"
  secondTag: 160400
  thirdTag: 4
  defaultSelect: 1
  subViewName: "UI_Mall_ExclusiveGiftSubView"
  showConditionGroup {
    condition {
      conditionType: 2
    }
  }
}
rows {
  id: 160500
  shopType: 0
  name: "宝库"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 160000
  secondTag: 5
  defaultSelect: 0
  subViewName: "UI_NewMall_TreasureSubView"
  secondSeq: 6
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
rows {
  id: 160501
  shopType: 130
  name: "农场"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_16"
  secondSelectTagIcon: "T_Shop_Icon_Selected_16"
  secondTag: 160500
  thirdTag: 1
  defaultSelect: 1
  itemType: "ItemType_FarmActive"
  giftMallId: 130
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160502
  shopType: 131
  name: "飞车"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Car"
  secondSelectTagIcon: "T_Shop_Icon_Car_Selected"
  secondTag: 160500
  thirdTag: 2
  defaultSelect: 0
  itemType: "ItemType_Kart"
  pakGroup: 20031
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160503
  shopType: 132
  name: "狼人杀"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_15"
  secondSelectTagIcon: "T_Shop_Icon_Selected_15"
  secondTag: 160500
  thirdTag: 3
  defaultSelect: 0
  itemType: "ItemType_NR3E_Interactive"
  pakGroup: 20005
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160504
  shopType: 133
  name: "moba"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_17"
  secondSelectTagIcon: "T_Shop_Icon_Selected_17"
  secondTag: 160500
  thirdTag: 4
  defaultSelect: 0
  itemType: "ItemType_Arena_HeroTry"
  minVersion: "1.3.26.1"
  pakGroup: 50021
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160505
  shopType: 163
  name: "躲猫猫"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Peekaboo"
  secondSelectTagIcon: "T_Shop_Icon_Peekaboo_Selected"
  secondTag: 160500
  thirdTag: 5
  defaultSelect: 0
  itemType: "ItemType_NR3E_DisguiseEffect"
  minVersion: "1.3.37.1"
  pakGroup: 20001
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160600
  shopType: 134
  name: "星梭"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 160000
  secondTag: 8
  defaultSelect: 0
  subViewName: "UI_NewMall_ShuttleSubView"
  itemType: "ItemType_Shuttle"
  secondSeq: 1
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160700
  shopType: 0
  name: "载具"
  firstTagIcon: "T_Shop_Icon_Dress"
  firstTag: 160000
  secondTag: 6
  defaultSelect: 0
  subViewName: "UI_NewMall_InteractionSubView"
  secondSeq: 7
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160701
  shopType: 39
  name: "载具"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Vehicle"
  secondSelectTagIcon: "T_Shop_Icon_Vehicle_Selected"
  secondTag: 160700
  thirdTag: 1
  defaultSelect: 1
  itemType: "ItemType_Vehicle"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160800
  shopType: 0
  name: "定制"
  firstTag: 160000
  secondTag: 7
  defaultSelect: 0
  subViewName: "UI_NewMall_AvatarSubView"
  secondSeq: 2
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160801
  shopType: 195
  name: "环绕物"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_20"
  secondSelectTagIcon: "T_Shop_Icon_Selected_20"
  secondTag: 160800
  thirdTag: 1
  defaultSelect: 1
  subViewName: "UI_NewMall_AvatarSubView"
  itemType: "ItemType_Surroundings"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160802
  shopType: 45
  name: "脚印"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Streaking"
  secondSelectTagIcon: "T_Shop_Icon_Streaking_Selected"
  secondTag: 160800
  thirdTag: 2
  defaultSelect: 0
  subViewName: "UI_NewMall_AvatarSubView"
  itemType: "ItemType_FootPrint"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 160803
  shopType: 46
  name: "登场秀"
  firstTag: 160000
  secondTagIcon: "T_Shop_Icon_Broadcast"
  secondSelectTagIcon: "T_Shop_Icon_Broadcast_Selected"
  secondTag: 160800
  thirdTag: 3
  defaultSelect: 0
  subViewName: "UI_NewMall_AvatarSubView"
  itemType: "ItemType_Velarium"
  showConditionGroup {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 180000
  shopType: 0
  name: "充值"
  firstTag: 180000
  defaultSelect: 0
  subViewName: "UI_Recharge_Recharge"
  openFun: "IsPermanentShow"
  redDotType: 37
  currencyCfg: 1
  mallFirstTagSort: 10
  bIsShowBG: true
  bIsHideByMiniGame: true
  showConditionGroup {
    condition {
      conditionType: 1
      subConditionList {
        type: 1001
        value: 2
        value: 1502
        value: 0
      }
    }
  }
}
