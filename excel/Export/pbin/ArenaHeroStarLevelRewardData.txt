com.tencent.wea.xlsRes.table_ArenaHeroStarLevelReward
excel/xls/Arena/Z_Arena主目标系统.xlsx sheet:英雄等级奖励
rows {
  rewardID: 1
  heroID: 1001
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 2
  heroID: 1002
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 3
  heroID: 1003
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 4
  heroID: 1004
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 5
  heroID: 1005
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 6
  heroID: 1006
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 7
  heroID: 1007
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 8
  heroID: 1008
}
rows {
  rewardID: 9
  heroID: 1009
}
rows {
  rewardID: 10
  heroID: 1010
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 11
  heroID: 1011
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 12
  heroID: 1012
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 13
  heroID: 1013
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 14
  heroID: 1014
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 15
  heroID: 1015
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 16
  heroID: 1016
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 17
  heroID: 1017
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 18
  heroID: 1018
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 19
  heroID: 1019
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 20
  heroID: 1020
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 21
  heroID: 1021
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 22
  heroID: 1022
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 23
  heroID: 1023
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 24
  heroID: 1024
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 25
  heroID: 1025
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 26
  heroID: 1026
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 27
  heroID: 1027
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 28
  heroID: 1028
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 29
  heroID: 1029
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 30
  heroID: 1030
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 31
  heroID: 1031
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 32
  heroID: 1032
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 33
  heroID: 1033
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 34
  heroID: 1034
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 35
  heroID: 1035
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 36
  heroID: 1036
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 37
  heroID: 1037
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 38
  heroID: 1038
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 39
  heroID: 1039
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 40
  heroID: 1040
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 41
  heroID: 1041
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 42
  heroID: 1042
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 43
  heroID: 1043
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 44
  heroID: 1044
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 45
  heroID: 1045
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 46
  heroID: 1046
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 47
  heroID: 1047
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 48
  heroID: 1048
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 49
  heroID: 1049
  rewardGroup: 1
  rewardType: AHSRT_Item
  itemId: 3541
  itemNum: 20
}
rows {
  rewardID: 50
  heroID: 1001
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 51
  heroID: 1002
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 52
  heroID: 1003
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 53
  heroID: 1004
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 54
  heroID: 1005
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 55
  heroID: 1006
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 56
  heroID: 1007
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 57
  heroID: 1008
}
rows {
  rewardID: 58
  heroID: 1009
}
rows {
  rewardID: 59
  heroID: 1010
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 60
  heroID: 1011
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 61
  heroID: 1012
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 62
  heroID: 1013
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 63
  heroID: 1014
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 64
  heroID: 1015
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 65
  heroID: 1016
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 66
  heroID: 1017
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 67
  heroID: 1018
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 68
  heroID: 1019
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 69
  heroID: 1020
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 70
  heroID: 1021
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 71
  heroID: 1022
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 72
  heroID: 1023
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 73
  heroID: 1024
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 74
  heroID: 1025
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 75
  heroID: 1026
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 76
  heroID: 1027
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 77
  heroID: 1028
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 78
  heroID: 1029
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 79
  heroID: 1030
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 80
  heroID: 1031
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 81
  heroID: 1032
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 82
  heroID: 1033
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 83
  heroID: 1034
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 84
  heroID: 1035
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 85
  heroID: 1036
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 86
  heroID: 1037
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 87
  heroID: 1038
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 88
  heroID: 1039
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 89
  heroID: 1040
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 90
  heroID: 1041
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 91
  heroID: 1042
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 92
  heroID: 1043
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 93
  heroID: 1044
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 94
  heroID: 1045
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 95
  heroID: 1046
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 96
  heroID: 1047
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 97
  heroID: 1048
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 98
  heroID: 1049
  rewardGroup: 2
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 99
  heroID: 1001
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303415
  itemNum: 1
}
rows {
  rewardID: 100
  heroID: 1002
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303416
  itemNum: 1
}
rows {
  rewardID: 101
  heroID: 1003
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303417
  itemNum: 1
}
rows {
  rewardID: 102
  heroID: 1004
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303418
  itemNum: 1
}
rows {
  rewardID: 103
  heroID: 1005
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303419
  itemNum: 1
}
rows {
  rewardID: 104
  heroID: 1006
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303420
  itemNum: 1
}
rows {
  rewardID: 105
  heroID: 1007
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303421
  itemNum: 1
}
rows {
  rewardID: 106
  heroID: 1008
}
rows {
  rewardID: 107
  heroID: 1009
}
rows {
  rewardID: 108
  heroID: 1010
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303425
  itemNum: 1
}
rows {
  rewardID: 109
  heroID: 1011
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303422
  itemNum: 1
}
rows {
  rewardID: 110
  heroID: 1012
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303423
  itemNum: 1
}
rows {
  rewardID: 111
  heroID: 1013
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303424
  itemNum: 1
}
rows {
  rewardID: 112
  heroID: 1014
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303426
  itemNum: 1
}
rows {
  rewardID: 113
  heroID: 1015
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303428
  itemNum: 1
}
rows {
  rewardID: 114
  heroID: 1016
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303433
  itemNum: 1
}
rows {
  rewardID: 115
  heroID: 1017
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303429
  itemNum: 1
}
rows {
  rewardID: 116
  heroID: 1018
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303431
  itemNum: 1
}
rows {
  rewardID: 117
  heroID: 1019
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303427
  itemNum: 1
}
rows {
  rewardID: 118
  heroID: 1020
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303432
  itemNum: 1
}
rows {
  rewardID: 119
  heroID: 1021
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303430
  itemNum: 1
}
rows {
  rewardID: 120
  heroID: 1022
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303434
  itemNum: 1
}
rows {
  rewardID: 121
  heroID: 1023
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303435
  itemNum: 1
}
rows {
  rewardID: 122
  heroID: 1024
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303436
  itemNum: 1
}
rows {
  rewardID: 123
  heroID: 1025
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303437
  itemNum: 1
}
rows {
  rewardID: 124
  heroID: 1026
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303438
  itemNum: 1
}
rows {
  rewardID: 125
  heroID: 1027
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303439
  itemNum: 1
}
rows {
  rewardID: 126
  heroID: 1028
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303441
  itemNum: 1
}
rows {
  rewardID: 127
  heroID: 1029
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303442
  itemNum: 1
}
rows {
  rewardID: 128
  heroID: 1030
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303453
  itemNum: 1
}
rows {
  rewardID: 129
  heroID: 1031
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303446
  itemNum: 1
}
rows {
  rewardID: 130
  heroID: 1032
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303445
  itemNum: 1
}
rows {
  rewardID: 131
  heroID: 1033
  rewardGroup: 3
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
  forbidReward: true
}
rows {
  rewardID: 132
  heroID: 1034
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303440
  itemNum: 1
}
rows {
  rewardID: 133
  heroID: 1035
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303444
  itemNum: 1
}
rows {
  rewardID: 134
  heroID: 1036
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303447
  itemNum: 1
}
rows {
  rewardID: 135
  heroID: 1037
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303450
  itemNum: 1
}
rows {
  rewardID: 136
  heroID: 1038
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303451
  itemNum: 1
}
rows {
  rewardID: 137
  heroID: 1039
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303452
  itemNum: 1
}
rows {
  rewardID: 138
  heroID: 1040
  rewardGroup: 3
  rewardType: AHSRT_Item
  itemId: 303504
  itemNum: 1
}
rows {
  rewardID: 139
  heroID: 1041
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303443
  itemNum: 1
}
rows {
  rewardID: 140
  heroID: 1042
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303449
  itemNum: 1
}
rows {
  rewardID: 141
  heroID: 1043
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303455
  itemNum: 1
}
rows {
  rewardID: 142
  heroID: 1044
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303498
  itemNum: 1
}
rows {
  rewardID: 143
  heroID: 1045
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303448
  itemNum: 1
}
rows {
  rewardID: 144
  heroID: 1046
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303503
  itemNum: 1
}
rows {
  rewardID: 145
  heroID: 1047
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303506
  itemNum: 1
}
rows {
  rewardID: 146
  heroID: 1048
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303511
  itemNum: 1
}
rows {
  rewardID: 147
  heroID: 1049
  rewardGroup: 3
  rewardType: AHSRT_Card_Blue
  itemId: 303508
  itemNum: 1
}
rows {
  rewardID: 148
  heroID: 1001
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 149
  heroID: 1002
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 150
  heroID: 1003
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 151
  heroID: 1004
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 152
  heroID: 1005
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 153
  heroID: 1006
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 154
  heroID: 1007
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 155
  heroID: 1008
}
rows {
  rewardID: 156
  heroID: 1009
}
rows {
  rewardID: 157
  heroID: 1010
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 158
  heroID: 1011
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 159
  heroID: 1012
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 160
  heroID: 1013
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 161
  heroID: 1014
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 162
  heroID: 1015
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 163
  heroID: 1016
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 164
  heroID: 1017
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 165
  heroID: 1018
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 166
  heroID: 1019
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 167
  heroID: 1020
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 168
  heroID: 1021
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 169
  heroID: 1022
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 170
  heroID: 1023
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 171
  heroID: 1024
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 172
  heroID: 1025
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 173
  heroID: 1026
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 174
  heroID: 1027
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 175
  heroID: 1028
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 176
  heroID: 1029
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 177
  heroID: 1030
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 178
  heroID: 1031
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 179
  heroID: 1032
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 180
  heroID: 1033
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 181
  heroID: 1034
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 182
  heroID: 1035
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 183
  heroID: 1036
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 184
  heroID: 1037
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 185
  heroID: 1038
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 186
  heroID: 1039
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 187
  heroID: 1040
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 188
  heroID: 1041
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 189
  heroID: 1042
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 190
  heroID: 1043
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 191
  heroID: 1044
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 192
  heroID: 1045
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 193
  heroID: 1046
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 194
  heroID: 1047
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 195
  heroID: 1048
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 196
  heroID: 1049
  rewardGroup: 4
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 197
  heroID: 1001
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 198
  heroID: 1002
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 199
  heroID: 1003
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 200
  heroID: 1004
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 201
  heroID: 1005
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 202
  heroID: 1006
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 203
  heroID: 1007
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 204
  heroID: 1008
}
rows {
  rewardID: 205
  heroID: 1009
}
rows {
  rewardID: 206
  heroID: 1010
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 207
  heroID: 1011
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 208
  heroID: 1012
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 209
  heroID: 1013
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 210
  heroID: 1014
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 211
  heroID: 1015
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 212
  heroID: 1016
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 213
  heroID: 1017
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 214
  heroID: 1018
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 215
  heroID: 1019
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 216
  heroID: 1020
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 217
  heroID: 1021
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 218
  heroID: 1022
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 219
  heroID: 1023
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 220
  heroID: 1024
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 221
  heroID: 1025
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 222
  heroID: 1026
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 223
  heroID: 1027
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 224
  heroID: 1028
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 225
  heroID: 1029
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 226
  heroID: 1030
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 227
  heroID: 1031
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 228
  heroID: 1032
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 229
  heroID: 1033
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 230
  heroID: 1034
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 231
  heroID: 1035
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 232
  heroID: 1036
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 233
  heroID: 1037
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 234
  heroID: 1038
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 235
  heroID: 1039
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 236
  heroID: 1040
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 237
  heroID: 1041
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 238
  heroID: 1042
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 239
  heroID: 1043
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 240
  heroID: 1044
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 241
  heroID: 1045
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 242
  heroID: 1046
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 243
  heroID: 1047
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 244
  heroID: 1048
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 245
  heroID: 1049
  rewardGroup: 5
  rewardType: AHSRT_Piece
  itemId: 304001
  itemNum: 10
}
rows {
  rewardID: 246
  heroID: 1001
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 247
  heroID: 1002
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 248
  heroID: 1003
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 249
  heroID: 1004
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 250
  heroID: 1005
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 251
  heroID: 1006
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 252
  heroID: 1007
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 253
  heroID: 1008
}
rows {
  rewardID: 254
  heroID: 1009
}
rows {
  rewardID: 255
  heroID: 1010
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 256
  heroID: 1011
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 257
  heroID: 1012
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 258
  heroID: 1013
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 259
  heroID: 1014
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 260
  heroID: 1015
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 261
  heroID: 1016
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 262
  heroID: 1017
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 263
  heroID: 1018
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 264
  heroID: 1019
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 265
  heroID: 1020
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 266
  heroID: 1021
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 267
  heroID: 1022
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 268
  heroID: 1023
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 269
  heroID: 1024
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 270
  heroID: 1025
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 271
  heroID: 1026
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 272
  heroID: 1027
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 273
  heroID: 1028
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 274
  heroID: 1029
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 275
  heroID: 1030
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 276
  heroID: 1031
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 277
  heroID: 1032
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 278
  heroID: 1033
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 279
  heroID: 1034
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 280
  heroID: 1035
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 281
  heroID: 1036
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 282
  heroID: 1037
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 283
  heroID: 1038
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 284
  heroID: 1039
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 285
  heroID: 1040
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 286
  heroID: 1041
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 287
  heroID: 1042
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 288
  heroID: 1043
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 289
  heroID: 1044
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 290
  heroID: 1045
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 291
  heroID: 1046
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 292
  heroID: 1047
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 293
  heroID: 1048
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 294
  heroID: 1049
  rewardGroup: 6
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
}
rows {
  rewardID: 295
  heroID: 1001
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303459
  itemNum: 1
}
rows {
  rewardID: 296
  heroID: 1002
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303460
  itemNum: 1
}
rows {
  rewardID: 297
  heroID: 1003
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303461
  itemNum: 1
}
rows {
  rewardID: 298
  heroID: 1004
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303462
  itemNum: 1
}
rows {
  rewardID: 299
  heroID: 1005
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303463
  itemNum: 1
}
rows {
  rewardID: 300
  heroID: 1006
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303464
  itemNum: 1
}
rows {
  rewardID: 301
  heroID: 1007
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303465
  itemNum: 1
}
rows {
  rewardID: 302
  heroID: 1008
}
rows {
  rewardID: 303
  heroID: 1009
}
rows {
  rewardID: 304
  heroID: 1010
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303469
  itemNum: 1
}
rows {
  rewardID: 305
  heroID: 1011
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303466
  itemNum: 1
}
rows {
  rewardID: 306
  heroID: 1012
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303467
  itemNum: 1
}
rows {
  rewardID: 307
  heroID: 1013
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303468
  itemNum: 1
}
rows {
  rewardID: 308
  heroID: 1014
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303470
  itemNum: 1
}
rows {
  rewardID: 309
  heroID: 1015
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303472
  itemNum: 1
}
rows {
  rewardID: 310
  heroID: 1016
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303477
  itemNum: 1
}
rows {
  rewardID: 311
  heroID: 1017
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303473
  itemNum: 1
}
rows {
  rewardID: 312
  heroID: 1018
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303475
  itemNum: 1
}
rows {
  rewardID: 313
  heroID: 1019
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303471
  itemNum: 1
}
rows {
  rewardID: 314
  heroID: 1020
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303476
  itemNum: 1
}
rows {
  rewardID: 315
  heroID: 1021
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303474
  itemNum: 1
}
rows {
  rewardID: 316
  heroID: 1022
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303478
  itemNum: 1
}
rows {
  rewardID: 317
  heroID: 1023
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303479
  itemNum: 1
}
rows {
  rewardID: 318
  heroID: 1024
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303480
  itemNum: 1
}
rows {
  rewardID: 319
  heroID: 1025
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303481
  itemNum: 1
}
rows {
  rewardID: 320
  heroID: 1026
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303482
  itemNum: 1
}
rows {
  rewardID: 321
  heroID: 1027
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303483
  itemNum: 1
}
rows {
  rewardID: 322
  heroID: 1028
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303485
  itemNum: 1
}
rows {
  rewardID: 323
  heroID: 1029
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303486
  itemNum: 1
}
rows {
  rewardID: 324
  heroID: 1030
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303499
  itemNum: 1
}
rows {
  rewardID: 325
  heroID: 1031
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303490
  itemNum: 1
}
rows {
  rewardID: 326
  heroID: 1032
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303489
  itemNum: 1
}
rows {
  rewardID: 327
  heroID: 1033
  rewardGroup: 7
  rewardType: AHSRT_Item
  itemId: 305003
  itemNum: 1
  forbidReward: true
}
rows {
  rewardID: 328
  heroID: 1034
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303484
  itemNum: 1
}
rows {
  rewardID: 329
  heroID: 1035
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303488
  itemNum: 1
}
rows {
  rewardID: 330
  heroID: 1036
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303491
  itemNum: 1
}
rows {
  rewardID: 331
  heroID: 1037
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303494
  itemNum: 1
}
rows {
  rewardID: 332
  heroID: 1038
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303495
  itemNum: 1
}
rows {
  rewardID: 333
  heroID: 1039
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303496
  itemNum: 1
}
rows {
  rewardID: 334
  heroID: 1040
  rewardGroup: 7
  rewardType: AHSRT_Item
  itemId: 303505
  itemNum: 1
}
rows {
  rewardID: 335
  heroID: 1041
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303487
  itemNum: 1
}
rows {
  rewardID: 336
  heroID: 1042
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303493
  itemNum: 1
}
rows {
  rewardID: 337
  heroID: 1043
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303454
  itemNum: 1
}
rows {
  rewardID: 338
  heroID: 1044
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303497
  itemNum: 1
}
rows {
  rewardID: 339
  heroID: 1045
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303492
  itemNum: 1
}
rows {
  rewardID: 340
  heroID: 1046
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303500
  itemNum: 1
}
rows {
  rewardID: 341
  heroID: 1047
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303507
  itemNum: 1
}
rows {
  rewardID: 342
  heroID: 1048
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303512
  itemNum: 1
}
rows {
  rewardID: 343
  heroID: 1049
  rewardGroup: 7
  rewardType: AHSRT_Card_Purple
  itemId: 303509
  itemNum: 1
}
rows {
  rewardID: 344
  heroID: 1001
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711492
  itemNum: 1
}
rows {
  rewardID: 345
  heroID: 1002
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711125
  itemNum: 1
}
rows {
  rewardID: 346
  heroID: 1003
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711228
  itemNum: 1
}
rows {
  rewardID: 347
  heroID: 1004
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711421
  itemNum: 1
}
rows {
  rewardID: 348
  heroID: 1005
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711127
  itemNum: 1
}
rows {
  rewardID: 349
  heroID: 1006
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711493
  itemNum: 1
}
rows {
  rewardID: 350
  heroID: 1007
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711420
  itemNum: 1
}
rows {
  rewardID: 351
  heroID: 1008
}
rows {
  rewardID: 352
  heroID: 1009
}
rows {
  rewardID: 353
  heroID: 1010
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711215
  itemNum: 1
}
rows {
  rewardID: 354
  heroID: 1011
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711494
  itemNum: 1
}
rows {
  rewardID: 355
  heroID: 1012
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711286
  itemNum: 1
}
rows {
  rewardID: 356
  heroID: 1013
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711221
  itemNum: 1
}
rows {
  rewardID: 357
  heroID: 1014
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711203
  itemNum: 1
}
rows {
  rewardID: 358
  heroID: 1015
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711129
  itemNum: 1
}
rows {
  rewardID: 359
  heroID: 1016
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711206
  itemNum: 1
}
rows {
  rewardID: 360
  heroID: 1017
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711214
  itemNum: 1
}
rows {
  rewardID: 361
  heroID: 1018
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711208
  itemNum: 1
}
rows {
  rewardID: 362
  heroID: 1019
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711495
  itemNum: 1
}
rows {
  rewardID: 363
  heroID: 1020
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711124
  itemNum: 1
}
rows {
  rewardID: 364
  heroID: 1021
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711496
  itemNum: 1
}
rows {
  rewardID: 365
  heroID: 1022
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711225
  itemNum: 1
}
rows {
  rewardID: 366
  heroID: 1023
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711219
  itemNum: 1
}
rows {
  rewardID: 367
  heroID: 1024
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711198
  itemNum: 1
}
rows {
  rewardID: 368
  heroID: 1025
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711293
  itemNum: 1
}
rows {
  rewardID: 369
  heroID: 1026
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711294
  itemNum: 1
}
rows {
  rewardID: 370
  heroID: 1027
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711288
  itemNum: 1
}
rows {
  rewardID: 371
  heroID: 1028
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711335
  itemNum: 1
}
rows {
  rewardID: 372
  heroID: 1029
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711341
  itemNum: 1
}
rows {
  rewardID: 373
  heroID: 1030
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711347
  itemNum: 1
}
rows {
  rewardID: 374
  heroID: 1031
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711351
  itemNum: 1
}
rows {
  rewardID: 375
  heroID: 1032
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711360
  itemNum: 1
}
rows {
  rewardID: 376
  heroID: 1033
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 305002
  itemNum: 1
  forbidReward: true
}
rows {
  rewardID: 377
  heroID: 1034
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711290
  itemNum: 1
}
rows {
  rewardID: 378
  heroID: 1035
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711355
  itemNum: 1
}
rows {
  rewardID: 379
  heroID: 1036
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711367
  itemNum: 1
}
rows {
  rewardID: 380
  heroID: 1037
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711505
  itemNum: 1
}
rows {
  rewardID: 381
  heroID: 1038
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711499
  itemNum: 1
}
rows {
  rewardID: 382
  heroID: 1039
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711497
  itemNum: 1
}
rows {
  rewardID: 383
  heroID: 1040
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711441
  itemNum: 1
}
rows {
  rewardID: 384
  heroID: 1041
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711345
  itemNum: 1
}
rows {
  rewardID: 385
  heroID: 1042
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711501
  itemNum: 1
}
rows {
  rewardID: 386
  heroID: 1043
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711364
  itemNum: 1
}
rows {
  rewardID: 387
  heroID: 1044
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711507
  itemNum: 1
}
rows {
  rewardID: 388
  heroID: 1045
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711503
  itemNum: 1
}
rows {
  rewardID: 389
  heroID: 1046
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711448
  itemNum: 1
}
rows {
  rewardID: 390
  heroID: 1047
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711450
  itemNum: 1
}
rows {
  rewardID: 391
  heroID: 1048
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711434
  itemNum: 1
}
rows {
  rewardID: 392
  heroID: 1049
  rewardGroup: 8
  rewardType: AHSRT_Item
  itemId: 711446
  itemNum: 1
}
rows {
  rewardID: 393
  heroID: 1001
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711080
  itemNum: 1
}
rows {
  rewardID: 394
  heroID: 1002
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711087
  itemNum: 1
}
rows {
  rewardID: 395
  heroID: 1003
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711086
  itemNum: 1
}
rows {
  rewardID: 396
  heroID: 1004
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711511
  itemNum: 1
}
rows {
  rewardID: 397
  heroID: 1005
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711309
  itemNum: 1
}
rows {
  rewardID: 398
  heroID: 1006
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711301
  itemNum: 1
}
rows {
  rewardID: 399
  heroID: 1007
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711513
  itemNum: 1
}
rows {
  rewardID: 400
  heroID: 1008
}
rows {
  rewardID: 401
  heroID: 1009
}
rows {
  rewardID: 402
  heroID: 1010
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711085
  itemNum: 1
}
rows {
  rewardID: 403
  heroID: 1011
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711305
  itemNum: 1
}
rows {
  rewardID: 404
  heroID: 1012
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711078
  itemNum: 1
}
rows {
  rewardID: 405
  heroID: 1013
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711512
  itemNum: 1
}
rows {
  rewardID: 406
  heroID: 1014
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711509
  itemNum: 1
}
rows {
  rewardID: 407
  heroID: 1015
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711093
  itemNum: 1
}
rows {
  rewardID: 408
  heroID: 1016
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711204
  itemNum: 1
}
rows {
  rewardID: 409
  heroID: 1017
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711212
  itemNum: 1
}
rows {
  rewardID: 410
  heroID: 1018
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711334
  itemNum: 1
}
rows {
  rewardID: 411
  heroID: 1019
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711092
  itemNum: 1
}
rows {
  rewardID: 412
  heroID: 1020
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711200
  itemNum: 1
}
rows {
  rewardID: 413
  heroID: 1021
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711094
  itemNum: 1
}
rows {
  rewardID: 414
  heroID: 1022
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711222
  itemNum: 1
}
rows {
  rewardID: 415
  heroID: 1023
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711218
  itemNum: 1
}
rows {
  rewardID: 416
  heroID: 1024
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711196
  itemNum: 1
}
rows {
  rewardID: 417
  heroID: 1025
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711297
  itemNum: 1
}
rows {
  rewardID: 418
  heroID: 1026
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711307
  itemNum: 1
}
rows {
  rewardID: 419
  heroID: 1027
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711296
  itemNum: 1
}
rows {
  rewardID: 420
  heroID: 1028
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711338
  itemNum: 1
}
rows {
  rewardID: 421
  heroID: 1029
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711339
  itemNum: 1
}
rows {
  rewardID: 422
  heroID: 1030
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711348
  itemNum: 1
}
rows {
  rewardID: 423
  heroID: 1031
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711352
  itemNum: 1
}
rows {
  rewardID: 424
  heroID: 1032
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711361
  itemNum: 1
}
rows {
  rewardID: 425
  heroID: 1033
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 305003
  itemNum: 1
  forbidReward: true
}
rows {
  rewardID: 426
  heroID: 1034
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711302
  itemNum: 1
}
rows {
  rewardID: 427
  heroID: 1035
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711356
  itemNum: 1
}
rows {
  rewardID: 428
  heroID: 1036
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711368
  itemNum: 1
}
rows {
  rewardID: 429
  heroID: 1037
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711522
  itemNum: 1
}
rows {
  rewardID: 430
  heroID: 1038
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711516
  itemNum: 1
}
rows {
  rewardID: 431
  heroID: 1039
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711514
  itemNum: 1
}
rows {
  rewardID: 432
  heroID: 1040
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711487
  itemNum: 1
}
rows {
  rewardID: 433
  heroID: 1041
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711344
  itemNum: 1
}
rows {
  rewardID: 434
  heroID: 1042
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711518
  itemNum: 1
}
rows {
  rewardID: 435
  heroID: 1043
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711363
  itemNum: 1
}
rows {
  rewardID: 436
  heroID: 1044
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711524
  itemNum: 1
}
rows {
  rewardID: 437
  heroID: 1045
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711469
  itemNum: 1
}
rows {
  rewardID: 438
  heroID: 1046
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711485
  itemNum: 1
}
rows {
  rewardID: 439
  heroID: 1047
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711486
  itemNum: 1
}
rows {
  rewardID: 440
  heroID: 1048
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711489
  itemNum: 1
}
rows {
  rewardID: 441
  heroID: 1049
  rewardGroup: 9
  rewardType: AHSRT_Item
  itemId: 711488
  itemNum: 1
}
rows {
  rewardID: 442
  heroID: 1001
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 443
  heroID: 1002
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 444
  heroID: 1003
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 445
  heroID: 1004
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 446
  heroID: 1005
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 447
  heroID: 1006
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 448
  heroID: 1007
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 449
  heroID: 1008
}
rows {
  rewardID: 450
  heroID: 1009
}
rows {
  rewardID: 451
  heroID: 1010
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 452
  heroID: 1011
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 453
  heroID: 1012
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 454
  heroID: 1013
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 455
  heroID: 1014
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 456
  heroID: 1015
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 457
  heroID: 1016
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 458
  heroID: 1017
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 459
  heroID: 1018
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 460
  heroID: 1019
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 461
  heroID: 1020
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 462
  heroID: 1021
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 463
  heroID: 1022
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 464
  heroID: 1023
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 465
  heroID: 1024
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 466
  heroID: 1025
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 467
  heroID: 1026
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 468
  heroID: 1027
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 469
  heroID: 1028
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 470
  heroID: 1029
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 471
  heroID: 1030
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 472
  heroID: 1031
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 473
  heroID: 1032
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 474
  heroID: 1033
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 475
  heroID: 1034
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 476
  heroID: 1035
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 477
  heroID: 1036
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 478
  heroID: 1037
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 479
  heroID: 1038
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 480
  heroID: 1039
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 481
  heroID: 1040
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 482
  heroID: 1041
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 483
  heroID: 1042
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 484
  heroID: 1043
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 485
  heroID: 1044
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 486
  heroID: 1045
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 487
  heroID: 1046
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 488
  heroID: 1047
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 489
  heroID: 1048
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
rows {
  rewardID: 490
  heroID: 1049
  rewardGroup: 10
  rewardType: AHSRT_Item
  itemId: 304001
  itemNum: 500
}
