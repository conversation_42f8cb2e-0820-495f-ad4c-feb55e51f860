com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置.xlsx sheet:文本配置
rows {
  content: "成员推荐"
  switch: 1
  stringId: "GameCorridor_MST_Recommend_Members"
}
rows {
  content: "推荐"
  switch: 1
  stringId: "GameCorridor_MST_Recommend_Official"
}
rows {
  content: "当前位置不能使用技能，请换一个平坦开阔的地方试一试"
  switch: 1
  stringId: "Exclusive_Vehicle_SuitSkill_IllegalLocation"
}
rows {
  content: "当前场景不能使用技能"
  switch: 1
  stringId: "Exclusive_Vehicle_SuitSkill_IllegalMap"
}
rows {
  content: "使用技能失败，请稍后再试"
  switch: 1
  stringId: "Exclusive_Vehicle_SuitSkill_IllegalCommon"
}
rows {
  content: "当前场景的负载较大，请等等再尝试"
  switch: 1
  stringId: "Exclusive_Vehicle_SuitSkill_TooMany"
}
rows {
  content: "当前角色状态不能使用技能"
  switch: 1
  stringId: "Exclusive_Vehicle_SuitSkill_IllegalState"
}
rows {
  content: "DJMixer调试完毕，快开始吧！"
  switch: 1
  stringId: "Handhold_MusicConcert_DJToning"
}
rows {
  content: "鼓手准备完毕，快开始吧！"
  switch: 1
  stringId: "Handhold_MusicConcert_DrumToning"
}
rows {
  content: "吉他调音完毕，快开始吧！"
  switch: 1
  stringId: "Handhold_MusicConcert_GuitarToning"
}
rows {
  content: "贝斯调音完毕，快开始吧！"
  switch: 1
  stringId: "Handhold_MusicConcert_BassToning"
}
rows {
  content: "谁是狼人月卡已激活"
  switch: 1
  stringId: "WolfKill_MonthCard_Reward"
}
rows {
  content: "即将完成共创申请流程，无法进行该操作"
  switch: 1
  stringId: "UGC_CoCreate_EditRequest_Completed"
}
rows {
  content: "场景模式下编辑的扣叮数据仅用于场景调试，不会在云端生效"
  switch: 1
  stringId: "UGC_CoCreate_SceneMode_CodingDataDescription"
}
rows {
  content: "云端的扣叮数据已覆盖至本地"
  switch: 1
  stringId: "UGC_CoCreate_SceneMode_CodingDataDownloadTips"
}
rows {
  content: "场景模式下扣叮数据已保存至本地，不会在云端生效"
  switch: 1
  stringId: "UGC_CoCreate_SceneMode_CodingDataSaveTips"
}
rows {
  content: "即将返回场景编辑，当前扣叮数据暂未保存，是否保存至本地以便用于场景调试？"
  switch: 1
  stringId: "UGC_CoCreate_SceneMode_CodingDataNotSaveTips"
}
rows {
  content: "当前地图存在本地的扣叮数据将不会被上传，退出后本地扣叮数据会被清空"
  switch: 1
  stringId: "UGC_CoCreate_SceneMode_Description"
}
rows {
  content: "场景数据已上传至云端"
  switch: 1
  stringId: "UGC_CoCreate_SceneMode_SceneDataUploadTips"
}
rows {
  content: "场景数据有更新，如需拉取最新数据请退出地图重新进入"
  switch: 1
  stringId: "UGC_CoCreate_CodingMode_SceneDataUpdateTips"
}
rows {
  content: "扣叮模式下编辑的场景数据仅用于扣叮调试，不会在云端生效"
  switch: 1
  stringId: "UGC_CoCreate_CodingMode_SceneDataDescription"
}
rows {
  content: "场景数据有更新，如需拉取最新数据请退出地图重新进入，是否确认退出地图？（退出将清空本地场景数据并上传扣叮数据）"
  switch: 1
  stringId: "UGC_CoCreate_CodingMode_SceneDataDownloadConfirm"
}
rows {
  content: "当前地图存在本地的场景数据将不会被上传，退出后本地场景数据会被清空"
  switch: 1
  stringId: "UGC_CoCreate_CodingMode_Description"
}
rows {
  content: "当前玩家[{0}]等正在编辑该地图，请等待完成编辑后再次尝试"
  switch: 1
  stringId: "UGC_CoCreate_MultiPersonEdit_AwaitTips"
}
rows {
  content: "{0}[{1}]正在等待编辑，并在排队的时候“戳”了你们一下"
  switch: 1
  stringId: "UGC_CoCreate_MultiPersonEdit_DingTips"
}
rows {
  content: "当前服务器暂无数据变化"
  switch: 1
  stringId: "UGC_CoCreate_DataSync_DataChange"
}
rows {
  content: "当前服务器有数据更新"
  switch: 1
  stringId: "UGC_CoCreate_DataSync_DataNoChange"
}
rows {
  content: "上传<Orange28>{0}</>至服务器"
  switch: 1
  stringId: "UGC_CoCreate_DataSync_UploadTips1"
}
rows {
  content: "上传本地数据成功后会覆盖并成为最新服务器数据"
  switch: 1
  stringId: "UGC_CoCreate_DataSync_UploadTips2"
}
rows {
  content: "下载<Orange28>{0}</>至本地"
  switch: 1
  stringId: "UGC_CoCreate_DataSync_DownloadTips1"
}
rows {
  content: "下载服务器数据成功后会覆盖并成为最新本地数据"
  switch: 1
  stringId: "UGC_CoCreate_DataSync_DownloadTips2"
}
rows {
  content: "正在编辑的创作者已同意你关于{0}的创作申请，请选择编辑内容模式"
  switch: 1
  stringId: "UGC_CoCreate_ModeSelect_Tips1"
}
rows {
  content: "{0}地图有创作者正在编辑，请选择编辑内容模式"
  switch: 1
  stringId: "UGC_CoCreate_ModeSelect_Tips2"
}
rows {
  content: "该模式主要关于场景内容的编辑，创作者可以在此模式中搭建美丽的场景"
  switch: 1
  stringId: "UGC_CoCreate_ModeSelect_SceneModeDesc"
}
rows {
  content: "该模式主要关于扣叮内容的编辑，创作者可以在此模式中构建丰富的逻辑"
  switch: 1
  stringId: "UGC_CoCreate_ModeSelect_CodingModeDesc"
}
rows {
  content: "在多人共创编辑下，场景模式中的创作者可以对场景内容进行编辑和保存，而对于扣叮部分编辑的内容仅能进行本地测试，无法将更新数据上传服务器，如果需要调整可以联系参与者一起修改。"
  switch: 1
  stringId: "UGC_CoCreate_ModeSelect_SceneModeFullDes"
}
rows {
  content: "在多人共创编辑下，扣叮模式中的创作者可以对扣叮逻辑内容进行编辑和保存，而对于场景部分编辑的内容仅能进行本地测试，无法将更新数据上传服务器，如果需要调整可以联系参与者一起修改。"
  switch: 1
  stringId: "UGC_CoCreate_ModeSelect_CodingModeFullDes"
}
rows {
  content: "场景模式"
  switch: 1
  stringId: "UGC_CoCreate_ModeName_Scene"
}
rows {
  content: "扣叮模式"
  switch: 1
  stringId: "UGC_CoCreate_ModeName_Coding"
}
rows {
  content: "本次共创已终止"
  switch: 1
  stringId: "UGC_CoCreate_Cooperate_Terminate"
}
rows {
  content: "今日触发随机事件"
  switch: 1
  stringId: "InLevel_QST_RandomEvent"
}
rows {
  content: "只有开启了场景唯一功能的情况下才能开启保留场景功能"
  switch: 1
  stringId: "UGC_MultiScene_PreserveScene_Tips"
}
rows {
  content: "该场景已经满员，无法进入"
  switch: 1
  stringId: "UGC_MultiScene_SceneFully_Tips"
}
rows {
  content: "检测到本地图包含多个场景且开启了中途加入，为了保证中途加入正常运行，主场景必须打开【保留场景】【场景唯一】，是否前往打开？"
  switch: 1
  stringId: "UGC_MultiScene_JionHalfway_PreserveScene_Tips"
}
rows {
  content: "游戏时间不足两分钟，不允许进入其他场景"
  switch: 1
  stringId: "UGC_MultiScene_JumpLock_EndingSoon_Tips"
}
rows {
  content: "游戏即将结束，不允许进入其他场景"
  switch: 1
  stringId: "UGC_MultiScene_JumpLock_Countdown_Tips"
}
rows {
  content: "进入场景的时间不足30秒，无法进入其他场景，请稍后再试"
  switch: 1
  stringId: "UGC_MultiScene_JumpCD_Tips"
}
rows {
  content: "加载异常，请稍后重试"
  switch: 1
  stringId: "UGC_MultiScene_JumpFailed_Tips1"
}
rows {
  content: "加载异常，已回到默认场景"
  switch: 1
  stringId: "UGC_MultiScene_JumpFailed_Tips2"
}
rows {
  content: "目标场景已结算销毁，无法进入，游戏进入默认结算"
  switch: 1
  stringId: "UGC_MultiScene_SceneDestroyed_Settlement_Tips"
}
rows {
  content: "当前加载的人数大于目标场景的出生点数量，无法进入场景，请联系作者修改"
  switch: 1
  stringId: "UGC_MultiScene_JumpFailed_OutOfRange_Tips"
}
rows {
  content: "开启后，场景在没有人的时候不会销毁，再次回到该场景时，场景的状态不会被重置，还会是原来的状态。若在保留的场景中触发了结算，其他玩家将无法回到该场景。"
  switch: 1
  stringId: "UGC_MultiScene_PreserveScene_SwitchDescription"
}
rows {
  content: "开启后，该场景在整个对局中唯一，在对局中已有一个该场景的情况下，其他玩家无法新建一个相同的场景进入。"
  switch: 1
  stringId: "UGC_MultiScene_OnlyScene_SwitchDescription"
}
rows {
  content: "添加到我的小程序"
  switch: 1
  stringId: "Subscribe_AddToMyMiniProgram"
}
rows {
  content: "订阅服务通知"
  switch: 1
  stringId: "Subscribe_ServiceNotify"
}
rows {
  content: "添加到桌面"
  switch: 1
  stringId: "Subscribe_AddToDesktop"
}
rows {
  content: "默认开启。开启功能后，拥有描边功能的特殊装扮会生效描边机制并在适用场景中常驻展示，场景中的其他玩家也可视描边效果。（如时装【幻彩画匠 绮】等拥有该机制）\n关闭功能后，则关闭展示特殊装扮的描边机制，场景中的其他玩家不可视描边效果。"
  switch: 1
  stringId: "SettingTip_RoleOutline"
}
rows {
  content: "恭喜冲级成功！"
  switch: 1
  stringId: "FarmReturn_LevelChallengeMax"
}
rows {
  content: "您不在本期新人农场主活动名单哦~\n请关注后续活动吧~"
  switch: 1
  stringId: "FarmReturn_LevelChallenge"
}
rows {
  content: "漫卷云舒"
  switch: 1
  stringId: "UGC_Skybox_Name_84"
}
rows {
  content: "请下载对应玩法资源后查看"
  switch: 1
  stringId: "Bp_Other_Download"
}
rows {
  content: "解锁10人模式后解锁"
  switch: 1
  stringId: "Bp_Other_Condition_Wolves"
}
rows {
  content: "每周任务"
  switch: 1
  stringId: "Activity_FarmPass_WeekTask"
}
rows {
  content: "暑期任务"
  switch: 1
  stringId: "Activity_FarmPass_CyclicalTask"
}
rows {
  content: "第{0}周"
  switch: 1
  stringId: "Activity_FarmPass_WeekIdx"
}
rows {
  content: "奖杯征程解锁多章节后，任意章节的奖杯征程进度会共享统一的周获取上限。\n本赛季时尚分可为奖杯征程中“通过玩法结算获得的奖杯量”提供加成，当前加成比例见左侧展示。"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips1"
}
rows {
  content: "当前奖杯征程章节尚未解锁，暂时无法进行切换"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips2"
}
rows {
  content: "当前星宝所选的奖杯征程章节已累计满进度，继续获得奖杯将会<Red28>产生浪费</>，是否确认切换？"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips3"
}
rows {
  content: "当前星宝所选的奖杯征程章节已累计满进度，继续获得奖杯将会<Red28>产生浪费</>，建议切换其他章节累计进度。"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips4"
}
rows {
  content: "前往匹配"
  switch: 1
  stringId: "UGC_ModeSelect_MapPool_GoMatch"
}
rows {
  content: "默认关闭。关闭功能时，个人奖杯征程的奖杯信息将会将在各类社交界面正常展示。\n开启功能时，个人奖杯征程的奖杯信息将会屏蔽游戏好友、广场玩家、搜索玩家、各社交聊天界面、组队招募界面等展示，其他星宝将不可见。"
  switch: 1
  stringId: "SettingTip_CupShow"
}
rows {
  content: "切换后奖杯累计至所选章节的奖杯征程进度，<ChatSysRed>不同章节每周共享上限</>"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips5"
}
rows {
  content: "当前章节未解锁，请达成解锁条件后查看"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips6"
}
rows {
  content: "本周上限：{0}/{1}"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips7"
}
rows {
  content: "本赛季时尚分加成奖杯量：{0}%"
  switch: 1
  stringId: "Cup_Collect_Multiple_Tips8"
}
rows {
  content: "是否下载当前时装的入场展示动画，已获得更好的视觉效果？（预计<MantelCardCount>{0}</>M）"
  switch: 1
  stringId: "Pak_PV_DownLoad"
}
rows {
  content: "当前可清理资源较多，为了保证您的使用体验，是否前往清理部分非必要资源？（包括缓存资源、近30天内未游玩的玩法资源等）"
  switch: 1
  stringId: "Pak_Delete_Tips"
}
rows {
  content: "资源管理"
  switch: 1
  stringId: "Pak_InterfaceName1"
}
rows {
  content: "推荐下载"
  switch: 1
  stringId: "Pak_InterfaceName2"
}
rows {
  content: "资源清理"
  switch: 1
  stringId: "Pak_InterfaceName3"
}
rows {
  content: "每个资源包下载完成均可领取奖励"
  switch: 1
  stringId: "Pak_InterfaceTip1"
}
rows {
  content: "包含<MantelCardCount>{0}</>，点击打开折叠窗可单独进行下载。"
  switch: 1
  stringId: "Pak_WindowDescription"
}
rows {
  content: "等待中"
  switch: 1
  stringId: "Pak_LineUp_Tip"
}
rows {
  content: "暂时无资源包下载完成"
  switch: 1
  stringId: "Pak_Interface_Tip1"
}
rows {
  content: "暂时无资源包处于下载列表中"
  switch: 1
  stringId: "Pak_Interface_Tip2"
}
rows {
  content: "当前所有资源包都处于下载中/已完成下载"
  switch: 1
  stringId: "Pak_Interface_Tip3"
}
rows {
  content: "已下载"
  switch: 1
  stringId: "Pak_CollectionInterface_Text1"
}
rows {
  content: "下载中"
  switch: 1
  stringId: "Pak_CollectionInterface_Text2"
}
rows {
  content: "（预计需要<MantelCardCount>{0}</>M）"
  switch: 1
  stringId: "Pak_AllDownload_Tip"
}
rows {
  content: "资源已占用：{0}/{1}"
  switch: 1
  stringId: "Pak_WVAInterface_Tip1"
}
rows {
  content: "<Red28>*需要{0}M以下载{1}</>"
  switch: 1
  stringId: "Pak_WVAInterface_Tip2"
}
rows {
  content: "删除模型"
  switch: 1
  stringId: "UGC_AIGCModelDelete_Title"
}
rows {
  content: "是否确认删除该模型？"
  switch: 1
  stringId: "UGC_AIGCModelDelete_Tips"
}
rows {
  content: "是否确认清空当前装扮方案？"
  switch: 1
  stringId: "System_Bag_Slot_Ready_To_Clear"
}
rows {
  content: "设备空间不足，资源已暂停下载"
  switch: 1
  stringId: "Pak_LowSpace_Tip1"
}
rows {
  content: "小游戏高清资源储存空间不足，请手动清理游戏资源后继续下载"
  switch: 1
  stringId: "Pak_LowSpace_Tip2"
}
rows {
  content: "设备空间不足，无法下载"
  switch: 1
  stringId: "Pak_LowSpace_Tip3"
}
rows {
  content: "小游戏高清资源储存空间不足，请手动清理游戏资源后下载"
  switch: 1
  stringId: "Pak_LowSpace_Tip4"
}
rows {
  content: "该资源内容无法发布"
  switch: 1
  stringId: "UGC_InterfaceCover_CantPublish"
}
rows {
  content: "无法选中社区资源进行二次分享"
  switch: 1
  stringId: "UGC_InterfaceRes_CantPickForPub"
}
