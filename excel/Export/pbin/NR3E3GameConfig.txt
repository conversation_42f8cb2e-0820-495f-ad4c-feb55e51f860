com.tencent.wea.xlsRes.table_NR3EGameConfigData
excel/xls/N_NR3E_游戏基础配置.xlsx sheet:E3GameConfig
rows {
  ConfigName: "E3.nouse1"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.floatParamConfig.LoadingTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.floatParamConfig.RemainTimeShowTimeTip"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.RemainTimeShowTimePanel"
  ConfigValue: 27.0
}
rows {
  ConfigName: "E3.floatParamConfig.EmergentReportCountLimit"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.floatParamConfig.EmergentReportCD"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.MeetingReportDelayTime"
  ConfigValue: 1.8
}
rows {
  ConfigName: "E3.floatParamConfig.GameFinishTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.floatParamConfig.GameFinishTimeAdd"
  ConfigValue: 6.0
}
rows {
  ConfigName: "E3.Meeting.DiscussOnePlayerTime"
  ConfigValue: 45.0
}
rows {
  ConfigName: "E3.Meeting.VoteTime"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_AutoReport.AutoReportDelayTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.GameConfig.NormalTaskConfig.AddNewTaskTimeInterval"
  ConfigValue: 30.0
  ConfigValue: 35.0
  ConfigValue: 40.0
  ConfigValue: 50.0
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.GameConfig.NormalTaskConfig.AddNewTaskDeadTimeInterval"
  ConfigValue: 80.0
  ConfigValue: 100.0
  ConfigValue: 100.0
  ConfigValue: 120.0
  ConfigValue: 140.0
}
rows {
  ConfigName: "E3.GameConfig.50305.NormalTaskConfig.AddNewTaskTimeInterval"
  ConfigValue: 35.0
  ConfigValue: 40.0
  ConfigValue: 45.0
  ConfigValue: 55.0
  ConfigValue: 65.0
}
rows {
  ConfigName: "E3.floatParamConfig.MeetingVoiceTalkRange"
  ConfigValue: 7000.0
}
rows {
  ConfigName: "E3.floatParamConfig.VoiceTalkRange"
  ConfigValue: 14.0
}
rows {
  ConfigName: "E3.WarmRoundConfig.Debug.ForceEnable"
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.floatParamConfig.MeetingReportTimeDiffLimit"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.UseRadar.Radius"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.UseRadio.Radius"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.UseRadio.Time"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.UseRadio.ShowEffectTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Reputation.QuitDeductScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Reputation.HangUpDeductScoreLevel1"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Reputation.HangUpDeductTimeLevel1"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.Reputation.HangUpDeductScoreLevel2"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Reputation.HangUpDeductTimeLevel2"
  ConfigValue: 120.0
}
rows {
  ConfigName: "E3.Reputation.E3CompleteGameAddScore"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Reputation.QuitDeductBehaviorScore"
  ConfigValue: 50.0
}
rows {
  ConfigName: "E3.Reputation.HangUpDeductBehaviorScoreLevel1"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Reputation.HangUpDeductBehaviorScoreLevel2"
  ConfigValue: 50.0
}
rows {
  ConfigName: "E3.Reputation.E3CompleteGameAddBehaviorScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Reputation.DailyLoginAddScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Reputation.AllowMatchPercent"
  ConfigValue: 70.0
}
rows {
  ConfigName: "E3.Reputation.ActivePlayerPercent"
  ConfigValue: 0.5
}
rows {
  ConfigName: "E3.DoorFacility.SkillUseMaxCD"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.GameConfig.FacilityParamConfig.FacilityParamMap.BP_NR3E3DoorFacility.Params.0.Param.IntParam.MaxAutoCoolCD"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.DoorFacility.CloseDuringTime"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.GameConfig.FacilityParamConfig.FacilityParamMap.BP_NR3E3_PA_PR_EmergentMeeting.Params.0.Param.FlotParam.ReportCD"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.GameConfig.FacilityParamConfig.FacilityParamMap.BP_NR3E3_PA_PR_EmergentMeeting.Params.0.Param.FlotParam.ReportCD1"
  ConfigValue: 45.0
}
rows {
  ConfigName: "E3.GameConfig.FacilityParamConfig.FacilityParamMap.BP_NR3E3_PA_PR_EmergentMeeting.Params.0.Param.FlotParam.ReportCD2"
  ConfigValue: 45.0
}
rows {
  ConfigName: "E3.GameConfig.FacilityParamConfig.FacilityParamMap.BP_NR3E3_PA_PR_EmergentMeeting.Params.0.Param.FlotParam.ReportCD3"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.GameConfig.FacilityParamConfig.FacilityParamMap.BP_NR3E3_PA_PR_EmergentMeeting.Params.0.Param.FlotParam.ReportCD4"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.GameConfig.109.MeetingConfig.MeetingConfig.VoteTime"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.GameConfig.109.MeetingConfig.MeetingConfig.DiscussTime"
  ConfigValue: 50.0
}
rows {
  ConfigName: "E3.GameConfig.GameSettlementConfig.TaskProgression"
  ConfigValue: 310.0
}
rows {
  ConfigName: "E3.floatParamConfig.CheckTaskRange"
  ConfigValue: 160000.0
}
rows {
  ConfigName: "E3.GameConfig.105.GameSettlementConfig.TaskProgression"
  ConfigValue: 270.0
}
rows {
  ConfigName: "E3.Meeting.DiscussTimeOutSkipSpeechTime1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.Meeting.DiscussTimeOutSkipSpeechTime2"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Meeting.DiscussTimeOutTipsTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.MasterRoad.MaxSpecialization"
  ConfigValue: 3000.0
}
rows {
  ConfigName: "E3.FeedBack.AllFeedBackCount"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.TipsShowTime"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.nouse2"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3MeetingTime_1"
  ConfigValue: 30.0
  ConfigValue: 60.0
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3MeetingTime_2"
  ConfigValue: 45.0
  ConfigValue: 90.0
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3MeetingTime_3"
  ConfigValue: 90.0
  ConfigValue: 120.0
  ConfigValue: 90.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3EmergencyNum_1"
  ConfigValue: 0.0
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3EmergencyNum_2"
  ConfigValue: 1.0
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3EmergencyNum_3"
  ConfigValue: 2.0
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3EmergencyNum_4"
  ConfigValue: 999.0
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3TaskNum_107"
  ConfigValue: 300.0
  ConfigValue: 350.0
  ConfigValue: 400.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3TaskNum_108"
  ConfigValue: 400.0
  ConfigValue: 500.0
  ConfigValue: 600.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3TaskNum_110"
  ConfigValue: 210.0
  ConfigValue: 270.0
  ConfigValue: 310.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3TaskNum_111"
  ConfigValue: 250.0
  ConfigValue: 300.0
  ConfigValue: 350.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_105"
  ConfigValue: 0.0
  ConfigValue: 50.0
  ConfigValue: 210.0
  ConfigValue: 50.0
  ConfigValue: 100.0
  ConfigValue: 260.0
  ConfigValue: 100.0
  ConfigValue: 150.0
  ConfigValue: 260.0
  ConfigValue: 150.0
  ConfigValue: 9999.0
  ConfigValue: 270.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_106"
  ConfigValue: 0.0
  ConfigValue: 50.0
  ConfigValue: 260.0
  ConfigValue: 50.0
  ConfigValue: 100.0
  ConfigValue: 280.0
  ConfigValue: 100.0
  ConfigValue: 150.0
  ConfigValue: 320.0
  ConfigValue: 150.0
  ConfigValue: 9999.0
  ConfigValue: 340.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_109"
  ConfigValue: 0.0
  ConfigValue: 50.0
  ConfigValue: 250.0
  ConfigValue: 50.0
  ConfigValue: 100.0
  ConfigValue: 270.0
  ConfigValue: 100.0
  ConfigValue: 150.0
  ConfigValue: 310.0
  ConfigValue: 150.0
  ConfigValue: 9999.0
  ConfigValue: 330.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_113"
  ConfigValue: 0.0
  ConfigValue: 50.0
  ConfigValue: 250.0
  ConfigValue: 50.0
  ConfigValue: 100.0
  ConfigValue: 270.0
  ConfigValue: 100.0
  ConfigValue: 150.0
  ConfigValue: 310.0
  ConfigValue: 150.0
  ConfigValue: 9999.0
  ConfigValue: 330.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_151"
  ConfigValue: 0.0
  ConfigValue: 50.0
  ConfigValue: 250.0
  ConfigValue: 50.0
  ConfigValue: 100.0
  ConfigValue: 270.0
  ConfigValue: 100.0
  ConfigValue: 150.0
  ConfigValue: 310.0
  ConfigValue: 150.0
  ConfigValue: 9999.0
  ConfigValue: 330.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_1010"
  ConfigValue: 0.0
  ConfigValue: 9999.0
  ConfigValue: 350.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_1011"
  ConfigValue: 0.0
  ConfigValue: 9999.0
  ConfigValue: 380.0
}
rows {
  ConfigName: "E3.Match.nr3e3TaskNum_1012"
  ConfigValue: 0.0
  ConfigValue: 9999.0
  ConfigValue: 380.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_105"
  ConfigValue: 5001.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_106"
  ConfigValue: 5002.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_107"
  ConfigValue: 5003.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_108"
  ConfigValue: 5004.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_109"
  ConfigValue: 5002.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_110"
  ConfigValue: 5001.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_111"
  ConfigValue: 5002.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_112"
  ConfigValue: 5002.0
}
rows {
  ConfigName: "E3.Match.AIMatchID_151"
  ConfigValue: 5002.0
}
rows {
  ConfigName: "E3.CustomRoom.MatchGroup"
  ConfigValue: 107.0
  ConfigValue: 108.0
  ConfigValue: 110.0
  ConfigValue: 111.0
}
rows {
  ConfigName: "E3.CustomRoom.nr3e3CampSort"
  ConfigValue: 2.0
  ConfigValue: 1.0
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.nouse3"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.floatParamConfig.HunterAttackRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.floatParamConfig.HunterAttackCoolTime"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.HunterTransferCoolTime"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.floatParamConfig.HunterReportCoolTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.floatParamConfig.AngelDefenseCoolTime"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.AngelAttackCount"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.floatParamConfig.AngelProtectRange"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.floatParamConfig.DeadBodyReportDistance"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.floatParamConfig.SheriffAttackCoolTime"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.SheriffAttackRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.floatParamConfig.GamblerGuessRightCount"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.floatParamConfig.SecretDetectiveGuessRightCount"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.BountyHunterKillCount"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.floatParamConfig.ActiveWarriorAttackTaskNum"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_AssassinWolf_Assassin.AbilityCosts.0.MaximumAvailableTimes"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_AssassinWolf_Assassin.AbilityCosts.0.DefaultAvailableTime"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_AssassinWolf_Assassin.AbilityCosts.1.MaximumAvailableTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_AssassinWolf_Assassin.AbilityCosts.1.DefaultAvailableTime"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Judge_Judgment.AbilityCosts.0.MaximumAvailableTimes"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Judge_Judgment.AbilityCosts.0.DefaultAvailableTime"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Judge_Judgment.AbilityCosts.1.MaximumAvailableTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Judge_Judgment.AbilityCosts.1.DefaultAvailableTime"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_BountyHunterAttack.CoolDownDuration"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_BountyHunterAttack.DetectedEnemyRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_BountyHunterAttack.HunterCampWeight"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_BountyHunterAttack.NoHunterCampWeight"
  ConfigValue: 100.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Death."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Detect.CoolDownDuration"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Detect.DetectDurationTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Detect.CanUseDetectDistance"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Detect.DetectDistance"
  ConfigValue: 500.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Detect.DetectAllowLeaveLimitTime"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Detect.AbilityCosts.0.MaximumAvailableTimes"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Detect.AbilityCosts.0.DefaultAvailableTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Doll_CompleteMission.DetectedTaskRadius"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Doll_Report.CoolDownDuration"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Doll_Report.ValidReportRange"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Doll_UrgentReport.CoolDownDuration"
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Doll_UrgentReport.DetectBellRadius"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Doll_UrgentReportAni."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_FireMen_CompleteUrgentMission.AbilityCosts.0.MaximumAvailableTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_FireMen_CompleteUrgentMission.AbilityCosts.0.DefaultAvailableTime"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Attack.CoolDownDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Attack.CoolDownDurationMag"
  ConfigValue: 1.167
  ConfigValue: 1.067
  ConfigValue: 0.867
  ConfigValue: 0.867
  ConfigValue: 0.8
  ConfigValue: 1.167
  ConfigValue: 0.933
  ConfigValue: 0.933
  ConfigValue: 0.867
  ConfigValue: 0.933
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Attack.DetectedEnemyRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Bomb.CoolDownDuration"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Bomb.BombDetectedRadius"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Bomb.BombFiredDuration"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_UseHole.CoolDownDuration"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_UseHole.DetectedHoleDistance"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Joker_BeVoteOutWin."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Mercy_Shield."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Mercy_ShieldBroke."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Preside_Vote."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Scholar_ExtraMissionProgress.ExtraProgressByFinishMission"
  ConfigValue: 0.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Sheriff_Attack."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_SideGuess."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Skunk_Fart.CoolDownDuration"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Skunk_Fart.DetectedEnemyRange"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_TrickWolf_ChangeAvatar.CoolDownDuration"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_TrickWolf_ChangeAvatar.EffectDuration"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Warrior_Attack.DetectedEnemyRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Warrior_Attack.AbilityCosts.0.MaximumAvailableTimes"
  ConfigValue: 999.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Warrior_Attack.AbilityCosts.0.DefaultAvailableTime"
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Warrior_Attack.CoolDownDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationSubAbility_Hunter_TransferBomb.CoolDownDuration"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationSubAbility_Hunter_TransferBomb.BombDetectedRadius"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationSubAbility_Hunter_UseHoleTelport."
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Bomb.MinDelayAddTransferBomb"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_Bomb.MaxDelayAddTransferBomb"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Drawer_UseRadar.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Drawer_UseRadar.MapCloseTime"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Naughty_Trick.CoolDown"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Naughty_Trick.ActivateRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Naughty_Trick.ActivateDuration"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SneakWolf_Sneak.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SneakWolf_Sneak.ActivateDuration"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SneakWolf_Sneak.SneakSpeedUpBoost"
  ConfigValue: 1.2
}
rows {
  ConfigName: "E3.floatParamConfig.CompletePuppetNumber"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_PuppetMaster_GeneratePuppet.CoolDownDuration"
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.VocationDistribution.PuppetMasterTeammates"
  ConfigValue: 5.0
  ConfigValue: 13.0
  ConfigValue: 17.0
  ConfigValue: 24.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Porter_Carry.CarryRange"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Porter_Carry.CarryCoolDown"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Porter_Carry.CarryVelocityMag"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Porter_Carry.CarryWinNumber"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_SubAbility_Porter_CarryDrop.CarryDropToHoleRange"
  ConfigValue: 500.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_SubAbility_Porter_CarryDrop.CarryDropCD"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_SubAbility_Porter_CarryDrop.CarryDropToHoleCD"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StrongHunter_Carry.CarryRange"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StrongHunter_Carry.CarryCoolDown"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StrongHunter_Carry.CarryVelocityMag"
  ConfigValue: 0.7
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_SubAbility_StrongHunter_Carry.CarryDropToHoleRange"
  ConfigValue: 500.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_SubAbility_StrongHunter_Carry.CarryDropCD"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_SubAbility_StrongHunter_Carry.CarryDropToHoleCD"
  ConfigValue: 30.0
}
rows {
  ConfigName: "BP_VocationAbility_NuClearHunter_Bomb.CoolDown"
  ConfigValue: 11.0
}
rows {
  ConfigName: "BP_VocationAbility_NuClearHunter_Bomb.BombRange"
  ConfigValue: 1500.0
}
rows {
  ConfigName: "BP_VocationAbility_NuClearHunter_Bomb.BombDuration"
  ConfigValue: 5.0
}
rows {
  ConfigName: "BP_VocationAbility_NuClearHunter_Bomb.WarningSoundDelayMin"
  ConfigValue: 3.0
}
rows {
  ConfigName: "BP_VocationAbility_NuClearHunter_Bomb.WarningSoundDelayMax"
  ConfigValue: 5.0
}
rows {
  ConfigName: "BP_VocationAbility_NuClearHunter_Bomb.BombMaxiumTimes"
  ConfigValue: 6.0
}
rows {
  ConfigName: "BP_Ability_Ranger_Attack.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "BP_Ability_Ranger_Attack.AttackRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "BP_Ability_Ranger_Attack.SingleModeRange"
  ConfigValue: 2200.0
}
rows {
  ConfigName: "BP_Ability_Ranger_Attack.SingleModeDuration"
  ConfigValue: 5.0
}
rows {
  ConfigName: "BP_Ability_Tramp_Pack.Range"
  ConfigValue: 250.0
}
rows {
  ConfigName: "BP_Ability_Tramp_Pack.CoolDown"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.SealingCount"
  ConfigValue: 2.0
}
rows {
  ConfigName: "BP_Ability_Forensic_Survey.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "BP_Ability_Forensic_Survey.DetectRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "BP_Ability_Forensic_Survey.SurveyCompleteDuration"
  ConfigValue: 3.0
}
rows {
  ConfigName: "BP_Ability_Forensic_Survey.SurveyWarningTime"
  ConfigValue: 1.5
}
rows {
  ConfigName: "BP_Ability_Forensic_Survey.SurveyWarningRange"
  ConfigValue: 500.0
}
rows {
  ConfigName: "BP_Ability_PhotoGrapher_Screen.CoolDown"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.LoneWolf_HunterAttack.CoolDown"
  ConfigValue: 22.0
}
rows {
  ConfigName: "BP_Ability_Psychic.Psychic.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "BP_Ability_Psychic.Psychic.Duration"
  ConfigValue: 60.0
}
rows {
  ConfigName: "BP_Ability_Psychic.Psychic.InterruptCoolDown"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_FlashWolf_Flash.CoolDown"
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_FlashWolf_Flash.FlashBombRadius"
  ConfigValue: 2000.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_FlashWolf_Flash.FlashBombDuration"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Ghost_Chasing.ChasingVelocityMultiplier"
  ConfigValue: 0.7
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_GhostWolfAttack.CoolDownDuration"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_GhostWolfAttack.StartChaseTime"
  ConfigValue: 4.1
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_GhostWolfAttack.PostAttackDisplayTime"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_GhostWolfAttack.AttackDelay"
  ConfigValue: 0.8
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_GhostWolfAttack.MaxChaseDistance"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_GhostWolfAttack.AllowedAttackDistance"
  ConfigValue: 2.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_GhostWolfAttack.MaxPathDistance"
  ConfigValue: 50.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Hunter_EndGhostWolfState.CoolDownDuration"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_EagleEye.PerspectiveRange"
  ConfigValue: 70.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_EagleEye.InViewDistance"
  ConfigValue: 50.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_EagleEye.Duration"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_EagleEye.InTaskFogDistance"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_EagleEye.InFogDistance"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_EagleEye.CoolDownDuration"
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Tracker_Guide.TrackDuration"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Defender_Protected.ProtectetCoolDown"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Defender_Protected.ProtectedRadius"
  ConfigValue: 350.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Defender_Protected.ProtectedDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Defender_Protected.ProtectedEffectiveRadius"
  ConfigValue: 1000.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Defender_Protected.ProtectedEffectiveStopTime"
  ConfigValue: 2.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_HoleWolf_SpawnHole.CoolDown"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Plumber.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Plumber.EffectiveRadius"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Plumber.EffectiveDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.VocationAbility_Dreamcatcher.CaptureQuantity"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Vocation.Ability.VocationAbility_Dreamcatcher.AttackDistance"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.VocationAbility_Dreamcatcher.CoolDownDuration"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_DeedWolf_Deed.GoalCoolDownBoost"
  ConfigValue: 0.6
}
rows {
  ConfigName: "E3.Vocation.Ability.VocationAbility_Ostrich_DodgeAttack.Duration"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Ostrich_DodgeAttack.CoolDownDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Ostrich_CancelDodge.CoolDownDuration"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.DanceWolf.SkillCoolTime"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.DanceWolf.SkillRadius"
  ConfigValue: 900.0
}
rows {
  ConfigName: "E3.Vocation.Ability.DanceWolf.SkillDisableControlTime"
  ConfigValue: 0.5
}
rows {
  ConfigName: "E3.Vocation.Ability.DanceWolf.SkillDurationTime"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Dancer.SkillCoolTime"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Dancer.SkillRadius"
  ConfigValue: 900.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Dancer.SkillDurationTime"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Delivery.SkillCoolTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Delivery.SkillRadius"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Delivery.WinCompletedTimes"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_FogSneakWolf_Sneak.SneakSpeedUpBoost"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_FirstAider_Rescue.DetectedTargetRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_FirstAider_Rescue.CoolDownDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_FirstAider_Rescue.CanRescueDuration"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_FirstAider_Rescue.RescueDuration"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_BestDetective_Survey.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_BestDetective_Survey.DetectDistance"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_BestDetective_Survey.CompleteDuration"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_BestDetective_Survey.WarningTime"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_BestDetective_Survey.WarningDistance"
  ConfigValue: 500.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Ability_Hamster_Storage.AbilityCosts.0.MaximumAvailableTimes"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Ability_Hamster_Storage.AbilityCosts.0.DefaultAvailableTime"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_SwordsManAttack.CoolDownDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_SwordsManAttack.DetectedEnemyRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_BlackWolf.CoolDown"
  ConfigValue: 35.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_BlackWolf.Duration"
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Fireworks.CoolDown"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Fireworks.Duration"
  ConfigValue: 6.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SnowWolf.CoolDown"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SnowWolf.Duration"
  ConfigValue: 0.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SnowWolf.SpeedMulty"
  ConfigValue: 1.4
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SnowWolf.MinExitTime"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Twin_Alive.CoolDown"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Twin_Alive.DetectRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Twin_Alive.SurveyCompleteDuration"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Twin_Alive.SurveyWarningTime"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Twin_Alive.SurveyWarningRange"
  ConfigValue: 500.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.SpeedMsg"
  ConfigValue: 0.7
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.BackSpeedMsg"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.AttackRange"
  ConfigValue: 350.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.AttackHalfHeight"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.DetectedRange"
  ConfigValue: 1800.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.ChasedTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.ChasedNearRange"
  ConfigValue: 150.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.SurvivalTime"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.PostAttackSurvivalTime"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.CoolDown"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.NumberOfCompleteToWin"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_GenerateNianBeast.CoolDown"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.NianBeastAttackLockTime"
  ConfigValue: 0.1
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_GenerateNianBeast.GenerateTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_NianBeast_Attack.NaoNaoFireKillNianBeastDistance"
  ConfigValue: 1600.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_TrapWolf.Duration"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_TrapWolf.CoolDown"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SmokeWolf_Smoke.MaxAddRadiusSeconds"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SmokeWolf_Smoke.CoolDown"
  ConfigValue: 13.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SmokeWolf_Smoke.DefaultRadius"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SmokeWolf_Smoke.EveryAddRadiusSeconds"
  ConfigValue: 0.03
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_SmokeWolf_Smoke.EveryAddRadius"
  ConfigValue: 0.07
}
rows {
  ConfigName: "E3.Vocation.Ability.NR3E3_VocationAbility_ControllerWolf.ControlDuration"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Conspirators_VoteOutNotHunter.NumberOfCompleteToWin"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Teacher.Radius"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Teacher.ExtraMission"
  ConfigValue: 0.2
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_Teacher.MaxTimes"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StingWolf.StingRadius"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StingWolf.PreStingBreakTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StingWolf.WinPreyRequiredNums"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StingWolf.WinHunterRequiredNums"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StingWolf.SpeedDownDuration"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StingWolf.SpeedDownStep"
  ConfigValue: 0.1
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_StingWolf.OverlapDistance"
  ConfigValue: 100.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_AgentAttack.CoolDown"
  ConfigValue: 45.0
}
rows {
  ConfigName: "E3.nouse4"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.floatParamConfig.VoteBadSideScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.FinishMainTaskScore"
  ConfigValue: 6.0
}
rows {
  ConfigName: "E3.floatParamConfig.FinishEmergentTaskScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.DollsScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.PoliceScore"
  ConfigValue: 35.0
}
rows {
  ConfigName: "E3.floatParamConfig.AngelScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.floatParamConfig.DetectScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.SentryScore"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.FireManUseSkillScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.floatParamConfig.FireManScore"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.floatParamConfig.WarriorScore"
  ConfigValue: 35.0
}
rows {
  ConfigName: "E3.floatParamConfig.AnchorScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.floatParamConfig.ScholaScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.NaughtyScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.NaughtyScore2"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.DrawerScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.GreatStarScore"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.ElderScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.RangerScore"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.ExaminerScore"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.PsychicScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.HackerScore"
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.floatParamConfig.ClairvoyantScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.ListenertScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.JudgetScore"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side43SettleScore2"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side44SettleScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side46SettleScore"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side51SettleScore2"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side49SettleScore1"
  ConfigValue: 35.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side52SettleScore"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side59SettleScore3"
  ConfigValue: 35.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side57SettleScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side56SettleScore"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side61SettleScore3"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side63SettleScore"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side65SettleScore"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side65TwinAliveScore"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side66SettleScore2"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side68SettleScore"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side71SettleScore"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side70SettleScore"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side75SettleScore2"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side77SettleScore"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side81SettleScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.KillScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.BadSideAliveScore"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.floatParamConfig.WolfScore"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.TrickWolfScore"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.BombScore"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.floatParamConfig.AssassinScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.SneakWolfScore"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.floatParamConfig.AloneWolfScore"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.floatParamConfig.StrengthWolfScore"
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.floatParamConfig.NuClearWolfScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.SealingWolfScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.GhostWolfScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.FireEyeWolfScore"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.ShineWolfScore"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side45SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side48SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side50SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side53SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side40SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side58SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side64SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side69SettleScore"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side73SettleScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side72SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side74SettleScore"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side74SettleScore_2"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side78SettleScore1"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side80SettleScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.NeutralSideAliveScore"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.floatParamConfig.GamblerScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.SecretDetectiveScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.BountyHunterScore"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.JokerScore"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.SkunkScore"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.floatParamConfig.PorterScore"
  ConfigValue: 70.0
}
rows {
  ConfigName: "E3.floatParamConfig.PuppetScore"
  ConfigValue: 70.0
}
rows {
  ConfigName: "E3.floatParamConfig.TrampScore"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.IronManScore1"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.floatParamConfig.IronManScore2"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.IronManScore3"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side47SettleScore"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side54SettleScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side55SettleScore"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side60SettleScore"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side67SettleScore3"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side82SettleScore"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side62SettleScoreParam_1"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side62SettleScoreParam_2"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side62SettleScoreParam_3"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side62SettleScoreParam_4"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side76SettleScoreParam_1"
  ConfigValue: 35.0
}
rows {
  ConfigName: "E3.floatParamConfig.Side76SettleScoreParam_2"
  ConfigValue: 45.0
}
rows {
  ConfigName: "E3.floatParamConfig.WinScore"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.floatParamConfig.LoseScore"
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.nouse5"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.floatParamConfig.FishDelayBaseTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.floatParamConfig.FishDelayRandomLimitTime"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.floatParamConfig.FishHookTime"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Task207.CompleteCount"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Task207.MouseShowTime"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Task206.BlockSpeed"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.Task206.CreateBlockCD"
  ConfigValue: 1.7
}
rows {
  ConfigName: "E3.Task206.CompleteCount"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Task217.CompleteCount"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Task222.CompleteTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Task213.FireTime"
  ConfigValue: 0.3
}
rows {
  ConfigName: "E3.Task404.MoveSpeed"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Task406.CompleteTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.NotAllowAssassinVocation"
  ConfigValue: 4.0
  ConfigValue: 9.0
}
rows {
  ConfigName: "E3.Vocation.Ability.NotAllowSealingVocation"
  ConfigValue: 4.0
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.NotAllowSideGuessVocation"
  ConfigValue: 4.0
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.Vocation.Ability.SecretDetectiveNotAllowSideGuessVocation"
  ConfigValue: 1.0
  ConfigValue: 4.0
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.VocationDistribution.TaskFirefightersConfig"
  ConfigValue: 7.0
  ConfigValue: 4.0
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.VocationDistribution.TaskFireTimeConfig"
  ConfigValue: 60.0
  ConfigValue: 70.0
  ConfigValue: 80.0
}
rows {
  ConfigName: "E3.VocationDistribution.TaskFireTimeConfig50305"
  ConfigValue: 65.0
  ConfigValue: 75.0
  ConfigValue: 85.0
}
rows {
  ConfigName: "E3.VocationDistribution.TaskFireTimeConfig50306"
  ConfigValue: 70.0
  ConfigValue: 80.0
  ConfigValue: 90.0
}
rows {
  ConfigName: "E3.VocationDistribution.TaskFireTimeConfig50307"
  ConfigValue: 70.0
  ConfigValue: 80.0
  ConfigValue: 90.0
}
rows {
  ConfigName: "E3.VocationDistribution.TaskFireTimeConfig50308"
  ConfigValue: 70.0
  ConfigValue: 80.0
  ConfigValue: 90.0
}
rows {
  ConfigName: "E3.VocationDistribution.TaskFireTimeConfig50309"
  ConfigValue: 70.0
  ConfigValue: 80.0
  ConfigValue: 90.0
}
rows {
  ConfigName: "E3.floatParamConfig.TakePillsAllTime"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.floatParamConfig.TakePillsInterval"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.SickenTask.PointNumKey"
  ConfigValue: 4.0
  ConfigValue: 6.0
  ConfigValue: 8.0
  ConfigValue: 11.0
}
rows {
  ConfigName: "E3.SickenTask.PointNumValue"
  ConfigValue: 3.0
  ConfigValue: 4.0
  ConfigValue: 5.0
  ConfigValue: 6.0
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.SickenTask.CutDownNum"
  ConfigValue: 65.0
}
rows {
  ConfigName: "E3.SickenTask.CutDownNum50306"
  ConfigValue: 75.0
}
rows {
  ConfigName: "E3.SickenTask.CutDownNum50307"
  ConfigValue: 75.0
}
rows {
  ConfigName: "E3.SickenTask.CutDownNum50308"
  ConfigValue: 75.0
}
rows {
  ConfigName: "E3.SickenTask.CutDownNum50309"
  ConfigValue: 75.0
}
rows {
  ConfigName: "E3.Vocation.Ability.NotAllowJudgeVocation"
  ConfigValue: 35.0
}
rows {
  ConfigName: "E3.floatParamConfig.BrushBlackboardPercentageLimit"
  ConfigValue: 0.95
}
rows {
  ConfigName: "E3.nouse6"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.MaxSpawnNumber"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.MaxSpawnNumberAfterMeeting"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_Ability_ResponLevelActor.ResponRadius"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.SpeedShoesPriority"
  ConfigValue: 6.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.StealthCloakPriority"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.GiganticPriority"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.RandomAvatarPriority"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.RefreshCoolDownPriority"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.IceBombPriority"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor.SlimeBombPriority"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_SpeedUpShooes.Duration"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_SpeedUpShooes.SpeedUpMag"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_StealthCloak.Duration"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_Gigantic.Duration"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_Gigantic.BoostMag"
  ConfigValue: 1.7
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_RandomAvatar.Duration"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_IceBomb.Duration"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_IceBomb.EffectRadius"
  ConfigValue: 500.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_IceBomb.BombDelay"
  ConfigValue: 1.4
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_SlimeBomb.Duration"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_SlimeBomb.EffectRadius"
  ConfigValue: 500.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_SlimeBomb.SpeedDownMag"
  ConfigValue: 0.5
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnPickableItems_SlimeBomb.BombDelay"
  ConfigValue: 1.4
}
rows {
  ConfigName: "E3.SeasonTheme.Effect.Duration"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.SeasonTheme.Refresh.Time"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.SeasonTheme.Refresh.Num"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActors_Grass.MaxSpawnNumber"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.nouse7"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_1"
  ConfigValue: 0.0
  ConfigValue: 100.0
  ConfigValue: 1.0
  ConfigValue: 1.0
  ConfigValue: 101.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_2"
  ConfigValue: 0.0
  ConfigValue: 100.0
  ConfigValue: 1.0
  ConfigValue: 2.0
  ConfigValue: 102.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_3"
  ConfigValue: 0.0
  ConfigValue: 100.0
  ConfigValue: 1.0
  ConfigValue: 3.0
  ConfigValue: 103.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_4"
  ConfigValue: 100.0
  ConfigValue: 250.0
  ConfigValue: 1.0
  ConfigValue: 1.0
  ConfigValue: 104.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_5"
  ConfigValue: 100.0
  ConfigValue: 250.0
  ConfigValue: 1.0
  ConfigValue: 2.0
  ConfigValue: 105.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_6"
  ConfigValue: 100.0
  ConfigValue: 250.0
  ConfigValue: 1.0
  ConfigValue: 3.0
  ConfigValue: 106.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_7"
  ConfigValue: 250.0
  ConfigValue: 400.0
  ConfigValue: 1.0
  ConfigValue: 1.0
  ConfigValue: 107.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_8"
  ConfigValue: 250.0
  ConfigValue: 400.0
  ConfigValue: 1.0
  ConfigValue: 2.0
  ConfigValue: 108.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_9"
  ConfigValue: 250.0
  ConfigValue: 400.0
  ConfigValue: 1.0
  ConfigValue: 3.0
  ConfigValue: 109.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_10"
  ConfigValue: 400.0
  ConfigValue: 9999.0
  ConfigValue: 1.0
  ConfigValue: 1.0
  ConfigValue: 110.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_11"
  ConfigValue: 400.0
  ConfigValue: 9999.0
  ConfigValue: 1.0
  ConfigValue: 2.0
  ConfigValue: 111.0
}
rows {
  ConfigName: "E3.NR3E3_VocationAbility_AIProxy.AIProxyLevelConfig_12"
  ConfigValue: 400.0
  ConfigValue: 9999.0
  ConfigValue: 1.0
  ConfigValue: 3.0
  ConfigValue: 112.0
}
rows {
  ConfigName: "E3.ActivityProp.Refresh.Time"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.ActivityProp.Refresh.Num"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.ThemeEvent"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.ThemeEvent.InitialMagicBoxCount"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.ThemeEvent.MaxMagicBoxCount"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.ThemeEvent.MeetingRefreshMagicBoxCount"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.ThemeEvent.MagicBoxInteractionRange"
  ConfigValue: 2.5
}
rows {
  ConfigName: "E3.ThemeEvent.PlayerIDVisibility"
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.Task.UrgentRoadLineAutoRefreshTime"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.Task.UrgentRoadLineAutoRefreshCount"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.StreetNPC"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor_StreetNPC.MaxSpawnIdleNPCNums"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor_StreetNPC.MaxSpawnMoveStreetNPCNums"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor_StreetNPC.MinSpawnMoveStreetNPCNums"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor_StreetNPC.MaxSpawnMoveCyclicStreetNPCTime"
  ConfigValue: 20.0
}
rows {
  ConfigName: "E3.Game.Ability.BP_GameAbility_SpawnLevelFeatureActor_StreetNPC.MinSpawnMoveCyclicStreetNPCTime"
  ConfigValue: 12.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Avengers_Attack.CoolDownDuration"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_Avengers_Attack.DetectedEnemyRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.floatParamConfig.ActiveAvengersAttackTaskNum"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.NaoNao.WinCompletedTimes"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_NaoNao_SetOffFirecracks.Duration"
  ConfigValue: 6.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_NaoNao_PlayOffFirecracks.Duration"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_NaoNao.CoolDownDuration"
  ConfigValue: 45.0
}
rows {
  ConfigName: "E3.floatParamConfig.FootprintHeight"
  ConfigValue: -90.0
}
rows {
  ConfigName: "E3.floatParamConfig.FootprintDuration"
  ConfigValue: 0.25
}
rows {
  ConfigName: "E3.floatParamConfig.FootprintDistance"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.LittleBone.MaxTasksCount"
  ConfigValue: 6.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Prophet.OnMeetingCheckWolfCount"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.Vocation.Ability.Prophet.CheckWolfCount"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.HyperCore"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.HyperCore.MercyExtensionTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.SheriffAttackCoolDown"
  ConfigValue: 0.5
}
rows {
  ConfigName: "E3.HyperCore.DetectExtensionTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.SchoralExtensionMissionProgress"
  ConfigValue: 0.3
}
rows {
  ConfigName: "E3.HyperCore.WarriorAttackReducedTaskNums"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.RangerSingleModeRange"
  ConfigValue: 0.7
}
rows {
  ConfigName: "E3.HyperCore.JudgeExtensionTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.PsychicSpeedUpMag"
  ConfigValue: 1.4
}
rows {
  ConfigName: "E3.HyperCore.EagleEyeRangeMag"
  ConfigValue: 1.4
}
rows {
  ConfigName: "E3.HyperCore.EagleEyeDurationMag"
  ConfigValue: 1.3
}
rows {
  ConfigName: "E3.HyperCore.TrackerDurationMag"
  ConfigValue: 1.34
}
rows {
  ConfigName: "E3.HyperCore.OristrichDurationMag"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.HyperCore.FirstAiderResuceTimeMag"
  ConfigValue: 1.3
}
rows {
  ConfigName: "E3.HyperCore.SwordsManAttackTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.AverageAttackReducedNums"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.KangarooPackageTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.ProphetTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.LittileBoneReducedTaskNums"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.HyperCore.TrickWolfDurationMag"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.HyperCore.AssassinWolfTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.BombWolfCoolDownMag"
  ConfigValue: 0.7
}
rows {
  ConfigName: "E3.HyperCore.SneakWolfDurationMag"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.HyperCore.StrongHunterCoolDownMag"
  ConfigValue: 0.5
}
rows {
  ConfigName: "E3.HyperCore.NuClearBombExtensionTimes"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.HyperCore.SealWolfExtensionTimes"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.HyperCore.XRayWolfCoolDownMag"
  ConfigValue: 0.77
}
rows {
  ConfigName: "E3.HyperCore.FlashWolfCoolDownMag"
  ConfigValue: 0.77
}
rows {
  ConfigName: "E3.HyperCore.MaskWolfCoolDownMag"
  ConfigValue: 0.77
}
rows {
  ConfigName: "E3.HyperCore.FogSneakSpeedUpMag"
  ConfigValue: 1.4
}
rows {
  ConfigName: "E3.HyperCore.DanceWolfDurationMag"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.HyperCore.BlackWolfDurationMag"
  ConfigValue: 1.33
}
rows {
  ConfigName: "E3.HyperCore.TrapWolfCoolDownMag"
  ConfigValue: 0.7
}
rows {
  ConfigName: "E3.HyperCore.ControlWolfDurationMag"
  ConfigValue: 1.5
}
rows {
  ConfigName: "E3.HyperCore.BountyHunterAttackCoolDown"
  ConfigValue: 0.8
}
rows {
  ConfigName: "E3.HyperCore.TrampPackageCoolDownMag"
  ConfigValue: 0.7
}
rows {
  ConfigName: "E3.HyperCore.DancerRangeMag"
  ConfigValue: 1.2
}
rows {
  ConfigName: "E3.HyperCore.DeliveryReducedNums"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.HyperCore.SwordsAttackCoolDownMag"
  ConfigValue: 1.67
}
rows {
  ConfigName: "E3.HyperCore.SmokeWolf_SmokeMag"
  ConfigValue: 1.4
}
rows {
  ConfigName: "E3.Vocation.Ability.XianZhi.MaxLevel"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.XianZhi.LevelUpNeedTaskAmount"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.Vocation.Ability.XianZhi.Level1CD"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.XianZhi.Level2CD"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.Vocation.Ability.XianZhi.RadarDuration"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_Ability_FogSneakWolf_Sneak.SneakFadeOutDuration"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.Vocation.Ability.LuckyStar.Gift.Book"
  ConfigValue: 0.2
}
rows {
  ConfigName: "E3.Vocation.Ability.LuckyStar.Range"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Treasure"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.TreasureHunt.SubmittedArt.AddScore"
  ConfigValue: 0.005
}
rows {
  ConfigName: "E3.TreasureHunt.SellTreasureWinGoal"
  ConfigValue: 1000.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.NR3E3_VocationAbility_CTH_TreasureCarry.CarryRange"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.NR3E3_VocationAbility_CTH_TreasureCarry.CarryInitCD"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.NR3E3_VocationAbility_CTH_TreasureCarry.CarryCDAfterDrop"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.VocationAbility_CTH_OpenTreasureBox.OpenCD"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.VocationAbility_CTH_OpenTreasureBox.ResponseRange"
  ConfigValue: 300.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C1.BirthCount"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C2.WalkSpeed"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C2.ChaseSpeed"
  ConfigValue: 70.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C3.AttackRange"
  ConfigValue: 2.5
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C4.SearchRange"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C4.SleepSearchRange"
  ConfigValue: 7.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.T1.ChaseTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C5.ReturnSpeed"
  ConfigValue: 90.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C6.SleepInitialProbability"
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C7.SleepRiseProbability"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.T2.SleepTime"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C6.AlertnessProbability"
  ConfigValue: 0.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ChickenMonster.C7.AlertnessRiseProbability"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vines.C8.Count"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vines.C9.Radius"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vines.T2.Cooldown"
  ConfigValue: 4.5
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vines.T2.VinesCooldown"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vines.ValidTimeBeforeCoolDown"
  ConfigValue: 4.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vines.T3.Duration"
  ConfigValue: 8.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ExcavationTeam.C10.Count"
  ConfigValue: 1.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ExcavationTeam.T4.Duration"
  ConfigValue: 120.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ExcavationTeam.BillboardGapMinTime"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ExcavationTeam.BillboardGapMaxTime"
  ConfigValue: 80.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.ExcavationTeam.BillboardDuration"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.SwampCount"
  ConfigValue: 3.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.SwampWalkSpeed"
  ConfigValue: 0.6
}
rows {
  ConfigName: "E3.UseScanner.perspecCD"
  ConfigValue: 60.0
}
rows {
  ConfigName: "E3.UseScanner.Radius"
  ConfigValue: 200.0
}
rows {
  ConfigName: "E3.UseScanner.Time"
  ConfigValue: 25.0
}
rows {
  ConfigName: "E3.UseScanner.PerspecRange"
  ConfigValue: 70.0
}
rows {
  ConfigName: "E3.UseScanner.InViewDis"
  ConfigValue: 50.0
}
rows {
  ConfigName: "E3.UseScanner.InUrgentTaskDis"
  ConfigValue: 10.0
}
rows {
  ConfigName: "E3.UseScanner.InSceneDis"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.HunterNPCMaxCount"
  ConfigValue: 2.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.HunterNPCMinTime"
  ConfigValue: 80.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.HunterNPCMaxTime"
  ConfigValue: 100.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.HunterNPCTreasureID"
  ConfigValue: 11.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.HunterNPCChatTime"
  ConfigValue: 5.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vender.BillboardGapMinTime"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.TreasureHunt.Vocation.Ability.Vender.BillboardGapMaxTime"
  ConfigValue: 40.0
}
rows {
  ConfigName: "E3.qita"
  ConfigValue: -1.0
}
rows {
  ConfigName: "E3.GameConfig.1010.GameSettlementConfig.GameDurationTime"
  ConfigValue: 1200.0
}
rows {
  ConfigName: "E3.GameConfig.1011.GameSettlementConfig.GameDurationTime"
  ConfigValue: 1200.0
}
rows {
  ConfigName: "E3.GameConfig.1012.GameSettlementConfig.GameDurationTime"
  ConfigValue: 1200.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_LieRen_Attack.DetectedEnemyRange"
  ConfigValue: 250.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_LieRen_Attack.CoolDownDuration"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_LieRen_Attack.CoolDownAfterMeeting"
  ConfigValue: 30.0
}
rows {
  ConfigName: "E3.Vocation.Ability.BP_VocationAbility_LieRen_Attack.CoolDownDurationAfterMeeting"
  ConfigValue: 15.0
}
rows {
  ConfigName: "E3.floatParamConfig.ActiveLieRenAttackTaskNum"
  ConfigValue: 5.0
}
