com.tencent.wea.xlsRes.table_TaskGroup
excel/xls/R_任务表_策划专用.xlsx sheet:任务组
rows {
  id: 1000
  type: TaskType_Daily
  taskIdList: 1019
  taskIdList: 1001
  taskIdList: 1002
  taskIdList: 1003
  taskIdList: 1004
  taskIdList: 1005
  taskIdList: 1006
  taskIdList: 1007
  taskIdList: 1008
  taskIdList: 1009
  taskIdList: 1010
  taskIdList: 1011
  taskIdList: 1012
  taskIdList: 1013
  taskIdList: 1014
  taskIdList: 1015
  taskIdList: 8001
  taskIdList: 8002
  taskIdList: 8003
  groupName: "日常任务"
}
rows {
  id: 1001
  type: TaskType_Daily
  taskIdList: 1101
  taskIdList: 1102
  taskIdList: 1103
  taskIdList: 1104
  taskIdList: 1105
  groupName: "日常活跃档位"
}
rows {
  id: 1100
  type: TaskType_Weekly
  taskIdList: 1021
  taskIdList: 1022
  taskIdList: 1023
  taskIdList: 1024
  taskIdList: 1025
  taskIdList: 1026
  taskIdList: 8004
  groupName: "周常任务"
}
rows {
  id: 1101
  type: TaskType_Weekly
  taskIdList: 2101
  taskIdList: 2102
  taskIdList: 2103
  groupName: "周常活跃档位"
}
rows {
  id: 1200
  type: TaskType_Common
  beginDoTime {
    seconds: 1697558400
  }
  endDoTime {
    seconds: 1730995199
  }
  taskIdList: 3001
  taskIdList: 3002
  taskIdList: 3003
  taskIdList: 3004
  taskIdList: 3005
  taskIdList: 3006
  taskIdList: 3007
  taskIdList: 3008
  taskIdList: 3009
  taskIdList: 3010
  taskIdList: 3011
  taskIdList: 3012
  taskIdList: 3013
  taskIdList: 3014
  taskIdList: 3015
  taskIdList: 3016
  taskIdList: 3017
  taskIdList: 3018
  taskIdList: 3019
  taskIdList: 3020
  taskIdList: 3021
  groupName: "新手任务"
  validDay: 30
}
rows {
  id: 1201
  type: TaskType_Common
  beginDoTime {
    seconds: 1697558400
  }
  endDoTime {
    seconds: 1730995199
  }
  taskIdList: 3101
  groupName: "新手任务额外奖励"
  validDay: 30
}
rows {
  id: 1202
  type: TaskType_Daily
  beginDoTime {
    seconds: 1697558400
  }
  endDoTime {
    seconds: 1730995199
  }
  taskIdList: 3201
  groupName: "新手任务每日奖励"
  validDay: 30
}
rows {
  id: 1300
  type: TaskType_Level
  endShowTime {
    seconds: 1724947200
  }
  endDoTime {
    seconds: 1724947200
  }
  taskIdList: 4001
  taskIdList: 4002
  taskIdList: 4003
  taskIdList: 4004
  taskIdList: 4005
  taskIdList: 4006
  taskIdList: 4007
  taskIdList: 4008
  taskIdList: 4009
  taskIdList: 4010
  taskIdList: 4011
  taskIdList: 4012
  taskIdList: 4013
  taskIdList: 4014
  taskIdList: 4015
  taskIdList: 4016
  taskIdList: 4017
  taskIdList: 4018
  taskIdList: 4019
  taskIdList: 4020
  taskIdList: 4021
  taskIdList: 4022
  taskIdList: 4023
  taskIdList: 4024
  taskIdList: 4025
  taskIdList: 4026
  taskIdList: 4027
  taskIdList: 4028
  taskIdList: 4029
  taskIdList: 4030
  groupName: "等级任务"
}
rows {
  id: 1402
  type: TaskType_UgcCreativeRoad
  taskIdList: 5201
  taskIdList: 5202
  taskIdList: 5203
  taskIdList: 5204
  taskIdList: 5205
  taskIdList: 5206
  taskIdList: 5207
  taskIdList: 5208
  taskIdList: 5209
  taskIdList: 5210
  taskIdList: 5211
  taskIdList: 5212
  taskIdList: 5213
  taskIdList: 5214
  taskIdList: 5215
  taskIdList: 5216
  taskIdList: 5217
  taskIdList: 5218
  taskIdList: 5219
  taskIdList: 5220
  taskIdList: 5221
  taskIdList: 5222
  taskIdList: 5223
  taskIdList: 5224
  taskIdList: 5225
  taskIdList: 5226
  taskIdList: 5227
  taskIdList: 5228
  taskIdList: 5229
  taskIdList: 5230
  groupName: "造梦之旅"
}
rows {
  id: 1403
  type: TaskType_UgcCreativeRoad
  beginDoTime {
    seconds: 1701360000
  }
  taskIdList: 9000
  taskIdList: 9001
  taskIdList: 9002
  taskIdList: 9003
  taskIdList: 9004
  groupName: "一颗星的诞生（阶段一）"
}
rows {
  id: 1404
  type: TaskType_Season
  beginDoTime {
    seconds: 1702569600
  }
  endDoTime {
    seconds: 1706198400
  }
  taskIdList: 5400
  taskIdList: 5401
  groupName: "造梦计划赛季激励"
}
rows {
  id: 1405
  type: TaskType_Season
  beginDoTime {
    seconds: 1701187200
  }
  endDoTime {
    seconds: 1709222399
  }
  taskIdList: 5410
  taskIdList: 5411
  taskIdList: 5412
  taskIdList: 5413
  taskIdList: 5414
  taskIdList: 5415
  groupName: "王者造梦赛"
}
rows {
  id: 1406
  type: TaskType_Season
  beginDoTime {
    seconds: 1701187200
  }
  endDoTime {
    seconds: 1709222399
  }
  taskIdList: 5420
  taskIdList: 5421
  taskIdList: 5422
  taskIdList: 5423
  taskIdList: 5424
  groupName: "星骑士来了"
}
rows {
  id: 1407
  type: TaskType_Season
  beginDoTime {
    seconds: 1701187200
  }
  endDoTime {
    seconds: 1709222399
  }
  taskIdList: 5430
  taskIdList: 5431
  groupName: "古风剧王"
}
rows {
  id: 1408
  type: TaskType_UgcCreativeRoad
  beginDoTime {
    seconds: 1701360000
  }
  taskIdList: 9005
  taskIdList: 9006
  taskIdList: 9007
  taskIdList: 9008
  taskIdList: 9009
  groupName: "追梦星途二阶段"
}
rows {
  id: 1409
  type: TaskType_UgcCreativeRoad
  beginDoTime {
    seconds: 1701360000
  }
  taskIdList: 9010
  taskIdList: 9011
  taskIdList: 9012
  taskIdList: 9013
  taskIdList: 9014
  groupName: "追梦星途三阶段"
}
rows {
  id: 1410
  type: TaskType_UgcCreativeRoad
  beginDoTime {
    seconds: 1701360000
  }
  taskIdList: 9015
  taskIdList: 9016
  taskIdList: 9017
  groupName: "追梦星途四阶段"
}
rows {
  id: 1450
  type: TaskType_Season
  beginDoTime {
    seconds: 1704297600
  }
  endDoTime {
    seconds: 1711814400
  }
  taskIdList: 5440
  taskIdList: 5441
  taskIdList: 5442
  taskIdList: 5443
  taskIdList: 5444
  taskIdList: 5445
  taskIdList: 5446
  groupName: "新春搭建赛"
}
rows {
  id: 1451
  type: TaskType_Season
  beginDoTime {
    seconds: 1704297600
  }
  endDoTime {
    seconds: 1711814400
  }
  taskIdList: 5450
  taskIdList: 5451
  taskIdList: 5452
  groupName: "企鹅新春赛"
}
rows {
  id: 1452
  type: TaskType_Season
  beginDoTime {
    seconds: 1704297600
  }
  endDoTime {
    seconds: 1714406400
  }
  taskIdList: 5460
  taskIdList: 5461
  taskIdList: 5462
  groupName: "红薯创作赛"
}
rows {
  id: 1601
  type: TaskType_Common
  taskIdList: 60002
  groupName: "染色任务"
}
rows {
  id: 1700
  type: TaskType_Daily
  taskIdList: 1
  taskIdList: 2
  taskIdList: 3
  taskIdList: 4
  taskIdList: 5
  groupName: "经典模式每日通关奖励"
}
rows {
  id: 1801
  type: TaskType_Common
  taskIdList: 2684
  taskIdList: 2685
  taskIdList: 2686
  taskIdList: 2687
  groupName: "NPC对话隐藏任务"
}
rows {
  id: 1900
  type: TaskType_Common
  taskIdList: 6000
  taskIdList: 6001
  taskIdList: 6002
  taskIdList: 6003
  taskIdList: 6004
  taskIdList: 6005
  taskIdList: 6006
  groupName: "回归每日签到任务"
}
rows {
  id: 1901
  type: TaskType_Common
  taskIdList: 6100
  taskIdList: 6101
  taskIdList: 6102
  taskIdList: 6103
  taskIdList: 6104
  groupName: "回归任务列表"
}
rows {
  id: 1902
  type: TaskType_Common
  taskIdList: 6200
  taskIdList: 6201
  taskIdList: 6202
  taskIdList: 6203
  taskIdList: 6204
  groupName: "回归任务进度"
}
rows {
  id: 1599
  type: TaskType_Common
  taskIdList: 55901
  taskIdList: 55903
  taskIdList: 55904
  taskIdList: 55902
  taskIdList: 55918
  taskIdList: 55919
  taskIdList: 55920
  taskIdList: 55905
  taskIdList: 55907
  taskIdList: 55908
  taskIdList: 55906
  taskIdList: 55921
  taskIdList: 55922
  taskIdList: 55923
  taskIdList: 55927
  taskIdList: 55928
  taskIdList: 55929
  taskIdList: 55930
  taskIdList: 55931
  taskIdList: 55932
  taskIdList: 55933
  taskIdList: 55909
  taskIdList: 55911
  taskIdList: 55912
  taskIdList: 55910
  taskIdList: 55934
  taskIdList: 55935
  taskIdList: 55936
  taskIdList: 55913
  taskIdList: 55915
  taskIdList: 55916
  taskIdList: 55914
  taskIdList: 55924
  taskIdList: 55925
  taskIdList: 55926
  taskIdList: 55917
  groupName: "亲密关系奖励任务组"
}
rows {
  id: 2001
  type: TaskType_Common
  taskIdList: 7000
  taskIdList: 7003
  taskIdList: 7004
  taskIdList: 7005
  taskIdList: 7006
  taskIdList: 7007
  taskIdList: 7008
  taskIdList: 7009
  taskIdList: 7010
  taskIdList: 7011
  taskIdList: 7012
  taskIdList: 7013
  taskIdList: 7014
  taskIdList: 7015
  taskIdList: 7016
  taskIdList: 7017
  taskIdList: 7018
  taskIdList: 7019
  taskIdList: 7020
  taskIdList: 7021
  taskIdList: 7022
  taskIdList: 7023
  taskIdList: 7024
  taskIdList: 7030
  taskIdList: 7031
  taskIdList: 7040
  taskIdList: 7041
  taskIdList: 7042
  taskIdList: 7043
  taskIdList: 7044
  taskIdList: 7045
  taskIdList: 7046
  taskIdList: 7047
  taskIdList: 7050
  taskIdList: 7051
  taskIdList: 7052
  taskIdList: 7054
  taskIdList: 7055
  taskIdList: 7056
  taskIdList: 7057
  groupName: "下载任务"
}
rows {
  id: 2002
  type: TaskType_Common
  beginShowTime {
    seconds: 1724947200
  }
  endShowTime {
    seconds: 1728921599
  }
  beginDoTime {
    seconds: 1724947200
  }
  endDoTime {
    seconds: 1728921599
  }
  taskIdList: 7100
  groupName: "s8赛季更新包下载"
}
rows {
  id: 2003
  type: TaskType_Common
  beginShowTime {
    seconds: 1729612800
  }
  endShowTime {
    seconds: 1733241599
  }
  beginDoTime {
    seconds: 1732032000
  }
  endDoTime {
    seconds: 1733241599
  }
  taskIdList: 7101
  groupName: "s9赛季更新包下载"
}
rows {
  id: 2004
  type: TaskType_Common
  beginShowTime {
    seconds: 1733932800
  }
  endShowTime {
    seconds: 1737388799
  }
  beginDoTime {
    seconds: 1733932800
  }
  endDoTime {
    seconds: 1737388799
  }
  taskIdList: 7102
  groupName: "s10赛季更新包下载"
}
rows {
  id: 2005
  type: TaskType_Common
  beginShowTime {
    seconds: 1739980800
  }
  endShowTime {
    seconds: 1742227199
  }
  beginDoTime {
    seconds: 1739980800
  }
  endDoTime {
    seconds: 1742227199
  }
  taskIdList: 7103
  groupName: "s11赛季更新包下载"
}
rows {
  id: 10000
  type: TaskType_Season
  beginShowTime {
    seconds: 1685894400
  }
  endShowTime {
    seconds: 1706198399
  }
  beginDoTime {
    seconds: 1687622400
  }
  endDoTime {
    seconds: 1706198399
  }
  taskIdList: 50021
  taskIdList: 50022
  taskIdList: 50023
  taskIdList: 50024
  taskIdList: 50025
  taskIdList: 50026
  taskIdList: 50027
  taskIdList: 50028
  taskIdList: 50029
  taskIdList: 50030
  groupName: "第1赛季冲段任务"
}
rows {
  id: 10001
  type: TaskType_Season
  beginShowTime {
    seconds: 1685894400
  }
  endShowTime {
    seconds: 1703779199
  }
  beginDoTime {
    seconds: 1687622400
  }
  endDoTime {
    seconds: 1703779199
  }
  groupName: "第1赛季历练值获取任务"
}
rows {
  id: 10002
  type: TaskType_Season
  beginShowTime {
    seconds: 1685894400
  }
  endShowTime {
    seconds: 1703779199
  }
  beginDoTime {
    seconds: 1687622400
  }
  endDoTime {
    seconds: 1703779199
  }
  groupName: "第1赛季历练值档位"
}
rows {
  id: 10003
  type: TaskType_Season
  beginShowTime {
    seconds: 1706198400
  }
  endShowTime {
    seconds: 1710431999
  }
  beginDoTime {
    seconds: 1706198400
  }
  endDoTime {
    seconds: 1710431999
  }
  taskIdList: 50031
  taskIdList: 50032
  taskIdList: 50033
  taskIdList: 50034
  taskIdList: 50035
  taskIdList: 50036
  taskIdList: 50037
  taskIdList: 50038
  taskIdList: 50039
  taskIdList: 50040
  groupName: "第2赛季冲段任务"
}
rows {
  id: 10004
  type: TaskType_Season
  beginShowTime {
    seconds: 1710432000
  }
  endShowTime {
    seconds: 1714060799
  }
  beginDoTime {
    seconds: 1710432000
  }
  endDoTime {
    seconds: 1714060799
  }
  taskIdList: 50041
  taskIdList: 50042
  taskIdList: 50043
  taskIdList: 50044
  taskIdList: 50045
  taskIdList: 50046
  taskIdList: 50047
  taskIdList: 50048
  taskIdList: 50049
  taskIdList: 50050
  groupName: "第3赛季冲段任务"
}
rows {
  id: 10005
  type: TaskType_Season
  beginShowTime {
    seconds: 1714060800
  }
  endShowTime {
    seconds: 1717689599
  }
  beginDoTime {
    seconds: 1714060800
  }
  endDoTime {
    seconds: 1717689599
  }
  taskIdList: 50051
  taskIdList: 50052
  taskIdList: 50053
  taskIdList: 50054
  taskIdList: 50055
  taskIdList: 50056
  taskIdList: 50057
  taskIdList: 50058
  taskIdList: 50059
  taskIdList: 50060
  taskIdList: 50061
  groupName: "第4赛季冲段任务"
}
rows {
  id: 10006
  type: TaskType_Season
  beginShowTime {
    seconds: 1717689600
  }
  endShowTime {
    seconds: 1721318399
  }
  beginDoTime {
    seconds: 1717689600
  }
  endDoTime {
    seconds: 1721318399
  }
  taskIdList: 50071
  taskIdList: 50072
  taskIdList: 50073
  taskIdList: 50074
  taskIdList: 50075
  taskIdList: 50076
  taskIdList: 50077
  taskIdList: 50078
  taskIdList: 50079
  taskIdList: 50080
  taskIdList: 50081
  groupName: "第5赛季冲段任务"
}
rows {
  id: 10007
  type: TaskType_Season
  beginShowTime {
    seconds: 1721318400
  }
  endShowTime {
    seconds: 1724947199
  }
  beginDoTime {
    seconds: 1721318400
  }
  endDoTime {
    seconds: 1724947199
  }
  taskIdList: 50091
  taskIdList: 50092
  taskIdList: 50093
  taskIdList: 50094
  taskIdList: 50095
  taskIdList: 50096
  taskIdList: 50097
  taskIdList: 50098
  taskIdList: 50099
  taskIdList: 50100
  taskIdList: 50101
  groupName: "第6赛季冲段任务"
}
rows {
  id: 10008
  type: TaskType_Season
  beginShowTime {
    seconds: 1724947200
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1724947200
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 50111
  taskIdList: 50112
  taskIdList: 50113
  taskIdList: 50114
  taskIdList: 50115
  taskIdList: 50116
  taskIdList: 50117
  taskIdList: 50118
  taskIdList: 50119
  taskIdList: 50120
  taskIdList: 50121
  groupName: "第7赛季冲段任务"
}
rows {
  id: 10009
  type: TaskType_Season
  beginShowTime {
    seconds: 1729180800
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1729180800
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 50131
  taskIdList: 50132
  taskIdList: 50133
  taskIdList: 50134
  taskIdList: 50135
  taskIdList: 50136
  taskIdList: 50137
  taskIdList: 50138
  taskIdList: 50139
  taskIdList: 50140
  taskIdList: 50141
  groupName: "第8赛季冲段任务"
}
rows {
  id: 10010
  type: TaskType_Season
  beginShowTime {
    seconds: 1732809600
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1732809600
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 50151
  taskIdList: 50152
  taskIdList: 50153
  taskIdList: 50154
  taskIdList: 50155
  taskIdList: 50156
  taskIdList: 50157
  taskIdList: 50158
  taskIdList: 50159
  taskIdList: 50160
  taskIdList: 50161
  taskIdList: 50162
  taskIdList: 50163
  groupName: "第9赛季冲段任务"
}
rows {
  id: 10011
  type: TaskType_Season
  beginShowTime {
    seconds: 1737043200
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1737043200
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 50171
  taskIdList: 50172
  taskIdList: 50173
  taskIdList: 50174
  taskIdList: 50175
  taskIdList: 50176
  taskIdList: 50177
  taskIdList: 50178
  taskIdList: 50179
  taskIdList: 50180
  taskIdList: 50181
  taskIdList: 50182
  taskIdList: 50183
  groupName: "第10赛季冲段任务"
}
rows {
  id: 10012
  type: TaskType_Season
  beginShowTime {
    seconds: 1741881600
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1741881600
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 50191
  taskIdList: 50192
  taskIdList: 50193
  taskIdList: 50194
  taskIdList: 50195
  taskIdList: 50196
  taskIdList: 50197
  taskIdList: 50198
  taskIdList: 50199
  taskIdList: 50200
  taskIdList: 50201
  taskIdList: 50202
  taskIdList: 50203
  groupName: "第11赛季冲段任务"
}
rows {
  id: 11081
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1717689600
  }
  endShowTime {
    seconds: 1718294399
  }
  beginDoTime {
    seconds: 1717689600
  }
  endDoTime {
    seconds: 1718294399
  }
  taskIdList: 70121
  taskIdList: 70122
  groupName: "一 · 星海节初遇"
}
rows {
  id: 11082
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1718294400
  }
  endShowTime {
    seconds: 1718899199
  }
  beginDoTime {
    seconds: 1718294400
  }
  endDoTime {
    seconds: 1718899199
  }
  taskIdList: 70123
  taskIdList: 70124
  groupName: "二 · 永明崖传说"
}
rows {
  id: 11083
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1718899200
  }
  endShowTime {
    seconds: 1719503999
  }
  beginDoTime {
    seconds: 1718899200
  }
  endDoTime {
    seconds: 1719503999
  }
  taskIdList: 70125
  taskIdList: 70126
  groupName: "三 · 误入人鱼城"
}
rows {
  id: 11084
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1719504000
  }
  endShowTime {
    seconds: 1720108799
  }
  beginDoTime {
    seconds: 1719504000
  }
  endDoTime {
    seconds: 1720108799
  }
  taskIdList: 70127
  taskIdList: 70128
  groupName: "四 ·破解时空迷"
}
rows {
  id: 11085
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1720108800
  }
  endShowTime {
    seconds: 1720713599
  }
  beginDoTime {
    seconds: 1720108800
  }
  endDoTime {
    seconds: 1720713599
  }
  taskIdList: 70129
  taskIdList: 70130
  groupName: "五 · 目标：星海岛"
}
rows {
  id: 11086
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1720713600
  }
  endShowTime {
    seconds: 1721318399
  }
  beginDoTime {
    seconds: 1720713600
  }
  endDoTime {
    seconds: 1721318399
  }
  taskIdList: 70131
  taskIdList: 70132
  groupName: "六 · 永远的友谊"
}
rows {
  id: 11087
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1717689600
  }
  endShowTime {
    seconds: 1718294399
  }
  beginDoTime {
    seconds: 1717689600
  }
  endDoTime {
    seconds: 1718294399
  }
  taskIdList: 70133
  taskIdList: 70134
  groupName: "一 · 星海节初遇"
}
rows {
  id: 11088
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1718294400
  }
  endShowTime {
    seconds: 1718899199
  }
  beginDoTime {
    seconds: 1718294400
  }
  endDoTime {
    seconds: 1718899199
  }
  taskIdList: 70135
  taskIdList: 70136
  groupName: "二 · 永明崖传说"
}
rows {
  id: 11089
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1718899200
  }
  endShowTime {
    seconds: 1719503999
  }
  beginDoTime {
    seconds: 1718899200
  }
  endDoTime {
    seconds: 1719503999
  }
  taskIdList: 70137
  taskIdList: 70138
  groupName: "三 · 误入人鱼城"
}
rows {
  id: 11090
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1719504000
  }
  endShowTime {
    seconds: 1720108799
  }
  beginDoTime {
    seconds: 1719504000
  }
  endDoTime {
    seconds: 1720108799
  }
  taskIdList: 70139
  taskIdList: 70140
  groupName: "四 ·破解时空迷"
}
rows {
  id: 11091
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1720108800
  }
  endShowTime {
    seconds: 1720713599
  }
  beginDoTime {
    seconds: 1720108800
  }
  endDoTime {
    seconds: 1720713599
  }
  taskIdList: 70141
  taskIdList: 70142
  groupName: "五 · 目标：星海岛"
}
rows {
  id: 11092
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1720713600
  }
  endShowTime {
    seconds: 1721318399
  }
  beginDoTime {
    seconds: 1720713600
  }
  endDoTime {
    seconds: 1721318399
  }
  taskIdList: 70143
  taskIdList: 70144
  groupName: "六 · 永远的友谊"
}
rows {
  id: 11093
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1721318400
  }
  endShowTime {
    seconds: 1721923199
  }
  beginDoTime {
    seconds: 1721318400
  }
  endDoTime {
    seconds: 1721923199
  }
  groupName: "一 · 前往甜心乐园"
}
rows {
  id: 11094
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1721923200
  }
  endShowTime {
    seconds: 1722527999
  }
  beginDoTime {
    seconds: 1721923200
  }
  endDoTime {
    seconds: 1722527999
  }
  groupName: "二 · 奇妙魔术表演"
}
rows {
  id: 11095
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1722528000
  }
  endShowTime {
    seconds: 1723132799
  }
  beginDoTime {
    seconds: 1722528000
  }
  endDoTime {
    seconds: 1723132799
  }
  groupName: "三 · 被调包的点心"
}
rows {
  id: 11096
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1723132800
  }
  endShowTime {
    seconds: 1723737599
  }
  beginDoTime {
    seconds: 1723132800
  }
  endDoTime {
    seconds: 1723737599
  }
  groupName: "四 · 黑烟入侵乐园"
}
rows {
  id: 11097
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1723737600
  }
  endShowTime {
    seconds: 1724342399
  }
  beginDoTime {
    seconds: 1723737600
  }
  endDoTime {
    seconds: 1724342399
  }
  groupName: "五 ·真相水落石出"
}
rows {
  id: 11098
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1724256000
  }
  endShowTime {
    seconds: 1724947199
  }
  beginDoTime {
    seconds: 1724256000
  }
  endDoTime {
    seconds: 1724947199
  }
  groupName: "六 · 最甜的是友谊"
}
rows {
  id: 11099
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1721318400
  }
  endShowTime {
    seconds: 1721923199
  }
  beginDoTime {
    seconds: 1721318400
  }
  endDoTime {
    seconds: 1721923199
  }
  groupName: "一 · 前往甜心乐园"
}
rows {
  id: 11100
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1721923200
  }
  endShowTime {
    seconds: 1722527999
  }
  beginDoTime {
    seconds: 1721923200
  }
  endDoTime {
    seconds: 1722527999
  }
  groupName: "二 · 奇妙魔术表演"
}
rows {
  id: 11101
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1722528000
  }
  endShowTime {
    seconds: 1723132799
  }
  beginDoTime {
    seconds: 1722528000
  }
  endDoTime {
    seconds: 1723132799
  }
  groupName: "三 · 被调包的点心"
}
rows {
  id: 11102
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1723132800
  }
  endShowTime {
    seconds: 1723737599
  }
  beginDoTime {
    seconds: 1723132800
  }
  endDoTime {
    seconds: 1723737599
  }
  groupName: "四 · 黑烟入侵乐园"
}
rows {
  id: 11103
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1723737600
  }
  endShowTime {
    seconds: 1724342399
  }
  beginDoTime {
    seconds: 1723737600
  }
  endDoTime {
    seconds: 1724342399
  }
  groupName: "五 ·真相水落石出"
}
rows {
  id: 11104
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1724256000
  }
  endShowTime {
    seconds: 1724947199
  }
  beginDoTime {
    seconds: 1724256000
  }
  endDoTime {
    seconds: 1724947199
  }
  groupName: "六 · 最甜的是友谊"
}
rows {
  id: 11105
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1724947200
  }
  endShowTime {
    seconds: 1725551999
  }
  beginDoTime {
    seconds: 1724947200
  }
  endDoTime {
    seconds: 1725551999
  }
  groupName: "一 ·  精灵之森"
}
rows {
  id: 11106
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1725552000
  }
  endShowTime {
    seconds: 1726156799
  }
  beginDoTime {
    seconds: 1725552000
  }
  endDoTime {
    seconds: 1726156799
  }
  groupName: "二 · 重建森林"
}
rows {
  id: 11107
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1726156800
  }
  endShowTime {
    seconds: 1726761599
  }
  beginDoTime {
    seconds: 1726156800
  }
  endDoTime {
    seconds: 1726761599
  }
  groupName: "三 · 风暴再起"
}
rows {
  id: 11108
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1726761600
  }
  endShowTime {
    seconds: 1727366399
  }
  beginDoTime {
    seconds: 1726761600
  }
  endDoTime {
    seconds: 1727366399
  }
  groupName: "四 · 保卫战"
}
rows {
  id: 11109
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1727366400
  }
  endShowTime {
    seconds: 1727971199
  }
  beginDoTime {
    seconds: 1727366400
  }
  endDoTime {
    seconds: 1727971199
  }
  groupName: "五 ·甜点的奇迹"
}
rows {
  id: 11110
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1727971200
  }
  endShowTime {
    seconds: 1728575999
  }
  beginDoTime {
    seconds: 1727971200
  }
  endDoTime {
    seconds: 1728575999
  }
  groupName: "六 · 治愈之力"
}
rows {
  id: 11117
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1728576000
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1728576000
  }
  endDoTime {
    seconds: 1729180799
  }
  groupName: "七 ·新的家园"
}
rows {
  id: 11111
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1724947200
  }
  endShowTime {
    seconds: 1725551999
  }
  beginDoTime {
    seconds: 1724947200
  }
  endDoTime {
    seconds: 1725551999
  }
  groupName: "一 ·  精灵之森"
}
rows {
  id: 11112
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1725552000
  }
  endShowTime {
    seconds: 1726156799
  }
  beginDoTime {
    seconds: 1725552000
  }
  endDoTime {
    seconds: 1726156799
  }
  groupName: "二 · 重建森林"
}
rows {
  id: 11113
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1726156800
  }
  endShowTime {
    seconds: 1726761599
  }
  beginDoTime {
    seconds: 1726156800
  }
  endDoTime {
    seconds: 1726761599
  }
  groupName: "三 · 风暴再起"
}
rows {
  id: 11114
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1726761600
  }
  endShowTime {
    seconds: 1727366399
  }
  beginDoTime {
    seconds: 1726761600
  }
  endDoTime {
    seconds: 1727366399
  }
  groupName: "四 · 保卫战"
}
rows {
  id: 11115
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1727366400
  }
  endShowTime {
    seconds: 1727971199
  }
  beginDoTime {
    seconds: 1727366400
  }
  endDoTime {
    seconds: 1727971199
  }
  groupName: "五 ·甜点的奇迹"
}
rows {
  id: 11116
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1727971200
  }
  endShowTime {
    seconds: 1728575999
  }
  beginDoTime {
    seconds: 1727971200
  }
  endDoTime {
    seconds: 1728575999
  }
  groupName: "六 · 治愈之力"
}
rows {
  id: 11118
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1728576000
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1728576000
  }
  endDoTime {
    seconds: 1729180799
  }
  groupName: "七 ·新的家园"
}
rows {
  id: 11119
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1729180800
  }
  endShowTime {
    seconds: 1729785599
  }
  beginDoTime {
    seconds: 1729180800
  }
  endDoTime {
    seconds: 1729785599
  }
  groupName: "一 ·  星愿博物馆"
}
rows {
  id: 11120
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1729785600
  }
  endShowTime {
    seconds: 1730390399
  }
  beginDoTime {
    seconds: 1729785600
  }
  endDoTime {
    seconds: 1730390399
  }
  groupName: "二 ·好心办坏事"
}
rows {
  id: 11121
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1730390400
  }
  endShowTime {
    seconds: 1730995199
  }
  beginDoTime {
    seconds: 1730390400
  }
  endDoTime {
    seconds: 1730995199
  }
  groupName: "三 · 有一只猫猫"
}
rows {
  id: 11122
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1730995200
  }
  endShowTime {
    seconds: 1731599999
  }
  beginDoTime {
    seconds: 1730995200
  }
  endDoTime {
    seconds: 1731599999
  }
  groupName: "四 ·橙黄色的梦"
}
rows {
  id: 11123
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1731600000
  }
  endShowTime {
    seconds: 1732204799
  }
  beginDoTime {
    seconds: 1731600000
  }
  endDoTime {
    seconds: 1732204799
  }
  groupName: "五 ·天蓝色的梦"
}
rows {
  id: 11124
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1732204800
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1732204800
  }
  endDoTime {
    seconds: 1732809599
  }
  groupName: "六 · 梦魇在奔涌"
}
rows {
  id: 11126
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1729180800
  }
  endShowTime {
    seconds: 1729785599
  }
  beginDoTime {
    seconds: 1729180800
  }
  endDoTime {
    seconds: 1729785599
  }
  groupName: "一 ·  星愿博物馆"
}
rows {
  id: 11127
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1729785600
  }
  endShowTime {
    seconds: 1730390399
  }
  beginDoTime {
    seconds: 1729785600
  }
  endDoTime {
    seconds: 1730390399
  }
  groupName: "二 ·好心办坏事"
}
rows {
  id: 11128
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1730390400
  }
  endShowTime {
    seconds: 1730995199
  }
  beginDoTime {
    seconds: 1730390400
  }
  endDoTime {
    seconds: 1730995199
  }
  groupName: "三 · 有一只猫猫"
}
rows {
  id: 11129
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1730995200
  }
  endShowTime {
    seconds: 1731599999
  }
  beginDoTime {
    seconds: 1730995200
  }
  endDoTime {
    seconds: 1731599999
  }
  groupName: "四 ·橙黄色的梦"
}
rows {
  id: 11130
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1731600000
  }
  endShowTime {
    seconds: 1732204799
  }
  beginDoTime {
    seconds: 1731600000
  }
  endDoTime {
    seconds: 1732204799
  }
  groupName: "五 ·天蓝色的梦"
}
rows {
  id: 11131
  type: TaskType_SeasonStage
  beginShowTime {
    seconds: 1732204800
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1732204800
  }
  endDoTime {
    seconds: 1732809599
  }
  groupName: "六 · 梦魇在奔涌"
}
rows {
  id: 11132
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1732809600
  }
  endShowTime {
    seconds: 1733414399
  }
  beginDoTime {
    seconds: 1732809600
  }
  endDoTime {
    seconds: 1733414399
  }
  groupName: "一 ·  不能说的秘方"
}
rows {
  id: 11133
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1733414400
  }
  endShowTime {
    seconds: 1734019199
  }
  beginDoTime {
    seconds: 1733414400
  }
  endDoTime {
    seconds: 1734019199
  }
  groupName: "二 · 神秘的大奖"
}
rows {
  id: 11134
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1734019200
  }
  endShowTime {
    seconds: 1734623999
  }
  beginDoTime {
    seconds: 1734019200
  }
  endDoTime {
    seconds: 1734623999
  }
  groupName: "三 · 欢乐挑战赛"
}
rows {
  id: 11135
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1734624000
  }
  endShowTime {
    seconds: 1735228799
  }
  beginDoTime {
    seconds: 1734624000
  }
  endDoTime {
    seconds: 1735228799
  }
  groupName: "四 · 混乱组队赛"
}
rows {
  id: 11136
  type: TaskType_SeasonStageDaily
  beginShowTime {
    seconds: 1735228800
  }
  endShowTime {
    seconds: 1735833599
  }
  beginDoTime {
    seconds: 1735228800
  }
  endDoTime {
    seconds: 1735833599
  }
  groupName: "五 · 巧思藏铭牌"
}
