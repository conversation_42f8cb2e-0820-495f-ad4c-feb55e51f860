com.tencent.wea.xlsRes.table_RaffleLuckyValueData
excel/xls/C_抽奖奖池_yujun.xlsx sheet:幸运值配置
rows {
  id: 3000801
  poolId: 30008
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 3000
  groupWeight: 700
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 38300
}
rows {
  id: 3000802
  poolId: 30008
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1200
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24800
}
rows {
  id: 3000803
  poolId: 30008
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 6000
  groupWeight: 2400
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 26600
}
rows {
  id: 3000804
  poolId: 30008
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 3000805
  poolId: 30008
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000806
  poolId: 30008
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000807
  poolId: 30008
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3000808
  poolId: 30008
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000809
  poolId: 30008
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000810
  poolId: 30008
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000811
  poolId: 30008
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 504501
  poolId: 5045
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 504502
  poolId: 5045
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 504503
  poolId: 5045
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
}
rows {
  id: 504504
  poolId: 5045
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 504505
  poolId: 5045
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 504506
  poolId: 5045
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 504601
  poolId: 5046
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 504602
  poolId: 5046
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 504603
  poolId: 5046
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
}
rows {
  id: 504604
  poolId: 5046
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 504605
  poolId: 5046
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 504606
  poolId: 5046
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 504701
  poolId: 5047
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 504702
  poolId: 5047
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 504703
  poolId: 5047
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
}
rows {
  id: 504704
  poolId: 5047
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 504705
  poolId: 5047
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 504706
  poolId: 5047
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 504801
  poolId: 5048
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 504802
  poolId: 5048
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 504803
  poolId: 5048
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
}
rows {
  id: 504804
  poolId: 5048
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 504805
  poolId: 5048
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 504806
  poolId: 5048
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 512401
  poolId: 51240
  lvThreshold: 2
  groupWeight: 4
  groupWeight: 996
}
rows {
  id: 512402
  poolId: 51240
  lvThreshold: 3
  groupWeight: 17
  groupWeight: 983
}
rows {
  id: 512403
  poolId: 51240
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 512404
  poolId: 51241
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 512405
  poolId: 51242
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512406
  poolId: 51243
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512407
  poolId: 51244
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512501
  poolId: 51250
  lvThreshold: 2
  groupWeight: 4
  groupWeight: 996
}
rows {
  id: 512502
  poolId: 51250
  lvThreshold: 3
  groupWeight: 17
  groupWeight: 983
}
rows {
  id: 512503
  poolId: 51250
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 512504
  poolId: 51251
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 512505
  poolId: 51252
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512506
  poolId: 51253
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512507
  poolId: 51254
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 3000901
  poolId: 30009
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 600
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 38300
}
rows {
  id: 3000902
  poolId: 30009
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24800
}
rows {
  id: 3000903
  poolId: 30009
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 6000
  groupWeight: 1500
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 26600
}
rows {
  id: 3000904
  poolId: 30009
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 3000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 25000
}
rows {
  id: 3000905
  poolId: 30009
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000906
  poolId: 30009
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000907
  poolId: 30009
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3000908
  poolId: 30009
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000909
  poolId: 30009
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000910
  poolId: 30009
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000911
  poolId: 30009
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 5360001
  poolId: 5360
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360002
  poolId: 5360
  lvThreshold: 15
  groupWeight: 2
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360003
  poolId: 5360
  lvThreshold: 20
  groupWeight: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360004
  poolId: 5360
  lvThreshold: 25
  groupWeight: 4
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360005
  poolId: 5360
  lvThreshold: 26
  groupWeight: 5
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360006
  poolId: 5360
  lvThreshold: 27
  groupWeight: 10
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360007
  poolId: 5360
  lvThreshold: 28
  groupWeight: 15
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360008
  poolId: 5360
  lvThreshold: 29
  groupWeight: 20
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360009
  poolId: 5360
  lvThreshold: 30
  groupWeight: 25
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360010
  poolId: 5360
  lvThreshold: 31
  groupWeight: 700
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360011
  poolId: 5360
  lvThreshold: 32
  groupWeight: 1000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360012
  poolId: 5360
  lvThreshold: 33
  groupWeight: 2000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360013
  poolId: 5360
  lvThreshold: 34
  groupWeight: 3000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5360014
  poolId: 5360
  lvThreshold: 35
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 3001001
  poolId: 30010
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 600
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 38300
}
rows {
  id: 3001002
  poolId: 30010
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24800
}
rows {
  id: 3001003
  poolId: 30010
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 6000
  groupWeight: 1500
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 26600
}
rows {
  id: 3001004
  poolId: 30010
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 3000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 25000
}
rows {
  id: 3001005
  poolId: 30010
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001006
  poolId: 30010
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001007
  poolId: 30010
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3001008
  poolId: 30010
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001009
  poolId: 30010
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001010
  poolId: 30010
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001011
  poolId: 30010
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 80001001
  poolId: 8000101
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 70
}
rows {
  id: 80001002
  poolId: 8000102
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 80001003
  poolId: 8000104
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80001004
  poolId: 8000105
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80001005
  poolId: 8000106
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80001006
  poolId: 8000107
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80001007
  poolId: 8000108
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80001008
  poolId: 8000109
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80001009
  poolId: 8000110
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 3001101
  poolId: 30011
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 600
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 38300
}
rows {
  id: 3001102
  poolId: 30011
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24800
}
rows {
  id: 3001103
  poolId: 30011
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 6000
  groupWeight: 1500
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 26600
}
rows {
  id: 3001104
  poolId: 30011
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 3000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 25000
}
rows {
  id: 3001105
  poolId: 30011
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001106
  poolId: 30011
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001107
  poolId: 30011
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3001108
  poolId: 30011
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001109
  poolId: 30011
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001110
  poolId: 30011
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001111
  poolId: 30011
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 5361001
  poolId: 5361
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361002
  poolId: 5361
  lvThreshold: 15
  groupWeight: 2
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361003
  poolId: 5361
  lvThreshold: 20
  groupWeight: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361004
  poolId: 5361
  lvThreshold: 25
  groupWeight: 4
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361005
  poolId: 5361
  lvThreshold: 26
  groupWeight: 5
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361006
  poolId: 5361
  lvThreshold: 27
  groupWeight: 10
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361007
  poolId: 5361
  lvThreshold: 28
  groupWeight: 15
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361008
  poolId: 5361
  lvThreshold: 29
  groupWeight: 20
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361009
  poolId: 5361
  lvThreshold: 30
  groupWeight: 25
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361010
  poolId: 5361
  lvThreshold: 31
  groupWeight: 700
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361011
  poolId: 5361
  lvThreshold: 32
  groupWeight: 1000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361012
  poolId: 5361
  lvThreshold: 33
  groupWeight: 2000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361013
  poolId: 5361
  lvThreshold: 34
  groupWeight: 3000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5361014
  poolId: 5361
  lvThreshold: 35
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 3001201
  poolId: 30012
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 600
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 38300
}
rows {
  id: 3001202
  poolId: 30012
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24800
}
rows {
  id: 3001203
  poolId: 30012
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 6000
  groupWeight: 1500
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 26600
}
rows {
  id: 3001204
  poolId: 30012
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 3000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 25000
}
rows {
  id: 3001205
  poolId: 30012
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001206
  poolId: 30012
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001207
  poolId: 30012
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3001208
  poolId: 30012
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001209
  poolId: 30012
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001210
  poolId: 30012
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001211
  poolId: 30012
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001301
  poolId: 30013
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 600
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 38300
}
rows {
  id: 3001302
  poolId: 30013
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24800
}
rows {
  id: 3001303
  poolId: 30013
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 6000
  groupWeight: 1500
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 26600
}
rows {
  id: 3001304
  poolId: 30013
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 3000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 25000
}
rows {
  id: 3001305
  poolId: 30013
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001306
  poolId: 30013
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001307
  poolId: 30013
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3001308
  poolId: 30013
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001309
  poolId: 30013
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001310
  poolId: 30013
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001311
  poolId: 30013
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 512601
  poolId: 51260
  lvThreshold: 2
  groupWeight: 4
  groupWeight: 996
}
rows {
  id: 512602
  poolId: 51260
  lvThreshold: 3
  groupWeight: 17
  groupWeight: 983
}
rows {
  id: 512603
  poolId: 51260
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 512604
  poolId: 51261
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512605
  poolId: 51262
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512606
  poolId: 51263
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512607
  poolId: 51264
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512701
  poolId: 51270
  lvThreshold: 2
  groupWeight: 4
  groupWeight: 996
}
rows {
  id: 512702
  poolId: 51270
  lvThreshold: 3
  groupWeight: 17
  groupWeight: 983
}
rows {
  id: 512703
  poolId: 51270
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 512704
  poolId: 51271
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512705
  poolId: 51272
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512706
  poolId: 51273
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512707
  poolId: 51274
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512801
  poolId: 51280
  lvThreshold: 2
  groupWeight: 4
  groupWeight: 996
}
rows {
  id: 512802
  poolId: 51280
  lvThreshold: 3
  groupWeight: 17
  groupWeight: 983
}
rows {
  id: 512803
  poolId: 51280
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 512804
  poolId: 51281
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 512805
  poolId: 51282
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512806
  poolId: 51283
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512807
  poolId: 51284
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512901
  poolId: 51290
  lvThreshold: 2
  groupWeight: 4
  groupWeight: 996
}
rows {
  id: 512902
  poolId: 51290
  lvThreshold: 3
  groupWeight: 17
  groupWeight: 983
}
rows {
  id: 512903
  poolId: 51290
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 512904
  poolId: 51291
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 512905
  poolId: 51292
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512906
  poolId: 51293
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 512907
  poolId: 51294
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 40000078
  poolId: 40000015
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 61685
  groupWeight: 500
  groupWeight: 28315
}
rows {
  id: 40000079
  poolId: 40000015
  lvThreshold: 240
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 21685
  groupWeight: 500
  groupWeight: 68315
}
rows {
  id: 3001401
  poolId: 30014
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 600
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 38300
}
rows {
  id: 3001402
  poolId: 30014
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24800
}
rows {
  id: 3001403
  poolId: 30014
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 6000
  groupWeight: 1500
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 26600
}
rows {
  id: 3001404
  poolId: 30014
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 3000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 25000
}
rows {
  id: 3001405
  poolId: 30014
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001406
  poolId: 30014
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001407
  poolId: 30014
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3001408
  poolId: 30014
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001409
  poolId: 30014
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3001410
  poolId: 30014
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3001411
  poolId: 30014
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 80006001
  poolId: 8000601
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 70
}
rows {
  id: 80006002
  poolId: 8000602
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 80006003
  poolId: 8000604
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80006004
  poolId: 8000605
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80006005
  poolId: 8000606
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80006006
  poolId: 8000607
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80006007
  poolId: 8000608
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80006008
  poolId: 8000609
  lvThreshold: 100
  groupWeight: 100
}
rows {
  id: 80006009
  poolId: 8000610
  lvThreshold: 100
  groupWeight: 100
}
