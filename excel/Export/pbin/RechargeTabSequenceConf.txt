com.tencent.wea.xlsRes.table_ResRechargeTabSequenceConf
excel/xls/C_充值配置表.xlsx sheet:页签顺序
rows {
  seq: 3
  name: "一元购背包"
  uiName: "UI_LuckBuy_MainView_Penguin"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowLuckBuy"
  getShowHelpRuleId: "GetLuckBuyHelpRuleId"
  redDotStyle: 0
  redDotType: 79
  id: 1
}
rows {
  seq: 4
  name: "首充"
  uiName: "UI_Recharge_FirstChargeSubView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowFirstCharge"
  getShowHelpRuleId: "GetFirstChargeHelpRuleId"
  redDotStyle: 0
  redDotType: 43
  currencyCfg: "1"
  id: 2
}
rows {
  seq: 5
  name: "金秋充值送"
  uiName: "UI_ChargeRebate_Main"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowChargeRebateMain"
  redDotStyle: 0
  redDotType: 52
  currencyCfg: "1"
  id: 3
}
rows {
  seq: 6
  name: "充值返利"
  uiName: "UI_SecondChargeRebate_Main"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowSecondChargeRebateMain"
  redDotStyle: 0
  redDotType: 130
  currencyCfg: "1"
  id: 4
}
rows {
  seq: 7
  name: "星宝会员"
  uiName: "UI_Recharge_MonthCard"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowMonthCardRecharge"
  getShowHelpRuleId: "GetMonthCardHelpRuleId"
  redDotStyle: 0
  redDotType: 50
  currencyCfg: "1"
  id: 5
}
rows {
  seq: 8
  name: "特惠礼包"
  uiName: "UI_Recharge_CutGift"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowCutGift"
  redDotStyle: 0
  redDotType: 47
  currencyCfg: "1"
  id: 6
}
rows {
  seq: 9
  name: "星愿招财猫"
  uiName: "UI_Recharge_SeasonRecharge"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowSeasonRecharge"
  redDotStyle: 0
  redDotType: 45
  currencyCfg: "1,2"
  id: 7
}
rows {
  seq: 10
  name: "充值"
  uiName: "UI_Recharge_Recharge"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsPermanentShow"
  redDotStyle: 0
  redDotType: 37
  currencyCfg: "1"
  id: 8
}
rows {
  seq: 11
  name: "不删档返利"
  uiName: "UI_ChargeRebate_OpenBetaRebate"
  viewTag: "UI_Recharge_MainView"
  visible: 0
  openFun: "IsShowOpenBetaRebate"
  getShowHelpRuleId: "GetOpenBetaRebateHelpRuleId"
  redDotStyle: 0
  currencyCfg: "1"
  id: 9
}
rows {
  seq: 12
  name: "一元幸启"
  uiName: "UI_LuckBuy_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 0
  openFun: "IsShowLuckBuy"
  getShowHelpRuleId: "GetLuckBuyHelpRuleId"
  redDotStyle: 0
  redDotType: 79
  id: 10
}
rows {
  seq: 13
  name: "春节刮刮卡"
  uiName: "UI_Activity_ScratchTicket_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 0
  openFun: "IsShowScratchTicket"
  getShowHelpRuleId: "GetScratchTicketHelpRuleId"
  redDotStyle: 0
  redDotType: 99
  id: 11
}
rows {
  seq: 14
  name: "春季回馈"
  uiName: "UI_Recharge_OpeningRebate"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowOpeningRebate"
  getShowHelpRuleId: "GetOpeningRebateHelpRuleId"
  redDotStyle: 0
  redDotType: 101
  currencyCfg: "1"
  id: 12
}
rows {
  seq: 2
  name: "噗噗登录礼"
  uiName: "UI_OMD_BattlePass_Reward"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowUpgradeCheckInManual"
  redDotStyle: 0
  redDotType: 133
  currencyCfg: "2,3"
  id: 13
}
rows {
  seq: 15
  name: "福利导航"
  uiName: "UI_Recharge_Navigation"
  viewTag: "UI_Recharge_MainView"
  visible: 0
  openFun: "IsShowRechargeNavigationView"
  redDotStyle: 0
  id: 14
}
rows {
  seq: 16
  name: "超核"
  uiName: "UI_Recharge_JumpView"
  viewTag: "UI_Recharge_MainView"
  visible: 0
  openFun: "IsShowHypernucleiJumpView"
  redDotStyle: 0
  activityId: 10005
  id: 15
}
rows {
  seq: 17
  name: "超核常驻"
  uiName: "UI_Recharge_SuperCoreCollect"
  viewTag: "UI_Recharge_MainView"
  visible: 0
  openFun: "IsShowHypernucleiCollectView"
  redDotStyle: 0
  activityId: 10006
  id: 16
}
rows {
  seq: 1
  name: "充值送星梭"
  uiName: "UI_Recharge_ShuttleView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowShuttleView"
  redDotStyle: 0
  redDotType: 217
  activityId: 10060
  currencyCfg: "1"
  id: 17
}
rows {
  seq: 1
  name: "充值送狡狡龙"
  uiName: "UI_Recharge_BlueOutfit"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowBlueOutfit"
  getShowHelpRuleId: "GetBlueOutfitRuleId"
  redDotStyle: 0
  activityId: 10054
  currencyCfg: "1"
  id: 18
}
rows {
  seq: 9
  name: "燎原之心"
  uiName: "UI_Arena_Mall_HotAvatarBuy"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowAvatarBuy"
  redDotStyle: 0
  redDotType: 236
  activityId: 10019
  currencyCfg: "3541,1"
  id: 21
}
rows {
  seq: 1
  name: "周末幸运星"
  uiName: "UI_WeekendLuckyStar_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowLuckyStarView"
  getShowHelpRuleId: "GetWeekendLuckyStarHelpRuleId"
  redDotStyle: 0
  redDotType: 172
  activityId: 10020
  id: 22
}
rows {
  seq: 3
  name: "赛季预购"
  uiName: "UI_ValuePreorder_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowSeasonPreOrder"
  redDotStyle: 0
  redDotType: 175
  activityId: 10021
  id: 23
}
rows {
  seq: 9
  name: "和平守望"
  uiName: "UI_Arena_Mall_HotAvatarBuy"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowAvatarBuy"
  redDotStyle: 0
  redDotType: 236
  activityId: 10025
  currencyCfg: "3541,1"
  id: 25
}
rows {
  seq: 2
  name: "满减大福利"
  uiName: "UI_Recharge_Werewolf_Promotion"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowWerewolfPromotion"
  redDotType: 239
  activityId: 10066
  currencyCfg: "1"
  id: 26
}
rows {
  seq: 2
  name: "幸运转盘"
  uiName: "UI_Recharge_LuckyTurntable"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowLuckTurntable"
  getShowHelpRuleId: "GetLuckyTurntableHelpRuleId"
  redDotStyle: 0
  redDotType: 173
  activityId: 10027
  currencyCfg: "1,3"
  id: 20
}
rows {
  seq: 1
  name: "冰雪赐福"
  uiName: "UI_SnowBless_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowBlueOutfit"
  getShowHelpRuleId: "GetBlueOutfitRuleId"
  redDotStyle: 0
  activityId: 10028
  currencyCfg: "1"
  id: 27
}
rows {
  seq: 1
  name: "赛季预购"
  uiName: "UI_ValuePreorder02_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowSeasonPreOrder"
  redDotStyle: 0
  redDotType: 175
  activityId: 10031
  id: 28
}
rows {
  seq: 1
  name: "赛季预购"
  uiName: "UI_ValuePreorder03_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowSeasonPreOrder"
  redDotStyle: 0
  redDotType: 175
  activityId: 10040
  id: 29
}
rows {
  seq: 2
  name: "幸运转盘"
  uiName: "UI_Recharge_LuckyTurntable"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowLuckTurntable"
  getShowHelpRuleId: "GetLuckyTurntableHelpRuleId"
  redDotStyle: 0
  redDotType: 173
  activityId: 10043
  currencyCfg: "1,3"
  id: 30
}
rows {
  seq: 1
  name: "赛季预购"
  uiName: "UI_ValuePreorder04_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowSeasonPreOrder"
  redDotStyle: 0
  redDotType: 175
  activityId: 10050
  id: 31
}
rows {
  seq: 1
  name: "赛季预购"
  uiName: "UI_ValuePreorder05_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowSeasonPreOrder"
  redDotStyle: 0
  redDotType: 175
  activityId: 10055
  id: 32
}
rows {
  seq: 1
  name: "膨胀爆红包"
  uiName: "UI_InflateHongBao_MainView"
  viewTag: "UI_Recharge_MainView"
  visible: 1
  openFun: "IsShowInflateHongBao"
  redDotStyle: 0
  activityId: 10059
  id: 33
}
