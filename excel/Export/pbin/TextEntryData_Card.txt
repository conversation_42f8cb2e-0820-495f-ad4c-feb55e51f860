com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/K_卡牌.xlsx sheet:文本表_卡牌
rows {
  content: "卡牌集换"
  switch: 1
  stringId: "Card_Main_Name"
}
rows {
  content: "卡牌集换"
  switch: 1
  stringId: "Card_Main_Title"
}
rows {
  content: "全卡册\n集齐奖励"
  switch: 1
  stringId: "Card_Collection_All_Reward"
}
rows {
  content: "收集奖杯奖励"
  switch: 1
  stringId: "Card_Collection_Cup"
}
rows {
  content: "查看"
  switch: 1
  stringId: "Card_Main_Deck_Button_Name"
}
rows {
  content: "至少{0}张{1}星"
  switch: 1
  stringId: "Card_Package_Des"
}
rows {
  content: "卡牌赠送"
  switch: 1
  stringId: "Card_Main_Give_Button_Name"
}
rows {
  content: "置换记录"
  switch: 1
  stringId: "Card_Main_History_Button_Name"
}
rows {
  content: "集齐卡册\n获得奖励"
  switch: 1
  stringId: "Card_Collection_Deck_Reward"
}
rows {
  content: "XXXXXX待补充，支持富文本"
  switch: 1
  stringId: "Card_Main_Help"
}
rows {
  content: "请帮我得到这张卡牌！"
  switch: 1
  stringId: "Card_Request_Base"
}
rows {
  content: "索要中"
  switch: 1
  stringId: "Card_Request_Ing_Self"
}
rows {
  content: "<Highlight24>{玩家名}</>\n赠予"
  switch: 1
  stringId: "Card_Request_Player_Give_Self"
}
rows {
  content: "已过期"
  switch: 1
  stringId: "Card_Request_TimeOut_Self"
}
rows {
  content: "赠送"
  switch: 1
  stringId: "Card_Request_Answer_Other"
}
rows {
  content: "暂无多余卡牌"
  switch: 1
  stringId: "Card_Request_Count_Less_Other"
}
rows {
  content: "<Highlight24>{0}</>\n赠予"
  switch: 1
  stringId: "Card_Request_Player_Give_Other"
}
rows {
  content: "已过期"
  switch: 1
  stringId: "Card_Request_TimeOut_Other"
}
rows {
  content: "我来赠送卡牌啦！速速领取！"
  switch: 1
  stringId: "Card_Give_Base"
}
rows {
  content: "赠送中"
  switch: 1
  stringId: "Card_Give_Ing_Self"
}
rows {
  content: "已领取"
  switch: 1
  stringId: "Card_Give_Player_Get_Self"
}
rows {
  content: "已过期"
  switch: 1
  stringId: "Card_Give_TimeOut_Self"
}
rows {
  content: "领取"
  switch: 1
  stringId: "Card_Give_Answer_Other"
}
rows {
  content: "已赠送"
  switch: 1
  stringId: "Card_Give_Player_Get_Other"
}
rows {
  content: "已过期"
  switch: 1
  stringId: "Card_Give_TimeOut_Other"
}
rows {
  content: "我帮你完成了赠送卡牌请求！"
  switch: 1
  stringId: "Card_Give_Other_Self"
}
rows {
  content: "我帮你完成了索要卡牌请求！"
  switch: 1
  stringId: "Card_Request_Other_Self"
}
rows {
  content: "谢谢你赠送的卡牌！"
  switch: 1
  stringId: "Card_Chat_Give_Other_Self"
}
rows {
  content: "谢谢你赠送的卡牌！"
  switch: 1
  stringId: "Card_Chat_Reqeust_Self_Other"
}
rows {
  content: "确认要向<FZLT32Orange>{0}</>索要<FZLT32Orange>{1}</>卡牌吗？"
  switch: 1
  stringId: "Card_Confirm_Request_Single_Player"
}
rows {
  content: "确认要向<FZLT32Orange>{0}</>赠送<FZLT32Orange>{1}</>卡牌吗？"
  switch: 1
  stringId: "Card_Confirm_Give_Players"
}
rows {
  content: "确认要向<FZLT32Orange>卡牌世界频道</>索要<FZLT32Orange>{0}</>卡牌吗？"
  switch: 1
  stringId: "Card_Confirm_Request_Chat"
}
rows {
  content: "确认要向<FZLT32Orange>卡牌世界频道</>赠送<FZLT32Orange>{0}</>卡牌吗？"
  switch: 1
  stringId: "Card_Confirm_Give_Chat"
}
rows {
  content: "赠送"
  switch: 1
  stringId: "Card_Confirm_Button_Give_Name"
}
rows {
  content: "索要"
  switch: 1
  stringId: "Card_Confirm_Button_Request_Name"
}
rows {
  content: "等待对方同意"
  switch: 1
  stringId: "Card_History_Give_WaitAnswer_Other"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Give_Chat"
}
rows {
  content: "领取"
  switch: 1
  stringId: "Card_History_Give_WaitAnswer_Self"
}
rows {
  content: "赠送"
  switch: 1
  stringId: "Card_History_Request_WaitAnswer_Other"
}
rows {
  content: "索要中"
  switch: 1
  stringId: "Card_History_Request_WaitAnswer_Self"
}
rows {
  content: "已过期"
  switch: 1
  stringId: "Card_History_Give_TimeOut"
}
rows {
  content: "已过期"
  switch: 1
  stringId: "Card_History_Request_TimeOut"
}
rows {
  content: "拥有{0}张及以上的卡牌才可赠送，今日已赠送张数{1}/{2}"
  switch: 1
  stringId: "Card_Give_Pop_Des"
}
rows {
  content: "你已拥有此卡牌，无法索要"
  switch: 1
  stringId: "Card_Tips_Request_Limit"
}
rows {
  content: "你卡牌数量不足{0}张，无法赠送"
  switch: 1
  stringId: "Card_Tips_Give_Limit"
}
rows {
  content: "一次最多只能向{0}个好友索要"
  switch: 1
  stringId: "Card_Tips_Request_Friend_Limit"
}
rows {
  content: "今日仅能赠送{0}张卡牌，已赠送张数:{1}"
  switch: 1
  stringId: "Card_Tips_Give_Max"
}
rows {
  content: "已赠送{0}/{1}"
  switch: 1
  stringId: "Card_Chat_Give_Limit"
}
rows {
  content: "此金色卡牌当前无法进行索要/赠送"
  switch: 1
  stringId: "Card_Preview_Glod"
}
rows {
  content: "赠送"
  switch: 1
  stringId: "Card_Preview_Give_Button_Name"
}
rows {
  content: "(今日已赠送{0})"
  switch: 1
  stringId: "Card_Preview_Give_Button_Count"
}
rows {
  content: "索要"
  switch: 1
  stringId: "Card_Preview_Request_Button_Name"
}
rows {
  content: "选择赠送渠道"
  switch: 1
  stringId: "Card_Channel_Title_Give"
}
rows {
  content: "选择索要渠道"
  switch: 1
  stringId: "Card_Channel_Title_Request"
}
rows {
  content: "卡牌世界频道"
  switch: 1
  stringId: "Card_Channel_Title_Chat"
}
rows {
  content: "游戏好友"
  switch: 1
  stringId: "Card_Channel_Title_Friend"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Request_Private_Angain_Button"
}
rows {
  content: "我向该玩家发起的卡牌索要请求进行中\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Request_Private_Angain"
}
rows {
  content: "赠送"
  switch: 1
  stringId: "Card_History_Request_Private_Other_Angain_Button"
}
rows {
  content: "该玩家向我发起的卡牌索要请求进行中\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Request_Private_Other_Angain"
}
rows {
  content: "我向该玩家发起的卡牌索要请求已完成"
  switch: 1
  stringId: "Card_History_Request_Private_Over"
}
rows {
  content: "该玩家向我发起的卡牌索要请求已完成"
  switch: 1
  stringId: "Card_History_Request_Private_Other_Over"
}
rows {
  content: "我向该玩家发起的卡牌索要请求已过期"
  switch: 1
  stringId: "Card_History_Request_Private_TimeOut"
}
rows {
  content: "该玩家向我发起的卡牌索要请求已过期"
  switch: 1
  stringId: "Card_History_Request_Private_Other_TimeOut"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Request_Chat_Angain_Button"
}
rows {
  content: "我正在向卡牌世界频道索要卡牌\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Request_Chat_Angain"
}
rows {
  content: "我向卡牌世界频道索要了一张卡牌，已由该玩家赠送给我"
  switch: 1
  stringId: "Card_History_Request_Chat_Over"
}
rows {
  content: "我向卡牌世界频道发起的卡牌索要请求已过期"
  switch: 1
  stringId: "Card_History_Request_Chat_TimeOut"
}
rows {
  content: "该玩家向卡牌世界频道发起的卡牌索要请求，已由我赠送"
  switch: 1
  stringId: "Card_History_Request_Chat_Answer_Over"
}
rows {
  content: "等待玩家领取"
  switch: 1
  stringId: "Card_History_Give_Private_Angain_Button"
}
rows {
  content: "我向该玩家发起的卡牌赠送请求进行中\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Give_Private_Angain"
}
rows {
  content: "领取"
  switch: 1
  stringId: "Card_History_Give_Private_Other_Angain_Button"
}
rows {
  content: "该玩家向我发起的卡牌赠送请求进行中\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Give_Private_Other_Angain"
}
rows {
  content: "我向该玩家发起的卡牌赠送请求已完成"
  switch: 1
  stringId: "Card_History_Give_Private_Over"
}
rows {
  content: "该玩家向我发起的卡牌赠送请求已完成"
  switch: 1
  stringId: "Card_History_Give_Private_Other_Over"
}
rows {
  content: "我向该玩家发起的卡牌赠送请求已过期"
  switch: 1
  stringId: "Card_History_Give_Private_TimeOut"
}
rows {
  content: "该玩家向我发起的卡牌赠送请求已过期"
  switch: 1
  stringId: "Card_History_Give_Private_Other_TimeOut"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Give_Chat_Angain_Button"
}
rows {
  content: "我正在向卡牌世界频道送出卡牌\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Give_Chat_Angain"
}
rows {
  content: "我向卡牌世界频道赠送的卡牌已由该玩家领取"
  switch: 1
  stringId: "Card_History_Give_Chat_Over"
}
rows {
  content: "我向卡牌世界频道发起的卡牌赠送请求已过期"
  switch: 1
  stringId: "Card_History_Give_Chat_TimeOut"
}
rows {
  content: "该玩家向卡牌世界频道赠送的卡牌已被我领取"
  switch: 1
  stringId: "Card_History_Give_Chat_Answer_Over"
}
rows {
  content: "赠送记录"
  switch: 1
  stringId: "Card_History_Give_Title"
}
rows {
  content: "索要记录"
  switch: 1
  stringId: "Card_History_Request_Title"
}
rows {
  content: "已选星宝:<CallPlayerLobby>{0}</>/{1}"
  switch: 1
  stringId: "Card_Friend_Select"
}
rows {
  content: "每次索要冷却间隔<CallPlayerLobby>{0}</>小时"
  switch: 1
  stringId: "Card_Friend_Time"
}
rows {
  content: "普通卡牌"
  switch: 1
  stringId: "Card_Chat_Request_Color_Blue"
}
rows {
  content: "金色卡牌"
  switch: 1
  stringId: "Card_Chat_Request_Color_Gold"
}
rows {
  content: "{1}卡册集齐"
  switch: 1
  stringId: "Card_GetCollection_Deck"
}
rows {
  content: "{0}全卡册集齐"
  switch: 1
  stringId: "Card_GetCollection_Set"
}
rows {
  content: "索要卡牌"
  switch: 1
  stringId: "Card_Friend_Request_Title"
}
rows {
  content: "恭喜获得以下奖励"
  switch: 1
  stringId: "Card_GetCollection_RewardTitle"
}
rows {
  content: "本周上限"
  switch: 1
  stringId: "Card_Circle_Limit_Des"
}
rows {
  content: "交换记录"
  switch: 1
  stringId: "Card_History_Exchange_Title"
}
rows {
  content: "我向这些玩家发起的卡牌交换请求进行中\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Exchange_Private_Self_To_Others_Again"
}
rows {
  content: "我向该玩家发起的卡牌交换请求进行中\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Exchange_Private_Self_To_Other_Again"
}
rows {
  content: "该玩家向我发起的卡牌交换请求进行中\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Exchange_Private_Other_To_Me_Again"
}
rows {
  content: "该玩家与我的卡牌交换请求已完成"
  switch: 1
  stringId: "Card_History_Exchange_Private_Other_To_Me_Over"
}
rows {
  content: "该请求已被其他玩家抢先完成！"
  switch: 1
  stringId: "Card_History_Exchange_Private_Other_To_Me_ByOne_Over"
}
rows {
  content: "我向这些玩家发起的卡牌交换请求已完成"
  switch: 1
  stringId: "Card_History_Exchange_Private_Me_To_Others_Over"
}
