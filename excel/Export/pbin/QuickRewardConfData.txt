com.tencent.wea.xlsRes.table_QuickRewardConf
excel/xls/Y_一键领奖.xlsx sheet:一键领奖
rows {
  id: 2
  rewardType: QRT_SEASON
  name: "赛季任务"
  icon: "T_Lobby_Img_Season"
  enable: true
  iconText: "赛季"
}
rows {
  id: 3
  rewardType: QRT_PERMIT
  name: "通行证任务"
  icon: "T_Lobby_Img_BattlePass"
  enable: true
  iconText: "通行证"
}
rows {
  id: 4
  rewardType: QRT_TASK
  idList: 1000
  idList: 1001
  name: "每日打卡"
  icon: "T_Lobby_Bg_TaskBtn"
  enable: true
  iconText: "任务"
  priority: 2
}
rows {
  id: 5
  rewardType: QRT_TASK
  idList: 1100
  idList: 1101
  name: "每周巡游"
  icon: "T_Lobby_Bg_TaskBtn"
  enable: true
  iconText: "任务"
  priority: 2
}
rows {
  id: 7
  rewardType: QRT_MONTH_CARD
  name: "星宝会员卡"
  icon: "T_Lobby_Bg_RecahrgeBtn"
  enable: true
  iconText: "热购"
  priority: 2
}
rows {
  id: 8
  rewardType: QRT_MALL
  idList: 120001
  name: "每日福利"
  icon: "T_Lobby_Bg_RecahrgeBtn"
  enable: true
  iconText: "热购"
}
rows {
  id: 9
  rewardType: QRT_PERMIT_LEVEL
  name: "通行证等级奖励"
  icon: "T_Lobby_Img_BattlePass"
  enable: true
  iconText: "通行证"
  priority: 1
}
rows {
  id: 10
  rewardType: QRT_TASK
  idList: 1700
  name: "每日通关奖励"
  icon: "T_Lobby_Bg_TaskBtn"
  enable: true
  iconText: "任务"
  priority: 2
}
rows {
  id: 11
  rewardType: QRT_ACTIVITY
  idList: 6
  name: "七日签到"
  icon: "T_Lobby_Bg_FoundBtn"
  enable: true
  iconText: "发现"
}
rows {
  id: 12
  rewardType: QRT_TASK
  idList: 51773
  name: "定点补给站"
  icon: "T_Lobby_Bg_FoundBtn"
  enable: true
  iconText: "发现"
  priority: 2
}
rows {
  id: 13
  rewardType: QRT_TASK
  idList: 1300
  name: "成长之路"
  icon: "T_Lobby_Bg_TaskBtn"
  enable: true
  iconText: "任务"
  priority: 2
}
rows {
  id: 14
  rewardType: QRT_TASK
  idList: 200001
  idList: 200002
  idList: 200003
  idList: 200004
  idList: 300001
  idList: 400001
  idList: 400002
  idList: 400003
  idList: 400004
  idList: 400005
  idList: 400006
  idList: 400007
  idList: 400008
  idList: 400009
  idList: 400010
  idList: 400011
  idList: 400012
  idList: 400013
  idList: 400014
  idList: 400015
  idList: 400016
  idList: 400017
  idList: 400018
  idList: 400019
  idList: 400020
  idList: 400021
  idList: 400022
  idList: 400023
  idList: 400024
  idList: 400025
  idList: 400026
  idList: 400027
  idList: 400028
  idList: 400029
  idList: 400030
  idList: 400031
  idList: 400032
  idList: 400033
  idList: 400034
  idList: 400035
  idList: 400036
  idList: 400037
  idList: 400038
  idList: 400039
  idList: 400040
  idList: 400041
  idList: 400042
  idList: 400043
  idList: 400044
  idList: 400045
  idList: 400046
  idList: 400047
  idList: 400048
  idList: 400049
  idList: 400050
  idList: 400051
  idList: 400052
  idList: 400053
  idList: 400054
  idList: 400055
  idList: 400056
  idList: 400057
  idList: 400058
  idList: 400059
  idList: 400060
  idList: 400061
  idList: 400062
  idList: 400063
  idList: 400064
  idList: 400065
  idList: 400066
  name: "奖杯征程"
  icon: "T_Lobby_Bg_StarCupBtn"
  enable: true
  iconText: "奖杯"
  priority: 2
}
rows {
  id: 15
  rewardType: QRT_TASK
  idList: 9104
  idList: 9105
  idList: 9106
  idList: 9108
  idList: 9109
  idList: 91100
  idList: 91101
  idList: 91102
  name: "每日奖杯挑战"
  icon: "T_Lobby_Bg_StarCupBtn"
  enable: true
  iconText: "奖杯"
  priority: 2
}
rows {
  id: 16
  rewardType: QRT_TASK
  idList: 46001
  idList: 46002
  idList: 46003
  idList: 46004
  idList: 46005
  idList: 46006
  idList: 46007
  idList: 46008
  idList: 44081
  idList: 44082
  idList: 44083
  idList: 44084
  idList: 44085
  idList: 44086
  idList: 44087
  idList: 44088
  idList: 44070
  idList: 44071
  idList: 44072
  idList: 44073
  idList: 44074
  idList: 44075
  idList: 44076
  idList: 44077
  idList: 44078
  idList: 44069
  idList: 44061
  idList: 44062
  idList: 44063
  idList: 44064
  idList: 44065
  idList: 44066
  idList: 44067
  idList: 44068
  name: "娱乐排位赛"
  icon: "T_Lobby_Bg_FoundBtn"
  enable: true
  iconText: "发现"
  priority: 2
}
rows {
  id: 17
  rewardType: QRT_BP
  idList: 3
  name: "峡谷通行证任务"
  icon: "T_BattlePassS4_Img_More"
  enable: true
  iconText: "峡谷通行证"
  priority: 2
  bpType: BT_Moba
}
rows {
  id: 18
  rewardType: QRT_BP
  idList: 2
  name: "狼人通行证任务"
  icon: "T_BattlePassS4_Img_More"
  enable: true
  iconText: "狼人通行证"
  priority: 2
  bpType: BT_Werewolf
}
rows {
  id: 19
  rewardType: QRT_BP_LEVEL
  idList: 3
  name: "峡谷通行证奖励"
  icon: "T_BattlePassS4_Img_More"
  enable: true
  iconText: "峡谷通行证"
  priority: 1
  bpType: BT_Moba
}
rows {
  id: 20
  rewardType: QRT_BP_LEVEL
  idList: 2
  name: "狼人通行证奖励"
  icon: "T_BattlePassS4_Img_More"
  enable: true
  iconText: "狼人通行证"
  priority: 1
  bpType: BT_Werewolf
}
rows {
  id: 21
  rewardType: QRT_BP
  idList: 1
  name: "赛季通行证任务"
  icon: "T_BattlePassS4_Img_More"
  enable: true
  iconText: "赛季通行证"
  priority: 2
  bpType: BT_Main
}
rows {
  id: 22
  rewardType: QRT_BP_LEVEL
  idList: 1
  name: "赛季通行证奖励"
  icon: "T_BattlePassS4_Img_More"
  enable: true
  iconText: "赛季通行证"
  priority: 1
  bpType: BT_Main
}
