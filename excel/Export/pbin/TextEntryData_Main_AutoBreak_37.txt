com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置.xlsx sheet:文本配置
rows {
  content: "<HeroName>糖果女巫 艾露温</>可作为峡谷模式中<HeroName>小乔</>的皮肤使用"
  switch: 1
  stringId: "DrawReward_WitchText"
}
rows {
  content: "作者已禁止当前地图使用载具，使用失败"
  switch: 1
  stringId: "UGC_Vehicle_Forbidden"
}
rows {
  content: "房主已禁止当前家园使用载具，使用失败"
  switch: 1
  stringId: "Home_Vehicle_Forbidden"
}
rows {
  content: "自制枪械设置"
  switch: 1
  stringId: "UI_FPSSetting_CustomWeapon_Title"
}
rows {
  content: "请稍候再试"
  switch: 1
  stringId: "Player_Firework_Use_Max_Tips"
}
rows {
  content: "不支持扫码功能，请使用移动端"
  switch: 1
  stringId: "UI_PC_QRScan_Not_Support_Hint"
}
rows {
  content: "场景名称不能为空！"
  switch: 1
  stringId: "UGC_MultiScene_Rename_Empty_Tips"
}
rows {
  content: "新建场景成功，出生点信息已继承"
  switch: 1
  stringId: "UGC_MultiScene_Add_SubScene_Success_Tips"
}
rows {
  content: "当前名称已占用，请修改后重试！"
  switch: 1
  stringId: "UGC_MultiScene_Rename_Occupied_Tips"
}
rows {
  content: "确定消耗{0}{1}，将{2}的{3}升级为{4}？"
  switch: 1
  stringId: "Mall_Gift_Notice04"
}
rows {
  content: "对方已拥有高级通行证，无法赠送"
  switch: 1
  stringId: "Mall_Gift_Tips01"
}
rows {
  content: "对方已拥有豪华通行证，无法赠送"
  switch: 1
  stringId: "Mall_Gift_Tips02"
}
rows {
  content: "{0}天{1}小时后结束"
  switch: 1
  stringId: "UI_SevenDayCheckIn_RemainingTime"
}
rows {
  content: "开启设置后，局内会显示玩家的鼠标指针"
  switch: 1
  stringId: "SettingTip_ShowMouseTip"
}
rows {
  content: "在局内可使用\"~键\"即时切换鼠标显示，显示后鼠标默认置于游戏界面中间，局内鼠标设置会同步到游戏设置里"
  switch: 1
  stringId: "SettingTip_ShowMouseShortcutTip"
}
rows {
  content: "正在分享中…"
  switch: 1
  stringId: "Sharing_Tips"
}
rows {
  content: "(已领取)"
  switch: 1
  stringId: "UI_ConnanCard_FullRewardGeted"
}
rows {
  content: "(可领取)"
  switch: 1
  stringId: "UI_ConnanCard_FullRewardCanGet"
}
rows {
  content: "解锁全部角色徽章可领取"
  switch: 1
  stringId: "UI_ConnanCard_FullReward_Normal"
}
rows {
  content: "请选择要赠送的徽章"
  switch: 1
  stringId: "UI_ConnanCard_SelectSendCard"
}
rows {
  content: "今天赠送次数已用完"
  switch: 1
  stringId: "UI_ConnanCard_SendCardNumEmpty"
}
rows {
  content: "数量达到2个才能赠送哦"
  switch: 1
  stringId: "UI_ConnanCard_CardSendLimitNum"
}
rows {
  content: "%能获取："
  switch: 1
  stringId: "UI_ConnanCard_ClusterRandom"
}
rows {
  content: "彩虹鞋"
  switch: 1
  stringId: "Prop_RainBow_Name"
}
rows {
  content: "火箭鸭"
  switch: 1
  stringId: "Prop_DuckRocket_Name"
}
rows {
  content: "神偷手"
  switch: 1
  stringId: "AB_Steal_Name"
}
rows {
  content: "反弹护盾"
  switch: 1
  stringId: "AB_BounceBack_Name"
}
rows {
  content: "这个护盾被攻击就会爆炸！"
  switch: 1
  stringId: "AB_BounceBack_Tips"
}
rows {
  content: "这个护盾被攻击就会爆炸！"
  switch: 1
  stringId: "AB_BounceBack_MoreTips"
}
rows {
  content: "偷取敌人的道具！"
  switch: 1
  stringId: "AB_Steal_Tips"
}
rows {
  content: "欢迎使用智能编辑助手，可以告诉我想做的编辑操作（批量对元件选中、缩放、旋转、排列），我还可以生成模组和元件，如何使用智能编辑助手可以查看<a style=\"AIAssistantChatPurple\" mark=\"showhelpview\">图文教程</>哦"
  switch: 1
  stringId: "UGC_AiAssistant_Welcome01"
}
rows {
  content: "当前状态无法打开军械库哦"
  switch: 1
  stringId: "CAN_NOT_OPEN_ARMORY_BY_ATTR"
}
rows {
  content: "加载玩法中，<MantelCardCount01>{0}</>已从资源清理列表中移除"
  switch: 1
  stringId: "Pak_AutoRemoveCleanUpTip"
}
rows {
  content: "<MantelCardCount>{0}</>处于清理列表中，是否移除清理状态？\n"
  switch: 1
  stringId: "Pak_RemoveCleanupTip3"
}
rows {
  content: "玩家{0}已经拥有{1}，无法赠送"
  switch: 1
  stringId: "Mall_Gift_Tips03"
}
rows {
  content: "玩法已切换为{0}，快点击左上角开始吧"
  switch: 1
  stringId: "SimpleModelChangeMatch"
}
rows {
  content: "小游戏暂不支持，请下载元梦之星app体验"
  switch: 1
  stringId: "CLOUD_TO_NATIVE_TIPS_MOBILE"
}
rows {
  content: "当前版本暂不支持，请下载元梦之星app体验"
  switch: 1
  stringId: "CLOUD_TO_NATIVE_TIPS_PC"
}
rows {
  content: "使用投掷物子弹时，弹片数量不能超过5"
  switch: 1
  stringId: "UGC_FPS_WEAPON_OneShotFragmentNum_Limit"
}
rows {
  content: "长按星梭，贴地飞行！"
  switch: 1
  stringId: "Prop_ShuttleNew_Tips"
}
rows {
  content: "长按星梭，贴地飞行，松开按钮，飞上天空！"
  switch: 1
  stringId: "Prop_ShuttleNew_MoreTips"
}
rows {
  content: "蛋糕"
  switch: 1
  stringId: "Prop_Cake_Name"
}
rows {
  content: "击中对手可糊住对方视线！"
  switch: 1
  stringId: "Prop_Cake_Tips"
}
rows {
  content: "宝宝奶昔不足，当前不可赠送，是否前往获取？"
  switch: 1
  stringId: "UI_Teletubbies_Tips"
}
rows {
  content: "甜品王国"
  switch: 1
  stringId: "UGC_Skybox_Name_80"
}
rows {
  content: "静态元件开启时该元件所有逻辑性功能不生效！"
  switch: 1
  stringId: "UGC_StaticBatch_Tips01"
}
rows {
  content: "加速并留下彩虹印记！"
  switch: 1
  stringId: "Prop_RainBow_Tips"
}
rows {
  content: "加速并留下彩虹印记！"
  switch: 1
  stringId: "Prop_RainBow_MoreTips"
}
rows {
  content: "可以向上飞也可以向前飞！"
  switch: 1
  stringId: "Prop_DuckRocket_Tips"
}
rows {
  content: "可以向上飞也可以向前飞！"
  switch: 1
  stringId: "Prop_DuckRocket_MoreTips"
}
rows {
  content: "<HeroName>美食品鉴官 红丝绒</>可作为峡谷模式中<HeroName>貂蝉</>的皮肤使用"
  switch: 1
  stringId: "DrawReward_FourthText"
}
rows {
  content: "继续"
  switch: 1
  stringId: "Common_Continue"
}
rows {
  content: "删除失败"
  switch: 1
  stringId: "Delete_ChatRecord_Fail"
}
rows {
  content: "星宝脚下元件会被锁定，无法进行替换操作"
  switch: 1
  stringId: "UGC_DownFootComp_IsLock"
}
rows {
  content: "当前图片删除后将无法恢复，是否确认删除"
  switch: 1
  stringId: "Delete_Photo_CantRecover"
}
rows {
  content: "新情报"
  switch: 1
  stringId: "New_Intelligence"
}
rows {
  content: "截止时间全服前{0}有奖"
  switch: 1
  stringId: "Activity_RankNum_HasReward"
}
rows {
  content: "暂无名次"
  switch: 1
  stringId: "Rank_Dont_Have_Rank"
}
rows {
  content: "你已经是最快的了"
  switch: 1
  stringId: "UI_KongFuPanda_Fastest"
}
rows {
  content: "排行榜空空"
  switch: 1
  stringId: "UI_KongFuPanda_EmptyRank"
}
rows {
  content: "第{0}名玩家竞速时间：{1}"
  switch: 1
  stringId: "UI_KongFuPanda_RankTime"
}
rows {
  content: "原耗时:{0}"
  switch: 1
  stringId: "UI_KongFuPanda_OldTime"
}
rows {
  content: "第{0}名玩家 "
  switch: 1
  stringId: "UI_KongFuPanda_IsRankNum"
}
rows {
  content: "如果想要生成内容到精确位置，可以先将<a style=\"AIAssistantChatPurple\" mark=\"openrulewindow\">【智能定位盒子】</>放到地图内，选中盒子后再让智能编辑助手生成内容"
  switch: 1
  stringId: "UGC_AiAssistant_Welcome02"
}
rows {
  content: "暂时无法和AI助手聊天，请稍后再试~"
  switch: 1
  stringId: "UGC_AiAssistant_Block"
}
rows {
  content: "邀请框"
  switch: 1
  stringId: "Player_ChangeInviteFrame"
}
rows {
  content: "糖果收集器"
  switch: 1
  stringId: "Prop_80022Vacuum_Name"
}
rows {
  content: "长按吸取糖果~当心过热！"
  switch: 1
  stringId: "Prop_80022Vacuum_Tips"
}
rows {
  content: "前往【设置-密码管理】开启免密操作可在一定时间内免除重复验证"
  switch: 1
  stringId: "UI_SysSetting_Password_Pass_Tips"
}
rows {
  content: "当前场景不能使用该技能"
  switch: 1
  stringId: "Exclusive_Vehicle_ForbidSpecialSkillSlotTips"
}
rows {
  content: "小游戏内无法使用该功能，请下载元梦之星app使用噢~"
  switch: 1
  stringId: "UI_Share_Not_Support"
}
rows {
  content: "{0}解锁，敬请期待"
  switch: 1
  stringId: "UI_Countdown_Main_Unlock"
}
rows {
  content: "你的限定自选时装等你登录元梦之星领取！"
  switch: 1
  stringId: "SystemMessageNextDayLoginTitle"
}
rows {
  content: "次日登录游戏，立刻领取限定自选时装，快来登录领取吧"
  switch: 1
  stringId: "SystemMessageNextDayLoginContent"
}
rows {
  content: "输入的字数超出限制了~"
  switch: 1
  stringId: "UGC_AiAssistant_TextLimit"
}
rows {
  content: "保存空地图出错了，请在场景中放置一些元件再使用智能编辑助手吧~"
  switch: 1
  stringId: "UGC_AiAssistant_EmptyMapError"
}
rows {
  content: "MD5码测试"
  switch: 1
  stringId: "MD5Test"
}
rows {
  content: "奖励找回"
  switch: 1
  stringId: "Retrieve_Entrance"
}
rows {
  content: "确认消耗[0]找回以下奖励吗？"
  switch: 1
  stringId: "Retrieve_Exchange_info"
}
rows {
  content: "一键找回"
  switch: 1
  stringId: "Retrieve_Oneclick"
}
rows {
  content: "每份奖励只能选择一种找回方式"
  switch: 1
  stringId: "Retrieve_Way_info"
}
rows {
  content: "奖励已全部领取，无需找回~"
  switch: 1
  stringId: "Retrieve_None_info"
}
rows {
  content: "您的[0]不足"
  switch: 1
  stringId: "Retrieve_Money_info"
}
rows {
  content: "找回"
  switch: 1
  stringId: "Retrieve_Button"
}
rows {
  content: "50%"
  switch: 1
  stringId: "Retrieve_Ratio_1"
}
rows {
  content: "100%"
  switch: 1
  stringId: "Retrieve_Ratio_2"
}
rows {
  content: "[0]至[1]限时开启"
  switch: 1
  stringId: "Retrieve_Time"
}
rows {
  content: "奖励已全部找回"
  switch: 1
  stringId: "Retrieve_All_info"
}
rows {
  content: "当前解锁奖励已全部找回"
  switch: 1
  stringId: "Retrieve_Unlock_All_info"
}
rows {
  content: "周末奖杯加倍"
  switch: 1
  stringId: "Cupgame_weekend_Duplicate_tag"
}
rows {
  content: "周末限时任务"
  switch: 1
  stringId: "Cuptask_weekend_Duplicate_tag"
}
rows {
  content: "可找回"
  switch: 1
  stringId: "Retrieve_Seek_Hint"
}
rows {
  content: "您已经拥有该祈愿的最终奖励，是否继续祈愿？"
  switch: 1
  stringId: "GotAllRaffleGrandReward"
}
rows {
  content: "当前暂无任务哦~"
  switch: 1
  stringId: "CupTask_None_info"
}
rows {
  content: "节日加倍"
  switch: 1
  stringId: "Addtype_tag1"
}
rows {
  content: "周末加倍"
  switch: 1
  stringId: "Addtype_tag2"
}
rows {
  content: "活动加倍"
  switch: 1
  stringId: "Addtype_tag3"
}
rows {
  content: "节日限时"
  switch: 1
  stringId: "LimitedType_tag1"
}
rows {
  content: "周末限时"
  switch: 1
  stringId: "LimitedType_tag2"
}
rows {
  content: "活动限时"
  switch: 1
  stringId: "LimitedType_tag3"
}
