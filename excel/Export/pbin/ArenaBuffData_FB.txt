com.tencent.wea.xlsRes.table_ArenaBuffData
excel/xls/FB/B_FB玩法Buff表.xlsx sheet:Buff表
rows {
  id: 99991101
  buff_group_id: 99991101
  duration: 3.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: false
  remove_on_round_end: true
  buff_abnormal_status: Arena_BAS_UnStoppableStrong
  is_buff: true
  buff_tags: Arena_BTag_Positive
  hero_type: AHE_Invalid
}
rows {
  id: 99991102
  buff_group_id: 99991101
  duration: 3.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: false
  remove_on_round_end: true
  effect_type: Arena_SetTag
  param1: "ATD_Unselectable"
  param2: "1"
  buff_abnormal_status: Arena_BAS_UnSelectable
  is_buff: true
  buff_tags: Arena_BTag_Positive
  hero_type: AHE_Invalid
}
rows {
  id: 99991103
  buff_group_id: 99991101
  duration: 3.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: false
  remove_on_round_end: true
  effect_type: Arena_SetTag
  param1: "ATD_Invincible"
  param2: "1"
  buff_abnormal_status: Arena_BAS_Invincible
  is_buff: true
  buff_tags: Arena_BTag_Positive
  hero_type: AHE_Invalid
}
rows {
  id: 99991104
  buff_group_id: 99991104
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: false
  remove_on_round_end: true
  effect_type: Arena_CombatDealDamage
  param1: "Arena_OnBuffTick"
  param2: "Arena_TCET_Target"
  param3: "Arena_TCET_Target"
  param4: "10008"
  param5: "0.5"
  param6: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 99991105
  buff_group_id: 99991105
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: false
  remove_on_round_end: true
  effect_type: Arena_ModAttr
  param1: "ATTR_MOVE_SPEED_BASICADD_PERCENT"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 99991106
  buff_group_id: 99991106
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: false
  remove_on_round_end: true
  effect_type: Arena_ModAttr
  param1: "ATTR_MOVE_SPEED_BASICADD_PERCENT"
  param2: "30"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 99991107
  buff_group_id: 99991107
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: false
  remove_on_round_end: true
  effect_type: Arena_ModAttr
  param1: "ATTR_MOVE_SPEED_BASICADD_PERCENT"
  param2: "40"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 99992108
  buff_group_id: 99992108
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_SetTag
  param1: "ATD_Invisible_System_Particle"
  param2: "1"
  buff_abnormal_status: Arena_BAS_Invisible
  is_buff: true
  buff_tags: Arena_BTag_Positive
  hero_type: AHE_Invalid
}
rows {
  id: 430001001
  buff_group_id: 430001001
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_CombatDealDamage
  param1: "Arena_OnBuffTick"
  param2: "Arena_TCET_Target"
  param3: "Arena_TCET_Target"
  param4: "10012"
  param5: "1"
  param6: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430101011
  buff_group_id: 430101011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "114"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430101021
  buff_group_id: 430101011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "27"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430101031
  buff_group_id: 430101011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430101041
  buff_group_id: 430101011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430102011
  buff_group_id: 430102011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "44"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430102021
  buff_group_id: 430102011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "19"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430102031
  buff_group_id: 430102011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430102041
  buff_group_id: 430102011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430103011
  buff_group_id: 430103011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "60"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430103021
  buff_group_id: 430103011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-17"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430103031
  buff_group_id: 430103011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430103041
  buff_group_id: 430103011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430104011
  buff_group_id: 430104011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "32"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430104021
  buff_group_id: 430104011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-17"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430104031
  buff_group_id: 430104011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430104041
  buff_group_id: 430104011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430105011
  buff_group_id: 430105011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "18"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430105021
  buff_group_id: 430105011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-10"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430105031
  buff_group_id: 430105011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430105041
  buff_group_id: 430105011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430106011
  buff_group_id: 430106011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "130"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430106021
  buff_group_id: 430106011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-22"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430106031
  buff_group_id: 430106011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430106041
  buff_group_id: 430106011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430107011
  buff_group_id: 430107011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "24"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430107021
  buff_group_id: 430107011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-22"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430107031
  buff_group_id: 430107011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430107041
  buff_group_id: 430107011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430108011
  buff_group_id: 430108011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "35"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430108021
  buff_group_id: 430108011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "18"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430108031
  buff_group_id: 430108011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430108041
  buff_group_id: 430108011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430109011
  buff_group_id: 430109011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "75"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430109021
  buff_group_id: 430109011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "21"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430109031
  buff_group_id: 430109011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430109041
  buff_group_id: 430109011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430110011
  buff_group_id: 430110011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "56"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430110021
  buff_group_id: 430110011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-24"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430110031
  buff_group_id: 430110011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430110041
  buff_group_id: 430110011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430111011
  buff_group_id: 430111011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "126"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430111021
  buff_group_id: 430111011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "27"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430111031
  buff_group_id: 430111011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430111041
  buff_group_id: 430111011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430112011
  buff_group_id: 430112011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "60"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430112021
  buff_group_id: 430112011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-29"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430112031
  buff_group_id: 430112011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430112041
  buff_group_id: 430112011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430113011
  buff_group_id: 430113011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "54"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430113021
  buff_group_id: 430113011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "29"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430113031
  buff_group_id: 430113011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430113041
  buff_group_id: 430113011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430114011
  buff_group_id: 430114011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "22"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430114021
  buff_group_id: 430114011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "8"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430114031
  buff_group_id: 430114011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430114041
  buff_group_id: 430114011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430115011
  buff_group_id: 430115011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "57"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430115021
  buff_group_id: 430115011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430115031
  buff_group_id: 430115011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430115041
  buff_group_id: 430115011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430116011
  buff_group_id: 430116011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "34"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430116021
  buff_group_id: 430116011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-10"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430116031
  buff_group_id: 430116011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430116041
  buff_group_id: 430116011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430117011
  buff_group_id: 430117011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "14"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430117021
  buff_group_id: 430117011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430117031
  buff_group_id: 430117011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430117041
  buff_group_id: 430117011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430118011
  buff_group_id: 430118011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "120"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430118021
  buff_group_id: 430118011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "7"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430118031
  buff_group_id: 430118011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430118041
  buff_group_id: 430118011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430119011
  buff_group_id: 430119011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "40"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430119021
  buff_group_id: 430119011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-29"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430119031
  buff_group_id: 430119011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430119041
  buff_group_id: 430119011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430120011
  buff_group_id: 430120011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "96"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430120021
  buff_group_id: 430120011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "35"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430120031
  buff_group_id: 430120011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430120041
  buff_group_id: 430120011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430121011
  buff_group_id: 430121011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "56"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430121021
  buff_group_id: 430121011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-12"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430121031
  buff_group_id: 430121011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430121041
  buff_group_id: 430121011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430122011
  buff_group_id: 430122011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "59"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430122021
  buff_group_id: 430122011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "4"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430122031
  buff_group_id: 430122011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430122041
  buff_group_id: 430122011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430123011
  buff_group_id: 430123011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "54"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430123021
  buff_group_id: 430123011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-32"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430123031
  buff_group_id: 430123011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430123041
  buff_group_id: 430123011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430124011
  buff_group_id: 430124011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "64"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430124021
  buff_group_id: 430124011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-28"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430124031
  buff_group_id: 430124011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430124041
  buff_group_id: 430124011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430125011
  buff_group_id: 430125011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "50"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430125021
  buff_group_id: 430125011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-34"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430125031
  buff_group_id: 430125011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430125041
  buff_group_id: 430125011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430126011
  buff_group_id: 430126011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "92"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430126021
  buff_group_id: 430126011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "47"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430126031
  buff_group_id: 430126011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430126041
  buff_group_id: 430126011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430127011
  buff_group_id: 430127011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "60"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430127021
  buff_group_id: 430127011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-15"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430127031
  buff_group_id: 430127011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430127041
  buff_group_id: 430127011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430128011
  buff_group_id: 430128011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "35"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430128021
  buff_group_id: 430128011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430128031
  buff_group_id: 430128011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430128041
  buff_group_id: 430128011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430129011
  buff_group_id: 430129011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "74"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430129021
  buff_group_id: 430129011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-38"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430129031
  buff_group_id: 430129011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430129041
  buff_group_id: 430129011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430130011
  buff_group_id: 430130011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "50"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430130021
  buff_group_id: 430130011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "24"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430130031
  buff_group_id: 430130011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430130041
  buff_group_id: 430130011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430131011
  buff_group_id: 430131011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "50"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430131021
  buff_group_id: 430131011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "0"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430131031
  buff_group_id: 430131011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430131041
  buff_group_id: 430131011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430132011
  buff_group_id: 430132011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430132021
  buff_group_id: 430132011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430132031
  buff_group_id: 430132011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430132041
  buff_group_id: 430132011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430133011
  buff_group_id: 430133011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "5"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430133021
  buff_group_id: 430133011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-16"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430133031
  buff_group_id: 430133011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430133041
  buff_group_id: 430133011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430134011
  buff_group_id: 430134011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "45"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430134021
  buff_group_id: 430134011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "16"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430134031
  buff_group_id: 430134011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430134041
  buff_group_id: 430134011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430135011
  buff_group_id: 430135011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "-30"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430135021
  buff_group_id: 430135011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-26"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430135031
  buff_group_id: 430135011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430135041
  buff_group_id: 430135011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430136011
  buff_group_id: 430136011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "-2"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430136021
  buff_group_id: 430136011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-42"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430136031
  buff_group_id: 430136011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430136041
  buff_group_id: 430136011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430137011
  buff_group_id: 430137011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "50"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430137021
  buff_group_id: 430137011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-10"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430137031
  buff_group_id: 430137011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430137041
  buff_group_id: 430137011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430138011
  buff_group_id: 430138011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "2"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430138021
  buff_group_id: 430138011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-14"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430138031
  buff_group_id: 430138011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430138041
  buff_group_id: 430138011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430139011
  buff_group_id: 430139011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "39"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430139021
  buff_group_id: 430139011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "30"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430139031
  buff_group_id: 430139011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430139041
  buff_group_id: 430139011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430140011
  buff_group_id: 430140011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "40"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430140021
  buff_group_id: 430140011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430140031
  buff_group_id: 430140011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430140041
  buff_group_id: 430140011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430141011
  buff_group_id: 430141011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "40"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430141021
  buff_group_id: 430141011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-5"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430141031
  buff_group_id: 430141011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430141041
  buff_group_id: 430141011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430142011
  buff_group_id: 430142011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "44"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430142021
  buff_group_id: 430142011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-6"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430142031
  buff_group_id: 430142011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430142041
  buff_group_id: 430142011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430143011
  buff_group_id: 430143011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "5"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430143021
  buff_group_id: 430143011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "5"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430143031
  buff_group_id: 430143011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430143041
  buff_group_id: 430143011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430144011
  buff_group_id: 430144011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "30"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430144021
  buff_group_id: 430144011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430144031
  buff_group_id: 430144011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430144041
  buff_group_id: 430144011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430145011
  buff_group_id: 430145011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "40"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430145021
  buff_group_id: 430145011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "20"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430145031
  buff_group_id: 430145011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430145041
  buff_group_id: 430145011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430146011
  buff_group_id: 430146011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "70"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430146021
  buff_group_id: 430146011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "10"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430146031
  buff_group_id: 430146011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430146041
  buff_group_id: 430146011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430147011
  buff_group_id: 430147011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_GEDMG_BONUS_GAMEMODE"
  param2: "-10"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430147021
  buff_group_id: 430147011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_DMGRD_GENERAL_BASICADD"
  param2: "-10"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430147031
  buff_group_id: 430147011
  duration: 99999.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  effect_type: Arena_ModAttr
  param1: "ATTR_RESILIENT_MARK"
  param2: "1"
  is_buff: true
  hero_type: AHE_Invalid
}
rows {
  id: 430147041
  buff_group_id: 430147011
  duration: 0.0
  coexist_rule: ARENA_BCR_Override
  stack_limit: 1
  remove_on_dead: true
  remove_on_round_end: true
  effect_type: Arena_AddBuffByCombatState
  param1: "430001001"
  is_buff: true
  hero_type: AHE_Invalid
}
