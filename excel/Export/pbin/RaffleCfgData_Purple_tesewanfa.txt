com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_特色玩法.xlsx sheet:奖池-紫色卡池
rows {
  poolId: 13000000
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000001
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000002
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000003
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000004
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000005
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000006
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000007
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000008
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000009
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000010
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 2
  }
  maxLimit: 2
}
rows {
  poolId: 13000011
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000012
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "7折"
        ratio: 0.7
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000013
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 2
  }
  maxLimit: 2
}
rows {
  poolId: 13000014
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000015
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "7折"
        ratio: 0.7
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000120
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000121
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000122
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000123
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000130
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000131
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000132
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000133
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000160
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000161
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000162
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000163
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000164
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000165
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000166
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000167
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000168
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000169
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000170
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000171
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000172
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000020
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000021
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 25
      permanent {
        text: "2折"
        ratio: 0.2
        maxNum: 8
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000022
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 25
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 8
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000023
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 25
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000173
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000174
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 3
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000175
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 5
  }
  maxLimit: 5
}
rows {
  poolId: 13000176
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000177
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000178
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 3
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000179
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 5
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 5
  }
  maxLimit: 5
}
rows {
  poolId: 13000180
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000190
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000191
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000192
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000193
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000194
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000195
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000196
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000197
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000198
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000199
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000200
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000201
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000202
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000230
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000231
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000232
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000233
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000240
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000241
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000242
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000243
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000250
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000251
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000252
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000253
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000260
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000261
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000262
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000263
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000270
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000271
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000272
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000273
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000274
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000275
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000276
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000277
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000278
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000279
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000280
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000281
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000282
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000210
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000211
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000212
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000213
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000214
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "8折"
        ratio: 0.8
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000220
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000221
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000222
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000223
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000224
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "8折"
        ratio: 0.8
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000290
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000291
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000292
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000293
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000294
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "8折"
        ratio: 0.8
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000300
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000301
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000302
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000303
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000304
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "8折"
        ratio: 0.8
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000310
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000311
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 3
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000312
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 5
  }
  maxLimit: 5
}
rows {
  poolId: 13000313
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000320
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000321
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 3
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000322
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 5
  }
  maxLimit: 5
}
rows {
  poolId: 13000323
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000330
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000331
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000332
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000333
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000334
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000335
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000336
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000337
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000338
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000339
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000340
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000350
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000351
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000352
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000353
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000360
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000361
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000362
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000363
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000370
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000371
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000372
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000373
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000374
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000375
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000376
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000377
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000378
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000379
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000380
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000381
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000382
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000390
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000391
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000392
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000393
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000394
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000395
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000396
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000397
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000398
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000399
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000400
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000410
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000411
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000412
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000413
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000420
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000421
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000422
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000423
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000430
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000431
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000432
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000433
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000434
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000435
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000436
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000437
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000438
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000439
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000440
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000450
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000451
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000452
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000453
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000460
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000461
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000462
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000463
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000470
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 6
  }
  maxLimit: 6
}
rows {
  poolId: 13000471
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "25折"
        ratio: 0.25
        maxNum: 6
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 6
  }
  maxLimit: 6
}
rows {
  poolId: 13000472
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "25折"
        ratio: 0.25
        maxNum: 8
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000473
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 12
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000474
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 16
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 16
  }
  maxLimit: 16
}
rows {
  poolId: 13000475
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 20
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 20
  }
  maxLimit: 20
}
rows {
  poolId: 13000476
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 30
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 30
  }
  maxLimit: 30
}
rows {
  poolId: 13000480
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000481
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "25折"
        ratio: 0.25
        maxNum: 8
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000482
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 8
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000483
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 8
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000484
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000490
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000491
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000492
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000493
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000494
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000495
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000496
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000497
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000498
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000499
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000500
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000501
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000502
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000510
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000511
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000512
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000513
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000520
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000521
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000522
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000523
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000530
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000531
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000532
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000533
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000534
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000535
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000536
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000537
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000538
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000539
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000540
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000550
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000551
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000552
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000553
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000560
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000561
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000562
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "7.5折"
        ratio: 0.75
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000563
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000570
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000571
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000572
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000573
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000580
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000581
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000582
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000583
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000584
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000585
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000586
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000587
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000588
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000589
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000590
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000591
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000592
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000600
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000601
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 4
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000602
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 8
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000603
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 7
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 7
  }
  maxLimit: 7
}
rows {
  poolId: 13000610
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 3
  }
  maxLimit: 3
}
rows {
  poolId: 13000611
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 25
      permanent {
        text: "2折"
        ratio: 0.2
        maxNum: 8
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000612
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 25
      permanent {
        text: "6折"
        ratio: 0.6
        maxNum: 8
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000613
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 25
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000620
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 4
  }
  maxLimit: 4
}
rows {
  poolId: 13000621
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 6
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 6
  }
  maxLimit: 6
}
rows {
  poolId: 13000622
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 8
  }
  maxLimit: 8
}
rows {
  poolId: 13000623
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 10
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 13000624
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 14
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 16
  }
  maxLimit: 16
}
rows {
  poolId: 13000630
  coinType: 3
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000631
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000632
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 5
      permanent {
        text: "4折"
        ratio: 0.4
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000633
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000634
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 12
      permanent {
        text: "5折"
        ratio: 0.5
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000635
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000636
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 16
      permanent {
        text: "75折"
        ratio: 0.75
        maxNum: 10
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000637
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000638
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 18
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000639
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
rows {
  poolId: 13000640
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 22
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 10
  }
  maxLimit: 10
}
