com.tencent.wea.xlsRes.table_NR3E3VocationData
excel/xls/N_NR3E3_职业表.xlsx sheet:玩法阵营角色
rows {
  id: 1
  faction: 1
  campRole: "狼人"
  CampName: "狼人"
  VocationName: "Hunter"
  icon: "T_E3_Icon_Hunter_002_png"
  inLevelIcon: "T_E3_Icon_Hunter_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_NR3ESkillDes2"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_UrgentReportAni"
  UISequence: 199
  inCustomRoomIcon: "T_E3_Room_Icon_Hunter_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle24"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo10"
  RuleIsVisibility: 1
  SelectIdentitySort: 199
  SelectIdentityIcon: "T_E3_Prepare_Icon_Hunter_001"
  SelectIdentitySort_New: "199"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7101
}
rows {
  id: 2
  faction: 2
  campRole: "哨子"
  CampName: "平民"
  VocationName: "Sentry"
  icon: "T_E3_Icon_Whistle_002_png"
  inLevelIcon: "T_E3_Icon_Whistle_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_Rule22"
  intro: "InLevel_NR3ESkillDes_Host"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_UrgentReportAni"
  UISequence: 3
  inCustomRoomIcon: "T_E3_Room_Icon_Whistle_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle19"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo5"
  RuleIsVisibility: 1
  SelectIdentitySort: 3
  SelectIdentityIcon: "T_E3_Prepare_Icon_Whistle_001"
  SelectIdentitySort_New: "3"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7001
}
rows {
  id: 3
  faction: 2
  campRole: "天使"
  CampName: "平民"
  VocationName: "Mercy"
  icon: "T_E3_Icon_Angel_002_png"
  inLevelIcon: "T_E3_Icon_Angel_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_Rule20"
  intro: "InLevel_NR3E3_IdentityInformation6"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_UrgentReportAni"
  UISequence: 4
  inCustomRoomIcon: "T_E3_Room_Icon_Angel_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle17"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo3"
  RuleIsVisibility: 1
  SelectIdentitySort: 4
  SelectIdentityIcon: "T_E3_Prepare_Icon_Angel_001"
  SelectIdentitySort_New: "4"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7001
}
rows {
  id: 4
  faction: 2
  campRole: "平民"
  CampName: "平民"
  VocationName: "Doll"
  icon: "T_Common_Icon_Civilian_001_png"
  inLevelIcon: "T_E3_Icon_Civilian_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_NR3ESkillDes1"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_UrgentReportAni"
  UISequence: 99
  inCustomRoomIcon: "T_E3_Room_Icon_Civilian_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle15"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo1"
  RuleIsVisibility: 1
  SelectIdentitySort: 99
  SelectIdentityIcon: "T_E3_Prepare_Icon_Civilian_001"
  SelectIdentitySort_New: "99"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7001
}
rows {
  id: 5
  faction: 2
  campRole: "警长"
  CampName: "平民"
  VocationName: "Sheriff"
  icon: "T_E3_Icon_Police_002_png"
  inLevelIcon: "T_E3_Icon_Police_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_Rule19"
  intro: "InLevel_NR3E3_IdentityInformation1"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Doll_CompleteMission:VocationAbility_Sheriff_Attack"
  UISequence: 1
  inCustomRoomIcon: "T_E3_Room_Icon_Police_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle16"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo2"
  RuleIsVisibility: 1
  SelectIdentitySort: 1
  SelectIdentityIcon: "T_E3_Prepare_Icon_Police_001"
  SelectIdentitySort_New: "1"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7001
}
rows {
  id: 6
  faction: 2
  campRole: "侦探"
  CampName: "平民"
  VocationName: "Detective"
  icon: "T_E3_Icon_Detective_002_png"
  inLevelIcon: "T_E3_Icon_Detective_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_Rule21"
  intro: "InLevel_NR3E3_IdentityInformation2"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Doll_CompleteMission:VocationAbility_Detect"
  UISequence: 2
  inCustomRoomIcon: "T_E3_Room_Icon_Detective_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle18"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo4"
  RuleIsVisibility: 1
  SelectIdentitySort: 2
  SelectIdentityIcon: "T_E3_Prepare_Icon_Detective_001"
  SelectIdentitySort_New: "2"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7001
}
rows {
  id: 7
  faction: 2
  campRole: "消防员"
  CampName: "平民"
  VocationName: "FireMen"
  icon: "T_E3_Icon_Fireman_002_png"
  inLevelIcon: "T_E3_Icon_Fireman_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_Rule24"
  intro: "InLevel_NR3E3_IdentityInformation3"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Hunter_UseHole:VocationAbility_Doll_CompleteMission:VocationAbility_FireMen_CompleteUrgentMission"
  UISequence: 5
  inCustomRoomIcon: "T_E3_Room_Icon_Fireman_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle21"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo7"
  RuleIsVisibility: 1
  SelectIdentitySort: 5
  SelectIdentityIcon: "T_E3_Prepare_Icon_Fireman_001"
  SelectIdentitySort_New: "5"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7001
}
rows {
  id: 8
  faction: 1
  campRole: "伪装狼"
  CampName: "狼人"
  VocationName: "TrickWolf"
  icon: "T_E3_Icon_Pretender_002_png"
  inLevelIcon: "T_E3_Icon_Pretender_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_Rule25"
  intro: "InLevel_NR3E3_IdentityInformation4"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Hunter_Attack:VocationAbility_Hunter_UseHole:VocationAbility_Doll_CompleteMission:VocationAbility_TrickWolf_ChangeAvatar"
  UISequence: 102
  inCustomRoomIcon: "T_E3_Room_Icon_Pretender_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle25"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo11"
  RuleIsVisibility: 1
  SelectIdentitySort: 102
  SelectIdentityIcon: "T_E3_Prepare_Icon_Pretender_001"
  SelectIdentitySort_New: "102"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7101
}
rows {
  id: 9
  faction: 1
  campRole: "刺客狼"
  CampName: "狼人"
  VocationName: "AssassinWolf"
  icon: "T_E3_Icon_Assassin_002_png"
  inLevelIcon: "T_E3_Icon_Assassin_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_Rule26"
  intro: "InLevel_NR3E3_IdentityInformation5"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Hunter_Attack:VocationAbility_Hunter_UseHole:VocationAbility_Doll_CompleteMission:VocationAbility_AssassinWolf_Assassin"
  UISequence: 101
  inCustomRoomIcon: "T_E3_Room_Icon_Assassin_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle27"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo13"
  RuleIsVisibility: 1
  SelectIdentitySort: 101
  SelectIdentityIcon: "T_E3_Prepare_Icon_Assassin_001"
  SelectIdentitySort_New: "101"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7102
}
rows {
  id: 10
  faction: 1
  campRole: "炸弹狼"
  CampName: "狼人"
  VocationName: "BomberWolf"
  icon: "T_E3_Icon_BombWolf_002_png"
  inLevelIcon: "T_E3_Icon_BombWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_Rule39"
  intro: "InLevel_NR3E3_IdentityInformation9"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Hunter_UseHole:VocationAbility_Doll_CompleteMission:VocationAbility_Hunter_Bomb"
  UISequence: 103
  inCustomRoomIcon: "T_E3_Room_Icon_BombWolf_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle26"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo12"
  RuleIsVisibility: 1
  SelectIdentitySort: 103
  SelectIdentityIcon: "T_E3_Prepare_Icon_BombWolf_001"
  SelectIdentitySort_New: "103"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7102
}
rows {
  id: 11
  faction: 3
  campRole: "臭鼬"
  CampName: "中立"
  VocationName: "Skunk"
  icon: "T_E3_Icon_Skunk_002_png"
  inLevelIcon: "T_E3_Icon_Skunk_001_png"
  target: "InLevel_NR3E3_IdentityInformation10"
  describe: "InLevel_NR3E3_Rule40"
  intro: "InLevel_NR3E3_IdentityInformation10"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Doll_CompleteMission:VocationAbility_Skunk_Fart"
  UISequence: 202
  inCustomRoomIcon: "T_E3_Room_Icon_Skunk_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle31"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo17"
  RuleIsVisibility: 1
  SelectIdentitySort: 202
  SelectIdentityIcon: "T_E3_Prepare_Icon_Skunk_001"
  SelectIdentitySort_New: "202"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 4
  RandomVocationGroupID: 7201
}
rows {
  id: 12
  faction: 3
  campRole: "赌徒"
  CampName: "中立"
  VocationName: "Gambler"
  icon: "T_E3_Icon_Gambler_002_png"
  inLevelIcon: "T_E3_Icon_Gambler_001_png"
  target: "InLevel_NR3E3_IdentityInformation11"
  describe: "InLevel_NR3E3_Rule41"
  intro: "InLevel_NR3E3_IdentityInformation11"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Hunter_UseHole:VocationAbility_Doll_CompleteMission:VocationAbility_SideGuess"
  UISequence: 203
  inCustomRoomIcon: "T_E3_Room_Icon_Gambler_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle28"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo14"
  RuleIsVisibility: 1
  SelectIdentitySort: 203
  SelectIdentityIcon: "T_E3_Prepare_Icon_Gambler_001"
  SelectIdentitySort_New: "203"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 3
  RandomVocationGroupID: 7201
}
rows {
  id: 13
  faction: 3
  campRole: "赏金猎人"
  CampName: "中立"
  VocationName: "BountyHunter"
  icon: "T_E3_Icon_BountyHunter_002_png"
  inLevelIcon: "T_E3_Icon_BountyHunter_001_png"
  target: "InLevel_NR3E3_IdentityInformation12"
  describe: "InLevel_NR3E3_Rule42"
  intro: "InLevel_NR3E3_IdentityInformation12"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Doll_CompleteMission:VocationAbility_BountyHunterAttack"
  UISequence: 204
  inCustomRoomIcon: "T_E3_Room_Icon_BountyHunter_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle29"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo15"
  RuleIsVisibility: 1
  SelectIdentitySort: 204
  SelectIdentityIcon: "T_E3_Prepare_Icon_BountyHunter_001"
  SelectIdentitySort_New: "204"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 5
  RandomVocationGroupID: 7202
}
rows {
  id: 14
  faction: 3
  campRole: "小丑"
  CampName: "中立"
  VocationName: "Joker"
  icon: "T_E3_Icon_Joker_002_png"
  inLevelIcon: "T_E3_Icon_Joker_001_png"
  target: "InLevel_NR3E3_IdentityInformation13"
  describe: "InLevel_NR3E3_Rule43"
  intro: "InLevel_NR3E3_IdentityInformation13"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Doll_CompleteMission:VocationAbility_Joker_BeVoteOutWin"
  UISequence: 201
  inCustomRoomIcon: "T_E3_Room_Icon_Joker_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle30"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo16"
  RuleIsVisibility: 1
  SelectIdentitySort: 201
  SelectIdentityIcon: "T_E3_Prepare_Icon_Joker_001"
  SelectIdentitySort_New: "201"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 3
  RandomVocationGroupID: 7202
}
rows {
  id: 15
  faction: 2
  campRole: "主持人"
  CampName: "平民"
  VocationName: "Preside"
  icon: "T_E3_Icon_Host_002_png"
  inLevelIcon: "T_E3_Icon_Host_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_Rule34"
  intro: "InLevel_NR3E3_IdentityInformation8"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Doll_CompleteMission:VocationAbility_Preside_Vote"
  UISequence: 6
  inCustomRoomIcon: "T_E3_Room_Icon_Host_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle20"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo6"
  RuleIsVisibility: 1
  SelectIdentitySort: 6
  SelectIdentityIcon: "T_E3_Prepare_Icon_Host_001"
  SelectIdentitySort_New: "6"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
}
rows {
  id: 16
  faction: 2
  campRole: "学者"
  CampName: "平民"
  VocationName: "Scholar"
  icon: "T_E3_Icon_Scholar_002_png"
  inLevelIcon: "T_E3_Icon_Scholar_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_Rule35"
  intro: "InLevel_NR3E3_IdentityInformation7"
  VocationAbilities: "VocationAbility_Death:VocationAbility_Doll_Report:VocationAbility_Doll_UrgentReport:VocationAbility_Doll_UrgentReportAni:VocationAbility_Doll_CompleteMission"
  UISequence: 7
  inCustomRoomIcon: "T_E3_Room_Icon_Scholar_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle23"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo9"
  RuleIsVisibility: 1
  SelectIdentitySort: 7
  SelectIdentityIcon: "T_E3_Prepare_Icon_Scholar_001"
  SelectIdentitySort_New: "7"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 17
  faction: 2
  campRole: "勇士"
  CampName: "平民"
  VocationName: "Warrior"
  icon: "T_E3_Icon_Warrior_002_png"
  inLevelIcon: "T_E3_Icon_Warrior_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_IdentityInformation15"
  intro: "InLevel_NR3E3_IdentityInformation14"
  UISequence: 8
  inCustomRoomIcon: "T_E3_Room_Icon_Warrior_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle22"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo8"
  RuleIsVisibility: 1
  SelectIdentitySort: 8
  SelectIdentityIcon: "T_E3_Prepare_Icon_Warrior_001"
  SelectIdentitySort_New: "8"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 18
  faction: 1
  campRole: "独狼"
  CampName: "狼人"
  VocationName: "LoneWolf"
  icon: "T_E3_Icon_LoneWolf_002_png"
  inLevelIcon: "T_E3_Icon_LoneWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_LoneWolf"
  intro: "InLevel_NR3E3_VocationDes_LoneWolf2"
  UISequence: 105
  inCustomRoomIcon: "T_E3_Room_Icon_LoneWolf_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle33"
  RuleDes: "InLevel_NR3E3_VocationDes_LoneWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 105
  SelectIdentityIcon: "T_E3_Prepare_Icon_LoneWolf_001"
  SelectIdentitySort_New: "105"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7103
}
rows {
  id: 19
  faction: 1
  campRole: "潜行狼"
  CampName: "狼人"
  VocationName: "SneakWolf"
  icon: "T_E3_Icon_StalkerWolf_002_png"
  inLevelIcon: "T_E3_Icon_StalkerWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_SneakWolf"
  intro: "InLevel_NR3E3_VocationDes_SneakWolf2"
  UISequence: 104
  inCustomRoomIcon: "T_E3_Room_Icon_StalkerWolf_001_png"
  RuleName: "UI_InLevel_NR3E3_Rule_StalkerWolf"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo19"
  RuleIsVisibility: 1
  SelectIdentitySort: 104
  SelectIdentityIcon: "T_E3_Prepare_Icon_StalkerWolf_001"
  SelectIdentitySort_New: "104"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7103
}
rows {
  id: 20
  faction: 2
  campRole: "调皮鬼"
  CampName: "平民"
  VocationName: "Naughty"
  icon: "T_E3_Icon_Rogue_002_png"
  inLevelIcon: "T_E3_Icon_Rogue_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Naughty"
  intro: "InLevel_NR3E3_VocationDes_Naughty2"
  UISequence: 9
  inCustomRoomIcon: "T_E3_Room_Icon_Rogue_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle32"
  RuleDes: "InLevel_NR3E3_VocationDes_Naughty"
  RuleIsVisibility: 1
  SelectIdentitySort: 9
  SelectIdentityIcon: "T_E3_Prepare_Icon_Rogue_001"
  SelectIdentitySort_New: "9"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 21
  faction: 2
  campRole: "占星师"
  CampName: "平民"
  VocationName: "Drawer"
  icon: "T_E3_Icon_Draftsman_002_png"
  inLevelIcon: "T_E3_Icon_Draftsman_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Drawer"
  intro: "InLevel_NR3E3_VocationDes_Drawer2"
  UISequence: 98
  inCustomRoomIcon: "T_E3_Room_Icon_Draftsman_001_png"
  RuleName: "UI_InLevel_NR3E3_Rule_RolePainter"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo18"
  RuleIsVisibility: 0
  SelectIdentityIcon: "T_E3_Prepare_Icon_Draftsman_001"
  HighestVersion: "1.3.7.999"
  RoleInfoPointsAddTypeId: 1
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 22
  faction: 2
  campRole: "大明星"
  CampName: "平民"
  VocationName: "GreatStar"
  icon: "T_E3_Icon_SuperStar_002_png"
  inLevelIcon: "T_E3_Icon_SuperStar_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_GreatStar"
  intro: "InLevel_NR3E3_VocationDes_GreatStar2"
  UISequence: 10
  inCustomRoomIcon: "T_E3_Room_Icon_SuperStar_001_png"
  RuleName: "UI_InLevel_NR3E3_GreatStarTip"
  RuleDes: "InLevel_NR3E3_VocationDes_GreatStar"
  RuleIsVisibility: 1
  SelectIdentitySort: 10
  SelectIdentityIcon: "T_E3_Prepare_Icon_SuperStar_001"
  SelectIdentitySort_New: "10"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
}
rows {
  id: 23
  faction: 3
  campRole: "人偶师"
  CampName: "中立"
  VocationName: "PuppetMaster"
  icon: "T_E3_Icon_Puppet_002_png"
  inLevelIcon: "T_E3_Icon_Puppet_001_png"
  target: "InLevel_NR3ERoleTarget_PuppetMaster"
  describe: "InLevel_NR3E3_VocationDes_PuppetMaster"
  UISequence: 206
  inCustomRoomIcon: "T_E3_Room_Icon_Puppet_001_png"
  RuleName: "InLevel_NR3E3_Rule51"
  RuleDes: "InLevel_NR3E3_VocationDes_PuppetMaster"
  RuleIsVisibility: 1
  SelectIdentitySort: 206
  SelectIdentityIcon: "T_E3_Prepare_Icon_Puppet_001"
  SelectIdentitySort_New: "206"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 4
  RandomVocationGroupID: 7203
}
rows {
  id: 24
  faction: 3
  campRole: "搬运工"
  CampName: "中立"
  VocationName: "Porter"
  icon: "T_E3_Icon_Porter_002_png"
  inLevelIcon: "T_E3_Icon_Porter_001_png"
  target: "InLevel_NR3ERoleTarget_Porter"
  describe: "InLevel_NR3E3_VocationDes_Porter"
  UISequence: 205
  inCustomRoomIcon: "T_E3_Room_Icon_Porter_001_png"
  RuleName: "InLevel_NR3E3_Rule52"
  RuleDes: "InLevel_NR3E3_VocationDes_Porter"
  RuleIsVisibility: 1
  SelectIdentitySort: 205
  SelectIdentityIcon: "T_E3_Prepare_Icon_Porter_001"
  SelectIdentitySort_New: "205"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 5
  RandomVocationGroupID: 7203
}
rows {
  id: 25
  faction: 1
  campRole: "大力狼"
  CampName: "狼人"
  VocationName: "StrongHunter"
  icon: "T_E3_Icon_MightWolf_002_png"
  inLevelIcon: "T_E3_Icon_MightWolf_001_png"
  target: "InLevel_NR3ERoleTarget_StrongHunter"
  describe: "InLevel_NR3E3_VocationDes_StrongHunter"
  intro: "InLevel_NR3E3_VocationDes_StrongHunter2"
  UISequence: 106
  inCustomRoomIcon: "T_E3_Room_Icon_MightWolf_001_png"
  RuleName: "InLevel_NR3E3_Rule53"
  RuleDes: "InLevel_NR3E3_VocationDes_StrongHunter"
  RuleIsVisibility: 1
  SelectIdentitySort: 106
  SelectIdentityIcon: "T_E3_Prepare_Icon_MightWolf_001"
  SelectIdentitySort_New: "106"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7104
}
rows {
  id: 26
  faction: 1
  campRole: "魔爆狼"
  CampName: "狼人"
  VocationName: "NuClearHunter"
  icon: "T_E3_Icon_BlastWolf_002_png"
  inLevelIcon: "T_E3_Icon_BlastWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_NuClearHunter"
  intro: "InLevel_NR3E3_VocationDes_NuClearHunter2"
  UISequence: 107
  inCustomRoomIcon: "T_E3_Room_Icon_BlastWolf_001_png"
  RuleName: "InLevel_NR3E3_Rule54"
  RuleDes: "InLevel_NR3E3_VocationDes_NuClearHunter"
  RuleIsVisibility: 1
  SelectIdentitySort: 107
  SelectIdentityIcon: "T_E3_Prepare_Icon_BlastWolf_001"
  SelectIdentitySort_New: "107"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7104
}
rows {
  id: 27
  faction: 2
  campRole: "独行侠"
  CampName: "平民"
  VocationName: "Ranger"
  icon: "T_E3_Icon_LoneRanger_002_png"
  inLevelIcon: "T_E3_Icon_LoneRanger_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Ranger"
  intro: "InLevel_NR3E3_VocationDes_Ranger2"
  UISequence: 11
  inCustomRoomIcon: "T_E3_Room_Icon_LoneRanger_001_png"
  RuleName: "InLevel_NR3E3_Rule55"
  RuleDes: "InLevel_NR3E3_VocationDes_Ranger"
  RuleIsVisibility: 1
  SelectIdentitySort: 11
  SelectIdentityIcon: "T_E3_Prepare_Icon_LoneRanger_001"
  SelectIdentitySort_New: "11"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
}
rows {
  id: 28
  faction: 3
  campRole: "铁蛋"
  CampName: "中立"
  VocationName: "IronMan"
  icon: "T_E3_Icon_IronEgg_002_png"
  inLevelIcon: "T_E3_Icon_IronEgg_001_png"
  target: "InLevel_NR3E3_VocationTarget_IronMan"
  describe: "InLevel_NR3E3_VocationDes_IronMan"
  UISequence: 207
  inCustomRoomIcon: "T_E3_Room_Icon_IronEgg_001_png"
  RuleName: "InLevel_NR3E3_Rule56"
  RuleDes: "InLevel_NR3E3_VocationDes_IronMan"
  RuleIsVisibility: 1
  SelectIdentitySort: 207
  SelectIdentityIcon: "T_E3_Prepare_Icon_IronEgg_001"
  SelectIdentitySort_New: "207"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 4
  RandomVocationGroupID: 7204
}
rows {
  id: 29
  faction: 3
  campRole: "流浪汉"
  CampName: "中立"
  VocationName: "Tramp"
  icon: "T_E3_Icon_Vagrant_002_png"
  inLevelIcon: "T_E3_Icon_Vagrant_001_png"
  target: "InLevel_NR3E3_VocationTarget_Tramp"
  describe: "InLevel_NR3E3_VocationDes_Tramp"
  UISequence: 208
  inCustomRoomIcon: "T_E3_Room_Icon_Vagrant_001_png"
  RuleName: "InLevel_NR3E3_Rule57"
  RuleDes: "InLevel_NR3E3_VocationDes_Tramp"
  RuleIsVisibility: 1
  SelectIdentitySort: 208
  SelectIdentityIcon: "T_E3_Prepare_Icon_Vagrant_001"
  LowestVersion: "********"
  SelectIdentitySort_New: "208"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240801
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 5
  RandomVocationGroupID: 7204
}
rows {
  id: 30
  faction: 1
  campRole: "秘术狼"
  CampName: "狼人"
  VocationName: "SealingWolf"
  icon: "T_E3_Icon_ArcaneWolf_002_png"
  inLevelIcon: "T_E3_Icon_ArcaneWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_SealingWolf"
  intro: "InLevel_NR3E3_VocationIntro_SealingWolf2"
  UISequence: 108
  inCustomRoomIcon: "T_E3_Room_Icon_ArcaneWolf_001_png"
  RuleName: "InLevel_NR3E3_Rule58"
  RuleDes: "InLevel_NR3E3_VocationDes_SealingWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 108
  SelectIdentityIcon: "T_E3_Prepare_Icon_ArcaneWolf_001"
  SelectIdentitySort_New: "108"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7105
}
rows {
  id: 31
  faction: 2
  campRole: "长老"
  CampName: "平民"
  VocationName: "Elder"
  icon: "T_E3_Icon_Elder_002_png"
  inLevelIcon: "T_E3_Icon_Elder_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Elder"
  intro: "InLevel_NR3E3_VocationIntro_Elder"
  UISequence: 12
  inCustomRoomIcon: "T_E3_Room_Icon_Elder_001_png"
  RuleName: "InLevel_NR3E3_Rule59"
  RuleDes: "InLevel_NR3E3_VocationDes_Elder"
  RuleIsVisibility: 1
  SelectIdentitySort: 12
  SelectIdentityIcon: "T_E3_Prepare_Icon_Elder_001"
  SelectIdentitySort_New: "12"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7003
}
rows {
  id: 32
  faction: 2
  campRole: "法医"
  CampName: "平民"
  VocationName: "Forencis"
  icon: "T_E3_Icon_Doctor_002_png"
  inLevelIcon: "T_E3_Icon_Doctor_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Forensic"
  intro: "InLevel_NR3E3_VocationIntro_Forensic"
  UISequence: 13
  inCustomRoomIcon: "T_E3_Room_Icon_Doctor_001_png"
  RuleName: "InLevel_NR3E3_Rule60"
  RuleDes: "InLevel_NR3E3_VocationDes_Forensic"
  RuleIsVisibility: 1
  SelectIdentitySort: 13
  SelectIdentityIcon: "T_E3_Prepare_Icon_Doctor_001"
  SelectIdentitySort_New: "13"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7003
}
rows {
  id: 33
  faction: 1
  campRole: "火眼狼"
  CampName: "狼人"
  VocationName: "XRayHunter"
  icon: "T_E3_Icon_FireeyeWolf_002_png"
  inLevelIcon: "T_E3_Icon_FireeyeWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationIntro_XRayHunter"
  intro: "InLevel_NR3E3_VocationIntro_XRayHunter2"
  UISequence: 109
  inCustomRoomIcon: "T_E3_Room_Icon_FireeyeWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationIntro_XRayHunter"
  RuleIsVisibility: 1
  SelectIdentitySort: 109
  SelectIdentityIcon: "T_E3_Prepare_Icon_FireeyeWolf_001"
  LowestVersion: "********"
  SelectIdentitySort_New: "109"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240802
  Preparations_JumpId: 234
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_4"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7105
}
rows {
  id: 34
  faction: 3
  campRole: "摄影师"
  CampName: "中立"
  VocationName: "PhotoGrapher"
  icon: "T_E3_Icon_Photo_002_png"
  inLevelIcon: "T_E3_Icon_Photo_001_png"
  target: "InLevel_NR3ERoleTarget_PhotaGrapher"
  describe: "InLevel_NR3E3_VocationDes_PhotoGrapher"
  inCustomRoomIcon: "T_E3_Room_Icon_Photo_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_PhotoGrapher"
  RuleIsVisibility: 0
  SelectIdentityIcon: "T_E3_Prepare_Icon_Photo_001"
  RoleInfoPointsAddTypeId: 5
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 35
  faction: 2
  campRole: "法官"
  CampName: "平民"
  VocationName: "Judge"
  icon: "T_E3_Icon_Judge_002_png"
  inLevelIcon: "T_E3_Icon_Judge_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Judge"
  intro: "InLevel_NR3E3_VocationIntro_Judge"
  UISequence: 15
  inCustomRoomIcon: "T_E3_Room_Icon_Judge_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Judge"
  RuleIsVisibility: 1
  SelectIdentitySort: 15
  SelectIdentityIcon: "T_E3_Prepare_Icon_Judge_001"
  LowestVersion: "********"
  SelectIdentitySort_New: "15"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240803
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7003
}
rows {
  id: 36
  faction: 2
  campRole: "监听员"
  CampName: "平民"
  VocationName: "Listener"
  icon: "T_E3_Icon_Monitor_002_png"
  inLevelIcon: "T_E3_Icon_Monitor_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Listener"
  intro: "InLevel_NR3E3_VocationIntro_Listener"
  UISequence: 16
  inCustomRoomIcon: "T_E3_Room_Icon_Monitor_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Listener"
  RuleIsVisibility: 1
  SelectIdentitySort: 16
  SelectIdentityIcon: "T_E3_Prepare_Icon_Monitor_001"
  LowestVersion: "********"
  SelectIdentitySort_New: "16"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240804
  Preparations_JumpId: 330
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7003
}
rows {
  id: 37
  faction: 1
  campRole: "闪光狼"
  CampName: "狼人"
  VocationName: "FlashWolf"
  icon: "T_E3_Icon_FlashWolf_002_png"
  inLevelIcon: "T_E3_Icon_FlashWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_FlashWolf"
  intro: "InLevel_NR3E3_VocationIntro_FlashWolf"
  UISequence: 110
  inCustomRoomIcon: "T_E3_Room_Icon_FlashWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_FlashWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 110
  SelectIdentityIcon: "T_E3_Prepare_Icon_FlashWolf_001"
  LowestVersion: "********"
  SelectIdentitySort_New: "110"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240805
  Preparations_JumpId: 330
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7106
}
rows {
  id: 38
  faction: 1
  campRole: "幽灵狼"
  CampName: "狼人"
  VocationName: "GhostWolf"
  icon: "T_E3_Icon_GhostWolf_002_png"
  inLevelIcon: "T_E3_Icon_GhostWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_GhostWolf"
  intro: "InLevel_NR3E3_VocationIntro_GhostWolf"
  UISequence: 112
  inCustomRoomIcon: "T_E3_Room_Icon_GhostWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_GhostWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 112
  SelectIdentityIcon: "T_E3_Prepare_Icon_GhostWolf_001"
  SelectIdentitySort_New: "112"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240806
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7106
}
rows {
  id: 39
  faction: 2
  campRole: "通灵师"
  CampName: "平民"
  VocationName: "Psychic"
  icon: "T_E3_Icon_Shaman_002_png"
  inLevelIcon: "T_E3_Icon_Shaman_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Psychic"
  intro: "InLevel_NR3E3_VocationIntro_Psychic"
  UISequence: 14
  inCustomRoomIcon: "T_E3_Room_Icon_Shaman_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Psychic"
  RuleIsVisibility: 1
  SelectIdentitySort: 14
  SelectIdentityIcon: "T_E3_Prepare_Icon_Shaman_001"
  SelectIdentitySort_New: "14"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7003
}
rows {
  id: 40
  faction: 1
  campRole: "假面狼"
  CampName: "狼人"
  VocationName: "MaskWolf"
  icon: "T_E3_Icon_MaskedWolf_002_png"
  inLevelIcon: "T_E3_Icon_MaskedWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_MaskWolf"
  intro: "InLevel_NR3E3_VocationIntro_MaskWolf"
  UISequence: 117
  inCustomRoomIcon: "T_E3_Room_Icon_MaskedWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_MaskWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 117
  SelectIdentityIcon: "T_E3_Prepare_Icon_MaskedWolf_001"
  LowestVersion: "1.3.37.1"
  SelectIdentitySort_New: "117"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240807
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7110
}
rows {
  id: 41
  faction: 2
  campRole: "千里眼"
  CampName: "平民"
  VocationName: "EagleEye"
  icon: "T_E3_Icon_Clairvoyant_002_png"
  inLevelIcon: "T_E3_Icon_Clairvoyant_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_EagleEye"
  intro: "InLevel_NR3E3_VocationIntro_EagleEye"
  UISequence: 17
  inCustomRoomIcon: "T_E3_Room_Icon_Clairvoyant_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_EagleEye"
  RuleIsVisibility: 1
  SelectIdentitySort: 17
  SelectIdentityIcon: "T_E3_Prepare_Icon_Clairvoyant_001"
  LowestVersion: "********"
  SelectIdentitySort_New: "17"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240808
  Preparations_JumpId: 330
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7003
}
rows {
  id: 42
  faction: 2
  campRole: "黑客"
  CampName: "平民"
  VocationName: "Hacker"
  icon: "T_E3_Icon_Hacker_002_png"
  inLevelIcon: "T_E3_Icon_Hacker_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Hacker"
  intro: "InLevel_NR3E3_VocationIntro_Hacker"
  UISequence: 18
  inCustomRoomIcon: "T_E3_Room_Icon_Hacker_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Hacker"
  RuleIsVisibility: 1
  SelectIdentitySort: 18
  SelectIdentityIcon: "T_E3_Prepare_Icon_Hacker_001"
  LowestVersion: "********"
  SelectIdentitySort_New: "18"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240809
  Preparations_JumpId: 330
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7004
}
rows {
  id: 43
  faction: 2
  campRole: "追踪者"
  CampName: "平民"
  VocationName: "Tracker"
  icon: "T_E3_Icon_Tracer_002_png"
  inLevelIcon: "T_E3_Icon_Tracer_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Tracker"
  intro: "InLevel_NR3E3_VocationIntro_Tracker"
  UISequence: 21
  inCustomRoomIcon: "T_E3_Room_Icon_Tracer_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Tracker"
  RuleIsVisibility: 1
  SelectIdentitySort: 21
  SelectIdentityIcon: "T_E3_Prepare_Icon_Tracer_001"
  SelectIdentitySort_New: "21"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240813
  Preparations_JumpId: 330
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7004
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 44
  faction: 2
  campRole: "保镖"
  CampName: "平民"
  VocationName: "Defender"
  icon: "T_E3_Icon_BodyGuard_002_png"
  inLevelIcon: "T_E3_Icon_BodyGuard_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Defender"
  intro: "InLevel_NR3E3_VocationIntro_Defender"
  UISequence: 19
  inCustomRoomIcon: "T_E3_Room_Icon_BodyGuard_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Defender"
  RuleIsVisibility: 1
  SelectIdentitySort: 19
  SelectIdentityIcon: "T_E3_Prepare_Icon_BodyGuard_001"
  SelectIdentitySort_New: "19"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240810
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_5"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7004
}
rows {
  id: 45
  faction: 1
  campRole: "密道狼"
  CampName: "狼人"
  VocationName: "HoleWolf"
  icon: "T_E3_Icon_SecretWolf_002_png"
  inLevelIcon: "T_E3_Icon_SecretWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_HoleWolf"
  intro: "InLevel_NR3E3_VocationIntro_HoleWolf"
  UISequence: 111
  inCustomRoomIcon: "T_E3_Room_Icon_SecretWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_HoleWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 111
  SelectIdentityIcon: "T_E3_Prepare_Icon_SecretWolf_001"
  SelectIdentitySort_New: "111"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240815
  Preparations_JumpId: 330
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7107
}
rows {
  id: 46
  faction: 2
  campRole: "管道工"
  CampName: "平民"
  VocationName: "Plumber"
  icon: "T_E3_Icon_PipeMan_002_png"
  inLevelIcon: "T_E3_Icon_PipeMan_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Plumber"
  intro: "InLevel_NR3E3_VocationIntro_Plumber"
  UISequence: 20
  inCustomRoomIcon: "T_E3_Room_Icon_PipeMan_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Plumber"
  RuleIsVisibility: 1
  SelectIdentitySort: 20
  SelectIdentityIcon: "T_E3_Prepare_Icon_PipeMan_001"
  SelectIdentitySort_New: "20"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240816
  Preparations_JumpId: 330
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7004
}
rows {
  id: 47
  faction: 3
  campRole: "捕梦者"
  CampName: "中立"
  VocationName: "Dreamcatcher"
  icon: "T_E3_Icon_DreamCatcher_002_png"
  inLevelIcon: "T_E3_Icon_DreamCatcher_001_png"
  target: "InLevel_NR3E3_VocationDes_DreamCatcher"
  describe: "InLevel_NR3E3_VocationDes_DreamCatcher"
  intro: "UI_InLevel_NR3E3_Rule3_RoleInfo20"
  UISequence: 209
  inCustomRoomIcon: "T_E3_Room_Icon_DreamCatcher_001_png"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo20"
  RuleIsVisibility: 1
  SelectIdentitySort: 209
  SelectIdentityIcon: "T_E3_Prepare_Icon_DreamCatcher_001"
  SelectIdentitySort_New: "209"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240814
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 3
  RandomVocationGroupID: 7205
}
rows {
  id: 48
  faction: 1
  campRole: "契约狼"
  CampName: "狼人"
  VocationName: "DeedWolf"
  icon: "T_E3_Icon_SoullessWolf_002_png"
  inLevelIcon: "T_E3_Icon_SoullessWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_DeedWolf"
  intro: "InLevel_NR3E3_VocationIntro_DeedWolf"
  UISequence: 113
  inCustomRoomIcon: "T_E3_Room_Icon_SoullessWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_DeedWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 113
  SelectIdentityIcon: "T_E3_Prepare_Icon_SoullessWolf_001"
  SelectIdentitySort_New: "113"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240812
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_5"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7108
}
rows {
  id: 49
  faction: 2
  campRole: "杂耍艺人"
  CampName: "平民"
  VocationName: "Juggler"
  icon: "T_E3_Icon_Juggler_002_png"
  inLevelIcon: "T_E3_Icon_Juggler_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Juggler"
  intro: "InLevel_NR3E3_VocationIntro_Juggler"
  UISequence: 22
  inCustomRoomIcon: "T_E3_Room_Icon_Juggler_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Juggler"
  RuleIsVisibility: 1
  SelectIdentityIcon: "T_E3_Prepare_Icon_Juggler_001"
  LowestVersion: "1.3.26.1"
  Identity_ItemUnlock: 240817
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7004
}
rows {
  id: 50
  faction: 1
  campRole: "雾隐狼"
  CampName: "狼人"
  VocationName: "FogSneakWolf"
  icon: "T_E3_Icon_FogWolf_002_png"
  inLevelIcon: "T_E3_Icon_FogWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_FogSneakWolf"
  intro: "InLevel_NR3E3_VocationDes_FogSneakWolf2"
  UISequence: 115
  inCustomRoomIcon: "T_E3_Room_Icon_FogWolf_001_png"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo21"
  RuleIsVisibility: 1
  SelectIdentitySort: 115
  SelectIdentityIcon: "T_E3_Prepare_Icon_FogWolf_001"
  LowestVersion: "1.3.26.81"
  SelectIdentitySort_New: "115"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240818
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7109
}
rows {
  id: 51
  faction: 2
  campRole: "鸵鸟"
  CampName: "平民"
  VocationName: "Ostrich"
  icon: "T_E3_Icon_Ostrich_002_png"
  inLevelIcon: "T_E3_Icon_Ostrich_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Ostrich"
  intro: "InLevel_NR3E3_VocationIntro_Ostrich"
  UISequence: 27
  inCustomRoomIcon: "T_E3_Room_Icon_Ostrich_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Ostrich"
  RuleIsVisibility: 1
  SelectIdentitySort: 27
  SelectIdentityIcon: "T_E3_Prepare_Icon_Ostrich_001"
  LowestVersion: "1.3.26.1"
  SelectIdentitySort_New: "27"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240819
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7005
}
rows {
  id: 52
  faction: 2
  campRole: "急救员"
  CampName: "平民"
  VocationName: "FirstAider"
  icon: "T_E3_Icon_Aider_002_png"
  inLevelIcon: "T_E3_Icon_Aider_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_FirstAider"
  intro: "InLevel_NR3E3_VocationIntro_FirstAider2"
  UISequence: 23
  inCustomRoomIcon: "T_E3_Room_Icon_Aider_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_FirstAider"
  RuleIsVisibility: 1
  SelectIdentitySort: 23
  SelectIdentityIcon: "T_E3_Prepare_Icon_Aider_001"
  LowestVersion: "1.3.26.1"
  SelectIdentitySort_New: "23"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240820
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7004
}
rows {
  id: 53
  faction: 1
  campRole: "舞蹈狼"
  CampName: "狼人"
  VocationName: "DanceWolf"
  icon: "T_E3_Icon_DanceWolf_002_png"
  inLevelIcon: "T_E3_Icon_DanceWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_DanceWolf"
  intro: "InLevel_NR3E3_VocationDes_DanceWolf2"
  UISequence: 118
  inCustomRoomIcon: "T_E3_Room_Icon_DanceWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_DanceWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 118
  SelectIdentityIcon: "T_E3_Prepare_Icon_DanceWolf_001"
  LowestVersion: "1.3.26.1"
  SelectIdentitySort_New: "118"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240821
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7108
}
rows {
  id: 54
  faction: 3
  campRole: " 舞者"
  CampName: "中立"
  VocationName: "Dancer"
  icon: "T_E3_Icon_Dancer_002_png"
  inLevelIcon: "T_E3_Icon_Dancer_001_png"
  target: "InLevel_NR3E3_VocationTarget_Dancer"
  describe: "InLevel_NR3E3_VocationDes_Dancer"
  intro: "InLevel_NR3E3_VocationDes_Dancer2"
  UISequence: 211
  inCustomRoomIcon: "T_E3_Room_Icon_Dancer_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Dancer"
  RuleIsVisibility: 1
  SelectIdentitySort: 217
  SelectIdentityIcon: "T_E3_Prepare_Icon_Dancer_001"
  LowestVersion: "1.3.26.1"
  SelectIdentitySort_New: "217"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240822
  RoleInfoPointsAddTypeId: 4
  RandomVocationGroupID: 7206
}
rows {
  id: 55
  faction: 3
  campRole: "快递员"
  CampName: "中立"
  VocationName: "Delivery"
  icon: "T_E3_Icon_Courier_002_png"
  inLevelIcon: "T_E3_Icon_Courier_001_png"
  target: "InLevel_NR3E3_VocationTarget_Delivery"
  describe: "InLevel_NR3E3_VocationDes_Delivery"
  intro: "InLevel_NR3E3_VocationDes_Delivery2"
  UISequence: 210
  inCustomRoomIcon: "T_E3_Room_Icon_Courier_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Delivery"
  RuleIsVisibility: 1
  SelectIdentitySort: 210
  SelectIdentityIcon: "T_E3_Prepare_Icon_Courier_001"
  LowestVersion: "1.3.26.1"
  SelectIdentitySort_New: "210"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240823
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 5
  RandomVocationGroupID: 7207
}
rows {
  id: 56
  faction: 2
  campRole: "名侦探"
  CampName: "平民"
  VocationName: "BestDetective"
  icon: "T_E3_Icon_Conan_002_png"
  inLevelIcon: "T_E3_Icon_Conan_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_BestDetective"
  intro: "InLevel_NR3E3_VocationIntro_BestDetective"
  UISequence: 24
  inCustomRoomIcon: "T_E3_Room_Icon_Conan_001_png"
  RuleDes: "InLevel_NR3E3_VocationIntro_BestDetective"
  RuleIsVisibility: 1
  SelectIdentitySort: 24
  SelectIdentityIcon: "T_E3_Prepare_Icon_Conan_001"
  SelectIdentitySort_New: "24"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240824
  RandomVocationGroupID: 7005
  SeasonTheme: "UI_Preparations_VocationItem_Season03"
}
rows {
  id: 57
  faction: 2
  campRole: "仓鼠"
  CampName: "平民"
  VocationName: "Hamster"
  icon: "T_E3_Icon_Hamster_002_png"
  inLevelIcon: "T_E3_Icon_Hamster_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Hamster"
  intro: "InLevel_NR3E3_VocationIntro_Hamster"
  UISequence: 25
  inCustomRoomIcon: "T_E3_Room_Icon_Hamster_001_png"
  RuleDes: "InLevel_NR3E3_VocationIntro_Hamster"
  RuleIsVisibility: 1
  SelectIdentitySort: 25
  SelectIdentityIcon: "T_E3_Prepare_Icon_Hamster_001"
  SelectIdentitySort_New: "25"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240825
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7005
}
rows {
  id: 58
  faction: 1
  campRole: "黑衣狼"
  CampName: "狼人"
  VocationName: "BlackWolf"
  icon: "T_E3_Icon_BlackWolf_002_png"
  inLevelIcon: "T_E3_Icon_BlackWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_BlackWolf"
  intro: "InLevel_NR3E3_VocationIntro_BlackWolf"
  UISequence: 116
  inCustomRoomIcon: "T_E3_Room_Icon_BlackWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_BlackWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 116
  SelectIdentityIcon: "T_E3_Prepare_Icon_BlackWolf_001"
  SelectIdentitySort_New: "116"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240826
  RandomVocationGroupID: 7109
  SeasonTheme: "UI_Preparations_VocationItem_Season03"
}
rows {
  id: 59
  faction: 2
  campRole: "侠客"
  CampName: "平民"
  VocationName: "SwordsMan"
  icon: "T_E3_Icon_Swordsman_002_png"
  inLevelIcon: "T_E3_Icon_Swordsman_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_SwordsMan"
  intro: "InLevel_NR3E3_VocationDes_SwordsMan2"
  UISequence: 26
  inCustomRoomIcon: "T_E3_Room_Icon_Swordsman_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_SwordsMan"
  RuleIsVisibility: 1
  SelectIdentitySort: 26
  SelectIdentityIcon: "T_E3_Prepare_Icon_Swordsman_001"
  SelectIdentitySort_New: "26"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240827
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7005
}
rows {
  id: 60
  faction: 3
  campRole: "密探"
  CampName: "中立"
  VocationName: "SecretDetective"
  icon: "T_E3_Icon_Spy_002_png"
  inLevelIcon: "T_E3_Icon_Spy_001_png"
  target: "InLevel_NR3E3_IdentityInformation16"
  describe: "InLevel_NR3E3_Rule69"
  intro: "InLevel_NR3E3_IdentityInformation16"
  UISequence: 212
  inCustomRoomIcon: "T_E3_Room_Icon_Spy_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle34"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo22"
  RuleIsVisibility: 1
  SelectIdentitySort: 212
  SelectIdentityIcon: "T_E3_Prepare_Icon_Spy_001"
  SelectIdentitySort_New: "212"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240828
  RandomVocationGroupID: 7208
  SeasonTheme: "UI_Preparations_VocationItem_Season03"
}
rows {
  id: 61
  faction: 2
  campRole: "复仇者"
  CampName: "平民"
  VocationName: "Avengers"
  icon: "T_E3_Icon_Revenge_002_png"
  inLevelIcon: "T_E3_Icon_Revenge_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Avengers"
  intro: "InLevel_NR3E3_VocationDes_Avengers2"
  UISequence: 27
  inCustomRoomIcon: "T_E3_Room_Icon_Revenge_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Avengers"
  RuleIsVisibility: 1
  SelectIdentitySort: 27
  SelectIdentityIcon: "T_E3_Prepare_Icon_Revenge_001"
  SelectIdentitySort_New: "27"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240829
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7006
}
rows {
  id: 62
  faction: 3
  campRole: "闹闹"
  CampName: "中立"
  VocationName: "NaoNao"
  icon: "T_E3_Icon_NaoNao_002_png"
  inLevelIcon: "T_E3_Icon_NaoNao_001_png"
  target: "InLevel_NR3ERoleTarget_NaoNao"
  describe: "InLevel_NR3E3_VocationDes_NaoNao"
  intro: "InLevel_NR3E3_VocationDes_NaoNao2"
  UISequence: 213
  inCustomRoomIcon: "T_E3_Room_Icon_NaoNao_001_png"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo23"
  RuleIsVisibility: 1
  SelectIdentitySort: 213
  SelectIdentityIcon: "T_E3_Prepare_Icon_NaoNao_001"
  SelectIdentitySort_New: "213"
  RandomVocationGroupID: 7205
}
rows {
  id: 63
  faction: 2
  campRole: "烟花师"
  CampName: "平民"
  VocationName: "Fireworks"
  icon: "T_E3_Icon_Firework_002_png"
  inLevelIcon: "T_E3_Icon_Firework_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Fireworks"
  intro: "InLevel_NR3E3_VocationIntro_Fireworks"
  UISequence: 28
  inCustomRoomIcon: "T_E3_Room_Icon_Firework_001_png"
  RuleName: "UI_InLevel_NR3E3_RuleTitle15"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo24"
  RuleIsVisibility: 1
  SelectIdentitySort: 28
  SelectIdentityIcon: "T_E3_Prepare_Icon_Firework_001"
  SelectIdentitySort_New: "28"
  RandomVocationGroupID: 7006
}
rows {
  id: 64
  faction: 1
  campRole: "雪狼"
  CampName: "狼人"
  VocationName: "SnowWolf"
  icon: "T_E3_Icon_SnowWolf_002_png"
  inLevelIcon: "T_E3_Icon_SnowWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_SnowWolf"
  intro: "InLevel_NR3E3_VocationDes_SnowWolf2"
  UISequence: 119
  inCustomRoomIcon: "T_E3_Room_Icon_SnowWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_SnowWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 119
  SelectIdentityIcon: "T_E3_Prepare_Icon_SnowWolf_001"
  SelectIdentitySort_New: "119"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240830
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7107
}
rows {
  id: 65
  faction: 2
  campRole: "双子"
  CampName: "平民"
  VocationName: "TwinAlive"
  icon: "T_E3_Icon_Gemini_002_png"
  inLevelIcon: "T_E3_Icon_Gemini_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_TwinAlive"
  intro: "InLevel_NR3E3_VocationIntro_TwinAlive"
  UISequence: 29
  inCustomRoomIcon: "T_E3_Room_Icon_Gemini_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_TwinAlive"
  RuleIsVisibility: 1
  SelectIdentitySort: 29
  SelectIdentityIcon: "T_E3_Prepare_Icon_Gemini_001"
  SelectIdentitySort_New: "29"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240831
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7006
}
rows {
  id: 66
  faction: 2
  campRole: "袋鼠"
  CampName: "平民"
  VocationName: "Kangaroo"
  icon: "T_E3_Icon_Kangaroo_002_png"
  inLevelIcon: "T_E3_Icon_Kangaroo_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Kangaroo"
  intro: "InLevel_NR3E3_VocationIntro_Kangaroo"
  UISequence: 30
  inCustomRoomIcon: "T_E3_Room_Icon_Kangaroo_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Kangaroo"
  RuleIsVisibility: 1
  SelectIdentitySort: 30
  SelectIdentityIcon: "T_E3_Prepare_Icon_Kangaroo_001"
  SelectIdentitySort_New: "30"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240832
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7006
}
rows {
  id: 67
  faction: 3
  campRole: "年年"
  CampName: "中立"
  VocationName: "NianNian"
  icon: "T_E3_Icon_NianNian_002_png"
  inLevelIcon: "T_E3_Icon_NianNian_001_png"
  target: "InLevel_NR3ERoleTarget_NianNian"
  describe: "InLevel_NR3E3_VocationDes_NianNian"
  intro: "InLevel_NR3E3_VocationIntro_NianNian"
  UISequence: 214
  inCustomRoomIcon: "T_E3_Room_Icon_NianNian_001_png"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo25"
  RuleIsVisibility: 1
  SelectIdentitySort: 214
  SelectIdentityIcon: "T_E3_Prepare_Icon_NianNian_001"
  SelectIdentitySort_New: "214"
  RandomVocationGroupID: 7206
}
rows {
  id: 68
  faction: 2
  campRole: "墨鱼"
  CampName: "平民"
  VocationName: "Cuttlefish"
  icon: "T_E3_Icon_Cuttlefish_002_png"
  inLevelIcon: "T_E3_Icon_Cuttlefish_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Cuttlefish"
  intro: "InLevel_NR3E3_VocationIntro_Cuttlefish"
  UISequence: 31
  inCustomRoomIcon: "T_E3_Room_Icon_Cuttlefish_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Cuttlefish"
  RuleIsVisibility: 1
  SelectIdentitySort: 31
  SelectIdentityIcon: "T_E3_Prepare_Icon_Cuttlefish_001"
  SelectIdentitySort_New: "31"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240833
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7007
}
rows {
  id: 69
  faction: 1
  campRole: "陷阱狼"
  CampName: "狼人"
  VocationName: "TrapWolf"
  icon: "T_E3_Icon_TrapWolf_002_png"
  inLevelIcon: "T_E3_Icon_TrapWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_TrapWolf"
  intro: "InLevel_NR3E3_VocationDes_TrapWolf2"
  UISequence: 120
  inCustomRoomIcon: "T_E3_Room_Icon_TrapWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_TrapWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 120
  SelectIdentityIcon: "T_E3_Prepare_Icon_TrapWolf_001"
  SelectIdentitySort_New: "120"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240834
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7111
}
rows {
  id: 70
  faction: 2
  campRole: "预言家"
  CampName: "平民"
  VocationName: "Prophet"
  icon: "T_E3_Icon_Prophet_002_png"
  inLevelIcon: "T_E3_Icon_Prophet_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Prophet1"
  intro: "InLevel_NR3E3_VocationDes_Prophet2"
  UISequence: 32
  inCustomRoomIcon: "T_E3_Room_Icon_Prophet_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Prophet1"
  RuleIsVisibility: 1
  SelectIdentitySort: 32
  SelectIdentityIcon: "T_E3_Prepare_Icon_Prophet_001"
  SelectIdentitySort_New: "32"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240835
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7006
}
rows {
  id: 71
  faction: 2
  campRole: "小骨"
  CampName: "平民"
  VocationName: "LittleBone"
  icon: "T_E3_Icon_Ossicle_002_png"
  inLevelIcon: "T_E3_Icon_Ossicle_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_LittleBone"
  intro: "InLevel_NR3E3_VocationIntro_LittleBone"
  UISequence: 33
  inCustomRoomIcon: "T_E3_Room_Icon_Ossicle_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_LittleBone"
  RuleIsVisibility: 1
  SelectIdentitySort: 33
  SelectIdentityIcon: "T_E3_Prepare_Icon_Ossicle_001"
  SelectIdentitySort_New: "33"
  RandomVocationGroupID: 7008
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 72
  faction: 1
  campRole: "烟雾狼"
  CampName: "狼人"
  VocationName: "SmokeWolf"
  icon: "T_E3_Icon_SmokyWolf_002_png"
  inLevelIcon: "T_E3_Icon_SmokyWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_SmokeWolf"
  intro: "InLevel_NR3E3_VocationIntro_SmokeWolf"
  UISequence: 121
  inCustomRoomIcon: "T_E3_Room_Icon_SmokyWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_SmokeWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 121
  SelectIdentityIcon: "T_E3_Prepare_Icon_SmokyWolf_001"
  SelectIdentitySort_New: "121"
  RandomVocationGroupID: 7112
}
rows {
  id: 73
  faction: 1
  campRole: "傀儡狼"
  CampName: "狼人"
  VocationName: "ControllerWolf"
  icon: "T_E3_Icon_PuppetWolf_002_png"
  inLevelIcon: "T_E3_Icon_PuppetWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_ControllerWolf"
  intro: "InLevel_NR3E3_VocationIntro_ControllerWolf"
  UISequence: 122
  inCustomRoomIcon: "T_E3_Room_Icon_PuppetWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_ControllerWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 122
  SelectIdentityIcon: "T_E3_Prepare_Icon_PuppetWolf_001"
  SelectIdentitySort_New: "122"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240836
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7112
}
rows {
  id: 74
  faction: 1
  campRole: "乱斗狼"
  CampName: "狼人"
  VocationName: "BrawlWolf"
  icon: "T_E3_Icon_BrawlerWolf_002_png"
  inLevelIcon: "T_E3_Icon_BrawlerWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_BrawlWolf"
  intro: "InLevel_NR3E3_VocationIntro_BrawlWolf"
  UISequence: 123
  inCustomRoomIcon: "T_E3_Room_Icon_BrawlerWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_BrawlWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 123
  SelectIdentityIcon: "T_E3_Prepare_Icon_BrawlerWolf_001"
  SelectIdentitySort_New: "123"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RandomVocationGroupID: 7113
}
rows {
  id: 75
  faction: 2
  campRole: "先知"
  CampName: "平民"
  VocationName: "XianZhi"
  icon: "T_E3_Icon_Seer_002_png"
  inLevelIcon: "T_E3_Icon_Seer_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_XianZhi"
  intro: "InLevel_NR3E3_VocationDes_XianZhi2"
  UISequence: 34
  inCustomRoomIcon: "T_E3_Room_Icon_Seer_001_png"
  RuleDes: "UI_InLevel_NR3E3_Rule3_RoleInfo26"
  RuleIsVisibility: 1
  SelectIdentitySort: 34
  SelectIdentityIcon: "T_E3_Prepare_Icon_Seer_001"
  SelectIdentitySort_New: "34"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240837
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7009
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 76
  faction: 3
  campRole: "阴谋家"
  CampName: "中立"
  VocationName: "Conspirators"
  icon: "T_E3_Icon_Schemer_002_png"
  inLevelIcon: "T_E3_Icon_Schemer_001_png"
  target: "InLevel_NR3ERoleTarget_Conspirators"
  describe: "InLevel_NR3E3_VocationDes_Conspirators"
  intro: "InLevel_NR3E3_VocationIntro_Conspirators"
  UISequence: 215
  inCustomRoomIcon: "T_E3_Room_Icon_Schemer_001_png"
  RuleDes: "InLevel_NR3E3_VocationIntro_Conspirators"
  RuleIsVisibility: 1
  SelectIdentitySort: 215
  SelectIdentityIcon: "T_E3_Prepare_Icon_Schemer_001"
  SelectIdentitySort_New: "215"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240838
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 4
  RandomVocationGroupID: 7209
}
rows {
  id: 77
  faction: 2
  campRole: "小福星"
  CampName: "平民"
  VocationName: "LuckyStar"
  icon: "T_E3_Icon_LuckyStar_002_png"
  inLevelIcon: "T_E3_Icon_LuckyStar_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_LuckyStar"
  intro: "InLevel_NR3E3_VocationDes_LuckyStar1"
  UISequence: 35
  inCustomRoomIcon: "T_E3_Icon_LuckyStar_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_LuckyStar"
  RuleIsVisibility: 1
  SelectIdentitySort: 35
  SelectIdentityIcon: "T_E3_Prepare_Icon_LuckyStar_001"
  SelectIdentitySort_New: "35"
  Identity_ItemUnlock: 240839
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_5"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7009
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 78
  faction: 1
  campRole: "双刀狼"
  CampName: "狼人"
  VocationName: "DoubleAttackWolf"
  icon: "T_E3_Icon_DoubleWolf_002_png"
  inLevelIcon: "T_E3_Icon_DoubleWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_DoubleAttackWolf"
  intro: "InLevel_NR3E3_VocationIntro_DoubleAttackWolf"
  UISequence: 124
  inCustomRoomIcon: "T_E3_Room_Icon_DoubleWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_DoubleAttackWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 124
  SelectIdentityIcon: "T_E3_Prepare_Icon_DoubleWolf_001"
  SelectIdentitySort_New: "124"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240840
  Preparations_JumpId: 331
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_2"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7114
}
rows {
  id: 79
  faction: 3
  campRole: "毒刺"
  CampName: "中立"
  VocationName: "StingWolf"
  icon: "T_E3_Icon_Stab_002_png"
  inLevelIcon: "T_E3_Icon_Stab_001_png"
  target: "InLevel_NR3ERoleTarget_StingWolf"
  describe: "InLevel_NR3E3_VocationDes_StingWolf"
  intro: "InLevel_NR3E3_VocationIntro_StingWolf"
  UISequence: 216
  inCustomRoomIcon: "T_E3_Room_Icon_Stab_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_StingWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 216
  SelectIdentityIcon: "T_E3_Prepare_Icon_Stab_001"
  SelectIdentitySort_New: "216"
  Identity_ItemUnlock: 240841
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_5"
  RoleInfoPointsAddTypeId: 4
  RandomVocationGroupID: 7210
}
rows {
  id: 80
  faction: 1
  campRole: "咒术狼"
  CampName: "狼人"
  VocationName: "CurseWolf"
  icon: "T_E3_Icon_SpellWolf_002_png"
  inLevelIcon: "T_E3_Icon_SpellWolf_001_png"
  target: "InLevel_NR3ERoleTarget_Hunter"
  describe: "InLevel_NR3E3_VocationDes_CurseWolf"
  intro: "InLevel_NR3E3_VocationIntro_CurseWolf"
  UISequence: 125
  inCustomRoomIcon: "T_E3_Room_Icon_SpellWolf_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_CurseWolf"
  RuleIsVisibility: 1
  SelectIdentitySort: 125
  SelectIdentityIcon: "T_E3_Prepare_Icon_SpellWolf_001"
  SelectIdentitySort_New: "125"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240842
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 2
  RandomVocationGroupID: 7114
}
rows {
  id: 81
  faction: 2
  campRole: "讲师"
  CampName: "平民"
  VocationName: "Teacher"
  icon: "T_E3_Icon_Lecturer_002_png"
  inLevelIcon: "T_E3_Icon_Lecturer_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Teacher"
  intro: "InLevel_NR3E3_VocationIntro_Teacher"
  UISequence: 36
  inCustomRoomIcon: "T_E3_Room_Icon_Lecturer_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Teacher"
  RuleIsVisibility: 1
  SelectIdentitySort: 36
  SelectIdentityIcon: "T_E3_Prepare_Icon_Lecturer_001"
  SelectIdentitySort_New: "36"
  Identity_ItemUnlock: 240843
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_5"
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7010
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 82
  faction: 3
  campRole: "特工"
  CampName: "中立"
  VocationName: "Agent"
  icon: "T_E3_Icon_Agent_002_png"
  inLevelIcon: "T_E3_Icon_Agent_001_png"
  target: "InLevel_NR3E3_RoloTarget_Agent"
  describe: "InLevel_NR3E3_VocationDes_Agent"
  intro: "InLevel_NR3E3_VocationIntro_Agent"
  UISequence: 217
  inCustomRoomIcon: "T_E3_Room_Icon_Agent_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_Agent"
  RuleIsVisibility: 1
  SelectIdentitySort: 217
  SelectIdentityIcon: "T_E3_Prepare_Icon_Agent_001"
  SelectIdentitySort_New: "217"
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240844
  Preparations_JumpId: 313
  Preparations_JumpDes: "UI_Preparations_NR3E3_Jump2MasterRoad_3"
  RoleInfoPointsAddTypeId: 4
  RandomVocationGroupID: 7210
}
rows {
  id: 83
  faction: 3
  campRole: "伪装者"
  CampName: "中立"
  VocationName: "Weizhuang"
  icon: "T_E3_Icon_Warrior_002_png"
  inLevelIcon: "T_E3_Icon_Warrior_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_Weizhuang"
  intro: "InLevel_NR3E3_VocationIntro_Weizhuang"
  UISequence: 8
  inCustomRoomIcon: "T_E3_Room_Icon_Warrior_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_LieRen"
  RuleIsVisibility: 1
  SelectIdentitySort: 218
  SelectIdentityIcon: "T_E3_Prepare_Icon_Warrior_001"
  SelectIdentitySort_New: "218"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
  Preparations_TreasureHuntDefaultUnLock: 1
}
rows {
  id: 84
  faction: 2
  campRole: "猎人"
  CampName: "平民"
  VocationName: "LieRen"
  icon: "T_E3_Icon_Warrior_002_png"
  inLevelIcon: "T_E3_Icon_Warrior_001_png"
  target: "InLevel_NR3ERoleTarget_Common"
  describe: "InLevel_NR3E3_VocationDes_LieRen"
  intro: "InLevel_NR3E3_VocationIntro_LieRen"
  UISequence: 8
  inCustomRoomIcon: "T_E3_Room_Icon_Warrior_001_png"
  RuleDes: "InLevel_NR3E3_VocationDes_LieRen"
  RuleIsVisibility: 1
  SelectIdentitySort: 8
  SelectIdentityIcon: "T_E3_Prepare_Icon_Warrior_001"
  SelectIdentitySort_New: "8"
  Preparations_DefaultUnLock: 1
  Preparations_DefaultShow: 1
  Identity_ItemUnlock: 240845
  RoleInfoPointsAddTypeId: 1
  RandomVocationGroupID: 7002
  Preparations_TreasureHuntDefaultUnLock: 1
}
