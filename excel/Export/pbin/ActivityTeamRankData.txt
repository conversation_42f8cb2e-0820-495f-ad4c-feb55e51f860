com.tencent.wea.xlsRes.table_ActivityTeamRankData
excel/xls/H_活动_组队排位.xlsx sheet:组队排位
rows {
  id: 1
  activityId: 667
  teamRankActivityType: TRAT_LoseProtect
  times: 3
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 2
  activityId: 667
  teamRankActivityType: TRAT_Additional
  params: 10
  times: 1
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 3
  activityId: 398
  teamRankActivityType: TRAT_LoseProtect
  times: 3
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 4
  activityId: 398
  teamRankActivityType: TRAT_Additional
  params: 10
  times: 1
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 5
  activityId: 425
  teamRankActivityType: TRAT_LoseProtect
  times: 3
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 6
  activityId: 425
  teamRankActivityType: TRAT_Additional
  params: 10
  times: 1
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 7
  activityId: 473
  teamRankActivityType: TRAT_LoseProtect
  times: 3
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 8
  activityId: 473
  teamRankActivityType: TRAT_Additional
  params: 10
  times: 1
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 9
  activityId: 502
  teamRankActivityType: TRAT_LoseProtect
  times: 3
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 10
  activityId: 502
  teamRankActivityType: TRAT_Additional
  params: 10
  times: 1
  resetType: ART_Daily
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 2519
}
rows {
  id: 11
  activityId: 558
  teamRankActivityType: TRAT_LoseProtect
  times: 7
  resetType: ART_Weekly
  matchTypeId: 4
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 1
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 1520
  qualifyIntegralRange: 2519
  desc: "赛季限定冲分增益{0}/{1}"
}
rows {
  id: 12
  activityId: 558
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 7
  resetType: ART_Weekly
  matchTypeId: 4
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 1
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 1519
  desc: "赛季限定冲分增益{0}/{1}"
}
rows {
  id: 13
  activityId: 30036
  teamRankActivityType: TRAT_LoseProtect
  times: 5
  resetType: ART_Daily
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 14
  activityId: 30053
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
  desc: "峡谷开黑排位分翻倍{0}/{1}"
}
rows {
  id: 15
  activityId: 30092
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 5
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
  desc: "峡谷开黑排位分翻倍{0}/{1}"
}
rows {
  id: 16
  activityId: 30106
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
  desc: "峡谷开黑排位分翻倍{0}/{1}"
}
rows {
  id: 17
  activityId: 30116
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
  desc: "峡谷开黑排位分翻倍{0}/{1}"
}
rows {
  id: 18
  activityId: 30127
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 19
  activityId: 30127
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 20
  activityId: 30137
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 21
  activityId: 30137
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 22
  activityId: 30152
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 23
  activityId: 30152
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 24
  activityId: 30156
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_NoReset
  matchTypeId: 109
  teamMemberCount: 2
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 25
  activityId: 30185
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 26
  activityId: 30185
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  teamMemberCount: 2
  teamMemberCount: 3
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 27
  activityId: 30198
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 28
  activityId: 30198
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 32
  activityId: 30242
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 33
  activityId: 30244
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  matchTypeId: 352
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 34
  activityId: 30244
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  matchTypeId: 352
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 35
  activityId: 30252
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 352
  teamMemberCount: 1
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 36
  activityId: 30235
  teamRankActivityType: TRAT_LoseProtect
  times: 7
  resetType: ART_Weekly
  matchTypeId: 4
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 1
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 1520
  qualifyIntegralRange: 2519
  desc: "春节BUFF不掉分{0}/{1}"
}
rows {
  id: 37
  activityId: 30235
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 7
  resetType: ART_Weekly
  matchTypeId: 4
  matchTypeId: 5
  matchTypeId: 6
  teamMemberCount: 1
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 1519
  desc: "春节BUFF上分翻倍{0}/{1}"
}
rows {
  id: 40
  activityId: 30313
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 41
  activityId: 30317
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 42
  activityId: 30320
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 43
  activityId: 30323
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 44
  activityId: 30325
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 45
  activityId: 30344
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 46
  activityId: 30353
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 354
  matchTypeId: 352
  teamMemberCount: 1
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 47
  activityId: 30353
  teamRankActivityType: TRAT_IntimacyMag
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 354
  matchTypeId: 352
  teamMemberCount: 1
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 48
  activityId: 30360
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 5601
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
rows {
  id: 49
  activityId: 30426
  teamRankActivityType: TRAT_MultiScore
  params: 100
  times: 3
  resetType: ART_Daily
  matchTypeId: 6102
  teamMemberCount: 2
  teamMemberCount: 3
  teamMemberCount: 4
  teamMemberCount: 5
  startTime {
  }
  endTime {
    seconds: 86340
  }
  qualifyIntegralRange: 0
  qualifyIntegralRange: 99999
}
