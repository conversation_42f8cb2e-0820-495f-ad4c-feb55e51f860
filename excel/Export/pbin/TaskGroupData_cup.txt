com.tencent.wea.xlsRes.table_TaskGroup
excel/xls/J_奖杯征程.xlsx sheet:奖杯任务组
rows {
  id: 200001
  type: TaskType_Daily
  taskIdList: 200001001
  taskIdList: 200001002
  taskIdList: 200001003
  taskIdList: 200001004
  taskIdList: 200001006
  taskIdList: 200001007
  taskIdList: 200001008
  taskIdList: 200001009
  taskIdList: 200001010
  taskIdList: 200001017
  taskIdList: 200001018
  taskIdList: 200001019
  taskIdList: 200001020
  taskIdList: 200001021
  taskIdList: 200001022
  taskIdList: 200001023
  taskIdList: 200001024
  taskIdList: 200001025
  taskIdList: 200001026
  taskIdList: 200001027
  taskIdList: 200001028
  taskIdList: 200001029
  taskIdList: 200001031
  taskIdList: 200001032
  taskIdList: 200001034
  taskIdList: 200001035
  taskIdList: 200001036
  taskIdList: 200001037
  taskIdList: 400001076
  groupName: "日常奖杯任务"
}
rows {
  id: 300001
  type: TaskType_Weekly
  taskIdList: 300001001
  taskIdList: 300001002
  taskIdList: 300001003
  taskIdList: 300001004
  taskIdList: 300001005
  taskIdList: 300001009
  taskIdList: 300001010
  taskIdList: 300001011
  taskIdList: 300001012
  taskIdList: 300001013
  taskIdList: 300001015
  taskIdList: 300001016
  taskIdList: 300001017
  taskIdList: 300001018
  taskIdList: 300001019
  taskIdList: 300001020
  taskIdList: 300001021
  taskIdList: 300001022
  taskIdList: 300001023
  taskIdList: 300001024
  taskIdList: 300001025
  taskIdList: 300001026
  taskIdList: 300001027
  taskIdList: 300001031
  taskIdList: 300001032
  taskIdList: 300001033
  taskIdList: 300001034
  taskIdList: 300001035
  taskIdList: 300001036
  taskIdList: 300001037
  taskIdList: 300001038
  taskIdList: 300001039
  taskIdList: 300001040
  taskIdList: 300001041
  taskIdList: 300001042
  taskIdList: 300001043
  taskIdList: 300001044
  taskIdList: 300001045
  taskIdList: 300001046
  taskIdList: 300001047
  groupName: "周常奖杯任务"
}
rows {
  id: 400001
  type: TaskType_Daily
  beginShowTime {
    seconds: 1724601600
  }
  endShowTime {
    seconds: 1725206399
  }
  beginDoTime {
    seconds: 1724601600
  }
  endDoTime {
    seconds: 1725206399
  }
  taskIdList: 400001001
  taskIdList: 400001002
  taskIdList: 400001003
  groupName: "每日限时奖杯"
}
rows {
  id: 400002
  type: TaskType_Daily
  beginShowTime {
    seconds: 1724342400
  }
  endShowTime {
    seconds: 1724601599
  }
  beginDoTime {
    seconds: 1724342400
  }
  endDoTime {
    seconds: 1724601599
  }
  taskIdList: 400001004
  groupName: "每日限时奖杯"
}
rows {
  id: 400003
  type: TaskType_Weekly
  beginShowTime {
    seconds: 1724342400
  }
  endShowTime {
    seconds: 1724601599
  }
  beginDoTime {
    seconds: 1724342400
  }
  endDoTime {
    seconds: 1724601599
  }
  taskIdList: 400001005
  taskIdList: 400001006
  taskIdList: 400001007
  groupName: "每周限时奖杯"
}
rows {
  id: 400004
  type: TaskType_Daily
  beginShowTime {
    seconds: 1726329600
  }
  endShowTime {
    seconds: 1726588799
  }
  beginDoTime {
    seconds: 1726329600
  }
  endDoTime {
    seconds: 1726588799
  }
  taskIdList: 400001009
  groupName: "每日限时奖杯"
}
rows {
  id: 400005
  type: TaskType_Daily
  beginShowTime {
    seconds: 1726761600
  }
  endShowTime {
    seconds: 1727020799
  }
  beginDoTime {
    seconds: 1726761600
  }
  endDoTime {
    seconds: 1727020799
  }
  taskIdList: 400001010
  taskIdList: 400001014
  groupName: "每日限时奖杯"
}
rows {
  id: 400006
  type: TaskType_Daily
  beginShowTime {
    seconds: 1727366400
  }
  endShowTime {
    seconds: 1727539199
  }
  beginDoTime {
    seconds: 1727366400
  }
  endDoTime {
    seconds: 1727539199
  }
  taskIdList: 400001011
  taskIdList: 400001015
  groupName: "每日限时奖杯"
}
rows {
  id: 400007
  type: TaskType_Daily
  beginShowTime {
    seconds: 1727712000
  }
  endShowTime {
    seconds: 1728316799
  }
  beginDoTime {
    seconds: 1727712000
  }
  endDoTime {
    seconds: 1728316799
  }
  taskIdList: 400001012
  taskIdList: 400001016
  taskIdList: 400001017
  taskIdList: 400001018
  groupName: "每日限时奖杯"
}
rows {
  id: 400008
  type: TaskType_Daily
  beginShowTime {
    seconds: 1728576000
  }
  endShowTime {
    seconds: 1730390399
  }
  beginDoTime {
    seconds: 1728576000
  }
  endDoTime {
    seconds: 1730390399
  }
  taskIdList: 400001008
  groupName: "每日限时奖杯（每周五-周日循环）"
}
rows {
  id: 400009
  type: TaskType_Common
  beginShowTime {
    seconds: 1727712000
  }
  endShowTime {
    seconds: 1728316799
  }
  beginDoTime {
    seconds: 1727712000
  }
  endDoTime {
    seconds: 1728316799
  }
  taskIdList: 400001019
  groupName: "一次性限时奖杯任务"
}
rows {
  id: 400010
  type: TaskType_Daily
  beginShowTime {
    seconds: 1729440000
  }
  endShowTime {
    seconds: 1729785599
  }
  beginDoTime {
    seconds: 1729440000
  }
  endDoTime {
    seconds: 1729785599
  }
  taskIdList: 400001020
  groupName: "每日限时奖杯"
}
rows {
  id: 400011
  type: TaskType_Daily
  beginShowTime {
    seconds: 1730390400
  }
  endShowTime {
    seconds: 1730649599
  }
  beginDoTime {
    seconds: 1730390400
  }
  endDoTime {
    seconds: 1730649599
  }
  taskIdList: 400001021
  groupName: "每日限时奖杯"
}
rows {
  id: 400012
  type: TaskType_Weekly
  beginShowTime {
    seconds: 1730390400
  }
  endShowTime {
    seconds: 1730649599
  }
  beginDoTime {
    seconds: 1730390400
  }
  endDoTime {
    seconds: 1730649599
  }
  taskIdList: 400001022
  groupName: "每周限时奖杯"
}
rows {
  id: 400013
  type: TaskType_Daily
  beginShowTime {
    seconds: 1730995200
  }
  endShowTime {
    seconds: 1731254399
  }
  beginDoTime {
    seconds: 1730995200
  }
  endDoTime {
    seconds: 1731254399
  }
  taskIdList: 400001023
  groupName: "每日限时奖杯"
}
rows {
  id: 400014
  type: TaskType_Daily
  beginShowTime {
    seconds: 1731600000
  }
  endShowTime {
    seconds: 1731859199
  }
  beginDoTime {
    seconds: 1731600000
  }
  endDoTime {
    seconds: 1731859199
  }
  taskIdList: 400001024
  groupName: "每日限时奖杯"
}
rows {
  id: 400015
  type: TaskType_Weekly
  beginShowTime {
    seconds: 1731600000
  }
  endShowTime {
    seconds: 1731859199
  }
  beginDoTime {
    seconds: 1731600000
  }
  endDoTime {
    seconds: 1731859199
  }
  taskIdList: 400001025
  groupName: "每周限时奖杯"
}
rows {
  id: 400016
  type: TaskType_Daily
  beginShowTime {
    seconds: 1732204800
  }
  endShowTime {
    seconds: 1732463999
  }
  beginDoTime {
    seconds: 1732204800
  }
  endDoTime {
    seconds: 1732463999
  }
  taskIdList: 400001026
  groupName: "每日限时奖杯"
}
rows {
  id: 400017
  type: TaskType_Daily
  beginShowTime {
    seconds: 1732809600
  }
  endShowTime {
    seconds: 1733068799
  }
  beginDoTime {
    seconds: 1732809600
  }
  endDoTime {
    seconds: 1733068799
  }
  taskIdList: 400001027
  groupName: "每日限时奖杯"
}
rows {
  id: 400018
  type: TaskType_Daily
  beginShowTime {
    seconds: 1728576000
  }
  endShowTime {
    seconds: 1733068799
  }
  beginDoTime {
    seconds: 1728576000
  }
  endDoTime {
    seconds: 1733068799
  }
  taskIdList: 400001013
  groupName: "每日限时奖杯（每周五-周日循环）"
}
rows {
  id: 200002
  type: TaskType_Daily
  endShowTime {
    seconds: 1730649599
  }
  endDoTime {
    seconds: 1730649599
  }
  taskIdList: 200001033
  groupName: "日常奖杯任务"
}
rows {
  id: 200003
  type: TaskType_Daily
  beginShowTime {
    seconds: 1730649600
  }
  beginDoTime {
    seconds: 1730649600
  }
  taskIdList: 200001038
  groupName: "日常奖杯任务"
}
rows {
  id: 400019
  type: TaskType_Daily
  beginShowTime {
    seconds: 1733414400
  }
  endShowTime {
    seconds: 1733673599
  }
  beginDoTime {
    seconds: 1733414400
  }
  endDoTime {
    seconds: 1733673599
  }
  taskIdList: 400001028
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400020
  type: TaskType_Daily
  beginShowTime {
    seconds: 1733414400
  }
  endShowTime {
    seconds: 1733673599
  }
  beginDoTime {
    seconds: 1733414400
  }
  endDoTime {
    seconds: 1733673599
  }
  taskIdList: 400001029
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400021
  type: TaskType_Daily
  beginShowTime {
    seconds: 1734019200
  }
  endShowTime {
    seconds: 1734278399
  }
  beginDoTime {
    seconds: 1734019200
  }
  endDoTime {
    seconds: 1734278399
  }
  taskIdList: 400001030
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400022
  type: TaskType_Daily
  beginShowTime {
    seconds: 1734019200
  }
  endShowTime {
    seconds: 1734278399
  }
  beginDoTime {
    seconds: 1734019200
  }
  endDoTime {
    seconds: 1734278399
  }
  taskIdList: 400001031
  groupName: "每周限时奖杯"
  limitedType: 2
}
rows {
  id: 400023
  type: TaskType_Daily
  beginShowTime {
    seconds: 1734019200
  }
  endShowTime {
    seconds: 1734278399
  }
  beginDoTime {
    seconds: 1734019200
  }
  endDoTime {
    seconds: 1734278399
  }
  taskIdList: 400001032
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400024
  type: TaskType_Daily
  beginShowTime {
    seconds: 1734624000
  }
  endShowTime {
    seconds: 1734883199
  }
  beginDoTime {
    seconds: 1734624000
  }
  endDoTime {
    seconds: 1734883199
  }
  taskIdList: 400001033
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400025
  type: TaskType_Daily
  beginShowTime {
    seconds: 1734624000
  }
  endShowTime {
    seconds: 1734883199
  }
  beginDoTime {
    seconds: 1734624000
  }
  endDoTime {
    seconds: 1734883199
  }
  taskIdList: 400001034
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400026
  type: TaskType_Daily
  beginShowTime {
    seconds: 1735228800
  }
  endShowTime {
    seconds: 1735487999
  }
  beginDoTime {
    seconds: 1735228800
  }
  endDoTime {
    seconds: 1735487999
  }
  taskIdList: 400001035
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400027
  type: TaskType_Daily
  beginShowTime {
    seconds: 1735228800
  }
  endShowTime {
    seconds: 1735487999
  }
  beginDoTime {
    seconds: 1735228800
  }
  endDoTime {
    seconds: 1735487999
  }
  taskIdList: 400001036
  groupName: "每周限时奖杯"
  limitedType: 2
}
rows {
  id: 400028
  type: TaskType_Daily
  beginShowTime {
    seconds: 1735228800
  }
  endShowTime {
    seconds: 1735487999
  }
  beginDoTime {
    seconds: 1735228800
  }
  endDoTime {
    seconds: 1735487999
  }
  taskIdList: 400001037
  groupName: "每日限时奖杯"
  limitedType: 2
}
rows {
  id: 400029
  type: TaskType_Daily
  beginShowTime {
    seconds: 1735833600
  }
  endShowTime {
    seconds: 1736092799
  }
  beginDoTime {
    seconds: 1735833600
  }
  endDoTime {
    seconds: 1736092799
  }
  taskIdList: 400001038
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400030
  type: TaskType_Daily
  beginShowTime {
    seconds: 1735833600
  }
  endShowTime {
    seconds: 1736092799
  }
  beginDoTime {
    seconds: 1735833600
  }
  endDoTime {
    seconds: 1736092799
  }
  taskIdList: 400001039
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400031
  type: TaskType_Daily
  beginShowTime {
    seconds: 1736438400
  }
  endShowTime {
    seconds: 1736697599
  }
  beginDoTime {
    seconds: 1736438400
  }
  endDoTime {
    seconds: 1736697599
  }
  taskIdList: 400001040
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400032
  type: TaskType_Daily
  beginShowTime {
    seconds: 1736438400
  }
  endShowTime {
    seconds: 1736697599
  }
  beginDoTime {
    seconds: 1736438400
  }
  endDoTime {
    seconds: 1736697599
  }
  taskIdList: 400001041
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400033
  type: TaskType_Daily
  beginShowTime {
    seconds: 1737043200
  }
  endShowTime {
    seconds: 1737302399
  }
  beginDoTime {
    seconds: 1737043200
  }
  endDoTime {
    seconds: 1737302399
  }
  taskIdList: 400001042
  taskIdList: 400001049
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400034
  type: TaskType_Daily
  beginShowTime {
    seconds: 1737043200
  }
  endShowTime {
    seconds: 1737302399
  }
  beginDoTime {
    seconds: 1737043200
  }
  endDoTime {
    seconds: 1737302399
  }
  taskIdList: 400001043
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400035
  type: TaskType_Daily
  beginShowTime {
    seconds: 1737043200
  }
  endShowTime {
    seconds: 1737302399
  }
  beginDoTime {
    seconds: 1737043200
  }
  endDoTime {
    seconds: 1737302399
  }
  taskIdList: 400001044
  groupName: "每周任务奖杯"
  limitedType: 2
}
rows {
  id: 400036
  type: TaskType_Daily
  beginShowTime {
    seconds: 1737648000
  }
  endShowTime {
    seconds: 1737907199
  }
  beginDoTime {
    seconds: 1737648000
  }
  endDoTime {
    seconds: 1737907199
  }
  taskIdList: 400001045
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400037
  type: TaskType_Daily
  beginShowTime {
    seconds: 1737648000
  }
  endShowTime {
    seconds: 1737907199
  }
  beginDoTime {
    seconds: 1737648000
  }
  endDoTime {
    seconds: 1737907199
  }
  taskIdList: 400001046
  taskIdList: 400001050
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400038
  type: TaskType_Daily
  beginShowTime {
    seconds: 1738252800
  }
  endShowTime {
    seconds: 1738511999
  }
  beginDoTime {
    seconds: 1738252800
  }
  endDoTime {
    seconds: 1738511999
  }
  taskIdList: 400001047
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400039
  type: TaskType_Daily
  beginShowTime {
    seconds: 1738252800
  }
  endShowTime {
    seconds: 1738511999
  }
  beginDoTime {
    seconds: 1738252800
  }
  endDoTime {
    seconds: 1738511999
  }
  taskIdList: 400001048
  taskIdList: 400001051
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400040
  type: TaskType_Daily
  beginShowTime {
    seconds: 1738857600
  }
  endShowTime {
    seconds: 1739116799
  }
  beginDoTime {
    seconds: 1738857600
  }
  endDoTime {
    seconds: 1739116799
  }
  taskIdList: 400001052
  taskIdList: 400001053
  taskIdList: 400001054
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400041
  type: TaskType_Daily
  beginShowTime {
    seconds: 1739462400
  }
  endShowTime {
    seconds: 1739721599
  }
  beginDoTime {
    seconds: 1739462400
  }
  endDoTime {
    seconds: 1739721599
  }
  taskIdList: 400001055
  taskIdList: 400001056
  taskIdList: 400001057
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400042
  type: TaskType_Daily
  beginShowTime {
    seconds: 1740067200
  }
  endShowTime {
    seconds: 1740326399
  }
  beginDoTime {
    seconds: 1740067200
  }
  endDoTime {
    seconds: 1740326399
  }
  taskIdList: 400001058
  taskIdList: 400001059
  taskIdList: 400001060
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400043
  type: TaskType_Daily
  beginShowTime {
    seconds: 1740672000
  }
  endShowTime {
    seconds: 1740931199
  }
  beginDoTime {
    seconds: 1740672000
  }
  endDoTime {
    seconds: 1740931199
  }
  taskIdList: 400001061
  taskIdList: 400001062
  taskIdList: 400001063
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400044
  type: TaskType_Daily
  beginShowTime {
    seconds: 1741276800
  }
  endShowTime {
    seconds: 1741535999
  }
  beginDoTime {
    seconds: 1741276800
  }
  endDoTime {
    seconds: 1741535999
  }
  taskIdList: 400001064
  taskIdList: 400001065
  taskIdList: 400001066
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400045
  type: TaskType_Daily
  beginShowTime {
    seconds: 1741881600
  }
  endShowTime {
    seconds: 1742140799
  }
  beginDoTime {
    seconds: 1741881600
  }
  endDoTime {
    seconds: 1742140799
  }
  taskIdList: 400001067
  taskIdList: 400001068
  taskIdList: 400001069
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400046
  type: TaskType_Daily
  beginShowTime {
    seconds: 1742486400
  }
  endShowTime {
    seconds: 1742745599
  }
  beginDoTime {
    seconds: 1742486400
  }
  endDoTime {
    seconds: 1742745599
  }
  taskIdList: 400001070
  taskIdList: 400001071
  taskIdList: 400001072
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400047
  type: TaskType_Daily
  beginShowTime {
    seconds: 1743091200
  }
  endShowTime {
    seconds: 1743350399
  }
  beginDoTime {
    seconds: 1743091200
  }
  endDoTime {
    seconds: 1743350399
  }
  taskIdList: 400001073
  taskIdList: 400001074
  taskIdList: 400001075
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 200004
  type: TaskType_Daily
  beginShowTime {
    seconds: 1743091200
  }
  beginDoTime {
    seconds: 1743091200
  }
  taskIdList: 200001039
  groupName: "日常奖杯任务"
}
rows {
  id: 400048
  type: TaskType_Daily
  beginShowTime {
    seconds: 1743696000
  }
  endShowTime {
    seconds: 1743955199
  }
  beginDoTime {
    seconds: 1743696000
  }
  endDoTime {
    seconds: 1743955199
  }
  taskIdList: 400001077
  taskIdList: 400001078
  taskIdList: 400001079
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400049
  type: TaskType_Daily
  beginShowTime {
    seconds: 1744300800
  }
  endShowTime {
    seconds: 1744559999
  }
  beginDoTime {
    seconds: 1744300800
  }
  endDoTime {
    seconds: 1744559999
  }
  taskIdList: 400001080
  taskIdList: 400001081
  taskIdList: 400001082
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400050
  type: TaskType_Daily
  beginShowTime {
    seconds: 1744905600
  }
  endShowTime {
    seconds: 1745164799
  }
  beginDoTime {
    seconds: 1744905600
  }
  endDoTime {
    seconds: 1745164799
  }
  taskIdList: 400001083
  taskIdList: 400001084
  taskIdList: 400001085
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400051
  type: TaskType_Daily
  beginShowTime {
    seconds: 1745510400
  }
  endShowTime {
    seconds: 1745769599
  }
  beginDoTime {
    seconds: 1745510400
  }
  endDoTime {
    seconds: 1745769599
  }
  taskIdList: 400001086
  taskIdList: 400001087
  taskIdList: 400001088
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400052
  type: TaskType_Daily
  beginShowTime {
    seconds: 1746115200
  }
  endShowTime {
    seconds: 1746374399
  }
  beginDoTime {
    seconds: 1746115200
  }
  endDoTime {
    seconds: 1746374399
  }
  taskIdList: 400001089
  taskIdList: 400001090
  taskIdList: 400001091
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400053
  type: TaskType_Daily
  beginShowTime {
    seconds: 1746720000
  }
  endShowTime {
    seconds: 1746979199
  }
  beginDoTime {
    seconds: 1746720000
  }
  endDoTime {
    seconds: 1746979199
  }
  taskIdList: 400001092
  taskIdList: 400001093
  taskIdList: 400001094
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400054
  type: TaskType_Daily
  beginShowTime {
    seconds: 1747324800
  }
  endShowTime {
    seconds: 1747583999
  }
  beginDoTime {
    seconds: 1747324800
  }
  endDoTime {
    seconds: 1747583999
  }
  taskIdList: 400001095
  taskIdList: 400001096
  taskIdList: 400001097
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400055
  type: TaskType_Daily
  beginShowTime {
    seconds: 1747929600
  }
  endShowTime {
    seconds: 1748188799
  }
  beginDoTime {
    seconds: 1747929600
  }
  endDoTime {
    seconds: 1748188799
  }
  taskIdList: 400001098
  taskIdList: 400001099
  taskIdList: 400001100
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400056
  type: TaskType_Daily
  beginShowTime {
    seconds: 1748534400
  }
  endShowTime {
    seconds: 1748793599
  }
  beginDoTime {
    seconds: 1748534400
  }
  endDoTime {
    seconds: 1748793599
  }
  taskIdList: 400001101
  taskIdList: 400001102
  taskIdList: 400001103
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400057
  type: TaskType_Daily
  taskIdList: 400001104
  groupName: "每日限时奖杯"
  limitedType: 3
}
rows {
  id: 400058
  type: TaskType_Weekly
  taskIdList: 400001105
  groupName: "每周限时奖杯"
  limitedType: 3
}
rows {
  id: 400059
  type: TaskType_Daily
  beginShowTime {
    seconds: 1749139200
  }
  endShowTime {
    seconds: 1749398399
  }
  beginDoTime {
    seconds: 1749139200
  }
  endDoTime {
    seconds: 1749398399
  }
  taskIdList: 400001106
  taskIdList: 400001107
  taskIdList: 400001108
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400060
  type: TaskType_Daily
  beginShowTime {
    seconds: 1749744000
  }
  endShowTime {
    seconds: 1750003199
  }
  beginDoTime {
    seconds: 1749744000
  }
  endDoTime {
    seconds: 1750003199
  }
  taskIdList: 400001109
  taskIdList: 400001110
  taskIdList: 400001111
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400061
  type: TaskType_Daily
  beginShowTime {
    seconds: 1750348800
  }
  endShowTime {
    seconds: 1750607999
  }
  beginDoTime {
    seconds: 1750348800
  }
  endDoTime {
    seconds: 1750607999
  }
  taskIdList: 400001112
  taskIdList: 400001113
  taskIdList: 400001114
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400062
  type: TaskType_Daily
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1751212799
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1751212799
  }
  taskIdList: 400001115
  taskIdList: 400001116
  taskIdList: 400001117
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400063
  type: TaskType_Daily
  beginShowTime {
    seconds: 1751558400
  }
  endShowTime {
    seconds: 1751817599
  }
  beginDoTime {
    seconds: 1751558400
  }
  endDoTime {
    seconds: 1751817599
  }
  taskIdList: 400001118
  taskIdList: 400001119
  taskIdList: 400001120
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400064
  type: TaskType_Daily
  beginShowTime {
    seconds: 1752163200
  }
  endShowTime {
    seconds: 1752422399
  }
  beginDoTime {
    seconds: 1752163200
  }
  endDoTime {
    seconds: 1752422399
  }
  taskIdList: 400001122
  taskIdList: 400001123
  taskIdList: 400001121
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400065
  type: TaskType_Daily
  beginShowTime {
    seconds: 1752768000
  }
  endShowTime {
    seconds: 1753027199
  }
  beginDoTime {
    seconds: 1752768000
  }
  endDoTime {
    seconds: 1753027199
  }
  taskIdList: 400001124
  taskIdList: 400001125
  taskIdList: 400001126
  groupName: "每日任务奖杯"
  limitedType: 2
}
rows {
  id: 400066
  type: TaskType_Daily
  beginShowTime {
    seconds: 1753372800
  }
  endShowTime {
    seconds: 1753631999
  }
  beginDoTime {
    seconds: 1753372800
  }
  endDoTime {
    seconds: 1753631999
  }
  taskIdList: 400001127
  taskIdList: 400001128
  taskIdList: 400001129
  groupName: "每日任务奖杯"
  limitedType: 2
}
