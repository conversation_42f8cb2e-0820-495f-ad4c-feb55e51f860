com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_chase.xlsx sheet:Sheet1
rows {
  content: "{0} - {1}"
  switch: 1
  stringId: "MCG_InGame_Ready_VIPNotice_Name"
}
rows {
  content: "带来了"
  switch: 1
  stringId: "MCG_InGame_Ready_VIPNotice_Skill"
}
rows {
  content: "专属"
  switch: 1
  stringId: "MCG_InGame_Ready_VIPOnly"
}
rows {
  content: "只有化身为{0}的星宝才可拾取"
  switch: 1
  stringId: "MCG_InGame_Ready_VIPOnlyTips"
}
rows {
  content: "齐天大圣"
  switch: 1
  stringId: "MCG_InGame_VIP01"
}
rows {
  content: "选择投放给星宝的技能池"
  switch: 1
  stringId: "MCG_InGame_Ready_ChooseSkillPool"
}
rows {
  content: "生命之泉"
  switch: 1
  stringId: "MCG_InGame_Spring"
}
rows {
  content: "生命之泉充能完成，可前往治疗"
  switch: 1
  stringId: "MCG_InGame_Spring_CanUse"
}
rows {
  content: "满血状态不可使用"
  switch: 1
  stringId: "MCG_InGame_Spring_CanNotUse"
}
rows {
  content: "{0}使用了生命之泉"
  switch: 1
  stringId: "MCG_InGame_Spring_Used"
}
rows {
  content: "需再完成{0}台星路仪搜寻，为生命之泉充能"
  switch: 1
  stringId: "MCG_InGame_Spring_Need"
}
rows {
  content: "治疗中…"
  switch: 1
  stringId: "MCG_InGame_Spring_Using"
}
rows {
  content: "治疗"
  switch: 1
  stringId: "MCG_InGame_Spring_Button"
}
rows {
  content: "生命之泉"
  switch: 1
  stringId: "MCG_ModeInstruction_Spring"
}
rows {
  content: "星宝每完成{0}台星路仪搜寻，可为生命之泉充能一层，最多{1}层。每次使用消耗一层充能，为自己回复生命值。"
  switch: 1
  stringId: "MCG_ModeInstruction_Spring_Text"
}
rows {
  content: "传送门"
  switch: 1
  stringId: "MCG_InGame_Prop_Door"
}
rows {
  content: "星路仪"
  switch: 1
  stringId: "MCG_InGame_Prop_HumanMachine"
}
rows {
  content: "时空祭坛"
  switch: 1
  stringId: "MCG_InGame_Prop_BossMachine"
}
rows {
  content: "路障机关"
  switch: 1
  stringId: "MCG_InGame_Prop_Roadblock"
}
rows {
  content: "窗口"
  switch: 1
  stringId: "MCG_InGame_Prop_Window"
}
rows {
  content: "生命之泉"
  switch: 1
  stringId: "MCG_InGame_Prop_Spring"
}
rows {
  content: "法宝坛"
  switch: 1
  stringId: "MCG_InGame_Prop_MagicWeaponBox"
}
rows {
  content: "秘宝匣"
  switch: 1
  stringId: "MCG_InGame_Prop_MagicWeaponBox_Vampire"
}
rows {
  content: "反馈提交成功"
  switch: 1
  stringId: "MCG_ReportBug_FeedBackSuccess"
}
rows {
  content: "请选择反馈事项"
  switch: 1
  stringId: "MCG_ReportBug_SelectFeedBackEvent"
}
rows {
  content: "请填写至少20字的反馈描述"
  switch: 1
  stringId: "MCG_ReportBug_EnterAtLeast20Words"
}
rows {
  content: "最多输入200个字！"
  switch: 1
  stringId: "MCG_ReportBug_EnterUpTo200Words"
}
rows {
  content: "欢迎星宝提供各种建议与反馈，可以尽可能描述更多细节，比如重现步骤、现场情况等，20-200字，感谢星宝的大力支持！"
  switch: 1
  stringId: "MCG_ReportBug_InputTips"
}
rows {
  content: "暗星皮肤"
  switch: 1
  stringId: "MCG_PropType_BossSkin"
}
rows {
  content: "道具皮肤"
  switch: 1
  stringId: "MCG_PropType_PropSkin"
}
rows {
  content: "公共技能"
  switch: 1
  stringId: "MCG_CommonSkill_NameText"
}
rows {
  content: "公共技能暂未公开，开局1分钟公开。"
  switch: 1
  stringId: "MCG_CommonSkill_HideText"
}
rows {
  content: "在大王别抓我模式中的违规行为将扣除信誉分"
  switch: 1
  stringId: "MCG_InLevel_Reputation_MyScore_LineOne"
}
rows {
  content: "信誉分低于70分将不能参与大王别抓我排位"
  switch: 1
  stringId: "MCG_InLevel_Reputation_MyScore_LineTwo"
}
rows {
  content: "<reputationScore23Red>中途退出</>：未被淘汰时，强制退出将扣除信誉分。"
  switch: 1
  stringId: "MCG_InLevel_Reputation_OffenseRule_LineOne"
}
rows {
  content: "<reputationScore23Red>托管挂机</>：未被淘汰时，局内长时间在托管状态下将扣除信誉分。"
  switch: 1
  stringId: "MCG_InLevel_Reputation_OffenseRule_LineTwo"
}
rows {
  content: "<reputationScore23Red>不当言论</>：局内辱骂他人，传播广告或违法信息等行为将扣除信誉分。"
  switch: 1
  stringId: "MCG_InLevel_Reputation_OffenseRule_LineThree"
}
rows {
  content: "<reputationScore23Red>举报核实</>：其他违规行为，被玩家举报核实后将扣除信誉分。"
  switch: 1
  stringId: "MCG_InLevel_Reputation_OffenseRule_LineFour"
}
rows {
  content: "<reputationScore23>正常对局</>：单人正常完成大王别抓我对局，没有违规行为，信誉分+{0}分，每日最多可恢复20分。"
  switch: 1
  stringId: "MCG_InLevel_Reputation_CreditRule_LineOne"
}
rows {
  content: "<reputationScore23>正常对局</>：组队正常完成大王别抓我对局，没有违规行为，信誉分+{0}分，每日最多可恢复20分。"
  switch: 1
  stringId: "MCG_InLevel_Reputation_CreditRule_LineTwo"
}
rows {
  content: "<reputationScore23>每日登录</>：每日首次登录，信誉分+{0}分。"
  switch: 1
  stringId: "MCG_InLevel_Reputation_CreditRule_LineThree"
}
rows {
  content: "退出会影响队友体验，<ChaseSurrenderPop>将扣除15信誉分（信誉过低将禁止参与排位）</>"
  switch: 1
  stringId: "MCG_InLevel_Main_NoCompleteWithReputation_Box"
}
rows {
  content: "投降(还剩{0}秒)"
  switch: 1
  stringId: "MCG_InLevel_Surrender_RemainingTime"
}
rows {
  content: "请先完成新手教学对局"
  switch: 1
  stringId: "MCG_PlayGuide_StartGame"
}
rows {
  content: "喵喵入侵！部分星路仪和祭坛形态变化"
  switch: 1
  stringId: "MCG_MEOWMEOW_START_TIPS"
}
rows {
  content: "我是新手"
  switch: 1
  stringId: "MCG_PlayGuide_Freshman"
}
rows {
  content: "我是高手"
  switch: 1
  stringId: "MCG_PlayGuide_Senior"
}
rows {
  content: "进入新手教学对局"
  switch: 1
  stringId: "MCG_PlayGuide_Enter"
}
rows {
  content: "跳过新手教学对局"
  switch: 1
  stringId: "MCG_PlayGuide_Skip"
}
rows {
  content: "即将进入大王别抓我新手教学局"
  switch: 1
  stringId: "MCG_PlayGuide_EnterTips"
}
rows {
  content: "三王模式"
  switch: 1
  stringId: "MCG_MatchCustom_Three"
}
rows {
  content: "单王模式"
  switch: 1
  stringId: "MCG_MatchCustom_One"
}
rows {
  content: "星宝阵营"
  switch: 1
  stringId: "MCG_MatchCustom_Star"
}
rows {
  content: "暗星阵营"
  switch: 1
  stringId: "MCG_MatchCustom_Boss"
}
rows {
  content: "身体不受控制的舞动起来...."
  switch: 1
  stringId: "MCG_MEOWMEOW_DANCING_TIPS"
}
rows {
  content: "(待结算)"
  switch: 1
  stringId: "MCG_IdentityScore_ToBeSettled"
}
rows {
  content: "每次投降失败以后要再过两分钟才能投降。"
  switch: 1
  stringId: "MCG_InLevel_Surrender_Again"
}
rows {
  content: "传送魔方"
  switch: 1
  stringId: "MCG_InGame_HumanSkill_MagicCube_Name"
}
rows {
  content: "魔方碰到星路仪/锤子，传送自己；魔方碰到星宝，传送星宝；魔方碰到暗星，减速暗星"
  switch: 1
  stringId: "MCG_InGame_HumanSkill_MagicCube_Info"
}
rows {
  content: "协助"
  switch: 1
  stringId: "MCG_InGame_HumanSkill_MagicCube_Position"
}
rows {
  content: "暂不支持查看此战绩。"
  switch: 1
  stringId: "Chase_GameDetail_NotFindData"
}
rows {
  content: "被暗星扫描发现，注意转移！"
  switch: 1
  stringId: "MCG_InGame_Player_Sense_Scan"
}
