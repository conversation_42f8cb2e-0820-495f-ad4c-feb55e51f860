com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_System.xlsx sheet:文本配置
rows {
  content: "是否消耗<Orange28>{0}</>个<Orange28>{1}</>快速完成任务"
  switch: 1
  stringId: "Accelerating_prompt_use"
}
rows {
  content: "本次加速缺少<Orange28>{0}</>个<Orange28>{1}</>，是否前往福利商店购买？"
  switch: 1
  stringId: "Accelerating_prompt_lack"
}
rows {
  content: "{0}-排位模式暂未开启，敬请期待"
  switch: 1
  stringId: "UP_ModeSeleclNoMatchInRank"
}
rows {
  content: "{0}-休闲模式暂未开启，敬请期待"
  switch: 1
  stringId: "UP_ModeSeleclNoMatchInCasual"
}
rows {
  content: "开启<DisturbText01>勿扰状态</>，开启后无法参与广场互动\n <DisturbText02>（开启后心情和称号暂时隐藏） </>"
  switch: 1
  stringId: "Lobby_NoDisturbState_MsgBoxTips"
}
rows {
  content: "再次点击可以关闭勿扰状态哦"
  switch: 1
  stringId: "Lobby_NoDisturbState_BubbleTips"
}
rows {
  content: "您正处在勿扰状态中"
  switch: 1
  stringId: "Lobby_NoDisturbState_SelfInNoDisturbTip"
}
rows {
  content: "对方处于勿扰状态"
  switch: 1
  stringId: "Lobby_NoDisturbState_TargetInNoDisturbTip"
}
rows {
  content: "您已经邀请过该好友，请稍后再次尝试哦"
  switch: 1
  stringId: "Team_NotInviteOfflineMemberTip"
}
rows {
  content: "网络延迟，请前往邮箱或客服查询抽奖结果"
  switch: 1
  stringId: "UI_ActivityLottery_BaseView_0"
}
rows {
  content: "可领取奖励份数："
  switch: 1
  stringId: "UI_GiftShare_RewardCountLimit"
}
rows {
  content: "当前阵营已切换至-{0}"
  switch: 1
  stringId: "UI_UPTip_OnSideChanged"
}
rows {
  content: "每日奖杯挑战：\n奖杯挑战进度及奖励会在每日00:00重置！\n奖杯进度达到10奖杯时，可领取一定农场币；\n奖杯进度达到20奖杯时，可领取1个奖杯赐福宝箱和1包田园牧歌炫彩包；\n奖杯进度达到50奖杯时，可领取30通行证经验；\n奖杯进度达到80奖杯时，可领取1包田园牧歌稀有包和1个奖杯挑战礼包，开启后会获得2000星宝印章、5云朵币和一定数量的农场币\n（农场币需开通星宝农场才可获得）。"
  switch: 1
  stringId: "Cup_DailyTaskHint3"
}
rows {
  content: "单人模式"
  switch: 1
  stringId: "UI_GameType_One"
}
rows {
  content: "双人模式"
  switch: 1
  stringId: "UI_GameType_Two"
}
rows {
  content: "四人模式"
  switch: 1
  stringId: "UI_GameType_Four"
}
rows {
  content: "作为队员，当前不可选择该玩法"
  switch: 1
  stringId: "TeamMatchSelectTips"
}
rows {
  content: "全国"
  switch: 1
  stringId: "Common_National"
}
rows {
  content: "幸运对局，跳过第一轮！"
  switch: 1
  stringId: "ThreeRoundTestTip"
}
rows {
  content: "临时相册上限：<Orange28>{0}</>/{1}，若超过临时相册储存上限或手机缓存被清理时照片会被<Orange28>删除</>，请及时处理"
  switch: 1
  stringId: "TemporaryMailboxNotice"
}
rows {
  content: "相册已满，照片已存储至<Orange28>临时相册</>，是否前往扩充相册上限？"
  switch: 1
  stringId: "PhotoAlbumFullTips1"
}
rows {
  content: "相册已满，照片已存储至<Orange28>临时相册</>，是否开通星宝会员扩充相册上限？"
  switch: 1
  stringId: "PhotoAlbumFullTips2"
}
rows {
  content: "相册已满，照片已存储至<Orange28>临时相册</>，请尽快清理照片"
  switch: 1
  stringId: "PhotoAlbumFullTips3"
}
rows {
  content: "相册已满，临时相册已满，请尽快清理照片"
  switch: 1
  stringId: "PhotoAlbumFullTips4"
}
rows {
  content: "保存提示"
  switch: 1
  stringId: "PhotoAlbumSaveTips"
}
rows {
  content: "前往扩充"
  switch: 1
  stringId: "PhotoAlbumTaskTips1"
}
rows {
  content: "前往开通"
  switch: 1
  stringId: "PhotoAlbumTaskTips2"
}
rows {
  content: "前往清理"
  switch: 1
  stringId: "PhotoAlbumTaskTips3"
}
rows {
  content: "当前编辑未保存，退出会丢失已编辑的内容，是否继续？"
  switch: 1
  stringId: "PhotoAlbumEditExitTips"
}
rows {
  content: "个人记录"
  switch: 1
  stringId: "Common_SelfRecord"
}
rows {
  content: "星世界排行规则"
  switch: 1
  stringId: "Leaderboard_Tips_Title_StarWorld_Mix"
}
rows {
  content: "【精选通关榜】\n（1）通关来自[每日精选]、[星图广场-广场首页]、[星图广场-星图热榜]的地图，可增加1排行榜计分。\n（2）每个赛季，相同的通关地图在精选通关排行榜只记录一次。\n（3）榜单数据在赛季初重置。\n\n【闯关挑战榜】\n（1）通过[闯关挑战]通关的地图，可增加1排行榜计分。\n（2）每个赛季，相同的通关地图在闯关挑战通关排行榜只记录一次。\n（3）榜单数据在赛季初重置。\n\n【赛季创作榜】\n（1）本赛季发布的地图中，综合积分最高的5张地图的积分之和。\n\n【每周创作榜】\n（1）过去7x24小时发布的地图中，综合积分最高的5张地图的积分之和。\n\n【每日创作榜】\n（1）过去24小时发布的地图中，综合积分最高的5张地图的积分之和。\n\n【资源创作榜】\n（1）当前赛季中，资源社区内发布的资源累积点数的排行榜，点数为综合积分最高的15个资源的积分之和。"
  switch: 1
  stringId: "Leaderboard_Tips_Desc_StarWorld_Mix"
}
rows {
  content: "谁是狼人专精排行规则"
  switch: 1
  stringId: "UI_RankTag_NR3E3_FinalPoints_Rule"
}
rows {
  content: "（1）谁是狼人专精排行依据星宝的身份专精点数进行排行。\n（2）专精点数出现变化时，排名更新会有一定延迟，属于正常现象。"
  switch: 1
  stringId: "UI_RankTag_NR3E3_FinalPoints_Desc"
}
rows {
  content: "地区第{0}名"
  switch: 1
  stringId: "UI_FinishBreakRecord_AreaRank"
}
rows {
  content: "全国第{0}名"
  switch: 1
  stringId: "UI_FinishBreakRecord_CountryRank"
}
rows {
  content: "地区新纪录"
  switch: 1
  stringId: "UI_FinishBreakRecord_AreaTitle"
}
rows {
  content: "全国新纪录"
  switch: 1
  stringId: "UI_FinishBreakRecord_CountryTitle"
}
rows {
  content: "达成时间{0}"
  switch: 1
  stringId: "UI_FinishBreakRecord_CurrentTimeFormat"
}
rows {
  content: "单人模式"
  switch: 1
  stringId: "UI_FinishBreakRecord_SingleModeTitle"
}
rows {
  content: "该玩家未到5级，无法赠送哦"
  switch: 1
  stringId: "UI_Bag_GiveGiftLevelTips"
}
rows {
  content: "照片已被隐藏，访客无法查看"
  switch: 1
  stringId: "PhotoAlbumHideTips1"
}
rows {
  content: "照片正常显示，访客可见"
  switch: 1
  stringId: "PhotoAlbumHideTips2"
}
rows {
  content: "大王别抓我专精榜规则"
  switch: 1
  stringId: "UI_RankTag_ChaseFinalPointsRule"
}
rows {
  content: "（1）大王别抓我专精排行依据当前赛季大王别抓我身份的专精点数进行排名。\n（2）赛季更新后，专精会随段位进行重置，届时专精可能会存在一定程度下降。\n（3）专精出现变化时，排名更新会有一定延迟，属于正常现象。\n（4）每周会为进入排行榜前100名（以周一0点的排名为准）的星宝发送限时称号，不同级别的排行榜对应的称号品级不同。\n（5）每周需要使用身份完成一局排位赛后，该身份才能参与排行榜排名。"
  switch: 1
  stringId: "UI_RankTag_ChaseFinalPointsDesc"
}
rows {
  content: "领取新手奖励"
  switch: 1
  stringId: "UI_RightOrUp_Newbie_Today"
}
rows {
  content: "明日登录可领"
  switch: 1
  stringId: "UI_RightOrUp_Newbie_Tomorrow"
}
rows {
  content: "总价值<LobbyRightOrUpTipsOrange>600元</> "
  switch: 1
  stringId: "UI_RightOrUp_Newbie_RewardTips"
}
rows {
  content: "上传失败，所选照片数量大于相册空间，请重新选择"
  switch: 1
  stringId: "Album_Space_Not_Enough_Multiple"
}
rows {
  content: "上传失败，相册空间不足"
  switch: 1
  stringId: "Album_Space_Not_Enough"
}
rows {
  content: "相册已满，保存失败"
  switch: 1
  stringId: "Album_Save_Fail"
}
rows {
  content: "相册已满，照片已暂存至临时相册，请尽快上传"
  switch: 1
  stringId: "Album_Save_Fail_Temporary"
}
rows {
  content: "照片上传成功，临时相册已无待上传照片"
  switch: 1
  stringId: "Album_TemporaryEmpty_Tips"
}
rows {
  content: "已提醒所选好友"
  switch: 1
  stringId: "Album_Friends_Notice"
}
rows {
  content: "被提醒"
  switch: 1
  stringId: "Album_Noticed"
}
rows {
  content: "提醒他人"
  switch: 1
  stringId: "Album_Notice_Others"
}
rows {
  content: "尚未选择时装奖励，快前往选择吧~"
  switch: 1
  stringId: "UI_Lottery_ConfirmText"
}
rows {
  content: "继续祈愿"
  switch: 1
  stringId: "UI_Lottery_Continue"
}
rows {
  content: "前往自选时装"
  switch: 1
  stringId: "UI_Lottery_Choose"
}
rows {
  content: "是否使用{0}次撕贴纸机会，撕开所有贴纸？"
  switch: 1
  stringId: "UI_FarmPuzzle_ConfirmTips"
}
rows {
  content: "{0}不足，可通过做任务获得。"
  switch: 1
  stringId: "UI_FarmPuzzle_GoGetCoin"
}
rows {
  content: "是否继续匹配"
  switch: 1
  stringId: "Team_ConfirmCancelGameTip"
}
rows {
  content: "继续匹配"
  switch: 1
  stringId: "Team_ConfirmCancelGameOK"
}
rows {
  content: "退出匹配"
  switch: 1
  stringId: "Team_ConfirmCancelGameNO"
}
rows {
  content: "来查看该照片"
  switch: 1
  stringId: "Album_Remind_ViewPhoto"
}
rows {
  content: "已超出该照片可@最大限制"
  switch: 1
  stringId: "Album_Remind_OverLimit"
}
rows {
  content: "该照片已被好友删除"
  switch: 1
  stringId: "Album_Photo_DeleteTips"
}
rows {
  content: "跳过前三轮，全员晋级决胜关"
  switch: 1
  stringId: "OneRoundTestTip"
}
rows {
  content: "展开海量新玩法!"
  switch: 1
  stringId: "UI_ModelSelect_UnFold"
}
rows {
  content: "收起玩法"
  switch: 1
  stringId: "UI_ModelSelect_Fold"
}
rows {
  content: "创作者主页"
  switch: 1
  stringId: "LFT_UgcCreatorPage"
}
rows {
  content: "未到开启时间，{0}开启"
  switch: 1
  stringId: "Video_Not_Open_Time"
}
rows {
  content: "开启礼包"
  switch: 1
  stringId: "Use_Prop_Item_Title_PackageType_Common"
}
rows {
  content: "开启礼包"
  switch: 1
  stringId: "Use_Prop_Item_Title_PackageType_Random"
}
rows {
  content: "自选礼包"
  switch: 1
  stringId: "Use_Prop_Item_Title_PackageType_Pick"
}
rows {
  content: "{0}当前无法接受匹配，请发个消息给Ta吧"
  switch: 1
  stringId: "Team_NotStartReasonLeaderTip"
}
rows {
  content: "队长邀请你开始游戏，请回到队伍开局吧"
  switch: 1
  stringId: "Team_NotStartReasonMemberTip"
}
rows {
  content: "查看更多福袋"
  switch: 1
  stringId: "GiftSharing_GetRecord_Button_Name"
}
rows {
  content: "顶端对齐"
  switch: 1
  stringId: "Album_VerticalAlign_Top"
}
rows {
  content: "垂直居中"
  switch: 1
  stringId: "Album_VerticalAlign_Center"
}
rows {
  content: "底端对齐"
  switch: 1
  stringId: "Album_VerticalAlign_Bottom"
}
rows {
  content: "左对齐"
  switch: 1
  stringId: "Album_HorizontalAlign_Left"
}
rows {
  content: "居中"
  switch: 1
  stringId: "Album_HorizontalAlign_Center"
}
rows {
  content: "右对齐"
  switch: 1
  stringId: "Album_HorizontalAlign_Right"
}
rows {
  content: "图片编辑已保存至个人相册"
  switch: 1
  stringId: "Album_EditSaveSuccessTips"
}
rows {
  content: "当前心情还未结束，确认更改？"
  switch: 1
  stringId: "UI_ChangeMoodState"
}
rows {
  content: "星搭子今日已登录对局（{0}/{1}）"
  switch: 1
  stringId: "UI_DoubleFormation_Online"
}
rows {
  content: "星搭子今日尚未登录游戏"
  switch: 1
  stringId: "UI_DoubleFormation_Offline"
}
rows {
  content: "快去找到你的CP搭子，登录跑局即可领取XXX奖励！"
  switch: 1
  stringId: "UI_DoubleFormation_ShareTip"
}
rows {
  content: "XX-XX与绑定好友 同日登录游戏，即可领取奖励"
  switch: 1
  stringId: "UI_DoubleFormation_MakeTip"
}
rows {
  content: "目前合适值"
  switch: 1
  stringId: "UI_DoubleFormation_ProgressName"
}
rows {
  content: "对方已成团，和其他好友组搭子吧！"
  switch: 1
  stringId: "UI_DoubleFormation_TeamFull"
}
rows {
  content: "是否与“<Orange28>{0}</>”组成搭子"
  switch: 1
  stringId: "UI_DoubleFormation_JoinTeam"
}
rows {
  content: "（活动期间仅可接受一位好友邀请）"
  switch: 1
  stringId: "UI_DoubleFormation_JoinTeamTip"
}
rows {
  content: "兑换商店"
  switch: 1
  stringId: "NRE3Activity_In_ShopTitle"
}
rows {
  content: "傀儡狼身份上新"
  switch: 1
  stringId: "NRE3Activity_In_ShopUpTitle"
}
rows {
  content: "<Orange28>{0}</>暂未开启，请等待活动开启"
  switch: 1
  stringId: "Mail_Askfor_Activityclosed_Insufficient"
}
rows {
  content: "索要的<Orange28>{0}</>已无法赠送，是否<Orange28>删除</>索要请求？"
  switch: 1
  stringId: "Mail_Askfor_Commodity_Unable"
}
rows {
  content: "此处可打开免打扰状态"
  switch: 1
  stringId: "Lobby_NoDisturbState_BubbleTips_PassiveInteract"
}
rows {
  content: "每日奖杯挑战"
  switch: 1
  stringId: "Cup_Bg_Title_Daily"
}
