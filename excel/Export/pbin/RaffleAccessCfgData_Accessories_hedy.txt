com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_hedy.xlsx sheet:活动-配饰卡池
rows {
  raffleId: 30000002
  name: "吾皇猫"
  startTime {
    seconds: 1727366400
  }
  endTime {
    seconds: 1729785599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30000002
    subPoolIds: 30000002
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 224
  text: "每次祈愿不会获得重复奖励，9次必得吾皇猫"
  lowestVersion: "1.3.18.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "吾皇猫"
  raffleTagIcon: "T_SupremoCat_Icon_Tab1"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1727366400
  }
  showEndTime {
    seconds: 1729785599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 30000003
  name: "吾皇猫"
  startTime {
    seconds: 1727366400
  }
  endTime {
    seconds: 1729785599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30000003
    subPoolIds: 30000003
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 224
  text: "每次祈愿不会获得重复奖励，9次必得巴扎黑"
  lowestVersion: "1.3.18.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "巴扎黑"
  raffleTagIcon: "T_SupremoCat_Icon_Tab2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1727366400
  }
  showEndTime {
    seconds: 1729785599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 30000022
  name: "蜜桃猫"
  startTime {
    seconds: 1712764800
  }
  endTime {
    seconds: 1749139199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30000022
    subPoolIds: 30000022
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 355
  text: "每次祈愿不会获得重复奖励，9次必得蜜桃猫"
  lowestVersion: "1.3.78.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "蜜桃猫猫"
  raffleTagIcon: "T_SupremoCat_Icon_Tab2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1712764800
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 30000025
  name: "吾皇猫"
  startTime {
    seconds: 1748016000
  }
  endTime {
    seconds: 1749743999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30000025
    subPoolIds: 30000025
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得吾皇猫"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "吾皇猫"
  raffleTagIcon: "T_SupremoCat_Icon_Tab1"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 30000026
  name: "吾皇猫"
  startTime {
    seconds: 1748016000
  }
  endTime {
    seconds: 1749743999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30000026
    subPoolIds: 30000026
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得巴扎黑"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "巴扎黑"
  raffleTagIcon: "T_SupremoCat_Icon_Tab2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  juniorItemAnimType: 1
}
