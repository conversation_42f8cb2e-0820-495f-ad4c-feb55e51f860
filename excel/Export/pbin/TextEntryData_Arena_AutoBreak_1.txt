com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_Arena.xlsx sheet:文本配置
rows {
  id: 500107
  content: "添加我方卡牌"
  switch: 1
  stringId: "Arena_Train_Card_Title_1"
}
rows {
  id: 500108
  content: "添加敌方卡牌"
  switch: 1
  stringId: "Arena_Train_Card_Title_2"
}
rows {
  id: 500109
  content: "峡谷3v3"
  switch: 1
  stringId: "Set_EditKey_Arena3v3"
}
rows {
  id: 500110
  content: "3v3段位详情"
  switch: 1
  stringId: "Arena_PerRankDetails_Title1"
}
rows {
  id: 500111
  content: "5v5段位详情"
  switch: 1
  stringId: "Arena_PerRankDetails_Title2"
}
rows {
  id: 500112
  content: "3v3比赛信息"
  switch: 1
  stringId: "Arena_PerRankInfo_Title1"
}
rows {
  id: 500113
  content: "5v5比赛信息"
  switch: 1
  stringId: "Arena_PerRankInfo_Title2"
}
rows {
  id: 500114
  content: "3v3峡谷印记"
  switch: 1
  stringId: "Arena_PerRankMark_Title1"
}
rows {
  id: 500115
  content: "5v5峡谷印记"
  switch: 1
  stringId: "Arena_PerRankMark_Title2"
}
rows {
  id: 500116
  content: "{0}年{1}月—{2}年{3}月"
  switch: 1
  stringId: "Arena_PerRankMark_OpenTime"
}
rows {
  id: 500117
  content: "S{0}"
  switch: 1
  stringId: "Arena_PerRankMark_SonalName"
}
rows {
  id: 500118
  content: "第{0}名"
  switch: 1
  stringId: "Arena_PerRankDetailds_Ranking"
}
rows {
  id: 500119
  content: "赛季暂未开启~"
  switch: 1
  stringId: "Arena_PerRankInfo_NoneTip"
}
rows {
  id: 500120
  content: "赛季结束时段位达到最强峡谷星可获得印记~"
  switch: 1
  stringId: "Arena_PerRankMark_NoneTip"
}
rows {
  id: 500121
  content: "秒伤"
  switch: 1
  stringId: "Arena_Train_DMG_DPS"
}
rows {
  id: 500122
  content: "物理"
  switch: 1
  stringId: "Arena_Train_DMG_Physical"
}
rows {
  id: 500123
  content: "法术"
  switch: 1
  stringId: "Arena_Train_DMG_Magic"
}
rows {
  id: 500124
  content: "真实"
  switch: 1
  stringId: "Arena_Train_DMG_Real"
}
rows {
  id: 500125
  content: "战斗场次"
  switch: 1
  stringId: "Arena_PerRankInfo_BattleCount"
}
rows {
  id: 500126
  content: "胜场数"
  switch: 1
  stringId: "Arena_PerRankInfo_WinCount"
}
rows {
  id: 500127
  content: "胜率"
  switch: 1
  stringId: "Arena_PerRankInfo_WinRate"
}
rows {
  id: 500128
  content: "MVP"
  switch: 1
  stringId: "Arena_PerRankInfo_MVP"
}
rows {
  id: 500129
  content: "胜场"
  switch: 1
  stringId: "Arena_PerRankInfo_HeroWinCount"
}
rows {
  id: 500130
  content: "场次"
  switch: 1
  stringId: "Arena_PerRankInfo_HeroBattleCount"
}
rows {
  id: 500131
  content: "游戏结束"
  switch: 1
  stringId: "Arena_GameOver"
}
rows {
  id: 500132
  content: "夺冠奖励"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_Champion"
}
rows {
  id: 500133
  content: "关卡名次"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_Level"
}
rows {
  id: 500134
  content: "关卡淘汰"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_Eliminate"
}
rows {
  id: 500135
  content: "连胜"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_WinStreak"
}
rows {
  id: 500136
  content: "终局排名"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_FinalLevelRank"
}
rows {
  id: 500137
  content: "排位分抵扣(消耗)"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_Protected"
}
rows {
  id: 500138
  content: "排位分加成(消耗)"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_Additional"
}
rows {
  id: 500139
  content: "全局整体表现奖励"
  switch: 1
  stringId: "Arena_InLevel_FinaLQualiying_QPST_FinalDimension"
}
rows {
  id: 500140
  content: "胜利"
  switch: 1
  stringId: "Arena_HotZone_Win"
}
rows {
  id: 500141
  content: "失败"
  switch: 1
  stringId: "Arena_HotZone_Lose"
}
rows {
  id: 500143
  content: "是否需要清除掉当前已添加的所有卡牌？"
  switch: 1
  stringId: "Arena_Training_CardClear"
}
rows {
  id: 500144
  content: "确认清空"
  switch: 1
  stringId: "Arena_Training_ConfirmClear"
}
rows {
  id: 500145
  content: "地盘已生成"
  switch: 1
  stringId: "HotZone_ZoneShow"
}
rows {
  id: 500146
  content: "我方已占领{0}%！"
  switch: 1
  stringId: "HotZone_ProgressTipSelf"
}
rows {
  id: 500147
  content: "敌方已占领{0}%！"
  switch: 1
  stringId: "HotZone_ProgressTipEnemy"
}
rows {
  id: 500148
  content: "召唤师，请选择你想禁用的英雄"
  switch: 1
  stringId: "Arena_HeroBan"
}
rows {
  id: 500149
  content: "召唤师，请选择你想使用的英雄"
  switch: 1
  stringId: "Arena_HeroPick"
}
rows {
  id: 500150
  content: "当前英雄已被禁用，请重新选择"
  switch: 1
  stringId: "Arena_HeroIsBan"
}
rows {
  id: 500151
  content: "当前英雄未解锁，请先解锁或重新选择"
  switch: 1
  stringId: "Arena_HeroIslock"
}
rows {
  id: 500152
  content: "当前英雄已被队友预选"
  switch: 1
  stringId: "Arena_HeroPreFormTeam"
}
rows {
  id: 500153
  content: "确认花费<InviteYellow>{0}星钻</>购买{1}？"
  switch: 1
  stringId: "Arena_HeroTryBuyPop"
}
rows {
  id: 500154
  content: "未获取"
  switch: 1
  stringId: "Arena_Imprint_Tips"
}
rows {
  id: 500155
  content: "峡谷对战"
  switch: 1
  stringId: "Arena_PleryInfo_Career_Title_1"
}
rows {
  id: 500156
  content: "夺冠次数"
  switch: 1
  stringId: "Arena_PleryInfo_Career_Title_2"
}
rows {
  id: 500157
  content: "全场最佳"
  switch: 1
  stringId: "Arena_PleryInfo_Career_Title_3"
}
rows {
  id: 500158
  content: "败方最佳"
  switch: 1
  stringId: "Arena_PleryInfo_Career_Title_4"
}
rows {
  id: 500159
  content: "超神次数"
  switch: 1
  stringId: "Arena_PleryInfo_Career_Title_5"
}
rows {
  id: 500160
  content: "峡谷皮肤"
  switch: 1
  stringId: "Arena_PleryInfo_Career_Title_6"
}
rows {
  id: 500161
  content: "英雄数量"
  switch: 1
  stringId: "Arena_PleryInfo_Career_Title_7"
}
rows {
  id: 500162
  content: "收集峡谷星解锁该英雄:"
  switch: 1
  stringId: "Arena_RoadToHonor_NewHero"
}
rows {
  id: 500163
  content: "收集峡谷星解锁该卡牌:"
  switch: 1
  stringId: "Arena_RoadToHonor_NewCard"
}
rows {
  id: 500164
  content: "收集峡谷星解锁该皮肤:"
  switch: 1
  stringId: "Arena_RoadToHonor_NewSkin"
}
rows {
  id: 500165
  content: "英雄皮肤"
  switch: 1
  stringId: "Arena_RoadToHonor_NewSkin_1"
}
rows {
  id: 500166
  content: "确认花费<InviteYellow>{0}</>{1}兑换{2}吗？"
  switch: 1
  stringId: "Arena_ShardExchange_Message_Sign"
}
rows {
  id: 500167
  content: "回合即将开始，无法开启宝箱"
  switch: 1
  stringId: "Arena_Shenmibaoxiang_Title_1"
}
rows {
  id: 500168
  content: "选秀结束后才能开启宝箱"
  switch: 1
  stringId: "Arena_Shenmibaoxiang_Title_2"
}
rows {
  id: 500169
  content: "亲密爱人"
  switch: 1
  stringId: "Arena_Recommend_Couple"
}
rows {
  id: 500170
  content: "最佳拍档"
  switch: 1
  stringId: "Arena_Recommend_Chum"
}
rows {
  id: 500171
  content: "生死兄弟"
  switch: 1
  stringId: "Arena_Recommend_Brother"
}
rows {
  id: 500172
  content: "灵魂挚友"
  switch: 1
  stringId: "Arena_Recommend_Girlfriends"
}
rows {
  id: 500173
  content: "超强大腿"
  switch: 1
  stringId: "Arena_Recommend_Friend_HighRank"
}
rows {
  id: 500174
  content: "亲密好友"
  switch: 1
  stringId: "Arena_Recommend_Friend_HighIntimate"
}
rows {
  id: 500175
  content: "好友"
  switch: 1
  stringId: "Arena_Recommend_Friend"
}
rows {
  id: 500176
  content: "上局最强"
  switch: 1
  stringId: "Arena_Recommend_LastMVP"
}
rows {
  id: 500177
  content: "百胜战神"
  switch: 1
  stringId: "Arena_Recommend_Win100"
}
rows {
  id: 500178
  content: "超神传奇"
  switch: 1
  stringId: "Arena_Recommend_Legendary"
}
rows {
  id: 500179
  content: "连胜5场"
  switch: 1
  stringId: "Arena_Recommend_WinStreak5"
}
rows {
  id: 500180
  content: "暂无段位"
  switch: 1
  stringId: "Arena_Ranking_None"
}
rows {
  id: 500181
  content: "英雄勋章"
  switch: 1
  stringId: "Arena_Hero_Star"
}
rows {
  id: 500182
  content: "全服第{0}名"
  switch: 1
  stringId: "Arena_Ranking_Global"
}
rows {
  id: 500183
  content: "广场星宝"
  switch: 1
  stringId: "Arena_Recommend_Lobby"
}
rows {
  id: 500184
  content: "敬请期待"
  switch: 1
  stringId: "Arena_Honor_Stay_Tuned"
}
rows {
  id: 500185
  content: "高光播报"
  switch: 1
  stringId: "Arena_Customize_Broadcast"
}
rows {
  id: 500186
  content: "心情"
  switch: 1
  stringId: "Arena_Customize_Mood"
}
rows {
  id: 500187
  content: "头像框"
  switch: 1
  stringId: "Arena_Customize_HeadFrame"
}
rows {
  id: 500188
  content: "喷漆"
  switch: 1
  stringId: "Arena_Customize_SparyPaint"
}
rows {
  id: 500189
  content: "三选一礼包"
  switch: 1
  stringId: "Arena_Honor_Three_One"
}
rows {
  id: 500190
  content: "二选一礼包"
  switch: 1
  stringId: "Arena_Honor_Two_One"
}
rows {
  id: 500191
  content: "当前勋章"
  stringId: "Arena_Honor_Cur_Star"
}
rows {
  id: 500192
  content: "返回大厅后将会自动开始下载资源"
  stringId: "Arena_In_Level_Download_Tip"
}
rows {
  id: 500193
  content: "每天前三场胜利可获取星运宝箱（<DayBox>仅限3v3或5v5</>）。\n后两场的宝箱可<DayBox>额外累积2天</>。即使星宝当天并未上线，后续完成也能<DayBox>一起获得</>。"
  switch: 1
  stringId: "Arena_DayBox_Tips"
}
rows {
  id: 500194
  content: "是否在{0}邀请<InviteYellow>{1}</>上线？"
  switch: 1
  stringId: "Arena_Invite_Platform"
}
rows {
  id: 500195
  content: "可获得峡谷皮肤"
  switch: 1
  stringId: "Arena_DayBox_Tips_HY"
}
rows {
  id: 500196
  content: "可获得角色皮肤"
  switch: 1
  stringId: "Arena_DayBox_Tips_QS"
}
rows {
  id: 500197
  content: "确认花费<InviteYellow>{0}</>购买{1}？"
  switch: 1
  stringId: "Arena_HeroTryBuyPopAndName"
}
rows {
  id: 500198
  content: "组队排位#排位积分翻倍#"
  switch: 1
  stringId: "Arena_WeekendWindow_Content"
}
rows {
  id: 500199
  content: "峡谷周末福利"
  switch: 1
  stringId: "Arena_WeekendWindow_TiTle"
}
rows {
  id: 500213
  content: "对局失败有可能扣分。排位赛段位越高，表现分上限越高。接近上限分时，表现分收益会有所衰减。对局失败会降低"
  switch: 1
  stringId: "Arena_Arena_BattleHighest_Tips1"
}
rows {
  id: 500214
  content: "长期不使用英雄，活跃系数会下降"
  switch: 1
  stringId: "Arena_Arena_BattleHighest_Tips2"
}
rows {
  id: 500215
  content: "您已经长时间未操作，长期进入托管状态会影响游戏体验，请快速活跃起来"
  switch: 1
  stringId: "Arena_HostingByAI_Tips_3"
}
rows {
  id: 500216
  content: "队伍成员未解锁该玩法"
  switch: 1
  stringId: "Arena_MatchLocked_Tips"
}
