com.tencent.wea.xlsRes.table_RaffleLuckyValueData
excel/xls/C_抽奖奖池_主表.xlsx sheet:幸运值配置
rows {
  id: 300001
  poolId: 3000
  lvThreshold: 10
  groupWeight: 10
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 33000
  groupWeight: 20
}
rows {
  id: 300002
  poolId: 3000
  lvThreshold: 30
  groupWeight: 10
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 22000
  groupWeight: 50
}
rows {
  id: 300003
  poolId: 3000
  lvThreshold: 40
  groupWeight: 50
  groupWeight: 2
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
  groupWeight: 500
}
rows {
  id: 300004
  poolId: 3000
  lvThreshold: 50
  groupWeight: 50
  groupWeight: 2
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
  groupWeight: 200
}
rows {
  id: 300005
  poolId: 3000
  lvThreshold: 80
  groupWeight: 5
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
  groupWeight: 400
}
rows {
  id: 300006
  poolId: 3000
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
  groupWeight: 350
}
rows {
  id: 300007
  poolId: 3000
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
  groupWeight: 350
}
rows {
  id: 300008
  poolId: 3000
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 80
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
  groupWeight: 50
}
rows {
  id: 300009
  poolId: 3000
  lvThreshold: 200
  groupWeight: 1
  groupWeight: 320
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
  groupWeight: 50
}
rows {
  id: 300010
  poolId: 3000
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 400
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
  groupWeight: 50
}
rows {
  id: 300011
  poolId: 3000
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
  groupWeight: 50
}
rows {
  id: 500001
  poolId: 5000
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 500002
  poolId: 5000
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 500003
  poolId: 5000
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 6500
  groupWeight: 3500
}
rows {
  id: 500004
  poolId: 5000
  lvThreshold: 8
  groupWeight: 140
  groupWeight: 3500
  groupWeight: 2500
}
rows {
  id: 500005
  poolId: 5000
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 3500
  groupWeight: 1500
}
rows {
  id: 500006
  poolId: 5002
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 500007
  poolId: 5002
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 500008
  poolId: 5002
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 6500
  groupWeight: 3500
}
rows {
  id: 500009
  poolId: 5002
  lvThreshold: 8
  groupWeight: 140
  groupWeight: 3500
  groupWeight: 2500
}
rows {
  id: 500010
  poolId: 5002
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 3500
  groupWeight: 1500
}
rows {
  id: 510101
  poolId: 51010
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 510102
  poolId: 51010
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 9985
}
rows {
  id: 510103
  poolId: 51010
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 510104
  poolId: 51011
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510105
  poolId: 51012
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510106
  poolId: 51013
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510107
  poolId: 51014
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510108
  poolId: 51020
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 510109
  poolId: 51020
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 9985
}
rows {
  id: 510110
  poolId: 51020
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 510111
  poolId: 51021
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510112
  poolId: 51022
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510113
  poolId: 51023
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510114
  poolId: 51024
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510115
  poolId: 51030
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 510116
  poolId: 51030
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 9985
}
rows {
  id: 510117
  poolId: 51030
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 510118
  poolId: 51031
  lvThreshold: 7
  groupWeight: 60
  groupWeight: 4
}
rows {
  id: 510119
  poolId: 51032
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510120
  poolId: 51033
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510121
  poolId: 51034
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510122
  poolId: 51040
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 510123
  poolId: 51040
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 9985
}
rows {
  id: 510124
  poolId: 51040
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 510125
  poolId: 51041
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510126
  poolId: 51042
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510127
  poolId: 51043
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510128
  poolId: 51044
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510129
  poolId: 51050
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 510130
  poolId: 51050
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 9985
}
rows {
  id: 510131
  poolId: 51050
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 510132
  poolId: 51051
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510133
  poolId: 51052
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510134
  poolId: 51053
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510135
  poolId: 51054
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 530001
  poolId: 5301
  lvThreshold: 240
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 530002
  poolId: 5302
  lvThreshold: 240
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 540001
  poolId: 5401
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 540002
  poolId: 5401
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 540003
  poolId: 5401
  lvThreshold: 5
  groupWeight: 1
  groupWeight: 19
}
rows {
  id: 540004
  poolId: 5401
  lvThreshold: 6
  groupWeight: 66
  groupWeight: 34
}
rows {
  id: 540005
  poolId: 5402
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 540006
  poolId: 5402
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 540007
  poolId: 5402
  lvThreshold: 5
  groupWeight: 1
  groupWeight: 19
}
rows {
  id: 540008
  poolId: 5402
  lvThreshold: 6
  groupWeight: 66
  groupWeight: 34
}
rows {
  id: 540009
  poolId: 5403
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 540010
  poolId: 5403
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 540011
  poolId: 5403
  lvThreshold: 5
  groupWeight: 1
  groupWeight: 19
}
rows {
  id: 540012
  poolId: 5403
  lvThreshold: 6
  groupWeight: 66
  groupWeight: 34
}
rows {
  id: 100001
  poolId: 1000
  lvThreshold: 5
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 3
  groupWeight: 27
  groupWeight: 15
}
rows {
  id: 100002
  poolId: 1000
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100003
  poolId: 1000
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100004
  poolId: 1000
  lvThreshold: 10
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 3
  groupWeight: 27
  groupWeight: 15
}
rows {
  id: 100005
  poolId: 1000
  lvThreshold: 30
  groupWeight: 45
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 3
  groupWeight: 32
  groupWeight: 15
}
rows {
  id: 100006
  poolId: 1000
  lvThreshold: 100
  groupWeight: 40
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 3
  groupWeight: 32
  groupWeight: 20
}
rows {
  id: 100007
  poolId: 1000
  lvThreshold: 200
  groupWeight: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 3
  groupWeight: 30
  groupWeight: 27
}
rows {
  id: 100008
  poolId: 1000
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 3
  groupWeight: 22
  groupWeight: 42
}
rows {
  id: 100009
  poolId: 1000
  lvThreshold: 600
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 1
  groupWeight: 30
  groupWeight: 62
}
rows {
  id: 100010
  poolId: 10001
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100011
  poolId: 10001
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100012
  poolId: 10001
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100013
  poolId: 10001
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100014
  poolId: 10001
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
}
rows {
  id: 100015
  poolId: 10001
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
}
rows {
  id: 100016
  poolId: 10001
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
}
rows {
  id: 100017
  poolId: 10001
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
}
rows {
  id: 84
  poolId: 13
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 85
  poolId: 13
  lvThreshold: 40
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 86
  poolId: 13
  lvThreshold: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520101
  poolId: 52010
  lvThreshold: 1
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520102
  poolId: 52010
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520103
  poolId: 52010
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 520104
  poolId: 52010
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520105
  poolId: 52010
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520106
  poolId: 52010
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520107
  poolId: 52010
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520110
  poolId: 52011
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 800001
  poolId: 8000
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 100
}
rows {
  id: 700001
  poolId: 7000
  lvThreshold: 2
  groupWeight: 10000
  groupWeight: 1
  groupWeight: 989999
}
rows {
  id: 700002
  poolId: 7000
  lvThreshold: 3
  groupWeight: 970000
  groupWeight: 1
  groupWeight: 29999
}
rows {
  id: 700003
  poolId: 7000
  lvThreshold: 7
  groupWeight: 10000
  groupWeight: 1
  groupWeight: 989999
}
rows {
  id: 700004
  poolId: 7000
  lvThreshold: 8
  groupWeight: 970000
  groupWeight: 1
  groupWeight: 29999
}
rows {
  id: 700005
  poolId: 7000
  lvThreshold: 11
  groupWeight: 10000
  groupWeight: 1
  groupWeight: 989999
}
rows {
  id: 700006
  poolId: 7000
  lvThreshold: 12
  groupWeight: 970000
  groupWeight: 1
  groupWeight: 29999
}
rows {
  id: 700007
  poolId: 7000
  lvThreshold: 34
  groupWeight: 300000
  groupWeight: 1
  groupWeight: 699999
}
rows {
  id: 520201
  poolId: 5202
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 520202
  poolId: 5202
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 520203
  poolId: 5202
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 520204
  poolId: 5202
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 520205
  poolId: 5202
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 300101
  poolId: 30001
  lvThreshold: 1
  groupWeight: 150
  groupWeight: 1500
  groupWeight: 2500
  groupWeight: 15
}
rows {
  id: 300102
  poolId: 30001
  lvThreshold: 200
  groupWeight: 2500
  groupWeight: 1500
  groupWeight: 150
  groupWeight: 15
}
rows {
  id: 500101
  poolId: 5001
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500102
  poolId: 5001
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500103
  poolId: 5001
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 500104
  poolId: 5001
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 500105
  poolId: 5001
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500301
  poolId: 5003
  lvThreshold: 6
  groupWeight: 1
  groupWeight: 10
  groupWeight: 989
}
rows {
  id: 500302
  poolId: 5003
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 50
  groupWeight: 945
}
rows {
  id: 500303
  poolId: 5003
  lvThreshold: 12
  groupWeight: 50
  groupWeight: 500
  groupWeight: 450
}
rows {
  id: 500401
  poolId: 5004
  lvThreshold: 6
  groupWeight: 1
  groupWeight: 10
  groupWeight: 989
}
rows {
  id: 500402
  poolId: 5004
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 50
  groupWeight: 945
}
rows {
  id: 500403
  poolId: 5004
  lvThreshold: 12
  groupWeight: 50
  groupWeight: 500
  groupWeight: 450
}
rows {
  id: 500501
  poolId: 5005
  lvThreshold: 6
  groupWeight: 1
  groupWeight: 10
  groupWeight: 989
}
rows {
  id: 500502
  poolId: 5005
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 50
  groupWeight: 945
}
rows {
  id: 500503
  poolId: 5005
  lvThreshold: 12
  groupWeight: 50
  groupWeight: 500
  groupWeight: 450
}
rows {
  id: 300201
  poolId: 30002
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 33000
}
rows {
  id: 300202
  poolId: 30002
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 22000
}
rows {
  id: 300203
  poolId: 30002
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 300204
  poolId: 30002
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 300205
  poolId: 30002
  lvThreshold: 80
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300206
  poolId: 30002
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300207
  poolId: 30002
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 300208
  poolId: 30002
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 60
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 300209
  poolId: 30002
  lvThreshold: 200
  groupWeight: 1
  groupWeight: 180
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 300210
  poolId: 30002
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300211
  poolId: 30002
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 501001
  poolId: 5010
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 100
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 501002
  poolId: 5010
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 501003
  poolId: 5010
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 501004
  poolId: 5010
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 501005
  poolId: 5010
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 800101
  poolId: 8001
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2871
}
rows {
  id: 800201
  poolId: 8002
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 800301
  poolId: 8003
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 800701
  poolId: 8007
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 501101
  poolId: 5011
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 501102
  poolId: 5011
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 501103
  poolId: 5011
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 501104
  poolId: 5011
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 501105
  poolId: 5011
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 510615
  poolId: 51060
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 510616
  poolId: 51060
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 9985
}
rows {
  id: 510617
  poolId: 51060
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 510618
  poolId: 51061
  lvThreshold: 7
  groupWeight: 60
  groupWeight: 4
}
rows {
  id: 510619
  poolId: 51062
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510620
  poolId: 51063
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510621
  poolId: 51064
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510700
  poolId: 51070
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 100
  groupWeight: 899
}
rows {
  id: 510701
  poolId: 51070
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 2000
  groupWeight: 7985
}
rows {
  id: 510702
  poolId: 51070
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 4000
  groupWeight: 5985
}
rows {
  id: 510703
  poolId: 51070
  lvThreshold: 5
  groupWeight: 1
  groupWeight: 60
  groupWeight: 39
}
rows {
  id: 510704
  poolId: 51070
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 70
  groupWeight: 28
}
rows {
  id: 510705
  poolId: 51071
  lvThreshold: 3
  groupWeight: 80
  groupWeight: 20
}
rows {
  id: 510706
  poolId: 51071
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 510707
  poolId: 51072
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510708
  poolId: 51073
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510709
  poolId: 51074
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510710
  poolId: 51075
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510711
  poolId: 51076
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510800
  poolId: 51080
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 100
  groupWeight: 899
}
rows {
  id: 510801
  poolId: 51080
  lvThreshold: 3
  groupWeight: 15
  groupWeight: 2000
  groupWeight: 7985
}
rows {
  id: 510802
  poolId: 51080
  lvThreshold: 4
  groupWeight: 15
  groupWeight: 4000
  groupWeight: 5985
}
rows {
  id: 510803
  poolId: 51080
  lvThreshold: 5
  groupWeight: 1
  groupWeight: 60
  groupWeight: 39
}
rows {
  id: 510804
  poolId: 51080
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 70
  groupWeight: 28
}
rows {
  id: 510805
  poolId: 51081
  lvThreshold: 3
  groupWeight: 80
  groupWeight: 20
}
rows {
  id: 510806
  poolId: 51081
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 510807
  poolId: 51082
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510808
  poolId: 51083
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510809
  poolId: 51084
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510810
  poolId: 51085
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510811
  poolId: 51086
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 510901
  poolId: 51091
  lvThreshold: 1
  groupWeight: 1
}
rows {
  id: 501201
  poolId: 5012
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 501202
  poolId: 5012
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 501203
  poolId: 5012
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 501204
  poolId: 5012
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 501205
  poolId: 5012
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 300301
  poolId: 30003
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 33000
}
rows {
  id: 300302
  poolId: 30003
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 22000
}
rows {
  id: 300303
  poolId: 30003
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 300304
  poolId: 30003
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 300305
  poolId: 30003
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300306
  poolId: 30003
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300307
  poolId: 30003
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 300308
  poolId: 30003
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 300309
  poolId: 30003
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 300310
  poolId: 30003
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300311
  poolId: 30003
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 100018
  poolId: 10002
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100019
  poolId: 10002
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100020
  poolId: 10002
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100021
  poolId: 10002
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100022
  poolId: 10002
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
}
rows {
  id: 100023
  poolId: 10002
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
}
rows {
  id: 100024
  poolId: 10002
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
}
rows {
  id: 100025
  poolId: 10002
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
}
rows {
  id: 100026
  poolId: 10003
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100027
  poolId: 10003
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100028
  poolId: 10003
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100029
  poolId: 10003
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100030
  poolId: 10003
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
}
rows {
  id: 100031
  poolId: 10003
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
}
rows {
  id: 100032
  poolId: 10003
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
}
rows {
  id: 100033
  poolId: 10003
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
}
rows {
  id: 100034
  poolId: 10004
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100035
  poolId: 10004
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100036
  poolId: 10004
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100037
  poolId: 10004
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100038
  poolId: 10004
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
}
rows {
  id: 100039
  poolId: 10004
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
}
rows {
  id: 100040
  poolId: 10004
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
}
rows {
  id: 100041
  poolId: 10004
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
}
rows {
  id: 100042
  poolId: 10005
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100043
  poolId: 10005
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100044
  poolId: 10005
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100045
  poolId: 10005
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100046
  poolId: 10005
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
}
rows {
  id: 100047
  poolId: 10005
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
}
rows {
  id: 100048
  poolId: 10005
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
}
rows {
  id: 100049
  poolId: 10005
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
}
rows {
  id: 501601
  poolId: 5016
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
  groupWeight: 3800
}
rows {
  id: 501602
  poolId: 5016
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
  groupWeight: 3800
}
rows {
  id: 501603
  poolId: 5016
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
  groupWeight: 3800
}
rows {
  id: 501604
  poolId: 5016
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3800
}
rows {
  id: 501605
  poolId: 5016
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 3800
}
rows {
  id: 501606
  poolId: 5016
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3800
}
rows {
  id: 500011
  poolId: 5014
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 500012
  poolId: 5014
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 500013
  poolId: 5014
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 500014
  poolId: 5014
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 500015
  poolId: 5014
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 500016
  poolId: 5017
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500017
  poolId: 5017
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500018
  poolId: 5017
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 500019
  poolId: 5017
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 500020
  poolId: 5017
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500021
  poolId: 5018
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500022
  poolId: 5018
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 500023
  poolId: 5018
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 500024
  poolId: 5018
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 500025
  poolId: 5018
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 300401
  poolId: 30004
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 33000
}
rows {
  id: 300402
  poolId: 30004
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 22000
}
rows {
  id: 300403
  poolId: 30004
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 300404
  poolId: 30004
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 300405
  poolId: 30004
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300406
  poolId: 30004
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300407
  poolId: 30004
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 300408
  poolId: 30004
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 300409
  poolId: 30004
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 300410
  poolId: 30004
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 300411
  poolId: 30004
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 500026
  poolId: 5019
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 500027
  poolId: 5019
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 500028
  poolId: 5019
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
}
rows {
  id: 500029
  poolId: 5019
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 500030
  poolId: 5019
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 500031
  poolId: 5019
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 500032
  poolId: 5020
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 500033
  poolId: 5020
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 500034
  poolId: 5020
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
}
rows {
  id: 500035
  poolId: 5020
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 500036
  poolId: 5020
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 500037
  poolId: 5020
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 500038
  poolId: 5021
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 10
  groupWeight: 1000
}
rows {
  id: 500039
  poolId: 5021
  lvThreshold: 5
  groupWeight: 2
  groupWeight: 20
  groupWeight: 1200
}
rows {
  id: 500040
  poolId: 5021
  lvThreshold: 8
  groupWeight: 4
  groupWeight: 40
  groupWeight: 1500
}
rows {
  id: 500041
  poolId: 5021
  lvThreshold: 10
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 500042
  poolId: 5021
  lvThreshold: 11
  groupWeight: 80
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 500043
  poolId: 5021
  lvThreshold: 12
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
}
rows {
  id: 550101
  poolId: 55010
  lvThreshold: 0
  groupWeight: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 20
}
rows {
  id: 550102
  poolId: 55010
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 50
}
rows {
  id: 550103
  poolId: 55010
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 125
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 875
}
rows {
  id: 550104
  poolId: 55010
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 91
}
rows {
  id: 550105
  poolId: 55010
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 95
}
rows {
  id: 550106
  poolId: 55010
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 96
}
rows {
  id: 550107
  poolId: 55010
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 965
}
rows {
  id: 550108
  poolId: 55010
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 97
}
rows {
  id: 550109
  poolId: 55010
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 0
  groupWeight: 98
}
rows {
  id: 550110
  poolId: 55010
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 550111
  poolId: 55010
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 550112
  poolId: 55011
  lvThreshold: 120
  groupWeight: 1
  groupWeight: 3399
  groupWeight: 100
  groupWeight: 25500
  groupWeight: 20000
  groupWeight: 15000
  groupWeight: 20000
  groupWeight: 16000
}
rows {
  id: 511000
  poolId: 51100
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 511001
  poolId: 51100
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 511002
  poolId: 51100
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511003
  poolId: 51101
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511004
  poolId: 51102
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511005
  poolId: 51103
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511006
  poolId: 51104
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511007
  poolId: 51110
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 511008
  poolId: 51110
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 511009
  poolId: 51110
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511010
  poolId: 51111
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511013
  poolId: 51112
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511014
  poolId: 51113
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511015
  poolId: 51114
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 540401
  poolId: 5404
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 30
  groupWeight: 969
}
rows {
  id: 540402
  poolId: 5404
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 300
  groupWeight: 695
}
rows {
  id: 540403
  poolId: 5404
  lvThreshold: 5
  groupWeight: 5
  groupWeight: 1
  groupWeight: 94
}
rows {
  id: 540404
  poolId: 5404
  lvThreshold: 6
  groupWeight: 66
  groupWeight: 1
  groupWeight: 33
}
rows {
  id: 540501
  poolId: 5405
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 30
  groupWeight: 969
}
rows {
  id: 540502
  poolId: 5405
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 300
  groupWeight: 695
}
rows {
  id: 540503
  poolId: 5405
  lvThreshold: 5
  groupWeight: 5
  groupWeight: 1
  groupWeight: 94
}
rows {
  id: 540504
  poolId: 5405
  lvThreshold: 6
  groupWeight: 66
  groupWeight: 1
  groupWeight: 33
}
rows {
  id: 511201
  poolId: 51120
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 511202
  poolId: 51120
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 511203
  poolId: 51120
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511204
  poolId: 51121
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511205
  poolId: 51122
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511206
  poolId: 51123
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511207
  poolId: 51124
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511208
  poolId: 51130
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 511209
  poolId: 51130
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 511210
  poolId: 51130
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511211
  poolId: 51131
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511212
  poolId: 51132
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511213
  poolId: 51133
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511214
  poolId: 51134
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511310
  poolId: 5303
  lvThreshold: 280
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 502301
  poolId: 5023
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 502302
  poolId: 5023
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 502303
  poolId: 5023
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 502304
  poolId: 5023
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 502305
  poolId: 5023
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 5027001
  poolId: 5027
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5027002
  poolId: 5027
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5027003
  poolId: 5027
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5027004
  poolId: 5027
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5027005
  poolId: 5027
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5028001
  poolId: 5028
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5028002
  poolId: 5028
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5028003
  poolId: 5028
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5028004
  poolId: 5028
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5028005
  poolId: 5028
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 520301
  poolId: 52030
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520302
  poolId: 52030
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 520303
  poolId: 52030
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520304
  poolId: 52030
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520305
  poolId: 52030
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520306
  poolId: 52030
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520307
  poolId: 52030
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520308
  poolId: 52030
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520309
  poolId: 52030
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520310
  poolId: 52030
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520311
  poolId: 52030
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520312
  poolId: 52030
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520313
  poolId: 52031
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 800400
  poolId: 8004
  lvThreshold: 100
  groupWeight: 1
}
rows {
  id: 502501
  poolId: 5025
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 900
}
rows {
  id: 502502
  poolId: 5025
  lvThreshold: 60
  groupWeight: 10
  groupWeight: 90
  groupWeight: 900
}
rows {
  id: 5026001
  poolId: 5026
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5026002
  poolId: 5026
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5026003
  poolId: 5026
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5026004
  poolId: 5026
  lvThreshold: 8
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5026005
  poolId: 5026
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 700008
  poolId: 7001
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 700009
  poolId: 7001
  lvThreshold: 30
  groupWeight: 1
  groupWeight: 1
  groupWeight: 2
}
rows {
  id: 5029001
  poolId: 5029
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5029002
  poolId: 5029
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5029003
  poolId: 5029
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5029004
  poolId: 5029
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5029005
  poolId: 5029
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 3000501
  poolId: 30005
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 33000
}
rows {
  id: 3000502
  poolId: 30005
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 22000
}
rows {
  id: 3000503
  poolId: 30005
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 3000504
  poolId: 30005
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 3000505
  poolId: 30005
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000506
  poolId: 30005
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000507
  poolId: 30005
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3000508
  poolId: 30005
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000509
  poolId: 30005
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000510
  poolId: 30005
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000511
  poolId: 30005
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 5700101
  poolId: 57001
  lvThreshold: 50
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700102
  poolId: 57001
  lvThreshold: 100
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700103
  poolId: 57001
  lvThreshold: 150
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700104
  poolId: 57001
  lvThreshold: 200
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700105
  poolId: 57001
  lvThreshold: 250
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700106
  poolId: 57001
  lvThreshold: 300
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700107
  poolId: 57001
  lvThreshold: 350
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700108
  poolId: 57001
  lvThreshold: 1545
  groupWeight: 2716
  groupWeight: 40742
  groupWeight: 8148
  groupWeight: 27161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 503001
  poolId: 5030
  lvThreshold: 6
  groupWeight: 1
  groupWeight: 10
  groupWeight: 989
}
rows {
  id: 503002
  poolId: 5030
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 50
  groupWeight: 945
}
rows {
  id: 503003
  poolId: 5030
  lvThreshold: 12
  groupWeight: 50
  groupWeight: 500
  groupWeight: 450
}
rows {
  id: 503004
  poolId: 30006
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503005
  poolId: 30006
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503006
  poolId: 30006
  lvThreshold: 11
  groupWeight: 5
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503007
  poolId: 30006
  lvThreshold: 21
  groupWeight: 10
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503008
  poolId: 30006
  lvThreshold: 31
  groupWeight: 15
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503009
  poolId: 30006
  lvThreshold: 39
  groupWeight: 3000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503010
  poolId: 30006
  lvThreshold: 40
  groupWeight: 4000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503011
  poolId: 30006
  lvThreshold: 41
  groupWeight: 5000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503012
  poolId: 30006
  lvThreshold: 42
  groupWeight: 10000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503013
  poolId: 30006
  lvThreshold: 43
  groupWeight: 15000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503014
  poolId: 30006
  lvThreshold: 44
  groupWeight: 20000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503015
  poolId: 30006
  lvThreshold: 45
  groupWeight: 25000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503016
  poolId: 30006
  lvThreshold: 46
  groupWeight: 100000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503017
  poolId: 30006
  lvThreshold: 47
  groupWeight: 150000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503018
  poolId: 30006
  lvThreshold: 48
  groupWeight: 200000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503019
  poolId: 30006
  lvThreshold: 49
  groupWeight: 250000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 503020
  poolId: 30006
  lvThreshold: 50
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5031001
  poolId: 5031
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5031002
  poolId: 5031
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5031003
  poolId: 5031
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5031004
  poolId: 5031
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5031005
  poolId: 5031
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 5032001
  poolId: 5032
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5032002
  poolId: 5032
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5032003
  poolId: 5032
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5032004
  poolId: 5032
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5032005
  poolId: 5032
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 5033001
  poolId: 5033
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 3500
  groupWeight: 6000
}
rows {
  id: 5033002
  poolId: 5033
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1500
  groupWeight: 3500
  groupWeight: 6000
}
rows {
  id: 5033003
  poolId: 5033
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 500
  groupWeight: 3500
  groupWeight: 6000
}
rows {
  id: 5033004
  poolId: 5033
  lvThreshold: 8
  groupWeight: 140
  groupWeight: 3500
  groupWeight: 2500
  groupWeight: 6000
}
rows {
  id: 5033005
  poolId: 5033
  lvThreshold: 9
  groupWeight: 10000
  groupWeight: 3500
  groupWeight: 1500
  groupWeight: 6000
}
rows {
  id: 5034001
  poolId: 5034
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1500
  groupWeight: 3500
  groupWeight: 6000
}
rows {
  id: 5034002
  poolId: 5034
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1500
  groupWeight: 3500
  groupWeight: 6000
}
rows {
  id: 5034003
  poolId: 5034
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 500
  groupWeight: 3500
  groupWeight: 6000
}
rows {
  id: 5034004
  poolId: 5034
  lvThreshold: 8
  groupWeight: 140
  groupWeight: 3500
  groupWeight: 2500
  groupWeight: 6000
}
rows {
  id: 5034005
  poolId: 5034
  lvThreshold: 9
  groupWeight: 10000
  groupWeight: 3500
  groupWeight: 1500
  groupWeight: 6000
}
rows {
  id: 503501
  poolId: 5035
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 503502
  poolId: 5035
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 503503
  poolId: 5035
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 503504
  poolId: 5035
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 503505
  poolId: 5035
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 1500
}
rows {
  id: 550201
  poolId: 55020
  lvThreshold: 0
  groupWeight: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 20
}
rows {
  id: 550202
  poolId: 55020
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 50
}
rows {
  id: 550203
  poolId: 55020
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 125
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 875
}
rows {
  id: 550204
  poolId: 55020
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 91
}
rows {
  id: 550205
  poolId: 55020
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 95
}
rows {
  id: 550206
  poolId: 55020
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 96
}
rows {
  id: 550207
  poolId: 55020
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 965
}
rows {
  id: 550208
  poolId: 55020
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 97
}
rows {
  id: 550209
  poolId: 55020
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 0
  groupWeight: 98
}
rows {
  id: 550210
  poolId: 55020
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 550211
  poolId: 55020
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 550212
  poolId: 55021
  lvThreshold: 120
  groupWeight: 1
  groupWeight: 3399
  groupWeight: 100
  groupWeight: 25500
  groupWeight: 20000
  groupWeight: 15000
  groupWeight: 20000
  groupWeight: 16000
}
rows {
  id: 503701
  poolId: 5037
  lvThreshold: 6
  groupWeight: 1
  groupWeight: 10
  groupWeight: 989
}
rows {
  id: 503702
  poolId: 5037
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 50
  groupWeight: 945
}
rows {
  id: 503703
  poolId: 5037
  lvThreshold: 12
  groupWeight: 50
  groupWeight: 500
  groupWeight: 450
}
rows {
  id: 503801
  poolId: 5038
  lvThreshold: 6
  groupWeight: 1
  groupWeight: 10
  groupWeight: 989
}
rows {
  id: 503802
  poolId: 5038
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 50
  groupWeight: 945
}
rows {
  id: 503803
  poolId: 5038
  lvThreshold: 12
  groupWeight: 50
  groupWeight: 500
  groupWeight: 450
}
rows {
  id: 511401
  poolId: 51140
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 511402
  poolId: 51140
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511403
  poolId: 51140
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511404
  poolId: 51141
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 511405
  poolId: 51142
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511406
  poolId: 51143
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511407
  poolId: 51144
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511501
  poolId: 51150
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 511502
  poolId: 51150
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511503
  poolId: 51150
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 511504
  poolId: 51151
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 511505
  poolId: 51152
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511506
  poolId: 51153
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511507
  poolId: 51154
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 503901
  poolId: 5039
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 503902
  poolId: 5039
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 503903
  poolId: 5039
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 503904
  poolId: 5039
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 503905
  poolId: 5039
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 503806
  poolId: 51155
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503807
  poolId: 51155
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503808
  poolId: 51155
  lvThreshold: 20
  groupWeight: 4
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503809
  poolId: 51155
  lvThreshold: 35
  groupWeight: 5
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503810
  poolId: 51156
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503811
  poolId: 51156
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503812
  poolId: 51156
  lvThreshold: 20
  groupWeight: 2
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503813
  poolId: 51156
  lvThreshold: 35
  groupWeight: 5
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503814
  poolId: 51157
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503815
  poolId: 51157
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503816
  poolId: 51157
  lvThreshold: 20
  groupWeight: 2
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503817
  poolId: 51157
  lvThreshold: 35
  groupWeight: 5
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 520401
  poolId: 52040
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520402
  poolId: 52040
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 520403
  poolId: 52040
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520404
  poolId: 52040
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520405
  poolId: 52040
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520406
  poolId: 52040
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520407
  poolId: 52040
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520408
  poolId: 52040
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520409
  poolId: 52040
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520410
  poolId: 52040
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520411
  poolId: 52040
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520412
  poolId: 52040
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520413
  poolId: 52041
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 5700201
  poolId: 57002
  lvThreshold: 19
  groupWeight: 2716
  groupWeight: 8148
  groupWeight: 40742
  groupWeight: 15161
  groupWeight: 5432
  groupWeight: 5432
  groupWeight: 13581
  groupWeight: 168400
}
rows {
  id: 5700202
  poolId: 57002
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 13581
  groupWeight: 0
}
rows {
  id: 503601
  poolId: 5036
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 503602
  poolId: 5036
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 503603
  poolId: 5036
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 503604
  poolId: 5036
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 503605
  poolId: 5036
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 511600
  poolId: 51160
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 100
  groupWeight: 899
}
rows {
  id: 511601
  poolId: 51160
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 20
  groupWeight: 78
}
rows {
  id: 511602
  poolId: 51160
  lvThreshold: 4
  groupWeight: 3
  groupWeight: 40
  groupWeight: 57
}
rows {
  id: 511603
  poolId: 51160
  lvThreshold: 5
  groupWeight: 5
  groupWeight: 55
  groupWeight: 39
}
rows {
  id: 511604
  poolId: 51160
  lvThreshold: 6
  groupWeight: 8
  groupWeight: 70
  groupWeight: 22
}
rows {
  id: 511605
  poolId: 51161
  lvThreshold: 3
  groupWeight: 80
  groupWeight: 20
}
rows {
  id: 511606
  poolId: 51161
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 511607
  poolId: 51162
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511608
  poolId: 51163
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511609
  poolId: 51164
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511610
  poolId: 51165
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511611
  poolId: 51166
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511700
  poolId: 51170
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 100
  groupWeight: 899
}
rows {
  id: 511701
  poolId: 51170
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 20
  groupWeight: 78
}
rows {
  id: 511702
  poolId: 51170
  lvThreshold: 4
  groupWeight: 3
  groupWeight: 40
  groupWeight: 57
}
rows {
  id: 511703
  poolId: 51170
  lvThreshold: 5
  groupWeight: 5
  groupWeight: 55
  groupWeight: 39
}
rows {
  id: 511704
  poolId: 51170
  lvThreshold: 6
  groupWeight: 8
  groupWeight: 70
  groupWeight: 22
}
rows {
  id: 511705
  poolId: 51171
  lvThreshold: 3
  groupWeight: 80
  groupWeight: 20
}
rows {
  id: 511706
  poolId: 51171
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 511707
  poolId: 51172
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511708
  poolId: 51173
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511709
  poolId: 51174
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511710
  poolId: 51175
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511711
  poolId: 51176
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 511801
  poolId: 51181
  lvThreshold: 1
  groupWeight: 1
}
rows {
  id: 540601
  poolId: 5406
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 540602
  poolId: 5406
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 540603
  poolId: 5406
  lvThreshold: 5
  groupWeight: 1
  groupWeight: 19
}
rows {
  id: 540604
  poolId: 5406
  lvThreshold: 6
  groupWeight: 66
  groupWeight: 34
}
rows {
  id: 3000701
  poolId: 30007
  lvThreshold: 10
  groupWeight: 3
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 16000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 33000
}
rows {
  id: 3000702
  poolId: 30007
  lvThreshold: 30
  groupWeight: 5
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 2000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 22000
}
rows {
  id: 3000703
  poolId: 30007
  lvThreshold: 40
  groupWeight: 8
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 3000704
  poolId: 30007
  lvThreshold: 50
  groupWeight: 25
  groupWeight: 1
  groupWeight: 7000
  groupWeight: 4000
  groupWeight: 12000
  groupWeight: 10000
  groupWeight: 27000
  groupWeight: 8000
  groupWeight: 18500
  groupWeight: 25000
  groupWeight: 5000
  groupWeight: 24000
}
rows {
  id: 3000705
  poolId: 30007
  lvThreshold: 100
  groupWeight: 4
  groupWeight: 1
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1000
  groupWeight: 2500
  groupWeight: 800
  groupWeight: 1850
  groupWeight: 2500
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000706
  poolId: 30007
  lvThreshold: 129
  groupWeight: 220
  groupWeight: 2
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 750
  groupWeight: 2000
  groupWeight: 2670
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000707
  poolId: 30007
  lvThreshold: 140
  groupWeight: 400
  groupWeight: 4
  groupWeight: 700
  groupWeight: 400
  groupWeight: 1000
  groupWeight: 1100
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 600
  groupWeight: 2400
}
rows {
  id: 3000708
  poolId: 30007
  lvThreshold: 180
  groupWeight: 1
  groupWeight: 40
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000709
  poolId: 30007
  lvThreshold: 230
  groupWeight: 1
  groupWeight: 90
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 700
  groupWeight: 2400
}
rows {
  id: 3000710
  poolId: 30007
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 380
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 3000711
  poolId: 30007
  lvThreshold: 350
  groupWeight: 1
  groupWeight: 500
  groupWeight: 500
  groupWeight: 300
  groupWeight: 800
  groupWeight: 1600
  groupWeight: 2400
  groupWeight: 820
  groupWeight: 2000
  groupWeight: 2600
  groupWeight: 500
  groupWeight: 2400
}
rows {
  id: 503818
  poolId: 51158
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503819
  poolId: 51158
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 20
  groupWeight: 280
}
rows {
  id: 503820
  poolId: 51158
  lvThreshold: 30
  groupWeight: 2
  groupWeight: 20
  groupWeight: 250
}
rows {
  id: 503821
  poolId: 51158
  lvThreshold: 45
  groupWeight: 5
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503822
  poolId: 51159
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 503823
  poolId: 51159
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 20
  groupWeight: 280
}
rows {
  id: 503824
  poolId: 51159
  lvThreshold: 30
  groupWeight: 2
  groupWeight: 20
  groupWeight: 250
}
rows {
  id: 503825
  poolId: 51159
  lvThreshold: 45
  groupWeight: 5
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 530501
  poolId: 5305
  lvThreshold: 50
  groupWeight: 1
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 8400
}
rows {
  id: 530502
  poolId: 5305
  lvThreshold: 80
  groupWeight: 1
  groupWeight: 4
  groupWeight: 12
  groupWeight: 84
}
rows {
  id: 530503
  poolId: 5305
  lvThreshold: 300
  groupWeight: 1
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 8400
}
rows {
  id: 504901
  poolId: 5049
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 504902
  poolId: 5049
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 504903
  poolId: 5049
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 504904
  poolId: 5049
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 504905
  poolId: 5049
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5043001
  poolId: 5043
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5043002
  poolId: 5043
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5043003
  poolId: 5043
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5043004
  poolId: 5043
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5043005
  poolId: 5043
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 5044001
  poolId: 5044
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5044002
  poolId: 5044
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5044003
  poolId: 5044
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5044004
  poolId: 5044
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5044005
  poolId: 5044
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 505101
  poolId: 5051
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 505102
  poolId: 5051
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 505103
  poolId: 5051
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 505104
  poolId: 5051
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 505105
  poolId: 5051
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 800500
  poolId: 8005
  lvThreshold: 100
  groupWeight: 2
  groupWeight: 10
  groupWeight: 20
  groupWeight: 43
  groupWeight: 25
}
rows {
  id: 505106
  poolId: 5306
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 505107
  poolId: 5306
  lvThreshold: 280
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 20000001
  poolId: 7002
  lvThreshold: 30
  groupWeight: 1
}
rows {
  id: 5304001
  poolId: 5304
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304002
  poolId: 5304
  lvThreshold: 15
  groupWeight: 2
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304003
  poolId: 5304
  lvThreshold: 20
  groupWeight: 3
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304004
  poolId: 5304
  lvThreshold: 25
  groupWeight: 4
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304005
  poolId: 5304
  lvThreshold: 26
  groupWeight: 5
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304006
  poolId: 5304
  lvThreshold: 27
  groupWeight: 10
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304007
  poolId: 5304
  lvThreshold: 28
  groupWeight: 15
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304008
  poolId: 5304
  lvThreshold: 29
  groupWeight: 20
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304009
  poolId: 5304
  lvThreshold: 30
  groupWeight: 25
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304010
  poolId: 5304
  lvThreshold: 31
  groupWeight: 700
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304011
  poolId: 5304
  lvThreshold: 32
  groupWeight: 1000
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304012
  poolId: 5304
  lvThreshold: 33
  groupWeight: 2000
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304013
  poolId: 5304
  lvThreshold: 34
  groupWeight: 3000
  groupWeight: 50
  groupWeight: 500
  groupWeight: 1450
  groupWeight: 10
}
rows {
  id: 5304014
  poolId: 5304
  lvThreshold: 35
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 600001
  poolId: 6000
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 300
  groupWeight: 500
  groupWeight: 500
  groupWeight: 4000
  groupWeight: 4700
}
rows {
  id: 600002
  poolId: 6000
  lvThreshold: 20
  groupWeight: 10
  groupWeight: 300
  groupWeight: 500
  groupWeight: 500
  groupWeight: 4000
  groupWeight: 4700
}
rows {
  id: 600003
  poolId: 6000
  lvThreshold: 40
  groupWeight: 20
  groupWeight: 300
  groupWeight: 500
  groupWeight: 500
  groupWeight: 4000
  groupWeight: 4700
}
rows {
  id: 600004
  poolId: 6000
  lvThreshold: 70
  groupWeight: 40
  groupWeight: 300
  groupWeight: 500
  groupWeight: 500
  groupWeight: 4000
  groupWeight: 4700
}
rows {
  id: 600005
  poolId: 6000
  lvThreshold: 130
  groupWeight: 110
  groupWeight: 300
  groupWeight: 500
  groupWeight: 500
  groupWeight: 4000
  groupWeight: 4700
}
rows {
  id: 5053001
  poolId: 5053
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5053002
  poolId: 5053
  lvThreshold: 6
  groupWeight: 3
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5053003
  poolId: 5053
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5053004
  poolId: 5053
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5053005
  poolId: 5053
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 5054001
  poolId: 5054
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5054002
  poolId: 5054
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5054003
  poolId: 5054
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5054004
  poolId: 5054
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5054005
  poolId: 5054
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 520501
  poolId: 52050
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520502
  poolId: 52050
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 520503
  poolId: 52050
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520504
  poolId: 52050
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520505
  poolId: 52050
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520506
  poolId: 52050
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520507
  poolId: 52050
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520508
  poolId: 52050
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520509
  poolId: 52050
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520510
  poolId: 52050
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520511
  poolId: 52050
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520512
  poolId: 52050
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520513
  poolId: 52051
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 530003
  poolId: 5307
  lvThreshold: 240
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 500045
  poolId: 5055
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 500046
  poolId: 5055
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 500047
  poolId: 5055
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 6500
  groupWeight: 3500
}
rows {
  id: 500048
  poolId: 5055
  lvThreshold: 8
  groupWeight: 140
  groupWeight: 3500
  groupWeight: 2500
}
rows {
  id: 500049
  poolId: 5055
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 3500
  groupWeight: 1500
}
rows {
  id: 800801
  poolId: 8008
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 520601
  poolId: 52060
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520602
  poolId: 52060
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 520603
  poolId: 52060
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520604
  poolId: 52060
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520605
  poolId: 52060
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520606
  poolId: 52060
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520607
  poolId: 52060
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520608
  poolId: 52060
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520609
  poolId: 52060
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520610
  poolId: 52060
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520611
  poolId: 52060
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520612
  poolId: 52060
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520613
  poolId: 52061
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 540701
  poolId: 5407
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 999
}
rows {
  id: 540702
  poolId: 5407
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 540703
  poolId: 5407
  lvThreshold: 5
  groupWeight: 1
  groupWeight: 19
}
rows {
  id: 540704
  poolId: 5407
  lvThreshold: 6
  groupWeight: 66
  groupWeight: 34
}
rows {
  id: 504101
  poolId: 5041
  lvThreshold: 6
  groupWeight: 1
  groupWeight: 10
  groupWeight: 989
}
rows {
  id: 504102
  poolId: 5041
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 50
  groupWeight: 945
}
rows {
  id: 504103
  poolId: 5041
  lvThreshold: 12
  groupWeight: 50
  groupWeight: 500
  groupWeight: 450
}
rows {
  id: 505603
  poolId: 5056
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 505607
  poolId: 5056
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 505606
  poolId: 5056
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 505601
  poolId: 5056
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 505602
  poolId: 5056
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 520701
  poolId: 52070
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520702
  poolId: 52070
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 520703
  poolId: 52070
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520704
  poolId: 52070
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520705
  poolId: 52070
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520706
  poolId: 52070
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520707
  poolId: 52070
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520708
  poolId: 52070
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520709
  poolId: 52070
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520710
  poolId: 52070
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520711
  poolId: 52070
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520712
  poolId: 52070
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520713
  poolId: 52071
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 5057001
  poolId: 5057
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5057002
  poolId: 5057
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5057003
  poolId: 5057
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5057004
  poolId: 5057
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5057007
  poolId: 5057
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5700301
  poolId: 57003
  lvThreshold: 19
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 1
  groupWeight: 1
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 5700302
  poolId: 57003
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 13581
  groupWeight: 0
}
rows {
  id: 800901
  poolId: 8009
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 520901
  poolId: 52090
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 520902
  poolId: 52090
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 520903
  poolId: 52090
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520904
  poolId: 52090
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520905
  poolId: 52090
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520906
  poolId: 52090
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520907
  poolId: 52090
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520908
  poolId: 52090
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520909
  poolId: 52090
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520910
  poolId: 52090
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520911
  poolId: 52090
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520912
  poolId: 52090
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 520913
  poolId: 52091
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 801001
  poolId: 8010
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 5201001
  poolId: 52100
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 5201002
  poolId: 52100
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 5201003
  poolId: 52100
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201004
  poolId: 52100
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201005
  poolId: 52100
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201006
  poolId: 52100
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201007
  poolId: 52100
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201008
  poolId: 52100
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201009
  poolId: 52100
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201010
  poolId: 52100
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201011
  poolId: 52100
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201012
  poolId: 52100
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5201013
  poolId: 52101
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 505801
  poolId: 5058
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 505802
  poolId: 5058
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 505803
  poolId: 5058
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 6500
  groupWeight: 3500
}
rows {
  id: 505804
  poolId: 5058
  lvThreshold: 8
  groupWeight: 140
  groupWeight: 3500
  groupWeight: 2500
}
rows {
  id: 505805
  poolId: 5058
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 3500
  groupWeight: 1500
}
rows {
  id: 521101
  poolId: 52110
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 521102
  poolId: 52110
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 521103
  poolId: 52110
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521104
  poolId: 52110
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521105
  poolId: 52110
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521106
  poolId: 52110
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521107
  poolId: 52110
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521108
  poolId: 52110
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521109
  poolId: 52110
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521110
  poolId: 52110
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521111
  poolId: 52110
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521112
  poolId: 52110
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521113
  poolId: 52111
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 550301
  poolId: 55030
  lvThreshold: 0
  groupWeight: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 20
}
rows {
  id: 550302
  poolId: 55030
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 50
}
rows {
  id: 550303
  poolId: 55030
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 125
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 875
}
rows {
  id: 550304
  poolId: 55030
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 91
}
rows {
  id: 550305
  poolId: 55030
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 95
}
rows {
  id: 550306
  poolId: 55030
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 96
}
rows {
  id: 550307
  poolId: 55030
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 35
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 965
}
rows {
  id: 550308
  poolId: 55030
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 97
}
rows {
  id: 550309
  poolId: 55030
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 0
  groupWeight: 98
}
rows {
  id: 550310
  poolId: 55030
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 550311
  poolId: 55030
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 550312
  poolId: 55031
  lvThreshold: 120
  groupWeight: 1
  groupWeight: 3399
  groupWeight: 100
  groupWeight: 25500
  groupWeight: 20000
  groupWeight: 15000
  groupWeight: 20000
  groupWeight: 16000
}
rows {
  id: 5308001
  poolId: 5308
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308002
  poolId: 5308
  lvThreshold: 15
  groupWeight: 2
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308003
  poolId: 5308
  lvThreshold: 20
  groupWeight: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308004
  poolId: 5308
  lvThreshold: 25
  groupWeight: 4
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308005
  poolId: 5308
  lvThreshold: 26
  groupWeight: 5
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308006
  poolId: 5308
  lvThreshold: 27
  groupWeight: 10
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308007
  poolId: 5308
  lvThreshold: 28
  groupWeight: 15
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308008
  poolId: 5308
  lvThreshold: 29
  groupWeight: 20
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308009
  poolId: 5308
  lvThreshold: 30
  groupWeight: 25
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308010
  poolId: 5308
  lvThreshold: 31
  groupWeight: 700
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308011
  poolId: 5308
  lvThreshold: 32
  groupWeight: 1000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308012
  poolId: 5308
  lvThreshold: 33
  groupWeight: 2000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308013
  poolId: 5308
  lvThreshold: 34
  groupWeight: 3000
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1150
  groupWeight: 10
}
rows {
  id: 5308014
  poolId: 5308
  lvThreshold: 35
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 5059001
  poolId: 5059
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5059002
  poolId: 5059
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5059003
  poolId: 5059
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5059004
  poolId: 5059
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5059005
  poolId: 5059
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 5060001
  poolId: 5060
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5060002
  poolId: 5060
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5060003
  poolId: 5060
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5060004
  poolId: 5060
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5060005
  poolId: 5060
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 506101
  poolId: 5061
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 506102
  poolId: 5061
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 506103
  poolId: 5061
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 506104
  poolId: 5061
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 506105
  poolId: 5061
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 801101
  poolId: 8011
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 521201
  poolId: 52120
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 521202
  poolId: 52120
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 521203
  poolId: 52120
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521204
  poolId: 52120
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521205
  poolId: 52120
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521206
  poolId: 52120
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521207
  poolId: 52120
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521208
  poolId: 52120
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521209
  poolId: 52120
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521210
  poolId: 52120
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521211
  poolId: 52120
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521212
  poolId: 52120
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521213
  poolId: 52121
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 801201
  poolId: 8012
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 521301
  poolId: 52130
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
}
rows {
  id: 521302
  poolId: 52130
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 521303
  poolId: 52130
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521304
  poolId: 52130
  lvThreshold: 4
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521305
  poolId: 52130
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521306
  poolId: 52130
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521307
  poolId: 52130
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521308
  poolId: 52130
  lvThreshold: 8
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521309
  poolId: 52130
  lvThreshold: 9
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521310
  poolId: 52130
  lvThreshold: 10
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521311
  poolId: 52130
  lvThreshold: 11
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521312
  poolId: 52130
  lvThreshold: 12
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 521313
  poolId: 52131
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 96960001
  poolId: 9696
  lvThreshold: 1
  groupWeight: 100
}
rows {
  id: 801301
  poolId: 8013
  lvThreshold: 100
  groupWeight: 1
  groupWeight: 0
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 6028
  groupWeight: 2471
  groupWeight: 500
}
rows {
  id: 5062001
  poolId: 5062
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5062002
  poolId: 5062
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 5062003
  poolId: 5062
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5062004
  poolId: 5062
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 5062005
  poolId: 5062
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 506301
  poolId: 5063
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 506302
  poolId: 5063
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 506303
  poolId: 5063
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 506304
  poolId: 5063
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 506305
  poolId: 5063
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 506401
  poolId: 5064
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 506402
  poolId: 5064
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 7500
  groupWeight: 3500
}
rows {
  id: 506403
  poolId: 5064
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 6500
  groupWeight: 3500
}
rows {
  id: 506404
  poolId: 5064
  lvThreshold: 8
  groupWeight: 140
  groupWeight: 3500
  groupWeight: 2500
}
rows {
  id: 506405
  poolId: 5064
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 3500
  groupWeight: 1500
}
rows {
  id: 5309001
  poolId: 5309
  lvThreshold: 280
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
