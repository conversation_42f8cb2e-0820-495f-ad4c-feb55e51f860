com.tencent.wea.xlsRes.table_ItemDisplayBoardConfData
excel/xls/D_道具表_玩具.xlsx sheet:玩具配置
rows {
  id: 729000
  itemDisplayBoardActionConf {
    actionIcon: "CDN:Icon_Elegant_001"
    actionRes: "AS_CH_Handhold_Tuiban_POSE_idle_001"
    outPropSkeletalRes: "SK_Handhold_100"
    outPropShowRes: "AS_CH_Handhold_Idle_100_01:hand_r"
    actionRes2: "AS_CH_Handhold_Tuiban_POSE_idle_001"
    actionRes5: "AS_CH_Handhold_Tuiban_POSE_idle_001"
    buttonType: "action"
    buttonIcon: "CDN:Icon_Elegant_001"
    buttonCD: 1.0
  }
  itemDisplayBoardActionConf {
    actionIcon: "CDN:Icon_Emergency_001"
    actionRes: "AS_CH_Handhold_Tuiban_001"
    outPropSkeletalRes: "SK_Handhold_100"
    outPropShowRes: "AS_CH_Handhold_Idleshow_100_01:hand_r"
    actionRes2: "AS_CH_Handhold_Tuiban_001"
    actionRes5: "AS_CH_Handhold_Tuiban_001"
    buttonType: "action"
    buttonIcon: "CDN:Icon_Emergency_001"
    buttonCD: 1.0
  }
  itemDisplayBoardActionConf {
    actionIcon: "CDN:Icon_EatApple_001"
    actionRes: "AS_CH_Handhold_Tuiban_002"
    outPropSkeletalRes: "SK_Handhold_100"
    outPropShowRes: "AS_CH_Handhold_Idleshow_100_01:hand_r"
    actionRes2: "AS_CH_Handhold_Tuiban_002"
    actionRes5: "AS_CH_Handhold_Tuiban_002"
    buttonType: "action"
    buttonIcon: "CDN:Icon_EatApple_001"
    buttonCD: 1.0
  }
  itemDisplayBoardActionConf {
    buttonType: "window"
    buttonIcon: "CDN:T_InGame_Setting"
    buttonText: "设置"
  }
  itemDisplayBoardActionConf {
    buttonType: "unequip"
    buttonIcon: "CDN:T_InGame_Out"
    buttonText: "卸下"
  }
}
rows {
  id: 729001
  itemDisplayBoardActionConf {
    actionIcon: "CDN:Icon_Elegant_001"
    actionRes: "AS_CH_Handhold_DIYtongbao_POSE_idle_001"
    outPropSkeletalRes: "SK_DIYtongbao_001"
    outPropShowRes: "AS_CH_Handhold_Idle_001_01:hand_r"
    actionRes2: "AS_CH_Handhold_DIYtongbao_POSE_idle_001_TF"
    actionRes5: "AS_CH_Handhold_DIYtongbao_POSE_idle_001"
    buttonType: "action"
    buttonIcon: "CDN:Icon_Elegant_001"
    buttonCD: 1.0
  }
  itemDisplayBoardActionConf {
    actionIcon: "CDN:Icon_Emergency_001"
    actionRes: "AS_CH_Handhold_DIYtongbao_001"
    outPropSkeletalRes: "SK_DIYtongbao_001"
    outPropShowRes: "AS_CH_Handhold_Idleshow_001_01:hand_r"
    actionRes2: "AS_CH_Handhold_DIYtongbao_001_TF"
    actionRes5: "AS_CH_Handhold_DIYtongbao_001"
    buttonType: "action"
    buttonIcon: "CDN:Icon_Emergency_001"
    buttonCD: 1.0
  }
  itemDisplayBoardActionConf {
    buttonType: "window"
    buttonIcon: "CDN:T_InGame_Setting"
    buttonText: "设置"
  }
  itemDisplayBoardActionConf {
    buttonType: "unequip"
    buttonIcon: "CDN:T_InGame_Out"
    buttonText: "卸下"
  }
}
