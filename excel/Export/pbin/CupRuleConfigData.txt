com.tencent.wea.xlsRes.table_CupRuleConfig
excel/xls/J_奖杯征程.xlsx sheet:玩法奖励规则
rows {
  id: 1001
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1001
  cupNum: 6
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1002
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1002
  cupNum: 5
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1003
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1003
  cupNum: 5
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1004
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1004
  cupNum: 4
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1005
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1005
  cupNum: 4
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1006
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1006
  cupNum: 4
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1007
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1007
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1008
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1008
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1009
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1009
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1010
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1010
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1011
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1011
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1012
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1012
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1013
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1013
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1014
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1014
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1015
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1015
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1016
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1016
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1017
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1017
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1018
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1018
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1019
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1019
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1020
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1020
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1021
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1021
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1022
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1022
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1023
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1023
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1024
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1024
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1025
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1025
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1026
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1026
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1027
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1027
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1028
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1028
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1029
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1029
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1030
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1030
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1031
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1031
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1032
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1032
  cupNum: 3
  modeGroupList: 4
  modeGroupList: 7
}
rows {
  id: 1033
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1033
  cupNum: 5
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1034
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1034
  cupNum: 4
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1035
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1035
  cupNum: 4
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1036
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1036
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1037
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1037
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1038
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1038
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1039
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1039
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1040
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1040
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1041
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1041
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1042
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1042
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1043
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1043
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1044
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1044
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1045
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1045
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1046
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1046
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1047
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1047
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1048
  opType: MROT_INTERSECTION
  dimList: 1052
  dimList: 1049
  dimList: 1048
  cupNum: 3
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1049
  opType: MROT_INTERSECTION
  dimList: 1050
  cupNum: 0
  modeGroupList: 4
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 7
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1050
  opType: MROT_INTERSECTION
  dimList: 1051
  dimList: 1049
  dimList: 1054
  cupNum: 1
  modeGroupList: 4
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 7
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1051
  opType: MROT_INTERSECTION
  dimList: 1051
  dimList: 1053
  cupNum: 2
  modeGroupList: 4
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 7
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 1052
  opType: MROT_INTERSECTION
  dimList: 1049
  dimList: 1054
  cupNum: 1
  modeGroupList: 4
  modeGroupList: 5
  modeGroupList: 6
  modeGroupList: 7
  modeGroupList: 8
  modeGroupList: 9
}
rows {
  id: 2001
  opType: MROT_INTERSECTION
  dimList: 2019
  cupNum: 0
  modeGroupList: 151
  modeGroupList: 105
  modeGroupList: 106
  modeGroupList: 109
  modeGroupList: 112
  modeGroupList: 113
  modeGroupList: 107
  modeGroupList: 108
  modeGroupList: 110
  modeGroupList: 111
}
rows {
  id: 2002
  opType: MROT_INTERSECTION
  dimList: 2020
  dimList: 2016
  dimList: 2023
  cupNum: 18
  modeGroupList: 151
  modeGroupList: 105
  modeGroupList: 106
  modeGroupList: 109
  modeGroupList: 112
  modeGroupList: 113
  modeGroupList: 107
  modeGroupList: 108
  modeGroupList: 110
  modeGroupList: 111
}
rows {
  id: 2003
  opType: MROT_INTERSECTION
  dimList: 2020
  dimList: 2017
  dimList: 2023
  cupNum: 9
  modeGroupList: 151
  modeGroupList: 105
  modeGroupList: 106
  modeGroupList: 109
  modeGroupList: 112
  modeGroupList: 113
  modeGroupList: 107
  modeGroupList: 108
  modeGroupList: 110
  modeGroupList: 111
}
rows {
  id: 2004
  opType: MROT_INTERSECTION
  dimList: 2021
  dimList: 2016
  dimList: 2023
  cupNum: 14
  modeGroupList: 151
  modeGroupList: 105
  modeGroupList: 106
  modeGroupList: 109
  modeGroupList: 112
  modeGroupList: 113
  modeGroupList: 107
  modeGroupList: 108
  modeGroupList: 110
  modeGroupList: 111
}
rows {
  id: 2005
  opType: MROT_INTERSECTION
  dimList: 2021
  dimList: 2017
  dimList: 2023
  cupNum: 7
  modeGroupList: 151
  modeGroupList: 105
  modeGroupList: 106
  modeGroupList: 109
  modeGroupList: 112
  modeGroupList: 113
  modeGroupList: 107
  modeGroupList: 108
  modeGroupList: 110
  modeGroupList: 111
}
rows {
  id: 2006
  opType: MROT_INTERSECTION
  dimList: 2022
  dimList: 2016
  dimList: 2023
  cupNum: 18
  modeGroupList: 151
  modeGroupList: 105
  modeGroupList: 106
  modeGroupList: 109
  modeGroupList: 112
  modeGroupList: 113
  modeGroupList: 107
  modeGroupList: 108
  modeGroupList: 110
  modeGroupList: 111
}
rows {
  id: 2007
  opType: MROT_INTERSECTION
  dimList: 2022
  dimList: 2017
  dimList: 2023
  cupNum: 9
  modeGroupList: 151
  modeGroupList: 105
  modeGroupList: 106
  modeGroupList: 109
  modeGroupList: 112
  modeGroupList: 113
  modeGroupList: 107
  modeGroupList: 108
  modeGroupList: 110
  modeGroupList: 111
}
rows {
  id: 3001
  opType: MROT_INTERSECTION
  dimList: 3005
  cupNum: 0
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3002
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3006
  cupNum: 7
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3003
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3007
  cupNum: 7
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3004
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3008
  cupNum: 7
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3005
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3009
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3006
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3010
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3007
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3011
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3008
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3012
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3009
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3013
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3010
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3014
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3011
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3015
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3012
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3016
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3013
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3017
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3014
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3018
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3015
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3019
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3016
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3020
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3017
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3021
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3018
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3022
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3019
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3023
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3020
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3024
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3021
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3025
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3022
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3026
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3023
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3027
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3024
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3028
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3025
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3029
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3026
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3030
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3027
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3031
  cupNum: 2
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3028
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3032
  cupNum: 2
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3029
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3033
  cupNum: 2
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3030
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3034
  cupNum: 2
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3031
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3035
  cupNum: 2
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3032
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3036
  cupNum: 1
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3033
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3037
  cupNum: 1
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3034
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3038
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3035
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3039
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3036
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3040
  cupNum: 6
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3037
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3041
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3038
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3042
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3039
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3043
  cupNum: 5
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3040
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3044
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3041
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3045
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3042
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3046
  cupNum: 4
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3043
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3047
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3044
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3048
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3045
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3049
  cupNum: 3
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3046
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3050
  cupNum: 2
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3047
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3051
  cupNum: 2
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3048
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3052
  cupNum: 1
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 3049
  opType: MROT_INTERSECTION
  dimList: 3004
  dimList: 3053
  cupNum: 1
  modeGroupList: 607
  modeGroupList: 608
  modeGroupList: 609
  modeGroupList: 604
  modeGroupList: 605
  modeGroupList: 606
}
rows {
  id: 4001
  opType: MROT_INTERSECTION
  dimList: 4014
  dimList: 4015
  cupNum: 0
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4002
  opType: MROT_INTERSECTION
  dimList: 4014
  dimList: 4016
  cupNum: 0
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4003
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4018
  dimList: 4001
  cupNum: 8
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4004
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4018
  dimList: 4002
  cupNum: 7
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4005
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4018
  dimList: 4003
  cupNum: 6
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4006
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4018
  dimList: 4004
  cupNum: 6
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4007
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4018
  dimList: 4009
  cupNum: 3
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4008
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4018
  dimList: 4010
  cupNum: 3
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4009
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4018
  dimList: 4011
  cupNum: 2
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4010
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4018
  dimList: 4012
  cupNum: 2
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4011
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4001
  cupNum: 7
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4012
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4002
  cupNum: 6
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4013
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4003
  cupNum: 6
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4014
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4004
  cupNum: 5
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4015
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4005
  cupNum: 5
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4016
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4006
  cupNum: 4
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4017
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4007
  cupNum: 4
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4018
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4015
  dimList: 4019
  dimList: 4008
  cupNum: 4
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4019
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4005
  cupNum: 5
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4020
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4006
  cupNum: 5
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4021
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4007
  cupNum: 4
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4022
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4008
  cupNum: 4
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4023
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4009
  cupNum: 3
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4024
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4010
  cupNum: 3
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4025
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4011
  cupNum: 2
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 4026
  opType: MROT_INTERSECTION
  dimList: 4013
  dimList: 4016
  dimList: 4019
  dimList: 4012
  cupNum: 2
  modeGroupList: 141
  modeGroupList: 104
}
rows {
  id: 5001
  opType: MROT_INTERSECTION
  dimList: 5005
  cupNum: 0
  modeGroupList: 501
  modeGroupList: 503
  modeGroupList: 701
  modeGroupList: 703
}
rows {
  id: 5002
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5006
  cupNum: 8
  modeGroupList: 501
  modeGroupList: 701
}
rows {
  id: 5003
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5007
  cupNum: 7
  modeGroupList: 501
  modeGroupList: 701
}
rows {
  id: 5004
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5008
  cupNum: 6
  modeGroupList: 501
  modeGroupList: 701
}
rows {
  id: 5005
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5009
  cupNum: 5
  modeGroupList: 501
  modeGroupList: 701
}
rows {
  id: 5006
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5010
  cupNum: 4
  modeGroupList: 501
  modeGroupList: 701
}
rows {
  id: 5007
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5011
  cupNum: 3
  modeGroupList: 501
  modeGroupList: 701
}
rows {
  id: 5008
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5001
  dimList: 5006
  cupNum: 9
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5009
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5001
  dimList: 5007
  cupNum: 8
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5010
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5001
  dimList: 5008
  cupNum: 7
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5011
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5001
  dimList: 5009
  cupNum: 6
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5012
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5001
  dimList: 5010
  cupNum: 6
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5013
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5002
  dimList: 5006
  cupNum: 6
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5014
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5002
  dimList: 5007
  cupNum: 5
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5015
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5002
  dimList: 5008
  cupNum: 5
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5016
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5002
  dimList: 5009
  cupNum: 4
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5017
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5002
  dimList: 5010
  cupNum: 4
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 5018
  opType: MROT_INTERSECTION
  dimList: 5004
  dimList: 5003
  cupNum: 7
  modeGroupList: 503
  modeGroupList: 703
}
rows {
  id: 6001
  opType: MROT_INTERSECTION
  dimList: 6032
  cupNum: 0
  modeGroupList: 350
  modeGroupList: 351
  modeGroupList: 352
  modeGroupList: 353
  modeGroupList: 354
}
rows {
  id: 6002
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6025
  cupNum: 0
  modeGroupList: 350
  modeGroupList: 351
  modeGroupList: 352
  modeGroupList: 353
  modeGroupList: 354
}
rows {
  id: 6003
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6028
  cupNum: 10
  modeGroupList: 350
  modeGroupList: 351
  modeGroupList: 352
}
rows {
  id: 6004
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6028
  cupNum: 14
  modeGroupList: 353
  modeGroupList: 354
}
rows {
  id: 6005
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6030
  dimList: 6026
  cupNum: 12
  modeGroupList: 350
  modeGroupList: 351
  modeGroupList: 352
}
rows {
  id: 6006
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6030
  dimList: 6027
  cupNum: 8
  modeGroupList: 350
  modeGroupList: 351
  modeGroupList: 352
}
rows {
  id: 6007
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6030
  dimList: 6026
  cupNum: 18
  modeGroupList: 353
  modeGroupList: 354
}
rows {
  id: 6008
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6030
  dimList: 6027
  cupNum: 12
  modeGroupList: 353
  modeGroupList: 354
}
rows {
  id: 6009
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6003
  cupNum: 5
  modeGroupList: 351
}
rows {
  id: 6010
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6004
  cupNum: 4
  modeGroupList: 351
}
rows {
  id: 6011
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6005
  cupNum: 3
  modeGroupList: 351
}
rows {
  id: 6012
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6006
  cupNum: 3
  modeGroupList: 351
}
rows {
  id: 6013
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6007
  cupNum: 2
  modeGroupList: 351
}
rows {
  id: 6014
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6008
  cupNum: 2
  modeGroupList: 351
}
rows {
  id: 6015
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6009
  cupNum: 1
  modeGroupList: 351
}
rows {
  id: 6016
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6010
  cupNum: 0
  modeGroupList: 351
}
rows {
  id: 6017
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6031
  dimList: 6026
  cupNum: 12
  modeGroupList: 350
  modeGroupList: 351
  modeGroupList: 352
}
rows {
  id: 6018
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6031
  dimList: 6027
  cupNum: 8
  modeGroupList: 350
  modeGroupList: 351
  modeGroupList: 352
}
rows {
  id: 6019
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6031
  dimList: 6026
  cupNum: 18
  modeGroupList: 353
  modeGroupList: 354
}
rows {
  id: 6020
  opType: MROT_INTERSECTION
  dimList: 6033
  dimList: 6031
  dimList: 6027
  cupNum: 12
  modeGroupList: 353
  modeGroupList: 354
}
rows {
  id: 6021
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6013
  cupNum: 5
  modeGroupList: 351
}
rows {
  id: 6022
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6014
  cupNum: 4
  modeGroupList: 351
}
rows {
  id: 6023
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6015
  cupNum: 3
  modeGroupList: 351
}
rows {
  id: 6024
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6016
  cupNum: 3
  modeGroupList: 351
}
rows {
  id: 6025
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6017
  cupNum: 2
  modeGroupList: 351
}
rows {
  id: 6026
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6018
  cupNum: 2
  modeGroupList: 351
}
rows {
  id: 6027
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6019
  cupNum: 1
  modeGroupList: 351
}
rows {
  id: 6028
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6020
  cupNum: 0
  modeGroupList: 351
}
rows {
  id: 6029
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6006
  cupNum: 5
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6030
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6007
  cupNum: 3
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6031
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6008
  cupNum: 2
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6032
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6009
  cupNum: 1
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6033
  opType: MROT_INTERSECTION
  dimList: 6030
  dimList: 6010
  cupNum: 0
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6034
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6016
  cupNum: 5
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6035
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6017
  cupNum: 3
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6036
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6018
  cupNum: 2
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6037
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6019
  cupNum: 1
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 6038
  opType: MROT_INTERSECTION
  dimList: 6031
  dimList: 6020
  cupNum: 0
  modeGroupList: 350
  modeGroupList: 352
}
rows {
  id: 7001
  opType: MROT_INTERSECTION
  dimList: 7001
  cupNum: 0
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7002
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7003
  cupNum: 18
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7003
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7004
  cupNum: 18
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7004
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7005
  cupNum: 18
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7005
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7006
  cupNum: 14
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7006
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7007
  cupNum: 14
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7007
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7008
  cupNum: 14
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7008
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7009
  cupNum: 10
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7009
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7010
  cupNum: 10
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7010
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7011
  cupNum: 10
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7011
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7012
  cupNum: 7
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7012
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7013
  cupNum: 7
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 7013
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7014
  cupNum: 7
  modeGroupList: 5600
  modeGroupList: 5601
}
rows {
  id: 8001
  opType: MROT_INTERSECTION
  dimList: 8001
  cupNum: 0
  modeGroupList: 6006
}
rows {
  id: 8002
  opType: MROT_INTERSECTION
  dimList: 8002
  dimList: 8004
  cupNum: 7
  modeGroupList: 6006
}
rows {
  id: 8003
  opType: MROT_INTERSECTION
  dimList: 8002
  dimList: 8005
  cupNum: 6
  modeGroupList: 6006
}
rows {
  id: 8004
  opType: MROT_INTERSECTION
  dimList: 8002
  dimList: 8006
  cupNum: 5
  modeGroupList: 6006
}
rows {
  id: 8005
  opType: MROT_INTERSECTION
  dimList: 8002
  dimList: 8007
  cupNum: 4
  modeGroupList: 6006
}
rows {
  id: 8006
  opType: MROT_INTERSECTION
  dimList: 8002
  dimList: 8008
  cupNum: 3
  modeGroupList: 6006
}
rows {
  id: 8007
  opType: MROT_INTERSECTION
  dimList: 8009
  cupNum: 1
  modeGroupList: 6006
}
rows {
  id: 8008
  opType: MROT_INTERSECTION
  dimList: 8010
  cupNum: 2
  modeGroupList: 6006
}
rows {
  id: 8009
  opType: MROT_INTERSECTION
  dimList: 8011
  cupNum: 1
  modeGroupList: 6006
}
rows {
  id: 9001
  opType: MROT_INTERSECTION
  dimList: 7001
  cupNum: 0
  modeGroupList: 6101
  modeGroupList: 6102
}
rows {
  id: 9002
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 9001
  cupNum: 18
  modeGroupList: 6101
  modeGroupList: 6102
}
rows {
  id: 9003
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 9002
  cupNum: 9
  modeGroupList: 6101
  modeGroupList: 6102
}
rows {
  id: 10001
  opType: MROT_INTERSECTION
  dimList: 7001
  cupNum: 0
  modeGroupList: 5700
}
rows {
  id: 10002
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7003
  cupNum: 10
  modeGroupList: 5700
}
rows {
  id: 10003
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7004
  cupNum: 10
  modeGroupList: 5700
}
rows {
  id: 10004
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7005
  cupNum: 10
  modeGroupList: 5700
}
rows {
  id: 10005
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7006
  cupNum: 5
  modeGroupList: 5700
}
rows {
  id: 10006
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7007
  cupNum: 5
  modeGroupList: 5700
}
rows {
  id: 10007
  opType: MROT_INTERSECTION
  dimList: 7002
  dimList: 7008
  cupNum: 5
  modeGroupList: 5700
}
rows {
  id: 11001
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10004
  dimList: 10005
  dimList: 10011
  cupNum: 10
  modeGroupList: 380
}
rows {
  id: 11002
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10004
  dimList: 10005
  dimList: 10012
  cupNum: 12
  modeGroupList: 380
}
rows {
  id: 11003
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10004
  dimList: 10007
  dimList: 10011
  cupNum: 12
  modeGroupList: 380
}
rows {
  id: 11004
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10004
  dimList: 10007
  dimList: 10012
  cupNum: 16
  modeGroupList: 380
}
rows {
  id: 11005
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10004
  dimList: 10009
  dimList: 10011
  cupNum: 14
  modeGroupList: 380
}
rows {
  id: 11006
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10004
  dimList: 10009
  dimList: 10012
  cupNum: 18
  modeGroupList: 380
}
rows {
  id: 11007
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10004
  dimList: 10006
  dimList: 10011
  cupNum: 16
  modeGroupList: 380
}
rows {
  id: 11008
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10004
  dimList: 10006
  dimList: 10012
  cupNum: 20
  modeGroupList: 380
}
rows {
  id: 11009
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10004
  dimList: 10008
  dimList: 10011
  cupNum: 20
  modeGroupList: 380
}
rows {
  id: 11010
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10004
  dimList: 10008
  dimList: 10012
  cupNum: 26
  modeGroupList: 380
}
rows {
  id: 11011
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10004
  dimList: 10010
  dimList: 10011
  cupNum: 24
  modeGroupList: 380
}
rows {
  id: 11012
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10004
  dimList: 10010
  dimList: 10012
  cupNum: 30
  modeGroupList: 380
}
rows {
  id: 11013
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10013
  dimList: 10014
  dimList: 10016
  cupNum: 12
  modeGroupList: 381
}
rows {
  id: 11014
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10013
  dimList: 10014
  dimList: 10017
  cupNum: 16
  modeGroupList: 381
}
rows {
  id: 11015
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10013
  dimList: 10015
  dimList: 10016
  cupNum: 20
  modeGroupList: 381
}
rows {
  id: 11016
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10013
  dimList: 10015
  dimList: 10017
  cupNum: 24
  modeGroupList: 381
}
rows {
  id: 11017
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10013
  dimList: 10014
  dimList: 10018
  cupNum: 12
  modeGroupList: 381
}
rows {
  id: 11018
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10013
  dimList: 10014
  dimList: 10019
  cupNum: 16
  modeGroupList: 381
}
rows {
  id: 11019
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10013
  dimList: 10015
  dimList: 10018
  cupNum: 20
  modeGroupList: 381
}
rows {
  id: 11020
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10013
  dimList: 10015
  dimList: 10019
  cupNum: 24
  modeGroupList: 381
}
rows {
  id: 11021
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10020
  dimList: 10021
  dimList: 10023
  cupNum: 14
  modeGroupList: 382
}
rows {
  id: 11022
  opType: MROT_INTERSECTION
  dimList: 10003
  dimList: 10020
  dimList: 10021
  dimList: 10024
  cupNum: 18
  modeGroupList: 382
}
rows {
  id: 11023
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10020
  dimList: 10022
  dimList: 10023
  cupNum: 24
  modeGroupList: 382
}
rows {
  id: 11024
  opType: MROT_INTERSECTION
  dimList: 10002
  dimList: 10020
  dimList: 10022
  dimList: 10024
  cupNum: 30
  modeGroupList: 382
}
rows {
  id: 11025
  opType: MROT_INTERSECTION
  dimList: 10026
  cupNum: 0
  modeGroupList: 380
  modeGroupList: 381
  modeGroupList: 382
}
rows {
  id: 12001
  opType: MROT_INTERSECTION
  dimList: 11001
  cupNum: 0
  modeGroupList: 6300
}
rows {
  id: 12002
  opType: MROT_INTERSECTION
  dimList: 11002
  dimList: 11004
  cupNum: 7
  modeGroupList: 6300
}
rows {
  id: 12003
  opType: MROT_INTERSECTION
  dimList: 11002
  dimList: 11005
  cupNum: 6
  modeGroupList: 6300
}
rows {
  id: 12004
  opType: MROT_INTERSECTION
  dimList: 11002
  dimList: 11006
  cupNum: 5
  modeGroupList: 6300
}
rows {
  id: 12005
  opType: MROT_INTERSECTION
  dimList: 11002
  dimList: 11007
  cupNum: 4
  modeGroupList: 6300
}
rows {
  id: 12006
  opType: MROT_INTERSECTION
  dimList: 11002
  dimList: 11008
  cupNum: 3
  modeGroupList: 6300
}
rows {
  id: 12007
  opType: MROT_INTERSECTION
  dimList: 11009
  cupNum: 1
  modeGroupList: 6300
}
rows {
  id: 12008
  opType: MROT_INTERSECTION
  dimList: 11010
  cupNum: 2
  modeGroupList: 6300
}
rows {
  id: 12009
  opType: MROT_INTERSECTION
  dimList: 11011
  cupNum: 1
  modeGroupList: 6300
}
