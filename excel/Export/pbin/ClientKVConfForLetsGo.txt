com.tencent.wea.xlsRes.table_ClientKVConf
excel/xls/Z_杂项配置.xlsx sheet:客户端KV表-LetsGo
rows {
  key: "MaxFps"
  value: "30"
}
rows {
  key: "TotalAcceptClient"
  value: "0"
}
rows {
  key: "NetPackedMovementMaxBits"
  value: "4096"
}
rows {
  key: "DisablePlayerGuide"
  value: "0"
}
rows {
  key: "EarlyAccessVersion"
  value: "1"
}
rows {
  key: "EarlyAccessVersionDesc"
  value: "本次测试为删档测试，祝你体验愉快！"
}
rows {
  key: "EnableDebug"
  value: "1"
}
rows {
  key: "EnableLogBody"
  value: "1"
}
rows {
  key: "EnableLogOpen"
  value: "1"
}
rows {
  key: "LogUploader.SecretId"
  value: "AKIDBD3e1C9xwgFhx8M1Uow0ub4HEhvtaHl3"
}
rows {
  key: "LogUploader.SecretKey"
  value: "72ymSzJRJvuhPt9G9mAKG1b0EKSZhhYE"
}
rows {
  key: "LogUploader.WechatRobotKey"
  value: "0a849571-9c99-41f0-937e-ff90cd7a3b2f"
}
rows {
  key: "LogUploader.ContentBoundary"
  value: "$$Con&#%tentBou&#%ndary$$"
}
rows {
  key: "SuitTaskGroupID"
  value: "1800"
}
rows {
  key: "ChatBubbleDelayTime"
  value: "3"
}
rows {
  key: "ChatBubbleVisibleDistance"
  value: "50"
}
rows {
  key: "PrivacyContactDesc"
  value: "请通过<a style=\"LoginRule\" mark=\"url\">https://kf.qq.com/</>与我们联系。您也可以登录腾讯隐私保护平台<a style=\"LoginRule\" mark=\"url\">https://privacy.qq.com</>获取更多信息。"
}
rows {
  key: "MonthCardRewardDesc"
  value: "30 日累计可获得<Orange2_0> 30 </>个星愿币 + <Orange2_0> 300 </>通行证经验"
}
rows {
  key: "QQPlatPrivilegeUrl"
  value: "https://www.qq.com"
}
rows {
  key: "BlessBagRewardList"
  value: "620044,1;400440,1;6,10;320012,1"
}
rows {
  key: "LowMobileDesc"
  value: "手机版本太低"
}
rows {
  key: "LowMobileGotoUrl"
  value: "https://www.qq.com"
}
rows {
  key: "GrabCountLimit"
  value: "0"
}
rows {
  key: "SeasonRechargeAmountMoneyRechargeLock"
  value: "0"
}
rows {
  key: "NoticeShowCount"
  value: "4"
}
rows {
  key: "SafetyForceRebirth"
  value: "0"
}
rows {
  key: "ForceRebirthLimit"
  value: "100"
}
rows {
  key: "PasswordExchangeUrl"
  value: "https://ymzx.qq.com/cp/a20231118passdh/index.html"
}
rows {
  key: "PasswordExchangeUrl_PC"
  value: "https://ymzx.qq.com/cp/a20231118passdh/index_pc.html"
}
rows {
  key: "CreditScore"
  value: "350"
}
rows {
  key: "rechargeNavigationExpireTime"
  value: "2"
}
rows {
  key: "passTime2StartFallBehind"
  value: "23"
}
rows {
  key: "DDPPhysicsSettings->bSubstepping"
  value: "1"
}
rows {
  key: "DDPPhysicsSettings->MaxSubsteps"
  value: "30"
}
rows {
  key: "DDPPhysicsSettings->MaxSubstepDeltaTime"
  value: "0.016"
}
rows {
  key: "EnableNegativeScaleHack"
  value: "1"
}
rows {
  key: "MatchRewardActivityId"
  value: "103"
}
rows {
  key: "LobbyCustomerLink"
  value: "https://kf.qq.com/touch/sy/prod/A10969/v2/index.html?scene_id=CSCE20230709093205rXzxikln"
}
rows {
  key: "IsNicknamePrecheckEnabled"
  value: "1"
}
rows {
  key: "CommitCreateRoleRequestLimit"
  value: "2.05"
}
rows {
  key: "TipsOnCreateRoleTimeout"
  value: "当前游戏注册过于火爆，请星宝再次尝试哦！"
}
rows {
  key: "CreateRoleTimeoutInterval"
  value: "3"
}
rows {
  key: "InteractiveEntranceCD"
  value: "2000"
}
rows {
  key: "MoeMovementNoCollision"
  value: "3"
}
rows {
  key: "LotteryGuideRaffleIdConfig"
  value: "{[\'2025-03-28 00:00:00\'] = 10011, [\'2025-05-16 00:00:00\'] = 10012}"
}
rows {
  key: "TencentMapSyncLocationTime"
  value: "120"
}
rows {
  key: "PlayerRedEnvelopeItemId"
  value: "725902;725903;7259045;725905;725906;725907;725908"
}
rows {
  key: "NearPlayerRequestTimeout"
  value: "5"
}
rows {
  key: "ModeSelectHomeRecNum"
  value: "3"
}
rows {
  key: "TeamInviteHintMaxFriendShowNum"
  value: "6"
}
rows {
  key: "TeamInviteHintMaxOtherShowNum"
  value: "1"
}
rows {
  key: "TeamInviteHintSingleShowTime"
  value: "10"
}
rows {
  key: "SendPacketOpenAndroid"
  value: "1"
}
rows {
  key: "SendPacketOpenIOS"
  value: "0"
}
rows {
  key: "SendPacketName"
  value: "发红包"
}
rows {
  key: "SendPacketWhiteServerList"
}
rows {
  key: "SendPacketBlackServerList"
  value: "letsgo-dev-test3;letsgo-dev-test4"
}
rows {
  key: "SendPacketWhiteOpenIds"
}
rows {
  key: "SendPacketBlackOpenIds"
  value: "000003;000004"
}
rows {
  key: "SendPacketQQJumpUrl"
  value: "https://pay.qq.com/h5/active/ingame_jump.php?offerId=1450156516&pf=__mds_fromapp_ymzxhongbao_march&url=https%3A%2F%2Fpay.qq.com%2Fs%2Fone%3Fs%3D1NhK1G2Ajc8%26pf%3D__mds_fromapp_ymzxhongbao_march%26_wwv%3D4"
}
rows {
  key: "SendPacketWXJumpUrl"
  value: "https://pay.qq.com/h5/active/ingame_jump.php?offerId=1450156516&pf=__mds_fromapp_ymzxhongbao_march&url=https%3A%2F%2Fpay.qq.com%2Fs%2Fone%3Fs%3D1NhK1G2Ajc8%26pf%3D__mds_fromapp_ymzxhongbao_march"
}
rows {
  key: "SendPacketShowTimeList"
  value: "1712764800-2028297600"
}
rows {
  key: "SendPacketReddotShowTimeList"
  value: "1712764800-2028297600"
}
rows {
  key: "AtLobbyTimeForCheckOnlienFriend"
  value: "3600"
}
rows {
  key: "BackLobbyTimeForCheckOnlienFriend"
  value: "3600"
}
rows {
  key: "VoiceHintCd"
  value: "3600"
}
rows {
  key: "VoiceHintShowTime"
  value: "3"
}
rows {
  key: "RequestOpenSpeakerCd"
  value: "15"
}
rows {
  key: "RequestOpenMicroCd"
  value: "15"
}
rows {
  key: "TeamOpenSpeakerHintCd"
  value: "1200"
}
rows {
  key: "TeamOpenMicroHintCd"
  value: "1200"
}
rows {
  key: "TeamMemberSpeakingHintMicroNeedTime"
  value: "600"
}
rows {
  key: "KartingFallBehindConfig"
  value: "1;100;0.0005;0.2;10"
}
rows {
  key: "exclusiveVehicleNumLimit"
  value: "5"
}
rows {
  key: "PhotoAlbumMaxNum"
  value: "100"
}
rows {
  key: "PerfDogOpenProbabilityInClient"
  value: "0.02"
}
rows {
  key: "EnableLobbyCharacterPool"
  value: "1"
}
rows {
  key: "EnableSilenceArea"
  value: "1"
}
rows {
  key: "SuperCorePlayerId"
  value: "10000"
}
rows {
  key: "MLUserWebsiteUrl"
  value: "https://act.flow.qq.com/mid/directional-flow/m0c2dyhdm0l789/index.html"
}
rows {
  key: "PerfSightOpenProbabilityInClient"
  value: "0.02"
}
rows {
  key: "CrashSightOpenProbabilityInClient"
  value: "1"
}
rows {
  key: "TeamWantToPlayCD"
  value: "10"
}
rows {
  key: "RecruitListUpdateCD"
  value: "10"
}
rows {
  key: "RecruitHallUpdateCD"
  value: "10"
}
rows {
  key: "AllowWantToPlayBubbleMaxCount"
  value: "3"
}
rows {
  key: "WantToPlayMsgTime"
  value: "5"
}
rows {
  key: "WXSubscribeUrl"
  value: "https://game.weixin.qq.com/cgi-bin/comm/openlink?noticeid=90308972&auth_appid=wx62d9035fd4fd2059&url=https%3A%2F%2Fgame.weixin.qq.com%2Fcgi-bin%2Fh5%2Fstatic%2Flivereservation%2Freserve.html%3Ffinder_username%3Dv2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918%40finder%26scene%3D129910%26appid%3Dwx692970c60bffaaa1%23wechat_redirect"
}
rows {
  key: "BanSnapshotInGameSceneGameTypes"
  value: "999;9999;999999"
}
rows {
  key: "SnapshotInGameSceneGameOpen"
  value: "1"
}
rows {
  key: "SnapshotInFarmSceneGameOpen"
  value: "1"
}
rows {
  key: "SnapshotInHomeSceneGameOpen"
  value: "1"
}
rows {
  key: "SnapshotInCommunitySceneGameOpen"
  value: "1"
}
rows {
  key: "SnapshotInSubLobbySceneGameOpen"
  value: "1"
}
rows {
  key: "SnapshotInUGCPlaySceneGameOpen"
  value: "1"
}
rows {
  key: "SnapshotOpen"
  value: "1"
}
rows {
  key: "haoyoubeizhustart"
  value: "1717344000"
}
rows {
  key: "haoyoubeizhuend"
  value: "1717603200"
}
rows {
  key: "wolfInteractionItemWaitTime"
  value: "5"
}
rows {
  key: "defaultTeamPreviewShowIcon"
  value: "T_Lobby_lmg_TeamBtn"
}
rows {
  key: "defaultTeamPreviewShowName"
  value: "队伍"
}
rows {
  key: "ScenePropMaxNumConfig"
  value: "20"
}
rows {
  key: "QQVideoProtectForgetTime"
  value: "12"
}
rows {
  key: "CloudEnvCacheMatchIdCount"
  value: "2"
}
rows {
  key: "MallUIPreloadType"
  value: "2"
}
rows {
  key: "NormalUIPreloadType"
  value: "2"
}
rows {
  key: "DressAssetPreloadType"
  value: "2"
}
rows {
  key: "CloudEnvEnableAudioToastText"
  value: "游戏语音全面开放，可以在谁是狼人、农场等场景中和朋友聊天。"
}
rows {
  key: "CloudEnvEnableAudioToastDuration"
  value: "3"
}
rows {
  key: "CloudEnvEnableAudioToastNum"
  value: "5"
}
rows {
  key: "SportsmanHelpUrl"
  value: "https://ymzx.qq.com/cp/a20240708sport/index.html"
}
rows {
  key: "QQVideo_Open"
  value: "0"
}
rows {
  key: "QQVideo_Open_CloudEnv_WX_Pad"
  value: "0"
}
rows {
  key: "QQVideo_Open_CloudEnv_QQ_Pad"
  value: "0"
}
rows {
  key: "QQVideo_Open_CloudEnv_WX_Phone"
  value: "0"
}
rows {
  key: "QQVideo_Open_CloudEnv_QQ_Phone"
  value: "0"
}
rows {
  key: "QQVideo_Open_Android_Pad"
  value: "0"
}
rows {
  key: "QQVideo_Open_IOS_Pad"
  value: "0"
}
rows {
  key: "QQVideo_Open_IOS_Phone"
  value: "0"
}
rows {
  key: "QQVideo_Open_Android_Phone"
  value: "0"
}
rows {
  key: "SportsmanRadarChatBaseScore"
  value: "15"
}
rows {
  key: "VADownloadTaskId"
  value: "7030"
}
rows {
  key: "Key_MineSweeperBobbleDuration"
  value: "3"
}
rows {
  key: "Key_MineSweeperBobbleInterval"
  value: "10"
}
rows {
  key: "LikeHistoryShowCount"
  value: "30"
}
rows {
  key: "FriendBotName"
  value: "小红狐"
}
rows {
  key: "FriendBotHeadIcon"
  value: "T_Friend_Img_Twinkie"
}
rows {
  key: "IntimacyTextRange"
  value: "1;90"
}
rows {
  key: "AIntimacyText"
  value: "快去建立亲密关系,获得专属奖励吧"
}
rows {
  key: "BIntimacyText"
  value: "已经可以建立亲密关系,快去绑定吧"
}
rows {
  key: "OrangeSuitTrailRadius"
  value: "450"
}
rows {
  key: "OrangeSuitTrailSpeed"
  value: "150"
}
rows {
  key: "ChangeMatchIdMaxTimes"
  value: "3"
}
rows {
  key: "QQAuthorizationLevelUpLevel"
  value: "3"
}
rows {
  key: "QQAuthorizationEnterFarmLevel"
  value: "3"
}
rows {
  key: "IdleShowPlayDistanceSqr"
  value: "25000000"
}
rows {
  key: "MaxGoldenIdleShowNum"
  value: "1"
}
rows {
  key: "SceneMaxPlaceableActorNumCfg"
  value: "30"
}
rows {
  key: "EnableInLevelShuttleSpawn"
  value: "1"
}
rows {
  key: "MaxFriendGuideShowCount"
  value: "2"
}
rows {
  key: "MaxFriendGuideShowLevel"
  value: "6"
}
rows {
  key: "NewMallShowHotMallCount"
  value: "3"
}
rows {
  key: "NewMallHotTopShowTimeInterval"
  value: "10"
}
rows {
  key: "Horital_MenuViewIndex"
  value: "1"
}
rows {
  key: "Vertical_MoreViewIndex"
  value: "1"
}
rows {
  key: "ColorItemTimerValue"
  value: "5"
}
rows {
  key: "GoFishingSharePicUrl"
  value: "https://image-manage.ymzx.qq.com/wuji/client/materials/yutangwuqifenxiang.astc"
}
rows {
  key: "SimpleModelBanListId"
  value: "3000|4000"
}
rows {
  key: "NewPlayerModelLevelLimit"
  value: "5"
}
rows {
  key: "CanShowGiftCardItemTypes"
  value: "305:201:202:203:204:205:206:207:209:306"
}
rows {
  key: "UseOldVersionGiftCard"
  value: "0"
}
rows {
  key: "PlayerRedEnvelopeItemIdList"
  value: "725908;725902;725903;725904;725905;725906;725907"
}
rows {
  key: "ConanJumpID"
  value: "1068"
}
rows {
  key: "ConanSuitID"
  value: "404350"
}
rows {
  key: "FinalAccountCupMaxCount"
  value: "10000"
}
rows {
  key: "FinalAccountCupMinCount"
  value: "1000"
}
rows {
  key: "ConanActivityID"
  value: "5046"
}
rows {
  key: "MainLobbyGrabStrangerActionLimit"
  value: "-1"
}
rows {
  key: "MainLobbyGrabStrangerPeopleLimit"
  value: "-1"
}
rows {
  key: "UGCLobbyGrabStrangerActionLimit"
  value: "-1"
}
rows {
  key: "UGCLobbyGrabStrangerPeopleLimit"
  value: "-1"
}
rows {
  key: "totalTopMessageCountInOneDay"
  value: "5"
}
rows {
  key: "totalSinglePlayerForReturnRecommendCount"
  value: "3"
}
rows {
  key: "intervalForTopMessageShow"
  value: "10000"
}
rows {
  key: "newPlayerTagTime"
  value: "********"
}
rows {
  key: "ExclusiveVehicleWeightPrevLimit"
  value: "100"
}
rows {
  key: "ExclusiveVehicleWeightPostLimit"
  value: "120"
}
rows {
  key: "GrabPredetectionAdditionalRadius"
  value: "48"
}
rows {
  key: "SeasonTimeInterval"
  value: "5"
}
rows {
  key: "WishListMaxNum"
  value: "10"
}
rows {
  key: "CupRewardTipsShowFarmLimit"
  value: "10"
}
rows {
  key: "CupRewardTipsShowTime"
  value: "3"
}
rows {
  key: "WishBlessingCharMaxNum"
  value: "60"
}
rows {
  key: "CustomRoomHideAvatar"
  value: "0"
}
rows {
  key: "SoloTrainDanceSwitcher"
  value: "1"
}
rows {
  key: "FollowNotifyMaxCount"
  value: "10"
}
rows {
  key: "vaCollectErrorTime"
  value: "0.3"
}
rows {
  key: "StartGameInterval"
  value: "4"
}
rows {
  key: "ChaseFeedBackOption"
  value: "建议与想法;问题与Bug;其他"
}
rows {
  key: "CloudEnvRecordGameCount"
  value: "1"
}
rows {
  key: "LobbyNoDisturbBubbleTime"
  value: "3"
}
rows {
  key: "listLobbyNavigationRecommendWeightMap"
  value: "1:4;2:4;3:1;4:0#1:2;2:1;3:1;4:1#1:3;2:1;3:1;4:1#1:999;2:1;3:1;4:1#1:1;2:1;3:1;4:1"
}
rows {
  key: "lobbyNavigationAvoidDuplicationCount"
  value: "10"
}
rows {
  key: "ShowWXGameAwardMinLevel"
  value: "5"
}
rows {
  key: "LobbyNoDisturbPassiveBubbleDailyCount"
  value: "2"
}
rows {
  key: "ModelSelectRecentPlayGetCount"
  value: "12"
}
rows {
  key: "CanInterrupInteractMotionList"
}
rows {
  key: "CantInterruptInteractActionList"
}
rows {
  key: "SoloActionMoveNoInterruptHintCount"
  value: "3"
}
rows {
  key: "ModelSelectMoveOutOffset"
  value: "75"
}
rows {
  key: "ModelSelectMoveOutHandleTime"
  value: "0"
}
rows {
  key: "ModelShowScrollTopTipsLv"
  value: "15"
}
rows {
  key: "ModelShowScrollTopTipsTimes"
  value: "1"
}
rows {
  key: "OpenHandHoldControlPadInLobby"
  value: "1"
}
rows {
  key: "NewSelectedPromotionMatchIdTestA"
  value: "4"
}
rows {
  key: "NewSelectedPromotionMatchIdTestB"
  value: "6"
}
rows {
  key: "NewSelectedSceneABTestDefaultMatchId"
  value: "4"
}
rows {
  key: "UGCMapCollectionHorTabName"
  value: "地图合集"
}
rows {
  key: "PCWebTransferTipPopStartTime"
  value: "600"
}
rows {
  key: "SummerFlashingActivityId"
  value: "5084"
}
rows {
  key: "FlashMobBlackBoardRefreshTime"
  value: "60"
}
