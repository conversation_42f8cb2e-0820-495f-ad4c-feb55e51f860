com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/K_卡牌.xlsx sheet:文本表_卡牌
rows {
  content: "赠送卡牌"
  switch: 1
  stringId: "Card_Give_Title"
}
rows {
  content: "卡牌已被赠送"
  switch: 1
  stringId: "Card_Chat_Request_Finish_Base"
}
rows {
  content: "卡牌已被领取"
  switch: 1
  stringId: "Card_Chat_Give_Finish_Base"
}
rows {
  content: "卡牌已被交换"
  switch: 1
  stringId: "Card_Chat_Exchange_Finish_Base"
}
rows {
  content: "卡牌获取途径"
  switch: 1
  stringId: "Card_Main_GetCard_ButtonName"
}
rows {
  content: "卡牌获取"
  switch: 1
  stringId: "Card_GetWayTips_Title"
}
rows {
  content: "获取途径"
  switch: 1
  stringId: "Card_GetWayTips_des"
}
rows {
  content: "前往"
  switch: 1
  stringId: "Card_GetWayTips_Get_ButtonName"
}
rows {
  content: "({0}平台发出)"
  switch: 1
  stringId: "Card_Record_Plat_Des"
}
rows {
  content: "微信"
  switch: 1
  stringId: "Card_Record_Plat_WX"
}
rows {
  content: "QQ"
  switch: 1
  stringId: "Card_Record_Plat_QQ"
}
rows {
  content: "获得奖杯"
  switch: 1
  stringId: "Card_GetWayTips_Cup_Title"
}
rows {
  content: "星宝们在【每日奖杯挑战】中获得一定奖杯后，可获取随机卡包！"
  switch: 1
  stringId: "Card_GetWayTips_Cup_Content"
}
rows {
  content: "知道了"
  switch: 1
  stringId: "Card_GetWayTips_Cup_SureButton_Name"
}
rows {
  content: "可兑换:{0}"
  switch: 1
  stringId: "Card_Exchange_Limit"
}
rows {
  content: "金色卡牌限时置换"
  switch: 1
  stringId: "Card_Activity_Bubble"
}
rows {
  content: "重复卡牌额外兑换"
  switch: 1
  stringId: "Card_Exchange_Bubble"
}
rows {
  content: "卡牌限时增量20%"
  switch: 1
  stringId: "Card_Bounds_Bubble"
}
rows {
  content: "万能卡待使用！"
  switch: 1
  stringId: "Card_WildCard_Bubble"
}
rows {
  content: "金卡置换中"
  switch: 1
  stringId: "Card_Activity_Bubble_1"
}
rows {
  content: "可兑换奖励"
  switch: 1
  stringId: "Card_Exchange_Bubble_1"
}
rows {
  content: "对方将会选择一张回赠卡牌"
  switch: 1
  stringId: "Card_Exchange_HitText"
}
rows {
  content: "请选择一张回赠卡牌"
  switch: 1
  stringId: "Card_Exchange_Be_HitText"
}
rows {
  content: "兑换星愿币"
  switch: 1
  stringId: "Card_Main_Shop_Name"
}
rows {
  content: "卡牌星级"
  switch: 1
  stringId: "SelfUmg_Card_Tips_1"
}
rows {
  content: "{0}卡册"
  switch: 1
  stringId: "SelfUmg_Card_Tips_2"
}
rows {
  content: "叮～福利星送来2张随机卡牌，继续加油收集哦！"
  switch: 1
  stringId: "Card_Welfare_Tips_1"
}
rows {
  content: "请用横屏体验此功能"
  switch: 1
  stringId: "Card_Vertical_Tip"
}
rows {
  content: "开启后，主界面可接收卡牌相关索要/交换的侧边弹窗信息；关闭后，则不再接收对应弹窗信息。"
  switch: 1
  stringId: "Card_PrivateSetting_Tip"
}
rows {
  content: "亲爱的星宝，检测到有新卡册更新，请升级游戏版本号后体验！"
  switch: 1
  stringId: "Card_Version_Tip_Main"
}
rows {
  content: "亲爱的星宝，新卡册未更新，请升级游戏版本号后体验相关内容！"
  switch: 1
  stringId: "Card_Version_Tip_Chat_Control"
}
rows {
  content: "因版本过低无法查看新获得的卡册内容，请升级游戏版本号后完整体验！"
  switch: 1
  stringId: "Card_Version_Tip_CardGet"
}
rows {
  content: "当前资源正在排队下载中...（当前进度{0}%）"
  switch: 1
  stringId: "Card_download_Tip"
}
rows {
  content: "我向{0}发起的卡牌赠送请求已过期"
  switch: 1
  stringId: "Card_History_Give_Normal_TimeOut"
}
rows {
  content: "我正在向{0}送出卡牌\n<FZLT20Red>({1}后过期)</>"
  switch: 1
  stringId: "Card_History_Give_Normal_Angain"
}
rows {
  content: "我正在向{0}索要卡牌\n<FZLT20Red>({1}后过期)</>"
  switch: 1
  stringId: "Card_History_Request_Normal_Angain"
}
rows {
  content: "<FeatureInLevelCard>{0}</>"
  switch: 1
  stringId: "Card_CrownView_CardBagName"
}
rows {
  content: "确定"
  switch: 1
  stringId: "Card_CloudToAPP_Title"
}
rows {
  content: "前往下载"
  switch: 1
  stringId: "Card_CloudToAPP_Sure"
}
rows {
  content: "取消"
  switch: 1
  stringId: "Card_CloudToAPP_Cancle"
}
rows {
  content: "iOS小游戏不支持该功能，请前往APP使用"
  switch: 1
  stringId: "Card_CloudToAPP_Main"
}
rows {
  content: "收集时间：{0}"
  switch: 1
  stringId: "Card_ActivityTime"
}
rows {
  content: "全部卡册"
  switch: 1
  stringId: "Card_Main_CardSetHistory_Button_Name"
}
rows {
  content: "本期卡册"
  switch: 1
  stringId: "Card_Main_CardSetHistory_CurrentSet"
}
rows {
  content: "往期卡册"
  switch: 1
  stringId: "Card_Main_CardSetHistory_HistorySet"
}
rows {
  content: "全卡册收集进度"
  switch: 1
  stringId: "Card_Main_CardSetHistory_HistorySet_Des"
}
rows {
  content: "卡片未获得"
  switch: 1
  stringId: "Card_Preview_History_NoCard"
}
rows {
  content: "前往卡册查看"
  switch: 1
  stringId: "Card_GetCard_GoButtonName"
}
rows {
  content: "确定消耗<Red28>{0}</>张重复卡牌进行兑换吗？"
  switch: 1
  stringId: "Card_Exchange_SecondTips_1"
}
rows {
  content: "（本次兑换消耗的卡牌中<Red28>包含金色卡牌</>，请谨慎兑换！）"
  switch: 1
  stringId: "Card_Exchange_SecondTips_2"
}
rows {
  content: "自动选择消耗的卡牌"
  switch: 1
  stringId: "Card_Exchange_AutoSelect"
}
rows {
  content: "未拥有我给出的卡牌"
  switch: 1
  stringId: "Card_Exchange_Limit_FriendNoCard"
}
rows {
  content: "拥有指定卡牌"
  switch: 1
  stringId: "Card_Exchange_Limit_FriendHasCard"
}
rows {
  content: "确定用<FZLT22Orange>{0}</>向<FZLT22Orange>{1}</>交换<FZLT22Orange>{2}</>吗?"
  switch: 1
  stringId: "Card_Exchange_Limit_Confirm_Content"
}
rows {
  content: "当前卡牌不能交换"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_NotCloudExchange"
}
rows {
  content: "您的卡牌数量不足"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_NotEnoughCount"
}
rows {
  content: "已赠送交换{0}/{1}"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_LimitCount"
}
rows {
  content: "我给出的"
  switch: 1
  stringId: "Card_Exchange_Limit_Give"
}
rows {
  content: "我想得到"
  switch: 1
  stringId: "Card_Exchange_Limit_Want"
}
rows {
  content: "更换指定卡牌"
  switch: 1
  stringId: "Card_Exchange_Limit_ButtonChange"
}
rows {
  content: "选择指定卡牌"
  switch: 1
  stringId: "Card_Exchange_Limit_ButtonChoose"
}
rows {
  content: "请与我交换指定卡牌"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_Content"
}
rows {
  content: "我给出的"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_MyGive"
}
rows {
  content: "我想得到"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_MyWant"
}
rows {
  content: "对方给出"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_OtherGive"
}
rows {
  content: "对方想要"
  switch: 1
  stringId: "Card_Exchange_Limit_Chat_OtherWant"
}
rows {
  content: "指定交换"
  switch: 1
  stringId: "Card_Exchange_Limit_Confirm_Title"
}
rows {
  content: "推荐"
  switch: 1
  stringId: "Card_Exchange_Limit_Choose_Recommend"
}
rows {
  content: "请求与你交换指定卡牌"
  switch: 1
  stringId: "Card_Exchange_Limit_Invite_Des"
}
rows {
  content: "对方给出"
  switch: 1
  stringId: "Card_Exchange_Limit_Invite_MyGive"
}
rows {
  content: "对方想要"
  switch: 1
  stringId: "Card_Exchange_Limit_Invite_MyWant"
}
rows {
  content: "只能选择{0}张卡牌"
  switch: 1
  stringId: "Card_Exchange_Limit_Max_SelectCard"
}
rows {
  content: "当前尚未选择指定交换的卡牌，确认是否保持任意卡牌交换？"
  switch: 1
  stringId: "Card_Exchange_Limit_NoSelectContent"
}
rows {
  content: "对方需要使用您指定的卡牌进行回赠"
  switch: 1
  stringId: "Card_Exchange_Limit_SelectDes"
}
rows {
  content: "选择指定卡牌交换"
  switch: 1
  stringId: "Card_Exchange_Limit_SelectTitle"
}
rows {
  content: "我在元梦之星卡牌集换中集齐了{0}\n全卡册，大奖到手！星宝们快来一起收集吧！"
  switch: 1
  stringId: "Card_ShareInPic_CardSet"
}
rows {
  content: "我在元梦之星卡牌集换中集齐了{0}\n卡册，奖励到手！星宝们快来一起收集吧！"
  switch: 1
  stringId: "Card_ShareInPic_CardDeck"
}
rows {
  content: "当前尚未选中任何卡牌，无法点击赠送"
  switch: 1
  stringId: "Card_Give_Button_NoCardSelect"
}
rows {
  content: "卡牌集换"
  switch: 1
  stringId: "LFT_CardTrade"
}
rows {
  content: "当前重复卡牌的累计星数"
  switch: 1
  stringId: "Card_Exchange_StarTips_1"
}
rows {
  content: "当前重复卡牌的累计星数："
  switch: 1
  stringId: "Card_Exchange_StarTips_2"
}
rows {
  content: "所有品质卡包概率掉落同品质"
  switch: 1
  stringId: "UI_Card_IP_Main_Content_Title"
}
rows {
  content: "夏日联动卡包"
  switch: 1
  stringId: "UI_Card_IP_Main_Content"
}
rows {
  content: "所有品质卡包概率掉落同品质夏日联动卡包"
  switch: 1
  stringId: "UI_Card_IP_Tips_Content"
}
rows {
  content: "新的卡集火热收集中"
  switch: 1
  stringId: "Card_CardOpen_Bubble"
}
