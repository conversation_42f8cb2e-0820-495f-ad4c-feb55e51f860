com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_主表.xlsx sheet:活动-配饰卡池
rows {
  raffleId: 5000
  name: "新年起飞"
  startTime {
    seconds: 1704024000
  }
  endTime {
    seconds: 1707407999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5000
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 30
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得守护之翼"
  lowestVersion: "0.6.1196.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1704024000
  }
  showEndTime {
    seconds: 1707407999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5202
  name: "摩天乐园"
  startTime {
    seconds: 1704988800
  }
  endTime {
    seconds: 1706803199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5202
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "每次祈愿不会获得重复奖励，9次必得Toby时装"
  lowestVersion: "0.6.1196.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1704988800
  }
  showEndTime {
    seconds: 1706803199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5001
  name: "奶龙祈愿"
  startTime {
    seconds: 1706889600
  }
  endTime {
    seconds: 1709222399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5001
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 89
  text: "每次祈愿不会获得重复奖励，9次必得奶龙时装"
  lowestVersion: "1.2.67.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1706889600
  }
  showEndTime {
    seconds: 1709222399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5002
  name: "绮梦灯"
  startTime {
    seconds: 1707494400
  }
  endTime {
    seconds: 1713455999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5002
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 112
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得绮梦灯"
  lowestVersion: "1.2.80.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  previewTag: "Camera_Ornament2"
  showStartTime {
    seconds: 1707494400
  }
  showEndTime {
    seconds: 1713455999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5003
  name: "奥特曼"
  startTime {
    seconds: 1706803200
  }
  endTime {
    seconds: 1711295999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5003
    subPoolIds: 5003
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 103
  text: "奖励不会重复获得，12次内必得迪迦奥特曼"
  lowestVersion: "1.2.67.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "迪迦"
  raffleTagIcon: "T_TheOtmankaPool_Img_Tiga"
  showStartTime {
    seconds: 1706803200
  }
  showEndTime {
    seconds: 1711295999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5004
  name: "奥特曼"
  startTime {
    seconds: 1708617600
  }
  endTime {
    seconds: 1711295999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5004
    subPoolIds: 5004
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 103
  text: "奖励不会重复获得，12次内必得赛罗奥特曼"
  lowestVersion: "1.2.67.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "赛罗"
  raffleTagIcon: "T_TheOtmankaPool_Img_Cerro"
  showStartTime {
    seconds: 1708617600
  }
  showEndTime {
    seconds: 1711295999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5005
  name: "奥特曼"
  startTime {
    seconds: 1708617600
  }
  endTime {
    seconds: 1711295999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5005
    subPoolIds: 5005
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 103
  text: "奖励不会重复获得，12次内必得泽塔奥特曼"
  lowestVersion: "1.2.67.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "泽塔"
  raffleTagIcon: "T_TheOtmankaPool_Img_Zeta"
  showStartTime {
    seconds: 1708617600
  }
  showEndTime {
    seconds: 1711295999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5010
  name: "冰雪玫瑰"
  startTime {
    seconds: 1708704000
  }
  endTime {
    seconds: 1710086399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5010
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 114
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得玫瑰之翼"
  lowestVersion: "1.2.80.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1708704000
  }
  showEndTime {
    seconds: 1710086399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5011
  name: "摩天乐园"
  startTime {
    seconds: 1709827200
  }
  endTime {
    seconds: 1710431999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5011
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "每次祈愿不会获得重复奖励，9次必得Toby时装"
  lowestVersion: "0.6.1196.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1709827200
  }
  showEndTime {
    seconds: 1710431999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5012
  name: "幸运祈愿"
  startTime {
    seconds: 1709827200
  }
  endTime {
    seconds: 1711641599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5012
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 123
  text: "每次祈愿不会获得重复奖励，9次必得欧米时装"
  lowestVersion: "0.6.1196.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1709827200
  }
  showEndTime {
    seconds: 1711641599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5014
  name: "饰品祈愿"
  startTime {
    seconds: 1713542400
  }
  endTime {
    seconds: 1719503999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5014
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 200
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得暗夜冰羽"
  lowestVersion: "1.3.7.75"
  raffleTagName: "暗夜冰羽"
  raffleTagIcon: "T_Pooling_Img_BlueHead"
  previewTag: "Camera_Ornament3"
  showStartTime {
    seconds: 1713542400
  }
  showEndTime {
    seconds: 1719503999
  }
  isShow: true
  combinePreviewIcon: "T_Pooling_Img_Wings"
  combinePreviewRewards: 5014001
  combinePreviewRewards: 5014002
  combinePreviewRewards: 5014003
  combineClaim: "暗夜冰羽"
  juniorItemAnimType: 1
}
rows {
  raffleId: 5016
  name: "星愿之旅"
  startTime {
    seconds: 1711641600
  }
  endTime {
    seconds: 1713455999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5016
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 127
  text: "每次祈愿不会获得重复奖励，12次必得小橘子时装"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1711641600
  }
  showEndTime {
    seconds: 1713455999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5017
  name: "蔬菜精灵"
  startTime {
    seconds: 1712851200
  }
  endTime {
    seconds: 1715529599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5017
    subPoolIds: 5017
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 135
  text: "奖励不会重复获得，9次内必得白菜狗"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "白菜狗"
  raffleTagIcon: "T_VegetableSub_Img_Avatar_Caigou"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  keyItemCount: 1
  ccdKey: 94
  showStartTime {
    seconds: 1712851200
  }
  showEndTime {
    seconds: 1715529599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5018
  name: "蔬菜精灵"
  startTime {
    seconds: 1712851200
  }
  endTime {
    seconds: 1715529599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5018
    subPoolIds: 5018
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 136
  text: "奖励不会重复获得，9次内必得莲藕狐"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "莲藕狐"
  raffleTagIcon: "T_VegetableSub_Img_Avatar_Huli"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  keyItemCount: 1
  ccdKey: 94
  showStartTime {
    seconds: 1712851200
  }
  showEndTime {
    seconds: 1715529599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5019
  name: "向日葵小班"
  startTime {
    seconds: 1713456000
  }
  endTime {
    seconds: 1715875199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5019
    subPoolIds: 5019
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 137
  text: "奖励不会重复获得，12次内必得风间"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "风间"
  raffleTagIcon: "T_XiaoXinka_Img_FengjianHead"
  jumpIds: 25
  commodityIds: 120023
  commodityIds: 120024
  commodityIds: 120025
  viewIndex: 4
  viewIndex: 6
  viewIndex: 9
  viewIndex: 2
  viewIndex: 5
  viewIndex: 12
  viewIndex: 1
  viewIndex: 7
  viewIndex: 8
  viewIndex: 3
  viewIndex: 11
  viewIndex: 10
  keyItemCount: 3
  ccdKey: 82
  showStartTime {
    seconds: 1713456000
  }
  showEndTime {
    seconds: 1715875199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5020
  name: "向日葵小班"
  startTime {
    seconds: 1713456000
  }
  endTime {
    seconds: 1715875199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5020
    subPoolIds: 5020
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 138
  text: "奖励不会重复获得，12次内必得妮妮"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "妮妮"
  raffleTagIcon: "T_XiaoXinka_Img_NiniHead"
  jumpIds: 25
  commodityIds: 120023
  commodityIds: 120024
  commodityIds: 120025
  viewIndex: 4
  viewIndex: 6
  viewIndex: 9
  viewIndex: 2
  viewIndex: 5
  viewIndex: 12
  viewIndex: 1
  viewIndex: 7
  viewIndex: 8
  viewIndex: 3
  viewIndex: 11
  viewIndex: 10
  keyItemCount: 3
  ccdKey: 82
  showStartTime {
    seconds: 1713456000
  }
  showEndTime {
    seconds: 1715875199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5021
  name: "向日葵小班"
  startTime {
    seconds: 1713456000
  }
  endTime {
    seconds: 1715875199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5021
    subPoolIds: 5021
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 139
  text: "奖励不会重复获得，12次内必得阿呆"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "阿呆"
  raffleTagIcon: "T_XiaoXinka_Img_AdaiHead"
  jumpIds: 25
  commodityIds: 120023
  commodityIds: 120024
  commodityIds: 120025
  viewIndex: 4
  viewIndex: 6
  viewIndex: 9
  viewIndex: 2
  viewIndex: 5
  viewIndex: 12
  viewIndex: 1
  viewIndex: 7
  viewIndex: 8
  viewIndex: 3
  viewIndex: 11
  viewIndex: 10
  keyItemCount: 3
  ccdKey: 82
  showStartTime {
    seconds: 1713456000
  }
  showEndTime {
    seconds: 1715875199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5023
  name: "饰品祈愿"
  startTime {
    seconds: 1716566400
  }
  endTime {
    seconds: 1798732799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5023
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 200
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得星梦使者"
  lowestVersion: "1.3.7.75"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "星梦使者"
  raffleTagIcon: "T_Pooling_Img_WhiteHead"
  previewTag: "Camera_Ornament4"
  showStartTime {
    seconds: 1716566400
  }
  showEndTime {
    seconds: 1798732799
  }
  isShow: true
  combinePreviewIcon: "T_Pooling_Img_Wings02"
  combinePreviewRewards: 5023001
  combinePreviewRewards: 5023002
  combinePreviewRewards: 5023003
  combineClaim: "星梦使者"
  juniorItemAnimType: 1
}
rows {
  raffleId: 5025
  name: "双生曼舞"
  startTime {
    seconds: 1718380800
  }
  endTime {
    seconds: 1720367999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5025
    subPoolIds: 5025
  }
  dailyLimit: 60
  maxLimit: 60
  textRuleId: 149
  text: "累计祈愿<SwanTipsYellow> 60 </>次必得所有奖励"
  lowestVersion: "*******"
  jumpIds: 25
  commodityIds: 120049
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  keyItemCount: 4
  ccdKey: 95
  showStartTime {
    seconds: 1718380800
  }
  showEndTime {
    seconds: 1720367999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5026
  name: "前线装备库"
  startTime {
    seconds: 1716480000
  }
  endTime {
    seconds: 1717689599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5026
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 89
  text: "每次祈愿不会获得重复奖励，9次必得 非凡时装"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1716480000
  }
  showEndTime {
    seconds: 1717689599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5027
  name: "铁臂阿童木"
  startTime {
    seconds: 1717171200
  }
  endTime {
    seconds: 1718899199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5027
    subPoolIds: 5027
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 152
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得阿童木"
  lowestVersion: "1.2.100.1"
  raffleTagName: "阿童木"
  raffleTagIcon: "T_AstroBoy_Img_HeadAstro"
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 2
  viewIndex: 4
  viewIndex: 3
  viewIndex: 9
  viewIndex: 1
  keyItemCount: 1
  showStartTime {
    seconds: 1717171200
  }
  showEndTime {
    seconds: 1718899199
  }
  isShow: true
  juniorItemAnimType: 2
}
rows {
  raffleId: 5028
  name: "铁臂阿童木"
  startTime {
    seconds: 1717171200
  }
  endTime {
    seconds: 1718899199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5028
    subPoolIds: 5028
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 152
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得小兰"
  lowestVersion: "1.2.100.1"
  raffleTagName: "小兰"
  raffleTagIcon: "T_AstroBoy_Img_HeadLan"
  viewIndex: 8
  viewIndex: 7
  viewIndex: 6
  viewIndex: 5
  viewIndex: 2
  viewIndex: 4
  viewIndex: 3
  viewIndex: 9
  viewIndex: 1
  keyItemCount: 1
  showStartTime {
    seconds: 1717171200
  }
  showEndTime {
    seconds: 1718899199
  }
  isShow: true
  juniorItemAnimType: 2
}
rows {
  raffleId: 5029
  name: "小肥柴"
  startTime {
    seconds: 1719590400
  }
  endTime {
    seconds: 1720713599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5029
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 154
  text: "<ShibaInuYellow>9</>次祈愿内必得<ShibaInuYellow>小肥柴</>"
  lowestVersion: "1.3.7.76"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  keyItemCount: 1
  showStartTime {
    seconds: 1719590400
  }
  showEndTime {
    seconds: 1720713599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5031
  name: "时光小船"
  startTime {
    seconds: 1718294400
  }
  endTime {
    seconds: 1720108799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5031
    subPoolIds: 5031
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 155
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得布朗熊"
  lowestVersion: "*******"
  raffleTagName: "布朗熊"
  raffleTagIcon: "T_Line_Icon_Avatar1"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  keyItemCount: 1
  showStartTime {
    seconds: 1718294400
  }
  showEndTime {
    seconds: 1720108799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5032
  name: "时光小船"
  startTime {
    seconds: 1718294400
  }
  endTime {
    seconds: 1720108799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5032
    subPoolIds: 5032
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 155
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得可妮兔"
  lowestVersion: "*******"
  raffleTagName: "可妮兔"
  raffleTagIcon: "T_Line_Icon_Avatar2"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  keyItemCount: 1
  showStartTime {
    seconds: 1718294400
  }
  showEndTime {
    seconds: 1720108799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5033
  name: "天鹅之心"
  startTime {
    seconds: 1718640000
  }
  endTime {
    seconds: 1720367999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5033
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 156
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得天鹅之心"
  lowestVersion: "*******"
  jumpIds: 25
  commodityIds: 120049
  previewTag: "Camera_Ornament5"
  showStartTime {
    seconds: 1718640000
  }
  showEndTime {
    seconds: 1720367999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5041
  name: "白鹤少年"
  startTime {
    seconds: 1764777600
  }
  endTime {
    seconds: 1765382399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5041
    subPoolIds: 5041
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 103
  text: "奖励不会重复获得，12次内必得云鹤仙"
  lowestVersion: "1.2.67.1"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 6
  viewIndex: 9
  viewIndex: 2
  viewIndex: 5
  viewIndex: 12
  viewIndex: 1
  viewIndex: 7
  viewIndex: 8
  viewIndex: 3
  viewIndex: 11
  viewIndex: 10
  showStartTime {
    seconds: 1764777600
  }
  showEndTime {
    seconds: 1765382399
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5034
  name: "星梭祈愿"
  startTime {
    seconds: 1719244800
  }
  endTime {
    seconds: 1720540799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5034
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 157
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得星梭-巡星者"
  lowestVersion: "1.3.7.53"
  previewTag: "Camera_Ornament6"
  showStartTime {
    seconds: 1719244800
  }
  showEndTime {
    seconds: 1720540799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5035
  name: "饰品祈愿"
  startTime {
    seconds: 1719504000
  }
  endTime {
    seconds: 1798732799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5035
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 200
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得守护圣翼"
  lowestVersion: "1.3.7.75"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "守护圣翼"
  raffleTagIcon: "T_Pooling_Img_KnightHead"
  previewTag: "Camera_Ornament7"
  showStartTime {
    seconds: 1719504000
  }
  showEndTime {
    seconds: 1798732799
  }
  isShow: true
  combinePreviewIcon: "T_Pooling_Img_Wings03"
  combinePreviewRewards: 5035001
  combinePreviewRewards: 5035002
  combinePreviewRewards: 5035003
  raffleOpenTag: 0
  combineClaim: "守护圣翼"
  juniorItemAnimType: 1
}
rows {
  raffleId: 5036
  name: "奶龙祈愿"
  startTime {
    seconds: 1720454400
  }
  endTime {
    seconds: 1721923199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5036
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 89
  text: "每次祈愿不会获得重复奖励，9次必得奶龙时装"
  lowestVersion: "1.3.7.95"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1720454400
  }
  showEndTime {
    seconds: 1721923199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5037
  name: "终极挑战"
  startTime {
    seconds: 1718294400
  }
  endTime {
    seconds: 1720108799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5037
    subPoolIds: 5037
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 155
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得可妮兔"
  lowestVersion: "*******"
  raffleTagName: "糖豆骑士"
  raffleTagIcon: "T_FallGuys_Img_Avatar_01"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  keyItemCount: 1
  showStartTime {
    seconds: 1718294400
  }
  showEndTime {
    seconds: 1720108799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5038
  name: "终极挑战"
  startTime {
    seconds: 1718294400
  }
  endTime {
    seconds: 1720108799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5038
    subPoolIds: 5038
  }
  dailyLimit: 12
  maxLimit: 12
  textRuleId: 155
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得可妮兔"
  lowestVersion: "*******"
  raffleTagName: "薯宝"
  raffleTagIcon: "T_FallGuys_Img_Avatar_02"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  keyItemCount: 1
  showStartTime {
    seconds: 1718294400
  }
  showEndTime {
    seconds: 1720108799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5039
  name: "摩天乐园"
  startTime {
    seconds: 1718121600
  }
  endTime {
    seconds: 1720195199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5039
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "每次祈愿不会获得重复奖励，9次必得齐天大圣"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1718121600
  }
  showEndTime {
    seconds: 1720195199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5049
  name: "甜兔仙踪"
  startTime {
    seconds: 1722009600
  }
  endTime {
    seconds: 1723823999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5049
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 205
  text: "奖励不会重复获得，9次内必得甜兔屋"
  lowestVersion: "1.3.12.52"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1722009600
  }
  showEndTime {
    seconds: 1723823999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5043
  name: "夏日派对"
  startTime {
    seconds: 1722528000
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5043
    subPoolIds: 5043
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "每次祈愿不会获得重复奖励，9次必得草莓味Toby"
  lowestVersion: "1.3.12.70"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "草莓味TOBY"
  raffleTagIcon: "T_Toby_Icon_Tab1"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1722528000
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5044
  name: "夏日派对"
  startTime {
    seconds: 1722528000
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5044
    subPoolIds: 5044
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "每次祈愿不会获得重复奖励，9次必得小青瓜"
  lowestVersion: "1.3.12.70"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "小青瓜"
  raffleTagIcon: "T_Toby_Icon_Tab2"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1722528000
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5051
  name: "摩天乐园"
  startTime {
    seconds: 1722528000
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5051
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "每次祈愿不会获得重复奖励，9次必得Toby"
  lowestVersion: "1.3.12.70"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1722528000
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5053
  name: "狼人魔法屋"
  startTime {
    seconds: 1723824000
  }
  endTime {
    seconds: 1726415999
  }
  poolSelection {
    policy: RPP_OnGrandFreeRest
    poolId: 5053
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 210
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>9</>次必得舞台报告动画"
  lowestVersion: "1.3.12.116"
  raffleTagName: "舞台剧"
  raffleTagIcon: "T_WerewolfTanabata_Icon_AwardPlateNormal"
  viewIndex: 7
  viewIndex: 8
  viewIndex: 5
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  viewIndex: 9
  showStartTime {
    seconds: 1723824000
  }
  showEndTime {
    seconds: 1726415999
  }
  isShow: true
  showRule: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 5054
  name: "狼人魔法屋"
  startTime {
    seconds: 1723824000
  }
  endTime {
    seconds: 1726415999
  }
  poolSelection {
    policy: RPP_OnGrandFreeRest
    poolId: 5054
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 210
  text: "奖励不会重复获得，<WerewolfTanabataYellow2>9</>次必得变蛙魔法攻击动画"
  lowestVersion: "1.3.12.116"
  raffleTagName: "变蛙魔法"
  raffleTagIcon: "T_WerewolfTanabata_Icon_AwardPlateSelect"
  viewIndex: 9
  viewIndex: 7
  viewIndex: 5
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  viewIndex: 8
  showStartTime {
    seconds: 1723824000
  }
  showEndTime {
    seconds: 1726415999
  }
  isShow: true
  showRule: 1
  juniorItemAnimType: 1
}
rows {
  raffleId: 5055
  name: "守护之翼"
  startTime {
    seconds: 1730131200
  }
  endTime {
    seconds: 1731859199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5055
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 30
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得守护之翼"
  lowestVersion: "1.3.26.33"
  activityImage: "ModSuit_0025_HalfBody.png"
  previewTag: "Camera_Ornament"
  showStartTime {
    seconds: 1730131200
  }
  showEndTime {
    seconds: 1731859199
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5056
  name: "电竞少女"
  startTime {
    seconds: 1744646400
  }
  endTime {
    seconds: 1746115199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5056
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10004
  text: "每次祈愿不会获得重复奖励，9次必得电竞少女时装"
  lowestVersion: "1.3.78.90"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1744646400
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  juniorItemAnimType: 2
}
rows {
  raffleId: 5057
  name: "摩登剧场"
  startTime {
    seconds: 1767369600
  }
  endTime {
    seconds: 1772294399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5057
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10004
  text: "每次祈愿不会获得重复奖励，9次必得夜莺之声时装"
  lowestVersion: "1.3.37.40"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 6
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 1
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  showStartTime {
    seconds: 1767369600
  }
  showEndTime {
    seconds: 1772294399
  }
  isShow: true
  juniorItemAnimType: 2
}
rows {
  raffleId: 5058
  name: "孔雀之翼"
  startTime {
    seconds: 1680796800
  }
  endTime {
    seconds: 1700236799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5058
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 30
  text: "每次祈愿不会获得重复奖励，9次祈愿内必得孔雀之翼"
  lowestVersion: "1.3.78.81"
  activityImage: "ModSuit_0025_HalfBody.png"
  previewTag: "Camera_Ornament"
  showStartTime {
    seconds: 1680796800
  }
  showEndTime {
    seconds: 1700236799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5059
  name: "夏日派对"
  startTime {
    seconds: 1748016000
  }
  endTime {
    seconds: 1749743999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5059
    subPoolIds: 5059
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得草莓味Toby"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "草莓味TOBY"
  raffleTagIcon: "T_Toby_Icon_Tab1"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5060
  name: "夏日派对"
  startTime {
    seconds: 1748016000
  }
  endTime {
    seconds: 1749743999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5060
    subPoolIds: 5060
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得小青瓜"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "小青瓜"
  raffleTagIcon: "T_Toby_Icon_Tab2"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5061
  name: "摩天乐园"
  startTime {
    seconds: 1748016000
  }
  endTime {
    seconds: 1749743999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5061
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得Toby"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5062
  name: "夏日派对"
  startTime {
    seconds: 1749657600
  }
  endTime {
    seconds: 1753977599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5062
    subPoolIds: 5062
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得鲨鱼猫"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "鲨鱼猫"
  raffleTagIcon: "T_Toby_Icon_Tab1"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1749657600
  }
  showEndTime {
    seconds: 1753977599
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 5063
  name: "夏日派对"
  startTime {
    seconds: 1749657600
  }
  endTime {
    seconds: 1753977599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5063
    subPoolIds: 5063
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得草莓猫"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "草莓猫"
  raffleTagIcon: "T_Toby_Icon_Tab2"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1749657600
  }
  showEndTime {
    seconds: 1753977599
  }
  isShow: true
  juniorItemAnimType: 1
}
