com.tencent.wea.xlsRes.table_TaskGroup
excel/xls/B_BP通行证_主玩法.xlsx sheet:任务组
rows {
  id: 13900
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1737043200
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1737043200
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85001
  taskIdList: 85002
  groupName: "天天晋级赛bp每日固定任务（S10）"
}
rows {
  id: 13901
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1737043200
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1737043200
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85003
  taskIdList: 85004
  taskIdList: 85005
  groupName: "天天晋级赛bp每日随机任务（S10）"
}
rows {
  id: 13902
  type: TaskType_BP
  beginShowTime {
    seconds: 1737043200
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1737043200
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85006
  taskIdList: 85007
  taskIdList: 85008
  taskIdList: 85009
  groupName: "天天晋级赛bp第一周任务（S10）"
}
rows {
  id: 13903
  type: TaskType_BP
  beginShowTime {
    seconds: 1737648000
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1737648000
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85010
  taskIdList: 85011
  taskIdList: 85012
  taskIdList: 85013
  groupName: "天天晋级赛bp第二周任务（S10）"
}
rows {
  id: 13904
  type: TaskType_BP
  beginShowTime {
    seconds: 1738252800
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1738252800
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85014
  taskIdList: 85015
  taskIdList: 85016
  taskIdList: 85017
  groupName: "天天晋级赛bp第三周任务（S10）"
}
rows {
  id: 13905
  type: TaskType_BP
  beginShowTime {
    seconds: 1738857600
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1738857600
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85018
  taskIdList: 85019
  taskIdList: 85020
  taskIdList: 85021
  groupName: "天天晋级赛bp第四周任务（S10）"
}
rows {
  id: 13906
  type: TaskType_BP
  beginShowTime {
    seconds: 1739462400
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1739462400
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85022
  taskIdList: 85023
  taskIdList: 85024
  taskIdList: 85025
  groupName: "天天晋级赛bp第五周任务（S10）"
}
rows {
  id: 13907
  type: TaskType_BP
  beginShowTime {
    seconds: 1740067200
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1740067200
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85026
  taskIdList: 85027
  taskIdList: 85028
  taskIdList: 85029
  groupName: "天天晋级赛bp第六周任务（S10）"
}
rows {
  id: 13908
  type: TaskType_BP
  beginShowTime {
    seconds: 1740672000
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1740672000
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85030
  taskIdList: 85031
  taskIdList: 85032
  taskIdList: 85033
  groupName: "天天晋级赛bp第七周任务（S10）"
}
rows {
  id: 13909
  type: TaskType_BP
  beginShowTime {
    seconds: 1741276800
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1741276800
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 85034
  taskIdList: 85035
  taskIdList: 85036
  taskIdList: 85037
  groupName: "天天晋级赛bp第八周任务（S10）"
}
rows {
  id: 13910
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1741881600
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1741881600
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85038
  taskIdList: 85039
  groupName: "天天晋级赛bp每日固定任务（S11）"
}
rows {
  id: 13911
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1741881600
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1741881600
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85040
  taskIdList: 85041
  taskIdList: 85042
  groupName: "天天晋级赛bp每日随机任务（S11）"
}
rows {
  id: 13912
  type: TaskType_BP
  beginShowTime {
    seconds: 1741881600
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1741881600
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85043
  taskIdList: 85044
  taskIdList: 85045
  taskIdList: 85046
  groupName: "天天晋级赛bp第一周任务（S11）"
}
rows {
  id: 13913
  type: TaskType_BP
  beginShowTime {
    seconds: 1742486400
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1742486400
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85047
  taskIdList: 85048
  taskIdList: 85049
  groupName: "天天晋级赛bp第二周任务（S11）"
}
rows {
  id: 13914
  type: TaskType_BP
  beginShowTime {
    seconds: 1743091200
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1743091200
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85051
  taskIdList: 85052
  taskIdList: 85053
  groupName: "天天晋级赛bp第三周任务（S11）"
}
rows {
  id: 13915
  type: TaskType_BP
  beginShowTime {
    seconds: 1743696000
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1743696000
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85055
  taskIdList: 85056
  taskIdList: 85057
  groupName: "天天晋级赛bp第四周任务（S11）"
}
rows {
  id: 13916
  type: TaskType_BP
  beginShowTime {
    seconds: 1744300800
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1744300800
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85059
  taskIdList: 85060
  taskIdList: 85061
  groupName: "天天晋级赛bp第五周任务（S11）"
}
rows {
  id: 13917
  type: TaskType_BP
  beginShowTime {
    seconds: 1744905600
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1744905600
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85063
  taskIdList: 85064
  taskIdList: 85065
  groupName: "天天晋级赛bp第六周任务（S11）"
}
rows {
  id: 13918
  type: TaskType_BP
  beginShowTime {
    seconds: 1745510400
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1745510400
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 85067
  taskIdList: 85068
  taskIdList: 85069
  groupName: "天天晋级赛bp第七周任务（S11）"
}
rows {
  id: 12000
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1746115200
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1746115200
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85070
  taskIdList: 85071
  groupName: "天天晋级赛bp每日固定任务（S12）"
}
rows {
  id: 12001
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1746115200
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1746115200
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85072
  taskIdList: 85073
  taskIdList: 85074
  groupName: "天天晋级赛bp每日随机任务（S12）"
}
rows {
  id: 12002
  type: TaskType_BP
  beginShowTime {
    seconds: 1746115200
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1746115200
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85075
  taskIdList: 85076
  taskIdList: 85077
  taskIdList: 85078
  taskIdList: 85079
  groupName: "天天晋级赛bp第一周任务（S12）"
}
rows {
  id: 12003
  type: TaskType_BP
  beginShowTime {
    seconds: 1746720000
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1746720000
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85080
  taskIdList: 85081
  taskIdList: 85082
  groupName: "天天晋级赛bp第二周任务（S12）"
}
rows {
  id: 12004
  type: TaskType_BP
  beginShowTime {
    seconds: 1747324800
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1747324800
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85083
  taskIdList: 85084
  taskIdList: 85085
  groupName: "天天晋级赛bp第三周任务（S12）"
}
rows {
  id: 12005
  type: TaskType_BP
  beginShowTime {
    seconds: 1747929600
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1747929600
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85086
  taskIdList: 85087
  taskIdList: 85088
  groupName: "天天晋级赛bp第四周任务（S12）"
}
rows {
  id: 12006
  type: TaskType_BP
  beginShowTime {
    seconds: 1748534400
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1748534400
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85089
  taskIdList: 85090
  taskIdList: 85091
  groupName: "天天晋级赛bp第五周任务（S12）"
}
rows {
  id: 12007
  type: TaskType_BP
  beginShowTime {
    seconds: 1749139200
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1749139200
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85092
  taskIdList: 85093
  taskIdList: 85094
  groupName: "天天晋级赛bp第六周任务（S12）"
}
rows {
  id: 12008
  type: TaskType_BP
  beginShowTime {
    seconds: 1749744000
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1749744000
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85095
  taskIdList: 85096
  taskIdList: 85097
  groupName: "天天晋级赛bp第七周任务（S12）"
}
rows {
  id: 12009
  type: TaskType_BP
  beginShowTime {
    seconds: 1750348800
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1750348800
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 85098
  taskIdList: 85099
  taskIdList: 85100
  groupName: "天天晋级赛bp第八周任务（S12）"
}
rows {
  id: 12010
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85101
  taskIdList: 85102
  groupName: "天天晋级赛bp每日固定任务（S13）"
}
rows {
  id: 12011
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85103
  taskIdList: 85104
  taskIdList: 85105
  groupName: "天天晋级赛bp每日随机任务（S13）"
}
rows {
  id: 12012
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85106
  taskIdList: 85107
  taskIdList: 85108
  taskIdList: 85109
  taskIdList: 85110
  groupName: "天天晋级赛bp第一周任务（S13）"
}
rows {
  id: 12013
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85111
  taskIdList: 85112
  taskIdList: 85113
  groupName: "天天晋级赛bp第二周任务（S13）"
}
rows {
  id: 12014
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85114
  taskIdList: 85115
  taskIdList: 85116
  groupName: "天天晋级赛bp第三周任务（S13）"
}
rows {
  id: 12015
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85117
  taskIdList: 85118
  taskIdList: 85119
  groupName: "天天晋级赛bp第四周任务（S13）"
}
rows {
  id: 12016
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85120
  taskIdList: 85121
  taskIdList: 85122
  groupName: "天天晋级赛bp第五周任务（S13）"
}
rows {
  id: 12017
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85123
  taskIdList: 85124
  taskIdList: 85125
  groupName: "天天晋级赛bp第六周任务（S13）"
}
rows {
  id: 12018
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85126
  taskIdList: 85127
  taskIdList: 85128
  groupName: "天天晋级赛bp第七周任务（S13）"
}
rows {
  id: 12019
  type: TaskType_BP
  beginShowTime {
    seconds: 1750953600
  }
  endShowTime {
    seconds: 1755791999
  }
  beginDoTime {
    seconds: 1750953600
  }
  endDoTime {
    seconds: 1755791999
  }
  taskIdList: 85129
  taskIdList: 85130
  taskIdList: 85131
  groupName: "天天晋级赛bp第八周任务（S13）"
}
