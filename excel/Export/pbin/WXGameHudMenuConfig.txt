com.tencent.wea.xlsRes.table_WXGameHudMenuConfig
excel/xls/X_小游戏切片_主界面.xlsx sheet:主界面菜单
rows {
  id: 1
  buttonName: "首充"
  buttonIcon: "T_Lobby_Img_Recharge"
  jumpId: 50001
  sortId: 1
  showArea: 0
}
rows {
  id: 2
  buttonName: "热购"
  buttonIcon: "T_Lobby_Bg_RecahrgeBtn"
  jumpId: 50002
  sortId: 2
  showArea: 1
  redDotType: 38
  isLockForMatch: 1
  buttonGreyIcon: "T_Lobby_Bg_GreyRecahrgeBtn"
}
rows {
  id: 3
  buttonName: "祈愿"
  buttonIcon: "T_Lobby_Bg_WishBtn"
  jumpId: 50003
  sortId: 4
  showArea: 1
  redDotType: 30
  isLockForMatch: 1
  buttonGreyIcon: "T_Lobby_Bg_GreyWishBtn"
}
rows {
  id: 4
  buttonName: "商城"
  buttonIcon: "T_Lobby_Bg_ShopBtn"
  jumpId: 15
  sortId: 5
  showArea: 0
  redDotType: -1
  isLockForMatch: 1
  buttonGreyIcon: "T_Lobby_Bg_GreyShopBtn"
}
rows {
  id: 5
  buttonName: "活动"
  buttonIcon: "T_Lobby_Bg_FoundBtn"
  jumpId: 20
  sortId: 3
  showArea: 1
  redDotType: 28
  isLockForMatch: 1
  buttonGreyIcon: "T_Lobby_Bg_GreyFoundBtn"
}
rows {
  id: 6
  buttonName: "通行证"
  buttonIcon: "T_Lobby_Img_BattlePass"
  jumpId: 50004
  sortId: 6
  showArea: 0
}
rows {
  id: 7
  buttonName: "背包"
  buttonIcon: "T_WXGame_Icon_Bag"
  jumpId: 50005
  sortId: 1
  showArea: 2
  redDotType: 1
}
rows {
  id: 8
  buttonName: "个人信息"
  buttonIcon: "T_WXGame_Icon_PersonnalInformation"
  jumpId: 50006
  sortId: 4
  showArea: 2
  redDotType: 58
}
rows {
  id: 9
  buttonName: "好友"
  buttonIcon: "T_Lobby_Img_Freind"
  jumpId: 50007
  sortId: 2
  showArea: 2
  redDotType: 51
}
rows {
  id: 10
  buttonName: "邮箱"
  buttonIcon: "T_Lobby_Img_Mail"
  jumpId: 50008
  sortId: 5
  showArea: 2
  redDotType: 17
}
rows {
  id: 11
  buttonName: "设置"
  buttonIcon: "T_Lobby_Img_FunctionSet"
  jumpId: 10000
  sortId: 6
  showArea: 2
  redDotType: 85
  redDotKey: "Lobby"
}
rows {
  id: 12
  buttonName: "退出登录"
  buttonIcon: "T_Lobby_Icon_Logout"
  jumpId: 518
  sortId: 7
  showArea: 2
}
rows {
  id: 13
  buttonName: "卡牌集换"
  buttonIcon: "T_CardSystemsCom_Lobby_Img_Card"
  jumpId: 902
  sortId: 3
  showArea: 0
}
rows {
  id: 14
  buttonName: "新手奖励"
  buttonIcon: "T_Lobby_Bg_NewbieBtn"
  jumpId: 38
  sortId: 1
  showArea: 3
}
rows {
  id: 15
  buttonName: "回归"
  buttonIcon: "T_Lobby_Bg_PlayReturnBtn"
  jumpId: 197
  sortId: 2
  showArea: 3
  redDotType: 115
  showRuleFunc: "NeedShowReturnReward"
}
rows {
  id: 16
  buttonName: "祈愿"
  buttonIcon: "T_Lobby_Bg_WishBtn"
  jumpId: 50003
  sortId: 5
  showArea: 3
  redDotType: 30
  isLockForMatch: 1
  buttonGreyIcon: "T_Lobby_Bg_GreyWishBtn"
}
rows {
  id: 17
  buttonName: "热购"
  buttonIcon: "T_Lobby_Bg_RecahrgeBtn"
  jumpId: 50002
  sortId: 3
  showArea: 3
  redDotType: 38
  isLockForMatch: 1
  buttonGreyIcon: "T_Lobby_Bg_GreyRecahrgeBtn"
  showRuleFunc: "NeedShowShop"
}
rows {
  id: 18
  buttonName: "活动"
  buttonIcon: "T_Lobby_Bg_FoundBtn"
  jumpId: 20
  sortId: 4
  showArea: 3
  redDotType: 28
  isLockForMatch: 1
  buttonGreyIcon: "T_Lobby_Bg_GreyFoundBtn"
}
rows {
  id: 19
  buttonName: "好友"
  jumpId: 50007
  sortId: 1
  showArea: 4
  redDotType: 51
}
rows {
  id: 20
  buttonName: "背包"
  jumpId: 50005
  sortId: 3
  showArea: 4
  redDotType: 1
}
rows {
  id: 21
  buttonName: "邮箱"
  jumpId: 50008
  sortId: 2
  showArea: 4
  redDotType: 17
}
rows {
  id: 22
  buttonName: "社团"
  jumpId: 50009
  sortId: 4
  showArea: 4
  redDotType: 100
}
rows {
  id: 23
  buttonName: "卡牌"
  jumpId: 902
  sortId: 5
  showArea: 4
}
rows {
  id: 24
  buttonName: "订阅"
  buttonIcon: "T_Lobby_Img_Subscribe"
  jumpId: 50010
  sortId: 1
  showArea: 5
}
rows {
  id: 25
  buttonName: "设置"
  buttonIcon: "T_Lobby_Img_FunctionSet"
  jumpId: 10000
  sortId: 2
  showArea: 5
  redDotType: 85
  redDotKey: "Lobby"
}
rows {
  id: 26
  buttonName: "退出登录"
  buttonIcon: "T_Lobby_Icon_Logout"
  jumpId: 518
  sortId: 3
  showArea: 5
}
rows {
  id: 27
  buttonName: "玩法回流"
  buttonIcon: "T_Lobby_Bg_PlayReturnBtn"
  jumpId: 654
  sortId: 2
  showArea: 3
  redDotType: 115
  showRuleFunc: "NeedShowGameModeReturnReward"
}
