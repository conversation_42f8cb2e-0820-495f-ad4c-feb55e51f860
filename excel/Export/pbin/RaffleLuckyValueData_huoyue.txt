com.tencent.wea.xlsRes.table_RaffleLuckyValueData
excel/xls/C_抽奖奖池_活跃.xlsx sheet:幸运值配置
rows {
  id: 100050
  poolId: 10006
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100051
  poolId: 10006
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100052
  poolId: 10006
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100053
  poolId: 10006
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
}
rows {
  id: 100054
  poolId: 10006
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
}
rows {
  id: 100055
  poolId: 10006
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
}
rows {
  id: 100056
  poolId: 10006
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
}
rows {
  id: 100057
  poolId: 10006
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
}
rows {
  id: 100058
  poolId: 20000001
  lvThreshold: 5
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100059
  poolId: 20000001
  lvThreshold: 400
  groupWeight: 9
  groupWeight: 30
  groupWeight: 10000
  groupWeight: 33000
  groupWeight: 17000
  groupWeight: 27000
  groupWeight: 160000
  groupWeight: 160000
  groupWeight: 5
  groupWeight: 1
  groupWeight: 830000
}
rows {
  id: 100060
  poolId: 20000001
  lvThreshold: 1500
  groupWeight: 9
  groupWeight: 30
  groupWeight: 5000
  groupWeight: 20000
  groupWeight: 10000
  groupWeight: 25000
  groupWeight: 160000
  groupWeight: 160000
  groupWeight: 5
  groupWeight: 1
  groupWeight: 830000
}
rows {
  id: 100070
  poolId: 20000002
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100071
  poolId: 20000002
  lvThreshold: 1000
  groupWeight: 500
  groupWeight: 0
  groupWeight: 2
  groupWeight: 30000
  groupWeight: 90000
  groupWeight: 1000000
  groupWeight: 100
}
rows {
  id: 100061
  poolId: 10007
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100062
  poolId: 10007
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100063
  poolId: 10007
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100064
  poolId: 10007
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100065
  poolId: 10007
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100066
  poolId: 10007
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
  groupWeight: 0
}
rows {
  id: 100067
  poolId: 10007
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
  groupWeight: 0
}
rows {
  id: 100068
  poolId: 10007
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
  groupWeight: 0
}
rows {
  id: 100069
  poolId: 10007
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
  groupWeight: 0
}
rows {
  id: 100080
  poolId: 10008
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100081
  poolId: 10008
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100082
  poolId: 10008
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100083
  poolId: 10008
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100084
  poolId: 10008
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100085
  poolId: 10008
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
  groupWeight: 0
}
rows {
  id: 100086
  poolId: 10008
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
  groupWeight: 0
}
rows {
  id: 100087
  poolId: 10008
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
  groupWeight: 0
}
rows {
  id: 100088
  poolId: 10008
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
  groupWeight: 0
}
rows {
  id: 100090
  poolId: 10009
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100091
  poolId: 10009
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100092
  poolId: 10009
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100093
  poolId: 10009
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100094
  poolId: 10009
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100095
  poolId: 10009
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
  groupWeight: 0
}
rows {
  id: 100096
  poolId: 10009
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
  groupWeight: 0
}
rows {
  id: 100097
  poolId: 10009
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
  groupWeight: 0
}
rows {
  id: 100098
  poolId: 10009
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
  groupWeight: 0
}
rows {
  id: 100120
  poolId: 20000003
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100121
  poolId: 20000003
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100122
  poolId: 20000003
  lvThreshold: 8
  groupWeight: 95
  groupWeight: 5
  groupWeight: 0
}
rows {
  id: 100123
  poolId: 20000003
  lvThreshold: 11
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100124
  poolId: 20000004
  lvThreshold: 1
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 100125
  poolId: 20000004
  lvThreshold: 12
  groupWeight: 975
  groupWeight: 25
}
rows {
  id: 100126
  poolId: 20000004
  lvThreshold: 16
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100127
  poolId: 20000005
  lvThreshold: 1
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 100128
  poolId: 20000005
  lvThreshold: 12
  groupWeight: 975
  groupWeight: 25
}
rows {
  id: 100129
  poolId: 20000005
  lvThreshold: 16
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100130
  poolId: 20000006
  lvThreshold: 1
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 100131
  poolId: 20000006
  lvThreshold: 12
  groupWeight: 975
  groupWeight: 25
}
rows {
  id: 100132
  poolId: 20000006
  lvThreshold: 16
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100133
  poolId: 20000007
  lvThreshold: 1
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 100134
  poolId: 20000007
  lvThreshold: 24
  groupWeight: 983
  groupWeight: 25
}
rows {
  id: 100135
  poolId: 20000007
  lvThreshold: 31
  groupWeight: 1
  groupWeight: 0
}
rows {
  id: 100109
  poolId: 20000008
  lvThreshold: 1
  groupWeight: 1
}
rows {
  id: 100110
  poolId: 20000008
  lvThreshold: 10
  groupWeight: 1
}
rows {
  id: 100111
  poolId: 10010
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100112
  poolId: 10010
  lvThreshold: 5
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100113
  poolId: 10010
  lvThreshold: 6
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100114
  poolId: 10010
  lvThreshold: 7
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100115
  poolId: 10010
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100116
  poolId: 10010
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
  groupWeight: 0
}
rows {
  id: 100117
  poolId: 10010
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
  groupWeight: 0
}
rows {
  id: 100118
  poolId: 10010
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
  groupWeight: 0
}
rows {
  id: 100119
  poolId: 10010
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
  groupWeight: 0
}
rows {
  id: 100136
  poolId: 10011
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100137
  poolId: 10011
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100138
  poolId: 10011
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100139
  poolId: 10011
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100140
  poolId: 10011
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
  groupWeight: 0
}
rows {
  id: 100141
  poolId: 10011
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
  groupWeight: 0
}
rows {
  id: 100142
  poolId: 10011
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
  groupWeight: 0
}
rows {
  id: 100143
  poolId: 10011
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
  groupWeight: 0
}
rows {
  id: 100190
  poolId: 20000009
  lvThreshold: 1
  groupWeight: 1
}
rows {
  id: 100191
  poolId: 20000009
  lvThreshold: 10
  groupWeight: 1
}
rows {
  id: 100200
  poolId: 10012
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100201
  poolId: 10012
  lvThreshold: 2
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
}
rows {
  id: 100202
  poolId: 10012
  lvThreshold: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 100203
  poolId: 10012
  lvThreshold: 30
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 3
  groupWeight: 20
  groupWeight: 45
  groupWeight: 0
}
rows {
  id: 100204
  poolId: 10012
  lvThreshold: 100
  groupWeight: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 51
  groupWeight: 0
}
rows {
  id: 100205
  poolId: 10012
  lvThreshold: 200
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 2
  groupWeight: 2
  groupWeight: 15
  groupWeight: 53
  groupWeight: 0
}
rows {
  id: 100206
  poolId: 10012
  lvThreshold: 300
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 14
  groupWeight: 55
  groupWeight: 0
}
rows {
  id: 100207
  poolId: 10012
  lvThreshold: 1000
  groupWeight: 28
  groupWeight: 0
  groupWeight: 0
  groupWeight: 1
  groupWeight: 2
  groupWeight: 12
  groupWeight: 57
  groupWeight: 0
}
