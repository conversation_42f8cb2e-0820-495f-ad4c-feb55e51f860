com.tencent.wea.xlsRes.table_PlotConfig
excel/xls/J_剧情表.xlsx sheet:剧情表
rows {
  id: 1
  plot_id: 1
  name: "小红狐"
  role: "T_Ultraman_Img_Role_11"
  background: "T_Ultraman_Img_Guide_01"
  text: "走过路过不要错过，星梦广场的“星”年派对可不一般，到处都在发红包，瞧我的口袋都鼓起来了，这回可这真要嗨翻天！"
  style: 0
}
rows {
  id: 2
  plot_id: 1
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_01"
  text: "不得了，原来“嗨翻天”的意思是真的给天空开个洞...好大一个洞！"
  style: 0
}
rows {
  id: 3
  plot_id: 1
  name: "小红狐"
  role: "T_Ultraman_Img_Role_11"
  background: "T_Ultraman_Img_Guide_01"
  text: "你在说什么“哈言哈语”……慢着，怎么天空中真的开了一个洞？不好，有不明星宝正在闯入星梦广场！"
  style: 0
}
rows {
  id: 4
  plot_id: 1
  name: "芝顿"
  role: "T_Ultraman_Img_Role_09"
  background: "T_Ultraman_Img_Guide_01"
  text: "芝顿……芝顿……"
  style: 0
}
rows {
  id: 5
  plot_id: 1
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_01"
  text: "好家伙，“嗨翻天”就是请芝顿来翻天。"
  style: 0
}
rows {
  id: 6
  plot_id: 1
  name: "小红狐"
  role: "T_Ultraman_Img_Role_12"
  background: "T_Ultraman_Img_Guide_01"
  text: "芝顿该不是要毁了派对吧？红鲤仙在哪？我想许个愿，请英雄来救场。"
  style: 0
}
rows {
  id: 7
  plot_id: 1
  name: "赛罗奥特曼"
  role: "T_Ultraman_Img_Role_06"
  background: "T_Ultraman_Img_Guide_01"
  text: "别担心，我总在关键时刻出手，这次也不例外。"
  style: 0
}
rows {
  id: 8
  plot_id: 1
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_01"
  text: "宇宙英雄出现了！快说谢谢红鲤仙。"
  style: 0
}
rows {
  id: 9
  plot_id: 1
  name: "小红狐"
  role: "T_Ultraman_Img_Role_11"
  background: "T_Ultraman_Img_Guide_01"
  text: "谢谢红鲤仙……等等，赛罗奥特曼竟然来到咱们派对了！请给我签名。"
  style: 0
}
rows {
  id: 10
  plot_id: 1
  name: "赛罗奥特曼"
  role: "T_Ultraman_Img_Role_06"
  background: "T_Ultraman_Img_Guide_01"
  text: "是我赛罗，芝顿交给我，你们快去收集散落在广场上的星光粒子，一同守护其他星宝！"
  style: 0
}
rows {
  id: 11
  plot_id: 1
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_07"
  background: "T_Ultraman_Img_Guide_01"
  text: "没错，我很需要被保护……"
  style: 0
}
rows {
  id: 12
  plot_id: 1
  name: "小红狐"
  role: "T_Ultraman_Img_Role_12"
  background: "T_Ultraman_Img_Guide_01"
  text: "（捂住哈士奇的嘴）请放心，星梦广场的安全就交给我们，赛罗，你尽管放心去战斗！"
  style: 0
}
rows {
  id: 13
  plot_id: 2
  name: "芝顿"
  role: "T_Ultraman_Img_Role_09"
  background: "T_Ultraman_Img_Guide_02"
  text: "芝顿，芝顿！"
  style: 0
}
rows {
  id: 14
  plot_id: 2
  background: "T_Ultraman_Img_Guide_02"
  text: "（芝顿投掷了一些包裹，分身小芝顿挤满广场。）"
  style: 0
}
rows {
  id: 15
  plot_id: 2
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_02"
  text: "不得了，天还没翻成，地先翻了……"
  style: 0
}
rows {
  id: 16
  plot_id: 2
  name: "巨鳄霸"
  role: "T_Ultraman_Img_Role_02"
  background: "T_Ultraman_Img_Guide_02"
  text: "小芝顿来咯！欢迎新朋友！"
  style: 0
}
rows {
  id: 17
  plot_id: 2
  name: "小红狐"
  role: "T_Ultraman_Img_Role_10"
  background: "T_Ultraman_Img_Guide_02"
  text: "巨鳄霸，你怎么能站在怪兽一边？！我们需要控制住场面，赛罗奥特曼，快来帮帮我们！"
  style: 0
}
rows {
  id: 18
  plot_id: 2
  name: "赛罗奥特曼"
  role: "T_Ultraman_Img_Role_06"
  background: "T_Ultraman_Img_Guide_02"
  text: "星宝们，在活动区可以领取我的眼镜，可以发射集束光线。广场上的所有道具也可应付的芝顿的分身，但是集束光线的力量会让你变得更强。芝顿交给我，我会拦住他。"
  style: 0
}
rows {
  id: 19
  plot_id: 2
  name: "小红狐"
  role: "T_Ultraman_Img_Role_12"
  background: "T_Ultraman_Img_Guide_02"
  text: "瞧我一手一个，打败分身还能得到星光粒子，赛罗眼镜的威力果然不一样！"
  style: 0
}
rows {
  id: 20
  plot_id: 2
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_02"
  text: "这下，我终于有个正经机会嗨翻天……"
  style: 0
}
rows {
  id: 21
  plot_id: 2
  name: "小红狐"
  role: "T_Ultraman_Img_Role_12"
  background: "T_Ultraman_Img_Guide_02"
  text: "可别添乱了！我们的目标是收集更多的星光粒子！"
  style: 0
}
rows {
  id: 22
  plot_id: 3
  name: "小红狐"
  role: "T_Ultraman_Img_Role_11"
  background: "T_Ultraman_Img_Guide_03"
  text: "星光粒子都收集齐了，赛罗奥特曼，能看到你变身吗？"
  style: 0
}
rows {
  id: 23
  plot_id: 3
  name: "赛罗奥特曼"
  role: "T_Ultraman_Img_Role_06"
  background: "T_Ultraman_Img_Guide_03"
  text: "接收完毕，看我的奥特之力！奥特战士，出击！"
  style: 0
}
rows {
  id: 24
  plot_id: 3
  name: "泽塔奥特曼"
  role: "T_Ultraman_Img_Role_03"
  background: "T_Ultraman_Img_Guide_03"
  text: "师父，我来助你一臂之力！"
  style: 0
}
rows {
  id: 25
  plot_id: 3
  name: "迪迦奥特曼"
  role: "T_Ultraman_Img_Role_01"
  background: "T_Ultraman_Img_Guide_03"
  text: "在三分钟内，我会解决战斗！"
  style: 0
}
rows {
  id: 26
  plot_id: 3
  name: "赛罗奥特曼"
  role: "T_Ultraman_Img_Role_05"
  background: "T_Ultraman_Img_Guide_03"
  text: "赛罗集束光线，发射！"
  style: 0
}
rows {
  id: 27
  plot_id: 3
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_03"
  text: "我居然能在星梦广场看到三个奥特曼集结，哈生圆梦了。"
  style: 0
}
rows {
  id: 28
  plot_id: 3
  name: "芝顿"
  role: "T_Ultraman_Img_Role_09"
  background: "T_Ultraman_Img_Guide_03"
  text: "芝——顿——"
  style: 0
}
rows {
  id: 29
  plot_id: 3
  background: "T_Ultraman_Img_Guide_03"
  text: "（随着赛罗的招数，芝顿的身体迅速缩小，在光束中散落了一地芝顿套装）"
  style: 0
}
rows {
  id: 30
  plot_id: 3
  name: "小红狐"
  role: "T_Ultraman_Img_Role_11"
  background: "T_Ultraman_Img_Guide_03"
  text: "这样的战利品还是第一次见！"
  style: 0
}
rows {
  id: 31
  plot_id: 3
  name: "巨鳄霸"
  role: "T_Ultraman_Img_Role_02"
  background: "T_Ultraman_Img_Guide_03"
  text: "什么战利品，可别错怪芝顿了，让我用智能翻译器，给你们翻译翻译——"
  style: 0
}
rows {
  id: 32
  plot_id: 3
  name: "芝顿"
  role: "T_Ultraman_Img_Role_09"
  background: "T_Ultraman_Img_Guide_03"
  text: "芝顿，芝顿，芝顿！（星宝们，新年好，我只是想参加你们的派对，还带了礼物。）"
  style: 0
}
rows {
  id: 33
  plot_id: 3
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_03"
  text: "原来怪兽也想过新年，怪兽的新年礼物，我还是第一次收，让我试试合不合身。"
  style: 0
}
rows {
  id: 34
  plot_id: 3
  name: "小红狐"
  role: "T_Ultraman_Img_Role_12"
  background: "T_Ultraman_Img_Guide_03"
  text: "你可别独吞了！礼物也要分享出去，这些都是星宝们共同战斗的结果，不如我们就举办一个芝顿派对吧！"
  style: 0
}
rows {
  id: 35
  plot_id: 3
  name: "巨鳄霸"
  role: "T_Ultraman_Img_Role_02"
  background: "T_Ultraman_Img_Guide_03"
  text: "理解芝顿，成为芝顿！"
  style: 0
}
rows {
  id: 36
  plot_id: 3
  name: "哈士奇"
  role: "T_Ultraman_Img_Role_08"
  background: "T_Ultraman_Img_Guide_03"
  text: "不得了，这下真的可以嗨翻天！"
  style: 0
}
rows {
  id: 37
  plot_id: 4
  name: "阿花"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "阿花文本测试"
}
rows {
  id: 38
  plot_id: 4
  name: "阿花"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "阿花文本测试"
}
rows {
  id: 39
  plot_id: 4
  name: "阿花"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "阿花文本测试"
}
rows {
  id: 40
  plot_id: 5
  name: "厨师"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "厨师文本测试"
}
rows {
  id: 41
  plot_id: 5
  name: "厨师"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "厨师文本测试"
}
rows {
  id: 42
  plot_id: 5
  name: "厨师"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "厨师文本测试"
}
rows {
  id: 43
  plot_id: 6
  name: "阿花"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "阿花和厨师的文本测试"
}
rows {
  id: 44
  plot_id: 6
  name: "厨师"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "阿花和厨师的文本测试"
}
rows {
  id: 45
  plot_id: 6
  name: "阿花"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "阿花和厨师的文本测试"
}
rows {
  id: 46
  plot_id: 6
  name: "厨师"
  role: "T_Ultraman_Img_Role_11"
  background: "T_FarmRestaurant_Img_Background"
  text: "阿花和厨师的文本测试"
}
