com.tencent.wea.xlsRes.table_TextEntryNR3EData
excel/xls/W_文本表_文本配置_NR3E_赛事.xlsx sheet:文本配置
rows {
  id: "UI_Competition_InLevelStartHint"
  content: "冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint1"
  content: "海选阶段"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint2"
  content: "周五20点开赛"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint3"
  content: "你是那个万里挑一的幸运星么？快来参赛，赢得各种大奖。"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint4"
  content: "海选赛将于{0}开赛"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint6"
  content: "快去参加<StartHintYellow23>排位赛</>，赢取海选积分吧！"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint5"
  content: "第<StartUpYellow>{0}</>名参赛星宝"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint7"
  content: "<{0}>海选赛第</><{1}>{2}</><{3}>名</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint8"
  content: "<{0}>淘汰赛第</><{1}>{2}</><{3}>名</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint9"
  content: "<{0}>巅峰对决第</><{1}>{2}</><{3}>名</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint10"
  content: "海选{0}分"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStartHint11"
  content: "赛事：冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_Main"
  content: "冠军之星信息"
  switch: 1
}
rows {
  id: "UI_Competition_Main1"
  content: "冠军之星积分"
  switch: 1
}
rows {
  id: "UI_Competition_Main2"
  content: "巅峰殿堂"
  switch: 1
}
rows {
  id: "UI_Competition_Main3"
  content: "冠军之星说明"
  switch: 1
}
rows {
  id: "UI_Competition_Main4"
  content: "第{0}周"
  switch: 1
}
rows {
  id: "UI_Competition_Main5"
  content: "报名中"
  switch: 1
}
rows {
  id: "UI_Competition_Main6"
  content: "剩余时间：{0}天{1}小时"
  switch: 1
}
rows {
  id: "UI_Competition_Main7"
  content: "快快报名参加冠军之星赛事吧。\n努力通过海选，晋级到激烈的淘汰赛中。\n丰富的赛事奖励在等着你呢～"
  switch: 1
}
rows {
  id: "UI_Competition_Main8"
  content: "报名费"
  switch: 1
}
rows {
  id: "UI_Competition_Main9"
  content: "报名 (周二8点到周五20点20分)"
  switch: 1
}
rows {
  id: "UI_Competition_Main10"
  content: "海选赛 (周五20点至21点)"
  switch: 1
}
rows {
  id: "UI_Competition_Main11"
  content: "淘汰赛 (周六20点5分至21点)"
  switch: 1
}
rows {
  id: "UI_Competition_Main12"
  content: "第{0}周冠军"
  switch: 1
}
rows {
  id: "UI_Competition_Main13"
  content: "已报名"
  switch: 1
}
rows {
  id: "UI_Competition_Main14"
  content: "需要达到白银启明星，才可报名。"
  switch: 1
}
rows {
  id: "UI_Competition_Main15"
  content: "冠军荣耀"
  switch: 1
}
rows {
  id: "UI_Competition_Main16"
  content: "淘汰阶段"
  switch: 1
}
rows {
  id: "UI_Competition_Main17"
  content: "第{0}参赛星宝"
  switch: 1
}
rows {
  id: "UI_Competition_Main18"
  content: "海选赛地图"
  switch: 1
}
rows {
  id: "UI_Competition_Main19"
  content: "匹配"
  switch: 1
}
rows {
  id: "UI_Competition_Main20"
  content: "我的积分：{0}"
  switch: 1
}
rows {
  id: "UI_Competition_Main21"
  content: "当前排名：{0}"
  switch: 1
}
rows {
  id: "UI_Competition_Main22"
  content: "取消匹配"
  switch: 1
}
rows {
  id: "UI_Competition_Main23"
  content: "匹配中"
  switch: 1
}
rows {
  id: "UI_Competition_Main24"
  content: "等待海选赛结束，确定最终排名。"
  switch: 1
}
rows {
  id: "UI_Competition_Main25"
  content: "{0}分{0}秒"
  switch: 1
}
rows {
  id: "UI_Competition_Main26"
  content: "你将在8张海选地图中各进行一场比赛。海选地图的比赛顺序是随机的"
  switch: 1
}
rows {
  id: "UI_Competition_Main27"
  content: "{1}秒后比赛开始"
  switch: 1
}
rows {
  id: "UI_Competition_Main28"
  content: "{0}分"
  switch: 1
}
rows {
  id: "UI_Competition_Main29"
  content: "第{0}组"
  switch: 1
}
rows {
  id: "UI_Competition_Main30"
  content: "第{1}轮"
  switch: 1
}
rows {
  id: "UI_Competition_Main31"
  content: "恭喜晋级淘汰赛"
  switch: 1
}
rows {
  id: "UI_Competition_Main32"
  content: "所有玩家分组后进行7轮比赛，胜者晋级，\n败者淘汰。\n最终的32名胜者将进行巅峰对决。"
  switch: 1
}
rows {
  id: "UI_Competition_Main33"
  content: "已结束"
  switch: 1
}
rows {
  id: "UI_Competition_Main34"
  content: "已淘汰"
  switch: 1
}
rows {
  id: "UI_Competition_Main35"
  content: "未晋级"
  switch: 1
}
rows {
  id: "UI_Competition_Main36"
  content: "未报名"
  switch: 1
}
rows {
  id: "UI_Competition_Main37"
  content: "巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_Main38"
  content: "恭喜晋级巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_Main39"
  content: "参与{0}场海选比赛，赢的个人积分。\n积分排名前<ModelSelectYellow>{1}名</>玩家晋级淘汰赛。"
  switch: 1
}
rows {
  id: "UI_Competition_Main40"
  content: "剩余：{0}"
  switch: 1
}
rows {
  id: "UI_Competition_Main41"
  content: "无"
  switch: 1
}
rows {
  id: "UI_Competition_Main42"
  content: "匹配中无法退出"
  switch: 1
}
rows {
  id: "UI_Competition_Main43"
  content: "我的比赛: 第 {0} 组 第 {1} 轮（{2}进{3}）"
  switch: 1
}
rows {
  id: "UI_Competition_Main44"
  content: "我的比赛: 巅峰对决（{0}进{1}）"
  switch: 1
}
rows {
  id: "UI_Competition_Main45"
  content: "淘汰赛"
  switch: 1
}
rows {
  id: "UI_Competition_Main46"
  content: "海选赛第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Main47"
  content: "海选阶段（{0}）"
  switch: 1
}
rows {
  id: "UI_Competition_Main48"
  content: "荣誉展示（{0}）"
  switch: 1
}
rows {
  id: "UI_Competition_Main49"
  content: "淘汰赛（{0}）"
  switch: 1
}
rows {
  id: "UI_Competition_Main50"
  content: "虚位以待"
  switch: 1
}
rows {
  id: "UI_Competition_Main51"
  content: "需要达到<CompetitionYellow>{0}</>，才可报名"
  switch: 1
}
rows {
  id: "UI_Competition_Main52"
  content: "淘汰赛第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Main53"
  content: "周一"
  switch: 1
}
rows {
  id: "UI_Competition_Main54"
  content: "周二"
  switch: 1
}
rows {
  id: "UI_Competition_Main55"
  content: "周三"
  switch: 1
}
rows {
  id: "UI_Competition_Main56"
  content: "周四"
  switch: 1
}
rows {
  id: "UI_Competition_Main57"
  content: "周五"
  switch: 1
}
rows {
  id: "UI_Competition_Main58"
  content: "周六"
  switch: 1
}
rows {
  id: "UI_Competition_Main59"
  content: "周日"
  switch: 1
}
rows {
  id: "UI_Competition_Main60"
  content: "巅峰对决{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Main61"
  content: "冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_Main62"
  content: "冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_Main63"
  content: "报名玩家:{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Main64"
  content: "剩余淘汰赛名额:{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Main65"
  content: "无法报名，晋级名额已满"
  switch: 1
}
rows {
  id: "UI_Competition_Main66"
  content: "第{0}周"
  switch: 1
}
rows {
  id: "UI_Competition_InLevel_Reconnect"
  content: "确定要报名参赛么？"
  switch: 1
}
rows {
  id: "UI_Competition_InLevel_Reconnect1"
  content: "报名费:"
  switch: 1
}
rows {
  id: "UI_Competition_InLevel_Reconnect2"
  content: "报名"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelRank"
  content: "排名信息更新中···"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelRank1"
  content: "海选排名"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelRank2"
  content: "暂无"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelRank3"
  content: "比赛中"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelRank4"
  content: "准备中"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelRank5"
  content: "{0}场"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelRank6"
  content: "{0}分"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount"
  content: "比赛结束"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount1"
  content: "本局排名"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount2"
  content: "本局积分"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount3"
  content: "当前总分"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount4"
  content: "恭喜晋级"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount5"
  content: "遗憾出局"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount6"
  content: "成功晋级巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount7"
  content: "第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount8"
  content: "返回大厅"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount9"
  content: "不要这样写！"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount10"
  content: "小组冠军"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount11"
  content: "淘汰赛第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount12"
  content: "冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount13"
  content: "淘汰赛 第{0}轮"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount14"
  content: "巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount15"
  content: "第{0}周冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount16"
  content: "巅峰对决第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount17"
  content: "冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelFinalAccount18"
  content: "比赛成绩：第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo"
  content: "分组信息"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo1"
  content: "冠军之星说明"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo2"
  content: "第{0}组"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo3"
  content: "是否退出房间？"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo4"
  content: "<CustomRoom29Y>{0}</>时"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo5"
  content: "第{0}轮"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo6"
  content: "{0}进{1}"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo7"
  content: "冠军之星（淘汰赛）"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo8"
  content: "冠军之星（巅峰对决）"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo9"
  content: "<CustomRoom29Y>{0}</>分钟"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo10"
  content: "<CustomRoom29Y>{0}</>秒"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo11"
  content: "竞速"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo12"
  content: "积分"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo13"
  content: "生存"
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo14"
  content: "分组："
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo15"
  content: "地图："
  switch: 1
}
rows {
  id: "UI_Competition_RoomInfo16"
  content: "模式："
  switch: 1
}
rows {
  id: "UI_Competition_Integral_Award"
  content: "本期积分"
  switch: 1
}
rows {
  id: "UI_Competition_Integral_Award1"
  content: "距离下个奖励，还需{0}分，"
  switch: 1
}
rows {
  id: "UI_Competition_Integral_Award2"
  content: "不要这样写！"
  switch: 1
}
rows {
  id: "UI_Competition_GroupPanel"
  content: "第{0}轮-{1}进{2}"
  switch: 1
}
rows {
  id: "UI_Competition_EliminationListView"
  content: "淘汰赛分组"
  switch: 1
}
rows {
  id: "UI_Competition_EliminationListView1"
  content: "巅峰对决({0}进{1})"
  switch: 1
}
rows {
  id: "UI_Competition_EliminationListView2"
  content: "{0}后比赛开始"
  switch: 1
}
rows {
  id: "UI_Competition_EliminationListView3"
  content: "{0}后可以进入房间"
  switch: 1
}
rows {
  id: "UI_Competition_Integral"
  content: "暂无任何成绩"
  switch: 1
}
rows {
  id: "UI_Competition_Integral1"
  content: "排名信息更新中···"
  switch: 1
}
rows {
  id: "UI_Competition_Integral2"
  content: "距离下个奖励,还需<CompetitionDeepYellow>{0}</>分"
  switch: 1
}
rows {
  id: "UI_Competition_Integral3"
  content: "{0},赛事奖杯与专属奖励都将重置刷新。"
  switch: 1
}
rows {
  id: "UI_Competition_Integral4"
  content: "可领取"
  switch: 1
}
rows {
  id: "UI_Competition_Integral5"
  content: "暂无任何数据"
  switch: 1
}
rows {
  id: "UI_Competition_Integral6"
  content: "暂无积分奖励"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStageHint"
  content: "恭喜晋级淘汰赛！"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStageHint1"
  content: "巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStageHint2"
  content: "恭喜晋级巅峰对决！"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStageHint3"
  content: "海选赛排名：{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStageHint4"
  content: "冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelStageHint5"
  content: "恭喜晋级决赛！"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_title1"
  content: "赛事介绍"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_title2"
  content: "报名"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_title3"
  content: "海选阶段"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_title4"
  content: "淘汰赛"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_title5"
  content: "巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_title6"
  content: "奖励"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_title7"
  content: "赛事奖杯"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content1"
  content: "基于天天晋级赛的盛大赛事，谁会是万里挑一的靓丽星宝呢？"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content2"
  content: "报名即可参赛，经过<Recruit23>海选阶段</>、<Recruit23>淘汰赛</>、<Recruit23>巅峰对决</>，决出最终的总冠军。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content3"
  content: "每一届赛事包含前几周的<Recruit23>线上周赛</>，以及<Recruit23>最终的线下赛</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content4"
  content: "报名时间:<Recruit23>{0}</>至<Recruit23>{1}</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content5"
  content: "报名条件:段位达到<Recruit23>{0}及以上</>，无人数限制"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content6"
  content: "报名费用:{0}{1}。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content7"
  content: "海选时间:<Recruit23>每{0}</>至<Recruit23>{1}</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content8"
  content: "报名后，在排位赛中<Recruit23>进入最终关</>将获得海选积分。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content9"
  content: "段位越高，获得的海选积分也越多；每<Recruit23>{0}</>期间，可以获得<Recruit23>双倍海选积分</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content10"
  content: "在海选时间内，累计<Recruit23>获得{0}个海选积分</>，将晋级淘汰赛。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content11"
  content: "<Recruit23>每{0}</>可以进入比赛房间，<Recruit23>{1}</>开始比赛。未进入房间的玩家将被<Recruit23>直接淘汰</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content12"
  content: "淘汰赛分为<Recruit23>淘汰阶段</>和<Recruit23>巅峰对决</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content13"
  content: "晋级淘汰赛的玩家将被<Recruit23>分为32个小组</>，每组<Recruit23>进行9轮比赛</>；决出最终的32名小组冠军，晋级巅峰对决。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content14"
  content: "淘汰赛制<Recruit23>包含32进16，16进8,8进1等形式</>，胜者晋级下一轮，败者立刻被淘汰。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content15"
  content: "淘汰赛32名小组冠军将进行最终的巅峰对决。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content16"
  content: "巅峰对决将只进行<Recruit23>一场比赛</>，该比赛有<Recruit23>7个回合</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content17"
  content: "每个回合中，玩家们将在不同的地图中进行比赛，并逐步淘汰部分玩家，比如32进28,28进24......"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content18"
  content: "最终的胜者将成为本周赛事总冠军。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content19"
  content: "根据每周比赛中的最终排名，玩家将获得相应的赛事奖杯。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content20"
  content: "玩家可累积赛事奖杯，兑换<Recruit23>专属奖励</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content21"
  content: "每届赛事会持续若干周。结束时，赛事奖杯与专属奖励都<Recruit23>将重置</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content22"
  content: "使用<Recruit23>模拟器</>、<Recruit23>破解设备</>，将被禁止报名参赛。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content23"
  content: "比赛中的任何<Recruit23>违规行为</>都将会受到<Recruit23>严厉的惩罚</>。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_content24"
  content: "快快报名吧，丰富的奖励等着你呢。"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_award1"
  content: "海选{0}-{1}分"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_award2"
  content: "淘汰赛{0}-{1}名"
  switch: 1
}
rows {
  id: "UI_Competition_ExplainMain_award3"
  content: "巅峰对决{0}-{1}名"
  switch: 1
}
rows {
  id: "UI_Competition_HistoryRecord"
  content: "冠军"
  switch: 1
}
rows {
  id: "UI_Competition_HistoryRecord1"
  content: "巅峰选手"
  switch: 1
}
rows {
  id: "UI_Competition_IntegralGrade1"
  content: "第{0}周 {1}"
  switch: 1
}
rows {
  id: "UI_Competition_IntegralGrade2"
  content: "{0}第{1}名"
  switch: 1
}
rows {
  id: "UI_Competition_IntegralGrade3"
  content: "{0}{1}分"
  switch: 1
}
rows {
  id: "UI_Competition_IntegralTips"
  content: "1、根据赛事中的最终排名，玩家将获得相应的赛事奖杯。\n2、玩家可以累积赛事奖杯，兑换专属奖励。\n3、每届赛事会持续若干周，结束时，赛事奖杯与专属奖励都将重置。"
  switch: 1
}
rows {
  id: "UI_Competition_IntegralUnlockAllReward"
  content: "已解锁本期全部奖励！"
  switch: 1
}
rows {
  id: "UI_Competition_IntegralRewardYMD"
  content: "%Y年%m月%d日"
  switch: 1
}
rows {
  id: "UI_Competition_InLevel_GroupChampion"
  content: "小组冠军"
  switch: 1
}
rows {
  id: "UI_Competition_InLevel_Elimination"
  content: "淘汰了"
  switch: 1
}
rows {
  id: "UI_Competition_RoomTip_Full"
  content: "房间已满，无法进入"
  switch: 1
}
rows {
  id: "UI_Competition_RoomTip_HasStarted"
  content: "比赛已经开始，无法进入"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection1"
  content: "海选积分{0}/{1}"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection2"
  content: "第{0}周-冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection3"
  content: "参加<CompetitionYellow21>排位赛</>，进入最终关可获得<CompetitionYellow21>海选积分</>！"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection4"
  content: "累积{0}个海选积分，将获得<CompetitionYellow21>淘汰赛资格</>！"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection5"
  content: "剩余时间：{0}"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection6"
  content: "<CompetitionYellow18>{0}</>期间，可以获得<CompetitionYellow18>双倍</>海选积分。"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection7"
  content: "剩余淘汰赛名额：<CompetitionMain23>{0}</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection8"
  content: "<CompetitionMainRed23>未晋级，淘汰赛名额已满</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection9"
  content: "海选{0}分（淘汰赛名额已满）"
  switch: 1
}
rows {
  id: "UI_Competition_HallOfFame"
  content: "第<CompetitionWhite80>{0}</>周 "
  switch: 1
}
rows {
  id: "UI_Competition_GroupPanelItem1"
  content: "神秘的AI选手"
  switch: 1
}
rows {
  id: "UI_Competition_Stage1"
  content: "海选"
  switch: 1
}
rows {
  id: "UI_Competition_Stage2"
  content: "淘汰赛"
  switch: 1
}
rows {
  id: "UI_Competition_Stage3"
  content: "巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_Enroll_OutOfTime"
  content: "报名超时，请留意报名时间！"
  switch: 1
}
rows {
  id: "UI_Competition_Enroll_ItemNotEnough"
  content: "星宝印章数量不足！"
  switch: 1
}
rows {
  id: "UI_Competition_CompetitionStartTip1"
  content: "淘汰赛将于<InLevel_PromotionYellow>{0}</>开赛"
  switch: 1
}
rows {
  id: "UI_Competition_CompetitionLimitTip"
  content: "很遗憾，本周淘汰赛名额已满"
  switch: 1
}
rows {
  id: "UI_Competition_EliminationGroup1"
  content: "暂无参赛玩家信息"
  switch: 1
}
rows {
  id: "UI_Competition_EnterRoomTip_Elimination"
  content: "冠军之星淘汰赛即将开始，是否前往？"
  switch: 1
}
rows {
  id: "UI_Competition_EnterRoomTip_Peak"
  content: "冠军之星巅峰对决即将开始，是否前往？"
  switch: 1
}
rows {
  id: "UI_Competition_RoomStartTip_Elimination_TenMinute"
  content: "冠军之星淘汰阶段将于10分钟后开始！"
  switch: 1
}
rows {
  id: "UI_Competition_RoomStartTip_Elimination"
  content: "冠军之星{0}分钟后开始，请前往比赛房间！"
  switch: 1
}
rows {
  id: "UI_Competition_RoomStartTip_Peak"
  content: "冠军之星{0}分钟后开始，请前往比赛房间！"
  switch: 1
}
rows {
  id: "UI_Competition_JoinRoomError_AlreadyInTeam"
  content: "已在队伍中，无法进入房间"
  switch: 1
}
rows {
  id: "UI_Competition_RankAwardGroup_Title1"
  content: "总冠军"
  switch: 1
}
rows {
  id: "UI_Competition_RankAward_Tips"
  content: "淘汰赛奖励会随着海选晋级人数\n动态变化。"
  switch: 1
}
rows {
  id: "UI_Competition_RedRewardPanel1"
  content: "{0}元现金红包"
  switch: 1
}
rows {
  id: "UI_Competition_RedRewardPanel2"
  content: "请在您的QQ钱包中确认到账"
  switch: 1
}
rows {
  id: "UI_Competition_RedRewardPanel3"
  content: "请在您的微信钱包中确认到账"
  switch: 1
}
rows {
  id: "UI_Competition_HonorRank"
  content: "巅峰排名"
  switch: 1
}
rows {
  id: "UI_Competition_HonorRank1"
  content: "本周冠军"
  switch: 1
}
rows {
  id: "UI_Competition_HonorRank2"
  content: "第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_HonorRank3"
  content: "第{0}周"
  switch: 1
}
rows {
  id: "UI_Competition_RoomTip_InvalidVersion"
  content: "游戏版本低，更新后再试试吧"
  switch: 1
}
rows {
  id: "UI_Competition_Security_UserCheatingRecently"
  content: "检测到近期有作弊行为，禁止参赛！"
  switch: 1
}
rows {
  id: "UI_Competition_Security_NotAllowSimulator"
  content: "模拟器设备不允许参赛，请更换设备！"
  switch: 1
}
rows {
  id: "UI_Competition_Security_NotAllowRootDevice"
  content: "破解设备不允许参赛，请更换设备！"
  switch: 1
}
rows {
  id: "UI_Competition_Security_DeviceReachEnrollLimit"
  content: "当前设备报名数量已达上限，禁止报名！"
  switch: 1
}
rows {
  id: "UI_Competition_Security_DeviceCheatingRecently"
  content: "检测到该设备有过作弊行为，禁止参赛！"
  switch: 1
}
rows {
  id: "UI_Competition_Security_BlackFace"
  content: "检测到该设备有过作弊行为，禁止参赛！"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection_RemainingEliminationQuota"
  content: "剩余淘汰赛名额：<CompetitionMain23>{0}</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection_EliminationQuotaIsFull"
  content: "<CompetitionMainRed23>淘汰赛名额已满</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection_EliminationTimeHint"
  content: "淘汰赛:<CompetitionMain23>{0}</>开始准备，<CompetitionMain23>{1}</>正式开赛"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection_EliminationUnPromotedScore"
  content: "最终成绩：<CompetitionMain23>海选{0}分</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection_EliminationUnPromotedReachLimit"
  content: "最终成绩：<CompetitionMain23>海选{0}分(淘汰赛名额已满)</>"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection_EliminationPeriod"
  content: "海选阶段"
  switch: 1
}
rows {
  id: "UI_Competition_InLevelMassElection_EliminationEnd"
  content: "海选结束"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Title1"
  content: "比赛列表"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Title2"
  content: "{0}-{1}组"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Title3"
  content: "关注玩家"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Sort1"
  content: "分组排序"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Sort2"
  content: "时间排序"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Review"
  content: "上周前{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Review1"
  content: "尚未进入比赛房间"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Review2"
  content: "排位前{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Review3"
  content: "巅峰对决第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Review4"
  content: "段位排行第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Review5"
  content: "暂无数据"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_RoomInfo1"
  content: "第{0}组"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_RoomInfo2"
  content: "第{0}轮"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_State1"
  content: "准备中"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_State2"
  content: "比赛中"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_State3"
  content: "已淘汰"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Sec"
  content: "秒"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_Min"
  content: "分"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_NullRoomList"
  content: "目前还没有可观战的比赛"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_NullAttentionList"
  content: "目前还没关注的玩家"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_AttentionPlayerNum"
  content: "{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_AttentionSuccessCallBack"
  content: "关注成功"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_AttentionfailCallBack1"
  content: "用户不存在"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_AttentionfailCallBack2"
  content: "超过关注人数"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_AttentionfailCallBack3"
  content: "已经关注过"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_AttentionfailCallBack4"
  content: "该玩家未报名参与本周赛事"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_AttentionfailCallBack5"
  content: "关注失败"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_DirectDelFollowPlayerCallBack1"
  content: "删除失败"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_DirectDelFollowPlayerCallBack2"
  content: "删除成功"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_GameInfo1"
  content: "第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_GameInfo2"
  content: "巅峰对决"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_GameInfo3"
  content: "未参赛"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_GameInfo4"
  content: "该玩家未参与本周赛事"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_GameInfo5"
  content: "该玩家已淘汰"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_NotAllowedParticipate"
  content: "导播不能报名参赛"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_EnterRoomInfo"
  content: "正在进入···"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_LeaveRoomFail"
  content: "不能退出"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_RankAwardGroupItem1"
  content: "第{0}-{1}名"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_Information1"
  content: "第{0}轮  比赛信息"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_EliminateMain1"
  content: "第{0}轮比赛，将在<EliminateYellow24>{1}</>后开始"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_EliminateMain2"
  content: "我的成绩：第<EliminateYellow42>{0}</>名"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_EliminateMain3"
  content: "{0}场比赛"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_EliminateMain4"
  content: "我的成绩：无"
  switch: 1
}
rows {
  id: "UI_Competition_Broadcast_MaxOB"
  content: "本场比赛观战导播席位已满，请选择其他比赛进行导播"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_EnrollRes_InviteCodeInvalid"
  content: "邀请码错误，请重新输入"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignIn1"
  content: "报名人数已满,无法报名"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignIn2"
  content: "当前段位不满足，无法报名"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignIn3"
  content: "报名费用不足，无法报名"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignIn4"
  content: "即将开始"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignIn5"
  content: "{0}及以上"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignIn6"
  content: "当前专精点数不满足，无法报名"
  switch: 1
}
rows {
  id: "UI_Competition_Common_Day"
  content: "天"
  switch: 1
}
rows {
  id: "UI_Competition_Common_Hour"
  content: "小时"
  switch: 1
}
rows {
  id: "UI_Competition_Common_Minute"
  content: "分"
  switch: 1
}
rows {
  id: "UI_Competition_Common_Second"
  content: "秒"
  switch: 1
}
rows {
  id: "UI_Competition_Common_ReturnButton"
  content: "赛事信息"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignInSuc1"
  content: "<StartHintYellow23>{0}</>后开始比赛"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_SignInSuc2"
  content: "<StartHintYellow23>{0}</>开始积分赛"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_CannotWatch"
  content: "无法观战该比赛"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_CannotWatch_End"
  content: "该比赛已结束，无法观战"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_EliminationMainItem1"
  content: "等待公布"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_CannotWatch_NotReady"
  content: "房间还未准备好，稍等一下。"
  switch: 1
}
rows {
  id: "UI_Competition_PortalItem1"
  content: "狼人之夜"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Tips"
  content: "积分赛事说明文本积分赛事说明文本积分赛事说明文本积分赛事说明文本积分赛事说明文本积分赛事说明文本积分赛事说明文本"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text1"
  content: "{0} 分"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text2"
  content: "{0}/{1} 场"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text3"
  content: "我的排名：<PointsCompetitionYellow21>第{0}名</>"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text4"
  content: "我的排名：<PointsCompetitionGray21>无</>"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text5"
  content: "未完成"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text6"
  content: "我的战绩"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text7"
  content: "玩家战绩"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text8"
  content: "当前处于匹配中，无法返回"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text9"
  content: "{0} 场"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_Text10"
  content: "我的排名：<PointsCompetitionGray21>未进入决赛</>"
  switch: 1
}
rows {
  id: "UI_Competition_Main_Season_Week"
  content: "第{0}届-第{1}周"
  switch: 1
}
rows {
  id: "UI_Competition_Main_Season"
  content: "第{0}届"
  switch: 1
}
rows {
  id: "UI_Competition_Integral_SeasonEndTimeLabel_0"
  content: "本届赛事结束时，赛事奖杯与专属奖励都将重置刷新。"
  switch: 1
}
rows {
  id: "UI_Competition_Integral_SeasonEndTimeLabel_1"
  content: "本届结束时间："
  switch: 1
}
rows {
  id: "UI_Competition_Main_SeasonWeek"
  content: "第{0}届第{1}周"
  switch: 1
}
rows {
  id: "UI_Competition_Notice_Suspension"
  content: "线上暂时休赛，线下决赛后将重新开启。"
  switch: 1
}
rows {
  id: "UI_Competition_Main_Week_WX"
  content: "第{0}周-微信"
  switch: 1
}
rows {
  id: "UI_Competition_Main_Week_QQ"
  content: "第{0}周-QQ"
  switch: 1
}
rows {
  id: "UI_Competition_CannotMatchInTeam"
  content: "组队时无法进行匹配"
  switch: 1
}
rows {
  id: "UI_Competition_OfflineChampion"
  content: "线下赛总冠军"
  switch: 1
}
rows {
  id: "UI_Competition_OfflineChampion_Title"
  content: "第{0}届-冠军之星"
  switch: 1
}
rows {
  id: "UI_Competition_OfflineChampion_Button"
  content: "冠军荣耀"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInfoRegister_Describe"
  content: "亲爱的星宝，祝贺您晋级本周巅峰对决32强！\n为方便赛事联络，请留下您的真实姓名与手机号进行报名。"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInfoRegister_ErrorPhoneNumber"
  content: "请输入正确的手机号码"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInfoRegister_Register"
  content: "登记"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInfoRegister_Replace"
  content: "更换"
  switch: 1
}
rows {
  id: "UI_PointsCompetition_EliminateMainGameTips999"
  content: "比赛结束后，根据您的最终排名，奖励将通过邮件进行发放，请注意查收。"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInfoRegister_ReplaceTip"
  content: "请重新登记您的姓名和手机号"
  switch: 1
}
rows {
  id: "UI_Competition_Main_Name"
  content: "赛事"
  switch: 1
}
rows {
  id: "UI_Competition_Main_StartSoon"
  content: "即将开赛"
  switch: 1
}
rows {
  id: "UI_Competition_Main_FinalRound"
  content: "决赛"
  switch: 1
}
rows {
  id: "UI_Preparations_MapItem"
  content: "解锁条件"
  switch: 1
}
rows {
  id: "UI_Preparations_MapItem1"
  content: "已获胜{0}次"
  switch: 1
}
rows {
  id: "UI_Preparations_View1"
  content: "等待队长开始游戏"
  switch: 1
}
rows {
  id: "UI_Preparations_View2"
  content: "排位模式仅限单人匹配"
  switch: 1
}
rows {
  id: "UI_Preparations_View3"
  content: "请先下载玩法资源包"
  switch: 1
}
rows {
  id: "UI_Preparations_View4"
  content: "该地图只能单人匹配"
  switch: 1
}
rows {
  id: "UI_Preparations_View5"
  content: "当前队伍人数不满足该模式要求"
  switch: 1
}
rows {
  id: "UI_Preparations_View6"
  content: "有队员未解锁该模式"
  switch: 1
}
rows {
  id: "UI_Preparations_View7"
  content: "已切换"
  switch: 1
}
rows {
  id: "UI_Preparations_IdentitySelectItem1"
  content: "组队不可选择身份"
  switch: 1
}
rows {
  id: "UI_Preparations_IdentitySelectItem2"
  content: "匹配中无法修改身份"
  switch: 1
}
rows {
  id: "UI_Preparations_MapSelectItem1"
  content: "匹配中无法修改地图"
  switch: 1
}
rows {
  id: "UI_Preparations_MapSelectItem2"
  content: "每日{0}限时开启"
  switch: 1
}
rows {
  id: "UI_Preparations_MapSelectItem3"
  content: "{0}暂未开启，敬请期待"
  switch: 1
}
rows {
  id: "UI_Preparations_MapSelectItem4"
  content: "排位赛"
  switch: 1
}
rows {
  id: "UI_Preparations_Room1"
  content: "等待中无法前往"
  switch: 1
}
rows {
  id: "UI_Preparations_MasterRoad1"
  content: "大师之路"
  switch: 1
}
rows {
  id: "UI_Preparations_MasterRoad2"
  content: "赢取专精点数，解锁奖励，努力成为一代大师吧！"
  switch: 1
}
rows {
  id: "UI_Preparations_MasterRoad3"
  content: "你的总点数是每个身份的专精点数之和。"
  switch: 1
}
rows {
  id: "UI_Preparations_MasterRoad4"
  content: "排名"
  switch: 1
}
rows {
  id: "UI_Preparations_MasterRoad5"
  content: "未入榜"
  switch: 1
}
rows {
  id: "UI_Preparations_MasterRoad6"
  content: "{0}万"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate1"
  content: "报告动画"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate2"
  content: "攻击动画"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate3"
  content: "会议表情"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate4"
  content: "互动道具"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate5"
  content: "确认要购买<DecorateYellow32>{0}</>吗？"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate6"
  content: "特殊动画"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate7"
  content: "你可以使用新的报告、攻击或MVP动画。"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate8"
  content: "使用后，你在游戏中报告、攻击或获得MVP时将展示新的动画。"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate9"
  content: "在装饰界面中，你可以装配要使用的会议表情。"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate10"
  content: "<CompetitionYellow18>装配后</>，你可以在会议中<CompetitionYellow18>点击自己</>使用会议表情。"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate11"
  content: "在会议中，你可以点击别人来使用互动道具。"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate12"
  content: "谁是狼人-{0}"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate13"
  content: "{0}数量不足"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate14"
  content: "MVP动画"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate15"
  content: "伪装特效"
  switch: 1
}
rows {
  id: "UI_Preparations_Decorate16"
  content: "武器外观"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInformationRegistration1"
  content: "请输入姓名和手机号码"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInformationRegistration2"
  content: "请输入姓名"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInformationRegistration3"
  content: "请输入手机号码"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInformationRegistration4"
  content: "请输入正确的姓名和手机号码"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInformationRegistration5"
  content: "请输入正确的姓名"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInformationRegistration6"
  content: "请输入正确的手机号码"
  switch: 1
}
rows {
  id: "UI_Competition_PersonalInformationRegistration7"
  content: "登记信息将作为线下赛唯一的身份认证信息，请如实填写"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentityChildContent3"
  content: "不可选择未解锁的身份"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3_IdentityChildContent4"
  content: "秘境寻宝中无法使用该身份"
  switch: 1
}
rows {
  id: "UI_Preparations_Mall_Exchange1"
  content: "已达到最大购买数量，无法购买"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_TipTitle1"
  content: "专精点数"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip1"
  content: "在<CompetitionYellow18>休闲模式和排位模式</>中进行对局，将可能获得所用身份的专精点数。"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip2"
  content: "不同的阵营，计算专精点数的规则是不同的。"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip3"
  content: "累计专精点数，可以在<CompetitionYellow18>大师之路</>中解锁各种奖励。"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_TipTitle2"
  content: "身份解锁"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip4"
  content: "你可以在对局中随机或自选到已解锁的身份。"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip5"
  content: "你<CompetitionYellow18>无法随机或自选到未解锁的身份</>。"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip6"
  content: "查看未解锁的身份，可以看到相应的获取途径。"
  switch: 1
}
rows {
  id: "UI_Custom_Text1"
  content: "彩蛋局"
  switch: 1
}
rows {
  id: "UI_Custom_Text2"
  content: "彩蛋局活动已结束"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3Mall_Common_MoneyNotEnough2"
  content: "您的<MessageTipYellow>{0}</>不足"
  switch: 1
}
rows {
  id: "UI_Preparations_TVTips"
  content: "组队中无法观看直播哦~"
  switch: 1
}
rows {
  id: "UI_Preparations_TVTips1"
  content: "活动已结束"
  switch: 1
}
rows {
  id: "UI_Preparations_TVTips2"
  content: "直播已结束"
  switch: 1
}
rows {
  id: "UI_NR3E0_InLevelFinalAccount1"
  content: "专精({0})"
  switch: 1
}
rows {
  id: "UI_Competition_Honour1"
  content: "休闲比赛"
  switch: 1
}
rows {
  id: "UI_Competition_Honour2"
  content: "按时间排序"
  switch: 1
}
rows {
  id: "UI_Competition_Honour3"
  content: "按成绩排序"
  switch: 1
}
rows {
  id: "UI_NR3E_MallItemTag_151"
  content: "折扣"
  switch: 1
}
rows {
  id: "UI_Custom_Text3"
  content: "彩蛋局({0})"
  switch: 1
}
rows {
  id: "UI_Custom_Text4"
  content: "随机"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip3_1"
  content: "专精点数是<CompetitionYellow18>永久</>的，不会随赛季重置或归零。"
  switch: 1
}
rows {
  id: "UI_Preparations_TVTips3"
  content: "匹配中，暂无法前往"
  switch: 1
}
rows {
  id: "UI_Preparations_WolfFeedBack1"
  content: "今日反馈次数已达上限"
  switch: 1
}
rows {
  id: "UI_Preparations_WolfFeedBack2"
  content: "请输入您想要反馈的内容"
  switch: 1
}
rows {
  id: "UI_Preparations_WolfFeedBack3"
  content: "提交成功，感谢您的反馈"
  switch: 1
}
rows {
  id: "UI_Preparations_IdentityItemDoubleDes"
  content: "双人模式不可选择身份"
  switch: 1
}
rows {
  id: "UI_Preparations_TVTips5"
  content: "直播"
  switch: 1
}
rows {
  id: "UI_Preparations_TVTips4"
  content: "活动"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice1"
  content: "主题季"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice2"
  content: "当前主题季：<Gride>头号侦探（第二季）</>。来一场推理对决，寻找真相吧！"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice3"
  content: "在每个主题季里，你可以游玩到富有特色的主题内容！"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice4"
  content: "新模式：秘境寻宝"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice5"
  content: "寻宝小队集结，前往神秘的秘境岛！这里埋藏着各种宝物，还有位置的险境。\n大家的任务是搜寻并上交各种宝物，出发吧！"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice6"
  content: "主题机制：侦探笔记"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice7"
  content: "一批侦探在各处调查狼人案件。你可以在场景中寻找到他们的笔记，笔记里可能会有一些线索与信息。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice8"
  content: "同时，还有主题季专属身份(赛季中期开放)"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice9"
  content: "平民阵营，名侦探可以调查一名被淘汰的玩家，从而知道是谁是凶手。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice10"
  content: "狼人阵营，黑衣狼可以使所有人都变成黑衣人，持续一段时间。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice11"
  content: "中立阵营，密探需要在会议中猜测2名指定的玩家身份，如果猜测成功，则获得胜利。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_WolfVillageNotice12"
  content: "主题季身份默认解锁，仅在主题季期间开放，且不会获得专精点数。"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_IdentityItemContent1"
  content: "共享"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E_IdentityItemContent2"
  content: "新获得"
  switch: 1
}
rows {
  id: "UI_Preparations_NR3E3Mall_Common_LimitHaveItem"
  content: "您已拥有此物品，无法购买"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip7"
  content: "每个主题季中，会开放一些专属身份。这些身份仅在该主题季期间可以选取使用。"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_Tip8"
  content: "主题季身份无法获取专精点数"
  switch: 1
}
rows {
  id: "UI_Preparations_Vocation_TipTitle3"
  content: "主题季身份"
  switch: 1
}
rows {
  id: "UI_Preparations_VocationItem_Season"
  content: "主题季专属：{0}"
  switch: 1
}
rows {
  id: "UI_Preparations_VocationItem_Season01"
  content: "奇趣派对"
  switch: 1
}
rows {
  id: "UI_Preparations_DisguiseEffect"
  content: "伪装者特效"
  switch: 1
}
rows {
  id: "UI_Preparations_Weapon"
  content: "武器装扮"
  switch: 1
}
rows {
  id: "UI_Preparations_VocationItem_Season02"
  content: "头号侦探"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity1"
  content: "自选身份"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity2"
  content: "待领取"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity3"
  content: "完成对局可领取"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity4"
  content: "从以下身份中选择1个奖励"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity5"
  content: "从以上奖励中选择1个领取"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity6"
  content: "自选"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity7"
  content: "限时攻击动画"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity8"
  content: "限时报告动画"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity9"
  content: "永久身份"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity10"
  content: "第"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity11"
  content: "天"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity12"
  content: "每日一局，"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity13"
  content: "精美动画、永久身份"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity14"
  content: "等你来领"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity15"
  content: "领取"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity16"
  content: "近期更新"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity17"
  content: "回归任务奖励"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity18"
  content: "回归"
  switch: 1
}
rows {
  id: "UI_Preparations_ReturnActivity19"
  content: "送永久身份"
  switch: 1
}
rows {
  id: "UI_Preparations_LimitButType1"
  content: "本日限购："
  switch: 1
}
rows {
  id: "UI_Preparations_LimitButType2"
  content: "本周限购："
  switch: 1
}
rows {
  id: "UI_Preparations_LimitButType3"
  content: "本月限购："
  switch: 1
}
rows {
  id: "UI_Preparations_LimitButType4"
  content: "本年限购："
  switch: 1
}
rows {
  id: "UI_Preparations_LimitButType5"
  content: "赛季限购："
  switch: 1
}
rows {
  id: "UI_Preparations_LimitButType6"
  content: "永久限购："
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_Exchange"
  content: "每购买珍宝值{0}点，可获赠狼人币{1}个"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_Exchange1"
  content: "距离下次升级还需："
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_Exchange2"
  content: "是否消耗{0}{1}购买<MantelCardCount>{2}</>{3}点"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem1"
  content: "珍宝"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem2"
  content: "珍宝等级"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem3"
  content: "提升珍宝等级，解锁稀世珍宝"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem4"
  content: "已达最高等级"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem5"
  content: "购买经验"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem6"
  content: "每周限购"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem7"
  content: "解锁新珍宝"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem8"
  content: "点击屏幕可继续"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem9"
  content: "分享"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem10"
  content: "已解锁"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem11"
  content: "未解锁"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem12"
  content: "今日已领取"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem13"
  content: "使用"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem14"
  content: "取消使用"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem15"
  content: "使用中"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem16"
  content: "珍宝等级{0}时解锁"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem17"
  content: "预览"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem18"
  content: "每拥有一个<TreasureYellow18>报告、攻击或MVP动画</>,可增加大量珍宝值。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem19"
  content: "消耗星钻购买<TreasureYellow18>互动道具、身份卡、阵营卡、礼包、月卡</>时，可获得珍宝值。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem20"
  content: "也可以消耗星钻<TreasureYellow18>直接购买</>一些限量的珍宝值。"
  switch: 1
}
rows {
  id: "UI_Preparations_VocationItem_Season03"
  content: "头号侦探"
  switch: 1
}
rows {
  id: "UI_Preparations_VocationItem_Season04"
  content: "超凡特攻"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem21"
  content: "拥有一个非默认动画后解锁珍宝系统。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_Share"
  content: "共享"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_Shared"
  content: "本月已共享"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedSucc"
  content: "<ShareYellow21>{0}</>获得了你{1}的{2}使用权。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedReceive"
  content: "你获得了<ShareYellow26>{0}</>{1}<ShareYellow26>{2}</>的使用权。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareType_Ani"
  content: "所拥有的各类动画"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareType_Vocation"
  content: "所有身份"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedSuccTip"
  content: "共享成功！"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedSucc_Btn"
  content: "告诉TA"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedFrom"
  content: "来自：{0}"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedTime"
  content: "共享时间：{0}"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedRemainTime"
  content: "剩余时间：{0}"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem22"
  content: "本周已领取"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Main1"
  content: "报名时间（{0}）"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Main2"
  content: "比赛时间（{0}）"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Main3"
  content: "荣誉展示（{0}）"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Main4"
  content: "比赛信息"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Main5"
  content: "荣誉展示"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMain_Tip1"
  content: "你可以把{0}<TreasureYellow22>共享给好友</>使用"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMain_Tip2"
  content: "每个月你可以<TreasureYellow22>选择3名好友</>进行{0}，选择后不可更换，次月重置"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMain_Tip3"
  content: "每次共享，该好友可以<TreasureYellow22>获得{0}的{1}</>使用权"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMain_Ani"
  content: "所拥有的报告、攻击、MVP动画"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMain_Vocation"
  content: "所有已解锁的身份"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMainType_Ani"
  content: "共享动画"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMainType_Vocation"
  content: "共享身份"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMainTitle_Ani"
  content: "动画共享"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMainTitle_Vocation"
  content: "身份共享"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareMainTitle"
  content: "把你的{0}给好友吧"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareReceive"
  content: "获得{0}"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareReceive_Vocation_Chat"
  content: "你共享的身份我已经收到啦，十分感谢你的馈赠！"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareReceive_Ani_Chat"
  content: "你共享的动画我已经收到啦，十分感谢你的馈赠！"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedSucc_Vocation_Chat"
  content: "我已经将我的全部身份都共享给你啦，快来谁是狼人的身份专精里看看吧。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedSucc_Ani_Chat"
  content: "我已经将我的全部动画都共享给你啦，快来谁是狼人的装饰系统里看看吧。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareFail1"
  content: "已选择共享好友，不可重新选择。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_ShareTimeEnd"
  content: "已结束"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch1"
  content: "不满足报名条件"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch2"
  content: "报名费用不足"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch3"
  content: "报名中"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch4"
  content: "比赛中"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch5"
  content: "{0} 点及以上"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch6"
  content: "[谁是狼人]每周精英赛-狼人之夜。\n报名时间：{0}至{1};\n积分赛时间：{2}至{3};\n周决赛时间：{4}至比赛结束。\n更多信息请查看[<Yellow17>赛事说明</>]。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch7"
  content: "报名阶段"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_InLevelMatch8"
  content: "条件满足其中1个，便可报名参加"
  switch: 1
}
rows {
  id: "UI_Competition_EnterRoomTip_Elimination_Feature"
  content: "<MantelCardCount>狼人之夜[积分赛]</>已经开始，是否前往？"
  switch: 1
}
rows {
  id: "UI_Competition_EnterRoomTip_Peak_Feature"
  content: "<MantelCardCount>狼人之夜[决赛]</>即将开始，是否前往？\n<Red28>无故弃赛，将有禁赛处罚。</>"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedTip"
  content: "该好友已处于共享中"
  switch: 1
}
rows {
  id: "UI_Competition_ExitGameTip1"
  content: "确认退出游戏？"
  switch: 1
}
rows {
  id: "UI_Competition_ExitGameTip2"
  content: "中途退出游戏，无法获得比赛积分。"
  switch: 1
}
rows {
  id: "UI_Competition_ExitGameTip3"
  content: "离开"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RecordItem1"
  content: "获胜"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RecordItem2"
  content: "失败"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Matching1"
  content: "积分赛"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Matching2"
  content: "决赛"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Matching3"
  content: "积分赛前{0}名将进入本周决赛。\n积分相同时，获得MVP次数较多的排名靠前。\n积分与MVP次数均相同时，先达到该积分的排名靠前。"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Matching4"
  content: "积分赛前{0}名玩家将连续进行{1}场比赛，决出本周冠军以及最终排名。"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Matching5"
  content: "您的{0}场积分赛已完成，请等待最终排名的结算；积分赛<Yellow17>前{1}名</>将进入本周决赛"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Matching6"
  content: "赛事：狼人之夜"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_Matching7"
  content: "比赛结束后，根据您的排名，奖励将通过邮件进行发放，请注意查收。"
  switch: 1
}
rows {
  id: "UI_NR3ECustonCompetition_Rule1"
  content: "禁止在比赛中或晋级决赛后，无故弃赛"
  switch: 1
}
rows {
  id: "UI_NR3ECustonCompetition_Rule2"
  content: "禁止场外获取信息或默契行为以及骂人等不文明行为"
  switch: 1
}
rows {
  id: "UI_NR3ECustonCompetition_Rule3"
  content: "禁止使用破解设备、越狱设备参与比赛"
  switch: 1
}
rows {
  id: "UI_NR3ECustonCompetition_Rule4"
  content: "违规者将受到"
  switch: 1
}
rows {
  id: "UI_NR3ECustonCompetition_Rule5"
  content: "禁赛处罚"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain1"
  content: "[谁是狼人]精英赛-狼人之夜盛大开放。群英荟萃，智勇对决，谁会成为今夜最闪亮的星呢?"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain2"
  content: "本赛事<Recruit23>每周举行1次</>，周二至周六进行报名，周六进行积分赛与决赛。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain3"
  content: "快快报名吧，丰富的奖励等着你呢。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain4"
  content: "报名时间: <Recruit23>{0}</>至<Recruit23>{1}</>。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain5"
  content: "报名要求: "
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain6"
  content: "报名费用: <Recruit23>{0}个狼人币</>。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain7"
  content: "积分赛时间: <Recruit23>{0}至{1}</>。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain8"
  content: "参赛玩家可进入比赛房间，进行最多<Recruit23>{0}场比赛</>，规则为<Recruit23>{1}人常规模式</>，<Recruit23>身份自选(3选1)</>。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain9"
  content: "每场比赛中，根据<Recruit23>胜负</>、<Recruit23>排名</>、<Recruit23>MVP</>，玩家可获得相应的比赛积分。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain10"
  content: "积分赛排名<Recruit23>前{0}</>名将进入本周的决赛。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain11"
  content: "决赛时间:<Recruit23>每{0}至比赛结束</>"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain12"
  content: "积分赛前{0}名玩家将连续进行{1}场比赛，规则为<Recruit23>{2}人常规模式</>，<Recruit23>身份自选(3选1)</>。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain13"
  content: "每场比赛中，根据<Recruit23>胜负</>、<Recruit23>排名</>、<Recruit23>MVP</>，玩家可获得相应的比赛积分。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain14"
  content: "根据决赛积分，将决出本周冠军以及决赛排名。"
  switch: 1
}
rows {
  id: "UI_NR3ECompetition_ExplainMain15"
  content: "[谁是狼人]排位赛达到<Recruit23>{0}</>，或身份专精总点数达到<Recruit23>{1}</><Recruit23>点</>，两个条件<Recruit23>满足其中之一</>便可报名。"
  switch: 1
}
rows {
  id: "UI_Preparations_TreasureSystem_SharedFail1"
  content: "对方版本过低，无法共享"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_PeekRanking1"
  content: "第{0}名-{1}"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView1"
  content: "战绩信息"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView2"
  content: "参赛次数"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView3"
  content: "最佳成绩"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView4"
  content: "平均排名"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView5"
  content: "擅长身份"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView6"
  content: "本届数据"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView7"
  content: "MVP数"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView8"
  content: "场均得分"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView9"
  content: "投狼率"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView10"
  content: "胜率"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView11"
  content: "场均排名"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView12"
  content: "场均淘汰"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView13"
  content: "决赛第{0}名"
  switch: 1
}
rows {
  id: "UI_Competition_RecordInfoView14"
  content: "{0}次"
  switch: 1
}
rows {
  id: "UI_NR3ECustomCompetition_RankAwardGroupItem1"
  content: "{0}-{1}名"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo1"
  content: "第{0}场"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo2"
  content: "等待上一轮比赛结束"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo3"
  content: "请稍候，房间人满后将自动开始比赛"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo4"
  content: "{0}人休闲"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo5"
  content: "确认要离开房间吗？"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo6"
  content: "请在比赛前返回，无故弃赛，将有禁赛处罚"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo7"
  content: "神秘的小狼人"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_RoomInfo8"
  content: "当前场次："
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_JoinRoomtips"
  content: "由于您在比赛中有违规行为，本次禁止报名!"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_JoinRoomtips1"
  content: "专精分不足!"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_AwardHint1"
  content: "有趣的狼人杀，就在《元梦之星》"
  switch: 1
}
rows {
  id: "UI_CustomCompetition_AwardHint2"
  content: "比赛成绩"
  switch: 1
}
rows {
  id: "UI_NR3E3_VocationMain_ExperienceTime"
  content: "剩余时间:{0}"
  switch: 1
}
rows {
  id: "UI_NR3E3Competition_HonourShow1"
  content: "直播还未开始，敬请期待"
  switch: 1
}
rows {
  id: "UI_Competition_CompetitionRoom"
  content: "赛事房间"
  switch: 1
}
rows {
  id: "UI_Competition_Room_SoloOnly_Tips"
  content: "比赛房间中禁止组队"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule1"
  content: "本次比赛中，游戏玩法规则为：{0}"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule2"
  content: "每场比赛，地图随机抽取：{0}"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule3"
  content: "获胜得分：{0}"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule4"
  content: "MVP得分：{0}"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule5"
  content: "排名得分："
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule6"
  content: "本局MVP({0}分)"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule7"
  content: "平民阵营"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule8"
  content: "狼人阵营"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule9"
  content: "中立玩家"
  switch: 1
}
rows {
  id: "UI_CustonCompetition_NR3E3MatchingRule10"
  content: "第{0}名（{1}分）"
  switch: 1
}
rows {
  id: "UI_Preparations_MapSelect1"
  content: "谁是狼人"
  switch: 1
}
