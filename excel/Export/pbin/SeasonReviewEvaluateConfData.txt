com.tencent.wea.xlsRes.table_SeasonReviewEvaluateConfData
excel/xls/S_赛季.xlsx sheet:赛季回顾评价
rows {
  id: 1
  mode: 1
  Label: "新星崛起"
  des: "一颗新星正在崛起~"
  modePriority: 75
  type: 2
  identity: "新星选手"
  identityPriority: 75
  conditionDes: "夺冠次数高于10次"
  data: 1
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 2
  mode: 1
  Label: "冠军常客"
  des: "你已经是个老手了~"
  modePriority: 52
  type: 2
  identity: "绝世高手"
  identityPriority: 52
  conditionDes: "夺冠次数高于50次"
  data: 1
  dataMax: 499.0
  dataMin: 50.0
}
rows {
  id: 3
  mode: 1
  Label: "传奇选手"
  des: "传说中的王者就是你！"
  modePriority: 27
  type: 2
  identity: "超凡大师"
  identityPriority: 27
  conditionDes: "夺冠次数高于500次"
  data: 1
  dataMin: 500.0
}
rows {
  id: 4
  mode: 1
  Label: "零冠成就"
  des: "还是快乐最重要啦~"
  modePriority: 2
  type: 1
  identity: "一胜难求"
  identityPriority: 2
  conditionDes: "夺冠次数为0"
  data: 1
  dataMax: 0.0
  dataMin: 0.0
}
rows {
  id: 5
  mode: 1
  Label: "初出茅庐"
  des: "星之旅正式开始~"
  modePriority: 76
  type: 1
  identity: "初出茅庐"
  identityPriority: 76
  conditionDes: "游戏场数高于10场"
  data: 2
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 6
  mode: 1
  Label: "资深玩家"
  des: "已经是星世界的熟面孔了！"
  modePriority: 53
  type: 1
  identity: "渐入佳境"
  identityPriority: 53
  conditionDes: "游戏场数高于50场"
  data: 2
  dataMax: 99.0
  dataMin: 50.0
}
rows {
  id: 7
  mode: 1
  Label: "肝帝"
  des: "你的坚持闪耀全场！"
  modePriority: 28
  type: 2
  identity: "忠实玩家"
  identityPriority: 28
  conditionDes: "游戏场数高于100场"
  data: 2
  dataMin: 100.0
}
rows {
  id: 8
  mode: 1
  Label: "佛系玩家"
  des: "轻松游戏也是一种态度~"
  modePriority: 3
  type: 1
  identity: "佛系"
  identityPriority: 3
  conditionDes: "游戏场数低于5场"
  data: 2
  dataMax: 5.0
  dataMin: 1.0
}
rows {
  id: 9
  mode: 1
  Label: "奖杯收藏家"
  des: "实力与努力并存！"
  modePriority: 59
  type: 1
  identity: "初绽光芒"
  identityPriority: 59
  conditionDes: "总奖杯数高于10000"
  data: 5
  dataMax: 19999.0
  dataMin: 10000.0
}
rows {
  id: 10
  mode: 1
  Label: "星杯闪耀"
  des: "闪耀星世界！"
  modePriority: 34
  type: 1
  identity: "极致辉煌"
  identityPriority: 34
  conditionDes: "总奖杯数高于20000"
  data: 5
  dataMax: 29999.0
  dataMin: 20000.0
}
rows {
  id: 11
  mode: 1
  Label: "星耀玩家"
  des: "登顶元梦之巅！"
  modePriority: 9
  type: 2
  identity: "超新星"
  identityPriority: 9
  conditionDes: "总奖杯数高于30000"
  data: 5
  dataMin: 30000.0
}
rows {
  id: 12
  mode: 1
  Label: "独行侠"
  des: "真正的高手总是独自面对"
  modePriority: 5
  type: 2
  identity: "独行侠"
  identityPriority: 5
  conditionDes: "组队次数为0"
  data: 7
  dataMax: 0.0
  dataMin: 0.0
}
rows {
  id: 13
  mode: 1
  Label: "决战时刻"
  des: "逆风翻盘是你的强项！"
  modePriority: 58
  type: 1
  identity: "稳进决赛"
  identityPriority: 58
  conditionDes: "终局次数高于20次"
  data: 6
  dataMin: 20.0
}
rows {
  id: 14
  mode: 1
  Label: "全能玩家"
  des: "你就是元梦多面手"
  modePriority: 31
  type: 2
  identity: "多面手"
  identityPriority: 31
  conditionDes: "玩过超过5种玩法模式"
  data: 10
  dataMax: 9.0
  dataMin: 5.0
}
rows {
  id: 15
  mode: 1
  Label: "模式大师"
  des: "行走的百科全书"
  modePriority: 6
  type: 1
  identity: "多才多艺"
  identityPriority: 6
  conditionDes: "玩过超过10种玩法模式"
  data: 10
  dataMin: 10.0
}
rows {
  id: 16
  mode: 1
  Label: "元梦执行"
  des: "快乐就是这样简单"
  modePriority: 999
  type: 2
  identity: "星宝"
  identityPriority: 999
  conditionDes: "快乐就是这样简单"
}
rows {
  id: 17
  mode: 2
  Label: "时尚萌新"
  des: "开始闪耀之旅"
  modePriority: 85
  type: 1
  identity: "风尚初现"
  identityPriority: 85
  conditionDes: "赛季时尚分高于100"
  data: 11
  dataMax: 2999.0
  dataMin: 100.0
}
rows {
  id: 18
  mode: 2
  Label: "潮流新锐"
  des: "穿搭界的风向标"
  modePriority: 79
  type: 1
  identity: "品味出众"
  identityPriority: 79
  conditionDes: "赛季时尚分高于3000"
  data: 11
  dataMax: 4999.0
  dataMin: 3000.0
}
rows {
  id: 19
  mode: 2
  Label: "闪耀之星"
  des: "你的造型是星空的焦点！"
  modePriority: 56
  type: 1
  identity: "引领风潮"
  identityPriority: 56
  conditionDes: "赛季时尚分高于5000"
  data: 11
  dataMax: 9999.0
  dataMin: 5000.0
}
rows {
  id: 20
  mode: 2
  Label: "梦幻典藏"
  des: "定义元梦时尚法则~"
  modePriority: 32
  type: 2
  identity: "造型专家"
  identityPriority: 32
  conditionDes: "赛季时尚分高于10000"
  data: 11
  dataMax: 19999.0
  dataMin: 10000.0
}
rows {
  id: 21
  mode: 2
  Label: "元梦顶流"
  des: "整个星世界都在模仿你！"
  modePriority: 7
  type: 2
  identity: "流行指挥官"
  identityPriority: 7
  conditionDes: "赛季时尚分高于20000"
  data: 11
  dataMin: 20000.0
}
rows {
  id: 22
  mode: 2
  Label: "许愿常客"
  des: "星空听见你的心愿啦~"
  modePriority: 51
  type: 1
  identity: "轻奢体验"
  identityPriority: 51
  conditionDes: "祈愿次数超过100次"
  data: 14
  dataMax: 999.0
  dataMin: 100.0
}
rows {
  id: 23
  mode: 2
  Label: "千愿之星"
  des: "虔诚的许愿者~"
  modePriority: 26
  type: 1
  identity: "管不住手"
  identityPriority: 26
  conditionDes: "祈愿次数超过1000次"
  data: 14
  dataMax: 1999.0
  dataMin: 1000.0
}
rows {
  id: 24
  mode: 2
  Label: "天命之子"
  des: "你已成为星愿的化身！"
  modePriority: 1
  type: 1
  identity: "财力自由"
  identityPriority: 1
  conditionDes: "祈愿次数超过2000次"
  data: 14
  dataMin: 2000.0
}
rows {
  id: 25
  mode: 2
  Label: "简单着装"
  des: "慢慢充实衣橱吧~"
  modePriority: 77
  type: 1
  identity: "初级穿搭"
  identityPriority: 77
  conditionDes: "获得装扮数超过1个"
  data: 13
  dataMax: 19.0
  dataMin: 5.0
}
rows {
  id: 26
  mode: 2
  Label: "百变新星"
  des: "每天都有新搭配~"
  modePriority: 54
  type: 1
  identity: "品牌粉丝"
  identityPriority: 54
  conditionDes: "获得装扮数超过10个"
  data: 13
  dataMax: 34.0
  dataMin: 20.0
}
rows {
  id: 27
  mode: 2
  Label: "穿搭自由"
  des: "你的衣橱让人羡慕！"
  modePriority: 29
  type: 1
  identity: "时装策展"
  identityPriority: 29
  conditionDes: "获得装扮数超过30个"
  data: 13
  dataMax: 99.0
  dataMin: 35.0
}
rows {
  id: 28
  mode: 2
  Label: "秀场主宰"
  des: "打造传奇时尚宝库！"
  modePriority: 4
  type: 2
  identity: "高定收藏家"
  identityPriority: 4
  conditionDes: "获得装扮数超过100个"
  data: 13
  dataMin: 100.0
}
rows {
  id: 29
  mode: 2
  Label: "时尚入门"
  des: "初入时尚界"
  modePriority: 999
  type: 1
  identity: "时尚"
  identityPriority: 999
  conditionDes: "初入时尚界"
}
rows {
  id: 30
  mode: 3
  Label: "社交新星"
  des: "朋友圈逐渐壮大"
  modePriority: 86
  type: 1
  identity: "捕捉缘分"
  identityPriority: 86
  conditionDes: "好友数超过10人"
  data: 19
  dataMax: 29.0
  dataMin: 10.0
}
rows {
  id: 31
  mode: 3
  Label: "信号满格"
  des: "走到哪都是焦点"
  modePriority: 81
  type: 1
  identity: "制造话题"
  identityPriority: 81
  conditionDes: "好友数超过30人"
  data: 19
  dataMax: 49.0
  dataMin: 30.0
}
rows {
  id: 32
  mode: 3
  Label: "社牛认证"
  des: "你的圈子闪闪发光"
  modePriority: 61
  type: 1
  identity: "永不冷场"
  identityPriority: 61
  conditionDes: "好友数超过50人"
  data: 19
  dataMax: 239.0
  dataMin: 50.0
}
rows {
  id: 33
  mode: 3
  Label: "社交天花板"
  des: "每天都是欢乐派对！"
  modePriority: 36
  type: 1
  identity: "星光汇聚"
  identityPriority: 36
  conditionDes: "好友数超过240人"
  data: 19
  dataMin: 240.0
}
rows {
  id: 34
  mode: 3
  Label: "社恐勿扰"
  des: "独处也是一种境界"
  modePriority: 11
  type: 2
  identity: "社恐患者"
  identityPriority: 11
  conditionDes: "没有新增好友"
  data: 19
  dataMax: 0.0
  dataMin: 0.0
}
rows {
  id: 35
  mode: 3
  Label: "知心好友"
  des: "友谊的小船稳稳的"
  modePriority: 80
  type: 2
  identity: "知心好友"
  identityPriority: 80
  conditionDes: "亲密好友数量超过1人"
  data: 10
  dataMax: 2.0
  dataMin: 1.0
}
rows {
  id: 36
  mode: 3
  Label: "挚友成群"
  des: "遇到属于你的美好"
  modePriority: 60
  type: 1
  identity: "挚友多多"
  identityPriority: 60
  conditionDes: "亲密好友数量超过3人"
  data: 20
  dataMax: 9.0
  dataMin: 3.0
}
rows {
  id: 37
  mode: 3
  Label: "灵魂伴侣"
  des: "走到哪里都有一群朋友！"
  modePriority: 35
  type: 2
  identity: "灵魂伴侣"
  identityPriority: 35
  conditionDes: "亲密好友数量超过10人"
  data: 20
  dataMin: 10.0
}
rows {
  id: 38
  mode: 3
  Label: "高冷玩家"
  des: "独享快乐，也挺好"
  modePriority: 10
  type: 1
  identity: "习惯独行"
  identityPriority: 10
  conditionDes: "亲密好友数量为0"
  data: 20
  dataMax: 0.0
  dataMin: 0.0
}
rows {
  id: 39
  mode: 3
  Label: "心意萌芽"
  des: "小小的礼物，温暖的开始"
  modePriority: 67
  type: 2
  identity: "贴心好友"
  identityPriority: 67
  conditionDes: "赠礼次数超过10次"
  data: 22
  dataMax: 99.0
  dataMin: 10.0
}
rows {
  id: 40
  mode: 3
  Label: "暖心伙伴"
  des: "朋友间的温暖使者"
  modePriority: 42
  type: 1
  identity: "礼尚往来"
  identityPriority: 42
  conditionDes: "赠礼次数超过100次"
  data: 22
  dataMax: 499.0
  dataMin: 100.0
}
rows {
  id: 41
  mode: 3
  Label: "慷慨之星"
  des: "你用礼物串联起整片星空"
  modePriority: 17
  type: 1
  identity: "倾心相赠"
  identityPriority: 17
  conditionDes: "赠礼次数超过500次"
  data: 22
  dataMin: 500.0
}
rows {
  id: 42
  mode: 3
  Label: "跨圈玩家"
  des: "在线求组，专业靠谱！"
  modePriority: 78
  type: 1
  identity: "乐于合作"
  identityPriority: 78
  conditionDes: "组队次数高于10"
  data: 7
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 43
  mode: 3
  Label: "最佳拍档"
  des: "多个小圈子的通行证"
  modePriority: 55
  type: 1
  identity: "亲密无间"
  identityPriority: 55
  conditionDes: "组队次数高于50"
  data: 7
  dataMax: 99.0
  dataMin: 50.0
}
rows {
  id: 44
  mode: 3
  Label: "团队之星"
  des: "你的社交名片闪闪发光"
  modePriority: 30
  type: 1
  identity: "默契满分"
  identityPriority: 30
  conditionDes: "组队次数高于100"
  data: 7
  dataMin: 100.0
}
rows {
  id: 45
  mode: 3
  Label: "家园新秀"
  des: "是谁悄悄来散步啦~"
  modePriority: 70
  type: 1
  identity: "开门迎客"
  identityPriority: 70
  conditionDes: "星家园被访问超过10次"
  data: 24
  dataMax: 19.0
  dataMin: 10.0
}
rows {
  id: 46
  mode: 3
  Label: "宝藏之家"
  des: "邻居最爱来你家开派对啦！"
  modePriority: 45
  type: 1
  identity: "热情好客"
  identityPriority: 45
  conditionDes: "星家园被访问超过20次"
  data: 24
  dataMax: 49.0
  dataMin: 20.0
}
rows {
  id: 47
  mode: 3
  Label: "明星宅邸"
  des: "你的家园已被列入星旅指南"
  modePriority: 20
  type: 1
  identity: "盛情邀约"
  identityPriority: 20
  conditionDes: "星家园被访问超过50次"
  data: 24
  dataMin: 50.0
}
rows {
  id: 48
  mode: 3
  Label: "派对初体验"
  des: "开启你的家园盛宴！"
  modePriority: 62
  type: 1
  identity: "派对新手"
  identityPriority: 62
  conditionDes: "星家园举办派对超过1次"
  data: 25
  dataMax: 9.0
  dataMin: 1.0
}
rows {
  id: 49
  mode: 3
  Label: "庆典大师"
  des: "三秒点燃全场气氛"
  modePriority: 37
  type: 1
  identity: "快乐焦点"
  identityPriority: 37
  conditionDes: "星家园举办派对超过10次"
  data: 25
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 50
  mode: 3
  Label: "狂欢主办"
  des: "用笑声与星光点亮你的家园"
  modePriority: 12
  type: 1
  identity: "嗨翻全场"
  identityPriority: 12
  conditionDes: "星家园举办派对超过50次"
  data: 25
  dataMin: 50.0
}
rows {
  id: 51
  mode: 3
  Label: "星宝家园"
  des: "独属我的小小家园"
  modePriority: 999
  type: 1
  identity: "家园小主"
  identityPriority: 999
  conditionDes: "独属我的小小家园"
}
rows {
  id: 52
  mode: 4
  Label: "小小农场主"
  des: "田园生活开始啦"
  modePriority: 87
  type: 2
  identity: "农场主"
  identityPriority: 87
  conditionDes: "农场等级大于10级"
  data: 29
  dataMax: 19.0
  dataMin: 10.0
}
rows {
  id: 53
  mode: 4
  Label: "茁壮成长"
  des: "传奇之路就在脚下"
  modePriority: 83
  type: 1
  identity: "欣欣向荣"
  identityPriority: 83
  conditionDes: "农场等级大于20级"
  data: 29
  dataMax: 49.0
  dataMin: 20.0
}
rows {
  id: 54
  mode: 4
  Label: "田园能手"
  des: "这才是土地该有的样子"
  modePriority: 66
  type: 2
  identity: "田园能手"
  identityPriority: 66
  conditionDes: "农场等级大于50级"
  data: 29
  dataMax: 79.0
  dataMin: 20.0
}
rows {
  id: 55
  mode: 4
  Label: "农业专家"
  des: "万物生长皆随你心"
  modePriority: 41
  type: 2
  identity: "农业专家"
  identityPriority: 41
  conditionDes: "农场等级大于80级"
  data: 29
  dataMax: 99.0
  dataMin: 80.0
}
rows {
  id: 56
  mode: 4
  Label: "神农降世"
  des: "农场界的传说"
  modePriority: 16
  type: 2
  identity: "神农"
  identityPriority: 16
  conditionDes: "农场等级大于100级"
  data: 29
  dataMin: 100.0
}
rows {
  id: 57
  mode: 4
  Label: "勤劳小蜜蜂"
  des: "把荒地变成小菜园"
  modePriority: 74
  type: 1
  identity: "勤劳"
  identityPriority: 74
  conditionDes: "活跃天数大于10天"
  data: 39
  dataMax: 19.0
  dataMin: 10.0
}
rows {
  id: 58
  mode: 4
  Label: "深耕者"
  des: "已经摸透农场生物的小脾气"
  modePriority: 50
  type: 1
  identity: "风雨无阻"
  identityPriority: 50
  conditionDes: "活跃天数大于20天"
  data: 39
  dataMax: 29.0
  dataMin: 20.0
}
rows {
  id: 59
  mode: 4
  Label: "四季行者"
  des: "土地记得你的坚持"
  modePriority: 25
  type: 1
  identity: "全勤"
  identityPriority: 25
  conditionDes: "活跃天数大于30天"
  data: 39
  dataMin: 30.0
}
rows {
  id: 60
  mode: 4
  Label: "小小收割机"
  des: "仓库满满当当"
  modePriority: 71
  type: 1
  identity: "欢乐丰收"
  identityPriority: 71
  conditionDes: "大丰收次数超过50"
  data: 27
  dataMax: 99.0
  dataMin: 50.0
}
rows {
  id: 61
  mode: 4
  Label: "灌溉大师"
  des: "时光在土壤中萌芽"
  modePriority: 46
  type: 2
  identity: "丰收导师"
  identityPriority: 46
  conditionDes: "大丰收次数超过100"
  data: 27
  dataMax: 499.0
  dataMin: 100.0
}
rows {
  id: 62
  mode: 4
  Label: "丰饶之主"
  des: "收获已成为呼吸般的习惯"
  modePriority: 21
  type: 2
  identity: "农场传奇"
  identityPriority: 21
  conditionDes: "大丰收次数超过500"
  data: 27
  dataMin: 500.0
}
rows {
  id: 63
  mode: 4
  Label: "萌芽投资"
  des: "这点农场币，只是洒洒水啦"
  modePriority: 68
  type: 2
  identity: "农场富豪"
  identityPriority: 68
  conditionDes: "消耗农场币超过1000万"
  data: 33
  dataMax: 100000000.0
  dataMin: 10000000.0
}
rows {
  id: 64
  mode: 4
  Label: "播种自由"
  des: "想买什么买什么"
  modePriority: 43
  type: 2
  identity: "农场大亨"
  identityPriority: 43
  conditionDes: "消耗农场币超过1亿"
  data: 33
  dataMax: 10000000000.0
  dataMin: 100000000.0
}
rows {
  id: 65
  mode: 4
  Label: "农场经济学"
  des: "原来百亿只是些数字而已"
  modePriority: 18
  type: 2
  identity: "农场首富"
  identityPriority: 18
  conditionDes: "消耗农场币超过100亿"
  data: 33
  dataMin: 10000000000.0
}
rows {
  id: 66
  mode: 4
  Label: "路不拾遗"
  des: "路边的野花不要采"
  modePriority: 82
  type: 1
  identity: "安分守己"
  identityPriority: 82
  conditionDes: "偷菜次数为0"
  data: 38
  dataMax: 0.0
  dataMin: 0.0
}
rows {
  id: 67
  mode: 4
  Label: "勤拿致富"
  des: "富贵险中求"
  modePriority: 63
  type: 1
  identity: "勤拿致富"
  identityPriority: 63
  conditionDes: "偷菜次数大于10次"
  data: 38
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 68
  mode: 4
  Label: "农场快闪"
  des: "你的菜我就收下了"
  modePriority: 38
  type: 2
  identity: "菜园行者"
  identityPriority: 38
  conditionDes: "偷菜次数大于50次"
  data: 38
  dataMax: 99.0
  dataMin: 50.0
}
rows {
  id: 69
  mode: 4
  Label: "农场圣手"
  des: "所到之处寸草不留"
  modePriority: 13
  type: 2
  identity: "农场圣手"
  identityPriority: 13
  conditionDes: "偷菜次数大于100次"
  data: 38
  dataMin: 100.0
}
rows {
  id: 70
  mode: 4
  Label: "种金时代"
  des: "第一桶金到手"
  modePriority: 69
  type: 1
  identity: "资源丰富"
  identityPriority: 69
  conditionDes: "获得农场币大于1000万"
  data: 24
  dataMax: 100000000.0
  dataMin: 10000000.0
}
rows {
  id: 71
  mode: 4
  Label: "农场大亨"
  des: "达成第一个小目标"
  modePriority: 44
  type: 1
  identity: "种收有道"
  identityPriority: 44
  conditionDes: "获得农场币大于1亿"
  data: 34
  dataMax: 10000000000.0
  dataMin: 100000000.0
}
rows {
  id: 72
  mode: 4
  Label: "农场首富"
  des: "种下一枚农场币，收获百亿"
  modePriority: 19
  type: 1
  identity: "谷仓丰盈"
  identityPriority: 19
  conditionDes: "获得农场币大于100亿"
  data: 34
  dataMin: 10000000000.0
}
rows {
  id: 73
  mode: 4
  Label: "农场小窝"
  des: "帮你遮风挡雨"
  modePriority: 999
  type: 2
  identity: "小小农场主"
  identityPriority: 999
  conditionDes: "帮你遮风挡雨"
}
rows {
  id: 74
  mode: 5
  Label: "造梦学徒"
  des: "创意之旅启程！"
  modePriority: 65
  type: 2
  identity: "造梦新手"
  identityPriority: 65
  conditionDes: "造梦师等级高于1级"
  data: 40
  dataMax: 4.0
  dataMin: 1.0
}
rows {
  id: 75
  mode: 5
  Label: "筑梦师"
  des: "想象力开始绽放！"
  modePriority: 40
  type: 2
  identity: "筑梦师"
  identityPriority: 40
  conditionDes: "造梦师等级高于5级"
  data: 40
  dataMax: 9.0
  dataMin: 5.0
}
rows {
  id: 76
  mode: 5
  Label: "首席造梦师"
  des: "你的世界由你主宰！"
  modePriority: 15
  type: 2
  identity: "首席造梦师"
  identityPriority: 15
  conditionDes: "造梦师等级高于10级"
  data: 40
  dataMin: 10.0
}
rows {
  id: 77
  mode: 5
  Label: "探索者"
  des: "新奇世界等你发现！"
  modePriority: 84
  type: 2
  identity: "探索者"
  identityPriority: 84
  conditionDes: "赛季至少玩过1张星世界地图"
  data: 48
  dataMax: 9.0
  dataMin: 1.0
}
rows {
  id: 78
  mode: 5
  Label: "冒险家"
  des: "足迹遍布元梦！"
  modePriority: 72
  type: 2
  identity: "冒险家"
  identityPriority: 72
  conditionDes: "赛季至少玩过10张星世界地图"
  data: 48
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 79
  mode: 5
  Label: "地图通"
  des: "没有你没去过的地方！"
  modePriority: 48
  type: 2
  identity: "地图通"
  identityPriority: 48
  conditionDes: "赛季至少玩过50张星世界地图"
  data: 48
  dataMax: 99.0
  dataMin: 50.0
}
rows {
  id: 80
  mode: 5
  Label: "星界导游"
  des: "星世界地图学家"
  modePriority: 23
  type: 2
  identity: "星界导游"
  identityPriority: 23
  conditionDes: "赛季至少玩过100张星世界地图"
  data: 48
  dataMin: 100.0
}
rows {
  id: 81
  mode: 5
  Label: "小小梦想家"
  des: "种下第一颗灵感种子"
  modePriority: 64
  type: 2
  identity: "梦想家"
  identityPriority: 64
  conditionDes: "发布地图数量大于1个"
  data: 49
  dataMax: 9.0
  dataMin: 1.0
}
rows {
  id: 82
  mode: 5
  Label: "奇想天地"
  des: "让想象力坐上过山车"
  modePriority: 39
  type: 2
  identity: "造梦大师"
  identityPriority: 39
  conditionDes: "发布地图数量大于10个"
  data: 49
  dataMax: 19.0
  dataMin: 10.0
}
rows {
  id: 83
  mode: 5
  Label: "创意无限"
  des: "构筑属于你的幻想天地"
  modePriority: 14
  type: 1
  identity: "创意无限"
  identityPriority: 14
  conditionDes: "发布地图数量大于20个"
  data: 49
  dataMin: 20.0
}
rows {
  id: 84
  mode: 5
  Label: "挑战者"
  des: "迈出超越自我的第一步！"
  modePriority: 73
  type: 1
  identity: "热爱挑战"
  identityPriority: 73
  conditionDes: "赛季闯关挑战积分大于10"
  data: 47
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 85
  mode: 5
  Label: "超级玩家"
  des: "没有关卡能够难住你！"
  modePriority: 49
  type: 2
  identity: "超级玩家"
  identityPriority: 49
  conditionDes: "赛季闯关挑战积分大于50"
  data: 47
  dataMax: 199.0
  dataMin: 50.0
}
rows {
  id: 86
  mode: 5
  Label: "极限王者"
  des: "超越极限，站在赛季巅峰！"
  modePriority: 24
  type: 1
  identity: "挑战极限"
  identityPriority: 24
  conditionDes: "赛季闯关挑战积分大于200"
  data: 47
  dataMin: 200.0
}
rows {
  id: 87
  mode: 5
  Label: "萌新出道"
  des: "有星宝开始关注你了"
  modePriority: 57
  type: 1
  identity: "小有名气"
  identityPriority: 57
  conditionDes: "赛季粉丝数大于10个"
  data: 45
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 88
  mode: 5
  Label: "小有名气"
  des: "你的粉丝团正在壮大"
  modePriority: 33
  type: 1
  identity: "位列顶流"
  identityPriority: 33
  conditionDes: "赛季粉丝数大于50个"
  data: 45
  dataMax: 99.0
  dataMin: 50.0
}
rows {
  id: 89
  mode: 5
  Label: "超级明星"
  des: "你就是元梦偶像"
  modePriority: 8
  type: 2
  identity: "超级明星"
  identityPriority: 8
  conditionDes: "赛季粉丝数大于100个"
  data: 45
  dataMin: 100.0
}
rows {
  id: 90
  mode: 5
  Label: "通关能手"
  des: "挑战的脚步从未停止"
  modePriority: 47
  type: 1
  identity: "通关能手"
  identityPriority: 47
  conditionDes: "通关地图数量大于10个"
  data: 50
  dataMax: 49.0
  dataMin: 10.0
}
rows {
  id: 91
  mode: 5
  Label: "速通大神"
  des: "实力无与伦比"
  modePriority: 22
  type: 1
  identity: "速通大神"
  identityPriority: 22
  conditionDes: "通关地图数量大于50个"
  data: 50
  dataMin: 50.0
}
rows {
  id: 92
  mode: 5
  Label: "创意世界"
  des: "比拼创意的世界"
  modePriority: 999
  type: 2
  identity: "小小旅客"
  identityPriority: 999
  conditionDes: "比拼创意的世界"
}
