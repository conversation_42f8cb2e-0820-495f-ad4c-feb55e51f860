com.tencent.wea.xlsRes.table_ProfileThemeData
excel/xls/D_道具表_个性化_背景.xlsx sheet:背景解锁
rows {
  id: 1
  items: 845001
  desc: "限时活动获得"
  name: "浪漫星空"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_006"
  windowBGRes: "UI_ProfileThemeBG_006"
  infoBGRes: "D_ProfileThemeUI_006"
  showInView: true
  beginTime {
    seconds: 1714060800
  }
  fashionValue: 200
}
rows {
  id: 2
  items: 400630
  desc: "获得永久时装月光女神后解锁"
  name: "月光女神"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_001"
  windowBGRes: "UI_ProfileThemeBG_001"
  infoBGRes: "D_ProfileThemeUI_001"
  showInView: true
  fashionValue: 0
}
rows {
  id: 3
  items: 400340
  items: 400570
  desc: "获得永久时装狐剑仙后解锁"
  name: "狐剑仙"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_002"
  windowBGRes: "UI_ProfileThemeBG_002"
  infoBGRes: "D_ProfileThemeUI_002"
  showInView: true
  fashionValue: 0
}
rows {
  id: 4
  items: 401340
  items: 401350
  desc: "获得永久时装小龙女/龙宫之主后解锁"
  name: "小龙女"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_003"
  windowBGRes: "UI_ProfileThemeBG_003"
  infoBGRes: "D_ProfileThemeUI_003"
  showInView: true
  fashionValue: 0
}
rows {
  id: 5
  items: 401440
  items: 401450
  desc: "获得永久时装时空守护神后解锁"
  name: "时空守护神"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_004"
  windowBGRes: "UI_ProfileThemeBG_004"
  infoBGRes: "D_ProfileThemeUI_004"
  showInView: true
  fashionValue: 0
}
rows {
  id: 6
  items: 400300
  desc: "获得永久时装冰之子后解锁"
  name: "冰之子"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_005"
  windowBGRes: "UI_ProfileThemeBG_005"
  infoBGRes: "D_ProfileThemeUI_005"
  showInView: true
  fashionValue: 0
}
rows {
  id: 7
  items: 401850
  items: 401860
  desc: "获得永久时装彩虹旋律/乐坛巨星后解锁"
  name: "彩虹旋律"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_007"
  windowBGRes: "UI_ProfileThemeBG_007"
  infoBGRes: "D_ProfileThemeUI_007"
  showInView: true
  beginTime {
    seconds: 1714060800
  }
  fashionValue: 0
}
rows {
  id: 8
  items: 402090
  items: 402100
  desc: "获得永久时装守护骑士/花海守护者后解锁"
  name: "永恒之誓"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_008"
  windowBGRes: "UI_ProfileThemeBG_008"
  infoBGRes: "D_ProfileThemeUI_008"
  showInView: true
  beginTime {
    seconds: 1715875200
  }
  fashionValue: 0
}
rows {
  id: 9
  items: 845002
  desc: "限时活动获得"
  name: "天鹅之舞"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_009"
  windowBGRes: "UI_ProfileThemeBG_009"
  infoBGRes: "D_ProfileThemeUI_009"
  showInView: true
  lowVer: "1.3.6.1"
  fashionValue: 200
}
rows {
  id: 10
  items: 845003
  desc: "限时活动获得"
  name: "夜色烟火"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_010"
  windowBGRes: "UI_ProfileThemeBG_010"
  infoBGRes: "D_ProfileThemeUI_010"
  showInView: true
  beginTime {
    seconds: 1717689600
  }
  lowVer: "1.3.6.1"
  fashionValue: 200
}
rows {
  id: 11
  items: 402370
  items: 402380
  desc: "获得永久时装海之神女/海之王 后解锁"
  name: "海之神"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_011"
  windowBGRes: "UI_ProfileThemeBG_011"
  infoBGRes: "D_ProfileThemeUI_011"
  showInView: true
  beginTime {
    seconds: 1717689600
  }
  lowVer: "1.3.6.1"
  fashionValue: 0
}
rows {
  id: 12
  items: 402460
  items: 402470
  desc: "获得永久时装焰战神/耀战神后解锁"
  name: "战神之路"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_012"
  windowBGRes: "UI_ProfileThemeBG_012"
  infoBGRes: "D_ProfileThemeUI_012"
  showInView: true
  beginTime {
    seconds: 1718899200
  }
  lowVer: "1.3.7.48"
  fashionValue: 0
}
rows {
  id: 13
  items: 402480
  desc: "获得永久时装灭战神后解锁"
  name: "战神颂歌"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_013"
  windowBGRes: "UI_ProfileThemeBG_013"
  infoBGRes: "D_ProfileThemeUI_013"
  showInView: true
  beginTime {
    seconds: 1718899200
  }
  lowVer: "1.3.7.48"
  fashionValue: 0
}
rows {
  id: 14
  items: 845004
  desc: "限时活动获得"
  name: "Hello Kitty"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_014"
  windowBGRes: "UI_ProfileThemeBG_014"
  infoBGRes: "D_ProfileThemeUI_014"
  showInView: true
  beginTime {
    seconds: 1720195200
  }
  lowVer: "1.3.7.106"
  fashionValue: 200
}
rows {
  id: 15
  items: 402830
  items: 402840
  desc: "获得永久时装甜心公主后解锁"
  name: "甜心公主"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_015"
  windowBGRes: "UI_ProfileThemeBG_015"
  infoBGRes: "D_ProfileThemeUI_015"
  showInView: true
  lowVer: "1.3.12.19"
  fashionValue: 0
}
rows {
  id: 16
  items: 845006
  desc: "限时活动获得"
  name: "夜晚游乐场"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_016"
  windowBGRes: "UI_ProfileThemeBG_016"
  infoBGRes: "D_ProfileThemeUI_016"
  showInView: true
  lowVer: "1.3.7.106"
  fashionValue: 200
}
rows {
  id: 17
  items: 403100
  items: 403110
  desc: "获得永久时装凤王/凰后后解锁"
  name: "凤求凰"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_017"
  windowBGRes: "UI_ProfileThemeBG_017"
  infoBGRes: "D_ProfileThemeUI_017"
  showInView: true
  beginTime {
    seconds: 1723132800
  }
  lowVer: "1.3.12.19"
  fashionValue: 0
}
rows {
  id: 18
  items: 845007
  desc: "限时活动获得"
  name: "丹枫秋意"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_018"
  windowBGRes: "UI_ProfileThemeBG_018"
  infoBGRes: "D_ProfileThemeUI_018"
  showInView: true
  beginTime {
    seconds: 1724947200
  }
  lowVer: "1.3.7.106"
  fashionValue: 200
}
rows {
  id: 19
  items: 403440
  items: 403450
  desc: "获得永久时装花之语/森之神后解锁"
  name: "精灵之森"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_019"
  windowBGRes: "UI_ProfileThemeBG_019"
  infoBGRes: "D_ProfileThemeUI_019"
  showInView: true
  beginTime {
    seconds: 1724947200
  }
  lowVer: "1.3.18.1"
  fashionValue: 0
}
rows {
  id: 20
  items: 845008
  desc: "限时活动获得"
  name: "落星之海"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_020"
  windowBGRes: "UI_ProfileThemeBG_020"
  infoBGRes: "D_ProfileThemeUI_020"
  showInView: true
  beginTime {
    seconds: 1724947200
  }
  lowVer: "1.3.18.1"
  fashionValue: 200
}
rows {
  id: 21
  items: 403460
  desc: "获得永久时装广寒仙子后解锁"
  name: "广寒仙子"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_021"
  windowBGRes: "UI_ProfileThemeBG_021"
  infoBGRes: "D_ProfileThemeUI_021"
  showInView: true
  beginTime {
    seconds: 1726156800
  }
  fashionValue: 0
}
rows {
  id: 22
  items: 403660
  desc: "获得永久时装三彩逸士后解锁"
  name: "三彩逸士"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_022"
  windowBGRes: "UI_ProfileThemeBG_022"
  infoBGRes: "D_ProfileThemeUI_022"
  showInView: true
  beginTime {
    seconds: 1727971200
  }
  fashionValue: 0
}
rows {
  id: 23
  items: 845010
  desc: "限时活动获得"
  name: "事件视界"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_023"
  windowBGRes: "UI_ProfileThemeBG_023"
  infoBGRes: "D_ProfileThemeUI_023"
  showInView: true
  beginTime {
    seconds: 1731772800
  }
  lowVer: "1.3.26.1"
  fashionValue: 200
}
rows {
  id: 24
  items: 403860
  items: 403870
  desc: "获得永久时装大收藏家/星河织梦者后解锁"
  name: "星语星愿"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_024"
  windowBGRes: "UI_ProfileThemeBG_024"
  infoBGRes: "D_ProfileThemeUI_024"
  showInView: true
  beginTime {
    seconds: 1729180800
  }
  lowVer: "1.3.26.1"
  fashionValue: 0
}
rows {
  id: 25
  items: 845009
  desc: "限时活动获得"
  name: "赛博小喵"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_025"
  windowBGRes: "UI_ProfileThemeBG_026"
  infoBGRes: "D_ProfileThemeUI_026"
  showInView: true
  beginTime {
    seconds: 1735833600
  }
  lowVer: "1.3.26.57"
  fashionValue: 200
}
rows {
  id: 28
  items: 404110
  items: 404120
  desc: "获得永久时装永昼男爵/暮色皇女后解锁"
  name: "皇家舞会"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_029"
  windowBGRes: "UI_ProfileThemeBG_028"
  infoBGRes: "D_ProfileThemeUI_028"
  showInView: true
  beginTime {
    seconds: 1730217600
  }
  fashionValue: 0
}
rows {
  id: 26
  items: 845011
  desc: "限时活动获得"
  name: "泡泡森林"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_026"
  windowBGRes: "UI_ProfileThemeBG_025"
  infoBGRes: "D_ProfileThemeUI_025"
  showInView: true
  beginTime {
    seconds: 1731772800
  }
  lowVer: "1.3.26.57"
  fashionValue: 200
}
rows {
  id: 29
  items: 404430
  items: 404440
  desc: "获得永久时装美食主播/美食品鉴官后解锁"
  name: "美食派对"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_028"
  windowBGRes: "UI_ProfileThemeBG_029"
  infoBGRes: "D_ProfileThemeUI_029"
  showInView: true
  beginTime {
    seconds: 1732809600
  }
  lowVer: "1.3.37.1"
  fashionValue: 0
}
rows {
  id: 27
  items: 845012
  desc: "限时活动获得"
  name: "电竞少女"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_031"
  windowBGRes: "UI_ProfileThemeBG_030"
  infoBGRes: "D_ProfileThemeUI_030"
  showInView: true
  beginTime {
    seconds: 1732809600
  }
  lowVer: "1.3.37.1"
  fashionValue: 200
}
rows {
  id: 31
  items: 404320
  desc: "获得永久时装冰雪精灵后解锁"
  name: "冰雪精灵"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_030"
  windowBGRes: "UI_ProfileThemeBG_031"
  infoBGRes: "D_ProfileThemeUI_031"
  showInView: true
  beginTime {
    seconds: 1735833600
  }
  lowVer: "1.3.37.68"
  fashionValue: 0
}
rows {
  id: 32
  items: 845013
  desc: "限时活动获得"
  name: "韵律宫阙"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_027"
  windowBGRes: "UI_ProfileThemeBG_027"
  infoBGRes: "D_ProfileThemeUI_027"
  showInView: true
  beginTime {
    seconds: 1734969600
  }
  lowVer: "1.3.26.111"
  fashionValue: 200
}
rows {
  id: 33
  items: 404540
  desc: "获得永久时装夜天使后解锁"
  name: "夜天使"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_036"
  windowBGRes: "UI_ProfileThemeBG_035"
  infoBGRes: "D_ProfileThemeUI_035"
  showInView: true
  beginTime {
    seconds: 1737993600
  }
  lowVer: "1.3.37.1"
  fashionValue: 0
}
rows {
  id: 34
  items: 404550
  desc: "获得永久时装昼天使后解锁"
  name: "昼天使"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_035"
  windowBGRes: "UI_ProfileThemeBG_036"
  infoBGRes: "D_ProfileThemeUI_036"
  showInView: true
  beginTime {
    seconds: 1737993600
  }
  lowVer: "1.3.37.1"
  fashionValue: 0
}
rows {
  id: 35
  items: 404560
  desc: "获得永久时装永恒天使后解锁"
  name: "永恒天使"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_034"
  windowBGRes: "UI_ProfileThemeBG_034"
  infoBGRes: "D_ProfileThemeUI_034"
  showInView: true
  beginTime {
    seconds: 1737993600
  }
  lowVer: "1.3.37.1"
  fashionValue: 0
}
rows {
  id: 36
  items: 404720
  items: 404730
  desc: "获得永久时装惊鸿舞姬/牡丹贵妃后解锁"
  name: "大唐风华"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_037"
  windowBGRes: "UI_ProfileThemeBG_037"
  infoBGRes: "D_ProfileThemeUI_037"
  showInView: true
  beginTime {
    seconds: 1737043200
  }
  lowVer: "1.3.37.1"
  fashionValue: 0
}
rows {
  id: 37
  items: 845014
  desc: "限时活动获得"
  name: "星河礼舞"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_033"
  windowBGRes: "UI_ProfileThemeBG_033"
  infoBGRes: "D_ProfileThemeUI_033"
  showInView: true
  beginTime {
    seconds: 1737043200
  }
  lowVer: "1.3.37.1"
  fashionValue: 200
}
rows {
  id: 38
  items: 845015
  desc: "限时活动获得"
  name: "树载星愿"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_032"
  windowBGRes: "UI_ProfileThemeBG_032"
  infoBGRes: "D_ProfileThemeUI_032"
  showInView: true
  beginTime {
    seconds: 1737648000
  }
  lowVer: "1.3.37.1"
  fashionValue: 200
}
rows {
  id: 39
  items: 410040
  items: 410050
  desc: "获得永久时装玲珑狐妖/愿力狐仙后解锁"
  name: "桃源万千"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_038"
  windowBGRes: "UI_ProfileThemeBG_038"
  infoBGRes: "D_ProfileThemeUI_038"
  showInView: true
  beginTime {
    seconds: 1740067200
  }
  lowVer: "1.3.68.100"
  fashionValue: 0
}
rows {
  id: 40
  items: 410390
  items: 410400
  desc: "获得永久时装预言家/圣光预言家后解锁"
  name: "真相之夜"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_039"
  windowBGRes: "UI_ProfileThemeBG_039"
  infoBGRes: "D_ProfileThemeUI_039"
  showInView: true
  beginTime {
    seconds: 1741881600
  }
  lowVer: "1.3.78.1"
  fashionValue: 0
}
rows {
  id: 41
  items: 845016
  desc: "限时活动获得"
  name: "月影古堡"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_041"
  windowBGRes: "UI_ProfileThemeBG_041"
  infoBGRes: "D_ProfileThemeUI_041"
  showInView: true
  beginTime {
    seconds: 1741881600
  }
  lowVer: "1.3.37.1"
  fashionValue: 200
}
rows {
  id: 42
  items: 410350
  desc: "获得永久时装玩偶修复师后解锁"
  name: "玩偶修复师"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_040"
  windowBGRes: "UI_ProfileThemeBG_040"
  infoBGRes: "D_ProfileThemeUI_040"
  showInView: true
  beginTime {
    seconds: 1744300800
  }
  lowVer: "1.3.78.61"
  fashionValue: 0
}
rows {
  id: 43
  items: 410660
  desc: "获得永久时装自由之蝶后解锁"
  name: "自由之蝶"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_043"
  windowBGRes: "UI_ProfileThemeBG_043"
  infoBGRes: "D_ProfileThemeUI_043"
  showInView: true
  beginTime {
    seconds: 1749139200
  }
  lowVer: "1.3.78.61"
  fashionValue: 0
}
rows {
  id: 44
  items: 410970
  items: 410980
  desc: "获得永久时装超能魔导师/无限魔导师后解锁"
  name: "超能学园"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_044"
  windowBGRes: "UI_ProfileThemeBG_044"
  infoBGRes: "D_ProfileThemeUI_044"
  showInView: true
  beginTime {
    seconds: 1746115200
  }
  fashionValue: 0
}
rows {
  id: 45
  items: 845017
  desc: "限时活动获得"
  name: "圣托里韵"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_046"
  windowBGRes: "UI_ProfileThemeBG_046"
  infoBGRes: "D_ProfileThemeUI_046"
  showInView: true
  beginTime {
    seconds: 1746115200
  }
  lowVer: "1.3.37.1"
  fashionValue: 200
}
rows {
  id: 46
  items: 410840
  desc: "获得永久时装幻彩画匠后解锁"
  name: "幻彩画匠"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_045"
  windowBGRes: "UI_ProfileThemeBG_045"
  infoBGRes: "D_ProfileThemeUI_045"
  showInView: true
  beginTime {
    seconds: 1747324800
  }
  fashionValue: 0
}
rows {
  id: 47
  items: 411630
  items: 411640
  desc: "获得永久时装昆仑守护神/昆仑之心后解锁"
  name: "昆仑秘境"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_048"
  windowBGRes: "UI_ProfileThemeBG_048"
  infoBGRes: "D_ProfileThemeUI_048"
  showInView: true
  beginTime {
    seconds: 1748534400
  }
  fashionValue: 0
}
rows {
  id: 48
  items: 845018
  desc: "限时活动获得"
  name: "竹影摇轩"
  quality: 1
  icon: "CDN:ListIcon_ProfileTheme_042"
  windowBGRes: "UI_ProfileThemeBG_042"
  infoBGRes: "D_ProfileThemeUI_042"
  showInView: true
  beginTime {
    seconds: 1750953600
  }
  lowVer: "1.3.37.1"
  fashionValue: 200
}
