com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_karl.xlsx sheet:奖池-配饰卡池
rows {
  poolId: 50000001
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 50000002
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 50000003
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 50000004
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 50000005
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 3
      prices: 9
      prices: 16
      prices: 25
      prices: 46
      prices: 68
      prices: 90
      prices: 112
      prices: 135
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 3
      originalPrices: 9
      originalPrices: 16
      originalPrices: 25
      originalPrices: 46
      originalPrices: 68
      originalPrices: 90
      originalPrices: 112
      originalPrices: 135
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
