com.tencent.wea.xlsRes.table_CardBagPoolConfigData
excel/xls/K_卡牌.xlsx sheet:卡包权重池
rows {
  id: 101
  cardBagPool {
    cardType: 0
    drawId: 1010201
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010202
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010203
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010204
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010205
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010206
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010207
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010208
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010209
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010501
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010502
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010503
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010504
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010505
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010506
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1010507
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1010508
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1010509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010701
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010702
    rangeWeight: 3600
  }
  cardBagPool {
    cardType: 0
    drawId: 1010703
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1010704
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1010705
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1010706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010709
    rangeWeight: 200
  }
  cardBagPool {
    cardType: 0
    drawId: 1011001
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1011002
    rangeWeight: 1282
  }
  cardBagPool {
    cardType: 0
    drawId: 1011003
    rangeWeight: 300
  }
  cardBagPool {
    cardType: 0
    drawId: 1011004
    rangeWeight: 300
  }
  cardBagPool {
    cardType: 0
    drawId: 1011005
    rangeWeight: 200
  }
  cardBagPool {
    cardType: 0
    drawId: 1011006
    rangeWeight: 200
  }
  cardBagPool {
    cardType: 0
    drawId: 1011007
    rangeWeight: 16
  }
  cardBagPool {
    cardType: 0
    drawId: 1011008
    rangeWeight: 16
  }
  cardBagPool {
    cardType: 0
    drawId: 1011009
    rangeWeight: 12
  }
  cardBagPool {
    cardType: 1
    drawId: 1
    rangeWeight: 900
  }
}
rows {
  id: 102
  cardBagPool {
    cardType: 0
    drawId: 1010201
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010202
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010203
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010204
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010205
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010206
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010207
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010208
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010209
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010501
    rangeWeight: 6578
  }
  cardBagPool {
    cardType: 0
    drawId: 1010502
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010503
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010504
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010505
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010506
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1010507
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1010508
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1010509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010701
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010702
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010703
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1010704
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1010705
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1010706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010709
    rangeWeight: 400
  }
  cardBagPool {
    cardType: 0
    drawId: 1011001
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1011002
    rangeWeight: 1850
  }
  cardBagPool {
    cardType: 0
    drawId: 1011003
    rangeWeight: 600
  }
  cardBagPool {
    cardType: 0
    drawId: 1011004
    rangeWeight: 600
  }
  cardBagPool {
    cardType: 0
    drawId: 1011005
    rangeWeight: 400
  }
  cardBagPool {
    cardType: 0
    drawId: 1011006
    rangeWeight: 400
  }
  cardBagPool {
    cardType: 0
    drawId: 1011007
    rangeWeight: 30
  }
  cardBagPool {
    cardType: 0
    drawId: 1011008
    rangeWeight: 30
  }
  cardBagPool {
    cardType: 0
    drawId: 1011009
    rangeWeight: 20
  }
  cardBagPool {
    cardType: 1
    drawId: 1
    rangeWeight: 1800
  }
}
rows {
  id: 103
  cardBagPool {
    cardType: 0
    drawId: 1010201
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010202
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010203
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010204
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010205
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010206
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010207
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010208
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010209
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010501
    rangeWeight: 6230
  }
  cardBagPool {
    cardType: 0
    drawId: 1010502
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010503
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010504
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010505
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010506
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010507
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010508
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010701
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010702
    rangeWeight: 3220
  }
  cardBagPool {
    cardType: 0
    drawId: 1010703
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010704
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010705
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010709
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1011001
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1011002
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1011003
    rangeWeight: 1190
  }
  cardBagPool {
    cardType: 0
    drawId: 1011004
    rangeWeight: 1190
  }
  cardBagPool {
    cardType: 0
    drawId: 1011005
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1011006
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1011007
    rangeWeight: 60
  }
  cardBagPool {
    cardType: 0
    drawId: 1011008
    rangeWeight: 60
  }
  cardBagPool {
    cardType: 0
    drawId: 1011009
    rangeWeight: 40
  }
  cardBagPool {
    cardType: 1
    drawId: 1
    rangeWeight: 3570
  }
}
rows {
  id: 104
  cardBagPool {
    cardType: 0
    drawId: 1010201
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010202
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010203
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010204
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010205
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010206
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010207
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010208
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010209
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010501
    rangeWeight: 5990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010502
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010503
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010504
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010505
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010506
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1010507
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1010508
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1010509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010701
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010702
    rangeWeight: 2990
  }
  cardBagPool {
    cardType: 0
    drawId: 1010703
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1010704
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1010705
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1010706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010709
    rangeWeight: 1006
  }
  cardBagPool {
    cardType: 0
    drawId: 1011001
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1011002
    rangeWeight: 2257
  }
  cardBagPool {
    cardType: 0
    drawId: 1011003
    rangeWeight: 1355
  }
  cardBagPool {
    cardType: 0
    drawId: 1011004
    rangeWeight: 1355
  }
  cardBagPool {
    cardType: 0
    drawId: 1011005
    rangeWeight: 1006
  }
  cardBagPool {
    cardType: 0
    drawId: 1011006
    rangeWeight: 1006
  }
  cardBagPool {
    cardType: 0
    drawId: 1011007
    rangeWeight: 115
  }
  cardBagPool {
    cardType: 0
    drawId: 1011008
    rangeWeight: 115
  }
  cardBagPool {
    cardType: 0
    drawId: 1011009
    rangeWeight: 81
  }
  cardBagPool {
    cardType: 1
    drawId: 1
    rangeWeight: 4065
  }
}
rows {
  id: 105
  cardBagPool {
    cardType: 0
    drawId: 1010201
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010202
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010203
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010204
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010205
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010206
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010207
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010208
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010209
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010501
    rangeWeight: 5870
  }
  cardBagPool {
    cardType: 0
    drawId: 1010502
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010503
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010504
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010505
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010506
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1010507
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1010508
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1010509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010701
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010702
    rangeWeight: 2700
  }
  cardBagPool {
    cardType: 0
    drawId: 1010703
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1010704
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1010705
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1010706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010709
    rangeWeight: 1114
  }
  cardBagPool {
    cardType: 0
    drawId: 1011001
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1011002
    rangeWeight: 2366
  }
  cardBagPool {
    cardType: 0
    drawId: 1011003
    rangeWeight: 1746
  }
  cardBagPool {
    cardType: 0
    drawId: 1011004
    rangeWeight: 1746
  }
  cardBagPool {
    cardType: 0
    drawId: 1011005
    rangeWeight: 1114
  }
  cardBagPool {
    cardType: 0
    drawId: 1011006
    rangeWeight: 1114
  }
  cardBagPool {
    cardType: 0
    drawId: 1011007
    rangeWeight: 170
  }
  cardBagPool {
    cardType: 0
    drawId: 1011008
    rangeWeight: 170
  }
  cardBagPool {
    cardType: 0
    drawId: 1011009
    rangeWeight: 100
  }
  cardBagPool {
    cardType: 1
    drawId: 1
    rangeWeight: 5238
  }
}
rows {
  id: 101001
  cardBagPool {
    cardType: 0
    drawId: 1010201
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010202
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010203
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010204
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010205
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010206
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010207
    rangeWeight: 7350
  }
  cardBagPool {
    cardType: 0
    drawId: 1010501
    rangeWeight: 7350
  }
}
rows {
  id: 102001
  cardBagPool {
    cardType: 0
    drawId: 1010208
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010209
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010502
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010503
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010504
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010505
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010701
    rangeWeight: 3537
  }
  cardBagPool {
    cardType: 0
    drawId: 1010702
    rangeWeight: 3537
  }
}
rows {
  id: 103001
  cardBagPool {
    cardType: 0
    drawId: 1010506
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010507
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010508
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010703
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010704
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1010705
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1011001
    rangeWeight: 1930
  }
  cardBagPool {
    cardType: 0
    drawId: 1011002
    rangeWeight: 1930
  }
}
rows {
  id: 104001
  cardBagPool {
    cardType: 0
    drawId: 1010509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1010709
    rangeWeight: 1006
  }
  cardBagPool {
    cardType: 0
    drawId: 1011003
    rangeWeight: 1355
  }
  cardBagPool {
    cardType: 0
    drawId: 1011004
    rangeWeight: 1355
  }
  cardBagPool {
    cardType: 0
    drawId: 1011005
    rangeWeight: 1006
  }
  cardBagPool {
    cardType: 0
    drawId: 1011006
    rangeWeight: 1006
  }
}
rows {
  id: 105001
  cardBagPool {
    cardType: 0
    drawId: 1011007
    rangeWeight: 170
  }
  cardBagPool {
    cardType: 0
    drawId: 1011008
    rangeWeight: 170
  }
  cardBagPool {
    cardType: 0
    drawId: 1011009
    rangeWeight: 100
  }
}
rows {
  id: 201
  cardBagPool {
    cardType: 0
    drawId: 1020101
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020102
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020103
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020104
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020105
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020106
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020107
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020108
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020109
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020201
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020202
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020203
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020204
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020205
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020206
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020207
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020208
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020209
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020301
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020302
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020303
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020304
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020305
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020306
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020307
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020308
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020309
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020401
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020402
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020403
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020404
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020405
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020406
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020407
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020408
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020409
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020501
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020502
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020503
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020504
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020505
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020506
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020507
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020508
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020509
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020601
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020602
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020603
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020604
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020605
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020606
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020607
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020608
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020609
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020701
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020702
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020703
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020704
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020705
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020706
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020707
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020708
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020709
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1020801
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020802
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020803
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020804
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020805
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020806
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020807
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020809
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1020901
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020902
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020903
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020904
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1020905
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020906
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020907
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1020908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020909
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021001
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1021002
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1021003
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1021004
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1021005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021007
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021008
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021009
    rangeWeight: 53
  }
  cardBagPool {
    cardType: 0
    drawId: 1021101
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1021102
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1021103
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021104
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021105
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021106
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021107
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021108
    rangeWeight: 53
  }
  cardBagPool {
    cardType: 0
    drawId: 1021109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021201
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1021202
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021203
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021204
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021205
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021206
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1021207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021208
    rangeWeight: 53
  }
  cardBagPool {
    cardType: 0
    drawId: 1021209
    rangeWeight: 53
  }
  cardBagPool {
    cardType: 1
    drawId: 2
    rangeWeight: 140
  }
  cardBagPool {
    cardType: 1
    drawId: 3
    rangeWeight: 53
  }
}
rows {
  id: 202
  cardBagPool {
    cardType: 0
    drawId: 1020101
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020102
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020103
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020104
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020105
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020106
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020107
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020108
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020109
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020201
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020202
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020203
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020204
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020205
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020206
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020207
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020208
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020209
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020301
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020302
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020303
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020304
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020305
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020306
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020307
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020308
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020309
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020401
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020402
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020403
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020404
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020405
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020406
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020407
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020408
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020409
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020501
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1020502
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020503
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020504
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020505
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020506
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020507
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020508
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020509
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020601
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020602
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020603
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020604
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020605
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020606
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020607
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020608
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020609
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020701
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020702
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020703
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020704
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020705
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020706
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020707
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020708
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020709
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1020801
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020802
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020803
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020804
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020805
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020806
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020807
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020809
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1020901
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020902
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020903
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020904
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1020905
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020906
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020907
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020909
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021001
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1021002
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1021003
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1021004
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1021005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021007
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021008
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021009
    rangeWeight: 101
  }
  cardBagPool {
    cardType: 0
    drawId: 1021101
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1021102
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1021103
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021104
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021105
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021106
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021107
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021108
    rangeWeight: 101
  }
  cardBagPool {
    cardType: 0
    drawId: 1021109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021201
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1021202
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021203
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021204
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021205
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021206
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1021207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021208
    rangeWeight: 101
  }
  cardBagPool {
    cardType: 0
    drawId: 1021209
    rangeWeight: 101
  }
  cardBagPool {
    cardType: 1
    drawId: 2
    rangeWeight: 290
  }
  cardBagPool {
    cardType: 1
    drawId: 3
    rangeWeight: 101
  }
}
rows {
  id: 203
  cardBagPool {
    cardType: 0
    drawId: 1020101
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020102
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020103
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020104
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020105
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020106
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020107
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020108
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020109
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020201
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020202
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020203
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020204
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020205
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020206
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020207
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020208
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020209
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020301
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020302
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020303
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020304
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020305
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020306
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020307
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020308
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020309
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020401
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020402
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020403
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020404
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020405
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020406
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020407
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020408
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020409
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020501
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1020502
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020503
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020504
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020505
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020506
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020507
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020508
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020509
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020601
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020602
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020603
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020604
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020605
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020606
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020607
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020608
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020609
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020701
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020702
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020703
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020704
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020705
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020706
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020707
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020708
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020709
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1020801
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1020802
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020803
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020804
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020805
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020806
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020807
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020809
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1020901
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020902
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020903
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020904
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020905
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020906
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020907
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1020908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020909
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021001
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1021002
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1021003
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021004
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021007
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021008
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021009
    rangeWeight: 218
  }
  cardBagPool {
    cardType: 0
    drawId: 1021101
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1021102
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021103
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021104
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021105
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021106
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021107
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021108
    rangeWeight: 218
  }
  cardBagPool {
    cardType: 0
    drawId: 1021109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021201
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021202
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021203
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021204
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021205
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021206
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1021207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021208
    rangeWeight: 218
  }
  cardBagPool {
    cardType: 0
    drawId: 1021209
    rangeWeight: 218
  }
  cardBagPool {
    cardType: 1
    drawId: 2
    rangeWeight: 560
  }
  cardBagPool {
    cardType: 1
    drawId: 3
    rangeWeight: 218
  }
}
rows {
  id: 204
  cardBagPool {
    cardType: 0
    drawId: 1020101
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020102
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020103
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020104
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020105
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020106
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020107
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020108
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020109
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020201
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020202
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020203
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020204
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020205
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020206
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020207
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020208
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020209
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020301
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020302
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020303
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020304
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020305
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020306
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020307
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020308
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020309
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020401
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020402
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020403
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020404
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020405
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020406
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020407
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020408
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020409
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020501
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1020502
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020503
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020504
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020505
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020506
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020507
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020508
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020509
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020601
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020602
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020603
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020604
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020605
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020606
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020607
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020608
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020609
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020701
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020702
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020703
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020704
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020705
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020706
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020707
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020708
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020709
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1020801
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020802
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020803
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020804
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020805
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020806
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020807
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020809
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1020901
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020902
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020903
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020904
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1020905
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020906
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020907
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020909
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021001
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1021002
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1021003
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021004
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021007
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021008
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021009
    rangeWeight: 300
  }
  cardBagPool {
    cardType: 0
    drawId: 1021101
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1021102
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021103
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021104
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021105
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021106
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021107
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021108
    rangeWeight: 300
  }
  cardBagPool {
    cardType: 0
    drawId: 1021109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021201
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021202
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021203
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021204
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021205
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021206
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021208
    rangeWeight: 300
  }
  cardBagPool {
    cardType: 0
    drawId: 1021209
    rangeWeight: 300
  }
  cardBagPool {
    cardType: 1
    drawId: 2
    rangeWeight: 700
  }
  cardBagPool {
    cardType: 1
    drawId: 3
    rangeWeight: 300
  }
}
rows {
  id: 205
  cardBagPool {
    cardType: 0
    drawId: 1020101
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020102
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020103
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020104
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020105
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020106
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020107
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020108
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020109
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020201
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020202
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020203
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020204
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020205
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020206
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020207
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020208
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020209
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020301
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020302
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020303
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020304
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020305
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020306
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020307
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020308
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020309
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020401
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020402
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020403
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020404
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020405
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020406
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020407
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020408
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020409
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020501
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1020502
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020503
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020504
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020505
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020506
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020507
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020508
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020509
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020601
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020602
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020603
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020604
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020605
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020606
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020607
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020608
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020609
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020701
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020702
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020703
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020704
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020705
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020706
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020707
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020708
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020709
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1020801
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1020802
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020803
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020804
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020805
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020806
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020807
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020809
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1020901
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020902
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020903
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020904
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1020905
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020906
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020907
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1020908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020909
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021001
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1021002
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1021003
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1021004
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1021005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021007
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021008
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021009
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021101
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1021102
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1021103
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021104
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021105
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021106
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021107
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021108
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021201
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1021202
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021203
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021204
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021205
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021206
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021208
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021209
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 1
    drawId: 2
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 1
    drawId: 3
    rangeWeight: 348
  }
}
rows {
  id: 201001
  cardBagPool {
    cardType: 0
    drawId: 1020101
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020102
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020103
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020104
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020105
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020106
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020107
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020108
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020109
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020201
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020202
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020203
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020204
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020205
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020206
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020207
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020301
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020302
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020303
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020304
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020305
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020401
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020402
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1020501
    rangeWeight: 2705
  }
}
rows {
  id: 202001
  cardBagPool {
    cardType: 0
    drawId: 1020208
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020209
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020306
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020307
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020308
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020403
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020404
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020405
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020406
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020407
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020502
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020503
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020504
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020505
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020601
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020602
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020603
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020604
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020701
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020702
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1020801
    rangeWeight: 1150
  }
}
rows {
  id: 203001
  cardBagPool {
    cardType: 0
    drawId: 1020309
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020408
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020409
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020506
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020507
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020508
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020605
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020606
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020607
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020703
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020704
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020705
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020802
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020803
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020804
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020805
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020901
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020902
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020903
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1020904
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1021001
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1021002
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1021101
    rangeWeight: 800
  }
}
rows {
  id: 204001
  cardBagPool {
    cardType: 0
    drawId: 1020509
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020608
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020609
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020706
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020707
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020708
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020709
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1020806
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020807
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1020905
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020906
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020907
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1020908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021003
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021004
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021102
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021103
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021104
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021201
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1021202
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1021203
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 1
    drawId: 2
    rangeWeight: 700
  }
}
rows {
  id: 205001
  cardBagPool {
    cardType: 0
    drawId: 1020809
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1020909
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021007
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021008
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021009
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021105
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021106
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021107
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021108
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021204
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021205
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021206
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1021207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1021208
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1021209
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 1
    drawId: 3
    rangeWeight: 348
  }
}
rows {
  id: 301
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 500
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 75
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 70
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 490
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 265
  }
}
rows {
  id: 302
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 2320
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 650
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 150
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 145
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 1015
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 505
  }
}
rows {
  id: 303
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 2020
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 1050
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 348
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 280
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 1960
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 1090
  }
}
rows {
  id: 304
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 1835
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 900
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 350
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 2450
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 1500
  }
}
rows {
  id: 305
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 1645
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 1000
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 950
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 552
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 3150
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 1740
  }
}
rows {
  id: 301001
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 2705
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 0
  }
}
rows {
  id: 302001
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 1150
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 0
  }
}
rows {
  id: 303001
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 800
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 0
  }
}
rows {
  id: 304001
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 454
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 2450
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 0
  }
}
rows {
  id: 305001
  cardBagPool {
    cardType: 0
    drawId: 1030101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030104
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030105
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030202
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030203
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030204
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030301
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030302
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030303
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030304
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030305
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030306
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030307
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030308
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030309
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030401
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030402
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030403
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030404
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030405
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030406
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030407
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030408
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030409
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030501
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030502
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030503
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030504
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030505
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030506
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030507
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030508
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030509
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030601
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030602
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030603
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030604
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030605
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030606
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030607
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030608
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030609
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030701
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030702
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030703
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030704
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030705
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030706
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030707
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030708
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030709
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1030801
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030802
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030803
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030804
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030805
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030806
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030807
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030808
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1030809
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030901
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030902
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030903
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030904
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030905
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030906
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030907
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1030908
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1030909
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031001
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031002
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031003
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031004
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031005
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031006
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031007
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031008
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031009
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031101
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031102
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031103
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031104
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031105
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031106
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031107
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031108
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031109
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031201
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031202
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031203
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031204
    rangeWeight: 450
  }
  cardBagPool {
    cardType: 0
    drawId: 1031205
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031206
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031207
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031208
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 0
    drawId: 1031209
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 24
    rangeWeight: 0
  }
  cardBagPool {
    cardType: 1
    drawId: 25
    rangeWeight: 1740
  }
}
rows {
  id: 401001
  cardBagPool {
    drawId: 1040101
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 0
  }
}
rows {
  id: 402001
  cardBagPool {
    drawId: 1040101
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 0
  }
}
rows {
  id: 403001
  cardBagPool {
    drawId: 1040101
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 0
  }
}
rows {
  id: 404001
  cardBagPool {
    drawId: 1040101
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 0
  }
}
rows {
  id: 405001
  cardBagPool {
    drawId: 1040101
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 125
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 125
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 125
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 25
  }
}
rows {
  id: 501001
  cardBagPool {
    drawId: 1040401
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 0
  }
}
rows {
  id: 502001
  cardBagPool {
    drawId: 1040401
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 0
  }
}
rows {
  id: 503001
  cardBagPool {
    drawId: 1040401
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 0
  }
}
rows {
  id: 504001
  cardBagPool {
    drawId: 1040401
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 0
  }
}
rows {
  id: 505001
  cardBagPool {
    drawId: 1040401
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 25
  }
}
rows {
  id: 601001
  cardBagPool {
    drawId: 1040901
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 0
  }
}
rows {
  id: 602001
  cardBagPool {
    drawId: 1040901
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 0
  }
}
rows {
  id: 603001
  cardBagPool {
    drawId: 1040901
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 0
  }
}
rows {
  id: 604001
  cardBagPool {
    drawId: 1040901
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 0
  }
}
rows {
  id: 605001
  cardBagPool {
    drawId: 1040901
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 0
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 25
  }
}
rows {
  id: 401
  cardBagPool {
    drawId: 1040101
    rangeWeight: 200
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 200
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 200
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 10
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 10
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 10
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 2
  }
}
rows {
  id: 402
  cardBagPool {
    drawId: 1040101
    rangeWeight: 150
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 150
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 150
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 20
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 20
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 20
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 4
  }
}
rows {
  id: 403
  cardBagPool {
    drawId: 1040101
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 6
  }
}
rows {
  id: 404
  cardBagPool {
    drawId: 1040101
    rangeWeight: 80
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 80
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 36
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 36
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 36
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 80
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 36
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 12
  }
}
rows {
  id: 405
  cardBagPool {
    drawId: 1040101
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040102
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040103
    rangeWeight: 75
  }
  cardBagPool {
    drawId: 1040104
    rangeWeight: 75
  }
  cardBagPool {
    drawId: 1040105
    rangeWeight: 75
  }
  cardBagPool {
    drawId: 1040201
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040202
    rangeWeight: 125
  }
  cardBagPool {
    drawId: 1040203
    rangeWeight: 75
  }
  cardBagPool {
    drawId: 1040204
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040205
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040301
    rangeWeight: 125
  }
  cardBagPool {
    drawId: 1040302
    rangeWeight: 125
  }
  cardBagPool {
    drawId: 1040303
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040304
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040305
    rangeWeight: 25
  }
}
rows {
  id: 501
  cardBagPool {
    drawId: 1040401
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 3
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 3
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 3
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 3
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 2
  }
}
rows {
  id: 502
  cardBagPool {
    drawId: 1040401
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 40
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 4
  }
}
rows {
  id: 503
  cardBagPool {
    drawId: 1040401
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 9
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 9
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 9
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 35
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 9
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 6
  }
}
rows {
  id: 504
  cardBagPool {
    drawId: 1040401
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 30
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 18
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 12
  }
}
rows {
  id: 505
  cardBagPool {
    drawId: 1040401
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040402
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040403
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040501
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040502
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040503
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040504
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040601
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040602
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040603
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040604
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040605
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040701
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040702
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040703
    rangeWeight: 38
  }
  cardBagPool {
    drawId: 1040801
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040802
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040803
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040804
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1040805
    rangeWeight: 25
  }
}
rows {
  id: 601
  cardBagPool {
    drawId: 1040901
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 2
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 2
  }
}
rows {
  id: 602
  cardBagPool {
    drawId: 1040901
    rangeWeight: 80
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 80
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 80
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 16
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 16
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 16
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 16
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 8
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 4
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 4
  }
}
rows {
  id: 603
  cardBagPool {
    drawId: 1040901
    rangeWeight: 70
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 70
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 70
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 6
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 6
  }
}
rows {
  id: 604
  cardBagPool {
    drawId: 1040901
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 60
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 48
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 48
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 48
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 48
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 24
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 12
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 12
  }
}
rows {
  id: 605
  cardBagPool {
    drawId: 1040901
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040902
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040903
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040904
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040905
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040906
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1040907
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1041001
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041002
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041003
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041004
    rangeWeight: 100
  }
  cardBagPool {
    drawId: 1041005
    rangeWeight: 50
  }
  cardBagPool {
    drawId: 1041006
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1041007
    rangeWeight: 25
  }
  cardBagPool {
    drawId: 1041008
    rangeWeight: 25
  }
}
