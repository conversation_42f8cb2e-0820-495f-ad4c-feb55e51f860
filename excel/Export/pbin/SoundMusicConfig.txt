com.tencent.wea.xlsRes.table_SoundMusicConfig
excel/xls/Y_音频之背景音乐.xlsx sheet:Sheet1
rows {
  id: 1000
  playEvent: "Play_MUS_Menu_Mute"
  stopEvent: "Stop_MUS_Menu_Mute"
  bank: "MUS_Menu_Mute"
  unloadcd: 7.0
}
rows {
  id: 1001
  playEvent: "Play_MUS_Menu_Login"
  stopEvent: "Stop_MUS_Menu_Login"
  bank: "MUS_Menu_Login"
  unloadcd: 7.0
}
rows {
  id: 1002
  playEvent: "Play_MUS_Menu_Square_Day"
  stopEvent: "Stop_MUS_Menu_Square_Day"
  bank: "MUS_Menu_Square"
  unloadcd: 7.0
}
rows {
  id: 1003
  playEvent: "Play_MUS_Menu_Square_Night"
  stopEvent: "Stop_MUS_Menu_Square_Night"
  bank: "MUS_Menu_Square"
  unloadcd: 7.0
}
rows {
  id: 1004
  playEvent: "Play_MUS_Menu_FirstCharacter"
  stopEvent: "Stop_MUS_Menu_FirstCharacter"
  bank: "MUS_Menu_FirstCharcter"
  unloadcd: 7.0
}
rows {
  id: 1005
  playEvent: "Play_MUS_Menu_UserCreation"
  stopEvent: "Stop_MUS_Menu_UserCreation"
  bank: "MUS_Menu_UserCreation"
  unloadcd: 7.0
}
rows {
  id: 1006
  playEvent: "Play_MUS_Menu_FirstCharacter"
  stopEvent: "Stop_MUS_Menu_FirstCharacter"
  bank: "MUS_Menu_FirstCharcter"
  unloadcd: 7.0
}
rows {
  id: 1007
  playEvent: "Play_MUS_Menu_Mute"
  stopEvent: "Play_MUS_Menu_Mute"
  bank: "MUS_Menu_Mute"
  unloadcd: 7.0
}
rows {
  id: 1008
  playEvent: "Play_MUS_Menu_User_Villa"
  stopEvent: "Stop_MUS_Menu_User_Villa"
  bank: "MUS_Menu_User_Villa"
  unloadcd: 7.0
}
rows {
  id: 1009
  playEvent: "Play_MUS_Menu_Square_Event"
  stopEvent: "Stop_MUS_Menu_Square_Event"
  bank: "MUS_Menu_Square"
  unloadcd: 7.0
}
rows {
  id: 1010
  playEvent: "Play_MUS_Special_FireworkShow_160"
  stopEvent: "Stop_MUS_Special_FireworkShow_160"
  bank: "MUS_Special_FireworkShow"
  unloadcd: 7.0
}
rows {
  id: 1011
  playEvent: "Play_MUS_Special_Concert_Warmup"
  stopEvent: "Stop_MUS_Special_Concert_Warmup"
  bank: "MUS_Special_Concert_Warmup"
  unloadcd: 7.0
}
rows {
  id: 1012
  playEvent: "Play_MUS_Menu_SP_Spaceship"
  stopEvent: "Stop_MUS_Menu_SP_Spaceship"
  bank: "MUS_Menu_SP_Spaceship"
  unloadcd: 7.0
}
rows {
  id: 1013
  playEvent: "Play_MUS_Menu_CardSystem"
  stopEvent: "Stop_MUS_Menu_CardSystem"
  bank: "MUS_Menu_CardSystem"
  unloadcd: 2.0
}
rows {
  id: 2001
  playEvent: "Play_MUS_Menu_StandbyStage"
  stopEvent: "Stop_MUS_Menu_StandbyStage"
  bank: "MUS_Menu_StandbyStage"
  unloadcd: 7.0
}
rows {
  id: 2002
}
rows {
  id: 2003
  playEvent: "Play_MUS_Menu_Checkout"
  stopEvent: "Stop_MUS_Menu_Checkout"
  bank: "MUS_Menu_Checkout"
  unloadcd: 7.0
}
rows {
  id: 2501
  playEvent: "Play_MUS_Ingame_Preview"
  stopEvent: "Stop_MUS_Ingame_Preview"
  bank: "MUS_InGame_Common"
  unloadcd: 7.0
}
rows {
  id: 3000
  playEvent: "Play_MUS_InGame_BasicPack_Random"
  stopEvent: "Stop_MUS_InGame_BasicPack_Random"
  bank: "MUS_InGame_BasicPack"
  unloadcd: 2.0
}
rows {
  id: 3004
  playEvent: "Play_MUS_Ingame_PartyA_01"
  stopEvent: "Stop_MUS_Ingame_PartyA_01"
  bank: "MUS_InGame_PartyA"
  unloadcd: 2.0
}
rows {
  id: 3005
  playEvent: "Play_MUS_Ingame_PartyA_02"
  stopEvent: "Stop_MUS_Ingame_PartyA_02"
  bank: "MUS_InGame_PartyA"
  unloadcd: 2.0
}
rows {
  id: 3006
  playEvent: "Play_MUS_Ingame_PartyA_03"
  stopEvent: "Stop_MUS_Ingame_PartyA_03"
  bank: "MUS_InGame_PartyA"
  unloadcd: 2.0
}
rows {
  id: 3007
  playEvent: "Play_MUS_Ingame_PartyB_01"
  stopEvent: "Stop_MUS_Ingame_PartyB_01"
  bank: "MUS_InGame_PartyB"
  unloadcd: 2.0
}
rows {
  id: 3008
  playEvent: "Play_MUS_Ingame_PartyB_02"
  stopEvent: "Stop_MUS_Ingame_PartyB_02"
  bank: "MUS_InGame_PartyB"
  unloadcd: 2.0
}
rows {
  id: 3009
  playEvent: "Play_MUS_Ingame_PartyB_03"
  stopEvent: "Stop_MUS_Ingame_PartyB_03"
  bank: "MUS_InGame_PartyB"
  unloadcd: 2.0
}
rows {
  id: 3010
  playEvent: "Play_MUS_Ingame_Race_01"
  stopEvent: "Stop_MUS_Ingame_Race_01"
  bank: "MUS_InGame_Race"
  unloadcd: 2.0
}
rows {
  id: 3011
  playEvent: "Play_MUS_Ingame_Race_02"
  stopEvent: "Stop_MUS_Ingame_Race_02"
  bank: "MUS_InGame_Race"
  unloadcd: 2.0
}
rows {
  id: 3012
  playEvent: "Play_MUS_Ingame_Race_03"
  stopEvent: "Stop_MUS_Ingame_Race_03"
  bank: "MUS_InGame_Race"
  unloadcd: 2.0
}
rows {
  id: 3013
  playEvent: "Play_MUS_Ingame_Race_04"
  stopEvent: "Stop_MUS_Ingame_Race_04"
  bank: "MUS_InGame_Race"
  unloadcd: 2.0
}
rows {
  id: 3014
  playEvent: "Play_Mus_Ingame_PartyA_random"
  stopEvent: "Stop_Mus_Ingame_PartyA_random"
  bank: "MUS_InGame_PartyA"
  unloadcd: 2.0
}
rows {
  id: 3015
  playEvent: "Play_Mus_Ingame_PartyB_random"
  stopEvent: "Stop_Mus_Ingame_PartyB_random"
  bank: "MUS_InGame_PartyB"
  unloadcd: 2.0
}
rows {
  id: 3016
  playEvent: "Play_Mus_Ingame_Race_random"
  stopEvent: "Stop_Mus_Ingame_Race_random"
  bank: "MUS_InGame_Race"
  unloadcd: 2.0
}
rows {
  id: 3018
  playEvent: "Play_MUS_Ingame_PartyA_04"
  stopEvent: "Stop_MUS_Ingame_PartyA_04"
  bank: "MUS_InGame_PartyA"
  unloadcd: 2.0
}
rows {
  id: 3019
  playEvent: "Play_MUS_Ingame_PartyB_04"
  stopEvent: "Stop_MUS_Ingame_PartyB_04"
  bank: "MUS_InGame_PartyB"
  unloadcd: 2.0
}
rows {
  id: 3020
  playEvent: "Play_MUS_Ingame_Race_05"
  stopEvent: "Stop_MUS_Ingame_Race_05"
  bank: "MUS_InGame_Race"
  unloadcd: 2.0
}
rows {
  id: 3022
  playEvent: "Play_MUS_Ingame_BossFight_01"
  stopEvent: "Stop_MUS_Ingame_BossFight_01"
  bank: "MUS_Ingame_BossFight"
  unloadcd: 2.0
}
rows {
  id: 3023
  playEvent: "Play_MUS_Ingame_BossFight_02"
  stopEvent: "Stop_MUS_Ingame_BossFight_02"
  bank: "MUS_Ingame_BossFight"
  unloadcd: 2.0
}
rows {
  id: 3024
  playEvent: "Play_MUS_Ingame_BossFight_Random"
  stopEvent: "Stop_MUS_Ingame_BossFight_Random"
  bank: "MUS_Ingame_BossFight"
  unloadcd: 2.0
}
rows {
  id: 3017
  playEvent: "Play_Mus_Ingame_Race_springfestival_random"
  stopEvent: "Stop_Mus_Ingame_Race_springfestival_random"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3001
  playEvent: "Play_MUS_Ingame_springfestival_01"
  stopEvent: "Stop_MUS_Ingame_springfestival_01"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3002
  playEvent: "Play_MUS_Ingame_springfestival_02"
  stopEvent: "Stop_MUS_Ingame_springfestival_02"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3035
  playEvent: "Play_MUS_InGame_SpringFestDance_01"
  stopEvent: "Stop_MUS_InGame_SpringFestDance_01"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3036
  playEvent: "Play_MUS_InGame_SpringFestDance_02"
  stopEvent: "Stop_MUS_InGame_SpringFestDance_02"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3037
  playEvent: "Play_MUS_InGame_SpringFestDance_03"
  stopEvent: "Stop_MUS_InGame_SpringFestDance_03"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3038
  playEvent: "Play_MUS_InGame_SpringFestDance_04"
  stopEvent: "Stop_MUS_InGame_SpringFestDance_04"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3043
  playEvent: "Play_MUS_InGame_SpringFestDance_05"
  stopEvent: "Stop_MUS_InGame_SpringFestDance_05"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3044
  playEvent: "Play_MUS_Ingame_M10_Party_02"
  switchState: "Main_M10_RockTime"
  switchState: "Main_M10_Normal"
  stateGroup: "Main_M10_MUS_State"
  stopEvent: "Stop_MUS_Ingame_M10_Party_02"
  bank: "MUS_Ingame_M10"
  unloadcd: 2.0
}
rows {
  id: 3045
  playEvent: "Play_MUS_Ingame_M10_Race_Random"
  stopEvent: "Stop_MUS_Ingame_M10_Race_Random"
  bank: "MUS_Ingame_M10"
  unloadcd: 2.0
}
rows {
  id: 3046
  playEvent: "Play_MUS_Ingame_NightTheme"
  stopEvent: "Stop_MUS_Ingame_NightTheme"
  bank: "MUS_Ingame_NightTheme"
  unloadcd: 2.0
}
rows {
  id: 3047
  playEvent: "Play_MUS_Ingame_S10_Tang"
  stopEvent: "Stop_MUS_Ingame_S10_Tang"
  bank: "MUS_Ingame_S10_Tang"
  unloadcd: 2.0
}
rows {
  id: 3030
  playEvent: "Play_MUS_InGame_LetsRide_01"
  stopEvent: "Stop_MUS_InGame_LetsRide_01"
  bank: "MUS_InGame_LetsRide"
  unloadcd: 2.0
}
rows {
  id: 3031
  playEvent: "Play_MUS_InGame_LetsRide_02"
  stopEvent: "Stop_MUS_InGame_LetsRide_02"
  bank: "MUS_InGame_LetsRide"
  unloadcd: 2.0
}
rows {
  id: 3032
  playEvent: "Play_MUS_InGame_LetsRide_Cart_02"
  stopEvent: "Stop_MUS_InGame_LetsRide_Cart_02"
  bank: "MUS_InGame_LetsRide_Cart"
  unloadcd: 2.0
}
rows {
  id: 3033
  playEvent: "Play_MUS_InGame_LetsRide_Cart_01"
  stopEvent: "Stop_MUS_InGame_LetsRide_Cart_01"
  bank: "MUS_InGame_LetsRide_Cart"
  unloadcd: 2.0
}
rows {
  id: 3034
  playEvent: "Play_MUS_InGame_SpringFestDance_04"
  stopEvent: "Stop_MUS_InGame_SpringFestDance_04"
  bank: "MUS_InGame_Chinese"
  unloadcd: 2.0
}
rows {
  id: 3040
  playEvent: "Play_MUS_InGame_LetsTower_01"
  switchState: "LetsTower_Boss"
  switchState: "LetsTower_PVP"
  switchState: "LetsTower_Normal"
  stateGroup: "LetsTower_MUS_State"
  stopEvent: "Stop_MUS_InGame_LetsTower_01"
  bank: "MUS_InGame_LetsTower"
  unloadcd: 2.0
}
rows {
  id: 3041
  playEvent: "Play_MUS_InGame_LetsTower_TD"
  switchState: "LetsTower_TD_BattleWave"
  switchState: "LetsTower_TD_Normal"
  switchState: "LetsTower_TD_PVP"
  stateGroup: "LetsTower_TD_MUS_State"
  stopEvent: "Stop_MUS_InGame_LetsTower_TD"
  bank: "MUS_InGame_LetsTower"
  unloadcd: 2.0
}
rows {
  id: 3042
  playEvent: "Play_MUS_InGame_LetsTower_TDS"
  switchState: "LetsTower_TDS_BattleWave"
  switchState: "LetsTower_TDS_PVP"
  switchState: "LetsTower_TDS_Normal"
  stateGroup: "LetsTower_TDS_MUS_State"
  stopEvent: "Stop_MUS_InGame_LetsTower_TDS"
  bank: "MUS_InGame_LetsTower"
  unloadcd: 2.0
}
rows {
  id: 5000
  playEvent: "Play_MUS_InGame_LetsBall_01"
  switchState: "LetsBall_MUS_Ingame"
  switchState: "LetsBall_MUS_Standby"
  stateGroup: "LetsBall_MUS_State"
  stopEvent: "Stop_MUS_InGame_LetsBall_01"
  bank: "MUS_InGame_LetsBall"
  unloadcd: 2.0
}
rows {
  id: 5001
  playEvent: "Play_MUS_InGame_LetsBrainstorm"
  switchState: "LetsBrainstorm_Standby"
  switchState: "LetsBrainstorm_Quiz"
  stateGroup: "LetsBrainstorm_MUS_State"
  stopEvent: "Stop_MUS_InGame_LetsBrainstorm"
  bank: "MUS_InGame_LetsBrainstorm"
  unloadcd: 2.0
}
rows {
  id: 5002
  playEvent: "Play_MUS_InGame_LetsRide_SpeedDrifters_01"
  stopEvent: "Stop_MUS_InGame_LetsRide_SpeedDrifters_01"
  bank: "MUS_InGame_LetsRide_SpeedDrifters"
  unloadcd: 2.0
}
rows {
  id: 5003
  playEvent: "Play_MUS_InGame_LetsRide_SpeedDrifters_02"
  stopEvent: "Stop_MUS_InGame_LetsRide_SpeedDrifters_02"
  bank: "MUS_InGame_LetsRide_SpeedDrifters"
  unloadcd: 2.0
}
rows {
  id: 5004
  playEvent: "Play_MUS_InGame_LetsRide_SpeedDrifters_Song"
  stopEvent: "Stop_MUS_InGame_LetsRide_SpeedDrifters_Song"
  bank: "MUS_InGame_LetsRide_SpeedDrifters_Song"
  unloadcd: 2.0
}
rows {
  id: 5005
  playEvent: "Play_MUS_InGame_LetsRide_SpeedDrifters_03"
  stopEvent: "Stop_MUS_InGame_LetsRide_SpeedDrifters_03"
  bank: "MUS_InGame_LetsRide_SpeedDrifters"
  unloadcd: 2.0
}
rows {
  id: 5006
  playEvent: "Play_MUS_InGame_LetsTower_OMD"
  switchState: "LetsTower_OMD_BattleWave"
  switchState: "LetsTower_OMD_Normal"
  switchState: "LetsTower_OMD_Tavern"
  switchState: "LetsTower_OMD_Win"
  switchState: "LetsTower_OMD_Lose"
  stateGroup: "LetsTower_OMD_MUS_State"
  stopEvent: "Stop_MUS_InGame_LetsTower_OMD"
  bank: "MUS_InGame_LetsTower_OMD"
  unloadcd: 2.0
}
rows {
  id: 5007
  playEvent: "Play_MUS_Ingame_LetsFarm"
  stopEvent: "Stop_MUS_Ingame_LetsFarm"
  bank: "MUS_Ingame_LetsFarm"
  unloadcd: 2.0
}
rows {
  id: 5009
  playEvent: "Play_MUS_Ingame_LetsFarm_Night"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Night"
  bank: "MUS_Ingame_LetsFarm"
  unloadcd: 2.0
}
rows {
  id: 5010
  playEvent: "Play_MUS_Ingame_LetsFarm_Visit"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Visit"
  bank: "MUS_Ingame_LetsFarm"
  unloadcd: 2.0
}
rows {
  id: 5101
  playEvent: "Play_MUS_Ingame_LetsFarm_Cabin_Edit"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Cabin_Edit"
  bank: "MUS_Ingame_LetsFarm_Cabin"
  unloadcd: 2.0
}
rows {
  id: 5102
  playEvent: "Play_MUS_Ingame_LetsFarm_Cabin_Day"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Cabin_Day"
  bank: "MUS_Ingame_LetsFarm_Cabin"
  unloadcd: 2.0
}
rows {
  id: 5103
  playEvent: "Play_MUS_Ingame_LetsFarm_Cabin_Night"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Cabin_Night"
  bank: "MUS_Ingame_LetsFarm_Cabin"
  unloadcd: 2.0
}
rows {
  id: 5104
  playEvent: "Play_MUS_Ingame_LetsFarm_Cook_Day"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Cook_Day"
  bank: "MUS_Ingame_LetsFarm_Cook"
  unloadcd: 2.0
}
rows {
  id: 5105
  playEvent: "Play_MUS_Ingame_LetsFarm_Cook_Night"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Cook_Night"
  bank: "MUS_Ingame_LetsFarm_Cook"
  unloadcd: 2.0
}
rows {
  id: 5106
  playEvent: "Play_MUS_Ingame_LetsFarm_Cook_Closed"
  stopEvent: "Stop_MUS_Ingame_LetsFarm_Cook_Closed"
  bank: "MUS_Ingame_LetsFarm_Cook"
  unloadcd: 2.0
}
rows {
  id: 5008
  playEvent: "Play_MUS_UGC_Square_01"
  stopEvent: "Stop_MUS_UGC_Square_01"
  bank: "MUS_UGC_Square_01"
  unloadcd: 2.0
}
rows {
  id: 5011
  playEvent: "Play_MUS_UGC_Square_02"
  stopEvent: "Stop_MUS_UGC_Square_02"
  bank: "MUS_UGC_Square_02"
  unloadcd: 2.0
}
rows {
  id: 5012
  playEvent: "Play_MUS_UGC_Square_03"
  stopEvent: "Stop_MUS_UGC_Square_03"
  bank: "MUS_UGC_Square_03"
  unloadcd: 2.0
}
rows {
  id: 5013
  playEvent: "Play_MUS_UGC_Square_04"
  stopEvent: "Stop_MUS_UGC_Square_04"
  bank: "MUS_UGC_Square_04"
  unloadcd: 2.0
}
rows {
  id: 5014
  playEvent: "Play_MUS_UGC_Square_Taihedian"
  stopEvent: "Stop_MUS_UGC_Square_Taihedian"
  bank: "MUS_UGC_Square_Taihedian"
  unloadcd: 2.0
}
rows {
  id: 5015
  playEvent: "Play_MUS_UGC_Square_Taimiao"
  stopEvent: "Stop_MUS_UGC_Square_Taimiao"
  bank: "MUS_UGC_Square_Taimiao"
  unloadcd: 2.0
}
rows {
  id: 5016
  playEvent: "Play_MUS_UGC_Square_Xiaowanzi"
  stopEvent: "Stop_MUS_UGC_Square_Xiaowanzi"
  bank: "MUS_UGC_Square_Xiaowanzi"
  unloadcd: 2.0
}
rows {
  id: 5017
  playEvent: "Play_MUS_UGC_Square_Zhonggulou"
  stopEvent: "Stop_MUS_UGC_Square_Zhonggulou"
  bank: "MUS_UGC_Square_Zhonggulou"
  unloadcd: 2.0
}
rows {
  id: 5018
  playEvent: "Play_MUS_UGC_Square_Winter_01"
  stopEvent: "Stop_MUS_UGC_Square_Winter_01"
  bank: "MUS_UGC_Square_Winter_01"
  unloadcd: 2.0
}
rows {
  id: 5019
  playEvent: "Play_MUS_UGC_Square_Winter_02"
  stopEvent: "Stop_MUS_UGC_Square_Winter_02"
  bank: "MUS_UGC_Square_Winter_02"
  unloadcd: 2.0
}
rows {
  id: 5020
  playEvent: "Play_MUS_UGC_Square_Winter_03"
  stopEvent: "Stop_MUS_UGC_Square_Winter_03"
  bank: "MUS_UGC_Square_Winter_03"
  unloadcd: 2.0
}
rows {
  id: 5021
  playEvent: "Play_MUS_UGC_Square_AprilFoolsDay"
  stopEvent: "Stop_MUS_UGC_Square_AprilFoolsDay"
  bank: "MUS_UGC_Square_AprilFoolsDay"
  unloadcd: 2.0
}
rows {
  id: 5022
  playEvent: "Play_MUS_UGC_Square_Miaomiaodao"
  stopEvent: "Stop_MUS_UGC_Square_Miaomiaodao"
  bank: "MUS_UGC_Square_Miaomiaodao"
  unloadcd: 2.0
}
rows {
  id: 5023
  playEvent: "Play_MUS_UGC_Square_Zaomengxing"
  stopEvent: "Stop_MUS_UGC_Square_Zaomengxing"
  bank: "MUS_UGC_Square_Zaomengxing"
  unloadcd: 2.0
}
rows {
  id: 5024
  playEvent: "Play_MUS_UGC_Square_QingLvParty"
  stopEvent: "Stop_MUS_UGC_Square_QingLvParty"
  bank: "MUS_UGC_Square_QingLvParty"
  unloadcd: 2.0
}
rows {
  id: 5025
  playEvent: "Play_MUS_UGC_Square_PaoKu"
  stopEvent: "Stop_MUS_UGC_Square_PaoKu"
  bank: "MUS_UGC_Square_PaoKu"
  unloadcd: 2.0
}
rows {
  id: 5026
  playEvent: "Play_MUS_UGC_Square_ChildrenPlayground"
  stopEvent: "Stop_MUS_UGC_Square_ChildrenPlayground"
  bank: "MUS_UGC_Square_ChildrenPlayground"
  unloadcd: 2.0
}
rows {
  id: 5027
  playEvent: "Play_MUS_UGC_Square_Biyeji"
  stopEvent: "Stop_MUS_UGC_Square_Biyeji"
  bank: "MUS_UGC_Square_Biyeji"
  unloadcd: 2.0
}
rows {
  id: 5028
  playEvent: "Play_MUS_UGC_Square_Daofantiangang"
  stopEvent: "Stop_MUS_UGC_Square_Daofantiangang"
  bank: "MUS_UGC_Square_Daofantiangang"
  unloadcd: 2.0
}
rows {
  id: 5029
  playEvent: "Play_MUS_UGC_Square_XiaoHongHu"
  stopEvent: "Stop_MUS_UGC_Square_XiaoHongHu"
  bank: "MUS_UGC_Square_XiaoHongHu"
  unloadcd: 2.0
}
rows {
  id: 5030
  playEvent: "Play_MUS_UGC_Square_Chiikawa"
  stopEvent: "Stop_MUS_UGC_Square_Chiikawa"
  bank: "MUS_UGC_Square_Chiikawa"
  unloadcd: 2.0
}
rows {
  id: 5031
  playEvent: "Play_MUS_UGC_Square_GuoJiaJia"
  stopEvent: "Stop_MUS_UGC_Square_GuoJiaJia"
  bank: "MUS_UGC_Square_GuoJiaJia"
  unloadcd: 2.0
}
rows {
  id: 4001
  playEvent: "Play_MUS_HideAndSeek_Standby"
  stopEvent: "Play_MUS_HideAndSeek_Standby_Stop"
  bank: "MUS_InGame_HideAndSeek"
  unloadcd: 2.0
}
rows {
  id: 4002
  playEvent: "Play_MUS_Rich"
  stopEvent: "Play_MUS_Rich_Stop"
  bank: "MUS_InGame_Rich"
  unloadcd: 2.0
}
rows {
  id: 4004
  playEvent: "Play_MUS_ToyRun_Ingame_Commoner"
  bank: "MUS_InGame_ToyRun"
  unloadcd: 2.0
}
rows {
  id: 4005
  playEvent: "Play_MUS_ToyRun_Ingame_Police"
  bank: "MUS_InGame_ToyRun"
  unloadcd: 2.0
}
rows {
  id: 4006
  playEvent: "Play_MUS_ToyRun_Ingame_Commoner_China"
  bank: "MUS_InGame_ToyRun"
  unloadcd: 2.0
}
rows {
  id: 4007
  playEvent: "Play_MUS_ToyRun_Ingame_Police_China"
  bank: "MUS_InGame_ToyRun"
  unloadcd: 2.0
}
rows {
  id: 4010
  playEvent: "Play_MUS_E3_Ingame_China"
  stopEvent: "Play_MUS_E3_Ingame_China_Stop"
  bank: "MUS_InGame_E3_Baisc"
  unloadcd: 2.0
}
rows {
  id: 4011
  playEvent: "Play_MUS_E3_Ingame_Castle"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4012
  playEvent: "Play_MUS_E3_Ingame_Forest"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4013
  playEvent: "Play_MUS_E3_Ingame_School"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4014
  playEvent: "Play_MUS_E3_Ingame_Moon"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4015
  playEvent: "Play_MUS_E3_Ingame_Amusement"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4016
  playEvent: "Play_MUS_E3_Ingame_Town"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4017
  playEvent: "Play_MUS_E3_Ingame_Icelake"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4018
  playEvent: "Play_MUS_E3_Ingame_Valley"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4019
  playEvent: "Play_MUS_E3_Ingame_Treasurehunt"
  stopEvent: "Play_MUS_E3_Ingame_Stop"
  bank: "MUS_InGame_E3"
  unloadcd: 2.0
}
rows {
  id: 4020
  playEvent: "Play_MUS_Menu_Competition"
  stopEvent: "Stop_MUS_Menu_Competition"
  bank: "MUS_Menu_Competition"
  unloadcd: 2.0
}
rows {
  id: 4021
  playEvent: "Play_MUS_Menu_Competition_Werewolf"
  stopEvent: "Stop_MUS_Menu_Competition_Werewolf"
  bank: "MUS_Menu_Competition_Werewolf"
  unloadcd: 2.0
}
rows {
  id: 4030
  playEvent: "Play_MUS_Ingame_Chase_Chinese_DarkStar"
  stopEvent: "Play_MUS_Ingame_Chase_Chinese_Stop"
  bank: "MUS_InGame_Chase_Old"
  unloadcd: 2.0
}
rows {
  id: 4031
  playEvent: "Play_MUS_Ingame_Chase_Chinese_StarBaby"
  stopEvent: "Play_MUS_Ingame_Chase_Chinese_Stop"
  bank: "MUS_InGame_Chase_Old"
  unloadcd: 2.0
}
rows {
  id: 4032
  playEvent: "Play_MUS_Ingame_Chase_Remains_DarkStar"
  stopEvent: "Play_MUS_Ingame_Chase_Remains_Stop"
  bank: "MUS_InGame_Chase_Old"
  unloadcd: 2.0
}
rows {
  id: 4033
  playEvent: "Play_MUS_Ingame_Chase_Remains_StarBaby"
  stopEvent: "Play_MUS_Ingame_Chase_Remains_Stop"
  bank: "MUS_InGame_Chase_Old"
  unloadcd: 2.0
}
rows {
  id: 4034
  playEvent: "Play_MUS_InGame_Chase_Chinoiserie_DarkStar"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Chinoiserie_DarkStar_Stop"
  bank: "MUS_InGame_Chase_Chinoiserie"
  unloadcd: 2.0
}
rows {
  id: 4035
  playEvent: "Play_MUS_InGame_Chase_Chinoiserie_StarBaby"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Chinoiserie_StarBaby_Stop"
  bank: "MUS_InGame_Chase_Chinoiserie"
  unloadcd: 2.0
}
rows {
  id: 4036
  playEvent: "Play_MUS_InGame_Chase_Relic_DarkStar"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Relic_DarkStar_Stop"
  bank: "MUS_InGame_Chase_Relic"
  unloadcd: 2.0
}
rows {
  id: 4037
  playEvent: "Play_MUS_InGame_Chase_Relic_StarBaby"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Relic_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Relic_StarBaby_Stop"
  bank: "MUS_InGame_Chase_Relic"
  unloadcd: 2.0
}
rows {
  id: 4038
  playEvent: "Play_MUS_InGame_Chase_Castle_DarkStar"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Castle_DarkStar_Stop"
  bank: "MUS_InGame_Chase_Castle"
  unloadcd: 2.0
}
rows {
  id: 4039
  playEvent: "Play_MUS_InGame_Chase_Castle_StarBaby"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Castle_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Castle_StarBaby_Stop"
  bank: "MUS_InGame_Chase_Castle"
  unloadcd: 2.0
}
rows {
  id: 4040
  playEvent: "Play_MUS_InGame_Chase_Pingding_Mountain_DarkStar"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Ready"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_TangSanzang"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Wukong"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Pingding_Mountain_DarkStar_Stop"
  bank: "MUS_InGame_Chase_Pingding_Mountain"
  unloadcd: 2.0
}
rows {
  id: 4041
  playEvent: "Play_MUS_InGame_Chase_Pingding_Mountain_StarBaby"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Escape_04"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Ready"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_TangSanzang"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_Wukong"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_BaJie"
  switchEvent: "MUS_InGame_Chase_Pingding_Mountain_StarBaby_ShaSeng"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Pingding_Mountain_StarBaby_Stop"
  bank: "MUS_InGame_Chase_Pingding_Mountain"
  unloadcd: 2.0
}
rows {
  id: 4042
  playEvent: "Play_MUS_MayDay_Silence"
  stopEvent: "Play_MUS_MayDay_Silence_Stop"
  bank: "MUS_InGame_MayDay"
  unloadcd: 2.0
}
rows {
  id: 4043
  playEvent: "Play_MUS_MayDay_Outdoor"
  stopEvent: "Play_MUS_MayDay_Outdoor_Stop"
  bank: "MUS_InGame_MayDay"
  unloadcd: 2.0
}
rows {
  id: 4044
  playEvent: "Play_MUS_InGame_Chase_GaiXia_City_DarkStar"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_GaiXia_City_DarkStar_Stop"
  bank: "MUS_InGame_Chase_GaiXia_City"
  unloadcd: 2.0
}
rows {
  id: 4045
  playEvent: "Play_MUS_InGame_Chase_GaiXia_City_StarBaby"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_GaiXia_City_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_GaiXia_City_StarBaby_Stop"
  bank: "MUS_InGame_Chase_GaiXia_City"
  unloadcd: 2.0
}
rows {
  id: 4046
  playEvent: "Play_MUS_InGame_Chase_Flaming_Mountain_DarkStar"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Ready"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_TangSanzang"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Wukong"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Flaming_Mountain_DarkStar_Stop"
  bank: "MUS_InGame_Chase_Flaming_Mountain"
  unloadcd: 2.0
}
rows {
  id: 4047
  playEvent: "Play_MUS_InGame_Chase_Flaming_Mountain_StarBaby"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Escape_04"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Ready"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_TangSanzang"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_Wukong"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_BaJie"
  switchEvent: "MUS_InGame_Chase_Flaming_Mountain_StarBaby_ShaSeng"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Flaming_Mountain_StarBaby_Stop"
  bank: "MUS_InGame_Chase_Flaming_Mountain"
  unloadcd: 2.0
}
rows {
  id: 4048
  playEvent: "Play_MUS_InGame_Chase_Flaming_City_DarkStar"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Flaming_City_DarkStar_Stop"
  bank: "MUS_InGame_Chase_Flaming_City"
  unloadcd: 2.0
}
rows {
  id: 4049
  playEvent: "Play_MUS_InGame_Chase_Flaming_City_StarBaby"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Flaming_City_StarBaby_Ready"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Flaming_City_StarBaby_Stop"
  bank: "MUS_InGame_Chase_Flaming_City"
  unloadcd: 2.0
}
rows {
  id: 4050
  playEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar"
  switchEvent: "MUS_InGame_Chase_Night_City_DarkStar_Chase_01"
  switchEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_Chase_02"
  switchEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_Chase_03"
  switchEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_MissionFail"
  switchEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_MissionVictory"
  switchEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_Ready"
  switchEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_Chase_04"
  switchEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_Fury"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Night_City_DarkStar_Stop"
  bank: "MUS_InGame_Chase_Night_City"
  unloadcd: 2.0
}
rows {
  id: 4051
  playEvent: "Play_MUS_InGame_Chase_Night_City_StarBaby"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_Ready"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_Angel"
  switchEvent: "MUS_InGame_Chase_Night_City_StarBaby_Werewolf"
  switchGroup: "MUS_InGame_Chase_Relic_StarBaby"
  stopEvent: "Play_MUS_InGame_Chase_Night_City_StarBaby_Stop"
  bank: "MUS_InGame_Chase_Night_City"
  unloadcd: 2.0
}
rows {
  id: 4052
  playEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_DarkStar_Chase_01"
  switchEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_Chase_02"
  switchEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_Chase_03"
  switchEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_MissionFail"
  switchEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_MissionVictory"
  switchEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_Ready"
  switchEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_Chase_04"
  switchEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_Fury"
  stopEvent: "Play_MUS_InGame_Chase_DarkValley_City_DarkStar_Stop"
  bank: "MUS_InGame_Chase_DarkValley_City"
  unloadcd: 2.0
}
rows {
  id: 4053
  playEvent: "Play_MUS_InGame_Chase_DarkValley_City_StarBaby"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_Escape_01"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_Escape_02"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_Escape_03"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_MissionFail"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_MissionVictory"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_Ready"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_Angel"
  switchEvent: "MUS_InGame_Chase_DarkValley_City_StarBaby_Werewolf"
  stopEvent: "Play_MUS_InGame_Chase_DarkValley_City_StarBaby_Stop"
  bank: "MUS_InGame_Chase_DarkValley_City"
  unloadcd: 2.0
}
rows {
  id: 4090
  playEvent: "Play_MUS_MayDay_Palace"
  stopEvent: "Play_MUS_MayDay_Palace_Stop"
  bank: "MUS_InGame_MayDay"
  unloadcd: 2.0
}
rows {
  id: 4100
  playEvent: "Play_MUS_Metro_Intro"
  stopEvent: "Play_MUS_Metro_Intro_Stop"
  bank: "MUS_Ingame_Metro"
  unloadcd: 2.0
}
rows {
  id: 4501
  playEvent: "MUS_Ingame_ArmsRace"
  switchState: "Normal_Ingame"
  switchState: "Player_Decisive"
  switchState: "Enemy_Decisive"
  stateGroup: "ArmsRace"
  stopEvent: "MUS_Ingame_ArmsRace_Stop"
  bank: "MUS_InGame_ArmsRace"
  unloadcd: 2.0
}
rows {
  id: 4502
  playEvent: "MUS_Ingame_ShengHua"
  switchEvent: "Human_Normal"
  switchEvent: "Human_HeroAppear"
  switchEvent: "Human_Danger"
  switchEvent: "Monster_Normal"
  switchEvent: "Monster_HeroAppear"
  switchEvent: "Monster_Danger"
  switchGroup: "ShengHua_Danger"
  switchState: "Human"
  switchState: "Monster"
  stateGroup: "ShengHua"
  stopEvent: "MUS_Ingame_ShengHua_Stop"
  bank: "MUS_InGame_ShengHua"
  unloadcd: 2.0
}
rows {
  id: 4503
  playEvent: "MUS_Ingame_BR_Start"
  stopEvent: "MUS_Ingame_BR_Stop"
  bank: "MUS_InGame_BR"
  unloadcd: 2.0
}
rows {
  id: 4504
  playEvent: "MUS_Ingame_Rogue_Start"
  switchState: "BossBattle,HardBattle,NoBattle,NormalBattle,XieDao"
  stateGroup: "MUS_Rogue"
  stopEvent: "MUS_Ingame_Rogue_Stop"
  bank: "MUS_InGame_Rogue"
  unloadcd: 2.0
}
rows {
  id: 4505
  playEvent: "MUS_Ingame_ShengHua_China"
  switchEvent: "Human_Normal"
  switchEvent: "Human_HeroAppear"
  switchEvent: "Human_Danger"
  switchEvent: "Monster_Normal"
  switchEvent: "Monster_HeroAppear"
  switchEvent: "Monster_Danger"
  switchGroup: "ShengHua_Danger"
  switchState: "Human"
  switchState: "Monster"
  stateGroup: "ShengHua"
  stopEvent: "MUS_Ingame_ShengHua_China_Stop"
  bank: "MUS_InGame_ShengHua"
  unloadcd: 2.0
}
rows {
  id: 4506
  playEvent: "MUS_Ingame_ArmsRace_Bounty"
  switchState: "Ingame"
  switchState: "Lose"
  switchState: "Win"
  stateGroup: "MUS_ArmsRace_Bounty"
  stopEvent: "MUS_Ingame_ArmsRace_Bounty_Stop"
  bank: "MUS_InGame_ArmsRace_Bounty"
  unloadcd: 2.0
}
rows {
  id: 4507
  playEvent: "MUS_Ingame_CheLi_StartCount"
  stopEvent: "MUS_Ingame_CheLi_StartCount_Stop"
  bank: "MUS_InGame_CheLi"
  unloadcd: 2.0
}
rows {
  id: 4508
  playEvent: "MUS_Ingame_CheLi_Box"
  switchState: "Box_1PGet"
  switchState: "Box_NotGet"
  stateGroup: "MUS_CheLi"
  stopEvent: "MUS_Ingame_CheLi_Box_Stop"
  bank: "MUS_InGame_CheLi"
  unloadcd: 2.0
}
rows {
  id: 4509
}
rows {
  id: 4510
}
rows {
  id: 7000
}
rows {
  id: 7001
}
rows {
  id: 7002
}
rows {
  id: 7003
}
rows {
  id: 7004
}
rows {
  id: 7005
}
rows {
  id: 7006
}
rows {
  id: 7007
}
rows {
  id: 7008
}
rows {
  id: 7501
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_BanPick"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_BanPick"
  bank: "MUS_InGame_LetsMOBA_HOK_BanPick"
  unloadcd: 2.0
}
rows {
  id: 7502
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_BanPick_VS"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_BanPick_VS"
  bank: "MUS_InGame_LetsMOBA_HOK_BanPick_VS"
  unloadcd: 2.0
}
rows {
  id: 7503
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_Prepare"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_Prepare"
  bank: "MUS_InGame_LetsMOBA_HOK_Prepare"
  unloadcd: 2.0
}
rows {
  id: 7504
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_OpeningSEQ"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_OpeningSEQ"
  bank: "MUS_InGame_LetsMOBA_HOK_OpeningSEQ"
  unloadcd: 2.0
}
rows {
  id: 7505
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_Battle"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_Battle"
  bank: "MUS_InGame_LetsMOBA_HOK_Battle"
  unloadcd: 2.0
}
rows {
  id: 7506
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_MidStage"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_MidStage"
  bank: "MUS_InGame_LetsMOBA_HOK_MidStage"
  unloadcd: 2.0
}
rows {
  id: 7507
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_ChooseStart"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_ChooseStart"
  bank: "MUS_InGame_LetsMOBA_HOK_ChooseStart"
  unloadcd: 2.0
}
rows {
  id: 7508
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_Bye"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_Bye"
  bank: "MUS_InGame_LetsMOBA_HOK_Bye"
  unloadcd: 2.0
}
rows {
  id: 7509
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_Battle_Final"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_Battle_Final"
  bank: "MUS_InGame_LetsMOBA_HOK_Battle_Final"
  unloadcd: 2.0
}
rows {
  id: 7510
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_FinalSEQ"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_FinalSEQ"
  bank: "MUS_InGame_LetsMOBA_HOK_FinalSEQ"
  unloadcd: 2.0
}
rows {
  id: 7511
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_FinalVictory"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_FinalVictory"
  bank: "MUS_InGame_LetsMOBA_HOK_FinalVictory"
  unloadcd: 2.0
}
rows {
  id: 7601
  playEvent: "Play_MUS_InGame_LetsMOBA_BR_BanPick"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_BR_BanPick"
  bank: "MUS_InGame_LetsMOBA_BR_BanPick"
  unloadcd: 2.0
}
rows {
  id: 7602
  playEvent: "Play_MUS_InGame_LetsMOBA_BR_BanPick_VS"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_BR_BanPick_VS"
  bank: "MUS_InGame_LetsMOBA_BR_BanPick_VS"
  unloadcd: 2.0
}
rows {
  id: 7603
  playEvent: "Play_MUS_InGame_LetsMOBA_BR_Battle"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_BR_Battle"
  bank: "MUS_InGame_LetsMOBA_BR_Battle"
  unloadcd: 2.0
}
rows {
  id: 7604
  playEvent: "Play_MUS_InGame_LetsMOBA_BR_Battle_SkyIsland"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_BR_Battle_SkyIsland"
  bank: "MUS_InGame_LetsMOBA_BR_Battle_SkyIsland"
  unloadcd: 2.0
}
rows {
  id: 7605
  playEvent: "Play_MUS_InGame_LetsMOBA_BR_Battle_Castle"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_BR_Battle_Castle"
  bank: "MUS_InGame_LetsMOBA_BR_Battle_Castle"
  unloadcd: 2.0
}
rows {
  id: 7606
  playEvent: "Play_MUS_InGame_LetsMOBA_BR_Battle_Esports"
  switchState: "LetsMOBA_MUS_Battle_BR_Default"
  switchState: "LetsMOBA_MUS_Battle_BR_FinalCicle"
  stateGroup: "LetsMOBA_MUS_Battle_BR"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_BR_Battle_Esports"
  bank: "MUS_InGame_LetsMOBA_BR_Battle_Esports"
  unloadcd: 2.0
}
rows {
  id: 7701
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_5v5_VS"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_5v5_VS"
  bank: "MUS_InGame_LetsMOBA_HOK_5v5_VS"
  unloadcd: 2.0
}
rows {
  id: 7702
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_5v5"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_5v5"
  bank: "MUS_InGame_LetsMOBA_HOK_5v5"
  unloadcd: 2.0
}
rows {
  id: 7703
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_5v5_Battle"
  switchState: "LetsMOBA_MUS_Battle_5v5_Stage_01"
  switchState: "LetsMOBA_MUS_Battle_5v5_Stage_02"
  switchState: "LetsMOBA_MUS_Battle_5v5_Stage_03"
  stateGroup: "LetsMOBA_MUS_Battle_5v5"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_5v5_Battle"
  bank: "MUS_InGame_LetsMOBA_HOK_5v5"
  unloadcd: 2.0
}
rows {
  id: 7704
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_5v5_Victory"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_5v5_Victory"
  bank: "MUS_InGame_LetsMOBA_HOK_5v5_Victory"
  unloadcd: 2.0
}
rows {
  id: 7705
  playEvent: "Play_MUS_InGame_LetsMOBA_HOK_5v5_Defeat"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_HOK_5v5_Defeat"
  bank: "MUS_InGame_LetsMOBA_HOK_5v5_Defeat"
  unloadcd: 2.0
}
rows {
  id: 7801
  playEvent: "Play_MUS_InGame_LetsMOBA_Football"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_Football"
  bank: "MUS_InGame_LetsMOBA_Football"
  unloadcd: 2.0
}
rows {
  id: 7802
  playEvent: "Play_MUS_InGame_LetsMOBA_Football_ExtraTime"
  stopEvent: "Stop_MUS_InGame_LetsMOBA_Football_ExtraTime"
  bank: "MUS_InGame_LetsMOBA_Football"
  unloadcd: 2.0
}
rows {
  id: 8001
  playEvent: "Play_MUS_InGame_LetsCOC_Default"
  stopEvent: "Stop_MUS_InGame_LetsCOC_Default"
  bank: "MUS_InGame_LetsCOC"
  unloadcd: 2.0
}
rows {
  id: 8002
  playEvent: "Play_MUS_InGame_LetsCOC_PVP"
  switchState: "LetsCOC_MUS_Battle_Prepare"
  switchState: "LetsCOC_MUS_Battle_Stage_01"
  switchState: "LetsCOC_MUS_Battle_Stage_02"
  stateGroup: "LetsCOC_MUS_Battle"
  stopEvent: "Stop_MUS_InGame_LetsCOC_PVP"
  bank: "MUS_InGame_LetsCOC"
  unloadcd: 2.0
}
rows {
  id: 8003
  playEvent: "Play_MUS_InGame_LetsCOC_PVE"
  switchState: "LetsCOC_MUS_Battle_Prepare"
  switchState: "LetsCOC_MUS_Battle_Stage_01"
  switchState: "LetsCOC_MUS_Battle_Stage_02"
  switchState: "LetsCOC_MUS_Battle_Prepare"
  switchState: "LetsCOC_MUS_Battle_Stage_01"
  switchState: "LetsCOC_MUS_Battle_Stage_02"
  stateGroup: "LetsCOC_MUS_Battle"
  stopEvent: "Stop_MUS_InGame_LetsCOC_PVE"
  bank: "MUS_InGame_LetsCOC"
  unloadcd: 2.0
}
rows {
  id: 8004
  playEvent: "Play_MUS_InGame_LetsCOC_Win"
  stopEvent: "Stop_MUS_InGame_LetsCOC_Win"
  bank: "MUS_InGame_LetsCOC"
  unloadcd: 2.0
}
rows {
  id: 8005
  playEvent: "Play_MUS_InGame_LetsCOC_Lose"
  stopEvent: "Stop_MUS_InGame_LetsCOC_Lose"
  bank: "MUS_InGame_LetsCOC"
  unloadcd: 2.0
}
rows {
  id: 9000
  playEvent: "Play_MUS_InGame_LetsChase_Prepare"
  switchState: "LetsChase_MUS_Prepare_Transition"
  stateGroup: "LetsChase_MUS_Prepare"
  stopEvent: "Stop_MUS_InGame_LetsChase_Prepare"
  bank: "MUS_InGame_LetsChase_NightCity"
  unloadcd: 2.0
}
rows {
  id: 9005
  playEvent: "Play_MUS_InGame_LetsChase_NightCity_DarkStar"
  stopEvent: "Stop_MUS_InGame_LetsChase_NightCity_DarkStar"
  bank: "MUS_InGame_LetsChase_NightCity"
  unloadcd: 2.0
}
rows {
  id: 9006
  playEvent: "Play_MUS_InGame_LetsChase_NightCity_StarBaby"
  switchState: "LetsChase_Mus_Chase_On"
  switchState: "LetsChase_Mus_Chase_Off"
  stateGroup: "LetsChase_Mus_Chase"
  stopEvent: "Stop_MUS_InGame_LetsChase_NightCity_StarBaby"
  bank: "MUS_InGame_LetsChase_NightCity"
  unloadcd: 2.0
}
rows {
  id: 9007
  playEvent: "Set_GP_MUS_InGame_LetsChase_Encountered"
  switchEvent: "LetsChase_MUS_Intensity_High"
  switchEvent: "LetsChase_MUS_Intensity_Low"
  switchGroup: "LetsChase_MUS_Intensity"
  bank: "MUS_InGame_LetsChase_NightCity"
  unloadcd: 2.0
}
rows {
  id: 9008
  playEvent: "Set_GP_MUS_InGame_LetsChase_Escaped"
  switchEvent: "LetsChase_MUS_Intensity_High"
  switchEvent: "LetsChase_MUS_Intensity_Low"
  switchGroup: "LetsChase_MUS_Intensity"
  bank: "MUS_InGame_LetsChase_NightCity"
  unloadcd: 2.0
}
