com.tencent.wea.xlsRes.table_MatchInfoPassThroughData
excel/xls/W_玩法模式_玩法信息透传.xlsx sheet:透传信息
rows {
  id: 1
  matchId: 4
  modelInfoType: 1
  desc: "概率触发随机事件！"
  startTime {
    seconds: 1742486400
  }
  endTime {
    seconds: 1743695999
  }
  bgType: 0
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 2
  matchId: 4
  modelInfoType: 1
  desc: "32人乱斗限时尝鲜！"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1744905599
  }
  bgType: 0
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 3
  matchId: 4
  modelInfoType: 1
  desc: "幸运对局跳过首轮!"
  startTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1746115199
  }
  bgType: 0
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 4
  matchId: 6101
  modelInfoType: 1
  desc: "组队排位分翻倍中！"
  startTime {
    seconds: 1743091200
  }
  endTime {
    seconds: 1743350399
  }
  bgType: 1
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 5
  matchId: 105
  modelInfoType: 1
  desc: "新地图精灵谷上线！"
  startTime {
    seconds: 1744300800
  }
  endTime {
    seconds: 1745596799
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 6
  matchId: 350
  modelInfoType: 1
  desc: "压龙皮肤限时领取！"
  startTime {
    seconds: 1743004800
  }
  endTime {
    seconds: 1743695999
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 7
  matchId: 666
  modelInfoType: 1
  desc: "成长狂欢生效中！"
  startTime {
    seconds: 1743091200
  }
  endTime {
    seconds: 1743350399
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 9
  matchId: 666
  modelInfoType: 1
  desc: "海量磷虾免费领！"
  startTime {
    seconds: 1743350400
  }
  endTime {
    seconds: 1743695999
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 10
  matchId: 6101
  modelInfoType: 1
  desc: "参与活动抽峡谷币！"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1743868799
  }
  bgType: 1
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 11
  matchId: 6101
  modelInfoType: 1
  desc: "组队排位翻倍中！"
  startTime {
    seconds: 1744300800
  }
  endTime {
    seconds: 1744559999
  }
  bgType: 1
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 12
  matchId: 666
  modelInfoType: 1
  desc: "联动时装免费拿！"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1744300799
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 13
  matchId: 666
  modelInfoType: 1
  desc: "免费领农场家具！"
  startTime {
    seconds: 1744300800
  }
  endTime {
    seconds: 1745164799
  }
  bgType: 2
  sortId: 1
  jumpId: 655
  redDotId: -1
  bVisible: true
}
rows {
  id: 14
  matchId: 350
  modelInfoType: 1
  desc: "三王排位限时开启！"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1744559999
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 15
  matchId: 350
  modelInfoType: 1
  desc: " 踩踩暗星送守护灵紫！"
  startTime {
    seconds: 1744819200
  }
  endTime {
    seconds: 1746115199
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 16
  matchId: 105
  modelInfoType: 1
  desc: "招募好友领配饰！"
  startTime {
    seconds: 1745596800
  }
  endTime {
    seconds: 1746028799
  }
  bgType: 2
  sortId: 1
  jumpId: 437
  redDotId: -1
  bVisible: true
}
rows {
  id: 17
  matchId: 666
  modelInfoType: 1
  desc: "答题领宠物装饰！"
  startTime {
    seconds: 1745164800
  }
  endTime {
    seconds: 1745769599
  }
  bgType: 2
  sortId: 1
  jumpId: 662
  redDotId: -1
  bVisible: true
}
rows {
  id: 18
  matchId: 6101
  modelInfoType: 1
  desc: "参与对局抽英雄！"
  startTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1745164799
  }
  bgType: 1
  sortId: 1
  jumpId: 10680
  redDotId: -1
  bVisible: true
}
rows {
  id: 19
  matchId: 6101
  modelInfoType: 1
  desc: "组队排位翻倍中！"
  startTime {
    seconds: 1745510400
  }
  endTime {
    seconds: 1745769599
  }
  bgType: 1
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 20
  matchId: 105
  modelInfoType: 1
  desc: "彩蛋局房间限时开放"
  startTime {
    seconds: 1746028800
  }
  endTime {
    seconds: 1746115199
  }
  bgType: 1
  sortId: 1
  jumpId: 323
  redDotId: -1
  bVisible: true
}
rows {
  id: 21
  matchId: 105
  modelInfoType: 1
  desc: "决斗季送身份体验礼！"
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1746633599
  }
  bgType: 1
  sortId: 1
  jumpId: 497
  redDotId: -1
  bVisible: true
}
rows {
  id: 22
  matchId: 105
  modelInfoType: 1
  desc: "送新身份复仇者"
  startTime {
    seconds: 1746633600
  }
  endTime {
    seconds: 1747929599
  }
  bgType: 1
  sortId: 1
  jumpId: 444
  redDotId: -1
  bVisible: true
}
rows {
  id: 25
  matchId: 350
  modelInfoType: 1
  desc: "上分领拉弥娅套装"
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1747324799
  }
  bgType: 2
  sortId: 1
  jumpId: 677
  redDotId: -1
  bVisible: true
}
rows {
  id: 26
  matchId: 6101
  modelInfoType: 1
  desc: "直售英雄全限免！"
  startTime {
    seconds: 1746028800
  }
  endTime {
    seconds: 1746460799
  }
  bgType: 1
  sortId: 1
  jumpId: 6101
  redDotId: -1
  bVisible: true
}
rows {
  id: 27
  matchId: 6101
  modelInfoType: 1
  desc: "抽588峡谷币！"
  startTime {
    seconds: 1746720000
  }
  endTime {
    seconds: 1746979199
  }
  bgType: 1
  sortId: 1
  jumpId: 10679
  redDotId: -1
  bVisible: true
}
rows {
  id: 28
  matchId: 5700
  modelInfoType: 1
  desc: "新地图新机制"
  startTime {
    seconds: 1747238400
  }
  endTime {
    seconds: 1747583999
  }
  bgType: 1
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 29
  matchId: 105
  modelInfoType: 1
  desc: "对局掉落喵喵信封"
  startTime {
    seconds: 1747929600
  }
  endTime {
    seconds: 1748534399
  }
  bgType: 1
  sortId: 1
  jumpId: 134
  redDotId: -1
  bVisible: true
}
rows {
  id: 30
  matchId: 666
  modelInfoType: 1
  desc: "农场餐厅上线"
  startTime {
    seconds: 1747627200
  }
  endTime {
    seconds: 1748620799
  }
  bgType: 2
  sortId: 1
  jumpId: 684
  redDotId: -1
  bVisible: true
}
rows {
  id: 31
  matchId: 350
  modelInfoType: 1
  desc: "飓风皮肤限时领"
  startTime {
    seconds: 1747324800
  }
  endTime {
    seconds: 1748534399
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 32
  matchId: 6101
  modelInfoType: 1
  desc: "玩排位送非凡时装"
  startTime {
    seconds: 1747324800
  }
  endTime {
    seconds: 1748534399
  }
  bgType: 1
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 33
  matchId: 4
  modelInfoType: 1
  desc: "随机事件单轮触发！"
  startTime {
    seconds: 1747324800
  }
  endTime {
    seconds: 1748534399
  }
  bgType: 0
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 34
  matchId: 4
  modelInfoType: 1
  desc: "32人乱斗新关卡！"
  startTime {
    seconds: 1748534400
  }
  endTime {
    seconds: 1749743999
  }
  bgType: 0
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 35
  matchId: 666
  modelInfoType: 1
  desc: "农场餐厅上线!"
  startTime {
    seconds: 1748664000
  }
  endTime {
    seconds: 1749743999
  }
  bgType: 2
  sortId: 1
  jumpId: 5104
  redDotId: -1
  bVisible: true
}
rows {
  id: 36
  matchId: 350
  modelInfoType: 1
  desc: "暗星2.0 焕新登场！"
  startTime {
    seconds: 1748534400
  }
  endTime {
    seconds: 1749743999
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 37
  matchId: 666
  modelInfoType: 1
  desc: "农场美食节盛大登场!"
  startTime {
    seconds: 1749744000
  }
  endTime {
    seconds: 1750953599
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
rows {
  id: 38
  matchId: 350
  modelInfoType: 1
  desc: "对局送红心火雷！"
  startTime {
    seconds: 1749744000
  }
  endTime {
    seconds: 1750953599
  }
  bgType: 2
  sortId: 1
  redDotId: -1
  bVisible: true
}
