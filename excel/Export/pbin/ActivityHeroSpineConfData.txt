com.tencent.wea.xlsRes.table_ActivityHeroSpineConfData
excel/xls/S_活动英雄Spine表.xlsx sheet:Sheet1
rows {
  id: 1001
  spineName: "UI_Spine_Wangzhe_sunce_atlas"
  spinePos: "-35;0"
  skeletonData: "UI_Spine_Wangzhe_sunce_skel"
  initialAnimation: "loop"
  spineSizeXY: "485;485"
}
rows {
  id: 1002
  spineName: "UI_Spine_Wangzhe_kaixiu_atlas"
  spinePos: "-40;0"
  skeletonData: "UI_Spine_Wangzhe_kaixiu_skel"
  initialAnimation: "loop"
  spineSizeXY: "485;485"
}
rows {
  id: 1003
  spineName: "UI_Spine_Wangzhe_AnQiLa_atlas"
  spinePos: "5;0"
  skeletonData: "UI_Spine_Wangzhe_AnQiLa_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "485;485"
}
rows {
  id: 1004
  spineName: "UI_Spine_Wangzhe_luban_atlas"
  spinePos: "-3;0"
  skeletonData: "UI_Spine_Wangzhe_luban_skel"
  initialAnimation: "loop"
  spineSizeXY: "485;485"
}
rows {
  id: 1005
  spineName: "UI_Spine_Wangzhe_LanLinWang_atlas"
  spinePos: "0;-34.5"
  skeletonData: "UI_Spine_Wangzhe_LanLinWang_skel"
  initialAnimation: "Ani_loop"
  spineSizeXY: "510;510"
}
rows {
  id: 1006
  spineName: "UI_Spine_Wangzhe_yao_01_atlas"
  spinePos: "-37;0"
  skeletonData: "UI_Spine_Wangzhe_yao_01_skel"
  initialAnimation: "loop"
  spineSizeXY: "485;485"
}
rows {
  id: 1007
  spinePos: "0;0"
  initialAnimation: "Ani_loop"
  spineSizeXY: "402;402"
}
rows {
  id: 1010
  spineName: "UI_Spine_Wangzhe_ZhaoYun_atlas"
  spinePos: "-25;-14"
  skeletonData: "UI_Spine_Wangzhe_ZhaoYun_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "510;510"
}
rows {
  id: 1011
  spineName: "UI_Spine_Wangzhe_yase_atlas"
  spinePos: "-70;-7"
  skeletonData: "UI_Spine_Wangzhe_yase_skel"
  initialAnimation: "idle"
  spineSizeXY: "485;485"
}
rows {
  id: 1012
  spineName: "UI_Spine_Wangzhe_houyi_atlas"
  spinePos: "-11;-5"
  skeletonData: "UI_Spine_Wangzhe_houyi_skel"
  initialAnimation: "Ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1013
  spineName: "UI_Spine_Wangzhe_CaiWenJi_atlas"
  spinePos: "-25;0"
  skeletonData: "UI_Spine_Wangzhe_CaiWenJi_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "420;420"
}
rows {
  id: 1014
  spineName: "UI_Spine_Wangzhe_SunShangXiang_atlas"
  spinePos: "-50;-18"
  skeletonData: "UI_Spine_Wangzhe_SunShangXiang_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "510;510"
}
rows {
  id: 1015
  spineName: "UI_Spine_Wangzhe_ZhongKui_atlas"
  spinePos: "-50;0"
  skeletonData: "UI_Spine_Wangzhe_ZhongKui_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "490;490"
}
rows {
  id: 1016
  spineName: "UI_Spine_Wangzhe_SunWuKong_atlas"
  spinePos: "0;0"
  skeletonData: "UI_Spine_Wangzhe_SunWuKong_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1017
  spineName: "UI_Spine_Wangzhe_YuanYing_atlas"
  spinePos: "-45;-10"
  skeletonData: "UI_Spine_Wangzhe_YuanYing_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "510;510"
}
rows {
  id: 1018
  spineName: "UI_Spine_Wangzhe_WangZhaoYun_atlas"
  spinePos: "78;17"
  skeletonData: "UI_Spine_Wangzhe_WangZhaoYun_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "485;485"
}
rows {
  id: 1019
  spineName: "UI_Spine_Wangzhe_LiBai_atlas"
  spinePos: "-30;-17"
  skeletonData: "UI_Spine_Wangzhe_LiBai_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "510;510"
}
rows {
  id: 1020
  spineName: "UI_Spine_Wangzhe_MoZi_atlas"
  spinePos: "0;0"
  skeletonData: "UI_Spine_Wangzhe_MoZi_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "485;485"
}
rows {
  id: 1021
  spineName: "UI_Spine_Wangzhe_GongSunLi_All_atlas"
  spinePos: "-55;-6"
  skeletonData: "UI_Spine_Wangzhe_GongSunLi_All_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "490;490"
}
rows {
  id: 1022
  spineName: "UI_Spine_Wangzhe_DongHuang_atlas"
  spinePos: "-20;-10"
  skeletonData: "UI_Spine_Wangzhe_DongHuang_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "530;530"
}
rows {
  id: 1023
  spineName: "UI_Spine_Wangzhe_DiRenJie_atlas"
  spinePos: "-20;-10"
  skeletonData: "UI_Spine_Wangzhe_DiRenJie_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "510;510"
}
rows {
  id: 1024
  spineName: "UI_Spine_Wangzhe_HuaMuLan_atlas"
  spinePos: "-35;-10"
  skeletonData: "UI_Spine_Wangzhe_HuaMuLan_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1025
  spineName: "UI_Spine_Wangzhe_Daji_atlas"
  spinePos: "0;-9"
  skeletonData: "UI_Spine_Wangzhe_Daji_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1027
  spineName: "UI_Spine_Wangzhe_Makeboluo_atlas"
  spinePos: "30;-9"
  skeletonData: "UI_Spine_Wangzhe_Makeboluo_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1028
  spineName: "UI_Spine_Wangzhe_Xiahoudun_atlas"
  spinePos: "0;-9"
  skeletonData: "UI_Spine_Wangzhe_Xiahoudun_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1029
  spineName: "UI_Spine_Wangzhe_ZhangLiang_atlas"
  spinePos: "-75;-9"
  skeletonData: "UI_Spine_Wangzhe_ZhangLiang_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1030
  spineName: "UI_Spine_Wangzhe_Chengyaojin_atlas"
  spinePos: "-100;0"
  skeletonData: "UI_Spine_Wangzhe_Chengyaojin_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1031
  spineName: "UI_Spine_Wangzhe_Yuji_atlas"
  spinePos: "-10;-9"
  skeletonData: "UI_Spine_Wangzhe_Yuji_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1032
  spineName: "UI_Spine_Wangzhe_Lvbu_atlas"
  spinePos: "-75;0"
  skeletonData: "UI_Spine_Wangzhe_Lvbu_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1034
  spineName: "UI_Spine_Wangzhe_GongBen_atlas"
  spinePos: "30;-9"
  skeletonData: "UI_Spine_Wangzhe_GongBen_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1035
  spineName: "UI_Spine_Wangzhe_Ake_atlas"
  spinePos: "-60;-18"
  skeletonData: "UI_Spine_Wangzhe_Ake_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1036
  spineName: "UI_Spine_Wangzhe_Dianwei_atlas"
  spinePos: "15;0"
  skeletonData: "UI_Spine_Wangzhe_Dianwei_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1037
  spineName: "UI_Spine_Wangzhe_Zhenji_atlas"
  spinePos: "15;-30"
  skeletonData: "UI_Spine_Wangzhe_Zhenji_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1040
  spinePos: "-45;-7"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1041
  spineName: "UI_Spine_Wangzhe_DiaoCan_atlas"
  spinePos: "-10;-9"
  skeletonData: "UI_Spine_Wangzhe_DiaoCan_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
rows {
  id: 1043
  spineName: "UI_Spine_Wangzhe_Liushan_atlas"
  spinePos: "-10;-9"
  skeletonData: "UI_Spine_Wangzhe_Liushan_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "650;650"
}
rows {
  id: 1045
  spineName: "UI_Spine_Wangzhe_Yangyuhuan_atlas"
  spinePos: "-20;0"
  skeletonData: "UI_Spine_Wangzhe_Yangyuhuan_skel"
  initialAnimation: "ani_loop"
  spineSizeXY: "500;500"
}
