com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_yujun.xlsx sheet:活动-臻藏卡池
rows {
  raffleId: 5360
  name: "星之恋空"
  startTime {
    seconds: 1731600000
  }
  endTime {
    seconds: 1738857599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5360
  }
  dailyLimit: 500
  maxLimit: 999999
  textRuleId: 269
  text: "<SwingSubViewDrowTips>每十次</>祈愿必得外观或非凡以上奖励"
  lowestVersion: "*********"
  milestone {
    atDraws: 20
    atDraws: 40
    atDraws: 80
    atDraws: 120
    atDraws: 160
    giftIds: 310041
    giftIds: 310042
    giftIds: 310043
    giftIds: 310044
    giftIds: 310045
  }
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 8
  previewTag: "Camera_Ornament8"
  showStartTime {
    seconds: 1731600000
  }
  showEndTime {
    seconds: 1738857599
  }
  isShow: true
  relateUmgSetting: "15;UI_Common_PackageRewardView"
  cratesItemId: 222
  cratesItemNum: 300
  cratesCommodityIds: 8801
  cratesCommodityIds: 8802
  firstTimeTaskId: 500220
  defaultPreviewItemId: 640076
  cratesBounceItems {
    itemId: 320074
    itemNum: 1
  }
  tagDailyLimit: 500
}
rows {
  raffleId: 5361
  name: "岚汀之约"
  startTime {
    seconds: 1739462400
  }
  endTime {
    seconds: 1748534399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 5361
  }
  dailyLimit: 30
  maxLimit: 999999
  textRuleId: 324
  text: "<ActivationSubView5Yellow>每十次</>祈愿必得外观或非凡以上奖励"
  lowestVersion: "1.3.68.87"
  milestone {
    atDraws: 20
    atDraws: 40
    atDraws: 80
    atDraws: 120
    atDraws: 160
    giftIds: 310306
    giftIds: 310307
    giftIds: 310308
    giftIds: 310309
    giftIds: 310310
  }
  previewTag: "Camera_Ornament10"
  showStartTime {
    seconds: 1739462400
  }
  showEndTime {
    seconds: 1748534399
  }
  isShow: true
  animType: 4
  relateUmgSetting: "15;UI_Common_PackageRewardView"
  cratesItemId: 226
  cratesItemNum: 300
  cratesCommodityIds: 8830
  cratesCommodityIds: 8831
  firstTimeTaskId: 500312
  defaultPreviewItemId: 640138
  cratesBounceItems {
    itemId: 320120
    itemNum: 1
  }
  previewScaleThenSetPos: true
  tagDailyLimit: 500
}
rows {
  raffleId: 40000015
  name: "玩偶之家"
  startTime {
    seconds: 1744300800
  }
  endTime {
    seconds: 1749139199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 40000015
  }
  dailyLimit: 30
  maxLimit: 999999
  textRuleId: 338
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.78.72"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310201
    giftIds: 310202
    giftIds: 310203
    giftIds: 310204
    giftIds: 310205
  }
  viewIndex: 1
  viewIndex: 6
  viewIndex: 5
  viewIndex: 2
  viewIndex: 4
  viewIndex: 10
  viewIndex: 3
  showStartTime {
    seconds: 1744300800
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  drawOverState: 4
  animType: 4
  tagDailyLimit: 300
}
