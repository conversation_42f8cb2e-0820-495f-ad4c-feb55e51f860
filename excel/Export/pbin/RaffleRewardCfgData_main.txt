com.tencent.wea.xlsRes.table_RaffleRewardCfgData
excel/xls/C_抽奖奖池_主表.xlsx sheet:奖励
rows {
  rewardId: 1000001
  poolId: 1000
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000002
  poolId: 1000
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 390
}
rows {
  rewardId: 1000003
  poolId: 1000
  name: "神奇水彩*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000004
  poolId: 1000
  name: "神奇水彩*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000005
  poolId: 1000
  name: "神奇水彩*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 110
}
rows {
  rewardId: 1000006
  poolId: 1000
  name: "小恶魔吉他"
  itemId: 620001
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000007
  poolId: 1000
  name: "狂想曲吉他"
  itemId: 620082
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000008
  poolId: 1000
  name: "追风头盔"
  itemId: 620022
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000009
  poolId: 1000
  name: "追风头盔"
  itemId: 620094
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000010
  poolId: 1000
  name: "追梦头盔"
  itemId: 620095
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000011
  poolId: 1000
  name: "欢呼"
  itemId: 720001
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000012
  poolId: 1000
  name: "一起摇摆"
  itemId: 720029
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000013
  poolId: 1000
  name: "熊抱"
  itemId: 720012
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000014
  poolId: 1000
  name: "暗送秋波"
  itemId: 710008
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000015
  poolId: 1000
  name: "午夜心碎"
  itemId: 710009
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000016
  poolId: 1000
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000017
  poolId: 1000
  name: "灰色边缘上装"
  itemId: 510018
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000018
  poolId: 1000
  name: "灰色边缘下装"
  itemId: 520015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000019
  poolId: 1000
  name: "灰色边缘手套"
  itemId: 530017
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000020
  poolId: 1000
  name: "最佳员工上装"
  itemId: 510005
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000021
  poolId: 1000
  name: "最佳员工下装"
  itemId: 520005
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000022
  poolId: 1000
  name: "最佳员工手套"
  itemId: 530007
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000023
  poolId: 1000
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000024
  poolId: 1000
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000025
  poolId: 1000
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000026
  poolId: 1000
  name: "红莓丝绒上装"
  itemId: 510032
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000027
  poolId: 1000
  name: "红莓丝绒下装"
  itemId: 520030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000028
  poolId: 1000
  name: "彩虹旋律上装"
  itemId: 510028
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000029
  poolId: 1000
  name: "彩虹旋律下装"
  itemId: 520026
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000030
  poolId: 1000
  name: "摩登学院上装"
  itemId: 510030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000031
  poolId: 1000
  name: "摩登学院下装"
  itemId: 520028
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000032
  poolId: 1000
  name: "黑莓可可上装"
  itemId: 510049
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000033
  poolId: 1000
  name: "黑莓可可下装"
  itemId: 520039
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000034
  poolId: 1000
  name: "蓝色童话上装"
  itemId: 510045
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000035
  poolId: 1000
  name: "蓝色童话下装"
  itemId: 520035
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000036
  poolId: 1000
  name: "蓝莓戚风上装"
  itemId: 510050
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000037
  poolId: 1000
  name: "蓝莓戚风下装"
  itemId: 520040
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000038
  poolId: 1000
  name: "三花摩卡上装"
  itemId: 510043
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000039
  poolId: 1000
  name: "三花摩卡下装"
  itemId: 520033
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000040
  poolId: 1000
  name: "烘焙甜心上装"
  itemId: 510029
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000041
  poolId: 1000
  name: "烘焙甜心下装"
  itemId: 520027
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 4
}
rows {
  rewardId: 1000042
  poolId: 1000
  name: "白桃气泡上装"
  itemId: 510044
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000043
  poolId: 1000
  name: "白桃气泡下装"
  itemId: 520034
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000044
  poolId: 10001
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 90
  limit: 5
}
rows {
  rewardId: 1000045
  poolId: 10001
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 10
  limit: 713
}
rows {
  rewardId: 1000046
  poolId: 10001
  name: "神奇水彩*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000047
  poolId: 10001
  name: "神奇水彩*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000048
  poolId: 10001
  name: "神奇水彩*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 100
}
rows {
  rewardId: 1000049
  poolId: 10001
  name: "元气吐司"
  itemId: 630086
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000050
  poolId: 10001
  name: "喵喵糖"
  itemId: 620164
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000051
  poolId: 10001
  name: "喵喵糖"
  itemId: 620165
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000052
  poolId: 10001
  name: "小恶魔吉他"
  itemId: 620001
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000053
  poolId: 10001
  name: "狂想曲吉他"
  itemId: 620082
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000054
  poolId: 10001
  name: "追风头盔"
  itemId: 620022
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000055
  poolId: 10001
  name: "追风头盔"
  itemId: 620094
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000056
  poolId: 10001
  name: "追梦头盔"
  itemId: 620095
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000057
  poolId: 10001
  name: "蹦蹦跳跳"
  itemId: 720106
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000058
  poolId: 10001
  name: "原地转圈"
  itemId: 720107
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000059
  poolId: 10001
  name: "欢呼"
  itemId: 720001
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000060
  poolId: 10001
  name: "一起摇摆"
  itemId: 720029
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000061
  poolId: 10001
  name: "熊抱"
  itemId: 720012
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000062
  poolId: 10001
  name: "让我看看"
  itemId: 710083
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000063
  poolId: 10001
  name: "皱眉"
  itemId: 710079
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000064
  poolId: 10001
  name: "暗送秋波"
  itemId: 710008
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000065
  poolId: 10001
  name: "午夜心碎"
  itemId: 710009
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000066
  poolId: 10001
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000067
  poolId: 10001
  name: "赞不绝手上装"
  itemId: 510092
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000068
  poolId: 10001
  name: "赞不绝手下装"
  itemId: 520061
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000069
  poolId: 10001
  name: "赞不绝手手套"
  itemId: 530041
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000070
  poolId: 10001
  name: "寻味奇趣上装"
  itemId: 510093
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000071
  poolId: 10001
  name: "寻味奇趣下装"
  itemId: 520062
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000072
  poolId: 10001
  name: "寻味奇趣手套"
  itemId: 530042
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000073
  poolId: 10001
  name: "红莓酥糖上装"
  itemId: 510094
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000074
  poolId: 10001
  name: "红莓酥糖下装"
  itemId: 520063
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000075
  poolId: 10001
  name: "红莓酥糖手套"
  itemId: 530043
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000076
  poolId: 10001
  name: "好运南风上装"
  itemId: 510077
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000077
  poolId: 10001
  name: "好运南风下装"
  itemId: 520051
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000078
  poolId: 10001
  name: "好运南风手套"
  itemId: 530030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000079
  poolId: 10001
  name: "粉墨工匠上装"
  itemId: 510026
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000080
  poolId: 10001
  name: "粉墨工匠下装"
  itemId: 520024
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000081
  poolId: 10001
  name: "粉墨工匠手套"
  itemId: 530019
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000082
  poolId: 10001
  name: "灰色边缘上装"
  itemId: 510018
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000083
  poolId: 10001
  name: "灰色边缘下装"
  itemId: 520015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000084
  poolId: 10001
  name: "灰色边缘手套"
  itemId: 530017
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000085
  poolId: 10001
  name: "最佳员工上装"
  itemId: 510005
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000086
  poolId: 10001
  name: "最佳员工下装"
  itemId: 520005
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000087
  poolId: 10001
  name: "最佳员工手套"
  itemId: 530007
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000088
  poolId: 10001
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000089
  poolId: 10001
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000090
  poolId: 10001
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000091
  poolId: 10001
  name: "红莓丝绒上装"
  itemId: 510032
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000092
  poolId: 10001
  name: "红莓丝绒下装"
  itemId: 520030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000093
  poolId: 10001
  name: "彩虹旋律上装"
  itemId: 510028
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000094
  poolId: 10001
  name: "彩虹旋律下装"
  itemId: 520026
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000095
  poolId: 10001
  name: "摩登学院上装"
  itemId: 510030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000096
  poolId: 10001
  name: "摩登学院下装"
  itemId: 520028
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000097
  poolId: 10001
  name: "黑莓可可上装"
  itemId: 510049
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000098
  poolId: 10001
  name: "黑莓可可下装"
  itemId: 520039
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000099
  poolId: 10001
  name: "蓝色童话上装"
  itemId: 510045
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000100
  poolId: 10001
  name: "蓝色童话下装"
  itemId: 520035
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000101
  poolId: 10001
  name: "蓝莓戚风上装"
  itemId: 510050
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000102
  poolId: 10001
  name: "蓝莓戚风下装"
  itemId: 520040
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000103
  poolId: 10001
  name: "三花摩卡上装"
  itemId: 510043
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000104
  poolId: 10001
  name: "三花摩卡下装"
  itemId: 520033
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000105
  poolId: 10001
  name: "烘焙甜心上装"
  itemId: 510029
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000106
  poolId: 10001
  name: "烘焙甜心下装"
  itemId: 520027
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000107
  poolId: 10001
  name: "白桃气泡上装"
  itemId: 510044
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000108
  poolId: 10001
  name: "白桃气泡下装"
  itemId: 520034
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000109
  poolId: 10002
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 90
  limit: 5
}
rows {
  rewardId: 1000110
  poolId: 10002
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 10
  limit: 692
}
rows {
  rewardId: 1000111
  poolId: 10002
  name: "神奇水彩*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000112
  poolId: 10002
  name: "神奇水彩*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000113
  poolId: 10002
  name: "神奇水彩*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 100
}
rows {
  rewardId: 1000114
  poolId: 10002
  name: "四季风车"
  itemId: 620198
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000115
  poolId: 10002
  name: "金巧风车"
  itemId: 620199
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000116
  poolId: 10002
  name: "猎豹眼镜"
  itemId: 610095
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000117
  poolId: 10002
  name: "元气吐司"
  itemId: 630086
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000118
  poolId: 10002
  name: "喵喵糖"
  itemId: 620164
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000119
  poolId: 10002
  name: "喵喵糖"
  itemId: 620165
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000120
  poolId: 10002
  name: "恐龙舞"
  itemId: 720119
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000121
  poolId: 10002
  name: "得意舞"
  itemId: 720122
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000122
  poolId: 10002
  name: "蹦蹦跳跳"
  itemId: 720106
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000123
  poolId: 10002
  name: "原地转圈"
  itemId: 720107
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000124
  poolId: 10002
  name: "宕机了"
  itemId: 710117
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000125
  poolId: 10002
  name: "惊了"
  itemId: 710120
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000126
  poolId: 10002
  name: "我没事"
  itemId: 710078
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000127
  poolId: 10002
  name: "皱眉"
  itemId: 710079
  itemNum: 1
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000128
  poolId: 10002
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000129
  poolId: 10002
  name: "拼贴风尚上装"
  itemId: 510106
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000130
  poolId: 10002
  name: "拼贴风尚下装"
  itemId: 520068
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000131
  poolId: 10002
  name: "拼贴风尚手套"
  itemId: 530048
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000132
  poolId: 10002
  name: "日落海岛上装"
  itemId: 510107
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000133
  poolId: 10002
  name: "日落海岛下装"
  itemId: 520069
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000134
  poolId: 10002
  name: "日落海岛手套"
  itemId: 530049
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000135
  poolId: 10002
  name: "热带风情上装"
  itemId: 510108
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000136
  poolId: 10002
  name: "热带风情下装"
  itemId: 520070
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000137
  poolId: 10002
  name: "热带风情手套"
  itemId: 530050
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000138
  poolId: 10002
  name: "新奇撞色上装"
  itemId: 510109
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000139
  poolId: 10002
  name: "新奇撞色下装"
  itemId: 520071
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000140
  poolId: 10002
  name: "新奇撞色手套"
  itemId: 530051
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000141
  poolId: 10002
  name: "赞不绝手上装"
  itemId: 510092
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000142
  poolId: 10002
  name: "赞不绝手下装"
  itemId: 520061
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000143
  poolId: 10002
  name: "赞不绝手手套"
  itemId: 530041
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000144
  poolId: 10002
  name: "寻味奇趣上装"
  itemId: 510093
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000145
  poolId: 10002
  name: "寻味奇趣下装"
  itemId: 520062
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000146
  poolId: 10002
  name: "寻味奇趣手套"
  itemId: 530042
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000147
  poolId: 10002
  name: "红莓酥糖上装"
  itemId: 510094
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000148
  poolId: 10002
  name: "红莓酥糖下装"
  itemId: 520063
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000149
  poolId: 10002
  name: "红莓酥糖手套"
  itemId: 530043
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000150
  poolId: 10002
  name: "好运南风上装"
  itemId: 510077
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000151
  poolId: 10002
  name: "好运南风下装"
  itemId: 520051
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000152
  poolId: 10002
  name: "好运南风手套"
  itemId: 530030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000153
  poolId: 10002
  name: "粉墨工匠上装"
  itemId: 510026
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000154
  poolId: 10002
  name: "粉墨工匠下装"
  itemId: 520024
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000155
  poolId: 10002
  name: "粉墨工匠手套"
  itemId: 530019
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000156
  poolId: 10002
  name: "灰色边缘上装"
  itemId: 510018
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000157
  poolId: 10002
  name: "灰色边缘下装"
  itemId: 520015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000158
  poolId: 10002
  name: "灰色边缘手套"
  itemId: 530017
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000159
  poolId: 10002
  name: "最佳员工上装"
  itemId: 510005
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000160
  poolId: 10002
  name: "最佳员工下装"
  itemId: 520005
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000161
  poolId: 10002
  name: "最佳员工手套"
  itemId: 530007
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000162
  poolId: 10002
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000163
  poolId: 10002
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000164
  poolId: 10002
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000165
  poolId: 10002
  name: "红莓丝绒上装"
  itemId: 510032
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000166
  poolId: 10002
  name: "红莓丝绒下装"
  itemId: 520030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000167
  poolId: 10002
  name: "彩虹旋律上装"
  itemId: 510028
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000168
  poolId: 10002
  name: "彩虹旋律下装"
  itemId: 520026
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000169
  poolId: 10002
  name: "摩登学院上装"
  itemId: 510030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000170
  poolId: 10002
  name: "摩登学院下装"
  itemId: 520028
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000171
  poolId: 10003
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000172
  poolId: 10003
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 715
}
rows {
  rewardId: 1000173
  poolId: 10003
  name: "神奇水彩*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000174
  poolId: 10003
  name: "神奇水彩*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000175
  poolId: 10003
  name: "神奇水彩*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1000176
  poolId: 10003
  name: "星夜太妃糖"
  itemId: 630155
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000177
  poolId: 10003
  name: "木星怪咖"
  itemId: 610115
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000178
  poolId: 10003
  name: "水星来客"
  itemId: 610116
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000179
  poolId: 10003
  name: "四季风车"
  itemId: 620198
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000180
  poolId: 10003
  name: "金巧风车"
  itemId: 620199
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000181
  poolId: 10003
  name: "猎豹眼镜"
  itemId: 610095
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000182
  poolId: 10003
  name: "元气吐司"
  itemId: 630086
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000183
  poolId: 10003
  name: "喵喵糖"
  itemId: 620164
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000184
  poolId: 10003
  name: "喵喵糖"
  itemId: 620165
  itemNum: 1
  groupId: 5
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000185
  poolId: 10003
  name: "八段锦叁式"
  itemId: 720092
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000186
  poolId: 10003
  name: "八段锦肆式"
  itemId: 720093
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000187
  poolId: 10003
  name: "恐龙舞"
  itemId: 720119
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000188
  poolId: 10003
  name: "得意舞"
  itemId: 720122
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000189
  poolId: 10003
  name: "看你表演"
  itemId: 710160
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000190
  poolId: 10003
  name: "顶不住了"
  itemId: 710157
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 2
}
rows {
  rewardId: 1000191
  poolId: 10003
  name: "宕机了"
  itemId: 710117
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000192
  poolId: 10003
  name: "惊了"
  itemId: 710120
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000193
  poolId: 10003
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000194
  poolId: 10003
  name: "邻家学妹上装"
  itemId: 510133
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000195
  poolId: 10003
  name: "邻家学妹下装"
  itemId: 520088
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000196
  poolId: 10003
  name: "邻家学妹手套"
  itemId: 530066
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000197
  poolId: 10003
  name: "精英学姐上装"
  itemId: 510134
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000198
  poolId: 10003
  name: "精英学姐下装"
  itemId: 520089
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000199
  poolId: 10003
  name: "精英学姐手套"
  itemId: 530067
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000200
  poolId: 10003
  name: "隐藏富豪上装"
  itemId: 510136
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000201
  poolId: 10003
  name: "隐藏富豪下装"
  itemId: 520091
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000202
  poolId: 10003
  name: "隐藏富豪手套"
  itemId: 530069
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000203
  poolId: 10003
  name: "年会爆款上装"
  itemId: 510137
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000204
  poolId: 10003
  name: "年会爆款下装"
  itemId: 520092
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000205
  poolId: 10003
  name: "年会爆款手套"
  itemId: 530070
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000206
  poolId: 10003
  name: "进步青年上装"
  itemId: 510138
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000207
  poolId: 10003
  name: "进步青年下装"
  itemId: 520093
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000208
  poolId: 10003
  name: "进步青年手套"
  itemId: 530071
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000209
  poolId: 10003
  name: "拼贴风尚上装"
  itemId: 510106
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000210
  poolId: 10003
  name: "拼贴风尚下装"
  itemId: 520068
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000211
  poolId: 10003
  name: "拼贴风尚手套"
  itemId: 530048
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000212
  poolId: 10003
  name: "日落海岛上装"
  itemId: 510107
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000213
  poolId: 10003
  name: "日落海岛下装"
  itemId: 520069
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000214
  poolId: 10003
  name: "日落海岛手套"
  itemId: 530049
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000215
  poolId: 10003
  name: "热带风情上装"
  itemId: 510108
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000216
  poolId: 10003
  name: "热带风情下装"
  itemId: 520070
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000217
  poolId: 10003
  name: "热带风情手套"
  itemId: 530050
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000218
  poolId: 10003
  name: "新奇撞色上装"
  itemId: 510109
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000219
  poolId: 10003
  name: "新奇撞色下装"
  itemId: 520071
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000220
  poolId: 10003
  name: "新奇撞色手套"
  itemId: 530051
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000221
  poolId: 10003
  name: "赞不绝手上装"
  itemId: 510092
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000222
  poolId: 10003
  name: "赞不绝手下装"
  itemId: 520061
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000223
  poolId: 10003
  name: "赞不绝手手套"
  itemId: 530041
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000224
  poolId: 10003
  name: "寻味奇趣上装"
  itemId: 510093
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000225
  poolId: 10003
  name: "寻味奇趣下装"
  itemId: 520062
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000226
  poolId: 10003
  name: "寻味奇趣手套"
  itemId: 530042
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000227
  poolId: 10003
  name: "红莓酥糖上装"
  itemId: 510094
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000228
  poolId: 10003
  name: "红莓酥糖下装"
  itemId: 520063
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000229
  poolId: 10003
  name: "红莓酥糖手套"
  itemId: 530043
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000230
  poolId: 10003
  name: "好运南风上装"
  itemId: 510077
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000231
  poolId: 10003
  name: "好运南风下装"
  itemId: 520051
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000232
  poolId: 10003
  name: "好运南风手套"
  itemId: 530030
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000233
  poolId: 10003
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000234
  poolId: 10003
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000235
  poolId: 10003
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000236
  poolId: 10004
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000237
  poolId: 10004
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1000238
  poolId: 10004
  name: "印章祈愿兑换券*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000239
  poolId: 10004
  name: "印章祈愿兑换券*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000240
  poolId: 10004
  name: "印章祈愿兑换券*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1000241
  poolId: 10004
  name: "微笑娃娃"
  itemId: 620329
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000242
  poolId: 10004
  name: "肉棕棕"
  itemId: 630216
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000243
  poolId: 10004
  name: "粉嘟嘟"
  itemId: 630217
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000244
  poolId: 10004
  name: "星夜太妃糖"
  itemId: 630155
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000245
  poolId: 10004
  name: "木星怪咖"
  itemId: 610115
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000246
  poolId: 10004
  name: "水星来客"
  itemId: 610116
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000247
  poolId: 10004
  name: "四季风车"
  itemId: 620198
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000248
  poolId: 10004
  name: "金巧风车"
  itemId: 620199
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000249
  poolId: 10004
  name: "猎豹眼镜"
  itemId: 610095
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000250
  poolId: 10004
  name: "八段锦伍式"
  itemId: 720094
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000251
  poolId: 10004
  name: "八段锦陆式"
  itemId: 720095
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000252
  poolId: 10004
  name: "八段锦叁式"
  itemId: 720092
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000253
  poolId: 10004
  name: "八段锦肆式"
  itemId: 720093
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000254
  poolId: 10004
  name: "好伤心"
  itemId: 710163
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000255
  poolId: 10004
  name: "爆发蓄力中"
  itemId: 710162
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000256
  poolId: 10004
  name: "看你表演"
  itemId: 710160
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000257
  poolId: 10004
  name: "唯唯诺诺"
  itemId: 710166
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000258
  poolId: 10004
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000259
  poolId: 10004
  name: "灰调潮流上装"
  itemId: 510159
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000260
  poolId: 10004
  name: "灰调潮流下装"
  itemId: 520110
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000261
  poolId: 10004
  name: "灰调潮流手套"
  itemId: 530087
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000262
  poolId: 10004
  name: "灰粉嘻哈上装"
  itemId: 510160
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000263
  poolId: 10004
  name: "灰粉嘻哈下装"
  itemId: 520111
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000264
  poolId: 10004
  name: "灰粉嘻哈手套"
  itemId: 530088
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000265
  poolId: 10004
  name: "多元时尚上装"
  itemId: 510161
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000266
  poolId: 10004
  name: "多元时尚下装"
  itemId: 520112
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000267
  poolId: 10004
  name: "多元时尚手套"
  itemId: 530089
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000268
  poolId: 10004
  name: "古韵长衣上装"
  itemId: 510153
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000269
  poolId: 10004
  name: "古韵长衣下装"
  itemId: 520105
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000270
  poolId: 10004
  name: "古韵长衣手套"
  itemId: 530082
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000271
  poolId: 10004
  name: "气息归元上装"
  itemId: 510154
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000272
  poolId: 10004
  name: "气息归元下装"
  itemId: 520106
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000273
  poolId: 10004
  name: "气息归元手套"
  itemId: 530083
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000274
  poolId: 10004
  name: "邻家学妹上装"
  itemId: 510133
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000275
  poolId: 10004
  name: "邻家学妹下装"
  itemId: 520088
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000276
  poolId: 10004
  name: "邻家学妹手套"
  itemId: 530066
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000277
  poolId: 10004
  name: "精英学姐上装"
  itemId: 510134
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000278
  poolId: 10004
  name: "精英学姐下装"
  itemId: 520089
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000279
  poolId: 10004
  name: "精英学姐手套"
  itemId: 530067
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000280
  poolId: 10004
  name: "隐藏富豪上装"
  itemId: 510136
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000281
  poolId: 10004
  name: "隐藏富豪下装"
  itemId: 520091
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000282
  poolId: 10004
  name: "隐藏富豪手套"
  itemId: 530069
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000283
  poolId: 10004
  name: "年会爆款上装"
  itemId: 510137
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000284
  poolId: 10004
  name: "年会爆款下装"
  itemId: 520092
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000285
  poolId: 10004
  name: "年会爆款手套"
  itemId: 530070
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000286
  poolId: 10004
  name: "进步青年上装"
  itemId: 510138
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000287
  poolId: 10004
  name: "进步青年下装"
  itemId: 520093
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000288
  poolId: 10004
  name: "进步青年手套"
  itemId: 530071
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000289
  poolId: 10004
  name: "拼贴风尚上装"
  itemId: 510106
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000290
  poolId: 10004
  name: "拼贴风尚下装"
  itemId: 520068
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000291
  poolId: 10004
  name: "拼贴风尚手套"
  itemId: 530048
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000292
  poolId: 10004
  name: "日落海岛上装"
  itemId: 510107
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000293
  poolId: 10004
  name: "日落海岛下装"
  itemId: 520069
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000294
  poolId: 10004
  name: "日落海岛手套"
  itemId: 530049
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000295
  poolId: 10004
  name: "热带风情上装"
  itemId: 510108
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000296
  poolId: 10004
  name: "热带风情下装"
  itemId: 520070
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000297
  poolId: 10004
  name: "热带风情手套"
  itemId: 530050
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000298
  poolId: 10004
  name: "新奇撞色上装"
  itemId: 510109
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000299
  poolId: 10004
  name: "新奇撞色下装"
  itemId: 520071
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000300
  poolId: 10004
  name: "新奇撞色手套"
  itemId: 530051
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000301
  poolId: 10004
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000302
  poolId: 10004
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000303
  poolId: 10004
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000304
  poolId: 10005
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000305
  poolId: 10005
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1000306
  poolId: 10005
  name: "神奇水彩*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000307
  poolId: 10005
  name: "神奇水彩*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000308
  poolId: 10005
  name: "神奇水彩*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1000309
  poolId: 10005
  name: "喵喵快乐罐"
  itemId: 630259
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000310
  poolId: 10005
  name: "科技起源"
  itemId: 610188
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000311
  poolId: 10005
  name: "赛博夜影"
  itemId: 610189
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000312
  poolId: 10005
  name: "微笑娃娃"
  itemId: 620329
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000313
  poolId: 10005
  name: "肉棕棕"
  itemId: 630216
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000314
  poolId: 10005
  name: "粉嘟嘟"
  itemId: 630217
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000315
  poolId: 10005
  name: "星夜太妃糖"
  itemId: 630155
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000316
  poolId: 10005
  name: "木星怪咖"
  itemId: 610115
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000317
  poolId: 10005
  name: "水星来客"
  itemId: 610116
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000318
  poolId: 10005
  name: "八段锦柒式"
  itemId: 720096
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000319
  poolId: 10005
  name: "八段锦捌式"
  itemId: 720097
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000320
  poolId: 10005
  name: "八段锦伍式"
  itemId: 720094
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000321
  poolId: 10005
  name: "八段锦陆式"
  itemId: 720095
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000322
  poolId: 10005
  name: "不要过来！"
  itemId: 711021
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000323
  poolId: 10005
  name: "不好意思鸭"
  itemId: 711027
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000324
  poolId: 10005
  name: "好伤心"
  itemId: 710163
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000325
  poolId: 10005
  name: "爆发蓄力中"
  itemId: 710162
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000326
  poolId: 10005
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000327
  poolId: 10005
  name: "夏意清新上装"
  itemId: 510182
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000328
  poolId: 10005
  name: "夏意清新下装"
  itemId: 520125
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000329
  poolId: 10005
  name: "夏意清新手套"
  itemId: 530102
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000330
  poolId: 10005
  name: "盛夏果实上装"
  itemId: 510183
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000331
  poolId: 10005
  name: "盛夏果实下装"
  itemId: 520126
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000332
  poolId: 10005
  name: "盛夏果实手套"
  itemId: 530103
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000333
  poolId: 10005
  name: "仲夏芳菲上装"
  itemId: 510200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000334
  poolId: 10005
  name: "仲夏芳菲下装"
  itemId: 520139
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000335
  poolId: 10005
  name: "仲夏芳菲手套"
  itemId: 530116
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000336
  poolId: 10005
  name: "童梦奇缘上装"
  itemId: 510201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000337
  poolId: 10005
  name: "童梦奇缘下装"
  itemId: 520140
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000338
  poolId: 10005
  name: "童梦奇缘手套"
  itemId: 530117
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000339
  poolId: 10005
  name: "青柠之恋上装"
  itemId: 510202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000340
  poolId: 10005
  name: "青柠之恋下装"
  itemId: 520141
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000341
  poolId: 10005
  name: "青柠之恋手套"
  itemId: 530118
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 4
}
rows {
  rewardId: 1000342
  poolId: 10005
  name: "灰调潮流上装"
  itemId: 510159
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000343
  poolId: 10005
  name: "灰调潮流下装"
  itemId: 520110
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000344
  poolId: 10005
  name: "灰调潮流手套"
  itemId: 530087
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000345
  poolId: 10005
  name: "灰粉嘻哈上装"
  itemId: 510160
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000346
  poolId: 10005
  name: "灰粉嘻哈下装"
  itemId: 520111
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000347
  poolId: 10005
  name: "灰粉嘻哈手套"
  itemId: 530088
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000348
  poolId: 10005
  name: "多元时尚上装"
  itemId: 510161
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000349
  poolId: 10005
  name: "多元时尚下装"
  itemId: 520112
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000350
  poolId: 10005
  name: "多元时尚手套"
  itemId: 530089
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000351
  poolId: 10005
  name: "古韵长衣上装"
  itemId: 510153
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000352
  poolId: 10005
  name: "古韵长衣下装"
  itemId: 520105
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000353
  poolId: 10005
  name: "古韵长衣手套"
  itemId: 530082
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000354
  poolId: 10005
  name: "气息归元上装"
  itemId: 510154
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000355
  poolId: 10005
  name: "气息归元下装"
  itemId: 520106
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000356
  poolId: 10005
  name: "气息归元手套"
  itemId: 530083
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000357
  poolId: 10005
  name: "邻家学妹上装"
  itemId: 510133
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000358
  poolId: 10005
  name: "邻家学妹下装"
  itemId: 520088
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000359
  poolId: 10005
  name: "邻家学妹手套"
  itemId: 530066
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000360
  poolId: 10005
  name: "精英学姐上装"
  itemId: 510134
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000361
  poolId: 10005
  name: "精英学姐下装"
  itemId: 520089
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000362
  poolId: 10005
  name: "精英学姐手套"
  itemId: 530067
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000363
  poolId: 10005
  name: "隐藏富豪上装"
  itemId: 510136
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000364
  poolId: 10005
  name: "隐藏富豪下装"
  itemId: 520091
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000365
  poolId: 10005
  name: "隐藏富豪手套"
  itemId: 530069
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000366
  poolId: 10005
  name: "年会爆款上装"
  itemId: 510137
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000367
  poolId: 10005
  name: "年会爆款下装"
  itemId: 520092
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000368
  poolId: 10005
  name: "年会爆款手套"
  itemId: 530070
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000369
  poolId: 10005
  name: "进步青年上装"
  itemId: 510138
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000370
  poolId: 10005
  name: "进步青年下装"
  itemId: 520093
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000371
  poolId: 10005
  name: "进步青年手套"
  itemId: 530071
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000372
  poolId: 10005
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000373
  poolId: 10005
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000374
  poolId: 10005
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 33
  poolId: 2
  name: "毛绒碎片*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
}
rows {
  rewardId: 3000001
  poolId: 3000
  name: "狐剑仙"
  itemId: 400340
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000002
  poolId: 3000
  name: "狐剑仙隐藏款"
  itemId: 400570
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000003
  poolId: 3000
  name: "狐仙剑"
  itemId: 620013
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000004
  poolId: 3000
  name: "狐仙葫芦"
  itemId: 620038
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000005
  poolId: 3000
  name: "狐仙面具"
  itemId: 610010
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000006
  poolId: 3000
  name: "蝴蝶"
  itemId: 400540
  itemNum: 1
  groupId: 4
  weight: 7
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000007
  poolId: 3000
  name: "桃花"
  itemId: 400550
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000008
  poolId: 3000
  name: "桃花-头饰"
  itemId: 630009
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000009
  poolId: 3000
  name: "蝶语幻镜"
  itemId: 610016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000010
  poolId: 3000
  name: "蝴蝶-翅膀"
  itemId: 620032
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000011
  poolId: 3000
  name: "头像框-狐影清逸"
  itemId: 840031
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000012
  poolId: 3000
  name: "星梦蝴蝶"
  itemId: 202
  itemNum: 1
  groupId: 13
  weight: 50
  limit: 3
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000013
  poolId: 3000
  name: "霸总亲亲"
  itemId: 720024
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000014
  poolId: 3000
  name: "空气吉他"
  itemId: 720010
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000015
  poolId: 3000
  name: "吃瓜"
  itemId: 720015
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000016
  poolId: 3000
  name: "上香祈福"
  itemId: 710010
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000017
  poolId: 3000
  name: "做鬼脸"
  itemId: 710012
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000018
  poolId: 3000
  name: "细细品味"
  itemId: 710013
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000019
  poolId: 3000
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000020
  poolId: 3000
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000021
  poolId: 3000
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000022
  poolId: 3000
  name: "渔小夫"
  itemId: 400260
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000023
  poolId: 3000
  name: "铃兰"
  itemId: 400510
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000024
  poolId: 3000
  name: "涂鸦小子"
  itemId: 400180
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000025
  poolId: 3000
  name: "理理我嘛（双人）"
  itemId: 720047
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000026
  poolId: 3000
  name: "捧腹大笑"
  itemId: 720027
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000027
  poolId: 3000
  name: "锤地大哭"
  itemId: 720008
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000028
  poolId: 3000
  name: "你过来呀"
  itemId: 720006
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000029
  poolId: 3000
  name: "满地打滚"
  itemId: 720007
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000030
  poolId: 3000
  name: "打哈欠"
  itemId: 710045
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000031
  poolId: 3000
  name: "听我解释"
  itemId: 710001
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000032
  poolId: 3000
  name: "慢走不送"
  itemId: 710003
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000033
  poolId: 3000
  name: "尴尬"
  itemId: 710036
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000034
  poolId: 3000
  name: "海盐奶盖上装"
  itemId: 510046
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000035
  poolId: 3000
  name: "海盐奶盖下装"
  itemId: 520036
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000036
  poolId: 3000
  name: "夏日微风上装"
  itemId: 510004
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000037
  poolId: 3000
  name: "夏日微风下装"
  itemId: 520004
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000038
  poolId: 3000
  name: "夏日微风手套"
  itemId: 530006
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000039
  poolId: 3000
  name: "赤土探险上装"
  itemId: 510027
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000040
  poolId: 3000
  name: "赤土探险下装"
  itemId: 520025
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000041
  poolId: 3000
  name: "赤土探险手套"
  itemId: 530018
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000042
  poolId: 3000
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000043
  poolId: 3000
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000044
  poolId: 3000
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000045
  poolId: 3000
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5000001
  poolId: 5000
  name: "橙色翅膀"
  itemId: 620041
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5000002
  poolId: 5000
  name: "竹蜻蜓"
  itemId: 630012
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5000003
  poolId: 5000
  name: "不许看眼罩"
  itemId: 610008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5000004
  poolId: 5000
  name: "暗中观察"
  itemId: 710049
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5000005
  poolId: 5000
  name: "派对狂欢"
  itemId: 720060
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5000006
  poolId: 5000
  name: "呱呱舞"
  itemId: 720075
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5000007
  poolId: 5000
  name: "一发入魂上装"
  itemId: 510062
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5000008
  poolId: 5000
  name: "一发入魂下装"
  itemId: 520046
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5000009
  poolId: 5000
  name: "一发入魂手套"
  itemId: 530025
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5101001
  poolId: 51010
  name: "龙虾小新"
  itemId: 400650
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101002
  poolId: 51010
  name: "红黑墨镜"
  itemId: 610019
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101003
  poolId: 51010
  name: "小新噢耶"
  itemId: 710051
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101004
  poolId: 51010
  name: "小白背包"
  itemId: 620072
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101005
  poolId: 51011
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101006
  poolId: 51011
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101007
  poolId: 51011
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101008
  poolId: 51011
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101009
  poolId: 51011
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101010
  poolId: 51011
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101011
  poolId: 51011
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101012
  poolId: 51012
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101013
  poolId: 51012
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101014
  poolId: 51012
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101015
  poolId: 51012
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101016
  poolId: 51012
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101017
  poolId: 51012
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101018
  poolId: 51012
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101019
  poolId: 51013
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101020
  poolId: 51013
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101021
  poolId: 51013
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101022
  poolId: 51013
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101023
  poolId: 51013
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101024
  poolId: 51013
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101025
  poolId: 51013
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101026
  poolId: 51014
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101027
  poolId: 51014
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101028
  poolId: 51014
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101029
  poolId: 51014
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101030
  poolId: 51014
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101031
  poolId: 51014
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101032
  poolId: 51014
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101033
  poolId: 51020
  name: "动感超人小新"
  itemId: 400640
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101034
  poolId: 51020
  name: "小新双色眼镜"
  itemId: 610020
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101035
  poolId: 51020
  name: "动感超人表情"
  itemId: 710052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101036
  poolId: 51020
  name: "康达姆机器人"
  itemId: 620071
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101037
  poolId: 51021
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101038
  poolId: 51021
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101039
  poolId: 51021
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101040
  poolId: 51021
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101041
  poolId: 51021
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101042
  poolId: 51021
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101043
  poolId: 51021
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101044
  poolId: 51022
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101045
  poolId: 51022
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101046
  poolId: 51022
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101047
  poolId: 51022
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101048
  poolId: 51022
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101049
  poolId: 51022
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101050
  poolId: 51022
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101051
  poolId: 51023
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101052
  poolId: 51023
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101053
  poolId: 51023
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101054
  poolId: 51023
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101055
  poolId: 51023
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101056
  poolId: 51023
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101057
  poolId: 51023
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101058
  poolId: 51024
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101059
  poolId: 51024
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101060
  poolId: 51024
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101061
  poolId: 51024
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101062
  poolId: 51024
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101063
  poolId: 51024
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101064
  poolId: 51024
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101065
  poolId: 51030
  name: "星梦者"
  itemId: 400330
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101066
  poolId: 51030
  name: "翅膀背包"
  itemId: 620035
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101067
  poolId: 51030
  name: "发光星星"
  itemId: 630006
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101068
  poolId: 51030
  name: "睡眠眼罩"
  itemId: 610013
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101069
  poolId: 51031
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101070
  poolId: 51031
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101071
  poolId: 51031
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101072
  poolId: 51031
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101073
  poolId: 51031
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101074
  poolId: 51031
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101075
  poolId: 51031
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101076
  poolId: 51032
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101077
  poolId: 51032
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101078
  poolId: 51032
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101079
  poolId: 51032
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101080
  poolId: 51032
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101081
  poolId: 51032
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101082
  poolId: 51032
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101083
  poolId: 51033
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101084
  poolId: 51033
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101085
  poolId: 51033
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101086
  poolId: 51033
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101087
  poolId: 51033
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101088
  poolId: 51033
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101089
  poolId: 51033
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101090
  poolId: 51034
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101091
  poolId: 51034
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101092
  poolId: 51034
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101093
  poolId: 51034
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101094
  poolId: 51034
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101095
  poolId: 51034
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101096
  poolId: 51034
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101097
  poolId: 51040
  name: "龙虾小新"
  itemId: 400650
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101098
  poolId: 51040
  name: "红黑墨镜"
  itemId: 610019
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101099
  poolId: 51040
  name: "小新噢耶"
  itemId: 710051
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101100
  poolId: 51040
  name: "小白背包"
  itemId: 620072
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101101
  poolId: 51041
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101102
  poolId: 51041
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101103
  poolId: 51041
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101104
  poolId: 51041
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101105
  poolId: 51041
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101106
  poolId: 51041
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101107
  poolId: 51041
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101108
  poolId: 51042
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101109
  poolId: 51042
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101110
  poolId: 51042
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101111
  poolId: 51042
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101112
  poolId: 51042
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101113
  poolId: 51042
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101114
  poolId: 51042
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101115
  poolId: 51043
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101116
  poolId: 51043
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101117
  poolId: 51043
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101118
  poolId: 51043
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101119
  poolId: 51043
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101120
  poolId: 51043
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101121
  poolId: 51043
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101122
  poolId: 51044
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101123
  poolId: 51044
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101124
  poolId: 51044
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101125
  poolId: 51044
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101126
  poolId: 51044
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101127
  poolId: 51044
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101128
  poolId: 51044
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101129
  poolId: 51050
  name: "动感超人小新"
  itemId: 400640
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101130
  poolId: 51050
  name: "小新双色眼镜"
  itemId: 610020
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101131
  poolId: 51050
  name: "动感超人表情"
  itemId: 710052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101132
  poolId: 51050
  name: "康达姆机器人"
  itemId: 620071
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5101133
  poolId: 51051
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101134
  poolId: 51051
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101135
  poolId: 51051
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101136
  poolId: 51051
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101137
  poolId: 51051
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101138
  poolId: 51051
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101139
  poolId: 51051
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101140
  poolId: 51052
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101141
  poolId: 51052
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101142
  poolId: 51052
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101143
  poolId: 51052
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101144
  poolId: 51052
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101145
  poolId: 51052
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101146
  poolId: 51052
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101147
  poolId: 51053
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101148
  poolId: 51053
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101149
  poolId: 51053
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101150
  poolId: 51053
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101151
  poolId: 51053
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101152
  poolId: 51053
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101153
  poolId: 51053
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101154
  poolId: 51054
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101155
  poolId: 51054
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101156
  poolId: 51054
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5101157
  poolId: 51054
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101158
  poolId: 51054
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101159
  poolId: 51054
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5101160
  poolId: 51054
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107000
  poolId: 51070
  name: "神龙大侠 阿宝"
  itemId: 401130
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5107001
  poolId: 51070
  name: "萌包出笼"
  itemId: 630098
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5107002
  poolId: 51070
  name: "智慧之杖"
  itemId: 620186
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5107003
  poolId: 51070
  name: "功夫巨星"
  itemId: 720146
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5107004
  poolId: 51070
  name: "阿宝吃惊"
  itemId: 710126
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5107005
  poolId: 51070
  name: "阿宝头像"
  itemId: 860020
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5107006
  poolId: 51071
  name: "幸运钥匙"
  groupId: 2
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5107007
  poolId: 51071
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107008
  poolId: 51071
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107009
  poolId: 51071
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107010
  poolId: 51071
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107011
  poolId: 51071
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107012
  poolId: 51071
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107013
  poolId: 51072
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5107014
  poolId: 51072
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107015
  poolId: 51072
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107016
  poolId: 51072
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107017
  poolId: 51072
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107018
  poolId: 51072
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107019
  poolId: 51072
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107020
  poolId: 51073
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5107021
  poolId: 51073
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107022
  poolId: 51073
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107023
  poolId: 51073
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107024
  poolId: 51073
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107025
  poolId: 51073
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107026
  poolId: 51073
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107027
  poolId: 51074
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5107028
  poolId: 51074
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107029
  poolId: 51074
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107030
  poolId: 51074
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107031
  poolId: 51074
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107032
  poolId: 51074
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107033
  poolId: 51074
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107034
  poolId: 51075
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5107035
  poolId: 51075
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107036
  poolId: 51075
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107037
  poolId: 51075
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107038
  poolId: 51075
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107039
  poolId: 51075
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107040
  poolId: 51075
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107041
  poolId: 51076
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5107042
  poolId: 51076
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107043
  poolId: 51076
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107044
  poolId: 51076
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107045
  poolId: 51076
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107046
  poolId: 51076
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5107047
  poolId: 51076
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108000
  poolId: 51080
  name: "师傅"
  itemId: 401740
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5108001
  poolId: 51080
  name: "功夫竹笛"
  itemId: 620187
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5108002
  poolId: 51080
  name: "宗师眼镜"
  itemId: 610086
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5108003
  poolId: 51080
  name: "神龙摆尾"
  itemId: 720110
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5108004
  poolId: 51080
  name: "师傅沉默"
  itemId: 710124
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5108005
  poolId: 51080
  name: "师傅头像"
  itemId: 860021
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5108006
  poolId: 51081
  name: "幸运钥匙"
  groupId: 2
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5108007
  poolId: 51081
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108008
  poolId: 51081
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108009
  poolId: 51081
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108010
  poolId: 51081
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108011
  poolId: 51081
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108012
  poolId: 51081
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108013
  poolId: 51082
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5108014
  poolId: 51082
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108015
  poolId: 51082
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108016
  poolId: 51082
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108017
  poolId: 51082
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108018
  poolId: 51082
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108019
  poolId: 51082
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108020
  poolId: 51083
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5108021
  poolId: 51083
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108022
  poolId: 51083
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108023
  poolId: 51083
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108024
  poolId: 51083
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108025
  poolId: 51083
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108026
  poolId: 51083
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108027
  poolId: 51084
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5108028
  poolId: 51084
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108029
  poolId: 51084
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108030
  poolId: 51084
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108031
  poolId: 51084
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108032
  poolId: 51084
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108033
  poolId: 51084
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108034
  poolId: 51085
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5108035
  poolId: 51085
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108036
  poolId: 51085
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108037
  poolId: 51085
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108038
  poolId: 51085
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108039
  poolId: 51085
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108040
  poolId: 51085
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108041
  poolId: 51086
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5108042
  poolId: 51086
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108043
  poolId: 51086
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108044
  poolId: 51086
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108045
  poolId: 51086
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108046
  poolId: 51086
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5108047
  poolId: 51086
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 510901
  poolId: 51091
  name: "小真"
  itemId: 401610
  itemNum: 1
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 147
  poolId: 13
  name: "英伦警卫"
  itemId: 400270
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
  isGrand: true
  lvCost: 100
}
rows {
  rewardId: 148
  poolId: 13
  name: "表情-吓到了"
  itemId: 710004
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 149
  poolId: 13
  name: "毛绒碎片*20"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 100
}
rows {
  rewardId: 150
  poolId: 13
  name: "活跃币*1"
  itemId: 4
  itemNum: 1
  groupId: 1
  weight: 100
}
rows {
  rewardId: 520101
  poolId: 52010
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 520102
  poolId: 52010
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 520103
  poolId: 52010
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 2
  weight: 1
  limit: 3
}
rows {
  rewardId: 520104
  poolId: 52010
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520105
  poolId: 52010
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520106
  poolId: 52011
  name: "空道具"
  groupId: 1
  weight: 1
}
rows {
  rewardId: 530001
  poolId: 5301
  name: "月光女神"
  itemId: 400630
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 530002
  poolId: 5301
  name: "月光女神面纱"
  itemId: 610044
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530003
  poolId: 5301
  name: "月光权杖"
  itemId: 620088
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530004
  poolId: 5301
  name: "悬浮星球"
  itemId: 630044
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530005
  poolId: 5301
  name: "四叶草精灵"
  itemId: 401060
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530006
  poolId: 5301
  name: "魔法师哈奇"
  itemId: 401040
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530007
  poolId: 5301
  name: "复古单片镜"
  itemId: 610027
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530008
  poolId: 5301
  name: "蜜罐儿"
  itemId: 620104
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530009
  poolId: 5301
  name: "橄榄花环"
  itemId: 630025
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530011
  poolId: 5301
  name: "炫彩独角兽"
  itemId: 401200
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530013
  poolId: 5301
  name: "蝶舞幽梦"
  itemId: 840053
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 530015
  poolId: 5301
  name: "星芒券*20"
  itemId: 203
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 530017
  poolId: 5301
  name: "星芒券*5"
  itemId: 203
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 530019
  poolId: 5301
  name: "星芒券*3"
  itemId: 203
  itemNum: 3
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530021
  poolId: 5301
  name: "星芒券*2"
  itemId: 203
  itemNum: 2
  groupId: 3
  weight: 25
}
rows {
  rewardId: 530023
  poolId: 5301
  name: "星芒券*1"
  itemId: 203
  itemNum: 1
  groupId: 3
  weight: 30
}
rows {
  rewardId: 530025
  poolId: 5301
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530027
  poolId: 5301
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530029
  poolId: 5301
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530031
  poolId: 5301
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530033
  poolId: 5301
  name: "棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530035
  poolId: 5301
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530037
  poolId: 5301
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 3
  weight: 2
}
rows {
  rewardId: 530201
  poolId: 5302
  name: "黑暗王子"
  itemId: 400300
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 530202
  poolId: 5302
  name: "蓝幽夜弓"
  itemId: 620020
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530203
  poolId: 5302
  name: "舞会假面"
  itemId: 610036
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530204
  poolId: 5302
  name: "使魔"
  itemId: 630092
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530205
  poolId: 5302
  name: "星光游侠 艾莉娜"
  itemId: 401810
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530206
  poolId: 5302
  name: "皮皮火 泰比"
  itemId: 401820
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530207
  poolId: 5302
  name: "夜猫子眼镜"
  itemId: 610096
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530208
  poolId: 5302
  name: "星光之矢"
  itemId: 620176
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530209
  poolId: 5302
  name: "草莓塔塔"
  itemId: 630127
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530210
  poolId: 5302
  name: "观星者"
  itemId: 401730
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530211
  poolId: 5302
  name: "雪夜星辰"
  itemId: 840081
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 530212
  poolId: 5302
  name: "臻藏代币*20"
  itemId: 203
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 530213
  poolId: 5302
  name: "臻藏代币*5"
  itemId: 203
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 530214
  poolId: 5302
  name: "臻藏代币*3"
  itemId: 203
  itemNum: 3
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530215
  poolId: 5302
  name: "臻藏代币*2"
  itemId: 203
  itemNum: 2
  groupId: 3
  weight: 25
}
rows {
  rewardId: 530216
  poolId: 5302
  name: "臻藏代币*1"
  itemId: 203
  itemNum: 1
  groupId: 3
  weight: 30
}
rows {
  rewardId: 530217
  poolId: 5302
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530218
  poolId: 5302
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530219
  poolId: 5302
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530220
  poolId: 5302
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530221
  poolId: 5302
  name: "棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530222
  poolId: 5302
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530223
  poolId: 5302
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 3
  weight: 2
}
rows {
  rewardId: 540101
  poolId: 5401
  name: "蜡笔小新"
  itemId: 400690
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540102
  poolId: 5401
  name: "蜡笔小新头像"
  itemId: 860002
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540103
  poolId: 5401
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540104
  poolId: 5401
  name: "万能棉花20"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540105
  poolId: 5401
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540106
  poolId: 5401
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540201
  poolId: 5402
  name: "暴暴龙"
  itemId: 401360
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540202
  poolId: 5402
  name: "暴暴龙头像"
  itemId: 860011
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540203
  poolId: 5402
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540204
  poolId: 5402
  name: "万能棉花40"
  itemId: 6
  itemNum: 40
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540205
  poolId: 5402
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540206
  poolId: 5402
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540301
  poolId: 5403
  name: "蜡笔小新"
  itemId: 400690
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540302
  poolId: 5403
  name: "蜡笔小新头像"
  itemId: 860002
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540303
  poolId: 5403
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540304
  poolId: 5403
  name: "万能棉花20"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540305
  poolId: 5403
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540306
  poolId: 5403
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 7000010
  poolId: 7000
  name: "（绿）星小递上衣*1"
  itemId: 510019
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
}
rows {
  rewardId: 7000011
  poolId: 7000
  name: "（绿）星小递下衣*1"
  itemId: 520016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 7000012
  poolId: 7000
  name: "（绿）星小递手套*1"
  itemId: 530010
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
}
rows {
  rewardId: 7000013
  poolId: 7000
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 3
  weight: 10
  limit: 4
}
rows {
  rewardId: 7000014
  poolId: 7000
  name: "小乔背饰"
  itemId: 620080
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 7000015
  poolId: 7000
  name: "印章*100"
  itemId: 4
  itemNum: 100
  groupId: 3
  weight: 50
  limit: 16
}
rows {
  rewardId: 7000016
  poolId: 7000
  name: "表情-亲亲"
  itemId: 710021
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
}
rows {
  rewardId: 7000017
  poolId: 7000
  name: "甜蜜初心*1"
  itemId: 200014
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 4
}
rows {
  rewardId: 7000018
  poolId: 7000
  name: "心心糖果*1"
  itemId: 200015
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 5
  isGrand: true
}
rows {
  rewardId: 8000001
  poolId: 8000
  name: "喵小困"
  itemId: 400130
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
}
rows {
  rewardId: 8000002
  poolId: 8000
  name: "河小呆"
  itemId: 400040
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
}
rows {
  rewardId: 8000003
  poolId: 8000
  name: "熊抱（双人）"
  itemId: 720012
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 10
}
rows {
  rewardId: 8000004
  poolId: 8000
  name: "暗送秋波"
  itemId: 710008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 10
}
rows {
  rewardId: 8000005
  poolId: 8000
  name: "神奇水彩*10"
  itemId: 2007
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 10
}
rows {
  rewardId: 8000006
  poolId: 8000
  name: "棉花*5"
  itemId: 6
  itemNum: 5
  groupId: 2
  weight: 10
  limit: 10
}
rows {
  rewardId: 8000007
  poolId: 8000
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 2
  weight: 10
  limit: 38
}
rows {
  rewardId: 8000008
  poolId: 8000
  name: "甜蜜初心*2"
  itemId: 200014
  itemNum: 2
  groupId: 2
  weight: 10
  limit: 10
}
rows {
  rewardId: 8000009
  poolId: 8000
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 10
}
rows {
  rewardId: 5202001
  poolId: 5202
  name: "toby"
  itemId: 401000
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5202002
  poolId: 5202
  name: "桃气满满"
  itemId: 620100
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5202003
  poolId: 5202
  name: "踢踏舞"
  itemId: 720063
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5202004
  poolId: 5202
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5202005
  poolId: 5202
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5202006
  poolId: 5202
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5202007
  poolId: 5202
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5202008
  poolId: 5202
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5202009
  poolId: 5202
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 3000101
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 10
  groupId: 1
  weight: 1000
}
rows {
  rewardId: 3000102
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 12
  groupId: 1
  weight: 1700
}
rows {
  rewardId: 3000103
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 16
  groupId: 2
  weight: 600
}
rows {
  rewardId: 3000104
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 18
  groupId: 2
  weight: 500
}
rows {
  rewardId: 3000105
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 20
  groupId: 3
  weight: 100
}
rows {
  rewardId: 3000106
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 24
  groupId: 3
  weight: 50
  limit: 1
}
rows {
  rewardId: 3000107
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 30
  groupId: 4
  weight: 10
  limit: 1
}
rows {
  rewardId: 3000108
  poolId: 30001
  name: "星愿币*2"
  itemId: 201
  itemNum: 36
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 5001001
  poolId: 5001
  name: "奶龙"
  itemId: 401010
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5001002
  poolId: 5001
  name: "动作-奶龙舞鱼"
  itemId: 720098
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5001003
  poolId: 5001
  name: "配饰-奶龙背包"
  itemId: 620147
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5001004
  poolId: 5001
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5001005
  poolId: 5001
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5001006
  poolId: 5001
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5001007
  poolId: 5001
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5001008
  poolId: 5001
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5001009
  poolId: 5001
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5002001
  poolId: 5002
  name: "古风灯笼"
  itemId: 620134
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  extraItemIds: 725204
  extraItemNums: 1
}
rows {
  rewardId: 5002002
  poolId: 5002
  name: "青花茶盏"
  itemId: 630063
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5002003
  poolId: 5002
  name: "木芙蓉花"
  itemId: 610049
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5002004
  poolId: 5002
  name: "为你星动"
  itemId: 710077
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5002005
  poolId: 5002
  name: "施展魔法"
  itemId: 720022
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5002006
  poolId: 5002
  name: "你干什么？"
  itemId: 710075
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5002007
  poolId: 5002
  name: "软糯奶牛上装"
  itemId: 510085
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5002008
  poolId: 5002
  name: "软糯奶牛下装"
  itemId: 520057
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5002009
  poolId: 5002
  name: "软糯奶牛手套"
  itemId: 530036
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 3000201
  poolId: 30002
  name: "小龙女 洛灵"
  itemId: 401340
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000202
  poolId: 30002
  name: "龙宫之主 洛灵"
  itemId: 401350
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000203
  poolId: 30002
  name: "沧海贝环"
  itemId: 620149
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000204
  poolId: 30002
  name: "潮汐之冠"
  itemId: 630073
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000205
  poolId: 30002
  name: "鲛人泪"
  itemId: 610074
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000206
  poolId: 30002
  name: " 鲤小仙  锦瞳"
  itemId: 401020
  itemNum: 1
  groupId: 4
  weight: 7
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000207
  poolId: 30002
  name: "虎啸龙 毕小烈"
  itemId: 401050
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000208
  poolId: 30002
  name: "烈之翼"
  itemId: 620137
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000209
  poolId: 30002
  name: "锦鲤附体"
  itemId: 630066
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000210
  poolId: 30002
  name: "嗷呜呜"
  itemId: 610063
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000211
  poolId: 30002
  name: "头像框-碧水寒潭"
  itemId: 840052
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000213
  poolId: 30002
  name: "灵光一现"
  itemId: 720019
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000214
  poolId: 30002
  name: "生气极了"
  itemId: 720044
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000215
  poolId: 30002
  name: "重锤出击"
  itemId: 720018
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000216
  poolId: 30002
  name: "墨镜小红狐"
  itemId: 710096
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000217
  poolId: 30002
  name: "哼哼"
  itemId: 710097
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000218
  poolId: 30002
  name: "哈哈"
  itemId: 710098
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000219
  poolId: 30002
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000220
  poolId: 30002
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000221
  poolId: 30002
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000222
  poolId: 30002
  name: "精小卫"
  itemId: 401150
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000223
  poolId: 30002
  name: "夔牛牛"
  itemId: 401160
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000224
  poolId: 30002
  name: "鹿灵灵"
  itemId: 401170
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000225
  poolId: 30002
  name: "加油"
  itemId: 720068
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000226
  poolId: 30002
  name: "鼓掌"
  itemId: 720069
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000227
  poolId: 30002
  name: "无奈"
  itemId: 720104
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000228
  poolId: 30002
  name: "开合跳"
  itemId: 720102
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000229
  poolId: 30002
  name: "开车舞"
  itemId: 720103
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000230
  poolId: 30002
  name: "斜眼笑"
  itemId: 710100
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000231
  poolId: 30002
  name: "发呆"
  itemId: 710101
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000232
  poolId: 30002
  name: "对对对"
  itemId: 710102
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000233
  poolId: 30002
  name: "星星眼"
  itemId: 710103
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000234
  poolId: 30002
  name: "像素风"
  itemId: 510073
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000235
  poolId: 30002
  name: "像素风"
  itemId: 520048
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000236
  poolId: 30002
  name: "像素风"
  itemId: 530027
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000237
  poolId: 30002
  name: "薯条"
  itemId: 510081
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000238
  poolId: 30002
  name: "薯条"
  itemId: 520053
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000239
  poolId: 30002
  name: "薯条"
  itemId: 530032
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000240
  poolId: 30002
  name: "涂鸦"
  itemId: 510074
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000241
  poolId: 30002
  name: "涂鸦"
  itemId: 520049
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000242
  poolId: 30002
  name: "涂鸦"
  itemId: 530028
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000243
  poolId: 30002
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000244
  poolId: 30002
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000245
  poolId: 30002
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000246
  poolId: 30002
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5003001
  poolId: 5003
  name: "迪迦"
  itemId: 401030
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5003002
  poolId: 5003
  name: "哉佩利敖光线"
  itemId: 720084
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5003003
  poolId: 5003
  name: "神光棒"
  itemId: 620103
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5003004
  poolId: 5003
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5003005
  poolId: 5003
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5003006
  poolId: 5003
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5003007
  poolId: 5003
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5003008
  poolId: 5003
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5003009
  poolId: 5003
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5003010
  poolId: 5003
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5003011
  poolId: 5003
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5003012
  poolId: 5003
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004001
  poolId: 5004
  name: "赛罗"
  itemId: 401100
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5004002
  poolId: 5004
  name: "等离子火花斩"
  itemId: 720086
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5004003
  poolId: 5004
  name: "赛罗眼镜"
  itemId: 610048
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5004004
  poolId: 5004
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5004005
  poolId: 5004
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004006
  poolId: 5004
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004007
  poolId: 5004
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004008
  poolId: 5004
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004009
  poolId: 5004
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004010
  poolId: 5004
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004011
  poolId: 5004
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5004012
  poolId: 5004
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005001
  poolId: 5005
  name: "泽塔"
  itemId: 401120
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5005002
  poolId: 5005
  name: "泽斯蒂姆光线"
  itemId: 720085
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5005003
  poolId: 5005
  name: "泽塔升华器"
  itemId: 620102
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5005004
  poolId: 5005
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5005005
  poolId: 5005
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005006
  poolId: 5005
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005007
  poolId: 5005
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005008
  poolId: 5005
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005009
  poolId: 5005
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005010
  poolId: 5005
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005011
  poolId: 5005
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5005012
  poolId: 5005
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5010001
  poolId: 5010
  name: "冰雪玫瑰"
  itemId: 620155
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5010002
  poolId: 5010
  name: "冰雪皇冠"
  itemId: 630081
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5010003
  poolId: 5010
  name: "一支玫瑰"
  itemId: 610052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5010004
  poolId: 5010
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5010005
  poolId: 5010
  name: "伤心猫猫舞"
  itemId: 720100
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5010006
  poolId: 5010
  name: "吃惊"
  itemId: 710099
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5010007
  poolId: 5010
  name: "豹纹野人套装"
  itemId: 510075
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5010008
  poolId: 5010
  name: "福星币*200"
  itemId: 3110
  itemNum: 200
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5010009
  poolId: 5010
  name: "好运来"
  itemId: 720118
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 8001001
  poolId: 8001
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8001002
  poolId: 8001
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8001003
  poolId: 8001
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8001004
  poolId: 8001
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8001005
  poolId: 8001
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8001006
  poolId: 8001
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8001007
  poolId: 8001
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8001008
  poolId: 8001
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8001009
  poolId: 8001
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8001010
  poolId: 8001
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8001012
  poolId: 8001
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8001011
  poolId: 8001
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 10
}
rows {
  rewardId: 8001013
  poolId: 8001
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 8001014
  poolId: 8001
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8001015
  poolId: 8001
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8001016
  poolId: 8001
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8001017
  poolId: 8001
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 8001018
  poolId: 8001
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8001019
  poolId: 8001
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8001020
  poolId: 8001
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8001021
  poolId: 8001
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8001022
  poolId: 8001
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8001023
  poolId: 8001
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8001024
  poolId: 8001
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8001025
  poolId: 8001
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 5011001
  poolId: 5011
  name: "toby"
  itemId: 401000
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5011002
  poolId: 5011
  name: "桃气满满"
  itemId: 620100
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5011003
  poolId: 5011
  name: "踢踏舞"
  itemId: 720063
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5011004
  poolId: 5011
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5011005
  poolId: 5011
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5011006
  poolId: 5011
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5011007
  poolId: 5011
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5011008
  poolId: 5011
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5011009
  poolId: 5011
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106065
  poolId: 51060
  name: "星梦者"
  itemId: 400330
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5106066
  poolId: 51060
  name: "翅膀背包"
  itemId: 620035
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106067
  poolId: 51060
  name: "发光星星"
  itemId: 630006
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106068
  poolId: 51060
  name: "睡眠眼罩"
  itemId: 610013
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106069
  poolId: 51061
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106070
  poolId: 51061
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106071
  poolId: 51061
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5106072
  poolId: 51061
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106073
  poolId: 51061
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106074
  poolId: 51061
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106075
  poolId: 51061
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 5106076
  poolId: 51062
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106077
  poolId: 51062
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106078
  poolId: 51062
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5106079
  poolId: 51062
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106080
  poolId: 51062
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106081
  poolId: 51062
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106082
  poolId: 51062
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106083
  poolId: 51063
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106084
  poolId: 51063
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106085
  poolId: 51063
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5106086
  poolId: 51063
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106087
  poolId: 51063
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106088
  poolId: 51063
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106089
  poolId: 51063
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106090
  poolId: 51064
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106091
  poolId: 51064
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106092
  poolId: 51064
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5106093
  poolId: 51064
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106094
  poolId: 51064
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106095
  poolId: 51064
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5106096
  poolId: 51064
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5012001
  poolId: 5012
  name: "欧米"
  itemId: 401090
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5012002
  poolId: 5012
  name: "无限追踪"
  itemId: 610071
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5012003
  poolId: 5012
  name: "兔子舞"
  itemId: 720062
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5012004
  poolId: 5012
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5012005
  poolId: 5012
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5012006
  poolId: 5012
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5012007
  poolId: 5012
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5012008
  poolId: 5012
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5012009
  poolId: 5012
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5501001
  poolId: 55010
  name: "进度1"
  groupId: 1
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501002
  poolId: 55010
  name: "进度2"
  groupId: 2
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501003
  poolId: 55010
  name: "进度3"
  groupId: 3
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501004
  poolId: 55010
  name: "进度4"
  itemId: 310104
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501005
  poolId: 55010
  name: "进度5"
  groupId: 5
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501006
  poolId: 55010
  name: "进度6"
  groupId: 6
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501007
  poolId: 55010
  name: "进度7"
  itemId: 310105
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501008
  poolId: 55010
  name: "进度8"
  groupId: 8
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501009
  poolId: 55010
  name: "进度9"
  groupId: 9
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501010
  poolId: 55010
  name: "进度10"
  itemId: 310106
  itemNum: 1
  groupId: 10
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5501011
  poolId: 55010
  name: "空道具"
  groupId: 11
  weight: 1
}
rows {
  rewardId: 5501101
  poolId: 55011
  name: "森之子·鲲宝"
  itemId: 730001
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 5501102
  poolId: 55011
  name: "背饰-魔方"
  itemId: 620169
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5501103
  poolId: 55011
  name: "魔法书"
  itemId: 620232
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5501104
  poolId: 55011
  name: "面饰-斗鸡眼眼镜"
  itemId: 610080
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5501105
  poolId: 55011
  name: "动态头像框-花海灵鲲"
  itemId: 840099
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 5501106
  poolId: 55011
  name: "漫溯花海"
  itemId: 820072
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 5501107
  poolId: 55011
  name: "表情-太牛了"
  itemId: 710128
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5501108
  poolId: 55011
  name: "表情-我可真帅"
  itemId: 710130
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5501109
  poolId: 55011
  name: "表情-真下饭"
  itemId: 710131
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5501110
  poolId: 55011
  name: "动作-翩翩少年"
  itemId: 720134
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5501111
  poolId: 55011
  name: "动作-化学实验"
  itemId: 720129
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5501112
  poolId: 55011
  name: "动作-手指转篮球"
  itemId: 720142
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5501113
  poolId: 55011
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501114
  poolId: 55011
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501115
  poolId: 55011
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501116
  poolId: 55011
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501117
  poolId: 55011
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501118
  poolId: 55011
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501119
  poolId: 55011
  name: "表情-剪刀手"
  itemId: 710178
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501120
  poolId: 55011
  name: "表情-笑拉了家人们"
  itemId: 710132
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501121
  poolId: 55011
  name: "表情-略略略"
  itemId: 710133
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501122
  poolId: 55011
  name: "动作-摇手手"
  itemId: 720121
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501123
  poolId: 55011
  name: "动作-猛男舞"
  itemId: 720123
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501124
  poolId: 55011
  name: "动作-显摆显摆"
  itemId: 720126
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501125
  poolId: 55011
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501126
  poolId: 55011
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501127
  poolId: 55011
  name: "家政服"
  itemId: 510102
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 5501128
  poolId: 55011
  name: "别来沾边"
  itemId: 510128
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501129
  poolId: 55011
  name: "别来沾边"
  itemId: 520084
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501130
  poolId: 55011
  name: "别来沾边"
  itemId: 530062
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501131
  poolId: 55011
  name: "真的栓Q"
  itemId: 510129
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501132
  poolId: 55011
  name: "真的栓Q"
  itemId: 520085
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501133
  poolId: 55011
  name: "真的栓Q"
  itemId: 530063
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501134
  poolId: 55011
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501135
  poolId: 55011
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501136
  poolId: 55011
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5501137
  poolId: 55011
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 3000301
  poolId: 30003
  name: "时空守护神 尤诺亚"
  itemId: 401440
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000302
  poolId: 30003
  name: "超维守护神 尤诺亚"
  itemId: 401450
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000303
  poolId: 30003
  name: "时光漂流瓶"
  itemId: 620183
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000304
  poolId: 30003
  name: "时空道具"
  itemId: 630095
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000305
  poolId: 30003
  name: "流星之眼"
  itemId: 610083
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000306
  poolId: 30003
  name: "记忆修复师 赫曼"
  itemId: 401690
  itemNum: 1
  groupId: 4
  weight: 7
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000307
  poolId: 30003
  name: "报时者 莫罗斯"
  itemId: 401540
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000308
  poolId: 30003
  name: "时光手提箱"
  itemId: 620188
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000309
  poolId: 30003
  name: "星河探测仪"
  itemId: 630107
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000310
  poolId: 30003
  name: "皮包眼镜"
  itemId: 610087
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000311
  poolId: 30003
  name: "头像框"
  itemId: 840057
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000312
  poolId: 30003
  name: "号外号外"
  itemId: 720061
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000313
  poolId: 30003
  name: "让我看看"
  itemId: 720056
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000314
  poolId: 30003
  name: "原地起飞"
  itemId: 720020
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000315
  poolId: 30003
  name: "缘来是你"
  itemId: 710108
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000316
  poolId: 30003
  name: "华丽出场"
  itemId: 710109
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000317
  poolId: 30003
  name: "还有谁"
  itemId: 710110
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000318
  poolId: 30003
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000319
  poolId: 30003
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000320
  poolId: 30003
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000321
  poolId: 30003
  name: "魔小浣"
  itemId: 401560
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000322
  poolId: 30003
  name: "时秒秒"
  itemId: 401680
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000323
  poolId: 30003
  name: "旅星者"
  itemId: 401660
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000324
  poolId: 30003
  name: "伸懒腰"
  itemId: 720128
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000325
  poolId: 30003
  name: "浪漫回旋"
  itemId: 720089
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000326
  poolId: 30003
  name: "扭屁股舞"
  itemId: 720132
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000329
  poolId: 30003
  name: "燥起来"
  itemId: 710111
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000330
  poolId: 30003
  name: "我听不见"
  itemId: 710112
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000331
  poolId: 30003
  name: "抱抱我"
  itemId: 710113
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000332
  poolId: 30003
  name: "快理理我嘛"
  itemId: 710114
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000333
  poolId: 30003
  name: "莹紫葡萄"
  itemId: 510113
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 3000334
  poolId: 30003
  name: "莹紫葡萄"
  itemId: 520073
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 3000335
  poolId: 30003
  name: "莹紫葡萄"
  itemId: 530053
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 3000336
  poolId: 30003
  name: "幸运橙意"
  itemId: 510105
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 3000337
  poolId: 30003
  name: "幸运橙意"
  itemId: 520076
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 3000338
  poolId: 30003
  name: "制胜代码"
  itemId: 510101
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 3000339
  poolId: 30003
  name: "制胜代码"
  itemId: 520075
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
  earningPoints: 1
}
rows {
  rewardId: 3000340
  poolId: 30003
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
  earningPoints: 1
}
rows {
  rewardId: 3000341
  poolId: 30003
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
  earningPoints: 1
}
rows {
  rewardId: 3000342
  poolId: 30003
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
  earningPoints: 1
}
rows {
  rewardId: 3000343
  poolId: 30003
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
  earningPoints: 1
}
rows {
  rewardId: 8002001
  poolId: 8002
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8002002
  poolId: 8002
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8002003
  poolId: 8002
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8002004
  poolId: 8002
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8002005
  poolId: 8002
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8002006
  poolId: 8002
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8002007
  poolId: 8002
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8002008
  poolId: 8002
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8002009
  poolId: 8002
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8002010
  poolId: 8002
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8002011
  poolId: 8002
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 8002012
  poolId: 8002
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 8002013
  poolId: 8002
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8002014
  poolId: 8002
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8002015
  poolId: 8002
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8002016
  poolId: 8002
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8002017
  poolId: 8002
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8002018
  poolId: 8002
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8002019
  poolId: 8002
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8002020
  poolId: 8002
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8002021
  poolId: 8002
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8002022
  poolId: 8002
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8002023
  poolId: 8002
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8002024
  poolId: 8002
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 8002025
  poolId: 8002
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8002026
  poolId: 8002
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8002027
  poolId: 8002
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8002028
  poolId: 8002
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 8002029
  poolId: 8002
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8002030
  poolId: 8002
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8002031
  poolId: 8002
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8002032
  poolId: 8002
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8002033
  poolId: 8002
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8002034
  poolId: 8002
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8002035
  poolId: 8002
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8002036
  poolId: 8002
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8003001
  poolId: 8003
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8003002
  poolId: 8003
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8003003
  poolId: 8003
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 8003004
  poolId: 8003
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8003005
  poolId: 8003
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8003006
  poolId: 8003
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8003007
  poolId: 8003
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8003008
  poolId: 8003
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8003009
  poolId: 8003
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8003010
  poolId: 8003
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8003011
  poolId: 8003
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8003012
  poolId: 8003
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8003013
  poolId: 8003
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 8003014
  poolId: 8003
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 8003015
  poolId: 8003
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8003016
  poolId: 8003
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8003017
  poolId: 8003
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8003018
  poolId: 8003
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8003019
  poolId: 8003
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8003020
  poolId: 8003
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8003021
  poolId: 8003
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8003022
  poolId: 8003
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8003023
  poolId: 8003
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8003024
  poolId: 8003
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8003025
  poolId: 8003
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8003026
  poolId: 8003
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8003027
  poolId: 8003
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8003028
  poolId: 8003
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 8003029
  poolId: 8003
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8003030
  poolId: 8003
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8003031
  poolId: 8003
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8003032
  poolId: 8003
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 8003033
  poolId: 8003
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8003034
  poolId: 8003
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8003035
  poolId: 8003
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8003036
  poolId: 8003
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8003037
  poolId: 8003
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8003038
  poolId: 8003
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8003039
  poolId: 8003
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8003040
  poolId: 8003
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8007000
  poolId: 8007
  name: "梦想收集者"
  itemId: 403250
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8007001
  poolId: 8007
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8007002
  poolId: 8007
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8007003
  poolId: 8007
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 8007004
  poolId: 8007
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8007005
  poolId: 8007
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8007006
  poolId: 8007
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8007007
  poolId: 8007
  name: "小粉猫"
  itemId: 620353
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8007008
  poolId: 8007
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8007009
  poolId: 8007
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8007010
  poolId: 8007
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8007011
  poolId: 8007
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8007012
  poolId: 8007
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8007013
  poolId: 8007
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 8007014
  poolId: 8007
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 8007015
  poolId: 8007
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 8007016
  poolId: 8007
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8007017
  poolId: 8007
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8007018
  poolId: 8007
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 8007019
  poolId: 8007
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8007020
  poolId: 8007
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 8007021
  poolId: 8007
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8007022
  poolId: 8007
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8007023
  poolId: 8007
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8007024
  poolId: 8007
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8007025
  poolId: 8007
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8007026
  poolId: 8007
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8007027
  poolId: 8007
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 8007028
  poolId: 8007
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8007029
  poolId: 8007
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 8007030
  poolId: 8007
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8007031
  poolId: 8007
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8007032
  poolId: 8007
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 8007033
  poolId: 8007
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 8007034
  poolId: 8007
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8007035
  poolId: 8007
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8007036
  poolId: 8007
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8007037
  poolId: 8007
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8007038
  poolId: 8007
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8007039
  poolId: 8007
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 8007040
  poolId: 8007
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 8007041
  poolId: 8007
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 5016001
  poolId: 5016
  name: "小橘子"
  itemId: 401780
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5016002
  poolId: 5016
  name: "心动飞驰镜"
  itemId: 610108
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016003
  poolId: 5016
  name: "神速喇叭"
  itemId: 620225
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016004
  poolId: 5016
  name: "神速之杯"
  itemId: 630125
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016005
  poolId: 5016
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 3
  limit: 1
}
rows {
  rewardId: 5016006
  poolId: 5016
  name: "紫色动作"
  itemId: 720143
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016007
  poolId: 5016
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016008
  poolId: 5016
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016009
  poolId: 5016
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016010
  poolId: 5016
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016011
  poolId: 5016
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5016012
  poolId: 5016
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014001
  poolId: 5014
  name: "暗夜冰羽"
  itemId: 620246
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5014002
  poolId: 5014
  name: "小清新面膜"
  itemId: 610103
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014003
  poolId: 5014
  name: "龙角"
  itemId: 630077
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014004
  poolId: 5014
  name: "动态表情-噗"
  itemId: 710129
  itemNum: 1
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5014005
  poolId: 5014
  name: "紫色动作-乌云密布"
  itemId: 720127
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014006
  poolId: 5014
  name: "表情-期待"
  itemId: 710115
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014007
  poolId: 5014
  name: "涂鸦印记上装"
  itemId: 510121
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014008
  poolId: 5014
  name: "涂鸦印记夏装"
  itemId: 520080
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014009
  poolId: 5014
  name: "涂鸦印记手套"
  itemId: 530058
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014010
  poolId: 5017
  name: "菜狗"
  itemId: 401640
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5014011
  poolId: 5017
  name: "菜狗灯牌"
  itemId: 630100
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014012
  poolId: 5017
  name: "菜狗汪汪舞"
  itemId: 720125
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014013
  poolId: 5017
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014014
  poolId: 5017
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5014015
  poolId: 5017
  name: "动态表情“你好”"
  itemId: 710105
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014016
  poolId: 5017
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5014017
  poolId: 5017
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014018
  poolId: 5017
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014019
  poolId: 5018
  name: "莲藕狐"
  itemId: 401650
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5014020
  poolId: 5018
  name: "莲藕狐头饰"
  itemId: 630121
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014021
  poolId: 5018
  name: "莲藕狐-惬意打滚"
  itemId: 720137
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014022
  poolId: 5018
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014023
  poolId: 5018
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5014024
  poolId: 5018
  name: "动态表情“贴贴”"
  itemId: 710104
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014025
  poolId: 5018
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5014026
  poolId: 5018
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5014027
  poolId: 5018
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 3000401
  poolId: 30004
  name: "彩虹旋律 洁西卡"
  itemId: 401850
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000402
  poolId: 30004
  name: "乐坛巨星  洁西卡"
  itemId: 401860
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000403
  poolId: 30004
  name: "麦克风手杖"
  itemId: 620270
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000404
  poolId: 30004
  name: "音符单片镜"
  itemId: 610127
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000405
  poolId: 30004
  name: "音乐精灵"
  itemId: 630147
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000406
  poolId: 30004
  name: "迷幻电子女"
  itemId: 401870
  itemNum: 1
  groupId: 4
  weight: 7
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000407
  poolId: 30004
  name: "爆裂鼓手男"
  itemId: 401880
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000408
  poolId: 30004
  name: "头饰combo"
  itemId: 630150
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000409
  poolId: 30004
  name: "热火之鼓"
  itemId: 620260
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000410
  poolId: 30004
  name: "面饰干杯"
  itemId: 610121
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000411
  poolId: 30004
  name: "头像框-杰西卡头像框"
  itemId: 840098
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000412
  poolId: 30004
  name: "捕萤火虫"
  itemId: 720185
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000413
  poolId: 30004
  name: "沙锤摇"
  itemId: 720175
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000414
  poolId: 30004
  name: "动次打次"
  itemId: 720170
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000415
  poolId: 30004
  name: "抱头猫猫"
  itemId: 710156
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000416
  poolId: 30004
  name: "阴阳怪气"
  itemId: 710168
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000417
  poolId: 30004
  name: "泪牛满面"
  itemId: 710161
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000418
  poolId: 30004
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000419
  poolId: 30004
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000420
  poolId: 30004
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000421
  poolId: 30004
  name: "乐团小号男"
  itemId: 401890
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000422
  poolId: 30004
  name: "乐团小号女"
  itemId: 401900
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000423
  poolId: 30004
  name: "乐团偶像"
  itemId: 401910
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000424
  poolId: 30004
  name: "单手侧翻"
  itemId: 720182
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000425
  poolId: 30004
  name: "跺脚敬礼"
  itemId: 720157
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000426
  poolId: 30004
  name: "欢迎礼"
  itemId: 720155
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000427
  poolId: 30004
  name: "深情歌唱"
  itemId: 720156
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000428
  poolId: 30004
  name: "嘟嘟舞"
  itemId: 720164
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000429
  poolId: 30004
  name: "你问我"
  itemId: 710170
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000430
  poolId: 30004
  name: "目瞪狗呆"
  itemId: 710169
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000431
  poolId: 30004
  name: "偷听"
  itemId: 710172
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000432
  poolId: 30004
  name: "大可不必"
  itemId: 710171
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000433
  poolId: 30004
  name: "春天日式风女"
  itemId: 510130
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000434
  poolId: 30004
  name: "春天日式风女"
  itemId: 520086
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000435
  poolId: 30004
  name: "春天日式风女"
  itemId: 530064
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000436
  poolId: 30004
  name: "春天牛仔男"
  itemId: 510131
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000437
  poolId: 30004
  name: "春天牛仔男"
  itemId: 520087
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000438
  poolId: 30004
  name: "春天牛仔男"
  itemId: 530065
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000439
  poolId: 30004
  name: "小青蛙毛衣"
  itemId: 510132
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000440
  poolId: 30004
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000441
  poolId: 30004
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000442
  poolId: 30004
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000443
  poolId: 30004
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5019001
  poolId: 5019
  name: "风间"
  itemId: 401510
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5019002
  poolId: 5019
  name: "星际朋友"
  itemId: 630122
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5019003
  poolId: 5019
  name: "风间头像"
  itemId: 860042
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5019004
  poolId: 5019
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5019005
  poolId: 5019
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5019006
  poolId: 5019
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5019007
  poolId: 5019
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5019008
  poolId: 5019
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5019009
  poolId: 5019
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5019010
  poolId: 5019
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5019011
  poolId: 5019
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5019012
  poolId: 5019
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020001
  poolId: 5020
  name: "妮妮"
  itemId: 401520
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5020002
  poolId: 5020
  name: "妮妮的兔子"
  itemId: 620251
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5020003
  poolId: 5020
  name: "妮妮头像"
  itemId: 860043
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5020004
  poolId: 5020
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5020005
  poolId: 5020
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020006
  poolId: 5020
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020007
  poolId: 5020
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020008
  poolId: 5020
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020009
  poolId: 5020
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020010
  poolId: 5020
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020011
  poolId: 5020
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5020012
  poolId: 5020
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021001
  poolId: 5021
  name: "阿呆"
  itemId: 401530
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5021002
  poolId: 5021
  name: "阿呆的金鱼"
  itemId: 630130
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5021003
  poolId: 5021
  name: "阿呆头像"
  itemId: 860041
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5021004
  poolId: 5021
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5021005
  poolId: 5021
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021006
  poolId: 5021
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021007
  poolId: 5021
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021008
  poolId: 5021
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021009
  poolId: 5021
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021010
  poolId: 5021
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021011
  poolId: 5021
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5021012
  poolId: 5021
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5110000
  poolId: 51100
  name: "时装自选礼包"
  itemId: 330037
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5110001
  poolId: 51100
  name: "背饰自选礼盒"
  itemId: 330038
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 5110002
  poolId: 51100
  name: "面饰自选礼盒"
  itemId: 330039
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110003
  poolId: 51100
  name: "小丸子吃刨冰"
  itemId: 720187
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110004
  poolId: 51101
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5110005
  poolId: 51101
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110006
  poolId: 51101
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110007
  poolId: 51101
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110008
  poolId: 51101
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110009
  poolId: 51101
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110010
  poolId: 51101
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110011
  poolId: 51102
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5110012
  poolId: 51102
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110013
  poolId: 51102
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110014
  poolId: 51102
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110015
  poolId: 51102
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110016
  poolId: 51102
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110017
  poolId: 51102
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110018
  poolId: 51103
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5110019
  poolId: 51103
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110020
  poolId: 51103
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110021
  poolId: 51103
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110022
  poolId: 51103
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110023
  poolId: 51103
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110024
  poolId: 51103
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110025
  poolId: 51104
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5110026
  poolId: 51104
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110027
  poolId: 51104
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110028
  poolId: 51104
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110029
  poolId: 51104
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110030
  poolId: 51104
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5110031
  poolId: 51104
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111000
  poolId: 51110
  name: "时装自选礼包"
  itemId: 330037
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5111001
  poolId: 51110
  name: "背饰自选礼盒"
  itemId: 330038
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 5111002
  poolId: 51110
  name: "面饰自选礼盒"
  itemId: 330039
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111003
  poolId: 51110
  name: "花轮摸刘海"
  itemId: 720190
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111004
  poolId: 51111
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5111005
  poolId: 51111
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111006
  poolId: 51111
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111007
  poolId: 51111
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111008
  poolId: 51111
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111009
  poolId: 51111
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111010
  poolId: 51111
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111011
  poolId: 51112
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5111012
  poolId: 51112
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111013
  poolId: 51112
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111014
  poolId: 51112
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111015
  poolId: 51112
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111016
  poolId: 51112
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111017
  poolId: 51112
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111018
  poolId: 51113
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5111019
  poolId: 51113
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111020
  poolId: 51113
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111021
  poolId: 51113
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111022
  poolId: 51113
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111023
  poolId: 51113
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111024
  poolId: 51113
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111025
  poolId: 51114
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5111026
  poolId: 51114
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111027
  poolId: 51114
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111028
  poolId: 51114
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111029
  poolId: 51114
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111030
  poolId: 51114
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5111031
  poolId: 51114
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 540401
  poolId: 5404
  name: "樱桃小丸子"
  itemId: 401980
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540402
  poolId: 5404
  name: "小樱桃"
  itemId: 630160
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540403
  poolId: 5404
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540404
  poolId: 5404
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540405
  poolId: 5404
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540406
  poolId: 5404
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540501
  poolId: 5405
  name: "小玉"
  itemId: 402010
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540502
  poolId: 5405
  name: "鲷鱼烧"
  itemId: 630161
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540503
  poolId: 5405
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540504
  poolId: 5405
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540505
  poolId: 5405
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540506
  poolId: 5405
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 25
  limit: 1
  isGrand: false
}
rows {
  rewardId: 5112001
  poolId: 51120
  name: "柠檬小甜豆"
  itemId: 401950
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112002
  poolId: 51120
  name: "柠檬眼镜"
  itemId: 610137
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 5112003
  poolId: 51120
  name: "我不酸的哦"
  itemId: 710208
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 5112004
  poolId: 51120
  name: "萌檬包包"
  itemId: 620304
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112006
  poolId: 51121
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112007
  poolId: 51121
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112008
  poolId: 51121
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112009
  poolId: 51121
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112010
  poolId: 51121
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112011
  poolId: 51121
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112012
  poolId: 51121
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112013
  poolId: 51122
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112014
  poolId: 51122
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112015
  poolId: 51122
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112016
  poolId: 51122
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112017
  poolId: 51122
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112018
  poolId: 51122
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112019
  poolId: 51122
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112020
  poolId: 51123
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112021
  poolId: 51123
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112022
  poolId: 51123
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112023
  poolId: 51123
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112024
  poolId: 51123
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112025
  poolId: 51123
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112026
  poolId: 51123
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112027
  poolId: 51124
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112028
  poolId: 51124
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112029
  poolId: 51124
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112030
  poolId: 51124
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112031
  poolId: 51124
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112032
  poolId: 51124
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112033
  poolId: 51124
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112034
  poolId: 51130
  name: "鸭鸭小甜豆"
  itemId: 401960
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112035
  poolId: 51130
  name: "咕咕镜"
  itemId: 610138
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 5112036
  poolId: 51130
  name: "悲伤那么大"
  itemId: 710209
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 5112037
  poolId: 51130
  name: "透明伞"
  itemId: 620305
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112038
  poolId: 51131
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112039
  poolId: 51131
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112040
  poolId: 51131
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112041
  poolId: 51131
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112042
  poolId: 51131
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112043
  poolId: 51131
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112044
  poolId: 51131
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112045
  poolId: 51132
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112046
  poolId: 51132
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112047
  poolId: 51132
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112048
  poolId: 51132
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112049
  poolId: 51132
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112050
  poolId: 51132
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112051
  poolId: 51132
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112052
  poolId: 51133
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112053
  poolId: 51133
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112054
  poolId: 51133
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112055
  poolId: 51133
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112056
  poolId: 51133
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112057
  poolId: 51133
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112058
  poolId: 51133
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112059
  poolId: 51134
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112060
  poolId: 51134
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112061
  poolId: 51134
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5112062
  poolId: 51134
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112063
  poolId: 51134
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112064
  poolId: 51134
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5112065
  poolId: 51134
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5113033
  poolId: 5303
  name: "时装自选礼盒*1"
  itemId: 330041
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 2
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 5113034
  poolId: 5303
  name: "永恒之翼"
  itemId: 620298
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113035
  poolId: 5303
  name: "永恒之冠"
  itemId: 630169
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113036
  poolId: 5303
  name: "梦幻铃铛"
  itemId: 630166
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113037
  poolId: 5303
  name: "犬系少年 阿柴"
  itemId: 402160
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113038
  poolId: 5303
  name: "猫系少女 喵喵"
  itemId: 402170
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113039
  poolId: 5303
  name: "幸福花束"
  itemId: 620309
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113040
  poolId: 5303
  name: "时尚魔头镜"
  itemId: 610139
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113041
  poolId: 5303
  name: "星语晶恋"
  itemId: 630174
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 2
  afterRefreshLimit: 2
}
rows {
  rewardId: 5113042
  poolId: 5303
  name: "纯白誓约"
  itemId: 721013
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 2
  afterRefreshLimit: 2
}
rows {
  rewardId: 5113043
  poolId: 5303
  name: "绮莉莉"
  itemId: 402040
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5113044
  poolId: 5303
  name: "心梦券*20"
  itemId: 211
  itemNum: 20
  groupId: 2
  weight: 12
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 5113045
  poolId: 5303
  name: "心梦券*5"
  itemId: 211
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 5113046
  poolId: 5303
  name: "心梦券*3"
  itemId: 211
  itemNum: 3
  groupId: 3
  weight: 10
}
rows {
  rewardId: 5113047
  poolId: 5303
  name: "心梦券*2"
  itemId: 211
  itemNum: 2
  groupId: 3
  weight: 20
}
rows {
  rewardId: 5113048
  poolId: 5303
  name: "心梦券*1"
  itemId: 211
  itemNum: 1
  groupId: 3
  weight: 32
}
rows {
  rewardId: 5113049
  poolId: 5303
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 9
}
rows {
  rewardId: 5113050
  poolId: 5303
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 9
}
rows {
  rewardId: 5113051
  poolId: 5303
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 5113052
  poolId: 5303
  name: "动态头像框"
  itemId: 840106
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5113053
  poolId: 5303
  name: "动态昵称框"
  itemId: 820075
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5023001
  poolId: 5023
  name: "星梦使者"
  itemId: 620301
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5023002
  poolId: 5023
  name: "神威墨镜"
  itemId: 610142
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5023003
  poolId: 5023
  name: "白金飞鸟"
  itemId: 630177
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5023004
  poolId: 5023
  name: "求帮帮"
  itemId: 710181
  itemNum: 1
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5023005
  poolId: 5023
  name: "舞动青春"
  itemId: 720178
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5023006
  poolId: 5023
  name: "满分"
  itemId: 710189
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5023007
  poolId: 5023
  name: "元气之星上装"
  itemId: 510146
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5023008
  poolId: 5023
  name: "元气之星套装"
  itemId: 520099
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5023009
  poolId: 5023
  name: "元气之星手套"
  itemId: 530075
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027001
  poolId: 5027
  name: "铁臂阿童木"
  itemId: 400730
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5027002
  poolId: 5027
  name: "蒸汽波太阳镜"
  itemId: 610042
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027003
  poolId: 5027
  name: "挥手告别"
  itemId: 710056
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027004
  poolId: 5027
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027005
  poolId: 5027
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027006
  poolId: 5027
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027007
  poolId: 5027
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027008
  poolId: 5027
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5027009
  poolId: 5027
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028001
  poolId: 5028
  name: "小兰"
  itemId: 400720
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5028002
  poolId: 5028
  name: "阿童木背包"
  itemId: 620076
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028003
  poolId: 5028
  name: "小兰摊手"
  itemId: 710200
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028004
  poolId: 5028
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028005
  poolId: 5028
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028006
  poolId: 5028
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028007
  poolId: 5028
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028008
  poolId: 5028
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5028009
  poolId: 5028
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520301
  poolId: 52030
  name: "古风少女"
  itemId: 402150
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 520302
  poolId: 52030
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 520303
  poolId: 52030
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520304
  poolId: 52030
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520305
  poolId: 52030
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520306
  poolId: 52030
  name: "粉色回忆"
  itemId: 630057
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 520307
  poolId: 52030
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 520308
  poolId: 52030
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 520309
  poolId: 52030
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 520310
  poolId: 52030
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 520311
  poolId: 52030
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 520312
  poolId: 52030
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 520313
  poolId: 52031
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 8004001
  poolId: 8004
  name: "臻藏代币*20"
  itemId: 203
  itemNum: 20
  groupId: 1
  weight: 1
}
rows {
  rewardId: 8004002
  poolId: 8004
  name: "臻藏代币*5"
  itemId: 203
  itemNum: 5
  groupId: 1
  weight: 1
}
rows {
  rewardId: 8004003
  poolId: 8004
  name: "臻藏代币*3"
  itemId: 203
  itemNum: 3
  groupId: 1
  weight: 1
}
rows {
  rewardId: 8004004
  poolId: 8004
  name: "臻藏代币*2"
  itemId: 203
  itemNum: 2
  groupId: 1
  weight: 1
}
rows {
  rewardId: 8004005
  poolId: 8004
  name: "臻藏代币*1"
  itemId: 203
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 5025001
  poolId: 5025
  name: "双面天鹅 娜塔莉"
  itemId: 402200
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 5025002
  poolId: 5025
  name: "天鹅羽扇"
  itemId: 620315
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5025003
  poolId: 5025
  name: "天鹅之冠"
  itemId: 630180
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5025004
  poolId: 5025
  name: "动作-旋转谢幕"
  itemId: 720181
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5025005
  poolId: 5025
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 1
  limit: 6
  ignoreRefresh: true
}
rows {
  rewardId: 5025006
  poolId: 5025
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 10
  ignoreRefresh: true
}
rows {
  rewardId: 5025007
  poolId: 5025
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 10
  ignoreRefresh: true
}
rows {
  rewardId: 5025008
  poolId: 5025
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 10
  ignoreRefresh: true
}
rows {
  rewardId: 5025009
  poolId: 5025
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 10
  ignoreRefresh: true
}
rows {
  rewardId: 5025010
  poolId: 5025
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 10
  ignoreRefresh: true
}
rows {
  rewardId: 5026001
  poolId: 5026
  name: "枪火新星 闪电"
  itemId: 401700
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5026002
  poolId: 5026
  name: "特战先锋"
  itemId: 620255
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5026003
  poolId: 5026
  name: "打瞌睡"
  itemId: 720172
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5026004
  poolId: 5026
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5026005
  poolId: 5026
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5026006
  poolId: 5026
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5026007
  poolId: 5026
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5026008
  poolId: 5026
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5026009
  poolId: 5026
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 7000019
  poolId: 7001
  name: "（绿）星小递上衣*1"
  itemId: 510019
  itemNum: 1
  groupId: 1
  weight: 5
  limit: 1
  isGrand: true
}
rows {
  rewardId: 7000020
  poolId: 7001
  name: "（绿）星小递下衣*1"
  itemId: 520016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  isGrand: true
}
rows {
  rewardId: 7000021
  poolId: 7001
  name: "（绿）星小递手套*1"
  itemId: 530010
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
}
rows {
  rewardId: 7000022
  poolId: 7001
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 3
  weight: 10
  limit: 4
  isGrand: true
}
rows {
  rewardId: 7000023
  poolId: 7001
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
  isGrand: true
}
rows {
  rewardId: 7000024
  poolId: 7001
  name: "印章*100"
  itemId: 4
  itemNum: 100
  groupId: 3
  weight: 50
  limit: 16
}
rows {
  rewardId: 7000025
  poolId: 7001
  name: "表情-亲亲"
  itemId: 710021
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
}
rows {
  rewardId: 7000026
  poolId: 7001
  name: "甜蜜初心*1"
  itemId: 200014
  itemNum: 1
  groupId: 3
  weight: 50
  limit: 4
}
rows {
  rewardId: 7000027
  poolId: 7001
  name: "心心糖果*1"
  itemId: 200015
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 5
}
rows {
  rewardId: 5029001
  poolId: 5029
  name: "小肥柴"
  itemId: 401630
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5029002
  poolId: 5029
  name: "豆柴眼罩"
  itemId: 610079
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5029003
  poolId: 5029
  name: "彩虹瀑布"
  itemId: 720651
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5029004
  poolId: 5029
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5029005
  poolId: 5029
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5029006
  poolId: 5029
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5029007
  poolId: 5029
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5029008
  poolId: 5029
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5029009
  poolId: 5029
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 3000501
  poolId: 30005
  name: "美人鱼"
  itemId: 402370
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000502
  poolId: 30005
  name: "海王"
  itemId: 402380
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000503
  poolId: 30005
  name: "流光鱼尾"
  itemId: 610162
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000504
  poolId: 30005
  name: "明月琉璃冠"
  itemId: 630204
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000505
  poolId: 30005
  name: "三叉戟"
  itemId: 640006
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000506
  poolId: 30005
  name: "虎鲸男孩"
  itemId: 402270
  itemNum: 1
  groupId: 4
  weight: 7
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000507
  poolId: 30005
  name: "珍珠少女"
  itemId: 402280
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000508
  poolId: 30005
  name: "浮力鲨鱼"
  itemId: 620326
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000509
  poolId: 30005
  name: "珊瑚瑰宝"
  itemId: 630213
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000510
  poolId: 30005
  name: "彩贝眼镜"
  itemId: 610168
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000511
  poolId: 30005
  name: "头像框-美人鱼头像框"
  itemId: 840115
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000512
  poolId: 30005
  name: "扔纸飞机"
  itemId: 720186
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000513
  poolId: 30005
  name: "跃动之星"
  itemId: 720177
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000514
  poolId: 30005
  name: "派对走起"
  itemId: 720136
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000515
  poolId: 30005
  name: "潜水"
  itemId: 710237
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000516
  poolId: 30005
  name: "水枪喷水"
  itemId: 710219
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000517
  poolId: 30005
  name: "鸭鸭吹风扇"
  itemId: 710239
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000518
  poolId: 30005
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000519
  poolId: 30005
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000520
  poolId: 30005
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000521
  poolId: 30005
  name: "海小浪"
  itemId: 402300
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000522
  poolId: 30005
  name: "梦游鱼"
  itemId: 402310
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000523
  poolId: 30005
  name: "舞乐乐"
  itemId: 402320
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000524
  poolId: 30005
  name: "秀肌肉"
  itemId: 720176
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000525
  poolId: 30005
  name: "摇摇舞"
  itemId: 720174
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000526
  poolId: 30005
  name: "金鸡独立"
  itemId: 720167
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000527
  poolId: 30005
  name: "可爱喵"
  itemId: 720171
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000528
  poolId: 30005
  name: "伸懒腰"
  itemId: 720154
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000529
  poolId: 30005
  name: "可爱也是种麻烦"
  itemId: 710236
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000530
  poolId: 30005
  name: "划水"
  itemId: 710238
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000531
  poolId: 30005
  name: "舔舔"
  itemId: 710212
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000532
  poolId: 30005
  name: "拽"
  itemId: 710226
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000533
  poolId: 30005
  name: "青春领航"
  itemId: 510156
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000534
  poolId: 30005
  name: "青春领航"
  itemId: 520107
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000535
  poolId: 30005
  name: "青春领航"
  itemId: 530084
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000536
  poolId: 30005
  name: "奇喵物语"
  itemId: 510168
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000537
  poolId: 30005
  name: "奇喵物语"
  itemId: 520117
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000538
  poolId: 30005
  name: "奇喵物语"
  itemId: 530094
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000539
  poolId: 30005
  name: "海岛一刻"
  itemId: 510157
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000540
  poolId: 30005
  name: "海岛一刻"
  itemId: 520108
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000541
  poolId: 30005
  name: "海岛一刻"
  itemId: 530085
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000542
  poolId: 30005
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000543
  poolId: 30005
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000544
  poolId: 30005
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000545
  poolId: 30005
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5700101
  poolId: 57001
  name: "粉嘟嘟兔礼盒"
  itemId: 620044
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 15
}
rows {
  rewardId: 5700102
  poolId: 57001
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 2
  weight: 1
  limit: 200
}
rows {
  rewardId: 5700103
  poolId: 57001
  name: "亲嘴鱼眼镜"
  itemId: 610118
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 40
}
rows {
  rewardId: 5700104
  poolId: 57001
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 150
}
rows {
  rewardId: 5700105
  poolId: 57001
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 5
  weight: 1
  limit: 30
}
rows {
  rewardId: 5700106
  poolId: 57001
  name: "荧光星秀"
  itemId: 400410
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 30
}
rows {
  rewardId: 5700107
  poolId: 57001
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 80
}
rows {
  rewardId: 5700108
  poolId: 57001
  name: "星宝印章*50"
  itemId: 4
  itemNum: 50
  groupId: 8
  weight: 1
  limit: 1000
}
rows {
  rewardId: 5030001
  poolId: 5030
  name: "白鹤少年"
  itemId: 402190
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5030002
  poolId: 5030
  name: "被拍照"
  itemId: 720632
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5030003
  poolId: 5030
  name: "食人花"
  itemId: 630210
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5030004
  poolId: 5030
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5030005
  poolId: 5030
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030006
  poolId: 5030
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030007
  poolId: 5030
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030008
  poolId: 5030
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030009
  poolId: 5030
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030010
  poolId: 5030
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030011
  poolId: 5030
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030012
  poolId: 5030
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5030013
  poolId: 30006
  name: "灭战神 阿多斯"
  itemId: 402480
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  lvCost: 50
  inGroupLimit {
    period: 7
    limit: 1
  }
}
rows {
  rewardId: 5030014
  poolId: 30006
  name: "焰战神  波尔托斯"
  itemId: 402460
  itemNum: 1
  groupId: 1
  weight: 120
  isGrand: true
  lvCost: 50
  inGroupLimit {
    period: 7
    limit: 2
  }
}
rows {
  rewardId: 5030015
  poolId: 30006
  name: "耀战神 达达尼亚"
  itemId: 402470
  itemNum: 1
  groupId: 1
  weight: 120
  isGrand: true
  lvCost: 50
  inGroupLimit {
    period: 7
    limit: 2
  }
}
rows {
  rewardId: 5030016
  poolId: 30006
  name: "璀璨之星"
  itemId: 213
  itemNum: 1
  groupId: 1
  weight: 4000
  lvCost: 50
  inGroupLimit {
    period: 7
    limit: 6
  }
}
rows {
  rewardId: 5030017
  poolId: 30006
  name: "都市猎人 明彦"
  itemId: 400370
  itemNum: 1
  groupId: 2
  weight: 7
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030018
  poolId: 30006
  name: "燃起来了"
  itemId: 720617
  itemNum: 1
  groupId: 2
  weight: 10
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030019
  poolId: 30006
  name: "剑来"
  itemId: 720622
  itemNum: 1
  groupId: 2
  weight: 10
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030020
  poolId: 30006
  name: "异星接触"
  itemId: 720625
  itemNum: 1
  groupId: 2
  weight: 10
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030021
  poolId: 30006
  name: "动态头像框-辉耀之光"
  itemId: 840127
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030022
  poolId: 30006
  name: "动态头像框-烈焰之心"
  itemId: 840128
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030023
  poolId: 30006
  name: "动态头像-焰战神"
  itemId: 860052
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030024
  poolId: 30006
  name: "动态头像-耀战神"
  itemId: 860053
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030025
  poolId: 30006
  name: "清柠柠"
  itemId: 402530
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 5030026
  poolId: 30006
  name: "蔚蓝动力"
  itemId: 510164
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 5030027
  poolId: 30006
  name: "异域织梦"
  itemId: 510173
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 5030028
  poolId: 30006
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 5030029
  poolId: 30006
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 5030030
  poolId: 30006
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 3
  weight: 5
}
rows {
  rewardId: 5030031
  poolId: 30006
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 8
}
rows {
  rewardId: 5030032
  poolId: 30006
  name: "心心糖果*1"
  itemId: 200015
  itemNum: 1
  groupId: 3
  weight: 10
}
rows {
  rewardId: 5030033
  poolId: 30006
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 20
}
rows {
  rewardId: 5031001
  poolId: 5031
  name: "布朗熊"
  itemId: 402400
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5031002
  poolId: 5031
  name: "救生圈莎莉鸡"
  itemId: 620318
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5031003
  poolId: 5031
  name: "害羞甜心"
  itemId: 720610
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5031004
  poolId: 5031
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5031005
  poolId: 5031
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5031006
  poolId: 5031
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5031007
  poolId: 5031
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5031008
  poolId: 5031
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5031009
  poolId: 5031
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032001
  poolId: 5032
  name: "可妮兔"
  itemId: 402410
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5032002
  poolId: 5032
  name: "可妮蛋糕"
  itemId: 620319
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032003
  poolId: 5032
  name: "动若脱兔"
  itemId: 720609
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032004
  poolId: 5032
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032005
  poolId: 5032
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032006
  poolId: 5032
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032007
  poolId: 5032
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032008
  poolId: 5032
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5032009
  poolId: 5032
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033001
  poolId: 5033
  name: "天鹅之心"
  itemId: 630195
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5033002
  poolId: 5033
  name: "樱桃眼镜"
  itemId: 610172
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033003
  poolId: 5033
  name: "油漆滚筒"
  itemId: 620344
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033004
  poolId: 5033
  name: "汪汪问号"
  itemId: 710203
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033005
  poolId: 5033
  name: "旺柴笑"
  itemId: 710080
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033006
  poolId: 5033
  name: "直拳快打"
  itemId: 720641
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033007
  poolId: 5033
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033008
  poolId: 5033
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5033009
  poolId: 5033
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034001
  poolId: 5034
  name: "星梭-巡星者"
  itemId: 740002
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5034002
  poolId: 5034
  name: "飞扇"
  itemId: 720653
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034003
  poolId: 5034
  name: "小睡片刻"
  itemId: 830076
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034004
  poolId: 5034
  name: "草莓抹茶上衣"
  itemId: 510145
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034005
  poolId: 5034
  name: "草莓抹茶下装"
  itemId: 520098
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034006
  poolId: 5034
  name: "草莓抹茶手套"
  itemId: 530074
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034007
  poolId: 5034
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034008
  poolId: 5034
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5034009
  poolId: 5034
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5035001
  poolId: 5035
  name: "守护圣翼"
  itemId: 620356
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5035002
  poolId: 5035
  name: "旗开得胜"
  itemId: 620323
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5035003
  poolId: 5035
  name: "火啦"
  itemId: 630198
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5035004
  poolId: 5035
  name: "狐狸脚印"
  itemId: 650002
  itemNum: 1
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5035005
  poolId: 5035
  name: "爆气"
  itemId: 720618
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5035006
  poolId: 5035
  name: "优雅安抚"
  itemId: 720645
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5035007
  poolId: 5035
  name: "蒸汽工程上装"
  itemId: 510172
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5035008
  poolId: 5035
  name: "蒸汽工程下装"
  itemId: 520120
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5035009
  poolId: 5035
  name: "蒸汽工程手套"
  itemId: 530097
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5037001
  poolId: 5037
  name: "糖豆骑士"
  itemId: 402490
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5037002
  poolId: 5037
  name: "救生圈莎莉鸡"
  itemId: 620318
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5037003
  poolId: 5037
  name: "害羞甜心"
  itemId: 720610
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5037004
  poolId: 5037
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5037005
  poolId: 5037
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5037006
  poolId: 5037
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5037007
  poolId: 5037
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5037008
  poolId: 5037
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5037009
  poolId: 5037
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5037010
  poolId: 5037
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5037011
  poolId: 5037
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5037012
  poolId: 5037
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038001
  poolId: 5038
  name: "薯宝"
  itemId: 402500
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5038002
  poolId: 5038
  name: "哉佩利敖光线"
  itemId: 720084
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5038003
  poolId: 5038
  name: "神光棒"
  itemId: 620103
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5038004
  poolId: 5038
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5038005
  poolId: 5038
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038006
  poolId: 5038
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038007
  poolId: 5038
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038008
  poolId: 5038
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038009
  poolId: 5038
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038010
  poolId: 5038
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038011
  poolId: 5038
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5038012
  poolId: 5038
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5502001
  poolId: 55020
  name: "进度1"
  groupId: 1
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502002
  poolId: 55020
  name: "进度2"
  groupId: 2
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502003
  poolId: 55020
  name: "进度3"
  groupId: 3
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502004
  poolId: 55020
  name: "进度4"
  itemId: 310107
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502005
  poolId: 55020
  name: "进度5"
  groupId: 5
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502006
  poolId: 55020
  name: "进度6"
  groupId: 6
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502007
  poolId: 55020
  name: "进度7"
  itemId: 310108
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502008
  poolId: 55020
  name: "进度8"
  groupId: 8
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502009
  poolId: 55020
  name: "进度9"
  groupId: 9
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502010
  poolId: 55020
  name: "进度10"
  itemId: 310109
  itemNum: 1
  groupId: 10
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5502011
  poolId: 55020
  name: "空道具"
  groupId: 11
  weight: 1
}
rows {
  rewardId: 5502101
  poolId: 55021
  name: "绮梦幻影"
  itemId: 730004
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 5502102
  poolId: 55021
  name: "背饰-天使背包"
  itemId: 620306
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5502103
  poolId: 55021
  name: "头饰-贪食花"
  itemId: 630210
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5502104
  poolId: 55021
  name: "面饰-冰淇淋眼镜"
  itemId: 610149
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5502105
  poolId: 55021
  name: "动态头像框-心动启程"
  itemId: 840138
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 5502106
  poolId: 55021
  name: "浪漫之约"
  itemId: 820093
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 5502107
  poolId: 55021
  name: "表情-我要奋斗"
  itemId: 710256
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5502108
  poolId: 55021
  name: "表情-小红狐吃瓜"
  itemId: 710262
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5502109
  poolId: 55021
  name: "表情-面部保养"
  itemId: 710264
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5502110
  poolId: 55021
  name: "动作-指点江山"
  itemId: 720631
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5502111
  poolId: 55021
  name: "动作-公正二连"
  itemId: 720633
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5502112
  poolId: 55021
  name: "动作-冰淇淋掉了"
  itemId: 720640
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5502113
  poolId: 55021
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502114
  poolId: 55021
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502115
  poolId: 55021
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502116
  poolId: 55021
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502117
  poolId: 55021
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502118
  poolId: 55021
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502119
  poolId: 55021
  name: "表情-动动脑子"
  itemId: 710240
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502120
  poolId: 55021
  name: "表情-听不清"
  itemId: 710214
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502121
  poolId: 55021
  name: "表情-心无杂念"
  itemId: 710216
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502122
  poolId: 55021
  name: "动作-弱小无助"
  itemId: 720621
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502123
  poolId: 55021
  name: "动作-起床失败"
  itemId: 720643
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502124
  poolId: 55021
  name: "动作-揣手"
  itemId: 720642
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502125
  poolId: 55021
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502126
  poolId: 55021
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502127
  poolId: 55021
  name: "多云转晴上装"
  itemId: 510163
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 5502128
  poolId: 55021
  name: "多云转晴下装"
  itemId: 520113
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 5502129
  poolId: 55021
  name: "多云转晴手套"
  itemId: 530090
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 5502130
  poolId: 55021
  name: "春城粉桃上装"
  itemId: 510167
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502131
  poolId: 55021
  name: "春城粉桃下装"
  itemId: 520116
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502132
  poolId: 55021
  name: "春城粉桃手套"
  itemId: 530093
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502133
  poolId: 55021
  name: "海盐兔兔上装"
  itemId: 510158
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502134
  poolId: 55021
  name: "海盐兔兔下装"
  itemId: 520109
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502135
  poolId: 55021
  name: "海盐兔兔手套"
  itemId: 530086
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502136
  poolId: 55021
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502137
  poolId: 55021
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502138
  poolId: 55021
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5502139
  poolId: 55021
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5039001
  poolId: 5039
  name: "toby"
  itemId: 401000
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5039002
  poolId: 5039
  name: "桃气满满"
  itemId: 620100
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5039003
  poolId: 5039
  name: "踢踏舞"
  itemId: 720063
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5039004
  poolId: 5039
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5039005
  poolId: 5039
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5039006
  poolId: 5039
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5039007
  poolId: 5039
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5039008
  poolId: 5039
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5039009
  poolId: 5039
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5115033
  poolId: 51155
  name: "时装自选礼盒"
  itemId: 330051
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115034
  poolId: 51155
  name: "饰品自选礼盒"
  itemId: 330052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5115035
  poolId: 51155
  name: "动作自选礼盒"
  itemId: 330053
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 5115036
  poolId: 51155
  name: "头像自选礼盒"
  itemId: 330054
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 5115037
  poolId: 51155
  name: "星愿币"
  itemId: 2
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 5
}
rows {
  rewardId: 5115038
  poolId: 51155
  name: "时装染色膏"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 8
}
rows {
  rewardId: 5115039
  poolId: 51155
  name: "饰品调色盘"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 8
}
rows {
  rewardId: 5115040
  poolId: 51155
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 3
  limit: 5
}
rows {
  rewardId: 5115041
  poolId: 51155
  name: "心心蜜罐"
  itemId: 200017
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 5
}
rows {
  rewardId: 5115042
  poolId: 51156
  name: "时装自选礼盒"
  itemId: 330051
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115043
  poolId: 51156
  name: "饰品自选礼盒"
  itemId: 330052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5115044
  poolId: 51156
  name: "动作自选礼盒"
  itemId: 330053
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 5115045
  poolId: 51156
  name: "头像自选礼盒"
  itemId: 330054
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 5115046
  poolId: 51156
  name: "星愿币"
  itemId: 2
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 5
}
rows {
  rewardId: 5115047
  poolId: 51156
  name: "时装染色膏"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 8
}
rows {
  rewardId: 5115048
  poolId: 51156
  name: "饰品调色盘"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 8
}
rows {
  rewardId: 5115049
  poolId: 51156
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 3
  limit: 5
}
rows {
  rewardId: 5115050
  poolId: 51156
  name: "心心蜜罐"
  itemId: 200017
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 5
}
rows {
  rewardId: 5115051
  poolId: 51157
  name: "时装自选礼盒"
  itemId: 330051
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115052
  poolId: 51157
  name: "饰品自选礼盒"
  itemId: 330052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5115053
  poolId: 51157
  name: "动作自选礼盒"
  itemId: 330053
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 5115054
  poolId: 51157
  name: "头像自选礼盒"
  itemId: 330054
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 5115055
  poolId: 51157
  name: "星愿币"
  itemId: 2
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 5
}
rows {
  rewardId: 5115056
  poolId: 51157
  name: "时装染色膏"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 8
}
rows {
  rewardId: 5115057
  poolId: 51157
  name: "饰品调色盘"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 8
}
rows {
  rewardId: 5115058
  poolId: 51157
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 3
  limit: 5
}
rows {
  rewardId: 5115059
  poolId: 51157
  name: "心心蜜罐"
  itemId: 200017
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 5
}
rows {
  rewardId: 520401
  poolId: 52040
  name: "古风少女"
  itemId: 402860
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 520402
  poolId: 52040
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 520403
  poolId: 52040
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520404
  poolId: 52040
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520405
  poolId: 52040
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520406
  poolId: 52040
  name: "荷包背饰"
  itemId: 620345
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 520407
  poolId: 52040
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 520408
  poolId: 52040
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 520409
  poolId: 52040
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 520410
  poolId: 52040
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 520411
  poolId: 52040
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 520412
  poolId: 52040
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 520413
  poolId: 52041
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 5800201
  poolId: 57002
  name: "湮灭之锁"
  itemId: 640052
  itemNum: 1
  expireDays: 7
  groupId: 1
  weight: 1
  limit: 15
}
rows {
  rewardId: 5800202
  poolId: 57002
  name: "阳光柠檬"
  itemId: 620226
  itemNum: 1
  expireDays: 1
  groupId: 2
  weight: 1
  limit: 40
}
rows {
  rewardId: 5800203
  poolId: 57002
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 1
  limit: 200
}
rows {
  rewardId: 5800204
  poolId: 57002
  name: "沙滩卡车"
  itemId: 620338
  itemNum: 1
  expireDays: 1
  groupId: 4
  weight: 1
  limit: 150
}
rows {
  rewardId: 5800205
  poolId: 57002
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 5
  weight: 1
  limit: 30
  isGrand: true
  lvCost: 20
}
rows {
  rewardId: 5800206
  poolId: 57002
  name: "绿植花洒"
  itemId: 640002
  itemNum: 1
  expireDays: 3
  groupId: 6
  weight: 1
  limit: 30
}
rows {
  rewardId: 5800207
  poolId: 57002
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 80
  isGrand: true
  lvCost: 20
}
rows {
  rewardId: 5800208
  poolId: 57002
  name: "星宝印章*50"
  itemId: 4
  itemNum: 50
  groupId: 8
  weight: 1
  limit: 1000
}
rows {
  rewardId: 5114001
  poolId: 51140
  name: "HelloKitty"
  itemId: 402420
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5114002
  poolId: 51140
  name: "红蝶结包包"
  itemId: 620376
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 5114003
  poolId: 51140
  name: "萌萌护眼"
  itemId: 610180
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 5114004
  poolId: 51140
  name: "粉粉仙女棒"
  itemId: 640022
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114005
  poolId: 51141
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5114006
  poolId: 51141
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114007
  poolId: 51141
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114008
  poolId: 51141
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114009
  poolId: 51141
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114010
  poolId: 51141
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114011
  poolId: 51141
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114012
  poolId: 51142
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5114013
  poolId: 51142
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114014
  poolId: 51142
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114015
  poolId: 51142
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114016
  poolId: 51142
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114017
  poolId: 51142
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114018
  poolId: 51142
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114019
  poolId: 51143
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5114020
  poolId: 51143
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114021
  poolId: 51143
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114022
  poolId: 51143
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114023
  poolId: 51143
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114024
  poolId: 51143
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114025
  poolId: 51143
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114026
  poolId: 51144
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5114027
  poolId: 51144
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114028
  poolId: 51144
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114029
  poolId: 51144
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114030
  poolId: 51144
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114031
  poolId: 51144
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5114032
  poolId: 51144
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115001
  poolId: 51150
  name: "酷洛米"
  itemId: 402430
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115002
  poolId: 51150
  name: "甜酷包包"
  itemId: 620372
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 5115003
  poolId: 51150
  name: "幻彩魅梦"
  itemId: 630244
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 5115004
  poolId: 51150
  name: "幻想星愿"
  itemId: 640017
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115005
  poolId: 51151
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115006
  poolId: 51151
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115007
  poolId: 51151
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115008
  poolId: 51151
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115009
  poolId: 51151
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115010
  poolId: 51151
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115011
  poolId: 51151
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115012
  poolId: 51152
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115013
  poolId: 51152
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115014
  poolId: 51152
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115015
  poolId: 51152
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115016
  poolId: 51152
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115017
  poolId: 51152
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115018
  poolId: 51152
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115019
  poolId: 51153
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115020
  poolId: 51153
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115021
  poolId: 51153
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115022
  poolId: 51153
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115023
  poolId: 51153
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115024
  poolId: 51153
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115025
  poolId: 51153
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115026
  poolId: 51154
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115027
  poolId: 51154
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115028
  poolId: 51154
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115029
  poolId: 51154
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115030
  poolId: 51154
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115031
  poolId: 51154
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5115032
  poolId: 51154
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5036001
  poolId: 5036
  name: "奶龙"
  itemId: 401010
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5036002
  poolId: 5036
  name: "动作-奶龙舞鱼"
  itemId: 720098
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5036003
  poolId: 5036
  name: "配饰-奶龙背包"
  itemId: 620147
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5036004
  poolId: 5036
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5036005
  poolId: 5036
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5036006
  poolId: 5036
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5036007
  poolId: 5036
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5036008
  poolId: 5036
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5036009
  poolId: 5036
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5116000
  poolId: 51160
  name: "神龙大侠 阿宝"
  itemId: 401130
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5116001
  poolId: 51160
  name: "萌包出笼"
  itemId: 630098
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5116002
  poolId: 51160
  name: "智慧之杖"
  itemId: 620186
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5116003
  poolId: 51160
  name: "功夫巨星"
  itemId: 720146
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5116004
  poolId: 51160
  name: "阿宝吃惊"
  itemId: 710126
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5116005
  poolId: 51160
  name: "阿宝头像"
  itemId: 860020
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5116006
  poolId: 51161
  name: "幸运钥匙"
  groupId: 2
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5116007
  poolId: 51161
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116008
  poolId: 51161
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116009
  poolId: 51161
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116010
  poolId: 51161
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116011
  poolId: 51161
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116012
  poolId: 51161
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116013
  poolId: 51162
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5116014
  poolId: 51162
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116015
  poolId: 51162
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116016
  poolId: 51162
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116017
  poolId: 51162
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116018
  poolId: 51162
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116019
  poolId: 51162
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116020
  poolId: 51163
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5116021
  poolId: 51163
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116022
  poolId: 51163
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116023
  poolId: 51163
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116024
  poolId: 51163
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116025
  poolId: 51163
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116026
  poolId: 51163
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116027
  poolId: 51164
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5116028
  poolId: 51164
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116029
  poolId: 51164
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116030
  poolId: 51164
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116031
  poolId: 51164
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116032
  poolId: 51164
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116033
  poolId: 51164
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116034
  poolId: 51165
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5116035
  poolId: 51165
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116036
  poolId: 51165
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116037
  poolId: 51165
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116038
  poolId: 51165
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116039
  poolId: 51165
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116040
  poolId: 51165
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116041
  poolId: 51166
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5116042
  poolId: 51166
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116043
  poolId: 51166
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116044
  poolId: 51166
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116045
  poolId: 51166
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116046
  poolId: 51166
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5116047
  poolId: 51166
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117000
  poolId: 51170
  name: "师傅"
  itemId: 401740
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5117001
  poolId: 51170
  name: "功夫竹笛"
  itemId: 620187
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5117002
  poolId: 51170
  name: "宗师眼镜"
  itemId: 610086
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5117003
  poolId: 51170
  name: "神龙摆尾"
  itemId: 720110
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5117004
  poolId: 51170
  name: "师傅沉默"
  itemId: 710124
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5117005
  poolId: 51170
  name: "师傅头像"
  itemId: 860021
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5117006
  poolId: 51171
  name: "幸运钥匙"
  groupId: 2
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5117007
  poolId: 51171
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117008
  poolId: 51171
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117009
  poolId: 51171
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117010
  poolId: 51171
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117011
  poolId: 51171
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117012
  poolId: 51171
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117013
  poolId: 51172
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5117014
  poolId: 51172
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117015
  poolId: 51172
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117016
  poolId: 51172
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117017
  poolId: 51172
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117018
  poolId: 51172
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117019
  poolId: 51172
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117020
  poolId: 51173
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5117021
  poolId: 51173
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117022
  poolId: 51173
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117023
  poolId: 51173
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117024
  poolId: 51173
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117025
  poolId: 51173
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117026
  poolId: 51173
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117027
  poolId: 51174
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5117028
  poolId: 51174
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117029
  poolId: 51174
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117030
  poolId: 51174
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117031
  poolId: 51174
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117032
  poolId: 51174
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117033
  poolId: 51174
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117034
  poolId: 51175
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5117035
  poolId: 51175
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117036
  poolId: 51175
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117037
  poolId: 51175
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117038
  poolId: 51175
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117039
  poolId: 51175
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117040
  poolId: 51175
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117041
  poolId: 51176
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5117042
  poolId: 51176
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117043
  poolId: 51176
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117044
  poolId: 51176
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117045
  poolId: 51176
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117046
  poolId: 51176
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5117047
  poolId: 51176
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 511801
  poolId: 51181
  name: "小真"
  itemId: 401610
  itemNum: 1
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540601
  poolId: 5406
  name: "暴暴龙"
  itemId: 401360
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540602
  poolId: 5406
  name: "暴暴龙头像"
  itemId: 860011
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540603
  poolId: 5406
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540604
  poolId: 5406
  name: "万能棉花40"
  itemId: 6
  itemNum: 40
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540605
  poolId: 5406
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540606
  poolId: 5406
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 3000701
  poolId: 30007
  name: "甜心公主 萝茜"
  itemId: 402830
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000702
  poolId: 30007
  name: "甜梦公主 莱拉"
  itemId: 402840
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000703
  poolId: 30007
  name: "星愿摩天轮"
  itemId: 620384
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000704
  poolId: 30007
  name: "梦幻天驹"
  itemId: 630270
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000705
  poolId: 30007
  name: "甜心公主杖"
  itemId: 640028
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000706
  poolId: 30007
  name: "魔术兔 拉比娜"
  itemId: 402900
  itemNum: 1
  groupId: 4
  weight: 7
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000707
  poolId: 30007
  name: "悠悠云 克劳德"
  itemId: 402850
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000708
  poolId: 30007
  name: "胶卷眼镜"
  itemId: 610158
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000709
  poolId: 30007
  name: "浪漫气球"
  itemId: 620387
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000710
  poolId: 30007
  name: "汪汪棉花糖"
  itemId: 640025
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000711
  poolId: 30007
  name: "头像框-心愿摩天轮"
  itemId: 840146
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000712
  poolId: 30007
  name: "华丽登场"
  itemId: 720698
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000713
  poolId: 30007
  name: "奇幻巡礼"
  itemId: 720685
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000714
  poolId: 30007
  name: "抓钱舞"
  itemId: 720658
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000715
  poolId: 30007
  name: "缤纷气球"
  itemId: 710251
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000716
  poolId: 30007
  name: "冲呀"
  itemId: 710257
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000717
  poolId: 30007
  name: "旋转木马"
  itemId: 710254
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000718
  poolId: 30007
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000719
  poolId: 30007
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000720
  poolId: 30007
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000721
  poolId: 30007
  name: "游乐乐"
  itemId: 402680
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000722
  poolId: 30007
  name: "游美美"
  itemId: 402690
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000723
  poolId: 30007
  name: "壶小小"
  itemId: 402780
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000724
  poolId: 30007
  name: "蝶步舞"
  itemId: 720682
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000725
  poolId: 30007
  name: "开心到飞起"
  itemId: 720700
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000726
  poolId: 30007
  name: "真的是你呀"
  itemId: 720703
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000727
  poolId: 30007
  name: "机械舞"
  itemId: 720648
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000728
  poolId: 30007
  name: "星潮萌动"
  itemId: 720704
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000729
  poolId: 30007
  name: "太劲爆了"
  itemId: 710258
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000730
  poolId: 30007
  name: "睡着了"
  itemId: 710269
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000731
  poolId: 30007
  name: "展示魅力"
  itemId: 710263
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000732
  poolId: 30007
  name: "打呵欠"
  itemId: 710270
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000733
  poolId: 30007
  name: "快乐出游"
  itemId: 510179
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000734
  poolId: 30007
  name: "快乐出游"
  itemId: 520122
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000735
  poolId: 30007
  name: "快乐出游"
  itemId: 530099
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000736
  poolId: 30007
  name: "悠扬青春"
  itemId: 510180
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000737
  poolId: 30007
  name: "悠扬青春"
  itemId: 520123
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000738
  poolId: 30007
  name: "悠扬青春"
  itemId: 530100
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000739
  poolId: 30007
  name: "波普艺术"
  itemId: 510178
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000740
  poolId: 30007
  name: "波普艺术"
  itemId: 520144
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000741
  poolId: 30007
  name: "波普艺术"
  itemId: 530121
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000742
  poolId: 30007
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000743
  poolId: 30007
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000744
  poolId: 30007
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000745
  poolId: 30007
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5115060
  poolId: 51158
  name: "时装自选礼盒"
  itemId: 330057
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115061
  poolId: 51158
  name: "饰品自选礼盒"
  itemId: 330058
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5115062
  poolId: 51158
  name: "动作自选礼盒"
  itemId: 330059
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 5115063
  poolId: 51158
  name: "头像自选礼盒"
  itemId: 330060
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 5115064
  poolId: 51158
  name: "星愿币"
  itemId: 2
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 7
}
rows {
  rewardId: 5115065
  poolId: 51158
  name: "时装染色膏"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 12
}
rows {
  rewardId: 5115066
  poolId: 51158
  name: "饰品调色盘"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 12
}
rows {
  rewardId: 5115068
  poolId: 51158
  name: "心心蜜罐"
  itemId: 200017
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 10
}
rows {
  rewardId: 5115069
  poolId: 51159
  name: "时装自选礼盒"
  itemId: 330057
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5115070
  poolId: 51159
  name: "饰品自选礼盒"
  itemId: 330058
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5115071
  poolId: 51159
  name: "动作自选礼盒"
  itemId: 330059
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 5115072
  poolId: 51159
  name: "头像自选礼盒"
  itemId: 330060
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 5115073
  poolId: 51159
  name: "星愿币"
  itemId: 2
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 7
}
rows {
  rewardId: 5115074
  poolId: 51159
  name: "时装染色膏"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 12
}
rows {
  rewardId: 5115075
  poolId: 51159
  name: "饰品调色盘"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 12
}
rows {
  rewardId: 5115077
  poolId: 51159
  name: "心心蜜罐"
  itemId: 200017
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 10
}
rows {
  rewardId: 5305001
  poolId: 5305
  name: "小幺"
  itemId: 401500
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5305002
  poolId: 5305
  name: "沧玹"
  itemId: 401480
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
}
rows {
  rewardId: 5305003
  poolId: 5305
  name: "涂山璟"
  itemId: 401490
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
}
rows {
  rewardId: 5305004
  poolId: 5305
  name: "赤水丰隆"
  itemId: 401470
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
}
rows {
  rewardId: 5305005
  poolId: 5305
  name: "相柳"
  itemId: 401460
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
}
rows {
  rewardId: 5305006
  poolId: 5305
  name: "配饰自选礼盒"
  itemId: 330063
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 2
}
rows {
  rewardId: 5305007
  poolId: 5305
  name: "语音自选礼包"
  itemId: 330066
  itemNum: 1
  groupId: 3
  weight: 36
  limit: 2
}
rows {
  rewardId: 5305008
  poolId: 5305
  name: "表情自选礼盒"
  itemId: 330064
  itemNum: 1
  groupId: 3
  weight: 36
  limit: 2
}
rows {
  rewardId: 5305009
  poolId: 5305
  name: "相思券*20"
  itemId: 216
  itemNum: 20
  groupId: 3
  weight: 28
  limit: 3
}
rows {
  rewardId: 5305010
  poolId: 5305
  name: "相思券*5"
  itemId: 216
  itemNum: 5
  groupId: 4
  weight: 25
}
rows {
  rewardId: 5305011
  poolId: 5305
  name: "相思券*3"
  itemId: 216
  itemNum: 3
  groupId: 4
  weight: 25
}
rows {
  rewardId: 5305012
  poolId: 5305
  name: "相思券*2"
  itemId: 216
  itemNum: 2
  groupId: 4
  weight: 15
}
rows {
  rewardId: 5305013
  poolId: 5305
  name: "相思券*1"
  itemId: 216
  itemNum: 1
  groupId: 4
  weight: 15
}
rows {
  rewardId: 5305014
  poolId: 5305
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 4
  weight: 10
}
rows {
  rewardId: 5305015
  poolId: 5305
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 10
}
rows {
  rewardId: 5305016
  poolId: 5305
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 10
}
rows {
  rewardId: 5049001
  poolId: 5049
  name: "配饰-小黄鸭背包"
  itemId: 620408
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5049002
  poolId: 5049
  name: "星宝印章"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5049003
  poolId: 5049
  name: "服装染色剂"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5049004
  poolId: 5049
  name: "甜兔屋"
  itemId: 218103
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5049005
  poolId: 5049
  name: "配饰-农场币"
  itemId: 630280
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5049006
  poolId: 5049
  name: "动作-爱意传递"
  itemId: 720719
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5049007
  poolId: 5049
  name: "稻草人汉克"
  itemId: 218102
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5049008
  poolId: 5049
  name: "云朵币"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5049009
  poolId: 5049
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043001
  poolId: 5043
  name: "TOBY"
  itemId: 402940
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5043002
  poolId: 5043
  name: "花花鸭篮"
  itemId: 620377
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043003
  poolId: 5043
  name: "陌生人给糖"
  itemId: 720720
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043004
  poolId: 5043
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043005
  poolId: 5043
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043006
  poolId: 5043
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043007
  poolId: 5043
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043008
  poolId: 5043
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5043009
  poolId: 5043
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044001
  poolId: 5044
  name: "小青瓜"
  itemId: 402950
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5044002
  poolId: 5044
  name: "彩虹背包"
  itemId: 620292
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044003
  poolId: 5044
  name: "你和我"
  itemId: 720724
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044004
  poolId: 5044
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044005
  poolId: 5044
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044006
  poolId: 5044
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044007
  poolId: 5044
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044008
  poolId: 5044
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5044009
  poolId: 5044
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5051001
  poolId: 5051
  name: "toby"
  itemId: 401000
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5051002
  poolId: 5051
  name: "桃气满满"
  itemId: 620100
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5051003
  poolId: 5051
  name: "踢踏舞"
  itemId: 720063
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5051004
  poolId: 5051
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5051005
  poolId: 5051
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5051006
  poolId: 5051
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5051007
  poolId: 5051
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5051008
  poolId: 5051
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5051009
  poolId: 5051
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 8005001
  poolId: 8005
  name: "幸运星*10"
  itemId: 219
  itemNum: 10
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 8005002
  poolId: 8005
  name: "幸运星*5"
  itemId: 219
  itemNum: 5
  groupId: 2
  weight: 1
}
rows {
  rewardId: 8005003
  poolId: 8005
  name: "幸运星*3"
  itemId: 219
  itemNum: 3
  groupId: 3
  weight: 1
}
rows {
  rewardId: 8005004
  poolId: 8005
  name: "幸运星*2"
  itemId: 219
  itemNum: 2
  groupId: 4
  weight: 1
}
rows {
  rewardId: 8005005
  poolId: 8005
  name: "幸运星*1"
  itemId: 219
  itemNum: 1
  groupId: 5
  weight: 1
}
rows {
  rewardId: 5051010
  poolId: 5306
  name: "时装自选礼盒*1"
  itemId: 330068
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 2
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 5051011
  poolId: 5306
  name: "焰羽瑶琴"
  itemId: 620415
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051012
  poolId: 5306
  name: "火凤羽冠"
  itemId: 630281
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051013
  poolId: 5306
  name: "凤羽之舞"
  itemId: 610204
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051014
  poolId: 5306
  name: "彩莲提灯"
  itemId: 640018
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051015
  poolId: 5306
  name: "倾绝之舞 莎希莉"
  itemId: 403070
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051016
  poolId: 5306
  name: "星佑之音 巴兰"
  itemId: 403080
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051017
  poolId: 5306
  name: "祥瑞喜鹊"
  itemId: 630284
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 2
  afterRefreshLimit: 2
}
rows {
  rewardId: 5051018
  poolId: 5306
  name: "卷不离身"
  itemId: 620418
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051019
  poolId: 5306
  name: "火力全开"
  itemId: 610201
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051020
  poolId: 5306
  name: "举高高"
  itemId: 721006
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051021
  poolId: 5306
  name: "缘芊芊"
  itemId: 403130
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 5051022
  poolId: 5306
  name: "心梦券*20"
  itemId: 211
  itemNum: 20
  groupId: 2
  weight: 12
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 5051023
  poolId: 5306
  name: "心梦券*5"
  itemId: 211
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 5051024
  poolId: 5306
  name: "心梦券*3"
  itemId: 211
  itemNum: 3
  groupId: 3
  weight: 10
}
rows {
  rewardId: 5051025
  poolId: 5306
  name: "心梦券*2"
  itemId: 211
  itemNum: 2
  groupId: 3
  weight: 20
}
rows {
  rewardId: 5051026
  poolId: 5306
  name: "心梦券*1"
  itemId: 211
  itemNum: 1
  groupId: 3
  weight: 32
}
rows {
  rewardId: 5051027
  poolId: 5306
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 12
}
rows {
  rewardId: 5051028
  poolId: 5306
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 12
}
rows {
  rewardId: 5051029
  poolId: 5306
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 5051030
  poolId: 5306
  name: "动态头像框"
  itemId: 840157
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5051031
  poolId: 5306
  name: "动态昵称框"
  itemId: 820107
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 5051032
  poolId: 5306
  name: "静态称号"
  itemId: 850462
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 20000001
  poolId: 7002
  name: "星愿币*12"
  itemId: 2
  itemNum: 12
  groupId: 1
  weight: 3
  limit: 1
}
rows {
  rewardId: 20000002
  poolId: 7002
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 1
  weight: 8
  limit: 1
}
rows {
  rewardId: 20000003
  poolId: 7002
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 100
  limit: 1
}
rows {
  rewardId: 20000004
  poolId: 7002
  name: "福利碎片*10"
  itemId: 3134
  itemNum: 10
  groupId: 1
  weight: 10000
  limit: 2
}
rows {
  rewardId: 20000005
  poolId: 7002
  name: "福利碎片*5"
  itemId: 3134
  itemNum: 5
  groupId: 1
  weight: 10000
  limit: 2
}
rows {
  rewardId: 20000006
  poolId: 7002
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 1
  weight: 209990
  limit: 10
}
rows {
  rewardId: 20000007
  poolId: 7002
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 1
  weight: 379800
  limit: 10
}
rows {
  rewardId: 20000008
  poolId: 7002
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 1
  weight: 390099
  limit: 10
}
rows {
  rewardId: 5304001
  poolId: 5304
  name: "凤舞千秋"
  itemId: 640031
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  lvCost: 35
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 5304002
  poolId: 5304
  name: "仙境灵翼"
  itemId: 620402
  itemNum: 1
  groupId: 1
  weight: 99
  isGrand: true
  lvCost: 35
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5304003
  poolId: 5304
  name: "冰泽面具"
  itemId: 610165
  itemNum: 1
  groupId: 1
  weight: 5000
  isGrand: true
  lvCost: 35
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 8
    limit: 3
  }
}
rows {
  rewardId: 5304004
  poolId: 5304
  name: "生命之环"
  itemId: 630222
  itemNum: 1
  groupId: 1
  weight: 4900
  isGrand: true
  lvCost: 35
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5304005
  poolId: 5304
  name: "水晶蝴蝶"
  itemId: 630248
  itemNum: 1
  groupId: 2
  weight: 1
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5304006
  poolId: 5304
  name: "蝶翼单照"
  itemId: 610177
  itemNum: 1
  groupId: 2
  weight: 1
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5304007
  poolId: 5304
  name: "赶考神器"
  itemId: 620421
  itemNum: 1
  groupId: 2
  weight: 1
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5304008
  poolId: 5304
  name: "爱邮邮"
  itemId: 402700
  itemNum: 1
  groupId: 2
  weight: 3
  extraItemIds: 320065
  extraItemNums: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5304009
  poolId: 5304
  name: "凤之钥*20"
  itemId: 217
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 3
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304010
  poolId: 5304
  name: "凤之钥*10"
  itemId: 217
  itemNum: 10
  groupId: 3
  weight: 10
  limit: 5
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304011
  poolId: 5304
  name: "凤之钥*5"
  itemId: 217
  itemNum: 5
  groupId: 3
  weight: 40
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304012
  poolId: 5304
  name: "凤之钥*2"
  itemId: 217
  itemNum: 2
  groupId: 3
  weight: 150
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304013
  poolId: 5304
  name: "凤之钥*1"
  itemId: 217
  itemNum: 1
  groupId: 4
  weight: 100
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304014
  poolId: 5304
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 30
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304015
  poolId: 5304
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 30
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304016
  poolId: 5304
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304017
  poolId: 5304
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 50
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304018
  poolId: 5304
  name: "气质暖狼上装"
  itemId: 510207
  itemNum: 1
  groupId: 4
  weight: 30
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304019
  poolId: 5304
  name: "气质暖狼下装"
  itemId: 520146
  itemNum: 1
  groupId: 4
  weight: 30
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304020
  poolId: 5304
  name: "气质暖狼手套"
  itemId: 530123
  itemNum: 1
  groupId: 4
  weight: 30
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 5304021
  poolId: 5304
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 50
  extraItemIds: 320065
  extraItemNums: 1
}
rows {
  rewardId: 6000001
  poolId: 6000
  name: "龙胆 赵云"
  itemId: 402990
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 6000002
  poolId: 6000
  name: "追逃游戏 安琪拉"
  itemId: 403010
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 6000003
  poolId: 6000
  name: "龙胆亮银枪"
  itemId: 620427
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 6000004
  poolId: 6000
  name: "月光精灵"
  itemId: 630295
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupChooseEnhance {
    magnifyRate: 2
    firstGuarantee: 2
  }
}
rows {
  rewardId: 6000005
  poolId: 6000
  name: "龙胆现世"
  itemId: 720733
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 6000006
  poolId: 6000
  name: "追逃游戏"
  itemId: 720735
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 6000007
  poolId: 6000
  name: "龙魂再起"
  itemId: 711062
  itemNum: 1
  groupId: 4
  weight: 2
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 6000008
  poolId: 6000
  name: "古灵精怪"
  itemId: 711061
  itemNum: 1
  groupId: 4
  weight: 2
  inGroupLimit {
    period: 2
    limit: 1
  }
}
rows {
  rewardId: 6000009
  poolId: 6000
  name: "峡谷券*5"
  itemId: 218
  itemNum: 5
  groupId: 5
  weight: 20
  comboItemIds: 320063
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 6000010
  poolId: 6000
  name: "峡谷券*3"
  itemId: 218
  itemNum: 3
  groupId: 5
  weight: 80
  comboItemIds: 320062
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 6000011
  poolId: 6000
  name: "峡谷券*2"
  itemId: 218
  itemNum: 2
  groupId: 5
  weight: 100
  comboItemIds: 320061
  comboItemNums: 1
  comboType: RCT_AfterGrand
  comboItemWeights: 1
}
rows {
  rewardId: 6000012
  poolId: 6000
  name: "峡谷币*10"
  itemId: 3541
  itemNum: 10
  groupId: 6
  weight: 1
  limit: 20
}
rows {
  rewardId: 6000013
  poolId: 6000
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 6000014
  poolId: 6000
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 6000015
  poolId: 6000
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 6
  weight: 1
}
rows {
  rewardId: 6000016
  poolId: 6000
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
}
rows {
  rewardId: 5053001
  poolId: 5053
  name: "舞台剧动画*1"
  itemId: 240002
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5053002
  poolId: 5053
  name: "鹊桥*5"
  itemId: 240407
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5053003
  poolId: 5053
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5053004
  poolId: 5053
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5053005
  poolId: 5053
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5053006
  poolId: 5053
  name: "天使昵称框*1"
  itemId: 820506
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5053007
  poolId: 5053
  name: "送花*10"
  itemId: 240404
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5053008
  poolId: 5053
  name: "天使头像*1"
  itemId: 860506
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5053009
  poolId: 5053
  name: "鸡蛋*10"
  itemId: 240401
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054001
  poolId: 5054
  name: "变青蛙动画*1"
  itemId: 240205
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5054002
  poolId: 5054
  name: "身份卡*5"
  itemId: 200102
  itemNum: 5
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054003
  poolId: 5054
  name: "阵营卡*5"
  itemId: 200101
  itemNum: 5
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054004
  poolId: 5054
  name: "狼人币*100"
  itemId: 13
  itemNum: 100
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054005
  poolId: 5054
  name: "天使头像框*1"
  itemId: 840506
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054006
  poolId: 5054
  name: "会议表情-无奈"
  itemId: 240607
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054007
  poolId: 5054
  name: "戒指*5"
  itemId: 240408
  itemNum: 5
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054008
  poolId: 5054
  name: "拳头*10"
  itemId: 240402
  itemNum: 10
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5054009
  poolId: 5054
  name: "爱心*10"
  itemId: 240403
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520501
  poolId: 52050
  name: "情绪测量师  欧柯塔"
  itemId: 403330
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 520502
  poolId: 52050
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 520503
  poolId: 52050
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520504
  poolId: 52050
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520505
  poolId: 52050
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520506
  poolId: 52050
  name: "远古灵杖"
  itemId: 620455
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 520507
  poolId: 52050
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 520508
  poolId: 52050
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 520509
  poolId: 52050
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 520510
  poolId: 52050
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 520511
  poolId: 52050
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 520512
  poolId: 52050
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 520513
  poolId: 52051
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 5000010
  poolId: 5055
  name: "橙色翅膀"
  itemId: 620041
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5000011
  poolId: 5055
  name: "竹蜻蜓"
  itemId: 630012
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5000012
  poolId: 5055
  name: "不许看眼罩"
  itemId: 610008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5000013
  poolId: 5055
  name: "暗中观察"
  itemId: 710049
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5000014
  poolId: 5055
  name: "派对狂欢"
  itemId: 720060
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5000015
  poolId: 5055
  name: "呱呱舞"
  itemId: 720075
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5000016
  poolId: 5055
  name: "一发入魂上装"
  itemId: 510062
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5000017
  poolId: 5055
  name: "一发入魂下装"
  itemId: 520046
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5000018
  poolId: 5055
  name: "一发入魂手套"
  itemId: 530025
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 530038
  poolId: 5307
  name: "月光女神"
  itemId: 400630
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 530039
  poolId: 5307
  name: "月光女神面纱"
  itemId: 610044
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530040
  poolId: 5307
  name: "月光权杖"
  itemId: 620088
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530041
  poolId: 5307
  name: "悬浮星球"
  itemId: 630044
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530042
  poolId: 5307
  name: "四叶草精灵"
  itemId: 401060
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530043
  poolId: 5307
  name: "魔法师哈奇"
  itemId: 401040
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530044
  poolId: 5307
  name: "复古单片镜"
  itemId: 610027
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530045
  poolId: 5307
  name: "蜜罐儿"
  itemId: 620104
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530046
  poolId: 5307
  name: "橄榄花环"
  itemId: 630025
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530047
  poolId: 5307
  name: "炫彩独角兽"
  itemId: 401200
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 530048
  poolId: 5307
  name: "蝶舞幽梦"
  itemId: 840053
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 530049
  poolId: 5307
  name: "星芒券*20"
  itemId: 203
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 530050
  poolId: 5307
  name: "星芒券*5"
  itemId: 203
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 530051
  poolId: 5307
  name: "星芒券*3"
  itemId: 203
  itemNum: 3
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530052
  poolId: 5307
  name: "星芒券*2"
  itemId: 203
  itemNum: 2
  groupId: 3
  weight: 25
}
rows {
  rewardId: 530053
  poolId: 5307
  name: "星芒券*1"
  itemId: 203
  itemNum: 1
  groupId: 3
  weight: 30
}
rows {
  rewardId: 530054
  poolId: 5307
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530055
  poolId: 5307
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530056
  poolId: 5307
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530057
  poolId: 5307
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 5
}
rows {
  rewardId: 530058
  poolId: 5307
  name: "棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530059
  poolId: 5307
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 4
}
rows {
  rewardId: 530060
  poolId: 5307
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 3
  weight: 2
}
rows {
  rewardId: 80080001
  poolId: 8008
  name: "糖果女巫"
  itemId: 404060
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80080002
  poolId: 8008
  name: "梦想收集者"
  itemId: 403250
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80080003
  poolId: 8008
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80080004
  poolId: 8008
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80080005
  poolId: 8008
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80080006
  poolId: 8008
  name: "南瓜糖盒"
  itemId: 620553
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80080007
  poolId: 8008
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80080008
  poolId: 8008
  name: "小粉猫"
  itemId: 620353
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80080009
  poolId: 8008
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80080010
  poolId: 8008
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80080011
  poolId: 8008
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80080012
  poolId: 8008
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80080013
  poolId: 8008
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80080014
  poolId: 8008
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80080015
  poolId: 8008
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80080016
  poolId: 8008
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80080017
  poolId: 8008
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 80080018
  poolId: 8008
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 80080019
  poolId: 8008
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80080020
  poolId: 8008
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80080021
  poolId: 8008
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80080022
  poolId: 8008
  name: "抓小星星"
  itemId: 720842
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80080023
  poolId: 8008
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80080024
  poolId: 8008
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80080025
  poolId: 8008
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80080026
  poolId: 8008
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80080027
  poolId: 8008
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80080028
  poolId: 8008
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80080029
  poolId: 8008
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80080030
  poolId: 8008
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80080031
  poolId: 8008
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80080032
  poolId: 8008
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80080033
  poolId: 8008
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 80080034
  poolId: 8008
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80080035
  poolId: 8008
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80080036
  poolId: 8008
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80080037
  poolId: 8008
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 80080038
  poolId: 8008
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80080039
  poolId: 8008
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80080040
  poolId: 8008
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80080041
  poolId: 8008
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80080042
  poolId: 8008
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80080043
  poolId: 8008
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80080044
  poolId: 8008
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80080045
  poolId: 8008
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 520601
  poolId: 52060
  name: "星光乐师"
  itemId: 404030
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 520602
  poolId: 52060
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 520603
  poolId: 52060
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520604
  poolId: 52060
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520605
  poolId: 52060
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520606
  poolId: 52060
  name: "音动星河"
  itemId: 640037
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 520607
  poolId: 52060
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 520608
  poolId: 52060
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 520609
  poolId: 52060
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 520610
  poolId: 52060
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 520611
  poolId: 52060
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 520612
  poolId: 52060
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 520613
  poolId: 52061
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 540701
  poolId: 5407
  name: "蜡笔小新"
  itemId: 400690
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 540702
  poolId: 5407
  name: "蜡笔小新头像"
  itemId: 860002
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540703
  poolId: 5407
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540704
  poolId: 5407
  name: "万能棉花20"
  itemId: 6
  itemNum: 20
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540705
  poolId: 5407
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 540706
  poolId: 5407
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 2
  weight: 20
  limit: 1
  isGrand: false
}
rows {
  rewardId: 5041001
  poolId: 5041
  name: "云鹤仙"
  itemId: 402190
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5041002
  poolId: 5041
  name: "一怒之下"
  itemId: 720764
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5041003
  poolId: 5041
  name: "幻灵墨笔"
  itemId: 640039
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5041004
  poolId: 5041
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5041005
  poolId: 5041
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5041006
  poolId: 5041
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5041007
  poolId: 5041
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5041008
  poolId: 5041
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5041009
  poolId: 5041
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5041010
  poolId: 5041
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5041011
  poolId: 5041
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5041012
  poolId: 5041
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5056001
  poolId: 5056
  name: "电竞之星 依可"
  itemId: 404910
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5056002
  poolId: 5056
  name: "巨星奖杯"
  itemId: 620424
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5056003
  poolId: 5056
  name: "花样滑板"
  itemId: 720656
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5056004
  poolId: 5056
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5056005
  poolId: 5056
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5056006
  poolId: 5056
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5056007
  poolId: 5056
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5056008
  poolId: 5056
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5056009
  poolId: 5056
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520701
  poolId: 52070
  name: "园艺精灵"
  itemId: 404050
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 520702
  poolId: 52070
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 520703
  poolId: 52070
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520704
  poolId: 52070
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520705
  poolId: 52070
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520706
  poolId: 52070
  name: "活力二哈"
  itemId: 620709
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 520707
  poolId: 52070
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 520708
  poolId: 52070
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 520709
  poolId: 52070
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 520710
  poolId: 52070
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 520711
  poolId: 52070
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 520712
  poolId: 52070
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 520713
  poolId: 52071
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 5057001
  poolId: 5057
  name: "夜莺之声 艾娜"
  itemId: 404600
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5057002
  poolId: 5057
  name: "缺口苹果"
  itemId: 620424
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5057003
  poolId: 5057
  name: "猫咪恰恰舞"
  itemId: 720956
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5057004
  poolId: 5057
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5057005
  poolId: 5057
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5057006
  poolId: 5057
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5057007
  poolId: 5057
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5057008
  poolId: 5057
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5057009
  poolId: 5057
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5700301
  poolId: 57003
  name: "蜡笔小新"
  itemId: 400690
  itemNum: 1
  expireDays: 3
  groupId: 1
  weight: 1
  limit: 150
}
rows {
  rewardId: 5700302
  poolId: 57003
  name: "星光乐师"
  itemId: 404030
  itemNum: 1
  expireDays: 3
  groupId: 2
  weight: 1
  limit: 150
}
rows {
  rewardId: 5700303
  poolId: 57003
  name: "音动星河"
  itemId: 640037
  itemNum: 1
  expireDays: 3
  groupId: 3
  weight: 1
  limit: 150
}
rows {
  rewardId: 5700304
  poolId: 57003
  name: "幻灵墨笔"
  itemId: 640039
  itemNum: 1
  expireDays: 3
  groupId: 4
  weight: 1
  limit: 150
}
rows {
  rewardId: 5700305
  poolId: 57003
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 5
  weight: 1
  limit: 30
  isGrand: true
  lvCost: 20
}
rows {
  rewardId: 5700306
  poolId: 57003
  name: "星宝印章*50"
  itemId: 4
  itemNum: 50
  groupId: 6
  weight: 1
  limit: 150
}
rows {
  rewardId: 5700307
  poolId: 57003
  name: "星愿币*1"
  itemId: 2
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 80
  isGrand: true
  lvCost: 20
}
rows {
  rewardId: 5700308
  poolId: 57003
  name: "星宝印章*50"
  itemId: 4
  itemNum: 50
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 80090001
  poolId: 8009
  name: "荆棘鸟 菲奥娜"
  itemId: 404940
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80090002
  poolId: 8009
  name: "糖果女巫"
  itemId: 404060
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80090003
  poolId: 8009
  name: "梦想收集者"
  itemId: 403250
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80090004
  poolId: 8009
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80090005
  poolId: 8009
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80090006
  poolId: 8009
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80090007
  poolId: 8009
  name: "社恐小蛛"
  itemId: 630464
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80090008
  poolId: 8009
  name: "南瓜糖盒"
  itemId: 620553
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80090009
  poolId: 8009
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80090010
  poolId: 8009
  name: "小粉猫"
  itemId: 620353
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80090011
  poolId: 8009
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80090012
  poolId: 8009
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80090013
  poolId: 8009
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80090014
  poolId: 8009
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80090015
  poolId: 8009
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80090016
  poolId: 8009
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80090017
  poolId: 8009
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80090018
  poolId: 8009
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80090019
  poolId: 8009
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 80090020
  poolId: 8009
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 80090021
  poolId: 8009
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80090022
  poolId: 8009
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80090023
  poolId: 8009
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80090024
  poolId: 8009
  name: "抓小星星"
  itemId: 720842
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80090025
  poolId: 8009
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80090026
  poolId: 8009
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80090027
  poolId: 8009
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80090028
  poolId: 8009
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80090029
  poolId: 8009
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80090030
  poolId: 8009
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80090031
  poolId: 8009
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80090032
  poolId: 8009
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80090033
  poolId: 8009
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80090034
  poolId: 8009
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80090035
  poolId: 8009
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 80090036
  poolId: 8009
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80090037
  poolId: 8009
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80090038
  poolId: 8009
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80090039
  poolId: 8009
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 80090040
  poolId: 8009
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80090041
  poolId: 8009
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80090042
  poolId: 8009
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80090043
  poolId: 8009
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80090044
  poolId: 8009
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80090045
  poolId: 8009
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80090046
  poolId: 8009
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80090047
  poolId: 8009
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 520901
  poolId: 52090
  name: "情绪测量师  欧柯塔"
  itemId: 403330
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 520902
  poolId: 52090
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 520903
  poolId: 52090
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520904
  poolId: 52090
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520905
  poolId: 52090
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520906
  poolId: 52090
  name: "远古灵杖"
  itemId: 620455
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 520907
  poolId: 52090
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 520908
  poolId: 52090
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 520909
  poolId: 52090
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 520910
  poolId: 52090
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 520911
  poolId: 52090
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 520912
  poolId: 52090
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 520913
  poolId: 52091
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 80100001
  poolId: 8010
  name: "执念清除师 离火"
  itemId: 410340
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 80100002
  poolId: 8010
  name: "荆棘鸟 菲奥娜"
  itemId: 404940
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80100003
  poolId: 8010
  name: "糖果女巫"
  itemId: 404060
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80100004
  poolId: 8010
  name: "梦想收集者"
  itemId: 403250
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80100005
  poolId: 8010
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80100006
  poolId: 8010
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80100007
  poolId: 8010
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80100008
  poolId: 8010
  name: "红尘铜障"
  itemId: 610344
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 80100009
  poolId: 8010
  name: "裁梦小剪"
  itemId: 620702
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100010
  poolId: 8010
  name: "社恐小蛛"
  itemId: 630464
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100011
  poolId: 8010
  name: "南瓜糖盒"
  itemId: 620553
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100012
  poolId: 8010
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100013
  poolId: 8010
  name: "小粉猫"
  itemId: 620353
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100014
  poolId: 8010
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100015
  poolId: 8010
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100016
  poolId: 8010
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80100017
  poolId: 8010
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80100018
  poolId: 8010
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80100019
  poolId: 8010
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80100020
  poolId: 8010
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80100021
  poolId: 8010
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80100022
  poolId: 8010
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 80100023
  poolId: 8010
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 80100024
  poolId: 8010
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80100025
  poolId: 8010
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80100026
  poolId: 8010
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80100027
  poolId: 8010
  name: "抓小星星"
  itemId: 720842
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80100028
  poolId: 8010
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80100029
  poolId: 8010
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80100030
  poolId: 8010
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80100031
  poolId: 8010
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80100032
  poolId: 8010
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80100033
  poolId: 8010
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80100034
  poolId: 8010
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80100035
  poolId: 8010
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80100036
  poolId: 8010
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80100037
  poolId: 8010
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80100038
  poolId: 8010
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 80100039
  poolId: 8010
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80100040
  poolId: 8010
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80100041
  poolId: 8010
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80100042
  poolId: 8010
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 80100043
  poolId: 8010
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80100044
  poolId: 8010
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80100045
  poolId: 8010
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80100046
  poolId: 8010
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80100047
  poolId: 8010
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80100048
  poolId: 8010
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80100049
  poolId: 8010
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80100050
  poolId: 8010
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 5201001
  poolId: 52100
  name: "祈雨师 墨瑜"
  itemId: 410870
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5201002
  poolId: 52100
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201003
  poolId: 52100
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201004
  poolId: 52100
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201005
  poolId: 52100
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201006
  poolId: 52100
  name: "很想你"
  itemId: 630467
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201007
  poolId: 52100
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201008
  poolId: 52100
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201009
  poolId: 52100
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201010
  poolId: 52100
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201011
  poolId: 52100
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201012
  poolId: 52100
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 5201013
  poolId: 52101
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 5058001
  poolId: 5058
  name: "孔雀之翼"
  itemId: 620359
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5058002
  poolId: 5058
  name: "竹蜻蜓"
  itemId: 630012
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5058003
  poolId: 5058
  name: "不许看眼罩"
  itemId: 610008
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
}
rows {
  rewardId: 5058004
  poolId: 5058
  name: "暗中观察"
  itemId: 710049
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5058005
  poolId: 5058
  name: "派对狂欢"
  itemId: 720060
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5058006
  poolId: 5058
  name: "呱呱舞"
  itemId: 720075
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5058007
  poolId: 5058
  name: "一发入魂上装"
  itemId: 510062
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5058008
  poolId: 5058
  name: "一发入魂下装"
  itemId: 520046
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5058009
  poolId: 5058
  name: "一发入魂手套"
  itemId: 530025
  itemNum: 1
  groupId: 2
  weight: 13
  limit: 1
}
rows {
  rewardId: 5211001
  poolId: 52110
  name: "古风少女"
  itemId: 402860
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 5211002
  poolId: 52110
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211003
  poolId: 52110
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211004
  poolId: 52110
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211005
  poolId: 52110
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211006
  poolId: 52110
  name: "荷包背饰"
  itemId: 620345
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211007
  poolId: 52110
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211008
  poolId: 52110
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211009
  poolId: 52110
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211010
  poolId: 52110
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211011
  poolId: 52110
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211012
  poolId: 52110
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 5211013
  poolId: 52111
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 5503001
  poolId: 55030
  name: "进度1"
  groupId: 1
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503002
  poolId: 55030
  name: "进度2"
  groupId: 2
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503003
  poolId: 55030
  name: "进度3"
  groupId: 3
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503004
  poolId: 55030
  name: "进度4"
  itemId: 310107
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503005
  poolId: 55030
  name: "进度5"
  groupId: 5
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503006
  poolId: 55030
  name: "进度6"
  groupId: 6
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503007
  poolId: 55030
  name: "进度7"
  itemId: 310108
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503008
  poolId: 55030
  name: "进度8"
  groupId: 8
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503009
  poolId: 55030
  name: "进度9"
  groupId: 9
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503010
  poolId: 55030
  name: "进度10"
  itemId: 310109
  itemNum: 1
  groupId: 10
  weight: 1
  limit: 1
  lvCost: -1
  earningPoints: 1
}
rows {
  rewardId: 5503011
  poolId: 55030
  name: "空道具"
  groupId: 11
  weight: 1
}
rows {
  rewardId: 5503101
  poolId: 55031
  name: "绮梦幻影"
  itemId: 730004
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 5503102
  poolId: 55031
  name: "背饰-天使背包"
  itemId: 620306
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5503103
  poolId: 55031
  name: "头饰-贪食花"
  itemId: 630210
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5503104
  poolId: 55031
  name: "面饰-冰淇淋眼镜"
  itemId: 610149
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 5503105
  poolId: 55031
  name: "动态头像框-心动启程"
  itemId: 840138
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 5503106
  poolId: 55031
  name: "浪漫之约"
  itemId: 820093
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 5503107
  poolId: 55031
  name: "表情-我要奋斗"
  itemId: 710256
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5503108
  poolId: 55031
  name: "表情-小红狐吃瓜"
  itemId: 710262
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5503109
  poolId: 55031
  name: "表情-面部保养"
  itemId: 710264
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5503110
  poolId: 55031
  name: "动作-指点江山"
  itemId: 720631
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5503111
  poolId: 55031
  name: "动作-公正二连"
  itemId: 720633
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5503112
  poolId: 55031
  name: "动作-冰淇淋掉了"
  itemId: 720640
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 5503113
  poolId: 55031
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503114
  poolId: 55031
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503115
  poolId: 55031
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503116
  poolId: 55031
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503117
  poolId: 55031
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503118
  poolId: 55031
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 8
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503119
  poolId: 55031
  name: "表情-动动脑子"
  itemId: 710240
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503120
  poolId: 55031
  name: "表情-听不清"
  itemId: 710214
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503121
  poolId: 55031
  name: "表情-心无杂念"
  itemId: 710216
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503122
  poolId: 55031
  name: "动作-弱小无助"
  itemId: 720621
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503123
  poolId: 55031
  name: "动作-起床失败"
  itemId: 720643
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503124
  poolId: 55031
  name: "动作-揣手"
  itemId: 720642
  itemNum: 1
  groupId: 5
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503125
  poolId: 55031
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503126
  poolId: 55031
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503127
  poolId: 55031
  name: "多云转晴上装"
  itemId: 510163
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 5503128
  poolId: 55031
  name: "多云转晴下装"
  itemId: 520113
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 5503129
  poolId: 55031
  name: "多云转晴手套"
  itemId: 530090
  itemNum: 1
  groupId: 7
  weight: 1
  limit: 5
  isGrand: false
  afterRefreshLimit: 5
}
rows {
  rewardId: 5503130
  poolId: 55031
  name: "春城粉桃上装"
  itemId: 510167
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503131
  poolId: 55031
  name: "春城粉桃下装"
  itemId: 520116
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503132
  poolId: 55031
  name: "春城粉桃手套"
  itemId: 530093
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503133
  poolId: 55031
  name: "海盐兔兔上装"
  itemId: 510158
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503134
  poolId: 55031
  name: "海盐兔兔下装"
  itemId: 520109
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503135
  poolId: 55031
  name: "海盐兔兔手套"
  itemId: 530086
  itemNum: 1
  groupId: 7
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503136
  poolId: 55031
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503137
  poolId: 55031
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503138
  poolId: 55031
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5503139
  poolId: 55031
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 6
  weight: 1
  isGrand: false
}
rows {
  rewardId: 5308001
  poolId: 5308
  name: "猫耳电音台"
  itemId: 640158
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 5308002
  poolId: 5308
  name: "音乐翅膀"
  itemId: 620925
  itemNum: 1
  groupId: 1
  weight: 99
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5308003
  poolId: 5308
  name: "游鱼遗尾"
  itemId: 630629
  itemNum: 1
  groupId: 1
  weight: 5000
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 3
  }
}
rows {
  rewardId: 5308004
  poolId: 5308
  name: "无尽耀光"
  itemId: 610376
  itemNum: 1
  groupId: 1
  weight: 4900
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5308005
  poolId: 5308
  name: "霓虹之面"
  itemId: 610372
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5308006
  poolId: 5308
  name: "葱香煎饼"
  itemId: 620867
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5308007
  poolId: 5308
  name: "香酥菠萝包"
  itemId: 630459
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5308008
  poolId: 5308
  name: "甜小朵"
  itemId: 410880
  itemNum: 1
  groupId: 2
  weight: 3
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5308009
  poolId: 5308
  name: "音之钥*20"
  itemId: 227
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 3
}
rows {
  rewardId: 5308010
  poolId: 5308
  name: "音之钥*10"
  itemId: 227
  itemNum: 10
  groupId: 3
  weight: 20
  limit: 5
  inGroupLimit {
    period: 20
    limit: 1
  }
  comboItemIds: 227
  comboItemIds: 227
  comboItemIds: 227
  comboItemNums: 10
  comboItemNums: 20
  comboItemNums: 30
  comboType: RCT_Always
  comboItemWeights: 350
  comboItemWeights: 40
  comboItemWeights: 10
}
rows {
  rewardId: 5308011
  poolId: 5308
  name: "音之钥*5"
  itemId: 227
  itemNum: 5
  groupId: 3
  weight: 60
  inGroupLimit {
    period: 20
    limit: 4
  }
  comboItemIds: 227
  comboItemIds: 227
  comboItemIds: 227
  comboItemNums: 5
  comboItemNums: 10
  comboItemNums: 15
  comboType: RCT_Always
  comboItemWeights: 300
  comboItemWeights: 80
  comboItemWeights: 20
}
rows {
  rewardId: 5308012
  poolId: 5308
  name: "音之钥*2"
  itemId: 227
  itemNum: 2
  groupId: 3
  weight: 120
  inGroupLimit {
    period: 20
    limit: 15
  }
  comboItemIds: 227
  comboItemIds: 227
  comboItemIds: 227
  comboItemNums: 2
  comboItemNums: 4
  comboItemNums: 6
  comboType: RCT_Always
  comboItemWeights: 200
  comboItemWeights: 125
  comboItemWeights: 50
}
rows {
  rewardId: 5308013
  poolId: 5308
  name: "音之钥*1"
  itemId: 227
  itemNum: 1
  groupId: 4
  weight: 100
  comboItemIds: 227
  comboItemIds: 227
  comboItemIds: 227
  comboItemNums: 1
  comboItemNums: 2
  comboItemNums: 3
  comboType: RCT_Always
  comboItemWeights: 160
  comboItemWeights: 160
  comboItemWeights: 80
}
rows {
  rewardId: 5308014
  poolId: 5308
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5308015
  poolId: 5308
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5308016
  poolId: 5308
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
}
rows {
  rewardId: 5308017
  poolId: 5308
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 50
}
rows {
  rewardId: 5308018
  poolId: 5308
  name: "酸甜柠趣上装"
  itemId: 510181
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5308019
  poolId: 5308
  name: "酸甜柠趣下装"
  itemId: 520124
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5308020
  poolId: 5308
  name: "酸甜柠趣手套"
  itemId: 530101
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5308021
  poolId: 5308
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 50
}
rows {
  rewardId: 5059001
  poolId: 5059
  name: "TOBY"
  itemId: 402940
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5059002
  poolId: 5059
  name: "花花鸭篮"
  itemId: 620377
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5059003
  poolId: 5059
  name: "陌生人给糖"
  itemId: 720720
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5059004
  poolId: 5059
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5059005
  poolId: 5059
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5059006
  poolId: 5059
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5059007
  poolId: 5059
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5059008
  poolId: 5059
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5059009
  poolId: 5059
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060001
  poolId: 5060
  name: "小青瓜"
  itemId: 402950
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5060002
  poolId: 5060
  name: "彩虹背包"
  itemId: 620292
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060003
  poolId: 5060
  name: "你和我"
  itemId: 720724
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060004
  poolId: 5060
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060005
  poolId: 5060
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060006
  poolId: 5060
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060007
  poolId: 5060
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060008
  poolId: 5060
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5060009
  poolId: 5060
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5061001
  poolId: 5061
  name: "toby"
  itemId: 401000
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5061002
  poolId: 5061
  name: "桃气满满"
  itemId: 620100
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5061003
  poolId: 5061
  name: "踢踏舞"
  itemId: 720063
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5061004
  poolId: 5061
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5061005
  poolId: 5061
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5061006
  poolId: 5061
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5061007
  poolId: 5061
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5061008
  poolId: 5061
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5061009
  poolId: 5061
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 80110001
  poolId: 8011
  name: "夜莺之声 艾娜"
  itemId: 404600
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110002
  poolId: 8011
  name: "执念清除师 离火"
  itemId: 410340
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110003
  poolId: 8011
  name: "荆棘鸟 菲奥娜"
  itemId: 404940
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110004
  poolId: 8011
  name: "糖果女巫"
  itemId: 404060
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110005
  poolId: 8011
  name: "梦想收集者"
  itemId: 403250
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110006
  poolId: 8011
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110007
  poolId: 8011
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110008
  poolId: 8011
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80110009
  poolId: 8011
  name: "几何视界"
  itemId: 610291
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110010
  poolId: 8011
  name: "红尘铜障"
  itemId: 610344
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 80110011
  poolId: 8011
  name: "裁梦小剪"
  itemId: 620702
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110012
  poolId: 8011
  name: "社恐小蛛"
  itemId: 630464
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110013
  poolId: 8011
  name: "南瓜糖盒"
  itemId: 620553
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110014
  poolId: 8011
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110015
  poolId: 8011
  name: "小粉猫"
  itemId: 620353
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110016
  poolId: 8011
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110017
  poolId: 8011
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110018
  poolId: 8011
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80110019
  poolId: 8011
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80110020
  poolId: 8011
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80110021
  poolId: 8011
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80110022
  poolId: 8011
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80110023
  poolId: 8011
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80110024
  poolId: 8011
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 80110025
  poolId: 8011
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 80110026
  poolId: 8011
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80110027
  poolId: 8011
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80110028
  poolId: 8011
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80110029
  poolId: 8011
  name: "抓小星星"
  itemId: 720842
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80110030
  poolId: 8011
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80110031
  poolId: 8011
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80110032
  poolId: 8011
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80110033
  poolId: 8011
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80110034
  poolId: 8011
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80110035
  poolId: 8011
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80110036
  poolId: 8011
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80110037
  poolId: 8011
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80110038
  poolId: 8011
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80110039
  poolId: 8011
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80110040
  poolId: 8011
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 80110041
  poolId: 8011
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80110042
  poolId: 8011
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80110043
  poolId: 8011
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80110044
  poolId: 8011
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 80110045
  poolId: 8011
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80110046
  poolId: 8011
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80110047
  poolId: 8011
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80110048
  poolId: 8011
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80110049
  poolId: 8011
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80110050
  poolId: 8011
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80110051
  poolId: 8011
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80110052
  poolId: 8011
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 521201
  poolId: 52120
  name: "云鹤仙  鸿鸣"
  itemId: 402190
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 521202
  poolId: 52120
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 521203
  poolId: 52120
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 521204
  poolId: 52120
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 521205
  poolId: 52120
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 521206
  poolId: 52120
  name: "云羽轻纱"
  itemId: 610244
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 521207
  poolId: 52120
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 521208
  poolId: 52120
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 521209
  poolId: 52120
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 521210
  poolId: 52120
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 521211
  poolId: 52120
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 521212
  poolId: 52120
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 521213
  poolId: 52121
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 80120001
  poolId: 8012
  name: "夜莺之声 艾娜"
  itemId: 404600
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120002
  poolId: 8012
  name: "执念清除师 离火"
  itemId: 410340
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120003
  poolId: 8012
  name: "荆棘鸟 菲奥娜"
  itemId: 404940
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120004
  poolId: 8012
  name: "糖果女巫"
  itemId: 404060
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120005
  poolId: 8012
  name: "梦想收集者"
  itemId: 403250
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120006
  poolId: 8012
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120007
  poolId: 8012
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120008
  poolId: 8012
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80120009
  poolId: 8012
  name: "几何视界"
  itemId: 610291
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120010
  poolId: 8012
  name: "红尘铜障"
  itemId: 610344
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 80120011
  poolId: 8012
  name: "裁梦小剪"
  itemId: 620702
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120012
  poolId: 8012
  name: "社恐小蛛"
  itemId: 630464
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120013
  poolId: 8012
  name: "南瓜糖盒"
  itemId: 620553
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120014
  poolId: 8012
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120015
  poolId: 8012
  name: "小粉猫"
  itemId: 620353
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120016
  poolId: 8012
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120017
  poolId: 8012
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120018
  poolId: 8012
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80120019
  poolId: 8012
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80120020
  poolId: 8012
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80120021
  poolId: 8012
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80120022
  poolId: 8012
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80120023
  poolId: 8012
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80120024
  poolId: 8012
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 80120025
  poolId: 8012
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 80120026
  poolId: 8012
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80120027
  poolId: 8012
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80120028
  poolId: 8012
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80120029
  poolId: 8012
  name: "抓小星星"
  itemId: 720842
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80120030
  poolId: 8012
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80120031
  poolId: 8012
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80120032
  poolId: 8012
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80120033
  poolId: 8012
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80120034
  poolId: 8012
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80120035
  poolId: 8012
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80120036
  poolId: 8012
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80120037
  poolId: 8012
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80120038
  poolId: 8012
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80120039
  poolId: 8012
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80120040
  poolId: 8012
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 80120041
  poolId: 8012
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80120042
  poolId: 8012
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80120043
  poolId: 8012
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80120044
  poolId: 8012
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 80120045
  poolId: 8012
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80120046
  poolId: 8012
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80120047
  poolId: 8012
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80120048
  poolId: 8012
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80120049
  poolId: 8012
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80120050
  poolId: 8012
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80120051
  poolId: 8012
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80120052
  poolId: 8012
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80120053
  poolId: 52130
  name: "古风少女"
  itemId: 402150
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 80120054
  poolId: 52130
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120055
  poolId: 52130
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120056
  poolId: 52130
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120057
  poolId: 52130
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120058
  poolId: 52130
  name: "粉色回忆"
  itemId: 630057
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120059
  poolId: 52130
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120060
  poolId: 52130
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120061
  poolId: 52130
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120062
  poolId: 52130
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120063
  poolId: 52130
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120064
  poolId: 52130
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 80120065
  poolId: 52131
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 96960001
  poolId: 9696
  name: "时装兑换券*1"
  itemId: 3639
  itemNum: 1
  groupId: 1
  weight: 50000
}
rows {
  rewardId: 96960002
  poolId: 9696
  name: "时装兑换券*3"
  itemId: 3639
  itemNum: 3
  groupId: 1
  weight: 30000
}
rows {
  rewardId: 96960003
  poolId: 9696
  name: "时装兑换券*5"
  itemId: 3639
  itemNum: 5
  groupId: 1
  weight: 10000
}
rows {
  rewardId: 96960004
  poolId: 9696
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 3778
}
rows {
  rewardId: 96960005
  poolId: 9696
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 4500
}
rows {
  rewardId: 96960006
  poolId: 9696
  name: "荔枝妞妞"
  itemId: 620398
  itemNum: 1
  groupId: 1
  weight: 1500
  limit: 1
}
rows {
  rewardId: 96960007
  poolId: 9696
  name: "背享净界"
  itemId: 620705
  itemNum: 1
  groupId: 1
  weight: 200
  limit: 1
}
rows {
  rewardId: 96960008
  poolId: 9696
  name: "心动和弦"
  itemId: 620280
  itemNum: 1
  groupId: 1
  weight: 20
  limit: 1
}
rows {
  rewardId: 96960009
  poolId: 9696
  name: "净净管家"
  itemId: 410220
  itemNum: 1
  groupId: 1
  weight: 2
  limit: 1
}
rows {
  rewardId: 80130001
  poolId: 8013
  name: "调香师 安娜苏"
  itemId: 411180
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130002
  poolId: 8013
  name: "执念清除师 离火"
  itemId: 410340
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130003
  poolId: 8013
  name: "荆棘鸟 菲奥娜"
  itemId: 404940
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130004
  poolId: 8013
  name: "糖果女巫"
  itemId: 404060
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130005
  poolId: 8013
  name: "梦想收集者"
  itemId: 403250
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130006
  poolId: 8013
  name: "浪漫笛音 笛娜"
  itemId: 402050
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130007
  poolId: 8013
  name: "天才甜点师 莱杰"
  itemId: 401620
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130008
  poolId: 8013
  name: "木偶王子 安德尔"
  itemId: 401140
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 80130009
  poolId: 8013
  name: "郁郁花香"
  itemId: 630609
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130010
  poolId: 8013
  name: "红尘铜障"
  itemId: 610344
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 80130011
  poolId: 8013
  name: "裁梦小剪"
  itemId: 620702
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130012
  poolId: 8013
  name: "社恐小蛛"
  itemId: 630464
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130013
  poolId: 8013
  name: "南瓜糖盒"
  itemId: 620553
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130014
  poolId: 8013
  name: "浮光绘羽"
  itemId: 620229
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130015
  poolId: 8013
  name: "小粉猫"
  itemId: 620353
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130016
  poolId: 8013
  name: "漂泊之钥匙"
  itemId: 620131
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130017
  poolId: 8013
  name: "狼系少年"
  itemId: 630035
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130018
  poolId: 8013
  name: "龟蜜"
  itemId: 400930
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80130019
  poolId: 8013
  name: "蘑咕咕"
  itemId: 400160
  itemNum: 1
  groupId: 2
  weight: 1
}
rows {
  rewardId: 80130020
  poolId: 8013
  name: "苹果之芯"
  itemId: 620073
  itemNum: 1
  groupId: 3
  weight: 1
}
rows {
  rewardId: 80130021
  poolId: 8013
  name: "金钱眼镜"
  itemId: 610078
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80130022
  poolId: 8013
  name: "像素眼镜"
  itemId: 610077
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80130023
  poolId: 8013
  name: "黑框眼镜"
  itemId: 610043
  itemNum: 1
  groupId: 4
  weight: 0
}
rows {
  rewardId: 80130024
  poolId: 8013
  name: "木偶王子动态头像框"
  itemId: 840061
  itemNum: 1
  groupId: 7
  weight: 1
}
rows {
  rewardId: 80130025
  poolId: 8013
  name: "木偶王子昵称框"
  itemId: 820041
  itemNum: 1
  groupId: 7
  weight: 2
}
rows {
  rewardId: 80130026
  poolId: 8013
  name: "木偶王子头像"
  itemId: 860036
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80130027
  poolId: 8013
  name: "蘑菇菇头像"
  itemId: 860035
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80130028
  poolId: 8013
  name: "龟蜜头像"
  itemId: 860034
  itemNum: 1
  groupId: 7
  weight: 10
}
rows {
  rewardId: 80130029
  poolId: 8013
  name: "抓小星星"
  itemId: 720842
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80130030
  poolId: 8013
  name: "水枪警告"
  itemId: 720055
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80130031
  poolId: 8013
  name: "瑟瑟发抖"
  itemId: 720002
  itemNum: 1
  groupId: 4
  weight: 4
}
rows {
  rewardId: 80130032
  poolId: 8013
  name: "晕倒"
  itemId: 720133
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80130033
  poolId: 8013
  name: "锤子舞"
  itemId: 720135
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80130034
  poolId: 8013
  name: "华丽后空翻"
  itemId: 720111
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80130035
  poolId: 8013
  name: "表情-百发百中"
  itemId: 710134
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80130036
  poolId: 8013
  name: "表情-你可真棒"
  itemId: 710135
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80130037
  poolId: 8013
  name: "表情-我想开了"
  itemId: 710136
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80130038
  poolId: 8013
  name: "表情-危"
  itemId: 710137
  itemNum: 1
  groupId: 4
  weight: 2
}
rows {
  rewardId: 80130039
  poolId: 8013
  name: "表情-急了急了"
  itemId: 710138
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 80130040
  poolId: 8013
  name: "兑换券*10"
  itemId: 205
  itemNum: 10
  groupId: 5
  weight: 3
}
rows {
  rewardId: 80130041
  poolId: 8013
  name: "兑换券*5"
  itemId: 205
  itemNum: 5
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80130042
  poolId: 8013
  name: "兑换券*3"
  itemId: 205
  itemNum: 3
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80130043
  poolId: 8013
  name: "兑换券*2"
  itemId: 205
  itemNum: 2
  groupId: 5
  weight: 30
}
rows {
  rewardId: 80130044
  poolId: 8013
  name: "兑换券*1"
  itemId: 205
  itemNum: 1
  groupId: 5
  weight: 10
}
rows {
  rewardId: 80130045
  poolId: 8013
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80130046
  poolId: 8013
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80130047
  poolId: 8013
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80130048
  poolId: 8013
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80130049
  poolId: 8013
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80130050
  poolId: 8013
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 6
  weight: 9
}
rows {
  rewardId: 80130051
  poolId: 8013
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 6
  weight: 1
}
rows {
  rewardId: 80130052
  poolId: 8013
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 6
  weight: 9
}
rows {
  rewardId: 5062001
  poolId: 5062
  name: "鲨鱼猫"
  itemId: 404950
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5062002
  poolId: 5062
  name: "招财猫"
  itemId: 620697
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5062003
  poolId: 5062
  name: "你和我"
  itemId: 720724
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5062004
  poolId: 5062
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5062005
  poolId: 5062
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5062006
  poolId: 5062
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5062007
  poolId: 5062
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5062008
  poolId: 5062
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5062009
  poolId: 5062
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5063001
  poolId: 5063
  name: "草莓猫"
  itemId: 410860
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5063002
  poolId: 5063
  name: "猫盒"
  itemId: 630458
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5063003
  poolId: 5063
  name: "踢踏舞"
  itemId: 720063
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5063004
  poolId: 5063
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5063005
  poolId: 5063
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 5063006
  poolId: 5063
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 5063007
  poolId: 5063
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 5063008
  poolId: 5063
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 5063009
  poolId: 5063
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
