com.tencent.wea.xlsRes.table_GMCmdConf
excel/xls/G_gm配置.xlsx sheet:gm指令-LetsGo
rows {
  id: 1
  handler: "GmSendMail"
  desc: "发送邮件"
  param {
    desc: "邮件标题"
    default: "系统邮件"
  }
  param {
    desc: "邮箱附件(格式 道具Id:数量:剩余有效秒数,id:num 例如10002:1:900,10003:2:0, 剩余有效秒数0为永久有效"
  }
  param {
    desc: "邮件内容"
    default: "测试邮件(GM指令)"
  }
  param {
    desc: "过期时间(秒),默认一小时"
    default: "3600"
  }
  param {
    desc: "邮件数目"
    default: "1"
  }
  param {
    desc: "跳转链接"
    default: "https://www.baidu.com"
  }
  param {
    desc: "邮件页签(1系统,4星世界)"
    default: "1"
  }
  param {
    desc: "cdkey"
  }
  param {
    desc: "是否星标邮件"
  }
}
rows {
  id: 2
  handler: "GmModifyItem"
  desc: "添加/删除 道具"
  param {
    desc: "1:添加 2:删除 3:清空背包"
    default: "1"
  }
  param {
    desc: "道具ID:道具数量;道具ID:道具数量:有效期(秒)…"
  }
}
rows {
  id: 3
  handler: "GmMallClearBuyRecord"
  desc: "重置商城购买"
  param {
    desc: "0:全部 else commodityId"
    default: "0"
  }
}
rows {
  id: 4
  handler: "GmPlayerModifyAttr"
  desc: "修改玩家属性"
  param {
    desc: "1:昵称, 2:性别, 3:头像, 4:个性签名, 5:地区, 6:关注数"
  }
  param {
    desc: "具体的值，注意类型"
  }
}
rows {
  id: 5
  handler: "GmPlayerRemoveData"
  desc: "清除玩家数据，清除后会进入注册逻辑"
  param {
    desc: "0:清空当前登录账号, 1:清空指定openid和platid账号"
    default: "0"
  }
  param {
    desc: "openid"
    default: "\"\""
  }
  param {
    desc: "platid(ios:0,android:1)"
    default: "0"
  }
  param {
    desc: "是否清除UGC账号(0否1是)"
    default: "1"
  }
}
rows {
  id: 6
  handler: "GmPlayerBackup"
  desc: "备份当前玩家数据"
}
rows {
  id: 7
  handler: "GmPlayerLoadBackup"
  desc: "恢复到备份数据"
}
rows {
  id: 9
  handler: "GmPlayerDressUpModify"
  desc: "修改装扮"
  param {
    desc: "1:穿 2：脱"
    default: "1"
  }
  param {
    desc: "装扮ID"
    default: "0"
  }
}
rows {
  id: 10
  handler: "GmActivityCenterClear"
  desc: "清空活动数据，然后重新加载活动配置"
  param {
    desc: "0:全部 else activityId"
    default: "0"
  }
  param {
    desc: "活动ID"
  }
}
rows {
  id: 11
  handler: "GmPlayerLoginDays"
  desc: "设置登录天数"
  param {
    desc: "活动id"
  }
  param {
    desc: "天数"
  }
  param {
    desc: "条件id"
  }
}
rows {
  id: 12
  handler: "GmSetDsLevelSeq"
  desc: "设置关卡序列"
  param {
    desc: "游戏ID"
    default: "0"
  }
  param {
    desc: "模式ID"
    default: "1"
  }
  param {
    desc: "关卡ID序列,英文分号分隔"
    default: "60002"
  }
  param {
    desc: "关卡晋级率序列,英文分号分隔"
  }
  param {
    desc: "随机事件id,英文分号分隔"
  }
}
rows {
  id: 13
  handler: "GmSetDsRobotCount"
  desc: "设置机器人数量"
  param {
    desc: "游戏ID"
    default: "4"
  }
  param {
    desc: "模式ID"
    default: "8"
  }
  param {
    desc: "机器人数量"
    default: "16"
  }
}
rows {
  id: 14
  handler: "GmAddBattleRecord"
  desc: "伪造对战记录"
  param {
    desc: "地图"
    default: "默认地图"
  }
  param {
    desc: "成绩"
    default: "1"
  }
  param {
    desc: "模式"
    default: "1"
  }
  param {
    desc: "通关用时"
    default: "365"
  }
  param {
    desc: "日期"
    default: "1634785071"
  }
}
rows {
  id: 15
  handler: "GmActivityTempOff"
  desc: "暂时下架活动"
  param {
    desc: "0:全部 else activityId"
  }
  param {
    desc: "活动ID"
  }
}
rows {
  id: 16
  handler: "GmSetStarCount"
  desc: "音游设置星星数量"
  param {
    desc: "数量"
    default: "0"
  }
}
rows {
  id: 17
  handler: "GMSetBattleResult"
  desc: "设置玩家胜负场数"
  param {
    desc: "操作(0 清空 1 增加)"
    default: "1"
  }
  param {
    desc: "模式ID"
    default: "8"
  }
  param {
    desc: "增加场数"
    default: "1"
  }
  param {
    desc: "战斗结果胜(0)负(1)平(2)"
    default: "0"
  }
}
rows {
  id: 18
  handler: "GmPlayerModifyAvatar"
  desc: "设置玩家avatar信息"
  param {
    desc: "AvatarId(AI表avatar序号)"
    default: "1"
  }
}
rows {
  id: 19
  handler: "GmPlayerModifyPrivilege"
  desc: "设置玩家特权信息"
  param {
    desc: "特权开关(0-隐藏按钮，1-不响应点击，2-响应点击)"
    default: "2"
  }
  param {
    desc: "特权等级(0未开通，1低档，2中档，3高档)"
    default: "0"
  }
}
rows {
  id: 20
  handler: "GmPlayerClearGuideTaskData"
  desc: "清除玩家引导任务完成状态"
  param {
    desc: "任务ID，传空则清空全部"
  }
}
rows {
  id: 21
  handler: "GmSetTodayVisitorNum"
  desc: "设置访客数目"
  param {
    desc: "今日访客数目(设为0 则维持原值)"
    default: "9998"
  }
  param {
    desc: "历史访客数目(设为0 则维持原值)"
    default: "9998"
  }
}
rows {
  id: 22
  handler: "GmAddVisitor"
  desc: "新增访客"
  param {
    desc: "新增访客数目(因为去重原因, 实际显示可能小于设定值)"
    default: "100"
  }
}
rows {
  id: 23
  handler: "GmUnlockGlobalSetting"
  desc: "取消锁定全局设定"
  param {
    desc: "0-修改服务器时间；1-设置关卡序列；2-设置机器人数量"
    default: "0"
  }
}
rows {
  id: 24
  handler: "GmAddDKBattleHistory"
  desc: "音游历史记录添加"
  param {
    desc: "指定添加的场次数"
    default: "0"
  }
  param {
    desc: "指定的歌曲，为空在已有的歌曲中随机"
    default: "\"\""
  }
  param {
    desc: "指定的星星数，给定了在对应范围内随机"
    default: "0"
  }
}
rows {
  id: 25
  handler: "GmAddDKLoginCnt"
  desc: "音游登陆天数添加"
  param {
    desc: "需要添加的登陆天数"
    default: "0"
  }
  param {
    desc: "添加解锁歌曲数"
    default: "0"
  }
  param {
    desc: "添加累计场次"
    default: "0"
  }
}
rows {
  id: 26
  handler: "GmSetDsRoundSurvivalRule"
  desc: "设置生存规则"
  param {
    desc: "游戏ID"
    default: "4"
  }
  param {
    desc: "模式ID"
    default: "8"
  }
  param {
    desc: "生存规则,英文分号分隔"
    default: "0.75;0.5;0.1"
  }
}
rows {
  id: 27
  handler: "GmDsGuideSwitch"
  desc: "局内新手引导开关"
  param {
    desc: "1:开 0:关"
    default: "1"
  }
}
rows {
  id: 28
  handler: "GmRefreshGlobalShop"
  desc: "刷新商城"
  param {
    desc: "商城ID"
    default: "0"
  }
}
rows {
  id: 29
  handler: "GmSetQualifyingInfo"
  desc: "设置段位"
  param {
    desc: "积分(根据积分推算段位)"
    default: "0"
  }
  param {
    desc: "段位类型(QualifyType枚举值)"
    default: "0"
  }
  param {
    desc: "赛季ID(非零触发赛季结算)"
    default: "0"
  }
  param {
    desc: "设置保分"
    default: "0"
  }
}
rows {
  id: 30
  handler: "GmAddBattleQualifyingRecord"
  desc: "伪造对局段位结算数据"
  param {
    desc: "模式类型(1单排2组排)"
    default: "1"
  }
  param {
    desc: "对局结果(0胜1负2平)"
    default: "0"
  }
  param {
    desc: "关卡排名,英文分号分隔(-1未达标)"
    default: "1;1;1"
  }
  param {
    desc: "队伍排名,英文分号分隔"
    default: "1;1;1"
  }
}
rows {
  id: 31
  handler: "GmTaskClear"
  desc: "重置任务数据"
  param {
    desc: "任务id(0表示所有)"
    default: "0"
  }
}
rows {
  id: 32
  handler: "GmFakeFinishBattleEvent"
  desc: "音游完成一场对局"
  param {
    desc: "星数"
    default: "0"
  }
  param {
    desc: "是否组队(0否1是)"
    default: "0"
  }
  param {
    desc: "战斗开始时间(yyyyMMddHHmmss)"
    default: "0"
  }
  param {
    desc: "战斗结束时间(yyyyMMddHHmmss)"
    default: "0"
  }
  param {
    desc: "战斗模式"
    default: "0"
  }
}
rows {
  id: 33
  handler: "GmSetRobotDifficult"
  desc: "设置机器人难度等级"
  param {
    desc: "难度级别"
    default: "0"
  }
}
rows {
  id: 34
  handler: "GmFakeFinishPvEStageEvent"
  desc: "通关指定PvE关卡"
  param {
    desc: "关卡id"
    default: "0"
  }
}
rows {
  id: 35
  handler: "GmBuyAllGoldItem"
  desc: "一键购买全部商城金币物品"
}
rows {
  id: 36
  handler: "GmBatchSendMsgToWorldChat"
  desc: "批量发送消息到世界聊天"
  param {
    desc: "消息正文"
    default: "测试"
  }
  param {
    desc: "条数"
    default: "1"
  }
}
rows {
  id: 37
  handler: "GmEquipOffAllItems"
  desc: "脱装扮"
  param {
    desc: "道具UUID"
  }
}
rows {
  id: 38
  handler: "GmSetDsMiscConfig"
  desc: "设置对局ds杂项配置"
  param {
    desc: "杂项配置key 例如ds_gm_newai_levellist"
    default: "ds_gm_newai_levellist"
  }
  param {
    desc: "杂项配置value"
    default: "60018;60045"
  }
}
rows {
  id: 39
  handler: "GmUnLockMode"
  desc: "解锁游戏模式"
  param {
    desc: "模式ID "
    default: "1"
  }
}
rows {
  id: 40
  handler: "GMaddPermitExp"
  desc: "通行证加经验"
  param {
    desc: "经验值"
    default: "1"
  }
  param {
    desc: "类型(0:主玩法 2:狼人)"
    default: "0"
  }
}
rows {
  id: 41
  handler: "GMPermitUnlockLevel"
  desc: "通行证解锁荣耀版"
  param {
    desc: "0：购买等级 1：高级 2：荣耀"
    default: "1"
  }
  param {
    desc: "等级数量"
    default: "0"
  }
  param {
    desc: "类型(0:主玩法 2:狼人)"
    default: "0"
  }
}
rows {
  id: 42
  handler: "GmSetDSDevEnv"
  desc: "ds局内测试环境id设置"
  param {
    desc: "根据ds管理配置的dev环境id来设置"
    default: "8"
  }
}
rows {
  id: 43
  handler: "GmAddPlatFriend"
  desc: "批量添加好友"
  param {
    desc: "1、游戏好友  2、平台好友"
    default: "1"
  }
  param {
    desc: "数量"
    default: "10"
  }
}
rows {
  id: 44
  handler: "GmAddInviteRegister"
  desc: "添加邀请注册好友"
  param {
    desc: "uid"
  }
  param {
    desc: "等级"
  }
}
rows {
  id: 45
  handler: "GMAddVipExp"
  desc: "增加充值经验"
  param {
    desc: "经验"
    default: "100"
  }
}
rows {
  id: 46
  handler: "GmMidas"
  desc: "米大师操作"
  param {
    desc: "1：更新余额 2：直购 3：代币购买 4：赠送代币"
    default: "1"
  }
  param {
    desc: "米大师商品id或赠送数量"
  }
}
rows {
  id: 47
  handler: "GmRaffleDraw"
  desc: "抽奖模拟抽取"
  param {
    desc: "奖池组ID"
  }
  param {
    desc: "每人点击次数（不超过500）"
  }
  param {
    desc: "玩家数量（不超过500）"
  }
  param {
    desc: "点击单抽比重"
  }
  param {
    desc: "点击多抽比重"
  }
  param {
    desc: "选中道具ID(英文逗号分割，可不填)"
  }
  param {
    desc: "额外参数(英文分号分割，可不填)"
  }
}
rows {
  id: 48
  handler: "GmSetMatchRoomInfoID"
  desc: "设置匹配房间"
  param {
    desc: "匹配房间ID(设置的人需要时队伍队长，填0则清空设定)"
    default: "0"
  }
}
rows {
  id: 49
  handler: "GmSetFakeRank"
  desc: "设置排行假数据"
  param {
    desc: "操作（1设置，2清空所有）"
  }
  param {
    desc: "排名类型:分数,分数;排名类型:分数;…"
  }
}
rows {
  id: 50
  handler: "GmBattleSettlement"
  desc: "模拟结算测试"
  param {
    desc: "玩法ID"
    default: "1"
  }
  param {
    desc: "组队玩家UID(多个用;分割)"
  }
  param {
    desc: "对局开始时间"
  }
  param {
    desc: "对局结束时间"
  }
}
rows {
  id: 51
  handler: "GmDepositReset"
  desc: "储蓄属性重置"
  param {
    desc: "1：设置领取奖励时间 2：清空 3：设置账面数值"
  }
  param {
    desc: "储蓄ID"
  }
  param {
    desc: "GiftID（设置领取时间）/ 账面数额（设置账面数额）"
  }
  param {
    desc: "uuid"
  }
  param {
    desc: "几分钟后到期"
  }
}
rows {
  id: 52
  handler: "GmDepositSetFakePurchase"
  desc: "储蓄设置假购买"
  param {
    desc: "0：Midas 1：30s后返回成功 2：立刻返回失败 3：无响应"
  }
}
rows {
  id: 53
  handler: "GmUgcGameCfg"
  desc: "ugc游戏配置"
  param {
    desc: "1：ugcid"
  }
}
rows {
  id: 54
  handler: "GmMSDKArkShare"
  desc: "MSDK ArkShare分享测试"
  param {
    desc: "0:好友openid（fOpenId）"
  }
  param {
    desc: "1:token(非必填)"
  }
}
rows {
  id: 55
  handler: "GmSetCreatorInfo"
  desc: "更新创作者信息"
  param {
    desc: "1.工匠等级"
  }
  param {
    desc: "具体值(注意类型)"
  }
}
rows {
  id: 56
  handler: "GmGetPlayerUgcInfo"
  desc: "查询UGC信息"
}
rows {
  id: 57
  handler: "GmGetUgcMapExp"
  desc: "查询ugc地图工匠值信息"
}
rows {
  id: 58
  handler: "GMClearUGCRedis"
  desc: "清除ugc地图redis缓存数据"
}
rows {
  id: 59
  handler: "GMSetUgcReportStatus"
  desc: "修改ugc地图审核状态"
  param {
    desc: "地图id"
  }
  param {
    desc: "0:预发布 1:待审核 2:正式发布 3:驳回 4:下架"
  }
}
rows {
  id: 60
  handler: "GmLobbyAddRobot"
  desc: "添加大厅假人"
  param {
    desc: "大厅id，填0为当前玩家大厅"
    default: "0"
  }
  param {
    desc: "人数，填0为填满大厅"
    default: "0"
  }
}
rows {
  id: 61
  handler: "GmLobbyClearRobot"
  desc: "清除大厅假人"
  param {
    desc: "大厅id，填0为当前玩家大厅"
    default: "0"
  }
}
rows {
  id: 62
  handler: "GmTestBanInfo"
  desc: "封禁测试"
  param {
    desc: "封禁类型 0:封号 1:禁止发言 2:封禁个性签名 3:封禁玩家头像 4:封禁玩家形象 5:封禁玩家修改个人信息"
    default: "0"
  }
  param {
    desc: "封禁时间(秒)"
    default: "60"
  }
  param {
    desc: "封禁原因"
  }
}
rows {
  id: 63
  handler: "GmPlayerMMR"
  desc: "玩家MMR分设置"
  param {
    desc: "1: 查询 2: 清空 3: 增加MMR分数变更"
    default: "0"
  }
  param {
    desc: "参数1"
    default: "0"
  }
  param {
    desc: "参数2"
    default: "0"
  }
  param {
    desc: "参数3"
    default: "0"
  }
  param {
    desc: "参数4"
    default: "0"
  }
  param {
    desc: "参数5"
    default: "0"
  }
}
rows {
  id: 64
  handler: "GmPlayerWarmRound"
  desc: "玩家温暖局分设置"
  param {
    desc: "1: 查询 2: 清空 3: 增加温暖局战斗结束后变更 4: 增加进入温暖局产生分数变更"
    default: "0"
  }
  param {
    desc: "模式3: 增加的温暖局类型数字id 模式4: matchType"
    default: "1"
  }
  param {
    desc: "1: 冠军 0: 非冠军"
    default: "0"
  }
  param {
    desc: "进入的回合数"
    default: "1"
  }
  param {
    desc: "增加的时间戳毫秒(0则用当前时间)"
    default: "0"
  }
}
rows {
  id: 65
  handler: "GmUGCDeleteGroup"
  desc: "ugc组合删除组件"
  param {
    desc: "组合id"
  }
}
rows {
  id: 66
  handler: "GmRaffleDebugTrigger"
  desc: "抽奖细节输出开关"
  param {
    desc: "1：开启 2：关闭"
  }
}
rows {
  id: 67
  handler: "GmLobbyMerge"
  desc: "触发大厅合并"
  param {
    desc: "目标大厅id"
    default: "0"
  }
}
rows {
  id: 68
  handler: "GmAddIntimacy"
  desc: "增加好友亲密度"
  param {
    desc: "好友uid"
  }
  param {
    desc: "增加亲密度值"
    default: "10"
  }
}
rows {
  id: 69
  handler: "GmAddRelation"
  desc: "添加社交关系"
  param {
    desc: "1游戏好友 2社交好友 3黑名单"
    default: "1"
  }
  param {
    desc: "1uid 2openid"
    default: "1"
  }
  param {
    desc: "uid/openid"
  }
}
rows {
  id: 70
  handler: "GmResetMallBuyLimit"
  desc: "重置商城限购"
}
rows {
  id: 71
  handler: "GmOpenLocalRecommendList"
  desc: "设置开启本地推荐列表"
  param {
    desc: "1开启0是不开启"
  }
}
rows {
  id: 72
  handler: "GmPlayerCompleteGuideTaskData"
  desc: "设置玩家引导任务完成状态"
  param {
    desc: "任务ID，传空则全部完成"
  }
}
rows {
  id: 73
  handler: "GmSetLimitTimes"
  desc: "设置限制次数"
  param {
    desc: "类型：1:局内掉落每日限制 2:赛季任务组掉落限制 3:世界聊天每日免费发消息限制 4:每日祈福次数限制"
    default: "0"
  }
  param {
    desc: "ID"
    default: "0"
  }
  param {
    desc: "设置的值"
    default: "0"
  }
}
rows {
  id: 74
  handler: "GmTriggerChatMsgDelete"
  desc: "触发聊天消息删除"
  param {
    desc: "聊天频道类型"
    default: "0"
  }
  param {
    desc: "聊天频道id或者分线"
    default: "0"
  }
  param {
    desc: "指定玩家uid/全部"
    default: "0"
  }
}
rows {
  id: 75
  handler: "GmSetDsLevelDuration"
  desc: "设置关卡通关时间"
  param {
    desc: "游戏ID"
    default: "0"
  }
  param {
    desc: "模式ID"
    default: "1"
  }
  param {
    desc: "关卡ID(设置默认通关时间则为0):通关时间(秒),英文分号分隔"
    default: "60002:30"
  }
}
rows {
  id: 76
  handler: "GmSetTaskProgress"
  desc: "设置任务进度"
  param {
    desc: "任务id"
  }
  param {
    desc: "任务进度"
  }
}
rows {
  id: 77
  handler: "GmResetMoneyTree"
  desc: "重置摇钱树操作次数，CD清空"
}
rows {
  id: 78
  handler: "GMAddPlantLv"
  desc: "增加种植等级"
  param {
    desc: "增加的等级"
    default: "1"
  }
}
rows {
  id: 79
  handler: "GMAddXiaowoLv"
  desc: "增加小窝等级"
  param {
    desc: "增加的等级"
    default: "1"
  }
}
rows {
  id: 80
  handler: "GMAddXiaowoLike"
  desc: "增加小窝赞"
  param {
    desc: "增加的个数"
  }
}
rows {
  id: 81
  handler: "GmSetZSetRankList"
  desc: "设置Zset排行榜"
  param {
    desc: "排行榜ID"
    default: "3"
  }
  param {
    desc: "1：添加 2：清空"
  }
  param {
    desc: "添加人数"
  }
  param {
    desc: "分数上限"
  }
  param {
    desc: "分数下限"
  }
}
rows {
  id: 82
  handler: "GmRaffleReset"
  desc: "抽奖活动重置"
  param {
    desc: "3: 清除 4：增加BI折扣 5：清空BI折扣 6: 设置福利卡"
    default: "3"
  }
  param {
    desc: "奖池组ID"
  }
  param {
    desc: "福利卡配置"
  }
}
rows {
  id: 83
  handler: "GmIdcNetwork"
  desc: "玩家IDC测速信息"
  param {
    desc: "1: 查询 2: 设置 3: clear某个IDC 4: 清空所有"
    default: "1"
  }
  param {
    desc: "Idc id"
    default: "1"
  }
  param {
    desc: "延迟ms"
    default: "100"
  }
}
rows {
  id: 84
  handler: "GmSetUgcDailyStageResetCount"
  desc: "设置巡游闯关的重置次数"
  param {
    desc: "数字几表示已经使用重置了几次"
    default: "0"
  }
}
rows {
  id: 85
  handler: "GmSetUgcDailyStageStartMapId"
  desc: "设置巡游闯关的下一个新地图ID"
  param {
    desc: "地图ID（mapid）,填0相当于清除"
    default: "0"
  }
}
rows {
  id: 86
  handler: "GmSetMapInfo"
  desc: "设置地图游玩点赞收藏信息"
  param {
    desc: "地图ID（mapid"
    default: "0"
  }
  param {
    desc: "改变类型(1:点赞,2:收藏,4:游玩,5:订阅(最大500.仅边界测试用),6收藏(最大500.仅边界测试用),7模组使用,8游玩时长,9地图状态改变,10分享,11评价人数"
    default: "0"
  }
  param {
    desc: "设置的数值"
    default: "0"
  }
}
rows {
  id: 87
  handler: "GmDispatchLevelEvent"
  desc: "关卡结算事件"
  param {
    desc: "关卡ID"
    default: "0"
  }
  param {
    desc: "轮次"
    default: "1"
  }
  param {
    desc: "玩法ID"
    default: "1"
  }
  param {
    desc: "关卡事件BattleEventTypeValue:num;BattleEventTypeValue:num;…..."
  }
}
rows {
  id: 89
  handler: "GMUpdateLoginType"
  desc: "修改登录方式"
  param {
    desc: "1:微信 2:QQ"
    default: "1"
  }
}
rows {
  id: 90
  handler: "GmPlayerLoginCountryCode"
  desc: "玩家登录国家id"
  param {
    desc: "1: 查询 2: 设置"
    default: "1"
  }
  param {
    desc: "国家id"
    default: "0"
  }
}
rows {
  id: 91
  handler: "GmRankRemove"
  desc: "清空排行榜"
  param {
    desc: "1:上个赛季 2:当前赛季 3:所有"
    default: "1"
  }
}
rows {
  id: 92
  handler: "GmNotify"
  desc: "触发全服广播"
  param {
    desc: "0：功能开关 1：导航页"
    default: "0"
  }
}
rows {
  id: 93
  handler: "GmRankFill"
  desc: "填充排行榜"
  param {
    desc: "榜单ID"
    default: "0"
  }
  param {
    desc: "关卡ID"
    default: "0"
  }
  param {
    desc: "数量"
    default: "0"
  }
  param {
    desc: "最小分数（段位榜填入积分，关卡榜填入秒数）"
    default: "0"
  }
  param {
    desc: "最大分数（段位榜填入积分，关卡榜填入秒数）"
    default: "0"
  }
  param {
    desc: "是否包含LBS(0:否 1:自身所在地区)"
    default: "0"
  }
}
rows {
  id: 94
  handler: "GMXiaowoRestartDs"
  desc: "重启家园ds"
}
rows {
  id: 95
  handler: "GmBattleDetailedScore"
  desc: "开启详细得分信息"
  param {
    desc: "0: 关闭  1: 开启"
  }
}
rows {
  id: 96
  handler: "GmPlayerRedEnvelopeRain"
  desc: "红包雨活动"
  param {
    desc: "0:查询 1:捡 2:打开 3:清空 4:增加未打开的红包 5:查询红包记录"
    default: "0"
  }
  param {
    desc: "活动id"
    default: "0"
  }
  param {
    desc: "参数2"
    default: "0"
  }
  param {
    desc: "参数3"
    default: "0"
  }
}
rows {
  id: 97
  handler: "GmChangeChannelId"
  desc: "修改注册渠道号"
  param {
    desc: "1: 微信; 2: 手Q  "
  }
}
rows {
  id: 98
  handler: "GmBatchSendItemByType"
  desc: "批量发送道具"
  param {
    desc: "道具类型：\n105 皇冠\n201 套装\n202 上装\n203 下装\n204 手套\n205 面饰\n206 头饰\n207 背饰\n208 脸部\n301 表情\n302 动作\n303 头像框\n304 称号\n306 头像"
  }
}
rows {
  id: 99
  handler: "GmSetAchievementProgress"
  desc: " 修改成就进度"
  param {
    desc: "成就id"
    default: "0"
  }
  param {
    desc: "进度值"
    default: "0"
  }
}
rows {
  id: 100
  handler: "GmSetInviteInfo"
  desc: "设置玩家邀请注册"
  param {
    desc: "邀请注册的玩家OpenId"
  }
}
rows {
  id: 101
  handler: "GmClearLuckyMoneyData"
  desc: "清理超级红包数据"
  param {
    desc: "1:超级红包活动id"
    default: "3"
  }
}
rows {
  id: 102
  handler: "GmResetInvitation"
  desc: "清理社交裂变、绵绵外卖助力记录"
  param {
    desc: "操作（0: 清理本人被助力和助力信息 1：仅清理被助力 2：仅清理助力他人）"
    default: "0"
  }
  param {
    desc: "配置ID（查看Y_邀请配置）"
    default: "0"
  }
}
rows {
  id: 103
  handler: "GmSetInterServerActivityProgress"
  desc: "设置全服礼包进度"
  param {
    desc: "活动id"
    default: "0"
  }
  param {
    desc: "进度值"
    default: "0"
  }
}
rows {
  id: 104
  handler: "GmSetPlayerState"
  desc: "设置玩家的一些状态"
  param {
    desc: "状态：\n1、暂离状态\n2、离线（会下线清缓存）"
    default: "1"
  }
  param {
    desc: "0：设置\n1：取消"
    default: "0"
  }
}
rows {
  id: 105
  handler: "GmClearIntimacyCDTime"
  desc: "清除所有亲密关系解除冷却时间"
}
rows {
  id: 106
  handler: "GmBattleDataEvent"
  desc: "模拟局内事件"
  param {
    desc: "事件id列表（逗号分隔）"
    default: "1,2"
  }
  param {
    desc: "事件数值列表（逗号分隔）"
    default: "10,10"
  }
}
rows {
  id: 107
  handler: "GmSendCreatorDailyReport"
  desc: "发生UGC日报"
  param {
    desc: "收藏;粉丝;点赞;游玩(英文分号分隔)"
    default: "1;2;3;4"
  }
  param {
    desc: "入选推荐地图id列表(英文分号间隔)"
    default: "1520844424930132657;1520844424930132653"
  }
}
rows {
  id: 108
  handler: "GmQueryGroupID"
  desc: "查询兼容组"
  param {
    desc: "目标UID"
  }
}
rows {
  id: 109
  handler: "GmUpdateMapInfo"
  desc: "根据地图MapId 修改名称、标签描述 不填写表示不修改"
  param {
    desc: "mapId"
  }
  param {
    desc: "名字"
  }
  param {
    desc: "标签"
  }
  param {
    desc: "描述"
  }
}
rows {
  id: 110
  handler: "GmKickSelf"
  desc: "把自己从小窝踢掉"
  param {
    desc: "Reason"
  }
}
rows {
  id: 111
  handler: "GmRecommendMatchType"
  desc: "玩法推荐请求测试"
}
rows {
  id: 112
  handler: "GmSetUgcPlayerInfo"
  desc: "设置玩家UGC相关数据"
  param {
    desc: "玩家CreatorId"
    default: "0"
  }
  param {
    desc: "1、粉丝数量 2、点阅数量"
    default: "0"
  }
  param {
    desc: "设置数值"
  }
}
rows {
  id: 113
  handler: "GmTestBroadcastNotice"
  desc: "跑马灯测试"
  param {
    desc: "id"
    default: "0"
  }
  param {
    desc: "参数1(参数类型:参数num:参数Str)"
  }
  param {
    desc: "参数2"
  }
  param {
    desc: "参数3"
  }
}
rows {
  id: 114
  handler: "GmSetMatchDscCpuLoad"
  desc: "设置MatchSvrDscCpu负载"
  param {
    desc: "开关(0关掉, 1打开)"
    default: "0"
  }
  param {
    desc: "负载值(0-100,大于100也可如果没配就按照100的配置)"
    default: "51"
  }
}
rows {
  id: 115
  handler: "GmSetCloseWarmRound"
  desc: "设置关闭普通温暖局"
  param {
    desc: "开关(0关掉, 1打开)"
    default: "1"
  }
}
rows {
  id: 116
  handler: "GmTestWXGameShare"
  desc: "微信游戏圈分享测试"
  param {
    desc: "actId"
  }
  param {
    desc: "contentId"
  }
  param {
    desc: "info"
  }
  param {
    desc: "jumpParams(a=1;b=2)"
  }
}
rows {
  id: 117
  handler: "GmClearTYCDB"
  desc: "塔防数据清理"
  param {
    desc: "玩法模式（0:所有,80101:大亨,80301:TD,80501:TDS）"
    default: "0"
  }
}
rows {
  id: 118
  handler: "GMXiaowoResetMap"
  desc: "重置小窝地图"
}
rows {
  id: 119
  handler: "GmActivityRecruiteConfirm"
  desc: "指定召集对象"
  param {
    desc: "actId"
    default: "116"
  }
  param {
    desc: "被召集者的openid"
  }
  param {
    desc: "被召集者的platid"
    default: "0"
  }
  param {
    desc: "被召集者的uid"
    default: "0"
  }
}
rows {
  id: 120
  handler: "GmClubJoin"
  desc: "申请加入指定社团"
  param {
    desc: "社团ID"
  }
}
rows {
  id: 121
  handler: "GmClubClear"
  desc: "清除玩家所有社团信息"
}
rows {
  id: 122
  handler: "GmLuckyBalloonReset"
  desc: "清除所有的幸运气球记录"
}
rows {
  id: 123
  handler: "GMXiaowoCreditCheck"
  desc: "家园信用分检查"
  param {
    desc: "0关1开"
  }
}
rows {
  id: 124
  handler: "GmReturningUser"
  desc: "回流玩家"
  param {
    desc: "0: 查询 1: 开启 2: 关闭"
    default: "0"
  }
}
rows {
  id: 125
  handler: "GmAllServerPreWarnMarqueeNotice"
  desc: "全服预警跑马灯"
  param {
    desc: "广播内容"
  }
  param {
    desc: "模板id"
    default: "4"
  }
  param {
    desc: "公告开始时间戳"
  }
  param {
    desc: "公告结束时间戳"
  }
  param {
    desc: "平台id 0:IOS 1:Android 2:PC 3:MAC 4:ALL"
    default: "4"
  }
  param {
    desc: "公告播放总次数"
    default: "1"
  }
  param {
    desc: "单次公告轮播次数"
    default: "1"
  }
}
rows {
  id: 126
  handler: "GmClearWholeGameProcess"
  desc: "设置全服进度数据（非计算值）"
  param {
    desc: "活动id"
    default: "0"
  }
  param {
    desc: "累计值"
    default: "12"
  }
  param {
    desc: "全服进度编号(12:99公益;13:功夫熊猫)"
  }
}
rows {
  id: 130
  handler: "GmClearLayoutPublishRecord"
  desc: "清空家园发布历史记录"
}
rows {
  id: 131
  handler: "GMFarmingCmd"
  desc: "种田指令"
  param {
    desc: "1全部干涸 2全部成熟 3下次浇水必白银 4下次浇水必黄金 5获取所有幼苗10个"
    default: "0"
  }
}
rows {
  id: 132
  handler: "GMFarmingHandbook"
  desc: "种田图鉴增加"
  param {
    desc: "作物id"
    default: "0"
  }
  param {
    desc: "数量"
    default: "1"
  }
}
rows {
  id: 133
  handler: "GmSetRankGeoInfo"
  desc: "排行榜LBS设置"
  param {
    desc: "1.允许再次设置"
  }
}
rows {
  id: 134
  handler: "GmAddHistoryMultiPlayerSquadDataToAttr"
  desc: "添加历史小队数据到属性系统（如果有配置的话）"
}
rows {
  id: 135
  handler: "GmReturnActivityStart"
  desc: "开启个人回归活动"
  param {
    desc: "流失天数"
    default: "10"
  }
  param {
    desc: "档位索引"
    default: "1"
  }
}
rows {
  id: 136
  handler: "GmReturnActivityStop"
  desc: "关闭个人回归活动"
  param {
    desc: "是否清除结束时间"
    default: "True"
  }
}
rows {
  id: 137
  handler: "GmClearXiaowoVer"
  desc: "清除小窝版本号"
  param {
    desc: "uid"
  }
}
rows {
  id: 138
  handler: "GmGetGeoCode"
  desc: "获取行政编码"
  param {
    desc: "1:按名称搜索 2:按经纬度搜索"
    default: "1"
  }
  param {
    desc: "名称或者经度"
  }
  param {
    desc: "纬度"
  }
}
rows {
  id: 139
  handler: "GmCmdJumpDs"
  desc: "gm通过服务器到ds"
  param {
    desc: "gm类型"
    default: "SetPlayerLevel"
  }
  param {
    desc: "值"
    default: "1:2;2:0"
  }
}
rows {
  id: 140
  handler: "GMXiaowoSendLiuYanMessage"
  desc: "发送小窝留言板消息"
  param {
    desc: "留言消息"
    default: "我是一条留言"
  }
  param {
    desc: "留言重复次数"
    default: "1"
  }
}
rows {
  id: 141
  handler: "GmModifyUpdatePublishTime"
  desc: "修改旧地图限定时间"
  param {
    desc: "开始时间 例：2024-02-27 00:00:00"
  }
  param {
    desc: "结束时间 例：2024-02-28 00:00:00"
  }
}
rows {
  id: 142
  handler: "GMXiaowoClearLiuYanMessage"
  desc: "清空小窝留言板消息"
}
rows {
  id: 143
  handler: "GMSetDirectMatchSucc"
  desc: "设置秒匹配成功(只针对当前账号生效)"
  param {
    desc: "指令生效或者取消 0取消 1生效 "
    default: "1"
  }
  param {
    desc: "配置超时失败也匹配成功 0 保持直接失败 1 直接成功"
    default: "1"
  }
}
rows {
  id: 144
  handler: "GmUpdateQualifyingIntegral"
  desc: "增加\\减少段位分值"
  param {
    desc: "段位类型"
    default: "1"
  }
  param {
    desc: "改变的段位分"
    default: "0"
  }
}
rows {
  id: 145
  handler: "GmSendTeamBattleBroadcastMsg"
  desc: "发送队伍战斗播报"
  param {
    desc: "播报ID(11-19)"
  }
  param {
    desc: "Param1"
  }
  param {
    desc: "Param2"
  }
  param {
    desc: "Param3"
  }
}
rows {
  id: 146
  handler: "GmSysUpdateTime"
  desc: "调整服务器时间【仅个人私服可用!!!】"
  param {
    desc: "1：按偏移量 2：按目标日期"
    default: "1"
  }
  param {
    desc: "偏移量（单位是毫秒），或者目标日期（格式为yyyyMMddHHmmss）"
    default: "0"
  }
}
rows {
  id: 147
  handler: "GMFarmCropCmd"
  desc: "农场养殖物指令"
  param {
    desc: "操作 1重置 2成熟 3增加生长 4清空 5减少照料"
    default: "0"
  }
  param {
    desc: "地格ID(0表示全部)"
    default: "0"
  }
  param {
    desc: "参数[时间]"
    default: "3600"
  }
}
rows {
  id: 148
  handler: "GMFarmAddItem"
  desc: "农场背包加道具"
  param {
    desc: "道具ID"
    default: "1001"
  }
  param {
    desc: "数量"
    default: "100"
  }
}
rows {
  id: 149
  handler: "GmFarmAddMainExp"
  desc: "农场增加小屋经验"
  param {
    desc: "增加经验"
    default: "0"
  }
}
rows {
  id: 150
  handler: "GmFarmBuildingLevelUp"
  desc: "农场建筑升级"
  param {
    desc: "建筑类型ID"
  }
  param {
    desc: "增加等级"
  }
}
rows {
  id: 151
  handler: "GMFarmAddCropExp"
  desc: "农场增加养殖物经验"
  param {
    desc: "操作类型 1增加经验 2增加等级"
    default: "1"
  }
  param {
    desc: "养殖物类型"
    default: "2001"
  }
  param {
    desc: "等级值/经验值"
    default: "0"
  }
}
rows {
  id: 152
  handler: "GMUnlockAllGrids"
  desc: "解锁全部地格"
}
rows {
  id: 153
  handler: "GMFarmRestartDs"
  desc: "重启农场ds"
}
rows {
  id: 154
  handler: "GMFarmSetRipeQuality"
  desc: "成熟必定暴击"
  param {
    desc: "0取消1小暴击2大暴击"
  }
}
rows {
  id: 155
  handler: "GMXiaowoResetMapToVersion"
  desc: "重置小窝地图版本"
  param {
    desc: "版本号int64"
  }
}
rows {
  id: 156
  handler: "GmSetUserLabel"
  desc: "设置用户标签"
  param {
    desc: "labelId"
    default: "1"
  }
  param {
    desc: "标签值，分号间隔"
  }
  param {
    desc: "时间戳"
    default: "0"
  }
}
rows {
  id: 157
  handler: "GmSendSuperCoreMsg"
  desc: "发送超核消息"
  param {
    desc: "文本内容"
    default: "Hello,World!"
  }
}
rows {
  id: 158
  handler: "GmAlbumTssCheckFail"
  desc: "相册图片自动审核结果设置"
  param {
    desc: "审核结果"
    default: "1"
  }
}
rows {
  id: 159
  handler: "GMSetUgcMatchMap"
  desc: "设置ugc地图匹配"
  param {
    desc: "星世界推荐"
  }
  param {
    desc: "一起来玩"
  }
  param {
    desc: "星图广场"
  }
  param {
    desc: "一起来玩-合集地图"
  }
  param {
    desc: "星世界推荐-合集地图"
  }
  param {
    desc: "星世界推荐-多按钮"
  }
  param {
    desc: "多轮次-合集地图"
  }
  param {
    desc: "星世界推荐-索引"
  }
}
rows {
  id: 160
  handler: "GMSetUgcMapPublishTime"
  desc: "修改草稿箱已发地图发布时间"
  param {
    desc: "ugcId"
    default: "0"
  }
  param {
    desc: "发布地图时间戳"
  }
  param {
    desc: "发布时间戳"
  }
}
rows {
  id: 161
  handler: "GMModifyXiaowoMD5"
  desc: "修改小窝md5码"
  param {
    desc: "type"
  }
  param {
    desc: "md5"
  }
  param {
    desc: "preMd5"
  }
  param {
    desc: "editMd5"
  }
  param {
    desc: "editPreMd5"
  }
}
rows {
  id: 162
  handler: "GMFarmSendLiuYanMessage"
  desc: "发送农场留言板消息"
  param {
    desc: "留言消息"
    default: "我是一条留言"
  }
  param {
    desc: "留言重复次数"
    default: "1"
  }
}
rows {
  id: 163
  handler: "GMFarmClearLiuYanMessage"
  desc: "清空农场留言板消息"
}
rows {
  id: 164
  handler: "GMUgcPlayerSignOut"
  desc: "注销ugcPlayer账号 仅限ugc大区"
  param {
    desc: "creatorId"
    default: "0"
  }
  param {
    desc: "0 正常 1 注销"
  }
}
rows {
  id: 165
  handler: "GmSetLobbyDsMiscConfig"
  desc: "设置大厅ds杂项配置"
  param {
    desc: "杂项配置key 例如ds_gm_newai_levellist"
    default: "ds_gm_newai_levellist"
  }
  param {
    desc: "杂项配置value"
    default: "60018;60045"
  }
}
rows {
  id: 166
  handler: "GmCleanRecommendData"
  desc: "限时娱乐推荐"
  param {
    desc: "开启推荐配置id（0-清空数据）"
    default: "0"
  }
  param {
    desc: "持续时间（单位小时）"
    default: "2"
  }
}
rows {
  id: 167
  handler: "GMOpenTestRoomInfoId"
  desc: "打开测试匹配配置"
  param {
    desc: "指令生效或者取消 0取消 1生效 "
    default: "1"
  }
}
rows {
  id: 168
  handler: "GMFarmAddMonPassSec"
  desc: "增加农场月卡秒数"
  param {
    desc: "秒数"
  }
}
rows {
  id: 169
  handler: "GMRoguelikeUnlock"
  desc: "解锁肉鸽全部难度"
}
rows {
  id: 170
  handler: "GMAddSquadActivityHistoryPartner"
  desc: "添加多人成团历史成员信息"
  param {
    desc: "uid"
  }
}
rows {
  id: 171
  handler: "GmModifyReputationScore"
  desc: "测试增加信誉分"
  param {
    desc: "modeId"
    default: "5"
  }
  param {
    desc: "behaviorId"
    default: "99999"
  }
}
rows {
  id: 172
  handler: "GMFarmSetFertilizeCount"
  desc: "农场设置今日施肥次数"
  param {
    desc: "今日施肥次数"
  }
}
rows {
  id: 173
  handler: "GMFarmSetFertilizeHitRatio"
  desc: "农场施肥暴击概率"
  param {
    desc: "概率[0,100]"
  }
}
rows {
  id: 174
  handler: "GmDepositNewEvent"
  desc: "触发部分事件"
  param {
    desc: "事件id,8:完成战斗，53:开始战斗, 51:完成关卡"
    default: "8"
  }
  param {
    desc: "触发事件次数"
    default: "1"
  }
}
rows {
  id: 175
  handler: "GmReturnActivityStart"
  desc: "回流开启"
  param {
    desc: "流失天数: 7  14  28"
    default: "28"
  }
  param {
    desc: "回流分档: 0:7天档  1:14天档  2:28天档"
    default: "2"
  }
  param {
    desc: "回流大版本: 1  2  3"
    default: "3"
  }
  param {
    desc: "玩家活跃度（默认-1不指定活跃度）：0 1 2"
    default: "-1"
  }
  param {
    desc: "玩家推荐玩法;分割"
    default: "104;105"
  }
}
rows {
  id: 176
  handler: "GmReturnActivityStop"
  desc: "回流关闭"
  param {
    desc: "上次回流数据: 0:保留  1:清理"
    default: "0"
  }
}
rows {
  id: 177
  handler: "GMFarmSetStealCount"
  desc: "农场设置今日偷菜次数"
  param {
    desc: "今天偷菜次数"
  }
}
rows {
  id: 178
  handler: "GMFarmSetBeFertilizedCount"
  desc: "农场设置今日被施肥次数"
  param {
    desc: "今天被施肥次数"
  }
}
rows {
  id: 179
  handler: "GMMusicOrderActivity"
  desc: "半周年手账活动"
  param {
    desc: "功能- 1:每日刷新 2:刷新订单 3:清除当日音符掉落 4:清除活动数据 5:触发玩法模式音符掉落 6:完成订单 7:重置订单 8:刷新重置次数 9:通过邮件发送所有奖励"
  }
  param {
    desc: "功能参数- 1:day 2:day 5:modeId 6:orderId 7:orderId 8:day"
  }
}
rows {
  id: 180
  handler: "GmSetIAAGrant"
  desc: "设置IAA强制开启"
  param {
    desc: "1开启0是不开启"
    default: "1"
  }
}
rows {
  id: 181
  handler: "GmTestCondition"
  desc: "快速测试条件"
  param {
    desc: "1注册条件, 2打印条件, 3清理条件"
    default: "1"
  }
  param {
    desc: "条件组1（示例：玩一局娱乐模式 -> 4-1;127-3）"
  }
  param {
    desc: "条件组2（条件组 -> 条件1*条件2）"
  }
  param {
    desc: "条件组3（条件 -> 主条件;子条件）"
  }
  param {
    desc: "条件组4（主条件 -> 主条件id-主条件参数）"
  }
  param {
    desc: "条件组5（子条件 -> 子条件id-子条件参数1,子条件参数2,子条件参数3）"
  }
}
rows {
  id: 182
  handler: "GmClubAddHeat"
  desc: "增加自身的社团活跃度（无法超过日上线）"
  param {
    desc: "参数1/2/3"
    default: "2"
  }
  param {
    desc: "占位参数"
    default: "0"
  }
}
rows {
  id: 183
  handler: "GMActivityClubChallenge"
  desc: "社团挑战活动gm指令"
  param {
    desc: "模式"
    default: "1"
  }
  param {
    desc: "参数1"
  }
  param {
    desc: "参数2"
  }
  param {
    desc: "参数3"
  }
}
rows {
  id: 184
  handler: "GmClubModifyGeo"
  desc: "社团编辑GEO信息"
  param {
    desc: "Province(上海为310000)"
    default: "310000"
  }
  param {
    desc: "City(上海徐汇310104)"
    default: "310104"
  }
  param {
    desc: "Town"
    default: "0"
  }
}
rows {
  id: 185
  handler: "GmSetUgcSingleDsInfo"
  desc: "UGC单人对局GM指令"
  param {
    desc: "CPU负载值(有效值为0-100, 默认为-1表示不设置)"
    default: "-1"
  }
  param {
    desc: "对局数(默认-1表示不设置)"
    default: "-1"
  }
  param {
    desc: "生效持续时间秒为单位(默认600s)"
    default: "600"
  }
}
rows {
  id: 186
  handler: "GmSnsGetRelation"
  desc: "Sns获取社交关系"
  param {
    desc: "uid"
    default: "0"
  }
  param {
    desc: "关系类型"
    default: "1;2;3;4;5"
  }
  param {
    desc: "条数"
    default: "0"
  }
  param {
    desc: "排序"
    default: "1;3;4"
  }
  param {
    desc: "降序"
    default: "true;true;false"
  }
}
rows {
  id: 187
  handler: "GmSnsGetInteractiveInfo"
  desc: "Sns获取交互数据"
  param {
    desc: "uid"
    default: "0"
  }
  param {
    desc: "Key(;分割)"
  }
}
rows {
  id: 188
  handler: "GmSnsSetInteractiveInfo"
  desc: "Sns设置交互数据"
  param {
    desc: "uid"
    default: "0"
  }
  param {
    desc: "Key(;分割)"
  }
  param {
    desc: "Value(;分割)"
  }
  param {
    desc: "Value类型(;分割, 0-数字 1-字符串)"
  }
  param {
    desc: "操作符(;分割)"
  }
}
rows {
  id: 189
  handler: "GMWolfKillAddRoleInfoPoints"
  desc: "增加狼人杀精炼点数"
  param {
    desc: "角色类型"
  }
  param {
    desc: "增加点数"
  }
}
rows {
  id: 190
  handler: "GmSendBroadcastNotice"
  desc: "发送广播notice"
  param {
    desc: "广播内容模板id"
    default: "1001"
  }
  param {
    desc: "内容参数列表"
    default: "1,2"
  }
  param {
    desc: "广播类型（见：enum BroadcastType {\n  BT_None = 0;\n  BT_Zone = 1; // 节点\n  BT_World = 2; // 集群\n  BT_Region = 3; // 大区\n  BT_Lobby = 4; // 广场\n  BT_Ds= 5; // 局内\n}"
  }
  param {
    desc: "是否可打断"
    default: "0"
  }
  param {
    desc: "是否排队"
    default: "0"
  }
}
rows {
  id: 191
  handler: "GmAnimalHandbookActivity"
  desc: "动物图鉴活动"
  param {
    desc: "功能- 1:完成诱捕 2:领取赠送图鉴 3:添加动物图鉴 4:生成赠送图鉴"
  }
  param {
    desc: "功能参数- 1:聚集地id 2:赠送id 3: 动物id 4:图鉴uniqueId"
  }
}
rows {
  id: 192
  handler: "GmMonopolyActivity"
  desc: "大富翁活动"
  param {
    desc: "活动ID"
  }
  param {
    desc: "参数1"
  }
  param {
    desc: "参数2"
  }
  param {
    desc: "参数3"
  }
  param {
    desc: "参数4"
  }
}
rows {
  id: 193
  handler: "GMFarmAddFishRecord"
  desc: "农场添加鱼记录"
  param {
    desc: "鱼id"
    default: "5001"
  }
  param {
    desc: "鱼的分数"
    default: "1"
  }
}
rows {
  id: 194
  handler: "GmFarmAddBuff"
  desc: "添加农场Buff"
  param {
    desc: "buffId"
  }
}
rows {
  id: 195
  handler: "GmFarmClearBuff"
  desc: "清空农场Buff"
}
rows {
  id: 196
  handler: "GmCompleteEntertainmentGuideTask"
  desc: "完成娱乐向导任务"
  param {
    desc: "任务ID（0-清空数据）"
    default: "100"
  }
}
rows {
  id: 197
  handler: "GMAssistSportsman"
  desc: "特训营助力运动员"
  param {
    desc: "助力人短uid"
    default: "0"
  }
  param {
    desc: "活动id"
    default: "0"
  }
  param {
    desc: "运动员id"
    default: "1"
  }
}
rows {
  id: 198
  handler: "GmArenaUnlockCard"
  desc: "Arena解锁卡牌"
  param {
    desc: "卡牌ID"
    default: "0"
  }
  param {
    desc: "解锁所有卡牌"
    default: "0"
  }
}
rows {
  id: 199
  handler: "GMArenaAddHero"
  desc: "Arena英雄解锁/加锁"
  param {
    desc: "英雄ID(加锁需输入英雄id)"
    default: "0"
  }
  param {
    desc: "是否所有英雄"
    default: "1"
  }
  param {
    desc: "解锁0/加锁1"
    default: "0"
  }
  param {
    desc: "时长(单位：秒)"
    default: "0"
  }
  param {
    desc: "是否解锁英雄所有皮肤：0否/1是"
    default: "1"
  }
  param {
    desc: "是否显示英雄获取弹窗"
    default: "0"
  }
  param {
    desc: "增加局内选人时长(秒)"
    default: "0"
  }
  param {
    desc: "是否解锁英雄所有专武皮肤：0否/1是"
    default: "0"
  }
}
rows {
  id: 200
  handler: "GMArenaAddEquip"
  desc: "Arena解锁武器"
  param {
    desc: "英雄ID"
    default: "0"
  }
  param {
    desc: "武器ID"
    default: "0"
  }
}
rows {
  id: 201
  handler: "GmActiveWishSelfZhuliCnt"
  desc: "设置心愿活动参数"
  param {
    desc: "活动ID"
  }
  param {
    desc: "心愿值"
  }
  param {
    desc: "已助力次数"
  }
  param {
    desc: "是否清空心愿礼物领取状态"
  }
  param {
    desc: "是否清空为好友助力状态"
  }
}
rows {
  id: 202
  handler: "GMClearGiftTodayHistory"
  desc: "清空农场送礼记录"
}
rows {
  id: 203
  handler: "GmFarmSelectFish"
  desc: "农场指定钓鱼"
  param {
    desc: "鱼id"
    default: "0"
  }
}
rows {
  id: 204
  handler: "GmAttrTest"
  desc: "属性系统测试指令"
}
rows {
  id: 205
  handler: "GmFarmAddAllFish"
  desc: "农场背包添加所有鱼"
}
rows {
  id: 206
  handler: "GmFarmBatchFishing"
  desc: "农场批量钓鱼"
  param {
    desc: "层数"
    default: "1"
  }
  param {
    desc: "鱼饵id"
    default: "6003"
  }
  param {
    desc: "养鱼周期"
    default: "43200"
  }
  param {
    desc: "钓鱼次数"
    default: "20"
  }
}
rows {
  id: 207
  handler: "GmAddCups"
  desc: "增加奖杯"
  param {
    desc: "奖杯数"
    default: "0"
  }
}
rows {
  id: 208
  handler: "GmClubWeekSettle"
  desc: "社团周结算"
  param {
    desc: "0-清理缓存，1-模拟结算"
    default: "0"
  }
  param {
    desc: "总榜排名（模拟前先清理）"
    default: "1"
  }
}
rows {
  id: 209
  handler: "GmLevelSim"
  desc: "关卡随机模拟"
  param {
    desc: "玩法模式ID"
    default: "4"
  }
  param {
    desc: "人数阵营（每个阵营人数:第一轮阵营数,第二轮阵营数,...）"
  }
  param {
    desc: "模拟真人配置(总人数:单局人数)"
    default: "100:1"
  }
  param {
    desc: "对局数"
    default: "100"
  }
  param {
    desc: "段位分/MMR配置（MMR:分数,MMR:分数）"
    default: "0:1000"
  }
  param {
    desc: "版本号（默认为客户端版本号）"
  }
  param {
    desc: "时间（默认为当前时间，格式为2024-07-17 00:00:00）"
  }
  param {
    desc: "分包类型（0-无 1-基础 2-核心）"
    default: "1"
  }
}
rows {
  id: 210
  handler: "GmClearLuckyFriendAttr"
  desc: "清楚幸运好友任务信息"
}
rows {
  id: 211
  handler: "GmResetThemeAdventure"
  desc: "重置星界奇遇活动任务"
  param {
    desc: "活动ID"
  }
}
rows {
  id: 212
  handler: "SetThemeAdventureDailyTaskProgress"
  desc: "设置星界奇遇每日任务进度"
  param {
    desc: "活动ID"
  }
  param {
    desc: "玩法ID"
    default: "1"
  }
  param {
    desc: "任务进度"
    default: "1"
  }
}
rows {
  id: 213
  handler: "GmSnsFriendsBroadcastTest"
  desc: "sns好友广播测试"
}
rows {
  id: 214
  handler: "GmSendReputationScoreBehaviorMessage"
  desc: "信誉分违规行为消息发送"
  param {
    desc: "对局ID"
  }
  param {
    desc: "违规者UID"
  }
}
rows {
  id: 215
  handler: "GmPlayerCSTest"
  desc: "玩家协议测试"
  param {
    desc: "类型"
  }
  param {
    desc: "参数1"
  }
  param {
    desc: "参数2"
  }
}
rows {
  id: 216
  handler: "GMUnlockAchievement"
  desc: "解锁成就"
  param {
    desc: "成就ID"
    default: "0"
  }
  param {
    desc: "解锁所有成就"
    default: "0"
  }
}
rows {
  id: 217
  handler: "GMLockAchievement"
  desc: "重置成就"
  param {
    desc: "成就ID"
    default: "0"
  }
  param {
    desc: "重置所有成就"
    default: "0"
  }
}
rows {
  id: 218
  handler: "GmWishTreeActivity"
  desc: "许愿树Gm"
  param {
    desc: "活动id"
    default: "900"
  }
  param {
    desc: "子命令"
    default: "1"
  }
  param {
    desc: "参数1"
    default: "0"
  }
  param {
    desc: "参数2"
    default: "0"
  }
  param {
    desc: "参数3"
    default: "0"
  }
  param {
    desc: "参数4"
    default: "0"
  }
  param {
    desc: "参数5"
    default: "0"
  }
}
rows {
  id: 219
  handler: "GMSkipGuideWarmRound"
  desc: "设置首局"
  param {
    desc: "0:开启跳过首局;1:关闭跳过首局; 2:持续开启首局;3:关闭持续首局"
    default: "1"
  }
}
rows {
  id: 220
  handler: "GmFarmBatchAddItems"
  desc: "农场批量加道具"
  param {
    desc: "1101作物产出 1103动物产出 1109作物加工产出 1110动物加工产出 1108小屋家具"
    default: "1101"
  }
  param {
    desc: "数量"
    default: "20"
  }
}
rows {
  id: 221
  handler: "GmFishingHallOfFameActivity"
  desc: "钓鱼名人堂GM"
  param {
    desc: "活动id"
    default: "30009"
  }
  param {
    desc: "子命令"
    default: "0"
  }
  param {
    desc: "参数1"
    default: "0"
  }
  param {
    desc: "参数2"
    default: "0"
  }
  param {
    desc: "参数3"
    default: "0"
  }
  param {
    desc: "参数4"
    default: "0"
  }
}
rows {
  id: 222
  handler: "GmPlayerActivityGeneralGm"
  desc: "活动通用Gm"
  param {
    desc: "活动id"
    default: "1"
  }
  param {
    desc: "子命令"
    default: "1"
  }
  param {
    desc: "参数1"
    default: "0"
  }
  param {
    desc: "参数2"
    default: "0"
  }
  param {
    desc: "参数3"
    default: "0"
  }
  param {
    desc: "参数4"
    default: "0"
  }
  param {
    desc: "参数5"
    default: "0"
  }
}
rows {
  id: 223
  handler: "GmFarmCancelFishStealLimit"
  desc: "解除炸鱼次数限制"
  param {
    desc: "1解除 0取消解除"
    default: "1"
  }
}
rows {
  id: 224
  handler: "GmFarmPet"
  desc: "农场宠物指令"
  param {
    desc: "类型 1设置饱腹值 2清空所有宠物数据 3生成社交面板数据 4设置好感度 5强制送礼 6调试信息开关 7设置野猫好感度 8强制野猫离开 9强制猫捕鱼 10获得宠物 11强制刷野猫 12打印野猫刷新时间 13打印家猫捕鱼时间"
    default: "1"
  }
  param {
    desc: "参数"
    default: "0"
  }
  param {
    desc: "类别【0狗1猫】"
    default: "0"
  }
}
rows {
  id: 225
  handler: "GmUnlockLevelAchievement"
  desc: "解锁所有关卡图鉴"
}
rows {
  id: 226
  handler: "GmCompleteAllAchievement"
  desc: "完成所有成就"
}
rows {
  id: 227
  handler: "GmStartCoSideMatch"
  desc: "开始匹配（码相同的队伍会分到一个阵营），会从第一个发起者开始等待30s"
  param {
    desc: "匹配码（整数）"
    default: "1111"
  }
  param {
    desc: "玩法id"
    default: "9"
  }
}
rows {
  id: 228
  handler: "GmGetServerCdnVersion"
  desc: "查询服务器CDN版本号"
}
rows {
  id: 229
  handler: "GmSetHistoryQualifyingInfo"
  desc: "设置历史赛季段位分"
  param {
    desc: "段位分"
    default: "0"
  }
  param {
    desc: "段位赛季ID，多个用分号分隔"
    default: "0"
  }
}
rows {
  id: 230
  handler: "GmHokAllRobotsFight"
  desc: "HOK都是机器人战斗"
  param {
    desc: "开启的场次"
    default: "1"
  }
}
rows {
  id: 231
  handler: "GMWolfKillGetActionScore"
  desc: "获取狼人杀行动分"
}
rows {
  id: 232
  handler: "GMWolfKillUnlockAllRoleType"
  desc: "狼人杀解锁玩家所有角色"
}
rows {
  id: 233
  handler: "GmActiveDanceOutCnt"
  desc: "设置当前换装"
  param {
    desc: "活动id"
  }
  param {
    desc: "服装id"
  }
}
rows {
  id: 234
  handler: "GmGetResData"
  desc: "根据pbin名字和key，返回服务器数据"
  param {
    desc: "Pbin文件名"
  }
  param {
    desc: "参数1"
  }
  param {
    desc: "参数2"
  }
  param {
    desc: "参数3"
  }
}
rows {
  id: 235
  handler: "GmRankSnapshotProgress"
  desc: "榜单结算进度"
  param {
    desc: "类型（1:社团 2:闪电赛 3:兽人无尽 4:MOBA3V3 5:MOBA5V5 7:其他）"
  }
  param {
    desc: "榜单类型"
    default: "0"
  }
}
rows {
  id: 236
  handler: "GmRankSnapshotRemove"
  desc: "榜单结算清理"
  param {
    desc: "类型（1:社团 2:闪电赛 3:兽人无尽 4:MOBA3V3 5:MOBA5V5 7:其他）"
  }
  param {
    desc: "榜单类型"
    default: "0"
  }
}
rows {
  id: 237
  handler: "GmReturnActivitySetActiveLevel"
  desc: "设置回流玩家活跃度"
  param {
    desc: "活跃度（0-低、1-中、2-高）"
    default: "0"
  }
}
rows {
  id: 238
  handler: "GmTest"
  desc: "GM测试"
  param {
    desc: "GM名称"
    default: "GmGetTaskInfo"
  }
  param {
    desc: "参数1"
  }
  param {
    desc: "参数2"
  }
  param {
    desc: "参数3"
  }
  param {
    desc: "参数3"
  }
  param {
    desc: "参数4"
  }
  param {
    desc: "参数5"
  }
}
rows {
  id: 239
  handler: "GmFarmAddSceneDrop"
  desc: "添加农场场景掉落（NpcEvt形式）"
  param {
    desc: "系列"
  }
  param {
    desc: "事件ID"
  }
  param {
    desc: "掉落场所（1农场 2小屋）"
  }
  param {
    desc: "位置"
  }
  param {
    desc: "过期时间"
  }
}
rows {
  id: 240
  handler: "GmFarmAddCollection"
  desc: "添加农场收藏品"
  param {
    desc: "收藏品ID"
  }
}
rows {
  id: 400
  handler: "GmFarmClearAllEvent"
  desc: "清除所有事件和历史记录（消除CD）"
}
rows {
  id: 401
  handler: "GmFarmDispatchEvent"
  desc: "触发一个事件"
  param {
    desc: "触发类型（比如钓鱼=1，浇水=2）"
  }
  param {
    desc: "系列ID"
  }
  param {
    desc: "事件ID"
  }
}
rows {
  id: 402
  handler: "GmFarmActiveTrigger"
  desc: "执行事件触发器"
  param {
    desc: "类型（比如钓鱼=1，浇水=2）"
  }
}
rows {
  id: 403
  handler: "GmUpdatePlayerPubCount"
  desc: "修改玩家发布地图数量"
  param {
    desc: "creatorId"
  }
  param {
    desc: "发布地图数量"
  }
}
rows {
  id: 404
  handler: "GmGetTaskInfo"
  desc: "获取任务信息"
  param {
    desc: "任务id"
  }
}
rows {
  id: 405
  handler: "GmFarmWeatherCmd"
  desc: "天气指令"
  param {
    desc: "1开启 0关闭"
    default: "1"
  }
  param {
    desc: "天气ID"
    default: "1"
  }
  param {
    desc: "参数[时间]"
    default: "0"
  }
}
rows {
  id: 406
  handler: "GmFarmEventDetails"
  desc: "事件详情"
}
rows {
  id: 408
  handler: "GmEventForceDispatch"
  desc: "事件触发概率到100%"
}
rows {
  id: 409
  handler: "GmOutputControlClearType"
  desc: "清除产出控制计数"
  param {
    desc: "类型(0:全部;2:任务;3:活动;6:商城;7:道具)"
    default: "0"
  }
}
rows {
  id: 410
  handler: "GMArenaModifyHeroCe"
  desc: "英雄战力值修改"
  param {
    desc: "英雄ID"
  }
  param {
    desc: "玩法分组（1：3v3）"
  }
  param {
    desc: "活跃系数变化（例如增加0.1，填10）"
    default: "0"
  }
  param {
    desc: "对局分变化"
  }
  param {
    desc: "表现分变化"
  }
}
rows {
  id: 411
  handler: "GMWolfKillAddActionScore"
  desc: "加狼人杀行动分"
  param {
    desc: "分数"
  }
  param {
    desc: "原因"
  }
}
rows {
  id: 412
  handler: "GMWolfKillSubActionScore"
  desc: "减狼人杀行动分"
  param {
    desc: "分数"
  }
  param {
    desc: "原因"
  }
}
rows {
  id: 413
  handler: "GMWolfKillAddReputationScore"
  desc: "加狼人杀信誉分"
  param {
    desc: "分数"
  }
  param {
    desc: "原因"
  }
}
rows {
  id: 414
  handler: "GMWolfKillSubReputationScore"
  desc: "减狼人杀信誉分"
  param {
    desc: "分数"
  }
  param {
    desc: "原因"
  }
}
rows {
  id: 415
  handler: "GmPlayerPersonalityState"
  desc: "玩家心情"
  param {
    desc: "参数1"
    default: "1"
  }
  param {
    desc: "参数2"
    default: "0"
  }
  param {
    desc: "参数3"
    default: "0"
  }
  param {
    desc: "参数4"
    default: "0"
  }
  param {
    desc: "参数5"
    default: "0"
  }
}
rows {
  id: 416
  handler: "GmDeleteUgcRepeatedName"
  desc: "删除UGC地图重复名字"
  param {
    desc: "名字"
  }
}
rows {
  id: 417
  handler: "GMArenaCeRank"
  desc: "英雄战力排行榜结算"
  param {
    desc: "赛季id"
  }
  param {
    desc: "玩法分组（1：3v3）"
  }
  param {
    desc: "强制结算（1：不检查结算时间）"
  }
}
rows {
  id: 418
  handler: "GmBookOfFriendsExpense"
  desc: "花房活动召回助力"
  param {
    desc: "活动id"
  }
  param {
    desc: "助力用户类型（1: 活跃用户; 2: 新进用户）"
  }
  param {
    desc: "发起招募者用户uid"
  }
}
rows {
  id: 419
  handler: "GmBookOfFriendsFarmGift"
  desc: "花房活动农场赠礼"
  param {
    desc: "被赠礼者uid"
  }
  param {
    desc: "用户类型(默认无需填写, 测试场景下填写。1: 活跃用户; 2: 流失用户)"
  }
}
rows {
  id: 420
  handler: "GmClubResetLabels"
  desc: "GM社团标签重置"
  param {
    desc: "社团id"
  }
}
rows {
  id: 421
  handler: "GMFarmTaskCmd"
  desc: "农场任务指令"
  param {
    desc: "1结束当前运行中的任务 2强制刷出下一个任务 3强制刷回上一个任务 4刷新到指定任务"
  }
  param {
    desc: "任务id(只有在刷新指定任务时生效)"
  }
}
rows {
  id: 422
  handler: "GMFarmBagCmd"
  desc: "农场背包指令"
  param {
    desc: "1清空特定物品 2清空背包"
  }
  param {
    desc: "物品ID"
  }
}
rows {
  id: 423
  handler: "GMFarmVileCmd"
  desc: "农场霸王花指令"
  param {
    desc: "1增加霸王花经验 2设置霸王花吞食期前 3设置霸王花成熟期前 4设置其他作物成熟前 5设置其他作物依次成熟"
    default: "2"
  }
  param {
    desc: "参数[经验/时间]"
    default: "10"
  }
}
rows {
  id: 424
  handler: "GmFarmVillager"
  desc: "农场村民指令"
  param {
    desc: "指令-： 1刷码头村民 2送码头村民 3加村民 4删村民 5设置好感度 6设置今日收礼次数 7直接设置村民礼物（参数0日常-1满好感度-2按需-3节日） 8删节日礼物数据 10设置日常礼物村民钱包（参数填钱包数） \n11设置按需礼物钱包 12跳过日常礼物刷新时间和概率判断 13跳过按需礼物刷新时间和概率判断 14设置村民今日送礼总次数 15设置村民入驻满几个小时 16跳过村民入驻8小时才能送日常礼限制"
    default: "2"
  }
  param {
    desc: "村民ID（指令1、2、11、12、13、14、16不填）"
    default: "0"
  }
  param {
    desc: "参数（指令1、2、12、13、14、16不填）"
    default: "0"
  }
  param {
    desc: "送礼礼物道具ID（指令7填）"
    default: "1001"
  }
  param {
    desc: "送礼礼物道具数量（指令7填）"
    default: "1"
  }
  param {
    desc: "节日礼物ID（指令7设置节日礼物填）"
  }
}
rows {
  id: 425
  handler: "GmFarmCollectionClearAll"
  desc: "农场一键清空收藏品"
}
rows {
  id: 426
  handler: "GMWolfPlayerTotalEventCmd"
  desc: "狼人新人礼对局数"
  param {
    desc: "对局数"
    default: "1"
  }
}
rows {
  id: 427
  handler: "GmConanActivityClearAll"
  desc: "柯南集卡活动一键清空数据"
}
rows {
  id: 428
  handler: "GmConanActivityGetAnimals"
  desc: "柯南集卡活动多次抽取徽章"
  param {
    desc: "抽取聚集地"
  }
  param {
    desc: "抽取次数(最大1000次)"
    default: "0"
  }
  param {
    desc: "是否开启保底功能"
  }
}
rows {
  id: 429
  handler: "GmPushScenePackage"
  desc: "推送场景礼包"
  param {
    desc: "推送场景礼包ID"
    default: "0"
  }
}
rows {
  id: 430
  handler: "GMWolfKillOpenComeBack"
  desc: "开启狼人杀回归系统"
}
rows {
  id: 431
  handler: "GMDiscoverGuideCmd"
  desc: "发现系统引导"
  param {
    desc: "0清除数据,>0使用存储引导配置"
    default: "0"
  }
  param {
    desc: ">=0对参数0配置id设置redis引导人数"
    default: "-1"
  }
  param {
    desc: "0关闭gm配置判断日志,1开启gm配置判断日志"
    default: "0"
  }
}
rows {
  id: 432
  handler: "GMArenaUnlockHero"
  desc: "Arena英雄解锁/加锁(纯净版)"
  param {
    desc: "英雄ID(加锁需输入英雄id)"
    default: "0"
  }
  param {
    desc: "是否所有英雄"
    default: "1"
  }
  param {
    desc: "解锁0/加锁1"
    default: "0"
  }
  param {
    desc: "时长(单位：秒)"
    default: "0"
  }
  param {
    desc: "是否解锁英雄所有皮肤：0否/1是"
    default: "1"
  }
  param {
    desc: "是否显示英雄获取弹窗"
    default: "0"
  }
  param {
    desc: "增加局内选人时长(秒)"
    default: "0"
  }
}
rows {
  id: 433
  handler: "GMFarmTalentCmd"
  desc: "农场神农宝典指令"
  param {
    desc: "1解锁全部天赋"
    default: "1"
  }
}
rows {
  id: 434
  handler: "GmAigcNpcReset"
  desc: "AI伙伴重置"
  param {
    desc: "参数（1: 重置到初始状态   2: 重置定制时间  3: 重置个人信息时间"
    default: "1"
  }
}
rows {
  id: 435
  handler: "GmAigcNpcPalMood"
  desc: "AI伙伴心情值修改"
  param {
    desc: "心情值变化量"
    default: "0"
  }
}
rows {
  id: 436
  handler: "GmTradingCardAddCard"
  desc: "卡牌系统增加卡牌"
  param {
    desc: "卡集id"
  }
  param {
    desc: "卡牌列表（卡牌id:卡牌数量）多个用;分隔"
  }
}
rows {
  id: 437
  handler: "GmTradingCardClearCD"
  desc: "卡牌系统清除cd"
  param {
    desc: "cd类型(0:全部 1:赠送次数 2:索要cd)"
    default: "0"
  }
}
rows {
  id: 438
  handler: "GmRandEventSim"
  desc: "随机事件模拟"
  param {
    desc: "玩法模式ID"
    default: "0"
  }
  param {
    desc: "关卡ID"
    default: "0"
  }
  param {
    desc: "对局数"
    default: "100"
  }
  param {
    desc: "时间（默认为当前时间，格式为2024-12-09 00:00:00）"
  }
}
rows {
  id: 439
  handler: "GmWolfKillResetTreasure"
  desc: "狼人杀重置珍宝系统"
}
rows {
  id: 440
  handler: "GMFashionFundActivityCmd"
  desc: "时装基金活动"
  param {
    desc: "活动id"
    default: "30189"
  }
  param {
    desc: "领奖类型0领取购买时间内返还;1领取购买时间结束返还"
    default: "0"
  }
}
rows {
  id: 441
  handler: "GMMaydayRandomEndlessModeSaveData"
  desc: "mayday无尽模式随机玩家存档(有存档则跳过)"
}
rows {
  id: 442
  handler: "GmRefreshHotSpring"
  desc: "刷新我的温泉"
}
rows {
  id: 443
  handler: "GmReobtainBuff"
  desc: "变成可以重新获取温泉buff的状态"
}
rows {
  id: 444
  handler: "GmResetHotSpring"
  desc: "重置温泉"
}
rows {
  id: 445
  handler: "GMWolfKillResetComeBack"
  desc: "狼人杀重置回归系统"
}
rows {
  id: 446
  handler: "GMWolfKillComeBackCrossDay"
  desc: "狼人杀回归系统跨天"
}
rows {
  id: 447
  handler: "GmFarmGodFigure"
  desc: "农场神像"
  param {
    desc: "1:重置许愿次数, 2:设定下一次大吉需要的许愿次数"
    default: "1"
  }
  param {
    desc: "下一次大吉需要的许愿次数"
    default: "1"
  }
}
rows {
  id: 448
  handler: "GmFarmAddMagic"
  desc: "农场仙术增加"
  param {
    desc: "仙术ID"
  }
  param {
    desc: "过期时间"
  }
}
rows {
  id: 449
  handler: "GmFarmDelMagic"
  desc: "农场仙术删除"
  param {
    desc: "仙术ID"
  }
}
rows {
  id: 450
  handler: "GmFarmAddMagicSkillMp"
  desc: "农场仙术Mp增加"
  param {
    desc: "仙术ID"
  }
  param {
    desc: "增加Mp"
  }
}
rows {
  id: 451
  handler: "GmFarmUseMagic"
  desc: "农场仙术使用"
  param {
    desc: "仙术ID"
  }
  param {
    desc: "使用对象"
  }
  param {
    desc: "参数"
  }
}
rows {
  id: 452
  handler: "GmFarmEquipMagic"
  desc: "农场仙术装备"
  param {
    desc: "仙术ID"
  }
  param {
    desc: "是否装备 0 卸下 1装备"
  }
}
rows {
  id: 453
  handler: "GmScoreGuideTrigger"
  desc: "评分引导触发对应条件弹窗"
  param {
    desc: "活动Id"
  }
  param {
    desc: "条件Id"
  }
}
rows {
  id: 454
  handler: "GmScoreGuideClearCd"
  desc: "评分引导清除弹窗冷却时间"
  param {
    desc: "活动Id"
  }
}
rows {
  id: 455
  handler: "GmScoreGuideClearTimes"
  desc: "评分引导清除弹窗触发次数"
  param {
    desc: "活动Id"
  }
}
rows {
  id: 456
  handler: "GmScoreGuideSetCoreUser"
  desc: "评分引导直接满足核心用户条件"
  param {
    desc: "活动Id"
  }
}
rows {
  id: 457
  handler: "GmFarmHotSpringRefresh"
  desc: "定制刷新温泉"
  param {
    desc: "温泉类型（typeId）"
    default: "1"
  }
  param {
    desc: "温泉品质（0普1金2彩）"
    default: "0"
  }
  param {
    desc: "Buff组（0,1,2,3……）"
    default: "0"
  }
}
rows {
  id: 458
  handler: "GmScoreGuideResetStatus"
  desc: "评分引导重置活动状态"
  param {
    desc: "活动Id"
  }
}
rows {
  id: 459
  handler: "GmSwitchStreamPlayStatus"
  desc: "修改直播状态"
  param {
    desc: "0:微信视频号, 1:抖音"
    default: "1"
  }
  param {
    desc: "0:关播, 1:开播"
    default: "1"
  }
  param {
    desc: "玩法id"
    default: "1"
  }
}
rows {
  id: 460
  handler: "GmNotifyStreamInteractionCmd"
  desc: "直播交互指令"
  param {
    desc: "特效id"
    default: "101"
  }
  param {
    desc: "触发次数"
    default: "1"
  }
  param {
    desc: "观众HeadUrl"
  }
  param {
    desc: "观众昵称"
  }
}
rows {
  id: 461
  handler: "GmSetUgcPlayerPublicAttr"
  desc: "设置玩家互动数值"
  param {
    desc: "地图ID"
  }
  param {
    desc: "属性名"
  }
  param {
    desc: "属性值"
    default: "0"
  }
  param {
    desc: "属性下限"
    default: "0"
  }
  param {
    desc: "属性上限"
    default: "999999"
  }
}
rows {
  id: 462
  handler: "GmSetDsReplayRecordCount"
  desc: "设置玩家DS录像局数"
  param {
    desc: "局数"
  }
}
rows {
  id: 463
  handler: "GmSeasonReviewCreateDataCmd"
  desc: "增加赛季回顾数据"
  param {
    desc: "赛季id(id小于当前赛季)"
  }
}
rows {
  id: 464
  handler: "GmSetEnterFarmTime"
  desc: "设置最近进入农场时间"
  param {
    desc: "时间(Y-m-d H:i:s)"
  }
}
rows {
  id: 465
  handler: "GmExchangeRoleData"
  desc: "交换ios&android帐号"
}
rows {
  id: 466
  handler: "GmChaseIdentityMastery"
  desc: "设置大王身份专精"
  param {
    desc: "身份ID"
  }
  param {
    desc: "场次分"
  }
  param {
    desc: "表现分"
  }
  param {
    desc: "活跃系数"
  }
}
rows {
  id: 467
  handler: "GmFarmKirinAddCollectFarmExp"
  desc: "仙麒麟增加采集的收获经验"
  param {
    desc: "经验值"
  }
}
rows {
  id: 468
  handler: "GmFarmKirinAddMana"
  desc: "仙麒麟增加仙力"
  param {
    desc: "仙力"
  }
}
rows {
  id: 469
  handler: "GmFarmKirinAdjustLevel"
  desc: "仙麒麟等级调整"
  param {
    desc: "调整等级"
  }
}
rows {
  id: 470
  handler: "GmFarmKirinIncubate"
  desc: "仙麒麟孵化"
}
rows {
  id: 471
  handler: "GMFarmRedRipe"
  desc: "小红狐立即成熟"
}
rows {
  id: 472
  handler: "GMFarmShowRedCD"
  desc: "显示小红狐可偷CD"
}
rows {
  id: 473
  handler: "GMRefreshRedFarm"
  desc: "小红狐立即播种"
}
rows {
  id: 474
  handler: "GmFarmAquariumResetCdClear"
  desc: "农场水族箱重置cd清除"
}
rows {
  id: 475
  handler: "GmSetIdentityRankItem"
  desc: "设置大王专精榜单"
  param {
    desc: "身份ID"
  }
  param {
    desc: "1设置上周徽章 2设置上周排行榜 3设置历史排行榜"
  }
  param {
    desc: "上周徽章id"
  }
  param {
    desc: "行政编码"
  }
  param {
    desc: "排名"
  }
  param {
    desc: "分数"
  }
}
rows {
  id: 476
  handler: "GmSetChaseIdentityGmInfo"
  desc: "打开大王专精结算详情"
  param {
    desc: "1 打开"
    default: "1"
  }
}
rows {
  id: 477
  handler: "GmSetMatchABTestSwitch"
  desc: "设置强制启用玩法AB实验开关"
  param {
    desc: "玩法实验Id"
  }
  param {
    desc: "强制启用时机（1.匹配前 2.匹配后）"
  }
  param {
    desc: "是否启用（0.关闭 1.启用）"
  }
  param {
    desc: "生效时间（秒）"
    default: "3600"
  }
}
rows {
  id: 478
  handler: "GmScoreGuideSetCoreUser"
  desc: "评分引导模拟算法数据前置完成"
  param {
    desc: "活动Id"
  }
}
rows {
  id: 479
  handler: "GmGetArenaStat"
  desc: "Arena统计查询"
  param {
    desc: "赛季Id"
    default: "10"
  }
  param {
    desc: "匹配类型(与匹配类型无关的填 0)"
    default: "6101"
  }
  param {
    desc: "统计类型 ArenaStatType 类型"
    default: "1"
  }
}
rows {
  id: 480
  handler: "GmUseBackUpPlatFriend"
  desc: "不更新平台好友，使用备份的"
  param {
    desc: "1 不更新 其他 更新"
    default: "1"
  }
}
rows {
  id: 481
  handler: "GmShowFarmEvent"
  desc: "显示农场事件CD和保底计数"
  param {
    desc: "系列"
  }
  param {
    desc: "事件ID"
  }
}
rows {
  id: 482
  handler: "GmClearShowFarmEventCDAndCount"
  desc: "清除农场某事件CD和保底"
  param {
    desc: "系列"
  }
  param {
    desc: "事件ID"
  }
}
rows {
  id: 483
  handler: "GmShowAttr"
  desc: "显示某些数据"
  param {
    desc: "数据编号\n1:被盗礼物CD"
    default: "1"
  }
}
rows {
  id: 484
  handler: "GMConanIpActiveActivityCmd"
  desc: "柯南二期广场活跃"
  param {
    desc: "activityId"
    default: "30315"
  }
  param {
    desc: "0:加buff,>0:领取config奖励"
    default: "0"
  }
}
rows {
  id: 485
  handler: "GMRoomAddRobot"
  desc: "一键添加房间机器人"
  param {
    desc: "ROOMID"
  }
  param {
    desc: "机器人阵营参数（狼人:中立场数.比如1:3;2:4;3:2）"
  }
  param {
    desc: "机器人地图参数，可为空。人数少补0多的抛弃（地图:场数比如50301:3,50302:1;50302:1,50302:1）"
  }
}
rows {
  id: 486
  handler: "GmSetBattleScriptId"
  desc: "设置对局剧本Id值"
  param {
    desc: "剧本Id值(负值关闭GM)"
    default: "-1"
  }
}
rows {
  id: 487
  handler: "GMArenaModifyHeroStar"
  desc: "主目标系统修改英雄奖章"
  param {
    desc: "英雄ID"
  }
  param {
    desc: "奖章变化值"
  }
  param {
    desc: "设置突破任务完成状态（已废弃）"
    default: "1"
  }
}
rows {
  id: 488
  handler: "GmNr3e8TaskRefreshAllTask"
  desc: "大富翁刷新所有任务"
  param {
    desc: "任务1;任务2;任务3（留空则随机刷新）"
  }
}
rows {
  id: 489
  handler: "GmNr3e8TaskCompleteAllTask"
  desc: "大富翁完成当日所有任务"
}
rows {
  id: 490
  handler: "GmNr3e8TaskClearAllWeekActivity"
  desc: "大富翁清除本周活跃度"
}
rows {
  id: 491
  handler: "GmTestEvent"
  desc: "测试事件"
  param {
    desc: "事件EventTypeId"
  }
}
rows {
  id: 492
  handler: "GmChangeGameModeReturnState"
  desc: "修改玩法回流状态"
  param {
    desc: "玩法回流ID"
    default: "1"
  }
  param {
    desc: "玩法回流状态(1,满足触发条件,2已开启)"
    default: "1"
  }
}
rows {
  id: 493
  handler: "GmUnlockGameModeReturnStep"
  desc: "玩法回流阶段解锁"
  param {
    desc: "玩法回流ID"
    default: "1"
  }
  param {
    desc: "阶段ID"
    default: "1"
  }
}
rows {
  id: 494
  handler: "GMSetWolfKillShieldVocationLevel"
  desc: "设置狼人杀小黑屋等级"
  param {
    desc: "等级"
    default: "1"
  }
}
rows {
  id: 495
  handler: "GmOpenCook"
  desc: "农场开启餐厅并删除牛牛"
}
rows {
  id: 496
  handler: "GMFarmCookAddCommentCustomer"
  desc: "农场餐厅生成顾客，必为评价对象"
}
rows {
  id: 497
  handler: "GMFarmCookSendOfflineComment"
  desc: "农场餐厅生成一条评价"
  param {
    desc: "台词id"
    default: "1"
  }
}
rows {
  id: 498
  handler: "GMFarmCookLevelCmd"
  desc: "农场餐厅等级指令"
  param {
    desc: "1增加等级 2增加点赞"
    default: "1"
  }
  param {
    desc: "参数"
    default: "0"
  }
}
rows {
  id: 499
  handler: "GMFarmCookVisitantCmd"
  desc: "农场餐厅贵宾预约指令"
  param {
    desc: "1预约指定组 2取消预约 3立即到达 4删除所有贵宾 5刷新预约列表 6重置偷取冷却 7必定暴击 8添加随机贵宾 9加快到达"
    default: "1"
  }
  param {
    desc: "参数"
    default: "1"
  }
}
rows {
  id: 500
  handler: "GMFarmCookSetCustomerFlow"
  desc: "农场餐厅设定客流量倍率"
  param {
    desc: "客流量倍率"
    default: "1"
  }
}
rows {
  id: 501
  handler: "GMFarmShowHotAttr"
  desc: "农场显示热度数据"
  param {
    desc: "0总热度值 1详细热度值"
  }
}
rows {
  id: 502
  handler: "GMFarmGridLevelUp"
  desc: "农场地格升级"
  param {
    desc: "地格类型(英文分号间隔)\n 1:作物, 2:动物"
    default: "1;2"
  }
  param {
    desc: "目标等级"
    default: "1"
  }
}
rows {
  id: 503
  handler: "GMFarmCookAddSpecifyCustomer"
  desc: "农场餐厅生成指定avatar的顾客"
  param {
    desc: "AvatarId"
  }
}
rows {
  id: 504
  handler: "GMFarmCropMagicCmd"
  desc: "农场养殖物仙术自测"
  param {
    desc: "指令1加速2恢复3缓时"
    default: "1"
  }
  param {
    desc: "类别1作物2动物"
    default: "1"
  }
  param {
    desc: "时间参数"
    default: "3600"
  }
}
rows {
  id: 505
  handler: "GMFarmRedfoxAddCow"
  desc: "小红狐农场立即出现奶牛"
}
rows {
  id: 506
  handler: "GMFarmPartyQueryPagingTest"
  desc: "农场派对查询分页测试"
  param {
    desc: "测试派对数量 0关闭"
  }
}
rows {
  id: 507
  handler: "GMFarmCookAddReply"
  desc: "农场餐厅发送回复到最新的点评"
  param {
    desc: "新增回复数量"
  }
}
rows {
  id: 508
  handler: "GMFarmRemoveFarm"
  desc: "农场从内存删除farm"
}
rows {
  id: 509
  handler: "GMFarmCookCommentReplyRecoverLimit"
  desc: "恢复每天星级评分板回复上限"
}
rows {
  id: 510
  handler: "GMFarmCookCleanRecruitmentMarketRefresh"
  desc: "农场餐厅招聘市场刷新次数清空"
}
rows {
  id: 511
  handler: "GmAlbumLimitUpdate"
  desc: "个人相册图片上限修改"
  param {
    desc: "图片上限>0生效"
    default: "0"
  }
}
rows {
  id: 512
  handler: "GmTransferUserPlat"
  desc: "转移角色平台"
  param {
    desc: "openid"
  }
  param {
    desc: "platid(ios:0,android:1)"
    default: "0"
  }
  param {
    desc: "uid"
  }
  param {
    desc: "指定开始步骤,默认0"
    default: "0"
  }
}
rows {
  id: 513
  handler: "GmFarmRedFertilize"
  desc: "小红狐来祈福送礼"
}
rows {
  id: 514
  handler: "GmAddUgcLayer"
  desc: "填加UGC地图指定场景"
  param {
    desc: "creatorId"
    default: "0"
  }
  param {
    desc: "ugcId"
    default: "0"
  }
  param {
    desc: "layerId"
    default: "1"
  }
  param {
    desc: "layerName"
  }
}
rows {
  id: 515
  handler: "GmAddGeneralRedDot"
  desc: "设置通用红点"
  param {
    desc: "模块类型(1:通用,2:活动)"
    default: "2"
  }
  param {
    desc: "模块id(类型2填活动id)"
  }
  param {
    desc: "红点类型(1:上新红点)"
    default: "1"
  }
  param {
    desc: "红点id"
    default: "0"
  }
}
rows {
  id: 516
  handler: "GmDeleteGeneralRedDot"
  desc: "移除通用红点"
  param {
    desc: "模块类型(1:通用,2:活动)"
    default: "2"
  }
  param {
    desc: "模块id(类型2填活动id)"
  }
  param {
    desc: "红点类型(1:上新红点)"
    default: "1"
  }
  param {
    desc: "红点id"
    default: "0"
  }
}
rows {
  id: 517
  handler: "GMFarmCookModifyCommentScore"
  desc: "农场餐厅修改评分"
  param {
    desc: "评分(精确到小数点后两位)"
  }
}
rows {
  id: 518
  handler: "GmSetWolfKillMonthCardEndTs"
  desc: "设置狼人月卡剩余多少秒"
  param {
    desc: "secs"
    default: "0"
  }
}
rows {
  id: 519
  handler: "GMFarmCookDefineAvatarIdsRecruitmentMarketRefresh"
  desc: "农场餐厅指定皮肤刷招聘市场（仅限测试皮肤）"
  param {
    desc: "id1"
  }
  param {
    desc: "id2"
  }
  param {
    desc: "id3"
  }
  param {
    desc: "id4"
  }
}
rows {
  id: 520
  handler: "GmFarmDailyAwardCreateDataCmd"
  desc: "农场天天领热购开启高级和总数"
  param {
    desc: "活动id"
    default: "30371"
  }
}
rows {
  id: 521
  handler: "GMFarmCookDishAddExp"
  desc: "农场餐厅菜品经验增加"
  param {
    desc: "餐品id"
  }
  param {
    desc: "经验"
  }
}
rows {
  id: 522
  handler: "GMFarmCookSetRecruitmentMarketProtectData"
  desc: "农场餐厅设置招聘市场保底值"
  param {
    desc: "品质"
  }
  param {
    desc: "保底值"
  }
}
rows {
  id: 523
  handler: "GMFarmFinEvent"
  desc: "立即结束一个农场事件"
  param {
    desc: "系列"
  }
}
rows {
  id: 524
  handler: "GmFarmRedFertilizeTimeReset"
  desc: "重置小红狐祈福送礼时间（次数）"
}
rows {
  id: 525
  handler: "GMArenaForwardGMToDS"
  desc: "Arena转发GM到DS"
  param {
    desc: "GM名"
  }
  param {
    desc: "参数1"
  }
  param {
    desc: "参数2"
  }
  param {
    desc: "参数3"
  }
  param {
    desc: "参数4"
  }
  param {
    desc: "参数5"
  }
}
rows {
  id: 526
  handler: "GmClientLogColoring"
  desc: "日志染色"
  param {
    desc: "UID"
  }
  param {
    desc: "染色类型"
  }
  param {
    desc: "stainId"
  }
  param {
    desc: "生效时间"
  }
  param {
    desc: "结束时间"
  }
  param {
    desc: "日志等级"
  }
}
rows {
  id: 527
  handler: "GmGetPlayerGroupIdCmd"
  desc: "玩家包体ID指令"
  param {
    desc: "1 查询x周前的最高游玩ID 2查询x天前所有玩过的ID 3增加xID游玩次数1次 4查询XID总游玩次数"
    default: "1"
  }
  param {
    desc: "参数1对应指令X"
  }
}
rows {
  id: 528
  handler: "GmGameModeReturnAddTlogData"
  desc: "玩法回流添加数据"
  param {
    desc: "matchTypeId:场次:最后一局时间;"
  }
  param {
    desc: "ugc场次:最后一局时间"
  }
  param {
    desc: "总计登录天数"
  }
}
rows {
  id: 529
  handler: "GMFarmCookDefineAttrRecruitmentMarketRefresh"
  desc: "农场餐厅指定属性刷新员工"
  param {
    desc: "职业"
  }
  param {
    desc: "品质"
  }
  param {
    desc: "专属能力"
  }
  param {
    desc: "速度"
  }
  param {
    desc: "魅力"
  }
}
rows {
  id: 530
  handler: "GmSummerFlashMobActivity"
  desc: "快闪季活动"
  param {
    desc: "操作类型(0:ugc排行榜发奖)"
    default: "0"
  }
  param {
    desc: "操作参数(操作类型为ugc排行榜发奖,填写ugcId)"
  }
}
