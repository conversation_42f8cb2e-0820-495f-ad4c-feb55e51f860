com.tencent.wea.xlsRes.table_TaskGroup
excel/xls/B_BP通行证_moba.xlsx sheet:任务组
rows {
  id: 30000
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1726156800
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1726156800
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 30001
  taskIdList: 30002
  groupName: "峡谷BP每日固定任务（S7）"
}
rows {
  id: 30001
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1726156800
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1726156800
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 30003
  taskIdList: 30004
  taskIdList: 30005
  taskIdList: 30006
  groupName: "峡谷BP每日随机任务（S7）"
}
rows {
  id: 30002
  type: TaskType_BP
  beginShowTime {
    seconds: 1726156800
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1726156800
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 30011
  taskIdList: 30012
  taskIdList: 30013
  taskIdList: 30014
  groupName: "峡谷BP第一周任务（S7）"
}
rows {
  id: 30003
  type: TaskType_BP
  beginShowTime {
    seconds: 1726761600
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1726761600
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 30015
  taskIdList: 30016
  taskIdList: 30017
  taskIdList: 30018
  groupName: "峡谷BP第二周任务（S7）"
}
rows {
  id: 30004
  type: TaskType_BP
  beginShowTime {
    seconds: 1727366400
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1727366400
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 30019
  taskIdList: 30020
  taskIdList: 30021
  taskIdList: 30022
  groupName: "峡谷BP第三周任务（S7）"
}
rows {
  id: 30005
  type: TaskType_BP
  beginShowTime {
    seconds: 1727971200
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1727971200
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 30023
  taskIdList: 30024
  taskIdList: 30025
  taskIdList: 30026
  groupName: "峡谷BP第四周任务（S7）"
}
rows {
  id: 30006
  type: TaskType_BP
  beginShowTime {
    seconds: 1728576000
  }
  endShowTime {
    seconds: 1729180799
  }
  beginDoTime {
    seconds: 1728576000
  }
  endDoTime {
    seconds: 1729180799
  }
  taskIdList: 30027
  taskIdList: 30028
  taskIdList: 30029
  taskIdList: 30030
  groupName: "峡谷BP第五周任务（S7）"
}
rows {
  id: 30010
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1729785600
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1729785600
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 30101
  taskIdList: 30102
  groupName: "峡谷BP每日固定任务（S8）"
}
rows {
  id: 30011
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1729785600
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1729785600
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 30103
  taskIdList: 30104
  taskIdList: 30105
  taskIdList: 30106
  groupName: "峡谷BP每日随机任务（S8）"
}
rows {
  id: 30012
  type: TaskType_BP
  beginShowTime {
    seconds: 1729785600
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1729785600
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 30111
  taskIdList: 30112
  taskIdList: 30113
  taskIdList: 30114
  groupName: "峡谷BP第一周任务（S8）"
}
rows {
  id: 30013
  type: TaskType_BP
  beginShowTime {
    seconds: 1730390400
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1730390400
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 30115
  taskIdList: 30116
  taskIdList: 30117
  taskIdList: 30118
  groupName: "峡谷BP第二周任务（S8）"
}
rows {
  id: 30014
  type: TaskType_BP
  beginShowTime {
    seconds: 1730995200
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1730995200
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 30119
  taskIdList: 30120
  taskIdList: 30121
  taskIdList: 30122
  groupName: "峡谷BP第三周任务（S8）"
}
rows {
  id: 30015
  type: TaskType_BP
  beginShowTime {
    seconds: 1731600000
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1731600000
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 30123
  taskIdList: 30124
  taskIdList: 30125
  taskIdList: 30126
  groupName: "峡谷BP第四周任务（S8）"
}
rows {
  id: 30016
  type: TaskType_BP
  beginShowTime {
    seconds: 1732204800
  }
  endShowTime {
    seconds: 1732809599
  }
  beginDoTime {
    seconds: 1732204800
  }
  endDoTime {
    seconds: 1732809599
  }
  taskIdList: 30127
  taskIdList: 30128
  taskIdList: 30129
  taskIdList: 30130
  groupName: "峡谷BP第五周任务（S8）"
}
rows {
  id: 30020
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1732809600
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1732809600
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30201
  taskIdList: 30202
  groupName: "峡谷BP每日固定任务（S9）"
}
rows {
  id: 30021
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1732809600
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1732809600
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30203
  taskIdList: 30204
  taskIdList: 30205
  taskIdList: 30206
  taskIdList: 30207
  groupName: "峡谷BP每日随机任务（S9）"
}
rows {
  id: 30022
  type: TaskType_BP
  beginShowTime {
    seconds: 1732809600
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1732809600
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30211
  taskIdList: 30212
  taskIdList: 30213
  taskIdList: 30214
  groupName: "峡谷BP第一周任务（S9）"
}
rows {
  id: 30023
  type: TaskType_BP
  beginShowTime {
    seconds: 1733414400
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1733414400
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30215
  taskIdList: 30216
  taskIdList: 30217
  taskIdList: 30218
  groupName: "峡谷BP第二周任务（S9）"
}
rows {
  id: 30024
  type: TaskType_BP
  beginShowTime {
    seconds: 1734105600
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1734105600
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30219
  taskIdList: 30220
  taskIdList: 30221
  taskIdList: 30222
  groupName: "峡谷BP第三周任务（S9）"
}
rows {
  id: 30025
  type: TaskType_BP
  beginShowTime {
    seconds: 1734624000
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1734624000
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30223
  taskIdList: 30224
  taskIdList: 30225
  taskIdList: 30226
  groupName: "峡谷BP第四周任务（S9）"
}
rows {
  id: 30026
  type: TaskType_BP
  beginShowTime {
    seconds: 1735228800
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1735228800
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30227
  taskIdList: 30228
  taskIdList: 30229
  taskIdList: 30230
  groupName: "峡谷BP第五周任务（S9）"
}
rows {
  id: 30027
  type: TaskType_BP
  beginShowTime {
    seconds: 1735833600
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1735833600
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30231
  taskIdList: 30232
  taskIdList: 30233
  taskIdList: 30234
  groupName: "峡谷BP第六周任务（S9）"
}
rows {
  id: 30028
  type: TaskType_BP
  beginShowTime {
    seconds: 1736438400
  }
  endShowTime {
    seconds: 1737043199
  }
  beginDoTime {
    seconds: 1736438400
  }
  endDoTime {
    seconds: 1737043199
  }
  taskIdList: 30235
  taskIdList: 30236
  taskIdList: 30237
  taskIdList: 30238
  groupName: "峡谷BP第七周任务（S9）"
}
rows {
  id: 30030
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1734278400
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1734278400
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30301
  groupName: "峡谷BP每日固定任务（S10）"
}
rows {
  id: 30032
  type: TaskType_BP
  beginShowTime {
    seconds: 1734278400
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1734278400
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30311
  taskIdList: 30312
  taskIdList: 30313
  taskIdList: 30314
  groupName: "峡谷BP第一周任务（S10）"
}
rows {
  id: 30033
  type: TaskType_BP
  beginShowTime {
    seconds: 1737648000
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1737648000
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30315
  taskIdList: 30316
  taskIdList: 30317
  taskIdList: 30318
  groupName: "峡谷BP第二周任务（S10）"
}
rows {
  id: 30034
  type: TaskType_BP
  beginShowTime {
    seconds: 1738252800
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1738252800
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30319
  taskIdList: 30320
  taskIdList: 30321
  taskIdList: 30322
  groupName: "峡谷BP第三周任务（S10）"
}
rows {
  id: 30035
  type: TaskType_BP
  beginShowTime {
    seconds: 1738857600
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1738857600
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30323
  taskIdList: 30324
  taskIdList: 30325
  taskIdList: 30326
  groupName: "峡谷BP第四周任务（S10）"
}
rows {
  id: 30036
  type: TaskType_BP
  beginShowTime {
    seconds: 1739462400
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1739462400
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30327
  taskIdList: 30328
  taskIdList: 30329
  taskIdList: 30330
  groupName: "峡谷BP第五周任务（S10）"
}
rows {
  id: 30037
  type: TaskType_BP
  beginShowTime {
    seconds: 1740067200
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1740067200
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30331
  taskIdList: 30332
  taskIdList: 30333
  taskIdList: 30334
  groupName: "峡谷BP第六周任务（S10）"
}
rows {
  id: 30038
  type: TaskType_BP
  beginShowTime {
    seconds: 1740672000
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1740672000
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30335
  taskIdList: 30336
  taskIdList: 30337
  taskIdList: 30338
  groupName: "峡谷BP第七周任务（S10）"
}
rows {
  id: 30039
  type: TaskType_BP
  beginShowTime {
    seconds: 1741276800
  }
  endShowTime {
    seconds: 1741881599
  }
  beginDoTime {
    seconds: 1741276800
  }
  endDoTime {
    seconds: 1741881599
  }
  taskIdList: 30339
  taskIdList: 30340
  taskIdList: 30341
  taskIdList: 30342
  groupName: "峡谷BP第八周任务（S10）"
}
rows {
  id: 30040
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1740326400
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1740326400
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30401
  groupName: "峡谷BP每日固定任务（S11）"
}
rows {
  id: 30042
  type: TaskType_BP
  beginShowTime {
    seconds: 1740326400
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1740326400
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30411
  taskIdList: 30412
  taskIdList: 30413
  taskIdList: 30414
  groupName: "峡谷BP第一周任务（S11）"
}
rows {
  id: 30043
  type: TaskType_BP
  beginShowTime {
    seconds: 1742486400
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1742486400
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30415
  taskIdList: 30416
  taskIdList: 30417
  taskIdList: 30418
  groupName: "峡谷BP第二周任务（S11）"
}
rows {
  id: 30044
  type: TaskType_BP
  beginShowTime {
    seconds: 1743091200
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1743091200
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30419
  taskIdList: 30420
  taskIdList: 30421
  taskIdList: 30422
  groupName: "峡谷BP第三周任务（S11）"
}
rows {
  id: 30045
  type: TaskType_BP
  beginShowTime {
    seconds: 1743696000
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1743696000
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30423
  taskIdList: 30424
  taskIdList: 30425
  taskIdList: 30426
  groupName: "峡谷BP第四周任务（S11）"
}
rows {
  id: 30046
  type: TaskType_BP
  beginShowTime {
    seconds: 1744300800
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1744300800
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30427
  taskIdList: 30428
  taskIdList: 30429
  taskIdList: 30430
  groupName: "峡谷BP第五周任务（S11）"
}
rows {
  id: 30047
  type: TaskType_BP
  beginShowTime {
    seconds: 1744905600
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1744905600
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30431
  taskIdList: 30432
  taskIdList: 30433
  taskIdList: 30434
  groupName: "峡谷BP第六周任务（S11）"
}
rows {
  id: 30048
  type: TaskType_BP
  beginShowTime {
    seconds: 1745510400
  }
  endShowTime {
    seconds: 1746115199
  }
  beginDoTime {
    seconds: 1745510400
  }
  endDoTime {
    seconds: 1746115199
  }
  taskIdList: 30435
  taskIdList: 30436
  taskIdList: 30437
  taskIdList: 30438
  groupName: "峡谷BP第七周任务（S11）"
}
rows {
  id: 30050
  type: TaskType_BPDaily
  beginShowTime {
    seconds: 1746115200
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1746115200
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30501
  groupName: "峡谷BP每日固定任务（S12）"
}
rows {
  id: 30052
  type: TaskType_BP
  beginShowTime {
    seconds: 1746115200
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1746115200
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30511
  taskIdList: 30512
  taskIdList: 30513
  taskIdList: 30514
  groupName: "峡谷BP第一周任务（S12）"
}
rows {
  id: 30053
  type: TaskType_BP
  beginShowTime {
    seconds: 1746720000
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1746720000
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30515
  taskIdList: 30516
  taskIdList: 30517
  taskIdList: 30518
  groupName: "峡谷BP第二周任务（S12）"
}
rows {
  id: 30054
  type: TaskType_BP
  beginShowTime {
    seconds: 1747324800
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1747324800
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30519
  taskIdList: 30520
  taskIdList: 30521
  taskIdList: 30522
  groupName: "峡谷BP第三周任务（S12）"
}
rows {
  id: 30055
  type: TaskType_BP
  beginShowTime {
    seconds: 1747929600
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1747929600
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30523
  taskIdList: 30524
  taskIdList: 30525
  taskIdList: 30526
  groupName: "峡谷BP第四周任务（S12）"
}
rows {
  id: 30056
  type: TaskType_BP
  beginShowTime {
    seconds: 1748534400
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1748534400
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30527
  taskIdList: 30528
  taskIdList: 30529
  taskIdList: 30530
  groupName: "峡谷BP第五周任务（S12）"
}
rows {
  id: 30057
  type: TaskType_BP
  beginShowTime {
    seconds: 1749139200
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1749139200
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30531
  taskIdList: 30532
  taskIdList: 30533
  taskIdList: 30534
  groupName: "峡谷BP第六周任务（S12）"
}
rows {
  id: 30058
  type: TaskType_BP
  beginShowTime {
    seconds: 1749744000
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1749744000
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30535
  taskIdList: 30536
  taskIdList: 30537
  taskIdList: 30538
  groupName: "峡谷BP第七周任务（S12）"
}
rows {
  id: 30059
  type: TaskType_BP
  beginShowTime {
    seconds: 1750348800
  }
  endShowTime {
    seconds: 1750953599
  }
  beginDoTime {
    seconds: 1750348800
  }
  endDoTime {
    seconds: 1750953599
  }
  taskIdList: 30539
  taskIdList: 30540
  taskIdList: 30541
  taskIdList: 30542
  groupName: "峡谷BP第八周任务（S12）"
}
