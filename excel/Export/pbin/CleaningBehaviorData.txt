com.tencent.wea.xlsRes.table_CleaningBehavior
excel/xls/Q_清理条件表.xlsx sheet:行为
rows {
  id: "O1"
  type: "弹窗提示可以清理"
  funcName: "ShowWindowTipFunc"
  args: "提示内容:str,弹窗CD:int"
  template: "可以清理缓存!,3"
  argsDesc: "提示内容为 可以清理缓存!, CD为3天"
}
rows {
  id: "O2"
  type: "清理tag的缓存"
  funcName: "ClearCacheFunc"
  args: "是否主动清理"
  template: "2"
  argsDesc: "主动清理, 不填即为自动清理"
}
rows {
  id: "O3"
  type: "弹窗提示，确定后清理tag的缓存"
  funcName: "ShowWindowConfirmFunc"
}
rows {
  id: "O4"
  type: "仅清理UGC地图缓存"
  funcName: "OnlyClearUGCMap"
  args: "是否主动清理"
  template: "2"
  argsDesc: "主动清理, 不填即为自动清理"
}
rows {
  id: "O5"
  type: "清理时装，支持不清理某些包组"
  funcName: "ClearAvatarWithoutSomeGroups"
  argsDesc: "不清理的时装包组id"
}
rows {
  id: "O6"
  type: "清理group"
  funcName: "ClearGroupPaks"
}
