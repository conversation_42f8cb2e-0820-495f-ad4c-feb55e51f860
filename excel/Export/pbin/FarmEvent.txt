com.tencent.wea.xlsRes.table_FarmEvent
excel/xls/Farm/N_农场事件表.xlsx sheet:事件配置
rows {
  reward: 24001
  series: 1
  evtID: 101
  next: 201
  mutex: 2
  mutexID: 1
  cond: ET_Fish
  prob: 0
  hasNPC: 1101
  start: "2000-11-11 10:01:01"
  end: "2100-11-11 10:01:01"
  pos: "4240,1310,161,180"
  count: 37
  level: 40
  specialProb: 1
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_fish_open_time"
  replace: 1
  rainbowNew: FMI_EventFish
}
rows {
  reward: 24002
  series: 1
  evtID: 201
  next: 301
  mutex: 2
  mutexID: 1
  cond: ET_Fish
  prob: 0
  hasNPC: 1101
  start: "2000-11-11 10:01:02"
  end: "2100-11-11 10:01:02"
  pos: "4240,1310,161,180"
  count: 100
  level: 40
  specialProb: 1
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_fish_open_time"
  replace: 1
  rainbowNew: FMI_EventFish
}
rows {
  reward: 24003
  series: 1
  evtID: 301
  mutex: 2
  mutexID: 1
  cond: ET_Fish
  prob: 0
  hasNPC: 1101
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "4240,1310,161,180"
  count: 187
  level: 40
  specialProb: 1
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_fish_open_time"
  replace: 1
  rainbowNew: FMI_EventFish
}
rows {
  series: 2
  evtID: 401
  next: 401
  mutex: 2
  mutexID: 2
  cond: ET_HavVeg
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 500
  sceneDropW: 1
  eventend: 1
  level: 5
  version: "109.3.88.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 3
  evtID: 402
  next: 402
  mutex: 2
  mutexID: 2
  cond: ET_HavAni
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 300
  sceneDropW: 2
  eventend: 1
  level: 5
  version: "109.3.78.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 4
  evtID: 403
  next: 403
  mutex: 2
  mutexID: 2
  cond: ET_PrayVeg
  cond: ET_PrayAni
  prob: 1000
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 15
  sceneDropW: 3
  eventend: 1
  level: 5
  version: "109.3.68.1"
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 5
  evtID: 404
  next: 404
  mutex: 2
  mutexID: 2
  cond: ET_StealVeg
  cond: ET_StealAni
  prob: 200
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 75
  sceneDropW: 4
  eventend: 1
  level: 5
  version: "109.3.88.1"
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 6
  evtID: 405
  next: 405
  mutex: 2
  mutexID: 2
  cond: ET_HavProc
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 300
  sceneDropW: 5
  eventend: 1
  level: 5
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 7
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 3
  cond: ET_WaterAmount1
  prob: 3000
  hasNPC: 1102
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "-365,2079,155,-50"
  count: 5
  level: 19
  handler: 1
  cooldown: 5760
  cooldown: 8640
  rainbow: "farm_event_weather_open_time"
  baseReward: 1.0
  replace: 3
  baseRewardCoin: 2.0
  rainbowNew: FMI_EventWeather
}
rows {
  series: 8
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 3
  cond: ET_WaterAmount1
  prob: 1200
  hasNPC: 1102
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "-365,2079,155,-50"
  count: 12
  level: 19
  handler: 1
  cooldown: 5760
  cooldown: 8640
  rainbow: "farm_event_weather_open_time"
  baseReward: 1.2
  replace: 3
  baseRewardCoin: 2.4
  maxGuarantee: 45
  rainbowNew: FMI_EventWeather
  specialCount: 900
}
rows {
  reward: 23012
  series: 9
  evtID: 101
  mutex: 2
  mutexID: 1
  cond: ET_WaterAmount3
  prob: 10000
  hasNPC: 1103
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "-365,2079,155,-50"
  count: 33
  level: 19
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_weather_open_time"
  replace: 3
  rainbowNew: FMI_EventWeather
}
rows {
  reward: 23013
  series: 10
  evtID: 101
  next: 201
  mutex: 2
  mutexID: 1
  cond: ET_WaterAmount4
  prob: 10000
  hasNPC: 1104
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "-365,2079,155,-50"
  count: 68
  level: 19
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_weather_open_time"
  replace: 3
  rainbowNew: FMI_EventWeather
}
rows {
  reward: 23014
  series: 10
  evtID: 201
  mutex: 2
  mutexID: 1
  cond: ET_WaterAmount5
  prob: 10000
  hasNPC: 1104
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "-365,2079,155,-50"
  count: 136
  level: 19
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_weather_open_time"
  replace: 3
  rainbowNew: FMI_EventWeather
}
rows {
  series: 11
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 6
  cond: ET_HavVeg
  prob: 0
  hasNPC: 1105
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "-237.4,-393.8,105,101.5"
  count: 150
  level: 16
  handler: 1
  version: "109.3.37.63"
  specialProb: 1
  cooldown: 5760
  cooldown: 8640
  rainbow: "farm_event_market_open_time"
  baseReward: 0.9
  replace: 5
  baseRewardCoin: 1.8
  cloud: 1
  last: 48
  rainbowNew: FMI_EventMarket
}
rows {
  reward: 23015
  series: 12
  evtID: 101
  next: 201
  mutex: 2
  mutexID: 1
  cond: ET_HavVeg
  prob: 0
  hasNPC: 1105
  start: "2000-11-11 10:01:01"
  end: "2100-11-11 10:01:01"
  pos: "-237.4,-393.8,105,101.5"
  count: 750
  level: 16
  version: "109.3.37.63"
  specialProb: 1
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_market_open_time"
  replace: 5
  unlockEvent: "(11,101,1)"
  rainbowNew: FMI_EventMarket
}
rows {
  reward: 23016
  series: 12
  evtID: 201
  next: 301
  mutex: 2
  mutexID: 1
  cond: ET_HavVeg
  prob: 0
  hasNPC: 1105
  start: "2000-11-11 10:01:02"
  end: "2100-11-11 10:01:02"
  pos: "-237.4,-393.8,105,101.5"
  count: 1500
  level: 16
  version: "109.3.37.63"
  specialProb: 1
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_market_open_time"
  replace: 5
  rainbowNew: FMI_EventMarket
}
rows {
  reward: 23017
  series: 12
  evtID: 301
  mutex: 2
  mutexID: 1
  cond: ET_HavVeg
  prob: 0
  hasNPC: 1105
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "-237.4,-393.8,105,101.5"
  count: 1500
  level: 16
  version: "109.3.37.63"
  specialProb: 1
  cooldown: 4320
  cooldown: 7200
  rainbow: "farm_event_market_open_time"
  replace: 5
  rainbowNew: FMI_EventMarket
}
rows {
  series: 13
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavVeg
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 750
  sceneDropW: 6
  eventend: 1
  level: 5
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 14
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavVeg
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 1500
  sceneDropW: 7
  eventend: 1
  level: 5
  version: "109.3.88.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 15
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavVeg
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 1500
  sceneDropW: 8
  eventend: 1
  level: 5
  version: "109.3.68.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 16
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavAni
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 375
  sceneDropW: 9
  eventend: 1
  level: 5
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 17
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavAni
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 750
  sceneDropW: 10
  eventend: 1
  level: 5
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 18
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavAni
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 1500
  sceneDropW: 11
  eventend: 1
  level: 5
  version: "109.3.88.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 19
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavProc
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 150
  sceneDropW: 12
  eventend: 1
  level: 5
  version: "109.3.88.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 20
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavProc
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 500
  sceneDropW: 13
  eventend: 1
  level: 5
  version: "109.3.88.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 21
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavProc
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 115
  sceneDropW: 14
  eventend: 1
  level: 5
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 22
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_Fish
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 37
  sceneDropW: 15
  eventend: 1
  level: 40
  version: "109.3.88.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 23
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_Fish
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 46
  sceneDropW: 16
  eventend: 1
  level: 40
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 24
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_Fish
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 187
  sceneDropW: 17
  eventend: 1
  level: 40
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 25
  evtID: 401
  next: 401
  mutex: 2
  mutexID: 5
  cond: ET_Fish
  prob: 0
  hasNPC: 1101
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  pos: "4240,1310,161,180"
  count: 9
  level: 50
  version: "109.3.36.1"
  specialProb: 1
  cooldown: 5760
  cooldown: 8640
  rainbow: "farm_event_fish2_open_time"
  baseReward: 0.6
  replace: 1
  baseRewardCoin: 1.2
  fish: 1
  rainbowNew: FMI_EventNPCWater
}
rows {
  series: 26
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 3
  cond: ET_WaterAmount1
  prob: 3000
  hasNPC: 1102
  start: "2000-11-11 10:01:03"
  end: "2025-02-28 10:01:03"
  pos: "-365,2079,155,-50"
  count: 5
  level: 19
  handler: 1
  version: "109.3.37.63"
  cooldown: 5760
  cooldown: 8640
  rainbow: "farm_event_weather_snow_open_time"
  baseReward: 1.0
  replace: 3
  baseRewardCoin: 2.0
  rainbowNew: FMI_EventWeatherSnow
}
rows {
  series: 27
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 3
  cond: ET_WaterAmount1
  prob: 1500
  hasNPC: 1102
  start: "2000-11-11 10:01:03"
  end: "2025-02-28 10:01:03"
  pos: "-365,2079,155,-50"
  count: 10
  level: 19
  handler: 1
  version: "109.3.37.63"
  cooldown: 5760
  cooldown: 8640
  rainbow: "farm_event_weather_snow_open_time"
  baseReward: 1.2
  replace: 3
  baseRewardCoin: 2.4
  rainbowNew: FMI_EventWeatherSnow
}
rows {
  series: 28
  evtID: 101
  mutex: 1
  cond: ET_AnimalShopLevel1
  prob: 10000
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  sceneDropW: 100
  eventend: 1
  level: 60
  version: "109.3.78.60"
  cooldown: 99999999
  cooldown: 99999999
  rainbowNew: FMI_NPCCowTrans
  onlyOneChance: 1
}
rows {
  series: 29
  evtID: 101
  mutex: 1
  cond: ET_CowNPCFinalTaskFinish
  prob: 10000
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  sceneDropW: 101
  eventend: 1
  level: 70
  version: "109.3.78.60"
  cooldown: 99999999
  cooldown: 99999999
  rainbowNew: FMI_NPCCowTrans
  onlyOneChance: 1
}
rows {
  series: 30
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_Fish
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  count: 93
  sceneDropW: 18
  eventend: 1
  level: 40
  version: "109.3.88.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 33
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_HavTTFlower
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  sceneDropW: 19
  eventend: 1
  level: 101
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 34
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_CollectCookCoin
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  sceneDropW: 20
  eventend: 1
  level: 80
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 36
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_InvCookGuest
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  sceneDropW: 22
  eventend: 1
  level: 80
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
rows {
  series: 35
  evtID: 101
  next: 101
  mutex: 2
  mutexID: 2
  cond: ET_InvCookGuest
  prob: 0
  start: "2000-11-11 10:01:03"
  end: "2100-11-11 10:01:03"
  sceneDropW: 21
  eventend: 1
  level: 80
  version: "109.3.99.1"
  specialProb: 1
  cooldown: 1440
  cooldown: 4320
}
