com.tencent.wea.xlsRes.table_ActivityBannerJumpStyleConf
excel/xls/W_玩法入口活动跳转-banner样式_OGC.xlsx sheet:样式
rows {
  id: 10000
  jumpOpenTime {
    seconds: 1743350400
  }
  jumpCloseTime {
    seconds: 1743955199
  }
  jumpId: 20005
  jumpTitle: "定点登录礼"
  jumpText: "通行证经验发放!"
  bannerUrl: "https://image-client.ymzx.qq.com/cdn_img/CN/Mobile/High/T_Main_ActivityBanner_02.astc"
  redDotType: 28
  jumpParams: "147"
}
rows {
  id: 10001
  jumpOpenTime {
    seconds: 1744300800
  }
  jumpCloseTime {
    seconds: 1745164799
  }
  jumpId: 20005
  jumpTitle: "晋级赛狂欢"
  jumpText: "幸运对局概率触发!"
  bannerUrl: "https://image-client.ymzx.qq.com/cdn_img/CN/Mobile/High/T_Main_ActivityBanner_01.astc"
  redDotType: 28
  jumpParams: "148"
}
rows {
  id: 10002
  jumpOpenTime {
    seconds: 1743436800
  }
  jumpCloseTime {
    seconds: 1746028799
  }
  jumpId: 20005
  jumpTitle: "晋级赛冲分"
  jumpText: "四人成行免费领皮肤！"
  bannerUrl: "https://image-client.ymzx.qq.com/cdn_img/CN/Mobile/High/T_Main_ActivityBanner_01.astc"
  redDotType: 28
  jumpParams: "680"
}
rows {
  id: 10003
  jumpOpenTime {
    seconds: 1743436800
  }
  jumpCloseTime {
    seconds: 1751299199
  }
  jumpId: 20005
  jumpTitle: "晋级赛冲分"
  jumpText: "四人成行免费领皮肤！"
  bannerUrl: "https://image-client.ymzx.qq.com/cdn_img/CN/Mobile/High/T_Main_ActivityBanner_01.astc"
  redDotType: 28
  jumpParams: "680"
}
