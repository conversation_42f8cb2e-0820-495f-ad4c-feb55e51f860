com.tencent.wea.xlsRes.table_BackpackCloakroomUnlock
excel/xls/B_背包.xlsx sheet:搭配存储解锁
rows {
  id: 1
  unlockCondition {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 2
  unlockCondition {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 3
  unlockCondition {
    condition {
      conditionType: 48
      value: 1
    }
  }
  confirmText: "您的星宝会员已到期，已自动切换为默认搭配方案，是否立即前往续费？"
  jumpId: 34
  unlockedIcon: "T_Bag_Img_V"
  jumpText: "开通<YellowCurrency>星宝会员</>即可解锁额外搭配方案，是否前往开通？"
  unlockTipText: "开通星宝会员解锁"
}
rows {
  id: 4
  unlockCondition {
    condition {
      conditionType: 48
      value: 1
    }
  }
  confirmText: "您的星宝会员已到期，已自动切换为默认搭配方案，是否立即前往续费？"
  jumpId: 34
  unlockedIcon: "T_Bag_Img_V"
  jumpText: "开通<YellowCurrency>星宝会员</>即可解锁额外搭配方案，是否前往开通？"
  unlockTipText: "开通星宝会员解锁"
}
rows {
  id: 5
  unlockCondition {
    condition {
      conditionType: 1
    }
  }
}
rows {
  id: 6
  unlockCondition {
    condition {
      conditionType: 2
    }
  }
  unlockedIcon: "T_Bag_Img_Coin"
  costCoinType: 6
  costCoinNum: 20
  jumpText: "消耗<YellowCurrency>{0}</>{1}<YellowCurrency>{2}</>即可解锁额外搭配方案，是否解锁？"
  unlockTipText: "消耗{0}{1}解锁"
}
rows {
  id: 7
  unlockCondition {
    condition {
      conditionType: 177
      value: 2
    }
  }
  confirmText: "您的<YellowCurrency>豪华通行证</>已到期，已自动切换为默认搭配方案，是否立即前往购买？"
  jumpId: 208
  unlockedIcon: "T_Bag_Img_Book"
  jumpText: "开通<YellowCurrency>豪华通行证</>即可解锁额外搭配方案，是否前往开通？"
  unlockTipText: "豪华通行证解锁"
}
rows {
  id: 8
  unlockCondition {
    condition {
      conditionType: 177
      value: 2
    }
  }
  confirmText: "您的<YellowCurrency>豪华通行证</>已到期，已自动切换为默认搭配方案，是否立即前往购买？"
  jumpId: 208
  unlockedIcon: "T_Bag_Img_Book"
  jumpText: "开通<YellowCurrency>豪华通行证</>即可解锁额外搭配方案，是否前往开通？"
  unlockTipText: "豪华通行证解锁"
}
rows {
  id: 9
  unlockCondition {
    condition {
      conditionType: 177
      value: 2
    }
  }
  jumpId: 234
  jumpText: "累计获得<YellowCurrency>50000</>奖杯即可解锁额外搭配方案，是否前往？"
  unlockTipText: "累计获得奖杯解锁"
}
rows {
  id: 10
  unlockCondition {
    condition {
      conditionType: 177
      value: 2
    }
  }
  jumpId: 234
  jumpText: "时尚之路达到<YellowCurrency>50级</>即可解锁额外搭配方案，是否前往？"
  unlockTipText: "时尚之路等级解锁"
}
rows {
  id: 11
  unlockCondition {
    condition {
      conditionType: 177
      value: 2
    }
  }
  jumpId: 234
  jumpText: "赛季时尚分达到<YellowCurrency>15000分</>即可解锁额外搭配方案，是否前往？"
  unlockTipText: "赛季时尚分解锁"
}
