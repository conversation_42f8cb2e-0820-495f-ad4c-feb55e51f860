com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_主表.xlsx sheet:奖池-配饰卡池
rows {
  poolId: 5000
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 3
      prices: 6
      prices: 12
      prices: 18
      prices: 36
      prices: 58
      prices: 88
      prices: 118
      prices: 158
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5202
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5001
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5002
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 3
      prices: 6
      prices: 12
      prices: 18
      prices: 36
      prices: 58
      prices: 88
      prices: 118
      prices: 158
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      biTimes: 2
      biTimes: 3
      biTimes: 4
      biTimes: 5
      biTimes: 6
      biTimes: 7
      biTimes: 8
      biTimes: 9
      biPrices: 3
      biPrices: 6
      biPrices: 9
      biPrices: 18
      biPrices: 29
      biPrices: 44
      biPrices: 59
      biPrices: 79
      biLimit: 1
      biExpireHour: 24
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5003
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 13
      prices: 16
      prices: 19
      prices: 22
      prices: 26
      prices: 30
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 1
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 13
      originalPrices: 16
      originalPrices: 19
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5004
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 13
      prices: 16
      prices: 19
      prices: 22
      prices: 26
      prices: 30
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 1
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 13
      originalPrices: 16
      originalPrices: 19
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5005
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 13
      prices: 16
      prices: 19
      prices: 22
      prices: 26
      prices: 30
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 1
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 13
      originalPrices: 16
      originalPrices: 19
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5010
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 3
      prices: 6
      prices: 12
      prices: 18
      prices: 36
      prices: 58
      prices: 88
      prices: 118
      prices: 158
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5011
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5012
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5014
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 4
      prices: 10
      prices: 18
      prices: 24
      prices: 48
      prices: 72
      prices: 102
      prices: 148
      prices: 208
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5016
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 3
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 24
      prices: 28
      prices: 32
      prices: 36
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 24
      originalPrices: 28
      originalPrices: 32
      originalPrices: 36
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5017
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5018
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5019
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 12
      prices: 15
      prices: 17
      prices: 20
      prices: 24
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 13
      originalPrices: 16
      originalPrices: 19
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5020
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 12
      prices: 15
      prices: 17
      prices: 20
      prices: 24
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 13
      originalPrices: 16
      originalPrices: 19
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5021
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 12
      prices: 15
      prices: 17
      prices: 20
      prices: 24
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 13
      originalPrices: 16
      originalPrices: 19
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5023
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 4
      prices: 10
      prices: 18
      prices: 24
      prices: 48
      prices: 72
      prices: 102
      prices: 148
      prices: 208
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5025
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 3
      prices: 3
      prices: 6
      prices: 6
      times: 1
      times: 10
      times: 11
      times: 60
      originalPrices: 6
      originalPrices: 6
      originalPrices: 6
      originalPrices: 6
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 60
  }
  maxLimit: 60
}
rows {
  poolId: 5026
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    dPrices {
      first {
        text: "免费"
      }
    }
    tPrices {
      prices: 0
      prices: 2
      prices: 3
      prices: 6
      prices: 9
      prices: 12
      prices: 15
      prices: 18
      prices: 25
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5027
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    dPrices {
      first {
        text: "免费"
      }
    }
    tPrices {
      prices: 0
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5028
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    dPrices {
      first {
        text: "免费"
      }
    }
    tPrices {
      prices: 0
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5029
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5031
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5032
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5033
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5034
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 8
      prices: 12
      prices: 18
      prices: 25
      prices: 32
      prices: 40
      prices: 56
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5035
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 4
      prices: 10
      prices: 18
      prices: 24
      prices: 48
      prices: 72
      prices: 102
      prices: 148
      prices: 208
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5036
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5037
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 5
      prices: 7
      prices: 8
      prices: 10
      prices: 12
      prices: 15
      prices: 18
      prices: 21
      prices: 24
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 12
      originalPrices: 15
      originalPrices: 18
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5038
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 5
      prices: 7
      prices: 8
      prices: 10
      prices: 12
      prices: 15
      prices: 18
      prices: 21
      prices: 24
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 12
      originalPrices: 15
      originalPrices: 18
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5039
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5049
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 3
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5043
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5044
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5051
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5053
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 3
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 12
      prices: 15
      prices: 20
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 12
      originalPrices: 15
      originalPrices: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5054
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 3
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 12
      prices: 15
      prices: 20
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 3
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 12
      originalPrices: 15
      originalPrices: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5055
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 3
      prices: 6
      prices: 12
      prices: 18
      prices: 36
      prices: 58
      prices: 88
      prices: 118
      prices: 158
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5041
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 1
      prices: 2
      prices: 4
      prices: 6
      prices: 8
      prices: 10
      prices: 13
      prices: 16
      prices: 19
      prices: 22
      prices: 26
      prices: 30
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      times: 10
      times: 11
      times: 12
      originalPrices: 1
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 8
      originalPrices: 10
      originalPrices: 13
      originalPrices: 16
      originalPrices: 19
      originalPrices: 22
      originalPrices: 26
      originalPrices: 30
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 12
  }
  maxLimit: 12
}
rows {
  poolId: 5056
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5057
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5058
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 3
      prices: 6
      prices: 12
      prices: 18
      prices: 36
      prices: 58
      prices: 88
      prices: 118
      prices: 158
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5059
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5060
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5061
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5062
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
rows {
  poolId: 5063
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Tiered
    tPrices {
      prices: 2
      prices: 4
      prices: 6
      prices: 9
      prices: 12
      prices: 16
      prices: 20
      prices: 25
      prices: 32
      times: 1
      times: 2
      times: 3
      times: 4
      times: 5
      times: 6
      times: 7
      times: 8
      times: 9
      originalPrices: 2
      originalPrices: 4
      originalPrices: 6
      originalPrices: 9
      originalPrices: 12
      originalPrices: 16
      originalPrices: 20
      originalPrices: 25
      originalPrices: 32
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 9
  }
  maxLimit: 9
}
