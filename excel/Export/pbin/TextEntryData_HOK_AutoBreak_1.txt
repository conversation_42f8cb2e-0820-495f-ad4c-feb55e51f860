com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/HOK/W_文本表_文本配置_HOK.xlsx sheet:文本配置
rows {
  id: 1500101
  content: "蓝方派出超级士兵"
  switch: 1
  stringId: "HOK_SpectatorSuperBlueComeOn"
}
rows {
  id: 1500102
  content: "红方派出超级士兵"
  switch: 1
  stringId: "HOK_SpectatorSuperRedComeOn"
}
rows {
  id: 1500103
  content: "用来展示一些画面外的关键信息，点击窗口即可将其关闭"
  switch: 1
  stringId: "HOK_Setting_Tip_PIP"
}
rows {
  id: 1500104
  content: "每秒获得额外经验，随战斗时间逐渐增加(当前增加:<CardDescYellow>{0}点</>)"
  switch: 1
  stringId: "HOK_AssistHint1_Exp"
}
rows {
  id: 1500105
  content: "唯一：每秒为经济最低友方额外增加金币(当前提供:<CardDescYellow>{0}金币</>)"
  switch: 1
  stringId: "HOK_AssistHint2_Gold"
}
rows {
  id: 1500106
  content: "不再与队友分配野怪和小兵的收益，转而额外获得部分收益"
  switch: 1
  stringId: "HOK_AssistHint3_Other"
}
rows {
  id: 1500107
  content: "被动"
  switch: 1
  stringId: "HOK_BattleReport_Passive"
}
rows {
  id: 1500108
  content: "防御塔保护已消失"
  switch: 1
  stringId: "HOK_PIPHint_TowerDefense"
}
rows {
  id: 1500109
  content: "主宰暴君降临"
  switch: 1
  stringId: "HOK_PIPHint_LongRefresh"
}
rows {
  id: 1500110
  content: "主宰暴君获得强化"
  switch: 1
  stringId: "HOK_PIPHint_LongUpgrade"
}
rows {
  id: 1500111
  content: "额外"
  switch: 1
  stringId: "HOK_BattleReport_Extra"
}
rows {
  id: 1500112
  content: "第{0}轮"
  switch: 1
  stringId: "UI_HOK_ShopDrawRound"
}
rows {
  id: 1500113
  content: "金币不足！"
  switch: 1
  stringId: "HOK_GoldRebirth_Hint"
}
rows {
  id: 1500114
  content: "已满"
  switch: 1
  stringId: "UI_HOK_ShopCardMax"
}
rows {
  id: 1500115
  content: "我方机甲暴龙刷新"
  switch: 1
  stringId: "HOK_Mount_Born_Self"
}
rows {
  id: 1500116
  content: "敌方机甲暴龙刷新"
  switch: 1
  stringId: "HOK_Mount_Born_Enemy"
}
rows {
  id: 1500117
  content: "<ChatSysWhite>{0}成为和平使者：不参与分配队友的野怪和兵线收益。</><ChatSysWhite>独自享受兵线和野怪收益折损。</>"
  switch: 1
  stringId: "HOK_AssistHint_Chat"
}
rows {
  id: 1500118
  content: "药剂效果已增强"
  switch: 1
  stringId: "HOK_PropCfgNextStage"
}
rows {
  id: 1500119
  content: "商店属性预览开关Tip"
  switch: 1
  stringId: "HOK_Setting_Tip_CardAttrPanel"
}
rows {
  id: 1500120
  content: "高地下方弹板已出现"
  switch: 1
  stringId: "HOK_HomeoutLaunchDevicestart"
}
rows {
  id: 1500121
  content: "我方机甲暴龙已出动"
  switch: 1
  stringId: "HOK_Mount_BeUsing_First_Friend"
}
rows {
  id: 1500122
  content: "敌方机甲暴龙已出动"
  switch: 1
  stringId: "HOK_Mount_BeUsing_First_Enemy"
}
rows {
  id: 1500123
  content: "我方机甲暴龙已消失"
  switch: 1
  stringId: "HOK_Mount_BeUsing_Die_Friend"
}
rows {
  id: 1500124
  content: "敌方机甲暴龙已消失"
  switch: 1
  stringId: "HOK_Mount_BeUsing_Die_Enemy"
}
rows {
  id: 1500125
  content: "边路弹板已消失"
  switch: 1
  stringId: "HOK_AreaMidleLaunchDevicedis"
}
rows {
  id: 1500126
  content: "通过新手局后可选择限免英雄"
  switch: 1
  stringId: "HOK_Tip_NewbieGuide_AfterTutorialFreeHero"
}
rows {
  id: 1500127
  content: "卡牌"
  switch: 1
  stringId: "HOK_BattleReport_Card"
}
rows {
  id: 1500128
  content: "<HOKYellowBroadCast>赤焰隼 幻境之灵</>降临龙坑"
  switch: 1
  stringId: "HOK_HongSun_Enhance"
}
rows {
  id: 1500129
  content: "<HOKYellowBroadCast>天火隼 破界之灵</>降临龙坑"
  switch: 1
  stringId: "HOK_HongSun_Mutation"
}
rows {
  id: 1500130
  content: "反弹"
  switch: 1
  stringId: "HOK_BattleReport_Rebound"
}
rows {
  id: 1500131
  content: "<HOKYellowBroadCast>赤焰隼</>重新降临"
  switch: 1
  stringId: "HOK_HongSun_Enhance_ComeOn"
}
rows {
  id: 1500132
  content: "<HOKYellowBroadCast>幻境之灵</>重新降临"
  switch: 1
  stringId: "HOK_KongJianZhiLing_Enhance_ComeOn"
}
rows {
  id: 1500133
  content: "<HOKYellowBroadCast>天火隼</>重新降临"
  switch: 1
  stringId: "HOK_HongSun_Mutation_ComeOn"
}
rows {
  id: 1500134
  content: "<HOKYellowBroadCast>破界之灵</>重新降临"
  switch: 1
  stringId: "HOK_KongJianZhiLing_Mutation_ComeOn"
}
