com.tencent.wea.xlsRes.table_ResRechargeMonthCardData
excel/xls/C_充值配置表.xlsx sheet:月卡
rows {
  id: "4_2"
  startTime {
    seconds: 1745096400
  }
  endTime {
    seconds: 7274955600
  }
  durationDays: 30
  price: 12
  returnItem {
    itemId: 1
    itemNum: 120
  }
  returnItem {
    itemId: 6
    itemNum: 120
  }
  dailyItem {
    itemId: 2
    itemNum: 1
  }
  dailyItem {
    itemId: 1005
    itemNum: 10
  }
  ratioShow: "10"
  privilegeInfo {
    id: 1
    subId: 130002
    showText: "大星愿礼包"
    type: MCPT_MallDiscount
  }
  privilegeInfo {
    id: 2
    subId: 630015
    showText: "专属装饰"
    icon: "T_Recharge_Img_VipDecorate"
    type: MCPT_PresentDurationItem
    showType: 1
  }
  privilegeInfo {
    id: 3
    showText: "星宝会员专属昵称颜色"
    icon: "Text_Name"
    type: MCPT_Other
    showType: 4
  }
  privilegeInfo {
    id: 4
    showText: "解锁背包中额外2个搭配方案"
    icon: "img_Collocation"
    type: MCPT_Other
    showType: 5
  }
  privilegeInfo {
    id: 5
    subId: 840022
    showText: "头像框"
    icon: "T_HeadFrame_022"
    type: MCPT_PresentDurationItem
    showType: 2
  }
  privilegeInfo {
    id: 6
    subId: 820017
    showText: "昵称框"
    icon: "T_NameFrame_bg_017"
    type: MCPT_PresentDurationItem
    showType: 3
  }
  privilegeInfo {
    id: 7
    subId: 970001
    showText: "炫彩文字"
    icon: "T_Common_Item_BubbleFrame_015"
    type: MCPT_PresentDurationItem
    showType: 6
  }
  textRuleId: 34
  useItemId: 1
  maxDurationDays: 360
  dailyItemMaxCumulativeDays: 7
  expiredRemindHours: 72
}
