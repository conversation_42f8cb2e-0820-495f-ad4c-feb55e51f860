com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置.xlsx sheet:文本配置
rows {
  content: "开启下一轮({0}s)"
  switch: 1
  stringId: "UGC_PartyGame_NextMap"
}
rows {
  content: "前往颁奖({0}s)"
  switch: 1
  stringId: "UGC_PartyGame_AwardMap"
}
rows {
  content: "{0}s后开始"
  switch: 1
  stringId: "UGC_PartyGame_WaitingNextMap"
}
rows {
  content: "剩余玩家不足{0}人，游戏结束"
  switch: 1
  stringId: "UGC_PartyGame_PlayerMissingWarning"
}
rows {
  content: "视野越远，越能看清远处的物体。"
  switch: 1
  stringId: "UGC_VisionSettings_Desc"
}
rows {
  content: "时装"
  switch: 1
  stringId: "ItemType_Suit"
}
rows {
  content: "背饰"
  switch: 1
  stringId: "ItemType_BackOrnament"
}
rows {
  content: "头饰"
  switch: 1
  stringId: "ItemType_HeadWear"
}
rows {
  content: "手持物"
  switch: 1
  stringId: "ItemType_HandOrnament"
}
rows {
  content: "面饰"
  switch: 1
  stringId: "ItemType_FaceOrnament"
}
rows {
  content: "表情"
  switch: 1
  stringId: "ItemType_Emoji"
}
rows {
  content: "当前玩法队伍人数已达上限"
  switch: 1
  stringId: "Group_Invitation_Restrictions"
}
rows {
  content: "当前角色状态不能召唤载具，请先收起当前载具"
  switch: 1
  stringId: "Exclusive_Vehicle_IllegalState_Using"
}
rows {
  content: "活动已过期"
  switch: 1
  stringId: "Task_Activity_HasExpired"
}
rows {
  content: "请使用数字小键盘进行输入"
  switch: 1
  stringId: "UGC_UGCEditor_NumberKeyboard_OpenTips"
}
rows {
  content: "当前输入不支持此功能"
  switch: 1
  stringId: "UGC_UGCEditor_NumberKeyboard_Disable"
}
rows {
  content: "奖杯导航"
  switch: 1
  stringId: "Cup_Collect_Main_Title"
}
rows {
  content: "玩法获得奖杯"
  switch: 1
  stringId: "Cup_Collect_Main_Game_Title"
}
rows {
  content: "（<Orange2_0>赛季时尚分 组队同玩</>加成奖杯获取中!）"
  switch: 1
  stringId: "Cup_Collect_Main_Game_Add"
}
rows {
  content: "任务获得奖杯"
  switch: 1
  stringId: "Cup_Collect_Main_Task_Title"
}
rows {
  content: "每日奖杯数"
  switch: 1
  stringId: "Cup_Collect_Main_Cup_Pre_Num"
}
rows {
  content: "奖杯征程进度值"
  switch: 1
  stringId: "Cup_Collect_Main_Cup_Rank_Title"
}
rows {
  content: "奖杯排行榜"
  switch: 1
  stringId: "Cup_Collect_Main_Cup_Rank_Btn"
}
rows {
  content: "再得"
  switch: 1
  stringId: "Cup_Collect_Main_Cup_Rank_Str1"
}
rows {
  content: "可得以下奖励"
  switch: 1
  stringId: "Cup_Collect_Main_Cup_Rank_Str2"
}
rows {
  content: "确定要退还联合创作权限吗?"
  switch: 1
  stringId: "UGC_CoCreate_RefundPermission"
}
rows {
  content: "该联合创作地图已下架"
  switch: 1
  stringId: "UGC_CoCreate_MapRemoved"
}
rows {
  content: "草稿箱已满，请清理后重试"
  switch: 1
  stringId: "UGC_MapTransToLobby_Draft_Full"
}
rows {
  content: "转化失败！当前仅支持竞速模式地图转化为乐园模式，请检查地图设置。"
  switch: 1
  stringId: "UGC_MapTransToLobby_Failed"
}
rows {
  content: "成功转化为乐园模式草稿"
  switch: 1
  stringId: "UGC_MapTransToLobby_Success"
}
rows {
  content: "地图内容较多，需要更多时间进行加载~"
  switch: 1
  stringId: "UGC_AiAssistant_LoadingTimeTips"
}
rows {
  content: "该乐园可能出现卡顿现象，影响游玩体验\n确认继续游玩吗？"
  switch: 1
  stringId: "UGC_PerformanceAlert_LowRisk"
}
rows {
  content: "该乐园可能出现严重卡顿现象，\n甚至有闪退风险，影响游玩体验\n确认继续游玩吗？"
  switch: 1
  stringId: "UGC_PerformanceAlert_HighRisk"
}
rows {
  content: "是否同意下载静默包资源？资源需要下载<MantelCardCount>{0}</>。"
  switch: 1
  stringId: "Pak_IOSArraignment_PromptText1"
}
rows {
  content: "即将下载身上穿戴的时装，需要下载<MantelCardCount>{0}</>，是否同意下载？"
  switch: 1
  stringId: "Pak_IOSArraignment_PromptText2"
}
rows {
  content: "星梦之杯未回应你的呼唤，前往晋级赛冲段吧！"
  switch: 1
  stringId: "ItemLimit_MainGame"
}
rows {
  content: "恭喜获得腾讯视频会员！7个工作日内将发送邮件，届时请前往游戏邮箱中查收领奖！"
  switch: 1
  stringId: "ItemRedBag_TCVip_Desc"
}
rows {
  content: "玩法说明中的图片单元已达数量上限"
  switch: 1
  stringId: "UGC_MapDescription_PhotoLimit"
}
rows {
  content: "确定要删除重要邮件吗？"
  switch: 1
  stringId: "Mail_ConfirmStarDelete"
}
rows {
  content: "该邮件为重要邮件，不支持批量删除"
  switch: 1
  stringId: "Mail_StarTip"
}
rows {
  content: "魔法小镇-日"
  switch: 1
  stringId: "UGC_Skybox_Name_81"
}
rows {
  content: "魔法小镇-夜"
  switch: 1
  stringId: "UGC_Skybox_Name_82"
}
rows {
  content: "图片加载中，请稍候"
  switch: 1
  stringId: "Share_PicDownloading"
}
rows {
  content: "加载图编辑"
  switch: 1
  stringId: "UGC_LoadDiagram_Edit"
}
rows {
  content: "加载图和导航图编辑"
  switch: 1
  stringId: "UGC_LoadAndNavigateDiagram_Edit"
}
rows {
  content: "主加载图"
  switch: 1
  stringId: "UGC_MainLoadDiagram"
}
rows {
  content: "子场景加载图"
  switch: 1
  stringId: "UGC_SubsceneLoadDiagram"
}
rows {
  content: "导航图"
  switch: 1
  stringId: "UGC_NavigateDiagram"
}
rows {
  content: "地图相册"
  switch: 1
  stringId: "UGC_MapAlbum"
}
rows {
  content: "自定义图片"
  switch: 1
  stringId: "UGC_CustomImage_Select"
}
rows {
  content: "相册图片"
  switch: 1
  stringId: "UGC_MapAlbumImage_Select"
}
rows {
  content: "图标图片"
  switch: 1
  stringId: "UGC_IconImage_Select"
}
rows {
  content: "请先上传图片"
  switch: 1
  stringId: "UGC_UploadImage_Tip"
}
rows {
  content: "图片上传失败"
  switch: 1
  stringId: "UGC_UploadImage_Fail"
}
rows {
  content: "图片上传成功"
  switch: 1
  stringId: "UGC_UploadImage_Success"
}
rows {
  content: "图片上传取消"
  switch: 1
  stringId: "UGC_UploadImage_Cancel"
}
rows {
  content: "导航图和图标不能为空"
  switch: 1
  stringId: "UGC_NavigateDiagramAndIcon_IsNull"
}
rows {
  content: "地图相册,自定义"
  switch: 1
  stringId: "UGC_MapAlbumAndCustomize"
}
rows {
  content: "导航底图"
  switch: 1
  stringId: "UGC_NavigateDiagram_Basemap"
}
rows {
  content: "导航图标"
  switch: 1
  stringId: "UGC_NavigateDiagram_Icon"
}
rows {
  content: "默认,自定义"
  switch: 1
  stringId: "UGC_DefaultImageAndCustomize"
}
rows {
  content: "加载图上传预览"
  switch: 1
  stringId: "UGC_LoadDiagramUpload_Preview"
}
rows {
  content: "导航图上传预览"
  switch: 1
  stringId: "UGC_NavigateDiagramUpload_Preview"
}
rows {
  content: "图标上传预览"
  switch: 1
  stringId: "UGC_IconUpload_Preview"
}
rows {
  content: "踩上卡牌快速飞行，记得给卡牌充能！"
  switch: 1
  stringId: "Prop_MagicCard_Tips"
}
rows {
  content: "踩上卡牌快速飞行，记得给卡牌充能！"
  switch: 1
  stringId: "Prop_MagicCard_MoreTips"
}
rows {
  content: "充能飞牌"
  switch: 1
  stringId: "Prop_MagicCard_Name"
}
rows {
  content: "1、创作者可以在该界面中给生物的各个部位添加外观元件，元件将实时跟随生物部位的运动轨迹，从而让武器、装饰物等外观元件拥有非常自然的运动表现；创作者也可以使用元件自由组合，创造出属于自己的生物，搭配预设的动画，让自己喜欢的生物动起来吧。\n2、创作者需要从场景中选择元件并添加到指定的部位，添加后可以选中元件，对元件进行移动、旋转和缩放的精细化调整。\n3、元件的物理碰撞、运动单元、效果器和变色器会在被添加成自定义外观后失效。"
  switch: 1
  stringId: "UGC_NPC_Quadruped_Help"
}
rows {
  content: "该玩法模式性能消耗较高，当前设备游玩时可能会引发闪退等问题，您确定要使用当前设备游玩该模式嘛？"
  switch: 1
  stringId: "OutOfMemory_Tip"
}
rows {
  content: "晚点试试"
  switch: 1
  stringId: "TryLater_Text"
}
rows {
  content: "超级跳"
  switch: 1
  stringId: "Prop_SuperJump_Name"
}
rows {
  content: "点按可高高跃起！"
  switch: 1
  stringId: "Prop_SuperJump_Tips"
}
rows {
  content: "穿梭"
  switch: 1
  stringId: "Prop_Teleport_Name"
}
rows {
  content: "无视地形障碍穿梭！"
  switch: 1
  stringId: "Prop_Teleport_Tips"
}
rows {
  content: "钩爪"
  switch: 1
  stringId: "Prop_HookLink_Name"
}
rows {
  content: "扔出钩爪向前摆荡！"
  switch: 1
  stringId: "Prop_HookLink_Tips"
}
rows {
  content: "当前环境暂不支持使用"
  switch: 1
  stringId: "CLOUD_TO_NATIVE_TIPS_WX"
}
rows {
  content: "测试中建议浏览整个地图，避免遗漏"
  switch: 1
  stringId: "UGC_Fairyland_Testing_Tips"
}
rows {
  content: "当前场景不支持跳转，请前往星梦广场参与活动"
  switch: 1
  stringId: "ActivityNeedCommunityScene"
}
rows {
  content: "当前场景不支持跳转，请前往谁是狼人参与活动"
  switch: 1
  stringId: "ActivityNeedWerewolf"
}
rows {
  content: "狼人通行证未开启，跳转失败"
  switch: 1
  stringId: "WerewolfNotOpenJumpFailed"
}
rows {
  content: "农场社交未解锁，请前往农场升至3级"
  switch: 1
  stringId: "FarmLevelNeed3"
}
rows {
  content: "基础攻击"
  switch: 1
  stringId: "Skill_BasicAttack_Name1"
}
rows {
  content: "连续攻击"
  switch: 1
  stringId: "Skill_CountAttack_Name2"
}
rows {
  content: "闪避"
  switch: 1
  stringId: "Skill_Flash_Name3"
}
rows {
  content: "范围攻击"
  switch: 1
  stringId: "Skill_BasicAOEAttack_Name4"
}
rows {
  content: "散弹攻击"
  switch: 1
  stringId: "Skill_ShotgunaAttack_Name5"
}
rows {
  content: "你已长时间未操作，{0}秒后将自动进入托管。"
  switch: 1
  stringId: "Main_HostingByAI_Tips_1"
}
rows {
  content: "你已长时间未操作，已自动进入托管状态。"
  switch: 1
  stringId: "Main_HostingByAI_Tips_2"
}
rows {
  content: "是否确定领取该外观奖励，不再更改定制外观选项？"
  switch: 1
  stringId: "GiveAway_GetTaskRewards"
}
rows {
  content: "前往定制"
  switch: 1
  stringId: "GiveAway_GetTaskRewards_GoChange"
}
rows {
  content: "确认领奖"
  switch: 1
  stringId: "GiveAway_GetTaskRewards_Get"
}
rows {
  content: "当前已经拥有该装扮，是否确认"
  switch: 1
  stringId: "GiveAway_ChangeTaskReward"
}
rows {
  content: "当前手册等级已满"
  switch: 1
  stringId: "Return_BookFullLevel"
}
rows {
  content: "回归礼"
  switch: 1
  stringId: "Return_GiftTitle"
}
rows {
  content: "回归任务"
  switch: 1
  stringId: "Return_TaskTitle"
}
rows {
  content: "每周任务"
  switch: 1
  stringId: "Return_WeekTaskTitle"
}
rows {
  content: "累计任务"
  switch: 1
  stringId: "Return_AccumulateTaskTitle"
}
rows {
  content: "点击按钮预览稀有时装效果"
  switch: 1
  stringId: "Return_BookSelectSkin"
}
rows {
  content: "离线收益积累中，请稍后再来领取"
  switch: 1
  stringId: "Return_NoOfflineExperience"
}
