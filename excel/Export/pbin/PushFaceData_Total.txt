com.tencent.wea.xlsRes.table_PushFaceData
excel/xls/P_拍脸.xlsx sheet:拍脸内容
rows {
  id: 1000001
  sortId: 100
  codeName: "TMESLive"
  fequency: 1
  open: true
  needLevel: 3
  sceneIds: 1
}
rows {
  id: 1000002
  sortId: 200
  codeName: "LoginPrivateChatNty"
  fequency: 1
  open: true
  sceneIds: 1
}
rows {
  id: 1000003
  sortId: 300
  codeName: "PlayerReturn"
  fequency: 1
  open: true
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000004
  sortId: 400
  codeName: "AppProfile"
  fequency: 1
  open: true
  sceneIds: 1
  sceneIds: 2
}
rows {
  id: 1000005
  sortId: 500
  codeName: "SeasonChanged"
  fequency: 1
  open: true
  sceneIds: 1
  sceneIds: 2
}
rows {
  id: 1000006
  sortId: 600
  codeName: "RewardAfterGame"
  fequency: 1
  open: true
  sceneIds: 1
  sceneIds: 2
}
rows {
  id: 1000007
  sortId: 700
  codeName: "FirstCharge"
  fequency: 1
  open: true
  sceneIds: 1
}
rows {
  id: 1000008
  sortId: 800
  codeName: "ScenePack"
  fequency: 1
  open: true
  needLevel: 1
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 7
}
rows {
  id: 1000009
  sortId: 801
  codeName: "WXScenePack"
  fequency: 1
  open: true
  needLevel: 1
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
}
rows {
  id: 1000010
  sortId: 900
  codeName: "TeamConfirmStart"
  fequency: 1
  open: true
  sceneIds: 2
}
rows {
  id: 1000011
  sortId: 1000
  codeName: "PlayerReturnSignIn"
  fequency: 2
  open: true
  needLevel: 5
  sceneIds: 1
}
rows {
  id: 1000012
  sortId: 1100
  codeName: "ShowFestivalCheckin"
  fequency: 2
  open: true
  activityId: 206
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000013
  sortId: 1200
  codeName: "NoticeBackToLobby"
  fequency: 2
  open: true
  needLevel: 3
  sceneIds: 2
}
rows {
  id: 1000014
  sortId: 1300
  codeName: "BlessBag"
  fequency: 1
  open: true
  needLevel: 3
  sceneIds: 2
}
rows {
  id: 1000015
  sortId: 1400
  codeName: "Notice"
  fequency: 1
  open: true
  needLevel: 3
  sceneIds: 1
}
rows {
  id: 1000016
  sortId: 100000
  codeName: "PrivateChatNty"
  fequency: 1
  open: true
  sceneIds: 2
}
rows {
  id: 1000017
  sortId: 200000
  codeName: "TeamInviteHint"
  fequency: 1
  open: true
  needLevel: 3
  sceneIds: 1
}
rows {
  id: 1000018
  sortId: 1401
  codeName: "PandoraNotice"
  fequency: 1
  open: true
  needLevel: 3
  sceneIds: 1
}
rows {
  id: 1000019
  sortId: 10000000
  codeName: "UpdateConfig"
  fequency: 1
  open: true
  sceneIds: 2
}
rows {
  id: 1000020
  sortId: 1500
  codeName: "InvitePlayerEvent"
  fequency: 2
  open: true
  activityId: 80402
  sceneIds: 1
  sceneIds: 3
  sceneIds: 6
}
rows {
  id: 1000021
  sortId: 1601
  codeName: "ArenaGetNewHero"
  fequency: 1
  open: true
  sceneIds: 7
  sceneIds: 8
  sceneIds: 10
}
rows {
  id: 1000022
  sortId: 1700
  codeName: "ArenaGetCardPack"
  fequency: 1
  open: true
  sceneIds: 7
}
rows {
  id: 1000023
  sortId: 1800
  codeName: "CloudGuideApp"
  fequency: 1
  open: true
  sceneIds: 3
  sceneIds: 6
}
rows {
  id: 1000024
  sortId: 1
  codeName: "VersionRotationPopup"
  fequency: 1
  open: false
  needLevel: 5
  sceneIds: 1
  sceneIds: 2
  sceneIds: 6
}
rows {
  id: 1000025
  sortId: 1100
  codeName: "ShowFestivalCheckin"
  fequency: 2
  open: true
  activityId: 206
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000026
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5100
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000027
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5100
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000028
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5101
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000029
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5101
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000030
  sortId: 210000
  codeName: "ChooseMatchToast"
  fequency: 1
  open: true
  sceneIds: 2
}
rows {
  id: 1000031
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5102
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000032
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5102
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000033
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5102
  needLevel: 1
  sceneIds: 4
  loginPlatArr: PLP_QQMiniGame
  loginPlatArr: PLP_VA
}
rows {
  id: 1000034
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5100
  needLevel: 1
  sceneIds: 4
  loginPlatArr: PLP_QQMiniGame
  loginPlatArr: PLP_VA
}
rows {
  id: 1000035
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5101
  needLevel: 1
  sceneIds: 4
  loginPlatArr: PLP_QQMiniGame
  loginPlatArr: PLP_VA
}
rows {
  id: 1000036
  sortId: 1
  codeName: "VersionRotationPopup01"
  fequency: 1
  open: true
  needLevel: 5
  sceneIds: 1
  sceneIds: 2
  sceneIds: 6
}
rows {
  id: 1000037
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5103
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000038
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5103
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000039
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5103
  needLevel: 1
  sceneIds: 4
  loginPlatArr: PLP_QQMiniGame
  loginPlatArr: PLP_VA
}
rows {
  id: 1000040
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5104
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000041
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5104
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000042
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5104
  needLevel: 1
  sceneIds: 4
  loginPlatArr: PLP_QQMiniGame
  loginPlatArr: PLP_VA
}
rows {
  id: 1000043
  sortId: 1499
  codeName: "ArenaVideoUI"
  fequency: 2
  open: true
  needLevel: 3
  sceneIds: 1
  beginTime {
    seconds: 1734192000
  }
  endTime {
    seconds: 1734537599
  }
  showPeriod {
    openWeek: 1
    openWeek: 2
    openWeek: 3
    openWeek: 4
    openWeek: 5
    openWeek: 6
    openWeek: 7
    startTime {
      seconds: 43200
    }
    endTime {
      seconds: 86399
    }
  }
  VideoCDN: "https://1318168317.vod-qcloud.com/1055d9b8vodtranssh1318168317/c0b2d8db1397757899782634061/v.f100030.mp4"
  JumpID: 50111
}
rows {
  id: 1000044
  sortId: 1301
  codeName: "ArenaVideoUI"
  fequency: 2
  open: true
  needLevel: 3
  sceneIds: 1
  beginTime {
    seconds: 1740326400
  }
  endTime {
    seconds: 1740412799
  }
  showPeriod {
    openWeek: 1
    openWeek: 2
    openWeek: 3
    openWeek: 4
    openWeek: 5
    openWeek: 6
    openWeek: 7
    startTime {
      seconds: 43200
    }
    endTime {
      seconds: 86399
    }
  }
  VideoCDN: "https://1318168317.vod-qcloud.com/1055d9b8vodtranssh1318168317/c0b2d8db1397757899782634061/v.f100030.mp4"
  JumpID: 50111
}
rows {
  id: 1000045
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5105
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000046
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5105
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000047
  sortId: 50
  codeName: "ShowSevenDayCheckin"
  fequency: 2
  open: true
  activityId: 5105
  needLevel: 1
  sceneIds: 4
  loginPlatArr: PLP_QQMiniGame
  loginPlatArr: PLP_VA
}
rows {
  id: 1000048
  sortId: 50
  codeName: "PlayerBirthdayToday"
  fequency: 1
  open: true
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000049
  sortId: 300000
  codeName: "RewardAfterGame"
  fequency: 1
  open: true
  sceneIds: 2
}
rows {
  id: 1000050
  sortId: 1600
  codeName: "ArenaHeroPost"
  fequency: 1
  open: true
  sceneIds: 8
}
rows {
  id: 1000051
  sortId: 1701
  codeName: "ArenaSevenDay"
  fequency: 1
  open: true
  activityId: 30201
  needLevel: 1
  sceneIds: 7
}
rows {
  id: 1000052
  sortId: 310000
  codeName: "H5TradeIn"
  fequency: 1
  open: true
  sceneIds: 1
  sceneIds: 2
}
rows {
  id: 1000053
  sortId: 50
  codeName: "PlayerBirthdayLatter"
  fequency: 2
  open: true
  needLevel: 1
  sceneIds: 1
  sceneIds: 2
}
rows {
  id: 1000054
  sortId: 50
  codeName: "CupLobbyViewPlayPushFaceAnimation"
  fequency: 1
  open: true
  sceneIds: 9
  sceneIds: 2
  sceneIds: 1
}
rows {
  id: 1000055
  sortId: 799
  codeName: "MiniGameSceneGift"
  fequency: 1
  open: true
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 6
}
rows {
  id: 1000056
  sortId: 789
  codeName: "MoneyTree"
  fequency: 2
  open: true
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 6
}
rows {
  id: 1000057
  sortId: 2
  codeName: "ReturnGameModeShow"
  fequency: 2
  open: true
  needLevel: 1
  sceneIds: 1
}
rows {
  id: 1000058
  sortId: 80000000
  codeName: "WeekendLuckyStar"
  fequency: 2
  open: true
  activityId: 10020
  needLevel: 1
  sceneIds: 6
  loginPlatArr: PLP_QQMiniGame
  loginPlatArr: PLP_WXMiniGame
  loginPlatArr: PLP_GameMatrix
  loginPlatArr: PLP_VA
  beginTime {
    seconds: 1740326400
  }
  endTime {
    seconds: 1745510399
  }
  showPeriod {
    openWeek: 6
    openWeek: 7
    startTime {
    }
    endTime {
      seconds: 75599
    }
  }
  JumpID: 629
  delayTime: 600
}
rows {
  id: 1000059
  sortId: 999999999
  codeName: "MobaPushFaceEnd"
  fequency: 1
  open: true
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 4
  sceneIds: 5
  sceneIds: 6
  sceneIds: 7
}
rows {
  id: 1000060
  sortId: 50
  codeName: "ActivityAccessories"
  fequency: 1
  open: true
  sceneIds: 2
}
rows {
  id: 1000061
  sortId: 1099
  codeName: "WXMiniGameSubscribeTip"
  fequency: 1
  open: true
  sceneIds: 2
}
rows {
  id: 1000062
  sortId: 50
  codeName: "CupRoundReachLimit"
  fequency: 1
  open: true
  needLevel: 1
  sceneIds: 2
}
rows {
  id: 1000063
  sortId: 9998
  codeName: "ShowMobaActivityInfo"
  fequency: 1
  open: true
  activityId: 30426
  sceneIds: 7
  beginTime {
    seconds: 1750953600
  }
  endTime {
    seconds: 1751212799
  }
}
rows {
  id: 1000064
  sortId: 9999
  codeName: "ShowPCWebTransClientTip"
  fequency: 2
  open: true
  sceneIds: 2
  loginPlatArr: PLP_GameMatrix
}
rows {
  id: 1000065
  sortId: 50
  codeName: "SummerFlashPushFace"
  fequency: 2
  open: true
  activityId: 5084
  needLevel: 1
  sceneIds: 1
  sceneIds: 2
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1756483199
  }
  showPeriod {
    openWeek: 5
    startTime {
    }
    endTime {
      seconds: 86399
    }
  }
}
