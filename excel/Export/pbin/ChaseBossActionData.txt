com.tencent.wea.xlsRes.table_ChaseBossAction
excel/xls/D_道具表_个性化_大王别抓我暗星动作.xlsx sheet:Sheet1
rows {
  type: "ItemType_Action1P"
  id: 770000
  name: "好戏开场"
  quality: 3
  icon: "CDN:Icon_Cheer_001"
  desc: "德古拉 好戏开场"
  movementConf {
    action: "AS_MCG_Dracula_Special_Idle_Re_01"
    assetType: 1
    isLoop: false
    actionType: 1
    needFriendIntimate: 0
    action_3_5: "AS_MCG_Dracula_Special_Idle_Re_01"
  }
  actorID: 1013
  actorName: "德古拉"
  useInLoca: 0
}
rows {
  type: "ItemType_Action1P"
  id: 770001
  name: "美味追击"
  quality: 3
  icon: "CDN:Icon_Shive_001"
  desc: "德古拉 美味追击"
  movementConf {
    action: "AS_MCG_Dracula_Special_Idle_Re_02"
    assetType: 1
    isLoop: false
    actionType: 1
    needFriendIntimate: 0
    action_3_5: "AS_MCG_Dracula_Special_Idle_Re_02"
  }
  actorID: 1013
  actorName: "德古拉"
  useInLoca: 0
}
rows {
  type: "ItemType_Action1P"
  id: 770002
  name: "女王登场"
  quality: 3
  icon: "CDN:Icon_Cheer_001"
  desc: "伊丽莎白 女王登场"
  movementConf {
    action: "AS_MCG_Liza_Special_Idle_Re_01"
    assetType: 1
    isLoop: false
    actionType: 1
    needFriendIntimate: 0
    action_3_5: "AS_MCG_Liza_Special_Idle_Re_01"
  }
  actorID: 1014
  actorName: "伊丽莎白"
  useInLoca: 0
}
rows {
  type: "ItemType_Action1P"
  id: 770003
  name: "月光舞步"
  quality: 3
  icon: "CDN:Icon_Shive_001"
  desc: "伊丽莎白 月光舞步"
  movementConf {
    action: "AS_MCG_Liza_Special_Idle_Re_02"
    assetType: 1
    isLoop: false
    actionType: 1
    needFriendIntimate: 0
    action_3_5: "AS_MCG_Liza_Special_Idle_Re_02"
  }
  actorID: 1014
  actorName: "伊丽莎白"
  useInLoca: 0
}
rows {
  type: "ItemType_Action1P"
  id: 770004
  name: "为你撑伞"
  quality: 3
  icon: "CDN:Icon_Cheer_001"
  desc: "拉弥亚 为你撑伞"
  movementConf {
    action: "AS_MCG_Mia_Special_Idle_01"
    assetType: 1
    isLoop: false
    actionType: 1
    needFriendIntimate: 0
    action_3_5: "AS_MCG_Mia_Special_Idle_01"
  }
  actorID: 1012
  actorName: "拉弥亚"
  useInLoca: 0
}
rows {
  type: "ItemType_Action1P"
  id: 770005
  name: "伞舞"
  quality: 3
  icon: "CDN:Icon_Shive_001"
  desc: "拉弥亚 伞舞"
  movementConf {
    action: "AS_MCG_Mia_Special_Idle_02"
    assetType: 1
    isLoop: false
    actionType: 1
    needFriendIntimate: 0
    action_3_5: "AS_MCG_Mia_Special_Idle_02"
  }
  actorID: 1012
  actorName: "拉弥亚"
  useInLoca: 0
}
rows {
  type: "ItemType_Action1P"
  id: 770006
  name: "美味追击1"
  quality: 3
  icon: "CDN:Icon_Shive_001"
  desc: "德古拉 美味追击"
  movementConf {
    action: "AS_MCG_Dracula_Special_Idle_Re_02"
    assetType: 1
    isLoop: true
    actionType: 1
    needFriendIntimate: 0
    animBlendType: 0
    action_3_5: "AS_MCG_Dracula_Special_Idle_Re_02"
  }
  actorID: 1013
  actorName: "德古拉"
  useInLoca: 1
}
rows {
  type: "ItemType_Action1P"
  id: 770007
  name: "美味追击2"
  quality: 3
  icon: "CDN:Icon_Shive_001"
  desc: "德古拉 美味追击"
  movementConf {
    action: "AS_MCG_Dracula_Special_Idle_Re_02"
    assetType: 1
    isLoop: true
    actionType: 1
    needFriendIntimate: 0
    animBlendType: 1
    action_3_5: "AS_MCG_Dracula_Special_Idle_Re_02"
  }
  actorID: 1013
  actorName: "德古拉"
  useInLoca: 1
}
