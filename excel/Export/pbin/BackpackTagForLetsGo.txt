com.tencent.wea.xlsRes.table_ResBackpackTag
excel/xls/B_背包.xlsx sheet:背包标签
rows {
  id: 1
  bagType: 1
  subBagType: 0
  subBagName: "装扮"
  groupid: 0
  sortId: 1
  bShowFarm: false
}
rows {
  id: 2
  bagType: 1
  subBagType: 1
  subBagName: "套装"
  type: ItemType_Suit
  subBagIcon: "T_Shop_Icon_09"
  subBagSelectIcon: "T_Shop_Icon_Selected_01"
  groupid: 0
  UIPath: "UI_Bag_AvatarSubView"
  bShowFarm: false
}
rows {
  id: 3
  bagType: 1
  subBagType: 6
  subBagName: "上装"
  type: ItemType_UpperGarment
  subBagIcon: "T_Shop_Icon_08"
  subBagSelectIcon: "T_Shop_Icon_Selected_02"
  groupid: 0
  UIPath: "UI_Bag_AvatarSubView"
  bShowFarm: false
}
rows {
  id: 4
  bagType: 1
  subBagType: 7
  subBagName: "下装"
  type: ItemType_LowerGarment
  subBagIcon: "T_Shop_Icon_07"
  subBagSelectIcon: "T_Shop_Icon_Selected_03"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 5
  bagType: 1
  subBagType: 8
  subBagName: "手套"
  type: ItemType_Gloves
  subBagIcon: "T_Shop_Icon_06"
  subBagSelectIcon: "T_Shop_Icon_Selected_04"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 6
  bagType: 1
  subBagType: 2
  subBagName: "面饰"
  type: ItemType_FaceOrnament
  subBagIcon: "T_Shop_Icon_04"
  subBagSelectIcon: "T_Shop_Icon_Selected_07"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 7
  bagType: 1
  subBagType: 3
  subBagName: "头饰"
  type: ItemType_HeadWear
  subBagIcon: "T_Shop_Icon_10"
  subBagSelectIcon: "T_Shop_Icon_Selected_05"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 8
  bagType: 1
  subBagType: 4
  subBagName: "背饰"
  type: ItemType_BackOrnament
  subBagIcon: "T_Shop_Icon_03"
  subBagSelectIcon: "T_Shop_Icon_Selected_06"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 9
  bagType: 1
  subBagType: 11
  subBagName: "脸部"
  type: ItemType_Face
  subBagIcon: "T_Shop_Icon_11"
  subBagSelectIcon: "T_Shop_Icon_Selected_11"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 10
  bagType: 1
  subBagType: 5
  subBagName: "手部"
  type: ItemType_HandOrnament
  subBagIcon: "T_Shop_Icon_14"
  subBagSelectIcon: "T_Shop_Icon_Selected_14"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 11
  bagType: 2
  subBagType: 0
  subBagName: "互动"
  groupid: 0
  sortId: 2
  bShowFarm: false
}
rows {
  id: 12
  bagType: 2
  subBagType: 1
  subBagName: "表情"
  type: ItemType_Emoji
  subBagIcon: "T_Shop_Icon_Face_01"
  subBagSelectIcon: "T_Shop_Icon_Face_01_Selected"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 13
  bagType: 2
  subBagType: 3
  subBagName: "单人动作"
  type: ItemType_Action1P
  subBagIcon: "T_Shop_Icon_Action"
  subBagSelectIcon: "T_Shop_Icon_Action_Selected"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 14
  bagType: 2
  subBagType: 4
  subBagName: "双人动作"
  type: ItemType_Action2P
  subBagIcon: "T_Shop_Icon_DoubleAction"
  subBagSelectIcon: "T_Shop_Icon_DoubleAction_Selected"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 15
  bagType: 2
  subBagType: 5
  subBagName: "互动道具"
  type: ItemType_InteractiveProp
  type: ItemType_InteractiveToy
  subBagIcon: "T_Shop_Icon_12"
  subBagSelectIcon: "T_Shop_Icon_Selected_12"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 16
  bagType: 8
  subBagType: 0
  subBagName: "载具"
  groupid: 0
  UIPath: "UI_Bag_InteractionSubView"
  sortId: 5
  bShowFarm: false
}
rows {
  id: 17
  bagType: 2
  subBagType: 6
  subBagName: "互动组合"
  type: ItemType_Combination
  subBagIcon: "T_Shop_Icon_13"
  subBagSelectIcon: "T_Shop_Icon_Selected_13"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 18
  bagType: 3
  subBagType: 0
  subBagName: "道具"
  groupid: 0
  sortId: 3
  bShowFarm: false
}
rows {
  id: 19
  bagType: 4
  subBagType: 0
  subBagName: "宝库"
  groupid: 0
  sortId: 4
  bShowFarm: false
}
rows {
  id: 20
  bagType: 4
  subBagType: 1
  subBagName: "飞车"
  type: ItemType_Kart
  subBagIcon: "T_Shop_Icon_Car"
  subBagSelectIcon: "T_Shop_Icon_Car_Selected"
  pakGroup: 20031
  groupid: 0
  bShowFarm: false
}
rows {
  id: 21
  bagType: 5
  subBagType: 0
  subBagName: "星梭"
  type: ItemType_Shuttle
  groupid: 0
  sortId: 7
  bShowFarm: false
}
rows {
  id: 22
  bagType: 4
  subBagType: 2
  subBagName: "农场"
  type: ItemType_FarmActive
  subBagIcon: "T_Shop_Icon_16"
  subBagSelectIcon: "T_Shop_Icon_Selected_16"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 23
  bagType: 4
  subBagType: 3
  subBagName: "狼人杀"
  type: ItemType_NR3E_Interactive
  type: ItemType_NR3E_ReportAnim
  type: ItemType_NR3E_AttackAnim
  type: ItemType_NR3E_Meeting
  type: ItemType_NR3E_Identity
  type: ItemType_NR3E_MVPAnim
  subBagIcon: "T_Shop_Icon_15"
  subBagSelectIcon: "T_Shop_Icon_Selected_15"
  pakGroup: 20003
  groupid: 0
  bShowFarm: false
}
rows {
  id: 24
  bagType: 4
  subBagType: 4
  subBagName: "moba"
  type: ItemType_Arena_HeroTry
  subBagIcon: "T_Shop_Icon_17"
  subBagSelectIcon: "T_Shop_Icon_Selected_17"
  pakGroup: 50021
  minVersion: "1.3.26.1"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 25
  bagType: 4
  subBagType: 5
  subBagName: "躲猫猫"
  type: ItemType_NR3E_DisguiseEffect
  subBagIcon: "T_Shop_Icon_Peekaboo"
  subBagSelectIcon: "T_Shop_Icon_Peekaboo_Selected"
  pakGroup: 20001
  minVersion: "1.3.37.1"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 26
  bagType: 1
  subBagType: 9
  subBagName: "脚印"
  type: ItemType_FootPrint
  subBagIcon: "T_Shop_Icon_Streaking"
  subBagSelectIcon: "T_Shop_Icon_Streaking_Selected"
  openTime {
    seconds: 4088073600
  }
  groupid: 0
  bShowFarm: false
}
rows {
  id: 27
  bagType: 1
  subBagType: 10
  subBagName: "天慕商品"
  type: ItemType_Velarium
  subBagIcon: "T_Shop_Icon_Broadcast"
  subBagSelectIcon: "T_Shop_Icon_Broadcast_Selected"
  openTime {
    seconds: 4088073600
  }
  groupid: 0
  bShowFarm: false
}
rows {
  id: 32
  bagType: 3
  subBagType: 1
  subBagName: "普通道具"
  type: ItemType_Common
  type: ItemType_Package
  type: ItemType_Package_Share
  type: ItemType_QualifyingAdditionalCard
  type: ItemType_QualifyingProtectedCard
  type: ItemType_AutoUse
  subBagIcon: "T_Shop_Icon_18"
  subBagSelectIcon: "T_Shop_Icon_Selected_18"
  groupid: 0
  bShowFarm: true
}
rows {
  id: 33
  bagType: 3
  subBagType: 2
  subBagName: "生日贺卡"
  type: ItemType_BirthdayCard
  subBagIcon: "T_Shop_Icon_19"
  subBagSelectIcon: "T_Shop_Icon_Selected_19"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 34
  bagType: 8
  subBagType: 1
  subBagName: "载具"
  type: ItemType_Vehicle
  type: ItemType_VehicleDecorate
  subBagIcon: "T_Shop_Icon_Vehicle"
  subBagSelectIcon: "T_Shop_Icon_Vehicle_Selected"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 35
  bagType: 3
  subBagType: 3
  subBagName: "活动券"
  type: ItemType_Currency
  subBagIcon: "T_Shop_Icon_ItemTicket"
  subBagSelectIcon: "T_Shop_Icon_ItemTicket_Select"
  groupid: 0
  bShowFarm: false
}
rows {
  id: 44
  bagType: 10
  subBagType: 0
  subBagName: "定制"
  groupid: 0
  UIPath: "UI_Bag_AvatarSubView"
  sortId: 6
  bShowFarm: false
}
rows {
  id: 45
  bagType: 10
  subBagType: 1
  subBagName: "环绕物"
  type: ItemType_Surroundings
  subBagIcon: "T_Shop_Icon_20"
  subBagSelectIcon: "T_Shop_Icon_Selected_20"
  groupid: 0
  UIPath: "UI_Bag_AvatarSubView"
  bShowFarm: false
}
rows {
  id: 46
  bagType: 10
  subBagType: 2
  subBagName: "脚印"
  type: ItemType_FootPrint
  subBagIcon: "T_Shop_Icon_Streaking"
  subBagSelectIcon: "T_Shop_Icon_Streaking_Selected"
  groupid: 0
  UIPath: "UI_Bag_AvatarSubView"
  bShowFarm: false
}
rows {
  id: 47
  bagType: 10
  subBagType: 3
  subBagName: "星梭"
  type: ItemType_Shuttle
  subBagIcon: "T_Shop_Icon_StarShuttle"
  subBagSelectIcon: "T_Shop_Icon_StarShuttle_Selected"
  openTime {
    seconds: 4088073600
  }
  groupid: 0
  UIPath: "UI_Bag_AvatarSubView"
  bShowFarm: false
}
rows {
  id: 48
  bagType: 10
  subBagType: 4
  subBagName: "天慕商品"
  type: ItemType_Velarium
  subBagIcon: "T_Shop_Icon_Broadcast"
  subBagSelectIcon: "T_Shop_Icon_Broadcast_Selected"
  openTime {
    seconds: 4088073600
  }
  groupid: 0
  UIPath: "UI_Bag_AvatarSubView"
  bShowFarm: false
}
rows {
  id: 31
  bagType: 7
  subBagType: 0
  subBagName: "飞跃特效"
  type: ItemType_MainPlayFinalWin_Halo
  subBagIcon: "T_Shop_Icon_Peekaboo"
  subBagSelectIcon: "T_Shop_Icon_Peekaboo_Selected"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_ShowSubView"
  bShowFarm: false
}
rows {
  id: 28
  bagType: 6
  subBagType: 0
  subBagName: "道具皮肤"
  subBagIcon: "T_Shop_Icon_15"
  subBagSelectIcon: "T_Shop_Icon_Selected_15"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 29
  bagType: 6
  subBagType: 1
  subBagName: "尖叫鸭"
  type: ItemType_MainPlayInner_ShriekingDuck
  subBagIcon: "T_InGame_Icon_Prop30"
  subBagSelectIcon: "T_InGame_Icon_Prop30"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 30
  bagType: 6
  subBagType: 2
  subBagName: "滑翔伞"
  type: ItemType_MainPlayInner_HangGlider
  subBagIcon: "T_InGame_Icon_Prop29"
  subBagSelectIcon: "T_InGame_Icon_Prop29"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 36
  bagType: 6
  subBagType: 3
  subBagName: "变大汉堡"
  type: ItemType_MainPlayInner_Big
  subBagIcon: "T_InGame_Icon_Prop03"
  subBagSelectIcon: "T_InGame_Icon_Prop03"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 37
  bagType: 6
  subBagType: 4
  subBagName: "路障"
  type: ItemType_MainPlayInner_Roadblock
  subBagIcon: "T_InGame_Icon_Prop16"
  subBagSelectIcon: "T_InGame_Icon_Prop16"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 38
  bagType: 6
  subBagType: 5
  subBagName: "炸弹"
  type: ItemType_MainPlayInner_Bomb
  subBagIcon: "T_InGame_Icon_Prop04"
  subBagSelectIcon: "T_InGame_Icon_Prop04"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 39
  bagType: 6
  subBagType: 6
  subBagName: "回旋镖"
  type: ItemType_MainPlayInner_Boomerang
  subBagIcon: "T_InGame_Icon_Prop05"
  subBagSelectIcon: "T_InGame_Icon_Prop05"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 40
  bagType: 6
  subBagType: 7
  subBagName: "魔方"
  type: ItemType_MainPlayInner_Substitute
  subBagIcon: "T_InGame_Icon_Prop20"
  subBagSelectIcon: "T_InGame_Icon_Prop20"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 41
  bagType: 6
  subBagType: 8
  subBagName: "云朵"
  type: ItemType_MainPlayInner_Cloud
  subBagIcon: "T_InGame_Icon_Prop07"
  subBagSelectIcon: "T_InGame_Icon_Prop07"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 42
  bagType: 6
  subBagType: 9
  subBagName: "加速鞋"
  type: ItemType_MainPlayInner_SpeedUp
  subBagIcon: "T_InGame_Icon_Prop18"
  subBagSelectIcon: "T_InGame_Icon_Prop18"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 43
  bagType: 6
  subBagType: 10
  subBagName: "仙人掌锤"
  type: ItemType_MainPlayInner_RotateHammer
  subBagIcon: "T_InGame_Icon_Prop51"
  subBagSelectIcon: "T_InGame_Icon_Prop51"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 49
  bagType: 6
  subBagType: 11
  subBagName: "番茄"
  type: ItemType_MainPlayInner_Tomato
  subBagIcon: "T_InGame_Icon_Prop01"
  subBagSelectIcon: "T_InGame_Icon_Prop01"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 50
  bagType: 6
  subBagType: 12
  subBagName: "香蕉皮"
  type: ItemType_MainPlayInner_Banana
  subBagIcon: "T_InGame_Icon_Prop02"
  subBagSelectIcon: "T_InGame_Icon_Prop02"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 51
  bagType: 6
  subBagType: 13
  subBagName: "缩小药水"
  type: ItemType_MainPlayInner_Small
  subBagIcon: "T_InGame_Icon_Prop17"
  subBagSelectIcon: "T_InGame_Icon_Prop17"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 52
  bagType: 6
  subBagType: 14
  subBagName: "雪人炸弹"
  type: ItemType_MainPlayInner_IceBomb
  subBagIcon: "T_InGame_Icon_Prop45"
  subBagSelectIcon: "T_InGame_Icon_Prop45"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 53
  bagType: 6
  subBagType: 15
  subBagName: "喷气背包"
  type: ItemType_MainPlayInner_Jetpack
  subBagIcon: "T_InGame_Icon_Prop11"
  subBagSelectIcon: "T_InGame_Icon_Prop11"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 54
  bagType: 6
  subBagType: 16
  subBagName: "地雷"
  type: ItemType_MainPlayInner_Landmine
  subBagIcon: "T_InGame_Icon_Prop012"
  subBagSelectIcon: "T_InGame_Icon_Prop012"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 55
  bagType: 6
  subBagType: 17
  subBagName: "撑杆"
  type: ItemType_MainPlayInner_Pole
  subBagIcon: "T_InGame_Icon_Prop25"
  subBagSelectIcon: "T_InGame_Icon_Prop25"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 56
  bagType: 6
  subBagType: 18
  subBagName: "弹板"
  type: ItemType_MainPlayInner_Springboard
  subBagIcon: "T_InGame_Icon_Prop33"
  subBagSelectIcon: "T_InGame_Icon_Prop33"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 57
  bagType: 6
  subBagType: 19
  subBagName: "烟花火箭"
  type: ItemType_MainPlayInner_FireworkRocket
  subBagIcon: "T_InGame_Icon_Prop41"
  subBagSelectIcon: "T_InGame_Icon_Prop41"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 58
  bagType: 6
  subBagType: 20
  subBagName: "吸尘器"
  type: ItemType_MainPlayInner_VacuumCleaner
  subBagIcon: "T_InGame_Icon_Prop50"
  subBagSelectIcon: "T_InGame_Icon_Prop50"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 59
  bagType: 6
  subBagType: 21
  subBagName: "黏液史莱姆"
  type: ItemType_MainPlayInner_Mucus
  subBagIcon: "T_InGame_Icon_Prop66"
  subBagSelectIcon: "T_InGame_Icon_Prop66"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
rows {
  id: 60
  bagType: 6
  subBagType: 22
  subBagName: "隐身斗篷"
  type: ItemType_MainPlayInner_Stealth
  subBagIcon: "T_InGame_Icon_Prop67"
  subBagSelectIcon: "T_InGame_Icon_Prop67"
  groupid: 1
  UIPath: "UI_DailyPromoteBag_PropSubView"
  bShowFarm: false
}
