com.tencent.wea.xlsRes.table_ServerInfoData
excel/xls/临时服务器配置.xlsx sheet:正式环境配置
rows {
  id: 1
  servername: "letsgo-dev"
  owner: "内网开发服"
  category: "LetsGo"
  namespace: "letsgo-dev"
  worldid: "7"
  url_LetsGo: "tcp://test.ymzx.qq.com:12007"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16007"
}
rows {
  id: 2
  servername: "letsgo-stable"
  owner: "热更测试3服"
  category: "热更新服务组"
  namespace: "letsgo-stable"
  worldid: "6"
  url_LetsGo: "tcp://test.ymzx.qq.com:12006"
}
rows {
  id: 3
  servername: "new-dstest"
  owner: "new-dstest"
  category: "DSTest"
  namespace: "user00_9_134_130_112"
  worldid: "17"
  url_LetsGo: "tcp://9.134.130.112:8201"
}
rows {
  id: 4
  servername: "letsgo-operate2"
  owner: "运营专用2服"
  category: "LetsGo"
  namespace: "letsgo-xp"
  worldid: "13"
  url_LetsGo: "tcp://test.ymzx.qq.com:12013"
}
rows {
  id: 5
  servername: "letsgo-designer"
  owner: "策划服"
  category: "LetsGo"
  namespace: "letsgo-designer"
  worldid: "27"
  url_LetsGo: "tcp://test.ymzx.qq.com:12027"
}
rows {
  id: 6
  servername: "letsgo-adapt"
  owner: "适配测试1服"
  category: "LetsGo"
  namespace: "letsgo-adapt"
  worldid: "28"
  url_LetsGo: "tcp://test.ymzx.qq.com:12028"
}
rows {
  id: 7
  servername: "letsgo-perf"
  owner: "QA性能测试"
  category: "Test"
  namespace: "letsgo-perf"
  worldid: "29"
  url_LetsGo: "tcp://test.ymzx.qq.com:12029"
}
rows {
  id: 8
  servername: "letsgo-ac"
  owner: "ac测试服"
  category: "Feature"
  namespace: "letsgo-ac"
  worldid: "32"
  url_LetsGo: "tcp://test.ymzx.qq.com:12032"
}
rows {
  id: 9
  servername: "digoldzhang"
  owner: "digoldzhang"
  category: "Personal"
  namespace: "digoldzhang_9_134_131_240"
  worldid: "6"
  url_LetsGo: "tcp://9.134.131.240:8201"
}
rows {
  id: 10
  servername: "wenhua"
  owner: "wenhua"
  category: "Personal"
  namespace: "wenhua_9_134_132_0"
  worldid: "9"
  url_LetsGo: "tcp://9.134.132.0:8201"
}
rows {
  id: 11
  servername: "snowzhuang"
  owner: "snowzhuang"
  category: "Personal"
  namespace: "user00_9_134_73_71"
  worldid: "7"
  url_LetsGo: "tcp://9.134.73.71:8201"
}
rows {
  id: 12
  servername: "blitzzhang"
  owner: "blitzzhang"
  category: "Personal"
  namespace: "blitzzhang_9_135_229_106"
  worldid: "12"
  url_LetsGo: "tcp://9.135.229.106:8201"
}
rows {
  id: 13
  servername: "nichtsun"
  owner: "nichtsun"
  category: "Personal"
  namespace: "nichtsun_9_135_56_219"
  worldid: "14"
  url_LetsGo: "tcp://9.135.56.219:8201"
}
rows {
  id: 14
  servername: "nilyang"
  owner: "nilyang"
  category: "Personal"
  namespace: "nilyang_9_135_1_124"
  worldid: "1"
  url_LetsGo: "tcp://9.135.1.124:8201"
}
rows {
  id: 15
  servername: "letsgo-press"
  owner: "压测服"
  category: "Press"
  namespace: "letsgo-press"
  worldid: "8"
  url_LetsGo: "tcp://press.ymzx.qq.com:12008"
}
rows {
  id: 16
  servername: "markshuang"
  owner: "markshuang"
  category: "Personal"
  namespace: "markshuang_9_134_149_210"
  worldid: "2"
  url_LetsGo: "tcp://9.134.149.210:8201"
}
rows {
  id: 17
  servername: "xufengdu"
  owner: "xufengdu"
  category: "Personal"
  namespace: "xufengdu_9_134_213_89"
  worldid: "19"
  url_LetsGo: "tcp://9.134.213.89:8201"
}
rows {
  id: 18
  servername: "willwizhang"
  owner: "willwizhang"
  category: "Personal"
  namespace: "willwizhang_9_134_211_227"
  worldid: "16"
  url_LetsGo: "tcp://9.134.211.227:8201"
}
rows {
  id: 19
  servername: "letsgo-devbeijing"
  owner: "k8s-北京开发版"
  category: "Personal"
  namespace: "letsgo-devbeijing"
  worldid: "22"
  url_LetsGo: "tcp://test.ymzx.qq.com:12022"
}
rows {
  id: 20
  servername: "dsserver-beijing"
  owner: "专用服务器-北京"
  category: "Personal"
  namespace: "du_9_134_212_29"
  worldid: "18"
  url_LetsGo: "tcp://9.134.212.29:8201"
}
rows {
  id: 21
  servername: "tyleryjyang"
  owner: "tyleryjyang"
  category: "Personal"
  namespace: "tyleryjyang_9_135_62_255"
  worldid: "20"
  url_LetsGo: "tcp://9.135.62.255:8201"
}
rows {
  id: 22
  servername: "halleychen"
  owner: "halleychen"
  category: "Personal"
  namespace: "halleychen_9_134_147_128"
  worldid: "3"
  url_LetsGo: "tcp://9.134.147.128:8201"
}
rows {
  id: 23
  servername: "letsgo-ugcdev"
  owner: "k8s-ugcdev"
  category: "UGC"
  namespace: "letsgo-devbeijing"
  worldid: "22"
  url_LetsGo: "tcp://test.ymzx.qq.com:12022"
}
rows {
  id: 24
  servername: "markshuang-2"
  owner: "markshuang-2"
  category: "Personal"
  url_LetsGo: "tcp://9.134.144.52:8201"
}
rows {
  id: 25
  servername: "letsgo-qasp"
  owner: "品管性能测试服"
  category: "LetsGo"
  namespace: "letsgo-qasp"
  worldid: "24"
  url_LetsGo: "tcp://test.ymzx.qq.com:12024"
}
rows {
  id: 26
  servername: "yingqiliu"
  owner: "yingqiliu"
  category: "Personal"
  namespace: "yingqiliu_9_134_131_243"
  worldid: "23"
  url_LetsGo: "tcp://9.134.131.243:8201"
}
rows {
  id: 27
  servername: "arvinymtan"
  owner: "arvinymtan"
  category: "Personal"
  namespace: "arvinymtan_9_134_130_183"
  worldid: "27"
  url_LetsGo: "tcp://9.134.130.183:8201"
}
rows {
  id: 28
  servername: "leancjli"
  owner: "leancjli"
  category: "Personal"
  namespace: "leancjli_9_134_6_79"
  worldid: "25"
  url_LetsGo: "tcp://9.134.6.79:8201"
}
rows {
  id: 29
  servername: "shmilychen"
  owner: "shmilychen"
  category: "Personal"
  namespace: "shmilychen_9_135_225_55"
  worldid: "26"
  url_LetsGo: "tcp://9.135.225.55:8201"
  config_url_LetsGo: "tcp://9.135.225.55:8401"
}
rows {
  id: 30
  servername: "letsgo-devoversea"
  owner: "海外开发服"
  category: "Feature"
  namespace: "letsgo-devoversea"
  worldid: "31"
  url_LetsGo: "tcp://test.ymzx.qq.com:12031"
}
rows {
  id: 31
  servername: "letsgo-ugcce"
  owner: "k8s-ugcce"
  category: "UGC"
  namespace: "letsgo-ugcce"
  worldid: "34"
  url_LetsGo: "tcp://test.ymzx.qq.com:12034"
}
rows {
  id: 32
  servername: "xuhuiwei"
  owner: "xuhuiwei"
  category: "Personal"
  namespace: "xuhuiwei_9_134_239_152"
  worldid: "28"
  url_LetsGo: "tcp://9.134.239.152:8201"
}
rows {
  id: 33
  servername: "miloofzhang"
  owner: "miloofzhang"
  category: "Personal"
  namespace: "miloofzhang_9_134_6_217"
  worldid: "29"
  url_LetsGo: "tcp://9.134.6.217:8201"
}
rows {
  id: 34
  servername: "letsgo-qafeat"
  owner: "team1-daily"
  category: "Team1"
  namespace: "letsgo-qafeat"
  worldid: "37"
  url_LetsGo: "tcp://test.ymzx.qq.com:12037"
}
rows {
  id: 35
  servername: "letsgo-deva"
  owner: "特性开发A服"
  category: "Feature"
  namespace: "letsgo-deva"
  worldid: "38"
  url_LetsGo: "tcp://test.ymzx.qq.com:12038"
}
rows {
  id: 36
  servername: "letsgo-devb"
  owner: "特性开发B服"
  category: "Feature"
  namespace: "letsgo-devb"
  worldid: "39"
  url_LetsGo: "tcp://test.ymzx.qq.com:12039"
}
rows {
  id: 37
  servername: "letsgo-ugccloudtest"
  owner: "ugc-cloud测试"
  category: "UGC"
  namespace: "letsgo-ugccloudtest"
  worldid: "40"
  url_LetsGo: "tcp://test.ymzx.qq.com:12040"
}
rows {
  id: 38
  servername: "letsgo-ugccloudpre"
  owner: "ugc-cloud预发布"
  category: "UGC"
  namespace: "letsgo-ugccloudpre"
  worldid: "41"
  url_LetsGo: "tcp://test.ymzx.qq.com:12041"
}
rows {
  id: 39
  servername: "letsgo-ce"
  owner: "CE专用服"
  category: "LetsGo"
  namespace: "letsgo-ce"
  worldid: "42"
  url_LetsGo: "tcp://test.ymzx.qq.com:12042"
}
rows {
  id: 40
  servername: "letsgo-ai"
  owner: "AI专用服"
  category: "Feature"
  namespace: "letsgo-ai"
  worldid: "43"
  url_LetsGo: "tcp://test.ymzx.qq.com:12043"
}
rows {
  id: 41
  servername: "letsgo-engine"
  owner: "引擎组专用服"
  category: "Feature"
  namespace: "letsgo-engine"
  worldid: "44"
  url_LetsGo: "tcp://test.ymzx.qq.com:12044"
}
rows {
  id: 42
  servername: "letsgo-acfeat"
  owner: "AC特性分支开发"
  category: "Feature"
  namespace: "letsgo-acfeat"
  worldid: "45"
  url_LetsGo: "tcp://test.ymzx.qq.com:12045"
}
rows {
  id: 43
  servername: "letsgo-reconnect"
  owner: "客户端测试专用"
  category: "Feature"
  namespace: "letsgo-reconnect"
  worldid: "46"
  url_LetsGo: "tcp://test.ymzx.qq.com:12046"
}
rows {
  id: 44
  servername: "letsgo-rmbpress"
  owner: "letsgo-rmbpress"
  category: "Press"
  namespace: "letsgo-rmbpress"
  worldid: "47"
  url_LetsGo: "tcp://press.ymzx.qq.com:12047"
  config_url_LetsGo: "tcp://test4config3.ymzx.qq.com:16047"
}
rows {
  id: 45
  servername: "letsgo-ugcdebug"
  owner: "letsgo-ugcdebug"
  category: "UGC"
  namespace: "letsgo-ugcdebug"
  worldid: "48"
  url_LetsGo: "tcp://test.ymzx.qq.com:12048"
}
rows {
  id: 46
  servername: "yaolonghe"
  owner: "yaolonghe"
  category: "Personal"
  namespace: "yaolonghe_9_134_232_103"
  worldid: "30"
  url_LetsGo: "tcp://9.134.232.103:8201"
}
rows {
  id: 47
  servername: "vacky"
  owner: "vacky"
  category: "Personal"
  namespace: "vacky_9_134_129_209"
  worldid: "31"
  url_LetsGo: "tcp://9.134.129.209:8201"
}
rows {
  id: 48
  servername: "oversea-sg"
  owner: "SG-1"
  category: "Oversea"
  namespace: "letsgo-devoversea-sg"
  worldid: "100"
  url_LetsGo: "tcp://lb-70vvc57u-8xh3vyujgrozmbl7.clb.ap-singapore.tencentclb.com:12100"
}
rows {
  id: 49
  servername: "oversea-sg-2"
  owner: "SG-2"
  category: "Oversea"
  namespace: "letsgo-devoversea-sg-2"
  worldid: "101"
  url_LetsGo: "tcp://lb-hkoqotoq-5s4zo0lrfj2cduxp.clb.ap-singapore.tencentclb.com:12100"
}
rows {
  id: 50
  servername: "oversea-sg-3"
  owner: "SG-3"
  category: "Oversea"
  namespace: "letsgo-devoversea-sg-3"
  worldid: "102"
  url_LetsGo: "tcp://lb-3mpx70fc-m8qoks4dtz3bi7r1.clb.ap-singapore.tencentclb.com:12100"
}
rows {
  id: 51
  servername: "oversea-sg-4"
  owner: "SG-4"
  category: "Oversea"
  namespace: "letsgo-devoversea-sg-4"
  worldid: "103"
  url_LetsGo: "tcp://lb-55ri47e0-kh0y9jcpqu52fli3.clb.ap-singapore.tencentclb.com:12100"
}
rows {
  id: 52
  servername: "oversea-sg-5"
  owner: "SG-5"
  category: "Oversea"
  namespace: "letsgo-devoversea-sg-5"
  worldid: "104"
  url_LetsGo: "tcp://lb-ey0ydl4y-hkxn6dcqz52i1mtf.clb.ap-singapore.tencentclb.com:12100"
}
rows {
  id: 53
  servername: "oversea-sg-6"
  owner: "SG-6"
  category: "Oversea"
  namespace: "letsgo-devoversea-sg-6"
  worldid: "105"
  url_LetsGo: "tcp://lb-eh6doiwe-94jwsr01pzimb7tu.clb.ap-singapore.tencentclb.com:12100"
}
rows {
  id: 54
  servername: "oversea-us-1"
  owner: "US-1"
  category: "Oversea"
  namespace: "letsgo-devoversea-us-1"
  worldid: "200"
  url_LetsGo: "tcp://lb-r4g3ssd4-yn1fl96urmzqh578.clb.na-siliconvalley.tencentclb.com:12100"
}
rows {
  id: 55
  servername: "oversea-us-2"
  owner: "US-2"
  category: "Oversea"
  namespace: "letsgo-devoversea-us-2"
  worldid: "101"
  url_LetsGo: "tcp://lb-8gmj00uq-q8u35jmwip4xtlec.clb.na-siliconvalley.tencentclb.com:12100"
}
rows {
  id: 56
  servername: "dexterzhu"
  owner: "dexterzhu"
  category: "Personal"
  namespace: "user00_9_134_130_61"
  worldid: "39"
  url_LetsGo: "tcp://************:8201"
}
rows {
  id: 57
  servername: "hoythuang"
  owner: "hoythuang"
  category: "Personal"
  namespace: "hoythuang_9_134_234_248"
  worldid: "36"
  url_LetsGo: "tcp://*************:8201"
}
rows {
  id: 58
  servername: "hakulachen"
  owner: "hakulachen"
  category: "Personal"
  namespace: "hakulachen_9_134_131_9"
  worldid: "37"
  url_LetsGo: "tcp://***********:8201"
}
rows {
  id: 59
  servername: "richymeng"
  owner: "richymeng"
  category: "Personal"
  namespace: "richymeng_9_135_1_212"
  worldid: "109"
  url_LetsGo: "tcp://***********:8201"
}
rows {
  id: 60
  servername: "letsgo-ugccloud"
  owner: "UGC-Cloud"
  category: "UGC"
  namespace: "letsgo-ugccloud"
  worldid: "11"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12011"
}
rows {
  id: 61
  servername: "bellazhao"
  owner: "bellazhao"
  category: "Personal"
  namespace: "bellazhao_9_134_20_190"
  worldid: "34"
  url_LetsGo: "tcp://9.134.20.190:8201"
}
rows {
  id: 62
  servername: "letsgo-internal"
  owner: "后端内部测试服"
  category: "Test"
  namespace: "letsgo-internal"
  worldid: "33"
  url_LetsGo: "tcp://test.ymzx.qq.com:12033"
}
rows {
  id: 63
  servername: "letsgo-stress"
  owner: "stress压测服"
  category: "Test"
  namespace: "letsgo-stress"
  worldid: "30"
  url_LetsGo: "tcp://press.ymzx.qq.com:12030"
}
rows {
  id: 64
  servername: "letsgo-master-modifytest"
  owner: "稳定服修改时间"
  category: "Test"
  namespace: "letsgo-master-modifytest"
  worldid: "52"
  url_LetsGo: "tcp://test.ymzx.qq.com:12052"
}
rows {
  id: 65
  servername: "letsgo-xiaowo"
  owner: "小窝开发1服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo"
  worldid: "53"
  url_LetsGo: "tcp://test.ymzx.qq.com:12053"
}
rows {
  id: 66
  servername: "letsgo-team1-feature"
  owner: "team1-feature"
  category: "Team1"
  namespace: "letsgo-team1-feature"
  worldid: "54"
  url_LetsGo: "tcp://test.ymzx.qq.com:12054"
}
rows {
  id: 67
  servername: "letsgo-mw-develop"
  owner: "mw-开发服"
  category: "LetsGo"
  namespace: "letsgo-mw-develop"
  worldid: "55"
  url_LetsGo: "tcp://test.ymzx.qq.com:12055"
}
rows {
  id: 68
  servername: "letsgo-mdev2"
  owner: "S13DEV"
  category: "LetsGo"
  namespace: "letsgo-mdev2"
  worldid: "61"
  url_LetsGo: "tcp://test.ymzx.qq.com:12061"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16061"
}
rows {
  id: 69
  servername: "letsgo-ugcmap"
  owner: "M8地图服务器"
  category: "UGC"
  namespace: "letsgo-ugcmap"
  worldid: "62"
  url_LetsGo: "tcp://test.ymzx.qq.com:12062"
}
rows {
  id: 70
  servername: "ac-dstest"
  owner: "ac-dstest"
  category: "DSTest"
  namespace: "user00_9_135_5_44"
  worldid: "33"
  url_LetsGo: "tcp://9.135.5.44:8201"
}
rows {
  id: 71
  servername: "letsgo-community"
  owner: "letsgo-community"
  category: "Feature"
  namespace: "letsgo-community"
  worldid: "63"
  url_LetsGo: "tcp://test.ymzx.qq.com:12063"
}
rows {
  id: 72
  servername: "letsgo-yunying"
  owner: "运营专用1服"
  category: "LetsGo"
  namespace: "letsgo-yunying"
  worldid: "65"
  url_LetsGo: "tcp://test.ymzx.qq.com:12065"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16065"
}
rows {
  id: 73
  servername: "vmiaochen"
  owner: "vmiaochen"
  category: "Feature"
  namespace: "vmiaochen_9_134_130_41"
  worldid: "32"
  url_LetsGo: "tcp://9.134.130.41:8201"
}
rows {
  id: 74
  servername: "cppzhang"
  owner: "cppzhang"
  category: "Feature"
  namespace: "cppzhang_9_135_62_77"
  worldid: "33"
  url_LetsGo: "tcp://9.135.62.77:8201"
}
rows {
  id: 75
  servername: "jianyongli"
  owner: "jianyongli"
  category: "Feature"
  namespace: "jianyongli_9_134_3_138"
  worldid: "34"
  url_LetsGo: "tcp://9.134.3.138:8201"
}
rows {
  id: 76
  servername: "letsgo-serverown"
  owner: "服务器专属"
  category: "DSTest"
  namespace: "letsgo-serverown"
  worldid: "67"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12067"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16067"
}
rows {
  id: 77
  servername: "letsgo-master"
  owner: "内网稳定服"
  category: "LetsGo"
  namespace: "letsgo-masterweekly"
  worldid: "68"
  url_LetsGo: "tcp://test.ymzx.qq.com:12068"
}
rows {
  id: 78
  servername: "letsgo-m5ugcmap"
  owner: "letsgo-m5ugcmap"
  category: "Feature"
  namespace: "letsgo-m5ugcmap"
  worldid: "77"
  url_LetsGo: "tcp://test.ymzx.qq.com:12077"
}
rows {
  id: 79
  servername: "letsgo-nr3feat"
  owner: "NR3E稳定服"
  category: "Feature"
  namespace: "letsgo-nr3feat"
  worldid: "72"
  url_LetsGo: "tcp://test.ymzx.qq.com:12072"
}
rows {
  id: 80
  servername: "letsgo-nr3e-dev"
  owner: "NR3E开发服"
  category: "Feature"
  namespace: "letsgo-nr3e-dev"
  worldid: "101"
  url_LetsGo: "tcp://test.ymzx.qq.com:12101"
}
rows {
  id: 81
  servername: "letsgo-nr3e-competition"
  owner: "NR3E赛事服"
  category: "Feature"
  namespace: "letsgo-nr3e-competition"
  worldid: "107"
  url_LetsGo: "tcp://test.ymzx.qq.com:12107"
}
rows {
  id: 82
  servername: "letsgo-nr3e-dstest"
  owner: "NR3E-dstest"
  category: "DSTest"
  namespace: "user00_9_135_0_138"
  worldid: "81"
  url_LetsGo: "tcp://9.135.0.138:8201"
}
rows {
  id: 83
  servername: "letsgo-ympress"
  owner: "元梦压测服"
  category: "LetsGo"
  namespace: "letsgo-ympress"
  worldid: "35"
  url_LetsGo: "tcp://press.ymzx.qq.com:12035"
}
rows {
  id: 84
  servername: "letsgo-sr-test"
  owner: "SR测试服"
  category: "Test"
  namespace: "letsgo-sr-test"
  worldid: "89"
  url_LetsGo: "tcp://test.ymzx.qq.com:12089"
}
rows {
  id: 85
  servername: "letsgo-out-xp"
  owner: "对外体验服"
  category: "Test"
  namespace: "letsgo-out-xp"
  worldid: "90"
  url_LetsGo: "tcp://test.ymzx.qq.com:12090"
}
rows {
  id: 86
  servername: "letsgo-stablepluswx"
  owner: "热更测试2服"
  category: "热更新服务组"
  namespace: "letsgo-stablepluswx"
  worldid: "179"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12179"
}
rows {
  id: 87
  servername: "letsgo-collect-perf"
  owner: "客户端性能收集"
  category: "Test"
  namespace: "letsgo-collect-perf"
  worldid: "97"
  url_LetsGo: "tcp://test.ymzx.qq.com:12097"
}
rows {
  id: 88
  servername: "letsgo-boundary-test"
  owner: "服务器边界测试"
  category: "Test"
  namespace: "letsgo-boundary-test"
  worldid: "98"
  url_LetsGo: "tcp://test.ymzx.qq.com:12098"
}
rows {
  id: 89
  servername: "letsgo-maingame"
  owner: "主玩法测试服"
  category: "LetsGo"
  namespace: "letsgo-maingame"
  worldid: "104"
  url_LetsGo: "tcp://test.ymzx.qq.com:12104"
}
rows {
  id: 90
  servername: "letsgo-dspress"
  owner: "dspress压测服"
  category: "Test"
  namespace: "letsgo-dspress"
  worldid: "131"
  url_LetsGo: "tcp://press.ymzx.qq.com:12131"
}
rows {
  id: 91
  servername: "letsgo-hakulapress"
  owner: "专属压测环境"
  category: "Test"
  namespace: "letsgo-hakulapress"
  worldid: "86"
  url_LetsGo: "tcp://press.ymzx.qq.com:12086"
}
rows {
  id: 92
  servername: "wallenwang"
  owner: "wallenwang"
  category: "Personal"
  namespace: "wallenwang_9_135_11_155"
  worldid: "32"
  url_LetsGo: "tcp://9.135.11.155:8201"
}
rows {
  id: 93
  servername: "letsgo-shoot"
  owner: "m12shoot日构建"
  category: "FPS"
  namespace: "letsgo-shoot"
  worldid: "25"
  url_LetsGo: "tcp://test.ymzx.qq.com:12025"
}
rows {
  id: 94
  servername: "letsgo-shootdev"
  owner: "m12shoot开发"
  category: "FPS"
  namespace: "letsgo-shootdev"
  worldid: "49"
  url_LetsGo: "tcp://test.ymzx.qq.com:12049"
}
rows {
  id: 95
  servername: "ac-dstest"
  owner: "ac-dstest"
  category: "Feature"
  namespace: "user00_9_135_5_44"
  worldid: "33"
  url_LetsGo: "tcp://9.135.5.44:8201"
}
rows {
  id: 96
  servername: "letsgo-boss"
  owner: "内部体验服"
  category: "LetsGo"
  namespace: "letsgo-boss"
  worldid: "70"
  url_LetsGo: "tcp://test.ymzx.qq.com:12070"
}
rows {
  id: 97
  servername: "letsgo-m5ugcmap"
  owner: "letsgo-m5ugcmap"
  category: "Feature"
  namespace: "letsgo-m5ugcmap"
  worldid: "77"
  url_LetsGo: "tcp://test.ymzx.qq.com:12077"
}
rows {
  id: 98
  servername: "letsgo-betaqq"
  owner: "M6正式服(DM)"
  category: "LetsGo"
  namespace: "letsgo-betawx"
  worldid: "15"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12015"
}
rows {
  id: 99
  servername: "letsgo-iosreview"
  owner: "苹果审核服"
  category: "LetsGo"
  namespace: "letsgo-iosreview"
  worldid: "14"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12014"
  servername_ios_review: "letsgo-iosreview2"
}
rows {
  id: 100
  servername: "letsgo-xiaowo2"
  owner: "小窝测试2服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo2"
  worldid: "57"
  url_LetsGo: "tcp://test.ymzx.qq.com:12057"
}
rows {
  id: 101
  servername: "letsgo-xiaowo3"
  owner: "小窝测试3服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo3"
  worldid: "58"
  url_LetsGo: "tcp://test.ymzx.qq.com:12058"
}
rows {
  id: 102
  servername: "letsgo-xiaowo4"
  owner: "小窝测试4服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo4"
  worldid: "59"
  url_LetsGo: "tcp://test.ymzx.qq.com:12059"
}
rows {
  id: 103
  servername: "letsgo-xiaowo5"
  owner: "小窝开发5服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo5"
  worldid: "83"
  url_LetsGo: "tcp://test.ymzx.qq.com:12083"
}
rows {
  id: 104
  servername: "letsgo-xiaowo6"
  owner: "小窝测试6服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo6"
  worldid: "84"
  url_LetsGo: "tcp://test.ymzx.qq.com:12084"
}
rows {
  id: 105
  servername: "letsgo-xiaowo7"
  owner: "小窝测试7服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo7"
  worldid: "105"
  url_LetsGo: "tcp://test.ymzx.qq.com:12105"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16105"
}
rows {
  id: 106
  servername: "letsgo-xiaowo8"
  owner: "小窝测试8服"
  category: "xiaowo"
  namespace: "letsgo-xiaowo8"
  worldid: "106"
  url_LetsGo: "tcp://test.ymzx.qq.com:12106"
}
rows {
  id: 107
  servername: "letsgo-roomplay"
  owner: "roomplay"
  category: "Feature"
  namespace: "letsgo-roomplay"
  worldid: "95"
  url_LetsGo: "tcp://test.ymzx.qq.com:12095"
}
rows {
  id: 108
  servername: "letsgo-pa-physx"
  owner: "PartyAnimal_PhysX"
  category: "Feature"
  namespace: "letsgo-pa-physx"
  worldid: "99"
  url_LetsGo: "tcp://test.ymzx.qq.com:12099"
}
rows {
  id: 109
  servername: "glueli"
  owner: "glueli"
  category: "Personal"
  namespace: "glueli_9_135_63_224"
  worldid: "68"
  url_LetsGo: "tcp://9.135.63.224:8201"
}
rows {
  id: 110
  servername: "letsgo-engine-dev"
  owner: "engine-dev服"
  category: "Feature"
  namespace: "letsgo-engine-dev"
  worldid: "108"
  url_LetsGo: "tcp://test.ymzx.qq.com:12108"
}
rows {
  id: 111
  servername: "letsgo-team1-feature-game"
  owner: "team1-feature-game"
  category: "Feature"
  namespace: "letsgo-team1-feature-game"
  worldid: "119"
  url_LetsGo: "tcp://test.ymzx.qq.com:12119"
}
rows {
  id: 112
  servername: "letsgo-featureui"
  owner: "featureui"
  category: "Feature"
  namespace: "letsgo-featureui"
  worldid: "100"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12100"
}
rows {
  id: 113
  servername: "letsgo-engine-maingame"
  owner: "引擎组MainGame服"
  category: "Feature"
  namespace: "letsgo-engine-maingame"
  worldid: "116"
  url_LetsGo: "tcp://test.ymzx.qq.com:12116"
}
rows {
  id: 114
  servername: "letsgo-pressqq"
  owner: "QQ压测服"
  category: "Test"
  namespace: "letsgo-pressqq"
  worldid: "75"
  url_LetsGo: "tcp://press.ymzx.qq.com:12075"
  config_url_LetsGo: "tcp://test4config3.ymzx.qq.com:16075"
}
rows {
  id: 115
  servername: "community-dstest"
  owner: "community-dstest"
  category: "DSTest"
  namespace: "user00_9_135_0_102"
  worldid: "194"
  url_LetsGo: "tcp://9.135.0.102:8201"
}
rows {
  id: 116
  servername: "letsgo-maingame-master"
  owner: "主玩法稳定服"
  category: "LetsGo"
  namespace: "letsgo-maingame-master"
  worldid: "122"
  url_LetsGo: "tcp://test.ymzx.qq.com:12122"
}
rows {
  id: 117
  servername: "zhoujiewang"
  owner: "zhoujiewang"
  category: "Feature"
  namespace: "zhoujiewang_9_134_5_187"
  worldid: "66"
  url_LetsGo: "tcp://9.134.5.187:8201"
}
rows {
  id: 118
  servername: "peterlccli"
  owner: "peterlccli"
  category: "Feature"
  namespace: "peterlccli_9_134_233_244"
  worldid: "64"
  url_LetsGo: "tcp://9.134.233.244:8201"
}
rows {
  id: 119
  servername: "luciusli"
  owner: "luciusli"
  category: "Feature"
  namespace: "luciusli_9_134_131_80"
  worldid: "88"
  url_LetsGo: "tcp://9.134.131.80:8201"
}
rows {
  id: 120
  servername: "leeoli"
  owner: "leeoli"
  category: "Feature"
  namespace: "leeoli_9_134_65_49"
  worldid: "35"
  url_LetsGo: "tcp://9.134.65.49:8201"
}
rows {
  id: 121
  servername: "cranezhou"
  owner: "cranezhou"
  category: "Feature"
  namespace: "cranezhou_9_134_147_6"
  worldid: "61"
  url_LetsGo: "tcp://9.134.147.6:8201"
}
rows {
  id: 122
  servername: "letsgo-autopress"
  owner: "自动压测环境"
  category: "Press"
  namespace: "letsgo-autopress"
  worldid: "64"
  url_LetsGo: "tcp://press.ymzx.qq.com:12064"
}
rows {
  id: 123
  servername: "letsgo-autopress-xiaowo"
  owner: "自动压测环境-小窝"
  category: "Press"
  namespace: "letsgo-autopress-xiaowo"
  worldid: "93"
  url_LetsGo: "tcp://press.ymzx.qq.com:12093"
}
rows {
  id: 124
  servername: "letsgo-ddp"
  owner: "GamePlayProfile专用"
  category: "Test"
  namespace: "letsgo-ddp"
  worldid: "128"
  url_LetsGo: "tcp://test.ymzx.qq.com:12128"
}
rows {
  id: 125
  servername: "letsgo-ai-dev"
  owner: "AI体验服-开发"
  category: "Feature"
  namespace: "letsgo-ai-dev"
  worldid: "121"
  url_LetsGo: "tcp://test.ymzx.qq.com:12121"
}
rows {
  id: 126
  servername: "letsgo-cloudgame"
  owner: "云游戏专用服"
  category: "Feature"
  namespace: "letsgo-cloudgame"
  worldid: "130"
  url_LetsGo: "tcp://test.ymzx.qq.com:12130"
}
rows {
  id: 127
  servername: "zhengqhuang"
  owner: "zhengqhuang"
  category: "Personal"
  namespace: "zhengqhuang_9_134_132_153"
  worldid: "50"
  url_LetsGo: "tcp://9.134.132.153:8201"
}
rows {
  id: 128
  servername: "letsgo-morecluster"
  owner: "dsasvr多集群测试环境"
  category: "Test"
  namespace: "letsgo-morecluster"
  worldid: "132"
  url_LetsGo: "tcp://test.ymzx.qq.com:12132"
}
rows {
  id: 129
  servername: "letsgo-ugctimi"
  owner: "UGCTimiContest"
  category: "UGC"
  namespace: "letsgo-ugctimi"
  worldid: "21"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12021"
}
rows {
  id: 130
  servername: "letsgo-iosreview2"
  owner: "苹果审核服2"
  category: "LetsGo"
  namespace: "letsgo-iosreview2"
  worldid: "200"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12200"
  config_url_LetsGo: "tcp://beta.ymzx.qq.com:16200"
}
rows {
  id: 131
  servername: "letsgo-m7ugcmap"
  owner: "ugc-M7地图服务器"
  category: "UGC"
  namespace: "letsgo-m7ugcmap"
  worldid: "135"
  url_LetsGo: "tcp://test.ymzx.qq.com:12135"
}
rows {
  id: 132
  servername: "letsgo-localai"
  owner: "AI专用服-localai"
  category: "Feature"
  namespace: "letsgo-localai"
  worldid: "126"
  url_LetsGo: "tcp://test.ymzx.qq.com:12126"
}
rows {
  id: 133
  servername: "letsgo-shootai"
  owner: "FPS AI"
  category: "FPS"
  namespace: "letsgo-shootai"
  worldid: "127"
  url_LetsGo: "tcp://test.ymzx.qq.com:12127"
}
rows {
  id: 134
  servername: "letsgo-qapress1"
  owner: "QA压测服1"
  category: "Press"
  namespace: "letsgo-qapress1"
  worldid: "137"
  url_LetsGo: "tcp://press.ymzx.qq.com:12137"
}
rows {
  id: 135
  servername: "letsgo-qapress2"
  owner: "QA压测服2"
  category: "Press"
  namespace: "letsgo-qapress2"
  worldid: "138"
  url_LetsGo: "tcp://press.ymzx.qq.com:12138"
  config_url_LetsGo: "tcp://test4config3.ymzx.qq.com:16138"
}
rows {
  id: 137
  servername: "letsgo-qapress4"
  owner: "QA压测服4"
  category: "Press"
  namespace: "letsgo-qapress4"
  worldid: "145"
  url_LetsGo: "tcp://press.ymzx.qq.com:12145"
}
rows {
  id: 138
  servername: "letsgo-serverown-1"
  owner: "服务器专属-1"
  category: "DSTest"
  namespace: "letsgo-serverown-1"
  worldid: "142"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12142"
}
rows {
  id: 139
  servername: "maingame-dstest"
  owner: "maingame-dstest"
  category: "DSTest"
  namespace: "maingame_9_134_130_47"
  worldid: "196"
  url_LetsGo: "tcp://9.134.130.47:8201"
}
rows {
  id: 140
  servername: "letsgo-release"
  owner: "现网玩家服"
  category: "LetsGo"
  namespace: "letsgo-release"
  worldid: "5"
  url_LetsGo: "tcp://wxdir.ymzx.qq.com:12005"
  servername_ios_review: "letsgo-iosreview2"
  config_url_LetsGo: "tcp://wxdir.ymzx.qq.com:16005"
}
rows {
  id: 141
  servername: "letsgo-qapress5"
  owner: "QA压测服5"
  category: "Press"
  namespace: "letsgo-qapress5"
  worldid: "146"
  url_LetsGo: "tcp://press.ymzx.qq.com:12146"
}
rows {
  id: 142
  servername: "letsgo-hakulapress"
  owner: "QA压测服6"
  category: "Press"
  namespace: "letsgo-hakulapress"
  worldid: "86"
  url_LetsGo: "tcp://press.ymzx.qq.com:12086"
}
rows {
  id: 143
  servername: "sereinsun"
  owner: "sereinsun"
  category: "Personal"
  namespace: "sereinsun_9_135_9_16"
  worldid: "40"
  url_LetsGo: "tcp://9.135.9.16:8201"
}
rows {
  id: 144
  servername: "letsgo-client-perf"
  owner: "机型适配及性能测试"
  category: "Test"
  namespace: "letsgo-client-perf"
  worldid: "147"
  url_LetsGo: "tcp://test.ymzx.qq.com:12147"
}
rows {
  id: 145
  servername: "letsgo-dscpress"
  owner: "letsgo-dscpress"
  category: "Press"
  namespace: "letsgo-dscpress"
  worldid: "136"
  url_LetsGo: "tcp://press.ymzx.qq.com:12136"
}
rows {
  id: 146
  servername: "ningweian"
  owner: "ningweian"
  category: "Personal"
  namespace: "ningweian_9_135_5_222"
  worldid: "58"
  url_LetsGo: "tcp://9.135.5.222:8201"
}
rows {
  id: 147
  servername: "yongzyzhang"
  owner: "yongzyzhang"
  category: "Personal"
  namespace: "yongzyzhang_9_134_78_235"
  worldid: "198"
  url_LetsGo: "tcp://9.134.78.235:8201"
}
rows {
  id: 148
  servername: "letsgo-tsg"
  owner: "tycoon-开发测试服"
  category: "Tycoon"
  namespace: "letsgo-tsg"
  worldid: "94"
  url_LetsGo: "tcp://test.ymzx.qq.com:12094"
}
rows {
  id: 149
  servername: "letsgo-tycoon"
  owner: "tycoon-稳定体验服"
  category: "Tycoon"
  namespace: "letsgo-tycoon"
  worldid: "109"
  url_LetsGo: "tcp://test.ymzx.qq.com:12109"
}
rows {
  id: 150
  servername: "tycoon-dstest"
  owner: "tycoon-本地DS测试服"
  category: "Tycoon"
  namespace: "tsg-build_9_135_1_105"
  worldid: "192"
  url_LetsGo: "tcp://9.135.1.105:8201"
}
rows {
  id: 151
  servername: "devinyou"
  owner: "devinyou"
  category: "Tycoon"
  namespace: "devinyou03_9_134_11_87"
  worldid: "188"
  url_LetsGo: "tcp://9.134.11.87:8201"
}
rows {
  id: 152
  servername: "cadengu"
  owner: "cadengu"
  category: "Tycoon"
  namespace: "cadengu_9_135_57_122"
  worldid: "189"
  url_LetsGo: "tcp://9.135.57.122:8201"
}
rows {
  id: 153
  servername: "rentaohu"
  owner: "rentaohu"
  category: "Tycoon"
  namespace: "rentaohu_9_135_56_242"
  worldid: "190"
  url_LetsGo: "tcp://9.135.56.242:8201"
}
rows {
  id: 154
  servername: "letsgo-tycoon-merge"
  owner: "tycoon-分支测试服"
  category: "Tycoon"
  namespace: "letsgo-tycoon-merge"
  worldid: "110"
  url_LetsGo: "tcp://test.ymzx.qq.com:12110"
}
rows {
  id: 155
  servername: "tycoon-205"
  owner: "tycoon-205"
  category: "Tycoon"
  namespace: "user00_9_143_103_125"
  worldid: "193"
  url_LetsGo: "tcp://101.89.42.23:13002"
}
rows {
  id: 156
  servername: "letsgo-tycoon-test"
  owner: "tycoon-QA测试服"
  category: "Tycoon"
  namespace: "letsgo-tycoon-test"
  worldid: "111"
  url_LetsGo: "tcp://test.ymzx.qq.com:12111"
}
rows {
  id: 157
  servername: "zihaoyuan"
  owner: "zihaoyuan"
  category: "Feature"
  namespace: "zihaoyuan_9_135_57_67"
  worldid: "195"
  url_LetsGo: "tcp://9.135.57.67:8201"
}
rows {
  id: 158
  servername: "tycoon-204"
  owner: "tycoon-快速验证服"
  category: "Tycoon"
  namespace: "user00_30_41_108_20"
  worldid: "196"
  url_LetsGo: "tcp://101.89.42.23:12305"
}
rows {
  id: 159
  servername: "tycoon-moba-localDS"
  owner: "tycoon-moba-localDS"
  category: "Tycoon"
  namespace: "user00_9_143_102_183"
  worldid: "197"
  url_LetsGo: "tcp://101.89.42.23:13004"
}
rows {
  id: 160
  servername: "letsgo-tycoon-feat"
  owner: "tycoon-兽人"
  category: "Tycoon"
  namespace: "letsgo-tycoon-feat"
  worldid: "110"
  url_LetsGo: "tcp://test.ymzx.qq.com:12114"
}
rows {
  id: 161
  servername: "letsgo-tycoon-ugc"
  owner: "tycoon-ugc测试服"
  category: "Tycoon"
  namespace: "letsgo-tycoon-ugc"
  worldid: "112"
  url_LetsGo: "tcp://test.ymzx.qq.com:12112"
}
rows {
  id: 162
  servername: "letsgo-ugc-rpg"
  owner: "ugc-rpg"
  category: "Feature"
  namespace: "letsgo-ugc-rpg"
  worldid: "160"
  url_LetsGo: "tcp://test.ymzx.qq.com:12160"
}
rows {
  id: 163
  servername: "letsgo-ugc-vehicle"
  owner: "UGC载具"
  category: "Feature"
  namespace: "letsgo-ugc-vehicle"
  worldid: "95"
  url_LetsGo: "tcp://test.ymzx.qq.com:12095"
}
rows {
  id: 164
  servername: "lifulfzhang"
  owner: "lifulfzhang"
  category: "Personal"
  namespace: "lifulfzhang_9_135_56_198"
  worldid: "199"
  url_LetsGo: "tcp://9.135.56.198:8201"
}
rows {
  id: 165
  servername: "albertwu"
  owner: "albertwu"
  category: "Personal"
  namespace: "albertwu_9_134_35_229"
  worldid: "84"
  url_LetsGo: "tcp://9.134.35.229:8201"
}
rows {
  id: 166
  servername: "dannisxiao"
  owner: "dannisxiao"
  category: "Personal"
  namespace: "dannisxiao_9_134_42_164"
  worldid: "86"
  url_LetsGo: "tcp://9.134.42.164:8201"
}
rows {
  id: 167
  servername: "qianjieli"
  owner: "qianjieli"
  category: "Personal"
  namespace: "qianjieli_9_134_188_60"
  worldid: "302"
  url_LetsGo: "tcp://9.134.188.60:8201"
}
rows {
  id: 168
  servername: "hhshan"
  owner: "hhshan"
  category: "Personal"
  namespace: "hhshan_9_134_193_31"
  worldid: "200"
  url_LetsGo: "tcp://9.134.193.31:8201"
}
rows {
  id: 169
  servername: "letsgo-huodong"
  owner: "运营验收服"
  category: "Test"
  namespace: "letsgo-huodong"
  worldid: "171"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12171"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16171"
}
rows {
  id: 170
  servername: "letsgo-adapt2"
  owner: "适配测试2服"
  category: "LetsGo"
  namespace: "letsgo-adapt2"
  worldid: "154"
  url_LetsGo: "tcp://press.ymzx.qq.com:12154"
  config_url_LetsGo: "tcp://test4config3.ymzx.qq.com:16154"
}
rows {
  id: 171
  servername: "letsgo-pioneer"
  owner: "先遣1服-公共"
  category: "LetsGo"
  namespace: "letsgo-pioneer"
  worldid: "210"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12210"
  servername_ios_review: "letsgo-pioneer-review"
  config_url_LetsGo: "tcp://beta.ymzx.qq.com:16210"
}
rows {
  id: 172
  servername: "letsgo-acddb"
  owner: "躲避球测试服"
  category: "Feature"
  namespace: "letsgo-acddb"
  worldid: "178"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12178"
}
rows {
  id: 174
  servername: "dexterzhu2"
  owner: "dexterzhu-2"
  category: "Personal"
  namespace: "dexterzhu_9_134_112_21"
  worldid: "52"
  url_LetsGo: "tcp://9.134.112.21:8201"
}
rows {
  id: 175
  servername: "letsgo-shootdev2"
  owner: "m11shoot开发2"
  category: "FPS"
  namespace: "letsgo-shootdev2"
  worldid: "82"
  url_LetsGo: "tcp://test.ymzx.qq.com:12082"
}
rows {
  id: 176
  servername: "letsgo-shootdaily2"
  owner: "m11shoot日构建2"
  category: "FPS"
  namespace: "letsgo-shootdaily2"
  worldid: "36"
  url_LetsGo: "tcp://test.ymzx.qq.com:12036"
}
rows {
  id: 177
  servername: "lionelzhang"
  owner: "张荣1服"
  category: "Personal"
  namespace: "lionelzhang_9_134_112_4"
  worldid: "53"
  url_LetsGo: "tcp://9.134.112.4:8201"
}
rows {
  id: 178
  servername: "zhengqhuang-2"
  owner: "zhengqhuang-2"
  category: "Personal"
  namespace: "zhengqhuang_21_6_0_188"
  worldid: "51"
  url_LetsGo: "tcp://21.6.0.188:8201"
}
rows {
  id: 179
  servername: "dstest_coc_118_179"
  owner: "dstest_coc_118_179"
  category: "COC"
  namespace: "dstest_9_134_118_179"
  worldid: "202"
  url_LetsGo: "tcp://9.134.118.179:8201"
}
rows {
  id: 180
  servername: "main-dstest"
  owner: "main-dstest"
  category: "DSTest"
  namespace: "dstest_9_134_120_54"
  worldid: "203"
  url_LetsGo: "tcp://9.134.120.54:8201"
}
rows {
  id: 181
  servername: "dstest_121_209"
  owner: "dstest_121_209"
  category: "DSTest"
  namespace: "dstest_9_134_121_209"
  worldid: "204"
  url_LetsGo: "tcp://9.134.121.209:8201"
}
rows {
  id: 182
  servername: "dstest_127_28"
  owner: "dstest_127_28"
  category: "DSTest"
  namespace: "dstest_9_134_127_28"
  worldid: "205"
  url_LetsGo: "tcp://9.134.127.28:8201"
}
rows {
  id: 183
  servername: "letsgo-starp"
  owner: "starP"
  category: "Feature"
  namespace: "letsgo-starp"
  worldid: "183"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12183"
}
rows {
  id: 184
  servername: "hunkjhwang"
  owner: "hunkjhwang"
  category: "Personal"
  namespace: "user00_9_134_196_151"
  worldid: "206"
  url_LetsGo: "tcp://9.134.196.151:8201"
}
rows {
  id: 185
  servername: "letsgo-betaqq"
  owner: "M6正式服(数据合并)"
  category: "DataMerge"
  namespace: "letsgo-betawx"
  worldid: "15"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12015"
  servername_ios_review: "letsgo-iosreview2"
}
rows {
  id: 186
  servername: "letsgo-esports"
  owner: "电竞赛事服"
  category: "LetsGo"
  namespace: "letsgo-esports"
  worldid: "208"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12208"
}
rows {
  id: 187
  servername: "letsgo-engine-dev2"
  owner: "engine-dev2服"
  category: "Feature"
  namespace: "letsgo-engine-dev2"
  worldid: "189"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12189"
}
rows {
  id: 188
  servername: "lantingxu"
  owner: "lantingxu"
  category: "Personal"
  namespace: "lantingxu_9_135_63_85"
  worldid: "220"
  url_LetsGo: "tcp://9.135.63.85:8201"
}
rows {
  id: 189
  servername: "letsgo-frame"
  owner: "frame"
  category: "Personal"
  namespace: "letsgo-frame"
  worldid: "215"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12215"
}
rows {
  id: 190
  servername: "letsgo-pre"
  owner: "常规预发布1"
  category: "LetsGo"
  namespace: "letsgo-pre"
  worldid: "12"
  url_LetsGo: "tcp://pretest.ymzx.qq.com:12012"
  config_url_LetsGo: "tcp://pretest.ymzx.qq.com:16012"
}
rows {
  id: 191
  servername: "letsgo-prewx"
  owner: "常规预发布2"
  category: "LetsGo"
  namespace: "letsgo-prewx"
  worldid: "60"
  url_LetsGo: "tcp://pretest.ymzx.qq.com:12060"
  config_url_LetsGo: "tcp://pretest.ymzx.qq.com:16060"
}
rows {
  id: 192
  servername: "letsgo-pre-time"
  owner: "常规预发布时间1"
  category: "LetsGo"
  namespace: "letsgo-pre-time"
  worldid: "102"
  url_LetsGo: "tcp://pretest.ymzx.qq.com:12102"
  config_url_LetsGo: "tcp://pretest.ymzx.qq.com:16102"
}
rows {
  id: 193
  servername: "letsgo-prewx-time"
  owner: "常规预发布时间2"
  category: "LetsGo"
  namespace: "letsgo-prewx-time"
  worldid: "103"
  url_LetsGo: "tcp://pretest.ymzx.qq.com:12103"
  config_url_LetsGo: "tcp://pretest.ymzx.qq.com:16103"
}
rows {
  id: 194
  servername: "letsgo-yunyingpre"
  owner: "运营预发布1"
  category: "LetsGo"
  namespace: "letsgo-yunyingpre"
  worldid: "172"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12172"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16172"
}
rows {
  id: 195
  servername: "letsgo-yunyingprewx"
  owner: "运营预发布2"
  category: "LetsGo"
  namespace: "letsgo-yunyingprewx"
  worldid: "173"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12173"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16173"
}
rows {
  id: 196
  servername: "letsgo-yunyingpre-time"
  owner: "运营预发布时间1"
  category: "LetsGo"
  namespace: "letsgo-yunyingpre-time"
  worldid: "174"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12174"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16174"
}
rows {
  id: 197
  servername: "letsgo-yunyingprewx-time"
  owner: "运营预发布时间2"
  category: "LetsGo"
  namespace: "letsgo-yunyingprewx-time"
  worldid: "175"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12175"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16175"
}
rows {
  id: 198
  servername: "letsgo-newpre"
  owner: "紧急预发布1"
  category: "LetsGo"
  namespace: "letsgo-newpre"
  worldid: "184"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12184"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16184"
}
rows {
  id: 199
  servername: "letsgo-newprewx"
  owner: "紧急预发布2"
  category: "LetsGo"
  namespace: "letsgo-newprewx"
  worldid: "185"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12185"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16185"
}
rows {
  id: 200
  servername: "letsgo-newpre-time"
  owner: "紧急预发布时间1"
  category: "LetsGo"
  namespace: "letsgo-newpre-time"
  worldid: "186"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12186"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16186"
}
rows {
  id: 201
  servername: "letsgo-newprewx-time"
  owner: "紧急预发布时间2"
  category: "LetsGo"
  namespace: "letsgo-newprewx-time"
  worldid: "187"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12187"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16187"
}
rows {
  id: 202
  servername: "letsgo-qa"
  owner: "QA1服"
  category: "QA"
  namespace: "letsgo-qa"
  worldid: "9"
  url_LetsGo: "tcp://test.ymzx.qq.com:12009"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16009"
}
rows {
  id: 203
  servername: "letsgo-qa-wx"
  owner: "QA2服"
  category: "QA"
  namespace: "letsgo-qa-wx"
  worldid: "92"
  url_LetsGo: "tcp://test.ymzx.qq.com:12092"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16092"
}
rows {
  id: 204
  servername: "letsgo-time"
  owner: "QA改时间服"
  category: "QA"
  namespace: "letsgo-time"
  worldid: "26"
  url_LetsGo: "tcp://test.ymzx.qq.com:12026"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16026"
}
rows {
  id: 205
  servername: "letsgo-qa-dev1-qq"
  owner: "QA主干1服"
  category: "QA"
  namespace: "letsgo-qa-dev1-qq"
  worldid: "168"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12168"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16168"
}
rows {
  id: 206
  servername: "letsgo-qa-dev1-wx"
  owner: "QA主干2服"
  category: "QA"
  namespace: "letsgo-qa-dev1-wx"
  worldid: "169"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12169"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16169"
}
rows {
  id: 207
  servername: "letsgo-qa-dev2"
  owner: "QA主干3服"
  category: "QA"
  namespace: "letsgo-qa-dev2"
  worldid: "170"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12170"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16170"
}
rows {
  id: 208
  servername: "letsgo-releaseqa"
  owner: "QA Release1服"
  category: "QA"
  namespace: "letsgo-releaseqa"
  worldid: "50"
  url_LetsGo: "tcp://test.ymzx.qq.com:12050"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16050"
}
rows {
  id: 209
  servername: "letsgo-releaseqawx"
  owner: "QA Release2服"
  category: "QA"
  namespace: "letsgo-releaseqawx"
  worldid: "69"
  url_LetsGo: "tcp://test.ymzx.qq.com:12069"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16069"
}
rows {
  id: 210
  servername: "letsgo-releasetime"
  owner: "QA Release改时间服"
  category: "QA"
  namespace: "letsgo-releasetime"
  worldid: "51"
  url_LetsGo: "tcp://test.ymzx.qq.com:12051"
  config_url_LetsGo: "tcp://test4config1.ymzx.qq.com:16051"
}
rows {
  id: 211
  servername: "letsgo-qa-activity1-qq"
  owner: "QA活动1服"
  category: "QA"
  namespace: "letsgo-qa-activity1-qq"
  worldid: "165"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12165"
}
rows {
  id: 212
  servername: "letsgo-qa-activity1-wx"
  owner: "QA活动2服"
  category: "QA"
  namespace: "letsgo-qa-activity1-wx"
  worldid: "166"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12166"
}
rows {
  id: 213
  servername: "letsgo-qa-activity2"
  owner: "QA活动3服"
  category: "QA"
  namespace: "letsgo-qa-activity2"
  worldid: "167"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12167"
}
rows {
  id: 214
  servername: "letsgo-nr3e-new-dstest"
  owner: "NR3E-new-dstest"
  category: "DSTest"
  namespace: "user00_9_134_75_252"
  worldid: "223"
  url_LetsGo: "tcp://************:8201"
}
rows {
  id: 215
  servername: "letsgo_custom"
  owner: "自定义ip服"
  category: "Custom"
  worldid: "500"
  url_LetsGo: "tcp://%s:8201"
}
rows {
  id: 216
  servername: "letsgo-ugclobby"
  owner: "ugc演唱会"
  category: "LetsGo"
  namespace: "letsgo-ugclobby"
  worldid: "216"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12216"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16216"
}
rows {
  id: 217
  servername: "letsgo-engine-cros"
  owner: "引擎cros"
  category: "LetsGo"
  namespace: "letsgo-engine-cros"
  worldid: "217"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12217"
}
rows {
  id: 218
  servername: "peterlccli2"
  owner: "peterlccli2"
  category: "Feature"
  namespace: "peterlccli_9_135_5_156"
  worldid: "252"
  url_LetsGo: "tcp://***********:8201"
}
rows {
  id: 219
  servername: "letsgo-acddb-stable"
  owner: "躲避球测试服-开发分支"
  category: "Feature"
  namespace: "letsgo-acddb-stable"
  worldid: "226"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12226"
}
rows {
  id: 220
  servername: "letsgo-acddb-pretest"
  owner: "躲避球测试2服"
  category: "Feature"
  namespace: "letsgo-acddb-pretest"
  worldid: "227"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12227"
}
rows {
  id: 221
  servername: "letsgo-ugcapp"
  owner: "ugcapp"
  category: "UGC"
  namespace: "letsgo-ugcapp"
  worldid: "230"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12230"
}
rows {
  id: 222
  servername: "letsgo-custom-dev1"
  owner: "custom-dev1"
  category: "CustomDev"
  namespace: "letsgo-custom-dev1"
  worldid: "224"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12224"
}
rows {
  id: 223
  servername: "letsgo-custom-dev2"
  owner: "custom-dev2"
  category: "CustomDev"
  namespace: "letsgo-custom-dev2"
  worldid: "248"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12248"
}
rows {
  id: 224
  servername: "letsgo-custom-dev3"
  owner: "custom-dev3"
  category: "CustomDev"
  namespace: "letsgo-custom-dev3"
  worldid: "231"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12231"
}
rows {
  id: 225
  servername: "letsgo-custom-dev4"
  owner: "custom-dev4"
  category: "CustomDev"
  namespace: "letsgo-custom-dev4"
  worldid: "241"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12241"
}
rows {
  id: 226
  servername: "letsgo-custom-dev5"
  owner: "custom-dev5"
  category: "CustomDev"
  namespace: "letsgo-custom-dev5"
  worldid: "242"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12242"
}
rows {
  id: 227
  servername: "letsgo-custom-dev6"
  owner: "custom-dev6"
  category: "CustomDev"
  namespace: "letsgo-custom-dev6"
  worldid: "243"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12243"
}
rows {
  id: 228
  servername: "letsgo-custom-dev7"
  owner: "custom-dev7"
  category: "CustomDev"
  namespace: "letsgo-custom-dev7"
  worldid: "244"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12244"
}
rows {
  id: 229
  servername: "letsgo-custom-dev8"
  owner: "custom-dev8"
  category: "CustomDev"
  namespace: "letsgo-custom-dev8"
  worldid: "245"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12245"
}
rows {
  id: 230
  servername: "letsgo-custom-dev9"
  owner: "custom-dev9"
  category: "CustomDev"
  namespace: "letsgo-custom-dev9"
  worldid: "246"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12246"
}
rows {
  id: 231
  servername: "letsgo-custom-dev10"
  owner: "custom-dev10"
  category: "CustomDev"
  namespace: "letsgo-custom-dev10"
  worldid: "247"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12247"
}
rows {
  id: 232
  servername: "letsgo-mdev1"
  owner: "S12DEV"
  category: "LetsGo"
  namespace: "letsgo-mdev1"
  worldid: "261"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12261"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16261"
}
rows {
  id: 233
  servername: "letsgo-stableplus"
  owner: "热更测试1服"
  category: "热更新服务组"
  namespace: "letsgo-stableplus"
  worldid: "96"
  url_LetsGo: "tcp://test.ymzx.qq.com:12096"
}
rows {
  id: 234
  servername: "leeoli2"
  owner: "leeoli私服"
  category: "Feature"
  namespace: "user00_9_134_64_158"
  worldid: "41"
  url_LetsGo: "tcp://9.134.64.158:8201"
}
rows {
  id: 235
  servername: "letsgo-feature-bsm"
  owner: "FeatureBSM"
  category: "Feature"
  namespace: "letsgo-feature-bsm"
  worldid: "262"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12262"
}
rows {
  id: 236
  servername: "hoythuang2"
  owner: "hoythuang2"
  category: "Personal"
  namespace: "hoyt_9_135_56_90"
  worldid: "114"
  url_LetsGo: "tcp://9.135.56.90:8201"
}
rows {
  id: 237
  servername: "letsgo-oversea-cn-test-2"
  owner: "国际化-国内环境-测试2服"
  category: "Oversea"
  namespace: "letsgo-oversea-cn-test-2"
  worldid: "118"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12118"
}
rows {
  id: 238
  servername: "letsgo-oversea-cn-test-1"
  owner: "国际化-国内环境-测试1服"
  category: "Oversea"
  namespace: "letsgo-oversea-cn-test-1"
  worldid: "124"
  url_LetsGo: "tcp://test.ymzx.qq.com:12124"
}
rows {
  id: 239
  servername: "letsgo-pioneer-pre"
  owner: "先遣预发布-公共常用"
  category: "LetsGo"
  namespace: "letsgo-pioneer-pre"
  worldid: "266"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12266"
}
rows {
  id: 240
  servername: "yairliu"
  owner: "yairliu"
  category: "Feature"
  namespace: "yairliu_9_135_63_143"
  worldid: "224"
  url_LetsGo: "tcp://9.135.63.143:8201"
}
rows {
  id: 241
  servername: "jankinye"
  owner: "jankinye"
  category: "Feature"
  namespace: "jankinye_9_135_12_59"
  worldid: "240"
  url_LetsGo: "tcp://9.135.12.59:8201"
}
rows {
  id: 242
  servername: "karenwwu"
  owner: "karenwwu"
  category: "Personal"
  namespace: "root_9_134_130_181"
  worldid: "229"
  url_LetsGo: "tcp://9.134.130.181:8201"
}
rows {
  id: 243
  servername: "mrtang"
  owner: "mrtang"
  category: "Personal"
  namespace: "mrtang_9_134_145_118"
  worldid: "232"
  url_LetsGo: "tcp://9.134.145.118:8201"
}
rows {
  id: 244
  servername: "letsgo-brawlstar-dstest"
  owner: "荒野乱斗-DS测试_DodgeBall_9_135_0_11"
  category: "DSTest"
  namespace: "user00_9_135_0_11"
  worldid: "233"
  url_LetsGo: "tcp://9.135.0.11:8201"
}
rows {
  id: 245
  servername: "letsgo-brawlstar"
  owner: "躲避球测试服-s10dev"
  category: "Feature"
  namespace: "letsgo-brawlstar"
  worldid: "252"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12252"
}
rows {
  id: 247
  servername: "sophiacxsu"
  owner: "sophiacxsu"
  category: "Feature"
  namespace: "sophiacxsu_9_134_144_46"
  worldid: "327"
  url_LetsGo: "tcp://9.134.144.46:8201"
}
rows {
  id: 248
  servername: "letsgo-perf-dev"
  owner: "性能专项-开发"
  category: "性能专项"
  namespace: "letsgo-perf-dev"
  worldid: "232"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12232"
}
rows {
  id: 249
  servername: "shiyisun"
  owner: "shiyisun"
  category: "Tycoon"
  namespace: "shiyisun_9_134_145_122"
  worldid: "228"
  url_LetsGo: "tcp://9.134.145.122:8201"
}
rows {
  id: 250
  servername: "devinyou-new"
  owner: "devinyou-new"
  category: "Tycoon"
  namespace: "devinyou_21_6_0_184"
  worldid: "187"
  url_LetsGo: "tcp://21.6.0.184:8201"
}
rows {
  id: 251
  servername: "letsgo-oversea-cn-test-1"
  owner: "海外-国内idc-测试1服"
  category: "Feature"
  namespace: "letsgo-oversea-cn-test-1"
  worldid: "210"
  url_LetsGo: "tcp://test.ymzx.qq.com:12210"
}
rows {
  id: 252
  servername: "forrestsong"
  owner: "forrestsong"
  category: "Tycoon"
  namespace: "forrestsong_9_134_76_110"
  worldid: "223"
  url_LetsGo: "tcp://9.134.76.110:8201"
}
rows {
  id: 253
  servername: "mingxuanyin"
  owner: "mingxuanyin"
  category: "Tycoon"
  namespace: "mingxuanyin_9_134_145_203"
  worldid: "234"
  url_LetsGo: "tcp://9.134.145.203:8201"
}
rows {
  id: 254
  servername: "letsgo-pioneer-pre2"
  owner: "先遣预发布-公共改时间"
  category: "LetsGo"
  namespace: "letsgo-pioneer-pre2"
  worldid: "270"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12270"
}
rows {
  id: 255
  servername: "letsgo-iosreviewwx2"
  owner: "苹果审核修改时间"
  category: "LetsGo"
  namespace: "letsgo-iosreviewwx2"
  worldid: "201"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12201"
}
rows {
  id: 256
  servername: "letsgo-perf-dev-qa"
  owner: "性能专项-QA"
  category: "性能专项"
  namespace: "letsgo-perf-dev-qa"
  worldid: "275"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12275"
}
rows {
  id: 257
  servername: "loopli"
  owner: "loopli"
  category: "LetsGo"
  namespace: "loopli_9_135_99_43"
  worldid: "235"
  url_LetsGo: "tcp://9.135.99.43:8201"
}
rows {
  id: 258
  servername: "xidali"
  owner: "xidali"
  category: "LetsGo"
  namespace: "xidali_9_135_0_169"
  worldid: "211"
  url_LetsGo: "tcp://9.135.0.169:8201"
}
rows {
  id: 259
  servername: "viviqzhang"
  owner: "viviqzhang"
  category: "Feature"
  namespace: "viviqzhang_9_134_65_128"
  worldid: "155"
  url_LetsGo: "tcp://9.134.65.128:8201"
}
rows {
  id: 260
  servername: "lucasonlu"
  owner: "lucasonlu"
  category: "LetsGo"
  namespace: "lucasonlu_9_134_130_175"
  worldid: "345"
  url_LetsGo: "tcp://9.134.130.175:8201"
}
rows {
  id: 261
  servername: "akeyzou"
  owner: "akeyzou"
  category: "Feature"
  namespace: "akeyzou_9_134_233_71"
  worldid: "238"
  url_LetsGo: "tcp://9.134.233.71:8201"
}
rows {
  id: 262
  servername: "fidelzhang"
  owner: "fidelzhang"
  category: "LetsGo"
  namespace: "fidelzhang_9_135_0_211"
  worldid: "488"
  url_LetsGo: "tcp://9.135.0.211:8201"
}
rows {
  id: 263
  servername: "tianqishi"
  owner: "tianqishi"
  category: "Feature"
  namespace: "tianqishi_9_135_56_59"
  worldid: "255"
  url_LetsGo: "tcp://9.135.56.59:8201"
}
rows {
  id: 264
  servername: "yairliu2"
  owner: "yairliu2"
  category: "Feature"
  namespace: "yairliu_9_134_130_87"
  worldid: "501"
  url_LetsGo: "tcp://9.134.130.87:8201"
}
rows {
  id: 265
  servername: "scottxiao"
  owner: "国一露娜(scottxiao)"
  category: "LetsGo"
  namespace: "scottxiao_9_135_32_218"
  worldid: "210"
  url_LetsGo: "tcp://9.135.32.218:8201"
}
rows {
  id: 266
  servername: "letsgo-coc-test"
  owner: "coc测试服"
  category: "COC"
  namespace: "letsgo-coc-test"
  worldid: "273"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12273"
}
rows {
  id: 268
  servername: "huansyang"
  owner: "huansyang"
  category: "Feature"
  namespace: "huansyang_9_134_236_228"
  worldid: "192"
  url_LetsGo: "tcp://9.134.236.228:8201"
}
rows {
  id: 269
  servername: "emberchen"
  owner: "emberchen"
  category: "Feature"
  namespace: "emberchen_9_134_73_62"
  worldid: "419"
  url_LetsGo: "tcp://9.134.73.62:8201"
}
rows {
  id: 270
  servername: "letsgo-coc-test2"
  owner: "coc测试服2"
  category: "COC"
  namespace: "letsgo-coc-test2"
  worldid: "236"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12236"
}
rows {
  id: 271
  servername: "letsgo-pcreview"
  owner: "PC审核服"
  category: "LetsGo"
  namespace: "letsgo-pcreview"
  worldid: "276"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12276"
}
rows {
  id: 273
  servername: "subfirehu"
  owner: "subfirehu-胡大正"
  category: "Personal"
  namespace: "subfirehu_9_134_145_254"
  worldid: "502"
  url_LetsGo: "tcp://9.134.145.254:8201"
}
rows {
  id: 275
  servername: "tyrojin"
  owner: "tyrojin"
  category: "Personal"
  namespace: "tyrojin_9_134_147_2"
  worldid: "212"
  url_LetsGo: "tcp://9.134.147.2:8201"
}
rows {
  id: 276
  servername: "djangobliu"
  owner: "djangobliu"
  category: "Personal"
  namespace: "djangobliu_9_134_144_131"
  worldid: "73"
  url_LetsGo: "tcp://9.134.144.131:8201"
}
rows {
  id: 277
  servername: "letsgo-backuppre1"
  owner: "备用预发布1"
  category: "LetsGo"
  namespace: "letsgo-backuppre1"
  worldid: "296"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12296"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16296"
}
rows {
  id: 278
  servername: "letsgo-backuppre2"
  owner: "备用预发布2"
  category: "LetsGo"
  namespace: "letsgo-backuppre2"
  worldid: "297"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12297"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16297"
}
rows {
  id: 279
  servername: "letsgo-backuppre3-time"
  owner: "备用预发布3-时间"
  category: "LetsGo"
  namespace: "letsgo-backuppre3-time"
  worldid: "298"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12298"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16298"
}
rows {
  id: 280
  servername: "letsgo-backuppre4-time"
  owner: "备用预发布4-时间"
  category: "LetsGo"
  namespace: "letsgo-backuppre4-time"
  worldid: "299"
  url_LetsGo: "tcp://pretestnew.ymzx.qq.com:12299"
  config_url_LetsGo: "tcp://pretestnew.ymzx.qq.com:16299"
}
rows {
  id: 281
  servername: "letsgo-urgentpre1"
  owner: "应急预发布1"
  category: "LetsGo"
  namespace: "letsgo-urgentpre1"
  worldid: "301"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12301"
  config_url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:16301"
}
rows {
  id: 282
  servername: "letsgo-urgentpre2-time"
  owner: "应急预发布2-时间"
  category: "LetsGo"
  namespace: "letsgo-urgentpre2-time"
  worldid: "302"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12302"
  config_url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:16302"
}
rows {
  id: 283
  servername: "jobhcao"
  owner: "jobhcao曹昊"
  category: "Personal"
  namespace: "root_9_134_147_251"
  worldid: "503"
  url_LetsGo: "tcp://9.134.147.251:8201"
}
rows {
  id: 284
  servername: "arena_Local_DS"
  owner: "arena_Local_DS"
  category: "Feature"
  namespace: "user00_21_6_195_36"
  worldid: "241"
  url_LetsGo: "tcp://21.6.195.36:8201"
}
rows {
  id: 285
  servername: "letsgo-pcreview-dev"
  owner: "PC开发服"
  category: "LetsGo"
  namespace: "letsgo-pcreview-dev"
  worldid: "308"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12308"
}
rows {
  id: 286
  servername: "letsgo-custom-dev11"
  owner: "custom-dev11"
  category: "CustomDev"
  namespace: "letsgo-custom-dev11"
  worldid: "307"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12307"
}
rows {
  id: 287
  servername: "lichao"
  owner: "lichao"
  category: "Personal"
  namespace: "lichao_9_134_247_212"
  worldid: "349"
  url_LetsGo: "tcp://9.134.247.212:8201"
}
rows {
  id: 288
  servername: "letsgo-perf-acddb"
  owner: "躲避球测试服-性能专项"
  category: "Feature"
  namespace: "letsgo-perf-acddb"
  worldid: "309"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12309"
  config_url_LetsGo: "tcp://test4config4.ymzx.qq.com:16309"
}
rows {
  id: 289
  servername: "letsgo-pioneer-pre4"
  owner: "先遣预发布-MOBA专用"
  category: "LetsGo"
  namespace: "letsgo-pioneer-pre4"
  worldid: "195"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12195"
}
rows {
  id: 290
  servername: "letsgo-pioneer3"
  owner: "先遣3服-moba专用"
  category: "LetsGo"
  namespace: "letsgo-pioneer3"
  worldid: "194"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12194"
  servername_ios_review: "letsgo-pioneer-review"
}
rows {
  id: 291
  servername: "longyzhang"
  owner: "longyzhang"
  category: "Personal"
  namespace: "longyzhang_9_135_12_4"
  worldid: "299"
  url_LetsGo: "tcp://9.135.12.4:8201"
}
rows {
  id: 292
  servername: "letsgo-moba-ai"
  owner: "Moba-AI测试"
  category: "Feature"
  namespace: "letsgo-moba-ai"
  worldid: "125"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12125"
}
rows {
  id: 293
  servername: "letsgo-coc-test3"
  owner: "coc稳定服3"
  category: "COC"
  namespace: "letsgo-coc-test3"
  worldid: "199"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12199"
}
rows {
  id: 294
  servername: "ltmao"
  owner: "ltmao"
  category: "Personal"
  namespace: "root_9_134_242_199"
  worldid: "504"
  url_LetsGo: "tcp://9.134.242.199:8201"
}
rows {
  id: 297
  servername: "letsgo-custom-dev12"
  owner: "custom-dev12"
  category: "CustomDev"
  namespace: "letsgo-custom-dev12"
  worldid: "313"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12313"
}
rows {
  id: 298
  servername: "letsgo-starp-merge"
  owner: "starP-Merge"
  category: "Feature"
  namespace: "letsgo-starp-merge"
  worldid: "191"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12191"
}
rows {
  id: 300
  servername: "willslin"
  owner: "willslin"
  category: "Personal"
  namespace: "user00_9_134_32_36"
  worldid: "246"
  url_LetsGo: "tcp://9.134.32.36:8201"
}
rows {
  id: 301
  servername: "mozizhai"
  owner: "mozizhai"
  category: "Personal"
  namespace: "user00_9_134_79_195"
  worldid: "235"
  url_LetsGo: "tcp://9.134.79.195:8201"
}
rows {
  id: 302
  servername: "mathewliu"
  owner: "mathewliu"
  category: "Personal"
  namespace: "mathewliu_9_134_68_76"
  worldid: "207"
  url_LetsGo: "tcp://9.134.68.76:8201"
}
rows {
  id: 303
  servername: "dstest_121_209"
  owner: "dstest_121_209"
  category: "DSTest"
  namespace: "dstest_9_134_121_209"
  worldid: "204"
  url_LetsGo: "tcp://9.134.121.209:8201"
}
rows {
  id: 304
  servername: "xiaomingwu"
  owner: "xiaomingwu"
  category: "Personal"
  namespace: "xiaomingwu_9_134_132_70"
  worldid: "247"
  url_LetsGo: "tcp://9.134.132.70:8201"
}
rows {
  id: 305
  servername: "cybroxtu"
  owner: "cybroxtu"
  category: "Personal"
  namespace: "cybroxtu_9_134_130_171"
  worldid: "249"
  url_LetsGo: "tcp://9.134.130.171:8201"
}
rows {
  id: 306
  servername: "emilytwang"
  owner: "emilytwang"
  category: "Personal"
  namespace: "user00_9_135_154_175"
  worldid: "227"
  url_LetsGo: "tcp://9.135.154.175:8201"
}
rows {
  id: 307
  servername: "jonesqiu"
  owner: "jonesqiu"
  category: "Personal"
  namespace: "jonesqiu_9_134_75_206"
  worldid: "223"
  url_LetsGo: "tcp://9.134.75.206:8201"
}
rows {
  id: 308
  servername: "flamehong"
  owner: "flamehong"
  category: "Personal"
  namespace: "user00_9_134_38_252"
  worldid: "226"
  url_LetsGo: "tcp://9.134.38.252:8201"
}
rows {
  id: 309
  servername: "jettchen"
  owner: "jettchen"
  category: "Personal"
  namespace: "jettchen_21_6_136_108"
  worldid: "252"
  url_LetsGo: "tcp://21.6.136.108:8201"
}
rows {
  id: 310
  servername: "letsgo-starp-test"
  owner: "starP-Test"
  category: "Feature"
  namespace: "letsgo-starp-test"
  worldid: "190"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12190"
}
rows {
  id: 311
  servername: "skylerzhang"
  owner: "skylerzhang"
  category: "Personal"
  namespace: "skylerzhang_9_134_128_25"
  worldid: "233"
  url_LetsGo: "tcp://9.134.128.25:8201"
}
rows {
  id: 312
  servername: "runliangxia"
  owner: "runliangxia"
  category: "Personal"
  namespace: "runliangxia_9_134_131_78"
  worldid: "117"
  url_LetsGo: "tcp://9.134.131.78:8201"
}
rows {
  id: 313
  servername: "letsgo-starp-daily"
  owner: "starP-Daily"
  category: "Feature"
  namespace: "letsgo-starp-daily"
  worldid: "218"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12218"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16218"
}
rows {
  id: 314
  servername: "letsgo-starp-ow-test"
  owner: "starP-大世界测试服"
  category: "Feature"
  namespace: "letsgo-starp-ow-test"
  worldid: "219"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12219"
}
rows {
  id: 315
  servername: "letsgo-starp-3c-test"
  owner: "starP-战斗3C武器测试服"
  category: "Feature"
  namespace: "letsgo-starp-3c-test"
  worldid: "220"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12220"
}
rows {
  id: 316
  servername: "letsgo-starp-protoss-test"
  owner: "starP-星灵测试服"
  category: "Feature"
  namespace: "letsgo-starp-protoss-test"
  worldid: "221"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12221"
}
rows {
  id: 317
  servername: "letsgo-starp-soc-test"
  owner: "starP-SOC测试服"
  category: "Feature"
  namespace: "letsgo-starp-soc-test"
  worldid: "222"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12222"
}
rows {
  id: 318
  servername: "letsgo-starp-system-test"
  owner: "starP-系统测试服"
  category: "Feature"
  namespace: "letsgo-starp-system-test"
  worldid: "223"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12223"
}
rows {
  id: 319
  servername: "qinglongge"
  owner: "qinglongge"
  category: "Personal"
  namespace: "qinglongge_9_135_63_45"
  worldid: "251"
  url_LetsGo: "tcp://9.135.63.45:8201"
}
rows {
  id: 320
  servername: "knutxiao"
  owner: "knutxiao"
  category: "Personal"
  namespace: "user03_9_134_71_115"
  worldid: "245"
  url_LetsGo: "tcp://9.134.71.115:8201"
}
rows {
  id: 321
  servername: "letsgo-starp-week"
  owner: "starP-Week"
  category: "Feature"
  namespace: "letsgo-starp-week"
  worldid: "229"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12229"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16229"
}
rows {
  id: 322
  servername: "letsgo-starp-ds-prof"
  owner: "starP-DS-Prof"
  category: "Feature"
  namespace: "letsgo-starp-ds-prof"
  worldid: "225"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12225"
}
rows {
  id: 323
  servername: "letsgo-starp-stress"
  owner: "starP-压测服"
  category: "Feature"
  namespace: "letsgo-starp-stress"
  worldid: "228"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12228"
}
rows {
  id: 324
  servername: "zyqqwang"
  owner: "zyqqwang"
  category: "Personal"
  namespace: "zyqqwang_9_134_146_37"
  worldid: "253"
  url_LetsGo: "tcp://9.134.146.37:8201"
}
rows {
  id: 325
  servername: "jerricdeng"
  owner: "jerricdeng"
  category: "Personal"
  namespace: "jerricdeng_9_134_132_168"
  worldid: "254"
  url_LetsGo: "tcp://9.134.132.168:8201"
}
rows {
  id: 326
  servername: "letsgo-starp-planner-battle"
  owner: "starP-战斗策划"
  category: "Feature"
  namespace: "letsgo-starp-planner-battle"
  worldid: "249"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12249"
}
rows {
  id: 327
  servername: "letsgo-starp-planner-soc"
  owner: "starP-SOC策划"
  category: "Feature"
  namespace: "letsgo-starp-planner-soc"
  worldid: "236"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12236"
}
rows {
  id: 328
  servername: "letsgo-starp-week-stable"
  owner: "starP-Week-stable"
  category: "Feature"
  namespace: "letsgo-starp-week-stable"
  worldid: "238"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12238"
}
rows {
  id: 329
  servername: "letsgo-starp-daily-stable"
  owner: "starP-Daily-stable"
  category: "Feature"
  namespace: "letsgo-starp-daily-stable"
  worldid: "237"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12237"
}
rows {
  id: 330
  servername: "letsgo-starp-ce"
  owner: "starP-CE"
  category: "Feature"
  namespace: "letsgo-starp-ce"
  worldid: "239"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12239"
}
rows {
  id: 331
  servername: "letsgo-starp-dev"
  owner: "starP-Dev"
  category: "Feature"
  namespace: "letsgo-starp-dev"
  worldid: "250"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12250"
}
rows {
  id: 332
  servername: "letsgo-starp-planner"
  owner: "starP-系统数值策划"
  category: "Feature"
  namespace: "letsgo-starp-planner"
  worldid: "240"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12240"
}
rows {
  id: 333
  servername: "letsgo-starp-temp-test"
  owner: "starP-临时测试"
  category: "Feature"
  namespace: "letsgo-starp-temp-test"
  worldid: "251"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12251"
}
rows {
  id: 334
  servername: "letsgo-starp-nadoo"
  owner: "letsgo-starp-nadoo"
  category: "Personal"
  namespace: "nadoo_21_6_136_43"
  worldid: "211"
  url_LetsGo: "tcp://21.6.136.43:8201"
}
rows {
  id: 335
  servername: "aibowang"
  owner: "aibowang"
  category: "Personal"
  namespace: "aibowang_21_6_136_118"
  worldid: "212"
  url_LetsGo: "tcp://21.6.136.118:8201"
}
rows {
  id: 336
  servername: "carlcheng"
  owner: "carlcheng"
  category: "Personal"
  namespace: "carlcheng_9_134_2_77"
  worldid: "244"
  url_LetsGo: "tcp://9.134.2.77:8201"
}
rows {
  id: 337
  servername: "awakezhao"
  owner: "awakezhao"
  category: "Personal"
  namespace: "awakezhao_21_6_136_55"
  worldid: "114"
  url_LetsGo: "tcp://21.6.136.55:8201"
}
rows {
  id: 338
  servername: "letsgo-starp-daily-evendate"
  owner: "starP-Daily-Evendate"
  category: "Feature"
  namespace: "letsgo-starp-daily-evendate"
  worldid: "264"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12264"
}
rows {
  id: 339
  servername: "letsgo-starp-release"
  owner: "starP-Release"
  category: "Feature"
  namespace: "letsgo-starp-release"
  worldid: "263"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12263"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16263"
}
rows {
  id: 340
  servername: "letsgo-starp-dstest"
  owner: "starP-Dstest"
  category: "DSTest"
  namespace: "letsgo-starp-dstest"
  worldid: "265"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12265"
}
rows {
  id: 341
  servername: "zijuezhang"
  owner: "zijuezhang"
  category: "Personal"
  namespace: "user00_9_134_126_128"
  worldid: "206"
  url_LetsGo: "tcp://9.134.126.128:8201"
}
rows {
  id: 342
  servername: "letsgo-starp-gameplay-test"
  owner: "starP-玩法测试服"
  category: "Feature"
  namespace: "letsgo-starp-gameplay-test"
  worldid: "267"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12267"
}
rows {
  id: 343
  servername: "letsgo-starp-research"
  owner: "starP-Research"
  category: "Feature"
  namespace: "letsgo-starp-research"
  worldid: "268"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12268"
}
rows {
  id: 344
  servername: "letsgo-starp-patch-test"
  owner: "Java热更新测试服"
  category: "Feature"
  namespace: "letsgo-starp-patch-test"
  worldid: "269"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12269"
}
rows {
  id: 345
  servername: "virtuos_xu"
  owner: "virtuos_xu"
  category: "virtuos"
  namespace: "xuhai_30_173_255_76"
  worldid: "173"
  url_LetsGo: "tcp://30.173.255.76:8201"
}
rows {
  id: 346
  servername: "virtuos_han"
  owner: "virtuos_稳定服"
  category: "virtuos"
  namespace: "hanxiaoyu_30_173_255_83"
  worldid: "175"
  url_LetsGo: "tcp://30.173.255.83:8201"
}
rows {
  id: 347
  servername: "virtuos_zhang"
  owner: "virtuos_zhang"
  category: "virtuos"
  namespace: "zhanglin_30_173_255_77"
  worldid: "177"
  url_LetsGo: "tcp://30.173.255.77:8201"
}
rows {
  id: 348
  servername: "virtuos_zhao"
  owner: "virtuos_zhao"
  category: "virtuos"
  namespace: "zhaowei_30_173_255_78"
  worldid: "174"
  url_LetsGo: "tcp://30.173.255.78:8201"
}
rows {
  id: 349
  servername: "letsgo-starp-winpack"
  owner: "starP-WinPack"
  category: "Feature"
  namespace: "letsgo-starp-winpack"
  worldid: "211"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12211"
}
rows {
  id: 350
  servername: "letsgo-starp-programmer-test"
  owner: "starP-Programmer-Test"
  category: "Feature"
  namespace: "letsgo-starp-programmer-test"
  worldid: "212"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12212"
}
rows {
  id: 351
  servername: "letsgo-starp-programmer-test2"
  owner: "starP-程序测试2服"
  category: "Feature"
  namespace: "letsgo-starp-programmer-test2"
  worldid: "213"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12213"
}
rows {
  id: 352
  servername: "wiiling"
  owner: "wiiling"
  category: "Personal"
  namespace: "wiiling_21_6_162_6"
  worldid: "230"
  url_LetsGo: "tcp://21.6.162.6:8201"
}
rows {
  id: 353
  servername: "virtuos_chen"
  owner: "virtuos_测试服"
  category: "virtuos"
  namespace: "chenyu_30_173_255_75"
  worldid: "170"
  url_LetsGo: "tcp://30.173.255.75:8201"
}
rows {
  id: 354
  servername: "letsgo-starp-longlife-ds-test"
  owner: "starP-长生命周期DS测试"
  category: "Feature"
  namespace: "letsgo-starp-longlife-ds-test"
  worldid: "214"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12214"
}
rows {
  id: 355
  servername: "pioneerzhou"
  owner: "pioneerzhou"
  category: "Personal"
  namespace: "pioneerzhou_9_134_126_227"
  worldid: "205"
  url_LetsGo: "tcp://9.134.126.227:8201"
}
rows {
  id: 356
  servername: "letsgo-starp-dungeonmode"
  owner: "starP-Dungeon"
  category: "Feature"
  namespace: "letsgo-starp-dungeonmode"
  worldid: "233"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12233"
}
rows {
  id: 357
  servername: "pluso"
  owner: "pluso"
  category: "Personal"
  namespace: "pluso_9_134_185_53"
  worldid: "212"
  url_LetsGo: "tcp://9.134.185.53:8201"
}
rows {
  id: 358
  servername: "evanxli"
  owner: "evanxli"
  category: "Personal"
  namespace: "evanxli_9_134_56_254"
  worldid: "236"
  url_LetsGo: "tcp://9.134.56.254:8201"
}
rows {
  id: 359
  servername: "starp-time-test1"
  owner: "starP-Time-Test1"
  category: "Feature"
  namespace: "starp-time-test1"
  worldid: "234"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12234"
}
rows {
  id: 360
  servername: "starp-time-test2"
  owner: "starP-Time-Test2"
  category: "Feature"
  namespace: "starp-time-test2"
  worldid: "235"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12235"
}
rows {
  id: 361
  servername: "starp-adaptation-test"
  owner: "SP适配测试服"
  category: "Feature"
  namespace: "starp-adaptation-test"
  worldid: "253"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12253"
}
rows {
  id: 362
  servername: "starp-update-test"
  owner: "starP-Update-Test"
  category: "Feature"
  namespace: "starp-update-test"
  worldid: "254"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12254"
}
rows {
  id: 363
  servername: "letsgo-starp-cdn-test"
  owner: "starP-CDN测试服"
  category: "Feature"
  namespace: "letsgo-starp-cdn-test"
  worldid: "255"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12255"
}
rows {
  id: 364
  servername: "tsukiren-test"
  owner: "tsukiren-test"
  category: "Personal"
  namespace: "tsukiren_9_134_60_246"
  worldid: "110"
  url_LetsGo: "tcp://9.134.60.246:8201"
}
rows {
  id: 365
  servername: "letsgo-starp-release-package"
  owner: "starP-小包分支"
  category: "Feature"
  namespace: "letsgo-starp-release-package"
  worldid: "256"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12256"
}
rows {
  id: 366
  servername: "haohaoliang"
  owner: "haohaoliang"
  category: "Personal"
  namespace: "haohaoliang_21_6_144_194"
  worldid: "231"
  url_LetsGo: "tcp://21.6.144.194:8201"
}
rows {
  id: 367
  servername: "letsgo-starp-release-dstest"
  owner: "starP-Release-DSTest"
  category: "DSTest"
  namespace: "letsgo-starp-release-dstest"
  worldid: "257"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12257"
}
rows {
  id: 368
  servername: "letsgo-starp-guild-merge"
  owner: "starP-工会合线服"
  category: "Feature"
  namespace: "letsgo-starp-guild-merge"
  worldid: "258"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12258"
}
rows {
  id: 369
  servername: "starp-feature-agritainment"
  owner: "StarP-Agritainment"
  category: "Feature"
  namespace: "starp-feature-agritainment"
  worldid: "280"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12280"
}
rows {
  id: 370
  servername: "starp-f-agritainment-dstest"
  owner: "StarP-Agritainment-DStest"
  category: "DSTest"
  namespace: "starp-f-agritainment-dstest"
  worldid: "281"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12281"
}
rows {
  id: 371
  servername: "starp-feature-room"
  owner: "StarP-Room"
  category: "Feature"
  namespace: "starp-feature-room"
  worldid: "282"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12282"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16282"
}
rows {
  id: 372
  servername: "starp-feature-room-dstest"
  owner: "StarP-Room-DStest"
  category: "DSTest"
  namespace: "starp-feature-room-dstest"
  worldid: "283"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12283"
}
rows {
  id: 373
  servername: "starp-feature-guild"
  owner: "StarP-Guild"
  category: "Feature"
  namespace: "starp-feature-guild"
  worldid: "284"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12284"
}
rows {
  id: 374
  servername: "starp-feature-guild-dstest"
  owner: "StarP-Guild-DStest"
  category: "DSTest"
  namespace: "starp-feature-guild-dstest"
  worldid: "285"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12285"
}
rows {
  id: 375
  servername: "starp-feature-combat"
  owner: "StarP-Combat"
  category: "Feature"
  namespace: "starp-feature-combat"
  worldid: "286"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12286"
}
rows {
  id: 376
  servername: "starp-feature-combat-dstest"
  owner: "StarP-Combat-DStest"
  category: "DSTest"
  namespace: "starp-feature-combat-dstest"
  worldid: "287"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12287"
}
rows {
  id: 377
  servername: "starp-feature-mpve"
  owner: "StarP-MPVE"
  category: "Feature"
  namespace: "starp-feature-mpve"
  worldid: "288"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12288"
}
rows {
  id: 378
  servername: "starp-feature-mpve-dstest"
  owner: "StarP-MPVE-DStest"
  category: "DSTest"
  namespace: "starp-feature-mpve-dstest"
  worldid: "289"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12289"
}
rows {
  id: 379
  servername: "starp-f-profession"
  owner: "StarP-Profession"
  category: "Feature"
  namespace: "starp-f-profession"
  worldid: "290"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12290"
}
rows {
  id: 380
  servername: "starp-f-profession-dstest"
  owner: "StarP-Profession-DStest"
  category: "DSTest"
  namespace: "starp-f-profession-dstest"
  worldid: "291"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12291"
}
rows {
  id: 381
  servername: "starp-f-pvp"
  owner: "StarP-PVP"
  category: "Feature"
  namespace: "starp-f-pvp"
  worldid: "292"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12292"
}
rows {
  id: 382
  servername: "starp-f-pvp-dstest"
  owner: "StarP-PVP-DStest"
  category: "DSTest"
  namespace: "starp-f-pvp-dstest"
  worldid: "293"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12293"
}
rows {
  id: 383
  servername: "vulpeswu"
  owner: "vulpeswu"
  category: "Personal"
  namespace: "vulpeswu_9_134_235_219"
  worldid: "232"
  url_LetsGo: "tcp://9.134.235.219:8201"
}
rows {
  id: 384
  servername: "letsgo-pioneer-pre3"
  owner: "先遣预发布-啾灵专用1"
  category: "LetsGo"
  namespace: "letsgo-pioneer-pre3"
  worldid: "279"
  url_LetsGo: "tcp://pretest.ymzx.qq.com:12279"
}
rows {
  id: 385
  servername: "letsgo-pioneer2"
  owner: "先遣2服-啾灵专用"
  category: "LetsGo"
  namespace: "letsgo-pioneer2"
  worldid: "193"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12193"
  servername_ios_review: "starp-ios-shenhe"
  config_url_LetsGo: "tcp://beta.ymzx.qq.com:16193"
}
rows {
  id: 386
  servername: "starp-ios-shenhe"
  owner: "SP-IOS审核服"
  category: "LetsGo"
  namespace: "starp-ios-shenhe"
  worldid: "294"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12294"
}
rows {
  id: 387
  servername: "starp-zhengshen"
  owner: "SP-政审服"
  category: "LetsGo"
  namespace: "starp-zhengshen"
  worldid: "295"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12295"
  config_url_LetsGo: "tcp://test4config2.ymzx.qq.com:16295"
}
rows {
  id: 388
  servername: "starp-feature1"
  owner: "StarP-V5-loadingtest"
  category: "Feature"
  namespace: "starp-feature1"
  worldid: "305"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12305"
}
rows {
  id: 389
  servername: "starp-feature1-dstest"
  owner: "StarP-V4Dev-DStest"
  category: "DSTest"
  namespace: "starp-feature1-dstest"
  worldid: "306"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12306"
}
rows {
  id: 390
  servername: "westjiang"
  owner: "westjiang"
  category: "Personal"
  namespace: "westjiang_21_6_194_158"
  worldid: "4"
  url_LetsGo: "tcp://21.6.194.158:8201"
}
rows {
  id: 391
  servername: "philoddeng"
  owner: "philoddeng"
  category: "Personal"
  namespace: "philoddeng_21_6_195_31"
  worldid: "118"
  url_LetsGo: "tcp://21.6.195.31:8201"
}
rows {
  id: 392
  servername: "yiqunxiao"
  owner: "yiqunxiao"
  category: "Personal"
  namespace: "yiqunxiao_21_6_195_83"
  worldid: "56"
  url_LetsGo: "tcp://21.6.195.83:8201"
}
rows {
  id: 393
  servername: "wavehong"
  owner: "wavehong"
  category: "Personal"
  namespace: "wavehong_21_6_195_57"
  worldid: "113"
  url_LetsGo: "tcp://21.6.195.57:8201"
}
rows {
  id: 394
  servername: "letsgo-pioneer-pre5"
  owner: "先遣预发布-鸿蒙专用"
  category: "LetsGo"
  namespace: "letsgo-pioneer-pre5"
  worldid: "312"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12312"
}
rows {
  id: 395
  servername: "letsgo-pioneer4"
  owner: "先遣4服-鸿蒙专用"
  category: "LetsGo"
  namespace: "letsgo-pioneer4"
  worldid: "197"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12197"
}
rows {
  id: 396
  servername: "jinghanhou"
  owner: "jinghanhou"
  category: "Personal"
  namespace: "jinghanhou_21_6_221_216"
  worldid: "254"
  url_LetsGo: "tcp://21.6.221.216:8201"
}
rows {
  id: 397
  servername: "haokaiwu"
  owner: "你喜爱的小蜜蜂"
  category: "Personal"
  namespace: "haokaiwu_9_134_238_179"
  worldid: "300"
  url_LetsGo: "tcp://9.134.238.179:8201"
}
rows {
  id: 398
  servername: "starp-feature2"
  owner: "StarP-Feature2"
  category: "Feature"
  namespace: "starp-feature2"
  worldid: "310"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12310"
}
rows {
  id: 399
  servername: "starp-feature2-dstest"
  owner: "SP特性分支2服DStest"
  category: "DSTest"
  namespace: "starp-feature2-dstest"
  worldid: "311"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12311"
}
rows {
  id: 400
  servername: "tanninzhu-dev"
  owner: "tanninzhu"
  category: "Personal"
  namespace: "tanninzhu_21_6_221_235"
  worldid: "228"
  url_LetsGo: "tcp://21.6.221.235:8201"
}
rows {
  id: 401
  servername: "letsgo-presswx"
  owner: "压测服2服"
  category: "Press"
  namespace: "letsgo-presswx"
  worldid: "74"
  url_LetsGo: "tcp://press.ymzx.qq.com:12074"
}
rows {
  id: 402
  servername: "letsgo-arena-qa1"
  owner: "arena-qa1"
  category: "Arena"
  namespace: "letsgo-arena-qa1"
  worldid: "410"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12410"
}
rows {
  id: 403
  servername: "arena_Local_DS_先遣_21_6_221_246"
  owner: "arena_Local_DS_先遣_21_6_221_246"
  category: "Arena"
  namespace: "user00_21_6_221_246"
  worldid: "242"
  url_LetsGo: "tcp://21.6.221.246:8201"
}
rows {
  id: 404
  servername: "letsgo-arena-qa2"
  owner: "arena-qa2"
  category: "Arena"
  namespace: "letsgo-arena-qa2"
  worldid: "411"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12411"
}
rows {
  id: 405
  servername: "letsgo-arena-qa3"
  owner: "arena-qa3"
  category: "Arena"
  namespace: "letsgo-arena-qa3"
  worldid: "412"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12412"
}
rows {
  id: 406
  servername: "guanhualiu-dev"
  owner: "guanhualiu"
  category: "Personal"
  namespace: "guanhualiu_9_134_186_186"
  worldid: "601"
  url_LetsGo: "tcp://9.134.186.186:8201"
}
rows {
  id: 407
  servername: "arena_Local_DS_s12_dev_21_6_195_36"
  owner: "arena_Local_DS_s12_dev_21_6_195_36"
  category: "Arena"
  namespace: "user00_21_6_195_36"
  worldid: "241"
  url_LetsGo: "tcp://21.6.195.36:8201"
}
rows {
  id: 408
  servername: "letsgo-acddb"
  owner: "躲避球测试服"
  category: "Arena"
  namespace: "letsgo-acddb"
  worldid: "178"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12178"
}
rows {
  id: 409
  servername: "letsgo-acddb-stable"
  owner: "躲避球测试服-开发分支"
  category: "Arena"
  namespace: "letsgo-acddb-stable"
  worldid: "226"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12226"
}
rows {
  id: 410
  servername: "letsgo-acddb-pretest"
  owner: "躲避球测试2服"
  category: "Arena"
  namespace: "letsgo-acddb-pretest"
  worldid: "227"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12227"
}
rows {
  id: 411
  servername: "letsgo-brawlstar"
  owner: "躲避球测试服-s12dev"
  category: "Arena"
  namespace: "letsgo-brawlstar"
  worldid: "252"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12252"
}
rows {
  id: 412
  servername: "letsgo-perf-acddb"
  owner: "躲避球测试服-性能专项"
  category: "Arena"
  namespace: "letsgo-perf-acddb"
  worldid: "309"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12309"
}
rows {
  id: 413
  servername: "letsgo-brawlstar-dstest"
  owner: "荒野乱斗-DS测试_DodgeBall_9_135_0_11"
  category: "Arena"
  namespace: "user00_9_135_0_11"
  worldid: "233"
  url_LetsGo: "tcp://9.135.0.11:8201"
}
rows {
  id: 414
  servername: "letsgo-pioneer-review"
  owner: "先遣审核服"
  category: "LetsGo"
  namespace: "letsgo-pioneer-review"
  worldid: "314"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12314"
}
rows {
  id: 415
  servername: "letsgo-mw-metro"
  owner: "mw-metro服"
  category: "LetsGo"
  namespace: "letsgo-mw-metro"
  worldid: "520"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12520"
}
rows {
  id: 416
  servername: "zicaihuang"
  owner: "zicaihuang"
  category: "Personal"
  namespace: "user00_9_135_93_226"
  worldid: "600"
  url_LetsGo: "tcp://9.135.93.226:8201"
}
rows {
  id: 417
  servername: "dickyjyang"
  owner: "dickyjyang"
  category: "Personal"
  namespace: "dickyjyang_9_134_114_45"
  worldid: "601"
  url_LetsGo: "tcp://9.134.114.45:8201"
}
rows {
  id: 418
  servername: "lycheezhli"
  owner: "lycheezhli"
  category: "Personal"
  namespace: "lycheezhli_9_134_55_41"
  worldid: "602"
  url_LetsGo: "tcp://9.134.55.41:8201"
}
rows {
  id: 419
  servername: "andrewli"
  owner: "andrewli"
  category: "Personal"
  namespace: "andrewli_9_134_233_131"
  worldid: "506"
  url_LetsGo: "tcp://9.134.233.131:8201"
}
rows {
  id: 420
  servername: "callanxu"
  owner: "callanxu"
  category: "Personal"
  namespace: "user00_9_134_233_173"
  worldid: "603"
  url_LetsGo: "tcp://9.134.233.173:8201"
}
rows {
  id: 421
  servername: "mileszhao"
  owner: "mileszhao"
  category: "Personal"
  namespace: "user00_9_135_153_27"
  worldid: "607"
  url_LetsGo: "tcp://9.135.153.27:8201"
}
rows {
  id: 422
  servername: "jinliangli"
  owner: "jinliangli"
  category: "Personal"
  namespace: "jinliangli_21_6_194_150"
  worldid: "605"
  url_LetsGo: "tcp://21.6.194.150:8201"
}
rows {
  id: 423
  servername: "dcrschen"
  owner: "dcrschen"
  category: "Personal"
  namespace: "dcrschen_9_134_45_92"
  worldid: "606"
  url_LetsGo: "tcp://9.134.45.92:8201"
}
rows {
  id: 424
  servername: "totoroli"
  owner: "totoroli"
  category: "Personal"
  namespace: "totoroli_9_135_56_55"
  worldid: "900"
  url_LetsGo: "tcp://9.135.56.55:8201"
}
rows {
  id: 425
  servername: "loveeyang"
  owner: "loveeyang"
  category: "Personal"
  namespace: "loveeyang_9_135_57_206"
  worldid: "520"
  url_LetsGo: "tcp://9.135.57.206:8201"
}
rows {
  id: 426
  servername: "letsgo-metro-qa"
  owner: "metro-qa服"
  category: "LetsGo"
  namespace: "letsgo-metro-qa"
  worldid: "521"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12521"
}
rows {
  id: 427
  servername: "letsgo-pixui"
  owner: "PixUI"
  category: "LetsGo"
  namespace: "letsgo-pixui"
  worldid: "316"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12316"
}
rows {
  id: 428
  servername: "totoroli"
  owner: "totoroli901"
  category: "Personal"
  namespace: "totoroli_9_135_56_55"
  worldid: "901"
  url_LetsGo: "tcp://9.135.56.55:8201"
}
rows {
  id: 429
  servername: "starp-feature3"
  owner: "StarP-Feature3"
  category: "Feature"
  namespace: "starp-feature3"
  worldid: "317"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12317"
}
rows {
  id: 430
  servername: "binnywang"
  owner: "binnywang"
  category: "Personal"
  namespace: "user00_9_135_73_150"
  worldid: "213"
  url_LetsGo: "tcp://9.135.73.150:8201"
}
rows {
  id: 431
  servername: "yuchenjia"
  owner: "yuchenjia"
  category: "Personal"
  namespace: "yuchenjia_21_6_240_22"
  worldid: "133"
  url_LetsGo: "tcp://21.6.240.22:8201"
}
rows {
  id: 432
  servername: "welkinwang"
  owner: "welkinwang"
  category: "Personal"
  namespace: "welkinwang_9_134_55_219"
  worldid: "243"
  url_LetsGo: "tcp://9.134.55.219:8201"
}
rows {
  id: 433
  servername: "asdwang"
  owner: "asdwang"
  category: "Personal"
  namespace: "asdwang_9_135_62_159"
  worldid: "137"
  url_LetsGo: "9.135.62.159:8201"
}
rows {
  id: 434
  servername: "inkzhuang"
  owner: "inkzhuang"
  category: "Personal"
  namespace: "inkzhuang_21_6_224_132"
  worldid: "166"
  url_LetsGo: "tcp://21.6.224.132:8201"
}
rows {
  id: 435
  servername: "tobytian"
  owner: "tobytian"
  category: "Personal"
  namespace: "tobytian_9_134_238_195"
  worldid: "250"
  url_LetsGo: "9.134.238.195:8201"
}
rows {
  id: 436
  servername: "letsgo-starp-ds-prof2"
  owner: "starP-DS-Prof2"
  category: "Feature"
  namespace: "letsgo-starp-ds-prof2"
  worldid: "318"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12318"
}
rows {
  id: 437
  servername: "chongrliu"
  owner: "chongrliu"
  category: "Personal"
  namespace: "chongrliu_21_6_224_61"
  worldid: "158"
  url_LetsGo: "tcp://21.6.224.61:8201"
}
rows {
  id: 438
  servername: "letsgo-metro-release"
  owner: "metro-release"
  category: "LetsGo"
  namespace: "letsgo-metro-release"
  worldid: "522"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12522"
}
rows {
  id: 439
  servername: "monayxxie"
  owner: "monayxxie"
  category: "Personal"
  namespace: "monayxxie_21_6_224_168"
  worldid: "248"
  url_LetsGo: "tcp://21.6.224.168:8201"
}
rows {
  id: 440
  servername: "letsgo-perf-dev-cloud"
  owner: "性能专项-云游性能测试服"
  category: "性能专项"
  namespace: "letsgo-perf-dev-cloud"
  worldid: "321"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12321"
}
rows {
  id: 441
  servername: "yifannluo"
  owner: "yifannluo"
  category: "Personal"
  namespace: "v_yifannluo_9_135_88_207"
  worldid: "593"
  url_LetsGo: "tcp://9.135.88.207:8201"
}
rows {
  id: 442
  servername: "letsgo-pioneer5"
  owner: "先遣5服-啾灵专用"
  category: "LetsGo"
  namespace: "letsgo-pioneer5"
  worldid: "198"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12198"
  servername_ios_review: "letsgo-pioneer-review"
}
rows {
  id: 443
  servername: "letsgo-pioneer-pre6"
  owner: "先遣预发布-啾灵专用2"
  category: "LetsGo"
  namespace: "letsgo-pioneer-pre6"
  worldid: "322"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12322"
}
rows {
  id: 444
  servername: "letsgo-coc-merge"
  owner: "letsgo-coc-merge"
  category: "COC"
  namespace: "letsgo-coc-merge"
  worldid: "320"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12320"
}
rows {
  id: 445
  servername: "letsgo-alphadev"
  owner: "alphadev"
  category: "Feature"
  namespace: "letsgo-alphadev"
  worldid: "319"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12319"
}
rows {
  id: 446
  servername: "limingnie"
  owner: "limingnie"
  category: "Personal"
  namespace: "limingnie_9_134_14_230"
  worldid: "235"
  url_LetsGo: "tcp://9.134.14.230:8201"
}
rows {
  id: 447
  servername: "letsgo-metro-presstest"
  owner: "metro-presstest"
  category: "Metro"
  namespace: "letsgo-metro-presstest"
  worldid: "523"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12523"
}
rows {
  id: 448
  servername: "letsgo-metro-qadev"
  owner: "metro-qadev"
  category: "Metro"
  namespace: "letsgo-metro-qadev"
  worldid: "524"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12524"
}
rows {
  id: 449
  servername: "kewan"
  owner: "kewan"
  category: "Personal"
  namespace: "kewan_9_135_63_182"
  worldid: "135"
  url_LetsGo: "tcp://9.135.63.182:8201"
}
rows {
  id: 450
  servername: "letsgo-urgentpre3-time"
  owner: "应急预发布3-时间"
  category: "LetsGo"
  namespace: "letsgo-urgentpre3-time"
  worldid: "323"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12323"
  config_url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:16323"
}
rows {
  id: 451
  servername: "letsgo-urgentpre4-time"
  owner: "应急预发布4-时间"
  category: "LetsGo"
  namespace: "letsgo-urgentpre4-time"
  worldid: "324"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12324"
  config_url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:16324"
}
rows {
  id: 452
  servername: "hangzzhao"
  owner: "hangzzhao"
  category: "Personal"
  namespace: "hangzzhao_9_134_65_139"
  worldid: "521"
  url_LetsGo: "tcp://9.134.65.139:8201"
}
rows {
  id: 453
  servername: "letsgo-metro-pcstable"
  owner: "metro-pcstable"
  category: "Metro"
  namespace: "letsgo-metro-pcstable"
  worldid: "525"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12525"
}
rows {
  id: 454
  servername: "letsgo-pcreview2"
  owner: "PC审核服2"
  category: "LetsGo"
  namespace: "letsgo-pcreview2"
  worldid: "325"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12325"
}
rows {
  id: 455
  servername: "letsgo-coc-dev"
  owner: "coc-改时间测试服"
  category: "COC"
  namespace: "letsgo-coc-dev"
  worldid: "405"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12405"
}
rows {
  id: 456
  servername: "s10-release-localDS"
  owner: "s10-release-localDS"
  category: "Arena"
  namespace: "user00_9_134_132_58"
  worldid: "500"
  url_LetsGo: "tcp://9.134.132.58:8201"
}
rows {
  id: 457
  servername: "ackergao"
  owner: "ackergao"
  category: "Personal"
  namespace: "ackergao_9_135_12_48"
  worldid: "137"
  url_LetsGo: "tcp://9.135.12.48:8201"
}
rows {
  id: 458
  servername: "niyzhang"
  owner: "niyzhang"
  category: "Personal"
  namespace: "niyzhang_9_134_61_117"
  worldid: "136"
  url_LetsGo: "tcp://9.134.61.117:8201"
}
rows {
  id: 459
  servername: "shushengxie"
  owner: "shushengxie"
  category: "Personal"
  namespace: "shushengxie_9_135_144_172"
  worldid: "149"
  url_LetsGo: "tcp://9.135.144.172:8201"
}
rows {
  id: 460
  servername: "hugozeng"
  owner: "hugozeng私服"
  category: "Personal"
  namespace: "hugozeng_9_135_155_1"
  worldid: "156"
  url_LetsGo: "tcp://9.135.155.1:8201"
}
rows {
  id: 461
  servername: "evandiao"
  owner: "evandiao"
  category: "Personal"
  namespace: "evandiao_21_91_250_243"
  worldid: "210"
  url_LetsGo: "tcp://21.91.250.243:8201"
}
rows {
  id: 462
  servername: "coolinchen"
  owner: "coolin_dev"
  category: "Personal"
  namespace: "coolinchen_9_134_6_198"
  worldid: "150"
  url_LetsGo: "tcp://9.134.6.198:8201"
}
rows {
  id: 463
  servername: "feifeiswu"
  owner: "feifeiswu_吴学飞"
  category: "Personal"
  namespace: "root_9_134_147_55"
  worldid: "523"
  url_LetsGo: "tcp://9.134.147.55:8201"
}
rows {
  id: 464
  servername: "hongxujin"
  owner: "hongxujin"
  category: "Personal"
  namespace: "hongxujin_develop11"
  worldid: "218"
  url_LetsGo: "tcp://21.6.149.192:8201"
}
rows {
  id: 465
  servername: "tunandeng"
  owner: "tunandeng"
  category: "Personal"
  namespace: "tunandeng_21_91_250_162"
  worldid: "608"
  url_LetsGo: "tcp://21.91.250.162:8201"
}
rows {
  id: 466
  servername: "chenaichen"
  owner: "chenaichen"
  category: "Personal"
  namespace: "user00_9_134_239_252"
  worldid: "605"
  url_LetsGo: "tcp://9.134.239.252:8201"
}
rows {
  id: 467
  servername: "lichao_server2"
  owner: "lichao_server2"
  category: "Personal"
  worldid: "349"
  url_LetsGo: "tcp://21.91.235.165:8201"
}
rows {
  id: 468
  servername: "letsgo-metro-activity"
  owner: "metro-activity"
  category: "Metro"
  namespace: "letsgo-metro-activity"
  worldid: "526"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12526"
}
rows {
  id: 469
  servername: "letsgo-metro-qadev2"
  owner: "metro-qadev2"
  category: "Metro"
  namespace: "letsgo-metro-qadev2"
  worldid: "527"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12527"
}
rows {
  id: 470
  servername: "jocktang"
  owner: "jocktang"
  category: "Personal"
  namespace: "jocktang_21_91_249_138"
  worldid: "78"
  url_LetsGo: "tcp://21.91.249.138:8201"
}
rows {
  id: 471
  servername: "felixhe"
  owner: "felixhe"
  category: "Personal"
  namespace: "felixhe_9_134_237_92"
  worldid: "118"
  url_LetsGo: "tcp://************:8201"
}
rows {
  id: 472
  servername: "xuemingxli"
  owner: "xuemingxli"
  category: "Personal"
  namespace: "user00_9_134_121_119"
  worldid: "157"
  url_LetsGo: "tcp://*************:8201"
}
rows {
  id: 473
  servername: "jinkunluo"
  owner: "jinkunluo"
  category: "Personal"
  namespace: "jinkunluo_9_134_134_94"
  worldid: "355"
  url_LetsGo: "tcp://************:8201"
}
rows {
  id: 474
  servername: "weipengsu"
  owner: "weipengsu"
  category: "Personal"
  namespace: "weipengsu_21_91_217_146"
  worldid: "609"
  url_LetsGo: "tcp://*************:8201"
}
rows {
  id: 475
  servername: "v_zwhozhong"
  owner: "v_zwhozhong"
  category: "Personal"
  namespace: "v_zwhozhong_21_6_241_201"
  worldid: "159"
  url_LetsGo: "tcp://************:8201"
}
rows {
  id: 476
  servername: "letsgo-starp-press"
  owner: "StarP-Press"
  category: "Press"
  namespace: "letsgo-starp-press"
  worldid: "315"
  url_LetsGo: "tcp://press.ymzx.qq.com:12315"
  config_url_LetsGo: "tcp://test4config3.ymzx.qq.com:16315"
}
rows {
  id: 477
  servername: "letsgo-starp-press2"
  owner: "StarP-Press2"
  category: "Press"
  namespace: "letsgo-starp-press2"
  worldid: "326"
  url_LetsGo: "tcp://press.ymzx.qq.com:12326"
  config_url_LetsGo: "tcp://test4config3.ymzx.qq.com:16326"
}
rows {
  id: 478
  servername: "letsgo-metro-qa-activity"
  owner: "qa-activity服"
  category: "Metro"
  namespace: "letsgo-metro-qa-activity"
  worldid: "528"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12528"
}
rows {
  id: 479
  servername: "letsgo-dodgeball-localds"
  owner: "ddb开发localDS_9_143_102_183_206"
  category: "Arena"
  namespace: "user00_9_143_102_183"
  worldid: "197"
  url_LetsGo: "tcp://101.89.42.23:13004"
  config_url_LetsGo: "9.135.1.105:17777"
}
rows {
  id: 480
  servername: "eggschen"
  owner: "eggschen"
  category: "Personal"
  namespace: "eggschen_9_135_63_86"
  worldid: "225"
  url_LetsGo: "tcp://9.135.63.86:8201"
}
rows {
  id: 481
  servername: "geneshang"
  owner: "geneshang私服"
  category: "Personal"
  namespace: "geneshang_9_135_219_236"
  worldid: "124"
  url_LetsGo: "tcp://9.135.219.236:8201"
}
rows {
  id: 482
  servername: "letsgo-metro-autotest"
  owner: "metro-autotest"
  category: "Metro"
  namespace: "letsgo-metro-autotest"
  worldid: "529"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12529"
}
rows {
  id: 483
  servername: "letsgo-moba-overseas-dev"
  owner: "MOBA海外测试服"
  category: "Arena"
  namespace: "letsgo-moba-overseas-dev"
  worldid: "413"
  url_LetsGo: "tcp://119.45.72.226:12413"
}
rows {
  id: 484
  servername: "fsijiewang"
  owner: "fsijiewang"
  category: "Personal"
  namespace: "fsijiewang_21_91_179_81"
  worldid: "10"
  url_LetsGo: "tcp://21.91.179.81:8201"
  config_url_LetsGo: "tcp://21.91.179.81:8201"
}
rows {
  id: 485
  servername: "letsgo-pioneer6"
  owner: "先遣6服"
  category: "LetsGo"
  namespace: "letsgo-pioneer6"
  worldid: "327"
  url_LetsGo: "tcp://beta.ymzx.qq.com:12327"
  config_url_LetsGo: "tcp://beta.ymzx.qq.com:16327"
}
rows {
  id: 486
  servername: "letsgo-pioneer-pre7"
  owner: "先遣预发布7服-先遣6服"
  category: "LetsGo"
  namespace: "letsgo-pioneer-pre7"
  worldid: "328"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12328"
  config_url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:16328"
}
rows {
  id: 487
  servername: "letsgo-sx-xqtest-moba"
  owner: "Moba先遣dev"
  category: "Arena"
  namespace: "letsgo-sx-xqtest-moba"
  worldid: "409"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12409"
}
rows {
  id: 488
  servername: "v_wuzhenzhu"
  owner: "v_wuzhenzhu"
  category: "Personal"
  namespace: "v_wuzhenzhu_9_134_76_107"
  worldid: "161"
  url_LetsGo: "tcp://9.134.76.107:8201"
}
rows {
  id: 490
  servername: "letsgo-metro-press-svr"
  owner: "metro服务器压测"
  category: "Metro"
  namespace: "letsgo-metro-press-svr"
  worldid: "530"
  url_LetsGo: "tcp://press.ymzx.qq.com:12530"
}
rows {
  id: 491
  servername: "MOBA-Overseas-LocalDS_9_135_3_17"
  owner: "MOBA海外LocalDS_9_135_3_17"
  category: "Arena"
  namespace: "user00_9_135_3_17"
  worldid: "5"
  url_LetsGo: "tcp://9.135.3.17:8201"
}
rows {
  id: 492
  servername: "benjenwang"
  owner: "benjenwang"
  category: "Personal"
  namespace: "benjenwang_21_91_179_130"
  worldid: "140"
  url_LetsGo: "tcp://21.91.179.130:8201"
}
rows {
  id: 493
  servername: "staticwang"
  owner: "staticwang"
  category: "Personal"
  namespace: "staticwang_21_91_179_128"
  worldid: "610"
  url_LetsGo: "tcp://21.91.179.128:8201"
}
rows {
  id: 494
  servername: "letsgo-moba-overseas-stable"
  owner: "MOBA海外稳定服"
  category: "Arena"
  namespace: "letsgo-moba-overseas-stable"
  worldid: "415"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12415"
}
rows {
  id: 495
  servername: "letsgo-acddb-test"
  owner: "测试服测试3服"
  category: "Arena"
  namespace: "letsgo-acddb-test"
  worldid: "416"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12416"
}
rows {
  id: 496
  servername: "kinanlin"
  owner: "kinanlin"
  category: "Personal"
  namespace: "kinan_9_135_247_144"
  worldid: "611"
  url_LetsGo: "tcp://9.135.247.144:8201"
}
rows {
  id: 497
  servername: "HeroTest_LinuxDS_21_6_1_78"
  owner: "英雄平衡性测试服"
  category: "Arena"
  namespace: "user00_21_6_1_78"
  worldid: "8"
  url_LetsGo: "tcp://21.6.1.78:8201"
}
rows {
  id: 498
  servername: "hoonwang"
  owner: "hoonwang"
  category: "Personal"
  namespace: "user00_9_134_129_23"
  worldid: "10"
  url_LetsGo: "tcp://9.134.129.23:8201"
}
rows {
  id: 499
  servername: "leancjli"
  owner: "leancjli私服"
  category: "LetsGo"
  namespace: "dstest_9_134_118_179"
  worldid: "202"
  url_LetsGo: "tcp://9.134.118.179:8201"
  config_url_LetsGo: "tcp://9.134.118.179:8401"
}
rows {
  id: 500
  servername: "letsgo-devoversea-sg-7"
  owner: "moba海外测试服-海外环境"
  category: "Arena"
  namespace: "letsgo-devoversea-sg-7"
  worldid: "106"
  url_LetsGo: "tcp://lb-jfkd2eeg-b5gaikxnlu0hf7z9.clb.sg-tencentclb.net:12006"
}
rows {
  id: 501
  servername: "letsgo-metro-stable"
  owner: "stable服"
  category: "Metro"
  namespace: "letsgo-metro-stable"
  worldid: "531"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12531"
}
rows {
  id: 502
  servername: "letsgo-starp-pretest"
  owner: "啾灵预发布测试服"
  category: "LetsGo"
  namespace: "letsgo-starp-pretest"
  worldid: "329"
  url_LetsGo: "tcp://testnew.ymzx.qq.com:12329"
}
rows {
  id: 503
  servername: "letsgo-metro-qadev3"
  owner: "qadev3服"
  category: "Metro"
  namespace: "letsgo-metro-qadev3"
  worldid: "532"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12532"
}
rows {
  id: 504
  servername: "letsgo-metro-qachangetime"
  owner: "qachangetime服"
  category: "Metro"
  namespace: "letsgo-metro-qachangetime"
  worldid: "533"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12533"
}
rows {
  id: 505
  servername: "letsgo-starp-designer"
  owner: "starP策划专用服"
  category: "Feature"
  namespace: "letsgo-starp-designer"
  worldid: "277"
  url_LetsGo: "tcp://bigtestnew.ymzx.qq.com:12277"
}
rows {
  id: 506
  servername: "lucaxli"
  owner: "lucaxli"
  category: "Personal"
  namespace: "lucaxli_9_134_5_237"
  worldid: "156"
  url_LetsGo: "tcp://9.134.5.237:8201"
}
