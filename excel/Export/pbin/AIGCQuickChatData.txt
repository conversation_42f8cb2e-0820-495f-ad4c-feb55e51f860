com.tencent.wea.xlsRes.table_AIGCQuickChatData
excel/xls/A_AIGCNPC配置.xlsx sheet:快捷聊天
rows {
  id: 1
  text: "我要玩海龟汤"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 2
  text: "给我唱首歌吧！"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 3
  text: "现在就跳舞啦！"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 4
  text: "你最好的朋友是谁？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 5
  text: "你从哪里来？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 6
  text: "你最喜欢什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 7
  text: "你可以介绍一下你自己吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 8
  text: "你的梦想是什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 9
  text: "平时你都喜欢干点什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 10
  text: "你都有哪些好朋友？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 11
  text: "好郁闷啊"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 12
  text: "在干嘛？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 13
  text: "你真可爱"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 14
  text: "你喜欢什么呢？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 15
  text: "我拿了第一名！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 16
  text: "你是什么星座的？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 17
  text: "现在几点啦？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 18
  text: "你是男孩还是女孩？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 19
  text: "你最喜欢什么运动？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 20
  text: "你擅长什么呢？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 21
  text: "你能陪我聊聊天吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 22
  text: "你今天心情如何？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 23
  text: "你今天做了什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 24
  text: "我遇到了一点困扰，能帮我吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 25
  text: "你有梦想吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 26
  text: "你最喜欢吃什么呢？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 27
  text: "你周末一般怎么过？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 28
  text: "你有好吃的推荐吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 29
  text: "我今天抽到了新皮肤哈哈哈"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 30
  text: "我有点emo"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 31
  text: "我可以和你做朋友吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 32
  text: "你最讨厌什么呢？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 33
  text: "你喜欢听什么歌？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 34
  text: "可以讲个故事吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 35
  text: "你最好的朋友是谁？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 36
  text: "你笑起来很好看"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 37
  text: "你在干什么"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 38
  text: "你吃饭了吗"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 39
  text: "你住在哪里呢？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 40
  text: "你喜欢玩什么"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 41
  text: "你朋友多吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 42
  text: "你的梦想是什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 43
  text: "你可以介绍一下你自己吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 44
  text: "你印象最深的经历是什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 45
  text: "你有过不开心的经历吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 46
  text: "说一个尴尬的经历"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 47
  text: "你喜欢我吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 48
  text: "你平时几点起床"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 49
  text: "你平时几点睡觉"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 50
  text: "你喜欢吃什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 51
  text: "你会游泳吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 52
  text: "你遇到过难过的事情吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 53
  text: "你最喜欢和谁玩"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 54
  text: "我们可以做朋友吗"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 55
  text: "你有没有最喜欢的节日？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 56
  text: "你平时喜欢做什么运动？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 57
  text: "你最喜欢的季节是什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 58
  text: "你今天心情怎么样？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 59
  text: "给我唱【夜上海】！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 60
  text: "给我唱【两只老虎】！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 61
  text: "给我唱【烦恼都变成泡泡】！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 62
  text: "给我唱【小兔子乖乖】！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 63
  text: "给我唱【洋娃娃和小熊跳舞】！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 64
  text: "给我唱【恭喜恭喜】！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 65
  text: "给我唱【快乐星期五】！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 66
  text: "哇，我们真有默契"
  enable: true
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 67
  text: "不服来战！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 68
  text: "咱俩谁跟谁呀！"
  enable: true
  priority: 1
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 69
  text: "星宝这厢有礼了"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 70
  text: "来吧！舞王决战！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 71
  text: "能邀请你跳支舞吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 72
  text: "来一段热情洋溢的舞蹈吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 73
  text: "我来给你一个暖心安慰吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 74
  text: "来展示我们的招牌动作吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 75
  text: "给你醒醒脑"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 76
  text: "看，蛋糕！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 77
  text: "我来公主抱啦！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 78
  text: "饿了吧，给你！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 79
  text: "我们一起嗨翻天了！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 80
  text: "和我一起来一段二重奏吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 81
  text: "蹭好运！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 82
  text: "咱们携手炸街去"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 83
  text: "快来和星宝贴贴"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 84
  text: "能和我跳一支冰上华尔兹吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 85
  text: "和我优雅对舞吧！"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 86
  text: "来一起默契比心吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 87
  text: "让我来把你举高高！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 88
  text: "快来，有个小秘密跟你说！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 89
  text: "来一起鞠个躬吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 90
  text: "我来为你撑伞吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 91
  text: "我喜欢你，嫁给我吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 92
  text: "别跑！小蝴蝶~"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 93
  text: "舞动青春，我最闪亮！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 94
  text: "我们可真棒，庆祝一下吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 95
  text: "让我们像天鹅一样优雅起舞吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 96
  text: "你的节奏感真棒！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 97
  text: "我跟你关系好！我们出去玩"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 98
  text: "好久没有跳舞了"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 99
  text: "你好冷漠"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 100
  text: "我想哭"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 101
  text: "我们的招牌动作是什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 102
  text: "我好孤独"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 103
  text: "好想你呀！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 104
  text: "我成功了"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 105
  text: "你好可爱，我真喜欢你"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 106
  text: "我已经好久没拍照了。"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 107
  text: "今天是我的生日"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 108
  text: "最近好累啊！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 109
  text: "我生气了，不想说话"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 110
  text: "好饿哦！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 111
  text: "你唱的真好听"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 112
  text: "你跳的真好"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 113
  text: "最近好幸运"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 114
  text: "我也想出去玩"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 115
  text: "不想理你了~"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 116
  text: "我想去滑冰了！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 117
  text: "我也想要跳舞。"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 118
  text: "爱你哦！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 119
  text: "要举高高！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 120
  text: "我想知道你的秘密"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 121
  text: "好大的雨"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 122
  text: "给我制造点惊喜吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 123
  text: "我们比一比吧！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 124
  text: "我感觉你很开心"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 125
  text: "我有点紧张"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
}
rows {
  id: 1001
  text: "退出海龟汤"
  enable: true
  priority: 2
  type: 1
  contentType: 2
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 1002
  text: "给个提示"
  enable: true
  priority: 1
  type: 1
  contentType: 2
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 1003
  text: "更换汤面"
  enable: true
  priority: 2
  type: 1
  contentType: 2
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 2001
  text: "我放弃说服了"
  enable: true
  priority: 0
  type: 1
  contentType: 4
  usageType: QCUT_Lobby
}
rows {
  id: 2002
  text: "好好鸭你最好了"
  enable: true
  priority: 0
  type: 1
  contentType: 4
  usageType: QCUT_Lobby
}
rows {
  id: 2003
  text: "求求你答应我嘛"
  enable: true
  priority: 0
  type: 1
  contentType: 4
  usageType: QCUT_Lobby
}
rows {
  id: 126
  text: "给我压岁钱"
  enable: false
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 127
  text: "什么时候回家过年？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 128
  text: "我配拥有一个红包吗？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 129
  text: "把你的红包都给我"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 130
  text: "过年回家吗？"
  enable: false
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 131
  text: "今年有什么目标？"
  enable: false
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 132
  text: "你的梦想今年能实现吗？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 133
  text: "帮帮我"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 134
  text: "这豆角真老啊！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 135
  text: "无语"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 136
  text: "我太想进步了，做互扇搭子吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 137
  text: "爱需不需要勇气？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 138
  text: "你怎么不笑？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 139
  text: "我要蛐蛐人了"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 140
  text: "那咋了"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 141
  text: "我是奶龙"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 142
  text: "咱就是说，有没有一种可能……"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 143
  text: "我真的会谢！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 144
  text: "夺笋呐！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 145
  text: "不会吧，不会吧~"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 146
  text: "我直接一个好家伙！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 147
  text: "我被秀了一脸。"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 148
  text: "给我发红包"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 149
  text: "你有对象吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 150
  text: "怎么还不过年？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 151
  text: "我是三好学生"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 152
  text: "年夜饭吃什么？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 153
  text: "放假了，我要回老家了"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 154
  text: "过年有什么计划？"
  enable: false
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 155
  text: "朋友过年了没上线"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 156
  text: "我买了新衣服"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 157
  text: "期末没考好"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 158
  text: "为什么一直站在这里？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 159
  text: "我可以加你好友吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 160
  text: "过年回家见爷爷奶奶啰~"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 161
  text: "你的衣服真好看"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 162
  text: "今年是什么年呀？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 163
  text: "你好傻"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 164
  text: "你喜欢吃烤鱼吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 165
  text: "我要退游"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 166
  text: "唱个难忘今宵吧"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 167
  text: "你能帮我找个星搭子吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 168
  text: "讨厌你"
  enable: true
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 169
  text: "你衣服真丑"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 170
  text: "我要开学了"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 171
  text: "寒假作业写完了吗？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 172
  text: "你能帮我做作业吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 173
  text: "鸭鸭你真是我最好的朋友！"
  enable: true
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 174
  text: "我想出去玩！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 175
  text: "怎么可以不上学？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 176
  text: "你见过奇迹吗？"
  enable: false
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 177
  text: "你怎么呆呆的？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 178
  text: "不想早起"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 179
  text: "你怎么不开心？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 180
  text: "我来为你发声！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 181
  text: "你个人机"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 182
  text: "说个峡谷3V3的必胜秘诀"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 183
  text: "告诉我一点峡谷小妙招"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 184
  text: "鸭鸭，你喜欢什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 185
  text: "别说话了"
  enable: true
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 186
  text: "写作业写到红温了！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 187
  text: "想放假！"
  enable: false
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 188
  text: "这太难了"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 189
  text: "假期什么时候来？"
  enable: false
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 190
  text: "你瘦了吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 191
  text: "鸭鸭你为什么不上学？"
  enable: true
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 192
  text: "你放假了吗？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 193
  text: "你行不行啊？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 194
  text: "什么是专属伙伴？"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 195
  text: "你喜欢晴天还是雨天？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 196
  text: "你最喜欢什么粽子？"
  enable: false
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 197
  text: "大人能过儿童节吗？"
  enable: false
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 198
  text: "陪我一起玩"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 199
  text: "我被粽子做局了！"
  enable: false
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 200
  text: "鸭鸭，你可真是我的妈系好友。"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 201
  text: "有什么好听的歌推荐吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 202
  text: "鸭鸭，我是你的什么？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 203
  text: "什么时候才能不下雨啊！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 204
  text: "叽里咕噜说啥呢！"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 205
  text: "好气哦~"
  enable: true
  priority: 0
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 206
  text: "你是谁？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 207
  text: "你吃甜粽子还是咸粽子？"
  enable: false
  priority: 1
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 208
  text: "帮我做作业"
  enable: true
  priority: 2
  type: 1
  contentType: 1
  usageType: QCUT_PrivateChat
}
rows {
  id: 209
  text: "我不需要做题了"
  enable: true
  priority: 2
  type: 1
  contentType: 3
  usageType: QCUT_PrivateChat
}
rows {
  id: 210
  text: "快期末了，你复习了吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 211
  text: "老师教的你都忘了吗？"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 212
  text: "考试准备的怎么样？"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 213
  text: "人人都不看好你"
  enable: true
  priority: 0
  type: 2
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
rows {
  id: 214
  text: "我想放暑假"
  enable: true
  priority: 1
  type: 1
  contentType: 1
  usageType: QCUT_Lobby
  usageType: QCUT_PrivateChat
}
