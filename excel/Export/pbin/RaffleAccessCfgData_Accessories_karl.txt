com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_karl.xlsx sheet:活动-配饰卡池
rows {
  raffleId: 50000001
  name: "夏日花园"
  startTime {
    seconds: 1726761600
  }
  endTime {
    seconds: 1728489600
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 50000001
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 57
  text: "每次祈愿不会获得重复奖励，9次必得LULU猪时装"
  lowestVersion: "1.3.18.23"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1726761600
  }
  showEndTime {
    seconds: 1728489600
  }
  isShow: true
  juniorItemAnimType: 2
}
rows {
  raffleId: 50000002
  name: "西行之路"
  startTime {
    seconds: 1725552000
  }
  endTime {
    seconds: 1727020799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 50000002
    subPoolIds: 50000002
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 224
  text: "每次祈愿不会获得重复奖励，9次必得齐天大圣"
  lowestVersion: "1.3.18.23"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "齐天大圣"
  raffleTagIcon: "T_MonkeyKing_Icon_Tab1"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1725552000
  }
  showEndTime {
    seconds: 1727020799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 50000003
  name: "西行之路"
  startTime {
    seconds: 1725552000
  }
  endTime {
    seconds: 1727020799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 50000003
    subPoolIds: 50000003
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 224
  text: "每次祈愿不会获得重复奖励，9次必得金蝉子"
  lowestVersion: "1.3.18.23"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "金蝉子"
  raffleTagIcon: "T_MonkeyKing_Icon_Tab2"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  showStartTime {
    seconds: 1725552000
  }
  showEndTime {
    seconds: 1727020799
  }
  isShow: true
  juniorItemAnimType: 1
}
rows {
  raffleId: 50000004
  name: "夏日花园"
  startTime {
    seconds: 1748016000
  }
  endTime {
    seconds: 1749743999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 50000004
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 10011
  text: "每次祈愿不会获得重复奖励，9次必得LULU猪时装"
  lowestVersion: "1.3.88.53"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  juniorItemAnimType: 2
}
rows {
  raffleId: 50000005
  name: "环绕特效"
  startTime {
    seconds: 1748102400
  }
  endTime {
    seconds: 1753459199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 50000005
  }
  dailyLimit: 9
  maxLimit: 9
  textRuleId: 379
  text: "5次内必得一个环绕特效，9次必得所有物品"
  activityImage: "ModSuit_0025_HalfBody.png"
  viewIndex: 8
  viewIndex: 6
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 7
  viewIndex: 9
  previewTag: "Camera_Ornament12"
  showStartTime {
    seconds: 1748102400
  }
  showEndTime {
    seconds: 1753459199
  }
  isShow: true
  juniorItemAnimType: 1
}
