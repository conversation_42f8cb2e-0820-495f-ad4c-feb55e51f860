com.tencent.wea.xlsRes.table_QualifyingLevelDimensionConditionData
excel/xls/J_奖杯征程.xlsx sheet:规则维度
rows {
  id: 1001
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "个人第1名"
}
rows {
  id: 1002
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "个人第2名"
}
rows {
  id: 1003
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "个人第3名"
}
rows {
  id: 1004
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "个人第4名"
}
rows {
  id: 1005
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "个人第5名"
}
rows {
  id: 1006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "个人第6名"
}
rows {
  id: 1007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "个人第7名"
}
rows {
  id: 1008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "个人第8名"
}
rows {
  id: 1009
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "个人第9名"
}
rows {
  id: 1010
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "个人第10名"
}
rows {
  id: 1011
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 11
  rangeRight: 11
  displayText: "个人第11名"
}
rows {
  id: 1012
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 12
  rangeRight: 12
  displayText: "个人第12名"
}
rows {
  id: 1013
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 13
  rangeRight: 13
  displayText: "个人第13名"
}
rows {
  id: 1014
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 14
  rangeRight: 14
  displayText: "个人第14名"
}
rows {
  id: 1015
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 15
  rangeRight: 15
  displayText: "个人第15名"
}
rows {
  id: 1016
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 16
  rangeRight: 16
  displayText: "个人第16名"
}
rows {
  id: 1017
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 17
  rangeRight: 17
  displayText: "个人第17名"
}
rows {
  id: 1018
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 18
  rangeRight: 18
  displayText: "个人第18名"
}
rows {
  id: 1019
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 19
  rangeRight: 19
  displayText: "个人第19名"
}
rows {
  id: 1020
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 20
  rangeRight: 20
  displayText: "个人第20名"
}
rows {
  id: 1021
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 21
  rangeRight: 21
  displayText: "个人第21名"
}
rows {
  id: 1022
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 22
  rangeRight: 22
  displayText: "个人第22名"
}
rows {
  id: 1023
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 23
  rangeRight: 23
  displayText: "个人第23名"
}
rows {
  id: 1024
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 24
  rangeRight: 24
  displayText: "个人第24名"
}
rows {
  id: 1025
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 25
  rangeRight: 25
  displayText: "个人第25名"
}
rows {
  id: 1026
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 26
  rangeRight: 26
  displayText: "个人第26名"
}
rows {
  id: 1027
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 27
  rangeRight: 27
  displayText: "个人第27名"
}
rows {
  id: 1028
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 28
  rangeRight: 28
  displayText: "个人第28名"
}
rows {
  id: 1029
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 29
  rangeRight: 29
  displayText: "个人第29名"
}
rows {
  id: 1030
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 30
  rangeRight: 30
  displayText: "个人第30名"
}
rows {
  id: 1031
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 31
  rangeRight: 31
  displayText: "个人第31名"
}
rows {
  id: 1032
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 32
  rangeRight: 32
  displayText: "个人第32名"
}
rows {
  id: 1033
  battleEventType: BET_TEAM_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "队伍第1名"
}
rows {
  id: 1034
  battleEventType: BET_TEAM_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "队伍第2名"
}
rows {
  id: 1035
  battleEventType: BET_TEAM_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "队伍第3名"
}
rows {
  id: 1036
  battleEventType: BET_TEAM_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "队伍第4名"
}
rows {
  id: 1037
  battleEventType: BET_TEAM_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "队伍第5名"
}
rows {
  id: 1038
  battleEventType: BET_TEAM_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "队伍第6名"
}
rows {
  id: 1039
  battleEventType: BET_TEAM_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "队伍第7名"
}
rows {
  id: 1040
  battleEventType: BET_TEAM_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "队伍第8名"
}
rows {
  id: 1041
  battleEventType: BET_TEAM_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "队伍第9名"
}
rows {
  id: 1042
  battleEventType: BET_TEAM_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "队伍第10名"
}
rows {
  id: 1043
  battleEventType: BET_TEAM_RANK
  rangeLeft: 11
  rangeRight: 11
  displayText: "队伍第11名"
}
rows {
  id: 1044
  battleEventType: BET_TEAM_RANK
  rangeLeft: 12
  rangeRight: 12
  displayText: "队伍第12名"
}
rows {
  id: 1045
  battleEventType: BET_TEAM_RANK
  rangeLeft: 13
  rangeRight: 13
  displayText: "队伍第13名"
}
rows {
  id: 1046
  battleEventType: BET_TEAM_RANK
  rangeLeft: 14
  rangeRight: 14
  displayText: "队伍第14名"
}
rows {
  id: 1047
  battleEventType: BET_TEAM_RANK
  rangeLeft: 15
  rangeRight: 15
  displayText: "队伍第15名"
}
rows {
  id: 1048
  battleEventType: BET_TEAM_RANK
  rangeLeft: 16
  rangeRight: 16
  displayText: "队伍第16名"
}
rows {
  id: 1049
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 1050
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 1051
  battleEventType: BET_CHAMPION_LEVEL
  rangeLeft: 0
  rangeRight: 0
  displayText: "非决胜关"
}
rows {
  id: 1052
  battleEventType: BET_CHAMPION_LEVEL
  rangeLeft: 1
  rangeRight: 1
  displayText: "决胜关"
}
rows {
  id: 1053
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 1054
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 2001
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "第1名"
}
rows {
  id: 2002
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "第2名"
}
rows {
  id: 2003
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "第3名"
}
rows {
  id: 2004
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "第4名"
}
rows {
  id: 2005
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "第5名"
}
rows {
  id: 2006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "第6名"
}
rows {
  id: 2007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "第7名"
}
rows {
  id: 2008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "第8名"
}
rows {
  id: 2009
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "第9名"
}
rows {
  id: 2010
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "第10名"
}
rows {
  id: 2011
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 11
  rangeRight: 11
  displayText: "第11名"
}
rows {
  id: 2012
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 12
  rangeRight: 12
  displayText: "第12名"
}
rows {
  id: 2013
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 13
  rangeRight: 13
  displayText: "第13名"
}
rows {
  id: 2014
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 14
  rangeRight: 14
  displayText: "第14名"
}
rows {
  id: 2015
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 15
  rangeRight: 15
  displayText: "第15名"
}
rows {
  id: 2016
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 2017
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 2018
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 2
  rangeRight: 2
  displayText: "平局"
}
rows {
  id: 2019
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 2020
  battleEventType: BET_LEVEL_CAMP_TYPE
  rangeLeft: 1
  rangeRight: 1
  displayText: "狼人阵营"
}
rows {
  id: 2021
  battleEventType: BET_LEVEL_CAMP_TYPE
  rangeLeft: 2
  rangeRight: 2
  displayText: "平民阵营"
}
rows {
  id: 2022
  battleEventType: BET_LEVEL_CAMP_TYPE
  rangeLeft: 3
  rangeRight: 3
  displayText: "中立阵营"
}
rows {
  id: 2023
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 3001
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 3002
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 3003
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 2
  rangeRight: 2
  displayText: "平局"
}
rows {
  id: 3004
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 3005
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 3006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "第1名"
}
rows {
  id: 3007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "第2名"
}
rows {
  id: 3008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "第3名"
}
rows {
  id: 3009
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "第4名"
}
rows {
  id: 3010
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "第5名"
}
rows {
  id: 3011
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "第6名"
}
rows {
  id: 3012
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "第7名"
}
rows {
  id: 3013
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "第8名"
}
rows {
  id: 3014
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "第9名"
}
rows {
  id: 3015
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "第10名"
}
rows {
  id: 3016
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 11
  rangeRight: 11
  displayText: "第11名"
}
rows {
  id: 3017
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 12
  rangeRight: 12
  displayText: "第12名"
}
rows {
  id: 3018
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 13
  rangeRight: 13
  displayText: "第13名"
}
rows {
  id: 3019
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 14
  rangeRight: 14
  displayText: "第14名"
}
rows {
  id: 3020
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 15
  rangeRight: 15
  displayText: "第15名"
}
rows {
  id: 3021
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 16
  rangeRight: 16
  displayText: "第16名"
}
rows {
  id: 3022
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 17
  rangeRight: 17
  displayText: "第17名"
}
rows {
  id: 3023
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 18
  rangeRight: 18
  displayText: "第18名"
}
rows {
  id: 3024
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 19
  rangeRight: 19
  displayText: "第19名"
}
rows {
  id: 3025
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 20
  rangeRight: 20
  displayText: "第20名"
}
rows {
  id: 3026
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 21
  rangeRight: 21
  displayText: "第21名"
}
rows {
  id: 3027
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 22
  rangeRight: 22
  displayText: "第22名"
}
rows {
  id: 3028
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 23
  rangeRight: 23
  displayText: "第23名"
}
rows {
  id: 3029
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 24
  rangeRight: 24
  displayText: "第24名"
}
rows {
  id: 3030
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 25
  rangeRight: 25
  displayText: "第25名"
}
rows {
  id: 3031
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 26
  rangeRight: 26
  displayText: "第26名"
}
rows {
  id: 3032
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 27
  rangeRight: 27
  displayText: "第27名"
}
rows {
  id: 3033
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 28
  rangeRight: 28
  displayText: "第28名"
}
rows {
  id: 3034
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 29
  rangeRight: 29
  displayText: "第29名"
}
rows {
  id: 3035
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 30
  rangeRight: 30
  displayText: "第30名"
}
rows {
  id: 3036
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 31
  rangeRight: 31
  displayText: "第31名"
}
rows {
  id: 3037
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 32
  rangeRight: 32
  displayText: "第32名"
}
rows {
  id: 3038
  battleEventType: BET_TEAM_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "团队第1名"
}
rows {
  id: 3039
  battleEventType: BET_TEAM_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "团队第2名"
}
rows {
  id: 3040
  battleEventType: BET_TEAM_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "团队第3名"
}
rows {
  id: 3041
  battleEventType: BET_TEAM_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "团队第4名"
}
rows {
  id: 3042
  battleEventType: BET_TEAM_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "团队第5名"
}
rows {
  id: 3043
  battleEventType: BET_TEAM_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "团队第6名"
}
rows {
  id: 3044
  battleEventType: BET_TEAM_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "团队第7名"
}
rows {
  id: 3045
  battleEventType: BET_TEAM_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "团队第8名"
}
rows {
  id: 3046
  battleEventType: BET_TEAM_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "团队第9名"
}
rows {
  id: 3047
  battleEventType: BET_TEAM_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "团队第10名"
}
rows {
  id: 3048
  battleEventType: BET_TEAM_RANK
  rangeLeft: 11
  rangeRight: 11
  displayText: "团队第11名"
}
rows {
  id: 3049
  battleEventType: BET_TEAM_RANK
  rangeLeft: 12
  rangeRight: 12
  displayText: "团队第12名"
}
rows {
  id: 3050
  battleEventType: BET_TEAM_RANK
  rangeLeft: 13
  rangeRight: 13
  displayText: "团队第13名"
}
rows {
  id: 3051
  battleEventType: BET_TEAM_RANK
  rangeLeft: 14
  rangeRight: 14
  displayText: "团队第14名"
}
rows {
  id: 3052
  battleEventType: BET_TEAM_RANK
  rangeLeft: 15
  rangeRight: 15
  displayText: "团队第15名"
}
rows {
  id: 3053
  battleEventType: BET_TEAM_RANK
  rangeLeft: 16
  rangeRight: 16
  displayText: "团队第16名"
}
rows {
  id: 4001
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "第1名"
}
rows {
  id: 4002
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "第2名"
}
rows {
  id: 4003
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "第3名"
}
rows {
  id: 4004
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "第4名"
}
rows {
  id: 4005
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "第5名"
}
rows {
  id: 4006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "第6名"
}
rows {
  id: 4007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "第7名"
}
rows {
  id: 4008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "第8名"
}
rows {
  id: 4009
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "第9名"
}
rows {
  id: 4010
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "第10名"
}
rows {
  id: 4011
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 11
  rangeRight: 11
  displayText: "第11名"
}
rows {
  id: 4012
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 12
  rangeRight: 12
  displayText: "第12名"
}
rows {
  id: 4013
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 4014
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 4015
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 4016
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 4017
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 2
  rangeRight: 2
  displayText: "平局"
}
rows {
  id: 4018
  battleEventType: BET_LEVEL_CAMP_TYPE
  rangeLeft: 0
  rangeRight: 0
  displayText: "搜捕者"
}
rows {
  id: 4019
  battleEventType: BET_LEVEL_CAMP_TYPE
  rangeLeft: 1
  rangeRight: 1
  displayText: "伪装者"
}
rows {
  id: 5001
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 5002
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 5003
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 2
  rangeRight: 2
  displayText: "平局"
}
rows {
  id: 5004
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 5005
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 5006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "第1名"
}
rows {
  id: 5007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "第2名"
}
rows {
  id: 5008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "第3名"
}
rows {
  id: 5009
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "第4名"
}
rows {
  id: 5010
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "第5名"
}
rows {
  id: 5011
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "第6名"
}
rows {
  id: 5012
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "第7名"
}
rows {
  id: 5013
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "第8名"
}
rows {
  id: 5014
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "第9名"
}
rows {
  id: 5015
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "第10名"
}
rows {
  id: 5016
  battleEventType: BET_TEAM_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "团队第1名"
}
rows {
  id: 5017
  battleEventType: BET_TEAM_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "团队第2名"
}
rows {
  id: 6001
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 9
  rangeRight: 9
  displayText: "星宝逃离分"
}
rows {
  id: 6002
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 8
  rangeRight: 8
  displayText: "星宝逃离分"
}
rows {
  id: 6003
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 7
  rangeRight: 7
  displayText: "星宝逃离分"
}
rows {
  id: 6004
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 6
  rangeRight: 6
  displayText: "星宝逃离分"
}
rows {
  id: 6005
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 5
  rangeRight: 5
  displayText: "星宝逃离分"
}
rows {
  id: 6006
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 4
  rangeRight: 4
  displayText: "星宝逃离分"
}
rows {
  id: 6007
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 3
  rangeRight: 3
  displayText: "星宝逃离分"
}
rows {
  id: 6008
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 2
  rangeRight: 2
  displayText: "星宝逃离分"
}
rows {
  id: 6009
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 1
  rangeRight: 1
  displayText: "星宝逃离分"
}
rows {
  id: 6010
  battleEventType: BET_LEVEL_CHASE_PLAYER_ESCAPECOUNT
  rangeLeft: 0
  rangeRight: 0
  displayText: "星宝逃离分"
}
rows {
  id: 6011
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 9
  rangeRight: 9
  displayText: "暗星淘汰分"
}
rows {
  id: 6012
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 8
  rangeRight: 8
  displayText: "暗星淘汰分"
}
rows {
  id: 6013
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 7
  rangeRight: 7
  displayText: "暗星淘汰分"
}
rows {
  id: 6014
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 6
  rangeRight: 6
  displayText: "暗星淘汰分"
}
rows {
  id: 6015
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 5
  rangeRight: 5
  displayText: "暗星淘汰分"
}
rows {
  id: 6016
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 4
  rangeRight: 4
  displayText: "暗星淘汰分"
}
rows {
  id: 6017
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 3
  rangeRight: 3
  displayText: "暗星淘汰分"
}
rows {
  id: 6018
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 2
  rangeRight: 2
  displayText: "暗星淘汰分"
}
rows {
  id: 6019
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 1
  rangeRight: 1
  displayText: "暗星淘汰分"
}
rows {
  id: 6020
  battleEventType: BET_LEVEL_CHASE_BOSS_EXILEPLAYER
  rangeLeft: 0
  rangeRight: 0
  displayText: "暗星淘汰分"
}
rows {
  id: 6021
  battleEventType: BET_RANK_IN_TEAM
  rangeLeft: 4
  rangeRight: 4
  displayText: "队伍排名分"
}
rows {
  id: 6022
  battleEventType: BET_RANK_IN_TEAM
  rangeLeft: 3
  rangeRight: 3
  displayText: "队伍排名分"
}
rows {
  id: 6023
  battleEventType: BET_RANK_IN_TEAM
  rangeLeft: 2
  rangeRight: 2
  displayText: "队伍排名分"
}
rows {
  id: 6024
  battleEventType: BET_RANK_IN_TEAM
  rangeLeft: 1
  rangeRight: 1
  displayText: "队伍排名分"
}
rows {
  id: 6025
  battleEventType: BET_LEVEL_CHASE_HANGUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "挂机惩罚"
}
rows {
  id: 6026
  battleEventType: BET_LEVEL_CHASE_CAMP_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 6027
  battleEventType: BET_LEVEL_CHASE_CAMP_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 6028
  battleEventType: BET_LEVEL_CHASE_CAMP_RESULT
  rangeLeft: 2
  rangeRight: 2
  displayText: "平局"
}
rows {
  id: 6029
  battleEventType: BET_LEVEL_END_REASON
  rangeLeft: 3
  rangeRight: 3
  displayText: "中途放弃"
}
rows {
  id: 6030
  battleEventType: BET_LEVEL_CAMP_TYPE
  rangeLeft: 2
  rangeRight: 2
  displayText: "星宝"
}
rows {
  id: 6031
  battleEventType: BET_LEVEL_CAMP_TYPE
  rangeLeft: 1
  rangeRight: 1
  displayText: "暗星"
}
rows {
  id: 6032
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 6033
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 7001
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 7002
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 7003
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "第1名"
}
rows {
  id: 7004
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "第1名"
}
rows {
  id: 7005
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "第1名"
}
rows {
  id: 7006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "第2名"
}
rows {
  id: 7007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "第2名"
}
rows {
  id: 7008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 6
  rangeRight: 6
  displayText: "第2名"
}
rows {
  id: 7009
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 7
  rangeRight: 7
  displayText: "第3名"
}
rows {
  id: 7010
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 8
  rangeRight: 8
  displayText: "第3名"
}
rows {
  id: 7011
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 9
  rangeRight: 9
  displayText: "第3名"
}
rows {
  id: 7012
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 10
  rangeRight: 10
  displayText: "第4名"
}
rows {
  id: 7013
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 11
  rangeRight: 11
  displayText: "第4名"
}
rows {
  id: 7014
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 12
  rangeRight: 12
  displayText: "第4名"
}
rows {
  id: 8001
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 8002
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 8003
  battleEventType: BET_LEVEL_CHASE_HANGUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "挂机惩罚"
}
rows {
  id: 8004
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "第1名"
}
rows {
  id: 8005
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "第2名"
}
rows {
  id: 8006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "第3名"
}
rows {
  id: 8007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "第4名"
}
rows {
  id: 8008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "第5名"
}
rows {
  id: 8009
  battleEventType: BET_ARENA_TOTAL_KILL
  rangeLeft: 3
  rangeRight: 4
  displayText: "击杀3/4次"
}
rows {
  id: 8010
  battleEventType: BET_ARENA_TOTAL_KILL
  rangeLeft: 5
  rangeRight: 999
  displayText: "击杀至少5次"
}
rows {
  id: 8011
  battleEventType: BET_ARENA_TOTAL_ASSIST
  rangeLeft: 5
  rangeRight: 999
  displayText: "助攻至少5次"
}
rows {
  id: 9001
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 9002
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 9003
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 2
  rangeRight: 2
  displayText: "平局"
}
rows {
  id: 10001
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 3
  rangeRight: 3
  displayText: "中途退出"
}
rows {
  id: 10002
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 0
  rangeRight: 0
  displayText: "胜利"
}
rows {
  id: 10003
  battleEventType: BET_LEVEL_RESULT
  rangeLeft: 1
  rangeRight: 1
  displayText: "失败"
}
rows {
  id: 10004
  battleEventType: BET_MAYDAY_NORMAL_PLAY
  rangeLeft: 1
  rangeRight: 1
  displayText: "游玩合作模式"
}
rows {
  id: 10005
  battleEventType: BET_MAYDAY_NORMAL_WIN_LEVEL_1
  rangeLeft: 0
  rangeRight: 0
  displayText: "在简单难度中未完成上交目标"
}
rows {
  id: 10006
  battleEventType: BET_MAYDAY_NORMAL_WIN_LEVEL_1
  rangeLeft: 1
  rangeRight: 1
  displayText: "在简单难度完成上交目标"
}
rows {
  id: 10007
  battleEventType: BET_MAYDAY_NORMAL_WIN_LEVEL_2
  rangeLeft: 0
  rangeRight: 0
  displayText: "在中等难度中未完成上交目标"
}
rows {
  id: 10008
  battleEventType: BET_MAYDAY_NORMAL_WIN_LEVEL_2
  rangeLeft: 1
  rangeRight: 1
  displayText: "在中等难度完成上交目标"
}
rows {
  id: 10009
  battleEventType: BET_MAYDAY_NORMAL_WIN_LEVEL_3
  rangeLeft: 0
  rangeRight: 0
  displayText: "在困难难度中未完成上交目标"
}
rows {
  id: 10010
  battleEventType: BET_MAYDAY_NORMAL_WIN_LEVEL_3
  rangeLeft: 1
  rangeRight: 1
  displayText: "在困难难度完成上交目标"
}
rows {
  id: 10011
  battleEventType: BET_MAYDAY_NORMAL_VALUE_TO_SHIP
  rangeLeft: 0
  rangeRight: 599
  displayText: "放进飞船物资不满600"
}
rows {
  id: 10012
  battleEventType: BET_MAYDAY_NORMAL_VALUE_TO_SHIP
  rangeLeft: 600
  rangeRight: 99999
  displayText: "放进飞船物资大于等于600"
}
rows {
  id: 10013
  battleEventType: BET_MAYDAY_SPY_PLAY
  rangeLeft: 1
  rangeRight: 1
  displayText: "游玩内鬼模式"
}
rows {
  id: 10014
  battleEventType: BET_MAYDAY_SPY_WIN
  rangeLeft: 0
  rangeRight: 0
  displayText: "己方阵营未获胜"
}
rows {
  id: 10015
  battleEventType: BET_MAYDAY_SPY_WIN
  rangeLeft: 1
  rangeRight: 1
  displayText: "己方阵营获胜一次"
}
rows {
  id: 10016
  battleEventType: BET_MAYDAY_SPY_KILL_PLAYER
  rangeLeft: 0
  rangeRight: 0
  displayText: "以内鬼身份未淘汰其他玩家"
}
rows {
  id: 10017
  battleEventType: BET_MAYDAY_SPY_KILL_PLAYER
  rangeLeft: 1
  rangeRight: 7
  displayText: "以内鬼身份淘汰了其他玩家"
}
rows {
  id: 10018
  battleEventType: BET_MAYDAY_SPY_GOOD_COMMIT_VALUE
  rangeLeft: 0
  rangeRight: 399
  displayText: "以探员身份累计上交物资不满400"
}
rows {
  id: 10019
  battleEventType: BET_MAYDAY_SPY_GOOD_COMMIT_VALUE
  rangeLeft: 400
  rangeRight: 99999
  displayText: "以探员身份累计上交物资大于等于400"
}
rows {
  id: 10020
  battleEventType: BET_MAYDAY_INF_PLAY
  rangeLeft: 1
  rangeRight: 1
  displayText: "游玩无尽模式"
}
rows {
  id: 10021
  battleEventType: BET_MAYDAY_INF_WIN
  rangeLeft: 0
  rangeRight: 0
  displayText: "未完成上交目标"
}
rows {
  id: 10022
  battleEventType: BET_MAYDAY_INF_WIN
  rangeLeft: 1
  rangeRight: 1
  displayText: "完成上交目标"
}
rows {
  id: 10023
  battleEventType: BET_MAYDAY_INF_VALUE_TO_SHIP
  rangeLeft: 0
  rangeRight: 1199
  displayText: "放进飞船物资不满1200"
}
rows {
  id: 10024
  battleEventType: BET_MAYDAY_INF_VALUE_TO_SHIP
  rangeLeft: 1200
  rangeRight: 99999
  displayText: "放进飞船物资大于等于1200"
}
rows {
  id: 10025
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "中途未退出"
}
rows {
  id: 10026
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途退出"
}
rows {
  id: 11001
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "中途放弃"
}
rows {
  id: 11002
  battleEventType: BET_IS_GIVEUP
  rangeLeft: 0
  rangeRight: 0
  displayText: "未中途放弃"
}
rows {
  id: 11003
  battleEventType: BET_LEVEL_CHASE_HANGUP
  rangeLeft: 1
  rangeRight: 1
  displayText: "挂机惩罚"
}
rows {
  id: 11004
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 1
  rangeRight: 1
  displayText: "第1名"
}
rows {
  id: 11005
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 2
  rangeRight: 2
  displayText: "第2名"
}
rows {
  id: 11006
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 3
  rangeRight: 3
  displayText: "第3名"
}
rows {
  id: 11007
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 4
  rangeRight: 4
  displayText: "第4名"
}
rows {
  id: 11008
  battleEventType: BET_LEVEL_RANK
  rangeLeft: 5
  rangeRight: 5
  displayText: "第5名"
}
rows {
  id: 11009
  battleEventType: BET_ARENA_TOTAL_KILL
  rangeLeft: 3
  rangeRight: 4
  displayText: "击杀3/4次"
}
rows {
  id: 11010
  battleEventType: BET_ARENA_TOTAL_KILL
  rangeLeft: 5
  rangeRight: 999
  displayText: "击杀至少5次"
}
rows {
  id: 11011
  battleEventType: BET_ARENA_TOTAL_ASSIST
  rangeLeft: 5
  rangeRight: 999
  displayText: "助攻至少5次"
}
