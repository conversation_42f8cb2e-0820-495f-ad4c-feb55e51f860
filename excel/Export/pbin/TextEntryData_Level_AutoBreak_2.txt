com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_关卡.xlsx sheet:文本配置
rows {
  content: "<InLevel_Highlight>狼人要咆哮了</>，星宝快跑！##3"
  switch: 1
  stringId: "LevelScene_70245_2"
}
rows {
  content: "淘气小幽灵在追赶!<InLevel_Highlight>向前跑!</>"
  switch: 1
  stringId: "LevelScene_70248_0"
}
rows {
  content: "躲避攻击！努力<InLevel_Highlight>生存</>下去！"
  switch: 1
  stringId: "LevelScene_70250_0"
}
rows {
  content: "注意！<InLevel_Highlight>边缘平台即将下落</>！"
  switch: 1
  stringId: "LevelScene_70250_1"
}
rows {
  content: "注意！<InLevel_Highlight>平台即将下落</>！"
  switch: 1
  stringId: "LevelScene_70250_2"
}
rows {
  content: "超强补给<InLevel_Highlight>已送达！</>"
  switch: 1
  stringId: "LevelScene_70254_0"
}
rows {
  content: "决战时刻！快去<InLevel_Highlight>中间！</>"
  switch: 1
  stringId: "LevelScene_70254_1"
}
rows {
  content: "空投补给<InLevel_Highlight>已送达！</>"
  switch: 1
  stringId: "LevelScene_70254_2"
}
rows {
  content: "第二波补给<InLevel_Highlight>已送达！</>"
  switch: 1
  stringId: "LevelScene_70254_3"
}
rows {
  content: "冰面解冻，<InLevel_Highlight>水位上涨</>！##5"
  switch: 1
  stringId: "LevelScene_70255_3"
}
rows {
  content: "水位上涨，将淹没<InLevel_Highlight>所有人</>！##5"
  switch: 1
  stringId: "LevelScene_70255_4"
}
rows {
  content: "<InLevel_Highlight>搭建家具爬高</>避免被淹没！"
  switch: 1
  stringId: "LevelScene_70255_5"
}
rows {
  content: "当心！<InLevel_Highlight>前方滚石来袭</>！"
  switch: 1
  stringId: "LevelScene_70256_1"
}
rows {
  content: "小心！<InLevel_Highlight>白泽即将释放冰火滚石</>！"
  switch: 1
  stringId: "LevelScene_70256_2"
}
rows {
  content: "注意！<InLevel_Highlight>边缘平台即将下落</>！"
  switch: 1
  stringId: "LevelScene_70257_0"
}
rows {
  content: "注意！<InLevel_Highlight>中间平台即将下落</>！"
  switch: 1
  stringId: "LevelScene_70258_0"
}
rows {
  content: "及时进入水罩！努力<InLevel_Highlight>生存</>下去！"
  switch: 1
  stringId: "LevelScene_70259_0"
}
rows {
  content: "炽阳即将爆炸！注意<InLevel_Highlight>躲避火浪</>！"
  switch: 1
  stringId: "LevelScene_70259_1"
}
rows {
  content: "你现在颜色是<Blue24>蓝色</>哟！"
  switch: 1
  stringId: "LevelScene_60269_0"
}
rows {
  content: "你现在颜色是<Orange24>橙色</>哟！"
  switch: 1
  stringId: "LevelScene_60269_1"
}
rows {
  content: "注意！<InLevel_Highlight>20s后所有幽灵将被淘汰</>！##20"
  switch: 1
  stringId: "LevelScene_70261_0"
}
rows {
  content: "躲避场上幽灵发射的攻击，<InLevel_Highlight>避免成为幽灵</>！"
  switch: 1
  stringId: "LevelScene_70261_1"
}
