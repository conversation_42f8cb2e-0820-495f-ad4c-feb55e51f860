com.tencent.wea.xlsRes.table_SurroundingsEffectData
excel/xls/D_道具表_环绕物.xlsx sheet:环绕物特效
rows {
  id: 850001
  type: 1
  bIsFollow: false
  bIsMutuallyExclusive: true
  pos: "Root"
  posEffect: "FX_Surroundings_001"
  posOnVehicle: "Bip001"
}
rows {
  id: 850002
  type: 0
  bIsFollow: true
  bIsMutuallyExclusive: true
  speed: 700.0
  maxSpeed: 1600.0
  maxDistance: 500.0
  pos: "Bip001"
  posEffect: "FX_Surroundings_Orbiting_001"
  posOnVehicle: "Bip001"
}
rows {
  id: 850003
  type: 0
  bIsFollow: true
  bIsMutuallyExclusive: true
  speed: 700.0
  maxSpeed: 1600.0
  maxDistance: 500.0
  pos: "Socket_Surrounding"
  posEffect: "FX_Surroundings_Orbiting_001"
  posOnVehicle: "Bip001"
}
rows {
  id: 850004
  type: 1
  soundId: 40655
  bIsFollow: false
  bIsMutuallyExclusive: true
  pos: "Root"
  posEffect: "FX_Orbiting_IdleShowTest"
  posOnVehicle: "Bip001"
}
rows {
  id: 850005
  type: 0
  bIsFollow: true
  bIsMutuallyExclusive: true
  speed: 700.0
  maxSpeed: 1600.0
  maxDistance: 500.0
  pos: "Bip001"
  posEffect: "FX_Orbiting_Test"
  posOnVehicle: "Bip001"
}
rows {
  id: 850006
  type: 0
  bIsFollow: true
  bIsMutuallyExclusive: true
  speed: 700.0
  maxSpeed: 1600.0
  maxDistance: 500.0
  pos: "Root"
  posEffect: "FX_Orbiting_Test"
  posOnVehicle: "Bip001"
}
rows {
  id: 850007
  type: 0
  bIsFollow: true
  bIsMutuallyExclusive: false
  speed: 700.0
  maxSpeed: 1600.0
  maxDistance: 500.0
  pos: "Bip001"
  posEffect: "FX_Orbiting_TrailTest"
  posOnVehicle: "Bip001"
}
