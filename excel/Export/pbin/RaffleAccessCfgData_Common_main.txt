com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_主表.xlsx sheet:活动-赛季金币活跃卡池
rows {
  raffleId: 3000
  name: "赛季祈愿"
  startTime {
    seconds: 1702569600
  }
  endTime {
    seconds: 1706198399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 3000
  }
  dailyLimit: 200
  maxLimit: 9999999
  textRuleId: 10
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "0.6.1196.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1702569600
  }
  showEndTime {
    seconds: 1706198399
  }
  isShow: true
  categoryId: 1
  tagDailyLimit: 200
}
rows {
  raffleId: 30001
  name: "星梦蝴蝶"
  startTime {
    seconds: 1702569600
  }
  endTime {
    seconds: 1706198399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30001
  }
  dailyLimit: 200
  maxLimit: 9999999
  textRuleId: 56
  lowestVersion: "0.6.1196.1"
  showStartTime {
    seconds: 1702569600
  }
  showEndTime {
    seconds: 1706198399
  }
  isShow: true
  tagDailyLimit: 200
}
rows {
  raffleId: 30002
  name: "赛季祈愿"
  startTime {
    seconds: 1706198400
  }
  endTime {
    seconds: 1710431999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30002
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 101
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.2.67.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showStartTime {
    seconds: 1706198400
  }
  showEndTime {
    seconds: 1710431999
  }
  isShow: true
  categoryId: 1
  tagDailyLimit: 300
}
rows {
  raffleId: 30003
  name: "赛季祈愿"
  startTime {
    seconds: 1710432000
  }
  endTime {
    seconds: 1714060799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30003
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 125
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "0.6.1196.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1710432000
  }
  showEndTime {
    seconds: 1714060799
  }
  isShow: true
  categoryId: 1
  tagDailyLimit: 300
}
rows {
  raffleId: 30004
  name: "赛季祈愿"
  startTime {
    seconds: 1714060800
  }
  endTime {
    seconds: 1717689599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30004
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.2.100.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1714060800
  }
  showEndTime {
    seconds: 1717689599
  }
  isShow: true
  categoryId: 1
  tagDailyLimit: 500
}
rows {
  raffleId: 30005
  name: "赛季祈愿"
  startTime {
    seconds: 1717689600
  }
  endTime {
    seconds: 1721318399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30005
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.6.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1717689600
  }
  showEndTime {
    seconds: 1721318399
  }
  isShow: true
  categoryId: 1
  tagDailyLimit: 500
}
rows {
  raffleId: 30007
  name: "赛季祈愿"
  startTime {
    seconds: 1721318400
  }
  endTime {
    seconds: 1724947199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30007
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.12.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1721318400
  }
  showEndTime {
    seconds: 1724947199
  }
  isShow: true
  categoryId: 1
  tagDailyLimit: 500
}
rows {
  raffleId: 1000
  name: "印章祈愿"
  startTime {
    seconds: 1702569600
  }
  endTime {
    seconds: 1706803199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 1000
  }
  dailyLimit: 600
  maxLimit: 600
  textRuleId: 8
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "0.6.1196.1"
  bubbleImage: "T_DrawReward_Img_SealTips02.png"
  bubbleStartTime {
    seconds: 1697731200
  }
  bubbleEndTime {
    seconds: 1699372800
  }
  showStartTime {
    seconds: 1702569600
  }
  showEndTime {
    seconds: 1706803199
  }
  isShow: true
  categoryId: 2
  tagDailyLimit: 600
}
rows {
  raffleId: 5
  name: "印章祈愿"
  startTime {
    seconds: 1648828800
  }
  endTime {
    seconds: 1648828800
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 13
  }
  dailyLimit: 10
  maxLimit: 10
  textRuleId: 8
  lowestVersion: "0.6.1196.1"
  activityImage: "Acivity0621.png"
  showStartTime {
    seconds: 1648828800
  }
  showEndTime {
    seconds: 1648828800
  }
  isShow: true
  categoryId: 2
  tagDailyLimit: 10
}
rows {
  raffleId: 10001
  name: "印章祈愿"
  startTime {
    seconds: 1706803200
  }
  endTime {
    seconds: 1711641599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10001
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.2.67.1"
  showStartTime {
    seconds: 1706803200
  }
  showEndTime {
    seconds: 1711641599
  }
  isShow: true
  categoryId: 2
  tagDailyLimit: 1000
}
rows {
  raffleId: 10002
  name: "印章祈愿"
  startTime {
    seconds: 1711641600
  }
  endTime {
    seconds: 1715270399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10002
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.2.90.1"
  showStartTime {
    seconds: 1711641600
  }
  showEndTime {
    seconds: 1715270399
  }
  isShow: true
  categoryId: 2
  tagDailyLimit: 1000
}
rows {
  raffleId: 10003
  name: "印章祈愿"
  startTime {
    seconds: 1715270400
  }
  endTime {
    seconds: 1718899199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10003
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.2.100.1"
  showStartTime {
    seconds: 1715270400
  }
  showEndTime {
    seconds: 1718899199
  }
  isShow: true
  categoryId: 2
  tagDailyLimit: 1000
}
rows {
  raffleId: 10004
  name: "印章祈愿"
  startTime {
    seconds: 1718899200
  }
  endTime {
    seconds: 1722527999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10004
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.7.1"
  showStartTime {
    seconds: 1718899200
  }
  showEndTime {
    seconds: 1722527999
  }
  isShow: true
  categoryId: 2
  tagDailyLimit: 1000
}
rows {
  raffleId: 10005
  name: "印章祈愿"
  startTime {
    seconds: 1722528000
  }
  endTime {
    seconds: 1726761599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10005
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.12.69"
  showStartTime {
    seconds: 1722528000
  }
  showEndTime {
    seconds: 1726761599
  }
  isShow: true
  categoryId: 2
  tagDailyLimit: 1000
}
rows {
  raffleId: 8001
  name: "星光剧场"
  startTime {
    seconds: 1709222400
  }
  endTime {
    seconds: 1711641599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8001
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 122
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.2.80.1"
  showStartTime {
    seconds: 1709222400
  }
  showEndTime {
    seconds: 1711641599
  }
  isShow: true
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8002
  name: "星光剧场"
  startTime {
    seconds: 1711728000
  }
  endTime {
    seconds: 1715356799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8002
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  showStartTime {
    seconds: 1711728000
  }
  showEndTime {
    seconds: 1715356799
  }
  isShow: true
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8003
  name: "星光剧场"
  startTime {
    seconds: 1715356800
  }
  endTime {
    seconds: 1725033599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8003
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.2.100.1"
  showStartTime {
    seconds: 1715356800
  }
  showEndTime {
    seconds: 1725033599
  }
  isShow: true
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8007
  name: "星光剧场"
  startTime {
    seconds: 1729526400
  }
  endTime {
    seconds: 1729871999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8007
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.26.34"
  showStartTime {
    seconds: 1729526400
  }
  showEndTime {
    seconds: 1729871999
  }
  isShow: true
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8008
  name: "糖果女巫"
  startTime {
    seconds: 1729872000
  }
  endTime {
    seconds: 1735660799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8008
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 131
  lowestVersion: "1.3.26.34"
  viewIndex: 4
  viewIndex: 2
  viewIndex: 5
  viewIndex: 1
  viewIndex: 6
  viewIndex: 3
  viewIndex: 7
  viewIndex: 9
  viewIndex: 8
  showStartTime {
    seconds: 1729872000
  }
  showEndTime {
    seconds: 1735660799
  }
  isShow: true
  drawOverState: 4
  animType: 4
  relateUmgSetting: "7;UI_Lottery_RewardNotice|DrawReward_WitchText;;404060"
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8009
  name: "星光剧场"
  startTime {
    seconds: 1740672000
  }
  endTime {
    seconds: 1742486399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8009
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.68.119"
  viewIndex: 5
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  showStartTime {
    seconds: 1740672000
  }
  showEndTime {
    seconds: 1742486399
  }
  isShow: true
  drawOverState: 4
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8010
  name: "星光剧场"
  startTime {
    seconds: 1742572800
  }
  endTime {
    seconds: 1747929599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8010
  }
  dailyLimit: 30
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.78.33"
  viewIndex: 5
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  showStartTime {
    seconds: 1742572800
  }
  showEndTime {
    seconds: 1747929599
  }
  isShow: true
  drawOverState: 4
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8011
  name: "星光剧场"
  startTime {
    seconds: 1745942400
  }
  endTime {
    seconds: 1750607999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8011
  }
  dailyLimit: 30
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.78.33"
  viewIndex: 5
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  showStartTime {
    seconds: 1745942400
  }
  showEndTime {
    seconds: 1750607999
  }
  isShow: true
  drawOverState: 4
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8012
  name: "星光剧场"
  startTime {
    seconds: 1746720000
  }
  endTime {
    seconds: 1750607999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8012
  }
  dailyLimit: 30
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.78.33"
  viewIndex: 5
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  showStartTime {
    seconds: 1746720000
  }
  showEndTime {
    seconds: 1750607999
  }
  isShow: true
  drawOverState: 4
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8013
  name: "星光剧场"
  startTime {
    seconds: 1749744000
  }
  endTime {
    seconds: 1753199999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8013
  }
  dailyLimit: 30
  maxLimit: 9999999
  textRuleId: 131
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.78.33"
  viewIndex: 5
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 6
  showStartTime {
    seconds: 1749744000
  }
  showEndTime {
    seconds: 1753199999
  }
  isShow: true
  drawOverState: 4
  categoryId: 3
  tagDailyLimit: 300
}
rows {
  raffleId: 8000
  name: "活跃升级"
  startTime {
    seconds: 1680364800
  }
  endTime {
    seconds: 1680537599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8000
  }
  dailyLimit: 100
  maxLimit: 100
  textRuleId: 8
  text: "每<UILotteryActSubYellow>10</>次祈愿必得外观或非凡奖励"
  lowestVersion: "0.6.1196.1"
  activityImage: "Acivity0901.png"
  showStartTime {
    seconds: 1680364800
  }
  showEndTime {
    seconds: 1680537599
  }
  isShow: true
  tagDailyLimit: 100
}
rows {
  raffleId: 8004
  name: "蓝色卡池一期"
  startTime {
    seconds: 1709222400
  }
  endTime {
    seconds: 1715270400
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8004
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 122
  text: "首次祈愿10次享有5折优惠"
  showStartTime {
    seconds: 1709222400
  }
  showEndTime {
    seconds: 1715270400
  }
  isShow: true
  tagDailyLimit: 300
}
rows {
  raffleId: 30006
  name: "战神颂歌"
  startTime {
    seconds: 1718899200
  }
  endTime {
    seconds: 1734623999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30006
  }
  dailyLimit: 1000
  maxLimit: 9999999
  textRuleId: 201
  text: "每{1}次内必得<KnightTips>臻藏奖励({0}/{1})</>"
  lowestVersion: "********"
  raffleTagIcon: "T_Knight_Icon_SuitHead01;T_Knight_Icon_SuitHead02;T_Knight_Icon_SuitHead03"
  milestone {
    atDraws: 10
    atDraws: 20
    atDraws: 50
    atDraws: 100
    atDraws: 160
    giftIds: 310224
    giftIds: 310225
    giftIds: 310226
    giftIds: 310227
    giftIds: 310228
  }
  jumpIds: 25
  commodityIds: 120050
  commodityIds: 120051
  commodityIds: 120053
  commodityIds: 120054
  showSubPoolProgress: false
  showStartTime {
    seconds: 1718899200
  }
  showEndTime {
    seconds: 1734623999
  }
  isShow: true
  animType: 4
  relateUmgSetting: "2;UI_Lottery_LuckyPanel#3;UI_Lottery_GloryRoadPanel"
  tagDailyLimit: 1000
}
rows {
  raffleId: 8005
  name: "幸运星"
  startTime {
    seconds: 1725897600
  }
  endTime {
    seconds: 1727020799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 8005
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 207
  lowestVersion: "*********"
  showStartTime {
    seconds: 1725897600
  }
  showEndTime {
    seconds: 1727020799
  }
  isShow: true
  tagDailyLimit: 300
}
rows {
  raffleId: 6000
  name: "峡谷幻梦"
  startTime {
    seconds: 1724083200
  }
  endTime {
    seconds: 1726156799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 6000
  }
  dailyLimit: 300
  maxLimit: 9999999
  textRuleId: 212
  text: "<HonorofKings2Tips>每十次</>祈愿必得非凡奖励"
  lowestVersion: "**********"
  raffleTagIcon: "T_HonorofKings2_Img_Tab1;T_HonorofKings2_Img_Tab2"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 80
    giftIds: 310250
    giftIds: 310251
    giftIds: 310252
  }
  showSubPoolProgress: false
  showStartTime {
    seconds: 1724083200
  }
  showEndTime {
    seconds: 1726156799
  }
  isShow: true
  animType: 4
  relateUmgSetting: "6;UI_Lottery_PopReward_ItemUpTips"
  rewardsForInGroupEnhance {
    array: 6000001
    array: 6000002
  }
  rewardsForInGroupEnhance {
    array: 6000003
    array: 6000004
  }
  limitForInGroupEnhance: 1
  limitForInGroupEnhance: 1
  chooseType: RCT_BeforeDrawnEnhance
  tagDailyLimit: 300
}
rows {
  raffleId: 9696
  name: "暑期特惠"
  startTime {
    seconds: 1749484800
  }
  endTime {
    seconds: 1751039999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 9696
  }
  dailyLimit: 1
  maxLimit: 9999999
  lowestVersion: "**********"
  showSubPoolProgress: false
  showStartTime {
    seconds: 1749484800
  }
  showEndTime {
    seconds: 1751299199
  }
  isShow: true
  tagDailyLimit: 1
}
