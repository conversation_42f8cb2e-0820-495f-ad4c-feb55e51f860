com.tencent.wea.xlsRes.table_BackpackItemTypeDef
excel/xls/D_道具表_道具类型参数.xlsx sheet:Sheet1
rows {
  type: "ItemType_NamePlate"
  showtext: "昵称框"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Portrait"
  showtext: "头像"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Frame"
  showtext: "头像框"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_ChatBubble"
  showtext: "聊天气泡"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_ChatColorFont"
  showtext: "字体颜色"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Title"
  showtext: "称号"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Background"
  showtext: "界面主题"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Currency"
  showtext: "货币"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_QualifyingAdditionalCard"
  showtext: "排位升星券"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_InteractiveProp"
  showtext: "互动道具"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEMax
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_Common"
  showtext: "通用道具"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_QualifyingProtectedCard"
  showtext: "排位保护券"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_AutoUse"
  showtext: "自动使用道具"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_QQCash"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_WXCash"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_GongyiXHH"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_GongyiMedal"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_FarmChest"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_Arena_Frame"
  showtext: "头像框"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Arena_VoiceStyle"
  showtext: "高光播报"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Arena_ReportAnim"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Arena_HeroTry"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_Arena_HeroSkin"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_NR3E_ReportAnim"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_NR3E_AttackAnim"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_NR3E_Interactive"
  showtext: "互动道具"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_NR3E_Meeting"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_NR3E_Identity"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_NR3E_MVPAnim"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_NR3E_SpecialSpeak"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_NR3E_DisguiseEffect"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_NR3E_Weapon"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_NR3E_Treasure"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_TYC"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_UgcBadge"
  showtext: "徽章"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Kart"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
  showQualityList: 1
  showQualityList: 2
  showQualityList: 3
  showQualityList: 4
  showQualityList: 5
  shareBtnEntranceList: 2
  shareTypeId: SSST_Item_Default
}
rows {
  type: "ItemType_Emoji"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Action1P"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Action2P"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Vehicle"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Package"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_FarmActive"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Suit"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_UpperGarment"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_LowerGarment"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Gloves"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_FaceOrnament"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_BackOrnament"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_HeadWear"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_HandOrnament"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
  showQualityList: 1
  showQualityList: 2
  shareBtnEntranceList: 1
  shareBtnEntranceList: 2
  shareTypeId: SSST_GetHighLevelItem
}
rows {
  type: "ItemType_Face"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Xiaowo_Ornament"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Shuttle"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
  showQualityList: 1
  showQualityList: 2
  shareBtnEntranceList: 1
  shareBtnEntranceList: 2
  shareTypeId: SSST_Shuttle
}
rows {
  type: "ItemType_FriendInviteBox"
  showtext: "邀请框"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_FriendOnlineReminder"
  showtext: "亲密好友提示框"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_FootPrint"
  showtext: "脚印"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_PersonalityState"
  ownedTagDisplayConditionType: OTDC_ShowWithCountGTEOne
  firstGetNeedShowNew: true
}
rows {
  type: "ItemType_Package_Share"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
rows {
  type: "ItemType_AppIcon"
  ownedTagDisplayConditionType: OTDC_Hide
  firstGetNeedShowNew: false
}
