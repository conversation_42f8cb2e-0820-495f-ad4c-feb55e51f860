com.tencent.wea.xlsRes.table_RaffleMajorGDrawData
excel/xls/C_抽奖奖池_miles.xlsx sheet:大保底抽数掉率
rows {
  id: 40000001
  poolId: 40000001
  draw: 240
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000004
  poolId: 400000040
  draw: 10
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000005
  poolId: 400000040
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000006
  poolId: 400000040
  draw: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000007
  poolId: 400000040
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000008
  poolId: 400000040
  draw: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000009
  poolId: 400000040
  draw: 110
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000010
  poolId: 400000040
  draw: 140
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000011
  poolId: 400000040
  draw: 170
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000012
  poolId: 400000040
  draw: 210
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000013
  poolId: 400000040
  draw: 250
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000014
  poolId: 400000041
  draw: 50
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000015
  poolId: 400000041
  draw: 80
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000016
  poolId: 400000041
  draw: 120
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000017
  poolId: 40000005
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000018
  poolId: 40000005
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000019
  poolId: 40000006
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000020
  poolId: 40000006
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000021
  poolId: 40000007
  draw: 4
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000022
  poolId: 40000007
  draw: 9
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000023
  poolId: 40000008
  draw: 240
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000024
  poolId: 40000009
  draw: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000025
  poolId: 40000009
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000026
  poolId: 40000009
  draw: 9
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000027
  poolId: 400000140
  draw: 10
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000028
  poolId: 400000140
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000029
  poolId: 400000140
  draw: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000030
  poolId: 400000140
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000031
  poolId: 400000140
  draw: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000032
  poolId: 400000140
  draw: 110
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000033
  poolId: 400000140
  draw: 140
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000034
  poolId: 400000140
  draw: 170
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000035
  poolId: 400000140
  draw: 210
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000036
  poolId: 400000140
  draw: 250
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000037
  poolId: 400000141
  draw: 50
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000038
  poolId: 400000141
  draw: 80
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000039
  poolId: 400000141
  draw: 120
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000041
  poolId: 40000016
  draw: 3
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000042
  poolId: 40000016
  draw: 6
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000043
  poolId: 40000016
  draw: 9
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000044
  poolId: 400000150
  draw: 10
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000045
  poolId: 400000150
  draw: 20
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000046
  poolId: 400000150
  draw: 30
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000047
  poolId: 400000150
  draw: 50
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000048
  poolId: 400000150
  draw: 80
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000049
  poolId: 400000150
  draw: 110
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000050
  poolId: 400000150
  draw: 140
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000051
  poolId: 400000150
  draw: 170
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000052
  poolId: 400000150
  draw: 210
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000053
  poolId: 400000150
  draw: 250
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
}
rows {
  id: 40000054
  poolId: 400000151
  draw: 50
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 40000055
  poolId: 400000151
  draw: 80
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
rows {
  id: 40000056
  poolId: 400000151
  draw: 120
  groupWeight: 0
  groupWeight: 100
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  groupWeight: 0
  noSkip: true
}
