com.tencent.wea.xlsRes.table_RaffleRewardCfgData
excel/xls/C_抽奖奖池_karl.xlsx sheet:奖励
rows {
  rewardId: 50000001
  poolId: 50000001
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000002
  poolId: 50000001
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000003
  poolId: 50000001
  name: "LULU猪表情1"
  itemId: 711052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000004
  poolId: 50000001
  name: "LULU猪灯牌"
  itemId: 630229
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000005
  poolId: 50000001
  name: " LULU猪"
  itemId: 402960
  itemNum: 1
  groupId: 4
  weight: 15
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000006
  poolId: 50000001
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 35
  limit: 1
}
rows {
  rewardId: 50000007
  poolId: 50000001
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
  limit: 1
}
rows {
  rewardId: 50000008
  poolId: 50000001
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000009
  poolId: 50000001
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000010
  poolId: 50000002
  name: "齐天大圣 孙悟空"
  itemId: 402220
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000011
  poolId: 50000002
  name: "【紫配】金箍棒"
  itemId: 640044
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000012
  poolId: 50000002
  name: "【紫动】猴哥pose"
  itemId: 720721
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 50000013
  poolId: 50000002
  name: "动态头像框-齐天大圣"
  itemId: 840172
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000014
  poolId: 50000002
  name: "静态称号-齐天大圣"
  itemId: 850488
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000015
  poolId: 50000002
  name: "星愿币"
  itemId: 2
  itemNum: 4
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000016
  poolId: 50000002
  name: "头像-齐天大圣"
  itemId: 860103
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000017
  poolId: 50000002
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000018
  poolId: 50000002
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000019
  poolId: 50000003
  name: "金蝉子 唐三藏"
  itemId: 402970
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000020
  poolId: 50000003
  name: "【紫配】紫金钵"
  itemId: 620472
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000021
  poolId: 50000003
  name: "【紫动】念经"
  itemId: 720754
  itemNum: 1
  groupId: 2
  weight: 3
  limit: 1
}
rows {
  rewardId: 50000022
  poolId: 50000003
  name: "动态头像框-金蝉子"
  itemId: 840173
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000023
  poolId: 50000003
  name: "静态称号-十世金蝉"
  itemId: 850490
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000024
  poolId: 50000003
  name: "星愿币"
  itemId: 2
  itemNum: 4
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000025
  poolId: 50000003
  name: "头像-金蝉子"
  itemId: 860104
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000026
  poolId: 50000003
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000027
  poolId: 50000003
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000028
  poolId: 500010010
  name: "三彩逸士 青云"
  itemId: 403660
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  extraItemIds: 405097
  extraItemNums: 1
}
rows {
  rewardId: 50000029
  poolId: 500010010
  name: "染青烟"
  itemId: 640053
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000030
  poolId: 500010010
  name: "洇重霄"
  itemId: 640060
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000031
  poolId: 500010010
  name: "绘长歌"
  itemId: 620485
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000032
  poolId: 500010010
  name: "见龙吟"
  itemId: 630351
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000033
  poolId: 500010010
  name: "露水精灵 嘟嘟"
  itemId: 403820
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000034
  poolId: 500010010
  name: "熊猫女孩"
  itemId: 403810
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000035
  poolId: 500010010
  name: "雀翎羽"
  itemId: 630190
  itemNum: 1
  groupId: 4
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000036
  poolId: 500010010
  name: "开屏眼罩"
  itemId: 610152
  itemNum: 1
  groupId: 4
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000037
  poolId: 500010010
  name: "花影之刃"
  itemId: 620500
  itemNum: 1
  groupId: 4
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000038
  poolId: 500010010
  name: "凤果果"
  itemId: 403530
  itemNum: 1
  groupId: 4
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000039
  poolId: 500010010
  name: "幸运币*15"
  itemId: 3
  itemNum: 15
  groupId: 4
  weight: 3
  limit: 1
}
rows {
  rewardId: 50000043
  poolId: 500010011
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000044
  poolId: 500010011
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000045
  poolId: 500010011
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000046
  poolId: 500010011
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000047
  poolId: 500010011
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000048
  poolId: 500010011
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000049
  poolId: 500010011
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000050
  poolId: 500010011
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000051
  poolId: 500010011
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000053
  poolId: 500010012
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000054
  poolId: 500010012
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000055
  poolId: 500010012
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000056
  poolId: 500010012
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000057
  poolId: 500010012
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000058
  poolId: 500010012
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000059
  poolId: 500010012
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000060
  poolId: 500010012
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000061
  poolId: 500010012
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000063
  poolId: 500010013
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000064
  poolId: 500010013
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000065
  poolId: 500010013
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000066
  poolId: 500010013
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000067
  poolId: 500010013
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000068
  poolId: 500010013
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000069
  poolId: 500010013
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000070
  poolId: 500010013
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000071
  poolId: 500010013
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000073
  poolId: 500010014
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000074
  poolId: 500010014
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000075
  poolId: 500010014
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000076
  poolId: 500010014
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000077
  poolId: 500010014
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000078
  poolId: 500010014
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000079
  poolId: 500010014
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000080
  poolId: 500010014
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000081
  poolId: 500010014
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000083
  poolId: 500010015
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000084
  poolId: 500010015
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000085
  poolId: 500010015
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000086
  poolId: 500010015
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000087
  poolId: 500010015
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000088
  poolId: 500010015
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000089
  poolId: 500010015
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000090
  poolId: 500010015
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000091
  poolId: 500010015
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000093
  poolId: 500010016
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000094
  poolId: 500010016
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000095
  poolId: 500010016
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000096
  poolId: 500010016
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000097
  poolId: 500010016
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000098
  poolId: 500010016
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000099
  poolId: 500010016
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000100
  poolId: 500010016
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000101
  poolId: 500010016
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000103
  poolId: 500010017
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000104
  poolId: 500010017
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000105
  poolId: 500010017
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000106
  poolId: 500010017
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000107
  poolId: 500010017
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000108
  poolId: 500010017
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000109
  poolId: 500010017
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000110
  poolId: 500010017
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000111
  poolId: 500010017
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000113
  poolId: 500010018
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000114
  poolId: 500010018
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000115
  poolId: 500010018
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000116
  poolId: 500010018
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000117
  poolId: 500010018
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000118
  poolId: 500010018
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000119
  poolId: 500010018
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000120
  poolId: 500010018
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000121
  poolId: 500010018
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000123
  poolId: 500010019
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000124
  poolId: 500010019
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000125
  poolId: 500010019
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000126
  poolId: 500010019
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000127
  poolId: 500010019
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000128
  poolId: 500010019
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000129
  poolId: 500010019
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000130
  poolId: 500010019
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000131
  poolId: 500010019
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000133
  poolId: 500010020
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000134
  poolId: 500010020
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000135
  poolId: 500010020
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000136
  poolId: 500010020
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000137
  poolId: 500010020
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000138
  poolId: 500010020
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000139
  poolId: 500010020
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000140
  poolId: 500010020
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000141
  poolId: 500010020
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000143
  poolId: 500010021
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000144
  poolId: 500010021
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000145
  poolId: 500010021
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000146
  poolId: 500010021
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000147
  poolId: 500010021
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000148
  poolId: 500010021
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000149
  poolId: 500010021
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000150
  poolId: 500010021
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000151
  poolId: 500010021
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000153
  poolId: 500010022
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000154
  poolId: 500010022
  name: "星愿币*3"
  itemId: 2
  itemNum: 3
  groupId: 1
  weight: 18
  limit: 1
}
rows {
  rewardId: 50000155
  poolId: 500010022
  name: "烟花礼盒*10"
  itemId: 725201
  itemNum: 10
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000156
  poolId: 500010022
  name: "时装染色膏*3"
  itemId: 200006
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000157
  poolId: 500010022
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000158
  poolId: 500010022
  name: "云朵币*50"
  itemId: 6
  itemNum: 50
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000159
  poolId: 500010022
  name: "饰品调色盘*3"
  itemId: 200008
  itemNum: 3
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000160
  poolId: 500010022
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000161
  poolId: 500010022
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 15
  limit: 1
}
rows {
  rewardId: 50000162
  poolId: 53000001
  name: "时装自选礼盒*1"
  itemId: 330083
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 2
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 50000163
  poolId: 53000001
  name: "【橙配】酒杯-手持"
  itemId: 640068
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000164
  poolId: 53000001
  name: "双羽焰舞"
  itemId: 620570
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000165
  poolId: 53000001
  name: "心梦光环"
  itemId: 630392
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000166
  poolId: 53000001
  name: "赤焰之舞"
  itemId: 610264
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000167
  poolId: 53000001
  name: "小南瓜 卡芭莎"
  itemId: 404040
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000168
  poolId: 53000001
  name: "小幽灵 凡托姆"
  itemId: 404130
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000169
  poolId: 53000001
  name: "皇家权杖"
  itemId: 640071
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 2
  afterRefreshLimit: 2
}
rows {
  rewardId: 50000170
  poolId: 53000001
  name: "星愿号角"
  itemId: 620550
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000171
  poolId: 53000001
  name: "大眼萌萌"
  itemId: 610267
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000172
  poolId: 53000001
  name: "发型不能乱"
  itemId: 720781
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000173
  poolId: 53000001
  name: "莓果果"
  itemId: 404150
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000174
  poolId: 53000001
  name: "心梦券*20"
  itemId: 211
  itemNum: 20
  groupId: 2
  weight: 12
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 50000175
  poolId: 53000001
  name: "心梦券*5"
  itemId: 211
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 50000176
  poolId: 53000001
  name: "心梦券*3"
  itemId: 211
  itemNum: 3
  groupId: 3
  weight: 10
}
rows {
  rewardId: 50000177
  poolId: 53000001
  name: "心梦券*2"
  itemId: 211
  itemNum: 2
  groupId: 3
  weight: 20
}
rows {
  rewardId: 50000178
  poolId: 53000001
  name: "心梦券*1"
  itemId: 211
  itemNum: 1
  groupId: 3
  weight: 32
}
rows {
  rewardId: 50000179
  poolId: 53000001
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 12
}
rows {
  rewardId: 50000180
  poolId: 53000001
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 12
}
rows {
  rewardId: 50000181
  poolId: 53000001
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 50000182
  poolId: 53000001
  name: "动态头像框"
  itemId: 840194
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000183
  poolId: 53000001
  name: "动态昵称框"
  itemId: 820132
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000184
  poolId: 53000001
  name: "静态称号"
  itemId: 850508
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000185
  poolId: 500010023
  name: "巴啦啦-小蓝"
  itemId: 404020
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000186
  poolId: 500010023
  name: "魔仙变身"
  itemId: 720886
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 50000187
  poolId: 500010023
  name: "转运魔法"
  itemId: 711142
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 50000188
  poolId: 500010023
  name: "小蓝魔法棒"
  itemId: 640077
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000189
  poolId: 500010024
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000190
  poolId: 500010024
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000191
  poolId: 500010024
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000192
  poolId: 500010024
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000193
  poolId: 500010024
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000194
  poolId: 500010024
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000195
  poolId: 500010024
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000196
  poolId: 500010025
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000197
  poolId: 500010025
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000198
  poolId: 500010025
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000199
  poolId: 500010025
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000200
  poolId: 500010025
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000201
  poolId: 500010025
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000202
  poolId: 500010025
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000203
  poolId: 500010026
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000204
  poolId: 500010026
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000205
  poolId: 500010026
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000206
  poolId: 500010026
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000207
  poolId: 500010026
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000208
  poolId: 500010026
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000209
  poolId: 500010026
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000210
  poolId: 500010027
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000211
  poolId: 500010027
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000212
  poolId: 500010027
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000213
  poolId: 500010027
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000214
  poolId: 500010027
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000215
  poolId: 500010027
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000216
  poolId: 500010027
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000217
  poolId: 500010028
  name: "巴啦啦-游乐"
  itemId: 404010
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000218
  poolId: 500010028
  name: "星意回旋"
  itemId: 720887
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 50000219
  poolId: 500010028
  name: "闪亮登场"
  itemId: 711143
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 50000220
  poolId: 500010028
  name: "极光剑"
  itemId: 620624
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000221
  poolId: 500010029
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000222
  poolId: 500010029
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000223
  poolId: 500010029
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000224
  poolId: 500010029
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000225
  poolId: 500010029
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000226
  poolId: 500010029
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000227
  poolId: 500010029
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000228
  poolId: 500010030
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000229
  poolId: 500010030
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000230
  poolId: 500010030
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000231
  poolId: 500010030
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000232
  poolId: 500010030
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000233
  poolId: 500010030
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000234
  poolId: 500010030
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000235
  poolId: 500010031
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000236
  poolId: 500010031
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000237
  poolId: 500010031
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000238
  poolId: 500010031
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000239
  poolId: 500010031
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000240
  poolId: 500010031
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000241
  poolId: 500010031
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000242
  poolId: 500010032
  name: "幸运钥匙"
  groupId: 1
  weight: 2
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000243
  poolId: 500010032
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000244
  poolId: 500010032
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000245
  poolId: 500010032
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000246
  poolId: 500010032
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000247
  poolId: 500010032
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000248
  poolId: 500010032
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000249
  poolId: 5800001
  name: "永恒天使 艾薇"
  itemId: 404560
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  lvCost: 50
}
rows {
  rewardId: 50000250
  poolId: 5800001
  name: "昼天使 卢兹"
  itemId: 404550
  itemNum: 1
  groupId: 1
  weight: 370
  isGrand: true
  lvCost: 50
}
rows {
  rewardId: 50000251
  poolId: 5800001
  name: "夜天使 莎莉娅"
  itemId: 404540
  itemNum: 1
  groupId: 1
  weight: 370
  isGrand: true
  lvCost: 50
}
rows {
  rewardId: 50000252
  poolId: 5800001
  name: "璀璨之星"
  itemId: 213
  itemNum: 1
  groupId: 1
  weight: 9260
  lvCost: 50
}
rows {
  rewardId: 50000253
  poolId: 5800001
  name: "深渊之眼 尤利西斯"
  itemId: 404170
  itemNum: 1
  groupId: 2
  weight: 5
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000254
  poolId: 5800001
  name: "甜心烘焙师 芙芙"
  itemId: 404420
  itemNum: 1
  groupId: 2
  weight: 5
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000255
  poolId: 5800001
  name: "星月共舞时"
  itemId: 721037
  itemNum: 1
  groupId: 2
  weight: 10
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000256
  poolId: 5800001
  name: "神圣庇佑"
  itemId: 720908
  itemNum: 1
  groupId: 2
  weight: 10
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000257
  poolId: 5800001
  name: "审判之刃"
  itemId: 720909
  itemNum: 1
  groupId: 2
  weight: 10
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000258
  poolId: 5800001
  name: "动态头像框-永昼"
  itemId: 840240
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000259
  poolId: 5800001
  name: "动态头像框-永夜"
  itemId: 840241
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000260
  poolId: 5800001
  name: "静态头像-昼天使"
  itemId: 860171
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000261
  poolId: 5800001
  name: "静态头像-夜天使"
  itemId: 860172
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000262
  poolId: 5800001
  name: "夜空暗翼"
  itemId: 620691
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000263
  poolId: 5800001
  name: "乐小汐"
  itemId: 404830
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 11
    limit: 1
  }
}
rows {
  rewardId: 50000264
  poolId: 5800001
  name: "喵趣横生"
  itemId: 510283
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 50000265
  poolId: 5800001
  name: "甜梦畅游家"
  itemId: 510232
  itemNum: 1
  groupId: 3
  weight: 4
}
rows {
  rewardId: 50000266
  poolId: 5800001
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 50000267
  poolId: 5800001
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 50000268
  poolId: 5800001
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 3
  weight: 5
}
rows {
  rewardId: 50000269
  poolId: 5800001
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 8
}
rows {
  rewardId: 50000270
  poolId: 5800001
  name: "心心糖果*1"
  itemId: 200015
  itemNum: 1
  groupId: 3
  weight: 10
}
rows {
  rewardId: 50000271
  poolId: 5800001
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 20
}
rows {
  rewardId: 50000299
  poolId: 53000004
  name: "时装自选礼盒*1"
  itemId: 330041
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 2
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 50000300
  poolId: 53000004
  name: "永恒之翼"
  itemId: 620298
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000301
  poolId: 53000004
  name: "永恒之冠"
  itemId: 630169
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000302
  poolId: 53000004
  name: "梦幻铃铛"
  itemId: 630166
  itemNum: 1
  groupId: 2
  weight: 6
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000303
  poolId: 53000004
  name: "犬系少年 阿柴"
  itemId: 402160
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000304
  poolId: 53000004
  name: "猫系少女 喵喵"
  itemId: 402170
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000305
  poolId: 53000004
  name: "幸福花束"
  itemId: 620309
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000306
  poolId: 53000004
  name: "时尚魔头镜"
  itemId: 610139
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000307
  poolId: 53000004
  name: "星语晶恋"
  itemId: 630174
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 2
  afterRefreshLimit: 2
}
rows {
  rewardId: 50000308
  poolId: 53000004
  name: "挥手撒花"
  itemId: 720902
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 2
  afterRefreshLimit: 2
}
rows {
  rewardId: 50000309
  poolId: 53000004
  name: "绮莉莉"
  itemId: 402040
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000310
  poolId: 53000004
  name: "心梦券*20"
  itemId: 211
  itemNum: 20
  groupId: 2
  weight: 12
  limit: 3
  afterRefreshLimit: 3
}
rows {
  rewardId: 50000311
  poolId: 53000004
  name: "心梦券*5"
  itemId: 211
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 50000312
  poolId: 53000004
  name: "心梦券*3"
  itemId: 211
  itemNum: 3
  groupId: 3
  weight: 10
}
rows {
  rewardId: 50000313
  poolId: 53000004
  name: "心梦券*2"
  itemId: 211
  itemNum: 2
  groupId: 3
  weight: 20
}
rows {
  rewardId: 50000314
  poolId: 53000004
  name: "心梦券*1"
  itemId: 211
  itemNum: 1
  groupId: 3
  weight: 32
}
rows {
  rewardId: 50000315
  poolId: 53000004
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 9
}
rows {
  rewardId: 50000316
  poolId: 53000004
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 9
}
rows {
  rewardId: 50000317
  poolId: 53000004
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 3
  weight: 15
}
rows {
  rewardId: 50000318
  poolId: 53000004
  name: "动态头像框"
  itemId: 840106
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000319
  poolId: 53000004
  name: "动态昵称框"
  itemId: 820075
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000320
  poolId: 53000002
  name: "【橙装】九尾狐-高级"
  itemId: 410050
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000321
  poolId: 53000002
  name: "【橙装】九尾狐-基础"
  itemId: 410040
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000322
  poolId: 53000002
  name: "【橙配】手持-火焰扇子"
  itemId: 640132
  itemNum: 1
  groupId: 2
  weight: 2
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000323
  poolId: 53000002
  name: "【橙装】狐狸头饰"
  itemId: 630489
  itemNum: 1
  groupId: 2
  weight: 6
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000324
  poolId: 53000002
  name: "【橙配】红缨面饰"
  itemId: 610319
  itemNum: 1
  groupId: 2
  weight: 6
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000325
  poolId: 53000002
  name: "【橙配】狐狸背饰"
  itemId: 620734
  itemNum: 1
  groupId: 2
  weight: 6
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000326
  poolId: 53000002
  name: "【紫装】画中仙"
  itemId: 410150
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000327
  poolId: 53000002
  name: "【紫装】丹青圣手"
  itemId: 410160
  itemNum: 1
  groupId: 3
  weight: 1
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000328
  poolId: 53000002
  name: "【蓝装】掌柜"
  itemId: 410180
  itemNum: 1
  groupId: 2
  weight: 15
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000329
  poolId: 53000002
  name: "【手持物】团扇"
  itemId: 640129
  itemNum: 1
  groupId: 2
  weight: 15
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000330
  poolId: 53000002
  name: "【背饰】笛子"
  itemId: 620737
  itemNum: 1
  groupId: 2
  weight: 15
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000331
  poolId: 53000002
  name: "【头饰】花枝"
  itemId: 630492
  itemNum: 1
  groupId: 2
  weight: 15
  inGroupLimit {
    period: 14
    limit: 1
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000332
  poolId: 53000002
  name: "【动作】小猫舞"
  itemId: 720956
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 14
    limit: 2
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000333
  poolId: 53000002
  name: "【动作】扇子狐火"
  itemId: 720957
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 14
    limit: 2
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000334
  poolId: 53000002
  name: "【动作】元气舞蹈"
  itemId: 720958
  itemNum: 1
  groupId: 2
  weight: 20
  inGroupLimit {
    period: 14
    limit: 2
  }
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000335
  poolId: 53000002
  name: "祥龙送宝上装"
  itemId: 510323
  itemNum: 1
  groupId: 4
  weight: 4
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000336
  poolId: 53000002
  name: "祥龙送宝下装"
  itemId: 520218
  itemNum: 1
  groupId: 4
  weight: 4
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000337
  poolId: 53000002
  name: "祥龙送宝手套"
  itemId: 530194
  itemNum: 1
  groupId: 4
  weight: 4
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000338
  poolId: 53000002
  name: "暖冬物语上装"
  itemId: 510246
  itemNum: 1
  groupId: 4
  weight: 4
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000339
  poolId: 53000002
  name: "暖冬物语下装"
  itemId: 520172
  itemNum: 1
  groupId: 4
  weight: 4
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000340
  poolId: 53000002
  name: "暖冬物语手套"
  itemId: 530148
  itemNum: 1
  groupId: 4
  weight: 4
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000341
  poolId: 53000002
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 4
  weight: 15
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000342
  poolId: 53000002
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 15
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000343
  poolId: 53000002
  name: "心心蜜罐*1"
  itemId: 200017
  itemNum: 1
  groupId: 4
  weight: 5
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000344
  poolId: 53000002
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 8
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000345
  poolId: 53000002
  name: "心心糖果*1"
  itemId: 200015
  itemNum: 1
  groupId: 4
  weight: 10
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000346
  poolId: 53000002
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 20
  sortId: 1
  sortName: "祈愿奖励"
}
rows {
  rewardId: 50000347
  poolId: 53000003
  name: "桃花"
  itemId: 3479
  itemNum: 100
  groupId: 1
  weight: 1
  limit: 1
  sortId: 2
  sortName: "额外奖励"
}
rows {
  rewardId: 50000348
  poolId: 53000003
  name: "桃花"
  itemId: 3479
  itemNum: 30
  groupId: 2
  weight: 1
  sortId: 2
  sortName: "额外奖励"
}
rows {
  rewardId: 50000349
  poolId: 53000003
  name: "桃花"
  itemId: 3479
  itemNum: 10
  groupId: 3
  weight: 1
  sortId: 2
  sortName: "额外奖励"
}
rows {
  rewardId: 50000350
  poolId: 53000003
  name: "桃花"
  itemId: 3479
  itemNum: 5
  groupId: 4
  weight: 1
  sortId: 2
  sortName: "额外奖励"
}
rows {
  rewardId: 50000351
  poolId: 53000003
  name: "桃花"
  itemId: 3479
  itemNum: 3
  groupId: 5
  weight: 1
  sortId: 2
  sortName: "额外奖励"
}
rows {
  rewardId: 50000352
  poolId: 53000003
  name: "桃花"
  itemId: 3479
  itemNum: 1
  groupId: 6
  weight: 1
  sortId: 2
  sortName: "额外奖励"
}
rows {
  rewardId: 50000353
  poolId: 53000005
  name: "【橙装】幻彩画匠  绮"
  itemId: 410840
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
}
rows {
  rewardId: 50000354
  poolId: 53000005
  name: "【紫装】梦笔生花  绮"
  itemId: 411190
  itemNum: 1
  groupId: 1
  weight: 2
}
rows {
  rewardId: 50000355
  poolId: 53000005
  name: "【头饰】粉兔颜料"
  itemId: 630606
  itemNum: 1
  groupId: 1
  weight: 10
}
rows {
  rewardId: 50000356
  poolId: 53000005
  name: "【面饰】柠沫微霞"
  itemId: 610392
  itemNum: 1
  groupId: 1
  weight: 10
}
rows {
  rewardId: 50000357
  poolId: 53000005
  name: "【背饰】萌兔调色盘"
  itemId: 620900
  itemNum: 1
  groupId: 1
  weight: 10
}
rows {
  rewardId: 50000358
  poolId: 53000005
  name: "【橙手持】绮梦独角兽"
  itemId: 640163
  itemNum: 1
  groupId: 1
  weight: 10
}
rows {
  rewardId: 50000359
  poolId: 53000005
  name: "【紫面饰】音浪视界"
  itemId: 610359
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000360
  poolId: 53000005
  name: "【紫头饰】更衣香蕉鸭"
  itemId: 630556
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000361
  poolId: 53000005
  name: "【紫背饰】小熊拍立得"
  itemId: 620870
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000362
  poolId: 53000005
  name: "【蓝装】番茄朵朵"
  itemId: 410190
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000363
  poolId: 53000005
  name: "【紫单动】停不下来"
  itemId: 720979
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000364
  poolId: 53000005
  name: "【紫单动】捡到宝了"
  itemId: 720782
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000365
  poolId: 53000005
  name: "【紫单动】真香"
  itemId: 720793
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000366
  poolId: 53000005
  name: "“幻彩调律”动态称号"
  itemId: 850652
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000367
  poolId: 53000005
  name: "“幻彩调律”动态昵称框"
  itemId: 820188
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 9
    limit: 1
  }
}
rows {
  rewardId: 50000368
  poolId: 53000005
  name: "水粉刷"
  itemId: 3556
  itemNum: 20
  groupId: 3
  weight: 1
}
rows {
  rewardId: 50000369
  poolId: 53000005
  name: "水粉刷"
  itemId: 3556
  itemNum: 10
  groupId: 3
  weight: 5
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemNums: 10
  comboItemNums: 20
  comboItemNums: 60
  comboType: RCT_Always
  comboItemWeights: 50
  comboItemWeights: 3
  comboItemWeights: 1
}
rows {
  rewardId: 50000370
  poolId: 53000005
  name: "水粉刷"
  itemId: 3556
  itemNum: 5
  groupId: 3
  weight: 11
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemNums: 5
  comboItemNums: 10
  comboItemNums: 30
  comboType: RCT_Always
  comboItemWeights: 50
  comboItemWeights: 10
  comboItemWeights: 1
}
rows {
  rewardId: 50000371
  poolId: 53000005
  name: "水粉刷"
  itemId: 3556
  itemNum: 2
  groupId: 3
  weight: 20
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemNums: 3
  comboItemNums: 6
  comboItemNums: 15
  comboType: RCT_Always
  comboItemWeights: 50
  comboItemWeights: 10
  comboItemWeights: 3
}
rows {
  rewardId: 50000372
  poolId: 53000005
  name: "水粉刷"
  itemId: 3556
  itemNum: 1
  groupId: 3
  weight: 30
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemIds: 3556
  comboItemNums: 1
  comboItemNums: 2
  comboItemNums: 5
  comboType: RCT_Always
  comboItemWeights: 50
  comboItemWeights: 50
  comboItemWeights: 5
}
rows {
  rewardId: 50000373
  poolId: 53000005
  name: "【绿装】自然精灵上装"
  itemId: 510309
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 50000374
  poolId: 53000005
  name: "【绿装】自然精灵下装"
  itemId: 520208
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 50000375
  poolId: 53000005
  name: "【绿装】自然精灵手套"
  itemId: 530184
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 50000376
  poolId: 53000005
  name: "【绿装】松果收藏家上装"
  itemId: 510238
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 50000377
  poolId: 53000005
  name: "【绿装】松果收藏家下装"
  itemId: 520165
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 50000378
  poolId: 53000005
  name: "【绿装】松果收藏家手套"
  itemId: 530141
  itemNum: 1
  groupId: 4
  weight: 1
}
rows {
  rewardId: 50000379
  poolId: 53000005
  name: "时装染色膏"
  itemId: 200006
  itemNum: 1
  groupId: 4
  weight: 3
}
rows {
  rewardId: 50000380
  poolId: 53000005
  name: "饰品调色盘"
  itemId: 200008
  itemNum: 1
  groupId: 4
  weight: 3
}
rows {
  rewardId: 50000381
  poolId: 53000005
  name: "心心蜜罐"
  itemId: 200017
  itemNum: 1
  groupId: 4
  weight: 3
}
rows {
  rewardId: 50000382
  poolId: 53000005
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 3
}
rows {
  rewardId: 50000383
  poolId: 53000005
  name: "心心糖果"
  itemId: 200015
  itemNum: 1
  groupId: 4
  weight: 3
}
rows {
  rewardId: 50000384
  poolId: 53000005
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 3
}
rows {
  rewardId: 50000385
  poolId: 50000004
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000386
  poolId: 50000004
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000387
  poolId: 50000004
  name: "LULU猪表情1"
  itemId: 711052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000388
  poolId: 50000004
  name: "LULU猪灯牌"
  itemId: 630229
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000389
  poolId: 50000004
  name: " LULU猪"
  itemId: 402960
  itemNum: 1
  groupId: 4
  weight: 15
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000390
  poolId: 50000004
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 35
  limit: 1
}
rows {
  rewardId: 50000391
  poolId: 50000004
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
  limit: 1
}
rows {
  rewardId: 50000392
  poolId: 50000004
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000393
  poolId: 50000004
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000394
  poolId: 53000006
  name: "自由之蝶 凡妮莎"
  itemId: 410660
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 50000395
  poolId: 53000006
  name: "晶珀之梦"
  itemId: 620838
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000396
  poolId: 53000006
  name: "晶蝶幻面"
  itemId: 610353
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000397
  poolId: 53000006
  name: "蝶梦尘铃"
  itemId: 630538
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000398
  poolId: 53000006
  name: "星闪流萤 埃莉诺"
  itemId: 410640
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000399
  poolId: 53000006
  name: "怪奇菌学家 奇奇奥"
  itemId: 410650
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000400
  poolId: 53000006
  name: "奇幻蘑菇手持"
  itemId: 640144
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000401
  poolId: 53000006
  name: "星愿苹果头饰"
  itemId: 630526
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000402
  poolId: 53000006
  name: "松树之境面饰"
  itemId: 610403
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 50000403
  poolId: 53000006
  name: "琪露露"
  itemId: 404690
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 2
}
rows {
  rewardId: 50000404
  poolId: 53000006
  name: "动态称号"
  itemId: 850659
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000405
  poolId: 53000006
  name: "动态昵称框"
  itemId: 820194
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000406
  poolId: 53000006
  name: "动态头像框"
  itemId: 840310
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000407
  poolId: 53000006
  name: "凡妮莎头像"
  itemId: 860211
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 50000408
  poolId: 53000006
  name: "月华券*20"
  itemId: 3950
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 3
  afterRefreshLimit: 2
}
rows {
  rewardId: 50000409
  poolId: 53000006
  name: "月华券*5"
  itemId: 3950
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 50000410
  poolId: 53000006
  name: "月华券*3"
  itemId: 3950
  itemNum: 3
  groupId: 3
  weight: 5
}
rows {
  rewardId: 50000411
  poolId: 53000006
  name: "月华券*2"
  itemId: 3950
  itemNum: 2
  groupId: 3
  weight: 25
}
rows {
  rewardId: 50000412
  poolId: 53000006
  name: "月华券*1"
  itemId: 3950
  itemNum: 1
  groupId: 3
  weight: 30
}
rows {
  rewardId: 50000413
  poolId: 53000006
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 5
  weight: 4
}
rows {
  rewardId: 50000414
  poolId: 53000006
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 5
  weight: 5
}
rows {
  rewardId: 50000415
  poolId: 53000006
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 4
}
rows {
  rewardId: 50000416
  poolId: 53000006
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 5
  weight: 5
}
rows {
  rewardId: 50000417
  poolId: 53000006
  name: "棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 4
}
rows {
  rewardId: 50000418
  poolId: 53000006
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 5
  weight: 4
}
rows {
  rewardId: 50000419
  poolId: 53000006
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 5
  weight: 2
}
rows {
  rewardId: 50000420
  poolId: 50000005
  name: "臻藏环绕红莲生"
  itemId: 750002
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 50000421
  poolId: 50000005
  name: "非凡环绕青莲落"
  itemId: 750001
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 50000422
  poolId: 50000005
  name: "【蓝配】比心灯牌"
  itemId: 630463
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
}
rows {
  rewardId: 50000423
  poolId: 50000005
  name: "【紫配】背饰-美味寿司"
  itemId: 620625
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000424
  poolId: 50000005
  name: "单人动作-旋转烟花"
  itemId: 720961
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 50000425
  poolId: 50000005
  name: "双人-琴笛合奏"
  itemId: 721051
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000426
  poolId: 50000005
  name: "绿上装-像素小红狐"
  itemId: 510384
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000427
  poolId: 50000005
  name: "绿下装-像素小红狐"
  itemId: 520252
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 50000428
  poolId: 50000005
  name: "绿手套-像素小红狐"
  itemId: 530228
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
