com.tencent.wea.xlsRes.table_RaffleLuckyValueData
excel/xls/C_抽奖奖池_karl.xlsx sheet:幸运值配置
rows {
  id: 50000001
  poolId: 50000001
  lvThreshold: 3
  groupWeight: 1500
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 50
  groupWeight: 2000
}
rows {
  id: 50000002
  poolId: 50000001
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 50000003
  poolId: 50000001
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 50000004
  poolId: 50000001
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 50000005
  poolId: 50000001
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 50000006
  poolId: 50000002
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 300
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000007
  poolId: 50000002
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 500
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000008
  poolId: 50000002
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 50000009
  poolId: 50000002
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 50000010
  poolId: 50000002
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 50000011
  poolId: 50000003
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 300
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000012
  poolId: 50000003
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 500
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000013
  poolId: 50000003
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 50000014
  poolId: 50000003
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 50000015
  poolId: 50000003
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 50000016
  poolId: 500010010
  lvThreshold: 1
  groupWeight: 1
  groupWeight: 10
  groupWeight: 20
  groupWeight: 200
}
rows {
  id: 50000017
  poolId: 500010010
  lvThreshold: 4
  groupWeight: 1
  groupWeight: 5
  groupWeight: 20
  groupWeight: 180
}
rows {
  id: 50000018
  poolId: 500010010
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 15
  groupWeight: 20
  groupWeight: 150
}
rows {
  id: 50000019
  poolId: 500010010
  lvThreshold: 10
  groupWeight: 2
  groupWeight: 30
  groupWeight: 30
  groupWeight: 150
}
rows {
  id: 50000020
  poolId: 500010010
  lvThreshold: 12
  groupWeight: 10
  groupWeight: 30
  groupWeight: 30
  groupWeight: 125
}
rows {
  id: 50000024
  poolId: 500010011
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000025
  poolId: 500010012
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000026
  poolId: 500010013
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000027
  poolId: 500010014
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000028
  poolId: 500010015
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000029
  poolId: 500010016
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000030
  poolId: 500010017
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000031
  poolId: 500010018
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000032
  poolId: 500010019
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000033
  poolId: 500010020
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000034
  poolId: 500010021
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000035
  poolId: 500010022
  lvThreshold: 9
  groupWeight: 1
}
rows {
  id: 50000036
  poolId: 53000001
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 50000037
  poolId: 53000001
  lvThreshold: 280
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 50000038
  poolId: 500010023
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 50000039
  poolId: 500010023
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 50000040
  poolId: 500010023
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 50000041
  poolId: 500010024
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 50000042
  poolId: 500010025
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 50000043
  poolId: 500010026
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 50000044
  poolId: 500010027
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 50000045
  poolId: 500010028
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 50000046
  poolId: 500010028
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 50000047
  poolId: 500010028
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 50000048
  poolId: 500010029
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 50000049
  poolId: 500010030
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 50000050
  poolId: 500010031
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 50000051
  poolId: 500010032
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 50000052
  poolId: 5800001
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000053
  poolId: 5800001
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000054
  poolId: 5800001
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000055
  poolId: 5800001
  lvThreshold: 20
  groupWeight: 5
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000056
  poolId: 5800001
  lvThreshold: 30
  groupWeight: 10
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000057
  poolId: 5800001
  lvThreshold: 38
  groupWeight: 15
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000058
  poolId: 5800001
  lvThreshold: 39
  groupWeight: 3000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000059
  poolId: 5800001
  lvThreshold: 40
  groupWeight: 4000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000060
  poolId: 5800001
  lvThreshold: 41
  groupWeight: 5000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000061
  poolId: 5800001
  lvThreshold: 42
  groupWeight: 10000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000062
  poolId: 5800001
  lvThreshold: 43
  groupWeight: 15000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000063
  poolId: 5800001
  lvThreshold: 44
  groupWeight: 20000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000064
  poolId: 5800001
  lvThreshold: 45
  groupWeight: 25000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000065
  poolId: 5800001
  lvThreshold: 46
  groupWeight: 100000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000066
  poolId: 5800001
  lvThreshold: 47
  groupWeight: 150000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000067
  poolId: 5800001
  lvThreshold: 48
  groupWeight: 200000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000068
  poolId: 5800001
  lvThreshold: 49
  groupWeight: 250000
  groupWeight: 5200
  groupWeight: 100000
}
rows {
  id: 50000069
  poolId: 5800001
  lvThreshold: 50
  groupWeight: 1
  groupWeight: 0
  groupWeight: 0
}
rows {
  id: 50000070
  poolId: 53000002
  lvThreshold: 1
  groupWeight: 0
  groupWeight: 9000
  groupWeight: 500
  groupWeight: 90000
}
rows {
  id: 50000071
  poolId: 53000002
  lvThreshold: 10
  groupWeight: 5
  groupWeight: 9000
  groupWeight: 500
  groupWeight: 90000
}
rows {
  id: 50000073
  poolId: 53000003
  lvThreshold: 1
  groupWeight: 1
  groupWeight: 10
  groupWeight: 150
  groupWeight: 700
  groupWeight: 1000
  groupWeight: 0
}
rows {
  id: 50000074
  poolId: 53000004
  lvThreshold: 280
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 90000
  groupWeight: 500
}
rows {
  id: 50000075
  poolId: 53000003
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 10
  groupWeight: 150
  groupWeight: 700
  groupWeight: 1000
  groupWeight: 133
}
rows {
  id: 50000077
  poolId: 50000004
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000078
  poolId: 50000004
  lvThreshold: 5
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000079
  poolId: 50000004
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000080
  poolId: 50000004
  lvThreshold: 7
  groupWeight: 700
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 50000081
  poolId: 50000004
  lvThreshold: 8
  groupWeight: 1200
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 50000082
  poolId: 50000004
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 50000076
  poolId: 53000005
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 500
  groupWeight: 10550
  groupWeight: 10000
}
rows {
  id: 50000083
  poolId: 53000006
  lvThreshold: 240
  groupWeight: 1
  groupWeight: 9499
  groupWeight: 61685
  groupWeight: 500
  groupWeight: 28315
}
rows {
  id: 50000085
  poolId: 50000005
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 3500
}
rows {
  id: 50000086
  poolId: 50000005
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 3500
}
rows {
  id: 50000087
  poolId: 50000005
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 50000088
  poolId: 50000005
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 50000089
  poolId: 50000005
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
}
