com.tencent.wea.xlsRes.table_ItemStatusChangeData
excel/xls/D_道具表_状态切换.xlsx sheet:状态切换
rows {
  id: 500020
  statusInfo {
    status: 0
  }
  statusInfo {
    status: 1
  }
}
rows {
  id: 402200
  statusInfo {
    status: 0
    itemId: 402200
    emitter: "FX_CH_Change_PL_103_001"
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 402210
    emitter: "FX_CH_Change_PL_103_001"
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 402201
  statusInfo {
    status: 0
    itemId: 402201
    emitter: "FX_CH_Change_PL_103_002"
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 402211
    emitter: "FX_CH_Change_PL_103_002"
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 402202
  statusInfo {
    status: 0
    itemId: 402202
    emitter: "FX_CH_Change_PL_103_003"
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 402212
    emitter: "FX_CH_Change_PL_103_003"
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 730013
  statusInfo {
    status: 0
    itemId: 730013
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 730016
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 730014
  statusInfo {
    status: 0
    itemId: 730014
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 730017
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 730015
  statusInfo {
    status: 0
    itemId: 730015
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 730018
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 411660
  statusInfo {
    status: 0
    itemId: 411660
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_MANUAL
  }
  statusInfo {
    status: 1
    itemId: 411670
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_MANUAL
  }
}
rows {
  id: 411661
  statusInfo {
    status: 0
    itemId: 411661
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_MANUAL
  }
  statusInfo {
    status: 1
    itemId: 411671
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_MANUAL
  }
}
rows {
  id: 411662
  statusInfo {
    status: 0
    itemId: 411662
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_MANUAL
  }
  statusInfo {
    status: 1
    itemId: 411672
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_MANUAL
  }
}
rows {
  id: 640174
  statusInfo {
    status: 0
    itemId: 640174
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 640175
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 750002
  statusInfo {
    status: 0
    itemId: 750002
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 750003
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 750005
  statusInfo {
    status: 0
    itemId: 750005
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 750008
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 750006
  statusInfo {
    status: 0
    itemId: 750006
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 750009
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
rows {
  id: 750007
  statusInfo {
    status: 0
    itemId: 750007
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
  statusInfo {
    status: 1
    itemId: 750010
    type: 1
    triggerType: BTSSTT_TRIGGER_BY_SPRINT
  }
}
