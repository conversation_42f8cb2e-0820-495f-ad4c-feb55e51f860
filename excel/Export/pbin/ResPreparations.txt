com.tencent.wea.xlsRes.table_PreparationsData
excel/xls/B_备战.xlsx sheet:备战
rows {
  id: 1
  showMatchID: 105
  showMatchID: 113
  showMatchID: 151
  showMatchID: 106
  showMatchID: 109
  showMatchID: 112
  showLevelType: 2
  showLevelType: 3
  showLevelType: 1
  showLevelType: 1
  showLevelType: 1
  showLevelType: 1
  RankID: 151
  leftbutton1: Collect
  leftbutton2: ReputationsScore
  leftbutton3: Strategy
  leftbutton4: WolfNotice
  leftbutton5: Achievement
  leftbutton6: FeedBack
  rightbutton1: Room
  rightbutton2: Shop
  rightbutton3: Decorate
  rightbutton4: MonthCard
  leftbutton7: HistoricalRecord
  ChatChannel: 26
}
rows {
  id: 2
  showMatchID: 380
  showMatchID: 381
  showLevelType: 2
  showLevelType: 2
  leftbutton1: Collect
  leftbutton3: Strategy
  rightbutton1: Room
}
rows {
  id: 3
  showMatchID: 5600
  showMatchID: 5601
  showMatchID: 6006
  showMatchID: 6101
  showMatchID: 5700
  showMatchID: 6300
  showLevelType: 1
  showLevelType: 1
  showLevelType: 1
  showLevelType: 1
  showLevelType: 1
  showLevelType: 1
  WindowName: "UI_Arena_Preparations_Main_New"
}
