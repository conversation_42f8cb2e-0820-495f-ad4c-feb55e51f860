com.tencent.wea.xlsRes.table_RightUpInfoData
excel/xls/R_RightUpInfo.xlsx sheet:页签排序
rows {
  Index: 2
  ButtonName: "赛季"
  ButtonID: "w_btn_NewQualifying"
  ShowIndex: 11
  RedDotType: "Lobby_Season"
  ShowIndex2: 12
  ShowIndex3: 12
  ShowIndex4: 12
  ShowIndex5: 12
  ShowIndex6: 12
  ShowIndex7: 6
  ShowIndex8: 13
  bDefaultShow: false
  ShowIndex9: 13
  ShowIndex10: 6
}
rows {
  Index: 3
  ButtonName: "通行证"
  ButtonID: "w_btn_PermitSystem"
  ShowIndex: 12
  RedDotType: "Lobby_Permit"
  ShowIndex2: 13
  ShowIndex3: 13
  ShowIndex4: 13
  ShowIndex5: 13
  ShowIndex6: 13
  ShowIndex7: 16
  ShowIndex8: 17
  bDefaultShow: false
  ShowIndex9: 12
  ShowIndex10: 16
}
rows {
  Index: 4
  ButtonName: "锦标赛"
  ButtonID: "w_btn_Competition"
  ShowIndex: 28
  RedDotType: "Lobby_Competition"
  ShowIndex2: 10
  ShowIndex3: 7
  ShowIndex4: 7
  ShowIndex5: 7
  ShowIndex6: 7
  ShowIndex7: 7
  ShowIndex8: 8
  bDefaultShow: false
  ShowIndex9: 13
  ShowIndex10: 7
}
rows {
  Index: 5
  ButtonName: "开园庆典"
  ButtonID: "w_btn_Pilot"
  ShowIndex: 18
  RedDotType: "-1"
  ShowIndex2: 19
  ShowIndex3: 19
  ShowIndex4: 19
  ShowIndex5: 19
  ShowIndex6: 19
  ShowIndex7: 20
  ShowIndex8: 20
  bDefaultShow: false
  ShowIndex9: 18
  ShowIndex10: 20
}
rows {
  Index: 6
  ButtonName: "首充"
  ButtonID: "w_btn_FirstCharge"
  ShowIndex: 15
  RedDotType: "FirstCharge"
  ShowIndex2: 16
  ShowIndex3: 16
  ShowIndex4: 16
  ShowIndex5: 16
  ShowIndex6: 16
  ShowIndex7: 24
  ShowIndex8: 25
  bDefaultShow: false
  ShowIndex9: 5
  ShowIndex10: 24
}
rows {
  Index: 7
  ButtonName: "红包雨"
  ButtonID: "w_btn_RedEnvelope"
  ShowIndex: 4
  RedDotType: "RedEnvelopeEntrance"
  ShowIndex2: 5
  ShowIndex3: 5
  ShowIndex4: 5
  ShowIndex5: 5
  ShowIndex6: 8
  ShowIndex7: 9
  ShowIndex8: 6
  bDefaultShow: false
  ShowIndex9: 4
  ShowIndex10: 9
}
rows {
  Index: 8
  ButtonName: "烟花秀"
  ButtonID: "w_btn_FireworkParty"
  ShowIndex: 7
  RedDotType: "FireworkPartyEntrance"
  ShowIndex2: 7
  ShowIndex3: 10
  ShowIndex4: 10
  ShowIndex5: 10
  ShowIndex6: 10
  ShowIndex7: 11
  ShowIndex8: 11
  bDefaultShow: false
  ShowIndex9: 9
  ShowIndex10: 11
}
rows {
  Index: 9
  ButtonName: "限时礼包"
  ButtonID: "w_btn_SceneGift"
  ShowIndex: 2
  RedDotType: "-1"
  ShowIndex2: 2
  ShowIndex3: 2
  ShowIndex4: 2
  ShowIndex5: 2
  ShowIndex6: 2
  ShowIndex7: 2
  ShowIndex8: 2
  bDefaultShow: false
  ShowIndex9: 2
  ShowIndex10: 2
}
rows {
  Index: 10
  ButtonName: "夺冠"
  ButtonID: "w_btn_BlessBag"
  ShowIndex: 16
  RedDotType: "-1"
  ShowIndex2: 17
  ShowIndex3: 17
  ShowIndex4: 17
  ShowIndex5: 17
  ShowIndex6: 17
  ShowIndex7: 18
  ShowIndex8: 18
  bDefaultShow: false
  ShowIndex9: 16
  ShowIndex10: 18
}
rows {
  Index: 11
  ButtonName: "问卷"
  ButtonID: "w_btn_Questionnaire"
  ShowIndex: 17
  RedDotType: "Lobby_Questionnaire"
  ShowIndex2: 18
  ShowIndex3: 18
  ShowIndex4: 18
  ShowIndex5: 18
  ShowIndex6: 18
  ShowIndex7: 19
  ShowIndex8: 19
  bDefaultShow: false
  ShowIndex9: 6
  ShowIndex10: 19
}
rows {
  Index: 13
  ButtonName: "新春导航"
  ButtonID: "w_btn_Navigation"
  ShowIndex: 13
  RedDotType: "NewNavigation"
  ShowIndex2: 14
  ShowIndex3: 14
  ShowIndex4: 14
  ShowIndex5: 14
  ShowIndex6: 14
  ShowIndex7: 28
  ShowIndex8: 15
  bDefaultShow: false
  ShowIndex9: 13
  ShowIndex10: 28
}
rows {
  Index: 14
  ButtonName: "回流"
  ButtonID: "w_btn_Return"
  ShowIndex: 4
  RedDotType: "-1"
  ShowIndex2: 4
  ShowIndex3: 4
  ShowIndex4: 4
  ShowIndex5: 4
  ShowIndex6: 4
  ShowIndex7: 4
  ShowIndex8: 4
  bDefaultShow: false
  ShowIndex9: 5
  ShowIndex10: 4
}
rows {
  Index: 15
  ButtonName: "玩法回流"
  ButtonID: "w_btn_ModeReturn"
  ShowIndex: 4
  RedDotType: "-1"
  ShowIndex2: 4
  ShowIndex3: 4
  ShowIndex4: 4
  ShowIndex5: 4
  ShowIndex6: 4
  ShowIndex7: 4
  ShowIndex8: 4
  bDefaultShow: false
  ShowIndex9: 5
  ShowIndex10: 4
}
rows {
  Index: 16
  ButtonName: "电视台"
  ButtonID: "w_btn_Competition_TV"
  ShowIndex: 10
  RedDotType: "Tmes_TVLiving"
  ShowIndex2: 11
  ShowIndex3: 11
  ShowIndex4: 11
  ShowIndex5: 11
  ShowIndex6: 11
  ShowIndex7: 12
  ShowIndex8: 12
  bDefaultShow: false
  ShowIndex9: 10
  ShowIndex10: 12
}
rows {
  Index: 17
  ButtonName: "奥特曼"
  ButtonID: "w_btn_RevivalOfLight"
  ShowIndex: 29
  RedDotType: "-1"
  ShowIndex2: 6
  ShowIndex3: 6
  ShowIndex4: 6
  ShowIndex5: 6
  ShowIndex6: 6
  ShowIndex7: 13
  ShowIndex8: 7
  bDefaultShow: false
  ShowIndex9: 29
  ShowIndex10: 13
}
rows {
  Index: 18
  ButtonName: "UGC新年活动"
  ButtonID: "w_btn_NewYearCollection"
  ShowIndex: 20
  RedDotType: "NYActivityCollection"
  ShowIndex2: 21
  ShowIndex3: 21
  ShowIndex4: 21
  ShowIndex5: 21
  ShowIndex6: 21
  ShowIndex7: 21
  ShowIndex8: 22
  bDefaultShow: false
  ShowIndex9: 20
  ShowIndex10: 21
}
rows {
  Index: 19
  ButtonName: "脑力达人"
  ButtonID: "w_btn_Mental"
  ShowIndex: 6
  RedDotType: "BrainiacActivity"
  ShowIndex2: 9
  ShowIndex3: 9
  ShowIndex4: 8
  ShowIndex5: 8
  ShowIndex6: 5
  ShowIndex7: 10
  ShowIndex8: 10
  bDefaultShow: false
  ShowIndex9: 6
  ShowIndex10: 10
}
rows {
  Index: 20
  ButtonName: "星运红包"
  ButtonID: "w_btn_StarLuck"
  ShowIndex: 3
  RedDotType: "StarLuckEntrance"
  ShowIndex2: 3
  ShowIndex3: 3
  ShowIndex4: 3
  ShowIndex5: 3
  ShowIndex6: 3
  ShowIndex7: 3
  ShowIndex8: 3
  bDefaultShow: false
  ShowIndex9: 4
  ShowIndex10: 3
}
rows {
  Index: 21
  ButtonName: "轮换Icon"
  ButtonID: "w_btn_IconSwitch"
  ShowIndex: 19
  ShowIndex2: 20
  ShowIndex3: 20
  ShowIndex4: 20
  ShowIndex5: 20
  ShowIndex6: 20
  ShowIndex7: 8
  ShowIndex8: 14
  bDefaultShow: false
  ShowIndex9: 8
  ShowIndex10: 8
}
rows {
  Index: 22
  ButtonName: "星友滴滴"
  ButtonID: "w_btn_TravelTogether"
  ShowIndex: 8
  RedDotType: "-1"
  ShowIndex2: 8
  ShowIndex3: 8
  ShowIndex4: 9
  ShowIndex5: 9
  ShowIndex6: 9
  ShowIndex7: 14
  ShowIndex8: 9
  bDefaultShow: false
  ShowIndex9: 10
  ShowIndex10: 14
}
rows {
  Index: 23
  ButtonName: "功夫熊猫"
  ButtonID: "w_btn_KongfuPandaActivity"
  ShowIndex: 20
  ShowIndex2: 21
  ShowIndex3: 21
  ShowIndex4: 21
  ShowIndex5: 21
  ShowIndex6: 21
  ShowIndex7: 22
  ShowIndex8: 22
  bDefaultShow: false
  ShowIndex9: 20
  ShowIndex10: 22
}
rows {
  Index: 24
  ButtonName: "踏青迎春"
  ButtonID: "w_btn_ActNavigation"
  ShowIndex: 21
  RedDotType: "-1"
  ShowIndex2: 17
  ShowIndex3: 7
  ShowIndex4: 17
  ShowIndex5: 2
  ShowIndex6: 6
  ShowIndex7: 27
  ShowIndex8: 15
  bDefaultShow: false
  ShowIndex9: 21
  ShowIndex10: 27
}
rows {
  Index: 25
  ButtonName: "巅峰盛典"
  ButtonID: "w_btn_Concert"
  ShowIndex: 24
  RedDotType: "-1"
  ShowIndex2: 25
  ShowIndex3: 25
  ShowIndex4: 25
  ShowIndex5: 25
  ShowIndex6: 25
  ShowIndex7: 5
  bDefaultShow: false
  ShowIndex9: 24
  ShowIndex10: 5
}
rows {
  Index: 26
  ButtonName: "限时特惠"
  ButtonID: "w_btn_WXSceneGift"
  ShowIndex: 3
  RedDotType: "-1"
  ShowIndex2: 2
  ShowIndex3: 2
  ShowIndex4: 2
  ShowIndex5: 2
  ShowIndex6: 2
  ShowIndex7: 2
  ShowIndex8: 2
  bDefaultShow: false
  ShowIndex9: 7
  ShowIndex10: 2
}
rows {
  Index: 27
  ButtonName: "轮换Icon-娱乐活动专属"
  ButtonID: "w_btn_IconSwitchJoy"
  ShowIndex: 21
  ShowIndex2: 20
  ShowIndex3: 20
  ShowIndex4: 20
  ShowIndex5: 20
  ShowIndex6: 20
  ShowIndex7: 17
  ShowIndex8: 14
  bDefaultShow: false
  ShowIndex9: 11
  ShowIndex10: 17
}
rows {
  Index: 28
  ButtonName: "限时游戏"
  ButtonID: "w_btn_Entertament"
  ShowIndex: 20
  RedDotType: "-1"
  ShowIndex2: 27
  ShowIndex3: 27
  ShowIndex4: 27
  ShowIndex5: 27
  ShowIndex6: 27
  ShowIndex7: 23
  ShowIndex8: 28
  bDefaultShow: false
  ShowIndex9: 27
  ShowIndex10: 23
}
rows {
  Index: 29
  ButtonName: "娱乐向导"
  ButtonID: "w_btn_GameJoyGuide"
  ShowIndex: 9
  RedDotType: "Lobby_GameGuide"
  ShowIndex2: 28
  ShowIndex3: 28
  ShowIndex4: 28
  ShowIndex5: 28
  ShowIndex6: 28
  ShowIndex7: 28
  ShowIndex8: 29
  ShowIndex9: 29
  ShowIndex10: 25
}
rows {
  Index: 30
  ButtonName: "免费抽奖"
  ButtonID: "w_btn_FreeDraw"
  ShowIndex: 5
  RedDotType: "-1"
  ShowIndex2: 29
  ShowIndex3: 29
  ShowIndex4: 29
  ShowIndex5: 29
  ShowIndex6: 29
  ShowIndex7: 29
  ShowIndex8: 5
  bDefaultShow: false
  ShowIndex9: 5
  ShowIndex10: 29
}
rows {
  Index: 31
  ButtonName: "下载福利"
  ButtonID: "w_btn_VADownload"
  ShowIndex: 7
  RedDotType: "Lobby_VADownload"
  ShowIndex2: 30
  ShowIndex3: 30
  ShowIndex4: 30
  ShowIndex5: 30
  ShowIndex6: 30
  ShowIndex7: 30
  ShowIndex8: 30
  bDefaultShow: false
  ShowIndex9: 30
  ShowIndex10: 30
}
rows {
  Index: 32
  ButtonName: "云转端"
  ButtonID: "w_btn_GuideApp"
  ShowIndex: 4
  RedDotType: "-1"
  ShowIndex2: 4
  ShowIndex3: 4
  ShowIndex4: 4
  ShowIndex5: 4
  ShowIndex6: 4
  ShowIndex7: 4
  ShowIndex8: 4
  bDefaultShow: false
  ShowIndex9: 4
  ShowIndex10: 4
}
rows {
  Index: 33
  ButtonName: "跑马灯"
  ButtonID: "w_btn_IconMarquee"
  ShowIndex: 5
  RedDotType: "LobbyActivityMarquee"
  ShowIndex2: 5
  ShowIndex3: 5
  ShowIndex4: 5
  ShowIndex5: 5
  ShowIndex6: 5
  ShowIndex7: 5
  ShowIndex8: 5
  bDefaultShow: false
  ShowIndex9: 5
  ShowIndex10: 5
}
rows {
  Index: 34
  ButtonName: "重返农场"
  ButtonID: "w_btn_ReturnFarm"
  ShowIndex: 30
  RedDotType: "-1"
  ShowIndex2: 30
  ShowIndex3: 30
  ShowIndex4: 30
  ShowIndex5: 30
  ShowIndex6: 30
  ShowIndex7: 30
  ShowIndex8: 30
  ShowIndex9: 30
  ShowIndex10: 5
}
rows {
  Index: 35
  ButtonName: "限时返利"
  ButtonID: "w_btn_MiniGameSceneGift"
  ShowIndex: 4
  RedDotType: "-1"
  ShowIndex2: 31
  ShowIndex3: 31
  ShowIndex4: 31
  ShowIndex5: 31
  ShowIndex6: 31
  ShowIndex7: 31
  ShowIndex8: 31
  bDefaultShow: false
  ShowIndex9: 31
  ShowIndex10: 31
}
rows {
  Index: 36
  ButtonName: "摇钱树"
  ButtonID: "w_btn_IosCashCow"
  ShowIndex: 5
  RedDotType: "-1"
  ShowIndex2: 31
  ShowIndex3: 31
  ShowIndex4: 31
  ShowIndex5: 31
  ShowIndex6: 31
  ShowIndex7: 31
  ShowIndex8: 31
  bDefaultShow: false
  ShowIndex9: 31
  ShowIndex10: 31
}
rows {
  Index: 37
  ButtonName: "限时惊喜"
  ButtonID: "w_btn_Surprised"
  ShowIndex: 4
  RedDotType: "-1"
  ShowIndex2: 31
  ShowIndex3: 31
  ShowIndex4: 31
  ShowIndex5: 31
  ShowIndex6: 31
  ShowIndex7: 31
  ShowIndex8: 31
  bDefaultShow: false
  ShowIndex9: 31
  ShowIndex10: 31
}
