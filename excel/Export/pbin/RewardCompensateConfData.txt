com.tencent.wea.xlsRes.table_RewardCompensateConf
excel/xls/J_奖励补领.xlsx sheet:奖励补领
rows {
  type: RCT_ACTIVITY_7D_SIGNIN
  enable: true
  mailTemplateId: 136
  handleType: RCHT_SELFDEFINED
}
rows {
  type: RCT_MONTH_CARD
  enable: false
  mailTemplateId: 50
  handleType: RCHT_SELFDEFINED
}
rows {
  type: RCT_SEASON_FASHION_SCORE
  enable: true
  mailTemplateId: 123
  handleType: RCHT_SELFDEFINED
}
rows {
  type: RCT_TASK_DAILY
  enable: true
  mailTemplateId: 124
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 1001
    beginTimeSec {
      seconds: 1716912000
    }
    endTimeSec {
      seconds: 4086432000
    }
  }
}
rows {
  type: RCT_TASK_WEEKLY
  enable: true
  mailTemplateId: 125
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 1101
    beginTimeSec {
      seconds: 1716912000
    }
    endTimeSec {
      seconds: 4086432000
    }
  }
}
rows {
  type: RCT_DAILY_CLEARANCE
  enable: true
  mailTemplateId: 126
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 1700
    beginTimeSec {
      seconds: 1716912000
    }
    endTimeSec {
      seconds: 4086432000
    }
  }
}
rows {
  type: RCT_SUPPLY_ACTIVITY
  enable: true
  mailTemplateId: 127
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 51773
    beginTimeSec {
      seconds: 1716912000
    }
    endTimeSec {
      seconds: 4086432000
    }
  }
}
rows {
  type: RCT_FIFTH_SEASON_SPRINT
  enable: true
  mailTemplateId: 128
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 10006
    beginTimeSec {
      seconds: 1717689600
    }
    endTimeSec {
      seconds: 1721318399
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP1
  enable: true
  mailTemplateId: 132
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 58002
    groupIdList: 58003
    groupIdList: 58004
    groupIdList: 58005
    groupIdList: 58006
    groupIdList: 58007
    groupIdList: 58008
    beginTimeSec {
      seconds: 1715270400
    }
    endTimeSec {
      seconds: 1720195199
    }
  }
}
rows {
  type: RCT_LIGHTING_SEASON_TASK
  enable: true
  mailTemplateId: 134
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 9501
    beginTimeSec {
      seconds: 1718294400
    }
    endTimeSec {
      seconds: 1721318399
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP5
  enable: true
  mailTemplateId: 224
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 44019
    beginTimeSec {
      seconds: 1724342400
    }
    endTimeSec {
      seconds: 1725551999
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP6
  enable: true
  mailTemplateId: 225
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 44020
    beginTimeSec {
      seconds: 1724342400
    }
    endTimeSec {
      seconds: 1726415999
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP7
  enable: true
  mailTemplateId: 226
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 44021
    beginTimeSec {
      seconds: 1724342400
    }
    endTimeSec {
      seconds: 1725206399
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP8
  enable: true
  mailTemplateId: 227
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 44022
    beginTimeSec {
      seconds: 1725552000
    }
    endTimeSec {
      seconds: 1726415999
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP9
  enable: true
  mailTemplateId: 230
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 44039
    beginTimeSec {
      seconds: 1726761600
    }
    endTimeSec {
      seconds: 1727625599
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP15
  enable: true
  mailTemplateId: 231
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 44040
    beginTimeSec {
      seconds: 1728316800
    }
    endTimeSec {
      seconds: 1729180799
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP10
  enable: true
  mailTemplateId: 230
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 60502
    beginTimeSec {
      seconds: 1727712000
    }
    endTimeSec {
      seconds: 1727798399
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP11
  enable: true
  mailTemplateId: 230
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 60503
    beginTimeSec {
      seconds: 1727971200
    }
    endTimeSec {
      seconds: 1728057599
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP12
  enable: true
  mailTemplateId: 230
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 60504
    beginTimeSec {
      seconds: 1728316800
    }
    endTimeSec {
      seconds: 1728403199
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP13
  enable: true
  mailTemplateId: 230
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 60505
    beginTimeSec {
      seconds: 1728576000
    }
    endTimeSec {
      seconds: 1728662399
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP14
  enable: true
  mailTemplateId: 230
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 60506
    beginTimeSec {
      seconds: 1728662400
    }
    endTimeSec {
      seconds: 1728748799
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP16
  enable: true
  mailTemplateId: 242
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 2003
    beginTimeSec {
      seconds: 1732032000
    }
    endTimeSec {
      seconds: 1733241599
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP17
  enable: true
  mailTemplateId: 242
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 2004
    beginTimeSec {
      seconds: 1733932800
    }
    endTimeSec {
      seconds: 1737388799
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP18
  enable: true
  mailTemplateId: 242
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 2005
    beginTimeSec {
      seconds: 1739980800
    }
    endTimeSec {
      seconds: 1742227199
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP19
  enable: true
  mailTemplateId: 242
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 2006
    beginTimeSec {
      seconds: 1742400000
    }
    endTimeSec {
      seconds: 1746460799
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP22
  enable: true
  mailTemplateId: 242
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 2007
    beginTimeSec {
      seconds: 1748448000
    }
    endTimeSec {
      seconds: 1751299199
    }
  }
}
rows {
  type: RCT_CAPTURE_SHADOW
  enable: true
  mailTemplateId: 241
  handleType: RCHT_SELFDEFINED
}
rows {
  type: RCT_CAPTURE_SHADOW_FINAL
  enable: true
  mailTemplateId: 241
  handleType: RCHT_SELFDEFINED
}
rows {
  type: RCT_COMMON_TASKGROUP20
  enable: true
  mailTemplateId: 288
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 910042
    groupIdList: 910043
    groupIdList: 910044
    beginTimeSec {
      seconds: 1743350400
    }
    endTimeSec {
      seconds: 1755446399
    }
  }
}
rows {
  type: RCT_COMMON_TASKGROUP21
  enable: true
  mailTemplateId: 288
  handleType: RCHT_TASKGROUP
  taskGroupInfo {
    groupIdList: 910041
    beginTimeSec {
      seconds: 1743350400
    }
    endTimeSec {
      seconds: 1755446399
    }
  }
}
