com.tencent.wea.xlsRes.table_GameSettingOptionConf
excel/xls/S_通用设置表.xlsx sheet:设置选项表
rows {
  id: "1"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_QUICK
  name: "快捷设置"
  isShow: true
  order: 1
  whitePlatformIdList: 0
  whitePlatformIdList: 1
  whitePlatformIdList: 2
  whitePlatformIdList: 5
}
rows {
  id: "99"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_DETAIL
  name: "视野"
  functionType: GSFT_GRAPHICS_VIEWDIS
  layoutType: GSLT_CUSTOMCOMPONENT
  tipsInfo: "UGC_VisionSettings_Desc"
  isShow: true
  order: 6
}
rows {
  id: "2"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_DETAIL
  name: "分辨率"
  functionType: GSFT_GRAPHICS_RESOLUTION
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "1"
  isShow: true
  order: 5
  whitePlatformIdList: 0
  whitePlatformIdList: 1
}
rows {
  id: "3"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_DETAIL
  name: "帧率"
  functionType: GSFT_GRAPHICS_FRAMERATE
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "2"
  isShow: true
  order: 3
  whitePlatformIdList: 0
  whitePlatformIdList: 1
  whitePlatformIdList: 2
  whitePlatformIdList: 5
}
rows {
  id: "4"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_DETAIL
  name: "窗口"
  functionType: GSFT_GRAPHICS_PCWINDOWS
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "7"
  isShow: true
  order: 4
  whitePlatformIdList: 2
  whitePlatformIdList: 5
}
rows {
  id: "5"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_DETAIL
  name: "分辨率"
  functionType: GSFT_GRAPHICS_PCWINDOWSIZE
  layoutType: GSLT_COMBOXCOMPONENT
  layoutID: "1"
  isShow: true
  order: 2
  whitePlatformIdList: 2
  whitePlatformIdList: 5
}
rows {
  id: "6"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_JOYSTICK
  name: "类型"
  functionType: GSFT_GRAPHICS_JOYSTICKMODE
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "3"
  tipsInfo: "SettingTip_JotstickMode"
  isShow: true
  order: 7
  whitePlatformIdList: 0
  whitePlatformIdList: 1
}
rows {
  id: "7"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_MOUSE
  name: "局内显示鼠标指针"
  functionType: GSFT_GRAPHICS_DEFAULTSHOWMOUSE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "41"
  tipsInfo: "SettingTip_ShowMouseTip"
  isShow: true
  order: 8
  whitePlatformIdList: 2
}
rows {
  id: "8"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_MOUSE
  name: "快捷切换按键"
  functionType: GSFT_GRAPHICS_MOUSESHORTCUT
  layoutType: GSLT_IMAGECOMPONENT
  layoutID: "1"
  tipsInfo: "SettingTip_ShowMouseShortcutTip"
  isShow: true
  order: 9
  whitePlatformIdList: 2
}
rows {
  id: "103"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_MOUSE
  name: "局内显示键鼠按键"
  functionType: GSFT_GRAPHICS_DEFAULTSHOWKEYBUTTON
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "41"
  tipsInfo: "SettingTip_ShowKeyOnButtonTip"
  isShow: true
  order: 103
  whitePlatformIdList: 2
}
rows {
  id: "104"
  tabType: GSTT_GRAPHICS
  subTabType: GSTT_GRAPHICS_MOUSE
  name: "快捷切换按键"
  functionType: GSFT_GRAPHICS_KEYBUTTONSHORTCUT
  layoutType: GSLT_IMAGECOMPONENT
  layoutID: "2"
  tipsInfo: "SettingTip_ShowKeyOnButtonShortcutTip"
  isShow: true
  order: 104
  whitePlatformIdList: 2
}
rows {
  id: "9"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOLUME
  name: "音乐"
  functionType: GSFT_VOICE_MUSIC
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "1"
  isShow: true
  order: 10
}
rows {
  id: "10"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOLUME
  name: "音效"
  functionType: GSFT_VOICE_SFX
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "2"
  isShow: true
  order: 11
}
rows {
  id: "11"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOLUME
  name: "角色语音"
  functionType: GSFT_VOICE_CHARACTERVOICE
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "3"
  isShow: true
  order: 12
}
rows {
  id: "12"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOLUME
  name: "星世界推荐音乐"
  functionType: GSFT_VOICE_STARWORLDRECOMMENDEDMUSIC
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "1"
  tipsInfo: "UI_VoiceSetting_RecommandMusicTip"
  isShow: true
  order: 13
}
rows {
  id: "13"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOICE
  name: "语音"
  functionType: GSFT_VOICE_VOICE
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "4"
  isShow: true
  order: 14
}
rows {
  id: "14"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOICE
  name: "麦克风输入"
  functionType: GSFT_VOICE_MICROPHONE
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "5"
  isShow: true
  order: 15
}
rows {
  id: "15"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOICE
  name: "接收魔音"
  functionType: GSFT_VOICE_RECEIVEMAGICVOICE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "2"
  tipsInfo: "UI_VoiceSetting_MagicVoice"
  isShow: true
  order: 16
}
rows {
  id: "16"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOICE
  name: "开启广场收听喇叭"
  functionType: GSFT_VOICE_OPENLOBBYVOICE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "3"
  isShow: true
  order: 17
}
rows {
  id: "105"
  tabType: GSTT_VOICE
  subTabType: GSTT_VOICE_VOICE
  name: "开启实时语音转文字"
  functionType: GSFT_VOICE_OPENREALTIMEVOICETEXT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "3"
  isShow: true
  order: 106
}
rows {
  id: "17"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_CAMERA
  name: "镜头灵敏度"
  functionType: GSFT_GAME_CAMERASENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "6"
  isShow: true
  order: 18
}
rows {
  id: "18"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_CAMERA
  name: "镜头辅助"
  functionType: GSFT_GAME_OPENCAMERAHELP
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "4"
  isShow: true
  order: 19
}
rows {
  id: "19"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_CAMERA
  name: "道具小窗口"
  functionType: GSFT_GAME_OPENCAMERACAPTURE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "5"
  isShow: true
  order: 20
}
rows {
  id: "20"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "速度灵敏度"
  functionType: GSFT_GAME_SPEEDSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "7"
  tipsInfo: "SettingTip_SpeedSensitivity"
  isShow: true
  order: 21
}
rows {
  id: "21"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "转向灵敏度"
  functionType: GSFT_GAME_ROCKERSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "8"
  tipsInfo: "SettingTip_RockerSensitivity"
  isShow: true
  order: 22
}
rows {
  id: "22"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "回拉辅助"
  functionType: GSFT_GAME_AIRINERTIA
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "6"
  tipsInfo: "SettingTip_AirInertia"
  isShow: true
  order: 23
}
rows {
  id: "23"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "角色柔软度"
  functionType: GSFT_GAME_CHARACTERFLEXIBILITY
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "4"
  tipsInfo: "SettingTip_CharacterFlexibility"
  isShow: true
  order: 24
}
rows {
  id: "24"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "角色标记点"
  functionType: GSFT_GAME_CHARACTERPOINT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "7"
  isShow: true
  order: 25
}
rows {
  id: "25"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "星世界载具驾驶"
  functionType: GSFT_GAME_VEHICLECONTROLTYPE
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "5"
  tipsInfo: "SettingTip_VehicleControlType"
  isShow: true
  order: 26
}
rows {
  id: "26"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "臻藏时装移动动作"
  functionType: GSFT_GAME_SUITSPECIALANIM
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "8"
  isShow: true
  order: 27
}
rows {
  id: "27"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_CAMERA
  name: "镜头灵敏度"
  functionType: GSFT_GAME_CAMERASENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "6"
  isShow: true
  order: 28
}
rows {
  id: "28"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_CAMERA
  name: "镜头辅助"
  functionType: GSFT_GAME_OPENCAMERAHELP
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "4"
  isShow: true
  order: 29
}
rows {
  id: "29"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_CAMERA
  name: "道具小窗口"
  functionType: GSFT_GAME_OPENCAMERACAPTURE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "5"
  isShow: true
  order: 30
}
rows {
  id: "30"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_OPERATION
  name: "速度灵敏度"
  functionType: GSFT_GAME_SPEEDSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "7"
  tipsInfo: "SettingTip_SpeedSensitivity"
  isShow: true
  order: 31
}
rows {
  id: "31"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_OPERATION
  name: "转向灵敏度"
  functionType: GSFT_GAME_ROCKERSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "8"
  tipsInfo: "SettingTip_RockerSensitivity"
  isShow: true
  order: 32
}
rows {
  id: "32"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_OPERATION
  name: "回拉辅助"
  functionType: GSFT_GAME_AIRINERTIA
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "6"
  tipsInfo: "SettingTip_AirInertia"
  isShow: true
  order: 33
}
rows {
  id: "33"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_OPERATION
  name: "角色柔软度"
  functionType: GSFT_GAME_CHARACTERFLEXIBILITY
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "4"
  tipsInfo: "SettingTip_CharacterFlexibility"
  isShow: true
  order: 34
}
rows {
  id: "34"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_OPERATION
  name: "角色标记点"
  functionType: GSFT_GAME_CHARACTERPOINT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "7"
  isShow: true
  order: 35
}
rows {
  id: "35"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_OPERATION
  name: "星世界载具驾驶"
  functionType: GSFT_GAME_VEHICLECONTROLTYPE
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "5"
  tipsInfo: "SettingTip_VehicleControlType"
  isShow: true
  order: 36
}
rows {
  id: "36"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_OPERATION
  name: "臻藏时装移动动作"
  functionType: GSFT_GAME_SUITSPECIALANIM
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "8"
  isShow: true
  order: 37
}
rows {
  id: "37"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_CAMERA
  name: "镜头灵敏度"
  functionType: GSFT_GAME_CAMERASENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "6"
  isShow: true
  order: 38
}
rows {
  id: "38"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_CAMERA
  name: "镜头辅助"
  functionType: GSFT_GAME_OPENCAMERAHELP
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "4"
  isShow: true
  order: 39
}
rows {
  id: "39"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_CAMERA
  name: "道具小窗口"
  functionType: GSFT_GAME_OPENCAMERACAPTURE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "5"
  isShow: true
  order: 40
}
rows {
  id: "40"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_OPERATION
  name: "速度灵敏度"
  functionType: GSFT_GAME_SPEEDSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "7"
  tipsInfo: "SettingTip_SpeedSensitivity"
  isShow: true
  order: 41
}
rows {
  id: "41"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_OPERATION
  name: "转向灵敏度"
  functionType: GSFT_GAME_ROCKERSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "8"
  tipsInfo: "SettingTip_RockerSensitivity"
  isShow: true
  order: 42
}
rows {
  id: "42"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_OPERATION
  name: "回拉辅助"
  functionType: GSFT_GAME_AIRINERTIA
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "6"
  tipsInfo: "SettingTip_AirInertia"
  isShow: true
  order: 43
}
rows {
  id: "43"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_OPERATION
  name: "角色柔软度"
  functionType: GSFT_GAME_CHARACTERFLEXIBILITY
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "4"
  tipsInfo: "SettingTip_CharacterFlexibility"
  isShow: true
  order: 44
}
rows {
  id: "44"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_OPERATION
  name: "角色标记点"
  functionType: GSFT_GAME_CHARACTERPOINT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "7"
  isShow: true
  order: 45
}
rows {
  id: "45"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_OPERATION
  name: "星世界载具驾驶"
  functionType: GSFT_GAME_VEHICLECONTROLTYPE
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "5"
  tipsInfo: "SettingTip_VehicleControlType"
  isShow: true
  order: 46
}
rows {
  id: "46"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_OPERATION
  name: "臻藏时装移动动作"
  functionType: GSFT_GAME_SUITSPECIALANIM
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "8"
  isShow: true
  order: 47
}
rows {
  id: "47"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_CAMERA
  name: "镜头灵敏度"
  functionType: GSFT_GAME_CAMERASENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "6"
  isShow: true
  order: 48
}
rows {
  id: "48"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_CAMERA
  name: "镜头辅助"
  functionType: GSFT_GAME_OPENCAMERAHELP
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "4"
  isShow: true
  order: 49
}
rows {
  id: "49"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_CAMERA
  name: "道具小窗口"
  functionType: GSFT_GAME_OPENCAMERACAPTURE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "5"
  isShow: true
  order: 50
}
rows {
  id: "50"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_OPERATION
  name: "速度灵敏度"
  functionType: GSFT_GAME_SPEEDSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "7"
  tipsInfo: "SettingTip_SpeedSensitivity"
  isShow: true
  order: 51
}
rows {
  id: "51"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_OPERATION
  name: "转向灵敏度"
  functionType: GSFT_GAME_ROCKERSENSITIVITY
  layoutType: GSLT_SLIDERCOMPONENT
  layoutID: "8"
  tipsInfo: "SettingTip_RockerSensitivity"
  isShow: true
  order: 52
}
rows {
  id: "52"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_OPERATION
  name: "回拉辅助"
  functionType: GSFT_GAME_AIRINERTIA
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "6"
  tipsInfo: "SettingTip_AirInertia"
  isShow: true
  order: 53
}
rows {
  id: "53"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_OPERATION
  name: "角色柔软度"
  functionType: GSFT_GAME_CHARACTERFLEXIBILITY
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "4"
  tipsInfo: "SettingTip_CharacterFlexibility"
  isShow: true
  order: 54
}
rows {
  id: "54"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_OPERATION
  name: "角色标记点"
  functionType: GSFT_GAME_CHARACTERPOINT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "7"
  isShow: true
  order: 55
}
rows {
  id: "55"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_OPERATION
  name: "星世界载具驾驶"
  functionType: GSFT_GAME_VEHICLECONTROLTYPE
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "5"
  tipsInfo: "SettingTip_VehicleControlType"
  isShow: true
  order: 56
}
rows {
  id: "56"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_OPERATION
  name: "臻藏时装移动动作"
  functionType: GSFT_GAME_SUITSPECIALANIM
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "8"
  isShow: true
  order: 57
}
rows {
  id: "57"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_STARPROTECTION
  name: "星宝防护设置总开关"
  functionType: GSFT_PRIVACY_STARGUARDSETTINGMASTERSWITCH
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "9"
  tipsInfo: "SettingTip_baby_funSetting"
  isShow: true
  order: 58
}
rows {
  id: "58"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_STARPROTECTION
  name: "禁用公共聊天"
  functionType: GSFT_PRIVACY_CLOSECHAT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "10"
  tipsInfo: "SettingTip_baby_closeChat"
  isShow: true
  order: 59
}
rows {
  id: "59"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_STARPROTECTION
  name: "禁用陌生人私聊"
  functionType: GSFT_PRIVACY_CLOSESTRANGERCHAT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "11"
  tipsInfo: "SettingTip_baby_closeStrangerChat"
  isShow: true
  order: 60
}
rows {
  id: "60"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_STARPROTECTION
  name: "禁止陌生人组队邀请"
  functionType: GSFT_PRIVACY_CLOSESTRANGERTEAMINVITE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "12"
  tipsInfo: "SettingTip_baby_closeTeamInvite"
  isShow: true
  order: 61
}
rows {
  id: "61"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_STARPROTECTION
  name: "禁止陌生人拜访星家园"
  functionType: GSFT_PRIVACY_CLOSESTRANGERVISITHOME
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "13"
  tipsInfo: "SettingTip_baby_closeVisitHome"
  isShow: true
  order: 62
}
rows {
  id: "62"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "亲密关系显示"
  functionType: GSFT_PRIVACY_INTIMATERELATIONDISPLAY
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "14"
  isShow: true
  order: 63
}
rows {
  id: "63"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "个性化推荐"
  functionType: GSFT_PRIVACY_PERSONALIZEDRECOMMEND
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "15"
  tipsInfo: "SettingTip_PersonalizedRecommendation"
  isShow: true
  order: 64
}
rows {
  id: "64"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "星世界足迹"
  functionType: GSFT_PRIVACY_STARWORLDFOOTPRINTS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "16"
  tipsInfo: "SettingTip_UGCWorldRecord"
  isShow: true
  order: 65
}
rows {
  id: "65"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "亲密关系特效"
  functionType: GSFT_PRIVACY_INTIMATERELATIONEFFECT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "17"
  isShow: true
  order: 66
}
rows {
  id: "66"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "排行榜信息展示"
  functionType: GSFT_PRIVACY_PERSONALPROFILE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "18"
  tipsInfo: "SettingTip_RankDisplay"
  isShow: true
  order: 67
}
rows {
  id: "67"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "历史战绩"
  functionType: GSFT_PRIVACY_BATTLEHISTORY
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "19"
  isShow: true
  order: 68
}
rows {
  id: "68"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "外观图鉴"
  functionType: GSFT_PRIVACY_SUITBOOK
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "20"
  tipsInfo: "SettingTip_SuitBook"
  isShow: true
  order: 69
}
rows {
  id: "69"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "微信游戏动态"
  functionType: GSFT_PRIVACY_WECHATSHARE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "21"
  tipsInfo: "SettingTip_WeChatGameDynamic"
  isShow: false
  order: 70
}
rows {
  id: "70"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "QQ好友申请"
  functionType: GSFT_PRIVACY_QQFRIENDREQ
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "22"
  isShow: true
  order: 71
}
rows {
  id: "71"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "隐身"
  functionType: GSFT_PRIVACY_INVISIBLE
  layoutType: GSLT_CUSTOMCOMPONENT
  tipsInfo: "该控件和通用控件不同，走自定义控件处理。"
  isShow: true
  order: 72
}
rows {
  id: "72"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "所属社团"
  functionType: GSFT_PRIVACY_CLUBINFO
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "23"
  tipsInfo: "SettingTip_ShowBelongClub"
  isShow: true
  order: 73
}
rows {
  id: "73"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "广场/世界频道聊天"
  functionType: GSFT_PRIVACY_WORLDLOBBYCHAT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "24"
  tipsInfo: "SettingTip_LobbyOrWorldChat"
  isShow: true
  order: 74
}
rows {
  id: "74"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "双人动作需确认"
  functionType: GSFT_PRIVACY_NEEDACTIONCONFIRM
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "25"
  tipsInfo: "SettingTip_DoubleAction"
  isShow: true
  order: 75
}
rows {
  id: "75"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "允许非好友组队"
  functionType: GSFT_PRIVACY_ROOMINVITATION
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "26"
  tipsInfo: "SettingTip_AcceptTeamInvite"
  isShow: true
  order: 76
}
rows {
  id: "76"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "允许预约"
  functionType: GSFT_PRIVACY_RESERVATION
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "27"
  isShow: true
  order: 77
}
rows {
  id: "77"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "展示赛季手册"
  functionType: GSFT_PRIVACY_SEASONFASHIONSHOWSTATUS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "28"
  tipsInfo: "SettingTip_ShowSeasonFashion"
  isShow: true
  order: 78
}
rows {
  id: "78"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "展示组队/对局详情"
  functionType: GSFT_PRIVACY_SHOWROOMEXTRAINFO
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "29"
  tipsInfo: "SettingTip_ShowTeamExtraInfo"
  isShow: true
  order: 79
}
rows {
  id: "79"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "允许好友推荐"
  functionType: GSFT_PRIVACY_ALLOWRECOMMENDFRIENDS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "30"
  tipsInfo: "SettingTip_RecommendFriend"
  isShow: true
  order: 80
}
rows {
  id: "80"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "不被UID搜索到"
  functionType: GSFT_PRIVACY_NOTSEARCHEDBYUID
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "31"
  isShow: true
  order: 81
}
rows {
  id: "81"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "禁止非好友跟随"
  functionType: GSFT_PRIVACY_FORBIDSTRANGERFOLLOW
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "32"
  isShow: true
  order: 82
}
rows {
  id: "82"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "好友索要礼物"
  functionType: GSFT_PRIVACY_DEMANDSWITCH
  layoutType: GSLT_LONGOPTIONCOMPONENT
  layoutID: "6"
  isShow: true
  order: 83
}
rows {
  id: "83"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "对好友展示信息"
  functionType: GSFT_PRIVACY_SHOWPROFILETOFRIEND
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "33"
  tipsInfo: "SettingTip_ToFriendShowInfo"
  isShow: false
  order: 84
}
rows {
  id: "84"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "对陌生人展示信息"
  functionType: GSFT_PRIVACY_SHOWPROFILETOSTRANGER
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "34"
  tipsInfo: "SettingTip_ToStrangerShowInfo"
  isShow: false
  order: 85
}
rows {
  id: "85"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_DISPLAY
  name: "广场昵称显示"
  functionType: GSFT_PRIVACY_SHOWLOBBYNAME
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "35"
  isShow: true
  order: 86
}
rows {
  id: "86"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_DISPLAY
  name: "局内昵称显示"
  functionType: GSFT_PRIVACY_SHOWPLAYINGNAME
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "36"
  isShow: true
  order: 87
}
rows {
  id: "87"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_DISPLAY
  name: "好友私聊顶部提示"
  functionType: GSFT_PRIVACY_LOBBYPRIVATECHAT
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "37"
  isShow: true
  order: 88
}
rows {
  id: "88"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_DISPLAY
  name: "好友在线提醒"
  functionType: GSFT_PRIVACY_FRIENDONLINEREMINDER
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "38"
  isShow: true
  order: 89
}
rows {
  id: "89"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_DISPLAY
  name: "亲密好友上线提醒"
  functionType: GSFT_PRIVACY_INTIMATEONLINENOTICE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "39"
  isShow: true
  order: 90
}
rows {
  id: "90"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_DISPLAY
  name: "农场昵称显示"
  functionType: GSFT_PRIVACY_SHOWFARMNAME
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "40"
  isShow: true
  order: 91
}
rows {
  id: "91"
  tabType: GSTT_OTHERS
  subTabType: GSTT_OTHERS_EXCHANGE
  name: "礼包码兑换"
  functionType: GSTT_OTHERS_GIFTPACKAGECODEEXCHANGE
  layoutType: GSLT_BUTTONCOMPONENT
  layoutID: "1"
  isShow: true
  order: 92
}
rows {
  id: "92"
  tabType: GSTT_OTHERS
  subTabType: GSTT_OTHERS_EXCHANGE
  name: "口令码兑换"
  functionType: GSTT_OTHERS_PASSWORDCODEEXCHANGE
  layoutType: GSLT_BUTTONCOMPONENT
  layoutID: "2"
  isShow: true
  order: 93
}
rows {
  id: "93"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "相册访问权限"
  functionType: GSFT_PRIVACY_PHOTOLIBRARYDESCRIPTION
  layoutType: GSLT_LONGOPTIONCOMPONENT
  layoutID: "6"
  isShow: true
  order: 94
}
rows {
  id: "94"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY_CUSTOM
  name: "隐私权限管理"
  functionType: GSFT_PRIVACY_AUTHORITY
  layoutType: GSLT_BUTTONCOMPONENT
  layoutID: "3"
  isShow: true
  order: 95
  whitePlatformIdList: 0
  whitePlatformIdList: 1
}
rows {
  id: "95"
  tabType: GSTT_PASSWORDMANAGER
  subTabType: GSTT_PASSWORDMANAGER_SECONDPASSWORD
  name: "二级密码"
  functionType: GSTT_PASSWORDMANAGER_SECONDPASSWORDS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "42"
  isShow: true
  order: 96
}
rows {
  id: "96"
  tabType: GSTT_PASSWORDMANAGER
  subTabType: GSTT_PASSWORDMANAGER_SECRETFREE
  name: "免密操作"
  functionType: GSTT_PASSWORDMANAGER_SECRETFREES
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "43"
  isShow: true
  order: 97
}
rows {
  id: "97"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "卡牌信息主界面提醒"
  functionType: GSFT_PRIVACY_CARDRIGHT_NOTICE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "44"
  tipsInfo: "Card_PrivateSetting_Tip"
  isShow: true
  order: 98
}
rows {
  id: "98"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "PC智能施法"
  functionType: GSFT_GAME_PCSMARTCAST
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "7"
  isShow: true
  order: 99
  whitePlatformIdList: 2
}
rows {
  id: "100"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "队友传送键位方案"
  functionType: GSFT_GAME_TELEPORTTYPE
  layoutType: GSLT_UIWIDGET
  layoutID: "1"
  isShow: true
  order: 100
  whitePlatformIdList: 0
  whitePlatformIdList: 1
}
rows {
  id: "101"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_CAMERA
  name: "技巧播报"
  functionType: GSFT_GAME_TECHNIQUEBROADCAST
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "7"
  isShow: true
  order: 101
}
rows {
  id: "102"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "使用道具"
  functionType: GSFT_GAME_USEPROP
  layoutType: GSLT_OPTIONCOMPONENT
  layoutID: "8"
  isShow: true
  order: 102
  whitePlatformIdList: 2
}
rows {
  id: "106"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_OPERATION
  name: "角色装扮描边"
  functionType: GSFT_GAME_CHARACTER_OUTLINE
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "45"
  tipsInfo: "SettingTip_RoleOutline"
  isShow: true
  order: 28
}
rows {
  id: "107"
  tabType: GSTT_PRIVACY
  subTabType: GSTT_PRIVACY_PERSONALPRIVACY
  name: "不向他人展示奖杯信息"
  functionType: GSFT_PRIVACY_HIDE_CUP_INFO
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "46"
  tipsInfo: "SettingTip_CupShow"
  isShow: true
  order: 99
}
rows {
  id: "108"
  tabType: GSTT_GAME_FULLSCENE
  subTabType: GSTT_GAME_FULLSCENE_RECORD
  name: "元梦时刻"
  functionType: GSFT_GAME_LETSGOMOMENTS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "47"
  isShow: true
  order: 1
}
rows {
  id: "109"
  tabType: GSTT_GAME_SINGLESCENE_INSIDE
  subTabType: GSTT_GAME_SINGLESCENE_INSIDE_RECORD
  name: "元梦时刻"
  functionType: GSFT_GAME_LETSGOMOMENTS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "47"
  isShow: true
  order: 1
}
rows {
  id: "110"
  tabType: GSTT_GAME_SINGLESCENE_SOCIALIZE
  subTabType: GSTT_GAME_SINGLESCENE_SOCIALIZE_RECORD
  name: "元梦时刻"
  functionType: GSFT_GAME_LETSGOMOMENTS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "47"
  isShow: true
  order: 1
}
rows {
  id: "111"
  tabType: GSTT_GAME_SINGLESCENE_STARWORLD
  subTabType: GSTT_GAME_SINGLESCENE_STARWORLD_RECORD
  name: "元梦时刻"
  functionType: GSFT_GAME_LETSGOMOMENTS
  layoutType: GSLT_SWITCHCOMPONENT
  layoutID: "47"
  isShow: true
  order: 1
}
rows {
  id: "112"
  tabType: GSTT_OTHERS
  subTabType: GSTT_OTHERS_APPICON
  name: "应用图标设置"
  functionType: GSFT_GAME_CHANGEAPPICON
  layoutType: GSLT_BUTTONCOMPONENT
  layoutID: "4"
  tipsInfo: "App_Icon_Change"
  isShow: true
  order: 107
  whitePlatformIdList: 0
  whitePlatformIdList: 1
}
