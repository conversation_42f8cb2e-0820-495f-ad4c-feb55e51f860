com.tencent.wea.xlsRes.table_UgcMapRankRewardConfData
excel/xls/P_排行榜.xlsx sheet:榜单奖励_UGC地图
rows {
  id: 1
  mapId: 100000
  creatorId: 2000000
  beginTime {
    seconds: 1748016000
  }
  endTime {
    seconds: 1758643200
  }
  rankConf {
    rankId: 300000
    rewardRange {
      priority: 1
      fromIndex: 1
      toIndex: 1
      mailId: 201
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 2
      fromIndex: 2
      toIndex: 2
      mailId: 201
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 3
      fromIndex: 3
      toIndex: 3
      mailId: 201
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
  }
  rankConf {
    rankId: 400000
    rewardRange {
      priority: 1
      fromIndex: 1
      toIndex: 1
      mailId: 202
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 2
      fromIndex: 2
      toIndex: 2
      mailId: 202
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 3
      fromIndex: 3
      toIndex: 3
      mailId: 202
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
  }
  rankConf {
    rankId: 500000
    rewardRange {
      priority: 1
      fromIndex: 1
      toIndex: 1
      mailId: 203
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 2
      fromIndex: 2
      toIndex: 2
      mailId: 203
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 3
      fromIndex: 3
      toIndex: 3
      mailId: 203
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
  }
  rankConf {
    rankId: 600000
    rewardRange {
      priority: 1
      fromIndex: 1
      toIndex: 1
      mailId: 204
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 2
      fromIndex: 2
      toIndex: 2
      mailId: 204
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
    rewardRange {
      priority: 3
      fromIndex: 3
      toIndex: 3
      mailId: 204
      rewardItems {
        itemId: 602
        itemNum: 100
        expireDays: 4
      }
    }
  }
}
