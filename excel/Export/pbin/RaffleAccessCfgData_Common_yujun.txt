com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_yujun.xlsx sheet:活动-赛季金币活跃卡池
rows {
  raffleId: 30008
  name: "赛季祈愿"
  startTime {
    seconds: 1724947200
  }
  endTime {
    seconds: 1729180799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30008
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.18.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1724947200
  }
  showEndTime {
    seconds: 1729180799
  }
  isShow: true
  animType: 4
  relateUmgSetting: "7;UI_Lottery_RewardNotice|DrawReward_FristText;DrawReward_SecondText;405072"
  categoryId: 1
  tagDailyLimit: 500
}
rows {
  raffleId: 30009
  name: "赛季祈愿"
  startTime {
    seconds: 1729180800
  }
  endTime {
    seconds: 1732809599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30009
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.26.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1729180800
  }
  showEndTime {
    seconds: 1732809599
  }
  isShow: true
  animType: 4
  relateUmgSetting: "7;UI_Lottery_RewardNotice|DrawReward_ThridText;;403860"
  categoryId: 1
  tagDailyLimit: 500
}
rows {
  raffleId: 30010
  name: "赛季祈愿"
  startTime {
    seconds: 1732809600
  }
  endTime {
    seconds: 1737043199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30010
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.37.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1732809600
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  animType: 4
  relateUmgSetting: "7;UI_Lottery_RewardNotice|DrawReward_FourthText;;404440"
  categoryId: 1
  tagDailyLimit: 500
}
rows {
  raffleId: 30011
  name: "赛季祈愿"
  startTime {
    seconds: 1737043200
  }
  endTime {
    seconds: 1741881599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30011
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.68.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  showStartTime {
    seconds: 1737043200
  }
  showEndTime {
    seconds: 1741881599
  }
  isShow: true
  animType: 4
  relateUmgSetting: "7;UI_Lottery_RewardNotice|DrawReward_FifthText;;404730"
  categoryId: 1
  tagDailyLimit: 500
}
rows {
  raffleId: 30012
  name: "赛季祈愿"
  startTime {
    seconds: 1741881600
  }
  endTime {
    seconds: 1746115199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30012
  }
  dailyLimit: 30
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.78.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  drawConditions {
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 310315
        value: 201206
      }
    }
  }
  showStartTime {
    seconds: 1741881600
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  animType: 4
  relateUmgSetting: "13;UI_Lottery_LuckyCardBox"
  rewardsForInGroupEnhance {
    array: 3001206
    array: 3001207
  }
  categoryId: 1
  limitForInGroupEnhance: 1
  chooseType: RCT_BeforeDrawnEnhance_BenefitCard
  tagDailyLimit: 500
}
rows {
  raffleId: 30013
  name: "赛季祈愿"
  startTime {
    seconds: 1746115200
  }
  endTime {
    seconds: 1750953599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30013
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.78.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  drawConditions {
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 310325
        value: 201207
      }
    }
  }
  showStartTime {
    seconds: 1746115200
  }
  showEndTime {
    seconds: 1750953599
  }
  isShow: true
  animType: 4
  relateUmgSetting: "13;UI_Lottery_LuckyCardBox"
  rewardsForInGroupEnhance {
    array: 3001306
    array: 3001307
  }
  categoryId: 1
  limitForInGroupEnhance: 1
  chooseType: RCT_BeforeDrawnEnhance_BenefitCard
  tagDailyLimit: 500
}
rows {
  raffleId: 30014
  name: "赛季祈愿"
  startTime {
    seconds: 1750953600
  }
  endTime {
    seconds: 1755791999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 30014
  }
  dailyLimit: 500
  maxLimit: 9999999
  textRuleId: 140
  text: "<DRYellow>每十次</>祈愿必得稀有以上奖励"
  lowestVersion: "1.3.78.1"
  jumpIds: 25
  commodityIds: 120010
  commodityIds: 120011
  showSubPoolProgress: false
  drawConditions {
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 310343
        value: 201208
      }
    }
  }
  showStartTime {
    seconds: 1750953600
  }
  showEndTime {
    seconds: 1755791999
  }
  isShow: true
  animType: 4
  relateUmgSetting: "13;UI_Lottery_LuckyCardBox"
  rewardsForInGroupEnhance {
    array: 3001406
    array: 3001407
  }
  categoryId: 1
  limitForInGroupEnhance: 1
  chooseType: RCT_BeforeDrawnEnhance_BenefitCard
  tagDailyLimit: 500
}
