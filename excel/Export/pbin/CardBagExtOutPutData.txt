com.tencent.wea.xlsRes.table_CardBagExtOutPutConfig
excel/xls/K_卡牌.xlsx sheet:卡包额外产出
rows {
  id: 1
  packId: 290021
  extraPackId: 290031
  extraPackChance: 30
  weight: 100
  addWeight: 1
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1753372799
  }
}
rows {
  id: 2
  packId: 290022
  extraPackId: 290032
  extraPackChance: 40
  weight: 100
  addWeight: 1
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1753372799
  }
}
rows {
  id: 3
  packId: 290023
  extraPackId: 290033
  extraPackChance: 60
  weight: 100
  addWeight: 1
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1753372799
  }
}
rows {
  id: 4
  packId: 290024
  extraPackId: 290034
  extraPackChance: 80
  weight: 100
  addWeight: 1
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1753372799
  }
}
rows {
  id: 5
  packId: 290025
  extraPackId: 290035
  extraPackChance: 100
  weight: 100
  addWeight: 1
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1753372799
  }
}
rows {
  id: 6
  packId: 290021
  extraPackId: 290031
  extraPackId: 290041
  extraPackChance: 30
  weight: 40
  weight: 60
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1754582399
  }
}
rows {
  id: 7
  packId: 290022
  extraPackId: 290032
  extraPackId: 290042
  extraPackChance: 40
  weight: 40
  weight: 60
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1754582399
  }
}
rows {
  id: 8
  packId: 290023
  extraPackId: 290033
  extraPackId: 290043
  extraPackChance: 60
  weight: 40
  weight: 60
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1754582399
  }
}
rows {
  id: 9
  packId: 290024
  extraPackId: 290034
  extraPackId: 290044
  extraPackChance: 80
  weight: 40
  weight: 60
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1754582399
  }
}
rows {
  id: 10
  packId: 290025
  extraPackId: 290035
  extraPackId: 290045
  extraPackChance: 100
  weight: 40
  weight: 60
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1754582399
  }
}
rows {
  id: 11
  packId: 290021
  extraPackId: 290031
  extraPackId: 290041
  extraPackId: 290051
  extraPackChance: 30
  weight: 15
  weight: 15
  weight: 70
  addWeight: 5
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755791999
  }
}
rows {
  id: 12
  packId: 290022
  extraPackId: 290032
  extraPackId: 290042
  extraPackId: 290052
  extraPackChance: 40
  weight: 15
  weight: 15
  weight: 70
  addWeight: 5
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755791999
  }
}
rows {
  id: 13
  packId: 290023
  extraPackId: 290033
  extraPackId: 290043
  extraPackId: 290053
  extraPackChance: 60
  weight: 15
  weight: 15
  weight: 70
  addWeight: 5
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755791999
  }
}
rows {
  id: 14
  packId: 290024
  extraPackId: 290034
  extraPackId: 290044
  extraPackId: 290054
  extraPackChance: 80
  weight: 15
  weight: 15
  weight: 70
  addWeight: 5
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755791999
  }
}
rows {
  id: 15
  packId: 290025
  extraPackId: 290035
  extraPackId: 290045
  extraPackId: 290055
  extraPackChance: 100
  weight: 15
  weight: 15
  weight: 70
  addWeight: 5
  addWeight: 5
  addWeight: 5
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755791999
  }
}
