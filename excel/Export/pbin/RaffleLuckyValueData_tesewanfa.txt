com.tencent.wea.xlsRes.table_RaffleLuckyValueData
excel/xls/C_抽奖奖池_特色玩法.xlsx sheet:幸运值配置
rows {
  id: 9001001
  poolId: 9001
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9001002
  poolId: 9001
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9001003
  poolId: 9001
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9001004
  poolId: 9001
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9001005
  poolId: 9001
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 9002001
  poolId: 9002
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9002002
  poolId: 9002
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9002003
  poolId: 9002
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9002004
  poolId: 9002
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9002005
  poolId: 9002
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 10000001
  poolId: 10000001
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 10000002
  poolId: 10000001
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 10000003
  poolId: 10000001
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 10000004
  poolId: 10000001
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 10000005
  poolId: 10000001
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 10000006
  poolId: 13000000
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 10000007
  poolId: 13000000
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 10000008
  poolId: 13000000
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 10000009
  poolId: 13000001
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 10000010
  poolId: 13000002
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 10000011
  poolId: 13000003
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 10000012
  poolId: 13000004
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 10000013
  poolId: 13000005
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 10000014
  poolId: 13000005
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 10000015
  poolId: 13000005
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 10000016
  poolId: 13000006
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 10000017
  poolId: 13000007
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 10000018
  poolId: 13000008
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 10000019
  poolId: 13000009
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 10000020
  poolId: 13000010
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 10000021
  poolId: 13000011
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 10000022
  poolId: 13000012
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 10000023
  poolId: 13000013
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 10000024
  poolId: 13000014
  lvThreshold: 7
  groupWeight: 90
  groupWeight: 10
}
rows {
  id: 10000025
  poolId: 13000015
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 9003001
  poolId: 9003
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9003002
  poolId: 9003
  lvThreshold: 6
  groupWeight: 3
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9003003
  poolId: 9003
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9003004
  poolId: 9003
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9003005
  poolId: 9003
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 9004001
  poolId: 9004
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9004002
  poolId: 9004
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 9004003
  poolId: 9004
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9004004
  poolId: 9004
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 9004005
  poolId: 9004
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 14001001
  poolId: 10001001
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 14001002
  poolId: 10001002
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 14001003
  poolId: 10001002
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 1200
}
rows {
  id: 14001004
  poolId: 10001003
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 14001005
  poolId: 10001003
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 14001006
  poolId: 10001003
  lvThreshold: 7
  groupWeight: 15000
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 600010101
  poolId: 6000101
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010102
  poolId: 6000101
  lvThreshold: 40
  groupWeight: 10
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010103
  poolId: 6000101
  lvThreshold: 70
  groupWeight: 20
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010104
  poolId: 6000101
  lvThreshold: 110
  groupWeight: 30
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010105
  poolId: 6000101
  lvThreshold: 140
  groupWeight: 50
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 130001201
  poolId: 13000120
  lvThreshold: 2
  groupWeight: 1000
  groupWeight: 700
  groupWeight: 1
}
rows {
  id: 130001202
  poolId: 13000120
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 130001203
  poolId: 13000121
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 1
}
rows {
  id: 130001204
  poolId: 13000121
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 10
}
rows {
  id: 130001205
  poolId: 13000121
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130001206
  poolId: 13000122
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 1
}
rows {
  id: 130001207
  poolId: 13000122
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 10
}
rows {
  id: 130001208
  poolId: 13000122
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130001209
  poolId: 13000123
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 1
}
rows {
  id: 130001210
  poolId: 13000123
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 10
}
rows {
  id: 130001211
  poolId: 13000123
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130001212
  poolId: 13000130
  lvThreshold: 2
  groupWeight: 1000
  groupWeight: 700
  groupWeight: 1
}
rows {
  id: 130001213
  poolId: 13000130
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 130001214
  poolId: 13000131
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 1
}
rows {
  id: 130001215
  poolId: 13000131
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 10
}
rows {
  id: 130001216
  poolId: 13000131
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130001217
  poolId: 13000132
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 1
}
rows {
  id: 130001218
  poolId: 13000132
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 10
}
rows {
  id: 130001219
  poolId: 13000132
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130001220
  poolId: 13000133
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 1
}
rows {
  id: 130001221
  poolId: 13000133
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 10
}
rows {
  id: 130001222
  poolId: 13000133
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 10000026
  poolId: 10000005
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 10000027
  poolId: 10000005
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 10000028
  poolId: 10000005
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 10000029
  poolId: 10000005
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 10000030
  poolId: 10000005
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600010201
  poolId: 6000102
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010202
  poolId: 6000102
  lvThreshold: 40
  groupWeight: 10
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010203
  poolId: 6000102
  lvThreshold: 70
  groupWeight: 20
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010204
  poolId: 6000102
  lvThreshold: 110
  groupWeight: 30
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010205
  poolId: 6000102
  lvThreshold: 140
  groupWeight: 50
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 10000031
  poolId: 10000006
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 10000032
  poolId: 10000006
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 10000033
  poolId: 10000006
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 10000034
  poolId: 10000006
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 10000035
  poolId: 10000006
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 1000100701
  poolId: 10001007
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 1000100702
  poolId: 10001007
  lvThreshold: 6
  groupWeight: 3
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 1000100703
  poolId: 10001007
  lvThreshold: 7
  groupWeight: 200
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 1000100704
  poolId: 10001007
  lvThreshold: 8
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 1000100705
  poolId: 10001007
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 1000100706
  poolId: 10001008
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 1000100707
  poolId: 10001008
  lvThreshold: 6
  groupWeight: 3
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 1000100708
  poolId: 10001008
  lvThreshold: 7
  groupWeight: 200
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 1000100709
  poolId: 10001008
  lvThreshold: 8
  groupWeight: 400
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 1000100710
  poolId: 10001008
  lvThreshold: 9
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
  groupWeight: 8000
}
rows {
  id: 60001031
  poolId: 60001031
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001032
  poolId: 60001031
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001033
  poolId: 60001031
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001034
  poolId: 60001031
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001035
  poolId: 60001031
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001036
  poolId: 60001031
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001037
  poolId: 60001032
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001038
  poolId: 60001032
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001039
  poolId: 60001032
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001040
  poolId: 60001032
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001041
  poolId: 60001032
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001042
  poolId: 60001032
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 130017301
  poolId: 13000173
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130017302
  poolId: 13000174
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130017303
  poolId: 13000174
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130017304
  poolId: 13000175
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 2000
  groupWeight: 500
}
rows {
  id: 130017305
  poolId: 13000175
  lvThreshold: 5
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130017306
  poolId: 13000176
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 3000
  groupWeight: 1000
}
rows {
  id: 130017307
  poolId: 13000176
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 2000
  groupWeight: 500
}
rows {
  id: 130017308
  poolId: 13000176
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130017309
  poolId: 13000177
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130017310
  poolId: 13000178
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130017311
  poolId: 13000178
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130017312
  poolId: 13000179
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 2000
  groupWeight: 500
}
rows {
  id: 130017313
  poolId: 13000179
  lvThreshold: 5
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130017314
  poolId: 13000180
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 3000
  groupWeight: 1000
}
rows {
  id: 130017315
  poolId: 13000180
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 2000
  groupWeight: 500
}
rows {
  id: 130017316
  poolId: 13000180
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 600010401
  poolId: 6000104
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010402
  poolId: 6000104
  lvThreshold: 40
  groupWeight: 10
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010403
  poolId: 6000104
  lvThreshold: 70
  groupWeight: 20
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010404
  poolId: 6000104
  lvThreshold: 110
  groupWeight: 30
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010405
  poolId: 6000104
  lvThreshold: 140
  groupWeight: 50
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 130001601
  poolId: 13000160
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130001602
  poolId: 13000160
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 9500
  groupWeight: 10
}
rows {
  id: 130001603
  poolId: 13000160
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 15
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 500
  groupWeight: 15
}
rows {
  id: 130001604
  poolId: 13000160
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 25
  groupWeight: 9500
  groupWeight: 25
  groupWeight: 25
  groupWeight: 25
}
rows {
  id: 130001605
  poolId: 13000160
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
}
rows {
  id: 130001606
  poolId: 13000160
  lvThreshold: 12
  groupWeight: 9500
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
}
rows {
  id: 130001607
  poolId: 13000161
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130001608
  poolId: 13000162
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130001609
  poolId: 13000163
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130001610
  poolId: 13000164
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130001611
  poolId: 13000165
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130001612
  poolId: 13000166
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130001613
  poolId: 13000167
  lvThreshold: 10
  groupWeight: 495
  groupWeight: 5
}
rows {
  id: 130001614
  poolId: 13000168
  lvThreshold: 10
  groupWeight: 495
  groupWeight: 5
}
rows {
  id: 130001615
  poolId: 13000169
  lvThreshold: 10
  groupWeight: 495
  groupWeight: 5
}
rows {
  id: 130001616
  poolId: 13000170
  lvThreshold: 10
  groupWeight: 995
  groupWeight: 5
}
rows {
  id: 130001617
  poolId: 13000171
  lvThreshold: 10
  groupWeight: 995
  groupWeight: 5
}
rows {
  id: 130001618
  poolId: 13000172
  lvThreshold: 10
  groupWeight: 995
  groupWeight: 5
}
rows {
  id: 130001619
  poolId: 13000020
  lvThreshold: 1
  groupWeight: 50
  groupWeight: 20000
}
rows {
  id: 130001620
  poolId: 13000020
  lvThreshold: 3
  groupWeight: 200
  groupWeight: 20000
}
rows {
  id: 130001621
  poolId: 13000021
  lvThreshold: 8
  groupWeight: 25
  groupWeight: 5
}
rows {
  id: 130001622
  poolId: 13000022
  lvThreshold: 8
  groupWeight: 35
  groupWeight: 5
}
rows {
  id: 130001623
  poolId: 13000023
  lvThreshold: 4
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130001624
  poolId: 13000023
  lvThreshold: 8
  groupWeight: 45
  groupWeight: 55
}
rows {
  id: 10000091
  poolId: 10000009
  lvThreshold: 3
  groupWeight: 1500
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 50
  groupWeight: 2000
}
rows {
  id: 10000092
  poolId: 10000009
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 2500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 10000093
  poolId: 10000009
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 10000094
  poolId: 10000009
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 10000095
  poolId: 10000009
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 130001901
  poolId: 13000190
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130001902
  poolId: 13000190
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 9500
  groupWeight: 10
}
rows {
  id: 130001903
  poolId: 13000190
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 15
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 500
  groupWeight: 15
}
rows {
  id: 130001904
  poolId: 13000190
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 25
  groupWeight: 9500
  groupWeight: 25
  groupWeight: 25
  groupWeight: 25
}
rows {
  id: 130001905
  poolId: 13000190
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
}
rows {
  id: 130001906
  poolId: 13000190
  lvThreshold: 12
  groupWeight: 9500
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
}
rows {
  id: 130001907
  poolId: 13000191
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130001908
  poolId: 13000192
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130001909
  poolId: 13000193
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130001910
  poolId: 13000194
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130001911
  poolId: 13000195
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130001912
  poolId: 13000196
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130001913
  poolId: 13000197
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130001914
  poolId: 13000198
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130001915
  poolId: 13000199
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130001916
  poolId: 13000200
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130001917
  poolId: 13000201
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130001918
  poolId: 13000202
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130002101
  poolId: 13000210
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130002102
  poolId: 13000210
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 130002103
  poolId: 13000210
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 130002104
  poolId: 13000211
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002105
  poolId: 13000212
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002106
  poolId: 13000213
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002107
  poolId: 13000214
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002201
  poolId: 13000220
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130002202
  poolId: 13000220
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 130002203
  poolId: 13000220
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 130002204
  poolId: 13000221
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002205
  poolId: 13000222
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002206
  poolId: 13000223
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002207
  poolId: 13000224
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002901
  poolId: 13000290
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130002902
  poolId: 13000290
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 130002903
  poolId: 13000290
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 130002904
  poolId: 13000291
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002905
  poolId: 13000292
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002906
  poolId: 13000293
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002907
  poolId: 13000294
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130003001
  poolId: 13000300
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130003002
  poolId: 13000300
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 99
}
rows {
  id: 130003003
  poolId: 13000300
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 98
}
rows {
  id: 130003004
  poolId: 13000301
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130003005
  poolId: 13000302
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130003006
  poolId: 13000303
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130003007
  poolId: 13000304
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130002301
  poolId: 13000230
  lvThreshold: 2
  groupWeight: 1000
  groupWeight: 700
  groupWeight: 1
}
rows {
  id: 130002302
  poolId: 13000230
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 130002303
  poolId: 13000231
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 10
}
rows {
  id: 130002304
  poolId: 13000231
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 50
}
rows {
  id: 130002305
  poolId: 13000231
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130002306
  poolId: 13000232
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 30
}
rows {
  id: 130002307
  poolId: 13000232
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 60
}
rows {
  id: 130002308
  poolId: 13000232
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130002309
  poolId: 13000233
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 20
}
rows {
  id: 130002310
  poolId: 13000233
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 100
}
rows {
  id: 130002311
  poolId: 13000233
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130002312
  poolId: 13000240
  lvThreshold: 2
  groupWeight: 1000
  groupWeight: 700
  groupWeight: 1
}
rows {
  id: 130002313
  poolId: 13000240
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 130002314
  poolId: 13000241
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 10
}
rows {
  id: 130002315
  poolId: 13000241
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 50
}
rows {
  id: 130002316
  poolId: 13000241
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130002317
  poolId: 13000242
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 30
}
rows {
  id: 130002318
  poolId: 13000242
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 60
}
rows {
  id: 130002319
  poolId: 13000242
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 130002320
  poolId: 13000243
  lvThreshold: 4
  groupWeight: 1000
  groupWeight: 600
  groupWeight: 50
}
rows {
  id: 130002321
  poolId: 13000243
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
  groupWeight: 100
}
rows {
  id: 130002322
  poolId: 13000243
  lvThreshold: 7
  groupWeight: 1
  groupWeight: 1
  groupWeight: 3000
}
rows {
  id: 100000101
  poolId: 10000010
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000102
  poolId: 10000011
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000103
  poolId: 10000011
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 1200
}
rows {
  id: 100000104
  poolId: 10000012
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000105
  poolId: 10000012
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000106
  poolId: 10000012
  lvThreshold: 7
  groupWeight: 15000
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 130002501
  poolId: 13000250
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130002502
  poolId: 13000250
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130002503
  poolId: 13000251
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130002504
  poolId: 13000251
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130002505
  poolId: 13000251
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130002506
  poolId: 13000252
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130002507
  poolId: 13000252
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130002508
  poolId: 13000252
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130002509
  poolId: 13000253
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130002510
  poolId: 13000253
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130002511
  poolId: 13000253
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130002601
  poolId: 13000260
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130002602
  poolId: 13000260
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130002603
  poolId: 13000261
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130002604
  poolId: 13000261
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130002605
  poolId: 13000261
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130002606
  poolId: 13000262
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130002607
  poolId: 13000262
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130002608
  poolId: 13000262
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130002609
  poolId: 13000263
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130002610
  poolId: 13000263
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130002611
  poolId: 13000263
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 600010501
  poolId: 6000105
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 60
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010502
  poolId: 6000105
  lvThreshold: 40
  groupWeight: 10
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010503
  poolId: 6000105
  lvThreshold: 70
  groupWeight: 20
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010504
  poolId: 6000105
  lvThreshold: 110
  groupWeight: 30
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010505
  poolId: 6000105
  lvThreshold: 140
  groupWeight: 50
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 130002701
  poolId: 13000270
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130002702
  poolId: 13000270
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 9500
  groupWeight: 10
}
rows {
  id: 130002703
  poolId: 13000270
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 15
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 500
  groupWeight: 15
}
rows {
  id: 130002704
  poolId: 13000270
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 25
  groupWeight: 9500
  groupWeight: 25
  groupWeight: 25
  groupWeight: 25
}
rows {
  id: 130002705
  poolId: 13000270
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
}
rows {
  id: 130002706
  poolId: 13000270
  lvThreshold: 12
  groupWeight: 9500
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
}
rows {
  id: 130002707
  poolId: 13000271
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130002708
  poolId: 13000272
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130002709
  poolId: 13000273
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130002710
  poolId: 13000274
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130002711
  poolId: 13000275
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130002712
  poolId: 13000276
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130002713
  poolId: 13000277
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130002714
  poolId: 13000278
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130002715
  poolId: 13000279
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130002716
  poolId: 13000280
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130002717
  poolId: 13000281
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130002718
  poolId: 13000282
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 100000131
  poolId: 10000013
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000132
  poolId: 10000014
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000133
  poolId: 10000014
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 1200
}
rows {
  id: 100000134
  poolId: 10000015
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000135
  poolId: 10000015
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000136
  poolId: 10000015
  lvThreshold: 7
  groupWeight: 15000
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100001201
  poolId: 1000012
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100001202
  poolId: 1000012
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100001203
  poolId: 1000012
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100001301
  poolId: 1000013
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100001302
  poolId: 1000013
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100001303
  poolId: 1000013
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100001401
  poolId: 1000014
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100001402
  poolId: 1000014
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100001403
  poolId: 1000014
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100001501
  poolId: 1000015
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100001502
  poolId: 1000015
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100001503
  poolId: 1000015
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100000161
  poolId: 10000016
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000162
  poolId: 10000017
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000163
  poolId: 10000017
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 1200
}
rows {
  id: 100000164
  poolId: 10000018
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000165
  poolId: 10000018
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000166
  poolId: 10000018
  lvThreshold: 7
  groupWeight: 15000
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 60001061
  poolId: 60001061
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001062
  poolId: 60001061
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001063
  poolId: 60001061
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001064
  poolId: 60001061
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001065
  poolId: 60001061
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001066
  poolId: 60001061
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001067
  poolId: 60001062
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001068
  poolId: 60001062
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001069
  poolId: 60001062
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001070
  poolId: 60001062
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001071
  poolId: 60001062
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001072
  poolId: 60001062
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100000191
  poolId: 10000021
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 50
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100000192
  poolId: 10000021
  lvThreshold: 4
  groupWeight: 2
  groupWeight: 10
  groupWeight: 20000
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100000193
  poolId: 10000021
  lvThreshold: 8
  groupWeight: 3
  groupWeight: 20000
  groupWeight: 20000
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100000194
  poolId: 10000021
  lvThreshold: 9
  groupWeight: 2000
  groupWeight: 20000
  groupWeight: 20000
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100000195
  poolId: 10000021
  lvThreshold: 10
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001011
  poolId: 70001011
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001012
  poolId: 70001011
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001013
  poolId: 70001011
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001014
  poolId: 70001011
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 70001015
  poolId: 70001011
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 70001016
  poolId: 70001011
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001017
  poolId: 70001012
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001018
  poolId: 70001012
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001019
  poolId: 70001012
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 70001020
  poolId: 70001012
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 70001021
  poolId: 70001012
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 70001022
  poolId: 70001012
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600010701
  poolId: 6000107
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 60
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010702
  poolId: 6000107
  lvThreshold: 40
  groupWeight: 10
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010703
  poolId: 6000107
  lvThreshold: 70
  groupWeight: 20
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010704
  poolId: 6000107
  lvThreshold: 110
  groupWeight: 30
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600010705
  poolId: 6000107
  lvThreshold: 140
  groupWeight: 50
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 130003101
  poolId: 13000310
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130003102
  poolId: 13000310
  lvThreshold: 3
  groupWeight: 999
  groupWeight: 1
}
rows {
  id: 130003103
  poolId: 13000311
  lvThreshold: 3
  groupWeight: 1
}
rows {
  id: 130003104
  poolId: 13000312
  lvThreshold: 5
  groupWeight: 1
}
rows {
  id: 130003105
  poolId: 13000313
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130003201
  poolId: 13000320
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130003202
  poolId: 13000320
  lvThreshold: 3
  groupWeight: 999
  groupWeight: 1
}
rows {
  id: 130003203
  poolId: 13000321
  lvThreshold: 3
  groupWeight: 1
}
rows {
  id: 130003204
  poolId: 13000322
  lvThreshold: 5
  groupWeight: 1
}
rows {
  id: 130003205
  poolId: 13000323
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130003301
  poolId: 13000330
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130003302
  poolId: 13000330
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 300
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130003303
  poolId: 13000330
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 200
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130003304
  poolId: 13000330
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130003305
  poolId: 13000330
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130003306
  poolId: 13000331
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003307
  poolId: 13000332
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003308
  poolId: 13000333
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003309
  poolId: 13000334
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003310
  poolId: 13000335
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003311
  poolId: 13000336
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003312
  poolId: 13000337
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130003313
  poolId: 13000338
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130003314
  poolId: 13000339
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130003315
  poolId: 13000340
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 100000221
  poolId: 10000019
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000222
  poolId: 10000020
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000223
  poolId: 10000020
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 1200
}
rows {
  id: 100000224
  poolId: 10000022
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000225
  poolId: 10000022
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000226
  poolId: 10000022
  lvThreshold: 7
  groupWeight: 15000
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 130003501
  poolId: 13000350
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130003502
  poolId: 13000350
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003503
  poolId: 13000351
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130003504
  poolId: 13000351
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130003505
  poolId: 13000351
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003506
  poolId: 13000352
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130003507
  poolId: 13000352
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130003508
  poolId: 13000352
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003509
  poolId: 13000353
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130003510
  poolId: 13000353
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130003511
  poolId: 13000353
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003601
  poolId: 13000360
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130003602
  poolId: 13000360
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003603
  poolId: 13000361
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130003604
  poolId: 13000361
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130003605
  poolId: 13000361
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003606
  poolId: 13000362
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130003607
  poolId: 13000362
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130003608
  poolId: 13000362
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003609
  poolId: 13000363
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130003610
  poolId: 13000363
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130003611
  poolId: 13000363
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100000231
  poolId: 10000023
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 200
  groupWeight: 1800
  groupWeight: 2000
  groupWeight: 2000
}
rows {
  id: 100000232
  poolId: 10000023
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1800
  groupWeight: 2000
  groupWeight: 2000
  groupWeight: 2000
}
rows {
  id: 100000233
  poolId: 10000023
  lvThreshold: 7
  groupWeight: 300
  groupWeight: 1800
  groupWeight: 2000
  groupWeight: 2000
  groupWeight: 2000
}
rows {
  id: 100000234
  poolId: 10000023
  lvThreshold: 8
  groupWeight: 800
  groupWeight: 1800
  groupWeight: 2000
  groupWeight: 2000
  groupWeight: 2000
}
rows {
  id: 100000235
  poolId: 10000023
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 2000
  groupWeight: 2000
}
rows {
  id: 600011001
  poolId: 6000110
  lvThreshold: 10
  groupWeight: 1
  groupWeight: 32
  groupWeight: 77
  groupWeight: 17
  groupWeight: 200
  groupWeight: 100
  groupWeight: 390
}
rows {
  id: 600011002
  poolId: 6000110
  lvThreshold: 20
  groupWeight: 1
  groupWeight: 32
  groupWeight: 77
  groupWeight: 17
  groupWeight: 200
  groupWeight: 100
  groupWeight: 390
}
rows {
  id: 600011003
  poolId: 6000110
  lvThreshold: 30
  groupWeight: 1
  groupWeight: 32
  groupWeight: 77
  groupWeight: 17
  groupWeight: 200
  groupWeight: 100
  groupWeight: 390
}
rows {
  id: 600011004
  poolId: 6000110
  lvThreshold: 40
  groupWeight: 1
  groupWeight: 32
  groupWeight: 77
  groupWeight: 17
  groupWeight: 200
  groupWeight: 100
  groupWeight: 390
}
rows {
  id: 600011005
  poolId: 6000110
  lvThreshold: 50
  groupWeight: 2
  groupWeight: 32
  groupWeight: 77
  groupWeight: 17
  groupWeight: 200
  groupWeight: 100
  groupWeight: 390
}
rows {
  id: 10000201
  poolId: 1000020
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
}
rows {
  id: 10000202
  poolId: 1000020
  lvThreshold: 6
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
}
rows {
  id: 10000203
  poolId: 1000020
  lvThreshold: 7
  groupWeight: 19000
  groupWeight: 6500
  groupWeight: 1300
  groupWeight: 2500
}
rows {
  id: 60001111
  poolId: 60001111
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001112
  poolId: 60001111
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001113
  poolId: 60001111
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001114
  poolId: 60001111
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001115
  poolId: 60001111
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001116
  poolId: 60001111
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001117
  poolId: 60001112
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001118
  poolId: 60001112
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001119
  poolId: 60001112
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001120
  poolId: 60001112
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001121
  poolId: 60001112
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001122
  poolId: 60001112
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100002101
  poolId: 1000021
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100002102
  poolId: 1000021
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100002103
  poolId: 1000021
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100002201
  poolId: 1000022
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100002202
  poolId: 1000022
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100002203
  poolId: 1000022
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100002301
  poolId: 1000023
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100002302
  poolId: 1000023
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100002303
  poolId: 1000023
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100002401
  poolId: 1000024
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100002402
  poolId: 1000024
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100002403
  poolId: 1000024
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130003701
  poolId: 13000370
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130003702
  poolId: 13000370
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 9500
  groupWeight: 10
}
rows {
  id: 130003703
  poolId: 13000370
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 15
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 500
  groupWeight: 15
}
rows {
  id: 130003704
  poolId: 13000370
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 25
  groupWeight: 9500
  groupWeight: 25
  groupWeight: 25
  groupWeight: 25
}
rows {
  id: 130003705
  poolId: 13000370
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
}
rows {
  id: 130003706
  poolId: 13000370
  lvThreshold: 12
  groupWeight: 9500
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
}
rows {
  id: 130003707
  poolId: 13000371
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003708
  poolId: 13000372
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003709
  poolId: 13000373
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003710
  poolId: 13000374
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003711
  poolId: 13000375
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003712
  poolId: 13000376
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003713
  poolId: 13000377
  lvThreshold: 10
  groupWeight: 495
  groupWeight: 5
}
rows {
  id: 130003714
  poolId: 13000378
  lvThreshold: 10
  groupWeight: 495
  groupWeight: 5
}
rows {
  id: 130003715
  poolId: 13000379
  lvThreshold: 10
  groupWeight: 495
  groupWeight: 5
}
rows {
  id: 130003716
  poolId: 13000380
  lvThreshold: 10
  groupWeight: 995
  groupWeight: 5
}
rows {
  id: 130003717
  poolId: 13000381
  lvThreshold: 10
  groupWeight: 995
  groupWeight: 5
}
rows {
  id: 130003718
  poolId: 13000382
  lvThreshold: 10
  groupWeight: 995
  groupWeight: 5
}
rows {
  id: 130003901
  poolId: 13000390
  lvThreshold: 4
  groupWeight: 1
  groupWeight: 50
  groupWeight: 50
  groupWeight: 9500
}
rows {
  id: 130003902
  poolId: 13000390
  lvThreshold: 6
  groupWeight: 5
  groupWeight: 100
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130003903
  poolId: 13000390
  lvThreshold: 8
  groupWeight: 100
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130003904
  poolId: 13000390
  lvThreshold: 10
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130003905
  poolId: 13000391
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003906
  poolId: 13000392
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003907
  poolId: 13000393
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130003908
  poolId: 13000394
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003909
  poolId: 13000395
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003910
  poolId: 13000396
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130003911
  poolId: 13000397
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130003912
  poolId: 13000398
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130003913
  poolId: 13000399
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130003914
  poolId: 13000400
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 600011201
  poolId: 6000112
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 60
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011202
  poolId: 6000112
  lvThreshold: 40
  groupWeight: 10
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011203
  poolId: 6000112
  lvThreshold: 70
  groupWeight: 20
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011204
  poolId: 6000112
  lvThreshold: 110
  groupWeight: 30
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011205
  poolId: 6000112
  lvThreshold: 140
  groupWeight: 50
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011206
  poolId: 10000025
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600011207
  poolId: 10000025
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600011208
  poolId: 10000025
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 600011209
  poolId: 10000025
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 600011210
  poolId: 10000025
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600011211
  poolId: 10000026
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600011212
  poolId: 10000026
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600011213
  poolId: 10000026
  lvThreshold: 7
  groupWeight: 4000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 600011214
  poolId: 10000026
  lvThreshold: 8
  groupWeight: 10000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 600011215
  poolId: 10000026
  lvThreshold: 9
  groupWeight: 1000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 130004101
  poolId: 13000410
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130004102
  poolId: 13000410
  lvThreshold: 3
  groupWeight: 99
  groupWeight: 1
}
rows {
  id: 130004103
  poolId: 13000411
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130004104
  poolId: 13000412
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130004105
  poolId: 13000413
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130004106
  poolId: 13000420
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130004107
  poolId: 13000420
  lvThreshold: 3
  groupWeight: 99
  groupWeight: 1
}
rows {
  id: 130004108
  poolId: 13000421
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130004109
  poolId: 13000422
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130004110
  poolId: 13000423
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130004301
  poolId: 13000430
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130004302
  poolId: 13000430
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 300
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130004303
  poolId: 13000430
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 200
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130004304
  poolId: 13000430
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130004305
  poolId: 13000430
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130004306
  poolId: 13000431
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004307
  poolId: 13000432
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004308
  poolId: 13000433
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004309
  poolId: 13000434
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004310
  poolId: 13000435
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130004311
  poolId: 13000436
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130004312
  poolId: 13000437
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004313
  poolId: 13000438
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004314
  poolId: 13000439
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004315
  poolId: 13000440
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 100002601
  poolId: 1000026
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100002602
  poolId: 1000026
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100002603
  poolId: 1000026
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100002701
  poolId: 1000027
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100002702
  poolId: 1000027
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100002703
  poolId: 1000027
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 60001131
  poolId: 60001131
  lvThreshold: 2
  groupWeight: 2
  groupWeight: 20
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001132
  poolId: 60001131
  lvThreshold: 3
  groupWeight: 5
  groupWeight: 300
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001133
  poolId: 60001131
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 800
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001134
  poolId: 60001131
  lvThreshold: 5
  groupWeight: 200
  groupWeight: 1000
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001135
  poolId: 60001131
  lvThreshold: 6
  groupWeight: 15000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001136
  poolId: 60001132
  lvThreshold: 2
  groupWeight: 2
  groupWeight: 20
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001137
  poolId: 60001132
  lvThreshold: 3
  groupWeight: 5
  groupWeight: 300
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001138
  poolId: 60001132
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 800
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001139
  poolId: 60001132
  lvThreshold: 5
  groupWeight: 200
  groupWeight: 1000
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 60001140
  poolId: 60001132
  lvThreshold: 6
  groupWeight: 15000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 6000
}
rows {
  id: 10000271
  poolId: 10000028
  lvThreshold: 3
  groupWeight: 1500
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 50
  groupWeight: 2000
}
rows {
  id: 10000272
  poolId: 10000028
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 2500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 10000273
  poolId: 10000028
  lvThreshold: 7
  groupWeight: 50
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 10000274
  poolId: 10000028
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 10000275
  poolId: 10000028
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 100000291
  poolId: 10000029
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 4000
  groupWeight: 3000
  groupWeight: 3000
}
rows {
  id: 100000292
  poolId: 10000029
  lvThreshold: 3
  groupWeight: 2
  groupWeight: 50
  groupWeight: 20000
  groupWeight: 3000
  groupWeight: 3000
}
rows {
  id: 100000293
  poolId: 10000029
  lvThreshold: 4
  groupWeight: 3
  groupWeight: 3000
  groupWeight: 20000
  groupWeight: 3000
  groupWeight: 3000
}
rows {
  id: 100000294
  poolId: 10000029
  lvThreshold: 9
  groupWeight: 50
  groupWeight: 20000
  groupWeight: 20000
  groupWeight: 3000
  groupWeight: 3000
}
rows {
  id: 100000295
  poolId: 10000029
  lvThreshold: 10
  groupWeight: 15000
  groupWeight: 3000
  groupWeight: 3000
  groupWeight: 3000
  groupWeight: 3000
}
rows {
  id: 130004501
  poolId: 13000450
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130004502
  poolId: 13000450
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004503
  poolId: 13000451
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130004504
  poolId: 13000451
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130004505
  poolId: 13000451
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004506
  poolId: 13000452
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130004507
  poolId: 13000452
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130004508
  poolId: 13000452
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004509
  poolId: 13000453
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130004510
  poolId: 13000453
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130004511
  poolId: 13000453
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004601
  poolId: 13000460
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130004602
  poolId: 13000460
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004603
  poolId: 13000461
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130004604
  poolId: 13000461
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130004605
  poolId: 13000461
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004606
  poolId: 13000462
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130004607
  poolId: 13000462
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130004608
  poolId: 13000462
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004609
  poolId: 13000463
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130004610
  poolId: 13000463
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130004611
  poolId: 13000463
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100000301
  poolId: 10000030
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 100000302
  poolId: 10000031
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 100000303
  poolId: 10000031
  lvThreshold: 4
  groupWeight: 80
  groupWeight: 1000
}
rows {
  id: 100000304
  poolId: 10000031
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
}
rows {
  id: 100000305
  poolId: 10000032
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 4000
  groupWeight: 12000
}
rows {
  id: 100000306
  poolId: 10000032
  lvThreshold: 7
  groupWeight: 80
  groupWeight: 4000
  groupWeight: 12000
}
rows {
  id: 100000307
  poolId: 10000032
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 4000
  groupWeight: 20000
}
rows {
  id: 100003101
  poolId: 1000031
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100003102
  poolId: 1000031
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100003103
  poolId: 1000031
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130004701
  poolId: 13000470
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 100
  groupWeight: 20000
}
rows {
  id: 130004702
  poolId: 13000470
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 20000
  groupWeight: 500
}
rows {
  id: 130004703
  poolId: 13000470
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 20000
  groupWeight: 20000
}
rows {
  id: 130004704
  poolId: 13000471
  lvThreshold: 6
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004705
  poolId: 13000472
  lvThreshold: 8
  groupWeight: 105
  groupWeight: 5
}
rows {
  id: 130004706
  poolId: 13000473
  lvThreshold: 12
  groupWeight: 135
  groupWeight: 5
}
rows {
  id: 130004707
  poolId: 13000474
  lvThreshold: 16
  groupWeight: 135
  groupWeight: 5
}
rows {
  id: 130004708
  poolId: 13000475
  lvThreshold: 20
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004709
  poolId: 13000476
  lvThreshold: 30
  groupWeight: 895
  groupWeight: 5
}
rows {
  id: 100000331
  poolId: 10000033
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000332
  poolId: 10000034
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000333
  poolId: 10000034
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 1200
}
rows {
  id: 100000334
  poolId: 10000035
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000335
  poolId: 10000035
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000336
  poolId: 10000035
  lvThreshold: 7
  groupWeight: 15000
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000361
  poolId: 10000036
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100000362
  poolId: 10000036
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 100000363
  poolId: 10000036
  lvThreshold: 7
  groupWeight: 40
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 100000364
  poolId: 10000036
  lvThreshold: 8
  groupWeight: 80
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 100000365
  poolId: 10000036
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 600011401
  poolId: 6000114
  lvThreshold: 20
  groupWeight: 0
  groupWeight: 60
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011402
  poolId: 6000114
  lvThreshold: 40
  groupWeight: 10
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011403
  poolId: 6000114
  lvThreshold: 70
  groupWeight: 20
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011404
  poolId: 6000114
  lvThreshold: 110
  groupWeight: 30
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 600011405
  poolId: 6000114
  lvThreshold: 140
  groupWeight: 50
  groupWeight: 120
  groupWeight: 330
  groupWeight: 330
  groupWeight: 4100
  groupWeight: 4100
  groupWeight: 1000
}
rows {
  id: 130004801
  poolId: 13000480
  lvThreshold: 2
  groupWeight: 50
  groupWeight: 20000
}
rows {
  id: 130004802
  poolId: 13000480
  lvThreshold: 4
  groupWeight: 200
  groupWeight: 20000
}
rows {
  id: 130004803
  poolId: 13000481
  lvThreshold: 8
  groupWeight: 55
  groupWeight: 5
}
rows {
  id: 130004804
  poolId: 13000482
  lvThreshold: 8
  groupWeight: 65
  groupWeight: 5
}
rows {
  id: 130004805
  poolId: 13000483
  lvThreshold: 8
  groupWeight: 65
  groupWeight: 5
}
rows {
  id: 130004806
  poolId: 13000484
  lvThreshold: 8
  groupWeight: 145
  groupWeight: 5
}
rows {
  id: 130004901
  poolId: 13000490
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130004902
  poolId: 13000490
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 9500
  groupWeight: 10
}
rows {
  id: 130004903
  poolId: 13000490
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 15
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 500
  groupWeight: 15
}
rows {
  id: 130004904
  poolId: 13000490
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 25
  groupWeight: 9500
  groupWeight: 25
  groupWeight: 25
  groupWeight: 25
}
rows {
  id: 130004905
  poolId: 13000490
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
}
rows {
  id: 130004906
  poolId: 13000490
  lvThreshold: 12
  groupWeight: 9500
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
}
rows {
  id: 130004907
  poolId: 13000491
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004908
  poolId: 13000492
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004909
  poolId: 13000493
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130004910
  poolId: 13000494
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130004911
  poolId: 13000495
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130004912
  poolId: 13000496
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130004913
  poolId: 13000497
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004914
  poolId: 13000498
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004915
  poolId: 13000499
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004916
  poolId: 13000500
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004917
  poolId: 13000501
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130004918
  poolId: 13000502
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005101
  poolId: 13000510
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130005102
  poolId: 13000510
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005103
  poolId: 13000511
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005104
  poolId: 13000511
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005105
  poolId: 13000511
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005106
  poolId: 13000512
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005107
  poolId: 13000512
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005108
  poolId: 13000512
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005109
  poolId: 13000513
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005110
  poolId: 13000513
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005111
  poolId: 13000513
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005201
  poolId: 13000520
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130005202
  poolId: 13000520
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005203
  poolId: 13000521
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005204
  poolId: 13000521
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005205
  poolId: 13000521
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005206
  poolId: 13000522
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005207
  poolId: 13000522
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005208
  poolId: 13000522
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005209
  poolId: 13000523
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005210
  poolId: 13000523
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005211
  poolId: 13000523
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005301
  poolId: 13000530
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130005302
  poolId: 13000530
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 300
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130005303
  poolId: 13000530
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 200
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130005304
  poolId: 13000530
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130005305
  poolId: 13000530
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130005306
  poolId: 13000531
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130005307
  poolId: 13000532
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130005308
  poolId: 13000533
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130005309
  poolId: 13000534
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130005310
  poolId: 13000535
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130005311
  poolId: 13000536
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130005312
  poolId: 13000537
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005313
  poolId: 13000538
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005314
  poolId: 13000539
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005315
  poolId: 13000540
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005501
  poolId: 13000550
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130005502
  poolId: 13000550
  lvThreshold: 3
  groupWeight: 99
  groupWeight: 1
}
rows {
  id: 130005503
  poolId: 13000551
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130005504
  poolId: 13000552
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130005505
  poolId: 13000553
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130005506
  poolId: 13000560
  lvThreshold: 2
  groupWeight: 5
  groupWeight: 995
}
rows {
  id: 130005507
  poolId: 13000560
  lvThreshold: 3
  groupWeight: 99
  groupWeight: 1
}
rows {
  id: 130005508
  poolId: 13000561
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130005509
  poolId: 13000562
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 130005510
  poolId: 13000563
  lvThreshold: 7
  groupWeight: 1
}
rows {
  id: 100003401
  poolId: 1000034
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100003402
  poolId: 1000034
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100003403
  poolId: 1000034
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100003501
  poolId: 1000035
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100003502
  poolId: 1000035
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100003503
  poolId: 1000035
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100003601
  poolId: 1000036
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100003602
  poolId: 1000036
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100003603
  poolId: 1000036
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005701
  poolId: 13000570
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130005702
  poolId: 13000570
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005703
  poolId: 13000571
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005704
  poolId: 13000571
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005705
  poolId: 13000571
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005706
  poolId: 13000572
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005707
  poolId: 13000572
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005708
  poolId: 13000572
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130005709
  poolId: 13000573
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130005710
  poolId: 13000573
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130005711
  poolId: 13000573
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 10000371
  poolId: 10000037
  lvThreshold: 3
  groupWeight: 1500
  groupWeight: 8000
  groupWeight: 1950
  groupWeight: 50
  groupWeight: 2000
}
rows {
  id: 10000372
  poolId: 10000037
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 5000
  groupWeight: 100
  groupWeight: 2000
}
rows {
  id: 10000373
  poolId: 10000037
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 150
  groupWeight: 2000
}
rows {
  id: 10000374
  poolId: 10000037
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 2000
}
rows {
  id: 10000375
  poolId: 10000037
  lvThreshold: 9
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 130005801
  poolId: 13000580
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130005802
  poolId: 13000580
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 10
  groupWeight: 10
  groupWeight: 9500
  groupWeight: 10
}
rows {
  id: 130005803
  poolId: 13000580
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 15
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 500
  groupWeight: 15
}
rows {
  id: 130005804
  poolId: 13000580
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 25
  groupWeight: 9500
  groupWeight: 25
  groupWeight: 25
  groupWeight: 25
}
rows {
  id: 130005805
  poolId: 13000580
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
  groupWeight: 50
}
rows {
  id: 130005806
  poolId: 13000580
  lvThreshold: 12
  groupWeight: 9500
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
  groupWeight: 75
}
rows {
  id: 130005807
  poolId: 13000581
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130005808
  poolId: 13000582
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130005809
  poolId: 13000583
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130005810
  poolId: 13000584
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130005811
  poolId: 13000585
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130005812
  poolId: 13000586
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130005813
  poolId: 13000587
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005814
  poolId: 13000588
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005815
  poolId: 13000589
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005816
  poolId: 13000590
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005817
  poolId: 13000591
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130005818
  poolId: 13000592
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 100000381
  poolId: 10000038
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 100000382
  poolId: 10000039
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1000
}
rows {
  id: 100000383
  poolId: 10000039
  lvThreshold: 4
  groupWeight: 80
  groupWeight: 1000
}
rows {
  id: 100000384
  poolId: 10000039
  lvThreshold: 6
  groupWeight: 500
  groupWeight: 1000
}
rows {
  id: 100000385
  poolId: 10000040
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 4000
  groupWeight: 12000
}
rows {
  id: 100000386
  poolId: 10000040
  lvThreshold: 7
  groupWeight: 80
  groupWeight: 4000
  groupWeight: 12000
}
rows {
  id: 100000387
  poolId: 10000040
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 4000
  groupWeight: 20000
}
rows {
  id: 60001151
  poolId: 6000115
  lvThreshold: 3
  groupWeight: 10
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001152
  poolId: 6000115
  lvThreshold: 5
  groupWeight: 150
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001153
  poolId: 6000115
  lvThreshold: 6
  groupWeight: 350
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 60001154
  poolId: 6000115
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001155
  poolId: 6000115
  lvThreshold: 8
  groupWeight: 900
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 2200
  groupWeight: 2800
}
rows {
  id: 60001156
  poolId: 6000115
  lvThreshold: 9
  groupWeight: 15000
  groupWeight: 1200
  groupWeight: 1500
  groupWeight: 3000
  groupWeight: 4000
}
rows {
  id: 130006001
  poolId: 13000600
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 700
  groupWeight: 1000
}
rows {
  id: 130006002
  poolId: 13000600
  lvThreshold: 3
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130006003
  poolId: 13000601
  lvThreshold: 4
  groupWeight: 10
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130006004
  poolId: 13000601
  lvThreshold: 6
  groupWeight: 50
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130006005
  poolId: 13000601
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130006006
  poolId: 13000602
  lvThreshold: 4
  groupWeight: 30
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130006007
  poolId: 13000602
  lvThreshold: 6
  groupWeight: 60
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130006008
  poolId: 13000602
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130006009
  poolId: 13000603
  lvThreshold: 4
  groupWeight: 50
  groupWeight: 600
  groupWeight: 1000
}
rows {
  id: 130006010
  poolId: 13000603
  lvThreshold: 6
  groupWeight: 100
  groupWeight: 1000
  groupWeight: 500
}
rows {
  id: 130006011
  poolId: 13000603
  lvThreshold: 7
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100000411
  poolId: 10000041
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000412
  poolId: 10000042
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 1200
}
rows {
  id: 100000413
  poolId: 10000042
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 1200
}
rows {
  id: 100000414
  poolId: 10000043
  lvThreshold: 3
  groupWeight: 1
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000415
  poolId: 10000043
  lvThreshold: 5
  groupWeight: 80
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 100000416
  poolId: 10000043
  lvThreshold: 7
  groupWeight: 15000
  groupWeight: 2000
  groupWeight: 24000
}
rows {
  id: 130006101
  poolId: 13000610
  lvThreshold: 1
  groupWeight: 50
  groupWeight: 20000
}
rows {
  id: 130006102
  poolId: 13000610
  lvThreshold: 3
  groupWeight: 200
  groupWeight: 20000
}
rows {
  id: 130006103
  poolId: 13000611
  lvThreshold: 8
  groupWeight: 25
  groupWeight: 5
}
rows {
  id: 130006104
  poolId: 13000612
  lvThreshold: 8
  groupWeight: 35
  groupWeight: 5
}
rows {
  id: 130006105
  poolId: 13000613
  lvThreshold: 4
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130006106
  poolId: 13000613
  lvThreshold: 8
  groupWeight: 45
  groupWeight: 55
}
rows {
  id: 10000441
  poolId: 10000044
  lvThreshold: 3
  groupWeight: 1500
  groupWeight: 8000
  groupWeight: 1200
  groupWeight: 50
  groupWeight: 2000
}
rows {
  id: 10000442
  poolId: 10000044
  lvThreshold: 6
  groupWeight: 2
  groupWeight: 5000
  groupWeight: 5000
  groupWeight: 50
  groupWeight: 2000
}
rows {
  id: 10000443
  poolId: 10000044
  lvThreshold: 7
  groupWeight: 600
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 150
  groupWeight: 2000
}
rows {
  id: 10000444
  poolId: 10000044
  lvThreshold: 8
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 2000
  groupWeight: 2000
}
rows {
  id: 10000445
  poolId: 10000044
  lvThreshold: 9
  groupWeight: 200
  groupWeight: 5000
  groupWeight: 1500
  groupWeight: 1500
  groupWeight: 2000
}
rows {
  id: 100004101
  poolId: 1000041
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100004102
  poolId: 1000041
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100004103
  poolId: 1000041
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 100004201
  poolId: 1000042
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 800
  groupWeight: 1200
}
rows {
  id: 100004202
  poolId: 1000042
  lvThreshold: 5
  groupWeight: 100
  groupWeight: 1200
  groupWeight: 500
}
rows {
  id: 100004203
  poolId: 1000042
  lvThreshold: 6
  groupWeight: 1000
  groupWeight: 1
  groupWeight: 1
}
rows {
  id: 130006201
  poolId: 13000620
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 100
  groupWeight: 20000
}
rows {
  id: 130006202
  poolId: 13000620
  lvThreshold: 3
  groupWeight: 50
  groupWeight: 20000
  groupWeight: 500
}
rows {
  id: 130006203
  poolId: 13000620
  lvThreshold: 4
  groupWeight: 100
  groupWeight: 20000
  groupWeight: 20000
}
rows {
  id: 130006204
  poolId: 13000621
  lvThreshold: 6
  groupWeight: 25
  groupWeight: 5
}
rows {
  id: 130006205
  poolId: 13000622
  lvThreshold: 8
  groupWeight: 105
  groupWeight: 5
}
rows {
  id: 130006206
  poolId: 13000623
  lvThreshold: 12
  groupWeight: 355
  groupWeight: 5
}
rows {
  id: 130006207
  poolId: 13000624
  lvThreshold: 16
  groupWeight: 495
  groupWeight: 5
}
rows {
  id: 130006301
  poolId: 13000630
  lvThreshold: 2
  groupWeight: 1
  groupWeight: 5
  groupWeight: 5
  groupWeight: 500
  groupWeight: 9500
}
rows {
  id: 130006302
  poolId: 13000630
  lvThreshold: 4
  groupWeight: 5
  groupWeight: 10
  groupWeight: 300
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130006303
  poolId: 13000630
  lvThreshold: 6
  groupWeight: 10
  groupWeight: 200
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130006304
  poolId: 13000630
  lvThreshold: 8
  groupWeight: 15
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130006305
  poolId: 13000630
  lvThreshold: 10
  groupWeight: 20
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
  groupWeight: 9500
}
rows {
  id: 130006306
  poolId: 13000631
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130006307
  poolId: 13000632
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130006308
  poolId: 13000633
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130006309
  poolId: 13000634
  lvThreshold: 10
  groupWeight: 95
  groupWeight: 5
}
rows {
  id: 130006310
  poolId: 13000635
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130006311
  poolId: 13000636
  lvThreshold: 10
  groupWeight: 195
  groupWeight: 5
}
rows {
  id: 130006312
  poolId: 13000637
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130006313
  poolId: 13000638
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130006314
  poolId: 13000639
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
rows {
  id: 130006315
  poolId: 13000640
  lvThreshold: 10
  groupWeight: 395
  groupWeight: 5
}
