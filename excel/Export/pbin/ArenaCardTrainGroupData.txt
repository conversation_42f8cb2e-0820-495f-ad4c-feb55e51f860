com.tencent.wea.xlsRes.table_ArenaCardTrainGroupData
excel/xls/Arena/K_Arena卡牌随机表.xlsx sheet:3v3训练场卡包表
rows {
  heroId: 201
  cardPackage1: 3701
  cardPackage1: 3705
  cardPackage1: 3724
  cardPackage1: 3725
  cardPackage1: 3723
  cardPackage1: 3704
  cardPackage2: 4705
  cardPackage2: 4724
  cardPackage2: 4725
  cardPackage2: 4723
  cardPackage2: 4704
  cardPackage2: 4701
  cardSuitList1: 1
  cardSuitList1: 5
  cardSuitList1: 23
  cardSuitList1: 24
  cardSuitList1: 25
  cardSuitList1: 4
  cardSuitList2: 1
  cardSuitList2: 5
  cardSuitList2: 4
  cardSuitList2: 23
  cardSuitList2: 24
  cardSuitList2: 25
}
rows {
  heroId: 101
  cardPackage1: 3701
  cardPackage1: 3720
  cardPackage1: 3724
  cardPackage1: 3705
  cardPackage1: 3702
  cardPackage1: 3704
  cardPackage2: 4701
  cardPackage2: 4702
  cardPackage2: 4704
  cardPackage2: 4705
  cardPackage2: 4720
  cardPackage2: 4724
  cardSuitList1: 1
  cardSuitList1: 2
  cardSuitList1: 4
  cardSuitList1: 5
  cardSuitList1: 20
  cardSuitList1: 24
  cardSuitList2: 1
  cardSuitList2: 2
  cardSuitList2: 4
  cardSuitList2: 5
  cardSuitList2: 20
  cardSuitList2: 24
}
rows {
  heroId: 301
  cardPackage1: 3709
  cardPackage1: 3723
  cardPackage1: 3710
  cardPackage1: 3713
  cardPackage1: 3704
  cardPackage1: 3720
  cardPackage2: 4709
  cardPackage2: 4710
  cardPackage2: 4704
  cardPackage2: 4713
  cardPackage2: 4720
  cardPackage2: 4723
  cardSuitList1: 9
  cardSuitList1: 10
  cardSuitList1: 13
  cardSuitList1: 4
  cardSuitList1: 20
  cardSuitList1: 23
  cardSuitList2: 9
  cardSuitList2: 10
  cardSuitList2: 4
  cardSuitList2: 13
  cardSuitList2: 20
  cardSuitList2: 23
}
rows {
  heroId: 401
  cardPackage1: 3703
  cardPackage1: 3723
  cardPackage1: 3701
  cardPackage1: 3702
  cardPackage1: 3713
  cardPackage1: 3704
  cardPackage2: 4703
  cardPackage2: 4702
  cardPackage2: 4701
  cardPackage2: 4704
  cardPackage2: 4713
  cardPackage2: 4723
  cardSuitList1: 1
  cardSuitList1: 2
  cardSuitList1: 3
  cardSuitList1: 4
  cardSuitList1: 13
  cardSuitList1: 23
  cardSuitList2: 1
  cardSuitList2: 2
  cardSuitList2: 3
  cardSuitList2: 4
  cardSuitList2: 13
  cardSuitList2: 23
}
rows {
  heroId: 501
  cardPackage1: 3701
  cardPackage1: 3722
  cardPackage1: 3703
  cardPackage1: 3702
  cardPackage1: 3713
  cardPackage1: 3704
  cardPackage2: 4701
  cardPackage2: 4702
  cardPackage2: 4703
  cardPackage2: 4704
  cardPackage2: 4713
  cardPackage2: 4722
  cardSuitList1: 1
  cardSuitList1: 2
  cardSuitList1: 3
  cardSuitList1: 4
  cardSuitList1: 13
  cardSuitList1: 22
  cardSuitList2: 1
  cardSuitList2: 2
  cardSuitList2: 3
  cardSuitList2: 4
  cardSuitList2: 13
  cardSuitList2: 22
}
rows {
  heroId: 601
  cardPackage1: 3714
  cardPackage1: 3725
  cardPackage1: 3723
  cardPackage1: 3706
  cardPackage1: 3704
  cardPackage1: 3709
  cardPackage2: 4709
  cardPackage2: 4714
  cardPackage2: 4706
  cardPackage2: 4704
  cardPackage2: 4725
  cardPackage2: 4723
  cardSuitList1: 9
  cardSuitList1: 14
  cardSuitList1: 6
  cardSuitList1: 4
  cardSuitList1: 23
  cardSuitList1: 25
  cardSuitList2: 9
  cardSuitList2: 14
  cardSuitList2: 6
  cardSuitList2: 4
  cardSuitList2: 25
  cardSuitList2: 23
}
rows {
  heroId: 602
  cardPackage1: 3714
  cardPackage1: 3725
  cardPackage1: 3723
  cardPackage1: 3706
  cardPackage1: 3704
  cardPackage1: 3701
  cardPackage2: 4701
  cardPackage2: 4714
  cardPackage2: 4706
  cardPackage2: 4704
  cardPackage2: 4725
  cardPackage2: 4723
  cardSuitList1: 1
  cardSuitList1: 14
  cardSuitList1: 6
  cardSuitList1: 4
  cardSuitList1: 23
  cardSuitList1: 25
  cardSuitList2: 1
  cardSuitList2: 14
  cardSuitList2: 6
  cardSuitList2: 4
  cardSuitList2: 25
  cardSuitList2: 23
}
