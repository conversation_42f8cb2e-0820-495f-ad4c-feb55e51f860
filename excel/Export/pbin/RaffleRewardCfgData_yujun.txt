com.tencent.wea.xlsRes.table_RaffleRewardCfgData
excel/xls/C_抽奖奖池_yujun.xlsx sheet:奖励
rows {
  rewardId: 3000801
  poolId: 30008
  name: "花之语  伊笠丝"
  itemId: 403440
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  extraItemIds: 405073
  extraItemNums: 1
}
rows {
  rewardId: 3000802
  poolId: 30008
  name: "森之神 西尔维"
  itemId: 403450
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000803
  poolId: 30008
  name: "仙境之音"
  itemId: 620465
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000804
  poolId: 30008
  name: "轻语鸢梦"
  itemId: 610228
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000805
  poolId: 30008
  name: "星辉烁羽"
  itemId: 630324
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000806
  poolId: 30008
  name: "蔷薇精灵 路易斯"
  itemId: 403390
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  extraItemIds: 405072
  extraItemNums: 1
}
rows {
  rewardId: 3000807
  poolId: 30008
  name: "蓝百合精灵 莉莉"
  itemId: 403400
  itemNum: 1
  groupId: 4
  weight: 8
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000808
  poolId: 30008
  name: "郁金流年"
  itemId: 610220
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000809
  poolId: 30008
  name: "花语使者"
  itemId: 620429
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000810
  poolId: 30008
  name: "微笑玫瑰"
  itemId: 630307
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000811
  poolId: 30008
  name: "头像框-鲜花盛开"
  itemId: 840169
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000812
  poolId: 30008
  name: "神秘配方"
  itemId: 720740
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000813
  poolId: 30008
  name: "睡梦仙子"
  itemId: 720741
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000814
  poolId: 30008
  name: "复苏祈愿"
  itemId: 720742
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000815
  poolId: 30008
  name: "摘下花瓣"
  itemId: 711064
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000816
  poolId: 30008
  name: "探头偷看"
  itemId: 711065
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000817
  poolId: 30008
  name: "黑暗料理"
  itemId: 711066
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000818
  poolId: 30008
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000819
  poolId: 30008
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000820
  poolId: 30008
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000821
  poolId: 30008
  name: "叶蔓蔓"
  itemId: 403260
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000822
  poolId: 30008
  name: "晨小枫"
  itemId: 403170
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000823
  poolId: 30008
  name: "菊小雅"
  itemId: 403210
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000824
  poolId: 30008
  name: "可爱回旋"
  itemId: 720737
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000825
  poolId: 30008
  name: "白日梦想家"
  itemId: 720738
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000826
  poolId: 30008
  name: "转圈登场"
  itemId: 720750
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000827
  poolId: 30008
  name: "流行律动"
  itemId: 720751
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000828
  poolId: 30008
  name: "身手了得"
  itemId: 720752
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000829
  poolId: 30008
  name: "让我看看"
  itemId: 711067
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000830
  poolId: 30008
  name: "举牌666"
  itemId: 711068
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000831
  poolId: 30008
  name: "我+1"
  itemId: 711069
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000832
  poolId: 30008
  name: "斗鸡眼吐舌"
  itemId: 711070
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000833
  poolId: 30008
  name: "萌虎出动上装"
  itemId: 510215
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000834
  poolId: 30008
  name: "萌虎出动下装"
  itemId: 520152
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000835
  poolId: 30008
  name: "萌虎出动手套"
  itemId: 530128
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000836
  poolId: 30008
  name: "饭团星球上装"
  itemId: 510227
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000837
  poolId: 30008
  name: "饭团星球下装"
  itemId: 520160
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000838
  poolId: 30008
  name: "饭团星球手套"
  itemId: 530136
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000839
  poolId: 30008
  name: "蔷薇星云上装"
  itemId: 510228
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000840
  poolId: 30008
  name: "蔷薇星云下装"
  itemId: 520161
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000841
  poolId: 30008
  name: "蔷薇星云手套"
  itemId: 530137
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000842
  poolId: 30008
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000843
  poolId: 30008
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000844
  poolId: 30008
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000845
  poolId: 30008
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5045001
  poolId: 5045
  name: "正男"
  itemId: 403300
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5045002
  poolId: 5045
  name: "上班族背包"
  itemId: 620447
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5045003
  poolId: 5045
  name: "正男头像"
  itemId: 860099
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5045004
  poolId: 5045
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5045005
  poolId: 5045
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5045006
  poolId: 5045
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5045007
  poolId: 5045
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5045008
  poolId: 5045
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5045009
  poolId: 5045
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5045010
  poolId: 5045
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5045011
  poolId: 5045
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5045012
  poolId: 5045
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046001
  poolId: 5046
  name: "风间"
  itemId: 401510
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5046002
  poolId: 5046
  name: "星际朋友"
  itemId: 630122
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5046003
  poolId: 5046
  name: "风间头像"
  itemId: 860042
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5046004
  poolId: 5046
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5046005
  poolId: 5046
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046006
  poolId: 5046
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046007
  poolId: 5046
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046008
  poolId: 5046
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046009
  poolId: 5046
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046010
  poolId: 5046
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046011
  poolId: 5046
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5046012
  poolId: 5046
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047001
  poolId: 5047
  name: "妮妮"
  itemId: 401520
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5047002
  poolId: 5047
  name: "妮妮的兔子"
  itemId: 620251
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5047003
  poolId: 5047
  name: "妮妮头像"
  itemId: 860043
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5047004
  poolId: 5047
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5047005
  poolId: 5047
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047006
  poolId: 5047
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047007
  poolId: 5047
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047008
  poolId: 5047
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047009
  poolId: 5047
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047010
  poolId: 5047
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047011
  poolId: 5047
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5047012
  poolId: 5047
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048001
  poolId: 5048
  name: "阿呆"
  itemId: 401530
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5048002
  poolId: 5048
  name: "阿呆的金鱼"
  itemId: 630130
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5048003
  poolId: 5048
  name: "阿呆头像"
  itemId: 860041
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5048004
  poolId: 5048
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 5048005
  poolId: 5048
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048006
  poolId: 5048
  name: "万能棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048007
  poolId: 5048
  name: "时装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048008
  poolId: 5048
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048009
  poolId: 5048
  name: "饰品染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048010
  poolId: 5048
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048011
  poolId: 5048
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5048012
  poolId: 5048
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 5124001
  poolId: 51240
  name: "睡衣小新"
  itemId: 403290
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5124002
  poolId: 51240
  name: "温泉毛巾"
  itemId: 630306
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5124003
  poolId: 51240
  name: "头好烫"
  itemId: 711076
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5124004
  poolId: 51240
  name: "哞哞眼罩"
  itemId: 610195
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5124005
  poolId: 51241
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124006
  poolId: 51241
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124007
  poolId: 51241
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5124008
  poolId: 51241
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124009
  poolId: 51241
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124010
  poolId: 51241
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124011
  poolId: 51241
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124012
  poolId: 51242
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124013
  poolId: 51242
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124014
  poolId: 51242
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5124015
  poolId: 51242
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124016
  poolId: 51242
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124017
  poolId: 51242
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124018
  poolId: 51242
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124019
  poolId: 51243
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124020
  poolId: 51243
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124021
  poolId: 51243
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5124022
  poolId: 51243
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124023
  poolId: 51243
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124024
  poolId: 51243
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124025
  poolId: 51243
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124026
  poolId: 51244
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124027
  poolId: 51244
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124028
  poolId: 51244
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5124029
  poolId: 51244
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124030
  poolId: 51244
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124031
  poolId: 51244
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5124032
  poolId: 51244
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125001
  poolId: 51250
  name: "左卫门 小新"
  itemId: 403310
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5125002
  poolId: 51250
  name: "鳄鱼山先生背包"
  itemId: 620448
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5125003
  poolId: 51250
  name: "自信满满"
  itemId: 711075
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5125004
  poolId: 51250
  name: "炫彩回旋镖"
  itemId: 640024
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5125005
  poolId: 51251
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125006
  poolId: 51251
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125007
  poolId: 51251
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5125008
  poolId: 51251
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125009
  poolId: 51251
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125010
  poolId: 51251
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125011
  poolId: 51251
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125012
  poolId: 51252
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125013
  poolId: 51252
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125014
  poolId: 51252
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5125015
  poolId: 51252
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125016
  poolId: 51252
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125017
  poolId: 51252
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125018
  poolId: 51252
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125019
  poolId: 51253
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125020
  poolId: 51253
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125021
  poolId: 51253
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5125022
  poolId: 51253
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125023
  poolId: 51253
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125024
  poolId: 51253
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125025
  poolId: 51253
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125026
  poolId: 51254
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125027
  poolId: 51254
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125028
  poolId: 51254
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5125029
  poolId: 51254
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125030
  poolId: 51254
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125031
  poolId: 51254
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5125032
  poolId: 51254
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 3000901
  poolId: 30009
  name: "大收藏家 柯莱荻"
  itemId: 403860
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  extraItemIds: 405102
  extraItemNums: 1
  ifShow: 1
}
rows {
  rewardId: 3000902
  poolId: 30009
  name: "星河织梦者 诗寇蒂"
  itemId: 403870
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3000903
  poolId: 30009
  name: "星球眼镜"
  itemId: 610254
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000904
  poolId: 30009
  name: "星辰罗盘"
  itemId: 620540
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000905
  poolId: 30009
  name: "星云头饰"
  itemId: 630373
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000906
  poolId: 30009
  name: "万事屋屋主 怀特妮"
  itemId: 403850
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000907
  poolId: 30009
  name: "炼星术师 尼古拉斯"
  itemId: 403840
  itemNum: 1
  groupId: 4
  weight: 8
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000908
  poolId: 30009
  name: "信号勘测器"
  itemId: 620520
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000909
  poolId: 30009
  name: "星空蛋"
  itemId: 620517
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000910
  poolId: 30009
  name: "宇宙晶体"
  itemId: 630361
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3000911
  poolId: 30009
  name: "头像框-鲜花盛开"
  itemId: 840184
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3000912
  poolId: 30009
  name: "呼唤群星"
  itemId: 720804
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000913
  poolId: 30009
  name: "星夜圆舞曲"
  itemId: 720805
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000914
  poolId: 30009
  name: "宇宙起源"
  itemId: 720806
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000915
  poolId: 30009
  name: "星球接触"
  itemId: 711166
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000916
  poolId: 30009
  name: "占星"
  itemId: 711167
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000917
  poolId: 30009
  name: "流星划过"
  itemId: 711163
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3000918
  poolId: 30009
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3000919
  poolId: 30009
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3000920
  poolId: 30009
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3000921
  poolId: 30009
  name: "星星疗愈师"
  itemId: 403670
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000922
  poolId: 30009
  name: "星梦邮差"
  itemId: 403680
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000923
  poolId: 30009
  name: "星星工程师"
  itemId: 403740
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3000924
  poolId: 30009
  name: "怀旧迪斯科"
  itemId: 720808
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000925
  poolId: 30009
  name: "圆周摇"
  itemId: 720809
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000926
  poolId: 30009
  name: "空中筋斗"
  itemId: 720810
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000927
  poolId: 30009
  name: "耍宝舞"
  itemId: 720811
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000928
  poolId: 30009
  name: "打气舞"
  itemId: 720812
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000929
  poolId: 30009
  name: "飞碟来了"
  itemId: 711170
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000930
  poolId: 30009
  name: "摘星星"
  itemId: 711179
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000931
  poolId: 30009
  name: "月球插旗"
  itemId: 711180
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000932
  poolId: 30009
  name: "抱月亮"
  itemId: 711181
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3000933
  poolId: 30009
  name: "星空学院上装"
  itemId: 510239
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000934
  poolId: 30009
  name: "星空学院下装"
  itemId: 520166
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000935
  poolId: 30009
  name: "星空学院手套"
  itemId: 530142
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000936
  poolId: 30009
  name: "名牌标签服上装"
  itemId: 510240
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000937
  poolId: 30009
  name: "名牌标签服下装"
  itemId: 520167
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000938
  poolId: 30009
  name: "名牌标签服手套"
  itemId: 530143
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000939
  poolId: 30009
  name: "宇航员主题裙上装"
  itemId: 510244
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000940
  poolId: 30009
  name: "宇航员主题裙下装"
  itemId: 520171
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000941
  poolId: 30009
  name: "宇航员主题裙手套"
  itemId: 530147
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3000942
  poolId: 30009
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000943
  poolId: 30009
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000944
  poolId: 30009
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3000945
  poolId: 30009
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5360001
  poolId: 5360
  name: "星语摩天轮"
  itemId: 640075
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 5360002
  poolId: 5360
  name: "梦幻花羽"
  itemId: 620588
  itemNum: 1
  groupId: 1
  weight: 99
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5360003
  poolId: 5360
  name: "飞鼠宝宝"
  itemId: 630411
  itemNum: 1
  groupId: 1
  weight: 5000
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 3
  }
}
rows {
  rewardId: 5360004
  poolId: 5360
  name: "冰晶花梦"
  itemId: 620152
  itemNum: 1
  groupId: 1
  weight: 4900
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5360005
  poolId: 5360
  name: "寿司仔"
  itemId: 630207
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5360006
  poolId: 5360
  name: "香肠派对"
  itemId: 610278
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5360007
  poolId: 5360
  name: "龟龟探险家"
  itemId: 620618
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5360008
  poolId: 5360
  name: "戏院小乖"
  itemId: 404340
  itemNum: 1
  groupId: 2
  weight: 3
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5360009
  poolId: 5360
  name: "凤之钥*20"
  itemId: 222
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 3
}
rows {
  rewardId: 5360010
  poolId: 5360
  name: "凤之钥*10"
  itemId: 222
  itemNum: 10
  groupId: 3
  weight: 20
  limit: 5
  inGroupLimit {
    period: 20
    limit: 1
  }
  comboItemIds: 222
  comboItemIds: 222
  comboItemIds: 222
  comboItemNums: 10
  comboItemNums: 20
  comboItemNums: 30
  comboType: RCT_Always
  comboItemWeights: 350
  comboItemWeights: 40
  comboItemWeights: 10
}
rows {
  rewardId: 5360011
  poolId: 5360
  name: "凤之钥*5"
  itemId: 222
  itemNum: 5
  groupId: 3
  weight: 60
  inGroupLimit {
    period: 20
    limit: 4
  }
  comboItemIds: 222
  comboItemIds: 222
  comboItemIds: 222
  comboItemNums: 5
  comboItemNums: 10
  comboItemNums: 15
  comboType: RCT_Always
  comboItemWeights: 300
  comboItemWeights: 80
  comboItemWeights: 20
}
rows {
  rewardId: 5360012
  poolId: 5360
  name: "凤之钥*2"
  itemId: 222
  itemNum: 2
  groupId: 3
  weight: 120
  inGroupLimit {
    period: 20
    limit: 15
  }
  comboItemIds: 222
  comboItemIds: 222
  comboItemIds: 222
  comboItemNums: 2
  comboItemNums: 4
  comboItemNums: 6
  comboType: RCT_Always
  comboItemWeights: 200
  comboItemWeights: 125
  comboItemWeights: 50
}
rows {
  rewardId: 5360013
  poolId: 5360
  name: "凤之钥*1"
  itemId: 222
  itemNum: 1
  groupId: 4
  weight: 100
  comboItemIds: 222
  comboItemIds: 222
  comboItemIds: 222
  comboItemNums: 1
  comboItemNums: 2
  comboItemNums: 3
  comboType: RCT_Always
  comboItemWeights: 160
  comboItemWeights: 160
  comboItemWeights: 80
}
rows {
  rewardId: 5360014
  poolId: 5360
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5360015
  poolId: 5360
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5360016
  poolId: 5360
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
}
rows {
  rewardId: 5360017
  poolId: 5360
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 50
}
rows {
  rewardId: 5360018
  poolId: 5360
  name: "堡你喜欢上装"
  itemId: 510250
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5360019
  poolId: 5360
  name: "堡你喜欢下装"
  itemId: 520176
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5360020
  poolId: 5360
  name: "堡你喜欢手套"
  itemId: 530152
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5360021
  poolId: 5360
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 50
}
rows {
  rewardId: 3001001
  poolId: 30010
  name: "美食主播 可可"
  itemId: 404430
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001002
  poolId: 30010
  name: "美食品鉴官 红丝绒"
  itemId: 404440
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  extraItemIds: 405105
  extraItemNums: 1
  ifShow: 1
}
rows {
  rewardId: 3001003
  poolId: 30010
  name: "甜蜜礼赞"
  itemId: 640087
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001004
  poolId: 30010
  name: "甜蜜橱窗"
  itemId: 620615
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001005
  poolId: 30010
  name: "可可圆舞曲"
  itemId: 630408
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001006
  poolId: 30010
  name: "茶点师 安娜"
  itemId: 404400
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001007
  poolId: 30010
  name: "拉面大师 秦风"
  itemId: 404410
  itemNum: 1
  groupId: 4
  weight: 8
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001008
  poolId: 30010
  name: "蟹钳眼镜"
  itemId: 610272
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001009
  poolId: 30010
  name: "鱿趣背包"
  itemId: 620605
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001010
  poolId: 30010
  name: "甜梦头冠"
  itemId: 630396
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001011
  poolId: 30010
  name: "头像框-甜蜜起司"
  itemId: 840205
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3001012
  poolId: 30010
  name: "颠勺大师"
  itemId: 720867
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001013
  poolId: 30010
  name: "烧烤大师"
  itemId: 720868
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001014
  poolId: 30010
  name: "面包出炉"
  itemId: 720869
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001015
  poolId: 30010
  name: "唱歌"
  itemId: 711261
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001016
  poolId: 30010
  name: "牛牛偷听"
  itemId: 711262
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001017
  poolId: 30010
  name: "伸手比倒耶"
  itemId: 711264
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001018
  poolId: 30010
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3001019
  poolId: 30010
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3001020
  poolId: 30010
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3001021
  poolId: 30010
  name: "姜饼小子"
  itemId: 404230
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001022
  poolId: 30010
  name: "芦小甜"
  itemId: 404240
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001023
  poolId: 30010
  name: "玉暖暖"
  itemId: 404270
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001024
  poolId: 30010
  name: "扶额叹气"
  itemId: 720866
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001025
  poolId: 30010
  name: "太生气了"
  itemId: 720870
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001026
  poolId: 30010
  name: "专业气氛组"
  itemId: 720871
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001027
  poolId: 30010
  name: "拉伸运动"
  itemId: 720872
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001028
  poolId: 30010
  name: "可爱告别"
  itemId: 720873
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001029
  poolId: 30010
  name: "不赞同"
  itemId: 711265
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001030
  poolId: 30010
  name: "困到模糊"
  itemId: 711266
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001031
  poolId: 30010
  name: "生气"
  itemId: 711267
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001032
  poolId: 30010
  name: "闪耀的我"
  itemId: 711268
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001033
  poolId: 30010
  name: "酣眠之冬上装"
  itemId: 510270
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001034
  poolId: 30010
  name: "酣眠之冬下装"
  itemId: 520184
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001035
  poolId: 30010
  name: "酣眠之冬手套"
  itemId: 530160
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001036
  poolId: 30010
  name: "暖樱冬语上装"
  itemId: 510272
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001037
  poolId: 30010
  name: "暖樱冬语下装"
  itemId: 520186
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001038
  poolId: 30010
  name: "暖樱冬语手套"
  itemId: 530162
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001039
  poolId: 30010
  name: "速食美学上装"
  itemId: 510273
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001040
  poolId: 30010
  name: "速食美学下装"
  itemId: 520187
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001041
  poolId: 30010
  name: "速食美学手套"
  itemId: 530163
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001042
  poolId: 30010
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001043
  poolId: 30010
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001044
  poolId: 30010
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001045
  poolId: 30010
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 80001001
  poolId: 8000101
  groupId: 1
  weight: 980
  earningPoints: 1
}
rows {
  rewardId: 80001002
  poolId: 8000101
  groupId: 1
  weight: 15
  earningPoints: 2
}
rows {
  rewardId: 80001003
  poolId: 8000101
  groupId: 1
  weight: 5
  earningPoints: 3
}
rows {
  rewardId: 80001004
  poolId: 8000101
  name: "鎏金硬币"
  itemId: 224
  itemNum: 5
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 7
    limit: 5
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80001005
  poolId: 8000101
  name: "鎏金硬币"
  itemId: 224
  itemNum: 8
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 7
    limit: 1
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80001006
  poolId: 8000101
  name: "鎏金硬币"
  itemId: 224
  itemNum: 15
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 7
    limit: 1
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80001007
  poolId: 8000102
  groupId: 1
  weight: 980
  earningPoints: 1
}
rows {
  rewardId: 80001008
  poolId: 8000102
  groupId: 1
  weight: 15
  earningPoints: 2
}
rows {
  rewardId: 80001009
  poolId: 8000102
  groupId: 1
  weight: 5
  earningPoints: 3
}
rows {
  rewardId: 80001010
  poolId: 8000102
  name: "鎏金硬币"
  itemId: 224
  itemNum: 10
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 6
    limit: 1
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80001011
  poolId: 8000102
  name: "鎏金硬币"
  itemId: 224
  itemNum: 20
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 6
    limit: 2
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80001012
  poolId: 8000102
  name: "鎏金硬币"
  itemId: 224
  itemNum: 30
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 6
    limit: 3
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80001013
  poolId: 8000110
  name: "星辉幼角"
  itemId: 730010
  itemNum: 1
  groupId: 1
  weight: 680
  isGrand: true
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80001014
  poolId: 8000110
  name: "星澜灵角"
  itemId: 730011
  itemNum: 1
  groupId: 1
  weight: 0
  isGrand: true
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80001015
  poolId: 8000110
  name: "璀璨星翼"
  itemId: 730013
  itemNum: 1
  groupId: 1
  weight: 0
  isGrand: true
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80001016
  poolId: 8000110
  name: "突破石*10"
  itemId: 225
  itemNum: 10
  groupId: 1
  weight: 10
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80001017
  poolId: 8000110
  name: "突破石*5"
  itemId: 225
  itemNum: 5
  groupId: 1
  weight: 1200
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80001018
  poolId: 8000110
  name: "突破石*3"
  itemId: 225
  itemNum: 3
  groupId: 1
  weight: 110
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80001019
  poolId: 8000109
  name: "蝴蝶翅膀"
  itemId: 620368
  itemNum: 1
  groupId: 1
  weight: 120
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80001020
  poolId: 8000109
  name: "大白鲨"
  itemId: 640105
  itemNum: 1
  groupId: 1
  weight: 400
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80001021
  poolId: 8000109
  name: "独角兽角"
  itemId: 630484
  itemNum: 1
  groupId: 1
  weight: 400
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80001022
  poolId: 8000109
  name: "突破石*2"
  itemId: 225
  itemNum: 2
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80001023
  poolId: 8000109
  name: "突破石*1"
  itemId: 225
  itemNum: 1
  groupId: 1
  weight: 40
  inGroupLimit {
    period: 3
    limit: 3
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80001024
  poolId: 8000109
  name: "丘比特"
  itemId: 404630
  itemNum: 1
  groupId: 1
  weight: 40
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80001025
  poolId: 8000108
  name: "大白鲨"
  itemId: 640105
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80001026
  poolId: 8000108
  name: "独角兽角"
  itemId: 630484
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80001027
  poolId: 8000108
  name: "突破石*1"
  itemId: 225
  itemNum: 1
  groupId: 1
  weight: 5
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80001028
  poolId: 8000108
  name: "丘比特"
  itemId: 404630
  itemNum: 1
  groupId: 1
  weight: 300
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80001029
  poolId: 8000108
  name: "圣诞小鹿男"
  itemId: 404780
  itemNum: 1
  groupId: 1
  weight: 300
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80001030
  poolId: 8000108
  name: "圣诞小鹿女"
  itemId: 404790
  itemNum: 1
  groupId: 1
  weight: 300
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80001031
  poolId: 8000107
  name: "突破石*1"
  itemId: 225
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80001032
  poolId: 8000107
  name: "圣诞小鹿男"
  itemId: 404780
  itemNum: 1
  groupId: 1
  weight: 40
  limit: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80001033
  poolId: 8000107
  name: "圣诞小鹿女"
  itemId: 404790
  itemNum: 1
  groupId: 1
  weight: 40
  limit: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80001034
  poolId: 8000107
  name: "丘比特之弓"
  itemId: 640102
  itemNum: 1
  groupId: 1
  weight: 240
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80001035
  poolId: 8000107
  name: "倒扣冰淇淋"
  itemId: 630426
  itemNum: 1
  groupId: 1
  weight: 240
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80001036
  poolId: 8000107
  name: "头饰-鸟窝"
  itemId: 630445
  itemNum: 1
  groupId: 1
  weight: 240
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80001037
  poolId: 8000106
  name: "【紫动】伸手接雪花"
  itemId: 720910
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 5
  sortName: "奖池（♥ × 3）"
}
rows {
  rewardId: 80001038
  poolId: 8000106
  name: "【紫动】吹泡泡"
  itemId: 720911
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 5
  sortName: "奖池（♥ × 3）"
}
rows {
  rewardId: 80001039
  poolId: 8000106
  name: "【紫动】扔球杂耍"
  itemId: 720912
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 5
  sortName: "奖池（♥ × 3）"
}
rows {
  rewardId: 80001040
  poolId: 8000105
  name: "【蓝配】背饰-香菇串"
  itemId: 620604
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 6
  sortName: "奖池（♥ × 2）"
}
rows {
  rewardId: 80001041
  poolId: 8000105
  name: "【蓝配】头饰-生日帽"
  itemId: 630395
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 6
  sortName: "奖池（♥ × 2）"
}
rows {
  rewardId: 80001042
  poolId: 8000105
  name: "【蓝配】头饰-白菜"
  itemId: 630328
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 6
  sortName: "奖池（♥ × 2）"
}
rows {
  rewardId: 80001043
  poolId: 8000104
  name: "拥抱侏罗纪套装"
  itemId: 310284
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 7
  sortName: "奖池（♥ × 1）"
}
rows {
  rewardId: 80001044
  poolId: 8000104
  name: "漆墨拼图套装"
  itemId: 310285
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 7
  sortName: "奖池（♥ × 1）"
}
rows {
  rewardId: 80001045
  poolId: 8000104
  name: "蓝庭花语套装"
  itemId: 310286
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 7
  sortName: "奖池（♥ × 1）"
}
rows {
  rewardId: 3001101
  poolId: 30011
  name: "惊鸿舞姬 若凝"
  itemId: 404720
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001102
  poolId: 30011
  name: "牡丹贵妃 洛安"
  itemId: 404730
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  extraItemIds: 405106
  extraItemNums: 1
  ifShow: 1
}
rows {
  rewardId: 3001103
  poolId: 30011
  name: "比翼双栖"
  itemId: 630477
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001104
  poolId: 30011
  name: "鸿运祥云"
  itemId: 610309
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001105
  poolId: 30011
  name: "弦歌雅韵"
  itemId: 640120
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001106
  poolId: 30011
  name: "飞鸟衔花 渺渺"
  itemId: 410030
  itemNum: 1
  groupId: 4
  weight: 2
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001107
  poolId: 30011
  name: "猫探员 怀义"
  itemId: 404990
  itemNum: 1
  groupId: 4
  weight: 8
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001108
  poolId: 30011
  name: "三彩小马"
  itemId: 620657
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001109
  poolId: 30011
  name: "肉夹馍"
  itemId: 620660
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001110
  poolId: 30011
  name: "金锁吉祥"
  itemId: 630436
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001111
  poolId: 30011
  name: "头像框-倾城绝代"
  itemId: 840235
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3001112
  poolId: 30011
  name: "流云飞天"
  itemId: 720937
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001113
  poolId: 30011
  name: "阅金经"
  itemId: 720936
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001114
  poolId: 30011
  name: "吹笛者"
  itemId: 720935
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001115
  poolId: 30011
  name: "憨厚的坏笑"
  itemId: 711323
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001116
  poolId: 30011
  name: "吨吨吨"
  itemId: 711325
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001117
  poolId: 30011
  name: "抓拍"
  itemId: 711329
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001118
  poolId: 30011
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3001119
  poolId: 30011
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3001120
  poolId: 30011
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3001121
  poolId: 30011
  name: "墨小轩"
  itemId: 404650
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001122
  poolId: 30011
  name: "古咚咚"
  itemId: 404860
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001123
  poolId: 30011
  name: "昭小容"
  itemId: 404680
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001124
  poolId: 30011
  name: "百媚生"
  itemId: 720933
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001125
  poolId: 30011
  name: "仕女图"
  itemId: 720932
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001126
  poolId: 30011
  name: "急急国王"
  itemId: 720927
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001127
  poolId: 30011
  name: "打招呼"
  itemId: 720926
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001128
  poolId: 30011
  name: "小发雷霆"
  itemId: 720925
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001129
  poolId: 30011
  name: "让我吃吃"
  itemId: 711326
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001130
  poolId: 30011
  name: "猫猫呆住"
  itemId: 711327
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001131
  poolId: 30011
  name: "大帅牛"
  itemId: 711328
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001132
  poolId: 30011
  name: "占位"
  itemId: 711333
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001133
  poolId: 30011
  name: "奶芙蝶语上装"
  itemId: 510294
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001134
  poolId: 30011
  name: "奶芙蝶语下装"
  itemId: 520196
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001135
  poolId: 30011
  name: "奶芙蝶语手套"
  itemId: 530172
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001136
  poolId: 30011
  name: "自愿上学上装"
  itemId: 510295
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001137
  poolId: 30011
  name: "自愿上学下装"
  itemId: 520197
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001138
  poolId: 30011
  name: "自愿上学手套"
  itemId: 530173
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001139
  poolId: 30011
  name: "自愿上班上装"
  itemId: 510296
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001140
  poolId: 30011
  name: "自愿上班下装"
  itemId: 520198
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001141
  poolId: 30011
  name: "自愿上班手套"
  itemId: 530174
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001142
  poolId: 30011
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001143
  poolId: 30011
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001144
  poolId: 30011
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001145
  poolId: 30011
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5361001
  poolId: 5361
  name: "心语之桥"
  itemId: 640123
  itemNum: 1
  groupId: 1
  weight: 1
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 1
  }
}
rows {
  rewardId: 5361002
  poolId: 5361
  name: "沧海之羽"
  itemId: 620770
  itemNum: 1
  groupId: 1
  weight: 99
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5361003
  poolId: 5361
  name: "梦幻泡影"
  itemId: 610322
  itemNum: 1
  groupId: 1
  weight: 5000
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 3
  }
}
rows {
  rewardId: 5361004
  poolId: 5361
  name: "兔耳宝匣"
  itemId: 620775
  itemNum: 1
  groupId: 1
  weight: 4900
  isGrand: true
  lvCost: 35
  inGroupLimit {
    period: 8
    limit: 2
  }
}
rows {
  rewardId: 5361005
  poolId: 5361
  name: "海洋小兔"
  itemId: 630495
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5361006
  poolId: 5361
  name: "银铃手鼓"
  itemId: 640126
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5361007
  poolId: 5361
  name: "糖果背包"
  itemId: 620573
  itemNum: 1
  groupId: 2
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5361008
  poolId: 5361
  name: "雪茸茸"
  itemId: 410230
  itemNum: 1
  groupId: 2
  weight: 3
  inGroupLimit {
    period: 4
    limit: 1
  }
}
rows {
  rewardId: 5361009
  poolId: 5361
  name: "凤之钥*20"
  itemId: 226
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 3
}
rows {
  rewardId: 5361010
  poolId: 5361
  name: "凤之钥*10"
  itemId: 226
  itemNum: 10
  groupId: 3
  weight: 20
  limit: 5
  inGroupLimit {
    period: 20
    limit: 1
  }
  comboItemIds: 226
  comboItemIds: 226
  comboItemIds: 226
  comboItemNums: 10
  comboItemNums: 20
  comboItemNums: 30
  comboType: RCT_Always
  comboItemWeights: 350
  comboItemWeights: 40
  comboItemWeights: 10
}
rows {
  rewardId: 5361011
  poolId: 5361
  name: "凤之钥*5"
  itemId: 226
  itemNum: 5
  groupId: 3
  weight: 60
  inGroupLimit {
    period: 20
    limit: 4
  }
  comboItemIds: 226
  comboItemIds: 226
  comboItemIds: 226
  comboItemNums: 5
  comboItemNums: 10
  comboItemNums: 15
  comboType: RCT_Always
  comboItemWeights: 300
  comboItemWeights: 80
  comboItemWeights: 20
}
rows {
  rewardId: 5361012
  poolId: 5361
  name: "凤之钥*2"
  itemId: 226
  itemNum: 2
  groupId: 3
  weight: 120
  inGroupLimit {
    period: 20
    limit: 15
  }
  comboItemIds: 226
  comboItemIds: 226
  comboItemIds: 226
  comboItemNums: 2
  comboItemNums: 4
  comboItemNums: 6
  comboType: RCT_Always
  comboItemWeights: 200
  comboItemWeights: 125
  comboItemWeights: 50
}
rows {
  rewardId: 5361013
  poolId: 5361
  name: "凤之钥*1"
  itemId: 226
  itemNum: 1
  groupId: 4
  weight: 100
  comboItemIds: 226
  comboItemIds: 226
  comboItemIds: 226
  comboItemNums: 1
  comboItemNums: 2
  comboItemNums: 3
  comboType: RCT_Always
  comboItemWeights: 160
  comboItemWeights: 160
  comboItemWeights: 80
}
rows {
  rewardId: 5361014
  poolId: 5361
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5361015
  poolId: 5361
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5361016
  poolId: 5361
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 50
}
rows {
  rewardId: 5361017
  poolId: 5361
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 4
  weight: 50
}
rows {
  rewardId: 5361018
  poolId: 5361
  name: "落樱衬衫上装"
  itemId: 510322
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5361019
  poolId: 5361
  name: "落樱衬衫下装"
  itemId: 520217
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5361020
  poolId: 5361
  name: "落樱衬衫手套"
  itemId: 530193
  itemNum: 1
  groupId: 4
  weight: 30
}
rows {
  rewardId: 5361021
  poolId: 5361
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 50
}
rows {
  rewardId: 3001201
  poolId: 30012
  name: "预言家 西比尔"
  itemId: 410390
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001202
  poolId: 30012
  name: "圣光预言家 西比尔"
  itemId: 410400
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001203
  poolId: 30012
  name: "幻耀神石"
  itemId: 630534
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001204
  poolId: 30012
  name: "星辰之纱"
  itemId: 610332
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001205
  poolId: 30012
  name: "星术秘谕"
  itemId: 620791
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001206
  poolId: 30012
  name: "潜行狼王 雅各布"
  itemId: 410410
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  inGroupChooseEnhance {
    firstGuarantee: 1
  }
  ifShow: 1
}
rows {
  rewardId: 3001207
  poolId: 30012
  name: "精灵猎人 米兰达"
  itemId: 410420
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  inGroupChooseEnhance {
    firstGuarantee: 1
  }
  ifShow: 1
}
rows {
  rewardId: 3001208
  poolId: 30012
  name: "蔷薇之毒"
  itemId: 630531
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001209
  poolId: 30012
  name: "星语捕梦网"
  itemId: 620794
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001210
  poolId: 30012
  name: "小羊咩咩"
  itemId: 610335
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001211
  poolId: 30012
  name: "头像框-洞察未来"
  itemId: 840263
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3001212
  poolId: 30012
  name: "查验身份中"
  itemId: 720985
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001213
  poolId: 30012
  name: "狼人别过来"
  itemId: 720986
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001214
  poolId: 30012
  name: "在叫我吗"
  itemId: 720763
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001215
  poolId: 30012
  name: "泡澡时光"
  itemId: 711030
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001216
  poolId: 30012
  name: "举栗"
  itemId: 710260
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001217
  poolId: 30012
  name: "嘴馋了"
  itemId: 711040
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001218
  poolId: 30012
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3001219
  poolId: 30012
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3001220
  poolId: 30012
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3001221
  poolId: 30012
  name: "星梦捕手"
  itemId: 410450
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001222
  poolId: 30012
  name: "雅力士"
  itemId: 410460
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001223
  poolId: 30012
  name: "依芙琳"
  itemId: 410440
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001224
  poolId: 30012
  name: "摇骰子"
  itemId: 720989
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001225
  poolId: 30012
  name: "不舍道别"
  itemId: 720990
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001226
  poolId: 30012
  name: "优雅站定"
  itemId: 720991
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001227
  poolId: 30012
  name: "慵懒午后"
  itemId: 720992
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001228
  poolId: 30012
  name: "给你鼓劲"
  itemId: 720993
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001229
  poolId: 30012
  name: "比个心"
  itemId: 711247
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001230
  poolId: 30012
  name: "哎鸭鸭"
  itemId: 711248
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001231
  poolId: 30012
  name: "拜托了"
  itemId: 711249
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001232
  poolId: 30012
  name: "爬墙偷瞄"
  itemId: 711250
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001233
  poolId: 30012
  name: "萌动旋风上装"
  itemId: 510331
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001234
  poolId: 30012
  name: "萌动旋风下装"
  itemId: 520220
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001235
  poolId: 30012
  name: "萌动旋风手套"
  itemId: 530196
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001236
  poolId: 30012
  name: "悦动甜心上装"
  itemId: 510332
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001237
  poolId: 30012
  name: "悦动甜心下装"
  itemId: 520221
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001238
  poolId: 30012
  name: "悦动甜心手套"
  itemId: 530197
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001239
  poolId: 30012
  name: "原野兔踪上装"
  itemId: 510333
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001240
  poolId: 30012
  name: "原野兔踪下装"
  itemId: 520222
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001241
  poolId: 30012
  name: "原野兔踪手套"
  itemId: 530198
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001242
  poolId: 30012
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001243
  poolId: 30012
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001244
  poolId: 30012
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001245
  poolId: 30012
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001301
  poolId: 30013
  name: "超能魔导师 喵妮斯"
  itemId: 410970
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001302
  poolId: 30013
  name: "无限魔导师 喵妮斯"
  itemId: 410980
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001303
  poolId: 30013
  name: "星冕之核"
  itemId: 630580
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001304
  poolId: 30013
  name: "星钥秘杖"
  itemId: 640152
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001305
  poolId: 30013
  name: "心语密卷"
  itemId: 620864
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001306
  poolId: 30013
  name: "雷电之子 索尔"
  itemId: 410950
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  inGroupChooseEnhance {
    firstGuarantee: 1
  }
  ifShow: 1
}
rows {
  rewardId: 3001307
  poolId: 30013
  name: "变形术师 玛吉"
  itemId: 410960
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  inGroupChooseEnhance {
    firstGuarantee: 1
  }
  ifShow: 1
}
rows {
  rewardId: 3001308
  poolId: 30013
  name: "土豆芽芽乐"
  itemId: 630583
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001309
  poolId: 30013
  name: "雷霆视界"
  itemId: 610379
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001310
  poolId: 30013
  name: "林间猎影"
  itemId: 620579
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001311
  poolId: 30013
  name: "头像框-xxx"
  itemId: 840287
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3001312
  poolId: 30013
  name: "飘飘而立"
  itemId: 722030
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001313
  poolId: 30013
  name: "魔法攻击"
  itemId: 722031
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001314
  poolId: 30013
  name: "万象引"
  itemId: 722032
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001315
  poolId: 30013
  name: "星星眼发射中"
  itemId: 711399
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001316
  poolId: 30013
  name: "乖巧歪头"
  itemId: 711400
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001317
  poolId: 30013
  name: "美女震惊"
  itemId: 711401
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001318
  poolId: 30013
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3001319
  poolId: 30013
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3001320
  poolId: 30013
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3001321
  poolId: 30013
  name: "金小豹"
  itemId: 411020
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001322
  poolId: 30013
  name: "艺小萱"
  itemId: 411030
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001323
  poolId: 30013
  name: "程小维"
  itemId: 411050
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001324
  poolId: 30013
  name: "活跃狂欢"
  itemId: 720861
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001325
  poolId: 30013
  name: "欢呼雀跃"
  itemId: 720862
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001326
  poolId: 30013
  name: "萌萌舞蹈"
  itemId: 720864
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001327
  poolId: 30013
  name: "劈叉"
  itemId: 720797
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001328
  poolId: 30013
  name: "可爱歪头"
  itemId: 720771
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001329
  poolId: 30013
  name: "您先请"
  itemId: 711404
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001330
  poolId: 30013
  name: "瑟瑟发抖"
  itemId: 711405
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001331
  poolId: 30013
  name: "信心爆棚"
  itemId: 711406
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001332
  poolId: 30013
  name: "高调登场"
  itemId: 711411
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001333
  poolId: 30013
  name: "像素叠叠乐上装"
  itemId: 510363
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001334
  poolId: 30013
  name: "像素叠叠乐下装"
  itemId: 520241
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001335
  poolId: 30013
  name: "像素叠叠乐手套"
  itemId: 530217
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001336
  poolId: 30013
  name: "火树银花上装"
  itemId: 510364
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001337
  poolId: 30013
  name: "火树银花下装"
  itemId: 520242
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001338
  poolId: 30013
  name: "火树银花手套"
  itemId: 530218
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001339
  poolId: 30013
  name: "数字边缘上装"
  itemId: 510365
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001340
  poolId: 30013
  name: "数字边缘下装"
  itemId: 520243
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001341
  poolId: 30013
  name: "数字边缘手套"
  itemId: 530219
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001342
  poolId: 30013
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001343
  poolId: 30013
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001344
  poolId: 30013
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001345
  poolId: 30013
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 5126001
  poolId: 51260
  name: "龙虾小新"
  itemId: 400650
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5126002
  poolId: 51260
  name: "红黑墨镜"
  itemId: 610019
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5126003
  poolId: 51260
  name: "小新噢耶"
  itemId: 710051
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5126004
  poolId: 51260
  name: "小白背包"
  itemId: 620072
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5126005
  poolId: 51261
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126006
  poolId: 51261
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126007
  poolId: 51261
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5126008
  poolId: 51261
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126009
  poolId: 51261
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126010
  poolId: 51261
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126011
  poolId: 51261
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126012
  poolId: 51262
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126013
  poolId: 51262
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126014
  poolId: 51262
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5126015
  poolId: 51262
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126016
  poolId: 51262
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126017
  poolId: 51262
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126018
  poolId: 51262
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126019
  poolId: 51263
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126020
  poolId: 51263
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126021
  poolId: 51263
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5126022
  poolId: 51263
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126023
  poolId: 51263
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126024
  poolId: 51263
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126025
  poolId: 51263
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126026
  poolId: 51264
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126027
  poolId: 51264
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126028
  poolId: 51264
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5126029
  poolId: 51264
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126030
  poolId: 51264
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126031
  poolId: 51264
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5126032
  poolId: 51264
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127001
  poolId: 51270
  name: "动感超人小新"
  itemId: 400640
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5127002
  poolId: 51270
  name: "小新双色眼镜"
  itemId: 610020
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5127003
  poolId: 51270
  name: "动感超人表情"
  itemId: 710052
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5127004
  poolId: 51270
  name: "康达姆机器人"
  itemId: 620071
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5127005
  poolId: 51271
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127006
  poolId: 51271
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127007
  poolId: 51271
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5127008
  poolId: 51271
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127009
  poolId: 51271
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127010
  poolId: 51271
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127011
  poolId: 51271
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127012
  poolId: 51272
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127013
  poolId: 51272
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127014
  poolId: 51272
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5127015
  poolId: 51272
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127016
  poolId: 51272
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127017
  poolId: 51272
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127018
  poolId: 51272
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127019
  poolId: 51273
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127020
  poolId: 51273
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127021
  poolId: 51273
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5127022
  poolId: 51273
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127023
  poolId: 51273
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127024
  poolId: 51273
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127025
  poolId: 51273
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127026
  poolId: 51274
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127027
  poolId: 51274
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127028
  poolId: 51274
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5127029
  poolId: 51274
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127030
  poolId: 51274
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127031
  poolId: 51274
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5127032
  poolId: 51274
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128001
  poolId: 51280
  name: "睡衣小新"
  itemId: 403290
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5128002
  poolId: 51280
  name: "温泉毛巾"
  itemId: 630306
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5128003
  poolId: 51280
  name: "头好烫"
  itemId: 711076
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5128004
  poolId: 51280
  name: "哞哞眼罩"
  itemId: 610195
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5128005
  poolId: 51281
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128006
  poolId: 51281
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128007
  poolId: 51281
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5128008
  poolId: 51281
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128009
  poolId: 51281
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128010
  poolId: 51281
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128011
  poolId: 51281
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128012
  poolId: 51282
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128013
  poolId: 51282
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128014
  poolId: 51282
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5128015
  poolId: 51282
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128016
  poolId: 51282
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128017
  poolId: 51282
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128018
  poolId: 51282
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128019
  poolId: 51283
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128020
  poolId: 51283
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128021
  poolId: 51283
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5128022
  poolId: 51283
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128023
  poolId: 51283
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128024
  poolId: 51283
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128025
  poolId: 51283
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128026
  poolId: 51284
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128027
  poolId: 51284
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128028
  poolId: 51284
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5128029
  poolId: 51284
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128030
  poolId: 51284
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128031
  poolId: 51284
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5128032
  poolId: 51284
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129001
  poolId: 51290
  name: "左卫门 小新"
  itemId: 403310
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5129002
  poolId: 51290
  name: "鳄鱼山先生背包"
  itemId: 620448
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5129003
  poolId: 51290
  name: "自信满满"
  itemId: 711075
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5129004
  poolId: 51290
  name: "炫彩回旋镖"
  itemId: 640024
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 5129005
  poolId: 51291
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129006
  poolId: 51291
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129007
  poolId: 51291
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5129008
  poolId: 51291
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129009
  poolId: 51291
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129010
  poolId: 51291
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129011
  poolId: 51291
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129012
  poolId: 51292
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129013
  poolId: 51292
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129014
  poolId: 51292
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5129015
  poolId: 51292
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129016
  poolId: 51292
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129017
  poolId: 51292
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129018
  poolId: 51292
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129019
  poolId: 51293
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129020
  poolId: 51293
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129021
  poolId: 51293
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5129022
  poolId: 51293
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129023
  poolId: 51293
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129024
  poolId: 51293
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129025
  poolId: 51293
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129026
  poolId: 51294
  name: "星宝印章*2000"
  itemId: 4
  itemNum: 2000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129027
  poolId: 51294
  name: "蓝色服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129028
  poolId: 51294
  name: "幸运钥匙"
  groupId: 1
  weight: 4
  limit: 1
  isGrand: true
}
rows {
  rewardId: 5129029
  poolId: 51294
  name: "万能棉花*40"
  itemId: 6
  itemNum: 40
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129030
  poolId: 51294
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129031
  poolId: 51294
  name: "蓝色配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 5129032
  poolId: 51294
  name: "3级好感礼物*2"
  itemId: 200016
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 40000323
  poolId: 40000015
  name: "玩偶修复师 多莉"
  itemId: 410350
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
  isGrand: true
  ignoreRefresh: true
}
rows {
  rewardId: 40000324
  poolId: 40000015
  name: "粉红吱吱"
  itemId: 620778
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000325
  poolId: 40000015
  name: "趴趴猪"
  itemId: 630514
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000326
  poolId: 40000015
  name: "编织睛彩"
  itemId: 610325
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000327
  poolId: 40000015
  name: "甜心女仆 莉迪亚"
  itemId: 410300
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000328
  poolId: 40000015
  name: "萌犬管家 艾尔弗斯"
  itemId: 410290
  itemNum: 1
  groupId: 2
  weight: 4
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000329
  poolId: 40000015
  name: "缤纷糖果罐"
  itemId: 620781
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000330
  poolId: 40000015
  name: "爱心十字绷"
  itemId: 610328
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000331
  poolId: 40000015
  name: "午后茶歇"
  itemId: 630519
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
  afterRefreshLimit: 1
}
rows {
  rewardId: 40000332
  poolId: 40000015
  name: "乐桃桃"
  itemId: 410310
  itemNum: 1
  groupId: 2
  weight: 15
  limit: 1
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000333
  poolId: 40000015
  name: "疗愈天使"
  itemId: 840271
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 40000334
  poolId: 40000015
  name: "多莉头像"
  itemId: 860192
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 40000335
  poolId: 40000015
  name: "臻藏代币*20"
  itemId: 203
  itemNum: 20
  groupId: 2
  weight: 10
  limit: 3
  afterRefreshLimit: 2
}
rows {
  rewardId: 40000336
  poolId: 40000015
  name: "臻藏代币*5"
  itemId: 203
  itemNum: 5
  groupId: 3
  weight: 1
}
rows {
  rewardId: 40000337
  poolId: 40000015
  name: "臻藏代币*3"
  itemId: 203
  itemNum: 3
  groupId: 3
  weight: 5
}
rows {
  rewardId: 40000338
  poolId: 40000015
  name: "臻藏代币*2"
  itemId: 203
  itemNum: 2
  groupId: 3
  weight: 25
}
rows {
  rewardId: 40000339
  poolId: 40000015
  name: "臻藏代币*1"
  itemId: 203
  itemNum: 1
  groupId: 3
  weight: 30
}
rows {
  rewardId: 40000340
  poolId: 40000015
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 5
  weight: 4
}
rows {
  rewardId: 40000341
  poolId: 40000015
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 5
  weight: 5
}
rows {
  rewardId: 40000342
  poolId: 40000015
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 4
}
rows {
  rewardId: 40000343
  poolId: 40000015
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 5
  weight: 5
}
rows {
  rewardId: 40000344
  poolId: 40000015
  name: "棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 4
}
rows {
  rewardId: 40000345
  poolId: 40000015
  name: "棉花*10"
  itemId: 6
  itemNum: 10
  groupId: 5
  weight: 4
}
rows {
  rewardId: 40000346
  poolId: 40000015
  name: "印章*500"
  itemId: 4
  itemNum: 500
  groupId: 5
  weight: 2
}
rows {
  rewardId: 3001401
  poolId: 30014
  name: "超能魔导师 喵妮斯"
  itemId: 410970
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001402
  poolId: 30014
  name: "无限魔导师 喵妮斯"
  itemId: 410980
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: false
  ignoreRefresh: true
  ifShow: 1
}
rows {
  rewardId: 3001403
  poolId: 30014
  name: "星冕之核"
  itemId: 630580
  itemNum: 1
  groupId: 3
  weight: 3
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001404
  poolId: 30014
  name: "星钥秘杖"
  itemId: 640152
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001405
  poolId: 30014
  name: "心语密卷"
  itemId: 620864
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001406
  poolId: 30014
  name: "雷电之子 索尔"
  itemId: 410950
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  inGroupChooseEnhance {
    firstGuarantee: 1
  }
  ifShow: 1
}
rows {
  rewardId: 3001407
  poolId: 30014
  name: "变形术师 玛吉"
  itemId: 410960
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  inGroupChooseEnhance {
    firstGuarantee: 1
  }
  ifShow: 1
}
rows {
  rewardId: 3001408
  poolId: 30014
  name: "土豆芽芽乐"
  itemId: 630583
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001409
  poolId: 30014
  name: "雷霆视界"
  itemId: 610379
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001410
  poolId: 30014
  name: "林间猎影"
  itemId: 620579
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
  ifShow: 1
}
rows {
  rewardId: 3001411
  poolId: 30014
  name: "头像框-xxx"
  itemId: 840287
  itemNum: 1
  groupId: 11
  weight: 10
  limit: 1
  isGrand: false
  ignoreRefresh: true
}
rows {
  rewardId: 3001412
  poolId: 30014
  name: "飘飘而立"
  itemId: 722030
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001413
  poolId: 30014
  name: "魔法攻击"
  itemId: 722031
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001414
  poolId: 30014
  name: "万象引"
  itemId: 722032
  itemNum: 1
  groupId: 8
  weight: 40
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001415
  poolId: 30014
  name: "星星眼发射中"
  itemId: 711399
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001416
  poolId: 30014
  name: "乖巧歪头"
  itemId: 711400
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001417
  poolId: 30014
  name: "美女震惊"
  itemId: 711401
  itemNum: 1
  groupId: 8
  weight: 233
  limit: 1
  isGrand: false
  afterRefreshLimit: 1
}
rows {
  rewardId: 3001418
  poolId: 30014
  name: "万能棉花*15"
  itemId: 6
  itemNum: 15
  groupId: 12
  weight: 500
  isGrand: false
}
rows {
  rewardId: 3001419
  poolId: 30014
  name: "饰品染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 12
  weight: 100
  limit: 12
  isGrand: false
  afterRefreshLimit: 12
}
rows {
  rewardId: 3001420
  poolId: 30014
  name: "时装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 12
  weight: 50
  limit: 10
  isGrand: false
  afterRefreshLimit: 10
}
rows {
  rewardId: 3001421
  poolId: 30014
  name: "金小豹"
  itemId: 411020
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001422
  poolId: 30014
  name: "艺小萱"
  itemId: 411030
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 2
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001423
  poolId: 30014
  name: "程小维"
  itemId: 411050
  itemNum: 1
  groupId: 6
  weight: 306
  limit: 1
  isGrand: false
  afterRefreshLimit: 2
}
rows {
  rewardId: 3001424
  poolId: 30014
  name: "活跃狂欢"
  itemId: 720861
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001425
  poolId: 30014
  name: "欢呼雀跃"
  itemId: 720862
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001426
  poolId: 30014
  name: "萌萌舞蹈"
  itemId: 720864
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001427
  poolId: 30014
  name: "劈叉"
  itemId: 720797
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001428
  poolId: 30014
  name: "可爱歪头"
  itemId: 720771
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001429
  poolId: 30014
  name: "您先请"
  itemId: 711404
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001430
  poolId: 30014
  name: "瑟瑟发抖"
  itemId: 711405
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001431
  poolId: 30014
  name: "信心爆棚"
  itemId: 711406
  itemNum: 1
  groupId: 9
  weight: 306
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001432
  poolId: 30014
  name: "高调登场"
  itemId: 711411
  itemNum: 1
  groupId: 9
  weight: 280
  limit: 3
  isGrand: false
  afterRefreshLimit: 3
}
rows {
  rewardId: 3001433
  poolId: 30014
  name: "像素叠叠乐上装"
  itemId: 510363
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001434
  poolId: 30014
  name: "像素叠叠乐下装"
  itemId: 520241
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001435
  poolId: 30014
  name: "像素叠叠乐手套"
  itemId: 530217
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001436
  poolId: 30014
  name: "火树银花上装"
  itemId: 510364
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001437
  poolId: 30014
  name: "火树银花下装"
  itemId: 520242
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001438
  poolId: 30014
  name: "火树银花手套"
  itemId: 530218
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001439
  poolId: 30014
  name: "数字边缘上装"
  itemId: 510365
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001440
  poolId: 30014
  name: "数字边缘下装"
  itemId: 520243
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001441
  poolId: 30014
  name: "数字边缘手套"
  itemId: 530219
  itemNum: 1
  groupId: 7
  weight: 677
  limit: 4
  isGrand: false
  afterRefreshLimit: 4
}
rows {
  rewardId: 3001442
  poolId: 30014
  name: "亲密度道具1"
  itemId: 200014
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001443
  poolId: 30014
  name: "亲密度道具2"
  itemId: 200015
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001444
  poolId: 30014
  name: "亲密度道具3"
  itemId: 200016
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 3001445
  poolId: 30014
  name: "亲密度道具4"
  itemId: 200017
  itemNum: 1
  groupId: 10
  weight: 100
  isGrand: false
}
rows {
  rewardId: 80006001
  poolId: 8000601
  groupId: 1
  weight: 980
  earningPoints: 1
}
rows {
  rewardId: 80006002
  poolId: 8000601
  groupId: 1
  weight: 15
  earningPoints: 2
}
rows {
  rewardId: 80006003
  poolId: 8000601
  groupId: 1
  weight: 5
  earningPoints: 3
}
rows {
  rewardId: 80006004
  poolId: 8000601
  name: "鎏金硬币"
  itemId: 224
  itemNum: 5
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 7
    limit: 5
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80006005
  poolId: 8000601
  name: "鎏金硬币"
  itemId: 224
  itemNum: 8
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 7
    limit: 1
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80006006
  poolId: 8000601
  name: "鎏金硬币"
  itemId: 224
  itemNum: 15
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 7
    limit: 1
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80006007
  poolId: 8000602
  groupId: 1
  weight: 980
  earningPoints: 1
}
rows {
  rewardId: 80006008
  poolId: 8000602
  groupId: 1
  weight: 15
  earningPoints: 2
}
rows {
  rewardId: 80006009
  poolId: 8000602
  groupId: 1
  weight: 5
  earningPoints: 3
}
rows {
  rewardId: 80006010
  poolId: 8000602
  name: "鎏金硬币"
  itemId: 224
  itemNum: 10
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 6
    limit: 1
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80006011
  poolId: 8000602
  name: "鎏金硬币"
  itemId: 224
  itemNum: 20
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 6
    limit: 2
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80006012
  poolId: 8000602
  name: "鎏金硬币"
  itemId: 224
  itemNum: 30
  groupId: 2
  weight: 1
  earningPoints: 0
  inGroupLimit {
    period: 6
    limit: 3
  }
  sortId: 8
  sortName: "基础奖池"
}
rows {
  rewardId: 80006013
  poolId: 8000610
  name: "星辉幼角"
  itemId: 730010
  itemNum: 1
  groupId: 1
  weight: 680
  isGrand: true
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80006014
  poolId: 8000610
  name: "星澜灵角"
  itemId: 730011
  itemNum: 1
  groupId: 1
  weight: 0
  isGrand: true
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80006015
  poolId: 8000610
  name: "璀璨星翼"
  itemId: 730013
  itemNum: 1
  groupId: 1
  weight: 0
  isGrand: true
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80006016
  poolId: 8000610
  name: "突破石*10"
  itemId: 225
  itemNum: 10
  groupId: 1
  weight: 10
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80006017
  poolId: 8000610
  name: "突破石*5"
  itemId: 225
  itemNum: 5
  groupId: 1
  weight: 1200
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80006018
  poolId: 8000610
  name: "突破石*3"
  itemId: 225
  itemNum: 3
  groupId: 1
  weight: 110
  earningPoints: -9
  inGroupLimit {
    period: 2
    limit: 1
  }
  sortId: 1
  sortName: "惊喜大奖"
}
rows {
  rewardId: 80006019
  poolId: 8000609
  name: "蝴蝶翅膀"
  itemId: 620368
  itemNum: 1
  groupId: 1
  weight: 120
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80006020
  poolId: 8000609
  name: "大白鲨"
  itemId: 640105
  itemNum: 1
  groupId: 1
  weight: 400
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80006021
  poolId: 8000609
  name: "独角兽角"
  itemId: 630484
  itemNum: 1
  groupId: 1
  weight: 400
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80006022
  poolId: 8000609
  name: "突破石*2"
  itemId: 225
  itemNum: 2
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80006023
  poolId: 8000609
  name: "突破石*1"
  itemId: 225
  itemNum: 1
  groupId: 1
  weight: 40
  inGroupLimit {
    period: 3
    limit: 3
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80006024
  poolId: 8000609
  name: "丘比特"
  itemId: 404630
  itemNum: 1
  groupId: 1
  weight: 40
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 2
  sortName: "奖池（♥ × 6）"
}
rows {
  rewardId: 80006025
  poolId: 8000608
  name: "大白鲨"
  itemId: 640105
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80006026
  poolId: 8000608
  name: "独角兽角"
  itemId: 630484
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80006027
  poolId: 8000608
  name: "突破石*1"
  itemId: 225
  itemNum: 1
  groupId: 1
  weight: 5
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80006028
  poolId: 8000608
  name: "丘比特"
  itemId: 404630
  itemNum: 1
  groupId: 1
  weight: 300
  limit: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80006029
  poolId: 8000608
  name: "圣诞小鹿男"
  itemId: 404780
  itemNum: 1
  groupId: 1
  weight: 300
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80006030
  poolId: 8000608
  name: "圣诞小鹿女"
  itemId: 404790
  itemNum: 1
  groupId: 1
  weight: 300
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 3
  sortName: "奖池（♥ × 5）"
}
rows {
  rewardId: 80006031
  poolId: 8000607
  name: "突破石*1"
  itemId: 225
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80006032
  poolId: 8000607
  name: "圣诞小鹿男"
  itemId: 404780
  itemNum: 1
  groupId: 1
  weight: 40
  limit: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80006033
  poolId: 8000607
  name: "圣诞小鹿女"
  itemId: 404790
  itemNum: 1
  groupId: 1
  weight: 40
  limit: 1
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80006034
  poolId: 8000607
  name: "丘比特之弓"
  itemId: 640102
  itemNum: 1
  groupId: 1
  weight: 240
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80006035
  poolId: 8000607
  name: "倒扣冰淇淋"
  itemId: 630426
  itemNum: 1
  groupId: 1
  weight: 240
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80006036
  poolId: 8000607
  name: "头饰-鸟窝"
  itemId: 630445
  itemNum: 1
  groupId: 1
  weight: 240
  inGroupLimit {
    period: 4
    limit: 1
  }
  sortId: 4
  sortName: "奖池（♥ × 4）"
}
rows {
  rewardId: 80006037
  poolId: 8000606
  name: "【紫动】伸手接雪花"
  itemId: 720910
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 5
  sortName: "奖池（♥ × 3）"
}
rows {
  rewardId: 80006038
  poolId: 8000606
  name: "【紫动】吹泡泡"
  itemId: 720911
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 5
  sortName: "奖池（♥ × 3）"
}
rows {
  rewardId: 80006039
  poolId: 8000606
  name: "【紫动】扔球杂耍"
  itemId: 720912
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 5
  sortName: "奖池（♥ × 3）"
}
rows {
  rewardId: 80006040
  poolId: 8000605
  name: "【蓝配】背饰-香菇串"
  itemId: 620604
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 6
  sortName: "奖池（♥ × 2）"
}
rows {
  rewardId: 80006041
  poolId: 8000605
  name: "【蓝配】头饰-生日帽"
  itemId: 630395
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 6
  sortName: "奖池（♥ × 2）"
}
rows {
  rewardId: 80006042
  poolId: 8000605
  name: "【蓝配】头饰-白菜"
  itemId: 630328
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 6
  sortName: "奖池（♥ × 2）"
}
rows {
  rewardId: 80006043
  poolId: 8000604
  name: "拥抱侏罗纪套装"
  itemId: 310284
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 7
  sortName: "奖池（♥ × 1）"
}
rows {
  rewardId: 80006044
  poolId: 8000604
  name: "漆墨拼图套装"
  itemId: 310285
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 7
  sortName: "奖池（♥ × 1）"
}
rows {
  rewardId: 80006045
  poolId: 8000604
  name: "蓝庭花语套装"
  itemId: 310286
  itemNum: 1
  groupId: 1
  weight: 1
  inGroupLimit {
    period: 3
    limit: 1
  }
  sortId: 7
  sortName: "奖池（♥ × 1）"
}
