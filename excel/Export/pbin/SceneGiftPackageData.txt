com.tencent.wea.xlsRes.table_SceneGiftPackageConf
excel/xls/C_场景礼包.xlsx sheet:场景礼包
rows {
  id: 1
  type: SGPT_Default
  sort: 1
  commodityIds: 160001
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 2
      value: 3
    }
  }
  bugExpireTime: 12
  beginTime {
    seconds: 1699200000
  }
  endTime {
    seconds: 1699200000
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 2
  type: SGPT_Default
  sort: 2
  commodityIds: 160002
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 2
      value: 2
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1699200000
  }
  endTime {
    seconds: 1699200000
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 4
  type: SGPT_PickOneOfTwo
  sort: 4
  commodityIds: 160003
  commodityIds: 160004
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 2
      value: 5
    }
    condition {
      conditionType: 2
      value: 9
    }
  }
  bugExpireTime: 24
  originCommodityIds: 160005
  originCommodityIds: 160006
  beginTime {
    seconds: 1699632000
  }
  endTime {
    seconds: 1699718400
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_SelectableGift"
}
rows {
  id: 5
  type: SGPT_Default
  sort: 1
  commodityIds: 160009
  pushConditions {
    conditionRelation: ConditionRelation_Or
    condition {
      conditionType: 40
      value: 10
      subConditionList {
        type: 20
        value: 30002
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1706889600
  }
  endTime {
    seconds: 1711036800
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 6
  type: SGPT_Default
  sort: 1
  commodityIds: 160016
  pushConditions {
    conditionRelation: ConditionRelation_Or
    condition {
      conditionType: 43
      value: 5
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1711036800
  }
  endTime {
    seconds: 1711900799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 8
  type: SGPT_PickOneOfTwo
  sort: 1
  commodityIds: 120019
  commodityIds: 120010
  commodityIds: 120011
  pushConditions {
    conditionRelation: ConditionRelation_Or
    condition {
      conditionType: 43
      value: 5
      subConditionList {
        type: 147
        value: 2
      }
    }
  }
  bugExpireTime: 1
  beginTime {
    seconds: 1709222400
  }
  endTime {
    seconds: 4102412400
  }
  isExclusiveWindow: true
  sceneIds: 3
  widgetName: "UI_SceneGift_SelectableGift"
}
rows {
  id: 9
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160017
  commodityIds: 160018
  commodityIds: 160019
  commodityIds: 160020
  pushConditions {
    conditionRelation: ConditionRelation_Or
    condition {
      conditionType: 2
      value: 5
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.90.76"
  beginTime {
    seconds: 1712851200
  }
  endTime {
    seconds: 1713283199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 10
  type: SGPT_Default
  sort: 1
  commodityIds: 160021
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 179
      value: 1
      subConditionList {
        type: 153
        value: 401440
        value: 1
        value: 0
      }
      subConditionList {
        type: 153
        value: 401445
        value: 1
        value: 0
      }
    }
    condition {
      conditionType: 43
      value: 5
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.90.96"
  beginTime {
    seconds: 1713369600
  }
  endTime {
    seconds: 1713970800
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 11
  type: SGPT_Default
  sort: 1
  commodityIds: 160022
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.90.96"
  beginTime {
    seconds: 1714665600
  }
  endTime {
    seconds: 1715529599
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 12
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160023
  commodityIds: 160024
  commodityIds: 160025
  commodityIds: 160026
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.90.76"
  beginTime {
    seconds: 1716048000
  }
  endTime {
    seconds: 1716739199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 13
  type: SGPT_FirstChargePush
  sort: 1
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 5
    }
  }
  minVersion: "1.2.100.1"
  beginTime {
    seconds: 1716480000
  }
  endTime {
    seconds: 4080124800
  }
  isExclusiveWindow: false
  sceneIds: 3
}
rows {
  id: 14
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160028
  commodityIds: 160029
  commodityIds: 160030
  commodityIds: 160031
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.100.99"
  beginTime {
    seconds: 1717430400
  }
  endTime {
    seconds: 1718035199
  }
  isExclusiveWindow: false
  sceneIds: 2
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 15
  type: SGPT_Default
  sort: 1
  commodityIds: 160032
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
      subConditionList {
        type: 167
        value: 1
      }
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 401750
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.100.99"
  beginTime {
    seconds: 1718985600
  }
  endTime {
    seconds: 1720108799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 16
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160033
  commodityIds: 160034
  commodityIds: 160035
  commodityIds: 160036
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
      subConditionList {
        type: 167
        value: 1
      }
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 20
      value: 1
      subConditionList {
        type: 3
        value: 401750
      }
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 640004
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.100.99"
  beginTime {
    seconds: 1718985600
  }
  endTime {
    seconds: 1720108799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 17
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160037
  commodityIds: 160038
  commodityIds: 160039
  commodityIds: 160040
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
      subConditionList {
        type: 167
        value: 0
      }
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.2.100.99"
  beginTime {
    seconds: 1718899200
  }
  endTime {
    seconds: 1720108799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 18
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160041
  commodityIds: 160042
  commodityIds: 160043
  commodityIds: 160044
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "*********"
  beginTime {
    seconds: 1721059200
  }
  endTime {
    seconds: 1721663999
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 19
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160045
  commodityIds: 160046
  commodityIds: 160047
  commodityIds: 160048
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.12.20"
  beginTime {
    seconds: 1722268800
  }
  endTime {
    seconds: 1722873599
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 20
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160049
  commodityIds: 160050
  commodityIds: 160051
  commodityIds: 160052
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.12.95"
  beginTime {
    seconds: 1723478400
  }
  endTime {
    seconds: 1724083199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 21
  type: SGPT_Default
  sort: 1
  commodityIds: 160053
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 46
      value: 1
      subConditionList {
        type: 2
        value: 5500
        value: 5600
        value: 5601
        value: 6006
      }
      subConditionList {
        type: 189
        value: 0
        value: 1014
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1723737600
  }
  endTime {
    seconds: 1724342399
  }
  isExclusiveWindow: false
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 22
  type: SGPT_BIRecommend
  sort: 1
  commodityIds: 160054
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1797695998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift2"
}
rows {
  id: 23
  type: SGPT_BIRecommend
  sort: 1
  commodityIds: 160055
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1797695998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift2"
}
rows {
  id: 24
  type: SGPT_BIRecommend
  sort: 1
  commodityIds: 160056
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1797695998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift2"
}
rows {
  id: 25
  type: SGPT_BIRecommend
  sort: 1
  commodityIds: 160057
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1797695998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift2"
}
rows {
  id: 26
  type: SGPT_BIRecommend
  sort: 1
  commodityIds: 160058
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1797695998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift2"
}
rows {
  id: 27
  type: SGPT_BIRecommend
  sort: 1
  commodityIds: 160059
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1797695998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift2"
}
rows {
  id: 28
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160060
  commodityIds: 160061
  commodityIds: 160062
  commodityIds: 160063
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.12.100"
  beginTime {
    seconds: 1724688000
  }
  endTime {
    seconds: 1725292799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 29
  type: SGPT_Default
  sort: 1
  commodityIds: 160064
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 46
      value: 1
      subConditionList {
        type: 2
        value: 5500
        value: 5600
        value: 5601
        value: 6006
      }
      subConditionList {
        type: 189
        value: 0
        value: 1019
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1725033600
  }
  endTime {
    seconds: 1725206399
  }
  isExclusiveWindow: false
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 30
  type: SGPT_Default
  sort: 1
  commodityIds: 160065
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.12.100"
  beginTime {
    seconds: 1725206400
  }
  endTime {
    seconds: 1725811199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 31
  type: SGPT_Default
  sort: 2
  commodityIds: 160066
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 46
      value: 1
      subConditionList {
        type: 2
        value: 5500
        value: 5600
        value: 5601
        value: 6006
      }
      subConditionList {
        type: 189
        value: 0
        value: 1015
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1725552000
  }
  endTime {
    seconds: 1725811199
  }
  isExclusiveWindow: false
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 32
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160067
  commodityIds: 160068
  commodityIds: 160069
  commodityIds: 160070
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.18.23"
  beginTime {
    seconds: 1725638400
  }
  endTime {
    seconds: 1726243199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 36
  type: SGPT_Default
  sort: 1
  commodityIds: 160074
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 4
      value: 1
      subConditionList {
        type: 2
        value: 4
        value: 5
        value: 6
      }
    }
    condition {
      conditionType: 179
      value: 1
      subConditionList {
        type: 153
        value: 400930
        value: 1
        value: 0
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1725552000
  }
  endTime {
    seconds: 1726156799
  }
  isExclusiveWindow: false
  sceneIds: 2
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 37
  type: SGPT_Default
  sort: 1
  commodityIds: 160075
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.18.37"
  beginTime {
    seconds: 1726416000
  }
  endTime {
    seconds: 1727107199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 38
  type: SGPT_Default
  sort: 1
  commodityIds: 160076
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 46
      value: 1
      subConditionList {
        type: 2
        value: 5500
        value: 5600
        value: 5601
        value: 6006
      }
      subConditionList {
        type: 189
        value: 0
        value: 1017
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.18.37"
  beginTime {
    seconds: 1726156800
  }
  endTime {
    seconds: 1726329599
  }
  isExclusiveWindow: false
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 39
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160081
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 40
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160082
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 41
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160083
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 42
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160084
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 43
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160085
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 44
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160086
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 45
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160087
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 46
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160088
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 48
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160090
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 49
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160091
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 50
  type: SGPT_RetrningRecommend
  sort: 1
  commodityIds: 160092
  bugExpireTime: 1
  minVersion: "1.3.18.39"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1766159998
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift3"
}
rows {
  id: 51
  type: SGPT_Default
  sort: 1
  commodityIds: 160093
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 46
      value: 1
      subConditionList {
        type: 2
        value: 5500
        value: 5600
        value: 5601
        value: 6006
      }
      subConditionList {
        type: 189
        value: 0
        value: 1018
      }
    }
  }
  bugExpireTime: 3
  minVersion: "1.3.18.56"
  beginTime {
    seconds: 1726761600
  }
  endTime {
    seconds: 1727020799
  }
  isExclusiveWindow: false
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 52
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160094
  commodityIds: 160095
  commodityIds: 160096
  commodityIds: 160097
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.18.73"
  beginTime {
    seconds: 1727366400
  }
  endTime {
    seconds: 1728316799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 53
  type: SGPT_ArenaShowInfo
  sort: 1
  commodityIds: 160076
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 1
    }
  }
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1730217598
  }
  isExclusiveWindow: false
  sceneIds: 7
  frequency: Once
  tips_1: "测试文本1"
  tips_2: "测试文本2"
  title: "标题文本1"
  imgUrl: "T_Shop_Img_Sale_64.png"
  jumpId: 50101
  strategy: DonotShow
  widgetName: "UI_Arena_PopTopInfo"
}
rows {
  id: 54
  type: SGPT_ArenaJump
  sort: 1
  commodityIds: 160076
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 1
    }
  }
  bugExpireTime: 3
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1730217598
  }
  isExclusiveWindow: false
  sceneIds: 7
  frequency: EveryLogIn
  tips_1: "测试文本3"
  tips_2: "测试文本4"
  title: "标题文本2"
  imgUrl: "T_Shop_Img_Sale_64.png"
  jumpId: 50101
  strategy: ShowCommodity
  widgetName: "UI_Arena_PopTopGo"
}
rows {
  id: 55
  type: SGPT_ArenaBuy
  sort: 3
  commodityIds: 1101302
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 46
      value: 1
      subConditionList {
        type: 2
        value: 5600
        value: 5601
        value: 6006
      }
      subConditionList {
        type: 189
        value: 0
        value: 1018
      }
    }
  }
  bugExpireTime: 3
  beginTime {
    seconds: 1730649600
  }
  endTime {
    seconds: 1734623998
  }
  isExclusiveWindow: false
  sceneIds: 7
  frequency: EveryLogIn
  tips_1: "限时英雄星钻特惠！"
  title: "限时福利"
  imgUrl: "T_Arena_Role_1018"
  strategy: ShowCommodity
  widgetName: "UI_Arena_PopTopBuy"
}
rows {
  id: 56
  type: SGPT_Default
  sort: 1
  commodityIds: 160098
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "**********"
  beginTime {
    seconds: 1729526400
  }
  endTime {
    seconds: 1730044799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 57
  type: SGPT_Default
  sort: 1
  commodityIds: 160099
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "**********"
  beginTime {
    seconds: 1730044800
  }
  endTime {
    seconds: 1731254399
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 58
  type: SGPT_IAALotteryTimeout
  sort: 1
  commodityIds: 11038
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 1
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1729008000
  }
  endTime {
    seconds: 1731254399
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_WXGame_LotteryDiscountPop"
}
rows {
  id: 59
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160100
  commodityIds: 160101
  commodityIds: 160102
  commodityIds: 160103
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.26.60"
  beginTime {
    seconds: 1730736000
  }
  endTime {
    seconds: 1731254399
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 60
  type: SGPT_FarmLimitGift
  sort: 1
  commodityIds: 160104
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.26.60"
  beginTime {
    seconds: 1728057600
  }
  endTime {
    seconds: 1731340799
  }
  isExclusiveWindow: false
  sceneIds: 3
}
rows {
  id: 61
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160105
  commodityIds: 160106
  commodityIds: 160107
  commodityIds: 160108
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.26.81"
  beginTime {
    seconds: 1731340800
  }
  endTime {
    seconds: 1731859199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 62
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160109
  commodityIds: 160110
  commodityIds: 160111
  commodityIds: 160112
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 610155
      }
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 620193
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.26.93"
  beginTime {
    seconds: 1731945600
  }
  endTime {
    seconds: 1732463999
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 63
  type: SGPT_Default
  sort: 1
  commodityIds: 160113
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 630240
      }
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 640032
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.37.1"
  beginTime {
    seconds: 1733155200
  }
  endTime {
    seconds: 1733673599
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 65
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160114
  commodityIds: 160115
  commodityIds: 160116
  commodityIds: 160117
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.37.28"
  beginTime {
    seconds: 1732377600
  }
  endTime {
    seconds: 1735660799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 64
  type: SGPT_ArenaBuyHero
  sort: 1
  commodityIds: 1101302
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 46
      value: 1
      subConditionList {
        type: 2
        value: 5600
        value: 5601
        value: 6006
        value: 6101
        value: 6102
      }
      subConditionList {
        type: 189
        value: 0
        value: 1018
      }
      subConditionList {
        type: 204
        value: 4
        value: 5
      }
    }
  }
  bugExpireTime: 72
  minVersion: "********"
  beginTime {
    seconds: 1722441600
  }
  endTime {
    seconds: 1734623998
  }
  isExclusiveWindow: false
  sceneIds: 7
  frequency: EveryDay
  tips_1: "第一行文本第一行文本第一行文本第一行文本第一行文本第一行文本第一行文本第一行文本第一行文本"
  title: "商品购买"
  imgUrl: "xinshoulibao_1.astc"
  strategy: ShowCommodity
}
rows {
  id: 70
  type: SGPT_SeasonGiftPackage
  sort: 1
  commodityIds: 120010
  commodityIds: 120011
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 283
      value: 0
      subConditionList {
        type: 242
        value: 30012
      }
    }
    condition {
      conditionType: 43
      value: 5
    }
  }
  bugExpireTime: 999999
  beginTime {
    seconds: 1732723200
  }
  endTime {
    seconds: 1765900799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_Lottery_SeasonSubView_CutGift"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 71
  type: SGPT_SeasonGiftPackage
  sort: 1
  commodityIds: 120010
  commodityIds: 120011
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 283
      value: 10000000
      subConditionList {
        type: 242
        value: 311111
      }
    }
    condition {
      conditionType: 5
      value: 10000000
    }
  }
  bugExpireTime: 1
  beginTime {
    seconds: 1732723200
  }
  endTime {
    seconds: 1741881599
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_Lottery_SeasonSubView_CutGift"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 75
  type: SGPT_Default
  sort: 1
  commodityIds: 160118
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.68.3"
  beginTime {
    seconds: 1737388800
  }
  endTime {
    seconds: 1737907199
  }
  isExclusiveWindow: false
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 76
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160119
  commodityIds: 160120
  commodityIds: 160121
  commodityIds: 160122
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.68.51"
  beginTime {
    seconds: 1738339200
  }
  endTime {
    seconds: 1739980799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 77
  type: SGPT_IAALotteryTimeout
  sort: 1
  commodityIds: 160127
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 202
      value: 1
      subConditionList {
        type: 3
        value: 640052
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1729008000
  }
  endTime {
    seconds: 1767110398
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 6
  widgetName: "UI_WXGame_LotteryDiscountPop"
  pushLoginPlat: 4
}
rows {
  id: 78
  type: SGPT_IAALotteryTimeout
  sort: 1
  commodityIds: 160128
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 202
      value: 1
      subConditionList {
        type: 3
        value: 620226
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1729008000
  }
  endTime {
    seconds: 1767110398
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 6
  widgetName: "UI_WXGame_LotteryDiscountPop"
  pushLoginPlat: 4
}
rows {
  id: 79
  type: SGPT_IAALotteryTimeout
  sort: 1
  commodityIds: 160129
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 202
      value: 1
      subConditionList {
        type: 3
        value: 620338
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1729008000
  }
  endTime {
    seconds: 1767110398
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 6
  widgetName: "UI_WXGame_LotteryDiscountPop"
  pushLoginPlat: 4
}
rows {
  id: 80
  type: SGPT_IAALotteryTimeout
  sort: 1
  commodityIds: 160130
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 202
      value: 1
      subConditionList {
        type: 3
        value: 640002
      }
    }
  }
  bugExpireTime: 24
  beginTime {
    seconds: 1729008000
  }
  endTime {
    seconds: 1767110398
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  sceneIds: 3
  sceneIds: 6
  widgetName: "UI_WXGame_LotteryDiscountPop"
  pushLoginPlat: 4
}
rows {
  id: 81
  type: SGPT_FirstChargePush
  sort: 1
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 3
    }
    condition {
      conditionType: 5
      value: 1
    }
  }
  bugExpireTime: 999999
  minVersion: "1.2.100.1"
  beginTime {
    seconds: 1716480000
  }
  endTime {
    seconds: 4080124800
  }
  isExclusiveWindow: false
  sceneIds: 3
  frequency: EveryDay
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 82
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160131
  commodityIds: 160132
  commodityIds: 160133
  commodityIds: 160134
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.68.101"
  beginTime {
    seconds: 1740412800
  }
  endTime {
    seconds: 1740931199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 83
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160135
  commodityIds: 160136
  commodityIds: 160137
  commodityIds: 160138
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "*********"
  beginTime {
    seconds: 1740931200
  }
  endTime {
    seconds: 1743436799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 84
  type: SGPT_MultipleBuy
  sort: 1
  commodityIds: 120216
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 164
      value: 3
    }
  }
  bugExpireTime: 3
  beginTime {
    seconds: 1738512000
  }
  endTime {
    seconds: 1751212799
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 85
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160139
  commodityIds: 160140
  commodityIds: 160141
  commodityIds: 160142
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 403240
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.68.122"
  beginTime {
    seconds: 1740585600
  }
  endTime {
    seconds: 1742140799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 86
  type: SGPT_Default
  sort: 1
  commodityIds: 160143
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.78.1"
  beginTime {
    seconds: 1742227200
  }
  endTime {
    seconds: 1742745599
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 87
  type: SGPT_IOSIceBreakGift
  sort: 1
  commodityIds: 160145
  commodityIds: 160146
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 0
    }
  }
  bugExpireTime: 168
  beginTime {
    seconds: 1741017600
  }
  endTime {
    seconds: 1745423999
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 6
  frequency: EveryDayOnce
  widgetName: "UI_WXGame_IosRechargePop2"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 88
  type: SGPT_IOSIceBreakCharge
  sort: 2
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 0
    }
  }
  bugExpireTime: 168
  beginTime {
    seconds: 1741017600
  }
  endTime {
    seconds: 1745423999
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 6
  frequency: EveryDayOnce
  widgetName: "UI_WXGame_IosRechargePop"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
  giftChargeTask: 50286
}
rows {
  id: 89
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160147
  commodityIds: 160148
  commodityIds: 160149
  commodityIds: 160150
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 630364
      }
    }
  }
  bugExpireTime: 24
  minVersion: "*********"
  beginTime {
    seconds: 1742572800
  }
  endTime {
    seconds: 1743091199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 90
  type: SGPT_MoneyTree
  sort: 1
  commodityIds: 160151
  commodityIds: 160152
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
  }
  bugExpireTime: 0
  minVersion: "1.3.68.3"
  conflictIds: 92
  beginTime {
    seconds: 1737388800
  }
  endTime {
    seconds: 1745683199
  }
  isExclusiveWindow: false
  sceneIds: 6
  frequency: EveryDayOnce
  widgetName: "UI_WXGame_IosCashCowPop"
  pushLoginPlat: 4
  bugExpireTimeMinute: 30
  resetType: 1
  ruleID: 999
}
rows {
  id: 91
  type: SGPT_PickOneOfMulti
  sort: 1
  commodityIds: 160153
  commodityIds: 160154
  commodityIds: 160155
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "*********"
  beginTime {
    seconds: 1747065600
  }
  endTime {
    seconds: 1751299199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_Recharge_ThreeChoiceGift_Mainview"
}
rows {
  id: 92
  type: SGPT_MoneyTree
  sort: 1
  commodityIds: 160164
  commodityIds: 160165
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
  }
  bugExpireTime: 0
  minVersion: "1.3.68.3"
  conflictIds: 90
  beginTime {
    seconds: 1737388800
  }
  endTime {
    seconds: 1745683199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_WXGame_IosCashCowPop"
  pushLoginPlat: 3
  bugExpireTimeMinute: 30
  resetType: 0
  ruleID: 999
}
rows {
  id: 93
  type: SGPT_Default
  sort: 1
  commodityIds: 160157
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 403930
      }
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 640039
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.78.73"
  beginTime {
    seconds: 1744041600
  }
  endTime {
    seconds: 1744387199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 94
  type: SGPT_Default
  sort: 1
  commodityIds: 160158
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.78.99"
  beginTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1745251199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 95
  type: SGPT_Default
  sort: 1
  commodityIds: 160159
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "********"
  beginTime {
    seconds: 1746201600
  }
  endTime {
    seconds: 1746547199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 96
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160160
  commodityIds: 160161
  commodityIds: 160162
  commodityIds: 160163
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "********"
  beginTime {
    seconds: 1746720000
  }
  endTime {
    seconds: 1747324799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 97
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160166
  commodityIds: 160167
  commodityIds: 160168
  commodityIds: 160169
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "*********"
  beginTime {
    seconds: 1747411200
  }
  endTime {
    seconds: 1747670400
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 98
  type: SGPT_MultipleBuy
  sort: 1
  commodityIds: 120217
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 10
    }
    condition {
      conditionType: 164
      value: 5
    }
  }
  bugExpireTime: 3
  beginTime {
    seconds: 1738512000
  }
  endTime {
    seconds: 1751212799
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 99
  type: SGPT_MultipleBuy
  sort: 1
  commodityIds: 120218
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 15
    }
    condition {
      conditionType: 164
      value: 15
    }
  }
  bugExpireTime: 3
  beginTime {
    seconds: 1738512000
  }
  endTime {
    seconds: 1751212799
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  widgetName: "UI_SceneGift_TimeLimitGift"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 101
  type: SGPT_SeasonGiftPackage
  sort: 1
  commodityIds: 120010
  commodityIds: 120011
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 283
      value: 1
      subConditionList {
        type: 242
        value: 30013
      }
    }
    condition {
      conditionType: 43
      value: 5
    }
  }
  bugExpireTime: 24
  minVersion: "********"
  beginTime {
    seconds: 1747411200
  }
  endTime {
    seconds: 1750953599
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_Lottery_SeasonSubView_CutGift"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
}
rows {
  id: 102
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160170
  commodityIds: 160171
  commodityIds: 160172
  commodityIds: 160173
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "********"
  beginTime {
    seconds: 1748275200
  }
  endTime {
    seconds: 1748879999
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 103
  type: SGPT_Default
  sort: 1
  commodityIds: 160174
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 610210
      }
    }
  }
  bugExpireTime: 24
  minVersion: "********"
  beginTime {
    seconds: 1748880000
  }
  endTime {
    seconds: 1749484799
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 104
  type: SGPT_MultipleBuy
  sort: 1
  commodityIds: 120240
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 15
    }
    condition {
      conditionType: 208
      value: 1
    }
  }
  bugExpireTime: 3
  beginTime {
    seconds: 1738512000
  }
  endTime {
    seconds: 1751212799
  }
  isExclusiveWindow: false
  sceneIds: 1
  sceneIds: 2
  widgetName: "UI_Arena_Preparation_HotBuy_Gift"
  pushLoginPlat: 2
  pushLoginPlat: 4
  pushLoginPlat: 5
  pushLoginPlat: 6
  pakGroup: 50021
}
rows {
  id: 105
  type: SGPT_LevelBuy
  sort: 1
  commodityIds: 160175
  commodityIds: 160176
  commodityIds: 160177
  commodityIds: 160178
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 1
    }
    condition {
      conditionType: 5
      value: 6
    }
    condition {
      conditionType: 205
      value: 1
      subConditionList {
        type: 3
        value: 640001
      }
    }
  }
  bugExpireTime: 24
  minVersion: "1.3.88.93"
  beginTime {
    seconds: 1747674000
  }
  endTime {
    seconds: 1748275199
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_CascadeGift"
}
rows {
  id: 106
  type: SGPT_Default
  sort: 1
  commodityIds: 160179
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "********55"
  beginTime {
    seconds: 1750089600
  }
  endTime {
    seconds: 1750607999
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
rows {
  id: 107
  type: SGPT_Default
  sort: 1
  commodityIds: 160180
  pushConditions {
    conditionRelation: ConditionRelation_And
    condition {
      conditionType: 43
      value: 5
    }
    condition {
      conditionType: 5
      value: 6
    }
  }
  bugExpireTime: 24
  minVersion: "********55"
  beginTime {
    seconds: 1749744000
  }
  endTime {
    seconds: 1751817599
  }
  isExclusiveWindow: false
  sceneIds: 3
  widgetName: "UI_SceneGift_TimeLimitGift"
}
