com.tencent.wea.xlsRes.table_ItemToyMiscConfData
excel/xls/D_道具表_玩具.xlsx sheet:杂项配置
rows {
  stringId: "UI_DisplayBoard_Sting_InputMaxNum"
  content: "10"
}
rows {
  stringId: "manifestoCharacterSizeMax"
  content: "10"
}
rows {
  stringId: "UI_DisplayBoard_Sting_InputRecordNum"
  content: "10"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str1"
  content: "弹幕配文"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str2"
  content: "快捷配文"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str3"
  content: "清空"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str4"
  content: "保存并建房"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str5"
  content: "保存"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str6"
  content: "【滴滴卡】将会展示设置保存的地图封面"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str7"
  content: "快捷配文"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str8"
  content: "输入记录"
}
rows {
  stringId: "UI_DisplayBoard_Sting_Str9"
  content: "点击输入配文（最多10个字)"
}
rows {
  stringId: "UI_DisplayBoard_Bag_Button_Name"
  content: "设置滴滴卡"
}
rows {
  stringId: "UI_DisplayBoard_Setting_Save_Success"
  content: "保存设置成功"
}
rows {
  stringId: "mapSettingHistoryMax"
  content: "100"
}
rows {
  stringId: "manifestoHistoryMax"
  content: "7"
}
rows {
  stringId: "ViewMapButtonIcon"
  content: "CDN:T_InGame_Btn_Find"
}
rows {
  stringId: "ViewMapButtonText"
  content: "进图地图房间"
}
rows {
  stringId: "EnterRoomButtonIcon"
  content: "CDN:T_InGame_Btn_Enter"
}
rows {
  stringId: "EnterRoomButtonText"
  content: "查看地图"
}
