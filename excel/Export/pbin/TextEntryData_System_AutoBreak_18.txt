com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置_System.xlsx sheet:文本配置
rows {
  content: "需{0}关系达到<Orange2>{1}</>级"
  switch: 1
  stringId: "CommodityBuyCondition2"
}
rows {
  content: "获取默契币可兑换"
  switch: 1
  stringId: "UI_Season_EarnIntimateCoinsPass_Txt"
}
rows {
  content: "提升默契等级可兑换"
  switch: 1
  stringId: "UI_Season_EarnIntimateLevelPass_Txt"
}
rows {
  content: "当前匹配需要下载{0}"
  switch: 1
  stringId: "UI_MatchPakDetail_DownloadTip"
}
rows {
  content: "剩余{0}"
  switch: 1
  stringId: "UI_MatchPakDetail_LeftDownloadTime"
}
rows {
  content: "每邀请1名新玩家或回归玩家可得<CallUp>星愿币</>和<CallUp>抽奖机会</>"
  switch: 1
  stringId: "CallUpActivity_TabTitle_Invite"
}
rows {
  content: "已邀请玩家累计登录天数达标后可获得<CallUp>召集能量</>和<CallUp>抽奖机会</>"
  switch: 1
  stringId: "CallUpActivity_TabTitle_Login"
}
rows {
  content: "萌新任务"
  switch: 1
  stringId: "CallUpActivity_ReturnTabName_New"
}
rows {
  content: "回归任务"
  switch: 1
  stringId: "CallUpActivity_ReturnTabName_Return"
}
rows {
  content: "登录天数达标后自己与邀请者均可获得<CallUp>召集能量</>和<CallUp>抽奖机会</>"
  switch: 1
  stringId: "CallUpActivity_TabTitle_ReturnPlayer"
}
rows {
  content: "登录天数达标后自己与邀请者均可获得<CallUp>召集能量</>和<CallUp>抽奖机会</>"
  switch: 1
  stringId: "CallUpActivity_TabTitle_NewPlayer"
}
rows {
  content: "每邀请1名新玩家或回归玩家<SummerCallUp>可获得1次抽奖机会</>"
  switch: 1
  stringId: "CallUpActivity_SubTitle_Invite"
}
rows {
  content: "已邀请玩家登录天数达标<SummerCallUp>双方均可获得抽奖机会</>"
  switch: 1
  stringId: "CallUpActivity_SubTitle_Login"
}
rows {
  content: "每个成功邀请的好友累计登录天数\n达标后可获得以下奖励"
  switch: 1
  stringId: "CallUpActivity_TabTitle_LoginEmpty"
}
rows {
  content: "暂停中…"
  switch: 1
  stringId: "UI_MatchPakDetail_DownloadPauseTip"
}
rows {
  content: "排队中…"
  switch: 1
  stringId: "UI_MatchPakDetail_DownloadWaitTip"
}
