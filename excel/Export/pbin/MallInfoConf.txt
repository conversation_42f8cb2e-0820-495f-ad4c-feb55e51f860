com.tencent.wea.xlsRes.table_MallInfoConf
excel/xls/S_商城_兑换类型.xlsx sheet:商城和兑换类型
rows {
  mallId: 4
  shopType: ST_SeasonExchange
  mallName: "赛季兑换"
}
rows {
  mallId: 5
  shopType: ST_BPExchange
  mallName: "BP兑换"
}
rows {
  mallId: 6
  shopType: ST_MallSuit
  mallName: "商城套装"
}
rows {
  mallId: 7
  shopType: ST_MallUpperGarment
  mallName: "商城上装"
}
rows {
  mallId: 8
  shopType: ST_MallLowerGarment
  mallName: "商城下装"
}
rows {
  mallId: 9
  shopType: ST_MallGloves
  mallName: "商城手套"
}
rows {
  mallId: 10
  shopType: ST_MallFaceOrnament
  mallName: "商城面饰"
}
rows {
  mallId: 11
  shopType: ST_MallHeadWear
  mallName: "商城头饰"
}
rows {
  mallId: 12
  shopType: ST_MallBackOrnament
  mallName: "商城背饰"
}
rows {
  mallId: 13
  shopType: ST_MallEmoji
  mallName: "商城表情"
}
rows {
  mallId: 14
  shopType: ST_MallMovement
  mallName: "商城动作"
}
rows {
  mallId: 15
  shopType: ST_MallItem
  mallName: "商城道具"
}
rows {
  mallId: 39
  shopType: ST_MallVehicle
  mallName: "商城载具"
}
rows {
  mallId: 16
  shopType: ST_MallHot
  mallName: "特殊用途不要使用"
}
rows {
  mallId: 17
  shopType: ST_CurrencyExchange
  mallName: "货币互换"
}
rows {
  mallId: 18
  shopType: ST_LotteryExchange
  mallName: "抽奖兑换"
}
rows {
  mallId: 20
  shopType: ST_MallDirectBuy
  mallName: "特惠礼包"
}
rows {
  mallId: 21
  shopType: ST_MallAvatarGift
  mallName: "Avatar超值礼包"
}
rows {
  mallId: 22
  shopType: ST_ReturningUserBuy
  mallName: "回归直购礼包"
}
rows {
  mallId: 23
  shopType: ST_ActivityNormalExchange
  mallName: "【运营活动】常规兑换"
}
rows {
  mallId: 24
  shopType: ST_ActivityChapterTaskExchange
  mallName: "【运营活动】章节任务"
}
rows {
  mallId: 25
  shopType: ST_ActivityBigRewardExchange
  mallName: "【运营活动】大奖常规兑换"
}
rows {
  mallId: 26
  shopType: ST_MallFace
  mallName: "商城脸部"
}
rows {
  mallId: 27
  shopType: ST_MallInteractiveProp
  mallName: "商城互动道具"
}
rows {
  mallId: 28
  shopType: ST_MallDirectBuy
  mallName: "月卡礼包"
}
rows {
  mallId: 29
  shopType: ST_UgcDailyStage
  mallName: "巡游商城"
}
rows {
  mallId: 30
  shopType: ST_UgcStarCruise
  mallName: "新巡游商城"
}
rows {
  mallId: 31
  shopType: ST_SceneGiftPackage
  mallName: "场景礼包"
}
rows {
  mallId: 32
  shopType: ST_ActivityTakeaway
  mallName: "挂机外卖"
}
rows {
  mallId: 33
  shopType: ST_LotteryPoolExchange
  mallName: "月光女神兑换商店"
}
rows {
  mallId: 34
  shopType: ST_LotteryActivityPoolExchange
  mallName: "星光剧场兑换商店"
}
rows {
  mallId: 35
  shopType: ST_MallPairMovement
  mallName: "双人动作"
}
rows {
  mallId: 36
  shopType: ST_AccumulateBlessingsExchange
  mallName: "积攒福气兑换商店"
}
rows {
  mallId: 37
  shopType: ST_MallNewYearExchange
  mallName: "春节庆典"
}
rows {
  mallId: 38
  shopType: ST_RecruiteExchange
  mallName: "召集令商店"
}
rows {
  mallId: 40
  shopType: ST_ReturnFireExchange
  mallName: "回归火种兑换商店"
}
rows {
  mallId: 41
  shopType: ST_ReturnChargeSignIn
  mallName: "回归付费签到抽奖"
}
rows {
  mallId: 42
  shopType: ST_RaffleDiamondExchange
  mallName: "抽奖星钻兑换"
}
rows {
  mallId: 43
  shopType: ST_MallHandOrnament
  mallName: "商城手部装扮"
}
rows {
  mallId: 44
  shopType: ST_TacitAgreementShop
  mallName: "默契商店"
}
rows {
  mallId: 45
  shopType: ST_FootPrint
  mallName: "商城脚印"
}
rows {
  mallId: 46
  shopType: ST_Velarium
  mallName: "商城登场秀"
}
rows {
  mallId: 80
  shopType: ST_TYCExchange
  mallName: "塔防大亨水晶商店"
}
rows {
  mallId: 101
  shopType: ST_BlackPrince
  mallName: "黑暗王子商城"
}
rows {
  mallId: 102
  shopType: ST_SquadActivityExchange
  mallName: "【策划活动】多人组队兑换商城"
}
rows {
  mallId: 103
  shopType: ST_FuwanfaExchange
  mallName: "【策划活动】赛季兑换商店"
}
rows {
  mallId: 104
  shopType: ST_PermanentExchange
  mallName: "常驻兑换商店"
}
rows {
  mallId: 105
  shopType: ST_MallUniveralLottery
  mallName: "永恒之誓商城"
}
rows {
  mallId: 107
  shopType: ST_LotteryBlueSuitPoolExchange
  mallName: "蓝装卡池兑换"
}
rows {
  mallId: 108
  shopType: ST_MallKnightExchange
  mallName: "顶奢商城"
}
rows {
  mallId: 109
  shopType: ST_SecondaryGameplay_JSCar
  mallName: "商城-宝库-飞车商店"
}
rows {
  mallId: 110
  shopType: ST_SecondaryGameplay_Wolfkill
  mallName: "商城-宝库-谁是狼人商店"
}
rows {
  mallId: 111
  shopType: ST_SecondaryGameplay_Farm
  mallName: "商城-宝库-农场商店"
}
rows {
  mallId: 112
  shopType: ST_ArenaCardPack
  mallName: "峡谷相逢卡包商城"
}
rows {
  mallId: 119
  shopType: ST_MallFarmFishing
  mallName: "农场钓鱼道具"
}
rows {
  mallId: 120
  shopType: ST_MallFarmItem
  mallName: "商城-农场道具"
}
rows {
  mallId: 121
  shopType: ST_ActivityFridayCollectionExchange
  mallName: "【运营活动】星期五导航栏兑换"
}
rows {
  mallId: 122
  shopType: ST_PermitExchange
  mallName: "通行证兑换"
}
rows {
  mallId: 130
  shopType: ST_Mall_Farm
  mallName: "商店-农场"
}
rows {
  mallId: 131
  shopType: ST_Mall_JSCar
  mallName: "商店-飞车"
}
rows {
  mallId: 132
  shopType: ST_Mall_Wolfkill
  mallName: "商店-狼人杀"
}
rows {
  mallId: 133
  shopType: ST_Mall_Arena
  mallName: "商店-Moba"
}
rows {
  mallId: 134
  shopType: ST_Mall_Shuttle
  mallName: "商店-星梭"
}
rows {
  mallId: 140
  shopType: ST_LotteryMall_ChangXiangSi
  mallName: "长相思"
}
rows {
  mallId: 141
  shopType: ST_HalfAnniversaryExchange
  mallName: "半周年庆兑换"
}
rows {
  mallId: 142
  shopType: ST_GroupReturning
  mallName: "拼团返利"
}
rows {
  mallId: 143
  shopType: ST_MallUniversalQixi
  mallName: "凤求凰商城"
}
rows {
  mallId: 144
  shopType: ST_MallUniversalSwing
  mallName: "秋千卡池"
}
rows {
  mallId: 145
  shopType: ST_MallUniversalHonor2
  mallName: "王者二期商店"
}
rows {
  mallId: 146
  shopType: ST_MallLottery_Chiikawa
  mallName: "chiikawa兑换商店"
}
rows {
  mallId: 147
  shopType: ST_MallLottery_MidAutumn
  mallName: "嫦娥兑换商店"
}
rows {
  mallId: 148
  shopType: ST_MallAvatarBuy
  mallName: "moba皮肤礼包"
}
rows {
  mallId: 149
  shopType: ST_WeekenIceBroken
  mallName: "周末破冰活动"
}
rows {
  mallId: 150
  shopType: ST_WolfKillDecorate
  mallName: "狼人杀装饰商店"
}
rows {
  mallId: 152
  shopType: ST_MallUniversalHonor4
  mallName: "王者四期商店"
}
rows {
  mallId: 153
  shopType: ST_ActivityCommon
  mallName: "祈福牌"
}
rows {
  mallId: 154
  shopType: ST_ActivityCommonShopType
  mallName: "活动写死商品ID使用的通用商城类型"
}
rows {
  mallId: 155
  shopType: ST_NR3E1Decorate
  mallName: "躲猫猫装饰商店"
}
rows {
  mallId: 156
  shopType: ST_DoubleEvelevenCoinTree
  mallName: "双十一摇钱树"
}
rows {
  mallId: 157
  shopType: ST_NewMall_Festival
  mallName: "新商城-限时礼包"
}
rows {
  mallId: 158
  shopType: ST_NewMall_Special
  mallName: "新商城-特色礼包"
}
rows {
  mallId: 159
  shopType: ST_LotteryMall_FengQiuHuang2
  mallName: "商店-凤求凰2"
}
rows {
  mallId: 160
  shopType: ST_ActivityActivityWerewolfFullReduce
  mallName: "狼人满减活动商品"
}
rows {
  mallId: 161
  shopType: ST_ThemeMall
  mallName: "主题商城"
}
rows {
  mallId: 162
  shopType: ST_ThemeMall2
  mallName: "冰雪精灵兑换商店"
}
rows {
  mallId: 163
  shopType: ST_NR3E1Shop
  mallName: "商店-躲猫猫"
}
rows {
  mallId: 164
  shopType: ST_MallUniversalZaiju
  mallName: "商店-载具"
}
rows {
  mallId: 165
  shopType: ST_VehicleUpgrade
  mallName: "载具升级"
}
rows {
  mallId: 167
  shopType: ST_DailyFarmShop
  mallName: "农场兑换商城"
}
rows {
  mallId: 168
  shopType: ST_ActivityNewYearActivities
  mallName: "活动-春节活跃"
}
rows {
  mallId: 169
  shopType: ST_ActivityFashionFund
  mallName: "活动-时装基金"
}
rows {
  mallId: 172
  shopType: ST_MallUniversalHonor6
  mallName: "商店-王者卡池备用"
}
rows {
  mallId: 173
  shopType: ST_MallCardSystem
  mallName: "兑换星愿币"
}
rows {
  mallId: 174
  shopType: ST_LotteryAvatarGift
  mallName: "商店-祈愿礼包"
}
rows {
  mallId: 175
  shopType: ST_MobaShardExchange
  mallName: "峡谷碎片兑换"
}
rows {
  mallId: 176
  shopType: ST_YouLeYuan
  mallName: "活动-特色玩法游乐园"
}
rows {
  mallId: 177
  shopType: ST_MallCardSystem
  mallName: "兑换星愿币"
}
rows {
  mallId: 188
  shopType: ST_MallNE3E8
  mallName: "商店-NR3E8"
}
rows {
  mallId: 190
  shopType: ST_ChaseIdentity
  mallName: "大王别抓我身份商店"
}
rows {
  mallId: 191
  shopType: ST_Painter
  mallName: "二次元画家商城"
}
rows {
  mallId: 192
  shopType: ST_TravelingDog
  mallName: "旅行小狗商城"
}
rows {
  mallId: 193
  shopType: ST_MallUniversalHonorTest
  mallName: "王者测试商店"
}
rows {
  mallId: 194
  shopType: ST_UGC_BP_Exchange
  mallName: "星世界漫游商店测试"
}
rows {
  mallId: 195
  shopType: ST_Mall_Surroundings
  mallName: "商店-环绕物"
}
rows {
  mallId: 196
  shopType: ST_LotteryButterfly
  mallName: "蝶舞花间兑换"
}
rows {
  mallId: 197
  shopType: ST_MallCardSystem
  mallName: "兑换星愿币"
}
rows {
  mallId: 198
  shopType: ST_OneRMBRaffle
  mallName: "一元抽奖活动兑换"
}
rows {
  mallId: 200
  shopType: ST_DailyMobaShop
  mallName: "Moba天天领"
}
rows {
  mallId: 201
  shopType: ST_SummerNavigationBar
  mallName: "暑期导航栏兑换"
}
rows {
  mallId: 202
  shopType: ST_DisplayBoard
  mallName: "推图板兑换"
}
rows {
  mallId: 203
  shopType: ST_ChaseCoinMall
  mallName: "大王币兑换商店"
}
rows {
  mallId: 204
  shopType: ST_TreasureHuntMall
  mallName: "秘境寻宝兑换商店"
}
