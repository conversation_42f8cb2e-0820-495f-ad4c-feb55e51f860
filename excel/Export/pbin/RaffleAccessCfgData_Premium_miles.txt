com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_miles.xlsx sheet:活动-臻藏卡池
rows {
  raffleId: 40000001
  name: "桂月清平"
  startTime {
    seconds: 1726156800
  }
  endTime {
    seconds: 1732809599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 40000001
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 223
  text: "首次祈愿1次免费，首次10连祈愿享有5折优惠"
  lowestVersion: "1.3.18.54"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310201
    giftIds: 310202
    giftIds: 310203
    giftIds: 310204
    giftIds: 310205
  }
  jumpIds: 25
  viewIndex: 1
  viewIndex: 6
  viewIndex: 5
  viewIndex: 2
  viewIndex: 4
  viewIndex: 10
  viewIndex: 3
  showStartTime {
    seconds: 1726156800
  }
  showEndTime {
    seconds: 1732809599
  }
  isShow: true
  drawOverState: 4
  animType: 4
  relateUmgSetting: "7;UI_Lottery_RewardNotice|DrawReward_MidAutumnText;;403570"
}
rows {
  raffleId: 40000002
  name: "chiikawa"
  startTime {
    seconds: 1716825600
  }
  endTime {
    seconds: 1748361600
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 40000002
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 270
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.12.47"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 50
    atDraws: 80
    atDraws: 120
    giftIds: 310347
    giftIds: 310348
    giftIds: 310349
    giftIds: 310350
    giftIds: 310351
  }
  jumpIds: 25
  commodityIds: 120034
  commodityIds: 120035
  commodityIds: 120036
  commodityIds: 120037
  viewIndex: 16
  viewIndex: 15
  viewIndex: 14
  viewIndex: 13
  viewIndex: 12
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  showStartTime {
    seconds: 1748361600
  }
  showEndTime {
    seconds: 1748361600
  }
  isShow: true
  drawOverState: 2
  relateUmgSetting: "5;UI_Lottery_Chiikawa_ChoiceView#3;UI_Lottery_GloryRoadPanel#8;UI_Lottery_Chiikawa_SelectReward"
}
rows {
  raffleId: 40000008
  name: "冰雪圆舞曲"
  startTime {
    seconds: 1735833600
  }
  endTime {
    seconds: 1740671999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 40000008
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 285
  text: "首次祈愿奖励2选1，首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.37.68"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 60
    atDraws: 120
    atDraws: 180
    giftIds: 310201
    giftIds: 310202
    giftIds: 310203
    giftIds: 310204
    giftIds: 310205
  }
  jumpIds: 25
  viewIndex: 1
  viewIndex: 6
  viewIndex: 5
  viewIndex: 2
  viewIndex: 4
  viewIndex: 10
  viewIndex: 3
  showStartTime {
    seconds: 1735833600
  }
  showEndTime {
    seconds: 1740671999
  }
  isShow: true
  drawOverState: 4
  animType: 4
  relateUmgSetting: "8;UI_Lottery_IceWaltz_SelectReward"
}
rows {
  raffleId: 40000018
  name: "chiikawa"
  startTime {
    seconds: 1716825600
  }
  endTime {
    seconds: 1758297599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 40000002
  }
  dailyLimit: 300
  maxLimit: 999999
  textRuleId: 270
  text: "首次祈愿10次享有5折优惠"
  lowestVersion: "1.3.12.47"
  milestone {
    atDraws: 10
    atDraws: 30
    atDraws: 50
    atDraws: 80
    atDraws: 120
    giftIds: 310347
    giftIds: 310348
    giftIds: 310349
    giftIds: 310350
    giftIds: 310351
  }
  jumpIds: 25
  commodityIds: 120034
  commodityIds: 120035
  commodityIds: 120036
  commodityIds: 120037
  viewIndex: 16
  viewIndex: 15
  viewIndex: 14
  viewIndex: 13
  viewIndex: 12
  viewIndex: 4
  viewIndex: 2
  viewIndex: 1
  viewIndex: 3
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  viewIndex: 8
  viewIndex: 9
  viewIndex: 10
  viewIndex: 11
  showStartTime {
    seconds: 1716825600
  }
  showEndTime {
    seconds: 1758297599
  }
  isShow: true
  drawOverState: 1
  relateUmgSetting: "5;UI_Lottery_Chiikawa_ChoiceView#3;UI_Lottery_GloryRoadPanel#8;UI_Lottery_Chiikawa_SelectReward"
}
