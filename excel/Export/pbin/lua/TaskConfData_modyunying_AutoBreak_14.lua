--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_特色玩法运营.xlsx: 活动任务

local data = {
[651702] = {
id = 651702,
name = "完成10局谁是狼人排位模式",
desc = "完成10局谁是狼人排位模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 10,
subConditionList = {
{
type = 2,
value = {
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
200179
},
numList = {
1
}
},
jumpId = 1024,
taskGroupId = 1023021
},
[651703] = {
id = 651703,
name = "谁是狼人达到白银段位",
desc = "谁是狼人达到白银段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20011
}
},
{
type = 32,
value = {
2,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
100
}
},
jumpId = 1024,
taskGroupId = 1023021
},
[651704] = {
id = 651704,
name = "谁是狼人达到黄金段位",
desc = "谁是狼人达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20011
}
},
{
type = 32,
value = {
3,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
240410
},
numList = {
1
}
},
jumpId = 1024,
taskGroupId = 1023021
},
[651705] = {
id = 651705,
name = "谁是狼人达到铂金段位",
desc = "谁是狼人达到铂金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20011
}
},
{
type = 32,
value = {
4,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
711391
},
numList = {
1
}
},
jumpId = 1024,
taskGroupId = 1023021
},
[651706] = {
id = 651706,
name = "谁是狼人达到钻石段位",
desc = "谁是狼人达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20011
}
},
{
type = 32,
value = {
5,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
722048
},
numList = {
1
}
},
jumpId = 1024,
taskGroupId = 1023021
},
[651707] = {
id = 651707,
name = "谁是狼人达到星耀段位",
desc = "谁是狼人达到星耀段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20011
}
},
{
type = 32,
value = {
6,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
410710
},
numList = {
1
}
},
jumpId = 1024,
taskGroupId = 1023021
},
[659436] = {
id = 659436,
name = "农场上线猫咪新萌宠啦！去看看",
desc = "农场上线猫咪新萌宠啦！去看看",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
5100
}
}
}
}
}
}
},
reward = {
itemIdList = {
317161
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022152
},
[659437] = {
id = 659437,
name = "【每日】在星宝农场中浇水15次",
desc = "【每日】在星宝农场中浇水15次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 156,
value = 15,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317161
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022150
},
[659438] = {
id = 659438,
name = "【每日】在好友农场祈福5次",
desc = "【每日】在好友农场祈福5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 5
}
}
}
},
reward = {
itemIdList = {
317161
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022150
},
[659439] = {
id = 659439,
name = "【每周】游玩【农场喵喵番外】星世界地图",
desc = "【每周】游玩【农场喵喵番外】星世界地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204946721
}
}
}
}
}
}
},
reward = {
itemIdList = {
317161
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 83,
taskGroupId = 1022151
},
[659440] = {
id = 659440,
name = "【每周】在星宝农场钓鱼60次",
desc = "【每周】在星宝农场钓鱼60次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 290,
value = 60
}
}
}
},
reward = {
itemIdList = {
317161
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022151
},
[659441] = {
id = 659441,
name = "【累计】通过泡温泉获得3次增益",
desc = "【累计】通过泡温泉获得3次增益",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 900,
value = 3,
subConditionList = {
{
type = 600,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317161
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022152
},
[659442] = {
id = 659442,
name = "喵星人来袭-兑换",
desc = "喵星人来袭-兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
317161
}
}
}
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022153
},
[659443] = {
id = 659443,
name = "喵星人来袭-兑换",
desc = "喵星人来袭-兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
317161
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022153
},
[659444] = {
id = 659444,
name = "喵星人来袭-兑换",
desc = "喵星人来袭-兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
317161
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022153
},
[659445] = {
id = 659445,
name = "喵星人来袭-兑换",
desc = "喵星人来袭-兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 60,
subConditionList = {
{
type = 3,
value = {
317161
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
15
},
validPeriodList = {
0
}
},
taskGroupId = 1022153
},
[659446] = {
id = 659446,
name = "喵星人来袭-兑换",
desc = "喵星人来袭-兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 100,
subConditionList = {
{
type = 3,
value = {
317161
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1022153
},
[659447] = {
id = 659447,
name = "喵星人来袭-兑换",
desc = "喵星人来袭-兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 130,
subConditionList = {
{
type = 3,
value = {
317161
}
}
}
}
}
}
},
reward = {
itemIdList = {
860225
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022153
},
[781010] = {
id = 781010,
name = "完成1局峡谷5v5排位",
desc = "完成1局峡谷5v5排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329962
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078940
},
[781011] = {
id = 781011,
name = "完成2局峡谷5v5排位",
desc = "完成2局峡谷5v5排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329962
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078940
},
[781012] = {
id = 781012,
name = "【每日】登录领取峡谷排位升星券",
desc = "【每日】登录领取峡谷排位升星券",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
203012
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078941
},
[781013] = {
id = 781013,
name = "完成3局峡谷5v5排位",
desc = "完成3局峡谷5v5排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
722039
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078942
},
[781014] = {
id = 781014,
name = "完成5局峡谷5v5排位",
desc = "完成5局峡谷5v5排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
331036
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078942
},
[781015] = {
id = 781015,
name = "【累计】游玩1次【峡谷擂台赛】",
desc = "【累计】游玩1次【峡谷擂台赛】",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204946721
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 83,
taskGroupId = 1078942
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data