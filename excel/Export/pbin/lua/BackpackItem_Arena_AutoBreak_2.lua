--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_Arena.xlsx: Arena物件

local v0 = 1

local v1 = 0

local v2 = "IUTO_ArenaHeroTry"

local v3 = "IUTO_ArenaCard"

local v4 = "50021"

local v5 = "T_Arena_Img_BigCard"

local data = {
[271040] = {
id = 271040,
type = "ItemType_Arena_HeadPic",
stackedNum = 1,
maxNum = 1,
name = "项羽头像",
desc = "获取后可使用英雄项羽的峡谷专用头像",
icon = "CDN:CT_Arena_SquareHero_1040",
showInView = 1,
isShowPopRewardView = 1
},
[271048] = {
id = 271048,
type = "ItemType_Arena_HeadPic",
stackedNum = 1,
maxNum = 1,
name = "艾琳头像",
desc = "获取后可使用英雄艾琳的峡谷专用头像",
icon = "CDN:CT_Arena_SquareHero_1046",
showInView = 1,
isShowPopRewardView = 1
},
[301020] = {
id = 301020,
type = "ItemType_Arena_HeroTry",
name = "刘禅体验卡(1天)",
desc = "使用后可免费体验英雄刘禅。",
icon = "CDN:CT_Arena_SquareHero_1043",
useType = v2,
useParam = {
1043
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
},
[301135] = {
id = 301135,
type = "ItemType_Arena_HeroTry",
quality = 2,
name = "伽罗",
desc = "（限峡谷模式使用）获取后可以使用射手伽罗。",
icon = "CDN:CT_Arena_SquareHero_1038",
useType = v2,
useParam = {
1038
},
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 60
},
autoUse = true,
isShowPopRewardView = 1
},
[301136] = {
id = 301136,
type = "ItemType_Arena_HeroTry",
quality = 2,
name = "刘备",
desc = "（限峡谷模式使用）获取后可以使用战士刘备。",
icon = "CDN:CT_Arena_SquareHero_1042",
useType = v2,
useParam = {
1042
},
resourceConf = {
model = "SK_PL_235_moba",
physics = "SK_PL_235_moba_Physics"
},
outEnter = "as_ch_enter_pl_235_moba",
outIdle = "AS_CH_OutIdle_001",
outShow = "AS_CH_IdleShow_PL_235_moba",
outShowIntervalTime = 10,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 200
},
autoUse = true,
isShowPopRewardView = 1
},
[301137] = {
id = 301137,
type = "ItemType_Arena_HeroTry",
quality = 2,
name = "甄姬",
desc = "（限峡谷模式使用）获取后可以使用法师甄姬。",
icon = "CDN:CT_Arena_SquareHero_1037",
useType = v2,
useParam = {
1037
},
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 60
},
autoUse = true,
isShowPopRewardView = 1
},
[301139] = {
id = 301139,
type = "ItemType_Arena_HeroTry",
quality = 2,
name = "大乔",
desc = "（限峡谷模式使用）获取后可以使用辅助大乔。",
icon = "CDN:CT_Arena_SquareHero_1039",
useType = v2,
useParam = {
1039
},
resourceConf = {
model = "SK_PL_241_moba",
physics = "SK_PL_241_moba_Physics"
},
outEnter = "as_ch_enter_pl_241_moba",
outIdle = "AS_CH_OutIdle_001",
outShow = "AS_CH_IdleShow_PL_241_moba",
outShowIntervalTime = 10,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 1000
},
autoUse = true,
isShowPopRewardView = 1
},
[301140] = {
id = 301140,
type = "ItemType_Arena_HeroTry",
quality = 2,
name = "峡谷英雄：项羽",
desc = "（限峡谷模式使用）获取后可以使用辅助项羽。重复获得时将自动转换为1000峡谷币。",
icon = "CDN:CT_Arena_SquareHero_1040",
useType = v2,
useParam = {
1040
},
resourceConf = {
model = "SK_PL_237_moba",
physics = "SK_PL_237_moba_Physics"
},
outEnter = "as_ch_enter_pl_237_moba",
outIdle = "AS_CH_OutIdle_001",
outShow = "AS_CH_IdleShow_PL_237_moba",
outShowIntervalTime = 10,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 1000
},
autoUse = true,
isShowPopRewardView = 1
},
[301144] = {
id = 301144,
type = "ItemType_Arena_HeroTry",
quality = 2,
name = "诸葛亮",
desc = "（限峡谷模式使用）获取后可以使用法师诸葛亮。",
icon = "CDN:CT_Arena_SquareHero_1044",
useType = v2,
useParam = {
1044
},
resourceConf = {
model = "SK_PL_236_moba",
physics = "SK_PL_236_moba_PhysicsAsset"
},
outEnter = "as_ch_enter_pl_236_moba",
outIdle = "AS_CH_OutIdle_001",
outShow = "AS_CH_IdleShow_PL_236_moba",
outShowIntervalTime = 10,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 200
},
autoUse = true,
isShowPopRewardView = 1
},
[301148] = {
id = 301148,
type = "ItemType_Arena_HeroTry",
quality = 2,
name = "艾琳",
desc = "（限峡谷模式使用）获取后可以使用射手艾琳。",
icon = "CDN:CT_Arena_SquareHero_1048",
useType = v2,
useParam = {
1048
},
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 200
},
autoUse = true,
isShowPopRewardView = 1
},
[301505] = {
id = 301505,
name = "兰陵王7天限免",
desc = "免费体验7天兰陵王（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1005",
useType = v2,
useParam = {
1005
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301506] = {
id = 301506,
name = "赵云7天限免",
desc = "免费体验7天赵云（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1010",
useType = v2,
useParam = {
1010
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301507] = {
id = 301507,
name = "蔡文姬7天限免",
desc = "免费体验7天蔡文姬（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1013",
useType = v2,
useParam = {
1013
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301508] = {
id = 301508,
name = "孙尚香7天限免",
desc = "免费体验7天孙尚香（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1014",
useType = v2,
useParam = {
1014
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301509] = {
id = 301509,
name = "钟馗7天限免",
desc = "免费体验7天钟馗（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1015",
useType = v2,
useParam = {
1015
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301510] = {
id = 301510,
name = "云缨7天限免",
desc = "免费体验7天云缨（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1017",
useType = v2,
useParam = {
1017
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301511] = {
id = 301511,
name = "王昭君7天限免",
desc = "免费体验7天王昭君（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1018",
useType = v2,
useParam = {
1018
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301512] = {
id = 301512,
name = "李白7天限免",
desc = "免费体验7天李白（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1019",
useType = v2,
useParam = {
1019
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301513] = {
id = 301513,
name = "墨子7天限免",
desc = "免费体验7天墨子（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1020",
useType = v2,
useParam = {
1020
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301514] = {
id = 301514,
name = "公孙离7天限免",
desc = "免费体验7天公孙离（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1021",
useType = v2,
useParam = {
1021
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301601] = {
id = 301601,
name = "貂蝉体验卡（7天）",
desc = "免费体验7天峡谷英雄貂蝉（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1041",
useType = v2,
useParam = {
1041
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301602] = {
id = 301602,
name = "曜体验卡（7天）",
desc = "免费体验7天峡谷英雄曜（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1026",
useType = v2,
useParam = {
1026
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301603] = {
id = 301603,
name = "公孙离体验卡（7天）",
desc = "免费体验7天峡谷英雄公孙离（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1021",
useType = v2,
useParam = {
1021
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301604] = {
id = 301604,
name = "典韦体验卡（7天）",
desc = "免费体验7天峡谷英雄典韦（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1036",
useType = v2,
useParam = {
1036
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301605] = {
id = 301605,
name = "阿轲体验卡（7天）",
desc = "免费体验7天峡谷英雄阿轲（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1035",
useType = v2,
useParam = {
1035
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301606] = {
id = 301606,
name = "吕布体验卡（7天）",
desc = "免费体验7天峡谷英雄吕布（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1032",
useType = v2,
useParam = {
1032
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301607] = {
id = 301607,
name = "虞姬体验卡（7天）",
desc = "免费体验7天峡谷英雄虞姬（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1031",
useType = v2,
useParam = {
1031
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301608] = {
id = 301608,
name = "马可波罗体验卡（7天）",
desc = "免费体验7天峡谷英雄马可波罗（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1027",
useType = v2,
useParam = {
1027
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[301609] = {
id = 301609,
name = "花木兰体验卡（7天）",
desc = "免费体验7天峡谷英雄花木兰（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1024",
useType = v2,
useParam = {
1024
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
},
autoUse = true,
isShowPopRewardView = 1
},
[302005] = {
id = 302005,
type = "ItemType_Arena_Equip",
stackedNum = 1,
maxNum = 1,
quality = 2,
name = "赤龙之锋 兰陵王",
desc = "（限峡谷模式使用）获取后可以获得兰陵王的专属武器。",
icon = "T_Arena_Skin_5_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1005,
1019
},
resourceConf = {
model = "SK_PL_Prop_087_01_moba",
modelType = 2
},
scaleTimes = 70,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
-60,
-10
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302007] = {
id = 302007,
type = "ItemType_Arena_Equip",
stackedNum = 1,
maxNum = 1,
name = "甜蜜和弦 小乔",
desc = "（限峡谷模式使用）获取后可以获得小乔的专属武器。",
icon = "T_Arena_Skin_7_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1007,
1020
},
resourceConf = {
model = "SK_PL_Prop_086_01_moba",
modelType = 2
},
scaleTimes = 70,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-100
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302101] = {
id = 302101,
quality = 4,
name = "小花花",
desc = "（限峡谷模式使用）在观战模式下，使用后送出小花花。",
icon = "T_Arena_Icon_InsideGift05",
useType = "IUTO_PeriodInteract",
bHideInBag = true,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemNum = 10
},
autoUse = true
},
[302102] = {
id = 302102,
name = "普通盲盒",
desc = "（限峡谷模式使用）在观战模式下，使用后送出普通盲盒。",
icon = "T_Arena_Icon_InsideGift04",
useType = "IUTO_PeriodInteract",
bHideInBag = true,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemNum = 10
},
autoUse = true
},
[302103] = {
id = 302103,
quality = 2,
name = "小火箭",
desc = "（限峡谷模式使用）在观战模式下，使用后送出小火箭。",
icon = "T_Arena_Icon_InsideGift03",
useType = "IUTO_PeriodInteract",
bHideInBag = true,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemNum = 10
},
autoUse = true
},
[302104] = {
id = 302104,
name = "花束",
desc = "（限峡谷模式使用）在观战模式下，使用后送出花束。",
icon = "T_Arena_Icon_InsideGift06",
useType = "IUTO_PeriodInteract",
bHideInBag = true,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemNum = 10
},
autoUse = true
},
[302105] = {
id = 302105,
quality = 2,
name = "高级盲盒",
desc = "（限峡谷模式使用）在观战模式下，使用后送出高级盲盒。",
icon = "T_Arena_Icon_InsideGift01",
useType = "IUTO_PeriodInteract",
bHideInBag = true,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemNum = 10
},
autoUse = true
},
[302106] = {
id = 302106,
quality = 1,
name = "大火箭",
desc = "（限峡谷模式使用）在观战模式下，使用后送出大火箭。",
icon = "T_Arena_Icon_InsideGift02",
useType = "IUTO_PeriodInteract",
bHideInBag = true,
pakGroup = v4,
arenaHeroTryCardConf = {
exchangeItemNum = 10
},
autoUse = true
},
[303001] = {
id = 303001,
stackedNum = 1,
maxNum = 1,
quality = 1,
name = "灼热圣光",
desc = "峡谷英雄亚瑟专属卡【灼热圣光】：【被动】触发时，对附近的目标造成伤害",
picture = v5,
useType = v3,
useParam = {
1011001
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303002] = {
id = 303002,
stackedNum = 1,
maxNum = 1,
quality = 2,
name = "不屈骑士",
desc = "峡谷英雄亚瑟专属卡【不屈骑士】：释放【大招】后，扣除亚瑟当前一定百分比生命，同时获得累计的护盾",
picture = v5,
useType = v3,
useParam = {
1011024
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303003] = {
id = 303003,
stackedNum = 1,
maxNum = 1,
name = "圣辉同在",
desc = "峡谷英雄亚瑟专属卡【圣辉同在】：【被动】触发时附近队友也获得少量回复",
picture = v5,
useType = v3,
useParam = {
1011004
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303004] = {
id = 303004,
stackedNum = 1,
maxNum = 1,
quality = 1,
name = "锐不可当",
desc = "峡谷英雄铠专属卡【锐不可当】：【大招】变为砍出三道光束，期间持续霸体，且获得额外暴击率、暴击伤害、减伤",
picture = v5,
useType = v3,
useParam = {
1002007
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303005] = {
id = 303005,
stackedNum = 1,
maxNum = 1,
quality = 2,
name = "重装",
desc = "峡谷英雄铠专属卡【重装】：大幅提升最大生命值",
picture = v5,
useType = v3,
useParam = {
1002012
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303006] = {
id = 303006,
stackedNum = 1,
maxNum = 1,
name = "随身风暴",
desc = "峡谷英雄铠专属卡【随身风暴】：脱战后每秒回复1.5%生命，并且移速+80点",
picture = v5,
useType = v3,
useParam = {
1002001
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[304001] = {
id = 304001,
type = "ItemType_Arena_Shard",
quality = 1,
name = "峡谷卡牌碎片",
desc = "可在峡谷商城兑换卡牌",
icon = "CDN:CT_Arena_Icon_Coin_004"
},
[304002] = {
id = 304002,
type = "ItemType_Arena_Shard",
quality = 1,
name = "峡谷皮肤碎片",
desc = "可在峡谷商城兑换皮肤",
icon = "CDN:CT_Arena_Icon_Coin_005"
},
[304003] = {
id = 304003,
type = "ItemType_Arena_Shard",
quality = 1,
name = "峡谷英雄碎片",
desc = "可在峡谷商城兑换英雄",
icon = "CDN:CT_Arena_Icon_Coin_006"
},
[320214] = {
id = 320214,
type = "ItemType_Arena_ReportAnim",
quality = 1,
name = "毫光万影",
desc = "峡谷英雄孙悟空专属卡【毫光万影】：【斗战冲锋】将召唤3只猴子攻击，二次位移时可对目标区域造成伤害并击飞",
icon = "CDN:CT_Arena_Item_SunWuKongCard",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11006
},
pakGroup = v4,
autoUse = true
},
[320215] = {
id = 320215,
quality = 1,
name = "谪仙遗韵",
desc = "峡谷英雄李白专属卡【谪仙遗韵】：【将进酒】路径终点生成残像，残像一定时间后会引爆，对目标造成30%减速效果",
icon = "CDN:CT_Arena_Item_Libai1",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11007
},
pakGroup = v4,
isShowPopRewardView = 0
},
[320216] = {
id = 320216,
quality = 1,
name = "无尽之冬",
desc = "峡谷英雄王昭君专属卡【无尽之冬】：【凛冬已至】目标区域周围将随机出现另一个【凛冬已至】",
icon = "CDN:CT_Arena_Item_WangZhaoJun1",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11008
},
pakGroup = v4,
isShowPopRewardView = 0
},
[320217] = {
id = 320217,
quality = 1,
name = "电光石火",
desc = "峡谷英雄马可波罗专属卡【电光石火】：【华丽左轮】有概率生成闪电球，闪电球生成、马可波罗普攻和施放1技能时会射出闪电链，造成伤害并弹射附近目标，最多弹射3次",
icon = "CDN:CT_Arena_Item_MaKe1",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11009
},
pakGroup = v4,
isShowPopRewardView = 0
},
[320218] = {
id = 320218,
type = "ItemType_Common",
quality = 1,
name = "橙色星喜卡牌匣",
desc = "开启后必定获得一张橙色英雄专属卡",
picture = v5,
useType = "IUTO_ArenaCardPack",
useParam = {
2,
10003
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 1
},
[320219] = {
id = 320219,
type = "ItemType_Arena_ReportAnim",
quality = 1,
name = "操纵·九霄",
desc = "峡谷英雄张良专属卡【操纵·九霄】：【操纵·决胜千里】张良化身天道，飞上天空，将目标困在旋风中，拖动旋风可控制目标移动",
icon = "CDN:CT_Arena_Item_ZhangliangCard",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11010
},
pakGroup = v4,
autoUse = true
},
[320220] = {
id = 320220,
quality = 1,
name = "造物者",
desc = "峡谷英雄东皇太一专属卡【造物者】：每秒回复自身1.5%最大生命，受伤后3秒内回复效果翻倍",
icon = "CDN:CT_Arena_Item_Donghuang",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11011
},
pakGroup = v4,
isShowPopRewardView = 0
},
[320221] = {
id = 320221,
quality = 1,
name = "炽道炎炎",
desc = "峡谷英雄花木兰专属卡【炽道炎炎】：【空裂斩】冲锋后的路径附加火焰效果，持续伤害路径上的目标",
icon = "CDN:CT_Arena_Item_HuaMuLan",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11012
},
pakGroup = v4,
isShowPopRewardView = 0
},
[320222] = {
id = 320222,
quality = 1,
name = "一波万波",
desc = "峡谷英雄妲己专属卡【一波万波】：【灵魂冲击】改为蓄力释放，伤害与蓄力时间成正比",
icon = "CDN:CT_Arena_Item_DaJi",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11013
},
pakGroup = v4,
isShowPopRewardView = 0
},
[301901] = {
id = 301901,
name = "刘备体验卡（90天）",
desc = "免费体验90天峡谷英雄刘备（获取后自动使用）。",
icon = "T_Arena_SquareHero_1042",
useType = v2,
useParam = {
1042
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 2160,
exchangeItemId = 3541,
exchangeItemNum = 200
},
autoUse = true,
isShowPopRewardView = 1
},
[301902] = {
id = 301902,
name = "诸葛亮体验卡（90天）",
desc = "免费体验90天峡谷英雄诸葛亮（获取后自动使用）。",
icon = "T_Arena_SquareHero_1044",
useType = v2,
useParam = {
1044
},
pakGroup = v4,
arenaHeroTryCardConf = {
duration = 2160,
exchangeItemId = 3541,
exchangeItemNum = 200
},
autoUse = true,
isShowPopRewardView = 1
},
[300220] = {
id = 300220,
quality = 2,
name = "玻璃心",
desc = "【偶像魅力】不再锁定目标，魅力爱心飞行至终点爆炸，造成范围伤害和眩晕",
icon = "T_Arena_Item_Manual_WangZhaoJun",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
10110
},
pakGroup = v4,
isShowPopRewardView = 0
},
[303007] = {
id = 303007,
stackedNum = 1,
maxNum = 1,
name = "伤心舞步",
picture = v5,
useType = v3,
useParam = {
1001013
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303008] = {
id = 303008,
stackedNum = 1,
maxNum = 1,
name = "轻甲",
picture = v5,
useType = v3,
useParam = {
1002009
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303009] = {
id = 303009,
stackedNum = 1,
maxNum = 1,
name = "盛火",
picture = v5,
useType = v3,
useParam = {
1003005
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303010] = {
id = 303010,
stackedNum = 1,
maxNum = 1,
name = "快枪手",
picture = v5,
useType = v3,
useParam = {
1004011
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303011] = {
id = 303011,
stackedNum = 1,
maxNum = 1,
name = "回魂之刃",
picture = v5,
useType = v3,
useParam = {
1005014
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303012] = {
id = 303012,
stackedNum = 1,
maxNum = 1,
name = "妙舞",
picture = v5,
useType = v3,
useParam = {
1006011
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303013] = {
id = 303013,
stackedNum = 1,
maxNum = 1,
name = "独舞",
picture = v5,
useType = v3,
useParam = {
1007009
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303014] = {
id = 303014,
stackedNum = 1,
maxNum = 1,
name = "强袭",
picture = v5,
useType = v3,
useParam = {
1010015
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303015] = {
id = 303015,
stackedNum = 1,
maxNum = 1,
name = "圣辉同在",
picture = v5,
useType = v3,
useParam = {
1011004
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303016] = {
id = 303016,
stackedNum = 1,
maxNum = 1,
name = "没羽",
picture = v5,
useType = v3,
useParam = {
1012007
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303017] = {
id = 303017,
stackedNum = 1,
maxNum = 1,
name = "清泉广泽",
picture = v5,
useType = v3,
useParam = {
1013002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303018] = {
id = 303018,
stackedNum = 1,
maxNum = 1,
name = "严阵以待",
picture = v5,
useType = v3,
useParam = {
1014004
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303019] = {
id = 303019,
stackedNum = 1,
maxNum = 1,
name = "断魂掌",
picture = v5,
useType = v3,
useParam = {
1015009
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303020] = {
id = 303020,
stackedNum = 1,
maxNum = 1,
name = "瞬影疾步",
picture = v5,
useType = v3,
useParam = {
1016002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303021] = {
id = 303021,
stackedNum = 1,
maxNum = 1,
name = "善战",
picture = v5,
useType = v3,
useParam = {
1017006
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303022] = {
id = 303022,
stackedNum = 1,
maxNum = 1,
name = "踏雪",
picture = v5,
useType = v3,
useParam = {
1018003
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303023] = {
id = 303023,
stackedNum = 1,
maxNum = 1,
name = "霜刃初试",
picture = v5,
useType = v3,
useParam = {
1019002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303024] = {
id = 303024,
stackedNum = 1,
maxNum = 1,
name = "后坐强击",
picture = v5,
useType = v3,
useParam = {
1020001
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303025] = {
id = 303025,
stackedNum = 1,
maxNum = 1,
name = "流云步",
picture = v5,
useType = v3,
useParam = {
1021002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303026] = {
id = 303026,
stackedNum = 1,
maxNum = 1,
name = "星尘裂解",
picture = v5,
useType = v3,
useParam = {
1022002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303027] = {
id = 303027,
stackedNum = 1,
maxNum = 1,
name = "草中飞梭",
picture = v5,
useType = v3,
useParam = {
1023001
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303028] = {
id = 303028,
stackedNum = 1,
maxNum = 1,
name = "剑芒复现",
picture = v5,
useType = v3,
useParam = {
1024008
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303029] = {
id = 303029,
stackedNum = 1,
maxNum = 1,
name = "卸下心防",
picture = v5,
useType = v3,
useParam = {
1025002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303030] = {
id = 303030,
stackedNum = 1,
maxNum = 1,
name = "尘幕",
picture = v5,
useType = v3,
useParam = {
1026007
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303031] = {
id = 303031,
stackedNum = 1,
maxNum = 1,
name = "追猎",
picture = v5,
useType = v3,
useParam = {
1027006
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303032] = {
id = 303032,
stackedNum = 1,
maxNum = 1,
name = "力挽狂澜",
picture = v5,
useType = v3,
useParam = {
1028003
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303033] = {
id = 303033,
stackedNum = 1,
maxNum = 1,
name = "咒令·道化",
picture = v5,
useType = v3,
useParam = {
1029002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303034] = {
id = 303034,
stackedNum = 1,
maxNum = 1,
name = "虎虎生风",
picture = v5,
useType = v3,
useParam = {
1030007
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303035] = {
id = 303035,
stackedNum = 1,
maxNum = 1,
name = "灵犀速射",
picture = v5,
useType = v3,
useParam = {
1031002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303036] = {
id = 303036,
stackedNum = 1,
maxNum = 1,
name = "碾压",
picture = v5,
useType = v3,
useParam = {
1032005
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303037] = {
id = 303037,
stackedNum = 1,
maxNum = 1,
name = "决胜法则",
picture = v5,
useType = v3,
useParam = {
1034003
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303038] = {
id = 303038,
stackedNum = 1,
maxNum = 1,
name = "山盟",
picture = v5,
useType = v3,
useParam = {
1038003
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303039] = {
id = 303039,
stackedNum = 1,
maxNum = 1,
name = "动念",
picture = v5,
useType = v3,
useParam = {
1041004
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303040] = {
id = 303040,
stackedNum = 1,
maxNum = 1,
name = "身经百战",
picture = v5,
useType = v3,
useParam = {
1042001
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303041] = {
id = 303041,
stackedNum = 1,
maxNum = 1,
name = "浮躁",
picture = v5,
useType = v3,
useParam = {
1043004
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303042] = {
id = 303042,
stackedNum = 1,
maxNum = 1,
name = "扣人心弦",
picture = v5,
useType = v3,
useParam = {
1045002
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
},
[303043] = {
id = 303043,
stackedNum = 1,
maxNum = 1,
quality = 2,
name = "心河长流",
picture = v5,
useType = v3,
useParam = {
1001012
},
pakGroup = v4,
autoUse = true,
isShowPopRewardView = 0
}
}

local mt = {
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
quality = 3,
desc = "峡谷英雄专属卡牌",
icon = "T_Arena_Icon_ShopCard",
autoUse = false,
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data