--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置.xlsx: 活动任务

local data = {
[800021] = {
id = 800021,
name = "5月8日起累计登录3天",
desc = "5月8日起累计登录3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
3
},
validPeriodList = {
0
}
},
taskGroupId = 70017
},
[630081] = {
id = 630081,
name = "5月11日起登录游戏",
desc = "5月11日起登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3546,
290023
},
numList = {
4,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63800
},
[630082] = {
id = 630082,
name = "【每周】在星宝农场浇水10次",
desc = "【每周】在星宝农场浇水10次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 156,
value = 10
}
}
}
},
reward = {
itemIdList = {
3546
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 63801
},
[630083] = {
id = 630083,
name = "【每周】在好友农场拿取20次作物",
desc = "【每周】在好友农场拿取20次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 20
}
}
}
},
reward = {
itemIdList = {
3546
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 63801
},
[630084] = {
id = 630084,
name = "【每周】游玩1次天天晋级赛",
desc = "【每周】游玩1次天天晋级赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
1,
2,
3,
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3546
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1052,
taskGroupId = 63801
},
[630085] = {
id = 630085,
name = "【累计】游玩1次蜜桃猫主题地图",
desc = "【累计】游玩1次蜜桃猫主题地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 100,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425188006465
}
}
}
}
}
}
},
reward = {
itemIdList = {
3546,
290023
},
numList = {
2,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 83,
taskGroupId = 63802
},
[630086] = {
id = 630086,
name = "【累计】登录游戏2天",
desc = "【累计】登录游戏2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
3546,
290023
},
numList = {
2,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63802
},
[630087] = {
id = 630087,
name = "【累计】登录游戏3天",
desc = "【累计】登录游戏3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
3546,
290024
},
numList = {
3,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63802
},
[630088] = {
id = 630088,
name = "【累计】登录游戏5天",
desc = "【累计】登录游戏5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 5
}
}
}
},
reward = {
itemIdList = {
3546,
290024
},
numList = {
5,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63802
},
[630089] = {
id = 630089,
name = "【累计】登录游戏7天",
desc = "【累计】登录游戏7天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 7
}
}
}
},
reward = {
itemIdList = {
3546,
290025
},
numList = {
7,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63802
},
[630300] = {
id = 630300,
name = "成功邀请1名新玩家或召回1名好友",
desc = "成功邀请1名新玩家或召回1名好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 123,
value = 1,
subConditionList = {
{
type = 115,
value = {
1,
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
3463
},
numList = {
3,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63070
},
[630301] = {
id = 630301,
name = "成功邀请2名新玩家或召回2名好友",
desc = "成功邀请2名新玩家或召回2名好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 123,
value = 2,
subConditionList = {
{
type = 115,
value = {
1,
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
3463
},
numList = {
3,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63070
},
[630302] = {
id = 630302,
name = "成功邀请3名新玩家或召回3名好友",
desc = "成功邀请3名新玩家或召回3名好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 123,
value = 3,
subConditionList = {
{
type = 115,
value = {
1,
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
3463
},
numList = {
3,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63070
},
[630303] = {
id = 630303,
name = "成功邀请4名新玩家或召回4名好友",
desc = "成功邀请4名新玩家或召回4名好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 123,
value = 4,
subConditionList = {
{
type = 115,
value = {
1,
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
3463
},
numList = {
3,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63070
},
[630304] = {
id = 630304,
name = "成功邀请5名新玩家或召回5名好友",
desc = "成功邀请5名新玩家或召回5名好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 123,
value = 5,
subConditionList = {
{
type = 115,
value = {
1,
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
3463
},
numList = {
3,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63070
},
[630305] = {
id = 630305,
name = "成功邀请6名新玩家或召回6名好友",
desc = "成功邀请6名新玩家或召回6名好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 123,
value = 6,
subConditionList = {
{
type = 115,
value = {
1,
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
3463
},
numList = {
3,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63070
},
[630306] = {
id = 630306,
name = "分享活动信息1次",
desc = "分享活动信息1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10128
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 10128,
taskGroupId = 63071
},
[630307] = {
id = 630307,
name = "5月2日起登录游戏1天",
desc = "5月2日起登录游戏1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
630605
},
numList = {
1
},
validPeriodList = {
7
}
},
taskGroupId = 63072
},
[630308] = {
id = 630308,
name = "5月3日起登录游戏1天",
desc = "5月3日起登录游戏1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
290022,
290023
},
numList = {
2,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63073
},
[630309] = {
id = 630309,
name = "5月5日起登录游戏1天",
desc = "5月5日起登录游戏1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
870048,
290023
},
numList = {
1,
2
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63074
},
[630310] = {
id = 630310,
name = "5月5日起登录游戏2天",
desc = "5月5日起登录游戏2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
630605,
290024
},
numList = {
1,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63075
},
[630311] = {
id = 630311,
name = "5月5日起登录游戏3天",
desc = "5月5日起登录游戏3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
290025
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 63076
},
[630312] = {
id = 630312,
name = "分享1次全新赛季福利讯息",
desc = "分享1次全新赛季福利讯息",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10129
}
}
}
}
}
}
},
reward = {
itemIdList = {
411060,
410250
},
numList = {
1,
1
},
validPeriodList = {
3,
3
}
},
jumpId = 10129,
taskGroupId = 63077
},
[630313] = {
id = 630313,
name = "5v5战骑玩法上线，玩1局→",
desc = "5v5战骑玩法上线，玩1局→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541,
2
},
numList = {
10,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 50112,
taskGroupId = 63077
},
[630390] = {
id = 630390,
name = "5月5日小爱惊喜返场，去福利导航免费兑换→",
desc = "5月5日小爱惊喜返场，去福利导航免费兑换→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
126
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 126,
taskGroupId = 63084
},
[630314] = {
id = 630314,
name = "【每日】游玩1次晋级赛排位",
desc = "【每日】游玩1次晋级赛排位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
200800
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1052,
taskGroupId = 63078,
canUseItemFinish = true,
finishNeedItemCount = 1
},
[630315] = {
id = 630315,
name = "前往喵喵分岛1次",
desc = "前往喵喵分岛1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
21924
}
}
}
}
}
}
},
reward = {
itemIdList = {
200800
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 21924,
taskGroupId = 63079
},
[630316] = {
id = 630316,
name = "星梦广场使用喵喵炸弹1次",
desc = "星梦广场使用喵喵炸弹1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 42,
value = 1,
subConditionList = {
{
type = 253,
value = {
10025
}
}
}
}
}
}
},
reward = {
itemIdList = {
200800
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 14,
taskGroupId = 63080
},
[630317] = {
id = 630317,
name = " 5.2前往星世界漫游卡1次",
desc = " 5.2前往星世界漫游卡1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
663
}
}
}
}
}
}
},
reward = {
itemIdList = {
200800
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 663,
taskGroupId = 63081
},
[630318] = {
id = 630318,
name = "【每周】累计登录2天",
desc = "【每周】累计登录2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
200800
},
numList = {
3
},
validPeriodList = {
0
}
},
taskGroupId = 63082
},
[630319] = {
id = 630319,
name = "【每周】累计登录3天",
desc = "【每周】累计登录3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
200800
},
numList = {
4
},
validPeriodList = {
0
}
},
taskGroupId = 63083
},
[630400] = {
id = 630400,
name = "5月20日起登录游戏兑爱心头饰",
desc = "5月20日起登录游戏兑爱心头饰",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3548,
3544
},
numList = {
60,
50
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63803
},
[630401] = {
id = 630401,
name = "狼人太空战祈愿来袭！去看看→",
desc = "狼人太空战祈愿来袭！去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
673
}
}
}
}
}
}
},
reward = {
itemIdList = {
3548
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 673,
taskGroupId = 63804
},
[630402] = {
id = 630402,
name = "印章祈愿更新啦！去看看→",
desc = "印章祈愿更新啦！去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
705
}
}
}
}
}
}
},
reward = {
itemIdList = {
3548
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 705,
taskGroupId = 63804
},
[630403] = {
id = 630403,
name = "【每周】完成2次谁是狼人模式",
desc = "【每周】完成2次谁是狼人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3548
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 63805,
canUseItemFinish = true,
finishNeedItemCount = 2
},
[630404] = {
id = 630404,
name = "【累计】获得200个专精点数",
desc = "【累计】获得200个专精点数",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 220,
value = 200
}
}
}
},
reward = {
itemIdList = {
3548
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 63804
},
[630405] = {
id = 630405,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3549
}
}
}
}
}
}
},
reward = {
itemIdList = {
510368
},
numList = {
1
},
validPeriodList = {
3
}
},
taskGroupId = 63806
},
[630406] = {
id = 630406,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3549
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63806
},
[630407] = {
id = 630407,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3549
}
}
}
}
}
}
},
reward = {
itemIdList = {
290023
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63806
},
[630408] = {
id = 630408,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3549
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 63806
},
[630409] = {
id = 630409,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 50,
subConditionList = {
{
type = 3,
value = {
3549
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63806
},
[630410] = {
id = 630410,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 60,
subConditionList = {
{
type = 3,
value = {
3549
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 63806
},
[630411] = {
id = 630411,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 80,
subConditionList = {
{
type = 3,
value = {
3549
}
}
}
}
}
}
},
reward = {
itemIdList = {
510368
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63806
},
[630412] = {
id = 630412,
name = "【每周】游玩2次天天晋级赛（排位）",
desc = "【每周】游玩2次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3549
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 1052,
taskGroupId = 63807,
canUseItemFinish = true,
finishNeedItemCount = 1
},
[630413] = {
id = 630413,
name = "【累计】游玩XXX地图1次",
desc = "【累计】游玩XXX地图1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 100,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204980098
}
}
}
}
}
}
},
reward = {
itemIdList = {
3549
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 83,
taskGroupId = 63808,
canUseItemFinish = true,
finishNeedItemCount = 1
},
[630414] = {
id = 630414,
name = "【每周】登录游戏2天",
desc = "【每周】登录游戏2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
3549
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63807
},
[630415] = {
id = 630415,
name = "【每周】登录游戏3天",
desc = "【每周】登录游戏3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
3549
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 63807
},
[660010] = {
id = 660010,
name = "5月31日起，卓大王祈愿开启，去看看→",
desc = "5月31日起，卓大王祈愿开启，去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
1096
}
}
}
}
}
}
},
reward = {
itemIdList = {
3632
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1096,
taskGroupId = 66011
},
[660011] = {
id = 660011,
name = "暗星血族豪门焕发新颜，去看看→",
desc = "暗星血族豪门焕发新颜，去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
699
}
}
}
}
}
}
},
reward = {
itemIdList = {
3632
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 699,
taskGroupId = 66010
},
[660012] = {
id = 660012,
name = "狼人之夜赛事即将开启，去订阅→",
desc = "狼人之夜赛事即将开启，去订阅→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
139
}
}
}
}
}
}
},
reward = {
itemIdList = {
3632
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 139,
taskGroupId = 66024
},
[660013] = {
id = 660013,
name = "5月31日起，前往六一乐园",
desc = "5月31日起，前往六一乐园",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
21924
}
}
}
}
}
}
},
reward = {
itemIdList = {
3632
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 21924,
taskGroupId = 66011
},
[660014] = {
id = 660014,
name = "【累计】游玩1次卓大王手帐地图",
desc = "【累计】游玩1次卓大王手帐地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425210532500
}
}
}
}
}
}
},
reward = {
itemIdList = {
3632
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 83,
taskGroupId = 66010
},
[660015] = {
id = 660015,
name = "【每周】游玩2次“六一精选”地图",
desc = "【每周】游玩2次“六一精选”地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 2,
subConditionList = {
{
type = 89,
value = {
5
}
}
}
}
}
}
},
reward = {
itemIdList = {
3632
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 76,
taskGroupId = 66012
},
[660016] = {
id = 660016,
name = "【每周】游玩2次谁是狼人模式 或 【每周】游玩3局任意模式",
desc = "【每周】游玩2次谁是狼人模式 或 【每日】游玩3局任意模式",
condition = {
resCompleteConditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
},
jumpId = 310,
desc = "【每周】游玩2次谁是狼人模式"
},
{
conditionType = 4,
value = 3,
jumpId = 4,
desc = "【每周】游玩3局任意模式"
}
}
}
},
reward = {
itemIdList = {
3632
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 66012,
canUseItemFinish = true,
finishNeedItemCount = 3
},
[630420] = {
id = 630420,
name = "完成以下全部任务",
desc = "完成以下全部任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 6,
subConditionList = {
{
type = 4,
value = {
630421,
630422,
630423,
630424,
630425,
630437
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
290025
},
numList = {
6,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63810
},
[630421] = {
id = 630421,
name = "5月31日端午节起，登录游戏",
desc = "5月31日端午节起，登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
411280,
630621
},
numList = {
1,
1
},
validPeriodList = {
3,
3
}
},
taskGroupId = 63811
},
[630422] = {
id = 630422,
name = "6月1日儿童节起，登录游戏",
desc = "6月1日儿童节起，登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3544,
3134
},
numList = {
30,
15
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63812
},
[630423] = {
id = 630423,
name = "6月3日开工起，登录游戏",
desc = "6月3日开工起，登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
2,
3544
},
numList = {
3,
30
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63813
},
[630437] = {
id = 630437,
name = "周五-周日礼物盒暴击开启，去农场→",
desc = "周五-周日礼物盒暴击开启，去农场→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
5100
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134,
290022
},
numList = {
15,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 5100,
taskGroupId = 63822
},
[630424] = {
id = 630424,
name = "订阅活动消息，接收活动提醒→",
desc = "订阅活动消息，接收活动提醒→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 1,
subConditionList = {
{
type = 4,
value = {
300001046
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544,
290022
},
numList = {
15,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 442,
taskGroupId = 63810
},
[630425] = {
id = 630425,
name = "成功绑定手机号",
desc = "成功绑定手机号",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 246,
value = 1
}
}
}
},
reward = {
itemIdList = {
3544,
290022
},
numList = {
15,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 10213,
taskGroupId = 63810
},
[630426] = {
id = 630426,
name = "完成以下全部任务",
desc = "完成以下全部任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 6,
subConditionList = {
{
type = 4,
value = {
630427,
630428,
630429,
630430,
630431,
630438
}
}
}
}
}
}
},
reward = {
itemIdList = {
2,
290025
},
numList = {
6,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63814
},
[630427] = {
id = 630427,
name = "5月31日端午节起，登录游戏",
desc = "5月31日端午节起，登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
411280,
630621
},
numList = {
1,
1
},
validPeriodList = {
3,
3
}
},
taskGroupId = 63815
},
[630428] = {
id = 630428,
name = "6月1日儿童节起，登录游戏",
desc = "6月1日儿童节起，登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3544,
3134
},
numList = {
30,
15
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63816
},
[630429] = {
id = 630429,
name = "6月3日开工起，登录游戏",
desc = "6月3日开工起，登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
2,
3544
},
numList = {
3,
30
},
validPeriodList = {
0,
0
}
},
taskGroupId = 63817
},
[630438] = {
id = 630438,
name = "周五-周日礼物盒暴击开启，去农场→",
desc = "周五-周日礼物盒暴击开启，去农场→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
5100
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134,
290022
},
numList = {
15,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 5100,
taskGroupId = 63823
},
[630430] = {
id = 630430,
name = "订阅元梦星妹，接收活动提醒→",
desc = "订阅元梦星妹，接收活动提醒→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 1,
subConditionList = {
{
type = 4,
value = {
300001016
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544,
290022
},
numList = {
15,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 500,
taskGroupId = 63814
},
[630431] = {
id = 630431,
name = "成功绑定手机号",
desc = "成功绑定手机号",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 246,
value = 1
}
}
}
},
reward = {
itemIdList = {
3544,
290022
},
numList = {
15,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 10213,
taskGroupId = 63814
},
[630432] = {
id = 630432,
name = "蝶舞花间进行中！去看看→",
desc = "蝶舞花间进行中！去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
1096
}
}
}
}
}
}
},
reward = {
itemIdList = {
3633
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 1096,
taskGroupId = 63818
},
[630433] = {
id = 630433,
name = "6月9日开启！登录兑星愿币！",
desc = "6月9日开启！登录兑星愿币！",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3633
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 63819
},
[630434] = {
id = 630434,
name = "【每日】游玩1次“每日精选”地图 或 【每日】游玩1次天天晋级赛（排位）",
desc = "【每日】游玩1次“每日精选”地图 或 【每日】游玩1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 89,
value = {
5
}
}
},
jumpId = 76,
desc = "【每日】游玩1次“每日精选”地图"
},
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
},
jumpId = 1052,
desc = "【每日】游玩1次天天晋级赛（排位）"
}
}
}
},
reward = {
itemIdList = {
3633
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 63820,
canUseItemFinish = true,
finishNeedItemCount = 1
},
[630435] = {
id = 630435,
name = "【每周】登录游戏2天",
desc = "【每周】登录游戏2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
3633
},
numList = {
15
},
validPeriodList = {
0
}
},
taskGroupId = 63821
},
[630436] = {
id = 630436,
name = "【每周】登录游戏3天",
desc = "【每周】登录游戏3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
3633
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 63821
},
[630500] = {
id = 630500,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3635
}
}
}
}
}
}
},
reward = {
itemIdList = {
510327
},
numList = {
1
},
validPeriodList = {
7
}
},
taskGroupId = 63830
},
[630501] = {
id = 630501,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3635
}
}
}
}
}
}
},
reward = {
itemIdList = {
316038
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63830
},
[630502] = {
id = 630502,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3635
}
}
}
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63830
},
[630503] = {
id = 630503,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3635
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 63830
},
[630504] = {
id = 630504,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 50,
subConditionList = {
{
type = 3,
value = {
3635
}
}
}
}
}
}
},
reward = {
itemIdList = {
200020
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63830
},
[630505] = {
id = 630505,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 70,
subConditionList = {
{
type = 3,
value = {
3635
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 63830
},
[630506] = {
id = 630506,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 90,
subConditionList = {
{
type = 3,
value = {
3635
}
}
}
}
}
}
},
reward = {
itemIdList = {
510327
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63830
},
[630507] = {
id = 630507,
name = "泡泡大战6.13-6.22限时返场，玩1局→",
desc = "泡泡大战6.13-6.22限时返场，玩1局→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
12,
13,
14,
18,
19,
20
}
}
}
}
}
}
},
reward = {
itemIdList = {
3635
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 1030,
taskGroupId = 63831
},
[630508] = {
id = 630508,
name = "前往【青春不散场】乐园1次",
desc = "前往【青春不散场】乐园1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
21928
}
}
}
}
}
}
},
reward = {
itemIdList = {
3635
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 21928,
taskGroupId = 63831
},
[630509] = {
id = 630509,
name = "【每周】游玩2次泡泡大战模式 或 【每周】游玩2次【站队模拟器】",
desc = "【每周】游玩2次泡泡大战模式 或 【每周】游玩2次【站队模拟器】",
condition = {
resCompleteConditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
12,
13,
14,
18,
19,
20
}
}
},
jumpId = 1030,
desc = "【每周】游玩2次泡泡大战模式"
},
{
conditionType = 100,
value = 2,
subConditionList = {
{
type = 90,
value = {
50844425240184192
}
}
},
jumpId = 83,
desc = "【每周】游玩2次【站队模拟器】",
jumpParams = "50844425204980098"
}
}
}
},
reward = {
itemIdList = {
3635
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 63832
},
[630510] = {
id = 630510,
name = "【每周】登录游戏2天",
desc = "【每周】登录游戏2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
3635
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63832
},
[630511] = {
id = 630511,
name = "【每周】登录游戏3天",
desc = "【每周】登录游戏3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
3635
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 63832
},
[630512] = {
id = 630512,
name = "【累计】登录游戏5天",
desc = "【累计】登录游戏5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 5
}
}
}
},
reward = {
itemIdList = {
410590
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63833
},
[630513] = {
id = 630513,
name = "订阅6月27日全新赛季",
desc = "订阅6月27日全新赛季",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
630513
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
3
},
validPeriodList = {
0
}
},
jumpId = -1,
taskGroupId = 63833
},
[630514] = {
id = 630514,
name = "分享赛季新资讯，体验新赛季时装",
desc = "分享赛季新资讯，体验新赛季时装",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10132
}
}
}
}
}
}
},
reward = {
itemIdList = {
200018
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 10132,
taskGroupId = 63833
},
[630515] = {
id = 630515,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 132,
value = 1000000,
subConditionList = {
{
type = 146,
value = {
795
}
}
}
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63834
},
[630516] = {
id = 630516,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 132,
value = 6000000,
subConditionList = {
{
type = 146,
value = {
795
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63834
},
[630517] = {
id = 630517,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 132,
value = 15000000,
subConditionList = {
{
type = 146,
value = {
795
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 63834
},
[630518] = {
id = 630518,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 132,
value = 20000000,
subConditionList = {
{
type = 146,
value = {
795
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
3
},
validPeriodList = {
0
}
},
taskGroupId = 63834
},
[630519] = {
id = 630519,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 5,
subConditionList = {
{
type = 3,
value = {
3636
}
}
}
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63835
},
[630520] = {
id = 630520,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3636
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 63835
},
[630521] = {
id = 630521,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 15,
subConditionList = {
{
type = 3,
value = {
3636
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63835
},
[630522] = {
id = 630522,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3636
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 63835
},
[630523] = {
id = 630523,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 25,
subConditionList = {
{
type = 3,
value = {
3636
}
}
}
}
}
}
},
reward = {
itemIdList = {
290023
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63835
},
[630524] = {
id = 630524,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3636
}
}
}
}
}
}
},
reward = {
itemIdList = {
316023
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 63835
},
[630525] = {
id = 630525,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3636
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
4
},
validPeriodList = {
0
}
},
taskGroupId = 63835
},
[630526] = {
id = 630526,
name = "6.21线索揭秘：狐利派送，多套联动时装免费领！",
desc = "狐利派送，多套联动时装免费领！去分享→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10133
}
}
}
}
}
}
},
reward = {
itemIdList = {
3636
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 10133,
taskGroupId = 63836
}
}

local mt = {
name = "兑换",
desc = "兑换",
canUseItemFinish = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data