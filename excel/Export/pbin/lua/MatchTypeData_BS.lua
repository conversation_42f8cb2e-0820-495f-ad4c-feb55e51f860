--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_BS.xlsx: 玩法

local data = {
[6005] = {
id = 6005,
modeID = 3,
desc = "峡谷吃鸡",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_BS",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_BS",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "6006",
modeGroup = "6005|6006",
settleProc = "MTSC_Common",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "请到移动端游玩",
battleRecordCnt = 30,
matchRuleId = 6200,
battlePlayerNum = 10,
dropId = 28,
mmrType = "MST_Arena_Bs",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena_Bs",
descShort = "双人配合五个阵营，争夺精彩刺激的竞技赛冠军！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_BSBattleResult",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
buttonDesc = "混战",
accountUIName = "UI_BS_FinalAccount",
gameTypeId = 720,
layoutID = 20,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 62004,
warmRoundMatchRuleId = 6006,
UseDefaultChampionDisplayScene = true,
detailLinkDesc = "英雄详情",
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
loginPlatList = {
7
},
isBan = true,
cloudSysList = {
2,
3
},
cloudIsBan = true,
isArena = true,
battleDetailStyle = "Feature.Arena.Script.BrawlStar.UI.FinalAccount.UI_BS_FinalAccount_DetailBattleground"
},
[6006] = {
id = 6006,
modeID = 3,
desc = "峡谷吃鸡",
maxTeamMember = 2,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_BS",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_BS",
showType = "UI_Model_SingleMatchTypeItem",
modeGroup = "6006",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2
},
teamTag = "2",
unlockRule = "请到移动端游玩",
battleRecordCnt = 30,
matchRuleId = 6006,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 28,
mmrType = "MST_Arena_Bs",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena_Bs",
isShowBattleRecord = true,
descShort = "双人配合五个阵营，争夺精彩刺激的竞技赛冠军！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_BSBattleResult",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
recordType = 6006,
buttonDesc = "双人",
accountUIName = "UI_BS_FinalAccount",
gameTypeId = 720,
layoutID = 20,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 60061,
warmRoundMatchRuleId = 6006,
UseDefaultChampionDisplayScene = true,
detailLinkId = 232,
detailLinkDesc = "英雄详情",
detailLinkRedDot = 801,
ComplicatedPlay = 3,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
loginPlatList = {
7
},
isBan = true,
cloudSysList = {
2,
3
},
cloudIsBan = true,
isArena = true,
battleDetailStyle = "Feature.Arena.Script.BrawlStar.UI.FinalAccount.UI_BS_FinalAccount_DetailBattleground"
}
}

local mt = {
isPermanent = false,
isShowBattleRecord = false,
UseDefaultChampionDisplayScene = false,
isBan = false,
cloudIsBan = false,
isArena = false,
TeamMatchGame = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data