--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_载具.xlsx: 载具

local data = {
[730001] = {
id = 730001,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "森之灵·鲲宝",
desc = "风光无限好，游嬉正当时。",
icon = "CDN:Icon_Vehicle_002",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 1200
},
resourceConf = {
material = "MI_Vehicle_002_1;MI_Vehicle_002_2;MI_Vehicle_002_3;MI_Vehicle_002_4",
idleAnim = "AS_CH_Vehicle_002_Idle_Loop_001;AS_CH_Vehicle_002_Idle_Loop_002;AS_VH_Vehicle_002_Idle_Loop_001",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03",
extraOffset = {
-35
}
},
commodityId = 0,
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_002_Pose_001",
outPropSkeletal = "SK_Vehicle_002",
shareTexts = {
"乘上森之灵，一起扑向自然的怀抱。"
},
shareAnim = "AS_VH_Vehicle_002_Pose_002",
minVer = "*********",
beginTime = {
seconds = 1714665600
},
rotateYaw = 170,
sharePic = "T_Share_Vehicle_Kun_730001.astc",
suitId = 20001,
suitName = "森之灵·鲲宝",
suitIcon = "CDN:Icon_Vehicle_002",
profileRotateYaw = 220,
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730002] = {
id = 730002,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "森之灵·鲲宝",
desc = "风光无限好，游嬉正当时。",
icon = "CDN:Icon_Vehicle_002_01",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
belongTo = 730001,
fashionValue = 145,
belongToGroup = {
730002,
730003
}
},
resourceConf = {
material = "MI_Vehicle_002_1_HP01;MI_Vehicle_002_2_HP01;MI_Vehicle_002_3_HP01;MI_Vehicle_002_4_HP01",
idleAnim = "AS_CH_Vehicle_002_Idle_Loop_001;AS_CH_Vehicle_002_Idle_Loop_002;AS_VH_Vehicle_002_Idle_Loop_001",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03",
extraOffset = {
-35
}
},
commodityId = 95501,
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_002_Pose_001",
outPropSkeletal = "SK_Vehicle_002",
shareTexts = {
"乘上森之灵，一起扑向自然的怀抱。"
},
shareAnim = "AS_VH_Vehicle_002_Pose_002",
rotateYaw = 170,
sharePic = "T_Share_Vehicle_Kun_730002.astc",
profileRotateYaw = 220,
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730003] = {
id = 730003,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "森之灵·鲲宝",
desc = "风光无限好，游嬉正当时。",
icon = "CDN:Icon_Vehicle_002_02",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
belongTo = 730001,
fashionValue = 145,
belongToGroup = {
730002,
730003
}
},
resourceConf = {
material = "MI_Vehicle_002_1_HP02;MI_Vehicle_002_2_HP02;MI_Vehicle_002_3_HP02;MI_Vehicle_002_4_HP02",
idleAnim = "AS_CH_Vehicle_002_Idle_Loop_001;AS_CH_Vehicle_002_Idle_Loop_002;AS_VH_Vehicle_002_Idle_Loop_001",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03",
extraOffset = {
-35
}
},
commodityId = 95502,
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_002_Pose_001",
outPropSkeletal = "SK_Vehicle_002",
shareTexts = {
"乘上森之灵，一起扑向自然的怀抱。"
},
shareAnim = "AS_VH_Vehicle_002_Pose_002",
rotateYaw = 170,
sharePic = "T_Share_Vehicle_Kun_730003.astc",
profileRotateYaw = 220,
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730004] = {
id = 730004,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "绮梦幻影",
desc = "乘上幻影，共赴绮梦",
icon = "CDN:Icon_Vehicle_003",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 1200
},
resourceConf = {
material = "MI_Vehicle_003_1;MI_Vehicle_003_2;MI_Vehicle_003_3",
idleAnim = "AS_CH_Vehicle_003_ProfileIdle_Loop_001;AS_CH_Vehicle_003_ldle_Loop_002;AS_VH_Vehicle_003_ProfileIdle_Loop_001",
materialSlot = "Vehicle_003_1;Vehicle_003_2;Vehicle_003_3",
extraOffset = {
5
}
},
commodityId = 0,
scaleTimes = 40,
outPropShow = "AS_VH_Vehicle_003_Pose_001",
outPropSkeletal = "SK_Vehicle_003",
shareTexts = {
"和星宝一同驶入绮丽幻境"
},
shareAnim = "AS_VH_Vehicle_003_Pose_001",
minVer = "********",
beginTime = {
seconds = 1720195200
},
rotateYaw = 170,
sharePic = "T_Share_Vehicle_car_background.astc",
suitId = 20002,
suitName = "绮梦幻影",
suitIcon = "CDN:Icon_Vehicle_003",
shareNamePic = "T_Share_Vehicle_car_name.astc",
shareBubblePic = "T_Share_Vehicle_car_bubble.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730005] = {
id = 730005,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "绮梦幻影",
desc = "乘上幻影，共赴绮梦",
icon = "CDN:Icon_Vehicle_003_01",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
belongTo = 730004,
fashionValue = 145,
belongToGroup = {
730005,
730006
}
},
resourceConf = {
material = "MI_Vehicle_003_1_HP01;MI_Vehicle_003_2_HP01;MI_Vehicle_003_3_HP01",
idleAnim = "AS_CH_Vehicle_003_ProfileIdle_Loop_001;AS_CH_Vehicle_003_ldle_Loop_002;AS_VH_Vehicle_003_ProfileIdle_Loop_001",
materialSlot = "Vehicle_003_1;Vehicle_003_2;Vehicle_003_3",
extraOffset = {
5
}
},
commodityId = 95504,
scaleTimes = 40,
outPropShow = "AS_VH_Vehicle_003_Pose_001",
outPropSkeletal = "SK_Vehicle_003",
shareTexts = {
"和星宝一同驶入绮丽幻境"
},
shareAnim = "AS_VH_Vehicle_003_Pose_001",
rotateYaw = 170,
sharePic = "T_Share_Vehicle_car_background.astc",
shareNamePic = "T_Share_Vehicle_car_name.astc",
shareBubblePic = "T_Share_Vehicle_car_bubble.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730006] = {
id = 730006,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "绮梦幻影",
desc = "乘上幻影，共赴绮梦",
icon = "CDN:Icon_Vehicle_003_02",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
belongTo = 730004,
fashionValue = 145,
belongToGroup = {
730005,
730006
}
},
resourceConf = {
material = "MI_Vehicle_003_1_HP02;MI_Vehicle_003_2_HP02;MI_Vehicle_003_3_HP02",
idleAnim = "AS_CH_Vehicle_003_ProfileIdle_Loop_001;AS_CH_Vehicle_003_ldle_Loop_002;AS_VH_Vehicle_003_ProfileIdle_Loop_001",
materialSlot = "Vehicle_003_1;Vehicle_003_2;Vehicle_003_3",
extraOffset = {
5
}
},
commodityId = 95505,
scaleTimes = 40,
outPropShow = "AS_VH_Vehicle_003_Pose_001",
outPropSkeletal = "SK_Vehicle_003",
shareTexts = {
"和星宝一同驶入绮丽幻境"
},
shareAnim = "AS_VH_Vehicle_003_Pose_001",
rotateYaw = 170,
sharePic = "T_Share_Vehicle_car_background.astc",
shareNamePic = "T_Share_Vehicle_car_name.astc",
shareBubblePic = "T_Share_Vehicle_car_bubble.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730007] = {
id = 730007,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "苍穹玄龙",
desc = "如梦似幻，踏云追月",
icon = "CDN:Icon_Vehicle_004",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
fashionValue = 1200
},
resourceConf = {
material = "MI_Vehicle_004_1;MI_Vehicle_004_2;MI_Vehicle_004_3;MI_Vehicle_004_4",
idleAnim = "AS_CH_Vehicle_004_ProfileIdle_Loop_001;AS_CH_Vehicle_004_ldle_Loop_003;AS_VH_Vehicle_004_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 0,
scaleTimes = 40,
outPropShow = "AS_VH_Vehicle_004_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_004",
shareTexts = {
"乘龙兮辚辚，高驰兮冲天"
},
shareAnim = "AS_VH_Vehicle_004_Pose_001",
minVer = "*********",
beginTime = {
seconds = 1724947200
},
rotateYaw = 190,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_004.astc",
suitId = 20003,
suitName = "苍穹玄龙",
suitIcon = "CDN:Icon_Vehicle_004",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_004.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_004.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730008] = {
id = 730008,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "苍穹玄龙",
desc = "如梦似幻，踏云追月",
icon = "CDN:Icon_Vehicle_004_01",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
belongTo = 730007,
fashionValue = 145,
belongToGroup = {
730008,
730009
}
},
resourceConf = {
material = "MI_Vehicle_004_1_HP01;MI_Vehicle_004_2_HP01;MI_Vehicle_004_3_HP01;MI_Vehicle_004_4_HP01",
idleAnim = "AS_CH_Vehicle_004_ProfileIdle_Loop_001;AS_CH_Vehicle_004_ldle_Loop_003;AS_VH_Vehicle_004_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 95507,
scaleTimes = 40,
outPropShow = "AS_VH_Vehicle_004_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_004",
shareTexts = {
"乘龙兮辚辚，高驰兮冲天"
},
shareAnim = "AS_VH_Vehicle_004_Pose_001",
rotateYaw = 190,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_004.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_004.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_004.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730009] = {
id = 730009,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "苍穹玄龙",
desc = "如梦似幻，踏云追月",
icon = "CDN:Icon_Vehicle_004_02",
getWay = "商城",
jumpId = {
15
},
outlookConf = {
belongTo = 730007,
fashionValue = 145,
belongToGroup = {
730008,
730009
}
},
resourceConf = {
material = "MI_Vehicle_004_1_HP02;MI_Vehicle_004_2_HP02;MI_Vehicle_004_3_HP02;MI_Vehicle_004_4_HP02",
idleAnim = "AS_CH_Vehicle_004_ProfileIdle_Loop_001;AS_CH_Vehicle_004_ldle_Loop_003;AS_VH_Vehicle_004_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 95508,
scaleTimes = 40,
outPropShow = "AS_VH_Vehicle_004_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_004",
shareTexts = {
"乘龙兮辚辚，高驰兮冲天"
},
shareAnim = "AS_VH_Vehicle_004_Pose_001",
rotateYaw = 190,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_004.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_004.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_004.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 20
},
[730010] = {
id = 730010,
exceedReplaceItem = {
{
itemId = 225,
itemNum = 6
}
},
quality = 3,
name = "星辉幼角",
desc = "甜蜜不是目的地，而是一路有你",
icon = "CDN:Icon_Vehicle_005",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
fashionValue = 800
},
resourceConf = {
material = "MI_Vehicle_005_1;MI_Vehicle_005_2;MI_Vehicle_005_3",
idleAnim = "AS_CH_Vehicle_005_ProfileIdle_Loop_001;AS_CH_Vehicle_005_Idle_Loop_001;AS_VH_Vehicle_005_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3",
extraOffset = {
5
}
},
commodityId = 0,
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_005_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_005",
shareTexts = {
"一起去看看满是糖果的城堡"
},
shareAnim = "AS_VH_Vehicle_005_Pose_001",
minVer = "*********",
beginTime = {
seconds = 1735660800
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_005.astc",
suitId = 20004,
suitName = "星辉幼角",
suitIcon = "CDN:Icon_Vehicle_005",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_005.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_005.astc",
shareOffset = {
0,
300,
-150,
0,
150,
0,
45
},
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 13,
SeatingCount = 1,
SkillIds = {
73001001,
73001002
}
},
[730011] = {
id = 730011,
exceedReplaceItem = {
{
itemId = 225,
itemNum = 9
}
},
quality = 2,
name = "星澜灵角",
desc = "上车的这一刻，是童话故事的开始",
icon = "CDN:Icon_Vehicle_006",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
fashionValue = 1200
},
resourceConf = {
material = "MI_Vehicle_006_1;MI_Vehicle_006_2;MI_Vehicle_006_3;MI_Vehicle_006_4",
idleAnim = "AS_CH_Vehicle_006_ProfileIdle_Loop_001;AS_CH_Vehicle_006_Idle_Loop_002;AS_VH_Vehicle_006_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 0,
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_006_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_006",
shareTexts = {
"彩虹之下，快乐冒险不停歇"
},
shareAnim = "AS_VH_Vehicle_006_Pose_001",
minVer = "*********",
beginTime = {
seconds = 1735660800
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_006.astc",
suitId = 20005,
suitName = "星澜灵角",
suitIcon = "CDN:Icon_Vehicle_006",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_006.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_006.astc",
shareOffset = {
0,
300,
-150,
0,
150,
0,
45
},
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 17,
SeatingCount = 2,
SkillIds = {
73001101
}
},
[730012] = {
id = 730012,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "星澜灵角",
desc = "上车的这一刻，是童话故事的开始",
icon = "CDN:Icon_Vehicle_006_01",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
belongTo = 730011,
fashionValue = 145,
belongToGroup = {
730012
}
},
resourceConf = {
material = "MI_Vehicle_006_1_HP01;MI_Vehicle_006_2_HP01;MI_Vehicle_006_3_HP01;MI_Vehicle_006_4_HP01",
idleAnim = "AS_CH_Vehicle_006_ProfileIdle_Loop_001;AS_CH_Vehicle_006_Idle_Loop_002;AS_VH_Vehicle_006_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 95511,
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_006_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_006",
shareTexts = {
"彩虹之下，快乐冒险不停歇"
},
shareAnim = "AS_VH_Vehicle_006_Pose_001",
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_006.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_006.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_006.astc",
shareOffset = {
0,
300,
-150,
0,
150,
0,
45
},
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 17,
SeatingCount = 2,
SkillIds = {
73001101
}
},
[730013] = {
id = 730013,
exceedReplaceItem = {
{
itemId = 225,
itemNum = 18
}
},
quality = 1,
name = "璀璨星翼",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_007",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
fashionValue = 2000
},
resourceConf = {
material = "MI_Vehicle_007_1;MI_Vehicle_007_2;MI_Vehicle_007_3;MI_Vehicle_007_4;MI_Vehicle_007_5;MI_Vehicle_007_6;MI_Vehicle_007_7",
idleAnim = "AS_CH_Vehicle_007_ProfileIdle_Loop_001;AS_CH_Vehicle_006_Idle_Loop_002;AS_CH_Vehicle_006_Idle_Loop_002;AS_CH_Vehicle_006_Idle_Loop_002;AS_VH_Vehicle_007_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4;Vehicle_5;Vehicle_6;Vehicle_7",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_007_appear_002",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_007_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_007",
shareTexts = {
"是来接公主的专车"
},
minVer = "*********",
beginTime = {
seconds = 1735660800
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
suitId = 20006,
suitName = "璀璨星翼",
suitIcon = "CDN:Icon_Vehicle_007",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket_001",
"Bip001-PelvisSocket_002",
"Bip001-PelvisSocket_003"
},
perfWeight = 20,
SeatingCount = 4,
SkillIds = {
73001301,
73001302,
73001303
}
},
[730014] = {
id = 730014,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "璀璨星翼",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_007_01",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
belongTo = 730013,
fashionValue = 175,
belongToGroup = {
730014,
730015
}
},
resourceConf = {
material = "MI_Vehicle_007_1_HP01;MI_Vehicle_007_2_HP01;MI_Vehicle_007_3_HP01;MI_Vehicle_007_4_HP01;MI_Vehicle_007_5_HP01;MI_Vehicle_007_6_HP01;MI_Vehicle_007_7_HP01",
idleAnim = "AS_CH_Vehicle_007_ProfileIdle_Loop_001;AS_CH_Vehicle_006_Idle_Loop_002;AS_CH_Vehicle_006_Idle_Loop_002;AS_CH_Vehicle_006_Idle_Loop_002;AS_VH_Vehicle_007_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4;Vehicle_5;Vehicle_6;Vehicle_7",
extraOffset = {
5
}
},
commodityId = 95513,
outEnter = "AS_VH_Vehicle_007_appear_002",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_007_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_007",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket_001",
"Bip001-PelvisSocket_002",
"Bip001-PelvisSocket_003"
},
perfWeight = 20,
SeatingCount = 4,
SkillIds = {
73001301,
73001302,
73001303
}
},
[730015] = {
id = 730015,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "璀璨星翼",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_007_02",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
belongTo = 730013,
fashionValue = 175,
belongToGroup = {
730014,
730015
}
},
resourceConf = {
material = "MI_Vehicle_007_1_HP02;MI_Vehicle_007_2_HP02;MI_Vehicle_007_3_HP02;MI_Vehicle_007_4_HP02;MI_Vehicle_007_5_HP02;MI_Vehicle_007_6_HP02;MI_Vehicle_007_7_HP02",
idleAnim = "AS_CH_Vehicle_007_ProfileIdle_Loop_001;AS_CH_Vehicle_006_Idle_Loop_002;AS_CH_Vehicle_006_Idle_Loop_002;AS_CH_Vehicle_006_Idle_Loop_002;AS_VH_Vehicle_007_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4;Vehicle_5;Vehicle_6;Vehicle_7",
extraOffset = {
5
}
},
commodityId = 95514,
outEnter = "AS_VH_Vehicle_007_appear_002",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_007_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_007",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket_001",
"Bip001-PelvisSocket_002",
"Bip001-PelvisSocket_003"
},
perfWeight = 20,
SeatingCount = 4,
SkillIds = {
73001301,
73001302,
73001303
}
},
[730016] = {
id = 730016,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "璀璨星翼",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_008",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
fashionValue = 2000
},
resourceConf = {
material = "MI_Vehicle_008_1;MI_Vehicle_008_2;MI_Vehicle_008_3;MI_Vehicle_008_4",
idleAnim = "AS_CH_Vehicle_008_ProfileIdle_Loop_001;AS_CH_Vehicle_008_Idle_Loop_001;AS_VH_Vehicle_008_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_008_appear_002",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_008_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_008",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 20,
SeatingCount = 1,
SkillIds = {
73001301,
73001302,
73001303
}
},
[730017] = {
id = 730017,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "璀璨星翼",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_008_01",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
belongTo = 730016,
fashionValue = 175,
belongToGroup = {
730017,
730018
}
},
resourceConf = {
material = "MI_Vehicle_008_1_HP01;MI_Vehicle_008_2_HP01;MI_Vehicle_008_3_HP01;MI_Vehicle_008_4_HP01",
idleAnim = "AS_CH_Vehicle_008_ProfileIdle_Loop_001;AS_CH_Vehicle_008_Idle_Loop_001;AS_VH_Vehicle_008_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_008_appear_002",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_008_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_008",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 20,
SeatingCount = 1,
SkillIds = {
73001301,
73001302,
73001303
}
},
[730018] = {
id = 730018,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "璀璨星翼",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_008_02",
getWay = "云梦绮旅",
jumpId = {
634
},
outlookConf = {
belongTo = 730016,
fashionValue = 175,
belongToGroup = {
730017,
730018
}
},
resourceConf = {
material = "MI_Vehicle_008_1_HP02;MI_Vehicle_008_2_HP02;MI_Vehicle_008_3_HP02;MI_Vehicle_008_4_HP02",
idleAnim = "AS_CH_Vehicle_008_ProfileIdle_Loop_001;AS_CH_Vehicle_008_Idle_Loop_001;AS_VH_Vehicle_008_ProfileIdle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_008_appear_002",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_008_ProfileIdle_Loop_001",
outPropSkeletal = "SK_Vehicle_008",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 20,
SeatingCount = 1,
SkillIds = {
73001301,
73001302,
73001303
}
},
[730019] = {
id = 730019,
exceedReplaceItem = {
{
itemId = 225,
itemNum = 9
}
},
quality = 2,
name = "星澜灵角（测试）",
desc = "上车的这一刻，是童话故事的开始",
icon = "CDN:Icon_Vehicle_006",
outlookConf = {
fashionValue = 1200
},
resourceConf = {
material = "MI_Vehicle_006_1;MI_Vehicle_006_2;MI_Vehicle_006_3;MI_Vehicle_006_4",
idleAnim = "AS_CH_Vehicle_009_Idle_Loop_001;AS_CH_Vehicle_009_Idle_Loop_001;AS_VH_Vehicle_009_Idle_Loop_001",
materialSlot = "Vehicle_1;Vehicle_2;Vehicle_3;Vehicle_4",
extraOffset = {
5
}
},
commodityId = 0,
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_009_Idle_Loop_001",
outPropSkeletal = "SK_Vehicle_009_Lod1",
shareTexts = {
"彩虹之下，快乐冒险不停歇"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_006.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_006.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_006.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 17,
SeatingCount = 2,
SkillIds = {
73001101
},
isNewVehicle = true
},
[730020] = {
id = 730020,
quality = 1,
name = "蝴蝶",
resourceConf = {
material = "MI_OG_043_Prop",
materialSlot = "prop",
extraOffset = {
5
}
},
scaleTimes = 45,
outPropSkeletal = "SK_OG_043_Prop_001",
rotateYaw = 210,
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 1,
isNewVehicle = true
},
[730021] = {
id = 730021,
quality = 1,
name = "蝴蝶",
resourceConf = {
material = "MI_OG_043_Prop_HP01",
materialSlot = "prop",
extraOffset = {
5
}
},
scaleTimes = 45,
outPropSkeletal = "SK_OG_043_Prop_001",
rotateYaw = 210,
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 1,
isNewVehicle = true
},
[730022] = {
id = 730022,
quality = 1,
name = "蝴蝶",
resourceConf = {
material = "MI_OG_043_Prop_HP02",
materialSlot = "prop",
extraOffset = {
5
}
},
scaleTimes = 45,
outPropSkeletal = "SK_OG_043_Prop_001",
rotateYaw = 210,
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 1,
isNewVehicle = true
},
[730023] = {
id = 730023,
exceedReplaceItem = {
{
itemId = 225,
itemNum = 6
}
},
quality = 3,
name = "九尾狐一阶",
desc = "甜蜜不是目的地，而是一路有你",
icon = "CDN:Icon_Vehicle_005",
outlookConf = {
fashionValue = 800
},
resourceConf = {
material = "MI_Vehicle_009_1",
idleAnim = "AS_CH_Vehicle_009_Idle_Loop_001;AS_CH_Vehicle_009_IdleShow_001;AS_VH_Vehicle_009_Idle_Loop_001",
materialSlot = "Skin",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_009_Appear_001",
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_009_Idle_Loop_001",
outPropSkeletal = "SK_Vehicle_009",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main"
},
perfWeight = 13,
SeatingCount = 1,
SkillIds = {
73002301,
73002302
},
DecorateIds = {
891000
}
},
[730024] = {
id = 730024,
exceedReplaceItem = {
{
itemId = 225,
itemNum = 9
}
},
quality = 2,
name = "九尾狐二阶原色",
desc = "上车的这一刻，是童话故事的开始",
icon = "CDN:Icon_Vehicle_006",
outlookConf = {
fashionValue = 1200
},
resourceConf = {
material = "MI_Vehicle_010_1",
idleAnim = "AS_CH_Vehicle_010_Idle_Loop_001;AS_CH_Vehicle_010_Idle_Loop_002;AS_VH_Vehicle_010_Idle_Loop_001",
materialSlot = "Vehicle_010",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_010_Appear_001",
scaleTimes = 50,
outPropShow = "AS_VH_Vehicle_010_Idle_Loop_001",
outPropSkeletal = "SK_Vehicle_010",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 17,
SeatingCount = 2,
SkillIds = {
73002401
},
DecorateIds = {
891001,
891002
}
},
[730025] = {
id = 730025,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 2,
name = "九尾狐二阶染色",
desc = "上车的这一刻，是童话故事的开始",
icon = "CDN:Icon_Vehicle_006_01",
outlookConf = {
belongTo = 730024,
fashionValue = 145,
belongToGroup = {
730025
}
},
resourceConf = {
material = "MI_Vehicle_010_1",
idleAnim = "AS_CH_Vehicle_010_Idle_Loop_001;AS_CH_Vehicle_010_Idle_Loop_002;AS_VH_Vehicle_010_Idle_Loop_001",
materialSlot = "Vehicle_010",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_010_Appear_001",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_010_Idle_Loop_001",
outPropSkeletal = "SK_Vehicle_010",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket"
},
perfWeight = 17,
SeatingCount = 2,
SkillIds = {
73002401
},
DecorateIds = {
891001,
891002
}
},
[730026] = {
id = 730026,
exceedReplaceItem = {
{
itemId = 225,
itemNum = 18
}
},
quality = 1,
name = "九尾狐三阶原色",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_007",
outlookConf = {
fashionValue = 2000
},
resourceConf = {
material = "MI_RimPlus_12;MI_Vehicle_011_2;MI_RimPlus_011",
idleAnim = "AS_CH_Vehicle_011_Idle_Loop_001;AS_CH_Vehicle_011_Idle_Loop_002;AS_CH_Vehicle_011_Idle_Loop_003;AS_CH_Vehicle_011_Idle_Loop_004;AS_VH_Vehicle_011_Idle_Loop_001",
materialSlot = "Skin;Skin_ncl1_1;Skin_2",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_011_Appear_001",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_011_Idle_Loop_001",
outPropSkeletal = "SK_Vehicle_011",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket_001",
"Bip001-PelvisSocket_002",
"Bip001-PelvisSocket_003"
},
perfWeight = 20,
SeatingCount = 4,
SkillIds = {
73002601,
73002602
},
DecorateIds = {
891003,
891004,
891005
}
},
[730027] = {
id = 730027,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "九尾狐三阶染色1",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_007_01",
outlookConf = {
belongTo = 730026,
fashionValue = 175,
belongToGroup = {
730027,
730028
}
},
resourceConf = {
material = "MI_RimPlus_12;MI_Vehicle_011_2;MI_RimPlus_011",
idleAnim = "AS_CH_Vehicle_011_Idle_Loop_001;AS_CH_Vehicle_011_Idle_Loop_002;AS_CH_Vehicle_011_Idle_Loop_003;AS_CH_Vehicle_011_Idle_Loop_004;AS_VH_Vehicle_011_Idle_Loop_001",
materialSlot = "Skin;Skin_ncl1_1;Skin_2",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_011_Appear_001",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_011_Idle_Loop_001",
outPropSkeletal = "SK_Vehicle_011",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket_001",
"Bip001-PelvisSocket_002",
"Bip001-PelvisSocket_003"
},
perfWeight = 20,
SeatingCount = 4,
SkillIds = {
73002601,
73002602
},
DecorateIds = {
891003,
891004,
891005
}
},
[730028] = {
id = 730028,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "九尾狐三阶染色2",
desc = "要相信，世间真的有独角兽和精灵",
icon = "CDN:Icon_Vehicle_007_02",
outlookConf = {
belongTo = 730026,
fashionValue = 175,
belongToGroup = {
730027,
730028
}
},
resourceConf = {
material = "MI_RimPlus_12;MI_Vehicle_011_2;MI_RimPlus_011",
idleAnim = "AS_CH_Vehicle_011_Idle_Loop_001;AS_CH_Vehicle_011_Idle_Loop_002;AS_CH_Vehicle_011_Idle_Loop_003;AS_CH_Vehicle_011_Idle_Loop_004;AS_VH_Vehicle_011_Idle_Loop_001",
materialSlot = "Skin;Skin_ncl1_1;Skin_2",
extraOffset = {
5
}
},
commodityId = 0,
outEnter = "AS_VH_Vehicle_011_Appear_001",
scaleTimes = 45,
outPropShow = "AS_VH_Vehicle_011_Idle_Loop_001",
outPropSkeletal = "SK_Vehicle_011",
shareTexts = {
"是来接公主的专车"
},
rotateYaw = 210,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_background_007.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_name_007.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Vehicle_bubble_007.astc",
bEnableDoubleShow = true,
previewSocketNames = {
"Bip001-PelvisSocket_Main",
"Bip001-PelvisSocket_001",
"Bip001-PelvisSocket_002",
"Bip001-PelvisSocket_003"
},
perfWeight = 20,
SeatingCount = 4,
SkillIds = {
73002601,
73002602
},
DecorateIds = {
891003,
891004,
891005
}
}
}

local mt = {
type = "ItemType_Vehicle",
stackedNum = 1,
maxNum = 1,
bHideInBag = false,
bNotCostCount = false,
profileRotateYaw = 210,
bEnableDoubleShow = false,
isNewVehicle = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data