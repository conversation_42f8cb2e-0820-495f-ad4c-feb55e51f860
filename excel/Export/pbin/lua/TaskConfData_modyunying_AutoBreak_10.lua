--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_特色玩法运营.xlsx: 活动任务

local data = {
[651403] = {
id = 651403,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 10,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3497
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 1001480
},
[651405] = {
id = 651405,
name = "谁是狼人新地图上线，去看看→",
desc = "谁是狼人新地图上线，去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
310
}
}
}
}
}
}
},
reward = {
itemIdList = {
3496
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001483
},
[651406] = {
id = 651406,
name = "【每日】完成1次谁是狼人对局",
desc = "【每日】完成1次谁是狼人对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3496
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001481
},
[651407] = {
id = 651407,
name = "【每周】完成10次谁是狼人普通任务",
desc = "【每周】完成10次谁是狼人普通任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 53,
value = 10,
subConditionList = {
{
type = 2,
value = {
105,
106,
107,
108,
109,
110,
111,
151,
112,
113
}
},
{
type = 49,
value = {
61
}
}
}
}
}
}
},
reward = {
itemIdList = {
3496
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001482
},
[651408] = {
id = 651408,
name = "【每周】组队完成3次谁是狼人对局",
desc = "【每周】组队完成3次谁是狼人对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
151,
112,
113
}
},
{
type = 88,
value = {
0
}
}
}
}
}
}
},
reward = {
itemIdList = {
3496
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001482
},
[651409] = {
id = 651409,
name = "【累计】游玩3次谁是狼人新地图精灵谷",
desc = "【累计】游玩3次谁是狼人新地图精灵谷",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
},
{
type = 9,
value = {
50309
}
}
}
}
}
}
},
reward = {
itemIdList = {
3496
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001483
},
[651410] = {
id = 651410,
name = "完成1局峡谷5v5",
desc = "完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001485
},
[651411] = {
id = 651411,
name = "完成1局谁是狼人",
desc = "完成1局谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001485
},
[651412] = {
id = 651412,
name = "完成1局天天晋级赛",
desc = "完成1局天天晋级赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001485
},
[651413] = {
id = 651413,
name = "完成1局大王别抓我",
desc = "完成1局大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001485
},
[651414] = {
id = 651414,
name = "在好友农场祈福5次",
desc = "在好友农场祈福5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 5
}
}
}
},
reward = {
itemIdList = {
3512
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1001485
},
[651415] = {
id = 651415,
name = "兑换",
desc = "自选大王/moba排位加星卡*1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
203001
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651416] = {
id = 651416,
name = "兑换",
desc = "晋级赛BP经验*300",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
300
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651417] = {
id = 651417,
name = "兑换",
desc = "狼人bp值*200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
1014
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651418] = {
id = 651418,
name = "兑换",
desc = "峡谷bp值*200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
1015
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651419] = {
id = 651419,
name = "兑换",
desc = "狼人币*100",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 60,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
100
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651420] = {
id = 651420,
name = "兑换",
desc = "蓝色星运宝箱*1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 80,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
300102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651421] = {
id = 651421,
name = "兑换",
desc = "磷虾*2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 100,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651422] = {
id = 651422,
name = "兑换",
desc = "自选大王/moba排位加星卡*1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 120,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
203001
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651423] = {
id = 651423,
name = "兑换",
desc = "磷虾*4",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 140,
subConditionList = {
{
type = 3,
value = {
3512
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
4
},
validPeriodList = {
0
}
},
taskGroupId = 1001486
},
[651424] = {
id = 651424,
name = "特色咨询-每日奖励",
desc = "特色咨询-每日奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
200018
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001487
},
[651425] = {
id = 651425,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022098
},
[651426] = {
id = 651426,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022099
},
[651427] = {
id = 651427,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022100
},
[651428] = {
id = 651428,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022101
},
[651429] = {
id = 651429,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022102
},
[651430] = {
id = 651430,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022103
},
[651431] = {
id = 651431,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022104
},
[651432] = {
id = 651432,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022105
},
[651433] = {
id = 651433,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022106
},
[651434] = {
id = 651434,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022107
},
[651440] = {
id = 651440,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
220
},
numList = {
50
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001490
},
[651441] = {
id = 651441,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 2,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001490
},
[651442] = {
id = 651442,
name = "第3天",
desc = "第3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 3,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
50
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001490
},
[651443] = {
id = 651443,
name = "第4天",
desc = "第4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 4,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
711392
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001490
},
[659279] = {
id = 659279,
name = "分享鱼塘幸运星第四期",
desc = "分享鱼塘幸运星第四期",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659279
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1022086
},
[659280] = {
id = 659280,
name = "4.5 翡光仙灵祈愿上线！去看看→",
desc = "4.5 翡光仙灵祈愿上线！去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
598
}
}
}
}
}
}
},
reward = {
itemIdList = {
317129
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 598,
taskGroupId = 1022093
},
[659281] = {
id = 659281,
name = "4.7 12点农场欢乐派对限时开启！去看看→",
desc = "4.7 12点农场欢乐派对限时开启！去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
5102
}
}
}
}
}
}
},
reward = {
itemIdList = {
317129
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022094
},
[659282] = {
id = 659282,
name = "【每日】收获10次农作物",
desc = "【每日】收获10次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 10,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317129
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022091
},
[659283] = {
id = 659283,
name = "【每周】在好友农场拿取20次农作物",
desc = "【每周】在好友农场拿取20次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 20,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317129
},
numList = {
4
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022092
},
[659284] = {
id = 659284,
name = "【每周】在好友农场祈福20次",
desc = "【每周】在好友农场祈福20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 20
}
}
}
},
reward = {
itemIdList = {
317129
},
numList = {
4
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022092
},
[659285] = {
id = 659285,
name = "【累计】在星宝农场解锁9个地块",
desc = "【累计】在星宝农场解锁9个地块",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 171,
value = 9,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317129
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022110
},
[659286] = {
id = 659286,
name = "播种5次",
desc = "播种5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 155,
value = 5,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659287] = {
id = 659287,
name = "收获5次农作物",
desc = "收获5次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 5,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659288] = {
id = 659288,
name = "在好友农场拿取1次农作物",
desc = "在好友农场拿取1次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 1,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659289] = {
id = 659289,
name = "收获1次动物产品",
desc = "收获1次动物产品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 1,
subConditionList = {
{
type = 159,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659290] = {
id = 659290,
name = "在星宝农场钓鱼1次",
desc = "在星宝农场钓鱼1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 209,
value = 1
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659291] = {
id = 659291,
name = "通过温泉获取增益1次",
desc = "通过温泉获取增益1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 900,
value = 1,
subConditionList = {
{
type = 600,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659292] = {
id = 659292,
name = "在好友农场祈福1次",
desc = "在好友农场祈福1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 1
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659293] = {
id = 659293,
name = "去好友农场赠礼1次",
desc = "去好友农场赠礼1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 257,
value = 1
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659294] = {
id = 659294,
name = "在星宝农场加工1次",
desc = "在星宝农场加工1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 238,
value = 1
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659295] = {
id = 659295,
name = "拜访好友的小屋1次",
desc = "拜访好友的小屋1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 243,
value = 1
}
}
}
},
reward = {
itemIdList = {
4,
3421
},
numList = {
50,
1
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1022096
},
[659296] = {
id = 659296,
name = "涂色次数达2次",
desc = "涂色次数达2次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
3421
}
}
}
}
}
}
},
reward = {
itemIdList = {
290011
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022097
},
[659297] = {
id = 659297,
name = "涂色次数达4次",
desc = "涂色次数达4次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 4,
subConditionList = {
{
type = 3,
value = {
3421
}
}
}
}
}
}
},
reward = {
itemIdList = {
290012
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022097
},
[659298] = {
id = 659298,
name = "涂色次数达5次",
desc = "涂色次数达5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 5,
subConditionList = {
{
type = 3,
value = {
3421
}
}
}
}
}
}
},
reward = {
itemIdList = {
200620
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1022097
},
[659299] = {
id = 659299,
name = "涂色次数达6次",
desc = "涂色次数达6次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 6,
subConditionList = {
{
type = 3,
value = {
3421
}
}
}
}
}
}
},
reward = {
itemIdList = {
218821
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022097
},
[659300] = {
id = 659300,
name = "农场欢乐派对活动限时开启！去看看→",
desc = "农场欢乐派对活动限时开启！去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
5102
}
}
}
}
}
}
},
reward = {
itemIdList = {
290012
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022111
},
[659301] = {
id = 659301,
name = "5.1 甜梦嘉年华祈愿开启！去看看",
desc = "5.1 甜梦嘉年华祈愿开启！去看看",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
669
}
}
}
}
}
}
},
reward = {
itemIdList = {
317130
},
numList = {
10
}
},
jumpId = 669,
taskGroupId = 1022112
},
[659302] = {
id = 659302,
name = "【每周】收获50次作物",
desc = "【每周】收获50次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 50,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317130
},
numList = {
20
}
},
jumpId = 5102,
taskGroupId = 1022113
},
[659303] = {
id = 659303,
name = "【每周】在星宝农场钓鱼60次",
desc = "【每周】在星宝农场钓鱼60次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 209,
value = 60
}
}
}
},
reward = {
itemIdList = {
317130
},
numList = {
20
}
},
jumpId = 5102,
taskGroupId = 1022113
},
[659304] = {
id = 659304,
name = "【累计】在星宝农场祈福50次",
desc = "【累计】在星宝农场祈福50次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 50
}
}
}
},
reward = {
itemIdList = {
317130
},
numList = {
40
}
},
jumpId = 5103,
taskGroupId = 1022114
},
[659305] = {
id = 659305,
name = "游玩1次【萌宠打工模拟器】地图",
desc = "游玩1次【萌宠打工模拟器】地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 20,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317130
},
numList = {
10
}
},
jumpId = 5102,
taskGroupId = 1022114
},
[780738] = {
id = 780738,
name = "大档_白银峡谷星V",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
2,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 1082002
},
[780741] = {
id = 780741,
name = "小档_黄金峡谷星II",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
711498
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082001
},
[780744] = {
id = 780744,
name = "大档_钻石峡谷星V",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
14
},
numList = {
6
},
validPeriodList = {
0
}
},
taskGroupId = 1082002
},
[780747] = {
id = 780747,
name = "小档_至尊峡谷星II",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
711463
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082001
},
[780750] = {
id = 780750,
name = "大档_无双峡谷星",
desc = "第13赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120009,
140006
}
}
}
}
}
}
},
reward = {
itemIdList = {
407049
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082002
},
[780753] = {
id = 780753,
name = "大档_黄金峡谷星V",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
14
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1082004
},
[780756] = {
id = 780756,
name = "小档_铂金峡谷星II",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
4
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
203012
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082003
},
[780759] = {
id = 780759,
name = "大档_至尊峡谷星V",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
1
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
411160
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1082004
},
[780762] = {
id = 780762,
name = "小档_最强峡谷星III",
desc = "第12赛季峡谷系列段位奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
3
}
},
{
type = 145,
value = {
12,
14
}
},
{
type = 154,
value = {
120008,
140005
}
}
}
}
}
}
},
reward = {
itemIdList = {
203012
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1082003
},
[780845] = {
id = 780845,
name = "答对所有题目",
desc = "答对所有题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 602,
value = 1,
subConditionList = {
{
type = 248,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
300102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078420
},
[780846] = {
id = 780846,
name = "阅读英雄故事",
desc = "阅读英雄故事",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
780835
}
}
}
}
}
}
},
reward = {
itemIdList = {
300101
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078421
},
[780847] = {
id = 780847,
name = "免费体验1天刘禅（自动使用）",
desc = "免费体验1天刘禅（自动使用）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
301020
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078422
},
[780848] = {
id = 780848,
name = "【刘禅】完成1局峡谷3v3/5v5",
desc = "【刘禅】完成1局峡谷3v3/5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 1,
subConditionList = {
{
type = 2,
value = {
5500,
5600,
5601,
6101,
6102
}
},
{
type = 50,
value = {
300,
1,
1043
}
}
}
}
}
}
},
reward = {
itemIdList = {
300102
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078422
},
[780849] = {
id = 780849,
name = "【刘禅】完成3局峡谷3v3/5v5",
desc = "【刘禅】完成3局峡谷3v3/5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5500,
5600,
5601,
6101,
6102
}
},
{
type = 50,
value = {
300,
1,
1043
}
}
}
}
}
}
},
reward = {
itemIdList = {
300103
},
numList = {
4
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078422
},
[780853] = {
id = 780853,
name = "【每日】组队完成1局峡谷5v5",
desc = "【每日】组队完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 88,
value = {
0
}
}
}
}
}
}
},
reward = {
itemIdList = {
329937
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078424
},
[780854] = {
id = 780854,
name = "【每日】完成1局峡谷5v5模式",
desc = "【每日】完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329937
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078425
},
[780855] = {
id = 780855,
name = "【每日】完成2局峡谷5v5模式",
desc = "【每日】完成2局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329937
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078425
},
[780856] = {
id = 780856,
name = "组队完成1局峡谷5v5",
desc = "组队完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 88,
value = {
0
}
}
}
}
}
}
},
reward = {
itemIdList = {
300102
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078426
},
[780857] = {
id = 780857,
name = "完成1局峡谷5v5模式",
desc = "完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
300101
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078426
},
[780858] = {
id = 780858,
name = "完成2局峡谷5v5模式",
desc = "完成2局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
300102
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078426
},
[780859] = {
id = 780859,
name = "组队2人",
desc = "组队2人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 268,
value = 2
}
}
}
},
reward = {
itemIdList = {
331025
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078427
},
[780860] = {
id = 780860,
name = "组队3人",
desc = "组队3人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 268,
value = 3
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 1078427
},
[780861] = {
id = 780861,
name = "周一只想划水，4.21起登录领动作！",
desc = "周一只想划水，4.21起登录领动作！",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
720628
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078428
},
[780862] = {
id = 780862,
name = "组队完成1局峡谷3v3模式",
desc = "组队完成1局峡谷3v3模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5500,
5600,
5601
}
},
{
type = 88,
value = {
0
}
}
}
}
}
}
},
reward = {
itemIdList = {
300302
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1016,
taskGroupId = 1078429
},
[780863] = {
id = 780863,
name = "完成3局峡谷3v3模式",
desc = "完成3局峡谷3v3模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
5500,
5600,
5601
}
}
}
}
}
}
},
reward = {
itemIdList = {
300303
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1016,
taskGroupId = 1078429
},
[780864] = {
id = 780864,
name = "完成5局峡谷5v5模式",
desc = "完成5局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
5500,
5600,
5601
}
}
}
}
}
}
},
reward = {
itemIdList = {
711349
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1016,
taskGroupId = 1078429
},
[780865] = {
id = 780865,
name = "峡谷勋章跳转测试",
desc = "峡谷勋章跳转测试",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 834,
value = 1,
subConditionList = {
{
type = 215,
value = {
0,
100
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 50114,
taskGroupId = 1078409
},
[780890] = {
id = 780890,
name = "阅读英雄故事",
desc = "阅读英雄故事",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
780890
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1078450
},
[780891] = {
id = 780891,
name = "查看视频",
desc = "查看视频",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 320,
value = 1,
subConditionList = {
{
type = 330,
value = {
3626
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1078451
},
[780892] = {
id = 780892,
name = "答对所有题目",
desc = "答对所有题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 602,
value = 1,
subConditionList = {
{
type = 248,
value = {
15
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1078453
},
[780893] = {
id = 780893,
name = "免费体验3天晴霜（自动使用）",
desc = "免费体验3天晴霜（自动使用）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
301232
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078452
},
[780894] = {
id = 780894,
name = "【晴霜】完成1局峡谷5v5",
desc = "【晴霜】完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 50,
value = {
300,
1,
1047
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078452
},
[780895] = {
id = 780895,
name = "游玩1次巅峰擂台赛（多人）地图",
desc = "游玩1次巅峰擂台赛（多人）地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 100,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204946721
}
}
}
}
}
}
},
reward = {
itemIdList = {
300101,
290022
},
numList = {
1,
1
},
validPeriodList = {
0,
0
}
},
jumpId = 83,
taskGroupId = 1078452
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data