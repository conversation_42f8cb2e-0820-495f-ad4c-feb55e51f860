--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 头像框

local v0 = 3

local v1 = "限时活动获得"

local v2 = 0

local data = {
[840189] = {
id = 840189,
lowVer = "1.3.26.1",
quality = 3,
name = "奇趣游乐场",
desc = v1,
icon = "CDN:T_HeadFrame_550",
isDynamic = 0,
beginTime = {
seconds = 1729180800
}
},
[840190] = {
id = 840190,
lowVer = "1.3.26.1",
quality = 3,
name = "糖果女巫",
desc = "限时礼包获得",
icon = "CDN:T_HeadFrame_184",
isDynamic = 0,
beginTime = {
seconds = 1729785600
}
},
[840191] = {
id = 840191,
lowVer = "1.3.26.1",
quality = 3,
name = "告白熊的礼物",
desc = "梦幻收藏季限时活动中获得",
icon = "CDN:T_HeadFrame_185",
isDynamic = 0,
beginTime = {
seconds = 1729785600
}
},
[840192] = {
id = 840192,
lowVer = "1.3.26.36",
name = "开心超人联盟",
desc = v1,
icon = "D_HeadFrame_187",
beginTime = {
seconds = 1730390400
}
},
[840193] = {
id = 840193,
lowVer = "1.3.26.36",
name = "不是麻瓜",
desc = v1,
icon = "D_HeadFrame_186",
beginTime = {
seconds = 1730908800
}
},
[840194] = {
id = 840194,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.26.68",
name = "永恒之舞",
desc = "永恒之舞祈愿活动获得",
icon = "D_HeadFrame_188"
},
[840195] = {
id = 840195,
lowVer = "1.3.12.118",
name = "星愿予你",
desc = "星之恋空祈愿活动获得",
icon = "D_HeadFrame_189"
},
[840200] = {
id = 840200,
lowVer = "1.3.26.93",
quality = 3,
name = "星缘奇境",
desc = "星缘奇境祈愿活动获得",
icon = "CDN:T_HeadFrame_176",
isDynamic = 0
},
[840202] = {
id = 840202,
name = "扬帆起航",
desc = "扬帆起航祈愿活动获得",
icon = "D_HeadFrame_191",
beginTime = {
seconds = 1731254400
}
},
[840201] = {
id = 840201,
lowVer = "1.3.26.93",
name = "蔚海绮梦",
desc = "蔚海绮梦祈愿中获得",
icon = "D_HeadFrame_192",
beginTime = {
seconds = 1731427200
}
},
[840203] = {
id = 840203,
lowVer = "1.3.37.1",
quality = 4,
name = "花菜椰椰",
desc = "美食派对通行证获得",
icon = "CDN:T_HeadFrame_201",
isDynamic = 0,
beginTime = {
seconds = 1732809600
}
},
[840204] = {
id = 840204,
lowVer = "1.3.37.1",
quality = 3,
name = "焦糖布丁",
desc = "美食派对通行证获得",
icon = "CDN:T_HeadFrame_202",
isDynamic = 0,
beginTime = {
seconds = 1732809600
}
},
[840205] = {
id = 840205,
lowVer = "1.3.37.1",
name = "甜蜜起司",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_198",
beginTime = {
seconds = 1732809600
}
},
[840206] = {
id = 840206,
lowVer = "1.3.37.1",
name = "美食派对",
desc = "美食派对赛季限时活动获得",
icon = "D_HeadFrame_199",
beginTime = {
seconds = 1732809600
}
},
[840207] = {
id = 840207,
quality = 3,
name = "奶油布丁头像框",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_193",
isDynamic = 0,
beginTime = {
seconds = 1732809600
}
},
[840208] = {
id = 840208,
quality = 3,
name = "甜心派对",
desc = "赛季冲段任务获得",
icon = "CDN:T_HeadFrame_194",
isDynamic = 0,
beginTime = {
seconds = 1732809600
}
},
[840209] = {
id = 840209,
lowVer = "1.3.36.1",
quality = 3,
name = "搜捕目镜",
desc = v1,
icon = "D_HeadFrame_601",
beginTime = {
seconds = 1732032000
}
},
[840210] = {
id = 840210,
lowVer = "1.3.36.1",
quality = 3,
name = "完美伪装",
desc = v1,
icon = "D_HeadFrame_602",
beginTime = {
seconds = 1732032000
}
},
[840211] = {
id = 840211,
lowVer = "1.3.36.1",
quality = 3,
name = "真相只有一个",
desc = "名侦探柯南祈愿活动获得",
icon = "CDN:T_HeadFrame_195",
isDynamic = 0,
beginTime = {
seconds = 1732032000
}
},
[840212] = {
id = 840212,
lowVer = "1.3.37.1",
quality = 3,
name = "破案大师",
desc = v1,
icon = "CDN:T_HeadFrame_200",
isDynamic = 0,
beginTime = {
seconds = 1734192000
}
},
[840213] = {
id = 840213,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.37.68",
name = "冰霜之华",
desc = "冰雪圆舞曲祈愿获得",
icon = "D_HeadFrame_203",
showInView = 0,
beginTime = {
seconds = 1735833600
}
},
[840214] = {
id = 840214,
lowVer = "1.3.37.1",
name = "灼灼风华",
desc = v1,
icon = "CDN:T_HeadFrame_196",
isDynamic = 0
},
[840215] = {
id = 840215,
lowVer = "1.3.37.1",
name = "桃之夭夭",
desc = v1,
icon = "CDN:T_HeadFrame_197",
isDynamic = 0
},
[840216] = {
id = 840216,
lowVer = "1.3.37.14",
quality = 3,
name = "天线宝宝",
desc = "天线宝宝祈愿活动获得",
icon = "CDN:T_HeadFrame_204",
isDynamic = 0,
beginTime = {
seconds = 1733500800
}
},
[840218] = {
id = 840218,
lowVer = "1.3.26.68",
quality = 3,
name = "风吹麦浪",
desc = "卡牌集换活动中获得",
icon = "CDN:T_HeadFrame_205",
isDynamic = 0,
beginTime = {
seconds = 1732809600
}
},
[840219] = {
id = 840219,
lowVer = "1.3.37.37",
name = "真相只有一个",
desc = "柯南联动时装祈愿活动获得",
icon = "D_HeadFrame_195",
beginTime = {
seconds = 1734624000
}
},
[840220] = {
id = 840220,
lowVer = "1.3.37.1",
name = "一岁一礼",
desc = v1,
icon = "CDN:T_HeadFrame_207",
isDynamic = 0
},
[840221] = {
id = 840221,
lowVer = "1.3.37.87",
name = "云梦绮旅",
desc = "云梦绮旅祈愿中获得",
icon = "D_HeadFrame_218",
beginTime = {
seconds = 1735660800
}
},
[840222] = {
id = 840222,
lowVer = "1.3.36.1",
name = "摸鱼达人",
desc = v1,
icon = "D_HeadFrame_208",
beginTime = {
seconds = 1734278400
}
},
[840223] = {
id = 840223,
lowVer = "1.3.36.1",
name = "推理大师",
desc = v1,
icon = "CDN:T_HeadFrame_666",
isDynamic = 0,
beginTime = {
seconds = 1734710400
}
},
[840224] = {
id = 840224,
lowVer = "1.3.36.1",
name = "星幻誓约",
desc = v1,
icon = "D_HeadFrame_190",
beginTime = {
seconds = 1733760000
}
},
[840228] = {
id = 840228,
name = "峡谷战神",
desc = "峡谷战神祈愿活动获得",
icon = "D_HeadFrame_210",
beginTime = {
seconds = 1735920000
}
},
[840229] = {
id = 840229,
name = "资深鉴赏家",
desc = "时尚之路中达到资深鉴赏家获得",
icon = "D_HeadFrame_211"
},
[840230] = {
id = 840230,
name = "殿堂鉴赏家",
desc = "时尚之路中达到殿堂鉴赏家获得",
icon = "D_HeadFrame_212"
},
[840231] = {
id = 840231,
name = "传说鉴赏家",
desc = "时尚之路中达到传说鉴赏家获得",
icon = "D_HeadFrame_213"
},
[840232] = {
id = 840232,
name = "至尊鉴赏家",
desc = "时尚之路中达到至尊鉴赏家获得",
icon = "D_HeadFrame_214"
},
[840233] = {
id = 840233,
lowVer = "1.3.68.1",
quality = 4,
name = "异域风情",
desc = "大唐风华通行证获得",
icon = "CDN:T_HeadFrame_225",
isDynamic = 0,
beginTime = {
seconds = 1737043200
}
},
[840234] = {
id = 840234,
lowVer = "1.3.68.1",
quality = 3,
name = "福运当头",
desc = "大唐风华通行证获得",
icon = "CDN:T_HeadFrame_224",
isDynamic = 0,
beginTime = {
seconds = 1737043200
}
},
[840235] = {
id = 840235,
lowVer = "1.3.68.1",
name = "倾城绝代",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_222",
beginTime = {
seconds = 1737043200
}
},
[840236] = {
id = 840236,
lowVer = "1.3.68.1",
name = "大唐风华",
desc = "大唐风华赛季限时活动获得",
icon = "D_HeadFrame_223",
beginTime = {
seconds = 1737043200
}
},
[840237] = {
id = 840237,
lowVer = "1.3.68.1",
quality = 3,
name = "婉儿头像框",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_219",
isDynamic = 0
},
[840238] = {
id = 840238,
lowVer = "1.3.68.1",
quality = 3,
name = "云窗岁月",
desc = "赛季冲段任务获得",
icon = "CDN:T_HeadFrame_220",
isDynamic = 0,
beginTime = {
seconds = 1737043200
}
},
[840239] = {
id = 840239,
lowVer = "1.3.68.26",
name = "福运琳琅",
desc = "福运琳琅祈愿中获得",
icon = "D_HeadFrame_229",
beginTime = {
seconds = 1737734400
}
},
[840240] = {
id = 840240,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
lowVer = "1.3.68.33",
name = "永昼",
desc = "天启圣谕祈愿活动获得",
icon = "D_HeadFrame_216",
beginTime = {
seconds = 1737993600
}
},
[840241] = {
id = 840241,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
lowVer = "1.3.68.33",
name = "永夜",
desc = "天启圣谕祈愿活动获得",
icon = "D_HeadFrame_215",
beginTime = {
seconds = 1737993600
}
},
[840242] = {
id = 840242,
lowVer = "1.3.68.33",
name = "永恒",
desc = "天启圣谕祈愿活动获得",
icon = "D_HeadFrame_217",
beginTime = {
seconds = 1737993600
}
},
[840243] = {
id = 840243,
lowVer = "1.3.68.1",
quality = 3,
name = "金蛇祈福",
desc = v1,
icon = "CDN:T_HeadFrame_226",
isDynamic = 0
},
[840244] = {
id = 840244,
lowVer = "1.3.68.1",
quality = 3,
name = "杨玉环-盛世芳华",
desc = "峡谷星春福袋获得",
icon = "CDN:CT_Arena_HeadFrame_007",
showInView = 0,
isDynamic = 0
},
[840245] = {
id = 840245,
lowVer = "1.3.68.1",
name = "云海立誓",
desc = "峡谷祈愿活动获得",
icon = "CDN:T_HeadFrame_228",
isDynamic = 0,
beginTime = {
seconds = 1738166400
}
},
[840246] = {
id = 840246,
lowVer = "1.3.68.52",
name = "三丽鸥家族游园会",
desc = "三丽鸥家族祈愿活动获得",
icon = "D_HeadFrame_230",
beginTime = {
seconds = 1737648000
}
},
[840247] = {
id = 840247,
lowVer = "1.3.68.1",
quality = 3,
name = "吉祥锦鲤",
desc = "鱼塘幸运星活动获得",
icon = "CDN:T_HeadFrame_233",
isDynamic = 0,
beginTime = {
seconds = 1737648000
}
},
[840248] = {
id = 840248,
lowVer = "1.3.68.1",
name = "湖畔双姝",
desc = v1,
icon = "D_HeadFrame_231",
beginTime = {
seconds = 1738857600
}
},
[840249] = {
id = 840249,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.68.1",
name = "烦恼清除大师",
desc = v1,
icon = "D_HeadFrame_234",
beginTime = {
seconds = 1737388800
}
},
[840250] = {
id = 840250,
lowVer = "1.3.68.87",
name = "花舞漫天",
desc = "峡谷女明星活动获得",
icon = "D_HeadFrame_232",
beginTime = {
seconds = 1739548800
}
},
[840251] = {
id = 840251,
lowVer = "1.3.68.87",
name = "缘来是你",
desc = "岚汀之约祈愿活动获得",
icon = "D_HeadFrame_235",
beginTime = {
seconds = 1739462400
}
},
[840252] = {
id = 840252,
lowVer = "1.3.12.118",
name = "重度依赖",
desc = v1,
icon = "D_HeadFrame_236",
beginTime = {
seconds = 1739203200
}
},
[840253] = {
id = 840253,
lowVer = "1.3.68.68",
quality = 3,
name = "春日绿意",
desc = v1,
icon = "CDN:T_HeadFrame_227",
isDynamic = 0,
beginTime = {
seconds = 1739462400
}
},
[840254] = {
id = 840254,
lowVer = "1.3.68.116",
name = "桃坞问春",
desc = "桃坞问春祈愿获得",
icon = "CDN:T_HeadFrame_243",
isDynamic = 0,
beginTime = {
seconds = 1740758400
}
},
[840255] = {
id = 840255,
lowVer = "1.3.68.100",
name = "桃源万千",
desc = "桃源万千祈愿获得",
icon = "D_HeadFrame_237",
beginTime = {
seconds = 1740067200
}
},
[840256] = {
id = 840256,
lowVer = "1.3.12.118",
name = "泡汤休息时间",
desc = "官方社区活动",
icon = "CDN:T_HeadFrame_238",
beginTime = {
seconds = 1740758400
}
},
[840257] = {
id = 840257,
quality = 3,
name = "傲娇狼宝头像框",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_242",
isDynamic = 0
},
[840258] = {
id = 840258,
name = "旋转童谣",
desc = "达成特定亲密关系后获得",
icon = "CDN:T_HeadFrame_240",
isDynamic = 0
},
[840259] = {
id = 840259,
name = "共赴花朝",
desc = "达成特定亲密关系后获得",
icon = "CDN:T_HeadFrame_241",
isDynamic = 0
},
[840260] = {
id = 840260,
name = "掌心港湾",
desc = "达成特定亲密关系后获得",
icon = "CDN:T_HeadFrame_239",
isDynamic = 0
},
[840261] = {
id = 840261,
lowVer = "1.3.78.1",
quality = 4,
name = "人畜无害",
desc = "真相之夜通行证获得",
icon = "CDN:T_HeadFrame_245",
isDynamic = 0,
beginTime = {
seconds = 1741881600
}
},
[840262] = {
id = 840262,
lowVer = "1.3.78.1",
quality = 3,
name = "学富五车",
desc = "真相之夜通行证获得",
icon = "CDN:T_HeadFrame_246",
isDynamic = 0,
beginTime = {
seconds = 1741881600
}
},
[840263] = {
id = 840263,
lowVer = "1.3.78.1",
name = "洞察未来",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_248",
beginTime = {
seconds = 1741881600
}
},
[840264] = {
id = 840264,
lowVer = "1.3.78.1",
name = "真相之夜",
desc = "真相之夜赛季限时活动获得",
icon = "D_HeadFrame_251",
beginTime = {
seconds = 1741881600
}
},
[840265] = {
id = 840265,
lowVer = "1.3.68.116",
name = "丰收派对",
desc = "丰收派对祈愿获得",
icon = "CDN:T_HeadFrame_243",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 4074768000
}
},
[840266] = {
id = 840266,
lowVer = "1.3.68.1",
quality = 3,
name = "真相大白",
desc = "赛季冲段任务获得",
icon = "CDN:T_HeadFrame_247",
isDynamic = 0,
beginTime = {
seconds = 1737043200
}
},
[840267] = {
id = 840267,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.68.1",
name = "魅力甜心",
desc = v1,
icon = "D_HeadFrame_249",
beginTime = {
seconds = 1741881600
}
},
[840268] = {
id = 840268,
lowVer = "1.3.78.33",
name = "珍馐百味",
desc = "珍馐百味祈愿中获得",
icon = "D_HeadFrame_244",
beginTime = {
seconds = 1742486400
}
},
[840269] = {
id = 840269,
quality = 3,
name = "摆摊有礼",
desc = "卡牌集换活动中获得",
icon = "CDN:T_HeadFrame_253",
isDynamic = 0,
beginTime = {
seconds = 1741190400
}
},
[840270] = {
id = 840270,
lowVer = "1.3.78.29",
name = "冰雪聪明",
desc = "峡谷英豪祈愿活动获得",
icon = "D_HeadFrame_254",
beginTime = {
seconds = 1743436800
}
},
[840271] = {
id = 840271,
lowVer = "1.3.78.12",
name = "疗愈天使",
desc = "玩偶之家祈愿获得",
icon = "D_HeadFrame_252",
beginTime = {
seconds = 1742140800
}
},
[840272] = {
id = 840272,
lowVer = "1.3.78.12",
name = "奶茶星人",
desc = v1,
icon = "D_HeadFrame_256",
beginTime = {
seconds = 1743523200
}
},
[840273] = {
id = 840273,
lowVer = "1.3.78.12",
name = "暗中观察酱",
desc = v1,
icon = "D_HeadFrame_255",
beginTime = {
seconds = 1743523200
}
},
[840274] = {
id = 840274,
quality = 3,
name = "烟柳纷纷",
desc = v1,
icon = "CDN:T_HeadFrame_257",
beginTime = {
seconds = 1743436800
}
},
[840275] = {
id = 840275,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.78.1",
name = "舞台之星",
desc = v1,
icon = "D_HeadFrame_250",
beginTime = {
seconds = 1743609600
}
},
[840276] = {
id = 840276,
lowVer = "1.3.88.1",
name = "炫彩星冠",
desc = "参加锦标赛《冠军之星》可以获得",
icon = "CDN:T_HeadFrame_258",
isDynamic = 0,
beginTime = {
seconds = 1742918400
}
},
[840277] = {
id = 840277,
lowVer = "1.3.88.1",
name = "冠军皇冠",
desc = "参加锦标赛《冠军之星》可以获得",
icon = "CDN:T_HeadFrame_259",
isDynamic = 0,
beginTime = {
seconds = 1742918400
}
},
[840278] = {
id = 840278,
lowVer = "1.3.78.61",
name = "卓大王",
icon = "CDN:T_HeadFrame_260",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 4078137600
}
},
[840280] = {
id = 840280,
lowVer = "1.3.78.73",
name = "菠萝绒绒派对",
desc = "泡泡玛特祈愿获得",
icon = "D_HeadFrame_262",
beginTime = {
seconds = 1743696000
}
},
[840281] = {
id = 840281,
lowVer = "1.3.78.73",
name = "泡泡玛特",
desc = "泡泡玛特祈愿获得",
icon = "D_HeadFrame_170",
beginTime = {
seconds = 1743696000
}
},
[840282] = {
id = 840282,
lowVer = "1.3.88.1",
name = "满杯蜜桃猫",
desc = "满杯蜜桃猫祈愿获得",
icon = "D_HeadFrame_278",
beginTime = {
seconds = 1746720000
}
},
[840283] = {
id = 840283,
lowVer = "1.3.78.33",
name = "甜梦嘉年华",
desc = "甜梦嘉年华祈愿中获得",
icon = "D_HeadFrame_268",
beginTime = {
seconds = 1744128000
}
},
[840284] = {
id = 840284,
name = "小熊猫",
desc = "活动获得",
icon = "CDN:T_HeadFrame_264",
isDynamic = 0
},
[840285] = {
id = 840285,
lowVer = "1.3.88.1",
quality = 4,
name = "极酷新星",
desc = "超能学园通行证获得",
icon = "CDN:T_HeadFrame_277",
isDynamic = 0,
beginTime = {
seconds = 1746115200
}
},
[840286] = {
id = 840286,
lowVer = "1.3.88.1",
quality = 3,
name = "礼法有度",
desc = "超能学园通行证获得",
icon = "CDN:T_HeadFrame_276",
isDynamic = 0,
beginTime = {
seconds = 1746115200
}
},
[840287] = {
id = 840287,
lowVer = "1.3.88.1",
name = "闪耀魔法",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_275",
beginTime = {
seconds = 1746115200
}
},
[840288] = {
id = 840288,
lowVer = "1.3.88.1",
name = "超能学园",
desc = "超能学园赛季限时活动获得",
icon = "D_HeadFrame_274",
beginTime = {
seconds = 1746115200
}
},
[840289] = {
id = 840289,
lowVer = "1.3.78.1",
name = "真相之夜",
desc = "真相之夜赛季限时活动获得",
icon = "D_HeadFrame_251",
beginTime = {
seconds = 1741881600
}
},
[840290] = {
id = 840290,
lowVer = "1.3.78.1",
name = "心动信号",
desc = v1,
icon = "D_HeadFrame_267",
beginTime = {
seconds = 1745856000
}
},
[840291] = {
id = 840291,
quality = 3,
name = "超能小宝头像框",
desc = "王冠商铺兑换中获得",
icon = "CDN:T_HeadFrame_269",
isDynamic = 0
},
[840300] = {
id = 840300,
quality = 3,
name = "魔力魔力",
desc = "赛季冲段任务获得",
icon = "T_HeadFrame_270",
isDynamic = 0
},
[840301] = {
id = 840301,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.88.1",
name = "赛博节奏",
desc = v1,
icon = "D_HeadFrame_271",
beginTime = {
seconds = 1744905600
}
},
[840302] = {
id = 840302,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.3.88.1",
name = "春绿奶萌",
desc = v1,
icon = "D_HeadFrame_272",
beginTime = {
seconds = 1747324800
}
},
[840303] = {
id = 840303,
name = "喵喵节",
desc = v1,
icon = "CDN:T_HeadFrame_273",
isDynamic = 0
},
[840304] = {
id = 840304,
quality = 3,
name = "夏季游乐园",
desc = "通过参与官方社区社群活动获得",
icon = "CDN:T_HeadFrame_112",
isDynamic = 0
},
[840305] = {
id = 840305,
lowVer = "1.3.88.1",
name = "幻彩调律",
desc = "幻彩调律祈愿获得",
icon = "D_HeadFrame_279",
beginTime = {
seconds = 1747324800
}
}
}

local mt = {
type = "ItemType_Frame",
maxNum = 1,
quality = 2,
showInView = 1,
isDynamic = 1
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data