--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_arena.xlsx: 玩法

local data = {
[5500] = {
id = 5500,
modeID = 3,
desc = "峡谷2v2",
maxTeamMember = 2,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_3V3",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_3V3",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "5500",
modeGroup = "5500",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2
},
teamTag = "2",
battleRecordCnt = 30,
matchRuleId = 5500,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 5600001,
mmrType = "MST_Arena",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena",
descShort = "双星组队并肩竞技，决战峡谷之巅！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
buttonDesc = "2V2",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 702,
layoutID = 20,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 55004,
warmRoundMatchRuleId = 3,
gameModeType = 1,
detailLinkId = 232,
detailLinkDesc = "备战",
detailLinkRedDot = 802,
detailBattleRecordPakId = 50021,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
detailLinkCardContent = "新增卡牌构筑",
isArena = true,
battleDetailStyle = "System.Arena.FinalAccount.UI_Arena_FinalAccount_Detail"
},
[5600] = {
id = 5600,
modeID = 3,
desc = "峡谷3v3",
maxTeamMember = 3,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_3V3",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_3V3",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "5500",
modeGroup = "5601|5600",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3
},
teamTag = "3",
battleRecordCnt = 30,
matchRuleId = 5600,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 5600001,
mmrType = "MST_Arena",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena",
isShowBattleRecord = true,
descShort = "三人组队并肩竞技，决战峡谷之巅！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult3v3",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
recordType = 5600,
buttonDesc = "休闲",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 702,
layoutID = 27,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 56004,
warmRoundMatchRuleId = 3,
gameModeType = 1,
detailLinkId = 232,
detailLinkDesc = "备战",
detailLinkRedDot = 801,
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
aiLabThreeTeamHumanWarmRoundRoomInfoId = 560001,
aiLabTwoTeamHumanWarmRoundRoomInfoId = 560002,
aiLabOneTeamHumanWarmRoundRoomInfoId = 560003,
aiLabOneHumanWarmRoundRoomInfoId = 560004,
isEnableAiLabArenaWarmRound = true,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
detailLinkCardContent = "新增卡牌构筑",
isArena = true,
battleDetailStyle = "Feature.Arena.Script.System.FinalAccountDetails.UI_Arena_FinalAccount_Detail3V3"
},
[5601] = {
id = 5601,
modeID = 3,
desc = "峡谷3v3",
maxTeamMember = 3,
conditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600
}
}
}
}
}
},
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_3V3",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_3V3",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "5500",
modeGroup = "5601|5600",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3
},
teamTag = "3",
unlockRule = "完成1次峡谷3v3[休闲]",
battleRecordCnt = 30,
matchRuleId = 5601,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 5600001,
mmrType = "MST_ArenaDegree",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_ArenaQualifying",
isShowBattleRecord = true,
descShort = "三人组队并肩竞技，决战峡谷之巅！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult3v3",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
recordType = 5600,
buttonDesc = "排位",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 702,
layoutID = 27,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 105601,
warmRoundMatchRuleId = 5601,
gameModeType = 2,
detailLinkId = 232,
detailLinkDesc = "备战",
detailLinkRedDot = 803,
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
aiLabThreeTeamHumanWarmRoundRoomInfoId = 560101,
aiLabTwoTeamHumanWarmRoundRoomInfoId = 560102,
aiLabOneTeamHumanWarmRoundRoomInfoId = 560103,
aiLabOneHumanWarmRoundRoomInfoId = 560104,
isEnableAiLabArenaWarmRound = true,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
detailLinkCardContent = "新增卡牌构筑",
isArena = true,
battleDetailStyle = "Feature.Arena.Script.System.FinalAccountDetails.UI_Arena_FinalAccount_Detail3V3"
},
[5700] = {
id = 5700,
modeID = 3,
desc = "峡谷占地盘",
maxTeamMember = 3,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_HotZone",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_HotZone",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "5700",
modeGroup = "5700",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3
},
teamTag = "3",
unlockRule = "请前往移动端游玩",
battleRecordCnt = 30,
matchRuleId = 5700,
TeamMatchGame = true,
battlePlayerNum = 6,
dropId = 5700001,
mmrType = "MST_Arena_Hz",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena_Hz",
isShowBattleRecord = true,
descShort = "6名星宝将组成2支3人小队进行竞技对抗!",
battleRecordStyle = "UI_PlayerInfo_ModRecord_HotZoneBattleResult",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
recordType = 5700,
buttonDesc = "休闲",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 730,
layoutID = 31,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 57002,
warmRoundMatchRuleId = 3,
gameModeType = 1,
detailLinkId = 232,
detailLinkDesc = "备战",
detailLinkRedDot = 801,
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
detailLinkCardContent = "新增卡牌构筑",
loginPlatList = {
7
},
isBan = true,
cloudSysList = {
2,
3
},
cloudIsBan = true,
isArena = true,
battleDetailStyle = "Feature.Arena.Script.System.FinalAccount.UI_HotZone_FinalAccount_Detail"
},
[5550] = {
id = 5550,
modeID = 999,
desc = "峡谷练功房",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_3V3",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_3V3",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "5500",
modeGroup = "5550",
settleProc = "MTSC_Common",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "请前往移动端游玩",
battleRecordCnt = 30,
matchRuleId = 5550,
TeamMatchGame = true,
battlePlayerNum = 1,
dropId = 5600001,
mmrType = "MST_Arena",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena",
descShort = "三人组队并肩竞技，决战峡谷之巅！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult3v3",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
buttonDesc = "训练",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 850,
layoutID = 27,
isShowEmotionEntrance = 1,
gameModeType = 1,
detailLinkId = 232,
detailLinkDesc = "备战",
detailLinkRedDot = 801,
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
detailLinkCardContent = "新增卡牌构筑",
loginPlatList = {
7
},
isBan = true,
cloudSysList = {
2,
3
},
cloudIsBan = true,
isArena = true
},
[5551] = {
id = 5551,
modeID = 999,
desc = "峡谷练功房",
maxTeamMember = 1,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_3V3",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_3V3",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "5551",
modeGroup = "5551",
settleProc = "MTSC_Common",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "请前往移动端游玩",
battleRecordCnt = 30,
matchRuleId = 5551,
TeamMatchGame = true,
battlePlayerNum = 1,
dropId = 5600001,
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena",
descShort = "三人组队并肩竞技，决战峡谷之巅！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult3v3",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
buttonDesc = "训练",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 851,
layoutID = 24,
isShowEmotionEntrance = 1,
gameModeType = 1,
detailLinkId = 232,
detailLinkDesc = "备战",
detailLinkRedDot = 801,
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
detailLinkCardContent = "新增卡牌构筑",
loginPlatList = {
7
},
isBan = true,
cloudSysList = {
2,
3
},
cloudIsBan = true,
isArena = true
}
}

local mt = {
isPermanent = false,
TeamMatchGame = false,
isShowBattleRecord = false,
UseDefaultChampionDisplayScene = false,
isArena = false,
isEnableAiLabArenaWarmRound = false,
isBan = false,
cloudIsBan = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data