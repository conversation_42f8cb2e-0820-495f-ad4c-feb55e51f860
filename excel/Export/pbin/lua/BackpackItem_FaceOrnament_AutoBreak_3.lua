--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 面饰

local v0 = {
{
itemId = 6,
itemNum = 320
}
}

local v1 = 1

local v2 = {
0,
0
}

local v3 = {
-10,
35
}

local v4 = {
seconds = 4074768000
}

local data = {
[610302] = {
id = 610302,
effect = true,
name = "琳琅点翠",
desc = "古韵今风，讲述岁月里的故事",
icon = "CDN:Icon_Mask_167_02",
outlookConf = {
belongTo = 610300,
fashionValue = 25,
belongToGroup = {
610301,
610302
}
},
resourceConf = {
model = "SM_Mask_167",
material = "MI_Mask_167_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51323,
shareTexts = {
"花丝为胎，翠羽为魂"
},
shareOffset = v2
},
[610303] = {
id = 610303,
effect = true,
name = "多彩纽扣镜",
desc = "用扭扣遮住眼睛，我也能变成玩偶",
icon = "CDN:Icon_Mask_169",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_169",
modelType = 1
},
shareTexts = {
"不能用力扭，会掉下来哦~"
},
beginTime = v4,
suitId = 50158,
suitName = "多彩纽扣镜",
suitIcon = "CDN:Icon_Mask_169",
shareOffset = v2
},
[610304] = {
id = 610304,
effect = true,
name = "多彩纽扣镜",
desc = "用扭扣遮住眼睛，我也能变成玩偶",
icon = "CDN:Icon_Mask_169_01",
outlookConf = {
belongTo = 610303,
fashionValue = 25,
belongToGroup = {
610304,
610305
}
},
resourceConf = {
model = "SM_Mask_169",
material = "MI_Mask_169_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51325,
shareTexts = {
"不能用力扭，会掉下来哦~"
},
shareOffset = v2
},
[610305] = {
id = 610305,
effect = true,
name = "多彩纽扣镜",
desc = "用扭扣遮住眼睛，我也能变成玩偶",
icon = "CDN:Icon_Mask_169_02",
outlookConf = {
belongTo = 610303,
fashionValue = 25,
belongToGroup = {
610304,
610305
}
},
resourceConf = {
model = "SM_Mask_169",
material = "MI_Mask_169_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51326,
shareTexts = {
"不能用力扭，会掉下来哦~"
},
shareOffset = v2
},
[610306] = {
id = 610306,
effect = true,
name = "风车视界",
desc = "好奇怪，我的世界突然天旋地转",
icon = "CDN:Icon_Mask_168",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_168",
modelType = 1
},
shareTexts = {
"盯着我转，你会被催眠哦~"
},
beginTime = v4,
suitId = 50159,
suitName = "风车视界",
suitIcon = "CDN:Icon_Mask_168",
shareOffset = v2
},
[610307] = {
id = 610307,
effect = true,
name = "风车视界",
desc = "好奇怪，我的世界突然天旋地转",
icon = "CDN:Icon_Mask_168_01",
outlookConf = {
belongTo = 610306,
fashionValue = 25,
belongToGroup = {
610307,
610308
}
},
resourceConf = {
model = "SM_Mask_168",
material = "MI_Mask_168_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51328,
shareTexts = {
"盯着我转，你会被催眠哦~"
},
shareOffset = v2
},
[610308] = {
id = 610308,
effect = true,
name = "风车视界",
desc = "好奇怪，我的世界突然天旋地转",
icon = "CDN:Icon_Mask_168_02",
outlookConf = {
belongTo = 610306,
fashionValue = 25,
belongToGroup = {
610307,
610308
}
},
resourceConf = {
model = "SM_Mask_168",
material = "MI_Mask_168_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51329,
shareTexts = {
"盯着我转，你会被催眠哦~"
},
shareOffset = v2
},
[610309] = {
id = 610309,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "鸿运祥云",
desc = "祥云驱散阴霾，带来光明和希望",
icon = "CDN:Icon_Mask_166",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Mask_166",
emitter = "FX_CH_Decorate_Mask_166_001",
modelType = 1
},
shareTexts = {
"祥云环绕，福泽绵延"
},
beginTime = v4,
suitId = 50160,
suitName = "鸿运祥云",
suitIcon = "CDN:Icon_Mask_166",
shareOffset = v2
},
[610310] = {
id = 610310,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "鸿运祥云",
desc = "祥云驱散阴霾，带来光明和希望",
icon = "CDN:Icon_Mask_166_01",
outlookConf = {
belongTo = 610309,
fashionValue = 35,
belongToGroup = {
610310,
610311
}
},
resourceConf = {
model = "SM_Mask_166",
material = "MI_Mask_166_HP01",
emitter = "FX_CH_Decorate_Mask_166_001_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51331,
shareTexts = {
"祥云环绕，福泽绵延"
},
shareOffset = v2
},
[610311] = {
id = 610311,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "鸿运祥云",
desc = "祥云驱散阴霾，带来光明和希望",
icon = "CDN:Icon_Mask_166_02",
outlookConf = {
belongTo = 610309,
fashionValue = 35,
belongToGroup = {
610310,
610311
}
},
resourceConf = {
model = "SM_Mask_166",
material = "MI_Mask_166_HP02",
emitter = "FX_CH_Decorate_Mask_166_001_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51332,
shareTexts = {
"祥云环绕，福泽绵延"
},
shareOffset = v2
},
[610312] = {
id = 610312,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "热狗梦境",
desc = "香香咸咸，梦里有最爱的热狗",
icon = "CDN:Icon_Mask_151",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_151",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50161,
suitName = "热狗梦境",
suitIcon = "CDN:Icon_Mask_151",
shareOffset = v2
},
[610313] = {
id = 610313,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "章鱼护目镜",
desc = "戴上它，再也不用担心墨汁困扰",
icon = "CDN:Icon_Mask_150",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_150",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50162,
suitName = "章鱼护目镜",
suitIcon = "CDN:Icon_Mask_150",
shareOffset = v2
},
[610314] = {
id = 610314,
effect = true,
name = "大耳狗眼罩",
desc = "像是云朵落在了睫毛上",
icon = "CDN:Icon_Mask_162",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_162",
modelType = 1,
IconLabelId = 102
},
scaleTimes = 150,
shareTexts = {
"现在，安心睡个好觉吧"
},
beginTime = v4,
suitId = 50163,
suitName = "大耳狗眼罩",
suitIcon = "CDN:Icon_Mask_162",
shareOffset = {
-11,
25
}
},
[610315] = {
id = 610315,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "心动时刻",
desc = "遇见你，世界忽然变得粉红~",
icon = "CDN:Icon_Mask_157",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_157",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50164,
suitName = "心动时刻",
suitIcon = "CDN:Icon_Mask_157",
shareOffset = v2
},
[610316] = {
id = 610316,
effect = true,
name = "醒狮舞面",
desc = "醒狮一舞，万物呈祥",
icon = "CDN:Icon_Mask_165",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_165",
modelType = 1
},
shareTexts = {
"看到我，就像看到了新年"
},
beginTime = v4,
suitId = 50165,
suitName = "醒狮舞面",
suitIcon = "CDN:Icon_Mask_165",
shareOffset = v2
},
[610317] = {
id = 610317,
effect = true,
name = "醒狮舞面",
desc = "醒狮一舞，万物呈祥",
icon = "CDN:Icon_Mask_165_01",
outlookConf = {
belongTo = 610316,
fashionValue = 25,
belongToGroup = {
610317,
610318
}
},
resourceConf = {
model = "SM_Mask_165",
material = "MI_Mask_165_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51338,
shareTexts = {
"看到我，就像看到了新年"
},
shareOffset = v2
},
[610318] = {
id = 610318,
effect = true,
name = "醒狮舞面",
desc = "醒狮一舞，万物呈祥",
icon = "CDN:Icon_Mask_165_02",
outlookConf = {
belongTo = 610316,
fashionValue = 25,
belongToGroup = {
610317,
610318
}
},
resourceConf = {
model = "SM_Mask_165",
material = "MI_Mask_165_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51339,
shareTexts = {
"看到我，就像看到了新年"
},
shareOffset = v2
},
[610319] = {
id = 610319,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "狐影倾城",
desc = "让神秘的传说，化为眉眼间的灵动",
icon = "CDN:Icon_Mask_170",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_170",
modelType = 2,
idleAnim = "AS_Mask_170_idle_001"
},
shareTexts = {
"沐于月下，优雅而梦幻"
},
beginTime = v4,
suitId = 50166,
suitName = "狐影倾城",
suitIcon = "CDN:Icon_Mask_170",
shareOffset = v2
},
[610320] = {
id = 610320,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "狐影倾城",
desc = "让神秘的传说，化为眉眼间的灵动",
icon = "CDN:Icon_Mask_170_01",
outlookConf = {
belongTo = 610319,
fashionValue = 35,
belongToGroup = {
610320,
610321
}
},
resourceConf = {
model = "SK_Mask_170",
material = "MI_Mask_170_HP01",
modelType = 2,
idleAnim = "AS_Mask_170_idle_001_HP01",
materialSlot = "Mask"
},
commodityId = 51341,
shareTexts = {
"沐于月下，优雅而梦幻"
},
shareOffset = v2
},
[610321] = {
id = 610321,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "狐影倾城",
desc = "让神秘的传说，化为眉眼间的灵动",
icon = "CDN:Icon_Mask_170_02",
outlookConf = {
belongTo = 610319,
fashionValue = 35,
belongToGroup = {
610320,
610321
}
},
resourceConf = {
model = "SK_Mask_170",
material = "MI_Mask_170_HP02",
modelType = 2,
idleAnim = "AS_Mask_170_idle_001_HP02",
materialSlot = "Mask"
},
commodityId = 51342,
shareTexts = {
"沐于月下，优雅而梦幻"
},
shareOffset = v2
},
[610322] = {
id = 610322,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 20
}
},
quality = 1,
name = "梦幻泡影",
desc = "泡沫的痕迹，记下一切美好的回忆",
icon = "CDN:Icon_Mask_174",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_174",
modelType = 2,
idleAnim = "AS_Mask_174_idle_001"
},
shareTexts = {
"梦幻泡影，奇幻之境"
},
beginTime = v4,
suitId = 50167,
suitName = "梦幻泡影",
suitIcon = "CDN:Icon_Mask_174",
shareOffset = v2
},
[610323] = {
id = 610323,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "梦幻泡影",
desc = "泡沫的痕迹，记下一切美好的回忆",
icon = "CDN:Icon_Mask_174_01",
outlookConf = {
belongTo = 610322,
fashionValue = 35,
belongToGroup = {
610323,
610324
}
},
resourceConf = {
model = "SK_Mask_174",
material = "MI_Mask_174_HP01",
modelType = 2,
idleAnim = "AS_Mask_174_idle_001_HP01",
materialSlot = "Mask"
},
commodityId = 51344,
shareTexts = {
"梦幻泡影，奇幻之境"
},
shareOffset = v2
},
[610324] = {
id = 610324,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "梦幻泡影",
desc = "泡沫的痕迹，记下一切美好的回忆",
icon = "CDN:Icon_Mask_174_02",
outlookConf = {
belongTo = 610322,
fashionValue = 35,
belongToGroup = {
610323,
610324
}
},
resourceConf = {
model = "SK_Mask_174",
material = "MI_Mask_174_HP02",
modelType = 2,
idleAnim = "AS_Mask_174_idle_001_HP02",
materialSlot = "Mask"
},
commodityId = 51345,
shareTexts = {
"梦幻泡影，奇幻之境"
},
shareOffset = v2
},
[610325] = {
id = 610325,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "编织睛彩",
desc = "五彩的针线，编织出独一无二的视线",
icon = "CDN:Icon_Mask_178",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_178",
modelType = 2,
idleAnim = "AS_Mask_178_idle_001"
},
shareTexts = {
"编织属于我的“视”界"
},
beginTime = v4,
suitId = 50168,
suitName = "编织睛彩",
suitIcon = "CDN:Icon_Mask_178",
shareOffset = v2
},
[610326] = {
id = 610326,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "编织睛彩",
desc = "五彩的针线，编织出独一无二的视线",
icon = "CDN:Icon_Mask_178_01",
outlookConf = {
belongTo = 610325,
fashionValue = 35,
belongToGroup = {
610326,
610327
}
},
resourceConf = {
model = "SK_Mask_178",
material = "MI_Mask_178_HP01",
modelType = 2,
idleAnim = "AS_Mask_178_idle_001_HP01",
materialSlot = "Mask"
},
commodityId = 51347,
shareTexts = {
"编织属于我的“视”界"
},
shareOffset = v2
},
[610327] = {
id = 610327,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "编织睛彩",
desc = "五彩的针线，编织出独一无二的视线",
icon = "CDN:Icon_Mask_178_02",
outlookConf = {
belongTo = 610325,
fashionValue = 35,
belongToGroup = {
610326,
610327
}
},
resourceConf = {
model = "SK_Mask_178",
material = "MI_Mask_178_HP02",
modelType = 2,
idleAnim = "AS_Mask_178_idle_001_HP02",
materialSlot = "Mask"
},
commodityId = 51348,
shareTexts = {
"编织属于我的“视”界"
},
shareOffset = v2
},
[610328] = {
id = 610328,
effect = true,
name = "爱心十字绷",
desc = "害怕说话嘴受凉？送你爱心创口贴",
icon = "CDN:Icon_Mask_164",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_164",
modelType = 1
},
shareTexts = {
"让我们享受此刻的宁静"
},
beginTime = v4,
suitId = 50169,
suitName = "爱心十字绷",
suitIcon = "CDN:Icon_Mask_164",
shareOffset = v2
},
[610329] = {
id = 610329,
effect = true,
name = "爱心十字绷",
desc = "害怕说话嘴受凉？送你爱心创口贴",
icon = "CDN:Icon_Mask_164_01",
outlookConf = {
belongTo = 610328,
fashionValue = 25,
belongToGroup = {
610329,
610330
}
},
resourceConf = {
model = "SM_Mask_164",
material = "MI_Mask_164_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51350,
shareTexts = {
"让我们享受此刻的宁静"
},
shareOffset = v2
},
[610330] = {
id = 610330,
effect = true,
name = "爱心十字绷",
desc = "害怕说话嘴受凉？送你爱心创口贴",
icon = "CDN:Icon_Mask_164_02",
outlookConf = {
belongTo = 610328,
fashionValue = 25,
belongToGroup = {
610329,
610330
}
},
resourceConf = {
model = "SM_Mask_164",
material = "MI_Mask_164_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51351,
shareTexts = {
"让我们享受此刻的宁静"
},
shareOffset = v2
},
[610331] = {
id = 610331,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "暗影黑喵",
desc = "黑夜给了我黑喵的眼睛~",
icon = "CDN:Icon_Mask_171",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_171",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50170,
suitName = "暗影黑喵",
suitIcon = "CDN:Icon_Mask_171",
shareOffset = v2
},
[610332] = {
id = 610332,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "星辰之纱",
desc = "未来化作星光，遮住无限遐想",
icon = "CDN:Icon_Mask_179",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_179",
modelType = 2,
idleAnim = "AS_Mask_179_idle_001"
},
shareTexts = {
"揭开面纱，未来便在你眼前"
},
beginTime = v4,
suitId = 50171,
suitName = "星辰之纱",
suitIcon = "CDN:Icon_Mask_179",
shareOffset = v3
},
[610333] = {
id = 610333,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "星辰之纱",
desc = "未来化作星光，遮住无限遐想",
icon = "CDN:Icon_Mask_179_01",
outlookConf = {
belongTo = 610332,
fashionValue = 35,
belongToGroup = {
610333,
610334
}
},
resourceConf = {
model = "SK_Mask_179",
material = "MI_Mask_179_1_HP01;MI_Mask_179_2_HP01;MI_Mask_179_3_HP01",
modelType = 2,
idleAnim = "AS_Mask_179_idle_001_HP01",
materialSlot = "Mask_1;Mask_2;Mask_3"
},
commodityId = 51354,
shareTexts = {
"揭开面纱，未来便在你眼前"
},
shareOffset = v3
},
[610334] = {
id = 610334,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "星辰之纱",
desc = "未来化作星光，遮住无限遐想",
icon = "CDN:Icon_Mask_179_02",
outlookConf = {
belongTo = 610332,
fashionValue = 35,
belongToGroup = {
610333,
610334
}
},
resourceConf = {
model = "SK_Mask_179",
material = "MI_Mask_179_1_HP02;MI_Mask_179_2_HP02;MI_Mask_179_3_HP02",
modelType = 2,
idleAnim = "AS_Mask_179_idle_001_HP02",
materialSlot = "Mask_1;Mask_2;Mask_3"
},
commodityId = 51355,
shareTexts = {
"揭开面纱，未来便在你眼前"
},
shareOffset = v3
},
[610335] = {
id = 610335,
effect = true,
name = "小羊咩咩",
desc = "治愈每一场梦境",
icon = "CDN:Icon_Mask_185",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_185",
modelType = 1
},
shareTexts = {
"和我一起，编织甜美梦境~"
},
beginTime = v4,
suitId = 50172,
suitName = "小羊咩咩",
suitIcon = "CDN:Icon_Mask_185",
shareOffset = {
-10,
20
}
},
[610336] = {
id = 610336,
effect = true,
name = "小羊咩咩",
desc = "治愈每一场梦境",
icon = "CDN:Icon_Mask_185_01",
outlookConf = {
belongTo = 610335,
fashionValue = 25,
belongToGroup = {
610336,
610337
}
},
resourceConf = {
model = "SM_Mask_185",
material = "MI_Mask_185_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51357,
shareTexts = {
"和我一起，编织甜美梦境~"
},
shareOffset = {
-10,
20
}
},
[610337] = {
id = 610337,
effect = true,
name = "小羊咩咩",
desc = "治愈每一场梦境",
icon = "CDN:Icon_Mask_185_02",
outlookConf = {
belongTo = 610335,
fashionValue = 25,
belongToGroup = {
610336,
610337
}
},
resourceConf = {
model = "SM_Mask_185",
material = "MI_Mask_185_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51358,
shareTexts = {
"和我一起，编织甜美梦境~"
},
shareOffset = {
-10,
20
}
},
[610338] = {
id = 610338,
effect = true,
name = "寻宝嗅嗅鼻",
desc = "嗯~宝物的气息！",
icon = "CDN:Icon_Mask_186",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_186",
modelType = 1
},
shareTexts = {
"等找到宝物，一起发财！"
},
beginTime = v4,
suitId = 50173,
suitName = "寻宝嗅嗅鼻",
suitIcon = "CDN:Icon_Mask_186",
shareOffset = v3
},
[610339] = {
id = 610339,
effect = true,
name = "寻宝嗅嗅鼻",
desc = "嗯~宝物的气息！",
icon = "CDN:Icon_Mask_186_01",
outlookConf = {
belongTo = 610338,
fashionValue = 25,
belongToGroup = {
610339,
610340
}
},
resourceConf = {
model = "SM_Mask_186",
material = "MI_Mask_186_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51360,
shareTexts = {
"等找到宝物，一起发财！"
},
shareOffset = v3
},
[610340] = {
id = 610340,
effect = true,
name = "寻宝嗅嗅鼻",
desc = "嗯~宝物的气息！",
icon = "CDN:Icon_Mask_186_02",
outlookConf = {
belongTo = 610338,
fashionValue = 25,
belongToGroup = {
610339,
610340
}
},
resourceConf = {
model = "SM_Mask_186",
material = "MI_Mask_186_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51361,
shareTexts = {
"等找到宝物，一起发财！"
},
shareOffset = v3
},
[610341] = {
id = 610341,
effect = true,
name = "奶猫小爪",
desc = "猫咪不语，只是一味踩脸",
icon = "CDN:Icon_Mask_187",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_187",
modelType = 1
},
shareTexts = {
"送你一个粉红色的小爪爪~"
},
beginTime = v4,
suitId = 50174,
suitName = "奶猫小爪",
suitIcon = "CDN:Icon_Mask_187",
shareOffset = v3
},
[610342] = {
id = 610342,
effect = true,
name = "奶猫小爪",
desc = "猫咪不语，只是一味踩脸",
icon = "CDN:Icon_Mask_187_01",
outlookConf = {
belongTo = 610341,
fashionValue = 25,
belongToGroup = {
610342,
610343
}
},
resourceConf = {
model = "SM_Mask_187",
material = "MI_Mask_187_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51363,
shareTexts = {
"送你一个粉红色的小爪爪~"
},
shareOffset = v3
},
[610343] = {
id = 610343,
effect = true,
name = "奶猫小爪",
desc = "猫咪不语，只是一味踩脸",
icon = "CDN:Icon_Mask_187_02",
outlookConf = {
belongTo = 610341,
fashionValue = 25,
belongToGroup = {
610342,
610343
}
},
resourceConf = {
model = "SM_Mask_187",
material = "MI_Mask_187_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51364,
shareTexts = {
"送你一个粉红色的小爪爪~"
},
shareOffset = v3
},
[610344] = {
id = 610344,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "红尘铜障",
desc = "真假虚妄，到底谁能分清？",
icon = "CDN:Icon_Mask_184",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Mask_184",
emitter = "FX_CH_Decorate_Mask_184",
modelType = 1
},
shareTexts = {
"助我看清这个世界，拜托你"
},
beginTime = v4,
suitId = 50175,
suitName = "红尘铜障",
suitIcon = "CDN:Icon_Mask_184",
shareOffset = {
-10,
30
}
},
[610345] = {
id = 610345,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "红尘铜障",
desc = "真假虚妄，到底谁能分清？",
icon = "CDN:Icon_Mask_184_01",
outlookConf = {
belongTo = 610344,
fashionValue = 35,
belongToGroup = {
610345,
610346
}
},
resourceConf = {
model = "SM_Mask_184",
material = "MI_Mask_184_HP01",
emitter = "FX_CH_Decorate_Mask_184_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51366,
shareTexts = {
"助我看清这个世界，拜托你"
},
shareOffset = {
-10,
30
}
},
[610346] = {
id = 610346,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "红尘铜障",
desc = "真假虚妄，到底谁能分清？",
icon = "CDN:Icon_Mask_184_02",
outlookConf = {
belongTo = 610344,
fashionValue = 35,
belongToGroup = {
610345,
610346
}
},
resourceConf = {
model = "SM_Mask_184",
material = "MI_Mask_184_HP02",
emitter = "FX_CH_Decorate_Mask_184_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51367,
shareTexts = {
"助我看清这个世界，拜托你"
},
shareOffset = {
-10,
30
}
},
[610347] = {
id = 610347,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "向左看齐",
desc = "一路向左，环游整个世界",
icon = "CDN:Icon_Mask_175",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_175",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50176,
suitName = "向左看齐",
suitIcon = "CDN:Icon_Mask_175",
shareOffset = v2
},
[610348] = {
id = 610348,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "向右看齐",
desc = "向右前行，实现梦想不会太远",
icon = "CDN:Icon_Mask_176",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_176",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50177,
suitName = "向右看齐",
suitIcon = "CDN:Icon_Mask_176",
shareOffset = v2
},
[610349] = {
id = 610349,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "怪兽眼罩",
desc = "邪恶小怪兽，准备捣乱啦！",
icon = "CDN:Icon_Mask_172",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_172",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50178,
suitName = "怪兽眼罩",
suitIcon = "CDN:Icon_Mask_172",
shareOffset = v2
},
[610350] = {
id = 610350,
effect = true,
name = "蝶梦之面",
desc = "庄周梦蝶，蝶落玉人面",
icon = "CDN:Icon_Mask_173",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_173",
modelType = 1
},
shareTexts = {
"一同踏入这大梦一场"
},
beginTime = v4,
suitId = 50179,
suitName = "蝶梦之面",
suitIcon = "CDN:Icon_Mask_173",
shareOffset = v3
},
[610351] = {
id = 610351,
effect = true,
name = "蝶梦之面",
desc = "庄周梦蝶，蝶落玉人面",
icon = "CDN:Icon_Mask_173_01",
outlookConf = {
belongTo = 610350,
fashionValue = 25,
belongToGroup = {
610351,
610352
}
},
resourceConf = {
model = "SM_Mask_173",
material = "MI_Mask_173_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51372,
shareTexts = {
"一同踏入这大梦一场"
},
shareOffset = v3
},
[610352] = {
id = 610352,
effect = true,
name = "蝶梦之面",
desc = "庄周梦蝶，蝶落玉人面",
icon = "CDN:Icon_Mask_173_02",
outlookConf = {
belongTo = 610350,
fashionValue = 25,
belongToGroup = {
610351,
610352
}
},
resourceConf = {
model = "SM_Mask_173",
material = "MI_Mask_173_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51373,
shareTexts = {
"一同踏入这大梦一场"
},
shareOffset = v3
},
[610353] = {
id = 610353,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "晶蝶幻面",
desc = "假面之下，真心是否瞬息万变？",
icon = "CDN:Icon_Mask_188",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_188",
modelType = 2,
idleAnim = "AS_Mask_188_idle_001"
},
shareTexts = {
"蝴蝶扇动翅膀，真心永恒不变"
},
beginTime = {
seconds = 1729180800
},
suitId = 50180,
suitName = "晶蝶幻面",
suitIcon = "CDN:Icon_Mask_188",
shareOffset = v3,
shareScaleTimes = 150
},
[610354] = {
id = 610354,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "晶蝶幻面",
desc = "假面之下，真心是否瞬息万变？",
icon = "CDN:Icon_Mask_188_01",
outlookConf = {
belongTo = 610353,
fashionValue = 35,
belongToGroup = {
610354,
610355
}
},
resourceConf = {
model = "SK_Mask_188",
material = "MI_Mask_188_1_HP01;MI_Mask_188_2_HP01",
modelType = 2,
idleAnim = "AS_Mask_188_idle_001_HP01",
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51375,
shareTexts = {
"蝴蝶扇动翅膀，真心永恒不变"
},
shareOffset = v3,
shareScaleTimes = 150
},
[610355] = {
id = 610355,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "晶蝶幻面",
desc = "假面之下，真心是否瞬息万变？",
icon = "CDN:Icon_Mask_188_02",
outlookConf = {
belongTo = 610353,
fashionValue = 35,
belongToGroup = {
610354,
610355
}
},
resourceConf = {
model = "SK_Mask_188",
material = "MI_Mask_188_1_HP02;MI_Mask_188_2_HP02",
modelType = 2,
idleAnim = "AS_Mask_188_idle_001_HP02",
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51376,
shareTexts = {
"蝴蝶扇动翅膀，真心永恒不变"
},
shareOffset = v3,
shareScaleTimes = 150
},
[610356] = {
id = 610356,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "弥生花语",
desc = "花瓣里，闪耀着宇宙的永恒",
icon = "CDN:Icon_Mask_189",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_189",
modelType = 2,
idleAnim = "AS_Mask_189_idle_001"
},
shareTexts = {
"点点微光，串联起与你的一世之约"
},
beginTime = v4,
suitId = 50181,
suitName = "弥生花语",
suitIcon = "CDN:Icon_Mask_189",
shareOffset = v2
},
[610357] = {
id = 610357,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "弥生花语",
desc = "花瓣里，闪耀着宇宙的永恒",
icon = "CDN:Icon_Mask_189_01",
outlookConf = {
belongTo = 610356,
fashionValue = 35,
belongToGroup = {
610357,
610358
}
},
resourceConf = {
model = "SK_Mask_189",
material = "MI_Mask_189_1_HP01;MI_Mask_189_2_HP01;MI_Mask_189_3_HP01",
modelType = 2,
idleAnim = "AS_Mask_189_idle_001_HP01",
materialSlot = "Mask_1;Mask_2;Mask_3"
},
commodityId = 51378,
shareTexts = {
"点点微光，串联起与你的一世之约"
},
shareOffset = v2
},
[610358] = {
id = 610358,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "弥生花语",
desc = "花瓣里，闪耀着宇宙的永恒",
icon = "CDN:Icon_Mask_189_02",
outlookConf = {
belongTo = 610356,
fashionValue = 35,
belongToGroup = {
610357,
610358
}
},
resourceConf = {
model = "SK_Mask_189",
material = "MI_Mask_189_1_HP02;MI_Mask_189_2_HP02;MI_Mask_189_3_HP02",
modelType = 2,
idleAnim = "AS_Mask_189_idle_001_HP02",
materialSlot = "Mask_1;Mask_2;Mask_3"
},
commodityId = 51379,
shareTexts = {
"点点微光，串联起与你的一世之约"
},
shareOffset = v2
},
[610359] = {
id = 610359,
effect = true,
exceedReplaceItem = {
{
itemId = 3556,
itemNum = 5
}
},
name = "音浪视界",
desc = "音浪很强，晃到世界摇晃！",
icon = "CDN:Icon_Mask_181",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_181",
modelType = 1
},
shareTexts = {
"加入这节奏，你还在等什么！"
},
beginTime = v4,
suitId = 50182,
suitName = "音浪视界",
suitIcon = "CDN:Icon_Mask_181",
shareOffset = {
-10,
25
}
},
[610360] = {
id = 610360,
effect = true,
name = "音浪视界",
desc = "音浪很强，晃到世界摇晃！",
icon = "CDN:Icon_Mask_181_01",
outlookConf = {
belongTo = 610359,
fashionValue = 25,
belongToGroup = {
610360,
610361
}
},
resourceConf = {
model = "SM_Mask_181",
material = "MI_Mask_181_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51381,
shareTexts = {
"加入这节奏，你还在等什么！"
},
shareOffset = {
-10,
25
}
},
[610361] = {
id = 610361,
effect = true,
name = "音浪视界",
desc = "音浪很强，晃到世界摇晃！",
icon = "CDN:Icon_Mask_181_02",
outlookConf = {
belongTo = 610359,
fashionValue = 25,
belongToGroup = {
610360,
610361
}
},
resourceConf = {
model = "SM_Mask_181",
material = "MI_Mask_181_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51382,
shareTexts = {
"加入这节奏，你还在等什么！"
},
shareOffset = {
-10,
25
}
},
[610362] = {
id = 610362,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "迷糊泡泡",
desc = "吹出来，就不会再犯迷糊啦！",
icon = "CDN:Icon_Mask_190",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Mask_190",
modelType = 1
},
shareTexts = {
"我会吹泡泡，你会吗？"
},
beginTime = v4,
suitId = 50183,
suitName = "迷糊泡泡",
suitIcon = "CDN:Icon_Mask_190",
shareOffset = {
-10,
45
}
},
[610363] = {
id = 610363,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "迷糊泡泡",
desc = "吹出来，就不会再犯迷糊啦！",
icon = "CDN:Icon_Mask_190_01",
outlookConf = {
belongTo = 610362,
fashionValue = 35,
belongToGroup = {
610363,
610364
}
},
resourceConf = {
model = "SM_Mask_190",
material = "MI_Mask_190_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51384,
shareTexts = {
"我会吹泡泡，你会吗？"
},
shareOffset = {
-10,
45
}
},
[610364] = {
id = 610364,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "迷糊泡泡",
desc = "吹出来，就不会再犯迷糊啦！",
icon = "CDN:Icon_Mask_190_02",
outlookConf = {
belongTo = 610362,
fashionValue = 35,
belongToGroup = {
610363,
610364
}
},
resourceConf = {
model = "SM_Mask_190",
material = "MI_Mask_190_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51385,
shareTexts = {
"我会吹泡泡，你会吗？"
},
shareOffset = {
-10,
45
}
},
[610365] = {
id = 610365,
effect = true,
name = "苹果视界",
desc = "戴上我，万物飘散苹果味道",
icon = "CDN:Icon_Mask_180",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_180",
modelType = 1,
IconLabelId = 102
},
shareTexts = {
"喜欢吃苹果，更喜欢你"
},
beginTime = v4,
suitId = 50184,
suitName = "苹果视界",
suitIcon = "CDN:Icon_Mask_180",
shareOffset = {
-12,
20
}
},
[610366] = {
id = 610366,
effect = true,
name = "破碎视界",
desc = "在破碎中，探寻一切的真相",
icon = "CDN:Icon_Mask_182",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_182",
modelType = 1
},
shareTexts = {
"距离找出真相，只差你的帮助"
},
beginTime = v4,
suitId = 50185,
suitName = "破碎视界",
suitIcon = "CDN:Icon_Mask_182",
shareOffset = {
-12,
15
}
},
[610367] = {
id = 610367,
effect = true,
name = "破碎视界",
desc = "在破碎中，探寻一切的真相",
icon = "CDN:Icon_Mask_182_01",
outlookConf = {
belongTo = 610366,
fashionValue = 25,
belongToGroup = {
610367,
610368
}
},
resourceConf = {
model = "SM_Mask_182",
material = "MI_Mask_182_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51388,
shareTexts = {
"距离找出真相，只差你的帮助"
},
shareOffset = {
-12,
15
}
},
[610368] = {
id = 610368,
effect = true,
name = "破碎视界",
desc = "在破碎中，探寻一切的真相",
icon = "CDN:Icon_Mask_182_02",
outlookConf = {
belongTo = 610366,
fashionValue = 25,
belongToGroup = {
610367,
610368
}
},
resourceConf = {
model = "SM_Mask_182",
material = "MI_Mask_182_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51389,
shareTexts = {
"距离找出真相，只差你的帮助"
},
shareOffset = {
-12,
15
}
},
[610369] = {
id = 610369,
effect = true,
name = "颠倒之视",
desc = "谁也看不出，我在看哪里",
icon = "CDN:Icon_Mask_177",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_177",
modelType = 1
},
shareTexts = {
"这是一个奇奇怪怪的世界~"
},
beginTime = v4,
suitId = 50186,
suitName = "颠倒之视",
suitIcon = "CDN:Icon_Mask_177",
shareOffset = {
-9,
25
}
},
[610370] = {
id = 610370,
effect = true,
name = "颠倒之视",
desc = "谁也看不出，我在看哪里",
icon = "CDN:Icon_Mask_177_01",
outlookConf = {
belongTo = 610369,
fashionValue = 25,
belongToGroup = {
610370,
610371
}
},
resourceConf = {
model = "SM_Mask_177",
material = "MI_Mask_177_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51391,
shareTexts = {
"这是一个奇奇怪怪的世界~"
},
shareOffset = {
-9,
25
}
},
[610371] = {
id = 610371,
effect = true,
name = "颠倒之视",
desc = "谁也看不出，我在看哪里",
icon = "CDN:Icon_Mask_177_02",
outlookConf = {
belongTo = 610369,
fashionValue = 25,
belongToGroup = {
610370,
610371
}
},
resourceConf = {
model = "SM_Mask_177",
material = "MI_Mask_177_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51392,
shareTexts = {
"这是一个奇奇怪怪的世界~"
},
shareOffset = {
-9,
25
}
},
[610372] = {
id = 610372,
effect = true,
exceedReplaceItem = {
{
itemId = 227,
itemNum = 10
}
},
name = "霓虹之面",
desc = "光影之下，是猫咪面孔~",
icon = "CDN:Icon_Mask_183",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_183",
modelType = 1
},
shareTexts = {
"最真实的自己，只对你展露"
},
beginTime = v4,
suitId = 50187,
suitName = "霓虹之面",
suitIcon = "CDN:Icon_Mask_183",
shareOffset = v3,
shareScaleTimes = 100
},
[610373] = {
id = 610373,
effect = true,
name = "霓虹之面",
desc = "光影之下，是猫咪面孔~",
icon = "CDN:Icon_Mask_183_01",
outlookConf = {
belongTo = 610372,
fashionValue = 25,
belongToGroup = {
610373,
610374
}
},
resourceConf = {
model = "SM_Mask_183",
material = "MI_Mask_183_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51394,
shareTexts = {
"最真实的自己，只对你展露"
},
shareOffset = v3,
shareScaleTimes = 100
},
[610374] = {
id = 610374,
effect = true,
name = "霓虹之面",
desc = "光影之下，是猫咪面孔~",
icon = "CDN:Icon_Mask_183_02",
outlookConf = {
belongTo = 610372,
fashionValue = 25,
belongToGroup = {
610373,
610374
}
},
resourceConf = {
model = "SM_Mask_183",
material = "MI_Mask_183_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51395,
shareTexts = {
"最真实的自己，只对你展露"
},
shareOffset = v3,
shareScaleTimes = 100
},
[610375] = {
id = 610375,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "粉嘟嘟",
desc = "有了我，谁还不是个笨蛋美人呢~",
icon = "CDN:Icon_Mask_201",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_201",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"我才不是脸红，我只是萌萌嘟~"
},
beginTime = v4,
suitId = 50188,
suitName = "红扑嘟嘟",
suitIcon = "CDN:Icon_Mask_201",
shareOffset = v2
},
[610376] = {
id = 610376,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 20
}
},
quality = 1,
name = "无尽耀光",
desc = "闪烁吧，在这无尽的尘埃回廊里",
icon = "CDN:Icon_Mask_191",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Mask_191",
emitter = "FX_CH_Decorate_Mask_191_001",
modelType = 1
},
shareTexts = {
"终有一天，光芒将照耀你我"
},
beginTime = v4,
suitId = 50189,
suitName = "无尽耀光",
suitIcon = "CDN:Icon_Mask_191",
shareOffset = v3
},
[610377] = {
id = 610377,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "无尽耀光",
desc = "闪烁吧，在这无尽的尘埃回廊里",
icon = "CDN:Icon_Mask_191_01",
outlookConf = {
belongTo = 610376,
fashionValue = 35,
belongToGroup = {
610377,
610378
}
},
resourceConf = {
model = "SM_Mask_191",
material = "MI_Mask_191_HP01",
emitter = "FX_CH_Decorate_Mask_191_001_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51398,
shareTexts = {
"终有一天，光芒将照耀你我"
},
shareOffset = v3
},
[610378] = {
id = 610378,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "无尽耀光",
desc = "闪烁吧，在这无尽的尘埃回廊里",
icon = "CDN:Icon_Mask_191_02",
outlookConf = {
belongTo = 610376,
fashionValue = 35,
belongToGroup = {
610377,
610378
}
},
resourceConf = {
model = "SM_Mask_191",
material = "MI_Mask_191_HP02",
emitter = "FX_CH_Decorate_Mask_191_001_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51399,
shareTexts = {
"终有一天，光芒将照耀你我"
},
shareOffset = v3
},
[610379] = {
id = 610379,
effect = true,
name = "雷霆视界",
desc = "戴上闪电，快如闪电",
icon = "CDN:Icon_Mask_193",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_193",
modelType = 1
},
shareTexts = {
"不想停歇，来到你身边"
},
beginTime = v4,
suitId = 50190,
suitName = "雷霆视界",
suitIcon = "CDN:Icon_Mask_193",
shareOffset = {
-10,
25
}
},
[610380] = {
id = 610380,
effect = true,
name = "雷霆视界",
desc = "戴上闪电，快如闪电",
icon = "CDN:Icon_Mask_193_01",
outlookConf = {
belongTo = 610379,
fashionValue = 25,
belongToGroup = {
610380,
610381
}
},
resourceConf = {
model = "SM_Mask_193",
material = "MI_Mask_193_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51401,
shareTexts = {
"不想停歇，来到你身边"
},
shareOffset = {
-10,
25
}
},
[610381] = {
id = 610381,
effect = true,
name = "雷霆视界",
desc = "戴上闪电，快如闪电",
icon = "CDN:Icon_Mask_193_02",
outlookConf = {
belongTo = 610379,
fashionValue = 25,
belongToGroup = {
610380,
610381
}
},
resourceConf = {
model = "SM_Mask_193",
material = "MI_Mask_193_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51402,
shareTexts = {
"不想停歇，来到你身边"
},
shareOffset = {
-10,
25
}
},
[610382] = {
id = 610382,
effect = true,
name = "靓仔标配",
desc = "整条街最靓的仔，除了你，还有谁！",
icon = "CDN:Icon_Mask_194",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_194",
modelType = 1
},
shareTexts = {
"你也为我着迷吗？"
},
beginTime = v4,
suitId = 50191,
suitName = "靓仔标配",
suitIcon = "CDN:Icon_Mask_194",
shareOffset = {
-10,
25
}
},
[610383] = {
id = 610383,
effect = true,
name = "靓仔标配",
desc = "整条街最靓的仔，除了你，还有谁！",
icon = "CDN:Icon_Mask_194_01",
outlookConf = {
belongTo = 610382,
fashionValue = 25,
belongToGroup = {
610383,
610384
}
},
resourceConf = {
model = "SM_Mask_194",
material = "MI_Mask_194_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51404,
shareTexts = {
"你也为我着迷吗？"
},
shareOffset = {
-10,
25
}
},
[610384] = {
id = 610384,
effect = true,
name = "靓仔标配",
desc = "整条街最靓的仔，除了你，还有谁！",
icon = "CDN:Icon_Mask_194_02",
outlookConf = {
belongTo = 610382,
fashionValue = 25,
belongToGroup = {
610383,
610384
}
},
resourceConf = {
model = "SM_Mask_194",
material = "MI_Mask_194_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51405,
shareTexts = {
"你也为我着迷吗？"
},
shareOffset = {
-10,
25
}
},
[610385] = {
id = 610385,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "曲奇回忆",
desc = "曲奇味的回忆，是回不去的从前",
icon = "CDN:Icon_Mask_200",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_200",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50192,
suitName = "曲奇回忆",
suitIcon = "CDN:Icon_Mask_200",
shareOffset = v2
},
[610386] = {
id = 610386,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "熊宝嗅嗅",
desc = "哪里有吃的，熊宝一闻就知道！",
icon = "CDN:Icon_Mask_192",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_192",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50193,
suitName = "熊宝嗅嗅",
suitIcon = "CDN:Icon_Mask_192",
shareOffset = v2
},
[610387] = {
id = 610387,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "胡言乱语",
desc = "输出全靠嘴，伤害值为零",
icon = "CDN:Icon_Mask_207",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_207",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"警告，我要开始输出了！"
},
beginTime = v4,
suitId = 50194,
suitName = "胡言乱语",
suitIcon = "CDN:Icon_Mask_207",
shareOffset = v2
},
[610388] = {
id = 610388,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "兔兔眼镜",
desc = "小兔甜心，带你看世界",
icon = "CDN:Icon_Mask_204",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_204",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50195,
suitName = "兔兔眼镜",
suitIcon = "CDN:Icon_Mask_204",
shareOffset = v2
},
[610389] = {
id = 610389,
effect = true,
name = "小丑鼻",
desc = "我真的不是小丑！",
icon = "CDN:Icon_Mask_202",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_202",
modelType = 1
},
shareTexts = {
"小丑轮到我当啦！"
},
beginTime = v4,
suitId = 50196,
suitName = "小丑鼻",
suitIcon = "CDN:Icon_Mask_202",
shareOffset = v3
},
[610390] = {
id = 610390,
effect = true,
name = "小丑鼻",
desc = "我真的不是小丑！",
icon = "CDN:Icon_Mask_202_01",
outlookConf = {
belongTo = 610389,
fashionValue = 25,
belongToGroup = {
610390,
610391
}
},
resourceConf = {
model = "SM_Mask_202",
material = "MI_Mask_202_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51411,
shareTexts = {
"小丑轮到我当啦！"
},
shareOffset = v3
},
[610391] = {
id = 610391,
effect = true,
name = "小丑鼻",
desc = "我真的不是小丑！",
icon = "CDN:Icon_Mask_202_02",
outlookConf = {
belongTo = 610389,
fashionValue = 25,
belongToGroup = {
610390,
610391
}
},
resourceConf = {
model = "SM_Mask_202",
material = "MI_Mask_202_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51412,
shareTexts = {
"小丑轮到我当啦！"
},
shareOffset = v3
},
[610392] = {
id = 610392,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 100
}
},
quality = 1,
name = "柠光绘梦",
desc = "蘸取晨曦中最亮眼的柠黄，点缀视野",
icon = "CDN:Icon_Mask_209",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Mask_209",
emitter = "FX_CH_Decorate_Mask_209_001",
modelType = 1
},
shareTexts = {
"用艺术的视角发现清新色彩"
},
beginTime = v4,
suitId = 50197,
suitName = "柠光绘梦",
suitIcon = "CDN:Icon_Mask_209",
shareOffset = v3
},
[610393] = {
id = 610393,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "柠光绘梦",
desc = "蘸取晨曦中最亮眼的柠黄，点缀视野",
icon = "CDN:Icon_Mask_209_01",
outlookConf = {
belongTo = 610392,
fashionValue = 35,
belongToGroup = {
610393,
610394
}
},
resourceConf = {
model = "SM_Mask_209",
material = "MI_Mask_209_HP01",
emitter = "FX_CH_Decorate_Mask_209_001_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51414,
shareTexts = {
"用艺术的视角发现清新色彩"
},
shareOffset = v3
},
[610394] = {
id = 610394,
effect = true,
exceedReplaceItem = v0,
quality = 1,
name = "柠光绘梦",
desc = "蘸取晨曦中最亮眼的柠黄，点缀视野",
icon = "CDN:Icon_Mask_209_02",
outlookConf = {
belongTo = 610392,
fashionValue = 35,
belongToGroup = {
610393,
610394
}
},
resourceConf = {
model = "SM_Mask_209",
material = "MI_Mask_209_HP02",
emitter = "FX_CH_Decorate_Mask_209_001_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51415,
shareTexts = {
"用艺术的视角发现清新色彩"
},
shareOffset = v3
},
[610395] = {
id = 610395,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "落日眼镜",
desc = "远山的落日，是一片橘红色",
icon = "CDN:Icon_Mask_195",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_195",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50198,
suitName = "落日眼镜",
suitIcon = "CDN:Icon_Mask_195"
},
[610396] = {
id = 610396,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "霓虹眼镜",
desc = "霓虹灯下，一切皆是梦幻",
icon = "CDN:Icon_Mask_196",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_196",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50199,
suitName = "霓虹眼镜",
suitIcon = "CDN:Icon_Mask_196"
},
[610397] = {
id = 610397,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "初日眼镜",
desc = "如果可以，想要亲眼看到太阳升起",
icon = "CDN:Icon_Mask_197",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_197",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50200,
suitName = "初日眼镜",
suitIcon = "CDN:Icon_Mask_197"
},
[610398] = {
id = 610398,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "滑稽面具",
desc = "回忆里，欢乐的街头传奇",
icon = "CDN:Icon_Mask_208",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_208",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50201,
suitName = "滑稽面具",
suitIcon = "CDN:Icon_Mask_208"
},
[610399] = {
id = 610399,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "钳钳视界",
desc = "潜入深海，探索海中世界~",
icon = "CDN:Icon_Mask_205",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_205",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50202,
suitName = "钳钳视界",
suitIcon = "CDN:Icon_Mask_205"
},
[618001] = {
id = 618001,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "恶魔面具笑",
desc = "May玩法测试",
icon = "CDN:Icon_Mask_091",
useType = "IUTO_GiftPackage",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_MAY_DemonMask_01",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"测试测试"
}
},
[618002] = {
id = 618002,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "恶魔面具哭",
desc = "May玩法测试",
icon = "CDN:Icon_Mask_091",
useType = "IUTO_GiftPackage",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_MAY_DemonMask_02",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"测试测试"
}
}
}

local mt = {
effect = false,
type = "ItemType_FaceOrnament",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
scaleTimes = 200,
previewShareOffset = v2
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data