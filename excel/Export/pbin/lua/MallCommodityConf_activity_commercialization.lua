--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/H_活动中心配置_商业化.xlsx: 活动商城

local data = {
[770001] = {
mallId = 154,
commodityId = 770001,
commodityName = "幸运卡礼包",
coinType = 0,
price = 30,
discountPrice = 6,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1730390399
},
gender = 0,
canDirectBuy = true,
itemIds = {
310261
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770002] = {
mallId = 154,
commodityId = 770002,
commodityName = "新赛季BP礼包",
coinType = 0,
price = 30,
discountPrice = 24,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1730390399
},
gender = 0,
canDirectBuy = true,
itemIds = {
310262
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770003] = {
mallId = 154,
commodityId = 770003,
commodityName = "幸运卡礼包",
coinType = 0,
price = 30,
discountPrice = 6,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1732806000
},
gender = 0,
canDirectBuy = true,
itemIds = {
310274
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770004] = {
mallId = 154,
commodityId = 770004,
commodityName = "新赛季BP礼包",
coinType = 0,
price = 30,
discountPrice = 24,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1732806000
},
gender = 0,
canDirectBuy = true,
itemIds = {
310275
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770005] = {
mallId = 154,
commodityId = 770005,
commodityName = "幸运卡礼包",
coinType = 0,
price = 30,
discountPrice = 6,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1737039600
},
gender = 0,
canDirectBuy = true,
itemIds = {
310295
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770006] = {
mallId = 154,
commodityId = 770006,
commodityName = "新赛季BP礼包",
coinType = 0,
price = 30,
discountPrice = 24,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1737039600
},
gender = 0,
canDirectBuy = true,
itemIds = {
310296
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770007] = {
mallId = 154,
commodityId = 770007,
commodityName = "幸运卡礼包",
coinType = 0,
price = 30,
discountPrice = 6,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1741622400
},
endTime = {
seconds = 1741878000
},
gender = 0,
canDirectBuy = true,
itemIds = {
310315
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770008] = {
mallId = 154,
commodityId = 770008,
commodityName = "新赛季BP礼包",
coinType = 0,
price = 30,
discountPrice = 24,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1741622400
},
endTime = {
seconds = 1741878000
},
gender = 0,
canDirectBuy = true,
itemIds = {
310316
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770009] = {
mallId = 154,
commodityId = 770009,
commodityName = "幸运卡礼包",
coinType = 0,
price = 30,
discountPrice = 6,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1746111600
},
gender = 0,
canDirectBuy = true,
itemIds = {
310325
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770010] = {
mallId = 154,
commodityId = 770010,
commodityName = "新赛季BP礼包",
coinType = 0,
price = 30,
discountPrice = 24,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1746111600
},
gender = 0,
canDirectBuy = true,
itemIds = {
310326
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770011] = {
mallId = 154,
commodityId = 770011,
commodityName = "幸运卡礼包",
coinType = 0,
price = 30,
discountPrice = 6,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750950000
},
gender = 0,
canDirectBuy = true,
itemIds = {
310343
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
},
[770012] = {
mallId = 154,
commodityId = 770012,
commodityName = "新赛季BP礼包",
coinType = 0,
price = 30,
discountPrice = 24,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750950000
},
gender = 0,
canDirectBuy = true,
itemIds = {
310344
},
itemNums = {
1
},
itemMaxOwnNums = {
1
}
}
}

local mt = {
canDirectBuy = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data