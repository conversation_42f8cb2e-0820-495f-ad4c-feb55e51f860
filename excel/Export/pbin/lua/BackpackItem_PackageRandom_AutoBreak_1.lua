--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_礼包.xlsx: 随机礼包

local v0 = 3

local v1 = "购买获得"

local data = {
[320078] = {
id = 320078,
effect = true,
quality = 2,
name = "返场饰品随机礼包",
desc = "打开后有机会获得以下奖励的一项：饰品5选1礼包：15%；星愿币*1：25%；云朵币*30：60%",
getWay = "限时礼包",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
330082,
2,
6
},
itemNums = {
1,
1,
30
},
expireDays = {
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
15,
25,
60
},
guaranteeItemIds = {
330082
},
guaranteeTimes = {
2
},
ownedFilter = {
false,
false,
false
},
drawLimitTimes = {
2,
0,
0
},
guaranteeHitTimes = {
2
}
}
},
pictureUrl = "T_CutGift_Icon_pack_sjsp1.astc"
},
[320079] = {
id = 320079,
effect = true,
quality = 3,
name = "星愿宝匣",
desc = [[开启宝匣将随机获得以下任一奖励：星语摩天轮*1；梦幻花羽*1；飞鼠宝宝*1；冰晶花梦*1，优先获得尚未拥有的奖励
当奖励全部集齐时，开启宝匣将获得幸运币*60]],
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿兑换",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
640075,
620588,
630411,
620152,
3
},
itemNums = {
1,
1,
1,
1,
60
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
99,
5000,
4900,
0
},
ownedFilter = {
true,
true,
true,
true,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1
}
}
}
},
[320080] = {
id = 320080,
effect = true,
quality = 2,
name = "随机礼包",
desc = "《谁是狼人》礼包，物品获取概率：身份卡*1:20%，狼人币*200:20%，打call*5:20%，钻戒道具*5:20%，祝福道具*5:20%",
icon = "CDN:T_Common_Item_System_WerewolfBag_12",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
200102,
240413,
240408,
240419,
13
},
itemNums = {
1,
5,
5,
5,
200
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
10,
10,
10,
10
}
}
}
},
[320090] = {
id = 320090,
effect = true,
stackedNum = 99,
quality = 3,
name = "峡谷幸运宝箱",
desc = "打开后可获得峡谷3v3永久随机表情一个",
icon = "CDN:T_Common_Item_System_BagBig_010",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
711116,
711119,
711125,
711118,
711112,
711121,
711120,
711131,
711130,
711126,
711084,
711089,
711087,
711094,
711092
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
6,
6,
6,
6,
6,
6,
6,
6,
6,
6,
8,
8,
8,
8,
8
},
drawLimitTimes = {
5,
5,
5,
5,
5,
5,
5,
5,
5,
5,
5,
5,
5,
5,
5
}
}
}
},
[320091] = {
id = 320091,
effect = true,
stackedNum = 99,
quality = 2,
name = "峡谷装扮宝箱",
desc = "打开后可获得峡谷3v3限时随机装扮一件，有概率能够获得永久装扮",
icon = "CDN:T_Common_Item_System_BagBig_009",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620512,
620513,
620494,
620495,
620470,
403000,
403010,
402130,
403480,
402140,
620512,
620513,
620494,
620495,
620470,
403000,
403010,
402130,
403480,
402140
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
},
expireDays = {
30,
30,
30,
30,
30,
30,
30,
30,
30,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
90,
90,
90,
90,
90,
90,
90,
90,
90,
90,
10,
10,
10,
10,
10,
10,
10,
10,
10,
10
},
drawLimitTimes = {
5,
5,
5,
5,
5,
5,
5,
5,
5,
5,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
}
}
}
},
[320092] = {
id = 320092,
effect = true,
lowVer = "1.3.26.95",
quality = 2,
name = "狼人补给礼包1",
desc = "《谁是狼人》礼包，物品获取概率：发困会议表情*1、狼人币*200、身份卡*2、丢鸡蛋道具*10、祝福道具*3概率各20%",
icon = "CDN:T_Common_Item_System_BagBig_009",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
240625,
13,
200102,
240401,
240419
},
itemNums = {
1,
200,
2,
10,
3
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
20,
20,
20,
20,
20
},
guaranteeItemIds = {
240625
},
guaranteeTimes = {
1
},
drawLimitTimes = {
1,
8,
5,
6,
10
},
guaranteeHitTimes = {
5
}
}
}
},
[320093] = {
id = 320093,
effect = true,
lowVer = "1.3.26.95",
quality = 2,
name = "狼人补给礼包2",
desc = "《谁是狼人》礼包，物品获取概率：流汗会议表情*1、狼人币*200、阵营卡*3、永恒之恋*3、丢盲盒*3概率各20%",
icon = "CDN:T_Common_Item_System_BagBig_009",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
240623,
13,
200101,
240409,
240418
},
itemNums = {
1,
200,
3,
3,
3
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
20,
20,
20,
20,
20
},
guaranteeItemIds = {
240623
},
guaranteeTimes = {
1
},
drawLimitTimes = {
1,
5,
6,
8,
10
},
guaranteeHitTimes = {
5
}
}
}
},
[320094] = {
id = 320094,
effect = true,
name = "兑换券宝箱",
desc = "打开后有机会获得以下奖励的一项：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:T_Common_Item_System_Farm11",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
20,
30,
40
}
}
}
},
[320095] = {
id = 320095,
effect = true,
name = "高级兑换券宝箱",
desc = "打开后有机会获得以下奖励的一项：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:T_Common_Item_System_Farm12",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
12,
25,
63
}
}
}
},
[320096] = {
id = 320096,
effect = true,
quality = 3,
name = "冬日随机礼盒",
desc = "打开后有机会获得以下奖励的一项：豆趣横生：15%；绒绒暖帽：15%；星愿币*6：30%；云朵币*60：40%",
icon = "T_CutGift_Icon_pack_drsj1",
getWay = "限时礼包",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620699,
630364,
2,
6
},
itemNums = {
1,
1,
6,
60
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
15,
15,
30,
40
},
ownedFilter = {
true,
true,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1
}
}
}
},
[320097] = {
id = 320097,
effect = true,
quality = 3,
name = "卡包测试礼包",
desc = "卡包测试礼包，5种卡包",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "超值礼包",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
290011,
290012,
290013,
290014,
290015
},
itemNums = {
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
20,
20,
20,
20,
20
},
ownedFilter = {
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1
}
}
}
},
[320098] = {
id = 320098,
effect = true,
quality = 3,
name = "幸运币随机礼包",
desc = "打开后有机会获得以下奖励的一项：幸运币*2：61%；幸运币*4：30%；幸运币*6：8%；幸运币*12：1%",
icon = "T_CutGift_Icon_Pack_02",
getWay = "限时礼包",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3,
3,
3,
3
},
itemNums = {
2,
4,
6,
12
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
61,
30,
8,
1
}
}
}
},
[320121] = {
id = 320121,
effect = true,
quality = 3,
name = "秋千进入随机礼包2",
desc = "获得后自动打开，获得随机数量的宝箱钥匙",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿额外",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
226,
226,
226,
226,
226,
226
},
itemNums = {
180,
185,
190,
195,
200,
210
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
200,
200,
200,
200,
50
},
ownedFilter = {
false,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1
}
}
}
},
[320120] = {
id = 320120,
effect = true,
quality = 3,
name = "秋千开箱随机礼包2",
desc = "获得后自动打开，获得随机数量的宝箱钥匙",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿额外",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
226,
226,
226,
226,
226,
226
},
itemNums = {
20,
30,
35,
40,
45,
60
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
200,
200,
200,
200,
50
},
ownedFilter = {
false,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1
}
}
}
},
[320122] = {
id = 320122,
effect = true,
quality = 3,
name = "心语宝匣",
desc = [[开启宝匣将随机获得以下任一奖励：心语之桥*1；沧海之羽*1；梦幻泡影*1；兔耳宝匣*1，优先获得尚未拥有的奖励
当奖励全部集齐时，开启宝匣将获得幸运币*60]],
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿兑换",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
640123,
620770,
610322,
620775,
3
},
itemNums = {
1,
1,
1,
1,
60
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
99,
5000,
4900,
0
},
ownedFilter = {
true,
true,
true,
true,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1
}
}
}
},
[320099] = {
id = 320099,
effect = true,
quality = 3,
name = "晴天宝宝随机礼盒",
desc = "打开后有机会获得以下奖励的一项：晴天宝宝：10%(4次必得)；幸运币*5(20%)；云朵币*50(35%)；时装染色剂*3(35%)",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "购买",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
410010,
3,
6,
200006
},
itemNums = {
1,
5,
50,
3
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
20,
35,
35
},
guaranteeItemIds = {
410010
},
guaranteeTimes = {
4
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[320201] = {
id = 320201,
effect = true,
quality = 3,
name = "星钻宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "摇钱树奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
1,
1,
1,
1,
1,
1
},
itemNums = {
50,
60,
70,
80,
90,
100
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
600,
250,
100,
30,
15,
5
}
}
}
},
[320202] = {
id = 320202,
effect = true,
quality = 3,
name = "星钻宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "摇钱树奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
1,
1,
1,
1,
1,
1
},
itemNums = {
90,
120,
150,
180,
240,
300
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
625,
250,
85,
20,
10,
5
}
}
}
},
[320123] = {
id = 320123,
effect = true,
quality = 2,
name = "茸茸随机礼盒",
desc = "物品获取概率：焰狸狸：20%；茸茸兔：20%；幸运币：20%；云朵币：40%",
icon = "CDN:T_Common_Item_System_foxrabbit",
getWay = "发现系统",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
410560,
410570,
3,
6
},
itemNums = {
1,
1,
6,
60
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
8,
8,
34,
50
},
guaranteeItemIds = {
410560,
410570
},
guaranteeTimes = {
2,
2
},
ownedFilter = {
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1
}
}
}
},
[320116] = {
id = 320116,
effect = true,
type = "ItemType_AutoUse",
name = "（屏蔽自身）开箱动画测试礼包",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxColourful",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
400460,
620238,
400800,
400890,
400790,
2,
2,
2,
203002,
203003,
203001
},
itemNums = {
1,
1,
1,
1,
1,
3,
5,
8,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
827000,
747000,
372666,
372666,
372666,
1500000,
2500000,
3308000,
1,
1,
1
}
},
animationNtf = 1
},
isShowPopRewardView = 0,
animationParam = {
"UI_Cup_TreasureChest",
"3"
}
},
[320117] = {
id = 320117,
effect = true,
quality = 3,
name = "幻彩宝匣",
desc = [[开启宝匣将随机获得以下任一奖励：幻彩画匠  绮*1；梦笔生花  绮*1；粉兔颜料*1；柠光绘梦*1；萌兔调色盘*1；绮梦独角兽*1，优先获得尚未拥有的奖励
当奖励全部集齐时，若再次开启宝匣将获得幸运币*60]],
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿兑换",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620900,
640163,
410840,
411190,
630606,
610392,
3
},
itemNums = {
1,
1,
1,
1,
1,
1,
60
},
expireDays = {
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
300,
25,
1,
900,
10000,
10000,
0
},
ownedFilter = {
true,
true,
true,
true,
true,
true,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1,
1
}
}
}
},
[320801] = {
id = 320801,
effect = true,
quality = 2,
name = "每日免费礼包",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_WerewolfFreeBag_001",
getWay = "谁是狼人月卡",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
13,
240426,
240427,
200101,
200102
},
itemNums = {
25,
2,
2,
1,
1
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
30,
25,
25,
10,
10
},
drawLimitTimes = {
1,
1,
1,
1,
1
}
}
}
},
[320118] = {
id = 320118,
effect = true,
type = "ItemType_AutoUse",
name = "（屏蔽开启后）开箱动画测试礼包",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxColourful",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
400460,
620238,
400800,
400890,
400790,
2,
2,
2,
203002,
203003,
203001
},
itemNums = {
1,
1,
1,
1,
1,
3,
5,
8,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
827000,
747000,
372666,
372666,
372666,
1500000,
2500000,
3308000,
1,
1,
1
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"3"
},
isShowCommonPopRewardView = 0
},
[320124] = {
id = 320124,
effect = true,
lowVer = "1.3.88.1",
quality = 2,
name = "测试随机礼盒",
desc = "物品获取概率：真言骑士 蕾亚：1.5%；智慧猫头鹰：4%；剑影流光：8%；幸运币：5%；时装染色膏：18%；饰品调色盘：18%；云朵币：15.5%；星宝印章：15%；心心宝瓶：15%",
icon = "CDN:T_Common_Item_System_foxrabbit",
getWay = "限时礼包",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
410280,
620654,
722041,
3,
200006,
200008,
6,
4,
200016
},
itemNums = {
1,
1,
1,
2,
2,
2,
30,
2000,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
15,
40,
80,
50,
180,
180,
155,
150,
150
},
guaranteeItemIds = {
410280,
620654,
3
},
guaranteeTimes = {
20,
10,
4
},
ownedFilter = {
false,
false,
false,
false,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
2,
2,
2,
2,
0,
4
}
}
}
},
[320125] = {
id = 320125,
effect = true,
name = "1星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:T_Common_Item_System_Farm11",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
15,
35,
45
}
}
}
},
[320126] = {
id = 320126,
effect = true,
name = "2星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:T_Common_Item_System_Farm11",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
25,
25,
45
}
}
}
},
[320127] = {
id = 320127,
effect = true,
name = "3星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:T_Common_Item_System_Farm11",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
20,
30,
40
}
}
}
},
[320128] = {
id = 320128,
effect = true,
name = "4星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:T_Common_Item_System_Farm11",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
15,
15,
35,
35
}
}
}
},
[320129] = {
id = 320129,
effect = true,
name = "5星兑换券宝箱",
desc = "概率获得其一：兑换券*10；兑换券*8；兑换券*6；兑换券*4",
icon = "CDN:T_Common_Item_System_Farm11",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
10,
8,
6,
4
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
2,
5,
5,
88
}
}
}
},
[320130] = {
id = 320130,
effect = true,
name = "1星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:T_Common_Item_System_Farm12",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
8,
24,
18,
50
}
}
}
},
[320131] = {
id = 320131,
effect = true,
name = "2星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:T_Common_Item_System_Farm12",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
9,
27,
19,
45
}
}
}
},
[320132] = {
id = 320132,
effect = true,
name = "3星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:T_Common_Item_System_Farm12",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
30,
20,
40
}
}
}
},
[320133] = {
id = 320133,
effect = true,
name = "4星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:T_Common_Item_System_Farm12",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
11,
33,
21,
35
}
}
}
},
[320134] = {
id = 320134,
effect = true,
name = "5星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:T_Common_Item_System_Farm12",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3510,
3510,
3510,
3510
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
12,
36,
22,
30
}
}
}
},
[320135] = {
id = 320135,
effect = true,
quality = 3,
name = "音语宝匣",
desc = [[开启宝匣将随机获得以下任一奖励：猫耳电音台*1；音乐翅膀*1；游鱼遗尾*1；无尽耀光*1，优先获得尚未拥有的奖励
当奖励全部集齐时，开启宝匣将获得幸运币*60]],
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿兑换",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
640123,
620770,
610322,
620775,
3
},
itemNums = {
1,
1,
1,
1,
60
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
99,
5000,
4900,
0
},
ownedFilter = {
true,
true,
true,
true,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1
}
}
}
},
[320136] = {
id = 320136,
effect = true,
quality = 3,
name = "DJ进入随机礼包2",
desc = "获得后自动打开，获得随机数量的宝箱钥匙",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿额外",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
227,
227,
227,
227,
227,
227
},
itemNums = {
180,
185,
190,
195,
200,
210
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
200,
200,
200,
200,
50
},
ownedFilter = {
false,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1
}
}
}
},
[320137] = {
id = 320137,
effect = true,
quality = 3,
name = "DJ开箱随机礼包2",
desc = "获得后自动打开，获得随机数量的宝箱钥匙",
icon = "CDN:T_Common_Item_System_BagBig_009",
getWay = "祈愿额外",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
227,
227,
227,
227,
227,
227
},
itemNums = {
20,
30,
35,
40,
45,
60
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
200,
200,
200,
200,
50
},
ownedFilter = {
false,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1
}
}
}
},
[320140] = {
id = 320140,
effect = true,
name = "1星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:CT_Arena_Icon_Gift_0010",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
15,
35,
45
}
}
}
},
[320141] = {
id = 320141,
effect = true,
name = "2星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:CT_Arena_Icon_Gift_0010",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
25,
25,
45
}
}
}
},
[320142] = {
id = 320142,
effect = true,
name = "3星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:CT_Arena_Icon_Gift_0010",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
20,
30,
40
}
}
}
},
[320143] = {
id = 320143,
effect = true,
name = "4星兑换券宝箱",
desc = "概率获得其一：兑换券*8；兑换券*6；兑换券*4；兑换券*2",
icon = "CDN:CT_Arena_Icon_Gift_0010",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
8,
6,
4,
2
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
15,
15,
35,
35
}
}
}
},
[320144] = {
id = 320144,
effect = true,
name = "5星兑换券宝箱",
desc = "概率获得其一：兑换券*10；兑换券*8；兑换券*6；兑换券*4",
icon = "CDN:CT_Arena_Icon_Gift_0010",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
10,
8,
6,
4
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
2,
5,
5,
88
}
}
}
},
[320145] = {
id = 320145,
effect = true,
name = "1星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:CT_Arena_Icon_Gift_011",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
8,
24,
18,
50
}
}
}
},
[320146] = {
id = 320146,
effect = true,
name = "2星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:CT_Arena_Icon_Gift_011",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
9,
27,
19,
45
}
}
}
},
[320147] = {
id = 320147,
effect = true,
name = "3星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:CT_Arena_Icon_Gift_011",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
30,
20,
40
}
}
}
},
[320148] = {
id = 320148,
effect = true,
name = "4星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:CT_Arena_Icon_Gift_011",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
11,
33,
21,
35
}
}
}
},
[320149] = {
id = 320149,
effect = true,
name = "5星高级兑换券宝箱",
desc = "概率获得其一：兑换券*50；兑换券*10；兑换券*8；兑换券*6",
icon = "CDN:CT_Arena_Icon_Gift_011",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3640,
3640,
3640,
3640
},
itemNums = {
50,
10,
8,
6
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
12,
36,
22,
30
}
}
}
},
[320150] = {
id = 320150,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
quality = 3,
name = "狼人稀有互动礼盒",
desc = "开启后随机获得以下奖励其一",
icon = "CDN:T_Common_Item_System_WerewolfBag_General01",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
240402,
240413,
240417,
240418
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
10,
10,
10
}
}
}
},
[320151] = {
id = 320151,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
quality = 3,
name = "奖杯祈梦宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxBlue",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
610291,
722101,
711538,
404800,
620888,
630518,
711536,
711537,
722102,
722103,
711536,
711537,
510142,
510269,
510312,
510319,
520095,
520183,
520211,
520215,
530073,
530159,
530187,
530191,
203002,
203003,
203001,
219000,
200101,
3541,
240412,
240416,
240420,
3134,
3134,
3134,
200005,
200006,
200008,
725001,
725002,
725003,
725004,
725005,
725006,
200017,
200016,
200015,
6,
6,
6,
4,
4,
4
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
100,
3,
3,
3,
25,
10,
5,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
10,
5,
2,
1000,
800,
600
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
22,
299,
21,
21,
44,
44,
34,
34,
97,
97,
97,
97,
3,
3,
3,
3,
3,
3,
3,
3,
3,
3,
3,
3,
41,
41,
41,
854,
196,
654,
160,
160,
160,
10,
90,
17,
6,
6,
6,
101,
101,
101,
101,
101,
101,
1,
2,
7,
499,
1200,
800,
800,
1500,
1200
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"1"
}
},
[320152] = {
id = 320152,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
quality = 2,
name = "奖杯祈梦宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxGold",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
610291,
722101,
711538,
404800,
620888,
630518,
711536,
711537,
722102,
722103,
711536,
711537,
203002,
203003,
203001,
219000,
200101,
3541,
240412,
240416,
240420,
3134,
3134,
3134,
200005,
200006,
200008,
725001,
725002,
725003,
725004,
725005,
725006,
200017,
200016,
6,
6,
6,
4,
4,
4
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
100,
3,
3,
3,
25,
10,
5,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
10,
5,
2,
1000,
800,
600
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
384,
1771,
484,
484,
741,
741,
95,
95,
100,
100,
100,
100,
601,
601,
601,
1063,
22,
1264,
10,
10,
10,
10,
20,
10,
2,
2,
2,
12,
12,
12,
12,
12,
12,
1,
1,
100,
50,
50,
140,
100,
63
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"2"
}
},
[320153] = {
id = 320153,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
name = "奖杯祈梦宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxColourful",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
610291,
722101,
711538,
404800,
620888,
630518,
711536,
711537,
722102,
722103,
711536,
711537,
203002,
203003,
203001,
219000,
3541
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
100
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1118,
1118,
1118,
500,
50,
50,
50,
50,
50,
50,
50,
50,
1249,
1249,
1249,
1749,
249
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"3"
}
},
[320154] = {
id = 320154,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
quality = 3,
name = "奖杯祈梦宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxBlue",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
610291,
722101,
711538,
404800,
620888,
630518,
711536,
711537,
722102,
722103,
711536,
711537,
510142,
510269,
510312,
510319,
520095,
520183,
520211,
520215,
530073,
530159,
530187,
530191,
203002,
203003,
203001,
219000,
200101,
3541,
240412,
240416,
240420,
3134,
3134,
3134,
200005,
200006,
200008,
725001,
725002,
725003,
725004,
725005,
725006,
200017,
200016,
200015,
6,
6,
6,
4,
4,
4
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
100,
3,
3,
3,
25,
10,
5,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
10,
5,
2,
1000,
800,
600
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
1,
1,
1,
4400,
4400,
3400,
3400,
9700,
9700,
9700,
9700,
300,
300,
300,
300,
300,
300,
300,
300,
300,
300,
300,
300,
200,
200,
200,
56132,
22200,
56432,
13000,
13000,
13000,
4632,
9000,
1000,
12200,
12200,
12200,
6000,
6000,
6000,
6000,
6000,
6000,
100,
700,
4000,
20000,
80000,
175800,
50000,
150000,
199800
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"1"
}
},
[320155] = {
id = 320155,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
quality = 3,
name = "奖杯祈梦宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxBlue",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
610291,
722101,
711538,
404800,
620888,
630518,
711536,
711537,
722102,
722103,
711536,
711537,
510142,
510269,
510312,
510319,
520095,
520183,
520211,
520215,
530073,
530159,
530187,
530191,
203002,
203003,
203001,
219000,
200101,
3541,
240412,
240416,
240420,
3134,
3134,
3134,
200005,
200006,
200008,
725001,
725002,
725003,
725004,
725005,
725006,
200017,
200016,
200015,
6,
6,
6,
4,
4,
4
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
100,
3,
3,
3,
25,
10,
5,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
10,
5,
2,
1000,
800,
600
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
1,
1,
1,
500,
500,
1000,
1000,
500,
500,
500,
500,
300,
300,
300,
300,
300,
300,
300,
300,
300,
300,
300,
300,
2000,
2000,
2000,
26348,
19600,
20048,
15000,
15000,
15000,
1700,
10000,
1000,
14500,
14500,
14500,
19600,
19600,
19600,
19600,
19600,
19600,
100,
200,
300,
20000,
50000,
80000,
50000,
100000,
400000
},
drawLimitTimes = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
4,
6,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"1"
}
},
[320156] = {
id = 320156,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
quality = 2,
name = "奖杯祈梦宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxGold",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
610291,
722101,
711538,
404800,
620888,
630518,
711536,
711537,
722102,
722103,
711536,
711537,
203002,
203003,
203001,
219000,
200101,
3541,
240412,
240416,
240420,
3134,
3134,
3134,
200005,
200006,
200008,
725001,
725002,
725003,
725004,
725005,
725006,
200017,
200016,
6,
6,
6,
4,
4,
4
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
100,
3,
3,
3,
25,
10,
5,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
10,
5,
2,
1000,
800,
600
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1133000,
1771000,
1161000,
548000,
773000,
773000,
127000,
127000,
132000,
132000,
132000,
132000,
1,
1,
1,
1740000,
1,
1319000,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"2"
}
},
[320157] = {
id = 320157,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
name = "奖杯祈梦宝箱",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_StarCup_Icon_BigBoxColourful",
getWay = "奖杯征程进度奖励",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
610291,
722101,
711538,
404800,
620888,
630518,
711536,
711537,
722102,
722103,
711536,
711537,
203002,
203003,
203001,
219000,
3541
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
100
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
2367000,
2367000,
2367000,
550000,
75000,
75000,
75000,
75000,
75000,
75000,
75000,
75000,
1,
1,
1,
1749000,
1
}
},
animationNtf = 1
},
animationParam = {
"UI_Cup_TreasureChest",
"3"
}
},
[320158] = {
id = 320158,
effect = true,
quality = 3,
name = "随机礼包",
desc = "打开后有机会获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_Palette",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
4,
200006,
200006,
200008,
200008
},
itemNums = {
1000,
2,
1,
2,
1
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
1,
1,
1,
1
}
}
}
},
[320159] = {
id = 320159,
effect = true,
quality = 2,
name = "峡谷宝箱",
desc = "打开峡谷宝箱，可随机获得一定数量的峡谷币",
icon = "CDN:CT_Arena_Icon_Coin_010",
getWay = v1,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3541,
3541,
3541,
3541
},
itemNums = {
50,
30,
20,
10
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
20,
50,
25
}
}
}
},
[320160] = {
id = 320160,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.88.153",
quality = 3,
name = "冻冻宝藏",
desc = "获得后自动打开，获得以下奖励的一项：冰冻手雷: 炸弹猫（3日）*1；大王排位组队升星券*1；大王排位升星券*1；缤纷市集稀有包*1；缤纷市集炫彩包*1；星宝印章*500",
icon = "CDN:T_Common_Item_System_BagBig_012",
getWay = "大王每日任务获得",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3201041,
203013,
203006,
290022,
290021,
4
},
itemNums = {
1,
1,
1,
1,
1,
500
},
expireDays = {
3,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
30,
20,
20,
100,
200,
800
}
}
}
},
[320161] = {
id = 320161,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.88.153",
quality = 3,
name = "龙龙宝藏",
desc = "获得后自动打开，获得以下奖励的一项：压龙大仙：桃花（3日）*1；大王排位组队升星券*1；大王排位升星券*1；缤纷市集稀有包*1；缤纷市集炫彩包*1；星宝印章*500",
icon = "CDN:T_Common_Item_System_BagBig_012",
getWay = "大王每日任务获得",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3202042,
203013,
203006,
290022,
290021,
4
},
itemNums = {
1,
1,
1,
1,
1,
500
},
expireDays = {
3,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
30,
20,
20,
100,
800,
200
}
}
}
},
[320162] = {
id = 320162,
effect = true,
type = "ItemType_AutoUse",
lowVer = "1.3.99.1",
quality = 3,
name = "暑期快闪随机礼盒",
desc = "开启后随机获得以下奖励其一",
icon = "CDN:T_Common_Item_System_WerewolfBag_General01",
getWay = "暑期快闪活动获得",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
203006,
203012,
203005,
200635,
203008,
203009,
203010,
711026,
203011,
203002,
203007,
6,
200635,
200635
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
25,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
8192,
4096,
2048,
1024,
512,
256,
128,
64,
32,
16,
8,
4,
2,
1
},
guaranteeItemIds = {
711026
},
guaranteeTimes = {
10
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
},
minDrawItemIds = {
711026
},
minDraw = {
6
}
}
}
}
}

local mt = {
effect = false,
type = "ItemType_Package",
stackedNum = 999,
quality = 1,
bagId = 1,
useType = "IUTO_GiftPackage"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data