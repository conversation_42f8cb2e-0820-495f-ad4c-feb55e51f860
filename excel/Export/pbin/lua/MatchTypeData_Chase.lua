--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_Chase.xlsx: 玩法

local data = {
[350] = {
id = 350,
modeID = 3,
desc = "大王别抓我",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 3,
thumbImage = "CDN:T_ModelSelect_Img_Type_354",
image = "CDN:T_ModelSelectLarge_Img_Type_354",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "350",
categoryDescription = "非对称",
modeGroup = "350|351|352|353|354",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 350,
TeamMatchGame = true,
battlePlayerNum = 5,
dropId = 29,
mmrType = "MST_StarChase",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：戴上耳机，氛围感立刻提升满满！",
descShort = "星宝的时空探险，搜寻星路仪开启回家的门，被暗星大王抓到会被放逐到时空漩涡！",
battleRecordStyle = "UI_MCG_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_DWBZW",
pakGroup = 50001,
recordType = 350,
buttonDesc = "单王",
gameTypeId = 100,
layoutID = 3,
isShowEmotionEntrance = 2,
isDisableWarmRoundWhenMultiplayer = 1,
randomSideType = 1,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
AutoDownloadPakGroup = {
50001
},
AutoDeletePakGroup = {
50001
},
PakPlayID = 201,
playName = "CHASE",
playTagArr = {
"生化"
},
playShow = "逃脱",
SimpleModelMatchDesc = "竞技",
useDynamicCfg = true,
jumpRandomLevelSequence = true,
isEnableAiLabChaseWarmRound = true,
aiLabChaseWarmRoundRoomInfoId = 11350,
aiLabChaseWarmRoundMatchRuleId = 11350,
battleDetailStyle = "System.InLevel.UI_CHASE.UI_Chase_GameDetail",
isDisableRoomSideFakeRandomSelect = 1
},
[351] = {
id = 351,
modeID = 3,
desc = "大王别抓我",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 3,
thumbImage = "CDN:T_ModelSelect_Img_Type_354",
image = "CDN:T_ModelSelectLarge_Img_Type_354",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "350",
categoryDescription = "非对称",
modeGroup = "350|351|352|353|354",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 351,
TeamMatchGame = true,
battlePlayerNum = 9,
dropId = 29,
mmrType = "MST_StarChase",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：戴上耳机，氛围感立刻提升满满！",
descShort = "星宝的时空探险，搜寻星路仪开启回家的门，被暗星大王抓到会被放逐到时空漩涡！",
battleRecordStyle = "UI_MCG_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_DWBZW",
pakGroup = 50001,
recordType = 350,
buttonDesc = "大小王",
gameTypeId = 100,
layoutID = 3,
isShowEmotionEntrance = 2,
randomSideType = 1,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
AutoDownloadPakGroup = {
50001
},
AutoDeletePakGroup = {
50001
},
PakPlayID = 201,
playName = "CHASE",
playTagArr = {
"生化"
},
playShow = "逃脱",
SimpleModelMatchDesc = "竞技",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_CHASE.UI_Chase_GameDetail",
isDisableRoomSideFakeRandomSelect = 1
},
[352] = {
id = 352,
modeID = 3,
desc = "大王别抓我",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 3,
thumbImage = "CDN:T_ModelSelect_Img_Type_354",
image = "CDN:T_ModelSelectLarge_Img_Type_354",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "350",
categoryDescription = "非对称",
modeGroup = "350|351|352|353|354",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 352,
TeamMatchGame = true,
battlePlayerNum = 5,
dropId = 29,
mmrType = "MST_StarChaseDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：戴上耳机，氛围感立刻提升满满！",
descShort = "星宝的时空探险，搜寻星路仪开启回家的门，被暗星大王抓到会被放逐到时空漩涡！",
battleRecordStyle = "UI_MCG_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_DWBZW",
pakGroup = 50001,
recordType = 350,
buttonDesc = "排位",
gameTypeId = 100,
layoutID = 3,
isShowEmotionEntrance = 2,
isDisableWarmRoundWhenMultiplayer = 1,
randomSideType = 1,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
AutoDownloadPakGroup = {
50001
},
AutoDeletePakGroup = {
50001
},
PakPlayID = 201,
playName = "CHASE",
playTagArr = {
"生化"
},
playShow = "逃脱",
SimpleModelMatchDesc = "竞技",
useDynamicCfg = true,
jumpRandomLevelSequence = true,
isEnableAiLabChaseWarmRound = true,
aiLabChaseWarmRoundRoomInfoId = 11352,
aiLabChaseWarmRoundMatchRuleId = 11352,
battleDetailStyle = "System.InLevel.UI_CHASE.UI_Chase_GameDetail",
isDisableRoomSideFakeRandomSelect = 1
},
[353] = {
id = 353,
modeID = 3,
desc = "大王别抓我",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 3,
thumbImage = "CDN:T_ModelSelect_Img_Type_354",
image = "CDN:T_ModelSelectLarge_Img_Type_354",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "350",
categoryDescription = "非对称",
modeGroup = "350|351|352|353|354",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 353,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 29,
mmrType = "MST_StarChase",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：戴上耳机，氛围感立刻提升满满！",
descShort = "星宝的时空探险，搜寻星路仪开启回家的门，被暗星大王抓到会被放逐到时空漩涡！",
battleRecordStyle = "UI_MCG_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_DWBZW",
pakGroup = 50001,
recordType = 350,
buttonDesc = "三王",
gameTypeId = 100,
layoutID = 3,
isShowEmotionEntrance = 2,
isDisableWarmRoundWhenMultiplayer = 1,
randomSideType = 1,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
AutoDownloadPakGroup = {
50001
},
AutoDeletePakGroup = {
50001
},
PakPlayID = 201,
playName = "CHASE",
playTagArr = {
"生化"
},
playShow = "逃脱",
SimpleModelMatchDesc = "竞技",
useDynamicCfg = true,
jumpRandomLevelSequence = true,
isEnableAiLabChaseWarmRound = true,
aiLabChaseWarmRoundRoomInfoId = 11353,
aiLabChaseWarmRoundMatchRuleId = 11353,
aiLabChaseWarmRoundDarkStarRoomInfoId = 12353,
aiLabChaseWarmRoundDarkStarMatchRuleId = 12353,
battleDetailStyle = "System.InLevel.UI_CHASE.UI_Chase_GameDetail",
isDisableRoomSideFakeRandomSelect = 1
},
[354] = {
id = 354,
modeID = 3,
desc = "大王别抓我",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 3,
thumbImage = "CDN:T_ModelSelect_Img_Type_354",
image = "CDN:T_ModelSelectLarge_Img_Type_354",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "350",
categoryDescription = "非对称",
modeGroup = "350|351|352|353|354",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 354,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 29,
mmrType = "MST_StarChaseDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：戴上耳机，氛围感立刻提升满满！",
descShort = "星宝的时空探险，搜寻星路仪开启回家的门，被暗星大王抓到会被放逐到时空漩涡！",
battleRecordStyle = "UI_MCG_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_DWBZW",
pakGroup = 50001,
recordType = 350,
buttonDesc = "三王",
gameTypeId = 100,
layoutID = 3,
isShowEmotionEntrance = 2,
isDisableWarmRoundWhenMultiplayer = 1,
randomSideType = 1,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
AutoDownloadPakGroup = {
50001
},
AutoDeletePakGroup = {
50001
},
PakPlayID = 201,
playName = "CHASE",
playTagArr = {
"生化"
},
playShow = "逃脱",
SimpleModelMatchDesc = "竞技",
useDynamicCfg = true,
jumpRandomLevelSequence = true,
isEnableAiLabChaseWarmRound = true,
aiLabChaseWarmRoundRoomInfoId = 11354,
aiLabChaseWarmRoundMatchRuleId = 11354,
aiLabChaseWarmRoundDarkStarRoomInfoId = 12354,
aiLabChaseWarmRoundDarkStarMatchRuleId = 12354,
battleDetailStyle = "System.InLevel.UI_CHASE.UI_Chase_GameDetail",
isDisableRoomSideFakeRandomSelect = 1
}
}

local mt = {
isPermanent = false,
TeamMatchGame = false,
NeedShowProcessDisplay = false,
isShowBattleRecord = false,
bShowTeamEnter = false,
useDynamicCfg = false,
jumpRandomLevelSequence = false,
isEnableAiLabChaseWarmRound = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data