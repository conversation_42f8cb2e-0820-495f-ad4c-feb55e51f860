--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_nr3e.xlsx: 玩法

local data = {
[103] = {
id = 103,
modeID = 3,
desc = "卧底行动",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 13,
thumbImage = "CDN:T_ModelSelect_Img_Type_50201_png",
image = "CDN:T_ModelSelectLarge_Img_Type_50201",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "103",
categoryDescription = "谁是卧底",
modeGroup = "131|103",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 103,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 7,
mmrType = "MST_DollsRun",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
descShort = "一场星宝与卧底的智斗游戏，智谋与策略大检验！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_WoDiXingDong",
pakGroup = 20002,
recordType = 103,
buttonDesc = "休闲",
gameTypeId = 18,
layoutID = 1,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 10017,
videoLinkId = 103,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
AutoDownloadPakGroup = {
20002
},
AutoDeletePakGroup = {
20002
},
PakPlayID = 102,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "卧底",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E2.GameDetailsInfo.UI_NR3E2_GameDetails"
},
[104] = {
id = 104,
modeID = 3,
desc = "躲猫猫",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 7,
thumbImage = "CDN:T_ModelSelect_Img_Type_50101_png",
image = "CDN:T_ModelSelectLarge_Img_Type_50101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "104",
categoryDescription = "捉迷藏",
modeGroup = "141|104",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 104,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 8,
mmrType = "MST_HideAndSeek",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
descShort = "惊心动魄的伪装与搜寻，嘘，别出声！",
warmDropId = 34,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_DuoMaoMao",
pakGroup = 20001,
recordType = 104,
buttonDesc = "休闲",
gameTypeId = 19,
layoutID = 1,
isShowEmotionEntrance = 1,
videoLinkId = 104,
randomSideType = 2,
detailLinkId = 241,
detailLinkDesc = "备战",
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
detailLinkRedDot = 320,
secondaryNewbieWarmRoundRoomInfoId = 10021,
AutoDownloadPakGroup = {
20001
},
AutoDeletePakGroup = {
20001
},
PakPlayID = 101,
playName = "NR3E",
detailLinkIcon = "T_E1_Prepare_Icon_Entrance_001",
playTagArr = {
"躲藏"
},
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E1.GameDetailsInfo.UI_NR3E1_GameDetails"
},
[105] = {
id = 105,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "151|105|106|109|112|113",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
unlockRule = "请到移动端游玩",
battleRecordCnt = 30,
matchRuleId = 105,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "8人",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
ComplicatedPlay = 1,
secondaryNewbieWarmRoundRoomInfoId = 10022,
bSkipFlyEnter = true,
previewShowIcon = "T_Lobby_lmg_PrepareBtn",
previewShowName = "备战",
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[106] = {
id = 106,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 1,
conditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 150,
value = 151
},
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "151|105|106|109|112|113",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "完成5次匹配对局",
battleRecordCnt = 30,
matchRuleId = 106,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "12人(单人)",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
LowestVersion = "********",
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
ComplicatedPlay = 1,
bSkipFlyEnter = true,
previewShowIcon = "T_Lobby_lmg_PrepareBtn",
previewShowName = "备战",
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[107] = {
id = 107,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "107|108|110|111|114",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 107,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "12人",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[108] = {
id = 108,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "107|108|110|111|114",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 108,
TeamMatchGame = true,
battlePlayerNum = 15,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "15人",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[109] = {
id = 109,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 2,
conditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 150,
value = 151
},
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "151|105|106|109|112|113",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2
},
teamTag = "1",
unlockRule = "完成3次匹配对局",
battleRecordCnt = 30,
matchRuleId = 109,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "10人(双人)",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
LowestVersion = "********",
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
ComplicatedPlay = 1,
bSkipFlyEnter = true,
previewShowIcon = "T_Lobby_lmg_PrepareBtn",
previewShowName = "备战",
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[110] = {
id = 110,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "107|108|110|111|114",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 107,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "8人",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[111] = {
id = 111,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "107|108|110|111|114",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 108,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "10人",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[112] = {
id = 112,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 150,
value = 151
},
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "151|105|106|109|112|113",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2
},
teamTag = "1",
unlockRule = "完成5次匹配对局",
battleRecordCnt = 30,
matchRuleId = 112,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "秘境寻宝",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
ComplicatedPlay = 1,
bSkipFlyEnter = true,
previewShowIcon = "T_Lobby_lmg_PrepareBtn",
previewShowName = "备战",
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[113] = {
id = 113,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 150,
value = 151
},
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
},
isPermanent = true,
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "151|105|106|109|112|113",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
unlockRule = "完成5次匹配对局",
battleRecordCnt = 30,
matchRuleId = 113,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "10人",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
LowestVersion = "*******",
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
ComplicatedPlay = 1,
bSkipFlyEnter = true,
previewShowIcon = "T_Lobby_lmg_PrepareBtn",
previewShowName = "备战",
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[131] = {
id = 131,
modeID = 3,
desc = "卧底行动",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 13,
thumbImage = "CDN:T_ModelSelect_Img_Type_50201_png",
image = "CDN:T_ModelSelectLarge_Img_Type_50201",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "103",
categoryDescription = "谁是卧底",
modeGroup = "131|103",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 131,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 7,
mmrType = "MST_DollsRunDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
descShort = "一场星宝与卧底的智斗游戏，智谋与策略大检验！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_WoDiXingDong",
pakGroup = 20002,
recordType = 103,
buttonDesc = "排位",
gameTypeId = 18,
layoutID = 1,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 10017,
LowestVersion = " ********",
videoLinkId = 103,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
AutoDownloadPakGroup = {
20002
},
AutoDeletePakGroup = {
20002
},
PakPlayID = 102,
playName = "NR3E",
playTagArr = {
"狼人"
},
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E2.GameDetailsInfo.UI_NR3E2_GameDetails"
},
[141] = {
id = 141,
modeID = 3,
desc = "躲猫猫",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 7,
thumbImage = "CDN:T_ModelSelect_Img_Type_50101_png",
image = "CDN:T_ModelSelectLarge_Img_Type_50101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "104",
categoryDescription = "捉迷藏",
modeGroup = "141|104",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 141,
TeamMatchGame = true,
battlePlayerNum = 12,
dropId = 8,
mmrType = "MST_HideAndSeekDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
descShort = "惊心动魄的伪装与搜寻，嘘，别出声！",
warmDropId = 34,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResult",
outImage = "T_Lobby_Start_DuoMaoMao",
pakGroup = 20001,
recordType = 104,
buttonDesc = "排位",
gameTypeId = 19,
layoutID = 1,
isShowEmotionEntrance = 1,
videoLinkId = 104,
randomSideType = 2,
detailLinkId = 241,
detailLinkDesc = "备战",
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
detailLinkRedDot = 320,
secondaryNewbieWarmRoundRoomInfoId = 10021,
AutoDownloadPakGroup = {
20001
},
AutoDeletePakGroup = {
20001
},
PakPlayID = 101,
playName = "NR3E",
detailLinkIcon = "T_E1_Prepare_Icon_Entrance_001",
playTagArr = {
"躲藏"
},
playShow = "躲猫猫",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E1.GameDetailsInfo.UI_NR3E1_GameDetails"
},
[151] = {
id = 151,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 39,
value = 5,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "151|105|106|109|112|113",
settleProc = "MTSC_Camp",
matchTeamNum = {
1
},
teamTag = "1",
unlockRule = "在匹配对局中获胜5次",
battleRecordCnt = 30,
matchRuleId = 151,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 14,
mmrType = "MST_WerewolfDegree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRankMixture",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
warmDropId = 33,
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "10人(排位)",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
overrideButton = "单人",
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
ComplicatedPlay = 1,
bSkipFlyEnter = true,
previewShowIcon = "T_Lobby_lmg_PrepareBtn",
previewShowName = "备战",
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[114] = {
id = 114,
modeID = 3,
desc = "谁是狼人",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_50301",
image = "CDN:T_ModelSelectLarge_Img_Type_50301",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "105",
categoryDescription = "狼人杀",
modeGroup = "107|108|110|111|114",
settleProc = "MTSC_Camp",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 108,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 14,
mmrType = "MST_Werewolf",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
descShort = "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_RankSideBattleResultNoTip",
outImage = "T_Lobby_Start_ShuiShiLangRen",
pakGroup = 20003,
recordType = 105,
buttonDesc = "10人",
gameTypeId = 20,
layoutID = 1,
isShowEmotionEntrance = 2,
videoLinkId = 105,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
20003
},
AutoDeletePakGroup = {
20003
},
PakPlayID = 103,
playName = "NR3E",
playTagArr = {
"狼人"
},
playShow = "狼人杀",
SimpleModelMatchDesc = "休闲",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.UI_NR3E3.GameDetailsInfo.UI_NR3E3_GameDetails"
},
[188] = {
id = 188,
modeID = 3,
desc = "E8-RICH",
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_188",
image = "CDN:T_ModelSelectLarge_Img_Type_188",
showType = "UI_Model_SingleMatchTypeItem",
matchTeamNum = {
1
},
descShort = "获得金币，然后建造你的城市",
outImage = "T_Lobby_Start_Rich",
pakGroup = 20004,
buttonDesc = "进入",
gameTypeId = 88,
layoutID = 1,
isShowEmotionEntrance = 2,
linkID = 888,
AutoDownloadPakGroup = {
20004
},
AutoDeletePakGroup = {
20004
},
PakPlayID = 104,
playName = "NR3E"
}
}

local mt = {
isPermanent = false,
TeamMatchGame = false,
NeedShowProcessDisplay = false,
isShowBattleRecord = false,
bShowTeamEnter = false,
jumpRandomLevelSequence = false,
bSkipFlyEnter = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data