--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表.xlsx: 货币

local v0 = "JumpToast_common"

local v1 = {
{
itemId = 4,
itemNum = 1
}
}

local data = {
[15] = {
id = 15,
effect = true,
quality = 2,
name = "珍宝币",
desc = "用来在【我要暴富】玩法中购买商品。",
icon = "T_E8_Common_Icon_RichCoin_01",
getWay = "充值",
bHideInBag = true,
jumpToast = v0
},
[3402] = {
id = 3402,
effect = true,
expiredReplaceItem = v1,
name = "兔兔玩偶",
desc = "用于在农场住新居中兑换奖励",
bHideInBag = true,
jumpToast = v0
},
[3451] = {
id = 3451,
effect = true,
expiredReplaceItem = v1,
name = "集合哨音",
desc = "用于在晋级冲冲冲活动中兑换奖励",
icon = "T_Common_Icon_Coin_50",
getWay = "完成晋级冲冲冲任务获得",
bHideInBag = true
},
[3452] = {
id = 3452,
effect = true,
expiredReplaceItem = v1,
name = "魔力舞鞋",
desc = "用于在周年返场礼活动中兑换奖励",
icon = "T_Common_Item_DancingShoeBig_01",
getWay = "完成周年返场礼任务获得",
bHideInBag = true
},
[3601] = {
id = 3601,
effect = true,
expiredReplaceItem = v1,
name = "雪花币",
desc = "用于兑换周年送英雄活动奖励。",
icon = "T_Common_Item_System_Bag_043",
getWay = "限时活动获得",
bHideInBag = true
},
[3455] = {
id = 3455,
effect = true,
expiredReplaceItem = v1,
name = "棒棒糖",
desc = "用于在庆典不停歇活动中兑换奖励",
icon = "T_Common_Icon_Coin_19",
getWay = "完成庆典不停歇任务获得",
bHideInBag = true
},
[3456] = {
id = 3456,
effect = true,
expiredReplaceItem = v1,
name = "棒棒糖",
desc = "用于在庆典不停歇活动中兑换奖励",
icon = "T_Common_Icon_Coin_19",
getWay = "完成庆典不停歇任务获得",
bHideInBag = true
},
[3457] = {
id = 3457,
effect = true,
expiredReplaceItem = v1,
name = "小糖心",
desc = "可用于在吉星好运树中兑换奖励",
icon = "T_Common_Icon_Token_Christmas",
getWay = "完成吉星好运树任务获得",
bHideInBag = true
},
[3458] = {
id = 3458,
effect = true,
expiredReplaceItem = v1,
name = "吉星摇树券",
desc = "可用于在吉星好运树中抽取奖励",
icon = "T_Common_Item_System_Card_Christmas",
getWay = "完成吉星好运树任务获得",
bHideInBag = true
},
[3459] = {
id = 3459,
effect = true,
expiredReplaceItem = v1,
name = "舞动能量",
desc = "用于在一起跳舞吧活动中兑换奖励",
icon = "T_Common_Icon_Coin_98",
getWay = "完成一起跳舞吧任务",
bHideInBag = true,
jumpToast = v0
},
[3450] = {
id = 3450,
effect = true,
expiredReplaceItem = v1,
name = "应援棒",
desc = "用于在阵营对决活动中兑换奖励",
icon = "T_Common_Item_CheerStickBig_01",
getWay = "完成阵营对决任务",
bHideInBag = true,
jumpToast = v0
},
[3453] = {
id = 3453,
effect = true,
expiredReplaceItem = v1,
name = "免肝券测试",
desc = "免肝券测试",
icon = "T_Common_Item_System_Farm_01",
getWay = "完成周年返场礼任务",
bHideInBag = true
},
[3602] = {
id = 3602,
effect = true,
name = "新春签到补签次数",
desc = "可用于新春天天送活动中补签",
icon = "T_Common_Item_System_Bag_108",
getWay = "可用于新春天天送活动中补签",
bHideInBag = true,
jumpToast = v0
},
[3603] = {
id = 3603,
effect = true,
name = "双人成团合柿币",
desc = "双人成团端内版任务奖励的兑换币",
icon = "CDN:T_Common_Item_System_Bag_043",
getWay = "可用于双人成团中解锁线性奖励",
bHideInBag = true,
jumpToast = v0
},
[3608] = {
id = 3608,
effect = true,
quality = 1,
name = "峡谷之心",
desc = "用于在祈愿活动中兑换峡谷英雄，祈愿活动结束后不会清空",
icon = "CDN:CT_Arena_Item_CanyonCrystal",
getWay = "参与峡谷英雄祈愿",
sort = 3,
useType = "IUTO_JumpPanel",
useParam = {
10711
},
jumpToast = v0
},
[3610] = {
id = 3610,
effect = true,
name = "晋级礼活动经验值",
desc = "用于提升【天天晋级礼】或【喵喵晋级礼】的活动等级",
icon = "T_Common_Item_System_Bag_107",
getWay = "天天晋级赛活动任务",
bHideInBag = true,
jumpToast = v0
},
[3620] = {
id = 3620,
effect = true,
expiredReplaceItem = v1,
name = "新春特饮",
desc = "用于在新年送英雄活动中获取奖励。",
icon = "T_Common_Item_ColaBig_01",
getWay = "限时活动获得",
bHideInBag = true
},
[3460] = {
id = 3460,
effect = true,
name = "花花",
desc = "用于兑换雨琦试衣间活动奖励。",
icon = "T_Exchange_Icon_001",
getWay = "完成雨琦试衣间活动任务可",
bHideInBag = true,
jumpToast = v0
},
[3461] = {
id = 3461,
effect = true,
expiredReplaceItem = v1,
name = "幸运饼干",
desc = "用于在福利抢先知活动中兑换奖励",
icon = "T_Common_Item_System_Bag_095",
getWay = "完成福利抢先知任务",
jumpToast = v0
},
[3462] = {
id = 3462,
effect = true,
expiredReplaceItem = v1,
name = "对决能量",
desc = "可用于在地铁跑酷联动中兑换奖励",
icon = "T_Common_Icon_Entertament",
getWay = "完成地铁跑酷联动任务获得",
bHideInBag = true,
jumpToast = v0
},
[3463] = {
id = 3463,
effect = true,
expiredReplaceItem = v1,
name = "友情券",
desc = "可用于在携友同行活动中抽取奖励",
icon = "T_Common_Icon_Token_Return",
getWay = "完成携友同行任务获得",
bHideInBag = true,
jumpToast = v0
},
[3464] = {
id = 3464,
effect = true,
expiredReplaceItem = v1,
name = "星福糕点",
desc = "用于在冰雪奇妙旅中前进",
icon = "T_Common_Item_CookieBig_01",
getWay = "完成冰雪奇妙旅任务获得",
bHideInBag = true,
jumpToast = v0
},
[3465] = {
id = 3465,
effect = true,
expiredReplaceItem = v1,
name = "遗迹物资",
desc = "用于在大王星玩法活动中兑换奖励",
icon = "T_Common_Icon_Token_Mayday",
getWay = "完成大王星玩法任务获得",
bHideInBag = true,
jumpToast = v0
},
[3467] = {
id = 3467,
effect = true,
name = "甜蜜糖芯",
desc = "用于在三丽鸥家族活动中兑换奖励",
icon = "T_Common_Icon_Coin_19",
getWay = "完成三丽鸥家族任务获得",
bHideInBag = true,
jumpToast = v0
},
[3468] = {
id = 3468,
effect = true,
name = "千响春愿",
desc = "用于在过个元梦年活动中兑换奖励",
icon = "T_Common_Icon_Coin_36",
getWay = "完成过个元梦年任务获得",
bHideInBag = true,
jumpToast = v0
},
[3469] = {
id = 3469,
effect = true,
expiredReplaceItem = v1,
name = "爱心饲料",
desc = "用于在牧场物语活动中开启图鉴",
icon = "T_Common_Item_System_Farm_01",
getWay = "完成牧场物语任务",
bHideInBag = true
},
[3476] = {
id = 3476,
effect = true,
name = "贴纸铺消耗品占坑",
desc = "用于在三丽鸥家族活动中兑换奖励",
icon = "T_Common_Icon_Coin_19",
getWay = "完成三丽鸥家族任务获得",
bHideInBag = true,
jumpToast = v0
},
[3470] = {
id = 3470,
expiredReplaceItem = {
{
itemId = 200632,
itemNum = 1
}
},
quality = 2,
name = "水精灵贴纸",
desc = "用于在福利任务站活动中兑换奖励",
icon = "T_ReturnFram_Img_Badge1",
picture = "1",
getWay = "完成福利任务站任务获得",
bHideInBag = true,
jumpToast = v0
},
[3471] = {
id = 3471,
expiredReplaceItem = {
{
itemId = 200632,
itemNum = 1
}
},
quality = 2,
name = "小柯基贴纸",
desc = "用于在福利任务站活动中兑换奖励",
icon = "T_ReturnFram_Img_Badge2",
picture = "1",
getWay = "完成福利任务站任务获得",
bHideInBag = true,
jumpToast = v0
},
[3472] = {
id = 3472,
expiredReplaceItem = {
{
itemId = 200632,
itemNum = 1
}
},
quality = 2,
name = "精灵商人贴纸",
desc = "用于在福利任务站活动中兑换奖励",
icon = "T_ReturnFram_Img_Badge3",
picture = "1",
getWay = "完成福利任务站任务获得",
bHideInBag = true,
jumpToast = v0
},
[3473] = {
id = 3473,
expiredReplaceItem = {
{
itemId = 200632,
itemNum = 1
}
},
quality = 2,
name = "小红狐贴纸",
desc = "用于在福利任务站活动中兑换奖励",
icon = "T_ReturnFram_Img_Badge4",
picture = "1",
getWay = "完成福利任务站任务获得",
bHideInBag = true,
jumpToast = v0
},
[3474] = {
id = 3474,
expiredReplaceItem = {
{
itemId = 200632,
itemNum = 1
}
},
quality = 2,
name = "云精灵贴纸",
desc = "用于在福利任务站活动中兑换奖励",
icon = "T_ReturnFram_Img_Badge5",
picture = "1",
getWay = "完成福利任务站任务获得",
bHideInBag = true,
jumpToast = v0
},
[3700] = {
id = 3700,
effect = true,
expiredReplaceItem = v1,
name = "新春霸福券",
desc = "用于在新春霸福节活动中抽取奖励",
icon = "T_Common_Icon_Token_NewyearTicket",
getWay = "春节抽奖代币占位",
bHideInBag = true
},
[3710] = {
id = 3710,
effect = true,
expiredReplaceItem = v1,
name = "福运灯笼",
desc = "用于在新春霸福节活动中兑换奖励",
icon = "T_Common_Icon_Token_Lantern",
getWay = "完成新春霸福节活动获得",
bHideInBag = true
},
[3800] = {
id = 3800,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "星愿币兑换券",
desc = "可在卡牌集换系统中兑换限时星愿币（限定【田园牧歌】卡册的收集有效期内兑换）",
icon = "T_Common_Item_System_Card_Coin_01",
getWay = "卡牌集换中通过集换齐各个卡册获得",
sort = 53,
useType = "IUTO_JumpPanel",
useParam = {
902
}
},
[3801] = {
id = 3801,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "星愿币兑换券",
desc = "可在卡牌集换系统中兑换限时星愿币（限定【缤纷市集】卡册的收集有效期内兑换）",
icon = "T_Common_Item_System_Card_Coin_01",
getWay = "卡牌集换中通过集换齐各个卡册获得",
sort = 54,
useType = "IUTO_JumpPanel",
useParam = {
902
}
},
[3475] = {
id = 3475,
effect = true,
expiredReplaceItem = v1,
name = "冬日礼盒",
desc = "用于在冬日补给站活动中兑换奖励",
icon = "T_Common_Item_GiftBoxBig_03",
picture = "1",
getWay = "完成冬日补给站任务获得",
bHideInBag = true
},
[3477] = {
id = 3477,
effect = true,
expiredReplaceItem = v1,
name = "心心应援棒",
desc = "用于在阵营对决第二期活动中兑换奖励",
icon = "T_Common_Item_CheerStickBig_02",
getWay = "完成阵营对决第二期活动任务",
jumpToast = v0
},
[3478] = {
id = 3478,
effect = true,
name = "妁情玫瑰",
desc = "用于在暖爱陪伴活动中兑换奖励",
icon = "T_Common_Icon_Coin_38",
getWay = "完成暖爱陪伴任务",
bHideInBag = true,
jumpToast = v0
},
[3479] = {
id = 3479,
effect = true,
quality = 2,
name = "桃花",
desc = "收集一定数量桃花即可在桃源万千祈愿中领取奖励，祈愿活动结束后清空",
icon = "T_Common_Item_System_Bag_119",
getWay = "参与桃源万千祈愿获得",
jumpId = {
8018
},
jumpToast = v0
},
[3481] = {
id = 3481,
effect = true,
name = "宝藏指南针",
desc = "用于在松弛春日季活动中兑换奖励",
icon = "T_Common_Icon_Token_Compass",
getWay = "完成松弛春日季任务",
jumpToast = v0
},
[3482] = {
id = 3482,
effect = true,
name = "花花",
desc = "用于在暖春赠礼活动中兑换奖励",
icon = "T_Exchange_Icon_001",
getWay = "完成暖春赠礼活动任务",
jumpToast = v0
},
[3483] = {
id = 3483,
effect = true,
expiredReplaceItem = v1,
name = "寻宝图章",
desc = "用于在游园寻宝活动中参与寻宝",
icon = "T_Common_Icon_Coin_77",
getWay = "完成游园寻宝任务",
bHideInBag = true
},
[3466] = {
id = 3466,
effect = true,
expiredReplaceItem = v1,
name = "蝴蝶币",
desc = "用于在默契星搭子活动中兑换奖励",
icon = "CDN:T_Common_Icon_Token_Butterfly",
getWay = "完成默契星搭子任务",
bHideInBag = true,
jumpToast = v0
},
[3480] = {
id = 3480,
expiredReplaceItem = v1,
name = "幸运飞镖",
desc = "用于在陷阱狼来袭活动中兑换奖励",
icon = "T_Common_Item_DartBig_01",
picture = "1",
getWay = "完成陷阱狼来袭任务",
jumpToast = v0
},
[3485] = {
id = 3485,
effect = true,
expiredReplaceItem = v1,
name = "魔核",
desc = "用于在外观系统上线活动中兑换奖励",
icon = "T_Common_Icon_112",
getWay = "完成外观系统上线活动任务获得",
jumpToast = v0
},
[3486] = {
id = 3486,
effect = true,
expiredReplaceItem = v1,
name = "充能饼干",
desc = "用于在萝萝星愿礼活动中参与寻宝",
icon = "T_Common_Item_System_Bag_095",
getWay = "完成萝萝星愿礼任务"
},
[3487] = {
id = 3487,
effect = true,
expiredReplaceItem = v1,
name = "超凡能量",
desc = "用于在超凡特攻季活动中兑换奖励",
icon = "T_Common_Item_Energiser_01",
getWay = "完成超凡特攻季任务"
},
[3488] = {
id = 3488,
effect = true,
name = "春日纸鸢",
desc = "用于在悠闲春日季活动中兑换奖励",
icon = "T_Common_Icon_Coin_66",
getWay = "完成悠闲春日季任务",
jumpToast = v0
},
[3621] = {
id = 3621,
effect = true,
expiredReplaceItem = v1,
name = "乐园游戏机",
desc = "用于在玩法大更新活动中兑换奖励",
icon = "T_Common_Icon_Entertament",
getWay = "完成玩法大更新任务",
jumpToast = v0
},
[3489] = {
id = 3489,
effect = true,
expiredReplaceItem = v1,
name = "报告铃铛",
desc = "用于在狼人播报站活动中兑换奖励",
icon = "T_Common_Item_WerewolfBell_01",
getWay = "完成玩法大更新任务",
jumpToast = v0
},
[3491] = {
id = 3491,
effect = true,
name = "幌金绳",
desc = "用于在身份专精活动中兑换奖励",
icon = "T_Common_Item_System_Bag_127",
getWay = "完成身份专精活动任务获得",
jumpToast = v0
},
[3492] = {
id = 3492,
effect = true,
name = "春日时刻",
desc = "用于在元气出游记活动中兑换奖励",
icon = "T_Common_Icon_Coin_49",
getWay = "完成元气出游记任务",
jumpToast = v0
},
[3622] = {
id = 3622,
effect = true,
expiredReplaceItem = v1,
name = "游戏机",
desc = "用于在玩法游乐园活动中兑换奖励",
icon = "T_Common_Icon_Entertament",
getWay = "完成玩法游乐园任务",
bHideInBag = true,
jumpToast = v0
},
[3493] = {
id = 3493,
effect = true,
name = "如愿纸鹤",
desc = "用于在福利抢先知活动中兑换奖励",
icon = "T_Common_Item_System_Bag_085",
getWay = "完成福利抢先知任务",
bHideInBag = true,
jumpToast = v0
},
[3494] = {
id = 3494,
effect = true,
name = "如愿纸鹤",
desc = "用于在福利抢先知活动中兑换奖励",
icon = "T_Common_Item_System_Bag_085",
getWay = "完成福利抢先知任务",
bHideInBag = true,
jumpToast = v0
},
[3495] = {
id = 3495,
effect = true,
name = "胜利果实",
desc = "用于在晋级赛狂欢活动中兑换奖励",
icon = "T_Common_Item_System_Bag_086",
getWay = "完成晋级赛狂欢任务",
bHideInBag = true,
jumpToast = v0
},
[3900] = {
id = 3900,
effect = true,
quality = 3,
name = "农场赠礼券",
desc = "用于在奶油乐园奇遇祈愿活动中赠送装扮，祈愿活动结束后不会清空",
icon = "T_Common_Item_System_Bag_139",
getWay = "参与奶油乐园奇遇祈愿",
jumpId = {
599
},
jumpToast = v0
},
[3496] = {
id = 3496,
effect = true,
name = "花灵果实",
desc = "用于在狼人春日行活动中兑换奖励",
icon = "T_Common_Item_WerewolfSlime_01",
getWay = "完成狼人春日行任务",
bHideInBag = true
},
[3497] = {
id = 3497,
effect = true,
name = "集结币",
desc = "用于在精灵谷集结活动中兑换奖励",
icon = "T_Common_Item_RallyCoinBig_01",
getWay = "完成精灵谷集结任务",
bHideInBag = true
},
[3498] = {
id = 3498,
effect = true,
name = "小饼干",
desc = "用于在喵喵节来袭中兑换奖励",
icon = "T_Common_Item_System_Bag_095",
getWay = "完成喵喵节来袭任务",
bHideInBag = true,
jumpToast = v0
},
[3623] = {
id = 3623,
effect = true,
expiredReplaceItem = v1,
name = "棒棒糖",
desc = "用于在玩法游乐园活动中兑换奖励",
icon = "T_Common_Icon_Coin_19",
getWay = "完成玩法游乐园任务",
bHideInBag = true,
jumpToast = v0
},
[3499] = {
id = 3499,
effect = true,
name = "公益科普点",
desc = "用于星宝守护者活动兑换奖励。",
icon = "T_Common_Item_System_Bag_135",
getWay = "用于星宝守护者活动兑换奖励。",
bHideInBag = true,
jumpToast = v0
},
[3512] = {
id = 3512,
effect = true,
name = "冲刺币",
desc = "用于在赛季冲刺周活动中兑换奖励",
icon = "T_Common_Icon_Coin_115",
getWay = "完成赛季冲刺周任务",
bHideInBag = true,
jumpToast = v0
},
[3611] = {
id = 3611,
effect = true,
name = "友谊代币",
desc = "用于提升“天天晋级赛，四人齐上分”的活动进度",
icon = "T_Common_Icon_Coin_76",
getWay = "完成天天晋级赛四人冲分任务",
bHideInBag = true,
jumpToast = v0
},
[3555] = {
id = 3555,
effect = true,
quality = 3,
name = "大王币",
desc = "用于解锁大王别抓我的身份",
icon = "T_Common_Icon_Coin_116",
getWay = "参与大王别抓我玩法",
bHideInBag = true,
jumpToast = v0
},
[3556] = {
id = 3556,
effect = true,
quality = 3,
name = "水粉刷",
desc = "用于开启幻彩宝匣",
icon = "CDN:T_Common_Item_System_Bag_145",
getWay = "参与幻彩调律祈愿",
jumpId = {
88888
},
bHideInBag = true,
jumpToast = v0
},
[3625] = {
id = 3625,
effect = true,
expiredReplaceItem = v1,
name = "晴霜积分",
desc = "用于在晴霜试炼中获取奖励",
icon = "CDN:CT_Arena_Icon_Coin_008",
getWay = "完成晴霜试炼任务",
bHideInBag = true,
jumpToast = v0
},
[3543] = {
id = 3543,
effect = true,
name = "初心金币",
desc = "用于回归手册活动中获取奖励",
icon = "T_Common_Icon_Coin_114",
getWay = "完成回归手册活动任务",
bHideInBag = true,
jumpToast = v0
},
[3544] = {
id = 3544,
effect = true,
name = "喵喵币",
desc = "用于元梦喵喵节活动中获取奖励",
icon = "CDN:T_Common_Icon_Coin_114",
getWay = "完成元梦喵喵节系列活动任务",
bHideInBag = true,
jumpToast = v0
},
[3545] = {
id = 3545,
effect = true,
name = "双味甜筒",
desc = "用于在狼人五重礼活动中兑换奖励",
icon = "T_Common_Item_WerewolfIce_01",
getWay = "完成狼人五重礼任务",
bHideInBag = true,
jumpToast = v0
},
[3626] = {
id = 3626,
effect = true,
expiredReplaceItem = v1,
name = "晴霜视频",
desc = "晴霜视频",
icon = "T_Common_Icon_Coin_19",
getWay = "用于视频展示",
bHideInBag = true,
jumpToast = v0
},
[3546] = {
id = 3546,
effect = true,
name = "樱花",
desc = "用于萌动蜜桃猫活动中兑换奖励",
icon = "CDN:T_Common_Icon_Sakura",
getWay = "完成萌动蜜桃猫活动内相关任务",
bHideInBag = true,
jumpToast = v0
},
[3547] = {
id = 3547,
effect = true,
expiredReplaceItem = v1,
name = "召集能量",
desc = "用于在携友同行商店中兑换奖励",
icon = "T_Common_Icon_Coin_42",
getWay = "参与携友同行活动",
bHideInBag = true,
jumpToast = v0
},
[3777] = {
id = 3777,
effect = true,
expiredReplaceItem = v1,
name = "风雷魔方",
desc = "用于飓风来袭活动中兑换奖励",
icon = "T_Common_Item_System_Bag_146",
getWay = "参与飓风来袭活动",
bHideInBag = true,
jumpToast = v0
},
[3630] = {
id = 3630,
effect = true,
expiredReplaceItem = v1,
name = "绮积分",
desc = "用于在绮的试炼中获取奖励",
icon = "CDN:CT_Arena_Icon_Coin_009",
getWay = "完成绮试炼任务",
bHideInBag = true,
jumpToast = v0
},
[3548] = {
id = 3548,
effect = true,
name = "瑰之誓",
desc = "用于在情定之时活动中兑换奖励",
icon = "CDN:T_Common_Icon_Coin_38",
getWay = "完成情定之时任务",
bHideInBag = true,
jumpToast = v0
},
[3549] = {
id = 3549,
effect = true,
name = "追梦响铃",
desc = "用于在汪汪队长到活动中兑换奖励",
icon = "CDN:T_Common_Icon_Coin_18",
getWay = "完成汪汪队长到任务",
bHideInBag = true,
jumpToast = v0
},
[3560] = {
id = 3560,
effect = true,
expiredReplaceItem = v1,
name = "漫游币",
desc = "用于在星世界漫游卡活动中兑换奖励",
icon = "T_Common_Icon_Coin_42",
getWay = "完成星世界漫游任务",
bHideInBag = true,
jumpToast = v0
},
[3561] = {
id = 3561,
effect = true,
name = "助威投票券",
desc = "用于参与助威闪电赛活动并兑换奖励。",
icon = "CDN:T_Common_Item_System_Bag_050",
getWay = "完成助威闪电赛活动任务",
jumpId = {
713
},
plusjumpId = 713,
bHideInBag = true,
jumpToast = v0
},
[3632] = {
id = 3632,
effect = true,
name = "路漫游哨",
desc = "用于在阿卓来漫游活动中获取奖励",
icon = "cT_Common_Icon_Coin_50",
getWay = "完成阿卓来漫游任务",
bHideInBag = true,
jumpToast = v0
},
[3633] = {
id = 3633,
effect = true,
name = "清凉饮料",
desc = "用于在清凉夏日礼中兑换奖励",
icon = "CDN:T_Common_Item_System_Bag_081",
getWay = "完成清凉夏日礼任务",
bHideInBag = true,
jumpToast = v0
},
[227] = {
id = 227,
effect = true,
quality = 3,
name = "音之钥",
desc = "用于开启音语宝匣",
icon = "T_Common_Item_System_Bag_150",
getWay = "参与幻猫音境祈愿",
jumpId = {
638
},
bHideInBag = true,
jumpToast = v0
},
[3803] = {
id = 3803,
effect = true,
name = "夏日西瓜",
desc = "用于在活动中兑换奖励",
icon = "T_Common_Item_System_Bag_083",
getWay = "完成限时活动",
bHideInBag = true,
jumpToast = v0
},
[3950] = {
id = 3950,
effect = true,
quality = 3,
name = "月华券",
desc = "用于在祈愿活动中兑换商品，祈愿活动结束后不会清空",
icon = "T_Common_Icon_Coin_118",
getWay = "参与蝶舞花间祈愿",
jumpId = {
8899
},
jumpToast = v0
},
[3788] = {
id = 3788,
effect = true,
expiredReplaceItem = v1,
name = "南瓜积分",
desc = "用于永夜城竞答活动中兑换奖励",
icon = "T_Common_Item_ PumpkinBig_01",
getWay = "参与永夜城竞答活动",
bHideInBag = true,
jumpToast = v0
},
[3634] = {
id = 3634,
effect = true,
name = "狼人礼盒",
desc = "用于在组队开宝箱活动中开启宝箱",
icon = "T_Common_Item_GiftBoxBig_01",
getWay = "完成组队开宝箱任务",
bHideInBag = true,
jumpToast = v0
},
[3804] = {
id = 3804,
effect = true,
expiredReplaceItem = v1,
name = "挑战点",
desc = "用于在峡谷挑战中获取奖励",
icon = "CDN:CT_Arena_Icon_Coin_008",
getWay = "完成挑战任务",
bHideInBag = true,
jumpToast = v0
},
[3635] = {
id = 3635,
effect = true,
name = "密语信笺",
desc = "用于在青春不散场活动中兑换奖励",
icon = "CDN:T_Common_Icon_Token_Letter",
getWay = "完成青春不散场任务",
bHideInBag = true,
jumpToast = v0
},
[3636] = {
id = 3636,
effect = true,
name = "照相机",
desc = "用于在快闪季来袭中兑换奖励",
icon = "CDN:T_Common_Icon_Coin_49",
getWay = "完成快闪季来袭任务",
bHideInBag = true,
jumpToast = v0
},
[3637] = {
id = 3637,
effect = true,
name = "冲刺币",
desc = "用于在赛季末冲刺活动中兑换奖励",
icon = "CDN:T_Common_Icon_Coin_115",
getWay = "完成赛季末冲刺任务",
bHideInBag = true,
jumpToast = v0
},
[3638] = {
id = 3638,
effect = true,
name = "冰激凌",
desc = "用于在宝宝游园记活动中兑换奖励",
icon = "CDN:T_Common_Item_System_Bag_082",
getWay = "完成宝宝游园记任务获得",
bHideInBag = true,
jumpToast = v0
},
[3639] = {
id = 3639,
effect = true,
quality = 3,
name = "时装兑换券",
desc = "用于在暑期特惠中兑换商品，活动结束后不会清空",
icon = "CDN:T_Common_Item_System_Bag_152",
getWay = "参与每日幸运星抽奖或购买周末幸运礼",
bHideInBag = true
},
[3640] = {
id = 3640,
effect = true,
quality = 3,
name = "峡谷兑换券",
desc = "用于在峡谷活动中兑换商品，活动结束后不会清空",
icon = "CDN:CT_Arena_Item_DailyGift_ExchangeCard",
getWay = "参与峡谷活动获得",
bHideInBag = true
},
[3641] = {
id = 3641,
effect = true,
quality = 3,
name = "暑期货币",
desc = "导航栏货币测试",
icon = "CDN:T_Common_Icon_Coin_115",
getWay = "暑期导航栏",
bHideInBag = true
},
[3642] = {
id = 3642,
effect = true,
quality = 3,
name = "铲子",
desc = "秘境寻宝货币测试",
icon = "CDN:T_Common_Icon_Coin_49",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3643] = {
id = 3643,
effect = true,
quality = 3,
name = "铲子币",
desc = "秘境寻宝货币测试",
icon = "CDN:T_Common_Icon_Coin_115",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3644] = {
id = 3644,
effect = true,
quality = 3,
name = "吧唧占坑1",
desc = "导航栏货币测试",
icon = "T_MusicOrder_Icon_Item1",
getWay = "暑期导航栏",
bHideInBag = true
},
[3645] = {
id = 3645,
effect = true,
quality = 3,
name = "吧唧占坑2",
desc = "导航栏货币测试",
icon = "T_MusicOrder_Icon_Item2",
getWay = "暑期导航栏",
bHideInBag = true
},
[3646] = {
id = 3646,
effect = true,
quality = 3,
name = "吧唧占坑3",
desc = "导航栏货币测试",
icon = "T_MusicOrder_Icon_Item3",
getWay = "暑期导航栏",
bHideInBag = true
}
}

local mt = {
effect = false,
type = "ItemType_Currency",
quality = 4,
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data