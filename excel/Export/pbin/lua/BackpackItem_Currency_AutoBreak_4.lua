--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表.xlsx: 货币

local data = {
[3647] = {
id = 3647,
effect = true,
type = "ItemType_Currency",
quality = 3,
name = "吧唧占坑4",
desc = "导航栏货币测试",
icon = "T_MusicOrder_Icon_Item4",
getWay = "暑期导航栏",
bHideInBag = true
},
[3648] = {
id = 3648,
effect = true,
type = "ItemType_Currency",
quality = 3,
name = "吧唧占坑5",
desc = "导航栏货币测试",
icon = "T_MusicOrder_Icon_Item5",
getWay = "暑期导航栏",
bHideInBag = true
},
[3649] = {
id = 3649,
effect = true,
type = "ItemType_Currency",
quality = 3,
name = "吧唧占坑6",
desc = "导航栏货币测试",
icon = "T_MusicOrder_Icon_Item6",
getWay = "暑期导航栏",
bHideInBag = true
},
[3650] = {
id = 3650,
effect = true,
type = "ItemType_Currency",
quality = 5,
name = "探险家旧靴",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_LetterBig_01",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3651] = {
id = 3651,
effect = true,
type = "ItemType_Currency",
quality = 5,
name = "废弃矿工帽",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_KeyBig_05",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3652] = {
id = 3652,
effect = true,
type = "ItemType_Currency",
quality = 5,
name = "笨重的石头",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_DrinkBig_01",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3653] = {
id = 3653,
effect = true,
type = "ItemType_Currency",
quality = 3,
name = "秘银星",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_PhoneBig_01",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3654] = {
id = 3654,
effect = true,
type = "ItemType_Currency",
quality = 3,
name = "金苹果",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_WalletBig_01",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3655] = {
id = 3655,
effect = true,
type = "ItemType_Currency",
quality = 2,
name = "翡翠原石",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_GiftBoxBig_02",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3656] = {
id = 3656,
effect = true,
type = "ItemType_Currency",
quality = 2,
name = "蓝晶宝石",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_DrinkBig_01",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3657] = {
id = 3657,
effect = true,
type = "ItemType_Currency",
quality = 2,
name = "百宝袋",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_PhoneBig_01",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3658] = {
id = 3658,
effect = true,
type = "ItemType_Currency",
quality = 1,
name = "鸟蛋",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_WalletBig_01",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3659] = {
id = 3659,
effect = true,
type = "ItemType_Currency",
quality = 1,
name = "日曜金轮",
desc = "秘境寻宝货币测试",
icon = "T_Common_Item_GiftBoxBig_02",
getWay = "秘境寻宝货币测试",
bHideInBag = true
},
[3562] = {
id = 3562,
effect = true,
type = "ItemType_Currency",
quality = 4,
name = "冲刺硬币",
desc = "用于在新赛季来袭中兑换奖励",
icon = "T_Common_Icon_Coin_98",
getWay = "完成新赛季来袭任务",
bHideInBag = true,
jumpToast = "JumpToast_common"
},
[3563] = {
id = 3563,
effect = true,
type = "ItemType_Currency",
quality = 4,
name = "耀目珍珠",
desc = "用于在吉伊卡哇中兑换奖励",
icon = "T_Common_Item_System_Bag_088",
getWay = "完成吉伊卡哇任务",
bHideInBag = true,
jumpToast = "JumpToast_common"
}
}

local mt = {
effect = false,
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data