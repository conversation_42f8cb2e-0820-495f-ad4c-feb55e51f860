--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_Arena.xlsx: 商城-Arena

local v0 = 1

local data = {
[1100301] = {
commodityId = 1100301,
commodityName = "安琪拉",
price = 300,
itemIds = {
301103
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1003
}
}
}
}
}
},
NoShowInMall = 1
},
[1100302] = {
commodityId = 1100302,
commodityName = "赵云",
price = 500,
discountPrice = 60,
itemIds = {
301110
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1010
}
}
}
}
}
},
NoShowInMall = 1
},
[1100303] = {
commodityId = 1100303,
commodityName = "蔡文姬",
price = 500,
itemIds = {
301113
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1013
}
}
}
}
}
},
NoShowInMall = 1
},
[1100305] = {
commodityId = 1100305,
commodityName = "孙尚香",
price = 300,
discountPrice = 60,
itemIds = {
301114
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1014
}
}
}
}
}
},
NoShowInMall = 1
},
[1100306] = {
commodityId = 1100306,
commodityName = "李白",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-08-30 00:00:00",
"2024-09-05 23:59:59"
}
},
itemIds = {
301119
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1019
}
}
}
}
}
},
NoShowInMall = 1
},
[1100307] = {
commodityId = 1100307,
commodityName = "钟馗",
price = 150,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "40",
params = {
"2024-09-06 00:00:00",
"2024-09-12 23:59:59"
}
},
itemIds = {
301115
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1015
}
}
}
}
}
},
NoShowInMall = 1
},
[1100308] = {
commodityId = 1100308,
commodityName = "云缨",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-09-13 00:00:00",
"2024-09-19 23:59:59"
}
},
itemIds = {
301117
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1017
}
}
}
}
}
},
NoShowInMall = 1
},
[1100309] = {
commodityId = 1100309,
commodityName = "王昭君",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-09-20 00:00:00",
"2024-09-26 23:59:59"
}
},
itemIds = {
301118
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1018
}
}
}
}
}
},
NoShowInMall = 1
},
[1100304] = {
commodityId = 1100304,
commodityName = "橙色星喜卡牌匣",
coinType = 3541,
price = 400,
limitType = "MCL_SeasonLimit",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
minVersion = "1.3.36.1",
itemIds = {
300203
}
},
[1100310] = {
commodityId = 1100310,
commodityName = "孙悟空",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-10-01 00:00:00",
"2024-10-07 23:59:59"
}
},
itemIds = {
301116
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1016
}
}
}
}
}
},
NoShowInMall = 1
},
[1100311] = {
commodityId = 1100311,
commodityName = "墨子",
price = 150,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "40",
params = {
"2024-09-27 00:00:00",
"2024-10-03 23:59:59"
}
},
itemIds = {
301120
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1020
}
}
}
}
}
},
NoShowInMall = 1
},
[1100312] = {
commodityId = 1100312,
commodityName = "东皇太一",
price = 150,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "40",
params = {
"2024-10-03 00:00:00",
"2024-10-09 23:59:59"
}
},
itemIds = {
301122
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1022
}
}
}
}
}
},
NoShowInMall = 1
},
[1100313] = {
commodityId = 1100313,
commodityName = "狄仁杰",
price = 150,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "40",
params = {
"2024-10-05 00:00:00",
"2024-10-11 23:59:59"
}
},
itemIds = {
301123
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1023
}
}
}
}
}
},
NoShowInMall = 1
},
[1100314] = {
commodityId = 1100314,
commodityName = "花木兰",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-10-11 00:00:00",
"2024-10-17 23:59:59"
}
},
itemIds = {
301124
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1024
}
}
}
}
}
},
NoShowInMall = 1
},
[1101302] = {
commodityId = 1101302,
commodityName = "峡谷英雄王昭君",
price = 300,
discountPrice = 10,
canDirectBuy = true,
itemIds = {
301118
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1018
}
}
}
}
}
},
NoShowInMall = 1
},
[1100315] = {
commodityId = 1100315,
commodityName = "妲己",
price = 150,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "40",
params = {
"2024-10-18 00:00:00",
"2024-10-24 23:59:59"
}
},
itemIds = {
301125
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1025
}
}
}
}
}
},
NoShowInMall = 1
},
[1100316] = {
commodityId = 1100316,
commodityName = "马可波罗",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-11-01 00:00:00",
"2024-11-07 23:59:59"
}
},
itemIds = {
301127
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1027
}
}
}
}
}
},
NoShowInMall = 1
},
[1100317] = {
commodityId = 1100317,
commodityName = "宫本武藏",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-11-08 00:00:00",
"2024-11-14 23:59:59"
}
},
itemIds = {
301128
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1034
}
}
}
}
}
},
NoShowInMall = 1
},
[1100318] = {
commodityId = 1100318,
commodityName = "夏侯惇",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-11-15 00:00:00",
"2024-11-21 23:59:59"
}
},
itemIds = {
301129
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1028
}
}
}
}
}
},
NoShowInMall = 1
},
[1100319] = {
commodityId = 1100319,
commodityName = "张良",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-11-22 00:00:00",
"2024-11-28 23:59:59"
}
},
itemIds = {
301130
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1029
}
}
}
}
}
},
NoShowInMall = 1
},
[1100320] = {
commodityId = 1100320,
commodityName = "阿珂",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-12-13 00:00:00",
"2024-12-19 23:59:59"
}
},
itemIds = {
301131
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1035
}
}
}
}
}
},
NoShowInMall = 1
},
[1100321] = {
commodityId = 1100321,
commodityName = "吕布",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2024-12-20 00:00:00",
"2024-12-26 23:59:59"
}
},
itemIds = {
301132
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1032
}
}
}
}
}
},
NoShowInMall = 1
},
[1100322] = {
commodityId = 1100322,
commodityName = "典韦",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-01-03 00:00:00",
"2025-01-09 23:59:59"
}
},
itemIds = {
301134
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1036
}
}
}
}
}
},
NoShowInMall = 1
},
[1100323] = {
commodityId = 1100323,
commodityName = "甄姬",
price = 300,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-01-29 00:00:00",
"2025-02-04 23:59:59"
}
},
itemIds = {
301137
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1037
}
}
}
}
}
},
NoShowInMall = 1
},
[1100324] = {
commodityId = 1100324,
commodityName = "虞姬",
price = 600,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-02-14 00:00:00",
"2025-02-20 23:59:59"
}
},
itemIds = {
301133
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1031
}
}
}
}
}
},
NoShowInMall = 1
},
[1100325] = {
commodityId = 1100325,
commodityName = "大乔",
price = 450,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-02-21 00:00:00",
"2025-02-26 23:59:59"
}
},
itemIds = {
301139
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1039
}
}
}
}
}
},
NoShowInMall = 1
},
[1100326] = {
commodityId = 1100326,
commodityName = "诸葛亮",
price = 450,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-02-28 00:00:00",
"2025-03-06 23:59:59"
}
},
itemIds = {
301144
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1044
}
}
}
}
}
},
NoShowInMall = 1
},
[1100327] = {
commodityId = 1100327,
commodityName = "刘禅",
price = 450,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-03-28 00:00:00",
"2025-04-02 23:59:59"
}
},
itemIds = {
301143
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1043
}
}
}
}
}
},
NoShowInMall = 1
},
[1100328] = {
commodityId = 1100328,
commodityName = "海月",
price = 450,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-04-18 00:00:00",
"2025-04-24 23:59:59"
}
},
itemIds = {
301146
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1046
}
}
}
}
}
},
NoShowInMall = 1
},
[1100329] = {
commodityId = 1100329,
commodityName = "艾琳",
price = 450,
midasDiscountConf = {
condition = "MMDC_TimeLimited",
productIdSuffix = "60",
params = {
"2025-05-30 00:00:00",
"2025-06-05 23:59:59"
}
},
itemIds = {
301148
},
buyCondition = {
condition = {
{
conditionType = 814,
value = 1,
subConditionList = {
{
type = 189,
value = {
0,
1048
}
}
}
}
}
},
NoShowInMall = 1
}
}

local mt = {
mallId = 112,
coinType = 1,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
shopTag = {
4
},
gender = 0,
itemNums = {
1
},
canDirectBuy = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data