--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 表情

local v0 = {
{
itemId = 6,
itemNum = 35
}
}

local v1 = 2

local v2 = {
emojiType = 1
}

local data = {
[711527] = {
id = 711527,
quality = 2,
name = "优雅舞姿",
desc = "保持自己的姿态",
icon = "CDN:Icon_emoji_554",
resourceConf = {
bluePrint = "D_emoji_Season_12_001"
}
},
[711530] = {
id = 711530,
exceedReplaceItem = v0,
name = "指尖博弈",
desc = "我出布，你信吗？",
icon = "CDN:Icon_emoji_559",
emojiConf = v2
},
[711528] = {
id = 711528,
quality = 2,
name = "霸王降世",
desc = "来，做个了断吧！",
icon = "CDN:Icon_emoji_556",
resourceConf = {
bluePrint = "D_emoji_Season_12_004"
}
},
[711529] = {
id = 711529,
quality = 2,
name = "小熊拳拳",
desc = "三招都接不住，回家烤火吧！",
icon = "CDN:Icon_emoji_555",
resourceConf = {
bluePrint = "D_emoji_Season_12_003"
}
},
[711531] = {
id = 711531,
quality = 2,
name = "大脑过载",
desc = "别说话，我在烧烤",
icon = "CDN:Icon_emoji_557",
resourceConf = {
bluePrint = "D_emoji_Season_12_005"
}
},
[711532] = {
id = 711532,
quality = 2,
name = "淡淡悲伤",
desc = "喜欢我、不喜欢我、喜欢我……",
icon = "CDN:Icon_emoji_558",
resourceConf = {
bluePrint = "D_emoji_Season_12_006"
}
},
[711533] = {
id = 711533,
quality = 2,
name = "点亮灵感",
desc = "当灵感遇上画笔，万物跃然纸上",
icon = "CDN:Icon_emoji_562",
resourceConf = {
bluePrint = "D_emoji_Season_12_009"
}
},
[711534] = {
id = 711534,
quality = 2,
name = "画魂出窍",
desc = "画笔说它累了……",
icon = "CDN:Icon_emoji_560",
resourceConf = {
bluePrint = "D_emoji_Season_12_007"
}
},
[711535] = {
id = 711535,
quality = 2,
name = "全力绘画",
desc = "在飞逝的时光中准时完稿",
icon = "CDN:Icon_emoji_561",
resourceConf = {
bluePrint = "D_emoji_Season_12_008"
}
},
[711536] = {
id = 711536,
name = "奖杯征程蓝品表情占坑1",
desc = "在飞逝的时光中准时完稿",
icon = "CDN:Icon_emoji_561",
resourceConf = {
bluePrint = "D_emoji_Season_12_008"
}
},
[711537] = {
id = 711537,
name = "奖杯征程蓝品表情占坑2",
desc = "在飞逝的时光中准时完稿",
icon = "CDN:Icon_emoji_561",
resourceConf = {
bluePrint = "D_emoji_Season_12_008"
}
},
[711538] = {
id = 711538,
quality = 2,
name = "奖杯征程紫品表情占坑1",
desc = "喜欢我、不喜欢我、喜欢我……",
icon = "CDN:Icon_emoji_558",
resourceConf = {
bluePrint = "D_emoji_Season_12_006"
}
},
[711539] = {
id = 711539,
quality = 2,
name = "奖杯征程紫品表情占坑2",
desc = "喜欢我、不喜欢我、喜欢我……",
icon = "CDN:Icon_emoji_558",
resourceConf = {
bluePrint = "D_emoji_Season_12_006"
}
},
[711540] = {
id = 711540,
quality = 2,
name = "下大雨啦",
desc = "开心不起来",
icon = "CDN:Icon_emoji_568",
resourceConf = {
bluePrint = "D_emoji_Season_12_010"
}
},
[711541] = {
id = 711541,
name = "好吃的味道",
desc = "呀呀~嗯啪啪~咻啪咻啪",
icon = "CDN:Icon_emoji_563",
emojiConf = v2
},
[711542] = {
id = 711542,
quality = 2,
name = "不要啊",
desc = "害怕起来了",
icon = "CDN:Icon_emoji_564",
resourceConf = {
bluePrint = "D_emoji_Season_12_011"
}
},
[711543] = {
id = 711543,
name = "乌萨奇日常",
desc = "哈啊啊？",
icon = "CDN:Icon_emoji_565",
emojiConf = v2
},
[711544] = {
id = 711544,
quality = 2,
name = "呀哈",
desc = "咿~呀~哈~~",
icon = "CDN:Icon_emoji_566",
resourceConf = {
bluePrint = "D_emoji_Season_12_012"
}
},
[711545] = {
id = 711545,
name = "这什么啊",
desc = "呃啊！",
icon = "CDN:Icon_emoji_567",
emojiConf = v2
},
[711546] = {
id = 711546,
quality = 2,
name = "风灵戏语",
desc = "想和风比赛脚力吗？",
icon = "CDN:Icon_emoji_569",
resourceConf = {
bluePrint = "D_emoji_Season_12_014"
}
},
[711547] = {
id = 711547,
quality = 2,
name = "独家放送",
desc = "让这个午夜燥起来！",
icon = "CDN:Icon_emoji_570",
resourceConf = {
bluePrint = "D_emoji_Season_12_013"
}
},
[711548] = {
id = 711548,
quality = 2,
name = "天使的祈祷",
desc = "祝你丰收，祝你快乐~",
icon = "CDN:Icon_emoji_571",
resourceConf = {
bluePrint = "D_emoji_Season_12_013"
}
},
[711549] = {
id = 711549,
exceedReplaceItem = v0,
name = "得意洋洋",
desc = "现在知道谁是主角了吧？",
icon = "CDN:CT_Icon_emoji_Moba_306",
emojiConf = v2
},
[711550] = {
id = 711550,
exceedReplaceItem = v0,
name = "目瞪口呆",
desc = "离离原上谱",
icon = "CDN:CT_Icon_emoji_Moba_307",
emojiConf = v2
},
[711551] = {
id = 711551,
exceedReplaceItem = v0,
name = "暴跳如雷",
desc = "你的愿望绝不会实现",
icon = "CDN:CT_Icon_emoji_Moba_308",
emojiConf = v2
},
[711552] = {
id = 711552,
exceedReplaceItem = v0,
name = "泪流满面",
desc = "这就是，青春吗？",
icon = "CDN:CT_Icon_emoji_Moba_309",
emojiConf = v2
},
[711553] = {
id = 711553,
exceedReplaceItem = v0,
name = "呆若木鸡",
desc = "充满智慧的眼神",
icon = "CDN:CT_Icon_emoji_Moba_310",
emojiConf = v2
},
[711554] = {
id = 711554,
exceedReplaceItem = v0,
name = "热血沸腾",
desc = "拼搏一阵子，幸福一辈子",
icon = "CDN:CT_Icon_emoji_Moba_311",
emojiConf = v2
},
[711555] = {
id = 711555,
exceedReplaceItem = v0,
name = "冷汗直冒",
desc = "我才不紧张，只是穿太多了",
icon = "CDN:CT_Icon_emoji_Moba_312",
emojiConf = v2
},
[711556] = {
id = 711556,
exceedReplaceItem = v0,
name = "战术嘲讽",
desc = "这就是实力的差距",
icon = "CDN:CT_Icon_emoji_Moba_313",
emojiConf = v2
},
[711557] = {
id = 711557,
exceedReplaceItem = v0,
name = "重拳反击",
desc = "对他们使用火拳吧",
icon = "CDN:CT_Icon_emoji_Moba_314",
emojiConf = v2
},
[711558] = {
id = 711558,
exceedReplaceItem = v0,
name = "濒临崩溃",
desc = "我觉得我还能再抢救一下",
icon = "CDN:CT_Icon_emoji_Moba_315",
emojiConf = v2
},
[711559] = {
id = 711559,
exceedReplaceItem = v0,
name = "尴尬发作",
desc = "脚趾快扣出小别墅了",
icon = "CDN:CT_Icon_emoji_Moba_316",
emojiConf = v2
},
[711560] = {
id = 711560,
exceedReplaceItem = v0,
name = "事不关己",
desc = "可是这关我什么事呢？",
icon = "CDN:CT_Icon_emoji_Moba_317",
emojiConf = v2
},
[711561] = {
id = 711561,
exceedReplaceItem = v0,
name = "可爱心动",
desc = "太可爱了，好想抱一下",
icon = "CDN:CT_Icon_emoji_Moba_318",
emojiConf = v2
},
[711562] = {
id = 711562,
exceedReplaceItem = v0,
name = "扶额叹息",
desc = "罢了罢了，不和你一般见识",
icon = "CDN:CT_Icon_emoji_Moba_319",
emojiConf = v2
},
[711563] = {
id = 711563,
exceedReplaceItem = v0,
name = "捂嘴惊叫",
desc = "完全超出了我的认知",
icon = "CDN:CT_Icon_emoji_Moba_320",
emojiConf = v2
},
[711564] = {
id = 711564,
exceedReplaceItem = v0,
name = "邪魅一笑",
desc = "懂的都懂",
icon = "CDN:CT_Icon_emoji_Moba_321",
emojiConf = v2
},
[711565] = {
id = 711565,
exceedReplaceItem = v0,
name = "手足无措",
desc = "瞧我干的好事",
icon = "CDN:CT_Icon_emoji_Moba_322",
emojiConf = v2
},
[711566] = {
id = 711566,
exceedReplaceItem = v0,
name = "精神涣散",
desc = "感觉身体被掏空",
icon = "CDN:CT_Icon_emoji_Moba_323",
emojiConf = v2
},
[711567] = {
id = 711567,
exceedReplaceItem = v0,
name = "惊讶后仰",
desc = "真是细思恐极",
icon = "CDN:CT_Icon_emoji_Moba_324",
emojiConf = v2
},
[711568] = {
id = 711568,
exceedReplaceItem = v0,
name = "放肆大笑",
desc = "你成功将我逗笑",
icon = "CDN:CT_Icon_emoji_Moba_325",
emojiConf = v2
},
[711569] = {
id = 711569,
exceedReplaceItem = v0,
name = "瞳孔地震",
desc = "我怎么会捅这么大篓子",
icon = "CDN:CT_Icon_emoji_Moba_326",
emojiConf = v2
},
[711570] = {
id = 711570,
exceedReplaceItem = v0,
name = "算计得逞",
desc = "又得到了不错的故事素材",
icon = "CDN:CT_Icon_emoji_Moba_327",
emojiConf = v2
},
[711571] = {
id = 711571,
exceedReplaceItem = v0,
name = "突发灵感",
desc = "我有一个很棒的计划",
icon = "CDN:CT_Icon_emoji_Moba_328",
emojiConf = v2
},
[711572] = {
id = 711572,
quality = 2,
name = "心虚眨眼",
desc = "一点小事，没必要生气吧？",
icon = "CDN:CT_Icon_emoji_Moba_329",
resourceConf = {
bluePrint = "D_emoji_Moba_AilinXinxuzhayan_08_001"
}
},
[711573] = {
id = 711573,
quality = 2,
name = "扶眼镜",
desc = "战术性扶眼镜",
icon = "CDN:CT_Icon_emoji_Moba_330",
resourceConf = {
bluePrint = "D_emoji_Moba_ZhangliangSikao_08_001"
}
},
[711574] = {
id = 711574,
quality = 2,
name = "麻木呆坐",
desc = "对你们的需求已经麻木了",
icon = "CDN:CT_Icon_emoji_Moba_331",
resourceConf = {
bluePrint = "D_emoji_Moba_HaiyueWuliaodaosi_08_001"
}
},
[711575] = {
id = 711575,
quality = 2,
name = "吃货震惊",
desc = "原来还没到饭点吗？",
icon = "CDN:CT_Icon_emoji_Moba_332",
resourceConf = {
bluePrint = "D_emoji_Moba_LiubeiChihuozhenjing_08_001"
}
},
[711576] = {
id = 711576,
quality = 2,
name = "摸鱼划水",
desc = "今天的努力值已经用完了",
icon = "CDN:CT_Icon_emoji_Moba_333",
resourceConf = {
bluePrint = "D_emoji_Moba_LiushanMoyuhuashui_08_001"
}
},
[711577] = {
id = 711577,
quality = 2,
name = "向着星辰",
desc = "去捕捉梦中的色彩",
icon = "CDN:CT_Icon_emoji_Moba_334",
resourceConf = {
bluePrint = "D_emoji_Moba_LuopuxiaZhongerbing_08_001"
}
},
[711578] = {
id = 711578,
quality = 2,
name = "一睡不起",
desc = "现在正值冬眠期",
icon = "CDN:CT_Icon_emoji_Moba_335",
resourceConf = {
bluePrint = "D_emoji_Moba_QingshuangGeyoutan_08_001"
}
},
[711579] = {
id = 711579,
quality = 2,
name = "浑身触电",
desc = "我要的触电感觉可不是这种",
icon = "CDN:CT_Icon_emoji_Moba_336",
resourceConf = {
bluePrint = "D_emoji_Moba_SunceChudianchouchu_08_001"
}
},
[711580] = {
id = 711580,
quality = 2,
name = "静静地看",
desc = "我就静静地看着你",
icon = "CDN:CT_Icon_emoji_Moba_337",
resourceConf = {
bluePrint = "D_emoji_Moba_XiangyuAIGuzhang_08_001"
}
},
[711581] = {
id = 711581,
quality = 2,
name = "重力反转",
desc = "天呐，天到底在哪边？",
icon = "CDN:CT_Icon_emoji_Moba_338",
resourceConf = {
bluePrint = "D_emoji_Moba_YaseZhonglifanzhuan_08_001"
}
},
[711582] = {
id = 711582,
quality = 2,
name = "时间静止",
desc = "下面是小鹿时间",
icon = "CDN:CT_Icon_emoji_Moba_339",
resourceConf = {
bluePrint = "D_emoji_Moba_YaoShijianjingzhi_08_001"
}
},
[711583] = {
id = 711583,
quality = 2,
name = "悄悄摸摸",
desc = "悄悄飘到你的身边",
icon = "CDN:CT_Icon_emoji_Moba_340",
resourceConf = {
bluePrint = "D_emoji_Moba_GongsunliToumingyouling_08_001"
}
},
[711584] = {
id = 711584,
quality = 2,
name = "瞬间冻结",
desc = "糟糕，谁来救我出去？",
icon = "CDN:CT_Icon_emoji_Moba_341",
resourceConf = {
bluePrint = "D_emoji_Moba_BingyiDongchenbingdiao_08_002"
}
},
[711585] = {
id = 711585,
quality = 2,
name = "歪头提问",
desc = "问题是，该从哪问起呢？",
icon = "CDN:CT_Icon_emoji_Moba_342",
resourceConf = {
bluePrint = "D_emoji_Moba_XiaohonghuMaotouwaiwai_08_001"
}
},
[711586] = {
id = 711586,
quality = 2,
name = "赛博探测",
desc = "呵呵，战斗力只有5啊",
icon = "CDN:CT_Icon_emoji_Moba_343",
resourceConf = {
bluePrint = "D_emoji_Moba_MingshiyinSaibojixie_08_001"
}
},
[711587] = {
id = 711587,
quality = 2,
name = "生气暴走",
desc = "忍者也不能忍",
icon = "CDN:CT_Icon_emoji_Moba_344",
resourceConf = {
bluePrint = "D_emoji_Moba_RenzheBaozou_08_001"
}
},
[711588] = {
id = 711588,
quality = 2,
name = "裂开了",
desc = "这就叫道心破碎吧",
icon = "CDN:CT_Icon_emoji_Moba_345",
resourceConf = {
bluePrint = "D_emoji_Moba_BingyiCiyuanposui_08_001"
}
},
[711589] = {
id = 711589,
quality = 2,
name = "王牌出击",
desc = "我将以修罗形态出击",
icon = "CDN:CT_Icon_emoji_Moba_346",
resourceConf = {
bluePrint = "D_emoji_Moba_KaiLiyudadizhishang_08_001"
}
},
[711590] = {
id = 711590,
exceedReplaceItem = v0,
name = "扬帆起航",
desc = "从今天开始，我要扬帆远航了！",
icon = "CDN:Icon_emoji_595"
},
[711613] = {
id = 711613,
quality = 2,
name = "端庄",
desc = "这才是真正的艺术",
icon = "CDN:Icon_emoji_572",
resourceConf = {
bluePrint = "D_emoji_Season_13_001"
}
},
[711591] = {
id = 711591,
exceedReplaceItem = v0,
name = "再来一局",
desc = "再来一局，我怎么可能输？",
icon = "CDN:Icon_emoji_578",
emojiConf = v2
},
[711592] = {
id = 711592,
exceedReplaceItem = v0,
name = "耍帅",
desc = "今天的我，也依旧帅气呢~",
icon = "CDN:Icon_emoji_573",
emojiConf = v2
},
[711593] = {
id = 711593,
exceedReplaceItem = v0,
name = "深情",
desc = "爱我，你怕了吗？",
icon = "CDN:Icon_emoji_574",
emojiConf = v2
},
[711594] = {
id = 711594,
exceedReplaceItem = v0,
name = "盯",
desc = "拉弥娅会一直看着你，一直……",
icon = "CDN:Icon_emoji_575",
emojiConf = v2
},
[711595] = {
id = 711595,
exceedReplaceItem = v0,
name = "随便吧",
desc = "随便吧，无所谓了",
icon = "CDN:Icon_emoji_576",
emojiConf = v2
},
[711596] = {
id = 711596,
exceedReplaceItem = v0,
name = "坏笑",
desc = "承认吧，你已经是我的手下败将了",
icon = "CDN:Icon_emoji_577",
emojiConf = v2
}
}

local mt = {
type = "ItemType_Emoji",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 3,
emojiConf = {
emojiType = 3
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data