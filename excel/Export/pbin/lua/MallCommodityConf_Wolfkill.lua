--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-狼人杀

local v0 = 1

local v1 = "MCL_LifeLongLimit"

local data = {
[181002] = {
commodityId = 181002,
commodityName = "身份卡",
coinType = 1,
price = 22,
limitType = "MCL_DailyLimit",
limitNum = 5,
shopSort = 6,
itemIds = {
200102
}
},
[181001] = {
commodityId = 181001,
commodityName = "阵营卡",
coinType = 1,
price = 8,
limitType = "MCL_DailyLimit",
limitNum = 5,
shopSort = 7,
itemIds = {
200101
}
},
[180002] = {
commodityId = 180002,
commodityName = "扔炸弹",
coinType = 1,
price = 1,
shopSort = 20,
itemIds = {
240405
}
},
[180004] = {
commodityId = 180004,
commodityName = "爱心",
coinType = 1,
price = 1,
shopSort = 21,
itemIds = {
240403
}
},
[180005] = {
commodityId = 180005,
commodityName = "倒咖啡",
coinType = 1,
price = 1,
itemIds = {
240406
}
},
[180006] = {
commodityId = 180006,
commodityName = "丢鸡蛋",
coinType = 1,
price = 1,
itemIds = {
240401
}
},
[180007] = {
commodityId = 180007,
commodityName = "送花",
coinType = 1,
price = 5,
shopSort = 15,
itemIds = {
240404
},
canGift = true,
addIntimacy = 2,
giftCoinType = 1,
giftPrice = 5
},
[180008] = {
commodityId = 180008,
commodityName = "拳击",
coinType = 1,
price = 5,
shopSort = 16,
itemIds = {
240402
},
canGift = true,
addIntimacy = 2,
giftCoinType = 1,
giftPrice = 5
},
[180021] = {
commodityId = 180021,
commodityName = "疯狂点赞",
coinType = 1,
price = 1,
shopSort = 18,
itemIds = {
240416
},
canGift = true,
addIntimacy = 1,
giftCoinType = 1,
giftPrice = 1
},
[180022] = {
commodityId = 180022,
commodityName = "贴符",
coinType = 1,
price = 5,
shopSort = 12,
itemIds = {
240417
},
canGift = true,
addIntimacy = 2,
giftCoinType = 1,
giftPrice = 5
},
[180023] = {
commodityId = 180023,
commodityName = "丢盲盒",
coinType = 1,
price = 5,
shopSort = 13,
itemIds = {
240418
},
canGift = true,
addIntimacy = 2,
giftCoinType = 1,
giftPrice = 5
},
[180024] = {
commodityId = 180024,
commodityName = "祝福",
coinType = 1,
price = 5,
shopSort = 14,
itemIds = {
240419
},
canGift = true,
addIntimacy = 2,
giftCoinType = 1,
giftPrice = 5
},
[180025] = {
commodityId = 180025,
commodityName = "丢蛋糕",
coinType = 1,
price = 1,
shopSort = 18,
itemIds = {
240420
},
canGift = true,
addIntimacy = 1,
giftCoinType = 1,
giftPrice = 1
},
[240603] = {
commodityId = 240603,
commodityName = "小猪撞击",
coinType = 1,
price = 8,
shopSort = 11,
minVersion = "1.3.18.16",
itemIds = {
240410
},
canGift = true,
addIntimacy = 5,
giftCoinType = 1,
giftPrice = 8
},
[240604] = {
commodityId = 240604,
commodityName = "干杯",
coinType = 1,
price = 1,
shopSort = 19,
minVersion = "1.3.18.17",
itemIds = {
240412
}
},
[240605] = {
commodityId = 240605,
commodityName = "打call",
coinType = 1,
price = 5,
shopSort = 17,
minVersion = "1.3.18.18",
itemIds = {
240413
},
canGift = true,
addIntimacy = 5,
giftCoinType = 1,
giftPrice = 5
},
[240612] = {
commodityId = 240612,
commodityName = "扎心",
coinType = 1,
price = 40,
limitType = v1,
limitNum = 1,
shopSort = 8,
itemIds = {
240615
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 40
},
[181003] = {
commodityId = 181003,
commodityName = "震惊",
coinType = 1,
price = 40,
limitType = v1,
limitNum = 1,
shopSort = 9,
itemIds = {
240601
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 40
},
[181004] = {
commodityId = 181004,
commodityName = "点赞",
coinType = 1,
price = 40,
limitType = v1,
limitNum = 1,
shopSort = 10,
itemIds = {
240602
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 40
},
[385005] = {
commodityId = 385005,
commodityName = "大锤击飞",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1739635200
},
endTime = {
seconds = 1742745599
},
shopSort = 3,
jumpId = 569,
jumpText = "幸运翻牌",
minVersion = "1.3.68.55",
itemIds = {
240203
}
},
[180009] = {
commodityId = 180009,
commodityName = "敲锣打鼓",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1739635200
},
endTime = {
seconds = 1742745599
},
shopSort = 4,
jumpId = 570,
jumpText = "幸运翻牌",
minVersion = "1.3.68.55",
itemIds = {
240004
}
},
[180013] = {
commodityId = 180013,
commodityName = "小猪冲锋",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1739635200
},
endTime = {
seconds = 1742745599
},
shopSort = 5,
jumpId = 571,
jumpText = "幸运翻牌",
minVersion = "1.3.68.55",
itemIds = {
240208
}
},
[180014] = {
commodityId = 180014,
commodityName = "时空特警",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1739635200
},
endTime = {
seconds = 1742745599
},
shopSort = 6,
jumpId = 572,
jumpText = "幸运翻牌",
minVersion = "1.3.68.55",
itemIds = {
240007
}
},
[180015] = {
commodityId = 180015,
commodityName = "巨星登场",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1743868800
},
endTime = {
seconds = 1746460799
},
jumpId = 642,
jumpText = "幸运MVP",
minVersion = "1.3.78.50",
itemIds = {
241002
}
},
[240621] = {
commodityId = 240621,
commodityName = "我胆子小",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1742572800
},
endTime = {
seconds = 1744559999
},
shopSort = 1,
jumpId = 590,
jumpText = "幸运翻翻乐",
minVersion = "1.3.78.34",
itemIds = {
240006
}
},
[385006] = {
commodityId = 385006,
commodityName = "遥控坦克",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1742572800
},
endTime = {
seconds = 1744559999
},
shopSort = 2,
jumpId = 591,
jumpText = "幸运翻翻乐",
minVersion = "1.3.78.34",
itemIds = {
240209
}
},
[385009] = {
commodityId = 385009,
commodityName = "彩虹天桥",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750607999
},
shopSort = 3,
jumpId = 682,
jumpText = "幸运MVP",
minVersion = "1.3.88.50",
itemIds = {
241003
}
},
[385011] = {
commodityId = 385011,
commodityName = "紧急播报",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1746720000
},
endTime = {
seconds = 1748793599
},
shopSort = 1,
jumpId = 672,
jumpText = "幸运大翻牌",
minVersion = "1.3.88.1",
itemIds = {
240008
}
},
[385010] = {
commodityId = 385010,
commodityName = "宝葫芦",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1746720000
},
endTime = {
seconds = 1748793599
},
shopSort = 2,
jumpId = 673,
jumpText = "幸运大翻牌",
minVersion = "1.3.88.1",
itemIds = {
240210
}
},
[385012] = {
commodityId = 385012,
commodityName = "雷神降临",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1737043199
},
shopSort = 3,
jumpId = 331,
jumpText = "狼人通行证",
minVersion = "1.3.37.55",
itemIds = {
241004
}
},
[385015] = {
commodityId = 385015,
commodityName = "一飞冲天",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 2,
jumpId = 331,
jumpText = "狼人通行证",
minVersion = "1.3.37.100",
itemIds = {
240214
}
},
[385016] = {
commodityId = 385016,
commodityName = "逛龙宫",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 3,
jumpId = 331,
jumpText = "狼人通行证",
minVersion = "1.3.37.100",
itemIds = {
240012
}
},
[180003] = {
commodityId = 180003,
commodityName = "天外飞桶",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1734192000
},
endTime = {
seconds = 1735487999
},
shopSort = 4,
jumpId = 507,
jumpText = "周年满减购",
minVersion = "1.3.37.37",
itemIds = {
240202
},
canGift = true,
addIntimacy = 100,
giftCoinType = 1,
giftPrice = 580
},
[180010] = {
commodityId = 180010,
commodityName = "小狗柯基",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1734192000
},
endTime = {
seconds = 1735487999
},
shopSort = 5,
jumpId = 507,
jumpText = "周年满减购",
minVersion = "1.3.37.37",
itemIds = {
240003
},
canGift = true,
addIntimacy = 100,
giftCoinType = 1,
giftPrice = 580
},
[180011] = {
commodityId = 180011,
commodityName = "舞台剧",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742140799
},
jumpId = 580,
jumpText = "满减大福利",
minVersion = "1.3.68.113",
itemIds = {
240002
}
},
[180012] = {
commodityId = 180012,
commodityName = "变蛙魔法",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742140799
},
jumpId = 580,
jumpText = "满减大福利",
minVersion = "1.3.68.113",
itemIds = {
240205
}
},
[180019] = {
commodityId = 180019,
commodityName = "滑板涂鸦 ",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1746979199
},
jumpId = 667,
jumpText = "满减大福利",
minVersion = "1.3.78.93",
itemIds = {
240005
}
},
[180020] = {
commodityId = 180020,
commodityName = "闪烁突袭",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1746979199
},
jumpId = 667,
jumpText = "满减大福利",
minVersion = "1.3.78.93",
itemIds = {
240206
}
},
[385013] = {
commodityId = 385013,
commodityName = "魔法扫帚",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1750607999
},
shopSort = 1,
jumpId = 693,
jumpText = "满减大福利",
minVersion = "1.3.88.80",
itemIds = {
240009
}
},
[240619] = {
commodityId = 240619,
commodityName = "爱心冲击",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1750607999
},
shopSort = 2,
jumpId = 693,
jumpText = "满减大福利",
minVersion = "1.3.88.80",
itemIds = {
240207
}
},
[385001] = {
commodityId = 385001,
commodityName = "真相永远只有一个",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1716047999
},
shopSort = 1,
jumpId = 665,
jumpText = "名侦探推理",
minVersion = "1.3.78.100",
itemIds = {
240011
}
},
[385002] = {
commodityId = 385002,
commodityName = "麻醉枪攻击",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1716047999
},
shopSort = 2,
jumpId = 666,
jumpText = "名侦探推理",
minVersion = "1.3.78.100",
itemIds = {
240212
}
},
[385017] = {
commodityId = 385017,
commodityName = "钓了个鱼",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1752163199
},
shopSort = 1,
jumpId = 7002,
jumpText = "幸运翻翻乐",
minVersion = "1.3.88.153",
itemIds = {
240213
}
},
[385018] = {
commodityId = 385018,
commodityName = "花样滑冰",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1752163199
},
shopSort = 1,
jumpId = 7001,
jumpText = "幸运翻翻乐",
minVersion = "1.3.88.153",
itemIds = {
240013
}
},
[240624] = {
commodityId = 240624,
commodityName = "吃月饼",
coinType = 1,
price = 1,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1727366399
},
minVersion = "1.3.18.31",
itemIds = {
240414
}
},
[240625] = {
commodityId = 240625,
commodityName = "花好月圆",
coinType = 1,
price = 18,
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1727366399
},
minVersion = "1.3.18.31",
itemIds = {
240415
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 18
},
[180016] = {
commodityId = 180016,
commodityName = "快点",
coinType = 1,
price = 40,
limitType = v1,
limitNum = 1,
shopSort = 11,
minVersion = "1.3.18.31",
itemIds = {
240610
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 40
},
[180017] = {
commodityId = 180017,
commodityName = "剑来",
coinType = 1,
price = 40,
limitType = v1,
limitNum = 1,
shopSort = 12,
minVersion = "1.3.18.31",
itemIds = {
240612
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 40
},
[180018] = {
commodityId = 180018,
commodityName = "看戏",
coinType = 1,
price = 40,
limitType = v1,
limitNum = 1,
shopSort = 13,
minVersion = "1.3.18.31",
itemIds = {
240614
},
canGift = true,
addIntimacy = 20,
giftCoinType = 1,
giftPrice = 40
},
[385003] = {
commodityId = 385003,
commodityName = "手表麻醉针",
coinType = 1,
price = 8,
limitNum = 1,
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1737043199
},
shopSort = 8,
minVersion = "1.3.37.55",
itemIds = {
240421
},
canGift = true,
addIntimacy = 5,
giftCoinType = 1,
giftPrice = 8
},
[385004] = {
commodityId = 385004,
commodityName = "新年快乐",
coinType = 1,
price = 18,
limitNum = 1,
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1737043199
},
shopSort = 9,
minVersion = "1.3.37.55",
itemIds = {
240422
},
canGift = true,
addIntimacy = 10,
giftCoinType = 1,
giftPrice = 18
},
[385019] = {
commodityId = 385019,
commodityName = "都市疾驰",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1743350400
},
endTime = {
seconds = 1746460799
},
shopSort = 1,
jumpId = 596,
jumpText = "精灵谷修行",
minVersion = "1.3.78.53",
itemIds = {
240015
}
},
[385020] = {
commodityId = 385020,
commodityName = "胖哒出击",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1743350400
},
endTime = {
seconds = 1746460799
},
shopSort = 1,
jumpId = 597,
jumpText = "精灵谷修行",
minVersion = "1.3.78.53",
itemIds = {
240215
}
},
[385021] = {
commodityId = 385021,
commodityName = "星球大战",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1747411200
},
endTime = {
seconds = 1750003199
},
shopSort = 1,
jumpId = 683,
jumpText = "狼人太空战",
minVersion = "1.3.88.50",
itemIds = {
240017
}
},
[385022] = {
commodityId = 385022,
commodityName = "热血足球",
limitType = v1,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1751212799
},
shopSort = 1,
jumpId = 692,
jumpText = "狼人世界杯",
minVersion = "1.3.88.50",
itemIds = {
240217
}
},
[385023] = {
commodityId = 385023,
commodityName = "谁是狼人月卡",
coinType = 1,
price = 268,
beginTime = {
seconds = 1748534400
},
shopSort = 1,
minVersion = "1.3.88.50",
itemIds = {
200178
},
canGift = true,
addIntimacy = 150,
giftCoinType = 1,
giftPrice = 268
}
}

local mt = {
mallId = 132,
beginTime = {
seconds = 1703433600
},
endTime = {
seconds = 4101897599
},
shopTag = {
6,
29
},
itemNums = {
1
},
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data