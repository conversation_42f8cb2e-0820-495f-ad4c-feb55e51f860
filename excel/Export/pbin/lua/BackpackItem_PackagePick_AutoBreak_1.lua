--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_礼包.xlsx: 自选礼包

local data = {
[330102] = {
id = 330102,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "S12通行证礼包",
desc = "可以从以下奖励中选择1个领取：峡谷英雄曜、峡谷英雄公孙离、峡谷币*1000",
icon = "CDN:CT_Arena_Item_ThreeSelectBox3",
getWay = "S12通行证",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301126,
301121,
3541
},
itemNums = {
1,
1,
1000
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[330103] = {
id = 330103,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "S13通行证礼包",
desc = "可以从以下奖励中选择1个领取：峡谷英雄程咬金、峡谷英雄项羽、峡谷币*1000",
icon = "CDN:CT_Arena_Item_ThreeSelectBox4",
getWay = "S13通行证",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301100,
301140,
3541
},
itemNums = {
1,
1,
1000
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[330104] = {
id = 330104,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "S13通行证礼包",
desc = "可以从以下奖励中选择1个领取：峡谷英雄貂蝉、峡谷英雄杨玉环、峡谷币*1000",
icon = "CDN:CT_Arena_Item_ThreeSelectBox5",
getWay = "S13通行证",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301141,
301145,
3541
},
itemNums = {
1,
1,
1000
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[330105] = {
id = 330105,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "S13通行证礼包",
desc = "可以从以下奖励中选择1个领取：峡谷英雄曜、峡谷英雄公孙离、峡谷币*1000",
icon = "CDN:CT_Arena_Item_ThreeSelectBox3",
getWay = "S13通行证",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301126,
301121,
3541
},
itemNums = {
1,
1,
1000
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[330801] = {
id = 330801,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 1,
name = "身份体验礼包",
desc = "可以从礼包中任选2个领取哦",
icon = "CDN:T_Common_Item_System_WerewolfIdentityBag_001",
getWay = "谁是狼人月卡",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240831,
240825,
240820,
240832,
240803,
240827,
240836,
240830,
240806,
240801,
240823,
13
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
1,
50
},
expireDays = {
7,
7,
7,
7,
7,
7,
7,
7,
7,
7,
7,
0
},
packagePickConf = {
pickNum = 2
}
}
},
[331014] = {
id = 331014,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "4选1表情包",
desc = "可以从以下狼人表情：傻笑、瑟瑟发抖、流汗、扎心中任选1个",
icon = "CDN:T_Common_Item_System_WerewolfBag_General02",
getWay = "参与活动",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240622,
240623,
240615,
240613
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1,
1
}
}
}
},
[332001] = {
id = 332001,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 1,
name = "卡包测试礼包",
desc = "5卡包 选择1个领取",
icon = "CDN:T_Maruko_Icon_Item1",
getWay = "祈愿活动",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
290011,
290012,
290013,
290014,
290015
},
itemNums = {
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[332002] = {
id = 332002,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 1,
name = "昼/夜天使自选礼盒",
desc = "可从以下奖励：昼天使 卢兹、夜天使 莎莉娅选择1个领取（若选择已拥有时装则会自动兑换成5个璀璨之星）",
icon = "CDN:T_Common_Item_System_Box_Angel01",
getWay = "参与活动",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
404550,
404540
},
itemNums = {
1,
1
},
expireDays = {
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[332003] = {
id = 332003,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "2选1宝箱",
desc = "可以从以下奖励：拳击少女、星宝守护者 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_BagBig_008",
getWay = "参与活动",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
402080,
400270
},
itemNums = {
1,
1
},
expireDays = {
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1
}
}
}
},
[332004] = {
id = 332004,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
name = "娱乐模式自选补给",
desc = "可以从以下奖励选择1个领取哦",
icon = "CDN:T_Common_Item_System_BagBig_007",
getWay = "奖杯进度奖励",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
200829,
3541,
320150
},
itemNums = {
2,
200,
3
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
5,
5,
2
}
}
}
},
[332005] = {
id = 332005,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "娱乐模式自选宝箱",
desc = "可从以下奖励中选择1个领取",
icon = "CDN:T_Common_Item_System_BagBig_008",
getWay = "奖杯进度奖励",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
219000,
340016,
270022,
260025,
320150
},
itemNums = {
5,
1,
1,
1,
5
},
expireDays = {
0,
0,
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
2,
1,
1,
1,
4
}
}
}
},
[332006] = {
id = 332006,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
lowVer = "1.3.99.1",
quality = 2,
name = "暑期快闪自选宝箱",
desc = "可从以下奖励中选择1个领取",
icon = "CDN:T_Common_Item_System_BagBig_008",
getWay = "暑期快闪活动获得",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
200120,
200101,
3541
},
itemNums = {
3,
2,
500
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
}
}

local mt = {
effect = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data