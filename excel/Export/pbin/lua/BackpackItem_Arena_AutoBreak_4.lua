--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_Arena.xlsx: Arena物件

local v0 = 999999

local v1 = 2

local v2 = 1

local v3 = "IUTO_ArenaHeroTry"

local v4 = {
duration = 168,
exchangeItemId = 3541,
exchangeItemNum = 10
}

local v5 = "T_Arena_Img_BigCard"

local data = {
[301143] = {
id = 301143,
type = "ItemType_Arena_HeroTry",
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "刘禅",
desc = "（限峡谷模式使用）获取后可以使用辅助刘禅。",
icon = "CDN:CT_Arena_SquareHero_1043",
useType = v3,
useParam = {
1043
},
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 1000
},
autoUse = true,
isShowPopRewardView = 1
},
[301222] = {
id = 301222,
stackedNum = 999999,
maxNum = 999999,
name = "阿珂体验卡（1天）",
desc = "免费体验1天阿珂（获取后自动使用，不会进入背包）。",
icon = "CDN:CT_Arena_SquareHero_1035",
useType = v3,
useParam = {
1035
},
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
},
[301226] = {
id = 301226,
stackedNum = 999999,
maxNum = 999999,
name = "貂蝉体验卡（1天）",
desc = "免费体验1天峡谷英雄貂蝉（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1041",
useType = v3,
useParam = {
1041
},
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
},
[301227] = {
id = 301227,
stackedNum = 999999,
maxNum = 999999,
name = "曜体验卡（1天）",
desc = "免费体验1天峡谷英雄曜（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1026",
useType = v3,
useParam = {
1026
},
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
},
[301228] = {
id = 301228,
stackedNum = 999999,
maxNum = 999999,
name = "公孙离体验卡（1天）",
desc = "免费体验1天峡谷英雄公孙离（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1021",
useType = v3,
useParam = {
1021
},
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
},
[301701] = {
id = 301701,
stackedNum = 999999,
maxNum = 999999,
name = "孙策体验卡（7天）",
desc = "免费体验7天峡谷英雄孙策（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1001",
useType = v3,
useParam = {
1001
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301702] = {
id = 301702,
stackedNum = 999999,
maxNum = 999999,
name = "铠体验卡（7天）",
desc = "免费体验7天峡谷英雄铠（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1002",
useType = v3,
useParam = {
1002
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301703] = {
id = 301703,
stackedNum = 999999,
maxNum = 999999,
name = "安琪拉体验卡（7天）",
desc = "免费体验7天峡谷英雄安琪拉（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1003",
useType = v3,
useParam = {
1003
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301704] = {
id = 301704,
stackedNum = 999999,
maxNum = 999999,
name = "鲁班七号体验卡（7天）",
desc = "免费体验7天峡谷英雄鲁班七号（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1004",
useType = v3,
useParam = {
1004
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301705] = {
id = 301705,
stackedNum = 999999,
maxNum = 999999,
name = "兰陵王体验卡（7天）",
desc = "免费体验7天峡谷英雄兰陵王（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1005",
useType = v3,
useParam = {
1005
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301706] = {
id = 301706,
stackedNum = 999999,
maxNum = 999999,
name = "瑶体验卡（7天）",
desc = "免费体验7天峡谷英雄瑶（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1006",
useType = v3,
useParam = {
1006
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301707] = {
id = 301707,
stackedNum = 999999,
maxNum = 999999,
name = "小乔体验卡（7天）",
desc = "免费体验7天峡谷英雄小乔（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1007",
useType = v3,
useParam = {
1007
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301710] = {
id = 301710,
stackedNum = 999999,
maxNum = 999999,
name = "赵云体验卡（7天）",
desc = "免费体验7天峡谷英雄赵云（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1010",
useType = v3,
useParam = {
1010
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301711] = {
id = 301711,
stackedNum = 999999,
maxNum = 999999,
name = "亚瑟体验卡（7天）",
desc = "免费体验7天峡谷英雄亚瑟（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1011",
useType = v3,
useParam = {
1011
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301712] = {
id = 301712,
stackedNum = 999999,
maxNum = 999999,
name = "后羿体验卡（7天）",
desc = "免费体验7天峡谷英雄后羿（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1012",
useType = v3,
useParam = {
1012
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301713] = {
id = 301713,
stackedNum = 999999,
maxNum = 999999,
name = "蔡文姬体验卡（7天）",
desc = "免费体验7天峡谷英雄蔡文姬（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1013",
useType = v3,
useParam = {
1013
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301714] = {
id = 301714,
stackedNum = 999999,
maxNum = 999999,
name = "孙尚香体验卡（7天）",
desc = "免费体验7天峡谷英雄孙尚香（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1014",
useType = v3,
useParam = {
1014
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301715] = {
id = 301715,
stackedNum = 999999,
maxNum = 999999,
name = "钟馗体验卡（7天）",
desc = "免费体验7天峡谷英雄钟馗（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1015",
useType = v3,
useParam = {
1015
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301716] = {
id = 301716,
stackedNum = 999999,
maxNum = 999999,
name = "孙悟空体验卡（7天）",
desc = "免费体验7天峡谷英雄孙悟空（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1016",
useType = v3,
useParam = {
1016
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301717] = {
id = 301717,
stackedNum = 999999,
maxNum = 999999,
name = "云缨体验卡（7天）",
desc = "免费体验7天峡谷英雄云缨（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1017",
useType = v3,
useParam = {
1017
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301718] = {
id = 301718,
stackedNum = 999999,
maxNum = 999999,
name = "王昭君体验卡（7天）",
desc = "免费体验7天峡谷英雄王昭君（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1018",
useType = v3,
useParam = {
1018
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301719] = {
id = 301719,
stackedNum = 999999,
maxNum = 999999,
name = "李白体验卡（7天）",
desc = "免费体验7天峡谷英雄李白（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1019",
useType = v3,
useParam = {
1019
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301720] = {
id = 301720,
stackedNum = 999999,
maxNum = 999999,
name = "墨子体验卡（7天）",
desc = "免费体验7天峡谷英雄墨子（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1020",
useType = v3,
useParam = {
1020
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301721] = {
id = 301721,
stackedNum = 999999,
maxNum = 999999,
name = "公孙离体验卡（7天）",
desc = "免费体验7天峡谷英雄公孙离（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1021",
useType = v3,
useParam = {
1021
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301722] = {
id = 301722,
stackedNum = 999999,
maxNum = 999999,
name = "东皇太一体验卡（7天）",
desc = "免费体验7天峡谷英雄东皇太一（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1022",
useType = v3,
useParam = {
1022
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301723] = {
id = 301723,
stackedNum = 999999,
maxNum = 999999,
name = "狄仁杰体验卡（7天）",
desc = "免费体验7天峡谷英雄狄仁杰（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1023",
useType = v3,
useParam = {
1023
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301724] = {
id = 301724,
stackedNum = 999999,
maxNum = 999999,
name = "花木兰体验卡（7天）",
desc = "免费体验7天峡谷英雄花木兰（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1024",
useType = v3,
useParam = {
1024
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301725] = {
id = 301725,
stackedNum = 999999,
maxNum = 999999,
name = "妲己体验卡（7天）",
desc = "免费体验7天峡谷英雄妲己（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1025",
useType = v3,
useParam = {
1025
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301726] = {
id = 301726,
stackedNum = 999999,
maxNum = 999999,
name = "曜体验卡（7天）",
desc = "免费体验7天峡谷英雄曜（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1026",
useType = v3,
useParam = {
1026
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301727] = {
id = 301727,
stackedNum = 999999,
maxNum = 999999,
name = "马可波罗体验卡（7天）",
desc = "免费体验7天峡谷英雄马可波罗（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1027",
useType = v3,
useParam = {
1027
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301728] = {
id = 301728,
stackedNum = 999999,
maxNum = 999999,
name = "夏侯惇体验卡（7天）",
desc = "免费体验7天峡谷英雄夏侯惇（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1028",
useType = v3,
useParam = {
1028
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[301729] = {
id = 301729,
stackedNum = 999999,
maxNum = 999999,
name = "张良体验卡（7天）",
desc = "免费体验7天峡谷英雄张良（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1029",
useType = v3,
useParam = {
1029
},
arenaHeroTryCardConf = v4,
autoUse = true,
isShowPopRewardView = 1
},
[302008] = {
id = 302008,
type = "ItemType_Arena_Equip",
name = "高能应援 阿轲",
desc = "（限峡谷模式使用）获取后可以获得阿轲的专属武器。",
icon = "T_Arena_Skin_35_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1035,
1091
},
resourceConf = {
model = "SK_PL_Prop_252_01_moba",
modelType = 2
},
scaleTimes = 100,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
20,
-70
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302009] = {
id = 302009,
type = "ItemType_Arena_Equip",
name = "用爱发电 典韦",
desc = "（限峡谷模式使用）获取后可以获得典韦的专属武器。",
icon = "T_Arena_Skin_36_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1036,
1092
},
resourceConf = {
model = "SK_PL_Prop_234_01_moba",
modelType = 2
},
scaleTimes = 35,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-50
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302011] = {
id = 302011,
type = "ItemType_Arena_Equip",
name = "深空刃 曜",
desc = "（限峡谷模式使用）获取后可以获得曜的专属武器。",
icon = "T_Arena_Skin_26_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1026,
1094
},
resourceConf = {
model = "SK_PL_Prop_223_01_moba",
modelType = 2
},
scaleTimes = 60,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-35
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302012] = {
id = 302012,
type = "ItemType_Arena_Equip",
name = "原物主义 孙悟空",
desc = "（限峡谷模式使用）获取后可以获得孙悟空的专属武器。",
icon = "T_Arena_Skin_16_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1016,
1095
},
resourceConf = {
model = "SK_PL_Prop_130_01_moba",
modelType = 2
},
scaleTimes = 50,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-15
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302013] = {
id = 302013,
type = "ItemType_Arena_Equip",
name = "天与地 宫本武藏",
desc = "（限峡谷模式使用）获取后可以获得宫本武藏的专属武器。",
icon = "T_Arena_Skin_34_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1034,
1096
},
resourceConf = {
model = "SK_PL_Prop_225_01_moba",
modelType = 2
},
scaleTimes = 60,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
20,
-10
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302014] = {
id = 302014,
type = "ItemType_Arena_Equip",
name = "华清遗韵 杨玉环",
desc = "（限峡谷模式使用）获取后可以获得杨玉环的专属武器。",
icon = "T_Arena_Skin_45_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1045,
1097
},
resourceConf = {
model = "SK_PL_Prop_261_01_moba",
modelType = 2
},
scaleTimes = 90,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-70
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302015] = {
id = 302015,
type = "ItemType_Arena_Equip",
quality = 2,
name = "噬日之弓 后羿",
desc = "（限峡谷模式使用）获取后可以获得后羿的专属武器。",
icon = "T_Arena_Skin_12_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1012,
1098
},
resourceConf = {
model = "SK_PL_Prop_156_01_moba",
modelType = 2
},
scaleTimes = 60,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302016] = {
id = 302016,
type = "ItemType_Arena_Equip",
quality = 2,
name = "豪龙枪 赵云",
desc = "（限峡谷模式使用）获取后可以获得赵云的专属武器。",
icon = "T_Arena_Skin_10_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1010,
1099
},
resourceConf = {
model = "SK_PL_Prop_126_01_moba",
modelType = 2
},
scaleTimes = 60,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-10
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302017] = {
id = 302017,
type = "ItemType_Arena_Equip",
quality = 2,
name = "沉眠之心 瑶",
desc = "（限峡谷模式使用）获取后可以获得瑶的专属武器。",
icon = "T_Arena_Skin_6_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1006,
1100
},
resourceConf = {
model = "SK_PL_Prop_023_01_moba",
modelType = 2
},
scaleTimes = 90,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-100
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302018] = {
id = 302018,
type = "ItemType_Arena_Equip",
quality = 2,
name = "梦幻泡影 孙尚香",
desc = "（限峡谷模式使用）获取后可以获得孙尚香的专属武器。",
icon = "T_Arena_Skin_14_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1014,
1101
},
resourceConf = {
model = "SK_PL_Prop_129_01_moba",
modelType = 2
},
scaleTimes = 100,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[305006] = {
id = 305006,
stackedNum = 999999,
maxNum = 999999,
quality = 1,
name = "彩色星运宝箱",
desc = "包含丰富奖励，宝箱稀有度越高，奖励品质越高。",
icon = "CDN:CT_Arena_Icon_ShopBox6",
picture = "T_Arena_Img_CardBox5",
useType = "IUTO_ArenaCardPack",
useParam = {
1,
1026
}
},
[303139] = {
id = 303139,
quality = 1,
name = "花开富贵弹",
picture = v5,
useParam = {
1042008
},
autoUse = true
},
[303140] = {
id = 303140,
quality = 1,
name = "白云出岫",
picture = v5,
useParam = {
1038008
},
autoUse = true
},
[303141] = {
id = 303141,
quality = 1,
name = "虹销雨霁",
picture = v5,
useParam = {
1038016
},
autoUse = true
},
[303142] = {
id = 303142,
quality = 1,
name = "混世巨斧",
picture = v5,
useParam = {
1030008
},
autoUse = true
},
[303143] = {
id = 303143,
quality = 1,
name = "逢凶化吉",
picture = v5,
useParam = {
1030015
},
autoUse = true
},
[303144] = {
id = 303144,
quality = 1,
name = "无畏级堡垒",
picture = v5,
useParam = {
1043017
},
autoUse = true
},
[303145] = {
id = 303145,
quality = 2,
name = "情比金坚",
picture = v5,
useParam = {
1001022
},
autoUse = true
},
[303146] = {
id = 303146,
quality = 2,
name = "猎胜持继",
picture = v5,
useParam = {
1002022
},
autoUse = true
},
[303147] = {
id = 303147,
quality = 2,
name = "双面魔咒",
picture = v5,
useParam = {
1003006
},
autoUse = true
},
[303148] = {
id = 303148,
quality = 2,
name = "弹无虚发",
picture = v5,
useParam = {
1004012
},
autoUse = true
},
[303149] = {
id = 303149,
quality = 2,
name = "重影",
picture = v5,
useParam = {
1005017
},
autoUse = true
},
[303150] = {
id = 303150,
quality = 2,
name = "守护光环",
picture = v5,
useParam = {
1006019
},
autoUse = true
},
[303151] = {
id = 303151,
quality = 2,
name = "舞至心尖",
picture = v5,
useParam = {
1007012
},
autoUse = true
},
[303152] = {
id = 303152,
quality = 2,
name = "不屈骑士",
picture = v5,
useParam = {
1011024
},
autoUse = true
},
[303153] = {
id = 303153,
quality = 2,
name = "光之裁决",
picture = v5,
useParam = {
1012018
},
autoUse = true
},
[303154] = {
id = 303154,
quality = 2,
name = "根缠沙结",
picture = v5,
useParam = {
1013015
},
autoUse = true
},
[303155] = {
id = 303155,
quality = 2,
name = "冲锋陷阵",
picture = v5,
useParam = {
1010012
},
autoUse = true
},
[303156] = {
id = 303156,
quality = 2,
name = "落英缤纷",
picture = v5,
useParam = {
1019013
},
autoUse = true
},
[303157] = {
id = 303157,
quality = 2,
name = "手到擒来",
picture = v5,
useParam = {
1017010
},
autoUse = true
},
[303158] = {
id = 303158,
quality = 2,
name = "舞徘徊",
picture = v5,
useParam = {
1021006
},
autoUse = true
},
[303159] = {
id = 303159,
quality = 2,
name = "心如松柏",
picture = v5,
useParam = {
1018024
},
autoUse = true
},
[303160] = {
id = 303160,
quality = 2,
name = "迫击远袭",
picture = v5,
useParam = {
1020007
},
autoUse = true
},
[303161] = {
id = 303161,
quality = 2,
name = "强制过载",
picture = v5,
useParam = {
1016017
},
autoUse = true
},
[303162] = {
id = 303162,
quality = 2,
name = "契约之力",
picture = v5,
useParam = {
1022017
},
autoUse = true
},
[303163] = {
id = 303163,
quality = 2,
name = "奇点逆生",
picture = v5,
useParam = {
1023013
},
autoUse = true
},
[303164] = {
id = 303164,
quality = 2,
name = "蓄意",
picture = v5,
useParam = {
1024006
},
autoUse = true
},
[303165] = {
id = 303165,
quality = 1,
name = "心弦激荡",
picture = v5,
useParam = {
1001016
},
autoUse = true
},
[303166] = {
id = 303166,
quality = 1,
name = "心语回舟",
picture = v5,
useParam = {
1001023
},
autoUse = true
},
[303167] = {
id = 303167,
quality = 1,
name = "锐不可当",
picture = v5,
useParam = {
1002007
},
autoUse = true
},
[303168] = {
id = 303168,
quality = 1,
name = "电磁回充",
picture = v5,
useParam = {
1002023
},
autoUse = true
},
[303169] = {
id = 303169,
quality = 1,
name = "汪汪冲呀",
picture = v5,
useParam = {
1003008
},
autoUse = true
},
[303170] = {
id = 303170,
quality = 1,
name = "恣意妄为",
picture = v5,
useParam = {
1003024
},
autoUse = true
},
[303171] = {
id = 303171,
quality = 1,
name = "特快补给",
picture = v5,
useParam = {
1004019
},
autoUse = true
},
[303172] = {
id = 303172,
quality = 1,
name = "易燃易炸",
picture = v5,
useParam = {
1004024
},
autoUse = true
},
[303173] = {
id = 303173,
quality = 1,
name = "铭心之影",
picture = v5,
useParam = {
1005015
},
autoUse = true
},
[303174] = {
id = 303174,
quality = 1,
name = "亢龙之悔",
picture = v5,
useParam = {
1005022
},
autoUse = true
},
[303175] = {
id = 303175,
quality = 1,
name = "云梦泽的风",
picture = v5,
useParam = {
1006013
},
autoUse = true
},
[303176] = {
id = 303176,
quality = 1,
name = "潇潇木",
picture = v5,
useParam = {
1006016
},
autoUse = true
},
[303177] = {
id = 303177,
quality = 1,
name = "曼舞轻歌",
picture = v5,
useParam = {
1007003
},
autoUse = true
},
[303178] = {
id = 303178,
quality = 1,
name = "聚焦于你",
picture = v5,
useParam = {
1007016
},
autoUse = true
},
[303179] = {
id = 303179,
quality = 1,
name = "灼热圣光",
picture = v5,
useParam = {
1011001
},
autoUse = true
},
[303180] = {
id = 303180,
quality = 1,
name = "疗愈之光",
picture = v5,
useParam = {
1011003
},
autoUse = true
},
[303181] = {
id = 303181,
quality = 1,
name = "烈日河山",
picture = v5,
useParam = {
1012008
},
autoUse = true
},
[303182] = {
id = 303182,
quality = 1,
name = "金辉普照",
picture = v5,
useParam = {
1012019
},
autoUse = true
},
[303183] = {
id = 303183,
quality = 1,
name = "润物无声",
picture = v5,
useParam = {
1013011
},
autoUse = true
},
[303184] = {
id = 303184,
quality = 1,
name = "霜降润物",
picture = v5,
useParam = {
1013013
},
autoUse = true
},
[303185] = {
id = 303185,
quality = 1,
name = "背水之志",
picture = v5,
useParam = {
1010017
},
autoUse = true
},
[303186] = {
id = 303186,
quality = 1,
name = "常胜将军",
picture = v5,
useParam = {
1010021
},
autoUse = true
},
[303187] = {
id = 303187,
quality = 1,
name = "清世镇邪",
picture = v5,
useParam = {
1015015
},
autoUse = true
},
[303188] = {
id = 303188,
quality = 1,
name = "枪影舞飞花",
picture = v5,
useParam = {
1017013
},
autoUse = true
},
[303189] = {
id = 303189,
quality = 1,
name = "月照彩云归",
picture = v5,
useParam = {
1021008
},
autoUse = true
},
[303190] = {
id = 303190,
quality = 1,
name = "节用薄葬",
picture = v5,
useParam = {
1020013
},
autoUse = true
},
[303191] = {
id = 303191,
quality = 1,
name = "五雷神通",
picture = v5,
useParam = {
1016018
},
autoUse = true
},
[303192] = {
id = 303192,
quality = 1,
name = "真理圆环",
picture = v5,
useParam = {
1023010
},
autoUse = true
},
[301229] = {
id = 301229,
stackedNum = 999999,
maxNum = 999999,
name = "大乔体验卡（1天）",
desc = "免费体验1天峡谷英雄大乔（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1039",
useType = v3,
useParam = {
1039
},
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
},
[301230] = {
id = 301230,
stackedNum = 999999,
maxNum = 999999,
name = "海月体验卡（1天）",
desc = "免费体验1天峡谷英雄海月（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1046",
useType = v3,
useParam = {
1046
},
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
}
}

local mt = {
type = "ItemType_AutoUse",
stackedNum = 1,
maxNum = 1,
quality = 3,
desc = "峡谷英雄专属卡牌",
icon = "T_Arena_Icon_ShopCard",
useType = "IUTO_ArenaCard",
pakGroup = "50021",
autoUse = false,
isShowPopRewardView = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data