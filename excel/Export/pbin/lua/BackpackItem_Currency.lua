--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表.xlsx: 货币

local v0 = 3

local v1 = {
{
itemId = 4,
itemNum = 1
}
}

local data = {
[1] = {
id = 1,
effect = true,
quality = 2,
name = "星钻",
desc = "珍贵的货币，可用于兑换所有商品。",
icon = "T_Common_Icon_Coin_02",
getWay = "充值",
jumpId = {
72
},
sort = 4,
useType = "IUTO_JumpPanel",
useParam = {
661
},
plusjumpId = 8
},
[2] = {
id = 2,
effect = true,
quality = 2,
name = "星愿币",
desc = "稀有货币，用于参加赛季祈愿活动。",
icon = "T_Common_Icon_Coin_04",
getWay = "购买祈愿礼包;奖杯征程",
jumpId = {
6,
234
},
sort = 5,
useType = "IUTO_JumpPanel",
useParam = {
661
},
commodityId = 1,
plusjumpId = 6
},
[3] = {
id = 3,
effect = true,
quality = 2,
name = "幸运币",
desc = "稀有货币，可用于参加祈愿活动。",
icon = "T_Common_Icon_Coin_07",
getWay = "购买元梦礼包",
jumpId = {
6
},
sort = 6,
useType = "IUTO_JumpPanel",
useParam = {
8888
},
commodityId = 2,
plusjumpId = 6
},
[4] = {
id = 4,
effect = true,
quality = 3,
name = "星宝印章",
desc = "普通货币，用于参加印章许愿活动。",
icon = "T_Common_Icon_Coin_05",
getWay = "奖杯征程;参与对局;前往活动系统",
jumpId = {
234,
1,
20
},
sort = 56,
useType = "IUTO_JumpPanel",
useParam = {
705
},
plusjumpId = 234
},
[5] = {
id = 5,
effect = true,
quality = 3,
name = "夺冠奖章",
desc = "普通货币，用于参加奖章祈愿活动。",
icon = "T_Common_Icon_Coin_03",
getWay = "通过单局结算夺冠点数转换"
},
[6] = {
id = 6,
effect = true,
quality = 2,
name = "云朵币",
desc = "普通货币，可在商城内购买心仪商品。",
icon = "T_Common_Icon_Coin_01",
getWay = "星钻兑换;印章祈愿;奖杯征程",
jumpId = {
7,
705,
234
},
sort = 7,
useType = "IUTO_JumpPanel",
useParam = {
15
},
commodityId = 3,
plusjumpId = 7
},
[7] = {
id = 7,
effect = true,
expiredReplaceItem = v1,
name = "星光碎片",
desc = "用来兑换赛季商店的奖励。（达到每阶段获取上限后不可再获得）",
icon = "T_Common_Icon_Coin_06",
getWay = "完成赛季任务",
jumpId = {
3
},
plusjumpId = 3
},
[8] = {
id = 8,
effect = true,
quality = 2,
name = "通行证兑换券",
desc = "限时货币，仅限用于甜心乐园通行证兑换商店",
icon = "T_Common_Item_System_BagBig_047",
getWay = "通行证等级奖励",
bHideInBag = true
},
[9] = {
id = 9,
effect = true,
name = "星舞元宝",
desc = "用来兑换星舞龙游活动奖励。",
icon = "T_Common_Icon_Coin_39",
getWay = "完成星舞龙游活动任务",
bHideInBag = true
},
[10] = {
id = 10,
effect = true,
name = "许愿券",
desc = "用来在星灯许愿活动中许愿。",
icon = "T_Common_Item_System_Bag_053",
getWay = "完成星灯许愿活动任务",
bHideInBag = true
},
[11] = {
id = 11,
effect = true,
name = "加速器",
desc = "用来在全速前进活动中抽奖。",
icon = "T_Common_Item_System_Bag_053",
getWay = "完成全速前进活动任务",
bHideInBag = true
},
[12] = {
id = 12,
effect = true,
name = "祈梦星",
desc = "用于7月5日开启的寻梦嘉年华活动“寻梦之旅”。",
icon = "T_Common_Icon_Coin_96",
getWay = "参与寻梦嘉年华相关活动",
bHideInBag = true
},
[13] = {
id = 13,
effect = true,
quality = 2,
name = "狼人币",
desc = "用来在【谁是狼人】玩法中兑换奖励。",
icon = "T_E3_Icon_WolfCoin_001",
getWay = "狼人通行证;大师之路",
jumpId = {
331,
330
}
},
[14] = {
id = 14,
effect = true,
quality = 2,
name = "幻梦币",
desc = "稀有货币，可用于参加《王者荣耀》联动时装的祈愿活动",
icon = "T_Common_Item_System_Arena",
getWay = "购买幻梦币礼包",
jumpId = {
6
},
sort = 10,
useType = "IUTO_JumpPanel",
useParam = {
10711
},
commodityId = 14,
plusjumpId = 6
},
[501] = {
id = 501,
effect = true,
name = "乐之叶",
desc = "用以兑换部分元件。（在星家园的装修模式下，放置自然类别中的摇钱树，之后遵循摇树规则每天摇树获取。）",
icon = "T_Manor_Icon_Coin_01",
getWay = "摇晃乐之树(摇钱树);奖杯征程",
commodityId = 11,
plusjumpId = 7
},
[502] = {
id = 502,
effect = true,
quality = 3,
name = "梦之叶",
desc = "用以兑换部分元件。（在星家园的装修模式下，放置自然类别中的摇钱树，之后遵循摇树规则每天摇树获取。）",
icon = "T_Manor_Icon_Coin_02",
getWay = "摇晃梦之树(摇钱树);奖杯征程",
commodityId = 12,
plusjumpId = 7
},
[503] = {
id = 503,
effect = true,
name = "种植积分",
desc = "用以提升种植图鉴等级。（点击种植图鉴中的植物图标，了解获取种植积分的条件。）",
icon = "T_Common_Icon_Coin_56",
getWay = "种植图鉴",
bHideInBag = true
},
[601] = {
id = 601,
effect = true,
name = "水晶",
desc = "用来在“塔防大亨”模式中购买“水晶商店”道具的货币",
icon = "T_OMD_Coin_Icon_Gem",
getWay = "“塔防大亨”模式",
bHideInBag = true
},
[602] = {
id = 602,
effect = true,
name = "皇冠",
desc = "用来在“兽人必须死”塔防模式中升级装备的道具",
icon = "T_OMD_Coin_Icon_Crown",
getWay = "奖杯征程;“兽人必须死”模式",
bHideInBag = true
},
[603] = {
id = 603,
effect = true,
name = "兽人币",
desc = "用来在“兽人必须死”塔防模式中付费消耗的道具",
icon = "T_OMD_Coin_Icon_OrcCoins",
getWay = "“兽人必须死”模式",
bHideInBag = true
},
[1001] = {
id = 1001,
effect = true,
name = "经验值",
desc = "用于提升等级。",
icon = "T_Common_Item_System_BagBig_011",
getWay = "参与对局、完成任务",
bHideInBag = true
},
[1002] = {
id = 1002,
effect = true,
name = "日活跃度",
desc = "日常任务获得活跃度。",
icon = "T_Common_Icon_Coin_09",
getWay = "日常任务",
jumpId = {
35
},
bHideInBag = true
},
[1003] = {
id = 1003,
effect = true,
name = "周活跃度",
desc = "参与周常任务获得活跃度。",
icon = "T_Common_Icon_Coin_10",
getWay = "周常任务",
bHideInBag = true
},
[1004] = {
id = 1004,
effect = true,
name = "赛季历练值",
desc = "通过赛季历练任务获得。",
icon = "T_Common_Icon_Coin_08",
getWay = "赛季历练任务",
bHideInBag = true
},
[1005] = {
id = 1005,
effect = true,
name = "通行证经验值",
desc = "用于提升赛季通行证等级",
icon = "T_BattlePass_Icon_NoteBook",
getWay = "每日奖杯进度奖励，通行证任务",
bHideInBag = true
},
[1006] = {
id = 1006,
effect = true,
quality = 2,
name = "关卡成就铜奖杯",
desc = "展示",
icon = "T_Item_StainedFruit_Universal",
getWay = "关卡成就任务（已废弃）",
bHideInBag = true
},
[1007] = {
id = 1007,
effect = true,
quality = 2,
name = "关卡成就银奖杯",
desc = "展示",
icon = "T_Item_StainedFruit_Universal",
getWay = "关卡成就任务（已废弃）",
bHideInBag = true
},
[1008] = {
id = 1008,
effect = true,
quality = 2,
name = "关卡成就金奖杯",
desc = "展示",
icon = "T_Item_StainedFruit_Universal",
getWay = "关卡成就任务（已废弃）",
bHideInBag = true
},
[1009] = {
id = 1009,
effect = true,
quality = 2,
name = "关卡成就白金奖杯",
desc = "展示",
icon = "T_Item_StainedFruit_Universal",
getWay = "关卡成就任务（已废弃）",
bHideInBag = true
},
[1010] = {
id = 1010,
effect = true,
name = "星光烟花",
desc = "在烟花秀进行期间，可使用星光烟花发射烟花。",
icon = "T_Common_Icon_Coin_25",
getWay = "商城",
jumpId = {
7
},
commodityId = 100009,
plusjumpId = 7
},
[1011] = {
id = 1011,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
},
[1012] = {
id = 1012,
effect = true,
name = "奥特曼光之粒子",
desc = "用于奥特曼活动进度收集",
icon = "T_Common_Icon_Coin_09",
getWay = "奥特曼活动",
bHideInBag = true
},
[1013] = {
id = 1013,
effect = true,
name = "奥特曼小队友情值",
desc = "用于奥特曼小队任务",
icon = "T_Common_Icon_Coin_78",
getWay = "奥特曼小队任务",
bHideInBag = true
},
[1014] = {
id = 1014,
effect = true,
name = "通行证经验值",
desc = "用于提升【谁是狼人】赛季通行证等级",
icon = "T_E3_BattlePass_Icon_NoteBook",
getWay = "【谁是狼人】通行证任务",
bHideInBag = true
},
[1015] = {
id = 1015,
effect = true,
name = "通行证经验值",
desc = "用于提升【峡谷相逢】赛季通行证等级",
icon = "CDN:T_Arena_Icon_Coin_003",
getWay = "【峡谷相逢】通行证任务",
bHideInBag = true
},
[2001] = {
id = 2001,
effect = true,
name = "友情小队每日羁绊值",
desc = "展示",
icon = "T_Item_StainedFruit_Universal",
getWay = "友情小队任务",
bHideInBag = true
},
[2002] = {
id = 2002,
effect = true,
name = "友情小队羁绊值",
desc = "展示",
icon = "T_Item_StainedFruit_Universal",
getWay = "友情小队任务",
bHideInBag = true
},
[2003] = {
id = 2003,
effect = true,
name = "回归任务值",
desc = "展示",
icon = "T_Item_StainedFruit_Universal",
getWay = "完成回归任务",
bHideInBag = true
},
[2004] = {
id = 2004,
effect = true,
expiredReplaceItem = v1,
name = "限时福利值",
desc = "用于兑换限时福利赏奖励。",
icon = "T_Common_Icon_Coin_11",
getWay = "完成限时福利赏活动任务",
bHideInBag = true
},
[2005] = {
id = 2005,
effect = true,
expiredReplaceItem = v1,
name = "随心之花",
desc = "用于兑换随心挑战奖励。",
icon = "T_Exchange_Icon_001",
getWay = "随心挑战礼任务",
bHideInBag = true
},
[2006] = {
id = 2006,
effect = true,
expiredReplaceItem = v1,
name = "惊喜四叶草",
desc = "用于兑换小新幸运礼活动奖励。",
icon = "T_Common_Icon_Coin_12",
getWay = "完成小新幸运礼活动任务",
bHideInBag = true
},
[2007] = {
id = 2007,
effect = true,
expiredReplaceItem = v1,
quality = 3,
name = "印章祈愿兑换券",
desc = "用于兑换印章祈愿商城奖励。",
icon = "T_Common_Icon_Coin_14",
getWay = "印章祈愿抽奖",
sort = 59,
useType = "IUTO_JumpPanel",
useParam = {
705
}
},
[2008] = {
id = 2008,
effect = true,
expiredReplaceItem = v1,
name = "星运气球",
desc = "用于小雪接星运活动。",
icon = "T_Common_Item_System_Bag_040",
getWay = "小雪接星运活动",
bHideInBag = true
},
[2009] = {
id = 2009,
effect = true,
name = "奶油草莓 ",
desc = "用于一起露营吧的兑换货币。",
icon = "T_Common_Icon_Coin_48",
getWay = "一起露营吧活动",
bHideInBag = true
},
[2010] = {
id = 2010,
effect = true,
name = "外卖钥匙",
desc = "用于一起露营吧解锁宝箱的货币。",
icon = "T_Common_Icon_Coin_13",
getWay = "一起露营吧活动",
bHideInBag = true
},
[101] = {
id = 101,
effect = true,
expiredReplaceItem = v1,
quality = 3,
name = "闯关挑战金币",
desc = "每赛季可以在闯关挑战商店中兑换奖励。",
icon = "T_Common_Icon_Coin_16",
getWay = "参与闯关挑战(星世界)"
},
[102] = {
id = 102,
effect = true,
name = "星海巡游生命数",
desc = "每日剩余生命。",
icon = "T_UGC_Finish_Icon_Blood",
getWay = "每日自动",
bHideInBag = true
},
[103] = {
id = 103,
effect = true,
name = "星海巡游银币",
desc = "每赛季可以在巡游商店中兑换奖励。",
icon = "T_Common_Icon_Coin_15",
getWay = "不再掉落，已有银币仍可在商店中兑换",
bHideInBag = true
},
[104] = {
id = 104,
quality = 3,
name = "闯关挑战积分",
desc = "每赛季累计积分达标后可以获得稀有奖励。",
icon = "T_StarCruise_Img_Coin",
getWay = "参与闯关挑战(星世界)"
},
[105] = {
id = 105,
quality = 3,
name = "星世界活跃度",
desc = "星世界活跃度，用于提升星世界等级。",
icon = "T_UGCGrade_Icon_Badge07_01",
getWay = "游玩星世界指定页签",
bHideInBag = true
},
[201] = {
id = 201,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "限时星愿币",
desc = "限时货币，仅限用于桃源悠梦赛季祈愿活动",
icon = "T_Common_Icon_Coin_27",
getWay = "参与赛季祈愿-星梦蝴蝶活动",
bHideInBag = true
},
[202] = {
id = 202,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "星梦蝴蝶",
desc = "用于参与星梦蝴蝶转盘的货币。",
icon = "T_Common_Item_System_Bag_022",
getWay = "参与赛季祈愿",
bHideInBag = true
},
[203] = {
id = 203,
effect = true,
expiredReplaceItem = v1,
quality = 3,
name = "星芒券",
desc = "用于在祈愿活动中兑换商品，祈愿活动结束后不会清空",
icon = "T_Common_Icon_Coin_28",
getWay = "参与冰雪圆舞曲祈愿",
jumpId = {
503
}
},
[204] = {
id = 204,
effect = true,
expiredReplaceItem = v1,
name = "拼图碎片",
desc = "用于参与活动一元幸启的活动道具",
icon = "T_LuckBuy_Img_Splinter_03",
getWay = "用于参与活动一元幸启的活动道具",
bHideInBag = true
},
[205] = {
id = 205,
effect = true,
quality = 3,
name = "星光剧场兑换券",
desc = "用于在星光剧场祈愿中兑换商品，祈愿活动结束后不会清空",
icon = "T_Common_Icon_Coin_26",
getWay = "参与星光剧场祈愿",
jumpId = {
707
},
sort = 64,
useType = "IUTO_JumpPanel",
useParam = {
707
}
},
[208] = {
id = 208,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "限时星愿币",
desc = "限时货币，仅限用于山海奇遇赛季祈愿活动",
icon = "T_Common_Icon_Coin_27",
getWay = "限时活动",
bHideInBag = true
},
[209] = {
id = 209,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "限时星愿币",
desc = "限时货币，仅限用于时光漫游赛季祈愿活动",
icon = "T_Common_Icon_Coin_27",
getWay = "限时活动",
bHideInBag = true
},
[210] = {
id = 210,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "限时星愿币",
desc = "限时货币，仅限用于潮音畅想赛季祈愿活动",
icon = "T_Common_Icon_Coin_27",
getWay = "限时活动",
bHideInBag = true
},
[211] = {
id = 211,
effect = true,
quality = 3,
name = "心梦券",
desc = "用于在祈愿活动中兑换商品，祈愿活动结束后不会清空",
icon = "T_Common_Icon_BelovedVows",
getWay = "参与永恒之誓祈愿获得",
jumpId = {
175
}
},
[212] = {
id = 212,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "限时星愿币",
desc = "限时货币，仅限用于缤纷夏日赛季祈愿活动",
icon = "T_Common_Icon_Coin_27",
getWay = "通过限时活动活动",
bHideInBag = true
},
[214] = {
id = 214,
effect = true,
expiredReplaceItem = v1,
quality = 2,
name = "限时星愿币",
desc = "限时货币，仅限用于本赛季祈愿活动",
icon = "T_Common_Icon_Coin_27",
getWay = "通过限时活动",
sort = 22,
useType = "IUTO_JumpPanel",
useParam = {
661
}
},
[215] = {
id = 215,
effect = true,
expiredReplaceItem = v1,
quality = 3,
name = "幸运星",
desc = "用于在幸运星兑换活动中兑换，活动结束后不会清空",
icon = "T_Common_Icon_Coin_92",
getWay = "参与幸运星兑换祈愿活动",
jumpId = {
185,
79
}
},
[216] = {
id = 216,
effect = true,
quality = 3,
name = "相思券",
desc = "用于在长相思祈愿中兑换长相思系列商品",
icon = "T_Common_Icon_Coin_104",
getWay = "参与长相思祈愿",
jumpId = {
1054
},
bHideInBag = true
},
[219] = {
id = 219,
effect = true,
quality = 3,
name = "幸运星",
desc = "用于许愿星祈愿中兑换商品，祈愿活动结束后不会清空",
icon = "T_Common_Icon_Coin_92",
getWay = "参与许愿星祈愿",
jumpId = {
1057
}
},
[217] = {
id = 217,
effect = true,
quality = 3,
name = "凤之钥",
desc = "用于开启凤凰宝匣",
icon = "T_Swing_Icon_Exchange",
getWay = "参与夏夜绮梦祈愿",
jumpId = {
610
},
bHideInBag = true
},
[218] = {
id = 218,
effect = true,
quality = 3,
name = "峡谷券",
desc = "用于在《王者荣耀》联动时装的祈愿活动中兑换商品，祈愿活动结束后不会清空",
icon = "T_Common_Icon_Coin_106",
getWay = "参与祈愿活动",
jumpId = {
10718
},
sort = 70,
useType = "IUTO_JumpPanel",
useParam = {
10711
}
},
[220] = {
id = 220,
effect = true,
quality = 2,
name = "狼人福利券",
desc = "用于在【谁是狼人】的兑换商店中兑换奖励",
icon = "T_E3_Icon_WolfCoin_002",
getWay = "通过限时活动"
},
[221] = {
id = 221,
effect = true,
quality = 3,
name = "甜食券",
desc = "用于在吉伊卡哇祈愿中兑换吉伊卡哇系列商品",
icon = "T_Common_Icon_Coin_107",
getWay = "参与吉伊卡哇祈愿",
jumpId = {
1054
},
bHideInBag = true
},
[222] = {
id = 222,
effect = true,
quality = 3,
name = "星之钥",
desc = "用于开启星愿宝匣",
icon = "T_Common_Item_Swing_Key",
getWay = "参与星之恋空祈愿",
jumpId = {
628
},
bHideInBag = true
},
[223] = {
id = 223,
effect = true,
quality = 3,
name = "冰晶",
desc = "在冰雪赐福活动中提升冰晶数量，可获得奖励",
icon = "T_Common_Item_System_Bag_Snow",
getWay = "购买礼包或完成任务",
jumpId = {
628
},
bHideInBag = true
},
[224] = {
id = 224,
effect = true,
quality = 3,
name = "炫彩碎片",
desc = "用于在祈愿活动中兑换商品，祈愿活动结束后不会清空",
icon = "T_Common_Item_System_AscendingStone_Fragment",
getWay = "参与云梦绮旅祈愿获得",
jumpId = {
634
}
},
[225] = {
id = 225,
effect = true,
quality = 1,
name = "炫彩水晶",
desc = "用于载具升级和在祈愿活动中兑换载具，祈愿活动结束后不会清空",
icon = "T_Common_Item_System_AscendingStone",
getWay = "参与云梦绮旅祈愿获得",
jumpId = {
634
},
sort = 1,
useType = "IUTO_JumpPanel",
useParam = {
634
}
},
[226] = {
id = 226,
effect = true,
quality = 3,
name = "心之钥",
desc = "用于开启心语宝匣",
icon = "T_Common_Item_System_Bag_115",
getWay = "参与岚汀之约祈愿",
jumpId = {
638
},
bHideInBag = true
},
[2011] = {
id = 2011,
effect = true,
expiredReplaceItem = v1,
name = "娱乐福利值",
desc = "用于兑换娱乐福利赏奖励。",
icon = "T_Common_Icon_Coin_19",
getWay = "完成娱乐福利赏活动任务",
bHideInBag = true
},
[2012] = {
id = 2012,
effect = true,
name = "星动投票券",
desc = "用于参与星动投票活动并兑换奖励。",
icon = "T_Common_Item_System_Bag_050",
getWay = "完成星动投票活动任务",
bHideInBag = true
},
[2013] = {
id = 2013,
effect = true,
name = "星宝来信",
desc = "用于参与初星代言人活动并兑换奖励。",
icon = "T_Common_Icon_Coin_20",
getWay = "完成初星代言人活动任务",
bHideInBag = true
},
[2014] = {
id = 2014,
effect = true,
expiredReplaceItem = v1,
name = "星星饼干",
desc = "用于兑换小新幸运站活动奖励。",
icon = "T_Common_Item_System_Bag_039",
getWay = "完成小新幸运站活动任务",
bHideInBag = true
},
[2015] = {
id = 2015,
effect = true,
expiredReplaceItem = v1,
name = "梦幻初雪",
desc = "用于兑换期待满月见活动奖励。",
icon = "T_Common_Item_System_Bag_043",
getWay = "完成期待满月见活动任务",
bHideInBag = true
},
[2016] = {
id = 2016,
effect = true,
name = "冬日暖心值",
desc = "用于兑换大衣袄袄暖活动奖励。",
icon = "T_Common_Icon_Coin_22",
getWay = "完成大衣袄袄暖活动任务",
bHideInBag = true
},
[2017] = {
id = 2017,
effect = true,
expiredReplaceItem = v1,
name = "庆典铃铛",
desc = "用于兑换满月星动礼活动奖励。",
icon = "T_Common_Item_System_Bag_047",
getWay = "完成满月星动礼活动任务",
bHideInBag = true
},
[2018] = {
id = 2018,
effect = true,
expiredReplaceItem = v1,
name = "动感音符",
desc = "用于兑换元梦健康操活动奖励。",
icon = "T_Common_Icon_Coin_17",
getWay = "完成元梦健康操活动任务",
bHideInBag = true
},
[2019] = {
id = 2019,
effect = true,
name = "科普点",
desc = "用于公益满天星活动兑换奖励。",
icon = "T_PublicWelfare_Icon_04",
getWay = "通过公益满天星活动任务",
bHideInBag = true
},
[2020] = {
id = 2020,
effect = true,
name = "星运钥匙",
desc = "用于参加回流活动的钥匙。",
icon = "T_Common_Icon_Coin_13",
getWay = "回流活动",
bHideInBag = true
},
[2021] = {
id = 2021,
effect = true,
name = "友谊之火",
desc = "用于参加回流活动的钥匙。",
icon = "T_Common_Item_System_Bag_047",
getWay = "回流活动",
bHideInBag = true
},
[2022] = {
id = 2022,
effect = true,
name = "回流抽奖币",
desc = "用于参加回流活动的钥匙。",
icon = "T_Common_Icon_Coin_17",
getWay = "回流活动",
bHideInBag = true
},
[2023] = {
id = 2023,
effect = true,
name = "花花",
desc = "用于兑换雨琦试衣间活动奖励。",
icon = "T_Exchange_Icon_001",
getWay = "完成雨琦试衣间活动任务可",
bHideInBag = true
},
[2024] = {
id = 2024,
effect = true,
name = "音符",
desc = "用于兑换丞丞直播间力活动奖励。",
icon = "T_Common_Icon_Coin_17",
getWay = "完成丞丞直播间活动任务",
bHideInBag = true
},
[2025] = {
id = 2025,
effect = true,
name = "羽毛",
desc = "用于兑换大勋玉米地活动奖励。",
icon = "T_Common_Icon_Coin_22",
getWay = "完成大勋玉米地活动任务",
bHideInBag = true
},
[2026] = {
id = 2026,
effect = true,
expiredReplaceItem = v1,
name = "迎春福气值",
desc = "用于兑换迎春大焕新活动奖励。",
icon = "T_Common_Icon_Coin_47",
getWay = "完成迎春大焕新任务",
bHideInBag = true
},
[2027] = {
id = 2027,
effect = true,
name = "新年星运",
desc = "用于兑换元梦大舞台第一弹活动奖励。",
icon = "T_Common_Icon_Coin_76",
getWay = "完成元梦大舞台第一弹活动任务",
bHideInBag = true
},
[2028] = {
id = 2028,
effect = true,
name = "星运碎片",
desc = "用于兑换元梦大舞台第二弹活动奖励。",
icon = "T_Common_Icon_Coin_71",
getWay = "完成元梦大舞台第二弹活动任务",
bHideInBag = true
},
[2029] = {
id = 2029,
effect = true,
name = "星运图章",
desc = "用于兑换元梦大舞台第三弹活动奖励。",
icon = "T_Common_Icon_Coin_77",
getWay = "完成元梦大舞台第三弹任务",
bHideInBag = true
},
[2030] = {
id = 2030,
effect = true,
expiredReplaceItem = v1,
name = "光之星云",
desc = "用于兑换赛罗泽塔前来报到活动奖励。",
icon = "T_Common_Icon_Coin_11",
getWay = "完成赛罗泽塔前来报到任务",
bHideInBag = true
},
[3001] = {
id = 3001,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
},
[3002] = {
id = 3002,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
},
[3003] = {
id = 3003,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
},
[3004] = {
id = 3004,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
},
[3005] = {
id = 3005,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
},
[3006] = {
id = 3006,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
},
[3007] = {
id = 3007,
effect = true,
name = "赛事奖杯",
desc = "用于解锁赛事的奖杯奖励。",
icon = "T_CMPT_Icon_Grade_003",
getWay = "参与赛事比赛",
bHideInBag = true
}
}

local mt = {
effect = false,
type = "ItemType_Currency",
quality = 4,
bHideInBag = false,
jumpToast = "JumpToast_common"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data