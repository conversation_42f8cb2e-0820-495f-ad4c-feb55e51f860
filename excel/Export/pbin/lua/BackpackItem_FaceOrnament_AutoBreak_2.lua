--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 面饰

local v0 = {
{
itemId = 6,
itemNum = 50
}
}

local v1 = 3

local v2 = {
fashionValue = 125
}

local v3 = 260

local v4 = {
seconds = 4074768000
}

local data = {
[610202] = {
id = 610202,
effect = true,
name = "火力全开",
desc = "热血在燃烧！",
icon = "CDN:Icon_Mask_101_01",
outlookConf = {
belongTo = 610201,
fashionValue = 25,
belongToGroup = {
610202,
610203
}
},
resourceConf = {
model = "SM_Mask_101",
material = "MI_Mask_101_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51222,
scaleTimes = 250,
shareTexts = {
"跟我来，这把必赢"
}
},
[610203] = {
id = 610203,
effect = true,
name = "火力全开",
desc = "热血在燃烧！",
icon = "CDN:Icon_Mask_101_02",
outlookConf = {
belongTo = 610201,
fashionValue = 25,
belongToGroup = {
610202,
610203
}
},
resourceConf = {
model = "SM_Mask_101",
material = "MI_Mask_101_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51223,
scaleTimes = 250,
shareTexts = {
"跟我来，这把必赢"
}
},
[610204] = {
id = 610204,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "凤羽之舞",
desc = "轻柔的面纱下，是坚定不渝的心",
icon = "CDN:Icon_Mask_114",
getWay = "凤求凰祈愿",
jumpId = {
809
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_114",
modelType = 2,
idleAnim = "AS_Mask_114_idle_001"
},
shareTexts = {
"在重生之火中起舞"
},
minVer = "1.3.12.90",
beginTime = {
seconds = 1723132800
},
suitId = 50109,
suitName = "凤羽之舞",
suitIcon = "CDN:Icon_Mask_114"
},
[610205] = {
id = 610205,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "凤羽之舞",
desc = "轻柔的面纱下，是坚定不渝的心",
icon = "CDN:Icon_Mask_114_01",
outlookConf = {
belongTo = 610204,
fashionValue = 35,
belongToGroup = {
610205,
610206
}
},
resourceConf = {
model = "SK_Mask_114",
material = "MI_Mask_114_1_HP01;MI_Mask_114_2_HP01",
modelType = 2,
idleAnim = "AS_Mask_114_idle_001_HP01",
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51225,
shareTexts = {
"在重生之火中起舞"
}
},
[610206] = {
id = 610206,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "凤羽之舞",
desc = "轻柔的面纱下，是坚定不渝的心",
icon = "CDN:Icon_Mask_114_02",
outlookConf = {
belongTo = 610204,
fashionValue = 35,
belongToGroup = {
610205,
610206
}
},
resourceConf = {
model = "SK_Mask_114",
material = "MI_Mask_114_1_HP02;MI_Mask_114_2_HP02",
modelType = 2,
idleAnim = "AS_Mask_114_idle_001_HP02",
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51226,
shareTexts = {
"在重生之火中起舞"
}
},
[610207] = {
id = 610207,
effect = true,
name = "爆燃勇士",
desc = "热血燃烧，展现青春的活力",
icon = "CDN:Icon_Mask_116",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_116",
modelType = 1
},
scaleTimes = 250,
shareTexts = {
"没有什么可以阻挡我"
},
beginTime = v4,
suitId = 50110,
suitName = "爆燃勇士",
suitIcon = "CDN:Icon_Mask_116"
},
[610208] = {
id = 610208,
effect = true,
name = "爆燃勇士",
desc = "热血燃烧，展现青春的活力",
icon = "CDN:Icon_Mask_116_01",
outlookConf = {
belongTo = 610207,
fashionValue = 25,
belongToGroup = {
610208,
610209
}
},
resourceConf = {
model = "SM_Mask_116",
material = "MI_Mask_116_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51228,
scaleTimes = 250,
shareTexts = {
"没有什么可以阻挡我"
}
},
[610209] = {
id = 610209,
effect = true,
name = "爆燃勇士",
desc = "热血燃烧，展现青春的活力",
icon = "CDN:Icon_Mask_116_02",
outlookConf = {
belongTo = 610207,
fashionValue = 25,
belongToGroup = {
610208,
610209
}
},
resourceConf = {
model = "SM_Mask_116",
material = "MI_Mask_116_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51229,
scaleTimes = 250,
shareTexts = {
"没有什么可以阻挡我"
}
},
[610210] = {
id = 610210,
effect = true,
name = "猫猫面罩",
desc = "做个快乐的小奶猫",
icon = "CDN:Icon_Mask_111",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_111",
modelType = 1
},
scaleTimes = 250,
shareTexts = {
"谁来帮我挠痒痒？"
},
beginTime = v4,
suitId = 50111,
suitName = "猫猫面罩",
suitIcon = "CDN:Icon_Mask_111"
},
[610211] = {
id = 610211,
effect = true,
name = "猫猫面罩",
desc = "做个快乐的小奶猫",
icon = "CDN:Icon_Mask_111_01",
outlookConf = {
belongTo = 610210,
fashionValue = 25,
belongToGroup = {
610211,
610212
}
},
resourceConf = {
model = "SM_Mask_111",
material = "MI_Mask_111_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51231,
scaleTimes = 250,
shareTexts = {
"谁来帮我挠痒痒？"
}
},
[610212] = {
id = 610212,
effect = true,
name = "猫猫面罩",
desc = "做个快乐的小奶猫",
icon = "CDN:Icon_Mask_111_02",
outlookConf = {
belongTo = 610210,
fashionValue = 25,
belongToGroup = {
610211,
610212
}
},
resourceConf = {
model = "SM_Mask_111",
material = "MI_Mask_111_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51232,
scaleTimes = 250,
shareTexts = {
"谁来帮我挠痒痒？"
}
},
[610213] = {
id = 610213,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "爱心创可贴",
desc = "属于我的独家奖章",
icon = "CDN:Icon_Mask_113",
resourceConf = {
model = "SM_Mask_113",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"哎哟，被你发现了呀"
},
beginTime = v4,
suitId = 50112,
suitName = "爱心创可贴",
suitIcon = "CDN:Icon_Mask_113"
},
[610214] = {
id = 610214,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "独眼小宝",
desc = "月亮、太阳、森林和大海，尽在眼中",
icon = "CDN:Icon_Mask_119",
resourceConf = {
model = "SM_Mask_119",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"哎哟，被你发现了呀"
},
beginTime = v4,
suitId = 50113,
suitName = "独眼小宝",
suitIcon = "CDN:Icon_Mask_119"
},
[610215] = {
id = 610215,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "热力追踪",
desc = "健康小秘书，随时测量，随时了解",
icon = "CDN:Icon_Mask_120",
resourceConf = {
model = "SM_Mask_120",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"哎哟，被你发现了呀"
},
beginTime = v4,
suitId = 50114,
suitName = "热力追踪",
suitIcon = "CDN:Icon_Mask_120"
},
[610216] = {
id = 610216,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "还我小北鼻",
desc = "大家千万要爱惜自己的鼻子",
icon = "CDN:Icon_Mask_126",
resourceConf = {
model = "SM_Mask_126",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"哎哟，被你发现了呀"
},
beginTime = v4,
suitId = 50115,
suitName = "还我小北鼻",
suitIcon = "CDN:Icon_Mask_126"
},
[610217] = {
id = 610217,
effect = true,
name = "自然之灵",
desc = "拥有来自遥远星岛的神秘力量",
icon = "CDN:Icon_Mask_115",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_115",
modelType = 1
},
scaleTimes = 120,
shareTexts = {
"这下你找不到我了吧？"
},
minVer = "1.3.26.60",
beginTime = {
seconds = 1699113600
},
suitId = 50116,
suitName = "自然之灵",
suitIcon = "CDN:Icon_Mask_115"
},
[610218] = {
id = 610218,
effect = true,
name = "自然之灵",
desc = "拥有来自遥远星岛的神秘力量",
icon = "CDN:Icon_Mask_115_01",
outlookConf = {
belongTo = 610217,
fashionValue = 25,
belongToGroup = {
610218,
610219
}
},
resourceConf = {
model = "SM_Mask_115",
material = "MI_Mask_115_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51238,
scaleTimes = 120,
shareTexts = {
"这下你找不到我了吧？"
}
},
[610219] = {
id = 610219,
effect = true,
name = "自然之灵",
desc = "拥有来自遥远星岛的神秘力量",
icon = "CDN:Icon_Mask_115_02",
outlookConf = {
belongTo = 610217,
fashionValue = 25,
belongToGroup = {
610218,
610219
}
},
resourceConf = {
model = "SM_Mask_115",
material = "MI_Mask_115_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51239,
scaleTimes = 120,
shareTexts = {
"这下你找不到我了吧？"
}
},
[610220] = {
id = 610220,
effect = true,
name = "郁金流年",
desc = "希望每天都是甜甜的，冒着粉色泡泡",
icon = "CDN:Icon_Mask_121",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_121",
modelType = 1
},
scaleTimes = 120,
shareTexts = {
"坠入粉红色的甜美梦境"
},
minVer = "1.3.18.1",
beginTime = {
seconds = 1724947200
},
suitId = 50117,
suitName = "郁金流年",
suitIcon = "CDN:Icon_Mask_121"
},
[610221] = {
id = 610221,
effect = true,
name = "郁金流年",
desc = "希望每天都是甜甜的，冒着粉色泡泡",
icon = "CDN:Icon_Mask_121_01",
outlookConf = {
belongTo = 610220,
fashionValue = 25,
belongToGroup = {
610221,
610222
}
},
resourceConf = {
model = "SM_Mask_121",
material = "MI_Mask_121_1_HP01;MI_Mask_121_2_HP01",
modelType = 1,
materialSlot = "Mask;Mask_Translucent"
},
commodityId = 51241,
scaleTimes = 120,
shareTexts = {
"坠入粉红色的甜美梦境"
}
},
[610222] = {
id = 610222,
effect = true,
name = "郁金流年",
desc = "希望每天都是甜甜的，冒着粉色泡泡",
icon = "CDN:Icon_Mask_121_02",
outlookConf = {
belongTo = 610220,
fashionValue = 25,
belongToGroup = {
610221,
610222
}
},
resourceConf = {
model = "SM_Mask_121",
material = "MI_Mask_121_1_HP02;MI_Mask_121_2_HP02",
modelType = 1,
materialSlot = "Mask;Mask_Translucent"
},
commodityId = 51242,
scaleTimes = 120,
shareTexts = {
"坠入粉红色的甜美梦境"
}
},
[610223] = {
id = 610223,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "鸭趣横生",
desc = "鸭嘴一戴，笑容自来",
icon = "CDN:Icon_Mask_118",
resourceConf = {
model = "SM_Mask_118",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"哎哟，被你发现了呀"
},
beginTime = v4,
suitId = 50118,
suitName = "鸭趣横生",
suitIcon = "CDN:Icon_Mask_118"
},
[610224] = {
id = 610224,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "月夜潜行者",
desc = "也许是用来伪装的道具",
icon = "CDN:Icon_Mask_128",
resourceConf = {
model = "SM_Mask_128",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"哎哟，被你发现了呀"
},
beginTime = v4,
suitId = 50119,
suitName = "月夜潜行者",
suitIcon = "CDN:Icon_Mask_128"
},
[610225] = {
id = 610225,
effect = true,
name = "碧意轻扬",
desc = "没有两片相同的叶子，所以独一无二",
icon = "CDN:Icon_Mask_123",
getWay = "赛季通行证",
jumpId = {
9
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_123",
modelType = 1
},
scaleTimes = 250,
shareTexts = {
"会有人盯着绿叶看很久吗"
},
minVer = "1.3.18.1",
beginTime = {
seconds = 1724947200
},
suitId = 50120,
suitName = "碧意轻扬",
suitIcon = "CDN:Icon_Mask_123"
},
[610226] = {
id = 610226,
effect = true,
name = "碧意轻扬",
desc = "没有两片相同的叶子，所以独一无二",
icon = "CDN:Icon_Mask_123_01",
outlookConf = {
belongTo = 610225,
fashionValue = 25,
belongToGroup = {
610226,
610227
}
},
resourceConf = {
model = "SM_Mask_123",
material = "MI_Mask_123_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51246,
scaleTimes = 250,
shareTexts = {
"会有人盯着绿叶看很久吗"
}
},
[610227] = {
id = 610227,
effect = true,
name = "碧意轻扬",
desc = "没有两片相同的叶子，所以独一无二",
icon = "CDN:Icon_Mask_123_02",
outlookConf = {
belongTo = 610225,
fashionValue = 25,
belongToGroup = {
610226,
610227
}
},
resourceConf = {
model = "SM_Mask_123",
material = "MI_Mask_123_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51247,
scaleTimes = 250,
shareTexts = {
"会有人盯着绿叶看很久吗"
}
},
[610228] = {
id = 610228,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "轻语鸢梦",
desc = "轻柔的面纱下，是坚定不渝的心",
icon = "CDN:Icon_Mask_117",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_117",
modelType = 2,
idleAnim = "AS_Mask_idle_117_001"
},
shareTexts = {
"戴上它，步入轻盈璀璨的梦境"
},
minVer = "1.3.18.1",
beginTime = {
seconds = 1724947200
},
suitId = 50121,
suitName = "轻语鸢梦",
suitIcon = "CDN:Icon_Mask_117"
},
[610229] = {
id = 610229,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "轻语鸢梦",
desc = "轻柔的面纱下，是坚定不渝的心",
icon = "CDN:Icon_Mask_117_01",
outlookConf = {
belongTo = 610228,
fashionValue = 35,
belongToGroup = {
610229,
610230
}
},
resourceConf = {
model = "SK_Mask_117",
material = "MI_Mask_117_HP01;MI_Mask_117_1_HP01",
modelType = 2,
idleAnim = "AS_Mask_idle_117_001_HP01",
materialSlot = "Mask;Mask_Opaque"
},
commodityId = 51249,
shareTexts = {
"戴上它，步入轻盈璀璨的梦境"
}
},
[610230] = {
id = 610230,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "轻语鸢梦",
desc = "轻柔的面纱下，是坚定不渝的心",
icon = "CDN:Icon_Mask_117_02",
outlookConf = {
belongTo = 610228,
fashionValue = 35,
belongToGroup = {
610229,
610230
}
},
resourceConf = {
model = "SK_Mask_117",
material = "MI_Mask_117_HP02;MI_Mask_117_1_HP02",
modelType = 2,
idleAnim = "AS_Mask_idle_117_001_HP02",
materialSlot = "Mask;Mask_Opaque"
},
commodityId = 51250,
shareTexts = {
"戴上它，步入轻盈璀璨的梦境"
}
},
[610231] = {
id = 610231,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "游侠树叶",
desc = "情怀，懂不懂？",
icon = "CDN:Icon_Mask_140",
resourceConf = {
model = "SM_Mask_140",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50122,
suitName = "游侠树叶",
suitIcon = "CDN:Icon_Mask_140"
},
[610232] = {
id = 610232,
effect = true,
name = "玉叶琼枝",
desc = "折一枝桂花，把秋天和你分享",
icon = "CDN:Icon_Mask_122",
getWay = "桂月清平",
jumpId = {
363
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_122",
modelType = 1
},
scaleTimes = 250,
shareTexts = {
"下次见面，折枝桂花送你"
},
minVer = "1.3.18.37",
beginTime = {
seconds = 1726156800
},
suitId = 50123,
suitName = "玉叶琼枝",
suitIcon = "CDN:Icon_Mask_122",
shareOffset = {
50,
30
},
previewShareOffset = {
60,
0
}
},
[610233] = {
id = 610233,
effect = true,
name = "玉叶琼枝",
desc = "折一枝桂花，把秋天和你分享",
icon = "CDN:Icon_Mask_122_01",
outlookConf = {
belongTo = 610232,
fashionValue = 25,
belongToGroup = {
610233,
610234
}
},
resourceConf = {
model = "SM_Mask_122",
material = "MI_Mask_122_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51253,
scaleTimes = 250,
shareTexts = {
"下次见面，折枝桂花送你"
},
shareOffset = {
50,
30
},
previewShareOffset = {
60,
0
}
},
[610234] = {
id = 610234,
effect = true,
name = "玉叶琼枝",
desc = "折一枝桂花，把秋天和你分享",
icon = "CDN:Icon_Mask_122_02",
outlookConf = {
belongTo = 610232,
fashionValue = 25,
belongToGroup = {
610233,
610234
}
},
resourceConf = {
model = "SM_Mask_122",
material = "MI_Mask_122_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51254,
scaleTimes = 250,
shareTexts = {
"下次见面，折枝桂花送你"
},
shareOffset = {
50,
30
},
previewShareOffset = {
60,
0
}
},
[610235] = {
id = 610235,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "诚实之枝",
desc = "想说谎之前，先摸摸鼻子",
icon = "CDN:Icon_Mask_125",
resourceConf = {
model = "SM_Mask_125",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50124,
suitName = "诚实之枝",
suitIcon = "CDN:Icon_Mask_125"
},
[610236] = {
id = 610236,
effect = true,
name = "飞翼视界",
desc = "神秘魅影，看穿暗夜",
icon = "CDN:Icon_Mask_124",
getWay = "青霄龙吟",
jumpId = {
371
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_124",
modelType = 1
},
scaleTimes = 120,
shareTexts = {
"做暗夜中的主角"
},
minVer = "1.3.18.71",
beginTime = {
seconds = 1727971200
},
suitId = 50125,
suitName = "飞翼视界",
suitIcon = "CDN:Icon_Mask_124"
},
[610237] = {
id = 610237,
effect = true,
name = "飞翼视界",
desc = "神秘魅影，看穿暗夜",
icon = "CDN:Icon_Mask_124_01",
outlookConf = {
belongTo = 610236,
fashionValue = 25,
belongToGroup = {
610237,
610238
}
},
resourceConf = {
model = "SM_Mask_124",
material = "MI_Mask_124_1_HP01;MI_Mask_124_2_HP01",
modelType = 1,
materialSlot = "Mask;Mask_Translucent"
},
commodityId = 51257,
scaleTimes = 120,
shareTexts = {
"做暗夜中的主角"
}
},
[610238] = {
id = 610238,
effect = true,
name = "飞翼视界",
desc = "神秘魅影，看穿暗夜",
icon = "CDN:Icon_Mask_124_02",
outlookConf = {
belongTo = 610236,
fashionValue = 25,
belongToGroup = {
610237,
610238
}
},
resourceConf = {
model = "SM_Mask_124",
material = "MI_Mask_124_1_HP02;MI_Mask_124_2_HP02",
modelType = 1,
materialSlot = "Mask;Mask_Translucent"
},
commodityId = 51258,
scaleTimes = 120,
shareTexts = {
"做暗夜中的主角"
}
},
[610239] = {
id = 610239,
effect = true,
name = "酷彩视界",
desc = "所有霓虹灯，都在眼底闪烁",
icon = "CDN:Icon_Mask_134",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_134",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"色彩大胆，搭配无界"
},
beginTime = v4,
suitId = 50126,
suitName = "酷彩视界",
suitIcon = "CDN:Icon_Mask_134"
},
[610240] = {
id = 610240,
effect = true,
name = "绒花镜语",
desc = "彩线编成温柔梦，眸间绽放小绒花",
icon = "CDN:Icon_Mask_133",
getWay = "泡泡玛特",
jumpId = {
620
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_133",
modelType = 1
},
shareTexts = {
"眼前开出了绚丽的花"
},
minVer = "1.3.18.109",
beginTime = {
seconds = 1728576000
},
suitId = 50127,
suitName = "绒花镜语",
suitIcon = "CDN:Icon_Mask_133"
},
[610241] = {
id = 610241,
effect = true,
name = "绒花镜语",
desc = "彩线编成温柔梦，眸间绽放小绒花",
icon = "CDN:Icon_Mask_133_01",
outlookConf = {
belongTo = 610240,
fashionValue = 25,
belongToGroup = {
610241,
610242
}
},
resourceConf = {
model = "SM_Mask_133",
material = "MI_Mask_133_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51261,
shareTexts = {
"眼前开出了绚丽的花"
}
},
[610242] = {
id = 610242,
effect = true,
name = "绒花镜语",
desc = "彩线编成温柔梦，眸间绽放小绒花",
icon = "CDN:Icon_Mask_133_02",
outlookConf = {
belongTo = 610240,
fashionValue = 25,
belongToGroup = {
610241,
610242
}
},
resourceConf = {
model = "SM_Mask_133",
material = "MI_Mask_133_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51262,
shareTexts = {
"眼前开出了绚丽的花"
}
},
[610243] = {
id = 610243,
effect = true,
name = "吾皇眼镜",
desc = "猫的智慧，在于能看透一切",
icon = "CDN:Icon_Mask_141",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_141",
modelType = 1,
IconLabelId = 102
},
scaleTimes = 260,
shareTexts = {
"让我看看你在干啥"
},
beginTime = v4,
suitId = 50128,
suitName = "吾皇眼镜",
suitIcon = "CDN:Icon_Mask_141"
},
[610244] = {
id = 610244,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "云羽轻纱",
desc = "淡淡地，轻轻地，来到你面前",
icon = "CDN:Icon_Mask_127",
resourceConf = {
model = "SM_Mask_127",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50129,
suitName = "云羽轻纱",
suitIcon = "CDN:Icon_Mask_127"
},
[610245] = {
id = 610245,
effect = true,
name = "外星探视",
desc = "来自遥远星际的访客，看什么都很新鲜",
icon = "CDN:Icon_Mask_138",
getWay = "赛季通行证",
jumpId = {
9
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_138",
modelType = 1
},
shareTexts = {
"看世界的角度，会因此不同"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitId = 50130,
suitName = "外星探视",
suitIcon = "CDN:Icon_Mask_138"
},
[610246] = {
id = 610246,
effect = true,
name = "外星探视",
desc = "来自遥远星际的访客，看什么都很新鲜",
icon = "CDN:Icon_Mask_138_01",
outlookConf = {
belongTo = 610245,
fashionValue = 25,
belongToGroup = {
610246,
610247
}
},
resourceConf = {
model = "SM_Mask_138",
material = "MI_Mask_138_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51266,
shareTexts = {
"看世界的角度，会因此不同"
}
},
[610247] = {
id = 610247,
effect = true,
name = "外星探视",
desc = "来自遥远星际的访客，看什么都很新鲜",
icon = "CDN:Icon_Mask_138_02",
outlookConf = {
belongTo = 610245,
fashionValue = 25,
belongToGroup = {
610246,
610247
}
},
resourceConf = {
model = "SM_Mask_138",
material = "MI_Mask_138_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51267,
shareTexts = {
"看世界的角度，会因此不同"
}
},
[610248] = {
id = 610248,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "野性之息",
desc = "简单的豹纹，激起最原始的野性",
icon = "CDN:Icon_Mask_136",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SM_Mask_136",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 1734019200
},
suitId = 50131,
suitName = "野性之息",
suitIcon = "CDN:Icon_Mask_136"
},
[610249] = {
id = 610249,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "粉豹魅影",
desc = "眼波流转，是粉红豹的独特魅力",
icon = "CDN:Icon_Mask_137",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SM_Mask_137",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 1734019200
},
suitId = 50132,
suitName = "粉豹魅影",
suitIcon = "CDN:Icon_Mask_137"
},
[610250] = {
id = 610250,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "萝萝鼻",
desc = "装上萝卜小鼻，收获绝美侧脸",
icon = "CDN:Icon_Mask_130",
resourceConf = {
model = "SM_Mask_130",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50133,
suitName = "萝萝鼻",
suitIcon = "CDN:Icon_Mask_130"
},
[610251] = {
id = 610251,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "飘雪之镜",
desc = "戴上眼镜后的世界，是独属于冬日的回忆",
icon = "CDN:Icon_Mask_131",
resourceConf = {
model = "SM_Mask_131",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50134,
suitName = "飘雪之镜",
suitIcon = "CDN:Icon_Mask_131"
},
[610252] = {
id = 610252,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "蜜糖时刻",
desc = "舔一舔，甜蜜无可比拟",
icon = "CDN:Icon_Mask_132",
resourceConf = {
model = "SM_Mask_132",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50135,
suitName = "蜜糖时刻",
suitIcon = "CDN:Icon_Mask_132"
},
[610253] = {
id = 610253,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "暗影凝视",
desc = "专属守护，闲人勿近",
icon = "CDN:Icon_Mask_139",
resourceConf = {
model = "SM_Mask_139",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50136,
suitName = "暗影凝视",
suitIcon = "CDN:Icon_Mask_139"
},
[610254] = {
id = 610254,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星辰之眼",
desc = "如同夜空般深邃，嵌满了闪烁的星辰",
icon = "CDN:Icon_Mask_135",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_135",
modelType = 2,
idleAnim = "AS_Mask_135_idle_001"
},
shareTexts = {
"观星河流动，与星辰共舞"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitId = 50137,
suitName = "星辰之眼",
suitIcon = "CDN:Icon_Mask_135"
},
[610255] = {
id = 610255,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星辰之眼",
desc = "如同夜空般深邃，嵌满了闪烁的星辰",
icon = "CDN:Icon_Mask_135_01",
outlookConf = {
belongTo = 610254,
fashionValue = 35,
belongToGroup = {
610255,
610256
}
},
resourceConf = {
model = "SK_Mask_135",
material = "MI_Mask_135_1_HP01;MI_Mask_135_2_HP01",
modelType = 2,
idleAnim = "AS_Mask_135_idle_001_HP01",
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51275,
shareTexts = {
"观星河流动，与星辰共舞"
}
},
[610256] = {
id = 610256,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星辰之眼",
desc = "如同夜空般深邃，嵌满了闪烁的星辰",
icon = "CDN:Icon_Mask_135_02",
outlookConf = {
belongTo = 610254,
fashionValue = 35,
belongToGroup = {
610255,
610256
}
},
resourceConf = {
model = "SK_Mask_135",
material = "MI_Mask_135_1_HP02;MI_Mask_135_2_HP02",
modelType = 2,
idleAnim = "AS_Mask_135_idle_001_HP02",
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51276,
shareTexts = {
"观星河流动，与星辰共舞"
}
},
[610257] = {
id = 610257,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "古怪南瓜",
desc = "南瓜编织出梦境，专属的古怪幻想",
icon = "CDN:Icon_Mask_144",
resourceConf = {
model = "SM_Mask_144",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50138,
suitName = "古怪南瓜",
suitIcon = "CDN:Icon_Mask_144"
},
[610258] = {
id = 610258,
effect = true,
name = "香脆吐司",
desc = "为了吐司，多了一个早起的理由",
icon = "CDN:Icon_Mask_129",
getWay = "星辰日晷",
jumpId = {
380
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_129",
modelType = 1
},
shareTexts = {
"涂不涂酱，都很耐吃"
},
minVer = "1.3.26.29",
beginTime = {
seconds = 1730217600
},
suitId = 50139,
suitName = "香脆吐司",
suitIcon = "CDN:Icon_Mask_129"
},
[610259] = {
id = 610259,
effect = true,
name = "香脆吐司",
desc = "为了吐司，多了一个早起的理由",
icon = "CDN:Icon_Mask_129_01",
outlookConf = {
belongTo = 610258,
fashionValue = 25,
belongToGroup = {
610259,
610260
}
},
resourceConf = {
model = "SM_Mask_129",
material = "MI_Mask_129_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51279,
shareTexts = {
"涂不涂酱，都很耐吃"
}
},
[610260] = {
id = 610260,
effect = true,
name = "香脆吐司",
desc = "为了吐司，多了一个早起的理由",
icon = "CDN:Icon_Mask_129_02",
outlookConf = {
belongTo = 610258,
fashionValue = 25,
belongToGroup = {
610259,
610260
}
},
resourceConf = {
model = "SM_Mask_129",
material = "MI_Mask_129_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51280,
shareTexts = {
"涂不涂酱，都很耐吃"
}
},
[610261] = {
id = 610261,
effect = true,
name = "隐夜之面",
desc = "灵敏即是智慧，在黑暗中如鱼得水",
icon = "CDN:Icon_Mask_154",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_154",
modelType = 1
},
shareTexts = {
"夜晚，是最好的伪装"
},
beginTime = v4,
suitId = 50140,
suitName = "隐夜之面",
suitIcon = "CDN:Icon_Mask_154"
},
[610262] = {
id = 610262,
effect = true,
name = "隐夜之面",
desc = "灵敏即是智慧，在黑暗中如鱼得水",
icon = "CDN:Icon_Mask_154_01",
outlookConf = {
belongTo = 610261,
fashionValue = 25,
belongToGroup = {
610262,
610263
}
},
resourceConf = {
model = "SM_Mask_154",
material = "MI_Mask_154_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51282,
shareTexts = {
"夜晚，是最好的伪装"
}
},
[610263] = {
id = 610263,
effect = true,
name = "隐夜之面",
desc = "灵敏即是智慧，在黑暗中如鱼得水",
icon = "CDN:Icon_Mask_154_02",
outlookConf = {
belongTo = 610261,
fashionValue = 25,
belongToGroup = {
610262,
610263
}
},
resourceConf = {
model = "SM_Mask_154",
material = "MI_Mask_154_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51283,
shareTexts = {
"夜晚，是最好的伪装"
}
},
[610264] = {
id = 610264,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "赤焰之舞",
desc = "每次舞动，仿佛火焰在舞池中跳跃",
icon = "CDN:Icon_Mask_143",
getWay = "永恒之舞",
jumpId = {
8010
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_143",
modelType = 2,
idleAnim = "AS_Mask_143_idle_001"
},
shareTexts = {
"炽热而狂放，是猩红色的幻想"
},
minVer = "1.3.26.71",
beginTime = {
seconds = 1730995200
},
suitId = 50141,
suitName = "赤焰之舞",
suitIcon = "CDN:Icon_Mask_143"
},
[610265] = {
id = 610265,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "赤焰之舞",
desc = "每次舞动，仿佛火焰在舞池中跳跃",
icon = "CDN:Icon_Mask_143_01",
outlookConf = {
belongTo = 610264,
fashionValue = 35,
belongToGroup = {
610265,
610266
}
},
resourceConf = {
model = "SK_Mask_143",
material = "MI_Mask_143_HP01",
modelType = 2,
idleAnim = "AS_Mask_143_idle_001_HP01",
materialSlot = "Mask"
},
commodityId = 51285,
shareTexts = {
"炽热而狂放，是猩红色的幻想"
}
},
[610266] = {
id = 610266,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "赤焰之舞",
desc = "每次舞动，仿佛火焰在舞池中跳跃",
icon = "CDN:Icon_Mask_143_02",
outlookConf = {
belongTo = 610264,
fashionValue = 35,
belongToGroup = {
610265,
610266
}
},
resourceConf = {
model = "SK_Mask_143",
material = "MI_Mask_143_HP02",
modelType = 2,
idleAnim = "AS_Mask_143_idle_001_HP02",
materialSlot = "Mask"
},
commodityId = 51286,
shareTexts = {
"炽热而狂放，是猩红色的幻想"
}
},
[610267] = {
id = 610267,
effect = true,
name = "大眼萌萌",
desc = "小小的世界，大大的发现",
icon = "CDN:Icon_Mask_142",
getWay = "永恒之舞",
jumpId = {
8010
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_142",
modelType = 1
},
shareTexts = {
"不放过每一处细节"
},
minVer = "1.3.26.71",
beginTime = {
seconds = 1730995200
},
suitId = 50142,
suitName = "大眼萌萌",
suitIcon = "CDN:Icon_Mask_142"
},
[610268] = {
id = 610268,
effect = true,
name = "大眼萌萌",
desc = "小小的世界，大大的发现",
icon = "CDN:Icon_Mask_142_01",
outlookConf = {
belongTo = 610267,
fashionValue = 25,
belongToGroup = {
610268,
610269
}
},
resourceConf = {
model = "SM_Mask_142",
material = "MI_Mask_142_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51288,
shareTexts = {
"不放过每一处细节"
}
},
[610269] = {
id = 610269,
effect = true,
name = "大眼萌萌",
desc = "小小的世界，大大的发现",
icon = "CDN:Icon_Mask_142_02",
outlookConf = {
belongTo = 610267,
fashionValue = 25,
belongToGroup = {
610268,
610269
}
},
resourceConf = {
model = "SM_Mask_142",
material = "MI_Mask_142_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51289,
shareTexts = {
"不放过每一处细节"
}
},
[610270] = {
id = 610270,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "飞翔之眼",
desc = "带上翅膀，眼中的世界会不会飞起来？",
icon = "CDN:Icon_Mask_147",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SM_Mask_147",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 1734019200
},
suitId = 50143,
suitName = "飞翔之眼",
suitIcon = "CDN:Icon_Mask_147"
},
[610271] = {
id = 610271,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "粉羽风尚",
desc = "感觉轻飘飘，飞翔在粉红色的世界里",
icon = "CDN:Icon_Mask_148",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SM_Mask_148",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 1734019200
},
suitId = 50144,
suitName = "粉羽风尚",
suitIcon = "CDN:Icon_Mask_148"
},
[610272] = {
id = 610272,
effect = true,
name = "蟹钳眼镜",
desc = "这是一个“钳”所未有的世界",
icon = "CDN:Icon_Mask_145",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_145",
modelType = 1
},
shareTexts = {
"潮流在我，“钳”途非凡"
},
beginTime = v4,
suitId = 50145,
suitName = "蟹钳眼镜",
suitIcon = "CDN:Icon_Mask_145"
},
[610273] = {
id = 610273,
effect = true,
name = "蟹钳眼镜",
desc = "这是一个“钳”所未有的世界",
icon = "CDN:Icon_Mask_145_01",
outlookConf = {
belongTo = 610272,
fashionValue = 25,
belongToGroup = {
610273,
610274
}
},
resourceConf = {
model = "SM_Mask_145",
material = "MI_Mask_145_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51294,
shareTexts = {
"潮流在我，“钳”途非凡"
}
},
[610274] = {
id = 610274,
effect = true,
name = "蟹钳眼镜",
desc = "这是一个“钳”所未有的世界",
icon = "CDN:Icon_Mask_145_02",
outlookConf = {
belongTo = 610272,
fashionValue = 25,
belongToGroup = {
610273,
610274
}
},
resourceConf = {
model = "SM_Mask_145",
material = "MI_Mask_145_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51295,
shareTexts = {
"潮流在我，“钳”途非凡"
}
},
[610275] = {
id = 610275,
effect = true,
name = "美食视界",
desc = "每一天睁眼，都是金黄酥脆的好心情",
icon = "CDN:Icon_Mask_149",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_149",
modelType = 1
},
shareTexts = {
"眨眼之间，炸鸡出现！"
},
beginTime = v4,
suitId = 50146,
suitName = "美食视界",
suitIcon = "CDN:Icon_Mask_149"
},
[610276] = {
id = 610276,
effect = true,
name = "美食视界",
desc = "每一天睁眼，都是金黄酥脆的好心情",
icon = "CDN:Icon_Mask_149_01",
outlookConf = {
belongTo = 610275,
fashionValue = 25,
belongToGroup = {
610276,
610277
}
},
resourceConf = {
model = "SM_Mask_149",
material = "MI_Mask_149_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51297,
shareTexts = {
"眨眼之间，炸鸡出现！"
}
},
[610277] = {
id = 610277,
effect = true,
name = "美食视界",
desc = "每一天睁眼，都是金黄酥脆的好心情",
icon = "CDN:Icon_Mask_149_02",
outlookConf = {
belongTo = 610275,
fashionValue = 25,
belongToGroup = {
610276,
610277
}
},
resourceConf = {
model = "SM_Mask_149",
material = "MI_Mask_149_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51298,
shareTexts = {
"眨眼之间，炸鸡出现！"
}
},
[610278] = {
id = 610278,
effect = true,
exceedReplaceItem = {
{
itemId = 222,
itemNum = 10
}
},
name = "香肠派对",
desc = "成为派对大王，让笑容比香肠还长",
icon = "CDN:Icon_Mask_152",
getWay = "星之恋空",
jumpId = {
628
},
outlookConf = v2,
resourceConf = {
model = "SM_Mask_152",
modelType = 1
},
shareTexts = {
"嘴巴一戴，笑声自来"
},
beginTime = {
seconds = 1731600000
},
suitId = 50147,
suitName = "香肠派对",
suitIcon = "CDN:Icon_Mask_152"
},
[610279] = {
id = 610279,
effect = true,
name = "香肠派对",
desc = "成为派对大王，让笑容比香肠还长",
icon = "CDN:Icon_Mask_152_01",
outlookConf = {
belongTo = 610278,
fashionValue = 25,
belongToGroup = {
610279,
610280
}
},
resourceConf = {
model = "SM_Mask_152",
material = "MI_Mask_152_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51300,
shareTexts = {
"嘴巴一戴，笑声自来"
}
},
[610280] = {
id = 610280,
effect = true,
name = "香肠派对",
desc = "成为派对大王，让笑容比香肠还长",
icon = "CDN:Icon_Mask_152_02",
outlookConf = {
belongTo = 610278,
fashionValue = 25,
belongToGroup = {
610279,
610280
}
},
resourceConf = {
model = "SM_Mask_152",
material = "MI_Mask_152_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51301,
shareTexts = {
"嘴巴一戴，笑声自来"
}
},
[610281] = {
id = 610281,
effect = true,
name = "宝宝眼镜",
desc = "要试试我的宝宝眼镜吗？",
icon = "CDN:Icon_Mask_146",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_146",
modelType = 1
},
shareTexts = {
"没有镜片，只为凹造型"
},
beginTime = v4,
suitId = 50148,
suitName = "宝宝眼镜",
suitIcon = "CDN:Icon_Mask_146"
},
[610282] = {
id = 610282,
effect = true,
name = "宝宝眼镜",
desc = "要试试我的宝宝眼镜吗？",
icon = "CDN:Icon_Mask_146_01",
outlookConf = {
belongTo = 610281,
fashionValue = 25,
belongToGroup = {
610282,
610283
}
},
resourceConf = {
model = "SM_Mask_146",
material = "MI_Mask_146_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51303,
shareTexts = {
"没有镜片，只为凹造型"
}
},
[610283] = {
id = 610283,
effect = true,
name = "宝宝眼镜",
desc = "要试试我的宝宝眼镜吗？",
icon = "CDN:Icon_Mask_146_02",
outlookConf = {
belongTo = 610281,
fashionValue = 25,
belongToGroup = {
610282,
610283
}
},
resourceConf = {
model = "SM_Mask_146",
material = "MI_Mask_146_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51304,
shareTexts = {
"没有镜片，只为凹造型"
}
},
[610284] = {
id = 610284,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "迷雾之纱",
desc = "笼罩在轻柔的迷雾中，神秘而优雅",
icon = "CDN:Icon_Mask_153",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Mask_153",
emitter = "FX_CH_Decorate_Mask_153",
modelType = 1
},
shareTexts = {
"越是遮掩，越是绮丽"
},
beginTime = v4,
suitId = 50149,
suitName = "迷雾之纱",
suitIcon = "CDN:Icon_Mask_153"
},
[610285] = {
id = 610285,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "迷雾之纱",
desc = "笼罩在轻柔的迷雾中，神秘而优雅",
icon = "CDN:Icon_Mask_153_01",
outlookConf = {
belongTo = 610284,
fashionValue = 35,
belongToGroup = {
610285,
610286
}
},
resourceConf = {
model = "SM_Mask_153",
material = "MI_Mask_153_1_HP01;MI_Mask_153_2_HP01",
emitter = "FX_CH_Decorate_Mask_153_HP01",
modelType = 1,
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51306,
shareTexts = {
"越是遮掩，越是绮丽"
}
},
[610286] = {
id = 610286,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "迷雾之纱",
desc = "笼罩在轻柔的迷雾中，神秘而优雅",
icon = "CDN:Icon_Mask_153_02",
outlookConf = {
belongTo = 610284,
fashionValue = 35,
belongToGroup = {
610285,
610286
}
},
resourceConf = {
model = "SM_Mask_153",
material = "MI_Mask_153_1_HP02;MI_Mask_153_2_HP02",
emitter = "FX_CH_Decorate_Mask_153_HP02",
modelType = 1,
materialSlot = "Mask_1;Mask_2"
},
commodityId = 51307,
shareTexts = {
"越是遮掩，越是绮丽"
}
},
[610287] = {
id = 610287,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "兔门永存",
desc = "标准的微笑，一定露出两颗兔牙",
icon = "CDN:Icon_Mask_161",
resourceConf = {
model = "SM_Mask_161",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50150,
suitName = "兔门永存",
suitIcon = "CDN:Icon_Mask_161"
},
[610288] = {
id = 610288,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "红唇啵啵",
desc = "红唇拦路，通通亲过！",
icon = "CDN:Icon_Mask_160",
resourceConf = {
model = "SM_Mask_160",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50151,
suitName = "红唇啵啵",
suitIcon = "CDN:Icon_Mask_160"
},
[610289] = {
id = 610289,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "麻萤之眼",
desc = "我的眼睛可以看穿世界",
icon = "CDN:Icon_Mask_163",
resourceConf = {
model = "SM_Mask_163",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50152,
suitName = "麻萤之眼",
suitIcon = "CDN:Icon_Mask_163"
},
[610290] = {
id = 610290,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "小黑的凝视",
desc = "米花町里没有人能逃脱我的凝视",
icon = "CDN:Icon_Mask_159",
resourceConf = {
model = "SM_Mask_159",
modelType = 1
},
scaleTimes = 260,
beginTime = v4,
suitId = 50153,
suitName = "小黑的凝视",
suitIcon = "CDN:Icon_Mask_159"
},
[610291] = {
id = 610291,
effect = true,
name = "几何视界",
desc = "睁开眼，收获几何状的独特世界",
icon = "CDN:Icon_Mask_156",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_156",
modelType = 1
},
shareTexts = {
"简单线条，传达极致态度"
},
beginTime = v4,
suitId = 50154,
suitName = "几何视界",
suitIcon = "CDN:Icon_Mask_156"
},
[610292] = {
id = 610292,
effect = true,
name = "几何视界",
desc = "睁开眼，收获几何状的独特世界",
icon = "CDN:Icon_Mask_156_01",
outlookConf = {
belongTo = 610291,
fashionValue = 25,
belongToGroup = {
610292,
610293
}
},
resourceConf = {
model = "SM_Mask_156",
material = "MI_Mask_156_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51313,
shareTexts = {
"简单线条，传达极致态度"
}
},
[610293] = {
id = 610293,
effect = true,
name = "几何视界",
desc = "睁开眼，收获几何状的独特世界",
icon = "CDN:Icon_Mask_156_02",
outlookConf = {
belongTo = 610291,
fashionValue = 25,
belongToGroup = {
610292,
610293
}
},
resourceConf = {
model = "SM_Mask_156",
material = "MI_Mask_156_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51314,
shareTexts = {
"简单线条，传达极致态度"
}
},
[610294] = {
id = 610294,
effect = true,
name = "圣诞树眼镜",
desc = "透过新视角，发现圣诞节的小确幸",
icon = "CDN:Icon_Mask_155",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_155",
modelType = 1
},
shareTexts = {
"发现色彩缤纷的圣诞夜"
},
beginTime = v4,
suitId = 50155,
suitName = "圣诞树眼镜",
suitIcon = "CDN:Icon_Mask_155",
shareOffset = {
-10,
25
},
shareScaleTimes = 140
},
[610295] = {
id = 610295,
effect = true,
name = "圣诞树眼镜",
desc = "透过新视角，发现圣诞节的小确幸",
icon = "CDN:Icon_Mask_155_01",
outlookConf = {
belongTo = 610294,
fashionValue = 25,
belongToGroup = {
610295,
610296
}
},
resourceConf = {
model = "SM_Mask_155",
material = "MI_Mask_155_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51316,
shareTexts = {
"发现色彩缤纷的圣诞夜"
},
shareOffset = {
-10,
25
},
shareScaleTimes = 140
},
[610296] = {
id = 610296,
effect = true,
name = "圣诞树眼镜",
desc = "透过新视角，发现圣诞节的小确幸",
icon = "CDN:Icon_Mask_155_02",
outlookConf = {
belongTo = 610294,
fashionValue = 25,
belongToGroup = {
610295,
610296
}
},
resourceConf = {
model = "SM_Mask_155",
material = "MI_Mask_155_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51317,
shareTexts = {
"发现色彩缤纷的圣诞夜"
},
shareOffset = {
-10,
25
},
shareScaleTimes = 140
},
[610297] = {
id = 610297,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "双生之翼",
desc = "面罩下隐藏的，是内心的双重力量",
icon = "CDN:Icon_Mask_158",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_158",
modelType = 2,
idleAnim = "AS_Mask_158_idle_001"
},
shareTexts = {
"羽翼轻抚，释放无限可能"
},
beginTime = v4,
suitId = 50156,
suitName = "双生之翼",
suitIcon = "CDN:Icon_Mask_158"
},
[610298] = {
id = 610298,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "双生之翼",
desc = "面罩下隐藏的，是内心的双重力量",
icon = "CDN:Icon_Mask_158_01",
outlookConf = {
belongTo = 610297,
fashionValue = 35,
belongToGroup = {
610298,
610299
}
},
resourceConf = {
model = "SK_Mask_158",
material = "MI_Mask_158_1_HP01;MI_Mask_158_2_HP01;MI_Mask_158_3_HP01",
modelType = 2,
idleAnim = "AS_Mask_158_idle_001_HP01",
materialSlot = "Mask_1;Mask_2;Mask_3"
},
commodityId = 51319,
shareTexts = {
"羽翼轻抚，释放无限可能"
}
},
[610299] = {
id = 610299,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "双生之翼",
desc = "面罩下隐藏的，是内心的双重力量",
icon = "CDN:Icon_Mask_158_02",
outlookConf = {
belongTo = 610297,
fashionValue = 35,
belongToGroup = {
610298,
610299
}
},
resourceConf = {
model = "SK_Mask_158",
material = "MI_Mask_158_1_HP02;MI_Mask_158_2_HP02;MI_Mask_158_3_HP02",
modelType = 2,
idleAnim = "AS_Mask_158_idle_001_HP02",
materialSlot = "Mask_1;Mask_2;Mask_3"
},
commodityId = 51320,
shareTexts = {
"羽翼轻抚，释放无限可能"
}
},
[610300] = {
id = 610300,
effect = true,
name = "琳琅点翠",
desc = "古韵今风，讲述岁月里的故事",
icon = "CDN:Icon_Mask_167",
outlookConf = v2,
resourceConf = {
model = "SM_Mask_167",
modelType = 1
},
shareTexts = {
"花丝为胎，翠羽为魂"
},
beginTime = v4,
suitId = 50157,
suitName = "琳琅点翠",
suitIcon = "CDN:Icon_Mask_167"
},
[610301] = {
id = 610301,
effect = true,
name = "琳琅点翠",
desc = "古韵今风，讲述岁月里的故事",
icon = "CDN:Icon_Mask_167_01",
outlookConf = {
belongTo = 610300,
fashionValue = 25,
belongToGroup = {
610301,
610302
}
},
resourceConf = {
model = "SM_Mask_167",
material = "MI_Mask_167_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51322,
shareTexts = {
"花丝为胎，翠羽为魂"
}
}
}

local mt = {
effect = false,
type = "ItemType_FaceOrnament",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 60
},
scaleTimes = 200,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data