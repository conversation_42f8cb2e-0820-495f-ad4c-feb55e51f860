--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 铭牌

local v0 = 1

local v1 = 3

local v2 = 0

local v3 = {
{
itemId = 13,
itemNum = 50
}
}

local data = {
[820001] = {
id = 820001,
maxNum = 1,
quality = 3,
name = "闪电封锁",
desc = "星世界-星海巡游活动内获得",
icon = "CDN:Icon_NameFrame_001",
resourceConf = {
bg = "CDN:T_NameFrame_bg_001"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[820002] = {
id = 820002,
maxNum = 1,
name = "青柠气泡",
desc = "桃源悠梦—赛季兑换中获得",
icon = "CDN:Icon_NameFrame_002",
resourceConf = {
bg = "CDN:T_NameFrame_bg_002"
},
isDynamic = 0,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[820003] = {
id = 820003,
maxNum = 1,
name = "潮粉梦境",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_003",
resourceConf = {
bg = "CDN:T_NameFrame_bg_003"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[820004] = {
id = 820004,
maxNum = 1,
name = "街头涂鸦",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_004",
resourceConf = {
bg = "CDN:T_NameFrame_bg_004"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[820005] = {
id = 820005,
maxNum = 1,
name = "奇路电波",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_005",
resourceConf = {
bg = "CDN:T_NameFrame_bg_005"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[820006] = {
id = 820006,
maxNum = 1,
name = "胡萝卜乐园",
desc = "首充获得",
icon = "CDN:Icon_NameFrame_006",
resourceConf = {
bg = "CDN:T_NameFrame_bg_006"
},
isDynamic = 0,
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[820007] = {
id = 820007,
maxNum = 1,
name = "碧野青原",
desc = "桃源通行证获得",
icon = "CDN:Icon_NameFrame_007",
resourceConf = {
bg = "CDN:T_NameFrame_bg_007"
},
isDynamic = 0,
beginTime = {
seconds = 1702569600
},
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[820008] = {
id = 820008,
maxNum = 1,
name = "绿树悠然",
desc = "桃源通行证获得",
icon = "CDN:Icon_NameFrame_008",
resourceConf = {
bg = "CDN:T_NameFrame_bg_008"
},
isDynamic = 0,
beginTime = {
seconds = 1702569600
},
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[820009] = {
id = 820009,
maxNum = 1,
quality = 3,
name = "匠心闪耀",
desc = "造梦之旅活动内获得",
icon = "CDN:Icon_NameFrame_009",
resourceConf = {
bg = "CDN:T_NameFrame_bg_009"
},
isDynamic = 0,
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[820010] = {
id = 820010,
maxNum = 1,
name = "星力全开",
desc = "造梦之旅活动内获得",
icon = "CDN:Icon_NameFrame_010",
resourceConf = {
bg = "CDN:T_NameFrame_bg_010"
},
isDynamic = 0,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[820011] = {
id = 820011,
maxNum = 1,
name = "悠游海洋",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_011",
resourceConf = {
bg = "CDN:T_NameFrame_bg_011"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[820012] = {
id = 820012,
maxNum = 1,
name = "粉色心情",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_012",
resourceConf = {
bg = "CDN:T_NameFrame_bg_012"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[820013] = {
id = 820013,
maxNum = 1,
name = "黄绿柠檬",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_013",
resourceConf = {
bg = "CDN:T_NameFrame_bg_013"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[820014] = {
id = 820014,
maxNum = 1,
name = "心动迹象",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_014",
resourceConf = {
bg = "CDN:T_NameFrame_bg_014"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[820015] = {
id = 820015,
maxNum = 1,
name = "靛蓝印花",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_015",
resourceConf = {
bg = "CDN:T_NameFrame_bg_015"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[820016] = {
id = 820016,
maxNum = 1,
name = "樱桃蜜语",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_016",
resourceConf = {
bg = "CDN:T_NameFrame_bg_016"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[820017] = {
id = 820017,
maxNum = 1,
name = "特级明星",
desc = "星宝会员卡用户专享",
icon = "CDN:Icon_NameFrame_017",
resourceConf = {
bg = "CDN:T_NameFrame_bg_017"
},
isDynamic = 0,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[820018] = {
id = 820018,
maxNum = 1,
quality = 2,
name = "S1冰雪王者",
desc = "冲段挑战-元梦启明星段位获得",
icon = "CDN:Icon_NameFrame_018",
resourceConf = {
bg = "CDN:T_NameFrame_bg_018"
},
isDynamic = 0,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[820019] = {
id = 820019,
maxNum = 1,
name = "深蓝泡沫",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_019",
resourceConf = {
bg = "CDN:T_NameFrame_bg_019"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[820020] = {
id = 820020,
maxNum = 1,
name = "超开星庆典",
desc = "敬请期待",
icon = "CDN:Icon_NameFrame_020",
resourceConf = {
bg = "CDN:T_NameFrame_bg_020"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[820021] = {
id = 820021,
maxNum = 1,
name = "星宝诞生日",
desc = "小乔送星运活动获得",
icon = "CDN:Icon_NameFrame_021",
resourceConf = {
bg = "CDN:T_NameFrame_bg_021"
},
isDynamic = 0,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[820023] = {
id = 820023,
maxNum = 1,
name = "粉红蜜语",
desc = "达成特定亲密关系获得",
icon = "CDN:Icon_NameFrame_023",
resourceConf = {
bg = "CDN:T_NameFrame_bg_023"
},
isDynamic = 0,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[820024] = {
id = 820024,
maxNum = 1,
name = "乐战同行",
desc = "达成特定亲密关系获得",
icon = "CDN:Icon_NameFrame_024",
resourceConf = {
bg = "CDN:T_NameFrame_bg_024"
},
isDynamic = 0,
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[820025] = {
id = 820025,
maxNum = 1,
name = "甜蜜爱心",
desc = "达成特定亲密关系获得",
icon = "CDN:Icon_NameFrame_025",
resourceConf = {
bg = "CDN:T_NameFrame_bg_025"
},
isDynamic = 0,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[820026] = {
id = 820026,
maxNum = 1,
name = "拳拳至诚",
desc = "达成特定亲密关系获得",
icon = "CDN:Icon_NameFrame_026",
resourceConf = {
bg = "CDN:T_NameFrame_bg_026"
},
isDynamic = 0,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[820027] = {
id = 820027,
maxNum = 1,
lowVer = "1.2.67.1",
quality = 2,
name = "龙宝送福",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_027",
resourceConf = {
bg = "D_Namebox_000"
},
isDynamic = 1,
beginTime = {
seconds = 1707408000
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[820028] = {
id = 820028,
maxNum = 1,
lowVer = "1.2.67.1",
name = "张灯结彩",
desc = "山海奇遇—赛季兑换中获得",
icon = "CDN:Icon_NameFrame_028",
resourceConf = {
bg = "CDN:T_NameFrame_bg_028"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[820029] = {
id = 820029,
maxNum = 1,
lowVer = "1.2.67.1",
name = "第九人生",
desc = "腾讯新闻合作活动获得",
icon = "CDN:Icon_NameFrame_029",
resourceConf = {
bg = "CDN:T_NameFrame_bg_029"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[820030] = {
id = 820030,
maxNum = 1,
lowVer = "1.2.67.1",
name = "雪中寒梅",
desc = "山海通行证获得",
icon = "CDN:Icon_NameFrame_030",
resourceConf = {
bg = "CDN:T_NameFrame_bg_030"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[820031] = {
id = 820031,
maxNum = 1,
lowVer = "1.2.67.1",
name = "梅枝纵横",
desc = "山海通行证获得",
icon = "CDN:Icon_NameFrame_031",
resourceConf = {
bg = "CDN:T_NameFrame_bg_031"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[820035] = {
id = 820035,
maxNum = 1,
lowVer = "1.2.67.1",
name = "元宵福至",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_035",
resourceConf = {
bg = "CDN:T_NameFrame_bg_035"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[820036] = {
id = 820036,
maxNum = 1,
name = "花明萤暗",
desc = "时光通行证获得",
icon = "CDN:Icon_NameFrame_036",
resourceConf = {
bg = "CDN:T_NameFrame_bg_036"
},
isDynamic = 0,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[820037] = {
id = 820037,
maxNum = 1,
name = "花间飞舞",
desc = "时光通行证获得",
icon = "CDN:Icon_NameFrame_037",
resourceConf = {
bg = "CDN:T_NameFrame_bg_037"
},
isDynamic = 0,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[820038] = {
id = 820038,
maxNum = 1,
quality = 2,
name = "S3时光故事",
desc = "冲段挑战-最强元梦星段位获得",
icon = "CDN:Icon_NameFrame_038",
resourceConf = {
bg = "CDN:T_NameFrame_bg_038"
},
isDynamic = 0,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[820041] = {
id = 820041,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
quality = 2,
name = "剧场开幕",
desc = "星光剧场祈愿活动获得",
icon = "CDN:Icon_NameFrame_041",
resourceConf = {
bg = "CDN:T_NameFrame_bg_041"
},
isDynamic = 0,
beginTime = {
seconds = 1711728000
},
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[820051] = {
id = 820051,
maxNum = 1,
quality = 3,
name = "【限时】狼人学家",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_051",
resourceConf = {
bg = "CDN:T_NameFrame_bg_051"
},
isDynamic = 0,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[820052] = {
id = 820052,
maxNum = 1,
quality = 3,
name = "【限时】躲猫猫侠",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_052",
resourceConf = {
bg = "CDN:T_NameFrame_bg_052"
},
isDynamic = 0,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[820053] = {
id = 820053,
maxNum = 1,
quality = 3,
name = "【限时】飞车大神",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_053",
resourceConf = {
bg = "CDN:T_NameFrame_bg_053"
},
isDynamic = 0,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[820054] = {
id = 820054,
maxNum = 1,
quality = 3,
name = "【限时】乱斗新星",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_054",
resourceConf = {
bg = "CDN:T_NameFrame_bg_054"
},
isDynamic = 0,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[820055] = {
id = 820055,
maxNum = 1,
quality = 3,
name = "【限时】星宝高手",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_055",
resourceConf = {
bg = "CDN:T_NameFrame_bg_055"
},
isDynamic = 0,
beginTime = {
seconds = 1711641600
},
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[820056] = {
id = 820056,
maxNum = 1,
quality = 3,
name = "【限时】突围先锋",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_056",
resourceConf = {
bg = "CDN:T_NameFrame_bg_056"
},
isDynamic = 0,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[820057] = {
id = 820057,
maxNum = 1,
quality = 3,
name = "【限时】武器大师",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_057",
resourceConf = {
bg = "CDN:T_NameFrame_bg_057"
},
isDynamic = 0,
beginTime = {
seconds = 1712246400
},
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[820058] = {
id = 820058,
maxNum = 1,
quality = 3,
name = "【限时】暗星高手",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_058",
resourceConf = {
bg = "CDN:T_NameFrame_bg_058"
},
isDynamic = 0,
beginTime = {
seconds = 1711641600
},
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[820059] = {
id = 820059,
maxNum = 1,
name = "大侠风范",
desc = "乐园开趴中活动获得",
icon = "CDN:Icon_NameFrame_039",
resourceConf = {
bg = "CDN:T_NameFrame_bg_039"
},
isDynamic = 0,
beginTime = {
seconds = 1711641600
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[820060] = {
id = 820060,
maxNum = 1,
lowVer = "1.2.90.66",
name = "踏青迎春",
desc = "踏青迎春活动获得",
icon = "CDN:Icon_NameFrame_060",
resourceConf = {
bg = "CDN:T_NameFrame_bg_064"
},
isDynamic = 0,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[820061] = {
id = 820061,
maxNum = 1,
name = "守护星",
desc = "星宝守护者活动获得",
icon = "CDN:Icon_NameFrame_061",
resourceConf = {
bg = "CDN:T_NameFrame_bg_065"
},
isDynamic = 0,
beginTime = {
seconds = 1713110400
},
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[820066] = {
id = 820066,
maxNum = 1,
lowVer = "1.2.100.1",
name = "黄油蝴蝶结",
desc = "潮音通行证获得",
icon = "CDN:Icon_NameFrame_066",
resourceConf = {
bg = "CDN:T_NameFrame_bg_066"
},
isDynamic = 0,
beginTime = {
seconds = 1714060800
},
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[820067] = {
id = 820067,
maxNum = 1,
lowVer = "1.2.100.1",
name = "欢庆之铃",
desc = "潮音通行证获得",
icon = "CDN:Icon_NameFrame_067",
resourceConf = {
bg = "CDN:T_NameFrame_bg_067"
},
isDynamic = 0,
beginTime = {
seconds = 1714060800
},
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[820068] = {
id = 820068,
maxNum = 1,
name = "神笔马星",
desc = "皮肤共创大赛活动获得",
icon = "CDN:Icon_NameFrame_069",
resourceConf = {
bg = "CDN:T_NameFrame_bg_060"
},
isDynamic = 0,
beginTime = {
seconds = 1713369600
},
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[820070] = {
id = 820070,
maxNum = 1,
lowVer = "1.2.100.1",
quality = 3,
name = "自信花轮",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_070",
resourceConf = {
bg = "CDN:T_NameFrame_bg_070"
},
isDynamic = 0,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[820071] = {
id = 820071,
maxNum = 1,
lowVer = "1.2.100.1",
name = "S4潮音战记",
desc = "冲段挑战-最强王者段位获得",
icon = "CDN:Icon_NameFrame_071",
resourceConf = {
bg = "CDN:T_NameFrame_bg_071"
},
isDynamic = 0,
beginTime = {
seconds = 1714060800
},
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[820072] = {
id = 820072,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.2.100.1",
quality = 2,
name = "漫溯花海",
desc = "春水溯游祈愿获得",
icon = "CDN:Icon_NameFrame_072",
resourceConf = {
bg = "CDN:T_NameFrame_bg_072"
},
isDynamic = 0,
beginTime = {
seconds = 1714665600
},
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[820073] = {
id = 820073,
maxNum = 1,
lowVer = "1.2.100.1",
quality = 2,
name = "首席玩家",
desc = "超核管家限时活动获得",
icon = "CDN:Icon_NameFrame_068",
resourceConf = {
bg = "CDN:T_NameFrame_bg_068"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[820074] = {
id = 820074,
maxNum = 1,
lowVer = "1.2.100.53",
name = "甜蜜初心",
desc = "甜蜜出游活动获得",
icon = "CDN:Icon_NameFrame_073",
resourceConf = {
bg = "CDN:T_NameFrame_bg_073"
},
isDynamic = 0,
beginTime = {
seconds = 1715875200
},
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[820075] = {
id = 820075,
maxNum = 1,
lowVer = "1.2.100.46",
quality = 2,
name = "永恒之誓",
desc = "永恒之誓祈愿获得",
icon = "CDN:Icon_NameFrame_075",
resourceConf = {
bg = "D_Namebox_075"
},
isDynamic = 1,
beginTime = {
seconds = 1715875200
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[820076] = {
id = 820076,
name = "星宝守护者",
desc = "星宝守护者活动获得",
icon = "CDN:Icon_NameFrame_076",
resourceConf = {
bg = "CDN:T_NameFrame_bg_076"
},
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[820042] = {
id = 820042,
maxNum = 1,
quality = 3,
name = "可妮兔",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_078",
resourceConf = {
bg = "CDN:T_NameFrame_bg_078"
},
isDynamic = 0,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[820080] = {
id = 820080,
maxNum = 1,
lowVer = "1.3.6.1 ",
quality = 2,
name = "梦幻沙滩",
desc = "缤纷夏日—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_080",
resourceConf = {
bg = "D_Namebox_080"
},
isDynamic = 1,
beginTime = {
seconds = 1717689600
},
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[820081] = {
id = 820081,
maxNum = 1,
lowVer = "1.3.6.1 ",
quality = 3,
name = "夏日海歌",
desc = "缤纷夏日—赛季兑换中获得",
icon = "CDN:Icon_NameFrame_081",
resourceConf = {
bg = "CDN:T_NameFrame_bg_081"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
},
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[820084] = {
id = 820084,
maxNum = 1,
quality = 3,
name = "【限时】卧底高手",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_084",
resourceConf = {
bg = "CDN:T_NameFrame_bg_084"
},
isDynamic = 0,
beginTime = {
seconds = 1718899200
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[820082] = {
id = 820082,
maxNum = 1,
lowVer = "1.3.6.1 ",
name = "缤纷小丑鱼",
desc = "缤纷夏日通行证获得",
icon = "CDN:Icon_NameFrame_082",
resourceConf = {
bg = "CDN:T_NameFrame_bg_082"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[820083] = {
id = 820083,
maxNum = 1,
lowVer = "1.3.6.1 ",
name = "夏日蝴蝶结",
desc = "缤纷夏日通行证获得",
icon = "CDN:Icon_NameFrame_083",
resourceConf = {
bg = "CDN:T_NameFrame_bg_083"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
},
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[820085] = {
id = 820085,
maxNum = 1,
name = "夏日冰柠",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_085",
resourceConf = {
bg = "CDN:T_NameFrame_bg_085"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
},
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[820086] = {
id = 820086,
maxNum = 1,
lowVer = "1.3.7.47",
quality = 2,
name = "剑启黎明",
desc = "动态昵称框，战神颂歌祈愿活动获得",
icon = "CDN:Icon_NameFrame_089",
resourceConf = {
bg = "D_Namebox_089"
},
isDynamic = 1,
beginTime = {
seconds = 1718899200
},
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[820088] = {
id = 820088,
maxNum = 1,
quality = 2,
name = "Hello Kitty蝴蝶结",
desc = "寻梦嘉年华活动获得",
icon = "CDN:Icon_NameFrame_086",
resourceConf = {
bg = "CDN:T_NameFrame_bg_086"
},
isDynamic = 0,
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[820501] = {
id = 820501,
maxNum = 1,
exceedReplaceItem = v3,
quality = 3,
name = "秘术狼",
desc = "在【谁是狼人】玩法的兑换商店中获得",
icon = "CDN:Icon_NameFrame_501",
resourceConf = {
bg = "CDN:T_NameFrame_bg_501"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[820502] = {
id = 820502,
maxNum = 1,
exceedReplaceItem = v3,
quality = 3,
name = "小丑",
desc = "在【谁是狼人】玩法的大师之路中获得",
icon = "CDN:Icon_NameFrame_502",
resourceConf = {
bg = "CDN:T_NameFrame_bg_502"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[820503] = {
id = 820503,
maxNum = 1,
exceedReplaceItem = v3,
quality = 3,
name = "法医",
icon = "CDN:Icon_NameFrame_503",
resourceConf = {
bg = "CDN:T_NameFrame_bg_503"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[820504] = {
id = 820504,
maxNum = 1,
exceedReplaceItem = v3,
quality = 3,
name = "赌徒",
desc = "第6赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_504",
resourceConf = {
bg = "CDN:T_NameFrame_bg_504"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[820505] = {
id = 820505,
maxNum = 1,
exceedReplaceItem = v3,
quality = 3,
name = "占星师",
icon = "CDN:Icon_NameFrame_505",
resourceConf = {
bg = "CDN:T_NameFrame_bg_505"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[820506] = {
id = 820506,
maxNum = 1,
exceedReplaceItem = v3,
quality = 3,
name = "天使",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_506",
resourceConf = {
bg = "CDN:T_NameFrame_bg_506"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[820507] = {
id = 820507,
maxNum = 1,
exceedReplaceItem = v3,
quality = 3,
name = "警长",
desc = "在【谁是狼人】玩法的大师之路中获得",
icon = "CDN:Icon_NameFrame_507",
resourceConf = {
bg = "CDN:T_NameFrame_bg_507"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[820508] = {
id = 820508,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "刺客狼",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_508",
resourceConf = {
bg = "CDN:T_NameFrame_bg_508"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[820509] = {
id = 820509,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "流浪汉",
desc = "礼包获得",
icon = "CDN:Icon_NameFrame_509",
resourceConf = {
bg = "CDN:T_NameFrame_bg_509"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[820510] = {
id = 820510,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "法官",
desc = "第8赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_510",
resourceConf = {
bg = "CDN:T_NameFrame_bg_510"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[820511] = {
id = 820511,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.37.55",
quality = 3,
name = "千里眼",
icon = "CDN:Icon_NameFrame_511",
resourceConf = {
bg = "CDN:T_NameFrame_bg_511"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[820512] = {
id = 820512,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "通灵师",
desc = "第7赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_512",
resourceConf = {
bg = "CDN:T_NameFrame_bg_512"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[820513] = {
id = 820513,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.37.55",
quality = 3,
name = "监听员",
icon = "CDN:Icon_NameFrame_513",
resourceConf = {
bg = "CDN:T_NameFrame_bg_513"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[820514] = {
id = 820514,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.37.55",
quality = 3,
name = "黑客",
desc = "第12赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_514",
resourceConf = {
bg = "CDN:T_NameFrame_bg_514"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[820515] = {
id = 820515,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "侦探",
desc = "第9赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_515",
resourceConf = {
bg = "CDN:T_NameFrame_bg_515"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[820516] = {
id = 820516,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "伪装狼",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_516",
resourceConf = {
bg = "CDN:T_NameFrame_bg_516"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[820517] = {
id = 820517,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.37.55",
quality = 3,
name = "炸弹狼",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_517",
resourceConf = {
bg = "CDN:T_NameFrame_bg_517"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[820518] = {
id = 820518,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.37.55",
quality = 3,
name = "潜行狼",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_518",
resourceConf = {
bg = "CDN:T_NameFrame_bg_518"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[820519] = {
id = 820519,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.37.55",
quality = 3,
name = "独狼",
desc = "第11赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_519",
resourceConf = {
bg = "CDN:T_NameFrame_bg_519"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[820520] = {
id = 820520,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.37.55",
quality = 3,
name = "大力狼",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_520",
resourceConf = {
bg = "CDN:T_NameFrame_bg_520"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[820521] = {
id = 820521,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "魔爆狼",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_521",
resourceConf = {
bg = "CDN:T_NameFrame_bg_521"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[820522] = {
id = 820522,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.18.1",
quality = 3,
name = "赏金猎人",
desc = "第10赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_522",
resourceConf = {
bg = "CDN:T_NameFrame_bg_522"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[820523] = {
id = 820523,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.78.1",
quality = 3,
name = "臭鼬",
desc = "限时祈愿获得",
icon = "CDN:Icon_NameFrame_523",
resourceConf = {
bg = "CDN:T_NameFrame_bg_523"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[820531] = {
id = 820531,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.78.1",
quality = 3,
name = "学者",
desc = "第13赛季狼人通行证中获得",
icon = "CDN:Icon_NameFrame_531",
resourceConf = {
bg = "CDN:T_NameFrame_bg_531"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[820533] = {
id = 820533,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.78.1",
quality = 3,
name = "大明星",
icon = "CDN:Icon_NameFrame_533",
resourceConf = {
bg = "CDN:T_NameFrame_bg_533"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[820536] = {
id = 820536,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.78.1",
quality = 3,
name = "幽灵狼",
icon = "CDN:Icon_NameFrame_536",
resourceConf = {
bg = "CDN:T_NameFrame_bg_536"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[820544] = {
id = 820544,
maxNum = 1,
exceedReplaceItem = v3,
lowVer = "1.3.78.1",
quality = 3,
name = "捕梦者",
icon = "CDN:Icon_NameFrame_544",
resourceConf = {
bg = "CDN:T_NameFrame_bg_544"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[820079] = {
id = 820079,
maxNum = 1,
name = "鉴赏家",
desc = "星世界鉴赏家活动获得",
icon = "CDN:Icon_NameFrame_077",
resourceConf = {
bg = "CDN:T_NameFrame_bg_077"
},
isDynamic = 0,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[820078] = {
id = 820078,
maxNum = 1,
name = "阿童木",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_074",
resourceConf = {
bg = "CDN:T_NameFrame_bg_074"
},
isDynamic = 0,
beginTime = {
seconds = 1716739200
},
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[820077] = {
id = 820077,
maxNum = 1,
name = "星宝守护者",
desc = "星宝守护者活动获得",
icon = "CDN:Icon_NameFrame_079",
resourceConf = {
bg = "CDN:T_NameFrame_bg_079"
},
isDynamic = 0,
beginTime = {
seconds = 1716998400
}
},
[820087] = {
id = 820087,
maxNum = 1,
name = "星宝守护者",
desc = "星宝守护者活动获得",
icon = "CDN:Icon_NameFrame_087",
resourceConf = {
bg = "CDN:T_NameFrame_bg_087"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[820090] = {
id = 820090,
maxNum = 1,
name = "校园新星",
desc = "高校创作大赛获取  ",
icon = "CDN:Icon_NameFrame_090",
resourceConf = {
bg = "CDN:T_NameFrame_bg_090"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[820091] = {
id = 820091,
maxNum = 1,
name = "校园明星",
desc = "高校创作大赛获取  ",
icon = "CDN:Icon_NameFrame_091",
resourceConf = {
bg = "CDN:T_NameFrame_bg_091"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
}
},
[820092] = {
id = 820092,
maxNum = 1,
name = "中轴守护者",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_092",
resourceConf = {
bg = "CDN:T_NameFrame_bg_092"
},
isDynamic = 0
},
[820093] = {
id = 820093,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.7.90",
quality = 2,
name = "浪漫之约",
desc = "浪漫旅程祈愿获得",
icon = "CDN:Icon_NameFrame_093",
resourceConf = {
bg = "CDN:T_NameFrame_bg_093"
},
isDynamic = 0,
beginTime = {
seconds = 1720195200
}
}
}

local mt = {
type = "ItemType_NamePlate",
quality = 4,
showInView = 1
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data