--com.tencent.wea.xlsRes.table_ActivityMainConfig => excel/xls/H_活动中心配置_特色玩法运营.xlsx: 活动配置

local v0 = "ATTeamRank"

local v1 = "ATGuide"

local v2 = 1

local v3 = 5

local v4 = 4

local v5 = 2

local v6 = 3

local v7 = 99

local v8 = 6

local v9 = "UI_CommonTask_SingleTaskView"

local v10 = "UI_CommonTask_MulTaskView"

local v11 = "UI_TeamActi_MainView"

local v12 = "UI_CommonJumpActivity_View"

local v13 = 14

local v14 = "ANTTotalLogin"

local v15 = "ANTLinearRedeem"

local v16 = "ANTTeamRank"

local v17 = "ANTGuide"

local v18 = 0

local v19 = "每周惊喜上新"

local v20 = "AG_Farm"

local v21 = {
"奖励商城",
"兑换任务"
}

local v22 = 5100

local v23 = "T_Common_Icon_activity_Small08"

local v24 = "T_Common_Icon_activity_Small10"

local v25 = {
"AG_Arena"
}

local data = {
[30005] = {
id = 30005,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1729180800
}
},
labelId = 1,
lowVersion = "1.3.26.1",
activityName = "轻松减负季",
activityUIDetail = "UI_Activity_EasyNavigationBar_View",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTFridayCollection",
titleType = 0,
activitySubName = "奖励多快乐领",
activityGroup = v20
},
[30006] = {
id = 30006,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1726156799
},
showEndTime = {
seconds = 1726156799
},
showBeginTime = {
seconds = 1725552000
}
},
labelId = 5,
lowVersion = "1.3.18.23",
activityName = "寻秘冒险礼",
activityUIDetail = "UI_SeasonTheme_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1000101,
1000100
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送背饰相机包"
},
[30007] = {
id = 30007,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 5,
backgroundUrl = {
"youranfengshou.astc"
},
activityName = "悠然丰收季",
activityParam = {
25,
3170,
652029
},
activityDesc = "农场增益持续，悠然丰收不停！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001012,
1001013,
1001014
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3170
},
activitySubName = "农场增益返场"
},
[30008] = {
id = 30008,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1723737600
}
},
labelId = 4,
backgroundUrl = {
"dashixiulianji.astc"
},
lowVersion = "1.3.12.132",
activityName = "大师修炼季",
activityParam = {
24,
3169,
652036
},
activityDesc = "轻松做任务，领狼人丰厚专属奖励",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001008,
1001009,
1001010,
1001011
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3169
},
activitySubName = "轻松领礼包"
},
[30009] = {
id = 30009,
activityType = "ATFishingHallOfFame",
timeInfo = {
beginTime = {
seconds = 1715788800
},
endTime = {
seconds = 1718899199
},
showEndTime = {
seconds = 1718899199
},
showBeginTime = {
seconds = 1715788800
}
},
labelId = 2,
activityName = "鱼塘幸运星",
activityParam = {
8
},
activityUIDetail = "UI_Activity_GoFishing_MainView",
isInBottom = 1,
activityRuleId = 225,
tagId = 1,
activityTaskGroup = {
1000003,
1000004
},
showInCenter = true,
activityNameType = "ANTFishingHallOfFame",
isHideMainBackground = true,
titleType = 0,
activitySubName = "彰显渔王风范",
activityGroup = v20
},
[30012] = {
id = 30012,
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 3,
backgroundUrl = {
"libaihuodong_3.astc"
},
activityName = "峡谷排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001015,
1001016
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "上分领取李白",
animAssets = {
"UI_Spine_Wangzhe_Libai_Huodong_atlas",
"UI_Spine_Wangzhe_Libai_Huodong_skel",
"ani_loop"
}
},
[30013] = {
id = 30013,
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1725551999
},
showEndTime = {
seconds = 1725551999
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 2,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078008,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1078002,
1078003,
1078004,
1078005,
1078008
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "免费领幻梦币"
},
[30014] = {
id = 30014,
timeInfo = {
beginTime = {
seconds = 1725120000
},
endTime = {
seconds = 1725724799
},
showEndTime = {
seconds = 1725724799
},
showBeginTime = {
seconds = 1725120000
}
},
labelId = 3,
backgroundUrl = {
"zhongkui_1.astc"
},
activityName = "一钩定乾坤",
activityUIDetail = v9,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001017
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "钟馗即将登场"
},
[30015] = {
id = 30015,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1735315199
},
showEndTime = {
seconds = 1735315199
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 1,
activityName = "狼人游学季",
activityParam = {
3165,
10
},
activityDesc = "轻松游玩，领取两个新身份",
activityUIDetail = "UI_Activity_WerewolfView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1001018,
1001019
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
3165
},
activitySubName = "连送两新身份"
},
[30016] = {
id = 30016,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729871999
},
showEndTime = {
seconds = 1729871999
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 10,
backgroundUrl = {
"nongchangxpz.astc"
},
lowVersion = "1.3.12.149",
activityName = "农场新篇章",
activityParam = {
24,
317100,
651029
},
activityDesc = "新赛季农场月卡限时体验！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001020,
1001021,
1001022
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = {
"奖励商城",
"兑换任务",
"兑换铃铛"
},
titleType = 0,
activityBeginCleanCoin = {
317100
},
activitySubName = "农场月卡体验",
activityGroup = v20
},
[30017] = {
id = 30017,
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1734019199
},
showEndTime = {
seconds = 1734019199
},
showBeginTime = {
seconds = 1724947200
},
validDay = 14
},
labelId = 15,
backgroundUrl = {
"nongchangxinren.astc"
},
lowVersion = "1.3.12.149",
activityName = "农场新人礼",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1001023
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "农场月卡体验",
activityGroup = v20
},
[30020] = {
id = 30020,
activityType = "ATCallWerewolf",
timeInfo = {
beginTime = {
seconds = 1726761600
},
endTime = {
seconds = 1729439999
},
showEndTime = {
seconds = 1729439999
},
showBeginTime = {
seconds = 1726761600
}
},
labelId = 3,
backgroundUrl = {
"bumengzhe2.astc"
},
activityName = "狼人捕梦旅",
activityParam = {
2,
3404,
651044
},
activityDesc = "集满灵石获密匙，助力<Yellow19>狼人新身份捕梦者</>解锁",
activityUIDetail = "UI_Activity_DreamCatcherJourneys",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078024,
1078023,
1078019,
1078020,
1078021,
1078022
},
showInCenter = true,
activityNameType = "ANTCallWerewolf",
titleType = 0,
activitySubName = "新身份亮相"
},
[30025] = {
id = 30025,
timeInfo = {
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1726156799
},
showEndTime = {
seconds = 1726156799
},
showBeginTime = {
seconds = 1725552000
}
},
labelId = 2,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078018,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1078012,
1078013,
1078014,
1078015,
1078018
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币"
},
[30026] = {
id = 30026,
timeInfo = {
beginTime = {
seconds = 1725724800
},
endTime = {
seconds = 1726329599
},
showEndTime = {
seconds = 1726329599
},
showBeginTime = {
seconds = 1725724800
}
},
labelId = 3,
backgroundUrl = {
"libaihuodong_1.astc"
},
activityName = "峡谷女明星",
activityUIDetail = v9,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001080
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "双英雄迎中秋"
},
[30035] = {
id = 30035,
activityType = "ATActivityCompetitionWarmUp",
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1748447999
},
showEndTime = {
seconds = 1748447999
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 1,
backgroundUrl = {
"libaihuodong_1.astc"
},
activityName = "中秋来团战",
activityUIDetail = "UI_Season_ActivityTimeLimit",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078999
},
showInCenter = true,
activityNameType = "ANTActivityCompetitionWarmUp",
titleType = 0,
activitySubName = "开黑不掉分"
},
[30045] = {
id = 30045,
activityType = "ATWolfKillSquadTrophy",
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1736870399
},
showEndTime = {
seconds = 1736870399
},
showBeginTime = {
seconds = 1727712000
}
},
labelId = 2,
activityName = "狼人默契队",
activityParam = {
651071
},
activityUIDetail = "UI_WolfKillTeam_MainView",
isInBottom = 1,
activityRuleId = 245,
tagId = 14,
activityTaskGroup = {
1000110,
1000111,
1000112
},
showInCenter = true,
activityNameType = "ANTWolfKillSquadTrophy",
isHideMainBackground = true,
titleType = 0,
activitySubName = "组搭子领配饰"
},
[30037] = {
id = 30037,
timeInfo = {
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1726761599
},
showEndTime = {
seconds = 1726761599
},
showBeginTime = {
seconds = 1726156800
}
},
labelId = 3,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078038,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1078032,
1078033,
1078034,
1078035,
1078038
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币"
},
[30038] = {
id = 30038,
timeInfo = {
beginTime = {
seconds = 1725120000
},
endTime = {
seconds = 1726934399
},
showEndTime = {
seconds = 1726934399
},
showBeginTime = {
seconds = 1725120000
}
},
labelId = 4,
backgroundUrl = {
"wangzhaojun_1.astc"
},
activityName = "冰雪之华",
activityUIDetail = v9,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001081
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "王昭君将登场"
},
[30036] = {
id = 30036,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1726588799
},
showEndTime = {
seconds = 1726588799
},
showBeginTime = {
seconds = 1726329600
}
},
labelId = 99,
lowVersion = "1.3.18.37",
activityName = "中秋来团战",
activityUIDetail = v11,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1078998
},
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "开黑不掉分"
},
[30040] = {
id = 30040,
timeInfo = {
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1726761599
},
showEndTime = {
seconds = 1726761599
},
showBeginTime = {
seconds = 1726156800
}
},
labelId = 5,
activityName = "月球大冒险",
activityUIDetail = v10,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1000120,
1000121
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "狼人新图上线"
},
[30043] = {
id = 30043,
timeInfo = {
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1727366399
},
showEndTime = {
seconds = 1727366399
},
showBeginTime = {
seconds = 1726156800
}
},
labelId = 3,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078048,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1078042,
1078043,
1078044,
1078045,
1078048
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币"
},
[30044] = {
id = 30044,
timeInfo = {
beginTime = {
seconds = 1725120000
},
endTime = {
seconds = 1727539199
},
showEndTime = {
seconds = 1727539199
},
showBeginTime = {
seconds = 1725120000
}
},
labelId = 4,
backgroundUrl = {
"wangzhaojun_1.astc"
},
activityName = "峡谷机关师",
activityUIDetail = v9,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001082
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "墨子即将登场"
},
[30099] = {
id = 30099,
timeInfo = {
beginTime = {
seconds = 1726243200
},
endTime = {
seconds = 1727711999
},
showEndTime = {
seconds = 1727711999
},
showBeginTime = {
seconds = 1726243200
}
},
labelId = 1,
activityName = "通用跑马灯",
activityDesc = "活动期间登录游戏并保持在线，即可领取海量道具好礼！各位星宝不要错过哦",
activityUIDetail = "UI_ActivityMarquee_Tips",
tagId = 1,
activityTaskGroup = {
1078049
},
showInCenter = true,
activityNameType = "ANTActivityMarquee",
titleType = 0,
activitySubName = "通用跑马灯"
},
[30046] = {
id = 30046,
timeInfo = {
beginTime = {
seconds = 1726243200
},
endTime = {
seconds = 1727711999
},
showEndTime = {
seconds = 1727711999
},
showBeginTime = {
seconds = 1726243200
}
},
labelId = 2,
activityName = "节庆跑马灯",
activityDesc = "活动期间登录游戏并保持在线，即可领取海量道具好礼！各位星宝不要错过哦",
activityUIDetail = "UI_ActivityMarquee_Tips",
tagId = 1,
activityTaskGroup = {
1078050
},
showInCenter = true,
activityNameType = "ANTActivityMarquee",
titleType = 0,
activitySubName = "节庆跑马灯"
},
[30050] = {
id = 30050,
timeInfo = {
beginTime = {
seconds = 1727625600
},
endTime = {
seconds = 1744905599
},
showEndTime = {
seconds = 1744905599
},
showBeginTime = {
seconds = 1727625600
}
},
labelId = 2,
lowVersion = "*********",
activityName = "七日首胜礼",
activityUIDetail = "UI_Arena_NationalDayReward",
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1078055,
1078155
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = "李白青莲剑"
},
[30051] = {
id = 30051,
timeInfo = {
beginTime = {
seconds = 1727366400
},
endTime = {
seconds = 1727711999
},
showEndTime = {
seconds = 1727711999
},
showBeginTime = {
seconds = 1727366400
}
},
labelId = 3,
backgroundUrl = {
"fanbeifen_3.astc"
},
lowVersion = "*********",
activityName = "来峡谷开黑",
activityUIDetail = v9,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1078056
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "上分效率翻倍"
},
[30052] = {
id = 30052,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728316799
},
showEndTime = {
seconds = 1728316799
},
showBeginTime = {
seconds = 1727712000
}
},
labelId = 3,
backgroundUrl = {
"fanbeifen_2.astc"
},
jumpId = 1016,
lowVersion = "*********",
activityName = "来峡谷开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 13,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "上分效率翻倍"
},
[30053] = {
id = 30053,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728316799
},
showEndTime = {
seconds = 1728316799
},
showBeginTime = {
seconds = 1727712000
}
},
labelId = 99,
lowVersion = "*********",
activityName = "来峡谷开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 13,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "上分效率翻倍"
},
[30060] = {
id = 30060,
timeInfo = {
beginTime = {
seconds = 1736352000
},
endTime = {
seconds = 1738339199
},
showEndTime = {
seconds = 1738339199
},
showBeginTime = {
seconds = 1736352000
}
},
labelId = 1,
lowVersion = "*********",
activityName = "峡谷大事件",
activityUIDetail = "UI_Arena_NationalDayMain",
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "峡谷上新不停"
},
[30069] = {
id = 30069,
activityType = "ATMonopoly",
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1730390399
},
showEndTime = {
seconds = 1730390399
},
showBeginTime = {
seconds = 1729180800
}
},
labelId = 2,
activityName = "奇趣派对礼",
activityUIDetail = "UI_Activity_WolfKillPlaygroundView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1001192,
1001193,
1001194
},
showInCenter = true,
activityNameType = "ANTMonopoly",
activityShopType = {
3413
},
titleType = 0,
activitySubName = "狼人主题赛季"
},
[30061] = {
id = 30061,
activityType = "ATDailyColorPaint",
timeInfo = {
beginTime = {
seconds = 1725120000
},
endTime = {
seconds = 1731254399
},
showEndTime = {
seconds = 1731254399
},
showBeginTime = {
seconds = 1725120000
}
},
labelId = 10,
backgroundUrl = {
"nnnewtuzi.astc"
},
activityName = "国潮涂色屋",
activityUIDetail = "UI_Activity_DailyDrawView_VictoryTeam",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078057,
1078058
},
showInCenter = true,
activityNameType = "ANTDailyColorPaint",
titleType = 0,
activitySubName = "农场小屋家具",
activityGroup = v20
},
[30056] = {
id = 30056,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1727625600
},
endTime = {
seconds = 1728316799
},
showEndTime = {
seconds = 1728316799
},
showBeginTime = {
seconds = 1727625600
}
},
labelId = 6,
backgroundUrl = {
"langrennongchang.astc"
},
jumpId = 1202,
activityName = "双重惊喜礼",
activityUIDetail = v12,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "假期快乐畅玩",
activityGroup = v20
},
[30057] = {
id = 30057,
timeInfo = {
beginTime = {
seconds = 1728576000
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1728576000
}
},
labelId = 1,
backgroundUrl = {
"langrenyueqiu.astc"
},
activityName = "派对前夜",
mailId = 0,
activityUIDetail = v10,
tagId = 1,
activityTaskGroup = {
1078062,
1078063
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送狼人BP值"
},
[30064] = {
id = 30064,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1727366400
},
endTime = {
seconds = 1728316799
},
showEndTime = {
seconds = 1728316799
},
showBeginTime = {
seconds = 1727366400
}
},
labelId = 8,
backgroundUrl = {
"shuizuguan.astc"
},
jumpId = 378,
activityName = "水族馆开业",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30065] = {
id = 30065,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1727366400
},
endTime = {
seconds = 1728316799
},
showEndTime = {
seconds = 1728316799
},
showBeginTime = {
seconds = 1727366400
}
},
labelId = 8,
backgroundUrl = {
"shuizuguan.astc"
},
jumpId = 379,
activityName = "水族馆开业",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30066] = {
id = 30066,
timeInfo = {
beginTime = {
seconds = 1728576000
},
endTime = {
seconds = 1729439999
},
showEndTime = {
seconds = 1729439999
},
showBeginTime = {
seconds = 1728576000
}
},
labelId = 4,
backgroundUrl = {
"daji_1.astc"
},
activityName = "妲己陪你玩",
activityUIDetail = v9,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001182
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "来迎接新赛季"
},
[30067] = {
id = 30067,
timeInfo = {
beginTime = {
seconds = 1728576000
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1728576000
}
},
labelId = 1,
backgroundUrl = {
"chongfen_1.astc"
},
activityName = "峡谷上分周",
activityUIDetail = v10,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001183,
1001184
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "每日排位福利"
},
[30068] = {
id = 30068,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1727712000
}
},
labelId = 5,
backgroundUrl = {
"youranfengshou.astc"
},
activityName = "农场丰收季",
activityParam = {
25,
3412,
651161
},
activityDesc = "秋日丰收，欢乐满仓！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001187,
1001185,
1001186
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3412
},
activitySubName = "免费稀有配饰",
activityGroup = v20
},
[30048] = {
id = 30048,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1730217600
},
endTime = {
seconds = 1731945599
},
showEndTime = {
seconds = 1731945599
},
showBeginTime = {
seconds = 1730217600
}
},
labelId = 5,
backgroundUrl = {
"xingtianqi.astc"
},
activityName = "农场星天气",
activityParam = {
25,
317102,
661227
},
activityDesc = "完成任务领取礼物盒皮肤！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1071231,
1071230,
1071229,
1071228
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
25
},
clientParams = {
"奖励商城",
"兑换任务",
"兑换道具"
},
titleType = 0,
activityBeginCleanCoin = {
317102
},
activitySubName = "领取农场装饰",
activityGroup = v20
},
[30070] = {
id = 30070,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1728489600
},
endTime = {
seconds = 1729439999
},
showEndTime = {
seconds = 1729439999
},
showBeginTime = {
seconds = 1728489600
}
},
labelId = 3,
backgroundUrl = {
"xiaotiandou.astc"
},
activityName = "农场星愿夜",
activityParam = {
32,
3414
},
activityDesc = "免费领农场月卡3日体验！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1000129,
1000130,
1000131
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
32
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3414
},
activitySubName = "农场月卡体验",
activityGroup = v20
},
[30047] = {
id = 30047,
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1767110399
},
showEndTime = {
seconds = 1767110399
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 7,
backgroundUrl = {
"xinzeng01.astc"
},
jumpId = 5100,
lowVersion = "1.3.26.23",
activityName = "新人登录礼",
activityUIDetail = "UI_Activity_AccumulativeLogin_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1000132,
1001188
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "农场专属时装",
activityGroup = "AG_OnlyFarm",
createFarmAfter = {
seconds = 1729785600
},
hideWhenCompleted = 1,
thumbnail = v23
},
[30071] = {
id = 30071,
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1729785599
},
showEndTime = {
seconds = 1729785599
},
showBeginTime = {
seconds = 1729180800
}
},
labelId = 3,
backgroundUrl = {
"langrenqiqupaidui2.astc"
},
activityName = "畅玩新身份",
activityUIDetail = v9,
isInBottom = 1,
tagId = 13,
activityTaskGroup = {
1001195
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "领新赛季道具"
},
[30072] = {
id = 30072,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1729094400
},
endTime = {
seconds = 1729439999
},
showEndTime = {
seconds = 1729439999
},
showBeginTime = {
seconds = 1729094400
}
},
labelId = 6,
backgroundUrl = {
"jiagong.astc"
},
jumpId = 5100,
lowVersion = "1.3.18.80",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30080] = {
id = 30080,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1727366400
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1727366400
}
},
labelId = 8,
backgroundUrl = {
"mengchongshengji.astc"
},
jumpId = 433,
activityName = "萌宠新互动",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30081] = {
id = 30081,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1727366400
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1727366400
}
},
labelId = 8,
backgroundUrl = {
"mengchongshengji.astc"
},
jumpId = 434,
activityName = "萌宠新互动",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30085] = {
id = 30085,
activityType = "ATCallWerewolf",
timeInfo = {
beginTime = {
seconds = 1731772800
},
endTime = {
seconds = 1732550399
},
showEndTime = {
seconds = 1732550399
},
showBeginTime = {
seconds = 1731772800
}
},
labelId = 3,
backgroundUrl = {
"bumengzhe2.astc"
},
activityName = "一起养绿植",
activityParam = {
2,
317103,
661136
},
activityDesc = "积攒雨露领取兑换卷，将绿植家具带回家！",
activityUIDetail = "UI_Activity_PlantTreesView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071201,
1071200,
1071196,
1071197,
1071198,
1071199
},
showInCenter = true,
activityNameType = "ANTCallWerewolf",
titleType = 0,
activitySubName = "免费绿植家具",
activityGroup = v20
},
[30086] = {
id = 30086,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1728489600
},
endTime = {
seconds = 1730303999
},
showEndTime = {
seconds = 1730303999
},
showBeginTime = {
seconds = 1728489600
}
},
labelId = 3,
backgroundUrl = {
"xiaotiandou.astc"
},
activityName = "梦幻收藏季",
activityParam = {
32,
3417
},
activityDesc = "来星宝农场探寻神秘珍宝！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1071202,
1071203
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
32
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3417
},
activitySubName = "神秘藏品上线",
activityGroup = v20
},
[30073] = {
id = 30073,
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730390399
},
showEndTime = {
seconds = 1730390399
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 3,
backgroundUrl = {
"langrenqiqupaidui2.astc"
},
activityName = "畅玩新身份",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071209
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "领新赛季道具"
},
[30075] = {
id = 30075,
timeInfo = {
beginTime = {
seconds = 1729440000
},
endTime = {
seconds = 1730044799
},
showEndTime = {
seconds = 1730044799
},
showBeginTime = {
seconds = 1729440000
}
},
labelId = 6,
backgroundUrl = {
"dongfangyao_1.astc"
},
activityName = "星辰之子",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002001
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "曜即将登场"
},
[30076] = {
id = 30076,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1729180800
}
},
labelId = 2,
backgroundUrl = {
"dajisaiji_4.astc"
},
activityName = "峡谷排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002002,
1002003
},
showInCenter = true,
activityNameType = "ANTSingleLogin",
titleType = 0,
activitySubName = "上分领取妲己"
},
[30077] = {
id = 30077,
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1729785599
},
showEndTime = {
seconds = 1729785599
},
showBeginTime = {
seconds = 1729180800
}
},
labelId = 3,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078074,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078070,
1078071,
1078072,
1078073,
1078074
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币",
pakGroupId = 50021
},
[30087] = {
id = 30087,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730649599
},
showEndTime = {
seconds = 1730649599
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 3,
activityName = "狼人魔术夜",
activityParam = {
24,
3420,
661170
},
activityDesc = "免费领稀有配饰！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071208,
1071206,
1071207
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3420
},
activitySubName = "南瓜变身派对"
},
[30089] = {
id = 30089,
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730390399
},
showEndTime = {
seconds = 1730390399
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 2,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078079,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078075,
1078076,
1078077,
1078078,
1078079
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币",
pakGroupId = 50021
},
[30090] = {
id = 30090,
timeInfo = {
beginTime = {
seconds = 1730044800
},
endTime = {
seconds = 1730649599
},
showEndTime = {
seconds = 1730649599
},
showBeginTime = {
seconds = 1730044800
}
},
labelId = 4,
backgroundUrl = {
"makeboluo_1.astc"
},
activityName = "远游之枪",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002004
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "马可即将登场"
},
[30091] = {
id = 30091,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730390399
},
showEndTime = {
seconds = 1730390399
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 4,
backgroundUrl = {
"wanshengfb_4.astc"
},
jumpId = 1016,
activityName = "排位分翻倍",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "万圣前夜开黑"
},
[30092] = {
id = 30092,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730390399
},
showEndTime = {
seconds = 1730390399
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 99,
activityName = "排位分翻倍",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "万圣前夜开黑"
},
[30093] = {
id = 30093,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1729699200
},
endTime = {
seconds = 1730044799
},
showEndTime = {
seconds = 1730044799
},
showBeginTime = {
seconds = 1729699200
}
},
labelId = 6,
backgroundUrl = {
"fszzw.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30094] = {
id = 30094,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1731254400
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1731254400
}
},
labelId = 2,
backgroundUrl = {
"T_RegularExchange02_Img_Background.astc"
},
jumpId = 5100,
activityName = "农场赠礼季",
activityParam = {
317104,
10
},
activityDesc = "赠人玫瑰，手留余香",
activityUIDetail = "UI_Activity_DrawLinear03_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071210,
1071211,
1071212
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
317104
},
activitySubName = "领招财猫头像",
activityGroup = v20
},
[30100] = {
id = 30100,
timeInfo = {
beginTime = {
seconds = 1730649600
},
endTime = {
seconds = 1731254399
},
showEndTime = {
seconds = 1731254399
},
showBeginTime = {
seconds = 1730649600
}
},
labelId = 4,
backgroundUrl = {
"gongbenwuzang_1.astc"
},
activityName = "天下无双",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002005
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "宫本即将登场"
},
[30101] = {
id = 30101,
timeInfo = {
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
showEndTime = {
seconds = 1730995199
},
showBeginTime = {
seconds = 1730390400
}
},
labelId = 2,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078084,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078080,
1078081,
1078082,
1078083,
1078084
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币",
pakGroupId = 50021
},
[30102] = {
id = 30102,
timeInfo = {
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730649599
},
showEndTime = {
seconds = 1730649599
},
showBeginTime = {
seconds = 1730390400
}
},
labelId = 6,
activityName = "无限火力赛",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071213
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "闪电赛新主题"
},
[30103] = {
id = 30103,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
showEndTime = {
seconds = 1730995199
},
showBeginTime = {
seconds = 1730390400
}
},
labelId = 3,
activityName = "互动补给站",
activityParam = {
25,
3423
},
activityDesc = "免费领新赛季互动道具！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071214,
1071215,
1071216
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3423
},
activitySubName = "送互动道具"
},
[30104] = {
id = 30104,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730995199
},
showEndTime = {
seconds = 1730995199
},
showBeginTime = {
seconds = 1729785600
}
},
labelId = 5,
backgroundUrl = {
"fengshouji.astc"
},
activityName = "田园耕耘季",
activityParam = {
25,
3424,
661195
},
activityDesc = "一分耕耘，一分收获！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071219,
1071217,
1071218
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3424
},
activitySubName = "送稀有配饰",
activityGroup = v20
},
[30105] = {
id = 30105,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1730304000
},
endTime = {
seconds = 1730649599
},
showEndTime = {
seconds = 1730649599
},
showBeginTime = {
seconds = 1730304000
}
},
labelId = 6,
backgroundUrl = {
"fszzw.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30120] = {
id = 30120,
activityType = "ATFishingHallOfFame",
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 2,
lowVersion = "1.3.26.92",
activityName = "鱼塘幸运星",
activityParam = {
11
},
activityDesc = "元梦之星第二届鱼塘幸运星活动，尽享鱼塘欢乐，好运与你同行！",
activityUIDetail = "UI_Activity_GoFishing_MainView",
isInBottom = 1,
activityRuleId = 225,
tagId = 14,
activityTaskGroup = {
1071222,
1071223,
1071232
},
showInCenter = true,
activityNameType = "ANTFishingHallOfFame",
isHideMainBackground = true,
clientParams = {
"661228"
},
titleType = 0,
activityBeginCleanCoin = {
11
},
activitySubName = "彰显渔王风范",
activityGroup = v20
},
[30108] = {
id = 30108,
activityType = "ATLotteryDraw",
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1727712000
}
},
labelId = 1,
activityName = "狼人新身份",
activityUIDetail = "UI_WerewolfDart_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071224,
1071225
},
showInCenter = true,
activityNameType = "ANLotteryDraw",
titleType = 0,
activityBeginCleanCoin = {
3425
},
activitySubName = "免费领雾隐狼"
},
[30110] = {
id = 30110,
activityType = "ATBookOfFriends",
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1743436799
},
showEndTime = {
seconds = 1743436799
},
showBeginTime = {
seconds = 1727712000
}
},
labelId = 4,
activityName = "鹿篮招募礼",
activityDesc = "招募玩家随机增加进度！",
activityUIDetail = "UI_Activity_GreenHouseNew_MainView",
isInBottom = 1,
activityRuleId = 302,
tagId = 1,
activityTaskGroup = {
1071227
},
showInCenter = true,
activityNameType = "ANTWish",
clientParams = {
"1"
},
titleType = 0,
activitySubName = "免费农场配饰",
activityGroup = v20,
activityAssistType = "AAT_GreenHouse"
},
[30111] = {
id = 30111,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1730304000
},
endTime = {
seconds = 1730649599
},
showEndTime = {
seconds = 1730649599
},
showBeginTime = {
seconds = 1730304000
}
},
labelId = 6,
backgroundUrl = {
"fszzw.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30115] = {
id = 30115,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1731254399
},
showEndTime = {
seconds = 1731254399
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 3,
backgroundUrl = {
"zmkaihei_4.astc"
},
jumpId = 1016,
activityName = "排位分翻倍",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "周末开黑福利"
},
[30116] = {
id = 30116,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1731254399
},
showEndTime = {
seconds = 1731254399
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 99,
activityName = "排位分翻倍",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "周末开黑福利"
},
[30117] = {
id = 30117,
timeInfo = {
beginTime = {
seconds = 1731254400
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1731254400
}
},
labelId = 4,
backgroundUrl = {
"xiahoudun_1.astc"
},
activityName = "不羁之风",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002006
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "夏侯即将登场"
},
[30118] = {
id = 30118,
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1731513599
},
showEndTime = {
seconds = 1731513599
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 5,
backgroundUrl = {
"xiaguchiji_1.astc"
},
activityName = "峡谷新体验",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002007
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "吃鸡玩法上新"
},
[30119] = {
id = 30119,
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1731599999
},
showEndTime = {
seconds = 1731599999
},
showBeginTime = {
seconds = 1730995200
}
},
labelId = 2,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078089,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078085,
1078086,
1078087,
1078088,
1078089
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币",
pakGroupId = 50021
},
[30029] = {
id = 30029,
activityType = "ATThemeAdventure",
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1767110399
},
showEndTime = {
seconds = 1767110399
},
showBeginTime = {
seconds = 1727712000
},
validDay = 60
},
labelId = 4,
lowVersion = "1.3.18.92",
activityName = "星界奇遇",
activityDesc = "星宝农场降临奇遇，跟随指引完成任务获海量专属好礼！",
activityUIDetail = "UI_ThemeAdventure_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1000126,
1000127,
1000128
},
showInCenter = true,
activityNameType = "ANTThemeAdventure",
activitySubName = "探索农场奥秘",
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30125] = {
id = 30125,
timeInfo = {
beginTime = {
seconds = 1731513600
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1731513600
}
},
labelId = 2,
backgroundUrl = {
"chijishangxin_2.astc"
},
activityName = "峡谷新体验",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078095
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送全新表情"
},
[30126] = {
id = 30126,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 3,
backgroundUrl = {
"zmkaihei_5.astc"
},
jumpId = 1016,
activityName = "周五开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30127] = {
id = 30127,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 99,
activityName = "周五开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30128] = {
id = 30128,
timeInfo = {
beginTime = {
seconds = 1731859200
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1731859200
}
},
labelId = 4,
backgroundUrl = {
"zhangliang_1.astc"
},
activityName = "缤纷绘卷",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002008
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "张良即将登场"
},
[30129] = {
id = 30129,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 2,
backgroundUrl = {
"muchangwuyu.astc"
},
activityName = "英雄周试练",
activityParam = {
1078094,
1015,
50021
},
activityUIDetail = "UI_Activity_LeagueHeroes_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078090,
1078091,
1078092,
1078093,
1078094
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "快来领幻梦币",
pakGroupId = 50021
},
[30130] = {
id = 30130,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1731513600
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1731513600
}
},
labelId = 6,
backgroundUrl = {
"fszzw.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30112] = {
id = 30112,
timeInfo = {
beginTime = {
seconds = 1731513600
},
endTime = {
seconds = 1731859199
},
showEndTime = {
seconds = 1731859199
},
showBeginTime = {
seconds = 1731513600
}
},
labelId = 3,
backgroundUrl = {
"caidanju1.astc"
},
activityName = "狼人彩蛋礼",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071233
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "限时对局房间"
},
[30131] = {
id = 30131,
activityType = "ATWolfLinearExchange",
timeInfo = {
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1733414400
}
},
labelId = 7,
jumpId = 310,
activityName = "狼人新人礼",
activityParam = {
0,
0,
651231,
651235
},
activityDesc = "完成<WerewolfBridalgift>10</>次谁是狼人对局送自选身份！",
activityUIDetail = "UI_Activity_WerewolfBridalgift_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1071234,
1071235
},
showInCenter = true,
activityNameType = v14,
clientParams = {
"30266"
},
titleType = 0,
activitySubName = "免费自选身份",
hideWhenCompleted = 1,
openTypeConfigs = {
{
type = 1,
params = {
0
}
}
},
activityGroupList = {
"AG_OnlyNR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30132] = {
id = 30132,
activityType = "ATKuromiTechou",
timeInfo = {
beginTime = {
seconds = 1734105600
},
endTime = {
seconds = 1735228799
},
showEndTime = {
seconds = 1735228799
},
showBeginTime = {
seconds = 1734105600
}
},
labelId = 1,
jumpId = 310,
activityName = "奇闻侦探社",
activityUIDetail = "UI_WerewolfGonzoDetective_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071236,
1071237,
1071238
},
showInCenter = true,
activityNameType = "ANTKuromiTechou",
titleType = 0,
activitySubName = "假面狼免费送",
activityGroup = v20,
moduleConfigs = {
{
type = "AMT_Task",
params = {
1071236,
1071237,
1071238
}
}
}
},
[30136] = {
id = 30136,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1732204800
}
},
labelId = 3,
backgroundUrl = {
"zmkaihei_5.astc"
},
jumpId = 1016,
activityName = "周五开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30137] = {
id = 30137,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1732204800
}
},
labelId = 99,
activityName = "周五开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30138] = {
id = 30138,
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1732204800
}
},
labelId = 4,
backgroundUrl = {
"chongfen_1.astc"
},
activityName = "峡谷上分周",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078099,
1078100
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "每日排位福利"
},
[30133] = {
id = 30133,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732118400
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1732118400
}
},
labelId = 6,
backgroundUrl = {
"caidanju2.astc"
},
jumpId = 323,
activityName = "狼人彩蛋局",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "限时对局房间"
},
[30134] = {
id = 30134,
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1732204800
}
},
labelId = 3,
backgroundUrl = {
"langrenzhentan.astc"
},
activityName = "狼人冲刺周",
mailId = 0,
activityUIDetail = v9,
tagId = 14,
activityTaskGroup = {
1071240
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "头号侦探将至"
},
[30139] = {
id = 30139,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732550399
},
showEndTime = {
seconds = 1732550399
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 8,
backgroundUrl = {
"shuizuguan.astc"
},
jumpId = 459,
activityName = "收藏品大赏",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30140] = {
id = 30140,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1732550399
},
showEndTime = {
seconds = 1732550399
},
showBeginTime = {
seconds = 1731600000
}
},
labelId = 8,
backgroundUrl = {
"shuizuguan.astc"
},
jumpId = 460,
activityName = "收藏品大赏",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30141] = {
id = 30141,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732118400
},
endTime = {
seconds = 1732463999
},
showEndTime = {
seconds = 1732463999
},
showBeginTime = {
seconds = 1732118400
}
},
labelId = 6,
backgroundUrl = {
"fszzw.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30142] = {
id = 30142,
timeInfo = {
beginTime = {
seconds = 1731945600
},
endTime = {
seconds = 1734278399
},
showEndTime = {
seconds = 1734278399
},
showBeginTime = {
seconds = 1731945600
}
},
labelId = 3,
backgroundUrl = {
"zhoumoshuangbei.astc"
},
activityName = "农场星彩蛋",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071241
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "周末翻倍提前",
activityGroup = v20
},
[30144] = {
id = 30144,
timeInfo = {
beginTime = {
seconds = 1732118400
},
endTime = {
seconds = 1733414399
},
showEndTime = {
seconds = 1733414399
},
showBeginTime = {
seconds = 1732118400
}
},
labelId = 3,
backgroundUrl = {
"NActivity_BgDUO6_mh.astc"
},
activityName = "大王新宇宙",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071242,
1071243,
1071244,
1071245,
1071246
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "遗迹合作寻宝"
},
[30145] = {
id = 30145,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1734019199
},
showEndTime = {
seconds = 1734019199
},
showBeginTime = {
seconds = 1732723200
}
},
labelId = 2,
backgroundUrl = {
"langrenniuniujie.astc"
},
lowVersion = "1.3.36.1",
activityName = "侦探季登场",
activityParam = {
24,
3511,
661278
},
activityDesc = "玩狼人头号侦探季，领专属新配饰~",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071249,
1071247,
1071248
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3511
},
activitySubName = "领稀有配饰"
},
[30150] = {
id = 30150,
timeInfo = {
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1737043199
},
showEndTime = {
seconds = 1737043199
},
showBeginTime = {
seconds = 1732723200
}
},
labelId = 2,
backgroundUrl = {
"dajisaiji_4.astc"
},
activityName = "峡谷排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078101,
1078102
},
showInCenter = true,
activityNameType = "ANTSingleLogin",
titleType = 0,
activitySubName = "上分领取马可",
activityGroupList = v25
},
[30151] = {
id = 30151,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1733068799
},
showEndTime = {
seconds = 1733068799
},
showBeginTime = {
seconds = 1732809600
}
},
labelId = 3,
backgroundUrl = {
"zmkaihei_5.astc"
},
jumpId = 1016,
activityName = "周五开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30152] = {
id = 30152,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1733068799
},
showEndTime = {
seconds = 1733068799
},
showBeginTime = {
seconds = 1732809600
}
},
labelId = 99,
activityName = "周五开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30153] = {
id = 30153,
timeInfo = {
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1733414399
},
showEndTime = {
seconds = 1733414399
},
showBeginTime = {
seconds = 1732723200
}
},
labelId = 4,
backgroundUrl = {
"chongfen_3.astc"
},
activityName = "峡谷新体验",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078103
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "全新卡牌羁绊",
activityGroupList = v25
},
[30154] = {
id = 30154,
activityType = "ATTravelingDog",
timeInfo = {
beginTime = {
seconds = 1732464000
},
endTime = {
seconds = 1734710399
},
showEndTime = {
seconds = 1734710399
},
showBeginTime = {
seconds = 1732464000
}
},
labelId = 1,
activityName = "旅行小狗",
activityParam = {
514
},
activityUIDetail = "UI_Activity_TravelDog_MainView",
isInBottom = 1,
activityRuleId = 292,
tagId = 14,
activityTaskGroup = {
1071301,
1071302,
1071303,
1071320,
1071321
},
showInCenter = true,
activityNameType = v14,
isHideMainBackground = true,
titleType = 0,
activitySubName = "免费领哈士奇",
activityGroup = v20,
currencyCfg = {
317106,
317105
}
},
[30155] = {
id = 30155,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732464000
},
endTime = {
seconds = 1734710399
},
showEndTime = {
seconds = 1734710399
},
showBeginTime = {
seconds = 1732464000
}
},
labelId = 3,
backgroundUrl = {
"zmkaihei_5.astc"
},
jumpId = 310,
activityName = "狼人组队",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "亲密度翻倍"
},
[30156] = {
id = 30156,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1732464000
},
endTime = {
seconds = 1734710399
},
showEndTime = {
seconds = 1734710399
},
showBeginTime = {
seconds = 1732464000
}
},
labelId = 99,
activityName = "狼人组队",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "亲密度翻倍"
},
[30157] = {
id = 30157,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1733068799
},
showEndTime = {
seconds = 1733068799
},
showBeginTime = {
seconds = 1732723200
}
},
labelId = 6,
backgroundUrl = {
"zzw1129.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30158] = {
id = 30158,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1732464000
},
endTime = {
seconds = 1733846399
},
showEndTime = {
seconds = 1733846399
},
showBeginTime = {
seconds = 1732464000
}
},
labelId = 6,
backgroundUrl = {
"zzw1129.astc"
},
jumpId = 493,
activityName = "柯南跳转测试",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "测试",
activityGroup = v20
},
[30159] = {
id = 30159,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737647999
},
showEndTime = {
seconds = 1737647999
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 3,
lowVersion = "1.3.37.54",
activityName = "周年庆导航",
activityUIDetail = "UI_Activity_BenefitNavigation_View",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTFridayCollection",
titleType = 0,
activitySubName = "奖励多快乐领",
activityGroup = v20
},
[30160] = {
id = 30160,
timeInfo = {
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735228800
}
},
labelId = 2,
activityName = "狼人福利局",
activityUIDetail = "UI_Activity_WerewolfOstrich_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071304,
1071305
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = "送鸵鸟新身份"
},
[30161] = {
id = 30161,
timeInfo = {
beginTime = {
seconds = 1732636800
},
endTime = {
seconds = 1734623999
},
showEndTime = {
seconds = 1734623999
},
showBeginTime = {
seconds = 1732636800
}
},
labelId = 3,
activityName = "新身份挑战",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071306
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "福利券大派送"
},
[30162] = {
id = 30162,
activityType = "ATMOBANewHeroTrial",
timeInfo = {
beginTime = {
seconds = 1731513600
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1731513600
}
},
labelId = 1,
activityName = "貂蝉试炼",
activityUIDetail = "UI_Arena_NewHeroTrain_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078096,
1078097,
1078098
},
showInCenter = true,
activityNameType = "ANTMOBANewHeroTrial",
titleType = 0,
activitySubName = "送全新表情",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
3
}
}
}
},
[30163] = {
id = 30163,
activityType = "ATArenaWeeklyActivity",
timeInfo = {
beginTime = {
seconds = 1733760000
},
endTime = {
seconds = 1735660799
},
showEndTime = {
seconds = 1735660799
},
showBeginTime = {
seconds = 1733760000
}
},
labelId = 1,
activityName = "组队开黑",
activityUIDetail = "UI_Arena_FridayActivity_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
10000004,
10000005
},
showInCenter = true,
activityNameType = "ANTArenaWeeklyActivity",
isHideMainBackground = true,
titleType = 0,
activitySubName = "活动测试"
},
[30182] = {
id = 30182,
activityType = "ATMobaSquadDrawRedPacket",
timeInfo = {
beginTime = {
seconds = 1733760000
},
endTime = {
seconds = 1738339199
},
showEndTime = {
seconds = 1738339199
},
showBeginTime = {
seconds = 1733846400
}
},
labelId = 1,
activityName = "开黑红包",
activityUIDetail = "UI_Activity_TeamRedPacket_Main",
isInBottom = 1,
activityRuleId = 400,
tagId = 14,
showInCenter = true,
activityNameType = "ANTArenaRedPacketActivity",
isHideMainBackground = true,
titleType = 1,
activitySubName = "活动测试"
},
[30164] = {
id = 30164,
timeInfo = {
beginTime = {
seconds = 1731945600
},
endTime = {
seconds = 1734278399
},
showEndTime = {
seconds = 1734278399
},
showBeginTime = {
seconds = 1731945600
}
},
labelId = 3,
backgroundUrl = {
"zhoumoshuangbei.astc"
},
activityName = "关注有惊喜",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071307
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "磷虾免费领",
activityGroup = v20,
platforms = {
1,
3
}
},
[30165] = {
id = 30165,
timeInfo = {
beginTime = {
seconds = 1731945600
},
endTime = {
seconds = 1734278399
},
showEndTime = {
seconds = 1734278399
},
showBeginTime = {
seconds = 1731945600
}
},
labelId = 3,
backgroundUrl = {
"zhoumoshuangbei.astc"
},
activityName = "关注有惊喜",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071308
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "磷虾免费领",
activityGroup = v20,
platforms = {
1,
3
}
},
[30166] = {
id = 30166,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1733328000
},
endTime = {
seconds = 1733673599
},
showEndTime = {
seconds = 1733673599
},
showBeginTime = {
seconds = 1733328000
}
},
labelId = 6,
backgroundUrl = {
"zzw1129.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30167] = {
id = 30167,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1748534399
},
showEndTime = {
seconds = 1748534399
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 3,
backgroundUrl = {
"zzw1129.astc"
},
activityName = "农场天天领",
activityParam = {
7,
5,
240,
6,
0,
3510
},
activityUIDetail = "UI_Activity_FarmLogin_MainView",
isInBottom = 1,
tagId = 4,
showInCenter = true,
activityNameType = "ANUpgradeCheckInManual",
activityShopType = {
167
},
titleType = 0,
activitySubName = "农场天天领",
activityGroup = v20,
relatedModeId = 30167,
thumbnail = v23
},
[30168] = {
id = 30168,
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1733846399
},
showEndTime = {
seconds = 1733846399
},
showBeginTime = {
seconds = 1732204800
}
},
labelId = 4,
backgroundUrl = {
"chongfen_1.astc"
},
activityName = "雪景卡池测试",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071309
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "雪景卡池测试",
activityGroup = v20
},
[30246] = {
id = 30246,
activityType = "ATAnniversaryMoaba",
timeInfo = {
beginTime = {
seconds = 1731427200
},
endTime = {
seconds = 1734710399
},
showEndTime = {
seconds = 1734710399
},
showBeginTime = {
seconds = 1731427200
}
},
labelId = 5,
backgroundUrl = {
"libaihuodong_1.astc"
},
jumpId = 520,
activityName = "峡谷福利",
activityParam = {
329920,
3601,
1,
3
},
activityUIDetail = "UI_Arena_Anniversary_Reward",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078109
},
showInCenter = true,
activityNameType = "ANTMOBANewHeroTrial",
clientParams = {
"135"
},
titleType = 0,
activitySubName = "免费送虞姬",
activityGroupList = v25
},
[30170] = {
id = 30170,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1734105600
},
endTime = {
seconds = 1735228799
},
showEndTime = {
seconds = 1735228799
},
showBeginTime = {
seconds = 1734105600
}
},
labelId = 3,
backgroundUrl = {
"yxxianmian_5.astc"
},
jumpId = 1016,
activityName = "峡谷福利",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "直售英雄限免"
},
[30171] = {
id = 30171,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1733932800
},
endTime = {
seconds = 1734364799
},
showEndTime = {
seconds = 1734364799
},
showBeginTime = {
seconds = 1733932800
}
},
labelId = 6,
backgroundUrl = {
"zzw1212.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30172] = {
id = 30172,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1734364800
},
endTime = {
seconds = 1735776000
},
showEndTime = {
seconds = 1735776000
},
showBeginTime = {
seconds = 1734364800
}
},
labelId = 6,
backgroundUrl = {
"ncrili.astc"
},
jumpId = 5100,
activityName = "周年狂欢季",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "增益不停歇",
activityGroup = v20
},
[30173] = {
id = 30173,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735228799
},
showEndTime = {
seconds = 1735228799
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 3,
backgroundUrl = {
"znfc1212_2.astc"
},
lowVersion = "*********",
activityName = "周年返场礼",
activityParam = {
25,
3452
},
activityDesc = "免费领互动道具！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071322,
1071323,
1071324
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3452
},
activitySubName = "舞蹈狼再亮相"
},
[30174] = {
id = 30174,
timeInfo = {
beginTime = {
seconds = 1731945600
},
endTime = {
seconds = 1734278399
},
showEndTime = {
seconds = 1734278399
},
showBeginTime = {
seconds = 1731945600
}
},
labelId = 3,
backgroundUrl = {
"zhoumoshuangbei.astc"
},
activityName = "观赛有好礼",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071325,
1071326,
1071327
},
showInCenter = true,
activityNameType = v14,
titleType = 0
},
[30175] = {
id = 30175,
timeInfo = {
beginTime = {
seconds = 1733760000
},
endTime = {
seconds = 1735833599
},
showEndTime = {
seconds = 1735833599
},
showBeginTime = {
seconds = 1733760000
}
},
labelId = 2,
activityName = "新人好礼",
activityDesc = "活动期间完成农场任务，即可领取海量道具，更有机会赢取现金好礼！各位星宝不要错过哦",
activityUIDetail = "UI_ActivityMarquee_Tips",
tagId = 1,
activityTaskGroup = {
1071330
},
showInCenter = true,
activityNameType = "ANTActivityMarquee",
clientParams = {
"https://image-manage.ymzx.qq.com/wuji/2024/09/27/vzR2OCqD.astc",
"b26509"
},
titleType = 0,
activitySubName = "农场新人礼",
createFarmAfter = {
seconds = 1733760000
},
hideWhenCompleted = 1
},
[30177] = {
id = 30177,
timeInfo = {
beginTime = {
seconds = 1734710400
},
endTime = {
seconds = 1735315199
},
showEndTime = {
seconds = 1735315199
},
showBeginTime = {
seconds = 1734710400
}
},
labelId = 2,
backgroundUrl = {
"yqltl1213_1.astc"
},
lowVersion = "*********",
activityName = "一起来推理",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071332
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送联动道具"
},
[30178] = {
id = 30178,
timeInfo = {
beginTime = {
seconds = 1733760000
},
endTime = {
seconds = 1735574399
},
showEndTime = {
seconds = 1735574399
},
showBeginTime = {
seconds = 1733760000
}
},
labelId = 2,
activityName = "新人惊喜",
activityDesc = "完成新人任务，领取惊喜好礼！",
activityUIDetail = "UI_ActivityMarquee_Tips",
tagId = 1,
activityTaskGroup = {
1071333
},
showInCenter = true,
activityNameType = "ANTActivityMarquee",
clientParams = {
"https://image-manage.ymzx.qq.com/wuji/2024/09/27/vzR2OCqD.astc",
"b26509"
},
titleType = 0,
activitySubName = "农场新人礼"
},
[30183] = {
id = 30183,
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 2,
lowVersion = "*********",
activityName = "5v5首胜礼",
activityUIDetail = "UI_Arena_NationalDayReward",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078120,
1078121
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = "峡谷专属播报"
},
[30184] = {
id = 30184,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735487999
},
showEndTime = {
seconds = 1735487999
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 3,
backgroundUrl = {
"zhouniankaihei_5.astc"
},
jumpId = 1016,
activityName = "周年开黑礼",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30185] = {
id = 30185,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735487999
},
showEndTime = {
seconds = 1735487999
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 99,
activityName = "周年开黑礼",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30186] = {
id = 30186,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1734883199
},
showEndTime = {
seconds = 1734883199
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 6,
backgroundUrl = {
"qxcdj1213.astc"
},
jumpId = 323,
lowVersion = "*********",
activityName = "全新彩蛋局",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "限时房间开放"
},
[30187] = {
id = 30187,
activityType = "ATFactionBattle",
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1736697599
},
showEndTime = {
seconds = 1736783999
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 1,
activityName = "阵营对决赛",
activityUIDetail = "UI_Activity_WerewolfShowdown_MainView",
isInBottom = 1,
activityRuleId = 298,
tagId = 14,
activityTaskGroup = {
1002014,
1002013,
1002010,
1002011,
1002012
},
showInCenter = true,
activityNameType = "ANTFactionBattle",
titleType = 0,
activitySubName = "送阵营头像",
platforms = {
1,
2,
3,
4,
5,
6
}
},
[30201] = {
id = 30201,
activityType = "ATArenaSevenDaysLogin",
activityName = "峡谷七日登录",
isInBottom = 1,
activityTaskGroup = {
1078201
},
activityNameType = "ANTArenaSevenDaysLogin",
activitySubName = "2天可得蔡文姬，7天可得专属卡"
},
[30188] = {
id = 30188,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1733025600
},
endTime = {
seconds = 1733414399
},
showEndTime = {
seconds = 1733414399
},
showBeginTime = {
seconds = 1733025600
}
},
labelId = 6,
backgroundUrl = {
"yingchuxue.astc"
},
jumpId = 531,
lowVersion = "1.3.37.67",
activityName = "农场迎初雪",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30189] = {
id = 30189,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1733025600
},
endTime = {
seconds = 1733414399
},
showEndTime = {
seconds = 1733414399
},
showBeginTime = {
seconds = 1733025600
}
},
labelId = 6,
backgroundUrl = {
"yingchuxue.astc"
},
jumpId = 532,
lowVersion = "1.3.37.67",
activityName = "农场迎初雪",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "投稿赢好礼",
activityGroup = v20
},
[30190] = {
id = 30190,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1735617600
},
endTime = {
seconds = 1736438399
},
showEndTime = {
seconds = 1736438399
},
showBeginTime = {
seconds = 1735617600
}
},
labelId = 2,
backgroundUrl = {
"ncxpy.astc"
},
lowVersion = "1.3.37.67",
activityName = "农场星朋友",
activityParam = {
24,
317107,
661368
},
activityDesc = "免费配饰姜饼背包等你来拿！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002015,
1002016,
1002017,
1002018
},
showInCenter = true,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
317107
},
activitySubName = "免费领取配饰",
activityGroup = v20
},
[30192] = {
id = 30192,
timeInfo = {
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1735833599
},
showEndTime = {
seconds = 1735833599
},
showBeginTime = {
seconds = 1735228800
}
},
labelId = 2,
backgroundUrl = {
"rxzfn.astc"
},
lowVersion = "1.3.37.67",
activityName = "瑞雪兆丰年",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002019
},
showInCenter = true,
titleType = 0,
activitySubName = "免费领取卡包",
activityGroup = v20
},
[30191] = {
id = 30191,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735228800
}
},
labelId = 2,
backgroundUrl = {
"ncgongju.astc"
},
jumpId = 533,
lowVersion = "1.3.37.55",
activityName = "农场助手",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "全新升级上线",
activityGroup = v20,
platforms = {
3
}
},
[30193] = {
id = 30193,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1735228800
},
endTime = {
seconds = 1735747199
},
showEndTime = {
seconds = 1735747199
},
showBeginTime = {
seconds = 1735228800
}
},
labelId = 2,
backgroundUrl = {
"baiwanxianjin.astc"
},
jumpId = 534,
lowVersion = "1.3.37.55",
activityName = "农场送福利",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "狂撒百万现金",
activityGroup = v20
},
[30194] = {
id = 30194,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735747200
}
},
labelId = 6,
backgroundUrl = {
"zzw1225.astc"
},
jumpId = 5100,
lowVersion = "1.3.37.55",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30196] = {
id = 30196,
activityType = "ATMOBANewHeroTrial",
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1736438399
},
showEndTime = {
seconds = 1736438399
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 2,
activityName = "虞姬试炼",
activityUIDetail = "UI_Arena_NewHeroTrain_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078301,
1078302,
1078303
},
showInCenter = true,
activityNameType = "ANTMOBANewHeroTrial",
titleType = 0,
activitySubName = "送紫色宝箱",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
3
}
}
}
},
[30197] = {
id = 30197,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 3,
backgroundUrl = {
"zhoumkh_5.astc"
},
jumpId = 50112,
activityName = "周末开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30198] = {
id = 30198,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735833600
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "峡谷收益翻倍"
},
[30199] = {
id = 30199,
timeInfo = {
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1735660800
}
},
labelId = 3,
backgroundUrl = {
"zhang222liang_1.astc"
},
activityName = "迎接2025",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078304
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送通行证经验"
},
[30200] = {
id = 30200,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1737907199
},
showEndTime = {
seconds = 1737907199
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 1,
activityName = "冰雪奇妙旅",
activityParam = {
3464,
10
},
activityUIDetail = "UI_Activity_WerewolfSnowTown_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002020,
1002021
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
3464
},
activitySubName = "送稀有配饰"
},
[30208] = {
id = 30208,
timeInfo = {
beginTime = {
seconds = 1725120000
},
endTime = {
seconds = 1736265599
},
showEndTime = {
seconds = 1736265599
},
showBeginTime = {
seconds = 1725120000
}
},
labelId = 1,
backgroundUrl = {
"zhongkui_1.astc"
},
activityName = "COC引流活动测试",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071351
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "COC引流活动测试"
},
[30209] = {
id = 30209,
timeInfo = {
beginTime = {
seconds = 1725120000
},
endTime = {
seconds = 1736265599
},
showEndTime = {
seconds = 1736265599
},
showBeginTime = {
seconds = 1725120000
}
},
labelId = 2,
backgroundUrl = {
"zhongkui_1.astc"
},
activityName = "COC每日攻城活动",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1071352
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "COC每日攻城活动",
srcPakGroupId = 20083
},
[30210] = {
id = 30210,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1725120000
},
endTime = {
seconds = 1736265599
},
showEndTime = {
seconds = 1736265599
},
showBeginTime = {
seconds = 1725120000
}
},
labelId = 3,
backgroundUrl = {
"fszzw.astc"
},
activityName = "COC繁荣度活动",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "COC繁荣度活动",
srcPakGroupId = 20083
},
[30220] = {
id = 30220,
activityType = "ATFarmSquadActivity",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
showEndTime = {
seconds = 1739462399
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 1,
activityName = "祈福小队",
activityUIDetail = "UI_FarmBlessingTeam_MainView",
isInBottom = 1,
activityRuleId = 320,
tagId = 14,
activityTaskGroup = {
1071360,
1071361,
1071362,
1071363,
1071364,
1071365
},
showInCenter = true,
activityNameType = "ANTFarmPraySquadActivity",
titleType = 0,
activitySubName = "免费领取霸福",
activityGroup = v20
},
[30202] = {
id = 30202,
activityType = "ATFindPartnerActivity",
timeInfo = {
beginTime = {
seconds = 1736006400
},
endTime = {
seconds = 1743091199
},
showEndTime = {
seconds = 1743091199
},
showBeginTime = {
seconds = 1736006400
}
},
labelId = 2,
activityName = "默契星搭子",
activityParam = {
6,
7,
8
},
activityDesc = "与搭子一起完成任务累计蝴蝶币，解锁更多奖励！",
activityUIDetail = "UI_DoubleTeam_MainView",
isInBottom = 1,
activityRuleId = 245,
tagId = 14,
activityTaskGroup = {
1002024,
1002025,
1002026,
1002027
},
showInCenter = true,
activityNameType = "ANTFindPartnerActivity",
isHideMainBackground = true,
titleType = 0,
activitySubName = "送非凡配饰"
},
[30203] = {
id = 30203,
timeInfo = {
beginTime = {
seconds = 1738857600
},
endTime = {
seconds = 1740671999
},
showEndTime = {
seconds = 1740671999
},
showBeginTime = {
seconds = 1738857600
}
},
labelId = 11,
backgroundUrl = {
"langrengaoguang2.astc"
},
activityName = "晒高光时刻",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002028
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "分享领狼人币",
platforms = {
1,
3,
6,
7
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30204] = {
id = 30204,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1746115199
},
showEndTime = {
seconds = 1746115199
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 7,
backgroundUrl = {
"langrenhuiguixitong.astc"
},
jumpId = 310,
activityName = "狼人回归礼",
activityUIDetail = v12,
isInBottom = 1,
tagId = 4,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "送自选身份"
},
[30205] = {
id = 30205,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1736352000
},
endTime = {
seconds = 1736697599
},
showEndTime = {
seconds = 1736697599
},
showBeginTime = {
seconds = 1736352000
}
},
labelId = 6,
backgroundUrl = {
"zzw0110.astc"
},
jumpId = 5100,
lowVersion = "1.3.37.55",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30206] = {
id = 30206,
timeInfo = {
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1737043199
},
showEndTime = {
seconds = 1737043199
},
showBeginTime = {
seconds = 1736438400
}
},
labelId = 2,
backgroundUrl = {
"yqltl1213_1.astc"
},
activityName = "农场预告季",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1072029
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "免费领取磷虾"
},
[30207] = {
id = 30207,
timeInfo = {
beginTime = {
seconds = 1736438400
},
endTime = {
seconds = 1737043199
},
showEndTime = {
seconds = 1737043199
},
showBeginTime = {
seconds = 1736438400
}
},
labelId = 1,
backgroundUrl = {
"langrenchongcizhou.astc"
},
activityName = "狼人冲刺周",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002029,
1002030
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "迎接冬日季"
},
[30211] = {
id = 30211,
activityType = "ATMOBANewHeroTrial",
timeInfo = {
beginTime = {
seconds = 1704816000
},
endTime = {
seconds = 1768579199
},
showEndTime = {
seconds = 1768579199
},
showBeginTime = {
seconds = 1704816000
}
},
labelId = 2,
activityName = "典韦试炼",
activityUIDetail = "UI_Arena_NewHeroTrain_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078305,
1078306,
1078307
},
showInCenter = true,
activityNameType = "ANTMOBANewHeroTrial",
titleType = 0,
activitySubName = "送幻梦币",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
10
}
}
},
activityGroupList = v25
},
[30212] = {
id = 30212,
activityType = "ATFarmReturn",
timeInfo = {
beginTime = {
seconds = 1737302400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1737302400
}
},
labelId = 1,
activityName = "玩法抢先看",
activityUIDetail = "UI_Activity_ReturnFarm_VersionMainView",
isInBottom = 1,
tagId = 15,
showInCenter = true,
slapFace = true,
activityNameType = v17,
titleType = 0,
activitySubName = "重返农场",
activityGroup = v20
},
[30213] = {
id = 30213,
activityType = "ATFarmSevenDayTask",
timeInfo = {
beginTime = {
seconds = 1737302400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1737302400
}
},
labelId = 2,
activityName = "福利任务站",
activityParam = {
329910
},
mailId = 265,
activityUIDetail = "UI_Activity_ReturnFarm_RewardMainView",
isInBottom = 1,
tagId = 15,
activityTaskGroup = {
1002063
},
showInCenter = true,
activityNameType = v17,
titleType = 0,
activityBeginCleanCoin = {
3470,
3471,
3472,
3473,
3474
},
activitySubName = "重返农场",
activityGroup = v20
},
[30214] = {
id = 30214,
activityType = "ATFarmBuffWish",
timeInfo = {
beginTime = {
seconds = 1737302400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1737302400
}
},
labelId = 3,
activityName = "增益专属礼",
activityParam = {
2008,
14
},
rewardInfo = {
{
itemId = 200642,
itemNum = 1
}
},
activityUIDetail = "UI_Activity_ReturnFarm_BenefitMainView",
isInBottom = 1,
tagId = 15,
showInCenter = true,
activityNameType = v15,
titleType = 0,
activitySubName = "重返农场",
activityGroup = v20
},
[30215] = {
id = 30215,
activityType = "ATFarmReturn",
timeInfo = {
beginTime = {
seconds = 1737302400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1737302400
}
},
labelId = 4,
activityName = "活动新资讯",
activityUIDetail = "UI_Activity_ReturnFarm_ActivityMainView",
isInBottom = 1,
tagId = 15,
showInCenter = true,
activityNameType = "ANTFridayCollection",
titleType = 0,
activitySubName = "重返农场",
activityGroup = v20
},
[30216] = {
id = 30216,
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1738857599
},
showEndTime = {
seconds = 1738857599
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 2,
activityName = "新春狼人局",
activityUIDetail = "UI_Activity_WerewolfCuttlefish_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002031,
1002032
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = "福利天天送"
},
[30217] = {
id = 30217,
activityType = "ATFishingHallOfFame",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1738857599
},
showEndTime = {
seconds = 1738857599
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 2,
lowVersion = "1.3.26.92",
activityName = "鱼塘幸运星",
activityParam = {
11,
9
},
activityDesc = "元梦之星新春幸运星活动，尽享鱼塘欢乐，好运与你同行！",
activityUIDetail = "UI_Activity_GoFishing_MainView",
isInBottom = 1,
activityRuleId = 225,
tagId = 14,
activityTaskGroup = {
1081001,
1081002,
1081003,
1081004
},
showInCenter = true,
activityNameType = "ANTFishingHallOfFame",
isHideMainBackground = true,
clientParams = {
"661423"
},
titleType = 0,
activitySubName = "一起来钓锦鲤",
activityGroup = v20
},
[30218] = {
id = 30218,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1736265600
},
endTime = {
seconds = 1738339199
},
showEndTime = {
seconds = 1738339199
},
showBeginTime = {
seconds = 1736265600
}
},
labelId = 1,
activityName = "新春导航",
activityUIDetail = "UI_Activity_HolidayNavigation_View",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTFridayCollection",
titleType = 0,
activitySubName = "奖励多快乐领",
activityGroup = v20
},
[30219] = {
id = 30219,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1736956800
},
endTime = {
seconds = 1737302399
},
showEndTime = {
seconds = 1737302399
},
showBeginTime = {
seconds = 1736956800
}
},
labelId = 6,
backgroundUrl = {
"zzw0117.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20
},
[30221] = {
id = 30221,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1739375999
},
showEndTime = {
seconds = 1739375999
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 6,
backgroundUrl = {
"ncyingxinchun.astc"
},
jumpId = 5100,
activityName = "农场迎新春",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "更新不停歇",
activityGroup = v20
},
[30222] = {
id = 30222,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1737345600
},
endTime = {
seconds = 1737907199
},
showEndTime = {
seconds = 1737907199
},
showBeginTime = {
seconds = 1737345600
}
},
labelId = 6,
backgroundUrl = {
"wenquanji.astc"
},
activityName = "冬日温泉季",
activityParam = {
24,
317115,
651301
},
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002034,
1002035,
1002036
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
317115
},
activitySubName = "领招财喵头像",
activityGroup = v20
},
[30223] = {
id = 30223,
timeInfo = {
beginTime = {
seconds = 1737475200
},
endTime = {
seconds = 1739375999
},
showEndTime = {
seconds = 1739375999
},
showBeginTime = {
seconds = 1737475200
}
},
labelId = 6,
backgroundUrl = {
"nchongbaoli.astc"
},
activityName = "农场红包礼",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002037,
1002038
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "红包送不停",
activityGroup = v20
},
[30224] = {
id = 30224,
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 1,
backgroundUrl = {
"xinsaiji_4.astc"
},
activityName = "峡谷排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078308,
1078309
},
showInCenter = true,
activityNameType = "ANTSingleLogin",
titleType = 0,
activitySubName = "上分领取亚瑟",
activityGroupList = {
"AG_OnlyArena"
}
},
[30225] = {
id = 30225,
activityType = "ATMOBANewHeroTrial",
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1737647999
},
showEndTime = {
seconds = 1737647999
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 2,
activityName = "卡牌羁绊",
activityUIDetail = "UI_Arena_NewCardTrain_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078310,
1078311,
1078312
},
showInCenter = true,
activityNameType = "ANTMOBANewHeroTrial",
titleType = 0,
activitySubName = "送星运宝箱",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
11
}
}
},
activityGroupList = v25
},
[30226] = {
id = 30226,
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1737302399
},
showEndTime = {
seconds = 1737302399
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 3,
backgroundUrl = {
"suijishijian_1.astc"
},
activityName = "峡谷新体验",
activityUIDetail = v9,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078313
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "全新随机事件"
},
[30227] = {
id = 30227,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1737907199
},
showEndTime = {
seconds = 1737907199
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 1,
backgroundUrl = {
"dawangpaimingshoufa.astc"
},
activityName = "大王星玩法",
activityParam = {
25,
3465
},
activityDesc = "免费领全新动作！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002040,
1002042
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3465
},
activitySubName = "送全新动作",
platforms = {
1,
2,
3,
4,
5,
6
}
},
[30228] = {
id = 30228,
activityType = "ATFactionBattle",
timeInfo = {
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740326399
},
showEndTime = {
seconds = 1740585599
},
showBeginTime = {
seconds = 1739462400
}
},
labelId = 1,
activityName = "阵营对决赛",
activityUIDetail = "UI_Activity_WerewolfShowdown_MainView",
isInBottom = 1,
activityRuleId = 325,
tagId = 14,
activityTaskGroup = {
1002047,
1002046,
1002043,
1002044,
1002045
},
showInCenter = true,
activityNameType = "ANTFactionBattle",
titleType = 0,
activitySubName = "送稀有配饰",
activityGroupList = {
"AG_NR3E"
},
thumbnail = v24
},
[30229] = {
id = 30229,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1738512000
},
endTime = {
seconds = 1739375999
},
showEndTime = {
seconds = 1739375999
},
showBeginTime = {
seconds = 1738512000
}
},
labelId = 5,
backgroundUrl = {
"xingtianqi.astc"
},
activityName = "农场送装饰",
activityParam = {
24,
317119,
659119
},
activityDesc = "完成任务领取狗屋皮肤！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002067,
1002064,
1002065,
1002066
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = {
"奖励商城",
"兑换任务",
"兑换道具"
},
titleType = 0,
activityBeginCleanCoin = {
317119
},
activitySubName = "免费春节狗窝",
activityGroup = v20
},
[30230] = {
id = 30230,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1737561600
},
endTime = {
seconds = 1739116799
},
showEndTime = {
seconds = 1739116799
},
showBeginTime = {
seconds = 1737561600
}
},
labelId = 6,
backgroundUrl = {
"nckuanghuanjie.astc"
},
jumpId = 5100,
activityName = "农场狂欢节",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "一起迎新年",
activityGroup = v20
},
[30231] = {
id = 30231,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1738598399
},
showEndTime = {
seconds = 1738598399
},
showBeginTime = {
seconds = 1737993600
}
},
labelId = 4,
backgroundUrl = {
"chunjiecaidanju.astc"
},
jumpId = 323,
activityName = "彩蛋七天乐",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "限时房间开放"
},
[30240] = {
id = 30240,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1738857599
},
showEndTime = {
seconds = 1738857599
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 1,
backgroundUrl = {
"xinchunxg_5.astc"
},
jumpId = 50112,
activityName = "峡谷六重礼",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "福利领不停"
},
[30241] = {
id = 30241,
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1738252799
},
showEndTime = {
seconds = 1738252799
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 2,
backgroundUrl = {
"chongfen_3.astc"
},
activityName = "峡谷占地盘",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078314
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送全新表情",
activityGroupList = v25
},
[30242] = {
id = 30242,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1738857599
},
showEndTime = {
seconds = 1738857599
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 99,
activityName = "峡谷六重礼",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "福利领不停"
},
[30243] = {
id = 30243,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1739721599
},
showEndTime = {
seconds = 1739721599
},
showBeginTime = {
seconds = 1739462400
}
},
labelId = 2,
backgroundUrl = {
"xiagusf_5.astc"
},
jumpId = 50112,
activityName = "浪漫乐园",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = "T_Common_Icon_activity_Small01"
},
[30244] = {
id = 30244,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1739721599
},
showEndTime = {
seconds = 1739721599
},
showBeginTime = {
seconds = 1739462400
}
},
labelId = 99,
activityName = "浪漫乐园",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = "T_Common_Icon_activity_Small01"
},
[30245] = {
id = 30245,
timeInfo = {
beginTime = {
seconds = 1738771200
},
endTime = {
seconds = 1740067199
},
showEndTime = {
seconds = 1740067199
},
showBeginTime = {
seconds = 1738771200
}
},
labelId = 1,
activityName = "峡谷闹元宵",
activityUIDetail = "UI_Arena_NationalDayReward",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078315,
1078316
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = "元宵首胜礼",
thumbnail = v24
},
[30247] = {
id = 30247,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1738252800
},
endTime = {
seconds = 1739116799
},
showEndTime = {
seconds = 1739116799
},
showBeginTime = {
seconds = 1738252800
}
},
labelId = 2,
backgroundUrl = {
"langrendongri.astc"
},
activityName = "冬日补给站",
activityParam = {
25,
3475
},
activityDesc = "免费领头像框和昵称框！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002068,
1002069,
1002070
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3475
},
activitySubName = "送全新头像框"
},
[30232] = {
id = 30232,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739375999
},
showEndTime = {
seconds = 1739375999
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 6,
backgroundUrl = {
"chunjiexfl.astc"
},
jumpId = 567,
activityName = "春节星福利",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "农场乐不停",
activityGroup = v20,
thumbnail = v23
},
[30251] = {
id = 30251,
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1738857599
},
showEndTime = {
seconds = 1738857599
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 7,
backgroundUrl = {
"DWxinchunchongci.astc"
},
activityName = "新春冲刺",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1068001,
1068002
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "双倍积分",
thumbnail = "T_Common_Icon_activity_Small01"
},
[30252] = {
id = 30252,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1738857599
},
showEndTime = {
seconds = 1738857599
},
showBeginTime = {
seconds = 1737043200
}
},
labelId = 99,
activityName = "新春冲刺",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = "T_Common_Icon_activity_Small01"
},
[30233] = {
id = 30233,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739375999
},
showEndTime = {
seconds = 1739375999
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 6,
backgroundUrl = {
"chunjiexfl.astc"
},
jumpId = 566,
activityName = "春节星福利",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "农场乐不停",
activityGroup = v20,
thumbnail = v23
},
[30234] = {
id = 30234,
activityType = "ATFarmFoodFestival",
timeInfo = {
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1741276799
},
showEndTime = {
seconds = 1741276799
},
showBeginTime = {
seconds = 1740067200
}
},
labelId = 5,
backgroundUrl = {
"nckuanghuanjie.astc"
},
lowVersion = "1.3.68.97",
activityName = "农场美食节",
activityUIDetail = "UI_Activity_FoodFestival_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1081005,
1081006,
1081007
},
showInCenter = true,
activityNameType = v14,
clientParams = {
"317223"
},
titleType = 0,
activitySubName = "农场月卡体验",
activityGroup = v20,
thumbnail = v23
},
[30235] = {
id = 30235,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1738857599
},
showEndTime = {
seconds = 1738857599
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 7,
activityName = "排位双重礼",
activityDesc = "打排位不掉星及额外加分！",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
titleType = 0
},
[30300] = {
id = 30300,
activityType = "ATMobaRandomVote",
timeInfo = {
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1740671999
},
showEndTime = {
seconds = 1740671999
},
showBeginTime = {
seconds = 1740067200
}
},
labelId = 2,
jumpId = 50112,
activityName = "峡谷彩蛋局",
activityUIDetail = "UI_Arena_RandomVoting",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078317
},
showInCenter = true,
activityNameType = "ANTMobaRandomVote",
titleType = 0,
activitySubName = "随机事件投票",
thumbnail = v24
},
[30236] = {
id = 30236,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1739376000
},
endTime = {
seconds = 1739750400
},
showEndTime = {
seconds = 1739750400
},
showBeginTime = {
seconds = 1739376000
}
},
labelId = 6,
backgroundUrl = {
"zzw0213.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20,
thumbnail = v23
},
[30237] = {
id = 30237,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740326399
},
showEndTime = {
seconds = 1740326399
},
showBeginTime = {
seconds = 1739462400
}
},
labelId = 3,
backgroundUrl = {
"tianyuanlianyu.astc"
},
activityName = "田园恋语",
activityParam = {
25,
317224
},
activityDesc = "免费领全新配饰！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002071,
1002072,
1002073
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
317224
},
activitySubName = "免费领配饰",
activityGroup = v20,
thumbnail = v23
},
[30239] = {
id = 30239,
activityType = "ATLotteryDraw",
timeInfo = {
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1741276799
},
showEndTime = {
seconds = 1741276799
},
showBeginTime = {
seconds = 1740067200
}
},
labelId = 1,
activityName = "陷阱狼来袭",
activityUIDetail = "UI_WerewolfTrap_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1001470,
1001471,
1001472
},
showInCenter = true,
activityNameType = "ANLotteryDraw",
titleType = 0,
activityBeginCleanCoin = {
3480
},
activitySubName = "免费领新身份",
activityGroupList = {
"AG_OnlyNR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30248] = {
id = 30248,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1740326399
},
showEndTime = {
seconds = 1740326399
},
showBeginTime = {
seconds = 1740067200
}
},
labelId = 6,
backgroundUrl = {
"zzw0220.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20,
thumbnail = v23
},
[30250] = {
id = 30250,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1740931199
},
showEndTime = {
seconds = 1740931199
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 6,
backgroundUrl = {
"zzw0227.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20,
thumbnail = v23
},
[30253] = {
id = 30253,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 1741535999
},
showEndTime = {
seconds = 1741535999
},
showBeginTime = {
seconds = 1740672000
}
},
labelId = 5,
backgroundUrl = {
"tychunriji.astc"
},
activityName = "田园春日季",
activityParam = {
24,
317225,
659216
},
activityDesc = "免费领取月卡体验&宠物装饰",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1022078,
1022079,
1022075,
1022076,
1022077
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = {
"奖励商城",
"兑换任务",
"兑换道具"
},
titleType = 0,
activityBeginCleanCoin = {
317225
},
activitySubName = "免费宠物装饰",
activityGroup = v20,
thumbnail = v23
},
[30261] = {
id = 30261,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1740585600
},
endTime = {
seconds = 1742486399
},
showEndTime = {
seconds = 1742486399
},
showBeginTime = {
seconds = 1740585600
}
},
labelId = 4,
backgroundUrl = {
"DWzhuangshi.astc"
},
activityName = "新装饰系统",
activityParam = {
24,
3485,
680024
},
activityDesc = "限时领取大王专属皮肤！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1068006,
1068003,
1068004,
1068005
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3485
},
activitySubName = "限时送皮肤",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30255] = {
id = 30255,
activityType = "ATDailyColorPaint",
timeInfo = {
beginTime = {
seconds = 1740326400
},
endTime = {
seconds = 1743955199
},
showEndTime = {
seconds = 1743955199
},
showBeginTime = {
seconds = 1740326400
}
},
labelId = 5,
backgroundUrl = {
"nnnewtuzi.astc"
},
activityName = "国潮涂色屋",
activityUIDetail = "UI_Activity_DailyDrawView_VictoryTeam",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1081011,
1081012
},
showInCenter = true,
activityNameType = "ANTDailyColorPaint",
titleType = 0,
activitySubName = "农场小屋家具",
activityGroup = v20,
thumbnail = v23
},
[30256] = {
id = 30256,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1741190400
},
endTime = {
seconds = 1741535999
},
showEndTime = {
seconds = 1741535999
},
showBeginTime = {
seconds = 1741190400
}
},
labelId = 6,
backgroundUrl = {
"zzw0306.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20,
thumbnail = v23
},
[30257] = {
id = 30257,
timeInfo = {
beginTime = {
seconds = 1741276800
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1741276800
}
},
labelId = 7,
backgroundUrl = {
"tyhzj.astc"
},
activityName = "花朝送福利",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1081013,
1081014
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "农场月卡体验",
activityGroup = v20,
thumbnail = v23
},
[30258] = {
id = 30258,
timeInfo = {
beginTime = {
seconds = 1741276800
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1741276800
}
},
labelId = 1,
backgroundUrl = {
"langrenchongcis10.astc"
},
activityName = "狼人冲刺周",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1002074,
1002075
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "迎超凡特攻季",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30306] = {
id = 30306,
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 2,
backgroundUrl = {
"dagengxin_6.astc"
},
activityName = "峡谷大更新",
activityParam = {
24,
3621,
780830
},
activityDesc = "游玩领好礼",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078411,
1078412
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = {
"奖励商城",
"兑换任务",
"兑换道具"
},
titleType = 0,
activityBeginCleanCoin = {
3621
},
activitySubName = "游玩送好礼",
activityGroup = v20,
activityGroupList = v25,
thumbnail = "T_Common_Icon_activity_Small01"
},
[30307] = {
id = 30307,
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
showEndTime = {
seconds = 1746115199
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 1,
backgroundUrl = {
"xinsaiji_5.astc"
},
activityName = "峡谷排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078409,
1078410
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "上分领程咬金",
activityGroupList = v25,
thumbnail = v24
},
[30310] = {
id = 30310,
activityType = "ATMoboChallenge",
timeInfo = {
beginTime = {
seconds = 1750348800
},
endTime = {
seconds = 1752422399
},
showEndTime = {
seconds = 1752422399
},
showBeginTime = {
seconds = 1750348800
}
},
labelId = 2,
jumpId = 50112,
activityName = "峡谷挑战",
activityParam = {
2500190,
2500191,
2500192
},
activityUIDetail = "UI_Arena_CanyonMonthlyChallenge_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078400,
1078401,
1078402,
1078403,
1078404,
1078405,
1078406,
1078407,
1078408
},
showInCenter = true,
activityNameType = "ANTMoboChallenge",
activityShopType = {
25
},
clientParams = {
"3"
},
titleType = 0,
activityBeginCleanCoin = {
3804
},
activitySubName = "送背饰送英雄",
currencyCfg = {
3804
},
activityGroupList = v25,
thumbnail = v24
},
[30311] = {
id = 30311,
activityType = "ATAmusementPark",
timeInfo = {
beginTime = {
seconds = 1709049600
},
endTime = {
seconds = 1766505599
},
showEndTime = {
seconds = 1766505599
},
showBeginTime = {
seconds = 1709049600
}
},
labelId = 2,
jumpId = 50112,
activityName = "玩法游乐园",
activityParam = {
300101,
300102,
300103
},
activityUIDetail = "UI_Activity_AmusementPark_MainView",
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = "ANTAmusementPark",
activityShopType = {
176
},
titleType = 0,
activityBeginCleanCoin = {
3622
},
activitySubName = "活动测试",
thumbnail = v24
},
[30260] = {
id = 30260,
activityType = "ATSuperLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1743350399
},
showEndTime = {
seconds = 1743350399
},
showBeginTime = {
seconds = 1742486400
}
},
labelId = 2,
backgroundUrl = {
"langrenbobaozhan.astc"
},
activityName = "狼人播报站",
activityParam = {
3489,
10
},
activityUIDetail = "UI_Activity_WerewolfBroadcast_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1001473,
1001474,
1001475
},
showInCenter = true,
activityNameType = "ANTSuperLinearRedeem",
titleType = 0,
activityBeginCleanCoin = {
3489
},
activitySubName = "领稀有配饰",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30262] = {
id = 30262,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1741795200
},
endTime = {
seconds = 1742140799
},
showEndTime = {
seconds = 1742140799
},
showBeginTime = {
seconds = 1741795200
}
},
labelId = 6,
backgroundUrl = {
"zzw0311.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20,
thumbnail = v23
},
[30254] = {
id = 30254,
activityType = "ATPuzzle",
timeInfo = {
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1740067200
}
},
labelId = 1,
activityName = "快乐撕贴纸",
activityParam = {
1
},
activityUIDetail = "UI_Activity_FarmPuzzle_MainView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1081008,
1081009
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "小二哈返场",
activityGroup = v20,
thumbnail = v23
},
[30263] = {
id = 30263,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742486399
},
showEndTime = {
seconds = 1742486399
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 2,
backgroundUrl = {
"langrendongri.astc"
},
activityName = "超凡特攻季",
activityParam = {
32,
3487
},
activityDesc = "免费领取身份卡！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1001476,
1001477
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
32
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3487
},
activitySubName = "免费领身份卡",
activityGroupList = {
"AG_NR3E"
}
},
[30264] = {
id = 30264,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1741924800
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1741924800
}
},
labelId = 1,
backgroundUrl = {
"zaijianxhh.astc"
},
activityName = "再遇小红狐",
activityParam = {
24,
317127,
659252
},
activityDesc = "免费领取月卡体验&稀有配饰",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1022083,
1022080,
1022081,
1022082
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
317127
},
activitySubName = "农场月卡体验",
activityGroup = v20,
thumbnail = v23
},
[30312] = {
id = 30312,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742140799
},
showEndTime = {
seconds = 1742140799
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 2,
backgroundUrl = {
"xiagusf_7.astc"
},
jumpId = 50112,
activityName = "周末开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30313] = {
id = 30313,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742140799
},
showEndTime = {
seconds = 1742140799
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30314] = {
id = 30314,
activityType = "ATFishingHallOfFame",
timeInfo = {
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744300799
},
showEndTime = {
seconds = 1744300799
},
showBeginTime = {
seconds = 1743091200
}
},
labelId = 1,
lowVersion = "1.3.26.92",
activityName = "鱼塘幸运星",
activityParam = {
11
},
activityDesc = "元梦之星第四届鱼塘幸运星活动，尽享鱼塘欢乐，好运与你同行！",
activityUIDetail = "UI_Activity_GoFishing_MainView",
isInBottom = 1,
activityRuleId = 225,
tagId = 14,
activityTaskGroup = {
1022084,
1022085,
1022086
},
showInCenter = true,
activityNameType = "ANTFishingHallOfFame",
isHideMainBackground = true,
clientParams = {
"659279"
},
titleType = 0,
activitySubName = "领取海量磷虾",
activityGroup = v20
},
[30265] = {
id = 30265,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1742400000
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1742400000
}
},
labelId = 6,
backgroundUrl = {
"zzw0320.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = v20,
thumbnail = v23
},
[30280] = {
id = 30280,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1741708800
},
endTime = {
seconds = 1744300799
},
showEndTime = {
seconds = 1744300799
},
showBeginTime = {
seconds = 1741708800
}
},
labelId = 4,
backgroundUrl = {
"Dwzhuanjing.astc"
},
activityName = "身份专精",
activityParam = {
25,
3491
},
activityDesc = "免费领专属皮肤！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1068007,
1068008,
1068009
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
25
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3491
},
activitySubName = "新系统送皮肤",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30315] = {
id = 30315,
activityType = "ATMOBANewHeroTrial",
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1743091199
},
showEndTime = {
seconds = 1743091199
},
showBeginTime = {
seconds = 1742486400
}
},
labelId = 2,
activityName = "甄姬试炼",
activityUIDetail = "UI_Arena_NewHeroTrain_Main",
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1078415,
1078416,
1078417
},
showInCenter = true,
activityNameType = "ANTMOBANewHeroTrial",
titleType = 0,
activitySubName = "送星运宝箱",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
12
}
}
},
activityGroupList = v25
},
[30316] = {
id = 30316,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1742486400
}
},
labelId = 2,
backgroundUrl = {
"xiagusf_7.astc"
},
jumpId = 50112,
activityName = "周末开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30317] = {
id = 30317,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1742745599
},
showEndTime = {
seconds = 1742745599
},
showBeginTime = {
seconds = 1742486400
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 14,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30266] = {
id = 30266,
activityType = "ATWolfLinearExchangeTwo",
timeInfo = {
beginTime = {
seconds = 1744819200
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1744819200
}
},
labelId = 7,
jumpId = 310,
lowVersion = "1.3.78.95",
activityName = "狼人新人礼",
activityParam = {
0,
0,
651231,
651235
},
activityDesc = "完成<WerewolfBridalgift>10</>次谁是狼人对局送自选身份！",
activityUIDetail = "UI_Activity_WerewolfBridalgift_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1071250,
1071251
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "免费自选身份",
hideWhenCompleted = 1,
openTypeConfigs = {
{
type = 1,
params = {
0
}
}
},
activityGroupList = {
"AG_OnlyNR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30267] = {
id = 30267,
activityType = "ATFarmAnswer",
timeInfo = {
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1745769599
},
showEndTime = {
seconds = 1745769599
},
showBeginTime = {
seconds = 1744905600
}
},
labelId = 5,
activityName = "农场知识星",
activityParam = {
24,
317128,
659278
},
activityUIDetail = "UI_Activity_Answer_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022090,
1022087,
1022088,
1022089
},
showInCenter = true,
activityNameType = v14,
clientParams = {
"答题挑战",
"农场任务"
},
titleType = 0,
activityBeginCleanCoin = {
317128
},
activitySubName = "免费宠物装饰",
activityGroup = v20,
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
13
}
}
},
thumbnail = v23
},
[30268] = {
id = 30268,
activityType = "ATWolfReturn",
timeInfo = {
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1744905600
}
},
labelId = 1,
lowVersion = "1.3.78.95",
activityName = "精灵谷集结",
activityUIDetail = "UI_Activity_WerewolfRecall_MainView",
isInBottom = 1,
activityRuleId = 350,
tagId = 1,
activityTaskGroup = {
1001480
},
showInCenter = true,
activityNameType = "ANTWolfReturn",
titleType = 0,
activitySubName = "招募好友有礼",
activityGroupList = {
"AG_OnlyNR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30269] = {
id = 30269,
timeInfo = {
beginTime = {
seconds = 1741276800
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1741276800
}
},
labelId = 1,
backgroundUrl = {
"langrenchongcis10.astc"
},
activityName = "精灵谷将至",
activityUIDetail = v10,
isInBottom = 1,
tagId = 14,
activityTaskGroup = {
1001478
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "迎超凡特攻季",
activityGroupList = {
"AG_OnlyNR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30318] = {
id = 30318,
activityType = "ATArenaWeeklyActivity",
timeInfo = {
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1743350399
},
showEndTime = {
seconds = 1743350399
},
showBeginTime = {
seconds = 1743091200
}
},
labelId = 4,
activityName = "周末开黑礼",
activityUIDetail = "UI_Arena_FridayActivity_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078418,
1078419
},
showInCenter = true,
activityNameType = "ANTArenaWeeklyActivity",
isHideMainBackground = true,
titleType = 0,
activitySubName = "送星运宝箱",
thumbnail = v24
},
[30319] = {
id = 30319,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1743350399
},
showEndTime = {
seconds = 1743350399
},
showBeginTime = {
seconds = 1743091200
}
},
labelId = 99,
backgroundUrl = {
"xiagusf_7.astc"
},
jumpId = 50112,
activityName = "周末开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30320] = {
id = 30320,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1743350399
},
showEndTime = {
seconds = 1743350399
},
showBeginTime = {
seconds = 1743091200
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30270] = {
id = 30270,
activityType = "ATLotteryDraw",
timeInfo = {
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744559999
},
showEndTime = {
seconds = 1744559999
},
showBeginTime = {
seconds = 1743696000
}
},
labelId = 3,
activityName = "田园绽芳华",
activityUIDetail = "UI_FarmQingMing_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022091,
1022092,
1022093,
1022094,
1022110
},
showInCenter = true,
activityNameType = "ANLotteryDraw",
titleType = 0,
activityBeginCleanCoin = {
317129
},
activitySubName = "免费联动绿装",
activityGroup = v20,
currencyCfg = {
317129
},
thumbnail = v23
},
[30271] = {
id = 30271,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1743004800
},
endTime = {
seconds = 1743292800
},
showEndTime = {
seconds = 1743292800
},
showBeginTime = {
seconds = 1743004800
}
},
labelId = 6,
backgroundUrl = {
"zzw0328.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30272] = {
id = 30272,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1743609600
},
endTime = {
seconds = 1743897600
},
showEndTime = {
seconds = 1743897600
},
showBeginTime = {
seconds = 1743609600
}
},
labelId = 6,
backgroundUrl = {
"zzw0404.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30273] = {
id = 30273,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1744214400
},
endTime = {
seconds = 1744502400
},
showEndTime = {
seconds = 1744502400
},
showBeginTime = {
seconds = 1744214400
}
},
labelId = 6,
backgroundUrl = {
"zzw0411.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30274] = {
id = 30274,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744905599
},
showEndTime = {
seconds = 1744905599
},
showBeginTime = {
seconds = 1743696000
}
},
labelId = 2,
backgroundUrl = {
"langrenjinglinggu2.astc"
},
activityName = "狼人春日行",
activityParam = {
32,
3496
},
activityDesc = "免费领专属头像！",
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001481,
1001482,
1001483
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
32
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3496
},
activitySubName = "领取精灵头像",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30321] = {
id = 30321,
activityType = "ATMOBANewHeroTrial",
timeInfo = {
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1744905599
},
showEndTime = {
seconds = 1744905599
},
showBeginTime = {
seconds = 1744300800
}
},
labelId = 5,
activityName = "刘禅试炼",
activityUIDetail = "UI_Arena_NewHeroTrain_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078420,
1078421,
1078422
},
showInCenter = true,
activityNameType = "ANTMOBANewHeroTrial",
titleType = 0,
activitySubName = "送星运宝箱",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
14
}
}
},
activityGroupList = v25
},
[30322] = {
id = 30322,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1743955199
},
showEndTime = {
seconds = 1743955199
},
showBeginTime = {
seconds = 1743696000
}
},
labelId = 99,
backgroundUrl = {
"xiagusf_5.astc"
},
jumpId = 50112,
activityName = "周末开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30323] = {
id = 30323,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1743955199
},
showEndTime = {
seconds = 1743955199
},
showBeginTime = {
seconds = 1743696000
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30324] = {
id = 30324,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1744559999
},
showEndTime = {
seconds = 1744559999
},
showBeginTime = {
seconds = 1744300800
}
},
labelId = 99,
backgroundUrl = {
"xiagusf_5.astc"
},
jumpId = 50112,
activityName = "周末开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30325] = {
id = 30325,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1744559999
},
showEndTime = {
seconds = 1744559999
},
showBeginTime = {
seconds = 1744300800
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30326] = {
id = 30326,
activityType = "ATDailyColorPaint",
timeInfo = {
beginTime = {
seconds = 1742832000
},
endTime = {
seconds = 1744127999
},
showEndTime = {
seconds = 1744127999
},
showBeginTime = {
seconds = 1742832000
}
},
labelId = 4,
backgroundUrl = {
"nnnewtuzi.astc"
},
activityName = "春日绘彩行",
activityUIDetail = "UI_Activity_SpringPaintingView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022096,
1022097
},
showInCenter = true,
activityNameType = "ANTDailyColorPaint",
titleType = 0,
activityBeginCleanCoin = {
3421
},
activitySubName = "农场小屋家具",
activityGroup = v20,
thumbnail = v23
},
[30333] = {
id = 30333,
activityType = "ATFeatureIntegrationTemplate",
timeInfo = {
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1746719999
},
showEndTime = {
seconds = 1746719999
},
showBeginTime = {
seconds = 1745510400
}
},
labelId = 2,
backgroundUrl = {
"wanfasaiji.astc"
},
activityName = "赛季冲刺周",
activityParam = {
20,
1001485,
651423
},
activityUIDetail = "UI_Activity_FeaturedGameplay",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001485,
1001486,
1001487
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "每日领保护券",
activityGroup = v20,
activityGroupList = {
"AG_Arena",
"AG_NR3E"
}
},
[30328] = {
id = 30328,
timeInfo = {
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1744559999
},
showEndTime = {
seconds = 1744559999
},
showBeginTime = {
seconds = 1743696000
}
},
labelId = 5,
activityName = "大王新模式",
activityUIDetail = v9,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022098,
1022099,
1022100,
1022101,
1022102,
1022103,
1022104,
1022105,
1022106,
1022107
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "三王排位开启",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
}
},
[30329] = {
id = 30329,
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 4081334399
},
showEndTime = {
seconds = 4081334399
},
showBeginTime = {
seconds = 1741881600
}
},
labelId = 1,
activityName = "峡谷英雄帖",
activityUIDetail = "UI_Arena_HerosRoad_Main",
isInBottom = 1,
tagId = 14,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "玩峡谷得英雄",
activityGroupList = v25,
codeName = "ShowHerosRoad",
thumbnail = v24
},
[30330] = {
id = 30330,
timeInfo = {
beginTime = {
seconds = 1743998400
},
endTime = {
seconds = 1745164799
},
showEndTime = {
seconds = 1745164799
},
showBeginTime = {
seconds = 1743998400
}
},
labelId = 5,
backgroundUrl = {
"nongchangpaidui.astc"
},
activityName = "农场星彩蛋",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
10221111
},
showInCenter = true,
activityNameType = "ANTSingleLogin",
titleType = 0,
activitySubName = "欢乐派对来袭",
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30331] = {
id = 30331,
timeInfo = {
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 4102415999
},
showEndTime = {
seconds = 4102415999
},
showBeginTime = {
seconds = 1735660800
}
},
labelId = 1,
backgroundUrl = {
"xinsaiji_4.astc"
},
activityName = "峡谷排位赛_小档",
activityDesc = "1",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 0,
tagId = 14,
activityTaskGroup = {
1082001,
1082003
},
activityNameType = "ANTSingleLogin",
titleType = 0,
activitySubName = "上分领取角色皮肤",
activityGroupList = {
"AG_OnlyArena"
}
},
[30332] = {
id = 30332,
timeInfo = {
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 4102415999
},
showEndTime = {
seconds = 4102415999
},
showBeginTime = {
seconds = 1735660800
}
},
labelId = 1,
backgroundUrl = {
"xinsaiji_4.astc"
},
activityName = "峡谷排位赛_大档",
activityDesc = "1",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 0,
tagId = 14,
activityTaskGroup = {
1082002,
1082004
},
activityNameType = "ANTSingleLogin",
titleType = 0,
activitySubName = "上分领取英雄皮肤",
activityGroupList = {
"AG_OnlyArena"
}
},
[30340] = {
id = 30340,
activityType = "ATAmusementPark",
timeInfo = {
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1745510399
},
showEndTime = {
seconds = 1745510399
},
showBeginTime = {
seconds = 1744905600
}
},
labelId = 2,
jumpId = 50112,
activityName = "玩法游乐园",
activityParam = {
830098,
219000
},
activityUIDetail = "UI_Activity_AmusementPark_MainView",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTAmusementPark",
activityShopType = {
176
},
titleType = 0,
activityBeginCleanCoin = {
3623
},
activitySubName = "游玩送好礼",
thumbnail = v24
},
[30341] = {
id = 30341,
timeInfo = {
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1745164799
},
showEndTime = {
seconds = 1745164799
},
showBeginTime = {
seconds = 1744905600
}
},
labelId = 10,
backgroundUrl = {
"xiagubi_6.astc"
},
activityName = "峡谷周末礼",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078424,
1078425
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "免费抽英雄",
activityGroupList = {
"AG_OnlyArena"
},
thumbnail = v24
},
[30342] = {
id = 30342,
activityType = "ATArenaWeeklyActivity",
timeInfo = {
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1745769599
},
showEndTime = {
seconds = 1745769599
},
showBeginTime = {
seconds = 1745510400
}
},
labelId = 4,
activityName = "周末开黑礼",
activityUIDetail = "UI_Arena_FridayActivity_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078426,
1078427
},
showInCenter = true,
activityNameType = "ANTArenaWeeklyActivity",
isHideMainBackground = true,
titleType = 0,
activitySubName = "送星运宝箱",
thumbnail = v24
},
[30343] = {
id = 30343,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1745769599
},
showEndTime = {
seconds = 1745769599
},
showBeginTime = {
seconds = 1745510400
}
},
labelId = 99,
backgroundUrl = {
"xiagusf_5.astc"
},
jumpId = 50112,
activityName = "周末开黑",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30344] = {
id = 30344,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1745769599
},
showEndTime = {
seconds = 1745769599
},
showBeginTime = {
seconds = 1745510400
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30345] = {
id = 30345,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1745424000
},
endTime = {
seconds = 1746460799
},
showEndTime = {
seconds = 1746460799
},
showBeginTime = {
seconds = 1745424000
}
},
labelId = 6,
backgroundUrl = {
"zzw0501.astc"
},
jumpId = 5100,
activityName = "增益大焕新",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "喜迎喵喵节",
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30346] = {
id = 30346,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1744819200
},
endTime = {
seconds = 1745164799
},
showEndTime = {
seconds = 1745164799
},
showBeginTime = {
seconds = 1744819200
}
},
labelId = 6,
backgroundUrl = {
"zzw0417.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30347] = {
id = 30347,
timeInfo = {
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1745510399
},
showEndTime = {
seconds = 1745510399
},
showBeginTime = {
seconds = 1744905600
}
},
labelId = 3,
backgroundUrl = {
"xiagubi_7.astc"
},
activityName = "3v3大更新",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078428,
1078429
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送表情送动作",
activityGroupList = v25,
thumbnail = v24
},
[30348] = {
id = 30348,
activityType = "ATTravelingDog",
timeInfo = {
beginTime = {
seconds = 1744732800
},
endTime = {
seconds = 1745769599
},
showEndTime = {
seconds = 1745769599
},
showBeginTime = {
seconds = 1744732800
}
},
labelId = 3,
activityName = "旅行小狗",
activityParam = {
514
},
activityUIDetail = "UI_Activity_TravelDog_MainView",
isInBottom = 1,
activityRuleId = 292,
tagId = 1,
activityTaskGroup = {
1022112,
1022113,
1022114,
1022116,
1022115
},
showInCenter = true,
activityNameType = v14,
isHideMainBackground = true,
activityShopType = {
192
},
titleType = 0,
activityBeginCleanCoin = {
317130
},
activitySubName = "农场风景体验",
activityGroup = v20,
currencyCfg = {
317130
},
thumbnail = v23
},
[30350] = {
id = 30350,
activityType = "ATQingShuangTrial",
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1774627199
},
showEndTime = {
seconds = 1774627199
},
showBeginTime = {
seconds = 1742486400
}
},
labelId = 2,
backgroundUrl = {
"qingshuang.astc"
},
activityName = "晴霜试炼",
activityUIDetail = "UI_Arena_QingShuangTrain_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078453,
1078455,
1078450,
1078451,
1078452
},
showInCenter = true,
activityNameType = "ANTQingShuangTrial",
clientParams = {
"3080",
"3081"
},
titleType = 0,
activitySubName = "送英雄送背饰",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
15
}
}
},
activityGroupList = v25,
videos = {
3505
},
videoColorTemplate = 1,
activityTabSplitGroup = {
2,
3
}
},
[30351] = {
id = 30351,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1745942400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1745942400
}
},
labelId = 1,
activityName = "元梦喵喵节",
activityUIDetail = "UI_Activity_FridayCollection2_View",
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = "ANTFridayCollection",
titleType = 0,
activitySubName = "喵小萌治愈领",
activityGroup = v20,
currencyCfg = {
3544
}
},
[30352] = {
id = 30352,
timeInfo = {
beginTime = {
seconds = 1746633600
},
endTime = {
seconds = 1747929599
},
showEndTime = {
seconds = 1747929599
},
showBeginTime = {
seconds = 1746633600
}
},
labelId = 2,
activityName = "复仇大作战",
activityDesc = "1",
activityUIDetail = "UI_Activity_WerewolfRevenge_MainView",
isInBottom = 0,
tagId = 1,
activityTaskGroup = {
1001490,
1001491
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = "新身份免费送",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30353] = {
id = 30353,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1746115199
},
showEndTime = {
seconds = 1746115199
},
showBeginTime = {
seconds = 1745510400
}
},
labelId = 99,
activityName = "大王开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30701] = {
id = 30701,
activityType = "ATCOCEightDays",
activityName = "八日签到",
activityTaskGroup = {
1077001
},
activityNameType = "ANTCOCEightDays",
activitySubName = "可得好礼"
},
[30400] = {
id = 30400,
activityType = "ATTreasureLevelUp",
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1774627199
},
showEndTime = {
seconds = 1774627199
},
showBeginTime = {
seconds = 1742486400
}
},
labelId = 2,
activityName = "宝箱升级",
activityUIDetail = "UI_Arena_ChestUpgrade_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078454
},
showInCenter = true,
activityNameType = "ANTTreasureLevelUp",
titleType = 0,
activitySubName = "活动测试",
activityGroupList = v25
},
[30354] = {
id = 30354,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
backgroundUrl = {
"DWpaiweisai2.astc"
},
activityName = "大王排位赛",
activityUIDetail = v10,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022117,
1022118
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "领拉弥娅套装",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
}
},
[30355] = {
id = 30355,
activityType = "ATRestaurantThemed",
timeInfo = {
beginTime = {
seconds = 1745164800
},
endTime = {
seconds = 1748534399
},
showEndTime = {
seconds = 1748534399
},
showBeginTime = {
seconds = 1745164800
}
},
labelId = 3,
activityName = "餐厅星开业",
activityParam = {
1022126
},
activityUIDetail = "UI_Activity_FarmRestaurant_Mainview",
isInBottom = 1,
activityRuleId = 362,
tagId = 1,
activityTaskGroup = {
1022127,
1022128,
1022126,
1022119,
1022120,
1022121
},
showInCenter = true,
activityNameType = v14,
isHideMainBackground = true,
titleType = 0,
activityBeginCleanCoin = {
317131,
317132,
317133,
317134,
317135,
317136,
317137,
317138,
317139,
317140,
317141
},
activitySubName = "免费农场装饰",
activityGroup = v20,
currencyCfg = {
317141
},
childViews = {
"SimpleTaskItemView_Detail:UI_Activity_FarmRestaurant_MenuInformation"
}
},
[30359] = {
id = 30359,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 5,
backgroundUrl = {
"dawangsanwangpw.astc"
},
activityName = "三王排位",
activityUIDetail = v9,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022122
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "限时开启",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
}
},
[30357] = {
id = 30357,
activityType = v1,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 6,
backgroundUrl = {
"zzw0509.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm"
},
[30358] = {
id = 30358,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 4,
backgroundUrl = {
"tianyuanhsg.astc"
},
activityName = "田园好食光",
activityUIDetail = v10,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022123,
1022124,
1022125
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = v19,
activityGroup = v20
},
[30360] = {
id = 30360,
activityType = v0,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30361] = {
id = 30361,
activityType = v1,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 99,
backgroundUrl = {
"qingshuang1_5.astc"
},
jumpId = 10690,
activityName = "峡谷排位赛",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送非凡时装",
activityGroupList = {
"AG_OnlyArena"
},
thumbnail = v24
},
[30362] = {
id = 30362,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1747324799
},
showEndTime = {
seconds = 1747324799
},
showBeginTime = {
seconds = **********
}
},
labelId = 3,
backgroundUrl = {
"xiagubi_7.astc"
},
activityName = "峡谷礼遇",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078456
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "抽588峡谷币",
activityGroupList = {
"AG_OnlyArena"
},
thumbnail = v24
},
[30363] = {
id = 30363,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1746460799
},
showEndTime = {
seconds = 1746460799
},
showBeginTime = {
seconds = 1746028800
}
},
labelId = 4,
backgroundUrl = {
"caidanju250501.astc"
},
jumpId = 323,
activityName = "彩蛋五天乐",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "限时房间开放",
activityGroupList = {
"AG_OnlyNR3E"
}
},
[30364] = {
id = 30364,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1746633599
},
showEndTime = {
seconds = 1746633599
},
showBeginTime = {
seconds = 1746028800
}
},
labelId = 2,
backgroundUrl = {
"langrenwufuli.astc"
},
activityName = "狼人五重礼",
activityParam = {
3545
},
activityDesc = "身份体验礼包首发，更有身份卡等你拿~",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001495,
1001492,
1001493,
1001494,
1001496
},
showInCenter = true,
activityNameType = v15,
clientParams = {
"24",
"3545",
"651466"
},
titleType = 0,
activityBeginCleanCoin = {
24
},
activitySubName = "身份卡大派送",
animAssets = v21,
activityGroupList = {
"AG_NR3E"
}
},
[30365] = {
id = 30365,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1748188799
},
showEndTime = {
seconds = 1748188799
},
showBeginTime = {
seconds = 1747065600
}
},
labelId = 4,
backgroundUrl = {
"zzw0509.astc"
},
activityName = "餐厅社区礼",
activityUIDetail = v12,
isInBottom = 1,
tagId = 4,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "社区惊喜活动",
activityGroup = v20
},
[30366] = {
id = 30366,
activityType = "ATWolfTeamChest",
timeInfo = {
beginTime = {
seconds = 1745337600
},
endTime = {
seconds = 1747756799
},
showEndTime = {
seconds = 1747756799
},
showBeginTime = {
seconds = 1745337600
}
},
labelId = 2,
activityName = "组队开宝箱",
activityParam = {
329937
},
activityUIDetail = "UI_Activity_WerewolfOpenTreasure_Main",
isInBottom = 1,
activityRuleId = 400,
tagId = 1,
activityTaskGroup = {
1001505
},
showInCenter = true,
activityNameType = "ANTArenaRedPacketActivity",
isHideMainBackground = true,
titleType = 1,
activitySubName = "活动测试",
moduleConfigs = {
{
type = "AMT_Lottery",
params = {
30366
}
}
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30367] = {
id = 30367,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1745769600
},
endTime = {
seconds = 1748793599
},
showEndTime = {
seconds = 1748793599
},
showBeginTime = {
seconds = 1745769600
}
},
labelId = 7,
backgroundUrl = {
"0428paidui.astc"
},
jumpId = 5102,
activityName = "派对再来袭",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "派对限时返场",
activityGroup = "AG_OnlyFarm",
thumbnail = v23
},
[30370] = {
id = 30370,
activityType = "ATQingShuangTrial",
timeInfo = {
beginTime = {
seconds = 1743523200
},
endTime = {
seconds = 1778515199
},
showEndTime = {
seconds = 1778515199
},
showBeginTime = {
seconds = 1743523200
}
},
labelId = 2,
backgroundUrl = {
"qingshuang.astc"
},
activityName = "绮的试炼",
activityUIDetail = "UI_Arena_QiPaintingPractice_Main",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078460,
1078461,
1078457,
1078458,
1078459
},
showInCenter = true,
activityNameType = "ANTQingShuangTrial",
clientParams = {
"3080",
"3081"
},
titleType = 0,
activitySubName = "送英雄送背饰",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
15
}
}
},
activityGroupList = v25,
videos = {
3626
},
videoColorTemplate = 1,
activityTabSplitGroup = {
2,
3
}
},
[30368] = {
id = 30368,
activityType = "ATPuzzle",
timeInfo = {
beginTime = {
seconds = 1745683200
},
endTime = {
seconds = 1748534399
},
showEndTime = {
seconds = 1748534399
},
showBeginTime = {
seconds = 1745683200
}
},
labelId = 3,
activityName = "快乐撕贴纸",
activityParam = {
2
},
activityUIDetail = "UI_Activity_FarmPuzzle_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022129,
1022130
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activityBeginCleanCoin = {
317142
},
activitySubName = "免费宠物装饰",
activityGroup = v20,
thumbnail = v23
},
[30371] = {
id = 30371,
activityType = "ATFarmDailyAward",
timeInfo = {
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1767110399
},
showEndTime = {
seconds = 1767110399
},
showBeginTime = {
seconds = 1748534400
}
},
labelId = 3,
backgroundUrl = {
"zzw1129.astc"
},
activityName = "农场天天领活动",
activityParam = {
11,
5,
240,
6,
0,
3510,
12,
24,
200814,
102041
},
activityUIDetail = " UI_MallFarmLogin_MainView",
isInBottom = 1,
activityRuleId = 363,
tagId = 4,
activityTaskGroup = {
1005301
},
activityNameType = "ANTFarmDailyAward",
activityShopType = {
167
},
titleType = 0,
activitySubName = "农场天天领活动",
activityGroup = v20,
relatedModeId = 30371,
thumbnail = v23
},
[30369] = {
id = 30369,
activityType = "ATFarmDragonBoatFestival",
timeInfo = {
beginTime = {
seconds = 1747238400
},
endTime = {
seconds = 1749398399
},
showEndTime = {
seconds = 1749398399
},
showBeginTime = {
seconds = 1747238400
}
},
labelId = 4,
activityName = "农场小队",
activityParam = {
1022131,
1022132
},
activityUIDetail = "UI_Activity_DragonBoatFestival_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022131,
1022132,
1022134
},
showInCenter = true,
activityNameType = v14,
activityShopType = {
24
},
titleType = 0,
activityBeginCleanCoin = {
317143
},
activitySubName = "领家具和配饰",
activityGroup = v20,
thumbnail = v23
},
[30372] = {
id = 30372,
timeInfo = {
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1748534399
},
showEndTime = {
seconds = 1748534399
},
showBeginTime = {
seconds = 1747929600
}
},
labelId = 2,
backgroundUrl = {
"langrenkuanghuan0509.astc"
},
activityName = "狼人狂欢节",
activityUIDetail = v10,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001497,
1001498
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "开局送福利券",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30373] = {
id = 30373,
activityType = v1,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 6,
backgroundUrl = {
"zzw0509.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm"
},
[30374] = {
id = 30374,
activityType = v1,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = **********
}
},
labelId = 6,
backgroundUrl = {
"zzw0509.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm"
},
[30377] = {
id = 30377,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1749139199
},
showEndTime = {
seconds = 1749139199
},
showBeginTime = {
seconds = 1747324800
}
},
labelId = 5,
backgroundUrl = {
"DWjufenglaixi.astc"
},
activityName = "飓风来袭",
activityParam = {
24,
3777,
651615
},
activityDesc = "限时领取大王专属皮肤！",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1023004,
1023001,
1023002,
1023003
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3777
},
activitySubName = "限时送皮肤",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30378] = {
id = 30378,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1746633600
},
endTime = {
seconds = 1749139199
},
showEndTime = {
seconds = 1749139199
},
showBeginTime = {
seconds = 1746633600
}
},
labelId = 2,
activityName = "观赛有好礼",
activityUIDetail = "UI_WerewolfWatchEvents_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001500,
1001501,
1001502,
1001503
},
showInCenter = true,
activityNameType = v15,
titleType = 0,
activitySubName = "开局送福利券",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30379] = {
id = 30379,
timeInfo = {
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1748534399
},
showEndTime = {
seconds = 1748534399
},
showBeginTime = {
seconds = 1747929600
}
},
labelId = 3,
backgroundUrl = {
"xiagubi_8.astc"
},
activityName = "峡谷礼遇",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078462
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "抽588峡谷币",
activityGroupList = {
"AG_OnlyArena"
},
thumbnail = v24
},
[30380] = {
id = 30380,
timeInfo = {
beginTime = {
seconds = **********
},
endTime = {
seconds = 1747843200
},
showEndTime = {
seconds = 1747843200
},
showBeginTime = {
seconds = **********
}
},
labelId = 5,
backgroundUrl = {
"dawangsanwangpw.astc"
},
activityName = "条件测试",
activityUIDetail = v9,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078463
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "限时开启",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
}
},
[30381] = {
id = 30381,
activityType = "ATFishingHallOfFame",
timeInfo = {
beginTime = {
seconds = 1747238400
},
endTime = {
seconds = 1750348799
},
showEndTime = {
seconds = 1750348799
},
showBeginTime = {
seconds = 1747238400
}
},
labelId = 4,
lowVersion = "1.3.26.92",
activityName = "鱼塘幸运星",
activityParam = {
11
},
activityDesc = "元梦之星第五届鱼塘幸运星活动，尽享鱼塘欢乐，好运与你同行！",
activityUIDetail = "UI_Activity_GoFishing_MainView",
isInBottom = 1,
activityRuleId = 225,
tagId = 1,
activityTaskGroup = {
1022135,
1022136,
1022137
},
showInCenter = true,
activityNameType = "ANTFishingHallOfFame",
isHideMainBackground = true,
clientParams = {
"659392"
},
titleType = 0,
activitySubName = "领取海量磷虾",
activityGroup = v20,
thumbnail = v23
},
[30382] = {
id = 30382,
activityType = "ATLevelUpChallenge",
timeInfo = {
beginTime = {
seconds = 1737302400
},
endTime = {
seconds = 1767196799
},
showEndTime = {
seconds = 1767196799
},
showBeginTime = {
seconds = 1737302400
}
},
labelId = 1,
jumpId = 5100,
activityName = "冲级大挑战",
activityUIDetail = "UI_Activity_ReturnFarm2_MainView",
isInBottom = 1,
tagId = 15,
showInCenter = true,
activityNameType = v15,
titleType = 0,
activitySubName = "萌新专属增益",
activityGroup = v20
},
[30383] = {
id = 30383,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1750003199
},
showEndTime = {
seconds = 1750003199
},
showBeginTime = {
seconds = 1748534400
}
},
labelId = 6,
backgroundUrl = {
"zzw0509.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "好友赠礼暴击",
activityGroup = "AG_OnlyFarm"
},
[30384] = {
id = 30384,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1745942400
},
endTime = {
seconds = 1750003199
},
showEndTime = {
seconds = 1750003199
},
showBeginTime = {
seconds = 1745942400
}
},
labelId = 2,
backgroundUrl = {
"chaliliandong1.astc"
},
activityName = "送非凡时装",
activityParam = {
24,
3803
},
activityUIDetail = "UI_Activity_Exchange_KingView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078465,
1078466,
1078467
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
24
},
clientParams = v21,
titleType = 0,
activityBeginCleanCoin = {
3803
},
activitySubName = "茶理联动上线",
activityGroupList = v25
},
[30393] = {
id = 30393,
activityType = "ATFarmAnswer",
timeInfo = {
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1750003199
},
showEndTime = {
seconds = 1750003199
},
showBeginTime = {
seconds = 1748534400
}
},
labelId = 5,
activityName = "永夜城竞答",
activityParam = {
24,
3788,
651650
},
activityUIDetail = "UI_Activity_FarmPass_Reward",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1023014,
1023011,
1023012,
1023013
},
showInCenter = true,
activityNameType = v14,
activityShopType = {
24
},
clientParams = {
"答题挑战",
"永夜任务"
},
titleType = 0,
activityBeginCleanCoin = {
3788
},
activitySubName = "免费送摄魂罐",
activityGroup = "AG_Chase",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
17
}
}
},
activityGroupList = {
"AG_Chase"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30401] = {
id = 30401,
timeInfo = {
beginTime = {
seconds = 1748880000
},
endTime = {
seconds = 1782316799
},
showEndTime = {
seconds = 1782316799
},
showBeginTime = {
seconds = 1748880000
}
},
labelId = 99,
backgroundUrl = {
"xiagusf_5.astc"
},
jumpId = 50112,
activityName = "对战掉落",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078900
},
showInCenter = true,
activityNameType = "ANTMoBaBattleReward",
titleType = 0,
activitySubName = "活动测试"
},
[30402] = {
id = 30402,
activityType = "ATSummerVacationBP",
timeInfo = {
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1756396799
},
showEndTime = {
seconds = 1756396799
},
showBeginTime = {
seconds = 1747929600
}
},
labelId = 5,
activityName = "航海计划",
activityUIDetail = "UI_Activity_FarmPass_Reward",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022138,
1022139,
1022143,
1022144,
1022145,
1022146,
1022147,
1022148,
1022149
},
showInCenter = true,
activityNameType = v14,
isHideMainBackground = true,
clientParams = {
"100",
"50"
},
titleType = 0,
activitySubName = "农场通行证",
activityGroup = v20,
thumbnail = v23
},
[30403] = {
id = 30403,
activityType = "ATFarmFoodFestival",
timeInfo = {
beginTime = {
seconds = 1748361600
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1748361600
}
},
labelId = 2,
backgroundUrl = {
"nckuanghuanjie.astc"
},
lowVersion = "1.3.68.97",
activityName = "农场美食节",
activityUIDetail = "UI_Activity_FoodFestival2_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022140,
1022141,
1022142
},
showInCenter = true,
activityNameType = v14,
clientParams = {
"317151"
},
titleType = 0,
activityBeginCleanCoin = {
317151,
317152,
317153,
317154
},
activitySubName = "免费领取头像",
activityGroup = v20,
thumbnail = v23
},
[30394] = {
id = 30394,
timeInfo = {
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1749743999
},
showEndTime = {
seconds = 1749743999
},
showBeginTime = {
seconds = 1748534400
}
},
labelId = 5,
backgroundUrl = {
"DWSANWANGXIN.astc"
},
activityName = "三王排位",
activityUIDetail = v9,
isInBottom = 1,
tagId = 4,
activityTaskGroup = {
1023015
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "限时开启",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30399] = {
id = 30399,
timeInfo = {
beginTime = {
seconds = 1748707200
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1748707200
}
},
labelId = 5,
activityName = "大王福利周",
activityUIDetail = "UI_Activity_ChaseDailyReward_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1023016,
1023017
},
showInCenter = true,
activityNameType = v14,
titleType = 1,
activitySubName = "领红心火雷",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30404] = {
id = 30404,
activityType = "ATFarmDailyAward",
timeInfo = {
beginTime = {
seconds = 1747065600
},
endTime = {
seconds = 1767110399
},
showEndTime = {
seconds = 1767110399
},
showBeginTime = {
seconds = 1747065600
}
},
labelId = 3,
activityName = "moba天天领活动",
activityParam = {
11,
5,
240,
6,
0,
3640,
12,
24,
200828,
102043,
2,
1
},
activityUIDetail = "UI_DailyGift_MainView",
isInBottom = 1,
activityRuleId = 374,
tagId = 4,
activityTaskGroup = {
1005302
},
activityNameType = "ANTFarmDailyAward",
activityShopType = {
200
},
titleType = 0,
activitySubName = "moba天天领活动",
activityGroup = "AG_Arena",
relatedModeId = 30404,
thumbnail = v23
},
[30405] = {
id = 30405,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1748448000
},
endTime = {
seconds = 1748707199
},
showEndTime = {
seconds = 1748707199
},
showBeginTime = {
seconds = 1748448000
}
},
labelId = 2,
backgroundUrl = {
"langrencaidan0529.astc"
},
jumpId = 323,
activityName = "彩蛋三天乐",
activityUIDetail = v12,
isInBottom = 1,
tagId = 4,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = "限时房间开放",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30406] = {
id = 30406,
activityType = "ATFeatureIntegrationTemplate",
timeInfo = {
beginTime = {
seconds = 1750348800
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1750348800
}
},
labelId = 3,
backgroundUrl = {
"wanfasaiji.astc"
},
activityName = "赛季末冲刺",
activityParam = {
30,
1001506,
651495,
651496
},
activityUIDetail = "UI_Activity_FeaturedGameplay",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001506,
1001507,
1001508
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "每日领保护券",
activityGroup = v20,
activityGroupList = {
"AG_Arena",
"AG_NR3E"
}
},
[30407] = {
id = 30407,
timeInfo = {
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1750348799
},
showEndTime = {
seconds = 1750348799
},
showBeginTime = {
seconds = 1749744000
}
},
labelId = 3,
backgroundUrl = {
"chongci_8.astc"
},
activityName = "赛季末冲刺",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078930,
1078931
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送动作送背饰",
activityGroupList = v25,
thumbnail = v24
},
[30408] = {
id = 30408,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1750348800
},
endTime = {
seconds = 1750607999
},
showEndTime = {
seconds = 1750607999
},
showBeginTime = {
seconds = 1750348800
}
},
labelId = 6,
backgroundUrl = {
"zzw0620.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm"
},
[30409] = {
id = 30409,
activityType = "ATTreasureHunt",
timeInfo = {
beginTime = {
seconds = 1748966400
},
endTime = {
seconds = 1750003199
},
showEndTime = {
seconds = 1750003199
},
showBeginTime = {
seconds = 1748966400
}
},
labelId = 3,
activityName = "寻宝挑战",
activityParam = {
204,
3642
},
activityUIDetail = "UI_WerewolfTreasureHunt_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001509,
1001510,
1001511,
1001512
},
showInCenter = true,
activityNameType = "ANTTreasureHunt",
clientParams = {
"9570",
"651506"
},
titleType = 0,
activitySubName = "免费狼宝时装",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small09"
},
[30410] = {
id = 30410,
activityType = "ATSummerNavigationBar",
timeInfo = {
beginTime = {
seconds = 1745942400
},
endTime = {
seconds = **********
},
showEndTime = {
seconds = **********
},
showBeginTime = {
seconds = 1745942400
}
},
labelId = 1,
activityName = "暑期导航",
activityParam = {
201,
3641
},
activityUIDetail = "UI_Activity_SummerVacation_MainView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1001513,
1001514
},
showInCenter = true,
activityNameType = "ANTSummerNavigationBar",
activityShopType = {
201
},
titleType = 0,
activityBeginCleanCoin = {
3641
},
activitySubName = "喵小萌治愈领",
activityGroup = v20,
currencyCfg = {
3641
}
},
[30420] = {
id = 30420,
timeInfo = {
beginTime = {
seconds = 1749571200
},
endTime = {
seconds = 1755791999
},
showEndTime = {
seconds = 1755791999
},
showBeginTime = {
seconds = 1749571200
}
},
labelId = 3,
backgroundUrl = {
"DW13paiweisai.astc"
},
activityName = "大王排位赛",
activityUIDetail = v10,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1023018,
1023019
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "领银角套装",
activityGroup = "AG_Chase",
activityGroupList = {
"AG_Chase"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30421] = {
id = 30421,
timeInfo = {
beginTime = {
seconds = 1749571200
},
endTime = {
seconds = 1755791999
},
showEndTime = {
seconds = 1755791999
},
showBeginTime = {
seconds = 1749571200
}
},
labelId = 3,
backgroundUrl = {
"DW13paiweisai.astc"
},
activityName = "狼人排位赛",
activityUIDetail = v10,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1023020,
1023021
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "领稀有时装",
activityGroup = "AG_NR3E",
activityGroupList = {
"AG_NR3E"
},
thumbnail = "T_Common_Icon_activity_Small03"
},
[30422] = {
id = 30422,
activityType = v1,
timeInfo = {
beginTime = {
seconds = 1750867200
},
endTime = {
seconds = 1752422399
},
showEndTime = {
seconds = 1752422399
},
showBeginTime = {
seconds = 1750867200
}
},
labelId = 6,
backgroundUrl = {
"zzw0620.astc"
},
jumpId = 5100,
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
showInCenter = true,
activityNameType = v17,
titleType = 0,
activitySubName = v19,
activityGroup = "AG_OnlyFarm"
},
[30423] = {
id = 30423,
activityType = "ATLinearRedeem",
timeInfo = {
beginTime = {
seconds = 1750996800
},
endTime = {
seconds = 1751817599
},
showEndTime = {
seconds = 1751817599
},
showBeginTime = {
seconds = 1750996800
}
},
labelId = 5,
backgroundUrl = {
"tychunriji.astc"
},
activityName = "喵星人来袭",
activityParam = {
24,
317161,
659447
},
activityDesc = "免费领小橘子头像&精品磷虾",
activityUIDetail = "UI_Activity_Exchange_LinearView",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1022153,
1022150,
1022151,
1022152
},
showInCenter = true,
activityNameType = v15,
activityShopType = {
24
},
clientParams = {
"奖励商城",
"兑换任务",
"兑换道具"
},
titleType = 0,
activityBeginCleanCoin = {
317161
},
activitySubName = "农场星萌宠",
activityGroup = v20,
thumbnail = v23
},
[30425] = {
id = 30425,
timeInfo = {
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1752163199
},
showEndTime = {
seconds = 1752163199
},
showBeginTime = {
seconds = 1750953600
}
},
labelId = 2,
backgroundUrl = {
"diaoluo_5.astc"
},
jumpId = 50112,
activityName = "峡谷新赛季",
activityUIDetail = v12,
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078940
},
showInCenter = true,
activityNameType = "ANTMoBaBattleReward",
titleType = 0,
activitySubName = "领非凡时装",
activityGroupList = v25
},
[30426] = {
id = 30426,
activityType = v0,
timeInfo = {
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1751212799
},
showEndTime = {
seconds = 1751212799
},
showBeginTime = {
seconds = 1750953600
}
},
labelId = 99,
activityName = "周末开黑",
activityUIDetail = v11,
isInBottom = 1,
tagId = 1,
activityNameType = v16,
isHideMainBackground = true,
titleType = 0,
activitySubName = "组队上分翻倍",
thumbnail = v24
},
[30427] = {
id = 30427,
timeInfo = {
beginTime = {
seconds = 1751558400
},
endTime = {
seconds = 1752163199
},
showEndTime = {
seconds = 1752163199
},
showBeginTime = {
seconds = 1751558400
}
},
labelId = 3,
backgroundUrl = {
"swzj_8.astc"
},
activityName = "峡谷排位礼",
activityUIDetail = "UI_CommonTask_MulTaskView_Libai",
isInBottom = 1,
tagId = 1,
activityTaskGroup = {
1078941,
1078942
},
showInCenter = true,
activityNameType = v14,
titleType = 0,
activitySubName = "送英雄送动作",
activityGroupList = v25,
thumbnail = v24
}
}

local mt = {
activityType = "ATTask",
activityName = "农场新增益",
showInCenter = false,
isHideMainBackground = false,
slapFace = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data