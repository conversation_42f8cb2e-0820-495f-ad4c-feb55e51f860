--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/B_BP通行证_moba.xlsx: 任务配置

local v0 = 50108

local data = {
[30323] = {
id = 30323,
jumpId = 50108,
taskGroupId = 30035
},
[30324] = {
id = 30324,
name = "本赛季峡谷3v3或5v5段位达到青铜I",
desc = "本赛季峡谷3v3或5v5段位达到青铜I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
1,
5
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30035
},
[30325] = {
id = 30325,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30035
},
[30326] = {
id = 30326,
name = "第四周累计完成3个每周目标任务",
desc = "第四周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30323,
30324,
30325
}
}
}
}
}
}
},
taskGroupId = 30035
},
[30327] = {
id = 30327,
jumpId = 50108,
taskGroupId = 30036
},
[30328] = {
id = 30328,
name = "本赛季峡谷3v3或5v5段位达到白银I",
desc = "本赛季峡谷3v3或5v5段位达到白银I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
2,
5
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30036
},
[30329] = {
id = 30329,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30036
},
[30330] = {
id = 30330,
name = "第五周累计完成3个每周目标任务",
desc = "第五周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30327,
30328,
30329
}
}
}
}
}
}
},
taskGroupId = 30036
},
[30331] = {
id = 30331,
jumpId = 50108,
taskGroupId = 30037
},
[30332] = {
id = 30332,
name = "本赛季峡谷3v3或5v5段位达到黄金I",
desc = "本赛季峡谷3v3或5v5段位达到黄金I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
3,
5
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30037
},
[30333] = {
id = 30333,
name = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
desc = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 800,
value = 30000,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30037
},
[30334] = {
id = 30334,
name = "第六周累计完成3个每周目标任务",
desc = "第六周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30331,
30332,
30333
}
}
}
}
}
}
},
taskGroupId = 30037
},
[30335] = {
id = 30335,
jumpId = 50108,
taskGroupId = 30038
},
[30336] = {
id = 30336,
name = "本赛季峡谷3v3或5v5段位达到铂金III",
desc = "本赛季峡谷3v3或5v5段位达到铂金III",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
4,
3
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30038
},
[30337] = {
id = 30337,
name = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
desc = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 803,
value = 10,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30038
},
[30338] = {
id = 30338,
name = "第七周累计完成3个每周目标任务",
desc = "第七周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30335,
30336,
30337
}
}
}
}
}
}
},
taskGroupId = 30038
},
[30339] = {
id = 30339,
jumpId = 50108,
taskGroupId = 30039
},
[30340] = {
id = 30340,
name = "本赛季峡谷3v3或5v5段位达到铂金I",
desc = "本赛季峡谷3v3或5v5段位达到铂金I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
4,
5
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30039
},
[30341] = {
id = 30341,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30039
},
[30342] = {
id = 30342,
name = "第八周累计完成3个每周目标任务",
desc = "第八周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30339,
30340,
30341
}
}
}
}
}
}
},
taskGroupId = 30039
},
[30401] = {
id = 30401,
name = "参与峡谷任意模式(每分钟10经验，获胜额外加5经验，可重复完成)",
desc = "参与峡谷任意模式(每分钟10经验，获胜额外加5经验，可重复完成)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102,
6006,
5700
}
}
}
}
}
}
},
reward = {
itemIdList = {
1015,
1015
},
numList = {
15,
10
}
},
jumpId = 50108,
taskGroupId = 30040
},
[30411] = {
id = 30411,
jumpId = 50108,
taskGroupId = 30042
},
[30412] = {
id = 30412,
name = "在峡谷3v3或5v5任意模式中，助攻20次",
desc = "在峡谷3v3或5v5任意模式中，助攻20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 804,
value = 20,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30042
},
[30413] = {
id = 30413,
name = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
desc = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 800,
value = 30000,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30042
},
[30414] = {
id = 30414,
name = "第一周累计完成3个每周目标任务",
desc = "第一周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30411,
30412,
30413
}
}
}
}
}
}
},
taskGroupId = 30042
},
[30415] = {
id = 30415,
jumpId = 50108,
taskGroupId = 30043
},
[30416] = {
id = 30416,
name = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
desc = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 800,
value = 30000,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30043
},
[30417] = {
id = 30417,
name = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
desc = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 803,
value = 10,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30043
},
[30418] = {
id = 30418,
name = "第二周累计完成3个每周目标任务",
desc = "第二周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30415,
30416,
30417
}
}
}
}
}
}
},
taskGroupId = 30043
},
[30419] = {
id = 30419,
jumpId = 50108,
taskGroupId = 30044
},
[30420] = {
id = 30420,
name = "在峡谷3v3或5v5任意模式中，助攻20次",
desc = "在峡谷3v3或5v5任意模式中，助攻20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 804,
value = 20,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30044
},
[30421] = {
id = 30421,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30044
},
[30422] = {
id = 30422,
name = "第三周累计完成3个每周目标任务",
desc = "第三周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30419,
30420,
30421
}
}
}
}
}
}
},
taskGroupId = 30044
},
[30423] = {
id = 30423,
jumpId = 50108,
taskGroupId = 30045
},
[30424] = {
id = 30424,
name = "本赛季峡谷3v3或5v5段位达到青铜I",
desc = "本赛季峡谷3v3或5v5段位达到青铜I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
1,
5
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30045
},
[30425] = {
id = 30425,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30045
},
[30426] = {
id = 30426,
name = "第四周累计完成3个每周目标任务",
desc = "第四周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30423,
30424,
30425
}
}
}
}
}
}
},
taskGroupId = 30045
},
[30427] = {
id = 30427,
jumpId = 50108,
taskGroupId = 30046
},
[30428] = {
id = 30428,
name = "本赛季峡谷3v3或5v5段位达到白银I",
desc = "本赛季峡谷3v3或5v5段位达到白银I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
2,
5
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30046
},
[30429] = {
id = 30429,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30046
},
[30430] = {
id = 30430,
name = "第五周累计完成3个每周目标任务",
desc = "第五周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30427,
30428,
30429
}
}
}
}
}
}
},
taskGroupId = 30046
},
[30431] = {
id = 30431,
jumpId = 50108,
taskGroupId = 30047
},
[30432] = {
id = 30432,
name = "本赛季峡谷3v3或5v5段位达到黄金I",
desc = "本赛季峡谷3v3或5v5段位达到黄金I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
3,
5
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30047
},
[30433] = {
id = 30433,
name = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
desc = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 800,
value = 30000,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30047
},
[30434] = {
id = 30434,
name = "第六周累计完成3个每周目标任务",
desc = "第六周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30431,
30432,
30433
}
}
}
}
}
}
},
taskGroupId = 30047
},
[30435] = {
id = 30435,
jumpId = 50108,
taskGroupId = 30048
},
[30436] = {
id = 30436,
name = "本赛季峡谷3v3或5v5段位达到铂金III",
desc = "本赛季峡谷3v3或5v5段位达到铂金III",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
4,
3
}
}
}
}
}
}
},
jumpId = 50109,
taskGroupId = 30048
},
[30437] = {
id = 30437,
name = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
desc = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 803,
value = 10,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50108,
taskGroupId = 30048
},
[30438] = {
id = 30438,
name = "第七周累计完成3个每周目标任务",
desc = "第七周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30435,
30436,
30437
}
}
}
}
}
}
},
taskGroupId = 30048
},
[30501] = {
id = 30501,
name = "参与峡谷任意模式(每分钟10经验，获胜额外加5经验，可重复完成)",
desc = "参与峡谷任意模式(每分钟10经验，获胜额外加5经验，可重复完成)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102,
6006,
5700
}
}
}
}
}
}
},
reward = {
itemIdList = {
1015,
1015
},
numList = {
15,
10
}
},
jumpId = 50111,
taskGroupId = 30050
},
[30511] = {
id = 30511,
jumpId = 50111,
taskGroupId = 30052
},
[30512] = {
id = 30512,
name = "在峡谷3v3或5v5任意模式中，助攻20次",
desc = "在峡谷3v3或5v5任意模式中，助攻20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 804,
value = 20,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50111,
taskGroupId = 30052
},
[30513] = {
id = 30513,
name = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
desc = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 800,
value = 30000,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50111,
taskGroupId = 30052
},
[30514] = {
id = 30514,
name = "第一周累计完成3个每周目标任务",
desc = "第一周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30511,
30512,
30513
}
}
}
}
}
}
},
taskGroupId = 30052
},
[30515] = {
id = 30515,
jumpId = 50111,
taskGroupId = 30053
},
[30516] = {
id = 30516,
name = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
desc = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 800,
value = 30000,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50111,
taskGroupId = 30053
},
[30517] = {
id = 30517,
name = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
desc = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 803,
value = 10,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50111,
taskGroupId = 30053
},
[30518] = {
id = 30518,
name = "第二周累计完成3个每周目标任务",
desc = "第二周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30515,
30516,
30517
}
}
}
}
}
}
},
taskGroupId = 30053
},
[30519] = {
id = 30519,
jumpId = 50111,
taskGroupId = 30054
},
[30520] = {
id = 30520,
name = "在峡谷3v3或5v5任意模式中，助攻20次",
desc = "在峡谷3v3或5v5任意模式中，助攻20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 804,
value = 20,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50111,
taskGroupId = 30054
},
[30521] = {
id = 30521,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30054
},
[30522] = {
id = 30522,
name = "第三周累计完成3个每周目标任务",
desc = "第三周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30519,
30520,
30521
}
}
}
}
}
}
},
taskGroupId = 30054
},
[30523] = {
id = 30523,
jumpId = 50111,
taskGroupId = 30055
},
[30524] = {
id = 30524,
name = "本赛季峡谷3v3或5v5段位达到青铜I",
desc = "本赛季峡谷3v3或5v5段位达到青铜I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
1,
5
}
}
}
}
}
}
},
jumpId = 50112,
taskGroupId = 30055
},
[30525] = {
id = 30525,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30055
},
[30526] = {
id = 30526,
name = "第四周累计完成3个每周目标任务",
desc = "第四周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30523,
30524,
30525
}
}
}
}
}
}
},
taskGroupId = 30055
},
[30527] = {
id = 30527,
jumpId = 50111,
taskGroupId = 30056
},
[30528] = {
id = 30528,
name = "本赛季峡谷3v3或5v5段位达到白银I",
desc = "本赛季峡谷3v3或5v5段位达到白银I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
2,
5
}
}
}
}
}
}
},
jumpId = 50112,
taskGroupId = 30056
},
[30529] = {
id = 30529,
name = "参与3次峡谷占地盘模式",
desc = "参与3次峡谷占地盘模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 3,
subConditionList = {
{
type = 2,
value = {
5700
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30056
},
[30530] = {
id = 30530,
name = "第五周累计完成3个每周目标任务",
desc = "第五周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30527,
30528,
30529
}
}
}
}
}
}
},
taskGroupId = 30056
},
[30531] = {
id = 30531,
jumpId = 50111,
taskGroupId = 30057
},
[30532] = {
id = 30532,
name = "本赛季峡谷3v3或5v5段位达到黄金I",
desc = "本赛季峡谷3v3或5v5段位达到黄金I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
3,
5
}
}
}
}
}
}
},
jumpId = 50112,
taskGroupId = 30057
},
[30533] = {
id = 30533,
name = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
desc = "在峡谷3v3或5v5任意模式中，击倒10个敌人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 803,
value = 10,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50111,
taskGroupId = 30057
},
[30534] = {
id = 30534,
name = "第六周累计完成3个每周目标任务",
desc = "第六周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30531,
30532,
30533
}
}
}
}
}
}
},
taskGroupId = 30057
},
[30535] = {
id = 30535,
jumpId = 50111,
taskGroupId = 30058
},
[30536] = {
id = 30536,
name = "本赛季峡谷3v3或5v5段位达到铂金III",
desc = "本赛季峡谷3v3或5v5段位达到铂金III",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
4,
3
}
}
}
}
}
}
},
jumpId = 50112,
taskGroupId = 30058
},
[30537] = {
id = 30537,
name = "在峡谷3v3或5v5任意模式中，助攻20次",
desc = "在峡谷3v3或5v5任意模式中，助攻20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 804,
value = 20,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50111,
taskGroupId = 30058
},
[30538] = {
id = 30538,
name = "第七周累计完成3个每周目标任务",
desc = "第七周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30535,
30536,
30537
}
}
}
}
}
}
},
taskGroupId = 30058
},
[30539] = {
id = 30539,
jumpId = 50111,
taskGroupId = 30059
},
[30540] = {
id = 30540,
name = "本赛季峡谷3v3或5v5段位达到铂金I",
desc = "本赛季峡谷3v3或5v5段位达到铂金I",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
12,
14
}
},
{
type = 32,
value = {
4,
5
}
}
}
}
}
}
},
jumpId = 50112,
taskGroupId = 30059
},
[30541] = {
id = 30541,
name = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
desc = "在峡谷3v3或5v5任意模式中，造成30000点伤害",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 800,
value = 30000,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50113,
taskGroupId = 30059
},
[30542] = {
id = 30542,
name = "第八周累计完成3个每周目标任务",
desc = "第八周累计完成3个每周目标任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
30539,
30540,
30541
}
}
}
}
}
}
},
taskGroupId = 30059
}
}

local mt = {
name = "参与6次峡谷3v3或5v5任意模式",
desc = "参与6次峡谷3v3或5v5任意模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 6,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
1015
},
numList = {
125
}
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data