--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 头饰

local v0 = {
seconds = 4074768000
}

local data = {
[630601] = {
id = 630601,
effect = true,
name = "永恒银冠",
desc = "今夜月光泠然，适合狼人出没",
icon = "CDN:Icon_Halo_364",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_364",
modelType = 1
},
scaleTimes = 180,
shareTexts = {
"一顶银冠，是对我的奖励"
},
beginTime = v0,
suitId = 70335,
suitName = "永恒银冠",
suitIcon = "CDN:Icon_Halo_364",
shareOffset = {
-10,
10
}
},
[630602] = {
id = 630602,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "嗷呜金币",
desc = "赚点狼人币，还不是轻轻松松~",
icon = "CDN:Icon_Halo_367",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_367",
modelType = 1
},
scaleTimes = 230,
shareTexts = {
"我的狼人币分你一半~"
},
beginTime = v0,
suitId = 70336,
suitName = "嗷呜金币",
suitIcon = "CDN:Icon_Halo_367",
shareOffset = {
0,
0
}
},
[630603] = {
id = 630603,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "爆爆弹",
desc = "嘿嘿嘿，看我魔爆狼大显身手！",
icon = "CDN:Icon_Halo_368",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_368",
modelType = 1
},
scaleTimes = 230,
shareTexts = {
"什么？谁暴露了我的身份？"
},
beginTime = v0,
suitId = 70337,
suitName = "爆爆弹",
suitIcon = "CDN:Icon_Halo_368",
shareOffset = {
0,
0
}
},
[630604] = {
id = 630604,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "赛博天眼",
desc = "检测仪一扫，星宝别想跑~",
icon = "CDN:Icon_Halo_369",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_369",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70338,
suitName = "赛博天眼",
suitIcon = "CDN:Icon_Halo_369",
shareOffset = {
0,
0
}
},
[630605] = {
id = 630605,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "元梦喵喵节",
desc = "可爱喵喵的专属节日~",
icon = "CDN:Icon_Halo_372",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_372",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70339,
suitName = "元梦喵喵节",
suitIcon = "CDN:Icon_Halo_372",
shareOffset = {
0,
0
}
},
[630606] = {
id = 630606,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 100
}
},
quality = 1,
name = "粉兔颜料",
desc = "跃入色彩的世界，每一跳都是灵动创意",
icon = "CDN:Icon_Halo_374",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_374",
modelType = 2,
idleAnim = "AS_Halo_374_idle_001"
},
scaleTimes = 120,
shareTexts = {
"在画布上跳跃出绚丽彩虹"
},
beginTime = v0,
suitId = 70340,
suitName = "粉兔颜料",
suitIcon = "CDN:Icon_Halo_374",
shareOffset = {
-10,
30
},
previewShareOffset = {
0,
-40
}
},
[630607] = {
id = 630607,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "粉兔颜料",
desc = "跃入色彩的世界，每一跳都是灵动创意",
icon = "CDN:Icon_Halo_374_01",
outlookConf = {
belongTo = 630606,
fashionValue = 35,
belongToGroup = {
630607,
630608
}
},
resourceConf = {
model = "SK_Halo_374",
material = "MI_Halo_374_HP01;MI_Halo_374_HP01",
modelType = 2,
idleAnim = "AS_Halo_374_idle_001_HP01",
materialSlot = "Halo;Halo_Translucent"
},
commodityId = 61624,
scaleTimes = 120,
shareTexts = {
"在画布上跳跃出绚丽彩虹"
},
shareOffset = {
-10,
30
},
previewShareOffset = {
0,
-40
}
},
[630608] = {
id = 630608,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "粉兔颜料",
desc = "跃入色彩的世界，每一跳都是灵动创意",
icon = "CDN:Icon_Halo_374_02",
outlookConf = {
belongTo = 630606,
fashionValue = 35,
belongToGroup = {
630607,
630608
}
},
resourceConf = {
model = "SK_Halo_374",
material = "MI_Halo_374_HP02;MI_Halo_374_HP02",
modelType = 2,
idleAnim = "AS_Halo_374_idle_001_HP02",
materialSlot = "Halo;Halo_Translucent"
},
commodityId = 61625,
scaleTimes = 120,
shareTexts = {
"在画布上跳跃出绚丽彩虹"
},
shareOffset = {
-10,
30
},
previewShareOffset = {
0,
-40
}
},
[630609] = {
id = 630609,
effect = true,
name = "郁郁花香",
desc = "一点花香，满满幸福",
icon = "CDN:Icon_Halo_346",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_346",
modelType = 1
},
scaleTimes = 100,
shareTexts = {
"送你一朵小花花~"
},
beginTime = v0,
suitId = 70341,
suitName = "郁郁花香",
suitIcon = "CDN:Icon_Halo_346",
shareOffset = {
-10,
30
},
shareScaleTimes = 100
},
[630610] = {
id = 630610,
effect = true,
name = "郁郁花香",
desc = "一点花香，满满幸福",
icon = "CDN:Icon_Halo_346_01",
outlookConf = {
belongTo = 630609,
fashionValue = 25,
belongToGroup = {
630610,
630611
}
},
resourceConf = {
model = "SM_Halo_346",
material = "MI_Halo_346_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61627,
scaleTimes = 100,
shareTexts = {
"送你一朵小花花~"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 100
},
[630611] = {
id = 630611,
effect = true,
name = "郁郁花香",
desc = "一点花香，满满幸福",
icon = "CDN:Icon_Halo_346_02",
outlookConf = {
belongTo = 630609,
fashionValue = 25,
belongToGroup = {
630610,
630611
}
},
resourceConf = {
model = "SM_Halo_346",
material = "MI_Halo_346_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61628,
scaleTimes = 100,
shareTexts = {
"送你一朵小花花~"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 100
},
[630612] = {
id = 630612,
effect = true,
name = "樱樱清晨",
desc = "遮住骄阳，遮不住懵懂的心",
icon = "CDN:Icon_Halo_371",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_371",
modelType = 1
},
scaleTimes = 100,
shareTexts = {
"下个晴天，一起出游吧！"
},
beginTime = v0,
suitId = 70342,
suitName = "樱樱清晨",
suitIcon = "CDN:Icon_Halo_371",
shareOffset = {
-10,
30
},
shareScaleTimes = 100,
shareRotateYaw = 230,
previewShareRotateYaw = 230
},
[630613] = {
id = 630613,
effect = true,
name = "樱樱清晨",
desc = "遮住骄阳，遮不住懵懂的心",
icon = "CDN:Icon_Halo_371_01",
outlookConf = {
belongTo = 630612,
fashionValue = 25,
belongToGroup = {
630613,
630614
}
},
resourceConf = {
model = "SM_Halo_371",
material = "MI_Halo_371_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61630,
scaleTimes = 100,
shareTexts = {
"下个晴天，一起出游吧！"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 100,
shareRotateYaw = 230,
previewShareRotateYaw = 230
},
[630614] = {
id = 630614,
effect = true,
name = "樱樱清晨",
desc = "遮住骄阳，遮不住懵懂的心",
icon = "CDN:Icon_Halo_371_02",
outlookConf = {
belongTo = 630612,
fashionValue = 25,
belongToGroup = {
630613,
630614
}
},
resourceConf = {
model = "SM_Halo_371",
material = "MI_Halo_371_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61631,
scaleTimes = 100,
shareTexts = {
"下个晴天，一起出游吧！"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 100,
shareRotateYaw = 230,
previewShareRotateYaw = 230
},
[630615] = {
id = 630615,
effect = true,
name = "时尚中分",
desc = "为梦想努力的星宝，一定会闪闪发光",
icon = "CDN:Icon_Halo_388",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_388",
modelType = 1
},
scaleTimes = 100,
shareTexts = {
"你就是下一个舞台王者！"
},
beginTime = v0,
suitId = 70343,
suitName = "时尚中分",
suitIcon = "CDN:Icon_Halo_388",
shareOffset = {
-10,
0
},
shareScaleTimes = 100
},
[630616] = {
id = 630616,
effect = true,
name = "时尚中分",
desc = "为梦想努力的星宝，一定会闪闪发光",
icon = "CDN:Icon_Halo_388_01",
outlookConf = {
belongTo = 630615,
fashionValue = 25,
belongToGroup = {
630616,
630617
}
},
resourceConf = {
model = "SM_Halo_388",
material = "MI_Halo_388_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61633,
scaleTimes = 100,
shareTexts = {
"你就是下一个舞台王者！"
},
shareOffset = {
-10,
0
},
shareScaleTimes = 100
},
[630617] = {
id = 630617,
effect = true,
name = "时尚中分",
desc = "为梦想努力的星宝，一定会闪闪发光",
icon = "CDN:Icon_Halo_388_02",
outlookConf = {
belongTo = 630615,
fashionValue = 25,
belongToGroup = {
630616,
630617
}
},
resourceConf = {
model = "SM_Halo_388",
material = "MI_Halo_388_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61634,
scaleTimes = 100,
shareTexts = {
"你就是下一个舞台王者！"
},
shareOffset = {
-10,
0
},
shareScaleTimes = 100
},
[630618] = {
id = 630618,
effect = true,
name = "荒野诗篇",
desc = "在熄灭的篝火中，翻出半截原野的朝阳",
icon = "CDN:Icon_Halo_373",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_373",
modelType = 1
},
scaleTimes = 100,
shareTexts = {
"流浪的风织成花，别在身上"
},
beginTime = v0,
suitId = 70344,
suitName = "荒野诗篇",
suitIcon = "CDN:Icon_Halo_373",
shareOffset = {
-10,
30
},
shareScaleTimes = 100
},
[630619] = {
id = 630619,
effect = true,
name = "荒野诗篇",
desc = "在熄灭的篝火中，翻出半截原野的朝阳",
icon = "CDN:Icon_Halo_373_01",
outlookConf = {
belongTo = 630618,
fashionValue = 25,
belongToGroup = {
630619,
630620
}
},
resourceConf = {
model = "SM_Halo_373",
material = "MI_Halo_373_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61636,
scaleTimes = 100,
shareTexts = {
"流浪的风织成花，别在身上"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 100
},
[630620] = {
id = 630620,
effect = true,
name = "荒野诗篇",
desc = "在熄灭的篝火中，翻出半截原野的朝阳",
icon = "CDN:Icon_Halo_373_02",
outlookConf = {
belongTo = 630618,
fashionValue = 25,
belongToGroup = {
630619,
630620
}
},
resourceConf = {
model = "SM_Halo_373",
material = "MI_Halo_373_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61637,
scaleTimes = 100,
shareTexts = {
"流浪的风织成花，别在身上"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 100
},
[630621] = {
id = 630621,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "“粽”心如意",
desc = "吃个粽子，万事开心~",
icon = "CDN:Icon_Halo_359",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_359",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70345,
suitName = "“粽”心如意",
suitIcon = "CDN:Icon_Halo_359",
shareOffset = {
0,
0
}
},
[630622] = {
id = 630622,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "星宝蛋糕",
desc = "只要不说，就是零卡！",
icon = "CDN:Icon_Halo_366",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_366",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70346,
suitName = "星宝蛋糕",
suitIcon = "CDN:Icon_Halo_366",
shareOffset = {
0,
0
}
},
[630623] = {
id = 630623,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "大明星",
desc = "大明星驾到，签名请排队",
icon = "CDN:Icon_Halo_355",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_355",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70347,
suitName = "大明星",
suitIcon = "CDN:Icon_Halo_355",
shareOffset = {
0,
0
}
},
[630624] = {
id = 630624,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "黄金松果",
desc = "当黄金有了温度，松果也变得闪耀",
icon = "CDN:Icon_Halo_268",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_268",
modelType = 1
},
scaleTimes = 230,
shareTexts = {
"小小一颗，能量无限"
},
beginTime = v0,
suitId = 70348,
suitName = "黄金松果",
suitIcon = "CDN:Icon_Halo_268",
shareOffset = {
0,
0
}
},
[630625] = {
id = 630625,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "开心万岁",
desc = "不管几岁，开心万岁！",
icon = "CDN:Icon_Halo_360",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_360",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70349,
suitName = "开心万岁",
suitIcon = "CDN:Icon_Halo_360",
shareOffset = {
0,
0
}
},
[630626] = {
id = 630626,
effect = true,
name = "星河之果",
desc = "一口咬下星河，将宇宙的甜蜜私藏",
icon = "CDN:Icon_Halo_351",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_351",
modelType = 1
},
scaleTimes = 200,
shareTexts = {
"星光洒落，果实闪耀"
},
beginTime = v0,
suitId = 70350,
suitName = "星河之果",
suitIcon = "CDN:Icon_Halo_351",
shareOffset = {
-10,
35
}
},
[630627] = {
id = 630627,
effect = true,
name = "星河之果",
desc = "一口咬下星河，将宇宙的甜蜜私藏",
icon = "CDN:Icon_Halo_351_01",
outlookConf = {
belongTo = 630626,
fashionValue = 25,
belongToGroup = {
630627,
630628
}
},
resourceConf = {
model = "SM_Halo_351",
material = "MI_Halo_351_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61644,
scaleTimes = 200,
shareTexts = {
"星光洒落，果实闪耀"
},
shareOffset = {
-10,
35
}
},
[630628] = {
id = 630628,
effect = true,
name = "星河之果",
desc = "一口咬下星河，将宇宙的甜蜜私藏",
icon = "CDN:Icon_Halo_351_02",
outlookConf = {
belongTo = 630626,
fashionValue = 25,
belongToGroup = {
630627,
630628
}
},
resourceConf = {
model = "SM_Halo_351",
material = "MI_Halo_351_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61645,
scaleTimes = 200,
shareTexts = {
"星光洒落，果实闪耀"
},
shareOffset = {
-10,
35
}
},
[630629] = {
id = 630629,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 20
}
},
quality = 1,
name = "游鱼遗尾",
desc = "凤箫声动，一夜鱼龙舞",
icon = "CDN:Icon_Halo_370",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_370",
modelType = 2,
idleAnim = "AS_Halo_370_idle_001"
},
scaleTimes = 180,
shareTexts = {
"灯火阑珊，与你共赏"
},
beginTime = v0,
suitId = 70351,
suitName = "游鱼遗尾",
suitIcon = "CDN:Icon_Halo_370",
shareOffset = {
-5,
50
}
},
[630630] = {
id = 630630,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "游鱼遗尾",
desc = "凤箫声动，一夜鱼龙舞",
icon = "CDN:Icon_Halo_370_01",
outlookConf = {
belongTo = 630629,
fashionValue = 35,
belongToGroup = {
630630,
630631
}
},
resourceConf = {
model = "SK_Halo_370",
material = "MI_Halo_370_HP01",
modelType = 2,
idleAnim = "AS_Halo_370_idle_001_HP01",
materialSlot = "Halo"
},
commodityId = 61647,
scaleTimes = 180,
shareTexts = {
"灯火阑珊，与你共赏"
},
shareOffset = {
-5,
50
}
},
[630631] = {
id = 630631,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "游鱼遗尾",
desc = "凤箫声动，一夜鱼龙舞",
icon = "CDN:Icon_Halo_370_02",
outlookConf = {
belongTo = 630629,
fashionValue = 35,
belongToGroup = {
630630,
630631
}
},
resourceConf = {
model = "SK_Halo_370",
material = "MI_Halo_370_HP02",
modelType = 2,
idleAnim = "AS_Halo_370_idle_001_HP02",
materialSlot = "Halo"
},
commodityId = 61648,
scaleTimes = 180,
shareTexts = {
"灯火阑珊，与你共赏"
},
shareOffset = {
-5,
50
}
},
[630632] = {
id = 630632,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "雾隐重峦",
desc = "远山已现，日耀其辉",
icon = "CDN:Icon_Halo_378",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Halo_378",
emitter = "FX_CH_Decorate_Halo_378_001",
modelType = 1
},
scaleTimes = 120,
shareTexts = {
"山光风月，与你共赏"
},
beginTime = v0,
suitId = 70352,
suitName = "星冕之核",
suitIcon = "CDN:Icon_Halo_378"
},
[630633] = {
id = 630633,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "雾隐重峦",
desc = "远山已现，日耀其辉",
icon = "CDN:Icon_Halo_378_01",
outlookConf = {
belongTo = 630632,
fashionValue = 35,
belongToGroup = {
630633,
630634
}
},
resourceConf = {
model = "SM_Halo_378",
material = "MI_Halo_378_1_HP01;MI_Halo_378_2_HP01",
emitter = "FX_CH_Decorate_Halo_378_001_HP01",
modelType = 1,
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61650,
scaleTimes = 120,
shareTexts = {
"山光风月，与你共赏"
}
},
[630634] = {
id = 630634,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "雾隐重峦",
desc = "远山已现，日耀其辉",
icon = "CDN:Icon_Halo_378_02",
outlookConf = {
belongTo = 630632,
fashionValue = 35,
belongToGroup = {
630633,
630634
}
},
resourceConf = {
model = "SM_Halo_378",
material = "MI_Halo_378_1_HP02;MI_Halo_378_2_HP02",
emitter = "FX_CH_Decorate_Halo_378_001_HP02",
modelType = 1,
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61651,
scaleTimes = 120,
shareTexts = {
"山光风月，与你共赏"
}
},
[630635] = {
id = 630635,
effect = true,
name = "匠心萌动",
desc = "承载传承，也承载思念",
icon = "CDN:Icon_Halo_376",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_376",
modelType = 1
},
scaleTimes = 200,
shareTexts = {
"今天也要“装装”的！"
},
beginTime = v0,
suitId = 70353,
suitName = "星河之果",
suitIcon = "CDN:Icon_Halo_376"
},
[630636] = {
id = 630636,
effect = true,
name = "匠心萌动",
desc = "承载传承，也承载思念",
icon = "CDN:Icon_Halo_376_01",
outlookConf = {
belongTo = 630635,
fashionValue = 25,
belongToGroup = {
630636,
630637
}
},
resourceConf = {
model = "SM_Halo_376",
material = "MI_Halo_376_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61653,
scaleTimes = 200,
shareTexts = {
"今天也要“装装”的！"
}
},
[630637] = {
id = 630637,
effect = true,
name = "匠心萌动",
desc = "承载传承，也承载思念",
icon = "CDN:Icon_Halo_376_02",
outlookConf = {
belongTo = 630635,
fashionValue = 25,
belongToGroup = {
630636,
630637
}
},
resourceConf = {
model = "SM_Halo_376",
material = "MI_Halo_376_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61654,
scaleTimes = 200,
shareTexts = {
"今天也要“装装”的！"
}
},
[630638] = {
id = 630638,
effect = true,
name = "半熟章丸丸",
desc = "张嘴不是抱歉，而是实在美味！",
icon = "CDN:Icon_Halo_389",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_389",
modelType = 1
},
scaleTimes = 100,
shareTexts = {
"想做一只简单的丸丸"
},
beginTime = v0,
suitId = 70354,
suitName = "星河之果",
suitIcon = "CDN:Icon_Halo_389"
},
[630639] = {
id = 630639,
effect = true,
name = "半熟章丸丸",
desc = "张嘴不是抱歉，而是实在美味！",
icon = "CDN:Icon_Halo_389_01",
outlookConf = {
belongTo = 630638,
fashionValue = 25,
belongToGroup = {
630639,
630640
}
},
resourceConf = {
model = "SM_Halo_389",
material = "MI_Halo_389_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61656,
scaleTimes = 100,
shareTexts = {
"想做一只简单的丸丸"
}
},
[630640] = {
id = 630640,
effect = true,
name = "半熟章丸丸",
desc = "张嘴不是抱歉，而是实在美味！",
icon = "CDN:Icon_Halo_389_02",
outlookConf = {
belongTo = 630638,
fashionValue = 25,
belongToGroup = {
630639,
630640
}
},
resourceConf = {
model = "SM_Halo_389",
material = "MI_Halo_389_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61657,
scaleTimes = 100,
shareTexts = {
"想做一只简单的丸丸"
}
},
[630641] = {
id = 630641,
effect = true,
name = "萌喵之视",
desc = "猫咪不语，只是一味凝视",
icon = "CDN:Icon_Halo_395",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_395",
modelType = 1
},
scaleTimes = 100,
shareTexts = {
"你就是新来的两脚兽嘛？"
},
beginTime = v0,
suitId = 70355,
suitName = "星河之果",
suitIcon = "CDN:Icon_Halo_395"
},
[630642] = {
id = 630642,
effect = true,
name = "萌喵之视",
desc = "猫咪不语，只是一味凝视",
icon = "CDN:Icon_Halo_395_01",
outlookConf = {
belongTo = 630641,
fashionValue = 25,
belongToGroup = {
630642,
630643
}
},
resourceConf = {
model = "SM_Halo_395",
material = "MI_Halo_395_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61659,
scaleTimes = 100,
shareTexts = {
"你就是新来的两脚兽嘛？"
}
},
[630643] = {
id = 630643,
effect = true,
name = "萌喵之视",
desc = "猫咪不语，只是一味凝视",
icon = "CDN:Icon_Halo_395_02",
outlookConf = {
belongTo = 630641,
fashionValue = 25,
belongToGroup = {
630642,
630643
}
},
resourceConf = {
model = "SM_Halo_395",
material = "MI_Halo_395_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61660,
scaleTimes = 100,
shareTexts = {
"你就是新来的两脚兽嘛？"
}
},
[630644] = {
id = 630644,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "轮回箭头",
desc = "这场游戏，还在继续……",
icon = "CDN:Icon_Halo_396",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_396",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70356,
suitName = "轮回箭头",
suitIcon = "CDN:Icon_Halo_396",
shareOffset = {
0,
0
}
},
[630645] = {
id = 630645,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "香暖便当",
desc = "肚肚也能闻到便当的香味~",
icon = "CDN:Icon_Halo_397",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_397",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70357,
suitName = "香暖便当",
suitIcon = "CDN:Icon_Halo_397",
shareOffset = {
0,
0
}
},
[630646] = {
id = 630646,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "扬帆小船",
desc = "纸船也能征服星辰大海！",
icon = "CDN:Icon_Halo_398",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_398",
modelType = 1
},
scaleTimes = 230,
beginTime = v0,
suitId = 70358,
suitName = "扬帆小船",
suitIcon = "CDN:Icon_Halo_398",
shareOffset = {
0,
0
}
},
[630647] = {
id = 630647,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "头顶机灵",
desc = "来自【星衣妙想】创作者笋条的创意！才不是装饰品，是会思考的头顶小伙伴",
icon = "CDN:Icon_Halo_400",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_400",
modelType = 1
},
scaleTimes = 230,
shareTexts = {
"“啾啾”接收指令中"
},
beginTime = v0,
suitId = 70359,
suitName = "头顶机灵",
suitIcon = "CDN:Icon_Halo_400",
shareOffset = {
0,
0
}
},
[630650] = {
id = 630650,
effect = true,
name = "跃动喵喵",
desc = "在发间跳跃的灵动，是你的专属猫步",
icon = "CDN:Icon_Halo_289",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_289",
modelType = 1
},
scaleTimes = 180,
shareTexts = {
"喵喵舞步，萌到心里"
},
beginTime = v0,
suitId = 70362,
suitName = "跃动喵喵",
suitIcon = "CDN:Icon_Halo_289"
},
[630651] = {
id = 630651,
effect = true,
name = "跃动喵喵",
desc = "在发间跳跃的灵动，是你的专属猫步",
icon = "CDN:Icon_Halo_289_01",
outlookConf = {
belongTo = 630650,
fashionValue = 25,
belongToGroup = {
630651,
630652
}
},
resourceConf = {
model = "SM_Halo_289",
material = "MI_Halo_289_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61666,
scaleTimes = 180,
shareTexts = {
"喵喵舞步，萌到心里"
}
},
[630652] = {
id = 630652,
effect = true,
name = "跃动喵喵",
desc = "在发间跳跃的灵动，是你的专属猫步",
icon = "CDN:Icon_Halo_289_02",
outlookConf = {
belongTo = 630650,
fashionValue = 25,
belongToGroup = {
630651,
630652
}
},
resourceConf = {
model = "SM_Halo_289",
material = "MI_Halo_289_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61667,
scaleTimes = 180,
shareTexts = {
"喵喵舞步，萌到心里"
}
},
[630653] = {
id = 630653,
effect = true,
name = "乌云绵绵",
desc = "最美的风景，在每一个雨后",
icon = "CDN:Icon_Halo_348",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_348",
modelType = 1
},
scaleTimes = 180,
shareTexts = {
"彩虹总在风雨后"
},
beginTime = v0,
suitId = 70363,
suitName = "乌云绵绵",
suitIcon = "CDN:Icon_Halo_348"
},
[630654] = {
id = 630654,
effect = true,
name = "乌云绵绵",
desc = "最美的风景，在每一个雨后",
icon = "CDN:Icon_Halo_348_01",
outlookConf = {
belongTo = 630653,
fashionValue = 25,
belongToGroup = {
630654,
630655
}
},
resourceConf = {
model = "SM_Halo_348",
material = "MI_Halo_348_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61669,
scaleTimes = 180,
shareTexts = {
"彩虹总在风雨后"
}
},
[630655] = {
id = 630655,
effect = true,
name = "乌云绵绵",
desc = "最美的风景，在每一个雨后",
icon = "CDN:Icon_Halo_348_02",
outlookConf = {
belongTo = 630653,
fashionValue = 25,
belongToGroup = {
630654,
630655
}
},
resourceConf = {
model = "SM_Halo_348",
material = "MI_Halo_348_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61670,
scaleTimes = 180,
shareTexts = {
"彩虹总在风雨后"
}
},
[630656] = {
id = 630656,
effect = true,
name = "囧囧先生",
desc = "别看外表囧囧，内心活力满满",
icon = "CDN:Icon_Halo_381",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_381",
modelType = 1
},
scaleTimes = 180,
shareTexts = {
"囧只是我的保护色"
},
beginTime = v0,
suitId = 70364,
suitName = "囧囧先生",
suitIcon = "CDN:Icon_Halo_381"
},
[630657] = {
id = 630657,
effect = true,
name = "囧囧先生",
desc = "别看外表囧囧，内心活力满满",
icon = "CDN:Icon_Halo_381_01",
outlookConf = {
belongTo = 630656,
fashionValue = 25,
belongToGroup = {
630657,
630658
}
},
resourceConf = {
model = "SM_Halo_381",
material = "MI_Halo_381_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61672,
scaleTimes = 180,
shareTexts = {
"囧只是我的保护色"
}
},
[630658] = {
id = 630658,
effect = true,
name = "囧囧先生",
desc = "别看外表囧囧，内心活力满满",
icon = "CDN:Icon_Halo_381_02",
outlookConf = {
belongTo = 630656,
fashionValue = 25,
belongToGroup = {
630657,
630658
}
},
resourceConf = {
model = "SM_Halo_381",
material = "MI_Halo_381_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61673,
scaleTimes = 180,
shareTexts = {
"囧只是我的保护色"
}
},
[630659] = {
id = 630659,
effect = true,
name = "蛇小帕",
desc = "这可是时尚界的新潮流",
icon = "CDN:Icon_Halo_319",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_319",
modelType = 1
},
scaleTimes = 180,
shareTexts = {
"生活也要“巾”上添花"
},
beginTime = v0,
suitId = 70365,
suitName = "蛇小帕",
suitIcon = "CDN:Icon_Halo_319",
shareRotateYaw = 170
},
[630660] = {
id = 630660,
effect = true,
name = "蛇小帕",
desc = "这可是时尚界的新潮流",
icon = "CDN:Icon_Halo_319_01",
outlookConf = {
belongTo = 630659,
fashionValue = 25,
belongToGroup = {
630660,
630661
}
},
resourceConf = {
model = "SM_Halo_319",
material = "MI_Halo_319_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61675,
scaleTimes = 180,
shareTexts = {
"生活也要“巾”上添花"
},
shareRotateYaw = 170
},
[630661] = {
id = 630661,
effect = true,
name = "蛇小帕",
desc = "这可是时尚界的新潮流",
icon = "CDN:Icon_Halo_319_02",
outlookConf = {
belongTo = 630659,
fashionValue = 25,
belongToGroup = {
630660,
630661
}
},
resourceConf = {
model = "SM_Halo_319",
material = "MI_Halo_319_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61676,
scaleTimes = 180,
shareTexts = {
"生活也要“巾”上添花"
},
shareRotateYaw = 170
},
[630662] = {
id = 630662,
effect = true,
name = "心心蟹",
desc = "别害羞啦，把所有的爱意都比给你！",
icon = "CDN:Icon_Halo_380",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Halo_380",
modelType = 1
},
scaleTimes = 180,
shareTexts = {
"海风咸咸，蟹蟹甜甜"
},
beginTime = v0,
suitId = 70366,
suitName = "心心蟹",
suitIcon = "CDN:Icon_Halo_380"
},
[630663] = {
id = 630663,
effect = true,
name = "心心蟹",
desc = "别害羞啦，把所有的爱意都比给你！",
icon = "CDN:Icon_Halo_380_01",
outlookConf = {
belongTo = 630662,
fashionValue = 25,
belongToGroup = {
630663,
630664
}
},
resourceConf = {
model = "SM_Halo_380",
material = "MI_Halo_380_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61678,
scaleTimes = 180,
shareTexts = {
"海风咸咸，蟹蟹甜甜"
}
},
[630664] = {
id = 630664,
effect = true,
name = "心心蟹",
desc = "别害羞啦，把所有的爱意都比给你！",
icon = "CDN:Icon_Halo_380_02",
outlookConf = {
belongTo = 630662,
fashionValue = 25,
belongToGroup = {
630663,
630664
}
},
resourceConf = {
model = "SM_Halo_380",
material = "MI_Halo_380_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61679,
scaleTimes = 180,
shareTexts = {
"海风咸咸，蟹蟹甜甜"
}
},
[638001] = {
id = 638001,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "农场NPC厨师3",
useType = "IUTO_GiftPackage",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_391",
modelType = 1
},
scaleTimes = 230,
shareOffset = {
0,
0
}
},
[638002] = {
id = 638002,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "农场NPC厨师4",
useType = "IUTO_GiftPackage",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_392",
modelType = 1
},
scaleTimes = 230,
shareOffset = {
0,
0
}
},
[638003] = {
id = 638003,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "农场NPC厨师5",
useType = "IUTO_GiftPackage",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Halo_393",
modelType = 1
},
scaleTimes = 230,
shareOffset = {
0,
0
}
}
}

local mt = {
effect = false,
type = "ItemType_HeadWear",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
shareOffset = {
-8,
35
},
previewShareOffset = {
0,
0
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data