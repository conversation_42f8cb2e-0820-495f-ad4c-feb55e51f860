--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local v0 = {
{
itemId = 6,
itemNum = 400
}
}

local v1 = 2

local v2 = 10

local v3 = "AS_CH_Pose_Common_001"

local v4 = {
seconds = 4101552000
}

local data = {
[411180] = {
id = 411180,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "调香师 安娜苏",
desc = "如果你感到忧虑，不妨来调香室冥想",
icon = "CDN:Icon_PL_293",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_293",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_293_Physics"
},
outEnter = "AS_CH_Enter_PL_293",
outShow = "AS_CH_IdleShow_PL_293",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_293",
shareTexts = {
"拥抱花香，重拾勇气与希望"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[超能学园的药剂室外挂着一块木牌：若你有心事，请在花开时分来访。

安娜苏不喜欢把自己称作调香师。她更愿意自诩为“植物的倾听者”。她会在晨露未干时来到药剂室，轻声对每一株植物说早安。玻璃瓶里的精油会随着她的脚步轻轻晃动，折射出彩虹般的光晕。这是她一天中最宁静的时刻。

很少有星宝知道，曾经的安娜苏还只是个连火柴都点不着的实习生。直到遇见了那个总是躲在角落哭泣的小女孩。安娜苏用魔法从雏菊中萃取出了“微笑精油”，那是她的第一瓶成功作品。从此，她明白了：香味魔法不仅能散播香气，更是一种温柔的治愈方式。

她的药剂室从不按常理布置。角落里种着会发光的薄荷，天花板上悬挂着倒生的紫藤，窗台上是会跳舞的迷迭香。每一种植物都在诉说着不同的心事。

她调配香水的方式很特别。不用魔法书，不照配方，只凭植物的轻语。当一个困扰的星宝走进药剂室，她会安静地观察：是春草般的慌乱？还是秋叶般的惆怅？然后，她会选择最合适的植物，让它们诉说治愈的故事。白玫瑰配鼠尾草，是为了安抚那个对未来充满疑惑的少年；薰衣草混合柠檬草，献给总是焦虑的小提琴手。她始终坚信：每一种情绪都能在植物中找到共鸣，每一份困扰都值得被温柔以待。

超能学园的学生们都知道：当你闻到一缕特别的香气，那是安娜苏在用植物的语言，轻轻拥抱着某个需要安慰的灵魂。]],
suitId = 1074,
suitName = "调香师 安娜苏",
suitIcon = "CDN:Icon_PL_293"
},
[411181] = {
id = 411181,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "调香师 安娜苏",
desc = "如果你感到忧虑，不妨来调香室冥想",
icon = "CDN:Icon_PL_293_01",
outlookConf = {
belongTo = 411180,
fashionValue = 35,
belongToGroup = {
411181,
411182
}
},
resourceConf = {
model = "SK_PL_293",
material = "MI_PL_293_1_HP01;MI_PL_293_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_293_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12100,
outEnter = "AS_CH_Enter_PL_293",
outShow = "AS_CH_IdleShow_PL_293",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_293",
shareTexts = {
"拥抱花香，重拾勇气与希望"
},
shareAnim = v3
},
[411182] = {
id = 411182,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "调香师 安娜苏",
desc = "如果你感到忧虑，不妨来调香室冥想",
icon = "CDN:Icon_PL_293_02",
outlookConf = {
belongTo = 411180,
fashionValue = 35,
belongToGroup = {
411181,
411182
}
},
resourceConf = {
model = "SK_PL_293",
material = "MI_PL_293_1_HP02;MI_PL_293_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_293_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12101,
outEnter = "AS_CH_Enter_PL_293",
outShow = "AS_CH_IdleShow_PL_293",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_293",
shareTexts = {
"拥抱花香，重拾勇气与希望"
},
shareAnim = v3
},
[411190] = {
id = 411190,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 300
}
},
quality = 2,
name = " 梦笔生花 绮",
desc = "墨香盈袖，画意随心",
icon = "CDN:Icon_PL_288",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_288",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_288_Physics",
skeletalMesh = "SK_PL_288"
},
outEnter = "AS_CH_Enter_PL_288",
outShow = "AS_CH_IdleShow_PL_288",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_288",
shareTexts = {
"画笔显神通！"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[相传在一幅泛黄的古画中，沉睡着一位画灵。发丝如初绽的樱花，眉目如晕染开的水墨。她是一代画圣临终前的最后一副作品，并赋名为——绮。画圣最后一笔点睛，魂魄化作春风散去，而绮却在那一刻缓缓睁开了眼眸，继承了画圣毕生所追求的至善至美，以及未竟的心愿——绘尽世间所有的美好。

画中自有千秋雪，画外却是万般春。绮临摹万物，渴望捕捉画外的光影，可墨色再浓，也难画出风的温度，笔触再妙，也难描摹内心的悸动。直到一个月圆之夜，绮用朱砂点破画卷，将自己从纸上剥离。画中角，终成画外仙。

初入元梦世界，绮对一切都充满好奇。她用画笔记录晨曦初露的微光，描摹夜市灯火的流转，甚至尝试勾勒星宝们的笑颜——可总觉得笔下生花，却少了几分温度。
她渴望创造属于自己的“真实”，于是，在一个细雨微落的春日午后，她轻轻挥动画笔，在纸上描绘出一朵带着露珠的花。那是一朵她从未在世间见过的奇花，花瓣如霞，花心闪烁微光。就在最后一笔落下的瞬间——那朵花竟缓缓绽放，化作灵体，轻盈地飘在绮的肩畔。

她为这朵花灵取名为——夭夭。夭夭是绮画出的第一个真实生命，也是她的画笔第一次“唤醒”了情感的具象。它不是普通的花灵，而是能感知情绪的存在。当绮悲伤时，夭夭会轻轻摇曳，用花瓣拭去她的眼泪；当她喜悦时，夭夭便会绽放出七彩光辉，仿佛回应她心中的欢愉。从此，夭夭便成了她最亲密的伙伴，也成为了她画中梦境的引领者。

后来绮又遇见一个哭泣的星宝——他的纸鸢破损，泪水涟涟。绮执笔描绘，奇迹再次发生——那只凤凰竟从纸上飞起，盘旋在空中，化作真正的灵鸟！小星宝破涕为笑，而绮终于明白：画，不止于形，更在于心。

或许是因为灵魂来自画卷的缘故，绮常常坠入神秘的梦境。梦里，她看见了另一个世界——那里没有苍白的墨色，只有斑斓的光影与异想天开的奇景，甚至还有另一个自己！绮行走四方，将梦境中的画变为现实，为怯懦者添上勇气的双翼，为孤独者描绘出相伴的知己……她的画，已不仅仅是画，而是承载了真实情感的生命。夭夭常伴她左右，见证着她的每一幅画，每一次心动
。
“世间万物，皆可入画；但若画中无情，终究不过是空壳。”她如此说道，笑容如初绽的樱花，美得不似世间所有。]],
suitId = 1076,
suitName = " 梦笔生花 绮",
suitIcon = "CDN:Icon_PL_288",
bodytype = 2
},
[411200] = {
id = 411200,
effect = true,
name = "绒绒兔叽",
desc = "时间还早，让我再睡会儿吧~",
icon = "CDN:Icon_BU_308",
resourceConf = {
model = "SK_BU_308",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"遇到问题，睡个懒觉"
},
shareAnim = v3,
beginTime = v4,
suitId = 1078,
suitName = "绒绒兔叽",
suitIcon = "CDN:Icon_BU_308"
},
[411201] = {
id = 411201,
effect = true,
name = "绒绒兔叽",
desc = "时间还早，让我再睡会儿吧~",
icon = "CDN:Icon_BU_308_01",
outlookConf = {
belongTo = 411200,
fashionValue = 25,
belongToGroup = {
411201
}
},
resourceConf = {
model = "SK_BU_308",
material = "MI_BU_308_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12104,
shareTexts = {
"遇到问题，睡个懒觉"
},
shareAnim = v3
},
[411210] = {
id = 411210,
effect = true,
name = "星小虎",
desc = "虎虎生威，吓你一跳",
icon = "CDN:Icon_BU_296",
resourceConf = {
model = "SK_BU_296",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"不是纸老虎"
},
shareAnim = v3,
beginTime = v4,
suitId = 1080,
suitName = "星小虎",
suitIcon = "CDN:Icon_BU_296"
},
[411211] = {
id = 411211,
effect = true,
name = "星小虎",
desc = "虎虎生威，吓你一跳",
icon = "CDN:Icon_BU_296_01",
outlookConf = {
belongTo = 411210,
fashionValue = 25,
belongToGroup = {
411211
}
},
resourceConf = {
model = "SK_BU_296",
material = "MI_BU_296_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12106,
shareTexts = {
"不是纸老虎"
},
shareAnim = v3
},
[411220] = {
id = 411220,
effect = true,
name = "狸小豹",
desc = "把我哄高兴了，就奖励你和我玩一会儿",
icon = "CDN:Icon_BU_293",
resourceConf = {
model = "SK_BU_293",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"独一无二的小狸猫"
},
shareAnim = v3,
beginTime = v4,
suitId = 1082,
suitName = "狸小豹",
suitIcon = "CDN:Icon_BU_293"
},
[411221] = {
id = 411221,
effect = true,
name = "狸小豹",
desc = "把我哄高兴了，就奖励你和我玩一会儿",
icon = "CDN:Icon_BU_293_01",
outlookConf = {
belongTo = 411220,
fashionValue = 25,
belongToGroup = {
411221
}
},
resourceConf = {
model = "SK_BU_293",
material = "MI_BU_293_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12108,
shareTexts = {
"独一无二的小狸猫"
},
shareAnim = v3
},
[411230] = {
id = 411230,
effect = true,
name = "奥黛特",
desc = "不想洗头，但想保持美丽",
icon = "CDN:Icon_BU_292",
resourceConf = {
model = "SK_BU_292",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"换个角度，从头开始"
},
shareAnim = v3,
beginTime = v4,
suitId = 1084,
suitName = "奥黛特",
suitIcon = "CDN:Icon_BU_292"
},
[411231] = {
id = 411231,
effect = true,
name = "奥黛特",
desc = "不想洗头，但想保持美丽",
icon = "CDN:Icon_BU_292_01",
outlookConf = {
belongTo = 411230,
fashionValue = 25,
belongToGroup = {
411231
}
},
resourceConf = {
model = "SK_BU_292",
material = "MI_BU_292_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12110,
shareTexts = {
"换个角度，从头开始"
},
shareAnim = v3
},
[411240] = {
id = 411240,
effect = true,
name = "啵啵软糖",
desc = "喜欢我？哼，理所当然~",
icon = "CDN:Icon_BU_303",
resourceConf = {
model = "SK_BU_303",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"软软弹弹，但不好拿捏"
},
shareAnim = v3,
beginTime = v4,
suitId = 1086,
suitName = "啵啵软糖",
suitIcon = "CDN:Icon_BU_303"
},
[411241] = {
id = 411241,
effect = true,
name = "啵啵软糖",
desc = "喜欢我？哼，理所当然~",
icon = "CDN:Icon_BU_303_01",
outlookConf = {
belongTo = 411240,
fashionValue = 25,
belongToGroup = {
411241
}
},
resourceConf = {
model = "SK_BU_303",
material = "MI_BU_303_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12112,
shareTexts = {
"软软弹弹，但不好拿捏"
},
shareAnim = v3
},
[411250] = {
id = 411250,
effect = true,
name = "可可小姐",
desc = "波点，是一种优雅",
icon = "CDN:Icon_BU_302",
resourceConf = {
model = "SK_BU_302",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"复古，但仍是潮流"
},
shareAnim = v3,
beginTime = v4,
suitId = 1088,
suitName = "可可小姐",
suitIcon = "CDN:Icon_BU_302"
},
[411251] = {
id = 411251,
effect = true,
name = "可可小姐",
desc = "波点，是一种优雅",
icon = "CDN:Icon_BU_302_01",
outlookConf = {
belongTo = 411250,
fashionValue = 25,
belongToGroup = {
411251
}
},
resourceConf = {
model = "SK_BU_302",
material = "MI_BU_302_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12114,
shareTexts = {
"复古，但仍是潮流"
},
shareAnim = v3
},
[411260] = {
id = 411260,
effect = true,
name = "黄花少女",
desc = "不要小看，我很有才华",
icon = "CDN:Icon_BU_294",
resourceConf = {
model = "SK_BU_294",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"小小身体，大大才华"
},
shareAnim = v3,
beginTime = v4,
suitId = 1090,
suitName = "黄花少女",
suitIcon = "CDN:Icon_BU_294"
},
[411261] = {
id = 411261,
effect = true,
name = "黄花少女",
desc = "不要小看，我很有才华",
icon = "CDN:Icon_BU_294_01",
outlookConf = {
belongTo = 411260,
fashionValue = 25,
belongToGroup = {
411261
}
},
resourceConf = {
model = "SK_BU_294",
material = "MI_BU_294_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12116,
shareTexts = {
"小小身体，大大才华"
},
shareAnim = v3
},
[411270] = {
id = 411270,
effect = true,
name = "星星海",
desc = "放下执着，视野与海洋一样宽广",
icon = "CDN:Icon_BU_307",
resourceConf = {
model = "SK_BU_307",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"不开心的时候，看看海吧"
},
shareAnim = v3,
beginTime = v4,
suitId = 1092,
suitName = "星星海",
suitIcon = "CDN:Icon_BU_307"
},
[411271] = {
id = 411271,
effect = true,
name = "星星海",
desc = "放下执着，视野与海洋一样宽广",
icon = "CDN:Icon_BU_307_01",
outlookConf = {
belongTo = 411270,
fashionValue = 25,
belongToGroup = {
411271
}
},
resourceConf = {
model = "SK_BU_307",
material = "MI_BU_307_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12118,
shareTexts = {
"不开心的时候，看看海吧"
},
shareAnim = v3
},
[411280] = {
id = 411280,
effect = true,
name = "安康叶叶",
desc = "送你一个小粽子，安康又快乐",
icon = "CDN:Icon_BU_309",
resourceConf = {
model = "SK_BU_309",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"你是咸党，还是甜党？"
},
shareAnim = v3,
beginTime = v4,
suitId = 1094,
suitName = "安康叶叶",
suitIcon = "CDN:Icon_BU_309"
},
[411281] = {
id = 411281,
effect = true,
name = "安康叶叶",
desc = "送你一个小粽子，安康又快乐",
icon = "CDN:Icon_BU_309_01",
outlookConf = {
belongTo = 411280,
fashionValue = 25,
belongToGroup = {
411281
}
},
resourceConf = {
model = "SK_BU_309",
material = "MI_BU_309_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12120,
shareTexts = {
"你是咸党，还是甜党？"
},
shareAnim = v3
},
[411290] = {
id = 411290,
effect = true,
name = "舞翩翩",
desc = "一舞太平世，二舞意中人",
icon = "CDN:Icon_BU_321",
resourceConf = {
model = "SK_BU_321",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"歌声悠扬，于异域传唱"
},
shareAnim = v3,
beginTime = v4,
suitId = 1096,
suitName = "舞翩翩",
suitIcon = "CDN:Icon_BU_321"
},
[411291] = {
id = 411291,
effect = true,
name = "舞翩翩",
desc = "一舞太平世，二舞意中人",
icon = "CDN:Icon_BU_321_01",
outlookConf = {
belongTo = 411290,
fashionValue = 25,
belongToGroup = {
411291
}
},
resourceConf = {
model = "SK_BU_321",
material = "MI_BU_321_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12122,
shareTexts = {
"歌声悠扬，于异域传唱"
},
shareAnim = v3
},
[411300] = {
id = 411300,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "赤魂守心 龙绛",
desc = "若无法改变世界，那就改变自己",
icon = "CDN:Icon_PL_282",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_282",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_282_Physics"
},
outEnter = "AS_CH_Enter_PL_282",
outShow = "AS_CH_IdleShow_PL_282",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_282",
shareTexts = {
"我命由我"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[上古时期，赤龙为害四方，各方侠士协力镇压，方平息祸患。赤龙陨落的那一天，下了一场雨，龙绛从这场雨中诞生，他是残余着赤龙精魂的孩子。

龙绛从小便会做漫长的噩梦，梦中的他是赤龙，作恶多端、为害世间。他修炼着心法，默默忍受这种煎熬。

通常龙族大家长会为小辈安排庇护之地。可龙绛总是独身，得不到帮助，他边修炼边游历，倒是真让他寻得一方山水间，还未得到庇护的地儿。这里的星宝不多，却朴素善良，在和他们相互了解之后，便成了此地的庇护龙王。日子闲适，连噩梦都少了许多。

一次与黑烟的较量中，黑烟化作他的模样，面目狰狞，长着赤色之角，如凶神降世。龙绛大惊失色，被黑烟偷袭，最后费尽力气才将它消灭。大雨落下，看着水中自己逐渐变成赤色的角，龙绛痛苦不已，终究还是逃不过吗？ 

他眼中的世界开始变成愤怒的红色，在即将发狂的瞬间，他感受到一双双温暖的手托住了他，是他庇护的星宝们！他们带着龙绛走向山水间，龙绛意识到，原来那里早已是他的家了。

几个月后，摸着自己变红的角，龙绛明晰，是他所行之事定义着他究竟为谁，只要他全心庇护着山水之地，他是不是赤龙，又重要吗？]],
suitId = 1098,
suitName = "赤魂守心 龙绛",
suitIcon = "CDN:Icon_PL_282"
},
[411301] = {
id = 411301,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "赤魂守心 龙绛",
desc = "若无法改变世界，那就改变自己",
icon = "CDN:Icon_PL_282_01",
outlookConf = {
belongTo = 411300,
fashionValue = 35,
belongToGroup = {
411301,
411302
}
},
resourceConf = {
model = "SK_PL_282",
material = "MI_PL_282_1_HP01;MI_PL_282_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_282_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12125,
outEnter = "AS_CH_Enter_PL_282",
outShow = "AS_CH_IdleShow_PL_282",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_282",
shareTexts = {
"我命由我"
},
shareAnim = v3
},
[411302] = {
id = 411302,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "赤魂守心 龙绛",
desc = "若无法改变世界，那就改变自己",
icon = "CDN:Icon_PL_282_02",
outlookConf = {
belongTo = 411300,
fashionValue = 35,
belongToGroup = {
411301,
411302
}
},
resourceConf = {
model = "SK_PL_282",
material = "MI_PL_282_1_HP02;MI_PL_282_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_282_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12126,
outEnter = "AS_CH_Enter_PL_282",
outShow = "AS_CH_IdleShow_PL_282",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_282",
shareTexts = {
"我命由我"
},
shareAnim = v3
},
[411310] = {
id = 411310,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "黄金骑士 席德",
desc = "谱写黄金精神的新篇章",
icon = "CDN:Icon_PL_297",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_297",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_297_Physics"
},
outEnter = "AS_CH_Enter_PL_297",
outShow = "AS_CH_IdleShow_PL_297",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_297",
shareTexts = {
"骑士永远不会后退"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[沙之城孤单的伫立在沙漠中央，四周有黑暗环绕，里面窥伺的魔兽总在蠢蠢欲动。在沙城星宝陷入绝望时，他们救下了一位受伤的骑士。

席德醒来时，周围聚着数不清的星宝，大家将他当作了拯救沙城的黄金骑士。传说中，黄金骑士会带领大家穿过黑暗，找到繁茂的绿洲。

席德开始守卫沙城，出城和归来时星宝都会在道路旁为他歌唱·。黑暗依旧在逼近，大家发现黄金骑士不像传说中那般无所不能，他会受伤、疲惫，也未见他使用黄金之力。

黑暗兵临城下，席德却依旧没有使用黄金之力。有星宝注意到他眼中的星芒，恍然大悟：“他不是不想用，而是不能用，他被诅咒了。” 

星宝们讶异的看着席德，他只得坦白真相。席德曾梦想成为守护一切的黄金骑士，与伪善的国王立下誓约后，却发现他贪得无厌、暴戾无道，只想把席德用作战争的工具。于是席德背弃了守护誓约，任由贪婪的国王被风沙掩埋，而他也遭受了誓约的反噬诅咒，再也无法使用神赐的黄金之力。

席德本该迷失在沙漠中，结束这一生。可沙城的救助却给了他再来一次的机会，星宝们的善良也让他再次找回了愿意守护一切的心。即使他已经失去了黄金之力，但只要他在，沙城就不会倒下！席德只身踏入黑暗……

星宝们皆在为席德祈祷。也不知过了多久，黑暗中有金色光芒闪烁，且愈来愈旺盛，黑暗在四散逃逸。席德持剑伫立，金色光辉闪耀，凭借意志和想守护一切的心铸成的黄金精神，比所有神赐的黄金之力都更耀眼。他即是黄金本身。

如今，若来到沙城，在城市中心的纪念碑下能见到一行显眼的文字：为了沙城的和谐安宁，我将挚诚守护——席德。]],
suitId = 1100,
suitName = "甜心女仆 莉迪亚",
suitIcon = "CDN:Icon_PL_297"
},
[411311] = {
id = 411311,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "黄金骑士 席德",
desc = "谱写黄金精神的新篇章",
icon = "CDN:Icon_PL_297_01",
outlookConf = {
belongTo = 411310,
fashionValue = 35,
belongToGroup = {
411311,
411312
}
},
resourceConf = {
model = "SK_PL_297",
material = "MI_PL_297_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_297_Physics",
materialSlot = "Skin"
},
commodityId = 12128,
outEnter = "AS_CH_Enter_PL_297",
outShow = "AS_CH_IdleShow_PL_297",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_297",
shareTexts = {
"骑士永远不会后退"
},
shareAnim = v3
},
[411312] = {
id = 411312,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "黄金骑士 席德",
desc = "谱写黄金精神的新篇章",
icon = "CDN:Icon_PL_297_02",
outlookConf = {
belongTo = 411310,
fashionValue = 35,
belongToGroup = {
411311,
411312
}
},
resourceConf = {
model = "SK_PL_297",
material = "MI_PL_297_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_297_Physics",
materialSlot = "Skin"
},
commodityId = 12129,
outEnter = "AS_CH_Enter_PL_297",
outShow = "AS_CH_IdleShow_PL_297",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_297",
shareTexts = {
"骑士永远不会后退"
},
shareAnim = v3
},
[411320] = {
id = 411320,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "海洋画家 安德娅",
desc = "大海的故事，由我来描绘",
icon = "CDN:Icon_PL_298",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_298",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_298_Physics",
skeletalMesh = "SK_PL_298"
},
outEnter = "AS_CH_Enter_PL_298",
outIdle = "AS_CH_OutIdle_PL_298",
outShow = "AS_CH_IdleShow_PL_298",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_298",
shareTexts = {
"一起完成这幅海底画卷吧！"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[安德娅是海洋中最特别的画家。她的画室是一颗巨大的虹彩贝壳，以发光的海藻汁为颜料，海月冰为画布，绘出绚丽的海洋奇景。然而海水总会融化颜料，让她的作品消散。她说着“没关系，我再画一次”，可心底仍渴望一种永不褪色的色彩。

某天，她在沉船中发现一幅百年未褪的壁画，旁边刻着：深渊海之泪，可固色彩。她触碰那些鲜活的颜色，心中燃起希望，带着最爱的画作奔赴深渊。

狂暴的暗流冲击，颜料四散，可她不愿放弃，她想画出能够永恒留存的画作。终于，在画作即将消失前，她抵达四周满是瑰丽壁画的深渊。一个冰冷的声音自壁画间响起：“想得到海之泪，就用你最珍贵的回忆交换。”

安德娅怔住了。若失去记忆，永恒的画卷又有何意义？她看着画纸渐渐空白，最终摇头：“我不能答应。”她的声音轻而坚定。

壁画守护者沉默良久，语气忽然柔和：“你是第一个拒绝交易的画家。”一滴晶莹的海之泪浮现，融入安德娅的调色盘：“海之泪是无数画家记忆的结晶，真正永恒的不在颜料，而在于创作回忆的星宝内心。带着你的回忆，请回吧。”

回到珊瑚礁，安德娅举办了一场“漂流画展”，她将画系在鱼群背上，让色彩游遍整片海洋。夕阳穿透海面时，她望着颜料如流星般散入水流，笑意比以往更明亮——海水带得走色彩，却带不走故事，也带不走那些铭刻在生命中的记忆。

远处，守护者的低语随波荡漾： “看，你的画已经永恒了。”]],
suitId = 1102,
suitName = "海洋画家 安德娅",
suitIcon = "CDN:Icon_PL_298",
bodytype = 2
},
[411321] = {
id = 411321,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "海洋画家 安德娅",
desc = "大海的故事，由我来描绘",
icon = "CDN:Icon_PL_298_01",
outlookConf = {
belongTo = 411320,
fashionValue = 35,
belongToGroup = {
411321,
411322
}
},
resourceConf = {
model = "SK_PL_298",
material = "MI_PL_298_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_298_Physics",
materialSlot = "Skin",
skeletalMesh = "SK_PL_298"
},
commodityId = 12131,
outEnter = "AS_CH_Enter_PL_298",
outIdle = "AS_CH_OutIdle_PL_298",
outShow = "AS_CH_IdleShow_PL_298",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_298",
shareTexts = {
"一起完成这幅海底画卷吧！"
},
shareAnim = v3,
bodytype = 2
},
[411322] = {
id = 411322,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "海洋画家 安德娅",
desc = "大海的故事，由我来描绘",
icon = "CDN:Icon_PL_298_02",
outlookConf = {
belongTo = 411320,
fashionValue = 35,
belongToGroup = {
411321,
411322
}
},
resourceConf = {
model = "SK_PL_298",
material = "MI_PL_298_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_298_Physics",
materialSlot = "Skin",
skeletalMesh = "SK_PL_298"
},
commodityId = 12132,
outEnter = "AS_CH_Enter_PL_298",
outIdle = "AS_CH_OutIdle_PL_298",
outShow = "AS_CH_IdleShow_PL_298",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_298",
shareTexts = {
"一起完成这幅海底画卷吧！"
},
shareAnim = v3,
bodytype = 2
},
[411330] = {
id = 411330,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 300
}
},
quality = 2,
name = "Human限定款",
desc = "专属于独一无二的你",
icon = "CDN:Icon_PL_314",
outlookConf = {
fashionValue = 2000
},
resourceConf = {
model = "SK_PL_314",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_314_Physics",
IconLabelId = 103
},
outEnter = "AS_CH_Enter_PL_314",
outShow = "AS_CH_IdleShow_PL_314",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_314",
shareTexts = {
"定制你的独特冒险"
},
shareAnim = v3,
beginTime = v4,
suitId = 1104,
suitName = "Human限定款",
suitIcon = "CDN:Icon_PL_314"
},
[411340] = {
id = 411340,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 300
}
},
quality = 2,
name = "Human限定款",
desc = "专属于独一无二的你",
icon = "CDN:Icon_PL_315",
outlookConf = {
fashionValue = 1500
},
resourceConf = {
model = "SK_PL_315",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_315_Physics",
IconLabelId = 103
},
outEnter = "AS_CH_Enter_PL_315",
outShow = "AS_CH_IdleShow_PL_315",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_315",
shareTexts = {
"定制你的独特冒险"
},
shareAnim = v3,
beginTime = v4,
suitId = 1106,
suitName = "Human限定款",
suitIcon = "CDN:Icon_PL_315"
},
[411350] = {
id = 411350,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 300
}
},
quality = 2,
name = "Human限定款",
desc = "专属于独一无二的你",
icon = "CDN:Icon_PL_316",
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_PL_316",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_316_Physics",
IconLabelId = 103
},
outEnter = "AS_CH_Enter_PL_316",
outShow = "AS_CH_IdleShow_PL_316",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_316",
shareTexts = {
"定制你的独特冒险"
},
shareAnim = v3,
beginTime = v4,
suitId = 1108,
suitName = "Human限定款",
suitIcon = "CDN:Icon_PL_316"
},
[411360] = {
id = 411360,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 300
}
},
quality = 2,
name = "奇遇舞章 艾琳",
desc = "我们因爱，而自由",
icon = "CDN:Icon_PL_289",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_289",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_289_Physics",
IconLabelId = 102,
skeletalMesh = "SK_PL_289"
},
outEnter = "AS_CH_Enter_PL_289",
outIdle = "AS_CH_OutIdle_PL_289",
outShow = "AS_CH_IdleShow_PL_289",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_289",
shareTexts = {
"去追逐，去寻找"
},
shareAnim = v3,
beginTime = v4,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_411360.astc",
suitId = 1110,
suitName = "奇遇舞章 艾琳",
suitIcon = "CDN:Icon_PL_289",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_411360.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_411360.astc"
},
[411370] = {
id = 411370,
effect = true,
name = "萌力欧皇",
desc = "哎呀，又抽中了隐藏款！",
icon = "CDN:Icon_BU_304",
resourceConf = {
model = "SK_BU_304",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"幸运，也是一种烦恼"
},
shareAnim = v3,
beginTime = v4,
suitId = 1112,
suitName = "萌力欧皇",
suitIcon = "CDN:Icon_BU_304"
},
[411371] = {
id = 411371,
effect = true,
name = "萌力欧皇",
desc = "哎呀，又抽中了隐藏款！",
icon = "CDN:Icon_BU_304_01",
outlookConf = {
belongTo = 411370,
fashionValue = 25,
belongToGroup = {
411371
}
},
resourceConf = {
model = "SK_BU_304",
material = "MI_BU_304_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12138,
shareTexts = {
"幸运，也是一种烦恼"
},
shareAnim = v3
},
[411380] = {
id = 411380,
effect = true,
name = "宝葫涂涂",
desc = "糊糊涂涂，也很好嘛~",
icon = "CDN:Icon_BU_310",
resourceConf = {
model = "SK_BU_310",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"圆圆润润，也很可爱"
},
shareAnim = v3,
beginTime = v4,
suitId = 1114,
suitName = "宝葫涂涂",
suitIcon = "CDN:Icon_BU_310"
},
[411381] = {
id = 411381,
effect = true,
name = "宝葫涂涂",
desc = "糊糊涂涂，也很好嘛~",
icon = "CDN:Icon_BU_310_01",
outlookConf = {
belongTo = 411380,
fashionValue = 25,
belongToGroup = {
411381
}
},
resourceConf = {
model = "SK_BU_310",
material = "MI_BU_310_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12140,
shareTexts = {
"圆圆润润，也很可爱"
},
shareAnim = v3
},
[411390] = {
id = 411390,
effect = true,
name = "羽飞飞",
desc = "行侠仗义，是我的本色",
icon = "CDN:Icon_BU_311",
resourceConf = {
model = "SK_BU_311",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"飞檐走壁，消息小灵通"
},
shareAnim = v3,
beginTime = v4,
suitId = 1116,
suitName = "羽飞飞",
suitIcon = "CDN:Icon_BU_311"
},
[411391] = {
id = 411391,
effect = true,
name = "羽飞飞",
desc = "行侠仗义，是我的本色",
icon = "CDN:Icon_BU_311_01",
outlookConf = {
belongTo = 411390,
fashionValue = 25,
belongToGroup = {
411391
}
},
resourceConf = {
model = "SK_BU_311",
material = "MI_BU_311_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12142,
shareTexts = {
"飞檐走壁，消息小灵通"
},
shareAnim = v3
},
[411400] = {
id = 411400,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "黄金·气泡狗",
desc = "这一次，不是绿泡泡，而是金泡泡",
icon = "CDN:Icon_Body_044",
outlookConf = {
fashionValue = 800
},
resourceConf = {
model = "SK_Body_Head_044",
upper = "SK_Body_Upper_044",
bottom = "SK_Body_Under_044",
gloves = "SK_Body_Hands_044",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 103
},
shareTexts = {
"好运冒泡中~"
},
shareAnim = v3,
beginTime = v4,
suitId = 1118,
suitName = "黄金·气泡狗",
suitIcon = "CDN:Icon_Body_044"
},
[411410] = {
id = 411410,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "水母歌姬 妮丽达",
desc = "我会把海洋的故事，唱给全世界",
icon = "CDN:Icon_OG_044",
outlookConf = {
fashionValue = 1500
},
resourceConf = {
model = "SK_OG_044",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_044_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_044_PV/Level_OG_044_Intact",
outIdle = "AS_CH_OutIdle_OG_044",
outShow = "AS_CH_IdleShow_OG_044",
outShowIntervalTime = 20,
soundId = {
4055,
4057
},
outIdle_pv = "LS_PV_OG_044_Idle",
outEnterSequence = "LS_PV_OG_044",
shareTexts = {
"海洋之歌，为你而唱"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[妮丽达很小的时候，被一支来自远古鲸落的歌声唤醒。从那天起，她能听懂万物的声音——珊瑚礁的低语、海藻的轻叹，甚至是游鱼跃出水面时的喜悦——并将它们转化为歌声。
在一个月光如水的夜晚，妮丽达在珊瑚礁旁吟唱，歌声惊扰了沉静的海水，无形的波浪如银色丝带般翩翩起舞。沿着海水涌动的轨迹，她遇到了德里安——珊瑚海的王子。他身影与周围的珊瑚融为一体，孤独而温柔。
德里安为了守护被污染的金珊瑚海域，用强大的力量控制着海水的流动，封印海底城堡，却也因此被困于此。妮丽达被他的坚守所打动，选择留下为他歌唱。在德里安的鼓励下，她的歌声越发悠扬，甚至拥有了不可思议的力量，能让黯淡的珊瑚重新焕发生机，让狂暴的海浪变得平和。
妮丽达成了海洋世界最负盛名的歌姬，她用歌声抚慰着海洋的伤痕，却也付出了代价：她的身体逐渐变得透明，歌声越发微弱。但她依然选择歌唱，为了这片海洋，也为了那个被禁锢的温柔身影。
德里安不忍妮丽达这样消逝，他迈出了守护的海域，将海流的力量传递给妮丽达，试图为她提供一丝支持。那一刻，歌声与海流产生了强烈的共鸣，化作一股净化的力量，让珊瑚海重新焕发生机。
妮丽达的身体焕发出晶莹的光泽，却并没有消逝，她的歌声似乎溶解在汹涌的海水中，回荡至远方。她恍然明白，她愈发透明的身体凝结出了珊瑚海最纯粹的声音，与守护者相呼应，一同治愈了他们的海洋。
如今，每当夜幕降临，珊瑚海域便亮起梦幻的光芒。那是妮丽达的歌声与德里安的海水之力在共舞，守护着这片海洋的每一个角落。 ]],
suitId = 1120,
suitName = "水母歌姬 妮丽达",
suitIcon = "CDN:Icon_OG_044",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[411411] = {
id = 411411,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "水母歌姬 妮丽达",
desc = "我会把海洋的故事，唱给全世界",
icon = "CDN:Icon_OG_044_01",
outlookConf = {
belongTo = 411410,
fashionValue = 145,
belongToGroup = {
411411,
411412
}
},
resourceConf = {
model = "SK_OG_044",
material = "MI_OG_044_1_HP01;MI_OG_044_2_HP01;MI_OG_044_3_HP01;MI_OG_044_4_HP01;MI_OG_044_5_HP01;MI_OG_044_6_HP01;MI_OG_044_7_HP01;MI_OG_044_8_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_044_Physics",
materialSlot = "Skin;Skin_2;Skin_3;Skin_4;Skin_5;Skin_6;Skin_7;Skin_8"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_044_PV/Level_OG_044_Intact",
outIdle = "AS_CH_OutIdle_OG_044",
outShow = "AS_CH_IdleShow_OG_044_HP01",
outShowIntervalTime = 20,
soundId = {
4055,
4057
},
outIdle_pv = "LS_PV_OG_044_HP01_Idle",
outEnterSequence = "LS_PV_OG_044_HP01",
shareTexts = {
"海洋之歌，为你而唱"
},
shareAnim = v3,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[411412] = {
id = 411412,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "水母歌姬 妮丽达",
desc = "我会把海洋的故事，唱给全世界",
icon = "CDN:Icon_OG_044_02",
outlookConf = {
belongTo = 411410,
fashionValue = 145,
belongToGroup = {
411411,
411412
}
},
resourceConf = {
model = "SK_OG_044",
material = "MI_OG_044_1_HP02;MI_OG_044_2_HP02;MI_OG_044_3_HP02;MI_OG_044_4_HP02;MI_OG_044_5_HP02;MI_OG_044_6_HP02;MI_OG_044_7_HP02;MI_OG_044_8_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_044_Physics",
materialSlot = "Skin;Skin_2;Skin_3;Skin_4;Skin_5;Skin_6;Skin_7;Skin_8"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_044_PV/Level_OG_044_Intact",
outIdle = "AS_CH_OutIdle_OG_044",
outShow = "AS_CH_IdleShow_OG_044_HP02",
outShowIntervalTime = 20,
soundId = {
4055,
4057
},
outIdle_pv = "LS_PV_OG_044_HP02_Idle",
outEnterSequence = "LS_PV_OG_044_HP02",
shareTexts = {
"海洋之歌，为你而唱"
},
shareAnim = v3,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[411420] = {
id = 411420,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "珊瑚海王子 德里安",
desc = "与你相遇，我的守护有了新的意义",
icon = "CDN:Icon_OG_050",
outlookConf = {
fashionValue = 1500
},
resourceConf = {
model = "SK_OG_050",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_050_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_044_PV/Level_OG_044_Intact",
outIdle = "AS_CH_OutIdle_OG_050",
outShow = "AS_CH_IdleShow_OG_050",
outShowIntervalTime = 20,
soundId = {
4054,
4056
},
outIdle_pv = "LS_PV_OG_050_Idle",
outEnterSequence = "LS_PV_OG_050",
shareTexts = {
"我的心意，埋藏在珊瑚海"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[德里安是珊瑚海域的守护者，这片曾经生机勃勃的海域如今被污染笼罩。为了守护家园，他将自己封印在海底城堡中，用强大的力量控制着海水的流动，抵御污染的侵蚀，却也因此被困于此，成了被遗忘的珊瑚王子。他的生活只剩下孤独与坚守，直到妮丽达的歌声穿透了他的世界。
那歌声如海浪般细腻，惊醒了沉寂的德里安。在朦胧的光影中，他看到了妮丽达纤丽的身影，相视一笑，唤醒了海洋的温柔。妮丽达被他的坚守所打动，选择留下，用歌声抚平他心中的孤寂。
在德里安的鼓励下，妮丽达的歌声越发悠扬，甚至拥有了恢复海洋生机的神奇力量。然而，随着时间流逝，妮丽达的身体变得越来越透明，歌声也越发微弱。原来她的天赋竟是以生命为代价！但妮丽达不肯放弃歌唱，她要让歌声传至整片珊瑚海域，为了海洋的存续，也为了他坚守的身影。
德里安陷入了深深的挣扎，他不忍心看着妮丽达消失，但她的歌声又是这片海洋唯一的希望。最终，德里安做出了决定。他第一次迈出被遗忘的海域，引导海流环绕妮丽达，试图用这份守护之力保护妮丽达。
奇迹发生了，原本被污染的海水开始涌动，仿佛被赋予了新的生命。这股力量不再仅仅是德里安的枷锁，更是化作了一股能量巨大的净化之力，随着妮丽达的歌声在海洋中扩散开来。
德里安终于明白，他的使命不是困守一隅，而是顺应整个珊瑚海域的声音，守护里面的每个生灵。他不再是被封印的珊瑚王子，而是化作一股流动的力量，与妮丽达的歌声一起，净化着整片海洋，将希望和生机带到每一个角落。 ]],
suitId = 1122,
suitName = "珊瑚海王子 德里安",
suitIcon = "CDN:Icon_OG_050",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[411421] = {
id = 411421,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "珊瑚海王子 德里安",
desc = "与你相遇，我的守护有了新的意义",
icon = "CDN:Icon_OG_050_01",
outlookConf = {
belongTo = 411420,
fashionValue = 145,
belongToGroup = {
411421,
411422
}
},
resourceConf = {
model = "SK_OG_050",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_050_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_044_PV/Level_OG_044_Intact",
outIdle = "AS_CH_OutIdle_OG_050",
outShow = "AS_CH_IdleShow_OG_050_HP01",
outShowIntervalTime = 20,
soundId = {
4054,
4056
},
outIdle_pv = "LS_PV_OG_050_HP01_Idle",
outEnterSequence = "LS_PV_OG_050_HP01",
shareTexts = {
"我的心意，埋藏在珊瑚海"
},
shareAnim = v3,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[411422] = {
id = 411422,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "珊瑚海王子 德里安",
desc = "与你相遇，我的守护有了新的意义",
icon = "CDN:Icon_OG_050_02",
outlookConf = {
belongTo = 411420,
fashionValue = 145,
belongToGroup = {
411421,
411422
}
},
resourceConf = {
model = "SK_OG_050",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_050_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_044_PV/Level_OG_044_Intact",
outIdle = "AS_CH_OutIdle_OG_050",
outShow = "AS_CH_IdleShow_OG_050_HP02",
outShowIntervalTime = 20,
soundId = {
4054,
4056
},
outIdle_pv = "LS_PV_OG_050_HP02_Idle",
outEnterSequence = "LS_PV_OG_050_HP02",
shareTexts = {
"我的心意，埋藏在珊瑚海"
},
shareAnim = v3,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[411430] = {
id = 411430,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 300
}
},
quality = 2,
name = "森之风灵 虞姬",
desc = "明媚如风，轻盈似箭",
icon = "CDN:Icon_PL_232",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_232",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_232_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_232",
outShow = "AS_CH_IdleShow_PL_232",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_232",
shareTexts = {
"听，森林之灵的轻语"
},
shareAnim = v3,
beginTime = v4,
suitId = 1124,
suitName = "森之风灵 虞姬",
suitIcon = "CDN:Icon_PL_232"
},
[411440] = {
id = 411440,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "管弦指挥家 玛琳",
desc = "指挥棒的魔法会消失，但对音乐的爱常在",
icon = "CDN:Icon_PL_296",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_296",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_296_Physics"
},
outEnter = "AS_CH_Enter_PL_296",
outShow = "AS_CH_IdleShow_PL_296",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_296",
shareTexts = {
"万物听我指挥奏响乐章！"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[玛琳梦想成为指挥家，但她讨厌枯燥的练习，幻想能一夜成名。一天她在阁楼发现了一支精美的指挥棒，旁边附着令她心动的标签：魔法指挥棒，能让万物合奏，限用99次。

99次，简直太充足了！她兴奋地跑到花园挥动指挥棒。魔法随着她的指挥化为金光，向日葵都随之摇摆。她指挥猫咪合唱，将凌乱的风声变成温柔的摇篮曲。魔法逐渐减少：75次……50次……但她毫不在意，沉浸在成为优秀指挥家的美梦中。

渐渐地，玛琳完全依赖起魔法。在学校音乐会上，当她第99次挥动指挥棒时，“咔嚓”一声，指挥棒裂开了。魔法瞬间消散，乐团的演奏乱成一团。玛琳手足无措，耳边只剩下自己慌乱的心跳声。

就在这时，玛琳看见第一琴手的琴弓悬在空中，等待她的指示；大提琴手的指尖轻触琴弦，随时准备继续演奏。指挥棒在她手中微微发烫，她忽然想起爱上指挥的那天，舞台上指挥家的话语：“真正的指挥，是用心去对话。”

她闭上双眼，任由周围的嘈杂声涌入耳中。那些被魔法掩盖的声音开始有了形状——乐手们不安的呼吸、椅子轻微的挪动、远处观众席的窃窃私语……这些声音交织在一起，竟形成一种奇妙的韵律。

玛琳的手臂开始自然地挥动，仿佛指挥棒从未存在。那些被她忽略的练习、那些花园里的对空比划，此刻都成为指尖的笃定。乐团的声音再次汇聚，这一次，每个音符都充满了生命力。

当最后一个音符落下，破碎的指挥棒化作金色的星光。玛琳知道自己不再需要魔法了，因为真正的音乐，一直都在等待用心聆听的星宝。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_411440.astc",
suitId = 1126,
suitName = "管弦指挥家 玛琳",
suitIcon = "CDN:Icon_PL_296",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_411440.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_411440.astc"
},
[411441] = {
id = 411441,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "管弦指挥家 玛琳",
desc = "指挥棒的魔法会消失，但对音乐的爱常在",
icon = "CDN:Icon_PL_296_01",
outlookConf = {
belongTo = 411440,
fashionValue = 35,
belongToGroup = {
411441,
411442
}
},
resourceConf = {
model = "SK_PL_296",
material = "MI_PL_296_1_HP01;MI_PL_296_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_296_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12146,
outEnter = "AS_CH_Enter_PL_296",
outShow = "AS_CH_IdleShow_PL_296",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_296",
shareTexts = {
"万物听我指挥奏响乐章！"
},
shareAnim = v3,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_411440.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_411440.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_411440.astc"
},
[411442] = {
id = 411442,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "管弦指挥家 玛琳",
desc = "指挥棒的魔法会消失，但对音乐的爱常在",
icon = "CDN:Icon_PL_296_02",
outlookConf = {
belongTo = 411440,
fashionValue = 35,
belongToGroup = {
411441,
411442
}
},
resourceConf = {
model = "SK_PL_296",
material = "MI_PL_296_1_HP02;MI_PL_296_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_296_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12147,
outEnter = "AS_CH_Enter_PL_296",
outShow = "AS_CH_IdleShow_PL_296",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_296",
shareTexts = {
"万物听我指挥奏响乐章！"
},
shareAnim = v3,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_411440.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_411440.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_411440.astc"
},
[411450] = {
id = 411450,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "花栗鼠 珍妮特",
desc = "说好了，不许抢我的橡果哦",
icon = "CDN:Icon_PL_300",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_300",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_300_Physics"
},
outEnter = "AS_CH_Enter_PL_300",
outShow = "AS_CH_IdleShow_PL_300",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_300",
shareTexts = {
"橡果藏在哪里好呢？"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[珍妮特有一颗特别的橡果，圆润饱满，泛着蜜糖般的光泽。森林婆婆曾告诉她：“这是能保护整片森林的魔法橡果，只有在真正需要的时候才会显灵。”

她小心地将橡果藏在身上，每天都要检查三遍。每当其他星宝好奇地询问，她总是紧张地抱紧橡果：“弄丢了怎么办？魔法失效了谁来负责？”

雨季的第三天，天空突然阴沉下来。珍妮特刚把橡果放好，就听见外面传来此起彼伏的惊叫。她探出树洞，看见洪水已经冲垮了下游的堤坝，正咆哮着冲向森林。

“快上来！”高处传来星宝们的呼喊。珍妮特发现一个星宝被困在洪水中央的岩石上，湍急的水流正不断冲刷着那块摇摇欲坠的岩石。

她试着抓住星宝，却因湿滑的雨水差点双双坠落。就在这时，她怀里的橡果突然发出光亮，像是在提醒她什么，她猛然想起森林婆婆后半句话：“只有在真正需要的时候……”

眼看岩石就要被冲垮，珍妮特咬咬牙，用尽全力将橡果抛向洪水最湍急处。在橡果脱手的瞬间，她的心仿佛被揪了一下——那可是她最珍贵的宝贝啊！

橡果落入水中，一道金光从河底迸发。一棵巨大的橡树破水而出，粗壮的根系像一张巨网驯服了洪水；无数枝条化作救援的手臂，将惊慌失措的星宝们一一救起。

珍妮特蹚水来到橡树下，发现树上结满了蜜糖般的橡果。雨过天晴，她终于明白了森林婆婆的用意——分享才能让珍贵的魔法显现。她摘下新生的橡果，轻轻放在森林中央——从现在起，她愿意把魔法分享给每一个需要的星宝。]],
suitId = 1128,
suitName = "花栗鼠 珍妮特",
suitIcon = "CDN:Icon_PL_300"
},
[411451] = {
id = 411451,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "花栗鼠 珍妮特",
desc = "说好了，不许抢我的橡果哦",
icon = "CDN:Icon_PL_300_01",
outlookConf = {
belongTo = 411450,
fashionValue = 35,
belongToGroup = {
411451,
411452
}
},
resourceConf = {
model = "SK_PL_300",
material = "MI_PL_300_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_300_Physics",
materialSlot = "Skin"
},
commodityId = 12149,
outEnter = "AS_CH_Enter_PL_300",
outShow = "AS_CH_IdleShow_PL_300",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_300",
shareTexts = {
"橡果藏在哪里好呢？"
},
shareAnim = v3
},
[411452] = {
id = 411452,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "花栗鼠 珍妮特",
desc = "说好了，不许抢我的橡果哦",
icon = "CDN:Icon_PL_300_02",
outlookConf = {
belongTo = 411450,
fashionValue = 35,
belongToGroup = {
411451,
411452
}
},
resourceConf = {
model = "SK_PL_300",
material = "MI_PL_300_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_300_Physics",
materialSlot = "Skin"
},
commodityId = 12150,
outEnter = "AS_CH_Enter_PL_300",
outShow = "AS_CH_IdleShow_PL_300",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_300",
shareTexts = {
"橡果藏在哪里好呢？"
},
shareAnim = v3
},
[411460] = {
id = 411460,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "灼翼重明鸟 焰昕",
desc = "疾风烈火，助我夺魁",
icon = "CDN:Icon_PL_304",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_304",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_304_Physics"
},
outEnter = "AS_CH_Enter_PL_304",
outShow = "AS_CH_IdleShow_PL_304",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_304",
shareTexts = {
"诚邀你来火行宫做客！"
},
shareAnim = v3,
beginTime = {
seconds = 1744905600
},
suitStoryTextKey = [[上古时期，赤龙为害四方，各方侠士协力镇压，方平息祸患。赤龙陨落的那一天，下了一场雨，龙绛从这场雨中诞生，他是残余着赤龙精魂的孩子。

龙绛从小便会做漫长的噩梦，梦中的他是赤龙，作恶多端、为害世间。他修炼着心法，默默忍受这种煎熬。

通常龙族大家长会为小辈安排庇护之地。可龙绛总是独身，得不到帮助，他边修炼边游历，倒是真让他寻得一方山水间，还未得到庇护的地儿。这里的星宝不多，却朴素善良，在和他们相互了解之后，便成了此地的庇护龙王。日子闲适，连噩梦都少了许多。

一次与黑烟的较量中，黑烟化作他的模样，面目狰狞，长着赤色之角，如凶神降世。龙绛大惊失色，被黑烟偷袭，最后费尽力气才将它消灭。大雨落下，看着水中自己逐渐变成赤色的角，龙绛痛苦不已，终究还是逃不过吗？ 

他眼中的世界开始变成愤怒的红色，在即将发狂的瞬间，他感受到一双双温暖的手托住了他，是他庇护的星宝们！他们带着龙绛走向山水间，龙绛意识到，原来那里早已是他的家了。

几个月后，摸着自己变红的角，龙绛明晰，是他所行之事定义着他究竟为谁，只要他全心庇护着山水之地，他是不是赤龙，又重要吗？]],
suitId = 1130,
suitName = "灼翼重明鸟 焰昕",
suitIcon = "CDN:Icon_PL_304",
SeasonShowIdList = {
{
key = 13,
value = 1
}
}
},
[411461] = {
id = 411461,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "灼翼重明鸟 焰昕",
desc = "疾风烈火，助我夺魁",
icon = "CDN:Icon_PL_304_01",
outlookConf = {
belongTo = 411460,
fashionValue = 35,
belongToGroup = {
411461,
411462
}
},
resourceConf = {
model = "SK_PL_304",
material = "MI_PL_304_1_HP01;MI_PL_304_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_304_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12152,
outEnter = "AS_CH_Enter_PL_304",
outShow = "AS_CH_IdleShow_PL_304",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_304",
shareTexts = {
"诚邀你来火行宫做客！"
},
shareAnim = v3
},
[411462] = {
id = 411462,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "灼翼重明鸟 焰昕",
desc = "疾风烈火，助我夺魁",
icon = "CDN:Icon_PL_304_02",
outlookConf = {
belongTo = 411460,
fashionValue = 35,
belongToGroup = {
411461,
411462
}
},
resourceConf = {
model = "SK_PL_304",
material = "MI_PL_304_1_HP02;MI_PL_304_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_304_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12153,
outEnter = "AS_CH_Enter_PL_304",
outShow = "AS_CH_IdleShow_PL_304",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_304",
shareTexts = {
"诚邀你来火行宫做客！"
},
shareAnim = v3
},
[411470] = {
id = 411470,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "金辉白虎 宁岳",
desc = "金戈之气破万敌",
icon = "CDN:Icon_PL_305",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_305",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_305_Physics"
},
outEnter = "AS_CH_Enter_PL_305",
outShow = "AS_CH_IdleShow_PL_305",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_305",
shareTexts = {
"气吞万里，虎啸山林"
},
shareAnim = v3,
beginTime = v4,
suitStoryTextKey = [[白虎宁岳，金灵山的守护者，生得那叫一个温柔无害，乖萌可爱。宁岳却不喜欢自己这副可爱的模样，作为常年浸淫在传统白虎族文化中的星宝，他多希望自己有着和兄长们一般凶神恶煞的面容啊，这样也不至于每次出去巡山都被喊可爱小宁岳了！

为了改变自己的人设，他找到昭明以火行术暂时重塑他的面容。完成后，宁岳看着镜子，啧啧称奇，这下威严多了。不过术法见雨则会消退，得多加注意。

没多久宁岳就接到新的任务，是时候展现他的新面貌了！急匆匆赶到现场，却是让他护送一群小星宝到昆仑之心。这可着实让宁岳犯了难，星宝们见着他的模样就开始哭闹，精心换脸却弄巧成拙。宁岳只好想办法露出自己最为和善的表情，那可真是比哭还难看，但还真有用，小家伙们大喊着小丑，倒是不再哭了。

路上不太平，水妖趁宁岳准备晚餐时掳走了两只小星宝。愤怒的宁岳循着踪迹找到了水妖，灵力正欲暴走时，水妖把小星宝拿出当作挡箭牌。金灵力被强行压制住，刺痛着宁岳的皮肤，他假意投降，趁着水妖准备攻击他时，一把夺过小星宝，再释放之前压抑的力量，水妖消散，大雨随之落下，宁岳自己也被伤的千疮百孔。

焦急的小星宝们贡献自己微弱的灵力为宁岳疗伤，宁岳不禁笑出了声，小家伙们也纷纷跟着笑了起来，捏着他的脸说可爱，原来随着雨水滑落，术法已然失效。

最后宁岳拖着疲惫的身体，和小星宝们唱着歌，成功将他们送到了昆仑之心。

“可爱小宁岳~“欲离去时，白泽悠悠说到，”既无法改变，何不试试享受呢？“

宁岳尴尬的笑了笑，也不用这么快就享受啦！]],
suitId = 1132,
suitName = "金辉白虎 宁岳",
suitIcon = "CDN:Icon_PL_305"
},
[411471] = {
id = 411471,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "金辉白虎 宁岳",
desc = "金戈之气破万敌",
icon = "CDN:Icon_PL_305_01",
outlookConf = {
belongTo = 411470,
fashionValue = 35,
belongToGroup = {
411471,
411472
}
},
resourceConf = {
model = "SK_PL_305",
material = "MI_PL_305_1_HP01;MI_PL_305_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_305_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12155,
outEnter = "AS_CH_Enter_PL_305",
outShow = "AS_CH_IdleShow_PL_305",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_305",
shareTexts = {
"气吞万里，虎啸山林"
},
shareAnim = v3
},
[411472] = {
id = 411472,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "金辉白虎 宁岳",
desc = "金戈之气破万敌",
icon = "CDN:Icon_PL_305_02",
outlookConf = {
belongTo = 411470,
fashionValue = 35,
belongToGroup = {
411471,
411472
}
},
resourceConf = {
model = "SK_PL_305",
material = "MI_PL_305_1_HP02;MI_PL_305_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_305_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12156,
outEnter = "AS_CH_Enter_PL_305",
outShow = "AS_CH_IdleShow_PL_305",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_305",
shareTexts = {
"气吞万里，虎啸山林"
},
shareAnim = v3
},
[411480] = {
id = 411480,
effect = true,
name = "腓腓糯",
desc = "绒尾扫愁云，蜷作雪团团",
icon = "CDN:Icon_BU_297",
resourceConf = {
model = "SK_BU_297",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"抱一抱腓腓，攒一日欢喜~"
},
shareAnim = v3,
beginTime = {
seconds = 1744905600
},
suitId = 1134,
suitName = "腓腓糯",
suitIcon = "CDN:Icon_BU_297",
SeasonShowIdList = {
{
key = 13,
value = 2
}
}
},
[411481] = {
id = 411481,
effect = true,
name = "腓腓糯",
desc = "绒尾扫愁云，蜷作雪团团",
icon = "CDN:Icon_BU_297_01",
outlookConf = {
belongTo = 411480,
fashionValue = 25,
belongToGroup = {
411481
}
},
resourceConf = {
model = "SK_BU_297",
material = "MI_BU_297_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12158,
shareTexts = {
"抱一抱腓腓，攒一日欢喜~"
},
shareAnim = v3
},
[411490] = {
id = 411490,
effect = true,
name = "灵芝露露",
desc = "灵芝小仙菇，住在月梢头~",
icon = "CDN:Icon_BU_295",
resourceConf = {
model = "SK_BU_295",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"伞盖一撑，百病不侵～"
},
shareAnim = v3,
beginTime = {
seconds = 1744905600
},
suitId = 1136,
suitName = "灵芝露露",
suitIcon = "CDN:Icon_BU_295",
SeasonShowIdList = {
{
key = 13,
value = 4
}
}
},
[411491] = {
id = 411491,
effect = true,
name = "灵芝露露",
desc = "灵芝小仙菇，住在月梢头~",
icon = "CDN:Icon_BU_295_01",
outlookConf = {
belongTo = 411490,
fashionValue = 25,
belongToGroup = {
411491
}
},
resourceConf = {
model = "SK_BU_295",
material = "MI_BU_295_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12160,
shareTexts = {
"伞盖一撑，百病不侵～"
},
shareAnim = v3
},
[411500] = {
id = 411500,
effect = true,
name = "人参娃娃",
desc = "三百年长一寸，三千年才学会撒娇~",
icon = "CDN:Icon_BU_298",
resourceConf = {
model = "SK_BU_298",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"人参娃娃？要叫我参姐姐！"
},
shareAnim = v3,
beginTime = {
seconds = 1744905600
},
suitId = 1138,
suitName = "人参娃娃",
suitIcon = "CDN:Icon_BU_298",
SeasonShowIdList = {
{
key = 13,
value = 5
}
}
},
[411501] = {
id = 411501,
effect = true,
name = "人参娃娃",
desc = "三百年长一寸，三千年才学会撒娇~",
icon = "CDN:Icon_BU_298_01",
outlookConf = {
belongTo = 411500,
fashionValue = 25,
belongToGroup = {
411501
}
},
resourceConf = {
model = "SK_BU_298",
material = "MI_BU_298_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12162,
shareTexts = {
"人参娃娃？要叫我参姐姐！"
},
shareAnim = v3
},
[411510] = {
id = 411510,
effect = true,
name = "当康穗穗",
desc = "在稻草堆旁打盹，发间稻穗沙沙地歌唱",
icon = "CDN:Icon_BU_299",
resourceConf = {
model = "SK_BU_299",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"见之则穰？不，见之则揉捏~"
},
shareAnim = v3,
beginTime = v4,
suitId = 1140,
suitName = "当康穗穗",
suitIcon = "CDN:Icon_BU_299"
},
[411511] = {
id = 411511,
effect = true,
name = "当康穗穗",
desc = "在稻草堆旁打盹，发间稻穗沙沙地歌唱",
icon = "CDN:Icon_BU_299_01",
outlookConf = {
belongTo = 411510,
fashionValue = 25,
belongToGroup = {
411511
}
},
resourceConf = {
model = "SK_BU_299",
material = "MI_BU_299_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12164,
shareTexts = {
"见之则穰？不，见之则揉捏~"
},
shareAnim = v3
},
[411520] = {
id = 411520,
effect = true,
name = "天狗烬",
desc = "蹲在屋顶装凶神，却会送吓哭的星宝回家",
icon = "CDN:Icon_BU_300",
resourceConf = {
model = "SK_BU_300",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"专业镇宅，童叟无欺"
},
shareAnim = v3,
beginTime = v4,
suitId = 1142,
suitName = "天狗烬",
suitIcon = "CDN:Icon_BU_300"
},
[411521] = {
id = 411521,
effect = true,
name = "天狗烬",
desc = "蹲在屋顶装凶神，却会送吓哭的星宝回家",
icon = "CDN:Icon_BU_300_01",
outlookConf = {
belongTo = 411520,
fashionValue = 25,
belongToGroup = {
411521
}
},
resourceConf = {
model = "SK_BU_300",
material = "MI_BU_300_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12166,
shareTexts = {
"专业镇宅，童叟无欺"
},
shareAnim = v3
},
[411530] = {
id = 411530,
effect = true,
name = "茶甜甜",
desc = "蹦蹦跳跳哼着歌，藏起一把童话糖果",
icon = "CDN:Icon_BU_305",
resourceConf = {
model = "SK_BU_305",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"茶会规则：饼干分我一半"
},
shareAnim = v3,
beginTime = v4,
suitId = 1144,
suitName = "茶甜甜",
suitIcon = "CDN:Icon_BU_305"
},
[411531] = {
id = 411531,
effect = true,
name = "茶甜甜",
desc = "蹦蹦跳跳哼着歌，藏起一把童话糖果",
icon = "CDN:Icon_BU_305_01",
outlookConf = {
belongTo = 411530,
fashionValue = 25,
belongToGroup = {
411531
}
},
resourceConf = {
model = "SK_BU_305",
material = "MI_BU_305_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12168,
shareTexts = {
"茶会规则：饼干分我一半"
},
shareAnim = v3
},
[411540] = {
id = 411540,
effect = true,
name = "花间猫猫",
desc = "传说拥抱三花猫，会带来好运哦~",
icon = "CDN:Icon_BU_314",
resourceConf = {
model = "SK_BU_314",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"喵呜~摸摸这里，超软的！"
},
shareAnim = v3,
beginTime = v4,
suitId = 1146,
suitName = "花间猫猫",
suitIcon = "CDN:Icon_BU_314"
},
[411541] = {
id = 411541,
effect = true,
name = "花间猫猫",
desc = "传说拥抱三花猫，会带来好运哦~",
icon = "CDN:Icon_BU_314_01",
outlookConf = {
belongTo = 411540,
fashionValue = 25,
belongToGroup = {
411541
}
},
resourceConf = {
model = "SK_BU_314",
material = "MI_BU_314_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12170,
shareTexts = {
"喵呜~摸摸这里，超软的！"
},
shareAnim = v3
},
[411550] = {
id = 411550,
effect = true,
name = "蜜桃邦妮",
desc = "所有难题，都能用一杯兔兔特调来解决",
icon = "CDN:Icon_BU_315",
resourceConf = {
model = "SK_BU_315",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"难过时就捏捏她的兔耳朵"
},
shareAnim = v3,
beginTime = v4,
suitId = 1148,
suitName = "蜜桃邦妮",
suitIcon = "CDN:Icon_BU_315"
},
[411551] = {
id = 411551,
effect = true,
name = "蜜桃邦妮",
desc = "所有难题，都能用一杯兔兔特调来解决",
icon = "CDN:Icon_BU_315_01",
outlookConf = {
belongTo = 411550,
fashionValue = 25,
belongToGroup = {
411551
}
},
resourceConf = {
model = "SK_BU_315",
material = "MI_BU_315_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12172,
shareTexts = {
"难过时就捏捏她的兔耳朵"
},
shareAnim = v3
},
[411560] = {
id = 411560,
effect = true,
name = "像素派派",
desc = "午夜剧场，把月光投影成猫猫的形状",
icon = "CDN:Icon_BU_306",
resourceConf = {
model = "SK_BU_306",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"要投币才能点播哦~"
},
shareAnim = v3,
beginTime = v4,
suitId = 1150,
suitName = "像素派派",
suitIcon = "CDN:Icon_BU_306"
},
[411561] = {
id = 411561,
effect = true,
name = "像素派派",
desc = "午夜剧场，把月光投影成猫猫的形状",
icon = "CDN:Icon_BU_306_01",
outlookConf = {
belongTo = 411560,
fashionValue = 25,
belongToGroup = {
411561
}
},
resourceConf = {
model = "SK_BU_306",
material = "MI_BU_306_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12174,
shareTexts = {
"要投币才能点播哦~"
},
shareAnim = v3
},
[411570] = {
id = 411570,
effect = true,
name = "团尾栗栗",
desc = "怀中藏着几颗松果？不告诉你~",
icon = "CDN:Icon_BU_316",
resourceConf = {
model = "SK_BU_316",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"每一颗坚果都有名字~"
},
shareAnim = v3,
beginTime = v4,
suitId = 1152,
suitName = "团尾栗栗",
suitIcon = "CDN:Icon_BU_316"
},
[411571] = {
id = 411571,
effect = true,
name = "团尾栗栗",
desc = "怀中藏着几颗松果？不告诉你~",
icon = "CDN:Icon_BU_316_01",
outlookConf = {
belongTo = 411570,
fashionValue = 25,
belongToGroup = {
411571
}
},
resourceConf = {
model = "SK_BU_316",
material = "MI_BU_316_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12176,
shareTexts = {
"每一颗坚果都有名字~"
},
shareAnim = v3
},
[411580] = {
id = 411580,
effect = true,
name = "金铃小满",
desc = "左手招财，右手招福，尾巴招美食~",
icon = "CDN:Icon_BU_317",
resourceConf = {
model = "SK_BU_317",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"招财秘诀，限量放送~"
},
shareAnim = v3,
beginTime = v4,
suitId = 1154,
suitName = "金铃小满",
suitIcon = "CDN:Icon_BU_317"
},
[411581] = {
id = 411581,
effect = true,
name = "金铃小满",
desc = "左手招财，右手招福，尾巴招美食~",
icon = "CDN:Icon_BU_317_01",
outlookConf = {
belongTo = 411580,
fashionValue = 25,
belongToGroup = {
411581
}
},
resourceConf = {
model = "SK_BU_317",
material = "MI_BU_317_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12178,
shareTexts = {
"招财秘诀，限量放送~"
},
shareAnim = v3
},
[411590] = {
id = 411590,
effect = true,
name = "铜锈锈",
desc = "昆仑之西镇守结界，连风都不敢擅自穿过",
icon = "CDN:Icon_BU_301",
resourceConf = {
model = "SK_BU_301",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"昆仑山最年轻的守卫"
},
shareAnim = v3,
beginTime = v4,
suitId = 1156,
suitName = "铜锈锈",
suitIcon = "CDN:Icon_BU_301"
},
[411591] = {
id = 411591,
effect = true,
name = "铜锈锈",
desc = "昆仑之西镇守结界，连风都不敢擅自穿过",
icon = "CDN:Icon_BU_301_01",
outlookConf = {
belongTo = 411590,
fashionValue = 25,
belongToGroup = {
411591
}
},
resourceConf = {
model = "SK_BU_301",
material = "MI_BU_301_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12180,
shareTexts = {
"昆仑山最年轻的守卫"
},
shareAnim = v3
},
[411600] = {
id = 411600,
effect = true,
name = "莓莓糖心",
desc = "裙摆飘过时，连风都染上了草莓味的甜",
icon = "CDN:Icon_BU_322",
resourceConf = {
model = "SK_BU_322",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"头上的草莓会成熟掉落吗？"
},
shareAnim = v3,
beginTime = v4,
suitId = 1158,
suitName = "莓莓糖心",
suitIcon = "CDN:Icon_BU_322"
},
[411601] = {
id = 411601,
effect = true,
name = "莓莓糖心",
desc = "裙摆飘过时，连风都染上了草莓味的甜",
icon = "CDN:Icon_BU_322_01",
outlookConf = {
belongTo = 411600,
fashionValue = 25,
belongToGroup = {
411601
}
},
resourceConf = {
model = "SK_BU_322",
material = "MI_BU_322_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12182,
shareTexts = {
"头上的草莓会成熟掉落吗？"
},
shareAnim = v3
},
[411610] = {
id = 411610,
effect = true,
name = "魔绒斑比",
desc = "藏好棉花絮，今夜是无害小鹿",
icon = "CDN:Icon_BU_324",
resourceConf = {
model = "SK_BU_324",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"用角钩走你的噩梦，怕吗？"
},
shareAnim = v3,
beginTime = v4,
suitId = 1160,
suitName = "魔绒斑比",
suitIcon = "CDN:Icon_BU_324"
},
[411611] = {
id = 411611,
effect = true,
name = "魔绒斑比",
desc = "藏好棉花絮，今夜是无害小鹿",
icon = "CDN:Icon_BU_324_01",
outlookConf = {
belongTo = 411610,
fashionValue = 25,
belongToGroup = {
411611
}
},
resourceConf = {
model = "SK_BU_324",
material = "MI_BU_324_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12184,
shareTexts = {
"用角钩走你的噩梦，怕吗？"
},
shareAnim = v3
},
[411620] = {
id = 411620,
effect = true,
name = "猫奈奈",
desc = "衣裙够飒，猫耳够软，反差萌才是王道",
icon = "CDN:Icon_BU_318",
resourceConf = {
model = "SK_BU_318",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"现在流行甜酷猫系少女！"
},
shareAnim = v3,
beginTime = v4,
suitId = 1162,
suitName = "猫奈奈",
suitIcon = "CDN:Icon_BU_318"
},
[411621] = {
id = 411621,
effect = true,
name = "猫奈奈",
desc = "衣裙够飒，猫耳够软，反差萌才是王道",
icon = "CDN:Icon_BU_318_01",
outlookConf = {
belongTo = 411620,
fashionValue = 25,
belongToGroup = {
411621
}
},
resourceConf = {
model = "SK_BU_318",
material = "MI_BU_318_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12186,
shareTexts = {
"现在流行甜酷猫系少女！"
},
shareAnim = v3
},
[411630] = {
id = 411630,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "昆仑守护神 白泽",
desc = "此身愿做昆仑雪，散入千门万户灯",
icon = "CDN:Icon_OG_048",
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_048",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_048_Physics",
skeletalMesh = "SK_OG_048"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_048_PV/Level_OG_048_Intact",
outIdle = "AS_CH_OutIdle_001_OG_048",
outShow = "AS_CH_IdleShow_OG_048",
outShowIntervalTime = 20,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_048_Idle",
outEnterSequence = "LS_PV_OG_048",
shareTexts = {
"许我凡心，阅尽山河"
},
shareAnim = v3,
beginTime = {
seconds = 1744905600
},
suitStoryTextKey = [[昆仑山巅，白雪纷飞。白泽立于云端，远眺着破碎的山河。作为昆仑最后的守护神，她能感受到天地间正在崩塌的秩序。昆仑之心的碎裂，让五行失衡，更让她引以为傲的预知能力变得一片混沌。然而，正是这混沌让她察觉到了一丝不寻常——五行失衡的背后，似乎隐藏着某种阴谋。

“若直接以神力修复，或许会落入陷阱。”白泽轻语，眼中闪过警惕。她决定亲自下凡，以凡身经历，寻找真相。

她褪去神光，化作一位白发女子，踏入凡间。金域战火肆虐，她以智慧调解，发现神秘势力挑拨；木域藤蔓吞天，她以耐心平衡，察觉禁术作祟；水域洪灾泛滥，她以巧思疏导，发现源头被破坏；火域焦土连绵，她以冷静抚平，揭露地脉被抽取；土域山崩地裂，她以坚韧重筑，找到幕后黑手。

每解决一域危机，她的神力便消逝一分，但智慧与洞察愈发深邃。她发现，这一切源于被封印的混沌之灵，企图利用五行失衡毁灭昆仑之境。最终，白泽集齐五行灵核，重铸昆仑之心，并以凡躯对抗混沌之灵，将其重新封印。

她站在昆仑山巅，看着重归安宁的世界，嘴角漾起欣慰的笑容。“用凡人之躯守护这片天地，或许比做一位高高在上的神明更有意义。”她轻声说着，转身走入凡间。

从此，民间流传着白发女子的传说。她用智慧而非神力化解灾难，以柔情而非威严抚平伤痛。很少有人知道，这位游走凡间的女子，正是那个用神格换来天地安宁的昆仑守护神。

夜幕降临时，一道白影常在山巅出现，遥望昆仑之心，温柔微笑。那是白泽，一个选择以凡心守护万物的神明。]],
suitId = 1164,
suitName = "昆仑守护神 白泽",
suitIcon = "CDN:Icon_OG_048",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1,
SeasonShowIdList = {
{
key = 13,
value = 3
}
}
},
[411631] = {
id = 411631,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "昆仑守护神 白泽",
desc = "此身愿做昆仑雪，散入千门万户灯",
icon = "CDN:Icon_OG_048_01",
outlookConf = {
belongTo = 411630,
fashionValue = 125,
belongToGroup = {
411631,
411632
}
},
resourceConf = {
model = "SK_OG_048",
material = "MI_OG_048_1_HP01;MI_OG_048_2_HP01;MI_OG_048_3_HP01;MI_OG_048_4_HP01;MI_OG_048_5_HP01;MI_OG_048_6_HP01;MI_OG_048_7_HP01;MI_OG_048_8_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_048_Physics",
materialSlot = "Skin;Skin_Opaque_02;Skin_Translucent_01;Skin_Opaque_03;Skin_Translucent_02;Skin_Opaque_04;Skin_Translucent_03;Skin_Opaque_05",
skeletalMesh = "SK_OG_048"
},
commodityId = 12188,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_048_PV/Level_OG_048_Intact",
outIdle = "AS_CH_OutIdle_001_OG_048",
outShow = "AS_CH_IdleShow_OG_048_HP01",
outShowIntervalTime = 20,
soundId = {
4099,
4098
},
outIdle_pv = "LS_PV_OG_048_HP01_Idle",
outEnterSequence = "LS_PV_OG_048_HP01",
shareTexts = {
"许我凡心，阅尽山河"
},
shareAnim = v3,
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030",
bodytype = 2,
firstNoSkip = 1
},
[405260] = {
id = 405260,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "二头身测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_998",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_998_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405261] = {
id = 405261,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "二头身测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_999",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_999_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405271] = {
id = 405271,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "9999测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_9999",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_9999_Physics",
skeletalMesh = "SK_PL_9999"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
},
billboardOffsetZ = 50,
bodytype = 2
},
[405272] = {
id = 405272,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "外接骨骼测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_PL_9998",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_9998_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
},
billboardOffsetZ = 50
},
[405280] = {
id = 405280,
effect = true,
name = "农场外卖员",
desc = "测试测试",
icon = "CDN:Icon_BU_335",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "/Game/Feature/Farm/Assets/Cook/Employee/BU_335/Mesh/SK_BU_335.SK_BU_335",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"测试测试"
},
shareAnim = v3
}
}

local mt = {
effect = false,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data