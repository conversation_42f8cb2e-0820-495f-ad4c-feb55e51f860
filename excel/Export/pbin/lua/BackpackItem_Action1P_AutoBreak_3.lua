--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_动作.xlsx: 单人动作

local v0 = {
{
itemId = 6,
itemNum = 35
}
}

local v1 = 3

local data = {
[720719] = {
id = 720719,
name = "爱意传递",
desc = "感受到我的满满爱意了吗？",
icon = "CDN:Icon_Farm_001",
movementConf = {
action = "AS_CH_Encourage_001_Montage",
assetType = 2,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720720] = {
id = 720720,
name = "拒绝陌生糖",
desc = "我可不会随便吃来路不明的糖果",
icon = "CDN:Icon_Refuse_0001",
movementConf = {
action = "AS_CH_refuse_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720721] = {
id = 720721,
name = "猴哥亮相",
desc = "嗯？有妖气！",
icon = "CDN:Icon_MonkeyKing_001",
movementConf = {
action = "AS_CH_houge_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720722] = {
id = 720722,
name = "凭栏望远",
desc = "看好戏咯~",
icon = "CDN:Icon_RelyingOn_001",
movementConf = {
action = "AS_CH_Leisure_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720723] = {
id = 720723,
exceedReplaceItem = v0,
quality = 3,
name = "抖肩舞",
desc = "哇哦，你跳的好棒！",
icon = "CDN:Icon_ShakeShouder_001",
movementConf = {
action = "AS_CH_crazy_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720724] = {
id = 720724,
exceedReplaceItem = v0,
quality = 3,
name = "你和我",
desc = "遇见你的我，元气又可爱！",
icon = "CDN:Icon_You_001",
movementConf = {
action = "AS_CH_Dance_YOU_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720725] = {
id = 720725,
exceedReplaceItem = v0,
quality = 3,
name = "二年级快乐",
desc = "我是喜欢跳舞和嘻哈的星宝",
icon = "CDN:Icon_StudentDance_001",
movementConf = {
action = "AS_CH_Dance_Ernianjiwudao_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720726] = {
id = 720726,
name = "星光发射",
desc = "你就是最亮的星！",
icon = "CDN:Icon_LoveShoot_001",
movementConf = {
action = "AS_CH_aixinfashe_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720727] = {
id = 720727,
name = "召唤花瓣雨",
desc = "为你们献上最美好的祝福",
icon = "CDN:Icon_ScatterFlowers_001",
movementConf = {
action = "AS_CH_Splashing_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720728] = {
id = 720728,
name = "闪耀星秀",
desc = "只要是我想的，统统可以实现",
icon = "CDN:Icon_HandsDance_001",
movementConf = {
action = "AS_CH_shouguangwu_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720729] = {
id = 720729,
name = "优雅乘凉",
desc = "不管气温几度，心里只有风度",
icon = "CDN:Icon_Lying_001",
movementConf = {
action = "AS_CH_Lying_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
},
resourceConf = {
bForbidPhysicalAnimation = true
}
},
[720730] = {
id = 720730,
exceedReplaceItem = v0,
quality = 3,
name = "这可不兴说",
desc = "这是可以随便说的吗？",
icon = "CDN:Icon_DontTalk_001",
movementConf = {
action = "AS_CH_xu_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720731] = {
id = 720731,
name = "头号粉丝",
desc = "我的灯牌永远为你闪耀",
icon = "CDN:Icon_CrazyCall_001",
movementConf = {
action = "AS_CH_dacall_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720732] = {
id = 720732,
name = "梦游星海",
desc = "不管了，先游上一圈再说",
icon = "CDN:Icon_Weightlessness_001",
movementConf = {
action = "AS_CH_weightlessness_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720733] = {
id = 720733,
name = "龙胆现世",
desc = "战魂不灭！仁者，无敌！",
icon = "CDN:Icon_ZhaoYun_001",
movementConf = {
action = "AS_CH_longdan_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720734] = {
id = 720734,
name = "花期如约",
desc = "一捧春色关不住",
icon = "CDN:Icon_WenJi_001",
movementConf = {
action = "AS_CH_huaqi_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720735] = {
id = 720735,
name = "追逃游戏",
desc = "哼，发现你了！",
icon = "CDN:Icon_AnQi_001",
movementConf = {
action = "AS_CH_zhuitao_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720736] = {
id = 720736,
exceedReplaceItem = v0,
quality = 3,
name = "还我小北鼻",
desc = "我不管，你还我小北鼻",
icon = "CDN:Icon_Nose_001",
movementConf = {
action = "AS_CH_Dance_Xiaobeibi_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720737] = {
id = 720737,
exceedReplaceItem = v0,
quality = 3,
name = "可爱回旋",
desc = "今天要带我去哪玩呢？",
icon = "CDN:Icon_Lolita_001",
movementConf = {
action = "AS_CH_LoliJump_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720738] = {
id = 720738,
exceedReplaceItem = v0,
quality = 3,
name = "白日梦想家",
desc = "诶......我刚得的冠军奖杯呢？",
icon = "CDN:Icon_Dream_001",
movementConf = {
action = "AS_CH_Daydream_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720739] = {
id = 720739,
name = "大大弹球",
desc = "有没有可能这也是一种运动",
icon = "CDN:Icon_Balloon_001",
movementConf = {
action = "AS_CH_BalloonSitting_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720740] = {
id = 720740,
name = "神秘配方",
desc = "加入神秘配方，这次一定能成功",
icon = "CDN:Icon_Medicine_001",
movementConf = {
action = "AS_CH_Alchemy_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720741] = {
id = 720741,
name = "睡梦仙子",
desc = "梦里梦外，又有什么不同呢？",
icon = "CDN:Icon_SleepWell_001",
movementConf = {
action = "AS_CH_SleepingElf_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720742] = {
id = 720742,
name = "复苏祈愿",
desc = "给我一点阳光，还你一片花海",
icon = "CDN:Icon_Grow_001",
movementConf = {
action = "AS_CH_PrayToGrow_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720743] = {
id = 720743,
name = "认真思考",
desc = "我该先迈左脚，还是先迈右脚呢？",
icon = "CDN:Icon_Thinking_001",
movementConf = {
action = "AS_CH_Thinker_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720744] = {
id = 720744,
exceedReplaceItem = v0,
quality = 3,
name = "大事不好了",
desc = "糟糕糟糕，我的点心不见了",
icon = "CDN:Icon_Nervious_001",
movementConf = {
action = "AS_CH_HurryAttack_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720745] = {
id = 720745,
exceedReplaceItem = v0,
quality = 3,
name = "训练有素",
desc = "这点小训练，根本难不倒我",
icon = "CDN:Icon_StandStraight_001",
movementConf = {
action = "AS_CH_RestAndAttention_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720746] = {
id = 720746,
exceedReplaceItem = v0,
quality = 3,
name = "向右看齐",
desc = "优秀的星宝，时刻都能做到最好",
icon = "CDN:Icon_Align_001",
movementConf = {
action = "AS_CH_ToTheRight_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720747] = {
id = 720747,
name = "甩葱舞",
desc = "故事要从一根大葱说起",
icon = "CDN:Icon_Onion_001",
movementConf = {
action = "AS_CH_ThrowTheOnions_001_Montage",
assetType = 2,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720748] = {
id = 720748,
name = "清洁能手",
desc = "别光看着，快夸夸我",
icon = "CDN:Icon_Sweep_001",
movementConf = {
action = "AS_CH_SweepTheFloor_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
},
resourceConf = {
bForbidPhysicalAnimation = true
}
},
[720749] = {
id = 720749,
exceedReplaceItem = v0,
quality = 3,
name = "运动舞步",
desc = "天天五分钟，减脂又轻松",
icon = "CDN:Icon_CoolDance_001",
movementConf = {
action = "AS_CH_CoolDance_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720750] = {
id = 720750,
exceedReplaceItem = v0,
quality = 3,
name = "转圈登场",
desc = "谁还没点小才艺呢？",
icon = "CDN:Icon_TurnAround_001",
movementConf = {
action = "AS_CH_TripleTurnPose_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720751] = {
id = 720751,
exceedReplaceItem = v0,
quality = 3,
name = "流行律动",
desc = "在我的字典里，没有和丧有关的词",
icon = "CDN:Icon_JumpJump_001",
movementConf = {
action = "AS_CH_BouncingDance_001_Montage",
assetType = 2,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720752] = {
id = 720752,
exceedReplaceItem = v0,
quality = 3,
name = "身手了得",
desc = "不过是基本的本能反应而已",
icon = "CDN:Icon_Evade_001",
movementConf = {
action = "AS_CH_DangerDodge_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720753] = {
id = 720753,
name = "热情搏击",
desc = "我要默默地变强，然后一飞冲天",
icon = "CDN:Icon_BoxingAction_001",
movementConf = {
action = "AS_CH_Boxing_002_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720754] = {
id = 720754,
name = "亮出金身",
desc = "闹够了就赶紧收手吧",
icon = "CDN:Icon_Reading_001",
movementConf = {
action = "AS_CH_formula_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720755] = {
id = 720755,
exceedReplaceItem = v0,
quality = 3,
name = "秀新鞋子",
desc = "你怎么知道我买了一双新鞋子？",
icon = "CDN:Icon_Jio_001",
movementConf = {
action = "AS_CH_dance_onlyJio_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720756] = {
id = 720756,
exceedReplaceItem = v0,
quality = 3,
name = "脑瓜子疼",
desc = "本宝宝为什么得想这种问题",
icon = "CDN:Icon_Upset_001",
movementConf = {
action = "AS_CH_Distress_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720757] = {
id = 720757,
exceedReplaceItem = v0,
quality = 3,
name = "许个愿吧",
desc = "我的愿望是......噢，不能说",
icon = "CDN:Icon_PrayForYou_001",
movementConf = {
action = "AS_CH_pray_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720758] = {
id = 720758,
name = "挚爱之约",
desc = "天空的孩子不该住在笼子里",
icon = "CDN:Icon_TrueLove_001",
movementConf = {
action = "AS_CH_Sunce_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720759] = {
id = 720759,
name = "绛天战甲",
desc = "为共同的明天而战！",
icon = "CDN:Icon_Armor_001",
movementConf = {
action = "AS_CH_kai_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720760] = {
id = 720760,
exceedReplaceItem = v0,
quality = 3,
name = "拥抱太阳",
desc = "成为一切美好事物的守护者吧！",
icon = "CDN:Icon_Sun_001",
movementConf = {
action = "AS_CH_praiseTheSun_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720761] = {
id = 720761,
exceedReplaceItem = v0,
quality = 3,
name = "向着太阳",
desc = "希望我能给大家带来一些阳光",
icon = "CDN:Icon_SunFlower_001",
movementConf = {
action = "AS_CH_sunflower_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720762] = {
id = 720762,
exceedReplaceItem = v0,
quality = 3,
name = "一脸懵",
desc = "对星发誓，这次真的不关我的事",
icon = "CDN:Icon_Puzzle_001",
movementConf = {
action = "AS_CH_Confuser_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720763] = {
id = 720763,
name = "在叫我吗",
desc = "啊?真的让我来？",
icon = "CDN:Icon_MeAgain_001",
movementConf = {
action = "AS_CH_WhyAlwaysMe_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720764] = {
id = 720764,
name = "一怒之下",
desc = "我一怒之下怒了一下",
icon = "CDN:Icon_Ground_001",
movementConf = {
action = "AS_CH_SmashTheGround_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720765] = {
id = 720765,
exceedReplaceItem = v0,
quality = 3,
name = "降龙一式",
desc = "你若不循章法，在下也略懂拳法",
icon = "CDN:Icon_FifhtEnemy_001",
movementConf = {
action = "AS_CH_CatchTheEnemy_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720766] = {
id = 720766,
exceedReplaceItem = v0,
quality = 3,
name = "伏虎二式",
desc = "我可是练过的，不要逼我出手",
icon = "CDN:Icon_FifhtEnemy_002",
movementConf = {
action = "AS_CH_CatchTheEnemy_002",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720767] = {
id = 720767,
name = "送个大月饼",
desc = "祝你的生活像月饼一样幸福圆满",
icon = "CDN:Icon_MoonCake_001",
movementConf = {
action = "AS_CH_MooncakeExpress_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720768] = {
id = 720768,
name = "给你花花",
desc = "只有最鲜艳的花，才配得上最棒的你",
icon = "CDN:Icon_Flowers_001",
movementConf = {
action = "AS_CH_GiveYouFlowers_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720769] = {
id = 720769,
name = "望明月",
desc = "但愿人长久，千里共婵娟",
icon = "CDN:Icon_MoonLight_001",
movementConf = {
action = "AS_CH_LookAtTheMoon_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720770] = {
id = 720770,
name = "倒地不起",
desc = "年轻就是好，倒头就睡",
icon = "CDN:Icon_ImDead_001",
movementConf = {
action = "AS_CH_FakeDeath_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
},
resourceConf = {
bForbidPhysicalAnimation = true
}
},
[720771] = {
id = 720771,
exceedReplaceItem = v0,
quality = 3,
name = "可爱歪头",
desc = "嗯，你对我的想法有什么意见吗？",
icon = "CDN:Icon_Tilt_001",
movementConf = {
action = "AS_CH_TiltHead_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720772] = {
id = 720772,
name = "单人华尔兹",
desc = "这就叫做遗世独立的优雅",
icon = "CDN:Icon_Waltz_001",
movementConf = {
action = "AS_CH_Waltz_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720773] = {
id = 720773,
exceedReplaceItem = v0,
quality = 3,
name = "平沙落雁式",
desc = "这个故事告诉我们要脚踏实地",
icon = "CDN:Icon_FlyToSky_001",
movementConf = {
action = "AS_CH_Launch_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720774] = {
id = 720774,
exceedReplaceItem = v0,
quality = 3,
name = "嗨翻全场",
desc = "把我的快乐用舞步传递给大家",
icon = "CDN:Icon_High_001",
movementConf = {
action = "AS_CH_GetHigh_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720775] = {
id = 720775,
name = "八音盒玩偶",
desc = "喜欢的话，我可以转上一整天",
icon = "CDN:Icon_MusicBox_001",
movementConf = {
action = "AS_CH_MusicBoxDance_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720776] = {
id = 720776,
name = "灵动之舞",
desc = "情感于舞蹈中绽放",
icon = "CDN:Icon_GongSunLi_001",
movementConf = {
action = "AS_CH_jiyizhixin_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720777] = {
id = 720777,
name = "初次公演",
desc = "羞涩的初次公演，要支持我哟！",
icon = "CDN:Icon_ZhaoJun_001",
movementConf = {
action = "AS_CH_geshou_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720778] = {
id = 720778,
name = "排球游戏",
desc = "为什么还不来陪我玩呢？",
icon = "CDN:Icon_Volleyball_001",
movementConf = {
action = "AS_CH_popmart_002",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720779] = {
id = 720779,
name = "钓鱼大师",
desc = "小意思，我还能钓到更大的",
icon = "CDN:Icon_Fishing_001",
movementConf = {
action = "AS_CH_popmart_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720780] = {
id = 720780,
exceedReplaceItem = v0,
quality = 3,
name = "抱拳",
desc = "您的技术令在下好生佩服",
icon = "CDN:Icon_Hugup_001",
movementConf = {
action = "AS_CH_FistEtiquette_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720781] = {
id = 720781,
name = "发型不能乱",
desc = "发型第一，比赛第二",
icon = "CDN:Icon_BlowHair_001",
movementConf = {
action = "AS_CH_BlowTheHair_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720782] = {
id = 720782,
exceedReplaceItem = {
{
itemId = 3556,
itemNum = 5
}
},
name = "捡到宝了",
desc = "行云流水般地收入囊中",
icon = "CDN:Icon_PickMoney_001",
movementConf = {
action = "AS_CH_PickMoney_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720783] = {
id = 720783,
exceedReplaceItem = v0,
quality = 3,
name = "轻松扭扭",
desc = "啦啦啦，这次的冠军稳了",
icon = "CDN:Icon_TwistHips_001",
movementConf = {
action = "AS_CH_HipDance_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720784] = {
id = 720784,
exceedReplaceItem = v0,
quality = 3,
name = "欢乐交叉步",
desc = "表达开心最好的方式就是跳舞",
icon = "CDN:Icon_CrossDance_001",
movementConf = {
action = "AS_CH_CrossDance_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720785] = {
id = 720785,
exceedReplaceItem = v0,
quality = 3,
name = "悠闲摇晃",
desc = "想象自己是一朵无忧无虑的小花",
icon = "CDN:Icon_ShakeIt_001",
movementConf = {
action = "AS_CH_LeisureTime_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
},
resourceConf = {
bForbidPhysicalAnimation = true
}
},
[720786] = {
id = 720786,
exceedReplaceItem = v0,
quality = 3,
name = "汪汪可爱摇",
desc = "各位宝宝们快和我一起来",
icon = "CDN:Icon_BellDance_001",
movementConf = {
action = "AS_CH_Dance_Senbeitiaowu_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720787] = {
id = 720787,
exceedReplaceItem = v0,
quality = 3,
name = "焦点热舞",
desc = "我就是变化多端，难以捉摸",
icon = "CDN:Icon_Drama_001",
movementConf = {
action = "AS_CH_Dance_DRAMA_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720788] = {
id = 720788,
exceedReplaceItem = v0,
quality = 3,
name = "大神求带",
desc = "我可不是见到谁都会拜的",
icon = "CDN:Icon_QiuLaoTian_001",
movementConf = {
action = "AS_CH_Dance_Qiulaotian_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720789] = {
id = 720789,
exceedReplaceItem = v0,
quality = 3,
name = "社牛见面舞",
desc = "告别社恐，远离内向",
icon = "CDN:Icon_XiaoXinDance_001",
movementConf = {
action = "AS_CH_Dance_Xiaoxinwu_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720790] = {
id = 720790,
name = "庆典快乐",
desc = "扇舞庆典，齐享欢乐",
icon = "CDN:Icon_NationalDance_001",
movementConf = {
action = "AS_CH_SongOfHeart_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720791] = {
id = 720791,
name = "一掌劈飞",
desc = "什么东西？吃我一掌！",
icon = "CDN:Icon_WuHuang_002",
movementConf = {
action = "AS_CH_wuhuang_002",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720792] = {
id = 720792,
name = "举白旗",
desc = "地心引力太强，我只能趴了",
icon = "CDN:Icon_Surrender_001",
movementConf = {
action = "AS_CH_TouXiang_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
},
resourceConf = {
bForbidPhysicalAnimation = true
}
},
[720793] = {
id = 720793,
exceedReplaceItem = {
{
itemId = 3556,
itemNum = 5
}
},
name = "真香",
desc = "很害羞，吃饱了也不停筷，一直吃",
icon = "CDN:Icon_Eating_001",
movementConf = {
action = "AS_CH_ChiFan_001",
assetType = 1,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720794] = {
id = 720794,
name = "跪伏",
desc = "雨点飘飘，北风萧萧",
icon = "CDN:Icon_IceRain_001",
movementConf = {
action = "AS_CH_gdly_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720795] = {
id = 720795,
exceedReplaceItem = v0,
quality = 3,
name = "好累哦",
desc = "今天收了20个草莓，好累，感觉要晕倒了",
icon = "CDN:Icon_Frail_001",
movementConf = {
action = "AS_CH_BeingSoft_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720796] = {
id = 720796,
name = "舞步轻俏",
desc = "心情好就摇摇摆摆！",
icon = "CDN:Icon_NiuNiuCute_001",
movementConf = {
action = "AS_CH_PrettyAndTwisted_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720797] = {
id = 720797,
exceedReplaceItem = v0,
quality = 3,
name = "劈叉",
desc = "小小劈叉，拿捏",
icon = "CDN:Icon_Splits_001",
movementConf = {
action = "AS_CH_DoTheSpilts_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720798] = {
id = 720798,
exceedReplaceItem = v0,
quality = 3,
name = "舞动青春",
desc = "我就是最闪亮的明日之星",
icon = "CDN:Icon_HappyDance_001",
movementConf = {
action = "AS_CH_HappyDance_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720799] = {
id = 720799,
name = "高抬腿训练",
desc = "跟着音乐的节奏1、2、1、2",
icon = "CDN:Icon_HighKnee_001",
movementConf = {
action = "AS_CH_RaiseLegNodHead_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720800] = {
id = 720800,
name = "吹风",
desc = "吹吹新风，带来幸运~",
icon = "CDN:Icon_Fan_001",
movementConf = {
action = "AS_CH_EnjoyTheFan_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720801] = {
id = 720801,
exceedReplaceItem = v0,
quality = 3,
name = "恳求",
desc = "拜托拜托，就这一次！",
icon = "CDN:Icon_Beg_001",
movementConf = {
action = "AS_CH_BegHard_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720802] = {
id = 720802,
exceedReplaceItem = v0,
quality = 3,
name = "快乐舞蹈",
desc = "脖子扭扭，屁股扭扭",
icon = "CDN:Icon_NiuNiuDance_001",
movementConf = {
action = "AS_CH_TwistDance_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720803] = {
id = 720803,
name = "变身",
desc = "变身，魔法出击",
icon = "CDN:Icon_Spirit_001",
movementConf = {
action = "AS_CH_BabalaTransform_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720804] = {
id = 720804,
name = "呼唤群星",
desc = "群星啊，听从我的召唤！",
icon = "CDN:Icon_Prayers_001",
movementConf = {
action = "AS_CH_QunXing_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720805] = {
id = 720805,
name = "星夜圆舞曲",
desc = "与群星来一场华尔兹",
icon = "CDN:Icon_PlanetDance_001",
movementConf = {
action = "AS_CH_XingXing_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720806] = {
id = 720806,
name = "宇宙起源",
desc = "先这样再这样，新世界就诞生了",
icon = "CDN:Icon_Prayers_002",
movementConf = {
action = "AS_CH_XingQiu_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720807] = {
id = 720807,
name = "干饭步伐",
desc = "遵守食堂秩序，谁也别想插队",
icon = "CDN:Icon_Axe_001",
movementConf = {
action = "AS_CH_KungFuShow_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
},
resourceConf = {
bForbidPhysicalAnimation = true,
suitPhysicalAnimationWhiteList = {
"410050",
"410051",
"410052",
"410320"
}
}
},
[720808] = {
id = 720808,
exceedReplaceItem = v0,
quality = 3,
name = "怀旧迪斯科",
desc = "在歌声与舞蹈中永远年轻",
icon = "CDN:Icon_CrazyDance_001",
movementConf = {
action = "AS_CH_TiZhouWu_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720809] = {
id = 720809,
exceedReplaceItem = v0,
quality = 3,
name = "圆周摇",
desc = "整个世界都跟着摇起来了",
icon = "CDN:Icon_ShakingDance_001",
movementConf = {
action = "AS_CH_PuTongYao_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720810] = {
id = 720810,
exceedReplaceItem = v0,
quality = 3,
name = "空中筋斗",
desc = "一个筋斗十万八千里",
icon = "CDN:Icon_FlyRotate_001",
movementConf = {
action = "AS_CH_YuanDiQiTiao_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720811] = {
id = 720811,
exceedReplaceItem = v0,
quality = 3,
name = "耍宝舞",
desc = "没谁可以忍住五秒还不笑",
icon = "CDN:Icon_PigDance_001",
movementConf = {
action = "AS_CH_ZhuZhuWu_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720812] = {
id = 720812,
exceedReplaceItem = v0,
quality = 3,
name = "打气舞",
desc = "加油，千万别在最后关头放弃",
icon = "CDN:Icon_DuckDance_001",
movementConf = {
action = "AS_CH_YaYaWu_001_Montage",
assetType = 2,
isLoop = true,
actionType = 1,
needFriendIntimate = 0
}
},
[720813] = {
id = 720813,
name = "发射火箭",
desc = "我的一小步，航天的一大步",
icon = "CDN:Icon_Rocket_001",
movementConf = {
action = "AS_CH_XiaoHuoJian_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720814] = {
id = 720814,
exceedReplaceItem = v0,
quality = 3,
name = "箭无虚发",
desc = "心中有靶，百发百中",
icon = "CDN:Icon_Archery_001",
movementConf = {
action = "AS_CH_ZhuangSheJian_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720815] = {
id = 720815,
exceedReplaceItem = v0,
quality = 3,
name = "灵巧躲避",
desc = "还好我身手敏捷",
icon = "CDN:Icon_JumpBack_001",
movementConf = {
action = "AS_CH_XiangHouTiao_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720816] = {
id = 720816,
exceedReplaceItem = v0,
quality = 3,
name = "闪电连击",
desc = "这就是我压箱底的绝学",
icon = "CDN:Icon_BoxDancing_001",
movementConf = {
action = "AS_CH_DaQuanWu_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720817] = {
id = 720817,
name = "必胜姿态",
desc = "当我摆出这个姿势，你已经输了",
icon = "CDN:Icon_Rock_001",
movementConf = {
action = "AS_CH_YaoGunWu_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
},
[720818] = {
id = 720818,
name = "丝滑转圈",
desc = "太开心了，不由自主转了一圈",
icon = "CDN:Icon_Rotate_002",
movementConf = {
action = "AS_CH_Sihua_001",
assetType = 1,
isLoop = false,
actionType = 1,
needFriendIntimate = 0
}
}
}

local mt = {
type = "ItemType_Action1P",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 2
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data