--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_称号.xlsx: 称号

local data = {
[850600] = {
id = 850600,
lowVer = "1.3.68.1",
quality = 3,
name = "峡谷明日星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_191",
picture = "CDN:T_Designation_Img_191",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1736697600
}
},
[850650] = {
id = 850650,
lowVer = "1.3.88.1",
quality = 2,
name = "超能学园",
desc = "外观图鉴收集对应套装获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746115200
}
},
[850651] = {
id = 850651,
lowVer = "1.3.88.1",
quality = 2,
name = "梦想无垠",
desc = "动态称号，奶油乐园奇遇祈愿活动获得",
icon = "CDN:Icon_Designation_Img_223",
picture = "D_Titlebox_223",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1746288000
}
},
[850652] = {
id = 850652,
exceedReplaceItem = {
{
itemId = 3556,
itemNum = 5
}
},
lowVer = "1.3.88.1",
quality = 2,
name = "幻彩调律",
desc = "动态称号，幻彩调律祈愿活动获得",
icon = "CDN:Icon_Designation_Img_224",
picture = "D_Titlebox_224",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1747324800
}
},
[850653] = {
id = 850653,
quality = 2,
name = "拯救喵星人",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746547200
}
},
[850654] = {
id = 850654,
lowVer = "1.3.88.1",
quality = 1,
desc = "动态称号“我绘尽万物”，幻彩调律祈愿获得",
icon = "CDN:Icon_Designation_Img_227",
picture = "D_Titlebox_227",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1747324800
}
},
[850655] = {
id = 850655,
lowVer = "1.3.88.1",
quality = 1,
name = "动感猫猫",
desc = "幻音喵境祈愿活动获得",
icon = "CDN:Icon_Designation_Img_228",
picture = "D_Titlebox_228",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1749657600
}
},
[850656] = {
id = 850656,
lowVer = "1.3.88.1",
quality = 1,
name = "霓虹喵喵机",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_229",
picture = "T_Designation_Img_229",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1748448000
}
},
[850657] = {
id = 850657,
lowVer = "1.3.88.1",
quality = 1,
name = "喵喵次元袋",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_230",
picture = "T_Designation_Img_230",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1748448000
}
},
[850658] = {
id = 850658,
quality = 2,
name = "一起喵喵喵",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1748707200
}
},
[850659] = {
id = 850659,
lowVer = "1.3.88.1",
quality = 2,
name = "蝶舞花间",
desc = "动态称号，蝶舞花间祈愿活动获得",
icon = "CDN:Icon_Designation_Img_215",
picture = "D_Titlebox_215",
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1749139200
}
},
[850660] = {
id = 850660,
quality = 2,
name = "卓大王",
desc = "外观图鉴收集对应套装获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1748620800
}
},
[850661] = {
id = 850661,
quality = 3,
name = "为你撑腰",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:TIcon_Designation_Img_002",
showInView = 0,
isDynamic = 0
},
[850662] = {
id = 850662,
lowVer = "1.3.88.1",
quality = 2,
name = "超能激荡",
desc = "S13通行证达到指定等级获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746115200
}
},
[850663] = {
id = 850663,
lowVer = "1.3.88.1",
quality = 1,
name = "超能学园",
desc = "S13赛季限时活动获得",
icon = "CDN:Icon_Designation_Img_222",
picture = "CDN:T_Designation_Img_222",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1746115200
}
},
[850664] = {
id = 850664,
lowVer = "1.3.99.1",
quality = 2,
name = "山海风雅客",
desc = "通过赛季时尚分任务获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1751040000
}
},
[850665] = {
id = 850665,
lowVer = "1.3.99.1",
quality = 1,
name = "昆仑自在仙",
desc = "通过赛季时尚分任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1751040000
}
},
[850666] = {
id = 850666,
lowVer = "1.3.99.1",
quality = 1,
name = "小小文曲星",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_236",
picture = "CDN:T_Designation_Img_236",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1751558400
}
},
[850667] = {
id = 850667,
lowVer = "1.3.99.1",
quality = 1,
name = "小小艺术家",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_237",
picture = "CDN:T_Designation_Img_237",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1751558400
}
},
[850668] = {
id = 850668,
lowVer = "1.3.99.1",
quality = 1,
name = "小小科学家",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_235",
picture = "CDN:T_Designation_Img_235",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1751558400
}
},
[850669] = {
id = 850669,
lowVer = "1.3.99.1",
quality = 1,
name = "全能智慧星",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_234",
picture = "CDN:T_Designation_Img_234",
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1751558400
}
},
[850670] = {
id = 850670,
lowVer = "1.3.99.1",
quality = 2,
name = "昆仑秘境",
desc = "外观图鉴收集对应套装获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1750953600
}
}
}

local mt = {
type = "ItemType_Title",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 4,
itemNum = 10
}
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data