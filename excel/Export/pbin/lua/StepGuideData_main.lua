--com.tencent.wea.xlsRes.table_StepGuideData => excel/xls/X_新手引导表_主玩法.xlsx: 新引导步骤

local v0 = 2

local v1 = 3

local v2 = "UILobbyView"

local v3 = 1002

local v4 = "{X=-0, Y =0}"

local v5 = "clicked"

local v6 = 1001

local v7 = 1005

local v8 = "4"

local data = {
[1] = {
StepID = 1,
GuideID = 10015,
Comment = "点击开始按钮",
StepType = 3,
TypeParams = "{delay = 0}",
WindowName = "UI_GameplaySelection_View",
WidgetName = "w_btn_Start",
GetButtonFunc = "GetGuideStartBtn",
UIStyle_Third = 1002,
UIOffset_Third = v4,
bOtherButtonExit = true,
ButtonMode = v5
},
[2] = {
StepID = 2,
GuideID = 10200,
Comment = "完成引导",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[3] = {
StepID = 3,
GuideID = 10210,
Comment = "点击模式按钮（弱引导）",
WindowName = v2,
WidgetName = "w_btn_ModelSelect",
ButtonName = "w_btn_ModelSelect",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-80, Y = 80}",
UIStyle_Second = 1005,
UIText_Second = "点击这里还有<Highlight31>更多玩法</>噢",
UIOffset_Second = "{X=-226, Y = 172}",
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[4] = {
StepID = 4,
GuideID = 10210,
Comment = "新手模式选择界面-引导第一个未玩过的玩法",
SubStep = 2,
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = "UI_GameplaySelection_View",
GetWidgetFunc = "GetListItemForFirstNotPlayedMatch",
GetButtonFunc = "GetListItemForFirstNotPlayedMatch",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "试试推荐的玩法吧！",
UIOffset_Second = "{X=-320, Y = -300}",
ButtonMode = v5,
bTriggerOnlyUIShowOnTop = true
},
[5] = {
StepID = 5,
GuideID = 10210,
Comment = "新手模式选择界面-开始游戏",
SubStep = 3,
WindowName = "UI_GameplaySelection_View",
WidgetName = "w_btn_Start",
ButtonName = "w_btn_Start",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-300, Y =-50}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "赶紧开始吧！",
UIOffset_Second = "{X=-830, Y = -50}",
ButtonMode = v5,
bOtherButtonComplete = true,
bTriggerOnlyUIShowOnTop = true
},
[6] = {
StepID = 6,
GuideID = 11404,
Comment = "点击更多玩法",
WindowName = "UI_GameplaySelection_View",
WidgetName = "w_overlay_functionMore",
ButtonName = "w_btn_FunctionMore",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-150, Y = -200}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "这里还有更多有趣的玩法！",
UIOffset_Second = "{X=-650, Y = -250}",
ButtonMode = v5
},
[7] = {
StepID = 7,
GuideID = 11404,
Comment = "等待打开界面",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_Model_MainView\"}",
IsForceGuide = true,
FinishEvent = "ON_FINISH_OPEN_MODE_VIEW_IN_MODE_GUIDE",
UIStyle_Frist = 2005,
UIText_Frist = "乐园里项目新奇多变，玩法层出不穷！快选择你最爱的玩法模式吧！",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[8] = {
StepID = 8,
GuideID = 11404,
Comment = "引导玩家切换模式",
SubStep = 3,
FinishEvent = "ON_FINISH_SWITCH_MODE_IN_MODE_GUIDE",
WindowName = "UI_Model_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "乐园里项目新奇多变，玩法层出不穷！快选择你最爱的玩法模式吧！",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5
},
[9] = {
StepID = 9,
GuideID = 10220,
Comment = "匹配后取消引导",
StepType = 5,
TypeParams = "{conditions = {c_isBegnningMatch = {}},  isTrue = 2, isFalse = 4}",
ButtonMode = v5
},
[10] = {
StepID = 10,
GuideID = 10220,
Comment = "点击开始按钮（弱引导）",
SubStep = 2,
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = v2,
WidgetName = "w_btn_StartGame",
ButtonName = "w_btn_StartGame",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 60}",
UIStyle_Third = 2006,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "点击<Highlight31>开始</>，试一下你刚刚选择的玩法吧！",
UIOffset_Second = "{X=-60, Y = 180}",
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[11] = {
StepID = 11,
GuideID = 10220,
Comment = "等待玩家关卡结束",
SubStep = 3,
StepType = 3,
TypeParams = "{delay = 1,autoEnd = true}",
IsKeyStep = true,
ButtonMode = v5
},
[12] = {
StepID = 12,
GuideID = 10211,
Comment = "完成引导",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[13] = {
StepID = 13,
GuideID = 10212,
Comment = "完成引导-保底",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[14] = {
StepID = 14,
GuideID = 10213,
Comment = "强制进入新手模式选择场景",
StepType = 8,
TypeParams = "{actionId = 12}",
IsKeyStep = true,
ButtonMode = v5
},
[15] = {
StepID = 15,
GuideID = 19997,
Comment = "永远不完成",
StepType = 3,
TypeParams = "{delay = 0}",
bAnyWhereNext = true,
ButtonMode = v5
},
[16] = {
StepID = 16,
GuideID = 19998,
Comment = "完成引导",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[17] = {
StepID = 17,
GuideID = 19999,
Comment = "完成引导",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[18] = {
StepID = 18,
GuideID = 10101,
Comment = "欢迎进入关卡",
StepType = 2,
TypeParams = "{sequence = \"LS_Intro_LevelIntroduction\",  isAuto = true, isBindingCharacter = true, talkData = { BeginFrame = 169, EndFrame = 372, ShowTipFrame = 310, LoopFrameBegin = 300, LoopFrameEnd = 372 } , SfxID=3515, clickSkip = true }",
IsForceGuide = true,
ShowUIList = "{UI_InLevelPartyControlPad= {} , UI_InLevel= {}}",
UIStyle_Frist = 1011,
UIText_Frist = "来来来，教你几个小绝招，助你在乐园里一路畅通，玩遍所有精彩！",
bAnyWhereNext = true,
ButtonMode = v5
},
[19] = {
StepID = 19,
GuideID = 10101,
Comment = "关卡介绍",
SubStep = 2,
StepType = 2,
TypeParams = "{sequence = \"LS_Intro_LevelIntroduction\",  isAuto = true, isBindingCharacter = true, talkData = { BeginFrame = 373, EndFrame = 672, ShowTipFrame = 672, LoopFrameBegin = 672, LoopFrameEnd = 744 } , SfxID=3516, clickSkip = true }",
IsForceGuide = true,
ShowUIList = "{UI_InLevelPartyControlPad= {} , UI_InLevel= {}}",
UIStyle_Frist = 1011,
UIText_Frist = "只要顺利到达终点，就能进入乐园啦！",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[20] = {
StepID = 20,
GuideID = 10102,
Comment = "显示出移动摇杆",
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {4}}",
ButtonMode = v5
},
[21] = {
StepID = 21,
GuideID = 10102,
Comment = "显示特效",
SubStep = 2,
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_Move\"}"
},
[22] = {
StepID = 22,
GuideID = 10102,
Comment = "移动到目标点",
SubStep = 3,
StepType = 6,
TypeParams = "Newcomer_Move_Finish",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "w_Guide_Joystick",
UIStyle_Frist = 1015,
UIOffset_Frist = "{X=-20, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "推动摇杆，走向目标点",
UIOffset_Second = "{X=-200, Y = -450}",
IsKeyStep = true,
ButtonMode = v5
},
[23] = {
StepID = 23,
GuideID = 10103,
Comment = "显示出跳跃按钮",
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {1,4}}",
ButtonMode = "pressed"
},
[24] = {
StepID = 24,
GuideID = 10103,
Comment = "跳跃引导1",
SubStep = 2,
StepType = 6,
TypeParams = "Newcomer_Jump1_Finish",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonJump",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-20, Y = -40}",
UIStyle_Third = 1003,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击跳跃，跳上台阶",
UIOffset_Second = "{X=-390, Y = -580}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[25] = {
StepID = 25,
GuideID = 10104,
Comment = "跳跃引导2",
StepType = 6,
TypeParams = "Newcomer_Jump2_Finish",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonJump",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-20, Y = -40}",
UIStyle_Third = 1003,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "再次点击，跳过缝隙",
UIOffset_Second = "{X=-390, Y = -580}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[26] = {
StepID = 26,
GuideID = 10105,
Comment = "显示出飞扑按钮",
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {1,2,4}}",
ButtonMode = "pressed"
},
[27] = {
StepID = 27,
GuideID = 10105,
Comment = "跳扑引导-跳",
SubStep = 2,
StepType = 8,
TypeParams = "{actionId = 8}",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonJump",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-20, Y = -40}",
UIStyle_Third = 1003,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击跳跃，在空中点击飞扑",
UIOffset_Second = "{X=-390, Y = -580}",
ButtonMode = "pressed"
},
[28] = {
StepID = 28,
GuideID = 10105,
Comment = "显示出飞扑按钮2",
SubStep = 3,
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {1,2,4}}",
ButtonMode = "pressed"
},
[29] = {
StepID = 29,
GuideID = 10105,
Comment = "跳扑引导-扑",
SubStep = 4,
StepType = 8,
TypeParams = "{actionId = 9}",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonSlot1",
ButtonName = "ButtonSkill",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-10, Y = -20}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击飞扑",
UIOffset_Second = "{X=-130, Y = -610}",
ButtonMode = "pressed"
},
[30] = {
StepID = 30,
GuideID = 10105,
Comment = "等待玩家扑完(并监听事件），到对面完成引导，没到返回1",
SubStep = 5,
StepType = 11,
TypeParams = "{delay = 3,eventName =\"Newcomer_Dive_Finish\" ,isFalse = 1}",
ButtonMode = v5
},
[31] = {
StepID = 31,
GuideID = 10106,
Comment = "介绍大炮",
StepType = 2,
TypeParams = "{sequence = \"LS_Intro_WatchOutLaunchers\", isAuto = true, talkData = { BeginFrame = 0, EndFrame = 270, ShowTipFrame = 50, LoopFrameBegin = 270, LoopFrameEnd = 342 } , SfxID=3517 , clickSkip = true}",
IsForceGuide = true,
ShowUIList = "{UI_InLevelPartyControlPad= {} , UI_InLevel= {}}",
UIStyle_Frist = 1011,
UIText_Frist = "大炮突袭！前方道路危险，躲避障碍物，注意安全！",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[32] = {
StepID = 32,
GuideID = 10107,
Comment = "调整镜头",
StepType = 101,
TypeParams = "Newcomer_AdjustCamera_Over",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonJump",
UIStyle_Frist = 1008,
UIOffset_Frist = "{X=-300, Y = -550}",
UIStyle_Second = 1005,
UIText_Second = "滑动此处调整镜头",
UIOffset_Second = "{X=-350, Y = -700}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[33] = {
StepID = 33,
GuideID = 10108,
Comment = "显示出冲刺按钮",
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {1,2,3,4}}",
ButtonMode = "pressed"
},
[34] = {
StepID = 34,
GuideID = 10108,
Comment = "引导玩家点击冲刺",
SubStep = 2,
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonSlot2",
ButtonName = "ButtonSkill",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-10, Y = -20}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击冲刺",
UIOffset_Second = "{X=-180, Y = -410}",
IsKeyStep = true,
ButtonMode = "pressed"
},
[35] = {
StepID = 35,
GuideID = 10109,
Comment = "介绍台子",
IsForceGuide = true,
ShowUIList = "{UI_InLevelPartyControlPad= {} , UI_InLevel= {}}",
UIStyle_Frist = 1011,
UIText_Frist = "前方高台无法跳过，不用担心，捡起喷气背包一跃而上吧！",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5,
SfxID = 3518
},
[36] = {
StepID = 36,
GuideID = 10110,
Comment = "显示出抓取按钮",
StepType = 8,
TypeParams = "{actionId = 5,funcParams = {1,2,3,4,5, 8}}",
ButtonMode = "pressed"
},
[37] = {
StepID = 37,
GuideID = 10110,
Comment = "删除弹板道具生成器",
SubStep = 2,
StepType = 8,
TypeParams = "{actionId = 10, funcParams={actorTag = \"PropSpawner\"}}",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonSlot3",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-10, Y = -20}",
UIStyle_Second = 1005,
UIText_Second = "点击使用喷气背包",
UIOffset_Second = "{X=-250, Y = -350}",
ButtonMode = v5
},
[38] = {
StepID = 38,
GuideID = 10110,
Comment = "条件选择跳转步骤",
SubStep = 3,
StepType = 5,
TypeParams = "{conditions = {c_isActorExits = {actorTags = {\"PropSpawner\", \"InfinitePropSpawner\"}}}, isTrue = 5 , isFalse = 4}",
IsForceGuide = true,
bMask = true,
ButtonMode = v5
},
[39] = {
StepID = 39,
GuideID = 10110,
Comment = "生成弹板道具",
SubStep = 4,
StepType = 12,
TypeParams = "{delay = 0,eventName =\"ON_SPAWN_SPRINGBOARD\"}",
ButtonMode = v5
},
[40] = {
StepID = 40,
GuideID = 10110,
Comment = "捡弹板道具",
SubStep = 5,
StepType = 13,
TypeParams = "{actionId = 1,ClassName =\"BP_PA_PR_Jetpack_C\"}",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "w_Guide_Joystick",
UIStyle_Frist = 1015,
UIOffset_Frist = "{X=-20, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "前往获得喷气背包",
UIOffset_Second = "{X=-200, Y = -450}",
ButtonMode = v5
},
[41] = {
StepID = 41,
GuideID = 10110,
Comment = "使用弹板道具（判断是否使用完）",
SubStep = 6,
StepType = 13,
TypeParams = "{actionId = 2}",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonSlot3",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-10, Y = -20}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击使用喷气背包",
UIOffset_Second = "{X=-350, Y = -360}",
ButtonMode = v5
},
[42] = {
StepID = 42,
GuideID = 10110,
Comment = "到对面完成引导，没到返回1",
SubStep = 7,
StepType = 11,
TypeParams = "{delay = 3,eventName = \"Newcomer_SpringBoard_Finish\" ,isFalse = 1, isTrue=0}",
IsForceGuide = true,
ButtonMode = v5
},
[43] = {
StepID = 43,
GuideID = 10112,
Comment = "删除滚球道具",
StepType = 8,
TypeParams = "{actionId = 10, funcParams={actorTag = \"BallSpawner\"}}",
ButtonMode = v5
},
[44] = {
StepID = 44,
GuideID = 10112,
Comment = "生成滚球道具",
SubStep = 2,
StepType = 12,
TypeParams = "{delay = 0,eventName =\"ON_SPAWN_HXB\"}",
ButtonMode = v5
},
[45] = {
StepID = 45,
GuideID = 10112,
Comment = "捡滚球道具",
SubStep = 3,
StepType = 13,
TypeParams = "{actionId = 1,ClassName =\"BP_PA_PR_Ball_C\"}",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "w_Guide_Joystick",
UIStyle_Frist = 1015,
UIOffset_Frist = "{X=-20, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "前往获得番茄道具",
UIOffset_Second = "{X=-200, Y = -450}",
ButtonMode = v5
},
[46] = {
StepID = 46,
GuideID = 10112,
Comment = "使用滚球道具",
SubStep = 4,
StepType = 13,
TypeParams = "{actionId = 2}",
WindowName = "UI_InLevelPartyControlPad",
WidgetName = "SkillButtonSlot3",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-10, Y = -20}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击使用番茄道具",
UIOffset_Second = "{X=-350, Y = -360}",
ButtonMode = v5
},
[47] = {
StepID = 47,
GuideID = 10112,
Comment = "判断滚球CD时间是否到",
SubStep = 5,
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
IsKeyStep = true,
ButtonMode = v5
},
[48] = {
StepID = 48,
GuideID = 10114,
Comment = "到达终点sequencer",
StepType = 2,
TypeParams = "{sequence = \"LS_Intro_Destination\",   isAuto = true, isBindingCharacter = true, talkData = { BeginFrame = 0, EndFrame = 213, ShowTipFrame = 30,LoopFrameBegin = 214, LoopFrameEnd =773} , SfxID=3519, clickSkip = true}",
IsForceGuide = true,
ShowUIList = "{UI_InLevelPartyControlPad= {} , UI_InLevel= {}}",
UIStyle_Frist = 1011,
UIText_Frist = "完美通过！简直太棒啦！热身环节对你来说小菜一碟。接下来，尽情享受开心之旅吧！",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[49] = {
StepID = 49,
GuideID = 10002,
Comment = "显示UI、引导抽奖（弱引导）",
StepType = 3,
TypeParams = "{delay =0.5}",
WindowName = v2,
WidgetName = "w_btn_DrawReward",
ButtonName = "w_btn_DrawReward",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "第一次十连抽<Highlight31>免费送</>，赶紧来试试！",
UIOffset_Second = "{X=-320, Y =160}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[50] = {
StepID = 50,
GuideID = 10003,
Comment = "跳转到活跃奖池",
StepType = 8,
TypeParams = "{actionId = 3}",
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
ButtonMode = v5
},
[51] = {
StepID = 51,
GuideID = 10003,
Comment = "没送代币到3，送代币到7",
SubStep = 2,
StepType = 5,
TypeParams = "{conditions = {c_isReward = {}}, isTrue = 7 , isFalse = 3}",
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
ButtonMode = v5
},
[52] = {
StepID = 52,
GuideID = 10003,
Comment = "介绍抽奖店",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "在这里可以获得各种有趣的装扮，真诚许下心愿，说不定就会实现呢！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[53] = {
StepID = 53,
GuideID = 10003,
Comment = "送代币介绍",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "乐园里每天都有好事发生！先送你10000星宝印章的见面礼，祝你心想事成！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[54] = {
StepID = 54,
GuideID = 10003,
Comment = "送代币逻辑",
SubStep = 5,
StepType = 8,
TypeParams = "{actionId = 2}",
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
bMask = true,
ButtonMode = v5
},
[55] = {
StepID = 55,
GuideID = 10003,
Comment = "点击确定",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_Common_PopRewardView",
GetWidgetFunc = "GetCommonConfirmBtn",
GetButtonFunc = "GetCommonConfirmBtn",
ButtonMode = v5
},
[56] = {
StepID = 56,
GuideID = 10003,
Comment = "点击抽奖按钮",
SubStep = 7,
IsForceGuide = true,
WindowName = "UI_Lottery_MainView",
GetWidgetFunc = "GetLotteryBtnWidget_BG",
GetButtonFunc = "GetLotteryBtnWidget",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-80, Y = -230}",
UIStyle_Second = 1005,
UIText_Second = "点击十连抽！",
UIOffset_Second = "{X=-240, Y = -360}",
bMask = true,
IsKeyStep = true,
ButtonMode = v5
},
[57] = {
StepID = 57,
GuideID = 10004,
Comment = "点击背包（弱引导）",
WindowName = v2,
WidgetName = "w_btn_Bag",
ButtonName = "w_btn_Bag",
UIStyle_Frist = 1014,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = -160}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "打开背包，看看刚刚抽到的<Highlight31>皮肤</>吧！",
UIOffset_Second = "{X=-270, Y = -360}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[58] = {
StepID = 58,
GuideID = 10004,
Comment = "点击装扮",
SubStep = 2,
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = "UI_Bag_MainView",
GetWidgetFunc = "GetTabMakeUpItemAtOne_BG",
GetButtonFunc = "GetTabMakeUpItemAtOne",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "点击穿戴",
UIOffset_Second = "{X=-225, Y = 180}",
ButtonMode = v5,
GetWidgetFuncParams = "{itemId = 510262}",
GetButtonFuncParams = "{itemId = 510262}"
},
[59] = {
StepID = 59,
GuideID = 10004,
Comment = "等待装扮加载, 显示保存搭配按钮",
SubStep = 3,
StepType = 13,
TypeParams = "{actionId=3, funcParams={windowName=\"UI_Bag_MainView\", getWidgetFunc=\"GetTabMakeUpItemBtnSave\"}}",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-50, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "点击保存搭配",
UIOffset_Second = "{X=-350, Y = -320}",
ButtonMode = v5
},
[60] = {
StepID = 60,
GuideID = 10004,
Comment = "保存搭配",
SubStep = 4,
WindowName = "UI_Bag_MainView",
GetWidgetFunc = "GetTabMakeUpItemBtnSave",
GetButtonFunc = "GetTabMakeUpItemBtnSave",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-50, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "点击保存搭配",
UIOffset_Second = "{X=-350, Y = -320}",
ButtonMode = v5
},
[61] = {
StepID = 61,
GuideID = 10011,
Comment = "匹配后取消引导",
StepType = 5,
TypeParams = "{conditions = {c_isBegnningMatch = {}},  isTrue = 2, isFalse = 4}",
ButtonMode = v5
},
[62] = {
StepID = 62,
GuideID = 10011,
Comment = "点击开始按钮（弱引导）",
SubStep = 2,
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = v2,
WidgetName = "w_btn_StartGame",
ButtonName = "w_btn_StartGame",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 60}",
UIStyle_Third = 2006,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "点击<Highlight31>开始</>，试一下你刚刚选择的玩法吧！",
UIOffset_Second = "{X=-60, Y = 180}",
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[63] = {
StepID = 63,
GuideID = 10011,
Comment = "等待玩家关卡结束",
SubStep = 3,
StepType = 3,
TypeParams = "{delay = 1,autoEnd = true}",
IsKeyStep = true,
ButtonMode = v5
},
[64] = {
StepID = 64,
GuideID = 10006,
Comment = "介绍排位积分",
IsForceGuide = true,
WindowName = "UI_InLevelFinalQualifying",
WidgetName = "w_image_guide_score",
UIStyle_Frist = 2005,
UIText_Frist = "名次越靠前，获得的积分越多，段位升的越快哦！",
UIOffset_Frist = "{X=-10, Y = -5}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5
},
[65] = {
StepID = 65,
GuideID = 10006,
Comment = "介绍段位奖励",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_InLevelFinalQualifying",
WidgetName = "w_image_guide_pic",
UIStyle_Frist = 2005,
UIText_Frist = "努力提升段位，追求更高的荣誉吧！还有<Highlight31>超棒奖励</>等着你~",
UIOffset_Frist = "{X=-10, Y = -5}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[66] = {
StepID = 66,
GuideID = 10007,
Comment = "引导玩家点击任务（弱引导）",
WindowName = v2,
WidgetName = "w_btn_Task",
ButtonName = "w_btn_Task",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "<Highlight31>免费</>送各种新手福利，快来看看吧！",
UIOffset_Second = "{X=-250, Y = 160}",
bOtherButtonExit = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[67] = {
StepID = 67,
GuideID = 10007,
Comment = "等待界面打开",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_Task_Main\"}",
IsKeyStep = true,
ButtonMode = v5
},
[68] = {
StepID = 68,
GuideID = 10007,
Comment = "介绍新手7日活动",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_Task_Main",
UIStyle_Frist = 2005,
UIText_Frist = "新星指南中每日都会更新活动，完成所有打卡即可领取<Highlight31>潮流时装</>哦~记得每天都要来看看哦！",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[69] = {
StepID = 69,
GuideID = 10009,
Comment = "点击星世界",
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = v2,
WidgetName = "w_overlay_StarWorld",
ButtonName = "w_btn_UGC",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "星世界里还有<Highlight31>更多玩法</>，快来看看吧！",
UIOffset_Second = "{X=-300, Y = 160}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[70] = {
StepID = 70,
GuideID = 10008,
Comment = "点击开始按钮",
WindowName = v2,
WidgetName = "w_btn_StartGame",
ButtonName = "w_btn_StartGame",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 60}",
UIStyle_Third = 2006,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "来一场全新比赛吧！",
UIOffset_Second = "{X=-60, Y = 180}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[71] = {
StepID = 71,
GuideID = 10010,
Comment = "点击星世界",
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = v2,
WidgetName = "w_overlay_StarWorld",
ButtonName = "w_btn_UGC",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "来星世界的闯关挑战看看吧!",
UIOffset_Second = "{X=-300, Y = 160}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[72] = {
StepID = 72,
GuideID = 10014,
Comment = "点击开始按钮",
StepType = 3,
TypeParams = "{delay = 0}",
WindowName = v2,
WidgetName = "w_btn_StartGame",
ButtonName = "w_btn_StartGame",
UIStyle_Third = 2006,
UIOffset_Third = v4,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[73] = {
StepID = 73,
GuideID = 10018,
Comment = "点击进入农场",
StepType = 17,
TypeParams = "{delay=8}",
WindowName = v2,
WidgetName = "w_btn_Farmyard",
ButtonName = "w_btn_Farmyard",
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[74] = {
StepID = 74,
GuideID = 10019,
Comment = "大厅引导回到农场",
WindowName = v2,
WidgetName = "w_btn_Farmyard",
ButtonName = "w_btn_Farmyard",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击这里，可以返回农场哦",
UIOffset_Second = "{X=-250, Y = -250}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[75] = {
StepID = 75,
GuideID = 18001,
Comment = "段位详情界面",
IsForceGuide = true,
WindowName = "UI_PlayerInfo",
WidgetName = "w_size_Dun",
ButtonName = "w_btn_CloseTips",
UIStyle_Third = 1003,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "可以点击此处打开段位设置界面，选择自己喜欢的玩法段位进行展示！",
UIOffset_Second = "{X=-300, Y = -240}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[76] = {
StepID = 76,
GuideID = 18002,
Comment = "点击跳跃按钮",
StepType = 16,
TypeParams = "{delay=3}",
WindowName = "UI_InLevelControlPad",
WidgetName = "SkillButtonJump",
ButtonName = "ButtonSkill",
UIStyle_Frist = 1016,
UIText_Frist = v8,
UIOffset_Frist = "{X=-20, Y = -40}",
UIStyle_Third = 1003,
UIOffset_Third = "{X=0, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "点击跳跃",
UIOffset_Second = "{X=-390, Y = -580}",
ButtonMode = "pressed"
},
[77] = {
StepID = 77,
GuideID = 10116,
Comment = "竖屏聊天引导（弱引导）",
WindowName = "UI_NewChat_Main",
WidgetName = "w_btn_Change",
ButtonName = "w_btn_Change",
UIStyle_Frist = 1001,
UIText_Frist = "5",
UIOffset_Frist = "{X=50, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "试试竖屏进行聊天吧",
UIOffset_Second = "{X=100, Y = -250}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5
},
[78] = {
StepID = 78,
GuideID = 10116,
Comment = "返回横屏",
SubStep = 3,
WindowName = "UI_NewChat_Vertical",
WidgetName = "w_btn_Change",
ButtonName = "w_btn_Change",
UIStyle_Frist = 1001,
UIText_Frist = "7",
UIOffset_Frist = "{X=50, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "再次点击可返回横屏聊天界面",
UIOffset_Second = "{X=100, Y = 100}",
bOtherButtonExit = true,
ButtonMode = v5
},
[79] = {
StepID = 79,
GuideID = 10301,
Comment = "点击短剧按钮",
StepType = 3,
TypeParams = "{delay = 0.5}",
WindowName = v2,
WidgetName = "w_btn_QQVideo",
ButtonName = "w_btn_QQVideo",
UIStyle_Frist = 1014,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = -160}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "元梦里可以看短剧啦！",
UIOffset_Second = "{X=-180, Y = -360}",
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[80] = {
StepID = 80,
GuideID = 10500,
Comment = "活动引导_半周年走格子活动_首次进入",
IsForceGuide = true,
WindowName = "UI_Activity_FlyingChess_Base_MainView",
WidgetName = "w_btn_Draw",
ButtonName = "w_btn_Draw",
UIStyle_Frist = 1014,
UIOffset_Frist = "{X=-50, Y = -160}",
UIStyle_Second = 1005,
UIText_Second = "点击前进，向前走随机步数，并获得途经格子的所有奖励",
UIOffset_Second = "{X=-240, Y = -360}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[81] = {
StepID = 81,
GuideID = 10501,
Comment = "活动引导_半周年走格子活动_首次获得碎片奖励",
IsForceGuide = true,
WindowName = "UI_Activity_FlyingChess_Base_MainView",
WidgetName = "w_btn_ProgressGuide",
ButtonName = "w_btn_ProgressGuide",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-50, Y = 60}",
UIStyle_Second = 1005,
UIText_Second = "收集100张服装手稿，即可获得最终大奖",
UIOffset_Second = "{X=-60, Y = 180}",
bAnyWhereNext = true,
bOtherButtonExit = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[82] = {
StepID = 82,
GuideID = 10601,
Comment = "引导玩家点击奖杯征程（弱引导）",
WindowName = v2,
GetWidgetFunc = "GetCupHudOpenButton",
GetButtonFunc = "GetCupHudOpenButton",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "【奖杯征程】系统开启，星宝们可点击前往查看啦！",
UIOffset_Second = "{X=-250, Y = 160}",
bOtherButtonExit = true,
ButtonMode = v5
},
[83] = {
StepID = 83,
GuideID = 10601,
Comment = "等待【奖杯征程】界面打开",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_Cup_Bg\"}",
FinishEvent = "ON_NEWCOMER_SUB_STEP_UI_CUP_VIEW_OPEN",
IsKeyStep = true,
ButtonMode = v5
},
[84] = {
StepID = 84,
GuideID = 10601,
Comment = "点击介绍【奖杯征程】主轴（弱引导）",
SubStep = 3,
IsForceGuide = true,
FinishEvent = "ON_NEWCOMER_SUB_STEP_TRIGGER_UI_MAIN_AXIS_STEP",
WindowName = "UI_Cup_Bg",
WidgetName = "w_image_guide",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=130, Y =-330}",
UIStyle_Second = 1005,
UIText_Second = "参与奖杯任务、部分玩法等获取奖杯，累计奖杯征程进度值，领取丰厚奖励！",
UIOffset_Second = "{X=-50, Y =-430}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[85] = {
StepID = 85,
GuideID = 10601,
Comment = "点击介绍【奖杯任务】（弱引导）",
SubStep = 4,
WindowName = "UI_Cup_Bg",
GetWidgetFunc = "GetTaskTabBtn",
GetButtonFunc = "GetTaskTabBtn",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "点击这里即可查看【奖杯任务】，去完成任务获取奖杯吧！",
UIOffset_Second = "{X=-175, Y = 180}",
bMask = true,
ButtonMode = v5
},
[86] = {
StepID = 86,
GuideID = 10701,
Comment = "皮肤租借卡教学引导",
WindowName = v2,
GetWidgetFunc = "GetButtonInvite",
GetButtonFunc = "GetButtonInvite",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=0, Y = 50}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=60, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "和亲密关系好友组队时可以向其借用时装哦~",
UIOffset_Second = "{X=100, Y = 0}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[87] = {
StepID = 87,
GuideID = 10801,
Comment = "广场大厅引导进组队秀大厅",
WindowName = v2,
WidgetName = "w_btn_Interface",
ButtonName = "w_btn_Interface",
UIStyle_Frist = 1016,
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Second = 1005,
UIText_Second = "组队界面全新升级，快来试试吧！",
UIOffset_Second = "{X=-250, Y = 160}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[88] = {
StepID = 88,
GuideID = 10901,
Comment = "介绍闪电赛的奖章",
IsForceGuide = true,
WindowName = "UI_InLevelFinalAccount",
GetWidgetFunc = "GetLightningMedalWidget",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y = 90}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "根据名次获得奖章，连胜有额外奖励！",
UIOffset_Second = "{X=-105, Y =180}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5
},
[89] = {
StepID = 89,
GuideID = 10901,
Comment = "介绍闪电赛规则",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_InLevelFinalAccount",
UIStyle_Frist = 2005,
UIText_Frist = "快速对局，畅爽无极限！丰富的主题关卡等你继续体验哦~",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[90] = {
StepID = 90,
GuideID = 10901,
Comment = "介绍闪电赛再来一局",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_InLevelFinalAccount",
WidgetName = "w_switcher_Again",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-70, Y =-230}",
UIStyle_Second = 1005,
UIText_Second = "闪电再开局、快乐不停歇！",
UIOffset_Second = "{X=-250, Y =-330}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[91] = {
StepID = 91,
GuideID = 10510,
Comment = "活动引导_新界奇遇_首次进入",
IsForceGuide = true,
WindowName = "UI_Events_Main",
GetWidgetFunc = "GetGuideBtn_ThemeAdventure",
GetButtonFunc = "GetGuideBtn_ThemeAdventure",
UIStyle_Frist = 1014,
UIOffset_Frist = "{X=-50, Y = -160}",
UIStyle_Second = 1005,
UIText_Second = "点击切换玩法任务",
UIOffset_Second = "{X=-240, Y = -360}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[92] = {
StepID = 92,
GuideID = 10903,
Comment = "再来一局引导（弱引导）",
WindowName = "UI_InLevelFinalAccount",
GetWidgetFunc = "GetBattleTogetherGuidBtn",
GetButtonFunc = "GetBattleTogetherGuidBtn",
UIStyle_Frist = 1014,
UIText_Frist = "可以和队友再来一局哦",
UIOffset_Frist = "{X=-70, Y = -160}",
UIStyle_Second = 1005,
UIText_Second = "可以和队友再来一局哦",
UIOffset_Second = "{X=-250, Y = -360}",
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UI_InLevelFinalAccount;UI_NR3E0_InLevelFinalAccount;UI_DDP_FinalAccount;UI_Arena_FinalAccount"
},
[93] = {
StepID = 93,
GuideID = 11001,
Comment = "组队秀二级页引导返回大厅按钮",
WindowName = "UI_PreviewTeam_View",
WidgetName = "w_btn_ReturnToLobby",
ButtonName = "w_btn_ReturnToLobby",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Second = 1005,
UIText_Second = "点击离开当前页面，但不会退队哦",
UIOffset_Second = "{X=-250, Y = 160}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true,
bTriggerOnlyUIShowOnTop = true
},
[94] = {
StepID = 94,
GuideID = 11101,
Comment = "指引点击个性化按钮",
WindowName = "UI_Bag_MainView",
WidgetName = "w_btn_setting",
ButtonName = "w_btn_Setting",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Second = 1005,
UIText_Second = "点击搭配时装的个性内容",
UIOffset_Second = "{X=-250, Y = 180}",
bOtherButtonExit = true,
ButtonMode = v5,
OtherWindowName = "UI_Bag_MainView",
bTriggerOnlyUIShowOnTop = true
},
[95] = {
StepID = 95,
GuideID = 11101,
Comment = "查看个性化内容",
SubStep = 2,
StepType = 3,
TypeParams = "{delay =0.5}",
WindowName = "UI_OrangeSuit_MainView",
WidgetName = "w_btn_CommonBtn",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = "6",
UIOffset_Frist = "{X=240, Y =170}",
UIStyle_Second = 1005,
UIText_Second = "看看有哪些个性内容吧！",
UIOffset_Second = "{X=150, Y =290}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true,
bTriggerOnlyUIShowOnTop = true
},
[96] = {
StepID = 96,
GuideID = 11102,
Comment = "指引点击个性化按钮",
WindowName = "UI_DrawReward_MallView",
WidgetName = "w_btn_setting",
ButtonName = "w_btn_setting",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Second = 1005,
UIText_Second = "点击预览时装的个性内容",
UIOffset_Second = "{X=-250, Y = 180}",
bOtherButtonExit = true,
ButtonMode = v5,
OtherWindowName = "UI_DrawReward_MallView;UI_Mall_MainView",
bTriggerOnlyUIShowOnTop = true
},
[97] = {
StepID = 97,
GuideID = 11102,
Comment = "查看个性化内容",
SubStep = 2,
StepType = 3,
TypeParams = "{delay =0.5}",
WindowName = "UI_OrangeSuit_MainView",
WidgetName = "w_btn_CommonBtn",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = "6",
UIOffset_Frist = "{X=240, Y =170}",
UIStyle_Second = 1005,
UIText_Second = "看看有哪些个性内容吧！",
UIOffset_Second = "{X=150, Y =290}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true,
bTriggerOnlyUIShowOnTop = true
},
[98] = {
StepID = 98,
GuideID = 11103,
Comment = "点击热购按钮（弱引导）",
StepType = 3,
TypeParams = "{delay =0.5}",
WindowName = v2,
WidgetName = "w_btn_Recharge",
ButtonName = "w_btn_Recharge",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "全新商城上线啦，快来看看吧！",
UIOffset_Second = "{X=-320, Y =160}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView"
},
[99] = {
StepID = 99,
GuideID = 11103,
Comment = "合并介绍",
SubStep = 2,
TypeParams = "{windowName = \"UI_NewMall_MainView\"}",
IsForceGuide = true,
WindowName = "UI_NewMall_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "热购和商城合并啦！让我们一起看看吧！",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[100] = {
StepID = 100,
GuideID = 11103,
Comment = "Banner介绍",
SubStep = 3,
TypeParams = "{windowName = \"UI_NewMall_MainView\"}",
IsForceGuide = true,
WindowName = "UI_NewMall_MainView",
WidgetName = "w_btn_TopShow",
UIStyle_Frist = 2005,
UIText_Frist = "推荐里增加了新窗口，为星宝们推荐最新最潮的新品！",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[101] = {
StepID = 101,
GuideID = 11103,
Comment = "页签介绍",
SubStep = 4,
TypeParams = "{windowName = \"UI_NewMall_MainView\"}",
IsForceGuide = true,
WindowName = "UI_NewMall_MainView",
WidgetName = "w_list_TagList",
UIStyle_Frist = 2005,
UIText_Frist = "原热购和商城的功能全都集中在了这里！",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5
},
[102] = {
StepID = 102,
GuideID = 10005,
Comment = "引导点击新手入口",
IsForceGuide = true,
WindowName = v2,
WidgetName = "w_btn_NoviceReward",
ButtonName = "w_btn_NoviceReward",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "超多<Highlight31>新手福利</>等你来拿，快来看看吧！",
UIOffset_Second = "{X=-300, Y = 150}",
bMask = true,
ButtonMode = v5
},
[103] = {
StepID = 103,
GuideID = 10005,
Comment = "介绍新手界面",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_NoviceReward_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "我们为星宝准备了大量新手福利，快来看看吧！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[104] = {
StepID = 104,
GuideID = 10020,
Comment = "引导点击新手入口",
WindowName = v2,
WidgetName = "w_btn_NoviceReward",
ButtonName = "w_btn_NoviceReward",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1005,
UIText_Second = "超多<Highlight31>新手福利</>等你来拿，快来看看吧！",
UIOffset_Second = "{X=-300, Y = 150}",
ButtonMode = v5
},
[105] = {
StepID = 105,
GuideID = 10020,
Comment = "介绍新手界面",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_NoviceReward_MainView",
UIStyle_Frist = 2005,
UIText_Frist = "我们为星宝准备了大量新手福利，快来看看吧！",
UIOffset_Frist = "{X=0, Y = 0}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[106] = {
StepID = 106,
GuideID = 11201,
Comment = "卡牌集换系统入口引导",
WindowName = v2,
WidgetName = "w_btn_Card",
ButtonName = "w_btn_Card",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-30, Y = -200}",
UIStyle_Second = 1005,
UIText_Second = "卡牌集换玩法上线啦，星宝们快来点击查看，集齐卡册可兑换丰厚奖励哦！",
UIOffset_Second = "{X=-150, Y = -360}",
IsKeyStep = true,
ButtonMode = v5,
OtherWindowName = "UILobbyView;UI_TeamShow_LobbyView",
bTriggerOnlyUIShowOnTop = true
},
[107] = {
StepID = 107,
GuideID = 11205,
Comment = "次留优化专项-实验组A_默认进农场+退出提示",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[108] = {
StepID = 108,
GuideID = 11206,
Comment = "次留优化专项-实验组B_新手玩法选择+退出提示",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v5
},
[109] = {
StepID = 109,
GuideID = 11207,
Comment = "次留优化专项-实验组A和B_退出提示辅助引导（关键步骤为FALSE）",
StepType = 3,
TypeParams = "{delay = 0,autoEnd =true}",
bAnyWhereNext = true,
ButtonMode = v5
},
[110] = {
StepID = 110,
GuideID = 10602,
Comment = "引导玩家点击导航页（弱引导）",
WindowName = v2,
WidgetName = "w_btn_entrance",
ButtonName = "w_btn_entrance",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-142, Y =-166}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "这里是<Highlight31>导航</>，里面有丰富的快捷功能！",
UIOffset_Second = "{X=-480, Y = -284}",
bOtherButtonExit = true,
ButtonMode = v5
},
[111] = {
StepID = 111,
GuideID = 10602,
Comment = "等待导航页界面打开",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_Lobby_NavigationPop\"}",
ButtonMode = v5
},
[112] = {
StepID = 112,
GuideID = 10602,
Comment = "点击介绍快捷、历史记录、收藏（强引导）",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_Lobby_NavigationPop",
WidgetName = "w_Img_farme02",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=0, Y =200}",
UIStyle_Second = 1005,
UIText_Second = "在这里，星宝可以找到常玩或者收藏的玩法。",
UIOffset_Second = "{X=-200, Y =300}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[113] = {
StepID = 113,
GuideID = 10602,
Comment = "点击介绍推荐（强引导）",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Lobby_NavigationPop",
WidgetName = "w_Img_farme01",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=50, Y =200}",
UIStyle_Second = 1005,
UIText_Second = "这里是为星宝精心准备的推荐内容！",
UIOffset_Second = "{X=-150, Y =300}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[114] = {
StepID = 114,
GuideID = 10602,
Comment = "点击介绍系统功能（强引导）",
SubStep = 5,
IsForceGuide = true,
WindowName = "UI_Lobby_NavigationPop",
WidgetName = "w_Img_farme03",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-60, Y =0}",
UIStyle_Second = 1005,
UIText_Second = "这里是一些星宝常用的功能，如拍照、排行榜、扫一扫、设置等。",
UIOffset_Second = "{X=-520, Y =100}",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[115] = {
StepID = 115,
GuideID = 10602,
Comment = "点击介绍设置和改键（强引导）",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_Lobby_NavigationPop",
WidgetName = "w_btn_ShowConfigTab",
ButtonName = "w_btn_ShowConfigTab",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-200, Y = -30}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "设置里可以修改玩法的快捷键，选择自己的个性按键吧！",
UIOffset_Second = "{X=-700, Y = -30}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5
},
[116] = {
StepID = 116,
GuideID = 11210,
Comment = "引导玩家点击更多玩法（弱引导）",
WindowName = "UI_InLevelFinalAccount",
WidgetName = "UI_Common_FinalAccountRecommendMatch",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-350, Y =-50}",
UIStyle_Second = 1005,
UIText_Second = "看看其他有趣的玩法吧！",
UIOffset_Second = "{X=-900, Y =-50}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[117] = {
StepID = 117,
GuideID = 11211,
Comment = "引导玩家点击更多玩法（默认农场）",
WindowName = "UI_InLevelFinalAccount",
WidgetName = "UI_Common_FinalAccountRecommendMatch",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-350, Y =-50}",
UIStyle_Second = 1005,
UIText_Second = "来星宝农场看看吧！",
UIOffset_Second = "{X=-900, Y =-50}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[118] = {
StepID = 118,
GuideID = 10115,
Comment = "显示特效",
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_Move1\"}"
},
[119] = {
StepID = 119,
GuideID = 10115,
Comment = "移动到目标点",
SubStep = 2,
StepType = 6,
TypeParams = "Newcomer_Move_Finish1",
WindowName = "UI_OGC_ControlPad",
WidgetName = "w_Guide_Joystick",
UIStyle_Frist = 1015,
UIOffset_Frist = "{X=-20, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "推动摇杆，跑向终点！",
UIOffset_Second = "{X=-200, Y = -450}",
IsKeyStep = true,
ButtonMode = v5
},
[120] = {
StepID = 120,
GuideID = 11300,
Comment = "拍照引导开始",
TypeParams = "{delay =0.5}",
WindowName = "UI_Photo_Main",
UIStyle_Frist = 2005,
UIText_Frist = "欢迎来到全新的拍照功能，让我们一起看看都有什么改变吧！",
bAnyWhereNext = true,
bOtherButtonExit = true,
bMask = true,
ButtonMode = v5
},
[121] = {
StepID = 121,
GuideID = 11300,
Comment = "介绍镜头旋转",
SubStep = 2,
WindowName = "UI_Photo_Main",
WidgetName = "UI_Photo_Turn2",
UIStyle_Frist = 1003,
UIOffset_Frist = "{X=0, Y = 0}",
UIStyle_Second = 1006,
UIText_Second = "转动轮盘可以调整镜头的旋转，拍出更多精彩角度！",
UIOffset_Second = "{X=-300, Y = 150}",
bAnyWhereNext = true,
bOtherButtonExit = true,
bMask = true,
ButtonMode = v5
},
[122] = {
StepID = 122,
GuideID = 11300,
Comment = "介绍镜头缩放",
SubStep = 3,
WindowName = "UI_Photo_Main",
WidgetName = "w_Horizontal_Slider",
ButtonName = "w_Horizontal_Slider",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=0, Y = -200}",
UIStyle_Second = 1006,
UIText_Second = "滑动滑块可以调整镜头的缩放，抓住每一处细节！",
UIOffset_Second = "{X=0, Y = -350}",
bAnyWhereNext = true,
bOtherButtonExit = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[123] = {
StepID = 123,
GuideID = 11301,
Comment = "点击录像模式按钮（强引导）",
WindowName = "UI_Photo_Main",
WidgetName = "w_btn_Camera",
ButtonName = "w_btn_Camera",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-150, Y = 50}",
UIStyle_Second = 1006,
UIText_Second = "现在让我们来看看如何记录精彩瞬间~",
UIOffset_Second = "{X=-350, Y = 150}",
IsKeyStep = true,
ButtonMode = v5
},
[124] = {
StepID = 124,
GuideID = 11302,
Comment = "引导录制按钮",
WindowName = "UI_Photo_Main",
WidgetName = "w_btn_Video",
ButtonName = "w_btn_Video",
UIStyle_Frist = 1002,
UIOffset_Frist = "{X=0, Y = 0}",
UIStyle_Second = 1006,
UIText_Second = "点击按钮即可开始录制，视频将会保存在本地文件中。",
UIOffset_Second = "{X=-480, Y = 100}",
bAnyWhereNext = true,
bOtherButtonExit = true,
ButtonMode = v5
},
[125] = {
StepID = 125,
GuideID = 11302,
Comment = "拍照引导结束",
SubStep = 2,
TypeParams = "{delay =0.5}",
WindowName = "UI_Photo_Main",
ShowUIList = "{UI_Photo_Main = {\"w_vertical_Left\",\"w_nameSlot_downAdaptArea\",\"w_canvas_Input\"}}",
UIStyle_Frist = 2005,
UIText_Frist = "了解了全新功能后，开始拍摄你的心仪作品吧！",
bAnyWhereNext = true,
bOtherButtonExit = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[126] = {
StepID = 126,
GuideID = 11106,
Comment = "提示新增载具页签（弱引导新号）",
WindowName = "UI_LobbyJoy",
WidgetName = "UI_Lobby_JoyTabMount",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-200, Y = -80}",
UIStyle_Second = 1005,
UIText_Second = "新增了载具页签，可直接召唤和切换载具，等待你的体验！",
UIOffset_Second = "{X=-500, Y = 150}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[127] = {
StepID = 127,
GuideID = 11199,
Comment = "提示新增载具页签",
WindowName = "UI_LobbyJoy",
WidgetName = "UI_Lobby_JoyTabMount",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-200, Y = -80}",
UIStyle_Second = 1005,
UIText_Second = "新增了载具页签，可直接召唤和切换载具，快去背包装配体验吧！",
UIOffset_Second = "{X=-500, Y = 150}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true
},
[128] = {
StepID = 128,
GuideID = 11104,
Comment = "发现系统局后引导（APP端）",
StepType = 3,
TypeParams = "{delay =0.5}",
WindowName = v2,
WidgetName = "w_btn_Activity",
ButtonName = "w_btn_Activity",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
bOtherButtonExit = true,
ButtonMode = v5
},
[129] = {
StepID = 129,
GuideID = 11104,
Comment = "发现系统局后引导（APP端）",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_Events_Main\"}",
IsForceGuide = true,
FinishEvent = "Events_DiscoveryGuidance_LocationFinish",
bAnyWhereNext = true,
bOtherButtonExit = true,
ButtonMode = v5
},
[130] = {
StepID = 130,
GuideID = 11105,
Comment = "发现系统局后引导（小游戏端）",
StepType = 3,
TypeParams = "{delay =0.5}",
WindowName = "UI_WXGame_LobbyView",
GetWidgetFunc = "GetGuideBtn_Activity",
GetButtonFunc = "GetGuideBtn_Activity",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =70}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
bOtherButtonExit = true,
ButtonMode = v5
},
[131] = {
StepID = 131,
GuideID = 11105,
Comment = "发现系统局后引导（小游戏端）",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_Events_Main\"}",
IsForceGuide = true,
FinishEvent = "Events_DiscoveryGuidance_LocationFinish",
bAnyWhereNext = true,
bOtherButtonExit = true,
ButtonMode = v5
},
[132] = {
StepID = 132,
GuideID = 11401,
Comment = "痛包使用引导",
WindowName = v2,
WidgetName = "SkillButtonSlot3",
ButtonName = "SkillButtonSlot3",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -220}",
UIStyle_Second = 1006,
UIText_Second = "点击设置/使用【痛包】，在场景中向其他星宝展示自己精心装饰的包包吧！",
UIOffset_Second = "{X=-430, Y = -330}",
IsKeyStep = true,
ButtonMode = v5
},
[133] = {
StepID = 133,
GuideID = 11402,
Comment = "推图展板使用引导",
WindowName = v2,
WidgetName = "SkillButtonSlot3",
ButtonName = "SkillButtonSlot3",
UIStyle_Frist = 1001,
UIText_Frist = "3",
UIOffset_Frist = "{X=-250, Y = -220}",
UIStyle_Second = 1006,
UIText_Second = "点击设置/使用推图展板，在场景中向其他星宝推荐有趣的地图吧！",
UIOffset_Second = "{X=-430, Y = -330}",
IsKeyStep = true,
ButtonMode = v5
},
[134] = {
StepID = 134,
GuideID = 11410,
Comment = "引导点击主玩法/狼人匹配按钮",
WindowName = "UI_Preparations_View",
WidgetName = "UI_CommonBtn_Start",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Second = 1005,
UIText_Second = "赶紧开始比赛吧！",
UIOffset_Second = "{X=-320, Y = -300}",
IsKeyStep = true,
ButtonMode = v5,
bTriggerOnlyUIShowOnTop = true
},
[135] = {
StepID = 135,
GuideID = 11411,
Comment = "引导点击大王匹配按钮",
WindowName = "UI_UniversalPreparation_Template1",
WidgetName = "UI_UPComponent_MatchState",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Second = 1005,
UIText_Second = "赶紧开始比赛吧！",
UIOffset_Second = "{X=-320, Y = -300}",
IsKeyStep = true,
ButtonMode = v5,
bTriggerOnlyUIShowOnTop = true
},
[136] = {
StepID = 136,
GuideID = 11412,
Comment = "引导点moba匹配按钮",
WindowName = "UI_Arena_Preparations_Main",
WidgetName = "UI_CommonBtn_Start",
ButtonName = "w_btn_CommonBtn",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Second = 1005,
UIText_Second = "赶紧开始比赛吧！",
UIOffset_Second = "{X=-320, Y = -300}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v5,
bOtherButtonComplete = true,
bTriggerOnlyUIShowOnTop = true
},
[137] = {
StepID = 137,
GuideID = 11501,
Comment = "新玩家首先进入广场的AB实验（实验A引导）小红狐开场白",
UIStyle_Frist = 2005,
UIText_Frist = "欢迎来到星梦广场，这里是玩家聚集的热门地，一起愉快的玩耍吧~",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v5
},
[140] = {
StepID = 140,
GuideID = 11501,
Comment = "等待操作摇杆并结束",
SubStep = 2,
StepType = 6,
TypeParams = "ON_MAIN_UI_JOYSTICK_TOUCH_END",
WindowName = v2,
WidgetName = "w_Guide_Joystick",
UIStyle_Frist = 1015,
UIOffset_Frist = "{X=-20, Y = 0}",
UIStyle_Second = 1005,
UIText_Second = "推动摇杆，可以自由移动",
UIOffset_Second = "{X=-200, Y = -450}",
IsKeyStep = true,
ButtonMode = v5
},
[141] = {
StepID = 141,
GuideID = 11502,
Comment = "点击模式按钮（弱引导）",
WindowName = v2,
WidgetName = "w_btn_ModelSelect",
ButtonName = "w_btn_ModelSelect",
UIStyle_Frist = 1001,
UIOffset_Frist = "{X=-80, Y = 80}",
UIStyle_Second = 1005,
UIText_Second = "快来试试<Highlight31>有趣玩法</>吧",
UIOffset_Second = "{X=-226, Y = 172}",
IsKeyStep = true,
ButtonMode = v5
},
[142] = {
StepID = 142,
GuideID = 11502,
Comment = "新手模式选择界面（实验A引导）",
SubStep = 2,
StepType = 3,
TypeParams = "{delay = 1.0}",
WindowName = "UI_GameplaySelection_View",
GetWidgetFunc = "GetListItemForFirstNotPlayedMatch",
GetButtonFunc = "GetListItemForFirstNotPlayedMatch",
UIStyle_Frist = 1001,
UIText_Frist = v8,
UIOffset_Frist = "{X=-50, Y = -180}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "试试推荐的玩法吧！",
UIOffset_Second = "{X=-320, Y = -300}",
ButtonMode = v5,
bTriggerOnlyUIShowOnTop = true
},
[143] = {
StepID = 143,
GuideID = 11502,
Comment = "新手模式选择界面-开始游戏",
SubStep = 3,
WindowName = "UI_GameplaySelection_View",
WidgetName = "w_btn_Start",
ButtonName = "w_btn_Start",
UIStyle_Frist = 1001,
UIText_Frist = "2",
UIOffset_Frist = "{X=-300, Y =-50}",
UIStyle_Third = 1002,
UIOffset_Third = v4,
UIStyle_Second = 1005,
UIText_Second = "赶紧开始吧！",
UIOffset_Second = "{X=-830, Y = -50}",
ButtonMode = v5,
bOtherButtonComplete = true,
bTriggerOnlyUIShowOnTop = true
}
}

local mt = {
SubStep = 1,
StepType = 1,
IsForceGuide = false,
bAnyWhereNext = false,
bOtherButtonExit = false,
bMask = false,
bShowSkipButton = false,
IsKeyStep = false,
bOtherButtonComplete = false,
bTriggerOnlyUIShowOnTop = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data