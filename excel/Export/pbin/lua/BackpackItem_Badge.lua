--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_玩具.xlsx: 吧唧

local data = {
[895000] = {
id = 895000,
name = "小红狐1",
desc = "小红狐1【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank1"
},
[895001] = {
id = 895001,
name = "小红狐2",
desc = "小红狐2【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank2"
},
[895002] = {
id = 895002,
name = "小红狐3",
desc = "小红狐3【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank3"
},
[895003] = {
id = 895003,
name = "紫萝萝1",
desc = "紫萝萝1【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank4"
},
[895004] = {
id = 895004,
name = "紫萝萝2",
desc = "紫萝萝2【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank5"
},
[895005] = {
id = 895005,
name = "好好鸭1",
desc = "好好鸭1【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank6"
},
[895006] = {
id = 895006,
name = "好好鸭2",
desc = "好好鸭2【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank7"
},
[895007] = {
id = 895007,
name = "哈士奇1",
desc = "哈士奇1【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank8"
},
[895008] = {
id = 895008,
name = "哈士奇2",
desc = "哈士奇2【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank9"
},
[895009] = {
id = 895009,
name = "巨鳄霸1",
desc = "巨鳄霸1【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_2_Rank10"
},
[895010] = {
id = 895010,
name = "巨鳄霸2",
desc = "巨鳄霸2【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank1"
},
[895011] = {
id = 895011,
name = "玫珊珊1",
desc = "玫珊珊1【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank2"
},
[895012] = {
id = 895012,
name = "玫珊珊2",
desc = "玫珊珊2【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank3"
},
[895013] = {
id = 895013,
name = "吉伊",
desc = "吉伊【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank4"
},
[895014] = {
id = 895014,
name = "小八",
desc = "小八【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank5"
},
[895015] = {
id = 895015,
name = "乌萨奇",
desc = "乌萨奇【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank6"
},
[895016] = {
id = 895016,
name = "柯南",
desc = "柯南【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank7"
},
[895017] = {
id = 895017,
name = "基德",
desc = "基德【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank8"
},
[895018] = {
id = 895018,
name = "小黑",
desc = "小黑【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank9"
},
[895019] = {
id = 895019,
name = "唐三",
desc = "唐三【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank10"
},
[895020] = {
id = 895020,
name = "小舞",
desc = "小舞【吧唧】描述",
icon = "CDN:T_StarCupIcon_Icon_Rank10"
}
}

local mt = {
type = "ItemType_Common",
stackedNum = 999999,
expiredReplaceItem = {
{
itemId = 6,
itemNum = 1
}
},
quality = 3,
getWay = "活动",
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data