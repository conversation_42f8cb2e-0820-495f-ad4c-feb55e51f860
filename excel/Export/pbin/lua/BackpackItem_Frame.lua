--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 头像框

local v0 = 3

local v1 = 0

local v2 = 1

local data = {
[840001] = {
id = 840001,
quality = 3,
name = "浪漫星球",
icon = "CDN:T_HeadFrame_001",
showInView = 0,
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[840002] = {
id = 840002,
quality = 3,
name = "轻风启航",
icon = "CDN:T_HeadFrame_002",
showInView = 0,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[840003] = {
id = 840003,
quality = 3,
name = "白日梦想家",
desc = "购买幸运币礼包获得",
icon = "CDN:T_HeadFrame_003",
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[840004] = {
id = 840004,
quality = 3,
name = "小小星",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_004",
beginTime = {
seconds = 1702569600
},
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[840005] = {
id = 840005,
quality = 3,
name = "绿野星踪",
icon = "CDN:T_HeadFrame_005",
showInView = 0,
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[840006] = {
id = 840006,
name = "亲密邀约",
desc = "达成特定亲密关系后获得",
icon = "CDN:T_HeadFrame_006",
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[840007] = {
id = 840007,
name = "极夜星粉",
icon = "CDN:T_HeadFrame_007",
showInView = 0,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[840008] = {
id = 840008,
name = "星在路上",
icon = "CDN:T_HeadFrame_008",
showInView = 0,
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[840009] = {
id = 840009,
quality = 3,
name = "巡游新星",
desc = "星世界-星海巡游活动内获得",
icon = "CDN:T_HeadFrame_009",
showInView = 0,
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[840010] = {
id = 840010,
name = "薄荷棒棒糖",
icon = "CDN:T_HeadFrame_010",
showInView = 0,
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[840011] = {
id = 840011,
name = "活力新星",
icon = "CDN:T_HeadFrame_011",
showInView = 0,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[840012] = {
id = 840012,
name = "粉萌甜心",
icon = "CDN:T_HeadFrame_012",
showInView = 0,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[840013] = {
id = 840013,
name = "乐园影星",
icon = "CDN:T_HeadFrame_013",
showInView = 0,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[840014] = {
id = 840014,
name = "晴空翱翔",
icon = "CDN:T_HeadFrame_014",
showInView = 0,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[840015] = {
id = 840015,
name = "阳光海岸",
icon = "CDN:T_HeadFrame_015",
showInView = 0,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[840016] = {
id = 840016,
quality = 3,
name = "大神带飞",
icon = "CDN:T_HeadFrame_016",
showInView = 0,
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[840017] = {
id = 840017,
quality = 3,
name = "招财进宝",
icon = "CDN:T_HeadFrame_017",
showInView = 0,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[840018] = {
id = 840018,
quality = 4,
name = "蜜语冰激凌",
icon = "CDN:T_HeadFrame_018",
showInView = 0,
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[840019] = {
id = 840019,
name = "乐园潮人",
icon = "D_HeadFrame_019",
showInView = 0,
isDynamic = 1,
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[840020] = {
id = 840020,
quality = 3,
name = "梦想之翼",
icon = "CDN:T_HeadFrame_020",
showInView = 0,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[840021] = {
id = 840021,
quality = 3,
name = "人生赢家",
icon = "CDN:T_HeadFrame_021",
showInView = 0,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[840022] = {
id = 840022,
name = "闪耀明星",
desc = "星宝会员卡用户专享",
icon = "D_HeadFrame_022",
isDynamic = 1,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[840023] = {
id = 840023,
name = "神工巧匠",
desc = "造梦之旅活动内获得",
icon = "D_HeadFrame_023",
isDynamic = 1,
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[840024] = {
id = 840024,
quality = 3,
name = "乐园学徒",
desc = "造梦之旅活动内获得",
icon = "CDN:T_HeadFrame_024",
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[840025] = {
id = 840025,
quality = 3,
name = "绿意盎然",
desc = "桃源通行证获得",
icon = "CDN:T_HeadFrame_025",
beginTime = {
seconds = 1702569600
},
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[840026] = {
id = 840026,
quality = 4,
name = "新苗初生",
desc = "桃源通行证获得",
icon = "CDN:T_HeadFrame_026",
beginTime = {
seconds = 1702569600
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[840027] = {
id = 840027,
quality = 3,
name = "妙趣冬日",
icon = "CDN:T_HeadFrame_027",
showInView = 0,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[840028] = {
id = 840028,
quality = 3,
name = "庆典时光",
icon = "CDN:T_HeadFrame_028",
showInView = 0,
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[840029] = {
id = 840029,
quality = 3,
name = "满级小星",
desc = "个人成就获得",
icon = "CDN:T_HeadFrame_029",
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[840030] = {
id = 840030,
quality = 3,
name = "逐梦踏青",
desc = "赛季祈愿-购买星愿币礼包获得",
icon = "CDN:T_HeadFrame_030",
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[840031] = {
id = 840031,
name = "狐影清逸",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_031",
isDynamic = 1,
beginTime = {
seconds = 1702569600
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[840032] = {
id = 840032,
quality = 3,
name = "兔耳乖乖",
desc = "首充获得",
icon = "CDN:T_HeadFrame_032",
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[840033] = {
id = 840033,
name = "森林童话",
icon = "CDN:T_HeadFrame_033",
showInView = 0,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[840034] = {
id = 840034,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
name = "小新",
desc = "盛装小新祈愿获得",
icon = "CDN:T_HeadFrame_034",
beginTime = {
seconds = 1703779200
},
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[840035] = {
id = 840035,
name = "星月神话",
desc = "热购-充值福利获得",
icon = "D_HeadFrame_035",
isDynamic = 1,
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[840036] = {
id = 840036,
name = "粉红蝶梦",
desc = "达成特定亲密关系后获得",
icon = "CDN:T_HeadFrame_036",
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[840037] = {
id = 840037,
name = "友情存档",
desc = "达成特定亲密关系后获得",
icon = "CDN:T_HeadFrame_037",
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[840038] = {
id = 840038,
name = "拳路同行",
desc = "达成特定亲密关系后获得",
icon = "CDN:T_HeadFrame_038",
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[840039] = {
id = 840039,
name = "赛事明星",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:T_HeadFrame_039",
showInView = 0,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[840040] = {
id = 840040,
name = "赛事星冠",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:T_HeadFrame_040",
showInView = 0,
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[840041] = {
id = 840041,
name = "赛事皇冠",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:T_HeadFrame_041",
showInView = 0,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[840042] = {
id = 840042,
quality = 3,
name = "星动造梦",
desc = "内测获奖造梦师专享",
icon = "CDN:T_HeadFrame_042",
showInView = 0,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[840043] = {
id = 840043,
lowVer = "1.2.67.1",
quality = 4,
name = "寒梅朵朵",
desc = "山海通行证获得",
icon = "CDN:T_HeadFrame_043",
beginTime = {
seconds = 1706198400
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[840044] = {
id = 840044,
lowVer = "1.2.67.1",
quality = 3,
name = "雪落梅枝",
desc = "山海通行证获得",
icon = "CDN:T_HeadFrame_044",
beginTime = {
seconds = 1706198400
},
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[840045] = {
id = 840045,
lowVer = "1.2.67.1",
name = "光之战士",
desc = "限时活动获得",
icon = "D_HeadFrame_045",
isDynamic = 1,
beginTime = {
seconds = 1708704000
},
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[840046] = {
id = 840046,
lowVer = "1.2.67.1",
quality = 4,
name = "乘风纸鸢",
desc = "活动预留",
icon = "CDN:T_HeadFrame_046",
showInView = 0,
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[840047] = {
id = 840047,
lowVer = "1.2.67.1",
name = "S2灯火长明",
desc = "冲段挑战-最强元梦星段位获得",
icon = "CDN:T_HeadFrame_047",
beginTime = {
seconds = 1706198400
},
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[840048] = {
id = 840048,
lowVer = "1.2.67.1",
quality = 3,
name = "龙宝庆春",
desc = "活动获得",
icon = "CDN:T_HeadFrame_048",
showInView = 0,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[840049] = {
id = 840049,
lowVer = "1.2.67.1",
name = "暴暴龙",
desc = "活动预留",
icon = "CDN:T_HeadFrame_049",
showInView = 0,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[840050] = {
id = 840050,
lowVer = "1.2.67.1",
name = "奶龙",
desc = "活动获得",
icon = "CDN:T_HeadFrame_050",
beginTime = {
seconds = 1706889600
},
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[840051] = {
id = 840051,
lowVer = "1.2.67.1",
quality = 3,
name = "书写青春",
desc = "活动获得",
icon = "CDN:T_HeadFrame_051",
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[840052] = {
id = 840052,
lowVer = "1.2.67.1",
name = "碧水寒潭",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_052",
isDynamic = 1,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[840053] = {
id = 840053,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.2.80.1",
name = "蝶舞幽梦",
desc = "月夜歌吟祈愿活动获得",
icon = "D_HeadFrame_053",
isDynamic = 1,
beginTime = {
seconds = 1707408000
},
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[840054] = {
id = 840054,
quality = 4,
name = "逐光而行",
desc = "时光通行证获得",
icon = "CDN:T_HeadFrame_053",
beginTime = {
seconds = 1710432000
},
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[840055] = {
id = 840055,
quality = 3,
name = "向阳而生",
desc = "时光通行证获得",
icon = "CDN:T_HeadFrame_054",
beginTime = {
seconds = 1710432000
},
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[840056] = {
id = 840056,
quality = 4,
name = "时间之旅",
desc = "冲段挑战-最强元梦星段位获得",
icon = "CDN:T_HeadFrame_056",
showInView = 0,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[840057] = {
id = 840057,
name = "守护时空",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_057",
isDynamic = 1,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[840058] = {
id = 840058,
quality = 3,
name = "草长莺飞",
desc = "时光漫游—赛季兑换中获得",
icon = "CDN:T_HeadFrame_058",
showInView = 0,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[840059] = {
id = 840059,
name = "神龙大侠",
desc = "功夫熊猫祈愿活动获得",
icon = "D_HeadFrame_059",
isDynamic = 1,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[840060] = {
id = 840060,
name = "时光漫游",
desc = "时光漫游赛季限时活动获得",
icon = "D_HeadFrame_060",
isDynamic = 1,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[840061] = {
id = 840061,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
name = "木偶剧场",
desc = "星光剧场祈愿活动获得",
icon = "D_HeadFrame_064",
isDynamic = 1,
beginTime = {
seconds = 1711728000
},
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[840062] = {
id = 840062,
quality = 3,
name = "功夫巨星",
desc = "热购-特惠礼包限时获得",
icon = "CDN:T_HeadFrame_060",
beginTime = {
seconds = 1711036800
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[840063] = {
id = 840063,
name = "小橘子启航",
desc = "发现-小橘子有礼活动获得",
icon = "D_HeadFrame_063",
isDynamic = 1,
beginTime = {
seconds = 1711641600
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[840071] = {
id = 840071,
quality = 3,
name = "【限时】狼人学家",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_071",
beginTime = {
seconds = 1710432000
},
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[840072] = {
id = 840072,
quality = 3,
name = "【限时】躲猫猫侠",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_072",
beginTime = {
seconds = 1711036800
},
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[840073] = {
id = 840073,
quality = 3,
name = "【限时】飞车大神",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_073",
beginTime = {
seconds = 1711036800
},
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[840074] = {
id = 840074,
quality = 3,
name = "【限时】乱斗新星",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_074",
beginTime = {
seconds = 1712851200
},
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[840075] = {
id = 840075,
quality = 3,
name = "【限时】星宝高手",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_075",
beginTime = {
seconds = 1711641600
},
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[840076] = {
id = 840076,
quality = 3,
name = "【限时】突围先锋",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_076",
beginTime = {
seconds = 1712851200
},
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[840077] = {
id = 840077,
quality = 3,
name = "【限时】武器大师",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_077",
beginTime = {
seconds = 1712246400
},
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[840078] = {
id = 840078,
quality = 3,
name = "【限时】暗星高手",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_HeadFrame_078",
beginTime = {
seconds = 1711641600
},
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[840079] = {
id = 840079,
quality = 3,
name = "旋风之路",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_086",
beginTime = {
seconds = 1711641600
},
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[840080] = {
id = 840080,
quality = 3,
name = "茶话会",
desc = "官方社区活跃获得",
icon = "CDN:T_HeadFrame_080",
beginTime = {
seconds = 1711900800
},
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[840081] = {
id = 840081,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
name = "雪夜星辰",
desc = "霜天冰雨祈愿活动获得",
icon = "D_HeadFrame_087",
isDynamic = 1,
beginTime = {
seconds = 1712160000
},
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[840082] = {
id = 840082,
name = "龙爪戏珠",
desc = "龙舞星宵祈愿活动获得",
icon = "D_HeadFrame_062",
showInView = 0,
isDynamic = 1,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[840084] = {
id = 840084,
quality = 3,
name = "欢乐菜一起",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_087",
beginTime = {
seconds = 1712851200
},
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[840085] = {
id = 840085,
name = "蔬菜精灵",
desc = "蔬菜精灵祈愿活动获得",
icon = "D_HeadFrame_061",
isDynamic = 1,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[840086] = {
id = 840086,
quality = 3,
name = "星宝守护者",
desc = "星宝守护者活动获得",
icon = "CDN:T_HeadFrame_088",
beginTime = {
seconds = 1713110400
},
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[840089] = {
id = 840089,
name = "春日部防卫队",
desc = "小新联动祈愿活动获得",
icon = "CDN:T_HeadFrame_089",
beginTime = {
seconds = 1713456000
},
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[840090] = {
id = 840090,
name = "逐梦探月",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_090",
showInView = 0,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[840091] = {
id = 840091,
quality = 3,
name = "神笔马星",
desc = "皮肤共创大赛活动获得",
icon = "CDN:T_HeadFrame_082",
beginTime = {
seconds = 1713369600
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[840092] = {
id = 840092,
lowVer = "1.2.100.1",
quality = 1,
name = "首席玩家",
desc = "超核管家限时活动获得",
icon = "D_HeadFrame_092",
showInView = 0,
isDynamic = 1,
beginTime = {
seconds = 1714492800
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[840093] = {
id = 840093,
lowVer = "1.2.100.1",
name = "小丸子",
desc = "小丸子便当屋祈愿活动获得",
icon = "D_HeadFrame_093",
isDynamic = 1,
beginTime = {
seconds = 1714492800
},
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[840094] = {
id = 840094,
lowVer = "1.2.100.1",
name = "夏日舞会",
desc = "盛装派对祈愿活动获得",
icon = "D_HeadFrame_094",
isDynamic = 1,
beginTime = {
seconds = 1714492800
},
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[840095] = {
id = 840095,
lowVer = "1.2.100.1",
quality = 3,
name = "悠闲假期",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_095",
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[840096] = {
id = 840096,
lowVer = "1.2.100.1",
quality = 3,
name = " 闪耀梦想 ",
desc = "潮音畅想—赛季兑换中获得",
icon = "CDN:T_HeadFrame_096",
beginTime = {
seconds = 1714060800
},
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[840097] = {
id = 840097,
lowVer = "1.2.100.1",
name = "潮音畅想",
desc = "潮音畅想赛季限时活动获得",
icon = "D_HeadFrame_097",
isDynamic = 1,
beginTime = {
seconds = 1714060800
},
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[840098] = {
id = 840098,
lowVer = "1.2.100.1",
name = "炫彩音符",
desc = "赛季祈愿中获得",
icon = "D_HeadFrame_098",
isDynamic = 1,
beginTime = {
seconds = 1714060800
},
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[840099] = {
id = 840099,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.2.100.1",
name = "花海灵鲲",
desc = "春水溯游祈愿获得",
icon = "D_HeadFrame_099",
isDynamic = 1,
beginTime = {
seconds = 1714665600
},
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[840100] = {
id = 840100,
lowVer = "1.2.100.1",
quality = 4,
name = "铜铃乐曲",
desc = "潮音通行证获得",
icon = "CDN:T_HeadFrame_100",
beginTime = {
seconds = 1714060800
}
},
[840101] = {
id = 840101,
lowVer = "1.2.100.1",
quality = 3,
name = "音符之铃",
desc = "潮音通行证获得",
icon = "CDN:T_HeadFrame_101",
beginTime = {
seconds = 1714060800
}
},
[840102] = {
id = 840102,
lowVer = "1.2.100.1",
quality = 3,
name = "甜心樱桃",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_102",
beginTime = {
seconds = 1714492800
}
},
[840103] = {
id = 840103,
name = "泡泡玛特",
desc = "泡泡玛特祈愿获得",
icon = "D_HeadFrame_103",
isDynamic = 1,
beginTime = {
seconds = 1715270400
}
},
[840104] = {
id = 840104,
quality = 3,
name = "紫藤幻梦",
desc = "限时活动获得",
icon = "CDN:T_HeadFrame_104",
beginTime = {
seconds = 1715011200
}
},
[840105] = {
id = 840105,
lowVer = "1.2.100.53",
quality = 4,
name = "回眸一笑汪",
desc = "活动获得",
icon = "CDN:T_HeadFrame_091",
beginTime = {
seconds = 1715875200
}
},
[840106] = {
id = 840106,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
lowVer = "1.2.100.46",
name = "永恒之誓",
desc = "永恒之誓祈愿活动获得",
icon = "D_HeadFrame_105",
isDynamic = 1,
beginTime = {
seconds = 1715875200
}
},
[840107] = {
id = 840107,
lowVer = "1.2.100.46",
name = "天鹅之舞",
desc = "暂无获取途径",
icon = "D_HeadFrame_107",
showInView = 0,
isDynamic = 1
},
[840108] = {
id = 840108,
lowVer = "1.2.100.46",
name = "童趣乐园",
desc = "烂漫时光活动获得",
icon = "T_HeadFrame_106",
beginTime = {
seconds = 1716739200
}
},
[840110] = {
id = 840110,
lowVer = "1.3.7.1",
quality = 3,
name = "粽享欢夏",
desc = "欢夏签到礼活动获得",
icon = "CDN:T_HeadFrame_110",
beginTime = {
seconds = 1717776000
}
},
[840113] = {
id = 840113,
lowVer = "1.2.100.1",
name = "阿童木&小兰",
desc = "铁臂阿童木祈愿获得",
icon = "D_HeadFrame_113",
isDynamic = 1,
beginTime = {
seconds = 1717171200
}
}
}

local mt = {
type = "ItemType_Frame",
maxNum = 1,
quality = 2,
desc = "敬请期待",
showInView = 1,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data