--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: avatar礼包（新）

local data = {
[140006] = {
mallId = 21,
commodityId = 140006,
commodityName = "海精灵 海伦娜",
coinType = 1,
price = 800,
discountPrice = 630,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074336000
},
endTime = {
seconds = 4098959999
},
shopTag = {
5,
23
},
gender = 0,
itemIds = {
400610
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_MultipleItemSea0101.png",
packageDec = "购买后获得海精灵 海伦娜"
},
[140009] = {
mallId = 21,
commodityId = 140009,
commodityName = "森林童话礼包",
coinType = 1,
price = 2050,
discountPrice = 960,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074336000
},
endTime = {
seconds = 4098959999
},
shopTag = {
5,
23
},
gender = 0,
minVersion = "********",
itemIds = {
400580,
400620,
721005
},
itemNums = {
1,
1,
1
},
packageIcon = "T_AvatarGiftSubView_Icon_MultipleItem01(3).png",
packageDec = "购买后获得狼少年 格雷、小红帽 露比和星宝贴贴"
},
[140010] = {
mallId = 21,
commodityId = 140010,
commodityName = "小红帽 露比",
coinType = 1,
price = 800,
discountPrice = 630,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074336000
},
endTime = {
seconds = 4098959999
},
shopTag = {
5,
23
},
gender = 0,
minVersion = "********",
itemIds = {
400620
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_MultipleItem02(2).png",
packageDec = "购买后获得小红帽 露比"
},
[140011] = {
mallId = 21,
commodityId = 140011,
commodityName = "狼少年 格雷",
coinType = 1,
price = 800,
discountPrice = 630,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074336000
},
endTime = {
seconds = 4098959999
},
shopTag = {
5,
23
},
gender = 0,
minVersion = "********",
itemIds = {
400580
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_MultipleItem03(2).png",
packageDec = "购买后获得狼少年 格雷"
},
[140012] = {
mallId = 21,
commodityId = 140012,
commodityName = "快乐小狗",
coinType = 1,
discountPrice = 10,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074336000
},
endTime = {
seconds = 4098959999
},
shopTag = {
5,
23
},
gender = 0,
minVersion = "*******",
canDirectBuy = true,
itemIds = {
510245
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_MultipleItem01(3).png"
},
[140013] = {
mallId = 21,
commodityId = 140013,
commodityName = "巨星奖杯",
coinType = 1,
discountPrice = 60,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074336000
},
endTime = {
seconds = 4098959999
},
shopTag = {
5,
23
},
gender = 0,
minVersion = "*******",
canDirectBuy = true,
itemIds = {
620424
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_MultipleItem02(2).png"
},
[140014] = {
mallId = 21,
commodityId = 140014,
commodityName = "纸盒小怪兽",
coinType = 1,
discountPrice = 120,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074336000
},
endTime = {
seconds = 4098959999
},
shopTag = {
5,
23
},
gender = 0,
minVersion = "*******",
canDirectBuy = true,
itemIds = {
404160
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_MultipleItem03(2).png"
},
[140015] = {
mallId = 21,
commodityId = 140015,
commodityName = "纸盒小怪兽",
coinType = 1,
price = 120,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1740326400
},
endTime = {
seconds = 1742399999
},
shopTag = {
5,
160403
},
gender = 0,
minVersion = "*********",
canDirectBuy = true,
itemIds = {
404160
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_zhxgs1.astc",
packageDec = "购买后获得纸盒小怪兽"
},
[140016] = {
mallId = 21,
commodityId = 140016,
commodityName = "毛绒老鼠",
coinType = 1,
price = 60,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1740326400
},
endTime = {
seconds = 1742399999
},
shopTag = {
5,
160403
},
gender = 0,
minVersion = "*********",
canDirectBuy = true,
itemIds = {
620594
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_mrls1.astc",
packageDec = "购买后获得毛绒老鼠"
},
[140017] = {
mallId = 21,
commodityId = 140017,
commodityName = "快乐小狗",
coinType = 1,
price = 10,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1740326400
},
endTime = {
seconds = 1742399999
},
shopTag = {
5,
160403
},
gender = 0,
minVersion = "*********",
canDirectBuy = true,
itemIds = {
510245
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_klxg1.astc",
packageDec = "购买后获得快乐小狗"
},
[140021] = {
mallId = 21,
commodityId = 140021,
commodityName = "冬日随机礼盒",
coinType = 1,
price = 60,
limitType = "MCL_LifeLongLimit",
limitNum = 4,
beginTime = {
seconds = 1740326400
},
endTime = {
seconds = 1742399999
},
shopTag = {
5,
160404
},
gender = 0,
minVersion = "1.3.39.12",
canDirectBuy = true,
itemIds = {
320096
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_zhxgs1.astc",
packageDec = "购买后获得冬日随机礼盒"
},
[140022] = {
mallId = 21,
commodityId = 140022,
commodityName = "狗狗礼盒",
coinType = 1,
price = 200,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1740326400
},
endTime = {
seconds = 1742399999
},
shopTag = {
5,
160404
},
gender = 0,
minVersion = "1.3.39.12",
canDirectBuy = true,
itemIds = {
404330,
620582
},
itemNums = {
1,
1
},
packageIcon = "T_AvatarGiftSubView_Icon_mrls1.astc",
packageDec = "购买后获得袋袋狗*1、小狗曲奇*1"
},
[140023] = {
mallId = 21,
commodityId = 140023,
commodityName = "辣妹礼盒",
coinType = 1,
price = 200,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1740326400
},
endTime = {
seconds = 1742399999
},
shopTag = {
5,
160404
},
gender = 0,
minVersion = "1.3.39.12",
canDirectBuy = true,
itemIds = {
404610,
620651
},
itemNums = {
1,
1
},
packageIcon = "T_AvatarGiftSubView_Icon_klxg1.astc",
packageDec = "购买后获得派对甜心*1、甜心按键机*1"
},
[140026] = {
mallId = 21,
commodityId = 140026,
commodityName = "蝴蝶连衣裙",
coinType = 1,
price = 120,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750694399
},
shopTag = {
5,
160404
},
gender = 0,
minVersion = "1.3.88.88",
canDirectBuy = true,
itemIds = {
411650
},
itemNums = {
1
},
packageIcon = "T_AvatarGiftSubView_Icon_klxg1.astc",
packageDec = "购买后获得蝴蝶连衣裙"
},
[140025] = {
mallId = 21,
commodityId = 140025,
commodityName = "妙蛙吹风机",
coinType = 1,
price = 120,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750694399
},
shopTag = {
5,
160404
},
gender = 0,
minVersion = "1.3.88.88",
canDirectBuy = true,
itemIds = {
640148
},
itemNums = {
1
},
packageIcon = "Handhold_081.astc",
packageDec = "购买后获得妙蛙吹风机"
},
[140024] = {
mallId = 21,
commodityId = 140024,
commodityName = "戏春浅",
coinType = 1,
price = 10,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1747670400
},
endTime = {
seconds = 1750694399
},
shopTag = {
5,
160404
},
gender = 0,
minVersion = "1.3.88.88",
canDirectBuy = true,
itemIds = {
510304
},
itemNums = {
1
},
packageIcon = "TY_326.astc",
packageDec = "购买后获得戏春浅"
}
}

local mt = {
canDirectBuy = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data