--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local v0 = {
{
itemId = 6,
itemNum = 100
}
}

local v1 = 3

local v2 = "商城"

local v3 = {
15
}

local v4 = "IUTO_GiftPackage"

local v5 = {
fashionValue = 125
}

local v6 = "AS_CH_Pose_Common_001"

local v7 = {
seconds = 4101552000
}

local v8 = 10

local data = {
[410470] = {
id = 410470,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "亚当当",
desc = "不好意思，我真的什么都不知道",
icon = "CDN:Icon_BU_256",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_256",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"按部就班地生活，也挺好"
},
shareAnim = v6,
beginTime = v7,
suitId = 862,
suitName = "亚当当",
suitIcon = "CDN:Icon_BU_256"
},
[410471] = {
id = 410471,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "亚当当",
desc = "不好意思，我真的什么都不知道",
icon = "CDN:Icon_BU_256_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410470,
fashionValue = 25,
belongToGroup = {
410471
}
},
resourceConf = {
model = "SK_BU_256",
material = "MI_BU_256_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11952,
shareTexts = {
"按部就班地生活，也挺好"
},
shareAnim = v6
},
[410480] = {
id = 410480,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "格拉底",
desc = "当你凝视着深渊时，深渊也在凝视着你",
icon = "CDN:Icon_BU_257",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_257",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"认识你自己"
},
shareAnim = v6,
beginTime = v7,
suitId = 864,
suitName = "格拉底",
suitIcon = "CDN:Icon_BU_257"
},
[410481] = {
id = 410481,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "格拉底",
desc = "当你凝视着深渊时，深渊也在凝视着你",
icon = "CDN:Icon_BU_257_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410480,
fashionValue = 25,
belongToGroup = {
410481
}
},
resourceConf = {
model = "SK_BU_257",
material = "MI_BU_257_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11954,
shareTexts = {
"认识你自己"
},
shareAnim = v6
},
[410490] = {
id = 410490,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "莉莉娅",
desc = "被你找到，比藏起来更重要",
icon = "CDN:Icon_BU_258",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_258",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"不是丢了，会找到的"
},
shareAnim = v6,
beginTime = v7,
suitId = 866,
suitName = "莉莉娅",
suitIcon = "CDN:Icon_BU_258"
},
[410491] = {
id = 410491,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "莉莉娅",
desc = "被你找到，比藏起来更重要",
icon = "CDN:Icon_BU_258_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410490,
fashionValue = 25,
belongToGroup = {
410491
}
},
resourceConf = {
model = "SK_BU_258",
material = "MI_BU_258_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11956,
shareTexts = {
"不是丢了，会找到的"
},
shareAnim = v6
},
[410500] = {
id = 410500,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "花菲菲",
desc = "我正坐在花蕊上晒太阳，你看到了吗？",
icon = "CDN:Icon_BU_259",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_259",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"是花的孩子"
},
shareAnim = v6,
beginTime = v7,
suitId = 868,
suitName = "花菲菲",
suitIcon = "CDN:Icon_BU_259"
},
[410501] = {
id = 410501,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "花菲菲",
desc = "我正坐在花蕊上晒太阳，你看到了吗？",
icon = "CDN:Icon_BU_259_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410500,
fashionValue = 25,
belongToGroup = {
410501
}
},
resourceConf = {
model = "SK_BU_259",
material = "MI_BU_259_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11958,
shareTexts = {
"是花的孩子"
},
shareAnim = v6
},
[410510] = {
id = 410510,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "暖小熙",
desc = "裹在厚厚的织物里，度过暖暖的冬天",
icon = "CDN:Icon_BU_260",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_260",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"松弛感，还得靠毛线"
},
shareAnim = v6,
beginTime = v7,
suitId = 870,
suitName = "暖小熙",
suitIcon = "CDN:Icon_BU_260"
},
[410511] = {
id = 410511,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "暖小熙",
desc = "裹在厚厚的织物里，度过暖暖的冬天",
icon = "CDN:Icon_BU_260_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410510,
fashionValue = 25,
belongToGroup = {
410511
}
},
resourceConf = {
model = "SK_BU_260",
material = "MI_BU_260_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11960,
shareTexts = {
"松弛感，还得靠毛线"
},
shareAnim = v6
},
[410520] = {
id = 410520,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "阿力力",
desc = "从前的生活，有很多讲究",
icon = "CDN:Icon_BU_261",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_261",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"狩猎，是一种本能"
},
shareAnim = v6,
beginTime = v7,
suitId = 872,
suitName = "阿力力",
suitIcon = "CDN:Icon_BU_261"
},
[410521] = {
id = 410521,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "阿力力",
desc = "从前的生活，有很多讲究",
icon = "CDN:Icon_BU_261_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410520,
fashionValue = 25,
belongToGroup = {
410521
}
},
resourceConf = {
model = "SK_BU_261",
material = "MI_BU_261_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11962,
shareTexts = {
"狩猎，是一种本能"
},
shareAnim = v6
},
[410530] = {
id = 410530,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "杯面仔",
desc = "永远不会给你画饼",
icon = "CDN:Icon_BU_262",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_262",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"闻着香，吃得到，还管饱！"
},
shareAnim = v6,
beginTime = v7,
suitId = 874,
suitName = "杯面仔",
suitIcon = "CDN:Icon_BU_262"
},
[410531] = {
id = 410531,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "杯面仔",
desc = "永远不会给你画饼",
icon = "CDN:Icon_BU_262_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410530,
fashionValue = 25,
belongToGroup = {
410531
}
},
resourceConf = {
model = "SK_BU_262",
material = "MI_BU_262_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11964,
shareTexts = {
"闻着香，吃得到，还管饱！"
},
shareAnim = v6
},
[410540] = {
id = 410540,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "淘乐星",
desc = "偶尔没规矩，地球也一样会转",
icon = "CDN:Icon_BU_263",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_263",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"皮这一下很开心"
},
shareAnim = v6,
beginTime = v7,
suitId = 876,
suitName = "淘乐星",
suitIcon = "CDN:Icon_BU_263"
},
[410541] = {
id = 410541,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "淘乐星",
desc = "偶尔没规矩，地球也一样会转",
icon = "CDN:Icon_BU_263_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410540,
fashionValue = 25,
belongToGroup = {
410541
}
},
resourceConf = {
model = "SK_BU_263",
material = "MI_BU_263_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11966,
shareTexts = {
"皮这一下很开心"
},
shareAnim = v6
},
[410550] = {
id = 410550,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "嗅小宝",
desc = "快闪开，我要释放生化武器了！",
icon = "CDN:Icon_BU_264",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_264",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"也可以当宠物的...吧？"
},
shareAnim = v6,
beginTime = v7,
suitId = 878,
suitName = "嗅小宝",
suitIcon = "CDN:Icon_BU_264"
},
[410551] = {
id = 410551,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "嗅小宝",
desc = "快闪开，我要释放生化武器了！",
icon = "CDN:Icon_BU_264_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410550,
fashionValue = 25,
belongToGroup = {
410551
}
},
resourceConf = {
model = "SK_BU_264",
material = "MI_BU_264_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11968,
shareTexts = {
"也可以当宠物的...吧？"
},
shareAnim = v6
},
[410560] = {
id = 410560,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "焰狸狸",
desc = "爱是毛茸茸的信号",
icon = "CDN:Icon_BU_265",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_265",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"来呀，打声招呼"
},
shareAnim = v6,
beginTime = v7,
suitId = 880,
suitName = "焰狸狸",
suitIcon = "CDN:Icon_BU_265"
},
[410561] = {
id = 410561,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "焰狸狸",
desc = "爱是毛茸茸的信号",
icon = "CDN:Icon_BU_265_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410560,
fashionValue = 25,
belongToGroup = {
410561
}
},
resourceConf = {
model = "SK_BU_265",
material = "MI_BU_265_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11970,
shareTexts = {
"来呀，打声招呼"
},
shareAnim = v6
},
[410570] = {
id = 410570,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "茸茸兔",
desc = "竖起耳朵，收到收到~",
icon = "CDN:Icon_BU_266",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_266",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"你的小可爱已上线"
},
shareAnim = v6,
beginTime = v7,
suitId = 882,
suitName = "茸茸兔",
suitIcon = "CDN:Icon_BU_266"
},
[410571] = {
id = 410571,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "茸茸兔",
desc = "竖起耳朵，收到收到~",
icon = "CDN:Icon_BU_266_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410570,
fashionValue = 25,
belongToGroup = {
410571
}
},
resourceConf = {
model = "SK_BU_266",
material = "MI_BU_266_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11972,
shareTexts = {
"你的小可爱已上线"
},
shareAnim = v6
},
[410580] = {
id = 410580,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "INFP小蝴蝶",
desc = "计划表是空白的，但我的一天是满满的",
icon = "CDN:Icon_BU_267",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_267",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"无意义本身也是意义所在"
},
shareAnim = v6,
beginTime = v7,
suitId = 884,
suitName = "INFP小蝴蝶",
suitIcon = "CDN:Icon_BU_267"
},
[410581] = {
id = 410581,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "INFP小蝴蝶",
desc = "计划表是空白的，但我的一天是满满的",
icon = "CDN:Icon_BU_267_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410580,
fashionValue = 25,
belongToGroup = {
410581
}
},
resourceConf = {
model = "SK_BU_267",
material = "MI_BU_267_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11974,
shareTexts = {
"无意义本身也是意义所在"
},
shareAnim = v6
},
[410590] = {
id = 410590,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "小金毛",
desc = "要是永远长不大就好了",
icon = "CDN:Icon_Body_041",
getWay = v2,
jumpId = v3,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_041",
upper = "SK_Body_Upper_041",
bottom = "SK_Body_Under_041",
gloves = "SK_Body_Hands_041",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"可以摸摸头哦"
},
shareAnim = v6,
beginTime = v7,
suitId = 886,
suitName = "小金毛",
suitIcon = "CDN:Icon_Body_041"
},
[410600] = {
id = 410600,
effect = true,
name = "百合信使 真白",
desc = "一纸书信，跨越山海，传递心声",
icon = "CDN:Icon_PL_268",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_268",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_268_Physics"
},
outEnter = "AS_CH_Enter_PL_268",
outIdle = "AS_CH_OutIdle_PL_268",
outShow = "AS_CH_IdleShow_PL_268",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_268",
shareTexts = {
"真诚的笔触胜过千言万语"
},
shareAnim = v6,
beginTime = {
seconds = 1740067200
},
suitStoryTextKey = [[真白是一名信使，在精灵之森进修过“如何成为一名好信使”的相关课程后，她被派送到一个偏远的小村庄。

小村庄很神奇，没有信使，星宝们通过花粉网络传递信息，速度飞快！真白发现自己一身本领却无处可用，让她有些气馁。村民们深居浅出，平日·很难见到他们的身影，邻里之间的往来很少。真白独自重建小村唯一的信使驿站，着手绘制周围的地图，测算各个区域的送信时间。

经年久月的传递信息，花粉网络已不堪重负，一天早晨，它彻底陷入瘫痪。随之而来的则是村民们的恐慌，无法通过花粉传递信息，他们该怎么办？这时真白站了出来，承担起送信的职责。

小村的星宝们在这之前几乎没写过信，这种新奇的交流方式让他们感到很神奇。想说的话、想传递的情感在等信的时间里被酝酿出奇特的味道。真白想起课堂上老师的教诲：沟通是为了建立紧密且持久的连接，将彼此的心联系在一起，是信使的职责。

看着接到信件时星宝们脸上喜悦的表情，看着只待在家中的星宝也开始渐渐出门和邻居沟通交流，真白知晓，自己所做的一切都是值得的。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410600.astc",
suitId = 888,
suitName = "百合信使 真白",
suitIcon = "CDN:Icon_PL_268",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410600.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410600.astc"
},
[410601] = {
id = 410601,
effect = true,
name = "百合信使 真白",
desc = "一纸书信，跨越山海，传递心声",
icon = "CDN:Icon_PL_268_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410600,
fashionValue = 35,
belongToGroup = {
410601,
410602
}
},
resourceConf = {
model = "SK_PL_268",
material = "MI_PL_268_1_HP01;MI_PL_268_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_268_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11977,
outEnter = "AS_CH_Enter_PL_268",
outIdle = "AS_CH_OutIdle_PL_268",
outShow = "AS_CH_IdleShow_PL_268",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_268",
shareTexts = {
"真诚的笔触胜过千言万语"
},
shareAnim = v6,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410600.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410600.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410600.astc"
},
[410602] = {
id = 410602,
effect = true,
name = "百合信使 真白",
desc = "一纸书信，跨越山海，传递心声",
icon = "CDN:Icon_PL_268_02",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410600,
fashionValue = 35,
belongToGroup = {
410601,
410602
}
},
resourceConf = {
model = "SK_PL_268",
material = "MI_PL_268_1_HP02;MI_PL_268_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_268_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11978,
outEnter = "AS_CH_Enter_PL_268",
outIdle = "AS_CH_OutIdle_PL_268",
outShow = "AS_CH_IdleShow_PL_268",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_268",
shareTexts = {
"真诚的笔触胜过千言万语"
},
shareAnim = v6,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410600.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410600.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410600.astc"
},
[410610] = {
id = 410610,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "狮威威",
desc = "内心强大，才是真正的霸气",
icon = "CDN:Icon_Body_037",
getWay = v2,
jumpId = v3,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_037",
upper = "SK_Body_Upper_037",
bottom = "SK_Body_Under_037",
gloves = "SK_Body_Hands_037",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"狮子回头，万事不愁"
},
shareAnim = v6,
beginTime = v7,
suitId = 960,
suitName = "狮威威",
suitIcon = "CDN:Icon_Body_037"
},
[410620] = {
id = 410620,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "洛可可",
desc = "我就是我，千变万化的烟火",
icon = "CDN:Icon_BU_269",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_269",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"千禧年的风，又吹了回来"
},
shareAnim = v6,
beginTime = v7,
suitId = 962,
suitName = "洛可可",
suitIcon = "CDN:Icon_BU_269"
},
[410621] = {
id = 410621,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "洛可可",
desc = "我就是我，千变万化的烟火",
icon = "CDN:Icon_BU_269_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410620,
fashionValue = 25,
belongToGroup = {
410621
}
},
resourceConf = {
model = "SK_BU_269",
material = "MI_BU_269_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11980,
shareTexts = {
"千禧年的风，又吹了回来"
},
shareAnim = v6
},
[410630] = {
id = 410630,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "雷蒙蒙",
desc = "这舞台，就是我的摇滚王国",
icon = "CDN:Icon_BU_271",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_271",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"跟上节奏，一起摇起来"
},
shareAnim = v6,
beginTime = v7,
suitId = 964,
suitName = "雷蒙蒙",
suitIcon = "CDN:Icon_BU_271"
},
[410631] = {
id = 410631,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "雷蒙蒙",
desc = "这舞台，就是我的摇滚王国",
icon = "CDN:Icon_BU_271_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410630,
fashionValue = 25,
belongToGroup = {
410631
}
},
resourceConf = {
model = "SK_BU_271",
material = "MI_BU_271_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11982,
shareTexts = {
"跟上节奏，一起摇起来"
},
shareAnim = v6
},
[410640] = {
id = 410640,
effect = true,
name = "星闪流萤 埃莉诺",
desc = "最柔软的光，是我的心意",
icon = "CDN:Icon_PL_272",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_272",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_272_Physics"
},
outEnter = "AS_CH_Enter_PL_272",
outShow = "AS_CH_IdleShow_PL_272",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_272",
shareTexts = {
"萤火微光，为你照明"
},
shareAnim = v6,
beginTime = v7,
suitStoryTextKey = [[几亿星纪元前的森林像是被泼了墨，伸手不见五指。黑烟如同贪婪的魔爪，吞噬着月光和星光。在云端之上，一个“不太合格”的小精灵正撑着下巴，若有所思地注视着这片黑暗之地。

埃莉诺——这个连翅膀都比同伴小一圈的精灵，总是被其他精灵调侃是“缩水版”。没有出众的灵力，甚至连施法都会把自己绊倒。但谁能想到，这个“不太合格”的小精灵，拥有着比星空还要璀璨的梦。“夜晚最美的时刻，是繁星轻柔地照耀森林的瞬间。”她经常这样呢喃。每当看到星宝们在黑暗中踉跄前行，她的心就像被细针戳着般疼痛。

一天，埃莉诺冒着被驱逐的风险，闯入了天神的殿堂。“我愿意放弃精灵的身份，换取森林中的光明。”她的声音很轻，却掷地有声。天神叹了一口气：“代价很高，你将永远无法在白日中醒来，只能在黑夜中行走。”埃莉诺忽闪着眼睛，坚定的点头，“只要能给星宝们带来光亮，我愿意。”

最终天神被她的坚定和无私感动，答应了她的请求。只见埃莉诺的身躯开始慢慢变化，化作了一只发着淡淡光芒的萤火虫。那个“不太合格”的精灵，变成了夜空中最柔软的光。

埃莉诺的光芒虽然微弱，却如同坠落森林的一颗星星，成为了黑夜中唯一的指引。星宝们跟着她闪烁的光点，穿越过黑烟，找到了回家的路。]],
suitId = 966,
suitName = "星闪流萤 埃莉诺",
suitIcon = "CDN:Icon_PL_272"
},
[410641] = {
id = 410641,
effect = true,
name = "星闪流萤 埃莉诺",
desc = "最柔软的光，是我的心意",
icon = "CDN:Icon_PL_272_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410640,
fashionValue = 35,
belongToGroup = {
410641,
410642
}
},
resourceConf = {
model = "SK_PL_272",
material = "MI_PL_272_1_HP01;MI_PL_272_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_272_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11985,
outEnter = "AS_CH_Enter_PL_272",
outShow = "AS_CH_IdleShow_PL_272",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_272",
shareTexts = {
"萤火微光，为你照明"
},
shareAnim = v6
},
[410642] = {
id = 410642,
effect = true,
name = "星闪流萤 埃莉诺",
desc = "最柔软的光，是我的心意",
icon = "CDN:Icon_PL_272_02",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410640,
fashionValue = 35,
belongToGroup = {
410641,
410642
}
},
resourceConf = {
model = "SK_PL_272",
material = "MI_PL_272_1_HP02;MI_PL_272_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_272_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11986,
outEnter = "AS_CH_Enter_PL_272",
outShow = "AS_CH_IdleShow_PL_272",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_272",
shareTexts = {
"萤火微光，为你照明"
},
shareAnim = v6
},
[410650] = {
id = 410650,
effect = true,
name = "怪奇菌学家 奇奇奥",
desc = "谁说蘑菇不会跳舞，那是你见识太少！",
icon = "CDN:Icon_PL_273",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_273",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_273_Physics"
},
outEnter = "AS_CH_Enter_PL_273",
outShow = "AS_CH_IdleShow_PL_273",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_273",
shareTexts = {
"蘑菇的秘密，可比星星还多"
},
shareAnim = v6,
beginTime = v7,
suitStoryTextKey = [[没有星宝知道奇奇奥究竟收藏了多少稀奇古怪的蘑菇。这位顶着艳丽红点的蘑菇精灵，背着一个永远装不满的百宝袋，里面塞满了奇妙的宝贝：会发光的夜光菇、会跳舞的蘑菇胞子、会唱歌的菌丝，甚至还有会讲故事的孢子！他总是神出鬼没地出现在各种难以想象的地方。

奇奇奥是蘑菇村公认的首席“菌学家”，一位专门研究蘑菇的蘑菇精灵——是的，这听起来有点奇怪，但蘑菇界的知识远比大家想象的深奥。他热衷于研究蘑菇的生长习性、孢子的传播规律，以及蘑菇在森林生态中的种种秘密。他曾自豪地宣布：“森林里没有我不认识的蘑菇！”但没过三天，他就被一株奇特的黑蘑菇绊倒，还因此成了全村的笑柄。

奇奇奥天生对一切新事物充满热情。他喜欢用放大镜观察蘑菇的每个细节，还常常偷偷记录“蘑菇家族的夜间舞会”——据说每到月圆之夜，蘑菇们会聚在森林深处跳舞，虽然没有星宝亲眼见过，但奇奇奥发誓自己是唯一的目击者。

他偶尔也有点小迷糊，曾经把毒蘑菇当成药蘑菇送给村长，害得村长的尾巴都长了两天的绿斑点。尽管如此，奇奇奥的“菌丝粉”却是森林里的顶级发明，能让凶猛的生物瞬间被蘑菇香味迷住——也因此，他常常被森林里的小动物们追着要“蘑菇香水”。

“我的梦想是找到会打喷嚏的蘑菇！”奇奇奥总是这样憧憬着，“听说打一个喷嚏就能把你送到森林的任何角落！”虽然大家都说这种蘑菇是无稽之谈，但谁又能说得准呢？毕竟在奇奇奥的世界里，一切皆有可能！]],
suitId = 968,
suitName = "怪奇菌学家 奇奇奥",
suitIcon = "CDN:Icon_PL_273"
},
[410651] = {
id = 410651,
effect = true,
name = "怪奇菌学家 奇奇奥",
desc = "谁说蘑菇不会跳舞，那是你见识太少！",
icon = "CDN:Icon_PL_273_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410650,
fashionValue = 35,
belongToGroup = {
410651,
410652
}
},
resourceConf = {
model = "SK_PL_273",
material = "MI_PL_273_1_HP01;MI_PL_273_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_273_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11990,
outEnter = "AS_CH_Enter_PL_273",
outShow = "AS_CH_IdleShow_PL_273",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_273",
shareTexts = {
"蘑菇的秘密，可比星星还多"
},
shareAnim = v6
},
[410652] = {
id = 410652,
effect = true,
name = "怪奇菌学家 奇奇奥",
desc = "谁说蘑菇不会跳舞，那是你见识太少！",
icon = "CDN:Icon_PL_273_02",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410650,
fashionValue = 35,
belongToGroup = {
410651,
410652
}
},
resourceConf = {
model = "SK_PL_273",
material = "MI_PL_273_1_HP02;MI_PL_273_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_273_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11991,
outEnter = "AS_CH_Enter_PL_273",
outShow = "AS_CH_IdleShow_PL_273",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_273",
shareTexts = {
"蘑菇的秘密，可比星星还多"
},
shareAnim = v6
},
[410660] = {
id = 410660,
effect = true,
exceedReplaceItem = {
{
itemId = 3950,
itemNum = 320
}
},
quality = 1,
name = "自由之蝶 凡妮莎",
desc = "每一次振翅，都是对追寻自由的礼赞",
icon = "CDN:Icon_OG_043",
getWay = v2,
jumpId = v3,
outlookConf = {
fashionValue = 1200
},
resourceConf = {
model = "SK_OG_043",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_043_Physics",
IconLabelId = 108,
skeletalMesh = "SK_OG_043"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_043_PV/Level_OG_043_Intact",
outIdle = "AS_CH_OutIdle_OG_043",
outShow = "AS_CH_IdleShow_OG_043",
outShowIntervalTime = 20,
soundId = {
4091,
4090
},
outIdle_pv = "LS_PV_OG_043_Idle",
outEnterSequence = "LS_PV_OG_043",
shareTexts = {
"蝶翼轻扇，也能掀起风暴"
},
shareAnim = v6,
beginTime = v7,
suitStoryTextKey = [[“为了保护你们。”这是蝶族统治者瑟雷恩的谎言。他将永光蓝水晶封存在高塔，声称沧海外的世界充满危险，唯有困守孤岛才是蝶族生存之道。

蝶族公主凡妮莎从小就质疑这个“保护”。每当她站在悬崖边，望着远方的天际线，都能感受到一种莫名的召唤。

一个月前，她在禁地的古籍中发现了真相：蓝水晶并非囚笼的钥匙，而是蝶族远航的指明灯。千年前，蝶族曾是自由翱翔的民族，直到一场族内斗争后，统治者编造了“无法飞越沧海”的谎言，用恐惧束缚族人的翅膀。

她开始暗中寻找支持者，却被叔父瑟雷恩发现。“愚蠢的孩子！”他暴跳如雷，“你要让族人重返混乱吗？”随即下令清剿“叛逆”。

危险时刻，凡妮莎的祖母将高塔打开并将蓝水晶交给她：“去吧，飞向自由。让族人知道，我们的翅膀从未折断。”月辉如碎钻洒落在凡妮莎的晶翼上，手中的蓝水晶轻轻共鸣。

叔父得知蓝水晶被凡妮莎带走，于是下令追击。每当风暴将凡妮莎击落，蓝水晶就会微微发光，似乎在唤醒她体内沉睡的勇气。暴风雨中，瑟雷恩的怒吼在身后回荡：“回来！外面的世界会毁了你！”

凡妮莎没有回头。海雾腐蚀着她的羽翼，但她明白，真正腐蚀蝶族的，是恐惧与谎言。就在她力竭之时，蓝水晶突然绽放出璀璨光芒，照亮了整片沧海。那一刻，她听见了先祖的低语：“打破枷锁的，从来都不是蓝水晶的力量，而是敢于飞翔的勇气。”

追兵消散于光辉中，凡妮莎的身影与蓝光交织，化作永恒的传说。蝶族重新归于和平，也多了一个新的传说：每当风暴来临，总有一道晶翼的身影划破沧海，为迷途者指引方向。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410660.astc",
suitId = 970,
suitName = "自由之蝶 凡妮莎",
suitIcon = "CDN:Icon_OG_043",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410660.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410660.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
firstNoSkip = 1,
suitSkillId = 41066001
},
[410661] = {
id = 410661,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "自由之蝶 凡妮莎",
desc = "每一次振翅，都是对追寻自由的礼赞",
icon = "CDN:Icon_OG_043_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410660,
fashionValue = 145,
belongToGroup = {
410661,
410662
}
},
resourceConf = {
model = "SK_OG_043",
material = "MI_OG_043_1_HP01;MI_OG_043_2_HP01;MI_OG_043_3_HP01;MI_OG_043_4_HP01;MI_OG_043_5_HP01;MI_OG_043_6_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_043_Physics",
materialSlot = "Skin;Skin_01;Skin_02;Skin_04;Skin_05;Skin_06",
skeletalMesh = "SK_OG_043"
},
commodityId = 11993,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_043_PV/Level_OG_043_Intact",
outIdle = "AS_CH_OutIdle_OG_043",
outShow = "AS_CH_IdleShow_OG_043_HP01",
outShowIntervalTime = 20,
soundId = {
4091,
4090
},
outIdle_pv = "LS_PV_OG_043_HP01_Idle",
outEnterSequence = "LS_PV_OG_043_HP01",
shareTexts = {
"蝶翼轻扇，也能掀起风暴"
},
shareAnim = v6,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410660.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410660.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410660.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
firstNoSkip = 1,
suitSkillId = 41066101
},
[410662] = {
id = 410662,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "自由之蝶 凡妮莎",
desc = "每一次振翅，都是对追寻自由的礼赞",
icon = "CDN:Icon_OG_043_02",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410660,
fashionValue = 145,
belongToGroup = {
410661,
410662
}
},
resourceConf = {
model = "SK_OG_043",
material = "MI_OG_043_1_HP02;MI_OG_043_2_HP02;MI_OG_043_3_HP02;MI_OG_043_4_HP02;MI_OG_043_5_HP02;MI_OG_043_6_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_043_Physics",
materialSlot = "Skin;Skin_01;Skin_02;Skin_04;Skin_05;Skin_06",
skeletalMesh = "SK_OG_043"
},
commodityId = 11994,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_043_PV/Level_OG_043_Intact",
outIdle = "AS_CH_OutIdle_OG_043",
outShow = "AS_CH_IdleShow_OG_043_HP02",
outShowIntervalTime = 20,
soundId = {
4091,
4090
},
outIdle_pv = "LS_PV_OG_043_HP02_Idle",
outEnterSequence = "LS_PV_OG_043_HP02",
shareTexts = {
"蝶翼轻扇，也能掀起风暴"
},
shareAnim = v6,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410660.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410660.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410660.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
firstNoSkip = 1,
suitSkillId = 41066201
},
[410670] = {
id = 410670,
effect = true,
name = "小鸡小甜豆",
desc = "对世界的幻想，装在小鸡的脑袋里",
icon = "CDN:Icon_PL_278",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_278",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_278_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_278",
outIdle = "AS_CH_OutIdle_PL_278",
outShow = "AS_CH_IdleShow_PL_278",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_278",
shareTexts = {
"珍藏好米，送给珍贵的你~"
},
shareAnim = v6,
beginTime = {
seconds = 1736006400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410670.astc",
suitId = 972,
suitName = "小鸡小甜豆",
themedId = 32,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_278",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410670.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410670.astc",
ThemedShowIdList = {
{
key = 32,
value = 1
},
{
key = 33,
value = 2
}
}
},
[410680] = {
id = 410680,
effect = true,
name = "兔兔阿卓",
desc = "背起小行囊，向世界问好~",
icon = "CDN:Icon_PL_280",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_280",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_280_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_280",
outShow = "AS_CH_IdleShow_PL_280",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_280",
shareTexts = {
"兔兔没有坏心思，只想和你交朋友~"
},
shareAnim = v6,
beginTime = {
seconds = 1746720000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410680.astc",
suitId = 974,
suitName = "兔兔阿卓",
suitIcon = "CDN:Icon_PL_280",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410680.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410680.astc",
ThemedShowIdList = {
{
key = 36,
value = 1
}
}
},
[410690] = {
id = 410690,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "咕咕鹰",
desc = "睡吧，我会守护着你",
icon = "CDN:Icon_Body_042",
getWay = v2,
jumpId = v3,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_042",
upper = "SK_Body_Upper_042",
bottom = "SK_Body_Under_042",
gloves = "SK_Body_Hands_042",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"最喜欢柔和的月光"
},
shareAnim = v6,
beginTime = v7,
suitId = 976,
suitName = "咕咕鹰",
suitIcon = "CDN:Icon_Body_042"
},
[410700] = {
id = 410700,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "天眼萌仔",
desc = "怪兽只想吃星宝的薯片",
icon = "CDN:Icon_BU_275",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_275",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"长得怪，但超可爱"
},
shareAnim = v6,
beginTime = v7,
suitId = 978,
suitName = "天眼萌仔",
suitIcon = "CDN:Icon_BU_275"
},
[410701] = {
id = 410701,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "天眼萌仔",
desc = "怪兽只想吃星宝的薯片",
icon = "CDN:Icon_BU_275_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410700,
fashionValue = 25,
belongToGroup = {
410701
}
},
resourceConf = {
model = "SK_BU_275",
material = "MI_BU_275_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11997,
shareTexts = {
"长得怪，但超可爱"
},
shareAnim = v6
},
[410710] = {
id = 410710,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "春小莹",
desc = "花开了，又刚好是好天气",
icon = "CDN:Icon_BU_268",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_268",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"见山、见水、见春天"
},
shareAnim = v6,
beginTime = v7,
suitId = 980,
suitName = "春小莹",
suitIcon = "CDN:Icon_BU_268"
},
[410711] = {
id = 410711,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "春小莹",
desc = "花开了，又刚好是好天气",
icon = "CDN:Icon_BU_268_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410710,
fashionValue = 25,
belongToGroup = {
410711
}
},
resourceConf = {
model = "SK_BU_268",
material = "MI_BU_268_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11999,
shareTexts = {
"见山、见水、见春天"
},
shareAnim = v6
},
[410720] = {
id = 410720,
effect = true,
name = "木马小甜豆",
desc = "随时准备，陷入甜蜜梦境",
icon = "CDN:Icon_PL_276",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_276",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_276_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_276",
outIdle = "AS_CH_OutIdle_PL_276",
outShow = "AS_CH_IdleShow_PL_276",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_276",
shareTexts = {
"我的梦境，与你有关"
},
shareAnim = v6,
beginTime = {
seconds = 1736006400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410720.astc",
suitId = 982,
suitName = "木马小甜豆",
suitIcon = "CDN:Icon_PL_276",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410720.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_4410720.astc",
ThemedShowIdList = {
{
key = 33,
value = 4
}
}
},
[410730] = {
id = 410730,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "吉伊卡哇装",
desc = "诶嘿嘿~",
icon = "CDN:Icon_BU_194",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_194",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"噜~噜噜啦~"
},
shareAnim = v6,
beginTime = v7,
suitId = 984,
suitName = "吉伊卡哇装",
suitIcon = "CDN:Icon_BU_194"
},
[410740] = {
id = 410740,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "哈奇喵装",
desc = "早上好！",
icon = "CDN:Icon_BU_195",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_195",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"喂~来跳舞吗？"
},
shareAnim = v6,
beginTime = v7,
suitId = 986,
suitName = "哈奇喵装",
suitIcon = "CDN:Icon_BU_195"
},
[410750] = {
id = 410750,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "乌萨奇装",
desc = "呜啦！",
icon = "CDN:Icon_BU_196",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_196",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"呜啦~呀哈呀哈~"
},
shareAnim = v6,
beginTime = v7,
suitId = 988,
suitName = "乌萨奇装",
suitIcon = "CDN:Icon_BU_196"
},
[410760] = {
id = 410760,
effect = true,
name = "绝代智谋 诸葛亮",
desc = "天下如棋，一步三算",
icon = "CDN:Icon_PL_236",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_236",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_236_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_236",
outShow = "AS_CH_IdleShow_PL_236",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_236",
shareTexts = {
"踏入我的棋局"
},
shareAnim = v6,
beginTime = v7,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410760.astc",
suitId = 990,
suitName = "绝代智谋 诸葛亮",
suitIcon = "CDN:Icon_PL_236",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410760.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410760.astc"
},
[410770] = {
id = 410770,
effect = true,
name = "仁德义枪 刘备",
desc = "虚伪和无能是两回事",
icon = "CDN:Icon_PL_235",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_235",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_235_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_235",
outShow = "AS_CH_IdleShow_PL_235",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_235",
shareTexts = {
"适应时代方可生存"
},
shareAnim = v6,
beginTime = v7,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410770.astc",
suitId = 992,
suitName = "仁德义枪 刘备",
suitIcon = "CDN:Icon_PL_235",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410770.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410760.astc"
},
[410780] = {
id = 410780,
effect = true,
name = "菠萝小甜豆",
desc = "海的那边，藏着什么秘密？",
icon = "CDN:Icon_PL_275",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_275",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_275_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_275",
outIdle = "AS_CH_OutIdle_PL_275",
outShow = "AS_CH_IdleShow_PL_275",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_275",
shareTexts = {
"收集旅途的点点滴滴，分享给你"
},
shareAnim = v6,
beginTime = {
seconds = 1736006400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410780.astc",
suitId = 994,
suitName = "菠萝小甜豆",
themedId = 32,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_275",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410780.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410670.astc",
ThemedShowIdList = {
{
key = 32,
value = 2
},
{
key = 33,
value = 6
}
}
},
[410790] = {
id = 410790,
effect = true,
name = "咖啡师阿卓",
desc = "咖啡的秘密，是热情与童真的心",
icon = "CDN:Icon_PL_281",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_281",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_281_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_281",
outShow = "AS_CH_IdleShow_PL_281",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_281",
shareTexts = {
"要来一杯特调咖啡吗？"
},
shareAnim = v6,
beginTime = {
seconds = 1746720000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410790.astc",
suitId = 996,
suitName = "咖啡师阿卓",
suitIcon = "CDN:Icon_PL_281",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410790.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_4410790.astc",
ThemedShowIdList = {
{
key = 36,
value = 2
}
}
},
[410800] = {
id = 410800,
effect = true,
name = "异能熊猫 钦元",
desc = "岁月虽如梭，但见灯火升起，心则安之",
icon = "CDN:Icon_PL_277",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_277",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_277_Physics"
},
outEnter = "AS_CH_Enter_PL_277",
outShow = "AS_CH_IdleShow_PL_277",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_277",
shareTexts = {
"每个星宝都有自己的灵光"
},
shareAnim = v6,
beginTime = v7,
suitStoryTextKey = [[谷底深处，有熊猫村，常年幽暗无光。需掌灯使为村民掌灯引路，星宝钦元即是掌灯使中的一员。

掌灯使中有个不成文的规定，元宵节当天要熄灭所有的灯。钦元不解，询问师父，得知元宵节黑烟将弥漫谷底，讨厌光亮的它会带走擅自亮灯的星宝。

这本该吓退钦元的，可他做了一个梦，梦里熊猫村张灯结彩，花市灯如昼。自那以后，钦元只要睡觉，梦里出现的都是熊猫村繁华亮丽的模样，于是钦元想了个点子。

元宵节那天，熊猫村一片寂静，黑烟潜伏在四周。忽然一盏灯火亮起，钦元举着花灯挑衅黑烟，带着黑烟在村里跑，每经过一家门前他都会敲响窗子，在黑烟没注意到的身后，一盏盏微弱的灯火亮起。等到了村中央，黑烟才发现周围已经全部亮起了灯火！正当黑烟准备动手时，钦元露出无辜的笑容，他的身后突然爆发出强烈的光芒，黑暗中一只编织而成的巨大花灯熊猫显露出来！ 

黑烟消散，钦元看向四周，家家笙歌，满村灯火，该闹元宵了！

自那之后，熊猫村的元宵节灯会成了元梦世界的必玩项目。当问起钦元为何要这么做时，他总是淡淡的说：点亮黑暗，我只是做了我该做的。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410800.astc",
suitId = 998,
suitName = "执掌千灯 钦元",
suitIcon = "CDN:Icon_PL_277",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410800.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410800.astc"
},
[410801] = {
id = 410801,
effect = true,
name = "异能熊猫 钦元",
desc = "岁月虽如梭，但见灯火升起，心则安之",
icon = "CDN:Icon_PL_277_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410800,
fashionValue = 35,
belongToGroup = {
410801,
410802
}
},
resourceConf = {
model = "SK_PL_277",
material = "MI_PL_277_1_HP01;MI_PL_277_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_277_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 12008,
outEnter = "AS_CH_Enter_PL_277",
outShow = "AS_CH_IdleShow_PL_277",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_277",
shareTexts = {
"每个星宝都有自己的灵光"
},
shareAnim = v6,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410800.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410800.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410800.astc"
},
[410802] = {
id = 410802,
effect = true,
name = "异能熊猫 钦元",
desc = "岁月虽如梭，但见灯火升起，心则安之",
icon = "CDN:Icon_PL_277_02",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410800,
fashionValue = 35,
belongToGroup = {
410801,
410802
}
},
resourceConf = {
model = "SK_PL_277",
material = "MI_PL_277_1_HP02;MI_PL_277_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_277_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 12009,
outEnter = "AS_CH_Enter_PL_277",
outShow = "AS_CH_IdleShow_PL_277",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_277",
shareTexts = {
"每个星宝都有自己的灵光"
},
shareAnim = v6,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410800.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410800.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410800.astc"
},
[410810] = {
id = 410810,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "温小雪",
desc = "围巾也藏不住想要温暖你的心",
icon = "CDN:Icon_BU_276",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_276",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"倒春寒？不怕的"
},
shareAnim = v6,
beginTime = v7,
suitId = 1000,
suitName = "温小雪",
suitIcon = "CDN:Icon_BU_276"
},
[410811] = {
id = 410811,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "温小雪",
desc = "围巾也藏不住想要温暖你的心",
icon = "CDN:Icon_BU_276_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410810,
fashionValue = 25,
belongToGroup = {
410811
}
},
resourceConf = {
model = "SK_BU_276",
material = "MI_BU_276_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12011,
shareTexts = {
"倒春寒？不怕的"
},
shareAnim = v6
},
[410820] = {
id = 410820,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "牡丹丹",
desc = "国色天香，说的就是我啦",
icon = "CDN:Icon_BU_272",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_272",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"生于洛阳，闻于天下"
},
shareAnim = v6,
beginTime = v7,
suitId = 1002,
suitName = "牡丹丹",
suitIcon = "CDN:Icon_BU_272"
},
[410821] = {
id = 410821,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "牡丹丹",
desc = "国色天香，说的就是我啦",
icon = "CDN:Icon_BU_272_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410820,
fashionValue = 25,
belongToGroup = {
410821
}
},
resourceConf = {
model = "SK_BU_272",
material = "MI_BU_272_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12013,
shareTexts = {
"生于洛阳，闻于天下"
},
shareAnim = v6
},
[410830] = {
id = 410830,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星波波",
desc = "星梦世界就是最美好的地方",
icon = "CDN:Icon_BU_277",
getWay = v2,
jumpId = v3,
outlookConf = v5,
resourceConf = {
model = "SK_BU_277",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"也曾经在太空游荡"
},
shareAnim = v6,
beginTime = v7,
suitId = 1004,
suitName = "星波波",
suitIcon = "CDN:Icon_BU_277"
},
[410831] = {
id = 410831,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星波波",
desc = "星梦世界就是最美好的地方",
icon = "CDN:Icon_BU_277_01",
getWay = v2,
jumpId = v3,
outlookConf = {
belongTo = 410830,
fashionValue = 25,
belongToGroup = {
410831
}
},
resourceConf = {
model = "SK_BU_277",
material = "MI_BU_277_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12015,
shareTexts = {
"也曾经在太空游荡"
},
shareAnim = v6
},
[405262] = {
id = 405262,
effect = true,
name = "二头身测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_998",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_998_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405265] = {
id = 405265,
effect = true,
name = "993测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_993",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_993_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405266] = {
id = 405266,
effect = true,
name = "992测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_992",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_992_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405267] = {
id = 405267,
effect = true,
name = "991测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_991",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_991_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405268] = {
id = 405268,
effect = true,
name = "990测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_990",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_990_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405269] = {
id = 405269,
effect = true,
name = "998测试",
desc = "998测试",
icon = "CDN:Icon_Body_010",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_OG_998",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_998_Physics"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
}
},
[405270] = {
id = 405270,
effect = true,
name = "997测试",
desc = "测试测试",
icon = "CDN:Icon_Body_010",
getWay = v2,
jumpId = v3,
resourceConf = {
model = "SK_PL_997",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_997_Physics",
skeletalMesh = "SK_PL_997"
},
outShowIntervalTime = 10,
shareTexts = {
"测试测试"
},
billboardOffsetZ = 50,
bodytype = 2
},
[490031] = {
id = 490031,
effect = true,
name = "轻型兽人",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_010_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490032] = {
id = 490032,
effect = true,
name = "中型兽人",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_011",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_011_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490033] = {
id = 490033,
effect = true,
name = "兽人射手",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_012",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_012_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490034] = {
id = 490034,
effect = true,
name = "巨怪",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_013",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_013_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490035] = {
id = 490035,
effect = true,
name = "豺狼人猎手",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_014",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_014_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490036] = {
id = 490036,
effect = true,
name = "山地巨魔",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_015",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_015_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490037] = {
id = 490037,
effect = true,
name = "克拉格",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_016",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_016_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490038] = {
id = 490038,
effect = true,
name = "大富翁先生",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_017",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_017",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490039] = {
id = 490039,
effect = true,
name = "狗头人工兵",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_018",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_018_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490040] = {
id = 490040,
effect = true,
name = "巨魔",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_019",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_019_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490041] = {
id = 490041,
effect = true,
name = "轻型火魔",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_020",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_020_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490042] = {
id = 490042,
effect = true,
name = "地狱魔婴",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_021",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_021",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490043] = {
id = 490043,
effect = true,
name = "地狱蝙蝠",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_022",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_022",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[470001] = {
id = 470001,
effect = true,
name = "狼人杀换装",
icon = "CDN:Icon_Body_010",
useType = v4,
resourceConf = {
model = "AS_NR3E3_SK_SpaceSuit_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[479001] = {
id = 479001,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "好好鸭",
desc = "每天都要开心鸭",
icon = "CDN:Icon_Body_011",
getWay = v2,
jumpId = v3,
useType = v4,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_011",
upper = "SK_Body_Upper_011",
bottom = "SK_Body_Under_011",
gloves = "SK_Body_Hands_011",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareAnim = v6
},
[470002] = {
id = 470002,
effect = true,
name = "狼人杀换装-黑衣狼",
icon = "CDN:Icon_Body_010",
useType = v4,
resourceConf = {
model = "NR3E3_SK_BlackWolf_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[479002] = {
id = 479002,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "小红狐",
desc = "灵动似风，狐步生花路",
icon = "CDN:Icon_Body_020",
getWay = "冲段挑战",
jumpId = {
39
},
useType = v4,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_020",
upper = "SK_Body_Upper_020",
bottom = "SK_Body_Under_020",
gloves = "SK_Body_Hands_020",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareAnim = v6
},
[479003] = {
id = 479003,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "紫萝萝",
desc = "粘人是我的特权呀！",
icon = "CDN:Icon_Body_017",
getWay = v2,
jumpId = v3,
useType = v4,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_017",
upper = "SK_Body_Upper_017",
bottom = "SK_Body_Under_017",
gloves = "SK_Body_Hands_017",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareAnim = v6
},
[490044] = {
id = 490044,
effect = true,
name = "兽人23",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_023_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490045] = {
id = 490045,
effect = true,
name = "兽人24",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_024_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490046] = {
id = 490046,
effect = true,
name = "冰霜蝙蝠",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_025",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_025_LOD0",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490047] = {
id = 490047,
effect = true,
name = "兽人26",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_026_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490048] = {
id = 490048,
effect = true,
name = "兽人27",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_027_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490049] = {
id = 490049,
effect = true,
name = "豺狼人投弹手",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_028",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_028",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
},
[490050] = {
id = 490050,
effect = true,
name = "兽人29",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = v4,
resourceConf = {
model = "SK_UGC_Monster_029_LOD1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
}
}
}
}

local mt = {
effect = false,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
quality = 2,
useType = "IUTO_None",
outlookConf = {
fashionValue = 300
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data