--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城- 星梭

local data = {
[500001] = {
mallId = 134,
commodityId = 500001,
commodityName = "星梭-微甜盛夏",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1722268800
},
endTime = {
seconds = 1755619199
},
shopTag = {
7,
34
},
jumpId = 158,
jumpText = "充值送星梭",
gender = 0,
itemIds = {
740004
},
itemNums = {
1
},
bOpenSuit = true,
suitId = 30004
},
[500002] = {
mallId = 134,
commodityId = 500002,
commodityName = "星梭-星辰日晷",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = {
7,
34
},
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.26.29",
itemIds = {
740006
},
itemNums = {
1
},
bOpenSuit = true,
suitId = 30006
},
[500003] = {
mallId = 134,
commodityId = 500003,
commodityName = "星梭-星辰日晷",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = {
7,
34
},
jumpId = 1082,
jumpText = "遗落珍宝",
gender = 0,
minVersion = "1.3.26.29",
itemIds = {
740005
},
itemNums = {
1
},
bOpenSuit = true,
suitId = 30005
}
}

local mt = {
bOpenSuit = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data