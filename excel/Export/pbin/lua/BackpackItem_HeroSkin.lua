--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_Arena.xlsx: 皮肤

local v0 = "AS_CH_OutIdle_001"

local v1 = 10

local v2 = 100

local v3 = {
"爱就是组成我的元件",
"人类对孤独的理解是有限的",
"我的一小步,见证友谊的一大步",
"哼，你咋不上天呢？",
"我会飞得更高以求生存"
}

local v4 = {
0,
0
}

local v5 = "峡谷英雄皮肤"

local data = {
[405050] = {
id = 405050,
effect = true,
name = "铠",
desc = "（限峡谷模式使用）获取后可以使用战士铠。",
icon = "t_arena_role_1002",
resourceConf = {
model = "sk_pl_084_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_084_dev_physics"
},
outEnter = "as_ch_enter_pl_084_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_084_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_084",
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "家族所看守的奇迹碎片并非奇迹，而是诅咒，背负诅咒的子嗣最终将在厮杀下融为一体--认知到这一事实的青年被不甘与愤怒支配，于月夜下挥刀屠戮整个家族。利刃却在最后幸存的妹妹面前犹豫，他就此放弃，向东漂泊，却在长城为了拯救某个少年而被魔铠吞噬，失去了所有记忆。花木兰的邀请使他留下，魔道利刃自此化作守护之铠。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[405051] = {
id = 405051,
effect = true,
name = "孙策",
desc = "（限峡谷模式使用）获取后可以使用坦克孙策。",
icon = "t_arena_role_1001",
resourceConf = {
model = "sk_pl_085_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_085_moba_physics"
},
outEnter = "as_ch_enter_pl_085_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_085_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_085",
shareTexts = v3,
suitStoryTextKey = "人称“江东小霸王”的孙策仿佛注定为了结束吴的纷乱割据而生他虽出身名门，却豪爽、热情，无畏，也不屑与腐败者们同流合污。他与挚友周瑜联手领军，清洗着江郡奢靡堕落的腐败豪族。某次预料外的海难将他和大乔的命运牵连在一起少女手中的明灯成了指引他们闯出风浪的向标。灵魂触动一眼万年，他一往无前的心更加坚定。",
bHideInBag = true,
shareOffset = v4
},
[405052] = {
id = 405052,
effect = true,
name = "小乔",
desc = "（限峡谷模式使用）获取后可以使用法师小乔。",
icon = "t_arena_role_1007",
resourceConf = {
model = "sk_pl_086_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_086_moba_physics"
},
outEnter = "as_ch_enter_pl_086_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_086_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_086",
shareTexts = v3,
suitStoryTextKey = "对于江东乔氏而言，双胞胎是不详的征兆，大小乔自出生起便被迫分开。小乔被送往稷下直至长大才得以回家，但族人震惊其魔道天赋，唯恐魔道家族的秘密因此暴露，又将小乔送往东海边小镇。不久，小镇疫病横行引发暴乱，小乔召唤海风吹散毒雾时，被前来镇压的周瑜当成嫌犯，谁也没想到这次邂逅，却是两人情愫的开始。",
bHideInBag = true,
shareOffset = v4
},
[405053] = {
id = 405053,
effect = true,
name = "兰陵王",
desc = "（限峡谷模式使用）获取后可以使用刺客兰陵王。",
icon = "t_arena_role_1005",
resourceConf = {
model = "sk_pl_087_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_087_moba_physics"
},
outEnter = "as_ch_enter_pl_087_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_087_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "曾经高贵的兰陵王，在命运转折下痛失一切，无情的帝国铁骑将王都城池踏破，断垣残壁下埋藏着仇恨的种子。神秘老人将他带走，再度出现他已成为潜行于黑暗的杀戮者，复仇则是唯一的使命。而长城，将作为堡垒承受他所有的怒火。暗影之下，曾经的王终会夺回属于他的一切，哪怕前路漫漫哪怕将有那绯红身影前来阻拦。",
bHideInBag = true,
shareOffset = v4
},
[405054] = {
id = 405054,
effect = true,
name = "鲁班七号",
desc = "（限峡谷模式使用）获取后可以使用射手鲁班七号。",
icon = "t_arena_role_1004",
resourceConf = {
model = "sk_pl_022_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outEnter = "as_ch_enter_pl_022_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_022_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "鲁班七号是鲁班大师的天才造物，是机关术能制造出来的最精巧的人偶。短胳膊短腿，可爱又可怕，小身材有大能量，具有超强破坏力。因为血族之乱，鲁班被墨子带去邻城支援玄雍，可是，它在途中丢失……在它尚未成形的语言中，人们似乎能听出它对于父亲的崇拜和思念。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[405055] = {
id = 405055,
effect = true,
name = "铠（变身后）",
desc = "铠（变身后）",
icon = "t_arena_role_1002",
resourceConf = {
model = "sk_pl_084_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_084_dev_physics"
},
outIdle = v0,
scaleTimes = 100,
shareAnim = "AS_CH_Pose_001_PL_022",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[405056] = {
id = 405056,
effect = true,
name = "瑶",
desc = "（限峡谷模式使用）获取后可以使用辅助瑶。",
icon = "t_arena_role_1006",
resourceConf = {
model = "sk_pl_023_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_023_moba_physics"
},
outEnter = "as_ch_enter_pl_023_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_023_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "她本是林中幼鹿，在战争中丧命，却又在命运的眷顾下重生天真烂漫的阿瑶简直是古灵精怪的代名词，她像只小鹿般在幽谧的林中自由自在地奔跑跳跃，又莽莽撞撞闯到大河以西，用那变化多端的恶作剧引起一片恐慌。云中君说阿瑶就像个梦，闪耀着森林自然之光的梦，殊不知，梦境的彼岸便是曾经的羁绊与现实。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
shareOffset = v4
},
[405057] = {
id = 405057,
effect = true,
name = "亚瑟",
desc = "（限峡谷模式使用）获取后可以使用坦克亚瑟。",
icon = "t_arena_role_1011",
resourceConf = {
model = "sk_pl_127_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_127_moba_physics"
},
outEnter = "as_ch_enter_pl_127_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_127_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "同盟国的军队看准圣骑士团的日益衰弱，屡次发动突袭。第四次的攻击却终于一名青年之手，他手握“誓约胜利之剑”，单枪匹马剿灭了残忍的叛徒，守住了象征圣骑士团荣耀与尊严的圣殿。根据代代相传的古老条约，拔出这把剑的人将获得圣骑士们的宣誓与效忠，被冠以“亚瑟王”的神圣名号。新的亚瑟，新的时代，骑士精神永不磨灭。",
bHideInBag = true,
shareOffset = v4
},
[405058] = {
id = 405058,
effect = true,
name = "后羿",
desc = "（限峡谷模式使用）获取后可以使用射手后羿。",
icon = "t_arena_role_1012",
resourceConf = {
model = "sk_pl_156_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_156_dev_physics"
},
outEnter = "as_ch_enter_pl_156_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_156_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "后羿作为这世间绝无仅有的英勇射手，曾受到统治者的青睐与赞扬。奉命摧毁日之塔的过程中，他被这宏伟奇迹的力量吸引……然统治者的心思永远更为缜密，摧毁了最后一座日之塔的后羿肆意汲取其中能量，天罚却也几乎同时降至，他的光芒被熄灭、埋没入雪原，在千百年间等待一个人将其再次点亮，射出更璀璨的光。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[405059] = {
id = 405059,
effect = true,
name = "蔡文姬",
desc = "（限峡谷模式使用）获取后可以使用辅助蔡文姬。",
icon = "t_arena_role_1013",
resourceConf = {
model = "sk_pl_131_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_131_dev_physics"
},
outEnter = "as_ch_enter_pl_131_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_131_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "即便生活在三分乱世之地，蔡文姬也拥有纯挚的内心。她怀念已故的父亲，崇敬抚养自己的义父，信任身边沉默的典韦，但关于父亲的去世，义父说凶手是诸葛亮，典韦却闪烁其词。某日终于迎来真相，当典韦让她带着玉玺逃离武都时，耳畔又响起爹爹的声音:未来不管发生什么，爹爹都希望文姬能简单快乐地长大，倘若终将面临困惑，希望你终能用爱访过它……",
bHideInBag = true,
shareOffset = v4
},
[405060] = {
id = 405060,
effect = true,
name = "赵云",
desc = "（限峡谷模式使用）获取后可以使用战士赵云。",
icon = "t_arena_role_1010",
resourceConf = {
model = "sk_pl_126_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_126_moba_physics"
},
outEnter = "as_ch_enter_pl_126_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_126_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "最强佣兵团“龙”背后之人，被称作影子。曹操巧妙撩拨着龙的野心，影子从中嗅到了阴谋的气息，无法说服龙的影子只能退出组织。当佣兵团受命与吕布一战，却被屠戮殆尽时，影子现身与龙合力杀死了吕布后，又随之离去，并从此消失。数年后，当曹操大军席卷三分之地时，曾经站在龙背后的影子，以赵子龙之名于长坂坡参见。",
bHideInBag = true,
shareOffset = v4
},
[405061] = {
id = 405061,
effect = true,
name = "安琪拉",
desc = "（限峡谷模式使用）获取后可以使用法师安琪拉。",
icon = "t_arena_role_1003",
resourceConf = {
model = "sk_pl_128_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_128_dev_physics"
},
outEnter = "as_ch_enter_pl_128_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_128_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "她可不是如外表般纯良无害的可爱萝莉--大魔法师梅林因一次失手将自己困在气泡的囚笼，迫不得已才以灵魂占据了路过女孩的身体。然而命运的捉弄使得她与过去的主人亚瑟重逢，曾经的不满与愤恨一并倾泻而出，她势必要来一场以潜伏为前提的恶毒复仇!只是，这具身体对亚瑟的奇妙仰慕还真令她有些招架不及呀……",
bHideInBag = true,
shareOffset = v4
},
[405062] = {
id = 405062,
effect = true,
name = "孙悟空",
desc = "（限峡谷模式使用）获取后可以使用刺客孙悟空。",
icon = "t_arena_role_1016",
resourceConf = {
model = "sk_pl_130_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_130_dev_physics_moba"
},
outEnter = "as_ch_enter_pl_130_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_130_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "孙悟空生性桀骜，厌恶被管辖和拘束，更憎恶那些虚伪神灵铐在魔种身上的枷锁。黑暗的时代里，他俨然成为反抗的领袖，带领魔种们为自由奋起。起义以失败告终，神灵以绝对的力量击溃了乌合之众，将他封印在某座山脚……然而他的意志没有熄灭，某位路过的僧侣帮助孙悟空冲破束缚重生，齐天大圣的名号再度威震八方。",
bHideInBag = true
},
[405063] = {
id = 405063,
effect = true,
name = "孙尚香",
desc = "（限峡谷模式使用）获取后可以使用射手孙尚香。",
icon = "t_arena_role_1014",
resourceConf = {
model = "sk_pl_129_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_129_dev_physics"
},
outEnter = "as_ch_enter_pl_129_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_129_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "自长兄孙策奇袭江东建立新的国家开始，孙尚香便为了理想而战斗，但小公主似乎更擅长制造麻烦，她的枪炮甚至令海上的海盗都闻风丧胆。通往理想之路上，长兄不幸遇刺身亡使她不得不面对压力，在象征着责任的王位面前，她最终选择了用手中的炮火实现已故兄长的理想乡。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true
},
[405064] = {
id = 405064,
effect = true,
name = "李白",
desc = "（限峡谷模式使用）获取后可以使用刺客李白。",
icon = "t_arena_role_1019",
resourceConf = {
model = "sk_pl_157_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_157_dev_physics"
},
outEnter = "as_ch_enter_pl_157_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_157_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "随着朱雀门上刻下的剑痕，李白剑仙之名传遍长安，随后，他潇洒离去，在试剑游历中成为帝国第一强者。他也曾返回故乡云中漠地，但战火已将一切过往繁华化作废墟，质疑与愤怒令他剑指长安，然而，面对女帝的他，结局竟是失意而归，时隔数年，被人们认为一蹶不振的剑仙再度归来，这次他的剑将让整座长安为之动摇。",
bHideInBag = true
},
[405065] = {
id = 405065,
effect = true,
name = "云缨",
desc = "（限峡谷模式使用）获取后可以使用战士云缨。",
icon = "t_arena_role_1017",
resourceConf = {
model = "sk_pl_159_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_159_dev_physics"
},
outEnter = "as_ch_enter_pl_159_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_159_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = [[“哪里有麻烦，哪里就有云缨~!”
这位风风火火，手持长枪的少女，正是长安城现下出尽风头的大理寺新锐。无论遇到什么麻烦，她都会点燃热血抢先为您效劳。只要不担心留下更多的麻烦……]],
timeToStartAnim = 2.4700000286102295,
bHideInBag = true
},
[405066] = {
id = 405066,
effect = true,
quality = 2,
name = "偶像歌手 王昭君",
desc = "获取后可作为峡谷模式中王昭君的皮肤使用。",
icon = "t_arena_role_1018_01",
getWay = "峡谷幻梦",
resourceConf = {
model = "sk_pl_160_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_160_dev_physics"
},
outEnter = "as_ch_enter_pl_160_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_160_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outPropSkeletal = "SK_PL_Prop_160_dev",
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
beginTime = {
seconds = 1726848000
},
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
level = 4
},
[405067] = {
id = 405067,
effect = true,
name = "墨子",
desc = "（限峡谷模式使用）获取后可以使用法师墨子。",
icon = "t_arena_role_1020",
resourceConf = {
model = "sk_pl_187_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_187_moba_physics"
},
outEnter = "as_ch_enter_pl_187_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_187_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "因为墨子在机关术上的天赋与成就，人们称其为机关术宗师;因为墨子踏遍上古遗迹，联合他人打造稷下学院，人们称其为贤者;因为墨子打造长安并亲自以机关术守护，人们又称其为和平守护者。没人想到当初被唤作墨翟的普通青年工匠，如今能驾驶机关人所向披靡，成为那些觊觎长安城的宵小之辈心中的梦魇。",
bHideInBag = true
},
[405068] = {
id = 405068,
effect = true,
name = "钟馗",
desc = "（限峡谷模式使用）获取后可以使用辅助钟馗。",
icon = "t_arena_role_1015",
resourceConf = {
model = "sk_pl_158_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_158_dev_physics"
},
outEnter = "as_ch_enter_pl_158_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_158_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "钟馗存在于长安城神秘的“门”中，那里通往虚空，也通往一切智慧根源。作为门内之物的守护者，它比长安城还要更加古老，每个午夜子时，它都会从门中来到朱雀大街，巡视着这座在白日里无比繁华的城市，夜幕之下，一切试图破坏长安之人，都会被它吸入混沌之中，人们视它为“鬼”。而它，只是在执行着守护的指令。",
bHideInBag = true
},
[405069] = {
id = 405069,
effect = true,
name = "公孙离",
desc = "（限峡谷模式使用）获取后可以使用射手公孙离。",
icon = "t_arena_role_1021",
resourceConf = {
model = "sk_pl_188_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_188_moba_physics"
},
outEnter = "as_ch_enter_pl_188_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_188_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "阿离凭借勤奋刻苦，以曼妙柔美的舞姿从一百个孩子中脱颖而出。这之后她真正的舞台不再只限于长乐坊的灯光下一名扬长安的舞技成了她出入各地绝佳的掩饰，真正工作是在方士指引下妥善处理各路文书，在黑暗中维护长安的正义。少女像是个枫叶中的青涩精灵，一半是跃动着的甜美，另-半则是不为人知的、折柳相送的思念。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true
},
[405070] = {
id = 405070,
effect = true,
quality = 2,
name = "记忆之芯 公孙离",
desc = "获取后可作为峡谷模式中公孙离的皮肤使用。",
icon = "t_arena_role_1021_01",
getWay = "峡谷幻梦",
resourceConf = {
model = "sk_pl_162_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_162_moba_physicsasset"
},
outEnter = "as_ch_enter_pl_162_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_162_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
beginTime = {
seconds = 1726848000
},
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
level = 4
},
[405071] = {
id = 405071,
effect = true,
name = "花木兰",
desc = "（限峡谷模式使用）获取后可以使用战士花木兰。",
icon = "t_arena_role_1024",
resourceConf = {
model = "sk_pl_182_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_182_moba_physics"
},
outEnter = "as_ch_enter_pl_182_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_182_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "花木兰身为女性，却有着不输任何男性的热血豪情，她自愿请缨镇守长城，即便因歹人暗算而背负叛徒的污名，依旧徘徊在这片边疆为守护而战。她以超绝的领导力和毋庸置疑的实力召集各路强者，击退各路魔种和马贼。张扬跋扈的亮眼红发与轻重交替的高超剑术使她成为战场上一道靓丽的风景。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[405072] = {
id = 405072,
effect = true,
quality = 2,
name = "蔷薇精灵 孙策",
desc = "获取后可作为峡谷模式中孙策的皮肤使用。",
icon = "t_arena_role_1001_01",
getWay = "赛季祈愿",
resourceConf = {
model = "sk_pl_165_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_165_moba_physicsasset"
},
outEnter = "as_ch_enter_pl_165_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_165_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outPropEnter = "AS_CH_Enter_PL_165_Prop_moba",
outPropShow = "AS_CH_IdleShow_PL_165_Prop_moba",
outPropSkeletal = "SK_PL_Prop_165_dev",
beginTime = {
seconds = 1724947200
},
bHideInBag = true,
shareOffset = v4,
level = 4
},
[405073] = {
id = 405073,
effect = true,
quality = 1,
name = "花之语  瑶",
desc = "获取后可作为峡谷模式中瑶的皮肤使用。",
icon = "T_Arena_Role_1006_01",
getWay = "赛季祈愿",
resourceConf = {
model = "SK_OG_022_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_OG_022_moba_PhysicsAsset"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_022_PV/Level_OG_022_Intact",
outIdle = v0,
outShow = "AS_CH_IdleShow_OG_022_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outPropSkeletal = "SK_OG_Prop_022_dev",
soundId = {
4042,
4044
},
outEnterSequence = "LS_PV_OG_022",
beginTime = {
seconds = 1724947200
},
bHideInBag = true,
level = 6
},
[405074] = {
id = 405074,
effect = true,
name = "王昭君",
desc = "（限峡谷模式使用）获取后可以使用法师王昭君。",
icon = "t_arena_role_1018",
resourceConf = {
model = "sk_pl_186_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_186_moba_physicsasset"
},
outEnter = "as_ch_enter_pl_186_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_186_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "王昭君本是中原送来和亲的公主，早已被依照习俗献祭于圣地“凛冬之海”。多年后，贪婪的中原人趁又一个祭祀之日发起突袭，上演了一出染红雪原的“血色婚礼”。然而掠夺却止于暴雪降至，歹徒们被冰封于由雪崩代表的神罚。被拥戴的公主昭君悄然抚摸一只只冰棺，清冷的眼眸中却始终蕴含一丝眷恋与哀伤。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true
},
[405075] = {
id = 405075,
effect = true,
name = "东皇太一",
desc = "（限峡谷模式使用）获取后可以使用坦克东皇太一。",
icon = "t_arena_role_1022",
resourceConf = {
model = "sk_pl_184_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_184_moba_physics",
skeletalMesh = "SK_PL_184_moba",
animClassName = "Bp_DongHuangTaiYi_Avatar_PreviewAnimInstance"
},
outEnter = "as_ch_enter_pl_184_moba",
outIdle = "YM_MOBA_184_Idle_IdleShow",
outShow = "AS_CH_IdleShow_PL_184_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "他不甘于在平庸中走向必然消逝的结局，于是接受巫神祝试炼成为强大的神巫东皇太一。然而当奇迹的真相暴露在眼前昭示着一切从未改变--他毅然选择吞噬奇迹之力，进化半神之姿。如今，他高居云梦泽顶端的宫殿，受全知者们的敬仰，但这远非终点--总有一日，他将夺取全部的奇迹之力，成为真正的、唯一的神明。",
bHideInBag = true,
shareOffset = v4
},
[405076] = {
id = 405076,
effect = true,
name = "狄仁杰",
desc = "（限峡谷模式使用）获取后可以使用射手狄仁杰。",
icon = "t_arena_role_1023",
resourceConf = {
model = "sk_pl_183_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_183_moba_physics"
},
outEnter = "as_ch_enter_pl_183_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_183_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "雷厉风行而又不计后果的清洗行动，使狄仁杰之名被无数违法者憎恶，他是完美主义的侦探，也是长安城最可靠的守护者，女帝武则天信赖他，从封印姜子牙开始，他就是女帝最得力的助手，而长安城崩溃的地下世界，也证明着这位治安官非凡的实力，他的眼睛为女帝注视着一切，任何值得怀疑之人，他都绝对不会放过。",
bHideInBag = true,
shareOffset = v4
},
[405077] = {
id = 405077,
effect = true,
name = "妲己",
desc = "（限峡谷模式使用）获取后可以使用法师妲己。",
icon = "t_arena_role_1025",
resourceConf = {
model = "sk_pl_222_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_222_moba_physicsasset"
},
outEnter = "as_ch_enter_pl_222_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_222_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "以机关术为躯，以魔道术为灵魂，没有心的人偶被主人姜子牙献给纣王。她被新主人唤作妲己，但没有心就没有感情，她甚至不确定自己是不是爱上了纣王……她的心在哪?神秘的声音指引妲己打开了纣王爱人的寝陵，作为魂器的纣王心脏随着棺木打开而破碎。妲己和那灵魂在纠缠中沉睡，再度醒来时，仅有寻找“心”的夙愿格外清晰。",
bHideInBag = true,
shareOffset = v4
},
[405078] = {
id = 405078,
effect = true,
name = "曜",
desc = "（限峡谷模式使用）获取后可以使用战士曜。",
icon = "t_arena_role_1026",
resourceConf = {
model = "sk_pl_223_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_223_moba_lod1_physicsasset"
},
outEnter = "as_ch_enter_pl_223_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_223_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "曜从小是一个争强好胜的热血少年，以李白为偶像，希望能成为如他一般潇洒骄傲的剑客。进入稷下学习后，通过组建星之队参加庄周举行的归虚梦演大赛，他获得了友谊、能量和自我认知。星之队夺得了第一，然而他的英雄之旅才刚刚开始…",
bHideInBag = true,
shareOffset = v4
},
[405079] = {
id = 405079,
effect = true,
name = "马可波罗",
desc = "（限峡谷模式使用）获取后可以使用射手马可波罗。",
icon = "t_arena_role_1027",
resourceConf = {
model = "sk_pl_224_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_224_moba_Physics"
},
outEnter = "as_ch_enter_pl_224_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_224_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "他是个不折不扣的旅行者、冒险家、语言天才。他自由、热烈，旺盛的求知欲使他从小热衷于拆装古怪的机关。向芬奇大师请教的过程中，围绕“死海文书”的故事唤起他关于失踪父亲的回忆。惊醒的马可找出了父亲遗留下的铭文，破解其中的讯息，了解到自己的宿命。他毅然决定追寻父亲的足迹，前往遥远的东方大陆，寻求知识的根源。",
bHideInBag = true,
shareOffset = v4
},
[405080] = {
id = 405080,
effect = true,
name = "程咬金",
desc = "（限峡谷模式使用）获取后可以使用坦克程咬金。",
icon = "t_arena_role_1030",
resourceConf = {
model = "SK_PL_228_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_228_moba_Physics"
},
outEnter = "AS_CH_Enter_PL_228_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_228_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "一身健美的肌肉，饱含铁血的男儿柔情，说的便是口口相传的名将程咬金。他认为力量从来不是争斗与厮杀的武器，而是挑战自然的资本。但与偶然结识的友人李靖共同退治暴徒的经历使他改观，自此，他周游各地，守护弱小，惩恶扬善，誓要让肌肉和汗水在太阳照不到的黑暗角落里散发光辉。",
bHideInBag = true,
shareOffset = v4
},
[405081] = {
id = 405081,
effect = true,
name = "虞姬",
desc = "（限峡谷模式使用）获取后可以使用射手虞姬。",
icon = "t_arena_role_1031",
resourceConf = {
model = "sk_pl_232_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_232_moba_Physics"
},
outEnter = "as_ch_enter_pl_232_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_232_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "作为姜子牙的得意弟子之一，虞姬与自然为伍，同藤萝鸟儿相伴，射出的每一支箭矢都带有树神的祝福。反抗阴阳家暴政的过程中，她与英勇无畏的项羽堕入爱河，大战前夕，久别的师兄却前来告知，项羽注定为要世界带来混沌……来不及反驳命运的虞姬已然落入幻术的圈套，该下战场，那支穿透一切的利箭悄然对准爱人的心脏……",
bHideInBag = true,
shareOffset = v4
},
[405082] = {
id = 405082,
effect = true,
name = "吕布",
desc = "（限峡谷模式使用）获取后可以使用战士吕布。",
icon = "t_arena_role_1032",
resourceConf = {
model = "sk_pl_229_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_229_moba_Physics"
},
outEnter = "as_ch_enter_pl_229_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_229_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "王者大陆传颂着战神吕布的强大，但强如吕布也曾与死神擦肩而过。吕布躺在泥泞中奄奄一息时，一个小女孩递上的清水拯救了他，但从梦中被马蹄声惊醒的吕布，疑心被出卖杀光了小女孩一伙。此后战神几经浮沉，一度摸到天下，却被一个女人的舞蹈唤回当年的回忆。这一次，倒下的吕布，在漆黑中，听到一个声音说着“你可以选择的”。",
bHideInBag = true,
shareOffset = v4
},
[405083] = {
id = 405083,
effect = true,
name = "武则天",
desc = "（限峡谷模式使用）获取后可以使用法师武则天。",
icon = "t_arena_role_1033",
resourceConf = {
model = "sk_pl_231_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_186_moba_physicsasset"
},
outEnter = "as_ch_enter_pl_186_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_186_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outPropEnter = "AS_CH_Enter_PL_186_Prop_moba",
outPropShow = "AS_CH_IdleShow_PL_186_Prop_moba",
outPropSkeletal = "SK_PL_Prop_186_dev",
shareTexts = v3,
suitStoryTextKey = "武则天曾因美貌和魔道天赋被王室囚禁，少女怀抱满腔不甘与愤怒，成为太古魔导姜子牙的弟子。为换得自由，她同意继承老者肃清魔种的意志。然而誓言与乖顺只是表象，当她真的触及王位，权利的美妙则使野心加剧膨胀。她不惜与信赖的治安官联合封印老者，不起眼的棋子从此成为棋局的掌控者，而这个世界，也终将为她而臣服。",
bHideInBag = true,
shareOffset = v4
},
[405084] = {
id = 405084,
effect = true,
name = "宫本武藏",
desc = "（限峡谷模式使用）获取后可以使用战士宫本武藏。",
icon = "t_arena_role_1034",
resourceConf = {
model = "sk_pl_225_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_225_moba_PhysicsAsset"
},
outEnter = "as_ch_enter_pl_225_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_225_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "左手长刀，右手短剑，无人可挡的高超剑势令宫本武藏人挡杀人，佛挡杀佛。他力挑天下武道会的冠军，斩杀于扶桑兴风作浪的血族徐福，却因苦于再无对手，动身前往强者汇聚的长安，要找剑仙李白，治安官狄仁杰等人一较高下。大概他无敌人生中唯一的苦恼就是，今天好像又迷路了。",
bHideInBag = true,
shareOffset = v4
},
[405085] = {
id = 405085,
effect = true,
name = "阿轲",
desc = "（限峡谷模式使用）获取后可以使用刺客阿轲。",
icon = "t_arena_role_1035",
resourceConf = {
model = "sk_pl_252_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_252_moba_Physics"
},
outEnter = "as_ch_enter_pl_252_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_252_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "大周分崩，诸国并起，世代以刺客为生的荆氏一门仅存两兄妹相依为命。兄长拒绝了燕太子丹刺杀昔日好友樊於期的任务，次日，樊於期拜访荆氏兄长时，一同被太子丹杀害。太子丹志得意满，准备以二人人头重树地位，却在宴会上迎来了荆氏一脉最后的少女。宴会琴师救走了少女，但少女却只记得“阿轲”之名与止战的意志。",
bHideInBag = true,
shareOffset = v4
},
[405086] = {
id = 405086,
effect = true,
name = "典韦",
desc = "（限峡谷模式使用）获取后可以使用战士典韦。",
icon = "t_arena_role_1036",
resourceConf = {
model = "sk_pl_234_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_234_moba_physics"
},
outEnter = "as_ch_enter_pl_234_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_234_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "狂野的兽性与超凡的力量让曾经的典韦备受恐惧与唾弃，直到与恩师蔡邕的邂逅将他从绝望的边缘拉回--循循善诱的长者与其活泼可爱的幼小女儿成为了照亮深邃黑暗的光。他以为自已终将为人，然而枭雄曹操的阴谋再次将他推回深渊，蔡邕逝世，臣服于曹操是他的选择，理智或许会因嗜血兽性而淡化，但守护珍视之人的信念绝不退减。",
bHideInBag = true,
shareOffset = v4
},
[405087] = {
id = 405087,
effect = true,
name = "甄姬",
desc = "（限峡谷模式使用）获取后可以使用法师甄姬。",
icon = "t_arena_role_1037",
resourceConf = {
model = "sk_pl_239_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_239_moba_physics"
},
outEnter = "as_ch_enter_pl_239_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_239_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "因着高贵出身，圣者血脉，甄姬在家族的培养下成为了温柔仁慈闻名的绝代美女。尽管自出生起便被要求克制欲望，肩负感化世人净化污秽的责任，但甄姬仍在十八岁时萌动了春心。面对翩翩才子的疯狂追求，甄姬沦陷得比自己想象的更为彻底，抛弃家族，放弃责任。只是她如愿嫁入曹家时，才发现自己所爱的人，爱的只是自己的力量。",
bHideInBag = true,
shareOffset = v4
},
[405088] = {
id = 405088,
effect = true,
name = "伽罗",
desc = "（限峡谷模式使用）获取后可以使用射手伽罗。",
icon = "t_arena_role_1038",
resourceConf = {
model = "sk_pl_238_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_238_moba_PhysicsAsset"
},
outEnter = "as_ch_enter_pl_238_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_238_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "千窟城自古以来便贮藏着无数古老典籍，受祖辈的教导，伽罗自幼便坚信:文明或许会陨落，唯有知识令其意志长存，守护书本即使守护文明。她尽心抄书、苦练箭术，然而魔种突袭使得心血毁于一旦，家人逝去，古籍焚尽，她在漂泊中寻求真相，又偶然拯救了濒死的苏烈。伴随两个\"罪人\"邂逅，隐藏的阴谋终于启露冰山一角。",
bHideInBag = true,
shareOffset = v4
},
[405089] = {
id = 405089,
effect = true,
name = "大乔",
desc = "（限峡谷模式使用）获取后可以使用辅助大乔。",
icon = "t_arena_role_1039",
resourceConf = {
model = "sk_pl_241_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_241_moba_physics"
},
outEnter = "as_ch_enter_pl_241_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_241_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "因为同样被诅咒的命运，大乔曾发自内心感激着司马懿培养。但最终她还是沦为家族献给新任江东统治者的牺牲品。出嫁前夜，她提灯伫立海崖，却意外发现原本以为的暴虐君主，竟然是充斥活力的爽朗青年。爱意的萌生是突兀而简单，孙策就像是一团不灭的暖阳，耀眼而炽热，直直照进她的灵魂。",
bHideInBag = true,
shareOffset = v4
},
[405090] = {
id = 405090,
effect = true,
name = "项羽",
desc = "（限峡谷模式使用）获取后可以使用坦克项羽。",
icon = "t_arena_role_1040",
resourceConf = {
model = "sk_pl_237_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_237_moba_physics"
},
outEnter = "as_ch_enter_pl_237_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_237_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "他是姜子牙口中预言的“大魔神王”，注定要为这世界带来无尽黑暗。人们仇视他，妄图扼杀他，但他不信天命，更不认同这世界对自己的放逐，揭竿而起，剑指大河流域残暴的阴阳家们。垓下之战令他身心破碎，爱人的箭矢射穿他的心脏……但他清楚地知道这并非背叛，命运的捉弄使他重生，这一次，他注定不再重蹈覆辙。",
bHideInBag = true,
shareOffset = v4
},
[405091] = {
id = 405091,
effect = true,
name = "貂蝉",
desc = "（限峡谷模式使用）获取后可以使用法师貂蝉。",
icon = "t_arena_role_1041",
resourceConf = {
model = "SK_PL_230_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_230_moba_Physics"
},
outEnter = "as_ch_enter_pl_230_moba",
outIdle = "AS_CH_OutIdle_001_moba_PL_230",
outShow = "AS_CH_IdleShow_PL_230_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "乱世里的舞姬，以欺骗者的身份成为吕布所爱，她来自最强大的佣兵团，她的一切都被首领龙控制，甚至不得不以爱情为骗局，取走战神的性命，怜悯是如此多余，她的心里只有活着，与龙一起屈身曹操成了唯一的选择，唯有如此才能活着，唯有如此才有机会重逢。",
bHideInBag = true,
shareOffset = v4
},
[405092] = {
id = 405092,
effect = true,
name = "刘备",
desc = "（限峡谷模式使用）获取后可以使用战士刘备。",
icon = "t_arena_role_1042",
resourceConf = {
model = "sk_pl_235_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_235_moba_Physics"
},
outEnter = "as_ch_enter_pl_235_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_235_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "青梅煮酒的筵席上，以义气著称的刘备向昔日兄弟曹操吐露先祖托付天书的秘密，却引来曹操魏都军的入侵。此后刘备盯上了时为内应的督邮，但眼下督邮府浓烟滚滚，一个婴儿被塞进刘备的怀抱，追杀婴儿的魔种与猎杀魔种的猎魔人接踵而至，争执中，一个铁盒自襁褓中掉落，前世记忆潮水般涌入，命运邂逅的结局是英雄再度结义。",
bHideInBag = true,
shareOffset = v4
},
[405093] = {
id = 405093,
effect = true,
name = "刘禅",
desc = "（限峡谷模式使用）获取后可以使用辅助刘禅。",
icon = "t_arena_role_1043",
resourceConf = {
model = "sk_pl_240_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
skeletalMesh = "SK_PL_240_moba",
animClassName = "Bp_LiuShan_PreviewAnimInstance"
},
outEnter = "as_ch_enter_pl_240_moba",
outIdle = "YM_MOBA_240_Idle_IdleShow",
outShow = "AS_CH_IdleShow_PL_240_moba",
outShowIntervalTime = 10,
scaleTimes = 75,
shareTexts = v3,
suitStoryTextKey = "刘备之子，诸葛亮之徒，头顶诸多头衔的刘禅一直想证明自己是老爹和师父青出于蓝的继承者。但为了证明自己天才之名的挑战稷下墨子计划，因赤壁之战爆发而终止。当刘禅驾驶着自己设计的熊猫造型初号机进入战场后，却碰见了意想不到的敌人，一个有着渐变发色和拉风胡笳琴的可爱少女，初号机徒劳地发出预警:警告，心跳过快!",
bHideInBag = true,
shareOffset = v4
},
[405094] = {
id = 405094,
effect = true,
name = "诸葛亮",
desc = "（限峡谷模式使用）获取后可以使用法师诸葛亮。",
icon = "t_arena_role_1044",
resourceConf = {
model = "sk_pl_236_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_236_moba_physics"
},
outEnter = "as_ch_enter_pl_236_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_236_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "稷下学院全科第一的传奇学长，是公认的夫子继承者，他沉迷于研究天书的奥秘，行走于世间每个知识的角落，最终，定居蜀地草庐潜心解析。但血族之王徐福的归来，以及好友蔡邕的意外身死……使他不得不着眼于和枭雄曹操的对抗，赤壁之上，沉寂的可怕武器东风祭坛被再次唤醒，战事之后，或许一切终究明了。",
bHideInBag = true,
shareOffset = v4
},
[405095] = {
id = 405095,
effect = true,
name = "夏侯惇",
desc = "（限峡谷模式使用）获取后可以使用坦克夏侯惇。",
icon = "t_arena_role_1028",
resourceConf = {
model = "SK_PL_226_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_226_moba_PhysicsAsset"
},
outEnter = "as_ch_enter_pl_226_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_226_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "以百人斩之名让盗匪闻风丧胆的夏侯惇，有着不羁之风的名号，无人知晓他的来历，只知道他是仅次于“龙”的佣兵。神秘男子以重金诱导佣兵们前往陷阱时，夏侯惇也在其中，只不过他的目的从来都不是金钱，而是挑战更强者。陷阱发动，佣兵们悉数全灭。神秘男子看着以一只眼为代价活下来的夏侯，许下了无尽挑战的承诺。",
bHideInBag = true,
shareOffset = v4
},
[405096] = {
id = 405096,
effect = true,
quality = 2,
name = "皓月使者 李白",
desc = "获取后可作为峡谷模式中李白的皮肤使用。",
icon = "t_arena_role_1019_01",
getWay = "桂月清平",
resourceConf = {
model = "sk_pl_169_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_169_moba_physicsasset"
},
outEnter = "as_ch_enter_pl_169_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_169_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_169",
beginTime = {
seconds = 1726156800
},
bHideInBag = true,
level = 4
},
[405097] = {
id = 405097,
effect = true,
quality = 1,
name = "三彩逸士 赵云",
desc = "获取后可作为峡谷模式中赵云的皮肤使用。",
icon = "T_Arena_Role_1010_01",
getWay = "千都三彩",
resourceConf = {
model = "SK_OG_025_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_OG_025_dev_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_025_PV/Level_OG_025_Intact",
outIdle = "AS_CH_OutIdle_OG_025",
outShow = "AS_CH_IdleShow_OG_025",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4048,
4049
},
outEnterSequence = "LS_PV_OG_025",
shareTexts = {
"不识三彩，不见千都"
},
shareAnim = "AS_CH_Pose_Common_001",
beginTime = {
seconds = 1727712000
},
bHideInBag = true,
shareOffset = v4,
level = 6
},
[405098] = {
id = 405098,
effect = true,
name = "杨玉环",
desc = "（限峡谷模式使用）获取后可以使用法师杨玉环。",
icon = "t_arena_role_1045",
resourceConf = {
model = "sk_pl_261_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_261_moba_Physics"
},
outEnter = "as_ch_enter_pl_261_moba",
outIdle = "AS_CH_Outidle_001_moba_PL_261",
outShow = "AS_CH_IdleShow_PL_261_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "这位风华绝代的神秘美人，只需要轻轻拨动手中的琴弦便能让整个长安城为之撼动。她的琵琶像是有神奇的魔力，能让人沉醉于内心对美好事物的无限渴求。人们赞赏她，倾慕她，却并不真的了解她，就像她空洞的内心也不能真正理解口口相传的“幸福”究竟为何物，兴许只有某位同样神秘的牡丹方士对她的过往略知一二。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true
},
[405099] = {
id = 405099,
effect = true,
name = "海月",
desc = "（限峡谷模式使用）获取后可以使用法师海月。",
icon = "t_arena_role_1046",
resourceConf = {
model = "sk_pl_181_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_181_moba_PhysicsAsset"
},
outEnter = "as_ch_enter_pl_181_moba",
outIdle = "AS_CH_OutIdle_001_PL_181_moba",
outShow = "AS_CH_IdleShow_PL_181_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = [[曾被视为“无用之物”而被抛弃的少女海月，因帝俊拯救，成
为了神职者，她从此发誓将自己的一生奉献给神。在神陨之战
中，海月为了救帝俊而死，唯有一缕幽魂孤悬在漠北的天阙山
巅。千年之后，她被一场骸爆唤醒，并开始着手谋划帝俊归来
的大计。如今，阴谋逐渐浮出水面，尘封千年的云中往事也将
揭开帷幕……]],
timeToStartAnim = 2.4700000286102295,
bHideInBag = true
},
[405100] = {
id = 405100,
effect = true,
quality = 5,
name = "哈士奇（木桩）",
desc = "永远不要忘记让自己快乐",
icon = "Icon_Body_010",
resourceConf = {
model = "SK_Body_Head_010",
upper = "SK_Body_Upper_010",
bottom = "SK_Body_Under_010",
gloves = "SK_Body_Hands_010",
face = "SK_Body_Face_001",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = v0,
scaleTimes = 100,
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = -20,
bHideInBag = true,
suitName = "哈士奇",
suitIcon = "Icon_Body_010",
shareOffset = v4
},
[405101] = {
id = 405101,
effect = true,
name = "张良",
desc = "（限峡谷模式使用）获取后可以使用法师张良。",
icon = "t_arena_role_1029",
resourceConf = {
model = "SK_PL_227_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_227_moba_PhysicsAsset"
},
outEnter = "as_ch_enter_pl_227_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_227_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "令姜子牙都感到惊异的天才弟子，开创出自己独特魔道“言灵”的张良，他精通世间所有语言，甚至可以和整个世界交流。真理与他无限接近，所有魔道在他面前都不堪一击。姜子牙命他进入俗世，只为纠正世间混乱的秩序。但走入大河流域的张良与世隔绝太久，万能的言灵也无法挽救他的社交。看来，融入这个世界才是现在最大的难题。",
bHideInBag = true,
shareOffset = v4
},
[405102] = {
id = 405102,
effect = true,
quality = 1,
name = "大收藏家 鲁班",
desc = "获取后可作为峡谷模式中鲁班的皮肤使用。",
icon = "t_arena_role_1004_01",
getWay = "赛季祈愿",
resourceConf = {
model = "sk_og_026_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_og_026_dev_physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_026_PV/Level_OG_026_Intact",
outIdle = "AS_CH_OutIdle_OG_026",
outShow = "AS_CH_IdleShow_OG_026",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4050,
4051
},
outEnterSequence = "LS_PV_OG_026",
shareTexts = {
"黯淡的星星，也努力发光"
},
shareAnim = "AS_CH_Pose_Common_001",
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
bHideInBag = true,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403860.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403860.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
shareOffset = v4,
level = 6
},
[405103] = {
id = 405103,
effect = true,
quality = 2,
name = " 糖果女巫 小乔 ",
desc = "获取后可作为峡谷模式中小乔的皮肤使用。",
icon = "t_arena_role_1007_01",
getWay = "星光剧场商城",
resourceConf = {
model = "sk_pl_196_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_196_dev_physics"
},
outEnter = "as_ch_enter_pl_196_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_196_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_196",
shareTexts = {
"让你开心是我最厉害的魔法"
},
shareAnim = "AS_CH_Pose_Common_001",
minVer = "1.3.26.34",
beginTime = {
seconds = 1729872000
},
bHideInBag = true,
shareOffset = v4,
level = 4
},
[405104] = {
id = 405104,
effect = true,
quality = 1,
name = "永昼男爵 马可波罗",
desc = "获取后可作为峡谷模式中马可波罗的皮肤使用。",
icon = "T_Arena_Role_1027_01",
getWay = "永恒之舞",
resourceConf = {
model = "SK_OG_028_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_OG_028_dev_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_028_PV/Level_OG_028_Intact",
outIdle = "AS_CH_OutIdle_OG_028",
outShow = "AS_CH_IdleShow_OG_028",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4055,
4057
},
outEnterSequence = "LS_PV_OG_028",
shareTexts = {
"黯淡的星星，也努力发光"
},
shareAnim = "AS_CH_Pose_Common_001",
minVer = "1.3.26.65",
beginTime = {
seconds = 1730995200
},
bHideInBag = true,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403860.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403860.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
shareOffset = v4,
level = 6
},
[405105] = {
id = 405105,
effect = true,
quality = 1,
name = "美食品鉴官 貂蝉",
desc = "获取后可作为峡谷模式中貂蝉的皮肤使用。",
icon = "t_arena_role_1041_01",
getWay = "赛季祈愿",
resourceConf = {
model = "SK_OG_031_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_OG_031_dev_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_032_PV/Level_OG_032_Intact",
outIdle = "AS_CH_OutIdle_OG_031",
outShow = "AS_CH_IdleShow_OG_031",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4058,
4060
},
outIdle_pv = "LS_PV_OG_031_Idle",
outEnterSequence = "LS_PV_OG_031",
shareTexts = {
"尝尽佳肴百味才知平淡是真"
},
shareAnim = "AS_CH_Pose_Common_001",
minVer = "1.3.36.1",
beginTime = {
seconds = 1732809600
},
bHideInBag = true,
shareOffset = v4,
level = 6
},
[405106] = {
id = 405106,
effect = true,
quality = 1,
name = "牡丹贵妃 杨玉环",
desc = "获取后可作为峡谷模式中杨玉环的皮肤使用。",
icon = "T_Arena_Role_1045_01",
getWay = "赛季祈愿",
resourceConf = {
model = "SK_OG_037_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_OG_037_dev_Physics"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_037_PV/Level_OG_037_Intact",
outIdle = "AS_CH_OutIdle_OG_037",
outShow = "AS_CH_IdleShow_OG_037",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4078,
4079
},
outIdle_pv = "LS_PV_OG_037_Idle",
outEnterSequence = "LS_PV_OG_037",
shareTexts = {
"我愿落花成泥，滋养春天"
},
shareAnim = "AS_CH_Pose_Common_001",
minVer = "1.3.68.1",
beginTime = {
seconds = 1737043200
},
bHideInBag = true,
shareOffset = v4,
level = 6
},
[405107] = {
id = 405107,
effect = true,
name = "绮",
desc = "（限峡谷模式使用）获取后可以使用法师绮。",
icon = "t_arena_role_1049",
resourceConf = {
model = "SK_PL_288_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_288_moba_Physics",
skeletalMesh = "SK_PL_288_moba"
},
outEnter = "AS_CH_Enter_PL_288_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_288_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = [[相传在一幅泛黄的古画中，沉睡着一位画灵。发丝如初绽的桃花，眉目如晕染开的水墨。她是一代画圣临终前的最后一副作品，并赋名为——绮。画圣最后一笔点睛，魂魄化作春风散去，而绮却在那一刻缓缓睁开了眼眸，继承了画圣毕生所追求的至善至美，以及未竟的心愿——绘尽世间所有的美好。

画中自有千秋雪，画外却是万般春。绮临摹万物，渴望捕捉画外的光影，可墨色再浓，也难画出风的温度，笔触再妙，也难描摹内心的悸动。直到一个月圆之夜，绮用朱砂点破画卷，将自己从纸上剥离。画中角，终成画外仙。

初入元梦世界，绮对一切都充满好奇。她用画笔记录晨曦初露的微光，描摹夜市灯火的流转，甚至尝试勾勒星宝们的笑颜——可总觉得笔下生花，却少了几分温度。
她渴望创造属于自己的“真实”，于是，在一个细雨微落的春日午后，她轻轻挥动画笔，在纸上描绘出一朵带着露珠的花。那是一朵她从未在世间见过的奇花，花瓣如霞，花心闪烁微光。就在最后一笔落下的瞬间——那朵花竟缓缓绽放，化作灵体，轻盈地飘在绮的肩畔。

她为这朵花灵取名为——夭夭。夭夭是绮画出的第一个真实生命，也是她的画笔第一次“唤醒”了情感的具象。它不是普通的花灵，而是能感知情绪的存在。当绮悲伤时，夭夭会轻轻摇曳，用花瓣拭去她的眼泪；当她喜悦时，夭夭便会绽放出七彩光辉，仿佛回应她心中的欢愉。从此，夭夭便成了她最亲密的伙伴，也成为了她画中梦境的引领者。

后来绮又遇见一个哭泣的星宝——他的纸鸢破损，泪水涟涟。绮执笔描绘，奇迹再次发生——那只凤凰竟从纸上飞起，盘旋在空中，化作真正的灵鸟！小星宝破涕为笑，而绮终于明白：画，不止于形，更在于心。

或许是因为灵魂来自画卷的缘故，绮常常坠入神秘的梦境。梦里，她看见了另一个世界——那里没有苍白的墨色，只有斑斓的光影与异想天开的奇景，甚至还有另一个自己！绮行走四方，将梦境中的画变为现实，为怯懦者添上勇气的双翼，为孤独者描绘出相伴的知己……她的画，已不仅仅是画，而是承载了真实情感的生命。夭夭常伴她左右，见证着她的每一幅画，每一次心动。

“世间万物，皆可入画；但若画中无情，终究不过是空壳。”她如此说道，笑容如初绽的桃花，美得不似世间所有。]],
bHideInBag = true,
shareOffset = v4
},
[405110] = {
id = 405110,
effect = true,
name = "猫灵少女",
desc = "（限峡谷模式使用）获取后可以使用战士吕布。",
icon = "t_arena_role_1032",
resourceConf = {
model = "sk_pl_229_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_229_moba_Physics"
},
outEnter = "as_ch_enter_pl_229_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_229_moba",
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "王者大陆传颂着战神吕布的强大，但强如吕布也曾与死神擦肩而过。吕布躺在泥泞中奄奄一息时，一个小女孩递上的清水拯救了他，但从梦中被马蹄声惊醒的吕布，疑心被出卖杀光了小女孩一伙。此后战神几经浮沉，一度摸到天下，却被一个女人的舞蹈唤回当年的回忆。这一次，倒下的吕布，在漆黑中，听到一个声音说着“你可以选择的”。",
bHideInBag = true,
shareOffset = v4
},
[405111] = {
id = 405111,
effect = true,
quality = 1,
name = "幻彩画匠 绮",
desc = "获取后可作为峡谷模式中绮的皮肤使用。",
icon = "t_arena_role_1049_01",
getWay = "幻彩调律",
jumpId = {
88888
},
resourceConf = {
model = "SK_OG_047_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_OG_047_moba_Physics",
skeletalMesh = "SK_OG_047_moba"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_047_PV/Level_OG_047_Intact",
outIdle = "AS_CH_OutIdle_OG_047",
outShow = "AS_CH_IdleShow_OG_047",
outShowIntervalTime = 20,
scaleTimes = 100,
soundId = {
4097,
4096
},
outIdle_pv = "LS_PV_OG_047_Idle",
outEnterSequence = "LS_PV_OG_047",
shareTexts = {
"或许，我们可以一起画出梦境…..."
},
billboardOffsetZ = 50,
suitStoryTextKey = [[在寂静的月夜里，绮时常做着奇异的梦。梦里，她看见了另一个世界——那里有会说话的画具，会跳舞的颜料，还常梦见校园画室里有一位扎着彩色双马尾的少女，正用发梢沾着五彩斑斓的颜料作画。那个世界充满了不可思议的想象力，远比她所在的世界更为绚丽多彩。

每次从梦中醒来，绮都会迫不及待地将梦境绮制成画。在一次又一次的描绘中，那个彩色发梢少女的面孔渐渐清晰——竟是绮自己！这个来自梦境的自己仿佛是绮内心深处对现代世界的向往与憧憬，是她古老画魂中最为跳脱灵动的一面。

梦醒以后，画纸上总有一些奇妙的画作。虽然绮完全不记得学过这些，但笔下确实开始浮现出会发光的甜点、能当气球用的云朵——这些明显不属于本世界的造物。

在某一次施展画灵之力后，绮在梦境中再一次见到了另外一个自己。只是这一次，梦境中的少女开口说话了：“绮，我不只是你的梦……我也是你画中未完成的另一半。”

绮常常微笑着看着梦中的自己在校园画室里跑来跑去，发梢甩出的颜料在空中划出彩虹。她明白，梦里的自己正是内心最纯粹的想象力和童真的具现化。当现实中的她执笔作画时，是继承画圣衣钵的画灵；而当梦中的自己挥动彩色画笔时，则是她内心最活泼浪漫的一面。

当新研的彩墨正在宣纸上自动晕染，绮不再惊恐，而是微笑着放任画笔自己游走——因为最美好的画作，永远诞生在现实与梦境的交界处。

\"画中有画，心中有心。\"绮轻抚着画卷说道，\"或许这就是画圣想教会我的最后一课——艺术不该被规矩所束缚，想象力才是最珍贵的礼物。\"

绮望着自己最新完成的《千梦绘卷》：画卷里，无数个不同风格的自己正在各自的世界挥毫。最中央的留白处，墨迹自动延伸着，渐渐形成通往下一个梦境的入口。\"原来所谓灵感...\"她捻起一枚掉落在砚台边的齿轮花瓣，\"是万千世界的我在同时落笔啊。\"]],
bHideInBag = true,
shareOffset = v4,
level = 6
},
[405112] = {
id = 405112,
effect = true,
name = "艾琳",
desc = "（限峡谷模式使用）获取后可以使用射手艾琳。",
icon = "t_arena_role_1048",
resourceConf = {
model = "SK_PL_289_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_289_moba_Physics",
skeletalMesh = "SK_PL_289_moba"
},
outEnter = "AS_CH_Enter_PL_289_moba",
outIdle = "AS_CH_OutIdle_PL_289_moba",
outShow = "AS_CH_IdleShow_PL_289_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outPropShow = "AS_CH_IdleShow_PL_289_Prop_moba",
outPropSkeletal = "SK_PL_Prop_289_moba",
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "出生于黄金森林的精灵公主艾琳，天性好奇，热爱自由与冒险，因继承人的职责束缚，被要求遵循精灵族舞蹈的优雅与绝对的秩序。成年前夕，她决定暂时逃离森林，前往圣殿的人类世界开启一场自由的冒险，寻找到属于自己的舞步。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
shareOffset = v4
},
[405113] = {
id = 405113,
effect = true,
name = "泡泡",
desc = "（限峡谷模式使用）获取后可以使用射手泡泡。",
icon = "t_arena_role_1051",
resourceConf = {
headOffset = v4,
backOffset = v4,
faceOffset = v4
},
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true
},
[405114] = {
id = 405114,
effect = true,
name = "冰夷",
desc = "（限峡谷模式使用）获取后可以使用刺客冰夷。",
icon = "t_arena_role_1019",
resourceConf = {
model = "SK_PL_321_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_321_moba_Physics"
},
outEnter = "as_ch_enter_pl_157_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_157_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = [[昆仑的深处，是一座亘古不化的冰渊，那是整个昆仑山最为神秘的所在。星宝们都说，那是昆仑山的冰牢，里面关押的都是有罪的灵魂。司刑者冰夷法度严明，作恶之人，皆会被他的龙鳞冻成冰雕，永囚于冰渊。
冰渊的下游，是一条名为赤水的河流，赤水环绕昆仑，以结界之力守护昆仑避免黑烟混沌之力的侵蚀。昆仑有法，任何星宝不得穿越赤水。但三年前，司刑者冰夷却知法犯法，破坏了结界，放俗世的星宝渡过赤水来昆仑求仙问药。
“情由再哀，法不可违。”昆仑之主白泽声音低沉。
 “我甘愿受罚，但人间求助之声，不应该被阻断。我愿在此摆渡，接送真正需要帮助的人。”冰夷将一片龙鳞种入了自己的掌心，“若遇黑烟异动，此鳞便是最后一道锁。”
冰夷自囚于赤水，曾经执法的锁链变成了一杆青玉篙。往来星宝总见他在冰雾里穿梭，他的蓝袍结满霜花的蓝袍，却总把暖炉让给瑟瑟发抖的渡客。蓝袍结满霜花仍将暖炉让给体弱的乘客。第三年冬至，冰夷摆渡完第九百九十九个星宝时，赤水忽然漫起猩红雾霭——当年逃逸的黑烟卷土重来。
\"快去找白泽大人！\"小星宝哭着要拉他衣角，冰夷反手将其推上岸。赤水翻起十丈血浪，势要吞没整个昆仑。冰夷凛然不惧，重新渡回赤水之中，掌心的龙鳞炸开，冰夷用尽毕生精魂和法力，将黑烟与自己同时锁进漩涡。 
河面炸开万点冰晶时，昆仑山巅传来琉璃破碎之声。冰渊里那些被冰夷惩戒的罪人冰雕纷纷融化。冰水汇入赤水中，重新汇聚成一片片发光龙鳞，每片龙鳞，都藏着星宝们向善的暖意。冰夷真身再现，赤水河底腾起百米冰龙，新生的龙鳞不再寒光刺目，反而流转着赤水温润的霞光。
冰夷重新成为了昆仑的司刑者，他仍然如从前般威严和无私，但却在每次使用龙鳞惩戒罪人时，留有一念之仁。若身中龙鳞之人从此改过向善，便相安无事。若此人再次为恶，龙鳞将会将其变成一座冰雕。虽然冰夷是冰渊的司刑者，可是他真心的希望，有一天冰渊里再没有冰雕，只有春水流淌。]],
bHideInBag = true
},
[405115] = {
id = 405115,
effect = true,
name = "小红狐",
desc = "（限峡谷模式使用）获取后可以使用刺客李白。",
icon = "t_arena_role_ 1053",
resourceConf = {
model = "SK_PL_322_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_085_moba_physics"
},
outEnter = "as_ch_enter_pl_157_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_157_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = [[在星宝们眼中，小红狐是个行走的战术指南。他做事严谨，计划周全，带队探险时总像开了导航外挂，路线规划精确到秒，危机应对堪比军演，堪称“战略大神”。但就是这样一个高手，却总穿着一身洗到发白的工装，像个来蹭饭的“穷亲戚”，引得初来乍到的星宝们纷纷低声吐槽：“这打扮，能有多厉害？”

有回探险，小红狐误打误撞闯进了星宝农场。起初小红狐还按老习惯，计算产量，规划流程，忙得不亦乐乎。但慢慢地，他被这片土地的宁静“驯服”了。清晨浇水的清新、午后喂食的温柔、夜晚星光洒落的惬意，让他卸下防备，真正沉浸其中。

打这以后，小红狐彻底变了。他不再整天闷头搞那些复杂的计划，而是一头扎进和星宝们的相处里。星宝们遇到困难，他总能“恰好”出现。有星宝缺作物了，他不知道从哪儿掏出一些特别的种子；到了饭点，要是有星宝没带吃的，他就像哆啦A梦似的，从兜里掏出一份香喷喷的便当，还嘴硬说：“顺手做的，多了一份。”

时间一长，星宝们对他的态度来了个一百八十度大转弯。曾经被误会成“穷亲戚”的他，如今成了最可靠的存在。有星宝打趣喊他“义父”，没想到一传十、十传百，这称呼竟成了他的新身份。

小红狐也没闲着，他琢磨着把星宝农场打造成星宝们的快乐天堂。他给每个来农场的星宝都准备了星光种子，他希望把农场的治愈力量传递给每一个星宝，让大家都能在忙碌的生活中，停下来，找到属于自己的星光田园，独一无二的心灵栖息地。

不知道是哪位星宝偶然发现，小红狐那本翻得起毛边的战术手册最后一页上写着一句话——“终极目标：用一百种计划，守护星宝最快乐的笑容。”原来这位总被当成“穷亲戚”的战略家，早就把爱藏进了每个精心设计的小细节里。]],
bHideInBag = true
},
[405116] = {
id = 405116,
effect = true,
name = "明世隐",
desc = "（限峡谷模式使用）获取后可以使用辅助明世隐。",
icon = "t_arena_role_1044",
resourceConf = {
model = "SK_PL_312_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_312_moba_Physics"
},
outEnter = "as_ch_enter_pl_157_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_157_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "稷下学院全科第一的传奇学长，是公认的夫子继承者，他沉迷于研究天书的奥秘，行走于世间每个知识的角落，最终，定居蜀地草庐潜心解析。但血族之王徐福的归来，以及好友蔡邕的意外身死……使他不得不着眼于和枭雄曹操的对抗，赤壁之上，沉寂的可怕武器东风祭坛被再次唤醒，战事之后，或许一切终究明了。",
bHideInBag = true,
shareOffset = v4
},
[405117] = {
id = 405117,
effect = true,
name = "忍者",
desc = "（限峡谷模式使用）获取后可以使用刺客忍者。",
icon = "t_arena_role_1002",
resourceConf = {
model = "sk_pl_084_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_084_dev_physics"
},
outEnter = "as_ch_enter_pl_084_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_084_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_084",
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "家族所看守的奇迹碎片并非奇迹，而是诅咒，背负诅咒的子嗣最终将在厮杀下融为一体--认知到这一事实的青年被不甘与愤怒支配，于月夜下挥刀屠戮整个家族。利刃却在最后幸存的妹妹面前犹豫，他就此放弃，向东漂泊，却在长城为了拯救某个少年而被魔铠吞噬，失去了所有记忆。花木兰的邀请使他留下，魔道利刃自此化作守护之铠。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[406001] = {
id = 406001,
effect = true,
quality = 2,
name = "宝箱",
desc = "BS玩法的宝箱",
icon = "Icon_PL_023",
resourceConf = {
headOffset = v4,
backOffset = v4,
faceOffset = v4
},
bHideInBag = true,
shareOffset = v4
},
[406002] = {
id = 406002,
effect = true,
name = "铠_随机事件",
desc = "（限峡谷模式使用）获取后可以使用战士铠。",
icon = "t_arena_role_1002",
resourceConf = {
model = "sk_pl_084_dev",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_084_dev_physics"
},
outEnter = "as_ch_enter_pl_084_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_084_moba",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_084_Prop_moba",
outPropShow = "AS_CH_IdleShow_PL_084_Prop_moba",
outPropSkeletal = "SK_PL_Prop_084_moba",
outEnterSequence = "SQC_Enter_PL_084",
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "家族所看守的奇迹碎片并非奇迹，而是诅咒，背负诅咒的子嗣最终将在厮杀下融为一体--认知到这一事实的青年被不甘与愤怒支配，于月夜下挥刀屠戮整个家族。利刃却在最后幸存的妹妹面前犹豫，他就此放弃，向东漂泊，却在长城为了拯救某个少年而被魔铠吞噬，失去了所有记忆。花木兰的邀请使他留下，魔道利刃自此化作守护之铠。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[406003] = {
id = 406003,
effect = true,
name = "鲁班七号_随机事件",
desc = "（限峡谷模式使用）获取后可以使用射手鲁班七号。",
icon = "t_arena_role_1004",
resourceConf = {
model = "SK_PL_9002_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_156_dev_physics"
},
outEnter = "as_ch_enter_pl_022_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_022_moba",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_022_Prop_moba",
outPropShow = "AS_CH_IdleShow_PL_022_Prop_moba",
outPropSkeletal = "SK_PL_Prop_022_moba",
outEnterSequence = "SQC_Enter_PL_022",
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "鲁班七号是鲁班大师的天才造物，是机关术能制造出来的最精巧的人偶。短胳膊短腿，可爱又可怕，小身材有大能量，具有超强破坏力。因为血族之乱，鲁班被墨子带去邻城支援玄雍，可是，它在途中丢失……在它尚未成形的语言中，人们似乎能听出它对于父亲的崇拜和思念。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[406004] = {
id = 406004,
effect = true,
name = "钟馗_随机事件",
desc = "（限峡谷模式使用）获取后可以使用辅助钟馗。",
icon = "t_arena_role_1015",
resourceConf = {
model = "SK_PL_9003_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_158_dev_physics"
},
outEnter = "as_ch_enter_pl_158_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_158_moba",
outShowIntervalTime = 10,
outPropShow = "AS_CH_IdleShow_PL_158_Prop_moba",
outPropSkeletal = "SK_PL_Prop_158_dev",
suitStoryTextKey = "钟馗存在于长安城神秘的“门”中，那里通往虚空，也通往一切智慧根源。作为门内之物的守护者，它比长安城还要更加古老，每个午夜子时，它都会从门中来到朱雀大街，巡视着这座在白日里无比繁华的城市，夜幕之下，一切试图破坏长安之人，都会被它吸入混沌之中，人们视它为“鬼”。而它，只是在执行着守护的指令。",
bHideInBag = true
},
[406005] = {
id = 406005,
effect = true,
name = "王昭君_随机事件",
desc = "（限峡谷模式使用）获取后可以使用法师王昭君。",
icon = "t_arena_role_1018",
resourceConf = {
model = "SK_PL_9005_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_186_moba_physicsasset"
},
outEnter = "as_ch_enter_pl_186_moba",
outIdle = v0,
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_186_Prop_moba",
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "王昭君本是中原送来和亲的公主，早已被依照习俗献祭于圣地“凛冬之海”。多年后，贪婪的中原人趁又一个祭祀之日发起突袭，上演了一出染红雪原的“血色婚礼”。然而掠夺却止于暴雪降至，歹徒们被冰封于由雪崩代表的神罚。被拥戴的公主昭君悄然抚摸一只只冰棺，清冷的眼眸中却始终蕴含一丝眷恋与哀伤。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true
},
[406006] = {
id = 406006,
effect = true,
name = "后羿_随机事件",
desc = "（限峡谷模式使用）获取后可以使用射手后羿。",
icon = "t_arena_role_1012",
resourceConf = {
model = "SK_PL_9004_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_156_dev_physics"
},
outEnter = "as_ch_enter_pl_156_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_156_moba",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_156_Prop_moba",
outPropShow = "AS_CH_IdleShow_PL_156_Prop_moba",
outPropSkeletal = "SK_PL_Prop_156_dev",
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "后羿作为这世间绝无仅有的英勇射手，曾受到统治者的青睐与赞扬。奉命摧毁日之塔的过程中，他被这宏伟奇迹的力量吸引……然统治者的心思永远更为缜密，摧毁了最后一座日之塔的后羿肆意汲取其中能量，天罚却也几乎同时降至，他的光芒被熄灭、埋没入雪原，在千百年间等待一个人将其再次点亮，射出更璀璨的光。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4
},
[406007] = {
id = 406007,
effect = true,
quality = 5,
name = "训练场木桩-友方",
desc = "峡谷训练场专用木桩",
icon = "Icon_Body_010",
resourceConf = {
model = "SK_MOBA_MON_001",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = v0,
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = -20,
bHideInBag = true,
suitName = "哈士奇",
suitIcon = "Icon_Body_010",
shareOffset = v4
},
[406008] = {
id = 406008,
effect = true,
quality = 5,
name = "训练场木桩-敌方",
desc = "峡谷训练场专用木桩",
icon = "Icon_Body_010",
resourceConf = {
model = "SK_MOBA_MON_001",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outIdle = v0,
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = -20,
bHideInBag = true,
suitName = "哈士奇",
suitIcon = "Icon_Body_010",
shareOffset = v4
},
[406009] = {
id = 406009,
effect = true,
quality = 5,
name = "物理召唤兽",
icon = "Icon_Body_010",
resourceConf = {
model = "SK_MOBA_MON_004",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_MOBA_MON_004_PhysicsAsset"
},
outEnter = "SK_MOBA_MON_004_born",
outIdle = "SK_MOBA_MON_004_idle",
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = -20,
bHideInBag = true,
shareOffset = v4
},
[406010] = {
id = 406010,
effect = true,
quality = 5,
name = "法术召唤兽",
icon = "Icon_Body_010",
resourceConf = {
model = "SK_MOBA_MON_003",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_MOBA_MON_003_PhysicsAsset"
},
outEnter = "SK_MOBA_MON_003_born",
outIdle = "SK_MOBA_MON_003_idle",
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = -20,
bHideInBag = true,
shareOffset = v4
},
[406011] = {
id = 406011,
effect = true,
quality = 5,
name = "图腾",
icon = "Icon_Body_010",
resourceConf = {
headOffset = v4,
backOffset = v4,
faceOffset = v4
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = -20,
bHideInBag = true,
shareOffset = v4
},
[406012] = {
id = 406012,
effect = true,
quality = 5,
name = "载具_机甲",
icon = "Icon_Body_010",
resourceConf = {
model = "SK_HOK_Mount_20000",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_HOK_Mount_20000_Physics"
},
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = -20,
bHideInBag = true,
shareOffset = v4
},
[405200] = {
id = 405200,
effect = true,
name = "载具_终局机甲",
desc = "（限峡谷模式使用）获取后可以使用坦克程咬金。",
icon = "t_arena_role_1030",
resourceConf = {
model = "SK_PL_228_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_228_moba_Physics"
},
outEnter = "AS_CH_Enter_PL_228_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_228_moba",
outShowIntervalTime = 10,
shareTexts = v3,
suitStoryTextKey = "一身健美的肌肉，饱含铁血的男儿柔情，说的便是口口相传的名将程咬金。他认为力量从来不是争斗与厮杀的武器，而是挑战自然的资本。但与偶然结识的友人李靖共同退治暴徒的经历使他改观，自此，他周游各地，守护弱小，惩恶扬善，誓要让肌肉和汗水在太阳照不到的黑暗角落里散发光辉。",
bHideInBag = true,
shareOffset = v4
},
[405118] = {
id = 405118,
effect = true,
name = "晴霜",
desc = "（限峡谷模式使用）获取后可以使用战士晴霜。",
icon = "t_arena_role_1047",
resourceConf = {
model = "SK_PL_284_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_284_moba_Physics",
skeletalMesh = "SK_PL_284_moba"
},
outEnter = "AS_CH_Enter_PL_284_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_284_moba",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_284_Prop_moba",
shareTexts = v3,
suitStoryTextKey = [[晴霜出生于【猎魂者】世家，她的家族有着独特的修炼方法：每个星宝习武时，家族会帮助星宝猎取命格相合的猛兽，以猛兽之魄助力修行，从而达到常人无法企及的境界。
晴霜天赋异禀，从小立志成为武道宗师，她备受家族的期许，与她命格相合的猛兽是一头威猛不凡的白熊。12岁时，家族带着她去雪山猎取白熊，她看到白熊嗷嗷待哺的幼熊，心有不忍，偷偷放走了被困在家族陷阱中的白熊。这也是第一次，她对家族的修炼方式产生了质疑。
未能得到猛兽之魄的晴霜，习武的进度渐渐落后于同期，看到那些之前远不如自己的星宝都变得比自己更加勇猛，她坚持想要靠自己的力量成为武道宗师，只能付出比常人多百倍的努力。
在一次比武大会上，她爆冷门战胜了强劲的对手。但对手不相信没有武魂的拳手能赢，毫无证据地指责她作弊，而那些因为赌她输而输了钱的老板更是怀恨在心，在她回家的路上设伏，想要致她于死地。
关键时刻，那只被放走的白熊再次出现救下了她。原来，白熊被晴霜放走后，就一直在默默守护着她。
之后，蓄谋已久的猎魂家族出现，他们围住了白熊，想要借此机会猎取它的武魂。伤痕累累的晴霜挡在白熊身前，而猎魂家族则威胁她说，如果她违背家族，以后将被整个武道驱逐，再也不能参加比武大会。
“比起成为武道大宗师，我更珍惜和伙伴一起并肩作战的热血。”
晴霜毅然折断了自己的冠军金腰带，以示决绝。白熊将自己的意志化作一件披风落在重伤的晴霜身上，晴霜身上的伤口瞬间愈合了，她觉得自己充满了力量，一鼓作气打败了所有的敌人。一人一熊顺利逃跑。
此后，晴霜活跃在地下武馆，她创立了 「熊魂流」 ，强调武魂与武者的平等共生。她不能再参加比武大会，但在地下，她仍然用拳头贯彻着自己的正义。]],
bHideInBag = true,
shareOffset = v4
},
[407003] = {
id = 407003,
effect = true,
name = "甜蜜恋歌 小乔",
desc = "（限峡谷模式使用）获取后可以获得小乔的英雄皮肤。",
icon = "T_Arena_Role_1007_03",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_086_moba",
material = "MI_PL_086_01_HP01_moba;MI_PL_086_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_086_moba_physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_086_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_086_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_086",
shareTexts = {
"在全场欢呼声中牵你的手！"
},
suitStoryTextKey = "对于江东乔氏而言，双胞胎是不详的征兆，大小乔自出生起便被迫分开。小乔被送往稷下直至长大才得以回家，但族人震惊其魔道天赋，唯恐魔道家族的秘密因此暴露，又将小乔送往东海边小镇。不久，小镇疫病横行引发暴乱，小乔召唤海风吹散毒雾时，被前来镇压的周瑜当成嫌犯，谁也没想到这次邂逅，却是两人情愫的开始。",
bHideInBag = true,
shareOffset = v4,
level = 3,
customDesc = v5
},
[407004] = {
id = 407004,
effect = true,
name = "热舞派对 阿轲",
desc = "（限峡谷模式使用）获取后可以获得阿轲的英雄皮肤。",
icon = "T_Arena_Role_1035_02",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_252_moba",
material = "MI_PL_252_01_HP01_moba;MI_PL_252_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_252_moba_Physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_252_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_252_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"接着奏乐，接着舞"
},
suitStoryTextKey = "大周分崩，诸国并起，世代以刺客为生的荆氏一门仅存两兄妹相依为命。兄长拒绝了燕太子丹刺杀昔日好友樊於期的任务，次日，樊於期拜访荆氏兄长时，一同被太子丹杀害。太子丹志得意满，准备以二人人头重树地位，却在宴会上迎来了荆氏一脉最后的少女。宴会琴师救走了少女，但少女却只记得“阿轲”之名与止战的意志。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407005] = {
id = 407005,
effect = true,
name = "峡谷神箭 后羿",
desc = "（限峡谷模式使用）获取后可以获得后羿的英雄皮肤。",
icon = "T_Arena_Role_1012_01",
getWay = "段位任务产出",
resourceConf = {
model = "sk_pl_156_dev",
material = "MI_PL_156_01_HP01_moba;MI_PL_156_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_156_dev_physics",
materialSlot = "Skin;Skin_02"
},
outEnter = "AS_CH_Enter_PL_156_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_156_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "后羿作为这世间绝无仅有的英勇射手，曾受到统治者的青睐与赞扬。奉命摧毁日之塔的过程中，他被这宏伟奇迹的力量吸引……然统治者的心思永远更为缜密，摧毁了最后一座日之塔的后羿肆意汲取其中能量，天罚却也几乎同时降至，他的光芒被熄灭、埋没入雪原，在千百年间等待一个人将其再次点亮，射出更璀璨的光。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4,
level = 3,
customDesc = v5
},
[407006] = {
id = 407006,
effect = true,
name = "南阳风光 诸葛亮",
desc = "（限峡谷模式使用）获取后可以获得诸葛亮的英雄皮肤。",
icon = "T_Arena_Role_1044_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_236_moba",
material = "MI_PL_236_01_HP01_moba;MI_PL_236_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_236_moba_physics",
materialSlot = "Skin_Lod1;Skin_1_lod1"
},
outEnter = "AS_CH_Enter_PL_236_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_236_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "稷下学院全科第一的传奇学长，是公认的夫子继承者，他沉迷于研究天书的奥秘，行走于世间每个知识的角落，最终，定居蜀地草庐潜心解析。但血族之王徐福的归来，以及好友蔡邕的意外身死……使他不得不着眼于和枭雄曹操的对抗，赤壁之上，沉寂的可怕武器东风祭坛被再次唤醒，战事之后，或许一切终究明了。",
bHideInBag = true,
shareOffset = v4,
level = 3,
customDesc = v5
},
[407007] = {
id = 407007,
effect = true,
name = "桃园清心 刘备",
desc = "（限峡谷模式使用）获取后可以获得刘备的英雄皮肤。",
icon = "T_Arena_Role_1042_02",
getWay = "峡谷勋章产出",
resourceConf = {
model = "sk_pl_235_moba",
material = "MI_PL_235_03_HP01_moba;MI_PL_235_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_235_moba_Physics",
materialSlot = "initialShadingGroup;02 - Default"
},
outEnter = "AS_CH_Enter_PL_235_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_235_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "青梅煮酒的筵席上，以义气著称的刘备向昔日兄弟曹操吐露先祖托付天书的秘密，却引来曹操魏都军的入侵。此后刘备盯上了时为内应的督邮，但眼下督邮府浓烟滚滚，一个婴儿被塞进刘备的怀抱，追杀婴儿的魔种与猎杀魔种的猎魔人接踵而至，争执中，一个铁盒自襁褓中掉落，前世记忆潮水般涌入，命运邂逅的结局是英雄再度结义。",
bHideInBag = true,
shareOffset = v4,
level = 3,
customDesc = v5
},
[407008] = {
id = 407008,
effect = true,
name = "万物复苏 瑶",
desc = "（限峡谷模式使用）获取后可以获得瑶的英雄皮肤。",
icon = "T_Arena_Role_1006_02",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_023_moba",
material = "MI_PL_023_01_HP01_moba;MI_PL_023_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_023_moba_physics",
materialSlot = "Skin;Skin_Translucent"
},
outEnter = "AS_CH_Enter_PL_023_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_023_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "她本是林中幼鹿，在战争中丧命，却又在命运的眷顾下重生天真烂漫的阿瑶简直是古灵精怪的代名词，她像只小鹿般在幽谧的林中自由自在地奔跑跳跃，又莽莽撞撞闯到大河以西，用那变化多端的恶作剧引起一片恐慌。云中君说阿瑶就像个梦，闪耀着森林自然之光的梦，殊不知，梦境的彼岸便是曾经的羁绊与现实。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
shareOffset = v4,
level = 3,
customDesc = v5
},
[407009] = {
id = 407009,
effect = true,
name = "薜萝侠士 云缨",
desc = "（限峡谷模式使用）获取后可以获得云璎的英雄皮肤。",
icon = "T_Arena_Role_1017_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_159_dev",
material = "MI_PL_159_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_159_dev_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_159_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_159_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = [[“哪里有麻烦，哪里就有云缨~!”
这位风风火火，手持长枪的少女，正是长安城现下出尽风头的大理寺新锐。无论遇到什么麻烦，她都会点燃热血抢先为您效劳。只要不担心留下更多的麻烦……]],
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
level = 2,
customDesc = v5
},
[407010] = {
id = 407010,
effect = true,
name = "晨曦之星 曜",
desc = "（限峡谷模式使用）获取后可以获得曜的英雄皮肤。",
icon = "T_Arena_Role_1026_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_223_moba",
material = "MI_PL_223_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_223_moba_lod1_physicsasset",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_223_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_223_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "曜从小是一个争强好胜的热血少年，以李白为偶像，希望能成为如他一般潇洒骄傲的剑客。进入稷下学习后，通过组建星之队参加庄周举行的归虚梦演大赛，他获得了友谊、能量和自我认知。星之队夺得了第一，然而他的英雄之旅才刚刚开始…",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407011] = {
id = 407011,
effect = true,
name = "环保卫士 狄仁杰",
desc = "（限峡谷模式使用）获取后可以获得狄仁杰的英雄皮肤。",
icon = "T_Arena_Role_1023_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_183_moba",
material = "MI_PL_183_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_183_moba_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_183_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_183_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "雷厉风行而又不计后果的清洗行动，使狄仁杰之名被无数违法者憎恶，他是完美主义的侦探，也是长安城最可靠的守护者，女帝武则天信赖他，从封印姜子牙开始，他就是女帝最得力的助手，而长安城崩溃的地下世界，也证明着这位治安官非凡的实力，他的眼睛为女帝注视着一切，任何值得怀疑之人，他都绝对不会放过。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407012] = {
id = 407012,
effect = true,
name = "紫晶猎手 花木兰",
desc = "（限峡谷模式使用）获取后可以获得花木兰的英雄皮肤。",
icon = "T_Arena_Role_1024_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_182_moba",
material = "MI_PL_182_02_HP01_moba;MI_PL_182_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_182_moba_physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_182_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_182_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "花木兰身为女性，却有着不输任何男性的热血豪情，她自愿请缨镇守长城，即便因歹人暗算而背负叛徒的污名，依旧徘徊在这片边疆为守护而战。她以超绝的领导力和毋庸置疑的实力召集各路强者，击退各路魔种和马贼。张扬跋扈的亮眼红发与轻重交替的高超剑术使她成为战场上一道靓丽的风景。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407013] = {
id = 407013,
effect = true,
name = "青之骑士 亚瑟",
desc = "（限峡谷模式使用）获取后可以获得亚瑟的英雄皮肤。",
icon = "T_Arena_Role_1011_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_127_moba",
material = "MI_PL_127_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_127_moba_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_127_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_127_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "同盟国的军队看准圣骑士团的日益衰弱，屡次发动突袭。第四次的攻击却终于一名青年之手，他手握“誓约胜利之剑”，单枪匹马剿灭了残忍的叛徒，守住了象征圣骑士团荣耀与尊严的圣殿。根据代代相传的古老条约，拔出这把剑的人将获得圣骑士们的宣誓与效忠，被冠以“亚瑟王”的神圣名号。新的亚瑟，新的时代，骑士精神永不磨灭。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407014] = {
id = 407014,
effect = true,
name = "仲裁者 东皇太一",
desc = "（限峡谷模式使用）获取后可以获得东皇太一的英雄皮肤。",
icon = "T_Arena_Role_1022_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_184_moba",
material = "MI_PL_184_01_HP01_moba;MI_PL_184_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_184_moba_physics",
materialSlot = "Skin_LOD0;Skin_LOD1",
skeletalMesh = "SK_PL_184_moba",
animClassName = "Bp_DongHuangTaiYi_Avatar_PreviewAnimInstance"
},
outEnter = "AS_CH_Enter_PL_184_moba",
outIdle = "YM_MOBA_184_Idle_IdleShow",
outShow = "AS_CH_IdleShow_PL_184_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "他不甘于在平庸中走向必然消逝的结局，于是接受巫神祝试炼成为强大的神巫东皇太一。然而当奇迹的真相暴露在眼前昭示着一切从未改变--他毅然选择吞噬奇迹之力，进化半神之姿。如今，他高居云梦泽顶端的宫殿，受全知者们的敬仰，但这远非终点--总有一日，他将夺取全部的奇迹之力，成为真正的、唯一的神明。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407015] = {
id = 407015,
effect = true,
name = "橙意满满 孙策",
desc = "（限峡谷模式使用）获取后可以获得孙策的英雄皮肤。",
icon = "T_Arena_Role_1001_02",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_085_moba",
material = "MI_PL_085_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_085_moba_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_085_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_085_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
outEnterSequence = "SQC_Enter_PL_085",
shareTexts = v3,
suitStoryTextKey = "人称“江东小霸王”的孙策仿佛注定为了结束吴的纷乱割据而生他虽出身名门，却豪爽、热情，无畏，也不屑与腐败者们同流合污。他与挚友周瑜联手领军，清洗着江郡奢靡堕落的腐败豪族。某次预料外的海难将他和大乔的命运牵连在一起少女手中的明灯成了指引他们闯出风浪的向标。灵魂触动一眼万年，他一往无前的心更加坚定。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407016] = {
id = 407016,
effect = true,
name = "深渊暗影 吕布",
desc = "（限峡谷模式使用）获取后可以获得吕布的英雄皮肤。",
icon = "T_Arena_Role_1032_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_229_moba",
material = "MI_PL_229_01_HP01_moba;MI_PL_229_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_229_moba_Physics",
materialSlot = "SKIN;Skin_1"
},
outEnter = "AS_CH_Enter_PL_229_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_229_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "王者大陆传颂着战神吕布的强大，但强如吕布也曾与死神擦肩而过。吕布躺在泥泞中奄奄一息时，一个小女孩递上的清水拯救了他，但从梦中被马蹄声惊醒的吕布，疑心被出卖杀光了小女孩一伙。此后战神几经浮沉，一度摸到天下，却被一个女人的舞蹈唤回当年的回忆。这一次，倒下的吕布，在漆黑中，听到一个声音说着“你可以选择的”。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407017] = {
id = 407017,
effect = true,
name = "上古遗物 墨子",
desc = "（限峡谷模式使用）获取后可以获得墨子的英雄皮肤。",
icon = "T_Arena_Role_1020_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_187_moba",
material = "MI_PL_187_01_HP01_moba;MI_PL_187_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_187_moba_physics",
materialSlot = "Skin_1;Skin_2"
},
outEnter = "AS_CH_Enter_PL_187_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_187_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "因为墨子在机关术上的天赋与成就，人们称其为机关术宗师;因为墨子踏遍上古遗迹，联合他人打造稷下学院，人们称其为贤者;因为墨子打造长安并亲自以机关术守护，人们又称其为和平守护者。没人想到当初被唤作墨翟的普通青年工匠，如今能驾驶机关人所向披靡，成为那些觊觎长安城的宵小之辈心中的梦魇。",
bHideInBag = true,
level = 2,
customDesc = v5
},
[407018] = {
id = 407018,
effect = true,
name = "朝霞初现 公孙离",
desc = "（限峡谷模式使用）获取后可以获得公孙离的英雄皮肤。",
icon = "T_Arena_Role_1021_02",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_188_moba",
material = "MI_PL_188_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_188_moba_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_188_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_188_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "阿离凭借勤奋刻苦，以曼妙柔美的舞姿从一百个孩子中脱颖而出。这之后她真正的舞台不再只限于长乐坊的灯光下一名扬长安的舞技成了她出入各地绝佳的掩饰，真正工作是在方士指引下妥善处理各路文书，在黑暗中维护长安的正义。少女像是个枫叶中的青涩精灵，一半是跃动着的甜美，另-半则是不为人知的、折柳相送的思念。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
level = 2,
customDesc = v5
},
[407019] = {
id = 407019,
effect = true,
name = "透绿心情 夏侯惇",
desc = "（限峡谷模式使用）获取后可以获得夏侯惇的英雄皮肤。",
icon = "T_Arena_Role_1028_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "SK_PL_226_moba",
material = "MI_PL_226_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_226_moba_PhysicsAsset",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_226_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_226_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = v3,
suitStoryTextKey = "以百人斩之名让盗匪闻风丧胆的夏侯惇，有着不羁之风的名号，无人知晓他的来历，只知道他是仅次于“龙”的佣兵。神秘男子以重金诱导佣兵们前往陷阱时，夏侯惇也在其中，只不过他的目的从来都不是金钱，而是挑战更强者。陷阱发动，佣兵们悉数全灭。神秘男子看着以一只眼为代价活下来的夏侯，许下了无尽挑战的承诺。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407020] = {
id = 407020,
effect = true,
name = "青龙鳞 赵云",
desc = "（限峡谷模式使用）获取后可以获得赵云的英雄皮肤。",
icon = "T_Arena_Role_1010_02",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_126_moba",
material = "MI_PL_126_01_HP01_moba;MI_PL_126_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_126_moba_physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_126_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_126_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "最强佣兵团“龙”背后之人，被称作影子。曹操巧妙撩拨着龙的野心，影子从中嗅到了阴谋的气息，无法说服龙的影子只能退出组织。当佣兵团受命与吕布一战，却被屠戮殆尽时，影子现身与龙合力杀死了吕布后，又随之离去，并从此消失。数年后，当曹操大军席卷三分之地时，曾经站在龙背后的影子，以赵子龙之名于长坂坡参见。",
bHideInBag = true,
shareOffset = v4,
level = 2,
customDesc = v5
},
[407021] = {
id = 407021,
effect = true,
name = "丹霞垂天 杨玉环",
desc = "（限峡谷模式使用）获取后可以获得杨玉环的英雄皮肤。",
icon = "T_Arena_Role_1045_02",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_261_moba",
material = "MI_PL_261_01_HP01_moba;MI_PL_261_02_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "SK_PL_261_moba_Physics",
materialSlot = "skin;skin_1"
},
outEnter = "AS_CH_Enter_PL_261_moba",
outIdle = "AS_CH_Outidle_001_moba_PL_261",
outShow = "AS_CH_IdleShow_PL_261_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "这位风华绝代的神秘美人，只需要轻轻拨动手中的琴弦便能让整个长安城为之撼动。她的琵琶像是有神奇的魔力，能让人沉醉于内心对美好事物的无限渴求。人们赞赏她，倾慕她，却并不真的了解她，就像她空洞的内心也不能真正理解口口相传的“幸福”究竟为何物，兴许只有某位同样神秘的牡丹方士对她的过往略知一二。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
level = 2,
customDesc = v5
},
[407022] = {
id = 407022,
effect = true,
name = "青幽冥铠 钟馗",
desc = "（限峡谷模式使用）获取后可以获得钟馗的英雄皮肤。",
icon = "T_Arena_Role_1015_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_158_dev",
material = "MI_PL_158_01_HP01_moba",
headOffset = v4,
backOffset = v4,
faceOffset = v4,
physics = "sk_pl_158_dev_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_158_moba",
outIdle = v0,
outShow = "AS_CH_IdleShow_PL_158_moba",
outShowIntervalTime = 10,
scaleTimes = 100,
suitStoryTextKey = "钟馗存在于长安城神秘的“门”中，那里通往虚空，也通往一切智慧根源。作为门内之物的守护者，它比长安城还要更加古老，每个午夜子时，它都会从门中来到朱雀大街，巡视着这座在白日里无比繁华的城市，夜幕之下，一切试图破坏长安之人，都会被它吸入混沌之中，人们视它为“鬼”。而它，只是在执行着守护的指令。",
bHideInBag = true,
level = 2,
customDesc = v5
}
}

local mt = {
effect = false,
type = "ItemType_Arena_HeroSkin",
maxNum = 1,
quality = 3,
billboardOffsetZ = 0,
bHideInBag = false,
tags = {
1
},
level = 1
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data