--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 炫彩字

local data = {
[970001] = {
id = 970001,
type = "ItemType_ChatColorFont",
maxNum = 1,
quality = 3,
name = "炫彩",
desc = "星宝会员卡用户专享",
icon = "CDN:T_Common_Item_System_Social_Font",
picture = "T_Img_ColoredText_Bg",
showInView = 1,
isDynamic = 0,
bubbleColor = "R1",
showDesc = "炫彩字体颜色",
mallJumpId = {
34
},
jumpText = {
"月卡界面"
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data