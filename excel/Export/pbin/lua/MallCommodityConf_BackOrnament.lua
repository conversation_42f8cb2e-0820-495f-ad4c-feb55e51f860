--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-背饰

local v0 = {
seconds = 1702569600
}

local v1 = {
seconds = 1676390400
}

local v2 = {
seconds = 4074768000
}

local v3 = {
seconds = 1676476800
}

local v4 = {
seconds = 4074854400
}

local v5 = {
2,
13
}

local v6 = 2

local v7 = 1

local v8 = "赛季祈愿"

local v9 = "峡谷幻梦"

local v10 = "1.2.67.1"

local v11 = "1.2.100.1"

local v12 = "1.3.6.1"

local v13 = "1.3.12.118"

local v14 = 80

local v15 = 218

local v16 = 60

local v17 = 6

local v18 = 200008

local v19 = 10000

local v20 = 5

local v21 = 10

local data = {
[70001] = {
commodityId = 70001,
commodityName = "剑仙之剑",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopTag = v5,
shopSort = 2,
jumpId = 11,
jumpText = v8,
itemIds = {
620013
},
bOpenSuit = true,
suitId = 60009
},
[70002] = {
commodityId = 70002,
commodityName = "快乐之钥",
beginTime = v1,
shopTag = v5,
shopSort = 4,
jumpId = 8,
jumpText = "充值福利",
itemIds = {
620019
},
bOpenSuit = true,
suitId = 60015
},
[70003] = {
commodityId = 70003,
commodityName = "幻乐天鹅竖琴",
beginTime = v1,
shopTag = v5,
shopSort = 8,
jumpId = 8,
jumpText = "充值福利",
minVersion = v10,
itemIds = {
620014
},
bOpenSuit = true,
suitId = 60010
},
[70004] = {
commodityId = 70004,
commodityName = "蓝幽夜弓",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v5,
shopSort = 2,
jumpId = 185,
jumpText = "霜天冰雨",
itemIds = {
620020
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 60016
},
[70005] = {
commodityId = 70005,
commodityName = "橙装妹子配套",
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620018
},
suitId = 60014
},
[70006] = {
commodityId = 70006,
commodityName = "偶像歌手-话筒",
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620016
},
suitId = 60012
},
[70007] = {
commodityId = 70007,
commodityName = "潮流背饰",
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620021
},
suitId = 60017
},
[70008] = {
commodityId = 70008,
commodityName = "追风头盔",
beginTime = v1,
shopTag = v5,
shopSort = 9,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620022
},
bOpenSuit = true,
suitId = 60018
},
[70009] = {
commodityId = 70009,
commodityName = "小恶魔吉他",
beginTime = v1,
shopTag = v5,
shopSort = 10,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620001
},
bOpenSuit = true,
suitId = 60001
},
[70010] = {
commodityId = 70010,
commodityName = "缤纷甜甜圈",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopTag = v5,
shopSort = 11,
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
620023
},
bOpenSuit = true,
suitId = 60019
},
[70011] = {
commodityId = 70011,
commodityName = "热血电玩",
beginTime = {
seconds = 1704643200
},
endTime = {
seconds = 1704988799
},
shopTag = v5,
jumpId = 73,
jumpText = "赶海小队",
itemIds = {
620024
},
bOpenSuit = true,
suitId = 60020
},
[70012] = {
commodityId = 70012,
commodityName = "特工的伪装",
beginTime = v1,
endTime = v3,
shopTag = v5,
shopSort = 2,
jumpId = 42,
jumpText = "随心挑战礼",
itemIds = {
620005
},
bOpenSuit = true,
suitId = 60005
},
[70014] = {
commodityId = 70014,
commodityName = "绅士伞",
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620025
},
suitId = 60021
},
[70015] = {
commodityId = 70015,
commodityName = "哭哭咸鱼",
coinType = 6,
price = 500,
beginTime = v1,
shopTag = v5,
shopSort = 2,
itemIds = {
620008
},
canGift = true,
addIntimacy = 50,
giftCoinType = 1,
giftPrice = 500,
suitId = 60006
},
[70016] = {
commodityId = 70016,
commodityName = "骑士背包",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopTag = v5,
shopSort = 12,
jumpId = 42,
jumpText = "星宝来了",
itemIds = {
620011
},
bOpenSuit = true,
suitId = 60007
},
[70017] = {
commodityId = 70017,
commodityName = "飞行员背包",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620015
},
suitId = 60011
},
[70018] = {
commodityId = 70018,
commodityName = "大鸡腿",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620026
},
suitId = 60022
},
[70019] = {
commodityId = 70019,
commodityName = "熊熊夏日冰",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620027
},
suitId = 60023
},
[70020] = {
commodityId = 70020,
commodityName = "星运背包",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620028
},
suitId = 60024
},
[70021] = {
commodityId = 70021,
commodityName = "甜橙能量汁",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1709827199
},
shopTag = v5,
shopSort = 6,
jumpId = 171,
jumpText = "充值送好礼",
itemIds = {
620029
},
bOpenSuit = true,
suitId = 60025
},
[70022] = {
commodityId = 70022,
commodityName = "粉嘟嘟兔礼盒",
coinType = 6,
price = 800,
beginTime = v1,
shopTag = v5,
itemIds = {
620044
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 800,
suitId = 60031
},
[70023] = {
commodityId = 70023,
commodityName = "嘭嘭爆米花",
beginTime = v1,
endTime = v3,
shopTag = v5,
shopSort = 6,
jumpId = 43,
jumpText = "限时福利赏",
itemIds = {
620031
},
bOpenSuit = true,
suitId = 60026
},
[70024] = {
commodityId = 70024,
commodityName = "梦幻蝶翼",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopTag = v5,
shopSort = 2,
jumpId = 11,
jumpText = v8,
itemIds = {
620032
},
bOpenSuit = true,
suitId = 60027
},
[70025] = {
commodityId = 70025,
commodityName = "梦幻蝶翼",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
620033
}
},
[70026] = {
commodityId = 70026,
commodityName = "梦幻蝶翼",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
620034
}
},
[70027] = {
commodityId = 70027,
commodityName = "星梦背包",
beginTime = {
seconds = 1709827200
},
endTime = {
seconds = 1710431999
},
shopTag = v5,
shopSort = 2,
jumpId = 22,
jumpText = "云端星梦",
itemIds = {
620035
},
bOpenSuit = true,
suitId = 60028
},
[70028] = {
commodityId = 70028,
commodityName = "星梦背包",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704643200
},
itemIds = {
620036
}
},
[70029] = {
commodityId = 70029,
commodityName = "星梦背包",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704643200
},
itemIds = {
620037
}
},
[70030] = {
commodityId = 70030,
commodityName = "仙魄葫芦",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopTag = v5,
shopSort = 3,
jumpId = 11,
jumpText = v8,
itemIds = {
620038
},
bOpenSuit = true,
suitId = 60029
},
[70031] = {
commodityId = 70031,
commodityName = "仙魄葫芦",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620039
}
},
[70032] = {
commodityId = 70032,
commodityName = "仙魄葫芦",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620040
}
},
[70033] = {
commodityId = 70033,
commodityName = "守护之翼",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v5,
shopSort = 2,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
620041
},
bOpenSuit = true,
suitId = 60030
},
[70034] = {
commodityId = 70034,
commodityName = "守护之翼",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1704024000
},
itemIds = {
620042
}
},
[70035] = {
commodityId = 70035,
commodityName = "守护之翼",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1704024000
},
itemIds = {
620043
}
},
[70036] = {
commodityId = 70036,
commodityName = "粉嘟嘟兔礼盒",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620045
}
},
[70037] = {
commodityId = 70037,
commodityName = "粉嘟嘟兔礼盒",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620046
}
},
[70038] = {
commodityId = 70038,
commodityName = "星星平底锅",
beginTime = v1,
endTime = {
seconds = 1701964799
},
shopTag = v5,
shopSort = 2,
jumpId = 44,
jumpText = "小雪接星运",
itemIds = {
620047
},
bOpenSuit = true,
suitId = 60032
},
[70039] = {
commodityId = 70039,
commodityName = "蓝幽夜弓",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620048
}
},
[70040] = {
commodityId = 70040,
commodityName = "蓝幽夜弓",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620049
}
},
[70041] = {
commodityId = 70041,
commodityName = "动感机能包",
beginTime = v1,
endTime = v3,
shopTag = v5,
shopSort = 7,
jumpId = 45,
jumpText = "娱乐大师礼",
itemIds = {
620012
},
bOpenSuit = true,
suitId = 60008
},
[70042] = {
commodityId = 70042,
commodityName = "真棒棒",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620017
},
suitId = 60013
},
[70043] = {
commodityId = 70043,
commodityName = "幻乐天鹅竖琴",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620050
}
},
[70044] = {
commodityId = 70044,
commodityName = "幻乐天鹅竖琴",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620051
}
},
[70045] = {
commodityId = 70045,
commodityName = "快乐之钥",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620052
}
},
[70046] = {
commodityId = 70046,
commodityName = "快乐之钥",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620053
}
},
[70047] = {
commodityId = 70047,
commodityName = "剑仙之剑",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620054
}
},
[70048] = {
commodityId = 70048,
commodityName = "剑仙之剑",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620055
}
},
[70049] = {
commodityId = 70049,
commodityName = "海精灵翅膀",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620056
},
suitId = 60033
},
[70050] = {
commodityId = 70050,
commodityName = "海精灵翅膀",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
620057
}
},
[70051] = {
commodityId = 70051,
commodityName = "海精灵翅膀",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
620058
}
},
[70052] = {
commodityId = 70052,
commodityName = "手q-恶魔翅膀",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620059
},
suitId = 60034
},
[70053] = {
commodityId = 70053,
commodityName = "手q-恶魔翅膀",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620060
}
},
[70054] = {
commodityId = 70054,
commodityName = "手q-恶魔翅膀",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620061
}
},
[70055] = {
commodityId = 70055,
commodityName = "微信-天使翅膀",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620062
},
suitId = 60035
},
[70056] = {
commodityId = 70056,
commodityName = "微信-天使翅膀",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620063
}
},
[70057] = {
commodityId = 70057,
commodityName = "微信-天使翅膀",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620064
}
},
[70058] = {
commodityId = 70058,
commodityName = "爱心粉翅膀",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620065
},
suitId = 60036
},
[70059] = {
commodityId = 70059,
commodityName = "爱心粉翅膀",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620066
}
},
[70060] = {
commodityId = 70060,
commodityName = "爱心粉翅膀",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620067
}
},
[70061] = {
commodityId = 70061,
commodityName = "沙漏背饰",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620068
},
suitId = 60037
},
[70062] = {
commodityId = 70062,
commodityName = "沙漏背饰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620069
}
},
[70063] = {
commodityId = 70063,
commodityName = "沙漏背饰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620070
}
},
[70064] = {
commodityId = 70064,
commodityName = "康达姆机器人背饰",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopTag = v5,
shopSort = 2,
jumpId = 78,
jumpText = "盛装小新祈愿",
itemIds = {
620071
},
bOpenSuit = true,
suitId = 60038
},
[70065] = {
commodityId = 70065,
commodityName = "小白背包",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopTag = v5,
shopSort = 2,
jumpId = 80,
jumpText = "盛装小新祈愿",
itemIds = {
620072
},
bOpenSuit = true,
suitId = 60039
},
[70066] = {
commodityId = 70066,
commodityName = "小红帽书包",
beginTime = {
seconds = 1729526400
},
endTime = {
seconds = 4080211199
},
shopTag = v5,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
620073
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 60040
},
[70067] = {
commodityId = 70067,
commodityName = "小红帽书包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620074
}
},
[70068] = {
commodityId = 70068,
commodityName = "小红帽书包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620075
}
},
[70069] = {
commodityId = 70069,
commodityName = "阿童木背饰",
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1718899199
},
shopTag = v5,
shopSort = 2,
jumpId = 183,
jumpText = "阿童木祈愿",
minVersion = v11,
itemIds = {
620076
},
bOpenSuit = true,
suitId = 60041
},
[70070] = {
commodityId = 70070,
commodityName = "福星背饰",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620077
},
suitId = 60042
},
[70071] = {
commodityId = 70071,
commodityName = "福星背饰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620078
}
},
[70072] = {
commodityId = 70072,
commodityName = "福星背饰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620079
}
},
[70073] = {
commodityId = 70073,
commodityName = "琳琅纱织",
beginTime = v0,
endTime = {
seconds = 1704383999
},
shopTag = v5,
jumpId = 44,
jumpText = "小乔送星运",
itemIds = {
620080
},
bOpenSuit = true,
suitId = 60043
},
[70074] = {
commodityId = 70074,
commodityName = "鲁班背饰",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620081
},
suitId = 60044
},
[70075] = {
commodityId = 70075,
commodityName = "狂想曲吉他",
beginTime = v1,
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620082
},
bOpenSuit = true,
suitId = 60045
},
[70076] = {
commodityId = 70076,
commodityName = "小熊背包",
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1705507200
},
shopTag = v5,
shopSort = 4,
jumpId = 82,
jumpText = "一元购背包",
itemIds = {
620083
},
bOpenSuit = true,
suitId = 60046
},
[70077] = {
commodityId = 70077,
commodityName = "小熊背包",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704038400
},
itemIds = {
620084
}
},
[70078] = {
commodityId = 70078,
commodityName = "小熊背包",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1704038400
},
itemIds = {
620085
}
},
[70079] = {
commodityId = 70079,
commodityName = "甜橙能量汁",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620086
}
},
[70080] = {
commodityId = 70080,
commodityName = "甜橙能量汁",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620087
}
},
[70081] = {
commodityId = 70081,
commodityName = "月光权重",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v5,
jumpId = 1062,
jumpText = "月夜歌吟",
minVersion = "1.3.26.33",
itemIds = {
620088
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 60047
},
[70082] = {
commodityId = 70082,
commodityName = "月光权重",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
620089
}
},
[70083] = {
commodityId = 70083,
commodityName = "月光权重",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
620090
}
},
[70084] = {
commodityId = 70084,
commodityName = "花韵之琴",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopTag = v5,
shopSort = 5,
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
620091
},
bOpenSuit = true,
suitId = 60048
},
[70085] = {
commodityId = 70085,
commodityName = "花韵之琴",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
620092
}
},
[70086] = {
commodityId = 70086,
commodityName = "花韵之琴",
coinType = 200008,
price = 5,
beginTime = v0,
itemIds = {
620093
}
},
[70087] = {
commodityId = 70087,
commodityName = "追风头盔",
beginTime = v1,
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620094
},
bOpenSuit = true,
suitId = 60049
},
[70088] = {
commodityId = 70088,
commodityName = "追梦头盔",
beginTime = v1,
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620095
},
bOpenSuit = true,
suitId = 60050
},
[70089] = {
commodityId = 70089,
commodityName = "梦梦背包",
beginTime = v1,
shopTag = v5,
jumpId = 74,
jumpText = "造梦之旅",
itemIds = {
620096
},
bOpenSuit = true,
suitId = 60051
},
[70090] = {
commodityId = 70090,
commodityName = "造梦奖杯",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620097
},
suitId = 60052
},
[70091] = {
commodityId = 70091,
commodityName = "造梦奖杯",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620098
}
},
[70092] = {
commodityId = 70092,
commodityName = "造梦魔法棒",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620099
},
suitId = 60053
},
[70093] = {
commodityId = 70093,
commodityName = "toby的小玩具",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v5,
jumpId = 1091,
jumpText = "摩天乐园",
minVersion = "1.3.12.70",
itemIds = {
620100
},
bOpenSuit = true,
suitId = 60054
},
[70094] = {
commodityId = 70094,
commodityName = "瑶的手杖",
coinType = 6,
price = 10000,
beginTime = v1,
endTime = v3,
shopTag = v5,
itemIds = {
620101
},
suitId = 60055
},
[70095] = {
commodityId = 70095,
commodityName = "九环锡杖",
coinType = 6,
price = 500,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
shopTag = v5,
itemIds = {
620148
},
suitId = 60080
},
[70096] = {
commodityId = 70096,
commodityName = "金箍棒",
coinType = 6,
price = 500,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
shopTag = v5,
itemIds = {
620112
},
suitId = 60064
},
[71000] = {
commodityId = 71000,
commodityName = "泽塔升华器",
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1711295999
},
shopTag = v5,
jumpId = 179,
jumpText = "奥特曼祈愿",
minVersion = v10,
itemIds = {
620102
},
bOpenSuit = true,
suitId = 60056
},
[71001] = {
commodityId = 71001,
commodityName = "神光棒",
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1711295999
},
shopTag = v5,
jumpId = 179,
jumpText = "奥特曼祈愿",
minVersion = v10,
itemIds = {
620103
},
bOpenSuit = true,
suitId = 60057
},
[71002] = {
commodityId = 71002,
commodityName = "蜜瓶儿",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopTag = v5,
jumpId = 1062,
jumpText = "月夜歌吟",
minVersion = "1.3.26.33",
itemIds = {
620104
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 60058
},
[71003] = {
commodityId = 71003,
commodityName = "蜜瓶儿",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
620105
}
},
[71004] = {
commodityId = 71004,
commodityName = "蜜瓶儿",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.3.26.33",
itemIds = {
620106
}
},
[71005] = {
commodityId = 71005,
commodityName = "胜利队头盔",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620107
},
suitId = 60059
},
[71006] = {
commodityId = 71006,
commodityName = "年年有余背饰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620108
},
suitId = 60060
},
[71007] = {
commodityId = 71007,
commodityName = "糖葫芦",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620109
},
suitId = 60061
},
[71008] = {
commodityId = 71008,
commodityName = "中国结福袋（福字正贴）",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620110
},
suitId = 60062
},
[71009] = {
commodityId = 71009,
commodityName = "莫生气圆形扇子",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620111
},
suitId = 60063
},
[71010] = {
commodityId = 71010,
commodityName = "红包",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v5,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.26.61",
itemIds = {
620113
},
bOpenSuit = true,
suitId = 60065
},
[71011] = {
commodityId = 71011,
commodityName = "扇子",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1709827199
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "山海通行证",
minVersion = v10,
itemIds = {
620114
},
bOpenSuit = true,
suitId = 60066
},
[71012] = {
commodityId = 71012,
commodityName = "显眼包背饰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620115
},
suitId = 60067
},
[71013] = {
commodityId = 71013,
commodityName = "龙崽宝宝",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620116
},
suitId = 60068
},
[71014] = {
commodityId = 71014,
commodityName = "龙崽宝宝",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620117
}
},
[71015] = {
commodityId = 71015,
commodityName = "龙崽宝宝",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620118
}
},
[71016] = {
commodityId = 71016,
commodityName = "舞狮",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620119
},
suitId = 60069
},
[71017] = {
commodityId = 71017,
commodityName = "舞狮",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620120
}
},
[71018] = {
commodityId = 71018,
commodityName = "舞狮",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620121
}
},
[71019] = {
commodityId = 71019,
commodityName = "鞭炮",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620122
},
suitId = 60070
},
[71020] = {
commodityId = 71020,
commodityName = "鞭炮",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620123
}
},
[71021] = {
commodityId = 71021,
commodityName = "鞭炮",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620124
}
},
[71022] = {
commodityId = 71022,
commodityName = "企鹅背包",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1713715199
},
shopTag = v5,
shopSort = 2,
jumpId = 82,
jumpText = "一元购背包",
minVersion = v10,
itemIds = {
620125
},
bOpenSuit = true,
suitId = 60071
},
[71023] = {
commodityId = 71023,
commodityName = "企鹅背包",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620126
}
},
[71024] = {
commodityId = 71024,
commodityName = "企鹅背包",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620127
}
},
[71025] = {
commodityId = 71025,
commodityName = "梅花枝",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1709827199
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "山海通行证",
minVersion = v10,
itemIds = {
620128
},
bOpenSuit = true,
suitId = 60072
},
[71026] = {
commodityId = 71026,
commodityName = "梅花枝",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1706198400
},
minVersion = v10,
itemIds = {
620129
}
},
[71027] = {
commodityId = 71027,
commodityName = "梅花枝",
coinType = 200008,
price = 5,
minVersion = v10,
itemIds = {
620130
}
},
[71028] = {
commodityId = 71028,
commodityName = "忧郁王子-发条背饰",
beginTime = {
seconds = 1729526400
},
endTime = {
seconds = 4080211199
},
shopTag = v5,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
620131
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 60073
},
[71029] = {
commodityId = 71029,
commodityName = "忧郁王子-发条背饰",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620132
}
},
[71030] = {
commodityId = 71030,
commodityName = "忧郁王子-发条背饰",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620133
}
},
[71031] = {
commodityId = 71031,
commodityName = "古风灯笼背饰",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
shopTag = v5,
jumpId = 181,
jumpText = "绮梦灯",
minVersion = "1.2.80.1",
itemIds = {
620134
},
bOpenSuit = true,
suitId = 60074
},
[71032] = {
commodityId = 71032,
commodityName = "古风灯笼背饰",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707494400
},
minVersion = "1.2.80.1",
itemIds = {
620135
}
},
[71033] = {
commodityId = 71033,
commodityName = "古风灯笼背饰",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1707494400
},
minVersion = "1.2.80.1",
itemIds = {
620136
}
},
[71034] = {
commodityId = 71034,
commodityName = "狴犴翅膀",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopTag = v5,
shopSort = 2,
jumpId = 175,
jumpText = v8,
minVersion = v10,
itemIds = {
620137
},
bOpenSuit = true,
suitId = 60075
},
[71035] = {
commodityId = 71035,
commodityName = "狴犴翅膀",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1706198400
},
minVersion = v10,
itemIds = {
620138
}
},
[71036] = {
commodityId = 71036,
commodityName = "狴犴翅膀",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1706198400
},
minVersion = v10,
itemIds = {
620139
}
},
[71037] = {
commodityId = 71037,
commodityName = "锦鲤扇子",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
shopTag = v5,
jumpId = 171,
jumpText = "充值送好礼",
minVersion = "1.2.80.1",
itemIds = {
620141
},
bOpenSuit = true,
suitId = 60077
},
[71038] = {
commodityId = 71038,
commodityName = "锦鲤扇子",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.2.80.1",
itemIds = {
620142
}
},
[71039] = {
commodityId = 71039,
commodityName = "锦鲤扇子",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1707408000
},
minVersion = "1.2.80.1",
itemIds = {
620143
}
},
[71040] = {
commodityId = 71040,
commodityName = "丘比特爱心箭",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620144
},
suitId = 60078
},
[71041] = {
commodityId = 71041,
commodityName = "丘比特爱心箭",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620145
}
},
[71042] = {
commodityId = 71042,
commodityName = "丘比特爱心箭",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620146
}
},
[71043] = {
commodityId = 71043,
commodityName = "奶龙配饰",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v5,
jumpId = 1047,
jumpText = "奶龙返场",
minVersion = "1.3.7.97",
itemIds = {
620147
},
bOpenSuit = true,
suitId = 60079
},
[71044] = {
commodityId = 71044,
commodityName = "海浪背饰",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopTag = v5,
shopSort = 2,
jumpId = 175,
jumpText = v8,
minVersion = v10,
itemIds = {
620149
},
bOpenSuit = true,
suitId = 60081
},
[71045] = {
commodityId = 71045,
commodityName = "海浪背饰",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1706198400
},
minVersion = v10,
itemIds = {
620150
}
},
[71046] = {
commodityId = 71046,
commodityName = "海浪背饰",
coinType = 200008,
price = 10,
beginTime = {
seconds = 1706198400
},
minVersion = v10,
itemIds = {
620151
}
},
[71047] = {
commodityId = 71047,
commodityName = "雪域逐梦",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
shopTag = v5,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
minVersion = "1.3.26.93",
itemIds = {
620152
},
bOpenSuit = true,
suitId = 60082
},
[71048] = {
commodityId = 71048,
commodityName = "雪域逐梦",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = "1.3.26.93",
itemIds = {
620153
}
},
[71049] = {
commodityId = 71049,
commodityName = "雪域逐梦",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = "1.3.26.93",
itemIds = {
620154
}
},
[71050] = {
commodityId = 71050,
commodityName = "水晶玫瑰",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = v5,
shopSort = 2,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.2.80.1",
itemIds = {
620155
},
bOpenSuit = true,
suitId = 60083
},
[71051] = {
commodityId = 71051,
commodityName = "水晶玫瑰",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = v10,
itemIds = {
620156
}
},
[71052] = {
commodityId = 71052,
commodityName = "水晶玫瑰",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = v10,
itemIds = {
620157
}
},
[71053] = {
commodityId = 71053,
commodityName = "星宝花束",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620158
},
suitId = 60084
},
[71054] = {
commodityId = 71054,
commodityName = "星宝花束",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620159
}
},
[71055] = {
commodityId = 71055,
commodityName = "星宝花束",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620160
}
},
[71056] = {
commodityId = 71056,
commodityName = "福袋",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v10,
itemIds = {
620140
},
suitId = 60076
},
[71057] = {
commodityId = 71057,
commodityName = "福袋",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620161
}
},
[71058] = {
commodityId = 71058,
commodityName = "福袋",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v10,
itemIds = {
620162
}
},
[71059] = {
commodityId = 71059,
commodityName = "蜜桃喵喵糖",
beginTime = v1,
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
minVersion = v10,
itemIds = {
620164
},
bOpenSuit = true,
suitId = 60085
},
[71060] = {
commodityId = 71060,
commodityName = "青柠喵喵糖",
beginTime = v1,
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
minVersion = v10,
itemIds = {
620165
},
bOpenSuit = true,
suitId = 60086
},
[71061] = {
commodityId = 71061,
commodityName = "香芒喵喵糖",
beginTime = v1,
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
minVersion = v10,
itemIds = {
620166
},
bOpenSuit = true,
suitId = 60087
},
[71101] = {
commodityId = 71101,
commodityName = "魔方格格",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopTag = v5,
shopSort = 2,
jumpId = 186,
jumpText = "春水溯游",
minVersion = v11,
itemIds = {
620169
},
bOpenSuit = true,
suitId = 60090
},
[71102] = {
commodityId = 71102,
commodityName = "魔方格格",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620170
}
},
[71103] = {
commodityId = 71103,
commodityName = "魔方格格",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620171
}
},
[71104] = {
commodityId = 71104,
commodityName = "涂山璟 识神小狐",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v5,
jumpId = 1054,
jumpText = "长相思",
minVersion = "1.3.12.47",
itemIds = {
620172
},
bOpenSuit = true,
suitId = 60091
},
[71105] = {
commodityId = 71105,
commodityName = "玱玹 若木花簪",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v5,
jumpId = 1054,
jumpText = "长相思",
minVersion = "1.3.12.47",
itemIds = {
620173
},
bOpenSuit = true,
suitId = 60092
},
[71106] = {
commodityId = 71106,
commodityName = "丰隆 火球",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v5,
jumpId = 1054,
jumpText = "长相思",
minVersion = "1.3.12.47",
itemIds = {
620174
},
bOpenSuit = true,
suitId = 60093
},
[71107] = {
commodityId = 71107,
commodityName = "小夭 团扇",
beginTime = {
seconds = 1721923200
},
endTime = {
seconds = 1724342399
},
shopTag = v5,
jumpId = 1054,
jumpText = "长相思",
minVersion = "1.3.12.47",
itemIds = {
620175
},
bOpenSuit = true,
suitId = 60094
},
[71109] = {
commodityId = 71109,
commodityName = "星光之矢",
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1726761599
},
shopTag = v5,
shopSort = 2,
jumpId = 185,
jumpText = "霜天冰雨",
itemIds = {
620176
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 60095
},
[71110] = {
commodityId = 71110,
commodityName = "星光之矢",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620177
}
},
[71111] = {
commodityId = 71111,
commodityName = "星光之矢",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620178
}
},
[71112] = {
commodityId = 71112,
commodityName = "超级战术包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620179
},
suitId = 60096
},
[71113] = {
commodityId = 71113,
commodityName = "超级战术包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620180
}
},
[71114] = {
commodityId = 71114,
commodityName = "超级战术包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620181
}
},
[71115] = {
commodityId = 71115,
commodityName = "月牙铲",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620182
},
suitId = 60097
},
[71116] = {
commodityId = 71116,
commodityName = "时光漂流瓶",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v5,
shopSort = 2,
jumpId = 189,
jumpText = v8,
itemIds = {
620183
},
bOpenSuit = true,
suitId = 60098
},
[71117] = {
commodityId = 71117,
commodityName = "时光漂流瓶",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620184
}
},
[71118] = {
commodityId = 71118,
commodityName = "时光漂流瓶",
coinType = 200008,
price = 10,
beginTime = v0,
itemIds = {
620185
}
},
[71119] = {
commodityId = 71119,
commodityName = "功夫熊猫背饰",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v5,
jumpId = 1048,
jumpText = "功夫熊猫返场",
minVersion = "1.3.7.97",
itemIds = {
620186
},
bOpenSuit = true,
suitId = 60099
},
[71120] = {
commodityId = 71120,
commodityName = "师傅笛子",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopTag = v5,
jumpId = 1048,
jumpText = "功夫熊猫返场",
minVersion = "1.3.7.97",
itemIds = {
620187
},
bOpenSuit = true,
suitId = 60100
},
[71121] = {
commodityId = 71121,
commodityName = "时光手提箱",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v5,
shopSort = 2,
jumpId = 189,
jumpText = v8,
itemIds = {
620188
},
bOpenSuit = true,
suitId = 60101
},
[71122] = {
commodityId = 71122,
commodityName = "时光手提箱",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620189
}
},
[71123] = {
commodityId = 71123,
commodityName = "时光手提箱",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620190
}
},
[71124] = {
commodityId = 71124,
commodityName = "九齿钉耙",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620191
},
suitId = 60102
},
[71125] = {
commodityId = 71125,
commodityName = "唤星之杖",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620192
},
suitId = 60103
},
[71126] = {
commodityId = 71126,
commodityName = "热辣小熊",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620193
},
suitId = 60104
},
[71127] = {
commodityId = 71127,
commodityName = "热辣小熊",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620194
}
},
[71128] = {
commodityId = 71128,
commodityName = "热辣小熊",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620195
}
},
[71129] = {
commodityId = 71129,
commodityName = "千千星鹤",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620196
},
suitId = 60105
},
[71130] = {
commodityId = 71130,
commodityName = "鸢尾花",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620197
},
suitId = 60106
},
[71131] = {
commodityId = 71131,
commodityName = "四季风车",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620198
},
bOpenSuit = true,
suitId = 60107
},
[71132] = {
commodityId = 71132,
commodityName = "金巧风车",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620199
},
bOpenSuit = true,
suitId = 60108
},
[71133] = {
commodityId = 71133,
commodityName = "折纸风车",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
shopTag = v5,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
620200
},
bOpenSuit = true,
suitId = 60109
},
[71134] = {
commodityId = 71134,
commodityName = "仙木法剑",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620201
},
suitId = 60110
},
[71135] = {
commodityId = 71135,
commodityName = "煦风使者",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "时光通行证",
itemIds = {
620202
},
bOpenSuit = true,
suitId = 60111
},
[71136] = {
commodityId = 71136,
commodityName = "煦风使者",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620203
}
},
[71137] = {
commodityId = 71137,
commodityName = "煦风使者",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620204
}
},
[71138] = {
commodityId = 71138,
commodityName = "神秘扫帚",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620205
},
suitId = 60112
},
[71139] = {
commodityId = 71139,
commodityName = "嘟嘟奶瓶",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620206
},
suitId = 60113
},
[71140] = {
commodityId = 71140,
commodityName = "嘟嘟奶瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620207
}
},
[71141] = {
commodityId = 71141,
commodityName = "嘟嘟奶瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620208
}
},
[71142] = {
commodityId = 71142,
commodityName = "鼓鼓钱袋",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "潮音通行证",
minVersion = v11,
itemIds = {
620209
},
bOpenSuit = true,
suitId = 60114
},
[71143] = {
commodityId = 71143,
commodityName = "猎梦之镰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620210
},
suitId = 60115
},
[71144] = {
commodityId = 71144,
commodityName = "风之鸢",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620211
},
suitId = 60116
},
[71145] = {
commodityId = 71145,
commodityName = "奶芯兔子",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620212
},
suitId = 60117
},
[71146] = {
commodityId = 71146,
commodityName = "曲奇背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620213
},
suitId = 60118
},
[71147] = {
commodityId = 71147,
commodityName = "丞丞的日记本",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620214
},
suitId = 60119
},
[71148] = {
commodityId = 71148,
commodityName = "大勋大玉米",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620215
},
suitId = 60120
},
[71149] = {
commodityId = 71149,
commodityName = "星月之轮",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v5,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.26.61",
itemIds = {
620216
},
bOpenSuit = true,
suitId = 60121
},
[71150] = {
commodityId = 71150,
commodityName = "星月之轮",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620217
}
},
[71151] = {
commodityId = 71151,
commodityName = "星月之轮",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620218
}
},
[71152] = {
commodityId = 71152,
commodityName = "神速喇叭",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1713455999
},
shopTag = v5,
jumpId = 158,
jumpText = "星愿之旅",
itemIds = {
620225
},
bOpenSuit = true,
suitId = 60126
},
[71153] = {
commodityId = 71153,
commodityName = "鸭梨宝背",
beginTime = {
seconds = 1716825600
},
endTime = {
seconds = 1718035199
},
shopTag = v5,
shopSort = 2,
jumpId = 25,
jumpText = "特惠礼包",
minVersion = v11,
itemIds = {
620220
},
bOpenSuit = true,
suitId = 60123
},
[71154] = {
commodityId = 71154,
commodityName = "星光变身器",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620221
},
suitId = 60124
},
[71155] = {
commodityId = 71155,
commodityName = "星光变身器",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620222
}
},
[71156] = {
commodityId = 71156,
commodityName = "星光变身器",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620223
}
},
[71157] = {
commodityId = 71157,
commodityName = "星星报",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620224
},
suitId = 60125
},
[71158] = {
commodityId = 71158,
commodityName = "抱抱花苞",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620219
},
suitId = 60122
},
[71159] = {
commodityId = 71159,
commodityName = "柠檬背饰",
beginTime = {
seconds = 1722873600
},
endTime = {
seconds = 1724083199
},
shopTag = v5,
jumpId = 1059,
jumpText = "一元购背包",
minVersion = "1.3.12.70",
itemIds = {
620226
},
bOpenSuit = true,
suitId = 60127
},
[71160] = {
commodityId = 71160,
commodityName = "柠檬背饰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620227
}
},
[71161] = {
commodityId = 71161,
commodityName = "柠檬背饰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620228
}
},
[71162] = {
commodityId = 71162,
commodityName = "浮光绘羽",
beginTime = {
seconds = 1729526400
},
endTime = v4,
shopTag = v5,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
minVersion = v11,
itemIds = {
620229
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 60128
},
[71163] = {
commodityId = 71163,
commodityName = "浮光绘羽",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620230
}
},
[71164] = {
commodityId = 71164,
commodityName = "浮光绘羽",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620231
}
},
[71165] = {
commodityId = 71165,
commodityName = "妙语童话书",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopTag = v5,
jumpId = 186,
jumpText = "春水溯游",
minVersion = v11,
itemIds = {
620232
},
bOpenSuit = true,
suitId = 60129
},
[71166] = {
commodityId = 71166,
commodityName = "妙语童话书",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620233
}
},
[71167] = {
commodityId = 71167,
commodityName = "妙语童话书",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620234
}
},
[71168] = {
commodityId = 71168,
commodityName = "汪汪气球",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620235
},
suitId = 60130
},
[71169] = {
commodityId = 71169,
commodityName = "汪汪气球",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620236
}
},
[71170] = {
commodityId = 71170,
commodityName = "汪汪气球",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620237
}
},
[71171] = {
commodityId = 71171,
commodityName = "怪怪背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620238
},
suitId = 60131
},
[71172] = {
commodityId = 71172,
commodityName = "怪怪背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620239
}
},
[71173] = {
commodityId = 71173,
commodityName = "怪怪背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620240
}
},
[71174] = {
commodityId = 71174,
commodityName = "招牌可颂",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620241
},
suitId = 60132
},
[71175] = {
commodityId = 71175,
commodityName = "迷你键盘",
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopTag = v5,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.26.61",
itemIds = {
620242
},
bOpenSuit = true,
suitId = 60133
},
[71176] = {
commodityId = 71176,
commodityName = "火焰三叉戟",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620243
},
suitId = 60134
},
[71177] = {
commodityId = 71177,
commodityName = "火焰三叉戟",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620244
}
},
[71178] = {
commodityId = 71178,
commodityName = "火焰三叉戟",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620245
}
},
[71179] = {
commodityId = 71179,
commodityName = "暗夜冰羽",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
shopTag = v5,
shopSort = 2,
jumpId = 177,
jumpText = "暗夜冰羽",
minVersion = v11,
itemIds = {
620246
},
bOpenSuit = true,
suitId = 60135
},
[71180] = {
commodityId = 71180,
commodityName = "暗夜冰羽",
coinType = 200008,
price = 20,
beginTime = v0,
itemIds = {
620247
}
},
[71181] = {
commodityId = 71181,
commodityName = "暗夜冰羽",
coinType = 200008,
price = 20,
beginTime = v0,
itemIds = {
620248
}
},
[71182] = {
commodityId = 71182,
commodityName = "薯条",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620249
},
suitId = 60136
},
[71183] = {
commodityId = 71183,
commodityName = "麻将",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620250
},
suitId = 60137
},
[71184] = {
commodityId = 71184,
commodityName = "妮妮的兔子",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v5,
shopSort = 1,
jumpId = 614,
jumpText = "向日葵小班",
itemIds = {
620251
},
bOpenSuit = true,
suitId = 60138
},
[71185] = {
commodityId = 71185,
commodityName = "山竹背饰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620252
},
suitId = 60139
},
[71186] = {
commodityId = 71186,
commodityName = "山竹背饰",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620253
}
},
[71187] = {
commodityId = 71187,
commodityName = "山竹背饰",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620254
}
},
[71188] = {
commodityId = 71188,
commodityName = "特战先锋",
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1717689599
},
shopTag = v5,
shopSort = 2,
jumpId = 179,
jumpText = "前线装备库",
minVersion = "1.2.100.65",
itemIds = {
620255
},
bOpenSuit = true,
suitId = 60140
},
[71189] = {
commodityId = 71189,
commodityName = "特战先锋",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620256
}
},
[71190] = {
commodityId = 71190,
commodityName = "特战先锋",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620257
}
},
[71191] = {
commodityId = 71191,
commodityName = "浇水壶",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620258
},
suitId = 60141
},
[71192] = {
commodityId = 71192,
commodityName = "鹅毛枕头",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620259
},
suitId = 60142
},
[71193] = {
commodityId = 71193,
commodityName = "电子鼓",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v5,
shopSort = 2,
jumpId = 180,
jumpText = v8,
minVersion = v11,
itemIds = {
620260
},
bOpenSuit = true,
suitId = 60143
},
[71194] = {
commodityId = 71194,
commodityName = "电子鼓",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620261
}
},
[71195] = {
commodityId = 71195,
commodityName = "电子鼓",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620262
}
},
[71196] = {
commodityId = 71196,
commodityName = "键盘乐器",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "潮音通行证",
minVersion = v11,
itemIds = {
620263
},
bOpenSuit = true,
suitId = 60144
},
[71197] = {
commodityId = 71197,
commodityName = "键盘乐器",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620264
}
},
[71198] = {
commodityId = 71198,
commodityName = "键盘乐器",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620265
}
},
[71199] = {
commodityId = 71199,
commodityName = "小椅子",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620266
},
suitId = 60145
},
[71200] = {
commodityId = 71200,
commodityName = "小铲子",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620267
},
suitId = 60146
},
[71201] = {
commodityId = 71201,
commodityName = "遮阳草帽",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620268
},
suitId = 60147
},
[71202] = {
commodityId = 71202,
commodityName = "音响",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620269
},
suitId = 60148
},
[71203] = {
commodityId = 71203,
commodityName = "星梦麦克风",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopTag = v5,
shopSort = 2,
jumpId = 180,
jumpText = v8,
minVersion = v11,
itemIds = {
620270
},
bOpenSuit = true,
suitId = 60149
},
[71204] = {
commodityId = 71204,
commodityName = "星梦麦克风",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = v11,
itemIds = {
620271
}
},
[71205] = {
commodityId = 71205,
commodityName = "星梦麦克风",
coinType = 200008,
price = 10,
beginTime = v0,
minVersion = v11,
itemIds = {
620272
}
},
[71206] = {
commodityId = 71206,
commodityName = "星奇铅笔",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620273
},
suitId = 60150
},
[71207] = {
commodityId = 71207,
commodityName = "西瓜冰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620274
},
suitId = 60151
},
[71208] = {
commodityId = 71208,
commodityName = "洗刷刷",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620275
},
suitId = 60152
},
[71209] = {
commodityId = 71209,
commodityName = "蔬菜筐筐",
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1717084799
},
shopTag = v5,
jumpId = 25,
jumpText = "特惠礼包",
minVersion = v11,
itemIds = {
620276
},
bOpenSuit = true,
suitId = 60153
},
[71210] = {
commodityId = 71210,
commodityName = "绛天之翼",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620277
},
suitId = 60154
},
[71211] = {
commodityId = 71211,
commodityName = "绛天战刃",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620278
},
suitId = 60155
},
[71212] = {
commodityId = 71212,
commodityName = "挚爱之锚",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620279
},
suitId = 60156
},
[71213] = {
commodityId = 71213,
commodityName = "心动和弦",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v5,
shopSort = 3,
jumpId = 10674,
jumpText = v9,
minVersion = "1.3.18.56",
itemIds = {
620280
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60157
},
[71214] = {
commodityId = 71214,
commodityName = "影龙之锋",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v5,
shopSort = 3,
jumpId = 10674,
jumpText = v9,
minVersion = "1.3.18.56",
itemIds = {
620281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60158
},
[71215] = {
commodityId = 71215,
commodityName = "小丸子书包",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v5,
shopSort = 2,
jumpId = 188,
jumpText = "盛装派对",
minVersion = v11,
itemIds = {
620282
},
bOpenSuit = true,
suitId = 60159
},
[71216] = {
commodityId = 71216,
commodityName = "小提琴",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopTag = v5,
shopSort = 2,
jumpId = 188,
jumpText = "盛装派对",
minVersion = v11,
itemIds = {
620283
},
bOpenSuit = true,
suitId = 60160
},
[71217] = {
commodityId = 71217,
commodityName = "小提琴",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620284
}
},
[71218] = {
commodityId = 71218,
commodityName = "小提琴",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620285
}
},
[71219] = {
commodityId = 71219,
commodityName = "戏曲靠旗",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620286
},
suitId = 60161
},
[71220] = {
commodityId = 71220,
commodityName = "戏曲靠旗",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620287
}
},
[71221] = {
commodityId = 71221,
commodityName = "戏曲靠旗",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620288
}
},
[71222] = {
commodityId = 71222,
commodityName = "星空猫舱",
beginTime = {
seconds = 1719244800
},
endTime = {
seconds = 1720454399
},
shopTag = v5,
jumpId = 25,
jumpText = "特惠礼包",
minVersion = "1.3.7.1",
itemIds = {
620289
},
bOpenSuit = true,
suitId = 60162
},
[71223] = {
commodityId = 71223,
commodityName = "星空猫舱",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620290
}
},
[71224] = {
commodityId = 71224,
commodityName = "星空猫舱",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620291
}
},
[71225] = {
commodityId = 71225,
commodityName = "彩虹手挎包",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v5,
jumpId = 1090,
jumpText = "夏日派对",
minVersion = "1.3.88.53",
itemIds = {
620292
},
bOpenSuit = true,
suitId = 60163
},
[71226] = {
commodityId = 71226,
commodityName = "彩虹手挎包",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620293
}
},
[71227] = {
commodityId = 71227,
commodityName = "彩虹手挎包",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v11,
itemIds = {
620294
}
},
[71228] = {
commodityId = 71228,
commodityName = "指弹时分",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620295
},
suitId = 60164
},
[71229] = {
commodityId = 71229,
commodityName = "萌系民谣",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v11,
itemIds = {
620296
},
suitId = 60165
},
[71230] = {
commodityId = 71230,
commodityName = "机械甲壳",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620297
},
suitId = 60166
},
[71231] = {
commodityId = 71231,
commodityName = "永恒之翼",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v5,
shopSort = 2,
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.2.100.46",
itemIds = {
620298
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 120,
bOpenSuit = true,
suitId = 60167
},
[71232] = {
commodityId = 71232,
commodityName = "永恒之翼",
coinType = 200008,
price = 20,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
620299
}
},
[71233] = {
commodityId = 71233,
commodityName = "永恒之翼",
coinType = 200008,
price = 20,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
620300
}
},
[71234] = {
commodityId = 71234,
commodityName = "星梦使者",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
shopTag = v5,
shopSort = 2,
jumpId = 176,
jumpText = "星梦使者",
itemIds = {
620301
},
bOpenSuit = true,
suitId = 60168
},
[71235] = {
commodityId = 71235,
commodityName = "星梦使者",
coinType = 200008,
price = 20,
beginTime = {
seconds = 1716566400
},
itemIds = {
620302
}
},
[71236] = {
commodityId = 71236,
commodityName = "星梦使者",
coinType = 200008,
price = 20,
beginTime = {
seconds = 1716566400
},
itemIds = {
620303
}
},
[71237] = {
commodityId = 71237,
commodityName = "萌檬包包",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v5,
jumpId = 10602,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
620304
},
bOpenSuit = true,
suitId = 60169
},
[71238] = {
commodityId = 71238,
commodityName = "透明伞",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v5,
jumpId = 10603,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
620305
},
bOpenSuit = true,
suitId = 60170
},
[71239] = {
commodityId = 71239,
commodityName = "天使背包",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = v5,
shopSort = 2,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
620306
},
bOpenSuit = true,
suitId = 60171
},
[71240] = {
commodityId = 71240,
commodityName = "天使背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620307
}
},
[71241] = {
commodityId = 71241,
commodityName = "天使背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620308
}
},
[71242] = {
commodityId = 71242,
commodityName = "幸福花束",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopTag = v5,
shopSort = 2,
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.2.100.46",
itemIds = {
620309
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 60172
},
[71243] = {
commodityId = 71243,
commodityName = "幸福花束",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
620310
}
},
[71244] = {
commodityId = 71244,
commodityName = "幸福花束",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1715875200
},
minVersion = "1.2.100.46",
itemIds = {
620311
}
},
[71245] = {
commodityId = 71245,
commodityName = "桃子背包",
beginTime = {
seconds = 1715961600
},
endTime = {
seconds = 1717171199
},
shopTag = v5,
shopSort = 2,
jumpId = 82,
jumpText = "一元购背包",
minVersion = "1.2.100.54",
itemIds = {
620312
},
bOpenSuit = true,
suitId = 60173
},
[71246] = {
commodityId = 71246,
commodityName = "桃子背包",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.2.100.54",
itemIds = {
620313
}
},
[71247] = {
commodityId = 71247,
commodityName = "桃子背包",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.2.100.54",
itemIds = {
620314
}
},
[71248] = {
commodityId = 71248,
commodityName = "天鹅羽扇",
beginTime = {
seconds = 1718380800
},
endTime = {
seconds = 1720367999
},
shopTag = v5,
shopSort = 2,
jumpId = 603,
jumpText = "双生曼舞",
minVersion = "1.3.7.31",
itemIds = {
620315
},
bOpenSuit = true,
suitId = 60174
},
[71249] = {
commodityId = 71249,
commodityName = "天鹅羽扇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620316
}
},
[71250] = {
commodityId = 71250,
commodityName = "天鹅羽扇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620317
}
},
[71251] = {
commodityId = 71251,
commodityName = "莎莉救生圈",
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1720108799
},
shopTag = v5,
shopSort = 2,
jumpId = 601,
jumpText = "时光小船",
minVersion = "1.3.7.31",
itemIds = {
620318
},
bOpenSuit = true,
suitId = 60175
},
[71252] = {
commodityId = 71252,
commodityName = "可妮蛋糕",
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1720108799
},
shopTag = v5,
shopSort = 2,
jumpId = 602,
jumpText = "时光小船",
minVersion = "1.3.7.31",
itemIds = {
620319
},
bOpenSuit = true,
suitId = 60176
},
[71253] = {
commodityId = 71253,
commodityName = "炫彩贝壳",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v12,
itemIds = {
620320
},
bOpenSuit = true,
suitId = 60177
},
[71254] = {
commodityId = 71254,
commodityName = "炫彩贝壳",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620321
}
},
[71255] = {
commodityId = 71255,
commodityName = "炫彩贝壳",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620322
}
},
[71256] = {
commodityId = 71256,
commodityName = "火焰战旗",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
shopTag = v5,
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
620323
},
bOpenSuit = true,
suitId = 60178
},
[71257] = {
commodityId = 71257,
commodityName = "火焰战旗",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1717689600
},
minVersion = "1.3.7.75",
itemIds = {
620324
}
},
[71258] = {
commodityId = 71258,
commodityName = "火焰战旗",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1717689600
},
minVersion = "1.3.7.75",
itemIds = {
620325
}
},
[71259] = {
commodityId = 71259,
commodityName = "充气鲨鱼",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v5,
shopSort = 2,
jumpId = 11,
jumpText = v8,
minVersion = v12,
itemIds = {
620326
},
bOpenSuit = true,
suitId = 60179
},
[71260] = {
commodityId = 71260,
commodityName = "充气鲨鱼",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620327
}
},
[71261] = {
commodityId = 71261,
commodityName = "充气鲨鱼",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620328
}
},
[71262] = {
commodityId = 71262,
commodityName = "蓝色晴天娃娃",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620329
},
suitId = 60180
},
[71263] = {
commodityId = 71263,
commodityName = "红色晴天娃娃",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620330
},
suitId = 60181
},
[71264] = {
commodityId = 71264,
commodityName = "救生圈",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v12,
itemIds = {
620331
},
bOpenSuit = true,
suitId = 60182
},
[71265] = {
commodityId = 71265,
commodityName = "清凉椰汁",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620332
},
suitId = 60183
},
[71266] = {
commodityId = 71266,
commodityName = "Q版小篮球",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620333
},
suitId = 60184
},
[71267] = {
commodityId = 71267,
commodityName = "可乐饮料",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620334
},
suitId = 60185
},
[71268] = {
commodityId = 71268,
commodityName = "小狗",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620335
},
suitId = 60186
},
[71269] = {
commodityId = 71269,
commodityName = "小狗",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620336
}
},
[71270] = {
commodityId = 71270,
commodityName = "小狗",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620337
}
},
[71271] = {
commodityId = 71271,
commodityName = "沙滩小卡车",
beginTime = {
seconds = 1718985600
},
endTime = {
seconds = 1720108799
},
shopTag = v5,
jumpId = 82,
jumpText = "一元购背包",
minVersion = "1.3.7.53",
itemIds = {
620338
},
bOpenSuit = true,
suitId = 60187
},
[71272] = {
commodityId = 71272,
commodityName = "沙滩小卡车",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620339
}
},
[71273] = {
commodityId = 71273,
commodityName = "沙滩小卡车",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620340
}
},
[71274] = {
commodityId = 71274,
commodityName = "复古留声机",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620341
},
suitId = 60188
},
[71275] = {
commodityId = 71275,
commodityName = "复古留声机",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620342
}
},
[71276] = {
commodityId = 71276,
commodityName = "复古留声机",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v12,
itemIds = {
620343
}
},
[71277] = {
commodityId = 71277,
commodityName = "油漆滚筒",
beginTime = {
seconds = 1718640000
},
endTime = {
seconds = 1720367999
},
shopTag = v5,
shopSort = 2,
jumpId = 604,
jumpText = "天鹅之心",
minVersion = "1.3.7.31",
itemIds = {
620344
},
bOpenSuit = true,
suitId = 60189
},
[71278] = {
commodityId = 71278,
commodityName = "荷包背饰",
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
shopTag = v5,
jumpId = 1084,
jumpText = "雨荷仙子",
minVersion = "1.3.7.122",
itemIds = {
620345
},
bOpenSuit = true,
suitId = 60190
},
[71279] = {
commodityId = 71279,
commodityName = "心态超好扇子",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = v12,
itemIds = {
620346
},
suitId = 60191
},
[71280] = {
commodityId = 71280,
commodityName = "超核钻石圣环",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620347
},
suitId = 60192
},
[71281] = {
commodityId = 71281,
commodityName = "超核钻石圣环",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620348
}
},
[71282] = {
commodityId = 71282,
commodityName = "超核钻石圣环",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620349
}
},
[71283] = {
commodityId = 71283,
commodityName = "绒绒布朗",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620350
},
suitId = 60193
},
[71284] = {
commodityId = 71284,
commodityName = "绒绒布朗",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620351
}
},
[71285] = {
commodityId = 71285,
commodityName = "绒绒布朗",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620352
}
},
[71286] = {
commodityId = 71286,
commodityName = "萌爪探索袋",
beginTime = {
seconds = 1729526400
},
endTime = v4,
shopTag = v5,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.3.26.34",
itemIds = {
620353
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 60194
},
[71287] = {
commodityId = 71287,
commodityName = "萌爪探索袋",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620354
}
},
[71288] = {
commodityId = 71288,
commodityName = "萌爪探索袋",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620355
}
},
[71289] = {
commodityId = 71289,
commodityName = "守护圣翼",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
shopTag = v5,
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
620356
},
bOpenSuit = true,
suitId = 60195
},
[71290] = {
commodityId = 71290,
commodityName = "守护圣翼",
coinType = 200008,
price = 20,
beginTime = {
seconds = 1717689600
},
minVersion = "1.3.7.75",
itemIds = {
620357
}
},
[71291] = {
commodityId = 71291,
commodityName = "守护圣翼",
coinType = 200008,
price = 20,
beginTime = {
seconds = 1717689600
},
minVersion = "1.3.7.75",
itemIds = {
620358
}
},
[71292] = {
commodityId = 71292,
commodityName = "孔雀翎羽",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620359
},
suitId = 60196
},
[71293] = {
commodityId = 71293,
commodityName = "孔雀翎羽",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620360
}
},
[71294] = {
commodityId = 71294,
commodityName = "孔雀翎羽",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620361
}
},
[71295] = {
commodityId = 71295,
commodityName = "西瓜甜甜",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620362
},
suitId = 60197
},
[71296] = {
commodityId = 71296,
commodityName = "西瓜甜甜",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620363
}
},
[71297] = {
commodityId = 71297,
commodityName = "西瓜甜甜",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620364
}
},
[71298] = {
commodityId = 71298,
commodityName = "樱花香皂",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620365
},
suitId = 60198
},
[71299] = {
commodityId = 71299,
commodityName = "胡萝背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620366
},
suitId = 60199
},
[71300] = {
commodityId = 71300,
commodityName = "迷你水枪",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620367
},
suitId = 60200
},
[71301] = {
commodityId = 71301,
commodityName = "晶莹蝶翼",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = v5,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = "1.3.37.87",
itemIds = {
620368
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 500,
giftCoinType = 224,
giftPrice = 500,
bOpenSuit = true,
suitId = 60201
},
[71302] = {
commodityId = 71302,
commodityName = "晶莹蝶翼",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620369
}
},
[71303] = {
commodityId = 71303,
commodityName = "晶莹蝶翼",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620370
}
},
[71304] = {
commodityId = 71304,
commodityName = "纯果篮",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620371
},
suitId = 60202
},
[71305] = {
commodityId = 71305,
commodityName = "法师之剑",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1720713599
},
shopTag = v5,
jumpId = 25,
jumpText = "特惠礼包",
minVersion = "1.3.7.75",
itemIds = {
620375
},
bOpenSuit = true,
suitId = 60206
},
[71306] = {
commodityId = 71306,
commodityName = "糖豆包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620373
},
suitId = 60204
},
[71307] = {
commodityId = 71307,
commodityName = "兽人斧头",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620374
},
suitId = 60205
},
[71308] = {
commodityId = 71308,
commodityName = "甜酷包包",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v5,
jumpId = 1074,
jumpText = "三丽鸥家族",
minVersion = "1.3.7.97",
itemIds = {
620372
},
bOpenSuit = true,
suitId = 60203
},
[71309] = {
commodityId = 71309,
commodityName = "红蝶结包包",
beginTime = {
seconds = 1735747200
},
endTime = {
seconds = 1738425599
},
shopTag = v5,
jumpId = 1074,
jumpText = "三丽鸥家族",
minVersion = "1.3.7.97",
itemIds = {
620376
},
bOpenSuit = true,
suitId = 60207
},
[71310] = {
commodityId = 71310,
commodityName = "花花鸭篮",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v5,
jumpId = 1089,
jumpText = "夏日派对",
minVersion = "1.3.88.53",
itemIds = {
620377
},
bOpenSuit = true,
suitId = 60208
},
[71311] = {
commodityId = 71311,
commodityName = "长相思",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
shopTag = v5,
jumpId = 801,
jumpText = "甄嬛传祈愿",
itemIds = {
620378
},
bOpenSuit = true,
suitId = 60209
},
[71312] = {
commodityId = 71312,
commodityName = "长相思",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620379
}
},
[71313] = {
commodityId = 71313,
commodityName = "长相思",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620380
}
},
[71314] = {
commodityId = 71314,
commodityName = "圣旨到",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
shopTag = v5,
jumpId = 801,
jumpText = "甄嬛传祈愿",
itemIds = {
620381
},
bOpenSuit = true,
suitId = 60210
},
[71315] = {
commodityId = 71315,
commodityName = "圣旨到",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620382
}
},
[71316] = {
commodityId = 71316,
commodityName = "圣旨到",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620383
}
},
[71317] = {
commodityId = 71317,
commodityName = "摩天轮背包",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v5,
shopSort = 2,
jumpId = 706,
jumpText = v8,
minVersion = "1.3.12.1",
itemIds = {
620384
},
bOpenSuit = true,
suitId = 60211
},
[71318] = {
commodityId = 71318,
commodityName = "摩天轮背包",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620385
}
},
[71319] = {
commodityId = 71319,
commodityName = "摩天轮背包",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620386
}
},
[71320] = {
commodityId = 71320,
commodityName = "热气球",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v5,
shopSort = 2,
jumpId = 706,
jumpText = v8,
minVersion = "1.3.12.1",
itemIds = {
620387
},
bOpenSuit = true,
suitId = 60212
},
[71321] = {
commodityId = 71321,
commodityName = "热气球",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620388
}
},
[71322] = {
commodityId = 71322,
commodityName = "热气球",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620389
}
},
[71323] = {
commodityId = 71323,
commodityName = "小恐龙",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v5,
shopSort = 2,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = "1.3.12.1",
itemIds = {
620390
},
bOpenSuit = true,
suitId = 60213
},
[71324] = {
commodityId = 71324,
commodityName = "小恐龙",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620391
}
},
[71325] = {
commodityId = 71325,
commodityName = "小恐龙",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620392
}
},
[71326] = {
commodityId = 71326,
commodityName = "鱼竿",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.12.1",
itemIds = {
620393
},
suitId = 60214
},
[71327] = {
commodityId = 71327,
commodityName = "排球",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.12.1",
itemIds = {
620394
},
suitId = 60215
},
[71328] = {
commodityId = 71328,
commodityName = "清凉扇子",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.12.1",
itemIds = {
620395
},
suitId = 60216
},
[71329] = {
commodityId = 71329,
commodityName = "火炬",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.12.1",
itemIds = {
620396
},
suitId = 60217
},
[71330] = {
commodityId = 71330,
commodityName = "卡通电风扇",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.12.1",
itemIds = {
620397
},
suitId = 60218
},
[71331] = {
commodityId = 71331,
commodityName = "荔枝妞妞",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.12.1",
itemIds = {
620398
},
suitId = 60219
},
[71332] = {
commodityId = 71332,
commodityName = "荔枝妞妞",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620399
}
},
[71333] = {
commodityId = 71333,
commodityName = "荔枝妞妞",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.1",
itemIds = {
620400
}
},
[71334] = {
commodityId = 71334,
commodityName = "圣代杯",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.12.1",
itemIds = {
620401
},
suitId = 60220
},
[71335] = {
commodityId = 71335,
commodityName = "仙境灵翼",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730995199
},
shopTag = v5,
shopSort = 2,
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = v13,
itemIds = {
620402
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 7500,
bOpenSuit = true,
suitId = 60221
},
[71336] = {
commodityId = 71336,
commodityName = "仙境灵翼",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = v13,
itemIds = {
620403
}
},
[71337] = {
commodityId = 71337,
commodityName = "仙境灵翼",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = v13,
itemIds = {
620404
}
},
[71338] = {
commodityId = 71338,
commodityName = "积木城堡",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620405
},
suitId = 60222
},
[71339] = {
commodityId = 71339,
commodityName = "披萨甜心",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620406
},
suitId = 60223
},
[71340] = {
commodityId = 71340,
commodityName = "炸虾大侠",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620407
},
suitId = 60224
},
[71341] = {
commodityId = 71341,
commodityName = "小黄鸭背包",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1729785599
},
shopTag = v5,
jumpId = 366,
jumpText = "甜兔仙踪",
minVersion = "1.3.12.52",
itemIds = {
620408
},
bOpenSuit = true,
suitId = 60225
},
[71342] = {
commodityId = 71342,
commodityName = "滑稽背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620409
},
suitId = 60226
},
[71343] = {
commodityId = 71343,
commodityName = "豪华法棍",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620410
},
suitId = 60227
},
[71344] = {
commodityId = 71344,
commodityName = "豪华法棍",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620411
}
},
[71345] = {
commodityId = 71345,
commodityName = "豪华法棍",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620412
}
},
[71346] = {
commodityId = 71346,
commodityName = "盼盼吐司包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620413
},
suitId = 60228
},
[71347] = {
commodityId = 71347,
commodityName = "全息秘宝",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620414
},
suitId = 60229
},
[71348] = {
commodityId = 71348,
commodityName = "焰羽瑶琴",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 2,
jumpId = 809,
jumpText = "凤求凰祈愿",
minVersion = "1.3.12.90",
itemIds = {
620415
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 100,
bOpenSuit = true,
suitId = 60230
},
[71349] = {
commodityId = 71349,
commodityName = "焰羽瑶琴",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.12.90",
itemIds = {
620416
}
},
[71350] = {
commodityId = 71350,
commodityName = "焰羽瑶琴",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.12.90",
itemIds = {
620417
}
},
[71351] = {
commodityId = 71351,
commodityName = "卷不离身",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 2,
jumpId = 809,
jumpText = "凤求凰祈愿",
minVersion = "1.3.12.90",
itemIds = {
620418
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 60231
},
[71352] = {
commodityId = 71352,
commodityName = "卷不离身",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.90",
itemIds = {
620419
}
},
[71353] = {
commodityId = 71353,
commodityName = "卷不离身",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.12.90",
itemIds = {
620420
}
},
[71354] = {
commodityId = 71354,
commodityName = "赶考神器",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 2,
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = v13,
itemIds = {
620421
},
bOpenSuit = true,
suitId = 60232
},
[71355] = {
commodityId = 71355,
commodityName = "赶考神器",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v13,
itemIds = {
620422
}
},
[71356] = {
commodityId = 71356,
commodityName = "赶考神器",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = v13,
itemIds = {
620423
}
},
[71357] = {
commodityId = 71357,
commodityName = "巨星奖杯",
beginTime = {
seconds = 1744646400
},
endTime = {
seconds = 1746115199
},
shopTag = v5,
jumpId = 1069,
jumpText = "电竞少女",
minVersion = "1.3.78.90",
itemIds = {
620424
},
bOpenSuit = true,
suitId = 60233
},
[71358] = {
commodityId = 71358,
commodityName = "巨星奖杯",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620425
}
},
[71359] = {
commodityId = 71359,
commodityName = "巨星奖杯",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620426
}
},
[71360] = {
commodityId = 71360,
commodityName = "龙胆亮银枪",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v5,
shopSort = 2,
jumpId = 10674,
jumpText = v9,
minVersion = "1.3.18.56",
itemIds = {
620427
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60234
},
[71361] = {
commodityId = 71361,
commodityName = "逗趣之声",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620428
},
suitId = 60235
},
[71362] = {
commodityId = 71362,
commodityName = "花语使者",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v5,
shopSort = 1,
jumpId = 619,
jumpText = v8,
minVersion = "1.3.18.1",
itemIds = {
620429
},
bOpenSuit = true,
suitId = 60236
},
[71363] = {
commodityId = 71363,
commodityName = "花语使者",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620430
}
},
[71364] = {
commodityId = 71364,
commodityName = "花语使者",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620431
}
},
[71365] = {
commodityId = 71365,
commodityName = "清凉花",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620432
}
},
[71366] = {
commodityId = 71366,
commodityName = "清凉花",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620433
}
},
[71367] = {
commodityId = 71367,
commodityName = "幸运锤",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v5,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
minVersion = "1.3.37.68",
itemIds = {
620434
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 60237
},
[71368] = {
commodityId = 71368,
commodityName = "幸运锤",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620435
}
},
[71369] = {
commodityId = 71369,
commodityName = "幸运锤",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620436
}
},
[71370] = {
commodityId = 71370,
commodityName = "星月童话",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620437
},
suitId = 60238
},
[71371] = {
commodityId = 71371,
commodityName = "星月童话",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620438
}
},
[71372] = {
commodityId = 71372,
commodityName = "星月童话",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620439
}
},
[71373] = {
commodityId = 71373,
commodityName = "梦幻火箭",
beginTime = {
seconds = 1699113600
},
endTime = {
seconds = 1699718399
},
shopTag = v5,
minVersion = "1.3.26.61",
itemIds = {
620440
},
suitId = 60239
},
[71374] = {
commodityId = 71374,
commodityName = "梦幻火箭",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620441
}
},
[71375] = {
commodityId = 71375,
commodityName = "梦幻火箭",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620442
}
},
[71376] = {
commodityId = 71376,
commodityName = "繁花轻摇",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = v5,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
620443
},
bOpenSuit = true,
suitId = 60240
},
[71377] = {
commodityId = 71377,
commodityName = "繁花轻摇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620444
}
},
[71378] = {
commodityId = 71378,
commodityName = "繁花轻摇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620445
}
},
[71379] = {
commodityId = 71379,
commodityName = "甜乐杯",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620446
},
suitId = 60241
},
[71380] = {
commodityId = 71380,
commodityName = "上班族背包",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v5,
shopSort = 1,
jumpId = 614,
jumpText = "向日葵小班",
itemIds = {
620447
},
bOpenSuit = true,
suitId = 60242
},
[71381] = {
commodityId = 71381,
commodityName = "鳄鱼山先生背包",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopTag = v5,
shopSort = 1,
jumpId = 618,
jumpText = "百变小新",
itemIds = {
620448
},
bOpenSuit = true,
suitId = 60243
},
[71382] = {
commodityId = 71382,
commodityName = "环保小卫士",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620449
},
suitId = 60244
},
[71383] = {
commodityId = 71383,
commodityName = "炸鸡乐园",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620450
},
suitId = 60245
},
[71384] = {
commodityId = 71384,
commodityName = "金穗之愿",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620451
},
suitId = 60246
},
[71385] = {
commodityId = 71385,
commodityName = "相机奇趣包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620452
},
suitId = 60247
},
[71386] = {
commodityId = 71386,
commodityName = "星宝日历",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620453
},
suitId = 60248
},
[71387] = {
commodityId = 71387,
commodityName = "异界旅伴",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620454
},
suitId = 60249
},
[71388] = {
commodityId = 71388,
commodityName = "阿努比斯权杖",
beginTime = {
seconds = 1709740800
},
endTime = {
seconds = 1743695999
},
shopTag = v5,
jumpId = 1079,
jumpText = "沙海寻踪",
minVersion = "1.3.68.101",
itemIds = {
620455
},
bOpenSuit = true,
suitId = 60250
},
[71389] = {
commodityId = 71389,
commodityName = "阿努比斯权杖",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620456
}
},
[71390] = {
commodityId = 71390,
commodityName = "阿努比斯权杖",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620457
}
},
[71391] = {
commodityId = 71391,
commodityName = "清凉蓝叉",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620458
},
suitId = 60251
},
[71392] = {
commodityId = 71392,
commodityName = "灿烂黄叉",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620459
},
suitId = 60252
},
[71393] = {
commodityId = 71393,
commodityName = "生机绿叉",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620460
},
suitId = 60253
},
[71394] = {
commodityId = 71394,
commodityName = "小熊趴趴",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
shopTag = v5,
jumpId = 573,
jumpText = "梦幻告白",
minVersion = "1.3.26.23",
itemIds = {
620461
},
bOpenSuit = true,
suitId = 60254
},
[71395] = {
commodityId = 71395,
commodityName = "萌萌刺头",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v5,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.18.1",
itemIds = {
620462
},
bOpenSuit = true,
suitId = 60255
},
[71396] = {
commodityId = 71396,
commodityName = "萌萌刺头",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620463
}
},
[71397] = {
commodityId = 71397,
commodityName = "萌萌刺头",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620464
}
},
[71398] = {
commodityId = 71398,
commodityName = "仙境之音",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopTag = v5,
shopSort = 1,
jumpId = 619,
jumpText = v8,
minVersion = "1.3.18.1",
itemIds = {
620465
},
bOpenSuit = true,
suitId = 60256
},
[71399] = {
commodityId = 71399,
commodityName = "仙境之音",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620466
}
},
[71400] = {
commodityId = 71400,
commodityName = "仙境之音",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620467
}
},
[71401] = {
commodityId = 71401,
commodityName = "大白兔奶糖",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620468
},
suitId = 60257
},
[71402] = {
commodityId = 71402,
commodityName = "琼浆仙葫",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620469
},
suitId = 60258
},
[71403] = {
commodityId = 71403,
commodityName = "霜雪明",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620470
},
suitId = 60259
},
[71404] = {
commodityId = 71404,
commodityName = "月球背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620471
},
suitId = 60260
},
[71405] = {
commodityId = 71405,
commodityName = "紫金宝器",
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1727020799
},
shopTag = v5,
shopSort = 1,
jumpId = 8001,
jumpText = "西行之路祈愿",
minVersion = "1.3.18.23",
itemIds = {
620472
},
bOpenSuit = true,
suitId = 60261
},
[71406] = {
commodityId = 71406,
commodityName = "紫金宝器",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.18.23",
itemIds = {
620473
}
},
[71407] = {
commodityId = 71407,
commodityName = "紫金宝器",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.18.23",
itemIds = {
620474
}
},
[71408] = {
commodityId = 71408,
commodityName = "时光花语",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v5,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
minVersion = "1.3.18.24",
itemIds = {
620475
},
canGift = true,
addIntimacy = 80,
giftCoinType = 203,
giftPrice = 40,
bOpenSuit = true,
suitId = 60262
},
[71409] = {
commodityId = 71409,
commodityName = "时光花语",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620476
}
},
[71410] = {
commodityId = 71410,
commodityName = "时光花语",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620477
}
},
[71411] = {
commodityId = 71411,
commodityName = "山河扇卷",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
shopTag = v5,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.37",
itemIds = {
620478
},
bOpenSuit = true,
suitId = 60263
},
[71412] = {
commodityId = 71412,
commodityName = "山河扇卷",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620479
}
},
[71413] = {
commodityId = 71413,
commodityName = "山河扇卷",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620480
}
},
[71414] = {
commodityId = 71414,
commodityName = "兔倚秋夕",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1732809599
},
shopTag = v5,
shopSort = 1,
jumpId = 363,
jumpText = "桂月清平",
minVersion = "1.3.18.24",
itemIds = {
620481
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 60264
},
[71415] = {
commodityId = 71415,
commodityName = "兔倚秋夕",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620482
}
},
[71416] = {
commodityId = 71416,
commodityName = "兔倚秋夕",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620483
}
},
[71417] = {
commodityId = 71417,
commodityName = "霜叶红",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620484
},
suitId = 60265
},
[71418] = {
commodityId = 71418,
commodityName = "绘长歌",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v5,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.72",
itemIds = {
620485
},
bOpenSuit = true,
suitId = 60266
},
[71419] = {
commodityId = 71419,
commodityName = "绘长歌",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620486
}
},
[71420] = {
commodityId = 71420,
commodityName = "绘长歌",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620487
}
},
[71421] = {
commodityId = 71421,
commodityName = "光耀圣盾",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620488
},
suitId = 60267
},
[71422] = {
commodityId = 71422,
commodityName = "甜豆泳圈",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v5,
jumpId = 10601,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
620489
},
bOpenSuit = true,
suitId = 60268
},
[71423] = {
commodityId = 71423,
commodityName = "萌玩奇趣",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620490
},
suitId = 60269
},
[71424] = {
commodityId = 71424,
commodityName = "萌玩奇趣",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620491
}
},
[71425] = {
commodityId = 71425,
commodityName = "萌玩奇趣",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620492
}
},
[71426] = {
commodityId = 71426,
commodityName = "冲锋鲨鲨",
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1735919999
},
shopTag = v5,
jumpId = 368,
jumpText = "丰收兔",
minVersion = "1.3.18.54",
itemIds = {
620493
},
bOpenSuit = true,
suitId = 60270
},
[71427] = {
commodityId = 71427,
commodityName = "琳琅千机",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v5,
shopSort = 1,
jumpId = 10674,
jumpText = v9,
minVersion = "1.3.18.56",
itemIds = {
620494
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60271
},
[71428] = {
commodityId = 71428,
commodityName = "心动热麦",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopTag = v5,
shopSort = 1,
jumpId = 10674,
jumpText = v9,
minVersion = "1.3.18.56",
itemIds = {
620495
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60272
},
[71429] = {
commodityId = 71429,
commodityName = "冲锋鲨鲨",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620496
}
},
[71430] = {
commodityId = 71430,
commodityName = "冲锋鲨鲨",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620497
}
},
[71431] = {
commodityId = 71431,
commodityName = "吾皇福袋",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v5,
jumpId = 1092,
jumpText = "吾皇猫",
minVersion = "1.3.88.53",
itemIds = {
620498
},
bOpenSuit = true,
suitId = 60273
},
[71432] = {
commodityId = 71432,
commodityName = "巴扎黑福袋",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
shopTag = v5,
jumpId = 1093,
jumpText = "巴扎黑",
minVersion = "1.3.88.53",
itemIds = {
620499
},
bOpenSuit = true,
suitId = 60274
},
[71433] = {
commodityId = 71433,
commodityName = "花影之刃",
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v5,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.72",
itemIds = {
620500
},
bOpenSuit = true,
suitId = 60275
},
[71434] = {
commodityId = 71434,
commodityName = "花影之刃",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620501
}
},
[71435] = {
commodityId = 71435,
commodityName = "花影之刃",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620502
}
},
[71436] = {
commodityId = 71436,
commodityName = "乾坤丹炉",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
minVersion = "1.3.18.73",
itemIds = {
620503
},
suitId = 60276
},
[71437] = {
commodityId = 71437,
commodityName = "乾坤丹炉",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620504
}
},
[71438] = {
commodityId = 71438,
commodityName = "乾坤丹炉",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620505
}
},
[71439] = {
commodityId = 71439,
commodityName = "时光齿轮",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620506
},
suitId = 60277
},
[71440] = {
commodityId = 71440,
commodityName = "时光齿轮",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620507
}
},
[71441] = {
commodityId = 71441,
commodityName = "时光齿轮",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620508
}
},
[71442] = {
commodityId = 71442,
commodityName = "柯基短尾包",
beginTime = {
seconds = 1730217600
},
endTime = {
seconds = 1731427199
},
shopTag = v5,
jumpId = 380,
jumpText = "星辰日晷",
minVersion = "1.3.26.29",
itemIds = {
620509
},
bOpenSuit = true,
suitId = 60278
},
[71443] = {
commodityId = 71443,
commodityName = "柯基短尾包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620510
}
},
[71444] = {
commodityId = 71444,
commodityName = "柯基短尾包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620511
}
},
[71445] = {
commodityId = 71445,
commodityName = "五星好锤",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620512
},
suitId = 60279
},
[71446] = {
commodityId = 71446,
commodityName = "全息灵庙",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620513
},
suitId = 60280
},
[71447] = {
commodityId = 71447,
commodityName = "热血校园",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620514
},
suitId = 60281
},
[71448] = {
commodityId = 71448,
commodityName = "禁止挂机",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620515
},
suitId = 60282
},
[71449] = {
commodityId = 71449,
commodityName = "半神之弓",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620516
},
suitId = 60283
},
[71450] = {
commodityId = 71450,
commodityName = "星辰孵化器",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v5,
shopSort = 1,
jumpId = 623,
jumpText = v8,
minVersion = "1.3.26.1",
itemIds = {
620517
},
bOpenSuit = true,
suitId = 60284
},
[71451] = {
commodityId = 71451,
commodityName = "星辰孵化器",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620518
}
},
[71452] = {
commodityId = 71452,
commodityName = "星辰孵化器",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620519
}
},
[71453] = {
commodityId = 71453,
commodityName = "移动信标",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v5,
shopSort = 1,
jumpId = 623,
jumpText = v8,
minVersion = "1.3.26.1",
itemIds = {
620520
},
bOpenSuit = true,
suitId = 60285
},
[71454] = {
commodityId = 71454,
commodityName = "移动信标",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620521
}
},
[71455] = {
commodityId = 71455,
commodityName = "移动信标",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620522
}
},
[71456] = {
commodityId = 71456,
commodityName = "银河小熊",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v5,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.26.1",
itemIds = {
620523
},
bOpenSuit = true,
suitId = 60286
},
[71457] = {
commodityId = 71457,
commodityName = "银河小熊",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620524
}
},
[71458] = {
commodityId = 71458,
commodityName = "银河小熊",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620525
}
},
[71459] = {
commodityId = 71459,
commodityName = "香氛宝瓶",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620526
},
suitId = 60287
},
[71460] = {
commodityId = 71460,
commodityName = "香氛宝瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620527
}
},
[71461] = {
commodityId = 71461,
commodityName = "香氛宝瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620528
}
},
[71462] = {
commodityId = 71462,
commodityName = "天际之镜",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v5,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.26.1",
itemIds = {
620529
},
bOpenSuit = true,
suitId = 60288
},
[71463] = {
commodityId = 71463,
commodityName = "挚爱之锚",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
minVersion = v13,
itemIds = {
620279
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60156
},
[71464] = {
commodityId = 71464,
commodityName = "心动和弦",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
minVersion = v13,
itemIds = {
620280
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60157
},
[71465] = {
commodityId = 71465,
commodityName = "影龙之锋",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
minVersion = v13,
itemIds = {
620281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60158
},
[71466] = {
commodityId = 71466,
commodityName = "龙胆亮银枪",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
minVersion = v13,
itemIds = {
620427
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60234
},
[71467] = {
commodityId = 71467,
commodityName = "异界旅伴",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
minVersion = v13,
itemIds = {
620454
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60249
},
[71468] = {
commodityId = 71468,
commodityName = "琳琅千机",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
minVersion = v13,
itemIds = {
620494
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60271
},
[71469] = {
commodityId = 71469,
commodityName = "心动热麦",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopTag = v5,
shopSort = 1,
jumpId = 10677,
jumpText = v9,
minVersion = v13,
itemIds = {
620495
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60272
},
[71470] = {
commodityId = 71470,
commodityName = "厨王之铲",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620530
},
suitId = 60289
},
[71471] = {
commodityId = 71471,
commodityName = "一击必中",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620531
},
suitId = 60290
},
[71472] = {
commodityId = 71472,
commodityName = "百步穿杨",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620532
},
suitId = 60291
},
[71473] = {
commodityId = 71473,
commodityName = "活力满分",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620533
},
suitId = 60292
},
[71474] = {
commodityId = 71474,
commodityName = "香脆油条",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620534
},
suitId = 60293
},
[71475] = {
commodityId = 71475,
commodityName = "小暖狐",
beginTime = {
seconds = 1749225600
},
endTime = {
seconds = 1751558399
},
shopTag = v5,
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = "1.3.88.112",
itemIds = {
620535
}
},
[71476] = {
commodityId = 71476,
commodityName = "棋王小帅",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732723200
},
shopTag = v5,
jumpId = 33,
jumpText = "闯关挑战",
itemIds = {
620536
},
bOpenSuit = true,
suitId = 60295
},
[71477] = {
commodityId = 71477,
commodityName = "小蜗行囊",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620537
},
suitId = 60296
},
[71478] = {
commodityId = 71478,
commodityName = "小蜗行囊",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620538
}
},
[71479] = {
commodityId = 71479,
commodityName = "小蜗行囊",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620539
}
},
[71480] = {
commodityId = 71480,
commodityName = "星河罗盘",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopTag = v5,
shopSort = 1,
jumpId = 623,
jumpText = v8,
minVersion = "1.3.26.1",
itemIds = {
620540
},
bOpenSuit = true,
suitId = 60297
},
[71481] = {
commodityId = 71481,
commodityName = "星河罗盘",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620541
}
},
[71482] = {
commodityId = 71482,
commodityName = "星河罗盘",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620542
}
},
[71483] = {
commodityId = 71483,
commodityName = "海底奇遇包",
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750348799
},
shopTag = v5,
jumpId = 400,
jumpText = "招财喵",
minVersion = "1.3.88.98",
itemIds = {
620543
},
bOpenSuit = true,
suitId = 60298
},
[71484] = {
commodityId = 71484,
commodityName = "海底奇遇包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620544
}
},
[71485] = {
commodityId = 71485,
commodityName = "海底奇遇包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620545
}
},
[71486] = {
commodityId = 71486,
commodityName = "小鹿篮",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620546
},
suitId = 60299
},
[71487] = {
commodityId = 71487,
commodityName = "小鹿篮",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620547
}
},
[71488] = {
commodityId = 71488,
commodityName = "小鹿篮",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620548
}
},
[71489] = {
commodityId = 71489,
commodityName = "妲己背饰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620549
},
suitId = 60300
},
[71490] = {
commodityId = 71490,
commodityName = "星愿号角",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v5,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
620550
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 60301
},
[71491] = {
commodityId = 71491,
commodityName = "星愿号角",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.26.71",
itemIds = {
620551
}
},
[71492] = {
commodityId = 71492,
commodityName = "星愿号角",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.26.71",
itemIds = {
620552
}
},
[71493] = {
commodityId = 71493,
commodityName = "南瓜糖盒",
beginTime = {
seconds = 1729872000
},
endTime = v4,
shopTag = v5,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.3.26.34",
itemIds = {
620553
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 60302
},
[71494] = {
commodityId = 71494,
commodityName = "南瓜糖盒",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1729872000
},
itemIds = {
620554
}
},
[71495] = {
commodityId = 71495,
commodityName = "南瓜糖盒",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1729872000
},
itemIds = {
620555
}
},
[71496] = {
commodityId = 71496,
commodityName = "嘟嘟河马",
beginTime = {
seconds = 1730304000
},
endTime = {
seconds = 1732204799
},
shopTag = v5,
jumpId = 379,
jumpText = "仙狐花隐",
minVersion = "1.3.26.54",
itemIds = {
620556
},
bOpenSuit = true,
suitId = 60303
},
[71497] = {
commodityId = 71497,
commodityName = "河马背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620557
}
},
[71498] = {
commodityId = 71498,
commodityName = "河马背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620558
}
},
[71499] = {
commodityId = 71499,
commodityName = "开心超人迷你背包",
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1732204799
},
shopTag = v5,
shopSort = 1,
jumpId = 381,
jumpText = "开心超人联盟",
minVersion = "1.3.26.61",
itemIds = {
620559
},
bOpenSuit = true,
suitId = 60304
},
[71500] = {
commodityId = 71500,
commodityName = "甜心超人天使之翼",
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1732204799
},
shopTag = v5,
shopSort = 1,
jumpId = 382,
jumpText = "开心超人联盟",
minVersion = "1.3.26.61",
itemIds = {
620560
},
bOpenSuit = true,
suitId = 60305
},
[71501] = {
commodityId = 71501,
commodityName = "小心超人的刀",
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1732204799
},
shopTag = v5,
shopSort = 1,
jumpId = 383,
jumpText = "开心超人联盟",
minVersion = "1.3.26.61",
itemIds = {
620561
},
bOpenSuit = true,
suitId = 60306
},
[71502] = {
commodityId = 71502,
commodityName = "全息灵庙",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620513
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60280
},
[71503] = {
commodityId = 71503,
commodityName = "脉冲饰带",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620563
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60308
},
[71504] = {
commodityId = 71504,
commodityName = "挚爱之锚",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620279
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60156
},
[71505] = {
commodityId = 71505,
commodityName = "心动和弦",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620280
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60157
},
[71506] = {
commodityId = 71506,
commodityName = "影龙之锋",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60158
},
[71507] = {
commodityId = 71507,
commodityName = "龙胆亮银枪",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620427
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60234
},
[71508] = {
commodityId = 71508,
commodityName = "异界旅伴",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620454
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60249
},
[71509] = {
commodityId = 71509,
commodityName = "琳琅千机",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620494
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60271
},
[71510] = {
commodityId = 71510,
commodityName = "心动热麦",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopTag = v5,
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.26.38",
itemIds = {
620495
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60272
},
[71511] = {
commodityId = 71511,
commodityName = "星曜秘卷",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620562
},
suitId = 60307
},
[71512] = {
commodityId = 71512,
commodityName = "脉冲饰带",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620563
},
suitId = 60308
},
[71513] = {
commodityId = 71513,
commodityName = "蓝钥幽梦",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620564
},
suitId = 60309
},
[71514] = {
commodityId = 71514,
commodityName = "蓝钥幽梦",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620565
}
},
[71515] = {
commodityId = 71515,
commodityName = "蓝钥幽梦",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620566
}
},
[71516] = {
commodityId = 71516,
commodityName = "红绸公主包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620567
},
suitId = 60310
},
[71517] = {
commodityId = 71517,
commodityName = "红绸公主包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620568
}
},
[71518] = {
commodityId = 71518,
commodityName = "红绸公主包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620569
}
},
[71519] = {
commodityId = 71519,
commodityName = "双羽焰舞",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v5,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
620570
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 60311
},
[71520] = {
commodityId = 71520,
commodityName = "双羽焰舞",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.26.71",
itemIds = {
620571
}
},
[71521] = {
commodityId = 71521,
commodityName = "双羽焰舞",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.26.71",
itemIds = {
620572
}
},
[71522] = {
commodityId = 71522,
commodityName = "糖果背包",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v5,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
itemIds = {
620573
},
bOpenSuit = true,
suitId = 60312
},
[71523] = {
commodityId = 71523,
commodityName = "糖果背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620574
}
},
[71524] = {
commodityId = 71524,
commodityName = "糖果背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620575
}
},
[71525] = {
commodityId = 71525,
commodityName = "萌虎趴趴",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620576
},
suitId = 60313
},
[71526] = {
commodityId = 71526,
commodityName = "萌虎趴趴",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620577
}
},
[71527] = {
commodityId = 71527,
commodityName = "萌虎趴趴",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620578
}
},
[71528] = {
commodityId = 71528,
commodityName = "林间猎影",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620579
},
suitId = 60314
},
[71529] = {
commodityId = 71529,
commodityName = "林间猎影",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620580
}
},
[71530] = {
commodityId = 71530,
commodityName = "林间猎影",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620581
}
},
[71531] = {
commodityId = 71531,
commodityName = "小狗曲奇",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739980799
},
shopTag = v5,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.68.33",
itemIds = {
620582
},
bOpenSuit = true,
suitId = 60315
},
[71532] = {
commodityId = 71532,
commodityName = "小狗曲奇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620583
}
},
[71533] = {
commodityId = 71533,
commodityName = "小狗曲奇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620584
}
},
[71534] = {
commodityId = 71534,
commodityName = "心碎小丑",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620585
},
suitId = 60316
},
[71535] = {
commodityId = 71535,
commodityName = "心碎小丑",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620586
}
},
[71536] = {
commodityId = 71536,
commodityName = "心碎小丑",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620587
}
},
[71537] = {
commodityId = 71537,
commodityName = "梦幻花羽",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
shopTag = v5,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
minVersion = "1.3.26.93",
itemIds = {
620588
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 7500,
bOpenSuit = true,
suitId = 60317
},
[71538] = {
commodityId = 71538,
commodityName = "梦幻花羽",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620589
}
},
[71539] = {
commodityId = 71539,
commodityName = "梦幻花羽",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620590
}
},
[71540] = {
commodityId = 71540,
commodityName = "回旋飞盘",
beginTime = {
seconds = 1740153600
},
endTime = {
seconds = 1743091199
},
shopTag = v5,
jumpId = 510,
jumpText = "海狮公主",
itemIds = {
620591
},
suitId = 60318
},
[71541] = {
commodityId = 71541,
commodityName = "回旋飞盘",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620592
}
},
[71542] = {
commodityId = 71542,
commodityName = "回旋飞盘",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620593
}
},
[71543] = {
commodityId = 71543,
commodityName = "毛绒老鼠",
beginTime = {
seconds = 1731081600
},
endTime = {
seconds = 1732291199
},
shopTag = v5,
jumpId = 1065,
jumpText = "超值礼包",
minVersion = "1.3.26.81",
itemIds = {
620594
},
bOpenSuit = true,
suitId = 60319
},
[71544] = {
commodityId = 71544,
commodityName = "毛绒老鼠",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620595
}
},
[71545] = {
commodityId = 71545,
commodityName = "毛绒老鼠",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620596
}
},
[71546] = {
commodityId = 71546,
commodityName = "啃啃乐",
beginTime = {
seconds = 1739548800
},
endTime = {
seconds = 1741017599
},
shopTag = v5,
jumpId = 582,
jumpText = "丰收派对",
minVersion = "1.3.26.81",
itemIds = {
620597
},
bOpenSuit = true,
suitId = 60320
},
[71547] = {
commodityId = 71547,
commodityName = "啃啃乐",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620598
}
},
[71548] = {
commodityId = 71548,
commodityName = "啃啃乐",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620599
}
},
[71549] = {
commodityId = 71549,
commodityName = "鱿你同行",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620600
},
suitId = 60321
},
[71550] = {
commodityId = 71550,
commodityName = "鱿你同行",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620601
}
},
[71551] = {
commodityId = 71551,
commodityName = "鱿你同行",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620602
}
},
[71552] = {
commodityId = 71552,
commodityName = "瓜子背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620603
},
suitId = 60322
},
[71553] = {
commodityId = 71553,
commodityName = "香菇串",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620604
},
suitId = 60323
},
[71554] = {
commodityId = 71554,
commodityName = "鱿鱼娃娃",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v5,
shopSort = 1,
jumpId = 630,
jumpText = v8,
minVersion = "1.3.37.1",
itemIds = {
620605
},
bOpenSuit = true,
suitId = 60324
},
[71555] = {
commodityId = 71555,
commodityName = "鱿鱼娃娃",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620606
}
},
[71556] = {
commodityId = 71556,
commodityName = "鱿鱼娃娃",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620607
}
},
[71557] = {
commodityId = 71557,
commodityName = "巧克力娃娃",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v5,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.37.1",
itemIds = {
620608
},
bOpenSuit = true,
suitId = 60325
},
[71558] = {
commodityId = 71558,
commodityName = "巧克力娃娃",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620609
}
},
[71559] = {
commodityId = 71559,
commodityName = "巧克力娃娃",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620610
}
},
[71560] = {
commodityId = 71560,
commodityName = "大闸蟹背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620611
},
suitId = 60326
},
[71561] = {
commodityId = 71561,
commodityName = "大闸蟹背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620612
}
},
[71562] = {
commodityId = 71562,
commodityName = "大闸蟹背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620613
}
},
[71563] = {
commodityId = 71563,
commodityName = "象棋“将”",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620614
},
suitId = 60327
},
[71564] = {
commodityId = 71564,
commodityName = "烧烤货摊",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v5,
shopSort = 1,
jumpId = 630,
jumpText = v8,
minVersion = "1.3.37.1",
itemIds = {
620615
},
bOpenSuit = true,
suitId = 60328
},
[71565] = {
commodityId = 71565,
commodityName = "烧烤货摊",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620616
}
},
[71566] = {
commodityId = 71566,
commodityName = "烧烤货摊",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620617
}
},
[71567] = {
commodityId = 71567,
commodityName = "龟龟探险家",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
shopTag = v5,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
itemIds = {
620618
},
bOpenSuit = true,
suitId = 60329
},
[71568] = {
commodityId = 71568,
commodityName = "龟龟探险家",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620619
}
},
[71569] = {
commodityId = 71569,
commodityName = "龟龟探险家",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620620
}
},
[71570] = {
commodityId = 71570,
commodityName = "甜美爆弹",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v5,
jumpId = 1098,
jumpText = "天线宝宝",
minVersion = "1.3.88.155",
itemIds = {
620621
},
bOpenSuit = true,
suitId = 60330
},
[71571] = {
commodityId = 71571,
commodityName = "软绵绵旋风",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v5,
jumpId = 1098,
jumpText = "天线宝宝",
minVersion = "1.3.88.155",
itemIds = {
620622
},
bOpenSuit = true,
suitId = 60331
},
[71572] = {
commodityId = 71572,
commodityName = "诺诺背包",
beginTime = {
seconds = 1750435200
},
endTime = {
seconds = 1752249599
},
shopTag = v5,
jumpId = 1098,
jumpText = "天线宝宝",
minVersion = "1.3.88.155",
itemIds = {
620623
},
bOpenSuit = true,
suitId = 60332
},
[71573] = {
commodityId = 71573,
commodityName = "极光剑",
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
shopTag = v5,
jumpId = 8012,
jumpText = "星缘奇境",
minVersion = "1.3.26.93",
itemIds = {
620624
},
bOpenSuit = true,
suitId = 60333
},
[71574] = {
commodityId = 71574,
commodityName = "美味寿司",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620625
},
suitId = 60334
},
[71575] = {
commodityId = 71575,
commodityName = "美味寿司",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620626
}
},
[71576] = {
commodityId = 71576,
commodityName = "美味寿司",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620627
}
},
[71577] = {
commodityId = 71577,
commodityName = "除草手册",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620628
},
suitId = 60335
},
[71578] = {
commodityId = 71578,
commodityName = "除草手册",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620629
}
},
[71579] = {
commodityId = 71579,
commodityName = "除草手册",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620630
}
},
[71580] = {
commodityId = 71580,
commodityName = "学霸挎包",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
shopTag = v5,
jumpId = 10699,
jumpText = "扬帆起航",
itemIds = {
620631
},
bOpenSuit = true,
suitId = 60336
},
[71581] = {
commodityId = 71581,
commodityName = "大白鲨",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
shopTag = v5,
jumpId = 10700,
jumpText = "扬帆起航",
itemIds = {
620632
},
bOpenSuit = true,
suitId = 60337
},
[71582] = {
commodityId = 71582,
commodityName = "背负菜花",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620633
},
suitId = 60338
},
[71583] = {
commodityId = 71583,
commodityName = "背负菜花",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620634
}
},
[71584] = {
commodityId = 71584,
commodityName = "背负菜花",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620635
}
},
[71585] = {
commodityId = 71585,
commodityName = "精萤之翼",
beginTime = {
seconds = 1732680000
},
endTime = {
seconds = 1741881599
},
shopTag = v5,
jumpId = 457,
jumpText = "猫猫搜城记",
minVersion = "1.3.37.1",
itemIds = {
620636
},
bOpenSuit = true,
suitId = 60339
},
[71586] = {
commodityId = 71586,
commodityName = "海洋精灵",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v5,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
minVersion = "1.3.37.68",
itemIds = {
620637
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 80,
bOpenSuit = true,
suitId = 60340
},
[71587] = {
commodityId = 71587,
commodityName = "海洋精灵",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620638
}
},
[71588] = {
commodityId = 71588,
commodityName = "海洋精灵",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620639
}
},
[71589] = {
commodityId = 71589,
commodityName = "鱼你有约",
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
shopTag = v5,
jumpId = 502,
jumpText = "冰雪赐福",
minVersion = "1.3.68.33",
itemIds = {
620640
},
bOpenSuit = true,
suitId = 60341
},
[71590] = {
commodityId = 71590,
commodityName = "鱼你有约",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620641
}
},
[71591] = {
commodityId = 71591,
commodityName = "鱼你有约",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620642
}
},
[71592] = {
commodityId = 71592,
commodityName = "泡泡海豚",
beginTime = {
seconds = 1740153600
},
endTime = {
seconds = 1743091199
},
shopTag = v5,
jumpId = 510,
jumpText = "海狮公主",
minVersion = "1.3.37.14",
itemIds = {
620643
},
bOpenSuit = true,
suitId = 60342
},
[71593] = {
commodityId = 71593,
commodityName = "泡泡海豚",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620644
}
},
[71594] = {
commodityId = 71594,
commodityName = "泡泡海豚",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620645
}
},
[71595] = {
commodityId = 71595,
commodityName = "太阳能滑板",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v5,
jumpId = 1068,
jumpText = "柯南联动时装",
minVersion = "1.3.37.37",
itemIds = {
620646
},
bOpenSuit = true,
suitId = 60343
},
[71596] = {
commodityId = 71596,
commodityName = "小兰应援背挂",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v5,
jumpId = 1068,
jumpText = "柯南联动时装",
minVersion = "1.3.37.37",
itemIds = {
620647
},
bOpenSuit = true,
suitId = 60344
},
[71597] = {
commodityId = 71597,
commodityName = "空手道包",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v5,
jumpId = 1068,
jumpText = "柯南联动时装",
minVersion = "1.3.37.37",
itemIds = {
620648
},
bOpenSuit = true,
suitId = 60345
},
[71598] = {
commodityId = 71598,
commodityName = "APTX-4869",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v5,
jumpId = 1068,
jumpText = "柯南联动时装",
minVersion = "1.3.37.37",
itemIds = {
620649
},
bOpenSuit = true,
suitId = 60346
},
[71599] = {
commodityId = 71599,
commodityName = "侦探团徽章",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620650
},
suitId = 60347
},
[71600] = {
commodityId = 71600,
commodityName = "甜心按键机",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739894400
},
shopTag = v5,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.68.33",
itemIds = {
620651
},
bOpenSuit = true,
suitId = 60348
},
[71601] = {
commodityId = 71601,
commodityName = "甜心按键机",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620652
}
},
[71602] = {
commodityId = 71602,
commodityName = "甜心按键机",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620653
}
},
[71603] = {
commodityId = 71603,
commodityName = "智慧猫头鹰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620654
},
suitId = 60349
},
[71604] = {
commodityId = 71604,
commodityName = "智慧猫头鹰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620655
}
},
[71605] = {
commodityId = 71605,
commodityName = "智慧猫头鹰",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620656
}
},
[71606] = {
commodityId = 71606,
commodityName = "绛天战刃",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620278
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60155
},
[71607] = {
commodityId = 71607,
commodityName = "全息灵庙",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620513
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60280
},
[71608] = {
commodityId = 71608,
commodityName = "脉冲饰带",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620563
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60308
},
[71609] = {
commodityId = 71609,
commodityName = "挚爱之锚",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620279
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60156
},
[71610] = {
commodityId = 71610,
commodityName = "心动和弦",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620280
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60157
},
[71611] = {
commodityId = 71611,
commodityName = "影龙之锋",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60158
},
[71612] = {
commodityId = 71612,
commodityName = "龙胆亮银枪",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620427
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60234
},
[71613] = {
commodityId = 71613,
commodityName = "异界旅伴",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620454
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60249
},
[71614] = {
commodityId = 71614,
commodityName = "琳琅千机",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620494
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60271
},
[71615] = {
commodityId = 71615,
commodityName = "心动热麦",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1736697599
},
shopTag = v5,
shopSort = 1,
jumpId = 10701,
jumpText = v9,
minVersion = v13,
itemIds = {
620495
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60272
},
[71616] = {
commodityId = 71616,
commodityName = "三彩小马",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v5,
shopSort = 1,
jumpId = 636,
jumpText = v8,
minVersion = "1.3.68.1",
itemIds = {
620657
},
bOpenSuit = true,
suitId = 60350
},
[71617] = {
commodityId = 71617,
commodityName = "三彩小马",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620658
}
},
[71618] = {
commodityId = 71618,
commodityName = "三彩小马",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620659
}
},
[71619] = {
commodityId = 71619,
commodityName = "肉夹馍",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v5,
shopSort = 1,
jumpId = 636,
jumpText = v8,
minVersion = "1.3.68.1",
itemIds = {
620660
},
bOpenSuit = true,
suitId = 60351
},
[71620] = {
commodityId = 71620,
commodityName = "肉夹馍",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620661
}
},
[71621] = {
commodityId = 71621,
commodityName = "肉夹馍",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620662
}
},
[71622] = {
commodityId = 71622,
commodityName = "玲珑绣球",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620663
},
suitId = 60352
},
[71623] = {
commodityId = 71623,
commodityName = "玲珑绣球",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620664
}
},
[71624] = {
commodityId = 71624,
commodityName = "玲珑绣球",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620665
}
},
[71625] = {
commodityId = 71625,
commodityName = "如意发财树",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620666
},
suitId = 60353
},
[71626] = {
commodityId = 71626,
commodityName = "如意发财树",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620667
}
},
[71627] = {
commodityId = 71627,
commodityName = "如意发财树",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620668
}
},
[71628] = {
commodityId = 71628,
commodityName = "麻将幺鸡",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620669
},
suitId = 60354
},
[71629] = {
commodityId = 71629,
commodityName = "姜饼背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620670
},
suitId = 60355
},
[71630] = {
commodityId = 71630,
commodityName = "圣诞惊喜",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620671
},
suitId = 60356
},
[71631] = {
commodityId = 71631,
commodityName = "光暗之翼",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v5,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.52",
itemIds = {
620672
},
canGift = true,
addIntimacy = 1500,
giftCoinType = 213,
giftPrice = 5,
bOpenSuit = true,
suitId = 60357
},
[71632] = {
commodityId = 71632,
commodityName = "光暗之翼",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.68.52",
itemIds = {
620673
}
},
[71633] = {
commodityId = 71633,
commodityName = "光暗之翼",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.68.52",
itemIds = {
620674
}
},
[71634] = {
commodityId = 71634,
commodityName = "戴珍珠耳环的二哈",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620675
},
suitId = 60358
},
[71635] = {
commodityId = 71635,
commodityName = "戴珍珠耳环的二哈",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620676
}
},
[71636] = {
commodityId = 71636,
commodityName = "戴珍珠耳环的二哈",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620677
}
},
[71637] = {
commodityId = 71637,
commodityName = "浣熊奇遇记",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v5,
jumpId = 538,
jumpText = "仙福盈门",
minVersion = "1.3.37.68",
itemIds = {
620678
},
suitId = 60359
},
[71638] = {
commodityId = 71638,
commodityName = "星光宠物",
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740671999
},
shopTag = v5,
jumpId = 546,
jumpText = "福运琳琅",
minVersion = "1.3.37.68",
itemIds = {
620679
},
suitId = 60360
},
[71639] = {
commodityId = 71639,
commodityName = "星光宠物",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620680
}
},
[71640] = {
commodityId = 71640,
commodityName = "星光宠物",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620681
}
},
[71641] = {
commodityId = 71641,
commodityName = "喵来啦",
beginTime = {
seconds = 1749225600
},
endTime = {
seconds = 1751558399
},
shopTag = v5,
jumpId = 509,
jumpText = "雪境欢颂",
minVersion = "1.3.88.112",
itemIds = {
620682
}
},
[71642] = {
commodityId = 71642,
commodityName = "喵来啦",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.37.68",
itemIds = {
620683
}
},
[71643] = {
commodityId = 71643,
commodityName = "喵来啦",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.37.68",
itemIds = {
620684
}
},
[71644] = {
commodityId = 71644,
commodityName = "喜刷刷",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620685
},
suitId = 60362
},
[71645] = {
commodityId = 71645,
commodityName = "方天画戟",
beginTime = {
seconds = 1735920000
},
endTime = {
seconds = 1739462399
},
shopTag = v5,
jumpId = 10706,
jumpText = "峡谷战神",
itemIds = {
620686
},
bOpenSuit = true,
suitId = 60363
},
[71646] = {
commodityId = 71646,
commodityName = "猎龙双刃",
beginTime = {
seconds = 1735920000
},
endTime = {
seconds = 1739462399
},
shopTag = v5,
jumpId = 10707,
jumpText = "峡谷战神",
itemIds = {
620687
},
bOpenSuit = true,
suitId = 60364
},
[71647] = {
commodityId = 71647,
commodityName = "万物权衡",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v5,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.52",
itemIds = {
620688
},
bOpenSuit = true,
suitId = 60365
},
[71648] = {
commodityId = 71648,
commodityName = "万物权衡",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.68.52",
itemIds = {
620689
}
},
[71649] = {
commodityId = 71649,
commodityName = "万物权衡",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.68.52",
itemIds = {
620690
}
},
[71650] = {
commodityId = 71650,
commodityName = "夜空暗翼",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v5,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
620691
},
bOpenSuit = true,
suitId = 60366
},
[71651] = {
commodityId = 71651,
commodityName = "夜空暗翼",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.68.33",
itemIds = {
620692
}
},
[71652] = {
commodityId = 71652,
commodityName = "夜空暗翼",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.68.33",
itemIds = {
620693
}
},
[71653] = {
commodityId = 71653,
commodityName = "野营小精灵",
beginTime = {
seconds = 1739548800
},
endTime = {
seconds = 1741017599
},
shopTag = v5,
jumpId = 582,
jumpText = "丰收派对",
minVersion = "1.3.68.33",
itemIds = {
620694
},
bOpenSuit = true,
suitId = 60367
},
[71654] = {
commodityId = 71654,
commodityName = "野营小精灵",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620695
}
},
[71655] = {
commodityId = 71655,
commodityName = "野营小精灵",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620696
}
},
[71656] = {
commodityId = 71656,
commodityName = "招财猫",
beginTime = {
seconds = 1749657600
},
endTime = {
seconds = 1753977599
},
shopTag = v5,
jumpId = 1103,
jumpText = "鲨鱼猫",
itemIds = {
620697
},
bOpenSuit = true,
suitId = 60368
},
[71657] = {
commodityId = 71657,
commodityName = "捣蛋大师",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620698
},
suitId = 60369
},
[71658] = {
commodityId = 71658,
commodityName = "豆趣横生",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739894400
},
shopTag = v5,
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.68.33",
itemIds = {
620699
},
bOpenSuit = true,
suitId = 60370
},
[71659] = {
commodityId = 71659,
commodityName = "豆趣横生",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620700
}
},
[71660] = {
commodityId = 71660,
commodityName = "豆趣横生",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620701
}
},
[71661] = {
commodityId = 71661,
commodityName = "裁梦小剪",
beginTime = {
seconds = 1742486400
},
endTime = v4,
shopTag = v5,
shopSort = 2,
jumpId = 707,
jumpText = "星光剧场",
itemIds = {
620702
},
canGift = true,
addIntimacy = 80,
giftCoinType = 205,
giftPrice = 40,
bOpenSuit = true,
suitId = 60371
},
[71662] = {
commodityId = 71662,
commodityName = "裁梦小剪",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620703
}
},
[71663] = {
commodityId = 71663,
commodityName = "裁梦小剪",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620704
}
},
[71664] = {
commodityId = 71664,
commodityName = "背享净界",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620705
},
suitId = 60372
},
[71665] = {
commodityId = 71665,
commodityName = "背享净界",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620706
}
},
[71666] = {
commodityId = 71666,
commodityName = "背享净界",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620707
}
},
[71667] = {
commodityId = 71667,
commodityName = "飞人篮筐",
beginTime = {
seconds = 1736870400
},
endTime = {
seconds = 1741881599
},
shopTag = v5,
jumpId = 543,
jumpText = "猫猫搜城记",
itemIds = {
620708
},
bOpenSuit = true,
suitId = 60373
},
[71668] = {
commodityId = 71668,
commodityName = "活力二哈",
beginTime = {
seconds = 1736524800
},
endTime = {
seconds = 1737820799
},
shopTag = v5,
jumpId = 535,
jumpText = "春日精灵",
itemIds = {
620709
},
bOpenSuit = true,
suitId = 60374
},
[71669] = {
commodityId = 71669,
commodityName = "活力二哈",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1736524800
},
itemIds = {
620710
}
},
[71670] = {
commodityId = 71670,
commodityName = "活力二哈",
coinType = 200008,
price = 5,
beginTime = {
seconds = 1736524800
},
itemIds = {
620711
}
},
[71671] = {
commodityId = 71671,
commodityName = "云锦绘卷",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620712
},
suitId = 60375
},
[71672] = {
commodityId = 71672,
commodityName = "云锦绘卷",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620713
}
},
[71673] = {
commodityId = 71673,
commodityName = "云锦绘卷",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620714
}
},
[71674] = {
commodityId = 71674,
commodityName = "凤羽华扇",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620715
},
suitId = 60376
},
[71675] = {
commodityId = 71675,
commodityName = "凤羽华扇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620716
}
},
[71676] = {
commodityId = 71676,
commodityName = "凤羽华扇",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620717
}
},
[71677] = {
commodityId = 71677,
commodityName = "火焰精灵",
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v5,
jumpId = 538,
jumpText = "仙福盈门",
minVersion = "1.3.37.68",
itemIds = {
620718
},
suitId = 60377
},
[71678] = {
commodityId = 71678,
commodityName = "火焰精灵",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620719
}
},
[71679] = {
commodityId = 71679,
commodityName = "火焰精灵",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620720
}
},
[71680] = {
commodityId = 71680,
commodityName = "暖意小杯",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620721
},
suitId = 60378
},
[71681] = {
commodityId = 71681,
commodityName = "雪球鸟",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620722
},
suitId = 60379
},
[71682] = {
commodityId = 71682,
commodityName = "华莱士联动背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620723
},
suitId = 60380
},
[71683] = {
commodityId = 71683,
commodityName = "缤纷甜伞",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620724
},
suitId = 60381
},
[71684] = {
commodityId = 71684,
commodityName = "缤纷甜伞",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620725
}
},
[71685] = {
commodityId = 71685,
commodityName = "缤纷甜伞",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620726
}
},
[71686] = {
commodityId = 71686,
commodityName = "精彩世界",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620727
},
suitId = 60382
},
[71687] = {
commodityId = 71687,
commodityName = "精彩世界",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620728
}
},
[71688] = {
commodityId = 71688,
commodityName = "精彩世界",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620729
}
},
[71690] = {
commodityId = 71690,
commodityName = "电竞一号",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620731
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60384
},
[71691] = {
commodityId = 71691,
commodityName = "绛天战刃",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620278
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60155
},
[71692] = {
commodityId = 71692,
commodityName = "全息灵庙",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620513
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60280
},
[71693] = {
commodityId = 71693,
commodityName = "脉冲饰带",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620563
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60308
},
[71694] = {
commodityId = 71694,
commodityName = "挚爱之锚",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620279
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60156
},
[71695] = {
commodityId = 71695,
commodityName = "心动和弦",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620280
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60157
},
[71696] = {
commodityId = 71696,
commodityName = "影龙之锋",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60158
},
[71697] = {
commodityId = 71697,
commodityName = "龙胆亮银枪",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620427
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60234
},
[71698] = {
commodityId = 71698,
commodityName = "异界旅伴",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620454
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60249
},
[71699] = {
commodityId = 71699,
commodityName = "琳琅千机",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620494
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60271
},
[71700] = {
commodityId = 71700,
commodityName = "心动热麦",
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v5,
shopSort = 1,
jumpId = 10710,
jumpText = v9,
minVersion = "1.3.37.97",
itemIds = {
620495
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60272
},
[71701] = {
commodityId = 71701,
commodityName = "大耳狗背包",
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
shopTag = v5,
jumpId = 1073,
jumpText = "三丽鸥家族",
minVersion = "1.3.68.52",
itemIds = {
620732
},
bOpenSuit = true,
suitId = 60385
},
[71702] = {
commodityId = 71702,
commodityName = "美乐蒂小包",
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
shopTag = v5,
jumpId = 1074,
jumpText = "三丽鸥家族",
minVersion = "1.3.68.52",
itemIds = {
620733
},
bOpenSuit = true,
suitId = 60386
},
[71703] = {
commodityId = 71703,
commodityName = "流光狐羽",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v5,
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
620734
},
bOpenSuit = true,
suitId = 60387
},
[71704] = {
commodityId = 71704,
commodityName = "流光狐羽",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.68.100",
itemIds = {
620735
}
},
[71705] = {
commodityId = 71705,
commodityName = "流光狐羽",
coinType = 200008,
price = 10,
beginTime = v1,
minVersion = "1.3.68.100",
itemIds = {
620736
}
},
[71706] = {
commodityId = 71706,
commodityName = "悠扬竹笛",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v5,
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
620737
},
bOpenSuit = true,
suitId = 60388
},
[71707] = {
commodityId = 71707,
commodityName = "悠扬竹笛",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.68.100",
itemIds = {
620738
}
},
[71708] = {
commodityId = 71708,
commodityName = "悠扬竹笛",
coinType = 200008,
price = 5,
beginTime = v1,
minVersion = "1.3.68.100",
itemIds = {
620739
}
},
[71709] = {
commodityId = 71709,
commodityName = "竹韵清风",
beginTime = {
seconds = 1738857600
},
endTime = {
seconds = 1740671999
},
shopTag = v5,
shopSort = 1,
jumpId = 556,
jumpText = "千年烟雨",
minVersion = "1.3.68.52",
itemIds = {
620740
},
bOpenSuit = true,
suitId = 60389
},
[71710] = {
commodityId = 71710,
commodityName = "竹韵清风",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620741
}
},
[71711] = {
commodityId = 71711,
commodityName = "竹韵清风",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620742
}
},
[71712] = {
commodityId = 71712,
commodityName = "健康牛油果",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620743
},
suitId = 60390
},
[71713] = {
commodityId = 71713,
commodityName = "健康牛油果",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620744
}
},
[71714] = {
commodityId = 71714,
commodityName = "健康牛油果",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620745
}
},
[71715] = {
commodityId = 71715,
commodityName = "白水煮蛋",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620746
},
suitId = 60391
},
[71716] = {
commodityId = 71716,
commodityName = "白水煮蛋",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620747
}
},
[71717] = {
commodityId = 71717,
commodityName = "白水煮蛋",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620748
}
},
[71718] = {
commodityId = 71718,
commodityName = "慵懒小鹅",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620749
},
suitId = 60392
},
[71719] = {
commodityId = 71719,
commodityName = "慵懒小鹅",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620750
}
},
[71720] = {
commodityId = 71720,
commodityName = "慵懒小鹅",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620751
}
},
[71721] = {
commodityId = 71721,
commodityName = "灵蛇献瑞",
beginTime = {
seconds = 1738857600
},
endTime = {
seconds = 1740671999
},
shopTag = v5,
shopSort = 1,
jumpId = 556,
jumpText = "千年烟雨",
minVersion = "1.3.68.52",
itemIds = {
620752
},
bOpenSuit = true,
suitId = 60393
},
[71722] = {
commodityId = 71722,
commodityName = "灵蛇献瑞",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620753
}
},
[71723] = {
commodityId = 71723,
commodityName = "灵蛇献瑞",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620754
}
},
[71724] = {
commodityId = 71724,
commodityName = "瓶中阳光",
beginTime = {
seconds = 1736092800
},
endTime = {
seconds = 1740067199
},
shopTag = v5,
jumpId = 551,
jumpText = "嘶嘶灵宝",
itemIds = {
620755
},
suitId = 60394
},
[71725] = {
commodityId = 71725,
commodityName = "瓶中阳光",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620756
}
},
[71726] = {
commodityId = 71726,
commodityName = "瓶中阳光",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620757
}
},
[71727] = {
commodityId = 71727,
commodityName = "靴中百合",
beginTime = {
seconds = 1736611200
},
endTime = {
seconds = 1741881599
},
shopTag = v5,
jumpId = 558,
jumpText = "甜蜜魔法",
itemIds = {
620758
},
suitId = 60395
},
[71728] = {
commodityId = 71728,
commodityName = "靴中百合",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620759
}
},
[71729] = {
commodityId = 71729,
commodityName = "靴中百合",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620760
}
},
[71730] = {
commodityId = 71730,
commodityName = "梵汪自画像",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620761
},
suitId = 60396
},
[71731] = {
commodityId = 71731,
commodityName = "梵汪自画像",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620762
}
},
[71732] = {
commodityId = 71732,
commodityName = "梵汪自画像",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620763
}
},
[71733] = {
commodityId = 71733,
commodityName = "山河点破",
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1769788799
},
shopTag = v5,
jumpId = 10711,
jumpText = "峡谷祈愿",
minVersion = "1.3.68.37",
itemIds = {
620764
},
bOpenSuit = true,
suitId = 60397
},
[71734] = {
commodityId = 71734,
commodityName = "乾坤轮",
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1769788799
},
shopTag = v5,
jumpId = 10711,
jumpText = "峡谷祈愿",
minVersion = "1.3.68.37",
itemIds = {
620765
},
bOpenSuit = true,
suitId = 60398
},
[71735] = {
commodityId = 71735,
commodityName = "小小奶锅",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620766
},
suitId = 60399
},
[71736] = {
commodityId = 71736,
commodityName = "小小香锅",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620767
},
suitId = 60400
},
[71737] = {
commodityId = 71737,
commodityName = "小小暖锅",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620768
},
suitId = 60401
},
[71738] = {
commodityId = 71738,
commodityName = "切菜板板",
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
shopTag = v5,
jumpId = 586,
jumpText = "珍馐百味",
minVersion = "1.3.78.33",
itemIds = {
620769
},
suitId = 60402
},
[71739] = {
commodityId = 71739,
commodityName = "沧海之羽",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v5,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
itemIds = {
620770
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 7500,
bOpenSuit = true,
suitId = 60403
},
[71740] = {
commodityId = 71740,
commodityName = "沧海之羽",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620771
}
},
[71741] = {
commodityId = 71741,
commodityName = "沧海之羽",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620772
}
},
[71742] = {
commodityId = 71742,
commodityName = "狼人身份牌",
beginTime = {
seconds = 1739635200
},
endTime = {
seconds = 1742745599
},
shopTag = v5,
jumpId = 569,
jumpText = "幸运翻牌",
itemIds = {
620773
},
bOpenSuit = true,
suitId = 60404
},
[71743] = {
commodityId = 71743,
commodityName = "蜜语小罐",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620774
},
suitId = 60405
},
[71689] = {
commodityId = 71689,
commodityName = "炫光应援",
beginTime = {
seconds = 1750694400
},
endTime = {
seconds = 1752595199
},
shopTag = v5,
jumpId = 10712,
jumpText = "峡谷女明星",
itemIds = {
620730
},
bOpenSuit = true,
suitId = 60383
},
[71744] = {
commodityId = 71744,
commodityName = "兔耳宝匣",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v5,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
itemIds = {
620775
},
bOpenSuit = true,
suitId = 60406
},
[71745] = {
commodityId = 71745,
commodityName = "兔耳宝匣",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620776
}
},
[71746] = {
commodityId = 71746,
commodityName = "兔耳宝匣",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620777
}
},
[71747] = {
commodityId = 71747,
commodityName = "粉红吱吱",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v5,
jumpId = 641,
jumpText = "玩偶之家",
minVersion = "1.3.78.72",
itemIds = {
620778
},
bOpenSuit = true,
suitId = 60407
},
[71748] = {
commodityId = 71748,
commodityName = "粉红吱吱",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620779
}
},
[71749] = {
commodityId = 71749,
commodityName = "粉红吱吱",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620780
}
},
[71750] = {
commodityId = 71750,
commodityName = "缤纷糖果罐",
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1749139199
},
shopTag = v5,
jumpId = 641,
jumpText = "玩偶之家",
minVersion = "1.3.78.72",
itemIds = {
620781
},
bOpenSuit = true,
suitId = 60408
},
[71751] = {
commodityId = 71751,
commodityName = "缤纷糖果罐",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620782
}
},
[71752] = {
commodityId = 71752,
commodityName = "缤纷糖果罐",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620783
}
},
[71753] = {
commodityId = 71753,
commodityName = "长乐未央",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620784
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60409
},
[71754] = {
commodityId = 71754,
commodityName = "电竞一号",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620731
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60384
},
[71755] = {
commodityId = 71755,
commodityName = "绛天战刃",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620278
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60155
},
[71756] = {
commodityId = 71756,
commodityName = "全息灵庙",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620513
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60280
},
[71757] = {
commodityId = 71757,
commodityName = "脉冲饰带",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620563
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60308
},
[71758] = {
commodityId = 71758,
commodityName = "挚爱之锚",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620279
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60156
},
[71759] = {
commodityId = 71759,
commodityName = "心动和弦",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620280
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60157
},
[71760] = {
commodityId = 71760,
commodityName = "影龙之锋",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60158
},
[71761] = {
commodityId = 71761,
commodityName = "龙胆亮银枪",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620427
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60234
},
[71762] = {
commodityId = 71762,
commodityName = "异界旅伴",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620454
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60249
},
[71763] = {
commodityId = 71763,
commodityName = "琳琅千机",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620494
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60271
},
[71764] = {
commodityId = 71764,
commodityName = "心动热麦",
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v5,
shopSort = 1,
jumpId = 10714,
jumpText = v9,
minVersion = "1.3.68.100",
itemIds = {
620495
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60272
},
[71765] = {
commodityId = 71765,
commodityName = "云朵咩咩",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620785
},
suitId = 60410
},
[71766] = {
commodityId = 71766,
commodityName = "云朵咩咩",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620786
}
},
[71767] = {
commodityId = 71767,
commodityName = "云朵咩咩",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620787
}
},
[71768] = {
commodityId = 71768,
commodityName = "狗狗小屋",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620788
},
suitId = 60411
},
[71769] = {
commodityId = 71769,
commodityName = "斗士拳套",
beginTime = {
seconds = 1742572800
},
endTime = {
seconds = 1744559999
},
shopTag = v5,
jumpId = 590,
jumpText = "幸运翻翻乐",
minVersion = "1.3.78.34",
itemIds = {
620789
},
suitId = 60412
},
[71770] = {
commodityId = 71770,
commodityName = "兵卒一枚",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620790
},
suitId = 60413
},
[71771] = {
commodityId = 71771,
commodityName = "星术秘谕",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v5,
jumpId = 640,
jumpText = v8,
minVersion = "1.3.78.1",
itemIds = {
620791
},
bOpenSuit = true,
suitId = 60414
},
[71772] = {
commodityId = 71772,
commodityName = "星术秘谕",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620792
}
},
[71773] = {
commodityId = 71773,
commodityName = "星术秘谕",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620793
}
},
[71774] = {
commodityId = 71774,
commodityName = "星语捕梦网",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
shopTag = v5,
jumpId = 640,
jumpText = v8,
minVersion = "1.3.78.1",
itemIds = {
620794
},
bOpenSuit = true,
suitId = 60415
},
[71775] = {
commodityId = 71775,
commodityName = "星语捕梦网",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620795
}
},
[71776] = {
commodityId = 71776,
commodityName = "星语捕梦网",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620796
}
},
[71777] = {
commodityId = 71777,
commodityName = "神奇邮筒",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620797
},
suitId = 60416
},
[71778] = {
commodityId = 71778,
commodityName = "神奇邮筒",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620798
}
},
[71779] = {
commodityId = 71779,
commodityName = "神奇邮筒",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620799
}
},
[71780] = {
commodityId = 71780,
commodityName = "甜心出击",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620800
},
suitId = 60417
},
[71781] = {
commodityId = 71781,
commodityName = "甜心出击",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620801
}
},
[71782] = {
commodityId = 71782,
commodityName = "甜心出击",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620802
}
},
[71783] = {
commodityId = 71783,
commodityName = "甜酷背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620803
},
suitId = 60418
},
[71784] = {
commodityId = 71784,
commodityName = "甜酷背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620804
}
},
[71785] = {
commodityId = 71785,
commodityName = "甜酷背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620805
}
},
[71786] = {
commodityId = 71786,
commodityName = "时光收音机",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620806
},
suitId = 60419
},
[71787] = {
commodityId = 71787,
commodityName = "时光收音机",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620807
}
},
[71788] = {
commodityId = 71788,
commodityName = "时光收音机",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620808
}
},
[71789] = {
commodityId = 71789,
commodityName = "花语叉",
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
shopTag = v5,
jumpId = 575,
jumpText = "桃坞问春",
minVersion = "1.3.68.90",
itemIds = {
620809
},
suitId = 60420
},
[71790] = {
commodityId = 71790,
commodityName = "花语叉",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620810
}
},
[71791] = {
commodityId = 71791,
commodityName = "花语叉",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620811
}
},
[71792] = {
commodityId = 71792,
commodityName = "救急泡面",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620812
},
suitId = 60421
},
[71793] = {
commodityId = 71793,
commodityName = "救急泡面",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620813
}
},
[71794] = {
commodityId = 71794,
commodityName = "救急泡面",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620814
}
},
[71795] = {
commodityId = 71795,
commodityName = "萝卜兔兔兜",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620815
},
suitId = 60422
},
[71796] = {
commodityId = 71796,
commodityName = "萝卜兔兔兜",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620816
}
},
[71797] = {
commodityId = 71797,
commodityName = "萝卜兔兔兜",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620817
}
},
[71798] = {
commodityId = 71798,
commodityName = "偷心怀表",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620818
},
suitId = 60423
},
[71799] = {
commodityId = 71799,
commodityName = "偷心怀表",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620819
}
},
[71800] = {
commodityId = 71800,
commodityName = "偷心怀表",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620820
}
},
[71801] = {
commodityId = 71801,
commodityName = "吞吞包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620821
},
suitId = 60424
},
[71802] = {
commodityId = 71802,
commodityName = "吞吞包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620822
}
},
[71803] = {
commodityId = 71803,
commodityName = "吞吞包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620823
}
},
[71804] = {
commodityId = 71804,
commodityName = "忙碌小蜂",
beginTime = {
seconds = 1743350400
},
endTime = {
seconds = 1746460799
},
shopTag = v5,
jumpId = 596,
jumpText = "精灵谷修行",
minVersion = "1.3.78.42",
itemIds = {
620824
},
suitId = 60425
},
[71805] = {
commodityId = 71805,
commodityName = "忙碌小蜂",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620825
}
},
[71806] = {
commodityId = 71806,
commodityName = "忙碌小蜂",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620826
}
},
[71807] = {
commodityId = 71807,
commodityName = "戴帽子的狗狗",
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
shopTag = v5,
jumpId = 586,
jumpText = "珍馐百味",
minVersion = "1.3.78.33",
itemIds = {
620827
},
suitId = 60426
},
[71808] = {
commodityId = 71808,
commodityName = "戴帽子的狗狗",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620828
}
},
[71809] = {
commodityId = 71809,
commodityName = "戴帽子的狗狗",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620829
}
},
[71810] = {
commodityId = 71810,
commodityName = "以德服人",
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1746806399
},
shopTag = v5,
jumpId = 10716,
jumpText = "峡谷英豪",
itemIds = {
620830
},
bOpenSuit = true,
suitId = 60427
},
[71811] = {
commodityId = 71811,
commodityName = "桃心抱枕",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620831
},
suitId = 60428
},
[71812] = {
commodityId = 71812,
commodityName = "灭火神器",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620832
},
suitId = 60429
},
[71813] = {
commodityId = 71813,
commodityName = "宝藏小包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620833
},
suitId = 60430
},
[71814] = {
commodityId = 71814,
commodityName = "星盟护盾",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620834
},
suitId = 60431
},
[71815] = {
commodityId = 71815,
commodityName = "七彩太阳花",
beginTime = {
seconds = 1745985600
},
endTime = {
seconds = 1755187199
},
shopTag = v5,
jumpId = 670,
jumpText = "猫猫补习班",
minVersion = "1.3.88.1",
itemIds = {
620835
},
bOpenSuit = true,
suitId = 60432
},
[71816] = {
commodityId = 71816,
commodityName = "七彩太阳花",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620836
}
},
[71817] = {
commodityId = 71817,
commodityName = "七彩太阳花",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620837
}
},
[71818] = {
commodityId = 71818,
commodityName = "晶珀之梦",
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v5,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
minVersion = "1.3.88.98",
itemIds = {
620838
},
canGift = true,
addIntimacy = 160,
giftCoinType = 3950,
giftPrice = 80,
bOpenSuit = true,
suitId = 60463
},
[71819] = {
commodityId = 71819,
commodityName = "晶珀之梦",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620839
}
},
[71820] = {
commodityId = 71820,
commodityName = "晶珀之梦",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620840
}
},
[71821] = {
commodityId = 71821,
commodityName = "鸡仔快跑",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v5,
jumpId = 10598,
jumpText = "小甜豆",
minVersion = "1.3.78.73",
itemIds = {
620841
},
bOpenSuit = true,
suitId = 60464
},
[71822] = {
commodityId = 71822,
commodityName = "剁剁背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620842
},
suitId = 60465
},
[71823] = {
commodityId = 71823,
commodityName = "剁剁背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620843
}
},
[71824] = {
commodityId = 71824,
commodityName = "剁剁背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620844
}
},
[71825] = {
commodityId = 71825,
commodityName = "有力熊背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620845
},
suitId = 60466
},
[71826] = {
commodityId = 71826,
commodityName = "有力熊背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620846
}
},
[71827] = {
commodityId = 71827,
commodityName = "有力熊背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620847
}
},
[71828] = {
commodityId = 71828,
commodityName = "望海长明",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620848
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60467
},
[71829] = {
commodityId = 71829,
commodityName = "牛奶管家",
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
shopTag = v5,
jumpId = 594,
jumpText = "蜜糖彩虹之梦",
minVersion = "1.3.78.96",
itemIds = {
620849
},
suitId = 60468
},
[71832] = {
commodityId = 71832,
commodityName = "快乐菠萝",
beginTime = {
seconds = 1744387200
},
endTime = {
seconds = 1746719999
},
shopTag = v5,
jumpId = 643,
jumpText = "煎饼超人",
minVersion = "1.3.68.82",
itemIds = {
620852
},
suitId = 60469
},
[71833] = {
commodityId = 71833,
commodityName = "快乐菠萝",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620853
}
},
[71834] = {
commodityId = 71834,
commodityName = "快乐菠萝",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620854
}
},
[71835] = {
commodityId = 71835,
commodityName = "寿司旅行家",
beginTime = {
seconds = 1744387200
},
endTime = {
seconds = 1746719999
},
shopTag = v5,
jumpId = 643,
jumpText = "煎饼超人",
minVersion = "1.3.68.82",
itemIds = {
620855
},
suitId = 60470
},
[71836] = {
commodityId = 71836,
commodityName = "寿司旅行家",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620856
}
},
[71837] = {
commodityId = 71837,
commodityName = "寿司旅行家",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620857
}
},
[71838] = {
commodityId = 71838,
commodityName = "幸运玫瑰瓶",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620858
},
suitId = 60471
},
[71839] = {
commodityId = 71839,
commodityName = "幸运玫瑰瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620859
}
},
[71840] = {
commodityId = 71840,
commodityName = "幸运玫瑰瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620860
}
},
[71841] = {
commodityId = 71841,
commodityName = "汪汪葵",
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1749052800
},
shopTag = v5,
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.88.1",
itemIds = {
620861
},
suitId = 60472
},
[71842] = {
commodityId = 71842,
commodityName = "汪汪葵",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620862
}
},
[71843] = {
commodityId = 71843,
commodityName = "汪汪葵",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620863
}
},
[71844] = {
commodityId = 71844,
commodityName = "心语密卷",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620864
},
suitId = 60473
},
[71845] = {
commodityId = 71845,
commodityName = "心语密卷",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620865
}
},
[71846] = {
commodityId = 71846,
commodityName = "心语密卷",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620866
}
},
[71847] = {
commodityId = 71847,
commodityName = "葱香煎饼",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopTag = v5,
jumpId = 694,
jumpText = "幻音喵境",
itemIds = {
620867
},
bOpenSuit = true,
suitId = 60474
},
[71848] = {
commodityId = 71848,
commodityName = "葱香煎饼",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620868
}
},
[71849] = {
commodityId = 71849,
commodityName = "葱香煎饼",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620869
}
},
[71850] = {
commodityId = 71850,
commodityName = "小熊拍立得",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v5,
jumpId = 88888,
jumpText = "幻彩调律",
itemIds = {
620870
},
bOpenSuit = true,
suitId = 60475
},
[71851] = {
commodityId = 71851,
commodityName = "小熊拍立得",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620871
}
},
[71852] = {
commodityId = 71852,
commodityName = "小熊拍立得",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620872
}
},
[71853] = {
commodityId = 71853,
commodityName = "赛博妙妙包",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620873
},
suitId = 60476
},
[71854] = {
commodityId = 71854,
commodityName = "赛博妙妙包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620874
}
},
[71855] = {
commodityId = 71855,
commodityName = "赛博妙妙包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620875
}
},
[71856] = {
commodityId = 71856,
commodityName = "盒盒气气",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620876
},
suitId = 60477
},
[71857] = {
commodityId = 71857,
commodityName = "盒盒气气",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620877
}
},
[71858] = {
commodityId = 71858,
commodityName = "盒盒气气",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620878
}
},
[71859] = {
commodityId = 71859,
commodityName = "魔法契约",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620879
},
suitId = 60478
},
[71860] = {
commodityId = 71860,
commodityName = "魔法契约",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620880
}
},
[71861] = {
commodityId = 71861,
commodityName = "魔法契约",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620881
}
},
[71862] = {
commodityId = 71862,
commodityName = "星点小车",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620882
},
suitId = 60479
},
[71863] = {
commodityId = 71863,
commodityName = "星点小车",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620883
}
},
[71864] = {
commodityId = 71864,
commodityName = "星点小车",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620884
}
},
[71865] = {
commodityId = 71865,
commodityName = "南瓜随手包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620885
},
suitId = 60480
},
[71866] = {
commodityId = 71866,
commodityName = "鱼卷软垫",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620886
},
suitId = 60481
},
[71867] = {
commodityId = 71867,
commodityName = "时尚人字拖",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620887
},
suitId = 60482
},
[71868] = {
commodityId = 71868,
commodityName = "无忧防晒霜",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620888
},
suitId = 60483
},
[71869] = {
commodityId = 71869,
commodityName = "红心火雷",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620889
},
suitId = 60484
},
[71870] = {
commodityId = 71870,
commodityName = "冲刺旗标",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620890
},
suitId = 60485
},
[71871] = {
commodityId = 71871,
commodityName = "长乐未央",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620784
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60409
},
[71872] = {
commodityId = 71872,
commodityName = "电竞一号",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620731
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60384
},
[71873] = {
commodityId = 71873,
commodityName = "绛天战刃",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620278
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60155
},
[71874] = {
commodityId = 71874,
commodityName = "全息灵庙",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620513
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60280
},
[71875] = {
commodityId = 71875,
commodityName = "脉冲饰带",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620563
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60308
},
[71876] = {
commodityId = 71876,
commodityName = "挚爱之锚",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620279
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60156
},
[71877] = {
commodityId = 71877,
commodityName = "心动和弦",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620280
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60157
},
[71878] = {
commodityId = 71878,
commodityName = "影龙之锋",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620281
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60158
},
[71879] = {
commodityId = 71879,
commodityName = "龙胆亮银枪",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620427
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60234
},
[71880] = {
commodityId = 71880,
commodityName = "异界旅伴",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620454
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60249
},
[71881] = {
commodityId = 71881,
commodityName = "琳琅千机",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620494
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60271
},
[71882] = {
commodityId = 71882,
commodityName = "心动热麦",
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v5,
shopSort = 1,
jumpId = 10718,
jumpText = v9,
minVersion = "1.3.78.80",
itemIds = {
620495
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 60,
bOpenSuit = true,
suitId = 60272
},
[71883] = {
commodityId = 71883,
commodityName = "汪汪之家",
beginTime = {
seconds = 1746288000
},
endTime = {
seconds = 1749139199
},
shopTag = v5,
jumpId = 599,
jumpText = "奶油乐园奇遇",
minVersion = "1.3.88.1",
itemIds = {
620891
},
suitId = 60486
},
[71884] = {
commodityId = 71884,
commodityName = "逗猫棒",
beginTime = {
seconds = 1746288000
},
endTime = {
seconds = 1749139199
},
shopTag = v5,
jumpId = 599,
jumpText = "奶油乐园奇遇",
minVersion = "1.3.88.1",
itemIds = {
620892
},
suitId = 60487
},
[71885] = {
commodityId = 71885,
commodityName = "逗猫棒",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620893
}
},
[71886] = {
commodityId = 71886,
commodityName = "逗猫棒",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620894
}
},
[71887] = {
commodityId = 71887,
commodityName = "便携蜜桃猫",
beginTime = {
seconds = 1746720000
},
endTime = {
seconds = 1749743999
},
shopTag = v5,
jumpId = 659,
jumpText = "满杯蜜桃猫",
minVersion = "1.3.88.1",
itemIds = {
620895
},
suitId = 60488
},
[71888] = {
commodityId = 71888,
commodityName = "蜜桃可丽饼",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620896
},
suitId = 60489
},
[71889] = {
commodityId = 71889,
commodityName = "黄金椰椰",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620897
},
suitId = 60490
},
[71890] = {
commodityId = 71890,
commodityName = "熊魂之拳",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620898
},
suitId = 60491
},
[71891] = {
commodityId = 71891,
commodityName = "紫萝萝背包",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620899
},
suitId = 60492
},
[71892] = {
commodityId = 71892,
commodityName = "动物调色盘",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v5,
jumpId = 88888,
jumpText = "幻彩调律",
itemIds = {
620900
},
bOpenSuit = true,
suitId = 60493
},
[71893] = {
commodityId = 71893,
commodityName = "动物调色盘",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620901
}
},
[71894] = {
commodityId = 71894,
commodityName = "动物调色盘",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620902
}
},
[71895] = {
commodityId = 71895,
commodityName = "次元喵喵包",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620903
},
suitId = 60494
},
[71896] = {
commodityId = 71896,
commodityName = "次元喵喵包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620904
}
},
[71897] = {
commodityId = 71897,
commodityName = "次元喵喵包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620905
}
},
[71898] = {
commodityId = 71898,
commodityName = "指尖蛙蛙",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620906
},
suitId = 60495
},
[71899] = {
commodityId = 71899,
commodityName = "指尖蛙蛙",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620907
}
},
[71900] = {
commodityId = 71900,
commodityName = "指尖蛙蛙",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620908
}
},
[71901] = {
commodityId = 71901,
commodityName = "杠精小包",
beginTime = {
seconds = 1745856000
},
shopTag = v5,
jumpId = 705,
jumpText = "印章祈愿",
itemIds = {
620909
},
bOpenSuit = true,
suitId = 60496
},
[71902] = {
commodityId = 71902,
commodityName = "抬杠小包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620910
},
suitId = 60497
},
[71903] = {
commodityId = 71903,
commodityName = "梦笔生花",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620911
},
suitId = 60498
},
[71904] = {
commodityId = 71904,
commodityName = "樱花兔兔糕",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopTag = v5,
shopSort = 1,
jumpId = 1096,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
620912
},
bOpenSuit = true,
suitId = 60499
},
[71905] = {
commodityId = 71905,
commodityName = "星绵兔叽",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopTag = v5,
shopSort = 1,
jumpId = 1097,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
620913
},
bOpenSuit = true,
suitId = 60500
},
[71906] = {
commodityId = 71906,
commodityName = "吐司软床",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620914
},
suitId = 60501
},
[71907] = {
commodityId = 71907,
commodityName = "吐司软床",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620915
}
},
[71908] = {
commodityId = 71908,
commodityName = "吐司软床",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620916
}
},
[71909] = {
commodityId = 71909,
commodityName = "永不翻车",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620917
},
suitId = 60502
},
[71910] = {
commodityId = 71910,
commodityName = "永不翻车",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620918
}
},
[71911] = {
commodityId = 71911,
commodityName = "永不翻车",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620919
}
},
[71912] = {
commodityId = 71912,
commodityName = "星耳兔背包",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620920
},
suitId = 60503
},
[71913] = {
commodityId = 71913,
commodityName = "星耳兔背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620921
}
},
[71914] = {
commodityId = 71914,
commodityName = "星耳兔背包",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620922
}
},
[71915] = {
commodityId = 71915,
commodityName = "茶理宜世",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620923
},
suitId = 60504
},
[71916] = {
commodityId = 71916,
commodityName = "童话闪翼",
beginTime = {
seconds = 1749830400
},
endTime = {
seconds = 1752508799
},
shopTag = v5,
jumpId = 10719,
jumpText = "奇遇舞章",
minVersion = "1.3.88.105",
itemIds = {
620924
},
bOpenSuit = true,
suitId = 60505
},
[71917] = {
commodityId = 71917,
commodityName = "音乐飘带翅膀",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopTag = v5,
shopSort = 1,
jumpId = 694,
jumpText = "幻猫音境",
itemIds = {
620925
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 7500,
bOpenSuit = true,
suitId = 60506
},
[71918] = {
commodityId = 71918,
commodityName = "音乐飘带翅膀",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620926
}
},
[71919] = {
commodityId = 71919,
commodityName = "音乐飘带翅膀",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620927
}
},
[71920] = {
commodityId = 71920,
commodityName = "远山辉玉",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620928
},
suitId = 60507
},
[71921] = {
commodityId = 71921,
commodityName = "远山辉玉",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620929
}
},
[71922] = {
commodityId = 71922,
commodityName = "远山辉玉",
coinType = 200008,
price = 10,
beginTime = v1,
itemIds = {
620930
}
},
[71923] = {
commodityId = 71923,
commodityName = "稚玉之瓶",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620931
},
suitId = 60508
},
[71924] = {
commodityId = 71924,
commodityName = "稚玉之瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620932
}
},
[71925] = {
commodityId = 71925,
commodityName = "稚玉之瓶",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620933
}
},
[71926] = {
commodityId = 71926,
commodityName = "半融熊淇凌",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620934
},
suitId = 60509
},
[71927] = {
commodityId = 71927,
commodityName = "半融熊淇凌",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620935
}
},
[71928] = {
commodityId = 71928,
commodityName = "半融熊淇凌",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620936
}
},
[71929] = {
commodityId = 71929,
commodityName = "心跳打板器",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620937
},
suitId = 60510
},
[71930] = {
commodityId = 71930,
commodityName = "心跳打板器",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620938
}
},
[71931] = {
commodityId = 71931,
commodityName = "心跳打板器",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620939
}
},
[71932] = {
commodityId = 71932,
commodityName = "瓜瓜汽水",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620940
},
suitId = 60511
},
[71933] = {
commodityId = 71933,
commodityName = "啵啵汽水",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620941
},
suitId = 60512
},
[71934] = {
commodityId = 71934,
commodityName = "淘淘汽水",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620942
},
suitId = 60513
},
[71935] = {
commodityId = 71935,
commodityName = "谜团背包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620943
},
suitId = 60514
},
[71936] = {
commodityId = 71936,
commodityName = "破城霸王戟",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620944
},
suitId = 60515
},
[71937] = {
commodityId = 71937,
commodityName = "永恒誓言",
beginTime = {
seconds = 1749830400
},
endTime = {
seconds = 1752767999
},
shopTag = v5,
jumpId = 691,
jumpText = "怒海狂鲨",
minVersion = "1.3.88.153",
itemIds = {
620946
},
bOpenSuit = true,
suitId = 60517
},
[71938] = {
commodityId = 71938,
commodityName = "永恒誓言",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620947
}
},
[71939] = {
commodityId = 71939,
commodityName = "永恒誓言",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620948
}
},
[71940] = {
commodityId = 71940,
commodityName = "回“星”小包",
coinType = 6,
price = 10000,
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620949
},
suitId = 60518
},
[71941] = {
commodityId = 71941,
commodityName = "”鸭“力泳圈",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620950
},
suitId = 60519
},
[71942] = {
commodityId = 71942,
commodityName = "”鸭“力泳圈",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620951
}
},
[71943] = {
commodityId = 71943,
commodityName = "”鸭“力泳圈",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620952
}
},
[71944] = {
commodityId = 71944,
commodityName = "海鱼王子",
beginTime = v2,
endTime = v4,
shopTag = v5,
itemIds = {
620953
},
suitId = 60520
},
[71945] = {
commodityId = 71945,
commodityName = "海鱼王子",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620954
}
},
[71946] = {
commodityId = 71946,
commodityName = "海鱼王子",
coinType = 200008,
price = 5,
beginTime = v1,
itemIds = {
620955
}
}
}

local mt = {
mallId = 12,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
gender = 0,
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data