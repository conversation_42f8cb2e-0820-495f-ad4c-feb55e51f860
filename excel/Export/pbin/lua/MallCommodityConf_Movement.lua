--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-单人动作

local v0 = {
seconds = 1676390400
}

local v1 = {
seconds = 1676476800
}

local v2 = {
seconds = 1746115199
}

local v3 = 9

local v4 = "赛季祈愿"

local v5 = 1

local v6 = "1.2.67.1"

local v7 = "1.2.100.1"

local v8 = "1.3.6.1"

local v9 = "1.3.12.1"

local v10 = 50

local v11 = 218

local v12 = 30

local data = {
[90001] = {
commodityId = 90001,
commodityName = "欢呼",
beginTime = v0,
endTime = v1,
jumpId = 180,
jumpText = "印章祈愿",
itemIds = {
720001
}
},
[90002] = {
commodityId = 90002,
commodityName = "瑟瑟发抖",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4074854400
},
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
720002
},
canGift = true,
addIntimacy = 50,
giftCoinType = 205,
giftPrice = 40
},
[90003] = {
commodityId = 90003,
commodityName = "致谢",
beginTime = v0,
endTime = v1,
jumpId = 3,
jumpText = "星级挑战",
itemIds = {
720003
}
},
[90004] = {
commodityId = 90004,
commodityName = "爱心发射",
shopSort = 1,
jumpId = 27,
jumpText = "首充",
itemIds = {
720004
}
},
[90005] = {
commodityId = 90005,
commodityName = "躺平",
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1705593599
},
jumpId = 135,
jumpText = "新品献礼",
itemIds = {
720005
}
},
[90006] = {
commodityId = 90006,
commodityName = "你过来呀",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v4,
itemIds = {
720006
}
},
[90007] = {
commodityId = 90007,
commodityName = "满地打滚",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v4,
itemIds = {
720007
}
},
[90008] = {
commodityId = 90008,
commodityName = "锤地大哭",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v4,
itemIds = {
720008
}
},
[90009] = {
commodityId = 90009,
commodityName = "比心",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 46,
jumpText = "潮玩星方向",
itemIds = {
720009
}
},
[90010] = {
commodityId = 90010,
commodityName = "空气吉他",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v4,
itemIds = {
720010
}
},
[90011] = {
commodityId = 90011,
commodityName = "挥手",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 3,
jumpText = "赛季兑换",
itemIds = {
720014
}
},
[90012] = {
commodityId = 90012,
commodityName = "吃瓜",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v4,
itemIds = {
720015
}
},
[90013] = {
commodityId = 90013,
commodityName = "深度学习",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 33,
jumpText = "闯关挑战",
itemIds = {
720017
}
},
[90014] = {
commodityId = 90014,
commodityName = "重锤出击",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720018
}
},
[90015] = {
commodityId = 90015,
commodityName = "灵光一现",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720019
}
},
[90016] = {
commodityId = 90016,
commodityName = "原地起飞",
beginTime = v0,
endTime = v1,
jumpId = 11,
jumpText = v4,
itemIds = {
720020
}
},
[90017] = {
commodityId = 90017,
commodityName = "施展魔法",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
jumpId = 181,
jumpText = "绮梦灯",
minVersion = "1.2.80.1",
itemIds = {
720022
}
},
[90018] = {
commodityId = 90018,
commodityName = "准备起飞",
beginTime = v0,
endTime = v1,
itemIds = {
720023
}
},
[90019] = {
commodityId = 90019,
commodityName = "大明星来了",
jumpId = 47,
jumpText = "初星代言人",
itemIds = {
720026
}
},
[90020] = {
commodityId = 90020,
commodityName = "捧腹大笑",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v4,
itemIds = {
720027
}
},
[90021] = {
commodityId = 90021,
commodityName = "小天鹅舞",
beginTime = v0,
endTime = v1,
jumpId = 40,
jumpText = "庆典签到",
itemIds = {
720028
}
},
[90022] = {
commodityId = 90022,
commodityName = "一起摇摆",
beginTime = v0,
endTime = v1,
jumpId = 180,
jumpText = "印章祈愿",
itemIds = {
720029
}
},
[90023] = {
commodityId = 90023,
commodityName = "心动的感觉",
coinType = 6,
price = 500,
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1703347199
},
itemIds = {
720032
},
canGift = true,
addIntimacy = 50,
giftCoinType = 1,
giftPrice = 500
},
[90024] = {
commodityId = 90024,
commodityName = "喷火",
jumpId = 1808,
jumpText = "个人成就",
itemIds = {
720033
}
},
[90025] = {
commodityId = 90025,
commodityName = "撒花",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1703347199
},
jumpId = 115,
jumpText = "星动票选赛",
itemIds = {
720034
}
},
[90026] = {
commodityId = 90026,
commodityName = "原地坐下",
beginTime = v0,
endTime = v1,
itemIds = {
720035
}
},
[90027] = {
commodityId = 90027,
commodityName = "原地睡觉",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
720036
}
},
[90028] = {
commodityId = 90028,
commodityName = "小鸡舞",
beginTime = v0,
endTime = v1,
itemIds = {
720037
}
},
[90029] = {
commodityId = 90029,
commodityName = "僵尸舞",
beginTime = v0,
endTime = v1,
itemIds = {
720038
}
},
[90030] = {
commodityId = 90030,
commodityName = "尴尬抠抠",
coinType = 6,
price = 280,
itemIds = {
720039
},
canGift = true,
addIntimacy = 28,
giftCoinType = 1,
giftPrice = 180
},
[90031] = {
commodityId = 90031,
commodityName = "肚皮舞",
beginTime = v0,
endTime = v1,
itemIds = {
720041
}
},
[90032] = {
commodityId = 90032,
commodityName = "搓手舞",
beginTime = v0,
endTime = v1,
itemIds = {
720042
}
},
[90033] = {
commodityId = 90033,
commodityName = "偷偷流泪",
beginTime = v0,
endTime = v1,
itemIds = {
720043
}
},
[90034] = {
commodityId = 90034,
commodityName = "生气极了",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720044
}
},
[90035] = {
commodityId = 90035,
commodityName = "哈喽",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 3,
jumpText = "赛季兑换",
itemIds = {
720045
}
},
[90036] = {
commodityId = 90036,
commodityName = "满头问号",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
720049
}
},
[90037] = {
commodityId = 90037,
commodityName = "小新摇摆",
beginTime = v0,
endTime = v1,
itemIds = {
720051
}
},
[90038] = {
commodityId = 90038,
commodityName = "呼啦",
beginTime = v0,
endTime = v1,
itemIds = {
720052
}
},
[90039] = {
commodityId = 90039,
commodityName = "阿童木欢迎",
beginTime = v0,
endTime = v1,
itemIds = {
720053
}
},
[90040] = {
commodityId = 90040,
commodityName = "整个人状态非常好",
beginTime = v0,
endTime = v1,
itemIds = {
720054
}
},
[90041] = {
commodityId = 90041,
commodityName = "鸡哔你",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4074854400
},
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
720055
},
canGift = true,
addIntimacy = 50,
giftCoinType = 205,
giftPrice = 40
},
[90042] = {
commodityId = 90042,
commodityName = "让我看看",
beginTime = v0,
endTime = v1,
itemIds = {
720056
}
},
[90043] = {
commodityId = 90043,
commodityName = "泡泡汽水",
coinType = 6,
price = 500,
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
itemIds = {
720057
},
canGift = true,
addIntimacy = 50,
giftCoinType = 1,
giftPrice = 500
},
[90044] = {
commodityId = 90044,
commodityName = "动感节奏",
coinType = 6,
price = 500,
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
itemIds = {
720058
},
canGift = true,
addIntimacy = 50,
giftCoinType = 1,
giftPrice = 500
},
[90045] = {
commodityId = 90045,
commodityName = "喷射上天",
beginTime = v0,
endTime = v1,
itemIds = {
720059
}
},
[90046] = {
commodityId = 90046,
commodityName = "派对狂欢",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopSort = 2,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
720060
}
},
[90047] = {
commodityId = 90047,
commodityName = "号外号外",
beginTime = v0,
endTime = v1,
itemIds = {
720061
}
},
[90048] = {
commodityId = 90048,
commodityName = "兔子舞",
beginTime = v0,
endTime = v1,
shopSort = 2,
itemIds = {
720062
}
},
[90049] = {
commodityId = 90049,
commodityName = "Toby踢踏舞",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
jumpId = 1091,
jumpText = "摩天乐园",
minVersion = "1.3.88.53",
itemIds = {
720063
}
},
[90050] = {
commodityId = 90050,
commodityName = "呱呱舞",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopSort = 1,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
720075
}
},
[90051] = {
commodityId = 90051,
commodityName = "摇摆舞",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720076
}
},
[90052] = {
commodityId = 90052,
commodityName = "搓搓舞",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720077
}
},
[90053] = {
commodityId = 90053,
commodityName = "跺跺脚",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720078
}
},
[90054] = {
commodityId = 90054,
commodityName = "快乐秧歌",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720079
}
},
[90055] = {
commodityId = 90055,
commodityName = "刘畊宏元气操",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720080
}
},
[90056] = {
commodityId = 90056,
commodityName = "相亲相爱",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720081
}
},
[90057] = {
commodityId = 90057,
commodityName = "准备战斗",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720082
}
},
[90058] = {
commodityId = 90058,
commodityName = "赛罗变身",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720083
}
},
[90059] = {
commodityId = 90059,
commodityName = "哉佩利敖光线",
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1711295999
},
jumpId = 179,
jumpText = "奥特曼祈愿",
minVersion = v6,
itemIds = {
720084
}
},
[90060] = {
commodityId = 90060,
commodityName = "泽斯蒂姆光线",
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1711295999
},
jumpId = 179,
jumpText = "奥特曼祈愿",
minVersion = v6,
itemIds = {
720085
}
},
[90061] = {
commodityId = 90061,
commodityName = "等离子火花斩",
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1711295999
},
jumpId = 179,
jumpText = "奥特曼祈愿",
minVersion = v6,
itemIds = {
720086
}
},
[90062] = {
commodityId = 90062,
commodityName = "扭秧歌",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720087
}
},
[90063] = {
commodityId = 90063,
commodityName = "过年好",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720088
}
},
[90064] = {
commodityId = 90064,
commodityName = "浪漫回旋",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720089
}
},
[90065] = {
commodityId = 90065,
commodityName = "啦啦队舞",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720090
}
},
[90066] = {
commodityId = 90066,
commodityName = "不干",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720091
}
},
[90067] = {
commodityId = 90067,
commodityName = "八段锦叁式",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720092
}
},
[90068] = {
commodityId = 90068,
commodityName = "八段锦肆式",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720093
}
},
[90069] = {
commodityId = 90069,
commodityName = "八段锦伍式",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720094
}
},
[90070] = {
commodityId = 90070,
commodityName = "八段锦陆式",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720095
}
},
[90071] = {
commodityId = 90071,
commodityName = "八段锦柒式",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720096
}
},
[90072] = {
commodityId = 90072,
commodityName = "八段锦捌式",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720097
}
},
[90073] = {
commodityId = 90073,
commodityName = "奶龙舞鱼",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
jumpId = 1047,
jumpText = "奶龙返场",
minVersion = "1.3.7.97",
itemIds = {
720098
}
},
[90074] = {
commodityId = 90074,
commodityName = "扫帚飞",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 9,
jumpText = "山海通行证",
minVersion = v6,
itemIds = {
720099
}
},
[90075] = {
commodityId = 90075,
commodityName = "哭哭舞",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1710086399
},
jumpId = 183,
jumpText = "冰雪玫瑰",
minVersion = "1.2.80.1",
itemIds = {
720100
}
},
[90076] = {
commodityId = 90076,
commodityName = "扭一扭",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 9,
jumpText = "山海通行证",
minVersion = v6,
itemIds = {
720101
}
},
[90077] = {
commodityId = 90077,
commodityName = "开合跳",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720102
}
},
[90078] = {
commodityId = 90078,
commodityName = "开车舞",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720103
}
},
[90079] = {
commodityId = 90079,
commodityName = "无可奈何",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720104
}
},
[90080] = {
commodityId = 90080,
commodityName = "回旋飞踢",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720105
}
},
[90081] = {
commodityId = 90081,
commodityName = "蹦蹦跳跳",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720106
}
},
[90082] = {
commodityId = 90082,
commodityName = "卷起来",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720107
}
},
[90083] = {
commodityId = 90083,
commodityName = "起飞",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720108
}
},
[90084] = {
commodityId = 90084,
commodityName = "HFF人类一败涂地",
beginTime = v0,
endTime = v1,
minVersion = v6,
itemIds = {
720109
}
},
[90085] = {
commodityId = 90085,
commodityName = "功夫熊猫",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
jumpId = 1048,
jumpText = "功夫熊猫返场",
minVersion = "1.3.7.97",
itemIds = {
720110
}
},
[90086] = {
commodityId = 90086,
commodityName = "加油",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720068
}
},
[90087] = {
commodityId = 90087,
commodityName = "鼓掌",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720069
}
},
[90088] = {
commodityId = 90088,
commodityName = "无奈",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720104
}
},
[90089] = {
commodityId = 90089,
commodityName = "开合跳",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720102
}
},
[90090] = {
commodityId = 90090,
commodityName = "开车舞",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v4,
minVersion = v6,
itemIds = {
720103
}
},
[90118] = {
commodityId = 90118,
commodityName = "好运来",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1710086399
},
jumpId = 183,
jumpText = "冰雪玫瑰",
minVersion = "1.2.80.1",
itemIds = {
720118
}
},
[90091] = {
commodityId = 90091,
commodityName = "华丽后空翻",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4074854400
},
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.2.80.1",
itemIds = {
720111
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[90092] = {
commodityId = 90092,
commodityName = "功夫巨星",
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
shopSort = 1,
jumpId = 1048,
jumpText = "功夫熊猫返场",
minVersion = "1.3.7.97",
itemIds = {
720146
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[90093] = {
commodityId = 90093,
commodityName = "汪汪舞",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
jumpId = 175,
jumpText = "蔬菜精灵祈愿",
itemIds = {
720125
}
},
[90094] = {
commodityId = 90094,
commodityName = "惬意打滚",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
jumpId = 176,
jumpText = "蔬菜精灵祈愿",
itemIds = {
720137
}
},
[90095] = {
commodityId = 90095,
commodityName = "乌云密布",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
jumpId = 177,
jumpText = "暗夜冰羽",
minVersion = v7,
itemIds = {
720127
}
},
[90096] = {
commodityId = 90096,
commodityName = "冥想",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720147
}
},
[90097] = {
commodityId = 90097,
commodityName = "挥舞旗子",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720148
}
},
[90098] = {
commodityId = 90098,
commodityName = "耍帅",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720149
}
},
[90099] = {
commodityId = 90099,
commodityName = "大吃一惊",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720150
}
},
[90100] = {
commodityId = 90100,
commodityName = "托腮沉思",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720151
}
},
[90101] = {
commodityId = 90101,
commodityName = "清洁达人",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720152
}
},
[90102] = {
commodityId = 90102,
commodityName = "平地摔",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720153
}
},
[90103] = {
commodityId = 90103,
commodityName = "放松身体",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720154
}
},
[90104] = {
commodityId = 90104,
commodityName = "为你起舞",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720155
}
},
[90105] = {
commodityId = 90105,
commodityName = "麦霸形态",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720156
}
},
[90106] = {
commodityId = 90106,
commodityName = "向你致敬",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720157
}
},
[90107] = {
commodityId = 90107,
commodityName = "江南范",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v7,
itemIds = {
720158
}
},
[90108] = {
commodityId = 90108,
commodityName = "伸展运动",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720159
}
},
[90109] = {
commodityId = 90109,
commodityName = "四肢运动",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720160
}
},
[90110] = {
commodityId = 90110,
commodityName = "肩部运动",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720161
}
},
[90111] = {
commodityId = 90111,
commodityName = "体侧运动",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720162
}
},
[90112] = {
commodityId = 90112,
commodityName = "全身运动",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720163
}
},
[90113] = {
commodityId = 90113,
commodityName = "蹦蹦跳",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720164
}
},
[90114] = {
commodityId = 90114,
commodityName = "坚持自律",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720165
}
},
[90115] = {
commodityId = 90115,
commodityName = "云中起舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720166
}
},
[90116] = {
commodityId = 90116,
commodityName = "金鸡独立",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720167
}
},
[90117] = {
commodityId = 90117,
commodityName = "妖怪退散",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
minVersion = v7,
itemIds = {
720168
}
},
[90119] = {
commodityId = 90119,
commodityName = "要幸福哦",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.2.100.46",
itemIds = {
720169
}
},
[90120] = {
commodityId = 90120,
commodityName = "动次打次",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720170
}
},
[90121] = {
commodityId = 90121,
commodityName = "可爱喵",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720171
}
},
[90122] = {
commodityId = 90122,
commodityName = "犯困中",
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1717689599
},
jumpId = 179,
jumpText = "前线装备库",
minVersion = "1.2.100.65",
itemIds = {
720172
}
},
[90123] = {
commodityId = 90123,
commodityName = "指挥家",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
minVersion = v7,
itemIds = {
720173
}
},
[90124] = {
commodityId = 90124,
commodityName = "摇摇舞",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720174
}
},
[90125] = {
commodityId = 90125,
commodityName = "沙锤摇",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720175
}
},
[90126] = {
commodityId = 90126,
commodityName = "健美冠军",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720176
}
},
[90127] = {
commodityId = 90127,
commodityName = "跃动之星",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720177
}
},
[90128] = {
commodityId = 90128,
commodityName = "舞动青春",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
jumpId = 176,
jumpText = "星梦使者",
minVersion = v7,
itemIds = {
720178
}
},
[90129] = {
commodityId = 90129,
commodityName = "撑伞漫步",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720179
}
},
[90130] = {
commodityId = 90130,
commodityName = "酸爽无比",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720180
}
},
[90131] = {
commodityId = 90131,
commodityName = "天鹅少女旋转鞠躬",
beginTime = {
seconds = 1718380800
},
endTime = {
seconds = 1720367999
},
shopSort = 1,
jumpId = 603,
jumpText = "双生曼舞",
minVersion = "1.3.7.1",
itemIds = {
720181
}
},
[90132] = {
commodityId = 90132,
commodityName = "秀一下",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720182
}
},
[90133] = {
commodityId = 90133,
commodityName = "一起来跳舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720183
}
},
[90134] = {
commodityId = 90134,
commodityName = "小害羞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720184
}
},
[90135] = {
commodityId = 90135,
commodityName = "虫儿飞",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = v7,
itemIds = {
720185
}
},
[90136] = {
commodityId = 90136,
commodityName = "扔纸飞机",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720186
}
},
[90137] = {
commodityId = 90137,
commodityName = "吃刨冰",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopSort = 1,
jumpId = 188,
jumpText = "盛装派对",
minVersion = v7,
itemIds = {
720187
}
},
[90138] = {
commodityId = 90138,
commodityName = "好无语",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720188
}
},
[90139] = {
commodityId = 90139,
commodityName = "扔烟花",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720500
}
},
[90140] = {
commodityId = 90140,
commodityName = "蹦高高",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720601
}
},
[90141] = {
commodityId = 90141,
commodityName = "干劲满满",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720602
}
},
[90142] = {
commodityId = 90142,
commodityName = "胜利了！",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720603
}
},
[90143] = {
commodityId = 90143,
commodityName = "大反派",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720604
}
},
[90144] = {
commodityId = 90144,
commodityName = "不耐烦",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720605
}
},
[90145] = {
commodityId = 90145,
commodityName = "得瑟",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720606
}
},
[90146] = {
commodityId = 90146,
commodityName = "拳击准备",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720607
}
},
[90147] = {
commodityId = 90147,
commodityName = "爪爪舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720608
}
},
[90148] = {
commodityId = 90148,
commodityName = "伸懒腰",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720128
}
},
[90149] = {
commodityId = 90149,
commodityName = "黑暗实验",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopSort = 1,
jumpId = 561,
jumpText = "春水溯游",
minVersion = "1.3.68.52",
itemIds = {
720129
}
},
[90150] = {
commodityId = 90150,
commodityName = "好伤心",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720130
}
},
[90151] = {
commodityId = 90151,
commodityName = "哦耶",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720131
}
},
[90152] = {
commodityId = 90152,
commodityName = "屁屁显摆",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720132
}
},
[90153] = {
commodityId = 90153,
commodityName = "晕倒",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4074854400
},
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = v7,
itemIds = {
720133
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[90154] = {
commodityId = 90154,
commodityName = "翩翩少年",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopSort = 1,
jumpId = 561,
jumpText = "春水溯游",
minVersion = "1.3.68.52",
itemIds = {
720134
}
},
[90155] = {
commodityId = 90155,
commodityName = "庆功舞",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4074854400
},
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = v7,
itemIds = {
720135
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[90156] = {
commodityId = 90156,
commodityName = "派对走起",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v7,
itemIds = {
720136
}
},
[90157] = {
commodityId = 90157,
commodityName = "打太极",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720138
}
},
[90158] = {
commodityId = 90158,
commodityName = "扭扭舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720139
}
},
[90159] = {
commodityId = 90159,
commodityName = "洗澡舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720140
}
},
[90160] = {
commodityId = 90160,
commodityName = "地板动作",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720141
}
},
[90161] = {
commodityId = 90161,
commodityName = "旋转吧篮球",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopSort = 1,
jumpId = 561,
jumpText = "春水溯游",
minVersion = "1.3.68.52",
itemIds = {
720142
}
},
[90162] = {
commodityId = 90162,
commodityName = "打个喷嚏",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720143
}
},
[90163] = {
commodityId = 90163,
commodityName = "神枪手",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720144
}
},
[90164] = {
commodityId = 90164,
commodityName = "惊奇手雷",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720145
}
},
[90165] = {
commodityId = 90165,
commodityName = "恐龙舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720119
}
},
[90166] = {
commodityId = 90166,
commodityName = "踩点舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720120
}
},
[90167] = {
commodityId = 90167,
commodityName = "摇手手",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopSort = 1,
jumpId = 561,
jumpText = "春水溯游",
minVersion = "1.3.68.52",
itemIds = {
720121
}
},
[90168] = {
commodityId = 90168,
commodityName = "得意舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720122
}
},
[90169] = {
commodityId = 90169,
commodityName = "活宝舞",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopSort = 1,
jumpId = 561,
jumpText = "春水溯游",
minVersion = "1.3.68.52",
itemIds = {
720123
}
},
[90170] = {
commodityId = 90170,
commodityName = "丝滑舞步",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720124
}
},
[90171] = {
commodityId = 90171,
commodityName = "汪汪舞",
beginTime = v0,
endTime = v1,
minVersion = v7,
itemIds = {
720125
}
},
[90172] = {
commodityId = 90172,
commodityName = "显摆显摆",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopSort = 1,
jumpId = 561,
jumpText = "春水溯游",
minVersion = "1.3.68.52",
itemIds = {
720126
}
},
[90173] = {
commodityId = 90173,
commodityName = "帅气登场",
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1716739199
},
shopSort = 1,
jumpId = 188,
jumpText = "盛装派对",
minVersion = v7,
itemIds = {
720190
}
},
[90174] = {
commodityId = 90174,
commodityName = "超会顶",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v8,
itemIds = {
720191
}
},
[90175] = {
commodityId = 90175,
commodityName = "开花舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720192
}
},
[90176] = {
commodityId = 90176,
commodityName = "敲木鱼",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720193
}
},
[90177] = {
commodityId = 90177,
commodityName = "超级跳喵喵舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720194
}
},
[90178] = {
commodityId = 90178,
commodityName = "表白舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720195
}
},
[90179] = {
commodityId = 90179,
commodityName = "元气健身操",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720196
}
},
[90180] = {
commodityId = 90180,
commodityName = "夏日舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720197
}
},
[90181] = {
commodityId = 90181,
commodityName = "大小姐舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720198
}
},
[90182] = {
commodityId = 90182,
commodityName = "魅力舞步",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720199
}
},
[90183] = {
commodityId = 90183,
commodityName = "扔烟花",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720500
}
},
[90184] = {
commodityId = 90184,
commodityName = "蹦高高",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720601
}
},
[90185] = {
commodityId = 90185,
commodityName = "干劲满满",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720602
}
},
[90186] = {
commodityId = 90186,
commodityName = "胜利了！",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720603
}
},
[90187] = {
commodityId = 90187,
commodityName = "大反派",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720604
}
},
[90188] = {
commodityId = 90188,
commodityName = "不耐烦",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720605
}
},
[90189] = {
commodityId = 90189,
commodityName = "得瑟",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720606
}
},
[90190] = {
commodityId = 90190,
commodityName = "拳击准备",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720607
}
},
[90191] = {
commodityId = 90191,
commodityName = "爪爪舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720608
}
},
[90192] = {
commodityId = 90192,
commodityName = "动若脱兔",
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1720108799
},
jumpId = 601,
jumpText = "时光小船",
minVersion = "1.3.7.31",
itemIds = {
720609
}
},
[90193] = {
commodityId = 90193,
commodityName = "害羞甜心",
beginTime = {
seconds = 1718294400
},
endTime = {
seconds = 1720108799
},
jumpId = 602,
jumpText = "时光小船",
minVersion = "1.3.7.31",
itemIds = {
720610
}
},
[90194] = {
commodityId = 90194,
commodityName = "巡游舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720611
}
},
[90195] = {
commodityId = 90195,
commodityName = "掌声在哪里",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720612
}
},
[90196] = {
commodityId = 90196,
commodityName = "心动天鹅舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720613
}
},
[90197] = {
commodityId = 90197,
commodityName = "蝴蝶步",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720614
}
},
[90198] = {
commodityId = 90198,
commodityName = "燃起来了",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1734623999
},
jumpId = 800,
jumpText = "战神颂歌",
minVersion = "1.3.7.53",
itemIds = {
720617
}
},
[90199] = {
commodityId = 90199,
commodityName = "爆气",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
720618
}
},
[90200] = {
commodityId = 90200,
commodityName = "大风车",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720619
}
},
[90201] = {
commodityId = 90201,
commodityName = "爆裂鼓点",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720620
}
},
[90202] = {
commodityId = 90202,
commodityName = "招手舞",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
720621
}
},
[90203] = {
commodityId = 90203,
commodityName = "剑来",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1734623999
},
jumpId = 800,
jumpText = "战神颂歌",
minVersion = "1.3.7.53",
itemIds = {
720622
}
},
[90204] = {
commodityId = 90204,
commodityName = "响指舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720623
}
},
[90205] = {
commodityId = 90205,
commodityName = "仰卧起坐",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720624
}
},
[90206] = {
commodityId = 90206,
commodityName = "失重漂浮",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1734623999
},
jumpId = 800,
jumpText = "战神颂歌",
minVersion = "1.3.7.53",
itemIds = {
720625
}
},
[90207] = {
commodityId = 90207,
commodityName = "好戏开场",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720626
}
},
[90208] = {
commodityId = 90208,
commodityName = "甄嬛传-华妃",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
jumpId = 801,
jumpText = "甄嬛传祈愿",
itemIds = {
720627
}
},
[90211] = {
commodityId = 90211,
commodityName = "杏花微雨",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720630
}
},
[90212] = {
commodityId = 90212,
commodityName = "指点江山",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
720631
}
},
[90213] = {
commodityId = 90213,
commodityName = "专业爱豆",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720632
}
},
[90214] = {
commodityId = 90214,
commodityName = "公正二连",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
720633
}
},
[90215] = {
commodityId = 90215,
commodityName = "奖杯到手",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720634
}
},
[90216] = {
commodityId = 90216,
commodityName = "飘飘仙子",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720635
}
},
[90217] = {
commodityId = 90217,
commodityName = "仰卧高手",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720636
}
},
[90218] = {
commodityId = 90218,
commodityName = "美人鱼潜水",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1718899199
},
jumpId = 25,
jumpText = "特惠礼包",
minVersion = v8,
itemIds = {
720637
}
},
[90219] = {
commodityId = 90219,
commodityName = "人鲨共游",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1718899199
},
jumpId = 25,
jumpText = "特惠礼包",
minVersion = v8,
itemIds = {
720638
}
},
[90220] = {
commodityId = 90220,
commodityName = "水母互动",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1718899199
},
jumpId = 25,
jumpText = "特惠礼包",
minVersion = v8,
itemIds = {
720639
}
},
[90221] = {
commodityId = 90221,
commodityName = "冰淇淋",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
720640
}
},
[90222] = {
commodityId = 90222,
commodityName = "拳击",
beginTime = {
seconds = 1718640000
},
endTime = {
seconds = 1720367999
},
shopSort = 1,
jumpId = 604,
jumpText = "天鹅之心",
minVersion = "1.3.7.31",
itemIds = {
720641
}
},
[90223] = {
commodityId = 90223,
commodityName = "揣手",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
720642
}
},
[90224] = {
commodityId = 90224,
commodityName = "起床失败",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
720643
}
},
[90225] = {
commodityId = 90225,
commodityName = "失败丧气",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720644
}
},
[90226] = {
commodityId = 90226,
commodityName = "轻蔑",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
720645
}
},
[90227] = {
commodityId = 90227,
commodityName = "fate舞蹈",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720646
}
},
[90228] = {
commodityId = 90228,
commodityName = "伸展运动",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720647
}
},
[90229] = {
commodityId = 90229,
commodityName = "机械舞",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720648
}
},
[90230] = {
commodityId = 90230,
commodityName = "霹雳舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720649
}
},
[90232] = {
commodityId = 90232,
commodityName = "飞扇",
beginTime = {
seconds = 1719244800
},
endTime = {
seconds = 1720540799
},
jumpId = 1046,
jumpText = "星梭祈愿",
minVersion = "1.3.7.53",
itemIds = {
720653
}
},
[90233] = {
commodityId = 90233,
commodityName = "彩虹瀑布",
beginTime = {
seconds = 1719590400
},
endTime = {
seconds = 1720713599
},
shopSort = 1,
jumpId = 600,
jumpText = "小肥柴祈愿",
minVersion = "1.3.7.73",
itemIds = {
720651
}
},
[90238] = {
commodityId = 90238,
commodityName = "丟扇子",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720652
}
},
[90241] = {
commodityId = 90241,
commodityName = "半周年快乐",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720654
}
},
[90244] = {
commodityId = 90244,
commodityName = "午夜惊奇",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720655
}
},
[90247] = {
commodityId = 90247,
commodityName = "花样滑板",
beginTime = {
seconds = 1744646400
},
endTime = v2,
jumpId = 1069,
jumpText = "电竞少女",
minVersion = v8,
itemIds = {
720656
}
},
[90250] = {
commodityId = 90250,
commodityName = "纯果乐一夏",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720657
}
},
[90253] = {
commodityId = 90253,
commodityName = "招财舞",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720658
}
},
[90259] = {
commodityId = 90259,
commodityName = "扩胸运动",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720660
}
},
[90262] = {
commodityId = 90262,
commodityName = "腹背运动",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720661
}
},
[90265] = {
commodityId = 90265,
commodityName = "跳跃运动",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720662
}
},
[90268] = {
commodityId = 90268,
commodityName = "神龙",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720663
}
},
[90271] = {
commodityId = 90271,
commodityName = "惊鸿舞",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720664
}
},
[90274] = {
commodityId = 90274,
commodityName = "喵喵喵",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720665
}
},
[90277] = {
commodityId = 90277,
commodityName = "最靓的仔",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720666
}
},
[90280] = {
commodityId = 90280,
commodityName = "bangbang",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720667
}
},
[90283] = {
commodityId = 90283,
commodityName = "HUMBLE",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720668
}
},
[90286] = {
commodityId = 90286,
commodityName = "热巴第十幕",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720669
}
},
[90289] = {
commodityId = 90289,
commodityName = "星梭奖杯",
beginTime = v0,
endTime = v1,
minVersion = v8,
itemIds = {
720670
}
},
[90292] = {
commodityId = 90292,
commodityName = "甄嬛传皇上",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
jumpId = 801,
jumpText = "甄嬛传祈愿",
itemIds = {
720671
}
},
[90295] = {
commodityId = 90295,
commodityName = "甄嬛传卡池-甄嬛",
beginTime = {
seconds = 1720713600
},
endTime = {
seconds = 1723737599
},
jumpId = 801,
jumpText = "甄嬛传祈愿",
itemIds = {
720672
}
},
[90298] = {
commodityId = 90298,
commodityName = "拿了就跑",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720673
}
},
[90301] = {
commodityId = 90301,
commodityName = "热身运动",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720674
}
},
[90304] = {
commodityId = 90304,
commodityName = "元气宅舞",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720675
}
},
[90307] = {
commodityId = 90307,
commodityName = "打车舞",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720676
}
},
[90310] = {
commodityId = 90310,
commodityName = "牛仔舞",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720677
}
},
[90313] = {
commodityId = 90313,
commodityName = "甜心光波",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720678
}
},
[90316] = {
commodityId = 90316,
commodityName = "比划比划",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = v9,
itemIds = {
720679
}
},
[90319] = {
commodityId = 90319,
commodityName = "心动节拍",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720680
}
},
[90322] = {
commodityId = 90322,
commodityName = "天翔影龙",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720681
}
},
[90325] = {
commodityId = 90325,
commodityName = "蝶步舞",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720682
}
},
[90328] = {
commodityId = 90328,
commodityName = "魅力喷雾",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720683
}
},
[90331] = {
commodityId = 90331,
commodityName = "不是吧",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720684
}
},
[90334] = {
commodityId = 90334,
commodityName = "梦幻奇域",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720685
}
},
[90337] = {
commodityId = 90337,
commodityName = "天外飞仙",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720686
}
},
[90340] = {
commodityId = 90340,
commodityName = "走独木桥",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720687
}
},
[90343] = {
commodityId = 90343,
commodityName = "跳绳绊倒",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720688
}
},
[90346] = {
commodityId = 90346,
commodityName = "跪地欢呼",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720689
}
},
[90349] = {
commodityId = 90349,
commodityName = "升龙拳",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720690
}
},
[90352] = {
commodityId = 90352,
commodityName = "应援",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720691
}
},
[90355] = {
commodityId = 90355,
commodityName = "波比跳",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720692
}
},
[90358] = {
commodityId = 90358,
commodityName = "叉腰大笑",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
720693
}
},
[90361] = {
commodityId = 90361,
commodityName = "给你点赞",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720694
}
},
[90364] = {
commodityId = 90364,
commodityName = "哒咩哒咩",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720695
}
},
[90367] = {
commodityId = 90367,
commodityName = "波浪舞",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720696
}
},
[90370] = {
commodityId = 90370,
commodityName = "太热了",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720697
}
},
[90373] = {
commodityId = 90373,
commodityName = "华丽登场",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720698
}
},
[90376] = {
commodityId = 90376,
commodityName = "功夫星秀",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720699
}
},
[90379] = {
commodityId = 90379,
commodityName = "开心到飞起",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720700
}
},
[90382] = {
commodityId = 90382,
commodityName = "优雅拾取",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = v9,
itemIds = {
720701
}
},
[90385] = {
commodityId = 90385,
commodityName = "幸运祈愿",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720702
}
},
[90388] = {
commodityId = 90388,
commodityName = "真的是你呀",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720703
}
},
[90391] = {
commodityId = 90391,
commodityName = "星潮萌动",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = v9,
itemIds = {
720704
}
},
[90394] = {
commodityId = 90394,
commodityName = "假装投篮",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720705
}
},
[90397] = {
commodityId = 90397,
commodityName = "大摇大摆",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
720706
}
},
[90400] = {
commodityId = 90400,
commodityName = "演技派倒地",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
720707
}
},
[90403] = {
commodityId = 90403,
commodityName = "好开心",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720708
}
},
[90406] = {
commodityId = 90406,
commodityName = "翻转跳耍帅",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720709
}
},
[90409] = {
commodityId = 90409,
commodityName = "点到为止",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720710
}
},
[90412] = {
commodityId = 90412,
commodityName = "偷感小碎步",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720711
}
},
[90415] = {
commodityId = 90415,
commodityName = "偷感大跨步",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720712
}
},
[90418] = {
commodityId = 90418,
commodityName = "小弟膜拜膜拜你",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720713
}
},
[90421] = {
commodityId = 90421,
commodityName = "DDU-DU DDU-DU",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720714
}
},
[90424] = {
commodityId = 90424,
commodityName = "Armageddon",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720715
}
},
[90427] = {
commodityId = 90427,
commodityName = "Supernova",
beginTime = v0,
endTime = v1,
minVersion = v9,
itemIds = {
720716
}
},
[90296] = {
commodityId = 90296,
commodityName = "心动节拍",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 3,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.18.56",
itemIds = {
720680
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90297] = {
commodityId = 90297,
commodityName = "天翔影龙",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 3,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.18.56",
itemIds = {
720681
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90429] = {
commodityId = 90429,
commodityName = "爱意传递",
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1729785599
},
jumpId = 366,
jumpText = "甜兔仙踪",
minVersion = "1.3.12.52",
itemIds = {
720719
}
},
[90430] = {
commodityId = 90430,
commodityName = "你和我",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
jumpId = 1090,
jumpText = "夏日派对",
minVersion = "1.3.88.53",
itemIds = {
720724
}
},
[90431] = {
commodityId = 90431,
commodityName = "陌生人给糖",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
jumpId = 1089,
jumpText = "夏日派对",
minVersion = "1.3.88.53",
itemIds = {
720720
}
},
[90432] = {
commodityId = 90432,
commodityName = "召唤花瓣雨",
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
jumpId = 809,
jumpText = "凤求凰祈愿",
itemIds = {
720727
}
},
[90433] = {
commodityId = 90433,
commodityName = "龙胆现世",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 2,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.18.56",
itemIds = {
720733
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90434] = {
commodityId = 90434,
commodityName = "追逃游戏",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 2,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.18.56",
itemIds = {
720735
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90435] = {
commodityId = 90435,
commodityName = "神秘配方",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720740
}
},
[90436] = {
commodityId = 90436,
commodityName = "睡梦仙子",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720741
}
},
[90437] = {
commodityId = 90437,
commodityName = "复苏祈愿",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720742
}
},
[90438] = {
commodityId = 90438,
commodityName = "可爱回旋",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720737
}
},
[90439] = {
commodityId = 90439,
commodityName = "白日梦想家",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720738
}
},
[90440] = {
commodityId = 90440,
commodityName = "转圈登场",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720750
}
},
[90441] = {
commodityId = 90441,
commodityName = "流行律动",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720751
}
},
[90442] = {
commodityId = 90442,
commodityName = "身手了得",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v4,
minVersion = "1.3.18.1",
itemIds = {
720752
}
},
[90443] = {
commodityId = 90443,
commodityName = "大大弹球",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.18.1",
itemIds = {
720739
}
},
[90444] = {
commodityId = 90444,
commodityName = "运动舞步",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.18.1",
itemIds = {
720749
}
},
[90445] = {
commodityId = 90445,
commodityName = "念经",
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1727020799
},
shopSort = 1,
jumpId = 8001,
jumpText = "西行之路祈愿",
minVersion = "1.3.18.1",
itemIds = {
720754
}
},
[90446] = {
commodityId = 90446,
commodityName = "猴哥",
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1727020799
},
shopSort = 1,
jumpId = 8001,
jumpText = "西行之路祈愿",
minVersion = "1.3.18.1",
itemIds = {
720721
}
},
[90447] = {
commodityId = 90447,
commodityName = "望明月",
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1727366399
},
shopSort = 1,
jumpId = 364,
jumpText = "山河卷扇",
minVersion = "1.3.18.37",
itemIds = {
720769
}
},
[90448] = {
commodityId = 90448,
commodityName = "动作-葱葱舞",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
720747
}
},
[90449] = {
commodityId = 90449,
commodityName = "动作-倒地不起",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
720770
}
},
[90450] = {
commodityId = 90450,
commodityName = "动作-单人华尔兹",
beginTime = {
seconds = 1744905600
},
endTime = v2,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
720772
}
},
[90451] = {
commodityId = 90451,
commodityName = "初次公演",
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1728143999
},
shopSort = 1,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720777
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90452] = {
commodityId = 90452,
commodityName = "灵动之舞",
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1728143999
},
shopSort = 1,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720776
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90453] = {
commodityId = 90453,
commodityName = "绛天战甲",
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1728143999
},
shopSort = 1,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720759
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90454] = {
commodityId = 90454,
commodityName = "挚爱之约",
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1728143999
},
shopSort = 1,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720758
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90455] = {
commodityId = 90455,
commodityName = "心动节拍",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720680
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90456] = {
commodityId = 90456,
commodityName = "天翔影龙",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720681
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90457] = {
commodityId = 90457,
commodityName = "龙胆现世",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720733
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90458] = {
commodityId = 90458,
commodityName = "追逃游戏",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720735
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90459] = {
commodityId = 90459,
commodityName = "初次公演",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720777
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90460] = {
commodityId = 90460,
commodityName = "灵动之舞",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720776
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90461] = {
commodityId = 90461,
commodityName = "异界灵契",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720819
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90462] = {
commodityId = 90462,
commodityName = "挚爱之约",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
minVersion = "1.3.12.118",
itemIds = {
720758
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90463] = {
commodityId = 90463,
commodityName = "一掌劈飞",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
jumpId = 1092,
jumpText = "吾皇猫",
minVersion = "1.3.88.53",
itemIds = {
720791
}
},
[90464] = {
commodityId = 90464,
commodityName = "干饭步伐",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732723200
},
jumpId = 33,
jumpText = "闯关挑战",
minVersion = "1.3.18.61",
itemIds = {
720807
}
},
[90465] = {
commodityId = 90465,
commodityName = "热情搏击",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732723200
},
jumpId = 33,
jumpText = "闯关挑战",
minVersion = "1.3.18.61",
itemIds = {
720753
}
},
[90466] = {
commodityId = 90466,
commodityName = "呼唤群星",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
itemIds = {
720804
}
},
[90467] = {
commodityId = 90467,
commodityName = "星夜圆舞曲",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
itemIds = {
720805
}
},
[90468] = {
commodityId = 90468,
commodityName = "宇宙起源",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
itemIds = {
720806
}
},
[90469] = {
commodityId = 90469,
commodityName = "怀旧迪斯科",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
itemIds = {
720808
}
},
[90470] = {
commodityId = 90470,
commodityName = "灵动之舞",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 3,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.18.56",
itemIds = {
720776
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90471] = {
commodityId = 90471,
commodityName = "初次公演",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 3,
jumpId = 10674,
jumpText = "峡谷幻梦",
minVersion = "1.3.18.56",
itemIds = {
720777
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90472] = {
commodityId = 90472,
commodityName = "耍宝舞",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
itemIds = {
720811
}
},
[90473] = {
commodityId = 90473,
commodityName = "打气舞",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
itemIds = {
720812
}
},
[90474] = {
commodityId = 90474,
commodityName = "发射火箭",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
itemIds = {
720813
}
},
[90475] = {
commodityId = 90475,
commodityName = "箭无虚发",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
itemIds = {
720814
}
},
[90478] = {
commodityId = 90478,
commodityName = "抓小星星",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4074854400
},
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
minVersion = "1.3.26.34",
itemIds = {
720842
},
canGift = true,
addIntimacy = 50,
giftCoinType = 205,
giftPrice = 40
},
[90476] = {
commodityId = 90476,
commodityName = "赛博行者",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.12.118",
itemIds = {
720834
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90477] = {
commodityId = 90477,
commodityName = "超时空追击",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
minVersion = "1.3.12.118",
itemIds = {
720825
},
canGift = true,
addIntimacy = 50,
giftCoinType = 218,
giftPrice = 30
},
[90479] = {
commodityId = 90479,
commodityName = "发型不能乱",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
720781
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[90480] = {
commodityId = 90480,
commodityName = "佩剑连击",
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
720841
}
},
[90481] = {
commodityId = 90481,
commodityName = "高抬腿训练",
beginTime = {
seconds = 1731772800
},
endTime = {
seconds = 1732809599
},
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.26.93",
itemIds = {
720799
}
},
[90482] = {
commodityId = 90482,
commodityName = "举白旗",
beginTime = {
seconds = 1731772800
},
endTime = {
seconds = 1732809599
},
jumpId = 1064,
jumpText = "限时礼包",
minVersion = "1.3.26.93",
itemIds = {
720792
}
},
[90485] = {
commodityId = 90485,
commodityName = "魔仙变身",
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
jumpId = 8011,
jumpText = "星缘奇境",
minVersion = "1.3.26.93",
itemIds = {
720886
}
},
[90486] = {
commodityId = 90486,
commodityName = "星意回旋",
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
jumpId = 8012,
jumpText = "星缘奇境",
minVersion = "1.3.26.93",
itemIds = {
720887
}
},
[90483] = {
commodityId = 90483,
commodityName = "缤纷绘卷",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
jumpId = 10699,
jumpText = "扬帆起航",
minVersion = "1.3.12.118",
itemIds = {
720875
}
},
[90484] = {
commodityId = 90484,
commodityName = "乘风破浪",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
jumpId = 10700,
jumpText = "扬帆起航",
minVersion = "1.3.12.118",
itemIds = {
720876
}
},
[90487] = {
commodityId = 90487,
commodityName = "颠勺大师",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720867
}
},
[90488] = {
commodityId = 90488,
commodityName = "烧烤大师",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720868
}
},
[90489] = {
commodityId = 90489,
commodityName = "面包出炉",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720869
}
},
[90490] = {
commodityId = 90490,
commodityName = "扶额叹气",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720866
}
},
[90491] = {
commodityId = 90491,
commodityName = "太生气了",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720870
}
},
[90492] = {
commodityId = 90492,
commodityName = "专业气氛组",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720871
}
},
[90493] = {
commodityId = 90493,
commodityName = "拉伸运动",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720872
}
},
[90494] = {
commodityId = 90494,
commodityName = "可爱告别",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = "1.3.37.1",
itemIds = {
720873
}
},
[90495] = {
commodityId = 90495,
commodityName = "分享冰棍",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.37.1",
itemIds = {
720865
}
},
[90496] = {
commodityId = 90496,
commodityName = "大事不好了",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.37.1",
itemIds = {
720744
}
},
[90497] = {
commodityId = 90497,
commodityName = "神圣庇佑",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
720908
}
},
[90498] = {
commodityId = 90498,
commodityName = "审判之刃",
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
720909
}
},
[90499] = {
commodityId = 90499,
commodityName = "松软蹦蹦",
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
shopSort = 1,
jumpId = 502,
jumpText = "冰雪赐福",
minVersion = "1.3.68.52",
itemIds = {
720900
}
},
[90601] = {
commodityId = 90601,
commodityName = "庆祝胜利",
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1740758399
},
shopSort = 1,
jumpId = 175,
jumpText = "永恒之誓",
minVersion = "1.3.68.70",
itemIds = {
720902
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40
},
[90600] = {
commodityId = 90600,
commodityName = "甩爪爪",
beginTime = {
seconds = 1740153600
},
endTime = {
seconds = 1743091199
},
shopSort = 1,
jumpId = 568,
jumpText = "莱恩特咖啡屋",
minVersion = "1.3.68.87",
itemIds = {
720984
}
},
[90602] = {
commodityId = 90602,
commodityName = "小猫舞",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
720956
}
},
[90603] = {
commodityId = 90603,
commodityName = "扇子狐火",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
720957
}
},
[90604] = {
commodityId = 90604,
commodityName = "元气舞蹈",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
720958
}
},
[90605] = {
commodityId = 90605,
commodityName = "绝对不行",
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 4074854400
},
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
itemIds = {
720974
},
canGift = true,
addIntimacy = 50,
giftCoinType = 205,
giftPrice = 40
},
[91001] = {
commodityId = 91001,
commodityName = "吹笛者",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720935
}
},
[91002] = {
commodityId = 91002,
commodityName = "阅金经",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720936
}
},
[91003] = {
commodityId = 91003,
commodityName = "流云飞天",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720937
}
},
[91004] = {
commodityId = 91004,
commodityName = "小发雷霆",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720925
}
},
[91005] = {
commodityId = 91005,
commodityName = "打招呼",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720926
}
},
[91006] = {
commodityId = 91006,
commodityName = "急急国王",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720927
}
},
[91007] = {
commodityId = 91007,
commodityName = "仕女图",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720932
}
},
[91008] = {
commodityId = 91008,
commodityName = "百媚生",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = "1.3.68.1",
itemIds = {
720933
}
},
[91009] = {
commodityId = 91009,
commodityName = "左右摇摆",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.68.1",
itemIds = {
720924
}
},
[91010] = {
commodityId = 91010,
commodityName = "放飞孔明灯",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.68.1",
itemIds = {
720934
}
},
[91011] = {
commodityId = 91011,
commodityName = "查验身份中",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720985
}
},
[91012] = {
commodityId = 91012,
commodityName = "狼人别过来",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720986
}
},
[91013] = {
commodityId = 91013,
commodityName = "在叫我吗",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720763
}
},
[91014] = {
commodityId = 91014,
commodityName = "摇骰子",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720989
}
},
[91015] = {
commodityId = 91015,
commodityName = "不舍道别",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720990
}
},
[91016] = {
commodityId = 91016,
commodityName = "优雅站定",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720991
}
},
[91017] = {
commodityId = 91017,
commodityName = "慵懒午后",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720992
}
},
[91018] = {
commodityId = 91018,
commodityName = "给你鼓劲",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 640,
jumpText = v4,
itemIds = {
720993
}
},
[91019] = {
commodityId = 91019,
commodityName = "暴烈黑影",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
itemIds = {
720987
}
},
[91020] = {
commodityId = 91020,
commodityName = "花样旋步",
beginTime = {
seconds = 1741881600
},
endTime = v2,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
itemIds = {
720999
}
},
[91021] = {
commodityId = 91021,
commodityName = "撒欢舞",
beginTime = {
seconds = 1742572800
},
endTime = {
seconds = 1744559999
},
shopSort = 1,
jumpId = 590,
jumpText = "幸运翻翻乐",
minVersion = "1.3.78.34",
itemIds = {
720994
}
},
[91022] = {
commodityId = 91022,
commodityName = "焦点爵士舞",
beginTime = {
seconds = 1742572800
},
endTime = {
seconds = 1744559999
},
shopSort = 1,
jumpId = 591,
jumpText = "幸运翻翻乐",
minVersion = "1.3.78.34",
itemIds = {
720995
}
},
[91023] = {
commodityId = 91023,
commodityName = "秀秀大鱼",
beginTime = {
seconds = 1742572800
},
endTime = {
seconds = 1749139199
},
shopSort = 1,
jumpId = 599,
jumpText = "奶油乐园奇遇",
minVersion = "1.3.78.34",
itemIds = {
720844
}
},
[91024] = {
commodityId = 91024,
commodityName = "飘飘而立",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
722030
}
},
[91025] = {
commodityId = 91025,
commodityName = "魔法攻击",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
722031
}
},
[91026] = {
commodityId = 91026,
commodityName = "万象引",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
722032
}
},
[91027] = {
commodityId = 91027,
commodityName = "活跃狂欢",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
720861
}
},
[91028] = {
commodityId = 91028,
commodityName = "欢呼雀跃",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
720862
}
},
[91029] = {
commodityId = 91029,
commodityName = "萌萌舞蹈",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
720864
}
},
[91030] = {
commodityId = 91030,
commodityName = "劈叉",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
720797
}
},
[91031] = {
commodityId = 91031,
commodityName = "可爱歪头",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
720771
}
},
[91032] = {
commodityId = 91032,
commodityName = "不开心嘛",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
720880
}
},
[91033] = {
commodityId = 91033,
commodityName = "又空军了",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
722003
}
},
[91034] = {
commodityId = 91034,
commodityName = "停不下来",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopSort = 1,
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = "1.3.88.1",
itemIds = {
720979
}
},
[91035] = {
commodityId = 91035,
commodityName = "捡到宝了",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopSort = 1,
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = "1.3.88.1",
itemIds = {
720782
}
},
[91036] = {
commodityId = 91036,
commodityName = "真香",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopSort = 1,
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = "1.3.88.1",
itemIds = {
720793
}
},
[91037] = {
commodityId = 91037,
commodityName = "轻轻水上漂",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722061
}
},
[91038] = {
commodityId = 91038,
commodityName = "山海泛涟漪",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722063
}
},
[91039] = {
commodityId = 91039,
commodityName = "览山河",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722091
}
},
[91040] = {
commodityId = 91040,
commodityName = "笑塌地面",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722067
}
},
[91041] = {
commodityId = 91041,
commodityName = "疯狂流汗",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722068
}
},
[91042] = {
commodityId = 91042,
commodityName = "战术后仰",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722069
}
},
[91043] = {
commodityId = 91043,
commodityName = "完美展示",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722070
}
},
[91044] = {
commodityId = 91044,
commodityName = "从天而降",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = "1.3.99.1",
itemIds = {
722071
}
},
[91045] = {
commodityId = 91045,
commodityName = "专属服务",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.99.1",
itemIds = {
722072
}
},
[91046] = {
commodityId = 91046,
commodityName = "虎虎生风拳",
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.99.1",
itemIds = {
722062
}
},
[91047] = {
commodityId = 91047,
commodityName = "鲨鱼咬手手",
beginTime = {
seconds = 1749657600
},
endTime = {
seconds = 1753977599
},
shopSort = 1,
jumpId = 1103,
jumpText = "鲨鱼猫",
minVersion = "1.3.99.1",
itemIds = {
720901
}
},
[91048] = {
commodityId = 91048,
commodityName = "接个大球",
beginTime = {
seconds = 1749657600
},
endTime = {
seconds = 1753977599
},
shopSort = 1,
jumpId = 1104,
jumpText = "草莓猫",
minVersion = "1.3.99.1",
itemIds = {
720846
}
}
}

local mt = {
mallId = 14,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
shopTag = {
3,
16
},
gender = 0,
itemNums = {
1
},
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data