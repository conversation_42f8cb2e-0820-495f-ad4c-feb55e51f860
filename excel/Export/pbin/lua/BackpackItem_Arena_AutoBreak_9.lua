--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_Arena.xlsx: Arena物件

local v0 = 1

local v1 = "50021"

local data = {
[260025] = {
id = 260025,
type = "ItemType_Arena_Frame",
quality = 3,
name = "奖杯头像框",
desc = "奖杯头像框",
icon = "T_Arena_Item_HeadFrameCircle_027",
picture = "CDN:CT_Arena_Item_HeadFrameCircle_027",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[260026] = {
id = 260026,
type = "ItemType_Arena_Frame",
desc = "符合S13昆仑秘境主题，预期BP高级付费投放",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[260027] = {
id = 260027,
type = "ItemType_Arena_Frame",
quality = 2,
desc = "符合S13昆仑秘境主题，预期BP付费投放",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[260028] = {
id = 260028,
type = "ItemType_Arena_Frame",
desc = "唐三小舞元素头像框，投放在唐三小舞奖池内",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[260029] = {
id = 260029,
type = "ItemType_Arena_Frame",
quality = 2,
desc = "忍者的头像框",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[260030] = {
id = 260030,
type = "ItemType_Arena_Frame",
quality = 3,
desc = "符合S13昆仑秘境主题",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[260031] = {
id = 260031,
type = "ItemType_Arena_Frame",
quality = 2,
desc = "铠",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[270022] = {
id = 270022,
type = "ItemType_Arena_VoiceStyle",
quality = 3,
name = "奖杯播报",
desc = "奖杯播报",
icon = "CDN:CT_Arena_BroadCast_Item_027",
picture = "T_Arena_BroadCast_027",
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[270023] = {
id = 270023,
type = "ItemType_Arena_VoiceStyle",
desc = "符合S13昆仑秘境主题",
isDynamic = 1,
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[270024] = {
id = 270024,
type = "ItemType_Arena_VoiceStyle",
desc = "符合S13昆仑秘境主题",
isDynamic = 1,
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[270025] = {
id = 270025,
type = "ItemType_Arena_VoiceStyle",
desc = "忍者的播报",
isDynamic = 1,
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[270026] = {
id = 270026,
type = "ItemType_Arena_VoiceStyle",
desc = "机甲龙",
isDynamic = 1,
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[270027] = {
id = 270027,
type = "ItemType_Arena_VoiceStyle",
desc = "铠换色",
isDynamic = 1,
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[270028] = {
id = 270028,
type = "ItemType_Arena_VoiceStyle",
desc = "符合S13昆仑秘境主题",
isDynamic = 1,
isShowPopRewardView = 1,
gotoButtonJumpId = 350,
gotoButtonJumpText = "前往装配"
},
[300221] = {
id = 300221,
stackedNum = 999999,
maxNum = 999999,
name = "亚瑟的赠礼",
desc = "购买后将获得亚瑟的英雄皮肤和亚瑟专属橙卡（限峡谷模式使用）",
icon = "CDN:T_Common_Item_System_Arena_Arthur",
picture = "CDN:T_Common_Item_System_Arena_Arthur",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
10003
},
pakGroup = v1,
isShowPopRewardView = 0
},
[301152] = {
id = 301152,
type = "ItemType_Arena_HeroTry",
stackedNum = 999999,
maxNum = 999999,
quality = 2,
name = "峡谷英雄：冰夷",
desc = "（限峡谷模式使用）获取后可以使用刺客冰夷。重复获得时将自动转换为1000峡谷币。",
icon = "CDN:CT_Arena_SquareHero_1052",
useType = "IUTO_ArenaHeroTry",
useParam = {
1052
},
resourceConf = {
model = "SK_PL_321_moba",
physics = "SK_PL_321_moba_Physics"
},
outEnter = "as_ch_enter_pl_321_moba",
outIdle = "AS_CH_OutIdle_001",
outShow = "AS_CH_IdleShow_PL_321_moba",
outShowIntervalTime = 10,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 1000
},
autoUse = true,
isShowPopRewardView = 1
},
[301231] = {
id = 301231,
stackedNum = 999999,
maxNum = 999999,
quality = 3,
name = "虞姬体验卡（1天）",
desc = "免费体验1天峡谷英雄虞姬（获取后自动使用）。",
icon = "CDN:CT_Arena_SquareHero_1031",
useType = "IUTO_ArenaHeroTry",
useParam = {
1031
},
pakGroup = v1,
arenaHeroTryCardConf = {
duration = 24,
exchangeItemId = 3541,
exchangeItemNum = 1
},
autoUse = true,
isShowPopRewardView = 1
},
[302019] = {
id = 302019,
type = "ItemType_Arena_Equip",
quality = 3,
name = "磐石之心 刘备",
desc = "（限峡谷模式使用）获取后可以获得刘备的专属武器。",
icon = "T_Arena_Skin_42_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1042,
1107
},
resourceConf = {
model = "SK_PL_Prop_235_1_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302020] = {
id = 302020,
type = "ItemType_Arena_Equip",
quality = 3,
name = "浪漫启航 孙策",
desc = "（限峡谷模式使用）获取后可以获得孙策的专属武器。",
icon = "T_Arena_Skin_1_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1001,
1108
},
resourceConf = {
model = "SK_PL_Prop_085_11_moba",
modelType = 2
},
scaleTimes = 80,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-30
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302021] = {
id = 302021,
type = "ItemType_Arena_Equip",
quality = 3,
name = "千机之书 张良",
desc = "（限峡谷模式使用）获取后可以获得张良的专属武器。",
icon = "T_Arena_Skin_29_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1029,
1109
},
resourceConf = {
model = "SK_PL_Prop_227_11_moba",
modelType = 2
},
scaleTimes = 150,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302022] = {
id = 302022,
type = "ItemType_Arena_Equip",
quality = 3,
name = "冰锐斩空 花木兰",
desc = "（限峡谷模式使用）获取后可以获得花木兰的专属武器。",
icon = "T_Arena_Skin_24_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1024,
1110
},
resourceConf = {
model = "SK_PL_Prop_182_01_moba",
modelType = 2
},
scaleTimes = 60,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
30
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302023] = {
id = 302023,
type = "ItemType_Arena_Equip",
quality = 3,
name = "苍蓝剑盾 亚瑟",
desc = "（限峡谷模式使用）获取后可以获得亚瑟的专属武器。",
icon = "T_Arena_Skin_11_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1011,
1111
},
resourceConf = {
model = "SK_PL_Prop_127_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302024] = {
id = 302024,
type = "ItemType_Arena_Equip",
quality = 3,
name = "霜钩玉露 李白",
desc = "（限峡谷模式使用）获取后可以获得李白的专属武器。",
icon = "T_Arena_Skin_19_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1019,
1112
},
resourceConf = {
model = "SK_PL_Prop_157_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302025] = {
id = 302025,
type = "ItemType_Arena_Equip",
quality = 3,
name = "黑礁猎手 夏侯惇",
desc = "（限峡谷模式使用）获取后可以获得夏侯惇的专属武器。",
icon = "T_Arena_Skin_28_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1028,
1113
},
resourceConf = {
model = "SK_PL_Prop_226_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302026] = {
id = 302026,
type = "ItemType_Arena_Equip",
quality = 2,
name = "枫林唱晚 海月",
desc = "（限峡谷模式使用）获取后可以获得海月的专属武器。",
icon = "T_Arena_Skin_46_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1046,
1114
},
resourceConf = {
model = "SK_PL_Prop_181_11_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302027] = {
id = 302027,
type = "ItemType_Arena_Equip",
quality = 2,
name = "银海生花 绮",
desc = "（限峡谷模式使用）获取后可以获得绮的专属武器。",
icon = "T_Arena_Skin_36_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1049,
1115
},
resourceConf = {
model = "SK_PL_Prop_288_01_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302028] = {
id = 302028,
type = "ItemType_Arena_Equip",
quality = 2,
name = "废土猎手 马可波罗",
desc = "（限峡谷模式使用）获取后可以获得马可波罗的专属武器。",
icon = "T_Arena_Skin_27_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1027,
1116
},
resourceConf = {
model = "SK_PL_Prop_224_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302029] = {
id = 302029,
type = "ItemType_Arena_Equip",
quality = 2,
name = "春风得意 诸葛亮",
desc = "（限峡谷模式使用）获取后可以获得诸葛亮的专属武器。",
icon = "T_Arena_Skin_44_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1044,
1117
},
resourceConf = {
model = "SK_PL_Prop_236_01_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302030] = {
id = 302030,
type = "ItemType_Arena_Equip",
quality = 2,
name = "寒冬勇士 程咬金",
desc = "（限峡谷模式使用）获取后可以获得程咬金的专属武器。",
icon = "T_Arena_Skin_30_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1030,
1118
},
resourceConf = {
model = "SK_PL_Prop_228_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[302031] = {
id = 302031,
type = "ItemType_Arena_Equip",
quality = 2,
name = "白色修罗 铠",
desc = "（限峡谷模式使用）获取后可以获得铠的专属武器。",
icon = "T_Arena_Skin_30_01",
useType = "IUTO_ArenaHeroEquip",
useParam = {
1002,
1121
},
resourceConf = {
model = "SK_PL_Prop_084_01_moba",
modelType = 2
},
scaleTimes = 100,
pakGroup = v1,
arenaHeroTryCardConf = {
exchangeItemId = 3541,
exchangeItemNum = 10
},
previewShareOffset = {
0,
-5
},
autoUse = true,
isShowPopRewardView = 1,
customDesc = "峡谷英雄专属武器"
},
[320228] = {
id = 320228,
type = "ItemType_Common",
name = "S135v5赛季更新视频",
desc = "S135v5赛季更新视频",
pakGroup = v1
},
[320229] = {
id = 320229,
type = "ItemType_Common",
name = "S133v3赛季更新视频",
desc = "S133v3赛季更新视频",
pakGroup = v1
},
[320230] = {
id = 320230,
type = "ItemType_Common",
name = "S13足球赛季更新视频",
desc = "S13足球赛季更新视频",
pakGroup = v1
},
[320231] = {
id = 320231,
type = "ItemType_Arena_ReportAnim",
stackedNum = 999999,
maxNum = 999999,
name = "风神灌注",
desc = "【阵前舞】效果改为：释放后，【楚歌起】改为发射冷却极短的龙卷风，造成伤害和1.5秒击飞",
icon = "CDN:CT_Arena_Item_YuJiCard",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11018
},
pakGroup = v1,
autoUse = true
},
[320232] = {
id = 320232,
stackedNum = 999999,
maxNum = 999999,
name = "破军星陨",
desc = "【魔神降世】可连续施放2次，冷却减少20%；禁锢场可眩晕敌人0.6秒。",
icon = "CDN:CT_Arena_Item_LvBu",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11019
},
pakGroup = v1,
isShowPopRewardView = 0
},
[320233] = {
id = 320233,
stackedNum = 999999,
maxNum = 999999,
name = "攻坚核心",
desc = "释放【红眼】吸引周围目标。5秒伤害+20%，强化普攻不再突进，攻击改为360°",
icon = "CDN:CT_Arena_Item_DianWei",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11020
},
pakGroup = v1,
isShowPopRewardView = 0
},
[320234] = {
id = 320234,
stackedNum = 999999,
maxNum = 999999,
name = "无敌熊猫车",
desc = "【小霸王护盾】开启时变形成战车，免疫控制向前横冲，撞飞首次接触的目标",
icon = "CDN:CT_Arena_Item_LiuShan",
useType = "IUTO_ArenaCardPack",
useParam = {
2,
11021
},
pakGroup = v1,
isShowPopRewardView = 0
},
[303500] = {
id = 303500,
quality = 2,
name = "虚妄纠缠",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconPur",
picture = "CDN:CT_Arena_Item_CardIconPur",
useType = "IUTO_ArenaCard",
useParam = {
1046008
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303501] = {
id = 303501,
quality = 2,
name = "全力击飞",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconPur",
picture = "CDN:CT_Arena_Item_CardIconPur",
useType = "IUTO_ArenaCard",
useParam = {
1047009
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303502] = {
id = 303502,
quality = 2,
name = "大手笔",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconPur",
picture = "CDN:CT_Arena_Item_CardIconPur",
useType = "IUTO_ArenaCard",
useParam = {
1049016
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303503] = {
id = 303503,
quality = 3,
name = "新月",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconBlue",
picture = "CDN:CT_Arena_Item_CardIconBlue",
useType = "IUTO_ArenaCard",
useParam = {
1046005
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303504] = {
id = 303504,
quality = 3,
name = "崩山",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconBlue",
picture = "CDN:CT_Arena_Item_CardIconBlue",
useType = "IUTO_ArenaCard",
useParam = {
1040008
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303505] = {
id = 303505,
quality = 2,
name = "余威阵阵",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconPur",
picture = "CDN:CT_Arena_Item_CardIconPur",
useType = "IUTO_ArenaCard",
useParam = {
1040013
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303506] = {
id = 303506,
quality = 3,
name = "释放负重",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconBlue",
picture = "CDN:CT_Arena_Item_CardIconBlue",
useType = "IUTO_ArenaCard",
useParam = {
1047008
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303507] = {
id = 303507,
quality = 2,
name = "浴血奋战",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconPur",
picture = "CDN:CT_Arena_Item_CardIconPur",
useType = "IUTO_ArenaCard",
useParam = {
1047003
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303508] = {
id = 303508,
quality = 3,
name = "浪潮",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconBlue",
picture = "CDN:CT_Arena_Item_CardIconBlue",
useType = "IUTO_ArenaCard",
useParam = {
1049013
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303509] = {
id = 303509,
quality = 2,
name = "大手笔",
desc = "峡谷英雄专属卡牌",
icon = "CDN:CT_Arena_Item_CardIconPur",
picture = "CDN:CT_Arena_Item_CardIconPur",
useType = "IUTO_ArenaCard",
useParam = {
1049016
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303511] = {
id = 303511,
quality = 3,
name = "月影序曲",
desc = "峡谷英雄专属卡牌",
icon = "T_Arena_Icon_ShopCard",
picture = "T_Arena_Img_BigCard",
useType = "IUTO_ArenaCard",
useParam = {
1048011
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 0
},
[303512] = {
id = 303512,
quality = 2,
name = "心绪",
desc = "峡谷英雄专属卡牌",
icon = "T_Arena_Icon_ShopCard",
picture = "T_Arena_Img_BigCard",
useType = "IUTO_ArenaCard",
useParam = {
1048004
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 0
},
[303513] = {
id = 303513,
name = "暗无天日",
desc = "<ArenaCardBlue>【日蚀祭典】</>一次生成三颗黑暗能量体，并额外获得护盾",
icon = "CDN:CT_Arena_Item_CardIconOra",
picture = "CDN:CT_Arena_Item_CardIconOra",
useType = "IUTO_ArenaCard",
useParam = {
1022008
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303514] = {
id = 303514,
name = "万剑归宗",
desc = "<ArenaCardBlue>【神来之笔】</>范围边缘生成多道飞剑，技能结束时，飞剑下落造成<ArenaCardGreen>200%</>物攻的伤害",
icon = "CDN:CT_Arena_Item_CardIconOra",
picture = "CDN:CT_Arena_Item_CardIconOra",
useType = "IUTO_ArenaCard",
useParam = {
1019012
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303515] = {
id = 303515,
name = "伞影婆娑",
desc = "无论手中是否有伞，释放技能均可在场上留下伞，并可在伞之间换位。场上存在纸伞时，暴击增加<ArenaCardGreen>40%</>",
icon = "CDN:CT_Arena_Item_CardIconOra",
picture = "CDN:CT_Arena_Item_CardIconOra",
useType = "IUTO_ArenaCard",
useParam = {
1021015
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303516] = {
id = 303516,
name = "剑走星驰",
desc = "<ArenaCardBlue>【逐星】</>召唤一把飞剑代替本体位移，获得强化时召唤剑阵",
icon = "CDN:CT_Arena_Item_CardIconOra",
picture = "CDN:CT_Arena_Item_CardIconOra",
useType = "IUTO_ArenaCard",
useParam = {
1026013
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303517] = {
id = 303517,
quality = 3,
name = "斩立决",
desc = "<ArenaCardBlue>【苍破斩】</>的前摇时间降低一半",
icon = "CDN:CT_Arena_Item_CardIconBlue",
picture = "CDN:CT_Arena_Item_CardIconBlue",
useType = "IUTO_ArenaCard",
useParam = {
1024005
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
},
[303518] = {
id = 303518,
quality = 3,
name = "御剑术",
desc = "在空中普攻时获得霸体并向前御剑飞行一段距离，对路径附近目标造成<ArenaCardGreen>150%</>物攻的伤害",
icon = "CDN:CT_Arena_Item_CardIconBlue",
picture = "CDN:CT_Arena_Item_CardIconBlue",
useType = "IUTO_ArenaCard",
useParam = {
1026014
},
pakGroup = v1,
autoUse = true,
isShowPopRewardView = 1
}
}

local mt = {
type = "ItemType_AutoUse",
stackedNum = 1,
maxNum = 1,
quality = 1,
autoUse = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data