--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_策划专用.xlsx: 下载任务

local data = {
[7000] = {
id = 7000,
name = "下载趣斗玩法包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7000
}
}
}
}
}
}
}
},
[7003] = {
id = 7003,
name = "下载大乱斗包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7003
}
}
}
}
}
}
}
},
[7004] = {
id = 7004,
name = "下载FPS包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7004
}
}
}
}
}
}
}
},
[7005] = {
id = 7005,
name = "下载家具包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7005
}
}
}
}
}
}
}
},
[7006] = {
id = 7006,
name = "下载极限竞速包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7006
}
}
}
}
}
}
}
},
[7007] = {
id = 7007,
name = "下载语音",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7007
}
}
}
}
}
}
}
},
[7008] = {
id = 7008,
name = "下载塔防大亨包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7008
}
}
}
}
}
}
}
},
[7009] = {
id = 7009,
name = "下载chase包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7009
}
}
}
}
}
}
}
},
[7010] = {
id = 7010,
name = "下载DDB包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7010
}
}
}
}
}
}
}
},
[7011] = {
id = 7011,
name = "下载兽人塔防包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7011
}
}
}
}
}
}
}
},
[7012] = {
id = 7012,
name = "下载皮肤展示资源包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7012
}
}
}
}
}
}
}
},
[7013] = {
id = 7013,
name = "下载农场资源包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7013
}
}
}
}
}
}
}
},
[7014] = {
id = 7014,
name = "下载竞技场包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7014
}
}
}
}
}
}
}
},
[7015] = {
id = 7015,
name = "下载Mayday包体",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7015
}
}
}
}
}
}
}
},
[7016] = {
id = 7016,
name = "下载皮肤高清资源包2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7016
}
}
}
}
}
}
}
},
[7017] = {
id = 7017,
name = "下载皮肤基础资源包1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7017
}
}
}
}
}
}
}
},
[7018] = {
id = 7018,
name = "下载谁是狼人基础包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7018
}
}
}
}
}
}
}
},
[7019] = {
id = 7019,
name = "下载贴图资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7019
}
}
}
}
}
}
}
},
[7020] = {
id = 7020,
name = "下载关卡音频资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7020
}
}
}
}
}
}
}
},
[7021] = {
id = 7021,
name = "下载缤纷时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7021
}
}
}
}
}
}
}
},
[7022] = {
id = 7022,
name = "下载基础资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7022
}
}
}
}
}
}
}
},
[7023] = {
id = 7023,
name = "下载玩法界面资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7023
}
}
}
}
}
}
}
},
[7024] = {
id = 7024,
name = "下载排位资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7024
}
}
}
}
}
}
}
},
[7030] = {
id = 7030,
name = "下载高清资源包",
desc = "下载高清资源包，重新登录后可以获得额外奖励哦！",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7030
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
6,
400530
},
numList = {
10000,
50,
1
},
validPeriodList = {
0,
0,
14
}
}
},
[7031] = {
id = 7031,
name = "下载橙装PV资源包",
desc = "下载高清资源包，重新登录后可以获得额外奖励哦！",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7031
}
}
}
}
}
}
}
},
[7040] = {
id = 7040,
name = "下载乐园时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7040
}
}
}
}
}
}
}
},
[7041] = {
id = 7041,
name = "下载精灵时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7041
}
}
}
}
}
}
}
},
[7042] = {
id = 7042,
name = "下载星愿时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7042
}
}
}
}
}
}
}
},
[7043] = {
id = 7043,
name = "下载美食时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7043
}
}
}
}
}
}
}
},
[7044] = {
id = 7044,
name = "下载大唐时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7044
}
}
}
}
}
}
}
},
[7045] = {
id = 7045,
name = "下载真相时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7045
}
}
}
}
}
}
}
},
[7046] = {
id = 7046,
name = "下载超能时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7046
}
}
}
}
}
}
}
},
[7047] = {
id = 7047,
name = "下载热门时装资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7047
}
}
}
}
}
}
}
},
[7050] = {
id = 7050,
name = "下载经典模式资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7050
}
}
}
}
}
}
}
},
[7051] = {
id = 7051,
name = "下载泡泡大战资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7051
}
}
}
}
}
}
}
},
[7052] = {
id = 7052,
name = "下载星宝农场拓展包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7052
}
}
}
}
}
}
}
},
[7054] = {
id = 7054,
name = "下载NR3E8Rich资源包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7054
}
}
}
}
}
}
}
},
[7055] = {
id = 7055,
name = "下载天天晋级赛基础包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7055
}
}
}
}
}
}
}
},
[7056] = {
id = 7056,
name = "下载天天晋级赛白银包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7056
}
}
}
}
}
}
}
},
[7057] = {
id = 7057,
name = "下载谁是狼人扩展包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7057
}
}
}
}
}
}
}
},
[7100] = {
id = 7100,
name = "下载s8赛季更新包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7100
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 2002
},
[7101] = {
id = 7101,
name = "下载S9赛季更新包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7101
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 2003
},
[7102] = {
id = 7102,
name = "下载S10赛季更新包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7102
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 2004
},
[7103] = {
id = 7103,
name = "下载S11赛季更新包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7103
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 2005
},
[7104] = {
id = 7104,
name = "下载S12赛季更新包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7104
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 2006
},
[7105] = {
id = 7105,
name = "下载S13赛季更新包",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
7105
}
}
}
}
}
}
},
reward = {
itemIdList = {
2
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 2007
}
}

local mt = {
reward = {
itemIdList = {
6
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 2001
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data