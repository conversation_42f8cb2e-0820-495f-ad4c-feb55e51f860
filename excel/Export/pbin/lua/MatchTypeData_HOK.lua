--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_HOK.xlsx: 玩法

local data = {
[6101] = {
id = 6101,
modeID = 3,
desc = "峡谷5v5",
maxTeamMember = 5,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_HOK",
image = "CDN:T_ModelSelectLarge_Img_Type_HOK",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "6101",
modeGroup = "6102|6101",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4,
5
},
teamTag = "5",
unlockRule = "请前往手机端游玩",
battleRecordCnt = 30,
matchRuleId = 6101,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 6100001,
mmrType = "MST_HOK",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_HOK",
isShowBattleRecord = true,
descShort = "经典的5v5峡谷对决，率先击破敌方水晶，即可获得胜利！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult3v3",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
recordType = 6101,
buttonDesc = "休闲",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 900,
layoutID = 24,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 66004,
warmRoundMatchRuleId = 3,
gameModeType = 1,
detailLinkId = 232,
detailLinkDesc = "备战",
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
isEnableAiLabArenaWarmRound = true,
aiLabWarmRoundRoomInfoId = 66801,
aiLabWarmRoundMatchRuleId = 66801,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
loginResetCheckLock = 1,
useDynamicCfg = true,
isArena = true,
battleDetailStyle = "Feature.Arena.Script.HOK.Moba.UI.FinalAccount.UI_HOK_FinalAccount_DetailMain"
},
[6102] = {
id = 6102,
modeID = 3,
desc = "峡谷5v5",
maxTeamMember = 5,
conditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101
}
}
}
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_HOK",
image = "CDN:T_ModelSelectLarge_Img_Type_HOK",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "6101",
modeGroup = "6102|6101",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4,
5
},
teamTag = "5",
unlockRule = "完成1次峡谷5v5[休闲]",
battleRecordCnt = 30,
matchRuleId = 6102,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 6100001,
mmrType = "MST_HOKDegree",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_HOKQualifying",
isShowBattleRecord = true,
descShort = "经典的5v5峡谷对决，率先击破敌方水晶，即可获得胜利！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult3v3",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
recordType = 6101,
buttonDesc = "排位",
accountUIName = "UI_Arena_FinalAccount",
gameTypeId = 900,
layoutID = 24,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 66104,
warmRoundMatchRuleId = 3,
gameModeType = 2,
detailLinkId = 232,
detailLinkDesc = "备战",
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
isEnableAiLabArenaWarmRound = true,
aiLabWarmRoundRoomInfoId = 66802,
aiLabWarmRoundMatchRuleId = 66802,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
loginResetCheckLock = 1,
useDynamicCfg = true,
isArena = true,
aiLabEquivalentRoomInfo = {
66301,
66302,
66303,
66304
},
aiLabEquivalentMatchRule = {
66802
},
battleDetailStyle = "Feature.Arena.Script.HOK.Moba.UI.FinalAccount.UI_HOK_FinalAccount_DetailMain"
}
}

local mt = {
isPermanent = false,
TeamMatchGame = false,
isShowBattleRecord = false,
UseDefaultChampionDisplayScene = false,
isEnableAiLabArenaWarmRound = false,
useDynamicCfg = false,
isArena = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data