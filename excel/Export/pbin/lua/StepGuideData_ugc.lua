--com.tencent.wea.xlsRes.table_StepGuideData => excel/xls/X_新手引导表_UGC.xlsx: 新引导步骤

local v0 = 1002

local v1 = "{X=0, Y = 0}"

local v2 = 1001

local v3 = "clicked"

local data = {
[5001] = {
StepID = 5001,
GuideID = 50001,
Comment = "引导点开乐园中地图",
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetGuildFirstMapItem",
GetButtonFunc = "GetGuildFirstMapItemBtn",
UIStyle_Frist = 1012,
UIText_Frist = "欢迎来到乐园星图！这里收藏了星宝们精彩的自制地图，快来感受奇妙之旅吧！",
UIOffset_Frist = v1,
UIStyle_Third = 1003,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=-50, Y = 0}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3,
SfxID = 3502
},
[5002] = {
StepID = 5002,
GuideID = 50001,
Comment = "引导开始“单人游玩”",
SubStep = 2,
StepType = 6,
TypeParams = "UI_UGCEditor_RoomList_5002",
IsForceGuide = true,
WindowName = "UI_UGCEditor_RoomList",
GetWidgetFunc = "GetGuildFirstMapSimpleBtn",
GetButtonFunc = "GetGuildFirstMapSimpleBtn",
UIStyle_Frist = 1006,
UIText_Frist = "星宝们精心制作的地图在召唤你，快来一探究竟！",
UIOffset_Frist = "{X=-450, Y = -300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-150, Y = -200}",
bMask = true,
ButtonMode = v3
},
[5003] = {
StepID = 5003,
GuideID = 50002,
Comment = "引导“新建地图”",
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetGuildGongFangCreateMap",
GetButtonFunc = "GetGuildGongFangCreateMap",
UIStyle_Frist = 1006,
UIText_Frist = "点击按钮，新建地图，开启奇遇造梦之旅！",
UIOffset_Frist = "{X=-450, Y =-300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-150, Y = -200}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[5005] = {
StepID = 5005,
GuideID = 50002,
Comment = "引导进入地图编辑",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_UGC_TemplateView_Main",
GetWidgetFunc = "GetGuildGongFangCreateMap_OK",
GetButtonFunc = "GetGuildGongFangCreateMap_OK",
UIStyle_Frist = 1006,
UIText_Frist = "点击创建地图，进入编辑模式，探索无限可能！",
UIOffset_Frist = "{X=-450, Y = -300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-150, Y = -200}",
bAnyWhereNext = true,
bOtherButtonExit = true,
bMask = true,
ButtonMode = v3
},
[5006] = {
StepID = 5006,
GuideID = 50003,
Comment = "引导打开“帮助”入口",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_TopLeft",
WidgetName = "UI_UGC_SwitcherButton_help",
GetButtonFunc = "GetGuideGongFangBtn",
UIStyle_Frist = 1012,
UIText_Frist = "欢迎进入创意无限的星图编辑器！在开始你的造梦之旅前，先跟我学习一些小技巧吧！",
UIOffset_Frist = v1,
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=-55, Y = 50}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3,
SfxID = 3500
},
[5007] = {
StepID = 5007,
GuideID = 50004,
Comment = "介绍专项玩法规则",
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "每天来闯关挑战通关地图获得<Highlight31>积分</>，使用<Highlight31>积分</>可以在<Highlight31>闯关挑战商店</>中兑换丰厚奖励！随着冒险深入，难度和积分都会提升。",
UIOffset_Frist = v1,
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3,
SfxID = 3501
},
[5009] = {
StepID = 5009,
GuideID = 50004,
Comment = "引导开始专项玩法",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetDailyLevelEnterMapBtn",
GetButtonFunc = "GetDailyLevelEnterMapBtn",
UIStyle_Frist = 1006,
UIText_Frist = "那么，开始闯关挑战吧！",
UIOffset_Frist = "{X=-450, Y = -300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-150, Y = -200}",
bMask = true,
ButtonMode = v3
},
[5010] = {
StepID = 5010,
GuideID = 50005,
Comment = "介绍巡游玩法规则",
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "闯关挑战每轮会提供<Highlight31>两个</>不同的选择，对应独特的<Highlight31>挑战难度</>和<Highlight31>奖励</>，难度越高奖励越丰厚哦。",
UIOffset_Frist = v1,
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3,
SfxID = 3501
},
[5011] = {
StepID = 5011,
GuideID = 50005,
Comment = "介绍巡游玩法规则2",
SubStep = 2,
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "完成闯关挑战即可获得积分和金币。<Highlight31>累计积分可以获得稀有奖励</>，金币则可以在闯关挑战商店内兑换物品！",
UIOffset_Frist = v1,
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3,
SfxID = 3502
},
[5012] = {
StepID = 5012,
GuideID = 50005,
Comment = "引导巡游2.0按钮",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetStarCruiseHelpClickWidget",
GetButtonFunc = "GetStarCruiseHelpClickWidget",
UIStyle_Frist = 1006,
UIText_Frist = "那么，开始闯关挑战吧！",
UIOffset_Frist = "{X=-450, Y = -450}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-200, Y = -250}",
bMask = true,
ButtonMode = v3,
bTriggerOnlyUIShowOnTop = true
},
[5013] = {
StepID = 5013,
GuideID = 50006,
Comment = "编辑器工具箱",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_TopLeft",
WidgetName = "UI_UGC_SwitcherButton_moreOperation",
GetButtonFunc = "GetHelpToolBoxBtn",
UIStyle_Frist = 1006,
UIText_Frist = "工具箱内容多多，助力星宝自由造梦，来看看吧！",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v3
},
[5014] = {
StepID = 5014,
GuideID = 50007,
Comment = "星图推荐-高亮推荐项(NEW)",
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetGuideTuiJIanPanel",
UIStyle_Frist = 1012,
UIText_Frist = "欢迎来到星世界！充满创意的星宝们为大家带来了个性又精彩的<Highlight31>自制地图</>，常来探索发现，开启属于你的奇妙之旅吧~",
UIOffset_Frist = v1,
UIStyle_Third = 1003,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=250, Y = 200}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
SfxID = 3502
},
[5016] = {
StepID = 5016,
GuideID = 50008,
Comment = "星图推荐-高亮推荐项B",
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetGuideTuiJIanPanel",
GetButtonFunc = "GetGuideTuiJianBtn",
UIStyle_Frist = 1012,
UIText_Frist = "欢迎来到星世界！充满创意的星宝们为大家带来了个性又精彩的<Highlight31>自制地图</>，来看这里！",
UIOffset_Frist = v1,
UIStyle_Third = 1003,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=-50, Y = 0}",
bMask = true,
ButtonMode = v3,
SfxID = 3502
},
[5114] = {
StepID = 5114,
GuideID = 50008,
Comment = "星图推荐-等待地图详情页打开",
SubStep = 2,
StepType = 6,
TypeParams = "UI_UGCEditor_RoomList_5002",
IsForceGuide = true,
bMask = true
},
[5017] = {
StepID = 5017,
GuideID = 50008,
Comment = "星图推荐-引导开始游玩B",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_UGCEditor_RoomList",
GetWidgetFunc = "GetGuildFirstMapSimpleBtn",
GetButtonFunc = "GetGuildFirstMapSimpleBtn",
UIStyle_Frist = 1013,
UIText_Frist = "{head=\"T_Head_UGC_005\", headBg=\"T_HeadFrame_030\", name=\"伸懒腰\",    content=\"我收藏了很多飞机模型，最喜欢双翼机，想分享给大家，玩玩我搭的地图吧！\",    typeName1=\"内测玩家\",    typeName2=\"模玩爱好者\",    gender=0,    authType=0,} ",
UIOffset_Frist = "{X=0, Y = -250}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-150, Y = -250}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[5018] = {
StepID = 5018,
GuideID = 50009,
Comment = "星图推荐-高亮推荐项C",
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetGuideTuiJIanPanel",
GetButtonFunc = "GetGuideTuiJianBtn",
UIStyle_Frist = 1012,
UIText_Frist = "欢迎来到星世界！充满创意的星宝们为大家带来了个性又精彩的<Highlight31>自制地图</>，来看这里！",
UIOffset_Frist = v1,
UIStyle_Third = 1003,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=-50, Y = 0}",
bMask = true,
ButtonMode = v3,
SfxID = 3502
},
[5019] = {
StepID = 5019,
GuideID = 50009,
Comment = "星图推荐-引导开始游玩C",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_UGCEditor_RoomList",
GetWidgetFunc = "GetGuildFirstMapSimpleBtn",
GetButtonFunc = "GetGuildFirstMapSimpleBtn",
UIStyle_Frist = 1013,
UIText_Frist = "{head=\"T_Head_UGC_005\", headBg=\"T_HeadFrame_030\", name=\"伸懒腰\",    content=\"我收藏了很多飞机模型，最喜欢双翼机，想分享给大家，玩玩我搭的地图吧！\",    typeName1=\"内测玩家\",    typeName2=\"模玩爱好者\",    gender=0,    authType=0,} ",
UIOffset_Frist = "{X=0, Y = -250}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-150, Y = -250}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[5020] = {
StepID = 5020,
GuideID = 50010,
Comment = "星图推荐(New)-高亮左侧栏",
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetLeftTabGuideBtn",
GetButtonFunc = "GetLeftTabGuideBtn",
UIStyle_Frist = 1006,
UIText_Frist = "这里可以切换星图推荐的地图类型！",
UIOffset_Frist = "{X=250, Y =50}",
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=130, Y = 0}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[5021] = {
StepID = 5021,
GuideID = 50011,
Comment = "星图广场-名图堂高亮",
IsForceGuide = true,
WindowName = "UI_Park",
GetWidgetFunc = "GetFamousMapGuidePanel",
GetButtonFunc = "GetFamousMapGuideBtn",
UIStyle_Frist = 1012,
UIText_Frist = "【名图堂】中收藏了众多顶级造梦师的优秀作品，就从这里开启星世界之旅吧！",
UIOffset_Frist = "{X=-0, Y = 0}",
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=-50, Y = 70}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[5101] = {
StepID = 5101,
GuideID = 51001,
Comment = "配色灵感--编辑器工具箱",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_TopLeft",
WidgetName = "UI_UGC_SwitcherButton_moreOperation",
GetButtonFunc = "GetHelpToolBoxBtn",
UIStyle_Frist = 1005,
UIText_Frist = "试试配色灵感功能吧！",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[5102] = {
StepID = 5102,
GuideID = 51001,
Comment = "配色灵感",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_UGCEditor_Tools",
WidgetName = "UI_UGC_SwitcherButton_AIColor",
GetButtonFunc = "GetGuildAIColorBtn",
UIStyle_Frist = 1005,
UIText_Frist = "可一键对视野内的物件换色！",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bAnyWhereNext = true,
bOtherButtonExit = true,
ButtonMode = v3
},
[5103] = {
StepID = 5103,
GuideID = 51002,
Comment = "引导新增技能",
WindowName = "UI_UGCEditor_skillEdit",
WidgetName = "UI_UGCEditor_skillListItem",
GetWidgetFunc = "GetGuideAddSkillBtn",
GetButtonFunc = "GetGuideAddSkillBtn",
UIStyle_Frist = 1005,
UIText_Frist = "点击新增技能",
UIOffset_Frist = "{X=100, Y = 0}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y =-20}",
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v3,
SfxID = 3502
},
[5104] = {
StepID = 5104,
GuideID = 51003,
Comment = "搭建灵感-选主题",
IsForceGuide = true,
WindowName = "UI_AIReference",
GetWidgetFunc = "GetGuideFirstGenerateItem",
GetButtonFunc = "GetGuideFirstGenerateItem",
UIStyle_Frist = 1005,
UIText_Frist = "选个主题试试吧",
UIOffset_Frist = "{X=-250, Y =-300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "4",
UIOffset_Second = "{X=-50, Y = -200}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v3
},
[5105] = {
StepID = 5105,
GuideID = 51003,
Comment = "搭建灵感-改文本",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_AIReference",
WidgetName = "UI_Common_EditHintTextBox",
ButtonName = "w_btn_guideEditHintText",
UIStyle_Frist = 1005,
UIText_Frist = "这里可以修改描述文字",
UIOffset_Frist = "{X=-250, Y =-300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "4",
UIOffset_Second = "{X=-50, Y = -200}",
bAnyWhereNext = true,
bOtherButtonExit = true,
ButtonMode = v3
},
[5106] = {
StepID = 5106,
GuideID = 51003,
Comment = "搭建灵感-生成",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_AIReference",
WidgetName = "w_switcher_Generate_Btn",
GetButtonFunc = "GetGuideGenerateBtn",
UIStyle_Frist = 1005,
UIText_Frist = "点击可以生成参考图",
UIOffset_Frist = "{X=-250, Y =-300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "4",
UIOffset_Second = "{X=-50, Y = -200}",
bAnyWhereNext = true,
bOtherButtonExit = true,
ButtonMode = v3
},
[5107] = {
StepID = 5107,
GuideID = 51004,
Comment = "简单编辑模式弹窗",
StepType = 7,
TypeParams = "{windowName=\"UI_UGCEditor_ModeSelect\"}",
IsForceGuide = true,
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true
},
[5108] = {
StepID = 5108,
GuideID = 51005,
Comment = "简单编辑模式背包折叠提示",
WindowName = "UI_UGCEditor_Bag",
WidgetName = "UI_UGCEditor_BagOccupy",
GetButtonFunc = "GetGuideOpenBagBtn",
UIStyle_Frist = 1005,
UIText_Frist = "移动时元件栏会收起，点击按钮收起和展开元件栏",
UIOffset_Frist = "{X=-800,Y=100}",
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y = -10}",
UIStyle_Second = 1001,
UIText_Second = "1",
UIOffset_Second = "{X=-200,Y=100}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v3
},
[5109] = {
StepID = 5109,
GuideID = 51006,
Comment = "UGC编辑器简单编辑模式提示1",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_TopLeft",
WidgetName = "UI_UGC_SwitcherButton_setting",
GetButtonFunc = "GetHelpSettingBtn",
UIStyle_Frist = 1005,
UIText_Frist = "简易模式开启，可以用精简的操作方式编辑地图",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v3
},
[5110] = {
StepID = 5110,
GuideID = 51006,
Comment = "UGC编辑器简单编辑模式提示2",
SubStep = 2,
WindowName = "UI_UGCSetting",
GetWidgetFunc = "GetGuideEditModeBtn",
UIStyle_Frist = 1005,
UIText_Frist = "快来尝试一下吧",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Third = 1003,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bAnyWhereNext = true,
bOtherButtonExit = true,
ButtonMode = v3
},
[5111] = {
StepID = 5111,
GuideID = 51007,
Comment = "UGC编辑器-简单编辑模式-放置",
StepType = 6,
TypeParams = "UGC_GUIDE_EDITMODE_PLACEFINISH",
IsForceGuide = true,
WindowName = "UI_UGCEditor_Bag",
GetWidgetFunc = "GetGuidePlaceItemBtn",
UIStyle_Frist = 1005,
UIText_Frist = "从元件栏拖出元件，放置到场景中",
UIOffset_Frist = "{X=-800,Y=-200}",
UIStyle_Third = 1003,
UIOffset_Third = v1,
UIStyle_Second = 1021,
UIText_Second = "0",
UIOffset_Second = "{X=-900,Y=-100}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v3
},
[5112] = {
StepID = 5112,
GuideID = 51008,
Comment = "UGC编辑器-简单编辑模式-操作",
IsForceGuide = true,
WindowName = "UI_UGCEasyOperate",
GetWidgetFunc = "GetGuideRoateCenter",
UIStyle_Frist = 1005,
UIText_Frist = "拖动元件移动，使用操作栏调整高度、旋转和缩放",
UIOffset_Frist = "{X=-300,Y=-300}",
UIStyle_Second = 1008,
UIText_Second = "0",
UIOffset_Second = "{X=-250,Y=-100}",
bAnyWhereNext = true,
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v3
},
[5113] = {
StepID = 5113,
GuideID = 51009,
Comment = "UGC编辑器-简单编辑模式-飞行",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_Right",
GetWidgetFunc = "GetGuideFlyBtn",
GetButtonFunc = "GetGuideFlyBtn",
UIStyle_Frist = 1005,
UIText_Frist = "点击飞行按钮可以进入飞行状态，可以再次点击恢复行走",
UIOffset_Frist = "{X=-450, Y =-300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-200, Y = -200}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v3
},
[5115] = {
StepID = 5115,
GuideID = 51010,
Comment = "UGC编辑器-简单编辑模式-复位",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_Right",
GetWidgetFunc = "GetGuideRepositionBtn",
GetButtonFunc = "GetGuideRepositionBtn",
UIStyle_Frist = 1005,
UIText_Frist = "点击复位按钮可以回到出生点位置",
UIOffset_Frist = "{X=-450, Y =-300}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "3",
UIOffset_Second = "{X=-200, Y = -200}",
bAnyWhereNext = true,
IsKeyStep = true,
ButtonMode = v3
},
[5116] = {
StepID = 5116,
GuideID = 51011,
Comment = "界面编辑-引导打开“界面帮助”入口",
IsForceGuide = true,
WindowName = "UI_UGC_UIEditor",
WidgetName = "w_btn_Help",
GetButtonFunc = "GetUIEditorGuideBtn",
UIStyle_Frist = 1012,
UIText_Frist = "欢迎进入界面编辑器！在开始你的界面创作前，先跟我学习一些必要的小技巧吧！",
UIOffset_Frist = v1,
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y = -5}",
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=-55, Y = 50}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3,
SfxID = 3500
},
[5118] = {
StepID = 5118,
GuideID = 51012,
Comment = "塔防NPC对话",
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "欢迎进入界面编辑器！在塔防模版中我们加入了一些有趣的新功能，现在就先跟我学习这些新功能吧！",
UIOffset_Frist = v1,
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
SfxID = 3502
},
[5119] = {
StepID = 5119,
GuideID = 51013,
Comment = "引导打开“设置”",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_TopLeft",
WidgetName = "UI_UGC_SwitcherButton_setting",
GetButtonFunc = "GetHelpSettingBtn",
UIStyle_Frist = 1005,
UIText_Frist = "首先打开设置界面",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bMask = true,
ButtonMode = v3
},
[5121] = {
StepID = 5121,
GuideID = 51013,
Comment = "引导打开“地图设置”",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_UGCSetting",
GetWidgetFunc = "GetGuideMapSettingTabBtn",
GetButtonFunc = "GetGuideMapSettingTabBtn",
UIStyle_Frist = 1005,
UIText_Frist = "打开【地图设置】",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=30, Y = 50}",
bMask = true,
ButtonMode = v3
},
[5122] = {
StepID = 5122,
GuideID = 51013,
Comment = "NPC介绍\"地图设置\"",
SubStep = 3,
IsForceGuide = true,
WindowName = "UI_UGCSetting",
WidgetName = "w_switcher_content",
UIStyle_Frist = 1012,
UIText_Frist = "在这里，造梦师可以编辑塔防地图的各项属性啦！",
UIOffset_Frist = v1,
bAnyWhereNext = true,
bMask = true,
SfxID = 3500
},
[5124] = {
StepID = 5124,
GuideID = 51013,
Comment = "NPC介绍\"玩法设置\"\"角色设置\"",
SubStep = 4,
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "在【玩法设置】和【角色设置】内，造梦师可以自由修改它们的数值参数，调整地图的难易程度。",
UIOffset_Frist = v1,
bAnyWhereNext = true,
bMask = true
},
[5130] = {
StepID = 5130,
GuideID = 51013,
Comment = "NPC介绍“导航网格数据”",
SubStep = 5,
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "【导航网格数据】功能需要与【寻路区域】元件组合使用，它们可以根据场景规划出地面兽人活动的区域。",
UIOffset_Frist = v1,
bAnyWhereNext = true,
bMask = true
},
[5131] = {
StepID = 5131,
GuideID = 51013,
Comment = "引导点击塔防元件栏",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_UGCEditor_Bag",
GetWidgetFunc = "GetGuideTaFangTabBtn",
UIStyle_Frist = 1012,
UIText_Frist = "说到这里，就不得不再具体介绍一下编辑器新增的塔防元件啦！快来和我学习这部分的内容吧！",
UIOffset_Frist = v1,
UIStyle_Second = 1001,
UIText_Second = "1",
UIOffset_Second = "{X=-200, Y = 50}",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
SfxID = 3500
},
[5141] = {
StepID = 5141,
GuideID = 51016,
Comment = "引导打开“地图检测”",
IsForceGuide = true,
WindowName = "UI_UGCEditor_ControlPanel_TopLeft",
WidgetName = "UI_UGC_SwitcherButton_MapDetection",
GetButtonFunc = "GetGuideOMDMapDetectionBtn",
UIStyle_Frist = 1012,
UIText_Frist = "地图创作好了可少不了检查的环节，这就需要用到【地图检测】功能咯！我们一起来看看吧！",
UIOffset_Frist = v1,
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bMask = true,
ButtonMode = v3,
SfxID = 3504
},
[5142] = {
StepID = 5142,
GuideID = 51016,
Comment = "等待地图检测打开",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_UGCEditor_Monster_MapDetection_PopView\"}"
},
[5143] = {
StepID = 5143,
GuideID = 51016,
Comment = "NPC介绍“地图检测”",
SubStep = 3,
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "【地图检测】可以帮助造梦师快速检查当前地图存在的问题。点击前往按钮，直达问题所在地！",
UIOffset_Frist = v1,
bAnyWhereNext = true
},
[5144] = {
StepID = 5144,
GuideID = 51016,
Comment = "塔防UGC引导一阶段结束语",
SubStep = 4,
IsForceGuide = true,
UIStyle_Frist = 1012,
UIText_Frist = "现在你应该清楚塔防编辑器的功能了！快去开启属于你的星世界之旅吧！",
UIOffset_Frist = v1,
bAnyWhereNext = true,
IsKeyStep = true,
SfxID = 3502
},
[5145] = {
StepID = 5145,
GuideID = 51018,
Comment = "编辑器工具箱",
WindowName = "UI_UGCEditor_ControlPanel_TopLeft",
WidgetName = "UI_UGC_SwitcherButton_moreOperation",
GetButtonFunc = "GetHelpToolBoxBtn",
UIStyle_Frist = 1006,
UIText_Frist = "工具箱塔防新增，助力星宝自由造梦，来看看吧！",
UIOffset_Frist = "{X=0, Y =150}",
UIStyle_Third = 1002,
UIOffset_Third = v1,
UIStyle_Second = 1001,
UIText_Second = "7",
UIOffset_Second = "{X=30, Y = 50}",
bOtherButtonExit = true,
IsKeyStep = true,
ButtonMode = v3
},
[5146] = {
StepID = 5146,
GuideID = 51019,
Comment = "玩法说明介绍",
StepType = 7,
TypeParams = "{windowName=\"UI_UGCEditor_MapDescribe_MapExplain\"}",
IsKeyStep = true
},
[5150] = {
StepID = 5150,
GuideID = 51020,
Comment = "新版技能编辑器-引导打开“帮助”入口",
IsForceGuide = true,
WindowName = "UI_UGCEditor_skillEdit",
WidgetName = "w_btn_Help",
GetButtonFunc = "GetSkillEditorGuideBtn",
UIStyle_Frist = 1012,
UIText_Frist = "欢迎进入技能编辑器！在开始创作技能之前，先跟我学习一些必要的小技巧吧！",
UIOffset_Frist = v1,
UIStyle_Third = 1002,
UIOffset_Third = "{X=0, Y = -5}",
UIStyle_Second = 1001,
UIText_Second = "0",
UIOffset_Second = "{X=-55, Y = 50}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3,
SfxID = 3500
}
}

local mt = {
SubStep = 1,
StepType = 1,
IsForceGuide = false,
bAnyWhereNext = false,
bOtherButtonExit = false,
bMask = false,
bShowSkipButton = false,
IsKeyStep = false,
bTriggerOnlyUIShowOnTop = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data