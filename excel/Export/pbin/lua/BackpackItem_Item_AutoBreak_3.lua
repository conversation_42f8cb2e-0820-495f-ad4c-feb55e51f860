--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表.xlsx: 道具

local v0 = 1

local v1 = "商店兑换"

local v2 = {
{
itemId = 6,
itemNum = 10
}
}

local v3 = {
306
}

local data = {
[181886] = {
id = 181886,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "高顶礼帽",
desc = "可在【挑战基德】活动中兑换奖励",
icon = "CDN:T_Common_Item_System_Bag",
picture = "CDN:T_Common_Item_System_Bag",
getWay = "活动",
bagId = 1,
bHideInBag = true
},
[200178] = {
id = 200178,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999999,
maxNum = 999999999,
quality = 2,
name = "狼人月卡",
desc = "使用后，谁是狼人月卡有效时间增加30天。",
icon = "T_Common_Item_System_WerewolfMonthCard_001",
picture = "T_Common_Item_System_WerewolfMonthCard_001",
getWay = "商城",
useType = "IUTO_NR3E3MonthCard",
useParam = {
30
}
},
[200179] = {
id = 200179,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 2,
name = "狼人月卡1日体验",
desc = "使用后，谁是狼人月卡有效时间增加1天。体验月卡不会赠送身份体验礼包和月卡专属礼包。",
icon = "T_Common_Item_System_WerewolfMonthCard_002",
picture = "T_Common_Item_System_WerewolfMonthCard_002",
getWay = "活动",
useType = "IUTO_NR3E3MonthCard",
useParam = {
1
}
},
[200180] = {
id = 200180,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 2,
name = "狼人月卡3日体验",
desc = "使用后，谁是狼人月卡有效时间增加3天。体验月卡不会赠送身份体验礼包和月卡专属礼包。",
icon = "T_Common_Item_System_WerewolfMonthCard03_002",
picture = "T_Common_Item_System_WerewolfMonthCard03_002",
getWay = "活动",
useType = "IUTO_NR3E3MonthCard",
useParam = {
3
}
},
[200181] = {
id = 200181,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 2,
name = "狼人月卡7日体验",
desc = "使用后，谁是狼人月卡有效时间增加7天。体验月卡不会赠送身份体验礼包和月卡专属礼包。",
icon = "T_Common_Item_System_WerewolfMonthCard07_002",
picture = "T_Common_Item_System_WerewolfMonthCard07_002",
getWay = "活动",
useType = "IUTO_NR3E3MonthCard",
useParam = {
7
}
},
[200182] = {
id = 200182,
effect = true,
stackedNum = 999999999,
maxNum = 999999999,
quality = 2,
name = "狼人月卡15日体验",
desc = "使用后，谁是狼人月卡有效时间增加15天。体验月卡不会赠送身份体验礼包和月卡专属礼包。",
icon = "T_Common_Item_System_WerewolfMonthCard15_002",
picture = "T_Common_Item_System_WerewolfMonthCard15_002",
getWay = "活动",
useType = "IUTO_NR3E3MonthCard",
useParam = {
15
}
},
[201021] = {
id = 201021,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 1,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "峡谷S12赛季高级通行证",
desc = "购买后立即获得峡谷S12赛季高级通行证",
icon = "T_Common_Icon_BattlePass",
picture = "T_Common_Icon_BattlePass",
bagId = 1,
useType = "IUTO_GENERAL_BP",
useParam = {
3,
3006,
1
},
bHideInBag = true,
isShowPopRewardView = 0
},
[201022] = {
id = 201022,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 1,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "峡谷S12赛季豪华通行证",
desc = "购买后立即获得峡谷S12赛季豪华通行证",
icon = "T_Common_Icon_BattlePassLuxury",
picture = "T_Common_Icon_BattlePassLuxury",
bagId = 1,
useType = "IUTO_GENERAL_BP",
useParam = {
3,
3006,
2
},
bHideInBag = true,
isShowPopRewardView = 0
},
[201086] = {
id = 201086,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "峡谷高级通行证",
desc = "购买后立即将峡谷高级通行证升阶为峡谷豪华通行证",
icon = "CDN:T_Common_Item_System_Bag_103",
picture = "CDN:T_Common_Item_System_Bag_103",
bagId = 1,
useType = "IUTO_GENERAL_BP",
useParam = {
3,
3006,
2
},
bHideInBag = true,
isShowPopRewardView = 0
},
[201087] = {
id = 201087,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "狼人豪华通行证",
desc = "购买后立即将狼人高级通行证升阶为豪华通行证",
icon = "CDN:T_Common_Item_System_Bag_103",
picture = "CDN:T_Common_Item_System_Bag_103",
bagId = 1,
useType = "IUTO_GENERAL_BP",
useParam = {
2,
2008,
1,
2
},
bHideInBag = true,
isShowPopRewardView = 0
},
[201088] = {
id = 201088,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "狼人豪华通行证",
desc = "购买后立即将狼人高级通行证升阶为豪华通行证",
icon = "CDN:T_Common_Item_System_Bag_103",
picture = "CDN:T_Common_Item_System_Bag_103",
bagId = 1,
useType = "IUTO_GENERAL_BP",
useParam = {
2,
2007,
1,
2
},
bHideInBag = true,
isShowPopRewardView = 0
},
[202006] = {
id = 202006,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S11闪耀辉煌",
desc = "获得后，可激活【闪电赛奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "CDN:T_Common_Item_System_Bag_079",
picture = "CDN:T_Common_Item_System_Bag_079",
getWay = "参加闪电赛活动，积累闪电奖章",
bagId = 1,
bHideInBag = true
},
[210000] = {
id = 210000,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "星宝会员卡(30天)",
desc = "获得后立即获得星宝会员卡的购买奖励，并自动激活30天的星宝会员权益",
icon = "T_Common_Item_System_Bag_014",
picture = "T_Common_Item_System_BagBig_014",
bagId = 1,
useType = "IUTO_MonthCard",
useParam = {
1
}
},
[201207] = {
id = 201207,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "赛季祈愿幸运卡",
desc = [[1.可定向自选祈愿中获得的非凡时装
2.随机减少外观的保底祈愿次数3~10次
影响范围：非凡及臻藏时装、臻藏配饰的保底祈愿次数]],
icon = "CDN:T_Common_Item_ValuePreorder_Bag_02",
picture = "CDN:T_Common_Item_ValuePreorder_Bag_02",
bagId = 1,
useType = "IUTO_RaffleBenefitCard",
useParam = {
3001301,
30,
3001302,
30,
3001303,
30,
3001304,
30,
3001305,
30,
3001306,
30,
3001307,
5,
3001308,
10,
3001309,
5
},
bHideInBag = true
},
[200760] = {
id = 200760,
effect = true,
type = "ItemType_GongyiMedal",
stackedNum = 9999,
maxNum = 99999,
name = "星宝反诈大使勋章",
desc = "腾讯公益“星宝反诈大使”勋章，可前往腾讯公益查看",
icon = "T_PublicWelfare_Icon_03",
getWay = "活动"
},
[200761] = {
id = 200761,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12经典模式奖杯",
desc = "获得后，可激活【经典模式奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_09",
picture = "Icon_Cup_09",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200762] = {
id = 200762,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12谁是狼人奖杯",
desc = "获得后，可激活【谁是狼人奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_05",
picture = "Icon_Cup_05",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200763] = {
id = 200763,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12暗星奖杯",
desc = "获得后，可激活【暗星奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_01",
picture = "Icon_Cup_01",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200764] = {
id = 200764,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12极速飞车奖杯",
desc = "获得后，可激活【极速飞车奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_04",
picture = "Icon_Cup_04",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200765] = {
id = 200765,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12大乱斗奖杯",
desc = "获得后，可激活【大乱斗奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_02",
picture = "Icon_Cup_02",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200766] = {
id = 200766,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12武器大师奖杯",
desc = "获得后，可激活【武器大师奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_07",
picture = "Icon_Cup_07",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200767] = {
id = 200767,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12躲猫猫奖杯",
desc = "获得后，可激活【躲猫猫奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_03",
picture = "Icon_Cup_03",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200768] = {
id = 200768,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12梦幻岛奖杯",
desc = "获得后，可激活【突围梦幻岛奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_06",
picture = "Icon_Cup_06",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200769] = {
id = 200769,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12星宝奖杯",
desc = "获得后，可激活【星宝奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_08",
picture = "Icon_Cup_08",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200770] = {
id = 200770,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12峡谷奖杯",
desc = "获得后，可激活【峡谷奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_12",
picture = "Icon_Cup_12",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[202007] = {
id = 202007,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S12闪耀辉煌",
desc = "获得后，可激活【闪电赛奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "CDN:T_Common_Item_System_Bag_079",
picture = "CDN:T_Common_Item_System_Bag_079",
getWay = "参加闪电赛活动，积累闪电奖章",
bagId = 1,
bHideInBag = true
},
[202008] = {
id = 202008,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13闪耀辉煌",
desc = "获得后，可激活【闪电赛奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "CDN:T_Common_Item_System_Bag_079",
picture = "CDN:T_Common_Item_System_Bag_079",
getWay = "参加闪电赛活动，积累闪电奖章",
bagId = 1,
bHideInBag = true
},
[200780] = {
id = 200780,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 9999,
name = "元梦周边-紫萝萝痛包",
desc = "可能获得以下奖励之一：腾讯视频季卡兑换券、元梦周边套装兑换券",
icon = "CDN:T_Common_Item_System_Bag_FivePieceBox",
picture = "CDN:T_Common_Item_System_Bag_FivePieceBox",
getWay = "携友同行",
bagId = 1,
useType = "IUTO_AMS",
useParam = {
15
}
},
[200800] = {
id = 200800,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "喵喵相机",
desc = "用于在喵喵相册礼活动中抽取福签",
icon = "CDN:T_Common_Icon_Coin_47",
picture = "CDN:T_Common_Icon_Coin_47",
getWay = "完成喵喵相册礼活动任务",
bagId = 1,
bHideInBag = true
},
[200801] = {
id = 200801,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "紫萝萝相册",
desc = "紫萝萝相册：集齐全部相册得喵喵节限定好礼",
icon = "CDN:T_ActivityStarBaby_Icon_Card01Normal",
picture = "CDN:T_ActivityStarBaby_Icon_Card01Normal",
getWay = "使用喵喵相机抽取",
bagId = 1,
bHideInBag = true
},
[200802] = {
id = 200802,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "喵小困相册",
desc = "喵小困相册：集齐全部相册得喵喵节限定好礼",
icon = "CDN:T_ActivityStarBaby_Icon_Card02Normal",
picture = "CDN:T_ActivityStarBaby_Icon_Card02Normal",
getWay = "使用喵喵相机抽取",
bagId = 1,
bHideInBag = true
},
[200803] = {
id = 200803,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "虎子哥相册",
desc = "虎子哥相册：集齐全部相册得喵喵节限定好礼",
icon = "CDN:T_ActivityStarBaby_Icon_Card03Normal",
picture = "CDN:T_ActivityStarBaby_Icon_Card03Normal",
getWay = "使用喵喵相机抽取",
bagId = 1,
bHideInBag = true
},
[200804] = {
id = 200804,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "墨妙妙相册",
desc = "墨妙妙相册：集齐全部相册得喵喵节限定好礼",
icon = "CDN:T_ActivityStarBaby_Icon_Card04Normal",
picture = "CDN:T_ActivityStarBaby_Icon_Card04Normal",
getWay = "使用喵喵相机抽取",
bagId = 1,
bHideInBag = true
},
[200805] = {
id = 200805,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "狮威威相册",
desc = "墨妙妙相册：集齐全部相册得喵喵节限定好礼",
icon = "CDN:T_ActivityStarBaby_Icon_Card05Normal",
picture = "CDN:T_ActivityStarBaby_Icon_Card05Normal",
getWay = "使用喵喵相机抽取",
bagId = 1,
bHideInBag = true
},
[200806] = {
id = 200806,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "虎小妞相册",
desc = "虎小妞相册：集齐全部相册得喵喵节限定好礼",
icon = "CDN:T_ActivityStarBaby_Icon_Card06Normal",
picture = "CDN:T_ActivityStarBaby_Icon_Card06Normal",
getWay = "使用喵喵相机抽取",
bagId = 1,
bHideInBag = true
},
[200807] = {
id = 200807,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "喵小萌相册",
desc = "喵小萌相册：集齐全部相册得喵喵节限定好礼",
icon = "CDN:T_ActivityStarBaby_Icon_Card07Normal",
picture = "CDN:T_ActivityStarBaby_Icon_Card07Normal",
getWay = "使用喵喵相机抽取",
bagId = 1,
bHideInBag = true
},
[200808] = {
id = 200808,
effect = true,
stackedNum = 9999,
name = "紫萝萝痛包兑换券",
desc = "获得紫萝萝痛包兑换券的星宝，我们将在7个工作日内发送游戏邮件，届时请前往邮箱中填写收件信息！",
icon = "CDN:T_Common_Item_System_StyleBox11",
picture = "CDN:T_Common_Item_System_StyleBox11",
getWay = "携友同行",
bagId = 1
},
[200810] = {
id = 200810,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "腾讯视频年卡兑换券",
desc = "获得腾讯视频会员的星宝，我们将在7个工作日内发送游戏邮件，届时请前往邮箱中查收并领奖！",
icon = "CDN:T_Common_Item_System_Bag_Video_06",
picture = "CDN:T_Common_Item_System_Bag_Video_06",
getWay = "活动获得",
bagId = 1
},
[200811] = {
id = 200811,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "腾讯视频月卡兑换券",
desc = "获得腾讯视频会员的星宝，我们将在7个工作日内发送游戏邮件，届时请前往邮箱中查收并领奖！",
icon = "T_Common_Item_System_Bag_Video_03",
picture = "T_Common_Item_System_Bag_Video_03",
getWay = "活动获得",
bagId = 1
},
[200812] = {
id = 200812,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "腾讯视频双周兑换券",
desc = "获得腾讯视频会员的星宝，我们将在7个工作日内发送游戏邮件，届时请前往邮箱中查收并领奖！",
icon = "CDN:T_Common_Item_System_Bag_063",
picture = "CDN:T_Common_Item_System_Bag_063",
getWay = "活动获得",
bagId = 1
},
[200813] = {
id = 200813,
effect = true,
stackedNum = 9999,
maxNum = 9999,
name = "腾讯视频季卡兑换券",
desc = "获得腾讯视频会员的星宝，我们将在7个工作日内发送游戏邮件，届时请前往邮箱中查收并领奖！",
icon = "CDN:T_Common_Item_System_Bag_Video_05",
picture = "CDN:T_Common_Item_System_Bag_Video_05",
getWay = "活动获得",
bagId = 1
},
[200814] = {
id = 200814,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 9999,
maxNum = 9999,
quality = 2,
name = "天天领权益卡",
desc = "未开启高级权益时，进入天天领页面将会自动使用一张并开启一周权益",
icon = "CDN:T_Common_Item_System_Bag_138",
picture = "CDN:T_Common_Item_System_Bag_138",
getWay = "活动获得",
bagId = 1
},
[201208] = {
id = 201208,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "赛季祈愿幸运卡",
desc = [[1.可定向自选祈愿中获得的非凡时装
2.随机减少外观的保底祈愿次数3~10次
影响范围：非凡及臻藏时装、臻藏配饰的保底祈愿次数]],
icon = "CDN:T_Common_Item_ValuePreorder_Bag_02",
picture = "CDN:T_Common_Item_ValuePreorder_Bag_02",
bagId = 1,
useType = "IUTO_RaffleBenefitCard",
useParam = {
3001401,
30,
3001402,
30,
3001403,
30,
3001404,
30,
3001405,
30,
3001406,
30,
3001407,
5,
3001408,
10,
3001409,
5
},
bHideInBag = true
},
[200816] = {
id = 200816,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13经典模式奖杯",
desc = "获得后，可激活【经典模式奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_09",
picture = "Icon_Cup_09",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200817] = {
id = 200817,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13谁是狼人奖杯",
desc = "获得后，可激活【谁是狼人奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_05",
picture = "Icon_Cup_05",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200818] = {
id = 200818,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13暗星奖杯",
desc = "获得后，可激活【暗星奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_01",
picture = "Icon_Cup_01",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200819] = {
id = 200819,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13极速飞车奖杯",
desc = "获得后，可激活【极速飞车奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_04",
picture = "Icon_Cup_04",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200820] = {
id = 200820,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13大乱斗奖杯",
desc = "获得后，可激活【大乱斗奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_02",
picture = "Icon_Cup_02",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200821] = {
id = 200821,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13武器大师奖杯",
desc = "获得后，可激活【武器大师奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_07",
picture = "Icon_Cup_07",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200822] = {
id = 200822,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13躲猫猫奖杯",
desc = "获得后，可激活【躲猫猫奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_03",
picture = "Icon_Cup_03",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200823] = {
id = 200823,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13梦幻岛奖杯",
desc = "获得后，可激活【突围梦幻岛奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_06",
picture = "Icon_Cup_06",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200824] = {
id = 200824,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13星宝奖杯",
desc = "获得后，可激活【星宝奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_08",
picture = "Icon_Cup_08",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200825] = {
id = 200825,
effect = true,
stackedNum = 1,
exceedReplaceItem = v2,
name = "S13峡谷奖杯",
desc = "获得后，可激活【峡谷奖杯】摆件，用来装扮星家园或在星世界中进行自由创建的元件。",
icon = "Icon_Cup_12",
picture = "Icon_Cup_12",
getWay = v1,
jumpId = v3,
bagId = 1,
bHideInBag = true
},
[200826] = {
id = 200826,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 1,
exceedReplaceItem = v2,
name = "娱米积分",
desc = "获得后，补充对应活动的积分。",
icon = "Icon_Cup_12",
picture = "Icon_Cup_12",
getWay = v1,
bagId = 1,
useType = "IUTO_AMS_YumiScore",
bHideInBag = true
},
[200828] = {
id = 200828,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 9999,
maxNum = 9999,
quality = 2,
name = "峡谷天天领权益卡",
desc = "未开启高级权益时，进入峡谷天天领页面将会自动使用一张并开启一周权益",
icon = "CDN:CT_Arena_Item_DailyGift_VipCard",
picture = "CDN:CT_Arena_Item_DailyGift_VipCard",
getWay = "活动获得",
bagId = 1
},
[200829] = {
id = 200829,
effect = true,
type = "ItemType_FarmChest",
maxNum = 999999999,
quality = 2,
name = "农神宝箱",
desc = "打开农场宝箱，可获得根据农场小屋等级对应数量的农场币",
icon = "CDN:T_Common_Item_System_Bag_114",
picture = "CDN:T_Common_Item_System_Bag_114",
getWay = "奖杯征程",
useType = "IUTO_FarmChest",
useParam = {
218000,
10000
},
bHideInBag = true
},
[200830] = {
id = 200830,
effect = true,
type = "ItemType_AutoUse",
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "天穹登录礼",
desc = "购买后立即激活天穹登录礼",
picture = "T_Common_Icon_BattlePass",
bagId = 1,
bHideInBag = true,
isShowPopRewardView = 0
},
[200835] = {
id = 200835,
effect = true,
stackedNum = 999999,
maxNum = 999999,
exceedReplaceItem = v2,
quality = 2,
name = "快闪季砖头",
desc = "获得后，可前往星梦广场，寻找快闪季施工队对话兑换福利",
icon = "CDN:T_Common_Item_Dye_01",
picture = "CDN:T_Common_Item_DyeBig_01",
getWay = "活动获得",
bagId = 1
}
}

local mt = {
effect = false,
type = "ItemType_Common",
maxNum = 1,
quality = 1,
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data