--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/C_充值配置表.xlsx: 充值任务

local v0 = 8

local v1 = {
beginTime = 1
}

local data = {
[500321] = {
id = 500321,
name = "拥有6个装扮中的6个",
desc = "拥有6个装扮中的6个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 6,
subConditionList = {
{
type = 3,
value = {
410050,
410040,
630489,
610319,
620734,
640132
}
}
}
}
}
}
},
reward = {
itemIdList = {
850614
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50279
},
[450018] = {
id = 450018,
name = "收集幽灵巫师和收获日",
desc = "收集幽灵巫师和收获日",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
219302,
219300
}
}
}
}
}
}
},
reward = {
itemIdList = {
840265
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45003
},
[500323] = {
id = 500323,
name = "收集两个躲猫猫搜捕者武器",
desc = "收集两个躲猫猫搜捕者武器",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
241406,
241407
}
}
}
}
}
}
},
reward = {
itemIdList = {
410490
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50281
},
[500324] = {
id = 500324,
name = "任意途径充值满6元",
desc = "任意途径充值满6元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 6,
subConditionList = {
{
type = 254,
value = {
2025031400,
2025050200
}
}
}
}
}
}
},
reward = {
itemIdList = {
630382
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50015
},
[500325] = {
id = 500325,
name = "任意途径充值满30元",
desc = "任意途径充值满30元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 30,
subConditionList = {
{
type = 254,
value = {
2025031400,
2025050200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214
},
numList = {
6
}
},
jumpId = 8,
taskGroupId = 50015
},
[500326] = {
id = 500326,
name = "任意途径充值满98元",
desc = "任意途径充值满98元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 98,
subConditionList = {
{
type = 254,
value = {
2025031400,
2025050200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
721039
},
numList = {
12,
1
}
},
jumpId = 8,
taskGroupId = 50015
},
[500327] = {
id = 500327,
name = "任意途径充值满198元",
desc = "任意途径充值满198元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 198,
subConditionList = {
{
type = 254,
value = {
2025031400,
2025050200
}
}
}
}
}
}
},
reward = {
itemIdList = {
860185,
860186
},
numList = {
1,
1
}
},
jumpId = 8,
taskGroupId = 50015
},
[500328] = {
id = 500328,
name = "任意途径充值满368元",
desc = "任意途径充值满368元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 368,
subConditionList = {
{
type = 254,
value = {
2025031400,
2025050200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
840264
},
numList = {
30,
1
}
},
jumpId = 8,
taskGroupId = 50015
},
[500329] = {
id = 500329,
name = "任意途径充值满688元",
desc = "任意途径充值满688元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 688,
subConditionList = {
{
type = 254,
value = {
2025031400,
2025050200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
850619
},
numList = {
60,
1
}
},
jumpId = 8,
taskGroupId = 50015
},
[500330] = {
id = 500330,
name = "任意途径充值满1088元",
desc = "任意途径充值满1088元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 1088,
subConditionList = {
{
type = 254,
value = {
2025031400,
2025050200
}
}
}
}
}
}
},
reward = {
itemIdList = {
845016
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50015
},
[450019] = {
id = 450019,
name = "拥有4个农场装饰中的1个",
desc = "拥有4个农场装饰中的1个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
218171,
218172,
218173,
218174
}
}
}
}
}
}
},
reward = {
itemIdList = {
870043
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45004
},
[450020] = {
id = 450020,
name = "拥有4个农场装饰中的2个",
desc = "拥有4个农场装饰中的2个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
218171,
218172,
218173,
218174
}
}
}
}
}
}
},
reward = {
itemIdList = {
820173
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45004
},
[450021] = {
id = 450021,
name = "拥有4个农场装饰中的3个",
desc = "拥有4个农场装饰中的3个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 3,
subConditionList = {
{
type = 3,
value = {
218171,
218172,
218173,
218174
}
}
}
}
}
}
},
reward = {
itemIdList = {
840268
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45004
},
[450022] = {
id = 450022,
name = "拥有4个农场装饰中的4个",
desc = "拥有4个农场装饰中的4个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 4,
subConditionList = {
{
type = 3,
value = {
218171,
218172,
218173,
218174
}
}
}
}
}
}
},
reward = {
itemIdList = {
620827
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45004
},
[500331] = {
id = 500331,
name = "累计登录1天",
desc = "累计登录1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 50282
},
[500332] = {
id = 500332,
name = "累计登录2天",
desc = "累计登录2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200014
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50282
},
[500333] = {
id = 500333,
name = "累计登录3天",
desc = "累计登录3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
725002
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50282
},
[500334] = {
id = 500334,
name = "累计登录4天",
desc = "累计登录4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 4
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
6
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 50282
},
[500335] = {
id = 500335,
name = "累计登录5天",
desc = "累计登录5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 5
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200104
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50282
},
[500336] = {
id = 500336,
name = "收集两个动画，额外赠送专属背饰",
desc = "收集两个动画，额外赠送专属背饰",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
240006,
240209
}
}
}
}
}
}
},
reward = {
itemIdList = {
620789
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50283
},
[500337] = {
id = 500337,
name = "累计登录1天",
desc = "充值且累计登录1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 312,
value = 1,
subConditionList = {
{
type = 146,
value = {
10052
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
218826
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50284
},
[500338] = {
id = 500338,
name = "累计登录2天",
desc = "充值且累计登录2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 312,
value = 2,
subConditionList = {
{
type = 146,
value = {
10052
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200006
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 50284
},
[500339] = {
id = 500339,
name = "累计登录3天",
desc = "充值且累计登录3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 312,
value = 3,
subConditionList = {
{
type = 146,
value = {
10052
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
1
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 50284
},
[500340] = {
id = 500340,
name = "累计登录4天",
desc = "充值且累计登录4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 312,
value = 4,
subConditionList = {
{
type = 146,
value = {
10052
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200008
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 50284
},
[500341] = {
id = 500341,
name = "累计登录4天",
desc = "充值且累计登录5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 312,
value = 5,
subConditionList = {
{
type = 146,
value = {
10052
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
3
},
numList = {
6
},
validPeriodList = {
0
}
},
taskGroupId = 50284
},
[500342] = {
id = 500342,
name = "IOS支付充值",
desc = "充值60星钻",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 58,
value = 60,
subConditionList = {
{
type = 325,
value = {
0
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
610039
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50286
},
[500343] = {
id = 500343,
name = "收集峡谷英豪2个角色",
desc = "收集峡谷英豪2个角色",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
410760,
410770
}
}
}
}
}
}
},
reward = {
itemIdList = {
310738
},
numList = {
1
}
},
taskGroupId = 50285
},
[500344] = {
id = 500344,
name = "收集1号小甜豆",
desc = "收集1号小甜豆",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
410780
}
}
}
}
}
}
},
jumpId = 10598,
taskGroupId = 50287
},
[500345] = {
id = 500345,
name = "收集2号小甜豆",
desc = "收集2号小甜豆",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
410670
}
}
}
}
}
}
},
jumpId = 10599,
taskGroupId = 50287
},
[500346] = {
id = 500346,
name = "收集3号小甜豆",
desc = "收集3号小甜豆",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
403620
}
}
}
}
}
}
},
jumpId = 10600,
taskGroupId = 50287
},
[500347] = {
id = 500347,
name = "收集4号小甜豆",
desc = "收集4号小甜豆",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
403630
}
}
}
}
}
}
},
jumpId = 10601,
taskGroupId = 50287
},
[500348] = {
id = 500348,
name = "收集5号小甜豆",
desc = "收集5号小甜豆",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
401950
}
}
}
}
}
}
},
jumpId = 10602,
taskGroupId = 50287
},
[500349] = {
id = 500349,
name = "收集6号小甜豆",
desc = "收集6号小甜豆",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
401960
}
}
}
}
}
}
},
jumpId = 10603,
taskGroupId = 50287
},
[500350] = {
id = 500350,
name = "收集6个小甜豆中的6个",
desc = "收集6个小甜豆中的6个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 6,
subConditionList = {
{
type = 3,
value = {
410670,
410780,
403630,
403620,
401950,
401960
}
}
}
}
}
}
},
reward = {
itemIdList = {
410720
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50287
},
[500351] = {
id = 500351,
name = "集齐两个动画，送专属背饰",
desc = "集齐两个动画，送专属背饰",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
240015,
240215
}
}
}
}
}
}
},
reward = {
itemIdList = {
620824
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50288
},
[500352] = {
id = 500352,
name = "收集泡泡玛特2人组",
desc = "收集泡泡玛特2人组",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
401950,
401960
}
}
}
}
}
}
},
reward = {
itemIdList = {
840103
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50289
},
[500353] = {
id = 500353,
name = "收集泡泡玛特2人组",
desc = "收集泡泡玛特2人组",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
403630,
403620
}
}
}
}
}
}
},
reward = {
itemIdList = {
840281
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50289
},
[450051] = {
id = 450051,
name = "翡光仙灵层级指示",
desc = "收集玫瑰/蒸蒸日上小窝/翡光仙灵庭和牛马发箍",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
610369,
218176,
218184,
630573
}
}
}
}
}
}
},
taskGroupId = 45005
},
[450071] = {
id = 450071,
name = "第一层",
desc = "收集寿司旅行家/猪猪包礼盒/煎饼果子超人和快乐菠萝",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
620852,
218179,
218177,
620855
}
}
}
}
}
}
},
taskGroupId = 45007
},
[500354] = {
id = 500354,
name = "任意途径充值6次",
desc = "任意途径充值6次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 25,
value = 6,
subConditionList = {
{
type = 274,
value = {
1
}
},
{
type = 276,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
402800
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50290
},
[500356] = {
id = 500356,
name = "收集泡泡玛特2人组",
desc = "收集泡泡玛特2人组",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
410670,
410780
}
}
}
}
}
}
},
reward = {
itemIdList = {
840280
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50289
},
[450081] = {
id = 450081,
name = "收集蜜桃猫星星杯和蜜桃猫星礼盒",
desc = "收集蜜桃猫星星杯和蜜桃猫星礼盒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
218185,
218186
}
}
}
}
}
}
},
reward = {
itemIdList = {
840282
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45008
},
[450082] = {
id = 450082,
name = "拥有4个农场装饰中的1个",
desc = "拥有4个农场装饰中的1个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 1,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
870046
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45009
},
[450083] = {
id = 450083,
name = "拥有4个农场装饰中的2个",
desc = "拥有4个农场装饰中的2个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 2,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
820180
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45009
},
[450084] = {
id = 450084,
name = "拥有4个农场装饰中的3个",
desc = "拥有4个农场装饰中的3个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 3,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
840283
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45009
},
[450085] = {
id = 450085,
name = "拥有4个农场装饰中的4个",
desc = "拥有4个农场装饰中的4个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 4,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
620861
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45009
},
[450061] = {
id = 450061,
name = "拥有布丁狗小窝",
desc = "拥有布丁狗小窝",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 1,
subConditionList = {
{
type = 3,
value = {
218193
}
}
}
}
}
}
},
reward = {
itemIdList = {
3900
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 45006
},
[450062] = {
id = 450062,
name = "拥有奶油云朵乐园",
desc = "拥有奶油云朵乐园",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 1,
subConditionList = {
{
type = 3,
value = {
218169
}
}
}
}
}
}
},
reward = {
itemIdList = {
850651,
3900
},
numList = {
1,
20
},
validPeriodList = {
0,
0
}
},
taskGroupId = 45006
},
[450063] = {
id = 450063,
name = "拥有蜜糖喵巡游站和奶油云朵乐园",
desc = "拥有蜜糖喵巡游站和奶油云朵乐园",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 2,
subConditionList = {
{
type = 3,
value = {
218168,
218169
}
}
}
}
}
}
},
reward = {
itemIdList = {
3900
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 45006
},
[500357] = {
id = 500357,
name = "收集两个躲猫猫搜捕者武器",
desc = "收集两个躲猫猫搜捕者武器",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
241409,
241408
}
}
}
}
}
}
},
reward = {
itemIdList = {
620835
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50291
},
[500358] = {
id = 500358,
name = "收集两个动画，额外赠送专属头饰",
desc = "收集两个动画，额外赠送专属头饰",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
240008,
240210
}
}
}
}
}
}
},
reward = {
itemIdList = {
630591
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50292
},
[500359] = {
id = 500359,
name = "立即获得",
desc = "立即获得",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 229,
value = 1,
subConditionList = {
{
type = 194,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
740005
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50293
},
[500360] = {
id = 500360,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 229,
value = 1,
subConditionList = {
{
type = 194,
value = {
1
}
}
}
},
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
1
},
numList = {
400
},
validPeriodList = {
0
}
},
taskGroupId = 50294
},
[500361] = {
id = 500361,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 229,
value = 1,
subConditionList = {
{
type = 194,
value = {
1
}
}
}
},
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
1
},
numList = {
400
},
validPeriodList = {
0
}
},
taskGroupId = 50294
},
[500362] = {
id = 500362,
name = "第3天",
desc = "第3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 229,
value = 1,
subConditionList = {
{
type = 194,
value = {
1
}
}
}
},
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
1
},
numList = {
480
},
validPeriodList = {
0
}
},
taskGroupId = 50294
},
[500363] = {
id = 500363,
name = "画家卡池进入奖励",
desc = "画家卡池进入奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1
}
}
}
},
reward = {
itemIdList = {
3556
},
numList = {
280
},
validPeriodList = {
0
}
},
taskGroupId = 50295
},
[500364] = {
id = 500364,
name = "任意途径充值满6元",
desc = "任意途径充值满6元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 6,
subConditionList = {
{
type = 254,
value = {
2025050200,
2025062700
}
}
}
}
}
}
},
reward = {
itemIdList = {
620446
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50016
},
[500365] = {
id = 500365,
name = "任意途径充值满30元",
desc = "任意途径充值满30元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 30,
subConditionList = {
{
type = 254,
value = {
2025050200,
2025062700
}
}
}
}
}
}
},
reward = {
itemIdList = {
214
},
numList = {
6
}
},
jumpId = 8,
taskGroupId = 50016
},
[500366] = {
id = 500366,
name = "任意途径充值满98元",
desc = "任意途径充值满98元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 98,
subConditionList = {
{
type = 254,
value = {
2025050200,
2025062700
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
721054
},
numList = {
12,
1
}
},
jumpId = 8,
taskGroupId = 50016
},
[500367] = {
id = 500367,
name = "任意途径充值满198元",
desc = "任意途径充值满198元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 198,
subConditionList = {
{
type = 254,
value = {
2025050200,
2025062700
}
}
}
}
}
}
},
reward = {
itemIdList = {
860201,
860202
},
numList = {
1,
1
}
},
jumpId = 8,
taskGroupId = 50016
},
[500368] = {
id = 500368,
name = "任意途径充值满368元",
desc = "任意途径充值满368元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 368,
subConditionList = {
{
type = 254,
value = {
2025050200,
2025062700
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
840288
},
numList = {
30,
1
}
},
jumpId = 8,
taskGroupId = 50016
},
[500369] = {
id = 500369,
name = "任意途径充值满688元",
desc = "任意途径充值满688元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 688,
subConditionList = {
{
type = 254,
value = {
2025050200,
2025062700
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
850646
},
numList = {
60,
1
}
},
jumpId = 8,
taskGroupId = 50016
},
[500370] = {
id = 500370,
name = "任意途径充值满1088元",
desc = "任意途径充值满1088元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 1088,
subConditionList = {
{
type = 254,
value = {
2025050200,
2025062700
}
}
}
}
}
}
},
reward = {
itemIdList = {
845017
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50016
},
[500371] = {
id = 500371,
name = "收集卓大王时装",
desc = "收集卓大王时装",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
410680,
410790
}
}
}
}
}
}
},
reward = {
itemIdList = {
840306
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50296
},
[450101] = {
id = 450101,
name = "怒海狂鲨层级指示",
desc = "收集永恒誓言/云晶花语时钟/怒海鲨王号和乌云绵绵",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
620946,
218206,
218200,
630653
}
}
}
}
}
}
},
taskGroupId = 45010
},
[500372] = {
id = 500372,
name = "任意途径充值满30元",
desc = "任意途径充值满30元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 30,
subConditionList = {
{
type = 254,
value = {
2025053000,
2025061300
}
}
}
}
}
}
},
reward = {
itemIdList = {
630566
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50298
},
[500373] = {
id = 500373,
name = "任意途径充值满98元",
desc = "任意途径充值满98元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 98,
subConditionList = {
{
type = 254,
value = {
2025053000,
2025061300
}
}
}
}
}
}
},
reward = {
itemIdList = {
200103
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50298
},
[500374] = {
id = 500374,
name = "任意途径充值满198元",
desc = "任意途径充值满198元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 198,
subConditionList = {
{
type = 254,
value = {
2025053000,
2025061300
}
}
}
}
}
}
},
reward = {
itemIdList = {
610362
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50298
},
[500375] = {
id = 500375,
name = "任意途径充值满328元",
desc = "任意途径充值满328元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 328,
subConditionList = {
{
type = 254,
value = {
2025053000,
2025061300
}
}
}
}
}
}
},
reward = {
itemIdList = {
740005
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50298
},
[500376] = {
id = 500376,
name = "任意途径充值满648元",
desc = "任意途径充值满648元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 648,
subConditionList = {
{
type = 254,
value = {
2025053000,
2025061300
}
}
}
}
}
}
},
reward = {
itemIdList = {
650002
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50298
},
[450111] = {
id = 450111,
name = "甜心琪琪层级指示",
desc = "收集囧囧先生/甜甜杯礼盒/甜心琪琪和跃动喵喵",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
630656,
218192,
218194,
630650
}
}
}
}
}
}
},
taskGroupId = 45011
},
[500377] = {
id = 500377,
name = "DJ卡池进入奖励2",
desc = "DJ卡池进入奖励2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1
}
}
}
},
reward = {
itemIdList = {
320136
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50299
},
[450151] = {
id = 450151,
name = "拥有热浪岛屿和石纹部落",
desc = "拥有热浪岛屿和石纹部落",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 2,
subConditionList = {
{
type = 3,
value = {
218195,
218196
}
}
}
}
}
}
},
reward = {
itemIdList = {
840317
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45015
},
[450161] = {
id = 450161,
name = "拥有4个农场装饰（永久）中的1个",
desc = "拥有4个农场装饰（永久）中的1个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 1,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
870046
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45016
},
[450162] = {
id = 450162,
name = "拥有4个农场装饰（永久）中的2个",
desc = "拥有4个农场装饰（永久）中的2个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 2,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
820180
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45016
},
[450163] = {
id = 450163,
name = "拥有4个农场装饰（永久）中的3个",
desc = "拥有4个农场装饰（永久）中的3个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 3,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
840283
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45016
},
[450164] = {
id = 450164,
name = "拥有4个农场装饰（永久）中的4个",
desc = "拥有4个农场装饰（永久）中的4个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 326,
value = 4,
subConditionList = {
{
type = 3,
value = {
218168,
218188,
218189,
218190
}
}
}
}
}
}
},
reward = {
itemIdList = {
620861
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45016
},
[450171] = {
id = 450171,
name = "累计登录1天",
desc = "累计登录1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 45017
},
[450172] = {
id = 450172,
name = "累计登录2天",
desc = "累计登录2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200014
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45017
},
[450173] = {
id = 450173,
name = "累计登录3天",
desc = "累计登录3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
725002
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45017
},
[450174] = {
id = 450174,
name = "累计登录4天",
desc = "累计登录4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 4
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
6
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 45017
},
[450175] = {
id = 450175,
name = "累计登录5天",
desc = "累计登录5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 5
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200104
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45017
},
[450176] = {
id = 450176,
name = "累计登录1天",
desc = "充值且累计登录1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
},
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
200830
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
218826
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 45018
},
[450177] = {
id = 450177,
name = "累计登录2天",
desc = "充值且累计登录2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
},
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
200830
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200006
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 45018
},
[450178] = {
id = 450178,
name = "累计登录3天",
desc = "充值且累计登录3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
},
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
200830
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
1
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 45018
},
[450179] = {
id = 450179,
name = "累计登录4天",
desc = "充值且累计登录4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 4
},
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
200830
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
200008
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 45018
},
[450180] = {
id = 450180,
name = "累计登录4天",
desc = "充值且累计登录5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 5
},
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
200830
}
}
}
}
}
}
},
showTime = v1,
reward = {
itemIdList = {
3
},
numList = {
6
},
validPeriodList = {
0
}
},
taskGroupId = 45018
},
[450181] = {
id = 450181,
name = "任意途径充值满6元",
desc = "任意途径充值满6元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 6,
subConditionList = {
{
type = 254,
value = {
2025062700,
2025082200
}
}
}
}
}
}
},
reward = {
itemIdList = {
630644
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50017
},
[450182] = {
id = 450182,
name = "任意途径充值满30元",
desc = "任意途径充值满30元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 30,
subConditionList = {
{
type = 254,
value = {
2025062700,
2025082200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214
},
numList = {
6
}
},
jumpId = 8,
taskGroupId = 50017
},
[450183] = {
id = 450183,
name = "任意途径充值满98元",
desc = "任意途径充值满98元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 98,
subConditionList = {
{
type = 254,
value = {
2025062700,
2025082200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
721052
},
numList = {
12,
1
}
},
jumpId = 8,
taskGroupId = 50017
},
[450184] = {
id = 450184,
name = "任意途径充值满198元",
desc = "任意途径充值满198元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 198,
subConditionList = {
{
type = 254,
value = {
2025062700,
2025082200
}
}
}
}
}
}
},
reward = {
itemIdList = {
860216,
860217
},
numList = {
1,
1
}
},
jumpId = 8,
taskGroupId = 50017
},
[450185] = {
id = 450185,
name = "任意途径充值满368元",
desc = "任意途径充值满368元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 368,
subConditionList = {
{
type = 254,
value = {
2025062700,
2025082200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
840316
},
numList = {
30,
1
}
},
jumpId = 8,
taskGroupId = 50017
},
[450186] = {
id = 450186,
name = "任意途径充值满688元",
desc = "任意途径充值满688元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 688,
subConditionList = {
{
type = 254,
value = {
2025062700,
2025082200
}
}
}
}
}
}
},
reward = {
itemIdList = {
214,
850663
},
numList = {
60,
1
}
},
jumpId = 8,
taskGroupId = 50017
},
[450187] = {
id = 450187,
name = "任意途径充值满1088元",
desc = "任意途径充值满1088元",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 267,
value = 1088,
subConditionList = {
{
type = 254,
value = {
2025062700,
2025082200
}
}
}
}
}
}
},
reward = {
itemIdList = {
845018
},
numList = {
1
}
},
jumpId = 8,
taskGroupId = 50017
},
[500378] = {
id = 500378,
name = "收集鲨鱼猫时装",
desc = "收集鲨鱼猫时装",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 2,
subConditionList = {
{
type = 3,
value = {
404950,
404960
}
}
}
}
}
}
},
reward = {
itemIdList = {
840323
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 50301
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data