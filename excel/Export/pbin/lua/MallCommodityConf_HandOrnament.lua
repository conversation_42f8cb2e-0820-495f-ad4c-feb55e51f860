--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-手部装扮

local v0 = "MCL_LifeLongLimit"

local v1 = 1

local v2 = {
seconds = 1676390400
}

local v3 = {
2,
25
}

local v4 = 200008

local v5 = 5

local v6 = 20

local data = {
[72001] = {
commodityId = 72001,
commodityName = "铃语",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 4074768000
},
endTime = {
seconds = 4074854400
},
shopTag = v3,
shopSort = 2,
minVersion = "1.2.100.1",
itemIds = {
640001
},
suitId = 40001
},
[72002] = {
commodityId = 72002,
commodityName = "绿植花洒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1715356800
},
shopTag = v3,
shopSort = 2,
jumpId = 25,
jumpText = "日常礼包",
minVersion = "1.2.100.1",
itemIds = {
640002
},
bOpenSuit = true,
suitId = 40002
},
[72003] = {
commodityId = 72003,
commodityName = "向导小旗",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 4074768000
},
endTime = {
seconds = 4074854400
},
shopTag = v3,
shopSort = 2,
minVersion = "1.2.100.1",
itemIds = {
640003
},
suitId = 40003
},
[72004] = {
commodityId = 72004,
commodityName = "九齿钉耙",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 4074768000
},
endTime = {
seconds = 4074854400
},
shopTag = v3,
shopSort = 2,
minVersion = "1.2.100.1",
itemIds = {
640004
},
suitId = 40004
},
[72005] = {
commodityId = 72005,
commodityName = "降妖宝杖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 4074768000
},
endTime = {
seconds = 4074854400
},
shopTag = v3,
shopSort = 2,
minVersion = "1.2.100.1",
itemIds = {
640005
},
suitId = 40005
},
[72006] = {
commodityId = 72006,
commodityName = "海神之戟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopTag = v3,
shopSort = 1,
jumpId = 11,
jumpText = "赛季祈愿",
minVersion = "1.3.6.1",
itemIds = {
640006
},
bOpenSuit = true,
suitId = 40006
},
[72007] = {
commodityId = 72007,
commodityName = "铃语",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1715356800
},
shopSort = 2,
minVersion = "1.2.100.1",
itemIds = {
640007
}
},
[72008] = {
commodityId = 72008,
commodityName = "铃语",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1715356800
},
shopSort = 2,
minVersion = "1.2.100.1",
itemIds = {
640008
}
},
[72009] = {
commodityId = 72009,
commodityName = "圣剑",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v3,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.33",
itemIds = {
640009
},
canGift = true,
addIntimacy = 500,
giftCoinType = 213,
giftPrice = 2,
bOpenSuit = true,
suitId = 40007
},
[72010] = {
commodityId = 72010,
commodityName = "圣剑",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
shopSort = 1,
minVersion = "1.3.68.33",
itemIds = {
640010
}
},
[72011] = {
commodityId = 72011,
commodityName = "圣剑",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
shopSort = 1,
minVersion = "1.3.68.33",
itemIds = {
640011
}
},
[72012] = {
commodityId = 72012,
commodityName = "噗噗锤",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1721664000
},
endTime = {
seconds = 1723478399
},
shopTag = v3,
shopSort = 2,
jumpId = 303,
jumpText = "噗噗登录礼",
minVersion = "1.2.100.1",
itemIds = {
640012
},
bOpenSuit = true,
suitId = 40008
},
[72013] = {
commodityId = 72013,
commodityName = "噗噗锤",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
shopSort = 2,
itemIds = {
640013
}
},
[72014] = {
commodityId = 72014,
commodityName = "噗噗锤",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
shopSort = 2,
itemIds = {
640014
}
},
[72015] = {
commodityId = 72015,
commodityName = "海神之戟",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
shopSort = 1,
minVersion = "1.3.6.1",
itemIds = {
640015
}
},
[72016] = {
commodityId = 72016,
commodityName = "海神之戟",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
shopSort = 1,
minVersion = "1.3.6.1",
itemIds = {
640016
}
},
[72017] = {
commodityId = 72017,
commodityName = "幻想星愿",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
shopTag = v3,
shopSort = 2,
jumpId = 1076,
jumpText = "三丽鸥家族",
minVersion = "1.3.7.97",
itemIds = {
640017
},
bOpenSuit = true,
suitId = 40009
},
[72019] = {
commodityId = 72019,
commodityName = "粉粉仙女棒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
shopTag = v3,
jumpId = 1075,
jumpText = "三丽鸥家族",
minVersion = "1.3.7.97",
itemIds = {
640022
},
bOpenSuit = true,
suitId = 40012
},
[72021] = {
commodityId = 72021,
commodityName = "泳池之王",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1720108800
},
shopTag = v3,
shopSort = 1,
jumpId = 8,
jumpText = "充值福利",
minVersion = "1.3.7.103",
itemIds = {
640021
},
bOpenSuit = true,
suitId = 40011
},
[72022] = {
commodityId = 72022,
commodityName = "汪汪棉花糖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v3,
shopSort = 1,
jumpId = 706,
jumpText = "赛季祈愿",
minVersion = "1.3.12.1",
itemIds = {
640025
},
bOpenSuit = true,
suitId = 40015
},
[72023] = {
commodityId = 72023,
commodityName = "汪汪棉花糖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.12.1",
itemIds = {
640026
}
},
[72024] = {
commodityId = 72024,
commodityName = "汪汪棉花糖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.12.1",
itemIds = {
640027
}
},
[72025] = {
commodityId = 72025,
commodityName = "甜心公主杖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopTag = v3,
shopSort = 1,
jumpId = 706,
jumpText = "赛季祈愿",
minVersion = "1.3.12.1",
itemIds = {
640028
},
bOpenSuit = true,
suitId = 40016
},
[72026] = {
commodityId = 72026,
commodityName = "甜心公主杖",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.12.1",
itemIds = {
640029
}
},
[72027] = {
commodityId = 72027,
commodityName = "甜心公主杖",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.12.1",
itemIds = {
640030
}
},
[72028] = {
commodityId = 72028,
commodityName = "电波光枪",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 4074768000
},
endTime = {
seconds = 4074854400
},
itemIds = {
640032
},
suitId = 40018
},
[72029] = {
commodityId = 72029,
commodityName = "电波光枪",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640033
}
},
[72030] = {
commodityId = 72030,
commodityName = "电波光枪",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640034
}
},
[72031] = {
commodityId = 72031,
commodityName = "彩莲提灯",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1730390399
},
shopTag = v3,
shopSort = 1,
jumpId = 809,
jumpText = "凤求凰祈愿",
minVersion = "1.3.12.90",
itemIds = {
640018
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 80,
bOpenSuit = true,
suitId = 40010
},
[72032] = {
commodityId = 72032,
commodityName = "彩莲提灯",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.12.90",
itemIds = {
640019
}
},
[72033] = {
commodityId = 72033,
commodityName = "彩莲提灯",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.12.90",
itemIds = {
640020
}
},
[72034] = {
commodityId = 72034,
commodityName = "凤舞千秋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730995199
},
shopTag = v3,
shopSort = 1,
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = "1.3.12.118",
itemIds = {
640031
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 12000,
bOpenSuit = true,
suitId = 40017
},
[72035] = {
commodityId = 72035,
commodityName = "墨韵千秋",
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640039
},
suitId = 40022
},
[72036] = {
commodityId = 72036,
commodityName = "墨韵千秋",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640040
}
},
[72037] = {
commodityId = 72037,
commodityName = "墨韵千秋",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640041
}
},
[72038] = {
commodityId = 72038,
commodityName = "炫彩回旋镖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopSort = 1,
itemIds = {
640024
},
suitId = 40014
},
[72039] = {
commodityId = 72039,
commodityName = "炫彩回旋镖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640042
}
},
[72040] = {
commodityId = 72040,
commodityName = "炫彩回旋镖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640043
}
},
[72041] = {
commodityId = 72041,
commodityName = "金箍棒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1727020799
},
shopTag = v3,
shopSort = 1,
jumpId = 8001,
jumpText = "西行之路祈愿",
minVersion = "1.3.18.23",
itemIds = {
640044
},
bOpenSuit = true,
suitId = 40023
},
[72042] = {
commodityId = 72042,
commodityName = "金箍棒",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.18.23",
itemIds = {
640045
}
},
[72043] = {
commodityId = 72043,
commodityName = "金箍棒",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.18.23",
itemIds = {
640046
}
},
[72044] = {
commodityId = 72044,
commodityName = "兔子灯",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744905599
},
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.78.58",
itemIds = {
640048
},
bOpenSuit = true,
suitId = 40025
},
[72045] = {
commodityId = 72045,
commodityName = "兔子灯",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640049
}
},
[72046] = {
commodityId = 72046,
commodityName = "兔子灯",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640050
}
},
[72047] = {
commodityId = 72047,
commodityName = "染青烟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.72",
itemIds = {
640053
},
bOpenSuit = true,
suitId = 40028
},
[72048] = {
commodityId = 72048,
commodityName = "染青烟",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640054
}
},
[72049] = {
commodityId = 72049,
commodityName = "染青烟",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640055
}
},
[72051] = {
commodityId = 72051,
commodityName = "掠火枪",
coinType = 1,
price = 300,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1728576000
},
endTime = {
seconds = 4095331200
},
shopTag = v3,
shopSort = 1,
minVersion = "1.2.100.1",
itemIds = {
640047
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 300,
suitId = 40024
},
[72052] = {
commodityId = 72052,
commodityName = "占星秘籍",
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640057
},
suitId = 40030
},
[72053] = {
commodityId = 72053,
commodityName = "占星秘籍",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640058
}
},
[72054] = {
commodityId = 72054,
commodityName = "占星秘籍",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640059
}
},
[72055] = {
commodityId = 72055,
commodityName = "草莓便当",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v3,
shopSort = 3,
jumpId = 10601,
jumpText = "小甜豆",
minVersion = "1.3.18.109",
itemIds = {
640056
},
bOpenSuit = true,
suitId = 40029
},
[72056] = {
commodityId = 72056,
commodityName = "洇重霄",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 1744819200
},
shopTag = v3,
shopSort = 1,
jumpId = 1082,
jumpText = "遗落珍宝",
minVersion = "1.3.18.72",
itemIds = {
640060
},
bOpenSuit = true,
suitId = 40031
},
[72057] = {
commodityId = 72057,
commodityName = "王者之剑",
coinType = 1,
price = 300,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 4096540799
},
shopTag = v3,
shopSort = 1,
minVersion = "1.2.100.1",
itemIds = {
640062
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 300,
suitId = 40033
},
[72058] = {
commodityId = 72058,
commodityName = "湮灭之锁",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1730217600
},
endTime = {
seconds = 1731859199
},
shopTag = v3,
shopSort = 1,
jumpId = 1066,
jumpText = "特色礼包",
minVersion = "1.2.100.1",
itemIds = {
640052
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 300,
bOpenSuit = true,
suitId = 40027
},
[72059] = {
commodityId = 72059,
commodityName = "小熊啵啵",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 4074768000
},
itemIds = {
640065
},
suitId = 40036
},
[72060] = {
commodityId = 72060,
commodityName = "小熊啵啵",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640066
}
},
[72061] = {
commodityId = 72061,
commodityName = "小熊啵啵",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640067
}
},
[72062] = {
commodityId = 72062,
commodityName = "宝石之杯",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v3,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
640068
},
canGift = true,
addIntimacy = 160,
giftCoinType = 211,
giftPrice = 100,
bOpenSuit = true,
suitId = 40037
},
[72063] = {
commodityId = 72063,
commodityName = "宝石之杯",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.26.71",
itemIds = {
640069
}
},
[72064] = {
commodityId = 72064,
commodityName = "宝石之杯",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.26.71",
itemIds = {
640070
}
},
[72065] = {
commodityId = 72065,
commodityName = "赤金权杖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1735660799
},
shopTag = v3,
shopSort = 1,
jumpId = 8010,
jumpText = "永恒之舞",
minVersion = "1.3.26.71",
itemIds = {
640071
},
canGift = true,
addIntimacy = 80,
giftCoinType = 211,
giftPrice = 40,
bOpenSuit = true,
suitId = 40038
},
[72066] = {
commodityId = 72066,
commodityName = "赤金权杖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.26.71",
itemIds = {
640072
}
},
[72067] = {
commodityId = 72067,
commodityName = "赤金权杖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.26.71",
itemIds = {
640073
}
},
[72068] = {
commodityId = 72068,
commodityName = "星语摩天轮",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738857599
},
shopTag = v3,
shopSort = 1,
jumpId = 628,
jumpText = "星之恋空",
minVersion = "1.3.26.93",
itemIds = {
640075
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 12000,
bOpenSuit = true,
suitId = 40040
},
[72070] = {
commodityId = 72070,
commodityName = "小蓝魔法棒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
shopTag = v3,
jumpId = 8012,
jumpText = "星缘奇境",
minVersion = "1.3.26.93",
itemIds = {
640077
},
bOpenSuit = true,
suitId = 40041
},
[72071] = {
commodityId = 72071,
commodityName = "霜之韵律",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1740671999
},
shopTag = v3,
shopSort = 1,
jumpId = 503,
jumpText = "冰雪圆舞曲",
minVersion = "1.3.37.68",
itemIds = {
640084
},
canGift = true,
addIntimacy = 160,
giftCoinType = 203,
giftPrice = 100,
bOpenSuit = true,
suitId = 40048
},
[72072] = {
commodityId = 72072,
commodityName = "霜之韵律",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640085
}
},
[72073] = {
commodityId = 72073,
commodityName = "霜之韵律",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640086
}
},
[72074] = {
commodityId = 72074,
commodityName = "甜蜜礼赞",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopTag = v3,
shopSort = 1,
jumpId = 630,
jumpText = "赛季祈愿",
minVersion = "1.3.37.1",
itemIds = {
640087
},
bOpenSuit = true,
suitId = 40049
},
[72075] = {
commodityId = 72075,
commodityName = "甜蜜礼赞",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640088
}
},
[72076] = {
commodityId = 72076,
commodityName = "甜蜜礼赞",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640089
}
},
[72077] = {
commodityId = 72077,
commodityName = "禁止挂机",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1732204800
},
shopTag = v3,
shopSort = 1,
jumpId = 1066,
jumpText = "特色礼包",
minVersion = "1.2.100.1",
itemIds = {
640074
},
canGift = true,
addIntimacy = 80,
giftCoinType = 1,
giftPrice = 300,
bOpenSuit = true,
suitId = 40039
},
[72078] = {
commodityId = 72078,
commodityName = "音动星河",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1732291200
},
endTime = {
seconds = 1734019199
},
shopTag = v3,
jumpId = 458,
jumpText = "月半夜曲",
minVersion = "1.3.26.111",
itemIds = {
640037
},
bOpenSuit = true,
suitId = 40020
},
[72079] = {
commodityId = 72079,
commodityName = "蛋糕手杖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737043199
},
shopTag = v3,
jumpId = 1070,
jumpText = "新年充值送",
minVersion = "1.3.37.87",
itemIds = {
640090
},
suitId = 40050
},
[72080] = {
commodityId = 72080,
commodityName = "蛋糕手杖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640091
}
},
[72081] = {
commodityId = 72081,
commodityName = "蛋糕手杖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640092
}
},
[72082] = {
commodityId = 72082,
commodityName = "浪漫花束",
limitType = v0,
limitNum = 1,
itemIds = {
640093
},
suitId = 40051
},
[72083] = {
commodityId = 72083,
commodityName = "浪漫花束",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640094
}
},
[72084] = {
commodityId = 72084,
commodityName = "浪漫花束",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640095
}
},
[72085] = {
commodityId = 72085,
commodityName = "鸣雷",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734105600
},
endTime = {
seconds = 1736697599
},
shopTag = v3,
shopSort = 1,
jumpId = 10701,
jumpText = "峡谷幻梦",
minVersion = "1.3.37.37",
itemIds = {
640096
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 90,
bOpenSuit = true,
suitId = 40052
},
[72086] = {
commodityId = 72086,
commodityName = "强化足球",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v3,
jumpId = 1068,
jumpText = "柯南联动时装",
minVersion = "1.3.37.37",
itemIds = {
640098
},
bOpenSuit = true,
suitId = 40054
},
[72087] = {
commodityId = 72087,
commodityName = "灰原哀的电脑",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v3,
jumpId = 1068,
jumpText = "柯南联动时装",
minVersion = "1.3.37.37",
itemIds = {
640099
},
bOpenSuit = true,
suitId = 40055
},
[72088] = {
commodityId = 72088,
commodityName = "空手道包",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopTag = v3,
jumpId = 1068,
jumpText = "柯南联动时装",
minVersion = "1.3.37.37",
itemIds = {
640100
},
bOpenSuit = true,
suitId = 40056
},
[72089] = {
commodityId = 72089,
commodityName = "爱神之弓",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopTag = v3,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = "1.3.37.87",
itemIds = {
640102
},
canGift = true,
addIntimacy = 80,
giftCoinType = 224,
giftPrice = 30,
bOpenSuit = true,
suitId = 40058
},
[72090] = {
commodityId = 72090,
commodityName = "爱神之弓",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
itemIds = {
640103
}
},
[72091] = {
commodityId = 72091,
commodityName = "爱神之弓",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
itemIds = {
640104
}
},
[72092] = {
commodityId = 72092,
commodityName = "泡泡鲨玩偶",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = v3,
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = "1.3.37.87",
itemIds = {
640105
},
canGift = true,
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
addIntimacy = 160,
giftCoinType = 224,
giftPrice = 180,
bOpenSuit = true,
suitId = 40059
},
[72093] = {
commodityId = 72093,
commodityName = "泡泡鲨玩偶",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
itemIds = {
640106
}
},
[72094] = {
commodityId = 72094,
commodityName = "泡泡鲨玩偶",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
itemIds = {
640107
}
},
[72095] = {
commodityId = 72095,
commodityName = "圣光权杖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1756655999
},
shopTag = v3,
shopSort = 1,
jumpId = 8888,
jumpText = "天启圣谕",
minVersion = "1.3.68.52",
itemIds = {
640108
},
canGift = true,
addIntimacy = 500,
giftCoinType = 213,
giftPrice = 2,
bOpenSuit = true,
suitId = 40060
},
[72096] = {
commodityId = 72096,
commodityName = "圣光权杖",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.68.52",
itemIds = {
640109
}
},
[72097] = {
commodityId = 72097,
commodityName = "圣光权杖",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
minVersion = "1.3.68.52",
itemIds = {
640110
}
},
[72098] = {
commodityId = 72098,
commodityName = "福运金镲",
limitType = v0,
limitNum = 1,
itemIds = {
640111
}
},
[72099] = {
commodityId = 72099,
commodityName = "福运金镲",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640112
}
},
[72100] = {
commodityId = 72100,
commodityName = "福运金镲",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640113
}
},
[72101] = {
commodityId = 72101,
commodityName = "净窗精灵",
limitType = v0,
limitNum = 1,
itemIds = {
640114
}
},
[72102] = {
commodityId = 72102,
commodityName = "净窗精灵",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640115
}
},
[72103] = {
commodityId = 72103,
commodityName = "净窗精灵",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640116
}
},
[72104] = {
commodityId = 72104,
commodityName = "地铁跑酷金币",
limitType = v0,
limitNum = 1,
itemIds = {
640117
}
},
[72107] = {
commodityId = 72107,
commodityName = "弦歌雅韵",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopTag = v3,
shopSort = 1,
jumpId = 636,
jumpText = "赛季祈愿",
minVersion = "1.3.68.1",
itemIds = {
640120
}
},
[72108] = {
commodityId = 72108,
commodityName = "弦歌雅韵",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
itemIds = {
640121
}
},
[72109] = {
commodityId = 72109,
commodityName = "弦歌雅韵",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
itemIds = {
640122
}
},
[72111] = {
commodityId = 72111,
commodityName = "鸣雷",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v3,
shopSort = 1,
jumpId = 10710,
jumpText = "峡谷幻梦",
minVersion = "1.3.37.97",
itemIds = {
640096
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 90,
bOpenSuit = true,
suitId = 40052
},
[72112] = {
commodityId = 72112,
commodityName = "闪耀应援",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741190399
},
shopTag = v3,
shopSort = 1,
jumpId = 10710,
jumpText = "峡谷幻梦",
minVersion = "1.3.37.97",
itemIds = {
640124
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 90,
bOpenSuit = true,
suitId = 40066
},
[72113] = {
commodityId = 72113,
commodityName = "大耳狗棒棒糖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
shopTag = v3,
jumpId = 1073,
jumpText = "三丽鸥家族",
minVersion = "1.3.68.52",
itemIds = {
640118
},
bOpenSuit = true,
suitId = 40067
},
[72114] = {
commodityId = 72114,
commodityName = "美乐蒂气球",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1740067199
},
shopTag = v3,
jumpId = 1074,
jumpText = "三丽鸥家族",
minVersion = "1.3.68.52",
itemIds = {
640119
},
bOpenSuit = true,
suitId = 40068
},
[72115] = {
commodityId = 72115,
commodityName = "银铃手鼓",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v3,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
minVersion = "1.3.68.87",
itemIds = {
640126
},
bOpenSuit = true,
suitId = 40070
},
[72116] = {
commodityId = 72116,
commodityName = "银铃手鼓",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640127
}
},
[72117] = {
commodityId = 72117,
commodityName = "银铃手鼓",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640128
}
},
[72118] = {
commodityId = 72118,
commodityName = "冷香团扇",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v3,
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
640129
},
bOpenSuit = true,
suitId = 40071
},
[72119] = {
commodityId = 72119,
commodityName = "冷香团扇",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
minVersion = "1.3.68.100",
itemIds = {
640130
}
},
[72120] = {
commodityId = 72120,
commodityName = "冷香团扇",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
minVersion = "1.3.68.100",
itemIds = {
640131
}
},
[72121] = {
commodityId = 72121,
commodityName = "赤霄焰舞",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
shopTag = v3,
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
640132
},
bOpenSuit = true,
suitId = 40072
},
[72122] = {
commodityId = 72122,
commodityName = "赤霄焰舞",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
minVersion = "1.3.68.100",
itemIds = {
640133
}
},
[72123] = {
commodityId = 72123,
commodityName = "赤霄焰舞",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
minVersion = "1.3.68.100",
itemIds = {
640134
}
},
[72126] = {
commodityId = 72126,
commodityName = "旋风清道夫",
limitType = v0,
limitNum = 1,
itemIds = {
640135
}
},
[72127] = {
commodityId = 72127,
commodityName = "旋风清道夫",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640136
}
},
[72128] = {
commodityId = 72128,
commodityName = "旋风清道夫",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640137
}
},
[72125] = {
commodityId = 72125,
commodityName = "苍龙行云",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1769788799
},
shopTag = v3,
jumpId = 10711,
jumpText = "峡谷祈愿",
minVersion = "1.3.68.37",
itemIds = {
640125
},
bOpenSuit = true,
suitId = 40069
},
[72129] = {
commodityId = 72129,
commodityName = "冰霜法杖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1750694400
},
endTime = {
seconds = 1752595199
},
shopTag = v3,
jumpId = 10713,
jumpText = "峡谷女明星",
itemIds = {
640079
},
bOpenSuit = true,
suitId = 40043
},
[72130] = {
commodityId = 72130,
commodityName = "心语之桥",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1746719999
},
shopTag = v3,
shopSort = 1,
jumpId = 638,
jumpText = "岚汀之约",
minVersion = "1.3.26.93",
itemIds = {
640123
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 12000,
bOpenSuit = true,
suitId = 40065
},
[72131] = {
commodityId = 72131,
commodityName = "麦霸助手",
limitType = v0,
limitNum = 1,
itemIds = {
640139
}
},
[72132] = {
commodityId = 72132,
commodityName = "麦霸助手",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640140
}
},
[72133] = {
commodityId = 72133,
commodityName = "麦霸助手",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640141
}
},
[72134] = {
commodityId = 72134,
commodityName = "鸣雷",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v3,
shopSort = 1,
jumpId = 10714,
jumpText = "峡谷幻梦",
minVersion = "1.3.68.100",
itemIds = {
640096
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 90,
bOpenSuit = true,
suitId = 40052
},
[72135] = {
commodityId = 72135,
commodityName = "闪耀应援",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1744991999
},
shopTag = v3,
shopSort = 1,
jumpId = 10714,
jumpText = "峡谷幻梦",
minVersion = "1.3.68.100",
itemIds = {
640124
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 90,
bOpenSuit = true,
suitId = 40066
},
[72136] = {
commodityId = 72136,
commodityName = "爱心之斧",
itemIds = {
640142
}
},
[72137] = {
commodityId = 72137,
commodityName = "天机羽扇",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1746806399
},
shopTag = v3,
jumpId = 10715,
jumpText = "峡谷英豪",
itemIds = {
640143
},
bOpenSuit = true,
suitId = 40076
},
[72138] = {
commodityId = 72138,
commodityName = "奇幻蘑菇",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1753977599
},
shopTag = v3,
shopSort = 1,
jumpId = 8899,
jumpText = "蝶舞花间",
minVersion = "1.3.88.98",
itemIds = {
640144
},
canGift = true,
addIntimacy = 80,
giftCoinType = 3950,
giftPrice = 40,
bOpenSuit = true,
suitId = 40077
},
[72139] = {
commodityId = 72139,
commodityName = "奇幻蘑菇",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640145
}
},
[72140] = {
commodityId = 72140,
commodityName = "奇幻蘑菇",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640146
}
},
[72141] = {
commodityId = 72141,
commodityName = "菠萝小桨",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopTag = v3,
jumpId = 10599,
jumpText = "小甜豆",
minVersion = "1.3.78.73",
itemIds = {
640147
},
bOpenSuit = true,
suitId = 40078
},
[72142] = {
commodityId = 72142,
commodityName = "妙蛙吹风机",
limitType = v0,
limitNum = 1,
itemIds = {
640148
}
},
[72143] = {
commodityId = 72143,
commodityName = "妙蛙吹风机",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640149
}
},
[72144] = {
commodityId = 72144,
commodityName = "妙蛙吹风机",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640150
}
},
[72145] = {
commodityId = 72145,
commodityName = "三角奶酪",
limitType = v0,
limitNum = 1,
itemIds = {
640151
}
},
[72146] = {
commodityId = 72146,
commodityName = "星钥秘杖",
itemIds = {
640152
}
},
[72147] = {
commodityId = 72147,
commodityName = "星钥秘杖",
coinType = 200008,
price = 20,
itemIds = {
640153
}
},
[72148] = {
commodityId = 72148,
commodityName = "星钥秘杖",
coinType = 200008,
price = 20,
itemIds = {
640154
}
},
[72149] = {
commodityId = 72149,
commodityName = "甜蜜鲷鱼烧",
itemIds = {
640155
}
},
[72150] = {
commodityId = 72150,
commodityName = "鸣雷",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v3,
shopSort = 1,
jumpId = 10718,
jumpText = "峡谷幻梦",
minVersion = "1.3.78.80",
itemIds = {
640096
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 90,
bOpenSuit = true,
suitId = 40052
},
[72151] = {
commodityId = 72151,
commodityName = "闪耀应援",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopTag = v3,
shopSort = 1,
jumpId = 10718,
jumpText = "峡谷幻梦",
minVersion = "1.3.78.80",
itemIds = {
640124
},
canGift = true,
addIntimacy = 80,
giftCoinType = 218,
giftPrice = 90,
bOpenSuit = true,
suitId = 40066
},
[72152] = {
commodityId = 72152,
commodityName = "甜心法杖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1749052800
},
shopTag = v3,
jumpId = 669,
jumpText = "甜梦嘉年华",
minVersion = "1.3.78.80",
itemIds = {
640061
},
bOpenSuit = true,
suitId = 40032
},
[72153] = {
commodityId = 72153,
commodityName = "樱语咖啡",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
jumpId = 1096,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
640156
},
bOpenSuit = true,
suitId = 40083
},
[72154] = {
commodityId = 72154,
commodityName = "兔宝糖葫芦",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
jumpId = 1097,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
640157
},
bOpenSuit = true,
suitId = 40084
},
[72155] = {
commodityId = 72155,
commodityName = "自拍神喵",
limitType = v0,
limitNum = 1,
shopTag = v3,
itemIds = {
640160
}
},
[72156] = {
commodityId = 72156,
commodityName = "自拍神喵",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640161
}
},
[72157] = {
commodityId = 72157,
commodityName = "自拍神喵",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640162
}
},
[72158] = {
commodityId = 72158,
commodityName = "绮梦独角兽",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
shopTag = v3,
jumpId = 88888,
jumpText = "幻彩调律",
itemIds = {
640163
},
bOpenSuit = true,
suitId = 40086
},
[72159] = {
commodityId = 72159,
commodityName = "绮梦独角兽",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640164
}
},
[72160] = {
commodityId = 72160,
commodityName = "绮梦独角兽",
coinType = 200008,
price = 20,
limitType = v0,
limitNum = 1,
beginTime = v2,
itemIds = {
640165
}
},
[72161] = {
commodityId = 72161,
commodityName = "彩虹波板糖",
limitType = v0,
limitNum = 1,
itemIds = {
640166
}
},
[72162] = {
commodityId = 72162,
commodityName = "彩虹波板糖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640167
}
},
[72163] = {
commodityId = 72163,
commodityName = "彩虹波板糖",
coinType = 200008,
price = 5,
limitType = v0,
limitNum = 1,
itemIds = {
640168
}
},
[72164] = {
commodityId = 72164,
commodityName = "猫耳电音台",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
shopSort = 1,
jumpId = 694,
jumpText = "幻猫音境",
itemIds = {
640158
},
canGift = true,
addIntimacy = 500,
giftCoinType = 1,
giftPrice = 12000,
bOpenSuit = true
},
[72165] = {
commodityId = 72165,
commodityName = "桂影轻舞",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749830400
},
endTime = {
seconds = 1752508799
},
shopTag = v3,
jumpId = 10719,
jumpText = "奇遇舞章",
minVersion = "1.3.88.105",
itemIds = {
640173
},
bOpenSuit = true,
suitId = 40090
}
}

local mt = {
mallId = 43,
gender = 0,
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data