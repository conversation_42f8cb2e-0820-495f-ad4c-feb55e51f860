--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_载具.xlsx: 装扮玩法

local data = {
[891000] = {
id = 891000,
type = "ItemType_VehicleDecorate",
quality = 3,
name = "1阶装饰",
desc = "装饰测试1的描述装饰测试1的描述装饰测试1的描述装饰测试1的描述装饰测试1的描述",
icon = "CDN:Icon_Wing_455",
getWay = "跳转描述",
jumpId = {
88888
},
buff = "载具配饰道具增益01",
buffValue = "+10%",
buffViewOffset = "-50,0,200",
buffSecond = "载具配饰道具第二增益01",
buffSecondValue = "+40%",
VehicleDecorateValues = {
HasSpecialJet = true
}
},
[891001] = {
id = 891001,
type = "ItemType_VehicleDecorate",
quality = 3,
name = "2阶装饰1",
desc = "装饰测试2的描述，装饰测试2的描述装饰测试2的描述装饰测试2的描述",
icon = "CDN:Icon_Wing_498",
getWay = "跳转描述",
jumpId = {
88888
},
buff = "载具配饰道具增益02",
buffValue = "+20%",
buffViewOffset = "-50,0,200",
buffSecond = "载具配饰道具第二增益02",
buffSecondValue = "+50%",
VehicleDecorateValues = {
HasSpecialJet = true
}
},
[891002] = {
id = 891002,
type = "ItemType_VehicleDecorate",
quality = 3,
name = "2阶装饰2",
desc = "装饰测试3的描述，装饰测试3的描述装饰测试3的描述装饰测试3的描述装饰测试3的描述",
icon = "CDN:Icon_Wing_493",
getWay = "跳转描述",
jumpId = {
88888
},
buff = "载具配饰道具增益03",
buffValue = "+30%",
buffViewOffset = "-50,0,200",
buffSecond = "载具配饰道具第二增益03",
buffSecondValue = "+60%",
VehicleDecorateValues = {
HasSpecialJet = true
}
},
[891003] = {
id = 891003,
type = "ItemType_VehicleDecorate",
quality = 3,
name = "3阶装饰1",
desc = "装饰测试1的描述装饰测试1的描述装饰测试1的描述装饰测试1的描述装饰测试1的描述",
icon = "CDN:Icon_Wing_455",
resourceConf = {
model = "1;2;3;4;5",
modelType = 1
},
buff = "载具配饰道具增益01",
buffValue = "+10%",
buffViewOffset = "-50,0,200",
buffSecond = "载具配饰道具第二增益01",
buffSecondValue = "+40%"
},
[891004] = {
id = 891004,
type = "ItemType_VehicleDecorate",
quality = 3,
name = "3阶装饰2",
desc = "装饰测试2的描述，装饰测试2的描述装饰测试2的描述装饰测试2的描述",
icon = "CDN:Icon_Wing_498",
buff = "载具配饰道具增益02",
buffValue = "+20%",
buffViewOffset = "-50,0,200",
buffSecond = "载具配饰道具第二增益02",
buffSecondValue = "+50%"
},
[891005] = {
id = 891005,
type = "ItemType_VehicleDecorate",
quality = 3,
name = "3阶装饰3",
desc = "装饰测试3的描述，装饰测试3的描述装饰测试3的描述装饰测试3的描述装饰测试3的描述",
icon = "CDN:Icon_Wing_493",
buff = "载具配饰道具增益03",
buffValue = "+30%",
buffViewOffset = "-50,0,200",
buffSecond = "钓鱼获取熟练度提升",
buffSecondValue = "+10%"
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data