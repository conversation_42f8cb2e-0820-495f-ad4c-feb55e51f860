--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_礼包_特色玩法运营.xlsx: 随机礼包

local v0 = "CDN:T_Common_Item_System_BagBig_009"

local data = {
[329906] = {
id = 329906,
effect = true,
name = "狼人随机宝箱",
desc = "开启后有概率获得：狼人币*50；阵营卡*1；身份卡*1",
icon = v0,
getWay = "奇趣派对礼活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
13,
200101,
200102
},
itemNums = {
50,
1,
1
},
expireDays = {
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
70,
20,
10
}
}
}
},
[329901] = {
id = 329901,
effect = true,
quality = 5,
name = "经典盲盒",
desc = "打开后随机获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_Box_01",
getWay = "活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
4,
6,
200014,
203001,
200006,
200008,
3134
},
itemNums = {
100,
1,
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
10,
30,
5,
2,
2,
1
}
}
}
},
[329902] = {
id = 329902,
effect = true,
quality = 4,
name = "炫彩盲盒",
desc = "打开后随机获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_Box_01",
getWay = "活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
4,
6,
200014,
203001,
200006,
200008,
3134
},
itemNums = {
300,
3,
2,
2,
1,
1,
2
},
expireDays = {
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
10,
30,
5,
2,
2,
1
}
}
}
},
[329903] = {
id = 329903,
effect = true,
name = "稀有盲盒",
desc = "打开后随机获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_Box_02",
getWay = "活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
4,
6,
200014,
203001,
200006,
200008,
3134
},
itemNums = {
500,
5,
3,
3,
1,
1,
3
},
expireDays = {
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
10,
30,
5,
2,
2,
1
}
}
}
},
[329904] = {
id = 329904,
effect = true,
quality = 2,
name = "非凡盲盒",
desc = "打开后随机获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_Box_02",
getWay = "活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
4,
6,
200014,
203001,
200006,
200008,
3134
},
itemNums = {
1000,
10,
4,
4,
1,
1,
4
},
expireDays = {
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
50,
10,
30,
5,
2,
2,
1
}
}
}
},
[329905] = {
id = 329905,
effect = true,
quality = 1,
name = "臻藏盲盒",
desc = "打开后随机获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_Box_03",
getWay = "活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
4,
6,
200014,
203001,
200006,
200008,
3134,
630258
},
itemNums = {
2000,
20,
5,
5,
1,
1,
5,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
45,
10,
30,
5,
2,
2,
3,
3
}
}
}
},
[329907] = {
id = 329907,
effect = true,
name = "狼人随机宝箱",
desc = "开启后有概率获得：狼人币*50；狼人币*100；阵营卡*1；身份卡*1",
icon = v0,
getWay = "限时赏金任务",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
13,
13,
200101,
200102
},
itemNums = {
50,
100,
1,
1
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
90000,
9000,
990,
10
}
}
}
},
[329908] = {
id = 329908,
effect = true,
name = "未知线索",
desc = "开启后有概率获得：信件线索*1；钥匙线索*1；饮料线索*1;手机线索*1;钱包线索*1;包裹线索*1",
icon = "CDN:T_Common_Item_UnknownBig_01",
getWay = "奇趣派对礼活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3434,
3435,
3436,
3437,
3438,
3439
},
itemNums = {
1,
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
10,
10,
10,
10,
10
}
}
}
},
[329920] = {
id = 329920,
effect = true,
name = "虞姬礼包",
desc = "打开后有概率获得以下奖励之一：峡谷英雄-虞姬、幻梦币*4、幻梦币*2、峡谷币*30、峡谷币*20、虞姬（1天）",
icon = "CDN:T_CutGift_Icon_Pack_08",
getWay = "活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301130,
14,
14,
3541,
3541,
301221
},
itemNums = {
1,
4,
2,
30,
20,
1
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
50,
100,
2000,
2000,
5000
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1
}
}
}
},
[329921] = {
id = 329921,
effect = true,
name = "宠物装饰随机礼包",
desc = "打开后有概率获得以下奖励之一：宠物装饰自选礼盒：15%；幸运币*1：25%；云朵币*30：60%",
getWay = "限时礼包获得",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
331009,
2,
6
},
itemNums = {
1,
1,
30
},
expireDays = {
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
15,
25,
60
},
guaranteeItemIds = {
331009,
331009,
331009
},
guaranteeTimes = {
3,
6,
9
},
ownedFilter = {
false,
false,
false
},
drawLimitTimes = {
3,
0,
0
}
}
},
pictureUrl = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_CutGift_Icon_pack_sjgp2.astc"
},
[329925] = {
id = 329925,
effect = true,
name = "英雄体验卡随机礼包",
desc = "开启后有概率获得：xxxx",
icon = "CDN:T_Common_Item_System_MobaBox",
getWay = "峡谷英雄祈愿获得",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301514,
301511
},
itemNums = {
1,
1
},
expireDays = {
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
10
}
}
}
},
[329926] = {
id = 329926,
effect = true,
name = "农场红包随机礼包",
desc = "打开后随机获得以下奖励的一项",
icon = "CDN:T_Common_Item_System_Redbag04",
getWay = "农场红包季活动获得",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
725902,
725903,
725904,
725905,
725906,
725907,
725908
},
itemNums = {
1,
1,
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
10,
10,
10,
10,
10,
10,
10
}
}
}
},
[329927] = {
id = 329927,
effect = true,
name = "宠物装饰随机礼包",
desc = "打开后有概率获得以下奖励之一：宠物装饰自选礼盒：15%；幸运币*1：25%；云朵币*30：60%",
getWay = "限时礼包获得",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
331017,
2,
6
},
itemNums = {
1,
1,
30
},
expireDays = {
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
15,
25,
60
},
guaranteeItemIds = {
331017,
331017,
331017
},
guaranteeTimes = {
3,
6,
9
},
ownedFilter = {
false,
false,
false
},
drawLimitTimes = {
3,
0,
0
}
}
},
pictureUrl = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_CutGift_Icon_pack_sjgp3.astc"
},
[329910] = {
id = 329910,
effect = true,
name = "农场贴纸随机礼包",
desc = "开启后有概率获得：水精灵贴纸*1；小柯基贴纸*1；精灵商人贴纸*1;小红狐贴纸*1;云精灵贴纸*1",
icon = "CDN:Icon_ReturnFarm_001",
getWay = "重返农场活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3470,
3471,
3472,
3473,
3474
},
itemNums = {
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
900,
40,
40,
15,
5
},
guaranteeItemIds = {
3473,
3474,
3470,
3472,
3471
},
guaranteeTimes = {
1,
2,
4,
6,
7
}
}
}
},
[329930] = {
id = 329930,
effect = true,
name = "刘备礼包",
desc = "打开后有概率获得以下奖励之一：峡谷英雄-刘备、幻梦币*4、幻梦币*2、峡谷币*30、峡谷币*20、新春霸福券*30",
icon = "CDN:T_CutGift_Icon_Pack_08",
getWay = "活动",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301136,
14,
14,
3541,
3541,
3700
},
itemNums = {
1,
4,
2,
30,
20,
30
},
expireDays = {
0,
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
1,
99999,
400000,
2000000,
2500000,
5000000
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
1,
1,
1,
1
}
}
}
},
[329931] = {
id = 329931,
effect = true,
name = "孙悟空英雄宝箱",
desc = "打开后随机获得以下奖励之一：英雄-孙悟空、孙悟空（1天）、峡谷币*15、蓝色卡牌宝箱",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301116,
301213,
3541,
300102
},
itemNums = {
1,
1,
15,
1
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
100,
5000,
4000,
900
}
}
}
},
[329932] = {
id = 329932,
effect = true,
name = "马可波罗宝箱",
desc = "打开后随机获得以下奖励之一：英雄-马可波罗、马可波罗（1天）、峡谷币*15、蓝色卡牌宝箱",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301127,
301018,
3541,
300102
},
itemNums = {
1,
1,
15,
1
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
100,
5000,
4000,
900
}
}
}
},
[329933] = {
id = 329933,
effect = true,
name = "张良宝箱",
desc = "打开后随机获得以下奖励之一：英雄-张良、张良（1天）、峡谷币*15、蓝色卡牌宝箱",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301130,
301221,
3541,
300102
},
itemNums = {
1,
1,
15,
1
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
100,
5000,
4000,
900
}
}
}
},
[329934] = {
id = 329934,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：致命啄客、致命啄客（3天体验卡）、田园牧歌炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
400190,
400190,
290011,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
3,
6,
9
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329935] = {
id = 329935,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：哄不好头饰、哄不好头饰（3天体验卡）、深度学习、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
630004,
630004,
720017,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
3,
6,
9
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329936] = {
id = 329936,
effect = true,
name = "峡谷币宝箱",
desc = "打开后随机获得以下奖励之一：峡谷币*588、峡谷币*88、峡谷币*25、峡谷币*15",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
3541,
3541,
3541,
3541
},
itemNums = {
588,
88,
25,
15
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
100,
6000,
3000,
900
}
}
}
},
[329937] = {
id = 329937,
effect = true,
name = "狼人随机宝箱",
desc = "开启后随机获得以下奖励之一：狼人币*50、狼人币*100、阵营卡*1、身份卡*1",
icon = "CDN:T_Common_Item_System_WerewolfBag_15",
getWay = "精灵谷集结任务",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
13,
13,
200101,
200102
},
itemNums = {
50,
100,
1,
1
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
90000,
9000,
990,
10
}
}
}
},
[329938] = {
id = 329938,
effect = true,
name = "阿珂宝箱",
desc = "打开后随机获得以下奖励之一：英雄-阿珂、阿珂（1天）、峡谷币*15、蓝色卡牌宝箱",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301131,
301222,
3541,
300102
},
itemNums = {
1,
1,
15,
1
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
100,
5000,
4000,
900
}
}
}
},
[329939] = {
id = 329939,
effect = true,
name = "星世界随机宝箱",
desc = "打开后随机获得以下奖励之一：星愿币*6、星愿币*3、时装染色膏*3、饰品调色盘*3、星宝印章*500",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
2,
2,
200006,
200008,
4
},
itemNums = {
6,
3,
3,
3,
500
},
expireDays = {
0,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
10,
550,
550,
8885
},
guaranteeItemIds = {
4,
4,
4
},
guaranteeTimes = {
3,
6,
9
},
ownedFilter = {
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
0,
0,
0,
0
}
}
}
},
[329940] = {
id = 329940,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：致命啄客、致命啄客（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
400190,
400190,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329942] = {
id = 329942,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：摩羯星、摩羯星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
401250,
401250,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329943] = {
id = 329943,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：惊叹号、惊叹号（3天体验卡）、彩虹漩涡、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
630072,
630072,
720108,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329944] = {
id = 329944,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：水瓶星、水瓶星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
401670,
401670,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329945] = {
id = 329945,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：海王星、海王星（3天体验卡）、地板动作、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
630103,
630103,
720141,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329946] = {
id = 329946,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：双鱼星、双鱼星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
401940,
401940,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329947] = {
id = 329947,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：整点薯条、整点薯条（3天体验卡）、坚持自律、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620249,
620249,
720165,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329948] = {
id = 329948,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：白羊星、白羊星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
402240,
402240,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329949] = {
id = 329949,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：可乐背包、可乐背包（3天体验卡）、仰卧高手、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620334,
620334,
720636,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329950] = {
id = 329950,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：金牛星、金牛星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
402660,
402660,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329951] = {
id = 329951,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：星乐杯、星乐杯（3天体验卡）、热身运动、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620401,
620401,
720674,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329952] = {
id = 329952,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：天秤星、天秤星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
403380,
403380,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329953] = {
id = 329953,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：炸鸡乐园、炸鸡乐园（3天体验卡）、热情搏击、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620450,
620450,
720753,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329954] = {
id = 329954,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：天蝎星、天蝎星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
403730,
403730,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329955] = {
id = 329955,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：棋王小帅、棋王小帅（3天体验卡）、干饭步伐、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620536,
620536,
720807,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329956] = {
id = 329956,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：射手星、射手星（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
404300,
404300,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329957] = {
id = 329957,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：神之一将、神之一将（3天体验卡）、优雅吃货、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620614,
620614,
720874,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329958] = {
id = 329958,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：ESTP行动派、ESTP行动派（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
404710,
404710,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329959] = {
id = 329959,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：麻将幺鸡、麻将幺鸡（3天体验卡）、有点功夫、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620669,
620669,
720938,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329960] = {
id = 329960,
effect = true,
name = "星世界时装宝箱",
desc = "打开后随机获得以下奖励之一：INFP小蝴蝶、INFP小蝴蝶（3天体验卡）、缤纷市集炫彩包*1、烟花礼盒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
410580,
410580,
290021,
725201
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
3,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
2995,
5500
},
guaranteeItemIds = {
725201,
725201,
725201
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0
}
}
}
},
[329961] = {
id = 329961,
effect = true,
name = "星世界配饰宝箱",
desc = "打开后随机获得以下奖励之一：兵卒一枚、兵卒一枚（3天体验卡）、慢摇美餐、钞能力*1、星宝印章*100、璀璨烟花棒*1",
icon = v0,
packageConf = {
packageType = "PackageType_Random",
itemIds = {
620790,
620790,
720988,
725102,
4,
725002
},
itemNums = {
1,
1,
1,
1,
100,
1
},
expireDays = {
0,
3,
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
5,
1500,
495,
2000,
2000,
4000
},
guaranteeItemIds = {
725002,
725002,
725002
},
guaranteeTimes = {
1,
3,
7
},
ownedFilter = {
true,
false,
false,
false,
false,
false
},
drawLimitTimes = {
1,
1,
0,
0,
0,
0
}
}
}
},
[329962] = {
id = 329962,
effect = true,
name = "随机英雄宝箱",
desc = "打开后随机获得以下奖励之一：英雄-孙悟空、英雄-虞姬、冰夷（1天）、峡谷币*20",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Random",
itemIds = {
301116,
301133,
301610,
3541
},
itemNums = {
1,
1,
1,
20
},
expireDays = {
0,
0,
0,
0
},
packageRandomConf = {
rangeWeight = {
100,
100,
5000,
4800
}
}
}
}
}

local mt = {
effect = false,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
getWay = "显示活动获得",
bagId = 1,
useType = "IUTO_GiftPackage"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data