--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_NR3E.xlsx: 狼人杀兑换商店

local v0 = 1

local v1 = {
1
}

local data = {
[380112] = {
commodityId = 380112,
commodityName = "咒术狼身份卡",
price = 3500,
limitNum = 1,
shopTag = {
1001
},
order = 1,
minVersion = "1.3.99.1",
itemIds = {
240842
},
itemMaxOwnNums = v1
},
[380113] = {
commodityId = 380113,
commodityName = "特工身份卡",
price = 3500,
limitNum = 1,
shopTag = {
1001
},
order = 2,
minVersion = "1.3.99.1",
itemIds = {
240844
},
itemMaxOwnNums = v1
},
[380110] = {
commodityId = 380110,
commodityName = "阴谋家身份卡",
price = 3500,
limitNum = 1,
shopTag = {
1001
},
order = 3,
minVersion = "1.3.88.1",
itemIds = {
240838
},
itemMaxOwnNums = v1
},
[380108] = {
commodityId = 380108,
commodityName = "傀儡狼身份卡",
price = 3500,
limitNum = 1,
shopTag = {
1001
},
order = 4,
minVersion = "1.3.78.1",
itemIds = {
240836
},
itemMaxOwnNums = v1
},
[380105] = {
commodityId = 380105,
commodityName = "雪狼身份卡",
price = 3500,
limitNum = 1,
beginTime = {
seconds = 1736870400
},
shopTag = {
1001
},
order = 5,
minVersion = "1.3.68.1",
itemIds = {
240830
},
beginShowTime = {
seconds = 1736870400
},
itemMaxOwnNums = v1
},
[380106] = {
commodityId = 380106,
commodityName = "双子身份卡",
price = 3500,
limitNum = 1,
beginTime = {
seconds = 1736870400
},
shopTag = {
1001
},
order = 6,
minVersion = "1.3.68.1",
itemIds = {
240831
},
beginShowTime = {
seconds = 1736870400
},
itemMaxOwnNums = v1
},
[380103] = {
commodityId = 380103,
commodityName = "仓鼠身份卡",
price = 3500,
limitNum = 1,
shopTag = {
1001
},
order = 7,
minVersion = "1.3.36.1",
itemIds = {
240825
},
itemMaxOwnNums = v1
},
[380101] = {
commodityId = 380101,
commodityName = "急救员身份卡",
price = 3500,
limitNum = 1,
shopTag = {
1001
},
order = 8,
minVersion = "1.3.26.1",
itemIds = {
240820
},
itemMaxOwnNums = v1
},
[380114] = {
commodityId = 380114,
commodityName = "先知身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
beginTime = {
seconds = 1750953600
},
shopTag = {
1001
},
order = 9,
minVersion = "1.3.99.1",
itemIds = {
240837
},
beginShowTime = {
seconds = 1750953600
},
itemMaxOwnNums = v1
},
[380111] = {
commodityId = 380111,
commodityName = "预言家身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
beginTime = {
seconds = 1746115200
},
shopTag = {
1001
},
order = 10,
minVersion = "1.3.88.1",
itemIds = {
240835
},
beginShowTime = {
seconds = 1746115200
},
itemMaxOwnNums = v1
},
[380109] = {
commodityId = 380109,
commodityName = "袋鼠身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
beginTime = {
seconds = 1741881600
},
shopTag = {
1001
},
order = 11,
minVersion = "1.3.78.1",
itemIds = {
240832
},
beginShowTime = {
seconds = 1741881600
},
itemMaxOwnNums = v1
},
[380016] = {
commodityId = 380016,
commodityName = "流浪汉身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
beginTime = {
seconds = 1724947200
},
shopTag = {
1001
},
order = 12,
itemIds = {
240801
},
beginShowTime = {
seconds = 1724947200
},
itemMaxOwnNums = v1
},
[380102] = {
commodityId = 380102,
commodityName = "幽灵狼身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
beginTime = {
seconds = 1729180800
},
shopTag = {
1001
},
order = 13,
minVersion = "1.3.36.1",
itemIds = {
240806
},
beginShowTime = {
seconds = 1729180800
},
itemMaxOwnNums = v1
},
[380014] = {
commodityId = 380014,
commodityName = "法官身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
shopTag = {
1001
},
order = 14,
itemIds = {
240803
},
itemMaxOwnNums = v1
},
[380104] = {
commodityId = 380104,
commodityName = "快递员身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
beginTime = {
seconds = 1732809600
},
shopTag = {
1001
},
order = 15,
itemIds = {
240823
},
beginShowTime = {
seconds = 1732809600
},
itemMaxOwnNums = v1
},
[380107] = {
commodityId = 380107,
commodityName = "侠客身份卡",
price = 2500,
limitNum = 1,
recommendTag = {
2,
151
},
beginTime = {
seconds = 1737043200
},
shopTag = {
1001
},
order = 16,
minVersion = "1.3.68.1",
itemIds = {
240827
},
beginShowTime = {
seconds = 1737043200
},
itemMaxOwnNums = v1
},
[380001] = {
commodityId = 380001,
commodityName = "阵营卡",
price = 80,
limitType = "MCL_WeeklyLimit",
limitNum = 10,
shopTag = {
1001
},
order = 17,
itemIds = {
200101
}
},
[380002] = {
commodityId = 380002,
commodityName = "身份卡",
price = 220,
limitType = "MCL_WeeklyLimit",
limitNum = 10,
shopTag = {
1001
},
order = 18,
itemIds = {
200102
}
},
[380003] = {
commodityId = 380003,
commodityName = "爱心",
price = 10,
limitType = "MCL_None",
shopTag = {
1002
},
order = 201,
itemIds = {
240403
}
},
[380004] = {
commodityId = 380004,
commodityName = "倒咖啡",
price = 10,
limitType = "MCL_None",
shopTag = {
1002
},
order = 202,
itemIds = {
240406
}
},
[380020] = {
commodityId = 380020,
commodityName = "干杯",
price = 10,
limitType = "MCL_None",
shopTag = {
1002
},
order = 203,
minVersion = "1.3.18.1",
itemIds = {
240412
}
},
[380005] = {
commodityId = 380005,
commodityName = "丢鸡蛋",
price = 10,
limitType = "MCL_None",
shopTag = {
1002
},
order = 204,
itemIds = {
240401
}
},
[380007] = {
commodityId = 380007,
commodityName = "扔炸弹",
price = 10,
limitType = "MCL_None",
shopTag = {
1002
},
order = 205,
itemIds = {
240405
}
},
[380201] = {
commodityId = 380201,
commodityName = "疯狂点赞",
price = 10,
limitType = "MCL_None",
shopTag = {
1002
},
order = 206,
minVersion = "1.3.26.1",
itemIds = {
240416
}
},
[380202] = {
commodityId = 380202,
commodityName = "丢蛋糕",
price = 10,
limitType = "MCL_None",
shopTag = {
1002
},
order = 207,
minVersion = "1.3.26.1",
itemIds = {
240420
}
},
[380006] = {
commodityId = 380006,
commodityName = "送花",
price = 50,
limitType = "MCL_None",
shopTag = {
1002
},
order = 208,
itemIds = {
240404
}
},
[380021] = {
commodityId = 380021,
commodityName = "打call",
price = 50,
limitType = "MCL_None",
shopTag = {
1002
},
order = 209,
minVersion = "1.3.18.1",
itemIds = {
240413
}
},
[380008] = {
commodityId = 380008,
commodityName = "连续拳击",
price = 50,
limitType = "MCL_None",
shopTag = {
1002
},
order = 210,
itemIds = {
240402
}
},
[380203] = {
commodityId = 380203,
commodityName = "贴符",
price = 50,
limitType = "MCL_None",
shopTag = {
1002
},
order = 211,
minVersion = "1.3.26.1",
itemIds = {
240417
}
},
[380204] = {
commodityId = 380204,
commodityName = "丢盲盒",
price = 50,
limitType = "MCL_None",
shopTag = {
1002
},
order = 212,
minVersion = "1.3.26.1",
itemIds = {
240418
}
},
[380205] = {
commodityId = 380205,
commodityName = "祝福",
price = 50,
limitType = "MCL_None",
shopTag = {
1002
},
order = 213,
minVersion = "1.3.26.1",
itemIds = {
240419
}
},
[380009] = {
commodityId = 380009,
commodityName = "大笑",
price = 800,
limitNum = 1,
shopTag = {
1003
},
order = 301,
itemIds = {
240605
},
itemMaxOwnNums = v1
},
[380010] = {
commodityId = 380010,
commodityName = "大哭",
price = 800,
limitNum = 1,
shopTag = {
1003
},
order = 302,
itemIds = {
240606
},
itemMaxOwnNums = v1
},
[380301] = {
commodityId = 380301,
commodityName = "看戏",
price = 800,
limitNum = 1,
shopTag = {
1003
},
order = 303,
minVersion = "1.3.26.1",
itemIds = {
240614
},
itemMaxOwnNums = v1
},
[380011] = {
commodityId = 380011,
commodityName = "赌徒头像",
price = 600,
limitNum = 1,
shopTag = {
1003
},
order = 304,
itemIds = {
860504
},
itemMaxOwnNums = v1
},
[380012] = {
commodityId = 380012,
commodityName = "秘术狼头像",
price = 600,
limitNum = 1,
shopTag = {
1003
},
order = 305,
itemIds = {
860501
},
itemMaxOwnNums = v1
},
[380013] = {
commodityId = 380013,
commodityName = "秘术狼昵称框",
price = 600,
limitNum = 1,
shopTag = {
1003
},
order = 306,
itemIds = {
820501
},
itemMaxOwnNums = v1
},
[380026] = {
commodityId = 380026,
commodityName = "秘术狼头像框",
price = 1200,
limitNum = 1,
shopTag = {
1003
},
order = 307,
itemIds = {
840501
},
itemMaxOwnNums = v1
},
[380412] = {
commodityId = 380412,
commodityName = "复仇者身份卡",
coinType = 220,
price = 250,
limitNum = 1,
shopTag = {
1004
},
order = 401,
minVersion = "1.3.99.1",
itemIds = {
240829
}
},
[380410] = {
commodityId = 380410,
commodityName = "陷阱狼身份卡",
coinType = 220,
price = 250,
limitNum = 1,
beginTime = {
seconds = 1741881600
},
shopTag = {
1004
},
order = 402,
minVersion = "1.3.78.1",
itemIds = {
240834
},
beginShowTime = {
seconds = 1741881600
}
},
[380411] = {
commodityId = 380411,
commodityName = "墨鱼身份卡",
coinType = 220,
price = 250,
limitNum = 1,
beginTime = {
seconds = 1741881600
},
shopTag = {
1004
},
order = 403,
minVersion = "1.3.78.1",
itemIds = {
240833
},
beginShowTime = {
seconds = 1741881600
}
},
[380403] = {
commodityId = 380403,
commodityName = "捕梦者身份卡",
coinType = 220,
price = 250,
limitNum = 1,
shopTag = {
1004
},
order = 404,
minVersion = "1.3.26.1",
itemIds = {
240814
}
},
[380404] = {
commodityId = 380404,
commodityName = "雾隐狼身份卡",
coinType = 220,
price = 250,
limitNum = 1,
shopTag = {
1004
},
order = 405,
minVersion = "1.3.36.1",
itemIds = {
240818
}
},
[380405] = {
commodityId = 380405,
commodityName = "假面狼身份卡",
coinType = 220,
price = 250,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
shopTag = {
1004
},
order = 406,
minVersion = "1.3.68.1",
itemIds = {
240807
},
beginShowTime = {
seconds = 1737043200
}
},
[380406] = {
commodityId = 380406,
commodityName = "鸵鸟身份卡",
coinType = 220,
price = 250,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
shopTag = {
1004
},
order = 407,
minVersion = "1.3.68.1",
itemIds = {
240819
},
beginShowTime = {
seconds = 1737043200
}
},
[380413] = {
commodityId = 380413,
commodityName = "表情-我出布",
coinType = 220,
price = 250,
limitNum = 1,
shopTag = {
1004
},
order = 408,
minVersion = "1.3.99.1",
itemIds = {
711530
}
},
[380414] = {
commodityId = 380414,
commodityName = "表情-我刀呢",
coinType = 220,
price = 250,
limitNum = 1,
shopTag = {
1004
},
order = 409,
minVersion = "1.3.99.1",
itemIds = {
711392
}
},
[380407] = {
commodityId = 380407,
commodityName = "狼人阵营头像",
coinType = 220,
price = 250,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
shopTag = {
1004
},
order = 410,
minVersion = "1.3.68.1",
itemIds = {
860162
},
beginShowTime = {
seconds = 1737043200
}
},
[380408] = {
commodityId = 380408,
commodityName = "平民阵营头像",
coinType = 220,
price = 250,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
shopTag = {
1004
},
order = 411,
minVersion = "1.3.68.1",
itemIds = {
860163
},
beginShowTime = {
seconds = 1737043200
}
},
[380409] = {
commodityId = 380409,
commodityName = "中立阵营头像",
coinType = 220,
price = 250,
limitNum = 1,
beginTime = {
seconds = 1737043200
},
shopTag = {
1004
},
order = 412,
minVersion = "1.3.68.1",
itemIds = {
860164
},
beginShowTime = {
seconds = 1737043200
}
},
[380402] = {
commodityId = 380402,
commodityName = "灰狼头像",
coinType = 220,
price = 60,
limitNum = 1,
shopTag = {
1004
},
order = 413,
gender = 6,
minVersion = "1.3.18.32",
itemIds = {
860084
}
}
}

local mt = {
mallId = 110,
coinType = 13,
limitType = "MCL_LifeLongLimit",
gender = 0,
itemNums = v1
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data