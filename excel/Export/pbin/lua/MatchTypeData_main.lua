--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_主表.xlsx: 玩法

local data = {
[1] = {
id = 1,
modeID = 1,
desc = "天天晋级赛",
sort = 6,
thumbImage = "CDN:T_ModelSelect_Img_Type_02",
image = "CDN:T_ModelSelectLarge_Img_Type_02",
showType = "UI_Model_GroupMachTypeItem_2",
modeGroup = "1|2|3",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
matchRuleId = 1,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlay",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：单人天天晋级赛，在32个人中角逐出唯一的冠军",
descShort = "休闲娱乐，轻松闯关，尽享欢乐趣味派对！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "单人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10004,
gameModeType = 1,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "经典",
preparationViewId = 1
},
[2] = {
id = 2,
modeID = 1,
desc = "天天晋级赛",
sort = 5,
thumbImage = "CDN:T_ModelSelect_Img_Type_02",
image = "CDN:T_ModelSelectLarge_Img_Type_02",
showType = "UI_Model_GroupMachTypeItem_2",
modeGroup = "1|2|3",
matchTeamNum = {
1,
2
},
teamTag = "2",
matchRuleId = 2,
TeamMatchGame = true,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlayTwo",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：双人天天晋级赛，队伍成员共同计分，共同参与晋级结算",
descShort = "休闲娱乐，轻松闯关，尽享欢乐趣味派对！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "双人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10005,
gameModeType = 1,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
playTagArr = {
"主玩法",
"休闲",
"双人"
},
playShow = "经典",
preparationViewId = 1
},
[3] = {
id = 3,
modeID = 1,
desc = "天天晋级赛",
sort = 4,
thumbImage = "CDN:T_ModelSelect_Img_Type_02",
image = "CDN:T_ModelSelectLarge_Img_Type_02",
showType = "UI_Model_GroupMachTypeItem_2",
modeGroup = "1|2|3",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "4",
matchRuleId = 3,
TeamMatchGame = true,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlayFour",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：四人天天晋级赛，队伍成员共同计分，共同参与晋级结算",
descShort = "休闲娱乐，轻松闯关，尽享欢乐趣味派对！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "四人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10006,
gameModeType = 1,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
playTagArr = {
"主玩法",
"休闲",
"四人"
},
playShow = "经典",
preparationViewId = 1
},
[4] = {
id = 4,
modeID = 1,
desc = "天天晋级赛",
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_01_S11",
image = "CDN:T_ModelSelectLarge_Img_Type_01_S11",
detailDesc = "4",
beDefault = 1,
modeGroup = "4|5|6|7|8|9",
settleProc = "MTSC_Rank",
matchTeamNum = {
1
},
teamTag = "1",
matchRuleId = 4,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Qualifying",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：单人天天晋级赛，在32个人中角逐出唯一的冠军",
descShort = "排位竞技，精彩对决，向更高的荣誉冲刺吧！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "单人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10014,
gameModeType = 2,
videoLinkId = 4,
overrideImageDesc = "天天晋级赛",
isEnableAiLabIntervention = 1,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
isEnableAiDifficultyTest = 1,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "竞技",
preparationViewId = 1,
bUseDetailedRecordDisplay = true
},
[5] = {
id = 5,
modeID = 1,
desc = "天天晋级赛",
isPermanent = true,
sort = 2,
thumbImage = "CDN:T_ModelSelect_Img_Type_01_S11",
image = "CDN:T_ModelSelectLarge_Img_Type_01_S11",
detailDesc = "4",
modeGroup = "4|5|6|7|8|9",
settleProc = "MTSC_Rank",
matchTeamNum = {
1,
2
},
teamTag = "2",
matchRuleId = 5,
TeamMatchGame = true,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QualifyingTwo",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：双人天天晋级赛，队伍成员共同计分，共同参与晋级结算",
descShort = "排位竞技，精彩对决，向更高的荣誉冲刺吧！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "双人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10015,
gameModeType = 2,
videoLinkId = 4,
overrideImageDesc = "天天晋级赛",
isEnableAiLabIntervention = 1,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
bShowTeamEnter = true,
isEnableAiDifficultyTest = 1,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"双人"
},
playShow = "竞技",
preparationViewId = 1,
bUseDetailedRecordDisplay = true,
openAIHosting = true
},
[6] = {
id = 6,
modeID = 1,
desc = "天天晋级赛",
isPermanent = true,
sort = 3,
thumbImage = "CDN:T_ModelSelect_Img_Type_01_S11",
image = "CDN:T_ModelSelectLarge_Img_Type_01_S11",
detailDesc = "4",
modeGroup = "4|5|6|7|8|9",
settleProc = "MTSC_Rank",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "4",
matchRuleId = 6,
TeamMatchGame = true,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QualifyingFour",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：四人天天晋级赛，队伍成员共同计分，共同参与晋级结算",
descShort = "排位竞技，精彩对决，向更高的荣誉冲刺吧！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "四人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10016,
gameModeType = 2,
videoLinkId = 4,
overrideImageDesc = "天天晋级赛",
isEnableAiLabIntervention = 1,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
bShowTeamEnter = true,
isEnableAiDifficultyTest = 1,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"四人"
},
playShow = "竞技",
preparationViewId = 1,
bUseDetailedRecordDisplay = true,
openAIHosting = true
},
[10] = {
id = 10,
modeID = 3,
desc = "桃源竞技场",
sort = 100,
thumbImage = "CDN:T_ModelSelect_Img_Type_10",
image = "CDN:T_ModelSelectLarge_Img_Type_10",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
matchRuleId = 100,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlay",
NeedShowProcessDisplay = true,
playTypeDesc = "提示：桃园竞技场，感受国风的魅力",
descShort = "国风关卡合集，感受国风的魅力吧",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 10,
buttonDesc = "单人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10100,
gameModeType = 7,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "经典"
},
[11] = {
id = 11,
modeID = 3,
desc = "激流勇进",
sort = 101,
thumbImage = "CDN:T_ModelSelect_Img_Type_11",
image = "CDN:T_ModelSelectLarge_Img_Type_11",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
matchRuleId = 101,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlay",
NeedShowProcessDisplay = true,
playTypeDesc = "提示：激流勇进，来刷新滑道关卡的世界记录吧！",
descShort = "滑道关卡合集，找到最快的路径，来刷新滑道关卡的世界记录吧！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 11,
buttonDesc = "单人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10101,
gameModeType = 7,
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "经典"
},
[7] = {
id = 7,
modeID = 1,
desc = "天天晋级赛",
isPermanent = true,
sort = 102,
thumbImage = "CDN:T_ModelSelect_Img_Type_03",
image = "CDN:T_ModelSelectLarge_Img_Type_02",
beDefault = 1,
beRecruitDefault = 1,
categoryDescription = "跑酷",
modeGroup = "4|5|6|7|8|9",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
matchRuleId = 1,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlay",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：单人天天晋级赛，在32个人中角逐出唯一的冠军",
descShort = "休闲娱乐，轻松闯关，尽享欢乐趣味派对！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "单人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10004,
gameModeType = 1,
overrideImageDesc = "天天晋级赛",
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "经典",
preparationViewId = 1,
bUseDetailedRecordDisplay = true
},
[8] = {
id = 8,
modeID = 1,
desc = "天天晋级赛",
isPermanent = true,
sort = 101,
thumbImage = "CDN:T_ModelSelect_Img_Type_03",
image = "CDN:T_ModelSelectLarge_Img_Type_02",
categoryDescription = "跑酷",
modeGroup = "4|5|6|7|8|9",
matchTeamNum = {
1,
2
},
teamTag = "2",
matchRuleId = 2,
TeamMatchGame = true,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlayTwo",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：双人天天晋级赛，队伍成员共同计分，共同参与晋级结算",
descShort = "休闲娱乐，轻松闯关，尽享欢乐趣味派对！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "双人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10005,
gameModeType = 1,
overrideImageDesc = "天天晋级赛",
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"双人"
},
playShow = "经典",
preparationViewId = 1,
bUseDetailedRecordDisplay = true
},
[9] = {
id = 9,
modeID = 1,
desc = "天天晋级赛",
isPermanent = true,
sort = 100,
thumbImage = "CDN:T_ModelSelect_Img_Type_03",
image = "CDN:T_ModelSelectLarge_Img_Type_02",
categoryDescription = "跑酷",
modeGroup = "4|5|6|7|8|9",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "4",
matchRuleId = 3,
TeamMatchGame = true,
battlePlayerNum = 32,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
warmRoundType = "WRST_QuickPlayFour",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：四人天天晋级赛，队伍成员共同计分，共同参与晋级结算",
descShort = "休闲娱乐，轻松闯关，尽享欢乐趣味派对！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 12001,
recordType = 2,
buttonDesc = "四人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10006,
gameModeType = 1,
overrideImageDesc = "天天晋级赛",
isOwnerStart = true,
UseDefaultChampionDisplayScene = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"四人"
},
playShow = "经典",
preparationViewId = 1,
bUseDetailedRecordDisplay = true
},
[12] = {
id = 12,
modeID = 3,
desc = "泡泡大战",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_Tang",
image = "CDN:T_ModelSelectLarge_Img_Type_Tang",
detailDesc = "12",
modeGroup = "12|13|14|18|19|20",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
matchRuleId = 12,
battlePlayerNum = 8,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlay",
isShowBattleRecord = true,
playTypeDesc = "提示：看看谁才是泡泡之王！",
descShort = "全新机制，邀你来战。用泡泡捕捉所有敌人！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 91001,
recordType = 11,
buttonDesc = "单人",
EnableRepGraph = true,
gameModeType = 7,
isOwnerStart = true,
AutoDownloadPakGroup = {
91001
},
AutoDeletePakGroup = {
91001
},
PakPlayID = 2,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "休闲",
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[13] = {
id = 13,
modeID = 3,
desc = "泡泡大战",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_Tang",
image = "CDN:T_ModelSelectLarge_Img_Type_Tang",
detailDesc = "12",
modeGroup = "12|13|14|18|19|20",
matchTeamNum = {
1,
2
},
teamTag = "2",
matchRuleId = 13,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlay",
isShowBattleRecord = true,
playTypeDesc = "提示：看看谁才是泡泡之王！",
descShort = "全新机制，邀你来战。用泡泡捕捉所有敌人！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 91001,
recordType = 11,
buttonDesc = "双人",
EnableRepGraph = true,
gameModeType = 7,
isOwnerStart = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
91001
},
AutoDeletePakGroup = {
91001
},
PakPlayID = 2,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"双人"
},
playShow = "休闲",
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[14] = {
id = 14,
modeID = 3,
desc = "泡泡大战",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_Tang",
image = "CDN:T_ModelSelectLarge_Img_Type_Tang",
detailDesc = "12",
modeGroup = "12|13|14|18|19|20",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "4",
matchRuleId = 14,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 1,
mmrType = "MST_Core",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_QuickPlay",
isShowBattleRecord = true,
playTypeDesc = "提示：看看谁才是泡泡之王！",
descShort = "全新机制，邀你来战。用泡泡捕捉所有敌人！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 91001,
recordType = 11,
buttonDesc = "四人",
EnableRepGraph = true,
gameModeType = 7,
isOwnerStart = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
91001
},
AutoDeletePakGroup = {
91001
},
PakPlayID = 2,
useBasicMusicGroup = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"四人"
},
playShow = "休闲",
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_TeamAccount_GDItem"
},
[15] = {
id = 15,
modeID = 3,
desc = "闪电赛",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_15",
image = "CDN:T_ModelSelectLarge_Img_Type_Lightning",
detailDesc = "15",
categoryDescription = "跑酷",
modeGroup = "15|16|17",
matchTeamNum = {
1
},
teamTag = "1",
matchRuleId = 15,
battlePlayerNum = 8,
dropId = 35,
mmrType = "WST_Lighting",
mmrAggrType = "MSAT_Highest",
warmRoundType = "WRST_Lighting",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：单人闪电赛，每人独立参与积分和结算",
descShort = "速战速决，一轮决胜，勇夺闪电奖章！",
warmDropId = 38,
outImage = "T_Lobby_Start_Lightning",
pakGroup = 12001,
recordType = 15,
buttonDesc = "单人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10115,
LowestVersion = "********",
gameModeType = 1,
overrideImageDesc = "闪电赛:随机事件",
isOwnerStart = true,
qualifyTimeIsShow = true,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
isLightningGame = true,
ignoreBestRecord = true,
overridePreShowText = "闪电赛已结束，请期待下次活动",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
preEndShowMatchName = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "竞技",
isOpenRandEvent = true,
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[16] = {
id = 16,
modeID = 3,
desc = "闪电赛",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_15",
image = "CDN:T_ModelSelectLarge_Img_Type_Lightning",
detailDesc = "15",
categoryDescription = "跑酷",
modeGroup = "15|16|17",
matchTeamNum = {
1,
2
},
teamTag = "2",
matchRuleId = 16,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 36,
mmrType = "WST_Lighting",
mmrAggrType = "MSAT_Highest",
warmRoundType = "WRST_Lighting",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：双人闪电赛，队伍成员共同计分和结算",
descShort = "速战速决，一轮决胜，勇夺闪电奖章！",
warmDropId = 39,
outImage = "T_Lobby_Start_Lightning",
pakGroup = 12001,
recordType = 15,
buttonDesc = "双人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10116,
LowestVersion = "********",
gameModeType = 1,
overrideImageDesc = "闪电赛:随机事件",
isOwnerStart = true,
qualifyTimeIsShow = true,
bShowTeamEnter = true,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
isLightningGame = true,
ignoreBestRecord = true,
overridePreShowText = "闪电赛已结束，请期待下次活动",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
preEndShowMatchName = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"双人"
},
playShow = "竞技",
isOpenRandEvent = true,
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[17] = {
id = 17,
modeID = 3,
desc = "闪电赛",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_15",
image = "CDN:T_ModelSelectLarge_Img_Type_Lightning",
detailDesc = "15",
categoryDescription = "跑酷",
modeGroup = "15|16|17",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "4",
matchRuleId = 17,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 37,
mmrType = "WST_Lighting",
mmrAggrType = "MSAT_Highest",
warmRoundType = "WRST_Lighting",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：四人闪电赛，队伍成员共同计分和结算",
descShort = "速战速决，一轮决胜，勇夺闪电奖章！",
warmDropId = 40,
outImage = "T_Lobby_Start_Lightning",
pakGroup = 12001,
recordType = 15,
buttonDesc = "四人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10117,
LowestVersion = "********",
gameModeType = 1,
overrideImageDesc = "闪电赛:随机事件",
isOwnerStart = true,
qualifyTimeIsShow = true,
bShowTeamEnter = true,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
isLightningGame = true,
ignoreBestRecord = true,
overridePreShowText = "闪电赛已结束，请期待下次活动",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
preEndShowMatchName = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"四人"
},
playShow = "竞技",
isOpenRandEvent = true,
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[18] = {
id = 18,
modeID = 3,
desc = "泡泡大战",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_Tang",
image = "CDN:T_ModelSelectLarge_Img_Type_Tang",
detailDesc = "12",
modeGroup = "12|13|14|18|19|20",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "1",
matchRuleId = 18,
battlePlayerNum = 8,
dropId = 1,
mmrType = "MST_BubbleDegree",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：看看谁才是泡泡之王！",
descShort = "排位竞技，精彩对决。用泡泡捕捉所有敌人！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 91001,
recordType = 11,
buttonDesc = "单人",
EnableRepGraph = true,
gameModeType = 7,
isOwnerStart = true,
AutoDownloadPakGroup = {
91001
},
AutoDeletePakGroup = {
91001
},
PakPlayID = 2,
useBasicMusicGroup = true,
playTagArr = {
"休闲"
},
playShow = "休闲",
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[19] = {
id = 19,
modeID = 3,
desc = "泡泡大战",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_Tang",
image = "CDN:T_ModelSelectLarge_Img_Type_Tang",
detailDesc = "12",
modeGroup = "12|13|14|18|19|20",
matchTeamNum = {
1,
2
},
teamTag = "2",
matchRuleId = 19,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 1,
mmrType = "MST_BubbleDegree",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：看看谁才是泡泡之王！",
descShort = "排位竞技，精彩对决。用泡泡捕捉所有敌人！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 91001,
recordType = 11,
buttonDesc = "双人",
EnableRepGraph = true,
gameModeType = 7,
isOwnerStart = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
91001
},
AutoDeletePakGroup = {
91001
},
PakPlayID = 2,
useBasicMusicGroup = true,
playTagArr = {
"休闲",
"双人"
},
playShow = "休闲",
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[20] = {
id = 20,
modeID = 3,
desc = "泡泡大战",
sort = 10,
thumbImage = "CDN:T_ModelSelect_Img_Type_Tang",
image = "CDN:T_ModelSelectLarge_Img_Type_Tang",
detailDesc = "12",
modeGroup = "12|13|14|18|19|20",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "4",
matchRuleId = 20,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 1,
mmrType = "MST_BubbleDegree",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
isShowBattleRecord = true,
playTypeDesc = "提示：看看谁才是泡泡之王！",
descShort = "排位竞技，精彩对决。用泡泡捕捉所有敌人！",
warmDropId = 9,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 91001,
recordType = 11,
buttonDesc = "四人",
EnableRepGraph = true,
gameModeType = 7,
isOwnerStart = true,
bShowTeamEnter = true,
AutoDownloadPakGroup = {
91001
},
AutoDeletePakGroup = {
91001
},
PakPlayID = 2,
useBasicMusicGroup = true,
playTagArr = {
"休闲",
"四人"
},
playShow = "休闲",
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_TeamAccount_GDItem"
},
[21] = {
id = 21,
modeID = 3,
desc = "闪电赛",
sort = 9,
thumbImage = "CDN:T_ModelSelect_Img_Type_LightingS10",
image = "CDN:T_ModelSelectLarge_Img_Type_LightingS10",
detailDesc = "15",
categoryDescription = "跑酷",
modeGroup = "21|22|23",
matchTeamNum = {
1
},
teamTag = "1",
matchRuleId = 21,
battlePlayerNum = 8,
dropId = 35,
mmrType = "WST_Lighting",
mmrAggrType = "MSAT_Highest",
warmRoundType = "WRST_Lighting",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：单人闪电赛，每人独立参与积分和结算",
descShort = "速战速决，一轮决胜，勇夺闪电奖章！",
warmDropId = 38,
outImage = "T_Lobby_Start_Lightning",
pakGroup = 12001,
recordType = 15,
buttonDesc = "单人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10121,
LowestVersion = "********",
gameModeType = 1,
overrideImageDesc = "闪电赛:新赛季关",
isOwnerStart = true,
qualifyTimeIsShow = true,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
isLightningGame = true,
ignoreBestRecord = true,
overridePreShowText = "闪电赛已结束，请期待下次活动",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
preEndShowMatchName = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲"
},
playShow = "竞技",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[22] = {
id = 22,
modeID = 3,
desc = "闪电赛",
sort = 9,
thumbImage = "CDN:T_ModelSelect_Img_Type_LightingS10",
image = "CDN:T_ModelSelectLarge_Img_Type_LightingS10",
detailDesc = "15",
categoryDescription = "跑酷",
modeGroup = "21|22|23",
matchTeamNum = {
1,
2
},
teamTag = "2",
matchRuleId = 22,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 36,
mmrType = "WST_Lighting",
mmrAggrType = "MSAT_Highest",
warmRoundType = "WRST_Lighting",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：双人闪电赛，队伍成员共同计分和结算",
descShort = "速战速决，一轮决胜，勇夺闪电奖章！",
warmDropId = 39,
outImage = "T_Lobby_Start_Lightning",
pakGroup = 12001,
recordType = 15,
buttonDesc = "双人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10122,
LowestVersion = "********",
gameModeType = 1,
overrideImageDesc = "闪电赛:新赛季关",
isOwnerStart = true,
qualifyTimeIsShow = true,
bShowTeamEnter = true,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
isLightningGame = true,
ignoreBestRecord = true,
overridePreShowText = "闪电赛已结束，请期待下次活动",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
preEndShowMatchName = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"双人"
},
playShow = "竞技",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
},
[23] = {
id = 23,
modeID = 3,
desc = "闪电赛",
sort = 9,
thumbImage = "CDN:T_ModelSelect_Img_Type_LightingS10",
image = "CDN:T_ModelSelectLarge_Img_Type_LightingS10",
detailDesc = "15",
categoryDescription = "跑酷",
modeGroup = "21|22|23",
matchTeamNum = {
1,
2,
3,
4
},
teamTag = "4",
matchRuleId = 23,
TeamMatchGame = true,
battlePlayerNum = 8,
dropId = 37,
mmrType = "WST_Lighting",
mmrAggrType = "MSAT_Highest",
warmRoundType = "WRST_Lighting",
NeedShowProcessDisplay = true,
isShowBattleRecord = true,
playTypeDesc = "提示：四人闪电赛，队伍成员共同计分和结算",
descShort = "速战速决，一轮决胜，勇夺闪电奖章！",
warmDropId = 40,
outImage = "T_Lobby_Start_Lightning",
pakGroup = 12001,
recordType = 15,
buttonDesc = "四人",
EnableRepGraph = true,
warmRoundRoomInfoId = 10123,
LowestVersion = "********",
gameModeType = 1,
overrideImageDesc = "闪电赛:新赛季关",
isOwnerStart = true,
qualifyTimeIsShow = true,
bShowTeamEnter = true,
displayLevelPath = "LetsGo_InLevelDisplayLevel_SideGame",
isLightningGame = true,
ignoreBestRecord = true,
overridePreShowText = "闪电赛已结束，请期待下次活动",
bSkipFlyEnter = true,
AutoDownloadPakGroup = {
12001
},
AutoDeletePakGroup = {
12001
},
PakPlayID = 1,
useBasicMusicGroup = true,
preEndShowMatchName = true,
bCloseMaxFPSOptimize = true,
playTagArr = {
"主玩法",
"休闲",
"四人"
},
playShow = "竞技",
jumpRandomLevelSequence = true,
battleDetailStyle = "System.InLevel.Bnb.Account.UI_BNB_Account_GDItem"
}
}

local mt = {
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = false,
showType = "UI_Model_SingleMatchTypeItem",
settleProc = "MTSC_Common",
battleRecordCnt = 30,
NeedShowProcessDisplay = false,
isShowBattleRecord = false,
battleRecordStyle = "UI_PlayerInfo_ModRecord_NomralBattleResult",
gameTypeId = 11800,
layoutID = 1,
isMainGame = 1,
isShowEmotionEntrance = 2,
EnableRepGraph = false,
isEnableAiLabIntervention = 0,
isOwnerStart = false,
UseDefaultChampionDisplayScene = false,
bShowTeamEnter = false,
isEnableAiDifficultyTest = 0,
playName = "Main",
useBasicMusicGroup = false,
bCloseMaxFPSOptimize = false,
SimpleModelMatchDesc = "竞技",
TeamMatchGame = false,
bUseDetailedRecordDisplay = false,
openAIHosting = false,
qualifyTimeIsShow = false,
isLightningGame = false,
ignoreBestRecord = false,
bSkipFlyEnter = false,
preEndShowMatchName = false,
isOpenRandEvent = false,
jumpRandomLevelSequence = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data