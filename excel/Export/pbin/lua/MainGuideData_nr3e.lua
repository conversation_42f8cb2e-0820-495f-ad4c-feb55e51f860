--com.tencent.wea.xlsRes.table_PlayerGuideData => excel/xls/X_新手引导表_nr3e.xlsx: 新引导总表

local v0 = "{c_grade = {grade = 1}}"

local data = {
[501010] = {
GuideID = 501010,
Comment = "副玩法1搜捕者",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekerPlayerGuide",
bEnabledInCloudGame = true,
bDisabledInPC = true
},
[501011] = {
GuideID = 501011,
Comment = "副玩法1搜捕者探查按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekerDetectGuide",
bEnabledInCloudGame = true,
bDisabledInPC = true
},
[501012] = {
GuideID = 501012,
Comment = "副玩法1躲藏者",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderPlayerGuide",
bEnabledInCloudGame = true,
bDisabledInPC = true
},
[501013] = {
GuideID = 501013,
Comment = "副玩法1搜捕者飞行背包按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeJetpackGuide",
bEnabledInCloudGame = true
},
[501014] = {
GuideID = 501014,
Comment = "副玩法1搜捕者爆破弹按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeBombGuide",
bEnabledInCloudGame = true
},
[501015] = {
GuideID = 501015,
Comment = "副玩法1搜捕者高爆雷按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeLandmineGuide",
bEnabledInCloudGame = true
},
[501016] = {
GuideID = 501016,
Comment = "副玩法1搜捕者雷达按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeRadartGuide",
bEnabledInCloudGame = true
},
[501017] = {
GuideID = 501017,
Comment = "副玩法1搜捕者磁铁按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeMagnetGuide",
bEnabledInCloudGame = true
},
[501018] = {
GuideID = 501018,
Comment = "副玩法1搜捕者诱捕帽按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeTrapGuide",
bEnabledInCloudGame = true
},
[501019] = {
GuideID = 501019,
Comment = "副玩法1伪装者护盾按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderShieldGuide",
bEnabledInCloudGame = true
},
[501020] = {
GuideID = 501020,
Comment = "副玩法1伪装者变身器按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderMimicryGuide",
bEnabledInCloudGame = true
},
[501021] = {
GuideID = 501021,
Comment = "副玩法1伪装者涂鸦手雷按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderGraffitiGuide",
bEnabledInCloudGame = true
},
[501022] = {
GuideID = 501022,
Comment = "副玩法1伪装者复制人偶按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderDuplicateGuide",
bEnabledInCloudGame = true
},
[501023] = {
GuideID = 501023,
Comment = "副玩法1伪装者传送门按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderOprtalGuide",
bEnabledInCloudGame = true
},
[501024] = {
GuideID = 501024,
Comment = "副玩法1伪装者诱捕帽按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderTrapGuide",
bEnabledInCloudGame = true
},
[501041] = {
GuideID = 501041,
Comment = "副玩法1伪装者雪人炮弹按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderSnowballGuide",
bEnabledInCloudGame = true
},
[501042] = {
GuideID = 501042,
Comment = "副玩法1伪装者守护精灵按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderSpiritGuide",
bEnabledInCloudGame = true
},
[501043] = {
GuideID = 501043,
Comment = "副玩法1搜捕者云朵按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeCloudGuide",
bEnabledInCloudGame = true
},
[501044] = {
GuideID = 501044,
Comment = "副玩法1搜捕者砰砰锤按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeGiantGuide",
bEnabledInCloudGame = true
},
[501025] = {
GuideID = 501025,
Comment = "副玩法1躲藏者PC",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderPlayerGuide_PC",
bEnabledInCloudGame = true
},
[501026] = {
GuideID = 501026,
Comment = "副玩法1搜捕者",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekerPlayerGuide_PC",
bEnabledInCloudGame = true
},
[501027] = {
GuideID = 501027,
Comment = "副玩法1搜捕者探查按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekerDetectGuide_PC",
bEnabledInCloudGame = true
},
[501028] = {
GuideID = 501028,
Comment = "副玩法1搜捕者飞行背包按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeJetpackGuide_PC",
bEnabledInCloudGame = true
},
[501029] = {
GuideID = 501029,
Comment = "副玩法1搜捕者爆破弹按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeBombGuide_PC",
bEnabledInCloudGame = true
},
[501030] = {
GuideID = 501030,
Comment = "副玩法1搜捕者高爆雷按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeLandmineGuide_PC",
bEnabledInCloudGame = true
},
[501031] = {
GuideID = 501031,
Comment = "副玩法1搜捕者雷达按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeRadartGuide_PC",
bEnabledInCloudGame = true
},
[501032] = {
GuideID = 501032,
Comment = "副玩法1搜捕者磁铁按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeMagnetGuide_PC",
bEnabledInCloudGame = true
},
[501033] = {
GuideID = 501033,
Comment = "副玩法1搜捕者诱捕帽按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeTrapGuide_PC",
bEnabledInCloudGame = true
},
[501034] = {
GuideID = 501034,
Comment = "副玩法1伪装者护盾按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderShieldGuide_PC",
bEnabledInCloudGame = true
},
[501035] = {
GuideID = 501035,
Comment = "副玩法1伪装者变身器按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderMimicryGuide_PC",
bEnabledInCloudGame = true
},
[501036] = {
GuideID = 501036,
Comment = "副玩法1伪装者涂鸦手雷按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderGraffitiGuide_PC",
bEnabledInCloudGame = true
},
[501037] = {
GuideID = 501037,
Comment = "副玩法1伪装者复制人偶按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderDuplicateGuide_PC",
bEnabledInCloudGame = true
},
[501038] = {
GuideID = 501038,
Comment = "副玩法1伪装者传送门按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderOprtalGuide_PC",
bEnabledInCloudGame = true
},
[501039] = {
GuideID = 501039,
Comment = "副玩法1伪装者诱捕帽按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderTrapGuide_PC",
bEnabledInCloudGame = true
},
[501045] = {
GuideID = 501045,
Comment = "副玩法1伪装者雪人炮弹按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderSnowballGuide_PC",
bEnabledInCloudGame = true
},
[501046] = {
GuideID = 501046,
Comment = "副玩法1伪装者守护精灵按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1HiderSpiritGuide_PC",
bEnabledInCloudGame = true
},
[501047] = {
GuideID = 501047,
Comment = "副玩法1搜捕者云朵按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeCloudGuide_PC",
bEnabledInCloudGame = true
},
[501048] = {
GuideID = 501048,
Comment = "副玩法1搜捕者砰砰锤按钮",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SeekeGiantGuide_PC",
bEnabledInCloudGame = true
},
[501040] = {
GuideID = 501040,
Comment = "躲猫猫阵营选择界面新手引导",
StartConditions = v0,
TriggerEventParam = "ON_NR3E1SideChoose",
bEnabledInCloudGame = true
},
[502010] = {
GuideID = 502010,
Comment = "副玩法2卧底角色",
StartConditions = v0,
TriggerEventParam = "ON_NR3E2BadPlayerGuide",
bEnabledInCloudGame = true
},
[502011] = {
GuideID = 502011,
Comment = "副玩法2警察角色",
StartConditions = v0,
TriggerEventParam = "ON_NR3E2PolicePlayerGuide",
bEnabledInCloudGame = true
},
[502012] = {
GuideID = 502012,
Comment = "副玩法2星宝角色",
StartConditions = v0,
TriggerEventParam = "ON_NR3E2NormalPlayerGuide",
bEnabledInCloudGame = true
},
[502013] = {
GuideID = 502013,
Comment = "副玩法2星宝角色捡枪提示",
StartConditions = v0,
TriggerEventParam = "NR3E2_PoliceDeadInGame",
bEnabledInCloudGame = true
},
[502014] = {
GuideID = 502014,
Comment = "副玩法2卧底角色放置陷阱",
StartConditions = v0,
TriggerEventParam = "ON_NR3E2BadPlayerGuidePutTrap",
bEnabledInCloudGame = true
},
[502015] = {
GuideID = 502015,
Comment = "副玩法2卧底角色挥棒攻击",
StartConditions = v0,
TriggerEventParam = "ON_NR3E2BadPlayerGuideWieldWeapon",
bEnabledInCloudGame = true
},
[502016] = {
GuideID = 502016,
Comment = "副玩法2卧底角色挥棒攻击PC",
StartConditions = v0,
TriggerEventParam = "ON_NR3E2BadPlayerGuideWieldWeapon_PC",
bEnabledInCloudGame = true
},
[502017] = {
GuideID = 502017,
Comment = "副玩法2卧底角色放置陷阱PC",
StartConditions = v0,
TriggerEventParam = "ON_NR3E2BadPlayerGuidePutTrap_PC",
bEnabledInCloudGame = true
},
[503010] = {
GuideID = 503010,
Comment = "副玩法3平民阵营",
TriggerEventParam = "ON_NR3E3GoodPlayerGuide",
TriggerFinishEvent = "ON_NR3E3GoodPlayerTaskGuide",
bEnabledInCloudGame = true
},
[503011] = {
GuideID = 503011,
Comment = "副玩法3平民阵营会议",
TriggerEventParam = "ON_NR3E3GoodPlayerGuideMeetingMulti",
TriggerFinishEvent = "ON_NR3E3PlayerGuideVoice",
bEnabledInCloudGame = true
},
[503012] = {
GuideID = 503012,
Comment = "副玩法3平民被淘汰",
TriggerEventParam = "ON_NR3E3GoodPlayerGuideOut",
bEnabledInCloudGame = true
},
[503013] = {
GuideID = 503013,
Comment = "副玩法3狼人阵营",
TriggerEventParam = "ON_NR3E3BadPlayerGuide",
TriggerFinishEvent = "ON_NR3E3_HunterAttack",
bEnabledInCloudGame = true
},
[503014] = {
GuideID = 503014,
Comment = "副玩法3狼人阵营会议",
TriggerEventParam = "ON_NR3E3BadPlayerGuideMeetingMulti",
TriggerFinishEvent = "ON_NR3E3PlayerGuideVoice",
bEnabledInCloudGame = true
},
[503015] = {
GuideID = 503015,
Comment = "副玩法3狼人被淘汰",
TriggerEventParam = "ON_NR3E3BadPlayerGuideOut",
bEnabledInCloudGame = true
},
[503016] = {
GuideID = 503016,
Comment = "副玩法3平民触碰任务点",
TriggerEventParam = "ON_NR3E3_FirstTouchTaskPoint",
bEnabledInCloudGame = true
},
[503017] = {
GuideID = 503017,
Comment = "副玩法3平民任务完成提示",
TriggerEventParam = "ON_NR3E3_FirstTaskComplete",
bEnabledInCloudGame = true
},
[503018] = {
GuideID = 503018,
Comment = "副玩法3平民会议结束",
TriggerEventParam = "ON_NR3E3_MeetingComplete",
bEnabledInCloudGame = true
},
[503019] = {
GuideID = 503019,
Comment = "副玩法3会议语音提示",
TriggerEventParam = "ON_NR3E3PlayerGuideVoice",
bEnabledInCloudGame = true
},
[503020] = {
GuideID = 503020,
Comment = "副玩法3平民阵营会议",
TriggerEventParam = "ON_NR3E3GoodPlayerGuideMeeting",
bEnabledInCloudGame = true
},
[503021] = {
GuideID = 503021,
Comment = "副玩法3狼人阵营会议",
TriggerEventParam = "ON_NR3E3BadPlayerGuideMeeting",
bEnabledInCloudGame = true
},
[503022] = {
GuideID = 503022,
Comment = "副玩法3狼人紧急任务提示",
TriggerEventParam = "ON_NR3E3BadEmergentTask",
bEnabledInCloudGame = true
},
[503023] = {
GuideID = 503023,
Comment = "副玩法3举报引导",
TriggerEventParam = "ON_NR3E3ReportGuide",
bEnabledInCloudGame = true
},
[503024] = {
GuideID = 503024,
Comment = "副玩法3狼人攻击提示",
TriggerEventParam = "ON_NR3E3_HunterAttack",
bEnabledInCloudGame = true
},
[503025] = {
GuideID = 503025,
Comment = "副玩法3新手局狼人首次攻击引导",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_FirstHunterAttack",
bEnabledInCloudGame = true
},
[503026] = {
GuideID = 503026,
Comment = "副玩法3新手局狼人二次攻击引导",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_SecondHunterAttack",
bEnabledInCloudGame = true
},
[503027] = {
GuideID = 503027,
Comment = "副玩法3新手局狼人二次进入会议",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_SecondMeeting",
bEnabledInCloudGame = true
},
[503028] = {
GuideID = 503028,
Comment = "副玩法3新手局狼人二次进入会议",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_SecondMeeting_No1Discuss",
bEnabledInCloudGame = true
},
[503029] = {
GuideID = 503029,
Comment = "副玩法3新手局狼人二次进入会议",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_SecondMeeting_Vote",
bEnabledInCloudGame = true
},
[503030] = {
GuideID = 503030,
Comment = "副玩法3新手局狼人阵营首次进入会议",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_FirstMeeting",
bEnabledInCloudGame = true
},
[503031] = {
GuideID = 503031,
Comment = "副玩法3新手局狼人阵营首次投票",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_FirstMeeting_Vote",
bEnabledInCloudGame = true
},
[503032] = {
GuideID = 503032,
Comment = "副玩法3新手局狼人阵营首次游戏",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_FirstPlay",
TriggerFinishEvent = "ON_NR3E3_NewComer_FinishFirstPlay"
},
[503033] = {
GuideID = 503033,
Comment = "副玩法3新手局狼人阵营逃离",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_Leave",
bEnabledInCloudGame = true
},
[503034] = {
GuideID = 503034,
Comment = "点击选择地图",
TriggerEventParam = "UI_Preparations_View_Map",
TriggerFinishEvent = "UI_Preparations_View_MapFinish",
bEnabledInCloudGame = true
},
[503035] = {
GuideID = 503035,
Comment = "点击预选身份",
TriggerEventParam = "UI_Preparations_View_IdentityGuid",
TriggerFinishEvent = "ON_UI_Preparations_View_IdentityFinish",
bEnabledInCloudGame = true
},
[503036] = {
GuideID = 503036,
Comment = "副玩法3平民阵营任务引导",
TriggerEventParam = "ON_NR3E3GoodPlayerTaskGuide",
TriggerFinishEvent = "ON_NR3E3GoodPlayerTaskGuide_End",
bEnabledInCloudGame = true
},
[503037] = {
GuideID = 503037,
Comment = "副玩法3引导使用道具点人",
TriggerEventParam = "ON_NR3E3_MeetingInteraction",
TriggerFinishEvent = "ON_NR3E3_MeetingInteraction2"
},
[503038] = {
GuideID = 503038,
Comment = "副玩法3引导使用道具点互动按钮",
TriggerEventParam = "ON_NR3E3_MeetingInteraction2"
},
[503039] = {
GuideID = 503039,
Comment = "副玩法3引导使用表情点人",
TriggerEventParam = "ON_NR3E3_MeetingEmoji"
},
[503040] = {
GuideID = 503040,
Comment = "点击选择地图1",
TriggerEventParam = "UI_Preparations_View_Map1",
TriggerFinishEvent = "UI_Preparations_View_MapFinish1",
bEnabledInCloudGame = true
},
[503041] = {
GuideID = 503041,
Comment = "来了解下双人模式的规则吧",
TriggerEventParam = "ON_NR3E3_NR3E3_SettingsRule",
bEnabledInCloudGame = true
},
[503042] = {
GuideID = 503042,
Comment = "副玩法3新手局狼人阵营首次游戏_PC",
Priority = 2,
StartConditions = "{c_isNR3E3WarmRound = {levelId = 50301,isWarmRound = true}}",
TriggerEventParam = "ON_NR3E3_NewComer_FirstPlay_PC",
TriggerFinishEvent = "ON_NR3E3_NewComer_FinishFirstPlay"
},
[503043] = {
GuideID = 503043,
Comment = "副玩法3狼人阵营PC",
TriggerEventParam = "ON_NR3E3BadPlayerGuide_PC",
TriggerFinishEvent = "ON_NR3E3_HunterAttack_PC",
bEnabledInCloudGame = true
},
[503044] = {
GuideID = 503044,
Comment = "副玩法3狼人紧急任务提示PC",
TriggerEventParam = "ON_NR3E3BadEmergentTask_PC",
bEnabledInCloudGame = true
},
[503045] = {
GuideID = 503045,
Comment = "副玩法3狼人攻击提示PC",
TriggerEventParam = "ON_NR3E3_HunterAttack_PC",
bEnabledInCloudGame = true
},
[503046] = {
GuideID = 503046,
Comment = "副玩法3平民触碰任务点PC",
TriggerEventParam = "ON_NR3E3_FirstTouchTaskPoint_PC",
bEnabledInCloudGame = true
},
[503047] = {
GuideID = 503047,
Comment = "副玩法3狼人阵营会议PC",
TriggerEventParam = "ON_NR3E3BadPlayerGuideMeetingMulti_PC",
TriggerFinishEvent = "ON_NR3E3PlayerGuideVoice_PC",
bEnabledInCloudGame = true
},
[503048] = {
GuideID = 503048,
Comment = "副玩法3平民阵营会议PC",
TriggerEventParam = "ON_NR3E3GoodPlayerGuideMeetingMulti_PC",
TriggerFinishEvent = "ON_NR3E3PlayerGuideVoice_PC",
bEnabledInCloudGame = true
},
[503049] = {
GuideID = 503049,
Comment = "副玩法3会议语音提示PC",
TriggerEventParam = "ON_NR3E3PlayerGuideVoice_PC",
bEnabledInCloudGame = true
},
[503050] = {
GuideID = 503050,
Comment = "备战界面引导返回大厅按钮",
TriggerEventParam = "ON_Preparations_View_GoBack",
TriggerFinishEvent = "ON_Preparations_View_GoBackEnd",
bEnabledInCloudGame = true
},
[503051] = {
GuideID = 503051,
Comment = "副玩法狼人管道提示PC",
TriggerEventParam = "ON_NR3E3BadPlayerGuideInHole_PC",
TriggerFinishEvent = "ON_NR3E3BadPlayerGuideInHole_PCEnd"
},
[503052] = {
GuideID = 503052,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局引导",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_StartGameGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Prey_StartGameGuideEnd",
bEnabledInCloudGame = true
},
[503053] = {
GuideID = 503053,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局挖宝引导",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_DiggingTreasureGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Prey_DiggingTreasureGuideEnd",
bEnabledInCloudGame = true
},
[503054] = {
GuideID = 503054,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局搬起引导",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_LiftTreasureGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Prey_LiftTreasureGuideEnd",
bEnabledInCloudGame = true
},
[503055] = {
GuideID = 503055,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局上交引导-查看地图",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_CheckMapGuide",
TriggerFinishEvent = "ON_NR3E3TreasureHuntRule_Prey_CheckNPCGuide",
bEnabledInCloudGame = true
},
[503056] = {
GuideID = 503056,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局上交引导-查看NPC",
StartConditions = "{c_isTreasureHuntPrey = {},c_guideId = {guideId = 503055} }",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_CheckNPCGuide",
bEnabledInCloudGame = true
},
[503057] = {
GuideID = 503057,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局上交宝物引导",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_HandInTreasureGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Prey_HandInTreasureGuideEnd",
bEnabledInCloudGame = true
},
[503058] = {
GuideID = 503058,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局查看小店引导",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_CheckShopGuide",
TriggerFinishEvent = "ON_NR3E3TreasureHuntRule_Prey_OpenShopGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Prey_CheckShopGuideEnd",
bEnabledInCloudGame = true
},
[503059] = {
GuideID = 503059,
Comment = "副玩法3狼人秘境寻宝(平民阵营)开局查看道具引导",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_CheckTreasureGuide",
bEnabledInCloudGame = true
},
[503060] = {
GuideID = 503060,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)开局引导",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_StartGameGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Hunter_StartGameGuideEnd",
bEnabledInCloudGame = true
},
[503061] = {
GuideID = 503061,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)挖宝引导",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_DiggingTreasureGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Hunter_DiggingTreasureGuideEnd",
bEnabledInCloudGame = true
},
[503062] = {
GuideID = 503062,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)搬起引导",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_LiftTreasureGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Hunter_LiftTreasureGuideEnd",
bEnabledInCloudGame = true
},
[503063] = {
GuideID = 503063,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)上交引导-查看地图",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_CheckMapGuide",
TriggerFinishEvent = "ON_NR3E3TreasureHuntRule_Hunter_CheckNPCGuide",
bEnabledInCloudGame = true
},
[503064] = {
GuideID = 503064,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)上交引导-查看NPC",
StartConditions = "{c_isTreasureHuntHunter = {},c_guideId = {guideId = 503063}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_CheckNPCGuide",
bEnabledInCloudGame = true
},
[503065] = {
GuideID = 503065,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)上交宝物引导",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_HandInTreasureGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Hunter_HandInTreasureGuideEnd",
bEnabledInCloudGame = true
},
[503066] = {
GuideID = 503066,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)查看小店引导",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_CheckShopGuide",
TriggerFinishEvent = "ON_NR3E3TreasureHuntRule_Hunter_OpenShopGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Hunter_CheckShopGuideEnd",
bEnabledInCloudGame = true
},
[503067] = {
GuideID = 503067,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)查看道具引导",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_CheckTreasureGuide",
bEnabledInCloudGame = true
},
[503068] = {
GuideID = 503068,
Comment = "副玩法3狼人秘境寻宝(平民阵营)打开小店引导",
StartConditions = "{c_isTreasureHuntPrey = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Prey_OpenShopGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Prey_OpenShopGuideEnd",
bEnabledInCloudGame = true
},
[503069] = {
GuideID = 503069,
Comment = "副玩法3狼人秘境寻宝(狼人阵营)打开小店引导",
StartConditions = "{c_isTreasureHuntHunter = {}}",
TriggerEventParam = "ON_NR3E3TreasureHuntRule_Hunter_OpenShopGuide",
EndEvent = "ON_NR3E3TreasureHuntRule_Hunter_OpenShopGuideEnd",
bEnabledInCloudGame = true
}
}

local mt = {
Priority = 1,
TriggerEventType = 1,
FinishEvent = "KeyStepCompleteEvent",
CanBeJump = false,
bEnabledInCloudGame = false,
bDisabledInPC = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data