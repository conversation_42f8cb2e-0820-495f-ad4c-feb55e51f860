--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_环绕物.xlsx: 环绕物

local data = {
[750001] = {
id = 750001,
type = "ItemType_Surroundings",
quality = 2,
name = "青莲落",
desc = "行至水穷处，青莲自留香",
icon = "CDN:T_Common_Item_System_Bag_159",
outlookConf = {
fashionValue = 125
},
shareTexts = {
"凋落是另一种开始"
},
surroundingsEffectId = {
850002
},
surroundingsDisplayType = 0
},
[750002] = {
id = 750002,
type = "ItemType_Surroundings",
quality = 1,
name = "红莲生",
desc = "水上红莲生，灼灼夏日长",
icon = "CDN:T_Common_Item_System_Bag_158",
outlookConf = {
fashionValue = 125
},
shareTexts = {
"为你，永远盛开"
},
surroundingsEffectId = {
850001,
850002
},
surroundingsDisplayType = 1
},
[750003] = {
id = 750003,
type = "ItemType_Surroundings",
quality = 1,
name = "红莲生",
desc = "水上红莲生，灼灼夏日长",
icon = "CDN:T_Common_Item_System_Bag_158",
outlookConf = {
fashionValue = 125
},
shareTexts = {
"为你，永远盛开"
},
surroundingsEffectId = {
850002
},
surroundingsDisplayType = 0
},
[750004] = {
id = 750004,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850003
},
surroundingsDisplayType = 0
},
[750005] = {
id = 750005,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物1",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850004,
850005
},
surroundingsDisplayType = 1
},
[750006] = {
id = 750006,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物2",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850004,
850006
},
surroundingsDisplayType = 1
},
[750007] = {
id = 750007,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物3",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850004,
850007
},
surroundingsDisplayType = 1
},
[750008] = {
id = 750008,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物1",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850005
},
surroundingsDisplayType = 0
},
[750009] = {
id = 750009,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物2",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850006
},
surroundingsDisplayType = 0
},
[750010] = {
id = 750010,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物3",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850007
},
surroundingsDisplayType = 0
},
[750011] = {
id = 750011,
type = "ItemType_Surroundings",
quality = 1,
name = "测试环绕物999",
desc = "测试环绕物",
icon = "CDN:T_Common_Item_System_Footprint",
outlookConf = {
fashionValue = 125
},
surroundingsEffectId = {
850007
},
surroundingsDisplayType = 0
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data