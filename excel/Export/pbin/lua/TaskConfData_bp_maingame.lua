--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/B_BP通行证_主玩法.xlsx: 任务配置

local v0 = {
itemIdList = {
3610
},
numList = {
200
}
}

local data = {
[85001] = {
id = 85001,
name = "在天天晋级赛（排位）中获得前16名",
desc = "在天天晋级赛（排位）中获得前16名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
2
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
25
}
},
taskGroupId = 13900
},
[85002] = {
id = 85002,
name = "在天天晋级赛（排位）中使用2次道具	",
desc = "在天天晋级赛（排位）中使用2次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 2,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
25
}
},
taskGroupId = 13900
},
[85003] = {
id = 85003,
name = "完成天天晋级赛（排位）3次",
desc = "完成天天晋级赛（排位）3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
50
}
},
taskGroupId = 13901
},
[85004] = {
id = 85004,
name = "完成1次天天晋级赛（排位）中的双人/四人模式",
desc = "完成1次天天晋级赛（排位）中的双人/四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
50
}
},
jumpId = 1152,
taskGroupId = 13901
},
[85005] = {
id = 85005,
reward = {
itemIdList = {
3610
},
numList = {
50
}
},
taskGroupId = 13901
},
[85006] = {
id = 85006,
taskGroupId = 13902
},
[85007] = {
id = 85007,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
taskGroupId = 13902
},
[85008] = {
id = 85008,
name = "新春晋级礼活动等级达到5级",
desc = "新春晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
taskGroupId = 13902
},
[85009] = {
id = 85009,
name = "完成第一周其余3个每周任务",
desc = "完成第一周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85006,
85007,
85008
}
}
}
}
}
}
},
taskGroupId = 13902
},
[85010] = {
id = 85010,
taskGroupId = 13903
},
[85011] = {
id = 85011,
name = "完成2次天天晋级赛（排位）中的四人模式",
desc = "完成2次天天晋级赛（排位）中的四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
jumpId = 1153,
taskGroupId = 13903
},
[85012] = {
id = 85012,
name = "新春晋级礼活动等级达到10级",
desc = "新春晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
taskGroupId = 13903
},
[85013] = {
id = 85013,
name = "完成第二周其余3个每周任务",
desc = "完成第二周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85010,
85011,
85012
}
}
}
}
}
}
},
taskGroupId = 13903
},
[85014] = {
id = 85014,
taskGroupId = 13904
},
[85015] = {
id = 85015,
name = "在天天晋级赛（排位）竞速关获得第一名1次",
desc = "在天天晋级赛（排位）竞速关获得第一名1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 11,
value = {
1
}
},
{
type = 50,
value = {
2,
1,
1
}
}
}
}
}
}
},
taskGroupId = 13904
},
[85016] = {
id = 85016,
name = "新春晋级礼活动等级达到15级",
desc = "新春晋级礼活动等级达到15级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 15,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
taskGroupId = 13904
},
[85017] = {
id = 85017,
name = "完成第三周其余3个每周任务",
desc = "完成第三周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85014,
85015,
85016
}
}
}
}
}
}
},
taskGroupId = 13904
},
[85018] = {
id = 85018,
taskGroupId = 13905
},
[85019] = {
id = 85019,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
taskGroupId = 13905
},
[85020] = {
id = 85020,
name = "新春晋级礼活动等级达到20级",
desc = "新春晋级礼活动等级达到20级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 20,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
taskGroupId = 13905
},
[85021] = {
id = 85021,
name = "完成第四周其余3个每周任务",
desc = "完成第四周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85018,
85019,
85020
}
}
}
}
}
}
},
taskGroupId = 13905
},
[85022] = {
id = 85022,
taskGroupId = 13906
},
[85023] = {
id = 85023,
name = "完成2次天天晋级赛（排位）中的四人模式",
desc = "完成2次天天晋级赛（排位）中的四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
jumpId = 1153,
taskGroupId = 13906
},
[85024] = {
id = 85024,
name = "新春晋级礼活动等级达到25级",
desc = "新春晋级礼活动等级达到25级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 25,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
taskGroupId = 13906
},
[85025] = {
id = 85025,
name = "完成第五周其余3个每周任务",
desc = "完成第五周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85022,
85023,
85024
}
}
}
}
}
}
},
taskGroupId = 13906
},
[85026] = {
id = 85026,
taskGroupId = 13907
},
[85027] = {
id = 85027,
name = "在天天晋级赛（排位）竞速关获得第一名1次",
desc = "在天天晋级赛（排位）竞速关获得第一名1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 11,
value = {
1
}
},
{
type = 50,
value = {
2,
1,
1
}
}
}
}
}
}
},
taskGroupId = 13907
},
[85028] = {
id = 85028,
name = "新春晋级礼活动等级达到30级",
desc = "新春晋级礼活动等级达到30级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 30,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
taskGroupId = 13907
},
[85029] = {
id = 85029,
name = "完成第六周其余3个每周任务",
desc = "完成第六周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85026,
85027,
85028
}
}
}
}
}
}
},
taskGroupId = 13907
},
[85030] = {
id = 85030,
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
taskGroupId = 13908
},
[85031] = {
id = 85031,
name = "在天天晋级赛（排位）中使用20次道具	",
desc = "在天天晋级赛（排位）中使用20次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 20,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
taskGroupId = 13908
},
[85032] = {
id = 85032,
name = "新春晋级礼活动等级达到35级",
desc = "新春晋级礼活动等级达到35级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 35,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
taskGroupId = 13908
},
[85033] = {
id = 85033,
name = "完成第七周其余3个每周任务",
desc = "完成第七周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85030,
85031,
85032
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
taskGroupId = 13908
},
[85034] = {
id = 85034,
name = "在天天晋级赛（排位）中夺冠1次",
desc = "在天天晋级赛（排位）中夺冠1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
taskGroupId = 13909
},
[85035] = {
id = 85035,
name = "完成4次天天晋级赛（排位）中的四人模式",
desc = "完成4次天天晋级赛（排位）中的四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 4,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
jumpId = 1153,
taskGroupId = 13909
},
[85036] = {
id = 85036,
name = "新春晋级礼活动等级达到40级",
desc = "新春晋级礼活动等级达到40级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 40,
subConditionList = {
{
type = 504,
value = {
4001
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
taskGroupId = 13909
},
[85037] = {
id = 85037,
name = "完成第八周其余3个每周任务",
desc = "完成第八周其余3个每周任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 121,
value = 3,
subConditionList = {
{
type = 4,
value = {
85034,
85035,
85036
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
250
}
},
taskGroupId = 13909
},
[85038] = {
id = 85038,
name = "在天天晋级赛（排位）中获得前16名",
desc = "在天天晋级赛（排位）中获得前16名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
2
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 13910
},
[85039] = {
id = 85039,
name = "在天天晋级赛（排位）中使用2次道具	",
desc = "在天天晋级赛（排位）中使用2次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 2,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 13910
},
[85040] = {
id = 85040,
name = "完成天天晋级赛（排位）3次",
desc = "完成天天晋级赛（排位）3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 13911
},
[85041] = {
id = 85041,
name = "完成1次天天晋级赛（排位）中的双人/四人模式",
desc = "完成1次天天晋级赛（排位）中的双人/四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
30
}
},
jumpId = 1152,
taskGroupId = 13911
},
[85042] = {
id = 85042,
reward = {
itemIdList = {
3610
},
numList = {
30
}
},
taskGroupId = 13911
},
[85043] = {
id = 85043,
name = "【赛季挑战】本赛季中完成25次天天晋级赛（排位）四人模式",
desc = "【赛季挑战】本赛季中完成25次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 25,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1200
}
},
jumpId = 1153,
taskGroupId = 13912
},
[85044] = {
id = 85044,
reward = v0,
taskGroupId = 13912
},
[85045] = {
id = 85045,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1153,
taskGroupId = 13912
},
[85046] = {
id = 85046,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4002
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
300
}
},
taskGroupId = 13912
},
[85047] = {
id = 85047,
reward = v0,
taskGroupId = 13913
},
[85048] = {
id = 85048,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 13913
},
[85049] = {
id = 85049,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4002
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
300
}
},
taskGroupId = 13913
},
[85051] = {
id = 85051,
reward = v0,
taskGroupId = 13914
},
[85052] = {
id = 85052,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1153,
taskGroupId = 13914
},
[85053] = {
id = 85053,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4002
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
300
}
},
taskGroupId = 13914
},
[85055] = {
id = 85055,
reward = v0,
taskGroupId = 13915
},
[85056] = {
id = 85056,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 13915
},
[85057] = {
id = 85057,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4002
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
300
}
},
taskGroupId = 13915
},
[85059] = {
id = 85059,
reward = v0,
taskGroupId = 13916
},
[85060] = {
id = 85060,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1153,
taskGroupId = 13916
},
[85061] = {
id = 85061,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4002
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
300
}
},
taskGroupId = 13916
},
[85063] = {
id = 85063,
reward = v0,
taskGroupId = 13917
},
[85064] = {
id = 85064,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 13917
},
[85065] = {
id = 85065,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4002
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
300
}
},
taskGroupId = 13917
},
[85067] = {
id = 85067,
reward = v0,
taskGroupId = 13918
},
[85068] = {
id = 85068,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1153,
taskGroupId = 13918
},
[85069] = {
id = 85069,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4002
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
300
}
},
taskGroupId = 13918
},
[85070] = {
id = 85070,
name = "在天天晋级赛（排位）中获得前16名",
desc = "在天天晋级赛（排位）中获得前16名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
2
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 12000
},
[85071] = {
id = 85071,
name = "在天天晋级赛（排位）中使用2次道具	",
desc = "在天天晋级赛（排位）中使用2次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 2,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 12000
},
[85072] = {
id = 85072,
name = "完成天天晋级赛（排位）3次",
desc = "完成天天晋级赛（排位）3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 12001
},
[85073] = {
id = 85073,
name = "完成1次天天晋级赛（排位）中的双人/四人模式",
desc = "完成1次天天晋级赛（排位）中的双人/四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
30
}
},
jumpId = 1152,
taskGroupId = 12001
},
[85074] = {
id = 85074,
reward = {
itemIdList = {
3610
},
numList = {
30
}
},
taskGroupId = 12001
},
[85075] = {
id = 85075,
name = "【赛季挑战】本赛季中完成25次天天晋级赛（排位）四人模式",
desc = "【赛季挑战】本赛季中完成25次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 25,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
jumpId = 1153,
taskGroupId = 12002
},
[85076] = {
id = 85076,
name = "【赛季挑战】在天天晋级赛（排位）中累计获得技巧分不低于60",
desc = "【赛季挑战】在天天晋级赛（排位）中累计获得技巧分不低于60",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 858,
value = 60,
subConditionList = {
{
type = 203,
value = {
766
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
taskGroupId = 12002
},
[85077] = {
id = 85077,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12002
},
[85078] = {
id = 85078,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12002
},
[85079] = {
id = 85079,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12002
},
[85080] = {
id = 85080,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12003
},
[85081] = {
id = 85081,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12003
},
[85082] = {
id = 85082,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12003
},
[85083] = {
id = 85083,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12004
},
[85084] = {
id = 85084,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12004
},
[85085] = {
id = 85085,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12004
},
[85086] = {
id = 85086,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12005
},
[85087] = {
id = 85087,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12005
},
[85088] = {
id = 85088,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12005
},
[85089] = {
id = 85089,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12006
},
[85090] = {
id = 85090,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12006
},
[85091] = {
id = 85091,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12006
},
[85092] = {
id = 85092,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12007
},
[85093] = {
id = 85093,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12007
},
[85094] = {
id = 85094,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12007
},
[85095] = {
id = 85095,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12008
},
[85096] = {
id = 85096,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12008
},
[85097] = {
id = 85097,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12008
},
[85098] = {
id = 85098,
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12009
},
[85099] = {
id = 85099,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12009
},
[85100] = {
id = 85100,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4003
}
}
}
}
}
}
},
reward = v0,
taskGroupId = 12009
},
[85101] = {
id = 85101,
name = "在天天晋级赛（排位）中获得前16名",
desc = "在天天晋级赛（排位）中获得前16名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
2
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 12010
},
[85102] = {
id = 85102,
name = "在天天晋级赛（排位）中使用2次道具	",
desc = "在天天晋级赛（排位）中使用2次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 2,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 12010
},
[85103] = {
id = 85103,
name = "完成天天晋级赛（排位）3次",
desc = "完成天天晋级赛（排位）3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
20
}
},
taskGroupId = 12011
},
[85104] = {
id = 85104,
name = "完成1次天天晋级赛（排位）中的双人/四人模式",
desc = "完成1次天天晋级赛（排位）中的双人/四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
30
}
},
jumpId = 1152,
taskGroupId = 12011
},
[85105] = {
id = 85105,
reward = {
itemIdList = {
3610
},
numList = {
30
}
},
taskGroupId = 12011
}
}

local mt = {
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
125
}
},
jumpId = 1080
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data