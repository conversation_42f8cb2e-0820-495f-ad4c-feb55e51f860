--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_策划专用.xlsx: UGC任务

local v0 = 14011

local v1 = 14012

local v2 = 32

local data = {
[5663] = {
id = 5663,
name = "积分达到10",
desc = "积分达到10",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 10,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5664] = {
id = 5664,
name = "积分达到20",
desc = "积分达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 20,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
520230
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5665] = {
id = 5665,
name = "积分达到30",
desc = "积分达到30",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 30,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5666] = {
id = 5666,
name = "积分达到40",
desc = "积分达到40",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 40,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5667] = {
id = 5667,
name = "积分达到50",
desc = "积分达到50",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 50,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
510341
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5668] = {
id = 5668,
name = "积分达到70",
desc = "积分达到70",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 70,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5669] = {
id = 5669,
name = "积分达到80",
desc = "积分达到80",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 80,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5670] = {
id = 5670,
name = "积分达到100",
desc = "积分达到100",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 100,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
620790
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5671] = {
id = 5671,
name = "积分达到120",
desc = "积分达到120",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 120,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
4000
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5672] = {
id = 5672,
name = "积分达到130",
desc = "积分达到130",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 130,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
40
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5673] = {
id = 5673,
name = "积分达到150",
desc = "积分达到150",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 150,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
720988
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5674] = {
id = 5674,
name = "积分达到185",
desc = "积分达到185",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 185,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5675] = {
id = 5675,
name = "积分达到200",
desc = "积分达到200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 200,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
410580
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5676] = {
id = 5676,
name = "积分达到220",
desc = "积分达到220",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 220,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5677] = {
id = 5677,
name = "积分达到240",
desc = "积分达到240",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 240,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
3000
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5678] = {
id = 5678,
name = "积分达到260",
desc = "积分达到260",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 260,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5679] = {
id = 5679,
name = "积分达到280",
desc = "积分达到280",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 280,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
3000
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5680] = {
id = 5680,
name = "积分达到300",
desc = "积分达到300",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 300,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5681] = {
id = 5681,
name = "积分达到320",
desc = "积分达到320",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 320,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5682] = {
id = 5682,
name = "积分达到340",
desc = "积分达到340",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 340,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
40
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5683] = {
id = 5683,
name = "积分达到360",
desc = "积分达到360",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 360,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5684] = {
id = 5684,
name = "积分达到380",
desc = "积分达到380",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 380,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
4000
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5685] = {
id = 5685,
name = "积分达到400",
desc = "积分达到400",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 400,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14011
},
[5686] = {
id = 5686,
name = "积分达到1",
desc = "积分达到1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
530221
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5687] = {
id = 5687,
name = "积分达到10",
desc = "积分达到10",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 10,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5688] = {
id = 5688,
name = "积分达到20",
desc = "积分达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 20,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
520245
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5689] = {
id = 5689,
name = "积分达到30",
desc = "积分达到30",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 30,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5690] = {
id = 5690,
name = "积分达到40",
desc = "积分达到40",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 40,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5691] = {
id = 5691,
name = "积分达到50",
desc = "积分达到50",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 50,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
510367
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5692] = {
id = 5692,
name = "积分达到70",
desc = "积分达到70",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 70,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5693] = {
id = 5693,
name = "积分达到80",
desc = "积分达到80",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 80,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5694] = {
id = 5694,
name = "积分达到100",
desc = "积分达到100",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 100,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
620890
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5695] = {
id = 5695,
name = "积分达到120",
desc = "积分达到120",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 120,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
4000
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5696] = {
id = 5696,
name = "积分达到130",
desc = "积分达到130",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 130,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
40
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5697] = {
id = 5697,
name = "积分达到150",
desc = "积分达到150",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 150,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
722033
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5698] = {
id = 5698,
name = "积分达到185",
desc = "积分达到185",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 185,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5699] = {
id = 5699,
name = "积分达到200",
desc = "积分达到200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 200,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
411080
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5700] = {
id = 5700,
name = "积分达到220",
desc = "积分达到220",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 220,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5702] = {
id = 5702,
name = "积分达到240",
desc = "积分达到240",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 240,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
3000
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5703] = {
id = 5703,
name = "积分达到260",
desc = "积分达到260",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 260,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5704] = {
id = 5704,
name = "积分达到280",
desc = "积分达到280",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 280,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
3000
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5705] = {
id = 5705,
name = "积分达到300",
desc = "积分达到300",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 300,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5706] = {
id = 5706,
name = "积分达到320",
desc = "积分达到320",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 320,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5707] = {
id = 5707,
name = "积分达到340",
desc = "积分达到340",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 340,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
40
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5708] = {
id = 5708,
name = "积分达到360",
desc = "积分达到360",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 360,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5709] = {
id = 5709,
name = "积分达到380",
desc = "积分达到380",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 380,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
4000
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5710] = {
id = 5710,
name = "积分达到400",
desc = "积分达到400",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 400,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14012
},
[5711] = {
id = 5711,
name = "积分达到1",
desc = "积分达到1",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 1,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14013
},
[5712] = {
id = 5712,
name = "积分达到10",
desc = "积分达到10",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 10,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 14013
},
[5713] = {
id = 5713,
name = "积分达到20",
desc = "积分达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 20,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
3000
},
validPeriodList = {
0
}
},
taskGroupId = 14013
},
[5714] = {
id = 5714,
name = "积分达到30",
desc = "积分达到30",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 30,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 14013
},
[5400] = {
id = 5400,
name = "发布1张造梦计划赛季激励地图",
desc = "发布1张造梦计划赛季激励地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 47,
value = 1,
subConditionList = {
{
type = 84,
value = {
55
}
}
}
}
}
}
},
jumpId = 31,
taskGroupId = 1404
},
[5401] = {
id = 5401,
name = "拥有1张造梦计划赛季激励地图，且有100游玩",
desc = "拥有1张造梦计划赛季激励地图，且有100游玩",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 55,
value = 1,
subConditionList = {
{
type = 62,
value = {
100
}
}
}
}
}
}
},
jumpId = 31,
taskGroupId = 1404
},
[5410] = {
id = 5410,
name = "创造1张王者造梦赛话题地图",
desc = "创造1张王者造梦赛话题地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 47,
value = 1,
subConditionList = {
{
type = 84,
value = {
56
}
}
}
}
}
}
},
jumpId = 31,
taskGroupId = 1405
},
[5411] = {
id = 5411,
name = "创造2张王者造梦赛话题地图",
desc = "创造2张王者造梦赛话题地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 47,
value = 2,
subConditionList = {
{
type = 84,
value = {
56
}
}
}
}
}
}
},
jumpId = 31,
taskGroupId = 1405
},
[5412] = {
id = 5412,
name = "通关王者造梦赛话题地图1张",
desc = "通关王者造梦赛话题地图1张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 1,
subConditionList = {
{
type = 84,
value = {
56
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1405
},
[5413] = {
id = 5413,
name = "通关王者造梦赛话题地图3张",
desc = "通关王者造梦赛话题地图3张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 3,
subConditionList = {
{
type = 84,
value = {
56
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1405
},
[5414] = {
id = 5414,
name = "通关王者造梦赛话题地图5张",
desc = "通关王者造梦赛话题地图5张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 5,
subConditionList = {
{
type = 84,
value = {
56
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1405
},
[5415] = {
id = 5415,
name = "通关王者造梦赛话题地图10张",
desc = "通关王者造梦赛话题地图10张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 10,
subConditionList = {
{
type = 84,
value = {
56
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1405
},
[5420] = {
id = 5420,
name = "创造1张星骑士来了话题地图",
desc = "创造1张星骑士来了话题地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 47,
value = 1,
subConditionList = {
{
type = 85,
value = {
57
}
}
}
}
}
}
},
jumpId = 31,
taskGroupId = 1406
},
[5421] = {
id = 5421,
name = "创造3张星骑士来了话题地图",
desc = "创造3张星骑士来了话题地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 47,
value = 3,
subConditionList = {
{
type = 85,
value = {
57
}
}
}
}
}
}
},
jumpId = 31,
taskGroupId = 1406
},
[5422] = {
id = 5422,
name = "游玩星骑士来了话题地图1张",
desc = "游玩星骑士来了话题地图1张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 85,
value = {
57
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1406
},
[5423] = {
id = 5423,
name = "游玩星骑士来了话题地图3张",
desc = "游玩星骑士来了话题地图3张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 3,
subConditionList = {
{
type = 85,
value = {
57
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1406
},
[5424] = {
id = 5424,
name = "游玩星骑士来了话题地图7张",
desc = "游玩星骑士来了话题地图7张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 7,
subConditionList = {
{
type = 85,
value = {
57
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1406
},
[5430] = {
id = 5430,
name = "创造1张古风剧王话题地图",
desc = "创造1张古风剧王话题地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 47,
value = 1,
subConditionList = {
{
type = 85,
value = {
58
}
}
}
}
}
}
},
jumpId = 31,
taskGroupId = 1407
},
[5431] = {
id = 5431,
name = "通关古风剧王话题地图10张",
desc = "通关古风剧王话题地图10张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 10,
subConditionList = {
{
type = 85,
value = {
58
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1407
},
[5440] = {
id = 5440,
name = "游玩新春话题地图1张",
desc = "游玩新春话题地图1张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 85,
value = {
64
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1450
},
[5441] = {
id = 5441,
name = "游玩新春话题地图3张",
desc = "游玩新春话题地图3张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 3,
subConditionList = {
{
type = 85,
value = {
64
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1450
},
[5442] = {
id = 5442,
name = "游玩新春话题地图10张",
desc = "游玩新春话题地图10张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 10,
subConditionList = {
{
type = 85,
value = {
64
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1450
},
[5443] = {
id = 5443,
name = "游玩新春话题地图30张",
desc = "游玩新春话题地图30张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 30,
subConditionList = {
{
type = 85,
value = {
64
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1450
},
[5444] = {
id = 5444,
name = "通关新春话题地图1张",
desc = "通关新春话题地图1张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 1,
subConditionList = {
{
type = 85,
value = {
64
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1450
},
[5445] = {
id = 5445,
name = "通关新春话题地图5张",
desc = "通关新春话题地图5张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 5,
subConditionList = {
{
type = 85,
value = {
64
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1450
},
[5446] = {
id = 5446,
name = "通关新春话题地图10张",
desc = "通关新春话题地图10张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 10,
subConditionList = {
{
type = 85,
value = {
64
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1450
},
[5450] = {
id = 5450,
name = "游玩企鹅新春赛话题地图1张",
desc = "游玩企鹅新春赛话题地图1张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 85,
value = {
65
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1451
},
[5451] = {
id = 5451,
name = "游玩企鹅新春赛话题地图3张",
desc = "游玩企鹅新春赛话题地图3张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 3,
subConditionList = {
{
type = 85,
value = {
65
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1451
},
[5452] = {
id = 5452,
name = "游玩企鹅新春赛话题地图7张",
desc = "游玩企鹅新春赛话题地图7张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 7,
subConditionList = {
{
type = 85,
value = {
65
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1451
},
[5460] = {
id = 5460,
name = "游玩红薯创作赛话题地图1张",
desc = "游玩红薯创作赛话题地图1张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 85,
value = {
66
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1452
},
[5461] = {
id = 5461,
name = "游玩红薯创作赛话题地图3张",
desc = "游玩红薯创作赛话题地图3张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 3,
subConditionList = {
{
type = 85,
value = {
66
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1452
},
[5462] = {
id = 5462,
name = "游玩红薯创作赛话题地图7张",
desc = "游玩红薯创作赛话题地图7张",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 7,
subConditionList = {
{
type = 85,
value = {
66
}
}
}
}
}
}
},
jumpId = 32,
taskGroupId = 1452
},
[5490] = {
id = 5490,
name = "完成1局",
desc = "完成1局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
1005
},
numList = {
100,
20
},
validPeriodList = {
0,
0
}
},
jumpId = 160
},
[5491] = {
id = 5491,
name = "完成3局",
desc = "完成3局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
6,
1005
},
numList = {
5,
30
},
validPeriodList = {
0,
0
}
},
jumpId = 160
},
[5492] = {
id = 5492,
name = "完成5局",
desc = "完成5局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
1005
},
numList = {
200,
50
},
validPeriodList = {
0,
0
}
},
jumpId = 160
},
[5493] = {
id = 5493,
name = "完成1局",
desc = "完成1局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 224,
value = 1,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
1005
},
numList = {
50,
10
},
validPeriodList = {
0,
0
}
},
jumpId = 160,
taskGroupId = 16002
},
[5494] = {
id = 5494,
name = "完成3局",
desc = "完成3局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 224,
value = 3,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
1005
},
numList = {
100,
20
},
validPeriodList = {
0,
0
}
},
jumpId = 160,
taskGroupId = 16002
},
[5495] = {
id = 5495,
name = "完成5局",
desc = "完成5局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 224,
value = 5,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
6,
1005
},
numList = {
5,
30
},
validPeriodList = {
0,
0
}
},
jumpId = 160,
taskGroupId = 16002
},
[5496] = {
id = 5496,
name = "完成7局",
desc = "完成7局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 224,
value = 7,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
1005
},
numList = {
150,
20
},
validPeriodList = {
0,
0
}
},
jumpId = 160,
taskGroupId = 16002
},
[5497] = {
id = 5497,
name = "完成10局",
desc = "完成10局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 224,
value = 10,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
4,
1005
},
numList = {
200,
20
},
validPeriodList = {
0,
0
}
},
jumpId = 160,
taskGroupId = 16002
},
[5501] = {
id = 5501,
name = "积分达1分",
desc = "积分达1分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 1
}
}
}
},
reward = {
itemIdList = {
311001
},
numList = {
1
}
},
taskGroupId = 17001
},
[5502] = {
id = 5502,
name = "积分达5分",
desc = "积分达5分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 5
}
}
}
},
reward = {
itemIdList = {
311002
},
numList = {
1
}
},
taskGroupId = 17001
},
[5503] = {
id = 5503,
name = "积分达10分",
desc = "积分达10分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 10
}
}
}
},
reward = {
itemIdList = {
311003
},
numList = {
1
}
},
taskGroupId = 17001
},
[5504] = {
id = 5504,
name = "积分达15分",
desc = "积分达15分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 15
}
}
}
},
reward = {
itemIdList = {
311004
},
numList = {
1
}
},
taskGroupId = 17001
},
[5505] = {
id = 5505,
name = "积分达20分",
desc = "积分达20分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 20
}
}
}
},
reward = {
itemIdList = {
311005
},
numList = {
1
}
},
taskGroupId = 17001
},
[5506] = {
id = 5506,
name = "积分达30分",
desc = "积分达30分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 30
}
}
}
},
reward = {
itemIdList = {
311006
},
numList = {
1
}
},
taskGroupId = 17001
},
[5507] = {
id = 5507,
name = "积分达1分",
desc = "积分达1分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 1
}
}
}
},
reward = {
itemIdList = {
311021
},
numList = {
1
}
},
taskGroupId = 17002
},
[5508] = {
id = 5508,
name = "积分达5分",
desc = "积分达5分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 5
}
}
}
},
reward = {
itemIdList = {
311022
},
numList = {
1
}
},
taskGroupId = 17002
},
[5509] = {
id = 5509,
name = "积分达10分",
desc = "积分达10分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 10
}
}
}
},
reward = {
itemIdList = {
311023
},
numList = {
1
}
},
taskGroupId = 17002
},
[5510] = {
id = 5510,
name = "积分达15分",
desc = "积分达15分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 15
}
}
}
},
reward = {
itemIdList = {
311024
},
numList = {
1
}
},
taskGroupId = 17002
},
[5511] = {
id = 5511,
name = "积分达20分",
desc = "积分达20分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 20
}
}
}
},
reward = {
itemIdList = {
311025
},
numList = {
1
}
},
taskGroupId = 17002
},
[5512] = {
id = 5512,
name = "积分达30分",
desc = "积分达30分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 208,
value = 30
}
}
}
},
reward = {
itemIdList = {
311026
},
numList = {
1
}
},
taskGroupId = 17002
},
[5701] = {
id = 5701,
name = "达到大师造梦师",
desc = "达到大师造梦师",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 56,
value = 18
}
}
}
},
reward = {
itemIdList = {
232002
},
numList = {
1
}
},
taskGroupId = 2101
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data