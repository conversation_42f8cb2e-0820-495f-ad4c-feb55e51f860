--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/B_备战装饰背包.xlsx: 天天晋级赛道具

local v0 = {
seconds = 957196799
}

local v1 = {
seconds = 4081334399
}

local v2 = 1

local v3 = {
354
}

local data = {
[340976] = {
id = 340976,
effect = true,
name = "彩虹鞋",
desc = "天天晋级赛中的彩虹鞋道具",
icon = "CDN:T_InGame_Icon_Shoe",
picture = "CDN:T_InGame_Icon_Shoe_1",
shareTexts = {
"彩虹鞋"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1
},
[340977] = {
id = 340977,
effect = true,
name = "火箭鸭",
desc = "天天晋级赛中的火箭鸭道具",
icon = "CDN:T_InGame_Icon_Duck",
picture = "CDN:T_InGame_Icon_Duck_1",
shareTexts = {
"火箭鸭"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1
},
[340978] = {
id = 340978,
effect = true,
type = "ItemType_MainPlayInner_Stealth",
name = "隐身斗篷",
desc = "天天晋级赛中的隐身斗篷道具",
icon = "CDN:T_InGame_Icon_Prop67",
picture = "CDN:T_InGame_Icon_Prop67_1",
shareTexts = {
"隐身斗篷"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340979] = {
id = 340979,
effect = true,
type = "ItemType_MainPlayInner_Mucus",
name = "黏液史莱姆",
desc = "天天晋级赛中的黏液史莱姆道具",
icon = "CDN:T_InGame_Icon_Prop66",
picture = "CDN:T_InGame_Icon_Prop66_1",
shareTexts = {
"黏液史莱姆"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340980] = {
id = 340980,
effect = true,
type = "ItemType_MainPlayInner_VacuumCleaner",
name = "吸尘器",
desc = "天天晋级赛中的吸尘器道具",
icon = "CDN:T_InGame_Icon_Prop50",
picture = "CDN:T_InGame_Icon_Prop50_1",
shareTexts = {
"吸尘器"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340981] = {
id = 340981,
effect = true,
type = "ItemType_MainPlayInner_FireworkRocket",
name = "烟花火箭",
desc = "天天晋级赛中的烟花火箭道具",
icon = "CDN:T_InGame_Icon_Prop41",
picture = "CDN:T_InGame_Icon_Prop41_1",
shareTexts = {
"烟花火箭"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340982] = {
id = 340982,
effect = true,
type = "ItemType_MainPlayInner_Springboard",
name = "弹板",
desc = "天天晋级赛中的弹板道具",
icon = "CDN:T_InGame_Icon_Prop33",
picture = "CDN:T_InGame_Icon_Prop33_1",
shareTexts = {
"弹板"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340983] = {
id = 340983,
effect = true,
type = "ItemType_MainPlayInner_Pole",
name = "撑杆",
desc = "天天晋级赛中的撑杆道具",
icon = "CDN:T_InGame_Icon_Prop25",
picture = "CDN:T_InGame_Icon_Prop25_1",
shareTexts = {
"撑杆"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340984] = {
id = 340984,
effect = true,
type = "ItemType_MainPlayInner_Landmine",
name = "地雷",
desc = "天天晋级赛中的地雷道具",
icon = "CDN:T_InGame_Icon_Prop012",
picture = "CDN:T_InGame_Icon_Prop012_1",
shareTexts = {
"地雷"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340985] = {
id = 340985,
effect = true,
type = "ItemType_MainPlayInner_Jetpack",
name = "喷气背包",
desc = "天天晋级赛中的喷气背包道具",
icon = "CDN:T_InGame_Icon_Prop11",
picture = "CDN:T_InGame_Icon_Prop11_1",
shareTexts = {
"喷气背包"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340986] = {
id = 340986,
effect = true,
type = "ItemType_MainPlayInner_IceBomb",
name = "雪人炸弹",
desc = "天天晋级赛中的雪人炸弹道具",
icon = "CDN:T_InGame_Icon_Prop45",
picture = "CDN:T_InGame_Icon_Prop45_1",
shareTexts = {
"雪人炸弹"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340987] = {
id = 340987,
effect = true,
type = "ItemType_MainPlayInner_Small",
name = "缩小药水",
desc = "天天晋级赛中的缩小药水道具",
icon = "CDN:T_InGame_Icon_Prop17",
picture = "CDN:T_InGame_Icon_Prop17_1",
shareTexts = {
"缩小药水"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340988] = {
id = 340988,
effect = true,
type = "ItemType_MainPlayInner_Banana",
name = "香蕉皮",
desc = "天天晋级赛中的香蕉皮道具",
icon = "CDN:T_InGame_Icon_Prop02",
picture = "CDN:T_InGame_Icon_Prop02_1",
shareTexts = {
"香蕉皮"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340989] = {
id = 340989,
effect = true,
type = "ItemType_MainPlayInner_Tomato",
name = "番茄",
desc = "天天晋级赛中的番茄道具",
icon = "CDN:T_InGame_Icon_Prop01",
picture = "CDN:T_InGame_Icon_Prop01_1",
shareTexts = {
"番茄"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340990] = {
id = 340990,
effect = true,
type = "ItemType_MainPlayInner_ShriekingDuck",
name = "尖叫鸭",
desc = "天天晋级赛中的尖叫鸭道具",
icon = "CDN:T_InGame_Icon_Prop30",
picture = "CDN:T_InGame_Icon_Prop30_1",
shareTexts = {
"尖叫鸭"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340991] = {
id = 340991,
effect = true,
type = "ItemType_MainPlayInner_Substitute",
name = "魔方",
desc = "天天晋级赛中的魔方道具",
icon = "CDN:T_InGame_Icon_Prop20",
picture = "CDN:T_InGame_Icon_Prop20_1",
shareTexts = {
"魔方"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340992] = {
id = 340992,
effect = true,
type = "ItemType_MainPlayInner_RotateHammer",
name = "仙人掌锤",
desc = "天天晋级赛中的仙人掌锤道具",
icon = "CDN:T_InGame_Icon_Prop51",
picture = "CDN:T_InGame_Icon_Prop51_1",
shareTexts = {
"仙人掌锤"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340993] = {
id = 340993,
effect = true,
type = "ItemType_MainPlayInner_SpeedUp",
name = "加速鞋",
desc = "天天晋级赛中的加速道具",
icon = "CDN:T_InGame_Icon_Prop18",
picture = "CDN:T_InGame_Icon_Prop18_1",
shareTexts = {
"加速"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340994] = {
id = 340994,
effect = true,
type = "ItemType_MainPlayInner_Roadblock",
name = "路障",
desc = "天天晋级赛中的路障道具",
icon = "CDN:T_InGame_Icon_Prop16",
picture = "CDN:T_InGame_Icon_Prop16_1",
shareTexts = {
"路障"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340995] = {
id = 340995,
effect = true,
type = "ItemType_MainPlayInner_HangGlider",
name = "滑翔翼",
desc = "天天晋级赛中的滑翔翼道具",
icon = "CDN:T_InGame_Icon_Prop29",
picture = "CDN:T_InGame_Icon_Prop29_1",
shareTexts = {
"滑翔翼"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340996] = {
id = 340996,
effect = true,
type = "ItemType_MainPlayInner_Cloud",
name = "云朵",
desc = "天天晋级赛中的云朵道具",
icon = "CDN:T_InGame_Icon_Prop07",
picture = "CDN:T_InGame_Icon_Prop07_1",
shareTexts = {
"云朵"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340997] = {
id = 340997,
effect = true,
type = "ItemType_MainPlayInner_Boomerang",
name = "回旋镖",
desc = "天天晋级赛中的回旋镖道具",
icon = "CDN:T_InGame_Icon_Prop05",
picture = "CDN:T_InGame_Icon_Prop05_1",
shareTexts = {
"回旋镖"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340998] = {
id = 340998,
effect = true,
type = "ItemType_MainPlayInner_Big",
name = "变大汉堡",
desc = "天天晋级赛中的变大汉堡道具",
icon = "CDN:T_InGame_Icon_Prop03",
picture = "CDN:T_InGame_Icon_Prop03_1",
shareTexts = {
"变大汉堡"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340999] = {
id = 340999,
effect = true,
type = "ItemType_MainPlayInner_Bomb",
name = "炸弹",
desc = "天天晋级赛中的炸弹道具",
icon = "CDN:T_InGame_Icon_Prop04",
picture = "CDN:T_InGame_Icon_Prop04_1",
shareTexts = {
"炸弹"
},
newShelvesBeginTime = v0,
newShelvesEndTime = v1,
isDailyBattleDefaultItem = 1
},
[340001] = {
id = 340001,
effect = true,
type = "ItemType_MainPlayInner_ShriekingDuck",
quality = 1,
name = "彩虹剑",
desc = "改变天天晋级赛中尖叫鸭道具的外观",
icon = "CDN:Icon_PropSkin_003",
picture = "CDN:T_InGame_Icon_Prop76_1",
sort = 1,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Scream/Skin/Rainbow/BP_PR_SK_ScreamingChicken_Rainbow.BP_PR_SK_ScreamingChicken_Rainbow_C"
},
shareTexts = {
"彩虹剑"
}
},
[340002] = {
id = 340002,
effect = true,
type = "ItemType_MainPlayInner_ShriekingDuck",
quality = 2,
name = "小葱葱",
desc = "改变天天晋级赛中尖叫鸭道具的外观",
icon = "CDN:Icon_PropSkin_001",
picture = "CDN:T_InGame_Icon_Prop77_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Scream/Skin/Scallion/BP_PR_SK_ScreamingChicken_Scallion.BP_PR_SK_ScreamingChicken_Scallion_C"
},
shareTexts = {
"小葱葱"
},
newShelvesBeginTime = {
seconds = 1737043200
},
newShelvesEndTime = {
seconds = 1741881599
},
skipButtonText = "新春晋级礼"
},
[340003] = {
id = 340003,
effect = true,
type = "ItemType_MainPlayInner_HangGlider",
quality = 1,
name = "蝶之梦",
desc = "改变天天晋级赛中滑翔翼道具的外观",
icon = "CDN:Icon_PropSkin_002",
picture = "CDN:T_InGame_Icon_Prop79_1",
sort = 1,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Glider/Skin/Butterfly/BP_PR_SK_HangGlide_Butterfly.BP_PR_SK_HangGlide_Butterfly_C"
},
shareTexts = {
"蝶之梦"
}
},
[340004] = {
id = 340004,
effect = true,
type = "ItemType_MainPlayInner_HangGlider",
quality = 2,
name = "纸鸢",
desc = "改变天天晋级赛中滑翔翼道具的外观",
icon = "CDN:Icon_PropSkin_004",
picture = "CDN:T_InGame_Icon_Prop78_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Glider/Skin/Kite/BP_PR_SK_HangGlide_Kite.BP_PR_SK_HangGlide_Kite_C"
},
shareTexts = {
"纸鸢"
},
newShelvesBeginTime = {
seconds = 1737043200
},
newShelvesEndTime = {
seconds = 1741881599
},
skipButtonText = "新春晋级礼"
},
[340005] = {
id = 340005,
effect = true,
type = "ItemType_MainPlayInner_Big",
quality = 2,
name = "甜甜圈",
desc = "改变天天晋级赛中变大汉堡道具的外观",
icon = "CDN:T_InGame_Icon_Prop87",
picture = "CDN:T_InGame_Icon_Prop87_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Big/Skin/Purple/BP_PA_SK_Big_Purple.BP_PA_SK_Big_Purple_C"
},
shareTexts = {
"甜甜圈"
},
newShelvesBeginTime = {
seconds = 1741881600
},
newShelvesEndTime = {
seconds = 1746115199
},
skipButtonText = "天天晋级礼"
},
[340006] = {
id = 340006,
effect = true,
type = "ItemType_MainPlayInner_Roadblock",
quality = 3,
name = "禁止通行",
desc = "改变天天晋级赛中路障道具的外观",
icon = "CDN:T_InGame_Icon_Prop88",
picture = "CDN:T_InGame_Icon_Prop88_1",
sort = 3,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Roadblock/Skin/Blue/BP_PR_SK_Roadblock_Blue.BP_PR_SK_Roadblock_Blue_C"
},
shareTexts = {
"禁止通行"
},
newShelvesBeginTime = {
seconds = 4077100800
},
newShelvesEndTime = v1,
skipButtonText = "回归奖励"
},
[340007] = {
id = 340007,
effect = true,
type = "ItemType_MainPlayInner_Roadblock",
quality = 2,
name = "药剂魔法",
desc = "改变天天晋级赛中路障道具的外观",
icon = "CDN:T_InGame_Icon_Prop86",
picture = "CDN:T_InGame_Icon_Prop86_1",
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Roadblock/Skin/Purple/BP_PR_SK_Roadblock_Purple.BP_PR_SK_Roadblock_Purple_C"
},
shareTexts = {
"药剂魔法"
},
newShelvesBeginTime = {
seconds = 4077100800
},
newShelvesEndTime = v1,
skipButtonText = "限时活动"
},
[340008] = {
id = 340008,
effect = true,
type = "ItemType_MainPlayInner_Bomb",
quality = 2,
name = "精灵爆爆",
desc = "改变天天晋级赛中炸弹道具的外观",
icon = "CDN:T_InGame_Icon_Prop91",
picture = "CDN:T_InGame_Icon_Prop91_1",
jumpId = {
5033
},
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Bomb/Skin/Purple/BP_PR_SK_Bomb_Purple.BP_PR_SK_Bomb_Purple_C"
},
shareTexts = {
"精灵爆爆"
},
newShelvesBeginTime = {
seconds = 1741881600
},
newShelvesEndTime = {
seconds = 1746115199
},
skipButtonText = "冲段挑战"
},
[340009] = {
id = 340009,
effect = true,
type = "ItemType_MainPlayInner_Boomerang",
quality = 3,
name = "部落木镖",
desc = "改变天天晋级赛中回旋镖道具的外观",
icon = "CDN:T_InGame_Icon_Prop89",
picture = "CDN:T_InGame_Icon_Prop89_1",
sort = 3,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Boomerang/Skin/Blue/BP_PR_SK_Boomerang_Blue.BP_PR_SK_Boomerang_Blue_C"
},
shareTexts = {
"部落木镖"
}
},
[340010] = {
id = 340010,
effect = true,
type = "ItemType_MainPlayInner_Boomerang",
quality = 2,
name = "蝙蝠之镖",
desc = "改变天天晋级赛中回旋镖道具的外观",
icon = "CDN:T_InGame_Icon_Prop90",
picture = "CDN:T_InGame_Icon_Prop90_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Boomerang/Skin/Purple/BP_PR_SK_Boomerang_Purple.BP_PR_SK_Boomerang_Purple_C"
},
shareTexts = {
"蝙蝠之镖"
},
newShelvesBeginTime = {
seconds = 1741881600
},
newShelvesEndTime = {
seconds = 1746115199
},
skipButtonText = "天天晋级礼"
},
[340011] = {
id = 340011,
effect = true,
type = "ItemType_MainPlayInner_Substitute",
quality = 3,
name = "机关盒子",
desc = "改变天天晋级赛中魔方道具的外观",
icon = "CDN:T_InGame_Icon_Prop93",
picture = "CDN:T_InGame_Icon_Prop93_1",
sort = 3,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Substitute/Skin/Blue_S12/BP_PR_SK_Substitute_Blue_01.BP_PR_SK_Substitute_Blue_01_C"
},
shareTexts = {
"机关盒子"
},
newShelvesBeginTime = {
seconds = 4077100800
},
newShelvesEndTime = v1,
skipButtonText = "敬请期待"
},
[340012] = {
id = 340012,
effect = true,
type = "ItemType_MainPlayInner_Cloud",
quality = 3,
name = "粉色云朵",
desc = "改变天天晋级赛中云朵道具的外观",
icon = "CDN:T_InGame_Icon_Prop92",
picture = "CDN:T_InGame_Icon_Prop92_1",
sort = 3,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Cloud/Skin/Blue_S12/BP_PR_SK_Cloud_Blue_01.BP_PR_SK_Cloud_Blue_01_C"
},
shareTexts = {
"粉色云朵"
},
newShelvesBeginTime = {
seconds = 4077100800
},
newShelvesEndTime = v1,
skipButtonText = "限时活动"
},
[340013] = {
id = 340013,
effect = true,
type = "ItemType_MainPlayInner_SpeedUp",
quality = 2,
name = "喵喵拖鞋",
desc = "改变天天晋级赛中加速鞋道具的外观",
icon = "CDN:T_InGame_Icon_Prop97",
picture = "CDN:T_InGame_Icon_Prop97_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/SpeedUp/Skin/Purple_S12/BP_PR_SK_SpeedUp_Purple_01.BP_PR_SK_SpeedUp_Purple_01_C"
},
shareTexts = {
"喵喵拖鞋"
},
newShelvesBeginTime = {
seconds = 1746115200
},
newShelvesEndTime = {
seconds = 1750953599
},
skipButtonText = "喵喵晋级礼"
},
[340014] = {
id = 340014,
effect = true,
type = "ItemType_MainPlayInner_HangGlider",
quality = 2,
name = "浮空魔法书",
desc = "改变天天晋级赛中滑翔翼道具的外观",
icon = "CDN:T_InGame_Icon_Prop94",
picture = "CDN:T_InGame_Icon_Prop94_1",
jumpId = {
5033
},
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Glider/Skin/Purple_S12/BP_PR_SK_HangGlide_Purple_01.BP_PR_SK_HangGlide_Purple_01_C"
},
shareTexts = {
"浮空魔法书"
},
newShelvesBeginTime = {
seconds = 1746115200
},
newShelvesEndTime = {
seconds = 1750953599
},
skipButtonText = "冲段挑战"
},
[340015] = {
id = 340015,
effect = true,
type = "ItemType_MainPlayInner_RotateHammer",
quality = 2,
name = "爱心之杖",
desc = "改变天天晋级赛中仙人掌锤道具的外观",
icon = "CDN:T_InGame_Icon_Prop95",
picture = "CDN:T_InGame_Icon_Prop95_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/RotateHammer/Skin/Purple_S13/BP_PR_SK_RotateHammer_Lollipop.BP_PR_SK_RotateHammer_Lollipop_C"
},
shareTexts = {
"爱心之杖"
},
newShelvesBeginTime = {
seconds = 1746115200
},
newShelvesEndTime = {
seconds = 1750953599
},
skipButtonText = "喵喵晋级礼"
},
[340016] = {
id = 340016,
effect = true,
type = "ItemType_MainPlayInner_ShriekingDuck",
quality = 2,
name = "奖杯",
desc = "改变天天晋级赛中尖叫鸭道具的外观",
icon = "CDN:T_InGame_Icon_Prop105",
picture = "CDN:T_InGame_Icon_Prop105_1",
jumpId = {
234
},
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Scream/Skin/Purple_S13/BP_PR_SK_ScreamingChicken_Trophy.BP_PR_SK_ScreamingChicken_Trophy_C"
},
shareTexts = {
"奖杯"
},
newShelvesBeginTime = {
seconds = 1737043200
},
newShelvesEndTime = {
seconds = 1741881599
},
skipButtonText = "奖杯征程"
},
[340017] = {
id = 340017,
effect = true,
type = "ItemType_MainPlayInner_ShriekingDuck",
quality = 2,
name = "棒球棍",
desc = "改变天天晋级赛中尖叫鸭道具的外观",
icon = "CDN:T_InGame_Icon_Prop106",
picture = "CDN:T_InGame_Icon_Prop106_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Scream/Skin/Purple_S13/BP_PR_SK_ScreamingChicken_BaseballBat.BP_PR_SK_ScreamingChicken_BaseballBat_C"
},
shareTexts = {
"棒球棍"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 1755791999
},
skipButtonText = "昆仑晋级礼"
},
[340018] = {
id = 340018,
effect = true,
type = "ItemType_MainPlayInner_RotateHammer",
quality = 2,
name = "波板糖",
desc = "改变天天晋级赛中仙人掌锤道具的外观",
icon = "CDN:T_InGame_Icon_Prop107",
picture = "CDN:T_InGame_Icon_Prop107_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/RotateHammer/Skin/Purple_S12/BP_PR_SK_RotateHammer_Purple_01.BP_PR_SK_RotateHammer_Purple_01_C"
},
shareTexts = {
"波板糖"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 1755791999
},
skipButtonText = "昆仑晋级礼"
},
[340019] = {
id = 340019,
effect = true,
type = "ItemType_MainPlayInner_Stealth",
quality = 3,
name = "魔力斗篷",
desc = "改变天天晋级赛中隐身斗篷道具的外观",
icon = "CDN:T_InGame_Icon_Prop108",
picture = "CDN:T_InGame_Icon_Prop108_1",
jumpId = v3,
sort = 3,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/StealthBoom/Skin/Blue_S13/BP_PR_SK_StealthBoom_Gloom.BP_PR_SK_StealthBoom_Gloom_C"
},
shareTexts = {
"魔力斗篷"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 4091011199
},
skipButtonText = "昆仑晋级礼"
},
[340020] = {
id = 340020,
effect = true,
type = "ItemType_MainPlayInner_Mucus",
quality = 2,
name = "香草布丁",
desc = "改变天天晋级赛中黏液史莱姆道具的外观",
icon = "CDN:T_InGame_Icon_Prop109",
picture = "CDN:T_InGame_Icon_Prop109_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/MucusTrap/Skin/Purple_S13/BP_PR_SK_MucusTrap_Pudding.BP_PR_SK_MucusTrap_Pudding_C"
},
shareTexts = {
"香草布丁"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 4091011199
},
skipButtonText = "昆仑晋级礼"
},
[340021] = {
id = 340021,
effect = true,
type = "ItemType_MainPlayInner_Pole",
quality = 3,
name = "如意金箍棒",
desc = "改变天天晋级赛中撑杆道具的外观",
icon = "CDN:T_InGame_Icon_Prop110",
picture = "CDN:T_InGame_Icon_Prop110_1",
jumpId = v3,
sort = 3,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Pole/Skin/Blue_S13/BP_PR_SK_Pole_JinguBang.BP_PR_SK_Pole_JinguBang_C"
},
shareTexts = {
"如意金箍棒"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 4091011199
},
skipButtonText = "昆仑晋级礼"
},
[340022] = {
id = 340022,
effect = true,
type = "ItemType_MainPlayInner_Banana",
quality = 2,
name = "西瓜皮",
desc = "改变天天晋级赛中香蕉皮道具的外观",
icon = "CDN:T_InGame_Icon_Prop111",
picture = "CDN:T_InGame_Icon_Prop111_1",
jumpId = v3,
sort = 2,
resourceConf = {
skinBPPath = "/Game/Feature/OGC/Assets/Placeables/Props/Banana/Skin/Purple_S13/BP_PR_SK_Banana_WatermelonPeel.BP_PR_SK_Banana_WatermelonPeel_C"
},
shareTexts = {
"西瓜皮"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 1755791999
},
skipButtonText = "昆仑晋级礼"
},
[341001] = {
id = 341001,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 2,
name = "锦鲤祈福",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_106",
jumpId = v3,
sort = 2,
shareTexts = {
"锦鲤祈福"
},
newShelvesBeginTime = {
seconds = 1737043200
},
newShelvesEndTime = {
seconds = 1741881599
},
skipButtonText = "新春晋级礼"
},
[341002] = {
id = 341002,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 3,
name = "水之舞",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_105",
jumpId = v3,
sort = 3,
shareTexts = {
"水之舞"
},
newShelvesBeginTime = {
seconds = 1737043200
},
newShelvesEndTime = {
seconds = 1741881599
},
skipButtonText = "新春晋级礼"
},
[341003] = {
id = 341003,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 2,
name = "狼王梦",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_121",
jumpId = v3,
sort = 2,
shareTexts = {
"狼王梦"
},
newShelvesBeginTime = {
seconds = 1741881600
},
newShelvesEndTime = {
seconds = 1746115199
},
skipButtonText = "天天晋级礼"
},
[341004] = {
id = 341004,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 3,
name = "银色月光",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_122",
jumpId = v3,
sort = 3,
shareTexts = {
"银色月光"
},
newShelvesBeginTime = {
seconds = 1741881600
},
newShelvesEndTime = {
seconds = 1746115199
},
skipButtonText = "天天晋级礼"
},
[341005] = {
id = 341005,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 2,
name = "喵喵拖尾",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_136",
jumpId = v3,
sort = 2,
shareTexts = {
"喵喵拖尾"
},
newShelvesBeginTime = {
seconds = 1746115200
},
newShelvesEndTime = {
seconds = 1750953599
},
skipButtonText = "喵喵晋级礼"
},
[341006] = {
id = 341006,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 3,
name = "黑魔法",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_137",
jumpId = v3,
sort = 3,
shareTexts = {
"黑魔法"
},
newShelvesBeginTime = {
seconds = 1746115200
},
newShelvesEndTime = {
seconds = 1750953599
},
skipButtonText = "昆仑晋级礼"
},
[341007] = {
id = 341007,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 3,
name = "万灵气韵",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_154",
jumpId = v3,
sort = 3,
shareTexts = {
"万灵气韵"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 1755791999
},
skipButtonText = "昆仑晋级礼"
},
[341008] = {
id = 341008,
effect = true,
type = "ItemType_MainPlayFinalWin_Halo",
quality = 2,
name = "观山海",
desc = "改变天天晋级赛竞速关中终点飞跃效果",
icon = "CDN:T_Common_Item_System_Bag_153",
jumpId = v3,
sort = 2,
shareTexts = {
"观山海"
},
newShelvesBeginTime = {
seconds = 1750953600
},
newShelvesEndTime = {
seconds = 1755791999
},
skipButtonText = "昆仑晋级礼"
}
}

local mt = {
effect = false,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 4,
sort = 4,
useType = "IUTO_None",
outlookConf = {
fashionValue = 0
},
commodityId = 0,
scaleTimes = 50,
shareOffset = {
-5,
15
},
previewShareOffset = {
0,
-15
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data