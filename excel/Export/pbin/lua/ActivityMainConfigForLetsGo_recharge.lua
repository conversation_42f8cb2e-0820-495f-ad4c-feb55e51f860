--com.tencent.wea.xlsRes.table_ActivityMainConfig => excel/xls/C_充值配置表.xlsx: 充值活动

local data = {
[10003] = {
id = 10003,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
showEndTime = {
seconds = 1708185599
},
showBeginTime = {
seconds = 1707408000
}
},
jumpId = 1,
lowVersion = "1.2.80.1",
activityName = "充值送好礼",
activityUIDetail = "UI_ChargeRebate_Main",
activityTaskGroup = {
50003
},
slapFace = true,
activityNameType = "ANTChargeRebate"
},
[10001] = {
id = 10001,
activityType = "ATInterServerGift",
timeInfo = {
beginTime = {
seconds = 1704384000
},
endTime = {
seconds = 1705593599
},
showEndTime = {
seconds = 1705593599
},
showBeginTime = {
seconds = 1704384000
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
jumpId = 1,
activityName = "一元幸启",
activityParam = {
1
},
activityUIDetail = "UI_ChargeRebate_Main",
activityNameType = "ANTInterServerGift"
},
[10002] = {
id = 10002,
activityType = "ATDeposit",
timeInfo = {
beginTime = {
seconds = 1675785600
},
endTime = {
seconds = 1893427199
},
showEndTime = {
seconds = 1893427199
},
showBeginTime = {
seconds = 1675785600
}
},
activityName = "招财返利",
activityParam = {
1
},
slapFace = true,
activityNameType = "ANTDeposit"
},
[10004] = {
id = 10004,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1727366399
},
showEndTime = {
seconds = 1727366399
},
showBeginTime = {
seconds = 1726156800
}
},
backgroundUrl = {
"chongzhi0913.astc"
},
jumpId = 1,
lowVersion = "1.3.18.23",
activityName = "金秋充值送",
activityUIDetail = "UI_ChargeRebate_Main",
activityTaskGroup = {
50010
},
slapFace = true,
activityNameType = "ANTChargeRebate"
},
[10005] = {
id = 10005,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
showEndTime = {
seconds = 1714060799
},
showBeginTime = {
seconds = 1710432000
}
},
jumpId = 1,
lowVersion = "1.2.80.1",
activityName = "充值送好礼",
activityUIDetail = "UI_ChargeRebate_Main",
activityTaskGroup = {
50005
},
slapFace = true,
activityNameType = "ANTChargeRebate"
},
[10006] = {
id = 10006,
activityType = "ATInterServerGift",
timeInfo = {
beginTime = {
seconds = 1717430400
},
endTime = {
seconds = 1720713599
},
showEndTime = {
seconds = 1720713599
},
showBeginTime = {
seconds = 1717430400
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
jumpId = 1,
lowVersion = "1.2.90.65",
activityName = "一元幸启",
activityParam = {
2
},
activityUIDetail = "UI_ChargeRebate_Main",
activityNameType = "ANTInterServerGift"
},
[10007] = {
id = 10007,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1729180800
}
},
backgroundUrl = {
"chongzhi1018.astc"
},
jumpId = 1,
lowVersion = "1.3.26.1",
activityName = "充值送好礼",
activityUIDetail = "UI_SecondChargeRebate_Main",
activityTaskGroup = {
50011
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
},
[10008] = {
id = 10008,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1721664000
},
endTime = {
seconds = 1723478399
},
showEndTime = {
seconds = 1723478399
},
showBeginTime = {
seconds = 1721664000
}
},
backgroundUrl = {
"T_OMDBattlePass_Img_Title0725.astc",
"T_OMDBattlePass_Img_Hero_New0723.astc"
},
lowVersion = "1.3.12.19",
activityName = "噗噗登录礼",
activityParam = {
4
},
slapFace = true,
activityNameType = "ANUpgradeCheckInManual"
},
[10009] = {
id = 10009,
activityType = "ATInterServerGift",
timeInfo = {
beginTime = {
seconds = 1715961600
},
endTime = {
seconds = 1718207999
},
showEndTime = {
seconds = 1718207999
},
showBeginTime = {
seconds = 1715961600
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
jumpId = 1,
lowVersion = "1.2.100.54",
activityName = "一元购背包",
activityParam = {
3
},
activityUIDetail = "UI_ChargeRebate_Main",
activityNameType = "ANTInterServerGift"
},
[10010] = {
id = 10010,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1709654400
},
endTime = {
seconds = 1742918399
},
showEndTime = {
seconds = 1742918399
},
showBeginTime = {
seconds = 1709654400
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
activityName = "超核配置测试",
activityDsc = "超核配置测试",
activityUIDetail = "UI_Recharge_JumpView",
activityNameType = "ANTGuide"
},
[10011] = {
id = 10011,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1709654400
},
endTime = {
seconds = 2026915199
},
showEndTime = {
seconds = 2026915199
},
showBeginTime = {
seconds = 1709654400
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
activityName = "超核配置测试-常驻活动",
activityDsc = "超核配置测试",
activityUIDetail = "UI_Recharge_SuperCoreCollect",
activityNameType = "ANTGuide"
},
[10012] = {
id = 10012,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1724947200
}
},
backgroundUrl = {
"chongzhi0830.astc"
},
jumpId = 1,
lowVersion = "1.3.12.138",
activityName = "充值送好礼",
activityUIDetail = "UI_SecondChargeRebate_Main",
activityTaskGroup = {
50009
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
},
[10013] = {
id = 10013,
activityType = "ATInterServerGift",
timeInfo = {
beginTime = {
seconds = 1722873600
},
endTime = {
seconds = 1724083199
},
showEndTime = {
seconds = 1724083199
},
showBeginTime = {
seconds = 1722873600
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
jumpId = 1,
lowVersion = "1.3.12.70",
activityName = "一元购背包",
activityParam = {
5
},
activityUIDetail = "UI_ChargeRebate_Main",
slapFace = true,
activityNameType = "ANTInterServerGift"
},
[10014] = {
id = 10014,
activityType = "ATPaidUnlock",
timeInfo = {
beginTime = {
seconds = 1722268800
},
endTime = {
seconds = 1724083199
},
showEndTime = {
seconds = 1724083199
},
showBeginTime = {
seconds = 1722268800
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
jumpId = 1,
lowVersion = "1.3.12.50",
activityName = "充值送星梭",
activityDsc = "充值送星梭",
activityParam = {
5
},
activityUIDetail = "UI_Recharge_ShuttleView",
activityTaskGroup = {
50221,
50223
},
activityNameType = "ANTPaidUnlock"
},
[10018] = {
id = 10018,
activityType = "ATLuckyTurntable",
timeInfo = {
beginTime = {
seconds = 1726156800
},
endTime = {
seconds = 1728316799
},
showEndTime = {
seconds = 1728316799
},
showBeginTime = {
seconds = 1726156800
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "1.3.18.37",
activityName = "幸运转盘",
activityDsc = "开启转盘，赢超值幸运币",
activityParam = {
1
},
activityUIDetail = "UI_Recharge_LuckyTurntable",
activityRuleId = 228,
activityNameType = "ANTRechargeLuckyTurntable"
},
[10019] = {
id = 10019,
activityType = "ATAvatarBuy",
timeInfo = {
beginTime = {
seconds = 1727798400
},
endTime = {
seconds = 1729612799
},
showEndTime = {
seconds = 1729612799
},
showBeginTime = {
seconds = 1727798400
}
},
backgroundUrl = {
"yy_2.astc"
},
activityName = "燎原之心云缨",
activityDsc = "优惠50%",
activityParam = {
12001017,
1
},
activityUIDetail = "UI_Arena_Mall_HotAvatarBuy",
activityNameType = "ANTAvatarBuy"
},
[10020] = {
id = 10020,
activityType = "ATWeekendIceBroken",
timeInfo = {
beginTime = {
seconds = 1716998400
},
endTime = {
seconds = 1767110399
},
showEndTime = {
seconds = 1767110399
},
showBeginTime = {
seconds = 1716998400
}
},
backgroundUrl = {
"saijichongzhi05.astc"
},
lowVersion = "1.3.26.60",
activityName = "周末幸运星",
activityDsc = "周末幸运星",
activityParam = {
3,
102998,
320072,
237,
300
},
activityUIDetail = "UI_WeekendLuckyStar_MainView",
activityRuleId = 253,
slapFace = true,
activityNameType = "ANTWeekendLuckyStar"
},
[10021] = {
id = 10021,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1728921600
},
endTime = {
seconds = 1729177200
},
showEndTime = {
seconds = 1729177200
},
showBeginTime = {
seconds = 1728921600
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "1.3.12.113",
activityName = "赛季预购",
activityDsc = "赛季预购",
activityParam = {
770001,
770002,
8
},
activityUIDetail = "UI_ValuePreorder_MainView",
activityNameType = "ANTSeasonPreOrder"
},
[10022] = {
id = 10022,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1728403200
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1728403200
}
},
backgroundUrl = {
"T_OMDBattlePass_Img_Farm2.astc",
"T_OMDBattlePass_Img_Furniture_1.astc"
},
lowVersion = "1.3.12.19",
activityName = "爱心小熊登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm3_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000014
},
[10025] = {
id = 10025,
activityType = "ATAvatarBuy",
timeInfo = {
beginTime = {
seconds = 1727798400
},
endTime = {
seconds = 1731599999
},
showEndTime = {
seconds = 1731599999
},
showBeginTime = {
seconds = 1727798400
}
},
backgroundUrl = {
"mz_1.astc"
},
activityName = "和平守望 墨子",
activityDsc = "优惠50%",
activityParam = {
12001018,
1
},
activityUIDetail = "UI_Arena_Mall_HotAvatarBuy",
activityNameType = "ANTAvatarBuy"
},
[10026] = {
id = 10026,
activityType = "ATWerewolfFullReduce",
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1732204799
},
showEndTime = {
seconds = 1732204799
},
showBeginTime = {
seconds = 1730995200
}
},
lowVersion = "1.3.26.80",
activityName = "满减福利",
activityDsc = "满减",
activityUIDetail = "UI_Recharge_Werewolf_Promotion",
activityRuleId = 257,
activityNameType = "ANTWerewolfFullReduce"
},
[10027] = {
id = 10027,
activityType = "ATLuckyTurntable",
timeInfo = {
beginTime = {
seconds = 1730995200
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1730995200
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "1.3.26.81",
activityName = "幸运转盘",
activityDsc = "开启转盘，赢超值幸运币",
activityParam = {
2
},
activityUIDetail = "UI_Recharge_LuckyTurntable",
activityRuleId = 266,
slapFace = true,
activityNameType = "ANTRechargeLuckyTurntable"
},
[10028] = {
id = 10028,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
showEndTime = {
seconds = 1739462399
},
showBeginTime = {
seconds = 1737648000
}
},
lowVersion = "1.3.68.51",
activityName = "冰雪赐福",
activityDsc = [[收集150个冰晶
即可获得
金线织梦 长乐]],
activityParam = {
410113
},
activityUIDetail = "UI_SnowBless_MainView",
activityRuleId = 272,
activityTaskGroup = {
50247,
50248,
50249
},
slapFace = true,
activityNameType = "ANTSnowBless",
activityBeginCleanCoin = {
223
}
},
[10029] = {
id = 10029,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1730822400
},
endTime = {
seconds = 1734623999
},
showEndTime = {
seconds = 1734623999
},
showBeginTime = {
seconds = 1730822400
}
},
backgroundUrl = {
"T_OMDBattlePass_Img_Farm3.astc",
"T_OMDBattlePass_Img_Furniture_3.astc"
},
lowVersion = "1.3.78.82",
activityName = "神奇海螺登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm4_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000018
},
[10031] = {
id = 10031,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1732806000
},
showEndTime = {
seconds = 1732806000
},
showBeginTime = {
seconds = 1732550400
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "1.3.26.111",
activityName = "赛季预购",
activityDsc = "赛季预购",
activityParam = {
770003,
770004,
9
},
activityUIDetail = "UI_ValuePreorder02_MainView",
slapFace = true,
activityNameType = "ANTSeasonPreOrder"
},
[10032] = {
id = 10032,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1728403200
},
endTime = {
seconds = 1761062399
},
showEndTime = {
seconds = 1761062399
},
showBeginTime = {
seconds = 1728403200
}
},
backgroundUrl = {
"T_OMDBattlePass_Img_Farm2.astc",
"T_OMDBattlePass_Img_Furniture_1.astc"
},
lowVersion = "1.3.12.19",
activityName = "登录礼",
activityParam = {
6,
5,
240,
6,
0,
3510
},
activityUIDetail = "UI_Farmyard_LoginSubView",
slapFace = true,
activityNameType = "ANTFarmLoginRewards",
activityShopType = {
160
}
},
[10033] = {
id = 10033,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
showEndTime = {
seconds = 1737043199
},
showBeginTime = {
seconds = 1732809600
}
},
backgroundUrl = {
"chongzhi1129.astc"
},
jumpId = 1,
lowVersion = "1.3.37.1",
activityName = "充值送好礼",
activityUIDetail = "UI_SecondChargeRebate_Main",
activityTaskGroup = {
50012
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
},
[10034] = {
id = 10034,
activityType = "ATDoubleDiamond",
timeInfo = {
beginTime = {
seconds = 1698163200
},
endTime = {
seconds = 1734364799
},
showEndTime = {
seconds = 1734364799
},
showBeginTime = {
seconds = 1698163200
}
},
lowVersion = "1.3.36.1",
activityName = "周年庆双倍星钻",
activityDsc = "返利100%",
activityParam = {
1
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
},
[10035] = {
id = 10035,
activityType = "ATWerewolfFullReduce",
timeInfo = {
beginTime = {
seconds = 1734192000
},
endTime = {
seconds = 1735487999
},
showEndTime = {
seconds = 1735487999
},
showBeginTime = {
seconds = 1734192000
}
},
lowVersion = "1.3.37.2",
activityName = "周年满减购",
activityDsc = "满减",
activityUIDetail = "UI_Recharge_Werewolf_Promotion",
activityRuleId = 257,
activityNameType = "ANTWerewolfFullReduce"
},
[10036] = {
id = 10036,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737734399
},
showEndTime = {
seconds = 1737734399
},
showBeginTime = {
seconds = 1735660800
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Farm4.astc",
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Furniture_4.astc"
},
lowVersion = "1.3.37.68",
activityName = "水晶糖树登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm5_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000027
},
[10038] = {
id = 10038,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739116799
},
showEndTime = {
seconds = 1739116799
},
showBeginTime = {
seconds = 1737648000
}
},
backgroundUrl = {
"T_Arena_Img_Manual_Title.astc",
"T_Arena_Item_ManualCard_Yao2.astc"
},
lowVersion = "1.3.12.19",
activityName = "限定卡牌登陆礼",
activityParam = {
8
},
activityUIDetail = "UI_Arena_Manual_Main",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 10038
},
[10039] = {
id = 10039,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737043199
},
showEndTime = {
seconds = 1737043199
},
showBeginTime = {
seconds = 1735660800
}
},
backgroundUrl = {
"chongzhi0101.astc"
},
jumpId = 1,
lowVersion = "1.3.37.87",
activityName = "新年充值送",
activityUIDetail = "UI_ChargeRebate_Main",
activityTaskGroup = {
50013
},
slapFace = true,
activityNameType = "ANTChargeRebate"
},
[10040] = {
id = 10040,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1737039600
},
showEndTime = {
seconds = 1737039600
},
showBeginTime = {
seconds = 1736784000
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "1.3.37.101",
activityName = "赛季预购",
activityDsc = "赛季预购",
activityParam = {
770005,
770006,
10
},
activityUIDetail = "UI_ValuePreorder03_MainView",
slapFace = true,
activityNameType = "ANTSeasonPreOrder"
},
[10041] = {
id = 10041,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1737043200
}
},
backgroundUrl = {
"chongzhi0117.astc"
},
jumpId = 1,
lowVersion = "1.3.68.3",
activityName = "充值送好礼",
activityUIDetail = "UI_SecondChargeRebate_Main",
activityTaskGroup = {
50014
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
},
[10042] = {
id = 10042,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1737734400
},
endTime = {
seconds = 1740671999
},
showEndTime = {
seconds = 1740671999
},
showBeginTime = {
seconds = 1737734400
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Farm5.astc",
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Furniture_5.astc"
},
lowVersion = "1.3.68.26",
activityName = "红梅登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm6_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000033
},
[10043] = {
id = 10043,
activityType = "ATLuckyTurntable",
timeInfo = {
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739721599
},
showEndTime = {
seconds = 1739721599
},
showBeginTime = {
seconds = 1737993600
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "1.3.68.52",
activityName = "幸运转盘",
activityDsc = "开启转盘，赢超值幸运币",
activityParam = {
3
},
activityUIDetail = "UI_Recharge_LuckyTurntable",
activityRuleId = 266,
slapFace = true,
activityNameType = "ANTRechargeLuckyTurntable"
},
[10047] = {
id = 10047,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1739462400
}
},
backgroundUrl = {
"T_OMDBattlePass_Img_Farm2.astc",
"T_OMDBattlePass_Img_Furniture_1.astc"
},
lowVersion = "1.3.12.19",
activityName = "爱心小熊登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm3_BattlePassView",
activityTaskGroup = {
50282
},
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000037
},
[10046] = {
id = 10046,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
showEndTime = {
seconds = 1742486399
},
showBeginTime = {
seconds = 1740758400
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Farm6.astc",
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Furniture_6.astc"
},
lowVersion = "1.3.68.116",
activityName = "莲池登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm7_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000038
},
[10048] = {
id = 10048,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1741535999
},
showEndTime = {
seconds = 1741535999
},
showBeginTime = {
seconds = 1740067200
}
},
backgroundUrl = {
"T_Arena_Img_Manual_Title.astc",
"T_Arena_Item_ManualCard_Mulan.astc"
},
lowVersion = "1.3.68.90",
activityName = "限定卡牌登陆礼",
activityParam = {
9
},
activityUIDetail = "UI_Arena_Manual_Main",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 10038
},
[10049] = {
id = 10049,
activityType = "ATWerewolfFullReduce",
timeInfo = {
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742140799
},
showEndTime = {
seconds = 1742140799
},
showBeginTime = {
seconds = 1740758400
}
},
lowVersion = "1.3.68.113",
activityName = "满减大福利",
activityDsc = "满减",
activityUIDetail = "UI_Recharge_Werewolf_Promotion",
activityRuleId = 257,
activityNameType = "ANTWerewolfFullReduce"
},
[10050] = {
id = 10050,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1741622400
},
endTime = {
seconds = 1741878000
},
showEndTime = {
seconds = 1741878000
},
showBeginTime = {
seconds = 1741622400
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "1.3.68.131",
activityName = "赛季预购",
activityDsc = "赛季预购",
activityParam = {
770007,
770008,
11
},
activityUIDetail = "UI_ValuePreorder04_MainView",
slapFace = true,
activityNameType = "ANTSeasonPreOrder"
},
[10051] = {
id = 10051,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1710345600
},
endTime = {
seconds = 1714579199
},
showEndTime = {
seconds = 1714579199
},
showBeginTime = {
seconds = 1710345600
}
},
backgroundUrl = {
"chongzhi0314.astc"
},
jumpId = 1,
lowVersion = "1.3.78.1",
activityName = "充值送好礼",
activityUIDetail = "UI_SecondChargeRebate_Main",
activityTaskGroup = {
50015
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
},
[10052] = {
id = 10052,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
showEndTime = {
seconds = 1746028799
},
showBeginTime = {
seconds = 1742486400
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Farm7.astc",
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Furniture_7.astc"
},
lowVersion = "1.3.78.33",
activityName = "香橙登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm8_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000039
},
[10053] = {
id = 10053,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1741017600
},
endTime = {
seconds = 1745164799
},
showEndTime = {
seconds = 1745164799
},
showBeginTime = {
seconds = 1741017600
}
},
backgroundUrl = {
"T_Arena_Img_Manual_Title.astc",
"T_Arena_Item_ManualCard_DaJi.astc"
},
lowVersion = "1.3.78.59",
activityName = "限定卡牌登陆礼",
activityParam = {
10
},
activityUIDetail = "UI_Arena_Manual_Main",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 10038
},
[10054] = {
id = 10054,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
showEndTime = {
seconds = 1745510399
},
showBeginTime = {
seconds = 1743696000
}
},
backgroundUrl = {
"T_Recharge_BlueOutfit_Img_Hero.astc",
"T_Recharge_BlueOutfit_Img_TitleNew.astc"
},
lowVersion = "*********",
activityName = "充值送狡狡龙",
activityDsc = "充值送狡狡龙",
activityUIDetail = "UI_Recharge_BlueOutfit",
activityRuleId = 231,
activityTaskGroup = {
50290
},
activityNameType = "ANTRechargeBlueEquip"
},
[10055] = {
id = 10055,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1745856000
},
endTime = {
seconds = 1746111600
},
showEndTime = {
seconds = 1746111600
},
showBeginTime = {
seconds = 1745856000
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "**********",
activityName = "赛季预购",
activityDsc = "赛季预购",
activityParam = {
770009,
770010,
12
},
activityUIDetail = "UI_ValuePreorder05_MainView",
slapFace = true,
activityNameType = "ANTSeasonPreOrder"
},
[10056] = {
id = 10056,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1747324799
},
showEndTime = {
seconds = 1747324799
},
showBeginTime = {
seconds = 1744905600
}
},
backgroundUrl = {
"T_OMDBattlePass_Img_Farm3.astc",
"T_OMDBattlePass_Img_Furniture_3.astc"
},
lowVersion = "*********",
activityName = "神奇海螺登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm4_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000048
},
[10057] = {
id = 10057,
activityType = "ATWerewolfFullReduce",
timeInfo = {
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1746979199
},
showEndTime = {
seconds = 1746979199
},
showBeginTime = {
seconds = 1745596800
}
},
lowVersion = "1.3.78.88",
activityName = "满减大福利",
activityDsc = "满减",
activityUIDetail = "UI_Recharge_Werewolf_Promotion",
activityRuleId = 257,
activityNameType = "ANTWerewolfFullReduce"
},
[10058] = {
id = 10058,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1749139199
},
showEndTime = {
seconds = 1749139199
},
showBeginTime = {
seconds = 1746028800
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_FarmPark_Img_BPTitle.astc",
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_FarmPark_Img_BPBigreward.astc"
},
lowVersion = "1.3.88.1",
activityName = "蜜糖登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_FarmPark_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000049
},
[10059] = {
id = 10059,
activityType = "ATInflateRedPacket",
timeInfo = {
beginTime = {
seconds = 1744128000
},
endTime = {
seconds = 1749139199
},
showEndTime = {
seconds = 1749139199
},
showBeginTime = {
seconds = 1744128000
}
},
backgroundUrl = {
"T_InflateHongBao_Icon_BigBg"
},
activityName = "爆红包测试",
activityDsc = "爆红包测试",
activityParam = {
301
},
activityUIDetail = "UI_InflateHongBao_MainView",
activityRuleId = 364,
activityNameType = "ANTInflateRedPacket"
},
[10060] = {
id = 10060,
activityType = "ATPaidUnlock",
timeInfo = {
beginTime = {
seconds = 1722268800
},
endTime = {
seconds = 1755619199
},
showEndTime = {
seconds = 1755619199
},
showBeginTime = {
seconds = 1722268800
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
jumpId = 1,
lowVersion = "1.3.12.50",
activityName = "充值送星梭",
activityDsc = "充值送星梭",
activityParam = {
5
},
activityUIDetail = "UI_Recharge_ShuttleView",
activityTaskGroup = {
50293,
50294
},
activityNameType = "ANTPaidUnlock"
},
[10061] = {
id = 10061,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1714579200
},
endTime = {
seconds = 1719417599
},
showEndTime = {
seconds = 1719417599
},
showBeginTime = {
seconds = 1714579200
}
},
backgroundUrl = {
"chongzhi0502.astc"
},
jumpId = 1,
lowVersion = "1.3.88.1",
activityName = "充值送好礼",
activityUIDetail = "UI_SecondChargeRebate_Main",
activityTaskGroup = {
50016
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
},
[10063] = {
id = 10063,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1749225600
},
endTime = {
seconds = 1751558399
},
showEndTime = {
seconds = 1751558399
},
showBeginTime = {
seconds = 1749225600
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Farm4.astc",
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_OMDBattlePass_Img_Furniture_4.astc"
},
lowVersion = "1.3.88.112",
activityName = "水晶糖树登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_Farm5_BattlePassView",
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000058
},
[10065] = {
id = 10065,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1749743999
},
showEndTime = {
seconds = 1749743999
},
showBeginTime = {
seconds = 1748534400
}
},
backgroundUrl = {
"chongzhi0530.astc"
},
jumpId = 1,
lowVersion = "1.3.37.87",
activityName = "充值送好礼",
activityDsc = "充值送好礼",
activityParam = {
5
},
activityUIDetail = "UI_ChargeRebate_Main",
activityTaskGroup = {
50298
},
slapFace = true,
activityNameType = "ANTChargeRebate"
},
[10066] = {
id = 10066,
activityType = "ATWerewolfFullReduce",
timeInfo = {
beginTime = {
seconds = 1749139200
},
endTime = {
seconds = 1750607999
},
showEndTime = {
seconds = 1750607999
},
showBeginTime = {
seconds = 1749139200
}
},
lowVersion = "1.3.88.80",
activityName = "满减大福利",
activityDsc = "满减",
activityUIDetail = "UI_Recharge_Werewolf_Promotion",
activityRuleId = 257,
activityNameType = "ANTWerewolfFullReduce"
},
[10067] = {
id = 10067,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750950000
},
showEndTime = {
seconds = 1750950000
},
showBeginTime = {
seconds = 1747929600
}
},
backgroundUrl = {
"T_RechargeRebate_Bg"
},
lowVersion = "**********",
activityName = "赛季预购",
activityDsc = "赛季预购",
activityParam = {
770011,
770012,
13
},
activityUIDetail = "UI_ValuePreorder06_MainView",
slapFace = true,
activityNameType = "ANTSeasonPreOrder"
},
[10068] = {
id = 10068,
activityType = "ATUpgradeCheckInManual",
timeInfo = {
beginTime = {
seconds = 1748707200
},
endTime = {
seconds = 1753372799
},
showEndTime = {
seconds = 1753372799
},
showBeginTime = {
seconds = 1748707200
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_FarmPark_Img_BPTitle.astc",
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_FarmPark_Img_BPBigreward.astc"
},
lowVersion = "1.3.88.1",
activityName = "天穹登录礼",
activityParam = {
6
},
activityUIDetail = "UI_Lottery_FarmPark_BattlePassView",
activityTaskGroup = {
45017,
45018
},
slapFace = true,
activityNameType = "ANUpgradeCheckInManual",
relatedModeId = 13000063
},
[10069] = {
id = 10069,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1750003200
},
endTime = {
seconds = 1755791999
},
showEndTime = {
seconds = 1755791999
},
showBeginTime = {
seconds = 1750003200
}
},
backgroundUrl = {
"chongzhi0627.astc"
},
jumpId = 1,
lowVersion = "1.3.88.1",
activityName = "充值送好礼",
activityUIDetail = "UI_SecondChargeRebate_Main",
activityTaskGroup = {
50017
},
slapFace = true,
activityNameType = "ANTSecondChargeRebate"
}
}

local mt = {
labelId = 3,
activityDsc = "返利50%",
tagId = 1,
slapFace = false,
showInCenter = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data