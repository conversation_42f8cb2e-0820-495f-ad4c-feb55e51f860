--com.tencent.wea.xlsRes.table_StepGuideData => excel/xls/X_新手引导表_农场.xlsx: 新引导步骤

local v0 = 3

local v1 = "UI_Farmyard_Mainview"

local v2 = 1001

local v3 = "clicked"

local v4 = 1005

local v5 = "{delay=0.3}"

local data = {
[200001] = {
StepID = 200001,
GuideID = 2000,
Comment = "【对话】移动到地块",
SubStep = 1,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "嗨！年轻的农夫，我是萌新观察员小红狐。我来帮助你体验农场的乐趣~开始<Highlight31>种田</>吧！",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[200201] = {
StepID = 200201,
GuideID = 2000,
Comment = "轮盘引导",
SubStep = 2,
GetWidgetFunc = "GetMoveGuideBtn",
GetButtonFunc = "GetMoveGuideBtn",
ShowUIList = "{UI_Farmyard_Mainview = {},UI_HandHold_ControlPad = {},UI_Farmyard_RightBottomBtn = {\"w_canvas_RightBottomBtn\"}}",
UIStyle_Frist = 1009,
UIOffset_Frist = "{X=350, Y =600}",
UIStyle_Second = 1005,
UIText_Second = "滑动轮盘，角色移动",
UIOffset_Second = "{X=100, Y =400}",
bAnyWhereNext = true,
ButtonMode = v3
},
[200202] = {
StepID = 200202,
GuideID = 2000,
Comment = "镜头引导",
SubStep = 3,
ShowUIList = "{UI_Farmyard_Mainview = {},UI_HandHold_ControlPad = {},UI_Farmyard_RightBottomBtn = {}}",
UIStyle_Frist = 1008,
UIOffset_Frist = "{X=700, Y =300}",
UIStyle_Second = 1005,
UIText_Second = "移动镜头",
UIOffset_Second = "{X=700, Y =150}",
bAnyWhereNext = true,
ButtonMode = v3
},
[200002] = {
StepID = 200002,
GuideID = 2000,
Comment = "【箭头】显示箭头",
SubStep = 4,
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_Move1\"}",
WindowName = v1
},
[200003] = {
StepID = 200003,
GuideID = 2000,
Comment = "走到地块",
SubStep = 5,
StepType = 6,
TypeParams = "Newcomer_Move1_Finish",
WindowName = v1
},
[200004] = {
StepID = 200004,
GuideID = 2000,
Comment = "打开农作物图鉴",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Menu",
ButtonName = "w_btn_Menu",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-280}",
UIStyle_Second = 1005,
UIText_Second = "打开种植清单。",
UIOffset_Second = "{X=-300, Y =-400}",
bMask = true,
ButtonMode = v3
},
[200005] = {
StepID = 200005,
GuideID = 2000,
Comment = "等待图鉴界面打开",
SubStep = 7,
StepType = 10,
TypeParams = "{windowName = \"UI_Farmyard_StashMain\"}",
IsForceGuide = true,
bMask = true
},
[200006] = {
StepID = 200006,
GuideID = 2000,
Comment = "选择农作物",
SubStep = 8,
IsForceGuide = true,
WindowName = "UI_Farmyard_StashMain",
GetWidgetFunc = "GetGuideFirstButton",
GetButtonFunc = "GetGuideFirstButton",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-60, Y =100}",
UIStyle_Second = 1005,
UIText_Second = "选择种子。",
UIOffset_Second = "{X=-245, Y =180}",
bMask = true,
ButtonMode = v3
},
[200007] = {
StepID = 200007,
GuideID = 2000,
Comment = "确定农作物",
SubStep = 9,
IsForceGuide = true,
WindowName = "UI_Farmyard_StashMain",
GetWidgetFunc = "GetGuideSureButton",
GetButtonFunc = "GetGuideSureButton",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-70, Y =-230}",
UIStyle_Second = 1005,
UIText_Second = "确定选择。",
UIOffset_Second = "{X=-250, Y =-330}",
bMask = true,
ButtonMode = v3
},
[200008] = {
StepID = 200008,
GuideID = 2000,
Comment = "【对话】农作物可播种",
SubStep = 10,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "选择好种子可以开始<Highlight31>播种</>了。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[200009] = {
StepID = 200009,
GuideID = 2000,
Comment = "播种农作物",
SubStep = 11,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Plant",
ButtonName = "w_btn_Plant",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-280}",
UIStyle_Second = 1005,
UIText_Second = "农作物要种在开垦过的土地。",
UIOffset_Second = "{X=-300, Y =-400}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[201001] = {
StepID = 201001,
GuideID = 2010,
Comment = "【对话】农作物可浇水",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "这次帮你加快成熟咯。<Highlight31>浇水</>也能使农作物更快成熟~",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[201002] = {
StepID = 201002,
GuideID = 2010,
Comment = "点击浇水",
SubStep = 2,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Interact",
ButtonName = "w_btn_Interact",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-280}",
UIStyle_Second = 1005,
UIText_Second = "点击水壶浇水。",
UIOffset_Second = "{X=-300, Y =-400}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[201003] = {
StepID = 201003,
GuideID = 2010,
Comment = "【对话】等待成熟提醒",
SubStep = 3,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "等待果实成熟时，把<Highlight31>所有土地</>都种上农作物吧。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[202001] = {
StepID = 202001,
GuideID = 2020,
Comment = "【对话】成熟提醒",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "有农作物<Highlight31>成熟</>了，快去看看吧。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[202002] = {
StepID = 202002,
GuideID = 2020,
Comment = "【箭头】显示箭头",
SubStep = 2,
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_Move5\"}",
WindowName = v1
},
[202003] = {
StepID = 202003,
GuideID = 2020,
Comment = "走到成熟地块",
SubStep = 3,
StepType = 6,
TypeParams = "Newcomer_Move5_Finish",
WindowName = v1
},
[202004] = {
StepID = 202004,
GuideID = 2020,
Comment = "收获农作物",
SubStep = 4,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Interact",
ButtonName = "w_btn_Interact",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-280}",
UIStyle_Second = 1005,
UIText_Second = "点击收获按钮获取果实。",
UIOffset_Second = "{X=-300, Y =-400}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[202101] = {
StepID = 202101,
GuideID = 2021,
Comment = "【对话】成熟提醒2",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "有农作物<Highlight31>成熟</>了，快去看看吧。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[202102] = {
StepID = 202102,
GuideID = 2021,
Comment = "收获农作物2",
SubStep = 2,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Interact",
ButtonName = "w_btn_Interact",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-280}",
UIStyle_Second = 1005,
UIText_Second = "点击收获按钮获取果实。",
UIOffset_Second = "{X=-300, Y =-400}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[203001] = {
StepID = 203001,
GuideID = 2030,
Comment = "【对话】移动到蔬菜摊",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "做得很好！现在你可以前往<Highlight31>蔬菜摊</>卖掉果实赚取农场币。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[203002] = {
StepID = 203002,
GuideID = 2030,
Comment = "【箭头】显示箭头",
SubStep = 2,
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_Move2\"}",
WindowName = v1
},
[203003] = {
StepID = 203003,
GuideID = 2030,
Comment = "走到蔬菜摊",
SubStep = 3,
StepType = 6,
TypeParams = "Newcomer_Move2_Finish",
WindowName = v1
},
[203004] = {
StepID = 203004,
GuideID = 2030,
Comment = "出售农作物",
SubStep = 4,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Interact",
ButtonName = "w_btn_Interact",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-280}",
UIStyle_Second = 1005,
UIText_Second = "点击出售按钮。",
UIOffset_Second = "{X=-300, Y =-400}",
bMask = true,
ButtonMode = v3
},
[203005] = {
StepID = 203005,
GuideID = 2030,
Comment = "等待商店界面打开",
SubStep = 5,
StepType = 10,
TypeParams = "{windowName = \"UI_Farmyard_HarvestMain\"}",
IsForceGuide = true,
bMask = true
},
[203006] = {
StepID = 203006,
GuideID = 2030,
Comment = "选择出售农作物",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_Farmyard_HarvestMain",
GetWidgetFunc = "GetGuideFirstButton",
GetButtonFunc = "GetGuideFirstButton",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-60, Y =100}",
UIStyle_Second = 1005,
UIText_Second = "选择想要卖出的农产品。",
UIOffset_Second = "{X=-245, Y =180}",
bMask = true,
ButtonMode = v3
},
[203007] = {
StepID = 203007,
GuideID = 2030,
Comment = "点击出售",
SubStep = 7,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_SellHarvest",
GetWidgetFunc = "GetGuideButtonSell",
GetButtonFunc = "GetGuideButtonSell",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-90, Y =80}",
UIStyle_Second = 1005,
UIText_Second = "确定了就点击出售吧。",
UIOffset_Second = "{X=-280, Y =150}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[204001] = {
StepID = 204001,
GuideID = 2040,
Comment = "【对话】前往小木屋",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "接下来，前往<Highlight31>农场小屋</>升级你的农场吧。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[204002] = {
StepID = 204002,
GuideID = 2040,
Comment = "【箭头】显示箭头",
SubStep = 2,
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_Move3\"}",
WindowName = v1
},
[204003] = {
StepID = 204003,
GuideID = 2040,
Comment = "走到小屋",
SubStep = 3,
StepType = 6,
TypeParams = "Newcomer_Move3_Finish",
WindowName = v1
},
[204004] = {
StepID = 204004,
GuideID = 2040,
Comment = "升级小屋",
SubStep = 4,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_interact2",
ButtonName = "w_btn_interact2",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-230}",
UIStyle_Second = 1005,
UIText_Second = "点击升级按钮。",
UIOffset_Second = "{X=-300, Y =-350}",
bMask = true,
ButtonMode = v3
},
[204005] = {
StepID = 204005,
GuideID = 2040,
Comment = "等待农场小屋界面打开",
SubStep = 5,
StepType = 10,
TypeParams = "{windowName = \"UI_Farmyard_FarmHouseLevelUpMain\"}",
IsForceGuide = true,
bMask = true
},
[204006] = {
StepID = 204006,
GuideID = 2040,
Comment = "确认升级",
SubStep = 6,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_FarmHouseLevelUpMain",
GetWidgetFunc = "GetGuideLevelButton",
GetButtonFunc = "GetGuideLevelButton",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-90, Y =-200}",
UIStyle_Second = 1005,
UIText_Second = "升级农场解锁更多有趣的内容。",
UIOffset_Second = "{X=-300, Y =-320}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[205001] = {
StepID = 205001,
GuideID = 2050,
Comment = "【对话】可以扩建土地",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "去<Highlight31>开垦</>新的土地，种植更多农作物吧。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[205002] = {
StepID = 205002,
GuideID = 2050,
Comment = "【箭头】显示箭头",
SubStep = 2,
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_Move4\"}",
WindowName = v1
},
[205003] = {
StepID = 205003,
GuideID = 2050,
Comment = "走到新解锁土地",
SubStep = 3,
StepType = 6,
TypeParams = "Newcomer_Move4_Finish",
WindowName = v1
},
[205004] = {
StepID = 205004,
GuideID = 2050,
Comment = "解锁土地",
SubStep = 4,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Interact2",
ButtonName = "w_btn_Interact2",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-230}",
UIStyle_Second = 1005,
UIText_Second = "点击开垦按钮。",
UIOffset_Second = "{X=-300, Y =-350}",
bMask = true,
ButtonMode = v3
},
[205005] = {
StepID = 205005,
GuideID = 2050,
Comment = "等待扩建界面打开",
SubStep = 5,
StepType = 10,
TypeParams = "{windowName = \"UI_MessageBox_LandBuyTips\"}",
IsForceGuide = true,
bMask = true
},
[205006] = {
StepID = 205006,
GuideID = 2050,
Comment = "确定扩建",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_MessageBox_LandBuyTips",
GetWidgetFunc = "GetGuideSureButton",
GetButtonFunc = "GetGuideSureButton",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "开垦土地需要支付农场币，确定开垦吗？",
UIOffset_Second = "{X=-250, Y =150}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[205101] = {
StepID = 205101,
GuideID = 2051,
Comment = "【对话】可以扩建土地",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "去<Highlight31>开垦</>新的土地，种植更多农作物吧。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[205102] = {
StepID = 205102,
GuideID = 2051,
Comment = "解锁土地",
SubStep = 2,
StepType = 3,
TypeParams = "{delay=0.1}",
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Interact2",
ButtonName = "w_btn_Interact2",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-230}",
UIStyle_Second = 1005,
UIText_Second = "点击开垦按钮。",
UIOffset_Second = "{X=-300, Y =-350}",
bMask = true,
ButtonMode = v3
},
[205103] = {
StepID = 205103,
GuideID = 2051,
Comment = "等待扩建界面打开",
SubStep = 3,
StepType = 10,
TypeParams = "{windowName = \"UI_MessageBox_LandBuyTips\"}",
IsForceGuide = true,
bMask = true
},
[205104] = {
StepID = 205104,
GuideID = 2051,
Comment = "确定扩建",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_MessageBox_LandBuyTips",
GetWidgetFunc = "GetGuideSureButton",
GetButtonFunc = "GetGuideSureButton",
UIStyle_Frist = 1001,
UIText_Frist = "0",
UIOffset_Frist = "{X=-50, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "开垦土地需要支付农场币，确定开垦吗？",
UIOffset_Second = "{X=-250, Y =150}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[206001] = {
StepID = 206001,
GuideID = 2060,
Comment = "【对话】引导升级",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "就是这样！赚更多的农场币，把农场小屋<Highlight31>升到3级</>吧。",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[207001] = {
StepID = 207001,
GuideID = 2070,
Comment = "【对话】社交引导",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "添加更多的好友，就可以去好友家摘取果实哦。",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[207002] = {
StepID = 207002,
GuideID = 2070,
Comment = "点击社交按钮",
SubStep = 2,
WindowName = v1,
WidgetName = "w_btn_Visit",
ButtonName = "w_btn_Visit",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "拜访好友农场",
UIOffset_Second = "{X=-450, Y =130}",
ButtonMode = v3
},
[207101] = {
StepID = 207101,
GuideID = 2071,
Comment = "【对话】社交说明",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "我家的果实成熟了，快来我家看看！",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[207102] = {
StepID = 207102,
GuideID = 2071,
Comment = "点击社交按钮",
SubStep = 2,
IsForceGuide = true,
WindowName = v1,
GetWidgetFunc = "GetSocialViewButton",
GetButtonFunc = "GetSocialViewButton",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "点击社交按钮",
UIOffset_Second = "{X=-500, Y =130}",
bMask = true,
ButtonMode = v3
},
[207103] = {
StepID = 207103,
GuideID = 2071,
Comment = "等待社交界面打开",
SubStep = 3,
StepType = 10,
TypeParams = "{windowName = \"UI_Farmyard_FriendList\"}",
IsForceGuide = true,
bMask = true
},
[207104] = {
StepID = 207104,
GuideID = 2071,
Comment = "点击拜访小红狐",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Farmyard_FriendList",
GetWidgetFunc = "GetGuideYuanYuan",
GetButtonFunc = "GetGuideYuanYuan",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "点击拜访好友",
UIOffset_Second = "{X=-450, Y =130}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[207201] = {
StepID = 207201,
GuideID = 2072,
Comment = "【对话】偷菜说明",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "好友家成熟的果实是可以<Highlight31>拿走</>的，你试试~",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[207202] = {
StepID = 207202,
GuideID = 2072,
Comment = "【箭头】显示箭头",
SubStep = 2,
StepType = 12,
TypeParams = "{delay=0,eventName=\"Newcomer_NPC_Move1\"}",
WindowName = v1
},
[207203] = {
StepID = 207203,
GuideID = 2072,
Comment = "走到地块",
SubStep = 3,
StepType = 6,
TypeParams = "Newcomer_NPC_Move1_OK",
WindowName = v1
},
[207204] = {
StepID = 207204,
GuideID = 2072,
Comment = "点击偷取",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Farmyard_RightBottomBtn",
WidgetName = "w_btn_Interact",
ButtonName = "w_btn_Interact",
UIStyle_Frist = 1001,
UIText_Frist = "4",
UIOffset_Frist = "{X=-75, Y =-280}",
UIStyle_Second = 1005,
UIText_Second = "点击摘取果实",
UIOffset_Second = "{X=-300, Y =-400}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[207301] = {
StepID = 207301,
GuideID = 2073,
Comment = "【对话】把菜偷完",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = [[我家所有的果实都送你啦，快拿吧！
]],
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[208001] = {
StepID = 208001,
GuideID = 2080,
Comment = "【对话】社交引导",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "要是你有<Highlight31>更多农场好友</>，就能拿更多的果实啦。",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[208002] = {
StepID = 208002,
GuideID = 2080,
Comment = "点击社交按钮",
SubStep = 2,
IsForceGuide = true,
WindowName = v1,
GetWidgetFunc = "GetSocialViewButton",
GetButtonFunc = "GetSocialViewButton",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "点击社交按钮",
UIOffset_Second = "{X=-500, Y =130}",
bMask = true,
ButtonMode = v3
},
[208003] = {
StepID = 208003,
GuideID = 2080,
Comment = "等待社交界面打开",
SubStep = 3,
StepType = 10,
TypeParams = "{windowName = \"UI_Farmyard_FriendList\"}",
IsForceGuide = true,
bMask = true
},
[208004] = {
StepID = 208004,
GuideID = 2080,
Comment = "选择陌生人",
SubStep = 4,
IsForceGuide = true,
WindowName = "UI_Farmyard_FriendList",
WidgetName = "UI_CommonComp_HorTab_Stranger",
GetButtonFunc = "GetGuideStrangerButton",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "点击陌生人",
UIOffset_Second = "{X=-450, Y =130}",
bMask = true,
ButtonMode = v3
},
[208005] = {
StepID = 208005,
GuideID = 2080,
Comment = "等待陌生人数据加载",
SubStep = 5,
StepType = 6,
TypeParams = "FARM_STRANGER_LIST_GUIDE_CONTINUE",
IsForceGuide = true,
WindowName = "UI_Farmyard_FriendList",
bMask = true
},
[208006] = {
StepID = 208006,
GuideID = 2080,
Comment = "高亮列表第1个",
SubStep = 6,
IsForceGuide = true,
WindowName = "UI_Farmyard_FriendList",
GetWidgetFunc = "GetGuideFirstStranger",
GetButtonFunc = "GetGuideFirstStranger",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "申请添加好友",
UIOffset_Second = "{X=-450, Y =130}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[208007] = {
StepID = 208007,
GuideID = 2080,
Comment = "高亮列表第2个",
SubStep = 7,
IsForceGuide = true,
WindowName = "UI_Farmyard_FriendList",
GetWidgetFunc = "GetGuideSecondStranger",
GetButtonFunc = "GetGuideSecondStranger",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "申请添加好友",
UIOffset_Second = "{X=-450, Y =130}",
bMask = true,
ButtonMode = v3
},
[208008] = {
StepID = 208008,
GuideID = 2080,
Comment = "高亮列表第3个",
SubStep = 8,
IsForceGuide = true,
WindowName = "UI_Farmyard_FriendList",
GetWidgetFunc = "GetGuideThirdStranger",
GetButtonFunc = "GetGuideThirdStranger",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "申请添加好友",
UIOffset_Second = "{X=-450, Y =130}",
bMask = true,
ButtonMode = v3
},
[208009] = {
StepID = 208009,
GuideID = 2080,
Comment = "【对话】结束引导",
SubStep = 9,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = "UI_Farmyard_FriendList",
UIStyle_Frist = 2005,
UIText_Frist = "以后可以常去好友的农场逛逛咯~",
bAnyWhereNext = true,
bMask = true,
ButtonMode = v3
},
[209001] = {
StepID = 209001,
GuideID = 2090,
Comment = "等待返回主界面",
SubStep = 1,
StepType = 6,
TypeParams = "FARM_BACK_TO_HOME_SUBGUIDE_CONTINUE"
},
[209002] = {
StepID = 209002,
GuideID = 2090,
Comment = "等待打开主界面",
SubStep = 2,
StepType = 10,
TypeParams = "{windowName = \"UI_Farmyard_Mainview\"}"
},
[209003] = {
StepID = 209003,
GuideID = 2090,
Comment = "高亮回家按钮",
SubStep = 3,
IsForceGuide = true,
WindowName = v1,
GetWidgetFunc = "GetReturnHomeButton",
GetButtonFunc = "GetReturnHomeButton",
UIStyle_Frist = 1001,
UIText_Frist = "1",
UIOffset_Frist = "{X=-200, Y =50}",
UIStyle_Second = 1005,
UIText_Second = "点击回家",
UIOffset_Second = "{X=-450, Y =130}",
bMask = true,
IsKeyStep = true,
ButtonMode = v3
},
[209101] = {
StepID = 209101,
GuideID = 2091,
Comment = "【对话】欢迎回家（从NPC返回）",
SubStep = 1,
StepType = 3,
TypeParams = v5,
IsForceGuide = true,
WindowName = v1,
UIStyle_Frist = 2005,
UIText_Frist = "远程帮助到此结束咯，我也要去拜访<Highlight31>某位好友</>的农场啦，嘻嘻。",
bAnyWhereNext = true,
bMask = true,
IsKeyStep = true,
ButtonMode = v3
}
}

local mt = {
StepType = 1,
IsForceGuide = false,
bAnyWhereNext = false,
bOtherButtonExit = false,
bMask = false,
bShowSkipButton = false,
IsKeyStep = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data