--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 背饰

local v0 = {
seconds = 4074768000
}

local data = {
[620909] = {
id = 620909,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "杠精小包",
desc = "哑铃成精，简称杠精",
icon = "CDN:Icon_Wing_501",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_501",
modelType = 1
},
scaleTimes = 160,
beginTime = v0,
suitId = 60496,
suitName = "杠精小包",
suitIcon = "CDN:Icon_Wing_501"
},
[620910] = {
id = 620910,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "抬杠小包",
desc = "每日一抬杠，快乐又健康",
icon = "CDN:Icon_Wing_502",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_502",
modelType = 1
},
scaleTimes = 160,
beginTime = v0,
suitId = 60497,
suitName = "抬杠小包",
suitIcon = "CDN:Icon_Wing_502"
},
[620911] = {
id = 620911,
effect = true,
name = "流光绘梦",
desc = "用纸笔描摹世间万物",
icon = "CDN:Icon_Wing_518",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_518",
modelType = 1
},
shareTexts = {
"悄然绘出诗画长卷"
},
beginTime = v0,
suitId = 60498,
suitName = "流光绘梦",
suitIcon = "CDN:Icon_Wing_518"
},
[620912] = {
id = 620912,
effect = true,
name = "樱花兔兔糕",
desc = "冒险的旅途上，美食必不可少~",
icon = "CDN:Icon_Wing_467",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_467",
modelType = 1,
IconLabelId = 102
},
shareTexts = {
"好吃的东西，当然要一起分享啦！"
},
beginTime = v0,
suitId = 60499,
suitName = "樱花兔兔糕",
suitIcon = "CDN:Icon_Wing_467"
},
[620913] = {
id = 620913,
effect = true,
name = "星绵兔叽",
desc = "遇到烦恼？翻翻万能的兔叽小书包！",
icon = "CDN:Icon_Wing_468",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_468",
modelType = 1,
IconLabelId = 102
},
shareTexts = {
"不管去哪，小兔永远相伴~"
},
beginTime = v0,
suitId = 60500,
suitName = "星绵兔叽",
suitIcon = "CDN:Icon_Wing_468"
},
[620914] = {
id = 620914,
effect = true,
name = "吐司软床",
desc = "睡上香香甜甜的一觉吧~",
icon = "CDN:Icon_Wing_487",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_487",
modelType = 1
},
shareTexts = {
"梦里的吐司吃不完"
},
beginTime = v0,
suitId = 60501,
suitName = "吐司软床",
suitIcon = "CDN:Icon_Wing_487"
},
[620915] = {
id = 620915,
effect = true,
name = "吐司软床",
desc = "睡上香香甜甜的一觉吧~",
icon = "CDN:Icon_Wing_487_01",
outlookConf = {
belongTo = 620914,
fashionValue = 25,
belongToGroup = {
620915,
620916
}
},
resourceConf = {
model = "SM_Wing_487",
material = "MI_Wing_487_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71907,
shareTexts = {
"梦里的吐司吃不完"
}
},
[620916] = {
id = 620916,
effect = true,
name = "吐司软床",
desc = "睡上香香甜甜的一觉吧~",
icon = "CDN:Icon_Wing_487_02",
outlookConf = {
belongTo = 620914,
fashionValue = 25,
belongToGroup = {
620915,
620916
}
},
resourceConf = {
model = "SM_Wing_487",
material = "MI_Wing_487_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71908,
shareTexts = {
"梦里的吐司吃不完"
}
},
[620917] = {
id = 620917,
effect = true,
name = "永不翻车",
desc = "背上翻车鱼，永远不翻车！",
icon = "CDN:Icon_Wing_491",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_491",
modelType = 1
},
shareTexts = {
"我的侧脸，非常可靠！"
},
beginTime = v0,
suitId = 60502,
suitName = "永不翻车",
suitIcon = "CDN:Icon_Wing_491"
},
[620918] = {
id = 620918,
effect = true,
name = "永不翻车",
desc = "背上翻车鱼，永远不翻车！",
icon = "CDN:Icon_Wing_491_01",
outlookConf = {
belongTo = 620917,
fashionValue = 25,
belongToGroup = {
620918,
620919
}
},
resourceConf = {
model = "SM_Wing_491",
material = "MI_Wing_491_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71910,
shareTexts = {
"我的侧脸，非常可靠！"
}
},
[620919] = {
id = 620919,
effect = true,
name = "永不翻车",
desc = "背上翻车鱼，永远不翻车！",
icon = "CDN:Icon_Wing_491_02",
outlookConf = {
belongTo = 620917,
fashionValue = 25,
belongToGroup = {
620918,
620919
}
},
resourceConf = {
model = "SM_Wing_491",
material = "MI_Wing_491_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71911,
shareTexts = {
"我的侧脸，非常可靠！"
}
},
[620920] = {
id = 620920,
effect = true,
name = "星耳兔背包",
desc = "优雅小兔，时尚经得起考验",
icon = "CDN:Icon_Wing_492",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_492",
modelType = 1
},
shareTexts = {
"没有毛毛，也很好摸~"
},
beginTime = v0,
suitId = 60503,
suitName = "星耳兔背包",
suitIcon = "CDN:Icon_Wing_492"
},
[620921] = {
id = 620921,
effect = true,
name = "星耳兔背包",
desc = "优雅小兔，时尚经得起考验",
icon = "CDN:Icon_Wing_492_01",
outlookConf = {
belongTo = 620920,
fashionValue = 25,
belongToGroup = {
620921,
620922
}
},
resourceConf = {
model = "SM_Wing_492",
material = "MI_Wing_492_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71913,
shareTexts = {
"没有毛毛，也很好摸~"
}
},
[620922] = {
id = 620922,
effect = true,
name = "星耳兔背包",
desc = "优雅小兔，时尚经得起考验",
icon = "CDN:Icon_Wing_492_02",
outlookConf = {
belongTo = 620920,
fashionValue = 25,
belongToGroup = {
620921,
620922
}
},
resourceConf = {
model = "SM_Wing_492",
material = "MI_Wing_492_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71914,
shareTexts = {
"没有毛毛，也很好摸~"
}
},
[620923] = {
id = 620923,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "茶理宜世",
desc = "一杯鲜茶，一缕文化",
icon = "CDN:Icon_Wing_503",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_503",
modelType = 1
},
scaleTimes = 160,
shareTexts = {
"邂逅元梦的浪漫时刻"
},
beginTime = v0,
suitId = 60504,
suitName = "茶理宜世",
suitIcon = "CDN:Icon_Wing_503"
},
[620924] = {
id = 620924,
effect = true,
name = "童话闪翼",
desc = "享受微风",
icon = "CDN:Icon_Wing_521",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_521",
modelType = 1
},
shareTexts = {
"启程时刻"
},
beginTime = v0,
suitId = 60505,
suitName = "童话闪翼",
suitIcon = "CDN:Icon_Wing_521"
},
[620925] = {
id = 620925,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 40
}
},
quality = 1,
name = "流音幻歌",
desc = "光的弦，音的翼，谱成一曲醒着的歌",
icon = "CDN:Icon_Wing_514",
outlookConf = {
fashionValue = 350
},
resourceConf = {
model = "SK_Wing_514",
physics = "SK_Wing_514_Physics",
modelType = 2,
idleAnim = "AS_Wing_514_Idle_001",
IconLabelId = 101
},
scaleTimes = 80,
shareTexts = {
"音符在光中翩跹，停驻肩头"
},
beginTime = v0,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_620925.astc",
suitId = 60506,
suitName = "流音幻歌",
suitIcon = "CDN:Icon_Wing_514",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_620925.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_620925.astc",
shareOffset = {
0,
0
}
},
[620926] = {
id = 620926,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "流音幻歌",
desc = "光的弦，音的翼，谱成一曲醒着的歌",
icon = "CDN:Icon_Wing_514_01",
outlookConf = {
belongTo = 620925,
fashionValue = 35,
belongToGroup = {
620926,
620927
}
},
resourceConf = {
model = "SK_Wing_514",
material = "MI_Wing_514_1_HP01;MI_Wing_514_2_HP01;MI_Wing_514_3_HP01",
physics = "SK_Wing_514_Physics",
modelType = 2,
idleAnim = "AS_Wing_514_Idle_001_HP01",
materialSlot = "Wing_1;Wing_2;Wing_3"
},
commodityId = 71918,
scaleTimes = 80,
shareTexts = {
"音符在光中翩跹，停驻肩头"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_620925.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_620925.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_620925.astc",
shareOffset = {
0,
0
}
},
[620927] = {
id = 620927,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "流音幻歌",
desc = "光的弦，音的翼，谱成一曲醒着的歌",
icon = "CDN:Icon_Wing_514_02",
outlookConf = {
belongTo = 620925,
fashionValue = 35,
belongToGroup = {
620926,
620927
}
},
resourceConf = {
model = "SK_Wing_514",
material = "MI_Wing_514_1_HP02;MI_Wing_514_2_HP02;MI_Wing_514_3_HP02",
physics = "SK_Wing_514_Physics",
modelType = 2,
idleAnim = "AS_Wing_514_Idle_001_HP02",
materialSlot = "Wing_1;Wing_2;Wing_3"
},
commodityId = 71919,
scaleTimes = 80,
shareTexts = {
"音符在光中翩跹，停驻肩头"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_620925.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_620925.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_620925.astc",
shareOffset = {
0,
0
}
},
[620928] = {
id = 620928,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 100
}
},
quality = 1,
name = "远山辉玉",
desc = "辉玉其烁，山川其光",
icon = "CDN:Icon_Wing_517",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Wing_517",
emitter = "FX_CH_Decorate_Wing_517_001",
modelType = 1
},
scaleTimes = 80,
shareTexts = {
"更多美景，等你发现"
},
beginTime = v0,
suitId = 60507,
suitName = "远山辉玉",
suitIcon = "CDN:Icon_Wing_517"
},
[620929] = {
id = 620929,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "远山辉玉",
desc = "辉玉其烁，山川其光",
icon = "CDN:Icon_Wing_517_01",
outlookConf = {
belongTo = 620928,
fashionValue = 35,
belongToGroup = {
620929,
620930
}
},
resourceConf = {
model = "SM_Wing_517",
material = "MI_Wing_517_1_HP01;MI_Wing_517_2_HP01",
emitter = "FX_CH_Decorate_Wing_517_001_HP01",
modelType = 1,
materialSlot = "Wing_1;Wing_2"
},
commodityId = 71921,
scaleTimes = 80,
shareTexts = {
"更多美景，等你发现"
}
},
[620930] = {
id = 620930,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "远山辉玉",
desc = "辉玉其烁，山川其光",
icon = "CDN:Icon_Wing_517_02",
outlookConf = {
belongTo = 620928,
fashionValue = 35,
belongToGroup = {
620929,
620930
}
},
resourceConf = {
model = "SM_Wing_517",
material = "MI_Wing_517_1_HP02;MI_Wing_517_2_HP02",
emitter = "FX_CH_Decorate_Wing_517_001_HP02",
modelType = 1,
materialSlot = "Wing_1;Wing_2"
},
commodityId = 71922,
scaleTimes = 80,
shareTexts = {
"更多美景，等你发现"
}
},
[620931] = {
id = 620931,
effect = true,
name = "稚玉之瓶",
desc = "小玉瓶，大奥妙！",
icon = "CDN:Icon_Wing_515",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_515",
modelType = 1
},
shareTexts = {
"小小嫩芽，茁壮成长中~"
},
beginTime = v0,
suitId = 60508,
suitName = "稚玉之瓶",
suitIcon = "CDN:Icon_Wing_515"
},
[620932] = {
id = 620932,
effect = true,
name = "稚玉之瓶",
desc = "小玉瓶，大奥妙！",
icon = "CDN:Icon_Wing_515_01",
outlookConf = {
belongTo = 620931,
fashionValue = 25,
belongToGroup = {
620932,
620933
}
},
resourceConf = {
model = "SM_Wing_515",
material = "MI_Wing_515_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71924,
shareTexts = {
"小小嫩芽，茁壮成长中~"
}
},
[620933] = {
id = 620933,
effect = true,
name = "稚玉之瓶",
desc = "小玉瓶，大奥妙！",
icon = "CDN:Icon_Wing_515_02",
outlookConf = {
belongTo = 620931,
fashionValue = 25,
belongToGroup = {
620932,
620933
}
},
resourceConf = {
model = "SM_Wing_515",
material = "MI_Wing_515_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71925,
shareTexts = {
"小小嫩芽，茁壮成长中~"
}
},
[620934] = {
id = 620934,
effect = true,
name = "半融熊淇凌",
desc = "想让陪伴，更久一点",
icon = "CDN:Icon_Wing_516",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_516",
modelType = 1
},
shareTexts = {
"融化之前，永远在一起"
},
beginTime = v0,
suitId = 60509,
suitName = "半融熊淇凌",
suitIcon = "CDN:Icon_Wing_516"
},
[620935] = {
id = 620935,
effect = true,
name = "半融熊淇凌",
desc = "想让陪伴，更久一点",
icon = "CDN:Icon_Wing_516_01",
outlookConf = {
belongTo = 620934,
fashionValue = 25,
belongToGroup = {
620935,
620936
}
},
resourceConf = {
model = "SM_Wing_516",
material = "MI_Wing_516_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71927,
shareTexts = {
"融化之前，永远在一起"
}
},
[620936] = {
id = 620936,
effect = true,
name = "半融熊淇凌",
desc = "想让陪伴，更久一点",
icon = "CDN:Icon_Wing_516_02",
outlookConf = {
belongTo = 620934,
fashionValue = 25,
belongToGroup = {
620935,
620936
}
},
resourceConf = {
model = "SM_Wing_516",
material = "MI_Wing_516_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71928,
shareTexts = {
"融化之前，永远在一起"
}
},
[620937] = {
id = 620937,
effect = true,
name = "心跳打板器",
desc = "这一刻，慌乱的心跳未经演习",
icon = "CDN:Icon_Wing_519",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_519",
modelType = 1
},
shareTexts = {
"小鹿乱撞，都怪你！"
},
beginTime = v0,
suitId = 60510,
suitName = "心跳打板器",
suitIcon = "CDN:Icon_Wing_519"
},
[620938] = {
id = 620938,
effect = true,
name = "心跳打板器",
desc = "这一刻，慌乱的心跳未经演习",
icon = "CDN:Icon_Wing_519_01",
outlookConf = {
belongTo = 620937,
fashionValue = 25,
belongToGroup = {
620938,
620939
}
},
resourceConf = {
model = "SM_Wing_519",
material = "MI_Wing_519_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71930,
shareTexts = {
"小鹿乱撞，都怪你！"
}
},
[620939] = {
id = 620939,
effect = true,
name = "心跳打板器",
desc = "这一刻，慌乱的心跳未经演习",
icon = "CDN:Icon_Wing_519_02",
outlookConf = {
belongTo = 620937,
fashionValue = 25,
belongToGroup = {
620938,
620939
}
},
resourceConf = {
model = "SM_Wing_519",
material = "MI_Wing_519_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71931,
shareTexts = {
"小鹿乱撞，都怪你！"
}
},
[620940] = {
id = 620940,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "瓜瓜汽水",
desc = "无限气泡，无限果味",
icon = "CDN:Icon_Wing_504",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_504",
modelType = 1
},
scaleTimes = 160,
shareTexts = {
"邂逅元梦的浪漫时刻"
},
beginTime = v0,
suitId = 60511,
suitName = "瓜瓜汽水",
suitIcon = "CDN:Icon_Wing_504"
},
[620941] = {
id = 620941,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "啵啵汽水",
desc = "酸中带甜，盛夏必备",
icon = "CDN:Icon_Wing_505",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_505",
modelType = 1
},
scaleTimes = 160,
shareTexts = {
"邂逅元梦的浪漫时刻"
},
beginTime = v0,
suitId = 60512,
suitName = "啵啵汽水",
suitIcon = "CDN:Icon_Wing_505"
},
[620942] = {
id = 620942,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "淘淘汽水",
desc = "一口香甜，一口回味",
icon = "CDN:Icon_Wing_506",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_506",
modelType = 1
},
scaleTimes = 160,
shareTexts = {
"邂逅元梦的浪漫时刻"
},
beginTime = v0,
suitId = 60513,
suitName = "淘淘汽水",
suitIcon = "CDN:Icon_Wing_506"
},
[620943] = {
id = 620943,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "谜团背包",
desc = "下一个宝物，会是什么呢？",
icon = "CDN:Icon_Wing_520",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_520",
modelType = 1
},
scaleTimes = 160,
shareTexts = {
"邂逅元梦的浪漫时刻"
},
beginTime = v0,
suitId = 60514,
suitName = "谜团背包",
suitIcon = "CDN:Icon_Wing_520"
},
[620944] = {
id = 620944,
effect = true,
name = "破城霸王戟",
desc = "谁与争锋？",
icon = "CDN:Icon_Wing_522",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_522",
modelType = 1
},
shareTexts = {
"将这混乱的时代，拉回正轨"
},
beginTime = v0,
suitId = 60515,
suitName = "破城霸王戟",
suitIcon = "CDN:Icon_Wing_522"
},
[620946] = {
id = 620946,
effect = true,
name = "永恒誓言",
desc = "将誓言写入花瓣，在时间尽头私藏",
icon = "CDN:Icon_Wing_447",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_447",
modelType = 1
},
shareTexts = {
"绽放一瞬，温柔永恒"
},
beginTime = v0,
suitId = 60517,
suitName = "永恒誓言",
suitIcon = "CDN:Icon_Wing_447"
},
[620947] = {
id = 620947,
effect = true,
name = "永恒誓言",
desc = "将誓言写入花瓣，在时间尽头私藏",
icon = "CDN:Icon_Wing_447_01",
outlookConf = {
belongTo = 620946,
fashionValue = 25,
belongToGroup = {
620947,
620948
}
},
resourceConf = {
model = "SM_Wing_447",
material = "MI_Wing_447_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71938,
shareTexts = {
"绽放一瞬，温柔永恒"
}
},
[620948] = {
id = 620948,
effect = true,
name = "永恒誓言",
desc = "将誓言写入花瓣，在时间尽头私藏",
icon = "CDN:Icon_Wing_447_02",
outlookConf = {
belongTo = 620946,
fashionValue = 25,
belongToGroup = {
620947,
620948
}
},
resourceConf = {
model = "SM_Wing_447",
material = "MI_Wing_447_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71939,
shareTexts = {
"绽放一瞬，温柔永恒"
}
},
[620949] = {
id = 620949,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "回“星”小包",
desc = "小针很刺手，使用请注意~",
icon = "CDN:Icon_Wing_495",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Wing_495",
modelType = 1
},
scaleTimes = 160,
beginTime = v0,
suitId = 60518,
suitName = "回“星”小包",
suitIcon = "CDN:Icon_Wing_495"
},
[620950] = {
id = 620950,
effect = true,
name = "”鸭“力泳圈",
desc = "鸭鸭泳圈在，有浮力，没压力！",
icon = "CDN:Icon_Wing_513",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_513",
modelType = 1
},
shareTexts = {
"鸭鸭就是很可爱！"
},
beginTime = v0,
suitId = 60519,
suitName = "”鸭“力泳圈",
suitIcon = "CDN:Icon_Wing_513"
},
[620951] = {
id = 620951,
effect = true,
name = "”鸭“力泳圈",
desc = "鸭鸭泳圈在，有浮力，没压力！",
icon = "CDN:Icon_Wing_513_01",
outlookConf = {
belongTo = 620950,
fashionValue = 25,
belongToGroup = {
620951,
620952
}
},
resourceConf = {
model = "SM_Wing_513",
material = "MI_Wing_513_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71942,
shareTexts = {
"鸭鸭就是很可爱！"
}
},
[620952] = {
id = 620952,
effect = true,
name = "”鸭“力泳圈",
desc = "鸭鸭泳圈在，有浮力，没压力！",
icon = "CDN:Icon_Wing_513_02",
outlookConf = {
belongTo = 620950,
fashionValue = 25,
belongToGroup = {
620951,
620952
}
},
resourceConf = {
model = "SM_Wing_513",
material = "MI_Wing_513_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71943,
shareTexts = {
"鸭鸭就是很可爱！"
}
},
[620953] = {
id = 620953,
effect = true,
name = "海鱼王子",
desc = "“鱼”你相遇，是这片海最美的童话",
icon = "CDN:Icon_Wing_509",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Wing_509",
modelType = 1
},
shareTexts = {
"自带王冠，闪耀登场"
},
beginTime = v0,
suitId = 60520,
suitName = "海鱼王子",
suitIcon = "CDN:Icon_Wing_509"
},
[620954] = {
id = 620954,
effect = true,
name = "海鱼王子",
desc = "“鱼”你相遇，是这片海最美的童话",
icon = "CDN:Icon_Wing_509_01",
outlookConf = {
belongTo = 620953,
fashionValue = 25,
belongToGroup = {
620954,
620955
}
},
resourceConf = {
model = "SM_Wing_509",
material = "MI_Wing_509_HP01",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71945,
shareTexts = {
"自带王冠，闪耀登场"
}
},
[620955] = {
id = 620955,
effect = true,
name = "海鱼王子",
desc = "“鱼”你相遇，是这片海最美的童话",
icon = "CDN:Icon_Wing_509_02",
outlookConf = {
belongTo = 620953,
fashionValue = 25,
belongToGroup = {
620954,
620955
}
},
resourceConf = {
model = "SM_Wing_509",
material = "MI_Wing_509_HP02",
modelType = 1,
materialSlot = "Wing"
},
commodityId = 71946,
shareTexts = {
"自带王冠，闪耀登场"
}
},
[620956] = {
id = 620956,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 100
}
},
quality = 1,
name = "幻潮音梦",
desc = "神秘歌声，从大海彼方传来",
icon = "CDN:Icon_Wing_500",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Wing_500",
emitter = "FX_CH_Decorate_Wing_500_001",
modelType = 1
},
scaleTimes = 80,
shareTexts = {
"神秘海螺，带来梦想"
},
beginTime = v0,
suitId = 60521,
suitName = "幻潮音梦",
suitIcon = "CDN:Icon_Wing_500"
},
[620957] = {
id = 620957,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "幻潮音梦",
desc = "神秘歌声，从大海彼方传来",
icon = "CDN:Icon_Wing_500_01",
outlookConf = {
belongTo = 620956,
fashionValue = 35,
belongToGroup = {
620957,
620958
}
},
resourceConf = {
model = "SM_Wing_500",
material = "MI_Wing_500_1_HP01;MI_Wing_500_2_HP01",
emitter = "FX_CH_Decorate_Wing_500_001_HP01",
modelType = 1,
materialSlot = "Wing_1;Wing_2"
},
scaleTimes = 80,
shareTexts = {
"神秘海螺，带来梦想"
}
},
[620958] = {
id = 620958,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "幻潮音梦",
desc = "神秘歌声，从大海彼方传来",
icon = "CDN:Icon_Wing_500_02",
outlookConf = {
belongTo = 620956,
fashionValue = 35,
belongToGroup = {
620957,
620958
}
},
resourceConf = {
model = "SM_Wing_500",
material = "MI_Wing_500_1_HP02;MI_Wing_500_2_HP02",
emitter = "FX_CH_Decorate_Wing_500_001_HP02",
modelType = 1,
materialSlot = "Wing_1;Wing_2"
},
scaleTimes = 80,
shareTexts = {
"神秘海螺，带来梦想"
}
}
}

local mt = {
effect = false,
type = "ItemType_BackOrnament",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
scaleTimes = 120,
shareOffset = {
-10,
25
},
previewShareOffset = {
0,
0
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data