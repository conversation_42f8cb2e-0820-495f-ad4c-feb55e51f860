--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_社交商品.xlsm: 邀请框

local data = {
[890500] = {
id = 890500,
type = "ItemType_FriendInviteBox",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 2,
name = "星梦奇缘",
desc = "限时活动",
icon = "CDN:Icon_Frame_TeamInvite_001",
resourceConf = {
showPic = "CDN:T_FriendPopupShow_Theme_001",
bg = "CDN:T_TeamInvite_Theme_001"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 4097750400
},
endTime = {
seconds = 4097750400
},
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[890501] = {
id = 890501,
type = "ItemType_FriendInviteBox",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 2,
name = "田园牧歌",
desc = "卡牌集换",
icon = "CDN:Icon_Frame_TeamInvite_002",
resourceConf = {
showPic = "CDN:T_FriendPopupShow_Theme_005",
bg = "CDN:T_TeamInvite_Theme_002"
},
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 4097750400
},
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[890502] = {
id = 890502,
type = "ItemType_FriendInviteBox",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 2,
name = "绮丽霜华",
desc = "限时活动",
icon = "CDN:Icon_Frame_TeamInvite_003",
resourceConf = {
showPic = "CDN:T_FriendPopupShow_Theme_007",
bg = "CDN:T_TeamInvite_Theme_003"
},
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 4097664000
},
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[890503] = {
id = 890503,
type = "ItemType_FriendInviteBox",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 2,
name = "神迹之辉",
desc = "限时活动",
icon = "CDN:Icon_Frame_TeamInvite_004",
resourceConf = {
showPic = "CDN:T_FriendPopupShow_Theme_010",
bg = "CDN:T_TeamInvite_Theme_005"
},
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 4097664000
}
},
[890504] = {
id = 890504,
type = "ItemType_FriendInviteBox",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 2,
name = "市集漫游",
desc = "卡牌集换",
icon = "CDN:Icon_Frame_TeamInvite_005",
resourceConf = {
showPic = "CDN:T_FriendPopupShow_Theme_014",
bg = "CDN:T_TeamInvite_Theme_006"
},
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1741190400
},
endTime = {
seconds = 4097750400
}
},
[890505] = {
id = 890505,
type = "ItemType_FriendInviteBox",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 1,
name = "主页互赞",
desc = "社区活动获得",
icon = "CDN:Icon_Frame_TeamInvite_006",
resourceConf = {
showPic = "CDN:T_FriendPopupShow_Theme_015",
bg = "D_TeamInvite_006"
},
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1741190400
},
endTime = {
seconds = 4097750400
},
srcPakGroupId = 10001
},
[890506] = {
id = 890506,
type = "ItemType_FriendInviteBox",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 75
}
},
quality = 1,
name = "幻彩调律",
desc = "限时活动",
icon = "CDN:Icon_Frame_TeamInvite_007",
resourceConf = {
showPic = "CDN:T_FriendPopupShow_Theme_016",
bg = "D_TeamInvite_007"
},
showInView = 1,
isDynamic = 1,
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 4097750400
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data