--com.tencent.wea.xlsRes.table_PlayerGuideData => excel/xls/X_新手引导表_主玩法.xlsx: 新引导总表

local v0 = 1

local v1 = "KeyStepCompleteEvent"

local v2 = "{c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"}}"

local v3 = "{c_abTest = {testId ={7252190}}}"

local v4 = {
condition = {
{
conditionType = 1902
}
}
}

local data = {
[10015] = {
GuideID = 10015,
Comment = "模式选择-开始游戏（弱引导）",
Priority = -99,
StartConditions = "{c_isNoTouchOnLobbyView={seconds=2}}",
TriggerEventParam = "UI_GameplaySelection_View",
FinishEvent = v1,
InterruptConditions = "{c_isInvalidMatchTypeInNewComerModeSelect = {}}",
CanBeJump = true,
DisableConditionsInCloudGame = v3,
InterruptCondition = {
condition = {
{
conditionType = 1901,
value = 0
}
}
},
StartCondition = {
condition = {
{
conditionType = 1908,
value = 2
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10200] = {
GuideID = 10200,
Comment = "新手关卡引导-提前完成",
Priority = 100,
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_GUIDE_PRE_COMPLETE_TRIGGER",
FinishEvent = v1,
TriggerFinishEvent = "ON_NEWCOMER_PRE_MODE_SELECT_GUIDE_TRIGGER_COMPLETE",
CompleteConditions = "{c_isStartAnyMatchFromCloudEnv={}}",
DisableConditionsInCloudGame = v3,
CompleteCondition = {
condition = {
{
conditionType = 1907
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10211] = {
GuideID = 10211,
Comment = "新模式选择-是否完成模式选择引导",
Priority = 100,
StartConditions = "{c_guideId= {guideId = {10213}}}",
TriggerEventParam = "UI_GameplaySelection_View",
FinishEvent = v1,
CompleteConditions = "{c_isStartAnyMatchFromCloudEnv={}}",
DisableConditionsInCloudGame = v3,
CompleteCondition = {
condition = {
{
conditionType = 1907
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10212] = {
GuideID = 10212,
Comment = "新模式选择-是否完成模式选择引导-保底",
Priority = 100,
StartConditions = "{c_guideId= {guideId = {10213}}}",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_TRIGGER_NEW_MODEL_SELECT_GUIDE_FINISH_IN_CREATE_ROLE_SCENE",
FinishEvent = v1,
CompleteConditions = "{c_isStartAnyMatchFromCloudEnv={}}",
DisableConditionsInCloudGame = v3,
CompleteCondition = {
condition = {
{
conditionType = 1907
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10213] = {
GuideID = 10213,
Comment = "新模式选择-强制进入模式选择场景",
Priority = 100,
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_TRIGGER_MODE_SELECT_GUIDE_AFTER_CREATE_ROLE",
FinishEvent = v1,
CompleteConditions = "{c_conditionMode = {mode = \"ANY\"}, c_isStartAnyMatchFromCloudEnv={}, c_guideId= {guideId = {11205}}}",
DisableConditionsInCloudGame = v3,
CompleteCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1904,
value = 11205
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[19997] = {
GuideID = 19997,
Comment = "新手关卡引导-判断模式选择是否能够正常打开（仅做判断，永不触发）",
Priority = -999,
StartConditions = "{c_isAnyGuideAvailable={guideIdList={10213}}}",
TriggerEventType = 1,
TriggerEventParam = "NONE_XXX_NEVER_COMPLETE",
FinishEvent = v1,
CanBeJump = true,
DisableConditionsInCloudGame = v3,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 905,
value = {
10213
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[19998] = {
GuideID = 19998,
Comment = "新手关卡引导-判断开启新模式选择的先决条件是否满足（保底，避免异常卡死）",
Priority = 100,
StartConditions = "{c_guideId= {guideId = {10200}}}",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_TRIGGER_NEW_MODEL_SELECT_GUIDE_FINISH_IN_CREATE_ROLE_SCENE",
FinishEvent = v1,
CompleteConditions = "{c_isStartAnyMatchFromCloudEnv={}}",
DisableConditionsInCloudGame = v3,
CompleteCondition = {
condition = {
{
conditionType = 1907
}
}
},
StartCondition = {
condition = {
{
conditionType = 1904,
value = 10200
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[19999] = {
GuideID = 19999,
Comment = "新手关卡引导-判断开启新模式选择的先决条件是否满足",
Priority = 100,
StartConditions = "{c_guideId= {guideId = {10200}}}",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_PRE_MODE_SELECT_GUIDE_TRIGGER_COMPLETE",
FinishEvent = v1,
CompleteConditions = "{c_isStartAnyMatchFromCloudEnv={}}",
DisableConditionsInCloudGame = v3,
CompleteCondition = {
condition = {
{
conditionType = 1907
}
}
},
StartCondition = {
condition = {
{
conditionType = 1904,
value = 10200
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10101] = {
GuideID = 10101,
Comment = "新手关卡-NPC介绍",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_SCENE_BEGIN",
FinishEvent = v1,
TriggerFinishEvent = "Newcomer_Move",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910101}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = v3,
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910101
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10102] = {
GuideID = 10102,
Comment = "新手关卡-移动引导",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Move",
FinishEvent = v1,
TriggerFinishEvent = "Newcomer_Jump1",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910102}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252191}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910102
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10103] = {
GuideID = 10103,
Comment = "新手关卡-跳跃引导1 ",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Jump1",
FinishEvent = v1,
EndEvent = "Newcomer_Jump1_Over",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910103}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252192}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910103
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10104] = {
GuideID = 10104,
Comment = "新手关卡-跳跃引导2",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Jump2",
FinishEvent = v1,
EndEvent = "Newcomer_Jump2_Over",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910104}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252193}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910104
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10105] = {
GuideID = 10105,
Comment = "新手关卡-跳扑引导",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Dive",
FinishEvent = "Newcomer_Dive_Finish",
TriggerFinishEvent = "Newcomer_Introduction_Bullet",
EndEvent = "Newcomer_Dive_Over",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910105}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252194}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910105
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10106] = {
GuideID = 10106,
Comment = "新手关卡-NPC介绍大炮",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Introduction_Bullet",
FinishEvent = v1,
TriggerFinishEvent = "Newcomer_AdjustCamera",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910106}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252195}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910106
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10107] = {
GuideID = 10107,
Comment = "新手关卡-调整镜头",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_AdjustCamera",
FinishEvent = v1,
TriggerFinishEvent = "Newcomer_Dash",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910107}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252196}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910107
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10108] = {
GuideID = 10108,
Comment = "新手关卡-使用冲刺",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Dash",
FinishEvent = v1,
EndEvent = "Newcomer_Dash_Over",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910108}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252197}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910108
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10109] = {
GuideID = 10109,
Comment = "新手关卡-NPC介绍台子",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Introduction_Cliff",
FinishEvent = v1,
TriggerFinishEvent = "Newcomer_SpringBoard",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910109}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252198}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910109
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10110] = {
GuideID = 10110,
Comment = "新手关卡-弹板引导",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_SpringBoard",
FinishEvent = "Newcomer_SpringBoard_Finish",
TriggerFinishEvent = "Newcomer_Intronduction_Box",
EndEvent = "Newcomer_SpringBoard_Over",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910110}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252199}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910110
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10112] = {
GuideID = 10112,
Comment = "新手关卡-捡滚球引导",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Bommerang",
FinishEvent = "Newcomer_Bommerang_Finish",
TriggerFinishEvent = "Newcomer_Encourage",
EndEvent = "Newcomer_Bommerang_Over",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910112}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252200}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910112
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10114] = {
GuideID = 10114,
Comment = "新手关卡-NPC祝贺",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Congratulate",
FinishEvent = v1,
TriggerFinishEvent = "Newcomer_Finish",
GuideGroup = 1,
CompleteConditions = "{c_guideId= {guideId = {910114}}}",
bEnabledInCloudGame = true,
DisableConditionsInCloudGame = "{c_abTest = {testId ={7252201}}}",
bDisabledInPC = true,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 910114
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[10019] = {
GuideID = 10019,
Comment = "大厅引导至农场",
Priority = 104,
StartConditions = "{c_grade = {grade = 1},c_pickGuideByMatchTypeSelected={typeList={666}}}",
FinishEvent = v1,
InterruptConditions = "{c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"},c_conditionMode={mode=\"ANY\"}}",
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1902
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
},
{
type = 907,
value = {
666
}
}
}
}
}
}
},
[10005] = {
GuideID = 10005,
Comment = "引导新手奖励系统",
Priority = 105,
StartConditions = "{c_grade = {grade = 1},c_abTest = {testId ={7254180}}}",
FinishEvent = v1,
CompleteConditions = "{c_guideId= {guideId = {10210}}}",
InterruptConditions = v2,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptedSceneIdByPushFace = 2,
InterruptCondition = v4,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 10210
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
},
{
type = 906,
value = {
7254180
}
}
}
}
}
}
},
[10020] = {
GuideID = 10020,
Comment = "引导新手奖励系统",
Priority = 105,
StartConditions = "{c_grade = {grade = 1},c_abTest = {testId ={7254181}}}",
FinishEvent = v1,
CompleteConditions = "{c_guideId= {guideId = {10210}}}",
InterruptConditions = v2,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptedSceneIdByPushFace = 2,
InterruptCondition = v4,
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 10210
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
},
{
type = 906,
value = {
7254181
}
}
}
}
}
}
},
[10210] = {
GuideID = 10210,
Comment = "新模式选择-引导点击更多玩法",
Priority = 100,
StartConditions = "{c_grade = {grade = 1}}",
FinishEvent = v1,
CompleteConditions = "{c_grade = {grade = 5}, c_isStartAnyMatchFromCloudEnv={}, c_conditionMode={mode=\"ANY\"} }",
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = v4,
CompleteCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1907,
subConditionList = {
{
type = 6,
value = {
5
}
}
}
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10220] = {
GuideID = 10220,
Comment = "第一次开局引导（1级）",
Priority = 99,
StartConditions = "{c_grade = {grade = 1},c_isLobbyModeBtnShow= {buttonIdex = 0},c_pickGuideByMatchTypeSelected={typeList={0,4,5600,105,104,501,604}}}",
FinishEvent = "ON_ENTER_EXIT_MOD_STAGE",
CompleteConditions = "{c_grade = {grade = 5}, c_isStartAnyMatchFromCloudEnv={}, c_conditionMode={mode=\"ANY\"} ,c_guideId= {guideId = 910220}}",
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
bDisabledInPC = true,
InterruptCondition = v4,
CompleteCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1904,
value = 910220,
subConditionList = {
{
type = 6,
value = {
5
}
}
}
}
}
},
StartCondition = {
condition = {
{
conditionType = 1909,
value = 0,
subConditionList = {
{
type = 6,
value = {
1
}
},
{
type = 907,
value = {
0,
4,
5600,
105,
104,
501,
604
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10002] = {
GuideID = 10002,
Comment = "引导进入抽奖界面（1级）",
Priority = 80,
StartConditions = "{c_grade = {grade = 1}}",
FinishEvent = v1,
CompleteConditions = "{c_guideId= {guideId = {10003}}}",
InterruptConditions = "{c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"}, c_IsLobbyMoreUIVisible={}, c_conditionMode={mode=\"ANY\"}}",
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1902
},
{
conditionType = 1903
}
}
},
CompleteCondition = {
condition = {
{
conditionType = 1904,
value = 10003
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10003] = {
GuideID = 10003,
Comment = "十连抽引导（1级）",
Priority = 70,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_Lottery_MainView",
FinishEvent = v1,
EndEvent = "ON_CLOSE_UI_UI_Lottery_MainView",
InterruptConditions = "{c_NotCompletedGuideId={guideId = 10002}}",
DisableConditionsInCloudGame = v3,
InterruptCondition = {
conditionRelation = "ConditionRelation_Not",
condition = {
{
conditionType = 1904,
value = 10002
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10004] = {
GuideID = 10004,
Comment = "背包引导（1级）",
Priority = 60,
StartConditions = "{c_grade = {grade = 1},c_guideId= {guideId = 10003}}",
FinishEvent = v1,
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = v4,
StartCondition = {
condition = {
{
conditionType = 1904,
value = 10003,
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10011] = {
GuideID = 10011,
Comment = "开局引导（1级）",
Priority = 50,
StartConditions = "{c_grade = {grade = 1},c_isLobbyModeBtnShow= {buttonIdex = 0},c_pickGuideByMatchTypeSelected={typeList={0,4,5600,105,104,501,604}}}",
FinishEvent = "ON_ENTER_EXIT_MOD_STAGE",
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = v4,
StartCondition = {
condition = {
{
conditionType = 1909,
value = 0,
subConditionList = {
{
type = 6,
value = {
1
}
},
{
type = 907,
value = {
0,
4,
5600,
105,
104,
501,
604
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10006] = {
GuideID = 10006,
Comment = "排位机制介绍（首局排位赛结算）",
Priority = 10,
TriggerEventParam = "UI_InLevelFinalQualifying",
FinishEvent = v1,
DisableConditionsInCloudGame = v3,
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10009] = {
GuideID = 10009,
Comment = "引导到星世界-乐园（3级）",
Priority = 40,
StartConditions = "{c_grade = {grade = 3}}",
FinishEvent = v1,
CompleteConditions = "{c_guideId= {guideId = {50007, 50008,910009}}}",
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
bDisabledInPC = true,
InterruptCondition = v4,
CompleteCondition = {
condition = {
{
subConditionList = {
{
type = 902,
value = {
5007,
5008,
910009
}
}
}
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
3
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10010] = {
GuideID = 10010,
Comment = "引导到星世界-巡游（5级）",
Priority = 10,
StartConditions = "{c_grade = {grade = 5}}",
FinishEvent = v1,
CompleteConditions = "{c_guideId= {guideId = 50004,910010}}",
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
bDisabledInPC = true,
InterruptCondition = v4,
CompleteCondition = {
condition = {
{
subConditionList = {
{
type = 902,
value = {
50004,
910010
}
}
}
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
5
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10014] = {
GuideID = 10014,
Comment = "开局（弱引导）",
Priority = -99,
StartConditions = "{c_isNoTouchOnLobbyView={seconds=5}}",
FinishEvent = v1,
CompleteConditions = "{c_grade = {grade = 10}}",
InterruptConditions = v2,
CanBeJump = true,
DisableConditionsInCloudGame = v3,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
CompleteCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
10
}
}
}
}
}
},
StartCondition = {
condition = {
{
conditionType = 1908,
value = 5
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10018] = {
GuideID = 10018,
Comment = "农场弱引导（没玩过的会触发）（5级）",
Priority = 5,
StartConditions = "{c_notInShield = {shieldTime=86400,guideId = 10018},c_hasNotCreateFarm={},c_grade = {grade = 5}}",
FinishEvent = v1,
TriggerFinishEvent = "ON_NEWCOMER_FARM_ENTRANCE_GUIDE_END",
EndEvent = "ON_NEWCOMER_FARM_ENTRANCE_GUIDE_END",
TriggerEndEvent = "ON_NEWCOMER_FARM_ENTRANCE_GUIDE_END",
InterruptConditions = "{c_conditionMode = {mode = \"PART\"},c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"},c_inShield={shieldTime=86400,guideId = 10018}}",
OnGuideTriggerEvent = "ON_NEWCOMER_FARM_ENTRANCE_GUIDE_TRIGGER",
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1902
}
}
},
StartCondition = {
condition = {
{
conditionType = 1910,
subConditionList = {
{
type = 6,
value = {
5
}
},
{
type = 908,
value = {
10018,
86400
}
}
}
}
}
}
},
[10116] = {
GuideID = 10116,
Comment = "竖屏聊天引导（弱引导）",
StartConditions = "{c_isNotPCModel={}}",
TriggerEventParam = "UI_NewChat_Main",
InterruptConditions = "{c_conditionMode = {mode = \"PART\"},c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"},c_highestFullScreenUIisNotUILobbyView={}}",
InterruptCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1902
},
{
conditionType = 1905
}
}
},
StartCondition = {
condition = {
{
conditionType = 1912
}
}
}
},
[18001] = {
GuideID = 18001,
Comment = "段位详情界面",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_UI_PLAYER_INFO_LEVEL_GUIDE",
FinishEvent = v1
},
[18002] = {
GuideID = 18002,
Comment = "主玩法局内跳跃键高亮",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_UI_OGC_STAGE_JUMP_GUIDE",
FinishEvent = v1
},
[10301] = {
GuideID = 10301,
Comment = "引导玩家点击短剧按钮",
Priority = 0,
StartConditions = "{c_grade = {grade = 10}}",
FinishEvent = v1,
InterruptConditions = v2,
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
10
}
}
}
}
}
}
},
[10500] = {
GuideID = 10500,
Comment = "活动引导_半周年走格子活动_首次进入",
TriggerEventType = 1,
TriggerEventParam = "EVENTS_FLYINGCHESS_FIRST_START",
FinishEvent = v1,
TriggerFinishEvent = "EVENTS_FLYINGCHESS_FIRST_START_FINISH",
TriggerEndEvent = "EVENTS_FLYINGCHESS_FIRST_START_FINISH",
bEnabledInCloudGame = true
},
[10501] = {
GuideID = 10501,
Comment = "活动引导_半周年走格子活动_首次获得碎片奖励",
TriggerEventType = 1,
TriggerEventParam = "EVENTS_FLYINGCHESS_FIRST_GET_ITEM",
FinishEvent = v1,
TriggerFinishEvent = "EVENTS_FLYINGCHESS_FIRST_GET_ITEM_FINISH",
TriggerEndEvent = "EVENTS_FLYINGCHESS_FIRST_GET_ITEM_FINISH",
bEnabledInCloudGame = true
},
[10510] = {
GuideID = 10510,
Comment = "活动引导_新界奇遇_首次进入",
TriggerEventType = 1,
TriggerEventParam = "Events_ThemeAdventure_FirstStart",
FinishEvent = v1,
TriggerFinishEvent = "Events_ThemeAdventure_FirstStartFinish",
TriggerEndEvent = "Events_ThemeAdventure_FirstStartFinish",
bEnabledInCloudGame = true
},
[10601] = {
GuideID = 10601,
Comment = "奖杯征程系统引导",
Priority = 9,
StartConditions = "{c_grade = {grade = 3}}",
FinishEvent = v1,
TriggerFinishEvent = "ON_NEWCOMER_UI_CUP_GUIDE_FINISH",
EndEvent = "ON_NEWCOMER_UI_CUP_GUIDE_FINISH",
TriggerEndEvent = "ON_NEWCOMER_UI_CUP_GUIDE_FINISH",
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
3
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7252190
}
}
}
}
}
}
},
[10602] = {
GuideID = 10602,
Comment = "导航页系统引导",
Priority = 0,
StartConditions = "{c_grade = {grade = 4}}",
FinishEvent = v1,
InterruptConditions = v2,
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
4
}
}
}
}
}
}
},
[10701] = {
GuideID = 10701,
Comment = "皮肤租借教学引导",
StartConditions = "{c_grade = {grade = 1},c_hasTeammate={},c_hasRentSkinCoin={}}",
FinishEvent = v1,
StartCondition = {
condition = {
{
conditionType = 1913,
subConditionList = {
{
type = 6,
value = {
2
}
}
}
},
{
conditionType = 1914
}
}
}
},
[10801] = {
GuideID = 10801,
Comment = "广场大厅引导进组队秀大厅",
Priority = 0,
StartConditions = "{c_grade = {grade = 8}}",
FinishEvent = v1,
InterruptConditions = "{c_state={state=\"E_STATE_TEAM_MATCH_EX\"}}",
InterruptCondition = {
condition = {
{
conditionType = 1917
}
}
}
},
[10901] = {
GuideID = 10901,
Comment = "闪电赛教学引导",
StartConditions = "{c_JudgeIsLightningGame = {}}",
TriggerEventParam = "UI_InLevelFinalAccount",
FinishEvent = v1
},
[10903] = {
GuideID = 10903,
Comment = "再来一局引导",
TriggerEventParam = "UI_InLevelFinalAgainTogetherHint",
FinishEvent = v1,
InterruptConditions = "{c_conditionMode = {mode = \"PART\"},c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"},c_isCoMatchBattleTogetherGuidNotShow={UIName = {\"UI_Arena_FinalAccount_Detail3V3\",\"UI_BS_FinalAccount_DetailBattleground\", \"UI_InLevel_GameDetails\", \"UI_ScoringSystemPanel_V2\"}}}",
OtherTriggerEventParam = "UI_InLevelFinalAccount;UI_NR3E0_InLevelFinalAccount;UI_DDP_FinalAccount;UI_Arena_FinalAccount",
InterruptCondition = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 1902
},
{
conditionType = 1906
}
}
}
},
[11001] = {
GuideID = 11001,
Comment = "组队秀二级页引导返回大厅按钮",
Priority = 8,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_PreviewTeam_View",
FinishEvent = v1,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
}
},
[11101] = {
GuideID = 11101,
Comment = "时装个性化引导",
StartConditions = "{c_grade = {grade = 1},c_isSettingBtnVisible={}}",
TriggerEventParam = "UI_Bag_MainView",
FinishEvent = v1,
InterruptConditions = v2,
OtherTriggerEventParam = "UI_Bag_MainView",
InterruptCondition = v4,
StartCondition = {
condition = {
{
conditionType = 1915,
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
}
},
[11102] = {
GuideID = 11102,
Comment = "时装个性化引导（祈愿和商城）",
StartConditions = "{c_grade = {grade = 1},c_isSettingBtnVisible={}}",
TriggerEventParam = "UI_DrawReward_MallView",
FinishEvent = v1,
InterruptConditions = v2,
OtherTriggerEventParam = "UI_DrawReward_MallView;UI_Mall_MainView",
InterruptCondition = v4,
StartCondition = {
condition = {
{
conditionType = 1915,
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
}
},
[11103] = {
GuideID = 11103,
Comment = "新商城引导",
Priority = 0,
StartConditions = "{c_grade = {grade = 10}}",
FinishEvent = v1,
InterruptConditions = v2,
bEnabledInCloudGame = true,
bEnabledInVA = true,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
10
}
}
}
}
}
}
},
[11104] = {
GuideID = 11104,
Comment = "发现系统局后引导（APP端）",
StartConditions = "{c_grade = {grade = 3}}",
TriggerEventType = 1,
TriggerEventParam = "Events_DiscoveryGuidance_LocationStart",
FinishEvent = v1,
TriggerEndEvent = "Events_DiscoveryGuidance_LocationEnd",
bEnabledInCloudGame = true,
bEnabledInVA = true,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
3
}
}
}
}
}
}
},
[11105] = {
GuideID = 11105,
Comment = "发现系统局后引导（小游戏端）",
StartConditions = "{c_grade = {grade = 3}}",
TriggerEventType = 1,
TriggerEventParam = "Events_DiscoveryGuidance_WXLocationStart",
FinishEvent = v1,
TriggerEndEvent = "Events_DiscoveryGuidance_LocationEnd",
bEnabledInCloudGame = true,
bEnabledInVA = true,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
3
}
}
}
}
}
}
},
[11106] = {
GuideID = 11106,
Comment = "快捷互动栏（新号随新手流程）",
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_LobbyJoy",
FinishEvent = v1,
InterruptConditions = v2,
bEnabledInCloudGame = true,
bEnabledInVA = true,
InterruptCondition = v4
},
[11199] = {
GuideID = 11199,
Comment = "快捷互动栏",
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_LobbyJoy",
FinishEvent = v1,
CompleteConditions = "{c_guideId= {guideId = {11106}}}",
InterruptConditions = v2,
bEnabledInCloudGame = true,
bEnabledInVA = true,
RegisterEndTime = {
seconds = 1748448000
},
InterruptCondition = v4
},
[11201] = {
GuideID = 11201,
Comment = "卡牌集换系统引导",
Priority = 8,
StartConditions = "{c_grade = {grade = 3}}",
FinishEvent = v1,
TriggerFinishEvent = "ON_NEWCOMER_UI_CUP_GUIDE_FINISH",
EndEvent = "ON_NEWCOMER_UI_CUP_GUIDE_FINISH",
TriggerEndEvent = "ON_NEWCOMER_UI_CUP_GUIDE_FINISH",
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
3
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[11205] = {
GuideID = 11205,
Comment = "次留优化专项-实验组A_默认进农场+退出提示",
Priority = 0,
StartConditions = "{c_abTest = {testId ={7254814}}}",
TriggerEventType = 1,
TriggerEventParam = "ON_BEGIN_FARM_GUIDE",
FinishEvent = v1,
TriggerFinishEvent = "ON_BEGIN_FARM_GUIDE",
EndEvent = "ON_BEGIN_FARM_GUIDE",
TriggerEndEvent = "ON_BEGIN_FARM_GUIDE",
StartCondition = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7254814
}
}
}
}
}
}
},
[11206] = {
GuideID = 11206,
Comment = "次留优化专项-实验组B_新手玩法选择+退出提示",
Priority = 0,
StartConditions = "{c_abTest = {testId ={7254815}}}",
TriggerEventType = 1,
TriggerEventParam = "ON_BEGIN_FARM_GUIDE",
FinishEvent = v1,
TriggerFinishEvent = "ON_BEGIN_FARM_GUIDE",
EndEvent = "ON_BEGIN_FARM_GUIDE",
TriggerEndEvent = "ON_BEGIN_FARM_GUIDE",
StartCondition = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7254815
}
}
}
}
}
}
},
[11207] = {
GuideID = 11207,
Comment = "次留优化专项-实验组A和B_退出提示辅助引导（等级小于等于5级生效）",
Priority = 0,
StartConditions = "{c_abTest = {testId ={7254814, 7254815}}}",
TriggerEventType = 1,
TriggerEventParam = "ON_NEWCOMER_MORE_GAME_PLAY_TRIGGER",
FinishEvent = v1,
CompleteConditions = "{c_grade = {grade = 6}}",
CompleteCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
6
}
}
}
}
}
},
StartCondition = {
condition = {
{
subConditionList = {
{
type = 906,
value = {
7254814,
7254815
}
}
}
}
}
}
},
[11210] = {
GuideID = 11210,
Comment = "中途退出后结算界面更多玩法的引导",
Priority = 90,
StartConditions = "{c_isQuitGame={}} ",
TriggerEventParam = "UI_InLevelFinalAccount",
FinishEvent = v1,
EndEvent = "ON_OPEN_UI_UI_Grade_GradeUpTips",
CompleteConditions = "{c_guideId= {guideId = {10211}}}",
bEnabledInCloudGame = true
},
[11211] = {
GuideID = 11211,
Comment = "结算界面农场玩法的引导",
Priority = 100,
TriggerEventParam = "UI_InLevelFinalAccount",
FinishEvent = v1,
EndEvent = "ON_OPEN_UI_UI_Grade_GradeUpTips",
CompleteConditions = "{c_grade = {grade = 5}}",
bEnabledInCloudGame = true
},
[10115] = {
GuideID = 10115,
Comment = "跨栏飞侠新手关-移动引导",
TriggerEventType = 1,
TriggerEventParam = "Newcomer_Move1",
FinishEvent = v1,
CompleteConditions = "{c_guideId= {guideId = {10102}}}",
bEnabledInCloudGame = true,
bDisabledInPC = true
},
[11300] = {
GuideID = 11300,
Comment = "拍照功能引导（镜头旋转+缩放）",
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_Photo_Main",
FinishEvent = v1,
InterruptConditions = v2,
bEnabledInCloudGame = true,
InterruptCondition = v4
},
[11301] = {
GuideID = 11301,
Comment = "拍照功能引导（切换模式）",
StartConditions = "{c_grade = {grade = 1},c_guideId= {guideId = 11300}}",
TriggerEventParam = "UI_Photo_Main",
FinishEvent = v1,
InterruptConditions = v2,
bEnabledInCloudGame = true,
InterruptCondition = v4
},
[11302] = {
GuideID = 11302,
Comment = "拍照功能引导（录像）",
StartConditions = "{c_grade = {grade = 1},c_guideId= {guideId = 11301}}",
TriggerEventParam = "UI_Photo_Main",
FinishEvent = v1,
InterruptConditions = v2,
bEnabledInCloudGame = true,
InterruptCondition = v4
},
[11401] = {
GuideID = 11401,
Comment = "痛包使用引导",
Priority = 7,
StartConditions = "{c_grade = {grade = 3}}",
TriggerEventType = 1,
TriggerEventParam = "OnEquipedTongBao",
FinishEvent = v1,
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
3
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[11402] = {
GuideID = 11402,
Comment = "推图展板使用引导",
Priority = 7,
StartConditions = "{c_grade = {grade = 3}}",
TriggerEventType = 1,
TriggerEventParam = "OnEquipedTuiTuZhanBan",
FinishEvent = v1,
InterruptConditions = v2,
DisableConditionsInCloudGame = v3,
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
3
}
}
}
}
}
},
DisableConditionsInCloudGames = {
condition = {
{
subConditionList = {
{
value = {
7252190
}
}
}
}
}
}
},
[11404] = {
GuideID = 11404,
Comment = "新手模式选择界面-引导更多玩法",
Priority = 99,
StartConditions = "{c_guideId= {guideId =10210}}",
TriggerEventParam = "UI_GameplaySelection_View",
FinishEvent = v1,
InterruptConditions = v2
},
[11410] = {
GuideID = 11410,
Comment = "引导点击主玩法/狼人匹配按钮",
Priority = 99,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_Preparations_View",
FinishEvent = v1,
CompleteConditions = "{c_grade = {grade = 5}}",
InterruptConditions = v2
},
[11411] = {
GuideID = 11411,
Comment = "引导点击大王匹配按钮",
Priority = 99,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_UniversalPreparation_Template1",
FinishEvent = v1,
CompleteConditions = "{c_grade = {grade = 5}}",
InterruptConditions = v2
},
[11412] = {
GuideID = 11412,
Comment = "引导点moba匹配按钮",
Priority = 99,
StartConditions = "{c_grade = {grade = 1}}",
TriggerEventParam = "UI_Arena_Preparations_Main",
FinishEvent = v1,
CompleteConditions = "{c_grade = {grade = 5}}",
InterruptConditions = v2
},
[11501] = {
GuideID = 11501,
Comment = "新玩家首先进入广场的AB实验（实验A引导）小红狐开场白",
Priority = 102,
StartConditions = "{c_grade = {grade = 1},c_abTest = {testId ={7255950}}",
FinishEvent = v1,
InterruptConditions = "{c_state={state=\"E_STATE_IN_TEAM_OR_TEAM_MATCH\"}",
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
}
},
[11502] = {
GuideID = 11502,
Comment = "新模式选择-引导点击更多玩法（实验A引导）",
Priority = 101,
StartConditions = "{c_grade = {grade = 1},c_abTest = {testId ={7255950}}",
FinishEvent = v1,
CompleteConditions = "{c_grade = {grade = 5}, c_isStartAnyMatchFromCloudEnv={}, c_conditionMode={mode=\"ANY\"} }",
InterruptConditions = v2,
OtherTriggerEventParam = "UILobbyView;UI_TeamShow_LobbyView",
InterruptCondition = v4,
StartCondition = {
condition = {
{
subConditionList = {
{
type = 6,
value = {
1
}
}
}
}
}
}
}
}

local mt = {
Priority = 1,
TriggerEventType = 2,
TriggerEventParam = "UILobbyView",
CanBeJump = false,
bEnabledInCloudGame = false,
bDisabledInPC = false,
bEnabledInVA = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data