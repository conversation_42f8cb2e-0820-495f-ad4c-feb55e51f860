--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 头饰

local v0 = {
{
itemId = 6,
itemNum = 50
}
}

local v1 = 3

local v2 = {
fashionValue = 60
}

local v3 = 230

local v4 = {
seconds = 4074768000
}

local v5 = {
-10,
35
}

local data = {
[630501] = {
id = 630501,
effect = true,
name = "福娃小虎",
desc = "内芯柔软，福气满满~",
icon = "CDN:Icon_Halo_267",
resourceConf = {
model = "SM_Halo_267",
modelType = 1
},
shareTexts = {
"我可不是纸糊的！"
},
beginTime = v4,
suitId = 70283,
suitName = "福娃小虎",
suitIcon = "CDN:Icon_Halo_267",
shareOffset = {
-10,
30
}
},
[630502] = {
id = 630502,
effect = true,
name = "福娃小虎",
desc = "内芯柔软，福气满满~",
icon = "CDN:Icon_Halo_267_01",
outlookConf = {
belongTo = 630501,
fashionValue = 25,
belongToGroup = {
630502,
630503
}
},
resourceConf = {
model = "SM_Halo_267",
material = "MI_Halo_267_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61516,
shareTexts = {
"我可不是纸糊的！"
},
shareOffset = {
-10,
30
}
},
[630503] = {
id = 630503,
effect = true,
name = "福娃小虎",
desc = "内芯柔软，福气满满~",
icon = "CDN:Icon_Halo_267_02",
outlookConf = {
belongTo = 630501,
fashionValue = 25,
belongToGroup = {
630502,
630503
}
},
resourceConf = {
model = "SM_Halo_267",
material = "MI_Halo_267_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61517,
shareTexts = {
"我可不是纸糊的！"
},
shareOffset = {
-10,
30
}
},
[630504] = {
id = 630504,
effect = true,
name = "笑口常开",
desc = "笑一个，会有好事发生",
icon = "CDN:Icon_Halo_278",
resourceConf = {
model = "SM_Halo_278",
modelType = 1
},
shareTexts = {
"笑口常开，好运一定来"
},
beginTime = v4,
suitId = 70284,
suitName = "笑口常开",
suitIcon = "CDN:Icon_Halo_278",
shareOffset = {
-7,
25
}
},
[630505] = {
id = 630505,
effect = true,
name = "笑口常开",
desc = "笑一个，会有好事发生",
icon = "CDN:Icon_Halo_278_01",
outlookConf = {
belongTo = 630504,
fashionValue = 25,
belongToGroup = {
630505,
630506
}
},
resourceConf = {
model = "SM_Halo_278",
material = "MI_Halo_278_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61519,
shareTexts = {
"笑口常开，好运一定来"
},
shareOffset = {
-7,
25
}
},
[630506] = {
id = 630506,
effect = true,
name = "笑口常开",
desc = "笑一个，会有好事发生",
icon = "CDN:Icon_Halo_278_02",
outlookConf = {
belongTo = 630504,
fashionValue = 25,
belongToGroup = {
630505,
630506
}
},
resourceConf = {
model = "SM_Halo_278",
material = "MI_Halo_278_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61520,
shareTexts = {
"笑口常开，好运一定来"
},
shareOffset = {
-7,
25
}
},
[630507] = {
id = 630507,
effect = true,
name = "小笼喵",
desc = "猜猜我是什么馅？",
icon = "CDN:Icon_Halo_290",
resourceConf = {
model = "SM_Halo_290",
modelType = 1
},
shareTexts = {
"小猫甄选，一口发发发"
},
beginTime = v4,
suitId = 70285,
suitName = "小笼喵",
suitIcon = "CDN:Icon_Halo_290",
shareOffset = {
-10,
32
}
},
[630508] = {
id = 630508,
effect = true,
name = "小笼喵",
desc = "猜猜我是什么馅？",
icon = "CDN:Icon_Halo_290_01",
outlookConf = {
belongTo = 630507,
fashionValue = 25,
belongToGroup = {
630508,
630509
}
},
resourceConf = {
model = "SM_Halo_290",
material = "MI_Halo_290_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61522,
shareTexts = {
"小猫甄选，一口发发发"
},
shareOffset = {
-10,
32
}
},
[630509] = {
id = 630509,
effect = true,
name = "小笼喵",
desc = "猜猜我是什么馅？",
icon = "CDN:Icon_Halo_290_02",
outlookConf = {
belongTo = 630507,
fashionValue = 25,
belongToGroup = {
630508,
630509
}
},
resourceConf = {
model = "SM_Halo_290",
material = "MI_Halo_290_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61523,
shareTexts = {
"小猫甄选，一口发发发"
},
shareOffset = {
-10,
32
}
},
[630510] = {
id = 630510,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "唤醒清晨",
desc = "到底是谁发明的闹钟！",
icon = "CDN:Icon_Halo_281",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_281",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70286,
suitName = "唤醒清晨",
suitIcon = "CDN:Icon_Halo_281"
},
[630511] = {
id = 630511,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "忧郁清晨",
desc = "一点蓝调，唤醒慵懒的一天",
icon = "CDN:Icon_Halo_282",
getWay = "印章祈愿",
jumpId = {
705
},
outlookConf = v2,
resourceConf = {
model = "SM_Halo_282",
modelType = 1
},
scaleTimes = 230,
beginTime = {
seconds = 1734019200
},
suitId = 70287,
suitName = "忧郁清晨",
suitIcon = "CDN:Icon_Halo_282"
},
[630512] = {
id = 630512,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "团圆瓷勺",
desc = "一颗汤圆，乘起一家团圆",
icon = "CDN:Icon_Halo_275",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_275",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70288,
suitName = "团圆瓷勺",
suitIcon = "CDN:Icon_Halo_275"
},
[630513] = {
id = 630513,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "骨感美鱼",
desc = "我这样，是不是有点太露骨了？",
icon = "CDN:Icon_Halo_271",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_271",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70289,
suitName = "骨感美鱼",
suitIcon = "CDN:Icon_Halo_271"
},
[630514] = {
id = 630514,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "趴趴猪",
desc = "可能引起极度舒适，让你忍不住摸一摸",
icon = "CDN:Icon_Halo_317",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_317",
modelType = 2,
idleAnim = "AS_Halo_317_idle_001"
},
shareTexts = {
"趴在哪里都是甜入心底"
},
beginTime = v4,
suitId = 70290,
suitName = "趴趴猪",
suitIcon = "CDN:Icon_Halo_317",
shareOffset = {
-15,
-8
},
shareRotateYaw = 120
},
[630515] = {
id = 630515,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "趴趴猪",
desc = "可能引起极度舒适，让你忍不住摸一摸",
icon = "CDN:Icon_Halo_317_01",
outlookConf = {
belongTo = 630514,
fashionValue = 35,
belongToGroup = {
630515,
630516
}
},
resourceConf = {
model = "SK_Halo_317",
material = "MI_Halo_317_HP01",
modelType = 2,
idleAnim = "AS_Halo_317_idle_001_HP01",
materialSlot = "Halo"
},
commodityId = 61529,
shareTexts = {
"趴在哪里都是甜入心底"
},
shareOffset = {
-15,
-8
},
shareRotateYaw = 120
},
[630516] = {
id = 630516,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "趴趴猪",
desc = "可能引起极度舒适，让你忍不住摸一摸",
icon = "CDN:Icon_Halo_317_02",
outlookConf = {
belongTo = 630514,
fashionValue = 35,
belongToGroup = {
630515,
630516
}
},
resourceConf = {
model = "SK_Halo_317",
material = "MI_Halo_317_HP02",
modelType = 2,
idleAnim = "AS_Halo_317_idle_001_HP02",
materialSlot = "Halo"
},
commodityId = 61530,
shareTexts = {
"趴在哪里都是甜入心底"
},
shareOffset = {
-15,
-8
},
shareRotateYaw = 120
},
[630517] = {
id = 630517,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "靓发神器",
desc = "其实我是自来卷",
icon = "CDN:Icon_Halo_273",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_273",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70291,
suitName = "靓发神器",
suitIcon = "CDN:Icon_Halo_273"
},
[630518] = {
id = 630518,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "好笋",
desc = "为了来到地面，顶走一切障碍",
icon = "CDN:Icon_Halo_270",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_270",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70292,
suitName = "好笋",
suitIcon = "CDN:Icon_Halo_270"
},
[630519] = {
id = 630519,
effect = true,
name = "午后茶歇",
desc = "午后的悠闲时光，应有红茶作陪",
icon = "CDN:Icon_Halo_297",
resourceConf = {
model = "SM_Halo_297",
modelType = 1
},
shareTexts = {
"该午休了，放松一下吧"
},
beginTime = v4,
suitId = 70293,
suitName = "午后茶歇",
suitIcon = "CDN:Icon_Halo_297",
shareOffset = {
-10,
10
}
},
[630520] = {
id = 630520,
effect = true,
name = "午后茶歇",
desc = "午后的悠闲时光，应有红茶作陪",
icon = "CDN:Icon_Halo_297_01",
outlookConf = {
belongTo = 630519,
fashionValue = 25,
belongToGroup = {
630520,
630521
}
},
resourceConf = {
model = "SM_Halo_297",
material = "MI_Halo_297_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61534,
shareTexts = {
"该午休了，放松一下吧"
},
shareOffset = {
-10,
10
}
},
[630521] = {
id = 630521,
effect = true,
name = "午后茶歇",
desc = "午后的悠闲时光，应有红茶作陪",
icon = "CDN:Icon_Halo_297_02",
outlookConf = {
belongTo = 630519,
fashionValue = 25,
belongToGroup = {
630520,
630521
}
},
resourceConf = {
model = "SM_Halo_297",
material = "MI_Halo_297_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61535,
shareTexts = {
"该午休了，放松一下吧"
},
shareOffset = {
-10,
10
}
},
[630522] = {
id = 630522,
effect = true,
name = "永冻蔷薇",
desc = "去最寒冷的地方绽放",
icon = "CDN:Icon_Halo_335",
resourceConf = {
model = "SM_Halo_335",
modelType = 1,
IconLabelId = 102
},
scaleTimes = 120,
shareTexts = {
"刺伤你的，正是你所追求的"
},
beginTime = v4,
suitId = 70294,
suitName = "永冻蔷薇",
suitIcon = "CDN:Icon_Halo_335",
shareOffset = {
-5,
5
}
},
[630523] = {
id = 630523,
effect = true,
name = "梦魇之翼",
desc = "恶魔的角，化作梦魇的能量",
icon = "CDN:Icon_Halo_326",
resourceConf = {
model = "SM_Halo_326",
modelType = 1
},
shareTexts = {
"和我一起，让一切坠入梦境"
},
beginTime = v4,
suitId = 70295,
suitName = "梦魇之翼",
suitIcon = "CDN:Icon_Halo_326",
shareOffset = {
-10,
15
}
},
[630524] = {
id = 630524,
effect = true,
name = "梦魇之翼",
desc = "恶魔的角，化作梦魇的能量",
icon = "CDN:Icon_Halo_326_01",
outlookConf = {
belongTo = 630523,
fashionValue = 25,
belongToGroup = {
630524,
630525
}
},
resourceConf = {
model = "SM_Halo_326",
material = "MI_Halo_326_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61539,
shareTexts = {
"和我一起，让一切坠入梦境"
},
shareOffset = {
-10,
15
}
},
[630525] = {
id = 630525,
effect = true,
name = "梦魇之翼",
desc = "恶魔的角，化作梦魇的能量",
icon = "CDN:Icon_Halo_326_02",
outlookConf = {
belongTo = 630523,
fashionValue = 25,
belongToGroup = {
630524,
630525
}
},
resourceConf = {
model = "SM_Halo_326",
material = "MI_Halo_326_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61540,
shareTexts = {
"和我一起，让一切坠入梦境"
},
shareOffset = {
-10,
15
}
},
[630526] = {
id = 630526,
effect = true,
name = "星愿苹果",
desc = "许下的心愿，化作带着星星的苹果",
icon = "CDN:Icon_Halo_327",
resourceConf = {
model = "SM_Halo_327",
modelType = 1
},
shareTexts = {
"送你一个苹果，没有被咬过哦！"
},
beginTime = v4,
suitId = 70296,
suitName = "星愿苹果",
suitIcon = "CDN:Icon_Halo_327",
shareOffset = v5
},
[630527] = {
id = 630527,
effect = true,
name = "星愿苹果",
desc = "许下的心愿，化作带着星星的苹果",
icon = "CDN:Icon_Halo_327_01",
outlookConf = {
belongTo = 630526,
fashionValue = 25,
belongToGroup = {
630527,
630528
}
},
resourceConf = {
model = "SM_Halo_327",
material = "MI_Halo_327_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61542,
shareTexts = {
"送你一个苹果，没有被咬过哦！"
},
shareOffset = v5
},
[630528] = {
id = 630528,
effect = true,
name = "星愿苹果",
desc = "许下的心愿，化作带着星星的苹果",
icon = "CDN:Icon_Halo_327_02",
outlookConf = {
belongTo = 630526,
fashionValue = 25,
belongToGroup = {
630527,
630528
}
},
resourceConf = {
model = "SM_Halo_327",
material = "MI_Halo_327_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61543,
shareTexts = {
"送你一个苹果，没有被咬过哦！"
},
shareOffset = v5
},
[630529] = {
id = 630529,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "勤劳小蜂",
desc = "勤劳采花匠，穿梭花丛中~",
icon = "CDN:Icon_Halo_308",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_308",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70297,
suitName = "勤劳小蜂",
suitIcon = "CDN:Icon_Halo_308"
},
[630530] = {
id = 630530,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "小太阳",
desc = "做一颗太阳，温暖目之所及的一切",
icon = "CDN:Icon_Halo_318",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_318",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70298,
suitName = "小太阳",
suitIcon = "CDN:Icon_Halo_318"
},
[630531] = {
id = 630531,
effect = true,
name = "蔷薇之毒",
desc = "这份美丽的毒药，送与谁饮下？",
icon = "CDN:Icon_Halo_334",
resourceConf = {
model = "SM_Halo_334",
modelType = 1
},
shareTexts = {
"桀桀桀，喝掉我吧！"
},
beginTime = v4,
suitId = 70299,
suitName = "蔷薇之毒",
suitIcon = "CDN:Icon_Halo_334"
},
[630532] = {
id = 630532,
effect = true,
name = "蔷薇之毒",
desc = "这份美丽的毒药，送与谁饮下？",
icon = "CDN:Icon_Halo_334_01",
outlookConf = {
belongTo = 630531,
fashionValue = 25,
belongToGroup = {
630532,
630533
}
},
resourceConf = {
model = "SM_Halo_334",
material = "MI_Halo_334_1_HP01;MI_Halo_334_2_HP01",
modelType = 1,
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61547,
shareTexts = {
"桀桀桀，喝掉我吧！"
}
},
[630533] = {
id = 630533,
effect = true,
name = "蔷薇之毒",
desc = "这份美丽的毒药，送与谁饮下？",
icon = "CDN:Icon_Halo_334_02",
outlookConf = {
belongTo = 630531,
fashionValue = 25,
belongToGroup = {
630532,
630533
}
},
resourceConf = {
model = "SM_Halo_334",
material = "MI_Halo_334_1_HP02;MI_Halo_334_2_HP02",
modelType = 1,
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61548,
shareTexts = {
"桀桀桀，喝掉我吧！"
}
},
[630534] = {
id = 630534,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "幻耀神石",
desc = "洞悉一切，将命运掌握手中",
icon = "CDN:Icon_Halo_324",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_324",
modelType = 2,
idleAnim = "AS_Halo_324_idle_001"
},
shareTexts = {
"助我看透未来吧！"
},
beginTime = v4,
suitId = 70300,
suitName = "幻耀神石",
suitIcon = "CDN:Icon_Halo_324"
},
[630535] = {
id = 630535,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "幻耀神石",
desc = "洞悉一切，将命运掌握手中",
icon = "CDN:Icon_Halo_324_01",
outlookConf = {
belongTo = 630534,
fashionValue = 35,
belongToGroup = {
630535,
630536
}
},
resourceConf = {
model = "SK_Halo_324",
material = "MI_Halo_324_1_HP01;MI_Halo_324_2_HP01",
modelType = 2,
idleAnim = "AS_Halo_324_idle_001_HP01",
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61550,
shareTexts = {
"助我看透未来吧！"
}
},
[630536] = {
id = 630536,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "幻耀神石",
desc = "洞悉一切，将命运掌握手中",
icon = "CDN:Icon_Halo_324_02",
outlookConf = {
belongTo = 630534,
fashionValue = 35,
belongToGroup = {
630535,
630536
}
},
resourceConf = {
model = "SK_Halo_324",
material = "MI_Halo_324_1_HP02;MI_Halo_324_2_HP02",
modelType = 2,
idleAnim = "AS_Halo_324_idle_001_HP02",
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61551,
shareTexts = {
"助我看透未来吧！"
}
},
[630537] = {
id = 630537,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "趴趴水豚",
desc = "不急，不急，先趴一会儿再说",
icon = "CDN:Icon_Halo_288",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_288",
modelType = 1
},
scaleTimes = 230,
shareTexts = {
"水豚趴趴，烦恼光光"
},
beginTime = v4,
suitId = 70301,
suitName = "趴趴水豚",
suitIcon = "CDN:Icon_Halo_288"
},
[630538] = {
id = 630538,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "蝶梦尘铃",
desc = "寻寻觅觅，铃声在何处响起？",
icon = "CDN:Icon_Halo_331",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_331",
modelType = 2,
idleAnim = "AS_Halo_331_idle_001"
},
shareTexts = {
"铃声响起时，我会来找你"
},
beginTime = v4,
suitId = 70302,
suitName = "蝶梦尘铃",
suitIcon = "CDN:Icon_Halo_331",
shareOffset = {
-10,
30
},
shareScaleTimes = 120
},
[630539] = {
id = 630539,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "蝶梦尘铃",
desc = "寻寻觅觅，铃声在何处响起？",
icon = "CDN:Icon_Halo_331_01",
outlookConf = {
belongTo = 630538,
fashionValue = 35,
belongToGroup = {
630539,
630540
}
},
resourceConf = {
model = "SK_Halo_331",
material = "MI_Halo_331_1_HP01;MI_Halo_331_2_HP01",
modelType = 2,
idleAnim = "AS_Halo_331_idle_001_HP01",
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61554,
shareTexts = {
"铃声响起时，我会来找你"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 120
},
[630540] = {
id = 630540,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "蝶梦尘铃",
desc = "寻寻觅觅，铃声在何处响起？",
icon = "CDN:Icon_Halo_331_02",
outlookConf = {
belongTo = 630538,
fashionValue = 35,
belongToGroup = {
630539,
630540
}
},
resourceConf = {
model = "SK_Halo_331",
material = "MI_Halo_331_1_HP02;MI_Halo_331_2_HP02",
modelType = 2,
idleAnim = "AS_Halo_331_idle_001_HP02",
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61555,
shareTexts = {
"铃声响起时，我会来找你"
},
shareOffset = {
-10,
30
},
shareScaleTimes = 120
},
[630541] = {
id = 630541,
effect = true,
name = "万彩潮流",
desc = "时尚最前端，有它足矣~",
icon = "CDN:Icon_Halo_337",
resourceConf = {
model = "SM_Halo_337",
modelType = 1
},
scaleTimes = 80,
shareTexts = {
"追逐时尚，没你不行！"
},
beginTime = v4,
suitId = 70303,
suitName = "万彩潮流",
suitIcon = "CDN:Icon_Halo_337",
shareOffset = {
-10,
30
}
},
[630542] = {
id = 630542,
effect = true,
name = "万彩潮流",
desc = "时尚最前端，有它足矣~",
icon = "CDN:Icon_Halo_337_01",
outlookConf = {
belongTo = 630541,
fashionValue = 25,
belongToGroup = {
630542,
630543
}
},
resourceConf = {
model = "SM_Halo_337",
material = "MI_Halo_337_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61557,
scaleTimes = 80,
shareTexts = {
"追逐时尚，没你不行！"
},
shareOffset = {
-10,
30
}
},
[630543] = {
id = 630543,
effect = true,
name = "万彩潮流",
desc = "时尚最前端，有它足矣~",
icon = "CDN:Icon_Halo_337_02",
outlookConf = {
belongTo = 630541,
fashionValue = 25,
belongToGroup = {
630542,
630543
}
},
resourceConf = {
model = "SM_Halo_337",
material = "MI_Halo_337_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61558,
scaleTimes = 80,
shareTexts = {
"追逐时尚，没你不行！"
},
shareOffset = {
-10,
30
}
},
[630544] = {
id = 630544,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "蔚蓝行星",
desc = "天空是蔚蓝色，大海也是蔚蓝色",
icon = "CDN:Icon_Halo_313",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_313",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70304,
suitName = "蔚蓝行星",
suitIcon = "CDN:Icon_Halo_313"
},
[630545] = {
id = 630545,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "赤土星球",
desc = "一星球的土，会有多重？",
icon = "CDN:Icon_Halo_314",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_314",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70305,
suitName = "赤土星球",
suitIcon = "CDN:Icon_Halo_314"
},
[630546] = {
id = 630546,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "梦幻星球",
desc = "梦中最美的星球，来到现实！",
icon = "CDN:Icon_Halo_315",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_315",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70306,
suitName = "梦幻星球",
suitIcon = "CDN:Icon_Halo_315"
},
[630547] = {
id = 630547,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "拦路小帽",
desc = "前方施工，此路不通！",
icon = "CDN:Icon_Halo_312",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_312",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70307,
suitName = "拦路小帽",
suitIcon = "CDN:Icon_Halo_312"
},
[630548] = {
id = 630548,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "无限加载中",
desc = "看到这个圈，你就得停下",
icon = "CDN:Icon_Halo_333",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_333",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70308,
suitName = "无限加载中",
suitIcon = "CDN:Icon_Halo_333"
},
[630549] = {
id = 630549,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "糯糯青团",
desc = "糯糯哒，但不弱弱哒！",
icon = "CDN:Icon_Halo_316",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_316",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70309,
suitName = "糯糯青团",
suitIcon = "CDN:Icon_Halo_316"
},
[630550] = {
id = 630550,
effect = true,
name = "晨曦小花",
desc = "晨曦中开放，带来一天好心情~",
icon = "CDN:Icon_Halo_309",
resourceConf = {
model = "SM_Halo_309",
modelType = 1
},
shareTexts = {
"牵住我的手，看花开花谢吧~"
},
beginTime = v4,
suitId = 70310,
suitName = "晨曦小花",
suitIcon = "CDN:Icon_Halo_309",
shareOffset = v5
},
[630551] = {
id = 630551,
effect = true,
name = "晨曦小花",
desc = "晨曦中开放，带来一天好心情~",
icon = "CDN:Icon_Halo_309_01",
outlookConf = {
belongTo = 630550,
fashionValue = 25,
belongToGroup = {
630551,
630552
}
},
resourceConf = {
model = "SM_Halo_309",
material = "MI_Halo_309_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61566,
shareTexts = {
"牵住我的手，看花开花谢吧~"
},
shareOffset = v5
},
[630552] = {
id = 630552,
effect = true,
name = "晨曦小花",
desc = "晨曦中开放，带来一天好心情~",
icon = "CDN:Icon_Halo_309_02",
outlookConf = {
belongTo = 630550,
fashionValue = 25,
belongToGroup = {
630551,
630552
}
},
resourceConf = {
model = "SM_Halo_309",
material = "MI_Halo_309_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61567,
shareTexts = {
"牵住我的手，看花开花谢吧~"
},
shareOffset = v5
},
[630553] = {
id = 630553,
effect = true,
name = "树莓软糖",
desc = "莓天元气满满，因为我的可爱是限定款",
icon = "CDN:Icon_Halo_286",
resourceConf = {
model = "SM_Halo_286",
modelType = 1
},
shareTexts = {
"甜甜蜜蜜，莓有烦恼"
},
beginTime = v4,
suitId = 70311,
suitName = "树莓软糖",
suitIcon = "CDN:Icon_Halo_286",
shareOffset = {
-10,
30
}
},
[630554] = {
id = 630554,
effect = true,
name = "树莓软糖",
desc = "莓天元气满满，因为我的可爱是限定款",
icon = "CDN:Icon_Halo_286_01",
outlookConf = {
belongTo = 630553,
fashionValue = 25,
belongToGroup = {
630554,
630555
}
},
resourceConf = {
model = "SM_Halo_286",
material = "MI_Halo_286_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61569,
shareTexts = {
"甜甜蜜蜜，莓有烦恼"
},
shareOffset = {
-10,
30
}
},
[630555] = {
id = 630555,
effect = true,
name = "树莓软糖",
desc = "莓天元气满满，因为我的可爱是限定款",
icon = "CDN:Icon_Halo_286_02",
outlookConf = {
belongTo = 630553,
fashionValue = 25,
belongToGroup = {
630554,
630555
}
},
resourceConf = {
model = "SM_Halo_286",
material = "MI_Halo_286_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61570,
shareTexts = {
"甜甜蜜蜜，莓有烦恼"
},
shareOffset = {
-10,
30
}
},
[630556] = {
id = 630556,
effect = true,
exceedReplaceItem = {
{
itemId = 3556,
itemNum = 5
}
},
name = "更衣香蕉鸭",
desc = "鸭鸭更衣中，非礼勿视！",
icon = "CDN:Icon_Halo_325",
resourceConf = {
model = "SM_Halo_325",
modelType = 1
},
shareTexts = {
"你怎么知道，鸭鸭喜欢香蕉！"
},
beginTime = v4,
suitId = 70312,
suitName = "更衣香蕉鸭",
suitIcon = "CDN:Icon_Halo_325",
shareOffset = v5
},
[630557] = {
id = 630557,
effect = true,
name = "更衣香蕉鸭",
desc = "鸭鸭更衣中，非礼勿视！",
icon = "CDN:Icon_Halo_325_01",
outlookConf = {
belongTo = 630556,
fashionValue = 25,
belongToGroup = {
630557,
630558
}
},
resourceConf = {
model = "SM_Halo_325",
material = "MI_Halo_325_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61572,
shareTexts = {
"你怎么知道，鸭鸭喜欢香蕉！"
},
shareOffset = v5
},
[630558] = {
id = 630558,
effect = true,
name = "更衣香蕉鸭",
desc = "鸭鸭更衣中，非礼勿视！",
icon = "CDN:Icon_Halo_325_02",
outlookConf = {
belongTo = 630556,
fashionValue = 25,
belongToGroup = {
630557,
630558
}
},
resourceConf = {
model = "SM_Halo_325",
material = "MI_Halo_325_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61573,
shareTexts = {
"你怎么知道，鸭鸭喜欢香蕉！"
},
shareOffset = v5
},
[630559] = {
id = 630559,
effect = true,
name = "斑斓蘑菇",
desc = "越鲜艳，越危险，非请勿靠近！",
icon = "CDN:Icon_Halo_332",
resourceConf = {
model = "SM_Halo_332",
modelType = 1
},
shareTexts = {
"鲜艳的色彩，只为吸引你"
},
beginTime = v4,
suitId = 70313,
suitName = "斑斓蘑菇",
suitIcon = "CDN:Icon_Halo_332",
shareOffset = v5
},
[630560] = {
id = 630560,
effect = true,
name = "斑斓蘑菇",
desc = "越鲜艳，越危险，非请勿靠近！",
icon = "CDN:Icon_Halo_332_01",
outlookConf = {
belongTo = 630559,
fashionValue = 25,
belongToGroup = {
630560,
630561
}
},
resourceConf = {
model = "SM_Halo_332",
material = "MI_Halo_332_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61575,
shareTexts = {
"鲜艳的色彩，只为吸引你"
},
shareOffset = v5
},
[630561] = {
id = 630561,
effect = true,
name = "斑斓蘑菇",
desc = "越鲜艳，越危险，非请勿靠近！",
icon = "CDN:Icon_Halo_332_02",
outlookConf = {
belongTo = 630559,
fashionValue = 25,
belongToGroup = {
630560,
630561
}
},
resourceConf = {
model = "SM_Halo_332",
material = "MI_Halo_332_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61576,
shareTexts = {
"鲜艳的色彩，只为吸引你"
},
shareOffset = v5
},
[630562] = {
id = 630562,
effect = true,
name = "樱花雪",
desc = "把春天的温柔握在手心，将甜甜的记忆珍藏",
icon = "CDN:Icon_Halo_287",
resourceConf = {
model = "SM_Halo_287",
modelType = 1
},
shareTexts = {
"樱花漫舞，甜蜜心间"
},
beginTime = v4,
suitId = 70314,
suitName = "樱花雪",
suitIcon = "CDN:Icon_Halo_287",
shareOffset = v5
},
[630563] = {
id = 630563,
effect = true,
name = "樱花雪",
desc = "把春天的温柔握在手心，将甜甜的记忆珍藏",
icon = "CDN:Icon_Halo_287_01",
outlookConf = {
belongTo = 630562,
fashionValue = 25,
belongToGroup = {
630563,
630564
}
},
resourceConf = {
model = "SM_Halo_287",
material = "MI_Halo_287_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61578,
shareTexts = {
"樱花漫舞，甜蜜心间"
},
shareOffset = v5
},
[630564] = {
id = 630564,
effect = true,
name = "樱花雪",
desc = "把春天的温柔握在手心，将甜甜的记忆珍藏",
icon = "CDN:Icon_Halo_287_02",
outlookConf = {
belongTo = 630562,
fashionValue = 25,
belongToGroup = {
630563,
630564
}
},
resourceConf = {
model = "SM_Halo_287",
material = "MI_Halo_287_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61579,
shareTexts = {
"樱花漫舞，甜蜜心间"
},
shareOffset = v5
},
[630565] = {
id = 630565,
effect = true,
name = "鸡王之冠",
desc = "别低头，小鸡永不认输！",
icon = "CDN:Icon_Halo_336",
resourceConf = {
model = "SM_Halo_336",
modelType = 1,
IconLabelId = 102
},
shareTexts = {
"喜欢小鸡？天经地义！"
},
beginTime = v4,
suitId = 70315,
suitName = "鸡王之冠",
suitIcon = "CDN:Icon_Halo_336",
shareOffset = v5
},
[630566] = {
id = 630566,
effect = true,
name = "软酪精灵",
desc = "藏在美食里的精灵，悄然苏醒",
icon = "CDN:Icon_Halo_330",
resourceConf = {
model = "SM_Halo_330",
modelType = 1
},
shareTexts = {
"脾气很好，任你拿捏"
},
beginTime = v4,
suitId = 70316,
suitName = "软酪精灵",
suitIcon = "CDN:Icon_Halo_330",
shareOffset = v5
},
[630567] = {
id = 630567,
effect = true,
name = "软酪精灵",
desc = "藏在美食里的精灵，悄然苏醒",
icon = "CDN:Icon_Halo_330_01",
outlookConf = {
belongTo = 630566,
fashionValue = 25,
belongToGroup = {
630567,
630568
}
},
resourceConf = {
model = "SM_Halo_330",
material = "MI_Halo_330_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61582,
shareTexts = {
"脾气很好，任你拿捏"
},
shareOffset = v5
},
[630568] = {
id = 630568,
effect = true,
name = "软酪精灵",
desc = "藏在美食里的精灵，悄然苏醒",
icon = "CDN:Icon_Halo_330_02",
outlookConf = {
belongTo = 630566,
fashionValue = 25,
belongToGroup = {
630567,
630568
}
},
resourceConf = {
model = "SM_Halo_330",
material = "MI_Halo_330_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61583,
shareTexts = {
"脾气很好，任你拿捏"
},
shareOffset = v5
},
[630569] = {
id = 630569,
effect = true,
name = "玲珑宝器",
desc = "镜花水月，终成虚影",
icon = "CDN:Icon_Halo_353",
resourceConf = {
model = "SM_Halo_353",
modelType = 1,
IconLabelId = 102
},
shareTexts = {
"长夜未尽，故人未归"
},
beginTime = v4,
suitId = 70317,
suitName = "玲珑宝器",
suitIcon = "CDN:Icon_Halo_353",
shareOffset = {
-10,
10
}
},
[630570] = {
id = 630570,
effect = true,
name = "咔咔大盗",
desc = "准备好了吗？一起开始新的冒险",
icon = "CDN:Icon_Halo_301",
resourceConf = {
model = "SM_Halo_301",
modelType = 1
},
shareTexts = {
"带走烦恼，留下笑容"
},
beginTime = v4,
suitId = 70318,
suitName = "咔咔大盗",
suitIcon = "CDN:Icon_Halo_301",
shareOffset = v5
},
[630571] = {
id = 630571,
effect = true,
name = "咔咔大盗",
desc = "准备好了吗？一起开始新的冒险",
icon = "CDN:Icon_Halo_301_01",
outlookConf = {
belongTo = 630570,
fashionValue = 25,
belongToGroup = {
630571,
630572
}
},
resourceConf = {
model = "SM_Halo_301",
material = "MI_Halo_301_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61586,
shareTexts = {
"带走烦恼，留下笑容"
},
shareOffset = v5
},
[630572] = {
id = 630572,
effect = true,
name = "咔咔大盗",
desc = "准备好了吗？一起开始新的冒险",
icon = "CDN:Icon_Halo_301_02",
outlookConf = {
belongTo = 630570,
fashionValue = 25,
belongToGroup = {
630571,
630572
}
},
resourceConf = {
model = "SM_Halo_301",
material = "MI_Halo_301_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61587,
shareTexts = {
"带走烦恼，留下笑容"
},
shareOffset = v5
},
[630573] = {
id = 630573,
effect = true,
name = "牛马发箍",
desc = "头顶的梦想，是挥洒汗水后的闪耀光芒",
icon = "CDN:Icon_Halo_292",
resourceConf = {
model = "SM_Halo_292",
modelType = 1
},
shareTexts = {
"好的好的，收到收到"
},
beginTime = v4,
suitId = 70319,
suitName = "牛马发箍",
suitIcon = "CDN:Icon_Halo_292",
shareOffset = {
-10,
30
}
},
[630574] = {
id = 630574,
effect = true,
name = "牛马发箍",
desc = "头顶的梦想，是挥洒汗水后的闪耀光芒",
icon = "CDN:Icon_Halo_292_01",
outlookConf = {
belongTo = 630573,
fashionValue = 25,
belongToGroup = {
630574,
630575
}
},
resourceConf = {
model = "SM_Halo_292",
material = "MI_Halo_292_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61589,
shareTexts = {
"好的好的，收到收到"
},
shareOffset = {
-10,
30
}
},
[630575] = {
id = 630575,
effect = true,
name = "牛马发箍",
desc = "头顶的梦想，是挥洒汗水后的闪耀光芒",
icon = "CDN:Icon_Halo_292_02",
outlookConf = {
belongTo = 630573,
fashionValue = 25,
belongToGroup = {
630574,
630575
}
},
resourceConf = {
model = "SM_Halo_292",
material = "MI_Halo_292_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61590,
shareTexts = {
"好的好的，收到收到"
},
shareOffset = {
-10,
30
}
},
[630576] = {
id = 630576,
effect = true,
name = "晴空朵朵",
desc = "赶走所有坏心情，只留下暖暖的晴天",
icon = "CDN:Icon_Halo_347",
resourceConf = {
model = "SM_Halo_347",
modelType = 1
},
shareTexts = {
"你的专属太阳"
},
beginTime = v4,
suitId = 70320,
suitName = "晴空朵朵",
suitIcon = "CDN:Icon_Halo_347",
shareOffset = {
-15,
35
}
},
[630577] = {
id = 630577,
effect = true,
name = "晴空朵朵",
desc = "赶走所有坏心情，只留下暖暖的晴天",
icon = "CDN:Icon_Halo_347_01",
outlookConf = {
belongTo = 630576,
fashionValue = 25,
belongToGroup = {
630577,
630578
}
},
resourceConf = {
model = "SM_Halo_347",
material = "MI_Halo_347_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61592,
shareTexts = {
"你的专属太阳"
},
shareOffset = {
-15,
35
}
},
[630578] = {
id = 630578,
effect = true,
name = "晴空朵朵",
desc = "赶走所有坏心情，只留下暖暖的晴天",
icon = "CDN:Icon_Halo_347_02",
outlookConf = {
belongTo = 630576,
fashionValue = 25,
belongToGroup = {
630577,
630578
}
},
resourceConf = {
model = "SM_Halo_347",
material = "MI_Halo_347_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61593,
shareTexts = {
"你的专属太阳"
},
shareOffset = {
-15,
35
}
},
[630579] = {
id = 630579,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "甜心小兔",
desc = "兔兔耳朵晃呀晃，捕捉每一个心动的瞬间",
icon = "CDN:Icon_Halo_350",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_350",
modelType = 1
},
scaleTimes = 230,
shareTexts = {
"萌力加倍，可爱无限"
},
beginTime = v4,
suitId = 70321,
suitName = "甜心小兔",
suitIcon = "CDN:Icon_Halo_350"
},
[630580] = {
id = 630580,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星冕之核",
desc = "改变一切的奇迹，来自星辰",
icon = "CDN:Icon_Halo_342",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Halo_342",
emitter = "FX_CH_Decorate_Halo_342_001",
modelType = 1
},
scaleTimes = 120,
shareTexts = {
"和我签订契约吧"
},
beginTime = v4,
suitId = 70322,
suitName = "星冕之核",
suitIcon = "CDN:Icon_Halo_342"
},
[630581] = {
id = 630581,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星冕之核",
desc = "改变一切的奇迹，来自星辰",
icon = "CDN:Icon_Halo_342_01",
outlookConf = {
belongTo = 630580,
fashionValue = 35,
belongToGroup = {
630581,
630582
}
},
resourceConf = {
model = "SM_Halo_342",
material = "MI_Halo_342_1_HP01;MI_Halo_342_2_HP01",
emitter = "FX_CH_Decorate_Halo_342_001_HP01",
modelType = 1,
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61596,
scaleTimes = 120,
shareTexts = {
"和我签订契约吧"
}
},
[630582] = {
id = 630582,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星冕之核",
desc = "改变一切的奇迹，来自星辰",
icon = "CDN:Icon_Halo_342_02",
outlookConf = {
belongTo = 630580,
fashionValue = 35,
belongToGroup = {
630581,
630582
}
},
resourceConf = {
model = "SM_Halo_342",
material = "MI_Halo_342_1_HP02;MI_Halo_342_2_HP02",
emitter = "FX_CH_Decorate_Halo_342_001_HP02",
modelType = 1,
materialSlot = "Halo_1;Halo_2"
},
commodityId = 61597,
scaleTimes = 120,
shareTexts = {
"和我签订契约吧"
}
},
[630583] = {
id = 630583,
effect = true,
name = "土豆芽芽乐",
desc = "悄悄发芽，惊艳全世界",
icon = "CDN:Icon_Halo_343",
resourceConf = {
model = "SM_Halo_343",
modelType = 1
},
shareTexts = {
"我发芽了，就不能吃啦！"
},
beginTime = v4,
suitId = 70323,
suitName = "土豆芽芽乐",
suitIcon = "CDN:Icon_Halo_343"
},
[630584] = {
id = 630584,
effect = true,
name = "土豆芽芽乐",
desc = "悄悄发芽，惊艳全世界",
icon = "CDN:Icon_Halo_343_01",
outlookConf = {
belongTo = 630583,
fashionValue = 25,
belongToGroup = {
630584,
630585
}
},
resourceConf = {
model = "SM_Halo_343",
material = "MI_Halo_343_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61599,
shareTexts = {
"我发芽了，就不能吃啦！"
}
},
[630585] = {
id = 630585,
effect = true,
name = "土豆芽芽乐",
desc = "悄悄发芽，惊艳全世界",
icon = "CDN:Icon_Halo_343_02",
outlookConf = {
belongTo = 630583,
fashionValue = 25,
belongToGroup = {
630584,
630585
}
},
resourceConf = {
model = "SM_Halo_343",
material = "MI_Halo_343_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61600,
shareTexts = {
"我发芽了，就不能吃啦！"
}
},
[630586] = {
id = 630586,
effect = true,
name = "毛绒恶魔",
desc = "桀桀桀，不听话的小朋友在哪里！",
icon = "CDN:Icon_Halo_357",
resourceConf = {
model = "SM_Halo_357",
modelType = 1
},
shareTexts = {
"我很凶？我装的"
},
beginTime = v4,
suitId = 70324,
suitName = "毛绒恶魔",
suitIcon = "CDN:Icon_Halo_357"
},
[630587] = {
id = 630587,
effect = true,
name = "毛绒恶魔",
desc = "桀桀桀，不听话的小朋友在哪里！",
icon = "CDN:Icon_Halo_357_01",
outlookConf = {
belongTo = 630586,
fashionValue = 25,
belongToGroup = {
630587,
630588
}
},
resourceConf = {
model = "SM_Halo_357",
material = "MI_Halo_357_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61602,
shareTexts = {
"我很凶？我装的"
}
},
[630588] = {
id = 630588,
effect = true,
name = "毛绒恶魔",
desc = "桀桀桀，不听话的小朋友在哪里！",
icon = "CDN:Icon_Halo_357_02",
outlookConf = {
belongTo = 630586,
fashionValue = 25,
belongToGroup = {
630587,
630588
}
},
resourceConf = {
model = "SM_Halo_357",
material = "MI_Halo_357_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61603,
shareTexts = {
"我很凶？我装的"
}
},
[630589] = {
id = 630589,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "甜心刨冰",
desc = "盛夏的第一口幸福，来自刨冰",
icon = "CDN:Icon_Halo_345",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_345",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70325,
suitName = "甜心刨冰",
suitIcon = "CDN:Icon_Halo_345"
},
[630590] = {
id = 630590,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "开花小绿瓜",
desc = "黄瓜头上一朵花，营造最美色差",
icon = "CDN:Icon_Halo_356",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_356",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70326,
suitName = "开花小绿瓜",
suitIcon = "CDN:Icon_Halo_356"
},
[630591] = {
id = 630591,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "音浪符号",
desc = "音乐的海洋里，一切都是快乐的符号",
icon = "CDN:Icon_Halo_354",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_354",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70327,
suitName = "音浪符号",
suitIcon = "CDN:Icon_Halo_354"
},
[630592] = {
id = 630592,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "逢考必胜",
desc = "天灵灵，地灵灵，逢考必过快显灵！",
icon = "CDN:Icon_Halo_344",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_344",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70328,
suitName = "逢考必胜",
suitIcon = "CDN:Icon_Halo_344"
},
[630593] = {
id = 630593,
effect = true,
name = "晚安云朵帽",
desc = "说完晚安，就该睡觉啦~",
icon = "CDN:Icon_Halo_341",
resourceConf = {
model = "SM_Halo_341",
modelType = 1,
IconLabelId = 102
},
shareTexts = {
"晚安的另一个意思，是我在想你"
},
beginTime = v4,
suitId = 70329,
suitName = "晚安云朵帽",
suitIcon = "CDN:Icon_Halo_341"
},
[630594] = {
id = 630594,
effect = true,
name = "礼盒灰灰",
desc = "打开礼盒，和灰灰来一个大大的拥抱吧",
icon = "CDN:Icon_Halo_321",
resourceConf = {
model = "SM_Halo_321",
modelType = 1,
IconLabelId = 102
},
shareTexts = {
"礼盒开，快乐来"
},
beginTime = v4,
suitId = 70330,
suitName = "礼盒灰灰",
suitIcon = "CDN:Icon_Halo_321"
},
[630595] = {
id = 630595,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "甜蜜奶茶",
desc = "甜蜜的不是奶茶，是和你度过的时光",
icon = "CDN:Icon_Halo_329",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_329",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70331,
suitName = "甜蜜奶茶",
suitIcon = "CDN:Icon_Halo_329",
shareOffset = {
-10,
30
}
},
[630596] = {
id = 630596,
effect = true,
name = "海风草帽",
desc = "赏花看海，记得防晒哦~",
icon = "CDN:Icon_Halo_365",
resourceConf = {
model = "SM_Halo_365",
modelType = 1
},
scaleTimes = 100,
shareTexts = {
"海风点点，与你相约"
},
beginTime = v4,
suitId = 70332,
suitName = "海风草帽",
suitIcon = "CDN:Icon_Halo_365",
shareOffset = {
-18,
18
},
shareScaleTimes = 100
},
[630597] = {
id = 630597,
effect = true,
name = "海风草帽",
desc = "赏花看海，记得防晒哦~",
icon = "CDN:Icon_Halo_365_01",
outlookConf = {
belongTo = 630596,
fashionValue = 25,
belongToGroup = {
630597,
630598
}
},
resourceConf = {
model = "SM_Halo_365",
material = "MI_Halo_365_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61614,
scaleTimes = 100,
shareTexts = {
"海风点点，与你相约"
},
shareOffset = {
-18,
18
},
shareScaleTimes = 100
},
[630598] = {
id = 630598,
effect = true,
name = "海风草帽",
desc = "赏花看海，记得防晒哦~",
icon = "CDN:Icon_Halo_365_02",
outlookConf = {
belongTo = 630596,
fashionValue = 25,
belongToGroup = {
630597,
630598
}
},
resourceConf = {
model = "SM_Halo_365",
material = "MI_Halo_365_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61615,
scaleTimes = 100,
shareTexts = {
"海风点点，与你相约"
},
shareOffset = {
-18,
18
},
shareScaleTimes = 100
},
[630599] = {
id = 630599,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星月王冠",
desc = "狼王之影，隐于夜月之下",
icon = "CDN:Icon_Halo_362",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_362",
modelType = 2,
idleAnim = "AS_Halo_362_Idle_001"
},
shareTexts = {
"直视群星者，可戴此冠"
},
beginTime = v4,
suitId = 70333,
suitName = "星月王冠",
suitIcon = "CDN:Icon_Halo_362",
shareOffset = {
-10,
0
}
},
[630600] = {
id = 630600,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "狼君金冠",
desc = "金冠之下，狼君百面",
icon = "CDN:Icon_Halo_363",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Halo_363",
emitter = "FX_P_Halo_363_401;FX_P_Halo_363_402",
modelType = 1,
idleAnim = "AS_Halo_363_Idle_001"
},
shareTexts = {
"隐藏起来，不要被找到……"
},
beginTime = v4,
suitId = 70334,
suitName = "狼君金冠",
suitIcon = "CDN:Icon_Halo_363",
shareOffset = {
-10,
0
}
}
}

local mt = {
effect = false,
type = "ItemType_HeadWear",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
scaleTimes = 180,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data