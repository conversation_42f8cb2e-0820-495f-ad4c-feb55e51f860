--com.tencent.wea.xlsRes.table_PlayerGuideData => excel/xls/X_新手引导表_chase.xlsx: 新引导总表

local v0 = "KeyStepCompleteEvent"

local data = {
[682001] = {
GuideID = 682001,
Comment = "星宝救被绑队友",
TriggerEventParam = "ON_MCG_StarRescueOnAltarTeammate",
EndEvent = v0
},
[682002] = {
GuideID = 682002,
Comment = "暗星能抓人",
TriggerEventParam = "ON_MCG_DarkStarCanCatch",
EndEvent = v0
},
[682003] = {
GuideID = 682003,
Comment = "暗星抓人放祭坛",
TriggerEventParam = "ON_MCG_DarkStarCaptureFinish",
EndEvent = v0
},
[682004] = {
GuideID = 682004,
Comment = "星宝和星路仪交互",
TriggerEventParam = "ON_MCG_StarInteractWithStarChart",
EndEvent = v0
},
[682005] = {
GuideID = 682005,
Comment = "星宝的拳头机关引导",
TriggerEventParam = "ON_MCG_StarActivateMechanism",
EndEvent = v0
},
[682006] = {
GuideID = 682006,
Comment = "星宝的翻窗引导",
TriggerEventParam = "ON_MCG_StarFlipWindow",
EndEvent = v0
},
[682007] = {
GuideID = 682007,
Comment = "暗宝的翻窗引导",
TriggerEventParam = "ON_MCG_DarkStarFlipWindow",
EndEvent = v0
},
[682008] = {
GuideID = 682008,
Comment = "暗星攻击星宝2次",
TriggerEventParam = "ON_MCG_DarkStarFirstStrike",
EndEvent = v0
},
[682009] = {
GuideID = 682009,
Comment = "暗星抓着星宝和祭坛交互",
TriggerEventParam = "ON_MCG_DarkStarCaptureStarAltarInteract",
EndEvent = v0
},
[682010] = {
GuideID = 682010,
Comment = "星宝开局目标",
TriggerEventParam = "ON_MCG_StartGuide_StarFaction",
EndEvent = v0
},
[682011] = {
GuideID = 682011,
Comment = "暗星开局目标",
TriggerEventParam = "ON_MCG_StartGuide_DarkStarFaction",
EndEvent = v0
},
[682012] = {
GuideID = 682012,
Comment = "星路仪QTE按钮操作",
TriggerEventParam = "ON_MCG_TryGuide_StarChartQTE",
EndEvent = v0
},
[682013] = {
GuideID = 682013,
Comment = "挣扎按钮",
TriggerEventParam = "ON_MCG_StartGuide_StarStruggle",
EndEvent = v0
},
[682014] = {
GuideID = 682014,
Comment = "暗星关闭星路仪",
Priority = 8,
TriggerEventParam = "ON_MCG_DarkStarShutDownStarChart",
EndEvent = v0
},
[682015] = {
GuideID = 682015,
Comment = "星宝踩关机点",
Priority = 20,
TriggerEventParam = "ON_MCG_StarInShutDownPoint",
EndEvent = v0
},
[682016] = {
GuideID = 682016,
Comment = "三王模式星宝开局目标-莲花洞",
TriggerEventParam = "ON_MCG_StartGuide_ThreeBossMode_Star",
EndEvent = v0
},
[682017] = {
GuideID = 682017,
Comment = "三王模式暗星开局目标-莲花洞",
TriggerEventParam = "ON_MCG_StartGuide_ThreeBossMode_DarkStar",
EndEvent = v0
},
[682018] = {
GuideID = 682018,
Comment = "星宝不可从未解锁阵营门进入",
TriggerEventParam = "ON_MCG_StarTouchLockedGate",
EndEvent = v0
},
[682019] = {
GuideID = 682019,
Comment = "阵营门解锁后星宝可救唐僧",
TriggerEventParam = "ON_MCG_StarGateUnlock",
EndEvent = v0
},
[682020] = {
GuideID = 682020,
Comment = "阵营门解锁后暗星提防星宝救唐僧",
TriggerEventParam = "ON_MCG_DarkStarGateUnlock",
EndEvent = v0
},
[682021] = {
GuideID = 682021,
Comment = "星宝进洞时提示唐僧",
TriggerEventParam = "ON_MCG_StarEnterGate",
EndEvent = v0
},
[682022] = {
GuideID = 682022,
Comment = "星宝变身唐僧",
TriggerEventParam = "ON_MCG_StarBecomeTang",
EndEvent = v0
},
[682023] = {
GuideID = 682023,
Comment = "星宝与金箍棒交互",
TriggerEventParam = "ON_MCG_StarInteractStick",
EndEvent = v0
},
[682024] = {
GuideID = 682024,
Comment = "星宝变身孙悟空",
Priority = 20,
TriggerEventParam = "ON_MCG_StarBecomeWukong",
EndEvent = v0
},
[682025] = {
GuideID = 682025,
Comment = "星宝与洞内法宝坛交互",
TriggerEventParam = "ON_MCG_StarInteractTreasure",
EndEvent = v0
},
[682026] = {
GuideID = 682026,
Comment = "星宝获得法宝",
Priority = 20,
TriggerEventParam = "ON_MCG_StarGetTreasure",
EndEvent = v0
},
[682027] = {
GuideID = 682027,
Comment = "金角吸了人到瓶子里",
Priority = 20,
TriggerEventParam = "ON_MCG_GoldBoyImprisonStar",
EndEvent = v0
},
[682028] = {
GuideID = 682028,
Comment = "银角吸了人到葫芦里",
Priority = 20,
TriggerEventParam = "ON_MCG_SilverBoyImprisonStar",
EndEvent = v0
},
[682029] = {
GuideID = 682029,
Comment = "星宝的哀伤值UI介绍",
TriggerEventParam = "ON_MCG_ShowSadProgress",
EndEvent = v0
},
[682030] = {
GuideID = 682030,
Comment = "星宝拿到芭蕉扇法宝",
TriggerEventParam = "ON_MCG_ThreeBossMode_Fire_IronFan",
EndEvent = v0
},
[682031] = {
GuideID = 682031,
Comment = "星宝靠近燃烧的火焰墙",
TriggerEventParam = "ON_MCG_ThreeBossMode_FireWall_Star",
EndEvent = v0
},
[682032] = {
GuideID = 682032,
Comment = "暗星靠近燃烧的火焰墙",
TriggerEventParam = "ON_MCG_ThreeBossMode_FireWall_DarkStar",
EndEvent = v0
},
[682033] = {
GuideID = 682033,
Comment = "三王模式星宝开局目标-火焰山",
TriggerEventParam = "ON_MCG_StartGuide_ThreeBossMode_Fire_Star",
EndEvent = v0
},
[682034] = {
GuideID = 682034,
Comment = "三王模式暗星开局目标-火焰山",
TriggerEventParam = "ON_MCG_StartGuide_ThreeBossMode_Fire_DarkStar",
EndEvent = v0
},
[682035] = {
GuideID = 682035,
Comment = "星宝不可从未解锁阵营门进入-火焰山",
TriggerEventParam = "ON_MCG_StarTouchLockedGate_Fire",
EndEvent = v0
},
[682036] = {
GuideID = 682036,
Comment = "阵营门解锁后星宝可救唐僧-火焰山",
TriggerEventParam = "ON_MCG_StarGateUnlock_Fire",
EndEvent = v0
},
[682037] = {
GuideID = 682037,
Comment = "选择飓风后，进入局内显示",
TriggerEventParam = "ON_MCG_BossShow",
EndEvent = v0
},
[682038] = {
GuideID = 682038,
Comment = "星宝进入飓风陷阱",
TriggerEventParam = "ON_MCG_GetIntoTrap",
EndEvent = v0
},
[682039] = {
GuideID = 682039,
Comment = "三王模式星宝开局目标-永夜城",
TriggerEventParam = "ON_MCG_StartGuide_ThreeBossMode_Nightcity_Star",
EndEvent = v0
},
[682040] = {
GuideID = 682040,
Comment = "三王模式暗星开局目标-永夜城",
TriggerEventParam = "ON_MCG_StartGuide_ThreeBossMode_Nightcity_DarkStar",
EndEvent = v0
},
[682041] = {
GuideID = 682041,
Comment = "星宝不可从未解锁阵营门进入",
TriggerEventParam = "ON_MCG_StarTouchLockedGate_Nightcity",
EndEvent = v0
},
[682042] = {
GuideID = 682042,
Comment = "阵营门解锁后星宝可救安琪儿",
TriggerEventParam = "ON_MCG_StarGateUnlock_Nightcity",
EndEvent = v0
},
[682043] = {
GuideID = 682043,
Comment = "阵营门解锁后暗星提防星宝救安琪儿",
TriggerEventParam = "ON_MCG_DarkStarGateUnlock_Nightcity",
EndEvent = v0
},
[682044] = {
GuideID = 682044,
Comment = "星宝进洞时提示救安琪儿",
TriggerEventParam = "ON_MCG_StarEnterGate_Nightcity",
EndEvent = v0
},
[682045] = {
GuideID = 682045,
Comment = "星宝变身安琪儿",
TriggerEventParam = "ON_MCG_StarBecomeAngel",
EndEvent = v0
},
[682046] = {
GuideID = 682046,
Comment = "星宝与变身狼人道具交互",
TriggerEventParam = "ON_MCG_StarInteractWolfclaw",
EndEvent = v0
},
[682047] = {
GuideID = 682047,
Comment = "星宝变身狼人",
Priority = 20,
TriggerEventParam = "ON_MCG_StarBecomeWerewolf",
EndEvent = v0
},
[682048] = {
GuideID = 682048,
Comment = "星宝与洞内法宝坛交互",
TriggerEventParam = "ON_MCG_StarInteractTreasure_Nightcity",
EndEvent = v0
},
[682049] = {
GuideID = 682049,
Comment = "星宝获得法宝",
Priority = 20,
TriggerEventParam = "ON_MCG_StarGetTreasure_Nightcity",
EndEvent = v0
},
[682050] = {
GuideID = 682050,
Comment = "拉弥娅处决星宝新手引导",
TriggerEventParam = "ON_MCG_DarkStarExecute",
EndEvent = v0
},
[682051] = {
GuideID = 682051,
Comment = "新手局-星宝局-备战引导",
TriggerEventParam = "ON_MCG_StarNewPlayBeiZhan",
FinishEvent = v0
}
}

local mt = {
Priority = 10,
TriggerEventType = 1,
CanBeJump = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data