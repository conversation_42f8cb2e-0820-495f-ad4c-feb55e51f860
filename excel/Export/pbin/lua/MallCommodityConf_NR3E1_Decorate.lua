--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_NR3E.xlsx: 躲猫猫装饰商店

local data = {
[389001] = {
commodityId = 389001,
commodityName = "伪装者特效：默认",
order = 1,
minVersion = "1.3.36.1",
itemIds = {
241201
}
},
[389002] = {
commodityId = 389002,
commodityName = "伪装者特效：繁花",
beginTime = {
seconds = 1732680000
},
endTime = {
seconds = 1746115199
},
order = 99,
jumpId = 457,
jumpText = "猫猫搜城记",
minVersion = "1.3.36.1",
itemIds = {
241202
}
},
[389003] = {
commodityId = 389003,
commodityName = "伪装者特效：烈焰",
beginTime = {
seconds = 1732680000
},
endTime = {
seconds = 1746115199
},
order = 98,
jumpId = 456,
jumpText = "猫猫搜城记",
minVersion = "1.3.36.1",
itemIds = {
241203
}
},
[389004] = {
commodityId = 389004,
commodityName = "伪装者特效：永恒誓言",
beginTime = {
seconds = 1732680000
},
endTime = {
seconds = 1746115199
},
order = 97,
jumpId = 456,
jumpText = "猫猫搜城记",
minVersion = "1.3.36.1",
itemIds = {
241204
}
},
[389005] = {
commodityId = 389005,
commodityName = "伪装者特效：红色",
beginTime = {
seconds = 1732680000
},
endTime = {
seconds = 1746115199
},
order = 96,
jumpId = 543,
jumpText = "猫猫搜城记",
minVersion = "1.3.68.1",
itemIds = {
241205
}
},
[389006] = {
commodityId = 389006,
commodityName = "伪装者特效：暴风雪",
beginTime = {
seconds = 1732680000
},
endTime = {
seconds = 1746115199
},
order = 95,
jumpId = 544,
jumpText = "猫猫搜城记",
minVersion = "1.3.68.1",
itemIds = {
241206
}
},
[389007] = {
commodityId = 389007,
commodityName = "伪装者特效：墨染身",
beginTime = {
seconds = 1741838400
},
endTime = {
seconds = 1755187199
},
order = 94,
jumpId = 583,
jumpText = "猫猫造物台",
minVersion = "1.3.78.1",
itemIds = {
241207
}
},
[389008] = {
commodityId = 389008,
commodityName = "伪装者特效：雨后霓虹",
beginTime = {
seconds = 1741838400
},
endTime = {
seconds = 1755187199
},
order = 93,
jumpId = 584,
jumpText = "猫猫造物台",
minVersion = "1.3.78.1",
itemIds = {
241208
}
},
[389009] = {
commodityId = 389009,
commodityName = "伪装者特效：赤芒晶核",
beginTime = {
seconds = 1745985600
},
endTime = {
seconds = 1755187199
},
order = 92,
jumpId = 670,
jumpText = "猫猫补习班",
minVersion = "1.3.88.1",
itemIds = {
241209
}
},
[389010] = {
commodityId = 389010,
commodityName = "伪装者特效：弹跳布丁",
beginTime = {
seconds = 1745985600
},
endTime = {
seconds = 1755187199
},
order = 91,
jumpId = 671,
jumpText = "猫猫补习班",
minVersion = "1.3.88.1",
itemIds = {
241210
}
},
[389011] = {
commodityId = 389011,
commodityName = "伪装者特效：星空",
beginTime = {
seconds = 1764043200
},
endTime = {
seconds = 4070966399
},
order = 90,
jumpId = 670,
jumpText = "猫猫补习班",
minVersion = "1.3.99.1",
itemIds = {
241211
}
},
[389012] = {
commodityId = 389012,
commodityName = "伪装者特效：动感灯光",
beginTime = {
seconds = 1764043200
},
endTime = {
seconds = 4070966399
},
order = 89,
jumpId = 671,
jumpText = "猫猫补习班",
minVersion = "1.3.99.1",
itemIds = {
241212
}
},
[389101] = {
commodityId = 389101,
commodityName = "默认武器",
order = 101,
minVersion = "1.3.36.1",
itemIds = {
241401
}
},
[389102] = {
commodityId = 389102,
commodityName = "雷霆之怒",
beginTime = {
seconds = 1736827200
},
endTime = {
seconds = 1746115199
},
order = 199,
jumpId = 456,
jumpText = "猫猫搜城记",
minVersion = "1.3.36.1",
itemIds = {
241402
}
},
[389103] = {
commodityId = 389103,
commodityName = "彩带飘飘",
beginTime = {
seconds = 1736827200
},
endTime = {
seconds = 1746115199
},
order = 198,
jumpId = 457,
jumpText = "猫猫搜城记",
minVersion = "1.3.36.1",
itemIds = {
241403
}
},
[389104] = {
commodityId = 389104,
commodityName = "雪球",
beginTime = {
seconds = 1736827200
},
endTime = {
seconds = 1746115199
},
order = 197,
jumpId = 544,
jumpText = "猫猫搜城记",
minVersion = "1.3.68.1",
itemIds = {
241404
}
},
[389105] = {
commodityId = 389105,
commodityName = "爆竹枪",
beginTime = {
seconds = 1736827200
},
endTime = {
seconds = 1746115199
},
order = 196,
jumpId = 543,
jumpText = "猫猫搜城记",
minVersion = "1.3.68.1",
itemIds = {
241405
}
},
[389106] = {
commodityId = 389106,
commodityName = "萌动甜点",
beginTime = {
seconds = 1741838400
},
endTime = {
seconds = 1755187199
},
order = 195,
jumpId = 583,
jumpText = "猫猫造物台",
minVersion = "1.3.78.1",
itemIds = {
241406
}
},
[389107] = {
commodityId = 389107,
commodityName = "量子魔方",
beginTime = {
seconds = 1741838400
},
endTime = {
seconds = 1755187199
},
order = 194,
jumpId = 584,
jumpText = "猫猫造物台",
minVersion = "1.3.78.1",
itemIds = {
241407
}
},
[389108] = {
commodityId = 389108,
commodityName = "嘟嘟号",
beginTime = {
seconds = 1745985600
},
endTime = {
seconds = 1755187199
},
order = 193,
jumpId = 671,
jumpText = "猫猫补习班",
minVersion = "1.3.88.1",
itemIds = {
241408
}
},
[389109] = {
commodityId = 389109,
commodityName = "繁星法杖",
beginTime = {
seconds = 1745985600
},
endTime = {
seconds = 1755187199
},
order = 192,
jumpId = 670,
jumpText = "猫猫补习班",
minVersion = "1.3.88.1",
itemIds = {
241409
}
},
[389110] = {
commodityId = 389110,
commodityName = "泡泡枪",
beginTime = {
seconds = 1764043200
},
endTime = {
seconds = 4070966399
},
order = 191,
jumpId = 671,
jumpText = "猫猫补习班",
minVersion = "1.3.99.1",
itemIds = {
241410
}
},
[389111] = {
commodityId = 389111,
commodityName = "爆裂篮球",
beginTime = {
seconds = 1764043200
},
endTime = {
seconds = 4070966399
},
order = 190,
jumpId = 670,
jumpText = "猫猫补习班",
minVersion = "1.3.99.1",
itemIds = {
241411
}
}
}

local mt = {
mallId = 150,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
shopTag = {
1005
},
gender = 0,
itemNums = {
1
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data