--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_策划专用.xlsx: UGC任务

local data = {
[5715] = {
id = 5715,
name = "积分达到40",
desc = "积分达到40",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 40,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
},
validPeriodList = {
0
}
}
},
[5716] = {
id = 5716,
name = "积分达到50",
desc = "积分达到50",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 50,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
510391
},
numList = {
1
},
validPeriodList = {
0
}
}
},
[5717] = {
id = 5717,
name = "积分达到70",
desc = "积分达到70",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 70,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
}
},
[5718] = {
id = 5718,
name = "积分达到80",
desc = "积分达到80",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 80,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
30
},
validPeriodList = {
0
}
}
},
[5719] = {
id = 5719,
name = "积分达到100",
desc = "积分达到100",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 100,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
620943
},
numList = {
1
},
validPeriodList = {
0
}
}
},
[5720] = {
id = 5720,
name = "积分达到120",
desc = "积分达到120",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 120,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
4000
},
validPeriodList = {
0
}
}
},
[5721] = {
id = 5721,
name = "积分达到130",
desc = "积分达到130",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 130,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
40
},
validPeriodList = {
0
}
}
},
[5722] = {
id = 5722,
name = "积分达到150",
desc = "积分达到150",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 150,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
411600
},
numList = {
1
},
validPeriodList = {
0
}
}
},
[5723] = {
id = 5723,
name = "积分达到185",
desc = "积分达到185",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 185,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
}
},
[5724] = {
id = 5724,
name = "积分达到200",
desc = "积分达到200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 200,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
722075
},
numList = {
1
},
validPeriodList = {
0
}
}
},
[5725] = {
id = 5725,
name = "积分达到220",
desc = "积分达到220",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 220,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
}
},
[5726] = {
id = 5726,
name = "积分达到240",
desc = "积分达到240",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 240,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
3000
},
validPeriodList = {
0
}
}
},
[5727] = {
id = 5727,
name = "积分达到260",
desc = "积分达到260",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 260,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
}
},
[5728] = {
id = 5728,
name = "积分达到280",
desc = "积分达到280",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 280,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
3000
},
validPeriodList = {
0
}
}
},
[5729] = {
id = 5729,
name = "积分达到300",
desc = "积分达到300",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 300,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
}
},
[5730] = {
id = 5730,
name = "积分达到320",
desc = "积分达到320",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 320,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
}
},
[5731] = {
id = 5731,
name = "积分达到340",
desc = "积分达到340",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 340,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
40
},
validPeriodList = {
0
}
}
},
[5732] = {
id = 5732,
name = "积分达到360",
desc = "积分达到360",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 360,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
10
},
validPeriodList = {
0
}
}
},
[5733] = {
id = 5733,
name = "积分达到380",
desc = "积分达到380",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 380,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
4000
},
validPeriodList = {
0
}
}
},
[5734] = {
id = 5734,
name = "积分达到400",
desc = "积分达到400",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 20,
value = 400,
subConditionList = {
{
type = 3,
value = {
104
}
}
}
}
}
}
},
reward = {
itemIdList = {
200008
},
numList = {
1
},
validPeriodList = {
0
}
}
}
}

local mt = {
taskGroupId = 14013
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data