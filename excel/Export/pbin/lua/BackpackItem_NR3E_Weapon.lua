--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_NR3E.xlsx: 武器外观

local data = {
[241401] = {
id = 241401,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.36.1",
quality = 4,
name = "默认武器",
desc = "【躲猫猫】玩法的默认武器，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_000",
picture = "CDN:T_E3_PropsIcon_001",
getWay = "默认获得",
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_CH_WaterGun_001",
type = 1,
scale = 70,
offset = "25,-10,30",
offsetInGame = "25,0,48",
rotateYaw = -193
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_PA_PR_Bullet.BP_NR3E1_PA_PR_Bullet_C",
soundId = "2069"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51001"
},
pack3 = {
model = {
scale = 150
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunWater"
},
effect = {
path = "FX_NR3E1_Player_Shoot_430"
}
},
pack6 = {
effect = {
path = "FX_NR3E1_Player_bullet_421",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241402] = {
id = 241402,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.36.1",
quality = 1,
name = "雷霆之怒",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_001",
picture = "CDN:T_E1_Icon_SeekerWeapon_001",
getWay = "活动获得",
jumpId = {
456
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_ElectricGun_001",
type = 1,
scale = 70,
offset = "25,-10,30",
offsetInGame = "40,-5,38",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon1_Hit_ZRZ_402",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Electric.BP_NR3E1_Bullet_Electric_C",
soundId = "51031"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
classPath = "/Game/Feature/NR3E/E1/Blueprints/Gameplay/DeadBody/BP_NR3E1_DeadBody_Electric.BP_NR3E1_DeadBody_Electric_C",
soundId = "51032"
},
pack3 = {
model = {
path = "SK_NR3E_Effect_Hit_Electric_bones",
scale = 150
},
effect = {
path = "FX_P_NR3E1_Weapon1_Gun_ZRZ_401",
offset = "3,0,1"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunElectric"
},
effect = {
path = "FX_P_NR3E1_Weapon1_Attack_ZRZ_403",
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon1_Bullet_ZRZ_401",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241403] = {
id = 241403,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.36.1",
quality = 1,
name = "彩带飘飘",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_002",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
457
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_BallGun_001",
type = 1,
scale = 70,
offset = "25,-10,30",
offsetInGame = "40,-5,38",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon2_Hit_103",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Ribbons.BP_NR3E1_Bullet_Ribbons_C",
soundId = "51033"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51034"
},
pack3 = {
model = {
scale = 150
},
effect = {
path = "FX_P_NR3E1_Weapon2_Gun_ZJH_102",
offset = "-5,-20,10"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunRibbons"
},
effect = {
path = "FX_P_NR3E1_Weapon2_Attack_ZJH_104",
offset = "0,0,0"
}
},
pack5 = {
effect = {
path = "FX_P_NR3E1_Weapon2_Gun1_ZJH_103",
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon2_Bullet_ZJH_101",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241404] = {
id = 241404,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.68.1",
quality = 1,
name = "甜心雪灵",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_003",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
544
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_Snowball_001",
type = 1,
scale = 70,
offset = "25,-10,30",
offsetInGame = "40,-5,38",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon3_Hit_HGZ_701",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Snowball.BP_NR3E1_Bullet_Snowball_C",
soundId = "51044"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51045"
},
pack3 = {
model = {
scale = 150
},
effect = {
path = "FX_P_NR3E1_Weapon3_Attack_HGZ_704",
offset = "15,0,-35"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunSnowball"
},
effect = {
path = "FX_P_NR3E1_Weapon3_Attack1_HGZ_702",
offset = "0,0,0"
}
},
pack5 = {
effect = {
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon3_Bullet_HGZ_703",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241405] = {
id = 241405,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.68.1",
quality = 1,
name = "璀璨焰火",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_004",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
543
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1731859200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_Firework_001",
type = 1,
scale = 70,
offset = "25,-10,30",
offsetInGame = "40,-5,38",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon4_Hit_HGZ_702",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Firecrackers.BP_NR3E1_Bullet_Firecrackers_C",
soundId = "51046"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51047"
},
pack3 = {
model = {
scale = 150
},
effect = {
path = "FX_P_NR3E1_Weapon4_Attack_HGZ_705",
offset = "10,0,-10"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunFirecrackers"
},
effect = {
path = "FX_P_NR3E1_Weapon4_Attack_HGZ_701",
offset = "0,0,0"
}
},
pack5 = {
effect = {
offset = "0,0,0"
}
},
pack6 = {
model = {
path = "SM_NR3E_WP_Firework_002",
offset = "0,0,0"
},
effect = {
path = "FX_P_NR3E1_Weapon4_Bullet_HGZ_703",
offset = "250,-250,400",
duration = 0.5
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241406] = {
id = 241406,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.78.1",
quality = 1,
name = "萌动甜点",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_005",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
583
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_Icecream_001",
type = 1,
scale = 70,
offset = "25,-10,30",
offsetInGame = "40,-5,38",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon5_Hit_MJ_107;FX_P_NR3E1_Weapon5_Hit_MJ_108;FX_P_NR3E1_Weapon5_Hit_MJ_109",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Icecream1.BP_NR3E1_Bullet_Icecream1_C;/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Icecream2.BP_NR3E1_Bullet_Icecream2_C;/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Icecream3.BP_NR3E1_Bullet_Icecream3_C",
soundId = "51060"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51061"
},
pack3 = {
model = {
scale = 150
},
effect = {
offset = "15,0,-35"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunIcecream"
},
effect = {
path = "FX_P_NR3E1_Weapon5_Attack_MJ_101",
offset = "0,0,0"
}
},
pack5 = {
effect = {
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon5_Bullet_MJ_102;FX_P_NR3E1_Weapon5_Bullet_MJ_103;FX_P_NR3E1_Weapon5_Bullet_MJ_104",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241407] = {
id = 241407,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.78.1",
quality = 1,
name = "量子魔方",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_006",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
584
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_ScifiBox_001",
type = 1,
scale = 70,
offset = "80,-74,-82",
offsetInGame = "80,-74,-82",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon6_Hit_MJ_102",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_ScifiBoxShoot_Release_Body_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_ScifiBox.BP_NR3E1_Bullet_ScifiBox_C",
soundId = "51062"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "-15,74,80"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51063"
},
pack3 = {
model = {
scale = 150
},
effect = {
offset = "15,0,-35"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunScifiBox"
},
effect = {
path = "FX_P_NR3E1_Weapon6_Attack_MJ_103",
offset = "0,0,0"
}
},
pack5 = {
effect = {
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon6_Bullet_MJ_101",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241408] = {
id = 241408,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.88.1",
quality = 1,
name = "嘟嘟号",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_007",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
671
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1745985600
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_MusicGun_001",
type = 1,
scale = 70,
offset = "25,-10,30",
offsetInGame = "40,-5,38",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon7_Hit_MJ_102",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Music1.BP_NR3E1_Bullet_Music1_C;/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Music2.BP_NR3E1_Bullet_Music2_C;/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Music3.BP_NR3E1_Bullet_Music3_C",
soundId = "51070"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51071"
},
pack3 = {
model = {
scale = 150
},
effect = {
path = "FX_P_NR3E1_Weapon7_Gun_MJ_101",
offset = "0,0,0"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunMusic"
},
effect = {
path = "FX_P_NR3E1_Weapon7_Attack_MJ_103",
offset = "0,0,0"
}
},
pack5 = {
effect = {
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon7_Bullet_MJ_101;FX_P_NR3E1_Weapon7_Bullet_MJ_102;FX_P_NR3E1_Weapon7_Bullet_MJ_103",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241409] = {
id = 241409,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.88.1",
quality = 1,
name = "繁星法杖",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_008",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
670
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1745985600
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_MagicStaff_001",
type = 1,
scale = 100,
offset = "17,-10,12",
offsetInGame = "17,-10,6",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon8_Hit_MJ_105",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "As_NR3E1_CH_WandShoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_MagicStaff.BP_NR3E1_Bullet_MagicStaff_C",
soundId = "51072"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "4,-4,2"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51073"
},
pack3 = {
model = {
scale = 150
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunMagicStaff"
},
effect = {
path = "FX_P_NR3E1_Weapon8_Attack_MJ_101",
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon8_Bullet_MJ_102",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241410] = {
id = 241410,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.99.1",
quality = 1,
name = "咕呱泡泡",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_009",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
671
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1763395200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SM_NR3E_WP_BubbleGun_001",
type = 1,
scale = 100,
offset = "25,-10,30",
offsetInGame = "32,-14,36",
rotateYaw = -193
},
effect = {
path = "FX_P_NR3E1_Weapon9_Hit_MJ_103",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Shoot_Release_001",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_Hubble.BP_NR3E1_Bullet_Hubble_C",
soundId = "51089"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "0,0,0"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51090"
},
pack3 = {
model = {
scale = 150
},
effect = {
path = "FX_P_NR3E1_Weapon9_Gun_MJ_104",
offset = "0,0,0"
}
},
pack4 = {
model = {
path = "BP_NR3E1_GunBubble"
},
effect = {
path = "FX_P_NR3E1_Weapon9_Attack_MJ_101",
offset = "0,0,0"
}
},
pack5 = {
effect = {
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon9_Bullet_MJ_102",
offset = "250,-250,400"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
},
[241411] = {
id = 241411,
effect = true,
type = "ItemType_NR3E_Weapon",
stackedNum = 1,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
lowVer = "1.3.99.1",
quality = 1,
name = "爆裂篮球",
desc = "【躲猫猫】玩法的武器装扮，可在备战中更换。",
icon = "CDN:T_E1_Icon_SeekerWeapon_010",
picture = "CDN:T_E1_Icon_SeekerWeapon_002",
getWay = "活动获得",
jumpId = {
670
},
resourceConf = {
customLabelSlot = 1
},
bHideInBag = true,
rotateYaw = -225,
chunkGroupId = 20001,
newShelvesBeginTime = {
seconds = 1763395200
},
nr3eDecorate = {
pack1 = {
model = {
path = "SK_NR3E1_Basketball_LOD0",
type = 1,
scale = 100,
offset = "0,0,0",
offsetInGame = "0,0,0",
rotateYaw = 0
},
effect = {
path = "FX_P_NR3E1_Weapon10_Hit_103",
scale = 100,
offset = "0,0,30",
startTime = 1.2999999523162842,
duration = 2.4000000953674316
},
anim = "AS_NR3E1_CH_Basketball_3P_FireLoop",
classPath = "/Game/Feature/NR3E/E1/Assets/Placeables/Props/Pistol/BP/BP_NR3E1_Bullet_BasketballBox.BP_NR3E1_Bullet_BasketballBox_C",
soundId = "51086"
},
pack2 = {
model = {
path = "SM_NR3E_DE_Vase1_001",
scale = 100,
offsetInGame = "19,-34,14"
},
effect = {
path = "FX_NR3E1_Player_WaterBoom_431",
offset = "0,0,30"
},
anim = "AS_CH_Die_001",
soundId = "51087"
},
pack3 = {
model = {
scale = 150
}
},
pack4 = {
model = {
path = "BP_NR3E1_BurstBasketball"
},
effect = {
path = "FX_P_NR3E1_Weapon10_Attack_SWB_103",
offset = "0,0,0"
}
},
pack5 = {
effect = {
offset = "0,0,0"
}
},
pack6 = {
effect = {
path = "FX_P_NR3E1_Weapon10_Bullet_SWB_101",
offset = "150,-250,300"
}
}
},
mallviewWeaponTargetScale = 66,
mallviewWeaponBulletOffset = "100,-100,160"
}
}

local mt = {
effect = false,
bHideInBag = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data