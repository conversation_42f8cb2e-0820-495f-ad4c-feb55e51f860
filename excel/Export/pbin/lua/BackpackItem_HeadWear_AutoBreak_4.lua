--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 头饰

local v0 = {
{
itemId = 6,
itemNum = 50
}
}

local v1 = 3

local v2 = {
fashionValue = 125
}

local v3 = 230

local v4 = {
seconds = 4074768000
}

local data = {
[630401] = {
id = 630401,
effect = true,
name = "蒜你聪明",
desc = "家常的味道，总嫌不够",
icon = "CDN:Icon_Halo_237_02",
outlookConf = {
belongTo = 630399,
fashionValue = 25,
belongToGroup = {
630400,
630401
}
},
resourceConf = {
model = "SM_Halo_237",
material = "MI_Halo_237_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61411,
shareTexts = {
"“蒜”了，我们就这样吧"
}
},
[630402] = {
id = 630402,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "绒绒小耳",
desc = "耳朵摇摇，小猫警惕一切风吹草动",
icon = "CDN:Icon_Halo_239",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SM_Halo_239",
modelType = 1
},
scaleTimes = 230,
beginTime = {
seconds = 1734019200
},
suitId = 70228,
suitName = "绒绒小耳",
suitIcon = "CDN:Icon_Halo_239"
},
[630403] = {
id = 630403,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "蓝纹绒帽",
desc = "看起来很高冷，接近才会发现温暖的内在",
icon = "CDN:Icon_Halo_240",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SM_Halo_240",
modelType = 1
},
scaleTimes = 230,
beginTime = {
seconds = 1734019200
},
suitId = 70229,
suitName = "蓝纹绒帽",
suitIcon = "CDN:Icon_Halo_240"
},
[630404] = {
id = 630404,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "斑点暖帽",
desc = "是喜欢扮演斑马的奶牛小猫",
icon = "CDN:Icon_Halo_241",
getWay = "印章祈愿",
jumpId = {
705
},
resourceConf = {
model = "SM_Halo_241",
modelType = 1
},
scaleTimes = 230,
beginTime = {
seconds = 1734019200
},
suitId = 70230,
suitName = "斑点暖帽",
suitIcon = "CDN:Icon_Halo_241"
},
[630405] = {
id = 630405,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "小狗之友",
desc = "乖乖的小狗，奖励香香的骨头",
icon = "CDN:Icon_Halo_233",
resourceConf = {
model = "SM_Halo_233",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70231,
suitName = "小狗之友",
suitIcon = "CDN:Icon_Halo_233"
},
[630406] = {
id = 630406,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "划水",
desc = "并非不愿努力，只是保存实力",
icon = "CDN:Icon_Halo_257",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_257",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70232,
suitName = "划水",
suitIcon = "CDN:Icon_Halo_257"
},
[630407] = {
id = 630407,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "废话不多说",
desc = "废话浪费口舌，埋头专心干活",
icon = "CDN:Icon_Halo_243",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_243",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70233,
suitName = "废话不多说",
suitIcon = "CDN:Icon_Halo_243"
},
[630408] = {
id = 630408,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "可可圆舞曲",
desc = "让生活像巧克力一样，丝滑而又甜蜜",
icon = "CDN:Icon_Halo_256",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_256",
modelType = 2,
idleAnim = "AS_Halo_256_idle_001"
},
shareTexts = {
"香甜的味道，抬头可得"
},
beginTime = v4,
suitId = 70234,
suitName = "可可圆舞曲",
suitIcon = "CDN:Icon_Halo_256",
previewShareOffset = {
0,
-50
}
},
[630409] = {
id = 630409,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "可可圆舞曲",
desc = "让生活像巧克力一样，丝滑而又甜蜜",
icon = "CDN:Icon_Halo_256_01",
outlookConf = {
belongTo = 630408,
fashionValue = 35,
belongToGroup = {
630409,
630410
}
},
resourceConf = {
model = "SK_Halo_256",
material = "MI_Halo_256_HP01",
modelType = 2,
idleAnim = "AS_Halo_256_idle_001_HP01",
materialSlot = "Halo"
},
commodityId = 61419,
shareTexts = {
"香甜的味道，抬头可得"
},
previewShareOffset = {
0,
-50
}
},
[630410] = {
id = 630410,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "可可圆舞曲",
desc = "让生活像巧克力一样，丝滑而又甜蜜",
icon = "CDN:Icon_Halo_256_02",
outlookConf = {
belongTo = 630408,
fashionValue = 35,
belongToGroup = {
630409,
630410
}
},
resourceConf = {
model = "SK_Halo_256",
material = "MI_Halo_256_HP02",
modelType = 2,
idleAnim = "AS_Halo_256_idle_001_HP02",
materialSlot = "Halo"
},
commodityId = 61420,
shareTexts = {
"香甜的味道，抬头可得"
},
previewShareOffset = {
0,
-50
}
},
[630411] = {
id = 630411,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 20
}
},
quality = 1,
name = "飞鼠宝宝",
desc = "接好了，千万别让我摔跤啊！",
icon = "CDN:Icon_Halo_221",
getWay = "星之恋空",
jumpId = {
628
},
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_221",
modelType = 2,
idleAnim = "AS_Halo_221_idle_001"
},
shareTexts = {
"其实有点恐高的"
},
beginTime = {
seconds = 1731600000
},
suitId = 70235,
suitName = "飞鼠宝宝",
suitIcon = "CDN:Icon_Halo_221"
},
[630412] = {
id = 630412,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "飞鼠宝宝",
desc = "接好了，千万别让我摔跤啊！",
icon = "CDN:Icon_Halo_221_01",
outlookConf = {
belongTo = 630411,
fashionValue = 35,
belongToGroup = {
630412,
630413
}
},
resourceConf = {
model = "SK_Halo_221",
material = "MI_Halo_221_HP01",
modelType = 2,
idleAnim = "AS_Halo_221_idle_001_HP01",
materialSlot = "Halo"
},
commodityId = 61422,
shareTexts = {
"其实有点恐高的"
}
},
[630413] = {
id = 630413,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "飞鼠宝宝",
desc = "接好了，千万别让我摔跤啊！",
icon = "CDN:Icon_Halo_221_02",
outlookConf = {
belongTo = 630411,
fashionValue = 35,
belongToGroup = {
630412,
630413
}
},
resourceConf = {
model = "SK_Halo_221",
material = "MI_Halo_221_HP02",
modelType = 2,
idleAnim = "AS_Halo_221_idle_001_HP02",
materialSlot = "Halo"
},
commodityId = 61423,
shareTexts = {
"其实有点恐高的"
}
},
[630414] = {
id = 630414,
effect = true,
name = "树叶触角",
desc = "头顶叶片，留下森林的轻吻",
icon = "CDN:Icon_Halo_222",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_222",
modelType = 1
},
shareTexts = {
"小巧叶片，清新如晨"
},
beginTime = v4,
suitId = 70236,
suitName = "树叶触角",
suitIcon = "CDN:Icon_Halo_222"
},
[630415] = {
id = 630415,
effect = true,
name = "树叶触角",
desc = "头顶叶片，留下森林的轻吻",
icon = "CDN:Icon_Halo_222_01",
outlookConf = {
belongTo = 630414,
fashionValue = 25,
belongToGroup = {
630415,
630416
}
},
resourceConf = {
model = "SM_Halo_222",
material = "MI_Halo_222_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61425,
shareTexts = {
"小巧叶片，清新如晨"
}
},
[630416] = {
id = 630416,
effect = true,
name = "树叶触角",
desc = "头顶叶片，留下森林的轻吻",
icon = "CDN:Icon_Halo_222_02",
outlookConf = {
belongTo = 630414,
fashionValue = 25,
belongToGroup = {
630415,
630416
}
},
resourceConf = {
model = "SM_Halo_222",
material = "MI_Halo_222_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61426,
shareTexts = {
"小巧叶片，清新如晨"
}
},
[630417] = {
id = 630417,
effect = true,
name = "幸运小蛙",
desc = "戴上它，跳进童话世界",
icon = "CDN:Icon_Halo_223",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_223",
modelType = 1
},
shareTexts = {
"和青蛙一起，收集沿途快乐"
},
beginTime = v4,
suitId = 70237,
suitName = "幸运小蛙",
suitIcon = "CDN:Icon_Halo_223"
},
[630418] = {
id = 630418,
effect = true,
name = "幸运小蛙",
desc = "戴上它，跳进童话世界",
icon = "CDN:Icon_Halo_223_01",
outlookConf = {
belongTo = 630417,
fashionValue = 25,
belongToGroup = {
630418,
630419
}
},
resourceConf = {
model = "SM_Halo_223",
material = "MI_Halo_223_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61428,
shareTexts = {
"和青蛙一起，收集沿途快乐"
}
},
[630419] = {
id = 630419,
effect = true,
name = "幸运小蛙",
desc = "戴上它，跳进童话世界",
icon = "CDN:Icon_Halo_223_02",
outlookConf = {
belongTo = 630417,
fashionValue = 25,
belongToGroup = {
630418,
630419
}
},
resourceConf = {
model = "SM_Halo_223",
material = "MI_Halo_223_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61429,
shareTexts = {
"和青蛙一起，收集沿途快乐"
}
},
[630420] = {
id = 630420,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "给我点点",
desc = "操作这么妙，点赞少不了",
icon = "CDN:Icon_Halo_283",
resourceConf = {
model = "SM_Halo_283",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70238,
suitName = "给我点点",
suitIcon = "CDN:Icon_Halo_283"
},
[630421] = {
id = 630421,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "真香",
desc = "哎呀，这饭，真香！",
icon = "CDN:Icon_Halo_251",
getWay = "赛季通行证",
jumpId = {
9
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_251",
modelType = 1
},
scaleTimes = 230,
beginTime = {
seconds = 1732809600
},
suitId = 70239,
suitName = "真香",
suitIcon = "CDN:Icon_Halo_251"
},
[630422] = {
id = 630422,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "好感度系统",
desc = "救命，怎样才能“攻略”他？",
icon = "CDN:Icon_Halo_294",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_294",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70240,
suitName = "好感度系统",
suitIcon = "CDN:Icon_Halo_294"
},
[630423] = {
id = 630423,
effect = true,
name = "冰雪兔兔",
desc = "可以送我数不尽的胡萝卜吗？",
icon = "CDN:Icon_Halo_253",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_253",
modelType = 1
},
shareTexts = {
"兔兔的一辈子全部用来陪你"
},
beginTime = v4,
suitId = 70241,
suitName = "冰雪兔兔",
suitIcon = "CDN:Icon_Halo_253"
},
[630424] = {
id = 630424,
effect = true,
name = "冰雪兔兔",
desc = "可以送我数不尽的胡萝卜吗？",
icon = "CDN:Icon_Halo_253_01",
outlookConf = {
belongTo = 630423,
fashionValue = 25,
belongToGroup = {
630424,
630425
}
},
resourceConf = {
model = "SM_Halo_253",
material = "MI_Halo_253_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61434,
shareTexts = {
"兔兔的一辈子全部用来陪你"
}
},
[630425] = {
id = 630425,
effect = true,
name = "冰雪兔兔",
desc = "可以送我数不尽的胡萝卜吗？",
icon = "CDN:Icon_Halo_253_02",
outlookConf = {
belongTo = 630423,
fashionValue = 25,
belongToGroup = {
630424,
630425
}
},
resourceConf = {
model = "SM_Halo_253",
material = "MI_Halo_253_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61435,
shareTexts = {
"兔兔的一辈子全部用来陪你"
}
},
[630426] = {
id = 630426,
effect = true,
exceedReplaceItem = {
{
itemId = 224,
itemNum = 30
}
},
name = "甜筒冰冰",
desc = "让快乐和甜蜜伴随你的每一天",
icon = "CDN:Icon_Halo_293",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_293",
modelType = 1
},
shareTexts = {
"毛绒绒软绵绵，和你很搭"
},
beginTime = v4,
suitId = 70242,
suitName = "甜筒冰冰",
suitIcon = "CDN:Icon_Halo_293"
},
[630427] = {
id = 630427,
effect = true,
name = "甜筒冰冰",
desc = "让快乐和甜蜜伴随你的每一天",
icon = "CDN:Icon_Halo_293_01",
outlookConf = {
belongTo = 630426,
fashionValue = 25,
belongToGroup = {
630427,
630428
}
},
resourceConf = {
model = "SM_Halo_293",
material = "MI_Halo_293_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61437,
shareTexts = {
"毛绒绒软绵绵，和你很搭"
}
},
[630428] = {
id = 630428,
effect = true,
name = "甜筒冰冰",
desc = "让快乐和甜蜜伴随你的每一天",
icon = "CDN:Icon_Halo_293_02",
outlookConf = {
belongTo = 630426,
fashionValue = 25,
belongToGroup = {
630427,
630428
}
},
resourceConf = {
model = "SM_Halo_293",
material = "MI_Halo_293_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61438,
shareTexts = {
"毛绒绒软绵绵，和你很搭"
}
},
[630429] = {
id = 630429,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "兔子苹果",
desc = "这个小兔子苹果，是你给我削的吗",
icon = "CDN:Icon_Halo_295",
resourceConf = {
model = "SM_Halo_295",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70243,
suitName = "兔子苹果",
suitIcon = "CDN:Icon_Halo_295"
},
[630430] = {
id = 630430,
effect = true,
name = "名侦探出动",
desc = "今晚，要和我一起行动吗？",
icon = "CDN:Icon_Halo_274",
getWay = "限时活动",
jumpId = {
580
},
outlookConf = v2,
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_274",
modelType = 1
},
scaleTimes = 150,
shareTexts = {
"排除所有的不可能"
},
beginTime = {
seconds = 1740758400
},
suitId = 70244,
suitName = "名侦探出动",
suitIcon = "CDN:Icon_Halo_274"
},
[630431] = {
id = 630431,
effect = true,
name = "名侦探出动",
desc = "今晚，要和我一起行动吗？",
icon = "CDN:Icon_Halo_274_01",
getWay = "限时活动",
jumpId = {
580
},
outlookConf = {
belongTo = 630430,
fashionValue = 25,
belongToGroup = {
630431,
630432
}
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_274_HP01",
modelType = 1
},
commodityId = 61441,
scaleTimes = 150,
shareTexts = {
"排除所有的不可能"
}
},
[630432] = {
id = 630432,
effect = true,
name = "名侦探出动",
desc = "今晚，要和我一起行动吗？",
icon = "CDN:Icon_Halo_274_02",
getWay = "限时活动",
jumpId = {
580
},
outlookConf = {
belongTo = 630430,
fashionValue = 25,
belongToGroup = {
630431,
630432
}
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_274_HP02",
modelType = 1
},
commodityId = 61442,
scaleTimes = 150,
shareTexts = {
"排除所有的不可能"
}
},
[630433] = {
id = 630433,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "樱花班小黄帽",
desc = "戴上小黄帽，回到最初相遇的春天",
icon = "CDN:Icon_Halo_258",
resourceConf = {
model = "SM_Halo_258",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70245,
suitName = "樱花班小黄帽",
suitIcon = "CDN:Icon_Halo_258"
},
[630434] = {
id = 630434,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "大厨之帽",
desc = "小帽一戴，美味自来",
icon = "CDN:Icon_Halo_248",
resourceConf = {
model = "SM_Halo_248",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70246,
suitName = "大厨之帽",
suitIcon = "CDN:Icon_Halo_248"
},
[630435] = {
id = 630435,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "冲天辫辫",
desc = "谁还不会扎个小辫子了？",
icon = "CDN:Icon_Halo_296",
resourceConf = {
model = "SM_Halo_296",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70247,
suitName = "冲天辫辫",
suitIcon = "CDN:Icon_Halo_296"
},
[630436] = {
id = 630436,
effect = true,
name = "金锁吉祥",
desc = "吉祥小锁，助你心想事成！",
icon = "CDN:Icon_Halo_266",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_266",
modelType = 1
},
shareTexts = {
"珍惜这份称心如意"
},
beginTime = v4,
suitId = 70248,
suitName = "金锁吉祥",
suitIcon = "CDN:Icon_Halo_266"
},
[630437] = {
id = 630437,
effect = true,
name = "金锁吉祥",
desc = "吉祥小锁，助你心想事成！",
icon = "CDN:Icon_Halo_266_01",
outlookConf = {
belongTo = 630436,
fashionValue = 25,
belongToGroup = {
630437,
630438
}
},
resourceConf = {
model = "SM_Halo_266",
material = "MI_Halo_266_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61447,
shareTexts = {
"珍惜这份称心如意"
}
},
[630438] = {
id = 630438,
effect = true,
name = "金锁吉祥",
desc = "吉祥小锁，助你心想事成！",
icon = "CDN:Icon_Halo_266_02",
outlookConf = {
belongTo = 630436,
fashionValue = 25,
belongToGroup = {
630437,
630438
}
},
resourceConf = {
model = "SM_Halo_266",
material = "MI_Halo_266_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61448,
shareTexts = {
"珍惜这份称心如意"
}
},
[630439] = {
id = 630439,
effect = true,
name = "八方卦象盘",
desc = "虽会八卦，但不聊八卦",
icon = "CDN:Icon_Halo_265",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_265",
modelType = 1
},
shareTexts = {
"有空吗，和我八卦一下？"
},
beginTime = v4,
suitId = 70249,
suitName = "八方卦象盘",
suitIcon = "CDN:Icon_Halo_265"
},
[630440] = {
id = 630440,
effect = true,
name = "八方卦象盘",
desc = "虽会八卦，但不聊八卦",
icon = "CDN:Icon_Halo_265_01",
outlookConf = {
belongTo = 630439,
fashionValue = 25,
belongToGroup = {
630440,
630441
}
},
resourceConf = {
model = "SM_Halo_265",
material = "MI_Halo_265_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61450,
shareTexts = {
"有空吗，和我八卦一下？"
}
},
[630441] = {
id = 630441,
effect = true,
name = "八方卦象盘",
desc = "虽会八卦，但不聊八卦",
icon = "CDN:Icon_Halo_265_02",
outlookConf = {
belongTo = 630439,
fashionValue = 25,
belongToGroup = {
630440,
630441
}
},
resourceConf = {
model = "SM_Halo_265",
material = "MI_Halo_265_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61451,
shareTexts = {
"有空吗，和我八卦一下？"
}
},
[630442] = {
id = 630442,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "团圆麦香",
desc = "年夜饭的美好味道，永远不会忘",
icon = "CDN:Icon_Halo_260",
resourceConf = {
model = "SM_Halo_260",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70250,
suitName = "团圆米饭",
suitIcon = "CDN:Icon_Halo_260"
},
[630443] = {
id = 630443,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "主角遥控杆",
desc = "我的人生，由我自己操纵",
icon = "CDN:Icon_Halo_261",
resourceConf = {
model = "SM_Halo_261",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70251,
suitName = "大厨之帽",
suitIcon = "CDN:Icon_Halo_261"
},
[630444] = {
id = 630444,
effect = true,
name = "元梦星期五天团",
desc = "周年有你 五小只成团出道啦",
icon = "CDN:Icon_Halo_305",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_305",
modelType = 1
},
scaleTimes = 150,
shareTexts = {
"和五小只一起走花路吧~"
},
beginTime = v4,
suitId = 70252,
suitName = "元梦星期五天团",
suitIcon = "CDN:Icon_Halo_305",
shareOffset = {
0,
40
}
},
[630445] = {
id = 630445,
effect = true,
exceedReplaceItem = {
{
itemId = 224,
itemNum = 30
}
},
name = "小鸟之家",
desc = "在自然中找到栖息的小天地",
icon = "CDN:Icon_Halo_254",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_254",
modelType = 1
},
shareTexts = {
"见证生命的诞生与成长"
},
beginTime = v4,
suitId = 70253,
suitName = "小鸟之家",
suitIcon = "CDN:Icon_Halo_254"
},
[630446] = {
id = 630446,
effect = true,
name = "小鸟之家",
desc = "在自然中找到栖息的小天地",
icon = "CDN:Icon_Halo_254_01",
outlookConf = {
belongTo = 630445,
fashionValue = 25,
belongToGroup = {
630446,
630447
}
},
resourceConf = {
model = "SM_Halo_254",
material = "MI_Halo_254_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61456,
shareTexts = {
"见证生命的诞生与成长"
}
},
[630447] = {
id = 630447,
effect = true,
name = "小鸟之家",
desc = "在自然中找到栖息的小天地",
icon = "CDN:Icon_Halo_254_02",
outlookConf = {
belongTo = 630445,
fashionValue = 25,
belongToGroup = {
630446,
630447
}
},
resourceConf = {
model = "SM_Halo_254",
material = "MI_Halo_254_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61457,
shareTexts = {
"见证生命的诞生与成长"
}
},
[630448] = {
id = 630448,
effect = true,
name = "小鹿星",
desc = "冬夜里最闪耀的那颗星",
icon = "CDN:Icon_Halo_277",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_277",
modelType = 1
},
shareTexts = {
"夜空亮晶晶，祝福小鹿星"
},
beginTime = v4,
suitId = 70254,
suitName = "小鹿星",
suitIcon = "CDN:Icon_Halo_277",
shareOffset = {
-8,
24
}
},
[630449] = {
id = 630449,
effect = true,
name = "小鹿星",
desc = "冬夜里最闪耀的那颗星",
icon = "CDN:Icon_Halo_277_01",
outlookConf = {
belongTo = 630448,
fashionValue = 25,
belongToGroup = {
630449,
630450
}
},
resourceConf = {
model = "SM_Halo_277",
material = "MI_Halo_277_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61459,
shareTexts = {
"夜空亮晶晶，祝福小鹿星"
},
shareOffset = {
-8,
24
}
},
[630450] = {
id = 630450,
effect = true,
name = "小鹿星",
desc = "冬夜里最闪耀的那颗星",
icon = "CDN:Icon_Halo_277_02",
outlookConf = {
belongTo = 630448,
fashionValue = 25,
belongToGroup = {
630449,
630450
}
},
resourceConf = {
model = "SM_Halo_277",
material = "MI_Halo_277_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61460,
shareTexts = {
"夜空亮晶晶，祝福小鹿星"
},
shareOffset = {
-8,
24
}
},
[630451] = {
id = 630451,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "2025",
desc = "又是一年新年到，2025，前来报到！",
icon = "CDN:Icon_Halo_246",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_246",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70255,
suitName = "2025",
suitIcon = "CDN:Icon_Halo_246"
},
[630452] = {
id = 630452,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "天使之辉",
desc = "置身于云端，感受天使光辉的温暖",
icon = "CDN:Icon_Halo_272",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_272",
modelType = 2,
idleAnim = "AS_Halo_272_idle_001"
},
scaleTimes = 120,
shareTexts = {
"光辉闪耀，编织梦想"
},
beginTime = v4,
suitId = 70256,
suitName = "天使之辉",
suitIcon = "CDN:Icon_Halo_272"
},
[630453] = {
id = 630453,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "天使之辉",
desc = "置身于云端，感受天使光辉的温暖",
icon = "CDN:Icon_Halo_272_01",
outlookConf = {
belongTo = 630452,
fashionValue = 35,
belongToGroup = {
630453,
630454
}
},
resourceConf = {
model = "SK_Halo_272",
material = "MI_Halo_272_1_HP01;MI_Halo_272_2_HP01;MI_Halo_272_3_HP01",
modelType = 2,
idleAnim = "AS_Halo_272_idle_001_HP01",
materialSlot = "Halo_1;Halo_2;Halo_3"
},
commodityId = 61463,
scaleTimes = 120,
shareTexts = {
"光辉闪耀，编织梦想"
}
},
[630454] = {
id = 630454,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "天使之辉",
desc = "置身于云端，感受天使光辉的温暖",
icon = "CDN:Icon_Halo_272_02",
outlookConf = {
belongTo = 630452,
fashionValue = 35,
belongToGroup = {
630453,
630454
}
},
resourceConf = {
model = "SK_Halo_272",
material = "MI_Halo_272_1_HP02;MI_Halo_272_2_HP02;MI_Halo_272_3_HP02",
modelType = 2,
idleAnim = "AS_Halo_272_idle_001_HP02",
materialSlot = "Halo_1;Halo_2;Halo_3"
},
commodityId = 61464,
scaleTimes = 120,
shareTexts = {
"光辉闪耀，编织梦想"
}
},
[630455] = {
id = 630455,
effect = true,
name = "烟囱小熊",
desc = "嗨，能帮把手吗？我卡住了……",
icon = "CDN:Icon_Halo_291",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_291",
modelType = 1
},
shareTexts = {
"这不是胖，这是毛茸茸"
},
beginTime = v4,
suitId = 70257,
suitName = "烟囱小熊",
suitIcon = "CDN:Icon_Halo_291",
shareOffset = {
-6,
32
}
},
[630456] = {
id = 630456,
effect = true,
name = "烟囱小熊",
desc = "嗨，能帮把手吗？我卡住了……",
icon = "CDN:Icon_Halo_291_01",
outlookConf = {
belongTo = 630455,
fashionValue = 25,
belongToGroup = {
630456,
630457
}
},
resourceConf = {
model = "SM_Halo_291",
material = "MI_Halo_291_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61466,
shareTexts = {
"这不是胖，这是毛茸茸"
},
shareOffset = {
-6,
32
}
},
[630457] = {
id = 630457,
effect = true,
name = "烟囱小熊",
desc = "嗨，能帮把手吗？我卡住了……",
icon = "CDN:Icon_Halo_291_02",
outlookConf = {
belongTo = 630455,
fashionValue = 25,
belongToGroup = {
630456,
630457
}
},
resourceConf = {
model = "SM_Halo_291",
material = "MI_Halo_291_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61467,
shareTexts = {
"这不是胖，这是毛茸茸"
},
shareOffset = {
-6,
32
}
},
[630458] = {
id = 630458,
effect = true,
name = "猫盒",
desc = "猫猫探头，爱意不休",
icon = "CDN:Icon_Halo_259",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_259",
modelType = 1,
IconLabelId = 102
},
scaleTimes = 120,
shareTexts = {
"吸猫一口，烦恼溜走~"
},
beginTime = v4,
suitId = 70258,
suitName = "猫盒",
suitIcon = "CDN:Icon_Halo_259"
},
[630459] = {
id = 630459,
effect = true,
exceedReplaceItem = {
{
itemId = 227,
itemNum = 10
}
},
name = "香酥菠萝包",
desc = "给今天画上一个又甜又圆的句号吧！",
icon = "CDN:Icon_Halo_255",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_255",
modelType = 1
},
shareTexts = {
"我是你的菠萝包"
},
beginTime = v4,
suitId = 70259,
suitName = "香酥菠萝包",
suitIcon = "CDN:Icon_Halo_255"
},
[630460] = {
id = 630460,
effect = true,
name = "香酥菠萝包",
desc = "给今天画上一个又甜又圆的句号吧！",
icon = "CDN:Icon_Halo_255_01",
outlookConf = {
belongTo = 630459,
fashionValue = 25,
belongToGroup = {
630460,
630461
}
},
resourceConf = {
model = "SM_Halo_255",
material = "MI_Halo_255_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61470,
shareTexts = {
"我是你的菠萝包"
}
},
[630461] = {
id = 630461,
effect = true,
name = "香酥菠萝包",
desc = "给今天画上一个又甜又圆的句号吧！",
icon = "CDN:Icon_Halo_255_02",
outlookConf = {
belongTo = 630459,
fashionValue = 25,
belongToGroup = {
630460,
630461
}
},
resourceConf = {
model = "SM_Halo_255",
material = "MI_Halo_255_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61471,
shareTexts = {
"我是你的菠萝包"
}
},
[630462] = {
id = 630462,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "弹弹网球",
desc = "世界级网球冲击，准备，发射！",
icon = "CDN:Icon_Halo_249",
resourceConf = {
model = "SM_Halo_249",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70260,
suitName = "弹弹网球",
suitIcon = "CDN:Icon_Halo_249"
},
[630463] = {
id = 630463,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "爱心手势",
desc = "感情到了，搓手比心",
icon = "CDN:Icon_Halo_250",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_250",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70261,
suitName = "爱心手势",
suitIcon = "CDN:Icon_Halo_250"
},
[630464] = {
id = 630464,
effect = true,
name = "社恐小蛛",
desc = "我有八个爪子，你只有俩，我赢啦",
icon = "CDN:Icon_Halo_299",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_299",
modelType = 1
},
shareTexts = {
"有点爱心，吐丝给你织毛衣"
},
beginTime = v4,
suitId = 70262,
suitName = "社恐小蛛",
suitIcon = "CDN:Icon_Halo_299"
},
[630465] = {
id = 630465,
effect = true,
name = "社恐小蛛",
desc = "我有八个爪子，你只有俩，我赢啦",
icon = "CDN:Icon_Halo_299_01",
outlookConf = {
belongTo = 630464,
fashionValue = 25,
belongToGroup = {
630465,
630466
}
},
resourceConf = {
model = "SM_Halo_299",
material = "MI_Halo_299_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61475,
shareTexts = {
"有点爱心，吐丝给你织毛衣"
}
},
[630466] = {
id = 630466,
effect = true,
name = "社恐小蛛",
desc = "我有八个爪子，你只有俩，我赢啦",
icon = "CDN:Icon_Halo_299_02",
outlookConf = {
belongTo = 630464,
fashionValue = 25,
belongToGroup = {
630465,
630466
}
},
resourceConf = {
model = "SM_Halo_299",
material = "MI_Halo_299_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61476,
shareTexts = {
"有点爱心，吐丝给你织毛衣"
}
},
[630467] = {
id = 630467,
effect = true,
name = "很想你",
desc = "发送一万遍想念讯息",
icon = "CDN:Icon_Halo_264",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_264",
modelType = 1
},
scaleTimes = 150,
shareTexts = {
"命中注定我想你"
},
beginTime = v4,
suitId = 70263,
suitName = "很想你",
suitIcon = "CDN:Icon_Halo_264"
},
[630468] = {
id = 630468,
effect = true,
name = "很想你",
desc = "发送一万遍想念讯息",
icon = "CDN:Icon_Halo_264_01",
outlookConf = {
belongTo = 630467,
fashionValue = 25,
belongToGroup = {
630468,
630469
}
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_264_HP01",
modelType = 1
},
commodityId = 61478,
scaleTimes = 150,
shareTexts = {
"命中注定我想你"
}
},
[630469] = {
id = 630469,
effect = true,
name = "很想你",
desc = "发送一万遍想念讯息",
icon = "CDN:Icon_Halo_264_02",
outlookConf = {
belongTo = 630467,
fashionValue = 25,
belongToGroup = {
630468,
630469
}
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_264_HP02",
modelType = 1
},
commodityId = 61479,
scaleTimes = 150,
shareTexts = {
"命中注定我想你"
}
},
[630470] = {
id = 630470,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "买它！",
desc = "3、2、1，上链接！",
icon = "CDN:Icon_Halo_262",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_262",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70264,
suitName = "买它！",
suitIcon = "CDN:Icon_Halo_262"
},
[630471] = {
id = 630471,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "种草",
desc = "很好用，已经买了一箱啦！ ",
icon = "CDN:Icon_Halo_263",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_263",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70265,
suitName = "种草",
suitIcon = "CDN:Icon_Halo_263"
},
[630472] = {
id = 630472,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "年年有余",
desc = "桌上留下一条鱼，往后年年都有余！",
icon = "CDN:Icon_Halo_302",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_302",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70266,
suitName = "年年有余",
suitIcon = "CDN:Icon_Halo_302"
},
[630473] = {
id = 630473,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "金光火眼",
desc = "燃点很低，兴奋就会有火眼金睛",
icon = "CDN:Icon_Halo_303",
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_303",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70267,
suitName = "金光火眼",
suitIcon = "CDN:Icon_Halo_303"
},
[630474] = {
id = 630474,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "幸福天降",
desc = "剪纸剪不尽的，是我对你的祝福",
icon = "CDN:Icon_Halo_304",
getWay = "新春霸福节",
jumpId = {
132
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_304",
modelType = 1
},
scaleTimes = 230,
beginTime = {
seconds = 1737648000
},
suitId = 70268,
suitName = "幸福天降",
suitIcon = "CDN:Icon_Halo_304"
},
[630475] = {
id = 630475,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "过年好",
desc = "新春到，见面第一句，就说”过年好“",
icon = "CDN:Icon_Halo_269",
getWay = "新春天天领",
jumpId = {
12307
},
resourceConf = {
model = "SM_Halo_021",
emitter = "FX_CH_Decorate_Halo_269",
modelType = 1
},
scaleTimes = 230,
beginTime = {
seconds = 1737993600
},
suitId = 70269,
suitName = "过年好",
suitIcon = "CDN:Icon_Halo_269"
},
[630476] = {
id = 630476,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "热烈街篮",
desc = "准备好为我的扣篮欢呼了吗？",
icon = "CDN:Icon_Halo_310",
resourceConf = {
model = "SM_Halo_310",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70270,
suitName = "热烈街篮",
suitIcon = "CDN:Icon_Halo_310"
},
[630477] = {
id = 630477,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "比翼双栖",
desc = "在天愿作比翼鸟，在地愿为连理枝",
icon = "CDN:Icon_Halo_298",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Halo_298",
emitter = "FX_CH_Decorate_Halo_298_001",
modelType = 1
},
scaleTimes = 120,
shareTexts = {
"比翼齐飞，爱意绵绵"
},
beginTime = v4,
suitId = 70271,
suitName = "比翼双栖",
suitIcon = "CDN:Icon_Halo_298",
shareOffset = {
-10,
35
}
},
[630478] = {
id = 630478,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "比翼双栖",
desc = "在天愿作比翼鸟，在地愿为连理枝",
icon = "CDN:Icon_Halo_298_01",
outlookConf = {
belongTo = 630477,
fashionValue = 35,
belongToGroup = {
630478,
630479
}
},
resourceConf = {
model = "SM_Halo_298",
material = "MI_Halo_298_HP01",
emitter = "FX_CH_Decorate_Halo_298_001_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61488,
scaleTimes = 120,
shareTexts = {
"比翼齐飞，爱意绵绵"
},
shareOffset = {
-10,
35
}
},
[630479] = {
id = 630479,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "比翼双栖",
desc = "在天愿作比翼鸟，在地愿为连理枝",
icon = "CDN:Icon_Halo_298_02",
outlookConf = {
belongTo = 630477,
fashionValue = 35,
belongToGroup = {
630478,
630479
}
},
resourceConf = {
model = "SM_Halo_298",
material = "MI_Halo_298_HP02",
emitter = "FX_CH_Decorate_Halo_298_001_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61489,
scaleTimes = 120,
shareTexts = {
"比翼齐飞，爱意绵绵"
},
shareOffset = {
-10,
35
}
},
[630480] = {
id = 630480,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "举个栗子",
desc = "你不明白？没事，让我举个“栗”子",
icon = "CDN:Icon_Halo_247",
resourceConf = {
model = "SM_Halo_247",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70272,
suitName = "举个栗子",
suitIcon = "CDN:Icon_Halo_247"
},
[630481] = {
id = 630481,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "雪花飘飘",
desc = "太过呵护，反而会融化",
icon = "CDN:Icon_Halo_245",
resourceConf = {
model = "SM_Halo_245",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70273,
suitName = "雪花飘飘",
suitIcon = "CDN:Icon_Halo_245"
},
[630482] = {
id = 630482,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "蜜糖暖意",
desc = "热气腾腾的烤红薯，温暖一整个冬天",
icon = "CDN:Icon_Halo_244",
resourceConf = {
model = "SM_Halo_244",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70274,
suitName = "蜜糖暖意",
suitIcon = "CDN:Icon_Halo_244"
},
[630483] = {
id = 630483,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "播音喇叭",
desc = "让世界听见你的声音",
icon = "CDN:Icon_Halo_242",
resourceConf = {
model = "SM_Halo_242",
modelType = 1
},
scaleTimes = 230,
beginTime = v4,
suitId = 70275,
suitName = "播音喇叭",
suitIcon = "CDN:Icon_Halo_242"
},
[630484] = {
id = 630484,
effect = true,
exceedReplaceItem = {
{
itemId = 224,
itemNum = 180
}
},
quality = 1,
name = "彩虹独角",
desc = "将梦幻般的力量，凝聚在尖角之上",
icon = "CDN:Icon_Halo_307",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SM_Halo_307",
emitter = "FX_CH_Decorate_Halo_307_001",
modelType = 1
},
scaleTimes = 120,
shareTexts = {
"送来独角兽特有的祝福"
},
beginTime = v4,
suitId = 70276,
suitName = "彩虹独角",
suitIcon = "CDN:Icon_Halo_307",
shareOffset = {
-10,
35
}
},
[630485] = {
id = 630485,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "彩虹独角",
desc = "将梦幻般的力量，凝聚在尖角之上",
icon = "CDN:Icon_Halo_307_01",
outlookConf = {
belongTo = 630484,
fashionValue = 35,
belongToGroup = {
630485,
630486
}
},
resourceConf = {
model = "SM_Halo_307",
material = "MI_Halo_307_HP01",
emitter = "FX_CH_Decorate_Halo_307_001_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61495,
scaleTimes = 120,
shareTexts = {
"送来独角兽特有的祝福"
},
shareOffset = {
-10,
35
}
},
[630486] = {
id = 630486,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "彩虹独角",
desc = "将梦幻般的力量，凝聚在尖角之上",
icon = "CDN:Icon_Halo_307_02",
outlookConf = {
belongTo = 630484,
fashionValue = 35,
belongToGroup = {
630485,
630486
}
},
resourceConf = {
model = "SM_Halo_307",
material = "MI_Halo_307_HP02",
emitter = "FX_CH_Decorate_Halo_307_001_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61496,
scaleTimes = 120,
shareTexts = {
"送来独角兽特有的祝福"
},
shareOffset = {
-10,
35
}
},
[630487] = {
id = 630487,
effect = true,
name = "美乐蒂小蛋糕",
desc = "在舌尖奏响梦幻乐章",
icon = "CDN:Icon_Halo_285",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_285",
modelType = 1,
IconLabelId = 102
},
scaleTimes = 120,
shareTexts = {
"萌动味蕾，是幸福的滋味"
},
beginTime = v4,
suitId = 70277,
suitName = "美乐蒂小蛋糕",
suitIcon = "CDN:Icon_Halo_285",
shareOffset = {
-10,
35
}
},
[630488] = {
id = 630488,
effect = true,
name = "大耳狗礼帽",
desc = "帽子摇一摇，耳朵动一动",
icon = "CDN:Icon_Halo_300",
getWay = "三丽鸥家族",
jumpId = {
12215
},
outlookConf = v2,
resourceConf = {
model = "SM_Halo_300",
modelType = 1,
IconLabelId = 102
},
scaleTimes = 120,
shareTexts = {
"要可爱，也要有风度"
},
beginTime = {
seconds = 1737302400
},
suitId = 70278,
suitName = "大耳狗礼帽",
suitIcon = "CDN:Icon_Halo_300",
shareOffset = {
-10,
30
}
},
[630489] = {
id = 630489,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "灵狐赐福",
desc = "灵狐带来祥瑞，庇佑你平安顺遂",
icon = "CDN:Icon_Halo_306",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Halo_306",
modelType = 2,
idleAnim = "AS_Halo_306_idle_001"
},
shareTexts = {
"狐仙驾到，好运拥抱"
},
beginTime = v4,
suitId = 70279,
suitName = "灵狐赐福",
suitIcon = "CDN:Icon_Halo_306",
shareOffset = {
0,
-10
},
previewShareOffset = {
0,
-40
}
},
[630490] = {
id = 630490,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "灵狐赐福",
desc = "灵狐带来祥瑞，庇佑你平安顺遂",
icon = "CDN:Icon_Halo_306_01",
outlookConf = {
belongTo = 630489,
fashionValue = 35,
belongToGroup = {
630490,
630491
}
},
resourceConf = {
model = "SK_Halo_306",
material = "MI_Halo_306_HP01",
modelType = 2,
idleAnim = "AS_Halo_306_idle_001_HP01",
materialSlot = "Halo"
},
commodityId = 61504,
shareTexts = {
"狐仙驾到，好运拥抱"
},
shareOffset = {
0,
-10
},
previewShareOffset = {
0,
-40
}
},
[630491] = {
id = 630491,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "灵狐赐福",
desc = "灵狐带来祥瑞，庇佑你平安顺遂",
icon = "CDN:Icon_Halo_306_02",
outlookConf = {
belongTo = 630489,
fashionValue = 35,
belongToGroup = {
630490,
630491
}
},
resourceConf = {
model = "SK_Halo_306",
material = "MI_Halo_306_HP02",
modelType = 2,
idleAnim = "AS_Halo_306_idle_001_HP02",
materialSlot = "Halo"
},
commodityId = 61505,
shareTexts = {
"狐仙驾到，好运拥抱"
},
shareOffset = {
0,
-10
},
previewShareOffset = {
0,
-40
}
},
[630492] = {
id = 630492,
effect = true,
name = "花木迎春",
desc = "春木衔花枝，岁暖又一年",
icon = "CDN:Icon_Halo_279",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_279",
modelType = 1
},
shareTexts = {
"送君一枝花，共赏光与霞"
},
beginTime = v4,
suitId = 70280,
suitName = "花木迎春",
suitIcon = "CDN:Icon_Halo_279",
shareOffset = {
-10,
35
}
},
[630493] = {
id = 630493,
effect = true,
name = "花木迎春",
desc = "春木衔花枝，岁暖又一年",
icon = "CDN:Icon_Halo_279_01",
outlookConf = {
belongTo = 630492,
fashionValue = 25,
belongToGroup = {
630493,
630494
}
},
resourceConf = {
model = "SM_Halo_279",
material = "MI_Halo_279_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61507,
shareTexts = {
"送君一枝花，共赏光与霞"
},
shareOffset = {
-10,
35
}
},
[630494] = {
id = 630494,
effect = true,
name = "花木迎春",
desc = "春木衔花枝，岁暖又一年",
icon = "CDN:Icon_Halo_279_02",
outlookConf = {
belongTo = 630492,
fashionValue = 25,
belongToGroup = {
630493,
630494
}
},
resourceConf = {
model = "SM_Halo_279",
material = "MI_Halo_279_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61508,
shareTexts = {
"送君一枝花，共赏光与霞"
},
shareOffset = {
-10,
35
}
},
[630495] = {
id = 630495,
effect = true,
exceedReplaceItem = {
{
itemId = 226,
itemNum = 10
}
},
name = "海洋小兔",
desc = "来自海底的神秘精灵",
icon = "CDN:Icon_Halo_284",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_284",
modelType = 1
},
shareTexts = {
"不可以欺负兔兔哦"
},
beginTime = v4,
suitId = 70281,
suitName = "海洋小兔",
suitIcon = "CDN:Icon_Halo_284",
shareOffset = {
-10,
35
}
},
[630496] = {
id = 630496,
effect = true,
name = "海洋小兔",
desc = "来自海底的神秘精灵",
icon = "CDN:Icon_Halo_284_01",
outlookConf = {
belongTo = 630495,
fashionValue = 25,
belongToGroup = {
630496,
630497
}
},
resourceConf = {
model = "SM_Halo_284",
material = "MI_Halo_284_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61510,
shareTexts = {
"不可以欺负兔兔哦"
},
shareOffset = {
-10,
35
}
},
[630497] = {
id = 630497,
effect = true,
name = "海洋小兔",
desc = "来自海底的神秘精灵",
icon = "CDN:Icon_Halo_284_02",
outlookConf = {
belongTo = 630495,
fashionValue = 25,
belongToGroup = {
630496,
630497
}
},
resourceConf = {
model = "SM_Halo_284",
material = "MI_Halo_284_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61511,
shareTexts = {
"不可以欺负兔兔哦"
},
shareOffset = {
-10,
35
}
},
[630498] = {
id = 630498,
effect = true,
name = "热力应援",
desc = "缤纷应援，抒发最高热情！",
icon = "CDN:Icon_Halo_280",
outlookConf = v2,
resourceConf = {
model = "SM_Halo_280",
modelType = 1
},
shareTexts = {
"活力应援，送你动力无限~"
},
beginTime = {
seconds = 1740153600
},
suitId = 70282,
suitName = "热力应援",
suitIcon = "CDN:Icon_Halo_280",
shareOffset = {
-10,
30
}
},
[630499] = {
id = 630499,
effect = true,
name = "热力应援",
desc = "缤纷应援，抒发最高热情！",
icon = "CDN:Icon_Halo_280_01",
outlookConf = {
belongTo = 630498,
fashionValue = 25,
belongToGroup = {
630499,
630500
}
},
resourceConf = {
model = "SM_Halo_280",
material = "MI_Halo_280_HP01",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61513,
shareTexts = {
"活力应援，送你动力无限~"
},
shareOffset = {
-10,
30
}
},
[630500] = {
id = 630500,
effect = true,
name = "热力应援",
desc = "缤纷应援，抒发最高热情！",
icon = "CDN:Icon_Halo_280_02",
outlookConf = {
belongTo = 630498,
fashionValue = 25,
belongToGroup = {
630499,
630500
}
},
resourceConf = {
model = "SM_Halo_280",
material = "MI_Halo_280_HP02",
modelType = 1,
materialSlot = "Halo"
},
commodityId = 61514,
shareTexts = {
"活力应援，送你动力无限~"
},
shareOffset = {
-10,
30
}
}
}

local mt = {
effect = false,
type = "ItemType_HeadWear",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 60
},
scaleTimes = 180,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data