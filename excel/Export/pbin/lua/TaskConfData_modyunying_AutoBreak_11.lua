--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_特色玩法运营.xlsx: 活动任务

local v0 = {
itemIdList = {
4
},
numList = {
50
},
validPeriodList = {
0
}
}

local data = {
[651444] = {
id = 651444,
name = "第5天",
desc = "第5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 5,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
290023
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001490
},
[651445] = {
id = 651445,
name = "第6天",
desc = "第6天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 6,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
100
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001490
},
[651446] = {
id = 651446,
name = "第7天",
desc = "第7天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 7,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
240829
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001490
},
[651447] = {
id = 651447,
name = "第一天",
desc = "第一天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 823,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 310,
taskGroupId = 1001491
},
[651450] = {
id = 651450,
name = "分享谁是狼人福利资讯，体验身份试用礼",
desc = "分享谁是狼人福利资讯，体验身份试用礼",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10130
}
}
}
}
}
}
},
reward = {
itemIdList = {
331027,
3544
},
numList = {
5,
20
},
validPeriodList = {
0,
0
}
},
jumpId = 10130,
taskGroupId = 1001494
},
[651451] = {
id = 651451,
name = "5/1-5/5彩蛋局房间限时开启，去看看>>",
desc = "5/1-5/5彩蛋局房间限时开启，去看看>>",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
323
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 323,
taskGroupId = 1001494
},
[651452] = {
id = 651452,
name = "全新主题身份乱斗狼上线，去看看>>",
desc = "全新主题身份乱斗狼上线，去看看>>",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
10502
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 10502,
taskGroupId = 1001496
},
[651453] = {
id = 651453,
name = "【每日】完成1局谁是狼人",
desc = "【每日】完成1局谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001492
},
[651454] = {
id = 651454,
name = "【每周】完成1局谁是狼人双人模式",
desc = "【每周】完成1局谁是狼人双人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
109
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001493
},
[651455] = {
id = 651455,
name = "【每周】获取100专精点数",
desc = "【每周】获取100专精点数",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 220,
value = 100
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001493
},
[651456] = {
id = 651456,
name = "【累计】在谁是狼人对局里见证1次决斗",
desc = "【累计】在谁是狼人对局里见证1次决斗",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 53,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
},
{
type = 49,
value = {
10003
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001494
},
[651457] = {
id = 651457,
name = "【累计】游玩1次【传递炸弹】地图",
desc = "【累计】游玩1次【传递炸弹】地图",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 100,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425169071017
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 83,
taskGroupId = 1001494
},
[651458] = {
id = 651458,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
331027
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651459] = {
id = 651459,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
200102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651460] = {
id = 651460,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 50,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651461] = {
id = 651461,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 70,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
200102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651462] = {
id = 651462,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 80,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
711530
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651463] = {
id = 651463,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 90,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
200102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651464] = {
id = 651464,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 100,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651465] = {
id = 651465,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 120,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
200102
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[651466] = {
id = 651466,
name = "兑换",
desc = "兑换",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 130,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
331028
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001495
},
[659308] = {
id = 659308,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10108
}
}
}
}
}
}
},
reward = v0,
jumpId = 10108
},
[659309] = {
id = 659309,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10109
}
}
}
}
}
}
},
reward = v0,
jumpId = 10109
},
[659310] = {
id = 659310,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10110
}
}
}
}
}
}
},
reward = v0,
jumpId = 10110
},
[659311] = {
id = 659311,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10111
}
}
}
}
}
}
},
reward = v0,
jumpId = 10111
},
[659312] = {
id = 659312,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10112
}
}
}
}
}
}
},
reward = v0,
jumpId = 10112
},
[659313] = {
id = 659313,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10113
}
}
}
}
}
}
},
reward = v0,
jumpId = 10113
},
[659314] = {
id = 659314,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10114
}
}
}
}
}
}
},
reward = v0,
jumpId = 10114
},
[659315] = {
id = 659315,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10115
}
}
}
}
}
}
},
reward = v0,
jumpId = 10115
},
[659316] = {
id = 659316,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10116
}
}
}
}
}
}
},
reward = v0,
jumpId = 10116
},
[659317] = {
id = 659317,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10117
}
}
}
}
}
}
},
reward = v0,
jumpId = 10117
},
[659318] = {
id = 659318,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10118
}
}
}
}
}
}
},
reward = v0,
jumpId = 10118
},
[659319] = {
id = 659319,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10119
}
}
}
}
}
}
},
reward = v0,
jumpId = 10119
},
[659320] = {
id = 659320,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10120
}
}
}
}
}
}
},
reward = v0,
jumpId = 10120
},
[659321] = {
id = 659321,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10121
}
}
}
}
}
}
},
reward = v0,
jumpId = 10121
},
[659322] = {
id = 659322,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10122
}
}
}
}
}
}
},
reward = v0,
jumpId = 10122
},
[659323] = {
id = 659323,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10123
}
}
}
}
}
}
},
reward = v0,
jumpId = 10123
},
[659324] = {
id = 659324,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10124
}
}
}
}
}
}
},
reward = v0,
jumpId = 10124
},
[659325] = {
id = 659325,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10125
}
}
}
}
}
}
},
reward = v0,
jumpId = 10125
},
[659326] = {
id = 659326,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10126
}
}
}
}
}
}
},
reward = v0,
jumpId = 10126
},
[659327] = {
id = 659327,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10127
}
}
}
}
}
}
},
reward = v0,
jumpId = 10127
},
[659328] = {
id = 659328,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659328
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659329] = {
id = 659329,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659329
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659330] = {
id = 659330,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659330
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659331] = {
id = 659331,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659331
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659332] = {
id = 659332,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659332
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659333] = {
id = 659333,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659333
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659334] = {
id = 659334,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659334
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659335] = {
id = 659335,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659335
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659336] = {
id = 659336,
name = "【订阅】日历订阅狗狗旅行活动提醒",
desc = "【订阅】日历订阅狗狗旅行活动提醒",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659336
}
}
}
}
}
}
},
taskGroupId = 1022115
},
[659337] = {
id = 659337,
name = "【每周】登录领取大王排位升星券",
desc = "【每周】登录领取大王排位升星券",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
}
},
taskGroupId = 1022117
},
[659338] = {
id = 659338,
name = "【每周】完成2局大王排位模式",
desc = "【每周】完成2局大王排位模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
352,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
}
},
jumpId = 1022,
taskGroupId = 1022117
},
[659339] = {
id = 659339,
name = "星宝/暗星达到星耀段位",
desc = "星宝/暗星达到星耀段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70011,
80011
}
},
{
type = 32,
value = {
6,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
410920
},
numList = {
1
}
},
jumpId = 1022,
taskGroupId = 1022118
},
[659340] = {
id = 659340,
name = "星宝/暗星达到钻石段位",
desc = "星宝/暗星达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70011,
80011
}
},
{
type = 32,
value = {
5,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
722029
},
numList = {
1
}
},
jumpId = 1022,
taskGroupId = 1022118
},
[659341] = {
id = 659341,
name = "星宝/暗星达到铂金段位",
desc = "星宝/暗星达到铂金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70011,
80011
}
},
{
type = 32,
value = {
4,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
3201141
},
numList = {
1
}
},
jumpId = 1022,
taskGroupId = 1022118
},
[659342] = {
id = 659342,
name = "星宝/暗星达到黄金段位",
desc = "星宝/暗星达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70011,
80011
}
},
{
type = 32,
value = {
3,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
830109
},
numList = {
1
}
},
jumpId = 1022,
taskGroupId = 1022118
},
[659343] = {
id = 659343,
name = "星宝/暗星达到白银段位",
desc = "星宝/暗星达到白银段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 145,
value = {
7,
8
}
},
{
type = 154,
value = {
70011,
80011
}
},
{
type = 32,
value = {
2,
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
3
}
},
jumpId = 1022,
taskGroupId = 1022118
},
[659344] = {
id = 659344,
name = "【每日】在星宝农场收获10次",
desc = "【每日】在星宝农场收获10次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 10,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317141
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022119
},
[659345] = {
id = 659345,
name = "【每日】在星宝农场祈福5次",
desc = "【每日】在星宝农场祈福5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 5
}
}
}
},
reward = {
itemIdList = {
317141
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022119
},
[659346] = {
id = 659346,
name = "【每周】在好友农场拿取20次作物",
desc = "【每周】在好友农场拿取20次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 20,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317141
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022120
},
[659347] = {
id = 659347,
name = "【每周】在农场餐厅营业2次",
desc = "【每周】在农场餐厅营业2次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 904,
value = 2
}
}
}
},
reward = {
itemIdList = {
317141
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022120
},
[659348] = {
id = 659348,
name = "【累计】在农场餐厅查看1次点评屏",
desc = "【累计】在农场餐厅查看1次点评屏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 905,
value = 1
}
}
}
},
reward = {
itemIdList = {
317141
},
numList = {
3
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022121
},
[659349] = {
id = 659349,
name = "【累计】在农场餐厅预约1位贵宾",
desc = "【累计】在农场餐厅预约1位贵宾",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 906,
value = 1
}
}
}
},
reward = {
itemIdList = {
317141
},
numList = {
3
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022121
},
[659350] = {
id = 659350,
name = "【每日限时】完成1次三王排位对局",
desc = "【每日限时】完成1次三王排位对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1022122
},
[659351] = {
id = 659351,
name = "5.16 中午12点农场餐厅上线！去看看",
desc = "5.16 中午12点农场餐厅上线！去看看",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
5100
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022123
},
[659352] = {
id = 659352,
name = "【每周】在星宝农场加工10次",
desc = "【每周】在星宝农场加工10次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 238,
value = 10
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022124
},
[659353] = {
id = 659353,
name = "【每周】收获20次动物产品",
desc = "【每周】收获20次动物产品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 20,
subConditionList = {
{
type = 159,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022124
},
[659354] = {
id = 659354,
name = "【累计】拜访5个星宝的农场",
desc = "【累计】拜访5个星宝的农场",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 175,
value = 5
}
}
}
},
reward = {
itemIdList = {
200620
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022125
},
[659355] = {
id = 659355,
name = "累计收集1套菜品",
desc = "累计收集1套菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 612,
value = 1
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1022126
},
[659356] = {
id = 659356,
name = "累计收集2套菜品",
desc = "累计收集2套菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 612,
value = 2
}
}
}
},
reward = {
itemIdList = {
200620
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1022126
},
[659357] = {
id = 659357,
name = "累计收集所有菜品",
desc = "累计收集所有菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 612,
value = 2
}
}
}
},
reward = {
itemIdList = {
218187
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022126
},
[659358] = {
id = 659358,
name = "收集阿花的菜品",
desc = "收集阿花的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317131
}
}
}
}
}
}
},
taskGroupId = 1022127
},
[659359] = {
id = 659359,
name = "收集阿花的菜品",
desc = "收集阿花的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317132
}
}
}
}
}
}
},
taskGroupId = 1022127
},
[659360] = {
id = 659360,
name = "收集阿花的菜品",
desc = "收集阿花的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317133
}
}
}
}
}
}
},
taskGroupId = 1022127
},
[659361] = {
id = 659361,
name = "收集阿花的菜品",
desc = "收集阿花的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317134
}
}
}
}
}
}
},
taskGroupId = 1022127
},
[659362] = {
id = 659362,
name = "收集阿花的菜品",
desc = "收集阿花的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317135
}
}
}
}
}
}
},
taskGroupId = 1022127
},
[659363] = {
id = 659363,
name = "收集厨师的菜品",
desc = "收集厨师的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317136
}
}
}
}
}
}
},
taskGroupId = 1022128
},
[659364] = {
id = 659364,
name = "收集厨师的菜品",
desc = "收集厨师的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317137
}
}
}
}
}
}
},
taskGroupId = 1022128
},
[659365] = {
id = 659365,
name = "收集厨师的菜品",
desc = "收集厨师的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317138
}
}
}
}
}
}
},
taskGroupId = 1022128
},
[659366] = {
id = 659366,
name = "收集厨师的菜品",
desc = "收集厨师的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317139
}
}
}
}
}
}
},
taskGroupId = 1022128
},
[659367] = {
id = 659367,
name = "收集厨师的菜品",
desc = "收集厨师的菜品",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 1,
subConditionList = {
{
type = 3,
value = {
317140
}
}
}
}
}
}
},
taskGroupId = 1022128
},
[770001] = {
id = 770001,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
300
}
},
taskGroupId = 1077001
},
[770002] = {
id = 770002,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
300
}
},
taskGroupId = 1077001
},
[770003] = {
id = 770003,
name = "第3天",
desc = "第3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
500
}
},
taskGroupId = 1077001
},
[770004] = {
id = 770004,
name = "第4天",
desc = "第4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 4
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
300
}
},
taskGroupId = 1077001
},
[770005] = {
id = 770005,
name = "第5天",
desc = "第5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 5
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
300
}
},
taskGroupId = 1077001
},
[770006] = {
id = 770006,
name = "第6天",
desc = "第6天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 6
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
500
}
},
taskGroupId = 1077001
},
[770007] = {
id = 770007,
name = "第7天",
desc = "第7天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 7
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
400
}
},
taskGroupId = 1077001
},
[770008] = {
id = 770008,
name = "第8天",
desc = "第8天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 8
}
}
}
},
reward = {
itemIdList = {
242005
},
numList = {
400
}
},
taskGroupId = 1077001
},
[780896] = {
id = 780896,
name = "【每日】完成1局峡谷5v5模式",
desc = "【每日】完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078455
},
[780900] = {
id = 780900,
name = "完成1局峡谷5v5模式",
desc = "完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 162,
value = {
6,
7
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078454
},
[780901] = {
id = 780901,
name = "完成2局峡谷5v5模式",
desc = "完成2局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 162,
value = {
6,
7
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078454
},
[780902] = {
id = 780902,
name = "完成3局峡谷5v5模式",
desc = "完成3局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 162,
value = {
6,
7
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078454
},
[780903] = {
id = 780903,
name = "完成4局峡谷5v5模式",
desc = "完成4局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 4,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 162,
value = {
6,
7
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078454
},
[780904] = {
id = 780904,
name = "完成5局峡谷5v5模式",
desc = "完成5局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 162,
value = {
6,
7
}
}
}
}
}
}
},
reward = {
itemIdList = {
3541
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078454
},
[780905] = {
id = 780905,
name = "组队完成1局峡谷5v5",
desc = "组队完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 88,
value = {
0
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078456
},
[780906] = {
id = 780906,
name = "完成1局峡谷5v5模式",
desc = "完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078456
},
[780907] = {
id = 780907,
name = "完成3局峡谷5v5模式",
desc = "完成3局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078456
},
[780908] = {
id = 780908,
name = "完成5局峡谷5v5模式",
desc = "完成5局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078456
},
[780909] = {
id = 780909,
name = "阅读英雄故事",
desc = "阅读英雄故事",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
780909
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1078457
}
}

local mt = {
name = "【分享】首次分享狗狗旅行活动",
desc = "【分享】首次分享狗狗旅行活动",
taskGroupId = 1022116
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data