--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_礼包.xlsx: 固定礼包

local data = {
[310343] = {
id = 310343,
effect = true,
type = "ItemType_Package",
stackedNum = 1,
quality = 2,
name = "昆仑秘境幸运礼包",
desc = [[礼包将在新赛季开启时自动打开
幸运卡：可定向自选非凡时装且随机减少外观的保底祈愿次数]],
icon = "T_Common_Item_ValuePreorder_Bag_01",
bagId = 1,
useType = "IUTO_SeasonChangeOpen",
useParam = {
13
},
packageConf = {
packageType = "PackageType_Common",
itemIds = {
201208,
214
},
itemNums = {
1,
12
}
}
},
[310344] = {
id = 310344,
effect = true,
type = "ItemType_Package",
stackedNum = 1,
quality = 2,
name = "昆仑秘境高级通行证",
desc = "新赛季开启时自动获得昆仑秘境赛季高级通行证",
icon = "T_Common_Icon_BattlePass",
bagId = 1,
useType = "IUTO_SeasonChangeOpen",
useParam = {
13
},
packageConf = {
packageType = "PackageType_Common",
itemIds = {
201003
},
itemNums = {
1
}
}
},
[310346] = {
id = 310346,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 2,
name = "音语宝匣",
desc = "打开后获得60幸运币。",
icon = "CDN:T_Item_Crown_03",
getWay = "星钻购买",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
3
},
itemNums = {
60
},
expireDays = {
0
}
}
},
[310347] = {
id = 310347,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得甜食券*5",
icon = "CDN:T_Common_Item_System_Bag_070",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
221
},
itemNums = {
5
}
}
},
[310348] = {
id = 310348,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得甜食券*5",
icon = "CDN:T_Common_Item_System_Bag_070",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
221
},
itemNums = {
5
}
}
},
[310349] = {
id = 310349,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得甜食券*10",
icon = "CDN:T_Common_Item_System_Bag_070",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
221
},
itemNums = {
10
}
}
},
[310350] = {
id = 310350,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得甜食券*10",
icon = "CDN:T_Common_Item_System_Bag_070",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
221
},
itemNums = {
10
}
}
},
[310351] = {
id = 310351,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
name = "祈愿进度礼包",
desc = "打开后获得甜食券*20",
icon = "CDN:T_Common_Item_System_Bag_070",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
221
},
itemNums = {
20
}
}
},
[311121] = {
id = 311121,
effect = true,
type = "ItemType_Package",
stackedNum = 999,
quality = 3,
name = "奖杯自选宝箱（附卡包）",
desc = "达到奖杯数可领取以下奖励",
icon = "CDN:T_StarCup_Icon_BigBoxBlue",
getWay = "奖杯征程进度奖励",
bagId = 1,
useType = "IUTO_GiftPackage",
packageConf = {
packageType = "PackageType_Common",
itemIds = {
332007,
290021
},
itemNums = {
1,
1
},
expireDays = {
0,
0
}
}
}
}

local mt = {
effect = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data