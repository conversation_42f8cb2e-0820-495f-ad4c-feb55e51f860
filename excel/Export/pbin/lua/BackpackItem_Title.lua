--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_称号.xlsx: 称号

local v0 = 2

local v1 = "CDN:Icon_Designation_Img_003"

local v2 = "CDN:T_Designation_Img_003"

local data = {
[850002] = {
id = 850002,
name = "造型大师",
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[850003] = {
id = 850003,
quality = 2,
name = "潮流教主",
icon = v1,
picture = v2,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[850015] = {
id = 850015,
quality = 4,
name = "灵巧达人",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[850016] = {
id = 850016,
name = "技术大师",
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[850017] = {
id = 850017,
name = "派对王者",
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[850018] = {
id = 850018,
quality = 2,
name = "头号玩家",
icon = v1,
picture = v2,
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[850022] = {
id = 850022,
name = "单枪匹马",
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[850023] = {
id = 850023,
quality = 2,
name = "孤胆英雄",
icon = v1,
picture = v2,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[850024] = {
id = 850024,
name = "好搭档",
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[850025] = {
id = 850025,
quality = 2,
name = "左膀右臂",
icon = v1,
picture = v2,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[850026] = {
id = 850026,
name = "草台班子",
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[850027] = {
id = 850027,
quality = 2,
name = "专业团队",
icon = v1,
picture = v2,
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[850028] = {
id = 850028,
name = "竞速新手",
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[850029] = {
id = 850029,
quality = 2,
name = "竞速达人",
icon = v1,
picture = v2,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[850030] = {
id = 850030,
name = "生存新手",
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[850031] = {
id = 850031,
quality = 2,
name = "生存达人",
icon = v1,
picture = v2,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[850032] = {
id = 850032,
name = "积分新手",
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[850033] = {
id = 850033,
quality = 2,
name = "积分达人",
icon = v1,
picture = v2,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[850034] = {
id = 850034,
quality = 2,
name = "隐世高手",
icon = v1,
picture = v2,
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[850035] = {
id = 850035,
quality = 1,
name = "威震八方",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[850036] = {
id = 850036,
name = "小有名气",
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[850037] = {
id = 850037,
quality = 2,
name = "人见人爱",
icon = v1,
picture = v2,
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[850038] = {
id = 850038,
name = "踏梦者",
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[850039] = {
id = 850039,
quality = 2,
name = "梦中仙",
icon = v1,
picture = v2,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[850040] = {
id = 850040,
quality = 2,
name = "游戏王",
icon = v1,
picture = v2,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[850041] = {
id = 850041,
quality = 2,
name = "潮流教主",
icon = v1,
picture = v2,
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[850042] = {
id = 850042,
quality = 2,
name = "天王巨星",
icon = v1,
picture = v2,
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[850043] = {
id = 850043,
quality = 2,
name = "筑梦之王",
icon = v1,
picture = v2,
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[850048] = {
id = 850048,
quality = 2,
name = "玩法大师",
icon = v1,
picture = v2,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[850049] = {
id = 850049,
name = "搜捕能手",
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[850050] = {
id = 850050,
name = "火眼金睛",
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[850051] = {
id = 850051,
quality = 2,
name = "伪装大师",
icon = v1,
picture = v2,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[850052] = {
id = 850052,
name = "星星收集者",
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[850053] = {
id = 850053,
name = "乱斗精英",
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[850054] = {
id = 850054,
quality = 2,
name = "陷阱大师",
icon = v1,
picture = v2,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[850055] = {
id = 850055,
quality = 2,
name = "勤能补拙",
icon = v1,
picture = v2,
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[850056] = {
id = 850056,
quality = 2,
name = "铁狼",
icon = v1,
picture = v2,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[850057] = {
id = 850057,
quality = 2,
name = "无间行者",
icon = v1,
picture = v2,
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[850058] = {
id = 850058,
name = "击晕能手",
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[850059] = {
id = 850059,
name = "道具达人",
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[850060] = {
id = 850060,
quality = 2,
name = "乱斗之星",
icon = v1,
picture = v2,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[850061] = {
id = 850061,
name = "火力全开",
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[850062] = {
id = 850062,
name = "孤胆英雄",
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[850063] = {
id = 850063,
quality = 2,
name = "急先锋",
icon = v1,
picture = v2,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[850064] = {
id = 850064,
name = "枪林弹雨",
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[850065] = {
id = 850065,
name = "武器大师 ",
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[850066] = {
id = 850066,
quality = 2,
name = "军火之王",
icon = v1,
picture = v2,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[850067] = {
id = 850067,
name = "全军出击",
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[850068] = {
id = 850068,
name = "竞技大师",
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[850069] = {
id = 850069,
quality = 2,
name = "叱咤战场",
icon = v1,
picture = v2,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[850070] = {
id = 850070,
name = "幽冥主宰",
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[850071] = {
id = 850071,
name = "炮火连天",
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[850072] = {
id = 850072,
quality = 2,
name = "我是传奇",
icon = v1,
picture = v2,
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[850073] = {
id = 850073,
name = "墙头草",
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[850074] = {
id = 850074,
quality = 2,
name = "会议达人",
icon = v1,
picture = v2,
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[850075] = {
id = 850075,
name = "小窝大师",
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[850076] = {
id = 850076,
name = "访问小窝",
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[850077] = {
id = 850077,
name = "点赞小窝",
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[850078] = {
id = 850078,
name = "摇树次数",
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[850079] = {
id = 850079,
name = "浇水次数",
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[850080] = {
id = 850080,
name = "植树等级",
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[850081] = {
id = 850081,
name = "小窝等级",
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[850082] = {
id = 850082,
name = "来去无踪",
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[850083] = {
id = 850083,
quality = 2,
name = "躲猫猫之王",
icon = v1,
picture = v2,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[850084] = {
id = 850084,
quality = 2,
name = "双面精英",
icon = v1,
picture = v2,
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[850085] = {
id = 850085,
name = "卧底大师",
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[850086] = {
id = 850086,
name = "拾荒者",
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[850087] = {
id = 850087,
quality = 2,
name = "神枪手",
icon = v1,
picture = v2,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[850088] = {
id = 850088,
quality = 2,
name = "福尔摩斯",
icon = v1,
picture = v2,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[850095] = {
id = 850095,
quality = 2,
name = "对决传奇",
icon = v1,
picture = v2,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[850096] = {
id = 850096,
quality = 2,
name = "人生赢家",
icon = v1,
picture = v2,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[850097] = {
id = 850097,
quality = 2,
name = "超星设计师",
icon = v1,
picture = v2,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[850098] = {
id = 850098,
quality = 2,
name = "筑梦大师",
icon = v1,
picture = v2,
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[850099] = {
id = 850099,
quality = 2,
name = "时尚先锋",
icon = v1,
picture = v2,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[850100] = {
id = 850100,
quality = 1,
name = "潮流教科书",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[850044] = {
id = 850044,
quality = 1,
name = "神仙眷侣",
desc = "达成特定亲密关系后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 1,
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[850045] = {
id = 850045,
quality = 1,
name = "情同手足",
desc = "达成特定亲密关系后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 1,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[850046] = {
id = 850046,
quality = 1,
name = "姊妹情深",
desc = "达成特定亲密关系后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 1,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[850047] = {
id = 850047,
quality = 1,
name = "义结金兰",
desc = "达成特定亲密关系后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 1,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[850091] = {
id = 850091,
quality = 2,
name = "千里挑一",
desc = "参加赛事《冠军之星》可以获得",
icon = v1,
picture = v2,
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[850092] = {
id = 850092,
name = "百里挑一",
desc = "参加赛事《冠军之星》可以获得",
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[850093] = {
id = 850093,
quality = 4,
name = "十里挑一",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[850094] = {
id = 850094,
quality = 4,
name = "挑不了一点",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[850101] = {
id = 850101,
quality = 4,
name = "初次星动",
desc = "星动测试累登3天获得",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[850102] = {
id = 850102,
quality = 4,
name = "造梦旅人",
desc = "造梦之旅达成阶段目标获得",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
showInView = 1,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[850103] = {
id = 850103,
name = "全勤王",
desc = "参加赛事《冠军之星》可以获得",
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[850104] = {
id = 850104,
quality = 1,
name = "冠军之星",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:Icon_Designation_Img_005",
picture = "CDN:T_Designation_Img_005",
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[850105] = {
id = 850105,
quality = 1,
name = "巅峰巨星",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[850106] = {
id = 850106,
quality = 2,
name = "种子选手",
desc = "参加赛事《冠军之星》可以获得",
icon = v1,
picture = v2,
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[850107] = {
id = 850107,
quality = 4,
name = "道具好手",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[850108] = {
id = 850108,
name = "海选达人",
desc = "参加赛事《冠军之星》可以获得",
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[850109] = {
id = 850109,
name = "飞人",
desc = "参加赛事《冠军之星》可以获得",
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[850110] = {
id = 850110,
name = "生存能手",
desc = "参加赛事《冠军之星》可以获得",
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[850111] = {
id = 850111,
quality = 2,
name = "排面无限大",
desc = "参与活动获得",
icon = v1,
picture = v2,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[850112] = {
id = 850112,
quality = 2,
name = "遥遥领先",
desc = "达成特定成就后获得",
icon = v1,
picture = v2,
showInView = 1,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[850113] = {
id = 850113,
quality = 2,
name = "菡萏摇香",
desc = "桃源通行证达到指定等级获得",
icon = v1,
picture = v2,
showInView = 1,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[850114] = {
id = 850114,
quality = 1,
name = "伪装大神",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[850115] = {
id = 850115,
quality = 1,
name = "隐匿之王",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[850116] = {
id = 850116,
quality = 1,
name = "推理之星",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[850117] = {
id = 850117,
quality = 1,
name = "擂台勇士",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
}
}

local mt = {
type = "ItemType_Title",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 4,
itemNum = 10
}
},
quality = 3,
desc = "达成成就获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002",
showInView = 0,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data