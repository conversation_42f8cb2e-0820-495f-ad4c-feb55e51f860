--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/B_BP通行证_主玩法.xlsx: 任务配置

local data = {
[85106] = {
id = 85106,
name = "【赛季挑战】本赛季中完成25次天天晋级赛（排位）四人模式",
desc = "【赛季挑战】本赛季中完成25次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 25,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
jumpId = 1153,
taskGroupId = 12012
},
[85107] = {
id = 85107,
name = "【赛季挑战】在天天晋级赛（排位）中累计获得技巧分不低于60",
desc = "【赛季挑战】在天天晋级赛（排位）中累计获得技巧分不低于60",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 858,
value = 60,
subConditionList = {
{
type = 203,
value = {
766
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
1000
}
},
taskGroupId = 12012
},
[85108] = {
id = 85108,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12012
},
[85109] = {
id = 85109,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12012
},
[85110] = {
id = 85110,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12012
},
[85111] = {
id = 85111,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12013
},
[85112] = {
id = 85112,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12013
},
[85113] = {
id = 85113,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12013
},
[85114] = {
id = 85114,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12014
},
[85115] = {
id = 85115,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12014
},
[85116] = {
id = 85116,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12014
},
[85117] = {
id = 85117,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12015
},
[85118] = {
id = 85118,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12015
},
[85119] = {
id = 85119,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12015
},
[85120] = {
id = 85120,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12016
},
[85121] = {
id = 85121,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12016
},
[85122] = {
id = 85122,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12016
},
[85123] = {
id = 85123,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12017
},
[85124] = {
id = 85124,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12017
},
[85125] = {
id = 85125,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12017
},
[85126] = {
id = 85126,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12018
},
[85127] = {
id = 85127,
name = "完成2次天天晋级赛（排位）四人模式",
desc = "完成2次天天晋级赛（排位）四人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
jumpId = 1153,
taskGroupId = 12018
},
[85128] = {
id = 85128,
name = "天天晋级礼活动等级达到5级",
desc = "天天晋级礼活动等级达到5级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 5,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12018
},
[85129] = {
id = 85129,
name = "在天天晋级赛（排位）中获得前8名",
desc = "在天天晋级赛（排位）中获得前8名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 14,
value = {
3
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12019
},
[85130] = {
id = 85130,
name = "在天天晋级赛（排位）中使用10次道具	",
desc = "在天天晋级赛（排位）中使用10次道具	",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 82,
value = 10,
subConditionList = {
{
type = 128,
value = {
10001,
10002,
10003,
10004,
10005,
10006,
10007,
10008,
10009,
10010,
10011,
10012,
10013,
10014,
10015,
10016,
10017,
10018,
10019,
10020,
10021,
10022,
10023,
10024
}
},
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
150
}
},
taskGroupId = 12019
},
[85131] = {
id = 85131,
name = "天天晋级礼活动等级达到10级",
desc = "天天晋级礼活动等级达到10级",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 310,
value = 10,
subConditionList = {
{
type = 504,
value = {
4004
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
200
}
},
taskGroupId = 12019
}
}

local mt = {
jumpId = 1080
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data