--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_兑换.xlsx: 【运营活动】常规兑换

local data = {
[2300031] = {
commodityId = 2300031,
commodityName = "【限时】狼人学家",
beginTime = {
seconds = 1711036800
},
order = 1,
itemIds = {
820051
}
},
[2300032] = {
commodityId = 2300032,
commodityName = "【限时】躲猫猫侠",
beginTime = {
seconds = 1711036800
},
order = 2,
itemIds = {
820052
}
},
[2300033] = {
commodityId = 2300033,
commodityName = "【限时】飞车大神",
beginTime = {
seconds = 1711036800
},
order = 3,
itemIds = {
820053
}
},
[2300034] = {
commodityId = 2300034,
commodityName = "【限时】狼人学家",
beginTime = {
seconds = 1711036800
},
order = 101,
itemIds = {
840071
}
},
[2300035] = {
commodityId = 2300035,
commodityName = "【限时】躲猫猫侠",
beginTime = {
seconds = 1711036800
},
order = 102,
itemIds = {
840072
}
},
[2300036] = {
commodityId = 2300036,
commodityName = "【限时】飞车大神",
beginTime = {
seconds = 1711036800
},
order = 103,
itemIds = {
840073
}
},
[2300037] = {
commodityId = 2300037,
commodityName = "【限时】狼人头像",
beginTime = {
seconds = 1711036800
},
order = 201,
itemIds = {
860023
}
},
[2300038] = {
commodityId = 2300038,
commodityName = "【限时】躲猫猫头像",
beginTime = {
seconds = 1711036800
},
order = 202,
itemIds = {
860024
}
},
[2300039] = {
commodityId = 2300039,
commodityName = "【限时】飞车头像",
beginTime = {
seconds = 1711036800
},
order = 203,
itemIds = {
860025
}
},
[2300040] = {
commodityId = 2300040,
commodityName = "【限时】乱斗新星",
beginTime = {
seconds = 1712851200
},
order = 8,
itemIds = {
820054
}
},
[2300041] = {
commodityId = 2300041,
commodityName = "【限时】星宝高手",
beginTime = {
seconds = 1711641600
},
order = 4,
itemIds = {
820055
}
},
[2300042] = {
commodityId = 2300042,
commodityName = "【限时】突围先锋",
beginTime = {
seconds = 1712851200
},
order = 7,
itemIds = {
820056
}
},
[2300043] = {
commodityId = 2300043,
commodityName = "【限时】武器大师",
beginTime = {
seconds = 1712246400
},
order = 6,
itemIds = {
820057
}
},
[2300044] = {
commodityId = 2300044,
commodityName = "【限时】暗星高手",
beginTime = {
seconds = 1711641600
},
order = 5,
itemIds = {
820058
}
},
[2300045] = {
commodityId = 2300045,
commodityName = "【限时】乱斗新星",
beginTime = {
seconds = 1712851200
},
order = 108,
itemIds = {
840074
}
},
[2300046] = {
commodityId = 2300046,
commodityName = "【限时】星宝高手",
beginTime = {
seconds = 1711641600
},
order = 104,
itemIds = {
840075
}
},
[2300047] = {
commodityId = 2300047,
commodityName = "【限时】突围先锋",
beginTime = {
seconds = 1712851200
},
order = 107,
itemIds = {
840076
}
},
[2300048] = {
commodityId = 2300048,
commodityName = "【限时】武器大师",
beginTime = {
seconds = 1712246400
},
order = 106,
itemIds = {
840077
}
},
[2300049] = {
commodityId = 2300049,
commodityName = "【限时】暗星高手",
beginTime = {
seconds = 1711641600
},
order = 105,
itemIds = {
840078
}
},
[2300050] = {
commodityId = 2300050,
commodityName = "【限时】大乱斗头像",
beginTime = {
seconds = 1712851200
},
order = 208,
itemIds = {
860026
}
},
[2300051] = {
commodityId = 2300051,
commodityName = "【限时】星宝头像",
beginTime = {
seconds = 1711641600
},
order = 204,
itemIds = {
860027
}
},
[2300052] = {
commodityId = 2300052,
commodityName = "【限时】梦幻岛头像",
beginTime = {
seconds = 1712851200
},
order = 207,
itemIds = {
860028
}
},
[2300053] = {
commodityId = 2300053,
commodityName = "【限时】武器大师头像",
beginTime = {
seconds = 1712246400
},
order = 206,
itemIds = {
860029
}
},
[2300054] = {
commodityId = 2300054,
commodityName = "【限时】暗星头像",
beginTime = {
seconds = 1711641600
},
order = 205,
itemIds = {
860030
}
},
[2300055] = {
mallId = 154,
commodityId = 2300055,
commodityName = "砖头",
coinType = 3641,
price = 20,
limitNum = 12,
beginTime = {
seconds = 1745942400
},
endTime = {
seconds = 1756483199
},
itemIds = {
200835
},
expireTimestamps = {
{
seconds = 1756483199
}
}
}
}

local mt = {
mallId = 23,
coinType = 3200,
price = 10,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
endTime = {
seconds = 1714060799
},
gender = 0,
itemNums = {
1
},
expireTimestamps = {
{
seconds = 1715270399
}
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data