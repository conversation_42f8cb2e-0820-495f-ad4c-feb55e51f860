--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_FB.xlsx: 玩法

local data = {
[6300] = {
id = 6300,
modeID = 3,
desc = "峡谷足球",
maxTeamMember = 5,
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = true,
sort = 1,
thumbImage = "CDN:T_ModelSelect_Img_Type_Arena_BS",
image = "CDN:T_ModelSelectLarge_Img_Type_Arena_BS",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "6006",
modeGroup = "6300",
settleProc = "MTSC_Common",
matchTeamNum = {
1,
2,
3,
4,
5
},
teamTag = "5",
unlockRule = "请前往手机端游玩",
battleRecordCnt = 30,
matchRuleId = 63000,
TeamMatchGame = true,
battlePlayerNum = 10,
dropId = 28,
mmrType = "MST_Arena_FootBall",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRank",
warmRoundType = "WRST_Arena_Bs",
isShowBattleRecord = true,
descShort = "双人配合五个阵营，争夺精彩刺激的竞技赛冠军！",
battleRecordStyle = "UI_PlayerInfo_ModRecord_ArenaBattleResult3v3",
outImage = "T_Lobby_Start_Arena",
pakGroup = 50021,
recordType = 6300,
buttonDesc = "团竞",
gameTypeId = 722,
layoutID = 36,
isShowEmotionEntrance = 1,
warmRoundRoomInfoId = 63001,
warmRoundMatchRuleId = 6300,
ComplicatedPlay = 3,
detailBattleRecordPakId = 50021,
AutoDownloadPakGroup = {
50021
},
AutoDeletePakGroup = {
50021
},
PakPlayID = 1201,
playName = "arena",
loginPlatList = {
7
},
isBan = true,
isArena = true,
battleDetailStyle = "Feature.Arena.Script.FootBall.UI.Settlement.UI_FB_Settlement_DetailMain"
}
}

local mt = {
isPermanent = false,
TeamMatchGame = false,
isShowBattleRecord = false,
UseDefaultChampionDisplayScene = false,
isBan = false,
isArena = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data