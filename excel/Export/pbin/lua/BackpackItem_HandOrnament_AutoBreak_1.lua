--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_手持物.xlsm: 手部装扮

local v0 = 1

local v1 = "商城"

local v2 = {
15
}

local v3 = "IUTO_None"

local v4 = {
fashionValue = 125
}

local v5 = 90

local v6 = {
seconds = 4074768000
}

local v7 = {
15,
-30
}

local data = {
[640101] = {
id = 640101,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "全垒打",
desc = "星运棒球棒，带来好运的棒球棒",
icon = "CDN:Icon_Handhold_043",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SK_Handhold_043",
modelType = 2
},
shareTexts = {
"星运棒球棒，带来好运的棒球棒"
},
beginTime = v6,
suitId = 40057,
suitName = "全垒打",
suitIcon = "CDN:Icon_Handhold_043",
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
}
},
[640102] = {
id = 640102,
effect = true,
exceedReplaceItem = {
{
itemId = 224,
itemNum = 30
}
},
name = "爱神之弓",
desc = "别再犹豫不决，因为开弓没有回头箭",
icon = "CDN:Icon_Handhold_045",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_045",
modelType = 2
},
shareTexts = {
"一箭射出，此生不悔"
},
beginTime = v6,
suitId = 40058,
suitName = "爱神之弓",
suitIcon = "CDN:Icon_Handhold_045",
shareOffset = {
-10,
20
},
previewShareOffset = {
25,
0
}
},
[640103] = {
id = 640103,
effect = true,
name = "爱神之弓",
desc = "别再犹豫不决，因为开弓没有回头箭",
icon = "CDN:Icon_Handhold_045_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640102,
fashionValue = 25,
belongToGroup = {
640103,
640104
}
},
resourceConf = {
model = "SK_Handhold_045",
material = "MI_Handhold_045_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72090,
shareTexts = {
"一箭射出，此生不悔"
},
shareOffset = {
-10,
20
},
previewShareOffset = {
25,
0
}
},
[640104] = {
id = 640104,
effect = true,
name = "爱神之弓",
desc = "别再犹豫不决，因为开弓没有回头箭",
icon = "CDN:Icon_Handhold_045_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640102,
fashionValue = 25,
belongToGroup = {
640103,
640104
}
},
resourceConf = {
model = "SK_Handhold_045",
material = "MI_Handhold_045_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72091,
shareTexts = {
"一箭射出，此生不悔"
},
shareOffset = {
-10,
20
},
previewShareOffset = {
25,
0
}
},
[640105] = {
id = 640105,
effect = true,
exceedReplaceItem = {
{
itemId = 224,
itemNum = 180
}
},
quality = 1,
name = "泡泡鲨玩偶",
desc = "别害怕，我是很温柔的鲨鱼",
icon = "CDN:Icon_Handhold_068",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_068",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"信奉和平，禁止打架"
},
beginTime = v6,
suitId = 40059,
suitName = "泡泡鲨玩偶",
suitIcon = "CDN:Icon_Handhold_068",
shareOffset = {
-10,
-20
},
previewShareOffset = {
25,
-20
}
},
[640106] = {
id = 640106,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "泡泡鲨玩偶",
desc = "别害怕，我是很温柔的鲨鱼",
icon = "CDN:Icon_Handhold_068_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640105,
fashionValue = 35,
belongToGroup = {
640106,
640107
}
},
resourceConf = {
model = "SK_Handhold_068",
material = "MI_Handhold_068_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72093,
scaleTimes = 90,
shareTexts = {
"信奉和平，禁止打架"
},
shareOffset = {
-10,
-20
},
previewShareOffset = {
25,
-20
}
},
[640107] = {
id = 640107,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "泡泡鲨玩偶",
desc = "别害怕，我是很温柔的鲨鱼",
icon = "CDN:Icon_Handhold_068_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640105,
fashionValue = 35,
belongToGroup = {
640106,
640107
}
},
resourceConf = {
model = "SK_Handhold_068",
material = "MI_Handhold_068_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72094,
scaleTimes = 90,
shareTexts = {
"信奉和平，禁止打架"
},
shareOffset = {
-10,
-20
},
previewShareOffset = {
25,
-20
}
},
[640108] = {
id = 640108,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "圣光权杖",
desc = "羽翼轻扬，圣光洒落，让心灵重归宁静",
icon = "CDN:Icon_Handhold_049",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_049",
modelType = 2
},
scaleTimes = 70,
shareTexts = {
"圣洁之光，照耀四方"
},
beginTime = v6,
suitId = 40060,
suitName = "圣光权杖",
suitIcon = "CDN:Icon_Handhold_049",
shareOffset = {
-10,
-50
},
previewShareOffset = {
25,
-50
}
},
[640109] = {
id = 640109,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "圣光权杖",
desc = "羽翼轻扬，圣光洒落，让心灵重归宁静",
icon = "CDN:Icon_Handhold_049_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640108,
fashionValue = 35,
belongToGroup = {
640109,
640110
}
},
resourceConf = {
model = "SK_Handhold_049",
material = "MI_Handhold_049_1_HP01;MI_Handhold_049_2_HP01;MI_Handhold_049_3_HP01",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72096,
scaleTimes = 70,
shareTexts = {
"圣洁之光，照耀四方"
},
shareOffset = {
-10,
-50
},
previewShareOffset = {
25,
-50
}
},
[640110] = {
id = 640110,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "圣光权杖",
desc = "羽翼轻扬，圣光洒落，让心灵重归宁静",
icon = "CDN:Icon_Handhold_049_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640108,
fashionValue = 35,
belongToGroup = {
640109,
640110
}
},
resourceConf = {
model = "SK_Handhold_049",
material = "MI_Handhold_049_1_HP02;MI_Handhold_049_2_HP02;MI_Handhold_049_3_HP02",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72097,
scaleTimes = 70,
shareTexts = {
"圣洁之光，照耀四方"
},
shareOffset = {
-10,
-50
},
previewShareOffset = {
25,
-50
}
},
[640111] = {
id = 640111,
effect = true,
name = "福运金镲",
desc = "镲鼓喧天的热闹，都与你有关",
icon = "CDN:Icon_Handhold_071",
getWay = "星宝送福签",
jumpId = {
12306
},
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_071",
modelType = 2
},
shareTexts = {
"小镲声响，黄金万两"
},
beginTime = {
seconds = 1737648000
},
suitId = 40061,
suitName = "福运金镲",
suitIcon = "CDN:Icon_Handhold_071",
shareOffset = {
-10,
20
},
previewShareOffset = {
15,
10
}
},
[640112] = {
id = 640112,
effect = true,
name = "福运金镲",
desc = "镲鼓喧天的热闹，都与你有关",
icon = "CDN:Icon_Handhold_071_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640111,
fashionValue = 25,
belongToGroup = {
640112,
640113
}
},
resourceConf = {
model = "SK_Handhold_071",
material = "MI_Handhold_071_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72099,
shareTexts = {
"小镲声响，黄金万两"
},
shareOffset = {
-10,
20
},
previewShareOffset = {
15,
10
}
},
[640113] = {
id = 640113,
effect = true,
name = "福运金镲",
desc = "镲鼓喧天的热闹，都与你有关",
icon = "CDN:Icon_Handhold_071_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640111,
fashionValue = 25,
belongToGroup = {
640112,
640113
}
},
resourceConf = {
model = "SK_Handhold_071",
material = "MI_Handhold_071_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72100,
shareTexts = {
"小镲声响，黄金万两"
},
shareOffset = {
-10,
20
},
previewShareOffset = {
15,
10
}
},
[640114] = {
id = 640114,
effect = true,
name = "净窗精灵",
desc = "玻璃擦得亮，生活有方向",
icon = "CDN:Icon_Handhold_072",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_072",
modelType = 2
},
shareTexts = {
"时时勤拂拭，勿使染尘埃"
},
beginTime = v6,
suitId = 40062,
suitName = "净窗精灵",
suitIcon = "CDN:Icon_Handhold_072",
shareOffset = {
-10,
20
},
previewShareOffset = {
15,
-20
}
},
[640115] = {
id = 640115,
effect = true,
name = "净窗精灵",
desc = "玻璃擦得亮，生活有方向",
icon = "CDN:Icon_Handhold_072_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640114,
fashionValue = 25,
belongToGroup = {
640115,
640116
}
},
resourceConf = {
model = "SK_Handhold_072",
material = "MI_Handhold_072_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72102,
shareTexts = {
"时时勤拂拭，勿使染尘埃"
},
shareOffset = {
-10,
20
},
previewShareOffset = {
15,
-20
}
},
[640116] = {
id = 640116,
effect = true,
name = "净窗精灵",
desc = "玻璃擦得亮，生活有方向",
icon = "CDN:Icon_Handhold_072_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640114,
fashionValue = 25,
belongToGroup = {
640115,
640116
}
},
resourceConf = {
model = "SK_Handhold_072",
material = "MI_Handhold_072_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72103,
shareTexts = {
"时时勤拂拭，勿使染尘埃"
},
shareOffset = {
-10,
20
},
previewShareOffset = {
15,
-20
}
},
[640117] = {
id = 640117,
effect = true,
name = "地铁跑酷金币",
desc = "为了捡金币绕远路，也许不值当",
icon = "CDN:Icon_Handhold_073",
getWay = "送手持物",
jumpId = {
129
},
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_073",
modelType = 2
},
scaleTimes = 200,
shareTexts = {
"当然是越多越好啦"
},
beginTime = {
seconds = 1737043200
},
suitId = 40063,
suitName = "地铁跑酷金币",
suitIcon = "CDN:Icon_Handhold_073",
shareOffset = {
-10,
20
},
previewShareOffset = {
25,
0
}
},
[640118] = {
id = 640118,
effect = true,
name = "大耳狗棒棒糖",
desc = "甜度刚好，幸福爆表",
icon = "CDN:Icon_Handhold_065",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_065",
modelType = 2,
IconLabelId = 102
},
shareTexts = {
"纯白色，是牛奶味儿的吗？"
},
beginTime = v6,
suitId = 40067,
suitName = "大耳狗棒棒糖",
suitIcon = "CDN:Icon_Handhold_065",
shareOffset = {
-5,
-20
},
previewShareOffset = {
10,
-30
}
},
[640119] = {
id = 640119,
effect = true,
name = "美乐蒂气球",
desc = "要牢牢抓紧，幸福才不会飞走哦",
icon = "CDN:Icon_Handhold_067",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_067",
modelType = 2,
IconLabelId = 102
},
shareTexts = {
"送给你，祝你天天好心情~"
},
beginTime = v6,
suitId = 40068,
suitName = "美乐蒂气球",
suitIcon = "CDN:Icon_Handhold_067",
shareOffset = {
-5,
-60
},
previewShareOffset = {
5,
-60
}
},
[640120] = {
id = 640120,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "弦歌雅韵",
desc = "一曲霓裳，绕梁三日，尽显盛世风华",
icon = "CDN:Icon_Handhold_069",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_069",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"指尖流转，乐动长安"
},
beginTime = v6,
suitId = 40064,
suitName = "弦歌雅韵",
suitIcon = "CDN:Icon_Handhold_069",
shareOffset = {
-10,
-20
},
previewShareOffset = {
25,
-25
}
},
[640121] = {
id = 640121,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "弦歌雅韵",
desc = "一曲霓裳，绕梁三日，尽显盛世风华",
icon = "CDN:Icon_Handhold_069_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640120,
fashionValue = 35,
belongToGroup = {
640121,
640122
}
},
resourceConf = {
model = "SK_Handhold_069",
material = "MI_Handhold_069_1_HP01;MI_Handhold_069_2_HP01;MI_Handhold_069_3_HP01",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72108,
scaleTimes = 90,
shareTexts = {
"指尖流转，乐动长安"
},
shareOffset = {
-10,
-20
},
previewShareOffset = {
25,
-25
}
},
[640122] = {
id = 640122,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "弦歌雅韵",
desc = "一曲霓裳，绕梁三日，尽显盛世风华",
icon = "CDN:Icon_Handhold_069_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640120,
fashionValue = 35,
belongToGroup = {
640121,
640122
}
},
resourceConf = {
model = "SK_Handhold_069",
material = "MI_Handhold_069_1_HP02;MI_Handhold_069_2_HP02;MI_Handhold_069_3_HP02",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72109,
scaleTimes = 90,
shareTexts = {
"指尖流转，乐动长安"
},
shareOffset = {
-10,
-20
},
previewShareOffset = {
25,
-25
}
},
[640123] = {
id = 640123,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 60
}
},
quality = 1,
name = "心语之桥",
desc = "愿每个明天，都比今天更美好一点",
icon = "CDN:Icon_Handhold_076",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 600
},
resourceConf = {
model = "SK_Handhold_076",
modelType = 2,
IconLabelId = 101
},
shareTexts = {
"祝心中的花朵，永不凋零"
},
beginTime = v6,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_640123.astc",
suitId = 40065,
suitName = "心语之桥",
suitIcon = "CDN:Icon_Handhold_076",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_640123.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_640123.astc",
shareOffset = {
0,
0
},
previewShareOffset = {
25,
-35
},
bEnableDoubleShow = true,
customPreviewConf = {
BP = "BP_CustomPreview_HandHold_076",
Scale = 0.27000001072883606,
CharIdleAnim = "AS_CH_Handhold_076_Extraeffect_001_DanceLoop;AS_CH_Handhold_076_Extraeffect_002_DanceLoop",
CharOffset = {
0,
0,
0
},
CharRotateYaw = 0
},
previewSocketNames = {
"LEFT_01",
"RIGHT_01"
}
},
[640124] = {
id = 640124,
effect = true,
name = "闪耀应援",
desc = "一击即中你的心",
icon = "CDN:Icon_Handhold_075",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_075",
modelType = 2,
IconLabelId = 102
},
shareTexts = {
"别跑啊，晚会还没结束呢"
},
beginTime = v6,
suitId = 40066,
suitName = "闪耀应援",
suitIcon = "CDN:Icon_Handhold_075",
shareOffset = {
-10,
-20
},
previewShareOffset = v7
},
[640125] = {
id = 640125,
effect = true,
name = "苍龙行云",
desc = "华山如立，不可摧折",
icon = "CDN:Icon_Handhold_077",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_077",
modelType = 2,
IconLabelId = 102
},
scaleTimes = 80,
shareTexts = {
"通往太平之路，自古只有一条"
},
beginTime = v6,
suitId = 40069,
suitName = "苍龙行云",
suitIcon = "CDN:Icon_Handhold_077",
shareOffset = {
-10,
10
},
previewShareOffset = {
15,
5
}
},
[640126] = {
id = 640126,
effect = true,
exceedReplaceItem = {
{
itemId = 226,
itemNum = 10
}
},
name = "银铃手鼓",
desc = "也曾打着节拍，轻轻哼唱",
icon = "CDN:Icon_Handhold_063",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_063",
modelType = 2
},
shareTexts = {
"铃声一响，童年登场"
},
beginTime = v6,
suitId = 40070,
suitName = "银铃手鼓",
suitIcon = "CDN:Icon_Handhold_063",
shareOffset = {
-10,
15
},
previewShareOffset = v7
},
[640127] = {
id = 640127,
effect = true,
name = "银铃手鼓",
desc = "也曾打着节拍，轻轻哼唱",
icon = "CDN:Icon_Handhold_063_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640126,
fashionValue = 25,
belongToGroup = {
640127,
640128
}
},
resourceConf = {
model = "SK_Handhold_063",
material = "MI_Handhold_063_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72116,
shareTexts = {
"铃声一响，童年登场"
},
shareOffset = {
-10,
15
},
previewShareOffset = v7
},
[640128] = {
id = 640128,
effect = true,
name = "银铃手鼓",
desc = "也曾打着节拍，轻轻哼唱",
icon = "CDN:Icon_Handhold_063_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640126,
fashionValue = 25,
belongToGroup = {
640127,
640128
}
},
resourceConf = {
model = "SK_Handhold_063",
material = "MI_Handhold_063_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72117,
shareTexts = {
"铃声一响，童年登场"
},
shareOffset = {
-10,
15
},
previewShareOffset = v7
},
[640129] = {
id = 640129,
effect = true,
name = "冷香团扇",
desc = "梅花点点，团扇轻摇雪中景",
icon = "CDN:Icon_Handhold_060",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_060",
modelType = 2
},
shareTexts = {
"素手持扇，梅花映雪"
},
beginTime = {
seconds = 1739721600
},
suitId = 40071,
suitName = "冷香团扇",
suitIcon = "CDN:Icon_Handhold_060",
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640130] = {
id = 640130,
effect = true,
name = "冷香团扇",
desc = "梅花点点，团扇轻摇雪中景",
icon = "CDN:Icon_Handhold_060_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640129,
fashionValue = 25,
belongToGroup = {
640130,
640131
}
},
resourceConf = {
model = "SK_Handhold_060",
material = "MI_Handhold_060_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72119,
shareTexts = {
"素手持扇，梅花映雪"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640131] = {
id = 640131,
effect = true,
name = "冷香团扇",
desc = "梅花点点，团扇轻摇雪中景",
icon = "CDN:Icon_Handhold_060_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640129,
fashionValue = 25,
belongToGroup = {
640130,
640131
}
},
resourceConf = {
model = "SK_Handhold_060",
material = "MI_Handhold_060_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72120,
shareTexts = {
"素手持扇，梅花映雪"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640132] = {
id = 640132,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 30
}
},
quality = 1,
name = "赤霄焰舞",
desc = "轻挥之间，热浪翻滚，带来温暖与光明",
icon = "CDN:Icon_Handhold_070",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_070",
modelType = 2
},
shareTexts = {
"火羽舞翩跹，焰光映容颜"
},
beginTime = {
seconds = 1739721600
},
suitId = 40072,
suitName = "赤霄焰舞",
suitIcon = "CDN:Icon_Handhold_070",
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640133] = {
id = 640133,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "赤霄焰舞",
desc = "轻挥之间，热浪翻滚，带来温暖与光明",
icon = "CDN:Icon_Handhold_070_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640132,
fashionValue = 35,
belongToGroup = {
640133,
640134
}
},
resourceConf = {
model = "SK_Handhold_070",
material = "MI_Handhold_070_1_HP01;MI_Handhold_070_2_HP01;MI_Handhold_070_3_HP01",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72122,
shareTexts = {
"火羽舞翩跹，焰光映容颜"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640134] = {
id = 640134,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "赤霄焰舞",
desc = "轻挥之间，热浪翻滚，带来温暖与光明",
icon = "CDN:Icon_Handhold_070_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640132,
fashionValue = 35,
belongToGroup = {
640133,
640134
}
},
resourceConf = {
model = "SK_Handhold_070",
material = "MI_Handhold_070_1_HP02;MI_Handhold_070_2_HP02;MI_Handhold_070_3_HP02",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72123,
shareTexts = {
"火羽舞翩跹，焰光映容颜"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640135] = {
id = 640135,
effect = true,
name = "旋风清道夫",
desc = "有我在，冲就完了",
icon = "CDN:Icon_Handhold_051",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_051",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"你想通了吗"
},
beginTime = v6,
suitId = 40073,
suitName = "旋风清道夫",
suitIcon = "CDN:Icon_Handhold_051",
shareOffset = {
-10,
0
},
previewShareOffset = v7
},
[640136] = {
id = 640136,
effect = true,
name = "旋风清道夫",
desc = "有我在，冲就完了",
icon = "CDN:Icon_Handhold_051_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640135,
fashionValue = 25,
belongToGroup = {
640136,
640137
}
},
resourceConf = {
model = "SK_Handhold_051",
material = "MI_Handhold_051_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72127,
scaleTimes = 90,
shareTexts = {
"你想通了吗"
},
shareOffset = {
-10,
0
},
previewShareOffset = v7
},
[640137] = {
id = 640137,
effect = true,
name = "旋风清道夫",
desc = "有我在，冲就完了",
icon = "CDN:Icon_Handhold_051_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640135,
fashionValue = 25,
belongToGroup = {
640136,
640137
}
},
resourceConf = {
model = "SK_Handhold_051",
material = "MI_Handhold_051_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72128,
scaleTimes = 90,
shareTexts = {
"你想通了吗"
},
shareOffset = {
-10,
0
},
previewShareOffset = v7
},
[640138] = {
id = 640138,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 60
}
},
quality = 1,
name = "心语之桥【供卡池展示】",
desc = "愿每个明天，都比今天更美好一点",
icon = "CDN:Icon_Handhold_076",
useType = v3,
resourceConf = {
model = "SK_Handhold_076_Extraeffect_001",
modelType = 2,
idleAnim = "AS_CH_Handhold_076_Extraeffect_002_Loop"
},
scaleTimes = 20,
shareTexts = {
"祝心中的花朵，永不凋零"
}
},
[640139] = {
id = 640139,
effect = true,
name = "麦霸助手",
desc = "聚光灯下，站的是我",
icon = "CDN:Icon_Handhold_057",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_057",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"想唱就唱，唱得响亮"
},
beginTime = v6,
suitId = 40074,
suitName = "麦霸助手",
suitIcon = "CDN:Icon_Handhold_057",
shareOffset = {
0,
-30
},
previewShareOffset = v7
},
[640140] = {
id = 640140,
effect = true,
name = "麦霸助手",
desc = "聚光灯下，站的是我",
icon = "CDN:Icon_Handhold_057_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640139,
fashionValue = 25,
belongToGroup = {
640140,
640141
}
},
resourceConf = {
model = "SK_Handhold_057",
material = "MI_Handhold_057_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72132,
scaleTimes = 90,
shareTexts = {
"想唱就唱，唱得响亮"
},
shareOffset = {
0,
-30
},
previewShareOffset = v7
},
[640141] = {
id = 640141,
effect = true,
name = "麦霸助手",
desc = "聚光灯下，站的是我",
icon = "CDN:Icon_Handhold_057_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640139,
fashionValue = 25,
belongToGroup = {
640140,
640141
}
},
resourceConf = {
model = "SK_Handhold_057",
material = "MI_Handhold_057_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72133,
scaleTimes = 90,
shareTexts = {
"想唱就唱，唱得响亮"
},
shareOffset = {
0,
-30
},
previewShareOffset = v7
},
[640142] = {
id = 640142,
effect = true,
name = "爱心之斧",
desc = "爱心之斧的正义冲击",
icon = "CDN:Icon_Handhold_064",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_064",
modelType = 2,
IconLabelId = 102
},
scaleTimes = 70,
shareTexts = {
"进攻是最好的防守"
},
beginTime = v6,
suitId = 40075,
suitName = "爱心之斧",
suitIcon = "CDN:Icon_Handhold_064",
shareOffset = {
-8,
-30
},
previewShareOffset = {
0,
-50
}
},
[640143] = {
id = 640143,
effect = true,
name = "天机羽扇",
desc = "运筹帷幄之中，决胜千里之外",
icon = "CDN:Icon_Handhold_086",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_086",
modelType = 2,
IconLabelId = 102
},
scaleTimes = 90,
shareTexts = {
"是的，这也是计划中的一环"
},
beginTime = v6,
suitId = 40076,
suitName = "天机羽扇",
suitIcon = "CDN:Icon_Handhold_086",
shareOffset = {
-5,
0
},
previewShareOffset = v7
},
[640144] = {
id = 640144,
effect = true,
name = "奇幻蘑菇",
desc = "小心，吃了可能会找不到回家的路",
icon = "CDN:Icon_Handhold_080",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_080",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"一口入幻，三口通关"
},
beginTime = v6,
suitId = 40077,
suitName = "奇幻蘑菇",
suitIcon = "CDN:Icon_Handhold_080",
shareOffset = {
-8,
15
},
previewShareOffset = {
15,
-10
}
},
[640145] = {
id = 640145,
effect = true,
name = "奇幻蘑菇",
desc = "小心，吃了可能会找不到回家的路",
icon = "CDN:Icon_Handhold_080_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640144,
fashionValue = 25,
belongToGroup = {
640145,
640146
}
},
resourceConf = {
model = "SK_Handhold_080",
material = "MI_Handhold_080_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72139,
scaleTimes = 90,
shareTexts = {
"一口入幻，三口通关"
},
shareOffset = {
-8,
15
},
previewShareOffset = {
15,
-10
}
},
[640146] = {
id = 640146,
effect = true,
name = "奇幻蘑菇",
desc = "小心，吃了可能会找不到回家的路",
icon = "CDN:Icon_Handhold_080_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640144,
fashionValue = 25,
belongToGroup = {
640145,
640146
}
},
resourceConf = {
model = "SK_Handhold_080",
material = "MI_Handhold_080_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72140,
scaleTimes = 90,
shareTexts = {
"一口入幻，三口通关"
},
shareOffset = {
-8,
15
},
previewShareOffset = {
15,
-10
}
},
[640147] = {
id = 640147,
effect = true,
name = "菠萝小桨",
desc = "迎风破浪，小桨解决一切难题",
icon = "CDN:Icon_Handhold_078",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_078",
modelType = 2,
IconLabelId = 102
},
scaleTimes = 90,
shareTexts = {
"要和我一起划船吗？"
},
beginTime = v6,
suitId = 40078,
suitName = "菠萝小桨",
suitIcon = "CDN:Icon_Handhold_078",
shareOffset = {
-3,
-20
},
previewShareOffset = v7
},
[640148] = {
id = 640148,
effect = true,
name = "妙蛙吹风机",
desc = "这大嘴，也太能吹了",
icon = "CDN:Icon_Handhold_081",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_081",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"不吹牛，只吹风"
},
beginTime = v6,
suitId = 40079,
suitName = "妙蛙吹风机",
suitIcon = "CDN:Icon_Handhold_081",
shareOffset = {
-7,
10
},
previewShareOffset = v7
},
[640149] = {
id = 640149,
effect = true,
name = "妙蛙吹风机",
desc = "这大嘴，也太能吹了",
icon = "CDN:Icon_Handhold_081_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640148,
fashionValue = 25,
belongToGroup = {
640149,
640150
}
},
resourceConf = {
model = "SK_Handhold_081",
material = "MI_Handhold_081_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72143,
scaleTimes = 90,
shareTexts = {
"不吹牛，只吹风"
},
shareOffset = {
-7,
10
},
previewShareOffset = v7
},
[640150] = {
id = 640150,
effect = true,
name = "妙蛙吹风机",
desc = "这大嘴，也太能吹了",
icon = "CDN:Icon_Handhold_081_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640148,
fashionValue = 25,
belongToGroup = {
640149,
640150
}
},
resourceConf = {
model = "SK_Handhold_081",
material = "MI_Handhold_081_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72144,
scaleTimes = 90,
shareTexts = {
"不吹牛，只吹风"
},
shareOffset = {
-7,
10
},
previewShareOffset = v7
},
[640151] = {
id = 640151,
effect = true,
name = "三角奶酪",
desc = "世上最巧妙的美食陷阱",
icon = "CDN:Icon_Handhold_079",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_079",
modelType = 2,
IconLabelId = 102
},
scaleTimes = 90,
shareTexts = {
"让我看看，这是谁的小奶酪"
},
beginTime = v6,
suitId = 40080,
suitName = "三角奶酪",
suitIcon = "CDN:Icon_Handhold_079",
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640152] = {
id = 640152,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星钥秘杖",
desc = "每个瞬间，都是奇迹的开始",
icon = "CDN:Icon_Handhold_085",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_085",
modelType = 2
},
shareTexts = {
"要相信魔法哦"
},
beginTime = {
seconds = 1739721600
},
suitId = 40081,
suitName = "星钥秘杖",
suitIcon = "CDN:Icon_Handhold_085",
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640153] = {
id = 640153,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星钥秘杖",
desc = "每个瞬间，都是奇迹的开始",
icon = "CDN:Icon_Handhold_085_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640152,
fashionValue = 35,
belongToGroup = {
640153,
640154
}
},
resourceConf = {
model = "SK_Handhold_085",
material = "MI_Handhold_085_1_HP01;MI_Handhold_085_2_HP01;MI_Handhold_085_3_HP01",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72147,
shareTexts = {
"要相信魔法哦"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640154] = {
id = 640154,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星钥秘杖",
desc = "每个瞬间，都是奇迹的开始",
icon = "CDN:Icon_Handhold_085_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640152,
fashionValue = 35,
belongToGroup = {
640153,
640154
}
},
resourceConf = {
model = "SK_Handhold_085",
material = "MI_Handhold_085_1_HP02;MI_Handhold_085_2_HP02;MI_Handhold_085_3_HP02",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72148,
shareTexts = {
"要相信魔法哦"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640155] = {
id = 640155,
effect = true,
name = "甜蜜鲷鱼烧",
desc = "遇到甜蜜的诱惑，就接受吧",
icon = "CDN:Icon_Handhold_087",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_087",
modelType = 2
},
commodityId = 72149,
scaleTimes = 90,
shareTexts = {
"想看见的，只是你的笑容"
},
beginTime = v6,
suitId = 40082,
suitName = "甜蜜鲷鱼烧",
suitIcon = "CDN:Icon_Handhold_087",
shareOffset = {
-10,
29
},
previewShareOffset = {
0,
0
}
},
[640156] = {
id = 640156,
effect = true,
name = "樱语咖啡",
desc = "某日午后，一杯樱花图案的美味咖啡",
icon = "CDN:Icon_Handhold_082",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_082",
modelType = 2,
IconLabelId = 102
},
commodityId = 72153,
scaleTimes = 90,
shareTexts = {
"要在奶油融化前饮用哦"
},
beginTime = v6,
suitId = 40083,
suitName = "樱语咖啡",
suitIcon = "CDN:Icon_Handhold_082",
shareOffset = {
-30,
20
},
previewShareOffset = v7
},
[640157] = {
id = 640157,
effect = true,
name = "兔宝糖葫芦",
desc = "甜甜的兔兔，吃甜甜的糖葫芦~",
icon = "CDN:Icon_Handhold_083",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_083",
modelType = 2,
IconLabelId = 102
},
commodityId = 72154,
scaleTimes = 90,
shareTexts = {
"你一颗，我一颗，一起吃才最快乐！"
},
beginTime = v6,
suitId = 40084,
suitName = "兔宝糖葫芦",
suitIcon = "CDN:Icon_Handhold_083",
shareOffset = {
-5,
-15
},
previewShareOffset = v7
},
[640158] = {
id = 640158,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 60
}
},
quality = 1,
name = "猫耳电音台",
desc = "聆听来自猫咪世界的声音",
icon = "CDN:Icon_Handhold_084",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 600
},
resourceConf = {
model = "SK_Handhold_084",
modelType = 2,
IconLabelId = 101
},
shareTexts = {
"猫猫点亮你的音乐梦想"
},
beginTime = {
seconds = 1739721600
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_640158.astc",
suitId = 40085,
suitName = "猫耳电音台",
suitIcon = "CDN:Icon_Handhold_084",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_640158.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_640158.astc",
shareOffset = {
10,
-15
},
previewShareOffset = {
25,
-35
},
bEnableDoubleShow = true,
customPreviewConf = {
BP = "BP_CustomPreview_HandHold_084",
Scale = 0.2199999988079071,
CharIdleAnim = "AS_CH_Handhold_084_Extraeffect_001_DJPlay;AS_CH_Handhold_084_Extraeffect_001_DrumPlay;AS_CH_Handhold_084_Extraeffect_001_GuitarPlay;AS_CH_Handhold_084_Extraeffect_001_BassPlay",
CharOffset = {
0,
0,
0
},
CharRotateYaw = 0
},
previewSocketNames = {
"Seat_DJ",
"Seat_Drum",
"Seat_Guitar",
"Seat_Bass"
}
},
[640160] = {
id = 640160,
effect = true,
name = "自拍神喵",
desc = "小爪轻轻一按，定格美丽瞬间",
icon = "CDN:Icon_Handhold_090",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_090",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"快和我一起拍照吧喵喵喵"
},
beginTime = v6,
suitId = 40087,
suitName = "自拍神喵",
suitIcon = "CDN:Icon_Handhold_090",
shareOffset = {
-5,
25
},
previewShareOffset = {
15,
-10
}
},
[640161] = {
id = 640161,
effect = true,
name = "自拍神喵",
desc = "小爪轻轻一按，定格美丽瞬间",
icon = "CDN:Icon_Handhold_090_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640160,
fashionValue = 125,
belongToGroup = {
640161,
640162
}
},
resourceConf = {
model = "SK_Handhold_090",
material = "MI_Handhold_090_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72156,
scaleTimes = 90,
shareTexts = {
"快和我一起拍照吧喵喵喵"
},
beginTime = v6,
shareOffset = {
-5,
25
},
previewShareOffset = {
15,
-10
}
},
[640162] = {
id = 640162,
effect = true,
name = "自拍神喵",
desc = "小爪轻轻一按，定格美丽瞬间",
icon = "CDN:Icon_Handhold_090_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640160,
fashionValue = 125,
belongToGroup = {
640161,
640162
}
},
resourceConf = {
model = "SK_Handhold_090",
material = "MI_Handhold_090_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72157,
scaleTimes = 90,
shareTexts = {
"快和我一起拍照吧喵喵喵"
},
beginTime = v6,
shareOffset = {
-5,
25
},
previewShareOffset = {
15,
-10
}
},
[640163] = {
id = 640163,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 100
}
},
quality = 1,
name = "绮梦独角兽",
desc = "云端藏着独角兽的秘密，轻摇出现彩虹",
icon = "CDN:Icon_Handhold_091",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_091",
modelType = 2
},
shareTexts = {
"是彩虹云絮编织的童话"
},
beginTime = {
seconds = 1739721600
},
suitId = 40086,
suitName = "绮梦独角兽",
suitIcon = "CDN:Icon_Handhold_091",
shareOffset = {
0,
0
},
previewShareOffset = v7
},
[640164] = {
id = 640164,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "绮梦独角兽",
desc = "云端藏着独角兽的秘密，轻摇出现彩虹",
icon = "CDN:Icon_Handhold_091_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640163,
fashionValue = 35,
belongToGroup = {
640164,
640165
}
},
resourceConf = {
model = "SK_Handhold_091",
material = "MI_Handhold_091_1_HP01",
modelType = 2,
materialSlot = "Handhold_1"
},
commodityId = 72159,
shareTexts = {
"是彩虹云絮编织的童话"
},
shareOffset = {
0,
0
},
previewShareOffset = v7
},
[640165] = {
id = 640165,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "绮梦独角兽",
desc = "云端藏着独角兽的秘密，轻摇出现彩虹",
icon = "CDN:Icon_Handhold_091_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640163,
fashionValue = 35,
belongToGroup = {
640164,
640165
}
},
resourceConf = {
model = "SK_Handhold_091",
material = "MI_Handhold_091_1_HP02",
modelType = 2,
materialSlot = "Handhold_1"
},
commodityId = 72160,
shareTexts = {
"是彩虹云絮编织的童话"
},
shareOffset = {
0,
0
},
previewShareOffset = v7
},
[640166] = {
id = 640166,
effect = true,
name = "彩虹波板糖",
desc = "戳戳，舔舔，快乐如此简单",
icon = "CDN:Icon_Handhold_092",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_092",
modelType = 2
},
scaleTimes = 90,
shareTexts = {
"糖果诱惑，谁能抵抗"
},
beginTime = v6,
suitId = 40088,
suitName = "彩虹波板糖",
suitIcon = "CDN:Icon_Handhold_092",
shareOffset = {
-5,
25
},
previewShareOffset = {
15,
-10
}
},
[640167] = {
id = 640167,
effect = true,
name = "彩虹波板糖",
desc = "戳戳，舔舔，快乐如此简单",
icon = "CDN:Icon_Handhold_092_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640166,
fashionValue = 125,
belongToGroup = {
640167,
640168
}
},
resourceConf = {
model = "SK_Handhold_092",
material = "MI_Handhold_092_HP01",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72162,
scaleTimes = 90,
shareTexts = {
"糖果诱惑，谁能抵抗"
},
beginTime = v6,
shareOffset = {
-5,
25
},
previewShareOffset = {
15,
-10
}
},
[640168] = {
id = 640168,
effect = true,
name = "彩虹波板糖",
desc = "戳戳，舔舔，快乐如此简单",
icon = "CDN:Icon_Handhold_092_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640166,
fashionValue = 125,
belongToGroup = {
640167,
640168
}
},
resourceConf = {
model = "SK_Handhold_092",
material = "MI_Handhold_092_HP02",
modelType = 2,
materialSlot = "Handhold"
},
commodityId = 72163,
scaleTimes = 90,
shareTexts = {
"糖果诱惑，谁能抵抗"
},
beginTime = v6,
shareOffset = {
-5,
25
},
previewShareOffset = {
15,
-10
}
},
[640169] = {
id = 640169,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 60
}
},
quality = 1,
name = "猫耳电音台【供卡池展示】",
desc = "聆听来自猫咪世界的声音",
icon = "CDN:Icon_Handhold_084",
resourceConf = {
model = "SK_Handhold_084_Extraeffect_001",
modelType = 2,
idleAnim = "AS_CH_Handhold_084_Extraeffect_002_Idle"
},
shareTexts = {
"猫猫点亮你的音乐梦想"
},
shareOffset = {
0,
0
},
previewShareOffset = {
25,
-35
},
customPreviewConf = {
BP = "BP_HandHold_Gacha_MusicConcert",
Scale = 0.5,
CharIdleAnim = "AS_CH_Handhold_084_Extraeffect_001_DJPlay",
CharOffset = {
0,
0,
0
},
CharRotateYaw = 0
},
previewSocketNames = {
"Seat_DJ"
}
},
[640170] = {
id = 640170,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "灵枝桂母",
desc = "一盏萤萤灯，徜徉波涛间",
icon = "CDN:Icon_Handhold_088",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_088",
modelType = 2
},
shareTexts = {
"水母也会吐泡泡~"
},
beginTime = {
seconds = 1739721600
},
suitId = 40089,
suitName = "灵枝桂母",
suitIcon = "CDN:Icon_Handhold_088",
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640171] = {
id = 640171,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "灵枝桂母",
desc = "一盏萤萤灯，徜徉波涛间",
icon = "CDN:Icon_Handhold_088_01",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640170,
fashionValue = 35,
belongToGroup = {
640171,
640172
}
},
resourceConf = {
model = "SK_Handhold_088",
material = "MI_Handhold_088_1_HP01;MI_Handhold_088_2_HP01;MI_Handhold_088_3_HP01",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72165,
shareTexts = {
"水母也会吐泡泡~"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640172] = {
id = 640172,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "灵枝桂母",
desc = "一盏萤萤灯，徜徉波涛间",
icon = "CDN:Icon_Handhold_088_02",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
belongTo = 640170,
fashionValue = 35,
belongToGroup = {
640171,
640172
}
},
resourceConf = {
model = "SK_Handhold_088",
material = "MI_Handhold_088_1_HP02;MI_Handhold_088_2_HP02;MI_Handhold_088_3_HP02",
modelType = 2,
materialSlot = "Handhold_1;Handhold_2;Handhold_3"
},
commodityId = 72166,
shareTexts = {
"水母也会吐泡泡~"
},
shareOffset = {
-12,
0
},
previewShareOffset = v7
},
[640173] = {
id = 640173,
effect = true,
name = "桂影轻舞",
desc = "爱，让我们忘记恐惧",
icon = "CDN:Icon_Handhold_097",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = v4,
resourceConf = {
model = "SK_Handhold_097",
modelType = 2,
IconLabelId = 102
},
scaleTimes = 70,
shareTexts = {
"结局，要由自己书写"
},
beginTime = v6,
suitId = 40090,
suitName = "桂影轻舞",
suitIcon = "CDN:Icon_Handhold_097",
shareOffset = {
-8,
-30
},
previewShareOffset = {
0,
-50
}
},
[640174] = {
id = 640174,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 100
}
},
quality = 1,
name = "开伞测试",
desc = "云端藏着独角兽的秘密，轻摇出现彩虹",
icon = "CDN:Icon_Handhold_091",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_091",
modelType = 2
},
shareTexts = {
"是彩虹云絮编织的童话"
},
beginTime = {
seconds = 1739721600
},
suitId = 40086,
suitName = "绮梦独角兽",
suitIcon = "CDN:Icon_Handhold_091",
shareOffset = {
0,
0
},
previewShareOffset = v7,
bEnableDoubleShow = true,
placeholder_106 = 721012,
previewSocketNames = {
"LEFT_01",
"RIGHT_01"
}
},
[640175] = {
id = 640175,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "关伞测试",
desc = "一盏萤萤灯，徜徉波涛间",
icon = "CDN:Icon_Handhold_088",
getWay = v1,
jumpId = v2,
useType = v3,
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Handhold_088",
modelType = 2
},
shareTexts = {
"水母也会吐泡泡~"
},
beginTime = {
seconds = 1739721600
},
suitId = 40089,
suitName = "灵枝桂母",
suitIcon = "CDN:Icon_Handhold_088",
shareOffset = {
-12,
0
},
previewShareOffset = v7
}
}

local mt = {
effect = false,
type = "ItemType_HandOrnament",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
scaleTimes = 100,
bEnableDoubleShow = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data