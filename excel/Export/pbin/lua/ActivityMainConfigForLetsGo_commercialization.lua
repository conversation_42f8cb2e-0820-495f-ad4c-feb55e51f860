--com.tencent.wea.xlsRes.table_ActivityMainConfig => excel/xls/H_活动中心配置_商业化.xlsx: 活动配置

local data = {
[10030] = {
id = 10030,
activityType = "ATSuperCoreRanking",
timeInfo = {
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1733328000
},
showEndTime = {
seconds = 1733328000
},
showBeginTime = {
seconds = 1730736000
}
},
labelId = 1,
backgroundUrl = {
"chaohepaihangbang.astc"
},
jumpId = 627,
activityName = "超核排行榜",
activityUIDetail = "UI_CommonJumpActivity_View",
tagId = 1,
showInCenter = true,
activityNameType = "ANSuperCoreRanking",
platforms = {
1,
2,
3,
4
}
},
[10017] = {
id = 10017,
activityType = "ATGroupReturning",
timeInfo = {
beginTime = {
seconds = 1735920000
},
endTime = {
seconds = 1737215999
},
showEndTime = {
seconds = 1737215999
},
showBeginTime = {
seconds = 1735920000
}
},
labelId = 9,
lowVersion = "1.3.37.78",
activityName = "拼团享好礼",
activityParam = {
5,
263
},
activityUIDetail = "UI_Activity_GroupReturning_MainView",
isInBottom = 1,
activityRuleId = 219,
tagId = 4,
showInCenter = true,
activityNameType = "ANTGroupReturning",
activityShopType = {
142
},
titleType = 0,
activitySubName = "组队拿返利"
},
[10044] = {
id = 10044,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1740758399
},
showEndTime = {
seconds = 1740758399
},
showBeginTime = {
seconds = 1737993600
}
},
labelId = 2,
backgroundUrl = {
"T_Activity_Bg_zxlsj.astc"
},
jumpId = 564,
lowVersion = "1.3.68.52",
activityName = "尊享利是节",
activityUIDetail = "UI_CommonJumpActivity_View",
tagId = 4,
showInCenter = true,
activityNameType = "ANTGuide",
titleType = 0,
activitySubName = "百万红包雨",
platforms = {
1,
2,
3,
4
}
},
[10045] = {
id = 10045,
activityType = "ATGuide",
timeInfo = {
beginTime = {
seconds = 1737388800
},
endTime = {
seconds = 1737993599
},
showEndTime = {
seconds = 1737993599
},
showBeginTime = {
seconds = 1737388800
}
},
labelId = 3,
backgroundUrl = {
"T_Activity_Bg_zxlsj.astc"
},
jumpId = 1077,
lowVersion = "1.3.68.52",
activityName = "尊享利是节2",
activityUIDetail = "UI_CommonJumpActivity_View",
tagId = 4,
showInCenter = true,
activityNameType = "ANTGuide",
titleType = 0,
activitySubName = "百万红包雨",
platforms = {
1,
2,
3,
4
}
},
[10062] = {
id = 10062,
activityType = "ATSuperCoreRanking",
timeInfo = {
beginTime = {
seconds = 1746547200
},
endTime = {
seconds = 1749743999
},
showEndTime = {
seconds = 1749743999
},
showBeginTime = {
seconds = 1746547200
}
},
labelId = 1,
backgroundUrl = {
"chaohepaihangbang.astc"
},
jumpId = 627,
activityName = "超核排行榜",
activityUIDetail = "UI_CommonJumpActivity_View",
tagId = 1,
showInCenter = true,
activityNameType = "ANSuperCoreRanking",
platforms = {
1,
2,
3,
4
}
},
[10088] = {
id = 10088,
activityType = "ATOneDollarRaffle",
timeInfo = {
beginTime = {
seconds = 1748880000
},
endTime = {
seconds = 1753891199
},
showEndTime = {
seconds = 1767110399
},
showBeginTime = {
seconds = 1748880000
}
},
labelId = 3,
backgroundUrl = {
"zzw1129.astc"
},
activityName = "暑期特惠",
activityParam = {
13,
5,
240
},
activityUIDetail = "UI_Activity_FarmLogin_MainView",
tagId = 4,
showInCenter = true,
activityNameType = "ANTOneDollarRaffle",
activityShopType = {
198
},
titleType = 0,
activitySubName = "暑期特惠",
currencyCfg = {
3639
},
platforms = {
2,
4,
5,
6
}
}
}

local mt = {
showInCenter = false,
slapFace = false,
isHideMainBackground = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data