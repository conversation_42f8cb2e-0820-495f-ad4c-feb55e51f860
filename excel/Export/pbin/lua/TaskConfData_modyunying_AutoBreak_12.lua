--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_特色玩法运营.xlsx: 活动任务

local data = {
[651467] = {
id = 651467,
name = "5月26日起登录游戏",
desc = "5月26日起登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
290025,
3544
},
numList = {
1,
100
},
validPeriodList = {
0,
0
}
},
taskGroupId = 1001497
},
[651468] = {
id = 651468,
name = "狼人幸运对局限时返场，去看看→",
desc = "狼人幸运对局限时返场，去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
310
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001498
},
[651469] = {
id = 651469,
name = "完成5次谁是狼人对局",
desc = "完成5次谁是狼人对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
200101
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskGroupId = 1001498
},
[651470] = {
id = 651470,
name = "【每周】观看1次赛事",
desc = "【每周】观看1次赛事",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
517
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 517,
taskGroupId = 1001501
},
[651471] = {
id = 651471,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
1
},
validPeriodList = {
3
}
},
taskGroupId = 1001500
},
[651472] = {
id = 651472,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 30,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
200102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001500
},
[651473] = {
id = 651473,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 50,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 1001500
},
[651474] = {
id = 651474,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 70,
subConditionList = {
{
type = 3,
value = {
3545
}
}
}
}
}
}
},
reward = {
itemIdList = {
200102
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1001500
},
[651475] = {
id = 651475,
name = "观赛有好礼-预约",
desc = "观赛有好礼-预约",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
651475
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = -1,
taskGroupId = 1001502
},
[651476] = {
id = 651476,
name = "观赛有好礼-分享",
desc = "观赛有好礼-分享",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10050
}
}
}
}
}
}
},
reward = {
itemIdList = {
13
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 10050,
taskGroupId = 1001503
},
[651480] = {
id = 651480,
name = "完成1局谁是狼人模式",
desc = "完成1局谁是狼人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskParam = {
1
},
taskGroupId = 1001505
},
[651481] = {
id = 651481,
name = "与任意队友组队双人模式",
desc = "与任意队友组队双人模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 617,
value = 1,
subConditionList = {
{
type = 2,
value = {
109
}
}
}
}
}
}
},
reward = {
itemIdList = {
3545
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 310,
taskParam = {
1
},
taskGroupId = 1001505
},
[651601] = {
id = 651601,
name = "【每日】完成1次大王别抓我对局",
desc = "【每日】完成1次大王别抓我对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
3777
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023001
},
[651602] = {
id = 651602,
name = "【每日】搜寻5台星路仪",
desc = "【每日】搜寻5台星路仪",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 819,
value = 5,
subConditionList = {
{
type = 203,
value = {
282
}
}
}
}
}
}
},
reward = {
itemIdList = {
3777
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023001
},
[651603] = {
id = 651603,
name = "【每周】累计解救10个星宝",
desc = "【每周】累计解救10个星宝",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 819,
value = 10,
subConditionList = {
{
type = 203,
value = {
98
}
}
}
}
}
}
},
reward = {
itemIdList = {
3777
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023002
},
[651604] = {
id = 651604,
name = "【每周】完成10次大王别抓我对局",
desc = "【每周】完成10次大王别抓我对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 10,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
3777
},
numList = {
15
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023002
},
[651605] = {
id = 651605,
name = "【累计】累计搜寻20台星路仪",
desc = "【累计】累计搜寻20台星路仪",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 819,
value = 20,
subConditionList = {
{
type = 203,
value = {
282
}
}
}
}
}
}
},
reward = {
itemIdList = {
3777
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023003
},
[651606] = {
id = 651606,
name = "【累计】完成15次大王别抓我对局",
desc = "【累计】完成15次大王别抓我对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 15,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
3777
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023003
},
[651607] = {
id = 651607,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 5,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651608] = {
id = 651608,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651609] = {
id = 651609,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 50,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651610] = {
id = 651610,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 100,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
203006
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651611] = {
id = 651611,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 120,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651612] = {
id = 651612,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 140,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
3201041
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651613] = {
id = 651613,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 160,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
15
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651614] = {
id = 651614,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 180,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
200006
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651615] = {
id = 651615,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 200,
subConditionList = {
{
type = 3,
value = {
3777
}
}
}
}
}
}
},
reward = {
itemIdList = {
3202012
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023004
},
[651616] = {
id = 651616,
name = "天天领升至4星",
desc = "天天领升至4星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 616,
value = 1
}
}
}
},
reward = {
itemIdList = {
310343
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1005301
},
[651631] = {
id = 651631,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 1
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651632] = {
id = 651632,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 2
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651633] = {
id = 651633,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 3
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651634] = {
id = 651634,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 4
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651635] = {
id = 651635,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 5
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651636] = {
id = 651636,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 6
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651637] = {
id = 651637,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 7
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651638] = {
id = 651638,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 8
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651639] = {
id = 651639,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 9
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651640] = {
id = 651640,
name = "答对当日题目",
desc = "答对当日题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 10
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1023011
},
[651641] = {
id = 651641,
name = "【每周】搜寻10台星路仪",
desc = "【每周】搜寻10台星路仪",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 819,
value = 10,
jumpId = 203,
desc = "282"
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023012
},
[651642] = {
id = 651642,
name = "【每周】累计解救10个星宝",
desc = "【每周】累计解救10个星宝",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 819,
value = 10,
subConditionList = {
{
type = 203,
value = {
98
}
}
}
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023012
},
[651643] = {
id = 651643,
name = "【每周】游玩1次【强哥别抓我】",
desc = "【每周】游玩1次【强哥别抓我】",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425218729612
}
}
}
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 83,
taskGroupId = 1023012
},
[651644] = {
id = 651644,
name = "【累计】完成10次大王别抓我对局",
desc = "【累计】完成10次大王别抓我对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 10,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353,
354
}
}
}
}
}
}
},
reward = {
itemIdList = {
3788
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 1154,
taskGroupId = 1023013
},
[651645] = {
id = 651645,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 10,
subConditionList = {
{
type = 3,
value = {
3788
}
}
}
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023014
},
[651646] = {
id = 651646,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 40,
subConditionList = {
{
type = 3,
value = {
3788
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 1023014
},
[651647] = {
id = 651647,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 80,
subConditionList = {
{
type = 3,
value = {
3788
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023014
},
[651648] = {
id = 651648,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 120,
subConditionList = {
{
type = 3,
value = {
3788
}
}
}
}
}
}
},
reward = {
itemIdList = {
290023
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023014
},
[651649] = {
id = 651649,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 150,
subConditionList = {
{
type = 3,
value = {
3788
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023014
},
[651650] = {
id = 651650,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 160,
subConditionList = {
{
type = 3,
value = {
3788
}
}
}
}
}
}
},
reward = {
itemIdList = {
3201231
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1023014
},
[659368] = {
id = 659368,
name = "珍馐百味卡池上线！去看看→",
desc = "珍馐百味卡池上线！去看看→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 71,
value = 1,
subConditionList = {
{
type = 80,
value = {
586
}
}
}
}
}
}
},
reward = {
itemIdList = {
630553
},
numList = {
1
},
validPeriodList = {
1
}
},
jumpId = 586,
taskGroupId = 1022129
},
[659369] = {
id = 659369,
name = "【每日】在星宝农场加工1次",
desc = "【每日】在星宝农场加工1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 238,
value = 1
}
}
}
},
reward = {
itemIdList = {
317142
},
numList = {
1
}
},
jumpId = 5103,
taskGroupId = 1022130
},
[659370] = {
id = 659370,
name = "【每日】在好友农场祈福5次",
desc = "【每日】在好友农场祈福5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 5
}
}
}
},
reward = {
itemIdList = {
317142
},
numList = {
1
}
},
jumpId = 5102,
taskGroupId = 1022130
},
[659371] = {
id = 659371,
name = "【每日】在好友农场拿取5次农作物",
desc = "【每日】在好友农场拿取5次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 5,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317142
},
numList = {
1
}
},
jumpId = 5102,
taskGroupId = 1022130
},
[659372] = {
id = 659372,
name = "【每日】浇水15次",
desc = "【每日】浇水15次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 156,
value = 15,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317142
},
numList = {
1
}
},
jumpId = 5100,
taskGroupId = 1022130
},
[659373] = {
id = 659373,
name = "【每日】在农场收获10次作物",
desc = "【每日】在农场收获10次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 10,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317143
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022131
},
[659374] = {
id = 659374,
name = "【每日】在好友农场拿取5次作物",
desc = "【每日】在好友农场拿取5次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 5,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317143
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022131
},
[659375] = {
id = 659375,
name = "【每周】游玩2次任意模式",
desc = "【每周】游玩2次任意模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1
}
}
}
},
reward = {
itemIdList = {
317143
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022132
},
[659376] = {
id = 659376,
name = "【每周】游玩【元梦厨房-双人对决】1次",
desc = "【每周】游玩【元梦厨房-双人对决】1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204946721
}
}
}
}
}
}
},
reward = {
itemIdList = {
317143
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022132
},
[659379] = {
id = 659379,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 615,
value = 10
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022134
},
[659380] = {
id = 659380,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 615,
value = 30
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 1022134
},
[659381] = {
id = 659381,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 615,
value = 60
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022134
},
[659382] = {
id = 659382,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 615,
value = 100
}
}
}
},
reward = {
itemIdList = {
290023
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022134
},
[659383] = {
id = 659383,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 615,
value = 150
}
}
}
},
reward = {
itemIdList = {
630621
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022134
},
[659384] = {
id = 659384,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 615,
value = 200
}
}
}
},
reward = {
itemIdList = {
200104
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022134
},
[659385] = {
id = 659385,
name = "【累计】在星宝农场钓鱼100次",
desc = "【累计】在星宝农场钓鱼100次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 904,
value = 1
}
}
}
},
reward = {
itemIdList = {
317141
},
numList = {
3
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022121
},
[659386] = {
id = 659386,
name = "【每周】在星宝农场中收获10次",
desc = "【每周】在星宝农场中收获10次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 10,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022135
},
[659387] = {
id = 659387,
name = "【每周】在星宝农场中钓鱼60次",
desc = "【每周】在星宝农场中钓鱼60次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 209,
value = 60
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022135
},
[659388] = {
id = 659388,
name = "【每周】游玩1次【桃园生活：种田人生】",
desc = "【每周】游玩1次【桃园生活：种田人生】",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204946721
}
}
}
}
}
}
},
reward = {
itemIdList = {
3544
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022135
},
[659389] = {
id = 659389,
name = "【累计】去好友农场捕鱼成功3次",
desc = "【累计】去好友农场捕鱼成功3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 211,
value = 3
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
4
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022136
},
[659390] = {
id = 659390,
name = "【累计】获得5条S及以上品质鱼",
desc = "【累计】获得5条S及以上品质鱼",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 214,
value = 5,
subConditionList = {
{
type = 173,
value = {
5,
6
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
4
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022136
},
[659391] = {
id = 659391,
name = "【累计】在钓鱼时触发10次幸运钩",
desc = "【累计】在钓鱼时触发10次幸运钩",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 215,
value = 10
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
4
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022136
},
[659392] = {
id = 659392,
name = "分享鱼塘幸运星第四期",
desc = "分享鱼塘幸运星第四期",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
659392
}
}
}
}
}
}
},
reward = {
itemIdList = {
219000
},
numList = {
2
},
validPeriodList = {
0
}
},
taskGroupId = 1022137
},
[659393] = {
id = 659393,
name = "【每日】登录游戏1次",
desc = "【每日】登录游戏1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
331032
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1022119
},
[659394] = {
id = 659394,
name = "在星宝农场收获100次农作物",
desc = "在星宝农场收获100次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 100,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022138
},
[659395] = {
id = 659395,
name = "在好友农场祈福40次",
desc = "在好友农场祈福40次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 40
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022138
},
[659396] = {
id = 659396,
name = "在星宝农场加工20次",
desc = "在星宝农场加工20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 238,
value = 20
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5103,
taskGroupId = 1022138
},
[659397] = {
id = 659397,
name = "通过泡温泉获得3次增益",
desc = "通过泡温泉获得3次增益",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 900,
value = 3,
subConditionList = {
{
type = 600,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022138
},
[659398] = {
id = 659398,
name = "在星宝农场浇水100次",
desc = "在星宝农场浇水100次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 156,
value = 100,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022139
},
[659399] = {
id = 659399,
name = "在好友农场拿取40次作物",
desc = "在好友农场拿取40次作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 173,
value = 40,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022139
},
[659400] = {
id = 659400,
name = "在星宝农场获得30次农作物的丰收",
desc = "在星宝农场获得30次农作物的丰收",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 30,
subConditionList = {
{
type = 157,
value = {
3
}
},
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5100,
taskGroupId = 1022139
},
[659401] = {
id = 659401,
name = "在星宝农场给好友赠礼3次",
desc = "在星宝农场给好友赠礼3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 257,
value = 10
}
}
}
},
reward = {
itemIdList = {
317160
},
numList = {
200
},
validPeriodList = {
0
}
},
jumpId = 5102,
taskGroupId = 1022139
},
[780910] = {
id = 780910,
name = "查看视频",
desc = "查看视频",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 320,
value = 1,
subConditionList = {
{
type = 330,
value = {
3626
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1078458
},
[780911] = {
id = 780911,
name = "答对所有题目",
desc = "答对所有题目",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 602,
value = 1,
subConditionList = {
{
type = 248,
value = {
15
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
5
},
validPeriodList = {
0
}
},
taskGroupId = 1078460
},
[780912] = {
id = 780912,
name = "免费体验3天晴霜（自动使用）",
desc = "免费体验3天晴霜（自动使用）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
301232
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078459
},
[780913] = {
id = 780913,
name = "【晴霜】完成1局峡谷5v5",
desc = "【晴霜】完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 46,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 50,
value = {
300,
1,
1047
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078459
},
[780914] = {
id = 780914,
name = "【每日】完成1局峡谷5v5模式",
desc = "【每日】完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
3625
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078461
},
[780915] = {
id = 780915,
name = "组队完成1局峡谷5v5",
desc = "组队完成1局峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
},
{
type = 88,
value = {
0
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078462
},
[780916] = {
id = 780916,
name = "完成1局峡谷5v5模式",
desc = "完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078462
},
[780917] = {
id = 780917,
name = "完成3局峡谷5v5模式",
desc = "完成3局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078462
},
[780918] = {
id = 780918,
name = "完成5局峡谷5v5模式",
desc = "完成5局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
329936
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078462
},
[780919] = {
id = 780919,
name = "消耗幻梦币5个",
desc = "消耗幻梦币5个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 104,
value = 5,
subConditionList = {
{
type = 3,
value = {
14
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 1078463
},
[780925] = {
id = 780925,
name = "【每日】游玩1局峡谷5v5 /游玩3局任意模式",
desc = "【每日】游玩1局峡谷5v5 /游玩3局任意模式",
condition = {
resCompleteConditionGroup = {
conditionRelation = "ConditionRelation_Or",
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 1,
value = {
6101,
6102
}
}
},
jumpId = 50112,
desc = "【每日】完成1局峡谷5v5模式"
},
{
conditionType = 4,
value = 3,
jumpId = 4,
desc = "【每日】完成3局任意模式"
}
}
}
},
reward = {
itemIdList = {
3803
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 1078465
},
[780926] = {
id = 780926,
name = "【每周】累计登录2天",
desc = "【每周】累计登录2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
3803
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 1078466
},
[780927] = {
id = 780927,
name = "【每周】累计登录4天",
desc = "【每周】累计登录4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 4
}
}
}
},
reward = {
itemIdList = {
3803
},
numList = {
30
},
validPeriodList = {
0
}
},
taskGroupId = 1078466
},
[780928] = {
id = 780928,
name = "【累计】消耗10枚幸运币",
desc = "【累计】消耗10枚幸运币",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 104,
value = 10,
subConditionList = {
{
type = 3,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
3803
},
numList = {
30
},
validPeriodList = {
0
}
},
jumpId = 88888,
taskGroupId = 1078467
},
[780929] = {
id = 780929,
name = "【累计】消耗30枚幸运币",
desc = "【累计】消耗30枚幸运币",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 104,
value = 30,
subConditionList = {
{
type = 3,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
3803
},
numList = {
50
},
validPeriodList = {
0
}
},
jumpId = 88888,
taskGroupId = 1078467
},
[780930] = {
id = 780930,
name = "【累计】消耗60枚幸运币",
desc = "【累计】消耗60枚幸运币",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 104,
value = 60,
subConditionList = {
{
type = 3,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
3803
},
numList = {
80
},
validPeriodList = {
0
}
},
jumpId = 88888,
taskGroupId = 1078467
},
[780931] = {
id = 780931,
name = "【累计】游玩1次【峡谷擂台赛】",
desc = "【累计】游玩1次【峡谷擂台赛】",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 76,
value = 1,
subConditionList = {
{
type = 90,
value = {
50844425204946721
}
}
}
}
}
}
},
reward = {
itemIdList = {
3803
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 83
},
[780950] = {
id = 780950,
name = "完成1局峡谷5v5模式",
desc = "完成1局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
300101
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078900
},
[780951] = {
id = 780951,
name = "完成2局峡谷5v5模式",
desc = "完成2局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
300101
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078900
},
[780952] = {
id = 780952,
name = "完成3局峡谷5v5模式",
desc = "完成3局峡谷5v5模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 3,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
300101
},
numList = {
1
},
validPeriodList = {
0
}
},
jumpId = 50112,
taskGroupId = 1078900
}
}

local mt = {
name = "兑换",
desc = "兑换"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data