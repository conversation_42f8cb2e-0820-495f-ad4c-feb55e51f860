--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_策划专用.xlsx: 活动任务

local data = {
[440539] = {
id = 440539,
name = "泡泡大战达到黄金段位",
desc = "泡泡大战达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
13
}
},
{
type = 154,
value = {
130006
}
}
}
}
}
}
},
reward = {
itemIdList = {
860090,
840154
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1741881599
},
{
seconds = 1741881599
}
}
},
jumpId = 1061,
order = 122,
taskGroupId = 44059
},
[440540] = {
id = 440540,
name = "泡泡大战达到钻石段位",
desc = "泡泡大战达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
13
}
},
{
type = 154,
value = {
130006
}
}
}
}
}
}
},
reward = {
itemIdList = {
820104
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1741881599
}
}
},
jumpId = 1061,
order = 121,
taskGroupId = 44059
},
[440600] = {
id = 440600,
name = "(每日)游玩任意娱乐模式排位赛1次",
desc = "(每日)游玩任意娱乐模式排位赛1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
708,
709,
710,
701,
703,
352,
151,
141,
607,
608,
609,
131,
18,
19,
20,
5601,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
200
}
},
jumpId = 4,
order = 1020,
taskGroupId = 44061
},
[440623] = {
id = 440623,
name = "突围梦幻岛达到黄金段位",
desc = "突围梦幻岛达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90007
}
}
}
}
}
}
},
reward = {
itemIdList = {
860028,
840076
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1027,
order = 118,
taskGroupId = 44062
},
[440624] = {
id = 440624,
name = "突围梦幻岛达到钻石段位",
desc = "突围梦幻岛达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90007
}
}
}
}
}
}
},
reward = {
itemIdList = {
820056
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1027,
order = 117,
taskGroupId = 44062
},
[440625] = {
id = 440625,
name = "武器大师达到黄金段位",
desc = "武器大师达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60007
}
}
}
}
}
}
},
reward = {
itemIdList = {
860029,
840077
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1026,
order = 116,
taskGroupId = 44063
},
[440626] = {
id = 440626,
name = "武器大师达到钻石段位",
desc = "武器大师达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60007
}
}
}
}
}
}
},
reward = {
itemIdList = {
820057
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1026,
order = 115,
taskGroupId = 44063
},
[440627] = {
id = 440627,
name = "大王别抓我星宝-达到黄金段位",
desc = "大王别抓我星宝-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860027,
840075
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1022,
order = 114,
taskGroupId = 44064
},
[440628] = {
id = 440628,
name = "大王别抓我星宝-达到钻石段位",
desc = "大王别抓我星宝-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820055
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1022,
order = 113,
taskGroupId = 44064
},
[440629] = {
id = 440629,
name = "大王别抓我暗星-达到黄金段位",
desc = "大王别抓我暗星-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860030,
840078
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1022,
order = 112,
taskGroupId = 44064
},
[440630] = {
id = 440630,
name = "大王别抓我暗星-达到钻石段位",
desc = "大王别抓我暗星-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820058
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1022,
order = 111,
taskGroupId = 44064
},
[440631] = {
id = 440631,
name = "谁是狼人达到黄金段位",
desc = "谁是狼人达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860023,
840071
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1024,
order = 106,
taskGroupId = 44065
},
[440632] = {
id = 440632,
name = "谁是狼人达到钻石段位",
desc = "谁是狼人达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820051
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1024,
order = 105,
taskGroupId = 44065
},
[440633] = {
id = 440633,
name = "躲猫猫达到黄金段位",
desc = "躲猫猫达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860024,
840072
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1023,
order = 108,
taskGroupId = 44066
},
[440634] = {
id = 440634,
name = "躲猫猫达到钻石段位",
desc = "躲猫猫达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820052
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1023,
order = 107,
taskGroupId = 44066
},
[440635] = {
id = 440635,
name = "极速飞车达到黄金段位",
desc = "极速飞车达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860025,
840073
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1025,
order = 110,
taskGroupId = 44067
},
[440636] = {
id = 440636,
name = "极速飞车达到钻石段位",
desc = "极速飞车达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1025,
order = 109,
taskGroupId = 44067
},
[440637] = {
id = 440637,
name = "卧底行动达到黄金段位",
desc = "卧底行动达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860061,
840123
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1039,
order = 120,
taskGroupId = 44068
},
[440638] = {
id = 440638,
name = "卧底行动达到钻石段位",
desc = "卧底行动达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820084
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1039,
order = 119,
taskGroupId = 44068
},
[440639] = {
id = 440639,
name = "泡泡大战达到黄金段位",
desc = "泡泡大战达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
13
}
},
{
type = 154,
value = {
130007
}
}
}
}
}
}
},
reward = {
itemIdList = {
860090,
840154
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1746719999
},
{
seconds = 1746719999
}
}
},
jumpId = 1061,
order = 122,
taskGroupId = 44069
},
[440640] = {
id = 440640,
name = "泡泡大战达到钻石段位",
desc = "泡泡大战达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
13
}
},
{
type = 154,
value = {
130007
}
}
}
}
}
}
},
reward = {
itemIdList = {
820104
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746719999
}
}
},
jumpId = 1061,
order = 121,
taskGroupId = 44069
},
[440541] = {
id = 440541,
name = "分享至任意渠道",
desc = "分享至任意渠道",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 10,
value = 1,
subConditionList = {
{
type = 67,
value = {
10010
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
200
}
},
taskGroupId = 443001
},
[440703] = {
id = 440703,
name = "(每日)游玩任意娱乐模式排位赛1次",
desc = "(每日)游玩任意娱乐模式排位赛1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
708,
709,
710,
701,
703,
352,
151,
141,
607,
608,
609,
131,
18,
19,
20,
5601,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
200
}
},
jumpId = 4,
order = 1020,
taskGroupId = 44071
},
[440723] = {
id = 440723,
name = "突围梦幻岛达到黄金段位",
desc = "突围梦幻岛达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860028,
840076
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1027,
order = 118,
taskGroupId = 44072
},
[440724] = {
id = 440724,
name = "突围梦幻岛达到钻石段位",
desc = "突围梦幻岛达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820056
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1027,
order = 117,
taskGroupId = 44072
},
[440725] = {
id = 440725,
name = "武器大师达到黄金段位",
desc = "武器大师达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60008
}
}
}
}
}
}
},
reward = {
itemIdList = {
860029,
840077
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1026,
order = 116,
taskGroupId = 44073
},
[440726] = {
id = 440726,
name = "武器大师达到钻石段位",
desc = "武器大师达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60008
}
}
}
}
}
}
},
reward = {
itemIdList = {
820057
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1026,
order = 115,
taskGroupId = 44073
},
[440727] = {
id = 440727,
name = "大王别抓我星宝-达到黄金段位",
desc = "大王别抓我星宝-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860027,
840075
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1022,
order = 114,
taskGroupId = 44074
},
[440728] = {
id = 440728,
name = "大王别抓我星宝-达到钻石段位",
desc = "大王别抓我星宝-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820055
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1022,
order = 113,
taskGroupId = 44074
},
[440729] = {
id = 440729,
name = "大王别抓我暗星-达到黄金段位",
desc = "大王别抓我暗星-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860030,
840078
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1022,
order = 112,
taskGroupId = 44074
},
[440730] = {
id = 440730,
name = "大王别抓我暗星-达到钻石段位",
desc = "大王别抓我暗星-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820058
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1022,
order = 111,
taskGroupId = 44074
},
[440731] = {
id = 440731,
name = "谁是狼人达到黄金段位",
desc = "谁是狼人达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860023,
840071
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1024,
order = 106,
taskGroupId = 44075
},
[440732] = {
id = 440732,
name = "谁是狼人达到钻石段位",
desc = "谁是狼人达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820051
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1024,
order = 105,
taskGroupId = 44075
},
[440733] = {
id = 440733,
name = "躲猫猫达到黄金段位",
desc = "躲猫猫达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860024,
840072
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1023,
order = 108,
taskGroupId = 44076
},
[440734] = {
id = 440734,
name = "躲猫猫达到钻石段位",
desc = "躲猫猫达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820052
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1023,
order = 107,
taskGroupId = 44076
},
[440735] = {
id = 440735,
name = "极速飞车达到黄金段位",
desc = "极速飞车达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860025,
840073
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1025,
order = 110,
taskGroupId = 44077
},
[440736] = {
id = 440736,
name = "极速飞车达到钻石段位",
desc = "极速飞车达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1025,
order = 109,
taskGroupId = 44077
},
[440737] = {
id = 440737,
name = "卧底行动达到黄金段位",
desc = "卧底行动达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860061,
840123
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1750953599
},
{
seconds = 1750953599
}
}
},
jumpId = 1039,
order = 120,
taskGroupId = 44078
},
[440738] = {
id = 440738,
name = "卧底行动达到钻石段位",
desc = "卧底行动达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820084
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1039,
order = 119,
taskGroupId = 44078
},
[420151] = {
id = 420151,
name = "（每日）在晋级赛使用3次传送",
desc = "（每日）在晋级赛使用3次传送",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1204,
value = 3,
subConditionList = {
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 1153,
taskGroupId = 40027
},
[420152] = {
id = 420152,
name = "（每日）1局晋级赛技巧分达10分",
desc = "（每日）1局晋级赛技巧分达10分",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
},
{
type = 506,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 1080,
taskGroupId = 40027
},
[420153] = {
id = 420153,
name = "（每日）小队组队开局天天晋级赛",
desc = "（每日）小队组队开局天天晋级赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 153,
value = 1,
subConditionList = {
{
type = 146,
value = {
5076
}
},
{
type = 1,
value = {
2,
3,
4
}
},
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
20
},
validPeriodList = {
0
}
},
jumpId = 1153,
taskGroupId = 40027
},
[420154] = {
id = 420154,
name = "小队成员达到2人",
desc = "小队成员达到2人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 154,
value = 2,
subConditionList = {
{
type = 146,
value = {
5076
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
40
},
validPeriodList = {
0
}
},
taskGroupId = 40028
},
[420155] = {
id = 420155,
name = "小队成员达到3人",
desc = "小队成员达到3人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 154,
value = 3,
subConditionList = {
{
type = 146,
value = {
5076
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
40
},
validPeriodList = {
0
}
},
taskGroupId = 40028
},
[420156] = {
id = 420156,
name = "小队成员达到4人",
desc = "小队成员达到4人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 154,
value = 4,
subConditionList = {
{
type = 146,
value = {
5076
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
40
},
validPeriodList = {
0
}
},
taskGroupId = 40028
},
[420157] = {
id = 420157,
name = "和1名小队成员成为好友",
desc = "和1名小队成员成为好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 184,
value = 1,
subConditionList = {
{
type = 146,
value = {
5076
}
},
{
type = 1,
value = {
1,
2,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 40028
},
[420158] = {
id = 420158,
name = "和2名小队成员成为好友",
desc = "和2名小队成员成为好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 184,
value = 1,
subConditionList = {
{
type = 146,
value = {
5076
}
},
{
type = 1,
value = {
2,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 40028
},
[420159] = {
id = 420159,
name = "和3名小队成员成为好友",
desc = "和3名小队成员成为好友",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 184,
value = 1,
subConditionList = {
{
type = 146,
value = {
5076
}
},
{
type = 1,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 40028
},
[420160] = {
id = 420160,
name = "开局天天晋级赛四人模式5局",
desc = "开局天天晋级赛四人模式5局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
50
},
validPeriodList = {
0
}
},
jumpId = 1153,
taskGroupId = 40028
},
[420161] = {
id = 420161,
name = "开局天天晋级赛四人模式10局",
desc = "开局天天晋级赛四人模式10局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 10,
subConditionList = {
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
100
},
validPeriodList = {
0
}
},
jumpId = 1153,
taskGroupId = 40028
},
[420162] = {
id = 420162,
name = "开局天天晋级赛四人模式15局",
desc = "开局天天晋级赛四人模式15局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 15,
subConditionList = {
{
type = 2,
value = {
4,
5,
6,
7,
8,
9
}
}
}
}
}
}
},
reward = {
itemIdList = {
3611
},
numList = {
150
},
validPeriodList = {
0
}
},
jumpId = 1153,
taskGroupId = 40028
},
[420163] = {
id = 420163,
name = "累计获得友谊代币50个",
desc = "累计获得友谊代币50个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 50,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
200
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420164] = {
id = 420164,
name = "累计获得友谊代币150个",
desc = "累计获得友谊代币150个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 150,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420165] = {
id = 420165,
name = "累计获得友谊代币250个",
desc = "累计获得友谊代币250个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 250,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
3610
},
numList = {
500
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420166] = {
id = 420166,
name = "累计获得友谊代币300个",
desc = "累计获得友谊代币300个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 300,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420167] = {
id = 420167,
name = "累计获得友谊代币350个",
desc = "累计获得友谊代币350个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 350,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
290021
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420168] = {
id = 420168,
name = "累计获得友谊代币400个",
desc = "累计获得友谊代币400个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 400,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
340012
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420169] = {
id = 420169,
name = "累计获得友谊代币450个",
desc = "累计获得友谊代币450个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 450,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
3134
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420170] = {
id = 420170,
name = "累计获得友谊代币500个",
desc = "累计获得友谊代币500个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 500,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
711393
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420171] = {
id = 420171,
name = "累计获得友谊代币550个",
desc = "累计获得友谊代币550个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 550,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
290022
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420172] = {
id = 420172,
name = "累计获得友谊代币600个",
desc = "累计获得友谊代币600个",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 600,
subConditionList = {
{
type = 3,
value = {
3611
}
}
}
}
}
}
},
reward = {
itemIdList = {
340007
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 40029
},
[420173] = {
id = 420173,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 1
}
}
}
},
reward = {
itemIdList = {
711273
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420174] = {
id = 420174,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 2
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420175] = {
id = 420175,
name = "第3天",
desc = "第3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 3
}
}
}
},
reward = {
itemIdList = {
840212
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420176] = {
id = 420176,
name = "第4天",
desc = "第4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 4
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
15
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420177] = {
id = 420177,
name = "第5天",
desc = "第5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 5
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420178] = {
id = 420178,
name = "第6天",
desc = "第6天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 6
}
}
}
},
reward = {
itemIdList = {
850522
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420179] = {
id = 420179,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 7
}
}
}
},
reward = {
itemIdList = {
711273
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420180] = {
id = 420180,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 8
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420181] = {
id = 420181,
name = "第3天",
desc = "第3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 9
}
}
}
},
reward = {
itemIdList = {
840212
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420182] = {
id = 420182,
name = "第4天",
desc = "第4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 10
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
15
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420183] = {
id = 420183,
name = "第5天",
desc = "第5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 11
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420184] = {
id = 420184,
name = "第6天",
desc = "第6天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 12
}
}
}
},
reward = {
itemIdList = {
850522
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420185] = {
id = 420185,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 13
}
}
}
},
reward = {
itemIdList = {
711273
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420186] = {
id = 420186,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 14
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420187] = {
id = 420187,
name = "第3天",
desc = "第3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 15
}
}
}
},
reward = {
itemIdList = {
840212
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420188] = {
id = 420188,
name = "第4天",
desc = "第4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 16
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
15
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420189] = {
id = 420189,
name = "第5天",
desc = "第5天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 17
}
}
}
},
reward = {
itemIdList = {
3433
},
numList = {
20
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[420190] = {
id = 420190,
name = "第6天",
desc = "第6天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 604,
value = 18
}
}
}
},
reward = {
itemIdList = {
850522
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44080
},
[440803] = {
id = 440803,
name = "(每日)游玩任意娱乐模式排位赛1次",
desc = "(每日)游玩任意娱乐模式排位赛1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
708,
709,
710,
701,
703,
352,
151,
141,
607,
608,
609,
131,
18,
19,
20,
5601,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
200
}
},
jumpId = 4,
order = 1020,
taskGroupId = 44081
},
[440823] = {
id = 440823,
name = "突围梦幻岛达到黄金段位",
desc = "突围梦幻岛达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860028,
840076
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1027,
order = 118,
taskGroupId = 44082
},
[440824] = {
id = 440824,
name = "突围梦幻岛达到钻石段位",
desc = "突围梦幻岛达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820056
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1027,
order = 117,
taskGroupId = 44082
},
[440825] = {
id = 440825,
name = "武器大师达到黄金段位",
desc = "武器大师达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60009
}
}
}
}
}
}
},
reward = {
itemIdList = {
860029,
840077
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1026,
order = 116,
taskGroupId = 44083
},
[440826] = {
id = 440826,
name = "武器大师达到钻石段位",
desc = "武器大师达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60009
}
}
}
}
}
}
},
reward = {
itemIdList = {
820057
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1026,
order = 115,
taskGroupId = 44083
},
[440827] = {
id = 440827,
name = "大王别抓我星宝-达到黄金段位",
desc = "大王别抓我星宝-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70011
}
}
}
}
}
}
},
reward = {
itemIdList = {
860027,
840075
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1022,
order = 114,
taskGroupId = 44084
},
[440828] = {
id = 440828,
name = "大王别抓我星宝-达到钻石段位",
desc = "大王别抓我星宝-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70011
}
}
}
}
}
}
},
reward = {
itemIdList = {
820055
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1022,
order = 113,
taskGroupId = 44084
},
[440829] = {
id = 440829,
name = "大王别抓我暗星-达到黄金段位",
desc = "大王别抓我暗星-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80011
}
}
}
}
}
}
},
reward = {
itemIdList = {
860030,
840078
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1022,
order = 112,
taskGroupId = 44084
},
[440830] = {
id = 440830,
name = "大王别抓我暗星-达到钻石段位",
desc = "大王别抓我暗星-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80011
}
}
}
}
}
}
},
reward = {
itemIdList = {
820058
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1022,
order = 111,
taskGroupId = 44084
},
[440831] = {
id = 440831,
name = "谁是狼人达到黄金段位",
desc = "谁是狼人达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20010
}
}
}
}
}
}
},
reward = {
itemIdList = {
860023,
840071
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1024,
order = 106,
taskGroupId = 44085
},
[440832] = {
id = 440832,
name = "谁是狼人达到钻石段位",
desc = "谁是狼人达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20010
}
}
}
}
}
}
},
reward = {
itemIdList = {
820051
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1024,
order = 105,
taskGroupId = 44085
},
[440833] = {
id = 440833,
name = "躲猫猫达到黄金段位",
desc = "躲猫猫达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30010
}
}
}
}
}
}
},
reward = {
itemIdList = {
860024,
840072
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1023,
order = 108,
taskGroupId = 44086
},
[440834] = {
id = 440834,
name = "躲猫猫达到钻石段位",
desc = "躲猫猫达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30010
}
}
}
}
}
}
},
reward = {
itemIdList = {
820052
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1023,
order = 107,
taskGroupId = 44086
},
[440835] = {
id = 440835,
name = "极速飞车达到黄金段位",
desc = "极速飞车达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50010
}
}
}
}
}
}
},
reward = {
itemIdList = {
860025,
840073
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1025,
order = 110,
taskGroupId = 44087
},
[440836] = {
id = 440836,
name = "极速飞车达到钻石段位",
desc = "极速飞车达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50010
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1025,
order = 109,
taskGroupId = 44087
},
[440837] = {
id = 440837,
name = "卧底行动达到黄金段位",
desc = "卧底行动达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110010
}
}
}
}
}
}
},
reward = {
itemIdList = {
860061,
840123
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1755791999
},
{
seconds = 1755791999
}
}
},
jumpId = 1039,
order = 120,
taskGroupId = 44088
},
[440838] = {
id = 440838,
name = "卧底行动达到钻石段位",
desc = "卧底行动达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110010
}
}
}
}
}
}
},
reward = {
itemIdList = {
820084
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1755791999
}
}
},
jumpId = 1039,
order = 119,
taskGroupId = 44088
},
[440900] = {
id = 440900,
name = "【每日】登录游戏",
desc = "【每日】登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3561
},
numList = {
2
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
order = 100,
taskGroupId = 44090
},
[440901] = {
id = 440901,
name = "【每周】完成5次闪电赛",
desc = "【每周】完成5次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 2,
value = {
15,
16,
17,
21,
22,
23
}
}
}
}
}
}
},
reward = {
itemIdList = {
3561
},
numList = {
2
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1043,
order = 99,
taskGroupId = 44091
},
[440902] = {
id = 440902,
name = "【每周】完成10次闪电赛",
desc = "【每周】完成10次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 10,
subConditionList = {
{
type = 2,
value = {
15,
16,
17,
21,
22,
23
}
}
}
}
}
}
},
reward = {
itemIdList = {
3561
},
numList = {
3
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1043,
order = 98,
taskGroupId = 44091
},
[440903] = {
id = 440903,
name = "【每周】完成15次闪电赛",
desc = "【每周】完成15次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 15,
subConditionList = {
{
type = 2,
value = {
15,
16,
17,
21,
22,
23
}
}
}
}
}
}
},
reward = {
itemIdList = {
3561
},
numList = {
4
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1043,
order = 97,
taskGroupId = 44091
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data