--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local v0 = {
{
itemId = 6,
itemNum = 100
}
}

local v1 = 3

local v2 = 10

local v3 = {
seconds = 4101552000
}

local data = {
[403471] = {
id = 403471,
effect = true,
name = "落笔云烟 丹青",
desc = "以我之笔，绘万物有灵",
icon = "CDN:Icon_PL_163_01",
outlookConf = {
belongTo = 403470,
fashionValue = 35,
belongToGroup = {
403471,
403472
}
},
resourceConf = {
model = "SK_PL_163",
material = "MI_PL_163_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_163_Physics",
materialSlot = "Skin"
},
commodityId = 11497,
outEnter = "AS_CH_Enter_PL_163",
outShow = "AS_CH_IdleShow_PL_163",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_163",
shareTexts = {
"诗是无形画，画是有形诗"
},
shareAnim = "AS_CH_Pose_001_PL_163",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403470.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403470.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403470.astc"
},
[403472] = {
id = 403472,
effect = true,
name = "落笔云烟 丹青",
desc = "以我之笔，绘万物有灵",
icon = "CDN:Icon_PL_163_02",
outlookConf = {
belongTo = 403470,
fashionValue = 35,
belongToGroup = {
403471,
403472
}
},
resourceConf = {
model = "SK_PL_163",
material = "MI_PL_163_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_163_Physics",
materialSlot = "Skin"
},
commodityId = 11498,
outEnter = "AS_CH_Enter_PL_163",
outShow = "AS_CH_IdleShow_PL_163",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_163",
shareTexts = {
"诗是无形画，画是有形诗"
},
shareAnim = "AS_CH_Pose_001_PL_163",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403470.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403470.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403470.astc"
},
[403480] = {
id = 403480,
effect = true,
name = "燎原之心 云缨",
desc = "准备好一战成名吧！",
icon = "CDN:Icon_PL_159",
resourceConf = {
model = "SK_PL_159",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_159_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_159",
outShow = "AS_CH_IdleShow_PL_159",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_159",
shareTexts = {
"保持干劲，继续前进！"
},
beginTime = v3,
suitId = 376,
suitName = "燎原之心 云缨",
suitIcon = "CDN:Icon_PL_159"
},
[403490] = {
id = 403490,
effect = true,
name = "舞龙少年 凌霄",
desc = "龙腾虎跃，步步高升",
icon = "CDN:Icon_PL_102",
resourceConf = {
model = "SK_PL_102",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_102_Physics"
},
outEnter = "AS_CH_Enter_PL_102",
outShow = "AS_CH_IdleShow_PL_102",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_102",
shareTexts = {
"龙抬头，好兆头"
},
beginTime = v3,
suitStoryTextKey = [[凌霄从小就喜欢舞龙，看着舞龙者在梅花桩上辗转腾挪，他无比艳羡。之后他加入了当地的舞龙团，刻苦训练，以成为最出色的舞龙者为目标而努力。

龙族有圣地天星峰，常年云雾缭绕。逢百年，天星峰才有片刻时间云雾散去。这个时候所有的舞龙团都会派出自家的舞龙好手，登天星峰敲万鸣钟，此为每一个舞龙者至高无上的荣耀。通往顶峰的路段是钉在岩石上的梅花桩，舞龙者在路途中唯一能依靠的就是其胆识和技术。

凌霄亦想登顶敲钟。可他所在的舞龙团规模太小，提供的装备简陋，训练不够专业，凌霄只好自己想法子。他学习以往攀登者爬行的路线，拼命记住所有梅花桩的点位，每天吃饭睡觉脑内都在模拟攀登天星峰。此外还没日没夜的加强训练，百年一次的机会，他不想错过。

登峰那天，他最后一个出发。由于天气变化很多舞龙者都退出了，等到他出发的时候，天气极端恶劣，登峰即将终止。凌霄毅然决然出发，在浑浊的云雾里，凭借自己的记忆，大胆向前。电闪雷鸣，风起云涌，凌霄脚步自如，如真龙一般穿云出雾。最后他成功登顶，敲响万鸣钟，钟鸣云散，阳光洒遍天星峰，凌霄已然成为传奇！]],
suitId = 377,
suitName = "舞龙少年 凌霄",
suitIcon = "CDN:Icon_PL_102"
},
[403491] = {
id = 403491,
effect = true,
name = "舞龙少年 凌霄",
desc = "龙腾虎跃，步步高升",
icon = "CDN:Icon_PL_102_01",
outlookConf = {
belongTo = 403490,
fashionValue = 35,
belongToGroup = {
403491,
403492
}
},
resourceConf = {
model = "SK_PL_102",
material = "MI_PL_102_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_102_Physics",
materialSlot = "Skin"
},
commodityId = 11501,
outEnter = "AS_CH_Enter_PL_102",
outShow = "AS_CH_IdleShow_PL_102",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_102",
shareTexts = {
"龙抬头，好兆头"
}
},
[403492] = {
id = 403492,
effect = true,
name = "舞龙少年 凌霄",
desc = "龙腾虎跃，步步高升",
icon = "CDN:Icon_PL_102_02",
outlookConf = {
belongTo = 403490,
fashionValue = 35,
belongToGroup = {
403491,
403492
}
},
resourceConf = {
model = "SK_PL_102",
material = "MI_PL_102_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_102_Physics",
materialSlot = "Skin"
},
commodityId = 11502,
outEnter = "AS_CH_Enter_PL_102",
outShow = "AS_CH_IdleShow_PL_102",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_102",
shareTexts = {
"龙抬头，好兆头"
}
},
[403500] = {
id = 403500,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "黄金·鼠鼠",
desc = "荣华富贵，非你莫“鼠”",
icon = "CDN:Icon_Body_034",
outlookConf = {
fashionValue = 800
},
resourceConf = {
model = "SK_Body_Head_034",
upper = "SK_Body_Upper_034",
bottom = "SK_Body_Under_034",
gloves = "SK_Body_Hands_034",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 103
},
shareTexts = {
"小鼠也要富贵安康"
},
beginTime = v3,
suitId = 378,
suitName = "黄金·鼠鼠",
suitIcon = "CDN:Icon_Body_034"
},
[403510] = {
id = 403510,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "桂月儿",
desc = "最爱在秋风中翩翩起舞",
icon = "Icon_BU_172",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_172",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"欲买桂花同载酒"
},
beginTime = v3,
suitId = 379,
suitName = "桂月儿",
suitIcon = "Icon_BU_172"
},
[403511] = {
id = 403511,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "桂月儿",
desc = "最爱在秋风中翩翩起舞",
icon = "CDN:Icon_BU_172_01",
outlookConf = {
belongTo = 403510,
fashionValue = 25,
belongToGroup = {
403511
}
},
resourceConf = {
model = "SK_BU_172",
material = "MI_BU_172_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11505,
shareTexts = {
"欲买桂花同载酒"
}
},
[403520] = {
id = 403520,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "石烁烁",
desc = "快乐就像宝石，只要用心，总能找到",
icon = "CDN:Icon_BU_169",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_169",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"别放弃，石头里也会有惊喜"
},
beginTime = v3,
suitId = 380,
suitName = "石烁烁",
suitIcon = "CDN:Icon_BU_169"
},
[403521] = {
id = 403521,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "石烁烁",
desc = "快乐就像宝石，只要用心，总能找到",
icon = "CDN:Icon_BU_169_01",
outlookConf = {
belongTo = 403520,
fashionValue = 25,
belongToGroup = {
403521
}
},
resourceConf = {
model = "SK_BU_169",
material = "MI_BU_169_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11507,
shareTexts = {
"别放弃，石头里也会有惊喜"
}
},
[403530] = {
id = 403530,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "凤果果",
desc = "酸酸甜甜，咬一口就知道",
icon = "CDN:Icon_BU_170",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_170",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"无需泡盐，每一口都甜"
},
beginTime = v3,
suitId = 381,
suitName = "石烁烁",
suitIcon = "CDN:Icon_BU_170"
},
[403531] = {
id = 403531,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "凤果果",
desc = "酸酸甜甜，咬一口就知道",
icon = "CDN:Icon_BU_170_01",
outlookConf = {
belongTo = 403530,
fashionValue = 25,
belongToGroup = {
403531
}
},
resourceConf = {
model = "SK_BU_170",
material = "MI_BU_170_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11509,
shareTexts = {
"无需泡盐，每一口都甜"
}
},
[403540] = {
id = 403540,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "汉堡贝贝",
desc = "曾经沧海难为水，汉堡可乐配鸡腿",
icon = "CDN:Icon_BU_171",
getWay = "携友同行",
jumpId = {
132
},
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_171",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"怎么不是堡堡呢？"
},
beginTime = {
seconds = 1737043200
},
suitId = 382,
suitName = "汉堡贝贝",
suitIcon = "CDN:Icon_BU_171"
},
[403541] = {
id = 403541,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "汉堡贝贝",
desc = "曾经沧海难为水，汉堡可乐配鸡腿",
icon = "CDN:Icon_BU_171_01",
outlookConf = {
belongTo = 403540,
fashionValue = 25,
belongToGroup = {
403541
}
},
resourceConf = {
model = "SK_BU_171",
material = "MI_BU_171_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11511,
shareTexts = {
"怎么不是堡堡呢？"
}
},
[403550] = {
id = 403550,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "猎小鹰",
desc = "小小猎手，大大世界",
icon = "CDN:Icon_BU_166",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_166",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"你走你的路，我待我的兔"
},
beginTime = v3,
suitId = 383,
suitName = "猎小鹰",
suitIcon = "CDN:Icon_BU_166"
},
[403551] = {
id = 403551,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "猎小鹰",
desc = "小小猎手，大大世界",
icon = "CDN:Icon_BU_166_01",
outlookConf = {
belongTo = 403550,
fashionValue = 25,
belongToGroup = {
403551
}
},
resourceConf = {
model = "SK_BU_166",
material = "MI_BU_166_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11513,
shareTexts = {
"你走你的路，我待我的兔"
}
},
[403560] = {
id = 403560,
effect = true,
name = "幻舞玲珑  公孙离",
desc = "一舞剑器动四方",
icon = "CDN:Icon_PL_188",
resourceConf = {
model = "SK_PL_188",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_188_Physics",
IconLabelId = 102,
skeletalMesh = "SK_PL_188"
},
outEnter = "AS_CH_Enter_PL_188",
outShow = "AS_CH_IdleShow_PL_188",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_188",
shareTexts = {
"再多喜欢阿离一点，可以吗？"
},
beginTime = v3,
suitId = 384,
suitName = "幻舞玲珑  公孙离",
suitIcon = "CDN:Icon_PL_188"
},
[403570] = {
id = 403570,
effect = true,
name = "皓月使者 丹桂",
desc = "愿所有阖家团圆，都不必跋山涉水相见",
icon = "CDN:Icon_PL_169",
resourceConf = {
model = "SK_PL_169",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_169_Physics"
},
outEnter = "AS_CH_Enter_PL_169",
outShow = "AS_CH_IdleShow_PL_169",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_169",
shareTexts = {
"丹桂飘香，秋风拂月"
},
beginTime = v3,
suitStoryTextKey = [[丹桂乃月宫最为温柔的星宝，并且他还肩负着月宫使者的职责。在中秋之时，每一个无法团圆的星宝都会令明月的光辉减损一分，而丹桂便会来到他们的身旁相伴，今年他亦是如此。

今夜，他不知何时就出现在星宝面前，这位星宝正在望着中秋圆月暗自神伤。丹桂温柔地拍拍肩膀安慰他，并端来一碟桂花糕。星宝接纳了他的善意，与他一同赏月。

但星宝内心仍然希望能见亲朋好友一面，丹桂看出了他的心思，也在赏月时瞥见了窗外的高楼大厦车水马龙，这才意识到似乎来到了什么不得了的地方。丹桂指向月亮说自己从哪里来，月亮上有宏美的月宫，还有成片的月桂林。

突然一声电话铃声打断了丹桂的话语，星宝赶忙去接电话，但却摔了一跤。回过神却发现是从沙发上掉了下来，刚才似乎是在沙发上小憩，接起视频电话，惊讶地发现自己的心愿成真了！星宝亲朋好友挤在电话另一边给他中秋祝福，于是，在似有似无的桂花香中他们聊起家常。

秋风扫过桌上，翻开了星宝正在看的图册，《夜游星月宫图》展现在眼前，其上画着丹桂正在月宫中，端起一碟桂花糕，看着画外越来越发明亮的圆月开心地笑着。 ]],
suitId = 385,
suitName = "皓月使者 丹桂",
suitIcon = "CDN:Icon_PL_169"
},
[403571] = {
id = 403571,
effect = true,
name = "皓月使者 丹桂",
desc = "愿所有阖家团圆，都不必跋山涉水相见",
icon = "CDN:Icon_PL_169_01",
outlookConf = {
belongTo = 403570,
fashionValue = 35,
belongToGroup = {
403571,
403572
}
},
resourceConf = {
model = "SK_PL_169",
material = "MI_PL_169_HP01;MI_PL_169_1_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_169_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11516,
outEnter = "AS_CH_Enter_PL_169",
outShow = "AS_CH_IdleShow_PL_169",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_169",
shareTexts = {
"丹桂飘香，秋风拂月"
}
},
[403572] = {
id = 403572,
effect = true,
name = "皓月使者 丹桂",
desc = "愿所有阖家团圆，都不必跋山涉水相见",
icon = "CDN:Icon_PL_169_02",
outlookConf = {
belongTo = 403570,
fashionValue = 35,
belongToGroup = {
403571,
403572
}
},
resourceConf = {
model = "SK_PL_169",
material = "MI_PL_169_HP02;MI_PL_169_1_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_169_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11517,
outEnter = "AS_CH_Enter_PL_169",
outShow = "AS_CH_IdleShow_PL_169",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_169",
shareTexts = {
"丹桂飘香，秋风拂月"
}
},
[403580] = {
id = 403580,
effect = true,
name = "月宫灵兔 锦儿",
desc = "宫灯虽小，足以为你照亮回家的路",
icon = "CDN:Icon_PL_170",
resourceConf = {
model = "SK_PL_170",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_170_Physics"
},
outEnter = "AS_CH_Enter_PL_170",
outShow = "AS_CH_IdleShow_PL_170",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_170",
shareTexts = {
"花好月圆，愿你日日甜"
},
beginTime = v3,
suitStoryTextKey = [[月宫灵兔锦儿是月宫的大管家，她总是拎一盏提灯，身着锦缎华服，戴月桂头饰，轻晃兔耳在宫中穿梭，将偌大的月宫耐心打理，倾听大家的想法。

但锦儿并非过去就有耐性，从前的她急躁如火，抓住一个星宝就讲个不停，没等对方听明白就又急匆匆的跑走了，一年到头她却发现自己没做成几件事。

锦儿很是苦恼，于是她找到师傅求助。师傅给她一盏提灯，让她停下所有事，提着灯将月宫周围所有路灯点亮。锦儿不理解点灯有何意义？师傅只是笑了笑，让她先去做。

锦儿开始了点灯修行，一盏两盏……全部点完已过去大半天，师傅让她继续。一天两天……十天锦儿实在干不下去了，师傅仍让她如旧。

这天锦儿点灯时，遇到了刚从星梭下来的星宝。对方感叹着月宫的深思熟虑，竟提前做好了警示，锦儿听后不可置信。这位星宝也不多说，拉上锦儿就飞到空中。从夜空看下去，整座月宫被灯火形成的光链标注，无比清晰。

锦儿终于明白耐心工作的意义，原来只有耐心做好小事，才会成就大事。

如今锦儿虽已是月宫大管家，但她每天仍会点亮所有路灯，再开始耐心处理好事务，将月宫打理的越发完善，成为月亮上的一盏明灯。]],
suitId = 386,
suitName = "月宫灵兔 锦儿",
suitIcon = "CDN:Icon_PL_170"
},
[403581] = {
id = 403581,
effect = true,
name = "月宫灵兔 锦儿",
desc = "宫灯虽小，足以为你照亮回家的路",
icon = "CDN:Icon_PL_170_01",
outlookConf = {
belongTo = 403580,
fashionValue = 35,
belongToGroup = {
403581,
403582
}
},
resourceConf = {
model = "SK_PL_170",
material = "MI_PL_170_1_HP01;MI_PL_170_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_170_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11519,
outEnter = "AS_CH_Enter_PL_170",
outShow = "AS_CH_IdleShow_PL_170",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_170",
shareTexts = {
"花好月圆，愿你日日甜"
}
},
[403582] = {
id = 403582,
effect = true,
name = "月宫灵兔 锦儿",
desc = "宫灯虽小，足以为你照亮回家的路",
icon = "CDN:Icon_PL_170_02",
outlookConf = {
belongTo = 403580,
fashionValue = 35,
belongToGroup = {
403581,
403582
}
},
resourceConf = {
model = "SK_PL_170",
material = "MI_PL_170_1_HP02;MI_PL_170_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_170_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11520,
outEnter = "AS_CH_Enter_PL_170",
outShow = "AS_CH_IdleShow_PL_170",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_170",
shareTexts = {
"花好月圆，愿你日日甜"
}
},
[403590] = {
id = 403590,
effect = true,
name = "玻璃精灵 格莉丝",
desc = "玻璃易碎，初心坚不可摧",
icon = "CDN:Icon_PL_175",
getWay = "金秋充值送",
jumpId = {
8
},
resourceConf = {
model = "SK_PL_175",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_175_Physics"
},
outEnter = "AS_CH_Enter_PL_175",
outShow = "AS_CH_IdleShow_PL_175",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_175",
shareTexts = {
"透明只是我的保护色"
},
beginTime = {
seconds = 1726156800
},
suitStoryTextKey = [[玻璃精灵格莉丝是森林小镇中的手工艺师，梦想着制作出世界上最美丽的艺术品。 恰逢小镇将举办一场艺术品评选大赛，她意识到这是一个绝佳的机会。

她四处寻找灵感，向最古老的树精询问，在他漫长的生命中，什么才是最美的。树精缓缓地回答：“是阳光，它能够温暖我的全身。”然而，树旁的岩石精却不以为然，“阳光怎能与大雨相比？只有大雨才能洗净岩石。”他们争论不休，格莉丝只好离开。

夜晚，她沉浸在古老树精与岩石精对话的深思中。对他们而言，最美的都是身边之物。她不禁思索：对于自己来说，什么才是最珍贵的美？沉思间月光洒进室内，一处闪光从角落折出，走近后发现这里静静躺着一朵小玻璃花。

这是她制作的首个工艺品，无比稚嫩，却凝聚了她曾经的所有心血，寄托着她对创造最美艺术品的渴望。格莉丝越看越喜爱，终于领悟到了自己真正的追求所在。

隔天，大赛如期举行，场上出现了一件巨大的玻璃花。每一层花瓣都由不同的工艺打造，折射出绚丽的光彩。在花瓣的中心，竟镶嵌着一朵稚嫩的小花作为花蕊。

格莉丝将这件作品命名为“初心”。对她而言，大赛的结果已不再重要，她已经创造出了自己心中最美的艺术品，那份最初的热爱与追求已然回归心间。 ]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403590.astc",
suitId = 387,
suitName = "玻璃精灵 格莉丝",
suitIcon = "CDN:Icon_PL_175",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403590.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403590.astc"
},
[403591] = {
id = 403591,
effect = true,
name = "玻璃精灵 格莉丝",
desc = "玻璃易碎，初心坚不可摧",
icon = "CDN:Icon_PL_175_01",
outlookConf = {
belongTo = 403590,
fashionValue = 35,
belongToGroup = {
403591,
403592
}
},
resourceConf = {
model = "SK_PL_175",
material = "MI_PL_175_1_HP01;MI_PL_175_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_175_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11522,
outEnter = "AS_CH_Enter_PL_175",
outShow = "AS_CH_IdleShow_PL_175",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_175",
shareTexts = {
"透明只是我的保护色"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403590.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403590.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403590.astc"
},
[403592] = {
id = 403592,
effect = true,
name = "玻璃精灵 格莉丝",
desc = "玻璃易碎，初心坚不可摧",
icon = "CDN:Icon_PL_175_02",
outlookConf = {
belongTo = 403590,
fashionValue = 35,
belongToGroup = {
403591,
403592
}
},
resourceConf = {
model = "SK_PL_175",
material = "MI_PL_175_1_HP02;MI_PL_175_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_175_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11523,
outEnter = "AS_CH_Enter_PL_175",
outShow = "AS_CH_IdleShow_PL_175",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_175",
shareTexts = {
"透明只是我的保护色"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403590.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403590.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403590.astc"
},
[403600] = {
id = 403600,
effect = true,
name = "晨曦玫瑰 罗瑟琳",
desc = "玫瑰会枯萎，但浪漫不会",
icon = "CDN:Icon_PL_177",
getWay = "晨曦玫瑰",
jumpId = {
622
},
resourceConf = {
model = "SK_PL_177",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_177_Physics"
},
outEnter = "AS_CH_Enter_PL_177",
outShow = "AS_CH_IdleShow_PL_177",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_177",
shareTexts = {
"属于你的浪漫，终究会来到"
},
beginTime = {
seconds = 1728662400
},
suitStoryTextKey = [[在遥远的森林深处，矗立着一棵参天巨树。树下是罗瑟琳的家，自幼年起，她便攀附在巨树的怀抱中，享受这片安全的绿荫，沐浴晨曦的光泽和雨露的滋润。

森林中有许多可爱的伙伴，松鼠蹦跳着送来新鲜的坚果，蝴蝶翩翩起舞，引领着花香弥漫；鸟儿的歌声唤醒了晨曦与甘露，还有那些远道而来的星宝，他们不时穿梭于森林，带来远方的奇闻异事与珍贵的宝物。罗瑟琳对森林外充满憧憬，但当星宝盛情邀请她一同探索广阔天地时，她总是怀揣一丝畏惧，未敢迈出森林的边界。

直到有一天，巨树不幸染疾，这让罗瑟琳非常伤心。巨树许诺，哪怕仅余最后一片叶子，也将继续守护她，但罗瑟琳更希望巨树能够恢复往日的生机。她鼓起勇气，决定去森林外找到治疗之法。

初涉外界，罗瑟琳仍存胆怯，她习惯性地想要找地方依靠，身后却不再有大树为她遮风避雨，她只能独自面对眼前的挑战。但很快，她发现外界并非想象中那般可怕。狂风教会了她坚韧，大雨也让她更加艳丽。当风雨过后，初升的第一抹晨曦落在罗瑟琳脸颊上，她如获新生，向着太阳盛放出最灿烂的笑容。

最终，罗瑟琳找到了生命源泉，让巨树重获新生，再度枝繁叶茂。但当巨树伸展枝桠，想再度让罗瑟琳依靠时，罗瑟琳却婉拒了这份厚爱。巨树虽感一丝落寞，却更为她的成长而欣喜。罗瑟琳深知，告别依赖与庇护，方能绽放更绚烂的自我。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403600.astc",
suitId = 388,
suitName = "晨曦玫瑰 罗瑟琳",
suitIcon = "CDN:Icon_PL_177",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403600.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403600.astc"
},
[403601] = {
id = 403601,
effect = true,
name = "晨曦玫瑰 罗瑟琳",
desc = "玫瑰会枯萎，但浪漫不会",
icon = "CDN:Icon_PL_177_01",
outlookConf = {
belongTo = 403600,
fashionValue = 35,
belongToGroup = {
403601,
403602
}
},
resourceConf = {
model = "SK_PL_177",
material = "MI_PL_177_1_HP01;MI_PL_177_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_177_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11525,
outEnter = "AS_CH_Enter_PL_177",
outShow = "AS_CH_IdleShow_PL_177",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_177",
shareTexts = {
"属于你的浪漫，终究会来到"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403600.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403600.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403600.astc"
},
[403602] = {
id = 403602,
effect = true,
name = "晨曦玫瑰 罗瑟琳",
desc = "玫瑰会枯萎，但浪漫不会",
icon = "CDN:Icon_PL_177_02",
outlookConf = {
belongTo = 403600,
fashionValue = 35,
belongToGroup = {
403601,
403602
}
},
resourceConf = {
model = "SK_PL_177",
material = "MI_PL_177_1_HP02;MI_PL_177_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_177_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11526,
outEnter = "AS_CH_Enter_PL_177",
outShow = "AS_CH_IdleShow_PL_177",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_177",
shareTexts = {
"属于你的浪漫，终究会来到"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403600.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403600.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403600.astc"
},
[403610] = {
id = 403610,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "黄金·小小象",
desc = "黄金小象，幸运吉祥",
icon = "CDN:Icon_Body_033",
outlookConf = {
fashionValue = 800
},
resourceConf = {
model = "SK_Body_Head_033",
upper = "SK_Body_Upper_033",
bottom = "SK_Body_Under_033",
gloves = "SK_Body_Hands_033",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
IconLabelId = 103
},
shareTexts = {
"小象不小，招财进宝"
},
beginTime = v3,
suitId = 389,
suitName = "黄金·小小象",
suitIcon = "CDN:Icon_Body_033"
},
[403620] = {
id = 403620,
effect = true,
name = "企鹅小甜豆",
desc = "我是游泳小冠军",
icon = "CDN:Icon_PL_173",
resourceConf = {
model = "SK_PL_173",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_173_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_173",
outShow = "AS_CH_IdleShow_PL_173",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_173",
shareTexts = {
"准备好新的冒险了吗？"
},
beginTime = {
seconds = 1728576000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403620.astc",
suitId = 390,
suitName = "企鹅小甜豆",
themedId = 21,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_173",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403620.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403620.astc",
ThemedShowIdList = {
{
key = 21,
value = 2
},
{
key = 33,
value = 7
}
}
},
[403630] = {
id = 403630,
effect = true,
name = "绵羊小甜豆",
desc = "喜欢这个柔软的世界",
icon = "CDN:Icon_PL_174",
resourceConf = {
model = "SK_PL_174",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_174_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_174",
outIdle = "AS_CH_Idle_001_PL_079",
outShow = "AS_CH_IdleShow_PL_174",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_174",
shareTexts = {
"喜欢轻柔的风，喜欢你"
},
beginTime = {
seconds = 1728576000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403630.astc",
suitId = 391,
suitName = "绵羊小甜豆",
themedId = 21,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_174",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403630.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403620.astc",
ThemedShowIdList = {
{
key = 21,
value = 1
},
{
key = 33,
value = 1
}
}
},
[403640] = {
id = 403640,
effect = true,
name = "偶像歌手 王昭君",
desc = "羞涩的初次公演，要支持我哟！",
icon = "CDN:Icon_PL_160",
resourceConf = {
model = "SK_PL_160",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_160_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_160",
outShow = "AS_CH_IdleShow_PL_160",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_160",
shareTexts = {
"别欺负我哟，会哭的"
},
beginTime = v3,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403640.astc",
suitId = 395,
suitName = "偶像歌手 王昭君",
suitIcon = "CDN:Icon_PL_160",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403640.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403640.astc"
},
[403650] = {
id = 403650,
effect = true,
name = "记忆之芯 公孙离",
desc = "你好，世界",
icon = "CDN:Icon_PL_162",
resourceConf = {
model = "SK_PL_162",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_162_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_162",
outShow = "AS_CH_IdleShow_PL_162",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_162",
shareTexts = {
"还想，探索更远的地方"
},
beginTime = v3,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403650.astc",
suitId = 396,
suitName = "记忆之芯 公孙离",
suitIcon = "CDN:Icon_PL_162",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403650.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403650.astc"
},
[403660] = {
id = 403660,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "三彩逸士 青云",
desc = "以身为柴，燃三彩之焰，承千都之韵",
icon = "CDN:Icon_OG_025",
outlookConf = {
fashionValue = 1200
},
resourceConf = {
model = "SK_OG_025",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_025_Physics",
IconLabelId = 103
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_025_PV/Level_OG_025_Intact",
outIdle = "AS_CH_OutIdle_OG_025",
outShow = "AS_CH_IdleShow_OG_025",
outShowIntervalTime = 20,
soundId = {
4048,
4049
},
outIdle_pv = "LS_PV_OG_025_Idle",
outEnterSequence = "LS_PV_OG_025",
shareTexts = {
"不识三彩，不见千都"
},
beginTime = v3,
suitStoryTextKey = [[青云生于古城千都，聪慧机敏，幼时博览群书，精通书画，有“才绝”之称。

千都在千年的节点遇到了史无前例的危机。一股黑烟悄悄潜入，夺走了千都的气韵——千都多年文化气息形成的黄绿白三色灵气。青云的父亲曾说过，若是失去了气韵，千都将会变得枯燥无味，失去色彩，最后只剩下单调的黑灰色，民众也会失去创造力，变得懒惰愚蠢。危机已至，青云收拾行李后，便循着黑烟的踪迹追去。

路途艰难，青云穿过泥泞的沼泽，贫瘠的荒原，干涸的热谷，而期间最难的当属火焰弥漫的山林。烬火狂妄，无法通过，青云无视缠绕身躯的烈火，执笔作画，画的是在千都流淌千年的素水，画卷展开，引滔天素水，熄灭了山林火焰。

终遇黑烟，它正以自身的力量炼化千都气韵。青云已疲惫不堪，可还是提起了手中的笔与黑烟战斗。战斗时青云手中的笔奇光异闪，那是父亲传给他的笔，不知写了多少诗，做了多少画，记录了多少传奇，这支笔是千都气韵的一部分。青云想起笔的名字：三彩，他轻声呼唤，笔有灵，飞舞旋动，顷刻间，黑烟消散，只余留奄奄一息的千都气韵。

回到千都，这里已是颓败不堪，星宝和建筑都失去了颜色。情况紧急，可三色灵气毫无反应。恍然间，他想起父亲的教诲：千都的气韵是和千都的文化紧密相连的。青云来到千都的文化中心，千都书院。他手执三彩，以气韵为墨，在书院旁的陶瓷白马上忘情绘画。蓦然间，彩马奔腾，其上颜色流淌飞舞，满溢全城，千都，又活了！

如今，千都三彩名号举世皆知，青云守护着千都的气韵，传授制作三彩的技巧。而千都的气韵在好学勤劳的星宝手中，必会传承又一个千年！]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403660.astc",
suitId = 397,
suitName = "三彩逸士 青云",
suitIcon = "CDN:Icon_OG_025",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403660.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403660.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030"
},
[403661] = {
id = 403661,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "三彩逸士 青云",
desc = "以身为柴，燃三彩之焰，承千都之韵",
icon = "CDN:Icon_OG_025_01",
outlookConf = {
belongTo = 403660,
fashionValue = 145,
belongToGroup = {
403661,
403662
}
},
resourceConf = {
model = "SK_OG_025",
material = "MI_OG_025_1_HP01;MI_OG_025_2_HP01;MI_OG_025_3_HP01;MI_OG_025_4_HP01;MI_OG_025_5_HP01;MI_OG_025_6_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_025_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06"
},
commodityId = 11533,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_025_PV/Level_OG_025_Intact",
outIdle = "AS_CH_OutIdle_OG_025",
outShow = "AS_CH_IdleShow_OG_025_HP01",
outShowIntervalTime = 20,
soundId = {
4048,
4049
},
outIdle_pv = "LS_PV_OG_025_HP01_Idle",
outEnterSequence = "LS_PV_OG_025_HP01",
shareTexts = {
"不识三彩，不见千都"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403660.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403660.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403660.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030"
},
[403662] = {
id = 403662,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "三彩逸士 青云",
desc = "以身为柴，燃三彩之焰，承千都之韵",
icon = "CDN:Icon_OG_025_02",
outlookConf = {
belongTo = 403660,
fashionValue = 145,
belongToGroup = {
403661,
403662
}
},
resourceConf = {
model = "SK_OG_025",
material = "MI_OG_025_1_HP02;MI_OG_025_2_HP02;MI_OG_025_3_HP02;MI_OG_025_4_HP02;MI_OG_025_5_HP02;MI_OG_025_6_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_025_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06"
},
commodityId = 11534,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_025_PV/Level_OG_025_Intact",
outIdle = "AS_CH_OutIdle_OG_025",
outShow = "AS_CH_IdleShow_OG_025_HP02",
outShowIntervalTime = 20,
soundId = {
4048,
4049
},
outIdle_pv = "LS_PV_OG_025_HP02_Idle",
outEnterSequence = "LS_PV_OG_025_HP02",
shareTexts = {
"不识三彩，不见千都"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403660.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403660.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403660.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_030"
},
[403670] = {
id = 403670,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "梦安安",
desc = "最伟大的超能力是自愈力",
icon = "CDN:Icon_BU_176",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_176",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"放轻松，静观己心"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitId = 398,
suitName = "梦安安",
bpShowId = 5,
seasonId = 8,
suitIcon = "CDN:Icon_BU_176",
SeasonShowIdList = {
{
key = 8,
value = 5
}
}
},
[403671] = {
id = 403671,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "梦安安",
desc = "最伟大的超能力是自愈力",
icon = "CDN:Icon_BU_176_01",
outlookConf = {
belongTo = 403670,
fashionValue = 25,
belongToGroup = {
403671
}
},
resourceConf = {
model = "SK_BU_176",
material = "MI_BU_176_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11536,
shareTexts = {
"放轻松，静观己心"
}
},
[403680] = {
id = 403680,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "萌熊信使",
desc = "我有个好消息，你要不要听?",
icon = "CDN:Icon_BU_178",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_178",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"走街串巷，风雨无阻"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitId = 399,
suitName = "萌熊信使",
bpShowId = 6,
seasonId = 8,
suitIcon = "CDN:Icon_BU_178",
SeasonShowIdList = {
{
key = 8,
value = 6
}
}
},
[403681] = {
id = 403681,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "萌熊信使",
desc = "我有个好消息，你要不要听?",
icon = "CDN:Icon_BU_178_01",
outlookConf = {
belongTo = 403680,
fashionValue = 25,
belongToGroup = {
403681
}
},
resourceConf = {
model = "SK_BU_178",
material = "MI_BU_178_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11538,
shareTexts = {
"走街串巷，风雨无阻"
}
},
[403690] = {
id = 403690,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "探乐乐",
desc = "世界那么大，我想去看看",
icon = "CDN:Icon_BU_180",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_180",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"生活是一种感觉"
},
beginTime = v3,
suitId = 500,
suitName = "探乐乐",
suitIcon = "CDN:Icon_BU_180"
},
[403691] = {
id = 403691,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "探乐乐",
desc = "世界那么大，我想去看看",
icon = "CDN:Icon_BU_180_01",
outlookConf = {
belongTo = 403690,
fashionValue = 25,
belongToGroup = {
403691
}
},
resourceConf = {
model = "SK_BU_180",
material = "MI_BU_180_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11540,
shareTexts = {
"生活是一种感觉"
}
},
[403700] = {
id = 403700,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "南瓜苒苒",
desc = "我的南瓜马车什么时候到？",
icon = "CDN:Icon_BU_182",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_182",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"不给糖，就捣蛋！"
},
beginTime = v3,
suitId = 502,
suitName = "南瓜苒苒",
suitIcon = "CDN:Icon_BU_182"
},
[403701] = {
id = 403701,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "南瓜苒苒",
desc = "我的南瓜马车什么时候到？",
icon = "CDN:Icon_BU_182_01",
outlookConf = {
belongTo = 403700,
fashionValue = 25,
belongToGroup = {
403701
}
},
resourceConf = {
model = "SK_BU_182",
material = "MI_BU_182_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11542,
shareTexts = {
"不给糖，就捣蛋！"
}
},
[403710] = {
id = 403710,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "安眠仔",
desc = "睡觉真的能删除坏心情，不信你试试",
icon = "CDN:Icon_BU_188",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_188",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"带你入睡，永不失眠"
},
beginTime = v3,
suitId = 504,
suitName = "安眠仔",
suitIcon = "CDN:Icon_BU_188"
},
[403711] = {
id = 403711,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "安眠仔",
desc = "睡觉真的能删除坏心情，不信你试试",
icon = "CDN:Icon_BU_188_01",
outlookConf = {
belongTo = 403710,
fashionValue = 25,
belongToGroup = {
403711
}
},
resourceConf = {
model = "SK_BU_188",
material = "MI_BU_188_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11544,
shareTexts = {
"带你入睡，永不失眠"
}
},
[403720] = {
id = 403720,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "夜小寐",
desc = "请尽情做梦吧！",
icon = "CDN:Icon_BU_189",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_189",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"合上双眼，进入我的世界"
},
beginTime = v3,
suitId = 506,
suitName = "夜小寐",
suitIcon = "CDN:Icon_BU_189"
},
[403721] = {
id = 403721,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "夜小寐",
desc = "请尽情做梦吧！",
icon = "CDN:Icon_BU_189_01",
outlookConf = {
belongTo = 403720,
fashionValue = 25,
belongToGroup = {
403721
}
},
resourceConf = {
model = "SK_BU_189",
material = "MI_BU_189_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11546,
shareTexts = {
"合上双眼，进入我的世界"
}
},
[403730] = {
id = 403730,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "天蝎星",
desc = "深邃而神秘，拥有无尽的魅力",
icon = "CDN:Icon_BU_192",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_192",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"神秘莫测，智慧超群"
},
beginTime = v3,
suitId = 508,
suitName = "天蝎星",
suitIcon = "CDN:Icon_BU_192"
},
[403731] = {
id = 403731,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "天蝎星",
desc = "深邃而神秘，拥有无尽的魅力",
icon = "CDN:Icon_BU_192_01",
outlookConf = {
belongTo = 403730,
fashionValue = 25,
belongToGroup = {
403731
}
},
resourceConf = {
model = "SK_BU_192",
material = "MI_BU_192_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11548,
shareTexts = {
"神秘莫测，智慧超群"
}
},
[403740] = {
id = 403740,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "筑梦师",
desc = "给荒芜画上路标，让现实直达梦想",
icon = "CDN:Icon_BU_177",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_177",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"一寸一厘，都不能有差"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitId = 510,
suitName = "筑梦师",
bpShowId = 2,
seasonId = 8,
suitIcon = "CDN:Icon_BU_177",
SeasonShowIdList = {
{
key = 8,
value = 2
}
}
},
[403741] = {
id = 403741,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "筑梦师",
desc = "给荒芜画上路标，让现实直达梦想",
icon = "CDN:Icon_BU_177_01",
outlookConf = {
belongTo = 403740,
fashionValue = 25,
belongToGroup = {
403741
}
},
resourceConf = {
model = "SK_BU_177",
material = "MI_BU_177_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11550,
shareTexts = {
"一寸一厘，都不能有差"
}
},
[403750] = {
id = 403750,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星际秘盗",
desc = "或许没有什么是不可能的",
icon = "CDN:Icon_BU_179",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_179",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"魔术师不需要不在场证明"
},
beginTime = v3,
suitId = 512,
suitName = "星际秘盗",
suitIcon = "CDN:Icon_BU_179"
},
[403751] = {
id = 403751,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星际秘盗",
desc = "或许没有什么是不可能的",
icon = "CDN:Icon_BU_179_01",
outlookConf = {
belongTo = 403750,
fashionValue = 25,
belongToGroup = {
403751
}
},
resourceConf = {
model = "SK_BU_179",
material = "MI_BU_179_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11552,
shareTexts = {
"魔术师不需要不在场证明"
}
},
[403760] = {
id = 403760,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "妮闪闪",
desc = "是精灵，不是神明，仅你可见",
icon = "CDN:Icon_BU_187",
getWay = "砍价活动",
jumpId = {
12210
},
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_187",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"偶尔皮一下，真的只是偶尔"
},
beginTime = {
seconds = 1730995200
},
suitId = 514,
suitName = "妮闪闪",
suitIcon = "CDN:Icon_BU_187"
},
[403761] = {
id = 403761,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "妮闪闪",
desc = "是精灵，不是神明，仅你可见",
icon = "CDN:Icon_BU_187_01",
outlookConf = {
belongTo = 403760,
fashionValue = 25,
belongToGroup = {
403761
}
},
resourceConf = {
model = "SK_BU_187",
material = "MI_BU_187_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11554,
shareTexts = {
"偶尔皮一下，真的只是偶尔"
}
},
[403770] = {
id = 403770,
effect = true,
name = "食梦兽 芭库",
desc = "路有无穷分叉，梦有万般变化",
icon = "CDN:Icon_PL_191",
resourceConf = {
model = "SK_PL_191",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_191_Physics"
},
outEnter = "AS_CH_Enter_PL_191",
outShow = "AS_CH_IdleShow_PL_191",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_191",
shareTexts = {
"循着星图，永不迷路！"
},
beginTime = v3,
suitStoryTextKey = [[在星河深处，有一只神秘的食梦兽芭库。芭库非常贪吃，山野间的蘑菇，餐桌上美味的蛋糕，星河中流淌的蜜露都无法喂饱他。直到有一天他无意间吃下了一个星宝的梦，美好的梦境让他大感满足。自此他四处寻找梦境吃，无论是美梦还是噩梦，来者不拒，吃饱肚子后看着离开梦境的星宝手足无措，他觉得很是开心。

大收藏家柯莱狄知晓此事后准备给芭库一点小小的教训。他将芭库自己的梦境泡泡放在芭库觅食的途中，芭库毫不知情的吃掉了自己的梦。结果晚上睡觉的时候失去梦境的他彻夜难眠，痛苦不堪。

柯莱狄现身告知了他真相，愧疚的芭库表示自己将来不会再偷吃星宝们的梦了，可是他总是吃不饱，这该怎么办呢？柯莱狄为他绘制了一份梦境星图，上面会标注噩梦和美梦，芭库只需要吃掉噩梦就行！

现在芭库晚上会准时出来帮星宝们消灭掉噩梦，虽然味道没那么好。芭库偶尔还会被美梦的甜美吸引，但他每次都控制住了欲望！越来越多的星宝知道了芭库这只可爱的食梦兽，因此即使白天饿肚子了，也会有很多星宝投喂他，芭库现在要担心的或许是会不会超重啦！]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403770.astc",
suitId = 516,
suitName = "食梦兽 芭库",
suitIcon = "CDN:Icon_PL_191",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403770.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403770.astc"
},
[403771] = {
id = 403771,
effect = true,
name = "食梦兽 芭库",
desc = "路有无穷分叉，梦有万般变化",
icon = "CDN:Icon_PL_191_01",
outlookConf = {
belongTo = 403770,
fashionValue = 35,
belongToGroup = {
403771,
403772
}
},
resourceConf = {
model = "SK_PL_191",
material = "MI_PL_191_1_HP01;MI_PL_191_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_191_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11556,
outEnter = "AS_CH_Enter_PL_191",
outShow = "AS_CH_IdleShow_PL_191",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_191",
shareTexts = {
"循着星图，永不迷路！"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403770.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403770.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403770.astc"
},
[403772] = {
id = 403772,
effect = true,
name = "食梦兽 芭库",
desc = "路有无穷分叉，梦有万般变化",
icon = "CDN:Icon_PL_191_02",
outlookConf = {
belongTo = 403770,
fashionValue = 35,
belongToGroup = {
403771,
403772
}
},
resourceConf = {
model = "SK_PL_191",
material = "MI_PL_191_1_HP02;MI_PL_191_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_191_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11557,
outEnter = "AS_CH_Enter_PL_191",
outShow = "AS_CH_IdleShow_PL_191",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_191",
shareTexts = {
"循着星图，永不迷路！"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403770.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403770.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403770.astc"
},
[403780] = {
id = 403780,
effect = true,
name = "冰雪之华 王昭君",
desc = "白梅落下之日，归去故里之时",
icon = "CDN:Icon_PL_186",
resourceConf = {
model = "SK_PL_186",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_186_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_186",
outShow = "AS_CH_IdleShow_PL_186",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_186",
shareTexts = {
"故乡的梅花开了吗？"
},
beginTime = v3,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403780.astc",
suitId = 518,
suitName = "冰雪之华 王昭君",
suitIcon = "CDN:Icon_PL_186",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403780.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_404640.astc"
},
[403790] = {
id = 403790,
effect = true,
name = "吾皇猫",
desc = "今天天气不错，想去窗前坐坐",
icon = "CDN:Icon_PL_124",
resourceConf = {
model = "SK_PL_124",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_124_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_124",
outShow = "AS_CH_IdleShow_PL_124",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_124",
shareTexts = {
"要把我分享给你最重要的人"
},
beginTime = {
seconds = 1727366400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403790.astc",
suitId = 520,
suitName = "吾皇猫",
themedId = 24,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_124",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403790.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403790.astc",
ThemedShowIdList = {
{
key = 24,
value = 2
}
}
},
[403800] = {
id = 403800,
effect = true,
name = "巴扎黑",
desc = "为什么我吃饱了还是想吃点啥",
icon = "CDN:Icon_PL_125",
resourceConf = {
model = "SK_PL_125",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_125_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_125",
outShow = "AS_CH_IdleShow_PL_125",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_125",
shareTexts = {
"我知道你需要我了"
},
beginTime = {
seconds = 1727366400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403800.astc",
suitId = 522,
suitName = "巴扎黑",
themedId = 24,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_125",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403800.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403790.astc",
ThemedShowIdList = {
{
key = 24,
value = 1
}
}
},
[403810] = {
id = 403810,
effect = true,
name = "竹韵隐侠 萌萌",
desc = "隐居竹林，遁世心明",
icon = "CDN:Icon_PL_171",
resourceConf = {
model = "SK_PL_171",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_171_Physics"
},
outEnter = "AS_CH_Enter_PL_171",
outShow = "AS_CH_IdleShow_PL_171",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_171",
shareTexts = {
"嘘，不要透露我的行踪哦~"
},
beginTime = v3,
suitStoryTextKey = [[竹林茶馆有一位温柔安静的茶艺师，名叫萌萌。她身材纤细，肌肤白皙，常穿一袭浅绿色的长裙，显得格外柔弱。她的长发总是用一根竹簪松松地挽起，偶尔几缕发丝垂在额前，更添几分娇俏。

茶馆所在的这片竹林虽紧靠丛山，却从未有猛兽袭击，仿若一层看不见的屏障护住了小镇。

一日，小镇上的星宝们正在广场嬉戏玩耍，突然一头灰熊向小镇逼近，星宝们顿时一片惊慌，纷纷找地方躲藏。然而，有一个跑得太慢的小星宝尚未找到藏身处，灰熊就已经缓缓逼近他，散发着凶狠的气息，吓得他动弹不得。

就在这危急时刻，一道身影从天而降。她身穿黑色斗篷，手握一根青竹，凌厉的眼神逼得灰熊连连后退。灰熊虽是猛兽，但此刻也显得有些犹豫。然而，它很快积蓄力量，全力扑向女子。斗篷女子毫不畏惧，双手紧握青竹，身形腾空而起，竟然将灰熊从小星宝面前拉开甩向一旁!小星宝正想向斗篷女子道谢，却见她已拖着灰熊，消失在竹林深处。

第二天，星宝们纷纷向萌萌询问昨天茶馆附近发生的事情。萌萌微笑着摇头，表示自己并没有看见什么特别的事情。

轻轻地用手指撩了撩耳边垂下的发丝，眺望着竹林，深藏功与名。]],
suitId = 524,
suitName = "竹韵隐侠 萌萌",
suitIcon = "CDN:Icon_PL_171"
},
[403811] = {
id = 403811,
effect = true,
name = "竹韵隐侠 萌萌",
desc = "隐居竹林，遁世心明",
icon = "CDN:Icon_PL_171_01",
outlookConf = {
belongTo = 403810,
fashionValue = 35,
belongToGroup = {
403811,
403812
}
},
resourceConf = {
model = "SK_PL_171",
material = "MI_PL_171_1_HP01;MI_PL_171_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_171_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11562,
outEnter = "AS_CH_Enter_PL_171",
outShow = "AS_CH_IdleShow_PL_171",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_171",
shareTexts = {
"嘘，不要透露我的行踪哦~"
}
},
[403812] = {
id = 403812,
effect = true,
name = "竹韵隐侠 萌萌",
desc = "隐居竹林，遁世心明",
icon = "CDN:Icon_PL_171_02",
outlookConf = {
belongTo = 403810,
fashionValue = 35,
belongToGroup = {
403811,
403812
}
},
resourceConf = {
model = "SK_PL_171",
material = "MI_PL_171_1_HP02;MI_PL_171_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_171_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11563,
outEnter = "AS_CH_Enter_PL_171",
outShow = "AS_CH_IdleShow_PL_171",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_171",
shareTexts = {
"嘘，不要透露我的行踪哦~"
}
},
[403820] = {
id = 403820,
effect = true,
name = "露水精灵 嘟嘟",
desc = "每一刻的努力，都在为未来铺路",
icon = "CDN:Icon_PL_172",
resourceConf = {
model = "SK_PL_172",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_172_Physics"
},
outEnter = "AS_CH_Enter_PL_172",
outShow = "AS_CH_IdleShow_PL_172",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_172",
shareTexts = {
"小小露水也有滚烫的大梦想"
},
beginTime = v3,
suitStoryTextKey = [[露水精灵嘟嘟由露水凝结而成，晶莹剔透。他在林间调皮玩耍，化作雾气，吓唬其他精灵。有的星宝问他为何不做些正事，他则笑着回答：“我不过是一颗小露水罢了，快乐最重要。”

一天，他来到一片戈壁滩。这里久旱无雨，植物们枯黄，感受到露水精灵身上的水汽后纷纷倒伏，希望他能给予帮助。嘟嘟试图唤出水滴，但失败了，这里太干燥了。他摇摇头，离开戈壁。

回到森林边缘，嘟嘟看到溪流精灵正向前流动。立刻拦住说：“前面是戈壁，再往前一定会消失的。”溪流精灵回答：“我就是要去戈壁，那里需要水。”露水精灵不解地问：“那为何是你去呢？”溪流精灵只说：“既然需要，那为什么不能是我呢。”随后消失在戈壁。

戈壁滩上，水汽终于凝结成厚厚的云层，可就差一点，水就可以落在这片土地上。露水精灵顺着朋友的痕迹，看到植物们期待的样子，终于明白了朋友的想法。他张开双手拥抱干涸的土地，“一颗露水，也有能做到的事。”

空气中的水汽终于足够，一场大雨降下带来生机。多年后，戈壁已是森林，晨曦中的枝头凝结出晶莹剔透的露水，无数精灵诞生，他们在林间嬉戏，准备踏上自己的冒险。]],
suitId = 526,
suitName = "露水精灵 嘟嘟",
suitIcon = "CDN:Icon_PL_172"
},
[403821] = {
id = 403821,
effect = true,
name = "露水精灵 嘟嘟",
desc = "每一刻的努力，都在为未来铺路",
icon = "CDN:Icon_PL_172_01",
outlookConf = {
belongTo = 403820,
fashionValue = 35,
belongToGroup = {
403821,
403822
}
},
resourceConf = {
model = "SK_PL_172",
material = "MI_PL_172_1_HP01;MI_PL_172_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_172_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11565,
outEnter = "AS_CH_Enter_PL_172",
outShow = "AS_CH_IdleShow_PL_172",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_172",
shareTexts = {
"小小露水也有滚烫的大梦想"
}
},
[403822] = {
id = 403822,
effect = true,
name = "露水精灵 嘟嘟",
desc = "每一刻的努力，都在为未来铺路",
icon = "CDN:Icon_PL_172_02",
outlookConf = {
belongTo = 403820,
fashionValue = 35,
belongToGroup = {
403821,
403822
}
},
resourceConf = {
model = "SK_PL_172",
material = "MI_PL_172_1_HP02;MI_PL_172_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_172_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11566,
outEnter = "AS_CH_Enter_PL_172",
outShow = "AS_CH_IdleShow_PL_172",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_172",
shareTexts = {
"小小露水也有滚烫的大梦想"
}
},
[403830] = {
id = 403830,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "蒸汽可可",
desc = "解决问题，要找到正确的工具",
icon = "CDN:Icon_BU_167",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_167",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"用双手创造世界"
},
beginTime = v3,
suitId = 528,
suitName = "蒸汽可可",
suitIcon = "CDN:Icon_BU_167"
},
[403831] = {
id = 403831,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "蒸汽可可",
desc = "解决问题，要找到正确的工具",
icon = "CDN:Icon_BU_167_01",
outlookConf = {
belongTo = 403830,
fashionValue = 25,
belongToGroup = {
403831
}
},
resourceConf = {
model = "SK_BU_167",
material = "MI_BU_167_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11568,
shareTexts = {
"用双手创造世界"
}
},
[403840] = {
id = 403840,
effect = true,
name = "炼星术师 尼古拉斯",
desc = "即使是微小的星屑，也可铸成最伟大作品",
icon = "CDN:Icon_PL_189",
resourceConf = {
model = "SK_PL_189",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_189_Physics"
},
outEnter = "AS_CH_Enter_PL_189",
outShow = "AS_CH_IdleShow_PL_189",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_189",
shareTexts = {
"取星河静谧，炼世间无穷"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitStoryTextKey = [[尼古拉斯是炼星世家技艺最高超的工匠，他以星屑炼制而出的作品有着难以置信的细节，即使是最刁钻的评论家也无法指责。但是，尼古拉斯的作品有一个致命的缺陷，缺乏灵气。这是因为他的心天然缺失感知情感的一部分，因此他无法理解也无法表达情感。尼古拉斯的父母说他会成为最好的炼星工匠，但也注定承受孤独。

可当尼古拉斯抬头望向无垠的夜空时，他觉得夜空也在温柔的注视他。他想要知晓那份情感，于是找到了大收藏家柯莱狄，向他寻得一份古老的星图，按照上面的指引制造药水，便可恢复情感。

尼古拉斯制造了一个特殊的容器，按照星图的指引，在晨曦中收集到孩童的笑声，在连绵的雨季获得悲伤的雨水，于林间寻得顽皮的树叶，停驻湖边祈求鱼儿的眼泪，最后在流星雨到来的那天，取下夜空的静谧和流星的热烈，药水完成了。

喝下后，尼古拉斯觉得世界都明朗了，一团火花似乎从胸中燃起，他立刻使用星屑雕刻了新的作品，栩栩如生，他终于能传达自己的情感了！

尼古拉斯前去感谢柯莱狄，可柯莱狄却神秘一笑，说那只是普通的糖水调制法，重要的是走出去感知世界的他自己呀！]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403840.astc",
suitId = 532,
suitName = "炼星术师 尼古拉斯",
bpShowId = 1,
seasonId = 8,
suitIcon = "CDN:Icon_PL_189",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403840.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
SeasonShowIdList = {
{
key = 8,
value = 1
}
}
},
[403841] = {
id = 403841,
effect = true,
name = "炼星术师 尼古拉斯",
desc = "即使是微小的星屑，也可铸成最伟大作品",
icon = "CDN:Icon_PL_189_01",
outlookConf = {
belongTo = 403840,
fashionValue = 35,
belongToGroup = {
403841,
403842
}
},
resourceConf = {
model = "SK_PL_189",
material = "MI_PL_189_1_HP01;MI_PL_189_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_189_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11578,
outEnter = "AS_CH_Enter_PL_189",
outShow = "AS_CH_IdleShow_PL_189",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_189",
shareTexts = {
"取星河静谧，炼世间无穷"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403840.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403840.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc"
},
[403842] = {
id = 403842,
effect = true,
name = "炼星术师 尼古拉斯",
desc = "即使是微小的星屑，也可铸成最伟大作品",
icon = "CDN:Icon_PL_189_02",
outlookConf = {
belongTo = 403840,
fashionValue = 35,
belongToGroup = {
403841,
403842
}
},
resourceConf = {
model = "SK_PL_189",
material = "MI_PL_189_1_HP02;MI_PL_189_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_189_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11579,
outEnter = "AS_CH_Enter_PL_189",
outShow = "AS_CH_IdleShow_PL_189",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_189",
shareTexts = {
"取星河静谧，炼世间无穷"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403840.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403840.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc"
},
[403850] = {
id = 403850,
effect = true,
name = "万事屋屋主 怀特妮",
desc = "星光万事屋，永远是星河中的避风港",
icon = "CDN:Icon_PL_190",
resourceConf = {
model = "SK_PL_190",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_190_Physics"
},
outEnter = "AS_CH_Enter_PL_190",
outShow = "AS_CH_IdleShow_PL_190",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_190",
shareTexts = {
"贩卖快乐，回收悲伤"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitStoryTextKey = [[怀特妮小时并不富裕，但一直向往美好的生活。她向大收藏家柯莱荻请教如何才能富足。柯莱荻给了她一个星愿瓶，告诉她，每帮星宝完成一个愿望，就能将愿望装入瓶中。只要星愿瓶装满，她就能拥有无限的财富。

长大后，她开了一家店，为来求助的星宝实现了许多愿望，但星愿瓶却始终差一点没装满。

一天，店铺里闯入了一只受伤的食梦兽芭库，怀特妮立马关上了大门，店铺随着星河流动，避开了黑烟的追袭。芭库伤痕累累，眨着双水汪汪的大眼睛，怀特妮心软下来，一边为他治伤，一边照顾起他的起居，晚上的时候他们就乘着晚风，一起躺在房顶上看星河。有时怀特妮会哀叹芭库的饭量实在是太大了，真是一场赔本生意。但看着芭库渐渐恢复活力，她又觉得一切都是值得的。

当芭库痊愈准备离开时，怀特妮突然发现，星愿瓶不知何时已经充满了光芒。芭库微笑着告诉她：“最后的愿望，不正是你自己许下的吗？”

怀特妮恍然大悟，原来自己想要帮助大家的愿望，正是填满星愿瓶的最后一个愿望。她明白了，帮助与关怀的心，才是柯莱荻所说的无限的财富。从此，她将店铺经营成了一家专门排忧解难的星光万事屋，成为了星河中的希望之光。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403850.astc",
suitId = 534,
suitName = "万事屋屋主 怀特妮",
bpShowId = 4,
seasonId = 8,
suitIcon = "CDN:Icon_PL_190",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403850.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
SeasonShowIdList = {
{
key = 8,
value = 4
}
}
},
[403851] = {
id = 403851,
effect = true,
name = "万事屋屋主 怀特妮",
desc = "星光万事屋，永远是星河中的避风港",
icon = "CDN:Icon_PL_190_01",
outlookConf = {
belongTo = 403850,
fashionValue = 35,
belongToGroup = {
403851,
403852
}
},
resourceConf = {
model = "SK_PL_190",
material = "MI_PL_190_1_HP01;MI_PL_190_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_190_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11581,
outEnter = "AS_CH_Enter_PL_190",
outShow = "AS_CH_IdleShow_PL_190",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_190",
shareTexts = {
"贩卖快乐，回收悲伤"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403850.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403850.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc"
},
[403852] = {
id = 403852,
effect = true,
name = "万事屋屋主 怀特妮",
desc = "星光万事屋，永远是星河中的避风港",
icon = "CDN:Icon_PL_190_02",
outlookConf = {
belongTo = 403850,
fashionValue = 35,
belongToGroup = {
403851,
403852
}
},
resourceConf = {
model = "SK_PL_190",
material = "MI_PL_190_1_HP02;MI_PL_190_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_190_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11582,
outEnter = "AS_CH_Enter_PL_190",
outShow = "AS_CH_IdleShow_PL_190",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_190",
shareTexts = {
"贩卖快乐，回收悲伤"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403850.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403850.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc"
},
[403860] = {
id = 403860,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "大收藏家 柯莱荻",
desc = "只有愿望被实现，这些收藏才有意义",
icon = "CDN:Icon_OG_026",
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_026",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_026_Physics",
emitter = "FX_CH_OG_026_Loop_Orbit"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_026_PV/Level_OG_026_Intact",
outIdle = "AS_CH_OutIdle_OG_026",
outShow = "AS_CH_IdleShow_OG_026",
outShowIntervalTime = 20,
soundId = {
4050,
4051
},
outIdle_pv = "LS_PV_OG_026_Idle",
outEnterSequence = "LS_PV_OG_026",
shareTexts = {
"黯淡的星星，也努力发光"
},
minVer = "1.3.26.1",
beginTime = {
seconds = 1729180800
},
suitStoryTextKey = [[柯莱荻出生在星河的一个古老家族，这个家族世代守护着元梦世界中各种各样的星图。星图不仅记录了星空的美丽，还蕴藏着无尽的智慧和力量。传说中，星河中漂流着一张极其重要的星图，记录了元梦星空的核心秘密。为了家族的荣耀，柯莱荻决定踏上寻找这张神秘星图的旅程。

柯莱荻对星图的热爱源于对未知星空的渴望，以及对家族荣耀的追求。柯莱荻认为只有通过不断的探索和收藏星图，才能证明自己的价值。

在一次探险中，柯莱荻来到一座被遗忘的星愿神庙。神庙中有着无数星图的碎片，散发着微弱的光芒。通过这些星图碎片，柯莱荻感受到星宝们的呼唤，了解到他们曾经的愿望和梦想。于是柯莱荻决定修复这些星图碎片，并将这些愿望一一实现。

在修复星图碎片并帮助星宝们实现愿望之后，柯莱荻的心情也随着实现愿望的路途而跌宕起伏。渐渐地，柯莱荻不再仅痴迷于收藏，而更希望能够真正帮助星宝实现愿望。

柯莱荻决定在星河建立一座“星愿博物馆”，将所有的星图展示出来，让每个微小的愿望都被看见，并让星宝们感受到愿望被实现的希望。柯莱荻不再只是一个星图收藏家，而逐渐成为了一名星愿实现者。每当找到一张星图，柯莱荻都竭尽全力去帮助星宝们实现愿望。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403860_1.astc",
suitId = 536,
suitName = "大收藏家 柯莱荻",
bpShowId = 3,
seasonId = 8,
suitIcon = "CDN:Icon_OG_026",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403860.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
SeasonShowIdList = {
{
key = 8,
value = 3
}
}
},
[403861] = {
id = 403861,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "大收藏家 柯莱荻",
desc = "只有愿望被实现，这些收藏才有意义",
icon = "CDN:Icon_OG_026_01",
outlookConf = {
belongTo = 403860,
fashionValue = 125,
belongToGroup = {
403861,
403862
}
},
resourceConf = {
model = "SK_OG_026",
material = "MI_OG_026_1_HP01;MI_OG_026_2_HP01;MI_OG_026_3_HP01;MI_OG_026_4_HP01;MI_OG_026_5_HP01;MI_OG_026_6_HP01;MI_OG_026_7_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_026_Physics",
emitter = "FX_CH_OG_026_Loop_Orbit_HP01",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Opaque_04;Skin_Translucent_01;Skin_Translucent_02"
},
commodityId = 11584,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_026_PV/Level_OG_026_Intact",
outIdle = "AS_CH_OutIdle_OG_026",
outShow = "AS_CH_IdleShow_OG_026_HP01",
outShowIntervalTime = 20,
soundId = {
4050,
4051
},
outIdle_pv = "LS_PV_OG_026_HP01_Idle",
outEnterSequence = "LS_PV_OG_026_HP01",
shareTexts = {
"黯淡的星星，也努力发光"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403860_1.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403860.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032"
},
[403862] = {
id = 403862,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "大收藏家 柯莱荻",
desc = "只有愿望被实现，这些收藏才有意义",
icon = "CDN:Icon_OG_026_02",
outlookConf = {
belongTo = 403860,
fashionValue = 125,
belongToGroup = {
403861,
403862
}
},
resourceConf = {
model = "SK_OG_026",
material = "MI_OG_026_1_HP02;MI_OG_026_2_HP02;MI_OG_026_3_HP02;MI_OG_026_4_HP02;MI_OG_026_5_HP02;MI_OG_026_6_HP02;MI_OG_026_7_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_026_Physics",
emitter = "FX_CH_OG_026_Loop_Orbit_HP02",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Opaque_04;Skin_Translucent_01;Skin_Translucent_02"
},
commodityId = 11585,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_026_PV/Level_OG_026_Intact",
outIdle = "AS_CH_OutIdle_OG_026",
outShow = "AS_CH_IdleShow_OG_026_HP02",
outShowIntervalTime = 20,
soundId = {
4050,
4051
},
outIdle_pv = "LS_PV_OG_026_HP02_Idle",
outEnterSequence = "LS_PV_OG_026_HP02",
shareTexts = {
"黯淡的星星，也努力发光"
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403860_1.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403860.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032"
},
[403870] = {
id = 403870,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "星河织梦者 诗寇蒂",
desc = "星星有话带给你，我们梦里相见",
icon = "CDN:Icon_OG_027",
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_027",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_027_Physics",
emitter = "FX_CH_OG_027_Loop_Orbit",
IconLabelId = 101
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_026_PV/Level_OG_026_Intact",
outIdle = "AS_CH_OutIdle_OG_027",
outShow = "AS_CH_IdleShow_OG_027",
outShowIntervalTime = 20,
soundId = {
4052,
4053
},
outIdle_pv = "LS_PV_OG_027_Idle",
outEnterSequence = "LS_PV_OG_027",
shareTexts = {
"循此苦旅，以达星辰"
},
beginTime = v3,
suitStoryTextKey = [[诗寇蒂来自于一个古老神秘的猫族，这个家族因星图中的某种元素而被赋予了特殊的力量，家族中比较出名的代表有魔法师哈奇。猫族中每一位都有迥异的特殊能力，而诗寇蒂除了睡得比其他猫族更久，也没有显现出任何与众不同。

然而，一次奇特的梦境改变了一切。诗寇蒂梦见一团黑烟正在侵蚀魔法师哈奇，使他丧失了魔法能力。在梦中，诗寇蒂感到极度的紧张和恐惧，努力在脑海中召唤出一位勇敢的骑士，最终赶走了黑烟，救下了哈奇。诗寇蒂原本以为这只是一个噩梦，但当哈奇诉说着，他也梦见被一位骑士拯救时，诗寇蒂才意识到，这不仅仅是梦，自己的神力似乎开始觉醒，这种神力是可以改变梦境的能力——织梦。

于是诗寇蒂编织了一个一个巨大而美丽的梦境，将星河中的所有星宝都包裹在其中，希望凭此让星宝们获得永恒的幸福。然而，随着时间的推移，诗寇蒂发现星宝们逐渐失去了活力和斗志，在梦境中浑浑噩噩无所事事。

诗寇蒂意识到，尽管梦境美丽，但它终究是虚幻的，逃避现实并不能解决问题，反而让星宝们失去了面对挑战的勇气。诗寇蒂最终亲手打破了自己编织的梦境，带着星宝们回到现实，一起面对星空的浩瀚和挑战。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403870_1.astc",
suitId = 538,
suitName = "星河织梦者 诗寇蒂",
suitIcon = "CDN:Icon_OG_027",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403870.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403860.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032"
},
[403880] = {
id = 403880,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "处女星",
desc = "理性而聪慧，永远如行云流水",
icon = "CDN:Icon_BU_193",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_193",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"追求完美，细节至上"
},
beginTime = v3,
suitId = 540,
suitName = "处女星",
suitIcon = "CDN:Icon_BU_193"
},
[403881] = {
id = 403881,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "处女星",
desc = "理性而聪慧，永远如行云流水",
icon = "CDN:Icon_BU_193_01",
outlookConf = {
belongTo = 403880,
fashionValue = 25,
belongToGroup = {
403881
}
},
resourceConf = {
model = "SK_BU_193",
material = "MI_BU_193_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11588,
shareTexts = {
"追求完美，细节至上"
}
},
[403890] = {
id = 403890,
effect = true,
name = "金属风暴 墨子",
desc = "风暴降临！",
icon = "CDN:Icon_PL_161",
resourceConf = {
model = "SK_PL_161",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_161_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_161",
outShow = "AS_CH_IdleShow_PL_161",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_161",
shareTexts = {
"见过我的朋友引擎之心吗？"
},
beginTime = v3,
suitId = 542,
suitName = "金属风暴 墨子",
suitIcon = "CDN:Icon_PL_161"
},
[403900] = {
id = 403900,
effect = true,
name = "和平守望 墨子",
desc = "生存，就是最精彩的战斗",
icon = "CDN:Icon_PL_187",
resourceConf = {
model = "SK_PL_187",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_187_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_187",
outShow = "AS_CH_IdleShow_PL_187",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_187",
shareTexts = {
"会让你印象深刻的！"
},
beginTime = v3,
suitId = 544,
suitName = "和平守望 墨子",
suitIcon = "CDN:Icon_PL_187"
},
[403910] = {
id = 403910,
effect = true,
name = "魅力之狐 妲己",
desc = "请尽情吩咐妲己，主人",
icon = "CDN:Icon_PL_222",
resourceConf = {
model = "SK_PL_222",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_222_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_222",
outShow = "AS_CH_IdleShow_PL_222",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_222",
shareTexts = {
"让妲己看看你的心"
},
beginTime = v3,
suitId = 546,
suitName = "魅力之狐 妲己",
suitIcon = "CDN:Icon_PL_222"
},
[403920] = {
id = 403920,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "田小野",
desc = "给土地一点时间，静待美好发生",
icon = "CDN:Icon_BU_183",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_183",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"莫问收获，但问耕耘"
},
beginTime = v3,
suitId = 548,
suitName = "田小野",
suitIcon = "CDN:Icon_BU_183"
},
[403921] = {
id = 403921,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "田小野",
desc = "给土地一点时间，静待美好发生",
icon = "CDN:Icon_BU_183_01",
outlookConf = {
belongTo = 403920,
fashionValue = 25,
belongToGroup = {
403921
}
},
resourceConf = {
model = "SK_BU_183",
material = "MI_BU_183_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11593,
shareTexts = {
"莫问收获，但问耕耘"
}
},
[403930] = {
id = 403930,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "小芒狗",
desc = "香香甜甜，不扰民",
icon = "CDN:Icon_BU_184",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_184",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"是你的梦中情宠吗？"
},
beginTime = v3,
suitId = 550,
suitName = "小芒狗",
suitIcon = "CDN:Icon_BU_184"
},
[403931] = {
id = 403931,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "小芒狗",
desc = "香香甜甜，不扰民",
icon = "CDN:Icon_BU_184_01",
outlookConf = {
belongTo = 403930,
fashionValue = 25,
belongToGroup = {
403931
}
},
resourceConf = {
model = "SK_BU_184",
material = "MI_BU_184_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11595,
shareTexts = {
"是你的梦中情宠吗？"
}
},
[403940] = {
id = 403940,
effect = true,
name = "丁丁",
desc = "让丁丁试试",
icon = "CDN:Icon_PL_197",
resourceConf = {
model = "SK_PL_197",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_197_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_197",
outShow = "AS_CH_IdleShow_PL_197",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_197",
shareTexts = {
"再一次，再一次！"
},
beginTime = {
seconds = 1733328000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403940.astc",
suitId = 552,
suitName = "丁丁",
themedId = 29,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_197",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403940.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403940.astc",
ThemedShowIdList = {
{
key = 29,
value = 2
}
}
},
[403941] = {
id = 403941,
effect = true,
name = "穿裙子的丁丁",
desc = "轮到丁丁穿裙子了！",
icon = "CDN:Icon_PL_242",
getWay = "天线宝宝",
jumpId = {
1098
},
outlookConf = {
belongTo = 403940,
fashionValue = 35,
belongToGroup = {
403941
}
},
resourceConf = {
model = "SK_PL_242",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_242_Physics"
},
commodityId = 11729,
outEnter = "AS_CH_Enter_PL_242",
outIdle = "AS_CH_Idle_242",
outShow = "AS_CH_IdleShow_PL_242",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_242",
shareTexts = {
"很优雅！"
},
gotDesc = "同时拥有4个天线宝宝原色外观后【前往祈愿界面】领取",
activateColorTaskId = 60002
},
[403950] = {
id = 403950,
effect = true,
name = "迪西",
desc = "迪西的帽子在哪里？",
icon = "CDN:Icon_PL_198",
resourceConf = {
model = "SK_PL_198",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_198_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_198",
outShow = "AS_CH_IdleShow_PL_198",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_198",
shareTexts = {
"迪西觉得非常快乐！"
},
beginTime = {
seconds = 1733328000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403950.astc",
suitId = 554,
suitName = "迪西",
themedId = 29,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_198",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403950.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403940.astc",
ThemedShowIdList = {
{
key = 29,
value = 1
}
}
},
[403951] = {
id = 403951,
effect = true,
name = "穿裙子的迪西",
desc = "呜啊，迪西不要穿裙子！",
icon = "CDN:Icon_PL_243",
getWay = "天线宝宝",
jumpId = {
1098
},
outlookConf = {
belongTo = 403950,
fashionValue = 35,
belongToGroup = {
403951
}
},
resourceConf = {
model = "SK_PL_243",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_243_Physics"
},
commodityId = 11730,
outEnter = "AS_CH_Enter_PL_243",
outIdle = "AS_CH_Idle_243",
outShow = "AS_CH_IdleShow_PL_243",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_243",
shareTexts = {
"跑掉咯，跑掉咯！"
},
gotDesc = "同时拥有4个天线宝宝原色外观后【前往祈愿界面】领取",
activateColorTaskId = 60002
},
[403960] = {
id = 403960,
effect = true,
name = "拉拉",
desc = "拉拉喜欢大大的拥抱",
icon = "CDN:Icon_PL_199",
resourceConf = {
model = "SK_PL_199",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_199_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_199",
outShow = "AS_CH_IdleShow_PL_199",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_199",
shareTexts = {
"拉拉在宝宝乐园里"
},
beginTime = {
seconds = 1733328000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403960.astc",
suitId = 556,
suitName = "拉拉",
themedId = 29,
bpShowId = 4,
suitIcon = "CDN:Icon_PL_199",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403960.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403940.astc",
ThemedShowIdList = {
{
key = 29,
value = 4
}
}
},
[403961] = {
id = 403961,
effect = true,
name = "穿裙子的拉拉",
desc = "拉拉很漂亮吧！",
icon = "CDN:Icon_PL_244",
getWay = "天线宝宝",
jumpId = {
1098
},
outlookConf = {
belongTo = 403960,
fashionValue = 35,
belongToGroup = {
403961
}
},
resourceConf = {
model = "SK_PL_244",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_244_Physics"
},
commodityId = 11731,
outEnter = "AS_CH_Enter_PL_244",
outIdle = "AS_CH_Idle_244",
outShow = "AS_CH_IdleShow_PL_244",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_244",
shareTexts = {
"自己编舞！"
},
gotDesc = "同时拥有4个天线宝宝原色外观后【前往祈愿界面】领取",
activateColorTaskId = 60002
},
[403970] = {
id = 403970,
effect = true,
name = "小波",
desc = "小波有很多宝宝吐司可以吃",
icon = "CDN:Icon_PL_200",
resourceConf = {
model = "SK_PL_200",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_200_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_200",
outShow = "AS_CH_IdleShow_PL_200",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_200",
shareTexts = {
"那是什么？"
},
beginTime = {
seconds = 1733328000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403970.astc",
suitId = 558,
suitName = "小波",
themedId = 29,
bpShowId = 3,
suitIcon = "CDN:Icon_PL_200",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403970.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403940.astc",
ThemedShowIdList = {
{
key = 29,
value = 3
}
}
},
[403971] = {
id = 403971,
effect = true,
name = "穿裙子的小波",
desc = "小波穿裙子跳舞啦！",
icon = "CDN:Icon_PL_245",
getWay = "天线宝宝",
jumpId = {
1098
},
outlookConf = {
belongTo = 403970,
fashionValue = 35,
belongToGroup = {
403971
}
},
resourceConf = {
model = "SK_PL_245",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_245_Physics"
},
commodityId = 11732,
outEnter = "AS_CH_Enter_PL_245",
outIdle = "AS_CH_Idle_245",
outShow = "AS_CH_IdleShow_PL_245",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_245",
shareTexts = {
"裙子，裙子！"
},
gotDesc = "同时拥有4个天线宝宝原色外观后【前往祈愿界面】领取",
activateColorTaskId = 60002
},
[403980] = {
id = 403980,
effect = true,
name = "开心超人",
desc = "吃货冠军",
icon = "CDN:Icon_PL_201",
resourceConf = {
model = "SK_PL_201",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_201_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_201",
outShow = "AS_CH_IdleShow_PL_201",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_201",
shareTexts = {
"开心超人来了！"
},
beginTime = {
seconds = 1729526400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_403980_1.astc",
suitId = 560,
suitName = "开心超人",
themedId = 26,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_201",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_403980_1.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_403980_1.astc",
ThemedShowIdList = {
{
key = 26,
value = 2
}
}
}
}

local mt = {
effect = false,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 300
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data