--com.tencent.wea.xlsRes.table_CustomRoomData => excel/xls/W_玩法模式_自定义房间.xlsx: 自定义房间玩法

local data = {
[7] = {
id = 7,
minNumber = 2,
image = "CDN:T_Customroom_Img_Mode_Big_01",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_01",
minImage = "CDN:T_Customroom_Img_Mode_Min_01",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Xiuxiansaiwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Xiuxiansaiwanfa.png",
maxObserverNum = 4,
sortOrder = 10
},
[8] = {
id = 8,
sideMemberNumber = 2,
minNumber = 4,
image = "CDN:T_Customroom_Img_Mode_Big_01",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_01",
minImage = "CDN:T_Customroom_Img_Mode_Min_01",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Xiuxiansaiwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Xiuxiansaiwanfa.png",
maxObserverNum = 4,
sortOrder = 10
},
[9] = {
id = 9,
sideMemberNumber = 4,
minNumber = 8,
image = "CDN:T_Customroom_Img_Mode_Big_01",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_01",
minImage = "CDN:T_Customroom_Img_Mode_Min_01",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Xiuxiansaiwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Xiuxiansaiwanfa.png",
maxObserverNum = 4,
sortOrder = 10
},
[110] = {
id = 110,
minNumber = 8,
image = "CDN:T_Customroom_Img_Mode_Big_50301",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50301",
minImage = "CDN:T_Customroom_Img_Mode_Min_50301",
robotSettings = {
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
maxObserverNum = 4,
sortOrder = 21,
TeamMemberViewType = 1,
bShowIdentity = true
},
[111] = {
id = 111,
minNumber = 10,
image = "CDN:T_Customroom_Img_Mode_Big_50301",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50301",
minImage = "CDN:T_Customroom_Img_Mode_Min_50301",
robotSettings = {
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
maxObserverNum = 4,
sortOrder = 22,
TeamMemberViewType = 2,
bShowIdentity = true
},
[114] = {
id = 114,
minNumber = 10,
image = "CDN:T_Customroom_Img_Mode_Big_50301",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50301",
minImage = "CDN:T_Customroom_Img_Mode_Min_50301",
robotSettings = {
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
maxObserverNum = 4,
sortOrder = 22,
TeamMemberViewType = 2,
bShowIdentity = true
},
[107] = {
id = 107,
minNumber = 12,
image = "CDN:T_Customroom_Img_Mode_Big_50301",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50301",
minImage = "CDN:T_Customroom_Img_Mode_Min_50301",
robotSettings = {
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
maxObserverNum = 4,
sortOrder = 23,
TeamMemberViewType = 3,
bShowIdentity = true
},
[108] = {
id = 108,
minNumber = 15,
image = "CDN:T_Customroom_Img_Mode_Big_50301",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50301",
minImage = "CDN:T_Customroom_Img_Mode_Min_50301",
robotSettings = {
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Shuishilangrenwanfa.png",
maxObserverNum = 4,
sortOrder = 24,
TeamMemberViewType = 4,
bShowIdentity = true
},
[104] = {
id = 104,
minNumber = 12,
image = "CDN:T_Customroom_Img_Mode_Big_50101",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50101",
minImage = "CDN:T_Customroom_Img_Mode_Min_50101",
robotSettings = {
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 5,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Duomaomaowanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Duomaomaowanfa.png",
sortOrder = 30,
TeamMemberViewType = 3,
bShowIdentity = true
},
[401] = {
id = 401,
minNumber = 2,
image = "CDN:T_Customroom_Img_Mode_Big_02",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_02",
minImage = "CDN:T_Customroom_Img_Mode_Min_02",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Daluandouwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Daluandouwanfa.png",
sortOrder = 60,
TeamMemberViewType = 1
},
[4012] = {
id = 4012,
sideMemberNumber = 4,
minNumber = 8,
image = "CDN:T_Customroom_Img_Mode_Big_02",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_02",
minImage = "CDN:T_Customroom_Img_Mode_Min_02",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Daluandouwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Daluandouwanfa.png",
sortOrder = 60
},
[501] = {
id = 501,
minNumber = 2,
image = "CDN:T_Customroom_Img_Mode_Big_50401",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50401",
minImage = "CDN:T_Customroom_Img_Mode_Min_50401",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Wuqidashihuzhanwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Wuqidashihuzhanwanfa.png",
sortOrder = 70
},
[503] = {
id = 503,
sideMemberNumber = 5,
minNumber = 10,
image = "CDN:T_Customroom_Img_Mode_Big_50402",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50402",
minImage = "CDN:T_Customroom_Img_Mode_Min_50402",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Wuqidashituanjingwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Wuqidashituanjingwanfa.png",
sortOrder = 70,
TeamMemberViewType = 5
},
[506] = {
id = 506,
sideMemberNumber = 5,
minNumber = 10,
image = "CDN:T_Customroom_Img_Mode_Big_50601",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50601",
minImage = "CDN:T_Customroom_Img_Mode_Min_50601",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Chongfengjingjiwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Chongfengjingjiwanfa.png",
sortOrder = 80,
TeamMemberViewType = 5
},
[103] = {
id = 103,
minNumber = 12,
image = "CDN:T_Customroom_Img_Mode_Big_50201",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50201",
minImage = "CDN:T_Customroom_Img_Mode_Min_50201",
robotSettings = {
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 5,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Wodixingdongwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Wodixingdongwanfa.png",
sortOrder = 110,
TeamMemberViewType = 3,
bShowIdentity = true
},
[601] = {
id = 601,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_601",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_601",
minImage = "CDN:T_Customroom_Img_Mode_Min_601",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Jixianjingsuwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Jixianjingsuwanfa.png",
sortOrder = 140
},
[602] = {
id = 602,
sideMemberNumber = 2,
minNumber = 4,
image = "CDN:T_Customroom_Img_Mode_Big_601",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_601",
minImage = "CDN:T_Customroom_Img_Mode_Min_601",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Jixianjingsuwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Jixianjingsuwanfa.png",
sortOrder = 140
},
[603] = {
id = 603,
sideMemberNumber = 4,
minNumber = 8,
image = "CDN:T_Customroom_Img_Mode_Big_601",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_601",
minImage = "CDN:T_Customroom_Img_Mode_Min_601",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Jixianjingsuwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Jixianjingsuwanfa.png",
sortOrder = 140
},
[604] = {
id = 604,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_701",
minImage = "CDN:T_Customroom_Img_Mode_Min_701",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_701.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_701.png",
sortOrder = 130,
bShowLifeRecord = true
},
[605] = {
id = 605,
sideMemberNumber = 2,
minNumber = 4,
image = "CDN:T_Customroom_Img_Mode_Big_701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_701",
minImage = "CDN:T_Customroom_Img_Mode_Min_701",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_701.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_701.png",
sortOrder = 130,
bShowLifeRecord = true
},
[606] = {
id = 606,
sideMemberNumber = 4,
minNumber = 8,
image = "CDN:T_Customroom_Img_Mode_Big_701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_701",
minImage = "CDN:T_Customroom_Img_Mode_Min_701",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_701.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_701.png",
sortOrder = 130,
bShowLifeRecord = true
},
[508] = {
id = 508,
minNumber = 2,
image = "CDN:T_Customroom_Img_Mode_Big_50801",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50801",
minImage = "CDN:T_Customroom_Img_Mode_Min_50801",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Tuweimenghuandaowanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Tuweimenghuandaowanfa.png",
sortOrder = 120
},
[509] = {
id = 509,
sideMemberNumber = 2,
minNumber = 4,
image = "CDN:T_Customroom_Img_Mode_Big_50801",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50801",
minImage = "CDN:T_Customroom_Img_Mode_Min_50801",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Tuweimenghuandaowanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Tuweimenghuandaowanfa.png",
sortOrder = 120
},
[510] = {
id = 510,
sideMemberNumber = 4,
minNumber = 8,
image = "CDN:T_Customroom_Img_Mode_Big_50801",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50801",
minImage = "CDN:T_Customroom_Img_Mode_Min_50801",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Tuweimenghuandaowanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Tuweimenghuandaowanfa.png",
sortOrder = 120
},
[350] = {
id = 350,
minNumber = 5,
image = "CDN:T_Customroom_Img_Mode_Big_354",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_350",
minImage = "CDN:T_Customroom_Img_Mode_Min_350",
robotSettings = {
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 5,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_350.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_350.png",
sideMemberDetails = "1,1;4,1",
sideNames = "暗星;星宝",
maxObserverNum = 4,
sortOrder = 40,
TeamMemberViewType = 8
},
[351] = {
id = 351,
minNumber = 9,
image = "CDN:T_Customroom_Img_Mode_Big_354",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_350",
minImage = "CDN:T_Customroom_Img_Mode_Min_350",
robotSettings = {
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 5,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_350.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_350.png",
endTime = {
seconds = 1737907200
},
sideMemberDetails = "2,1;7,1",
sideNames = "暗星;星宝",
maxObserverNum = 4,
sortOrder = 40,
TeamMemberViewType = 9
},
[353] = {
id = 353,
minNumber = 12,
image = "CDN:T_Customroom_Img_Mode_Big_354",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_350",
minImage = "CDN:T_Customroom_Img_Mode_Min_350",
robotSettings = {
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 5,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_350.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_350.png",
sideMemberDetails = "3,1;9,1",
sideNames = "暗星;星宝",
maxObserverNum = 4,
sortOrder = 40,
TeamMemberViewType = 10
},
[502] = {
id = 502,
minNumber = 16,
image = "CDN:T_Customroom_Img_Mode_Big_50501",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_50501",
minImage = "CDN:T_Customroom_Img_Mode_Min_50501",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 2,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 3,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Taotuopaiduiwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Taotuopaiduiwanfa.png",
sortOrder = 90
},
[12] = {
id = 12,
minNumber = 2,
image = "CDN:T_Customroom_Img_Mode_Big_12",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_12",
minImage = "CDN:T_Customroom_Img_Mode_Min_12",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_12.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_Customroom_Img_Mode_Small_12.png",
endTime = {
seconds = 1740672000
},
sortOrder = 50,
TeamMemberViewType = 1,
banPlatformList = {
7
}
},
[5600] = {
id = 5600,
sideMemberNumber = 3,
minNumber = 12,
image = "CDN:T_Customroom_Img_Mode_Big_56000",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_56000",
minImage = "CDN:T_Customroom_Img_Mode_Min_56000",
robotSettings = {
{
robotType = 1,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 1,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 1,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Moba3V3.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Moba3V3.png",
maxObserverNum = 4,
sortOrder = 25,
TeamMemberViewType = 7
},
[6006] = {
id = 6006,
sideMemberNumber = 2,
minNumber = 10,
image = "CDN:T_Customroom_Img_Mode_Big_57000",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_57000",
minImage = "CDN:T_Customroom_Img_Mode_Min_57000",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Moba.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Moba.png",
sortOrder = 26,
TeamMemberViewType = 6,
bUsingPakVersion = true,
banPlatformList = {
7
}
},
[380] = {
id = 380,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_380",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_380",
minImage = "CDN:T_Customroom_Img_Mode_Small_380",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_380.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_380.png",
beginTime = {
seconds = 1737108000
},
endTime = {
seconds = 1740153599
},
sortOrder = 200
},
[381] = {
id = 381,
minNumber = 5,
image = "CDN:T_Customroom_Img_Mode_Big_380",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_380",
minImage = "CDN:T_Customroom_Img_Mode_Small_380",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_380.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_380.png",
beginTime = {
seconds = 1737108000
},
endTime = {
seconds = 1740153599
},
sortOrder = 200
},
[382] = {
id = 382,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_380",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_380",
minImage = "CDN:T_Customroom_Img_Mode_Small_380",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_380.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/T_ModelSelect_Img_Type_380.png",
beginTime = {
seconds = 1737280800
},
endTime = {
seconds = 1740153599
},
sortOrder = 200
},
[808] = {
id = 808,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_80701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_80701",
minImage = "CDN:T_Customroom_Img_Mode_Min_80701",
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/807_QQ.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/807_WX.png",
sortOrder = 220
},
[809] = {
id = 809,
sideMemberNumber = 2,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_80701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_80701",
minImage = "CDN:T_Customroom_Img_Mode_Min_80701",
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/807_QQ.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/807_WX.png",
sortOrder = 220
},
[810] = {
id = 810,
sideMemberNumber = 4,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_80701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_80701",
minImage = "CDN:T_Customroom_Img_Mode_Min_80701",
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/807_QQ.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/807_WX.png",
sortOrder = 220
},
[6101] = {
id = 6101,
sideMemberNumber = 5,
minNumber = 10,
image = "CDN:T_Customroom_Img_Mode_Big_6101",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_6101",
minImage = "CDN:T_Customroom_Img_Mode_Min_6101",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Tafangdaluandouwanfa.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Tafangdaluandouwanfa.png",
maxObserverNum = 4,
sortOrder = 23,
TeamMemberViewType = 5
},
[820] = {
id = 820,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_80701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_80701",
minImage = "CDN:T_Customroom_Img_Mode_Min_80701",
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/807_QQ.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/807_WX.png",
sortOrder = 220
},
[821] = {
id = 821,
sideMemberNumber = 2,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_80701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_80701",
minImage = "CDN:T_Customroom_Img_Mode_Min_80701",
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/807_QQ.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/807_WX.png",
sortOrder = 220
},
[822] = {
id = 822,
sideMemberNumber = 4,
minNumber = 1,
image = "CDN:T_Customroom_Img_Mode_Big_80701",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_80701",
minImage = "CDN:T_Customroom_Img_Mode_Min_80701",
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/807_QQ.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/807_WX.png",
sortOrder = 220
},
[5700] = {
id = 5700,
sideMemberNumber = 3,
minNumber = 6,
image = "CDN:T_Customroom_Img_Mode_Big_58000",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_58000",
minImage = "CDN:T_Customroom_Img_Mode_Min_58000",
robotSettings = {
{
robotType = 2,
robotLevel = 2,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 5,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/HotZone_QQ.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/HotZone_WX.png",
beginTime = {
seconds = 1737648000
},
maxObserverNum = 4,
sortOrder = 27,
TeamMemberViewType = 7,
banPlatformList = {
7
}
},
[6300] = {
id = 6300,
sideMemberNumber = 5,
minNumber = 10,
image = "CDN:T_Customroom_Img_Mode_Big_57000",
thumbImage = "CDN:T_Customroom_Img_Mode_Small_57000",
minImage = "CDN:T_Customroom_Img_Mode_Min_57000",
robotSettings = {
{
robotType = 2,
robotLevel = 1,
robotLevelDes = "简单"
},
{
robotType = 2,
robotLevel = 4,
robotLevelDes = "中等"
},
{
robotType = 2,
robotLevel = 7,
robotLevelDes = "困难"
}
},
QRCode_QQ = "https://image-client.ymzx.qq.com/InviteTeam/Moba.png",
QRCode_WX = "https://image-client.ymzx.qq.com/InviteTeam/Moba.png",
sortOrder = 28,
TeamMemberViewType = 5,
bUsingPakVersion = true,
banPlatformList = {
7
}
}
}

local mt = {
sideMemberNumber = 1,
maxObserverNum = 0,
bShowIdentity = false,
bShowLifeRecord = false,
bUsingPakVersion = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data