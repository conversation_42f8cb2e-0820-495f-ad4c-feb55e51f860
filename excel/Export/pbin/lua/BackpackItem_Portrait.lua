--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 头像

local v0 = {
{
itemId = 6,
itemNum = 20
}
}

local v1 = {
{
itemId = 13,
itemNum = 50
}
}

local v2 = 0

local data = {
[860002] = {
id = 860002,
exceedReplaceItem = v0,
name = "蜡笔小新头像",
desc = "小新的饼干屋获得",
icon = "CDN:T_Head_001",
resourceConf = {
bg = "CDN:T_Head_bg_001",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/SZYTh4uA.png",
cdnFileName = "CDN:T_Head_bg_001_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1703779200
},
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[860003] = {
id = 860003,
exceedReplaceItem = v0,
name = "阿童木头像",
desc = "活动获得",
icon = "CDN:T_Head_002",
resourceConf = {
bg = "CDN:T_Head_bg_002",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/9ZenS9VU.png",
cdnFileName = "CDN:T_Head_bg_002_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[860004] = {
id = 860004,
exceedReplaceItem = v0,
name = "荷小颜头像",
desc = "桃源通行证获得",
icon = "CDN:T_Head_003",
resourceConf = {
bg = "CDN:T_Head_bg_003",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/Y149Sskf.png",
cdnFileName = "CDN:T_Head_bg_003_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1702569600
},
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[860005] = {
id = 860005,
exceedReplaceItem = v0,
name = "荷小悦头像",
desc = "桃源通行证获得",
icon = "CDN:T_Head_004",
resourceConf = {
bg = "CDN:T_Head_bg_004",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/D5GQktqj.png",
cdnFileName = "CDN:T_Head_bg_004_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1702569600
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[860006] = {
id = 860006,
exceedReplaceItem = v0,
name = "唐小僧头像",
desc = "首充获得",
icon = "CDN:T_Head_005",
resourceConf = {
bg = "CDN:T_Head_bg_005",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/6o6MqY3y.png",
cdnFileName = "CDN:T_Head_bg_005_hd"
},
isDynamic = 0,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[860007] = {
id = 860007,
exceedReplaceItem = v0,
name = "悟小空头像",
desc = "首充获得",
icon = "CDN:T_Head_006",
resourceConf = {
bg = "CDN:T_Head_bg_006",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/E0n2cf7Q.png",
cdnFileName = "CDN:T_Head_bg_006_hd"
},
isDynamic = 0,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[860008] = {
id = 860008,
exceedReplaceItem = v0,
lowVer = "1.2.67.1",
name = "小红花头像",
desc = "参与公益满天星活动获得",
icon = "CDN:T_Head_007",
resourceConf = {
bg = "CDN:T_Head_bg_007",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/NG8LOVvD.png",
cdnFileName = "CDN:T_Head_bg_007_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[860009] = {
id = 860009,
exceedReplaceItem = v0,
lowVer = "1.2.67.1",
name = "梅上仙头像",
desc = "山海通行证获得",
icon = "CDN:T_Head_008",
resourceConf = {
bg = "CDN:T_Head_bg_008",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/4YQVkbqG.png",
cdnFileName = "CDN:T_Head_bg_008_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[860010] = {
id = 860010,
exceedReplaceItem = v0,
lowVer = "1.2.67.1",
name = "梅中客头像",
desc = "山海通行证获得",
icon = "CDN:T_Head_009",
resourceConf = {
bg = "CDN:T_Head_bg_009",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/X2ENAhCx.png",
cdnFileName = "CDN:T_Head_bg_009_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[860011] = {
id = 860011,
exceedReplaceItem = v0,
lowVer = "1.2.67.1",
name = "暴暴龙头像",
desc = "通过暴暴龙盲盒获得",
icon = "CDN:T_Head_010",
resourceConf = {
bg = "CDN:T_Head_bg_010",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/Hrp3XN1X.png",
cdnFileName = "CDN:T_Head_bg_010_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1706889600
},
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[860012] = {
id = 860012,
exceedReplaceItem = v0,
lowVer = "1.2.67.1",
name = "奶龙头像",
desc = "暂无获取途径",
icon = "CDN:T_Head_011",
resourceConf = {
bg = "CDN:T_Head_bg_011",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/Ok3Gk6jx.png",
cdnFileName = "CDN:T_Head_bg_011_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[860013] = {
id = 860013,
exceedReplaceItem = v0,
lowVer = "1.2.67.1",
name = "元宵宝宝头像",
desc = "活动获得",
icon = "CDN:T_Head_012",
resourceConf = {
bg = "CDN:T_Head_bg_012",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/Wfw5YOzC.png",
cdnFileName = "CDN:T_Head_bg_012_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1706198400
},
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[860014] = {
id = 860014,
exceedReplaceItem = v0,
name = "葵贝贝头像",
desc = "时光通行证获得",
icon = "CDN:T_Head_018",
resourceConf = {
bg = "CDN:T_Head_bg_018",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/N1JB2QgR.png",
cdnFileName = "CDN:T_Head_bg_018_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[860015] = {
id = 860015,
exceedReplaceItem = v0,
name = "娱小编头像",
desc = "时光通行证获得",
icon = "CDN:T_Head_019",
resourceConf = {
bg = "CDN:T_Head_bg_019",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/4smV97fZ.png",
cdnFileName = "CDN:T_Head_bg_019_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[860016] = {
id = 860016,
exceedReplaceItem = v0,
name = "莲藕狐头像",
desc = "限时礼包获得",
icon = "CDN:T_Head_016",
resourceConf = {
bg = "CDN:T_Head_bg_016",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/u5dJIpqG.png",
cdnFileName = "CDN:T_Head_bg_016_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[860017] = {
id = 860017,
exceedReplaceItem = v0,
name = "白菜狗头像",
desc = "限时礼包获得",
icon = "CDN:T_Head_017",
resourceConf = {
bg = "CDN:T_Head_bg_017",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/j6R0xmGH.png",
cdnFileName = "CDN:T_Head_bg_017_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[860018] = {
id = 860018,
exceedReplaceItem = v0,
name = "香菇貂头像",
desc = "暂无获取途径",
icon = "CDN:T_Head_015",
resourceConf = {
bg = "CDN:T_Head_bg_015",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/VkyjAgVq.png",
cdnFileName = "CDN:T_Head_bg_015_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[860019] = {
id = 860019,
exceedReplaceItem = v0,
name = "好好鸭头像",
desc = "暂无获取途径",
icon = "CDN:T_Head_014",
resourceConf = {
bg = "CDN:T_Head_bg_014",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/4p1CYLfp.png",
cdnFileName = "CDN:T_Head_bg_014_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[860020] = {
id = 860020,
exceedReplaceItem = v0,
name = "阿宝头像",
desc = "功夫熊猫祈愿活动获得",
icon = "CDN:T_Head_020",
resourceConf = {
bg = "CDN:T_Head_bg_020",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/c4fUQOwm.png",
cdnFileName = "CDN:T_Head_bg_020_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[860021] = {
id = 860021,
exceedReplaceItem = v0,
name = "师傅头像",
desc = "功夫熊猫祈愿活动获得",
icon = "CDN:T_Head_021",
resourceConf = {
bg = "CDN:T_Head_bg_021",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/jPwshuED.png",
cdnFileName = "CDN:T_Head_bg_021_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[860022] = {
id = 860022,
exceedReplaceItem = v0,
name = "小真头像",
desc = "热购-特惠礼包限时获得",
icon = "CDN:T_Head_022",
resourceConf = {
bg = "CDN:T_Head_bg_022",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/NpBlRBLb.png",
cdnFileName = "CDN:T_Head_bg_022_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[860023] = {
id = 860023,
exceedReplaceItem = v0,
name = "【限时】狼人头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_023",
resourceConf = {
bg = "CDN:T_Head_bg_023",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/N3JThcti.png",
cdnFileName = "CDN:T_Head_bg_023_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1710432000
},
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[860024] = {
id = 860024,
exceedReplaceItem = v0,
name = "【限时】躲猫猫头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_024",
resourceConf = {
bg = "CDN:T_Head_bg_024",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/0S7YR2xv.png",
cdnFileName = "CDN:T_Head_bg_024_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[860025] = {
id = 860025,
exceedReplaceItem = v0,
name = "【限时】飞车头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_025",
resourceConf = {
bg = "CDN:T_Head_bg_025",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/K7z4X6Hh.png",
cdnFileName = "CDN:T_Head_bg_025_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1711036800
},
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[860026] = {
id = 860026,
exceedReplaceItem = v0,
name = "【限时】大乱斗头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_026",
resourceConf = {
bg = "CDN:T_Head_bg_026",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/XvVohU2o.png",
cdnFileName = "CDN:T_Head_bg_026_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[860027] = {
id = 860027,
exceedReplaceItem = v0,
name = "【限时】星宝头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_027",
resourceConf = {
bg = "CDN:T_Head_bg_027",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/Sbycra4g.png",
cdnFileName = "CDN:T_Head_bg_027_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1711641600
},
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[860028] = {
id = 860028,
exceedReplaceItem = v0,
name = "【限时】梦幻岛头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_028",
resourceConf = {
bg = "CDN:T_Head_bg_028",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/2OCN4706.png",
cdnFileName = "CDN:T_Head_bg_028_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1712851200
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[860029] = {
id = 860029,
exceedReplaceItem = v0,
name = "【限时】武器大师头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_029",
resourceConf = {
bg = "CDN:T_Head_bg_029",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/btIBdLgC.png",
cdnFileName = "CDN:T_Head_bg_029_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1712246400
},
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[860030] = {
id = 860030,
exceedReplaceItem = v0,
name = "【限时】暗星头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_031",
resourceConf = {
bg = "CDN:T_Head_bg_031",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/Mq7dqlkq.png",
cdnFileName = "CDN:T_Head_bg_031_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1711641600
},
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[860032] = {
id = 860032,
exceedReplaceItem = v0,
name = "小橘子头像",
desc = "暂无获取途径",
icon = "CDN:T_Head_032",
resourceConf = {
bg = "CDN:T_Head_bg_032",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/OGs6x4VR.png",
cdnFileName = "CDN:T_Head_bg_032_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[860034] = {
id = 860034,
exceedReplaceItem = v0,
name = "龟蜜头像",
desc = "星光剧场祈愿获得",
icon = "CDN:T_Head_034",
resourceConf = {
bg = "CDN:T_Head_bg_034",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/h2HYMBOw.png",
cdnFileName = "CDN:T_Head_bg_034_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1711728000
},
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[860035] = {
id = 860035,
exceedReplaceItem = v0,
name = "蘑咕咕头像",
desc = "星光剧场祈愿获得",
icon = "CDN:T_Head_035",
resourceConf = {
bg = "CDN:T_Head_bg_035",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/ZwNFdrzC.png",
cdnFileName = "CDN:T_Head_bg_035_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1711728000
},
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[860036] = {
id = 860036,
exceedReplaceItem = v0,
name = "木偶王子头像",
desc = "星光剧场祈愿获得",
icon = "CDN:T_Head_036",
resourceConf = {
bg = "CDN:T_Head_bg_036",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/Q0X0ydFV.png",
cdnFileName = "CDN:T_Head_bg_036_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1711728000
},
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[860037] = {
id = 860037,
exceedReplaceItem = v0,
lowVer = "1.2.100.1",
name = "冰之子头像",
desc = "暂无获取途径",
icon = "CDN:T_Head_037",
resourceConf = {
bg = "CDN:T_Head_bg_037",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/ktiED7Az.png",
cdnFileName = "CDN:T_Head_bg_037_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[860038] = {
id = 860038,
exceedReplaceItem = v0,
lowVer = "1.2.90.66",
name = "青团团头像",
desc = "初春签到礼活动获得",
icon = "CDN:T_Head_038",
resourceConf = {
bg = "CDN:T_Head_bg_038",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/UP1um0sm.png",
cdnFileName = "CDN:T_Head_bg_038_hd"
},
isDynamic = 0,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[860039] = {
id = 860039,
exceedReplaceItem = v0,
quality = 2,
name = "兔星星头像",
desc = "限时活动获得",
icon = "CDN:T_Head_039",
resourceConf = {
bg = "CDN:T_Head_bg_039",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/VOrqYxKM.png",
cdnFileName = "CDN:T_Head_bg_039_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[860040] = {
id = 860040,
exceedReplaceItem = v0,
lowVer = "1.2.100.1",
name = "柴柴必须萌头像",
desc = "活动获得",
icon = "CDN:T_Head_040",
resourceConf = {
bg = "CDN:T_Head_bg_040",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/tyqE2hyg.png",
cdnFileName = "CDN:T_Head_bg_040_hd"
},
beginTime = {
seconds = 1719504000
},
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[860044] = {
id = 860044,
exceedReplaceItem = v0,
lowVer = "1.2.100.1",
name = "钢琴家头像",
desc = "潮音通行证获得",
icon = "CDN:T_Head_044",
resourceConf = {
bg = "CDN:T_Head_bg_044",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/OAh1nG3h.png",
cdnFileName = "CDN:T_Head_bg_044_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1714060800
},
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[860045] = {
id = 860045,
exceedReplaceItem = v0,
lowVer = "1.2.100.1",
name = "歌剧少女头像",
desc = "潮音通行证获得",
icon = "CDN:T_Head_045",
resourceConf = {
bg = "CDN:T_Head_bg_045",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/SITYWvmP.png",
cdnFileName = "CDN:T_Head_bg_045_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1714060800
},
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[860041] = {
id = 860041,
exceedReplaceItem = v0,
name = "阿呆头像",
desc = "向日葵小班祈愿活动获得",
icon = "CDN:T_Head_041",
resourceConf = {
bg = "CDN:T_Head_bg_041",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/JCdBYMgL.png",
cdnFileName = "CDN:T_Head_bg_041_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1713456000
},
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[860042] = {
id = 860042,
exceedReplaceItem = v0,
name = "风间头像",
desc = "向日葵小班祈愿活动获得",
icon = "CDN:T_Head_042",
resourceConf = {
bg = "CDN:T_Head_bg_042",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/PwsVYskZ.png",
cdnFileName = "CDN:T_Head_bg_042_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1713456000
},
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[860043] = {
id = 860043,
exceedReplaceItem = v0,
name = "妮妮头像",
desc = "向日葵小班祈愿活动获得",
icon = "CDN:T_Head_043",
resourceConf = {
bg = "CDN:T_Head_bg_043",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/cqge1SEz.png",
cdnFileName = "CDN:T_Head_bg_043_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1713456000
},
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[860048] = {
id = 860048,
exceedReplaceItem = v0,
name = "鸭鸭小甜豆头像",
desc = "泡泡玛特祈愿活动获得",
icon = "CDN:T_Head_048",
resourceConf = {
bg = "CDN:T_Head_bg_048",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/of1ikXjG.png",
cdnFileName = "CDN:T_Head_bg_048_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1715270400
},
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[860049] = {
id = 860049,
exceedReplaceItem = v0,
name = "柠檬小甜豆头像",
desc = "泡泡玛特祈愿活动获得",
icon = "CDN:T_Head_049",
resourceConf = {
bg = "CDN:T_Head_bg_049",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/MhFZS5ly.png",
cdnFileName = "CDN:T_Head_bg_049_hd"
},
showInView = 0,
isDynamic = 0,
beginTime = {
seconds = 1715270400
},
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[860050] = {
id = 860050,
exceedReplaceItem = v0,
lowVer = "1.2.100.46",
quality = 2,
name = "花海守护者头像",
desc = "永恒之誓祈愿活动获得",
icon = "CDN:T_Head_055",
resourceConf = {
bg = "CDN:T_Head_bg_055",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/N4rK4Zk4.png",
cdnFileName = "CDN:T_Head_bg_055_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1715875200
},
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[860051] = {
id = 860051,
exceedReplaceItem = v0,
lowVer = "1.2.100.46",
quality = 2,
name = "守护骑士头像",
desc = "永恒之誓祈愿活动获得",
icon = "CDN:T_Head_054",
resourceConf = {
bg = "CDN:T_Head_bg_054",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/MjPLIebP.png",
cdnFileName = "CDN:T_Head_bg_054_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1715875200
},
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[860046] = {
id = 860046,
exceedReplaceItem = v0,
lowVer = "1.2.100.46",
name = "花轮头像",
desc = "限时活动获得",
icon = "CDN:T_Head_046",
resourceConf = {
bg = "CDN:T_Head_bg_046",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/AGdxVpuU.png",
cdnFileName = "CDN:T_Head_bg_046_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1714492800
},
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[860047] = {
id = 860047,
exceedReplaceItem = v0,
lowVer = "1.2.100.46",
name = "小丸子头像",
desc = "限时活动获得",
icon = "CDN:T_Head_047",
resourceConf = {
bg = "CDN:T_Head_bg_047",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/M70qKOaR.png",
cdnFileName = "CDN:T_Head_bg_047_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1714492800
},
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[860056] = {
id = 860056,
exceedReplaceItem = v0,
lowVer = "1.3.7.1",
name = "布朗熊头像",
desc = "限时活动获得",
icon = "CDN:T_Head_056",
resourceConf = {
bg = "CDN:T_Head_bg_056",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/2tx3vT2g.png",
cdnFileName = "CDN:T_Head_bg_056_hd"
},
isDynamic = 0,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[860057] = {
id = 860057,
exceedReplaceItem = v0,
lowVer = "1.3.7.1",
name = "可妮兔头像",
desc = "限时活动获得",
icon = "CDN:T_Head_057",
resourceConf = {
bg = "CDN:T_Head_bg_057",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/HbPZNj3V.png",
cdnFileName = "CDN:T_Head_bg_057_hd"
},
isDynamic = 0,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[860058] = {
id = 860058,
exceedReplaceItem = v0,
lowVer = "1.3.7.1",
name = "星小粽头像",
desc = "欢夏签到礼活动获得",
icon = "CDN:T_Head_058",
resourceConf = {
bg = "CDN:T_Head_bg_058",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/yBx20IH6.png",
cdnFileName = "CDN:T_Head_bg_058_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1717776000
},
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[860059] = {
id = 860059,
exceedReplaceItem = v0,
lowVer = "1.3.7.1",
name = "小丑鱼头像",
desc = "缤纷夏日通行证获得",
icon = "CDN:T_Head_059",
resourceConf = {
bg = "CDN:T_Head_bg_059",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/MzF4B3Qt.png",
cdnFileName = "CDN:T_Head_bg_059_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
},
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[860060] = {
id = 860060,
exceedReplaceItem = v0,
lowVer = "1.3.7.1",
name = "鱼甜甜头像",
desc = "缤纷夏日通行证获得",
icon = "CDN:T_Head_060",
resourceConf = {
bg = "CDN:T_Head_bg_060",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/g3rq2fSO.png",
cdnFileName = "CDN:T_Head_bg_060_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1717689600
},
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[860061] = {
id = 860061,
exceedReplaceItem = v0,
name = "【限时】卧底高手头像",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:T_Head_061",
resourceConf = {
bg = "CDN:T_Head_bg_061",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/f9v2XJX8.png",
cdnFileName = "CDN:T_Head_bg_061_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1718899200
},
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[860052] = {
id = 860052,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
lowVer = "1.3.7.47",
quality = 2,
name = "焰战神头像",
desc = "战神颂歌祈愿活动获得",
icon = "CDN:T_Head_071",
resourceConf = {
bg = "D_Head_bg_071",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/OLCj5r1H.png",
cdnFileName = "CDN:T_Head_bg_071_hd"
},
isDynamic = 1,
beginTime = {
seconds = 1718899200
},
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[860053] = {
id = 860053,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
lowVer = "1.3.7.47",
quality = 2,
name = "耀战神头像",
desc = "战神颂歌祈愿活动获得",
icon = "CDN:T_Head_072",
resourceConf = {
bg = "D_Head_bg_072",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/7HAxIf9r.png",
cdnFileName = "CDN:T_Head_bg_072_hd"
},
isDynamic = 1,
beginTime = {
seconds = 1718899200
},
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[860054] = {
id = 860054,
lowVer = "1.3.7.47",
quality = 2,
name = "灭战神头像",
desc = "战神颂歌祈愿活动获得",
icon = "CDN:T_Head_070",
resourceConf = {
bg = "D_Head_bg_070",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/06/28/TpkwVxT2.png",
cdnFileName = "CDN:T_Head_bg_070_hd"
},
isDynamic = 1,
beginTime = {
seconds = 1718899200
},
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[860068] = {
id = 860068,
exceedReplaceItem = v0,
lowVer = "1.3.7.1",
name = "浪漫笛音头像",
desc = "限时活动获得",
icon = "CDN:T_Head_068",
resourceConf = {
bg = "CDN:T_Head_bg_068",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/Kb9hTrsw.png",
cdnFileName = "CDN:T_Head_bg_068_hd"
},
isDynamic = 0,
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[860069] = {
id = 860069,
exceedReplaceItem = v0,
lowVer = "1.3.7.1",
name = "天才甜点师头像",
desc = "限时活动获得",
icon = "CDN:T_Head_069",
resourceConf = {
bg = "CDN:T_Head_bg_069",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/uxyObl27.png",
cdnFileName = "CDN:T_Head_bg_069_hd"
},
isDynamic = 0,
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[860962] = {
id = 860962,
name = "铠-绛天战士头像",
desc = "峡谷竞技活动获得",
icon = "CDN:T_Arena_Head_001",
resourceConf = {
bg = "CDN:T_Arena_Head_bg_001",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/YQdAkN2H.png",
cdnFileName = "CDN:T_Arena_Head_bg_001_hd"
},
isDynamic = 0,
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[860501] = {
id = 860501,
exceedReplaceItem = v1,
name = "秘术狼头像",
desc = "在【谁是狼人】玩法的兑换商店中获得",
icon = "CDN:T_Head_501",
resourceConf = {
bg = "CDN:T_Head_bg_501",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/KZBtz4Tn.png",
cdnFileName = "CDN:T_Head_bg_501_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[860502] = {
id = 860502,
exceedReplaceItem = v1,
name = "小丑头像",
desc = "在【谁是狼人】玩法的大师之路中获得",
icon = "CDN:T_Head_502",
resourceConf = {
bg = "CDN:T_Head_bg_502",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/JiVODdnC.png",
cdnFileName = "CDN:T_Head_bg_502_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[860503] = {
id = 860503,
exceedReplaceItem = v1,
name = "法医头像",
icon = "CDN:T_Head_503",
resourceConf = {
bg = "CDN:T_Head_bg_503",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/nXkHGalJ.png",
cdnFileName = "CDN:T_Head_bg_503_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[860504] = {
id = 860504,
exceedReplaceItem = v1,
name = "赌徒头像",
desc = "在【谁是狼人】玩法的兑换商店中获得",
icon = "CDN:T_Head_504",
resourceConf = {
bg = "CDN:T_Head_bg_504",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/NSGrFeUT.png",
cdnFileName = "CDN:T_Head_bg_504_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[860505] = {
id = 860505,
exceedReplaceItem = v1,
name = "占星师头像",
icon = "CDN:T_Head_505",
resourceConf = {
bg = "CDN:T_Head_bg_505",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/Zc5Nzrw8.png",
cdnFileName = "CDN:T_Head_bg_505_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[860506] = {
id = 860506,
exceedReplaceItem = v1,
name = "天使头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_506",
resourceConf = {
bg = "CDN:T_Head_bg_506",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/RDP6rRSq.png",
cdnFileName = "CDN:T_Head_bg_506_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[860507] = {
id = 860507,
exceedReplaceItem = v1,
name = "警长头像",
desc = "在【谁是狼人】玩法的大师之路中获得",
icon = "CDN:T_Head_507",
resourceConf = {
bg = "CDN:T_Head_bg_507",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/1ZwNDTbW.png",
cdnFileName = "CDN:T_Head_bg_507_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[860508] = {
id = 860508,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "刺客狼头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_508",
resourceConf = {
bg = "CDN:T_Head_bg_508",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/hLJzZPJG.png",
cdnFileName = "CDN:T_Head_bg_508_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[860509] = {
id = 860509,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "流浪汉头像",
desc = "礼包获得",
icon = "CDN:T_Head_509",
resourceConf = {
bg = "CDN:T_Head_bg_509",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/ysX5QSx7.png",
cdnFileName = "CDN:T_Head_bg_509_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[860510] = {
id = 860510,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "法官头像",
desc = "第8赛季狼人通行证中获得",
icon = "CDN:T_Head_510",
resourceConf = {
bg = "CDN:T_Head_bg_510",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/FilJi5cU.png",
cdnFileName = "CDN:T_Head_bg_510_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[860511] = {
id = 860511,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "千里眼头像",
icon = "CDN:T_Head_511",
resourceConf = {
bg = "CDN:T_Head_bg_511",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/6NeTRAQP.png",
cdnFileName = "CDN:T_Head_bg_511_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[860512] = {
id = 860512,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "通灵师头像",
desc = "第7赛季狼人通行证中获得",
icon = "CDN:T_Head_512",
resourceConf = {
bg = "CDN:T_Head_bg_512",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/FSH6s5oG.png",
cdnFileName = "CDN:T_Head_bg_512_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[860513] = {
id = 860513,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "监听员头像",
icon = "CDN:T_Head_513",
resourceConf = {
bg = "CDN:T_Head_bg_513",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/YPRZ7wuA.png",
cdnFileName = "CDN:T_Head_bg_513_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[860514] = {
id = 860514,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "黑客头像",
desc = "第12赛季狼人通行证中获得",
icon = "CDN:T_Head_514",
resourceConf = {
bg = "CDN:T_Head_bg_514",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/EnAHWiaP.png",
cdnFileName = "CDN:T_Head_bg_514_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[860515] = {
id = 860515,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "侦探头像",
desc = "第9赛季狼人通行证中获得",
icon = "CDN:T_Head_515",
resourceConf = {
bg = "CDN:T_Head_bg_515",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/c1V7Lol0.png",
cdnFileName = "CDN:T_Head_bg_515_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[860516] = {
id = 860516,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "伪装狼头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_516",
resourceConf = {
bg = "CDN:T_Head_bg_516",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/ZnfC82aa.png",
cdnFileName = "CDN:T_Head_bg_516_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[860517] = {
id = 860517,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "炸弹狼头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_517",
resourceConf = {
bg = "CDN:T_Head_bg_517",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/9RjJdomE.png",
cdnFileName = "CDN:T_Head_bg_517_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[860518] = {
id = 860518,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "潜行狼头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_518",
resourceConf = {
bg = "CDN:T_Head_bg_518",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/VG9nTMpb.png",
cdnFileName = "CDN:T_Head_bg_518_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[860519] = {
id = 860519,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "独狼头像",
desc = "第11赛季狼人通行证中获得",
icon = "CDN:T_Head_519",
resourceConf = {
bg = "CDN:T_Head_bg_519",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/hkQW46Pz.png",
cdnFileName = "CDN:T_Head_bg_519_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[860520] = {
id = 860520,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "大力狼头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_520",
resourceConf = {
bg = "CDN:T_Head_bg_520",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/tvUYR6jN.png",
cdnFileName = "CDN:T_Head_bg_520_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[860521] = {
id = 860521,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "魔爆狼头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_521",
resourceConf = {
bg = "CDN:T_Head_bg_521",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/vy8Wvbyp.png",
cdnFileName = "CDN:T_Head_bg_521_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[860522] = {
id = 860522,
exceedReplaceItem = v1,
lowVer = "1.3.18.1",
name = "赏金猎人头像",
desc = "第10赛季狼人通行证中获得",
icon = "CDN:T_Head_522",
resourceConf = {
bg = "CDN:T_Head_bg_522",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/11/07/vggUL8TW.png",
cdnFileName = "CDN:T_Head_bg_522_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[860523] = {
id = 860523,
exceedReplaceItem = v1,
lowVer = "1.3.78.1",
name = "臭鼬头像",
desc = "限时祈愿获得",
icon = "CDN:T_Head_523",
resourceConf = {
bg = "CDN:T_Head_bg_523",
cdnFileName = "CDN:T_Head_bg_523_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[860531] = {
id = 860531,
exceedReplaceItem = v1,
lowVer = "1.3.78.1",
name = "学者头像",
desc = "第13赛季狼人通行证中获得",
icon = "CDN:T_Head_531",
resourceConf = {
bg = "CDN:T_Head_bg_531",
cdnFileName = "CDN:T_Head_bg_531_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[860533] = {
id = 860533,
exceedReplaceItem = v1,
lowVer = "1.3.78.1",
name = "大明星头像",
icon = "CDN:T_Head_533",
resourceConf = {
bg = "CDN:T_Head_bg_533",
cdnFileName = "CDN:T_Head_bg_533_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[860536] = {
id = 860536,
exceedReplaceItem = v1,
lowVer = "1.3.78.1",
name = "幽灵狼头像",
icon = "CDN:T_Head_536",
resourceConf = {
bg = "CDN:T_Head_bg_536",
cdnFileName = "CDN:T_Head_bg_536_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[860544] = {
id = 860544,
exceedReplaceItem = v1,
lowVer = "1.3.78.1",
name = "捕梦者头像",
icon = "CDN:T_Head_544",
resourceConf = {
bg = "CDN:T_Head_bg_544",
cdnFileName = "CDN:T_Head_bg_544_hd"
},
showInView = 0,
isDynamic = 0,
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[860062] = {
id = 860062,
exceedReplaceItem = v0,
lowVer = "1.3.7.81",
name = "Hello Kitty头像",
desc = "限时活动获得",
icon = "CDN:T_Head_062",
resourceConf = {
bg = "CDN:T_Head_bg_062",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/8c1WeNPc.png",
cdnFileName = "CDN:T_Head_bg_062_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1720108800
},
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[860063] = {
id = 860063,
exceedReplaceItem = v0,
lowVer = "1.3.7.81",
name = "Kuromi头像",
desc = "限时活动获得",
icon = "CDN:T_Head_063",
resourceConf = {
bg = "CDN:T_Head_bg_063",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/f94YqoBI.png",
cdnFileName = "CDN:T_Head_bg_063_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1720108800
},
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[860070] = {
id = 860070,
exceedReplaceItem = v0,
quality = 2,
name = "星杯头像",
desc = "这是冠军专属的荣誉",
icon = "CDN:T_Head_073",
resourceConf = {
bg = "D_Head_bg_073",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Head_bg_trophy_hd.astc",
cdnFileName = "CDN:T_Head_bg_073_hd"
},
isDynamic = 1,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[860071] = {
id = 860071,
exceedReplaceItem = v0,
quality = 2,
name = "华妃 世兰头像",
desc = "甄嬛传祈愿活动获得",
icon = "CDN:T_Head_074",
resourceConf = {
bg = "CDN:T_Head_bg_074",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/QXCQHeDm.png",
cdnFileName = "CDN:T_Head_bg_074_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1720713600
},
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[860072] = {
id = 860072,
exceedReplaceItem = v0,
quality = 2,
name = "皇帝 四郎头像",
desc = "甄嬛传祈愿活动获得",
icon = "CDN:T_Head_075",
resourceConf = {
bg = "CDN:T_Head_bg_075",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/E2MVPrcY.png",
cdnFileName = "CDN:T_Head_bg_075_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1720713600
},
mallJumpId = {
22
},
jumpText = {
"紫色奖池-星梦者"
}
},
[860073] = {
id = 860073,
exceedReplaceItem = v0,
quality = 2,
name = "熹妃 甄嬛头像",
desc = "甄嬛传祈愿活动获得",
icon = "CDN:T_Head_076",
resourceConf = {
bg = "CDN:T_Head_bg_076",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/SqHjcokY.png",
cdnFileName = "CDN:T_Head_bg_076_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1720713600
},
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[860077] = {
id = 860077,
exceedReplaceItem = v0,
lowVer = "1.3.12.1",
name = "游乐乐头像",
desc = "甜心乐园通行证获得",
icon = "CDN:T_Head_077",
resourceConf = {
bg = "CDN:T_Head_bg_077",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/PQuVEKAZ.png",
cdnFileName = "CDN:T_Head_bg_077_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1721318400
},
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[860078] = {
id = 860078,
exceedReplaceItem = v0,
lowVer = "1.3.12.1",
name = "游美美头像",
desc = "甜心乐园通行证获得",
icon = "CDN:T_Head_078",
resourceConf = {
bg = "CDN:T_Head_bg_078",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/tbwK9Kv6.png",
cdnFileName = "CDN:T_Head_bg_078_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1721318400
},
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[860079] = {
id = 860079,
exceedReplaceItem = v0,
quality = 2,
name = "小乔头像",
desc = "峡谷幻梦祈愿活动获得",
icon = "CDN:T_Head_053",
resourceConf = {
bg = "CDN:T_Head_bg_053",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/J9oP8GBs.png",
cdnFileName = "CDN:T_Head_bg_053_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1721404800
}
},
[860080] = {
id = 860080,
exceedReplaceItem = v0,
quality = 2,
name = "兰陵王头像",
desc = "峡谷幻梦祈愿活动获得",
icon = "CDN:T_Head_051",
resourceConf = {
bg = "CDN:T_Head_bg_051",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/kfl03E9d.png",
cdnFileName = "CDN:T_Head_bg_051_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1721404800
}
},
[860081] = {
id = 860081,
name = "蔡文姬-百花神",
desc = "峡谷相逢通行证获得",
icon = "CDN:T_Arena_Head_002",
resourceConf = {
bg = "CDN:T_Arena_Head_bg_002",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/KIR95gBU.png",
cdnFileName = "CDN:T_Arena_Head_bg_002_hd"
},
showInView = 0,
isDynamic = 0
},
[860082] = {
id = 860082,
exceedReplaceItem = v0,
name = "草莓味 Toby头像",
desc = "限时活动获得",
icon = "CDN:T_Head_079",
resourceConf = {
bg = "CDN:T_Head_bg_079",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/4gDniUp0.png",
cdnFileName = "CDN:T_Head_bg_079_hd"
},
isDynamic = 0,
beginTime = {
seconds = 1722528000
}
},
[860083] = {
id = 860083,
exceedReplaceItem = v0,
name = "做鬼脸头像",
desc = "限时活动获得",
icon = "CDN:T_Head_080",
resourceConf = {
bg = "CDN:T_Head_bg_080",
cdnBg = "https://image-manage.ymzx.qq.com/wuji/2024/08/20/ceR0mnT6.png",
cdnFileName = "CDN:T_Head_bg_080_hd"
},
isDynamic = 0
}
}

local mt = {
type = "ItemType_Portrait",
maxNum = 1,
quality = 3,
showInView = 1
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data