--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_称号.xlsx: 称号

local v0 = "1.3.36.1"

local v1 = 1

local v2 = 0

local data = {
[850546] = {
id = 850546,
lowVer = v0,
name = "凤求凰"
},
[850547] = {
id = 850547,
lowVer = v0,
name = "百变小新"
},
[850548] = {
id = 850548,
lowVer = v0,
name = "小甜豆"
},
[850549] = {
id = 850549,
lowVer = v0,
name = "LuLu猪"
},
[850550] = {
id = 850550,
lowVer = v0,
name = "chiikawa",
showInView = 0
},
[850551] = {
id = 850551,
lowVer = v0,
name = "吾皇猫"
},
[850552] = {
id = 850552,
lowVer = v0,
name = "小魔仙"
},
[850553] = {
id = 850553,
lowVer = v0,
name = "开心超人"
},
[850554] = {
id = 850554,
lowVer = v0,
name = "皇家舞会"
},
[850555] = {
id = 850555,
lowVer = v0,
name = "名侦探柯南"
},
[850556] = {
id = 850556,
lowVer = v0,
name = "天线宝宝"
},
[850557] = {
id = 850557,
lowVer = v0,
name = "桃源悠梦"
},
[850558] = {
id = 850558,
lowVer = v0,
name = "山海奇遇"
},
[850559] = {
id = 850559,
lowVer = v0,
name = "时光漫游"
},
[850560] = {
id = 850560,
lowVer = v0,
name = "潮音畅想"
},
[850561] = {
id = 850561,
lowVer = v0,
name = "缤纷夏日"
},
[850562] = {
id = 850562,
lowVer = v0,
name = "甜心乐园"
},
[850563] = {
id = 850563,
lowVer = v0,
name = "精灵之森"
},
[850564] = {
id = 850564,
lowVer = v0,
name = "星语星愿"
},
[850565] = {
id = 850565,
lowVer = v0,
name = "美食派对"
},
[850566] = {
id = 850566,
lowVer = "1.3.37.69",
name = "星光熠熠",
desc = "动态称号，超核限时活动获得",
icon = "CDN:Icon_Designation_Img_174",
picture = "D_Titlebox_174",
showInView = 0,
isDynamic = 1
},
[850567] = {
id = 850567,
lowVer = "1.3.37.69",
quality = 1,
name = "山与海",
desc = "动态称号，超核限时活动获得",
icon = "CDN:Icon_Designation_Img_173",
picture = "D_Titlebox_173",
showInView = 0,
isDynamic = 1
},
[850568] = {
id = 850568,
lowVer = "1.3.37.69",
quality = 1,
name = "风和月",
desc = "动态称号，超核限时活动获得",
icon = "CDN:Icon_Designation_Img_173",
picture = "D_Titlebox_173",
showInView = 0,
isDynamic = 1
},
[850569] = {
id = 850569,
lowVer = "1.3.37.69",
quality = 1,
name = "手可摘星辰",
desc = "动态称号，超核限时活动获得",
icon = "CDN:Icon_Designation_Img_172",
picture = "D_Titlebox_172",
showInView = 0,
isDynamic = 1
},
[850570] = {
id = 850570,
lowVer = "1.3.37.87",
name = "仙境使者",
desc = "动态称号，云梦绮旅祈愿活动获得",
icon = "CDN:Icon_Designation_Img_188",
picture = "D_Titlebox_188",
isDynamic = 1,
beginTime = {
seconds = 1735660800
}
},
[850571] = {
id = 850571,
lowVer = "1.3.37.32",
name = "起床冷静期",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_177",
picture = "CDN:T_Designation_Img_177",
beginTime = {
seconds = 1734278400
}
},
[850573] = {
id = 850573,
lowVer = "1.3.37.1",
name = "元梦顶流星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_176",
picture = "CDN:T_Designation_Img_176",
beginTime = {
seconds = 1734624000
}
},
[850575] = {
id = 850575,
name = "精英伪装者",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_180",
picture = "CDN:T_Designation_Img_180",
showInView = 0
},
[850576] = {
id = 850576,
name = "最强伪装者",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_181",
picture = "CDN:T_Designation_Img_181",
showInView = 0
},
[850577] = {
id = 850577,
name = "最强搜捕者",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_178",
picture = "CDN:T_Designation_Img_178",
showInView = 0
},
[850578] = {
id = 850578,
name = "精英搜捕者",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_179",
picture = "CDN:T_Designation_Img_179",
showInView = 0
},
[850579] = {
id = 850579,
lowVer = v0,
quality = 3,
name = "资深鉴赏家",
desc = "时尚之路中达到资深鉴赏家获得",
icon = "CDN:Icon_Designation_Img_187",
picture = "CDN:T_Designation_Img_187"
},
[850580] = {
id = 850580,
lowVer = v0,
quality = 3,
name = "殿堂鉴赏家",
desc = "时尚之路中达到殿堂鉴赏家获得",
icon = "CDN:Icon_Designation_Img_186",
picture = "CDN:T_Designation_Img_186"
},
[850581] = {
id = 850581,
lowVer = v0,
name = "传说鉴赏家",
desc = "时尚之路中达到传说鉴赏家获得",
icon = "CDN:Icon_Designation_Img_185",
picture = "CDN:T_Designation_Img_185"
},
[850582] = {
id = 850582,
lowVer = v0,
quality = 1,
name = "至尊鉴赏家",
desc = "时尚之路中达到至尊鉴赏家获得",
icon = "CDN:Icon_Designation_Img_184",
picture = "CDN:T_Designation_Img_184"
},
[850584] = {
id = 850584,
lowVer = "1.3.68.1",
name = "时尚风华",
desc = "通过赛季时尚分任务获得",
showInView = 0,
beginTime = {
seconds = 1737043200
}
},
[850585] = {
id = 850585,
lowVer = "1.3.68.1",
quality = 1,
name = "大唐顶流",
desc = "通过赛季时尚分任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0,
beginTime = {
seconds = 1737043200
}
},
[850586] = {
id = 850586,
quality = 1,
name = "狼王降临",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_005",
picture = "CDN:T_Designation_Img_005",
showInView = 0
},
[850587] = {
id = 850587,
quality = 1,
name = "隐匿的狼王",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0
},
[850588] = {
id = 850588,
quality = 1,
name = "觉醒吧狼人",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0
},
[850589] = {
id = 850589,
quality = 1,
name = "真相守护者",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0
},
[850590] = {
id = 850590,
quality = 1,
name = "梦之王者",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_005",
picture = "CDN:T_Designation_Img_005",
showInView = 0
},
[850591] = {
id = 850591,
quality = 1,
name = "峡谷传说",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0
},
[850592] = {
id = 850592,
quality = 1,
name = "峡谷探险家",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0
},
[850593] = {
id = 850593,
quality = 1,
name = "叠速之王",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_005",
picture = "CDN:T_Designation_Img_005",
showInView = 0
},
[850594] = {
id = 850594,
quality = 1,
name = "疾风行者",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0
},
[850595] = {
id = 850595,
quality = 1,
name = "身法绝伦",
desc = "通过电竞赛事活动获得",
icon = "CDN:T_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0
},
[850596] = {
id = 850596,
lowVer = "1.3.68.1",
name = "风华绝代",
desc = "大唐风华通行证达到指定等级获得",
beginTime = {
seconds = 1737043200
}
},
[850597] = {
id = 850597,
lowVer = "1.3.68.1",
quality = 1,
name = "大唐风华",
desc = "大唐风华赛季限时活动获得",
icon = "CDN:Icon_Designation_Img_193",
picture = "CDN:T_Designation_Img_193",
beginTime = {
seconds = 1737043200
}
},
[850598] = {
id = 850598,
lowVer = "1.3.68.1",
name = "大唐风华"
},
[850599] = {
id = 850599,
lowVer = "1.3.68.1",
quality = 3,
name = "天选农场主",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_192",
picture = "CDN:T_Designation_Img_192",
beginTime = {
seconds = 1736697600
}
},
[850601] = {
id = 850601,
lowVer = "1.3.68.33",
quality = 1,
name = "天启圣谕",
desc = "动态称号，天启圣谕祈愿活动获得",
icon = "CDN:Icon_Designation_Img_183",
picture = "D_Titlebox_183",
isDynamic = 1,
beginTime = {
seconds = 1737993600
}
},
[850602] = {
id = 850602,
lowVer = "1.3.68.33",
quality = 1,
desc = "动态称号“我即为光明”，天启圣谕祈愿获得",
icon = "CDN:Icon_Designation_Img_194",
picture = "D_Titlebox_194",
isDynamic = 1,
beginTime = {
seconds = 1737993600
}
},
[850603] = {
id = 850603,
lowVer = "1.3.68.33",
quality = 1,
desc = "动态称号“我即是永恒”，天启圣谕祈愿获得",
icon = "CDN:Icon_Designation_Img_196",
picture = "D_Titlebox_196",
isDynamic = 1,
beginTime = {
seconds = 1737993600
}
},
[850604] = {
id = 850604,
name = "无尽探险家",
desc = "限时活动获得",
showInView = 0,
beginTime = {
seconds = 1737043200
}
},
[850605] = {
id = 850605,
name = "天天有钱花",
desc = "限时活动获得",
beginTime = {
seconds = 1737648000
}
},
[850606] = {
id = 850606,
lowVer = "1.3.68.25",
name = "生日幸运星",
desc = "生日获得",
beginTime = {
seconds = 1734192000
}
},
[850607] = {
id = 850607,
lowVer = v0,
name = "三丽鸥伙伴"
},
[850608] = {
id = 850608,
lowVer = v0,
name = "神迹降临"
},
[850609] = {
id = 850609,
lowVer = "1.3.68.1",
name = "星春游戏王",
desc = "胡闹派对乐新春活动获得",
showInView = 0
},
[850610] = {
id = 850610,
quality = 3,
name = "潮流先驱",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002",
showInView = 0
},
[850611] = {
id = 850611,
name = "烦恼清除大师",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_195",
picture = "CDN:T_Designation_Img_195",
beginTime = {
seconds = 1737388800
}
},
[850612] = {
id = 850612,
lowVer = "1.3.68.87",
name = "缘来是你",
desc = "动态称号，岚汀之约祈愿活动获得",
icon = "CDN:Icon_Designation_Img_197",
picture = "D_Titlebox_197",
isDynamic = 1,
beginTime = {
seconds = 1739462400
}
},
[850613] = {
id = 850613,
quality = 3,
name = "深情守护星",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_198",
picture = "CDN:T_Designation_Img_198",
beginTime = {
seconds = 1739203200
}
},
[850614] = {
id = 850614,
lowVer = "1.3.68.100",
quality = 1,
name = "所愿皆成真",
desc = "动态称号，桃源万千祈愿活动获得",
icon = "CDN:Icon_Designation_Img_202",
picture = "D_Titlebox_202",
isDynamic = 1,
beginTime = {
seconds = 1740067200
}
},
[850615] = {
id = 850615,
lowVer = "1.3.68.1",
name = "悠然桃源",
desc = "活动获得"
},
[850616] = {
id = 850616,
lowVer = "1.3.78.1",
name = "时尚猎手",
desc = "通过赛季时尚分任务获得",
showInView = 0,
beginTime = {
seconds = 1741881600
}
},
[850617] = {
id = 850617,
lowVer = "1.3.78.1",
quality = 1,
name = "潮流追踪者",
desc = "通过赛季时尚分任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0,
beginTime = {
seconds = 1741881600
}
},
[850618] = {
id = 850618,
lowVer = "1.3.78.1",
name = "推理大师",
desc = "真相之夜通行证达到指定等级获得",
beginTime = {
seconds = 1741881600
}
},
[850619] = {
id = 850619,
lowVer = "1.3.78.1",
quality = 1,
name = "真相之夜",
desc = "真相之夜赛季限时活动获得",
icon = "CDN:Icon_Designation_Img_206",
picture = "CDN:T_Designation_Img_206",
beginTime = {
seconds = 1741881600
}
},
[850620] = {
id = 850620,
quality = 1,
name = "两小无猜",
desc = "达成特定亲密关系后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004"
},
[850621] = {
id = 850621,
quality = 1,
name = "形影相护",
desc = "达成特定亲密关系后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004"
},
[850622] = {
id = 850622,
quality = 1,
name = "护家使者",
desc = "达成特定亲密关系后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004"
},
[850623] = {
id = 850623,
lowVer = "1.3.78.1",
name = "真相之夜",
beginTime = {
seconds = 1741881600
}
},
[850624] = {
id = 850624,
name = "千禧甜心",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_204",
picture = "CDN:T_Designation_Img_204",
beginTime = {
seconds = 1741881600
}
},
[850625] = {
id = 850625,
quality = 3,
name = "佛系摆摊",
desc = "卡牌集换活动中获得",
icon = "CDN:Icon_Designation_Img_207",
picture = "CDN:T_Designation_Img_207",
beginTime = {
seconds = 1741190400
}
},
[850626] = {
id = 850626,
quality = 3,
name = "暗中观察酱",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_208",
picture = "CDN:T_Designation_Img_208",
beginTime = {
seconds = 1741881600
}
},
[850627] = {
id = 850627,
quality = 3,
name = "奶茶星人",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_209",
picture = "CDN:T_Designation_Img_209",
beginTime = {
seconds = 1743523200
}
},
[850628] = {
id = 850628,
lowVer = "1.3.88.1",
quality = 1,
name = "绝世狼王",
desc = "参加锦标赛《狼人之夜》可以获得",
icon = "CDN:Icon_Designation_Img_210",
picture = "D_Titlebox_210",
isDynamic = 1,
beginTime = {
seconds = 1741881600
}
},
[850629] = {
id = 850629,
lowVer = "1.3.88.1",
name = "诡面狼君",
desc = "参加锦标赛《狼人之夜》可以获得",
icon = "CDN:Icon_Designation_Img_211",
picture = "CDN:T_Designation_Img_211",
beginTime = {
seconds = 1741881600
}
},
[850630] = {
id = 850630,
lowVer = "1.3.88.1",
name = "狼人巨星",
desc = "参加锦标赛《狼人之夜》可以获得",
beginTime = {
seconds = 1741881600
}
},
[850631] = {
id = 850631,
lowVer = "1.3.88.1",
quality = 3,
name = "狼人明星",
desc = "参加锦标赛《狼人之夜》可以获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002",
beginTime = {
seconds = 1741881600
}
},
[850632] = {
id = 850632,
lowVer = "1.3.88.1",
quality = 4,
name = "狼人新星",
desc = "参加锦标赛《狼人之夜》可以获得",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
beginTime = {
seconds = 1741881600
}
},
[850633] = {
id = 850633,
lowVer = "1.3.88.1",
quality = 3,
name = "满园芳华",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002",
showInView = 0
},
[850634] = {
id = 850634,
name = "快乐岛岛主",
beginTime = {
seconds = 1743696000
}
},
[850635] = {
id = 850635,
name = "甜豆控",
beginTime = {
seconds = 1743696000
}
},
[850636] = {
id = 850636,
name = "摇滚歌王",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_205",
picture = "CDN:T_Designation_Img_205",
beginTime = {
seconds = 1743609600
}
},
[850637] = {
id = 850637,
name = "主页互赞",
desc = "社区活动获得",
icon = "CDN:Icon_Designation_Img_213",
picture = "CDN:T_Designation_Img_213",
beginTime = {
seconds = 1743609600
}
},
[850638] = {
id = 850638,
quality = 3,
name = "狼宝初体验",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002",
showInView = 0,
beginTime = {
seconds = 1744819200
}
},
[850639] = {
id = 850639,
name = "兵主降临",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_219",
picture = "CDN:T_Designation_Img_219",
beginTime = {
seconds = 1744905600
}
},
[850640] = {
id = 850640,
name = "弹无虚发",
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_220",
picture = "CDN:T_Designation_Img_220",
beginTime = {
seconds = 1744905600
}
},
[850641] = {
id = 850641,
name = "鲨鱼猫",
beginTime = {
seconds = 1743696000
}
},
[850642] = {
id = 850642,
lowVer = "1.3.88.1",
name = "社团搭配新星",
desc = "通过赛季时尚分任务获得",
showInView = 0,
beginTime = {
seconds = 1746115200
}
},
[850643] = {
id = 850643,
lowVer = "1.3.88.1",
quality = 1,
name = "学园时尚部长",
desc = "通过赛季时尚分任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = "CDN:T_Designation_Img_004",
showInView = 0,
beginTime = {
seconds = 1746115200
}
},
[850644] = {
id = 850644,
name = "蜜桃猫",
beginTime = {
seconds = 1743696000
}
},
[850645] = {
id = 850645,
lowVer = "1.3.88.1",
name = "超能激荡",
desc = "超能学园通行证达到指定等级获得",
beginTime = {
seconds = 1746115200
}
},
[850646] = {
id = 850646,
lowVer = "1.3.88.1",
quality = 1,
name = "超能学园",
desc = "超能学园赛季限时活动获得",
icon = "CDN:Icon_Designation_Img_222",
picture = "CDN:T_Designation_Img_222",
beginTime = {
seconds = 1746115200
}
},
[850647] = {
id = 850647,
name = "心动信号",
desc = "限时活动获得",
icon = "CDN:Icon_Designation_Img_221",
picture = "CDN:T_Designation_Img_221",
showInView = 0,
beginTime = {
seconds = 1745856000
}
}
}

local mt = {
type = "ItemType_Title",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 4,
itemNum = 10
}
},
quality = 2,
desc = "外观图鉴收集对应套装获得",
icon = "CDN:Icon_Designation_Img_003",
picture = "CDN:T_Designation_Img_003",
showInView = 1,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data