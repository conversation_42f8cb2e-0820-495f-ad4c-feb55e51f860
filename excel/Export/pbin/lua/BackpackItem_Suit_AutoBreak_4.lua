--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local v0 = {
{
itemId = 6,
itemNum = 100
}
}

local v1 = 3

local v2 = {
fashionValue = 125
}

local v3 = 10

local data = {
[401891] = {
id = 401891,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "乐小琴",
desc = "我的乐团我做主",
icon = "CDN:Icon_BU_110_01",
outlookConf = {
belongTo = 401890,
fashionValue = 25,
belongToGroup = {
401891
}
},
resourceConf = {
model = "SK_BU_110",
material = "MI_BU_110_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11191,
shareTexts = {
"跟上节拍，给他们点小小震撼"
}
},
[401900] = {
id = 401900,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "乐小笛",
desc = "偶尔也会有点小紧张",
icon = "CDN:Icon_BU_111",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_111",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"必须不懈努力，才能站上舞台"
},
beginTime = {
seconds = 1714060800
},
suitId = 196,
suitName = "乐小笛",
bpShowId = 4,
seasonId = 4,
suitIcon = "CDN:Icon_BU_111",
SeasonShowIdList = {
{
key = 4,
value = 4
}
}
},
[401901] = {
id = 401901,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "乐小笛",
desc = "偶尔也会有点小紧张",
icon = "CDN:Icon_BU_111_01",
outlookConf = {
belongTo = 401900,
fashionValue = 25,
belongToGroup = {
401901
}
},
resourceConf = {
model = "SK_BU_111",
material = "MI_BU_111_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11193,
shareTexts = {
"必须不懈努力，才能站上舞台"
}
},
[401910] = {
id = 401910,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "元气练习生",
desc = "虽然只是练习生，但也绝对专业哦",
icon = "CDN:Icon_BU_102",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_102",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"偶像的使命，就是拯救不开心"
},
beginTime = {
seconds = 1714060800
},
suitId = 197,
suitName = "元气练习生",
bpShowId = 2,
seasonId = 4,
suitIcon = "CDN:Icon_BU_102",
SeasonShowIdList = {
{
key = 4,
value = 2
}
}
},
[401911] = {
id = 401911,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "元气练习生",
desc = "虽然只是练习生，但也绝对专业哦",
icon = "CDN:Icon_BU_102_01",
outlookConf = {
belongTo = 401910,
fashionValue = 25,
belongToGroup = {
401911
}
},
resourceConf = {
model = "SK_BU_102",
material = "MI_BU_102_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11195,
shareTexts = {
"偶像的使命，就是拯救不开心"
}
},
[401920] = {
id = 401920,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "歌剧少女",
desc = "表演是由外到内，再释放出来的",
icon = "CDN:Icon_BU_103",
getWay = "潮音通行证",
jumpId = {
9
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_103",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"生活如戏，真诚就是最好的演技"
},
beginTime = {
seconds = 1714060800
},
suitId = 198,
suitName = "歌剧少女",
suitIcon = "CDN:Icon_BU_103"
},
[401921] = {
id = 401921,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "歌剧少女",
desc = "表演是由外到内，再释放出来的",
icon = "CDN:Icon_BU_103_01",
outlookConf = {
belongTo = 401920,
fashionValue = 25,
belongToGroup = {
401921
}
},
resourceConf = {
model = "SK_BU_103",
material = "MI_BU_103_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11197,
shareTexts = {
"生活如戏，真诚就是最好的演技"
}
},
[401930] = {
id = 401930,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "钢琴师",
desc = "谱写月光的旋律，弹奏星空的乐章",
icon = "CDN:Icon_BU_104",
getWay = "潮音通行证",
jumpId = {
9
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_104",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"接下来为大家献上狂舞乐章"
},
beginTime = {
seconds = 1714060800
},
suitId = 199,
suitName = "钢琴师",
suitIcon = "CDN:Icon_BU_104"
},
[401931] = {
id = 401931,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "钢琴师",
desc = "谱写月光的旋律，弹奏星空的乐章",
icon = "CDN:Icon_BU_104_01",
outlookConf = {
belongTo = 401930,
fashionValue = 25,
belongToGroup = {
401931
}
},
resourceConf = {
model = "SK_BU_104",
material = "MI_BU_104_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11199,
shareTexts = {
"接下来为大家献上狂舞乐章"
}
},
[401940] = {
id = 401940,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "双鱼星",
desc = "感受星空的浪漫",
icon = "CDN:Icon_BU_100",
getWay = "闯关挑战",
jumpId = {
33
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_100",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"和双鱼来一场浪漫之旅吧"
},
beginTime = {
seconds = 1714060800
},
suitId = 190,
suitName = "双鱼星",
suitIcon = "CDN:Icon_BU_100"
},
[401941] = {
id = 401941,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "双鱼星",
desc = "感受星空的浪漫",
icon = "CDN:Icon_BU_100_01",
outlookConf = {
belongTo = 401940,
fashionValue = 25,
belongToGroup = {
401941
}
},
resourceConf = {
model = "SK_BU_100",
material = "MI_BU_100_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11201,
shareTexts = {
"和双鱼来一场浪漫之旅吧"
}
},
[401950] = {
id = 401950,
effect = true,
name = "柠檬小甜豆",
desc = "一口气泡水，放松一整天",
icon = "CDN:Icon_PL_078",
getWay = "泡泡玛特祈愿",
jumpId = {
181
},
resourceConf = {
model = "SK_PL_078",
material = "MI_PL_078;MI_PL_078_1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_078_Physics",
materialSlot = "Skin;Skin_02",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_078",
outIdle = "AS_CH_Idle_001_PL_078",
outShow = "AS_CH_IdleShow_PL_078",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_078",
shareTexts = {
"元气加载完毕！"
},
shareAnim = "AS_CH_Pose_001_PL_078",
beginTime = {
seconds = 1715270400
},
sharePic = "T_Share_Suit_401950_2.astc",
suitId = 217,
suitName = "柠檬小甜豆",
themedId = 6,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_078",
ThemedShowIdList = {
{
key = 6,
value = 2
},
{
key = 33,
value = 3
}
}
},
[401960] = {
id = 401960,
effect = true,
name = "鸭鸭小甜豆",
desc = "踩出小水花，真好玩",
icon = "CDN:Icon_PL_079",
getWay = "泡泡玛特祈愿",
jumpId = {
181
},
resourceConf = {
model = "SK_PL_079",
material = "MI_PL_079_1;MI_PL_079_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_079_Physics",
materialSlot = "Skin_01;Skin_02",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_079",
outIdle = "AS_CH_Idle_001_PL_079",
outShow = "AS_CH_IdleShow_PL_079",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_079",
shareTexts = {
"穿好雨衣，出发！"
},
shareAnim = "AS_CH_Pose_001_PL_079",
beginTime = {
seconds = 1715270400
},
sharePic = "T_Share_Suit_401960_2.astc",
suitId = 218,
suitName = "鸭鸭小甜豆",
themedId = 6,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_079",
ThemedShowIdList = {
{
key = 6,
value = 1
},
{
key = 33,
value = 5
}
}
},
[401970] = {
id = 401970,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "列车长",
desc = "安全可靠，是我做事的基本原则",
icon = "CDN:Icon_BU_099",
getWay = "印章祈愿",
jumpId = {
705
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_099",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"仪容和秩序一样，都不能乱"
},
beginTime = {
seconds = 1715270400
},
suitId = 226,
suitName = "列车长",
suitIcon = "CDN:Icon_BU_099"
},
[401971] = {
id = 401971,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "列车长",
desc = "安全可靠，是我做事的基本原则",
icon = "CDN:Icon_BU_099_01",
outlookConf = {
belongTo = 401970,
fashionValue = 25,
belongToGroup = {
401971
}
},
resourceConf = {
model = "SK_BU_099",
material = "MI_BU_099_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11209,
shareTexts = {
"仪容和秩序一样，都不能乱"
}
},
[401980] = {
id = 401980,
effect = true,
name = "樱桃小丸子",
desc = "我最擅长丢掉包袱，绽开笑容",
icon = "CDN:Icon_PL_080",
getWay = "小丸子便当屋",
jumpId = {
187
},
resourceConf = {
model = "SK_PL_080",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_080_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_080",
outShow = "AS_CH_IdleShow_PL_080",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_080",
shareTexts = {
"只要活着，一定会遇上好事的"
},
shareAnim = "AS_CH_Pose_001_PL_080",
beginTime = {
seconds = 1714492800
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_401980.astc",
suitId = 205,
suitName = "樱桃小丸子",
themedId = 5,
bpShowId = 3,
suitIcon = "CDN:Icon_PL_080",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_401980.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_401980.astc",
ThemedShowIdList = {
{
key = 5,
value = 3
}
}
},
[401990] = {
id = 401990,
effect = true,
name = "庆典小丸子",
desc = "幸福是不会自己走过来的，要努力争取哦！",
icon = "CDN:Icon_PL_081",
getWay = "盛装派对",
jumpId = {
188
},
resourceConf = {
model = "SK_PL_081",
material = "MI_PL_081_1;MI_PL_081_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_081_Physics",
materialSlot = "Skin_02;Skin",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_081",
outShow = "AS_CH_IdleShow_PL_081",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_081",
shareTexts = {
"懂我的人一定能明白我的魅力所在"
},
shareAnim = "AS_CH_Pose_001_PL_081",
beginTime = {
seconds = 1714492800
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_401990.astc",
suitId = 206,
suitName = "庆典小丸子",
themedId = 5,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_081",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_401990.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_401990.astc",
ThemedShowIdList = {
{
key = 5,
value = 1
}
}
},
[402000] = {
id = 402000,
effect = true,
name = "花轮和彦",
desc = "最好的礼物就是真心",
icon = "CDN:Icon_PL_082",
getWay = "盛装派对",
jumpId = {
188
},
resourceConf = {
model = "SK_PL_082",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_082_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_082",
outShow = "AS_CH_IdleShow_PL_082",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_082",
shareTexts = {
"要一起去兜风吗？"
},
shareAnim = "AS_CH_Pose_001_PL_082",
beginTime = {
seconds = 1714492800
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402000.astc",
suitId = 207,
suitName = "花轮和彦",
themedId = 5,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_082",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402000.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402000.astc",
ThemedShowIdList = {
{
key = 5,
value = 2
}
}
},
[402010] = {
id = 402010,
effect = true,
name = "小玉",
desc = "有些时候，问题的答案还是不知道的好",
icon = "CDN:Icon_PL_083",
getWay = "小丸子便当屋",
jumpId = {
187
},
resourceConf = {
model = "SK_PL_083",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_083_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_083",
outShow = "AS_CH_IdleShow_PL_083",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_083",
shareTexts = {
"要一直和小玉做好朋友哦"
},
shareAnim = "AS_CH_Pose_001_PL_083",
beginTime = {
seconds = 1714492800
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402010.astc",
suitId = 208,
suitName = "小玉",
themedId = 5,
bpShowId = 4,
suitIcon = "CDN:Icon_PL_083",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402010.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402010.astc",
ThemedShowIdList = {
{
key = 5,
value = 4
}
}
},
[402020] = {
id = 402020,
effect = true,
name = "铃铛指挥家 哆啦",
desc = "把快乐的瞬间编成乐章吧！",
icon = "CDN:Icon_PL_089",
getWay = "潮音通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_PL_089",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_089_Physics"
},
outEnter = "AS_CH_Enter_PL_089",
outShow = "AS_CH_IdleShow_PL_089",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_089_Prop",
outPropShow = "AS_CH_IdleShow_PL_089_Prop",
outPropSkeletal = "SK_PL_Prop_089",
outEnterSequence = "SQC_Enter_PL_089",
shareTexts = {
"音乐会即将开始，我来带你入场吧！"
},
shareAnim = "AS_CH_Pose_001_PL_089;AS_CH_Pose_001_PL_089_Prop",
beginTime = {
seconds = 1714060800
},
suitStoryTextKey = [[哆啦是个天生的指挥家，指挥棒在她手中像是拥有了生命，当她在舞台上抬起手时，音乐就会被赋予不同的灵魂。传说哆啦挥舞手中的指挥棒时，就连天空的鸟儿也会被吸引，并跟随着哆啦的指挥一起鸣唱。

没有演出时，哆啦的生活简单又充满乐趣，她会亲手创作一些奇奇怪怪的乐谱，即使失败了也乐此不疲；但当她进入指挥家的角色时，她就会变得认真又敬业，为每一个乐手提供最精准的指引。

为了成为最厉害的指挥家，哆啦给自己定下了一个远大的目标，她希望打造一场能够容纳所有美妙旋律，让所有人都感受幸福的演出。她向朋友们讲述了自己的伟大计划，虽然哆啦郑重其事的样子颇为有趣，但朋友们还是为她的梦想所打动，并帮助她踏上了寻找“最完美的音乐会”的旅途。

哆啦带着伙伴们的祝福和自己的愿望开始环游世界，她准备了记录旋律的乐谱，认识新朋友的礼物，和让自己快快长大的牛奶。她不在意路途是否辛苦，只是兴奋地去往哪些新奇又好玩的地方，并珍藏每一个途径之地所感受到的韵律，她坚信自己会认识更多热爱音乐，相信梦想的伙伴，然后他们会和自己一起，完成最棒的演出。]],
suitId = 204,
suitName = "铃铛指挥家 哆啦",
suitIcon = "CDN:Icon_PL_089"
},
[402021] = {
id = 402021,
effect = true,
name = "铃铛指挥家 哆啦",
desc = "把快乐的瞬间编成乐章吧！",
icon = "CDN:Icon_PL_089_01",
outlookConf = {
belongTo = 402020,
fashionValue = 35,
belongToGroup = {
402021,
402022
}
},
resourceConf = {
model = "SK_PL_089",
material = "MI_PL_089_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_089_Physics",
materialSlot = "Skin"
},
commodityId = 11215,
outEnter = "AS_CH_Enter_PL_089",
outShow = "AS_CH_IdleShow_PL_089",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_089_Prop",
outPropShow = "AS_CH_IdleShow_PL_089_Prop",
outPropSkeletal = "SK_PL_Prop_089_HP_01",
outEnterSequence = "SQC_Enter_PL_089",
shareTexts = {
"音乐会即将开始，我来带你入场吧！"
},
shareAnim = "AS_CH_Pose_001_PL_089;AS_CH_Pose_001_PL_089_Prop"
},
[402022] = {
id = 402022,
effect = true,
name = "铃铛指挥家 哆啦",
desc = "把快乐的瞬间编成乐章吧！",
icon = "CDN:Icon_PL_089_02",
outlookConf = {
belongTo = 402020,
fashionValue = 35,
belongToGroup = {
402021,
402022
}
},
resourceConf = {
model = "SK_PL_089",
material = "MI_PL_089_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_089_Physics",
materialSlot = "Skin"
},
commodityId = 11216,
outEnter = "AS_CH_Enter_PL_089",
outShow = "AS_CH_IdleShow_PL_089",
outShowIntervalTime = 10,
outPropEnter = "AS_CH_Enter_PL_089_Prop",
outPropShow = "AS_CH_IdleShow_PL_089_Prop",
outPropSkeletal = "SK_PL_Prop_089_HP_02",
outEnterSequence = "SQC_Enter_PL_089",
shareTexts = {
"音乐会即将开始，我来带你入场吧！"
},
shareAnim = "AS_CH_Pose_001_PL_089;AS_CH_Pose_001_PL_089_Prop"
},
[402030] = {
id = 402030,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "名画少女",
desc = "就像从画里走出来一样",
icon = "CDN:Icon_BU_101",
outlookConf = v2,
resourceConf = {
model = "SK_BU_101",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"潮流和艺术的完美结合"
},
beginTime = {
seconds = 4101552000
},
suitId = 952,
suitName = "名画少女",
suitIcon = "CDN:Icon_BU_101"
},
[402031] = {
id = 402031,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "名画少女",
desc = "就像从画里走出来一样",
icon = "CDN:Icon_BU_101_01",
outlookConf = {
belongTo = 402030,
fashionValue = 25,
belongToGroup = {
402031
}
},
resourceConf = {
model = "SK_BU_101",
material = "MI_BU_101_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11218,
shareTexts = {
"潮流和艺术的完美结合"
}
},
[402040] = {
id = 402040,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "绮莉莉",
desc = "我不想长大",
icon = "CDN:Icon_BU_109",
getWay = "永恒之誓",
jumpId = {
175
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_109",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"我想和你在一起，永远不分离"
},
beginTime = {
seconds = 1715875200
},
suitId = 229,
suitName = "绮莉莉",
suitIcon = "CDN:Icon_BU_109"
},
[402041] = {
id = 402041,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "绮莉莉",
desc = "我不想长大",
icon = "CDN:Icon_BU_109_01",
outlookConf = {
belongTo = 402040,
fashionValue = 25,
belongToGroup = {
402041
}
},
resourceConf = {
model = "SK_BU_109",
material = "MI_BU_109_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11220,
shareTexts = {
"我想和你在一起，永远不分离"
}
},
[402050] = {
id = 402050,
effect = true,
name = "浪漫笛音 笛娜",
desc = "我的长笛，蕴藏着治愈的力量",
icon = "CDN:Icon_PL_093",
getWay = "星光剧场",
jumpId = {
184
},
resourceConf = {
model = "SK_PL_093",
material = "MI_PL_093_1;MI_PL_093_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_093_Physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_093",
outShow = "AS_CH_IdleShow_PL_093",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_093",
shareTexts = {
"远方传来长笛声，一定有什么好消息"
},
shareAnim = "AS_CH_Pose_001_PL_093",
beginTime = {
seconds = 1715356800
},
suitStoryTextKey = [[笛娜是一位情感充沛的长笛演奏家。她善于用笛声治愈需要帮助的星宝。她的独奏会，总是带来爱和力量。在星宝的眼里，笛娜是治愈心灵的音乐女神。

笛娜出色的长笛艺术，离不开她在台下勤奋的练习。吹奏长笛需要精巧地控制气息。为了锻炼肺活量，笛娜每天都会在花园里吊嗓子。邻里的星宝们都喜欢她清如鸟啭的歌声，因为她甜美的歌声也使人充满希望。

她生活里是个安静爱美的小淑女，喜欢一切优雅又可爱的事物。她的衣橱里挂满了洛可可风格的华丽裙装。她喜欢用蝴蝶结和花园里剪下的新鲜玫瑰装饰衣裙。

在阳光明媚的休息日，笛娜常常在花园里悠闲地喝下午茶，沉浸在童话作家书写的浪漫故事里。她的书柜里藏着许多装帧精美的少女漫画，其中很多已是绝版品。这些真善美的浪漫故事常常是她灵感的源泉，使她创作出美妙的长笛乐曲。]],
sharePic = "T_Share_Suit_402050.astc",
suitId = 216,
suitName = "浪漫笛音 笛娜",
suitIcon = "CDN:Icon_PL_093"
},
[402051] = {
id = 402051,
effect = true,
name = "浪漫笛音 笛娜",
desc = "我的长笛，蕴藏着治愈的力量",
icon = "CDN:Icon_PL_093_01",
outlookConf = {
belongTo = 402050,
fashionValue = 35,
belongToGroup = {
402051,
402052
}
},
resourceConf = {
model = "SK_PL_093",
material = "MI_PL_093_1_HP01;MI_PL_093_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_093_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11222,
outEnter = "AS_CH_Enter_PL_093",
outShow = "AS_CH_IdleShow_PL_093",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_093",
shareTexts = {
"远方传来长笛声，一定有什么好消息"
},
shareAnim = "AS_CH_Pose_001_PL_093",
sharePic = "T_Share_Suit_402050.astc"
},
[402052] = {
id = 402052,
effect = true,
name = "浪漫笛音 笛娜",
desc = "我的长笛，蕴藏着治愈的力量",
icon = "CDN:Icon_PL_093_02",
outlookConf = {
belongTo = 402050,
fashionValue = 35,
belongToGroup = {
402051,
402052
}
},
resourceConf = {
model = "SK_PL_093",
material = "MI_PL_093_1_HP02;MI_PL_093_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_093_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11223,
outEnter = "AS_CH_Enter_PL_093",
outShow = "AS_CH_IdleShow_PL_093",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_093",
shareTexts = {
"远方传来长笛声，一定有什么好消息"
},
shareAnim = "AS_CH_Pose_001_PL_093",
sharePic = "T_Share_Suit_402050.astc"
},
[402060] = {
id = 402060,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "翩翩才子",
desc = "轻装策马青云路，人生从此驭长风！",
icon = "CDN:Icon_BU_106",
outlookConf = v2,
resourceConf = {
model = "SK_BU_106",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"能与阁下同游，小生不胜荣幸！"
},
beginTime = {
seconds = 1719504000
},
suitId = 279,
suitName = "翩翩才子",
suitIcon = "CDN:Icon_BU_106"
},
[402061] = {
id = 402061,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "翩翩才子",
desc = "轻装策马青云路，人生从此驭长风！",
icon = "CDN:Icon_BU_106_01",
outlookConf = {
belongTo = 402060,
fashionValue = 25,
belongToGroup = {
402061
}
},
resourceConf = {
model = "SK_BU_106",
material = "MI_BU_106_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11225,
shareTexts = {
"能与阁下同游，小生不胜荣幸！"
}
},
[402070] = {
id = 402070,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "萌星空乘",
desc = "所有航班都通向元梦世界",
icon = "CDN:Icon_BU_107",
getWay = "印章祈愿",
jumpId = {
705
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_107",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"前方有强欢乐气流，请做好准备"
},
beginTime = {
seconds = 1718899200
},
suitId = 274,
suitName = "萌星空乘",
suitIcon = "CDN:Icon_BU_107"
},
[402071] = {
id = 402071,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "萌星空乘",
desc = "所有航班都通向元梦世界",
icon = "CDN:Icon_BU_107_01",
outlookConf = {
belongTo = 402070,
fashionValue = 25,
belongToGroup = {
402071
}
},
resourceConf = {
model = "SK_BU_107",
material = "MI_BU_107_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11227,
shareTexts = {
"前方有强欢乐气流，请做好准备"
}
},
[402080] = {
id = 402080,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "拳击少女",
desc = "自律给我自信",
icon = "CDN:Icon_BU_108",
outlookConf = v2,
resourceConf = {
model = "SK_BU_108",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"小拳拳锤你胸口"
},
beginTime = {
seconds = 4101552000
},
suitId = 954,
suitName = "拳击少女",
suitIcon = "CDN:Icon_BU_108"
},
[402081] = {
id = 402081,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "拳击少女",
desc = "自律给我自信",
icon = "CDN:Icon_BU_108_01",
outlookConf = {
belongTo = 402080,
fashionValue = 25,
belongToGroup = {
402081
}
},
resourceConf = {
model = "SK_BU_108",
material = "MI_BU_108_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11229,
shareTexts = {
"小拳拳锤你胸口"
}
},
[402090] = {
id = 402090,
effect = true,
exceedReplaceItem = {
{
itemId = 211,
itemNum = 320
}
},
quality = 1,
name = "守护骑士 杰斯",
desc = "我想守护最好的世界和最好的你",
icon = "CDN:Icon_OG_011",
getWay = "永恒之誓",
jumpId = {
175
},
outlookConf = {
fashionValue = 1500
},
resourceConf = {
model = "SK_OG_011",
material = "MI_OG_011_1;MI_OG_011_2;MI_OG_011_3;MI_OG_011_4;MI_OG_011_5;MI_OG_011_6",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_011_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06",
IconLabelId = 103
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_011_PV/Level_OG_011_Intact",
outIdle = "AS_CH_OutIdle_OG_011",
outShow = "AS_CH_IdleShow_OG_011",
outShowIntervalTime = 20,
soundId = {
4020,
4022
},
outIdle_pv = "LS_PV_OG_011_Idle",
outEnterSequence = "LS_PV_OG_011",
shareTexts = {
"我在寻找我的天命星宝"
},
shareAnim = "AS_CH_Pose_001_OG_011",
beginTime = {
seconds = 1715875200
},
suitStoryTextKey = [[杰斯是著名的守护骑士，但和传说中骑士形象不太一样，杰斯不是勇猛无敌的战士，而是个帅气阳光的大男孩。

杰斯总会满腔热忱地帮助那些需要保护的星宝，不知疲倦也不畏艰难，久而久之，他的名字就成为了一种象征，当陷入困境时，呼唤杰斯的名字，他就会带着幸福到来。

后来杰斯听说在遥远的彼方，有个叫莉莉安的姑娘也和他一样守护着星宝的幸福，杰斯好奇地打听着关于莉莉安的故事，那些关于少女的美好诉说让他充满了前所未有的憧憬，和一种特殊的爱怜：如果她总是在为身旁的幸福祈愿，那她自己的幸福又该去哪里寻找呢？

杰斯决定，莉莉安的幸福，他也要守护，他一路跋涉，直到看到一片花海，有少女在莹白的光中散播希望的花瓣。

面对莉莉安询问他有什么愿望的温柔话语，杰斯没有诉说自己这一路的艰难，而是半跪着伸出手，祈愿自己能够守护莉莉安的幸福。

而在那之后，杰斯将祈愿的少女带上了幸福的旅程，世界很大，他们将会去更多的地方，散播希望与爱。]],
timeToStartAnim = 3.2300000190734863,
shareBackground = "T_Background_Share2",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402090.astc",
suitId = 219,
suitName = "守护骑士 杰斯",
themedId = 10,
bpShowId = 2,
suitIcon = "CDN:Icon_OG_011",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402090.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402090.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
ThemedShowIdList = {
{
key = 10,
value = 2
}
}
},
[402091] = {
id = 402091,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "守护骑士 杰斯",
desc = "我想守护最好的世界和最好的你",
icon = "CDN:Icon_OG_011_01",
outlookConf = {
belongTo = 402090,
fashionValue = 145,
belongToGroup = {
402091,
402092
}
},
resourceConf = {
model = "SK_OG_011",
material = "MI_OG_011_1_HP01;MI_OG_011_2_HP01;MI_OG_011_3_HP01;MI_OG_011_4_HP01;MI_OG_011_5_HP01;MI_OG_011_6_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_011_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06"
},
commodityId = 11231,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_011_PV/Level_OG_011_Intact",
outIdle = "AS_CH_OutIdle_OG_011_HP01",
outShow = "AS_CH_IdleShow_OG_011_HP01",
outShowIntervalTime = 20,
soundId = {
4020,
4022
},
outIdle_pv = "LS_PV_OG_011_HP01_Idle",
outEnterSequence = "LS_PV_OG_011_HP01",
shareTexts = {
"我在寻找我的天命星宝"
},
shareAnim = "AS_CH_Pose_001_OG_011",
timeToStartAnim = 3.2300000190734863,
shareBackground = "T_Background_Share2",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402090.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402090.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402090.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029"
},
[402092] = {
id = 402092,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "守护骑士 杰斯",
desc = "我想守护最好的世界和最好的你",
icon = "CDN:Icon_OG_011_02",
outlookConf = {
belongTo = 402090,
fashionValue = 145,
belongToGroup = {
402091,
402092
}
},
resourceConf = {
model = "SK_OG_011",
material = "MI_OG_011_1_HP02;MI_OG_011_2_HP02;MI_OG_011_3_HP02;MI_OG_011_4_HP02;MI_OG_011_5_HP02;MI_OG_011_6_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_011_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06"
},
commodityId = 11232,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_011_PV/Level_OG_011_Intact",
outIdle = "AS_CH_OutIdle_OG_011_HP02",
outShow = "AS_CH_IdleShow_OG_011_HP02",
outShowIntervalTime = 20,
soundId = {
4020,
4022
},
outIdle_pv = "LS_PV_OG_011_HP02_Idle",
outEnterSequence = "LS_PV_OG_011_HP02",
shareTexts = {
"我在寻找我的天命星宝"
},
shareAnim = "AS_CH_Pose_001_OG_011",
timeToStartAnim = 3.2300000190734863,
shareBackground = "T_Background_Share2",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402090.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402090.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402090.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029"
},
[402100] = {
id = 402100,
effect = true,
exceedReplaceItem = {
{
itemId = 211,
itemNum = 320
}
},
quality = 1,
name = "花海守护者 莉莉安",
desc = "这浪漫星辰要是能与你一起看就好了",
icon = "CDN:Icon_OG_012",
getWay = "永恒之誓",
jumpId = {
175
},
outlookConf = {
fashionValue = 1500
},
resourceConf = {
model = "SK_OG_012",
material = "MI_OG_012_1;MI_OG_012_2;MI_OG_012_3;MI_OG_012_4;MI_OG_012_5;MI_OG_012_6;MI_OG_012_7",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_012_Physics",
materialSlot = "Skin;Skin_2;Skin_3;Skin_4;Skin_5;Skin_6;Skin_7",
IconLabelId = 103
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_011_PV/Level_OG_011_Intact",
outIdle = "AS_CH_OutIdle_OG_012",
outShow = "AS_CH_IdleShow_OG_012",
outShowIntervalTime = 20,
soundId = {
4021,
4023
},
outIdle_pv = "LS_PV_OG_012_Idle",
outEnterSequence = "LS_PV_OG_012",
shareTexts = {
"我的天命星宝会在哪里呢？"
},
shareAnim = "AS_CH_Pose_001_OG_012",
beginTime = {
seconds = 1715875200
},
suitStoryTextKey = [[莉莉安是一个花海守护者，她用鲜花之力，将祈愿和祝福散播给了她所关心的一切，默默守护星宝。她的周围总是鲜花盛放，星月交辉，这是她送出的那些祝福凝聚编织的景象。

当然，莉莉安也有自己的小愿望，那就是遇到属于自己情投意合的星宝，在布满繁花的家园里度过美好的一生。但她总是把大家的幸福放在自己的愿望之前，即便偶尔感到孤单，但依然坚守在需要她的地方。

直到有一次为了回应一个星宝的请求，莉莉安动身前往那个地方，却在路上遭遇未知的迷雾迷失了方向，正当她感到无助之际，一个帅气的男孩突破迷雾来到了她身旁，并将她带到了目的地。

莉莉安一边为请愿者祈福，一边偷偷打量着那个找到她又一路保护她的男孩，却发现男孩也一直看向她的方向，这小小的插曲让莉莉安感到有些害羞，但心里又泛起小小的欢喜，作为守护大家幸福的天使，她也终于感受到了被守护的温暖和幸福。

当莉莉安要启程离开时，叫杰斯的男生再次出现在她身边，他告诉莉莉安，他一路修行，就是为了能够守护一切幸福的存在，自己曾在远方听过莉莉安的故事，如果可以，他希望由自己守护莉莉安。

“你守护其他星宝，我守护你。”杰斯温暖的话和坚定的眼神，驱散了莉莉安心中那份孤单，她向杰斯伸出自己的手，双手交握间，莉莉安能够感受到，未来的世界，一定会更加幸福。]],
timeToStartAnim = 3.2300000190734863,
shareBackground = "T_Background_Share2",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402100.astc",
suitId = 220,
suitName = "花海守护者 莉莉安",
themedId = 10,
bpShowId = 1,
suitIcon = "CDN:Icon_OG_012",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402100.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402090.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
ThemedShowIdList = {
{
key = 10,
value = 1
}
}
},
[402101] = {
id = 402101,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "花海守护者 莉莉安",
desc = "这浪漫星辰要是能与你一起看就好了",
icon = "CDN:Icon_OG_012_01",
outlookConf = {
belongTo = 402100,
fashionValue = 145,
belongToGroup = {
402101,
402102
}
},
resourceConf = {
model = "SK_OG_012",
material = "MI_OG_012_1_HP01;MI_OG_012_2_HP01;MI_OG_012_3_HP01;MI_OG_012_4_HP01;MI_OG_012_5_HP01;MI_OG_012_6_HP01;MI_OG_012_7_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_012_Physics",
materialSlot = "Skin;Skin_2;Skin_3;Skin_4;Skin_5;Skin_6;Skin_7"
},
commodityId = 11234,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_011_PV/Level_OG_011_Intact",
outIdle = "AS_CH_OutIdle_OG_012_HP01",
outShow = "AS_CH_IdleShow_OG_012_HP01",
outShowIntervalTime = 20,
soundId = {
4021,
4023
},
outIdle_pv = "LS_PV_OG_012_HP01_Idle",
outEnterSequence = "LS_PV_OG_012_HP01",
shareTexts = {
"我的天命星宝会在哪里呢？"
},
shareAnim = "AS_CH_Pose_001_OG_012",
timeToStartAnim = 3.2300000190734863,
shareBackground = "T_Background_Share2",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402100.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402100.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402090.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029"
},
[402102] = {
id = 402102,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "花海守护者 莉莉安",
desc = "这浪漫星辰要是能与你一起看就好了",
icon = "CDN:Icon_OG_012_02",
outlookConf = {
belongTo = 402100,
fashionValue = 145,
belongToGroup = {
402101,
402102
}
},
resourceConf = {
model = "SK_OG_012",
material = "MI_OG_012_1_HP02;MI_OG_012_2_HP02;MI_OG_012_3_HP02;MI_OG_012_4_HP02;MI_OG_012_5_HP02;MI_OG_012_6_HP02;MI_OG_012_7_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_012_Physics",
materialSlot = "Skin;Skin_2;Skin_3;Skin_4;Skin_5;Skin_6;Skin_7"
},
commodityId = 11235,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_011_PV/Level_OG_011_Intact",
outIdle = "AS_CH_OutIdle_OG_012_HP02",
outShow = "AS_CH_IdleShow_OG_012_HP02",
outShowIntervalTime = 20,
soundId = {
4021,
4023
},
outIdle_pv = "LS_PV_OG_012_HP02_Idle",
outEnterSequence = "LS_PV_OG_012_HP02",
shareTexts = {
"我的天命星宝会在哪里呢？"
},
shareAnim = "AS_CH_Pose_001_OG_012",
timeToStartAnim = 3.2300000190734863,
shareBackground = "T_Background_Share2",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402100.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402100.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402090.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029"
},
[402110] = {
id = 402110,
effect = true,
name = "绛天战甲 铠",
desc = "催生智能的，是我们的孤独",
icon = "CDN:Icon_PL_084",
resourceConf = {
model = "SK_PL_084",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_084_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_084",
outShow = "AS_CH_IdleShow_PL_084",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_084",
shareTexts = {
"永远向更强者挥剑！"
},
shareAnim = "AS_CH_Pose_001_PL_084",
beginTime = {
seconds = 4101552000
},
suitId = 221,
suitName = "绛天战甲 铠",
suitIcon = "CDN:Icon_PL_084"
},
[402120] = {
id = 402120,
effect = true,
name = "挚爱之约 孙策",
desc = "从每个不同的起点，奔向同一个你",
icon = "CDN:Icon_PL_085",
resourceConf = {
model = "SK_PL_085",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_085_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_085",
outShow = "AS_CH_IdleShow_PL_085",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_085",
shareTexts = {
"如果不出去看看，你会以为城堡就是全世界"
},
shareAnim = "AS_CH_Pose_001_PL_085",
beginTime = {
seconds = 4101552000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402120.astc",
suitId = 222,
suitName = "挚爱之约 孙策",
suitIcon = "CDN:Icon_PL_085",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402120.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402120.astc"
},
[402130] = {
id = 402130,
effect = true,
name = "音你心动 小乔",
desc = "去追梦吧，总有一天会遇见对的人",
icon = "CDN:Icon_PL_086",
resourceConf = {
model = "SK_PL_086",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_086_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_086",
outShow = "AS_CH_IdleShow_PL_086",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_086",
shareTexts = {
"相信天意，更相信不肯放弃的心！"
},
shareAnim = "AS_CH_Pose_001_PL_086",
beginTime = {
seconds = 1721404800
},
sharePic = "T_Share_Suit_405052.astc",
suitId = 223,
suitName = "音你心动 小乔",
suitIcon = "CDN:Icon_PL_086",
shareNamePic = "T_Share_Suit_name_405052.astc",
shareBubblePic = "T_Share_Suit_frame_405053.astc"
},
[402140] = {
id = 402140,
effect = true,
name = "影龙天霄 兰陵王",
desc = "行影无踪，潜龙化刃！",
icon = "CDN:Icon_PL_087",
resourceConf = {
model = "SK_PL_087",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_087_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_087",
outShow = "AS_CH_IdleShow_PL_087",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_087",
shareTexts = {
"执念无形，皆化龙鸣"
},
shareAnim = "AS_CH_Pose_001_PL_087",
beginTime = {
seconds = 1721404800
},
sharePic = "T_Share_Suit_405053.astc",
suitId = 224,
suitName = "影龙天霄 兰陵王",
suitIcon = "CDN:Icon_PL_087",
shareNamePic = "T_Share_Suit_name_405053.astc",
shareBubblePic = "T_Share_Suit_frame_405053.astc"
},
[402150] = {
id = 402150,
effect = true,
name = "飞燕仙 轻羽",
desc = "似曾相识燕归来",
icon = "CDN:Icon_PL_101",
getWay = "轻羽仙子",
jumpId = {
1033
},
resourceConf = {
model = "SK_PL_101",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_101_Physics"
},
outEnter = "AS_CH_Enter_PL_101",
outShow = "AS_CH_IdleShow_PL_101",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_101",
shareTexts = {
"落花人独立，微雨燕双飞"
},
shareAnim = "AS_CH_Pose_001_PL_101",
beginTime = {
seconds = 1717084800
},
suitStoryTextKey = [[和其他大多数隐居山林的上仙不同，飞燕仙·鸢语常现身于闹事，因为她制作的纸鸢非常受星宝们的欢迎，她也很乐于和星宝们分享纸鸢。这些纸鸢不光漂亮，还能感知和放大周围的快乐情绪。只要大家快乐，纸鸢便会悠然自得地在空中翩翩起舞，引来更多的欢声笑语。

可是突然有一天，鸢语的所有纸鸢都飞不起来了。原来最近慕名而来的星宝越来越多，大家经常因为争抢纸鸢而产生争执，渐渐变得对身边的其他事物都漠不关心，快乐也越来越少。

鸢语意识到，想让星宝们找回快乐，关键是让他们重新注意到身边的美好事物，从而放下争执。于是，她制作了一个前所未有的巨大纸鸢，拉着星宝们乘上纸鸢去感受久违的大自然。

鸢语操控着她的巨大纸鸢，带着星宝们来到了蓝天白云下的大森林。星宝们看到了绿树成荫、鸟语花香的美景，感受到了阳光温暖、微风拂面的舒适。大家将平日里的争执抛诸脑后，在森林中一起开心地野餐。在鸢语的引导下，星宝们开始重新认识到分享与合作的快乐，渐渐恢复了往日的活力与快乐。

鸢语的纸鸢也重新焕发了活力，无拘无束地在天空中飞舞。星宝们非常感激鸢语，并主动提出想要学习制作纸鸢，因为这些纸鸢不仅是快乐的载体，更是传递快乐的桥梁。大家都想像鸢语一样，将简单的快乐传递给更多的星宝。]],
sharePic = "T_Share_Suit_402150.astc",
suitId = 225,
suitName = "飞燕仙 鸢语",
suitIcon = "CDN:Icon_PL_101"
},
[402151] = {
id = 402151,
effect = true,
name = "飞燕仙 轻羽",
desc = "似曾相识燕归来",
icon = "CDN:Icon_PL_101_01",
outlookConf = {
belongTo = 402150,
fashionValue = 35,
belongToGroup = {
402151,
402152
}
},
resourceConf = {
model = "SK_PL_101",
material = "MI_PL_101_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_101_Physics",
materialSlot = "Skin"
},
commodityId = 11241,
outEnter = "AS_CH_Enter_PL_101",
outShow = "AS_CH_IdleShow_PL_101",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_101",
shareTexts = {
"落花人独立，微雨燕双飞"
},
shareAnim = "AS_CH_Pose_001_PL_101",
sharePic = "T_Share_Suit_402150.astc"
},
[402152] = {
id = 402152,
effect = true,
name = "飞燕仙 轻羽",
desc = "似曾相识燕归来",
icon = "CDN:Icon_PL_101_02",
outlookConf = {
belongTo = 402150,
fashionValue = 35,
belongToGroup = {
402151,
402152
}
},
resourceConf = {
model = "SK_PL_101",
material = "MI_PL_101_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_101_Physics",
materialSlot = "Skin"
},
commodityId = 11242,
outEnter = "AS_CH_Enter_PL_101",
outShow = "AS_CH_IdleShow_PL_101",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_101",
shareTexts = {
"落花人独立，微雨燕双飞"
},
shareAnim = "AS_CH_Pose_001_PL_101",
sharePic = "T_Share_Suit_402150.astc"
},
[402160] = {
id = 402160,
effect = true,
name = "犬系少年 阿柴",
desc = "我有两个心愿：在你身边，你在身边",
icon = "CDN:Icon_PL_090",
getWay = "永恒之誓",
jumpId = {
175
},
resourceConf = {
model = "SK_PL_090",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_090_Physics"
},
outEnter = "AS_CH_Enter_PL_090",
outShow = "AS_CH_IdleShow_PL_090",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_090",
shareTexts = {
"把你的手交给我吧"
},
shareAnim = "AS_CH_Pose_001_PL_090",
beginTime = {
seconds = 1715875200
},
suitStoryTextKey = [[阿柴是甜心餐厅的服务员，他沉稳乐观的性格和负责的态度让顾客们十分信赖。他用贴心的优质服务提升了甜心餐厅的口碑。

好朋友喵喵是阿柴最重要的伙伴。他们一起经营甜心餐厅，在朝夕相处中有了深厚的友谊。阿柴最幸福的事就是吃喵喵亲手做的甜点。他很享受喵喵对他的特别信任，无条件地支持着她尝试一切稀奇古怪的甜点配方。

在阿柴眼里，喵喵不仅能做出最美味的甜点，笑容也是最甜美的。在喵喵的带领下，阿柴的生活变得更丰富有趣。喵喵丰富多变的小情绪在他眼里十分可爱。他乐于陪伴喵喵，因为他最大的愿望，是守护喵喵的笑容。]],
suitId = 227,
suitName = "犬系少年 阿柴",
suitIcon = "CDN:Icon_PL_090"
},
[402161] = {
id = 402161,
effect = true,
name = "犬系少年 阿柴",
desc = "我有两个心愿：在你身边，你在身边",
icon = "CDN:Icon_PL_090_01",
outlookConf = {
belongTo = 402160,
fashionValue = 35,
belongToGroup = {
402161,
402162
}
},
resourceConf = {
model = "SK_PL_090",
material = "MI_PL_090_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_090_Physics",
materialSlot = "Skin"
},
commodityId = 11244,
outEnter = "AS_CH_Enter_PL_090",
outShow = "AS_CH_IdleShow_PL_090",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_090",
shareTexts = {
"把你的手交给我吧"
},
shareAnim = "AS_CH_Pose_001_PL_090"
},
[402162] = {
id = 402162,
effect = true,
name = "犬系少年 阿柴",
desc = "我有两个心愿：在你身边，你在身边",
icon = "CDN:Icon_PL_090_02",
outlookConf = {
belongTo = 402160,
fashionValue = 35,
belongToGroup = {
402161,
402162
}
},
resourceConf = {
model = "SK_PL_090",
material = "MI_PL_090_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_090_Physics",
materialSlot = "Skin"
},
commodityId = 11245,
outEnter = "AS_CH_Enter_PL_090",
outShow = "AS_CH_IdleShow_PL_090",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_090",
shareTexts = {
"把你的手交给我吧"
},
shareAnim = "AS_CH_Pose_001_PL_090"
},
[402170] = {
id = 402170,
effect = true,
name = "猫系少女 喵喵",
desc = "你是不是在我的心上撒了跳跳糖？",
icon = "CDN:Icon_PL_091",
getWay = "永恒之誓",
jumpId = {
175
},
resourceConf = {
model = "SK_PL_091",
material = "MI_PL_091_1;MI_PL_091_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_091_Physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_091",
outShow = "AS_CH_IdleShow_PL_091",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_091",
shareTexts = {
"来，牵着我的手"
},
shareAnim = "AS_CH_Pose_001_PL_091",
beginTime = {
seconds = 1715875200
},
suitStoryTextKey = [[喵喵是甜心餐厅的甜点师，擅长做各式各样的糕点。她享受将奶油和糖变成柔软蛋糕的过程，也乐于为星宝们创造甜美的味觉盛宴。

好朋友阿柴是喵喵的御用试吃员。他总是身先士卒地品尝喵喵发明的新糕点，尽管经常会吃到黑暗料理，但阿柴乐在其中，并为喵喵提出改进的意见。在阿柴的支持下，喵喵的甜点味道不断精进，甜心餐厅也越来越火。

活泼开朗的喵喵在休假的日子里，喜欢拽着阿柴到处玩。虽然阿柴喜欢待在家里休息，但只要喵喵喜欢他可以随时陪伴。喵喵非常信任阿柴，会对他倾诉一切烦恼。有时喵喵会有一些小情绪，但阿柴总能暖心化解。和阿柴在一起的日子，喵喵每天都很开心。]],
suitId = 228,
suitName = "猫系少女 喵喵",
suitIcon = "CDN:Icon_PL_091"
},
[402171] = {
id = 402171,
effect = true,
name = "猫系少女 喵喵",
desc = "你是不是在我的心上撒了跳跳糖？",
icon = "CDN:Icon_PL_091_01",
outlookConf = {
belongTo = 402170,
fashionValue = 35,
belongToGroup = {
402171,
402172
}
},
resourceConf = {
model = "SK_PL_091",
material = "MI_PL_091_1_HP01;MI_PL_091_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_091_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11247,
outEnter = "AS_CH_Enter_PL_091",
outShow = "AS_CH_IdleShow_PL_091",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_091",
shareTexts = {
"来，牵着我的手"
},
shareAnim = "AS_CH_Pose_001_PL_091"
},
[402172] = {
id = 402172,
effect = true,
name = "猫系少女 喵喵",
desc = "你是不是在我的心上撒了跳跳糖？",
icon = "CDN:Icon_PL_091_02",
outlookConf = {
belongTo = 402170,
fashionValue = 35,
belongToGroup = {
402171,
402172
}
},
resourceConf = {
model = "SK_PL_091",
material = "MI_PL_091_1_HP02;MI_PL_091_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_091_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11248,
outEnter = "AS_CH_Enter_PL_091",
outShow = "AS_CH_IdleShow_PL_091",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_091",
shareTexts = {
"来，牵着我的手"
},
shareAnim = "AS_CH_Pose_001_PL_091"
},
[402180] = {
id = 402180,
effect = true,
name = "秘密王牌 奇锋",
desc = "行动中别忘了微笑",
icon = "CDN:Icon_PL_070",
resourceConf = {
model = "SK_PL_070",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
outEnter = "AS_CH_Enter_PL_070",
outShow = "AS_CH_IdleShow_PL_070",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_070",
shareTexts = {
"勇往直前！"
},
beginTime = {
seconds = 4101552000
},
suitStoryTextKey = [[元梦兵团的王牌士兵奇锋，是全兵团最酷的星宝。他是未知险境的开荒者，也是废土迁移的守护者。再强大的蛮荒强兵遇到他，都被他那棕色镜片下的注视所震慑，瑟瑟发抖，缴械投降。

星宝战友都很崇拜奇锋的战绩，作为后辈，他们能表达尊敬的最佳途径是——想办法看看奇锋面具下的表情。可惜的是，他们花式摘面具的作战方案，都被沉默寡言的奇锋轻松化解，他仿佛是个不败的神话。

奇锋虽然战友们的胡来很宽容，但这并不是因为他不重视纪律，而是他源于他与队友的相互信任。进入兵团，应对危机绝不退缩是士兵的使命，但在危难时将后背交给相信的队友则是团魂所在。奇锋铭记战友的支持，更加潜心修炼战术战技，力求在每一次作战结束后，都能带所有星宝回家。]],
suitId = 230,
suitName = "秘密王牌 奇锋",
suitIcon = "CDN:Icon_PL_070"
},
[402181] = {
id = 402181,
effect = true,
name = "秘密王牌 奇锋",
desc = "行动中别忘了微笑",
icon = "CDN:Icon_PL_070_01",
outlookConf = {
belongTo = 402180,
fashionValue = 35,
belongToGroup = {
402181,
402182
}
},
resourceConf = {
model = "SK_PL_070",
material = "MI_PL_070_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11250,
outEnter = "AS_CH_Enter_PL_070",
outShow = "AS_CH_IdleShow_PL_070",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_070",
shareTexts = {
"勇往直前！"
}
},
[402182] = {
id = 402182,
effect = true,
name = "秘密王牌 奇锋",
desc = "行动中别忘了微笑",
icon = "CDN:Icon_PL_070_02",
outlookConf = {
belongTo = 402180,
fashionValue = 35,
belongToGroup = {
402181,
402182
}
},
resourceConf = {
model = "SK_PL_070",
material = "MI_PL_070_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11251,
outEnter = "AS_CH_Enter_PL_070",
outShow = "AS_CH_IdleShow_PL_070",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_070",
shareTexts = {
"勇往直前！"
}
},
[402190] = {
id = 402190,
effect = true,
name = "云鹤仙  鸿鸣",
desc = "我本海上鹤，偶逢江南客",
icon = "CDN:Icon_PL_092",
resourceConf = {
model = "SK_PL_092",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_092_Physics"
},
outEnter = "AS_CH_Enter_PL_092",
outShow = "AS_CH_IdleShow_PL_092",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_092",
shareTexts = {
"鹤步瑶阶净，萤飞星汉斜"
},
shareAnim = "AS_CH_Pose_001_PL_092",
beginTime = {
seconds = 4101552000
},
suitStoryTextKey = [[云鹤仙鸿鸣隐居在桃源仙山之巅，醉心于修道，追求着天人合一的境界。他博学多才，对于世间万物都有着独到的见解。同时他掌握各种神奇仙法，被仙山下的星宝们尊称为云鹤上仙。

原本，鸿鸣只专注于修炼。直到有一天，仙山下突然被一股邪恶之气所笼罩，许多善良的星宝都被困在水深火热之中。虽然鸿鸣平时很少关心凡间，但他内心淳良，也明白正邪不两立的道理。同时也抱着提升自身修为的想法，毅然走出山门，对抗山下肆虐的邪气。

在对抗邪气的过程中，不善交际的鸿鸣，意外结识了许多凡间的星宝朋友，他们共同面对困难，一起成长，原本习惯孤独的鸿鸣也渐渐感受到了来自他人的温暖。但邪气变得越来越强，甚至开始吞噬鸿鸣的法宝和力量。鸿鸣越是和邪气对抗，就越感到力不从心。

鸿鸣因为自己力量不足而惭愧不已。他劝朋友们离开，想要独自留下对付邪气。但朋友们并没有离鸿鸣而去，而是鼓励他重新振作。鸿鸣因朋友们的善良深受触动，瞬间领悟了新的境界，获得了更高的修为。他从邪气中取回了属于自己的法宝，最终成功将所有邪气吸收封印。仙山之下又恢复了往日的生机。

得益于这次经历，鸿鸣明白了芸芸众生中也潜藏着无限的可能。从此，鸿鸣由一个独善其身的修仙者，成长为了一个心怀天下的侠士。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402190.astc",
suitId = 231,
suitName = "云鹤仙  鸿鸣",
suitIcon = "CDN:Icon_PL_092",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402190.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402190.astc"
},
[402191] = {
id = 402191,
effect = true,
name = "云鹤仙  鸿鸣",
desc = "我本海上鹤，偶逢江南客",
icon = "CDN:Icon_PL_092_01",
outlookConf = {
belongTo = 402190,
fashionValue = 35,
belongToGroup = {
402191,
402192
}
},
resourceConf = {
model = "SK_PL_092",
material = "MI_PL_092_1_HP01;MI_PL_092_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_092_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11253,
outEnter = "AS_CH_Enter_PL_092",
outShow = "AS_CH_IdleShow_PL_092",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_092",
shareTexts = {
"鹤步瑶阶净，萤飞星汉斜"
},
shareAnim = "AS_CH_Pose_001_PL_092",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402190.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402190.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402190.astc"
},
[402192] = {
id = 402192,
effect = true,
name = "云鹤仙  鸿鸣",
desc = "我本海上鹤，偶逢江南客",
icon = "CDN:Icon_PL_092_02",
outlookConf = {
belongTo = 402190,
fashionValue = 35,
belongToGroup = {
402191,
402192
}
},
resourceConf = {
model = "SK_PL_092",
material = "MI_PL_092_1_HP02;MI_PL_092_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_092_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11254,
outEnter = "AS_CH_Enter_PL_092",
outShow = "AS_CH_IdleShow_PL_092",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_092",
shareTexts = {
"鹤步瑶阶净，萤飞星汉斜"
},
shareAnim = "AS_CH_Pose_001_PL_092",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402190.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402190.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402190.astc"
},
[402200] = {
id = 402200,
effect = true,
name = "双面天鹅 娜塔莉",
desc = "在夜幕中舞蹈，演绎深夜狂想曲",
icon = "CDN:Icon_PL_103",
getWay = "双生曼舞",
jumpId = {
603
},
resourceConf = {
model = "SK_PL_103",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_103_Physics"
},
outEnter = "AS_CH_Enter_PL_103",
outShow = "AS_CH_IdleShow_PL_103",
outShowIntervalTime = 10,
shareTexts = {
"夜的神秘，让我来告诉你"
},
shareAnim = "AS_CH_Pose_001_PL_103",
beginTime = {
seconds = 1718380800
},
suitStoryTextKey = [[娜塔莉是一个独特的芭蕾舞蹈家，她的舞蹈生涯和内心世界如同她在舞台上扮演的角色一样，复杂多面。

从小,娜塔莉就被送到了著名的芭蕾舞学校接受严格的训练。面对严酷的训练,她从未放弃过,而是咬牙坚持,日复一日地练习,不断进步。她热爱芭蕾,全身心地投入其中,后来也成功加入了一家著名芭蕾舞团,成为了领舞。她时刻保持高度的专注和自律,在舞台上每一次表演都倾尽全力。

舞台上的娜塔莉就像一只优雅的白天鹅,她翩翩起舞,步履轻盈,举手投足间尽显优雅气质。她的动作优美流畅,每一个舞步都掷地有声,仿佛身体和音乐融为一体。她专注投入,眼神温和而坚定,散发出独特的气质魅力,受到观众的一致好评。白天的演出中，娜塔莉展示的是芭蕾的传统美，那种无尽的优雅和温柔舞姿，是她坚持日复一日训练的结果。

然而,娜塔莉并不满足于这样的传统舞蹈风格，向不同的角色发起挑战。她常常在华丽的舞台谢幕后，到深夜小剧场进行另外一种舞蹈演绎。每到夜幕降临,娜塔莉就像蜕变了一般,变成了张扬奔放的黑天鹅。她挥洒激烈的舞步,肢体动作充满爆发力和冲击感,每一个动作都仿佛要击碎一切。观众被她狂野奔放的舞姿所震撼,仿佛置身于一场视觉盛宴之中。她眼神深邃,神情冷峻,仿佛要用舞蹈诉说自己内心的故事。夜晚的娜塔莉变得高贵冷艳，她的表演充满了挑战传统的勇气和创新精神。

娜塔莉的独特风格给观众留下了深刻印象,她被誉为芭蕾舞坛上的\"双面天鹅\"。白天,她温婉优雅,如白天鹅般翩翩起舞;黑夜,她狂野奔放,如黑天鹅般张扬个性。这种截然不同的双面风格让无数星宝为她倾倒。]],
sharePic = "T_Share_Suit_402201.astc",
suitId = 232,
suitName = "双面天鹅 娜塔莉",
suitIcon = "CDN:Icon_PL_103",
shareNamePic = "T_Share_Suit_name_402200.astc",
shareBubblePic = "T_Share_Suit_frame_402200.astc"
},
[402201] = {
id = 402201,
effect = true,
name = "双面天鹅 娜塔莉",
desc = "在夜幕中舞蹈，演绎深夜狂想曲",
icon = "CDN:Icon_PL_103_01",
outlookConf = {
belongTo = 402200,
fashionValue = 35,
belongToGroup = {
402201,
402202
}
},
resourceConf = {
model = "SK_PL_103",
material = "MI_PL_103_1_HP01;MI_PL_103_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_103_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11256,
outEnter = "AS_CH_Enter_PL_103_HP01",
outShow = "AS_CH_IdleShow_PL_103_HP01",
outShowIntervalTime = 10,
shareTexts = {
"夜的神秘，让我来告诉你"
},
shareAnim = "AS_CH_Pose_001_PL_103",
sharePic = "T_Share_Suit_402201.astc",
shareNamePic = "T_Share_Suit_name_402200.astc",
shareBubblePic = "T_Share_Suit_frame_402200.astc"
},
[402202] = {
id = 402202,
effect = true,
name = "双面天鹅 娜塔莉",
desc = "在夜幕中舞蹈，演绎深夜狂想曲",
icon = "CDN:Icon_PL_103_02",
outlookConf = {
belongTo = 402200,
fashionValue = 35,
belongToGroup = {
402201,
402202
}
},
resourceConf = {
model = "SK_PL_103",
material = "MI_PL_103_1_HP02;MI_PL_103_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_103_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11257,
outEnter = "AS_CH_Enter_PL_103_HP02",
outShow = "AS_CH_IdleShow_PL_103_HP02",
outShowIntervalTime = 10,
shareTexts = {
"夜的神秘，让我来告诉你"
},
shareAnim = "AS_CH_Pose_001_PL_103",
sharePic = "T_Share_Suit_402201.astc",
shareNamePic = "T_Share_Suit_name_402200.astc",
shareBubblePic = "T_Share_Suit_frame_402200.astc"
},
[402210] = {
id = 402210,
effect = true,
name = "双面天鹅 娜塔莉",
desc = "在白昼里旋转，演绎日光奏鸣曲",
icon = "CDN:Icon_PL_094",
resourceConf = {
model = "SK_PL_094",
material = "MI_PL_094_1;MI_PL_094_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_094_Physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_094",
outShow = "AS_CH_IdleShow_PL_094",
outShowIntervalTime = 10,
shareTexts = {
"光的秘密，让我来告诉你"
},
shareAnim = "AS_CH_Pose_001_PL_103",
beginTime = {
seconds = 4101552000
},
suitStoryTextKey = [[娜塔莉是一个独特的芭蕾舞蹈家，她的舞蹈生涯和内心世界如同她在舞台上扮演的角色一样，复杂多面。

从小,娜塔莉就被送到了著名的芭蕾舞学校接受严格的训练。面对严酷的训练,她从未放弃过,而是咬牙坚持,日复一日地练习,不断进步。她热爱芭蕾,全身心地投入其中,后来也成功加入了一家著名芭蕾舞团,成为了领舞。她时刻保持高度的专注和自律,在舞台上每一次表演都倾尽全力。

舞台上的娜塔莉就像一只优雅的白天鹅,她翩翩起舞,步履轻盈,举手投足间尽显优雅气质。她的动作优美流畅,每一个舞步都掷地有声,仿佛身体和音乐融为一体。她专注投入,眼神温和而坚定,散发出独特的气质魅力,受到观众的一致好评。白天的演出中，娜塔莉展示的是芭蕾的传统美，那种无尽的优雅和温柔舞姿，是她坚持日复一日训练的结果。

然而,娜塔莉并不满足于这样的传统舞蹈风格，向不同的角色发起挑战。她常常在华丽的舞台谢幕后，到深夜小剧场进行另外一种舞蹈演绎。每到夜幕降临,娜塔莉就像蜕变了一般,变成了张扬奔放的黑天鹅。她挥洒激烈的舞步,肢体动作充满爆发力和冲击感,每一个动作都仿佛要击碎一切。观众被她狂野奔放的舞姿所震撼,仿佛置身于一场视觉盛宴之中。她眼神深邃,神情冷峻,仿佛要用舞蹈诉说自己内心的故事。夜晚的娜塔莉变得高贵冷艳，她的表演充满了挑战传统的勇气和创新精神。

娜塔莉的独特风格给观众留下了深刻印象,她被誉为芭蕾舞坛上的\"双面天鹅\"。白天,她温婉优雅,如白天鹅般翩翩起舞;黑夜,她狂野奔放,如黑天鹅般张扬个性。这种截然不同的双面风格让无数星宝为她倾倒。]],
sharePic = "T_Share_Suit_402201.astc",
suitId = 956,
suitName = "双面天鹅 娜塔莉",
suitIcon = "CDN:Icon_PL_094",
shareNamePic = "T_Share_Suit_name_402200.astc",
shareBubblePic = "T_Share_Suit_frame_402200.astc",
suitInvalid = true
},
[402211] = {
id = 402211,
effect = true,
name = "双面天鹅 娜塔莉",
desc = "在白昼里旋转，演绎日光奏鸣曲",
icon = "CDN:Icon_PL_094_01",
outlookConf = {
belongTo = 402210,
fashionValue = 35,
belongToGroup = {
402211,
402212
}
},
resourceConf = {
model = "SK_PL_094",
material = "MI_PL_094_1_HP01;MI_PL_094_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_094_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11259,
outEnter = "AS_CH_Enter_PL_094_HP01",
outShow = "AS_CH_IdleShow_PL_094_HP01",
outShowIntervalTime = 10,
shareTexts = {
"光的秘密，让我来告诉你"
},
shareAnim = "AS_CH_Pose_001_PL_103",
sharePic = "T_Share_Suit_402201.astc",
shareNamePic = "T_Share_Suit_name_402200.astc",
shareBubblePic = "T_Share_Suit_frame_402200.astc"
},
[402212] = {
id = 402212,
effect = true,
name = "双面天鹅 娜塔莉",
desc = "在白昼里旋转，演绎日光奏鸣曲",
icon = "CDN:Icon_PL_094_02",
outlookConf = {
belongTo = 402210,
fashionValue = 35,
belongToGroup = {
402211,
402212
}
},
resourceConf = {
model = "SK_PL_094",
material = "MI_PL_094_1_HP02;MI_PL_094_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_094_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11260,
outEnter = "AS_CH_Enter_PL_094_HP02",
outShow = "AS_CH_IdleShow_PL_094_HP02",
outShowIntervalTime = 10,
shareTexts = {
"光的秘密，让我来告诉你"
},
shareAnim = "AS_CH_Pose_001_PL_103",
sharePic = "T_Share_Suit_402201.astc",
shareNamePic = "T_Share_Suit_name_402200.astc",
shareBubblePic = "T_Share_Suit_frame_402200.astc"
},
[402220] = {
id = 402220,
effect = true,
name = "齐天大圣 孙悟空",
desc = "一个跟头，十万八千里",
icon = "CDN:Icon_PL_095",
resourceConf = {
model = "SK_PL_095",
material = "MI_PL_095_1;MI_PL_095_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_095_Physics",
materialSlot = "Skin;Skin_2"
},
outEnter = "AS_CH_Enter_PL_095",
outShow = "AS_CH_IdleShow_PL_095",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_095",
shareTexts = {
"妖怪，哪里逃！"
},
shareAnim = "AS_CH_Pose_001_PL_095",
beginTime = {
seconds = 4101552000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402220.astc",
suitId = 234,
suitName = "齐天大圣 孙悟空",
suitIcon = "CDN:Icon_PL_095",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402220.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402220.astc"
},
[402221] = {
id = 402221,
effect = true,
name = "齐天大圣 孙悟空",
desc = "一个跟头，十万八千里",
icon = "CDN:Icon_PL_095_01",
outlookConf = {
belongTo = 402220,
fashionValue = 35,
belongToGroup = {
402221,
402222
}
},
resourceConf = {
model = "SK_PL_095",
material = "MI_PL_095_1_HP01;MI_PL_095_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_095_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11262,
outEnter = "AS_CH_Enter_PL_095",
outShow = "AS_CH_IdleShow_PL_095",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_095",
shareTexts = {
"妖怪，哪里逃！"
},
shareAnim = "AS_CH_Pose_001_PL_095",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402220.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402220.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402220.astc"
},
[402222] = {
id = 402222,
effect = true,
name = "齐天大圣 孙悟空",
desc = "一个跟头，十万八千里",
icon = "CDN:Icon_PL_095_02",
outlookConf = {
belongTo = 402220,
fashionValue = 35,
belongToGroup = {
402221,
402222
}
},
resourceConf = {
model = "SK_PL_095",
material = "MI_PL_095_1_HP02;MI_PL_095_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_095_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 11263,
outEnter = "AS_CH_Enter_PL_095",
outShow = "AS_CH_IdleShow_PL_095",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_095",
shareTexts = {
"妖怪，哪里逃！"
},
shareAnim = "AS_CH_Pose_001_PL_095",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402220.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402220.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402220.astc"
},
[402230] = {
id = 402230,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "木伊伊",
desc = "是时候出来活动一下了",
icon = "CDN:Icon_BU_096",
outlookConf = v2,
resourceConf = {
model = "SK_BU_096",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"腰酸背痛，偶尔伸展伸展吧！"
},
beginTime = {
seconds = 1722873600
},
suitId = 235,
suitName = "木伊伊",
suitIcon = "CDN:Icon_BU_096"
},
[402231] = {
id = 402231,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "木伊伊",
desc = "是时候出来活动一下了",
icon = "CDN:Icon_BU_096_01",
outlookConf = {
belongTo = 402230,
fashionValue = 25,
belongToGroup = {
402231
}
},
resourceConf = {
model = "SK_BU_096",
material = "MI_BU_096_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11265,
shareTexts = {
"腰酸背痛，偶尔伸展伸展吧！"
}
},
[402240] = {
id = 402240,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "白羊星",
desc = "生于春日的白羊星，热情洋溢，一片赤子心",
icon = "CDN:Icon_BU_114",
getWay = "闯关挑战",
jumpId = {
33
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_114",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"不要总是薅羊毛，会秃的"
},
beginTime = {
seconds = 1717689600
},
suitId = 236,
suitName = "白羊星",
suitIcon = "CDN:Icon_BU_114"
},
[402241] = {
id = 402241,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "白羊星",
desc = "生于春日的白羊星，热情洋溢，一片赤子心",
icon = "CDN:Icon_BU_114_01",
outlookConf = {
belongTo = 402240,
fashionValue = 25,
belongToGroup = {
402241
}
},
resourceConf = {
model = "SK_BU_114",
material = "MI_BU_114_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11267,
shareTexts = {
"不要总是薅羊毛，会秃的"
}
},
[402250] = {
id = 402250,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "海边假日",
desc = "在海边度假的淑女，喜欢小裙子和拍照",
icon = "CDN:Icon_BU_113",
outlookConf = v2,
resourceConf = {
model = "SK_BU_113",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"美美穿着，美美生活"
},
beginTime = {
seconds = 4101552000
},
suitId = 237,
suitName = "海边假日",
suitIcon = "Icon_BU_113"
},
[402251] = {
id = 402251,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "海边假日",
desc = "在海边度假的淑女，喜欢小裙子和拍照",
icon = "CDN:Icon_BU_113_01",
outlookConf = {
belongTo = 402250,
fashionValue = 25,
belongToGroup = {
402251
}
},
resourceConf = {
model = "SK_BU_113",
material = "MI_BU_113_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11269,
shareTexts = {
"美美穿着，美美生活"
}
},
[402260] = {
id = 402260,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "茶末末",
desc = "心灵手巧的点心师，最擅长做抹茶蛋糕",
icon = "CDN:Icon_BU_118",
getWay = "携友同行",
jumpId = {
132
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_118",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"每天一甜点，心情超级甜"
},
beginTime = {
seconds = 1737043200
},
suitId = 243,
suitName = "茶末末",
suitIcon = "CDN:Icon_BU_118"
},
[402261] = {
id = 402261,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "茶末末",
desc = "心灵手巧的点心师，最擅长做抹茶蛋糕",
icon = "CDN:Icon_BU_118_01",
outlookConf = {
belongTo = 402260,
fashionValue = 25,
belongToGroup = {
402261
}
},
resourceConf = {
model = "SK_BU_118",
material = "MI_BU_118_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11271,
shareTexts = {
"每天一甜点，心情超级甜"
}
},
[402270] = {
id = 402270,
effect = true,
name = "逆戟鲸 奥卡",
desc = "自由如风，我从不泊岸",
icon = "CDN:Icon_PL_105",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_PL_105",
material = "MI_PL_105;MI_PL_105_1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_105_Physics",
materialSlot = "Skin;Skin_01"
},
outEnter = "AS_CH_Enter_PL_105",
outShow = "AS_CH_IdleShow_PL_105",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_105",
shareTexts = {
"我从不孤独，四海为家"
},
shareAnim = "AS_CH_Pose_001_PL_105",
beginTime = {
seconds = 1717689600
},
suitStoryTextKey = [[传闻在遥远的北冰洋有一位部落间传唱的逆戟鲸守护灵奥卡，以凶猛和冷酷著称。

奥卡当上守护灵的故事还要从他小时候的一次迁徙经历说起。在逆戟鲸家族一次南方迁徙中，一场突如其来的海啸使得家族首领与族群失散，面对极寒的温度和无情的风暴，逆戟鲸家族遇到了从未有过的危机。年幼的奥卡面对这场风暴显露出了超越年纪的冷静，他凭借着自己超群的记忆和嗅觉找到了一条海底山洞，带领族群躲过了风暴。这次经历既是一次考验也是一次成长的契机，奥卡在逆境中学会了如何依靠自己的力量带领族群生存。

随着时间的流逝，奥卡变得更加强大和独立。他对待同类和其他海洋生物都采取强硬的态度，但也在关键时刻保护他们免受更大的威胁。奥卡曾多次驱赶掉入侵的鲨鱼和更大的海洋捕食者，也因此得到了族群的拥护，成为了逆戟鲸族群最年轻的守护灵，维护着族群的安全及北冰洋生态稳定。

奥卡的名声很块开始在海洋中传开，令大家敬畏不敢轻易侵犯领地。不管是同族的内斗争夺，还是其他掠夺族群的行为，奥卡都会以雷霆万钧之势予以制止，确保没有任何外来者能够破坏北冰洋的和平。

尽管奥卡给外界的印象是冷酷无情，但他对领地内的同胞和弱小民族有着深厚的感情。他特别保护那些无法自我防御的生灵，如幼小的海鱼和海龟。奥卡还会指导族群如何避免风暴和潮流，教导他们认识和利用海洋的资源。只要北冰洋海域有任何生灵遇到困难，奥卡都会毫不犹豫地伸出援手。奥卡这种对弱小生灵的无私关怀，赢得了北冰洋所有生灵的尊重，即使是最凶猛的海怪也会退避三舍。

听说深海之处有一块区域总是闪着金光，奥卡为了保护族群，决定前去一探究竟。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402270.astc",
suitId = 244,
suitName = "逆戟鲸 奥卡",
bpShowId = 2,
seasonId = 5,
suitIcon = "CDN:Icon_PL_105",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402270.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc",
SeasonShowIdList = {
{
key = 5,
value = 2
}
}
},
[402271] = {
id = 402271,
effect = true,
name = "逆戟鲸 奥卡",
desc = "自由如风，我从不泊岸",
icon = "CDN:Icon_PL_105_01",
outlookConf = {
belongTo = 402270,
fashionValue = 35,
belongToGroup = {
402271,
402272
}
},
resourceConf = {
model = "SK_PL_105",
material = "MI_PL_105_HP01;MI_PL_105_1_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_105_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11273,
outEnter = "AS_CH_Enter_PL_105",
outShow = "AS_CH_IdleShow_PL_105",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_105",
shareTexts = {
"我从不孤独，四海为家"
},
shareAnim = "AS_CH_Pose_001_PL_105",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402270.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402270.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc"
},
[402272] = {
id = 402272,
effect = true,
name = "逆戟鲸 奥卡",
desc = "自由如风，我从不泊岸",
icon = "CDN:Icon_PL_105_02",
outlookConf = {
belongTo = 402270,
fashionValue = 35,
belongToGroup = {
402271,
402272
}
},
resourceConf = {
model = "SK_PL_105",
material = "MI_PL_105_HP02;MI_PL_105_1_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_105_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11274,
outEnter = "AS_CH_Enter_PL_105",
outShow = "AS_CH_IdleShow_PL_105",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_105",
shareTexts = {
"我从不孤独，四海为家"
},
shareAnim = "AS_CH_Pose_001_PL_105",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402270.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402270.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc"
},
[402280] = {
id = 402280,
effect = true,
name = "海珍珠 珀尔",
desc = "坚硬的蚌壳，包裹柔软的心",
icon = "CDN:Icon_PL_104",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_PL_104",
material = "MI_PL_104_1;MI_PL_104_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_104_Physics",
materialSlot = "Skin;Skin_01"
},
outEnter = "AS_CH_Enter_PL_104",
outShow = "AS_CH_IdleShow_PL_104",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_104",
shareTexts = {
"我可不会随便掉小珍珠"
},
shareAnim = "AS_CH_Pose_001_PL_104",
beginTime = {
seconds = 1717689600
},
suitStoryTextKey = [[珀尔是珍珠化身的海中仙女。她拥有净化海洋的力量：将海洋里的污秽吸附到蚌壳里，用神力净化和包裹它们，最后孕育成美丽的珍珠。在珀尔的心中，珍珠不仅仅是一件装饰品，更是更是承载着情感与信仰的神圣之物。

生活在海边的星宝们对海珍珠的传说耳熟能详：珍珠是海中仙女珀尔的眼泪，象征她的纯洁与力量，能够化腐朽为神奇，驱散海中一切污秽。珀尔的出生仪式是一场盛大的庆典，海中的神灵和海洋生物都聚集一堂，为这位新生的海中精灵献上祝福。

珀尔善良而坚韧，即便在面对最黑暗的力量时，她也能够隐忍泪水，不轻易流露脆弱。这种内在的力量，源自她在成长中不断修炼的决心。面对海洋中蔓延的阴暗能量，初期她曾感到不堪重负。但她没有因此放弃，她向海洋中其他神明求教，不断努力学习，让自己的神力变得更为强大。在这个过程中，珀尔锻炼出蚌壳般坚硬的心志，始终不渝地坚持着自己的使命。

珀尔的宫殿座落于海底，由蚌壳、珍珠、珊瑚和火山岩石精心打造。她对挖掘海底珍宝有着浓厚的兴趣，希望能够收集足够的宝物来对抗海底的神秘阴暗能量。珀尔对未知宝藏的探求从未止步，她听闻深海中隐藏着未被发现的珍稀宝物，还能能守护整片海域，便立刻踏上寻宝之旅，希望能亲自感受这块珍宝的魅力。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402280.astc",
suitId = 245,
suitName = "海珍珠 珀尔",
bpShowId = 5,
seasonId = 5,
suitIcon = "CDN:Icon_PL_104",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402280.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc",
SeasonShowIdList = {
{
key = 5,
value = 5
}
}
},
[402281] = {
id = 402281,
effect = true,
name = "海珍珠 珀尔",
desc = "坚硬的蚌壳，包裹柔软的心",
icon = "CDN:Icon_PL_104_01",
outlookConf = {
belongTo = 402280,
fashionValue = 35,
belongToGroup = {
402281,
402282
}
},
resourceConf = {
model = "SK_PL_104",
material = "MI_PL_104_1_HP01;MI_PL_104_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_104_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11276,
outEnter = "AS_CH_Enter_PL_104",
outShow = "AS_CH_IdleShow_PL_104",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_104",
shareTexts = {
"我可不会随便掉小珍珠"
},
shareAnim = "AS_CH_Pose_001_PL_104",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402280.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402280.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc"
},
[402282] = {
id = 402282,
effect = true,
name = "海珍珠 珀尔",
desc = "坚硬的蚌壳，包裹柔软的心",
icon = "CDN:Icon_PL_104_02",
outlookConf = {
belongTo = 402280,
fashionValue = 35,
belongToGroup = {
402281,
402282
}
},
resourceConf = {
model = "SK_PL_104",
material = "MI_PL_104_1_HP02;MI_PL_104_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_104_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11277,
outEnter = "AS_CH_Enter_PL_104",
outShow = "AS_CH_IdleShow_PL_104",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_104",
shareTexts = {
"我可不会随便掉小珍珠"
},
shareAnim = "AS_CH_Pose_001_PL_104",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402280.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402280.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc"
},
[402290] = {
id = 402290,
effect = true,
name = "天籁海螺 兰妮卡",
desc = "想把属于大海的声音唱给全世界听",
icon = "CDN:Icon_PL_106",
getWay = "赛季通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_PL_106",
material = "MI_PL_106_1;MI_PL_106_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_106_Physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_106",
outShow = "AS_CH_IdleShow_PL_106",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_106",
shareTexts = {
"希望我的海之歌一直陪伴你"
},
shareAnim = "AS_CH_Pose_001_PL_106",
beginTime = {
seconds = 1717689600
},
suitStoryTextKey = [[兰妮卡是海螺化身的少女。她像海螺一样能够捕捉和吸收声音。从小她便能听到海里的各种声音：水流的声音、鱼吐泡泡的声音、海藻和珊瑚生长的声音，而她回以自己的歌声，从此爱上了歌唱。

兰妮卡善良体贴，拥有奇妙的共情能力。她能轻易和周围的灵魂共鸣，并用歌声回应。星宝们都沉迷于她美妙的歌喉所传递出来的情感，因此兰妮卡很快便和星宝们打成了一片。

兰妮卡的梦想是将大海的声音带给世界，尽管这个梦想听上去天马行空，但拥有赤子之心的星宝们非常理解和支持兰妮卡的想法。为了实现梦想，兰妮卡很勤奋地练习歌唱。她正打算推出她的第一张唱片专辑。这张专辑十分特别，是用海螺录制的。只要把海螺放在耳边，就能够听见兰妮卡美妙的歌声，以及，大海的声音。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402290.astc",
suitId = 246,
suitName = "天籁海螺 兰妮卡",
suitIcon = "CDN:Icon_PL_106",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402290.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402290.astc"
},
[402291] = {
id = 402291,
effect = true,
name = "天籁海螺 兰妮卡",
desc = "想把属于大海的声音唱给全世界听",
icon = "CDN:Icon_PL_106_01",
outlookConf = {
belongTo = 402290,
fashionValue = 35,
belongToGroup = {
402291,
402292
}
},
resourceConf = {
model = "SK_PL_106",
material = "MI_PL_106_1_HP01;MI_PL_106_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_106_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11279,
outEnter = "AS_CH_Enter_PL_106",
outShow = "AS_CH_IdleShow_PL_106",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_106",
shareTexts = {
"希望我的海之歌一直陪伴你"
},
shareAnim = "AS_CH_Pose_001_PL_106",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402290.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402290.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402290.astc"
},
[402292] = {
id = 402292,
effect = true,
name = "天籁海螺 兰妮卡",
desc = "想把属于大海的声音唱给全世界听",
icon = "CDN:Icon_PL_106_02",
outlookConf = {
belongTo = 402290,
fashionValue = 35,
belongToGroup = {
402291,
402292
}
},
resourceConf = {
model = "SK_PL_106",
material = "MI_PL_106_1_HP02;MI_PL_106_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_106_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11280,
outEnter = "AS_CH_Enter_PL_106",
outShow = "AS_CH_IdleShow_PL_106",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_106",
shareTexts = {
"希望我的海之歌一直陪伴你"
},
shareAnim = "AS_CH_Pose_001_PL_106",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402290.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402290.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402290.astc"
},
[402300] = {
id = 402300,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "海小浪",
desc = "放浪！恣意！大海！最棒的船长！",
icon = "CDN:Icon_BU_115",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_115",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"世界很大，一起冲浪"
},
beginTime = {
seconds = 1717689600
},
suitId = 247,
suitName = "海小浪",
bpShowId = 4,
seasonId = 5,
suitIcon = "CDN:Icon_BU_115",
SeasonShowIdList = {
{
key = 5,
value = 4
}
}
},
[402301] = {
id = 402301,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "海小浪",
desc = "放浪！恣意！大海！最棒的船长！",
icon = "CDN:Icon_BU_115_01",
outlookConf = {
belongTo = 402300,
fashionValue = 25,
belongToGroup = {
402301
}
},
resourceConf = {
model = "SK_BU_115",
material = "MI_BU_115_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11282,
shareTexts = {
"世界很大，一起冲浪"
}
},
[402310] = {
id = 402310,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "梦游鱼",
desc = "爱梦游的小鱼，有时创造艺术，有时制造麻烦",
icon = "CDN:Icon_BU_122",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_122",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"世界是梦想家创造的"
},
beginTime = {
seconds = 1717689600
},
suitId = 248,
suitName = "梦游鱼",
bpShowId = 6,
seasonId = 5,
suitIcon = "CDN:Icon_BU_122",
SeasonShowIdList = {
{
key = 5,
value = 6
}
}
},
[402311] = {
id = 402311,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "梦游鱼",
desc = "爱梦游的小鱼，有时创造艺术，有时制造麻烦",
icon = "CDN:Icon_BU_122_01",
outlookConf = {
belongTo = 402310,
fashionValue = 25,
belongToGroup = {
402311
}
},
resourceConf = {
model = "SK_BU_122",
material = "MI_BU_122_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11284,
shareTexts = {
"世界是梦想家创造的"
}
},
[402320] = {
id = 402320,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = " 舞乐乐",
desc = "美丽的舞者，喜欢鲜花草裙和阳光海滩",
icon = "CDN:Icon_BU_112",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_112",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"海草般跳舞，阳光般生活 "
},
beginTime = {
seconds = 1717689600
},
suitId = 249,
suitName = "舞乐乐",
bpShowId = 1,
seasonId = 5,
suitIcon = "CDN:Icon_BU_112",
SeasonShowIdList = {
{
key = 5,
value = 1
}
}
},
[402321] = {
id = 402321,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = " 舞乐乐",
desc = "美丽的舞者，喜欢鲜花草裙和阳光海滩",
icon = "CDN:Icon_BU_112_01",
outlookConf = {
belongTo = 402320,
fashionValue = 25,
belongToGroup = {
402321
}
},
resourceConf = {
model = "SK_BU_112",
material = "MI_BU_112_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11286,
shareTexts = {
"海草般跳舞，阳光般生活 "
}
},
[402330] = {
id = 402330,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "鱼甜甜",
desc = "爱学习的小章鱼，聪明伶俐，过目不忘",
icon = "CDN:Icon_BU_117",
getWay = "赛季通行证",
jumpId = {
9
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_117",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"握住我的手，一起成为生活多面手"
},
beginTime = {
seconds = 1717689600
},
suitId = 250,
suitName = "鱼甜甜",
suitIcon = "CDN:Icon_BU_117"
},
[402331] = {
id = 402331,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "鱼甜甜",
desc = "爱学习的小章鱼，聪明伶俐，过目不忘",
icon = "CDN:Icon_BU_117_01",
outlookConf = {
belongTo = 402330,
fashionValue = 25,
belongToGroup = {
402331
}
},
resourceConf = {
model = "SK_BU_117",
material = "MI_BU_117_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11288,
shareTexts = {
"握住我的手，一起成为生活多面手"
}
},
[402340] = {
id = 402340,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "小丑鱼",
desc = "如果你不开心，让我带你在海底散心",
icon = "CDN:Icon_BU_121",
getWay = "赛季通行证",
jumpId = {
9
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_121",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"在深海里听我指令，不会迷路哦"
},
beginTime = {
seconds = 1717689600
},
suitId = 251,
suitName = "小丑鱼",
suitIcon = "CDN:Icon_BU_121"
},
[402341] = {
id = 402341,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "小丑鱼",
desc = "如果你不开心，让我带你在海底散心",
icon = "CDN:Icon_BU_121_01",
outlookConf = {
belongTo = 402340,
fashionValue = 25,
belongToGroup = {
402341
}
},
resourceConf = {
model = "SK_BU_121",
material = "MI_BU_121_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11290,
shareTexts = {
"在深海里听我指令，不会迷路哦"
}
},
[402350] = {
id = 402350,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星牛仔",
desc = "喜欢探险的牛仔，总在开拓奇迹的路上",
icon = "CDN:Icon_BU_116",
getWay = "印章祈愿",
jumpId = {
705
},
outlookConf = v2,
resourceConf = {
model = "SK_BU_116",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"奇迹属于勇敢者"
},
beginTime = {
seconds = 1722528000
},
suitId = 252,
suitName = "星牛仔",
suitIcon = "CDN:Icon_BU_116"
},
[402351] = {
id = 402351,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星牛仔",
desc = "喜欢探险的牛仔，总在开拓奇迹的路上",
icon = "CDN:Icon_BU_116_01",
outlookConf = {
belongTo = 402350,
fashionValue = 25,
belongToGroup = {
402351
}
},
resourceConf = {
model = "SK_BU_116",
material = "MI_BU_116_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11292,
shareTexts = {
"奇迹属于勇敢者"
}
},
[402360] = {
id = 402360,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星世界鉴赏家",
desc = "巧思、乐趣、美感...经鉴定这是一张好图！！",
icon = "CDN:Icon_BU_123",
outlookConf = v2,
resourceConf = {
model = "SK_BU_123",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"专业星世界地图品鉴"
},
beginTime = {
seconds = 4101552000
},
suitId = 253,
suitName = "头号鉴赏家",
suitIcon = "CDN:Icon_BU_123"
},
[402361] = {
id = 402361,
effect = true,
exceedReplaceItem = v0,
quality = 3,
name = "星世界鉴赏家",
desc = "巧思、乐趣、美感...经鉴定这是一张好图！！",
icon = "CDN:Icon_BU_123_01",
outlookConf = {
belongTo = 402360,
fashionValue = 25,
belongToGroup = {
402361
}
},
resourceConf = {
model = "SK_BU_123",
material = "MI_BU_123_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11294,
shareTexts = {
"专业星世界地图品鉴"
}
}
}

local mt = {
effect = false,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 400
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 300
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false,
suitInvalid = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data