--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_农场.xlsx: 商城-道具

local v0 = "MCL_LifeLongLimit"

local v1 = 1

local v2 = {
seconds = 1725033600
}

local v3 = {
seconds = 3394540800
}

local v4 = 1066

local v5 = 378

local v6 = "前往获得"

local data = {
[103001] = {
commodityId = 103001,
commodityName = "农场月卡30天（赠送用）",
shopTag = {
4
},
gender = 0,
itemIds = {
218001
},
canGift = true,
addIntimacy = 12,
giftCoinType = 1,
giftPrice = 298,
NoShowInMall = 0,
hideGiftBuyCountUI = 1,
hideBatchGiftUI = 1,
hideGiftCoinUI = 1
},
[103003] = {
mallId = 119,
commodityId = 103003,
commodityName = "浮游层紫卡包",
coinType = 1,
price = 10,
limitType = "MCL_EachOnSaleLimit",
limitNum = 10,
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1747670399
},
itemIds = {
219003
},
disableNtf = true
},
[103004] = {
mallId = 119,
commodityId = 103004,
commodityName = "中浮游层紫卡包",
coinType = 1,
price = 10,
limitType = "MCL_EachOnSaleLimit",
limitNum = 10,
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1747670399
},
itemIds = {
219007
},
disableNtf = true
},
[103005] = {
mallId = 119,
commodityId = 103005,
commodityName = "下浮游层紫卡包",
coinType = 1,
price = 10,
limitType = "MCL_EachOnSaleLimit",
limitNum = 10,
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1747670399
},
itemIds = {
219011
},
disableNtf = true
},
[103006] = {
mallId = 119,
commodityId = 103006,
commodityName = "阳光层紫卡包",
coinType = 1,
price = 10,
limitType = "MCL_EachOnSaleLimit",
limitNum = 10,
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1747670399
},
itemIds = {
219015
},
disableNtf = true
},
[103007] = {
mallId = 119,
commodityId = 103007,
commodityName = "中阳光层紫卡包",
coinType = 1,
price = 10,
limitType = "MCL_EachOnSaleLimit",
limitNum = 10,
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1747670399
},
itemIds = {
219019
},
disableNtf = true
},
[103008] = {
mallId = 119,
commodityId = 103008,
commodityName = "透光层紫卡包",
coinType = 1,
price = 10,
limitType = "MCL_EachOnSaleLimit",
limitNum = 10,
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1747670399
},
itemIds = {
219023
},
disableNtf = true
},
[103500] = {
commodityId = 103500,
commodityName = "香草花房",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1722988800
},
endTime = {
seconds = 1725119999
},
jumpId = 357,
jumpText = v6,
itemIds = {
218100
}
},
[103501] = {
commodityId = 103501,
commodityName = "精灵时钟",
limitType = v0,
limitNum = 1,
jumpId = 234,
jumpText = v6,
itemIds = {
218101
}
},
[103502] = {
commodityId = 103502,
commodityName = "稻草人汉克",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
jumpId = 358,
jumpText = v6,
itemIds = {
218102
}
},
[103503] = {
commodityId = 103503,
commodityName = "甜兔屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1726156800
},
jumpId = 358,
jumpText = v6,
itemIds = {
218103
}
},
[103505] = {
commodityId = 103505,
commodityName = "小云宝",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1724385600
},
jumpId = 6,
jumpText = v6,
itemIds = {
218104
}
},
[103506] = {
commodityId = 103506,
commodityName = "洋葱头蔬果屋",
limitType = v0,
limitNum = 1,
beginTime = v2,
jumpId = 412,
jumpText = v6,
itemIds = {
218105
}
},
[103507] = {
commodityId = 103507,
commodityName = "牛牛牧场小店",
limitType = v0,
limitNum = 1,
beginTime = v2,
jumpId = 413,
jumpText = v6,
itemIds = {
218106
}
},
[103508] = {
commodityId = 103508,
commodityName = "丰收兔",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1727107200
},
jumpId = 368,
jumpText = v6,
itemIds = {
218107
}
},
[103509] = {
commodityId = 103509,
commodityName = "卧龙宝宝",
limitType = v0,
limitNum = 1,
jumpId = 8,
jumpText = v6,
itemIds = {
218108
}
},
[103510] = {
commodityId = 103510,
commodityName = "幸运星礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1731038400
},
endTime = {
seconds = 1731859199
},
jumpId = 438,
jumpText = v6,
itemIds = {
218109
}
},
[103511] = {
commodityId = 103511,
commodityName = "沙沙渔获",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1727107200
},
jumpId = 368,
jumpText = v6,
itemIds = {
218110
}
},
[103512] = {
commodityId = 103512,
commodityName = "丛林守卫",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
jumpId = 1066,
jumpText = v6,
itemIds = {
218111
}
},
[103513] = {
commodityId = 103513,
commodityName = "狐仙",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1730304000
},
endTime = {
seconds = 1732204799
},
jumpId = 379,
jumpText = v6,
itemIds = {
218112
}
},
[103514] = {
commodityId = 103514,
commodityName = "狸猫精灵",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218113
}
},
[103515] = {
commodityId = 103515,
commodityName = "胡萝卜时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1726804800
},
jumpId = 7,
jumpText = v6,
itemIds = {
218114
}
},
[103516] = {
commodityId = 103516,
commodityName = "告白熊梦幻屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
jumpId = 378,
jumpText = v6,
itemIds = {
218115
}
},
[103517] = {
commodityId = 103517,
commodityName = "仙人掌花屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1727884800
},
endTime = {
seconds = 1729785599
},
jumpId = 377,
jumpText = v6,
itemIds = {
218116
}
},
[103518] = {
commodityId = 103518,
commodityName = "招财喵",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
jumpId = 400,
jumpText = v6,
itemIds = {
218117
}
},
[103519] = {
commodityId = 103519,
commodityName = "云朵奶油时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
jumpId = 378,
jumpText = v6,
itemIds = {
218118
}
},
[103520] = {
commodityId = 103520,
commodityName = "蔷薇花车",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
jumpId = 378,
jumpText = v6,
itemIds = {
218119
}
},
[103521] = {
commodityId = 103521,
commodityName = "彩虹牧场",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
jumpId = 378,
jumpText = v6,
itemIds = {
218120
}
},
[103522] = {
commodityId = 103522,
commodityName = "梦幻海洋屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
jumpId = 378,
jumpText = v6,
itemIds = {
218121
}
},
[103523] = {
commodityId = 103523,
commodityName = "花蔓时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1730304000
},
endTime = {
seconds = 1732204799
},
jumpId = 379,
jumpText = v6,
itemIds = {
218122
}
},
[103524] = {
commodityId = 103524,
commodityName = "绮丽海螺城堡",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744905600
},
jumpId = 450,
jumpText = v6,
itemIds = {
218123
}
},
[103525] = {
commodityId = 103525,
commodityName = "海狮公主",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740153600
},
jumpId = 510,
jumpText = v6,
itemIds = {
218124
}
},
[103526] = {
commodityId = 103526,
commodityName = "璃海星光果行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744905600
},
jumpId = 450,
jumpText = v6,
itemIds = {
218125
}
},
[103527] = {
commodityId = 103527,
commodityName = "海盗宝藏小店",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744905600
},
jumpId = 450,
jumpText = v6,
itemIds = {
218126
}
},
[103528] = {
commodityId = 103528,
commodityName = "海妖鱼店",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744905600
},
jumpId = 450,
jumpText = v6,
itemIds = {
218127
}
},
[103529] = {
commodityId = 103529,
commodityName = "深海时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744905600
},
jumpId = 450,
jumpText = v6,
itemIds = {
218128
}
},
[103530] = {
commodityId = 103530,
commodityName = "超会稻稻鹅",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218129
}
},
[103531] = {
commodityId = 103531,
commodityName = "梦幻熊礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
jumpId = 400,
jumpText = v6,
itemIds = {
218130
}
},
[103532] = {
commodityId = 103532,
commodityName = "神农幻境",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1732852800
},
jumpText = v6,
itemIds = {
218131
}
},
[103533] = {
commodityId = 103533,
commodityName = "桃源仙居",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1732852800
},
jumpText = v6,
itemIds = {
218132
}
},
[103534] = {
commodityId = 103534,
commodityName = "泡泡鱼礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740153600
},
jumpId = 510,
jumpText = v6,
itemIds = {
218133
}
},
[103535] = {
commodityId = 103535,
commodityName = "雪花轻语果行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749225600
},
jumpId = 509,
jumpText = v6,
itemIds = {
218134
}
},
[103536] = {
commodityId = 103536,
commodityName = "水晶鹿角小店",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749225600
},
jumpId = 509,
jumpText = v6,
itemIds = {
218135
}
},
[103537] = {
commodityId = 103537,
commodityName = "雪乡小Q鱼铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749225600
},
jumpId = 509,
jumpText = v6,
itemIds = {
218136
}
},
[103538] = {
commodityId = 103538,
commodityName = "雪球小精灵",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1736438400
},
jumpId = 521,
jumpText = v6,
itemIds = {
218137
}
},
[103539] = {
commodityId = 103539,
commodityName = "糖果松树时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749225600
},
jumpId = 509,
jumpText = v6,
itemIds = {
218138
}
},
[103540] = {
commodityId = 103540,
commodityName = "冬日萌宠屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1736438400
},
jumpId = 521,
jumpText = v6,
itemIds = {
218139
}
},
[103541] = {
commodityId = 103541,
commodityName = "银风山谷",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749225600
},
endTime = {
seconds = 1751558399
},
jumpId = 509,
jumpText = v6,
itemIds = {
218140
}
},
[103542] = {
commodityId = 103542,
commodityName = "冬雪庄园",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749225600
},
endTime = {
seconds = 1751558399
},
jumpId = 509,
jumpText = v6,
itemIds = {
218141
}
},
[103543] = {
commodityId = 103543,
commodityName = "冰晶星梦城堡",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749225600
},
jumpId = 509,
jumpText = v6,
itemIds = {
218142
}
},
[104000] = {
commodityId = 104000,
commodityName = "冰激凌机",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728316799
},
jumpId = 10681,
jumpText = v6,
itemIds = {
218800
}
},
[104001] = {
commodityId = 104001,
commodityName = "爱心小熊沙发",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739462400
},
endTime = {
seconds = 1741881599
},
jumpId = 573,
jumpText = v6,
itemIds = {
218801
}
},
[105000] = {
commodityId = 105000,
commodityName = "收获日",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219300
}
},
[105001] = {
commodityId = 105001,
commodityName = "星夜魔法",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739548800
},
endTime = {
seconds = 1741881599
},
jumpId = 558,
jumpText = v6,
itemIds = {
219301
}
},
[105002] = {
commodityId = 105002,
commodityName = "幽灵巫师",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743091200
},
endTime = {
seconds = 3403785599
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219302
}
},
[105003] = {
commodityId = 105003,
commodityName = "磨牙棒",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219400
}
},
[105004] = {
commodityId = 105004,
commodityName = "海盐夏日",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 379,
jumpText = v6,
itemIds = {
219401
}
},
[105005] = {
commodityId = 105005,
commodityName = "小肥啾",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = v3,
jumpId = 5110,
jumpText = v6,
itemIds = {
219402
}
},
[105006] = {
commodityId = 105006,
commodityName = "桃之夭夭",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219403
}
},
[105007] = {
commodityId = 105007,
commodityName = "活力小苗",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219404
}
},
[105008] = {
commodityId = 105008,
commodityName = "大橘为重",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219405
}
},
[105009] = {
commodityId = 105009,
commodityName = "名流刘海",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219406
}
},
[105010] = {
commodityId = 105010,
commodityName = "降温毛巾",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219407
}
},
[105011] = {
commodityId = 105011,
commodityName = "派对狂欢",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219408
}
},
[105012] = {
commodityId = 105012,
commodityName = "友谊魔法",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
jumpId = 594,
jumpText = v6,
itemIds = {
219409
}
},
[105013] = {
commodityId = 105013,
commodityName = "童年回忆",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219410
}
},
[105014] = {
commodityId = 105014,
commodityName = "文雅眼镜",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219411
}
},
[105015] = {
commodityId = 105015,
commodityName = "项圈",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219600
}
},
[105016] = {
commodityId = 105016,
commodityName = "波点领结",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219601
}
},
[105017] = {
commodityId = 105017,
commodityName = "铃铛项圈",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219602
}
},
[105018] = {
commodityId = 105018,
commodityName = "职场精英",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219603
}
},
[105019] = {
commodityId = 105019,
commodityName = "卷草纹包袱",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219604
}
},
[105020] = {
commodityId = 105020,
commodityName = "爱心宝宝巾",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219605
}
},
[105021] = {
commodityId = 105021,
commodityName = "条纹围巾",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219606
}
},
[105022] = {
commodityId = 105022,
commodityName = "朋克项圈",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219607
}
},
[105023] = {
commodityId = 105023,
commodityName = "优雅态度",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219608
}
},
[105024] = {
commodityId = 105024,
commodityName = "星火领巾",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219609
}
},
[105025] = {
commodityId = 105025,
commodityName = "潮玩耳机",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219610
}
},
[105026] = {
commodityId = 105026,
commodityName = "运动背扣",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219800
}
},
[105027] = {
commodityId = 105027,
commodityName = "遇上彩虹",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737907200
},
endTime = {
seconds = 1740671999
},
jumpId = 538,
jumpText = v6,
itemIds = {
219801
}
},
[105028] = {
commodityId = 105028,
commodityName = "骨头小包",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219802
}
},
[105029] = {
commodityId = 105029,
commodityName = "咔哒咔哒",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219803
}
},
[105030] = {
commodityId = 105030,
commodityName = "牛仔很忙",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219804
}
},
[105031] = {
commodityId = 105031,
commodityName = "挚爱赠礼",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219805
}
},
[105032] = {
commodityId = 105032,
commodityName = "星星短裤",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219806
}
},
[105033] = {
commodityId = 105033,
commodityName = "镶边毛巾",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219807
}
},
[105034] = {
commodityId = 105034,
commodityName = "今日启航",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 378,
jumpText = v6,
itemIds = {
219808
}
},
[104002] = {
commodityId = 104002,
commodityName = "星星树",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1734278399
},
jumpId = 449,
jumpText = v6,
itemIds = {
218802
}
},
[104003] = {
commodityId = 104003,
commodityName = "神奇海螺壁炉",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1734623999
},
jumpId = 450,
jumpText = v6,
itemIds = {
218818
}
},
[104004] = {
commodityId = 104004,
commodityName = "清新花朵冰箱",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750348799
},
jumpId = 400,
jumpText = v6,
itemIds = {
218819
}
},
[106001] = {
commodityId = 106001,
commodityName = "小煤球",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740002400
},
endTime = {
seconds = 3403785599
},
jumpId = 5110,
jumpText = v6,
itemIds = {
219200
}
},
[106002] = {
commodityId = 106002,
commodityName = "小二哈",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1743350399
},
jumpId = 593,
jumpText = v6,
itemIds = {
219201
}
},
[106003] = {
commodityId = 106003,
commodityName = "小乌云",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
endTime = v3,
jumpId = 5110,
jumpText = v6,
itemIds = {
219202
}
},
[105035] = {
commodityId = 105035,
commodityName = "生日蛋糕",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734710400
},
endTime = {
seconds = 1735747199
},
jumpId = 125,
jumpText = v6,
itemIds = {
219412
}
},
[105036] = {
commodityId = 105036,
commodityName = "鹿角发箍",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734796800
},
endTime = {
seconds = 1736006399
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219413
}
},
[105037] = {
commodityId = 105037,
commodityName = "花环围脖",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734796800
},
endTime = {
seconds = 1736006399
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219611
}
},
[105038] = {
commodityId = 105038,
commodityName = "礼物口袋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1734796800
},
endTime = {
seconds = 1736006399
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219809
}
},
[103549] = {
commodityId = 103549,
commodityName = "树精宝宝",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
jumpId = 1072,
jumpText = v6,
itemIds = {
218148
}
},
[103550] = {
commodityId = 103550,
commodityName = "绿野时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735833600
},
jumpId = 1072,
jumpText = v6,
itemIds = {
218149
}
},
[103551] = {
commodityId = 103551,
commodityName = "胖胖菇时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737043199
},
jumpId = 1070,
jumpText = v6,
itemIds = {
218150
}
},
[103552] = {
commodityId = 103552,
commodityName = "琳琅摘星阁",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737734400
},
endTime = v3,
jumpId = 546,
jumpText = v6,
itemIds = {
218151
}
},
[103553] = {
commodityId = 103553,
commodityName = "金玉醒狮果行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737734400
},
endTime = v3,
jumpId = 546,
jumpText = v6,
itemIds = {
218152
}
},
[103554] = {
commodityId = 103554,
commodityName = "金闪闪小铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737734400
},
endTime = v3,
jumpId = 546,
jumpText = v6,
itemIds = {
218153
}
},
[103555] = {
commodityId = 103555,
commodityName = "鲤跃龙门鱼铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737734400
},
endTime = v3,
jumpId = 546,
jumpText = v6,
itemIds = {
218154
}
},
[103556] = {
commodityId = 103556,
commodityName = "嘶嘶灵宝",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1738339200
},
endTime = v3,
jumpId = 551,
jumpText = v6,
itemIds = {
218155
}
},
[103557] = {
commodityId = 103557,
commodityName = "仙福满满时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737993600
},
endTime = v3,
jumpId = 538,
jumpText = v6,
itemIds = {
218156
}
},
[103558] = {
commodityId = 103558,
commodityName = "宝莲灯礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1738339200
},
endTime = v3,
jumpId = 551,
jumpText = v6,
itemIds = {
218157
}
},
[103559] = {
commodityId = 103559,
commodityName = "金宝萌宠屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1738512000
},
endTime = {
seconds = 1739376000
},
jumpId = 559,
jumpText = v6,
itemIds = {
218158
}
},
[103560] = {
commodityId = 103560,
commodityName = "庆丰年宅院",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737993600
},
endTime = v3,
jumpId = 538,
jumpText = v6,
itemIds = {
218159
}
},
[104005] = {
commodityId = 104005,
commodityName = "水晶糖果树",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737734399
},
jumpId = 509,
jumpText = v6,
itemIds = {
218820
}
},
[104006] = {
commodityId = 104006,
commodityName = "锦绣狮宝",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737907200
},
endTime = {
seconds = 1740671999
},
jumpId = 538,
jumpText = v6,
itemIds = {
219303
}
},
[104007] = {
commodityId = 104007,
commodityName = "发财帽",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1739375999
},
jumpId = 378,
jumpText = v6,
itemIds = {
219414
}
},
[104008] = {
commodityId = 104008,
commodityName = "红围巾",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1739375999
},
jumpId = 378,
jumpText = v6,
itemIds = {
219612
}
},
[104009] = {
commodityId = 104009,
commodityName = "纳福袋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1739375999
},
jumpId = 378,
jumpText = v6,
itemIds = {
219810
}
},
[104010] = {
commodityId = 104010,
commodityName = "金桔盆栽",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
jumpId = 565,
jumpText = v6,
itemIds = {
218821
}
},
[104011] = {
commodityId = 104011,
commodityName = "红梅盆栽",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1737734400
},
endTime = {
seconds = 1740671999
},
jumpId = 546,
jumpText = v6,
itemIds = {
218822
}
},
[104012] = {
commodityId = 104012,
commodityName = "富贵屏风",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740002400
},
endTime = {
seconds = 2055600000
},
jumpId = 1072,
jumpText = v6,
itemIds = {
218823
}
},
[103561] = {
commodityId = 103561,
commodityName = "梦幻萌宠屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1739548800
},
jumpId = 558,
jumpText = v6,
itemIds = {
218160
}
},
[103562] = {
commodityId = 103562,
commodityName = "烟雨小筑",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
jumpId = 575,
jumpText = v6,
itemIds = {
218161
}
},
[103563] = {
commodityId = 103563,
commodityName = "江南果行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
jumpId = 575,
jumpText = v6,
itemIds = {
218162
}
},
[103564] = {
commodityId = 103564,
commodityName = "芙蓉商行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
jumpId = 575,
jumpText = v6,
itemIds = {
218163
}
},
[103565] = {
commodityId = 103565,
commodityName = "观鱼小铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
jumpId = 575,
jumpText = v6,
itemIds = {
218164
}
},
[103567] = {
commodityId = 103567,
commodityName = "悠悠亭",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
jumpId = 575,
jumpText = v6,
itemIds = {
218166
}
},
[103568] = {
commodityId = 103568,
commodityName = "日晷",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
jumpId = 575,
jumpText = v6,
itemIds = {
218167
}
},
[103544] = {
commodityId = 103544,
commodityName = "风悠悠树屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1747324800
},
jumpId = 1072,
jumpText = v6,
itemIds = {
218143
}
},
[103545] = {
commodityId = 103545,
commodityName = "萝尖尖果行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740672000
},
jumpId = 1072,
jumpText = v6,
itemIds = {
218144
}
},
[103546] = {
commodityId = 103546,
commodityName = "绿绒绒小铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743091200
},
jumpId = 1072,
jumpText = v6,
itemIds = {
218145
}
},
[103547] = {
commodityId = 103547,
commodityName = "叶泡泡鱼店",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1745510400
},
jumpId = 1072,
jumpText = v6,
itemIds = {
218146
}
},
[103569] = {
commodityId = 103569,
commodityName = "蜜糖喵巡游站",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
jumpId = 669,
jumpText = v6,
itemIds = {
218168
}
},
[103570] = {
commodityId = 103570,
commodityName = "奶油云朵乐园",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746072000
},
jumpId = 599,
jumpText = v6,
itemIds = {
218169
}
},
[103571] = {
commodityId = 103571,
commodityName = "蜜糖饼庭院",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1745510400
},
jumpId = 594,
jumpText = v6,
itemIds = {
218170
}
},
[104013] = {
commodityId = 104013,
commodityName = "树桩壁炉",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 2055600000
},
jumpId = 1066,
jumpText = v6,
itemIds = {
218824
}
},
[103572] = {
commodityId = 103572,
commodityName = "小红狐农场小屋",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218180
}
},
[103573] = {
commodityId = 103573,
commodityName = "小红狐农场风景",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218181
}
},
[103574] = {
commodityId = 103574,
commodityName = "小红狐农场院落",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218182
}
},
[103575] = {
commodityId = 103575,
commodityName = "狐爷爷",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743177600
},
jumpId = 592,
jumpText = v6,
itemIds = {
218183
}
},
[103576] = {
commodityId = 103576,
commodityName = "翡光仙灵庭",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743782400
},
jumpId = 598,
jumpText = v6,
itemIds = {
218184
}
},
[105039] = {
commodityId = 105039,
commodityName = "甜萝卜兔",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1745510400
},
endTime = {
seconds = 1748534399
},
jumpId = 594,
jumpText = v6,
itemIds = {
219304
}
},
[105040] = {
commodityId = 105040,
commodityName = "珍珠耳环",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 538,
jumpText = v6,
itemIds = {
219415
}
},
[105041] = {
commodityId = 105041,
commodityName = "针织花环",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1745769599
},
jumpId = 662,
jumpText = v6,
itemIds = {
219613
}
},
[105042] = {
commodityId = 105042,
commodityName = "春日纸鸢",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = v3,
jumpId = 538,
jumpText = v6,
itemIds = {
219811
}
},
[103577] = {
commodityId = 103577,
commodityName = "快乐涮涮屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1742486400
},
jumpId = 586,
jumpText = v6,
itemIds = {
218171
}
},
[103578] = {
commodityId = 103578,
commodityName = "三明治果行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1742486400
},
jumpId = 586,
jumpText = v6,
itemIds = {
218172
}
},
[103579] = {
commodityId = 103579,
commodityName = "罐罐茶小铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1742486400
},
jumpId = 586,
jumpText = v6,
itemIds = {
218173
}
},
[103580] = {
commodityId = 103580,
commodityName = "豪华寿司鱼店",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1742486400
},
jumpId = 586,
jumpText = v6,
itemIds = {
218174
}
},
[103582] = {
commodityId = 103582,
commodityName = "蒸蒸日上小窝",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743782400
},
jumpId = 598,
jumpText = v6,
itemIds = {
218176
}
},
[103583] = {
commodityId = 103583,
commodityName = "煎饼超人",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744387200
},
jumpId = 643,
jumpText = v6,
itemIds = {
218177
}
},
[103584] = {
commodityId = 103584,
commodityName = "披萨时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743177600
},
jumpId = 592,
jumpText = v6,
itemIds = {
218178
}
},
[103585] = {
commodityId = 103585,
commodityName = "猪猪包礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744387200
},
jumpId = 643,
jumpText = v6,
itemIds = {
218179
}
},
[104014] = {
commodityId = 104014,
commodityName = "莲池假山",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1740758400
},
endTime = {
seconds = 1742486399
},
jumpId = 575,
jumpText = v6,
itemIds = {
218825
}
},
[104015] = {
commodityId = 104015,
commodityName = "香橙面包床",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1742486400
},
endTime = {
seconds = 1746028799
},
jumpId = 586,
jumpText = v6,
itemIds = {
218826
}
},
[106004] = {
commodityId = 106004,
commodityName = "小雪球",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1743091200
},
endTime = v3,
jumpId = 1066,
jumpText = v6,
itemIds = {
219203
}
},
[104016] = {
commodityId = 104016,
commodityName = "祈福桃枝盆栽",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1745164799
},
jumpId = 655,
jumpText = v6,
itemIds = {
218827
}
},
[103586] = {
commodityId = 103586,
commodityName = "蜜桃猫星星杯",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746720000
},
jumpId = 659,
jumpText = v6,
itemIds = {
218185
}
},
[103587] = {
commodityId = 103587,
commodityName = "蜜桃猫星礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746720000
},
jumpId = 659,
jumpText = v6,
itemIds = {
218186
}
},
[103588] = {
commodityId = 103588,
commodityName = "星星咖啡礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1747627200
},
endTime = {
seconds = 1748793599
},
jumpId = 684,
jumpText = v6,
itemIds = {
218187
}
},
[103589] = {
commodityId = 103589,
commodityName = "海盐甜筒果行",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
jumpId = 669,
jumpText = v6,
itemIds = {
218188
}
},
[103590] = {
commodityId = 103590,
commodityName = "樱桃蛋糕小铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
jumpId = 669,
jumpText = v6,
itemIds = {
218189
}
},
[103591] = {
commodityId = 103591,
commodityName = "海盗星船鱼铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
jumpId = 669,
jumpText = v6,
itemIds = {
218190
}
},
[103592] = {
commodityId = 103592,
commodityName = "大力猫爪时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1745510400
},
jumpId = 594,
jumpText = v6,
itemIds = {
218191
}
},
[103593] = {
commodityId = 103593,
commodityName = "甜甜杯礼盒",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1750348800
},
jumpId = 900,
jumpText = v6,
itemIds = {
218192
}
},
[103594] = {
commodityId = 103594,
commodityName = "布丁狗小窝",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746288000
},
jumpId = 599,
jumpText = v6,
itemIds = {
218193
}
},
[103595] = {
commodityId = 103595,
commodityName = "甜心琪琪",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1750348800
},
jumpId = 900,
jumpText = v6,
itemIds = {
218194
}
},
[104017] = {
commodityId = 104017,
commodityName = "蜜糖沙发",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1749139199
},
jumpId = 669,
jumpText = v6,
itemIds = {
218828
}
},
[103598] = {
commodityId = 103598,
commodityName = "快乐小丑",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1746288000
},
endTime = {
seconds = 1749139199
},
jumpId = 599,
jumpText = v6,
itemIds = {
219305
}
},
[103599] = {
commodityId = 103599,
commodityName = "奶油萌宠屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1747929600
},
jumpId = 1066,
jumpText = v6,
itemIds = {
218197
}
},
[105043] = {
commodityId = 105043,
commodityName = "冲浪鲨鲨",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219306
}
},
[105044] = {
commodityId = 105044,
commodityName = "暗夜伯爵",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1750953599
},
jumpId = 690,
jumpText = v6,
itemIds = {
219307
}
},
[105045] = {
commodityId = 105045,
commodityName = "菠萝墨镜",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219416
}
},
[105046] = {
commodityId = 105046,
commodityName = "花盈冠",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219417
}
},
[105047] = {
commodityId = 105047,
commodityName = "星夜光环",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219418
}
},
[105048] = {
commodityId = 105048,
commodityName = "热带花环",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219614
}
},
[105049] = {
commodityId = 105049,
commodityName = "皎珠链",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219615
}
},
[105050] = {
commodityId = 105050,
commodityName = "繁星领结",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219616
}
},
[105051] = {
commodityId = 105051,
commodityName = "棕榈叶裙",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219812
}
},
[105052] = {
commodityId = 105052,
commodityName = "汉堡背包",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219813
}
},
[105053] = {
commodityId = 105053,
commodityName = "绮罗装",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219814
}
},
[105054] = {
commodityId = 105054,
commodityName = "星翼短裙",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219815
}
},
[103616] = {
commodityId = 103616,
commodityName = "沙洲旅人石屋",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
jumpId = 690,
jumpText = v6,
itemIds = {
218202
}
},
[104018] = {
commodityId = 104018,
commodityName = "宝石甜粽树",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1749398399
},
jumpId = 696,
jumpText = v6,
itemIds = {
218829
}
},
[103566] = {
commodityId = 103566,
commodityName = "杏花酒家",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1750089600
},
jumpId = 710,
jumpText = v6,
itemIds = {
218165
}
},
[103596] = {
commodityId = 103596,
commodityName = "热浪岛屿",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1751040000
},
jumpId = 720,
jumpText = v6,
itemIds = {
218195
}
},
[103597] = {
commodityId = 103597,
commodityName = "石纹部落",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1751040000
},
jumpId = 720,
jumpText = v6,
itemIds = {
218196
}
},
[103614] = {
commodityId = 103614,
commodityName = "怒海鲨王号",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749830400
},
jumpId = 691,
jumpText = v6,
itemIds = {
218200
}
},
[103619] = {
commodityId = 103619,
commodityName = "蓝鲸号餐厅",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1751558400
},
endTime = {
seconds = 1755791999
},
jumpId = 724,
jumpText = v6,
itemIds = {
218205
}
},
[103620] = {
commodityId = 103620,
commodityName = "云晶花语时钟",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1749830400
},
jumpId = 691,
jumpText = v6,
itemIds = {
218206
}
},
[103600] = {
commodityId = 103600,
commodityName = "海洋水族箱",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218198
}
},
[103601] = {
commodityId = 103601,
commodityName = "徽派温泉",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218199
}
},
[103617] = {
commodityId = 103617,
commodityName = "临时温泉1",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218203
}
},
[103618] = {
commodityId = 103618,
commodityName = "临时温泉2",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218204
}
},
[103621] = {
commodityId = 103621,
commodityName = "畜牧摊铺子",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218207
}
},
[103622] = {
commodityId = 103622,
commodityName = "斗罗稻草人",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218208
}
},
[105055] = {
commodityId = 105055,
commodityName = "格格吉祥",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1750089600
},
endTime = {
seconds = 1753372799
},
jumpId = 710,
jumpText = v6,
itemIds = {
219308
}
},
[106005] = {
commodityId = 106005,
commodityName = "小金毛",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1748534400
},
endTime = v3,
jumpId = 1066,
jumpText = v6,
itemIds = {
219205
}
},
[103623] = {
commodityId = 103623,
commodityName = "牦铃货铺",
limitType = v0,
limitNum = 1,
beginTime = {
seconds = 1750953600
},
jumpId = 5110,
jumpText = v6,
itemIds = {
218209
}
},
[106006] = {
commodityId = 106006,
commodityName = "小曲奇",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219204
}
},
[105056] = {
commodityId = 105056,
commodityName = "水手套装",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219309
}
},
[105057] = {
commodityId = 105057,
commodityName = "教皇套装",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219310
}
},
[105058] = {
commodityId = 105058,
commodityName = "魔法发带",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219419
}
},
[105059] = {
commodityId = 105059,
commodityName = "冒险眼镜",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219420
}
},
[105060] = {
commodityId = 105060,
commodityName = "魔法颈饰",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219618
}
},
[105061] = {
commodityId = 105061,
commodityName = "魔法身体",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219816
}
},
[105062] = {
commodityId = 105062,
commodityName = "奶牛颈饰",
limitType = v0,
limitNum = 1,
beginTime = v2,
endTime = {
seconds = 1726243199
},
jumpId = 1066,
jumpText = v6,
itemIds = {
219619
}
},
[103615] = {
commodityId = 103615,
commodityName = "圣灵之庭",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218201
}
},
[103624] = {
commodityId = 103624,
commodityName = "星露花台",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218210
}
},
[103625] = {
commodityId = 103625,
commodityName = "天穹彩虹小店",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218211
}
},
[103626] = {
commodityId = 103626,
commodityName = "灵泉圣亭",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218212
}
},
[103627] = {
commodityId = 103627,
commodityName = "云谣小亭",
limitType = v0,
limitNum = 1,
beginTime = v3,
jumpText = v6,
itemIds = {
218213
}
}
}

local mt = {
mallId = 120,
itemNums = {
1
},
canGift = false,
disableNtf = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data