--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_玩具.xlsx: 玩具表

local data = {
[729000] = {
id = 729000,
type = "ItemType_InteractiveToy",
stackedNum = 999,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
quality = 3,
name = "星友滴滴卡",
desc = "选图摇人，一键上车，星搭子快到碗里来！",
icon = "CDN:T_Common_Item_System_Bag_054",
useType = "IUTO_PeriodInteract",
useParam = {
0
},
movementConf = {
assetType = 1,
isLoop = true,
actionType = 8,
isBlend = true,
animBlendType = 3
}
},
[729001] = {
id = 729001,
type = "ItemType_InteractiveToy",
stackedNum = 999,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 20
}
},
quality = 3,
name = "小红狐痛包",
desc = "代号：痛包的描述",
icon = "CDN:T_Common_Item_System_Bag_054",
jumpId = {
727
},
useType = "IUTO_PeriodInteract",
useParam = {
0
},
movementConf = {
assetType = 1,
isLoop = true,
actionType = 7,
isBlend = true,
animBlendType = 3
},
shareTexts = {
"代号：小红狐痛包分享文案"
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data