--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local v0 = 1

local v1 = {
{
itemId = 6,
itemNum = 100
}
}

local v2 = {
{
itemId = 6,
itemNum = 400
}
}

local v3 = 3

local v4 = "商城"

local v5 = {
15
}

local v6 = "IUTO_None"

local v7 = 10

local v8 = "AS_CH_Pose_Common_001"

local v9 = {
seconds = 4101552000
}

local v10 = {
0,
0
}

local v11 = 0.0

local data = {
[410840] = {
id = 410840,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 600
}
},
quality = 1,
name = "幻彩画匠 绮",
desc = "梦的颜色，永远不会褪色",
icon = "CDN:Icon_OG_047",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 2000
},
resourceConf = {
model = "SK_OG_047",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_OG_047_Physics",
IconLabelId = 101,
skeletalMesh = "SK_OG_047"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_047_PV/Level_OG_047_Intact",
outIdle = "AS_CH_OutIdle_OG_047",
outShow = "AS_CH_IdleShow_OG_047",
outShowIntervalTime = 20,
soundId = {
4097,
4096
},
outIdle_pv = "LS_PV_OG_047_Idle",
outEnterSequence = "LS_PV_OG_047",
shareTexts = {
"就这个颜色！"
},
shareAnim = v8,
billboardOffsetZ = 50,
beginTime = v9,
suitStoryTextKey = [[在寂静的月夜里，绮时常做着奇异的梦。梦里，她看见了另一个世界——那里有会说话的画具，会跳舞的颜料，还常梦见校园画室里有一位扎着彩色双马尾的少女，正用发梢沾着五彩斑斓的颜料作画。那个世界充满了不可思议的想象力，远比她所在的世界更为绚丽多彩。

每次从梦中醒来，绮都会迫不及待地将梦境绮制成画。在一次又一次的描绘中，那个彩色发梢少女的面孔渐渐清晰——竟是绮自己！这个来自梦境的自己仿佛是绮内心深处对现代世界的向往与憧憬，是她古老画魂中最为跳脱灵动的一面。

梦醒以后，画纸上总有一些奇妙的画作。虽然绮完全不记得学过这些，但笔下确实开始浮现出会发光的甜点、能当气球用的云朵——这些明显不属于本世界的造物。

在某一次施展画灵之力后，绮在梦境中再一次见到了另外一个自己。只是这一次，梦境中的少女开口说话了：“绮，我不只是你的梦……我也是你画中未完成的另一半。”

绮常常微笑着看着梦中的自己在校园画室里跑来跑去，发梢甩出的颜料在空中划出彩虹。她明白，梦里的自己正是内心最纯粹的想象力和童真的具现化。当现实中的她执笔作画时，是继承画圣衣钵的画灵；而当梦中的自己挥动彩色画笔时，则是她内心最活泼浪漫的一面。

当新研的彩墨正在宣纸上自动晕染，绮不再惊恐，而是微笑着放任画笔自己游走——因为最美好的画作，永远诞生在现实与梦境的交界处。

“画中有画，心中有心。”绮轻抚着画卷说道，“或许这就是画圣想教会我的最后一课——艺术不该被规矩所束缚，想象力才是最珍贵的礼物。”

绮望着自己最新完成的《千梦绘卷》：画卷里，无数个不同风格的自己正在各自的世界挥毫。最中央的留白处，墨迹自动延伸着，渐渐形成通往下一个梦境的入口。“原来所谓灵感...”她捻起一枚掉落在砚台边的齿轮花瓣，“是万千世界的我在同时落笔啊。”]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410840.astc",
suitId = 1006,
suitName = "幻彩画匠 绮",
suitIcon = "CDN:Icon_OG_047",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410840.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410840.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
previewShareOffset = v10,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 3.0,
postProcessLogic = 1
},
[410850] = {
id = 410850,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "南嘉嘉",
desc = "警告：本南瓜含糖量超标！",
icon = "CDN:Icon_BU_273",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_273",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"被奶油腌入味啦"
},
shareAnim = v8,
beginTime = v9,
suitId = 1008,
suitName = "南嘉嘉",
suitIcon = "CDN:Icon_BU_273",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410851] = {
id = 410851,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "南嘉嘉",
desc = "警告：本南瓜含糖量超标！",
icon = "CDN:Icon_BU_273_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410850,
fashionValue = 25,
belongToGroup = {
410851
}
},
resourceConf = {
model = "SK_BU_273",
material = "MI_BU_273_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12017,
shareTexts = {
"被奶油腌入味啦"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410860] = {
id = 410860,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "草莓猫",
desc = "猫猫和草莓我都爱！",
icon = "CDN:Icon_PL_279",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_279",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_279_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_279",
outShow = "AS_CH_IdleShow_PL_279",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_279",
shareTexts = {
"和小猫一起分享草莓吧"
},
shareAnim = v8,
beginTime = {
seconds = 1736006400
},
suitId = 1010,
suitName = "草莓猫",
suitIcon = "CDN:Icon_PL_279",
previewShareOffset = v10,
ThemedShowIdList = {
{
key = 34,
value = 2
}
},
FootwearHeight = 0.0
},
[410870] = {
id = 410870,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "祈雨师 墨瑜",
desc = "鱼生水，水生花，花生遍地美满",
icon = "CDN:Icon_PL_283",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_283",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_283_Physics"
},
outEnter = "AS_CH_Enter_PL_283",
outShow = "AS_CH_IdleShow_PL_283",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_283",
shareTexts = {
"祈雨，亦是为大家祈福"
},
shareAnim = v8,
beginTime = v9,
suitStoryTextKey = [[身为黑金鱼家族年轻一代最为强大的祈雨师，墨瑜自出生起便一直四处奔波对抗危险的凶兽。凶兽驱散的差不多时，墨瑜被调回了家，开启了漫长的假期。为了给赋闲的墨瑜找点事情做，家族把她派去了一个偏远的缺水小村。

白天祈雨，晚上和村里的星宝吃饭，听他们讲起往事：曾经村子富饶繁盛，但有一日天空变成红色，滔天的洪水袭来，庄稼房屋毁于一旦，许多星宝都搬走了。墨瑜突然想起自己曾对抗一只凶恶的火魔，狂热的火焰烧红了天空，她引动滔天洪水才堪堪将之击倒。猛烈的洪水摧毁小村子自然毫不费力，意识到是自己的过失，墨瑜很是愧疚。

天气愈加炎热，某个夜晚，一只火魔出现在村里肆虐。墨瑜恍然大悟，当年它逃过一劫，躲在村子地下潜伏修养，引起了干旱！愤怒的墨瑜本想再次引起大水，但她克制住了，不能再让村子受损。于是她挑衅火魔，引之向东海，刚刚恢复的火魔不敌墨瑜，被沉到深海之中。

火魔被消灭，村子再也不会有干旱了。墨瑜本可直接离去，但她已知晓，修复远比破坏困难，她能做的事情，还有许多许多。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410870.astc",
suitId = 1012,
suitName = "祈雨师 墨瑜",
suitIcon = "CDN:Icon_PL_283",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410870.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410870.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410871] = {
id = 410871,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "祈雨师 墨瑜",
desc = "鱼生水，水生花，花生遍地美满",
icon = "CDN:Icon_PL_283_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410870,
fashionValue = 35,
belongToGroup = {
410871,
410872
}
},
resourceConf = {
model = "SK_PL_283",
material = "MI_PL_283_1_HP01;MI_PL_283_2_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_283_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12020,
outEnter = "AS_CH_Enter_PL_283",
outShow = "AS_CH_IdleShow_PL_283",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_283",
shareTexts = {
"祈雨，亦是为大家祈福"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410870.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410870.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410870.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410872] = {
id = 410872,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "祈雨师 墨瑜",
desc = "鱼生水，水生花，花生遍地美满",
icon = "CDN:Icon_PL_283_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410870,
fashionValue = 35,
belongToGroup = {
410871,
410872
}
},
resourceConf = {
model = "SK_PL_283",
material = "MI_PL_283_1_HP02;MI_PL_283_2_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_283_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12021,
outEnter = "AS_CH_Enter_PL_283",
outShow = "AS_CH_IdleShow_PL_283",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_283",
shareTexts = {
"祈雨，亦是为大家祈福"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410870.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410870.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410870.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410880] = {
id = 410880,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 227,
itemNum = 7
}
},
quality = 3,
name = "甜小朵",
desc = "心事，比云朵更绵软",
icon = "CDN:Icon_BU_278",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_278",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"甜甜的，蓬松的"
},
shareAnim = v8,
beginTime = v9,
suitId = 1014,
suitName = "甜小朵",
suitIcon = "CDN:Icon_BU_278",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410881] = {
id = 410881,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "甜小朵",
desc = "心事，比云朵更绵软",
icon = "CDN:Icon_BU_278_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410880,
fashionValue = 25,
belongToGroup = {
410881
}
},
resourceConf = {
model = "SK_BU_278",
material = "MI_BU_278_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12023,
shareTexts = {
"甜甜的，蓬松的"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410890] = {
id = 410890,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "墨小棠",
desc = "听梨花落雪，看海棠春眠",
icon = "CDN:Icon_BU_288",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_288",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"绒球坠襟，晃晃悠悠"
},
shareAnim = v8,
beginTime = v9,
suitId = 1016,
suitName = "墨小棠",
suitIcon = "CDN:Icon_BU_288",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410891] = {
id = 410891,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "墨小棠",
desc = "听梨花落雪，看海棠春眠",
icon = "CDN:Icon_BU_288_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410890,
fashionValue = 25,
belongToGroup = {
410891
}
},
resourceConf = {
model = "SK_BU_288",
material = "MI_BU_288_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12025,
shareTexts = {
"绒球坠襟，晃晃悠悠"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410900] = {
id = 410900,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "余小怪",
desc = "深海很黑，但我会发光",
icon = "CDN:Icon_BU_274",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_274",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"就是要长得实用才好"
},
shareAnim = v8,
beginTime = v9,
suitId = 1018,
suitName = "余小怪",
suitIcon = "CDN:Icon_BU_274",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410901] = {
id = 410901,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "余小怪",
desc = "深海很黑，但我会发光",
icon = "CDN:Icon_BU_274_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410900,
fashionValue = 25,
belongToGroup = {
410901
}
},
resourceConf = {
model = "SK_BU_274",
material = "MI_BU_274_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12027,
shareTexts = {
"就是要长得实用才好"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410910] = {
id = 410910,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "胡图图",
desc = "大胆创作，灵感才不会消失",
icon = "CDN:Icon_BU_270",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_270",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"小心，油漆未干"
},
shareAnim = v8,
beginTime = v9,
suitId = 1020,
suitName = "胡图图",
suitIcon = "CDN:Icon_BU_270",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410911] = {
id = 410911,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "胡图图",
desc = "大胆创作，灵感才不会消失",
icon = "CDN:Icon_BU_270_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410910,
fashionValue = 25,
belongToGroup = {
410911
}
},
resourceConf = {
model = "SK_BU_270",
material = "MI_BU_270_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12029,
shareTexts = {
"小心，油漆未干"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410920] = {
id = 410920,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "拉弥娅",
desc = "抓到谁，谁就是拉弥娅的好朋友！",
icon = "CDN:Icon_BU_280",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_280",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"拉弥娅想要很多好朋友~"
},
shareAnim = v8,
beginTime = v9,
suitId = 1022,
suitName = "拉弥娅",
suitIcon = "CDN:Icon_BU_280",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410921] = {
id = 410921,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "拉弥娅",
desc = "抓到谁，谁就是拉弥娅的好朋友！",
icon = "CDN:Icon_BU_280_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410920,
fashionValue = 25,
belongToGroup = {
410921
}
},
resourceConf = {
model = "SK_BU_280",
material = "MI_BU_280_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12031,
shareTexts = {
"拉弥娅想要很多好朋友~"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410930] = {
id = 410930,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "浮梦罗烟 海月",
desc = "心有所念，意有所极",
icon = "CDN:Icon_PL_181",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_181",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_181_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_181",
outIdle = "AS_CH_OutIdle_001_PL_181",
outShow = "AS_CH_IdleShow_PL_181",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_181",
shareTexts = {
"意有所极，梦亦同趣"
},
shareAnim = v8,
beginTime = v9,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410930.astc",
suitId = 1024,
suitName = "浮梦罗烟 海月",
suitIcon = "CDN:Icon_PL_181",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410930.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410930.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410940] = {
id = 410940,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "沧海之曜 大乔",
desc = "守望着天空、大海，和你的回忆",
icon = "CDN:Icon_PL_241",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_241",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_241_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_241",
outShow = "AS_CH_IdleShow_PL_241",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_241",
shareTexts = {
"逆流而上吧！"
},
shareAnim = v8,
beginTime = v9,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410940.astc",
suitId = 1026,
suitName = "沧海之曜 大乔",
suitIcon = "CDN:Icon_PL_241",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410940.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410940.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410950] = {
id = 410950,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "雷电之子 索尔",
desc = "闪电会为我蓄能，星星会为我加冕",
icon = "CDN:Icon_PL_292",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_292",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_292_Physics"
},
outEnter = "AS_CH_Enter_PL_292",
outShow = "AS_CH_IdleShow_PL_292",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_292",
shareTexts = {
"找对方向，照亮整片天空"
},
shareAnim = v8,
minVer = "1.3.88.1",
beginTime = {
seconds = 1744905600
},
suitStoryTextKey = [[“肯定又是索尔在捣乱！”每当超能学园发生大规模停电，师生们都会不约而同地把目光投向那个头上有着闪电标志的少年。

索尔，一个让所有老师头疼的存在。十四岁觉醒超能力时，他的额头突然出现了一道闪电形状的印记。这个叛逆少年仿佛突然找到了新的乐趣——用雷电捣蛋。他最爱在考试时制造短路，逼得学校不得不推迟考试；甚至会在无聊时让整个操场的照明系统忽明忽暗，上演一场“光影秀”。整个学园除了魔导师喵妮斯，谁都无法压制他的能力。

然而一场突如其来的能源风暴袭击了校园，电网接连崩溃。当所有星宝都在惊慌失措时，索尔却第一次感受到了不一样的东西——他能听到电流的“哭泣”，感受到能量的“痛苦”。那一刻，他仿佛明白了什么。

在整座校园陷入完全黑暗的那个晚上，索尔额头的闪电印记发出前所未有的光芒。他第一次认真地运用他的能力，调节着失控的电网，疏导着紊乱的能量。

后半夜校园重新恢复了光明，而那个站在高处的少年，已经累得瘫坐在地上。从那以后的索尔。他开始刻苦钻研电磁原理，主动向导师请教能量控制的理论。那个曾经制造停电的捣蛋鬼，现在却成了校园能源系统的守护者，甚至当上了学生会长。

如今，当新生们听说现在一本正经的学生会长曾有一段叛逆往事时，都会觉得不可思议。只有魔导师喵妮斯调皮地眨着眼睛：“有些星宝天生就像闪电，野性难驯，但只要找对方向，他们就能照亮整片天空。”]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410950.astc",
suitId = 1028,
suitName = "雷电之子 索尔",
suitIcon = "CDN:Icon_PL_292",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410950.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
previewShareOffset = v10,
SeasonShowIdList = {
{
key = 12,
value = 4
}
},
FootwearHeight = 0.0
},
[410951] = {
id = 410951,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "雷电之子 索尔",
desc = "闪电会为我蓄能，星星会为我加冕",
icon = "CDN:Icon_PL_292_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410950,
fashionValue = 35,
belongToGroup = {
410951,
410952
}
},
resourceConf = {
model = "SK_PL_292",
material = "MI_PL_292_1_HP01;MI_PL_292_2_HP01;MI_PL_292_3_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_292_Physics",
materialSlot = "Skin;Skin_2;Skin_3"
},
commodityId = 12035,
outEnter = "AS_CH_Enter_PL_292",
outShow = "AS_CH_IdleShow_PL_292",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_292",
shareTexts = {
"找对方向，照亮整片天空"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410950.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410950.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410952] = {
id = 410952,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "雷电之子 索尔",
desc = "闪电会为我蓄能，星星会为我加冕",
icon = "CDN:Icon_PL_292_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410950,
fashionValue = 35,
belongToGroup = {
410951,
410952
}
},
resourceConf = {
model = "SK_PL_292",
material = "MI_PL_292_1_HP02;MI_PL_292_2_HP02;MI_PL_292_3_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_292_Physics",
materialSlot = "Skin;Skin_2;Skin_3"
},
commodityId = 12036,
outEnter = "AS_CH_Enter_PL_292",
outShow = "AS_CH_IdleShow_PL_292",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_292",
shareTexts = {
"找对方向，照亮整片天空"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410950.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410950.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410960] = {
id = 410960,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "变形术师 玛吉",
desc = "精湛的变形术，是形式与功能的完美统一",
icon = "CDN:Icon_PL_291",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_291",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_291_Physics"
},
outEnter = "AS_CH_Enter_PL_291",
outShow = "AS_CH_IdleShow_PL_291",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_291",
shareTexts = {
"随心所欲就是我的风格"
},
shareAnim = v8,
minVer = "1.3.88.1",
beginTime = {
seconds = 1744905600
},
suitStoryTextKey = [[玛吉是一名变形术师，她的梦想是加入超能学园，入学考核的内容是“穿七环”，在阵风肆虐的环境里，穿过七座高楼上的自由圆环。

考核那天，玛吉变成了一只猫头鹰，开始很顺利，但突然风变大，玛吉在风中震荡、失控，她无法维持住变形术，从空中径直掉落。

一次次考核失败，让玛吉十分难过，她不明白风为什么要阻止她。

秋已至，窗外的树叶落下，她心想：你也逃不过被风吹落的命运。可叶子乘着风转了一圈又一圈，如同起舞，玛吉的心也在随着叶子起伏，她伸出了手，风中的叶子悄然落在她的手心。

又是一次考核，又是一次大风。玛吉张开双翅，感受桎梏之风的规律，想象自己是那片叶子，循着逆风高高飞起，风的轨迹渐渐在脑海中连接， 这片风场将成为她的乐园。在众星宝讶异的眼神中，玛吉似乎与风合为一体，风即是她的翅膀！她恣意飘荡，不知不觉间穿过了所有的圆环！

往后玛吉谈及此事时总会说道：让心灵解脱，倾听世界与自己的声音，那种和谐的状态，便是自由，而此时，万物皆为我所用。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410960.astc",
suitId = 1030,
suitName = "变形术师 玛吉",
suitIcon = "CDN:Icon_PL_291",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410960.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
previewShareOffset = v10,
SeasonShowIdList = {
{
key = 12,
value = 3
}
},
FootwearHeight = 0.0
},
[410961] = {
id = 410961,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "变形术师 玛吉",
desc = "精湛的变形术，是形式与功能的完美统一",
icon = "CDN:Icon_PL_291_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410960,
fashionValue = 35,
belongToGroup = {
410961,
410962
}
},
resourceConf = {
model = "SK_PL_291",
material = "MI_PL_291_1_HP01;MI_PL_291_2_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_291_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12038,
outEnter = "AS_CH_Enter_PL_291",
outShow = "AS_CH_IdleShow_PL_291",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_291",
shareTexts = {
"随心所欲就是我的风格"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410960.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410960.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410962] = {
id = 410962,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "变形术师 玛吉",
desc = "精湛的变形术，是形式与功能的完美统一",
icon = "CDN:Icon_PL_291_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410960,
fashionValue = 35,
belongToGroup = {
410961,
410962
}
},
resourceConf = {
model = "SK_PL_291",
material = "MI_PL_291_1_HP02;MI_PL_291_2_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_291_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12039,
outEnter = "AS_CH_Enter_PL_291",
outShow = "AS_CH_IdleShow_PL_291",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_291",
shareTexts = {
"随心所欲就是我的风格"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410960.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410960.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[410970] = {
id = 410970,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "超能魔导师 喵妮斯",
desc = "没有无用的魔法，只有等待被发现的奇迹",
icon = "CDN:Icon_OG_045",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_045",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_OG_045_Physics",
skeletalMesh = "SK_OG_045"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_045_PV/Level_OG_045_Intact",
outIdle = "AS_CH_OutIdle_OG_045",
outShow = "AS_CH_IdleShow_OG_045",
outShowIntervalTime = 20,
soundId = {
4094,
4092
},
outIdle_pv = "LS_PV_OG_045_Idle",
outEnterSequence = "LS_PV_OG_045",
shareTexts = {
"我喜欢创造全新的“现在”"
},
shareAnim = v8,
minVer = "1.3.88.1",
beginTime = {
seconds = 1744905600
},
suitStoryTextKey = [[喵妮斯是超能学园的创始人，也是一位天生“异类”的导师——一个从不拘泥于规则，却能用不按常理出牌的方法激发学生无限潜力的“魔法怪才”。

她有着一头如晨曦般耀眼的卷发，头顶翘着一撮永远不听话的呆毛，就像她本人一样，充满了活力与叛逆。她的课堂从来不被教室的四面墙束缚。你可能会在云端看到她与学生讨论风的形状，也可能在深海中倾听鲸鱼的歌声，甚至在火山口分析岩浆流动的魔法特性。

最有趣的是她头上那对小巧的翅膀。它们原本只是一次失败魔法实验的“残留物”，但如今却成了她的标志。一个学生曾对她说：“它们看起来像是梦想的象征，告诉我们早晚都能飞翔。”从那以后，这对翅膀成了她的骄傲——当她激动时，翅膀会闪烁银光；当她为学生自豪时，羽毛会染上彩虹般的色彩；而当她陷入沉思时，翅膀尖会悄悄旋转，像是在绘制一场新的冒险。

创办超能学园的初衷，源于她不愿让任何一个星宝因“能力不完美”而被世界遗弃，在她看来，魔法是最自由的艺术。每次她的奇思妙想降临，一个无限符号便会跃出，预示着一场不可预测的魔法狂潮即将展开。

她的信念很简单——“世界上没有失败的魔法，只有等待被发现的奇迹。”她要做的，就是为这些奇迹找到最适合它们发光的地方。

如今，超能学园已成为无数星宝的归宿，每个在这里成长的星宝都知道——只要抬头看见那道橙色的身影在天际掠过，就代表着又一场奇迹即将发生。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410970.astc",
suitId = 1032,
suitName = "超能魔导师 喵妮斯",
suitIcon = "CDN:Icon_OG_045",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410970.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
previewShareOffset = v10,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
bodytype = 2,
firstNoSkip = 1,
SeasonShowIdList = {
{
key = 12,
value = 2
}
},
FootwearHeight = 0.0
},
[410971] = {
id = 410971,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "超能魔导师 喵妮斯",
desc = "没有无用的魔法，只有等待被发现的奇迹",
icon = "CDN:Icon_OG_045_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410970,
fashionValue = 125,
belongToGroup = {
410971,
410972
}
},
resourceConf = {
model = "SK_OG_045",
material = "MI_OG_045_1_HP01;MI_OG_045_2_HP01;MI_OG_045_3_HP01;MI_OG_045_4_HP01;MI_OG_045_5_HP01;MI_OG_045_6_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_OG_045_Physics",
materialSlot = "Skin;Skin_Opaque_02;Skin_Opaque_03;Skin_Opaque_04;Skin_Translucent_01;Skin_Translucent_02",
skeletalMesh = "SK_OG_045"
},
commodityId = 12041,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_045_PV/Level_OG_045_Intact",
outIdle = "AS_CH_OutIdle_OG_045",
outShow = "AS_CH_IdleShow_OG_045_HP01",
outShowIntervalTime = 20,
soundId = {
4094,
4092
},
outIdle_pv = "LS_PV_OG_045_HP01_Idle",
outEnterSequence = "LS_PV_OG_045_HP01",
shareTexts = {
"我喜欢创造全新的“现在”"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410970.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410970.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
previewShareOffset = v10,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0
},
[410972] = {
id = 410972,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "超能魔导师 喵妮斯",
desc = "没有无用的魔法，只有等待被发现的奇迹",
icon = "CDN:Icon_OG_045_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410970,
fashionValue = 125,
belongToGroup = {
410971,
410972
}
},
resourceConf = {
model = "SK_OG_045",
material = "MI_OG_045_1_HP02;MI_OG_045_2_HP02;MI_OG_045_3_HP02;MI_OG_045_4_HP02;MI_OG_045_5_HP02;MI_OG_045_6_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_OG_045_Physics",
materialSlot = "Skin;Skin_Opaque_02;Skin_Opaque_03;Skin_Opaque_04;Skin_Translucent_01;Skin_Translucent_02",
skeletalMesh = "SK_OG_045"
},
commodityId = 12042,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_045_PV/Level_OG_045_Intact",
outIdle = "AS_CH_OutIdle_OG_045",
outShow = "AS_CH_IdleShow_OG_045_HP02",
outShowIntervalTime = 20,
soundId = {
4094,
4092
},
outIdle_pv = "LS_PV_OG_045_HP02_Idle",
outEnterSequence = "LS_PV_OG_045_HP02",
shareTexts = {
"我喜欢创造全新的“现在”"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410970.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410970.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
previewShareOffset = v10,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0
},
[410980] = {
id = 410980,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "无限魔导师 喵妮斯",
desc = "无限并不遥远，它就在你脚下的每一步",
icon = "CDN:Icon_OG_046",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_046",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_OG_046_Physics",
IconLabelId = 101,
skeletalMesh = "SK_OG_046"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_045_PV/Level_OG_045_Intact",
outIdle = "AS_CH_OutIdle_OG_046",
outShow = "AS_CH_IdleShow_OG_046",
outShowIntervalTime = 20,
soundId = {
4095,
4093
},
outIdle_pv = "LS_PV_OG_046_Idle",
outEnterSequence = "LS_PV_OG_046",
shareTexts = {
"魔法的真谛在于爱与信念"
},
shareAnim = v8,
beginTime = v9,
suitStoryTextKey = [[在超能学园图书馆藏着一张泛黄的老照片：一位卷发少女挽着学生们的肩膀，站在学园门前露出灿烂的笑容。谁能想到，这个头上翘着呆毛的导师喵妮斯，已成为跨越时空的旅者，在无数个世界间穿梭，为迷失的星宝们寻找归宿。

这一切起源于一场惊天动地的灾难。时空裂缝吞噬了超能学园的一部分，喵妮斯不惜燃尽所有魔力，只为将学生们送回现实。当所有星宝以为她将消逝时，她的翅膀化作连接过去、未来、平行宇宙的桥梁，呆毛成为接收多重时空信号的“天线”，她获得了超越时间和空间的能力——能够自由穿梭于不同的世界，寻找那些迷失的奇迹。

尽管成为了时空的掌控者，她依然是那个不正经的导师。她的课堂变得更加疯狂——带学生们去恐龙时代体验原始魔法，在未来城市研究科技与魔法的结合，甚至在某个平行宇宙的茶馆里，与另一个版本的自己对饮畅谈。她的每一次出现，都会带来一场不可思议的冒险。

她的笑声回荡在无数时空，她的身影穿梭于星海之间。她最喜欢做的事情，便是在无数平行世界中寻找那些拥有超能力又被现实埋没才能的星宝，将他们带回最适合他们成长的时空。她相信每个星宝的超能力都是一种独特的光芒，而她的使命，就是找到这种光芒最绚烂的绽放方式。

“最伟大的魔法不是改变命运，而是找到属于自己的舞台。”她这样说着，而她的翅膀，则在星云中划出璀璨的轨迹。

超能学园已不再局限于一方小天地，而喵妮斯也成为了连接无数可能性的超能魔导师。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410980.astc",
suitId = 1034,
suitName = "无限魔导师 喵妮斯",
suitIcon = "CDN:Icon_OG_046",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410980.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410970.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
previewShareOffset = v10,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
bodytype = 2,
firstNoSkip = 1,
FootwearHeight = 0.0
},
[410990] = {
id = 410990,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "蜜桃猫",
desc = "让生活充满“桃”气~",
icon = "CDN:Icon_PL_295",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_295",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_295_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_295",
outShow = "AS_CH_IdleShow_PL_295",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_295",
shareTexts = {
"探索快乐？交给我吧喵！"
},
shareAnim = v8,
beginTime = {
seconds = 1746720000
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410990.astc",
suitId = 1036,
suitName = "蜜桃猫",
suitIcon = "CDN:Icon_PL_295",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410990.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410990.astc",
previewShareOffset = v10,
ThemedShowIdList = {
{
key = 35,
value = 1
}
},
FootwearHeight = 0.0
},
[410991] = {
id = 410991,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "蜜桃猫",
desc = "让生活充满“桃”气~",
icon = "CDN:Icon_PL_295_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 410990,
fashionValue = 35,
belongToGroup = {
410991
}
},
resourceConf = {
model = "SK_PL_295",
material = "MI_PL_295_1_HP01;MI_PL_295_2_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_295_Physics",
materialSlot = "Skin;Skin_02",
IconLabelId = 102
},
commodityId = 12085,
outEnter = "AS_CH_Enter_PL_295",
outShow = "AS_CH_IdleShow_PL_295",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_295",
shareTexts = {
"探索快乐？交给我吧喵！"
},
shareAnim = v8,
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_410990.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_410990.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_410990.astc",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411000] = {
id = 411000,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "霸王 项羽",
desc = "我命由我！",
icon = "CDN:Icon_PL_237",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_237",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_237_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_237",
outShow = "AS_CH_IdleShow_PL_237",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_237",
shareTexts = {
"命运，不配做我的对手！"
},
shareAnim = v8,
beginTime = v9,
suitId = 1038,
suitName = "霸王 项羽",
suitIcon = "CDN:Icon_PL_237",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411010] = {
id = 411010,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "司小礼",
desc = "嗯，不听话的，都记在小本本上",
icon = "CDN:Icon_BU_287",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_287",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"没有规矩，不成方圆"
},
shareAnim = v8,
beginTime = v9,
suitId = 1040,
suitName = "司小礼",
suitIcon = "CDN:Icon_BU_287",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411011] = {
id = 411011,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "司小礼",
desc = "嗯，不听话的，都记在小本本上",
icon = "CDN:Icon_BU_287_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411010,
fashionValue = 25,
belongToGroup = {
411011
}
},
resourceConf = {
model = "SK_BU_287",
material = "MI_BU_287_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12047,
shareTexts = {
"没有规矩，不成方圆"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411020] = {
id = 411020,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "金小豹",
desc = "你好！钱来！",
icon = "CDN:Icon_BU_281",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_281",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"多“财”多“亿”罢了"
},
shareAnim = v8,
minVer = "1.3.88.1",
beginTime = {
seconds = 1744905600
},
suitId = 1042,
suitName = "金小豹",
suitIcon = "CDN:Icon_BU_281",
previewShareOffset = v10,
SeasonShowIdList = {
{
key = 12,
value = 5
}
},
FootwearHeight = 0.0
},
[411021] = {
id = 411021,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "金小豹",
desc = "你好！钱来！",
icon = "CDN:Icon_BU_281_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411020,
fashionValue = 25,
belongToGroup = {
411021
}
},
resourceConf = {
model = "SK_BU_281",
material = "MI_BU_281_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12049,
shareTexts = {
"多“财”多“亿”罢了"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411030] = {
id = 411030,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "艺小萱",
desc = "随便吧，我只是爱画画",
icon = "CDN:Icon_BU_286",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_286",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"唯有善与美才能永恒"
},
shareAnim = v8,
minVer = "1.3.88.1",
beginTime = {
seconds = 1744905600
},
suitId = 1044,
suitName = "艺小萱",
suitIcon = "CDN:Icon_BU_286",
previewShareOffset = v10,
SeasonShowIdList = {
{
key = 12,
value = 1
}
},
FootwearHeight = 0.0
},
[411031] = {
id = 411031,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "艺小萱",
desc = "随便吧，我只是爱画画",
icon = "CDN:Icon_BU_286_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411030,
fashionValue = 25,
belongToGroup = {
411031
}
},
resourceConf = {
model = "SK_BU_286",
material = "MI_BU_286_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12051,
shareTexts = {
"唯有善与美才能永恒"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411040] = {
id = 411040,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "酷乐星",
desc = "少年的勇敢，有时也被叫做叛逆",
icon = "CDN:Icon_BU_285",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_285",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"要去找点乐子吗"
},
shareAnim = v8,
beginTime = v9,
suitId = 1046,
suitName = "酷乐星",
suitIcon = "CDN:Icon_BU_285",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411041] = {
id = 411041,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "酷乐星",
desc = "少年的勇敢，有时也被叫做叛逆",
icon = "CDN:Icon_BU_285_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411040,
fashionValue = 25,
belongToGroup = {
411041
}
},
resourceConf = {
model = "SK_BU_285",
material = "MI_BU_285_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12053,
shareTexts = {
"要去找点乐子吗"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411050] = {
id = 411050,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "程小维",
desc = "优秀的建筑，是自然的延续",
icon = "CDN:Icon_BU_284",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_284",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"万丈高楼平地起"
},
shareAnim = v8,
minVer = "1.3.88.1",
beginTime = {
seconds = 1744905600
},
suitId = 1048,
suitName = "程小维",
suitIcon = "CDN:Icon_BU_284",
previewShareOffset = v10,
SeasonShowIdList = {
{
key = 12,
value = 6
}
},
FootwearHeight = 0.0
},
[411051] = {
id = 411051,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "程小维",
desc = "优秀的建筑，是自然的延续",
icon = "CDN:Icon_BU_284_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411050,
fashionValue = 25,
belongToGroup = {
411051
}
},
resourceConf = {
model = "SK_BU_284",
material = "MI_BU_284_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12055,
shareTexts = {
"万丈高楼平地起"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411060] = {
id = 411060,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "林小垒",
desc = "听，那是完美击球在空中翱翔的声音",
icon = "CDN:Icon_BU_279",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_279",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"每一分，都不简单"
},
shareAnim = v8,
beginTime = v9,
suitId = 1050,
suitName = "林小垒",
suitIcon = "CDN:Icon_BU_279",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411061] = {
id = 411061,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "林小垒",
desc = "听，那是完美击球在空中翱翔的声音",
icon = "CDN:Icon_BU_279_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411060,
fashionValue = 25,
belongToGroup = {
411061
}
},
resourceConf = {
model = "SK_BU_279",
material = "MI_BU_279_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12057,
shareTexts = {
"每一分，都不简单"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411070] = {
id = 411070,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "宫小千",
desc = "所有相遇，都是平行宇宙的重逢",
icon = "CDN:Icon_BU_283",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_283",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"跳一支舞，献给神灵"
},
shareAnim = v8,
beginTime = v9,
suitId = 1052,
suitName = "宫小千",
suitIcon = "CDN:Icon_BU_283",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411071] = {
id = 411071,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "宫小千",
desc = "所有相遇，都是平行宇宙的重逢",
icon = "CDN:Icon_BU_283_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411070,
fashionValue = 25,
belongToGroup = {
411071
}
},
resourceConf = {
model = "SK_BU_283",
material = "MI_BU_283_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12059,
shareTexts = {
"跳一支舞，献给神灵"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411080] = {
id = 411080,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "杨小帆",
desc = "做一个世界的水手，游遍所有的港口",
icon = "CDN:Icon_BU_282",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_282",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"吹吹海风，看看夕阳"
},
shareAnim = v8,
beginTime = v9,
suitId = 1054,
suitName = "杨小帆",
suitIcon = "CDN:Icon_BU_282",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411081] = {
id = 411081,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "杨小帆",
desc = "做一个世界的水手，游遍所有的港口",
icon = "CDN:Icon_BU_282_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411080,
fashionValue = 25,
belongToGroup = {
411081
}
},
resourceConf = {
model = "SK_BU_282",
material = "MI_BU_282_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12061,
shareTexts = {
"吹吹海风，看看夕阳"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411090] = {
id = 411090,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "乐小野",
desc = "耳机分你一半，心跳共享节拍",
icon = "CDN:Icon_BU_291",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_291",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"寻求快乐，开心就好"
},
shareAnim = v8,
beginTime = v9,
suitId = 1056,
suitName = "乐小野",
suitIcon = "CDN:Icon_BU_291",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411091] = {
id = 411091,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "乐小野",
desc = "耳机分你一半，心跳共享节拍",
icon = "CDN:Icon_BU_291_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411090,
fashionValue = 25,
belongToGroup = {
411091
}
},
resourceConf = {
model = "SK_BU_291",
material = "MI_BU_291_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12063,
shareTexts = {
"寻求快乐，开心就好"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411100] = {
id = 411100,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "喵小舞",
desc = "本喵可是时尚担当！",
icon = "CDN:Icon_BU_289",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_289",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_BU_289_Physics"
},
shareTexts = {
"多彩，是青春的颜色"
},
shareAnim = v8,
beginTime = v9,
suitId = 1058,
suitName = "喵小舞",
suitIcon = "CDN:Icon_BU_289",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411101] = {
id = 411101,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "喵小舞",
desc = "本喵可是时尚担当！",
icon = "CDN:Icon_BU_289_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411100,
fashionValue = 25,
belongToGroup = {
411101
}
},
resourceConf = {
model = "SK_BU_289",
material = "MI_BU_289_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_BU_289_Physics",
materialSlot = "Skin"
},
commodityId = 12065,
shareTexts = {
"多彩，是青春的颜色"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411110] = {
id = 411110,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "糖圈圈",
desc = "主动跳进的圈套，甜甜圈是其中一套",
icon = "CDN:Icon_BU_290",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SK_BU_290",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"生活也要圆圆又甜甜哦"
},
shareAnim = v8,
beginTime = v9,
suitId = 1060,
suitName = "糖圈圈",
suitIcon = "CDN:Icon_BU_290",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411111] = {
id = 411111,
effect = true,
maxNum = 1,
exceedReplaceItem = v1,
quality = 3,
name = "糖圈圈",
desc = "主动跳进的圈套，甜甜圈是其中一套",
icon = "CDN:Icon_BU_290_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411110,
fashionValue = 25,
belongToGroup = {
411111
}
},
resourceConf = {
model = "SK_BU_290",
material = "MI_BU_290_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 12067,
shareTexts = {
"生活也要圆圆又甜甜哦"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411120] = {
id = 411120,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "墨妙妙",
desc = "你听过关于黑猫的都市传说吗？",
icon = "CDN:Icon_Body_043",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_043",
upper = "SK_Body_Upper_043",
bottom = "SK_Body_Under_043",
gloves = "SK_Body_Hands_043",
face = "SK_Body_Face_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"会带来好运和幸福"
},
shareAnim = v8,
beginTime = v9,
suitId = 1062,
suitName = "墨妙妙",
suitIcon = "CDN:Icon_Body_043",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411130] = {
id = 411130,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "天气之女 晴子",
desc = "天气搭配大师，驾到！",
icon = "CDN:Icon_PL_290",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_290",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_290_Physics"
},
outEnter = "AS_CH_Enter_PL_290",
outShow = "AS_CH_IdleShow_PL_290",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_290",
shareTexts = {
"今日份晴天是为你特调的哦"
},
shareAnim = v8,
beginTime = v9,
suitStoryTextKey = [[“今天的天气是——捣蛋鬼晴子特制！”
——元梦气象台晨间新闻

如果你问为什么昨天的彩虹是螺旋形的，或者为什么今早的云朵都在哼着歌，答案只有一个：那个踩着会打雷的靴子，穿着七彩云裙的天气之女晴子又在捣蛋了。

晴子的工作听起来很简单：掌控元梦世界的天气。哦，可真不是！你以为天气这种东西是随便拨拉几下就能搞定的吗？不，晴子每天得忙着调试风的方向、调整云的厚度，甚至连彩虹的颜色顺序都要一遍遍确认。她还有一本厚厚的《天气配方书》，上面写着“晴天的微风需要混合三分温暖、一勺花香和两片阳光”这样复杂的公式。但晴子偏偏不喜欢照着公式来，她总是说：“天气嘛，得有点儿惊喜！要是每天都一样，怎么能让大家记住今天的天空呢？”

最让星宝难忘的是“大旋风事件”：那天她心血来潮，用金色大勺把风和云搅在一起，还撒了点雷霆作料。结果，一个疯狂的龙卷风带着一只懵懂的小绵羊在天空跳起了华尔兹！最后还是用她的七彩云裙才把这个“跳舞狂”给捆住。

如今星宝们已经习惯了每天看着天空猜谜：“今天的晴子小姐又会带来什么惊喜呢？”

至于那本厚厚的《天气配方书》？它早就被晴子改写成了《天气创意手册》，扉页上歪歪扭扭地写着：“配方什么的，不如听听天空的心情～”]],
suitId = 1064,
suitName = "天气之女 晴子",
suitIcon = "CDN:Icon_PL_290",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411131] = {
id = 411131,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "天气之女 晴子",
desc = "天气搭配大师，驾到！",
icon = "CDN:Icon_PL_290_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411130,
fashionValue = 35,
belongToGroup = {
411131,
411132
}
},
resourceConf = {
model = "SK_PL_290",
material = "MI_PL_290_1_HP01;MI_PL_290_2_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_290_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 12087,
outEnter = "AS_CH_Enter_PL_290",
outShow = "AS_CH_IdleShow_PL_290",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_290",
shareTexts = {
"今日份晴天是为你特调的哦"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411132] = {
id = 411132,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "天气之女 晴子",
desc = "天气搭配大师，驾到！",
icon = "CDN:Icon_PL_290_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411130,
fashionValue = 35,
belongToGroup = {
411131,
411132
}
},
resourceConf = {
model = "SK_PL_290",
material = "MI_PL_290_1_HP02;MI_PL_290_2_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_290_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 12088,
outEnter = "AS_CH_Enter_PL_290",
outShow = "AS_CH_IdleShow_PL_290",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_290",
shareTexts = {
"今日份晴天是为你特调的哦"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411140] = {
id = 411140,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "初阳金菊 幸",
desc = "遇见你，是我最大的幸运",
icon = "CDN:Icon_PL_270",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_270",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_270_Physics"
},
outEnter = "AS_CH_Enter_PL_270",
outShow = "AS_CH_IdleShow_PL_270",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_270",
shareTexts = {
"好事总在发生"
},
shareAnim = v8,
beginTime = v9,
suitStoryTextKey = [[正如其名字，幸是一个幸运值爆棚的家伙。可惜，幸的家族过度依赖他的好运，他被限制出行，理由是好运要留在家族里。幸讨厌这一切，却无能为力。

某次随着家族在陌生的小镇做生意时，他偷偷溜了出去，遇见了樱。

他们一同玩耍，樱花树下，只有他们的笑声在回荡。幸隐隐注意到，在和樱待着的时候，也没发生什么奇特的幸运之事，一切是那么平常，也那么美好。

幸更加频繁去找樱，然后不出意外的被发现了。家族准备彻底禁足他，于是他去见了樱最后一面，向她说明了一切。“你是我唯一的朋友，樱。”幸哭着说。

临走前，樱送了幸一朵樱花。那是不幸的樱花，带着它的幸不再是幸运星，眷顾家族的好运很快就散去了。不幸的幸，不被需要了。他飞快的找到樱，握住她那几乎透明的手。

“无论做什么，我都会陪着你！“幸说到。

还有，遇见你是我生命中最大的幸运！幸在心底悄悄说到，樱肯定也能听到吧？]],
suitId = 1066,
suitName = "初阳金菊 幸",
suitIcon = "CDN:Icon_PL_270",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411141] = {
id = 411141,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "初阳金菊 幸",
desc = "遇见你，是我最大的幸运",
icon = "CDN:Icon_PL_270_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411140,
fashionValue = 35,
belongToGroup = {
411141,
411142
}
},
resourceConf = {
model = "SK_PL_270",
material = "MI_PL_270_1_HP01;MI_PL_270_2_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_270_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12090,
outEnter = "AS_CH_Enter_PL_270",
outShow = "AS_CH_IdleShow_PL_270",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_270",
shareTexts = {
"好事总在发生"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411142] = {
id = 411142,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "初阳金菊 幸",
desc = "遇见你，是我最大的幸运",
icon = "CDN:Icon_PL_270_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411140,
fashionValue = 35,
belongToGroup = {
411141,
411142
}
},
resourceConf = {
model = "SK_PL_270",
material = "MI_PL_270_1_HP02;MI_PL_270_2_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_270_Physics",
materialSlot = "Skin;Skin_2"
},
commodityId = 12091,
outEnter = "AS_CH_Enter_PL_270",
outShow = "AS_CH_IdleShow_PL_270",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_270",
shareTexts = {
"好事总在发生"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411150] = {
id = 411150,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "仙灵教母 奥瑞莉亚",
desc = "若无力量，善意则是刺向自己的利刃",
icon = "CDN:Icon_PL_294",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_294",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_294_Physics",
skeletalMesh = "SK_PL_294"
},
outEnter = "AS_CH_Enter_PL_294",
outIdle = "AS_CH_OutIdle_PL_294",
outShow = "AS_CH_IdleShow_PL_294",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_294",
shareTexts = {
"前路艰险，但我矢志不渝"
},
shareAnim = v8,
beginTime = v9,
suitStoryTextKey = [[奥瑞莉亚是被星辰之力选中者，魔力强大；亦是仙灵教母，高贵优雅。 

想要管理喜好玩乐的仙灵不是件容易的事情，她们生性自由，不喜欢被约束。但奥瑞莉亚并不着急，凡事需要等待一个契机。

仙灵境外有月之塔，常有幽怨的哀嚎响起，是一处古老的禁地。某日，一群闲不住的仙灵悄悄溜了进去，探索后竟发现了许多被困的仙灵。她们开心的带着解救的仙灵来到奥瑞莉亚面前，吹嘘自己做了多么了不起的事情，希望得到奥瑞莉亚的奖赏。

奥瑞莉亚静静听着，等仙灵说完，她轻轻闭目，全身突然闪耀金光，那几个被解救的仙灵在金光的照射下，发出痛苦的嚎叫，顿时化成黑烟消散。显然她们不是仙灵，而是妄图侵入仙灵境的邪恶力量。

“若无力量，善意则是刺向自我的利刃。”奥瑞莉亚对着瞠目结舌的仙灵们淡淡说道。

只有经历才会使得仙灵们成长，奥瑞莉亚的目的达到了。她回想起在星辰之力选中自己的那一天，窥见未来的景象：孱弱的仙灵们被黑烟吞没，四散奔逃，仙灵境覆灭。奥瑞莉亚知晓自己背负的责任，有了她的指引，仙灵和仙灵境定会冲破命运的桎梏，寻到正确之路。]],
suitId = 1068,
suitName = "仙灵教母 奥瑞莉亚",
suitIcon = "CDN:Icon_PL_294",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411151] = {
id = 411151,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "仙灵教母 奥瑞莉亚",
desc = "若无力量，善意则是刺向自己的利刃",
icon = "CDN:Icon_PL_294_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411150,
fashionValue = 35,
belongToGroup = {
411151,
411152
}
},
resourceConf = {
model = "SK_PL_294",
material = "MI_PL_294_1_HP01;MI_PL_294_2_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_294_Physics",
materialSlot = "Skin;Skin_2",
skeletalMesh = "SK_PL_294"
},
commodityId = 12093,
outEnter = "AS_CH_Enter_PL_294",
outIdle = "AS_CH_OutIdle_PL_294",
outShow = "AS_CH_IdleShow_PL_294",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_294",
shareTexts = {
"前路艰险，但我矢志不渝"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411152] = {
id = 411152,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "仙灵教母 奥瑞莉亚",
desc = "若无力量，善意则是刺向自己的利刃",
icon = "CDN:Icon_PL_294_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411150,
fashionValue = 35,
belongToGroup = {
411151,
411152
}
},
resourceConf = {
model = "SK_PL_294",
material = "MI_PL_294_1_HP02;MI_PL_294_2_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_294_Physics",
materialSlot = "Skin;Skin_2",
skeletalMesh = "SK_PL_294"
},
commodityId = 12094,
outEnter = "AS_CH_Enter_PL_294",
outIdle = "AS_CH_OutIdle_PL_294",
outShow = "AS_CH_IdleShow_PL_294",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_294",
shareTexts = {
"前路艰险，但我矢志不渝"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411160] = {
id = 411160,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "熊魂武师 晴霜",
desc = "熊魂流，只为弱者而战",
icon = "CDN:Icon_PL_284",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_284",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_284_Physics",
skeletalMesh = "SK_PL_284"
},
outEnter = "AS_CH_Enter_PL_284",
outShow = "AS_CH_IdleShow_PL_284",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_284",
shareTexts = {
"一拳开天！"
},
shareAnim = v8,
beginTime = v9,
suitStoryTextKey = [[晴霜出生于“猎魂者”世家，她的家族有着独特的修炼方法：每个星宝习武时，家族会帮助星宝猎取命格相合的猛兽，以猛兽之魄助力修行，从而达到常人无法企及的境界。
晴霜天赋异禀，从小立志成为武道宗师，她备受家族的期许，与她命格相合的猛兽是一头威猛不凡的白熊。12岁时，家族带着她去雪山猎取白熊，她看到白熊嗷嗷待哺的幼熊，心有不忍，偷偷放走了被困在家族陷阱中的白熊。这也是第一次，她对家族的修炼方式产生了质疑。
未能得到猛兽之魄的晴霜，习武的进度渐渐落后于同期，看到那些之前远不如自己的星宝都变得比自己更加勇猛，她坚持想要靠自己的力量成为武道宗师，只能付出比常人多百倍的努力。
在一次比武大会上，她爆冷门战胜了强劲的对手。但对手不相信没有武魂的拳手能赢，毫无证据地指责她作弊，而那些因为赌她输而输了钱的老板更是怀恨在心，在她回家的路上设伏，想要致她于死地。
关键时刻，那只被放走的白熊再次出现救下了她。原来，白熊被晴霜放走后，就一直在默默守护着她。
之后，蓄谋已久的猎魂家族出现，他们围住了白熊，想要借此机会猎取它的武魂。伤痕累累的晴霜挡在白熊身前，而猎魂家族则威胁她说，如果她违背家族，以后将被整个武道驱逐，再也不能参加比武大会。
“比起成为武道大宗师，我更珍惜和伙伴一起并肩作战的热血。”
晴霜毅然折断了自己的冠军金腰带，以示决绝。白熊将自己的意志化作一件披风落在重伤的晴霜身上，晴霜身上的伤口瞬间愈合了，她觉得自己充满了力量，一鼓作气打败了所有的敌人。一人一熊顺利逃跑。
此后，晴霜活跃在地下武馆，她创立了 「熊魂流」 ，强调武魂与武者的平等共生。她不能再参加比武大会，但在地下，她仍然用拳头贯彻着自己的正义。]],
suitId = 1070,
suitName = "熊魂武师 晴霜",
suitIcon = "CDN:Icon_PL_284",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411170] = {
id = 411170,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "绯寒花灵 樱",
desc = "樱花飞舞迷离，如我命运难测",
icon = "CDN:Icon_PL_271",
getWay = v4,
jumpId = v5,
useType = v6,
resourceConf = {
model = "SK_PL_271",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_271_Physics"
},
outEnter = "AS_CH_Enter_PL_271",
outShow = "AS_CH_IdleShow_PL_271",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_271",
shareTexts = {
"但见樱花开，令我思往事"
},
shareAnim = v8,
beginTime = v9,
suitStoryTextKey = [[樱是樱花树灵，相传前往树下寻求祝福会获得好运。可樱天生就有些“不幸”，向她祈福的星宝只会碰上各种麻烦事。因此再无星宝问津。

某一天独自起舞的樱遇到了幸。一般的星宝靠近她总会发生不好的事情，但是幸没有，他们一同玩耍，樱很开心，也意识到幸不普通。 

若是能获得幸的幸运，星宝们不就会再来祈福吗？有这个念头的时候，幸突然找到了樱。他倾诉自己的过往：因为幸运而被当作攫取名利的工具，无法自由行动，每次找樱都是偷偷出来……樱是他唯一的朋友。

樱听罢，送给幸一朵樱花——不幸的樱花，携带着，幸的好运便不会生效，那时候没有了“价值”的幸就能自由了。

樱等了不知道多少天，很久，又或许很短。她的身体在渐渐变得透明，遗忘的力量会将她带走。恍惚间，一只温暖的手紧紧的抓住了她，和煦的阳光下，幸在对着她笑。他把事情都搞砸了，再也没有星宝缠着他了，他要一直陪着樱。

透明的手渐渐有了实感，樱意识到，这不就是属于她的幸运吗？]],
suitId = 1072,
suitName = "绯寒花灵 樱",
suitIcon = "CDN:Icon_PL_271",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411171] = {
id = 411171,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "绯寒花灵 樱",
desc = "樱花飞舞迷离，如我命运难测",
icon = "CDN:Icon_PL_271_01",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411170,
fashionValue = 35,
belongToGroup = {
411171,
411172
}
},
resourceConf = {
model = "SK_PL_271",
material = "MI_PL_271_1_HP01;MI_PL_271_2_HP01",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_271_Physics",
materialSlot = "Skin_1;Skin_2"
},
commodityId = 12097,
outEnter = "AS_CH_Enter_PL_271",
outShow = "AS_CH_IdleShow_PL_271",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_271",
shareTexts = {
"但见樱花开，令我思往事"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[411172] = {
id = 411172,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "绯寒花灵 樱",
desc = "樱花飞舞迷离，如我命运难测",
icon = "CDN:Icon_PL_271_02",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
belongTo = 411170,
fashionValue = 35,
belongToGroup = {
411171,
411172
}
},
resourceConf = {
model = "SK_PL_271",
material = "MI_PL_271_1_HP02;MI_PL_271_2_HP02",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_PL_271_Physics",
materialSlot = "Skin_1;Skin_2"
},
commodityId = 12098,
outEnter = "AS_CH_Enter_PL_271",
outShow = "AS_CH_IdleShow_PL_271",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_271",
shareTexts = {
"但见樱花开，令我思往事"
},
shareAnim = v8,
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490051] = {
id = 490051,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "重型兽人",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_030",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_030",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490052] = {
id = 490052,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "装甲巨怪",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_031",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_031",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490053] = {
id = 490053,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "兽人32",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_032_LOD1",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490054] = {
id = 490054,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "火魔",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_033",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_033_LOD0",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490055] = {
id = 490055,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "火魔霸主",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_034",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_034_LOD0",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490056] = {
id = 490056,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "火魔奔袭者",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_035",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_035_LOD0",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490057] = {
id = 490057,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "火魔法师",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_036",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_036_LOD0",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490058] = {
id = 490058,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "中型火魔",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_037",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_037_LOD0",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490059] = {
id = 490059,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "兽人38",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_038_LOD1",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490060] = {
id = 490060,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "兽人39",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_010",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_039_LOD1",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490061] = {
id = 490061,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "胆汁蝙蝠",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_040",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_040",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490062] = {
id = 490062,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "狂野奔袭者",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_041",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_041",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490063] = {
id = 490063,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "狗头人奔袭者",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_042",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_042_LOD0",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[490064] = {
id = 490064,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "爆炸射手",
icon = "CDN:T_UGC_ICON_NPC_MONSTER_043",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "SK_UGC_Monster_043_LOD0",
headOffset = v10,
backOffset = v10,
faceOffset = v10
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[450001] = {
id = 450001,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "主玩法狼人",
icon = "CDN:Icon_Body_010",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70245/Mesh/SK_70245.SK_70245",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "/Game/Feature/OGC/Assets/Placeables/LevelPrivate/70245/Mesh/SK_70245_Physics.SK_70245_Physics"
},
previewShareOffset = v10,
FootwearHeight = 0.0
},
[450020] = {
id = 450020,
effect = true,
maxNum = 1,
exceedReplaceItem = v2,
name = "安可测试",
icon = "CDN:Icon_Body_010",
useType = "IUTO_GiftPackage",
resourceConf = {
model = "/Game/Test/Poggyzhu/Toon/anke350/SK_MC_Anke35.SK_MC_Anke35",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "/Game/Test/Poggyzhu/Toon/anke350/SK_MC_Anke35_Physics.SK_MC_Anke35_Physics",
skeletalMesh = "/Game/Test/Poggyzhu/Toon/anke350/SK_MC_Anke35.SK_MC_Anke35"
},
previewShareOffset = v10,
bodytype = 2,
FootwearHeight = 0.0
},
[499998] = {
id = 499998,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 203,
itemNum = 320
}
},
quality = 1,
name = " 冰雪精灵（测试）",
desc = "融化的雪，凝结成新的我",
icon = "CDN:Icon_OG_030",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 1200
},
resourceConf = {
model = "SK_OG_030",
material = "MI_OG_030_1;MI_OG_030_2;MI_OG_030_3;MI_OG_030_7;MI_OG_030_5;MI_OG_030_6;MI_OG_030_4",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "SK_OG_030_Physics",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Translucent_01;Skin_Opaque_03;Skin_Translucent_02;Skin_Translucent_03",
IconLabelId = 103
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_030_PV_Test/Level_OG_030_Intact_Test",
outIdle = "AS_CH_OutIdle_OG_030",
outShow = "AS_CH_IdleShow_OG_030",
outShowIntervalTime = 20,
soundId = {
4062,
4063
},
outIdle_pv = "LS_PV_OG_030_Test_Idle",
outEnterSequence = "LS_PV_OG_030_Test",
shareTexts = {
"初雪降临时，牵着我的手"
},
shareAnim = v8,
suitStoryTextKey = [[在北境的冰封雪域，有一位冰雪精灵妮薇。传说她晶莹剔透的心灵会为星宝降下祝福，回应他们的祈愿。有许多星宝慕名前去寻找她。然而妮薇知道，她内心的祝福之光从未真正点亮过。

这一天，有星宝找到她，求取一个能让他们快乐玩耍的雪橇。不出所料，妮薇剔透的心灵依旧剔透，无事发生。不忍看大家失望，她带着星宝折树枝搓绳索，做出一个歪歪扭扭的雪橇。星宝忍着笑，随雪橇滑行而去。

妮薇疲惫地坐在雪原上，开始怀疑自己是否真的拥有法力。就在这时，一声震耳欲聋的咆哮划破宁静，一只让星宝畏惧的大雪怪闯入她的领地。妮薇面向雪怪，聚集起周围的风雪，准备奋力一搏。

一股力量忽然在她心中涌动，剔透的光芒从她周身散发，环绕住雪怪。与传说中的凶神恶煞相反，雪怪被光芒制住，顽皮地反身钻进雪地中，消失不见。 这时妮薇背后的雪道上，远远传来星宝们欢呼玩耍的笑声，他们祈愿的快乐似乎应验了。

妮薇恍然明白，她的祝福并不是回应从无到有的奇迹，而是守护星宝创造美好生活的精神。她笑着躺倒在松软的雪地上，感受着心中那股温暖力量。下一次，她就能真正为祈愿的星宝送上祝福了。]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_404320.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_404320.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_404320.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
previewShareOffset = v10,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
firstNoSkip = 1,
FootwearHeight = 0.0
},
[499999] = {
id = 499999,
effect = true,
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "哈士奇（测试）",
desc = "永远不要忘记让自己快乐",
icon = "CDN:Icon_Body_010",
getWay = v4,
jumpId = v5,
useType = v6,
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_010",
upper = "SK_Body_Upper_010",
bottom = "SK_Body_Under_010",
gloves = "SK_Body_Hands_010",
face = "SK_Body_Face_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareAnim = v8,
suitIcon = "CDN:Icon_Body_010",
previewShareOffset = v10,
FootwearHeight = 0.0
},
[469001] = {
id = 469001,
name = "街头风格",
desc = "街头风格",
icon = "Icon_SP_001",
resourceConf = {
model = "SK_StarP_Player_S1_M_Yundongnan_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
scaleTimes = 1,
shareTexts = {
"街头"
},
shareOffset = {
1
}
},
[469002] = {
id = 469002,
name = "街头风格",
desc = "街头风格",
icon = "Icon_SP_001",
resourceConf = {
model = "SK_StarP_Player_S1_F_Yundongnv_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
scaleTimes = 1,
shareTexts = {
"街头"
},
shareOffset = {
1
}
},
[469011] = {
id = 469011,
name = "侦探风格",
desc = "侦探风格",
icon = "Icon_SP_002",
resourceConf = {
model = "SK_StarP_Player_S1_M_Zhentanshenan_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
scaleTimes = 1,
shareTexts = {
"侦探"
},
shareOffset = {
1
}
},
[469012] = {
id = 469012,
name = "侦探风格",
desc = "侦探风格",
icon = "Icon_SP_002",
resourceConf = {
model = "SK_StarP_Player_S1_F_Zhentanshenv_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
scaleTimes = 1,
shareTexts = {
"侦探"
},
shareOffset = {
1
}
},
[469021] = {
id = 469021,
name = "科研风格",
desc = "科研风格",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Player_S1_M_Keyanjianan_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
}
},
[469022] = {
id = 469022,
name = "科研风格",
desc = "科研风格",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Player_S1_F_Keyanjianv_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
}
},
[469031] = {
id = 469031,
name = "SP3.5头身测试",
desc = "SP3.5头身测试描述",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Armor_S1_M_Jichutaonan35_Head_001",
upper = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Clothes_001",
bottom = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Leg_001",
gloves = "SK_StarP_Armor_S1_M_Jichutaonan35_Hand_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
skeletalMesh = "SK_StarP_Armor_S1_M_Jingyingwei35_001"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
},
bodytype = 2
},
[469032] = {
id = 469032,
name = "SP3.5头身测试",
desc = "SP3.5头身测试描述",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Armor_S1_F_Jichutaonv35_Head_001",
upper = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Clothes_001",
bottom = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Leg_001",
gloves = "SK_StarP_Armor_S1_F_Jichutaonv35_Hand_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
skeletalMesh = "SK_StarP_Armor_S1_M_Jingyingwei35_001"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
},
bodytype = 2
},
[469041] = {
id = 469041,
name = "SP3.5头身测试",
desc = "SP3.5头身测试描述",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Armor_S1_M_Jichutaonan35_Head_001",
upper = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Clothes_001",
bottom = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Leg_001",
gloves = "SK_StarP_Armor_S1_M_Jichutaonan35_Hand_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
skeletalMesh = "SK_StarP_Armor_S1_M_Jingyingwei35_001"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
},
bodytype = 2
},
[469042] = {
id = 469042,
name = "SP3.5头身测试",
desc = "SP3.5头身测试描述",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Armor_S1_F_Jichutaonv35_Head_001",
upper = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Clothes_001",
bottom = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Leg_001",
gloves = "SK_StarP_Armor_S1_F_Jichutaonv35_Hand_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
skeletalMesh = "SK_StarP_Armor_S1_M_Jingyingwei35_001"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
},
bodytype = 2
},
[469051] = {
id = 469051,
name = "SP3.5头身测试",
desc = "SP3.5头身测试描述",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Armor_S1_M_Jichutaonan35_Head_001",
upper = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Clothes_001",
bottom = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Leg_001",
gloves = "SK_StarP_Armor_S1_M_Jichutaonan35_Hand_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
skeletalMesh = "SK_StarP_Armor_S1_M_Jingyingwei35_001"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
},
bodytype = 2
},
[469052] = {
id = 469052,
name = "SP3.5头身测试",
desc = "SP3.5头身测试描述",
icon = "Icon_SP_003",
resourceConf = {
model = "SK_StarP_Armor_S1_F_Jichutaonv35_Head_001",
upper = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Clothes_001",
bottom = "SK_StarP_Armor_S1_M_Zhuzhutaonan35_Leg_001",
gloves = "SK_StarP_Armor_S1_F_Jichutaonv35_Hand_001",
headOffset = v10,
backOffset = v10,
faceOffset = v10,
physics = "ALL_ceshi_ModSuit_Physics_Head",
skeletalMesh = "SK_StarP_Armor_S1_M_Jingyingwei35_001"
},
scaleTimes = 1,
shareTexts = {
"科研"
},
shareOffset = {
1
},
bodytype = 2
}
}

local mt = {
effect = false,
type = "ItemType_Suit",
quality = 2,
outlookConf = {
fashionValue = 300
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
billboardOffsetZ = 0,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false,
shareOffset = v10,
bodytype = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data