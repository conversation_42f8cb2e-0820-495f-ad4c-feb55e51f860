--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_小游戏每日胜局奖励.xlsx: 任务

local data = {
[910011] = {
id = 910011,
name = "任意玩法胜利一次",
desc = "任意玩法胜利一次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 1
}
}
}
},
reward = {
itemIdList = {
320057
},
numList = {
1
}
},
taskGroupId = 9100
},
[910012] = {
id = 910012,
name = "任意玩法胜利三次",
desc = "任意玩法胜利三次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 3
}
}
}
},
reward = {
itemIdList = {
320057
},
numList = {
1
}
},
taskGroupId = 9100
},
[910013] = {
id = 910013,
name = "任意玩法胜利五次",
desc = "任意玩法胜利五次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 5
}
}
}
},
reward = {
itemIdList = {
320057
},
numList = {
2
}
},
taskGroupId = 9100
},
[910014] = {
id = 910014,
name = "任意玩法胜利七次",
desc = "任意玩法胜利七次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 7
}
}
}
},
reward = {
itemIdList = {
320057
},
numList = {
3
}
},
taskGroupId = 9100
},
[910115] = {
id = 910115,
name = "任意玩法胜利一次",
desc = "任意玩法胜利一次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 1
}
}
}
},
reward = {
itemIdList = {
320057
},
numList = {
1
}
},
taskGroupId = 9101
},
[910116] = {
id = 910116,
name = "任意玩法胜利三次",
desc = "任意玩法胜利三次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 3
}
}
}
},
reward = {
itemIdList = {
320057
},
numList = {
1
}
},
taskGroupId = 9101
},
[910117] = {
id = 910117,
name = "任意玩法胜利五次",
desc = "任意玩法胜利五次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 39,
value = 5
}
}
}
},
reward = {
itemIdList = {
320057
},
numList = {
2
}
},
taskGroupId = 9101
},
[910200] = {
id = 910200,
name = "收藏小游戏",
desc = "收藏小游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
910200
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
}
},
taskGroupId = 9102
},
[910201] = {
id = 910201,
name = "添加到桌面小游戏",
desc = "添加到桌面小游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
910201
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
}
},
taskGroupId = 9103
},
[910202] = {
id = 910202,
name = "VA/手Q添加到桌面小游戏",
desc = "VA/手Q添加到桌面小游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
910202
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
}
},
taskGroupId = 9107
},
[910203] = {
id = 910203,
name = "VA/手Q添加组件",
desc = "VA/手Q添加组件",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
910203
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
}
},
taskGroupId = 9107
},
[910204] = {
id = 910204,
name = "VA/手Q添加到彩签",
desc = "VA/手Q添加到彩签",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 64,
value = 1,
subConditionList = {
{
type = 4,
value = {
910204
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
20
}
},
taskGroupId = 9107
},
[910130] = {
id = 910130,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
500
}
},
taskGroupId = 9104
},
[910131] = {
id = 910131,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
320104
},
numList = {
1
}
},
taskGroupId = 9104
},
[910132] = {
id = 910132,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 9104
},
[910133] = {
id = 910133,
name = "每日获得80奖杯",
desc = "每日获得80奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311010
},
numList = {
1
}
},
taskGroupId = 9104
},
[910401] = {
id = 910401,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
200120
},
numList = {
3
}
},
taskGroupId = 9105
},
[910402] = {
id = 910402,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
320104
},
numList = {
1
}
},
taskGroupId = 9105
},
[910403] = {
id = 910403,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 9105
},
[910404] = {
id = 910404,
name = "每日获得80奖杯(附卡包)",
desc = "每日获得80奖杯(附卡包)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311010,
290001
},
numList = {
1,
1
}
},
taskGroupId = 9105
},
[910405] = {
id = 910405,
name = "每日获得80奖杯(附卡包)",
desc = "每日获得80奖杯(附卡包)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311010,
290002
},
numList = {
1,
1
}
},
taskGroupId = 9105
},
[910501] = {
id = 910501,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
200120
},
numList = {
3
}
},
taskGroupId = 9106
},
[910502] = {
id = 910502,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
320104
},
numList = {
1
}
},
taskGroupId = 9106
},
[910503] = {
id = 910503,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 9106
},
[910504] = {
id = 910504,
name = "每日获得80奖杯",
desc = "每日获得80奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311010
},
numList = {
1
}
},
taskGroupId = 9106
},
[910601] = {
id = 910601,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
200120
},
numList = {
3
}
},
taskGroupId = 9108
},
[910602] = {
id = 910602,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
320104
},
numList = {
1
}
},
taskGroupId = 9108
},
[910603] = {
id = 910603,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 9108
},
[910604] = {
id = 910604,
name = "每日获得80奖杯(附卡包)",
desc = "每日获得80奖杯(附卡包)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311010,
290011
},
numList = {
1,
1
}
},
taskGroupId = 9108
},
[910605] = {
id = 910605,
name = "每日获得80奖杯(附卡包)",
desc = "每日获得80奖杯(附卡包)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311010,
290012
},
numList = {
1,
1
}
},
taskGroupId = 9108
},
[910701] = {
id = 910701,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
200120
},
numList = {
3
}
},
taskGroupId = 9109
},
[910702] = {
id = 910702,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
320104
},
numList = {
1
}
},
taskGroupId = 9109
},
[910703] = {
id = 910703,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 9109
},
[910704] = {
id = 910704,
name = "每日获得80奖杯",
desc = "每日获得80奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311010
},
numList = {
1
}
},
taskGroupId = 9109
},
[910801] = {
id = 910801,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
200120
},
numList = {
3
}
},
taskGroupId = 91100
},
[910802] = {
id = 910802,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
311008
},
numList = {
1
}
},
taskGroupId = 91100
},
[910803] = {
id = 910803,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 91100
},
[910804] = {
id = 910804,
name = "每日获得80奖杯",
desc = "每日获得80奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311009
},
numList = {
1
}
},
taskGroupId = 91100
},
[910901] = {
id = 910901,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
200120
},
numList = {
3
}
},
taskGroupId = 91101
},
[910902] = {
id = 910902,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
311108
},
numList = {
1
}
},
taskGroupId = 91101
},
[910903] = {
id = 910903,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 91101
},
[910904] = {
id = 910904,
name = "每日获得80奖杯",
desc = "每日获得80奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311109
},
numList = {
1
}
},
taskGroupId = 91101
},
[911001] = {
id = 911001,
name = "每日获得10奖杯",
desc = "每日获得10奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 10
}
}
}
},
reward = {
itemIdList = {
200120
},
numList = {
3
}
},
taskGroupId = 91102
},
[911002] = {
id = 911002,
name = "每日获得20奖杯",
desc = "每日获得20奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 20
}
}
}
},
reward = {
itemIdList = {
311111
},
numList = {
1
},
cupsTaskCycleReward = {
92,
93
}
},
taskGroupId = 91102
},
[911003] = {
id = 911003,
name = "每日获得50奖杯",
desc = "每日获得50奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 50
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
30
}
},
taskGroupId = 91102
},
[911004] = {
id = 911004,
name = "每日获得80奖杯",
desc = "每日获得80奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 300,
value = 80
}
}
}
},
reward = {
itemIdList = {
311112
},
numList = {
1
}
},
taskGroupId = 91102
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data