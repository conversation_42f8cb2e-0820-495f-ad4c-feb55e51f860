--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_礼包_特色玩法运营.xlsx: 自选礼包

local data = {
[331001] = {
id = 331001,
effect = true,
quality = 3,
name = "3选1宝箱",
desc = "可以从以下奖励：迷你鱼竿、随心铲、云朵币*50 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_BagBig_008",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
620393,
620267,
6
},
itemNums = {
1,
1,
50
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331002] = {
id = 331002,
effect = true,
quality = 3,
name = "非凡皮肤自选礼",
desc = "可以从以下奖励：灵魂歌姬 宝拉（试用30天）、大魔术师 尼克（试用30天）选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Skin_01",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
400350,
400440
},
itemNums = {
1,
1
},
expireDays = {
30,
30
},
packagePickConf = {
pickNum = 1
}
}
},
[331004] = {
id = 331004,
effect = true,
quality = 3,
name = "2选1称号礼包",
desc = "可以从以下奖励：最佳狼搭子、最损狼队友中选择1个领取哦。",
icon = "CDN:T_Common_Item_System_BagBig_008",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
861000,
861001
},
itemNums = {
1,
1
},
expireDays = {
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331005] = {
id = 331005,
effect = true,
quality = 2,
name = "蔚海小摊自选礼盒",
desc = "可以从以下奖励：璃海星光果行、海盗宝藏小店、海妖鱼店 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Farm1122",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
218125,
218126,
218127
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331006] = {
id = 331006,
effect = true,
maxNum = 1,
quality = 3,
name = "配饰自选礼",
desc = "可以从以下奖励：金水玩家、铁好星宝中选择1个领取哦。",
icon = "CDN:T_Common_Item_Bridalgift_02",
getWay = "参与活动获取",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
630278,
610171
},
itemNums = {
1,
1
},
expireDays = {
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331007] = {
id = 331007,
effect = true,
maxNum = 1,
quality = 3,
name = "身份自选礼",
desc = "可以从以下身份：捕梦者、雾隐狼中选择1个领取哦。",
icon = "CDN:T_Common_Item_Bridalgift_01",
getWay = "参与活动获取",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240814,
240818
},
itemNums = {
1,
1
},
expireDays = {
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331008] = {
id = 331008,
effect = true,
quality = 2,
name = "雪境小摊自选礼盒",
desc = "可以从以下奖励：雪花轻语果行、水晶鹿角小店、雪乡小Q鱼铺 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_ScarecrowSnow",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
218134,
218135,
218136
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331009] = {
id = 331009,
effect = true,
quality = 3,
name = "宠物装饰自选礼盒",
desc = "可以从以下奖励：鹿角发箍、花环围脖、礼物口袋 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Farm9",
getWay = "参与活动获取",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
219413,
219611,
219809
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[331015] = {
id = 331015,
effect = true,
quality = 2,
name = "英雄自选宝箱",
desc = "可以从以下奖励：峡谷英雄-赵云、峡谷英雄-孙尚香、峡谷币*30 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_BagBig_011",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301110,
301114,
3541
},
itemNums = {
1,
1,
30
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331010] = {
id = 331010,
effect = true,
quality = 2,
name = "3选1宝箱",
desc = "可以从以下奖励：阵营卡*1；狼人币*100；福利券*50中选择1个领取哦",
icon = "CDN:T_Common_Item_System_BagBig_008",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
13,
200101,
220
},
itemNums = {
1,
1,
50
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331102] = {
id = 331102,
effect = true,
quality = 3,
name = "非凡皮肤自选礼",
desc = "可以从以下奖励：灵魂歌姬 宝拉（试用30天）、大魔术师 尼克（试用30天）、星宝印章*50选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Skin_01",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
400350,
400440,
4
},
itemNums = {
1,
1,
50
},
expireDays = {
30,
30,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331016] = {
id = 331016,
effect = true,
quality = 2,
name = "福运小摊自选礼盒",
desc = "可以从以下奖励：金玉醒狮果行、金闪闪小铺、鲤跃龙门鱼铺 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Farm_Building",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
218152,
218153,
218154
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[331017] = {
id = 331017,
effect = true,
quality = 3,
name = "宠物装饰自选礼盒",
desc = "可以从以下奖励：发财帽、红围巾、纳福袋 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Farm16",
getWay = "参与活动获取",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
219414,
219612,
219810
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[331020] = {
id = 331020,
effect = true,
quality = 2,
name = "狼人自选礼",
desc = "可以从以下奖励：身份卡*1；阵营卡*3；狼人币*200中选择1个领取哦",
icon = "CDN:T_Common_Item_System_WerewolfBag_15",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
200102,
200101,
13
},
itemNums = {
1,
3,
200
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331018] = {
id = 331018,
effect = true,
quality = 2,
name = "桃坞小摊自选礼盒",
desc = "可以从以下奖励：江南果行、芙蓉商行、观鱼小铺 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Optional01",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
218162,
218163,
218164
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[331021] = {
id = 331021,
effect = true,
quality = 3,
name = "贴纸活动自选礼盒",
desc = "可以从以下奖励：小二哈、贴纸礼盒 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_BagBig_008",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
219201,
310736
},
itemNums = {
1,
1
},
expireDays = {
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1
}
}
}
},
[331019] = {
id = 331019,
effect = true,
quality = 2,
name = "珍馐小摊自选礼盒",
desc = "可以从以下奖励：三明治果行、罐罐茶小铺、豪华寿司鱼店 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_RewardSelect01",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
218172,
218173,
218174
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[331022] = {
id = 331022,
effect = true,
quality = 3,
name = "互动道具自选礼",
desc = "可以从以下奖励：爱心*3、倒咖啡*3、丢鸡蛋*3、扔炸弹*3 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_BagBig_008",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240403,
240406,
240401,
240405
},
itemNums = {
3,
3,
3,
3
},
expireDays = {
0,
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1,
1
}
}
}
},
[331023] = {
id = 331023,
effect = true,
quality = 2,
name = "英雄体验卡自选",
desc = "可以从以下奖励：英雄-妲己（3天）、英雄-甄姬",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301511,
301607,
3541
},
itemNums = {
1,
1,
20
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331024] = {
id = 331024,
effect = true,
quality = 2,
name = "英雄体验卡自选",
desc = "可以从以下奖励：英雄-公孙离（7天）、英雄-貂蝉（7天）、峡谷币*50",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301603,
301601,
3541
},
itemNums = {
1,
1,
50
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331025] = {
id = 331025,
effect = true,
quality = 2,
name = "英雄体验卡自选",
desc = "可以从以下奖励：英雄-云缨（7天）、英雄-李白（7天）、峡谷币*50",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301510,
301512,
3541
},
itemNums = {
1,
1,
50
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331026] = {
id = 331026,
effect = true,
quality = 2,
name = "甜梦小摊自选礼盒",
desc = "可以从以下奖励：海盐甜筒果行、樱桃蛋糕小铺、海盗星船鱼铺 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_RewardSelect03",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
218188,
218189,
218190
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[331027] = {
id = 331027,
effect = true,
quality = 3,
name = "身份体验自选礼",
desc = "可以从以下身份体验奖励：墨鱼（1天）、法官（1天）、假面狼（1天）、傀儡狼（1天）、狼人币*50 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_WerewolfBag_18",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240833,
240803,
240807,
240836,
13
},
itemNums = {
1,
1,
1,
1,
50
},
expireDays = {
1,
1,
1,
1,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1,
1,
1
}
}
}
},
[331028] = {
id = 331028,
effect = true,
quality = 3,
name = "身份体验自选礼",
desc = "可以从以下身份体验奖励：捕梦者（3天）、流浪汉（3天）、墨鱼（3天）、法官（3天）、假面狼（3天）、傀儡狼（3天）、狼人币*50 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_WerewolfBag_19",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240814,
240801,
240833,
240803,
240807,
240836,
13
},
itemNums = {
1,
1,
1,
1,
1,
1,
50
},
expireDays = {
3,
3,
3,
3,
3,
3,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1,
1,
1,
1,
1
}
}
}
},
[331029] = {
id = 331029,
effect = true,
quality = 2,
name = "4选1表情包",
desc = "可以从以下狼人表情：无奈、扎心、疑问、别说了中任选1个",
icon = "CDN:T_Common_Item_System_WerewolfBag_General02",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240607,
240615,
240611,
240617
},
itemNums = {
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1,
1
}
}
}
},
[331030] = {
id = 331030,
effect = true,
quality = 3,
name = "互动道具自选礼",
desc = "可以从以下奖励：爱心*3、倒咖啡*3、丢鸡蛋*3、扔炸弹*3 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_BagBig_008",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240403,
240406,
240401,
240405
},
itemNums = {
3,
3,
3,
3
},
expireDays = {
0,
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331031] = {
id = 331031,
effect = true,
quality = 2,
name = "身份自选礼",
desc = "可以从以下身份：急救员、快递员中选择1个领取哦。",
icon = "CDN:T_Common_Item_Bridalgift_03",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
240820,
240823
},
itemNums = {
1,
1
},
expireDays = {
1,
1
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1
}
}
}
},
[331032] = {
id = 331032,
effect = true,
quality = 2,
name = "菜品自选礼",
desc = "可以从以下菜品：草莓大福、拍黄瓜、白灼菜心、凉拌海带、红烧香菇、卤鸡腿、糖醋排骨、黑椒牛排、烤鱼、叉烧中选择1个领取哦。",
icon = "CDN:T_Common_Item_System_Giftbox",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
317131,
317132,
317133,
317134,
317135,
317136,
317137,
317138,
317139,
317140
},
itemNums = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
},
expireDays = {
0,
0,
0,
0,
0,
0,
0,
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1,
1,
1,
1,
1,
1,
1,
1
}
}
}
},
[331033] = {
id = 331033,
effect = true,
quality = 2,
name = "英雄体验卡自选",
desc = "可以从以下奖励：英雄-晴霜（3天）、英雄-绮（7天）、峡谷币*50",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301232,
301233,
3541
},
itemNums = {
1,
1,
50
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331034] = {
id = 331034,
effect = true,
quality = 2,
name = "英雄自选宝箱",
desc = "可以从以下奖励：英雄-孙悟空、英雄-妲己、峡谷币*150",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301116,
301125,
3541
},
itemNums = {
1,
1,
150
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1
}
}
},
[331035] = {
id = 331035,
effect = true,
quality = 2,
name = "天穹小摊自选礼盒",
desc = "可以从以下奖励：星露花台、天穹彩虹小店、灵泉圣庭 选择1个领取哦。",
icon = "CDN:T_Common_Item_System_RewardSelect03",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
218210,
218211,
218212
},
itemNums = {
1,
1,
1
},
expireDays = {
0,
0,
0
},
packagePickConf = {
pickNum = 1,
pickLimit = {
1,
1,
1
}
}
}
},
[331036] = {
id = 331036,
effect = true,
quality = 2,
name = "王昭君英雄自选宝箱",
desc = "可以从以下奖励：英雄-王昭君、峡谷币*200",
icon = "CDN:T_Common_Item_System_RewardSelect02",
packageConf = {
packageType = "PackageType_Pick",
itemIds = {
301116,
3541
},
itemNums = {
1,
200
},
expireDays = {
0,
0
},
packagePickConf = {
pickNum = 1
}
}
}
}

local mt = {
effect = false,
type = "ItemType_Package",
stackedNum = 999,
getWay = "参与活动",
bagId = 1,
useType = "IUTO_GiftPackage"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data