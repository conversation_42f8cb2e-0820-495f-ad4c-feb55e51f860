--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 气泡框

local v0 = 2

local data = {
[870001] = {
id = 870001,
name = "心愿烟花",
desc = "暂未开放",
icon = "CDN:T_Common_Item_BubbleFrame_002",
picture = "CDN:Icon_BubbleFrame_002",
bubbleColor = "#fac569e6",
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[870002] = {
id = 870002,
name = "春暖花开",
desc = "暂未开放",
icon = "CDN:T_Common_Item_BubbleFrame_003",
picture = "CDN:Icon_BubbleFrame_003",
bubbleColor = "#92e657e6",
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[870003] = {
id = 870003,
name = "萌龙送瑞",
desc = "可前往春节庆典活动兑换获得",
icon = "CDN:T_Common_Item_BubbleFrame_001",
picture = "D_Chatbox_001",
isDynamic = 1,
bubbleColor = "#f16551e6",
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[870005] = {
id = 870005,
name = "三花猫",
desc = "官方社区活跃获得",
icon = "CDN:T_Common_Item_BubbleFrame_005",
picture = "CDN:Icon_BubbleFrame_005",
bubbleColor = "#ffa415e6",
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[870006] = {
id = 870006,
lowVer = "1.2.90.66",
name = "初春之韵",
desc = "初春签到礼活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_006",
picture = "CDN:Icon_BubbleFrame_006",
bubbleColor = "#89e1eae6",
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[870007] = {
id = 870007,
lowVer = "1.2.90.66",
name = "小丸子和小玉",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_007",
picture = "CDN:Icon_BubbleFrame_007",
bubbleColor = "#ffd981e6",
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[870008] = {
id = 870008,
lowVer = "1.2.90.66",
name = "小甜豆",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_008",
picture = "CDN:Icon_BubbleFrame_008",
bubbleColor = "#ffd981e6",
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[870009] = {
id = 870009,
lowVer = "1.2.100.46",
quality = 2,
name = "永恒之誓",
desc = "永恒之誓祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_009",
picture = "D_Chatbox_003",
isDynamic = 1,
bubbleColor = "#eb92e2e6",
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[870010] = {
id = 870010,
name = "亲密无间",
desc = "达成亲密关系等级获得",
icon = "CDN:T_Common_Item_BubbleFrame_010",
picture = "CDN:Icon_BubbleFrame_010",
bubbleColor = "#ffb1cce6",
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[870011] = {
id = 870011,
name = "情投意合",
desc = "默契商店兑换获得",
icon = "CDN:T_Common_Item_BubbleFrame_011",
picture = "D_Chatbox_004",
isDynamic = 1,
bubbleColor = "#ffafb1e6",
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[870012] = {
id = 870012,
name = "布朗熊",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_012",
picture = "CDN:Icon_BubbleFrame_012",
bubbleColor = "#5898f7",
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[870013] = {
id = 870013,
quality = 2,
name = "剑启黎明",
desc = "动态气泡框，战神颂歌祈愿活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_017",
picture = "D_Chatbox_006",
isDynamic = 1,
bubbleColor = "#7481b2e6",
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[870014] = {
id = 870014,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
quality = 2,
name = "闪耀之语",
desc = "参加《闪电赛》活动，积累闪电奖章获得",
icon = "CDN:T_Common_Item_BubbleFrame_014",
picture = "D_Chatbox_007",
isDynamic = 1,
bubbleColor = "#ffd981e6",
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[870015] = {
id = 870015,
name = "夏日海歌",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_015",
picture = "CDN:Icon_BubbleFrame_015",
bubbleColor = "#1cbcffe6",
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[870016] = {
id = 870016,
quality = 2,
name = "Kuromi冒泡",
desc = "寻梦嘉年华活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_016",
picture = "CDN:Icon_BubbleFrame_016",
bubbleColor = "#503c70e6",
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[870017] = {
id = 870017,
name = "铠-光刃斩",
desc = "峡谷竞技活动获得",
icon = "CDN:T_Arena_Item_BubbleFrame_001",
picture = "CDN:Icon_Arena_BubbleFrame_001",
bubbleColor = "#615154",
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[870018] = {
id = 870018,
lowVer = "1.3.12.1",
name = "甜心幻梦",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_018",
picture = "CDN:Icon_BubbleFrame_018",
beginTime = {
seconds = 1721318400
},
bubbleColor = "#ff7075e6",
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[870019] = {
id = 870019,
lowVer = "1.3.12.36",
name = "长相思",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_019",
picture = "CDN:Icon_BubbleFrame_019",
beginTime = {
seconds = 1721836800
},
bubbleColor = "#ff531de6",
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[870020] = {
id = 870020,
name = "蔡文姬-行花令",
desc = "峡谷相逢通行证获得",
icon = "CDN:T_Arena_Item_BubbleFrame_002",
picture = "CDN:Icon_Arena_BubbleFrame_002",
showInView = 0,
bubbleColor = "#ce712a",
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[870021] = {
id = 870021,
lowVer = "1.3.12.92",
quality = 2,
name = "凤求凰",
desc = "动态气泡框，凤求凰祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_021",
picture = "D_Chatbox_008",
isDynamic = 1,
bubbleColor = "#feb432e6",
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[870022] = {
id = 870022,
name = "花语之韵",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_022",
picture = "CDN:Icon_BubbleFrame_022",
beginTime = {
seconds = 1724947200
},
bubbleColor = "#38b174e6",
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[870023] = {
id = 870023,
name = "西南品咖啡",
desc = "秘境探索礼活动专属赠礼",
icon = "CDN:T_Common_Item_BubbleFrame_020",
picture = "CDN:Icon_BubbleFrame_020",
bubbleColor = "#38b174e6",
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[870024] = {
id = 870024,
lowVer = "1.3.18.72",
quality = 2,
name = "三彩逸士",
desc = "动态气泡框，千都三彩祈愿活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_023",
picture = "D_Chatbox_009",
isDynamic = 1,
bubbleColor = "#ffe393e6",
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[870025] = {
id = 870025,
lowVer = "1.3.26.1",
name = "月光星愿",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_024",
picture = "CDN:Icon_BubbleFrame_024",
beginTime = {
seconds = 1729180800
},
bubbleColor = "#354798e6",
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[870026] = {
id = 870026,
lowVer = "1.3.26.68",
quality = 2,
name = "永恒之舞",
desc = "动态气泡框，永恒之舞祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_025",
picture = "D_Chatbox_010",
isDynamic = 1,
bubbleColor = "#d91b36e6",
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[870027] = {
id = 870027,
lowVer = "1.3.26.68",
quality = 2,
name = "蔚海绮梦",
desc = "动态气泡框，蔚海绮梦祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_026",
picture = "D_Chatbox_011",
isDynamic = 1,
bubbleColor = "#e47c4fe6",
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[870028] = {
id = 870028,
lowVer = "1.3.37.1",
quality = 2,
name = "侦探讯息",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_029",
picture = "CDN:Icon_BubbleFrame_029",
beginTime = {
seconds = 1734624000
},
bubbleColor = "#8b4b13e6",
mallJumpId = {
17
},
jumpText = {
"等级任务页签"
}
},
[870029] = {
id = 870029,
lowVer = "1.3.36.1",
name = "宝宝奶昔",
desc = "参与活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_028",
picture = "CDN:Icon_BubbleFrame_028",
bubbleColor = "#ff687ce6",
mallJumpId = {
19
},
jumpText = {
"时装图鉴页签"
}
},
[870030] = {
id = 870030,
lowVer = "1.3.36.1",
name = "巧心巧意",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_027",
picture = "CDN:Icon_BubbleFrame_027",
beginTime = {
seconds = 1732809600
},
bubbleColor = "#d28f4fe6",
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[870031] = {
id = 870031,
lowVer = "1.3.37.68",
quality = 2,
name = "雪境欢颂",
desc = "动态气泡框，雪境欢颂祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_032",
picture = "D_Chatbox_012",
isDynamic = 1,
bubbleColor = "#85b5ffe6",
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[870032] = {
id = 870032,
lowVer = "1.3.26.68",
name = "牧场物语",
desc = "卡牌集换活动中获得",
icon = "CDN:T_Common_Item_BubbleFrame_030",
picture = "CDN:Icon_BubbleFrame_030",
beginTime = {
seconds = 1732809600
},
bubbleColor = "#75cf4be6",
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[870033] = {
id = 870033,
lowVer = "1.3.36.1",
name = "一岁一礼",
desc = "参与活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_031",
picture = "CDN:Icon_BubbleFrame_031",
bubbleColor = "#ff6d7be6",
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[870034] = {
id = 870034,
lowVer = "1.3.68.26",
quality = 2,
name = "福运琳琅",
desc = "动态气泡框，福运琳琅祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_037",
picture = "D_Chatbox_014",
isDynamic = 1,
bubbleColor = "#fe3e3de6",
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[870035] = {
id = 870035,
lowVer = "1.3.68.33",
quality = 2,
name = "天启圣谕",
desc = "动态气泡框，天启圣谕祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_033",
picture = "D_Chatbox_013",
isDynamic = 1,
beginTime = {
seconds = 1737993600
},
bubbleColor = "#96bbf8e6",
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[870036] = {
id = 870036,
lowVer = "1.3.36.1",
name = "星想柿橙",
desc = "参与活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_035",
picture = "CDN:Icon_BubbleFrame_035",
bubbleColor = "#ff9828e6",
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[870037] = {
id = 870037,
lowVer = "1.3.68.1",
name = "杨玉环-霓裳羽衣",
desc = "峡谷星春福袋获得",
icon = "CDN:CT_Arena_Item_BubbleFrame_007",
picture = "CDN:CT_Icon_Arena_BubbleFrame_007",
showInView = 0,
bubbleColor = "#3584c8"
},
[870038] = {
id = 870038,
lowVer = "1.3.68.1",
quality = 2,
name = "云海之上",
desc = "峡谷祈愿活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_036",
picture = "CDN:Icon_BubbleFrame_036",
beginTime = {
seconds = 1738166400
},
bubbleColor = "#bc7b3ae6"
},
[870039] = {
id = 870039,
lowVer = "1.3.68.87",
quality = 2,
name = "纤尘不染",
desc = "峡谷女明星活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_039",
picture = "CDN:Icon_BubbleFrame_039",
beginTime = {
seconds = 1739548800
},
bubbleColor = "#cb8654e6"
},
[870040] = {
id = 870040,
lowVer = "1.3.68.87",
quality = 2,
name = "冰雪之华",
desc = "峡谷女明星活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_038",
picture = "CDN:Icon_BubbleFrame_038",
beginTime = {
seconds = 1739548800
},
bubbleColor = "#3562e1e6"
},
[870041] = {
id = 870041,
lowVer = "1.3.68.116",
quality = 2,
name = "桃坞问春",
desc = "桃坞问春祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_041",
picture = "CDN:Icon_BubbleFrame_041",
beginTime = {
seconds = 1740758400
},
bubbleColor = "#4c75b8e6"
},
[870042] = {
id = 870042,
lowVer = "1.3.68.100",
quality = 2,
name = "桃源万千",
desc = "动态气泡框，桃源万千祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_040",
picture = "D_Chatbox_015",
isDynamic = 1,
beginTime = {
seconds = 1740067200
},
bubbleColor = "#d96c41e6"
},
[870043] = {
id = 870043,
lowVer = "1.3.78.33",
quality = 2,
name = "珍馐百味",
desc = "动态气泡框，珍馐百味祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_042",
picture = "D_Chatbox_016",
isDynamic = 1,
bubbleColor = "#ec4a49e6"
},
[870044] = {
id = 870044,
lowVer = "1.3.68.1",
name = "真相大白",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_043",
picture = "CDN:Icon_BubbleFrame_043",
beginTime = {
seconds = 1741881600
},
bubbleColor = "#f9b150e6"
},
[870045] = {
id = 870045,
name = "美好定格",
desc = "卡牌集换活动中获得",
icon = "CDN:T_Common_Item_BubbleFrame_044",
picture = "CDN:Icon_BubbleFrame_044",
beginTime = {
seconds = 1741190400
},
bubbleColor = "#ffab51e6"
},
[870046] = {
id = 870046,
lowVer = "1.3.78.33",
quality = 2,
name = "甜梦嘉年华",
desc = "动态气泡框，甜梦嘉年华祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_047",
picture = "D_Chatbox_017",
isDynamic = 1,
beginTime = {
seconds = 1744128000
},
bubbleColor = "#ec4a49e6"
},
[870047] = {
id = 870047,
quality = 2,
name = "小熊猫",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_045",
picture = "CDN:Icon_BubbleFrame_045",
bubbleColor = "#b13d26e6"
},
[870048] = {
id = 870048,
quality = 2,
name = "喵喵节",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_048",
picture = "CDN:Icon_BubbleFrame_048",
bubbleColor = "#b89dfbe6"
},
[870049] = {
id = 870049,
lowVer = "1.3.88.1",
quality = 2,
name = "幻彩调律",
desc = "动态气泡框，幻彩调律祈愿获得",
icon = "CDN:T_Common_Item_BubbleFrame_050",
picture = "D_Chatbox_018",
isDynamic = 1,
beginTime = {
seconds = 1747324800
},
bubbleColor = "#4078ffe6"
},
[870050] = {
id = 870050,
lowVer = "1.3.88.105",
quality = 2,
name = "艾琳-奇遇舞章",
desc = "奇遇舞章祈愿活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_052",
picture = "CDN:Icon_BubbleFrame_052",
beginTime = {
seconds = 1749830400
},
bubbleColor = "#ffe4e2e6"
},
[870051] = {
id = 870051,
lowVer = "1.3.88.153",
name = "超级可口甜点",
desc = "活动获得",
icon = "CDN:T_Common_Item_BubbleFrame_053",
picture = "CDN:Icon_BubbleFrame_053",
beginTime = {
seconds = 1750694400
},
bubbleColor = "#fffefae6"
}
}

local mt = {
type = "ItemType_ChatBubble",
maxNum = 1,
quality = 3,
showInView = 1,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data