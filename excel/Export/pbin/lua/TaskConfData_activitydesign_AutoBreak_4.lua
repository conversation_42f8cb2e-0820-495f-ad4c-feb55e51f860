--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置_策划专用.xlsx: 活动任务

local v0 = 726

local data = {
[450121] = {
id = 450121,
name = "第1天",
desc = "第1天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
1005
},
numList = {
100
},
validPeriodList = {
0
}
},
taskGroupId = 44020
},
[450122] = {
id = 450122,
name = "第2天",
desc = "第2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
200006
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44020
},
[450131] = {
id = 450131,
name = "活动期间累计获得200个奖杯",
desc = "活动期间累计获得200个奖杯",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 600,
value = 200,
subConditionList = {
{
type = 146,
value = {
5028
}
}
}
}
}
}
},
reward = {
itemIdList = {
530122
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 44021
},
[440511] = {
id = 440511,
name = "【每日】登录游戏",
desc = "【每日】登录游戏",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 1
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
20
},
expireTimestamps = {
{
seconds = 1757087999
}
}
},
taskGroupId = 44093
},
[440512] = {
id = 440512,
name = "【每日】游玩1次任意模式",
desc = "【每日】游玩1次任意模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
20
},
expireTimestamps = {
{
seconds = 1757087999
}
}
},
jumpId = 4,
taskGroupId = 44093
},
[440513] = {
id = 440513,
name = "【每日】完成1场谁是狼人对局",
desc = "【每日】完成1场谁是狼人对局",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
107,
108,
109,
110,
111,
151,
112,
113
}
}
}
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
10
},
expireTimestamps = {
{
seconds = 1757087999
}
}
},
jumpId = 310,
taskGroupId = 44093
},
[440514] = {
id = 440514,
name = "【每日】在星宝农场中收获5次农作物",
desc = "【每日】在星宝农场中收获5次农作物",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 157,
value = 5,
subConditionList = {
{
type = 159,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
10
},
expireTimestamps = {
{
seconds = 1757087999
}
}
},
jumpId = 5100,
taskGroupId = 44093
},
[440515] = {
id = 440515,
name = "【累计】登录游戏3天",
desc = "【累计】登录游戏3天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 3
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
30
},
expireTimestamps = {
{
seconds = 1757087999
}
}
}
},
[440516] = {
id = 440516,
name = "【累计】登录游戏7天",
desc = "【累计】登录游戏7天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 7
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
70
},
expireTimestamps = {
{
seconds = 1757087999
}
}
}
},
[440517] = {
id = 440517,
name = "【累计】赠送1个多余的角色徽章",
desc = "【累计】赠送1个多余的角色徽章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
20001
}
}
}
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
10
},
expireTimestamps = {
{
seconds = 1757087999
}
}
}
},
[440518] = {
id = 440518,
name = "【累计】赠送7个多余的角色徽章",
desc = "【累计】赠送7个多余的角色徽章",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 7,
subConditionList = {
{
type = 67,
value = {
20001
}
}
}
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
20
},
expireTimestamps = {
{
seconds = 1757087999
}
}
}
},
[440519] = {
id = 440519,
name = "【累计】使用名侦探获得3场谁是狼人胜利",
desc = "【累计】使用名侦探获得3场谁是狼人胜利",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 3,
subConditionList = {
{
type = 2,
value = {
105,
106,
107,
108,
109,
110,
111,
151,
112,
113
}
},
{
type = 50,
value = {
45,
1,
56
}
},
{
type = 50,
value = {
13,
1,
0
}
}
}
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
20
},
expireTimestamps = {
{
seconds = 1757087999
}
}
},
jumpId = 310
},
[440520] = {
id = 440520,
name = "【累计】在星宝农场中祈福20次",
desc = "【累计】在星宝农场中祈福20次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 188,
value = 20
}
}
}
},
reward = {
itemIdList = {
3431
},
numList = {
20
},
expireTimestamps = {
{
seconds = 1757087999
}
}
},
jumpId = 5100
},
[440904] = {
id = 440904,
name = "【每周】完成20次闪电赛",
desc = "【每周】完成20次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 15,
subConditionList = {
{
type = 2,
value = {
15,
16,
17,
21,
22,
23
}
}
}
}
}
}
},
reward = {
itemIdList = {
3561
},
numList = {
5
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1043,
order = 96,
taskGroupId = 44091
},
[440905] = {
id = 440905,
name = "【每周】完成30次闪电赛",
desc = "【每周】完成30次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 15,
subConditionList = {
{
type = 2,
value = {
15,
16,
17,
21,
22,
23
}
}
}
}
}
}
},
reward = {
itemIdList = {
3561
},
numList = {
5
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
jumpId = 1043,
order = 95,
taskGroupId = 44091
},
[440906] = {
id = 440906,
name = "完成5次投票",
desc = "完成5次投票",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 325,
value = 5,
subConditionList = {
{
type = 146,
value = {
5081
}
}
}
}
}
}
},
reward = {
itemIdList = {
200014
},
numList = {
1
}
},
order = 94,
taskGroupId = 44092
},
[440907] = {
id = 440907,
name = "完成8次投票",
desc = "完成8次投票",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 325,
value = 8,
subConditionList = {
{
type = 146,
value = {
5081
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
}
},
order = 93,
taskGroupId = 44092
},
[440908] = {
id = 440908,
name = "完成12次投票",
desc = "完成12次投票",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 325,
value = 12,
subConditionList = {
{
type = 146,
value = {
5081
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
15
}
},
order = 92,
taskGroupId = 44092
},
[440909] = {
id = 440909,
name = "完成18次投票",
desc = "完成18次投票",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 325,
value = 18,
subConditionList = {
{
type = 146,
value = {
5081
}
}
}
}
}
}
},
reward = {
itemIdList = {
850661
},
numList = {
1
}
},
order = 91,
taskGroupId = 44092
},
[440910] = {
id = 440910,
name = "(每日)游玩任意娱乐模式排位赛1次",
desc = "(每日)游玩任意娱乐模式排位赛1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
708,
709,
710,
701,
703,
354,
352,
151,
141,
607,
608,
609,
131,
18,
19,
20,
5601,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
200
}
},
jumpId = 4,
order = 1020,
taskGroupId = 46001
},
[440911] = {
id = 440911,
name = "突围梦幻岛达到黄金段位",
desc = "突围梦幻岛达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90010
}
}
}
}
}
}
},
reward = {
itemIdList = {
860028,
840076
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1027,
order = 118,
taskGroupId = 46002
},
[440912] = {
id = 440912,
name = "突围梦幻岛达到钻石段位",
desc = "突围梦幻岛达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
9
}
},
{
type = 154,
value = {
90010
}
}
}
}
}
}
},
reward = {
itemIdList = {
820056
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1027,
order = 117,
taskGroupId = 46002
},
[440913] = {
id = 440913,
name = "武器大师达到黄金段位",
desc = "武器大师达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60010
}
}
}
}
}
}
},
reward = {
itemIdList = {
860029,
840077
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1026,
order = 116,
taskGroupId = 46003
},
[440914] = {
id = 440914,
name = "武器大师达到钻石段位",
desc = "武器大师达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
6
}
},
{
type = 154,
value = {
60010
}
}
}
}
}
}
},
reward = {
itemIdList = {
820057
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1026,
order = 115,
taskGroupId = 46003
},
[440915] = {
id = 440915,
name = "大王别抓我星宝-达到黄金段位",
desc = "大王别抓我星宝-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70012
}
}
}
}
}
}
},
reward = {
itemIdList = {
860027,
840075
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1022,
order = 114,
taskGroupId = 46004
},
[440916] = {
id = 440916,
name = "大王别抓我星宝-达到钻石段位",
desc = "大王别抓我星宝-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
7
}
},
{
type = 154,
value = {
70012
}
}
}
}
}
}
},
reward = {
itemIdList = {
820055
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1022,
order = 113,
taskGroupId = 46004
},
[440917] = {
id = 440917,
name = "大王别抓我暗星-达到黄金段位",
desc = "大王别抓我暗星-达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80012
}
}
}
}
}
}
},
reward = {
itemIdList = {
860030,
840078
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1022,
order = 112,
taskGroupId = 46004
},
[440918] = {
id = 440918,
name = "大王别抓我暗星-达到钻石段位",
desc = "大王别抓我暗星-达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
8
}
},
{
type = 154,
value = {
80012
}
}
}
}
}
}
},
reward = {
itemIdList = {
820058
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1022,
order = 111,
taskGroupId = 46004
},
[440919] = {
id = 440919,
name = "谁是狼人达到黄金段位",
desc = "谁是狼人达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20011
}
}
}
}
}
}
},
reward = {
itemIdList = {
860023,
840071
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1024,
order = 106,
taskGroupId = 46005
},
[440920] = {
id = 440920,
name = "谁是狼人达到钻石段位",
desc = "谁是狼人达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
2
}
},
{
type = 154,
value = {
20011
}
}
}
}
}
}
},
reward = {
itemIdList = {
820051
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1024,
order = 105,
taskGroupId = 46005
},
[440921] = {
id = 440921,
name = "躲猫猫达到黄金段位",
desc = "躲猫猫达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30011
}
}
}
}
}
}
},
reward = {
itemIdList = {
860024,
840072
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1023,
order = 108,
taskGroupId = 46006
},
[440922] = {
id = 440922,
name = "躲猫猫达到钻石段位",
desc = "躲猫猫达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
3
}
},
{
type = 154,
value = {
30011
}
}
}
}
}
}
},
reward = {
itemIdList = {
820052
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1023,
order = 107,
taskGroupId = 46006
},
[440923] = {
id = 440923,
name = "极速飞车达到黄金段位",
desc = "极速飞车达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50011
}
}
}
}
}
}
},
reward = {
itemIdList = {
860025,
840073
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1025,
order = 110,
taskGroupId = 46007
},
[440924] = {
id = 440924,
name = "极速飞车达到钻石段位",
desc = "极速飞车达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
5
}
},
{
type = 154,
value = {
50011
}
}
}
}
}
}
},
reward = {
itemIdList = {
820053
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1025,
order = 109,
taskGroupId = 46007
},
[440925] = {
id = 440925,
name = "卧底行动达到黄金段位",
desc = "卧底行动达到黄金段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110011
}
}
}
}
}
}
},
reward = {
itemIdList = {
860061,
840123
},
numList = {
1,
1
},
expireTimestamps = {
{
seconds = 1760630399
},
{
seconds = 1760630399
}
}
},
jumpId = 1039,
order = 120,
taskGroupId = 46008
},
[440926] = {
id = 440926,
name = "卧底行动达到钻石段位",
desc = "卧底行动达到钻石段位",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110011
}
}
}
}
}
}
},
reward = {
itemIdList = {
820084
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1760630399
}
}
},
jumpId = 1039,
order = 119,
taskGroupId = 46008
},
[440927] = {
id = 440927,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46009
},
[440928] = {
id = 440928,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
jumpId = 726,
taskGroupId = 46009
},
[440929] = {
id = 440929,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
jumpId = 726,
taskGroupId = 46009
},
[440930] = {
id = 440930,
name = "UGC-星世界探索",
desc = "进入1次广场的星世界地图",
jumpId = 726,
taskGroupId = 46009
},
[440932] = {
id = 440932,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 1,
subConditionList = {
{
type = 4,
value = {
440927,
440928,
440929,
440930
}
}
}
}
}
}
},
reward = {
itemIdList = {
3641
},
numList = {
80
}
},
jumpId = 726,
taskGroupId = 46010
},
[440933] = {
id = 440933,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 2,
subConditionList = {
{
type = 4,
value = {
440927,
440928,
440929,
440930
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46010
},
[440934] = {
id = 440934,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 3,
subConditionList = {
{
type = 4,
value = {
440927,
440928,
440929,
440930
}
}
}
}
}
}
},
reward = {
itemIdList = {
332006
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46010
},
[440935] = {
id = 440935,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 4,
subConditionList = {
{
type = 4,
value = {
440927,
440928,
440929,
440930
}
}
}
}
}
}
},
reward = {
itemIdList = {
630589
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46010
},
[440936] = {
id = 440936,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46011
},
[440937] = {
id = 440937,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
jumpId = 726,
taskGroupId = 46011
},
[440938] = {
id = 440938,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
jumpId = 726,
taskGroupId = 46011
},
[440939] = {
id = 440939,
name = "娱乐-欢乐小游戏",
desc = "体验任意1局欢乐小游戏",
jumpId = 726,
taskGroupId = 46011
},
[440941] = {
id = 440941,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 1,
subConditionList = {
{
type = 4,
value = {
440936,
440937,
440938,
440939
}
}
}
}
}
}
},
reward = {
itemIdList = {
3641
},
numList = {
80
}
},
jumpId = 726,
taskGroupId = 46012
},
[440942] = {
id = 440942,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 2,
subConditionList = {
{
type = 4,
value = {
440936,
440937,
440938,
440939
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46012
},
[440943] = {
id = 440943,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 3,
subConditionList = {
{
type = 4,
value = {
440936,
440937,
440938,
440939
}
}
}
}
}
}
},
reward = {
itemIdList = {
332006
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46012
},
[440944] = {
id = 440944,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 4,
subConditionList = {
{
type = 4,
value = {
440936,
440937,
440938,
440939
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46012
},
[440945] = {
id = 440945,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46013
},
[440946] = {
id = 440946,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
jumpId = 726,
taskGroupId = 46013
},
[440947] = {
id = 440947,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
jumpId = 726,
taskGroupId = 46013
},
[440948] = {
id = 440948,
name = "UGC-星世界探索",
desc = "进入1次广场的星世界地图",
jumpId = 726,
taskGroupId = 46013
},
[440950] = {
id = 440950,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 1,
subConditionList = {
{
type = 4,
value = {
440945,
440946,
440947,
440948
}
}
}
}
}
}
},
reward = {
itemIdList = {
3641
},
numList = {
80
}
},
jumpId = 726,
taskGroupId = 46014
},
[440951] = {
id = 440951,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 2,
subConditionList = {
{
type = 4,
value = {
440945,
440946,
440947,
440948
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46014
},
[440952] = {
id = 440952,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 3,
subConditionList = {
{
type = 4,
value = {
440945,
440946,
440947,
440948
}
}
}
}
}
}
},
reward = {
itemIdList = {
332006
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46014
},
[440953] = {
id = 440953,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 4,
subConditionList = {
{
type = 4,
value = {
440945,
440946,
440947,
440948
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46014
},
[440954] = {
id = 440954,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46015
},
[440955] = {
id = 440955,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
jumpId = 726,
taskGroupId = 46015
},
[440956] = {
id = 440956,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
jumpId = 726,
taskGroupId = 46015
},
[440957] = {
id = 440957,
name = "娱乐-欢乐小游戏",
desc = "体验任意1局欢乐小游戏",
jumpId = 726,
taskGroupId = 46015
},
[440959] = {
id = 440959,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 1,
subConditionList = {
{
type = 4,
value = {
440954,
440955,
440956,
440957
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
500
}
},
jumpId = 726,
taskGroupId = 46016
},
[440960] = {
id = 440960,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 2,
subConditionList = {
{
type = 4,
value = {
440954,
440955,
440956,
440957
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46016
},
[440961] = {
id = 440961,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 3,
subConditionList = {
{
type = 4,
value = {
440954,
440955,
440956,
440957
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46016
},
[440962] = {
id = 440962,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 4,
subConditionList = {
{
type = 4,
value = {
440954,
440955,
440956,
440957
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46016
},
[440963] = {
id = 440963,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46017
},
[440964] = {
id = 440964,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
jumpId = 726,
taskGroupId = 46017
},
[440965] = {
id = 440965,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
jumpId = 726,
taskGroupId = 46017
},
[440966] = {
id = 440966,
name = "UGC-星世界探索",
desc = "进入1次广场的星世界地图",
jumpId = 726,
taskGroupId = 46017
},
[440968] = {
id = 440968,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 1,
subConditionList = {
{
type = 4,
value = {
440963,
440964,
440965,
440966
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
500
}
},
jumpId = 726,
taskGroupId = 46018
},
[440969] = {
id = 440969,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 2,
subConditionList = {
{
type = 4,
value = {
440963,
440964,
440965,
440966
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46018
},
[440970] = {
id = 440970,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 3,
subConditionList = {
{
type = 4,
value = {
440963,
440964,
440965,
440966
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46018
},
[440971] = {
id = 440971,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 4,
subConditionList = {
{
type = 4,
value = {
440963,
440964,
440965,
440966
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46018
},
[440972] = {
id = 440972,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46019
},
[440973] = {
id = 440973,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
jumpId = 726,
taskGroupId = 46019
},
[440974] = {
id = 440974,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
jumpId = 726,
taskGroupId = 46019
},
[440975] = {
id = 440975,
name = "娱乐-欢乐小游戏",
desc = "体验任意1局欢乐小游戏",
jumpId = 726,
taskGroupId = 46019
},
[440977] = {
id = 440977,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 1,
subConditionList = {
{
type = 4,
value = {
440972,
440973,
440974,
440975
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
500
}
},
jumpId = 726,
taskGroupId = 46020
},
[440978] = {
id = 440978,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 2,
subConditionList = {
{
type = 4,
value = {
440972,
440973,
440974,
440975
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46020
},
[440979] = {
id = 440979,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 3,
subConditionList = {
{
type = 4,
value = {
440972,
440973,
440974,
440975
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46020
},
[440980] = {
id = 440980,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 4,
subConditionList = {
{
type = 4,
value = {
440972,
440973,
440974,
440975
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46020
},
[440981] = {
id = 440981,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46021
},
[440982] = {
id = 440982,
name = "答题-星宝小课堂",
desc = "本周累计在自习室答对20道题",
jumpId = 726,
taskGroupId = 46021
},
[440983] = {
id = 440983,
name = "快闪-快闪小明星",
desc = "抱1次穿着chikawa皮肤的星宝",
jumpId = 726,
taskGroupId = 46021
},
[440984] = {
id = 440984,
name = "UGC-星世界探索",
desc = "进入1次广场的星世界地图",
jumpId = 726,
taskGroupId = 46021
},
[440986] = {
id = 440986,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 1,
subConditionList = {
{
type = 4,
value = {
440981,
440982,
440983,
440984
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
500
}
},
jumpId = 726,
taskGroupId = 46022
},
[440987] = {
id = 440987,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 2,
subConditionList = {
{
type = 4,
value = {
440981,
440982,
440983,
440984
}
}
}
}
}
}
},
reward = {
itemIdList = {
290024
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46022
},
[440988] = {
id = 440988,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 3,
subConditionList = {
{
type = 4,
value = {
440981,
440982,
440983,
440984
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46022
},
[440989] = {
id = 440989,
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 7,
value = 4,
subConditionList = {
{
type = 4,
value = {
440981,
440982,
440983,
440984
}
}
}
}
}
}
},
reward = {
itemIdList = {
320162
},
numList = {
1
}
},
jumpId = 726,
taskGroupId = 46022
},
[440990] = {
id = 440990,
name = "打卡-星愿小相馆",
desc = "前往chikawa舞台，和他合影吧~",
jumpId = 726,
taskGroupId = 46023
}
}

local mt = {
name = "奖励",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 152,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 145,
value = {
11
}
},
{
type = 154,
value = {
110010
}
}
}
}
}
}
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data