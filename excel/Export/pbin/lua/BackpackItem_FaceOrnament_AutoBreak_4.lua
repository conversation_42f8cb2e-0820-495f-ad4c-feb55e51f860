--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 面饰

local v0 = {
-10,
25
}

local data = {
[610400] = {
id = 610400,
effect = true,
name = "无忧眼镜",
desc = "没事哒，没事哒，一切问题消失啦",
icon = "CDN:Icon_Mask_199",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_199",
modelType = 1
},
shareTexts = {
"没有什么大不了，放心叭！"
},
beginTime = {
seconds = 4074768000
},
suitId = 50203,
suitName = "无忧眼镜",
suitIcon = "CDN:Icon_Mask_199",
shareOffset = v0
},
[610401] = {
id = 610401,
effect = true,
name = "无忧眼镜",
desc = "没事哒，没事哒，一切问题消失啦",
icon = "CDN:Icon_Mask_199_01",
outlookConf = {
belongTo = 610400,
fashionValue = 25,
belongToGroup = {
610401,
610402
}
},
resourceConf = {
model = "SM_Mask_199",
material = "MI_Mask_199_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51422,
shareTexts = {
"没有什么大不了，放心叭！"
},
shareOffset = v0
},
[610402] = {
id = 610402,
effect = true,
name = "无忧眼镜",
desc = "没事哒，没事哒，一切问题消失啦",
icon = "CDN:Icon_Mask_199_02",
outlookConf = {
belongTo = 610400,
fashionValue = 25,
belongToGroup = {
610401,
610402
}
},
resourceConf = {
model = "SM_Mask_199",
material = "MI_Mask_199_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51423,
shareTexts = {
"没有什么大不了，放心叭！"
},
shareOffset = v0
},
[610403] = {
id = 610403,
effect = true,
name = "松树之境",
desc = "想要看得高，就要站在树顶上！",
icon = "CDN:Icon_Mask_198",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_198",
modelType = 1
},
shareTexts = {
"一起眺望整个世界~"
},
beginTime = {
seconds = 4074768000
},
suitId = 50204,
suitName = "松树之境",
suitIcon = "CDN:Icon_Mask_198",
shareOffset = v0
},
[610404] = {
id = 610404,
effect = true,
name = "松树之境",
desc = "想要看得高，就要站在树顶上！",
icon = "CDN:Icon_Mask_198_01",
outlookConf = {
belongTo = 610403,
fashionValue = 25,
belongToGroup = {
610404,
610405
}
},
resourceConf = {
model = "SM_Mask_198",
material = "MI_Mask_198_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51425,
shareTexts = {
"一起眺望整个世界~"
},
shareOffset = v0
},
[610405] = {
id = 610405,
effect = true,
name = "松树之境",
desc = "想要看得高，就要站在树顶上！",
icon = "CDN:Icon_Mask_198_02",
outlookConf = {
belongTo = 610403,
fashionValue = 25,
belongToGroup = {
610404,
610405
}
},
resourceConf = {
model = "SM_Mask_198",
material = "MI_Mask_198_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51426,
shareTexts = {
"一起眺望整个世界~"
},
shareOffset = v0
},
[610406] = {
id = 610406,
effect = true,
name = "咕咕面具",
desc = "不服就来啄一下！",
icon = "CDN:Icon_Mask_203",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_203",
modelType = 1
},
shareTexts = {
"别害怕，我不啄你"
},
beginTime = {
seconds = 4074768000
},
suitId = 50205,
suitName = "咕咕面具",
suitIcon = "CDN:Icon_Mask_203",
shareOffset = v0
},
[610407] = {
id = 610407,
effect = true,
name = "咕咕面具",
desc = "不服就来啄一下！",
icon = "CDN:Icon_Mask_203_01",
outlookConf = {
belongTo = 610406,
fashionValue = 25,
belongToGroup = {
610407,
610408
}
},
resourceConf = {
model = "SM_Mask_203",
material = "MI_Mask_203_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51428,
shareTexts = {
"别害怕，我不啄你"
},
shareOffset = v0
},
[610408] = {
id = 610408,
effect = true,
name = "咕咕面具",
desc = "不服就来啄一下！",
icon = "CDN:Icon_Mask_203_02",
outlookConf = {
belongTo = 610406,
fashionValue = 25,
belongToGroup = {
610407,
610408
}
},
resourceConf = {
model = "SM_Mask_203",
material = "MI_Mask_203_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51429,
shareTexts = {
"别害怕，我不啄你"
},
shareOffset = v0
},
[610409] = {
id = 610409,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "惩恶符",
desc = "急急如律令，坏人退散！",
icon = "CDN:Icon_Mask_206",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_206",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 4074768000
},
suitId = 50206,
suitName = "惩恶符",
suitIcon = "CDN:Icon_Mask_206"
},
[610410] = {
id = 610410,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "飞雪衔珠",
desc = "白泽踏雪，玉桂衔珠",
icon = "CDN:Icon_Mask_215",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_215",
modelType = 2,
idleAnim = "AS_Mask_215_idle_001"
},
shareTexts = {
"比起珍宝，你更重要"
},
beginTime = {
seconds = 4074768000
},
suitId = 50207,
suitName = "飞雪衔珠",
suitIcon = "CDN:Icon_Mask_215"
},
[610411] = {
id = 610411,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "飞雪衔珠",
desc = "白泽踏雪，玉桂衔珠",
icon = "CDN:Icon_Mask_215_01",
outlookConf = {
belongTo = 610410,
fashionValue = 35,
belongToGroup = {
610411,
610412
}
},
resourceConf = {
model = "SK_Mask_215",
material = "MI_Mask_215_HP01",
modelType = 2,
idleAnim = "AS_Mask_215_idle_001_HP01",
materialSlot = "Mask"
},
commodityId = 51432,
shareTexts = {
"比起珍宝，你更重要"
},
shareOffset = v0
},
[610412] = {
id = 610412,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "飞雪衔珠",
desc = "白泽踏雪，玉桂衔珠",
icon = "CDN:Icon_Mask_215_02",
outlookConf = {
belongTo = 610410,
fashionValue = 35,
belongToGroup = {
610411,
610412
}
},
resourceConf = {
model = "SK_Mask_215",
material = "MI_Mask_215_HP02",
modelType = 2,
idleAnim = "AS_Mask_215_idle_001_HP02",
materialSlot = "Mask"
},
commodityId = 51433,
shareTexts = {
"比起珍宝，你更重要"
},
shareOffset = v0
},
[610413] = {
id = 610413,
effect = true,
name = "竹镜节节",
desc = "翠翠绿绿，护眼满分！",
icon = "CDN:Icon_Mask_219",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_219",
modelType = 1
},
shareTexts = {
"坚强小竹守护你~"
},
beginTime = {
seconds = 4074768000
},
suitId = 50208,
suitName = "竹镜节节",
suitIcon = "CDN:Icon_Mask_219",
shareOffset = v0
},
[610414] = {
id = 610414,
effect = true,
name = "竹镜节节",
desc = "翠翠绿绿，护眼满分！",
icon = "CDN:Icon_Mask_219_01",
outlookConf = {
belongTo = 610413,
fashionValue = 25,
belongToGroup = {
610414,
610415
}
},
resourceConf = {
model = "SM_Mask_219",
material = "MI_Mask_219_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51435,
shareTexts = {
"坚强小竹守护你~"
},
shareOffset = v0
},
[610415] = {
id = 610415,
effect = true,
name = "竹镜节节",
desc = "翠翠绿绿，护眼满分！",
icon = "CDN:Icon_Mask_219_02",
outlookConf = {
belongTo = 610413,
fashionValue = 25,
belongToGroup = {
610414,
610415
}
},
resourceConf = {
model = "SM_Mask_219",
material = "MI_Mask_219_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51436,
shareTexts = {
"坚强小竹守护你~"
},
shareOffset = v0
},
[610416] = {
id = 610416,
effect = true,
name = "微糖视界",
desc = "生活就像小糖豆，甜甜蜜蜜",
icon = "CDN:Icon_Mask_212",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_212",
modelType = 1
},
shareTexts = {
"戴上我，永远开心吧！"
},
beginTime = {
seconds = 4074768000
},
suitId = 50209,
suitName = "微糖视界",
suitIcon = "CDN:Icon_Mask_212",
shareOffset = v0
},
[610417] = {
id = 610417,
effect = true,
name = "微糖视界",
desc = "生活就像小糖豆，甜甜蜜蜜",
icon = "CDN:Icon_Mask_212_01",
outlookConf = {
belongTo = 610416,
fashionValue = 25,
belongToGroup = {
610417,
610418
}
},
resourceConf = {
model = "SM_Mask_212",
material = "MI_Mask_212_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51438,
shareTexts = {
"戴上我，永远开心吧！"
},
shareOffset = v0
},
[610418] = {
id = 610418,
effect = true,
name = "微糖视界",
desc = "生活就像小糖豆，甜甜蜜蜜",
icon = "CDN:Icon_Mask_212_02",
outlookConf = {
belongTo = 610416,
fashionValue = 25,
belongToGroup = {
610417,
610418
}
},
resourceConf = {
model = "SM_Mask_212",
material = "MI_Mask_212_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51439,
shareTexts = {
"戴上我，永远开心吧！"
},
shareOffset = v0
},
[610419] = {
id = 610419,
effect = true,
name = "甜力之眼",
desc = "捂住一只眼，萌力多一点！",
icon = "CDN:Icon_Mask_213",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_213",
modelType = 1
},
shareTexts = {
"可爱与实用，我都要！"
},
beginTime = {
seconds = 4074768000
},
suitId = 50210,
suitName = "甜力之眼",
suitIcon = "CDN:Icon_Mask_213",
shareOffset = v0
},
[610420] = {
id = 610420,
effect = true,
name = "甜力之眼",
desc = "捂住一只眼，萌力多一点！",
icon = "CDN:Icon_Mask_213_01",
outlookConf = {
belongTo = 610419,
fashionValue = 25,
belongToGroup = {
610420,
610421
}
},
resourceConf = {
model = "SM_Mask_213",
material = "MI_Mask_213_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51441,
shareTexts = {
"可爱与实用，我都要！"
},
shareOffset = v0
},
[610421] = {
id = 610421,
effect = true,
name = "甜力之眼",
desc = "捂住一只眼，萌力多一点！",
icon = "CDN:Icon_Mask_213_02",
outlookConf = {
belongTo = 610419,
fashionValue = 25,
belongToGroup = {
610420,
610421
}
},
resourceConf = {
model = "SM_Mask_213",
material = "MI_Mask_213_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51442,
shareTexts = {
"可爱与实用，我都要！"
},
shareOffset = v0
},
[610422] = {
id = 610422,
effect = true,
name = "心动防护罩",
desc = "前方潮流超标，请佩戴面罩",
icon = "CDN:Icon_Mask_220",
outlookConf = {
fashionValue = 125
},
resourceConf = {
model = "SM_Mask_220",
modelType = 1
},
shareTexts = {
"遮住脸，还会注意我吗？"
},
beginTime = {
seconds = 4074768000
},
suitId = 50211,
suitName = "心动防护罩",
suitIcon = "CDN:Icon_Mask_220",
shareOffset = v0
},
[610423] = {
id = 610423,
effect = true,
name = "心动防护罩",
desc = "前方潮流超标，请佩戴面罩",
icon = "CDN:Icon_Mask_220_01",
outlookConf = {
belongTo = 610422,
fashionValue = 25,
belongToGroup = {
610423,
610424
}
},
resourceConf = {
model = "SM_Mask_220",
material = "MI_Mask_220_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51444,
shareTexts = {
"遮住脸，还会注意我吗？"
},
shareOffset = v0
},
[610424] = {
id = 610424,
effect = true,
name = "心动防护罩",
desc = "前方潮流超标，请佩戴面罩",
icon = "CDN:Icon_Mask_220_02",
outlookConf = {
belongTo = 610422,
fashionValue = 25,
belongToGroup = {
610423,
610424
}
},
resourceConf = {
model = "SM_Mask_220",
material = "MI_Mask_220_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51445,
shareTexts = {
"遮住脸，还会注意我吗？"
},
shareOffset = v0
},
[610425] = {
id = 610425,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "恶魔创口贴",
desc = "桀桀桀，这就是恶魔的帅气！",
icon = "CDN:Icon_Mask_223",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_223",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 4074768000
},
suitId = 50212,
suitName = "恶魔创口贴",
suitIcon = "CDN:Icon_Mask_223"
},
[610426] = {
id = 610426,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "波频眼镜",
desc = "请注意，世界正在变色……",
icon = "CDN:Icon_Mask_217",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_217",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 4074768000
},
suitId = 50213,
suitName = "波频眼镜",
suitIcon = "CDN:Icon_Mask_217"
},
[610427] = {
id = 610427,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "彩波眼镜",
desc = "接收与众不同的彩色信号",
icon = "CDN:Icon_Mask_218",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_218",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 4074768000
},
suitId = 50214,
suitName = "彩波眼镜",
suitIcon = "CDN:Icon_Mask_218"
},
[610428] = {
id = 610428,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "学究小镜",
desc = "学海无涯，还需仔细钻研……",
icon = "CDN:Icon_Mask_214",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_214",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 4074768000
},
suitId = 50215,
suitName = "学究小镜",
suitIcon = "CDN:Icon_Mask_214"
},
[610429] = {
id = 610429,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "侠客眼镜",
desc = "黑夜之中，侠客无形",
icon = "CDN:Icon_Mask_221",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_221",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 4074768000
},
suitId = 50216,
suitName = "侠客眼镜",
suitIcon = "CDN:Icon_Mask_221"
},
[610430] = {
id = 610430,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "多巴胺镜",
desc = "来自【星衣妙想】创作者北沐辰的创意，戴上它，快乐立刻看得见！",
icon = "CDN:Icon_Mask_226",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_226",
modelType = 1
},
scaleTimes = 260,
shareTexts = {
"你的快乐有颜色~"
},
beginTime = {
seconds = 4074768000
},
suitId = 50217,
suitName = "多巴胺镜",
suitIcon = "CDN:Icon_Mask_226"
},
[610431] = {
id = 610431,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星漩吹梦",
desc = "每一次呼吸都绽放星光，撒落漫天童话",
icon = "CDN:Icon_Mask_216",
outlookConf = {
fashionValue = 300
},
resourceConf = {
model = "SK_Mask_216",
modelType = 2,
idleAnim = "AS_Mask_216_idle_001"
},
shareTexts = {
"把浪漫，吹进你的掌心"
},
beginTime = {
seconds = 4074768000
},
suitId = 50218,
suitName = "星漩吹梦",
suitIcon = "CDN:Icon_Mask_216",
shareOffset = v0
},
[610432] = {
id = 610432,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星漩吹梦",
desc = "每一次呼吸都绽放星光，撒落漫天童话",
icon = "CDN:Icon_Mask_216_01",
outlookConf = {
belongTo = 610431,
fashionValue = 35,
belongToGroup = {
610432,
610433
}
},
resourceConf = {
model = "SK_Mask_216",
material = "MI_Mask_216_HP01",
modelType = 2,
idleAnim = "AS_Mask_216_idle_001_HP01",
materialSlot = "Mask"
},
commodityId = 51453,
shareTexts = {
"把浪漫，吹进你的掌心"
},
shareOffset = v0
},
[610433] = {
id = 610433,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 320
}
},
quality = 1,
name = "星漩吹梦",
desc = "每一次呼吸都绽放星光，撒落漫天童话",
icon = "CDN:Icon_Mask_216_02",
outlookConf = {
belongTo = 610431,
fashionValue = 35,
belongToGroup = {
610432,
610433
}
},
resourceConf = {
model = "SK_Mask_216",
material = "MI_Mask_216_HP02",
modelType = 2,
idleAnim = "AS_Mask_216_idle_001_HP02",
materialSlot = "Mask"
},
commodityId = 51454,
shareTexts = {
"把浪漫，吹进你的掌心"
},
shareOffset = v0
},
[610434] = {
id = 610434,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 50
}
},
quality = 3,
name = "狼人之噬",
desc = "隐于黑暗中，吞噬一切",
icon = "CDN:Icon_Mask_229",
outlookConf = {
fashionValue = 60
},
resourceConf = {
model = "SM_Mask_229",
modelType = 1
},
scaleTimes = 260,
beginTime = {
seconds = 4074768000
},
suitId = 50219,
suitName = "狼人之噬",
suitIcon = "CDN:Icon_Mask_229"
},
[610435] = {
id = 610435,
effect = true,
name = "酷彩视界",
desc = "所有霓虹灯，都在眼底闪烁",
icon = "CDN:Icon_Mask_134_01",
outlookConf = {
belongTo = 610239,
fashionValue = 25,
belongToGroup = {
610435,
610436
}
},
resourceConf = {
model = "SM_Mask_134",
material = "MI_Mask_134_HP01",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51456,
shareTexts = {
"色彩大胆，搭配无界"
},
shareOffset = {
0,
0
}
},
[610436] = {
id = 610436,
effect = true,
name = "酷彩视界",
desc = "所有霓虹灯，都在眼底闪烁",
icon = "CDN:Icon_Mask_134_02",
outlookConf = {
belongTo = 610239,
fashionValue = 25,
belongToGroup = {
610435,
610436
}
},
resourceConf = {
model = "SM_Mask_134",
material = "MI_Mask_134_HP02",
modelType = 1,
materialSlot = "Mask"
},
commodityId = 51457,
shareTexts = {
"色彩大胆，搭配无界"
},
shareOffset = {
0,
0
}
}
}

local mt = {
effect = false,
type = "ItemType_FaceOrnament",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 2,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
scaleTimes = 200,
previewShareOffset = {
0,
0
}
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data