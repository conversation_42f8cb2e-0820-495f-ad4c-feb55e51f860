local table_TextEntryData_IndexTable = {}
table_TextEntryData_IndexTable.TableName = {
    "TextEntryData_AC",
    "TextEntryData_Arena",
    "TextEntryData_Arena_AutoBreak_1",
    "TextEntryData_BS",
    "TextEntryData_COC",
    "TextEntryData_COC_AutoBreak_1",
    "TextEntryData_COC_AutoBreak_2",
    "TextEntryData_Card",
    "TextEntryData_Card_AutoBreak_1",
    "TextEntryData_Card_AutoBreak_2",
    "TextEntryData_Club",
    "TextEntryData_Club_AutoBreak_1",
    "TextEntryData_FB",
    "TextEntryData_FPS",
    "TextEntryData_FPS_AutoBreak_1",
    "TextEntryData_HOK",
    "TextEntryData_HOK_AutoBreak_1",
    "TextEntryData_Level",
    "TextEntryData_Level_AutoBreak_1",
    "TextEntryData_Level_AutoBreak_2",
    "TextEntryData_Main",
    "TextEntryData_Main_AutoBreak_1",
    "TextEntryData_Main_AutoBreak_2",
    "TextEntryData_Main_AutoBreak_3",
    "TextEntryData_Main_AutoBreak_4",
    "TextEntryData_Main_AutoBreak_5",
    "TextEntryData_Main_AutoBreak_6",
    "TextEntryData_Main_AutoBreak_7",
    "TextEntryData_Main_AutoBreak_8",
    "TextEntryData_Main_AutoBreak_9",
    "TextEntryData_Main_AutoBreak_10",
    "TextEntryData_Main_AutoBreak_11",
    "TextEntryData_Main_AutoBreak_12",
    "TextEntryData_Main_AutoBreak_13",
    "TextEntryData_Main_AutoBreak_14",
    "TextEntryData_Main_AutoBreak_15",
    "TextEntryData_Main_AutoBreak_16",
    "TextEntryData_Main_AutoBreak_17",
    "TextEntryData_Main_AutoBreak_18",
    "TextEntryData_Main_AutoBreak_19",
    "TextEntryData_Main_AutoBreak_20",
    "TextEntryData_Main_AutoBreak_21",
    "TextEntryData_Main_AutoBreak_22",
    "TextEntryData_Main_AutoBreak_23",
    "TextEntryData_Main_AutoBreak_24",
    "TextEntryData_Main_AutoBreak_25",
    "TextEntryData_Main_AutoBreak_26",
    "TextEntryData_Main_AutoBreak_27",
    "TextEntryData_Main_AutoBreak_28",
    "TextEntryData_Main_AutoBreak_29",
    "TextEntryData_Main_AutoBreak_30",
    "TextEntryData_Main_AutoBreak_31",
    "TextEntryData_Main_AutoBreak_32",
    "TextEntryData_Main_AutoBreak_33",
    "TextEntryData_Main_AutoBreak_34",
    "TextEntryData_Main_AutoBreak_35",
    "TextEntryData_Main_AutoBreak_36",
    "TextEntryData_Main_AutoBreak_37",
    "TextEntryData_Main_AutoBreak_38",
    "TextEntryData_Main_AutoBreak_39",
    "TextEntryData_Main_AutoBreak_40",
    "TextEntryData_Main_AutoBreak_41",
    "TextEntryData_Main_AutoBreak_42",
    "TextEntryData_Mayday",
    "TextEntryData_Mayday_AutoBreak_1",
    "TextEntryData_Mayday_AutoBreak_2",
    "TextEntryData_Mayday_AutoBreak_3",
    "TextEntryData_Mayday_AutoBreak_4",
    "TextEntryData_Mayday_AutoBreak_5",
    "TextEntryData_Mayday_AutoBreak_6",
    "TextEntryData_Mayday_AutoBreak_7",
    "TextEntryData_Mayday_AutoBreak_8",
    "TextEntryData_Mayday_AutoBreak_9",
    "TextEntryData_Mayday_AutoBreak_10",
    "TextEntryData_Mayday_AutoBreak_11",
    "TextEntryData_Mayday_AutoBreak_12",
    "TextEntryData_Mayday_AutoBreak_13",
    "TextEntryData_Preload",
    "TextEntryData_Preload_AutoBreak_1",
    "TextEntryData_SuitStory",
    "TextEntryData_System",
    "TextEntryData_System_AutoBreak_1",
    "TextEntryData_System_AutoBreak_2",
    "TextEntryData_System_AutoBreak_3",
    "TextEntryData_System_AutoBreak_4",
    "TextEntryData_System_AutoBreak_5",
    "TextEntryData_System_AutoBreak_6",
    "TextEntryData_System_AutoBreak_7",
    "TextEntryData_System_AutoBreak_8",
    "TextEntryData_System_AutoBreak_9",
    "TextEntryData_System_AutoBreak_10",
    "TextEntryData_System_AutoBreak_11",
    "TextEntryData_System_AutoBreak_12",
    "TextEntryData_System_AutoBreak_13",
    "TextEntryData_System_AutoBreak_14",
    "TextEntryData_System_AutoBreak_15",
    "TextEntryData_System_AutoBreak_16",
    "TextEntryData_System_AutoBreak_17",
    "TextEntryData_System_AutoBreak_18",
    "TextEntryData_TYC",
    "TextEntryData_TYC_AutoBreak_1",
    "TextEntryData_TYC_AutoBreak_2",
    "TextEntryData_TYC_AutoBreak_3",
    "TextEntryData_TYC_AutoBreak_4",
    "TextEntryData_TYC_AutoBreak_5",
    "TextEntryData_Travel",
    "TextEntryData_UGCProgramme",
    "TextEntryData_UGCRPG",
    "TextEntryData_chase",
    "TextEntryData_chase_AutoBreak_1",
    "TextEntryData_chase_AutoBreak_2",
    "TextEntryData_chase_AutoBreak_3",
    "TextEntryData_chase_AutoBreak_4",
    "TextEntryData_chase_AutoBreak_5",
    "TextEntryData_chase_AutoBreak_6",
    "TextEntryData_chase_AutoBreak_7",
    "TextEntryData_chase_AutoBreak_8",
    "TextEntryData_chase_AutoBreak_9",
    "TextEntryData_chase_AutoBreak_10"
}
table_TextEntryData_IndexTable.AutoBreakTables = {
    ["TextEntryData_Arena"] = 1,
    ["TextEntryData_COC"] = 2,
    ["TextEntryData_Card"] = 2,
    ["TextEntryData_Club"] = 1,
    ["TextEntryData_FPS"] = 1,
    ["TextEntryData_HOK"] = 1,
    ["TextEntryData_Level"] = 2,
    ["TextEntryData_Main"] = 42,
    ["TextEntryData_Mayday"] = 13,
    ["TextEntryData_Preload"] = 1,
    ["TextEntryData_System"] = 18,
    ["TextEntryData_TYC"] = 5,
    ["TextEntryData_chase"] = 10,
}
table_TextEntryData_IndexTable.Data = {
    ["DDB_InLevel_Tips_1"] = 1,
    ["DDB_InLevel_Counting_Title"] = 1,
    ["DDB_InLevel_ChasingTips"] = 1,
    ["DDB_InLevel_LeftMsgFmt_KO"] = 1,
    ["DDB_InLevel_LeftMsgFmt_SelfMistake"] = 1,
    ["DDB_InLevel_LeftMsgFmt_Winner"] = 1,
    ["DDB_InLevel_LeftMsgFmt_KO_Normal"] = 1,
    ["DDB_InLevel_PlayerOutBallTip"] = 1,
    ["DDB_InLevel_RoundStartBallTip"] = 1,
    ["DDB_InLevel_RespawnBallCommonTip"] = 1,
    ["DDB_Prop_ScreamingChicken_Name"] = 1,
    ["DDB_Prop_DuckKing_Name"] = 1,
    ["DDB_Prop_Poision_Name"] = 1,
    ["DDB_Prop_Shield_Name"] = 1,
    ["DDB_Prop_Backtrack_Name"] = 1,
    ["DDB_Prop_Rush_Name"] = 1,
    ["DDB_Prop_SpeedUpDDB_Name"] = 1,
    ["DDB_Prop_StrengthPotions_Name"] = 1,
    ["DDB_Prop_TomatoDDB_Name"] = 1,
    ["DDB_Prop_Freeze_Name"] = 1,
    ["DDB_Prop_Stealth_Name"] = 1,
    ["DDB_InLevel_Tips_FreezingBall"] = 1,
    ["DDB_InLevel_WinnerToast"] = 1,
    ["DDB_FinalAccount_TableTitle_1"] = 1,
    ["DDB_InLevel_Roud1"] = 1,
    ["DDB_InLevel_Roud2"] = 1,
    ["DDB_InLevel_Roud3"] = 1,
    ["DDB_InLevel_Roud4"] = 1,
    ["DDB_InLevel_Roud5"] = 1,
    ["DDB_InLevel_CenterMsgFmt_KO_Win"] = 1,
    ["DDB_InLevel_CenterMsgFmt_KO_Lose"] = 1,
    ["DDB_InLevel_CenterMsgFmt_SelfMistake"] = 1,
    ["DDB_InLevel_CenterMsgFmt_Winner"] = 1,
    ["DDB_GrowUp_Buff"] = 1,
    ["DDB_SpeedUp_Buff"] = 1,
    ["DDB_Rage_Buff"] = 1,
    ["DDB_Shield_Buff"] = 1,
    ["DDB_Stamina_Buff"] = 1,
    ["DDB_Choose_Rage"] = 1,
    ["DDB_Choose_Shield"] = 1,
    ["DDB_Choose_Freeze"] = 1,
    ["DDB_Choose_Tomato"] = 1,
    ["DDB_Choose_Backtracking"] = 1,
    ["Arena_RoundPrepare"] = 2,
    ["Arena_RoundBattle"] = 2,
    ["Arena_BattleDetail"] = 2,
    ["Arena_MyAttribute"] = 2,
    ["Arena_MySkill"] = 2,
    ["Arena_PoisonCircleRefreshTip"] = 2,
    ["Arena_PickRoleDeadline"] = 2,
    ["Arena_SkillCDContent"] = 2,
    ["Arena_ShowingPickCardTipStart"] = 2,
    ["Arena_ShowingPickCardTipDeadline"] = 2,
    ["Arena_ShowingPickCardTipReady"] = 2,
    ["Arena_ShowingPickCardTip"] = 2,
    ["Arena_ReadyPick"] = 2,
    ["Arena_Picked"] = 2,
    ["Arena_ShowingCardBroastTitle"] = 2,
    ["Arena_ShowingCardBroastContent"] = 2,
    ["Arena_FinalBroastTitle"] = 2,
    ["Arena_FinalBroastContent"] = 2,
    ["Arena_AllPickReadyToCombat"] = 2,
    ["Arena_Skill_Passive"] = 2,
    ["Arena_RoleSelectFailReason"] = 2,
    ["Arena_SkillCDSecond"] = 2,
    ["Arena_ShowingCardEliminated"] = 2,
    ["Arena_Intro_1"] = 2,
    ["Arena_Intro_1_Text"] = 2,
    ["Arena_Intro_2"] = 2,
    ["Arena_Intro_2_1"] = 2,
    ["Arena_Intro_2_1_Text"] = 2,
    ["Arena_Intro_2_2"] = 2,
    ["Arena_Intro_2_2_Text"] = 2,
    ["Arena_Intro_2_3"] = 2,
    ["Arena_Intro_2_3_Text"] = 2,
    ["Arena_Intro_3"] = 2,
    ["Arena_Intro_3_Text"] = 2,
    ["Arena_Intro_4"] = 2,
    ["Arena_Intro_4_Text"] = 2,
    ["Arena_Intro_5"] = 2,
    ["Arena_Intro_5_Text"] = 2,
    ["Arena_Intro_6"] = 2,
    ["Arena_Intro_6_1"] = 2,
    ["Arena_Intro_6_1_Text"] = 2,
    ["Arena_Intro_6_2"] = 2,
    ["Arena_Intro_6_2_Text"] = 2,
    ["Arena_Intro_7"] = 2,
    ["Arena_Intro_7_Text"] = 2,
    ["Arena_Honor_Limit"] = 2,
    ["Arena_Kill_Broadcast_Player_Revenge"] = 2,
    ["Arena_Kill_Broadcast_Team_Revenge"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_FirstBlood"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_DoubleKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_TripleKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_QuadraKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_PentaKill"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_GodLike"] = 2,
    ["Arena_Kill_Broadcast_Kill_Streak_Legendary"] = 2,
    ["Arena_Kill_Broadcast_Kill_Slay"] = 2,
    ["Arena_FinalAccount_Detail_Text"] = 2,
    ["Arena_FinalAccount_Detail_Round"] = 2,
    ["Arena_HostingByAI_Tips_1"] = 2,
    ["Arena_HostingByAI_Tips_2"] = 2,
    ["Arena_CoinIsNotEnough"] = 2,
    ["Arena_BuyHeroFail"] = 2,
    ["Arena_CardGroup_Changed_Save"] = 2,
    ["Arena_CardGroup_Changed_Save_GiveUp"] = 2,
    ["Arena_CardGroup_Changed_SaveMessage"] = 2,
    ["Arena_Stage_Prepare"] = 2,
    ["Arena_Stage_Combat"] = 2,
    ["Arena_ItemUseNoPakGroup"] = 2,
    ["Arena_Herochoice"] = 2,
    ["Arena_Rank_1"] = 2,
    ["Arena_Rank_2"] = 2,
    ["Arena_Rank_3"] = 2,
    ["Arena_Rank_4"] = 2,
    ["Arena_Discount"] = 2,
    ["Arena_HeroCardNotOpen"] = 2,
    ["Arena_Discount_OverTime"] = 2,
    ["Arena_Rank_Not_Unlock"] = 2,
    ["Arena_Prepararion_Banner_OverTime"] = 2,
    ["Arena_SkillName_1"] = 2,
    ["Arena_SkillName_2"] = 2,
    ["Arena_SkillName_3"] = 2,
    ["Arena_SendQuickMessage"] = 2,
    ["Arena_CanNotUseInTheMode"] = 2,
    ["Arena_CanNotUseInTheModeToast"] = 2,
    ["Arena_ChatHeroName"] = 2,
    ["Arnea_GigtAddFriend"] = 2,
    ["Arena_CannotCharge_InBattle"] = 2,
    ["HotZone_ZoneRefreshCountDown"] = 2,
    ["HotZone_ZoneMoveCountDown"] = 2,
    ["Arena_DownloadCanSee"] = 2,
    ["Arena_Train_Card_Tab_1_1"] = 2,
    ["Arena_Train_Card_Tab_1_2"] = 2,
    ["Arena_Train_Card_Tab_1_3"] = 2,
    ["Arena_Train_Card_Max_Tip"] = 2,
    ["Arena_Train_Card_Tab_2_9"] = 2,
    ["Arena_Train_Card_Tab_2_10"] = 2,
    ["Arena_Train_Card_Tab_2_11"] = 2,
    ["Arena_Train_Card_Tab_2_12"] = 2,
    ["Arena_Train_Card_Sort_1"] = 2,
    ["Arena_Train_Card_Sort_2"] = 2,
    ["Arena_Train_Card_Title_1"] = 3,
    ["Arena_Train_Card_Title_2"] = 3,
    ["Set_EditKey_Arena3v3"] = 3,
    ["Arena_PerRankDetails_Title1"] = 3,
    ["Arena_PerRankDetails_Title2"] = 3,
    ["Arena_PerRankInfo_Title1"] = 3,
    ["Arena_PerRankInfo_Title2"] = 3,
    ["Arena_PerRankMark_Title1"] = 3,
    ["Arena_PerRankMark_Title2"] = 3,
    ["Arena_PerRankMark_OpenTime"] = 3,
    ["Arena_PerRankMark_SonalName"] = 3,
    ["Arena_PerRankDetailds_Ranking"] = 3,
    ["Arena_PerRankInfo_NoneTip"] = 3,
    ["Arena_PerRankMark_NoneTip"] = 3,
    ["Arena_Train_DMG_DPS"] = 3,
    ["Arena_Train_DMG_Physical"] = 3,
    ["Arena_Train_DMG_Magic"] = 3,
    ["Arena_Train_DMG_Real"] = 3,
    ["Arena_PerRankInfo_BattleCount"] = 3,
    ["Arena_PerRankInfo_WinCount"] = 3,
    ["Arena_PerRankInfo_WinRate"] = 3,
    ["Arena_PerRankInfo_MVP"] = 3,
    ["Arena_PerRankInfo_HeroWinCount"] = 3,
    ["Arena_PerRankInfo_HeroBattleCount"] = 3,
    ["Arena_GameOver"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Champion"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Level"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Eliminate"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_WinStreak"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_FinalLevelRank"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Protected"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_Additional"] = 3,
    ["Arena_InLevel_FinaLQualiying_QPST_FinalDimension"] = 3,
    ["Arena_HotZone_Win"] = 3,
    ["Arena_HotZone_Lose"] = 3,
    ["Arena_Training_CardClear"] = 3,
    ["Arena_Training_ConfirmClear"] = 3,
    ["HotZone_ZoneShow"] = 3,
    ["HotZone_ProgressTipSelf"] = 3,
    ["HotZone_ProgressTipEnemy"] = 3,
    ["Arena_HeroBan"] = 3,
    ["Arena_HeroPick"] = 3,
    ["Arena_HeroIsBan"] = 3,
    ["Arena_HeroIslock"] = 3,
    ["Arena_HeroPreFormTeam"] = 3,
    ["Arena_HeroTryBuyPop"] = 3,
    ["Arena_Imprint_Tips"] = 3,
    ["Arena_PleryInfo_Career_Title_1"] = 3,
    ["Arena_PleryInfo_Career_Title_2"] = 3,
    ["Arena_PleryInfo_Career_Title_3"] = 3,
    ["Arena_PleryInfo_Career_Title_4"] = 3,
    ["Arena_PleryInfo_Career_Title_5"] = 3,
    ["Arena_PleryInfo_Career_Title_6"] = 3,
    ["Arena_PleryInfo_Career_Title_7"] = 3,
    ["Arena_RoadToHonor_NewHero"] = 3,
    ["Arena_RoadToHonor_NewCard"] = 3,
    ["Arena_RoadToHonor_NewSkin"] = 3,
    ["Arena_RoadToHonor_NewSkin_1"] = 3,
    ["Arena_ShardExchange_Message_Sign"] = 3,
    ["Arena_Shenmibaoxiang_Title_1"] = 3,
    ["Arena_Shenmibaoxiang_Title_2"] = 3,
    ["Arena_Recommend_Couple"] = 3,
    ["Arena_Recommend_Chum"] = 3,
    ["Arena_Recommend_Brother"] = 3,
    ["Arena_Recommend_Girlfriends"] = 3,
    ["Arena_Recommend_Friend_HighRank"] = 3,
    ["Arena_Recommend_Friend_HighIntimate"] = 3,
    ["Arena_Recommend_Friend"] = 3,
    ["Arena_Recommend_LastMVP"] = 3,
    ["Arena_Recommend_Win100"] = 3,
    ["Arena_Recommend_Legendary"] = 3,
    ["Arena_Recommend_WinStreak5"] = 3,
    ["Arena_Ranking_None"] = 3,
    ["Arena_Hero_Star"] = 3,
    ["Arena_Ranking_Global"] = 3,
    ["Arena_Recommend_Lobby"] = 3,
    ["Arena_Honor_Stay_Tuned"] = 3,
    ["Arena_Customize_Broadcast"] = 3,
    ["Arena_Customize_Mood"] = 3,
    ["Arena_Customize_HeadFrame"] = 3,
    ["Arena_Customize_SparyPaint"] = 3,
    ["Arena_Honor_Three_One"] = 3,
    ["Arena_Honor_Two_One"] = 3,
    ["Arena_Honor_Cur_Star"] = 3,
    ["Arena_In_Level_Download_Tip"] = 3,
    ["Arena_DayBox_Tips"] = 3,
    ["Arena_Invite_Platform"] = 3,
    ["Arena_DayBox_Tips_HY"] = 3,
    ["Arena_DayBox_Tips_QS"] = 3,
    ["Arena_HeroTryBuyPopAndName"] = 3,
    ["Arena_WeekendWindow_Content"] = 3,
    ["Arena_WeekendWindow_TiTle"] = 3,
    ["Arena_Arena_BattleHighest_Tips1"] = 3,
    ["Arena_Arena_BattleHighest_Tips2"] = 3,
    ["Arena_HostingByAI_Tips_3"] = 3,
    ["Arena_MatchLocked_Tips"] = 3,
    ["BS_CardBeenLocked"] = 4,
    ["BS_Kill"] = 4,
    ["BS_Defeat"] = 4,
    ["BS_DefeatStreak"] = 4,
    ["BS_Revived"] = 4,
    ["BS_ScoreReach"] = 4,
    ["BS_Goal"] = 4,
    ["BS_Careful"] = 4,
    ["BS_Duel"] = 4,
    ["BS_ChestSpawn"] = 4,
    ["BS_ScoreReach1"] = 4,
    ["BS_ScoreReach2"] = 4,
    ["BS_WaitRevive"] = 4,
    ["BS_ScoreReach1Img"] = 4,
    ["BS_ScoreReach2Img"] = 4,
    ["BS_TOXICKILL"] = 4,
    ["Arena_Rank_5"] = 4,
    ["BS_CallHelp"] = 4,
    ["BS_CallHelpBack"] = 4,
    ["BS_PortalReady"] = 4,
    ["BS_PortalCD"] = 4,
    ["UI_COC_QuitSaveConfirm"] = 5,
    ["UI_COC_QuitSaveConfirm_Save"] = 5,
    ["UI_COC_QuitSaveConfirm_NoSave"] = 5,
    ["UI_COC_InCold"] = 5,
    ["UI_COC_PleaseChooseTeamFirst"] = 5,
    ["UI_COC_DeployMonster_Invalid"] = 5,
    ["UI_COC_DeployMonster_Tips"] = 5,
    ["UI_COC_MatchFailed"] = 5,
    ["BUILDING_COC_BuildLimit"] = 5,
    ["BUILDING_COC_ResNotEnough"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityLimitExceeded"] = 5,
    ["UI_COC_CocsoldierLevelError"] = 5,
    ["UI_COC_CocsoldierTrainingAlreadyAccelerated"] = 5,
    ["BUILDING_COC_ConcurrentUpgradeLimit"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityLimitExceeded2"] = 5,
    ["UI_COC_COC_DiamondNotEnough"] = 5,
    ["UI_COC_CocSoldierDelete1"] = 5,
    ["UI_COC_CocSoldierDelete2"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityNotEnough"] = 5,
    ["UI_COC_DialogInvestigativeDetails_NotEnough"] = 5,
    ["UI_COC_CurrencyTitle"] = 5,
    ["UI_COC_CurrencyBuyDesc"] = 5,
    ["UI_COC_CurrencyBuyTips"] = 6,
    ["UI_COC_CurrencyShow1"] = 5,
    ["UI_COC_RescourceAcc"] = 5,
    ["UI_COC_RescourceAccDetail1"] = 5,
    ["UI_COC_CurrencyShow2"] = 5,
    ["UI_COC_RescourceAccDetail2"] = 5,
    ["UI_COC_RescourceDetailStore1"] = 5,
    ["UI_COC_RescourceDetailStore2"] = 5,
    ["UI_COC_RescourceDetailSpeed1"] = 5,
    ["UI_COC_RescourceDetailSpeed2"] = 5,
    ["UI_COC_BuildingFinish1"] = 5,
    ["UI_COC_BuildingFinish2"] = 5,
    ["UI_COC_HudInvestigativeCompleteTip"] = 5,
    ["UI_COC_BuildingLevel"] = 5,
    ["UI_COC_MailMainView_Tab1"] = 5,
    ["UI_COC_MailMainView_Tab2"] = 5,
    ["UI_COC_MailMainView_Tab3"] = 5,
    ["UI_COC_MailMainView_Num"] = 5,
    ["UI_COC_ItemInvestigative_NeedTip"] = 5,
    ["COCBuildMsgModel_ReqUpgradeScience_Full"] = 5,
    ["UI_COC_BuildingFinishImmediate"] = 5,
    ["UI_COC_CocSoldierDeleteCancel"] = 5,
    ["UI_COC_ResearchFinishImmediateContent1"] = 5,
    ["UI_COC_ResearchFinishImmediateContent2"] = 5,
    ["UI_COC_SoldierTrainFinishImmediateContent1"] = 5,
    ["UI_COC_SoldierTrainFinishImmediateContent2"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityTip"] = 5,
    ["UI_COC_CocVillagerDeleteCancel"] = 5,
    ["UI_COC_CocVillagerChooseCancel"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityNotEnough2"] = 5,
    ["UI_COC_CocSoldierTrainingCapacitySet"] = 5,
    ["UI_COC_CocSoldierTrainingCapacitySet2"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityBillboard"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityBillboard2"] = 5,
    ["UI_COC_CocSoldierTrainingCapacityReservist"] = 5,
    ["UI_COC_CocVillagerRecruitCancelTitle"] = 5,
    ["UI_COC_CocVillagerRecruitCancelDesc1"] = 5,
    ["UI_COC_CocVillagerRecruitCancelDesc2"] = 5,
    ["UI_COC_CocVillagerRenameCancelDesc"] = 5,
    ["UI_COC_MonthCardName"] = 5,
    ["UI_COC_MonthCardDesc"] = 5,
    ["UI_COC_MonthCardPrivilege1"] = 5,
    ["UI_COC_MonthCardPrivilege2_1"] = 5,
    ["UI_COC_MonthCardPrivilege2_2"] = 5,
    ["UI_COC_MonthCardPrivilegeDesc"] = 5,
    ["UI_COC_MonthCardDailyText1"] = 5,
    ["UI_COC_MonthCardDailyText2"] = 5,
    ["UI_COC_MonthCardDailyText3"] = 5,
    ["UI_COC_MonthCardDailyMax"] = 5,
    ["UI_COC_MonthCardDailyMaxDesc"] = 5,
    ["UI_COC_MonthCardDailyItemDesc"] = 5,
    ["UI_COC_MonthCardDailyItemDesc1"] = 5,
    ["UI_COC_MonthCardDailyItemDesc2"] = 5,
    ["UI_COC_MonthCardBuy"] = 5,
    ["UI_COC_MonthCardRenew"] = 5,
    ["UI_COC_MonthCardWay"] = 5,
    ["UI_COC_MonthCardBuyDesc"] = 5,
    ["UI_COC_Time_DayHour"] = 5,
    ["UI_COC_Time_MinuteSecond"] = 5,
    ["UI_COC_TrainAcc"] = 5,
    ["UI_COC_TrainAccDetail1"] = 5,
    ["UI_COC_BuildHomeCost"] = 5,
    ["UI_COC_BuildHomeCostDetail1"] = 5,
    ["UI_COC_TempBuildHomeCost"] = 5,
    ["UI_COC_TempBuildHomeCostDetail1"] = 5,
    ["UI_COC_PeopleUnlock"] = 5,
    ["UI_COC_PeopleUnlockDetail1"] = 5,
    ["UI_COC_PVERewardsAlreadyCollected"] = 5,
    ["UI_COC_MonthCardHelpTitle"] = 5,
    ["UI_COC_MonthCardHelpSubTitle"] = 5,
    ["UI_COC_MonthCardHelpContent"] = 5,
    ["UI_COC_VillagerBuild"] = 5,
    ["UI_COC_CocSoldierSkillOpen"] = 5,
    ["UI_COC_TempBuildHomeTips"] = 5,
    ["UI_COC_TempBuildHomeReplace"] = 5,
    ["UI_COC_NewQueueWay1"] = 5,
    ["UI_COC_NewQueueWay2"] = 5,
    ["UI_COC_WallUpdateDetail1"] = 5,
    ["UI_COC_WallUpdateDetail2"] = 5,
    ["UI_COC_WallUpdateTips1"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityName"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips1"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips2"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips3"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips4"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips5"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips6"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips7"] = 6,
    ["UI_COC_CocSoldierTrainingCapacityTips8"] = 6,
    ["UI_COC_ResearchTips1"] = 6,
    ["UI_COC_AppointName1"] = 6,
    ["UI_COC_AppointName2"] = 6,
    ["UI_COC_GroundMode"] = 6,
    ["UI_COC_GroundAirMode"] = 6,
    ["UI_COC_AirMode"] = 6,
    ["UI_COC_RescueTime"] = 6,
    ["UI_COC_RescueText"] = 6,
    ["UI_COC_InviteFriendSuccess"] = 6,
    ["UI_COC_RewardWithItemNum"] = 6,
    ["UI_COC_SwitchMode"] = 6,
    ["UI_COC_HelpClickTips"] = 6,
    ["UI_COC_Help1"] = 6,
    ["UI_COC_Help2"] = 6,
    ["UI_COC_Help3"] = 6,
    ["UI_COC_Help4"] = 6,
    ["UI_COC_HelpRecord"] = 6,
    ["UI_COC_QueueShow"] = 6,
    ["UI_COC_GroundTarget"] = 6,
    ["UI_COC_GroundAirTarget"] = 6,
    ["UI_COC_AirTarget"] = 6,
    ["UI_COC_CocVillagerUnlock"] = 6,
    ["UI_COC_PVEFailNoSoldier"] = 6,
    ["UI_COC_PVEFailTownCenter"] = 6,
    ["UI_COC_PVEFailLevel"] = 6,
    ["UI_COC_PVEFailConfig"] = 6,
    ["UI_COC_AllMainQuestFinished"] = 6,
    ["UI_COC_NoticeBuild"] = 6,
    ["UI_COC_NoticeTrain"] = 6,
    ["UI_COC_NoticeResearch"] = 6,
    ["UI_COC_NoticeTitle"] = 6,
    ["UI_COC_JAIL_upgradeinfo_1"] = 6,
    ["UI_COC_JAIL_upgradeinfo_2"] = 6,
    ["UI_COC_JAIL_upgradeinfo_3"] = 6,
    ["UI_COC_JAIL_upgradeinfo_4"] = 6,
    ["UI_COC_JAIL_upgradeinfo_5"] = 6,
    ["UI_COC_JAIL_upgradeinfo_6"] = 6,
    ["UI_COC_JAIL_upgradeinfo_7"] = 6,
    ["UI_COC_JAIL_upgradeinfo_8"] = 6,
    ["UI_COC_JAIL_inmateinfo_1"] = 6,
    ["UI_COC_JAIL_inmateinfo_2"] = 6,
    ["UI_COC_JAIL_inmateinfo_3"] = 6,
    ["UI_COC_JAIL_inmateinfo_4"] = 6,
    ["UI_COC_JAIL_inmateinfo_5"] = 6,
    ["UI_COC_JAIL_release_1"] = 6,
    ["UI_COC_JAIL_release_2"] = 6,
    ["Ul_COC_JAlL_guide_info"] = 6,
    ["UI_COC_MainBtnAttack"] = 6,
    ["UI_COC_MainBtnChallenge"] = 6,
    ["UI_COC_MainBtnDiscuss"] = 6,
    ["UI_COC_MainBtnGoHome"] = 6,
    ["UI_COC_StarAttackBtn_Lock"] = 6,
    ["UI_COC_StarManSkill"] = 6,
    ["UI_COC_BuildBuilding"] = 6,
    ["UI_COC_CaptureText"] = 6,
    ["Ul_COC_JAlL_prisoner_info_1"] = 6,
    ["Ul_COC_JAlL_prisoner_info_2"] = 6,
    ["Ul_COC_JAlL_prisoner_info_3"] = 6,
    ["Ul_COC_JAlL_prisoner_info_4"] = 6,
    ["Ul_COC_JAlL_accelerate_ban_info_1"] = 6,
    ["Ul_COC_JAlL_accelerate_ban_info_2"] = 6,
    ["Ul_COC_JAlL_accelerate_ban_info_3"] = 6,
    ["UI_COC_FeatureUnlockText"] = 6,
    ["UI_COC_DefenceBattleStartText"] = 6,
    ["UI_COC_DefenceBattleWaveText"] = 6,
    ["UI_COC_VillagerFashion1"] = 6,
    ["UI_COC_VillagerFashion2"] = 6,
    ["UI_COC_TrophyLevel"] = 6,
    ["UI_COC_TrophyLevelRule"] = 6,
    ["UI_COC_TrophyLevelRuleSpec1"] = 6,
    ["UI_COC_TrophyRank"] = 6,
    ["UI_COC_TrophyRankRule"] = 6,
    ["UI_COC_TrophyRankRuleSpec1"] = 6,
    ["UI_COC_ProsperityRank"] = 6,
    ["UI_COC_ProsperityRankRule"] = 6,
    ["UI_COC_ProsperityRankRuleSpec1"] = 6,
    ["UI_COC_NotEnoughStorageNotification"] = 6,
    ["UI_COC_ResearchNotEnoughNotification"] = 6,
    ["UI_COC_AlreadyReceiveGiftNotification"] = 6,
    ["UI_COC_NotEnoughFire"] = 6,
    ["UI_COC_BuildingFinish3"] = 6,
    ["UI_COC_BuildingFinish4"] = 6,
    ["UI_COC_BuildingRevamp"] = 6,
    ["UI_COC_BuildingUpgradeTips"] = 6,
    ["UI_COC_RevampUnlockCondition"] = 6,
    ["UI_COC_RevampNext"] = 6,
    ["UI_COC_RevampProcess"] = 6,
    ["UI_COC_RevampResNotEnough"] = 6,
    ["UI_COC_RevampTips1"] = 6,
    ["UI_COC_FastFinishTitle1"] = 7,
    ["UI_COC_FastFinishTitle2"] = 7,
    ["UI_COC_FastFinishTitle3"] = 7,
    ["UI_COC_FastFinishTips1"] = 7,
    ["UI_COC_FastFinishBuild1"] = 7,
    ["UI_COC_FastFinishBuild2"] = 7,
    ["UI_COC_FastFinishBuild3"] = 7,
    ["UI_COC_FastFinishScience1"] = 7,
    ["UI_COC_FastFinishScience2"] = 7,
    ["Card_Main_Name"] = 8,
    ["Card_Main_Title"] = 8,
    ["Card_Collection_All_Reward"] = 8,
    ["Card_Collection_Cup"] = 8,
    ["Card_Main_Deck_Button_Name"] = 8,
    ["Card_Package_Des"] = 8,
    ["Card_Main_Give_Button_Name"] = 8,
    ["Card_Main_History_Button_Name"] = 8,
    ["Card_Collection_Deck_Reward"] = 8,
    ["Card_Main_Help"] = 8,
    ["Card_Request_Base"] = 8,
    ["Card_Request_Ing_Self"] = 8,
    ["Card_Request_Player_Give_Self"] = 8,
    ["Card_Request_TimeOut_Self"] = 8,
    ["Card_Request_Answer_Other"] = 8,
    ["Card_Request_Count_Less_Other"] = 8,
    ["Card_Request_Player_Give_Other"] = 8,
    ["Card_Request_TimeOut_Other"] = 8,
    ["Card_Give_Base"] = 8,
    ["Card_Give_Ing_Self"] = 8,
    ["Card_Give_Player_Get_Self"] = 8,
    ["Card_Give_TimeOut_Self"] = 8,
    ["Card_Give_Answer_Other"] = 8,
    ["Card_Give_Player_Get_Other"] = 8,
    ["Card_Give_TimeOut_Other"] = 8,
    ["Card_Give_Other_Self"] = 8,
    ["Card_Request_Other_Self"] = 8,
    ["Card_Chat_Give_Other_Self"] = 8,
    ["Card_Chat_Reqeust_Self_Other"] = 8,
    ["Card_Confirm_Request_Single_Player"] = 8,
    ["Card_Confirm_Give_Players"] = 8,
    ["Card_Confirm_Request_Chat"] = 8,
    ["Card_Confirm_Give_Chat"] = 8,
    ["Card_Confirm_Button_Give_Name"] = 8,
    ["Card_Confirm_Button_Request_Name"] = 8,
    ["Card_History_Give_WaitAnswer_Other"] = 8,
    ["Card_History_Give_Chat"] = 8,
    ["Card_History_Give_WaitAnswer_Self"] = 8,
    ["Card_History_Request_WaitAnswer_Other"] = 8,
    ["Card_History_Request_WaitAnswer_Self"] = 8,
    ["Card_History_Give_TimeOut"] = 8,
    ["Card_History_Request_TimeOut"] = 8,
    ["Card_Give_Pop_Des"] = 8,
    ["Card_Tips_Request_Limit"] = 8,
    ["Card_Tips_Give_Limit"] = 8,
    ["Card_Tips_Request_Friend_Limit"] = 8,
    ["Card_Tips_Give_Max"] = 8,
    ["Card_Chat_Give_Limit"] = 8,
    ["Card_Preview_Glod"] = 8,
    ["Card_Preview_Give_Button_Name"] = 8,
    ["Card_Preview_Give_Button_Count"] = 8,
    ["Card_Preview_Request_Button_Name"] = 8,
    ["Card_Channel_Title_Give"] = 8,
    ["Card_Channel_Title_Request"] = 8,
    ["Card_Channel_Title_Chat"] = 8,
    ["Card_Channel_Title_Friend"] = 8,
    ["Card_History_Request_Private_Angain_Button"] = 8,
    ["Card_History_Request_Private_Angain"] = 8,
    ["Card_History_Request_Private_Other_Angain_Button"] = 8,
    ["Card_History_Request_Private_Other_Angain"] = 8,
    ["Card_History_Request_Private_Over"] = 8,
    ["Card_History_Request_Private_Other_Over"] = 8,
    ["Card_History_Request_Private_TimeOut"] = 8,
    ["Card_History_Request_Private_Other_TimeOut"] = 8,
    ["Card_History_Request_Chat_Angain_Button"] = 8,
    ["Card_History_Request_Chat_Angain"] = 8,
    ["Card_History_Request_Chat_Over"] = 8,
    ["Card_History_Request_Chat_TimeOut"] = 8,
    ["Card_History_Request_Chat_Answer_Over"] = 8,
    ["Card_History_Give_Private_Angain_Button"] = 8,
    ["Card_History_Give_Private_Angain"] = 8,
    ["Card_History_Give_Private_Other_Angain_Button"] = 8,
    ["Card_History_Give_Private_Other_Angain"] = 8,
    ["Card_History_Give_Private_Over"] = 8,
    ["Card_History_Give_Private_Other_Over"] = 8,
    ["Card_History_Give_Private_TimeOut"] = 8,
    ["Card_History_Give_Private_Other_TimeOut"] = 8,
    ["Card_History_Give_Chat_Angain_Button"] = 8,
    ["Card_History_Give_Chat_Angain"] = 8,
    ["Card_History_Give_Chat_Over"] = 8,
    ["Card_History_Give_Chat_TimeOut"] = 8,
    ["Card_History_Give_Chat_Answer_Over"] = 8,
    ["Card_History_Give_Title"] = 8,
    ["Card_History_Request_Title"] = 8,
    ["Card_Friend_Select"] = 8,
    ["Card_Friend_Time"] = 8,
    ["Card_Chat_Request_Color_Blue"] = 8,
    ["Card_Chat_Request_Color_Gold"] = 8,
    ["Card_GetCollection_Deck"] = 8,
    ["Card_GetCollection_Set"] = 8,
    ["Card_Friend_Request_Title"] = 8,
    ["Card_GetCollection_RewardTitle"] = 8,
    ["Card_Circle_Limit_Des"] = 8,
    ["Card_History_Exchange_Title"] = 8,
    ["Card_History_Exchange_Private_Self_To_Others_Again"] = 8,
    ["Card_History_Exchange_Private_Self_To_Other_Again"] = 8,
    ["Card_History_Exchange_Private_Other_To_Me_Again"] = 8,
    ["Card_History_Exchange_Private_Other_To_Me_Over"] = 8,
    ["Card_History_Exchange_Private_Other_To_Me_ByOne_Over"] = 8,
    ["Card_History_Exchange_Private_Me_To_Others_Over"] = 8,
    ["Card_History_Exchange_Private_Me_To_Other_Over"] = 9,
    ["Card_History_Exchange_Private_Me_To_Other_TimeOut"] = 9,
    ["Card_History_Exchange_Private_Me_To_Others_TimeOut"] = 9,
    ["Card_History_Exchange_Private_Other_To_Me_TimeOut"] = 9,
    ["Card_History_Exchange_Chat_Me_TimeOut"] = 9,
    ["Card_History_Exchange_Chat_Me_Over"] = 9,
    ["Card_History_Exchange_Chat_Me_Again"] = 9,
    ["Card_History_Exchange_Chat_Other_Over"] = 9,
    ["Card_History_Exchange_Chat_Again_Button"] = 9,
    ["Card_History_Exchange_Private_Again_Button"] = 9,
    ["Card_History_Exchange_For_Other_Button"] = 9,
    ["Card_History_Exchange_TimeOut"] = 9,
    ["Card_History_Exchange_Over_Button"] = 9,
    ["Card_History_Exchange_Over_Next_Button"] = 9,
    ["Card_Exchange_Title"] = 9,
    ["Card_Exchange_Button"] = 9,
    ["Card_Exchange_Des"] = 9,
    ["Card_Exchange_No_Cards"] = 9,
    ["Card_Exchange_Right_Des"] = 9,
    ["Card_Confirm_Exchange_Title"] = 9,
    ["Card_Confirm_Exchange_Content"] = 9,
    ["Card_Confirm_Exchange_Cancle"] = 9,
    ["Card_Confirm_Exchange_Sure"] = 9,
    ["Card_Preview_Exchange_Button_Name"] = 9,
    ["Card_Preview_Exchange_Button_Count"] = 9,
    ["Card_Channel_Title_Exchange"] = 9,
    ["Card_Channel_QQ"] = 9,
    ["Card_Channel_WX"] = 9,
    ["Card_Channel_Close"] = 9,
    ["Card_Exchange_Select_Friend_Title"] = 9,
    ["Card_Exchange_Select_Friend_Des"] = 9,
    ["Card_Exchange_Select_Friend_Already"] = 9,
    ["Card_Exchange_Select_Friend_Time"] = 9,
    ["Card_Confirm_Answer_Exchange_Title"] = 9,
    ["Card_Confirm_Answe_Exchange_Content"] = 9,
    ["Card_Confirm_Answe_Exchange_Cancle"] = 9,
    ["Card_Confirm_Answe_Exchange_Sure"] = 9,
    ["Card_Confirm_Answe_Tips"] = 9,
    ["Card_Confirm_Tips"] = 9,
    ["Card_WildCard_Title"] = 9,
    ["Card_WildCard_TimeOut"] = 9,
    ["Card_WildCard_Left_CardDeck"] = 9,
    ["Card_WildCard_Check"] = 9,
    ["Card_WildCard_Des"] = 9,
    ["Card_WildCard_Button_Dis_Name"] = 9,
    ["Card_WildCard_Button_Name"] = 9,
    ["Card_WildCard_Max_Tips"] = 9,
    ["Card_WildCard_Tips_Dis"] = 9,
    ["Card_WildCard_Main_TimeOut"] = 9,
    ["Card_History_Request_QQWX_Angain_Button"] = 9,
    ["Card_History_Request_QQWX_Angain"] = 9,
    ["Card_History_Request_QQWX_Over"] = 9,
    ["Card_History_Request_QQWX_TimeOut"] = 9,
    ["Card_History_Request_QQWX_Answer_Over"] = 9,
    ["Card_History_Give_QQWX_Angain_Button"] = 9,
    ["Card_History_Give_QQWX_Angain"] = 9,
    ["Card_History_Give_QQWX_Over"] = 9,
    ["Card_History_Give_QQWX_TimeOut"] = 9,
    ["Card_History_Give_QQWX_Answer_Over"] = 9,
    ["Card_History_Exchange_QQWX_Me_TimeOut"] = 9,
    ["Card_History_Exchange_QQWX_Me_Over"] = 9,
    ["Card_History_Exchange_QQWX_Me_Again"] = 9,
    ["Card_History_Exchange_QQWX_Other_Over"] = 9,
    ["Card_History_Exchange_QQWX_Again_Button"] = 9,
    ["Card_History_Exchange_QQWX_Other_Me_TimeOut"] = 9,
    ["Card_History_Exchange_QQWX_Other_Me_Again"] = 9,
    ["Card_History_Request_QQWX_Other_Me_TimeOut"] = 9,
    ["Card_History_Request_QQWX_Other_Me_Again"] = 9,
    ["Card_History_WXHead_Name"] = 9,
    ["Card_History_QQHead_Name"] = 9,
    ["Card_Confirm_Request_Title"] = 9,
    ["Card_Confirm_Give_Title"] = 9,
    ["Card_Chat_Exchange_Waiting"] = 9,
    ["Card_Chat_Give_Waiting"] = 9,
    ["Card_Chat_Exchange_Button"] = 9,
    ["Card_Preview_Share_Button_Name"] = 9,
    ["Card_Preview_GiveAndExchange_Des"] = 9,
    ["Card_Preview_GiveAndExchange_Des_Count"] = 9,
    ["Card_Chat_Exchange_Title"] = 9,
    ["Card_Tips_Exchange_Friend_Limit"] = 9,
    ["Card_History_Daily_Close_Check"] = 9,
    ["Card_Right_Request"] = 9,
    ["Card_Right_Exchange"] = 9,
    ["Card_Right_Check_Content"] = 9,
    ["Card_Right_Cancle_Btn_Name"] = 9,
    ["Card_Right_Request_Sure_Btn_Name"] = 9,
    ["Card_Right_Exchange_Sure_Btn_Name"] = 9,
    ["Card_Right_Request_Sure_Dis_Btn_Name"] = 9,
    ["Card_Right_Exchange_Sure_Dis_Btn_Name"] = 9,
    ["Card_Right_Request_Title"] = 9,
    ["Card_Right_Exchange_Title"] = 9,
    ["Card_All_Powerful_Pop_Title"] = 9,
    ["Card_All_Powerful_Pop_Hint"] = 9,
    ["Card_All_Powerful_Pop_Show_Have_Btn_Msg"] = 9,
    ["Card_All_Powerful_Pop_Convert_Btn_Msg"] = 9,
    ["Card_All_Powerful_Pop_Convert_Btn_Msg_Dis"] = 9,
    ["Card_All_Powerful_Pop_Click_Card"] = 9,
    ["Card_All_Powerful_Pop_Click_Convert_Hint"] = 9,
    ["Card_All_Powerful_Pop_Click_Convert_Hint2"] = 9,
    ["Card_Request_Button"] = 9,
    ["Card_Give_Title"] = 10,
    ["Card_Chat_Request_Finish_Base"] = 10,
    ["Card_Chat_Give_Finish_Base"] = 10,
    ["Card_Chat_Exchange_Finish_Base"] = 10,
    ["Card_Main_GetCard_ButtonName"] = 10,
    ["Card_GetWayTips_Title"] = 10,
    ["Card_GetWayTips_des"] = 10,
    ["Card_GetWayTips_Get_ButtonName"] = 10,
    ["Card_Record_Plat_Des"] = 10,
    ["Card_Record_Plat_WX"] = 10,
    ["Card_Record_Plat_QQ"] = 10,
    ["Card_GetWayTips_Cup_Title"] = 10,
    ["Card_GetWayTips_Cup_Content"] = 10,
    ["Card_GetWayTips_Cup_SureButton_Name"] = 10,
    ["Card_Exchange_Limit"] = 10,
    ["Card_Activity_Bubble"] = 10,
    ["Card_Exchange_Bubble"] = 10,
    ["Card_Bounds_Bubble"] = 10,
    ["Card_WildCard_Bubble"] = 10,
    ["Card_Activity_Bubble_1"] = 10,
    ["Card_Exchange_Bubble_1"] = 10,
    ["Card_Exchange_HitText"] = 10,
    ["Card_Exchange_Be_HitText"] = 10,
    ["Card_Main_Shop_Name"] = 10,
    ["SelfUmg_Card_Tips_1"] = 10,
    ["SelfUmg_Card_Tips_2"] = 10,
    ["Card_Welfare_Tips_1"] = 10,
    ["Card_Vertical_Tip"] = 10,
    ["Card_PrivateSetting_Tip"] = 10,
    ["Card_Version_Tip_Main"] = 10,
    ["Card_Version_Tip_Chat_Control"] = 10,
    ["Card_Version_Tip_CardGet"] = 10,
    ["Card_download_Tip"] = 10,
    ["Card_History_Give_Normal_TimeOut"] = 10,
    ["Card_History_Give_Normal_Angain"] = 10,
    ["Card_History_Request_Normal_Angain"] = 10,
    ["Card_CrownView_CardBagName"] = 10,
    ["Card_CloudToAPP_Title"] = 10,
    ["Card_CloudToAPP_Sure"] = 10,
    ["Card_CloudToAPP_Cancle"] = 10,
    ["Card_CloudToAPP_Main"] = 10,
    ["Card_ActivityTime"] = 10,
    ["Card_Main_CardSetHistory_Button_Name"] = 10,
    ["Card_Main_CardSetHistory_CurrentSet"] = 10,
    ["Card_Main_CardSetHistory_HistorySet"] = 10,
    ["Card_Main_CardSetHistory_HistorySet_Des"] = 10,
    ["Card_Preview_History_NoCard"] = 10,
    ["Card_GetCard_GoButtonName"] = 10,
    ["Card_Exchange_SecondTips_1"] = 10,
    ["Card_Exchange_SecondTips_2"] = 10,
    ["Card_Exchange_AutoSelect"] = 10,
    ["Card_Exchange_Limit_FriendNoCard"] = 10,
    ["Card_Exchange_Limit_FriendHasCard"] = 10,
    ["Card_Exchange_Limit_Confirm_Content"] = 10,
    ["Card_Exchange_Limit_Chat_NotCloudExchange"] = 10,
    ["Card_Exchange_Limit_Chat_NotEnoughCount"] = 10,
    ["Card_Exchange_Limit_Chat_LimitCount"] = 10,
    ["Card_Exchange_Limit_Give"] = 10,
    ["Card_Exchange_Limit_Want"] = 10,
    ["Card_Exchange_Limit_ButtonChange"] = 10,
    ["Card_Exchange_Limit_ButtonChoose"] = 10,
    ["Card_Exchange_Limit_Chat_Content"] = 10,
    ["Card_Exchange_Limit_Chat_MyGive"] = 10,
    ["Card_Exchange_Limit_Chat_MyWant"] = 10,
    ["Card_Exchange_Limit_Chat_OtherGive"] = 10,
    ["Card_Exchange_Limit_Chat_OtherWant"] = 10,
    ["Card_Exchange_Limit_Confirm_Title"] = 10,
    ["Card_Exchange_Limit_Choose_Recommend"] = 10,
    ["Card_Exchange_Limit_Invite_Des"] = 10,
    ["Card_Exchange_Limit_Invite_MyGive"] = 10,
    ["Card_Exchange_Limit_Invite_MyWant"] = 10,
    ["Card_Exchange_Limit_Max_SelectCard"] = 10,
    ["Card_Exchange_Limit_NoSelectContent"] = 10,
    ["Card_Exchange_Limit_SelectDes"] = 10,
    ["Card_Exchange_Limit_SelectTitle"] = 10,
    ["Card_ShareInPic_CardSet"] = 10,
    ["Card_ShareInPic_CardDeck"] = 10,
    ["Card_Give_Button_NoCardSelect"] = 10,
    ["LFT_CardTrade"] = 10,
    ["Card_Exchange_StarTips_1"] = 10,
    ["Card_Exchange_StarTips_2"] = 10,
    ["UI_Card_IP_Main_Content_Title"] = 10,
    ["UI_Card_IP_Main_Content"] = 10,
    ["UI_Card_IP_Tips_Content"] = 10,
    ["Card_CardOpen_Bubble"] = 10,
    ["Club_MainTab"] = 11,
    ["Club_Members"] = 11,
    ["Club_GroupDailyCreateNumLimit"] = 11,
    ["Club_GroupAlreadyBound"] = 11,
    ["Club_GroupMaxCreateNumLimit"] = 11,
    ["Club_GroupFrequentLimit"] = 11,
    ["Club_GroupSecurityLimit"] = 11,
    ["Club_GroupJoinNumLimit"] = 11,
    ["Club_GroupCreateSuccess"] = 11,
    ["Club_GroupCreateFail"] = 11,
    ["Club_GroupJoinFailed"] = 11,
    ["Club_GroupJoinSuccess"] = 11,
    ["Club_GroupCreated"] = 11,
    ["Club_GroupJoined"] = 11,
    ["Club_GroupNotCreate"] = 11,
    ["Club_CreateCostCoinOwnNum"] = 11,
    ["Club_NameNull"] = 11,
    ["Club_Create"] = 11,
    ["Club_Setting"] = 11,
    ["Club_MemberStateXiaowo"] = 11,
    ["Club_MemberKickTips"] = 11,
    ["Club_MemberDissolve"] = 11,
    ["Club_UIDCopied"] = 11,
    ["Club_MemberLatelySaveXiaowo"] = 11,
    ["Club_MemberHourlyLayoutSaveXiaowo"] = 11,
    ["Club_MemberMinlyLayoutSaveXiaowo"] = 11,
    ["Club_MemberLeaveClub"] = 11,
    ["Club_HomeDataResponseFail"] = 11,
    ["Club_JoinApplied"] = 11,
    ["Club_JoinApplyFail"] = 11,
    ["Club_InviteMessageSended"] = 11,
    ["Club_ModifySettingSuccess"] = 11,
    ["Club_ModifySettingFail"] = 11,
    ["Club_NotOpen"] = 11,
    ["Club_SearchNoResult"] = 11,
    ["Club_NameAllSpace"] = 11,
    ["Club_MinTagLimit"] = 11,
    ["Club_InviteToWorldLeftCd"] = 11,
    ["Club_InviteToLobbyLeftCd"] = 11,
    ["Club_TitleAllMap"] = 11,
    ["Club_TitleAddMap"] = 11,
    ["Club_AddMapNum"] = 11,
    ["Club_MapNumLimit"] = 11,
    ["Club_MapSelectLimit"] = 11,
    ["Club_TagNumLimit"] = 11,
    ["Club_MapPinNumLimit"] = 11,
    ["Club_CreateCoinLimit"] = 11,
    ["Club_HeatTip"] = 11,
    ["Club_CreateLevelLimit"] = 11,
    ["Club_HomeTip"] = 11,
    ["Club_SearchPlayerNoResult"] = 11,
    ["Club_JoinToShareQRCode"] = 11,
    ["Club_NetErrorGetInfoFail"] = 11,
    ["Club_JoinToInvite"] = 11,
    ["Club_MapSaved"] = 11,
    ["ClubShareTitle"] = 11,
    ["ClubShareDescription"] = 11,
    ["Club_PromoteVicePresident"] = 11,
    ["Club_DemoteVicePresident"] = 11,
    ["Club_VicePresidentPromoteConfirm"] = 11,
    ["Club_VicePresidentDemoteConfirm"] = 11,
    ["Club_HandOverPresidentConfirm"] = 11,
    ["Club_President"] = 11,
    ["Club_VicePresident"] = 11,
    ["Club_HandOverPresidentBeforeQuit"] = 11,
    ["Club_AskGroupMemberToJoin"] = 11,
    ["Club_UnbindOldToCreateNewGroup"] = 11,
    ["Club_OwnerApproveApply_Success"] = 11,
    ["Club_Societies_UIHomepage"] = 11,
    ["Club_RankRule_SubTitle"] = 11,
    ["Club_RankRule_Content"] = 11,
    ["Club_RankSettlement_1"] = 11,
    ["Club_RankSettlement_2"] = 11,
    ["Club_RankSettlement_3"] = 11,
    ["Club_RankSettlement_Rule"] = 11,
    ["Club_LBS_Rule"] = 11,
    ["Club_JoinRank_Notice"] = 11,
    ["Club_JoinRank_LBS_Notice"] = 11,
    ["Club_JoinRank_LBS_System"] = 11,
    ["Club_Log_Log"] = 11,
    ["Club_Log_Course"] = 11,
    ["Club_Log_NoLog"] = 11,
    ["Club_Log_NoCourse"] = 11,
    ["Club_Log_CourseDesc"] = 11,
    ["Text_Societies_Rank_Des"] = 11,
    ["Text_Societies_Tags_Recommend"] = 11,
    ["Text_Societies_Tags_Rank"] = 11,
    ["BattleReMatch_WaitForPartner"] = 11,
    ["BattleReMatch_ProposalAgain"] = 11,
    ["BattleReMatch_ProposalAgainToLeader"] = 11,
    ["BattleReMatch_TimeCountDownSuffix"] = 11,
    ["BattleReMatch_CancelAgree"] = 11,
    ["BattleReMatch_BattleTogether"] = 11,
    ["BattleReMatch_WaitForTogether"] = 11,
    ["BattleReMatch_BattleAgain"] = 11,
    ["CaptureShadowItemUnlockTip"] = 11,
    ["Text_SFPlaze_Fuqi_Initiative_Tips"] = 11,
    ["Text_SFPlaze_Bainian_Initiative_Tips"] = 11,
    ["Text_SFPlaze_Huadeng_Initiative_Tips"] = 11,
    ["Text_SFPlaze_Fuqi_Passive_Tips"] = 11,
    ["Text_SFPlaze_Bainian_Passive_Tips"] = 12,
    ["Text_SFPlaze_Huadeng_Passive_Tips"] = 12,
    ["Text_SFPlaze_Need2beClose_Tips"] = 12,
    ["Text_SFPlaze_Need2Update_Tips"] = 12,
    ["Text_SFPlaze_InitiativeCD_Fuqi_Tips"] = 12,
    ["Text_SFPlaze_InitiativeCD_Bainian_Tips"] = 12,
    ["Text_SFPlaze_InitiativeCD_Huadeng_Tips"] = 12,
    ["Text_SFPlaze_LevelMax_Fuqi_Tips"] = 12,
    ["Text_SFPlaze_LevelMax_Bainian_Tips"] = 12,
    ["Text_SFPlaze_LevelMax_Huadeng_Tips"] = 12,
    ["WallaceVersionUpdateTips"] = 12,
    ["Text_Fuzi_NotEnough_Tips"] = 12,
    ["Text_General_ActionNegetive_Tips"] = 12,
    ["Text_SFPlaze_Name_Tiefuzi"] = 12,
    ["Text_SFPlaze_Name_Bainian"] = 12,
    ["Text_SFPlaze_Name_Dianhuadeng"] = 12,
    ["Text_SFPlaze_HeightLimiet_Tips"] = 12,
    ["Text_DoubleHigher_Intro_Tips"] = 12,
    ["Text_Wallace_Change_Tips_1"] = 12,
    ["Text_Wallace_Change_Tips_2"] = 12,
    ["Text_SFPlaze_LevelMax_Tiefuzi_Tips"] = 12,
    ["Text_SFPlaze_LevelMax_Bainian_Tips_1"] = 12,
    ["Text_SFPlaze_LevelMax_Dianhuadeng_Tips"] = 12,
    ["Text_SFPlaze_LevelMax_Button_Tips"] = 12,
    ["Text_SFPlaze_Name_Tiefuzi_Tips"] = 12,
    ["Text_SFPlaze_Name_Tiefuzi_MoreTips"] = 12,
    ["Text_SFPlaze_Name_Tiefuzi_Get_Tips"] = 12,
    ["Text_SFPlaze_Wulong_OverLimit_Tips"] = 12,
    ["Text_SFPlaze_Wulong_NotAccess_Tips"] = 12,
    ["UI_NewChat_PlayerNumTitle"] = 12,
    ["UI_NewChat_PlayerNumSwitch"] = 12,
    ["UI_NewChat_PlayerNumEmpty"] = 12,
    ["UI_NewChat_PlayerNumHome"] = 12,
    ["UI_NewChat_PlayerNumFarm"] = 12,
    ["UI_NewChatLobbyBottom_PlayerNum_Title"] = 12,
    ["UI_NewChatLobbyBottom_PlayerNum_Content"] = 12,
    ["Text_SFPlaze_HugNegative_Tips"] = 12,
    ["NewChat_SelfSharePosition"] = 12,
    ["Text_Club_WeixinGroup_CloudGame_Tips"] = 12,
    ["NewChat_TooManyDuplicateText"] = 12,
    ["Prop_MewMewBomb_Name"] = 12,
    ["Prop_MewMewBomb_Tips"] = 12,
    ["Prop_MewMewBomb_MoreTips"] = 12,
    ["UI_Club_NoneLocation"] = 12,
    ["NewChat_ClearAllNotRedDotMsg_Content"] = 12,
    ["Community_Conan_ChatBubble_CatchKid_1"] = 12,
    ["Community_Kid_Enter_Pretender"] = 12,
    ["Community_Conan_ChatBubble_CatchKid_2"] = 12,
    ["Community_Kid_Enter_Air"] = 12,
    ["Community_Conan_ChatBubble_CatchKid_3"] = 12,
    ["FB_Revive_CountDown"] = 13,
    ["RoguelikeTalent_Activate_Progress"] = 14,
    ["RoguelikeTalent_Need_UnlockPreTalent"] = 14,
    ["RoguelikeTalent_HadActivate"] = 14,
    ["RoguelikeTalent_UnlockSkill"] = 14,
    ["RoguelikeTalent_UpgradeSkill"] = 14,
    ["Set_EditKey_FPSRoguelikeGame"] = 14,
    ["RoguelikeTaskDifficulty_1"] = 14,
    ["RoguelikeTaskDifficulty_2"] = 14,
    ["RoguelikeTaskDifficulty_3"] = 14,
    ["RoguelikeTalent_Result_PreTalentLock"] = 14,
    ["RoguelikeTalent_Result_NoMoney"] = 14,
    ["RoguelikeTalent_Result_ConfigError"] = 14,
    ["RoguelikeTalent_Result_HadUnlock"] = 14,
    ["RoguelikeTalent_Result_Fail"] = 14,
    ["RoguelikeTask_RefreshTimes"] = 14,
    ["RoguelikeTask_ResetTime"] = 14,
    ["RoguelikeTask_NoCanRefreshTask"] = 14,
    ["RoguelikeTask_RefreshTimesLimit"] = 14,
    ["RoguelikeTask_TaskHadFinished"] = 14,
    ["RoguelikeTask_RefreshFail"] = 14,
    ["RoguelikeTask_RefreshSuccess"] = 14,
    ["BioChase_HeadIcon_Monster1002"] = 14,
    ["BioChase_HeadIcon_Monster1003"] = 14,
    ["BioChase_HeadIcon_Monster2001"] = 14,
    ["UI_BioChase_DeadTip"] = 14,
    ["UI_BioChase_DeadTip_1"] = 14,
    ["Level_BioChase_ShotGhost_Fight"] = 14,
    ["Level_BioChase_StartTips_Fight"] = 14,
    ["Level_BioChase_FinishiTips_Fight"] = 14,
    ["UI_FPSSetting_CommonItem_Tips"] = 14,
    ["UI_DfGame_Resurrection_BigTitle"] = 14,
    ["UI_DfGame_Resurrection_SmallTitle1"] = 14,
    ["UI_DfGame_Resurrection_SmallTitle2"] = 14,
    ["UI_DfGame_ResurrectionItem_LowQuality"] = 14,
    ["UI_DfGame_ResurrectionItem_MidQuality"] = 14,
    ["UI_DfGame_ResurrectionItem_HighQuality"] = 14,
    ["UI_DrGame_ResurrectionItem_AbandonRespawnTips"] = 14,
    ["UI_DrGame_ResurrectionItem_Free"] = 14,
    ["UI_DrGame_Time_BattleTime"] = 14,
    ["UI_DrGame_Time_LeaveTime"] = 14,
    ["UI_DrGame_PreEvent_BoxRefresh"] = 14,
    ["UI_DrGame_PreEvent_TreasureCrabRefresh"] = 14,
    ["UI_DrGame_PreEvent_EscapePointRefresh"] = 14,
    ["UI_DrGame_PreEvent_FinalEscapeTime"] = 14,
    ["UI_DrGame_RankItem_LeaveTip"] = 14,
    ["UI_DrGame_Get_Coin_Limit_Tips"] = 14,
    ["UI_DrGame_Get_Award_Skip_Animation_Tips"] = 14,
    ["UI_FPSGame_ChampionshipTeam"] = 14,
    ["UI_FPSGame_RunnerUpTeam"] = 14,
    ["UI_GunGameKC_RedBag"] = 14,
    ["UI_DFGame_LeaveSucc"] = 14,
    ["UI_DFGame_LeaveFail"] = 14,
    ["UI_DFGame_StartGame_BuyTips"] = 14,
    ["UI_DFGame_StartGame_TaskTips"] = 14,
    ["UI_GunGameKC_RankListTitle_Pick"] = 14,
    ["UI_FpsGame_VoiceRoom_Team"] = 14,
    ["UI_DFGame_DropAwardRule"] = 14,
    ["UI_DFGame_DropAwardRule_NoOverflow"] = 14,
    ["UI_KCGame_DropAwardRule"] = 14,
    ["UI_FPSGame_DropAwardDesc"] = 14,
    ["UI_FPSGame_DropAwardDescTitle"] = 14,
    ["UI_FPSGame_DropAwardDescContent"] = 14,
    ["UI_DFGame_DropAwardTitle"] = 14,
    ["UI_KCGame_DropAwardTitle"] = 14,
    ["UI_Setting_DFGame_CustomName"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_1"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_2"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_3"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_4"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_1"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_2"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_3"] = 14,
    ["UI_FPS_Roguelike_Difficulty_Select_Desc_4"] = 14,
    ["UI_FPS_Roguelike_RemainTime"] = 14,
    ["UI_FPS_Roguelike_SeasonLevel"] = 14,
    ["UI_FPS_Roguelike_RemainSeasonDays"] = 14,
    ["UI_FPS_Roguelike_EndlessPassNumber"] = 14,
    ["UI_FPS_Roguelike_FastStartTips"] = 14,
    ["UI_FPS_Roguelike_FastStartBtn"] = 14,
    ["UI_FPS_Roguelike_FastStartOver"] = 14,
    ["UI_FPS_Roguelike_FastFinishTips"] = 14,
    ["UI_FPS_Roguelike_EndlessContinue"] = 14,
    ["UI_FPS_Roguelike_EndlessIsSinglePlay"] = 14,
    ["UI_FPS_Roguelike_SeasonMaxRewardCondition"] = 14,
    ["UI_FPS_Roguelike_SeasonTaskTabName_2"] = 14,
    ["UI_FPS_Roguelike_SeasonTaskTabName_3"] = 14,
    ["UI_FPS_Roguelike_SeasonTaskTabName_4"] = 14,
    ["UI_FPS_Roguelike_SeasonTask_ResetTime"] = 14,
    ["UI_FPS_Roguelike_SeasonTask_EndTime"] = 14,
    ["UI_FPS_Roguelike_SeasonMilestoneName"] = 14,
    ["UI_FPS_Roguelike_SeasonTaskName"] = 14,
    ["UI_Roguelike_Rank"] = 14,
    ["UI_Roguelike_RankReward"] = 14,
    ["UI_BrGame_WaterPark"] = 14,
    ["UI_BrGame_SacredTree"] = 14,
    ["UI_BrGame_Relic"] = 14,
    ["UI_BrGame_Temple"] = 14,
    ["UI_BrGame_ChinaTown"] = 14,
    ["UI_BrGame_Village"] = 14,
    ["UI_BrGame_Idyllic"] = 14,
    ["UI_BrGame_CentralBase"] = 15,
    ["UI_BrGame_HeritagePark"] = 15,
    ["UI_BrGame_Corsair"] = 15,
    ["UI_BrGame_Laboratory"] = 15,
    ["UI_BrGame_BotanicalGarden"] = 15,
    ["UI_BrGame_WoodenHouse"] = 15,
    ["UI_FPSSetting_ChangeCameraTips"] = 15,
    ["UI_FPSSetting_DF_Title"] = 15,
    ["UI_Roguelike_Tutorial_PickReward"] = 15,
    ["UI_Roguelike_Tutorial_DifficultySelect"] = 15,
    ["UI_Roguelike_Tutorial_Talent"] = 15,
    ["UI_Roguelike_Tutorial_SkillSwitch"] = 15,
    ["UI_Roguelike_Tutorial_DifficultyEndless"] = 15,
    ["UI_Roguelike_Tutorial_MissionTarget"] = 15,
    ["UI_Roguelike_Tutorial_HealthBar"] = 15,
    ["UI_Roguelike_Tutorial_RewardConfirm"] = 15,
    ["UI_Roguelike_Tutorial_Bag"] = 15,
    ["UI_Roguelike_Tutorial_Collection"] = 15,
    ["UI_Roguelike_Tutorial_CollectionLvUp"] = 15,
    ["UI_Roguelike_Tutorial_CollectionLvUp2"] = 15,
    ["UI_Roguelike_Tutorial_CollectionSpecial"] = 15,
    ["UI_Roguelike_Tutorial_Artifact"] = 15,
    ["UI_Roguelike_Tutorial_LevelUp"] = 15,
    ["UI_Roguelike_Tutorial_PickLevel"] = 15,
    ["UI_Roguelike_Tutorial_PickLevelConfirm"] = 15,
    ["UI_Roguelike_Tutorial_TalentPage"] = 15,
    ["UI_Roguelike_Tutorial_UnlockTalent"] = 15,
    ["UI_Roguelike_Settlement_GetPuzzles"] = 15,
    ["UI_Roguelike_Tutorial_VoteToStart"] = 15,
    ["UI_Roguelike_Tutorial_RefreshItems"] = 15,
    ["UI_Roguelike_Tutorial_ShopBuyTip"] = 15,
    ["UI_Roguelike_Tutorial_Armory"] = 15,
    ["UI_Roguelike_Tutorial_RefreshShop"] = 15,
    ["UI_Roguelike_Tutorial_AfterShop"] = 15,
    ["UI_FPS_Weapon_Unlock_Official_1"] = 15,
    ["UI_FPS_Weapon_Unlock_Official_2"] = 15,
    ["UI_FPS_Weapon_Unlock_Official_3"] = 15,
    ["UI_Roguelike_PassLevelCount_EndLessLD"] = 15,
    ["UI_Roguelike_PassLevelCount"] = 15,
    ["UI_Roguelike_MarkUpgradMaterialName"] = 15,
    ["UI_Roguelike_TalentPointName"] = 15,
    ["UI_Roguelike_Tutorial_Lobby_Mark"] = 15,
    ["UI_Roguelike_Tutorial_Mark_NewSet"] = 15,
    ["UI_Roguelike_Tutorial_Mark_Desc"] = 15,
    ["UI_Roguelike_Tutorial_Mark_MoveToBoard"] = 15,
    ["UI_Roguelike_Tutorial_Mark_Combo"] = 15,
    ["UI_Roguelike_Tutorial_Mark_Upgrade"] = 15,
    ["UI_Roguelike_Tutorial_Mark_Delete"] = 15,
    ["UI_Roguelike_RulesTab_1"] = 15,
    ["UI_Roguelike_RulesTab_2"] = 15,
    ["UI_Roguelike_RulesTab_3"] = 15,
    ["UI_Roguelike_RulesTab_4"] = 15,
    ["UI_Roguelike_RulesTab_5"] = 15,
    ["UI_Roguelike_RulesTab_6"] = 15,
    ["UI_Roguelike_RulesTab_7"] = 15,
    ["UI_Roguelike_RulesTab_8"] = 15,
    ["UI_Roguelike_RulesTab_9"] = 15,
    ["UI_Roguelike_RulesTab_10"] = 15,
    ["UI_Roguelike_RulesTab_11"] = 15,
    ["UI_Roguelike_RulesTab_12"] = 15,
    ["UI_Roguelike_NextEnterFreshTask"] = 15,
    ["UI_Roguelike_Prepared"] = 15,
    ["UI_Roguelike_Unprepared"] = 15,
    ["UI_Roguelike_Talent_ActivateManyTitle"] = 15,
    ["UI_Roguelike_Talent_ActivateManyContent"] = 15,
    ["UI_Roguelike_Talent_ActivateAllTitle"] = 15,
    ["UI_Roguelike_Talent_ActivateAllContent"] = 15,
    ["UI_Roguelike_Talent_ActivateCost"] = 15,
    ["UI_Roguelike_Talent_ActivateTalentNum"] = 15,
    ["UI_Roguelike_Talent_UnlockSkillNum"] = 15,
    ["UI_Roguelike_Talent_UpgradeSkillNum"] = 15,
    ["RoguelikeTalent_Result_HadUnlock_ALL"] = 15,
    ["Set_EditKey_FPSBrGame_Vehicle"] = 15,
    ["HOK_FirstSoldierComeOn5"] = 16,
    ["HOK_FirstSoldierComeOn"] = 16,
    ["HOK_ArtilleryComeOn"] = 16,
    ["HOK_VanguardComeOn"] = 16,
    ["HOK_SuperSelfComeOn"] = 16,
    ["HOK_SuperEnemyComeOn"] = 16,
    ["HOK_MasterComeOn"] = 16,
    ["HOK_TyrantEnemyComeOn"] = 16,
    ["HOK_TyrantAndMasterEnemyComeOn"] = 16,
    ["HOK_SelfTowerOver"] = 16,
    ["HOK_EnemyTowerOver"] = 16,
    ["HOK_TowerProctedOver"] = 16,
    ["HOK_TotalKill_6"] = 16,
    ["HOK_TotalKill_7"] = 16,
    ["HOK_KillTarget_5"] = 16,
    ["Hok_Intro_1"] = 16,
    ["Hok_Intro_1_Text"] = 16,
    ["Hok_Intro_2"] = 16,
    ["Hok_Intro_2_1"] = 16,
    ["Hok_Intro_2_1_Text"] = 16,
    ["Hok_Intro_2_2"] = 16,
    ["Hok_Intro_2_2_Text"] = 16,
    ["Hok_Intro_2_3"] = 16,
    ["Hok_Intro_2_3_Text"] = 16,
    ["Hok_Intro_3"] = 16,
    ["Hok_Intro_3_Text"] = 16,
    ["Hok_Intro_4"] = 16,
    ["Hok_Intro_4_Text"] = 16,
    ["Hok_Intro_5"] = 16,
    ["Hok_Intro_5_Text"] = 16,
    ["HOK_Guide_DestroyEnemyCrystal"] = 16,
    ["HOK_Shop_RefreshHint"] = 16,
    ["HOK_Setting_Open"] = 16,
    ["HOK_Setting_Close"] = 16,
    ["HOK_Setting_Tip_HeadIcon"] = 16,
    ["HOK_Setting_Tip_BuyCard"] = 16,
    ["HOK_Setting_Tip_AddSkill"] = 16,
    ["HOK_Setting_Tip_Surrender"] = 16,
    ["HOK_Spectator_RoleSelect_Tips"] = 16,
    ["UI_HOK_Surrender_StartSurrenderBtnName"] = 16,
    ["UI_HOK_Surrender_EncourageText1"] = 16,
    ["UI_HOK_Surrender_EncourageText2"] = 16,
    ["UI_HOK_Surrender_EncourageText3"] = 16,
    ["UI_HOK_Surrender_EncourageText4"] = 16,
    ["Hok_Rank_Not_Unlock"] = 16,
    ["UI_HOK_SelectRole_LackHeroPosition_1"] = 16,
    ["UI_HOK_SelectRole_LackHeroPosition_2"] = 16,
    ["UI_HOK_SelectRole_LackHeroPosition_3"] = 16,
    ["UI_HOK_SelectRole_LackHeroPosition_4"] = 16,
    ["UI_HOK_SelectRole_LackHeroPosition_5"] = 16,
    ["UI_HOK_SelectRole_ShuntSelectText_1"] = 16,
    ["UI_HOK_SelectRole_ShuntSelectText_2"] = 16,
    ["UI_HOK_SelectRole_ShuntSelectText_3"] = 16,
    ["UI_HOK_SelectRole_ShuntSelectText_4"] = 16,
    ["UI_HOK_SelectRole_ShuntSelectText_5"] = 16,
    ["UI_HOK_SelectRole_HeroPositionName_1"] = 16,
    ["UI_HOK_SelectRole_HeroPositionName_2"] = 16,
    ["UI_HOK_SelectRole_HeroPositionName_3"] = 16,
    ["UI_HOK_SelectRole_HeroPositionName_4"] = 16,
    ["UI_HOK_SelectRole_HeroPositionName_5"] = 16,
    ["UI_HOK_SelectRole_HeroClassName_1"] = 16,
    ["UI_HOK_SelectRole_HeroClassName_2"] = 16,
    ["UI_HOK_SelectRole_HeroClassName_3"] = 16,
    ["UI_HOK_SelectRole_HeroClassName_4"] = 16,
    ["UI_HOK_SelectRole_HeroClassName_5"] = 16,
    ["UI_HOK_SelectRole_HeroClassName_6"] = 16,
    ["HOK_TyrantAndMasterEnhance"] = 16,
    ["UI_InLevel_Hok_Reputation_MyScore_LineOne"] = 16,
    ["UI_InLevel_Hok_Reputation_MyScore_LineTwo"] = 16,
    ["UI_InLevel_Hok_Reputation_OffenseRule_LineOne"] = 16,
    ["UI_InLevel_Hok_Reputation_OffenseRule_LineTwo"] = 16,
    ["UI_InLevel_Hok_Reputation_OffenseRule_LineThree"] = 16,
    ["UI_InLevel_Hok_Reputation_CreditRule_LineOne"] = 16,
    ["UI_InLevel_Hok_Reputation_CreditRule_LineTwo"] = 16,
    ["UI_InLevel_Hok_Reputation_CreditRule_LineThree"] = 16,
    ["UI_InLevel_Hok_Main_NoCompleteWithReputation_Box"] = 16,
    ["HOK_TransDoor_Description_Text"] = 16,
    ["HOK_TowerProctedOver_ArtilleryComeOn"] = 16,
    ["UI_HOK_Setting_SurrenderDisabled"] = 16,
    ["HOK_ShadowMasterComeOn"] = 16,
    ["HOK_ShadowTyrantEnemyComeOn"] = 16,
    ["HOK_ShowDefenceTowerRemind_Tips"] = 16,
    ["Set_EditKey_HOK"] = 16,
    ["HOK_Strategy1"] = 16,
    ["HOK_Strategy2"] = 16,
    ["HOK_Strategy3"] = 16,
    ["HOK_Strategy4"] = 16,
    ["HOK_Strategy5"] = 16,
    ["HOK_HomePortalstart"] = 16,
    ["InLevel_HOK_GameResult_Loser"] = 16,
    ["HOK_Player_Dressing"] = 16,
    ["HOK_Player_Undress"] = 16,
    ["HOK_BattleReport_SkillNormal"] = 16,
    ["HOK_BattleReport_SkillOne"] = 16,
    ["HOK_BattleReport_SkillTwo"] = 16,
    ["HOK_BattleReport_SkillThree"] = 16,
    ["HOK_BattleReport_SkillFour"] = 16,
    ["HOK_BattleReport_SkillSummoner"] = 16,
    ["HOK_SpectatorVanguardBlueComeOn"] = 16,
    ["HOK_SpectatorVanguardRedComeOn"] = 16,
    ["HOK_SpectatorSuperBlueComeOn"] = 17,
    ["HOK_SpectatorSuperRedComeOn"] = 17,
    ["HOK_Setting_Tip_PIP"] = 17,
    ["HOK_AssistHint1_Exp"] = 17,
    ["HOK_AssistHint2_Gold"] = 17,
    ["HOK_AssistHint3_Other"] = 17,
    ["HOK_BattleReport_Passive"] = 17,
    ["HOK_PIPHint_TowerDefense"] = 17,
    ["HOK_PIPHint_LongRefresh"] = 17,
    ["HOK_PIPHint_LongUpgrade"] = 17,
    ["HOK_BattleReport_Extra"] = 17,
    ["UI_HOK_ShopDrawRound"] = 17,
    ["HOK_GoldRebirth_Hint"] = 17,
    ["UI_HOK_ShopCardMax"] = 17,
    ["HOK_Mount_Born_Self"] = 17,
    ["HOK_Mount_Born_Enemy"] = 17,
    ["HOK_AssistHint_Chat"] = 17,
    ["HOK_PropCfgNextStage"] = 17,
    ["HOK_Setting_Tip_CardAttrPanel"] = 17,
    ["HOK_HomeoutLaunchDevicestart"] = 17,
    ["HOK_Mount_BeUsing_First_Friend"] = 17,
    ["HOK_Mount_BeUsing_First_Enemy"] = 17,
    ["HOK_Mount_BeUsing_Die_Friend"] = 17,
    ["HOK_Mount_BeUsing_Die_Enemy"] = 17,
    ["HOK_AreaMidleLaunchDevicedis"] = 17,
    ["HOK_Tip_NewbieGuide_AfterTutorialFreeHero"] = 17,
    ["HOK_BattleReport_Card"] = 17,
    ["HOK_HongSun_Enhance"] = 17,
    ["HOK_HongSun_Mutation"] = 17,
    ["HOK_BattleReport_Rebound"] = 17,
    ["HOK_HongSun_Enhance_ComeOn"] = 17,
    ["HOK_KongJianZhiLing_Enhance_ComeOn"] = 17,
    ["HOK_HongSun_Mutation_ComeOn"] = 17,
    ["HOK_KongJianZhiLing_Mutation_ComeOn"] = 17,
    ["LevelScene_60050_1"] = 18,
    ["LevelScene_60050_2"] = 18,
    ["LevelScene_60050_3"] = 18,
    ["LevelScene_60050_4"] = 18,
    ["LevelScene_60050_5"] = 18,
    ["LevelScene_60053_1"] = 18,
    ["LevelScene_60053_2"] = 18,
    ["LevelScene_60053_3"] = 18,
    ["LevelScene_60053_4"] = 18,
    ["LevelScene_60133_1"] = 18,
    ["LevelScene_60133_2"] = 18,
    ["LevelScene_60133_3"] = 18,
    ["LevelScene_70000_1"] = 18,
    ["LevelScene_70002_1"] = 18,
    ["LevelScene_70002_2"] = 18,
    ["LevelScene_70002_3"] = 18,
    ["LevelScene_70003_1"] = 18,
    ["LevelScene_70016_1"] = 18,
    ["LevelScene_70017_1"] = 18,
    ["LevelScene_70018_1"] = 18,
    ["LevelScene_70018_2"] = 18,
    ["LevelScene_70018_3"] = 18,
    ["LevelScene_70022New_1"] = 18,
    ["LevelScene_70022New_2"] = 18,
    ["LevelScene_70034_1"] = 18,
    ["LevelScene_70038_1"] = 18,
    ["LevelScene_70038_2"] = 18,
    ["LevelScene_70038_3"] = 18,
    ["LevelScene_70038_4"] = 18,
    ["LevelScene_70038_5"] = 18,
    ["LevelScene_70038_6"] = 18,
    ["LevelScene_70038_7"] = 18,
    ["LevelScene_70038_8"] = 18,
    ["LevelScene_70038_9"] = 18,
    ["LevelScene_70038_10"] = 18,
    ["LevelScene_70059_1"] = 18,
    ["LevelScene_70059_2"] = 18,
    ["LevelScene_70059_3"] = 18,
    ["LevelScene_70059_4"] = 18,
    ["LevelScene_70060_1"] = 18,
    ["LevelScene_70060_2"] = 18,
    ["LevelScene_70060_3"] = 18,
    ["LevelScene_70064_1"] = 18,
    ["LevelScene_70064_2"] = 18,
    ["LevelScene_80001_1"] = 18,
    ["LevelScene_80001_2"] = 18,
    ["LevelScene_80001_3"] = 18,
    ["LevelScene_80003_1"] = 18,
    ["LevelScene_80003_2"] = 18,
    ["LevelScene_80003_3"] = 18,
    ["LevelScene_80004_1"] = 18,
    ["LevelScene_80004_2"] = 18,
    ["LevelScene_80004_3"] = 18,
    ["LevelScene_80005_1"] = 18,
    ["LevelScene_80005_2"] = 18,
    ["LevelScene_80005_3"] = 18,
    ["LevelScene_80006_1"] = 18,
    ["LevelScene_80006_2"] = 18,
    ["LevelScene_80006_3"] = 18,
    ["LevelScene_80009_1"] = 18,
    ["LevelScene_80009_2"] = 18,
    ["LevelScene_80009_3"] = 18,
    ["LevelScene_85003_1"] = 18,
    ["LevelScene_85003_2"] = 18,
    ["LevelScene_85003_3"] = 18,
    ["LevelScene_85005_1"] = 18,
    ["LevelScene_85005_2"] = 18,
    ["LevelScene_85005_3"] = 18,
    ["LevelScene_70023_1"] = 18,
    ["LevelScene_70023_2"] = 18,
    ["LevelScene_70023_3"] = 18,
    ["LevelScene_70023_4"] = 18,
    ["LevelScene_85013_1"] = 18,
    ["LevelScene_85013_2"] = 18,
    ["LevelScene_70062_1"] = 18,
    ["LevelScene_70062_2"] = 18,
    ["LevelScene_70062_3"] = 18,
    ["LevelScene_70062_4"] = 18,
    ["LevelScene_70062_5"] = 18,
    ["LevelScene_70037_1"] = 18,
    ["LevelScene_70037_2"] = 18,
    ["LevelScene_70037_3"] = 18,
    ["LevelScene_70037_4"] = 18,
    ["LevelScene_60057_1"] = 18,
    ["LevelScene_70013_1"] = 18,
    ["LevelScene_70013_2"] = 18,
    ["LevelScene_70065_1"] = 18,
    ["LevelScene_70066_1"] = 18,
    ["LevelScene_70066_2"] = 18,
    ["LevelScene_70066_3"] = 18,
    ["LevelScene_70067_1"] = 18,
    ["LevelScene_70068_1"] = 18,
    ["LevelScene_70068_2"] = 18,
    ["LevelScene_70068_3"] = 18,
    ["LevelScene_70069_1"] = 18,
    ["LevelScene_70069_2"] = 18,
    ["LevelScene_70075_1"] = 18,
    ["LevelScene_70075_2"] = 18,
    ["LevelScene_70075_3"] = 18,
    ["LevelScene_60055_1"] = 18,
    ["LevelScene_70081_0"] = 19,
    ["LevelScene_70081_1"] = 19,
    ["LevelScene_70081_2"] = 19,
    ["LevelScene_70081_3"] = 19,
    ["LevelScene_70083_0"] = 19,
    ["LevelScene_80011_0"] = 19,
    ["LevelScene_70088_1"] = 19,
    ["LevelScene_70087_0"] = 19,
    ["LevelScene_70087_1"] = 19,
    ["LevelScene_70090_0"] = 19,
    ["LevelScene_70090_1"] = 19,
    ["LevelScene_70093_1"] = 19,
    ["LevelScene_70085_1"] = 19,
    ["LevelScene_80017_0"] = 19,
    ["LevelScene_80017_1"] = 19,
    ["LevelScene_80017_2"] = 19,
    ["LevelScene_80017_3"] = 19,
    ["LevelScene_80017_4"] = 19,
    ["LevelScene_70096_1"] = 19,
    ["LevelScene_70096_2"] = 19,
    ["LevelScene_70094_0"] = 19,
    ["LevelScene_70094_1"] = 19,
    ["LevelScene_70094_2"] = 19,
    ["LevelScene_70105_0"] = 19,
    ["LevelScene_70105_1"] = 19,
    ["LevelScene_70105_2"] = 19,
    ["LevelScene_70105_3"] = 19,
    ["LevelScene_70105_4"] = 19,
    ["LevelScene_70105_5"] = 19,
    ["LevelScene_70105_6"] = 19,
    ["LevelScene_70105_7"] = 19,
    ["LevelScene_70105_8"] = 19,
    ["LevelScene_70106_0"] = 19,
    ["LevelScene_70106_1"] = 19,
    ["LevelScene_70106_2"] = 19,
    ["LevelScene_70106_3"] = 19,
    ["LevelScene_70106_4"] = 19,
    ["LevelScene_70086_0"] = 19,
    ["LevelScene_70086_1"] = 19,
    ["LevelScene_70086_2"] = 19,
    ["LevelScene_70086_3"] = 19,
    ["LevelScene_70104_0"] = 19,
    ["LevelScene_70104_1"] = 19,
    ["LevelScene_80018_0"] = 19,
    ["LevelScene_70110_1"] = 19,
    ["LevelScene_70113_0"] = 19,
    ["LevelScene_70113_1"] = 19,
    ["LevelScene_70113_2"] = 19,
    ["LevelScene_70113_3"] = 19,
    ["LevelScene_70113_4"] = 19,
    ["LevelScene_70112_1"] = 19,
    ["LevelScene_70112_2"] = 19,
    ["LevelScene_70112_3"] = 19,
    ["LevelScene_70119_0"] = 19,
    ["LevelScene_70119_1"] = 19,
    ["LevelScene_70119_2"] = 19,
    ["LevelScene_70120_0"] = 19,
    ["LevelScene_80020_0"] = 19,
    ["LevelScene_80020_1"] = 19,
    ["LevelScene_80020_2"] = 19,
    ["LevelScene_80020_3"] = 19,
    ["LevelScene_60232_0"] = 19,
    ["LevelScene_70111_0"] = 19,
    ["LevelScene_70111_1"] = 19,
    ["LevelScene_70222_0"] = 19,
    ["LevelScene_70232_0"] = 19,
    ["LevelScene_70232_1"] = 19,
    ["LevelScene_70234_0"] = 19,
    ["LevelScene_70234_1"] = 19,
    ["LevelScene_70234_2"] = 19,
    ["LevelScene_70234_3"] = 19,
    ["LevelScene_70235_0"] = 19,
    ["LevelScene_76084_1"] = 19,
    ["LevelScene_76086_1"] = 19,
    ["LevelScene_66017_1"] = 19,
    ["LevelScene_66019_1"] = 19,
    ["LevelScene_66019_4"] = 19,
    ["LevelScene_66019_5"] = 19,
    ["LevelScene_66019_6"] = 19,
    ["LevelScene_66019_7"] = 19,
    ["LevelSecne_80022_1"] = 19,
    ["LevelSecne_80022_2"] = 19,
    ["LevelScene_66026_1"] = 19,
    ["LevelScene_70239_0"] = 19,
    ["LevelScene_70240_0"] = 19,
    ["LevelScene_70240_1"] = 19,
    ["LevelScene_70240_2"] = 19,
    ["LevelScene_70241_0"] = 19,
    ["LevelScene_70243_0"] = 19,
    ["LevelScene_80023_0"] = 19,
    ["LevelScene_70245_1"] = 19,
    ["LevelScene_70245_2"] = 20,
    ["LevelScene_70247_0"] = 19,
    ["LevelScene_70247_1"] = 19,
    ["LevelScene_70247_2"] = 19,
    ["LevelScene_70248_0"] = 20,
    ["LevelScene_70249_0"] = 19,
    ["LevelScene_70250_0"] = 20,
    ["LevelScene_70250_1"] = 20,
    ["LevelScene_70250_2"] = 20,
    ["LevelScene_70254_0"] = 20,
    ["LevelScene_70254_1"] = 20,
    ["LevelScene_70254_2"] = 20,
    ["LevelScene_70254_3"] = 20,
    ["LevelScene_70255_0"] = 19,
    ["LevelScene_70255_1"] = 19,
    ["LevelScene_70255_2"] = 19,
    ["LevelScene_70255_3"] = 20,
    ["LevelScene_70255_4"] = 20,
    ["LevelScene_70255_5"] = 20,
    ["LevelScene_70256_1"] = 20,
    ["LevelScene_70256_2"] = 20,
    ["LevelScene_70257_0"] = 20,
    ["LevelScene_70258_0"] = 20,
    ["LevelScene_70259_0"] = 20,
    ["LevelScene_70259_1"] = 20,
    ["LevelScene_60269_0"] = 20,
    ["LevelScene_60269_1"] = 20,
    ["LevelScene_70261_0"] = 20,
    ["LevelScene_70261_1"] = 20,
    ["Live_Confirm"] = 21,
    ["Live_Confirm2"] = 21,
    ["Live_GiveLive"] = 21,
    ["Live_GameName"] = 21,
    ["Live_OpenWxLiveWebTips"] = 21,
    ["Live_LiveLinkStart"] = 21,
    ["GM_level_Info"] = 21,
    ["GM_DS_Version"] = 21,
    ["Friend_Unbind_Intimate"] = 21,
    ["Friend_Confirm_Unbind"] = 21,
    ["Friend_SendApply"] = 21,
    ["Friend_Added"] = 21,
    ["Rank_AllServer"] = 21,
    ["Rank_Friend"] = 21,
    ["Rank_Self"] = 21,
    ["Rank_ChangeAreaSuccess"] = 21,
    ["Rank_ChangeInfoMax"] = 21,
    ["Rank_GpsIsClose"] = 21,
    ["Rank_HidePlayerInfo"] = 21,
    ["Rank_MapName_OneParam"] = 21,
    ["Model_IsClose"] = 21,
    ["Model_RewardTips"] = 21,
    ["Model_CurrPlayIsLock"] = 21,
    ["Model_CurrTeam_PlayerCountNotEnough"] = 21,
    ["Model_CurrTeam_NotOne"] = 21,
    ["Model_CurrTeam_NotTwo"] = 21,
    ["Model_CurrTeam_NotFour"] = 21,
    ["Activity_Month_CollectionItemed"] = 21,
    ["Activity_Month_CollectionItemed1"] = 21,
    ["Activity_Month_DrawPoolTitle"] = 21,
    ["Activith_Common_DrawCountIsMax"] = 21,
    ["Activith_Common_DayDrawCountIsMax"] = 21,
    ["Common_HideInfo_Name"] = 21,
    ["Common_HideInfo_ShortName"] = 21,
    ["Common_PlayerDataError"] = 21,
    ["Common_EveryDayMax_OneParam"] = 21,
    ["Common_ItemCount_NotEnough"] = 21,
    ["DrawReward_DrawBtnNoTimes"] = 21,
    ["DrawReward_DrawBtnNoEnoughDailyTimes"] = 21,
    ["DrawReward_DrawBtnNoDailyTimes"] = 21,
    ["DrawReward_MallViewUnshelve"] = 21,
    ["DrawReward_MallViewToUnshelve"] = 21,
    ["DrawReward_MallViewNoExchangeCount"] = 21,
    ["DrawReward_MallViewOwnedTurnInto"] = 21,
    ["DrawReward_MallConfirm"] = 21,
    ["DrawReward_AccessoriesViewNoTimes"] = 21,
    ["DrawReward_AccessoriesViewUpdateTime"] = 21,
    ["DrawReward_CardViewStepForward"] = 21,
    ["DrawReward_CardViewUpdateTime"] = 21,
    ["DrawReward_MainViewUpdateTime"] = 21,
    ["DrawReward_MultiRaffleUpdateTime"] = 21,
    ["DrawReward_MultiRaffleDrawOnce"] = 21,
    ["DrawReward_MultiRaffleDrawRound"] = 21,
    ["DrawReward_MultiRaffleBackTo"] = 21,
    ["DrawReward_MultiRaffleNoTimes"] = 21,
    ["DrawReward_SeasonViewBigReward"] = 21,
    ["DrawReward_SeasonViewUpdateTime"] = 21,
    ["DrawReward_SeasonViewButterflyDraw"] = 21,
    ["DrawReward_DrawAgain"] = 21,
    ["DrawReward_SocialViewFreeNum"] = 21,
    ["DrawReward_SocialViewFreeOpen"] = 21,
    ["DrawReward_SocialViewOpen"] = 21,
    ["DrawReward_SocialViewPayConfirm"] = 21,
    ["DrawReward_SocialViewDrawConfirm"] = 21,
    ["DrawReward_UpdateTime"] = 21,
    ["DrawReward_DayHour"] = 21,
    ["DrawReward_HourMinute"] = 21,
    ["DrawReward_CoinSupplyVoucher"] = 21,
    ["DrawReward_ActivationViewFreeCountDown"] = 21,
    ["DrawReward_DontRemindAnymore"] = 21,
    ["DrawReward_ExchangeBoxCurrentCurrency"] = 21,
    ["DrawReward_AutoExchangeFailed"] = 21,
    ["DrawReward_WrongVersion"] = 21,
    ["DrawReward_SocialViewDiscountNum"] = 21,
    ["TokenView_Invite"] = 21,
    ["TokenView_Assist"] = 21,
    ["TokenAssistConfirm"] = 21,
    ["TokenAssistNum"] = 21,
    ["TokenAssistSuccess"] = 21,
    ["TokenAssistNoNum"] = 21,
    ["TokenFailToCreate"] = 21,
    ["TokenNoInput"] = 21,
    ["UI_Setting_PrivacyAuthority_GotoChangeNameTitle"] = 21,
    ["UI_Setting_PrivacyAuthority_SearchInfoTitle"] = 21,
    ["UI_Setting_Frame_LogoutTip"] = 21,
    ["UI_Setting_Frame_Title"] = 21,
    ["UI_Setting_Voice_Title"] = 21,
    ["UI_Setting_Privacy_Title"] = 21,
    ["UI_Setting_DownloadManagement_Title"] = 21,
    ["UI_Recharge_Vip_Tip"] = 21,
    ["UI_Recharge_SeasonRecharge_IsOpening"] = 21,
    ["UI_Recharge_SeasonRecharge_BuyCountTime"] = 21,
    ["UI_Recharge_SeasonRecharge_GetCountTime"] = 21,
    ["UI_Recharge_SeasonRecharge_BuyAgainTip_Small"] = 21,
    ["UI_Recharge_SeasonRecharge_BuyAgainTip_Large"] = 21,
    ["UI_Recharge_Recharge_LevelupCanGetTip"] = 21,
    ["UI_Recharge_Recharge_MaxLevelGetTip"] = 21,
    ["UI_Recharge_Recharge_LevelGetTip"] = 21,
    ["UI_Recharge_Recharge_MaxLevelTitle"] = 21,
    ["UI_Recharge_Recharge_LevelUpTip"] = 21,
    ["UI_Recharge_MonthCard_BuyRewardTip"] = 22,
    ["Common_BuyConfirm"] = 22,
    ["UI_Recharge_MonthCard_BuyMonthCardConfirmDesc"] = 22,
    ["Common_CutDesc"] = 22,
    ["UI_Recharge_MonthCard_SeasonLimitDesc"] = 22,
    ["UI_Recharge_MonthCard_WeekLimitDesc"] = 22,
    ["UI_Recharge_MonthCard_BuyAgainDesc"] = 22,
    ["UI_Recharge_MonthCard_BuyDesc"] = 22,
    ["Common_TakeEffect"] = 22,
    ["UI_Recharge_Recharge_Title"] = 22,
    ["UI_Recharge_Vip_Title"] = 22,
    ["UI_Recharge_CutGift_Title"] = 22,
    ["UI_Recharge_FirstChargeMain_Title"] = 22,
    ["UI_Recharge_SeasonRecharge_Title"] = 22,
    ["UI_Recharge_MonthCard_Title"] = 22,
    ["UI_Recharge_UI_ChargeRebate_Main_Title"] = 22,
    ["UI_Recharge_ChargeRebate_OpenBetaRebate_Title"] = 22,
    ["Common_RMBDesc"] = 22,
    ["Mall_Limit_Buy"] = 22,
    ["Mall_QualitySort"] = 22,
    ["Mall_TimeSort"] = 22,
    ["Mall_BuyFailed"] = 22,
    ["Mall_Buy"] = 22,
    ["Mall_BuyConfirm"] = 22,
    ["Mall_NotFindAny_AndChange"] = 22,
    ["Mall_NotHasBaseColor"] = 22,
    ["Mall_IsBuy"] = 22,
    ["Mall_ShopAlreadyHas_NotBuy"] = 22,
    ["Mall_CurrencyNotEnough"] = 22,
    ["Task_Open"] = 22,
    ["Task_NotCompleted"] = 22,
    ["Task_AfterOpen"] = 22,
    ["Task_ActivityHasEnded"] = 22,
    ["Task_ActivityEnded_ComeBackNextTime"] = 22,
    ["Task_NextRankReward"] = 22,
    ["Task_DistanceNewbieEnded"] = 22,
    ["Task_NewbieHasEnded"] = 22,
    ["Task_ImpactTask"] = 22,
    ["Task_ExperienceTask"] = 22,
    ["Task_AchieveRankGetSkin"] = 22,
    ["Task_EndOfCurrentSeason"] = 22,
    ["Task_UGCTaskEnded"] = 22,
    ["Task_AwardReceived"] = 22,
    ["CustomRoom_InviteMsg"] = 22,
    ["CustomRoom_ApplyToChangeSeatMsg"] = 22,
    ["CustomRoom_RejectInviteInTeam"] = 22,
    ["CustomRoom_FullyTip"] = 22,
    ["CustomRoom_InviteCDTip"] = 22,
    ["CustomRoom_NotInviteByState"] = 22,
    ["CustomRoom_InviteSendTip"] = 22,
    ["CustomRoom_WaitPlayer"] = 22,
    ["CustomRoom_AllMode"] = 22,
    ["CustomRoom_AllMatch"] = 22,
    ["CustomRoom_RoomIDHint"] = 22,
    ["CustomRoom_PasswordHint"] = 22,
    ["CustomRoom_RoomName"] = 22,
    ["CustomRoom_JoinRoomChatTip"] = 22,
    ["CustomRoom_LeaveRoomChatTip"] = 22,
    ["CustomRoom_ChaterName"] = 22,
    ["CustomRoom_NoVoicePermission"] = 22,
    ["CustomRoom_NoPassword"] = 22,
    ["CustomRoom_LeaveRoomTip"] = 22,
    ["CustomRoom_StartRoomMinCountTip"] = 22,
    ["CustomRoom_RoomIDCopiedTip"] = 22,
    ["CustomRoom_RobotName"] = 22,
    ["CustomRoom_KickOut"] = 22,
    ["CustomRoom_KickOutCancel"] = 22,
    ["CustomRoom_RobotCover"] = 22,
    ["CustomRoom_RobotRemove"] = 22,
    ["CustomRoom_Invite"] = 22,
    ["CustomRoom_Start"] = 22,
    ["CustomRoom_WaitStart"] = 22,
    ["CustomRoom_KickOutOne"] = 22,
    ["CustomRoom_KickNoInvite"] = 22,
    ["UGCRoom_PreparationReminderAllPrepared"] = 22,
    ["UGCRoom_PreparationReminderCD"] = 22,
    ["UGCRoom_WaitReady"] = 22,
    ["UGCRoom_CancelReady"] = 22,
    ["UGCRoom_RemindReady"] = 22,
    ["Player_BanTip"] = 22,
    ["Player_BanTipWithReason"] = 22,
    ["Player_GetInfoError"] = 22,
    ["Player_HideProfileTip"] = 22,
    ["Player_ProfileTabName"] = 22,
    ["Player_UGCTabName"] = 22,
    ["Player_HistoryTabName"] = 22,
    ["Player_LevelIllustrationTabName"] = 22,
    ["Player_SuitTabName"] = 22,
    ["Player_AchievementTabName"] = 22,
    ["Player_ShareState"] = 22,
    ["Player_NoStateTip"] = 22,
    ["Player_NoTitleTip"] = 22,
    ["Player_NickName"] = 22,
    ["Player_Gender"] = 22,
    ["Player_HeadBG"] = 22,
    ["Player_NickNameBG"] = 22,
    ["Player_Title"] = 22,
    ["Player_RankShow"] = 22,
    ["Player_ChangeGenderTotalCDTip"] = 22,
    ["Player_ChangeGenderCurrentCDTip"] = 22,
    ["Player_ChangeGenderSuccess"] = 23,
    ["Player_ChangerGenderTip"] = 23,
    ["Player_NoGenderSelectTip"] = 23,
    ["Player_GenderChangedCDTip"] = 23,
    ["Player_FullyLabelTip"] = 23,
    ["Player_ChangeNickNameTip"] = 23,
    ["Player_ChangeNickNameSuccess"] = 23,
    ["Player_NoChangeNameCard"] = 23,
    ["Player_SuitAll"] = 23,
    ["Player_SuitTheme"] = 23,
    ["Player_SuitSeason"] = 23,
    ["Player_SuitCollection"] = 23,
    ["Player_HaveSuitTip"] = 23,
    ["Currency_NotEnoughTip"] = 23,
    ["Currency_ItemInfoError"] = 23,
    ["Currency_BuySuccessTip"] = 23,
    ["Currency_BuyFailTip"] = 23,
    ["UI_Login_AccountDiffDesc"] = 23,
    ["Common_Warning"] = 23,
    ["friend_list"] = 23,
    ["friend_islands"] = 23,
    ["friend_nearby"] = 23,
    ["friend_search"] = 23,
    ["friend_unknown"] = 23,
    ["friend_minute"] = 23,
    ["friend_hour"] = 23,
    ["friend_day"] = 23,
    ["friend_month"] = 23,
    ["friend_RequestSent"] = 23,
    ["friend_ServerError"] = 23,
    ["friend_SuccessfulRelationship"] = 23,
    ["friend_BuildRelationships"] = 23,
    ["friend_MaximumLimit"] = 23,
    ["friend_OppositeSex"] = 23,
    ["friend_IntimacyRequestSent"] = 23,
    ["friend_RelationshipConversionRequestSent"] = 23,
    ["friend_QuantityRemaining"] = 23,
    ["friend_applyforintimacy"] = 23,
    ["friend_conversionrelationship"] = 23,
    ["friend_Applicationforterminationofrelationship"] = 23,
    ["friend_Intimaterelationshiprights"] = 23,
    ["friend_Intimacyrewards"] = 23,
    ["friend_meter"] = 23,
    ["friend_alreadydeliveredcotton"] = 23,
    ["friend_cottongifted"] = 23,
    ["friend_brother"] = 23,
    ["friend_squarePlayer"] = 23,
    ["friend_clickRecruitment"] = 23,
    ["friend_goAdd"] = 23,
    ["friend_noPlayers"] = 23,
    ["friend_noPlayersNearby"] = 23,
    ["friend_development"] = 23,
    ["friend_intimacy"] = 23,
    ["friend_notFound"] = 23,
    ["friend_enterSearch"] = 23,
    ["friend_nodata"] = 23,
    ["friend_nonHomeowner"] = 23,
    ["friend_InTheGame"] = 23,
    ["friend_pullToHim"] = 23,
    ["friend_increaseIntimacy"] = 23,
    ["friend_RepeatApplication"] = 23,
    ["Common_MCL_DailyLimit"] = 23,
    ["Common_MCL_MonthlyLimit"] = 23,
    ["Common_MCL_WeeklyLimit"] = 23,
    ["Common_MCL_YearlyLimit"] = 23,
    ["Common_MCL_LifeLongLimit"] = 23,
    ["Common_MCL_SeasonLimit"] = 23,
    ["Common_MCL_DailyLimit2"] = 23,
    ["Common_MCL_MonthlyLimit2"] = 23,
    ["Common_MCL_WeeklyLimit2"] = 23,
    ["Common_MCL_YearlyLimit2"] = 23,
    ["Common_MCL_LifeLongLimit2"] = 23,
    ["Common_MCL_SeasonLimit2"] = 23,
    ["InLevel_QST_Champion"] = 23,
    ["InLevel_QST_Rank"] = 23,
    ["InLevel_QST_LevelPass"] = 23,
    ["InLevel_QST_FinalLevelRank"] = 23,
    ["InLevel_QST_FirstDegreeUp"] = 23,
    ["InLevel_QST_LevelPerformance"] = 23,
    ["InLevel_QST_FriendCooperation"] = 23,
    ["InLevel_QST_DailyFirstWin"] = 23,
    ["InLevel_QST_ProtectedScoreAdditional"] = 23,
    ["InLevel_QST_ProtectedScoreProtected"] = 23,
    ["InLevel_QST_ReturingCooperation"] = 23,
    ["InLevel_QST_ReturingPrivilege"] = 23,
    ["InLevel_QST_TeamRankActivityMultiScore"] = 23,
    ["Bag_UseItemSuccess"] = 23,
    ["Bag_UseItemFail"] = 23,
    ["Bag_DelItemSuccess"] = 23,
    ["Bag_DelItemFail"] = 23,
    ["Bag_SelItemSuccess"] = 23,
    ["Bag_SelItemFail"] = 23,
    ["Bag_ItemExpire"] = 23,
    ["Bag_SlotSaveSuccess"] = 23,
    ["Bag_SlotSaveSuccess2"] = 23,
    ["Bag_EmotionSaveSuccess"] = 23,
    ["Team_MemberFull"] = 23,
    ["Team_InviteSendFail"] = 23,
    ["Team_InviteCanNotSend"] = 23,
    ["Team_PlayerInTeam"] = 23,
    ["Team_InviteSendFinish"] = 24,
    ["Common_NetConnectError"] = 24,
    ["Team_JoinReqSendFinish"] = 24,
    ["Team_MemberFull2"] = 24,
    ["Team_RecruitCD"] = 24,
    ["Team_RecruitSendSuccess"] = 24,
    ["Team_CanNotJoinTeam"] = 24,
    ["Team_JoinTeamSuccess"] = 24,
    ["Team_InviteJoinTeam"] = 24,
    ["Team_ReqJoinTeam"] = 24,
    ["Team_CanNotAcceptInvite"] = 24,
    ["Team_CanNotAddMember"] = 24,
    ["Team_DownloadInWifi"] = 24,
    ["Team_DownloadInNet"] = 24,
    ["Team_DownloadInWifi2"] = 24,
    ["Team_DownloadInNet2"] = 24,
    ["Team_WaitMemberDownload"] = 24,
    ["Team_SelfCancelReady"] = 24,
    ["Team_PlayerCancelReady"] = 24,
    ["Team_CanNotCancelMatch"] = 24,
    ["Pak_XSDownloadTips"] = 24,
    ["Pak_PLDownloadTips"] = 24,
    ["Pak_RemoveCleanupTip1"] = 24,
    ["Pak_RemoveCleanupTip2"] = 24,
    ["Pak_RemoveCleanupGeneralTip"] = 24,
    ["Pak_StartDownload"] = 24,
    ["Pak_StopDownload"] = 24,
    ["Pak_EndDownload"] = 24,
    ["Pak_DownloadTip"] = 24,
    ["Mode_ModeSelectNoOpen"] = 24,
    ["Mode_CanNotChangeMode"] = 24,
    ["Mode_CanNotChangeMode2"] = 24,
    ["Mode_HasMemberInLevel"] = 24,
    ["Mode_MatchTypeNoOpen"] = 24,
    ["Common_MoneyNotEnough"] = 24,
    ["Common_MoneyNotEnough2"] = 24,
    ["Guide_CreateRole_Name1"] = 24,
    ["Guide_CreateRole_Name2"] = 24,
    ["Guide_CreateRole_Name3"] = 24,
    ["Guide_CreateRole_Name4"] = 24,
    ["Guide_CreateRole_Name5"] = 24,
    ["Guide_CreateRole_Name6"] = 24,
    ["Guide_CreateRole_Name7"] = 24,
    ["Guide_CreateRole_Name8"] = 24,
    ["Guide_CreateRole_Name9"] = 24,
    ["Guide_CreateRole_Name10"] = 24,
    ["Guide_CreateRole_Name11"] = 24,
    ["Guide_CreateRole_Name12"] = 24,
    ["Guide_CreateRole_Name13"] = 24,
    ["Guide_CreateRole_Name14"] = 24,
    ["Guide_CreateRole_Name15"] = 24,
    ["Guide_CreateRole_Name16"] = 24,
    ["Guide_CreateRole_Name17"] = 24,
    ["Guide_CreateRole_Name18"] = 24,
    ["Guide_CreateRole_Name19"] = 24,
    ["Guide_CreateRole_Name20"] = 24,
    ["Guide_CreateRole_Name21"] = 24,
    ["Guide_CreateRole_Name22"] = 24,
    ["Guide_CreateRole_Name23"] = 24,
    ["Guide_CreateRole_Name24"] = 24,
    ["Guide_CreateRole_Name25"] = 24,
    ["Guide_CreateRole_Name26"] = 24,
    ["Guide_CreateRole_Name27"] = 24,
    ["Guide_CreateRole_Name28"] = 24,
    ["Guide_CreateRole_Name29"] = 24,
    ["Guide_CreateRole_Name30"] = 24,
    ["Guide_CreateRole_Name31"] = 24,
    ["Guide_CreateRole_Name32"] = 24,
    ["Guide_CreateRole_Name33"] = 24,
    ["Guide_CreateRole_Name34"] = 24,
    ["Guide_CreateRole_Name35"] = 24,
    ["Guide_CreateRole_Name36"] = 24,
    ["Guide_CreateRole_Name37"] = 24,
    ["Guide_CreateRole_Name38"] = 24,
    ["Guide_CreateRole_Name39"] = 24,
    ["Guide_CreateRole_Name40"] = 24,
    ["Guide_CreateRole_Name41"] = 24,
    ["Guide_CreateRole_Name42"] = 24,
    ["Guide_CreateRole_Name43"] = 24,
    ["Guide_CreateRole_Name44"] = 24,
    ["Guide_CreateRole_Name45"] = 24,
    ["Guide_CreateRole_Name46"] = 24,
    ["Guide_CreateRole_Name47"] = 24,
    ["Guide_CreateRole_Name48"] = 24,
    ["Guide_CreateRole_Name49"] = 24,
    ["Guide_CreateRole_Name50"] = 24,
    ["Guide_CreateRole_Name51"] = 24,
    ["Guide_CreateRole_Name52"] = 24,
    ["Guide_CreateRole_Name53"] = 24,
    ["Guide_CreateRole_Name54"] = 24,
    ["Guide_CreateRole_Name55"] = 24,
    ["Guide_CreateRole_Name56"] = 24,
    ["Guide_CreateRole_Name57"] = 24,
    ["Guide_CreateRole_Name58"] = 24,
    ["Guide_CreateRole_Name59"] = 24,
    ["Guide_CreateRole_Name60"] = 24,
    ["Guide_CreateRole_Name61"] = 24,
    ["Guide_CreateRole_Name62"] = 24,
    ["Guide_CreateRole_Name63"] = 24,
    ["Guide_CreateRole_Name64"] = 24,
    ["Guide_CreateRole_Name65"] = 25,
    ["Guide_CreateRole_Name66"] = 25,
    ["Guide_CreateRole_Name67"] = 25,
    ["Guide_CreateRole_Name68"] = 25,
    ["Guide_CreateRole_Name69"] = 25,
    ["Guide_CreateRole_Name70"] = 25,
    ["Guide_CreateRole_Name71"] = 25,
    ["Guide_CreateRole_Name72"] = 25,
    ["Guide_CreateRole_Name73"] = 25,
    ["Guide_CreateRole_Name74"] = 25,
    ["Guide_CreateRole_Name75"] = 25,
    ["Guide_CreateRole_Name76"] = 25,
    ["Guide_CreateRole_Name77"] = 25,
    ["Guide_CreateRole_Name78"] = 25,
    ["Guide_CreateRole_Name79"] = 25,
    ["Guide_CreateRole_Name80"] = 25,
    ["Guide_CreateRole_Name81"] = 25,
    ["Guide_CreateRole_Name82"] = 25,
    ["Guide_CreateRole_Name83"] = 25,
    ["Guide_CreateRole_Name84"] = 25,
    ["Guide_CreateRole_Name85"] = 25,
    ["Guide_CreateRole_Name86"] = 25,
    ["Guide_CreateRole_Name87"] = 25,
    ["Guide_CreateRole_Name88"] = 25,
    ["Guide_CreateRole_Name89"] = 25,
    ["Guide_CreateRole_Name90"] = 25,
    ["Guide_CreateRole_Name91"] = 25,
    ["Guide_CreateRole_Name92"] = 25,
    ["Guide_CreateRole_Name93"] = 25,
    ["Guide_CreateRole_Name94"] = 25,
    ["Guide_CreateRole_Name95"] = 25,
    ["Guide_CreateRole_Name96"] = 25,
    ["Guide_CreateRole_Name97"] = 25,
    ["Guide_CreateRole_Name98"] = 25,
    ["Guide_CreateRole_Name99"] = 25,
    ["Guide_CreateRole_Name100"] = 25,
    ["Guide_CreateRole_Name101"] = 25,
    ["Guide_CreateRole_Name102"] = 25,
    ["Guide_CreateRole_Name103"] = 25,
    ["Guide_CreateRole_Name104"] = 25,
    ["Guide_CreateRole_Name105"] = 25,
    ["Guide_CreateRole_Name106"] = 25,
    ["Guide_CreateRole_Name107"] = 25,
    ["Guide_CreateRole_Name108"] = 25,
    ["Guide_CreateRole_Name109"] = 25,
    ["Guide_CreateRole_Name110"] = 25,
    ["Guide_CreateRole_Name111"] = 25,
    ["Guide_CreateRole_Name112"] = 25,
    ["Guide_CreateRole_Name113"] = 25,
    ["Guide_CreateRole_Name114"] = 25,
    ["Guide_CreateRole_Name115"] = 25,
    ["Guide_CreateRole_Name116"] = 25,
    ["Guide_CreateRole_Name117"] = 25,
    ["Guide_CreateRole_Name118"] = 25,
    ["Guide_CreateRole_Name119"] = 25,
    ["Guide_CreateRole_Name120"] = 25,
    ["Guide_CreateRole_Name121"] = 25,
    ["Guide_CreateRole_Name122"] = 25,
    ["Guide_CreateRole_Name123"] = 25,
    ["Guide_CreateRole_Name124"] = 25,
    ["Guide_CreateRole_Name125"] = 25,
    ["Guide_CreateRole_Name126"] = 25,
    ["Guide_CreateRole_Name127"] = 25,
    ["Guide_CreateRole_Name128"] = 25,
    ["Guide_CreateRole_Name129"] = 25,
    ["Guide_CreateRole_Name130"] = 25,
    ["Guide_CreateRole_Name131"] = 25,
    ["Guide_CreateRole_Name132"] = 25,
    ["Guide_CreateRole_Name133"] = 25,
    ["Guide_CreateRole_Name134"] = 25,
    ["Guide_CreateRole_Name135"] = 25,
    ["Guide_CreateRole_Name136"] = 25,
    ["Guide_CreateRole_Name137"] = 25,
    ["Guide_CreateRole_Name138"] = 25,
    ["Guide_CreateRole_Name139"] = 25,
    ["Guide_CreateRole_Name140"] = 25,
    ["Guide_CreateRole_Name141"] = 25,
    ["Guide_CreateRole_Name142"] = 25,
    ["Guide_CreateRole_Name143"] = 25,
    ["Guide_CreateRole_Name144"] = 25,
    ["Guide_CreateRole_Name145"] = 25,
    ["Guide_CreateRole_Name146"] = 25,
    ["Guide_CreateRole_Name147"] = 25,
    ["Guide_CreateRole_Name148"] = 25,
    ["Guide_CreateRole_Name149"] = 25,
    ["Guide_CreateRole_Name150"] = 25,
    ["Guide_CreateRole_Name151"] = 25,
    ["Guide_CreateRole_Name152"] = 25,
    ["Guide_CreateRole_Name153"] = 25,
    ["Guide_CreateRole_Name154"] = 25,
    ["Guide_CreateRole_Name155"] = 25,
    ["Guide_CreateRole_Name156"] = 25,
    ["Guide_CreateRole_Name157"] = 25,
    ["Guide_CreateRole_Name158"] = 25,
    ["Guide_CreateRole_Name159"] = 25,
    ["Guide_CreateRole_Name160"] = 25,
    ["Guide_CreateRole_Name161"] = 25,
    ["Guide_CreateRole_Name162"] = 25,
    ["Guide_CreateRole_Name163"] = 25,
    ["Guide_CreateRole_Name164"] = 25,
    ["Guide_CreateRole_Name165"] = 26,
    ["Guide_CreateRole_Name166"] = 26,
    ["Guide_CreateRole_Name167"] = 26,
    ["Guide_CreateRole_Name168"] = 26,
    ["Guide_CreateRole_Name169"] = 26,
    ["Guide_CreateRole_Name170"] = 26,
    ["Guide_CreateRole_Name171"] = 26,
    ["Guide_CreateRole_Name172"] = 26,
    ["Guide_CreateRole_Name173"] = 26,
    ["Guide_CreateRole_Name174"] = 26,
    ["Guide_CreateRole_Name175"] = 26,
    ["Guide_CreateRole_Name176"] = 26,
    ["Guide_CreateRole_Name177"] = 26,
    ["Guide_CreateRole_Name178"] = 26,
    ["Guide_CreateRole_Name179"] = 26,
    ["Guide_CreateRole_Name180"] = 26,
    ["Guide_CreateRole_Name181"] = 26,
    ["Guide_CreateRole_Name182"] = 26,
    ["Guide_CreateRole_Name183"] = 26,
    ["Guide_CreateRole_Name184"] = 26,
    ["Guide_CreateRole_Name185"] = 26,
    ["Guide_CreateRole_Name186"] = 26,
    ["Guide_CreateRole_Name187"] = 26,
    ["Guide_CreateRole_Name188"] = 26,
    ["Guide_CreateRole_Name189"] = 26,
    ["Guide_CreateRole_Name190"] = 26,
    ["Guide_CreateRole_Name191"] = 26,
    ["Guide_CreateRole_Name192"] = 26,
    ["Guide_CreateRole_Name193"] = 26,
    ["Guide_CreateRole_Name194"] = 26,
    ["Guide_CreateRole_Name195"] = 26,
    ["Guide_CreateRole_Name196"] = 26,
    ["Guide_CreateRole_Name197"] = 26,
    ["Guide_CreateRole_Name198"] = 26,
    ["Guide_CreateRole_Name199"] = 26,
    ["Guide_CreateRole_Name200"] = 26,
    ["Guide_CreateRole_Name201"] = 26,
    ["Guide_CreateRole_Name202"] = 26,
    ["Guide_CreateRole_Name203"] = 26,
    ["Guide_CreateRole_Name204"] = 26,
    ["Guide_CreateRole_Name205"] = 26,
    ["Guide_CreateRole_Name206"] = 26,
    ["Guide_CreateRole_Name207"] = 26,
    ["Guide_CreateRole_Name208"] = 26,
    ["Guide_CreateRole_Name209"] = 26,
    ["Guide_CreateRole_Name210"] = 26,
    ["Guide_CreateRole_Name211"] = 26,
    ["Guide_CreateRole_Name212"] = 26,
    ["Guide_CreateRole_Name213"] = 26,
    ["Guide_CreateRole_Name214"] = 26,
    ["Guide_CreateRole_Name215"] = 26,
    ["Guide_CreateRole_Name216"] = 26,
    ["Guide_CreateRole_Name217"] = 26,
    ["Guide_CreateRole_Name218"] = 26,
    ["Guide_CreateRole_Name219"] = 26,
    ["Guide_CreateRole_Name220"] = 26,
    ["Guide_CreateRole_Name221"] = 26,
    ["Guide_CreateRole_Name222"] = 26,
    ["Guide_CreateRole_Name223"] = 26,
    ["Guide_CreateRole_Name224"] = 26,
    ["Guide_CreateRole_Name225"] = 26,
    ["Guide_CreateRole_Name226"] = 26,
    ["Guide_CreateRole_Name227"] = 26,
    ["Guide_CreateRole_Name228"] = 26,
    ["Guide_CreateRole_Name229"] = 26,
    ["Guide_CreateRole_Name230"] = 26,
    ["Guide_CreateRole_Name231"] = 26,
    ["Guide_CreateRole_Name232"] = 26,
    ["Guide_CreateRole_Name233"] = 26,
    ["Guide_CreateRole_Name234"] = 26,
    ["Guide_CreateRole_Name235"] = 26,
    ["Guide_CreateRole_Name236"] = 26,
    ["Guide_CreateRole_Name237"] = 26,
    ["Guide_CreateRole_Name238"] = 26,
    ["Guide_CreateRole_Name239"] = 26,
    ["Guide_CreateRole_Name240"] = 26,
    ["Guide_CreateRole_Name241"] = 26,
    ["Guide_CreateRole_Name242"] = 26,
    ["Guide_CreateRole_Name243"] = 26,
    ["Guide_CreateRole_Name244"] = 26,
    ["Guide_CreateRole_Name245"] = 26,
    ["Guide_CreateRole_Name246"] = 26,
    ["Guide_CreateRole_Name247"] = 26,
    ["Guide_CreateRole_Name248"] = 26,
    ["Guide_CreateRole_Name249"] = 26,
    ["Guide_CreateRole_Name250"] = 26,
    ["Guide_CreateRole_Name251"] = 26,
    ["Guide_CreateRole_Name252"] = 26,
    ["Guide_CreateRole_Name253"] = 26,
    ["Guide_CreateRole_Name254"] = 26,
    ["Guide_CreateRole_Name255"] = 26,
    ["Guide_CreateRole_Name256"] = 26,
    ["Guide_CreateRole_Name257"] = 26,
    ["Guide_CreateRole_Name258"] = 26,
    ["Guide_CreateRole_Name259"] = 26,
    ["Guide_CreateRole_Name260"] = 26,
    ["Guide_CreateRole_Name261"] = 26,
    ["Guide_CreateRole_Name262"] = 26,
    ["Guide_CreateRole_Name263"] = 26,
    ["Guide_CreateRole_Name264"] = 26,
    ["Guide_CreateRole_Name265"] = 27,
    ["Guide_CreateRole_Name266"] = 27,
    ["Guide_CreateRole_Name267"] = 27,
    ["Guide_CreateRole_Name268"] = 27,
    ["Guide_CreateRole_Name269"] = 27,
    ["Guide_CreateRole_Name270"] = 27,
    ["Guide_CreateRole_Name271"] = 27,
    ["Guide_CreateRole_Name272"] = 27,
    ["Guide_CreateRole_Name273"] = 27,
    ["Guide_CreateRole_Name274"] = 27,
    ["Guide_CreateRole_Name275"] = 27,
    ["Guide_CreateRole_Name276"] = 27,
    ["Guide_CreateRole_Name277"] = 27,
    ["Guide_CreateRole_Name278"] = 27,
    ["Guide_CreateRole_Name279"] = 27,
    ["Guide_CreateRole_Name280"] = 27,
    ["Guide_CreateRole_Name281"] = 27,
    ["Guide_CreateRole_Name282"] = 27,
    ["Guide_CreateRole_Name283"] = 27,
    ["Guide_CreateRole_Name284"] = 27,
    ["Guide_CreateRole_Name285"] = 27,
    ["Guide_CreateRole_Name286"] = 27,
    ["Guide_CreateRole_Name287"] = 27,
    ["Guide_CreateRole_Name288"] = 27,
    ["Guide_CreateRole_Name289"] = 27,
    ["Guide_CreateRole_Name290"] = 27,
    ["Guide_CreateRole_Name291"] = 27,
    ["Guide_CreateRole_Name292"] = 27,
    ["Guide_CreateRole_Name293"] = 27,
    ["Guide_CreateRole_Name294"] = 27,
    ["Guide_CreateRole_Name295"] = 27,
    ["Guide_CreateRole_Name296"] = 27,
    ["Guide_CreateRole_Name297"] = 27,
    ["Guide_CreateRole_Name298"] = 27,
    ["Guide_CreateRole_Name299"] = 27,
    ["Guide_CreateRole_Name300"] = 27,
    ["Guide_CreateRole_Name301"] = 27,
    ["Guide_CreateRole_Name302"] = 27,
    ["Guide_CreateRole_Name303"] = 27,
    ["Guide_CreateRole_Name304"] = 27,
    ["Guide_CreateRole_Name305"] = 27,
    ["Guide_CreateRole_Name306"] = 27,
    ["Guide_CreateRole_Name307"] = 27,
    ["Guide_CreateRole_Name308"] = 27,
    ["Guide_CreateRole_Name309"] = 27,
    ["QQMusicEmpty"] = 27,
    ["Replay_ReplayFail"] = 27,
    ["Replay_NotFindReplayFile"] = 27,
    ["Replay_BadFile"] = 27,
    ["Replay_GetFileFail"] = 27,
    ["Replay_DownloadFileFail"] = 27,
    ["InLevel_HeartBeatTimeOut"] = 27,
    ["Activity_RemainCount"] = 27,
    ["Activity_Shared"] = 27,
    ["Activity_AfterUnlocking"] = 27,
    ["Activity_Received"] = 27,
    ["Activity_NotReceived"] = 27,
    ["Activity_ShareCanReceive"] = 27,
    ["Activity_RedPacketUnlock"] = 27,
    ["Activity_LoadPicFail"] = 27,
    ["Activity_Remaining"] = 27,
    ["Activity_SureSpendReSigning"] = 27,
    ["Activity_NotEnough"] = 27,
    ["Activity_ThisWeekSignNotCount"] = 27,
    ["Activity_ServerDataException"] = 27,
    ["Activity_DrawNotEnough"] = 27,
    ["Activity_ActivityTime"] = 27,
    ["Activity_SureToSpend"] = 27,
    ["Activity_MakeSignature"] = 27,
    ["Activity_ThisWeekCanSignTime"] = 27,
    ["Activity_Signature"] = 27,
    ["Activity_DepositNotEnough"] = 27,
    ["Activity_CheckInAndReceiveDeposit"] = 27,
    ["Activity_WeekendCanReceiveDeposit"] = 27,
    ["Activity_Unlock"] = 27,
    ["Activity_Receive"] = 27,
    ["Activity_SuperRedPacketShare"] = 27,
    ["Activity_ShareRedPacket"] = 27,
    ["Activity_ReceiveRedPacket"] = 27,
    ["RealTimeIntervene_Send"] = 27,
    ["RealTimeIntervene_Get"] = 27,
    ["ChargeRebate_Opening"] = 27,
    ["ChargeRebate_ReadyOpen"] = 27,
    ["ChargeRebate_Rebate"] = 27,
    ["Season_HasNum"] = 27,
    ["Season_HighBpCanExchange"] = 27,
    ["Season_HighBpLevelLimit"] = 27,
    ["Season_ExchangeTimeOut"] = 27,
    ["Season_ExchangeBuyCountOver"] = 27,
    ["Season_Left"] = 27,
    ["Season_Round"] = 27,
    ["Season_NextRoundTime"] = 27,
    ["Season_CurrentRoundEndTime"] = 27,
    ["Mail_DeleteAllRead"] = 27,
    ["Mail_TimeOut"] = 27,
    ["Mail_OnekeyGet"] = 27,
    ["Mail_OnekeyRead"] = 27,
    ["Mail_ConfirmDeleteAll"] = 27,
    ["Mail_ConfirmDelete"] = 27,
    ["BP_UpToLevelAndUnlock"] = 27,
    ["BP_CostBuy"] = 28,
    ["BP_AlreadyHas"] = 28,
    ["BP_HighBpCanExchange"] = 28,
    ["BP_ExchangeTimeOut"] = 28,
    ["BP_ExchangeBuyCountOver"] = 28,
    ["BP_Award"] = 28,
    ["BP_Task"] = 28,
    ["BP_Exchange"] = 28,
    ["BP_BPAward"] = 28,
    ["BP_EveryDayTask"] = 28,
    ["BP_SeasonTask"] = 28,
    ["BP_WeekTaskTarget"] = 28,
    ["BP_WeekTask"] = 28,
    ["BP_CostUnlockNormal"] = 28,
    ["BP_CostUnlockHigh"] = 28,
    ["NewChat_NotFriend"] = 28,
    ["NewChat_SendFailed"] = 28,
    ["NewChat_VoiceMessage"] = 28,
    ["NewChat_NoInput"] = 28,
    ["NewChat_SendCD"] = 28,
    ["NewChat_NotInGroup"] = 28,
    ["NewChat_All"] = 28,
    ["NewChat_World"] = 28,
    ["NewChat_TotalPeople"] = 28,
    ["NewChat_NewStar"] = 28,
    ["NewChat_Friend"] = 28,
    ["NewChat_Lobby"] = 28,
    ["NewChat_ChatRoom"] = 28,
    ["NewChat_Team"] = 28,
    ["NewChat_AIChat"] = 28,
    ["NewChat_ChatHourse"] = 28,
    ["NewChat_NowRecording"] = 28,
    ["NewChat_MicroOpenFailed"] = 28,
    ["NewChat_MySelf"] = 28,
    ["NewChat_EnterTeam"] = 28,
    ["NewChat_LeaveTeam"] = 28,
    ["NewChat_ReachMaxInput"] = 28,
    ["NewChat_NoMicroPermission"] = 28,
    ["NewChat_ChoseFriend"] = 28,
    ["NewChat_NoFriend"] = 28,
    ["NewChat_NoMessage"] = 28,
    ["NewChat_NotReadPlus"] = 28,
    ["NewChat_NotRead"] = 28,
    ["NewChat_CanSendAfter"] = 28,
    ["NewChat_InputMessage"] = 28,
    ["NewChat_IsUploading"] = 28,
    ["NewChat_CantUseVoice"] = 28,
    ["NewChat_NotKnowPlayer"] = 28,
    ["NewChat_MinuteBefore"] = 28,
    ["NewChat_HourBefore"] = 28,
    ["NewChat_DayBefore"] = 28,
    ["NewChat_MonthBefore"] = 28,
    ["NewChat_SendMyPosition"] = 28,
    ["NewChat_VoiceSendFailed"] = 28,
    ["NewChat_PositionInfoInvalied"] = 28,
    ["NewChat_TeamEnterTeam"] = 28,
    ["NewChat_TeamLeaveTeam"] = 28,
    ["NewChat_TeamMySelf"] = 28,
    ["NewChat_VoicePlayStart"] = 28,
    ["NewChat_VoiceDownloadFailed"] = 28,
    ["NewChat_VoicePlayEnd"] = 28,
    ["NewChat_StopPlayVoice"] = 28,
    ["NewChat_TimeShow"] = 28,
    ["NewChat_TeamDecorator"] = 28,
    ["NewChat_QuickChat"] = 28,
    ["NewChat_Chat"] = 28,
    ["NewChat_NoChatContent"] = 28,
    ["NewChat_VoiceToWordFailed"] = 28,
    ["NewChat_SayHiChat"] = 28,
    ["Common_Tips_MoreApply"] = 28,
    ["InLevel_OpenMonsterArea"] = 28,
    ["InLevel_ShootGhost"] = 28,
    ["InLevel_AsHero"] = 28,
    ["InLevel_HeroAppear"] = 28,
    ["InLevel_SteelAppear"] = 28,
    ["InLevel_AsSteel"] = 28,
    ["InLevel_Weapon_MachineGun"] = 28,
    ["InLevel_Weapon_M249Grenade"] = 28,
    ["InLevel_Weapon_M4A1"] = 28,
    ["InLevel_Weapon_Bazooka"] = 28,
    ["InLevel_Weapon_SniperGun"] = 28,
    ["InLevel_Weapon_ShotGun"] = 28,
    ["InLevel_ButtleIsMax"] = 28,
    ["InLevel_Reloading"] = 28,
    ["InLevel_GetButtle"] = 28,
    ["InLevel_SkillName_Rampage"] = 28,
    ["InLevel_SkillName_Shield"] = 28,
    ["InLevel_SkillName_SteelRampage"] = 28,
    ["InLevel_SkillName_Grenade"] = 28,
    ["InLevel_Hero_TitleCenter"] = 28,
    ["InLevel_StarBaby_TitleCenter"] = 28,
    ["InLevel_GameResult_Win"] = 28,
    ["InLevel_GameResult_Loser"] = 28,
    ["InLevel_GameResult_Draw"] = 28,
    ["InLevel_Weapon_TargetTips"] = 28,
    ["InLevel_RankNoun"] = 28,
    ["InLevel_Defeat_OneParam"] = 28,
    ["InLevel_GunGame_FefeatFrist"] = 28,
    ["InLevel_Common_Defeat"] = 28,
    ["InLevel_Common_TakeDown"] = 28,
    ["InLevel_Common_Integral"] = 29,
    ["InLevel_Common_Infect"] = 29,
    ["InLevel_Common_Eliminate"] = 29,
    ["InLevel_Common_NotChangeView"] = 29,
    ["Common_Min_NoParam"] = 29,
    ["Common_Count"] = 29,
    ["Common_NoVoiceAuthorit"] = 29,
    ["Common_GameOver_Time"] = 29,
    ["Common_GameStart_SecOnrParam"] = 29,
    ["Common_RankDesc"] = 29,
    ["Common_Infect"] = 29,
    ["Common_Score"] = 29,
    ["Common_WipeOut"] = 29,
    ["Common_FallDown"] = 29,
    ["Common_Beat"] = 29,
    ["UI_Recharge_ReceiveRightNow"] = 29,
    ["Recharge_HowManyDays"] = 29,
    ["Activity_Monday"] = 29,
    ["Activity_Tuesday"] = 29,
    ["Activity_Wednesday"] = 29,
    ["Activity_Thursday"] = 29,
    ["Activity_Friday"] = 29,
    ["Activity_Saturday"] = 29,
    ["Activity_Sunday"] = 29,
    ["DDP_InLevelTips1"] = 29,
    ["DDP_InLevelTips2"] = 29,
    ["DDP_InLevelTips3"] = 29,
    ["DDP_InLevelTips4"] = 29,
    ["DDP_InLevelTips5"] = 29,
    ["DDP_InLevelTips6"] = 29,
    ["DDP_InLevelTips_Default"] = 29,
    ["InLevel_ResultTip1"] = 29,
    ["InLevel_ResultTip2"] = 29,
    ["InLevel_ResultTip3"] = 29,
    ["InLevel_ResultTip4"] = 29,
    ["Login_BackToHome"] = 29,
    ["UGC_ErrorMsg_NoSwitchArea"] = 29,
    ["UGC_ErrorMsg_SwitchAreaSelf"] = 29,
    ["UGC_ErrorMsg_NoMovable"] = 29,
    ["UGC_ErrorMsg_NoCanSwitch"] = 29,
    ["UGC_ErrorMsg_IsMySlaver"] = 29,
    ["Achievement_FilterMode_ToStr1"] = 29,
    ["Achievement_FilterMode_ToStr2"] = 29,
    ["Achievement_FilterMode_ToStr3"] = 29,
    ["Achievement_FilterMode_ToStr4"] = 29,
    ["Achievement_FilterMode_ToStr5"] = 29,
    ["Achievement_NoRecord"] = 29,
    ["Team_InMatching"] = 29,
    ["Common_FuncNoOpen"] = 29,
    ["Common_FuncNoOpen2"] = 29,
    ["Common_UnlockSystemByLevel"] = 29,
    ["Activity_TheFirstWeek"] = 29,
    ["Activity_TheSecondWeek"] = 29,
    ["Activity_TheThirdWeek"] = 29,
    ["Activity_TheFourthWeek"] = 29,
    ["Activity_FifthWeek"] = 29,
    ["Activity_SixthWeek"] = 29,
    ["Activity_SeventhWeek"] = 29,
    ["Report_ReportRequestFailed"] = 29,
    ["Report_ScreenshotUploadAndTry"] = 29,
    ["Questionnaire_HasExpired"] = 29,
    ["Common_FunctionClosed"] = 29,
    ["Common_DS_Exception"] = 29,
    ["Common_CanNotDoAction"] = 29,
    ["Player_InviteDoAction"] = 29,
    ["Player_PlayerCancelDoAction"] = 29,
    ["Player_PlayerRejectYourAction"] = 29,
    ["Player_InteractiveActionStop"] = 29,
    ["CommunityClient_ResReload"] = 29,
    ["UGC_Map_Drafts"] = 29,
    ["UGC_Map_Release"] = 29,
    ["UGC_Map_MapRelease"] = 29,
    ["UGC_Map_LocalMap"] = 29,
    ["UGC_Map_Recycle"] = 29,
    ["UGC_CanNotDoThis"] = 29,
    ["UGC_Map_DownloadMapFail"] = 29,
    ["UGC_Map_TryLater"] = 29,
    ["UGC_Map_IsDeleteMap"] = 29,
    ["UGC_Map_IsCopyMap"] = 29,
    ["UGC_Map_IsRecoverMap"] = 29,
    ["UGC_Map_IsRemoveMap"] = 29,
    ["Common_RuleDesc"] = 29,
    ["PlayerInfo_UGCInfo_Subscribe"] = 29,
    ["PlayerInfo_UGCInfo_Publish"] = 29,
    ["PlayerInfo_UGCInfo_Collect"] = 29,
    ["PlayerInfo_UGCInfo_History"] = 29,
    ["PlayerInfo_UGCInfo_PublicTime"] = 29,
    ["PlayerInfo_UGCInfo_GetMapFail"] = 29,
    ["PlayerInfo_UGCInfo_GetCollectMapFail"] = 29,
    ["PlayerInfo_UGCInfo_NoSubscribe"] = 29,
    ["PlayerInfo_UGCInfo_GoSubscribe"] = 29,
    ["PlayerInfo_UGCInfo_NoMap"] = 29,
    ["PlayerInfo_UGCInfo_GoMadeMap"] = 29,
    ["PlayerInfo_UGCInfo_NoCollect"] = 29,
    ["PlayerInfo_UGCInfo_NoHistory"] = 29,
    ["UGC_ErrorMsg_OnlyGeneralSwitch"] = 29,
    ["UGC_ErrorMsg_SwitchAreaIsMy"] = 29,
    ["Common_UndefineContent"] = 29,
    ["UGC_Program_SelectTemplate"] = 29,
    ["UGC_Program_ClickToSelectTemplate"] = 29,
    ["Common_ResLoadComplete"] = 30,
    ["Activity_FriendShip_InviteTitle"] = 30,
    ["UGC_Map_MainLayer"] = 30,
    ["UGC_Map_ExitEdit"] = 30,
    ["UGC_Map_isExitEdit"] = 30,
    ["UGC_Editor_TabFunc"] = 30,
    ["UGC_Editor_PutStartPoint"] = 30,
    ["UGC_Map_PlayerMap"] = 30,
    ["UGC_Editor_MapList"] = 30,
    ["UGC_Editor_GameCorridor"] = 30,
    ["UGC_Editor_DailyLevel"] = 30,
    ["UGC_Photo_CreateGroup"] = 30,
    ["UGC_Editor_NoRoomCanEnter"] = 30,
    ["UGC_Editor_RoomDissolve"] = 30,
    ["UGC_Editor_GetKeyFail"] = 30,
    ["UGC_Editor_ReadyStartFail"] = 30,
    ["UGC_Editor_PreLoadMapFail"] = 30,
    ["UGC_Editor_Sort_Recommand"] = 30,
    ["UGC_Editor_Sort_Latest"] = 30,
    ["UGC_Editor_Sort_GiveLike"] = 30,
    ["UGC_Editor_CanNotUse"] = 30,
    ["UGC_Editor_UploadPicFail"] = 30,
    ["UGC_Editor_ReportStartFail"] = 30,
    ["UGC_Editor_MapNoPublish"] = 30,
    ["UGC_Editor_MapNoExist"] = 30,
    ["UGC_Editor_GroupMaxSize"] = 30,
    ["UGC_Editor_CanNotToBind"] = 30,
    ["UGC_Editor_CleanSuccess"] = 30,
    ["UGC_AI_IsRequesting"] = 30,
    ["UGC_AI_NoColorable"] = 30,
    ["UGC_AI_NetError"] = 30,
    ["UGC_AI_ServerBusy"] = 30,
    ["UGC_AI_CoolDown"] = 30,
    ["UGC_AI_GenerateFail"] = 30,
    ["UGC_Editor_DownloadGroupFail"] = 30,
    ["UGC_Editor_GroupDataLoadFail"] = 30,
    ["UGC_Editor_AddLocomotorFail"] = 30,
    ["UGC_Editor_MaxLocomotor"] = 30,
    ["UGC_Editor_LocomotorCell"] = 30,
    ["UGC_Editor_Creator_Custom"] = 30,
    ["UGC_Editor_Creator_OnewayMove"] = 30,
    ["UGC_Editor_Creator_LoopAndBackMove"] = 30,
    ["UGC_Editor_Creator_SimpleRotation"] = 30,
    ["UGC_Editor_Creator_Swing"] = 30,
    ["UGC_Editor_Creator_WayPoint"] = 30,
    ["UGC_Editor_Creator_OnewayScale"] = 30,
    ["UGC_Editor_SimulateColor"] = 30,
    ["UGC_Editor_AddSimulateColorFail"] = 30,
    ["UGC_Editor_AddSimulateColorFail2"] = 30,
    ["UGC_Editor_Palette_Collection"] = 30,
    ["UGC_Editor_Musical_GuZheng"] = 30,
    ["UGC_Editor_Musical_PiPa"] = 30,
    ["UGC_Editor_Musical_BianZhong"] = 30,
    ["UGC_Editor_Musical_ElecVocal"] = 30,
    ["UGC_Editor_Musical_ElecGuitar"] = 30,
    ["UGC_Editor_Musical_Saw"] = 30,
    ["UGC_Editor_Musical_Square"] = 30,
    ["UGC_Editor_Musical_YangQin"] = 30,
    ["UGC_Editor_Musical_ElecPiano"] = 30,
    ["UGC_Editor_Musical_Lead"] = 30,
    ["UGC_Editor_Musical_Piano"] = 30,
    ["UGC_Editor_MusicGroup_Little"] = 30,
    ["UGC_Editor_MusicGroup_LittleOne"] = 30,
    ["UGC_Editor_MusicGroup_LittleTwo"] = 30,
    ["UGC_Editor_MusicGroup_LittleThree"] = 30,
    ["UGC_Editor_MusicGroup_Big"] = 30,
    ["UGC_Editor_MusicGroup_BigOne"] = 30,
    ["UGC_Map_Upload_Fail"] = 30,
    ["UGC_Map_Save_UpLoad_Fail"] = 30,
    ["UGC_LocomotorName"] = 30,
    ["UGC_BindingLogic_Success"] = 30,
    ["UGC_BindingLogic_Fail"] = 30,
    ["UGC_OperateBind_Error201"] = 30,
    ["UGC_OperateBind_Error202"] = 30,
    ["UGC_OperateBind_CanNotSelectSelf"] = 30,
    ["UGC_CanNot_JoinTemplate"] = 30,
    ["UGC_CanNot_JoinGroup"] = 30,
    ["UGC_CanNot_Unique"] = 30,
    ["UGC_BindingMovement_Success"] = 30,
    ["UGC_Skybox_Name_1"] = 30,
    ["UGC_Skybox_Name_2"] = 30,
    ["UGC_Skybox_Name_3"] = 30,
    ["UGC_Skybox_Name_4"] = 30,
    ["UGC_Skybox_Name_5"] = 30,
    ["UGC_Skybox_Name_6"] = 30,
    ["UGC_Skybox_Name_7"] = 30,
    ["UGC_Skybox_Name_8"] = 30,
    ["UGC_Skybox_Name_9"] = 30,
    ["UGC_Skybox_Name_10"] = 30,
    ["UGC_Skybox_Name_11"] = 30,
    ["UGC_Skybox_Name_12"] = 30,
    ["UGC_Skybox_Name_13"] = 30,
    ["UGC_Skybox_Name_14"] = 30,
    ["UGC_Skybox_Name_15"] = 30,
    ["UGC_Skybox_Name_16"] = 30,
    ["UGC_Skybox_Name_17"] = 30,
    ["UGC_Skybox_Name_18"] = 30,
    ["UGC_Skybox_Name_19"] = 30,
    ["UGC_Skybox_Name_20"] = 30,
    ["UGC_Skybox_Name_21"] = 30,
    ["UGC_Skybox_Name_22"] = 31,
    ["UGC_Skybox_Name_23"] = 31,
    ["UGC_Skybox_Name_24"] = 31,
    ["UGC_Skybox_Name_25"] = 31,
    ["UGC_Skybox_Name_26"] = 31,
    ["UGC_Skybox_Name_27"] = 31,
    ["UGC_Skybox_Name_28"] = 31,
    ["UGC_Skybox_Name_29"] = 31,
    ["UGC_Skybox_Name_30"] = 31,
    ["UGC_Skybox_Name_31"] = 31,
    ["UGC_Skybox_Name_32"] = 31,
    ["UGC_Skybox_Name_33"] = 31,
    ["UGC_Skybox_Name_34"] = 31,
    ["UGC_Skybox_Name_35"] = 31,
    ["UGC_Skybox_Name_36"] = 31,
    ["UGC_Skybox_Name_37"] = 31,
    ["UGC_Skybox_Name_38"] = 31,
    ["UGC_Skybox_Name_39"] = 31,
    ["UGC_Skybox_Name_40"] = 31,
    ["UGC_Skybox_Name_41"] = 31,
    ["UGC_Skybox_Name_42"] = 31,
    ["UGC_Skybox_Name_43"] = 31,
    ["UGC_Skybox_Name_44"] = 31,
    ["UGC_Skybox_Name_45"] = 31,
    ["UGC_Skybox_Name_46"] = 31,
    ["UGC_Skybox_Name_47"] = 31,
    ["UGC_Skybox_Name_48"] = 31,
    ["UGC_Skybox_Name_49"] = 31,
    ["UGC_Skybox_Name_50"] = 31,
    ["UGC_Skybox_Name_51"] = 31,
    ["UGC_Skybox_Name_52"] = 31,
    ["UGC_Skybox_Name_53"] = 31,
    ["UGC_Skybox_Name_54"] = 31,
    ["UGC_Skybox_Name_55"] = 31,
    ["UGC_Skybox_Name_56"] = 31,
    ["UGC_Skybox_Name_57"] = 31,
    ["UGC_Skybox_Name_58"] = 31,
    ["UGC_Skybox_Name_59"] = 31,
    ["UGC_Skybox_Name_60"] = 31,
    ["UGC_Skybox_Name_61"] = 31,
    ["UGC_Skybox_Name_62"] = 31,
    ["UGC_Skybox_Name_63"] = 31,
    ["UGC_Skybox_Name_64"] = 31,
    ["UGC_Skybox_Name_65"] = 31,
    ["UGC_Skybox_Name_66"] = 31,
    ["UGC_Skybox_Name_67"] = 31,
    ["UGC_Skybox_Name_68"] = 31,
    ["UGC_Skybox_Name_69"] = 31,
    ["UGC_Skybox_Name_70"] = 31,
    ["UGC_Skybox_Name_71"] = 31,
    ["UGC_Skybox_Name_72"] = 31,
    ["UGC_Skybox_Name_73"] = 31,
    ["UGC_Skybox_Name_74"] = 31,
    ["UGC_Skybox_Name_75"] = 31,
    ["UGC_Skybox_Name_76"] = 31,
    ["UGC_Skybox_Name_77"] = 31,
    ["UGC_Skybox_Name_78"] = 31,
    ["UGC_Skybox_Name_79"] = 31,
    ["UGC_Effect_Name_1"] = 31,
    ["UGC_Effect_Name_2"] = 31,
    ["UGC_Skybox_Filter_Name_0"] = 31,
    ["UGC_Skybox_Filter_Name_1"] = 31,
    ["UGC_Skybox_Filter_Name_2"] = 31,
    ["UGC_Skybox_Filter_Name_3"] = 31,
    ["UGC_Skybox_Filter_Name_4"] = 31,
    ["UGC_Skybox_Filter_Name_5"] = 31,
    ["UGC_Skybox_Filter_Name_6"] = 31,
    ["UGC_Skybox_Filter_Name_7"] = 31,
    ["UGC_Skybox_Filter_Name_8"] = 31,
    ["UGC_Skybox_Filter_Name_9"] = 31,
    ["UGC_Skybox_Filter_Name_10"] = 31,
    ["UGC_Skybox_Filter_Name_11"] = 31,
    ["UGC_Skybox_Filter_Name_12"] = 31,
    ["UGC_Skybox_Filter_Name_13"] = 31,
    ["UGC_Skybox_Filter_Name_14"] = 31,
    ["UGC_Skybox_Filter_Name_15"] = 31,
    ["UGC_Skybox_Filter_Name_16"] = 31,
    ["UGC_Skybox_Filter_Name_17"] = 31,
    ["UGC_Skybox_Filter_Name_18"] = 31,
    ["UGC_Skybox_Filter_Name_19"] = 31,
    ["UGC_Skybox_Filter_Name_20"] = 31,
    ["UGC_Skybox_Filter_Name_21"] = 31,
    ["UGC_Skybox_Filter_Name_22"] = 31,
    ["UGC_Skybox_Filter_Name_23"] = 31,
    ["UGC_GroupPublish_NoticeCheck"] = 31,
    ["UGC_GroupPublish_Notice"] = 31,
    ["UGC_BindingMovement_Fail"] = 31,
    ["Team_NoLimit"] = 31,
    ["Team_PublicRecruit"] = 31,
    ["Team_RefreshInCD"] = 31,
    ["Team_CanNotJoinSelfTeam"] = 31,
    ["Team_SelfInTeam"] = 31,
    ["Team_InviteAddFriend"] = 31,
    ["Team_AlreadyAddFriend"] = 31,
    ["Team_ChangeLeader"] = 31,
    ["Team_LeaveTeam"] = 31,
    ["Team_CanNotDisbandTeam"] = 31,
    ["Team_ConfirmDisband"] = 31,
    ["Common_Invincible"] = 31,
    ["Set_FuncNoDevelop"] = 31,
    ["Set_isUnbind"] = 32,
    ["Set_Unbind"] = 32,
    ["Set_CostChangeCardTip"] = 32,
    ["Set_ChangeNameSuccess"] = 32,
    ["Set_NoChangeCard"] = 32,
    ["Lobby_CanNotDoAction"] = 32,
    ["Lobby_CanNotDoDoubleAction"] = 32,
    ["Lobby_PleaseSelectPlayer"] = 32,
    ["Lobby_StatusCanNotDoAction"] = 32,
    ["Lobby_StatusCanNotUseItem"] = 32,
    ["Lobby_Piano_Play"] = 32,
    ["Lobby_Piano_Listen"] = 32,
    ["Set_EditKey_Classic"] = 32,
    ["Set_EditKey_DaLuanDou"] = 32,
    ["Set_EditKey_Arena"] = 32,
    ["Set_EditKey_RoundGameRunning"] = 32,
    ["Set_EditKey_RoundGameFPS"] = 32,
    ["Set_EditKey_SaveKeySuccess"] = 32,
    ["Set_EditKey_SaveKeyFail"] = 32,
    ["Set_EditKey_isSaveCurrentKey"] = 32,
    ["Set_EditKey_isSaveCurrentKeyToExit"] = 32,
    ["Set_EditKey_ResetToDefault"] = 32,
    ["Level_BioChaseFeedback_Name1"] = 32,
    ["Level_BioChaseFeedback_Name2"] = 32,
    ["Level_BioChaseFeedback_Name3"] = 32,
    ["Level_BioChaseFeedback_Name4"] = 32,
    ["Level_BioChaseFeedback_Name5"] = 32,
    ["Level_BioChaseFeedback_Name6"] = 32,
    ["Level_BioChaseFeedback_Name7"] = 32,
    ["Level_GoalText_CanePromoted"] = 32,
    ["Level_GoalText_Out"] = 32,
    ["Level_GoalText_OutTeam"] = 32,
    ["Level_GoalText_Promoted"] = 32,
    ["Level_GoalText_UgcPlay"] = 32,
    ["Lobby_SceneConnectFail"] = 32,
    ["Lobby_ClickToEnterLobby"] = 32,
    ["Plaza_ReconnectInPlaza"] = 32,
    ["Plaza_ReconnectInPlazaSuccess"] = 32,
    ["Plaza_ReconnectInPlazaFail"] = 32,
    ["Item_PickUpWithCount"] = 32,
    ["Item_RandomGet"] = 32,
    ["Item_MustGet"] = 32,
    ["Item_ConfirmDeleteItem"] = 32,
    ["Item_SortQuality"] = 32,
    ["Item_SortTime"] = 32,
    ["Item_MaxRewardCount"] = 32,
    ["Item_ItemNotEnough"] = 32,
    ["Item_GiveFriendItem"] = 32,
    ["Bag_IsSaveSlot"] = 32,
    ["Item_ItemEmpty"] = 32,
    ["Bag_NeedUnlockSkinToSave"] = 32,
    ["Report_MostSelect"] = 32,
    ["Report_EnterReason_UpTo60Words"] = 32,
    ["Report_ReportSuccessful"] = 32,
    ["Report_EnterUpTo60Words"] = 32,
    ["Report_SelectReportReason"] = 32,
    ["Activity_StarRecruitmentInvitation"] = 32,
    ["Activity_ReachedLimit_AndChooseOther"] = 32,
    ["Activity_ChapterNotOpened"] = 32,
    ["Activity_RemianTime"] = 32,
    ["Activity_IsJoinTheTeam"] = 32,
    ["Activity_RecruitmentList"] = 32,
    ["Activity_InviteFriendsLevelToComplete"] = 32,
    ["Activity_InformYourFriends"] = 32,
    ["Activity_SuccessfullyRecruited"] = 32,
    ["Activity_CurrentLevel"] = 32,
    ["Activity_SuccessfullyRecruitedCount"] = 32,
    ["Activity_InviteFriendsReachLevel"] = 32,
    ["Activity_GetNewbieSuit"] = 32,
    ["Activity_SuccessfullyInvitedCount"] = 32,
    ["Activity_InviteFriendsToComplete"] = 32,
    ["Activity_GoalHasCompleted"] = 32,
    ["Battle_PoisonWillTrigger"] = 32,
    ["Level_DDP_ExitRagdoll"] = 32,
    ["Level_DDP_InRagdoll"] = 32,
    ["Level_BioChase_Ghost"] = 32,
    ["Level_BioChase_ShotGhost"] = 32,
    ["Level_BioChase_StarBaby"] = 32,
    ["Level_BioChase_GhostTips_Chase"] = 32,
    ["Level_BioChase_GhostTips_Fight"] = 32,
    ["Level_BioChase_HumanTips_Chase"] = 32,
    ["Level_BioChase_HumanTips_Fight"] = 32,
    ["Level_GhostCatch_PlayerTips"] = 32,
    ["Level_GhostCatch_GhostTips"] = 32,
    ["InLevel_GameOver"] = 32,
    ["Bag_NoSearchResult"] = 32,
    ["Friend_NoFriend"] = 32,
    ["Common_UnlockByLevel"] = 32,
    ["Bag_OpenVipToUnlock"] = 32,
    ["Bag_SlotLock"] = 32,
    ["InLevel_SkillName_Attack"] = 32,
    ["InLevel_RoleName_Killer"] = 32,
    ["InLevel_RoleName_Guard"] = 32,
    ["InLevel_RoleName_Doll"] = 32,
    ["InLevel_GameRule_Killer"] = 32,
    ["InLevel_GameRule_Guard"] = 32,
    ["InLevel_GameRule_Doll"] = 32,
    ["InLevel_Common_GameBeginSec_OneParam"] = 32,
    ["InLevel_GameRule_Killer1"] = 32,
    ["InLevel_GameRule_Guard1"] = 32,
    ["InLevel_GameRule_Doll1"] = 33,
    ["InLevel_RoleTips_Doll"] = 33,
    ["InLevel_RoleTips_Killer"] = 33,
    ["InLevel_RoleTips_Guard"] = 33,
    ["InLevel_RoleRule_Guard"] = 33,
    ["InLevel_RoleRule_Killer"] = 33,
    ["InLevel_Common_MyScoreTips"] = 33,
    ["InLevel_Common_RoleRankTips"] = 33,
    ["InLevel_RoleRank_Param"] = 33,
    ["InLevel_RoleRule_Killer1"] = 33,
    ["InLevel_RoleRule_Guard1"] = 33,
    ["InLevel_Common_Out"] = 33,
    ["InLevel_Common_Live"] = 33,
    ["InLevel_GameRule_Guard2"] = 33,
    ["InLevel_GameRule_Doll2"] = 33,
    ["InLevel_GameRule_Killer2"] = 33,
    ["InLevel_GameRule_Guard3"] = 33,
    ["InLevel_GameRule_Doll3"] = 33,
    ["InLevel_GameRule_Tips"] = 33,
    ["InLevel_GameRule_Guard4"] = 33,
    ["InLevel_GameRule_Tips1"] = 33,
    ["InLevel_GameRule_Tips2"] = 33,
    ["InLevel_GameRule_Tips3"] = 33,
    ["InLevel_GameRule_Guard5"] = 33,
    ["Common_Report"] = 33,
    ["InLevel_RoleRule_Killer3"] = 33,
    ["InLevel_RoleRule_Killer4"] = 33,
    ["InLevel_RoleRule_Killer5"] = 33,
    ["InLevel_GameRule_GameOverTips"] = 33,
    ["InLevel_MapTips_AttackSuccess"] = 33,
    ["InLevel_MapTips_AttackFail"] = 33,
    ["InLevel_Common_Camp"] = 33,
    ["Common_ShareSuccess"] = 33,
    ["Common_ShareFail"] = 33,
    ["Common_SaveSuccess"] = 33,
    ["Common_SaveFail"] = 33,
    ["DDP_SeparatedComa"] = 33,
    ["DDP_InComa"] = 33,
    ["DDP_InLevel_BackToCommunityTip"] = 33,
    ["DDP_InLevel_BackToCommunityTipBeWinner"] = 33,
    ["DDP_InLevel_ChampionTip"] = 33,
    ["DDP_InLevel_PoisonCircleRefreshTip"] = 33,
    ["Common_StartTip"] = 33,
    ["DDP_InLevel_TimeFormat"] = 33,
    ["Common_DontAddFriendAgain"] = 33,
    ["Common_DontKnownWinOrDefeat"] = 33,
    ["Common_YouArePass"] = 33,
    ["Common_YouAreOut"] = 33,
    ["Common_YourTeamIsOut"] = 33,
    ["Net_ReconnectToLevelFail"] = 33,
    ["Net_SecondsLaterTry"] = 33,
    ["Common_ExchangeTargetInfo"] = 33,
    ["Level_BioChase_PreKillZone"] = 33,
    ["Level_BioChase_OpenReady"] = 33,
    ["InLevel_PlayerNotInWhiteList"] = 33,
    ["DDP_InLevel_BackTimeFormat"] = 33,
    ["DDP_InLevel_OneIsOutTip"] = 33,
    ["DDP_InLevel_PoisonCircleBecomeSmaller"] = 33,
    ["Common_OutGameTip"] = 33,
    ["Common_BackToCommunityTip"] = 33,
    ["Common_BackToCommunityTipBeWinner"] = 33,
    ["Common_BackToMatchRoomTip"] = 33,
    ["TDM_Park_CreateMapTabName"] = 33,
    ["TDM_CreateMap_TabName"] = 33,
    ["TDM_MapList_DraftsTabName"] = 33,
    ["TDM_MapList_ReleaseTabName"] = 33,
    ["TDM_GameCorridor_MapSearchTabName"] = 33,
    ["TDM_GameCorridor_FastRoomListTabName"] = 33,
    ["TDM_FastRoomList_RefreshTabName"] = 33,
    ["TDM_FastRoomList_FastRoomItemTabName"] = 33,
    ["TDM_FastRoomList_FastJoinTabName"] = 33,
    ["TDM_RoomList_RefreshTabName"] = 33,
    ["TDM_RoomList_RoomItemTabName"] = 33,
    ["TDM_RoomList_RandomJoinTabName"] = 33,
    ["TDM_RoomList_CreateRoomTabName"] = 33,
    ["TDM_PlayerUgcInfo_UgcInfoTabTabName"] = 33,
    ["TDM_PlayerUgcInfo_UgcInfoToSubTabName"] = 33,
    ["TDM_PlayerUgcInfo_UgcInfoToMakeTabName"] = 33,
    ["GameCorridor_MST_Recommend"] = 33,
    ["GameCorridor_MST_Latest"] = 33,
    ["GameCorridor_MST_Popularity"] = 33,
    ["GameCorridor_MST_LastSeasonHot"] = 33,
    ["GameCorridor_MST_AllTimeHot"] = 33,
    ["GameCorridor_MST_Collect"] = 33,
    ["GameCorridor_MST_Recommend_Members"] = 62,
    ["GameCorridor_MST_Recommend_Official"] = 62,
    ["InLevel_ChangeLevel_Cruise"] = 33,
    ["Common_Tips_OpenVoiceChat"] = 33,
    ["Common_Tips_OPenTeamVoice"] = 33,
    ["Common_Tips_OPenTeamVoice1"] = 33,
    ["Common_GameShow_CurrGame"] = 33,
    ["InLevel_RankNo_GradeYellow24"] = 33,
    ["InLevel_Compliance"] = 33,
    ["InLevel_IsQuitCurrLevel"] = 33,
    ["InLevel_PlayerState_Promoted"] = 33,
    ["InLevel_PlayerState_Out"] = 33,
    ["InLevel_PlayerState_Passed"] = 33,
    ["InLevel_TeamState_Out"] = 33,
    ["InLevel_CommonState_Outcome"] = 33,
    ["InLevel_CommonsState_TeamOut"] = 33,
    ["InLevel_CommonsState_PlayerOut"] = 33,
    ["Common_LevelType_Speed"] = 33,
    ["Common_LevelInfo"] = 34,
    ["Common_RushForward"] = 34,
    ["Common_Begin"] = 34,
    ["Friend_MyIntimacy"] = 34,
    ["Friend_BuildIntimacy"] = 34,
    ["Friend_ApplyIntimacy"] = 34,
    ["Friend_ChangeIntimacy"] = 34,
    ["Friend_BreakIntimacy"] = 34,
    ["Friend_BreakIntimacyTip"] = 34,
    ["RealTimeIntervene_Title"] = 34,
    ["RealTimeIntervene_Play"] = 34,
    ["Player_DefaultName"] = 34,
    ["Player_Mine"] = 34,
    ["MessageBoard_Newest"] = 34,
    ["MessageBoard_Hotest"] = 34,
    ["MessageBoard_Mine"] = 34,
    ["MessageBoard_PlayerHideProfileTip"] = 34,
    ["Common_UserRuleTitle"] = 34,
    ["Common_PrivacyRuleTitle"] = 34,
    ["Common_PleaseConfirmRuleTip"] = 34,
    ["Common_PleaseConfirmRuleTip2"] = 34,
    ["Common_Agree"] = 34,
    ["Common_ChangeLanguageTip"] = 34,
    ["UI_Recharge_SeasonRecharge_BuyPriceFormat"] = 34,
    ["Common_CantBuyTip"] = 34,
    ["UI_Recharge_SeasonRecharge_BuyPriceFormat2"] = 34,
    ["UI_Recharge_SeasonRecharge_BuyPriceFormat3"] = 34,
    ["Common_ShareLinkTitle"] = 34,
    ["Common_ShareLinkDesc"] = 34,
    ["Common_ShareTitle"] = 34,
    ["Common_ShareDesc"] = 34,
    ["Common_ShareMusicTitle"] = 34,
    ["Common_ShareMusicDesc"] = 34,
    ["Common_InviteTitle"] = 34,
    ["Common_InviteDesc"] = 34,
    ["Common_ArkTitle"] = 34,
    ["Common_ArkDesc"] = 34,
    ["Mall_CurrentIsMax"] = 34,
    ["Team_PST_Offline"] = 34,
    ["Team_PST_Online"] = 34,
    ["Team_PST_Lobby"] = 34,
    ["Team_PST_Battle"] = 34,
    ["Team_PST_Matching"] = 34,
    ["Team_PST_Team"] = 34,
    ["Team_PST_TeamReady"] = 34,
    ["Team_PST_TeamMatching"] = 34,
    ["Team_PST_TeamMatching_Leave"] = 34,
    ["PlayerInfo_PLC_Unknow"] = 34,
    ["PlayerInfo_PLC_Mood"] = 34,
    ["PlayerInfo_PLC_Constellation"] = 34,
    ["PlayerInfo_PLC_Hobby"] = 34,
    ["PlayerInfo_PLC_State"] = 34,
    ["Activity_CanIncrease_AndReceiveMost"] = 34,
    ["PlayerInfo_BaseInfo_HasCopyNickName"] = 34,
    ["UI_Recharge_SeasonRecharge_XingxiuNotEnough"] = 34,
    ["Common_PayCancel"] = 34,
    ["Player_InteractiveOverMaxDistance"] = 34,
    ["Player_InteractiveWaitAccept"] = 34,
    ["Player_InteractiveInviteeLogout"] = 34,
    ["Currency_ReachedLimitCount"] = 34,
    ["PlayerInfo_UGCInfo_LikeCount"] = 34,
    ["PlayerInfo_UGCInfo_PlayCount"] = 34,
    ["PlayerInfo_UGCInfo_PlayTime"] = 34,
    ["Lobby_InviteDoAction"] = 34,
    ["Mall_IsGotoGet"] = 34,
    ["PlayerInfo_UGCInfo_OtherPublish"] = 34,
    ["PlayerInfo_UGCInfo_OtherCollect"] = 34,
    ["Grade_Task_Level"] = 34,
    ["Grade_Task_CurLevel"] = 34,
    ["FriendModel_ReplyNtf"] = 34,
    ["FriendModel_ReplyNtf_1"] = 34,
    ["FriendModel_RemoveFriend"] = 34,
    ["FriendModel_BanFriend"] = 34,
    ["FriendModel_RemoveBanFriend"] = 34,
    ["FriendModel_CheckFriendState"] = 34,
    ["FriendModel_CheckFriendState_1"] = 34,
    ["PlayerInfo_InitLevel"] = 34,
    ["Model_Single_TimeTips1"] = 34,
    ["Model_Single_TimeTips2"] = 34,
    ["Model_Single_TimeTips3"] = 34,
    ["Task_NewBiePage_Progress"] = 34,
    ["Setting_Lang"] = 34,
    ["PlayerInfo_AccountBinding"] = 34,
    ["UIInLevelFinishTip_Rank"] = 34,
    ["UIInLevelFinishTip_FriendRank"] = 34,
    ["Team_PlayerInfo"] = 34,
    ["InLevel_FinalAccount_CountDown"] = 34,
    ["PlayerInfo_ChangeState_UseTime"] = 34,
    ["NewChat_Main_ChatFriendName"] = 34,
    ["NewChat_Main_ChatFriendNameWithRemarkName"] = 34,
    ["CommonModel_NoGet"] = 34,
    ["PlayerModel_NoOperation"] = 34,
    ["Login_Agreement1"] = 34,
    ["Login_Agreement2"] = 34,
    ["Login_Agreement3"] = 34,
    ["Lobby_NotMeetFriendIntimate"] = 34,
    ["UI_Setting_Game_Title"] = 34,
    ["Common_NumberFormat"] = 34,
    ["InLevel_FPS_Auto"] = 34,
    ["InLevel_FPS_Manual"] = 34,
    ["InLevel_FPS_Shoot"] = 35,
    ["InLevel_FPS_Attack"] = 35,
    ["InLevel_FPS_SettingTip"] = 35,
    ["InLevel_FPS_SettingTip_1_3P"] = 35,
    ["InLevel_FPS_SettingTip_FireMode"] = 35,
    ["InLevel_FPS_ViewChange"] = 35,
    ["InLevel_FPS_RoguelikeDead"] = 35,
    ["UI_BrGame_HelpTips_Dying"] = 35,
    ["UI_BrGame_HelpTips_Heal"] = 35,
    ["UI_BrGame_HelpTips_Respawn"] = 35,
    ["UI_BrGame_HelpTips_Help"] = 35,
    ["UI_BrGame_HelpItem_Help"] = 35,
    ["UI_BrGame_HelpItem_Revive"] = 35,
    ["UI_BrGame_HelpItem_Out"] = 35,
    ["UI_BrGame_HelpState_Abandon_Tip"] = 35,
    ["UI_BrGame_HelpState_Abandon"] = 35,
    ["UI_BrGame_HelpState_Wait"] = 35,
    ["UI_BrGame_HelpState_Help"] = 35,
    ["UI_BrGame_HelpState_Revive"] = 35,
    ["UI_BrGame_TutorialTips_UseMedicine"] = 35,
    ["UI_BrGame_TutorialTips_SwitchWeapon"] = 35,
    ["UI_BrGame_DeadMoment_Desc"] = 35,
    ["UI_BrGame_DeadMoment_Watch"] = 35,
    ["UI_BrGame_RespawnTip_Close"] = 35,
    ["UI_BrGame_InPoisonCircle_Tip"] = 35,
    ["UI_BrGame_KillTip_KillTeam"] = 35,
    ["UI_BrGame_RulesTab_Parachuting"] = 35,
    ["UI_BrGame_RulesTab_Prop"] = 35,
    ["UI_BrGame_RulesTab_Electricity"] = 35,
    ["UI_BrGame_RulesTab_Help"] = 35,
    ["UI_BrGame_RulesTab_Bag"] = 35,
    ["UI_BrGame_RulesTab_Poison"] = 35,
    ["UI_BrGame_RulesTab_Victory"] = 35,
    ["UI_BrGame_RulesTab_Revive"] = 35,
    ["UI_DfGame_RulesTab_Ready"] = 35,
    ["UI_DfGame_RulesTab_Start"] = 35,
    ["UI_DfGame_RulesTab_Search"] = 35,
    ["UI_DfGame_RulesTab_Resurrection"] = 35,
    ["UI_DfGame_RulesTab_Boss"] = 35,
    ["UI_DfGame_RulesTab_Evacuate"] = 35,
    ["UI_DfGame_RulesTab_Reward"] = 35,
    ["UI_BrGame_KillInfoSelf_Kill"] = 35,
    ["UI_BrGame_KillInfoSelf_Dying"] = 35,
    ["UI_BrGame_TeamInfoItem_Revive"] = 35,
    ["UI_BrGame_TeamInfoItem_ReviveTime"] = 35,
    ["UI_BrGame_Weapon_NotEnough_Ammo"] = 35,
    ["UI_BrGame_Weapon_NotEnough_Ammo_Tip"] = 35,
    ["UI_BrGame_RankView_Title"] = 35,
    ["UI_BrGame_RankView_PlayerName"] = 35,
    ["UI_BrGame_RankView_KillNum"] = 35,
    ["UI_BrGame_RankView_HelpNum"] = 35,
    ["UI_BrGame_RankView_DamageNum"] = 35,
    ["UI_BrGame_RankView_LifeTime"] = 35,
    ["UI_FPSSetting_Common_Title"] = 35,
    ["UI_FPSSetting_Gun_Title"] = 35,
    ["UI_FPSSetting_BioChase_Title"] = 35,
    ["UI_FPSSetting_BR_Title"] = 35,
    ["UI_FPSSetting_RogueLike_Title"] = 35,
    ["UI_FPSSetting_CommonItem_Title"] = 35,
    ["UI_FPSSetting_PlayingItem_Title"] = 35,
    ["UI_FPSSetting_SniperItem_Title"] = 35,
    ["UI_FPSSetting_GyroscopeItem_Title"] = 35,
    ["UI_InLevel_Br_ArriveInfo_KillDes"] = 35,
    ["UI_InLevel_Br_ArriveInfo_AliveNum"] = 35,
    ["Login_FaceBook_CancelLogin"] = 35,
    ["Login_Google_LoginFailedCode1"] = 35,
    ["Login_Google_LoginFailedCode2"] = 35,
    ["Login_Google_LoginFailedCode3"] = 35,
    ["Login_Google_LoginFailedCode9"] = 35,
    ["Login_Google_LoginFailedCode18"] = 35,
    ["Mall_AvatarGiftHasExpire"] = 35,
    ["Chunk_Download_SpaceNotEnough"] = 35,
    ["Common_NotInstallQQTip"] = 35,
    ["Common_NotInstallWXTip"] = 35,
    ["TaskView_TimeUpdate"] = 35,
    ["NewChat_BlackFriend"] = 35,
    ["DDP_InLevel_StartTip"] = 35,
    ["Common_GetShareRewardSuccessTip"] = 35,
    ["UGC_GroupDelete_Title"] = 35,
    ["UGC_GroupDelete_Tips"] = 35,
    ["UGC_GroupUnShelve_Title"] = 35,
    ["UGC_GroupUnShelve_Tips"] = 35,
    ["FriendModel_RemoveIntimateFriend"] = 35,
    ["FriendModel_BanIntimateFriend"] = 35,
    ["Friend_LevelUp"] = 35,
    ["ScoringSystem_Submit"] = 35,
    ["ScoringSystem_Notice_One"] = 35,
    ["ScoringSystem_Notice_Two"] = 35,
    ["ScoringSystem_Simple"] = 35,
    ["ScoringSystem_Normal"] = 35,
    ["ScoringSystem_Difficulty"] = 35,
    ["ScoringSystem_SuperDifficult"] = 35,
    ["ScoringSystem_VeryPoor"] = 35,
    ["ScoringSystem_Dissatisfied"] = 35,
    ["ScoringSystem_Satisfy"] = 35,
    ["ScoringSystem_Awesome"] = 35,
    ["ScoringSystem_Ploy"] = 35,
    ["ScoringSystem_Difficulties"] = 35,
    ["ScoringSystem_Beautiful"] = 35,
    ["ScoringSystem_ordinary"] = 35,
    ["friend_ChangeRelationships"] = 36,
    ["CustomRoom_IsInTeamTip"] = 36,
    ["UgcRoom_ChangeMapSuc"] = 36,
    ["UgcRoom_LeaderChangedMap"] = 36,
    ["CustomRoom_InviteMsgTitle"] = 36,
    ["friend_InviteMsg"] = 36,
    ["Mall_CurrentHasCoinName"] = 36,
    ["UGC_Place_Occupy_OutOfRange"] = 36,
    ["UGC_Place_Available_OutOfRange"] = 36,
    ["UGC_ErrorMsg_TemplateCountOutOfRange"] = 36,
    ["UGC_Group_OutOfRange"] = 36,
    ["UGC_Map_Save_Success"] = 36,
    ["UGC_Map_Save_Fail"] = 36,
    ["UGC_Map_Save_Error"] = 36,
    ["UGC_Group_Name_Tips"] = 36,
    ["UGC_Popup_Rule_Title"] = 36,
    ["UGC_Popup_Rule_Content"] = 36,
    ["UGC_Group_DefaultName"] = 36,
    ["UGC_Copy_CountOutOfRange"] = 36,
    ["UGC_Copy_Lock"] = 36,
    ["UGC_Cannot_Group"] = 36,
    ["UGC_Group_Name_Check_Fail"] = 36,
    ["UGC_Net_Fail"] = 36,
    ["UGC_Group_Upload_Fail"] = 36,
    ["UGC_Group_Download_Fail"] = 36,
    ["UGC_Map_Save_LoadError"] = 36,
    ["UGC_Copy_Cannot_Copy"] = 36,
    ["UGC_CommunityGroup_Cannot_Edit"] = 36,
    ["UGC_CommunityGroup_Locked"] = 36,
    ["UGC_DailyLevel_Tips_1"] = 36,
    ["UGC_DailyLevel_Tips_2"] = 36,
    ["UGC_DailyLevel_Tips_3"] = 36,
    ["UGC_DailyLevel_Tips_4"] = 36,
    ["UGC_DailyLevel_Tips_5"] = 36,
    ["UGC_Input_Desc"] = 36,
    ["UGC_Output_Desc"] = 36,
    ["UGC_OutOf_Map_Range"] = 36,
    ["UGC_GroupObj_Cant_Bind"] = 36,
    ["UGC_GroupObj_Cant_Group"] = 36,
    ["UGC_Role_Pointer_Cant_Group"] = 36,
    ["UGC_Object_Cant_Bind"] = 36,
    ["UGC_Object_Cant_CycleBind"] = 36,
    ["UGC_Object_Cant_BindSelf"] = 36,
    ["UGC_Object_Cant_BindGroup"] = 36,
    ["UGC_BindLayer_OutOfRange"] = 36,
    ["UGC_Object_BindReachLimit"] = 36,
    ["UGC_Object_BindLimit0"] = 36,
    ["UGC_Object_Lock"] = 36,
    ["UGC_Object_Operating"] = 36,
    ["UGC_Rebirth_Mast_PlaceIn_Actor"] = 36,
    ["UGC_SpawnPoint_PlaceIn_Actor"] = 36,
    ["UGC_AtLess_One_RebirthPoint"] = 36,
    ["UGC_ToMuch_RebirthPoint"] = 36,
    ["UGC_AtLess_One_VectoryArea"] = 36,
    ["UGC_Cannot_Flavor_SelfGroup"] = 36,
    ["UGC_Map_Version_Not_Match"] = 36,
    ["UGC_MapPNG_Uplaod_Fail"] = 36,
    ["UGC_MapPNG_Uplaod_Success"] = 36,
    ["UGC_MapPNG_Cannot_Use"] = 36,
    ["UGC_Object_Drag_OutOfRange"] = 36,
    ["UGC_Star_Cannot_Copy"] = 36,
    ["UGC_Copy_Ocuupy_OutOfRange"] = 36,
    ["UGC_Copy_Map_OutOfRange"] = 36,
    ["UGC_GroupLayer_OutOfRange"] = 36,
    ["UGC_Group_Occupy_OutOfRange"] = 36,
    ["UGC_Objec_Bind_Success"] = 36,
    ["UGC_Objec_Bind_Error_Select_Zero"] = 36,
    ["UGC_Objec_UnBind_Success"] = 36,
    ["UGC_Cannot_Undo"] = 36,
    ["UGC_Cannot_Redo"] = 36,
    ["UI_ModelOpenTime1"] = 36,
    ["UI_ModelOpenTime2"] = 36,
    ["UI_ModelOpenTime3"] = 36,
    ["UGC_Editor_ChangeMapFailByPlayerCount"] = 36,
    ["UGC_AI_PromptCannotUse"] = 36,
    ["UGC_AI_SelectLabelOrInputText"] = 36,
    ["UGC_AI_PromptLengthLimit"] = 36,
    ["UGC_AI_PromptLanguageNotSuport"] = 36,
    ["UGC_AI_ChangeColorComplete"] = 36,
    ["UGC_AI_ThanksToFeedback"] = 36,
    ["UGC_AI_SelectColorTheme"] = 36,
    ["UGC_AI_SelectColorBoard"] = 36,
    ["UGC_AI_ExtractPaletteSuccess"] = 36,
    ["UGC_AI_HistoryPaletteApply"] = 36,
    ["UGC_AI_NoPermissionNotice"] = 36,
    ["UGC_AI_GoPermissionSetting"] = 36,
    ["UGC_AI_AdjsutScreenShot"] = 36,
    ["UGC_AI_HasUnColorableNotice"] = 36,
    ["UGC_AI_ColorInGenerateNotice"] = 36,
    ["UGC_AI_ReferenceInGenerateNotice"] = 36,
    ["UGC_AI_ColorUndo"] = 36,
    ["UGC_AI_Tab_ReferenceGenerate"] = 36,
    ["UGC_AI_Tab_ReferenceHistory"] = 36,
    ["UGC_AI_TabSub_ReferenceRecently"] = 36,
    ["UGC_AI_TabSub_ReferenceHistory"] = 36,
    ["UGC_AI_Tab_ColorTheme"] = 36,
    ["UGC_AI_Tab_ColorBoard"] = 36,
    ["UGC_AI_TabSub_ColorPalette"] = 36,
    ["UGC_AI_TabSub_ColorHistory"] = 36,
    ["UGC_AI_ColorChanging"] = 36,
    ["UGC_Group_PhotoBeforeSave"] = 37,
    ["UGC_Group_NameLengthLimit"] = 37,
    ["UGC_GroupPublish_CoverUnValid"] = 37,
    ["UGC_GroupPublish_NameIsNull"] = 37,
    ["UGC_GroupPublish_DescIsNull"] = 37,
    ["UGC_Group_OutOfSize"] = 37,
    ["UGC_GroupPublish_NameHasSpecialChar"] = 37,
    ["UGC_GroupPublish_DescHasSpecialChar"] = 37,
    ["UGC_GroupPublish_NoTag"] = 37,
    ["UGC_GroupPublish_PublishFail"] = 37,
    ["UGC_GroupPublish_PublishSuccess"] = 37,
    ["UGC_GroupPublish_DescNum"] = 37,
    ["UGC_Editor_SensitiveWord"] = 37,
    ["UGC_MapLaunch_NoPhoto"] = 37,
    ["UGC_MapLaunch_PhotoUploading"] = 37,
    ["UGC_MapLaunch_Fail"] = 37,
    ["UgcRoom_FullCommission"] = 37,
    ["friend_RelationshipRemoveRequestSent"] = 37,
    ["friend_AddFriendTips"] = 37,
    ["NewChat_JoinVoiceRoomSucceed"] = 37,
    ["NewChat_JoinVoiceRoomFailed"] = 37,
    ["UGC_ProtoSave_Fail"] = 37,
    ["UI_InLevel_Experience_Level"] = 37,
    ["UI_LuckBuy_RewardTips"] = 37,
    ["UI_LuckBuy_LockRewardTips"] = 37,
    ["UI_LuckBuy_ActivityName"] = 37,
    ["UI_LuckBuy_HotRewardTips"] = 37,
    ["UI_LuckBuy_RewardTips1"] = 37,
    ["UI_LuckBuy_ProgressTips"] = 37,
    ["UI_Common_PreviewReward"] = 37,
    ["UI_InLevel_TransCd"] = 37,
    ["Team_DisbandTeam"] = 37,
    ["UI_LuckBuy_Draw"] = 37,
    ["UI_LuckBuy_ViewName"] = 37,
    ["UGC_Waypoint_Cannot_MultipleSelect"] = 37,
    ["UGC_Waypoint_Cannot_Attach"] = 37,
    ["UGC_Waypoint_Cannot_Copy"] = 37,
    ["NewChat_VoiceForbidden"] = 37,
    ["UGC_Editor_Sort_Relevance"] = 37,
    ["Common_ZeroHourDesc"] = 37,
    ["BP_SeasonTitle"] = 37,
    ["BP_PreviewSeasonTitle"] = 37,
    ["UgcRoom_InTheRoom"] = 37,
    ["friend_removeIntimated"] = 37,
    ["UGC_ErrorMsg_SelectActorMaxNum"] = 37,
    ["UGC_Map_Save_Fail_FileOvercapacity"] = 37,
    ["UGC_Map_Clipboard_Share_Text_1"] = 37,
    ["UGC_Map_Clipboard_Share_Text_2"] = 37,
    ["UGC_Map_Clipboard_Share_Tips"] = 37,
    ["UGC_Map_Share_Title"] = 37,
    ["UGC_Map_Share_Wx_Circle_Title"] = 37,
    ["UGC_Map_Share_WxNative_Title"] = 37,
    ["UGC_Map_ShareContent"] = 37,
    ["UGC_Map_ShareContent_Less"] = 37,
    ["UGC_Map_CantShareInTestServer"] = 37,
    ["Item_ItemExpireTime"] = 37,
    ["UGC_Map_Actor_Overcapacity"] = 37,
    ["Player_Head"] = 37,
    ["UGC_Novice_Difficulty"] = 37,
    ["UGC_Simple_Difficulty"] = 37,
    ["UGC_Normal_Difficulty"] = 37,
    ["UGC_Hard_Difficulty"] = 37,
    ["UGC_Hell_Difficulty"] = 37,
    ["UGC_DreamTravel_Tip"] = 37,
    ["UGC_NumFormat_Wan_D"] = 37,
    ["UGC_NumFormat_Wan_F"] = 37,
    ["UGC_NumFormat_Yi_D"] = 37,
    ["UGC_NumFormat_Yi_F"] = 37,
    ["StringUtils_10000"] = 37,
    ["StringUtils_100000000"] = 37,
    ["Team_InviteQQ_Friend"] = 37,
    ["Team_InviteWX_Friend"] = 37,
    ["Team_InviteFail"] = 37,
    ["PlatPrivilege_WX_Tip"] = 37,
    ["FriendPlatPrivilege_WX_Tip"] = 37,
    ["CustomRoom_RoomNotExited"] = 37,
    ["PlatPrivilege_QQ_Tip"] = 37,
    ["FriendPlatPrivilege_QQ_Tip"] = 37,
    ["UGC_GrideOpacity_Open"] = 37,
    ["UGC_MirrorLimit_MirrorLock"] = 37,
    ["UGC_MirrorLimit_RotateLock"] = 37,
    ["UGC_MirrorTotal_ValueOverflow"] = 37,
    ["Login_OtherUserLoginSuccess"] = 37,
    ["UI_BlessBag_Expired"] = 37,
    ["Activity_ClickShare"] = 37,
    ["Activity_ReceivedParam"] = 37,
    ["DrawReward_MallViewToShelve"] = 37,
    ["UI_BlessBag_Date"] = 37,
    ["UI_Recruit_Target"] = 37,
    ["UI_Recruit_Num"] = 37,
    ["UI_Recruit_InviteNum"] = 37,
    ["UI_Recruit_ReachLevelNum"] = 37,
    ["UI_Recruit_TargetDesc"] = 37,
    ["UI_Pilot_NotStart"] = 37,
    ["Common_FormatInt1"] = 37,
    ["Common_FormatInt2"] = 37,
    ["CustomRoom_StartDownload"] = 37,
    ["UGC_SkyBox_Trans"] = 37,
    ["UGC_SkyBox_Background"] = 37,
    ["UGC_SkyBox_Trans_MaxNum_Hint"] = 37,
    ["UGC_SkyBox_Txt_Light"] = 38,
    ["UGC_SkyBox_Txt_Trans"] = 38,
    ["UI_Model_GamingCalendar"] = 38,
    ["UI_Model_NewGame_Status"] = 38,
    ["Activity_CheckIn_LotteryDes"] = 38,
    ["SDK_Message_StrContent"] = 38,
    ["SDK_Message_Cancel"] = 38,
    ["SDK_Message_Confirm"] = 38,
    ["SDK_Message_Input"] = 38,
    ["SDK_Message_Error1"] = 38,
    ["SDK_Message_Error2"] = 38,
    ["SDK_Message_Error3"] = 38,
    ["Home_Player_NoCreate"] = 38,
    ["Home_StatyInCurScene"] = 38,
    ["Home_Visitor_Limit"] = 38,
    ["Activity_Takeaway_BoxUnLocking"] = 38,
    ["Activity_Takeaway_BoxUnLockNotFinish"] = 38,
    ["Common_MCL_DailyLimit3"] = 38,
    ["Common_MCL_MonthlyLimit3"] = 38,
    ["Common_MCL_WeeklyLimit3"] = 38,
    ["Common_MCL_YearlyLimit3"] = 38,
    ["Common_MCL_LifeLongLimit3"] = 38,
    ["Common_MCL_SeasonLimit3"] = 38,
    ["Common_GameQuality"] = 38,
    ["Common_GameQuality_1"] = 38,
    ["Common_GameQuality_2"] = 38,
    ["Common_GameQuality_3"] = 38,
    ["Common_GameQuality_4"] = 38,
    ["Common_FunctionNotAvailable"] = 38,
    ["UGC_Map_Can_Not_Share"] = 38,
    ["AccountAdditionTip"] = 38,
    ["Mall_AskFotItem"] = 38,
    ["Mall_Gift"] = 38,
    ["Mall_Click_AskForItem"] = 38,
    ["Mall_Click_Gift"] = 38,
    ["Mall_Gift_Notice01"] = 38,
    ["Mall_Gift_Notice02"] = 38,
    ["Mall_Gift_Notice03"] = 38,
    ["Mail_Gift"] = 38,
    ["Mail_ReceiveGift"] = 38,
    ["Mail_AskForGift"] = 38,
    ["Mail_GiftLog"] = 38,
    ["Mall_Gift_Tab"] = 38,
    ["Mail_System"] = 38,
    ["Common_SeverDataError"] = 38,
    ["Bag_IPOutfitRefuseToJoinGame"] = 38,
    ["Bag_IPOutfitRefuseToSaveAsSpareOutfit"] = 38,
    ["Bag_IPOutfitRefuseToWearTogether"] = 38,
    ["UGC_Editor_Widget_Group"] = 38,
    ["UGC_Editor_Enter_CanMoveCanvas"] = 38,
    ["UGC_Waypoint_Cannot_Operate"] = 38,
    ["UGC_Waypoint_Reach_MaxNumber"] = 38,
    ["UGC_Waypoint_Reset"] = 38,
    ["UGC_Map_Save_OccupyOutOfRange"] = 38,
    ["UGC_Editor_ScaleInteractive_Text"] = 38,
    ["UGC_Map_Cooperation"] = 38,
    ["UGC_Map_TabName_Recycle"] = 38,
    ["UGC_MapList_CountTitle_DraftCommon"] = 38,
    ["UGC_MapList_CountTitle_Cooperation"] = 38,
    ["UGC_MapList_CountTitle_Release"] = 38,
    ["UGC_MapList_CountTitle_Recycler"] = 38,
    ["UGC_Author_Title"] = 38,
    ["UGC_Editor_TextInputMaxLimit"] = 38,
    ["UGC_Editor_CanvasResetDefault"] = 38,
    ["UGC_Editor_GroupChildAutoClear"] = 38,
    ["UGC_Editor_Widget_Text"] = 38,
    ["UGC_Editor_Widget_Button"] = 38,
    ["UGC_MapList_CountTips_DraftCommon"] = 38,
    ["UGC_MapList_CountTips_Cooperation"] = 38,
    ["UGC_MapList_CountTips_Release"] = 38,
    ["UGC_MapList_CountTips_Recycler"] = 38,
    ["UGC_MapList_MaxLimit_DraftCommon"] = 38,
    ["UGC_MapList_MaxLimit_Cooperation"] = 38,
    ["UGC_MapList_MaxLimit_Release"] = 38,
    ["UGC_MapList_MaxLimit_Recycler"] = 38,
    ["UGC_MapList_Edit"] = 38,
    ["UGC_MapList_Detail"] = 38,
    ["UGC_MapList_TimeDesc_Create"] = 38,
    ["UGC_MapList_TimeDesc_Modify"] = 38,
    ["UGC_MapList_TimeDesc_Update"] = 38,
    ["UGC_MapList_Sort_CreateTime"] = 38,
    ["UGC_MapList_Sort_ModifyTime"] = 38,
    ["UGC_MapList_Sort_MapName"] = 38,
    ["UGC_MapList_Sort_UpdateTime"] = 38,
    ["UGC_MapList_Sort_DeleteTime"] = 38,
    ["UGC_MapList_Filter_All"] = 38,
    ["UGC_GroupPublish_EditOption_Title"] = 38,
    ["UGC_GroupPopUp_EditOption_Title"] = 38,
    ["UGC_GroupPopUp_EditOption_Base"] = 38,
    ["UGC_GroupPopUp_EditOption_Appearance"] = 38,
    ["UGC_GroupPopUp_EditOption_Motion"] = 38,
    ["UGC_MusicSetting_Tip"] = 38,
    ["UGC_TextMask_Tips"] = 38,
    ["UGC_MapList_Play"] = 38,
    ["UGC_MapList_Destroy_LeftTime"] = 38,
    ["UGC_MapList_Publish_PreReview"] = 38,
    ["UGC_MapList_Publish_Rejected"] = 38,
    ["UGC_MapList_Publish_TakeOff"] = 38,
    ["UGC_MapList_TimeDesc_Upload"] = 38,
    ["UGC_MapList_TimeDesc_Publish"] = 38,
    ["UGC_MapList_Sort_PublishTime"] = 39,
    ["UGC_Cannot_Create_First_Layer"] = 39,
    ["Prop_Fireworks_Bullet"] = 39,
    ["UI_StarCruise_DailyRewardDesc"] = 39,
    ["Team_LeaveTeamSuccess"] = 39,
    ["Activity_Takeaway_NotEnoughKey"] = 39,
    ["Activity_GoTo_Finish"] = 39,
    ["EmitDoubleCheckText"] = 39,
    ["FireworkPartyNotOpen"] = 39,
    ["FireworkRelease_Unable"] = 39,
    ["SaveFailToast"] = 39,
    ["SaveTextTooLang"] = 39,
    ["PlayTextNotVaildText"] = 39,
    ["PlayFireworkItemNotEnough"] = 39,
    ["PlayFireworkItemError"] = 39,
    ["PlayTextNotInCommunity"] = 39,
    ["SaveFailToUnSupport"] = 39,
    ["PlayFireworkSuccess"] = 39,
    ["FireworkAuthorNamePrefix"] = 39,
    ["UGC_CoCreate_OnlyCreatorEditMap"] = 39,
    ["UGC_CoCreate_OnlyCreatorPublish"] = 39,
    ["UGC_CoCreate_ExitEdit"] = 39,
    ["UGC_CoCreate_ExitError"] = 39,
    ["UGC_CoCreate_InviteSucced"] = 39,
    ["UGC_CoCreate_TeamMember"] = 39,
    ["UGC_CoCreate_Creator"] = 39,
    ["UGC_CoCreate_SubSucceed"] = 39,
    ["UGC_CoCreate_CancelSubSucceed"] = 39,
    ["UGC_CoCreate_RemoveTeamMember"] = 39,
    ["UGC_Editor_UIWidget_Default_Text"] = 39,
    ["UGC_ChangeMapTip"] = 39,
    ["UGC_ChangeMap_Limit"] = 39,
    ["Activity_GoTo_Share"] = 39,
    ["ItemChangeReason_MonthCardBuy"] = 39,
    ["TDM_Room_BtnReadyTips"] = 39,
    ["PlayerInfo_SeasonName"] = 39,
    ["PlayerInfo_SeasonSuitCollection"] = 39,
    ["Model_MatchType_PreDesc"] = 39,
    ["Mail_Gift_Text"] = 39,
    ["Mail_AakForItem_Text"] = 39,
    ["UI_VoiceSetting_RecommandMusicTip"] = 39,
    ["Player_LevelMap_All"] = 39,
    ["Player_LevelMap_Race"] = 39,
    ["Player_LevelMap_Survival"] = 39,
    ["Player_LevelMap_SingleScore"] = 39,
    ["Player_LevelMap_TeamScore"] = 39,
    ["UGC_VERIFICATION_UNABLE_PLAY"] = 39,
    ["UGC_VERIFICATION_UNABLE_SHARE"] = 39,
    ["UGC_VERIFICATION_UNABLE_CREATE_ROOM"] = 39,
    ["UGC_CoCreate_InviteTitle"] = 39,
    ["UGC_CoCreate_DingTitle"] = 39,
    ["GameCorridor_MST_EveryonePlaying"] = 39,
    ["GameCorridor_FriendPlaying"] = 39,
    ["friend_InGame"] = 39,
    ["friend_DefaultPlatform"] = 39,
    ["Recharge_RechargeNotEnough_Tip"] = 39,
    ["Recharge_RechargeIsRunning_Tip"] = 39,
    ["Net_ConnectServerSuccess"] = 39,
    ["Common_TreasureBox"] = 39,
    ["Arena_ChestUpgrade_GetNum"] = 61,
    ["TDM_GameCorridor_Tag"] = 39,
    ["TDM_GameCorridor_FastTag"] = 39,
    ["UI_Recharge_SeasonRechargeXingyuanbiCount"] = 39,
    ["UI_Recharge_SeasonRechargeSingleGameGetDesc"] = 39,
    ["GameLive_Init_Hint"] = 39,
    ["Quit_Text"] = 39,
    ["Player_InteractiveActionPause"] = 39,
    ["UGC_Map_Share_Game_Friend_Self"] = 39,
    ["UGC_Map_Share_Game_Friend_Others"] = 39,
    ["Level_BioChase_HeroProgress"] = 39,
    ["Battle_TornadoWillTrigger"] = 39,
    ["DDP_InLevel_TornadoRefreshTip"] = 39,
    ["DDP_Team_InLevelTips1"] = 39,
    ["friend_normal_intimaty"] = 39,
    ["friend_multiplayer_intimaty"] = 39,
    ["friend_intimaty_reward_level"] = 39,
    ["CustomRoom_LevelSelectCount"] = 39,
    ["Team_ChangeTeamConfirm"] = 39,
    ["NewChat_Nearby"] = 39,
    ["Set_EditKey_FPSClassic"] = 39,
    ["Set_EditKey_FPSBioChase"] = 39,
    ["Set_EditKey_FPSBrGame"] = 39,
    ["Set_EditKey_FPSTycGame"] = 39,
    ["Set_EditKey_FPSDFGame"] = 39,
    ["Set_EditKey_FPSOmdGame"] = 39,
    ["Activity_CantDig_Msg"] = 39,
    ["Lobby_CanNotDoSingleAction"] = 39,
    ["Activity_Takeaway_BoxStartLock"] = 39,
    ["Prop_Ball_Tips"] = 39,
    ["Prop_Banana_Tips"] = 39,
    ["Prop_Bomb_Tips"] = 39,
    ["Prop_Boomerang_Tips"] = 39,
    ["Prop_Big_Tips"] = 39,
    ["Prop_Small_Tips"] = 39,
    ["Prop_Shuttle_Tips"] = 39,
    ["Prop_Cloud_Tips"] = 39,
    ["Prop_HangGlider_Tips"] = 39,
    ["Prop_Ice_Tips"] = 39,
    ["Prop_Jetpack_Tips"] = 39,
    ["Prop_Landmine_Tips"] = 39,
    ["Prop_Pole_Tips"] = 39,
    ["Prop_Roadblock_Tips"] = 40,
    ["Prop_SpeedUp_Tips"] = 40,
    ["Prop_Springboard_Tips"] = 40,
    ["Prop_Substitute_Tips"] = 40,
    ["Prop_ScreamingChicken_Tips"] = 40,
    ["Prop_Fishing_Tips"] = 40,
    ["Prop_Fireworks_Tips"] = 40,
    ["Prop_Portal_Tips"] = 40,
    ["Prop_VacuumCleaner_Tips"] = 40,
    ["Prop_RotateHammer_Tips"] = 40,
    ["Prop_TimeStave_Tips"] = 40,
    ["Prop_MucusTrap_Tips"] = 40,
    ["Prop_StealthBoom_Tips"] = 40,
    ["Prop_FishHook_Tips"] = 40,
    ["Prop_CloudFlappyBird_Tips"] = 40,
    ["AB_Backtrack_Tips"] = 40,
    ["AB_SuperBoomerang_Tips"] = 40,
    ["AB_BigHammer_Tips"] = 40,
    ["AB_SnowBall_Tips"] = 40,
    ["AB_Stealth_Tips"] = 40,
    ["AB_BigRotateHammer_Tips"] = 40,
    ["AB_JetpackGlider_Tips"] = 40,
    ["AB_Superman_Tips"] = 40,
    ["AB_Meteorite_Tips"] = 40,
    ["AB_ShapeshiftingBoom_Tips"] = 40,
    ["AB_PalmLeafFan_Tips"] = 40,
    ["AB_Lightning_Tips"] = 40,
    ["AB_Shield_Tips"] = 40,
    ["AB_StickyBomb_Tips"] = 40,
    ["AB_Ghost_Tips"] = 40,
    ["AB_RandomProp_Tips"] = 40,
    ["AB_RocketJump_Tips"] = 40,
    ["AB_HugeCloud_Tips"] = 40,
    ["AB_Vine_Tips"] = 40,
    ["AB_IcePrincess_Tips"] = 40,
    ["Prop_Ball_MoreTips"] = 40,
    ["Prop_Banana_MoreTips"] = 40,
    ["Prop_Bomb_MoreTips"] = 40,
    ["Prop_Boomerang_MoreTips"] = 40,
    ["Prop_Big_MoreTips"] = 40,
    ["Prop_Small_MoreTips"] = 40,
    ["Prop_Shuttle_MoreTips"] = 40,
    ["Prop_Cloud_MoreTips"] = 40,
    ["Prop_HangGlider_MoreTips"] = 40,
    ["Prop_Ice_MoreTips"] = 40,
    ["Prop_Jetpack_MoreTips"] = 40,
    ["Prop_Landmine_MoreTips"] = 40,
    ["Prop_Pole_MoreTips"] = 40,
    ["Prop_Roadblock_MoreTips"] = 40,
    ["Prop_SpeedUp_MoreTips"] = 40,
    ["Prop_Springboard_MoreTips"] = 40,
    ["Prop_Substitute_MoreTips"] = 40,
    ["Prop_ScreamingChicken_MoreTips"] = 40,
    ["Prop_Fishing_MoreTips"] = 40,
    ["Prop_Fireworks_MoreTips"] = 40,
    ["Prop_Portal_MoreTips"] = 40,
    ["Prop_VacuumCleaner_MoreTips"] = 40,
    ["Prop_RotateHammer_MoreTips"] = 40,
    ["Prop_MucusTrap_MoreTips"] = 40,
    ["Prop_StealthBoom_MoreTips"] = 40,
    ["Prop_CloudFlappyBird_MoreTips"] = 40,
    ["AB_Backtrack_MoreTips"] = 40,
    ["AB_SuperBoomerang_MoreTips"] = 40,
    ["AB_BigHammer_MoreTips"] = 40,
    ["AB_SnowBall_MoreTips"] = 40,
    ["AB_Stealth_MoreTips"] = 40,
    ["AB_BigRotateHammer_MoreTips"] = 40,
    ["AB_JetpackGlider_MoreTips"] = 40,
    ["AB_Superman_MoreTips"] = 40,
    ["AB_Meteorite_MoreTips"] = 40,
    ["AB_ShapeshiftingBoom_MoreTips"] = 40,
    ["AB_PalmLeafFan_MoreTips"] = 40,
    ["AB_Lightning_MoreTips"] = 40,
    ["AB_Shield_MoreTips"] = 40,
    ["AB_StickyBomb_MoreTips"] = 40,
    ["AB_Ghost_MoreTips"] = 40,
    ["AB_RandomProp_MoreTips"] = 40,
    ["AB_RocketJump_MoreTips"] = 40,
    ["AB_HugeCloud_MoreTips"] = 40,
    ["AB_Vine_MoreTips"] = 40,
    ["AB_IcePrincess_MoreTips"] = 40,
    ["Prop_InfiniteName"] = 40,
    ["Prop_Ball_Name"] = 40,
    ["Prop_Banana_Name"] = 40,
    ["Prop_Bomb_Name"] = 40,
    ["Prop_Boomerang_Name"] = 40,
    ["Prop_Big_Name"] = 40,
    ["Prop_Small_Name"] = 40,
    ["Prop_Shuttle_Name"] = 40,
    ["Prop_Cloud_Name"] = 40,
    ["Prop_HangGlider_Name"] = 40,
    ["Prop_Ice_Name"] = 40,
    ["Prop_Jetpack_Name"] = 40,
    ["Prop_Landmine_Name"] = 40,
    ["Prop_Pole_Name"] = 40,
    ["Prop_Roadblock_Name"] = 40,
    ["Prop_SpeedUp_Name"] = 40,
    ["Prop_Springboard_Name"] = 40,
    ["Prop_Substitute_Name"] = 40,
    ["Prop_ScreamingChicken_Name"] = 40,
    ["Prop_Fishing_Name"] = 41,
    ["Prop_Fireworks_Name"] = 41,
    ["Prop_Portal_Name"] = 41,
    ["Prop_VacuumCleaner_Name"] = 41,
    ["Prop_RotateHammer_Name"] = 41,
    ["Prop_TimeStave_Name"] = 41,
    ["Prop_MucusTrap_Name"] = 41,
    ["Prop_StealthBoom_Name"] = 41,
    ["Prop_FishHook_Name"] = 41,
    ["Prop_CloudFlappyBird_Name"] = 41,
    ["AB_Backtrack_Name"] = 41,
    ["AB_SuperBoomerang_Name"] = 41,
    ["AB_BigHammer_Name"] = 41,
    ["AB_SnowBall_Name"] = 41,
    ["AB_Stealth_Name"] = 41,
    ["AB_BigRotateHammer_Name"] = 41,
    ["AB_JetpackGlider_Name"] = 41,
    ["AB_Superman_Name"] = 41,
    ["AB_Meteorite_Name"] = 41,
    ["AB_ShapeshiftingBoom_Name"] = 41,
    ["AB_PalmLeafFan_Name"] = 41,
    ["AB_Lightning_Name"] = 41,
    ["AB_Shield_Name"] = 41,
    ["AB_StickyBomb_Name"] = 41,
    ["AB_Ghost_Name"] = 41,
    ["AB_RandomProp_Name"] = 41,
    ["AB_RocketJump_Name"] = 41,
    ["AB_HugeCloud_Name"] = 41,
    ["AB_Vine_Name"] = 41,
    ["AB_IcePrincess_Name"] = 41,
    ["NewChat_ChatRoomTitle"] = 41,
    ["Bnb_Count_Tips"] = 41,
    ["Bnb_Power_Tips"] = 41,
    ["Bnb_Speed_Tips"] = 41,
    ["Bnb_Limitation_Tips"] = 41,
    ["Common_ClickLikeSuccess"] = 41,
    ["PlayerInfo_NoDataForSuitSearch"] = 41,
    ["PlayerInfo_BaseInfo_HasCopyUID"] = 41,
    ["Common_SeasonNotOpenRank"] = 41,
    ["CustomRoom_CustomRoom"] = 41,
    ["Activity_CheckIn_CanSignTime"] = 41,
    ["Activity_CheckIn_SignNotCount"] = 41,
    ["Home_System_Close"] = 41,
    ["Home_VisitSystem_Close"] = 41,
    ["CustomRoom_TeamMemberLeaveRoomTip"] = 41,
    ["Lobby_CanNotBagPropItem"] = 41,
    ["friend_RecommenStr_MayBeIntersted"] = 41,
    ["friend_ApplyStr_WantToKnow"] = 41,
    ["friend_ApplyStr_Search"] = 41,
    ["Common_GetLocation"] = 41,
    ["friend_State_text01"] = 41,
    ["friend_State_text02"] = 41,
    ["friend_State_text03"] = 41,
    ["friend_Online_Notice"] = 41,
    ["CustomRoom_LevelSelect_AllRandom"] = 41,
    ["CustomRoom_LevelSelect_SomeRandom"] = 41,
    ["CustomRoom_LevelSelect_TargetOne"] = 41,
    ["UI_Recharge_CutGiftTotalCanBuy"] = 41,
    ["UGC_Editor_MapRecmond"] = 41,
    ["UGC_Editor_MapRecmond_Hot"] = 41,
    ["UI_Recharge_WxMiniGamePayFail"] = 41,
    ["UGC_WarningMsg_EventExceedLimit"] = 41,
    ["Mode_InCustomRoom"] = 41,
    ["UGCRoom_AutoExit_Unready"] = 41,
    ["UGCRoom_Exit_Countdown"] = 41,
    ["UGCRoom_AutoBeginClose"] = 41,
    ["Mall_gift_share_text"] = 41,
    ["Friend_intimate_bottom_notice"] = 41,
    ["BP_mission_start_time"] = 41,
    ["UGCRoom_Ready_Invite"] = 41,
    ["ItemChangeReason_EmailReceive"] = 41,
    ["UGC_AtLess_Two_SpawnPoint"] = 41,
    ["UGC_AtLess_Two_Side"] = 41,
    ["UGC_MapTip_DesEmpty_Tips"] = 41,
    ["Notice_LimitTimeTitle"] = 41,
    ["DDP_NetState_Weak"] = 41,
    ["Team_KickSuccess"] = 41,
    ["MCL_LifeLongLimit"] = 41,
    ["MCL_DailyLimit"] = 41,
    ["MCL_WeeklyLimit"] = 41,
    ["MCL_MonthlyLimit"] = 41,
    ["MCL_YearlyLimit"] = 41,
    ["Friend_intimate_gift_text"] = 41,
    ["UgcRoom_Camp"] = 41,
    ["UGC_GroupPublish_EditOption_TipsInfo"] = 41,
    ["GameCorridor_MST_Subscribe"] = 41,
    ["UgcRoom_InsufficientPosition"] = 41,
    ["__NR3E_All_Text_Begin__"] = 41,
    ["InLevel_NR3E_GiveUpVote"] = 41,
    ["InLevel_NR3E0_Pretender"] = 41,
    ["InLevel_NR3E1_Searcher"] = 41,
    ["InLevel_NR3E0_PretenderGameTarget"] = 41,
    ["InLevel_NR3E0_SearcherGameTarget"] = 41,
    ["InLevel_NR3E0_PretenderSkillDes"] = 41,
    ["InLevel_NR3E0_PretenderGameTips"] = 41,
    ["InLevel_NR3E0_PretenderGameTips1"] = 41,
    ["InLevel_NR3E0_SearcherGameTips"] = 41,
    ["InLevel_NR3E0_UserItem_Scan"] = 41,
    ["InLevel_NR3E0_UserItem_Exploration"] = 41,
    ["InLevel_NR3E1_ResiduePretender"] = 41,
    ["InLevel_NR3E1_Pertendered"] = 42,
    ["InLevel_NR3E1_SearcherArrestCount"] = 42,
    ["InLevel_NR3E0_Rule"] = 42,
    ["InLevel_NR3E1_SearcherTarget"] = 42,
    ["InLevel_NR3E1_PretenderTarget"] = 42,
    ["InLevel_NR3E1_SearcherGameTips"] = 42,
    ["InLevel_NR3E1_PretenderGameTips"] = 42,
    ["InLevel_NR3E1_PretendSkillName"] = 42,
    ["InLevel_NR3E1_HideSkillName"] = 42,
    ["InLevel_NR3E1_SearcherSkillName"] = 42,
    ["InLevel_NR3E1_DetectSkillName"] = 42,
    ["InLevel_NR3E1_OnlySearcherGet"] = 42,
    ["InLevel_NR3E1_SkillCD"] = 42,
    ["InLevel_NR3E1_PowerMax"] = 42,
    ["InLevel_NR3E1_DetctTargetArrested"] = 42,
    ["InLevel_NR3E1_GameFinishTips1"] = 42,
    ["InLevel_NR3E1_GameFinishTips2"] = 42,
    ["InLevel_NR3E1_FindDetectItem"] = 42,
    ["InLevel_NR3E1_DefineBackSquare"] = 42,
    ["InLevel_NR3E1_BackSquare_WinTips"] = 42,
    ["InLevel_NR3E1_Arrested"] = 42,
    ["InLevel_NR3E1_ArrestPlayer"] = 42,
    ["InLevel_NR3E2_ThrowSkillName"] = 42,
    ["InLevel_NR3E2_TarpSkillName"] = 42,
    ["InLevel_NR3E2_AidSkillName"] = 42,
    ["InLevel_NR3E2_ShootSkillName"] = 42,
    ["InLevel_NR3E2_PropSkillName"] = 42,
    ["InLevel_NR3E2_TrapNull"] = 42,
    ["InLevel_NR3E2_DollAllOut"] = 42,
    ["InLevel_NR3E2_BadGuyAllOut"] = 42,
    ["NR3E_EndReason_1"] = 42,
    ["NR3E_EndReason_2"] = 42,
    ["NR3E_EndReason_51"] = 42,
    ["NR3E_EndReason_52"] = 42,
    ["NR3E_EndReason_53"] = 42,
    ["InLevel_NR3E_OutTips1"] = 42,
    ["InLevel_NR3E_OutTips2"] = 42,
    ["InLevel_NR3E_OutTips3"] = 42,
    ["InLevel_NR3E_Talking"] = 42,
    ["InLevel_NR3E_Voting"] = 42,
    ["InLevel_NR3E_Voted"] = 42,
    ["InLevel_NR3E_IsGiveUpVoted"] = 42,
    ["InLevel_NR3E_TalkState"] = 42,
    ["InLevel_NR3E_VoteState"] = 42,
    ["InLevel_NR3E_BeginSay"] = 42,
    ["InLevel_NR3E_ChooseOutDoll"] = 42,
    ["InLevel_NR3ERoleDes_Hunter"] = 42,
    ["InLevel_NR3ERoleDes_Host"] = 42,
    ["InLevel_NR3ERoleDes_Angle"] = 42,
    ["InLevel_NR3ERoleDes_People"] = 42,
    ["InLevel_NR3ERoleName_Hunter"] = 42,
    ["InLevel_NR3ERoleName_Angle"] = 42,
    ["InLevel_NR3ERoleName_Host"] = 42,
    ["InLevel_NR3ERoleTarget_Hunter"] = 42,
    ["InLevel_NR3ERoleTarget_Common"] = 42,
    ["InLevel_NR3ESkillDes_Hunter"] = 42,
    ["InLevel_NR3ESkillDes_Host"] = 42,
    ["InLevel_NR3ESkillDes_Angle"] = 42,
    ["InLevel_NR3E_SliderSpeed_OneParam"] = 42,
    ["InLevel_NR3E_TaskDes"] = 42,
    ["InLevel_NR3E_CompleteFireMission"] = 42,
    ["InLevel_NR3E_CompleteFogMission"] = 42,
    ["InLevel_NR3E_CampInfo"] = 42,
    ["InLevel_NR3E_ChampionTitle"] = 42,
    ["InLevel_NR3E_ChampionDes"] = 42,
    ["InLevel_NR3E_ScoreProtect"] = 42,
    ["InLevel_NR3E_RankProtect"] = 42,
    ["InLevel_NR3E_FristGameRankProtect"] = 42,
    ["InLevel_NR3E2_Settings1"] = 42,
    ["InLevel_NR3E2_Settings2"] = 42,
    ["InLevel_NR3E3_ControlPad_SideA1"] = 42,
    ["InLevel_NR3E3_ControlPad_SideOther1"] = 42,
    ["InLevel_NR3E3_ControlPad_SideOther2"] = 42,
    ["InLevel_NR3E3_ControlPad_SideOther3"] = 42,
    ["InLevel_NR3E3_ControlPad_SideOther4"] = 42,
    ["InLevel_NR3E3_Rule1"] = 42,
    ["InLevel_NR3E3_Rule2"] = 42,
    ["InLevel_NR3E3_Rule3"] = 42,
    ["InLevel_NR3E3_Rule4"] = 42,
    ["InLevel_NR3E3_Rule5"] = 42,
    ["InLevel_NR3E3_Rule6"] = 42,
    ["InLevel_NR3E3_Rule7"] = 42,
    ["InLevel_NR3E3_Rule8"] = 42,
    ["InLevel_NR3E3_VoiceMicroCell"] = 42,
    ["InLevel_NR3E31"] = 42,
    ["InLevel_NR3E32"] = 42,
    ["InLevel_NR3E3_GameFinish"] = 42,
    ["InLevel_Chat_NR3E3Meeting"] = 42,
    ["InLevel_Chat_NR3E3MeetingInput"] = 42,
    ["InLevel_NR3E3_Meeting"] = 42,
    ["InLevel_NR3E3_MeetingStateChange"] = 42,
    ["InLevel_NR3E3_MeetingStateChange1"] = 42,
    ["InLevel_NR3E3_MeetingStateChange2"] = 42,
    ["InLevel_NR3E3_MeetingStateChange3"] = 42,
    ["InLevel_NR3E3_MeetingStateChange4"] = 42,
    ["InLevel_NR3E3_ChildTask_103"] = 42,
    ["InLevel_NR3E3_ChildTask_103_1"] = 42,
    ["InLevel_NR3E3_Task_1"] = 42,
    ["InLevel_NR3E3_Task_2"] = 42,
    ["InLevel_NR3E3_TaskPanel_EmergentMeeting"] = 42,
    ["InLevel_NR3E3_GameStart"] = 43,
    ["InLevel_NR3E3_GameStart1"] = 43,
    ["InLevel_NR3E3_GameStart2"] = 43,
    ["InLevel_NR3E3_JobInfo"] = 43,
    ["InLevel_NR3E3_JobInfo_1"] = 43,
    ["InLevel_NR3E3_JobInfo_2"] = 43,
    ["InLevel_NR3E3_JobInfo_3"] = 43,
    ["InLevel_NR3E3_JobInfo_4"] = 43,
    ["InLevel_NR3E3_JobInfo_5"] = 43,
    ["InLevel_NR3E3_JobInfo_6"] = 43,
    ["InLevel_NR3E3_JobInfo_7"] = 43,
    ["InLevel_NR3E3_KnockedOut"] = 43,
    ["InLevelNR3E2FinishName1"] = 43,
    ["InLevelNR3E2FinishName2"] = 43,
    ["InLevelNR3E1FinishName1"] = 43,
    ["InLevelNR3E1FinishName2"] = 43,
    ["InLevelNR3E1FinishWin1"] = 43,
    ["InLevelNR3E1FinishWin2"] = 43,
    ["InLevelNR3E3FinishWinCamp1"] = 43,
    ["InLevelNR3E3FinishWinCamp2"] = 43,
    ["BP_NR3E3MeetingManager"] = 43,
    ["NR3E2_DollsRunComponent1"] = 43,
    ["NR3E2_DollsRunComponent2"] = 43,
    ["NR3E2_DollsRunComponent3"] = 43,
    ["NR3E2_DollsRunComponent4"] = 43,
    ["NR3E2_DollsRunComponent5"] = 43,
    ["NR3E2_DollsRunComponent6"] = 43,
    ["NR3E2_DollsRunComponent7"] = 43,
    ["NR3E2_DollsRunComponent8"] = 43,
    ["NR3E2_DollsRunComponent9"] = 43,
    ["NR3E2_AIWarningBubbleTips"] = 43,
    ["NR3E3_BaseComponent"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel1"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel2"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel3"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel4"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel5"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel6"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel7"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel8"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel9"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel10"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel11"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel12"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel13"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel14"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel15"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel16"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel17"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel18"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel19"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel20"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel21"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel22"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel23"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel24"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel25"] = 43,
    ["UI_InLevel_NR3E3_TaskPanel26"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes1"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes2"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes3"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes4"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes5"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes6"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes7"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes8"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes9"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes10"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes11"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes12"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes13"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes14"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes15"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes16"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes17"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes18"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes19"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes20"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes21"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes22"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes23"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes24"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes25"] = 43,
    ["UI_InLevel_NR3E3_TaskPanelDes26"] = 43,
    ["UI_InLevel_NR3E3_HoleName1"] = 43,
    ["UI_InLevel_NR3E3_HoleName2"] = 43,
    ["UI_InLevel_NR3E3_HoleName3"] = 43,
    ["UI_InLevel_NR3E3_HoleName4"] = 43,
    ["UI_InLevel_NR3E3_HoleName5"] = 43,
    ["UI_InLevel_NR3E3_HoleName6"] = 43,
    ["UI_InLevel_NR3E3_HoleName7"] = 43,
    ["UI_InLevel_NR3E3_HoleName8"] = 43,
    ["UI_InLevel_NR3E3_MapName1"] = 43,
    ["UI_InLevel_NR3E3_MapName2"] = 43,
    ["UI_InLevel_NR3E3_MapName3"] = 43,
    ["UI_InLevel_NR3E3_MapName4"] = 43,
    ["UI_InLevel_NR3E3_MapName5"] = 43,
    ["UI_InLevel_NR3E3_MapName6"] = 43,
    ["UI_InLevel_NR3E3_MapName7"] = 43,
    ["UI_InLevel_NR3E3_MapName8"] = 43,
    ["UI_InLevel_NR3E3_MapName9"] = 44,
    ["UI_InLevel_NR3E3_MapName10"] = 44,
    ["UI_InLevel_NR3E3_MapName11"] = 44,
    ["UI_InLevel_NR3E3_MapName12"] = 44,
    ["UI_InLevel_NR3E3_MapName13"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_1_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_1_2"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_1_3"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_2_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_2_2"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_2_3"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_3_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_3_2"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_3_3"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_4_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_4_2"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_4_3"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_5_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_5_2"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_6_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_6_2"] = 44,
    ["NR3E3_AIMessage_FirstReport_Taking_6_3"] = 44,
    ["NR3E3_AIMessage_FirstReport_Voting_1_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Voting_2_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Voting_2_2"] = 44,
    ["NR3E3_AIMessage_FirstReport_Voting_4_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Voting_5_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Voting_6_1"] = 44,
    ["NR3E3_AIMessage_FirstReport_Voting_6_2"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_1_1"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_1_2"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_1_3"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_2_1"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_2_2"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_2_3"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_3_1"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_3_2"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_3_3"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_4_1"] = 44,
    ["NR3E3_AIMessage_SecondReport_Taking_4_2"] = 44,
    ["NR3E3_AIMessage_SecondReport_Voting_1_1"] = 44,
    ["NR3E3_AIMessage_SecondReport_Voting_2_1"] = 44,
    ["NR3E3_AIMessage_SecondReport_Voting_4_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_1_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_1_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_2_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_2_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_3_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_3_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_3_3"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_4_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_4_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_5_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_5_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_6_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_6_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Taking_7_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Voting_1_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Voting_1_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Voting_2_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Voting_2_2"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Voting_3_1"] = 44,
    ["NR3E3_AIMessage_PlayerReport_Voting_4_1"] = 44,
    ["UI_InLevel_NR3E3_Meeting1"] = 44,
    ["NR3E3_NoVoiceAuthorit"] = 44,
    ["NR3E3_PermissionPrompt"] = 44,
    ["NR3E3_PermissionConfirm"] = 44,
    ["NR3E3Meeting_Discuss"] = 44,
    ["NR3E3Meeting_DeathTip"] = 44,
    ["InLevel_NR3E2_VoiceMicroCell"] = 44,
    ["NR3E2_DollsRunComponent10"] = 44,
    ["UI_InLevel_NR3E3_UrgentTips"] = 44,
    ["NR3E_EndReason_101_Tips"] = 44,
    ["NR3E_EndReason_102_Tips"] = 44,
    ["NR3E_EndReason_104_Tips"] = 44,
    ["NR3E_EndReason_101"] = 44,
    ["NR3E_EndReason_102"] = 44,
    ["NR3E_EndReason_103"] = 44,
    ["NR3E_EndReason_104"] = 44,
    ["NR3E_EndReasonType1"] = 44,
    ["NR3E_EndReasonType2"] = 44,
    ["NR3E_EndReasonType3"] = 44,
    ["NR3E_EndReasonType4"] = 44,
    ["NR3E_TransferBomb"] = 44,
    ["NR3E_TransferBomb_IconText"] = 44,
    ["NR3E_PlacedBomb_IconText"] = 44,
    ["InLevel_NR3E3_Rule9"] = 44,
    ["InLevel_NR3E3_Rule10"] = 44,
    ["InLevel_NR3E3_Rule11"] = 44,
    ["InLevel_NR3E3_Rule12"] = 44,
    ["InLevel_NR3E3_Rule13"] = 44,
    ["InLevel_NR3E3_Rule14"] = 44,
    ["InLevel_NR3E3_Rule15"] = 44,
    ["InLevel_NR3E3_Rule16"] = 44,
    ["InLevel_NR3E3_Rule17"] = 44,
    ["InLevel_NR3E3_Rule18"] = 44,
    ["InLevel_NR3E3_Rule19"] = 44,
    ["InLevel_NR3E3_Rule20"] = 44,
    ["InLevel_NR3E3_Rule21"] = 44,
    ["InLevel_NR3E3_Rule22"] = 44,
    ["InLevel_NR3E3_Rule23"] = 45,
    ["InLevel_NR3E3_Rule24"] = 45,
    ["InLevel_NR3E3_Rule25"] = 45,
    ["InLevel_NR3E3_Rule26"] = 45,
    ["InLevel_NR3E3_Rule27"] = 45,
    ["InLevel_NR3E3_Rule28"] = 45,
    ["InLevel_NR3E3_Rule29"] = 45,
    ["InLevel_NR3E3_Rule30"] = 45,
    ["InLevel_NR3E3_Rule31"] = 45,
    ["InLevel_NR3E3_Rule32"] = 45,
    ["InLevel_NR3E3_Rule33"] = 45,
    ["InLevel_NR3E3_Rule34"] = 45,
    ["InLevel_NR3E3_Rule35"] = 45,
    ["InLevel_NR3E3_Rule36"] = 45,
    ["InLevel_NR3E3_Rule37"] = 45,
    ["UI_InLevel_NR3E3_Bell"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskTips1"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskTips2"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskItem1"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskItem2"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskItem3"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskItem4"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskItem5"] = 45,
    ["UI_InLevel_NR3E3_UrgentTaskItem6"] = 45,
    ["UI_InLevel_NR3E3_VocationItemTip"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft1"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft2"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft3"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft4"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft5"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft6"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft7"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft8"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft9"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft10"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft11"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft12"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft13"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft14"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft15"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft16"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft17"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft18"] = 45,
    ["UI_InLevel_NR3E3_MeetingLeft19"] = 45,
    ["UI_InLevel_NR3E3_VocationTitle"] = 45,
    ["UI_InLevel_NR3E3_SheriffAttackDoll"] = 45,
    ["UI_InLevel_NR3E3_Task1"] = 45,
    ["UI_InLevel_NR3E3_Task2"] = 45,
    ["UI_InLevel_NR3E3_Task3"] = 45,
    ["UI_InLevel_NR3E3_Task4"] = 45,
    ["UI_InLevel_NR3E3_Task5"] = 45,
    ["UI_InLevel_NR3E3_Task6"] = 45,
    ["UI_InLevel_NR3E3_DetectTargetDisappear"] = 45,
    ["UI_InLevel_NR3E3_DetectTargetTooFarAway"] = 45,
    ["UI_InLevel_NR3E3_DetectPleaseComeCloser"] = 45,
    ["UI_InLevel_NR3E3_DetectResult"] = 45,
    ["UI_InLevel_NR3E3_DetectNotTarget"] = 45,
    ["UI_InLevel_NR3E2_UndercoverTips"] = 45,
    ["UI_InLevel_NR3E2_UndercoverMoment1"] = 45,
    ["UI_InLevel_NR3E2_UndercoverMoment"] = 45,
    ["UI_InLevel_NR3E3_GameStartTip"] = 45,
    ["UI_InLevel_NR3E3_Bell1"] = 45,
    ["UI_InLevel_NR3E3_UrgentTask1"] = 45,
    ["UI_InLevel_NR3E3_NotAttackTarget"] = 45,
    ["UI_InLevel_NR3E3_Detect"] = 45,
    ["InLevel_NR3E3_IdentityInformation1"] = 45,
    ["InLevel_NR3E3_IdentityInformation2"] = 45,
    ["InLevel_NR3E3_IdentityInformation3"] = 45,
    ["InLevel_NR3E3_IdentityInformation4"] = 45,
    ["InLevel_NR3E3_IdentityInformation5"] = 45,
    ["InLevel_NR3E3_IdentityInformation6"] = 45,
    ["InLevel_NR3E3_IdentityInformation7"] = 45,
    ["InLevel_NR3E3_IdentityInformation8"] = 45,
    ["InLevel_NR3E3_NR3ESkillDes1"] = 45,
    ["InLevel_NR3E3_NR3ESkillDes2"] = 45,
    ["InLevel_NR3E3_MeetingSysTip1"] = 45,
    ["InLevel_NR3E3_MeetingSysTip2"] = 45,
    ["InLevel_NR3E3_MeetingSysTip3"] = 45,
    ["InLevel_NR3E3_MeetingSysTip4"] = 45,
    ["InLevel_NR3E3_MeetingSysTip5"] = 45,
    ["InLevel_NR3E3_MeetingSysTip6"] = 45,
    ["InLevel_NR3E3_MeetingSysTip7"] = 45,
    ["NR3E2_DollsRunComponent11"] = 45,
    ["NR3E2_DollsRunComponent12"] = 45,
    ["NR3E1_SkillName"] = 45,
    ["UI_InLevel_NR3E3_MarkNotTarget"] = 45,
    ["UI_InLevel_NR3E3_WarriorTip"] = 45,
    ["InLevel_NR3E3_Rule38"] = 45,
    ["InLevel_NR3E3_Rule39"] = 45,
    ["InLevel_NR3E3_Rule40"] = 45,
    ["InLevel_NR3E3_Rule41"] = 45,
    ["InLevel_NR3E3_Rule42"] = 45,
    ["InLevel_NR3E3_Rule43"] = 45,
    ["InLevel_NR3E3_IdentityInformation9"] = 45,
    ["InLevel_NR3E3_IdentityInformation10"] = 45,
    ["InLevel_NR3E3_IdentityInformation11"] = 45,
    ["InLevel_NR3E3_IdentityInformation12"] = 45,
    ["InLevel_NR3E3_IdentityInformation13"] = 45,
    ["InLevel_NR3E3_IdentityInformation14"] = 45,
    ["InLevel_NR3E3_IdentityInformation15"] = 45,
    ["UI_InLevel_NR3E3_Rule1_GameInfo"] = 46,
    ["UI_InLevel_NR3E3_Rule1_HideWinInfo"] = 46,
    ["UI_InLevel_NR3E3_Rule1_HideSkillInfo1"] = 46,
    ["UI_InLevel_NR3E3_Rule1_HideSkillInfo2"] = 46,
    ["UI_InLevel_NR3E3_Rule1_HideSkillInfo3"] = 46,
    ["UI_InLevel_NR3E3_Rule1_SeekWinInfo"] = 46,
    ["UI_InLevel_NR3E3_Rule1_SeekSkillInfo1"] = 46,
    ["UI_InLevel_NR3E3_Rule1_SeekSkillInfo2"] = 46,
    ["UI_InLevel_NR3E3_Rule1_SeekSkillInfo3"] = 46,
    ["UI_InLevel_NR3E3_Rule2_GameInfo"] = 46,
    ["UI_InLevel_NR3E3_Rule2_HideWinInfo"] = 46,
    ["UI_InLevel_NR3E3_Rule2_HideSkillInfo1"] = 46,
    ["UI_InLevel_NR3E3_Rule2_HideSkillInfo2"] = 46,
    ["UI_InLevel_NR3E3_Rule2_HideSkillInfo3"] = 46,
    ["UI_InLevel_NR3E3_Rule2_SeekWinInfo"] = 46,
    ["UI_InLevel_NR3E3_Rule2_SeekSkillInfo1"] = 46,
    ["UI_InLevel_NR3E3_Rule2_SeekSkillInfo2"] = 46,
    ["UI_InLevel_NR3E3_Rule2_SeekSkillInfo3"] = 46,
    ["UI_InLevel_NR3E3_LoseOut"] = 46,
    ["InLevel_NR3E3_Rule44"] = 46,
    ["InLevel_NR3E3_Rule45"] = 46,
    ["InLevel_NR3E3_Rule46"] = 46,
    ["InLevel_NR3E3_Rule47"] = 46,
    ["InLevel_NR3E3_Rule48"] = 46,
    ["InLevel_NR3E3_Rule49"] = 46,
    ["InLevel_NR3E3_Rule50"] = 46,
    ["InLevel_NR3E3_JobInfo_Zoli"] = 46,
    ["UI_InLevel_NR3E3_NotTarget"] = 46,
    ["UI_InLevel_NR3E3_FakerTaskTip"] = 46,
    ["UI_InLevel_NR3E3_BombFire"] = 46,
    ["InLevel_NR3E3_BountyTarget_Changed"] = 46,
    ["InLevel_NR3E3_NeutralCamp_CompleteMissionFailed"] = 46,
    ["InLevel_NR3E3_NeutralCamp_DeathNotify"] = 46,
    ["UI_InLevel_NR3E3_Rule3_GameInfo"] = 46,
    ["UI_InLevel_NR3E3_Rule3_GameTypeInfo1"] = 46,
    ["UI_InLevel_NR3E3_Rule3_GameTypeInfo2"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Target_Normal"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Target_Killer"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Target_Neutral"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Normal_Task"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Urgency_Task"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Report"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Meeting"] = 46,
    ["UI_InLevel_NR3E3_Rule3_SoulState"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Bell"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Broadcas"] = 46,
    ["UI_InLevel_NR3E3_Rule3_Radar"] = 46,
    ["UI_InLevel_NR3E3_Rule3_HiddenPath"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo1"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo2"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo3"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo4"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo5"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo6"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo7"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo8"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo9"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo10"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo11"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo12"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo13"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo14"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo15"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo16"] = 46,
    ["UI_InLevel_NR3E3_Rule3_RoleInfo17"] = 46,
    ["UI_InLevel_NR3E3_NoSelect"] = 46,
    ["NR3E3_AIMessage_SendChat_1"] = 46,
    ["NR3E3_AIMessage_SendChat_2"] = 46,
    ["NR3E3_AIMessage_SendChat_3"] = 46,
    ["NR3E3_AIMessage_SendChat_4"] = 46,
    ["NR3E3_AIMessage_SendChat_5"] = 46,
    ["NR3E3_AIMessage_SendChat_6"] = 46,
    ["CustomRoom_NR3E3_RoomTitle1"] = 46,
    ["CustomRoom_NR3E3_RoomTitle2"] = 46,
    ["CustomRoom_NR3E3_RoomTitle3"] = 46,
    ["CustomRoom_NR3E3_Rule1"] = 46,
    ["CustomRoom_NR3E3_Rule2"] = 46,
    ["CustomRoom_NR3E3_Rule3"] = 46,
    ["CustomRoom_NR3E3_Rule4"] = 46,
    ["CustomRoom_NR3E3_Rule5"] = 46,
    ["CustomRoom_NR3E3_Rule6"] = 46,
    ["CustomRoom_NR3E3_JobTitle1"] = 46,
    ["CustomRoom_NR3E3_JobTitle2"] = 46,
    ["CustomRoom_NR3E3_JobBtn"] = 46,
    ["CustomRoom_NR3E3_Tip1"] = 46,
    ["CustomRoom_NR3E3_Tip2"] = 46,
    ["__NR3E_All_Text_End__"] = 46,
    ["UGC_LocationCopySuccess"] = 46,
    ["UGC_Place_Occupy_OutOfOnceRange"] = 46,
    ["UI_RedEnvelope_ActivityDescription"] = 46,
    ["UI_RedEnvelope_NextTurnTimeInterval"] = 46,
    ["UI_RedEnvelope_ThisTurnTimeInterval"] = 46,
    ["UI_RedEnvelope_WaitWhichTurn"] = 46,
    ["UI_RedEnvelope_CurrentTurn"] = 46,
    ["UI_RedEnvelope_OwnQuantity"] = 46,
    ["UI_RedEnvelope_TurnTitle"] = 46,
    ["UI_RedEnvelope_TurnTime"] = 46,
    ["UI_GunGame_Killed_Content"] = 46,
    ["UI_GunGame_ArriveInfo_RankStr"] = 46,
    ["Friend_AddQQFriendTitle"] = 46,
    ["Friend_AddQQFriendDesc"] = 47,
    ["Friend_Send_QQ"] = 47,
    ["Friend_Send_WX"] = 47,
    ["Friend_Share_QQ"] = 47,
    ["Friend_Share_WX"] = 47,
    ["Net_NetDriverAlreadyExists"] = 47,
    ["Net_NetDriverCreateFailure"] = 47,
    ["Net_NetDriverListenFailure"] = 47,
    ["Net_ConnectionLost"] = 47,
    ["Net_ConnectionTimeout"] = 47,
    ["Net_FailureReceived"] = 47,
    ["Net_OutdatedClient"] = 47,
    ["Net_OutdatedServer"] = 47,
    ["Net_PendingConnectionFailure"] = 47,
    ["Net_NetGuidMismatch"] = 47,
    ["Net_NetChecksumMismatch"] = 47,
    ["Xiaowo_MoneyTree_OutOfRange"] = 47,
    ["UI_PlayerInfo_ModRecord_Draw"] = 47,
    ["UI_Recharge_SeasonRecharge_CountNotEnough"] = 47,
    ["Team_In_Match_Invite"] = 47,
    ["UGC_CoCreate_TeamMember_Simple"] = 47,
    ["UGC_CoCreate_Creator_Simple"] = 47,
    ["Mall_Use_Period"] = 47,
    ["ItemChangeReason_QQCash"] = 47,
    ["ItemChangeReason_WXCash"] = 47,
    ["UGC_Object_Cant_SelfBind"] = 47,
    ["UI_UGCFPS_Result_Player_UnRanked"] = 47,
    ["UI_UGCFPS_Result_Team_1"] = 47,
    ["UI_UGCFPS_Result_Team_2"] = 47,
    ["SettingTip_JotstickMode"] = 47,
    ["SettingTip_CharacterFlexibility"] = 47,
    ["UGCRoom_Ready"] = 47,
    ["UGCRoom_GoToRoom_Ready"] = 47,
    ["UI_Login_WxQRCodeDesc"] = 47,
    ["InGameScene_CanNotJump"] = 47,
    ["Common_LoginNetError"] = 47,
    ["ExchangeSeatFail_In"] = 47,
    ["ExchangeSeatFail_WaitCd"] = 47,
    ["UGC_ErrorMsg_LoopControl"] = 47,
    ["UGC_GroupCollect_Successful"] = 47,
    ["UGC_GroupCollect_UpperLimit"] = 47,
    ["UI_RedEnvelope_ReceiveTimeOut"] = 47,
    ["Activity_NotEnough2"] = 47,
    ["NewChat_ForbindHint"] = 47,
    ["UGC_Map_Share_WxCircle_Title"] = 47,
    ["Credit_Message"] = 47,
    ["Credit_Hint_CantSpeak"] = 47,
    ["Credit_ShowDetail"] = 47,
    ["UI_Recharge_Navigation_Title"] = 47,
    ["Activity_LockForCondition"] = 47,
    ["BoxCommunityLoadingHint"] = 47,
    ["BoxCommunityLoadFailed"] = 47,
    ["FriendModel_BatchBanFriend"] = 47,
    ["Emulator_Not_Tencent"] = 47,
    ["Friend_BatchBan_Notice"] = 47,
    ["Friend_BatchBan_NoFriendNotice"] = 47,
    ["Friend_BatchBan_SuccessNotice"] = 47,
    ["UGC_Map_DontSupportTranslate"] = 47,
    ["UGC_Group_DontSupportTranslate"] = 47,
    ["Credit_Message1"] = 47,
    ["Credit_Message2"] = 47,
    ["Credit_Message3"] = 47,
    ["Credit_Message4"] = 47,
    ["Credit_Message5"] = 47,
    ["Credit_Message6"] = 47,
    ["Credit_Message7"] = 47,
    ["platPrivilegesQQAdditional"] = 47,
    ["platPrivilegesWXAdditional"] = 47,
    ["Team_IsJoingingRoom"] = 47,
    ["Team_JoingRoomFailed"] = 47,
    ["NewChat_Translate"] = 47,
    ["NewChat_Translated"] = 47,
    ["NewChat_TranslateFrom"] = 47,
    ["NewChat_TranslateFailed"] = 47,
    ["UGC_Noattachmentsallowed_mirroring"] = 47,
    ["UI_UGCDailyLevelItem_1"] = 47,
    ["UI_Lottery_SeasonSubView_1"] = 47,
    ["Level_GoalText_TeamGoal"] = 47,
    ["UI_UGCEditorCommon_OperateView_1"] = 47,
    ["UI_Mall_Exchange_1"] = 47,
    ["UI_RedEnvelope_Remind_QQ"] = 47,
    ["UI_RedEnvelope_Remind_WX"] = 47,
    ["TimeUtils_M_Const"] = 47,
    ["UI_RedEnvelope_OpenTimeOut"] = 47,
    ["BattlePass_Unlock_ShowLevel"] = 47,
    ["Platform_CanNotEnterGameLive"] = 47,
    ["Activity_Takeaway_ReverseSuccess"] = 47,
    ["ChannelEntranceTips_1"] = 47,
    ["UGC_Editor_LocomotorInteractive_Text"] = 47,
    ["Common_CopyToClipboard_Text"] = 47,
    ["Activity_KingLinkageComing"] = 47,
    ["Activity_SharedAndReceiveQiaoSuit"] = 47,
    ["Activity_SharedAndReceiveSRP"] = 47,
    ["Activity_KingLinkageSuit"] = 47,
    ["UI_Recharge_SeasonRecharge_ActivityNotOpen"] = 47,
    ["UI_Recharge_SeasonRecharge_ActivityConditionNoPass"] = 47,
    ["UI_Recharge_SeasonRecharge_ActivityIsOver"] = 47,
    ["UGC_Editor_HomeElementLocked_Text"] = 47,
    ["Bnb_life_Tips"] = 47,
    ["MonkiBubbleAcquireSuccess"] = 47,
    ["MonkiBubbleAcquireFail"] = 48,
    ["InLevel_FPS_GameResult_TeamWin"] = 48,
    ["InLevel_FPS_GameResult_End"] = 48,
    ["InLevel_FPS_GameResult_SingleWin"] = 48,
    ["UGC_Waypoint_Cannot_Mirror"] = 48,
    ["Bnb_fail_Tips"] = 48,
    ["InLevel_FPS_FinalAcountRank"] = 48,
    ["Pandora_OpenApp_Fail_Tips"] = 48,
    ["UI_Common_BackToGame"] = 48,
    ["UI_UGC_MapVersion_Abort_iOS"] = 48,
    ["UI_UGC_MapVersion_PassButTips_iOS"] = 48,
    ["UI_UGC_MapVersion_Abort_Android"] = 48,
    ["UI_UGC_MapVersion_PassButTips_Android"] = 48,
    ["UI_UGC_MapVersion_Abort_ConfirmBtn"] = 48,
    ["UI_UGC_MapVersion_Abort_RebootBtn"] = 48,
    ["Tips_Gameplay_WaitingTime"] = 48,
    ["UI_Gameplay_Waiting"] = 48,
    ["UI_Gameplay_WaitingPlayerNumber"] = 48,
    ["UI_Gameplay_WaitingTime"] = 48,
    ["UI_Gameplay_ReadyForPlay"] = 48,
    ["Tips_UGC_ActorManager_CreateTag_Error"] = 48,
    ["UGC_Map_AutoSave_Fail"] = 48,
    ["Common_Minute"] = 48,
    ["UGC_CoCreate_HeartBeat_NoEditing"] = 48,
    ["UGC_CoCreate_Member_KickOut"] = 48,
    ["UGC_Map_AutoSave_Success"] = 48,
    ["UGC_Map_AutoSave_ConflictResolution_Reconfirm"] = 48,
    ["UGC_Map_AutoSave_ConflictResolution_Continue"] = 48,
    ["UGC_Map_AutoSave_ConflictResolution_Reselect"] = 48,
    ["UGC_Feautre_Cannot_Open"] = 48,
    ["UGC_GroupLike_ResVersionCheck_NotPass"] = 48,
    ["UGC_GroupCollect_ResVersionCheck_NotPass"] = 48,
    ["DrawReward_MultiRaffleKeyTip"] = 48,
    ["UGC_Editor_NotPublishMap"] = 48,
    ["UGC_Editor_Exit_TaskTips"] = 48,
    ["UI_Gameplay_WaitingTips"] = 48,
    ["UI_Team_Invite_BackSend_Succeed"] = 48,
    ["UI_Team_Invite_Send_Failed"] = 48,
    ["UI_Not_Support_Hint"] = 48,
    ["UGC_CopyCannotOverLimit"] = 48,
    ["UGC_CopyInPlaceCannotOverLimit"] = 48,
    ["UGC_CombinedMirrorExamine"] = 48,
    ["UGC_BindingMirrorExamine"] = 48,
    ["UGC_Editor_CollisionPreview_Text"] = 48,
    ["Permission_Live_Voice_Hint"] = 48,
    ["Permission_Save_Pic_Hint"] = 48,
    ["Permission_Camera_Hint"] = 48,
    ["Permission_Camera_Failed"] = 48,
    ["UGC_Search_Clear_History"] = 48,
    ["UGC_Editor_SetFixedXBMsg"] = 48,
    ["UGC_Editor_TranslucentSelection_ForbidMsg"] = 48,
    ["UGC_Editor_Selection_ForbidMsg"] = 48,
    ["UGC_Transform_NOParToPaste"] = 48,
    ["UGC_Transform_SucCopiedAllPar"] = 48,
    ["UGC_Transform_SucPastedAllPar"] = 48,
    ["UGC_Transform_SucPastedLocaPar"] = 48,
    ["UGC_Transform_SucPastedRotPar"] = 48,
    ["UGC_AnimMove_SucAllAinimPar"] = 48,
    ["UGC_AnimMove_SucPastedAnimPar"] = 48,
    ["UGC_AnimMove_SucCopAnimPar"] = 48,
    ["UGC_AnimMove_KeyframItemMaxLimit"] = 48,
    ["UGC_AnimMove_AdvanceItemMaxLimit"] = 48,
    ["UGC_Transform_SucPastedScalePar"] = 48,
    ["UGC_Transform_CannotNegScale"] = 48,
    ["UI_UGC_StarCruise_Version_Item"] = 48,
    ["UI_UGC_StarCruise_Version_Map"] = 48,
    ["UGC_StarCruise_SelfExit"] = 48,
    ["UGC_StarCruise_TimeOver"] = 48,
    ["UGCVehicle_OperateStatus1"] = 48,
    ["UGCVehicle_OperateStatus2"] = 48,
    ["UGCVehicle_OperateStatus3"] = 48,
    ["UGCVehicle_OperateStatus4"] = 48,
    ["UGCVehicle_OperateStatus5"] = 48,
    ["UGCVehicle_OperateStatus6"] = 48,
    ["UGCVehicle_OperateStatus7"] = 48,
    ["UGCVehicle_OperateStatus8"] = 48,
    ["UGCVehicle_NoOperateOnVehicle"] = 48,
    ["Activity_SuperLinear_IsInEnd"] = 48,
    ["Activity_SuperLinear_CurMile"] = 48,
    ["UGC_Editor_CollisionOverflow_Text"] = 48,
    ["UGC_Editor_CollisionBatchSet_Text"] = 48,
    ["UGC_Locomotion_NopParToPaste"] = 48,
    ["UGC_Locomotion_SucCopiedAllPar"] = 48,
    ["UGC_Locomotion_SucCopiedPar"] = 48,
    ["UGC_Locomotion_SucPastedAllPar"] = 48,
    ["Common_String_WB_Not_Installed"] = 48,
    ["Common_String_XHS_Not_Installed"] = 48,
    ["Common_String_Net_Error"] = 48,
    ["UI_Activity_Zupai_GainExtraPointsCount"] = 48,
    ["UI_Activity_Zupai_GainExtraPointsCount_ReturingCooperation"] = 48,
    ["UI_Activity_Zupai_GainExtraPointsCount_ReturingPrivilege"] = 48,
    ["UI_Activity_Zupai_Grading"] = 48,
    ["UI_Activity_Zupai_NotForNow"] = 48,
    ["UI_Activity_Zupai_NotLosePointsCount"] = 48,
    ["UI_Activity_Zupai_Timing"] = 48,
    ["UI_Activity_Zupai_NotLosePoints"] = 48,
    ["UI_Activity_Zupai_GainExtraPoints"] = 48,
    ["UI_Activity_Zupai_GainMultiPoints"] = 48,
    ["UGC_Editor_AdvanceItemMaxLimit"] = 48,
    ["UGC_WayPoint_Cannot_Operate_MultiSelect"] = 48,
    ["UGC_WayPoint_Cannot_SelectOther_MultiSelect"] = 49,
    ["FireworksUseCreditCheckFail"] = 49,
    ["UGC_Creator_AndOthers"] = 49,
    ["UGC_GatherStar_RefreshTime"] = 49,
    ["UGC_GatherStar_MapRefreshed"] = 49,
    ["GiftGiveLevelRequest"] = 49,
    ["Ugc_MapLaunch_Topic_Sensitive"] = 49,
    ["UGC_MultiplePlayer_Tab_MatchHall"] = 49,
    ["UGC_MultiplePlayer_Tab_RoomHall"] = 49,
    ["UGC_Home_Element_Lock"] = 49,
    ["ModuleCommunity_HotRecommend"] = 49,
    ["ModuleCommunity_ModuleResource"] = 49,
    ["UGC_Resource_GiveLike"] = 49,
    ["UGC_Resource_Use"] = 49,
    ["UGC_SearchResult_Empty"] = 49,
    ["UGC_Resource_AddBag"] = 49,
    ["UGC_Resource_RemoveBag"] = 49,
    ["UGC_Resource_LablesLimit"] = 49,
    ["UGC_Resource_DelLimit"] = 49,
    ["UGC_Resource_PasteEmpty"] = 49,
    ["UGC_ResourceDetailPanel_UGCIDStrReplicated"] = 49,
    ["UGC_ResourcePublishedInfoModify_Successfully"] = 49,
    ["UGC_ResType_CantUse"] = 49,
    ["UI_UGCEditor_Bag_Empty_ResMine"] = 49,
    ["UI_UGCEditor_Bag_Empty_Default"] = 49,
    ["UI_UGCEditor_Bag_Empty_ResLib"] = 49,
    ["UI_Countdown_Main_Paper"] = 49,
    ["UGC_Roadpoint_Cannot_Mirror"] = 49,
    ["UI_Activity_Zupai_ChangciCount"] = 49,
    ["UI_Activity_Zupai_ChangciTequan"] = 49,
    ["UI_Activity_Zupai_ChangciShengyu"] = 49,
    ["UGC_Editor_StarParty"] = 49,
    ["UGC_Editor_StarParty_MapOpen"] = 49,
    ["UGC_Editor_StarParty_MapClose"] = 49,
    ["UGC_Object_In_FilterMode"] = 49,
    ["UGC_LockMap_Edit"] = 49,
    ["UGC_LockMap_Outshelf"] = 49,
    ["UGC_LockMap_Exit"] = 49,
    ["UGC_Search_Map_Count"] = 49,
    ["UGC_SkyBox_Txt_Fog"] = 49,
    ["ChatModule_WorldAndLobbyNotOpenTips1"] = 49,
    ["ChatModule_WorldAndLobbyNotOpenTips2"] = 49,
    ["UGC_Editor_Widget_Image"] = 49,
    ["LuckyStar_CoinNotEngough"] = 49,
    ["LuckyStar_RewardDes_1"] = 49,
    ["LuckyStar_AlreadyReceiveByOthers"] = 49,
    ["LuckyStar_NotUnluckRound"] = 49,
    ["LuckyStar_StarNotHave"] = 49,
    ["LuckyStar_AlreadyReceiveFromOthers"] = 49,
    ["UGC_ErrorMsg_CanNotAsTemplate"] = 49,
    ["UGC_RoomList_PassMarks1"] = 49,
    ["UGC_RoomList_PassMarks2"] = 49,
    ["UGC_RoomList_PlayDuration1"] = 49,
    ["UGC_RoomList_PlayDuration2"] = 49,
    ["UGC_RoomList_PlayDuration3"] = 49,
    ["UGC_RoomList_PlayDuration4"] = 49,
    ["UGC_RoomList_PlayDuration5"] = 49,
    ["UI_RedEnvelope_SharedSuccessfully"] = 49,
    ["UI_RedEnvelope_GotChanceByClickLinker"] = 49,
    ["UI_RedEnvelope_YourShareBeenClicked"] = 49,
    ["Matching_Room_Intercept"] = 49,
    ["Matching_Leader_Change"] = 49,
    ["Matching_Leader_Start"] = 49,
    ["Matching_Number_Over"] = 49,
    ["Download_Map_Wait"] = 49,
    ["Download_Map_Fail_Self"] = 49,
    ["Download_Map_Fail_Leader"] = 49,
    ["Client_Low_Leader"] = 49,
    ["Downloading_UGC_Leader"] = 49,
    ["Matching_Faction_Number_Over"] = 49,
    ["UI_RedEnvelope_CountInfo"] = 49,
    ["UI_RedEnvelope_ShareCountInfo"] = 49,
    ["UI_RedEnvelope_ShareLinkCountInfo"] = 49,
    ["UGC_Editor_Prefab_ApplyToAll_Title"] = 49,
    ["UGC_Editor_Prefab_ApplyToAll_Tips"] = 49,
    ["UGC_Editor_Prefab_SaveToCustom_Title"] = 49,
    ["UGC_Editor_Prefab_SaveToCustom_Tips"] = 49,
    ["UGC_Editor_Prefab_Delete_Tips"] = 49,
    ["UGC_Editor_Prefab_Reset_Title"] = 49,
    ["UGC_Editor_Prefab_Reset_Tips"] = 49,
    ["UI_RedEnvelope_ActivityOver"] = 49,
    ["LuckyStar_GiftStar"] = 49,
    ["LuckyStar_RequestStar"] = 49,
    ["LuckyStar_RewardDes_2"] = 49,
    ["LuckyStar_RewardViewTitle_1"] = 49,
    ["LuckyStar_UnlockTip_2"] = 49,
    ["LuckyStar_RewardViewTitle_2"] = 49,
    ["DrawReward_BiDiscountTitle"] = 49,
    ["DrawReward_BiDiscount_Tips1"] = 49,
    ["UI_Raffle_BiDiscount_Tips2"] = 49,
    ["UI_Raffle_BiDiscount_CountDown"] = 49,
    ["UI_Raffle_ExtraReward_NotGet"] = 49,
    ["UI_Raffle_ExtraReward_Get"] = 49,
    ["UGC_StarCruise_DailyRewardDesc"] = 49,
    ["UGC_StarCruise_PrizeTips"] = 49,
    ["UGC_Object_Cant_Bind_Custom"] = 49,
    ["UGC_MapSearch_NoResult"] = 49,
    ["IQ_Game_To_Be_Start"] = 49,
    ["IQ_Game_Register_Success"] = 49,
    ["IQ_Game_Waiting"] = 49,
    ["IQ_Game_Finished"] = 50,
    ["IQ_Game_Started"] = 50,
    ["IQ_Game_Remove_Card_Desc"] = 50,
    ["IQ_Game_Revive_Card_Desc"] = 50,
    ["IQ_Game_Start_Time"] = 50,
    ["IQ_Game_Has_Started"] = 50,
    ["IQ_Help_Record_Rule"] = 50,
    ["IQ_Help_Record_Rule_1"] = 50,
    ["IQ_Game_Enter_PopUp_Txt"] = 50,
    ["UGC_Park_HotActivity"] = 50,
    ["UGC_Object_Cant_Bind_Limit"] = 50,
    ["QRScan_Fail_InUGCEditor"] = 50,
    ["QRScan_Fail_InHomeEditor"] = 50,
    ["QRScan_Fail_InGame"] = 50,
    ["QRScan_Fail_InPlayingHome"] = 50,
    ["QRScan_Fail_NotInLobby"] = 50,
    ["UGC_Prefab_DeleteCustom"] = 50,
    ["UGC_Prefab_ResetToDefault"] = 50,
    ["UGC_Prefab_ApplyToScene"] = 50,
    ["IQ_Game_Resurrection_Click_Hint"] = 50,
    ["IQ_Game_Use_RemoveWrong_Prop_Failed"] = 50,
    ["IQ_Game_Use_Resurrection_Prop_Failed"] = 50,
    ["IQ_Game_Current_WinNum"] = 50,
    ["IQ_Game_Resurrection_Dialog_Content"] = 50,
    ["IQ_Game_Resurrection_Dialog_CountDown"] = 50,
    ["UGC_Map_TestMapName"] = 50,
    ["UGC_Map_TestRoomName"] = 50,
    ["UGC_Editor_TestRoomDissolve"] = 50,
    ["UGC_Editor_TestRoomAutoDissolve"] = 50,
    ["CustomRoom_NoticeForCreator_InviteAllow"] = 50,
    ["CustomRoom_NoticeForCreator_InviteFull"] = 50,
    ["CustomRoom_NoticeForCreator_TestRule"] = 50,
    ["CustomRoom_InviteTestMsg"] = 50,
    ["CustomRoom_AtLessPlayerTip"] = 50,
    ["InLevel_AllCompleteToRelease"] = 50,
    ["InLevel_TestCompleteToReturn"] = 50,
    ["InLevel_TestCompleteToRelease"] = 50,
    ["InLevel_TestDisable"] = 50,
    ["CustomRoom_GeneralTips_FuncBlock"] = 50,
    ["CustomRoom_InvitationFull"] = 50,
    ["UGC_Editor_WaitForExceptionTips"] = 50,
    ["UGC_Editor_Prefab_SyncToCustom_Tips"] = 50,
    ["UGC_Editor_Prefab_SaveAsCustom_Tips"] = 50,
    ["UGC_RoomList_PlayTogether_PlayTogether"] = 50,
    ["UGC_RoomList_PlayTogether_Match"] = 50,
    ["UGC_RoomList_PlayTogether_SingleHint"] = 50,
    ["UGC_RoomList_PlayTogetherNoMatch_Wait"] = 50,
    ["UGC_RoomList_PlayTogetherNoMatch_Hint"] = 50,
    ["UGC_Roadpoint_CannotLess3"] = 50,
    ["UGC_Roadpoint_Reach_MaxNumber"] = 50,
    ["UGC_Roadpoint_Cannot_Operate"] = 50,
    ["UGC_Cant_Attach"] = 50,
    ["UGC_Cant_Attach_Model"] = 50,
    ["UGC_Attach_Max"] = 50,
    ["UGC_Cant_Custom_MeshModing"] = 50,
    ["UGC_Cant_AddMesh_Limited"] = 50,
    ["UGC_Cant_ChangeModelParam"] = 50,
    ["UGC_Cant_TooManyBooleanModelInMap"] = 50,
    ["UGC_Cant_TooManyBooleanModelAttached"] = 50,
    ["UGC_Map_IsResumeMapFromPublish"] = 50,
    ["UGC_Modeling_NoPhyxWhenComplexCollision"] = 50,
    ["UGC_Modeling_NoComplexCollisionWhenPhyx"] = 50,
    ["PlayerReturn_PopTop"] = 50,
    ["UI_Return_PopTopS3_01"] = 50,
    ["UI_Return_PopTopS3_02"] = 50,
    ["FriendShipFire_WeekLimit"] = 50,
    ["FriendShipFire_Team"] = 50,
    ["FriendShipFire_Team_Return"] = 50,
    ["Community_Dragon_Full"] = 50,
    ["Community_Dragon_Kick"] = 50,
    ["Community_Dragon_Disable"] = 50,
    ["Community_Dragon_View_Free"] = 50,
    ["Community_Dragon_View_Force"] = 50,
    ["UGC_Alignment_CannotAliRot"] = 50,
    ["UGC_Alignment_CannotAliScal"] = 50,
    ["UGC_Alignment_CannotAliScalLimit"] = 50,
    ["UGC_Alignment_CannotAliGroup"] = 50,
    ["UGC_Transform_SucCopiedsizePar"] = 50,
    ["UGC_Alignment_CannotAliLockedObj"] = 50,
    ["UGC_AIGroup_SeedCheck"] = 50,
    ["UGC_AIGen_SelectedImageIsTooSmall"] = 50,
    ["UGC_AIGenEditor_BuildSeedHintText"] = 50,
    ["UGC_AIGenEditor_UnselectedGraph"] = 50,
    ["UGC_AIGen_ImageOutOfMaxAspectRatio"] = 50,
    ["UGC_AIGen_ImagePreprocessingSingleStageTimeout"] = 50,
    ["UGC_AIGen_ImageFormatErrrorOpenFailed"] = 50,
    ["UGC_AIGenEditor_GraphAuditFailed"] = 50,
    ["UI_SKILL_CASTING"] = 50,
    ["UI_SKILL_BARRIER"] = 50,
    ["UI_UGC_UserSpecificaton"] = 50,
    ["IQ_Game_Ready_Exit"] = 50,
    ["IQ_Game_Anwser_Exit"] = 50,
    ["IQ_Game_Ready_Error"] = 50,
    ["IQ_Game_Result_Winner_count"] = 50,
    ["Return_OutfitDelivery_SignIn_Day"] = 50,
    ["Ultraman_Create_Team"] = 50,
    ["Ultraman_Team_Reward"] = 50,
    ["Ultraman_Exit_Team"] = 50,
    ["Ultraman_Kick_Out_Team"] = 50,
    ["Ultraman_InviteToClubLeftCD"] = 50,
    ["Ultraman_NoClub"] = 51,
    ["Ultraman_InviteMessageSend"] = 51,
    ["Ultraman_Task_Lock"] = 51,
    ["Ultraman_Task_Tab_1"] = 51,
    ["Ultraman_Task_Tab_2"] = 51,
    ["Ultraman_Team_Full"] = 51,
    ["Ultraman_Team_Already"] = 51,
    ["Ultraman_Team_Tips"] = 51,
    ["Ultraman_Plot_Lock"] = 51,
    ["Ultraman_Progress"] = 51,
    ["Ultraman_Join_Team"] = 51,
    ["Ultraman_Team_Empty"] = 51,
    ["Ultraman_Task_Pass"] = 51,
    ["Ultraman_Team_Exit_Fail"] = 51,
    ["UI_MiniGame_iOS_Not_Support_Hint"] = 51,
    ["UGC_TravelTogether_MatchTime"] = 51,
    ["UGC_TravelTogether_MatchSuccess"] = 51,
    ["UGC_TravelTogether_CountDown"] = 51,
    ["Common_NextStep_WithTime"] = 51,
    ["UGC_TogetherRecords_Success"] = 51,
    ["UGC_TogetherRecords_PlayTimes"] = 51,
    ["UGC_TravelTogether_InviteTips"] = 51,
    ["UGC_TravelTogether_CountdownText"] = 51,
    ["UGC_TravelTogether_RoomDestroyed"] = 51,
    ["UGC_TravelTogether_MapDownload_Fail"] = 51,
    ["UGC_TravelTogether_Title"] = 51,
    ["UGC_TravelTogether_MatchBtn"] = 51,
    ["UGC_TravelTogether_Match_TeamError"] = 51,
    ["UGC_TravelTogether_EnterCountdownText"] = 51,
    ["UGC_TravelTogether_EndVoteCountDown"] = 51,
    ["UGC_TravelTogether_Tip"] = 51,
    ["UGC_TravelTogether_EditorRoomCountDown"] = 51,
    ["UGC_TravelTogether_Tiptitle"] = 51,
    ["UGC_TravelTogether_CountDown_Again"] = 51,
    ["Activity_Team_Already"] = 51,
    ["Activity_Team_Empty"] = 51,
    ["Add_Player_Friend"] = 51,
    ["Come_Experience_Mathch"] = 51,
    ["Common_MaxWinCount"] = 51,
    ["Common_Screened"] = 51,
    ["Common_TrophyCount"] = 51,
    ["Common_WinCount"] = 51,
    ["Common_ContinuesWin"] = 51,
    ["ConcertMain_Close"] = 51,
    ["Enter_Room_Number"] = 51,
    ["Event_Farmingnotteam"] = 51,
    ["Exclusive_Vehicle_HasNoEmptySeat"] = 51,
    ["Exclusive_Vehicle_IllegalLocation"] = 51,
    ["Exclusive_Vehicle_IllegalMap"] = 51,
    ["Exclusive_Vehicle_IllegalState"] = 51,
    ["Exclusive_Vehicle_Invite_Fail"] = 51,
    ["Exclusive_Vehicle_Invite_MissingTarget"] = 51,
    ["Exclusive_Vehicle_Invite_Sent"] = 51,
    ["Exclusive_Vehicle_Passenger_IllegalState"] = 51,
    ["Exclusive_Vehicle_Passenger_Reject"] = 51,
    ["Exclusive_Vehicle_TooMany"] = 51,
    ["Exclusive_Vehicle_SuitSkill_IllegalLocation"] = 62,
    ["Exclusive_Vehicle_SuitSkill_IllegalMap"] = 62,
    ["Exclusive_Vehicle_SuitSkill_IllegalCommon"] = 62,
    ["Exclusive_Vehicle_SuitSkill_TooMany"] = 62,
    ["Exclusive_Vehicle_SuitSkill_IllegalState"] = 62,
    ["SuitSkill_Waiting_Pak_Ready"] = 63,
    ["Farmyard_Social_Button_1"] = 51,
    ["FirearmOccupancyValueIsFull"] = 51,
    ["FriendOfflineTip"] = 51,
    ["Home_Delete_SpawnPoint_Tips"] = 51,
    ["InLevel_GoalGuideLock"] = 51,
    ["InLevel_GoalGuideTips"] = 51,
    ["InLevel_QST_IntegralCommonAdditionalItem"] = 51,
    ["InLevel_QST_IntegralCommonProtectItem"] = 51,
    ["InLevel_QST_IntegralTeamAdditionalItem"] = 51,
    ["InLevel_QST_IntegralTeamProtectItem"] = 51,
    ["InLevel_RankDataNullTips"] = 51,
    ["InLevel_SingleTestRankingTips"] = 51,
    ["InLevel_TaskComplete"] = 51,
    ["InLevel_TaskNetConnectBusy"] = 51,
    ["InLevel_TaskReward"] = 51,
    ["InLevel_TaskRewardTips"] = 51,
    ["InLevel_TaskRewardLapse"] = 51,
    ["InLevel_TaskStart"] = 51,
    ["InLevel_TaskWaitForSubmit"] = 51,
    ["IQ_Game_Result_Error"] = 51,
    ["IQ_Game_Use_RemoveWrong_Num_Not_Enough"] = 51,
    ["KeepAtLeastOneAction"] = 51,
    ["Lobby_EnterFailed_LackOfChunk"] = 51,
    ["Lobby_InviteGetOnVehicle"] = 51,
    ["Lobby_UGCLobbyNotOpened"] = 51,
    ["Newcomer_tips"] = 51,
    ["MatchType_Lightning"] = 51,
    ["No_Tips_Today"] = 51,
    ["NoDrop_Dead"] = 51,
    ["PlayerInfo_MyRecord"] = 51,
    ["Please_Generate_Group_Photo"] = 51,
    ["PreDownload_DownloadFinished"] = 51,
    ["PreDownload_PreDownloadBookTip1"] = 51,
    ["PreDownload_PreDownloadBookTip2"] = 51,
    ["PreDownload_WaitOther"] = 51,
    ["PreDownload_Del"] = 51,
    ["Prop_LifeSaver_Name"] = 51,
    ["Prop_LifeSaver_Tips"] = 51,
    ["QST_Champion"] = 51,
    ["QST_Draw"] = 51,
    ["QST_LevelDimensionScore"] = 51,
    ["QST_LevelKill"] = 51,
    ["QST_LevelPass"] = 51,
    ["QST_levelSurvive"] = 51,
    ["QST_LevelTeamRank"] = 52,
    ["QST_Rank"] = 52,
    ["Scenegiftpackagetips"] = 52,
    ["Send_Squad_Invitations"] = 52,
    ["Set_EditKey_Classic_FinalAbility"] = 52,
    ["SheJi_duiju1"] = 52,
    ["Friend_MatchTime_OverOneMinute"] = 52,
    ["Subscribe_Self_Fail"] = 52,
    ["Task_MarukoDiscountText"] = 52,
    ["Task_Subscribe_QQrobot"] = 52,
    ["Task_Subscribe_Qqrobot_Failed"] = 52,
    ["Task_Subscribe_Qqrobot_Success"] = 52,
    ["Team_Invite_Tips"] = 52,
    ["TellMsgSendSucceed"] = 52,
    ["UGC LastWeapon Mustkeep"] = 52,
    ["UGC_AI_TextToPic_Tips"] = 52,
    ["UGC_Asset_MapData_SizeOverLimit_Tip"] = 52,
    ["UGC_Asset_Resource_QuantityOverLimit_Tip"] = 52,
    ["UGC_Asset_Resource_SizeOverLimit_Tip"] = 52,
    ["UGC_CanPhysics_DisableLocomotor"] = 52,
    ["UGC_Check_Developer_Agreement_Option"] = 52,
    ["UGC_Common_CoCreate"] = 52,
    ["UGC_Common_Creator"] = 52,
    ["UGC_Cover_CannotUse"] = 52,
    ["UGC_Cover_SelectExceedRank"] = 52,
    ["UGC_Cover_SelectLeastOne"] = 52,
    ["UGC_Cover_SelectMaximum"] = 52,
    ["UGC_Cover_SetFail"] = 52,
    ["UGC_Cover_SetSuccess"] = 52,
    ["UGC_CustomImage_Delete"] = 52,
    ["UGC_CustomImage_Download"] = 52,
    ["UGC_CustomImage_LackName"] = 52,
    ["UGC_CustomImage_LoadFail"] = 52,
    ["UGC_CustomImage_NeedLoad"] = 52,
    ["UGC_CustomImage_NoPass"] = 52,
    ["UGC_CustomImage_NotAvailable"] = 52,
    ["UGC_CustomImage_Pass"] = 52,
    ["UGC_Discard_Disable"] = 52,
    ["UGC_DiscardWeapon_Disable"] = 52,
    ["UGC_DynamicMerge_BanIssue"] = 52,
    ["UGC_DynamicMerge_BanSave"] = 52,
    ["UGC_DynamicMerge_CloseAllMerge"] = 52,
    ["UGC_DynamicMerge_CloseMerge"] = 52,
    ["UGC_DynamicMerge_OpenAllMerge"] = 52,
    ["UGC_DynamicMerge_OpenMerge"] = 52,
    ["UGC_ActorMerge_OverOccupy"] = 52,
    ["UGC_ActorMerge_CloseMergeButton"] = 52,
    ["UGC_Editor_BagPackIsFull"] = 52,
    ["UGC_Editor_CloseSingleSaveFail"] = 52,
    ["UGC_Editor_CopyDataTips"] = 52,
    ["UGC_Editor_CreateDataTips"] = 52,
    ["UGC_Editor_CreateGroup_DownloadSuccess"] = 52,
    ["UGC_Editor_DataInvalidTips"] = 52,
    ["UGC_Editor_DeleteDataTips"] = 52,
    ["UGC_Editor_DialogueLinkIsNull"] = 52,
    ["UGC_Editor_DialogueLinkTips"] = 52,
    ["UGC_Editor_DialogueOccupiedTips"] = 52,
    ["UGC_Editor_MultiFingerWhenCopy"] = 52,
    ["UGC_Editor_MutiDeleteTips"] = 52,
    ["UGC_Editor_OldGroup_DownloadFail"] = 52,
    ["UGC_Editor_Prefab_Save_ConfirmChange"] = 52,
    ["UGC_Editor_Prefab_Save_Preset"] = 52,
    ["UGC_Editor_Prefab_Save_Tip"] = 52,
    ["UGC_Editor_CopyAchievementDataTips"] = 52,
    ["UGC_Editor_CreateAchievementDataTips"] = 52,
    ["UGC_Editor_SaveAchievementDataTips"] = 52,
    ["UGC_Editor_AchievementNameIsNull"] = 52,
    ["UGC_Editor_AchievementDeleteDataTips"] = 52,
    ["UGC_Editor_AchievementMutiDeleteTips"] = 52,
    ["UGC_Editor_OriginalAchievementDeleteFail"] = 52,
    ["UGC_Editor_AchievementIsFull"] = 52,
    ["UGC_Editor_AchievementDataIsNull"] = 52,
    ["UGC_Editor_PreTaskName"] = 52,
    ["UGC_Editor_RankDataChangeTips"] = 52,
    ["UGC_Editor_RankDataDeleteTips"] = 52,
    ["UGC_Editor_RankDataErrorTips"] = 52,
    ["UGC_Editor_RankDataFullTips"] = 52,
    ["UGC_Editor_RankNameInvalidTips"] = 52,
    ["UGC_Editor_ReceiveTaskLinkFailTips"] = 52,
    ["UGC_Editor_SaveDataTips"] = 52,
    ["UGC_Editor_SubmitTaskLinkFailTips"] = 52,
    ["UGC_Editor_TaskConditionFullTips"] = 52,
    ["UGC_Editor_TaskConditionTips"] = 52,
    ["UGC_Editor_TaskDataIsNull"] = 52,
    ["UGC_Editor_TaskDescriptionTips"] = 52,
    ["UGC_Editor_TaskIsFull"] = 52,
    ["UGC_Editor_TaskLineLimitTips"] = 52,
    ["UGC_Editor_TaskNameIsNull"] = 52,
    ["UGC_Editor_TaskNormalDelete"] = 52,
    ["UGC_Editor_TaskProgressTips"] = 52,
    ["UGC_Editor_TaskRewardDes"] = 52,
    ["UGC_Editor_TaskRewardTips"] = 52,
    ["UGC_Editor_TaskSameConditionTips"] = 52,
    ["UGC_Editor_TooMuchDataTips"] = 52,
    ["UGC_Editor_UIEditor_CurrentCanvasRatio"] = 52,
    ["UGC_Editor_UIEditor_GroupLimit_Tip"] = 52,
    ["UGC_Editor_UIEditor_SwitchingCanvasRatio"] = 52,
    ["UGC_Effector_CustomNoSupport"] = 52,
    ["UGC_Effector_ExceedTheLimit"] = 52,
    ["UGC_Effector_HomeLimit"] = 52,
    ["UGC_Effector_IncreaseTheVolume"] = 53,
    ["UGC_Effector_NoEffectToPaste"] = 53,
    ["UGC_Effector_SucCopiedAllEffect"] = 53,
    ["UGC_Effector_SucCopiedEffect"] = 53,
    ["UGC_Effector_SucPastedAllEffect"] = 53,
    ["UGC_Effector_TurnOnTheVolume"] = 53,
    ["UGC_Eveluate_ConfirmNegative"] = 53,
    ["UGC_Eveluate_ConfirmSubmit"] = 53,
    ["UGC_Eveluate_ConfirmSubmit_Remark"] = 53,
    ["UGC_Eveluate_ConfirmSubmit_Score"] = 53,
    ["UGC_Eveluate_Eveluated"] = 53,
    ["UGC_Eveluate_NotChangeEveluat"] = 53,
    ["UGC_Group_CantContainAsset"] = 53,
    ["UGC_GroupPublish_CanUseInPayMapOption_TipsInfo"] = 53,
    ["UGC_identity_Contract_FinishScroll"] = 53,
    ["UGC_identity_Contract_Signing"] = 53,
    ["UGC_identity_Contract_Signing_Check"] = 53,
    ["UGC_identity_Contract_Signing_Content"] = 53,
    ["UGC_identity_Contract_Signing_Fail"] = 53,
    ["UGC_identity_Contract_Signing_Rollback"] = 53,
    ["UGC_identity_Contract_Signing_Start"] = 53,
    ["UGC_Info_AddDirectorTemplateSucc"] = 53,
    ["UGC_LastWeapon_Mustkeep"] = 53,
    ["UGC_Map_IsRemoveExcellentMap"] = 53,
    ["UGC_Map_MapData_SizeOverLimit_Tip"] = 53,
    ["UGC_Map_Resource_illegal_PreventLoad_Tip"] = 53,
    ["UGC_Map_Resource_illegal_PreventSave_Tip"] = 53,
    ["UGC_Map_Resource_QuantityOverLimit_Tip"] = 53,
    ["UGC_Map_Resource_SizeOverLimit_Tip"] = 53,
    ["UGC_MapLaunch_SizeLimit"] = 53,
    ["UGC_MapSave_SizeLimit"] = 53,
    ["UGC_MidJoin_ExitRoom"] = 53,
    ["UGC_MidJoin_JoinBattleFailTip"] = 53,
    ["UGC_MidJoin_NoInvite"] = 53,
    ["UGC_MidJoin_StayRoom"] = 53,
    ["UGC_Pause_NotEffect"] = 53,
    ["UGC_Photo_DeleteFail"] = 53,
    ["UGC_Photo_DeleteSuccess"] = 53,
    ["UGC_Photo_IsUploading"] = 53,
    ["UGC_Photo_ReplaceCancelNotice"] = 53,
    ["UGC_Photo_ReplaceConfirmNotice"] = 53,
    ["UGC_Photo_ReplaceFail"] = 53,
    ["UGC_Photo_ReplaceSuccess"] = 53,
    ["UGC_Photo_SaveFail"] = 53,
    ["UGC_Photo_SaveSuccess"] = 53,
    ["UGC_Photo_Saving"] = 53,
    ["UGC_Runtime_CanNotDragFloor"] = 53,
    ["UGC_Runtime_CanNotInteractGrabbed"] = 53,
    ["UGC_Runtime_OtherInteractTheActor"] = 53,
    ["UGC_SaveRecord_Failed"] = 53,
    ["UGC_SaveRecord_Success"] = 53,
    ["UGC_Scene_Paused"] = 53,
    ["UGC_Scene_Resumed"] = 53,
    ["UGC_Shop_AddGoodsFailed"] = 53,
    ["UGC_Shop_AddShopFailed"] = 53,
    ["UGC_Shop_AddShopShelfFailed"] = 53,
    ["UGC_Shop_BuyFail_BagNotEnough"] = 53,
    ["UGC_Shop_BuyFail_MoneyNotEnough"] = 53,
    ["UGC_Shop_CoatStarDiamondGoodAudit_NotReady"] = 53,
    ["UGC_Shop_Currency_Gold"] = 53,
    ["UGC_Shop_Currency_SilverCoin"] = 53,
    ["UGC_Shop_Currency_StarCoin"] = 53,
    ["UGC_Shop_Currency_StarDiamond"] = 53,
    ["UGC_Shop_DefaultShopName"] = 53,
    ["UGC_Shop_DefaultShopShelfName"] = 53,
    ["UGC_Shop_DeleteItemConfirmTip"] = 53,
    ["UGC_Shop_Draft_BuyStarDiamondGoodTip"] = 53,
    ["UGC_Shop_GoddsDesc_NotValid"] = 53,
    ["UGC_Shop_GoddsName_NotValid"] = 53,
    ["UGC_Shop_GoodsCount"] = 53,
    ["UGC_Shop_ItemsCount"] = 53,
    ["UGC_Shop_Name_NotValid"] = 53,
    ["UGC_Shop_OpenEvent_CanNotRepeat"] = 53,
    ["UGC_Shop_RemoveGoodsVertify"] = 53,
    ["UGC_Shop_RemoveShopShelfVertify"] = 53,
    ["UGC_Shop_RemoveShopVertify"] = 53,
    ["UGC_Shop_ShopShelfName_NotValid"] = 53,
    ["UGC_Shop_SinglePlayer_NotAllowToBuyStarDiamondGood"] = 53,
    ["UGC_Shop_Tab_Custom"] = 53,
    ["UGC_Shop_Tab_Offical"] = 53,
    ["UGC_Shop_TotalGoodsCount"] = 53,
    ["UGC_ShopBuy_Fail"] = 53,
    ["UGC_ShopBuy_HasGroupForbidUseInPayMap_Tip"] = 53,
    ["UGC_ShopBuy_Restart_Tip"] = 53,
    ["UGC_ShopBuy_Start_Notarize_Tip"] = 53,
    ["UGC_ShopBuy_Succeed"] = 53,
    ["UGC_SimpleEditMode_Attach_Forbidden"] = 53,
    ["UGC_SimpleEditMode_Default_Home"] = 53,
    ["UGC_SimpleEditMode_Default_UGCEditor"] = 53,
    ["UGC_SimpleEditMode_FixedXB_Disable"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_Delete"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_Hide"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_Mirror"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_Move"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_Rotate"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_Scale"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_UndoRedo"] = 53,
    ["UGC_SimpleEditMode_LockUnderfoot_Other"] = 53,
    ["UGC_SimpleEditMode_StartPoint_Illegal"] = 53,
    ["UGC_SpawnPoint_TeamSet_Less_MapCamp_Tips"] = 53,
    ["UGC_StaticMeshFlyModeTip"] = 54,
    ["UGC_TestTab_Check_Fail"] = 54,
    ["UGC_TestTab_GroupCheck_Fail"] = 54,
    ["UGC_UpgradeMap_DoubleCheckTips"] = 54,
    ["UGC_UpgradeMap_Fail"] = 54,
    ["UGC_UpgradeMap_NotEnoughSpace"] = 54,
    ["UGC_UpgradeMap_Success"] = 54,
    ["UGC_UpgradePublishedMap_Success"] = 54,
    ["UGC_UpgradeUniCreateMap_Success"] = 54,
    ["UGC_UpgradeMap_LowVersionTips"] = 54,
    ["UGCLobbyNav_ConfirmGoToLobby"] = 54,
    ["UGCProto_ProtosPage_Develop_ProtoTabTitle"] = 54,
    ["UGCProto_ProtosPage_Develop_ProtoTitle"] = 54,
    ["UGCProto_ProtosPage_InAppPurchase_ProtoTabTitle"] = 54,
    ["UGCProto_ProtosPage_InAppPurchase_ProtoTitle"] = 54,
    ["UGCProto_ProtosPage_PanelTitle"] = 54,
    ["UGCProto_SingleProto_Develop_CheckBoxTip"] = 54,
    ["UGCProto_SingleProto_Develop_PanelTitle"] = 54,
    ["UGCProto_SingleProto_Develop_ProtoTitle"] = 54,
    ["UGCProto_SingleProto_InAppPurchase_CheckBoxTip"] = 54,
    ["UGCProto_SingleProto_InAppPurchase_PanelTitle"] = 54,
    ["UGCProto_SingleProto_InAppPurchase_ProtoTitle"] = 54,
    ["UI_Activity_QQSpeed_GetAccelerator"] = 54,
    ["UI_Activity_QQSpeed_GetAllRewardsSuccessTip"] = 54,
    ["UI_Activity_QQSpeed_LotteryFullTip"] = 54,
    ["UI_Activity_QQSpeed_LotteryTip"] = 54,
    ["UI_Activity_QQSpeed_RemainingLotteryTip"] = 54,
    ["UI_Activity_QQSpeed_SpeedUpOnce"] = 54,
    ["UI_Change_Catchphrase"] = 54,
    ["UI_Lottery_KongFuPanda_TitleZhen"] = 54,
    ["UI_Lottery_KongFuPanda_Zhen"] = 54,
    ["UI_Lottery_KongFuPanda_ZhenBtn"] = 54,
    ["UI_Qualifying"] = 54,
    ["Unpickable_Number"] = 54,
    ["Unpickable_Space"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content1"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content10"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content11"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content12"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content13"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content14"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content15"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content16"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content17"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content18"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content19"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content2"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content20"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content3"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content4"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content5"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content6"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content7"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content8"] = 54,
    ["UI_InLevel_Integral_ReputationScore_content9"] = 54,
    ["UI_InLevel_FinalAccount_ReputationScore_1"] = 54,
    ["UI_InLevel_FinalAccount_ReputationScore_2"] = 54,
    ["UI_InLevel_FinalAccount_ReputationScore_3"] = 54,
    ["UI_InLevel_FinalAccount_ReputationScore_4"] = 54,
    ["UI_InLevel_Integral_ReputationScore_mail1"] = 54,
    ["UI_InLevel_Integral_ReputationScore_mail2"] = 54,
    ["UI_InLevel_Integral_ReputationScore_title"] = 54,
    ["UI_InLevel_Integral_ReputationScore_title1"] = 54,
    ["UI_InLevel_Integral_ReputationScore_title2"] = 54,
    ["UI_InLevel_Integral_ReputationScore_title3"] = 54,
    ["UI_InLevel_Integral_ReputationScore_title4"] = 54,
    ["UI_Integral_Reputation_Points_hangup_Hint"] = 54,
    ["UI_Integral_Reputation_Points_Quit_Button"] = 54,
    ["UI_Integral_Reputation_Points_Quit_Hint"] = 54,
    ["UI_Preparations_MapItem1"] = 54,
    ["UI_Preparations_MapSelectItem1"] = 54,
    ["UI_Preparations_MapSelectItem2"] = 54,
    ["UI_Preparations_MapSelectItem3"] = 54,
    ["WeaponModuleModificationTips"] = 54,
    ["WeaponHitPointTips"] = 54,
    ["UGC_CompCapLimit_Tips"] = 54,
    ["UGC_CompNumLimit_Tips"] = 54,
    ["UGC_MapCapLimit_Tips"] = 54,
    ["UGC_ResCapLimit_Tips"] = 54,
    ["UGC_LaunchLimit_RichText"] = 54,
    ["FinalAblity_Tip1"] = 54,
    ["Common_DownloadPak"] = 54,
    ["UI_Prop_EnergyDisable"] = 54,
    ["UGC_MapCollection_Create_Limit"] = 54,
    ["UGC_MapCollection_Add_Suf"] = 54,
    ["UGC_MapCollection_Add_Repeat"] = 54,
    ["UGC_MapCollection_Add_Limit"] = 54,
    ["LightningGameCommonQuitTip"] = 54,
    ["LightningGameQuitTip"] = 54,
    ["UGC_MapDislike_Suf"] = 54,
    ["UGC_MapDislike_NotSelected"] = 54,
    ["PlayerInfo_Photo_Check_Tip"] = 54,
    ["UGC_RoomList_PlayTogether_UGCSaveDB"] = 54,
    ["UGC_RoomList_OnlyMultiSaveDB_Desc"] = 54,
    ["UGC_RoomList_CanSaveDB_Desc"] = 54,
    ["UGC_StandaloneSave_GoodsLimitWorkTips"] = 54,
    ["UGC_StandaloneSave_EditrorSwitchTips"] = 54,
    ["RandomJumpDontFindCanPlayGame"] = 54,
    ["RandomJumpDontFindGameAdaptTeamNum"] = 54,
    ["RandomJumpPlayGameFinishTask"] = 54,
    ["RandomJumpChangeGameAndBeginToFinishTask"] = 55,
    ["RandomJumpBeginMatch"] = 55,
    ["UGC_BagSpaceLimit_Failed"] = 55,
    ["MilestoneGift_limit"] = 55,
    ["MallBuyLimit"] = 55,
    ["MallBuyLimit_Only"] = 55,
    ["UGC_Skill_Refresh_Weapon_Hiding"] = 55,
    ["ClearLotteryRedDotTip"] = 55,
    ["UI_ActivityMusicOrderGetWayTitle"] = 55,
    ["UI_ActivityMusicOrderTodayCollect"] = 55,
    ["UI_ActivityMusicOrderTodayCollectLimit"] = 55,
    ["UGC_Shop_BuyFail_StockNotEnough"] = 55,
    ["UGC_Shop_BuySuccess_StockNum"] = 55,
    ["UGC_Signal_SignalCopyTip"] = 55,
    ["UGC_Signal_SignalDetail"] = 55,
    ["UGC_Signal_SignalQuery"] = 55,
    ["UGC_Signal_SignalQuerySendRecFilter"] = 55,
    ["UGC_Signal_ChangeNameTip"] = 55,
    ["UGC_Signal_ChangeNameTip1"] = 55,
    ["UGC_Signal_ChangeNameTip2"] = 55,
    ["UGC_Signal_ChangeNameTip3"] = 55,
    ["UGC_Signal_SignalQueryTip1"] = 55,
    ["UGC_Signal_SignalQueryTip2"] = 55,
    ["UGC_SaleItem_Disable"] = 55,
    ["UGC_SimpleEditMode_Function_Disable"] = 55,
    ["UGC_StarParty_New_Matching"] = 55,
    ["UGC_StarParty_New_MatchTime"] = 55,
    ["UGC_StarParty_New_MatchSuccess"] = 55,
    ["UGC_StarParty_New_MatchFailTips"] = 55,
    ["UGC_StarParty_New_Will_Choose_Map"] = 55,
    ["UGC_StarParty_New_Team_Waiting_Confirm"] = 55,
    ["UGC_StarParty_New_Random_Choosing_Map"] = 55,
    ["UGC_StarParty_New_Current_Map"] = 55,
    ["UGC_StarParty_New_Waiting_DownloadMap"] = 55,
    ["UGC_StarParty_New_Will_Go_Battle"] = 55,
    ["UGC_StarParty_New_Go_Battle_CountDown"] = 55,
    ["Lightning_CurrentSeasonMaxMedal"] = 55,
    ["UI_Teleport_InvalidTargetLocation"] = 55,
    ["UGC_EditorEdit_AlignFail"] = 55,
    ["UGC_EditorEdit_AlignCanvas"] = 55,
    ["UGC_EditorEdit_AlignGroup"] = 55,
    ["UI_InLevelInvitePlayer"] = 55,
    ["UGC_Map_Demo_SHARE"] = 55,
    ["UI_SysSetting_Password_Window_Title_Change"] = 55,
    ["UI_SysSetting_Password_Window_Title_Set"] = 55,
    ["UI_SysSetting_Password_Tab_Title"] = 55,
    ["UI_SysSetting_Password_Toast_Success"] = 55,
    ["UI_SysSetting_Password_Toast_Change"] = 55,
    ["UI_SysSetting_Password_Toast_Close"] = 55,
    ["UI_SysSetting_Password_Application_Content"] = 55,
    ["UI_SysSetting_Password_Toast_Revoke"] = 55,
    ["UI_SysSetting_Password_Window_Btn_Revoke"] = 55,
    ["UI_SysSetting_Password_Window_Btn_Send"] = 55,
    ["UI_SysSetting_Password_Application_Time"] = 55,
    ["UI_SysSetting_Password_Open_Content"] = 55,
    ["UI_SysSetting_Password_Close_Content"] = 55,
    ["UI_SysSetting_Password_Toast_OpenPass"] = 55,
    ["UI_SysSetting_Password_Pass_Content"] = 55,
    ["InLevel_QST_ContinueWin"] = 55,
    ["UGC_MapRes_ViolationsLimit"] = 55,
    ["UGC_SocialTab_Empty"] = 55,
    ["UGC_SocialTab_EditTip"] = 55,
    ["Prop_Lovechannel_Name"] = 55,
    ["Prop_Cupidarrow_Name"] = 55,
    ["Prop_Lovebomb_Name"] = 55,
    ["Prop_Lovefireworks_Name"] = 55,
    ["Prop_Lovechannel_Tips"] = 55,
    ["Prop_Cupidarrow_Tips"] = 55,
    ["Prop_Lovebomb_Tips"] = 55,
    ["Prop_Lovefireworks_Tips"] = 55,
    ["UGC_MapRiseTip_StillPlaying"] = 55,
    ["UGC_MapRiseTip_ThinkAgain"] = 55,
    ["UGC_MapRiseTip_NoRemind"] = 55,
    ["Activity_Flychess_DataLoading"] = 55,
    ["Activity_Flychess_BackName"] = 55,
    ["NewChat_TimeLastDay"] = 55,
    ["NewChat_TimeMonthDay"] = 55,
    ["NewChat_TimeYearMonthDay"] = 55,
    ["System_NewVersion_Subscribe_Btn_Title"] = 55,
    ["System_NewVersion_Share_Btn_Title"] = 55,
    ["System_NewVersion_Video_Title"] = 55,
    ["QRCode_Scan_Bottom_Tips"] = 55,
    ["DeleteGiftLog_Tips"] = 55,
    ["Direction_Tip_1"] = 55,
    ["Direction_Tip_2"] = 55,
    ["LuckyRemainQuantity"] = 55,
    ["MonthCardRecviveEveryDay"] = 55,
    ["MonthCardRecviveFirstDay"] = 55,
    ["MonthCardRecviveToday"] = 55,
    ["MonthCardAlreadyCacheDays"] = 55,
    ["UGC_MultiScene_SubSceneEdit_Tips"] = 55,
    ["Orange_Suit_Preview_Title"] = 55,
    ["Orange_Suit_Title"] = 55,
    ["UGC_Shop_RemoveMapApply_Reason"] = 55,
    ["UGC_Shop_RemoveMapConfirmTip"] = 55,
    ["UGC_Shop_RemoveMapApply_Succeed"] = 55,
    ["UGC_Shop_RemoveMapApply_Repeated"] = 55,
    ["UGC_Shop_MapAudit_EditForbidden"] = 55,
    ["UGC_StarParty_Reward_title"] = 55,
    ["UGC_StarParty_Reward_desc"] = 55,
    ["QRCode_Gallery_Permission_Tips"] = 56,
    ["Task_DailyExpireText"] = 56,
    ["Task_WeekExpireText"] = 56,
    ["Task_DailyExpireText_2"] = 56,
    ["Task_LevelExpireSoon"] = 56,
    ["Dance_ResLoad_Tips"] = 56,
    ["UI_ResLoad_Tips"] = 56,
    ["UI_WxgameCollectGuide_Tips"] = 56,
    ["PermanentExchange_Entry"] = 56,
    ["Prop_Dart_Name"] = 56,
    ["Prop_Dart_Tips"] = 56,
    ["Common_Confirm_Retry"] = 56,
    ["Common_Confirm_Auto"] = 56,
    ["Common_Confirm_Use"] = 56,
    ["Common_Cancel_2"] = 56,
    ["UI_Setting_AutoSetGraph"] = 56,
    ["UI_Setting_Recommend"] = 56,
    ["UI_Setting_Recommend_1"] = 56,
    ["UI_Setting_Recommend_2"] = 56,
    ["UI_Toast_Recommend"] = 56,
    ["UI_Setting_GraphWarning"] = 56,
    ["UI_Setting_GraphBanned"] = 56,
    ["System_Athlete_Stamina_Title"] = 56,
    ["System_Athlete_Items_Insufficient"] = 56,
    ["System_Athlete_Be_Helped_Title"] = 56,
    ["System_Athlete_Help_Title"] = 56,
    ["SportsmanRadarChatBaseScore"] = 56,
    ["FarmCoin_Cannot_get"] = 56,
    ["UGC_Watch_ItemFail_Tips"] = 56,
    ["UGC_MapList_Publish_Maintain"] = 56,
    ["UGC_Map_Maintain_ForbiddenPlay"] = 56,
    ["Text_ChatUI_JourneyOfCups_Des"] = 56,
    ["Text_ChatUI_ReturnPlayer_Des"] = 56,
    ["Text_ChatUI_Group_Des"] = 56,
    ["UGC_MultiScene_Add_SubScene"] = 56,
    ["UGC_MultiScene_Delete_SubScene"] = 56,
    ["UGC_MultiScene_Edit_SubScene"] = 56,
    ["UGC_MultiScene_SpawnPoint_Delete"] = 56,
    ["UI_MessageBox_GoToBattleRoom"] = 56,
    ["ChastCanotOpen"] = 56,
    ["ChastCanOpen"] = 56,
    ["UI_SimpleGiftText"] = 56,
    ["UGC_StarParty_New_Reward_ThisWeek_Title"] = 56,
    ["UGC_StarParty_New_Reward_ThisWeek_PlayTimes"] = 56,
    ["UGC_StarParty_New_Match_NotInTime"] = 56,
    ["UI_InLevel_Arena_Reputation_MyScore_LineOne"] = 56,
    ["UI_InLevel_Arena_Reputation_MyScore_LineTwo"] = 56,
    ["UI_InLevel_Arena_Reputation_OffenseRule_LineOne"] = 56,
    ["UI_InLevel_Arena_Reputation_OffenseRule_LineTwo"] = 56,
    ["UI_InLevel_Arena_Reputation_OffenseRule_LineThree"] = 56,
    ["UI_InLevel_Arena_Reputation_CreditRule_LineOne"] = 56,
    ["UI_InLevel_Arena_Reputation_CreditRule_LineTwo"] = 56,
    ["UI_InLevel_Arena_Reputation_CreditRule_LineThree"] = 56,
    ["UI_InLevel_Arena_Main_NoCompleteWithReputation_Box"] = 56,
    ["Common_FreeDiscount"] = 56,
    ["UGC_PreventCropping_CrossOverLimit"] = 56,
    ["UGC_PreventCropping_CopyOverLimit"] = 56,
    ["GetAllTaskReward_Cup"] = 56,
    ["NewChat_SystemAdministrator"] = 56,
    ["Item_SortDefault"] = 56,
    ["Prop_SpringPunch_Name"] = 56,
    ["SpringPunch_LevelUp_Success"] = 56,
    ["SpringPunch_LevelUp_Fail"] = 56,
    ["SpringPunch_LevelLabel_1"] = 56,
    ["SpringPunch_LevelLabel_2"] = 56,
    ["SpringPunch_LevelLabel_3"] = 56,
    ["SpringPunch_LevelLabel_4"] = 56,
    ["BP_PA_PR_StarHammer_Name"] = 56,
    ["Setting_Game_MultiScene_Title"] = 56,
    ["UGC_NPC_Exit_Puzzle_Tips"] = 56,
    ["UI_UnableTo_TransmitTo_TheOther_Location"] = 56,
    ["DrawReward_FristText"] = 56,
    ["DrawReward_SecondText"] = 56,
    ["DrawReward_ThridText"] = 56,
    ["DrawReward_MOBATHIRDText"] = 56,
    ["System_ModelSelect_TypeSelectOption_RankMatch_Suffix"] = 56,
    ["System_ModelSelect_TypeSelectOption_AllMatch"] = 56,
    ["Team_StarWorldMap"] = 56,
    ["Mall_Closed"] = 56,
    ["CloudGame_WifiAutoDownload"] = 56,
    ["CloudGame_LoadingDownload"] = 56,
    ["VA_WifiAutoDownload"] = 56,
    ["Common_CantUseInCurrentScene"] = 56,
    ["Set_EditKey_Chase1"] = 56,
    ["Set_EditKey_Chase2"] = 56,
    ["Set_EditKey_NR3E3"] = 56,
    ["Set_EditKey_NR3E2"] = 56,
    ["Set_EditKey_NR3E1"] = 56,
    ["Lobby_StatusCanNotTrain"] = 56,
    ["Lobby_StatusCanNotBackpack"] = 56,
    ["Pak_GameModeDownloadText"] = 56,
    ["Pak_FirstRewardText"] = 56,
    ["Pak_LocalNetworkWIFIStatusText"] = 56,
    ["Pak_LocalNetworkNetStatusText"] = 56,
    ["Pak_AskForDownloadText"] = 56,
    ["Pak_PackageDownloadText"] = 56,
    ["Pak_VisualEffectsText"] = 56,
    ["ScoringSystem_NeedDownloadRes"] = 56,
    ["Text_ChatUI_Month_Card"] = 56,
    ["Text_ChatUI_MVP"] = 56,
    ["Text_ChatUI_Farm_Level"] = 57,
    ["Text_ChatUI_Arena_Hero"] = 57,
    ["Text_ChatUI_Wolf_Identity"] = 57,
    ["CommunityDress_Change_Success"] = 57,
    ["CommunityDress_Change_Revert"] = 57,
    ["CommunityDress_UI_Select_First"] = 57,
    ["CommunityDress_UI_Generate_First"] = 57,
    ["CommunityDress_UI_Change_Tip"] = 57,
    ["CommunityDress_UI_Leave_Tip"] = 57,
    ["CommunityDress_UI_Time_Format"] = 57,
    ["CommunityDress_UI_Keyword_Good"] = 57,
    ["CommunityDress_UI_Keyword_Great"] = 57,
    ["UI_ClothShow_Share_Fix"] = 57,
    ["CommunityDress_Item_Final_Confirm_Tip"] = 57,
    ["CommunityDress_Item_Final_Confirm"] = 57,
    ["CommunityDress_UI_Change_Tip_Dress"] = 57,
    ["CommunityDress_UI_Change_Tip_Nodress"] = 57,
    ["Toast_GetDressItem_Tips"] = 57,
    ["InLevel_MainGame_SceneCollectionTip"] = 57,
    ["CommunityDress_UI_Tips_Opentime"] = 57,
    ["UI_Friend_MatchTime_Short"] = 57,
    ["UI_Friend_MatchTime_OverOneMinute_WithTime"] = 57,
    ["GameCorridor_MST_CollectionRecommend"] = 57,
    ["GameCorridor_MST_CollectionGuessLike"] = 57,
    ["UGC_Park_Common_NetError"] = 57,
    ["UGC_FamousMap_Filter_All"] = 57,
    ["UGC_FamousMap_Filter_UnPlayed"] = 57,
    ["UGC_FamousMap_Filter_UnPassed"] = 57,
    ["UGC_FamousMap_DetailBtn_Text"] = 57,
    ["UGC_FamousMap_MatchBtn_SinglePlay"] = 57,
    ["UGC_FamousMap_MatchBtn_GoMatch"] = 57,
    ["UGC_FamousMap_MatchBtn_CreateRoom"] = 57,
    ["GameCorridor_HotRecommend"] = 57,
    ["UGC_Minimap_Image_Save_Success"] = 57,
    ["UGC_Minimap_Image_Save_Fail"] = 57,
    ["UGC_Minimap_Data_Save_Success"] = 57,
    ["UGC_Minimap_Data_Save_Fail"] = 57,
    ["Common_PlayerHideName"] = 57,
    ["UGC_WeaponDrag_Tips"] = 57,
    ["UGC_Homemadeweapon_Tips"] = 57,
    ["Lottery_FirstTab0"] = 57,
    ["Lottery_FirstTab1"] = 57,
    ["Lottery_FirstTab2"] = 57,
    ["Lottery_FirstTab3"] = 57,
    ["Lottery_FirstTab4"] = 57,
    ["UGC_OMD_Crystal_NumCheck_Min_Tips"] = 57,
    ["UGC_OMD_SpawnPoint_NumCheck_Tips"] = 57,
    ["UGC_OMD_RebirthPoint_PosCheck_Tips"] = 57,
    ["UGC_OMD_MonsterWave_ConfigCheck_Tips"] = 57,
    ["UGC_OMD_SpwanStartPointCantDelete"] = 57,
    ["UGC_OMD_GroundOverAdsorption"] = 57,
    ["UGC_OMD_PointCantRun_Tips"] = 57,
    ["UGC_OMD_GroundPoindIllegal"] = 57,
    ["UGC_OMD_NavMesh_Show"] = 57,
    ["UGC_OMD_NavMesh_Hide"] = 57,
    ["UGC_OMD_SwitchMonsterType"] = 57,
    ["UGC_OMD_SpawnPoint_PosCheck_Tips"] = 57,
    ["UGC_OMD_Difficulty_Easy"] = 57,
    ["UGC_OMD_Difficulty_Challenge"] = 57,
    ["UGC_OMD_Difficulty_Trouble"] = 57,
    ["UGC_OMD_Difficulty_Smart"] = 57,
    ["UGC_OMD_Difficulty_Abreact"] = 57,
    ["UGC_OMD_Difficulty_Oldsix"] = 57,
    ["UGC_OMD_Difficulty_Null_Tips"] = 57,
    ["UGC_OMD_SpwanEndPointCantDelete"] = 57,
    ["UGC_OMD_SpwanActorCantDelete"] = 57,
    ["Prop_Banana_N_Name"] = 57,
    ["Prop_Big_N_Name"] = 57,
    ["Prop_Small_N_Name"] = 57,
    ["Prop_Speedup_N_Name"] = 57,
    ["Prop_ScreamingChicken_N_Name"] = 57,
    ["Prop_StealthBoom_N_Name"] = 57,
    ["Prop_Banana_N_Tips"] = 57,
    ["Prop_Big_N_Tips"] = 57,
    ["Prop_Small_N_Tips"] = 57,
    ["Prop_Speedup_N_Tips"] = 57,
    ["Prop_ScreamingChicken_N_Tips"] = 57,
    ["Prop_StealthBoom_N_Tips"] = 57,
    ["Prop_Banana_N_MoreTips"] = 57,
    ["Prop_Big_N_MoreTips"] = 57,
    ["Prop_Small_N_MoreTips"] = 57,
    ["Prop_Speedup_N_MoreTips"] = 57,
    ["Prop_ScreamingChicken_N_MoreTips"] = 57,
    ["Prop_StealthBoom_N_MoreTips"] = 57,
    ["Activity_GrowthPath_Remind_Update"] = 57,
    ["Activity_GrowthPath_RemindToast_GoUnopened"] = 57,
    ["Activity_GrowthPath_RemindToast_OutTime"] = 57,
    ["Activity_GrowthPath_Remind_Undownload"] = 57,
    ["Common_Resource_Loading"] = 57,
    ["PlayerInfo_Suit"] = 57,
    ["PlayerInfo_ShowSuit"] = 57,
    ["PlayerInfo_SomeOneSuit"] = 57,
    ["UGC_Search_TopicDesc_FollowCount"] = 57,
    ["UGC_Search_TopicDesc_ViewActivity"] = 57,
    ["UGC_Search_TopicDesc_FollowBtn_Follow"] = 57,
    ["UGC_Search_TopicDesc_FollowBtn_UnFollow"] = 57,
    ["UGC_Search_TopicDesc_FollowTips_Followed"] = 57,
    ["UGC_Search_TopicDesc_FollowTips_UnFollowed"] = 57,
    ["UnPickable_Invalid_PlayerState"] = 57,
    ["PlayerInfo_ChangeSuit"] = 57,
    ["DrawReward_WitchText"] = 58,
    ["UGC_Vehicle_Forbidden"] = 58,
    ["Home_Vehicle_Forbidden"] = 58,
    ["UI_FPSSetting_CustomWeapon_Title"] = 58,
    ["Player_Firework_Use_Max_Tips"] = 58,
    ["UI_PC_QRScan_Not_Support_Hint"] = 58,
    ["UGC_MultiScene_Rename_Empty_Tips"] = 58,
    ["UGC_MultiScene_Add_SubScene_Success_Tips"] = 58,
    ["UGC_MultiScene_Rename_Occupied_Tips"] = 58,
    ["Mall_Gift_Notice04"] = 58,
    ["Mall_Gift_Tips01"] = 58,
    ["Mall_Gift_Tips02"] = 58,
    ["UI_SevenDayCheckIn_RemainingTime"] = 58,
    ["SettingTip_ShowMouseTip"] = 58,
    ["SettingTip_ShowMouseShortcutTip"] = 58,
    ["Sharing_Tips"] = 58,
    ["UI_ConnanCard_FullRewardGeted"] = 58,
    ["UI_ConnanCard_FullRewardCanGet"] = 58,
    ["UI_ConnanCard_FullReward_Normal"] = 58,
    ["UI_ConnanCard_SelectSendCard"] = 58,
    ["UI_ConnanCard_SendCardNumEmpty"] = 58,
    ["UI_ConnanCard_CardSendLimitNum"] = 58,
    ["UI_ConnanCard_ClusterRandom"] = 58,
    ["Prop_RainBow_Name"] = 58,
    ["Prop_DuckRocket_Name"] = 58,
    ["AB_Steal_Name"] = 58,
    ["AB_BounceBack_Name"] = 58,
    ["AB_BounceBack_Tips"] = 58,
    ["AB_BounceBack_MoreTips"] = 58,
    ["AB_Steal_Tips"] = 58,
    ["UGC_AiAssistant_Welcome01"] = 58,
    ["CAN_NOT_OPEN_ARMORY_BY_ATTR"] = 58,
    ["Pak_AutoRemoveCleanUpTip"] = 58,
    ["Pak_RemoveCleanupTip3"] = 58,
    ["Mall_Gift_Tips03"] = 58,
    ["SimpleModelChangeMatch"] = 58,
    ["CLOUD_TO_NATIVE_TIPS_MOBILE"] = 58,
    ["CLOUD_TO_NATIVE_TIPS_PC"] = 58,
    ["UGC_FPS_WEAPON_OneShotFragmentNum_Limit"] = 58,
    ["Prop_ShuttleNew_Tips"] = 58,
    ["Prop_ShuttleNew_MoreTips"] = 58,
    ["Prop_Cake_Name"] = 58,
    ["Prop_Cake_Tips"] = 58,
    ["UI_Teletubbies_Tips"] = 58,
    ["UGC_Skybox_Name_80"] = 58,
    ["UGC_StaticBatch_Tips01"] = 58,
    ["Prop_RainBow_Tips"] = 58,
    ["Prop_RainBow_MoreTips"] = 58,
    ["Prop_DuckRocket_Tips"] = 58,
    ["Prop_DuckRocket_MoreTips"] = 58,
    ["DrawReward_FourthText"] = 58,
    ["Common_Continue"] = 58,
    ["Delete_ChatRecord_Fail"] = 58,
    ["UGC_DownFootComp_IsLock"] = 58,
    ["Delete_Photo_CantRecover"] = 58,
    ["New_Intelligence"] = 58,
    ["Activity_RankNum_HasReward"] = 58,
    ["Rank_Dont_Have_Rank"] = 58,
    ["UI_KongFuPanda_Fastest"] = 58,
    ["UI_KongFuPanda_EmptyRank"] = 58,
    ["UI_KongFuPanda_RankTime"] = 58,
    ["UI_KongFuPanda_OldTime"] = 58,
    ["UI_KongFuPanda_IsRankNum"] = 58,
    ["UGC_AiAssistant_Welcome02"] = 58,
    ["UGC_AiAssistant_Block"] = 58,
    ["Player_ChangeInviteFrame"] = 58,
    ["Prop_80022Vacuum_Name"] = 58,
    ["Prop_80022Vacuum_Tips"] = 58,
    ["UI_SysSetting_Password_Pass_Tips"] = 58,
    ["Exclusive_Vehicle_ForbidSpecialSkillSlotTips"] = 58,
    ["UI_Share_Not_Support"] = 58,
    ["UI_Countdown_Main_Unlock"] = 58,
    ["SystemMessageNextDayLoginTitle"] = 58,
    ["SystemMessageNextDayLoginContent"] = 58,
    ["UGC_AiAssistant_TextLimit"] = 58,
    ["UGC_AiAssistant_EmptyMapError"] = 58,
    ["MD5Test"] = 58,
    ["Retrieve_Entrance"] = 58,
    ["Retrieve_Exchange_info"] = 58,
    ["Retrieve_Oneclick"] = 58,
    ["Retrieve_Way_info"] = 58,
    ["Retrieve_None_info"] = 58,
    ["Retrieve_Money_info"] = 58,
    ["Retrieve_Button"] = 58,
    ["Retrieve_Ratio_1"] = 58,
    ["Retrieve_Ratio_2"] = 58,
    ["Retrieve_Time"] = 58,
    ["Retrieve_All_info"] = 58,
    ["Retrieve_Unlock_All_info"] = 58,
    ["Cupgame_weekend_Duplicate_tag"] = 58,
    ["Cuptask_weekend_Duplicate_tag"] = 58,
    ["Retrieve_Seek_Hint"] = 58,
    ["GotAllRaffleGrandReward"] = 58,
    ["CupTask_None_info"] = 58,
    ["Addtype_tag1"] = 58,
    ["Addtype_tag2"] = 58,
    ["Addtype_tag3"] = 58,
    ["LimitedType_tag1"] = 58,
    ["LimitedType_tag2"] = 58,
    ["LimitedType_tag3"] = 58,
    ["Cup_Duplicate_tag"] = 59,
    ["TDM_AIRecommend_TabName"] = 59,
    ["Pak_PackageDownload_Tips"] = 59,
    ["Exclusive_Vehicle_BatchInviteTips"] = 59,
    ["Exclusive_Vehicle_SwitchSeatTips"] = 59,
    ["Exclusive_Vehicle_ForbidInviteNoSeatTips"] = 59,
    ["Exclusive_Vehicle_NoSeatTips"] = 59,
    ["Exclusive_Vehicle_ForbidPartySkillTips"] = 59,
    ["Exclusive_Vehicle_ForbidJetSkillTips"] = 59,
    ["Exclusive_Vehicle_ForbidSwitchMainSeatTips"] = 59,
    ["Exclusive_Vehicle_SwitchSeatSuccessNameTips"] = 59,
    ["Exclusive_Vehicle_SwitchSeatSuccessTips"] = 59,
    ["Exclusive_Vehicle_ScoreTips"] = 59,
    ["Exclusive_Vehicle_BatchInvitationTips"] = 59,
    ["Exclusive_Vehicle_BatchInvitationRepeatTips"] = 59,
    ["Exclusive_Vehicle_InvitationExpired"] = 59,
    ["UI_CustomLayout_Plan"] = 59,
    ["Set_EditKey_UseKeySuccess"] = 59,
    ["Set_EditKey_SaveAndUseKeySuccess"] = 59,
    ["Set_EditKey_isSaveAndUseCurrentKeyToExit"] = 59,
    ["Retrieve_Unlock_All_info1"] = 59,
    ["Set_EditKey_SaveAndUse"] = 59,
    ["Tips_DecorationRank_1"] = 59,
    ["Tips_DecorationRank_2"] = 59,
    ["Tips_DecorationRank_3"] = 59,
    ["Active_Remaining_Time"] = 59,
    ["UGC_Editor_InputScopeTip"] = 59,
    ["ResetToSelectCombineWnd"] = 59,
    ["UGC_OMD_SpwanActorCantDeleteDetail"] = 59,
    ["UGC_OMD_BigWaveInvalid"] = 59,
    ["UGC_OMD_EffectOrdinaryMode_NumCheck_Min_Tips"] = 59,
    ["UGC_OMD_EffectEndlessMode_NumCheck_Min_Tips"] = 59,
    ["UGC_OMD_SmallWavePathConfigInvalid"] = 59,
    ["UGC_OMD_MiniMapCheck"] = 59,
    ["UGC_OMD_SmallWaveLackMonster"] = 59,
    ["UGC_OMD_HasMonsterCheckPublish"] = 59,
    ["UGC_OMD_BigSmallWaveDiffType"] = 59,
    ["Tips_CardRank_1"] = 59,
    ["Tips_CardRank_2"] = 59,
    ["Tips_Theme_Firstshow"] = 59,
    ["Tips_ClickToSkipPV"] = 59,
    ["Lottery_WerewolfLuckCard_1"] = 59,
    ["Lottery_WerewolfLuckCard_2"] = 59,
    ["Wansong_Dance_Leave_Tip"] = 59,
    ["Wansong_Dance_Other_Cancel"] = 59,
    ["Set_EditKey_HotZone"] = 59,
    ["UGC_NPC_Exit_Argue_Tips"] = 59,
    ["Maingame_Bp_HuoQU"] = 59,
    ["Maingame_Bp_ZhuangShi"] = 59,
    ["UGC_MultiCamp_NotExistCamp"] = 59,
    ["UGC_Match_CanTeamNum"] = 59,
    ["UGC_MultiCamp_CantSameColor"] = 59,
    ["UGC_MultiCamp_CantSameTeamName"] = 59,
    ["UGC_Lobby_Already_In_Review"] = 59,
    ["UGC_Lobby_Can_Not_Withdraw_During_Reviewing"] = 59,
    ["UGC_Lobby_Reviewing"] = 59,
    ["UGC_Lobby_Review_Passed"] = 59,
    ["UGC_Lobby_Testing"] = 59,
    ["UGC_Lobby_Review_Limit_Exceeded"] = 59,
    ["UGC_Lobby_Review_Blocked_By_Safity_Check"] = 59,
    ["ConnanCard_NoSupportSendCard"] = 59,
    ["Wansong_Match_Not_Open"] = 59,
    ["Wansong_Match_Time_Tips"] = 59,
    ["Wansong_Match_In_Team"] = 59,
    ["Wansong_Match_In_Room"] = 59,
    ["Wansong_Match_In_Match"] = 59,
    ["UGC_IsQuit_Finished"] = 59,
    ["UGC_IsQuit_Unfinished"] = 59,
    ["AIGCNPC_Exit_CreatePartner_Confirm_Tips"] = 59,
    ["AIGCNPC_Exit_TempPalChat_Confirm_Tips"] = 59,
    ["MiniGame_InMatch_Tip"] = 59,
    ["Pak_PackageDownloadCommonText"] = 59,
    ["Pak_PackageDownloadCommonText1"] = 59,
    ["Wansong_Match_TimeDownFormat"] = 59,
    ["UGC_MultiCamp_LeastOneTeam"] = 59,
    ["Common_PlatformErrorTip"] = 59,
    ["UGC_PerformanceTips_1"] = 59,
    ["Prop_70240CurveWeapon_Name"] = 59,
    ["Prop_70240ThreeCurveWeapon_Name"] = 59,
    ["Prop_70240CurveWeapon_Tips"] = 59,
    ["Prop_70240ThreeCurveWeapon_Tips"] = 59,
    ["ScoreGuide_Feedback_MaxWords"] = 59,
    ["Pak_RemoveCleanupTip4"] = 59,
    ["UGC_MapDescription_SpaceEdit"] = 59,
    ["UGC_MapDescription_SpacePreviw"] = 59,
    ["UGC_MapDescription_SpaceSave"] = 59,
    ["UGC_MapDescription_EditSucceed"] = 59,
    ["UGC_MapDescription_Delete"] = 59,
    ["UGC_MapDescription_DeleteYes"] = 59,
    ["UGC_MapDescription_DeleteNot"] = 59,
    ["UGC_MapDescription_SaveNot"] = 59,
    ["UGC_MapDescription_SaveAbandon"] = 59,
    ["UGC_MapDescription_SaveConfirm"] = 59,
    ["Pak_HeroCardDownloadText"] = 59,
    ["DrawReward_FifthText"] = 59,
    ["FindPoketGameList"] = 59,
    ["UI_ShareChannel_SafetyText"] = 59,
    ["Bag_ShuttleEquipSuccess"] = 59,
    ["Common_UseNow"] = 59,
    ["UI_Activity_NewYearParty2025_Tips"] = 59,
    ["UGC_PartyGame_NextMap"] = 60,
    ["UGC_PartyGame_AwardMap"] = 60,
    ["UGC_PartyGame_WaitingNextMap"] = 60,
    ["UGC_PartyGame_PlayerMissingWarning"] = 60,
    ["UGC_VisionSettings_Desc"] = 60,
    ["ItemType_Suit"] = 60,
    ["ItemType_BackOrnament"] = 60,
    ["ItemType_HeadWear"] = 60,
    ["ItemType_HandOrnament"] = 60,
    ["ItemType_FaceOrnament"] = 60,
    ["ItemType_Emoji"] = 60,
    ["Group_Invitation_Restrictions"] = 60,
    ["Exclusive_Vehicle_IllegalState_Using"] = 60,
    ["Task_Activity_HasExpired"] = 60,
    ["UGC_UGCEditor_NumberKeyboard_OpenTips"] = 60,
    ["UGC_UGCEditor_NumberKeyboard_Disable"] = 60,
    ["Cup_Collect_Main_Title"] = 60,
    ["Cup_Collect_Main_Game_Title"] = 60,
    ["Cup_Collect_Main_Game_Add"] = 60,
    ["Cup_Collect_Main_Task_Title"] = 60,
    ["Cup_Collect_Main_Cup_Pre_Num"] = 60,
    ["Cup_Collect_Main_Cup_Rank_Title"] = 60,
    ["Cup_Collect_Main_Cup_Rank_Btn"] = 60,
    ["Cup_Collect_Main_Cup_Rank_Str1"] = 60,
    ["Cup_Collect_Main_Cup_Rank_Str2"] = 60,
    ["UGC_CoCreate_RefundPermission"] = 60,
    ["UGC_CoCreate_MapRemoved"] = 60,
    ["UGC_MapTransToLobby_Draft_Full"] = 60,
    ["UGC_MapTransToLobby_Failed"] = 60,
    ["UGC_MapTransToLobby_Success"] = 60,
    ["UGC_AiAssistant_LoadingTimeTips"] = 60,
    ["UGC_PerformanceAlert_LowRisk"] = 60,
    ["UGC_PerformanceAlert_HighRisk"] = 60,
    ["Pak_IOSArraignment_PromptText1"] = 60,
    ["Pak_IOSArraignment_PromptText2"] = 60,
    ["ItemLimit_MainGame"] = 60,
    ["ItemRedBag_TCVip_Desc"] = 60,
    ["UGC_MapDescription_PhotoLimit"] = 60,
    ["Mail_ConfirmStarDelete"] = 60,
    ["Mail_StarTip"] = 60,
    ["UGC_Skybox_Name_81"] = 60,
    ["UGC_Skybox_Name_82"] = 60,
    ["Share_PicDownloading"] = 60,
    ["UGC_LoadDiagram_Edit"] = 60,
    ["UGC_LoadAndNavigateDiagram_Edit"] = 60,
    ["UGC_MainLoadDiagram"] = 60,
    ["UGC_SubsceneLoadDiagram"] = 60,
    ["UGC_NavigateDiagram"] = 60,
    ["UGC_MapAlbum"] = 60,
    ["UGC_CustomImage_Select"] = 60,
    ["UGC_MapAlbumImage_Select"] = 60,
    ["UGC_IconImage_Select"] = 60,
    ["UGC_UploadImage_Tip"] = 60,
    ["UGC_UploadImage_Fail"] = 60,
    ["UGC_UploadImage_Success"] = 60,
    ["UGC_UploadImage_Cancel"] = 60,
    ["UGC_NavigateDiagramAndIcon_IsNull"] = 60,
    ["UGC_MapAlbumAndCustomize"] = 60,
    ["UGC_NavigateDiagram_Basemap"] = 60,
    ["UGC_NavigateDiagram_Icon"] = 60,
    ["UGC_DefaultImageAndCustomize"] = 60,
    ["UGC_LoadDiagramUpload_Preview"] = 60,
    ["UGC_NavigateDiagramUpload_Preview"] = 60,
    ["UGC_IconUpload_Preview"] = 60,
    ["Prop_MagicCard_Tips"] = 60,
    ["Prop_MagicCard_MoreTips"] = 60,
    ["Prop_MagicCard_Name"] = 60,
    ["UGC_NPC_Quadruped_Help"] = 60,
    ["OutOfMemory_Tip"] = 60,
    ["TryLater_Text"] = 60,
    ["Prop_SuperJump_Name"] = 60,
    ["Prop_SuperJump_Tips"] = 60,
    ["Prop_Teleport_Name"] = 60,
    ["Prop_Teleport_Tips"] = 60,
    ["Prop_HookLink_Name"] = 60,
    ["Prop_HookLink_Tips"] = 60,
    ["CLOUD_TO_NATIVE_TIPS_WX"] = 60,
    ["UGC_Fairyland_Testing_Tips"] = 60,
    ["ActivityNeedCommunityScene"] = 60,
    ["ActivityNeedWerewolf"] = 60,
    ["WerewolfNotOpenJumpFailed"] = 60,
    ["FarmLevelNeed3"] = 60,
    ["Skill_BasicAttack_Name1"] = 60,
    ["Skill_CountAttack_Name2"] = 60,
    ["Skill_Flash_Name3"] = 60,
    ["Skill_BasicAOEAttack_Name4"] = 60,
    ["Skill_ShotgunaAttack_Name5"] = 60,
    ["Main_HostingByAI_Tips_1"] = 60,
    ["Main_HostingByAI_Tips_2"] = 60,
    ["PlayModeReturn_LittleTitle1"] = 61,
    ["PlayModeReturn_LittleTitle2"] = 61,
    ["PlayModeReturn_LittleTitle3"] = 61,
    ["Prop_Furniture_Name"] = 61,
    ["Prop_Furniture_Tips"] = 61,
    ["Prop_Furniture_MoreTips"] = 61,
    ["Prop_TeleportUp_Name"] = 61,
    ["Prop_TeleportUp_Tips"] = 61,
    ["Prop_TeleportUp_MoreTips"] = 61,
    ["Handhold_MusicConcert_ForbidGetOn"] = 61,
    ["Handhold_MusicConcert_DJToning"] = 62,
    ["Handhold_MusicConcert_DrumToning"] = 62,
    ["Handhold_MusicConcert_GuitarToning"] = 62,
    ["Handhold_MusicConcert_BassToning"] = 62,
    ["UGC_Skybox_Name_83"] = 61,
    ["UGC_Quick_Join_Invite_Confirm_Title_Default"] = 61,
    ["UGC_Quick_Join_Invite_Confirm_Hint"] = 61,
    ["Prop_70250Skill1_Name"] = 61,
    ["Prop_70250Skill2_Name"] = 61,
    ["Prop_70250Skill3_Name"] = 61,
    ["Prop_70250Skill4_Name"] = 61,
    ["Prop_70250Flash_Name"] = 61,
    ["Prop_70254Skill1_Name"] = 61,
    ["Prop_70254Skill4_Name"] = 61,
    ["Prop_70254Flash_Name"] = 61,
    ["AB_70250Skill1_Tips"] = 61,
    ["AB_70250Skill2_Tips"] = 61,
    ["AB_70250Skill3_Tips"] = 61,
    ["AB_70250Skill4_Tips"] = 61,
    ["AB_70250Flash_Tips"] = 61,
    ["UGC_Jion_Halfway_Switch_off"] = 61,
    ["UGC_SkyBox_Txt_FilterAdjustments"] = 61,
    ["UGC_SkyBox_Txt_Effect"] = 61,
    ["FridayBuff_OutOfTime"] = 61,
    ["Location_None"] = 61,
    ["Predownloadtip1"] = 61,
    ["Predownloadtip2"] = 61,
    ["PlayerInfo_SomeOneInfo"] = 61,
    ["PlayerInfo_SomeOneFashion"] = 61,
    ["VehicleSkill_Region_Overlap"] = 61,
    ["Item_SortCount"] = 61,
    ["PakCleanUpText"] = 61,
    ["Activity_FeaturedGameplay_Tips"] = 61,
    ["WerewolfRecall_TipContent"] = 61,
    ["WerewolfRecall_MessageBoxTitle"] = 61,
    ["WerewolfRecall_MessageBoxContent"] = 61,
    ["WerewolfRecall_SendChatMessage"] = 61,
    ["BroadcastText_Main_01"] = 61,
    ["BroadcastText_Main_02"] = 61,
    ["BroadcastText_Main_03"] = 61,
    ["BroadcastText_Main_04"] = 61,
    ["BroadcastText_Main_05"] = 61,
    ["BroadcastText_Main_06"] = 61,
    ["BroadcastText_Main_07"] = 61,
    ["BroadcastText_Main_08"] = 61,
    ["BroadcastText_Main_09"] = 61,
    ["BroadcastText_Main_10"] = 61,
    ["BroadcastText_Main_11"] = 61,
    ["BroadcastText_Main_12"] = 61,
    ["BroadcastText_Main_13"] = 61,
    ["BroadcastText_Main_14"] = 61,
    ["BroadcastText_Main_15"] = 61,
    ["BroadcastText_Main_16"] = 61,
    ["BroadcastText_Main_17"] = 61,
    ["BroadcastText_Main_18"] = 61,
    ["BroadcastText_Main_19"] = 61,
    ["BroadcastText_Main_20"] = 61,
    ["BroadcastText_Main_21"] = 61,
    ["BroadcastText_Main_22"] = 61,
    ["BroadcastText_Main_23"] = 61,
    ["WolfKill_MonthCard_Reward"] = 62,
    ["Title_TakeALook"] = 61,
    ["Title_QuickEntrance"] = 61,
    ["Title_RecentPlay"] = 61,
    ["Title_MyCollections"] = 61,
    ["Cook_Report_1"] = 61,
    ["UGC_MultiScene_Edit_SpeedCheck_Tips"] = 61,
    ["Handhold_Common_ForbidenWhenTooClose"] = 61,
    ["GiveAway_GetTaskRewards"] = 60,
    ["GiveAway_GetTaskRewards_GoChange"] = 60,
    ["GiveAway_GetTaskRewards_Get"] = 60,
    ["GiveAway_ChangeTaskReward"] = 60,
    ["Return_BookFullLevel"] = 60,
    ["Return_GiftTitle"] = 60,
    ["Return_TaskTitle"] = 60,
    ["Return_WeekTaskTitle"] = 60,
    ["Return_AccumulateTaskTitle"] = 60,
    ["Return_BookSelectSkin"] = 60,
    ["Return_OfflineExpDesc"] = 61,
    ["Return_BigRewardDescribe"] = 61,
    ["Return_Activity_ReturnLink_Page1"] = 61,
    ["Return_Activity_ReturnLink_Page2"] = 61,
    ["Return_Activity_ReturnLink_Page3"] = 61,
    ["Return_Activity_ReturnLink_Page4"] = 61,
    ["Return_Activity_ReturnLink_Page5"] = 61,
    ["Return_NoOfflineExperience"] = 60,
    ["Photo_Reset_Success"] = 61,
    ["Video_Mode_Switch"] = 61,
    ["Video_Mode_Saved"] = 61,
    ["Video_Mode_SavedShort"] = 61,
    ["UGC_MyRes_LimitTips"] = 61,
    ["UGC_MyInterfaceRes_LimitTips"] = 61,
    ["UGC_InterfaceRes_CantSaveTips"] = 61,
    ["UGC_InterfaceRes_CantEditTips"] = 61,
    ["Pak_CleanupCompletionTip"] = 61,
    ["UGC_Room_Change_Map_Tips"] = 61,
    ["UGC_Room_Recommend_Map_Tips"] = 61,
    ["UGC_CoCreate_EditRequest_Sent"] = 61,
    ["UGC_CoCreate_EditRequest_Refuse"] = 61,
    ["UGC_CoCreate_EditRequest_Cooling"] = 61,
    ["UGC_CoCreate_EditRequest_Await"] = 61,
    ["UGC_CoCreate_EditRequest_Completed"] = 62,
    ["UGC_CoCreate_PrePelease_Refuse"] = 61,
    ["UGC_CoCreate_MultiPersonEdit_PublishRefuse"] = 61,
    ["UGC_CoCreate_EditRequest_NotJoin"] = 61,
    ["UGC_CoCreate_SceneMode_CodingDataUpdateTips"] = 61,
    ["UGC_CoCreate_SceneMode_CodingDataDescription"] = 62,
    ["UGC_CoCreate_SceneMode_CodingDataDownloadTips"] = 62,
    ["UGC_CoCreate_SceneMode_CodingDataSaveTips"] = 62,
    ["UGC_CoCreate_SceneMode_CodingDataNotSaveTips"] = 62,
    ["UGC_CoCreate_SceneMode_Description"] = 62,
    ["UGC_CoCreate_SceneMode_SceneDataUploadTips"] = 62,
    ["UGC_CoCreate_CodingMode_SceneDataUpdateTips"] = 62,
    ["UGC_CoCreate_CodingMode_SceneDataDescription"] = 62,
    ["UGC_CoCreate_CodingMode_SceneDataDownloadConfirm"] = 62,
    ["UGC_CoCreate_CodingMode_Description"] = 62,
    ["UGC_CoCreate_CodingMode_CodingDataUploadTips"] = 63,
    ["UGC_CoCreate_MultiPersonEdit_AwaitTips"] = 62,
    ["UGC_CoCreate_MultiPersonEdit_DingTips"] = 62,
    ["UGC_CoCreate_DataSync_DataChange"] = 62,
    ["UGC_CoCreate_DataSync_DataNoChange"] = 62,
    ["UGC_CoCreate_DataSync_UploadTips1"] = 62,
    ["UGC_CoCreate_DataSync_UploadTips2"] = 62,
    ["UGC_CoCreate_DataSync_DownloadTips1"] = 62,
    ["UGC_CoCreate_DataSync_DownloadTips2"] = 62,
    ["UGC_CoCreate_ModeSelect_Tips1"] = 62,
    ["UGC_CoCreate_ModeSelect_Tips2"] = 62,
    ["UGC_CoCreate_ModeSelect_SceneModeDesc"] = 62,
    ["UGC_CoCreate_ModeSelect_CodingModeDesc"] = 62,
    ["UGC_CoCreate_ModeSelect_SceneModeFullDes"] = 62,
    ["UGC_CoCreate_ModeSelect_CodingModeFullDes"] = 62,
    ["UGC_CoCreate_ModeName_Scene"] = 62,
    ["UGC_CoCreate_ModeName_Coding"] = 62,
    ["UGC_CoCreate_Cooperate_Terminate"] = 62,
    ["InLevel_QST_RandomEvent"] = 62,
    ["UGC_MultiScene_PreserveScene_Tips"] = 62,
    ["UGC_MultiScene_SceneFully_Tips"] = 62,
    ["UGC_MultiScene_JionHalfway_PreserveScene_Tips"] = 62,
    ["UGC_MultiScene_JumpLock_EndingSoon_Tips"] = 62,
    ["UGC_MultiScene_JumpLock_Countdown_Tips"] = 62,
    ["UGC_MultiScene_JumpCD_Tips"] = 62,
    ["UGC_MultiScene_JumpFailed_Tips1"] = 62,
    ["UGC_MultiScene_JumpFailed_Tips2"] = 62,
    ["UGC_MultiScene_SceneDestroyed_Settlement_Tips"] = 62,
    ["UGC_MultiScene_JumpFailed_OutOfRange_Tips"] = 62,
    ["UGC_MultiScene_JumpFailed_Tips3"] = 63,
    ["UGC_MultiScene_JumpFailed_WrongWay"] = 63,
    ["UGC_MultiScene_JumpWay_Name1"] = 63,
    ["UGC_MultiScene_JumpWay_Name2"] = 63,
    ["UGC_MultiScene_PreserveScene_SwitchDescription"] = 62,
    ["UGC_MultiScene_OnlyScene_SwitchDescription"] = 62,
    ["Subscribe_AddToMyMiniProgram"] = 62,
    ["Subscribe_ServiceNotify"] = 62,
    ["Subscribe_AddToDesktop"] = 62,
    ["SettingTip_RoleOutline"] = 62,
    ["FarmReturn_LevelChallengeMax"] = 62,
    ["FarmReturn_LevelChallenge"] = 62,
    ["UGC_Skybox_Name_84"] = 62,
    ["Bp_Other_Download"] = 62,
    ["Bp_Other_Condition_Wolves"] = 62,
    ["Activity_FarmPass_WeekTask"] = 62,
    ["Activity_FarmPass_CyclicalTask"] = 62,
    ["Activity_FarmPass_WeekIdx"] = 62,
    ["Cup_Collect_Multiple_Tips1"] = 62,
    ["Cup_Collect_Multiple_Tips2"] = 62,
    ["Cup_Collect_Multiple_Tips3"] = 62,
    ["Cup_Collect_Multiple_Tips4"] = 62,
    ["UGC_ModeSelect_MapPool_GoMatch"] = 62,
    ["SettingTip_CupShow"] = 62,
    ["Cup_Collect_Multiple_Tips5"] = 62,
    ["Cup_Collect_Multiple_Tips6"] = 62,
    ["Cup_Collect_Multiple_Tips7"] = 62,
    ["Cup_Collect_Multiple_Tips8"] = 62,
    ["Cup_Collect_Multiple_Tips9"] = 63,
    ["Pak_PV_DownLoad"] = 62,
    ["Pak_Delete_Tips"] = 62,
    ["Pak_InterfaceName1"] = 62,
    ["Pak_InterfaceName2"] = 62,
    ["Pak_InterfaceName3"] = 62,
    ["Pak_InterfaceTip1"] = 62,
    ["Pak_WindowDescription"] = 62,
    ["Pak_LineUp_Tip"] = 62,
    ["Pak_Interface_Tip1"] = 62,
    ["Pak_Interface_Tip2"] = 62,
    ["Pak_Interface_Tip3"] = 62,
    ["Pak_CollectionInterface_Text1"] = 62,
    ["Pak_CollectionInterface_Text2"] = 62,
    ["Pak_AllDownload_Tip"] = 62,
    ["Pak_WVAInterface_Tip1"] = 62,
    ["Pak_WVAInterface_Tip2"] = 62,
    ["UGC_AIGCModelDelete_Title"] = 62,
    ["UGC_AIGCModelDelete_Tips"] = 62,
    ["System_Bag_Slot_Ready_To_Clear"] = 62,
    ["Pak_LowSpace_Tip1"] = 62,
    ["Pak_LowSpace_Tip2"] = 62,
    ["Pak_LowSpace_Tip3"] = 62,
    ["Pak_LowSpace_Tip4"] = 62,
    ["UGC_InterfaceCover_CantPublish"] = 62,
    ["UGC_InterfaceRes_CantPickForPub"] = 62,
    ["SummerVacation_Decoration_Txt"] = 63,
    ["SummerVacation_Task_Txt"] = 63,
    ["Pak_DescriptionInterfaceText"] = 63,
    ["App_Icon_Change"] = 63,
    ["Pak_PV_DownLoad_Toast"] = 63,
    ["App_Icon_Ios"] = 63,
    ["App_Icon_Android"] = 63,
    ["UGC_AiAssistant_ErrorCodeTips"] = 63,
    ["UGC_StatusCanNotUseBag"] = 63,
    ["Pak_Recommended_Download1"] = 63,
    ["Pak_Recommended_Download2"] = 63,
    ["Pak_Recommended_Download"] = 63,
    ["Mayday_ModeName"] = 64,
    ["Mayday_Map_Rainforest"] = 64,
    ["Mayday_Map_Rainforest_Info"] = 64,
    ["Mayday_Map_KingsPalace"] = 64,
    ["Mayday_Map_KingsPalace_Info"] = 64,
    ["Mayday_Map_KingsPalace_Shangjiao"] = 64,
    ["Mayday_Map_SunDesert"] = 64,
    ["Mayday_Map_SunDesert_Info"] = 64,
    ["Mayday_Map_Tips1"] = 64,
    ["Mayday_InGame_MapInfo_Time"] = 64,
    ["Mayday_InGame_MapInfo_Time_DistantPast"] = 64,
    ["Mayday_InGame_MapInfo_Time_Ancient"] = 64,
    ["Mayday_InGame_MapInfo_Time_Modern"] = 64,
    ["Mayday_InGame_MapInfo_Time_Future"] = 64,
    ["Mayday_InGame_MapInfo_Weather"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Sunny"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Rain"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Cloudy"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Fog"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Flood"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Thunder"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Sandstorm"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Blizzard"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Eclipse"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Warning"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Flood_Warning"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Thunder_Warning"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Sandstorm_Warning"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Blizzard_Warning"] = 64,
    ["Mayday_InGame_MapInfo_Weather_Eclipse_Warning"] = 64,
    ["Mayday_InGame_MapInfo_Null"] = 64,
    ["Mayday_InGame_MapInfo_Environment"] = 64,
    ["Mayday_InGame_MapInfo_Environment_Rainforest"] = 64,
    ["Mayday_InGame_MapInfo_Environment_Desert"] = 64,
    ["Mayday_InGame_MapInfo_Environment_Cold"] = 64,
    ["Mayday_InGame_MapInfo_Unknown"] = 64,
    ["Mayday_InGame_MapInfo_RiskLevel"] = 64,
    ["Mayday_InGame_MapInfo_High"] = 64,
    ["Mayday_InGame_MapInfo_High_Text"] = 64,
    ["Mayday_InGame_MapInfo_Middle"] = 64,
    ["Mayday_InGame_MapInfo_Middle_Text"] = 64,
    ["Mayday_InGame_MapInfo_Low"] = 64,
    ["Mayday_InGame_MapInfo_Low_Text"] = 64,
    ["Mayday_InGame_MapInfo_Null_Text"] = 64,
    ["Mayday_InGame_MapInfo_Level_High"] = 64,
    ["Mayday_InGame_MapInfo_Level_Middle"] = 64,
    ["Mayday_InGame_MapInfo_Level_Low"] = 64,
    ["Mayday_InGame_MapInfo_Level_SSS"] = 64,
    ["Mayday_InGame_MapInfo_Level_SS"] = 64,
    ["Mayday_InGame_MapInfo_Level_S"] = 64,
    ["Mayday_InGame_MapInfo_Level_A"] = 64,
    ["Mayday_InGame_MapInfo_Level_B"] = 64,
    ["Mayday_InGame_MapInfo_Level_C"] = 64,
    ["Mayday_InGame_MapInfo_Level_D"] = 64,
    ["Mayday_InGame_MapInfo_Specialty"] = 64,
    ["Mayday_InGame_MapInfo_Specialty2"] = 64,
    ["Mayday_InGame_MapInfo_PalaceOwner"] = 64,
    ["Mayday_InGame_MapInfo_PalaceOwner_Meiying"] = 64,
    ["Mayday_InGame_MapInfo_PalaceOwner_Jufeng"] = 64,
    ["Mayday_InGame_MapInfo_PalaceOwner_Tianxin"] = 64,
    ["Mayday_InGame_MapInfo_BossLike"] = 64,
    ["Mayday_InGame_MapInfo_MapCost"] = 64,
    ["Mayday_InGame_SystemNotice_Notice"] = 64,
    ["Mayday_InGame_SystemNotice_Time2Leave1"] = 64,
    ["Mayday_InGame_SystemNotice_NeedBack"] = 64,
    ["Mayday_InGame_SystemNotice_Time2Leave2"] = 64,
    ["Mayday_InGame_SystemNotice_Time2Leave3"] = 64,
    ["Mayday_InGame_SystemNotice_AllDead2Leave"] = 64,
    ["Mayday_InGame_TimeMachine_Hydraulic"] = 64,
    ["Mayday_InGame_TimeMachine_DoorOpen"] = 64,
    ["Mayday_InGame_TimeMachine_DoorClose"] = 64,
    ["Mayday_InGame_TimeMachine_DoorOpenKey"] = 64,
    ["Mayday_InGame_TimeMachine_DoorOpenKey_Toast1"] = 64,
    ["Mayday_InGame_TimeMachine_DoorOpenKey_Toast2"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Go_Press"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Go"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Press"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Go_Tips01"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Go_Tips02"] = 64,
    ["Mayday_InGame_TimeMachine_Tips01"] = 64,
    ["Mayday_InGame_TimeMachine_Tips02"] = 64,
    ["Mayday_InGame_TimeMachine_Tips03"] = 64,
    ["Mayday_InGame_TimeMachine_Button_GoTip01"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Emergency"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Monitor"] = 64,
    ["Mayday_InGame_TimeMachine_Button_ChangeMap"] = 64,
    ["Mayday_InGame_TimeMachine_Button_TargetMap"] = 64,
    ["Mayday_InGame_TimeMachine_Button_TeleportOut"] = 64,
    ["Mayday_InGame_TimeMachine_Button_TeleportBack"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Radar"] = 64,
    ["Mayday_InGame_TimeMachine_Button_Broadcast"] = 64,
    ["Mayday_InGame_TimeMachine_Broadcast_Tips"] = 64,
    ["Mayday_InGame_TimeMachine_Goal"] = 64,
    ["Mayday_InGame_TimeMachine_Location"] = 64,
    ["Mayday_InGame_TimeMachine_Value"] = 64,
    ["Mayday_InGame_TimeMachine_DaysRemain"] = 64,
    ["Mayday_InGame_TimeMachine_BeBusy_Toast"] = 64,
    ["Mayday_InGame_TimeMachine_StartTravel"] = 64,
    ["Mayday_InGame_TimeMachine_FindNewItem"] = 64,
    ["Mayday_InGame_TimeMachine_Toast_NoFlyKPI"] = 64,
    ["Mayday_InGame_DaysRemain"] = 65,
    ["Mayday_InGame_LastDay"] = 65,
    ["Mayday_InGame_Button_Confirm"] = 65,
    ["Mayday_InGame_Button_Cancel"] = 65,
    ["Mayday_InGame_Button_Power"] = 65,
    ["Mayday_InGame_Button_Open"] = 65,
    ["Mayday_InGame_Button_Close"] = 65,
    ["Mayday_InGame_Button_Pick"] = 65,
    ["Mayday_InGame_Button_Drop"] = 65,
    ["Mayday_InGame_Button_Scan"] = 65,
    ["Mayday_InGame_Scan_Item"] = 65,
    ["Mayday_InGame_Scan_Ghost"] = 65,
    ["Mayday_InGame_Scan_RiskLevel"] = 65,
    ["Mayday_InGame_Scan_ItemPick1"] = 65,
    ["Mayday_InGame_Scan_ItemPick2"] = 65,
    ["Mayday_InGame_Scan_Ship"] = 65,
    ["Mayday_InGame_Scan_Door1"] = 65,
    ["Mayday_InGame_Scan_HighValue"] = 65,
    ["Mayday_InGame_Scan_Door2"] = 65,
    ["Mayday_InGame_TotalValue"] = 65,
    ["Mayday_InGame_Button_Talk"] = 65,
    ["Mayday_InGame_VoiceChannel_Close"] = 65,
    ["Mayday_InGame_VoiceChannel_Near"] = 65,
    ["Mayday_InGame_VoiceChannel_OB"] = 65,
    ["Mayday_InGame_VoiceChannel_OB_Tips"] = 65,
    ["Mayday_InGame_OB_Recheck"] = 65,
    ["Mayday_InGame_Tips_OpenMic"] = 65,
    ["Mayday_InGame_Tips_TurnOnSpeaker"] = 65,
    ["Mayday_InGame_OpenDoor"] = 65,
    ["Mayday_InGame_OpenDoor_Progress"] = 65,
    ["Mayday_InGame_ClimbUp"] = 65,
    ["Mayday_InGame_Fall"] = 65,
    ["Mayday_InGame_Back"] = 65,
    ["Mayday_InGame_Alive"] = 65,
    ["Mayday_InGame_Dead"] = 65,
    ["Mayday_InGame_Dead2"] = 65,
    ["Mayday_InGame_WaitingAfterDead"] = 65,
    ["Mayday_InGame_Body"] = 65,
    ["Mayday_InGame_Body_Button_Check"] = 65,
    ["Mayday_InGame_Body_Button_Report"] = 65,
    ["Mayday_InGame_Body_Button_Move"] = 65,
    ["Mayday_InGame_Body_New"] = 65,
    ["Mayday_InGame_Body_Info"] = 65,
    ["Mayday_InGame_Body_Info2"] = 65,
    ["Mayday_InGame_Body_Location"] = 65,
    ["Mayday_InGame_Body_Location_Room"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom01"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom02"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom03"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom04"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom05"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom06"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom07"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom08"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom09"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom10"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom11"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom12"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom13"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom14"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom15"] = 65,
    ["Mayday_InGame_Body_Location_RainRoom16"] = 65,
    ["Mayday_InGame_Body_Location_Forbidden"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest01"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest02"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest03"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest04"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest05"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest06"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest07"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest08"] = 65,
    ["Mayday_InGame_Body_Location_Rainforest09"] = 65,
    ["Mayday_InGame_Need2Guys"] = 65,
    ["Mayday_InGame_Body_DeathTime"] = 65,
    ["Mayday_InGame_Body_DeathLocale"] = 65,
    ["Mayday_InGame_Body_DeathInfo"] = 65,
    ["Mayday_InGame_Body_DeathInfo_Nobody"] = 65,
    ["Mayday_InGame_Body_Reason"] = 65,
    ["Mayday_InGame_Body_Reason_Default"] = 65,
    ["Mayday_InGame_Body_Reason_Fall"] = 65,
    ["Mayday_InGame_Body_Reason_Monster"] = 65,
    ["Mayday_InGame_Body_Reason_Teammate"] = 65,
    ["Mayday_InGame_Body_Reason_AIInjured"] = 65,
    ["Mayday_InGame_Body_Reason_Water"] = 65,
    ["Mayday_InGame_Body_Reason_Vote"] = 65,
    ["Mayday_InGame_Body_Reason_Accident"] = 65,
    ["Mayday_InGame_Body_Reason_MoleAttack"] = 65,
    ["Mayday_InGame_Body_Reason_DeathZone"] = 65,
    ["Mayday_InGame_Body_Reason_Fly"] = 65,
    ["Mayday_InGame_Body_Reason_Share"] = 65,
    ["Mayday_InGame_Body_Reason_Unkown"] = 65,
    ["Mayday_InGame_Body_Toast_NewOne"] = 65,
    ["Mayday_InGame_Item"] = 65,
    ["Mayday_InGame_Item_ItemPick_Tips1"] = 65,
    ["Mayday_InGame_Item_ItemPick_Tips2"] = 65,
    ["Mayday_InGame_Item_ItemPick_Toast"] = 65,
    ["Mayday_InGame_Weight"] = 65,
    ["Mayday_InGame_Item_Value"] = 65,
    ["Mayday_InGame_Item_Value_High"] = 65,
    ["Mayday_InGame_Twohands"] = 65,
    ["Mayday_InGame_Item_Flashlight"] = 66,
    ["Mayday_InGame_Item_Flashlight_Info"] = 66,
    ["Mayday_InGame_Item_FlashlightPro"] = 66,
    ["Mayday_InGame_Item_FlashlightPro_Info"] = 66,
    ["Mayday_InGame_Item_Stick"] = 66,
    ["Mayday_InGame_Item_Stick_Info"] = 66,
    ["Mayday_InGame_Item_WalkieTalkie"] = 66,
    ["Mayday_InGame_Item_WalkieTalkie_Info"] = 66,
    ["Mayday_InGame_Item_WalkieTalkie_UseText"] = 66,
    ["Mayday_InGame_Item_Stereo"] = 66,
    ["Mayday_InGame_Item_Stereo_Info"] = 66,
    ["Mayday_InGame_Item_Stereo_BtnOn"] = 66,
    ["Mayday_InGame_Item_Stereo_BtnOff"] = 66,
    ["Mayday_InGame_Item_Stereo_BtnSkip"] = 66,
    ["Mayday_InGame_Item_Stereo_BtnDance"] = 66,
    ["Mayday_InGame_Item_Ladder"] = 66,
    ["Mayday_InGame_Item_Ladder_Info"] = 66,
    ["Mayday_InGame_Item_Ladder_ToastNo"] = 66,
    ["Mayday_InGame_Item_Jetpack"] = 66,
    ["Mayday_InGame_Item_Jetpack_Info"] = 66,
    ["Mayday_InGame_Item_Jetpack_UseText"] = 66,
    ["Mayday_InGame_Item_Lockpick"] = 66,
    ["Mayday_InGame_Item_Lockpick_Info"] = 66,
    ["Mayday_InGame_Item_Lockpick_UseText"] = 66,
    ["Mayday_InGame_Item_Shotgun"] = 66,
    ["Mayday_InGame_Item_Bullet"] = 66,
    ["Mayday_InGame_Item_TimeGem"] = 66,
    ["Mayday_InGame_Item_TimeGem_Info"] = 66,
    ["Mayday_InGame_Item_GoldBrick"] = 66,
    ["Mayday_InGame_Item_GoldBrick_Info"] = 66,
    ["Mayday_InGame_Item_DivinationCrystal"] = 66,
    ["Mayday_InGame_Item_DivinationCrystal_Info"] = 66,
    ["Mayday_InGame_Item_SweetPoison"] = 66,
    ["Mayday_InGame_Item_SweetPoison_Info"] = 66,
    ["Mayday_InGame_Item_OwlRobot"] = 66,
    ["Mayday_InGame_Item_AntiqueVase"] = 66,
    ["Mayday_InGame_Item_HotPot"] = 66,
    ["Mayday_InGame_Item_HotPot_Info"] = 66,
    ["Mayday_InGame_Item_TableLamp"] = 66,
    ["Mayday_InGame_Item_DemonMask"] = 66,
    ["Mayday_InGame_Item_DemonMask_Info"] = 66,
    ["Mayday_InGame_Item_DemonMask_UseText"] = 66,
    ["Mayday_InGame_Item_DrinkingSet"] = 66,
    ["Mayday_InGame_Item_PigNose"] = 66,
    ["Mayday_InGame_Item_Cookies"] = 66,
    ["Mayday_InGame_Item_KeyRuin"] = 66,
    ["Mayday_InGame_Item_Key_Info"] = 66,
    ["Mayday_InGame_DoorNeedKey"] = 66,
    ["Mayday_InGame_Item_CrystalSkull"] = 66,
    ["Mayday_InGame_Item_CrystalSkull_Info"] = 66,
    ["Mayday_InGame_Item_GoldMask"] = 66,
    ["Mayday_InGame_Item_GoldMask_Info"] = 66,
    ["Mayday_InGame_Item_GoldNecklace"] = 66,
    ["Mayday_InGame_Item_DeerDecoration"] = 66,
    ["Mayday_InGame_Item_SpiceJar"] = 66,
    ["Mayday_InGame_Item_DivinationTablet"] = 66,
    ["Mayday_InGame_Item_CeramicFigure"] = 66,
    ["Mayday_InGame_Item_SweetBread"] = 66,
    ["Mayday_InGame_Item_EarthenwareJar"] = 66,
    ["Mayday_InGame_Item_OilLamp"] = 66,
    ["Mayday_InGame_Item_TimeGem2"] = 66,
    ["Mayday_InGame_Item_GoldBrick2"] = 66,
    ["Mayday_InGame_Item_DivinationCrystal2"] = 66,
    ["Mayday_InGame_Item_SweetPoison2"] = 66,
    ["Mayday_InGame_Item_OwlRobot2"] = 66,
    ["Mayday_InGame_Item_AntiqueVase2"] = 66,
    ["Mayday_InGame_Item_HotPot2"] = 66,
    ["Mayday_InGame_Item_TableLamp2"] = 66,
    ["Mayday_InGame_Item_DemonMask2"] = 66,
    ["Mayday_InGame_Item_DrinkingSet2"] = 66,
    ["Mayday_InGame_Item_KeyRuin2"] = 66,
    ["Mayday_InGame_Item_CrystalSkull2"] = 66,
    ["Mayday_InGame_Item_GoldMask2"] = 66,
    ["Mayday_InGame_Item_GoldNecklace2"] = 66,
    ["Mayday_InGame_Item_DeerDecoration2"] = 66,
    ["Mayday_InGame_Item_SpiceJar2"] = 66,
    ["Mayday_InGame_Item_DivinationTablet2"] = 66,
    ["Mayday_InGame_Item_CeramicFigure2"] = 66,
    ["Mayday_InGame_Item_SweetBread2"] = 66,
    ["Mayday_InGame_Item_EarthenwareJar2"] = 66,
    ["Mayday_InGame_Item_OilLamp2"] = 66,
    ["Mayday_InGame_Item_TimeGem3"] = 66,
    ["Mayday_InGame_Item_GoldBrick3"] = 66,
    ["Mayday_InGame_Item_DivinationCrystal3"] = 66,
    ["Mayday_InGame_Item_SweetPoison3"] = 66,
    ["Mayday_InGame_Item_OwlRobot3"] = 66,
    ["Mayday_InGame_Item_AntiqueVase3"] = 66,
    ["Mayday_InGame_Item_HotPot3"] = 66,
    ["Mayday_InGame_Item_TableLamp3"] = 66,
    ["Mayday_InGame_Item_DemonMask3"] = 66,
    ["Mayday_InGame_Item_DrinkingSet3"] = 66,
    ["Mayday_InGame_Item_KeyRuin3"] = 66,
    ["Mayday_InGame_Item_CrystalSkull3"] = 66,
    ["Mayday_InGame_Item_GoldMask3"] = 66,
    ["Mayday_InGame_Item_GoldNecklace3"] = 66,
    ["Mayday_InGame_Item_DeerDecoration3"] = 66,
    ["Mayday_InGame_Item_SpiceJar3"] = 66,
    ["Mayday_InGame_Item_DivinationTablet3"] = 66,
    ["Mayday_InGame_Item_CeramicFigure3"] = 66,
    ["Mayday_InGame_Item_SweetBread3"] = 66,
    ["Mayday_InGame_Item_EarthenwareJar3"] = 67,
    ["Mayday_InGame_Item_OilLamp3"] = 67,
    ["Mayday_InGame_Item_TimeGem4"] = 67,
    ["Mayday_InGame_Item_GoldBrick4"] = 67,
    ["Mayday_InGame_Item_DivinationCrystal4"] = 67,
    ["Mayday_InGame_Item_SweetPoison4"] = 67,
    ["Mayday_InGame_Item_OwlRobot4"] = 67,
    ["Mayday_InGame_Item_AntiqueVase4"] = 67,
    ["Mayday_InGame_Item_HotPot4"] = 67,
    ["Mayday_InGame_Item_TableLamp4"] = 67,
    ["Mayday_InGame_Item_DemonMask4"] = 67,
    ["Mayday_InGame_Item_DrinkingSet4"] = 67,
    ["Mayday_InGame_Item_KeyRuin4"] = 67,
    ["Mayday_InGame_Item_CrystalSkull4"] = 67,
    ["Mayday_InGame_Item_GoldMask4"] = 67,
    ["Mayday_InGame_Item_GoldNecklace4"] = 67,
    ["Mayday_InGame_Item_DeerDecoration4"] = 67,
    ["Mayday_InGame_Item_SpiceJar4"] = 67,
    ["Mayday_InGame_Item_DivinationTablet4"] = 67,
    ["Mayday_InGame_Item_CeramicFigure4"] = 67,
    ["Mayday_InGame_Item_SweetBread4"] = 67,
    ["Mayday_InGame_Item_EarthenwareJar4"] = 67,
    ["Mayday_InGame_Item_OilLamp4"] = 67,
    ["Mayday_InGame_Item_Monster01"] = 67,
    ["Mayday_InGame_Item_Monster02"] = 67,
    ["Mayday_InGame_Item_Monster03"] = 67,
    ["Mayday_InGame_Item_Monster04"] = 67,
    ["Mayday_InGame_Item_TeleportPlatform"] = 67,
    ["Mayday_InGame_Item_TeleportPlatform_Info"] = 67,
    ["Mayday_InGame_Item_TeleportPlatform_Btn"] = 67,
    ["Mayday_InGame_Item_TeleportPlatform_Toast"] = 67,
    ["Mayday_InGame_Item_TeleportPlatform_ToastNo"] = 67,
    ["Mayday_InGame_Item_Shutgun"] = 67,
    ["Mayday_InGame_Item_Shutgun_Info"] = 67,
    ["Mayday_InGame_Item_MoveBox"] = 67,
    ["Mayday_InGame_Item_MoveBox_Info1"] = 67,
    ["Mayday_InGame_Item_MoveBox_Info2"] = 67,
    ["Mayday_InGame_Item_MoveBox_ButtonSave"] = 67,
    ["Mayday_InGame_Item_MoveBox_ButtonTake"] = 67,
    ["Mayday_InGame_Item_MoveBox_ButtonPull"] = 67,
    ["Mayday_InGame_Item_MoveBox_ButtonPush"] = 67,
    ["Mayday_InGame_Item_MoveBox_ButtonLeave"] = 67,
    ["Mayday_InGame_Item_MoveBox_GoodsInBox"] = 67,
    ["Mayday_InGame_Item_MoveBox_TakeGoodsInBox"] = 67,
    ["Mayday_InGame_Item_MoveBox_Capacity"] = 67,
    ["Mayday_InGame_Item_Toast_Save"] = 67,
    ["Mayday_InGame_Item_Toast_Take"] = 67,
    ["Mayday_InGame_Item_Toast_Full"] = 67,
    ["Mayday_InGame_Item_Tips_RestRange_Pack"] = 67,
    ["Mayday_InGame_Item_Tips_Distance_MoveBox"] = 67,
    ["Mayday_InGame_ItemTag"] = 67,
    ["Mayday_InGame_ItemTag_Gold"] = 67,
    ["Mayday_InGame_ItemTag_Energe"] = 67,
    ["Mayday_InGame_ItemTag_Sacrifice"] = 67,
    ["Mayday_InGame_ItemTag_Mask"] = 67,
    ["Mayday_InGame_ItemTag_Animal"] = 67,
    ["Mayday_InGame_ItemTag_Jewelry"] = 67,
    ["Mayday_InGame_ItemTag_Decoration"] = 67,
    ["Mayday_InGame_ItemTag_Food"] = 67,
    ["Mayday_InGame_ItemTag_Spicy"] = 67,
    ["Mayday_InGame_ItemTag_Sweet"] = 67,
    ["Mayday_InGame_ItemTag_Divination"] = 67,
    ["Mayday_InGame_ItemTag_Effigy"] = 67,
    ["Mayday_InGame_ItemTag_Life"] = 67,
    ["Mayday_InGame_Item_Tips_Pick_Me"] = 67,
    ["Mayday_InGame_Item_Tips_Pick_Member"] = 67,
    ["Mayday_InGame_Item_Tips_Pick_Self"] = 67,
    ["Mayday_InGame_Item_Tips_CannotPick_Hand"] = 67,
    ["Mayday_InGame_Item_Tips_CannotPick_Pack"] = 67,
    ["Mayday_InGame_Money"] = 67,
    ["Mayday_InGame_PickThingsValue"] = 67,
    ["Mayday_InGame_Scene_MainEntrance"] = 67,
    ["Mayday_InGame_Scene_Terminal"] = 67,
    ["Mayday_InGame_Scene_Terminal_Info"] = 67,
    ["Mayday_InGame_DoorMonitor"] = 67,
    ["Mayday_InGame_DoorMonitor_Info"] = 67,
    ["Mayday_InGame_Store"] = 67,
    ["Mayday_InGame_Store_Info"] = 67,
    ["Mayday_InGame_Store_Sell"] = 67,
    ["Mayday_InGame_Store_Sell_Press"] = 67,
    ["Mayday_InGame_Store_SellConfirmed"] = 67,
    ["Mayday_InGame_Store_SellText"] = 67,
    ["Mayday_InGame_Store_MoneyNotEnough"] = 67,
    ["Mayday_InGame_Store_TeamsMoney"] = 67,
    ["Mayday_InGame_Store_Team"] = 67,
    ["Mayday_InGame_Store_Recycle"] = 67,
    ["Mayday_InGame_Store_GotMoneyToast"] = 67,
    ["Mayday_InGame_Store_Buy"] = 67,
    ["Mayday_InGame_Store_Buy_Limited01"] = 67,
    ["Mayday_InGame_Store_Buy_Limited02"] = 67,
    ["Mayday_InGame_Store_GoodsName"] = 67,
    ["Mayday_InGame_Store_GoodsQuantity"] = 67,
    ["Mayday_InGame_Store_GoodsPrice"] = 67,
    ["Mayday_InGame_Store_GoodsTotalValue"] = 67,
    ["Mayday_InGame_Store_GoodsSellPrice"] = 67,
    ["Mayday_InGame_Store_Tatal"] = 67,
    ["Mayday_InGame_Store_SellDiscount01"] = 67,
    ["Mayday_InGame_Store_SellDiscount02"] = 67,
    ["Mayday_InGame_Store_SellDiscount03"] = 67,
    ["Mayday_InGame_Store_IsFinish"] = 67,
    ["Mayday_InGame_Store_Wait"] = 68,
    ["Mayday_InGame_Store_Express00"] = 68,
    ["Mayday_InGame_Store_ExpressToast01"] = 68,
    ["Mayday_InGame_Store_ExpressToast02"] = 68,
    ["Mayday_InGame_Store_ExpressToast03"] = 68,
    ["Mayday_InGame_Store_ExpressToast04"] = 68,
    ["Mayday_InGame_Store_ExpressToast05"] = 68,
    ["Mayday_InGame_Store_ExpressToast06"] = 68,
    ["Mayday_InGame_Store_ExpressToast_NoMoney"] = 68,
    ["Mayday_InGame_Store_Express_Wait"] = 68,
    ["Mayday_InGame_Store_Express_Count"] = 68,
    ["Mayday_InGame_Store_Express_CountDown"] = 68,
    ["Mayday_InGame_Store_Bought"] = 68,
    ["Mayday_InGame_Store_BuyDiscount"] = 68,
    ["Mayday_InGame_Store_Sale"] = 68,
    ["Mayday_InGame_KingsPalace_HandIn1"] = 68,
    ["Mayday_InGame_KingsPalace_HandIn2"] = 68,
    ["Mayday_Ingame_TeamRank"] = 68,
    ["Mayday_Ingame_TeamRank_SSS"] = 68,
    ["Mayday_Ingame_TeamRank_SS"] = 68,
    ["Mayday_Ingame_TeamRank_S"] = 68,
    ["Mayday_Ingame_TeamRank_A"] = 68,
    ["Mayday_Ingame_TeamRank_B"] = 68,
    ["Mayday_Ingame_TeamRank_C"] = 68,
    ["Mayday_Ingame_TeamRank_D"] = 68,
    ["Mayday_Ingame_TeamRank_E"] = 68,
    ["Mayday_Ingame_TeamRank_F"] = 68,
    ["Mayday_Ingame_TeamPickValue"] = 68,
    ["Mayday_Ingame_LocationValue"] = 68,
    ["Mayday_InGame_PlayerState_Die"] = 68,
    ["Mayday_InGame_PlayerState_DieReason"] = 68,
    ["Mayday_InGame_OB"] = 68,
    ["Mayday_InGame_Vote"] = 68,
    ["Mayday_InGame_Vote_Text"] = 68,
    ["Mayday_InGame_Fines"] = 68,
    ["Mayday_InGame_DeadPlayerCount"] = 68,
    ["Mayday_InGame_GetBackBody"] = 68,
    ["Mayday_InGame_DUE"] = 68,
    ["Mayday_InGame_DayEnd"] = 68,
    ["Mayday_InGame_DayEnd_GoalProgress"] = 68,
    ["Mayday_InGame_DayEnd_WorkHardLines01"] = 68,
    ["Mayday_InGame_DayEnd_WorkHardLines02"] = 68,
    ["Mayday_InGame_DayEnd_WorkHardLines03"] = 68,
    ["Mayday_InGame_DayEnd_LastDayLines01"] = 68,
    ["Mayday_InGame_DayEnd_LastDayLines02"] = 68,
    ["Mayday_InGame_DayEnd_DoneLines01"] = 68,
    ["Mayday_InGame_DayEnd_PalaceLines01"] = 68,
    ["Mayday_InGame_GameStart_BossLines01"] = 68,
    ["Mayday_InGame_GameStart_BossLines02"] = 68,
    ["Mayday_InGame_GameStart_BossLines03"] = 68,
    ["Mayday_InGame_GameStart_BossLines04"] = 68,
    ["Mayday_InGame_GameStart_BossLines05"] = 68,
    ["Mayday_InGame_GameStart_BossLines06"] = 68,
    ["Mayday_InGame_Guide_Target"] = 68,
    ["Mayday_InGame_Guide_Gather01"] = 68,
    ["Mayday_InGame_Guide_Gather02"] = 68,
    ["Mayday_InGame_Guide_Go"] = 68,
    ["Mayday_InGame_Guide_Back"] = 68,
    ["Mayday_InGame_Guide_MeetBoss"] = 68,
    ["Mayday_InGame_Guide_PutAllGoods"] = 68,
    ["Mayday_InGame_Guide_End"] = 68,
    ["Mayday_EndGame_Seccess"] = 68,
    ["Mayday_EndGame_Seccess_Info"] = 68,
    ["Mayday_EndGame_Failed"] = 68,
    ["Mayday_EndGame_Failed_Info"] = 68,
    ["Mayday_EndGame_StarSeccess"] = 68,
    ["Mayday_EndGame_SpySeccess"] = 68,
    ["Mayday_EndGame_StarFailed"] = 68,
    ["Mayday_EndGame_SpyFailed"] = 68,
    ["Mayday_EndGame_Reason_Spy01"] = 68,
    ["Mayday_EndGame_Reason_Spy02"] = 68,
    ["Mayday_EndGame_Reason_Spy03"] = 68,
    ["Mayday_EndGame_Reason_Spy04"] = 68,
    ["Mayday_Monster_ShadowGhost"] = 68,
    ["Mayday_Monster_T009"] = 68,
    ["Mayday_Monster_ScarletSpider"] = 68,
    ["Mayday_Monster_Mole"] = 68,
    ["Mayday_Monster_AbyssalLizard"] = 68,
    ["Mayday_Monster_BatApe"] = 68,
    ["Mayday_Monster_BatApe_QTE"] = 68,
    ["Mayday_Monster_BatApe_ToastHelp01"] = 68,
    ["Mayday_Monster_BatApe_ToastHelp02"] = 68,
    ["Mayday_Monster_DemonMask"] = 68,
    ["Mayday_Monster_StoneGuard"] = 68,
    ["Mayday_Monster_LordAbyss"] = 68,
    ["Mayday_Monster_ClockGhost"] = 68,
    ["Mayday_Monster_RaccoonToy"] = 68,
    ["Mayday_Monster_BatSwarm"] = 68,
    ["Mayday_Monster_SlimeMimics"] = 68,
    ["Mayday_Monster_GiantGargoyle"] = 68,
    ["Mayday_Monster_GiantGargoyle_QTE"] = 68,
    ["Mayday_Monster_SandLocust"] = 68,
    ["Mayday_InGame_Quit_Recheck"] = 68,
    ["Mayday_InGame_NoOxygen"] = 68,
    ["Mayday_FTUE_Step01_Title"] = 68,
    ["Mayday_FTUE_Step01_Text"] = 68,
    ["Mayday_FTUE_Step02_Title"] = 68,
    ["Mayday_FTUE_Step02_Text"] = 68,
    ["Mayday_FTUE_Step03_Title"] = 68,
    ["Mayday_FTUE_Step03_Text"] = 68,
    ["Mayday_FTUE_Step04_Title"] = 69,
    ["Mayday_FTUE_Step04_Text"] = 69,
    ["Mayday_FTUE_Step05_Title"] = 69,
    ["Mayday_FTUE_Step05_Text"] = 69,
    ["Mayday_FTUESpy_Step01_Title"] = 69,
    ["Mayday_FTUESpy_Step01_Text"] = 69,
    ["Mayday_FTUESpy_Step02_Title"] = 69,
    ["Mayday_FTUESpy_Step02_Text"] = 69,
    ["Mayday_FTUESpy_Step03_Title"] = 69,
    ["Mayday_FTUESpy_Step03_Text"] = 69,
    ["Mayday_FTUESpy_Step04_Title"] = 69,
    ["Mayday_FTUESpy_Step04_Text"] = 69,
    ["Mayday_FTUESpy_Step05_Title"] = 69,
    ["Mayday_FTUESpy_Step05_Text"] = 69,
    ["Mayday_Spy"] = 69,
    ["Mayday_Spy_WhoAmI"] = 69,
    ["Mayday_Spy_IdentityInfo"] = 69,
    ["Mayday_Spy_Good"] = 69,
    ["Mayday_Spy_GoodMissionInfo"] = 69,
    ["Mayday_Spy_GoodHint"] = 69,
    ["Mayday_Spy_GoodHintInfo"] = 69,
    ["Mayday_Spy_GoodMissionInfo01"] = 69,
    ["Mayday_Spy_GoodMissionInfo02"] = 69,
    ["Mayday_Spy_GoodMissionTitle"] = 69,
    ["Mayday_Spy_GoodMissionText01"] = 69,
    ["Mayday_Spy_GoodMissionText02"] = 69,
    ["Mayday_Spy_GoodWin_Tips01"] = 69,
    ["Mayday_Spy_Spy"] = 69,
    ["Mayday_Spy_SpyMissionTitle"] = 69,
    ["Mayday_Spy_SpyMissionInfo"] = 69,
    ["Mayday_Spy_SpyMissionInfo01"] = 69,
    ["Mayday_Spy_SpyMissionInfo02"] = 69,
    ["Mayday_Spy_SpyPowerInfo"] = 69,
    ["Mayday_Spy_SpyMissionTitile"] = 69,
    ["Mayday_Spy_SpyMissionText01"] = 69,
    ["Mayday_Spy_SpyMissionText02"] = 69,
    ["Mayday_Spy_SpyMissionText03"] = 69,
    ["Mayday_Spy_IdentityConfirmed"] = 69,
    ["Mayday_Spy_SpyPower"] = 69,
    ["Mayday_Spy_SpyPowerList"] = 69,
    ["Mayday_Spy_SpyPower01"] = 69,
    ["Mayday_Spy_SpyPower01Icon"] = 69,
    ["Mayday_Spy_SpyPower01Text"] = 69,
    ["Mayday_Spy_SpyPower01Text2"] = 69,
    ["Mayday_Spy_SpyPower01ChooseMonster"] = 69,
    ["Mayday_Spy_SpyPower01Toast01"] = 69,
    ["Mayday_Spy_SpyPower01Toast02"] = 69,
    ["Mayday_Spy_SpyPower02"] = 69,
    ["Mayday_Spy_SpyPower02Icon"] = 69,
    ["Mayday_Spy_SpyPower02Text"] = 69,
    ["Mayday_Spy_SpyPower02Text2"] = 69,
    ["Mayday_Spy_SpyPower02Toast"] = 69,
    ["Mayday_Spy_SpyPower03"] = 69,
    ["Mayday_Spy_SpyPower03Icon"] = 69,
    ["Mayday_Spy_SpyPower03Text"] = 69,
    ["Mayday_Spy_SpyPower03Text2"] = 69,
    ["Mayday_Spy_SpyPower_NotEnough"] = 69,
    ["Mayday_Spy_SpyPower_CD"] = 69,
    ["Mayday_Spy_SpyPower_Boat"] = 69,
    ["Mayday_Spy_MeetingStart"] = 69,
    ["Mayday_Spy_Meeting_Tips1"] = 69,
    ["Mayday_Spy_Meeting_Tips2"] = 69,
    ["Mayday_Spy_Meeting_Tips3"] = 69,
    ["Mayday_Spy_Meeting_Tips4"] = 69,
    ["Mayday_Spy_Meeting_Tips5"] = 69,
    ["Mayday_Spy_Meeting_Tips6"] = 69,
    ["Mayday_Spy_FoundBody"] = 69,
    ["Mayday_Spy_FoundBodyText"] = 69,
    ["Mayday_Spy_EmergencyMeetingText"] = 69,
    ["Mayday_Spy_Reporter"] = 69,
    ["Mayday_Spy_Discussion"] = 69,
    ["Mayday_Spy_Discussion_CannotDiscuss1"] = 69,
    ["Mayday_Spy_Discussion_CannotDiscuss2"] = 69,
    ["Mayday_Spy_Discussion_CannotDiscuss3"] = 69,
    ["Mayday_Spy_Discussion_Tips01"] = 69,
    ["Mayday_Spy_Discussion_Close"] = 69,
    ["Mayday_Spy_Discussion_StartDiscuss"] = 69,
    ["Mayday_Spy_Discussion_PlayersTurn"] = 69,
    ["Mayday_Spy_Discussion_YourTurnTips"] = 69,
    ["Mayday_Spy_Discussion_Saying"] = 69,
    ["Mayday_Spy_Discussion_Meeting"] = 69,
    ["Mayday_Spy_Discussion_Pass"] = 69,
    ["Mayday_Spy_Discussion_Button_Over"] = 69,
    ["Mayday_Spy_Discussion_Button_Say"] = 69,
    ["Mayday_Spy_Discussion_FoundBody"] = 69,
    ["Mayday_Spy_Discussion_FoundBodyText"] = 69,
    ["Mayday_Spy_Discussion_BtnInfo"] = 69,
    ["Mayday_Spy_Vote"] = 69,
    ["Mayday_Spy_Vote2"] = 69,
    ["Mayday_Spy_Vote_Text"] = 69,
    ["Mayday_Spy_Vote_Progress"] = 69,
    ["Mayday_Spy_Vote_Finish"] = 69,
    ["Mayday_Spy_Vote_Button_Vote"] = 69,
    ["Mayday_Spy_Vote_Button_Cancel"] = 69,
    ["Mayday_Spy_Vote_Recheck"] = 69,
    ["Mayday_Spy_Vote_Recheck_Text"] = 69,
    ["Mayday_Spy_Vote_Recheck_Yes"] = 69,
    ["Mayday_Spy_Vote_Recheck_No"] = 69,
    ["Mayday_Spy_Vote_Voted"] = 69,
    ["Mayday_Spy_Vote_Result_NoOne"] = 69,
    ["Mayday_Spy_Vote_Result_NoOneText"] = 70,
    ["Mayday_Spy_Vote_Result_SomeOne"] = 70,
    ["Mayday_Spy_Vote_Result_SomeOne01"] = 70,
    ["Mayday_Spy_Vote_Result_SomeOne02"] = 70,
    ["Mayday_Spy_Vote_Toast"] = 70,
    ["Mayday_Spy_DeadNotice"] = 70,
    ["Mayday_ItemBox"] = 70,
    ["Mayday_ItemBox_Button_HandIn"] = 70,
    ["Mayday_ItemBox_Content"] = 70,
    ["Mayday_ItemBox_Content_Btn"] = 70,
    ["Mayday_ItemBox_Info"] = 70,
    ["Mayday_ItemBox_Button_Confirmed"] = 70,
    ["Mayday_ItemBox_Discount"] = 70,
    ["Mayday_ItemBox_Value"] = 70,
    ["Mayday_ItemBox_Toast"] = 70,
    ["Mayday_ItemBox_Progress"] = 70,
    ["Mayday_ItemBox_Full"] = 70,
    ["Mayday_ItemBox_Spy"] = 70,
    ["Mayday_ItemBox_WillGet"] = 70,
    ["Mayday_ItemBox_Got"] = 70,
    ["Mayday_ItemBox_GotItems"] = 70,
    ["Mayday_InGame_ItemBox_Tips1"] = 70,
    ["Mayday_InGame_ItemBox_Tips2"] = 70,
    ["Mayday_InGame_ItemBox_Tips3"] = 70,
    ["Mayday_InGame_ItemBox_Tips4"] = 70,
    ["Mayday_InGame_ItemBox_Tips5"] = 70,
    ["Mayday_Occupation"] = 70,
    ["Mayday_Occupation_WhoAmI"] = 70,
    ["Mayday_Occupation_SkillList"] = 70,
    ["Mayday_Occupation_SkillCD"] = 70,
    ["Mayday_Occupation_SpySkill"] = 70,
    ["Mayday_Occupation_OccupationSkill"] = 70,
    ["Mayday_Occupation_Info"] = 70,
    ["Mayday_Occupation_Economics"] = 70,
    ["Mayday_Occupation_Assist"] = 70,
    ["Mayday_Occupation_SkillUp"] = 70,
    ["Mayday_Occupation01"] = 70,
    ["Mayday_Occupation01Skill01"] = 70,
    ["Mayday_Occupation01Skill01_Info"] = 70,
    ["Mayday_Occupation01Skill02_toast1"] = 70,
    ["Mayday_Occupation_toast_Land1"] = 70,
    ["Mayday_InGame_Item_Beacon"] = 70,
    ["Mayday_InGame_Item_Beacon_Info"] = 70,
    ["Mayday_InGame_Item_Beacon_ButtonPlace"] = 70,
    ["Mayday_InGame_Item_Beacon_ButtonRecovery"] = 70,
    ["Mayday_InGame_Item_Beacon_ButtonRotate"] = 70,
    ["Mayday_InGame_Item_Beacon_Tips1"] = 70,
    ["Mayday_InGame_Item_Pad"] = 70,
    ["Mayday_InGame_Item_Pad_Info"] = 70,
    ["Mayday_InGame_Item_Pad_Btn_Room"] = 70,
    ["Mayday_InGame_Item_Pad_Btn_Wild"] = 70,
    ["Mayday_InGame_Item_Pad_Btn_Normal"] = 70,
    ["Mayday_InGame_Item_Pad_Btn_Map"] = 70,
    ["Mayday_InGame_Item_Pad_Btn_Teleport"] = 70,
    ["Mayday_InGame_Item_Pad_Btn_TeleportInfo"] = 70,
    ["Mayday_Occupation01_Toast01"] = 70,
    ["Mayday_Occupation01_Toast02"] = 70,
    ["Mayday_Occupation01_Unlimited_SkillBuff01"] = 70,
    ["Mayday_Occupation01_Unlimited_SkillBuff02"] = 70,
    ["Mayday_Occupation01_Unlimited_SkillBuff03"] = 70,
    ["Mayday_Occupation01_Unlimited_SkillBuff04"] = 70,
    ["Mayday_Occupation01_Unlimited_SkillBuff05"] = 70,
    ["Mayday_Occupation01_Unlimited_SkillBuff10"] = 70,
    ["Mayday_Occupation02"] = 70,
    ["Mayday_Occupation02Skill01"] = 70,
    ["Mayday_Occupation02Skill01_Info"] = 70,
    ["Mayday_Occupation02Skill01_InfoSpy"] = 70,
    ["Mayday_InGame_Item_RopeGun"] = 70,
    ["Mayday_InGame_Item_RopeGun_Info"] = 70,
    ["Mayday_InGame_Item_RopeGun_ButtonShoot"] = 70,
    ["Mayday_InGame_Item_RopeGun_ButtonPull"] = 70,
    ["Mayday_InGame_Item_RopeGun_ButtonBreak"] = 70,
    ["Mayday_InGame_Item_RopeGun_ButtonImprison"] = 70,
    ["Mayday_Occupation02_ToastImprison"] = 70,
    ["Mayday_Occupation02_Unlimited_SkillBuff01"] = 70,
    ["Mayday_Occupation02_Unlimited_SkillBuff02"] = 70,
    ["Mayday_Occupation02_Unlimited_SkillBuff03"] = 70,
    ["Mayday_Occupation02_Unlimited_SkillBuff04"] = 70,
    ["Mayday_Occupation02_Unlimited_SkillBuff05"] = 70,
    ["Mayday_Occupation02_Unlimited_SkillBuff10"] = 70,
    ["Mayday_Occupation03"] = 70,
    ["Mayday_Occupation03Skill01Self"] = 70,
    ["Mayday_Occupation03Skill01"] = 70,
    ["Mayday_Occupation03Skill01Self_Info"] = 70,
    ["Mayday_Occupation03Skill01_Info"] = 70,
    ["Mayday_Occupation03Skill01_InfoSpy"] = 70,
    ["Mayday_InGame_Item_MedicineGun"] = 70,
    ["Mayday_InGame_Item_MedicineGun_Info"] = 70,
    ["Mayday_InGame_Item_MedicineGun_BtnHealth"] = 70,
    ["Mayday_InGame_Item_MedicineGun_BtnKill"] = 70,
    ["Mayday_Occupation03_Toast01"] = 70,
    ["Mayday_Occupation03_Toast02"] = 70,
    ["Mayday_Occupation03_Toast03"] = 70,
    ["Mayday_Occupation03_Toast04"] = 70,
    ["Mayday_Occupation03_Unlimited_SkillBuff01"] = 70,
    ["Mayday_Occupation03_Unlimited_SkillBuff02"] = 70,
    ["Mayday_Occupation03_Unlimited_SkillBuff03"] = 70,
    ["Mayday_Occupation03_Unlimited_SkillBuff04"] = 70,
    ["Mayday_Occupation03_Unlimited_SkillBuff05"] = 70,
    ["Mayday_Occupation03_Unlimited_SkillBuff10"] = 70,
    ["Mayday_Occupation04"] = 71,
    ["Mayday_Occupation04Skill01"] = 71,
    ["Mayday_Occupation04Skill01_Info"] = 71,
    ["Mayday_Occupation04Skill01_InfoSpy"] = 71,
    ["Mayday_InGame_Item_InsectBottle"] = 71,
    ["Mayday_InGame_Item_InsectBottle_Info"] = 71,
    ["Mayday_InGame_Item_InsectBottle_BtnSmoke"] = 71,
    ["Mayday_InGame_Item_InsectBottle_BtnKill"] = 71,
    ["Mayday_Occupation04_Toast01"] = 71,
    ["Mayday_Occupation04_Toast02"] = 71,
    ["Mayday_Occupation04_Toast03"] = 71,
    ["Mayday_Occupation04_Unlimited_SkillBuff01"] = 71,
    ["Mayday_Occupation04_Unlimited_SkillBuff02"] = 71,
    ["Mayday_Occupation04_Unlimited_SkillBuff03"] = 71,
    ["Mayday_Occupation04_Unlimited_SkillBuff04"] = 71,
    ["Mayday_Occupation04_Unlimited_SkillBuff05"] = 71,
    ["Mayday_Occupation04_Unlimited_SkillBuff10"] = 71,
    ["Mayday_Occupation05"] = 71,
    ["Mayday_Occupation05Skill01"] = 71,
    ["Mayday_Occupation05Skill01_Info"] = 71,
    ["Mayday_Occupation05Skill01_Tips"] = 71,
    ["Mayday_Occupation05Skill01_Toast"] = 71,
    ["Mayday_Occupation05Skill02"] = 71,
    ["Mayday_Occupation05Skill02_Info"] = 71,
    ["Mayday_Occupation05Skill02_Tips"] = 71,
    ["Mayday_Occupation05Skill02_Toast"] = 71,
    ["Mayday_InGame_Item_Wanted"] = 71,
    ["Mayday_InGame_Item_Wanted_Info"] = 71,
    ["Mayday_Occupation05_BtnRefresh"] = 71,
    ["Mayday_Occupation05_NewTarget"] = 71,
    ["Mayday_Occupation05_BtnKill"] = 71,
    ["Mayday_Occupation05_Toast01"] = 71,
    ["Mayday_Occupation05_Toast02"] = 71,
    ["Mayday_Occupation05_Toast03"] = 71,
    ["Mayday_Occupation05_Toast04"] = 71,
    ["Mayday_Occupation05_Toast05"] = 71,
    ["Mayday_Occupation05_Toast06"] = 71,
    ["Mayday_Occupation05_Toast01Spy"] = 71,
    ["Mayday_Occupation05_Toast02Spy"] = 71,
    ["Mayday_Occupation05_Toast03Spy"] = 71,
    ["Mayday_Occupation05_Toast04Spy"] = 71,
    ["Mayday_Occupation05_Toast05Spy"] = 71,
    ["Mayday_Occupation05_Toast06Spy"] = 71,
    ["Mayday_Occupation05_Unlimited_SkillBuff01"] = 71,
    ["Mayday_Occupation05_Unlimited_SkillBuff02"] = 71,
    ["Mayday_Occupation05_Unlimited_SkillBuff03"] = 71,
    ["Mayday_Occupation05_Unlimited_SkillBuff04"] = 71,
    ["Mayday_Occupation05_Unlimited_SkillBuff05"] = 71,
    ["Mayday_Occupation05_Unlimited_SkillBuff09"] = 71,
    ["Mayday_Occupation05_Unlimited_SkillBuff10"] = 71,
    ["Mayday_Occupation06"] = 71,
    ["Mayday_Occupation06Skill01"] = 71,
    ["Mayday_Occupation06Skill01_Info"] = 71,
    ["Mayday_Occupation06Skill01_Btn"] = 71,
    ["Mayday_InGame_Item_Invisible"] = 71,
    ["Mayday_InGame_Item_Invisible_Info"] = 71,
    ["Mayday_Occupation06_BtnKill"] = 71,
    ["Mayday_Occupation06_Unlimited_SkillBuff01"] = 71,
    ["Mayday_Occupation06_Unlimited_SkillBuff02"] = 71,
    ["Mayday_Occupation06_Unlimited_SkillBuff03"] = 71,
    ["Mayday_Occupation06_Unlimited_SkillBuff04"] = 71,
    ["Mayday_Occupation06_Unlimited_SkillBuff05"] = 71,
    ["Mayday_Occupation06_Unlimited_SkillBuff10"] = 71,
    ["Mayday_Occupation07"] = 71,
    ["Mayday_Occupation07Skill01"] = 71,
    ["Mayday_Occupation07Skill01_Progress"] = 71,
    ["Mayday_Occupation07Skill01_Info"] = 71,
    ["Mayday_Occupation07Skill01_Toast01"] = 71,
    ["Mayday_Occupation07Skill01_Toast02"] = 71,
    ["Mayday_Occupation07Skill02"] = 71,
    ["Mayday_Occupation07Skill02_Info"] = 71,
    ["Mayday_Occupation07Skill02_New"] = 71,
    ["Mayday_Occupation07Skill02_Add"] = 71,
    ["Mayday_Occupation07Skill02_Del"] = 71,
    ["Mayday_Occupation07Skill02_Done"] = 71,
    ["Mayday_Occupation07Skill02_Toast01"] = 71,
    ["Mayday_Occupation07Skill02_Toast02"] = 71,
    ["Mayday_Occupation07Skill02_Toast03"] = 71,
    ["Mayday_Occupation07_BtnKill"] = 71,
    ["Mayday_Occupation07_Unlimited_SkillBuff01"] = 71,
    ["Mayday_Occupation07_Unlimited_SkillBuff02"] = 71,
    ["Mayday_Occupation07_Unlimited_SkillBuff03"] = 71,
    ["Mayday_Occupation07_Unlimited_SkillBuff04"] = 71,
    ["Mayday_Occupation07_Unlimited_SkillBuff05"] = 71,
    ["Mayday_Occupation07_Unlimited_SkillBuff10"] = 71,
    ["Mayday_Occupation08"] = 71,
    ["Mayday_Occupation08Skill01"] = 71,
    ["Mayday_Occupation08Skill01_Info"] = 71,
    ["Mayday_Occupation08Skill01_InfoSpy"] = 71,
    ["Mayday_Occupation08Skill01_BtnOn"] = 71,
    ["Mayday_Occupation08Skill01_BtnOff"] = 71,
    ["Mayday_Occupation08Skill01_PlayerBag"] = 71,
    ["Mayday_Occupation08_BtnKill"] = 71,
    ["Mayday_Occupation08_BtnSpyskill"] = 71,
    ["Mayday_Occupation08Skill01_Toast01"] = 71,
    ["Mayday_Occupation08Skill01_Toast02"] = 71,
    ["Mayday_Occupation08Skill01_Toast03"] = 71,
    ["Mayday_Occupation08_Unlimited_SkillBuff01"] = 71,
    ["Mayday_Occupation08_Unlimited_SkillBuff02"] = 71,
    ["Mayday_Occupation08_Unlimited_SkillBuff03"] = 71,
    ["Mayday_Occupation08_Unlimited_SkillBuff04"] = 72,
    ["Mayday_Occupation08_Unlimited_SkillBuff05"] = 72,
    ["Mayday_Occupation08_Unlimited_SkillBuff10"] = 72,
    ["Mayday_Occupation_Task"] = 72,
    ["Mayday_Occupation_Task_PersonalRewards"] = 72,
    ["Mayday_Occupation_Task_PersonalRewardsInfo"] = 72,
    ["Mayday_Occupation_Task_TeamRewards"] = 72,
    ["Mayday_Occupation01_Task01"] = 72,
    ["Mayday_Occupation01_Task02"] = 72,
    ["Mayday_Occupation01_Task03"] = 72,
    ["Mayday_Occupation01_Task04"] = 72,
    ["Mayday_Occupation01_Task05"] = 72,
    ["Mayday_Occupation02_Task01"] = 72,
    ["Mayday_Occupation02_Task02"] = 72,
    ["Mayday_Occupation02_Task03"] = 72,
    ["Mayday_Occupation02_Task04"] = 72,
    ["Mayday_OccupationAll_Task01"] = 72,
    ["Mayday_OccupationAll_Task02"] = 72,
    ["Mayday_OccupationAll_Task03"] = 72,
    ["Mayday_OccupationAll_Task04"] = 72,
    ["Mayday_OccupationAll_Task05"] = 72,
    ["Mayday_OccupationAll_Task06"] = 72,
    ["Mayday_OccupationAll_Task07"] = 72,
    ["Mayday_OccupationAll_Task08"] = 72,
    ["Mayday_Occupation_Full"] = 72,
    ["Mayday_Occupation_WaitOthers"] = 72,
    ["Mayday_Occupation_Ready"] = 72,
    ["Mayday_HUD_TotalValue"] = 72,
    ["Mayday_InGame_Confirmed"] = 72,
    ["Mayday_HUD_Config"] = 72,
    ["Mayday_HUD_PersonalConfig"] = 72,
    ["Mayday_HUD_Common"] = 72,
    ["Mayday_HUD_Sensitivity"] = 72,
    ["Mayday_HUD_SensitivityInfo"] = 72,
    ["Mayday_HUD_Rush"] = 72,
    ["Mayday_HUD_Rush_Switch01"] = 72,
    ["Mayday_HUD_Rush_Switch02"] = 72,
    ["Mayday_HUD_RushSensitivity"] = 72,
    ["Mayday_HUD_RushSensitivityInfo"] = 72,
    ["Mayday_HUD_Vision"] = 72,
    ["Mayday_HUD_Vision_1P"] = 72,
    ["Mayday_HUD_Vision_3P"] = 72,
    ["Mayday_HUD_Btn_Soul"] = 72,
    ["Mayday_HUD_System"] = 72,
    ["Mayday_HUD_System_Btn"] = 72,
    ["Mayday_HUD_System_Btn_Success"] = 72,
    ["Mayday_HUD_System_BtnCD_Toast"] = 72,
    ["Mayday_HUD_Unlimited_Exit_Text01"] = 72,
    ["Mayday_HUD_Unlimited_Exit_Text02"] = 72,
    ["Mayday_HUD_Unlimited_Exit_Text03"] = 72,
    ["Mayday_HUD_Unlimited_Exit_SaveBtn01"] = 72,
    ["Mayday_HUD_Unlimited_Exit_SaveBtn02"] = 72,
    ["Mayday_HUD_Unlimited_Exit_RestartBtn"] = 72,
    ["Mayday_HUD_Unlimited_Exit_Title"] = 72,
    ["Mayday_HUD_Unlimited_Exit_SaveText01"] = 72,
    ["Mayday_HUD_Unlimited_Exit_SaveText02"] = 72,
    ["Mayday_InGame_Wardrobe"] = 72,
    ["Mayday_InGame_Wardrobe_BtnOpen"] = 72,
    ["Mayday_InGame_Wardrobe_BtnDress"] = 72,
    ["Mayday_InGame_Wardrobe_BtnRevert"] = 72,
    ["Mayday_InGame_Btn_Teleport"] = 72,
    ["Mayday_InGame_Evaluation"] = 72,
    ["Mayday_InGame_Evaluation_All_P001"] = 72,
    ["Mayday_InGame_Evaluation_All_P002"] = 72,
    ["Mayday_InGame_Evaluation_All_P003"] = 72,
    ["Mayday_InGame_Evaluation_All_P004"] = 72,
    ["Mayday_InGame_Evaluation_All_P005"] = 72,
    ["Mayday_InGame_Evaluation_All_P006"] = 72,
    ["Mayday_InGame_Evaluation_All_I001"] = 72,
    ["Mayday_InGame_Evaluation_All_I002"] = 72,
    ["Mayday_InGame_Evaluation_All_I003"] = 72,
    ["Mayday_InGame_Evaluation_All_I004"] = 72,
    ["Mayday_InGame_Evaluation_All_I005"] = 72,
    ["Mayday_InGame_Evaluation_All_I006"] = 72,
    ["Mayday_InGame_Evaluation_All_N001"] = 72,
    ["Mayday_InGame_Evaluation_All_N002"] = 72,
    ["Mayday_InGame_Evaluation_All_N003"] = 72,
    ["Mayday_InGame_Evaluation_All_N004"] = 72,
    ["Mayday_InGame_Evaluation_All_N005"] = 72,
    ["Mayday_InGame_Evaluation_All_N006"] = 72,
    ["Mayday_InGame_Evaluation_All_N007"] = 72,
    ["Mayday_InGame_Evaluation_All_N008"] = 72,
    ["Mayday_InGame_Evaluation_All_N009"] = 72,
    ["Mayday_InGame_Evaluation_All_N010"] = 72,
    ["Mayday_InGame_Evaluation_All_N011"] = 72,
    ["Mayday_InGame_Evaluation_All_N012"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P001"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P002"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P003"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P004"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P005"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P006"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P007"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P008"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P009"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P010"] = 72,
    ["Mayday_InGame_Evaluation_Spy_P011"] = 72,
    ["Mayday_InGame_Evaluation_Spy_I001"] = 72,
    ["Mayday_InGame_Evaluation_Spy_I002"] = 72,
    ["Mayday_InGame_Evaluation_Spy_I003"] = 72,
    ["Mayday_InGame_Evaluation_Spy_I004"] = 73,
    ["Mayday_InGame_Evaluation_Spy_I005"] = 73,
    ["Mayday_InGame_Evaluation_Spy_I006"] = 73,
    ["Mayday_InGame_Evaluation_Spy_N001"] = 73,
    ["Mayday_InGame_Evaluation_Spy_N002"] = 73,
    ["Mayday_InGame_Evaluation_Spy_N003"] = 73,
    ["Mayday_InGame_Evaluation_Spy_N004"] = 73,
    ["Mayday_InGame_Evaluation_Spy_N005"] = 73,
    ["Mayday_InGame_Evaluation_Spy_N006"] = 73,
    ["Mayday_InGame_Body_ReasonBook"] = 73,
    ["Mayday_InGame_Body_ReasonList"] = 73,
    ["Mayday_InGame_Body_Reason01"] = 73,
    ["Mayday_InGame_Body_Reason01_Desc"] = 73,
    ["Mayday_InGame_Body_Reason02"] = 73,
    ["Mayday_InGame_Body_Reason02_Desc"] = 73,
    ["Mayday_InGame_Body_Reason03"] = 73,
    ["Mayday_InGame_Body_Reason03_Desc"] = 73,
    ["Mayday_InGame_Body_Reason04"] = 73,
    ["Mayday_InGame_Body_Reason04_Desc"] = 73,
    ["Mayday_InGame_Body_Reason05"] = 73,
    ["Mayday_InGame_Body_Reason05_Desc"] = 73,
    ["Mayday_InGame_Body_Reason06"] = 73,
    ["Mayday_InGame_Body_Reason06_Desc"] = 73,
    ["Mayday_InGame_Body_Reason07"] = 73,
    ["Mayday_InGame_Body_Reason07_Desc"] = 73,
    ["Mayday_InGame_Body_Reason08"] = 73,
    ["Mayday_InGame_Body_Reason08_Desc"] = 73,
    ["Mayday_InGame_Body_Reason09"] = 73,
    ["Mayday_InGame_Body_Reason09_Desc"] = 73,
    ["Mayday_InGame_Body_Reason10"] = 73,
    ["Mayday_InGame_Body_Reason10_Desc"] = 73,
    ["Mayday_InGame_Body_Action"] = 73,
    ["Mayday_InGame_Body_ActionOccup00"] = 73,
    ["Mayday_InGame_Body_ActionOccup01"] = 73,
    ["Mayday_InGame_Body_ActionOccup02"] = 73,
    ["Mayday_InGame_Body_ActionOccup03"] = 73,
    ["Mayday_InGame_Body_ActionOccup04"] = 73,
    ["Mayday_InGame_Body_ActionOccup05"] = 73,
    ["Mayday_InGame_Body_ActionOccup06"] = 73,
    ["Mayday_InGame_Body_ActionMonster01"] = 73,
    ["Mayday_InGame_Body_ActionMonster02"] = 73,
    ["Mayday_InGame_Body_ActionMonster03"] = 73,
    ["Mayday_InGame_Body_ActionMonster04"] = 73,
    ["Mayday_InGame_Body_ActionMonster05"] = 73,
    ["Mayday_InGame_Body_ActionMonster06"] = 73,
    ["Mayday_InGame_Body_ActionMonster07"] = 73,
    ["Mayday_InGame_Body_ActionMonster08"] = 73,
    ["Mayday_InGame_Body_ActionMonster09"] = 73,
    ["Mayday_InGame_Body_ActionMonster10"] = 73,
    ["Mayday_InGame_Body_ActionMonster11"] = 73,
    ["Mayday_InGame_Body_ActionMonster12"] = 73,
    ["Mayday_InGame_Body_ActionMonster13"] = 73,
    ["Mayday_InGame_Body_ActionTrap01"] = 73,
    ["Mayday_InGame_Body_ActionEnvir01"] = 73,
    ["Mayday_InGame_Body_ActionEnvir02"] = 73,
    ["Mayday_InGame_Body_ActionEnvir03"] = 73,
    ["Mayday_InGame_Body_ActionEnvir04"] = 73,
    ["Mayday_InGame_Body_ActionItem01"] = 73,
    ["Mayday_InGame_Body_ActionItem02"] = 73,
    ["Mayday_InGame_Body_ActionItem03"] = 73,
    ["Mayday_InGame_Body_ActionFall01"] = 73,
    ["Mayday_InGame_Book"] = 73,
    ["Mayday_InGame_Book_BodyInfo"] = 73,
    ["Mayday_InGame_Book_MyInfo"] = 73,
    ["Mayday_InGame_Book_Instruction"] = 73,
    ["Mayday_InGame_Book_New"] = 73,
    ["Mayday_InGame_Book_ThisTime"] = 73,
    ["Mayday_InGame_Book_OldInfo"] = 73,
    ["Mayday_InGame_Book_Finder"] = 73,
    ["Mayday_InGame_Book_Btn_ShowInfo"] = 73,
    ["Mayday_InGame_Book_Btn_OldInfo"] = 73,
    ["Mayday_InGame_Book_UnknownInfo"] = 73,
    ["Mayday_InGame_Book_PlayerShowInfo"] = 73,
    ["Mayday_InGame_Book_PlayerDead"] = 73,
    ["Mayday_InGame_Book_RecordInfo"] = 73,
    ["Mayday_InGame_BodyInfoReport_Tips"] = 73,
    ["Mayday_InGame_Ring"] = 73,
    ["Mayday_InGame_Ring_Btn"] = 73,
    ["Mayday_InGame_Ring_Tips"] = 73,
    ["Mayday_InGame_Ring_Tips2"] = 73,
    ["Mayday_InGame_Ring_Tips3"] = 73,
    ["Mayday_InGame_Ring_Toast"] = 73,
    ["Mayday_HUD_Champion_Title"] = 73,
    ["Mayday_HUD_Champion_Info"] = 73,
    ["Mayday_HUD_GameOver"] = 73,
    ["Mayday_InGame_Coffin"] = 73,
    ["Mayday_InGame_Coffin_Btn_Hide"] = 73,
    ["Mayday_InGame_Coffin_Btn_Leave"] = 73,
    ["Mayday_InGame_Paused"] = 73,
    ["Mayday_ModePedia_Title"] = 73,
    ["Mayday_ModePedia_Instruction"] = 73,
    ["Mayday_ModePedia_Bonus"] = 73,
    ["Mayday_ModePedia_FindNewGhost"] = 73,
    ["Mayday_ModePedia_GhostNumber"] = 73,
    ["Mayday_ModePedia_GhostName"] = 73,
    ["Mayday_ModePedia_RiskLevel"] = 73,
    ["Mayday_ModePedia_GetBonusTime"] = 73,
    ["Mayday_ModePedia_Location"] = 73,
    ["Mayday_ModePedia_GhostInfo"] = 73,
    ["Mayday_ModePedia_WillGet"] = 73,
    ["Mayday_ModeInstruction_Ghost1"] = 74,
    ["Mayday_ModeInstruction_Ghost1_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost1_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost1_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost2"] = 74,
    ["Mayday_ModeInstruction_Ghost2_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost2_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost2_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost3"] = 74,
    ["Mayday_ModeInstruction_Ghost3_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost3_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost3_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost4"] = 74,
    ["Mayday_ModeInstruction_Ghost4_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost4_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost4_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost5"] = 74,
    ["Mayday_ModeInstruction_Ghost5_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost5_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost5_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost6"] = 74,
    ["Mayday_ModeInstruction_Ghost6_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost6_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost6_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost7"] = 74,
    ["Mayday_ModeInstruction_Ghost7_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost7_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost7_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost8"] = 74,
    ["Mayday_ModeInstruction_Ghost8_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost8_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost8_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost9"] = 74,
    ["Mayday_ModeInstruction_Ghost9_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost9_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost9_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost10"] = 74,
    ["Mayday_ModeInstruction_Ghost10_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost10_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost10_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost11"] = 74,
    ["Mayday_ModeInstruction_Ghost11_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost11_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost11_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost12"] = 74,
    ["Mayday_ModeInstruction_Ghost12_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost12_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost12_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost13"] = 74,
    ["Mayday_ModeInstruction_Ghost13_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost13_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost13_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost14"] = 74,
    ["Mayday_ModeInstruction_Ghost14_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost14_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost14_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost15"] = 74,
    ["Mayday_ModeInstruction_Ghost15_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost15_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost15_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost16"] = 74,
    ["Mayday_ModeInstruction_Ghost16_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost16_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost16_Info3"] = 74,
    ["Mayday_ModeInstruction_Ghost17"] = 74,
    ["Mayday_ModeInstruction_Ghost17_Info"] = 74,
    ["Mayday_ModeInstruction_Ghost17_Info2"] = 74,
    ["Mayday_ModeInstruction_Ghost17_Info3"] = 74,
    ["Mayday_ModeInstruction_MainRule"] = 74,
    ["Mayday_ModeInstruction_MainRule_Text"] = 74,
    ["Mayday_ModeInstruction_PlayRule"] = 74,
    ["Mayday_ModeInstruction_StoreBuy"] = 74,
    ["Mayday_ModeInstruction_StoreBuy_Text"] = 74,
    ["Mayday_ModeInstruction_DriveShip"] = 74,
    ["Mayday_ModeInstruction_DriveShip_Text"] = 74,
    ["Mayday_ModeInstruction_GotItems"] = 74,
    ["Mayday_ModeInstruction_GotItems_Text"] = 74,
    ["Mayday_ModeInstruction_BackShip"] = 74,
    ["Mayday_ModeInstruction_BackShip_Text"] = 74,
    ["Mayday_ModeInstruction_ItemValue"] = 74,
    ["Mayday_ModeInstruction_ItemValue_Text"] = 74,
    ["Mayday_ModeInstruction_KingsPalace"] = 74,
    ["Mayday_ModeInstruction_KingsPalace_Text"] = 74,
    ["Mayday_ModeInstruction_HandIn"] = 74,
    ["Mayday_ModeInstruction_HandIn_Text"] = 74,
    ["Mayday_ModeInstruction_MoreRule"] = 74,
    ["Mayday_ModeInstruction_Level"] = 74,
    ["Mayday_ModeInstruction_Level_Text"] = 74,
    ["Mayday_ModeInstruction_Money"] = 74,
    ["Mayday_ModeInstruction_Money_Text"] = 74,
    ["Mayday_ModeInstruction_GotMoney"] = 74,
    ["Mayday_ModeInstruction_GotMoney_Text"] = 74,
    ["Mayday_ModeInstruction_PlayTime"] = 74,
    ["Mayday_ModeInstruction_PlayTime_Text"] = 74,
    ["Mayday_ModeInstruction_DeadPenalty"] = 74,
    ["Mayday_ModeInstruction_DeadPenalty_Text"] = 74,
    ["Mayday_ModeInstruction_Map"] = 74,
    ["Mayday_ModeInstruction_Room"] = 74,
    ["Mayday_ModeInstruction_Room_Text"] = 74,
    ["Mayday_ModeInstruction_RoomDoor"] = 74,
    ["Mayday_ModeInstruction_RoomDoor_Text"] = 75,
    ["Mayday_ModeInstruction_WildPlay"] = 75,
    ["Mayday_ModeInstruction_WildPlay_Text"] = 75,
    ["Mayday_ModeInstruction_WildDouble"] = 75,
    ["Mayday_ModeInstruction_WildDouble_Text"] = 75,
    ["Mayday_ModeInstruction_WildJump"] = 75,
    ["Mayday_ModeInstruction_WildJump_Text"] = 75,
    ["Mayday_ModeInstruction_GhostInfo"] = 75,
    ["Mayday_ModeInstruction_Career"] = 75,
    ["Mayday_ModeInstruction_Ship"] = 75,
    ["Mayday_ModeInstruction_ShipTakeOff"] = 75,
    ["Mayday_ModeInstruction_ShipTakeOff_Text"] = 75,
    ["Mayday_ModeInstruction_ShipLand"] = 75,
    ["Mayday_ModeInstruction_ShipLand_Text"] = 75,
    ["Mayday_ModeInstruction_Store"] = 75,
    ["Mayday_ModeInstruction_Store_Text"] = 75,
    ["Mayday_ModeInstruction_TeleportBack"] = 75,
    ["Mayday_ModeInstruction_TeleportBack_Text"] = 75,
    ["Mayday_ModeInstruction_TeleportOut"] = 75,
    ["Mayday_ModeInstruction_TeleportOut_Text"] = 75,
    ["Mayday_ModeInstruction_DoorControl"] = 75,
    ["Mayday_ModeInstruction_DoorControl_Text"] = 75,
    ["Mayday_ModeInstruction_Deck"] = 75,
    ["Mayday_ModeInstruction_Deck_Text"] = 75,
    ["Mayday_ModeInstruction_Wardrobe"] = 75,
    ["Mayday_ModeInstruction_Wardrobe_Text"] = 75,
    ["Mayday_ModeInstruction_MainRule_Text2"] = 75,
    ["Mayday_ModeInstruction_StarRule"] = 75,
    ["Mayday_ModeInstruction_WinCondition"] = 75,
    ["Mayday_ModeInstruction_WinCondition_Text"] = 75,
    ["Mayday_ModeInstruction_GotItems_Text2"] = 75,
    ["Mayday_ModeInstruction_HandIn_Text2"] = 75,
    ["Mayday_ModeInstruction_SpyDeath"] = 75,
    ["Mayday_ModeInstruction_SpyDeath_Text"] = 75,
    ["Mayday_ModeInstruction_SpyRule"] = 75,
    ["Mayday_ModeInstruction_WinCondition_Text2"] = 75,
    ["Mayday_ModeInstruction_SpyDisturb"] = 75,
    ["Mayday_ModeInstruction_SpyDisturb_Text"] = 75,
    ["Mayday_ModeInstruction_SpyLieLow"] = 75,
    ["Mayday_ModeInstruction_SpyLieLow_Text"] = 75,
    ["Mayday_ModeInstruction_Clue"] = 75,
    ["Mayday_ModeInstruction_AboutClue"] = 75,
    ["Mayday_ModeInstruction_AboutClue_Text"] = 75,
    ["Mayday_ModeInstruction_GotClue"] = 75,
    ["Mayday_ModeInstruction_GotClue_Text"] = 75,
    ["Mayday_ModeInstruction_ShowClue"] = 75,
    ["Mayday_ModeInstruction_ShowClue_Text"] = 75,
    ["Mayday_ModeInstruction_Discussion"] = 75,
    ["Mayday_ModeInstruction_FieldReport"] = 75,
    ["Mayday_ModeInstruction_FieldReport_Text"] = 75,
    ["Mayday_ModeInstruction_RingReport"] = 75,
    ["Mayday_ModeInstruction_RingReport_Text"] = 75,
    ["Mayday_ModeInstruction_ShowClue2"] = 75,
    ["Mayday_ModeInstruction_ShowClue2_Text"] = 75,
    ["Mayday_ModeInstruction_VoteRule"] = 75,
    ["Mayday_ModeInstruction_VoteRule_Text"] = 75,
    ["Mayday_ModeInstruction_ItemBox"] = 75,
    ["Mayday_ModeInstruction_ItemBox_Text"] = 75,
    ["Mayday_ModeInstruction_HandheldMap"] = 75,
    ["Mayday_ModeInstruction_HandheldMap_Text"] = 75,
    ["Mayday_ModeInstruction_UnlimitedLevel"] = 75,
    ["Mayday_ModeInstruction_UnlimitedLevel_Text"] = 75,
    ["Mayday_ModeInstruction_FirstMoney"] = 75,
    ["Mayday_ModeInstruction_FirstMoney_Text"] = 75,
    ["Mayday_ModeInstruction_LevelSelect"] = 75,
    ["Mayday_ModeInstruction_LevelSelect_Text"] = 75,
    ["Mayday_ModeInstruction_Keyword"] = 75,
    ["Mayday_ModeInstruction_Keyword_Text"] = 75,
    ["Mayday_ModeInstruction_OccupationLvUp"] = 75,
    ["Mayday_ModeInstruction_OccupationLvUp_Text"] = 75,
    ["Mayday_ModeGuide_Title"] = 75,
    ["Mayday_ModeGuide_GameStart"] = 75,
    ["Mayday_ModeGuide_GameStart_Text"] = 75,
    ["Mayday_ModeGuide_MissionInfo"] = 75,
    ["Mayday_ModeGuide_MissionInfo_Text"] = 75,
    ["Mayday_ModeGuide_Depart"] = 75,
    ["Mayday_ModeGuide_Depart_Text"] = 75,
    ["Mayday_ModeGuide_FindRoom"] = 75,
    ["Mayday_ModeGuide_FindRoom_Text"] = 75,
    ["Mayday_ModeGuide_Rainforest"] = 75,
    ["Mayday_ModeGuide_Rainforest_Text"] = 75,
    ["Mayday_ModeGuide_GotItems"] = 75,
    ["Mayday_ModeGuide_GotItems_Text"] = 75,
    ["Mayday_ModeGuide_Light"] = 75,
    ["Mayday_ModeGuide_Light_Text"] = 75,
    ["Mayday_ModeGuide_Battle"] = 75,
    ["Mayday_ModeGuide_Battle_Text"] = 75,
    ["Mayday_ModeGuide_HandIn"] = 75,
    ["Mayday_ModeGuide_HandIn_Text"] = 75,
    ["Mayday_ModeGuide_HandIn_Text2"] = 75,
    ["Mayday_ModeGuide_StarWin"] = 75,
    ["Mayday_ModeGuide_StarWin_Text"] = 75,
    ["Mayday_ModeGuide_FindSpy"] = 75,
    ["Mayday_ModeGuide_FindSpy_Text"] = 75,
    ["Mayday_ModeGuide_SpyWin"] = 75,
    ["Mayday_ModeGuide_SpyWin_Text"] = 75,
    ["Mayday_ModeGuide_SpyLieLow"] = 75,
    ["Mayday_ModeGuide_SpyLieLow_Text"] = 75,
    ["Mayday_ModeGuide_GotItems_Text2"] = 75,
    ["Mayday_ModeGuide_AttentionSpy"] = 75,
    ["Mayday_ModeGuide_AttentionSpy_Text"] = 76,
    ["Mayday_ModeGuide_SpyMission"] = 76,
    ["Mayday_ModeGuide_SpyMission_Text"] = 76,
    ["Mayday_ModeGuide_SpySkill"] = 76,
    ["Mayday_ModeGuide_SpySkill_Text"] = 76,
    ["Mayday_ModeMission1_Text"] = 76,
    ["Mayday_ModeMission2_Text"] = 76,
    ["Mayday_ModeMission3_Text"] = 76,
    ["Mayday_ModeMission4_Text"] = 76,
    ["Mayday_ModeMission5_Text"] = 76,
    ["Mayday_ModeMission6_Text"] = 76,
    ["Mayday_ModeMission7_Text"] = 76,
    ["Mayday_ModeMission8_Text"] = 76,
    ["Mayday_ModeMission9_Text"] = 76,
    ["Mayday_ModeMission10_Text"] = 76,
    ["Mayday_ModeMission11_Text"] = 76,
    ["Mayday_ModeMission12_Text"] = 76,
    ["Mayday_ModeMission13_Text"] = 76,
    ["Mayday_ModeMission14_Text"] = 76,
    ["Mayday_ModeMission15_Text"] = 76,
    ["Mayday_Unlimited"] = 76,
    ["Mayday_Unlimited_Go_Tips1"] = 76,
    ["Mayday_Unlimited_Go_Tips2"] = 76,
    ["Mayday_Unlimited_Go_Tips3"] = 76,
    ["Mayday_Unlimited_Go_LocationChange"] = 76,
    ["Mayday_Unlimited_JobReselect"] = 76,
    ["Mayday_Unlimited_Rank"] = 76,
    ["Mayday_Unlimited_TeamMoney"] = 76,
    ["Mayday_Unlimited_SkillPointUsed"] = 76,
    ["Mayday_Unlimited_Position"] = 76,
    ["Mayday_Unlimited_SkillPointRemain"] = 76,
    ["Mayday_Unlimited_JobLevel"] = 76,
    ["Mayday_Unlimited_Map01"] = 76,
    ["Mayday_Unlimited_Map02"] = 76,
    ["Mayday_Unlimited_Map03"] = 76,
    ["Mayday_Unlimited_DangerRank"] = 76,
    ["Mayday_Unlimited_DailyValue"] = 76,
    ["Mayday_Unlimited_TransPay"] = 76,
    ["Mayday_Unlimited_Short"] = 76,
    ["Mayday_Unlimited_Level1"] = 76,
    ["Mayday_Unlimited_Level2"] = 76,
    ["Mayday_Unlimited_Level3"] = 76,
    ["Mayday_Unlimited_TeamRecord"] = 76,
    ["Mayday_Unlimited_TeamRecord_Level"] = 76,
    ["Mayday_Unlimited_NewGoal"] = 76,
    ["Mayday_Unlimited_NewGoal2"] = 76,
    ["Mayday_Unlimited_LimitTime"] = 76,
    ["Mayday_Unlimited_NewRecord"] = 76,
    ["Mayday_Unlimited_RewardTeamMoney1"] = 76,
    ["Mayday_Unlimited_RewardTeamMoney2"] = 76,
    ["Mayday_Unlimited_UseTime"] = 76,
    ["Mayday_Unlimited_SkillBook"] = 76,
    ["Mayday_Unlimited_SkillBook_Info"] = 76,
    ["Mayday_Unlimited_SkillBook_Tips"] = 76,
    ["Mayday_Unlimited_SkillPointHave"] = 76,
    ["Mayday_Unlimited_SkillPointNeed"] = 76,
    ["Mayday_Unlimited_SkillLevelUp_Btn"] = 76,
    ["Mayday_Unlimited_SkillLevelUp_Toast01"] = 76,
    ["Mayday_Unlimited_SkillLevelUp_Toast02"] = 76,
    ["Mayday_Unlimited_SkillLevelUp_Toast03"] = 76,
    ["Mayday_Unlimited_SkillLevel_Max"] = 76,
    ["Mayday_Unlimited_SkillLevel_Max2"] = 76,
    ["Mayday_Unlimited_SkillLevel_Condition"] = 76,
    ["Mayday_Unlimited_LevelSelect"] = 76,
    ["Mayday_Unlimited_LocationLevel"] = 76,
    ["Mayday_Unlimited_TeleportConfirm01"] = 76,
    ["Mayday_Unlimited_TeleportConfirm02"] = 76,
    ["Mayday_Unlimited_SelectLocationLevel"] = 76,
    ["Mayday_Unlimited_Occupation_Title"] = 76,
    ["Mayday_Unlimited_Goal"] = 76,
    ["Mayday_Unlimited_Level"] = 76,
    ["Mayday_Unlimited_Recommend"] = 76,
    ["Mayday_Unlimited_Keyword"] = 76,
    ["Mayday_Unlimited_Keyword_ComingSoon"] = 76,
    ["Mayday_Unlimited_Keyword1_Title"] = 76,
    ["Mayday_Unlimited_Keyword1_Text"] = 76,
    ["Mayday_Unlimited_Keyword2_Title"] = 76,
    ["Mayday_Unlimited_Keyword2_Text"] = 76,
    ["Mayday_Unlimited_Keyword3_Title"] = 76,
    ["Mayday_Unlimited_Keyword3_Text"] = 76,
    ["Mayday_Unlimited_Keyword4_Title"] = 76,
    ["Mayday_Unlimited_Keyword4_Text"] = 76,
    ["Mayday_Unlimited_Keyword5_Title"] = 76,
    ["Mayday_Unlimited_Keyword5_Text"] = 76,
    ["Mayday_Unlimited_Keyword6_Title"] = 76,
    ["Mayday_Unlimited_Keyword6_Text"] = 76,
    ["Mayday_Unlimited_Keyword7_Title"] = 76,
    ["Mayday_Unlimited_Keyword7_Text"] = 76,
    ["Mayday_Unlimited_Keyword8_Title"] = 76,
    ["Mayday_Unlimited_Keyword8_Text"] = 76,
    ["Mayday_Unlimited_Keyword9_Title"] = 76,
    ["Mayday_Unlimited_Keyword9_Text"] = 76,
    ["Mayday_Unlimited_Keyword10_Title"] = 76,
    ["Mayday_Unlimited_Keyword10_Text"] = 76,
    ["Mayday_Unlimited_Keyword11_Title"] = 76,
    ["Mayday_Unlimited_Keyword11_Text"] = 76,
    ["Mayday_Unlimited_Keyword12_Title"] = 76,
    ["Mayday_Unlimited_Keyword12_Text"] = 76,
    ["Mayday_Unlimited_Keyword13_Title"] = 76,
    ["Mayday_Unlimited_Keyword13_Text"] = 76,
    ["Mayday_Unlimited_Keyword14_Title"] = 77,
    ["Mayday_Unlimited_Keyword14_Text"] = 77,
    ["Mayday_Unlimited_Keyword15_Title"] = 77,
    ["Mayday_Unlimited_Keyword15_Text"] = 77,
    ["Mayday_Unlimited_Keyword16_Title"] = 77,
    ["Mayday_Unlimited_Keyword16_Text"] = 77,
    ["Mayday_Unlimited_Keyword17_Title"] = 77,
    ["Mayday_Unlimited_Keyword17_Text"] = 77,
    ["Mayday_Unlimited_Keyword18_Title"] = 77,
    ["Mayday_Unlimited_Keyword18_Text"] = 77,
    ["Mayday_Unlimited_Keyword19_Title"] = 77,
    ["Mayday_Unlimited_Keyword19_Text"] = 77,
    ["Mayday_Unlimited_Keyword20_Title"] = 77,
    ["Mayday_Unlimited_Keyword20_Text"] = 77,
    ["Mayday_Unlimited_Keyword21_Title"] = 77,
    ["Mayday_Unlimited_Keyword21_Text"] = 77,
    ["Mayday_Unlimited_Keyword22_Title"] = 77,
    ["Mayday_Unlimited_Keyword22_Text"] = 77,
    ["Mayday_Unlimited_Keyword23_Title"] = 77,
    ["Mayday_Unlimited_Keyword23_Text"] = 77,
    ["Mayday_Unlimited_Keyword24_Title"] = 77,
    ["Mayday_Unlimited_Keyword24_Text"] = 77,
    ["Mayday_Unlimited_Keyword25_Title"] = 77,
    ["Mayday_Unlimited_Keyword25_Text"] = 77,
    ["Mayday_Unlimited_Keyword26_Title"] = 77,
    ["Mayday_Unlimited_Keyword26_Text"] = 77,
    ["Mayday_Unlimited_Keyword27_Title"] = 77,
    ["Mayday_Unlimited_Keyword27_Text"] = 77,
    ["Mayday_Unlimited_Keyword28_Title"] = 77,
    ["Mayday_Unlimited_Keyword28_Text"] = 77,
    ["Mayday_Unlimited_Keyword29_Title"] = 77,
    ["Mayday_Unlimited_Keyword29_Text"] = 77,
    ["Mayday_Unlimited_Keyword30_Title"] = 77,
    ["Mayday_Unlimited_Keyword30_Text"] = 77,
    ["Mayday_Unlimited_Keyword31_Title"] = 77,
    ["Mayday_Unlimited_Keyword31_Text"] = 77,
    ["Mayday_Unlimited_Keyword32_Title"] = 77,
    ["Mayday_Unlimited_Keyword32_Text"] = 77,
    ["Mayday_InGame_Speech"] = 77,
    ["Mayday_InGame_CannotUseEmoji_Toast"] = 77,
    ["Text_First"] = 78,
    ["DS_NoMatch"] = 78,
    ["Common_Confirm"] = 78,
    ["Common_Confirm2"] = 78,
    ["Common_Confirm3"] = 78,
    ["Common_Cancel"] = 78,
    ["Common_Prompt"] = 78,
    ["Common_Error"] = 78,
    ["Common_Success"] = 78,
    ["Common_Fail"] = 78,
    ["Common_Day"] = 78,
    ["Common_ShortHour"] = 78,
    ["Common_Hour"] = 78,
    ["Common_Min"] = 78,
    ["Common_Sec"] = 78,
    ["Common_QualityName_1"] = 78,
    ["Common_QualityName_2"] = 78,
    ["Common_QualityName_3"] = 78,
    ["Common_QualityName_4"] = 78,
    ["Common_QualityName_5"] = 78,
    ["Common_Accept"] = 78,
    ["Common_Reject"] = 78,
    ["Common_Forever"] = 78,
    ["Common_Yes"] = 78,
    ["Common_No"] = 78,
    ["Common_Save"] = 78,
    ["Common_NoSave"] = 78,
    ["Common_Return"] = 78,
    ["Common_Giveup"] = 78,
    ["Common_Close"] = 78,
    ["Common_Reconnect"] = 78,
    ["Common_PleaseInputNum"] = 78,
    ["Common_LimitCount"] = 78,
    ["Common_ReplaceRewardText"] = 78,
    ["Common_GotNewSkin"] = 78,
    ["Common_GotNewOrnament"] = 78,
    ["Common_ItemUse"] = 78,
    ["Common_ItemDel"] = 78,
    ["Common_ItemSell"] = 78,
    ["Common_Item"] = 78,
    ["Common_Expired"] = 78,
    ["Common_Discount"] = 78,
    ["Login_CreateRoleFail"] = 78,
    ["Login_NeedSelectAccount"] = 78,
    ["Lobby_EnterLobby"] = 78,
    ["Lobby_ReTryTimes"] = 78,
    ["TimeUtils_H_1"] = 78,
    ["TimeUtils_D_1"] = 78,
    ["TimeUtils_DH_1"] = 78,
    ["TimeUtils_TimeLength2_1"] = 78,
    ["TimeUtils_M"] = 78,
    ["TimeUtils_M_1"] = 78,
    ["TimeUtils_M_Const_1"] = 78,
    ["TimeUtils_MD"] = 78,
    ["TimeUtils_YMD_1"] = 78,
    ["TimeUtils_MD_HM_1"] = 78,
    ["TimeUtils_D_2"] = 78,
    ["TimeUtils_Month_2"] = 78,
    ["TimeUtils_Y_2"] = 78,
    ["TimeUtils_H_2"] = 78,
    ["TimeUtils_Min_2"] = 78,
    ["TimeUtils_S_2"] = 78,
    ["TimeUtils_Today"] = 78,
    ["TimeUtils_DH_3"] = 78,
    ["TimeUtils_DM_3"] = 78,
    ["TimeUtils_MS_3"] = 78,
    ["TimeUtils_S_3"] = 78,
    ["TimeUtils_DHMS_3"] = 78,
    ["TimeUtils_HMS_3"] = 78,
    ["TimeUtils_HM_3"] = 78,
    ["TimeUtils_DHM_3"] = 78,
    ["TimeUtils_D_TimeLangth3_1"] = 78,
    ["TimeUtils_D_TimeLangth2_1"] = 78,
    ["TimeUtils_H_4"] = 78,
    ["TimeUtils_H_Const_1"] = 78,
    ["TimeUtils_M_4"] = 78,
    ["TimeUtils_D"] = 78,
    ["TimeUtils_Monday"] = 78,
    ["TimeUtils_Tuesday"] = 78,
    ["TimeUtils_Wednesday"] = 78,
    ["TimeUtils_Thursday"] = 78,
    ["TimeUtils_Friday"] = 78,
    ["TimeUtils_Saturday"] = 78,
    ["TimeUtils_Sunday"] = 78,
    ["MoveTextConfig_Tips"] = 78,
    ["UI_Login_VersionDesc"] = 78,
    ["Common_LoginTimeout"] = 78,
    ["Common_LoginQRCodeWX"] = 78,
    ["Common_LoginQRCodeQQ"] = 78,
    ["UI_Login_CheckRuleTip"] = 78,
    ["UI_Login_LogoutDesc"] = 78,
    ["Common_LogoutFail"] = 78,
    ["UI_Login_LoginStatusErrorBackToLogin"] = 78,
    ["Common_LoginSuccess"] = 78,
    ["Common_YouNotInvited"] = 78,
    ["Common_LoginFail"] = 78,
    ["Common_CancelWXLogin"] = 78,
    ["Common_CancelQQLogin"] = 78,
    ["Common_LoginErrorTryLoginAgain"] = 78,
    ["Common_GuestLoginDesc"] = 78,
    ["UI_Login_ServerIsNull"] = 79,
    ["Guide_CreateRole_ShowText1"] = 79,
    ["Guide_CreateRole_ShowText2"] = 79,
    ["Guide_CreateRoleFinish_ShowText1"] = 79,
    ["Guide_CreateRoleFinish_ShowText2"] = 79,
    ["Guide_CreateRoleFinish_ShowText3"] = 79,
    ["Guide_CreateRole_Fail"] = 79,
    ["Guide_CreateRole_Fail_891"] = 79,
    ["Guide_CreateRole_NetFail"] = 79,
    ["Team_CurrentNoWifi"] = 79,
    ["Team_Ds_LoadFail"] = 79,
    ["Team_WaitConfirm"] = 79,
    ["Team_CanNotStartMatch"] = 79,
    ["Net_ServerConnectFail"] = 79,
    ["Net_ReTryConnect"] = 79,
    ["Net_ReturnLogin"] = 79,
    ["Net_ServerConnectFailDS"] = 79,
    ["Net_WaitConnectDS"] = 79,
    ["Net_ReturnLoginDS"] = 79,
    ["Net_ReturnBattleFail"] = 79,
    ["Net_IsNeedReConnect"] = 79,
    ["Net_PlazaReconnectTimesOut"] = 79,
    ["Net_GiveUpBattleFail"] = 79,
    ["Net_DSNotMatchClient"] = 79,
    ["Guide_GuideConfigError"] = 79,
    ["Login_LoginFail"] = 79,
    ["Net_NetError"] = 79,
    ["Net_ServerError"] = 79,
    ["Net_KickPlayer_Banned"] = 79,
    ["Login_AuthFail"] = 79,
    ["Login_CheckName"] = 79,
    ["Login_SetName"] = 79,
    ["Login_SetGender"] = 79,
    ["Login_NetException"] = 79,
    ["Login_NoNameCanUse"] = 79,
    ["Net_DS_Error"] = 79,
    ["Login_TokenExpired"] = 79,
    ["Net_Proto_Error"] = 79,
    ["Net_Server_Error"] = 79,
    ["Login_PlayerInfoNoInit"] = 79,
    ["Task_Unopened"] = 79,
    ["Common_Null"] = 79,
    ["UGC_String_E"] = 79,
    ["UGC_String_W"] = 79,
    ["UGC_Map_IsCompleteAll"] = 79,
    ["UGC_Map_NoTimes"] = 79,
    ["UGC_Map_isMatching"] = 79,
    ["UGC_Map_NoMapData"] = 79,
    ["UGC_Map_EnterMapFail"] = 79,
    ["UGC_Map_ReqResultFail"] = 79,
    ["UGC_Map_MapDelete"] = 79,
    ["UGC_Map_NoHpConfig"] = 79,
    ["UGC_Map_GetMapDataFail"] = 79,
    ["UGC_Map_EnterMapFail_WithParam"] = 79,
    ["UGC_Map_ChangeMapFail"] = 79,
    ["Device_Not_Support"] = 79,
    ["Pak_InitPufferModule"] = 79,
    ["Pak_PauseAllDownloads"] = 79,
    ["Pak_NoDownloadingCurrently"] = 79,
    ["Pak_WaitForMapResDownload"] = 79,
    ["Pak_WiFiDownloadALL"] = 79,
    ["Pak_MobileNetDownloadALL"] = 79,
    ["Pak_FinishDownloadToGetReward"] = 79,
    ["Pak_FinishDownloadAndLoginToGetReward"] = 79,
    ["Pak_WiFiDownloadHomeRes"] = 79,
    ["Pak_MobileNetDownloadHomeRes"] = 79,
    ["Pak_WiFiDownloadUGCMapRes"] = 79,
    ["Pak_MobileNetDownloadUGCMapRes"] = 79,
    ["Pak_DownloadConnectorMapRes"] = 79,
    ["Pak_PufferDownloadError"] = 79,
    ["Pak_MountPakFail"] = 79,
    ["Pak_DownloadAfterLogin"] = 79,
    ["Pak_InitPufferAfterReconnect"] = 79,
    ["Pak_ConfirmDeleteFiles"] = 79,
    ["Pak_CanntDeleteReferedPak"] = 79,
    ["Pak_WiFiDownloadExpansionPacks"] = 79,
    ["Pak_MobileNetDownloadExpansionPacks"] = 79,
    ["Pak_MoreOnThatLater"] = 79,
    ["Pak_GoNow"] = 79,
    ["Pak_Tip"] = 79,
    ["Pak_UpdateTips"] = 79,
    ["CutGitft_Discount"] = 79,
    ["CutGitft_MoneyYuan"] = 79,
    ["CutGitft_TimeOver"] = 79,
    ["CutGitft_BuyCountOver"] = 79,
    ["CutGitft_BuyConfirm"] = 79,
    ["Notice_NoticeTitle"] = 79,
    ["Common_ClickFast"] = 79,
    ["SUIT_STORY_APPEND"] = 80,
    ["SUIT_STORY_1"] = 80,
    ["Bag_SuitTips"] = 81,
    ["Bag_SeasonTips"] = 81,
    ["SettingTip_SpeedSensitivity"] = 81,
    ["SettingTip_RockerSensitivity"] = 81,
    ["SettingTip_AirInertia"] = 81,
    ["SettingTip_VehicleControlType"] = 81,
    ["CustomRoom_CannotJoinRoomInTeam"] = 81,
    ["TaskStar_Stage1"] = 81,
    ["TaskStar_Stage2"] = 81,
    ["TaskStar_Stage3"] = 81,
    ["TaskStar_Stage4"] = 81,
    ["TaskStar_NoFinishCurrStage"] = 81,
    ["TaskStar_NextStageNoOpen"] = 81,
    ["TaskStar_TimeDesc"] = 81,
    ["UI_RecruitItem_Level"] = 81,
    ["Friend_BanNotice"] = 81,
    ["PlayerInfo_DefaultHead"] = 81,
    ["PlayerInfo_DefaultHeadBG"] = 81,
    ["PlayerInfo_DefaultNicknameBG"] = 81,
    ["PlayerInfo_DefaultTitle"] = 81,
    ["Task_WeekTask"] = 81,
    ["Task_CommonRewardDesc"] = 81,
    ["PermissionHint_Voice"] = 81,
    ["PermissionHint_Location"] = 81,
    ["PermissionHint_Storage"] = 81,
    ["Common_ReceiveReward"] = 81,
    ["ModelSelect_PlayModelReward"] = 81,
    ["ModelSelect_PlayModelRewardDesc"] = 81,
    ["ModelSelect_SeasonRewardOneParam"] = 81,
    ["Common_Go"] = 81,
    ["Season_CurrSeasonIsClose"] = 81,
    ["Version_LowestVersion"] = 81,
    ["Version_HighestVersion"] = 81,
    ["Version_ExcludeVersions"] = 81,
    ["LevelFinal_RecommendPlayers"] = 81,
    ["LevelFinal_TeamPlayers"] = 81,
    ["UI_Unable_Go"] = 81,
    ["Team_InUGCTeam"] = 81,
    ["Common_ActivityIsClose"] = 81,
    ["QQQuickTeam_Fail"] = 81,
    ["Task_ActivityResourceUpdate_TryAgainLater"] = 81,
    ["Friend_IsBanState"] = 81,
    ["Team_JoinFromShare_InTeam"] = 81,
    ["Team_JoinFromShare_InRoom"] = 81,
    ["Team_JoinFromShare_InUGCEditor"] = 81,
    ["Team_JoinFromShare_InHome"] = 81,
    ["Common_ItemTagDefaultName"] = 81,
    ["Common_Empower_Tips"] = 81,
    ["Common_Confirm_Empower"] = 81,
    ["Common_Cancel_Empower"] = 81,
    ["LevelFinal_RecommendPlayersRea"] = 81,
    ["LevelFinal_TeamPlayersRea"] = 81,
    ["TaskParty_Hint"] = 81,
    ["CDK_ReceiveSuccessfully"] = 81,
    ["Team_MatchServicesBusy"] = 81,
    ["NewChat_SendMessageNeedLevel"] = 81,
    ["Team_CanNotAcceptStartMatch"] = 81,
    ["NewChat_VoiceReportSucceed"] = 81,
    ["NewChat_VoiceReportNoMessage"] = 81,
    ["NewChat_VoiceReportCD"] = 81,
    ["NewChat_VoiceReportFailed"] = 81,
    ["NewChat_VoiceReportNotOpen"] = 81,
    ["Team_TeamAutoMatch"] = 81,
    ["PlayerInfo_DefaultChatBubble"] = 81,
    ["Player_ChatBubble"] = 81,
    ["UI_Recharge_OpeningRebate_Title"] = 81,
    ["Login_TokenExpired_PleaseLoginAgain"] = 81,
    ["UI_Recharge_OpeningRebate_ActivityTipFormat"] = 81,
    ["Activity_Convened_RecallOldFriend"] = 81,
    ["Activity_Convened_InviteNewPlayer"] = 81,
    ["Leaderboard_Tips_Title_StarWorld"] = 81,
    ["Leaderboard_Tips_Desc_StarWorld"] = 81,
    ["UI_Common_PopRewardView_GoToDraw"] = 81,
    ["UI_Common_PopRewardView_GoToSend"] = 81,
    ["UI_Common_PopRewardView_GoToThank"] = 81,
    ["SpringRedPacket_Got"] = 81,
    ["SpringRedPacket_Sent"] = 81,
    ["friend_RecentPlay"] = 81,
    ["friend_Union"] = 81,
    ["QRCode_Room"] = 81,
    ["QRCode_Club"] = 81,
    ["QRCode_Friend"] = 81,
    ["QRCode_Team"] = 81,
    ["UI_RankCompleteLevel"] = 81,
    ["NewChat_TeamInLevel"] = 81,
    ["Activity_Levelup"] = 81,
    ["Club_ChatTab"] = 81,
    ["Club_EnterClub"] = 81,
    ["Club_ExitClub"] = 81,
    ["Leaderboard_Tips_Title_StarCreate"] = 81,
    ["Leaderboard_Tips_Desc_StarCreate"] = 81,
    ["Activity_Timer"] = 81,
    ["LuckyBallDraw"] = 81,
    ["LuckyBallTitle"] = 81,
    ["LuckyBallBlessTitle"] = 81,
    ["LuckyBallGetCoin"] = 81,
    ["LuckyBallBlessNum"] = 81,
    ["RewardNormal"] = 81,
    ["LuckyBallGetAll"] = 81,
    ["LuckyBall_CoinNotEnough"] = 81,
    ["LuckBall_RewardToast"] = 82,
    ["Spectator_Normal"] = 82,
    ["Spectator_Side"] = 82,
    ["Spectator_Top"] = 82,
    ["Spectator_Front"] = 82,
    ["Spectator_Free"] = 82,
    ["Activity_ItemNum_Small"] = 82,
    ["Activity_ItemNum_Middle"] = 82,
    ["Activity_ItemNum_Higher"] = 82,
    ["UI_Recharge_OpeningRebateMakeUpTip"] = 82,
    ["Activity_InviteSuccessCount"] = 82,
    ["UI_RankTag_QualityRule"] = 82,
    ["UI_RankTag_QualityDesc"] = 82,
    ["UI_RankTag_FashionRule"] = 82,
    ["UI_RankTag_FashionDesc"] = 82,
    ["UI_RankTag_LevelMpaRule"] = 82,
    ["UI_RankTag_LevelMpaDesc"] = 82,
    ["UI_YM_HighRecordNum"] = 82,
    ["UI_YM_HighRecordName"] = 82,
    ["TaskStar_PreTaskUnLock"] = 82,
    ["LuckyBall_UnKnowBless"] = 82,
    ["UI_WolfKillNotMatch"] = 82,
    ["Mail_UGC"] = 82,
    ["BoxDelivery_Push_Title"] = 82,
    ["BoxDelivery_Push_Content"] = 82,
    ["Bag_SeasonFashionTips"] = 82,
    ["Bag_HistoryAllFashionTips"] = 82,
    ["UI_VoiceSetting_MagicVoice"] = 82,
    ["MicroMagicVoiceCell_Normal"] = 82,
    ["Text_Stealth_Start_Notice"] = 82,
    ["Text_Stealth_Close_Notice"] = 82,
    ["Text_Stealth_Week_Times"] = 82,
    ["BP_WeekTaskTargetSecond"] = 82,
    ["Common_ScreenShotTip"] = 82,
    ["NewChat_OtherSharePosition_Normal"] = 82,
    ["NewChat_OtherSharePosition_Home"] = 82,
    ["NewChat_SelfSharePosition_Home"] = 82,
    ["UI_PublicWelfare_LockStatus"] = 82,
    ["UI_PublicWelfare_TenThousand"] = 82,
    ["UI_PublicWelfare_XHH_WX"] = 82,
    ["UI_PublicWelfare_Medal_WX"] = 82,
    ["UI_PublicWelfare_XHH_QQ"] = 82,
    ["UI_PublicWelfare_Medal_QQ"] = 82,
    ["UI_PublicWelfare_Lock_Headline"] = 82,
    ["UI_PublicWelfare_Lock_Description"] = 82,
    ["AssistBless_AssistedText"] = 82,
    ["Assisted_Failure"] = 82,
    ["AssistBless_NoFriendSlotsAvailable"] = 82,
    ["AssistBless_TipContent"] = 82,
    ["AssistBless_TipBottomContent"] = 82,
    ["AssistBless_DateFormat"] = 82,
    ["AssistBless_RewardContent"] = 82,
    ["AssistBless_LoginDay"] = 82,
    ["AssistBless_OpenRewardTime"] = 82,
    ["AssistBless_TimeStamp"] = 82,
    ["AssistBless_OpenReward"] = 82,
    ["UI_NewYearWish_Common_Content"] = 82,
    ["NicknameTooLong"] = 82,
    ["CustomRoom_AutoLeaveRoom"] = 82,
    ["UI_Match_GoMate"] = 82,
    ["UI_Match_MoreParty"] = 82,
    ["Activity_TakeawayNotifyTipTitle"] = 82,
    ["Activity_TakeawayNotifyTipContent"] = 82,
    ["Permission_LocalNotify_Hint"] = 82,
    ["Activity_Takeaway_CanNotHelpSelf"] = 82,
    ["Common_UpgradeVersion"] = 82,
    ["Bag_UpgradeVersionToSaveSlot"] = 82,
    ["UI_Rank_SeasonIsClose"] = 82,
    ["TeamInvite_Friend"] = 82,
    ["TeamInvite_Home"] = 82,
    ["TeamInvite_StarWorld"] = 82,
    ["TeamInvite_Recent"] = 82,
    ["TeamInvite_Near"] = 82,
    ["TeamInvite_Society"] = 82,
    ["TokenAssistHasAssised"] = 82,
    ["TokenAssistTimesIsFull"] = 82,
    ["Lobby_BecomingLocomotive"] = 82,
    ["Player_SameNickName"] = 82,
    ["VisitInProcess"] = 82,
    ["Activity_SuperLinear_GoGetCoin"] = 82,
    ["InLevel_FinaLQualiying_ExchangeLimit"] = 82,
    ["InLevel_FinaLQualiying_ExchangeRate"] = 82,
    ["InLevel_FinaLQualiying_QPST_None"] = 82,
    ["InLevel_FinaLQualiying_QPST_Champion"] = 82,
    ["InLevel_FinaLQualiying_QPST_Level"] = 82,
    ["InLevel_FinaLQualiying_QPST_Eliminate"] = 82,
    ["InLevel_FinaLQualiying_QPST_FinalLevelRank"] = 82,
    ["InLevel_FinaLQualiying_MainViewScore"] = 82,
    ["InviteFriendVisit"] = 82,
    ["VisitFriendInvalidLocation"] = 82,
    ["VisitFriendSuccess"] = 82,
    ["BP_BuySuperPass"] = 82,
    ["CustomRoom_ViewInfoTip"] = 82,
    ["CustomRoom_AddFriendTip"] = 82,
    ["Common_RankDataIsNullTip"] = 82,
    ["Common_RankRefreshing"] = 82,
    ["NewChat_LobbyRedPacketReceive"] = 82,
    ["NewChat_LobbyPositionShare"] = 82,
    ["NewChat_LobbyClubShare"] = 82,
    ["NewChat_LobbyShareBaseInfo"] = 82,
    ["NewChat_LobbyShareJoinTeam"] = 83,
    ["NewChat_LobbyShareJoinRoom"] = 83,
    ["NewChat_LobbyRedPacketSend"] = 83,
    ["CalendarModelFail"] = 83,
    ["RedEnvelope_OnlyLobby"] = 83,
    ["RedEnvelope_OnlyHome"] = 83,
    ["RedEnvelope_LobbyOrHome"] = 83,
    ["RedEnvelope_OnlyPlatFriendOpen"] = 83,
    ["RedEnvelope_OnlyGameFriendOpen"] = 83,
    ["RedEnvelope_OnlyFriendOpen"] = 83,
    ["RedEnvelope_OthersOpened"] = 83,
    ["RedEnvelope_SelfOpened"] = 83,
    ["RedEnvelope_UnEquipRedEnvelope"] = 83,
    ["Rank_Area"] = 83,
    ["VisitingHome"] = 83,
    ["ModelSelectHomeNameOneParam"] = 83,
    ["UI_BattlePass_TaskItemDescFormat"] = 83,
    ["Permission_TencentMap_Hint"] = 83,
    ["UI_NewYearWishes_Time_Tip"] = 83,
    ["UI_NewYearWishes_Fail_Tip"] = 83,
    ["NewChat_ShareToChannel"] = 83,
    ["NewChat_LobbyPositionShareNoSender"] = 83,
    ["NewChat_LobbyClubShareNoSender"] = 83,
    ["NewChat_LobbyShareBaseInfoNoSender"] = 83,
    ["NewChat_LobbyShareJoinTeamNoSender"] = 83,
    ["NewChat_LobbyShareJoinRoomNoSender"] = 83,
    ["NewChat_LobbyRedPacketSendNoSender"] = 83,
    ["QRCode_SubTitle_Team"] = 83,
    ["QRCode_SubTitle_TeamNotice"] = 83,
    ["QRCode_SubTitle_UGC"] = 83,
    ["QRCode_SubTitle_Club"] = 83,
    ["QRCode_SubTitle_Room"] = 83,
    ["QRCode_RoomId_Room"] = 83,
    ["QRCode_RoomId_Club"] = 83,
    ["QRCode_SubTitle_AddFriend"] = 83,
    ["CommonAssistHasAssised"] = 83,
    ["QRCode_Chat_Club_Error"] = 83,
    ["RaffleTap_LockTips"] = 83,
    ["RaffleAccessCfgData_DiscountTips"] = 83,
    ["QRScan_Fail_InRoom"] = 83,
    ["RedEnvelope_CannotEquiped"] = 83,
    ["Room_JoinRoomSuccess"] = 83,
    ["Room_CanNotJoinRoom"] = 83,
    ["Returning_Privilege_Over"] = 83,
    ["Common_Assist_LevelLimit"] = 83,
    ["AssistBless_CenterTilte1"] = 83,
    ["AssistBless_CenterTilte2"] = 83,
    ["Common_MonthDay"] = 83,
    ["Activity_StarLuck_LeftRedPack"] = 83,
    ["Activity_StarLuck_RedPackNotEnough"] = 83,
    ["Activity_LuckyStar_ItemChangeReason_Task"] = 83,
    ["Activity_LuckyStar_ItemChangeReason_Mail"] = 83,
    ["Activity_LuckyStar_ItemChangeReason_Other"] = 83,
    ["luckystar_1"] = 83,
    ["TeamInviteItem_StarWorld"] = 83,
    ["TeamInviteItem_Recent"] = 83,
    ["TeamInviteItem_Near"] = 83,
    ["TeamInviteItem_Society"] = 83,
    ["RedEnvelope_EquipRedEnvWithRedEnvInHand"] = 83,
    ["RedEnvelope_EquipOtherWithRedEnvInHand"] = 83,
    ["RedEnvelope_EquipRedEnvWithOtherInHand"] = 83,
    ["Returning_SelfPlayer_Addition"] = 83,
    ["Returning_PlayWith_Addition"] = 83,
    ["LBS_LocationNotOpen_Tips"] = 83,
    ["Activity_InvitePlayer"] = 83,
    ["Activity_ReCallPlayer"] = 83,
    ["Activity_ReCallLogin"] = 83,
    ["Team_InBattle"] = 83,
    ["AssistBless_RewardFinished"] = 83,
    ["Activity_LuckyStar_ArkShare_Prompt"] = 83,
    ["Activity_LuckyStar_ArkShare_Desc"] = 83,
    ["Team_Invite_CustomRoomPlay"] = 83,
    ["Team_Invite_UgcRoommPlay"] = 83,
    ["RedEnvelope_RedEnvelopeName"] = 83,
    ["RedEnvelope_RedEnvelopeNameInAward"] = 83,
    ["Common_HasCount"] = 83,
    ["UI_Player_Return_TaskDesc"] = 83,
    ["UI_Player_Return_MainView_Time"] = 83,
    ["Team_JoinRecruitFail"] = 83,
    ["Friend_InAddQQFriendWhiteList"] = 83,
    ["NewChat_IsSpeechToText"] = 83,
    ["NewChat_SearchFailed"] = 83,
    ["NewChat_MaxFriendSearch"] = 83,
    ["FriendModel_StickFriendTopSuccess"] = 83,
    ["FriendModel_CancelStickFriendTopSuccess"] = 83,
    ["FriendModel_MaxStickFriendTips"] = 83,
    ["ChatModel_StickFriendTopSuccess"] = 83,
    ["ChatModel_CancelStickFriendTopSuccess"] = 83,
    ["NewChat_RealTimeRecording"] = 83,
    ["RemarkNameNotValid"] = 83,
    ["RemarkNameSameAsOld"] = 83,
    ["Mall_Name"] = 83,
    ["Mall_Gift_Level_Notice"] = 83,
    ["Mall_AskForGift_Notice"] = 83,
    ["Common_ActivityIsOver"] = 83,
    ["Mall_AskForGift_Success"] = 83,
    ["Bag_Equip_Interactive_Fail1"] = 83,
    ["Bag_Equip_Interactive_Fail2"] = 83,
    ["Team_Reservation_WaitEnd"] = 83,
    ["Team_Reservation_NextTime"] = 83,
    ["Team_Reservation_Accept"] = 84,
    ["Team_Reservation_Reject"] = 84,
    ["Team_Reservation_WaitResult"] = 84,
    ["Team_Reservation_SendSuccess"] = 84,
    ["GiftSelfLevelRequest"] = 84,
    ["GiftFriendLevelRequest"] = 84,
    ["Team_Reservation_Player"] = 84,
    ["Common_Reservation"] = 84,
    ["Common_ReservationDone"] = 84,
    ["Common_RejectDone"] = 84,
    ["Common_AcceptDone"] = 84,
    ["Common_Download"] = 84,
    ["Team_Reservation_MoreThanThree"] = 84,
    ["Team_Reservation_DoNotForget"] = 84,
    ["Setting_Game_MultiScene_Explain"] = 84,
    ["Setting_Game_MultiScene_Game"] = 84,
    ["Setting_Game_MultiScene_Social"] = 84,
    ["Setting_Game_MultiScene_Ugc"] = 84,
    ["Club_NewOwner"] = 84,
    ["Club_AddManager"] = 84,
    ["Team_Reservation_PlayTogether"] = 84,
    ["Team_AutoMatch_SceneError"] = 84,
    ["Team_AutoMatch_NotLeader"] = 84,
    ["Team_AutoMatch_InBattle"] = 84,
    ["Team_AutoMatch_InMatch"] = 84,
    ["Team_AutoMatch_InHome"] = 84,
    ["Team_AutoMatch_InRoom"] = 84,
    ["Team_AutoMatch_InSpecificUI"] = 84,
    ["Mall_Gift_NeedCoin_Notice"] = 84,
    ["Activity_SuperLinear_DrawRange"] = 84,
    ["ModeSelect_SelectFactionFail"] = 84,
    ["ModeSelect_NoFaction"] = 84,
    ["ModeSelect_AutoChangeFaction"] = 84,
    ["Common_Random"] = 84,
    ["Mall_Gift_NotAskForItem"] = 84,
    ["Mall_Gift_Money_Enough"] = 84,
    ["Mall_Gift_No_Money"] = 84,
    ["SettingTip_UGCWorldRecord"] = 84,
    ["SettingTip_RankDisplay"] = 84,
    ["SettingTip_SuitBook"] = 84,
    ["SettingTip_ShowBelongClub"] = 84,
    ["SettingTip_DoubleAction"] = 84,
    ["SettingTip_PersonalizedRecommendation"] = 84,
    ["SettingTip_LobbyOrWorldChat"] = 84,
    ["SettingTip_AcceptTeamInvite"] = 84,
    ["ModelSelect_ActivityGoTo"] = 84,
    ["UI_Recharge_QQMiniGamePayFail"] = 84,
    ["Mall_GiftTitle"] = 84,
    ["Mall_DirectBuy_AskForGift_Notice"] = 84,
    ["BP_Title"] = 84,
    ["UI_Recharge_MonthCard_GiveMoreThanMaxDays_Tips"] = 84,
    ["DirectBuy_GiftOrDemand_GiveMoreThanMaxNum"] = 84,
    ["DirectBuy_GiftOrDemand_ConditionIsNotOK"] = 84,
    ["UI_Recharge_MonthCard_BuyMoreThanMaxDays_Tips"] = 84,
    ["Team_AutoMatch_SelfDownload"] = 84,
    ["FriendModel_StickStr"] = 84,
    ["ChatModel_StickStr"] = 84,
    ["QRCode_Friend_AddSelf_Error"] = 84,
    ["Team_AutoMatch_ChangeMatchType"] = 84,
    ["CustomRoom_InTeam"] = 84,
    ["Bag_WaiguanYuMoshiBupipei"] = 84,
    ["Bag_BeiyongwaiguanTishi"] = 84,
    ["Bag_YulanBushipeiTishi"] = 84,
    ["Chat_UGCMap_KongTaiTiShi"] = 84,
    ["MatchQualifyTime_Start"] = 84,
    ["MatchQualifyTime_End"] = 84,
    ["Activity_OldActivityError"] = 84,
    ["Common_SelfBuy"] = 84,
    ["Common_GotoMail"] = 84,
    ["UI_BattlePass_UnlockPreCantBuyTips"] = 84,
    ["KungFuPanda_NoNoodles"] = 84,
    ["KungFuPanda_Rebirth"] = 84,
    ["KungFuPanda_LongTimeStand"] = 84,
    ["KungFuPanda_PlayerClose"] = 84,
    ["KungFuPanda_FeedFull"] = 84,
    ["KungFuPanda_FeedSuccess"] = 84,
    ["KungFuPanda_CanntStart"] = 84,
    ["KungFuPanda_Use"] = 84,
    ["Gongfuxiongmao_huodongjieshao"] = 84,
    ["shenlongdaxiaabao"] = 84,
    ["KungFuPanda_FeedSuccess_UnderTime"] = 84,
    ["KungFuPanda_FeedFull_UnderTime"] = 84,
    ["UI_LuckBuy_GiveContent"] = 84,
    ["UI_Model_LimitedQualifyTime"] = 84,
    ["UI_Moeel_QualifyStartToEndDay"] = 84,
    ["UI_Model_QualifyWeekDayOpen"] = 84,
    ["UI_Model_AddCollect"] = 84,
    ["UI_Model_RemoveCollect"] = 84,
    ["KungFuPanda_yangguangpuzhao"] = 84,
    ["Model_CurrentModelCantTeam"] = 84,
    ["UI_Common_Quarantine"] = 84,
    ["Concert_StarDetail"] = 84,
    ["Concert_NotInTime"] = 84,
    ["UI_Recharge_UI_SecondChargeRebate_Main_Title"] = 84,
    ["Concert_GetTicket"] = 84,
    ["NewChat_TeamVoiceRestored"] = 84,
    ["NewChat_TeamVoiceNormal"] = 84,
    ["NewChat_TeamVoiceCantUse"] = 84,
    ["NewChat_TeamVoiceOpenSpeakerHint"] = 84,
    ["NewChat_TeamVoiceOpenMicroHint"] = 84,
    ["NewChat_TeamVoiceHintCd"] = 85,
    ["UGC_CANNOT_USE_VEHICLE"] = 85,
    ["Friend_RecommendRelation"] = 85,
    ["NewChat_TeamVoiceOpenSpeakerAsked"] = 85,
    ["NewChat_TeamVoiceOpenMicroAsked"] = 85,
    ["CustomMovement_Title"] = 85,
    ["CustomMovement_ChooseFriend"] = 85,
    ["CustomMovement_DoubleCheck"] = 85,
    ["CustomMovement_NoIntimateRelationship"] = 85,
    ["CustomMovement_NoSearchResult"] = 85,
    ["CustomMovement_FullMovement"] = 85,
    ["Activity_TeamPhoto_SaveSucceed"] = 85,
    ["Activity_TeamPhoto_SaveFailed"] = 85,
    ["Activity_TeamPhoto_ChangeSucceed"] = 85,
    ["Activity_TeamPhoto_ChangeFailed"] = 85,
    ["Activity_TeamPhoto_NotAllowNull"] = 85,
    ["Activity_TeamPhoto_MyTeam"] = 85,
    ["Activity_TeamPhoto_ChangeNameSucceed"] = 85,
    ["Activity_TeamPhoto_Pos"] = 85,
    ["Activity_TeamPhoto_Pos1"] = 85,
    ["Activity_TeamPhoto_Pos2"] = 85,
    ["Activity_TeamPhoto_Pos3"] = 85,
    ["Activity_TeamPhoto_Pos4"] = 85,
    ["Activity_TeamPhoto_Photo"] = 85,
    ["Activity_TeamPhoto_PhotoTime"] = 85,
    ["Team_PST_Xiaowo"] = 85,
    ["Team_PST_InSettlement"] = 85,
    ["Team_PST_UnPrepare"] = 85,
    ["Team_PST_Prepare"] = 85,
    ["Team_PST_CancelPrepare"] = 85,
    ["friend_OnlineReminderTip"] = 85,
    ["Chat_UGCCollectionMap_MapCount"] = 85,
    ["Chat_UGCCollectionMap_MapTitle"] = 85,
    ["Chat_UGCCollectionMap_Invalid"] = 85,
    ["Chat_UGCCollectionMap_CantShareEmpty"] = 85,
    ["Friend_Intimate_Gift"] = 85,
    ["Friend_Intimate_Team"] = 85,
    ["Friend_Intimate_GoMall"] = 85,
    ["Friend_Intimate_ShowTips"] = 85,
    ["NewChat_TeamVoiceOpenMicroAskSucceed"] = 85,
    ["NewChat_TeamVoiceOpenSpeakerAskSucceed"] = 85,
    ["UI_Recharge_MonthCard_GetMoreThanMaxDays_Tips"] = 85,
    ["Bag_SlotEquiped"] = 85,
    ["Bag_Slot_Backup"] = 85,
    ["Mall_CloudCoin"] = 85,
    ["Mall_StarDiamond"] = 85,
    ["Mall_Activity"] = 85,
    ["UI_Recharge_Ul_SecondChargeRebate_Main_Title"] = 85,
    ["Mode_TabSelect_Qualify"] = 85,
    ["Mode_TabSelect_Casual"] = 85,
    ["PlayerInfo_NoDataForSlot"] = 85,
    ["PlayerInfo_EditSlotConfirmTip"] = 85,
    ["PlayerInfo_SlotName"] = 85,
    ["PlayerInfo_BackupSlotName"] = 85,
    ["NewChat_SuperCore"] = 85,
    ["NewChat_Home"] = 85,
    ["SettingTip_ShowSeasonFashion"] = 85,
    ["NewChat_DeleteRecord_Confirm"] = 85,
    ["NewChat_DeleteRecord_Success"] = 85,
    ["NewChat_DeleteRecord_Error_Empty"] = 85,
    ["CustomRoom_NoneLifeRecord"] = 85,
    ["CustomRoom_LifeRecord"] = 85,
    ["Team_IsInFarmScene"] = 85,
    ["Player_PhotoAlbumTabName"] = 85,
    ["Activity_TeamPhoto_ActionTemplate"] = 85,
    ["Activity_TeamPhoto_Background"] = 85,
    ["Activity_TeamPhoto_Expression"] = 85,
    ["Activity_TeamPhoto_TemplateNotAvailable"] = 85,
    ["Clup_NoticeJoinGroup"] = 85,
    ["Clup_NoticeJoinGroupWX"] = 85,
    ["Clup_NoticeJoinGroupQQ"] = 85,
    ["PlayerInfo_DefaultProfileThemeName"] = 85,
    ["PlayerInfo_DefaultProfileThemeDes"] = 85,
    ["UI_PlayerInfo_PhotoAlbum_DeleteTips"] = 85,
    ["UI_PlayerInfo_PhotoAlbum_DeleteSuccessTips"] = 85,
    ["UI_PlayerInfo_PhotoAlbum_SaveMoreThanMaxTips"] = 85,
    ["UI_PlayerInfo_PhotoAlbum_SaveAgainTips"] = 85,
    ["UI_PlayerInfo_PhotoAlbum_SaveSuccessTips"] = 85,
    ["NewChat_TeamRecruit"] = 85,
    ["Bag_Combination_Count"] = 85,
    ["Bag_Combination_Name"] = 85,
    ["Bag_Combination_ErrorInfo"] = 85,
    ["Bag_Combination_ErrorPreview"] = 85,
    ["Bag_Combination_NameEmpty"] = 85,
    ["Bag_Combination_ChatMsgEmpty"] = 85,
    ["Bag_Combination_NotSave"] = 85,
    ["Bag_Combination_DeleteCombination"] = 85,
    ["Clup_JoinGroupWXSuccess"] = 85,
    ["Clup_JoinGroupQQSuccess"] = 85,
    ["Clup_NoticeUnbindGroup"] = 85,
    ["Club_NoticeUnbindGroupText"] = 85,
    ["Club_NoticeCreateGroupText"] = 85,
    ["Club_NoticeCreateGroupTips"] = 85,
    ["Club_Button_Close"] = 85,
    ["Club_Button_Confirm"] = 85,
    ["PlayerInfo_DunDes"] = 85,
    ["PlayerInfo_CannotPreviewSuit"] = 85,
    ["UI_PlayerInfo_PhotoAlbum_SaveFailedTips"] = 85,
    ["PlayerInfo_FashionValueInPreviewSuit"] = 85,
    ["Club_Button_WxGroup"] = 85,
    ["Club_Button_QQGroup"] = 86,
    ["friend_targetLobbyNotDownloaded"] = 86,
    ["friend_targetLobbyNotJump"] = 86,
    ["friend_notJump"] = 86,
    ["GameGotoTask_TS_Init"] = 86,
    ["GameGotoTask_TS_Completed"] = 86,
    ["GameGotoTask_TS_Rewarded"] = 86,
    ["GameGotoActivity_TS_Init"] = 86,
    ["GameGotoActivity_TS_Completed"] = 86,
    ["GameGotoActivity_TS_Rewarded"] = 86,
    ["Team_PST_Farm"] = 86,
    ["Team_ShowInfoInTeam"] = 86,
    ["Team_ShowInfoInRoom"] = 86,
    ["Lobby_StatusCanNotUseHandHold"] = 86,
    ["Rank_Gps_Location"] = 86,
    ["Club_NoticeCreateGroupQQText"] = 86,
    ["NewChat_Stranger_ChatOver"] = 86,
    ["NewChat_Stranger_ChatBusy"] = 86,
    ["NewChat_Stranger_ChatWaitResp"] = 86,
    ["NewChat_Stranger_ChatNeedResp"] = 86,
    ["NewChat_Stranger_ChatManualOver"] = 86,
    ["NewChat_Stranger_Fobbiden"] = 86,
    ["NewChat_Stranger_TooMuch"] = 86,
    ["NewChat_Stranger_Error"] = 86,
    ["NewChat_Stranger_DefaultMessage"] = 86,
    ["NewChat_Stranger_ChatOver_Confirm"] = 86,
    ["NewChat_Stranger_Tag"] = 86,
    ["NewChat_Stranger_AlreadyFriend"] = 86,
    ["NewChat_Stranger_PermitStrangerSayHi"] = 86,
    ["NewChat_Stranger_LimitBeforeResp"] = 86,
    ["Billboard_Voice_No_Text"] = 86,
    ["Activity_TeamPhoto_EmojiInoperable"] = 86,
    ["Activity_TeamPhoto_EmojiUnableToLock"] = 86,
    ["Activity_TeamPhoto_ReachTheUpperLimit"] = 86,
    ["Team_SimpleShowInfoInTeam"] = 86,
    ["Team_SimpleShowInfoInRoom"] = 86,
    ["AcceptJoinError_1"] = 86,
    ["AcceptJoinError_2"] = 86,
    ["AcceptJoinError_3"] = 86,
    ["AcceptJoinError_4"] = 86,
    ["AcceptJoinError_5"] = 86,
    ["Invite_Reject_ServerTip"] = 86,
    ["Team_WantJoinSendFinish"] = 86,
    ["Team_WantJoinSendFailed"] = 86,
    ["Team_IsInTargetTeam"] = 86,
    ["Team_IsInTargetRoom"] = 86,
    ["RH_Tab_MatchRecruit"] = 86,
    ["RH_Tab_UGCRecruit"] = 86,
    ["RH_SubTab_RoomRecruit"] = 86,
    ["RH_SubTab_TeamRecruit"] = 86,
    ["Friend_Later_add"] = 86,
    ["UI_ScreenSnapshot_Normal"] = 86,
    ["UI_ScreenSnapshot_UGCCommunityScene"] = 86,
    ["UI_ScreenSnapshot_CommunityScene"] = 86,
    ["UI_ScreenSnapshot_UGCMapScene"] = 86,
    ["UI_ScreenSnapshot_HomeScene"] = 86,
    ["UI_ScreenSnapshot_FarmScene"] = 86,
    ["UI_ScreenSnapshot_InGameScene"] = 86,
    ["Bag_RentFriendFashionTip"] = 86,
    ["Bag_RentFriendFashionTip_1"] = 86,
    ["SettingTip_RecommendFriend"] = 86,
    ["SettingTip_ShowTeamExtraInfo"] = 86,
    ["SettingTip_baby_funSetting"] = 86,
    ["SettingTip_baby_closeChat"] = 86,
    ["SettingTip_baby_closeTeamInvite"] = 86,
    ["SettingTip_baby_closeStrangerChat"] = 86,
    ["SettingTip_baby_closeVisitHome"] = 86,
    ["Setting_Tips_GoToBabyProtectFun"] = 86,
    ["SettingTip_baby_funSetting_all"] = 86,
    ["NewChat_WantToPlay"] = 86,
    ["Club_Activity"] = 86,
    ["NewChat_Stranger_CantUseVoice"] = 86,
    ["NewChat_Stranger_AlreadySayHiWaitResp"] = 86,
    ["Team_InWantToPlayCD"] = 86,
    ["Team_IsInMineRoom"] = 86,
    ["CustomRoom_ChangeLeaderTip"] = 86,
    ["CustomRoom_BeLeaderTip"] = 86,
    ["NewChat_Farm"] = 86,
    ["RaffleAccessCfgData_SwanDiscountTips"] = 86,
    ["QQVideoCantPlayWhenSwitchScene"] = 86,
    ["Mail_Gift_Notice"] = 86,
    ["Activity_WishesCameTrue_SelectGift"] = 86,
    ["Activity_WishesCameTrue_ConfirmGift"] = 86,
    ["Activity_WishesCameTrue_WishRecord"] = 86,
    ["Activity_WishesCameTrue_RewardRecord"] = 86,
    ["Activity_WishesCameTrue_NoLotteryCoin"] = 86,
    ["Activity_WishesCameTrue_NeedLockAllGifts"] = 86,
    ["Activity_WishesCameTrue_CopyTokenSucess"] = 86,
    ["Activity_WishesCameTrue_GetWishCoinSucess"] = 86,
    ["Activity_WishesCameTrue_NoRepeatHelp"] = 86,
    ["Activity_WishesCameTrue_NoRepeatHelp2"] = 86,
    ["Activity_WishesCameTrue_LotteryOpenTime"] = 86,
    ["Activity_WishesCameTrue_RewardNotification"] = 86,
    ["Activity_WishesCameTrue_InvalidToken"] = 86,
    ["Activity_WishesCameTrue_CannotHelpSelf"] = 86,
    ["Activity_WishesCameTrue_CannotShareToken"] = 86,
    ["Activity_WishesCameTrue_CannotHelp"] = 86,
    ["Activity_WishesCameTrue_HelpGetWishCoin"] = 86,
    ["Activity_WishesCameTrue_ReachHelpLimitTip"] = 86,
    ["Activity_WishesCameTrue_TaskGetWishCoin"] = 86,
    ["Activity_WishesCameTrue_LotteryGetReward"] = 87,
    ["Activity_WishesCameTrue_GetGift"] = 87,
    ["Activity_WishesCameTrue_WarmTip"] = 87,
    ["Activity_WishesCameTrue_GetFreeWishCoin"] = 87,
    ["Activity_WishesCameTrue_Known"] = 87,
    ["Activity_WishesCameTrue_ReachHelpLimitTip2"] = 87,
    ["Activity_WishesCameTrue_NoRepeatHelp3"] = 87,
    ["Activity_WishesCameTrue_OtherHelpErrorTip"] = 87,
    ["Activity_HYWarmUp_Day"] = 87,
    ["Activity_HYWarmUp_HYBegin"] = 87,
    ["Common_TipSign"] = 87,
    ["Common_GoNow"] = 87,
    ["Activity_HYWarmUp_FinishTaskTip"] = 87,
    ["Mall_NotOpenZeroBuy"] = 87,
    ["Mode_TimeLimitMatch"] = 87,
    ["Mode_SomeTimeLaterMatchClose"] = 87,
    ["Prohibit_Editing"] = 87,
    ["System_PlayerReturn_Recharge_BigReward"] = 87,
    ["System_PlayerReturn_Recharge_DailyReward"] = 87,
    ["SettingTip_ShowInfoInList"] = 87,
    ["Club_Disable_Name"] = 87,
    ["Club_Disable_Dsec"] = 87,
    ["Club_Disable_Icon"] = 87,
    ["Common_CurrentTrophyCount"] = 87,
    ["Common_WinCoun_Short"] = 87,
    ["Common_WXLoginFailWithReason"] = 87,
    ["Common_QQLoginFailWithReason"] = 87,
    ["Common_WXLoginFailWithCode"] = 87,
    ["Common_QQLoginFailWithCode"] = 87,
    ["Team_CanNotRecommendInMatch"] = 87,
    ["Team_CanNotJoinRoomInMatch"] = 87,
    ["Team_CanNotChangeModeInMatch"] = 87,
    ["Team_CanNotChangeMatchInMatch"] = 87,
    ["SettingTip_ToFriendShowInfo"] = 87,
    ["SettingTip_ToStrangerShowInfo"] = 87,
    ["CustomRoom_ChangeMatchTip"] = 87,
    ["System_PlayerReturn_ActivityIsDone"] = 87,
    ["Friend_AlreadyIntimate"] = 87,
    ["Friend_IntimateNumMax"] = 87,
    ["Friend_OnlyFriendCanChangeName"] = 87,
    ["Friend_NewChatFriendCellNewFriend"] = 87,
    ["NewChat_Club_ShareImageSuccess"] = 87,
    ["CustomRoom_UpdateLocationFail"] = 87,
    ["Team_OtherJoinSuccessFromOne"] = 87,
    ["Team_SelfJoinSuccess"] = 87,
    ["Team_OtherJoinSuccessFromTeam"] = 87,
    ["Team_SelfLeaveSuccess"] = 87,
    ["Team_OtherLeaveSuccessFromTeam"] = 87,
    ["Activity_WaitChecked"] = 87,
    ["Friend_IntimateNotEnough"] = 87,
    ["IAA_PlayAD_Fail"] = 87,
    ["IAA_PlayAD_Success"] = 87,
    ["IAA_LotteryView_CountTips"] = 87,
    ["IAA_FinalAccount_Tips"] = 87,
    ["System_PlayerReturn_InvalidJump"] = 87,
    ["Club_Rank"] = 87,
    ["IAA_LotteryView_Enter_CountTips"] = 87,
    ["Activity_CanReceiveReward"] = 87,
    ["Activity_SureToClearAllReddots"] = 87,
    ["Team_CannotUsePropInTeamShow"] = 87,
    ["CustomRoom_ChangeModeTipInLobby"] = 87,
    ["Team_CannotJoinRoomInTeam"] = 87,
    ["CustomRoom_ForbidenOperation"] = 87,
    ["CustomAction_Iknow"] = 87,
    ["CustomAction_GotoBind"] = 87,
    ["CustomAction_BindFriendTips"] = 87,
    ["Team_ReturnPlayerInTeam"] = 87,
    ["QQVideoProtect_Setting_Tips"] = 87,
    ["QQVideoProtect_Setting_ForgetPasswordTimeFormat"] = 87,
    ["QQVideoProtect_Setting_PasswordNotEnoughTips"] = 87,
    ["QQVideoProtect_Setting_UnlockSuccessTips"] = 87,
    ["QQVideoProtect_Setting_UnlockFailTips"] = 87,
    ["QQVideoProtect_Setting_ForgetPasswordTips"] = 87,
    ["QQVideoProtect_Setting_ForgetPasswordSuccessTips"] = 87,
    ["QQVideoProtect_Setting_UnlockEmptyInputTips"] = 87,
    ["QQVideoProtect_Setting_LockNotEnoughTips"] = 87,
    ["QQVideoProtect_Setting_UnlockOpenQQVideoNotEnoughTips"] = 87,
    ["QQVideoProtect_Setting_UnlockOpenQQVideoPasswordWrongTips"] = 87,
    ["QQVideoProtect_Setting_LockEmptyTips"] = 87,
    ["QQVideoProtect_Setting_LockAgainPasswordNotSameTips"] = 87,
    ["QQVideoProtect_Setting_LockSuccessTips"] = 87,
    ["QQVideoProtect_Setting_LockConfirm"] = 87,
    ["QQVideoProtect_Setting_LockAgainNotEnoughTips"] = 87,
    ["QQVideoProtect_Setting_LockAgainEmptyTips"] = 87,
    ["QQVideoProtect_Setting_OpenQQVideoLockTips"] = 87,
    ["QQVideoProtect_Setting_TipUserLockTips"] = 87,
    ["QQVideoProtect_Setting_TipUserConfirm"] = 87,
    ["QQVideoProtect_Setting_TipUserCancel"] = 87,
    ["NumericKeypad_MaxLength_Tips"] = 87,
    ["NumericKeypad_MinLength_Tips"] = 87,
    ["QQVideo_TipCloseTips"] = 87,
    ["CloudEnv_RecentPlay"] = 87,
    ["Cup_Bg_Title_Main"] = 87,
    ["Cup_Bg_Title_Task"] = 87,
    ["Cup_Bg_AwardWeekMaxHint"] = 87,
    ["Friend_Intimate_Farm"] = 87,
    ["UI_Friend_Days"] = 87,
    ["UI_Team_Nums"] = 87,
    ["UI_Speak_Nums"] = 87,
    ["UI_Coin_Nums"] = 87,
    ["UI_XiaoWo_Nums"] = 88,
    ["UI_Farm_Nums"] = 88,
    ["UI_SendGift_Nums"] = 88,
    ["Activity_Monopoly_MissDice"] = 88,
    ["Activity_Monopoly_TreasureChestsReward"] = 88,
    ["Activity_Monopoly_TreasureChestsRewardTip"] = 88,
    ["Activity_Monopoly_TreasureChestsRewardTip2"] = 88,
    ["Activity_Monopoly_RewardPreview"] = 88,
    ["Activity_Monopoly_AdvanceXStep"] = 88,
    ["Activity_Monopoly_AdvanceXStep2"] = 88,
    ["Activity_Monopoly_CannotDiceWhenMoving"] = 88,
    ["Activity_Monopoly_StopOnEmptyGrid"] = 88,
    ["Activity_Monopoly_StopOnAdvanceGrid"] = 88,
    ["Activity_Monopoly_StopOnConfirmedDiceGrid"] = 88,
    ["Activity_Monopoly_StopOnDoubleDiceGrid"] = 88,
    ["Activity_Monopoly_ClickTreasureGridTip"] = 88,
    ["Activity_Monopoly_PassTreasureGrid"] = 88,
    ["Activity_Monopoly_StopOnStartPoint"] = 88,
    ["Activity_Monopoly_PassStartPoint"] = 88,
    ["Activity_Monopoly_StopOnJumpGrid"] = 88,
    ["Activity_Monopoly_AutoLotteryTip"] = 88,
    ["Activity_Monopoly_LotteryPreviewTitle"] = 88,
    ["Activity_Monopoly_RemainsX"] = 88,
    ["Activity_Monopoly_ClickToTake"] = 88,
    ["Activity_Monopoly_TookAll"] = 88,
    ["Activity_Monopoly_TreasureFull"] = 88,
    ["Activity_Monopoly_LotteryTitle"] = 88,
    ["Activity_Monopoly_LotteryPoolTip"] = 88,
    ["Rank_ID_Mastery"] = 88,
    ["UI_PlayerInfo_CupAward_Tip"] = 88,
    ["ExclusiveVehicle_ForbidSpecialSkillSlotTips"] = 88,
    ["Cup_Bg_Title_LeftUp"] = 88,
    ["CustomAction_Vehicle1P"] = 88,
    ["CustomAction_Vehicle2P"] = 88,
    ["AnimalHandbook_FeedConfirm"] = 88,
    ["AnimalHandbook_FeedTips"] = 88,
    ["AnimalHandbook_DonateTimesTips"] = 88,
    ["ActNavigation_UnlockSystemByLevel"] = 88,
    ["Cup_NotUpToLevel"] = 88,
    ["Cup_NotUpToLevelHint"] = 88,
    ["NewChat_ClubScreenShare"] = 88,
    ["UI_ActivityFridayCollectionItemNotStarted"] = 88,
    ["UI_ActivityFridayCollectionTimeStart"] = 88,
    ["HandHold_ExtraEffect_LimitInCurren"] = 88,
    ["HandHold_ExtraEffect_InCD"] = 88,
    ["HandHold_ExtraEffect_ReachMaxCount"] = 88,
    ["HandHold_ExtraEffect_ExistOtherExtraEffect"] = 88,
    ["HandHold_ExtraEffect_UnknownReason"] = 88,
    ["Handbook_Record_Donate"] = 88,
    ["Handbook_Record_Receive"] = 88,
    ["Team_ReturnPlayerInTeamResult"] = 88,
    ["NewChat_Vertical_InviteTeam"] = 88,
    ["Cup_LevelEndTag"] = 88,
    ["Cup_MainSelfCupWeekMaxTag"] = 88,
    ["MainGame_SystemMusicDownload_Tip"] = 88,
    ["MainGame_SystemMusicDownload_Cancel"] = 88,
    ["MainGame_SystemMusicDownload_Download"] = 88,
    ["MainGame_NoDownloadMusicTip"] = 88,
    ["MainGame_RecommendDownloadMusicTip"] = 88,
    ["Friend_GotoIntimateShowTips"] = 88,
    ["Cup_MaxTipHint"] = 88,
    ["UI_PlayerInfo_CupAward_Title"] = 88,
    ["NewChat_Vertical_JoinClubRankNotice_Bottom"] = 88,
    ["NewChat_Vertical_JoinClubRank_Bottom"] = 88,
    ["NewChat_Vertical_SystemJoinClubRank_Bottom"] = 88,
    ["NewChat_Vertical_ClubWeekSettle_Bottom"] = 88,
    ["Cup_ShetuanTitle"] = 88,
    ["Rank_Common_Tips"] = 88,
    ["Rank_ID_Mastery_Tips"] = 88,
    ["UI_Please_MakeFriend"] = 88,
    ["Activity_ReserveLiveRoomFailed"] = 88,
    ["NewChat_ClubScreenShare_LobbyLeft"] = 88,
    ["NewChat_ClubWeekSettle_LobbyLeft"] = 88,
    ["NewChat_JoinClubRankNotice_LobbyLeft"] = 88,
    ["NewChat_SystemJoinClubRank_LobbyLeft"] = 88,
    ["NewChat_JoinClubRank_LobbyLeft"] = 88,
    ["NewChat_Club_ShareClubRankSuccess"] = 88,
    ["UI_Activity_FarmActivity_GetCount"] = 88,
    ["UI_Activity_FarmActivity_DrawOnce"] = 88,
    ["UI_Activity_FarmActivity_LotteryTip"] = 88,
    ["Task_TabNotExist"] = 88,
    ["Cup_NotReady"] = 88,
    ["Arena_CardPackOverflowReturn"] = 88,
    ["AnimalHandbook_CatchTips"] = 88,
    ["QQVideoCantPlayWhenScreenDirChange"] = 88,
    ["Text_TeamJoin_Notice"] = 88,
    ["Bag_SeasonTipsNum"] = 88,
    ["CupRankRule"] = 88,
    ["CupRankRuleDesc"] = 88,
    ["UI_PermitAdvanceUnlock_BtnTxt"] = 88,
    ["UI_ConvenedRecallTab_Txt"] = 88,
    ["UI_ConvenedInviteTab_Txt"] = 88,
    ["UI_ConvenedRecallLoginTab_Txt"] = 88,
    ["UI_ConvenedInviteLoginTab_Txt"] = 88,
    ["UI_ConvenedRecallLoginTips_Txt"] = 88,
    ["UI_ConvenedInviteLoginTips_Txt"] = 88,
    ["UI_ConvenedRecallEmptyTips_Txt"] = 88,
    ["UI_ConvenedInviteEmptyTips_Txt"] = 88,
    ["MatchQualifyTime_Start_ShowName"] = 88,
    ["MatchQualifyTime_End_ShowName"] = 88,
    ["BP_BuySuperPass_TipsText"] = 89,
    ["Cup_DailyTaskHint"] = 89,
    ["Common_QQBackToCommunityTip"] = 89,
    ["Common_GoToCommunity"] = 89,
    ["Activity_PleaseCheckedAndReceive"] = 89,
    ["Team_PST_TeamShow"] = 89,
    ["Team_PST_TeamShowLobbyTip"] = 89,
    ["Team_PST_NormalLobbyTip"] = 89,
    ["Team_MatchNotUnlockTip"] = 89,
    ["Team_MatchNotDownloadTip"] = 89,
    ["UI_ExpansionSuccessful"] = 89,
    ["Farmyard_QQShare_Tips"] = 89,
    ["Club_UGCMapCollected"] = 89,
    ["Club_PersonalPhotoAlbum"] = 89,
    ["Player_ChatFont"] = 89,
    ["ColorFont_BaseText"] = 89,
    ["Douyin_ShareNotInstallApp"] = 89,
    ["IAA_BalancePop_Tips"] = 89,
    ["CustomRoom_ExitRoomTip"] = 89,
    ["Club_SendToClub"] = 89,
    ["Club_RankNotOpen"] = 89,
    ["Team_ExitTeamTip"] = 89,
    ["ReChoose_Exit"] = 89,
    ["ReChoose_Confirm"] = 89,
    ["ReChoose_Succeed"] = 89,
    ["ClubRankRule"] = 89,
    ["ClubRankRuleDesc"] = 89,
    ["ReChoose_GiveUp"] = 89,
    ["UI_PrivacyIsSet"] = 89,
    ["UI_NoWayTeleportToLocation"] = 89,
    ["TeamShow_LobbyChanging"] = 89,
    ["TeamShow_LobbyChangedCD"] = 89,
    ["Activity_Exchange_PlayerReturn_SelectGift"] = 89,
    ["PlayerReturn_Calendar_Tip"] = 89,
    ["UI_PlayerInfo_StarPInfo_Text"] = 89,
    ["UI_PlayerInfo_StarPInfo_Tips"] = 89,
    ["Cup_Bg_Title_ModelSelect"] = 89,
    ["UI_GreenHouse_Result_Normal_1"] = 89,
    ["UI_GreenHouse_Result_Normal_2"] = 89,
    ["UI_GreenHouse_Result_Normal_3"] = 89,
    ["UI_GreenHouse_Result_Normal_1_Uid"] = 89,
    ["UI_GreenHouse_Result_Normal_2_Uid"] = 89,
    ["UI_GreenHouse_Result_Normal_3_Uid"] = 89,
    ["UI_GreenHouse_Result_Normal_Btn"] = 89,
    ["UI_GreenHouse_Result_New"] = 89,
    ["UI_GreenHouse_Result_New_Uid"] = 89,
    ["UI_GreenHouse_Result_New_Btn"] = 89,
    ["UI_GreenHouse_Result_Smurf"] = 89,
    ["UI_GreenHouse_Result_Smurf_Uid"] = 89,
    ["UI_GreenHouse_Result_Black"] = 89,
    ["UI_GreenHouse_Result_Already"] = 89,
    ["UI_GreenHouse_Result_Limit"] = 89,
    ["UI_GreenHouse_Result_End"] = 89,
    ["UI_GreenHouse_Result_Error"] = 89,
    ["UI_GreenHouse_Result_Same"] = 89,
    ["Lobby_InviteTrain"] = 89,
    ["Team_HasMemberMatchLocked"] = 89,
    ["Team_HasMemberMatchUnDownload"] = 89,
    ["UI_HonoroKing_SuitTitleFinish"] = 89,
    ["UI_HonoroKing_SuitTitleSelect"] = 89,
    ["UI_HonoroKing_OrnamentTitleFinish"] = 89,
    ["UI_HonoroKing_OrnamentTitleSelect"] = 89,
    ["UI_HonoroKing_SuitHasTips"] = 89,
    ["UI_HonoroKing_OrnamentHasTips"] = 89,
    ["UI_HonoroKing_SuitWishDoneTips"] = 89,
    ["UI_HonoroKing_OrnamentWishDoneTips"] = 89,
    ["Pak_WiFiDownloadPreviewRes"] = 89,
    ["Pak_MobileNetDownloadPreviewRes"] = 89,
    ["UI_BattlePass_IAA_CountTips"] = 89,
    ["Mall_NotDownloaded_GoToMain"] = 89,
    ["UI_ShowCup"] = 89,
    ["UI_ShowCupHelp"] = 89,
    ["Tips_BagChat_NoPassWorldCheck"] = 89,
    ["UI_NotEstablished"] = 89,
    ["UI_Couple"] = 89,
    ["UI_Brothers"] = 89,
    ["UI_Chums"] = 89,
    ["UI_Girlfriends"] = 89,
    ["System_MineSweeper_Items_Insufficient"] = 89,
    ["System_MineSweeper_Stamina_Title"] = 89,
    ["System_MineSweeper_Event_OpenRow"] = 89,
    ["System_MineSweeper_Event_OpenColumn"] = 89,
    ["System_MineSweeper_Event_ShowReward"] = 89,
    ["System_MineSweeper_ConsumeTicket_EveryTime"] = 89,
    ["System_MineSweeper_Slogan"] = 89,
    ["System_MineSweeper_Event_OpenRowToast"] = 89,
    ["System_MineSweeper_Event_OpenColumnToast"] = 89,
    ["System_MineSweeper_Event_ShowRewardToast"] = 89,
    ["System_MineSweeper_Is_Playing_Animation"] = 89,
    ["System_MineSweeper_Number_ShowAround"] = 89,
    ["UI_RefuseLuckyMission"] = 89,
    ["UI_Setting_Predown_Tips_On"] = 89,
    ["UI_Setting_Predown_Tips_Off"] = 89,
    ["UI_Langrenqixi_Process_Notfull_tips"] = 89,
    ["UI_Langrenqixi_Nokey_tips"] = 89,
    ["UI_Langrenqixi_NeitherofBoth_tips"] = 89,
    ["Team_ChangeLeaderTip"] = 89,
    ["Team_BeLeaderTip"] = 89,
    ["UI_TopIntimacyFriend"] = 89,
    ["MiniGame_DouYin_Not_Support"] = 89,
    ["ReChoose_ChangeNameFreeHint"] = 90,
    ["ReChoose_ChangeCharacterFreeHint"] = 90,
    ["UI_Mission_IsFinish"] = 90,
    ["UI_Mission_IsClose"] = 90,
    ["UI_WXGameHUD_CollectTip"] = 90,
    ["UI_Mission_HaveTime"] = 90,
    ["UI_Mission_HaveMission"] = 90,
    ["Mall_SuccessfullyCard_CheckToFarmCard"] = 90,
    ["Activity_GroupReturning_InviteLimitTip"] = 90,
    ["Activity_GroupReturning_BeLeaderTip"] = 90,
    ["Activity_GroupReturning_TickedTip"] = 90,
    ["Activity_GroupReturning_ExitedTip"] = 90,
    ["Activity_GroupReturning_BuyBeforeGetTip"] = 90,
    ["Activity_GroupReturning_BuySucessTip"] = 90,
    ["Activity_GroupReturning_NoPlayerTip"] = 90,
    ["Activity_GroupReturning_SentInviteTip"] = 90,
    ["Activity_GroupReturning_WaitTip"] = 90,
    ["Activity_GroupReturning_JoinedOtherTip"] = 90,
    ["Activity_GroupReturning_JoinedThisTip"] = 90,
    ["Activity_GroupReturning_FullTip"] = 90,
    ["Activity_GroupReturning_JoinSucessTip"] = 90,
    ["Activity_GroupReturning_Known"] = 90,
    ["Activity_GroupReturning_ViewInfo"] = 90,
    ["Activity_GroupReturning_Kick"] = 90,
    ["Activity_GroupReturning_Grouped"] = 90,
    ["Activity_GroupReturning_Invite"] = 90,
    ["Activity_GroupReturning_AskInvite"] = 90,
    ["Activity_GroupReturning_ExitGroupTip"] = 90,
    ["Activity_GroupReturning_AskJoin"] = 90,
    ["Activity_GroupReturning_JoinWarningTip"] = 90,
    ["Activity_GroupReturning_ExitConfirmTip"] = 90,
    ["Activity_GroupReturning_KickConfirmTip"] = 90,
    ["Activity_GroupReturning_Chat_JoinNoSender"] = 90,
    ["Activity_GroupReturning_Chat_JoinWithSender"] = 90,
    ["Activity_GroupReturning_BeKickedTip"] = 90,
    ["Activity_GroupReturning_InvalidInvate"] = 90,
    ["UI_LikeHistoryItemShow"] = 90,
    ["LFT_PersonalMain"] = 90,
    ["LFT_PersonalCard"] = 90,
    ["UI_Mission_LackSome"] = 90,
    ["UI_Mission_InviteFriend"] = 90,
    ["UI_WishingTree_NoWishing"] = 90,
    ["UI_WishingTree_NoChoose"] = 90,
    ["WXMoreGamePlay_BackToLobby"] = 90,
    ["Team_DownloadPercent"] = 90,
    ["NoPictureAccessTips"] = 90,
    ["GuideApp_Check_Fail"] = 90,
    ["GuideApp_Settlement"] = 90,
    ["GuideApp_MsgBox_Main"] = 90,
    ["GuideApp_MsgBox_Download"] = 90,
    ["GuideApp_PopAdsBtn_Normal"] = 90,
    ["GuideApp_PopAdsBtn_Countdown"] = 90,
    ["GuideApp_PopAdsBtn_Close"] = 90,
    ["GuideApp_MsgBox_Activity_Main"] = 90,
    ["GuideApp_MsgBox_Activity_Download"] = 90,
    ["Common_ShowAfterDownload"] = 90,
    ["UI_LikeHistoryHasFollowBack"] = 90,
    ["UI_CantChangeBackground"] = 90,
    ["UI_LeaderChangeBackground"] = 90,
    ["Team_CannotUsePropInMatching"] = 90,
    ["Common_DownloadFirst"] = 90,
    ["Activity_Easy_LockTips"] = 90,
    ["Activity_EasyReserve_MainTips"] = 90,
    ["Activity_EasyReserve_SuccessTips"] = 90,
    ["Activity_EasyTask_MainTips"] = 90,
    ["Activity_EasyTask_TotalTips"] = 90,
    ["Activity_EasyTask_CDTips"] = 90,
    ["Activity_EasyTask_ItemLeft"] = 90,
    ["ActivityEasyTeamTitle"] = 90,
    ["Activity_TabSubName_Lock"] = 90,
    ["Activity_IsJoinTheEasyTeam"] = 90,
    ["Activity_Easy_InTeamtips"] = 90,
    ["Activity_ShareTitle"] = 90,
    ["Activity_ShareSubTitle"] = 90,
    ["Activity_ShareFriendTitle"] = 90,
    ["Activity_ShareSubFriendTitle"] = 90,
    ["Activity_ShareSubTitleOk"] = 90,
    ["Activity_ShareSubFriendTitleOk"] = 90,
    ["UI_Unable_Go_CallBackToLobby"] = 90,
    ["Team_PST_House"] = 90,
    ["UI_WishingTree_CheckChoose"] = 90,
    ["Activity_GoFishing_Catties"] = 90,
    ["Activity_GoFishing_TimeBucket"] = 90,
    ["UI_QQAuthorizationRuleText"] = 90,
    ["UI_QQAuthorizationRejectText"] = 90,
    ["UI_QQAuthorizationFriendNumText"] = 90,
    ["DrawReward_MidAutumnText"] = 90,
    ["Player_Suit"] = 90,
    ["Player_Vehicle"] = 90,
    ["Recharge_LuckyTurntable_BtnOpen"] = 90,
    ["Recharge_LuckyTurntable_LockTips"] = 90,
    ["Activity_NationalDayHundredDraw_Tip"] = 90,
    ["Common_Exit"] = 90,
    ["Common_BackToAuthorization"] = 90,
    ["TeamShow_ExitConfirmTip"] = 90,
    ["TeamShow_KeepTeamTip"] = 90,
    ["TeamShow_ExitTeamTip"] = 90,
    ["GoToWXSubscription"] = 90,
    ["OpenWXSubscription"] = 90,
    ["Mall_MiniGameIosMoneyDeficitNotice"] = 90,
    ["Mall_DownloadApp"] = 91,
    ["Mall_MiniGameIosNotifyNotice"] = 91,
    ["Mall_WaitNotice"] = 91,
    ["Mall_RightNowNotice"] = 91,
    ["Mall_AskForGift_Notice_IOS"] = 91,
    ["Activity_WolfKillTeam_NoPlayerTip"] = 91,
    ["Activity_WolfKillTeam_SentInviteTip"] = 91,
    ["Activity_WolfKillTeam_JoinedOtherTip"] = 91,
    ["Activity_WolfKillTeam_JoinedThisTip"] = 91,
    ["Activity_WolfKillTeam_FullTip"] = 91,
    ["Activity_WolfKillTeam_JoinSucessTip"] = 91,
    ["Activity_WolfKillTeam_Invite"] = 91,
    ["Activity_WolfKillTeam_AskInvite"] = 91,
    ["Activity_WolfKillTeam_AskJoin"] = 91,
    ["Activity_WolfKillTeam_Chat_JoinNoSender"] = 91,
    ["Activity_WolfKillTeam_Chat_JoinWithSender"] = 91,
    ["Activity_WolfKillTeam_InvalidInvate"] = 91,
    ["Activity_WolfKillTeam_SentInviteToClubTip"] = 91,
    ["Activity_WolfKillTeam_SentInviteToWorldTip"] = 91,
    ["Activity_WolfKillTeam_SentInviteToLobbyTip"] = 91,
    ["Activity_WolfKillTeam_TeamNotFormedTip"] = 91,
    ["Activity_WolfKillTeam_InvalidTeamTip"] = 91,
    ["Activity_WolfKillTeam_ActivityTip"] = 91,
    ["UI_NationalDayWarm_SignSuccess"] = 91,
    ["NewChat_Arena_NotSetting"] = 91,
    ["NewChat_RemoveInterestChannel"] = 91,
    ["NewChat_StickChannel"] = 91,
    ["NewChat_CancelStickChannel"] = 91,
    ["NewChat_Channel_JoinSuccess"] = 91,
    ["NewChat_Channel_StickSuccess"] = 91,
    ["NewChat_Channel_CancelStickSuccess"] = 91,
    ["NewChat_Channel_SelfTag"] = 91,
    ["UI_CostumeBall_Tips"] = 91,
    ["UI_ClothShow_Share"] = 91,
    ["UI_WeekendLuckyStar_Tips"] = 91,
    ["UI_WeekendLuckyStar_ButtonText"] = 91,
    ["UI_Fixed_Success"] = 91,
    ["UI_Fixed_Fail"] = 91,
    ["UI_GetAllClothNum"] = 91,
    ["Friend_QQNewGuideTip"] = 91,
    ["Friend_WXNewGuideTip"] = 91,
    ["UI_ScoringSystemOptionOther"] = 91,
    ["Bag_ItemTypeSort"] = 91,
    ["Bag_ItemTimeSort"] = 91,
    ["Bag_FavoriteOk"] = 91,
    ["Bag_FavoriteBtnFalse"] = 91,
    ["Bag_Tips_AddFavorite"] = 91,
    ["Bag_Tips_RemoveFavorite"] = 91,
    ["Bag_Tips_SettingActionLoopSuccess"] = 91,
    ["Bag_Tips_SettingActionNoLoopSuccess"] = 91,
    ["Calendar_Permission_Tips"] = 91,
    ["DrawReward_NationalDayDrawConfirm"] = 91,
    ["UI_ScoringSystemExit"] = 91,
    ["UI_IllustrationScoringSystemLockTip"] = 91,
    ["UI_ScoringSystemLevelLockTip"] = 91,
    ["UI_ScoringSystemNotInputTip"] = 91,
    ["UI_ScoringSystemInputText"] = 91,
    ["TeamShow_GotoInviteCloseTip"] = 91,
    ["TeamShow_GotoCloseTip"] = 91,
    ["TeamShow_InviteCloseTip"] = 91,
    ["TeamShow_PlayerCloseTip"] = 91,
    ["Bag_Tips_ActionLoopCanNotSetting"] = 91,
    ["UI_Recharge_OverExchangeLimit"] = 91,
    ["Mall_CollectionTitieName"] = 91,
    ["System_LuckyStar_ActivityIsDone"] = 91,
    ["Bag_Favorite_Empty"] = 91,
    ["Bag_MyFavoriteTitle"] = 91,
    ["Bag_NormalTitle"] = 91,
    ["Bag_BackNormalTitle"] = 91,
    ["UI_Team_Invitetxt"] = 91,
    ["TeamShow_MatchNoPlayTip"] = 91,
    ["Recharge_LuckyFree_StatisticEndTime"] = 91,
    ["Recharge_LuckyFree_BtnWinners"] = 91,
    ["Recharge_LuckyFree_PrizeCD"] = 91,
    ["Recharge_LuckyFree_Prizeing"] = 91,
    ["Recharge_LuckyFree_PrizeResult"] = 91,
    ["Recharge_LuckyFree_RewardTips"] = 91,
    ["QRCode_ClubName"] = 91,
    ["QRCode_RoomName"] = 91,
    ["UI_NoFindPlayer"] = 91,
    ["Mall_Selling"] = 91,
    ["Mall_SaleTimeHM"] = 91,
    ["Mall_SaleTimeHMRange"] = 91,
    ["Mall_SaleTimeAfterDay"] = 91,
    ["ActivityLottery_CriticalTips"] = 91,
    ["DrawReward_MallViewNotInBuyTimeRange"] = 91,
    ["DrawReward_MallNextFreeTips"] = 91,
    ["DrawReward_NotOpenTip"] = 91,
    ["Bag_HaveOriginalColorItem"] = 91,
    ["UI_SETTING_ChangeNameConfirmStrContent"] = 91,
    ["UI_SETTING_ChangeNameConfirmBtnContent"] = 91,
    ["UI_Activity_WerewolfDart_GetCount"] = 91,
    ["UI_Activity_WerewolfDart_DrawOnce"] = 91,
    ["UI_Activity_WerewolfDart_Tips"] = 91,
    ["UILobbyChooseMatchTips"] = 91,
    ["UIWXLobbyChooseMatchTips"] = 91,
    ["UI_RankAchieve"] = 91,
    ["UI_RecruitItem_Title_StarWorld"] = 91,
    ["UI_RecruitItem_Title_RankModeSuffix"] = 91,
    ["UGC_PublishMap_HasNoDestination"] = 91,
    ["UGC_Cannot_Publish_NoAt_First_Layer"] = 92,
    ["Report_tab_Recommand"] = 92,
    ["Report_tab_Collect"] = 92,
    ["CaptureShadowFinalRewardCanGet"] = 92,
    ["CaptureShadowNextCaptureRewardShow"] = 92,
    ["CaptureShadowEndCaptureRewardShow"] = 92,
    ["CaptureShadowRewardTitle"] = 92,
    ["UI_RankTag_ArenaHeroRule"] = 92,
    ["UI_RankTag_ArenaHeroDesc"] = 92,
    ["UI_Rank_DownLoadTips"] = 92,
    ["NoCaptureRewardCanGet"] = 92,
    ["NewShadowAppear"] = 92,
    ["Activity_GoFishing_ShareDesc"] = 92,
    ["UI_Intimate_DoubleTips"] = 92,
    ["LFT_GoFishing"] = 92,
    ["UseChanceInPray"] = 92,
    ["UI_GreenHouse_RecordItemText"] = 92,
    ["UI_GreenHouseNew_RecordItemTextRecall"] = 92,
    ["UI_GreenHouseNew_RecordItemTextGift"] = 92,
    ["CaptureShadowClickedCoin"] = 92,
    ["UI_NoCanEnterRoom"] = 92,
    ["UI_GreenHouseNew_Result_Normal_1"] = 92,
    ["UI_GreenHouseNew_Result_Normal_2"] = 92,
    ["UI_GreenHouseNew_Result_Normal_3"] = 92,
    ["UI_GreenHouseNew_Result_Normal_1_Uid"] = 92,
    ["UI_GreenHouseNew_Result_Normal_2_Uid"] = 92,
    ["UI_GreenHouseNew_Result_Normal_3_Uid"] = 92,
    ["UI_GreenHouseNew_Result_Normal_Btn"] = 92,
    ["UI_GreenHouseNew_Result_New"] = 92,
    ["UI_GreenHouseNew_Result_New_Uid"] = 92,
    ["UI_GreenHouseNew_Result_New_Btn"] = 92,
    ["UI_GreenHouseNew_Result_Smurf"] = 92,
    ["UI_GreenHouseNew_Result_Smurf_Uid"] = 92,
    ["UI_GreenHouseNew_Result_Black"] = 92,
    ["UI_GreenHouseNew_Result_Already"] = 92,
    ["UI_GreenHouseNew_Result_Limit"] = 92,
    ["UI_GreenHouseNew_Result_End"] = 92,
    ["UI_GreenHouseNew_Result_Error"] = 92,
    ["UI_GreenHouseNew_Result_Same"] = 92,
    ["UI_GreenHouseNew_FarmVisitDisableTip"] = 92,
    ["UI_GreenHouseNew_RecallNotPlatTip"] = 92,
    ["UI_GreenHouseNew_RecallCDTip"] = 92,
    ["UI_Share_Gift_HideSender"] = 92,
    ["UI_Share_Gift_HideOwner"] = 92,
    ["UI_GreenHouseNew_GiftNoFarm"] = 92,
    ["Report_ModelName"] = 92,
    ["Lottery_BtnText_FreeAppend"] = 92,
    ["Lottery_BtnText_ProtectAppend"] = 92,
    ["Lottery_BtnText_EnableAppend"] = 92,
    ["Lottery_CollectConfirm"] = 92,
    ["Lottery_FreeConfirm"] = 92,
    ["Lottery_FreeAppendFail01"] = 92,
    ["Lottery_FreeAppendFail02"] = 92,
    ["Lottery_ProtectAppendFail"] = 92,
    ["Lottery_ProtectAppendTips"] = 92,
    ["Lobby_StrangerGrabLimit"] = 92,
    ["CDKEY_No_Auth"] = 92,
    ["CDKEY_No_InTime"] = 92,
    ["CDKEY_No_Valid"] = 92,
    ["CDKEY_Is_Used"] = 92,
    ["CDKEY_No_Exist"] = 92,
    ["CDKEY_Use_Limit"] = 92,
    ["CDKEY_Already_Used"] = 92,
    ["CDKEY_Failed"] = 92,
    ["CDKEY_Use_Suncess"] = 92,
    ["UI_UGC_MapVersion_Abort"] = 92,
    ["UI_UGC_MapVersion_PassButTips"] = 92,
    ["UGC_NPCEditor_InteractionInfoName"] = 92,
    ["Calender_RunMonth"] = 92,
    ["UGC_SKILL_CANNOT_LOOP_SKILL"] = 92,
    ["UGC_RoomList_GoodMarks"] = 92,
    ["UGC_RoomList_PassMarks"] = 92,
    ["TDM_FastRoomList_EditTextBoxTabName"] = 92,
    ["TDM_FastRoomList_CreateRoomTabName"] = 92,
    ["TDM_Room_SettingTabName"] = 92,
    ["TDM_Room_ChatTabName"] = 92,
    ["TDM_Room_RecruitTabName"] = 92,
    ["UI_Competition_InLevelStartHint"] = 92,
    ["UI_Competition_InLevelStartHint1"] = 92,
    ["UI_Competition_InLevelStartHint2"] = 92,
    ["UI_Competition_InLevelStartHint3"] = 92,
    ["UI_Competition_InLevelStartHint4"] = 92,
    ["UI_Competition_InLevelStartHint5"] = 92,
    ["UI_Competition_InLevelStartHint6"] = 92,
    ["UI_Competition_InLevelStartHint7"] = 92,
    ["UI_Competition_InLevelStartHint8"] = 92,
    ["UI_Competition_InLevelStartHint9"] = 92,
    ["UI_Competition_Main"] = 92,
    ["UI_Competition_Main1"] = 92,
    ["UI_Competition_Main2"] = 92,
    ["UI_Competition_Main3"] = 92,
    ["UI_Competition_Main4"] = 92,
    ["UI_Competition_Main5"] = 92,
    ["UI_Competition_Main6"] = 92,
    ["UI_Competition_Main7"] = 92,
    ["UI_Competition_Main8"] = 92,
    ["UI_Competition_Main9"] = 92,
    ["UI_Competition_Main10"] = 92,
    ["UI_Competition_Main11"] = 92,
    ["UI_Competition_Main12"] = 92,
    ["UI_Competition_Main13"] = 93,
    ["UI_Competition_Main14"] = 93,
    ["UI_Competition_Main15"] = 93,
    ["UI_Competition_Main16"] = 93,
    ["UI_Competition_Main17"] = 93,
    ["UI_Competition_Main18"] = 93,
    ["UI_Competition_Main19"] = 93,
    ["UI_Competition_Main20"] = 93,
    ["UI_Competition_Main21"] = 93,
    ["UI_Competition_Main22"] = 93,
    ["UI_Competition_Main23"] = 93,
    ["UI_Competition_Main24"] = 93,
    ["UI_Competition_Main25"] = 93,
    ["UI_Competition_Main26"] = 93,
    ["UI_Competition_Main27"] = 93,
    ["UI_Competition_Main28"] = 93,
    ["UI_Competition_Main29"] = 93,
    ["UI_Competition_Main30"] = 93,
    ["UI_Competition_Main31"] = 93,
    ["UI_Competition_Main32"] = 93,
    ["UI_Competition_Main33"] = 93,
    ["UI_Competition_Main34"] = 93,
    ["UI_Competition_Main35"] = 93,
    ["UI_Competition_Main36"] = 93,
    ["UI_Competition_Main37"] = 93,
    ["UI_Competition_Main38"] = 93,
    ["UI_Competition_Main39"] = 93,
    ["UI_Competition_Main40"] = 93,
    ["UI_Competition_Main41"] = 93,
    ["UI_Competition_Main42"] = 93,
    ["UI_Competition_Main43"] = 93,
    ["UI_Competition_Main44"] = 93,
    ["UI_Competition_Main45"] = 93,
    ["UI_Competition_Main46"] = 93,
    ["UI_Competition_Main47"] = 93,
    ["UI_Competition_Main48"] = 93,
    ["UI_Competition_Main49"] = 93,
    ["UI_Competition_Main50"] = 93,
    ["UI_Competition_Main51"] = 93,
    ["UI_Competition_Main52"] = 93,
    ["UI_Competition_InLevel_Reconnect"] = 93,
    ["UI_Competition_InLevel_Reconnect1"] = 93,
    ["UI_Competition_InLevel_Reconnect2"] = 93,
    ["UI_Competition_InLevelRank"] = 93,
    ["UI_Competition_InLevelRank1"] = 93,
    ["UI_Competition_InLevelRank2"] = 93,
    ["UI_Competition_InLevelRank3"] = 93,
    ["UI_Competition_InLevelRank4"] = 93,
    ["UI_Competition_InLevelRank5"] = 93,
    ["UI_Competition_InLevelRank6"] = 93,
    ["UI_Competition_InLevelFinalAccount"] = 93,
    ["UI_Competition_InLevelFinalAccount1"] = 93,
    ["UI_Competition_InLevelFinalAccount2"] = 93,
    ["UI_Competition_InLevelFinalAccount3"] = 93,
    ["UI_Competition_InLevelFinalAccount4"] = 93,
    ["UI_Competition_InLevelFinalAccount5"] = 93,
    ["UI_Competition_InLevelFinalAccount6"] = 93,
    ["UI_Competition_InLevelFinalAccount7"] = 93,
    ["UI_Competition_InLevelFinalAccount8"] = 93,
    ["UI_Competition_InLevelFinalAccount9"] = 93,
    ["UI_Competition_InLevelFinalAccount10"] = 93,
    ["UI_Competition_InLevelFinalAccount11"] = 93,
    ["UI_Competition_InLevelFinalAccount12"] = 93,
    ["UI_Competition_InLevelFinalAccount13"] = 93,
    ["UI_Competition_InLevelFinalAccount14"] = 93,
    ["UI_Competition_RoomInfo"] = 93,
    ["UI_Competition_RoomInfo1"] = 93,
    ["UI_Competition_RoomInfo2"] = 93,
    ["UI_Competition_RoomInfo3"] = 93,
    ["UI_Competition_RoomInfo4"] = 93,
    ["UI_Competition_RoomInfo5"] = 93,
    ["UI_Competition_RoomInfo6"] = 93,
    ["UI_Competition_RoomInfo7"] = 93,
    ["UI_Competition_RoomInfo8"] = 93,
    ["UI_Competition_RoomInfo9"] = 93,
    ["UI_Competition_RoomInfo10"] = 93,
    ["UI_Competition_Integral_Award"] = 93,
    ["UI_Competition_Integral_Award1"] = 93,
    ["UI_Competition_Integral_Award2"] = 93,
    ["UI_Competition_GroupPanel"] = 93,
    ["UI_Competition_EliminationListView"] = 93,
    ["UI_Competition_EliminationListView1"] = 93,
    ["UI_Competition_EliminationListView2"] = 93,
    ["UI_Competition_EliminationListView3"] = 93,
    ["UI_Competition_Integral"] = 93,
    ["UI_Competition_Integral1"] = 93,
    ["UI_Competition_Integral2"] = 93,
    ["UI_Competition_Integral3"] = 93,
    ["UI_Competition_Integral4"] = 93,
    ["UI_Competition_InLevelStageHint"] = 93,
    ["UI_Competition_InLevelStageHint1"] = 93,
    ["UI_Competition_InLevelStageHint2"] = 93,
    ["UI_Competition_InLevelStageHint3"] = 93,
    ["UI_Competition_ExplainMain_title1"] = 93,
    ["UI_Competition_ExplainMain_title2"] = 93,
    ["UI_Competition_ExplainMain_title3"] = 93,
    ["UI_Competition_ExplainMain_title4"] = 93,
    ["UI_Competition_ExplainMain_title5"] = 93,
    ["UI_Competition_ExplainMain_title6"] = 93,
    ["UI_Competition_ExplainMain_title7"] = 93,
    ["UI_Competition_ExplainMain_content1"] = 94,
    ["UI_Competition_ExplainMain_content2"] = 94,
    ["UI_Competition_ExplainMain_content3"] = 94,
    ["UI_Competition_ExplainMain_content4"] = 94,
    ["UI_Competition_ExplainMain_content5"] = 94,
    ["UI_Competition_ExplainMain_content6"] = 94,
    ["UI_Competition_ExplainMain_content7"] = 94,
    ["UI_Competition_ExplainMain_content8"] = 94,
    ["UI_Competition_ExplainMain_content9"] = 94,
    ["UI_Competition_ExplainMain_content10"] = 94,
    ["UI_Competition_ExplainMain_content11"] = 94,
    ["UI_Competition_ExplainMain_content12"] = 94,
    ["UI_Competition_ExplainMain_content13"] = 94,
    ["UI_Competition_ExplainMain_content14"] = 94,
    ["UI_Competition_ExplainMain_content15"] = 94,
    ["UI_Competition_ExplainMain_content16"] = 94,
    ["UI_Competition_ExplainMain_content17"] = 94,
    ["UI_Competition_ExplainMain_content18"] = 94,
    ["UI_Competition_ExplainMain_content19"] = 94,
    ["UI_Competition_ExplainMain_content20"] = 94,
    ["UI_Competition_ExplainMain_content21"] = 94,
    ["NR3E_EndReason_103_Tips"] = 94,
    ["NR3E_EndReason"] = 94,
    ["MeetingActionName9"] = 94,
    ["UI_RedPacket_ActivityName"] = 94,
    ["UI_Activity_OpenPacket_OneParam"] = 94,
    ["UI_BtnName_AllCash"] = 94,
    ["UI_RedPacket_ActivityDes"] = 94,
    ["UI_RedPacket_CurrTimes"] = 94,
    ["UI_RedPacket_CurrTimesDes"] = 94,
    ["UI_RedPacket_NextTimesDes"] = 94,
    ["UI_RedPacket_TimesTh"] = 94,
    ["UI_RedPacket_Record"] = 94,
    ["UI_RedPacket_OnGoing"] = 94,
    ["UI_RedPacket_Begin"] = 94,
    ["UI_RedPacket_History"] = 94,
    ["UI_RedPacket_Cash"] = 94,
    ["UI_RedPacket_CurrStage_TwoParam"] = 94,
    ["UI_RedPacket_GetReward_OneParam"] = 94,
    ["UI_RedPacket_Season_Left"] = 94,
    ["Rank_ResourceDownLoadTips"] = 94,
    ["UI_Setting_PopLink_Desc"] = 94,
    ["SettingTip_WeChatGameDynamic"] = 94,
    ["Login_Overseas_Agreement"] = 94,
    ["TimeUtils_YMD"] = 94,
    ["Activity_Exchange_PlayerReturn_ConfirmGift"] = 94,
    ["Activity_Exchange_PlayerReturn_CurGiftNum"] = 94,
    ["Common_Quit"] = 94,
    ["Common_Reconsider"] = 94,
    ["Common_Revocation"] = 94,
    ["Common_OverseaLoginSuccess"] = 94,
    ["Common_ActivityNotOpen"] = 94,
    ["Common_Sec_NoParam"] = 94,
    ["Common_GetOver"] = 94,
    ["Common_WaitGet"] = 94,
    ["Net_CommunityLevelTypeError"] = 94,
    ["UGC_ErrorMsg_CanNotAsTemplateInGroup"] = 94,
    ["Common_CopyToClipboard"] = 94,
    ["Permission_LocalNotify_Failed"] = 94,
    ["UGC_Resource_Delete_Confirm"] = 94,
    ["UGC_Shop_Goods_NotExist"] = 94,
    ["UI_ConvenedRecordItemInviteOnlineFaile_NotPlatFriend"] = 94,
    ["UI_LuckyWeekendValueText"] = 94,
    ["CustomRoom_PinJoinFailed"] = 94,
    ["UGC_OMD_Crystal_NumCheck_Max_Tips"] = 94,
    ["Tricycle_No_Place_For_Tricycle"] = 94,
    ["Tricycle_Spawn_Failed"] = 94,
    ["Tricycle_Too_Many"] = 94,
    ["GiftCard_Thanks_Tips"] = 94,
    ["UI_Nongchangyanglvzhi_Noticket_tips"] = 94,
    ["Calendar_Not_Support_Platform"] = 94,
    ["Login_SystemNotice_Title"] = 94,
    ["Login_SystemNotice_Content"] = 94,
    ["FriendShipFire_GetFire"] = 94,
    ["FriendShipFire_FriendList"] = 94,
    ["Share_PC_NotSupport_Tips"] = 94,
    ["PC_NotSupport_Recharget_Tips"] = 94,
    ["UI_UnlockAdvanceTab_Text"] = 94,
    ["UI_Season_UnlockAdvancedPass_Txt"] = 94,
    ["UI_Season_UpgradeReceive_Txt"] = 94,
    ["UI_Season_LotteryReceive_Txt"] = 94,
    ["UI_Season_LotteryReceiveHigh_Txt"] = 94,
    ["UI_Season_Redeemablerewards_Txt"] = 94,
    ["UI_Season_EarnCrownCoinsPass_Txt"] = 94,
    ["UI_Season_RankedMatch_Txt"] = 94,
    ["CustomRoom_ExchangeSeatFail_WaitCd"] = 94,
    ["UI_WerewolfGonzoDetective_ResetInfo"] = 94,
    ["Lottery_OwnSelect_Check"] = 94,
    ["Lottery_OwnSelect_Replace"] = 94,
    ["Lottery_OwnSelect_rule"] = 94,
    ["FriendShipFire_ReturnList"] = 94,
    ["CaptureShadowTipShaodwHasBuffer"] = 94,
    ["QQAuthority_Title"] = 94,
    ["UI_GreenHouseNew_LowVerNotSupport"] = 94,
    ["UI_PC_PasswordRoom_Not_Support_Hint"] = 94,
    ["UI_ConanActivityClose"] = 94,
    ["UI_WishList_CantAddWishMaxTips"] = 94,
    ["UI_WishList_FollowSuccess"] = 94,
    ["UI_WishList_UnfollowSucces"] = 94,
    ["UI_WishList_AddWishSuccess"] = 94,
    ["UI_WishList_RemoveWishTips"] = 95,
    ["UI_WishList_RemoveWishSuccess"] = 95,
    ["UI_WishList_CantGiftWish"] = 95,
    ["UI_RankTag_AchieveRule"] = 95,
    ["UI_RankTag_AchieveDesc"] = 95,
    ["Player_FaceOrnament"] = 95,
    ["Player_HeadWear"] = 95,
    ["Player_BackOrnament"] = 95,
    ["Player_HandOrnament"] = 95,
    ["NewChat_SurpassFriend"] = 95,
    ["RedEnvelop_NoThanksToSelf"] = 95,
    ["UI_Season_RankedFeatureMatch_Txt"] = 95,
    ["MaxNum_OneTime_Operation"] = 95,
    ["Lottery_LoversSubView_Gift"] = 95,
    ["AppearanceFashion_RoadLevel_Upgrade"] = 95,
    ["UI_SurpassFriend_ShowedRecord"] = 95,
    ["UI_Share_PreviewCardName"] = 95,
    ["UI_Share_PreviewCardProgress"] = 95,
    ["Team_MemberCountOverMaxTip"] = 95,
    ["AppearanceFashion_RoadLevel_Upgrade_1"] = 95,
    ["UI_WishList_GiftTips"] = 95,
    ["UI_Birthday_Set"] = 95,
    ["UI_Birthday_Record"] = 95,
    ["UI_Birthday_AllPlayer"] = 95,
    ["UI_Birthday_AllFriend"] = 95,
    ["UI_Birthday_IntimateFriend"] = 95,
    ["UI_Birthday_OnlySelf"] = 95,
    ["UI_Birthday_SetDayConfirm"] = 95,
    ["UI_Birthday_SetDayTips"] = 95,
    ["UI_Birthday_FirstTips"] = 95,
    ["UI_Birthday_SendCard"] = 95,
    ["UI_Birthday_MyHoney"] = 95,
    ["UI_Birthday_Yours"] = 95,
    ["UI_Birthday_SendTips"] = 95,
    ["UI_Birthday_AlreadySend"] = 95,
    ["Cup_DailyTaskHint1"] = 95,
    ["Cup_DailyTaskHint2"] = 95,
    ["UI_CardSystem_SendGiveSuccess"] = 95,
    ["UI_CardSystem_SendAskSuccess"] = 95,
    ["UI_CardSystem_SendExchangeSuccess"] = 95,
    ["CustomRoom_InvalidPlatformTips"] = 95,
    ["NewChat_ChatTypeForbid"] = 95,
    ["RedEnvelop_ThankedTip"] = 95,
    ["UI_Birthday_SendSucceedNow"] = 95,
    ["UI_Birthday_SendSucceedLatter"] = 95,
    ["UI_Birthday_SendSucceedEnd"] = 95,
    ["UI_Birthday_ChangeSucceed"] = 95,
    ["UI_Birthday_ChangeTip"] = 95,
    ["UI_Birthday_SendCardTip"] = 95,
    ["UI_Birthday_TimeEnd"] = 95,
    ["UI_Birthday_SendCardTimeEnd"] = 95,
    ["UI_Birthday_SendCardMaxNum"] = 95,
    ["UI_Birthday_NotFriend"] = 95,
    ["UI_Birthday_NotShow"] = 95,
    ["UI_Birthday_NotInTime"] = 95,
    ["UI_Birthday_MoreTime"] = 95,
    ["UI_Birthday_RemoveCard"] = 95,
    ["JumpToast_common"] = 95,
    ["UI_Birthday_ActivityContent"] = 95,
    ["AppearanceFashion_RoadLevel_Upgrade_2"] = 95,
    ["UI_LoginReminders_Success"] = 95,
    ["UI_LoginReminders_Fail"] = 95,
    ["Task_ActivityEnded_InPublicity"] = 95,
    ["UI_Dance_Start"] = 95,
    ["UI_Dance_End"] = 95,
    ["UI_PlayerInfo_ChangeInvite"] = 95,
    ["UI_Friend_ChangeIntimacy"] = 95,
    ["UI_Lightning_LevelInfo"] = 95,
    ["UI_Lightning_EventList"] = 95,
    ["UI_Lightning_Blessing"] = 95,
    ["UI_WishList_SelfLevelLimit_Tips"] = 95,
    ["UI_WishList_OtherLevelLimit_Tips"] = 95,
    ["UI_WishList_GiftLevelLimit_Tips"] = 95,
    ["MatchPreparation_BPLevel"] = 95,
    ["UI_FarmBlessingTeam_SingleTask"] = 95,
    ["UI_FarmBlessingTeam_TeamTask"] = 95,
    ["UI_FarmBlessingTeam_RecommendList"] = 95,
    ["UI_FarmBlessingTeam_FriendList"] = 95,
    ["CommonModeJump"] = 95,
    ["AppearanceFashion_EmptyBag"] = 95,
    ["AppearanceFashion_UnExist"] = 95,
    ["HotkeyChangeKeyStroke"] = 95,
    ["HotkeyConflictModifications"] = 95,
    ["HotkeylsOccupied"] = 95,
    ["HotkeyNoMoreReminders"] = 95,
    ["WXCollectAddFailed"] = 95,
    ["WXCollectAddSuccess"] = 95,
    ["UI_ReSign_Tips_Content"] = 95,
    ["UI_ReSign_Tips_Title"] = 95,
    ["MGP_CannotChangeModeInMatch"] = 95,
    ["UI_Activity_WerewolfShowdown_RewardTips"] = 95,
    ["UI_Activity_WerewolfShowdown_SelectTips"] = 95,
    ["vaCollectErrorLowQQVersion"] = 95,
    ["knockoutTips1"] = 95,
    ["knockoutTips2"] = 95,
    ["knockoutTips3"] = 95,
    ["UI_Lottery_NewYearCourtyard_MainView_1"] = 95,
    ["InValidTimeToStartGame"] = 95,
    ["UI_FarmBlessingTeam_Tips1"] = 95,
    ["UI_FarmBlessingTeam_Tips2"] = 95,
    ["UI_Lottery_NewYearCourtyard_MainView_2"] = 96,
    ["UI_Birthday_CardItemDesc"] = 96,
    ["Team_PST_Offline2"] = 96,
    ["Team_PST_OfflineStartGameTip1"] = 96,
    ["Team_PST_OfflineStartGameTip2"] = 96,
    ["UI_Birthday_SelectSendNow"] = 96,
    ["UI_Birthday_SelectSendBefore"] = 96,
    ["UI_FarmBlessingTeam_ChatDesc"] = 96,
    ["UI_TeamInviteLaterDispose"] = 96,
    ["UI_TeamInviteGive"] = 96,
    ["UI_TeamInviteExchange"] = 96,
    ["UI_Birthday_MakeCardTips"] = 96,
    ["UI_Birthday_BirthdayCardThanks"] = 96,
    ["UI_Birthday_ChangeBirthdayHelp"] = 96,
    ["UI_Birthday_ChatSendCard"] = 96,
    ["NewChat_AICommentary"] = 96,
    ["UI_PlayerInfo_ChangeInviteTip"] = 96,
    ["ShareGift_Cut_Button_Title"] = 96,
    ["ShareGift_Record_Left_Btn1"] = 96,
    ["ShareGift_Record_Left_Btn2"] = 96,
    ["ShareGift_Record_Top_Des_Share"] = 96,
    ["ShareGift_Record_Top_Des_Get"] = 96,
    ["ShareGift_Record_Content_Item_Time"] = 96,
    ["ShareGift_Record_Content_Item_Num"] = 96,
    ["ShareGift_Record_Content_Item_No"] = 96,
    ["ShareGift_Record_Content_Btn_Name"] = 96,
    ["ShareGift_Record_Content_Item_Get_Time"] = 96,
    ["ShareGift_Record_Content_Time_No"] = 96,
    ["ShareGift_Record_Title"] = 96,
    ["ShareGift_Get_Title"] = 96,
    ["UI_ClearingAwards_Back"] = 96,
    ["UI_FashionFund_BuyTips"] = 96,
    ["ShareGift_Time_No"] = 96,
    ["ShareGift_Item_No"] = 96,
    ["ShareGift_Item_Got"] = 96,
    ["ShareGift_Player_No"] = 96,
    ["UI_Birthday_ComeFrom"] = 96,
    ["ShareGift_Record_Top_Des_Share0"] = 96,
    ["ShareGift_Record_Top_Des_Share1"] = 96,
    ["ShareGift_Record_Top_Des_Share2"] = 96,
    ["ShareGift_Record_Top_Des_Share3"] = 96,
    ["ShareGift_Record_Top_Des_Share4"] = 96,
    ["ShareGift_Record_Top_Des_Share5"] = 96,
    ["ShareGift_Record_Top_Des_Share6"] = 96,
    ["ShareGift_Record_Top_Des_Get0"] = 96,
    ["ShareGift_Record_Top_Des_Get1"] = 96,
    ["ShareGift_Record_Top_Des_Get2"] = 96,
    ["ShareGift_Record_Top_Des_Get3"] = 96,
    ["ShareGift_Record_Top_Des_Get4"] = 96,
    ["ShareGift_Record_Top_Des_Get5"] = 96,
    ["ShareGift_Record_Top_Des_Get6"] = 96,
    ["Team_PST_HUD"] = 96,
    ["UI_FarmBlessingTeam_ShareChatDesc"] = 96,
    ["Team_WXMemberReminder"] = 96,
    ["Team_QQMemberReminder"] = 96,
    ["UI_Birthday_ChangeBirthdayVis"] = 96,
    ["UI_Birthday_ChatSendCardMy"] = 96,
    ["UI_Birthday_CanNotLook"] = 96,
    ["UI_Birthday_SendCardNow"] = 96,
    ["ShareGift_NewChat_ShareTitle"] = 96,
    ["ShareGift_Click_Share_No_Num_Tip1"] = 96,
    ["ShareGift_Click_Share_No_Num_Tip2"] = 96,
    ["ShareGift_Click_Share_No_Num_Tip3"] = 96,
    ["ShareGift_Click_get_No_Num_Tip1"] = 96,
    ["ShareGift_Click_get_No_Num_Tip2"] = 96,
    ["ShareGift_Click_get_No_Num_Tip3"] = 96,
    ["ShareGift_Record_No_Share_Des"] = 96,
    ["ShareGift_Record_No_Get_Des"] = 96,
    ["UI_Collection_NotificationPopup"] = 96,
    ["UI_Collection_ReceiveCount"] = 96,
    ["UI_Collection_MoreTip"] = 96,
    ["UI_Collection_CurrentTip"] = 96,
    ["UI_Collection_TotalTip"] = 96,
    ["ShareGift_Record_Rob_No"] = 96,
    ["ShareGift_Record_Rob_Minutes_No"] = 96,
    ["ShareGift_Record_Rob_Second_No"] = 96,
    ["UI_Birthday_CardDelete"] = 96,
    ["NewChat_ShareGift_ShowFormat"] = 96,
    ["Share_Gift_Record_Get_Tag"] = 96,
    ["ShareGift_UseItemFailed"] = 96,
    ["UI_Birthday_CardNotSend"] = 96,
    ["Share_Gift_Main_ShareBtn_Msg"] = 96,
    ["UI_Activity_WerewolfTrap_GetCount"] = 96,
    ["UI_NewChat_ShareGift_Success"] = 96,
    ["ShareGift_Record_Top_Des_Share7"] = 96,
    ["ShareGift_Record_Top_Des_Share8"] = 96,
    ["ShareGift_Record_Top_Des_Get7"] = 96,
    ["ShareGift_Record_Top_Des_Get8"] = 96,
    ["UI_Birthday_MailContent"] = 96,
    ["ShareGift_Click_Share_No_Num_Tip4"] = 96,
    ["ShareGift_Click_get_No_Num_Tip4"] = 96,
    ["UI_Activity_NewYearParty2025_Tips_Fix"] = 96,
    ["Friend_Info_EnterFarm_NotFriend"] = 96,
    ["Friend_Info_EnterFarm_FriendNoFarm"] = 96,
    ["Friend_Info_EnterFarm_YouNoFarm"] = 96,
    ["Friend_Info_EnterFarm_YouFriendBlack"] = 96,
    ["UI_Birthday_SendBlessingSucces"] = 96,
    ["UI_FarmBlessingTeam_NoFarm"] = 96,
    ["UI_ChooseCharacter_ChooseNoSelectTip"] = 96,
    ["UI_NewChat_JoinInterestChannel_First"] = 96,
    ["Accelerating_prompt_use"] = 97,
    ["Accelerating_prompt_lack"] = 97,
    ["UP_ModeSeleclNoMatchInRank"] = 97,
    ["UP_ModeSeleclNoMatchInCasual"] = 97,
    ["Lobby_NoDisturbState_MsgBoxTips"] = 97,
    ["Lobby_NoDisturbState_BubbleTips"] = 97,
    ["Lobby_NoDisturbState_SelfInNoDisturbTip"] = 97,
    ["Lobby_NoDisturbState_TargetInNoDisturbTip"] = 97,
    ["Team_NotInviteOfflineMemberTip"] = 97,
    ["UI_ActivityLottery_BaseView_0"] = 97,
    ["UI_GiftShare_RewardCountLimit"] = 97,
    ["UI_UPTip_OnSideChanged"] = 97,
    ["Cup_DailyTaskHint3"] = 97,
    ["UI_GameType_One"] = 97,
    ["UI_GameType_Two"] = 97,
    ["UI_GameType_Four"] = 97,
    ["TeamMatchSelectTips"] = 97,
    ["Common_National"] = 97,
    ["ThreeRoundTestTip"] = 97,
    ["TemporaryMailboxNotice"] = 97,
    ["PhotoAlbumFullTips1"] = 97,
    ["PhotoAlbumFullTips2"] = 97,
    ["PhotoAlbumFullTips3"] = 97,
    ["PhotoAlbumFullTips4"] = 97,
    ["PhotoAlbumSaveTips"] = 97,
    ["PhotoAlbumTaskTips1"] = 97,
    ["PhotoAlbumTaskTips2"] = 97,
    ["PhotoAlbumTaskTips3"] = 97,
    ["PhotoAlbumEditExitTips"] = 97,
    ["Common_SelfRecord"] = 97,
    ["Leaderboard_Tips_Title_StarWorld_Mix"] = 97,
    ["Leaderboard_Tips_Desc_StarWorld_Mix"] = 97,
    ["UI_RankTag_NR3E3_FinalPoints_Rule"] = 97,
    ["UI_RankTag_NR3E3_FinalPoints_Desc"] = 97,
    ["UI_FinishBreakRecord_AreaRank"] = 97,
    ["UI_FinishBreakRecord_CountryRank"] = 97,
    ["UI_FinishBreakRecord_AreaTitle"] = 97,
    ["UI_FinishBreakRecord_CountryTitle"] = 97,
    ["UI_FinishBreakRecord_CurrentTimeFormat"] = 97,
    ["UI_FinishBreakRecord_SingleModeTitle"] = 97,
    ["UI_Bag_GiveGiftLevelTips"] = 97,
    ["PhotoAlbumHideTips1"] = 97,
    ["PhotoAlbumHideTips2"] = 97,
    ["UI_RankTag_ChaseFinalPointsRule"] = 97,
    ["UI_RankTag_ChaseFinalPointsDesc"] = 97,
    ["UI_RightOrUp_Newbie_Today"] = 97,
    ["UI_RightOrUp_Newbie_Tomorrow"] = 97,
    ["UI_RightOrUp_Newbie_RewardTips"] = 97,
    ["Album_Space_Not_Enough_Multiple"] = 97,
    ["Album_Space_Not_Enough"] = 97,
    ["Album_Save_Fail"] = 97,
    ["Album_Save_Fail_Temporary"] = 97,
    ["Album_TemporaryEmpty_Tips"] = 97,
    ["Album_Friends_Notice"] = 97,
    ["Album_Noticed"] = 97,
    ["Album_Notice_Others"] = 97,
    ["UI_Lottery_ConfirmText"] = 97,
    ["UI_Lottery_Continue"] = 97,
    ["UI_Lottery_Choose"] = 97,
    ["UI_FarmPuzzle_ConfirmTips"] = 97,
    ["UI_FarmPuzzle_GoGetCoin"] = 97,
    ["Team_ConfirmCancelGameTip"] = 97,
    ["Team_ConfirmCancelGameOK"] = 97,
    ["Team_ConfirmCancelGameNO"] = 97,
    ["Album_Remind_ViewPhoto"] = 97,
    ["Album_Remind_OverLimit"] = 97,
    ["Album_Photo_DeleteTips"] = 97,
    ["OneRoundTestTip"] = 97,
    ["UI_ModelSelect_UnFold"] = 97,
    ["UI_ModelSelect_Fold"] = 97,
    ["LFT_UgcCreatorPage"] = 97,
    ["Video_Not_Open_Time"] = 97,
    ["Use_Prop_Item_Title_PackageType_Common"] = 97,
    ["Use_Prop_Item_Title_PackageType_Random"] = 97,
    ["Use_Prop_Item_Title_PackageType_Pick"] = 97,
    ["Team_NotStartReasonLeaderTip"] = 97,
    ["Team_NotStartReasonMemberTip"] = 97,
    ["GiftSharing_GetRecord_Button_Name"] = 97,
    ["Album_VerticalAlign_Top"] = 97,
    ["Album_VerticalAlign_Center"] = 97,
    ["Album_VerticalAlign_Bottom"] = 97,
    ["Album_HorizontalAlign_Left"] = 97,
    ["Album_HorizontalAlign_Center"] = 97,
    ["Album_HorizontalAlign_Right"] = 97,
    ["Album_EditSaveSuccessTips"] = 97,
    ["UI_ChangeMoodState"] = 97,
    ["UI_DoubleFormation_Online"] = 97,
    ["UI_DoubleFormation_Offline"] = 97,
    ["UI_DoubleFormation_ShareTip"] = 97,
    ["UI_DoubleFormation_MakeTip"] = 97,
    ["UI_DoubleFormation_ProgressName"] = 97,
    ["UI_DoubleFormation_TeamFull"] = 97,
    ["UI_DoubleFormation_JoinTeam"] = 97,
    ["UI_DoubleFormation_JoinTeamTip"] = 97,
    ["NRE3Activity_In_ShopTitle"] = 97,
    ["NRE3Activity_In_ShopUpTitle"] = 97,
    ["Mail_Askfor_Activityclosed_Insufficient"] = 97,
    ["Mail_Askfor_Commodity_Unable"] = 97,
    ["Lobby_NoDisturbState_BubbleTips_PassiveInteract"] = 97,
    ["Cup_Bg_Title_Daily"] = 97,
    ["Cup_Bg_Title_Total"] = 98,
    ["UI_GiftSharing_Details_GiftBtns_Title"] = 98,
    ["UI_Gift_dress"] = 98,
    ["shareHint_1"] = 98,
    ["Cup_DailyTaskHint4"] = 98,
    ["UI_MonthlyChallenge_Start"] = 98,
    ["UI_MonthlyChallenge_Complete"] = 98,
    ["UI_MonthlyChallenge_LackOfTime"] = 98,
    ["UI_MonthlyChallenge_Confirm"] = 98,
    ["UI_MonthlyChallenge_Cancel"] = 98,
    ["UI_MonthlyChallenge_Title"] = 98,
    ["UI_MonthlyChallenge_WeeklyTips"] = 98,
    ["UI_MonthlyChallenge_WeekendTips"] = 98,
    ["UI_MonthlyChallenge_WeeklyTimeTips"] = 98,
    ["UI_MonthlyChallenge_WeekendTimeTips"] = 98,
    ["UI_BattlePass_UnlockAdvanced"] = 98,
    ["UI_BattlePass_UnlockDeluxe"] = 98,
    ["Cup_Bg_Title_NormalTaskName"] = 98,
    ["NewChat_MusicConcertPositionShare"] = 98,
    ["NewChat_MusicConcertPositionShareNoSender"] = 98,
    ["TIPS_MOVECANTINTERRUPTSOLOACTION"] = 98,
    ["UI_GameReturnIsClose"] = 98,
    ["Album_PlayMode_Name"] = 98,
    ["Album_PlayMode_StarWorld"] = 98,
    ["Album_PlayMode_ParkHome"] = 98,
    ["Album_PlayMode_StarParty"] = 98,
    ["Album_PlayMode_Tips"] = 98,
    ["Album_StarWorld_Tips"] = 98,
    ["Album_ParkHome_Tips"] = 98,
    ["Album_StarParty_Tips"] = 98,
    ["RecentPlay"] = 98,
    ["Activity_OnlyShowInFarmScene"] = 98,
    ["Team_InviteJoinUGCRoomFromLink"] = 98,
    ["Team_InviteJoinUGCTeamFromLink"] = 98,
    ["friend_normal_only_intimaty"] = 98,
    ["friend_multiplayer_only_intimaty"] = 98,
    ["friend_normal_only_coin"] = 98,
    ["friend_multiplayer_only_coin"] = 98,
    ["photo_setting_item_block"] = 98,
    ["photo_setting_invite_block"] = 98,
    ["SettingTip_ShowKeyOnButtonTip"] = 98,
    ["SettingTip_ShowKeyOnButtonShortcutTip"] = 98,
    ["Team_PST_Cook"] = 98,
    ["VoiceKeyWords_VoiceSendTooShort"] = 98,
    ["VoiceKeyWords_VoiceSendFailed"] = 98,
    ["VoiceKeyWords_EmojiShowSuccess"] = 98,
    ["VoiceKeyWords_Action1PSendSuccess"] = 98,
    ["VoiceKeyWords_Action2PSendFailed"] = 98,
    ["VoiceKeyWords_DoNotHasItem"] = 98,
    ["VoiceKeyWords_Emoji"] = 98,
    ["VoiceKeyWords_Action"] = 98,
    ["VoiceKeyWords_MatchFailed_ForService"] = 98,
    ["VoiceKeyWords_MatchFailed"] = 98,
    ["VoiceKeyWords_Title"] = 98,
    ["VoiceKeyWords_Content"] = 98,
    ["Team_PST_Photo"] = 98,
    ["UI_InflateHongBao_ReceiveTip1"] = 98,
    ["UI_InflateHongBao_ReceiveTip2"] = 98,
    ["UI_InflateHongBao_Insufficient"] = 98,
    ["UI_InflateHongBao_AvailableCount"] = 98,
    ["UI_InflateHongBao_UnlockCount"] = 98,
    ["Cup_DailyTaskHint5"] = 98,
    ["WXGame_NewHud_TaskItemTitle"] = 98,
    ["UI_PlayerInfo_Title"] = 98,
    ["UI_Artist_RewardPopup_TimeText"] = 98,
    ["UI_Artist_RewardPopup_OrginPriceText"] = 98,
    ["UI_Lottery_Artist_HaveCountText"] = 98,
    ["UI_Lottery_Artist_GuaranteeItemText"] = 98,
    ["UI_Artist_RewardItem_Tips"] = 98,
    ["UI_Lottery_Artist_ConfimText"] = 98,
    ["UI_Lottery_Artist_CancelText"] = 98,
    ["UI_Lottery_Artist_StrContentText"] = 98,
    ["UI_Player_BattlePassUnlocked"] = 98,
    ["VoiceKeyWords_StepTipsContent"] = 98,
    ["BattlePass_WereWolfNotUnlocked"] = 98,
    ["UI_SimpleGiftText2"] = 98,
    ["PakDetail_UnDownloadTips"] = 98,
    ["UI_PlayerInfo_QualifyHistory"] = 98,
    ["UI_PlayerInfo_QualifyCur"] = 98,
    ["UI_CannotFind_MySelf"] = 98,
    ["UI_ModelSelect_ScrollUpTips"] = 98,
    ["UI_ModelSelect_ScrollBottomTips"] = 98,
    ["TR_AnyMatchDes"] = 98,
    ["SP_LeaveTeamTip"] = 98,
    ["SP_LeaveRoomTip"] = 98,
    ["SettingTip_Invisible"] = 98,
    ["UI_BeBlockedToBanFriend"] = 98,
    ["UI_IsBanFriend"] = 98,
    ["Activity_LightningMatch_Vote_Content"] = 98,
    ["Activity_LightningMatch_Vote_Success"] = 98,
    ["Activity_LightningMatch_Vote_NotEnoughTips"] = 98,
    ["VIP_Photo_Up"] = 98,
    ["ModelSelect_RecommendRecentTip"] = 98,
    ["UI_Vehicle_Information"] = 98,
    ["UI_Vehicle_Accessories"] = 98,
    ["UI_Vehicle_Save_Accessories"] = 98,
    ["UI_Vehicle_LevelUp_Accessories"] = 98,
    ["UI_Vehicle_Skill_Unlock"] = 98,
    ["UPGameSetting_HideFashionScore"] = 98,
    ["CommodityBuyCondition1"] = 98,
    ["CommodityBuyCondition2"] = 99,
    ["UI_Season_EarnIntimateCoinsPass_Txt"] = 99,
    ["UI_Season_EarnIntimateLevelPass_Txt"] = 99,
    ["UI_MatchPakDetail_DownloadTip"] = 99,
    ["UI_MatchPakDetail_LeftDownloadTime"] = 99,
    ["UI_TYC_Reincarnation_BuildProgress"] = 100,
    ["UI_TYC_Reincarnation_WelfareNum"] = 100,
    ["UI_TYC_Reincarnation_WelfareNumLess"] = 100,
    ["UI_TYC_Reincarnation_FullLevel"] = 100,
    ["UI_TYC_Reincarnation_CurrencyNotEnough"] = 100,
    ["UI_TYC_Reincarnation_BuildingNotFull"] = 100,
    ["UI_TYC_Reincarnation_ConfirmContent"] = 100,
    ["UI_TYC_Reincarnation"] = 100,
    ["UI_TYC_PlayerKillMonster"] = 100,
    ["UI_TYC_BankReceive"] = 100,
    ["UI_TYC_NomalGet"] = 100,
    ["UI_TYC_Wave_Coming"] = 100,
    ["UI_TYC_Killed_Defeat"] = 100,
    ["UI_TYC_Killed_Play"] = 100,
    ["UI_TYC_Killed_Monster"] = 100,
    ["UI_TYC_Wave_Relax"] = 100,
    ["UI_TYC_Weapon_Pistol"] = 100,
    ["UI_TYC_Weapon_MeleeWeapons"] = 100,
    ["UI_TYC_Weapon_SMG"] = 100,
    ["UI_TYC_Weapon_Shotgun"] = 100,
    ["UI_TYC_Weapon_AssaultRifle"] = 100,
    ["UI_TYC_Weapon_LMG"] = 100,
    ["UI_TYC_Weapon_SniperRifle"] = 100,
    ["UI_TYC_Weapon_Launcher"] = 100,
    ["UI_TYC_Weapon_Toy"] = 100,
    ["UI_TYC_Building_Flag"] = 100,
    ["UI_TYC_Building_Ornament"] = 100,
    ["UI_TYC_Building_Ornament2"] = 100,
    ["UI_TYC_Skin_Test_Name_Deserteagle"] = 100,
    ["UI_TYC_Skin_Test_Name_M1897"] = 100,
    ["UI_TYC_Skin_Test_Name_UZI"] = 100,
    ["UI_TYC_Skin_Test_Name_AK47"] = 100,
    ["UI_TYC_Skin_Test_Name_M4"] = 100,
    ["UI_TYC_Skin_Test_Name_SCAR"] = 100,
    ["UI_TYC_Skin_Test_Name_M82"] = 100,
    ["UI_TYC_Skin_Test_Name_M249"] = 100,
    ["UI_TYC_Skin_Test_Name_RPG"] = 100,
    ["UI_TYC_Skin_Test_Name_AMW"] = 100,
    ["UI_TYC_Skin_Test_Name_RPK"] = 100,
    ["UI_TYC_Skin_Test_Name_MP5"] = 100,
    ["UI_TYC_Skin_Test_Name_BaseballBat"] = 100,
    ["UI_TYC_Skin_Test_Name_Taser2"] = 100,
    ["UI_TYC_Skin_Test_Name_Basket"] = 100,
    ["UI_TYC_Skin_Test_Name_M1911"] = 100,
    ["UI_TYC_Skin_Test_Name_TEC9"] = 100,
    ["UI_TYC_Skin_Test_Name_AA12"] = 100,
    ["UI_TYC_Skin_Test_Name_UMP"] = 100,
    ["UI_TYC_Skin_Test_Name_Tommygun"] = 100,
    ["UI_TYC_Skin_Test_Name_P90"] = 100,
    ["UI_TYC_Skin_Test_Name_FAMAS"] = 100,
    ["UI_TYC_Skin_Test_Name_AUG"] = 100,
    ["UI_TYC_Skin_Test_Name_M1"] = 100,
    ["UI_TYC_Skin_Test_Name_SVD"] = 100,
    ["UI_TYC_Skin_Test_Name_Mgl"] = 100,
    ["UI_TYC_Skin_Test_Name_M202"] = 100,
    ["UI_TYC_Skin_Test_Desc_Deserteagle"] = 100,
    ["UI_TYC_Skin_Test_Desc_M1897"] = 100,
    ["UI_TYC_Skin_Test_Desc_UZI"] = 100,
    ["UI_TYC_Skin_Test_Desc_AK47"] = 100,
    ["UI_TYC_Skin_Test_Desc_M4"] = 100,
    ["UI_TYC_Skin_Test_Desc_SCAR"] = 100,
    ["UI_TYC_Skin_Test_Desc_M82"] = 100,
    ["UI_TYC_Skin_Test_Desc_M249"] = 100,
    ["UI_TYC_Skin_Test_Desc_RPG"] = 100,
    ["UI_TYC_Skin_Test_Desc_AMW"] = 100,
    ["UI_TYC_Skin_Test_Desc_RPK"] = 100,
    ["UI_TYC_Skin_Test_Desc_MP5"] = 100,
    ["UI_TYC_Skin_Test_Desc_BaseballBat"] = 100,
    ["UI_TYC_Skin_Test_Desc_Taser2"] = 100,
    ["UI_TYC_Skin_Test_Desc_Basket"] = 100,
    ["UI_TYC_Skin_Test_Desc_M1911"] = 100,
    ["UI_TYC_Skin_Test_Desc_TEC9"] = 100,
    ["UI_TYC_Skin_Test_Desc_AA12"] = 100,
    ["UI_TYC_Skin_Test_Desc_UMP"] = 100,
    ["UI_TYC_Skin_Test_Desc_Tommygun"] = 100,
    ["UI_TYC_Skin_Test_Desc_P90"] = 100,
    ["UI_TYC_Skin_Test_Desc_FAMAS"] = 100,
    ["UI_TYC_Skin_Test_Desc_AUG"] = 100,
    ["UI_TYC_Skin_Test_Desc_M1"] = 100,
    ["UI_TYC_Skin_Test_Desc_SVD"] = 100,
    ["UI_TYC_Skin_Test_Desc_Mgl"] = 100,
    ["UI_TYC_Skin_Test_Desc_M202"] = 100,
    ["UI_TYC_Skin_Test_Desc"] = 100,
    ["UI_TYC_Skin_Test_Name_Deserteagle_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_M1897_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_UZI_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_AK47_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_M4_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_SCAR_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_M82_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_M249_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_RPG_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_AMW_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_RPK_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_MP5_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_BaseballBat_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_Taser2_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_Basket_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_M1911_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_TEC9_Star"] = 100,
    ["UI_TYC_Skin_Test_Name_AA12_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_UMP_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_Tommygun_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_P90_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_FAMAS_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_AUG_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_M1_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_SVD_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_Mgl_Star"] = 101,
    ["UI_TYC_Skin_Test_Name_M202_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_Deserteagle_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_M1897_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_UZI_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_AK47_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_M4_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_SCAR_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_M82_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_M249_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_RPG_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_AMW_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_RPK_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_MP5_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_BaseballBat_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_Taser2_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_Basket_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_M1911_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_TEC9_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_AA12_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_UMP_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_Tommygun_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_P90_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_FAMAS_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_AUG_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_M1_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_SVD_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_Mgl_Star"] = 101,
    ["UI_TYC_Skin_Test_Desc_M202_Star"] = 101,
    ["UI_TYC_Store_Confirm"] = 101,
    ["UI_TYC_CrystalNotEnough"] = 101,
    ["UI_TYC_Store_UseSucces"] = 101,
    ["UI_TYC_Store_UnUseSucces"] = 101,
    ["UI_TYC_Store_BuySucces"] = 101,
    ["UI_TYC_Store_SystemHelpTitle"] = 101,
    ["UI_TYC_Store_SystemHelpText"] = 101,
    ["UI_TYC_Store_CrystalGetHelpTitle"] = 101,
    ["UI_TYC_Store_CrystalGetHelpText"] = 101,
    ["UI_TYC_Monster_Guild_Tips"] = 101,
    ["UI_TYC_Monster_Wave_Reward_Btn"] = 101,
    ["UI_TYC_Wave_Reward_Eliminate"] = 101,
    ["UI_TYC_Wave_Finish"] = 101,
    ["UI_TYC_Wave_Reward_String"] = 101,
    ["UI_TYC_GetCoin"] = 101,
    ["UI_TYC_TD_BackToCommunityTip"] = 101,
    ["UI_TYC_TDS_BackToCommunityTip"] = 101,
    ["UI_TYC_Wave_Explore"] = 101,
    ["UI_TYC_Skin_Test_Name_building1"] = 101,
    ["UI_TYC_Skin_Test_Name_building2"] = 101,
    ["UI_TYC_Skin_Test_Name_building3"] = 101,
    ["UI_TYC_Skin_Test_Name_building4"] = 101,
    ["UI_TYC_Skin_Test_Name_building5"] = 101,
    ["UI_TYC_Skin_Test_Name_building6"] = 101,
    ["UI_TYC_Skin_Test_Name_building7"] = 101,
    ["UI_TYC_Skin_Test_Name_building8"] = 101,
    ["UI_TYC_Skin_Test_Name_building9"] = 101,
    ["UI_TYC_Skin_Test_Name_building10"] = 101,
    ["UI_TYC_Skin_Test_Name_building11"] = 101,
    ["UI_TYC_Skin_Test_Name_building12"] = 101,
    ["UI_TYC_Skin_Test_Name_building13"] = 101,
    ["UI_TYC_Skin_Test_Name_building14"] = 101,
    ["UI_TYC_Skin_Test_Desc_building1"] = 101,
    ["UI_TYC_Skin_Test_Desc_building2"] = 101,
    ["UI_TYC_Skin_Test_Desc_building3"] = 101,
    ["UI_TYC_Skin_Test_Desc_building4"] = 101,
    ["UI_TYC_Skin_Test_Desc_building5"] = 101,
    ["UI_TYC_Skin_Test_Desc_building6"] = 101,
    ["UI_TYC_Skin_Test_Desc_building7"] = 101,
    ["UI_TYC_Skin_Test_Desc_building8"] = 101,
    ["UI_TYC_Skin_Test_Desc_building9"] = 101,
    ["UI_TYC_Skin_Test_Desc_building10"] = 101,
    ["UI_TYC_Skin_Test_Desc_building11"] = 101,
    ["UI_TYC_Skin_Test_Desc_building12"] = 101,
    ["UI_TYC_Skin_Test_Desc_building13"] = 101,
    ["UI_TYC_Skin_Test_Desc_building14"] = 101,
    ["UI_TYC_Skin_Test_Name_flag1"] = 101,
    ["UI_TYC_Skin_Test_Name_flag2"] = 101,
    ["UI_TYC_Skin_Test_Name_flag3"] = 101,
    ["UI_TYC_Skin_Test_Name_flag4"] = 101,
    ["UI_TYC_Skin_Test_Name_flag5"] = 101,
    ["UI_TYC_Skin_Test_Name_flag6"] = 101,
    ["UI_TYC_Skin_Test_Name_flag7"] = 101,
    ["UI_TYC_Skin_Test_Name_flag8"] = 101,
    ["UI_TYC_Skin_Test_Name_flag9"] = 101,
    ["UI_TYC_Skin_Test_Name_flag10"] = 101,
    ["UI_TYC_Skin_Test_Name_flag11"] = 101,
    ["UI_TYC_Skin_Test_Name_flag12"] = 101,
    ["UI_TYC_Skin_Test_Name_flag13"] = 101,
    ["UI_TYC_Skin_Test_Name_flag14"] = 101,
    ["UI_TYC_Skin_Test_Name_flag15"] = 101,
    ["UI_TYC_Skin_Test_Name_flag16"] = 101,
    ["UI_TYC_Skin_Test_Name_flag17"] = 101,
    ["UI_TYC_Skin_Test_Name_flag18"] = 102,
    ["UI_TYC_Skin_Test_Name_flag19"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag1"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag2"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag3"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag4"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag5"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag6"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag7"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag8"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag9"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag10"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag11"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag12"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag13"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag14"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag15"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag16"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag17"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag18"] = 102,
    ["UI_TYC_Skin_Test_Desc_flag19"] = 102,
    ["UI_TYC_Boss_Wave_Finish_Broadcast"] = 102,
    ["UI_TYC_Wave_Suspend_Tips"] = 102,
    ["UI_TYC_Wave_Suspend_End_Content"] = 102,
    ["UI_TYC_Wave_Finish_Broadcast"] = 102,
    ["UI_TD_MonitorSkill_NoEnoughGold"] = 102,
    ["UI_TD_MonitorSkill_InCd"] = 102,
    ["UI_TD_MonitorSkill_ForbiddenPeriod"] = 102,
    ["UI_TYC_Offline_Reward_Tips"] = 102,
    ["UI_TYC_Common_LongMin"] = 102,
    ["UI_TYC_Offline_Diamond_Reward_Tips"] = 102,
    ["UI_TDS_Wave_Pvp_Pre_Tips_Prefix"] = 102,
    ["UI_TDS_Wave_Pvp_Pre_Tips_Content"] = 102,
    ["UI_TDS_Wave_Pvp_tips"] = 102,
    ["UI_TDS_Wave_Endless_Pre_Tips_Prefix"] = 102,
    ["UI_TDS_Wave_Endless_Pre_Tips_Content"] = 102,
    ["UI_TDS_Wave_Endless_Pre_Tips_Suffix"] = 102,
    ["UI_TDS_Wave_Endless_Tips"] = 102,
    ["UI_TDS_Wave_Pvp_Pre_CountDown_Content"] = 102,
    ["UI_TD_Broadcast_WaveBegin"] = 102,
    ["UI_TD_Broadcast_WaveEnd"] = 102,
    ["UI_TD_Broadcast_CastSkill_Mine"] = 102,
    ["UI_TD_Broadcast_CastSkill_Enemy"] = 102,
    ["UI_TD_Broadcast_UnlockWeapon_Mine"] = 102,
    ["UI_TD_Broadcast_UpgradeWeapon_Mine"] = 102,
    ["UI_TD_Broadcast_UpgradeWeapon_Screen_Sub"] = 102,
    ["UI_TD_Broadcast_UpgradeWeapon_Screen"] = 102,
    ["UI_TD_Broadcast_MonsterEscape_Mine"] = 102,
    ["UI_TD_Broadcast_MonsterEscape_Enemy"] = 102,
    ["UI_TD_Monitor_Skill_Name_100004"] = 102,
    ["UI_TD_Monitor_Skill_Name_100002"] = 102,
    ["UI_TD_Monitor_Skill_Name_100003"] = 102,
    ["UI_TD_Monitor_Skill_Name_100008"] = 102,
    ["UI_TD_Monitor_Skill_Name_100009"] = 102,
    ["UI_TD_Monitor_Skill_Name_100011"] = 102,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100002"] = 102,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100004"] = 102,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100008"] = 102,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100009"] = 102,
    ["UI_TD_Monitor_Skill_Name_Broadcast_100011"] = 102,
    ["UI_TD_Broadcast_Summon_Friend_Lv1"] = 102,
    ["UI_TD_Broadcast_Summon_Friend_Lv2"] = 102,
    ["UI_TD_Broadcast_Summon_Friend_Lv3"] = 102,
    ["UI_TD_Broadcast_Summon_Enemy_Lv1"] = 102,
    ["UI_TD_Broadcast_Summon_Enemy_Lv2"] = 102,
    ["UI_TD_Broadcast_Summon_Enemy_Lv3"] = 102,
    ["UI_TD_Broadcast_Friend_Unlock_Weapon"] = 102,
    ["UI_TYC_FPSSetting_Common_Title"] = 102,
    ["UI_TYC_FPSSetting_TYC_Title"] = 102,
    ["UI_TYC_FPSSetting_GyroscopeItem_Title"] = 102,
    ["UI_TYC_FPSSetting_PlayingItem_Title"] = 102,
    ["UI_TYC_FPSSetting_SniperItem_Title"] = 102,
    ["UI_TYC_FPSSetting_CommonItem_Tips"] = 102,
    ["UI_TD_Common_SuperTower_UpGrade"] = 102,
    ["UI_TDS_Broadcast_Player_LoseBlood_Monster"] = 102,
    ["UI_TDS_Broadcast_Player_LoseBlood_BloodNum"] = 102,
    ["UI_TDS_Broadcast_Player_Fail"] = 102,
    ["UI_TDS_Broadcast_Player_Quit"] = 102,
    ["UI_TDS_GetCoin"] = 102,
    ["UI_TD_Common_SuperTower_UpGrade_Text"] = 102,
    ["UI_TD_Common_SuperTower_HasEvolute"] = 102,
    ["UI_TD_Common_SuperTower_DamageUp"] = 102,
    ["UI_TDS_Level_Start_Prefix"] = 102,
    ["UI_TDS_Level_Start_Tips"] = 102,
    ["UI_TD_Dander_Monster_Appear"] = 102,
    ["UI_TD_Dander_Monster_Appear_Tips"] = 102,
    ["UI_TD_Dander_Monster_Call_Appear"] = 102,
    ["UI_TYC_Crystal_Store_Skin_Use_After_Buy"] = 102,
    ["UI_TYC_Crystal_Store_Skin_Use_After_Buy_Success"] = 102,
    ["UI_TYC_NewbieWall_NoAttack_Broadcast"] = 102,
    ["UI_TYC_NewbieWall_NoShoot_Broadcast"] = 102,
    ["UI_TYC_NewbieWall_Over_Broadcast"] = 102,
    ["UI_TDS_Dander_Monster_Appear_Tips"] = 102,
    ["UI_TYC_Wave_Fail_Tips"] = 102,
    ["UI_TYC_Wave_Fail_Content"] = 102,
    ["UI_TYC_Reincarnation_WaveProgress"] = 102,
    ["UI_TYC_Reincarnation_StillHaveWave"] = 102,
    ["UI_TYC_GameResult_Countdown_Restart"] = 102,
    ["UI_TYC_GameResult_Countdown_Continue"] = 102,
    ["UI_TYC_Wave_Fail_Boss_Content"] = 102,
    ["UI_TYC_BackToCommunityTip"] = 103,
    ["UI_TYC_BackToCommunityTip_Back"] = 103,
    ["UI_TYC_BackToCommunityTip_Restart"] = 103,
    ["UI_TYC_QuitTip_Restart"] = 103,
    ["UI_TYC_QuitTip_2"] = 103,
    ["UI_TYC_QuitTip_3"] = 103,
    ["UI_TYC_QuitTip_4"] = 103,
    ["UI_TYC_QuitTip_5"] = 103,
    ["UI_TYC_QuitTip_6"] = 103,
    ["UI_TYC_QuitTip_7"] = 103,
    ["UI_TYC_QuitTip_8"] = 103,
    ["UI_TYC_Ntf_Player_Fail_Reward_Content"] = 103,
    ["UI_TYC_PeakReincarnation"] = 103,
    ["UI_TYC_Monster_Boss"] = 103,
    ["UI_TD_Dander_Monster_Name"] = 103,
    ["UI_TYC_Wave_Idx_Tips"] = 103,
    ["UI_OMD_PlayerNum_One"] = 103,
    ["UI_OMD_PlayerNum_Two"] = 103,
    ["UI_OMD_PlayerNum_Four"] = 103,
    ["UI_OMD_Monster_Invade_Tips"] = 103,
    ["UI_OMD_Monster_Appear_Tips"] = 103,
    ["UI_OMD_Broadcast_Player_LoseBlood_Monster"] = 103,
    ["UI_OMD_Broadcast_Player_LoseBlood_BloodNum"] = 103,
    ["UI_OMD_GameResult_BtnBack_Lobby"] = 103,
    ["UI_OMD_GameResult_Btn_Restart"] = 103,
    ["UI_OMD_GameResult_Btn_NextLevel"] = 103,
    ["UI_OMD_GameResult_Text_LevelScore"] = 103,
    ["UI_OMD_GameResult_Text_CrownGrade"] = 103,
    ["UI_OMD_GameResult_Text_AdditionalCrown"] = 103,
    ["UI_OMD_GameResult_Text_CostTime"] = 103,
    ["UI_OMD_GameResult_Text_GateNum"] = 103,
    ["UI_OMD_GameResult_Text_TotalScore"] = 103,
    ["UI_OMD_GameResult_Text_TotalSkeletons"] = 103,
    ["UI_OMD_GameResult_Text_Wave"] = 103,
    ["UI_OMD_UI_OMD_LevelSelect_Unlock_Tip"] = 103,
    ["UI_OMD_GameResult_GameName"] = 103,
    ["UI_OMD_GameResult_Details"] = 103,
    ["UI_OMD_TrapHint_State_Build"] = 103,
    ["UI_OMD_TrapHint_State_Sell"] = 103,
    ["UI_OMD_TrapHint_Build_Disable"] = 103,
    ["UI_OMD_TrapHint_WarTrap_Build_Invalid"] = 103,
    ["UI_OMD_TrapHint_Build_Invalid"] = 103,
    ["UI_OMD_TrapHint_Build_Type1"] = 103,
    ["UI_OMD_TrapHint_Build_Type2"] = 103,
    ["UI_OMD_TrapHint_Build_Type4"] = 103,
    ["UI_OMD_TrapHint_Build_Type5"] = 103,
    ["UI_OMD_TrapHint_Build_Type6"] = 103,
    ["UI_OMD_TrapHint_Build_Type3"] = 103,
    ["UI_OMD_Member_Wait_Leader_Battle"] = 103,
    ["UI_OMD_Endless_Db_ConfirmTitle"] = 103,
    ["UI_OMD_Endless_Db_ConfirmContent"] = 103,
    ["UI_OMD_Dander_Monster_Appear"] = 103,
    ["UI_OMD_Dander_Monster_Appear_Tips"] = 103,
    ["UI_OMD_Player_Join_Message_Tips"] = 103,
    ["UI_OMD_Difficulty_Simple"] = 103,
    ["UI_OMD_Difficulty_Normal"] = 103,
    ["UI_OMD_Difficulty_Difficulty"] = 103,
    ["UI_OMD_Difficulty_Endless"] = 103,
    ["UI_OMD_QuitSaveConfirm"] = 103,
    ["UI_OMD_QuitSaveConfirm_Save"] = 103,
    ["UI_OMD_QuitSaveConfirm_NoSave"] = 103,
    ["UI_OMD_Give_Up_Db_Info"] = 103,
    ["UI_OMD_GameResult_Text_NewbieTitle"] = 103,
    ["UI_OMD_GameResult_Text_NewbieFail"] = 103,
    ["UI_OMD_GameResult_Text_End"] = 103,
    ["UI_OMD_GameResult_Text_MoreWave"] = 103,
    ["UI_OMD_GameResult_Text_LevelName"] = 103,
    ["UI_OMD_GameResult_Text_WaveVictory"] = 103,
    ["UI_OMD_GameResult_Text_WaveFailed"] = 103,
    ["UI_OMD_Player_Insufficient_player_Message_Tips"] = 103,
    ["UI_OMD_Player_Fill_Back_Again_Message_Tips"] = 103,
    ["UI_OMD_Player_Fill_Back_Start_Message_Tips"] = 103,
    ["UI_OMD_SelectWave_Tips"] = 103,
    ["UI_OMD_SelectWave_Item_Tips"] = 103,
    ["UI_OMD_SelectWave_Item_Tips_1"] = 103,
    ["UI_OMD_SelectWave_Item_Tips_2"] = 103,
    ["UI_OMD_SelectWave_GameWin_Tips_1"] = 103,
    ["UI_OMD_SelectWave_GameWin_Tips_2"] = 103,
    ["UI_OMD_TeamPreparation_Replay_Text"] = 103,
    ["UI_OMD_TeamPreparation_GoLobby_Text"] = 103,
    ["UI_OMD_EquipmentUpEntrance_Name"] = 103,
    ["UI_OMD_Tip_Insufficient_Crowns"] = 103,
    ["UI_OMD_Tip_Complete_Previous_Task"] = 103,
    ["UI_OMD_NotInRank"] = 103,
    ["UI_OMD_LockedRank"] = 103,
    ["UI_OMD_RankNoInfo"] = 103,
    ["UI_OMD_NewbieClickJianQiang"] = 103,
    ["UI_OMD_NewbieClickNu"] = 103,
    ["UI_OMD_NewbieClickObstacle"] = 103,
    ["UI_OMD_PlayerKillMonster"] = 103,
    ["UI_OMD_GetCoins"] = 103,
    ["UI_OMD_SellTrap"] = 103,
    ["UI_OMD_TeamPreparation_Prepared_Confirm_Text"] = 103,
    ["UI_OMD_TeamPreparation_Prepared_Prepare_Text"] = 103,
    ["UI_OMD_TeamPreparation_Select_Btn_Refuse_Text"] = 103,
    ["UI_OMD_TeamPreparation_Select_Btn_Confirm_Text"] = 103,
    ["UI_OMD_TeamPreparation_Select_Btn_Prepare_Text"] = 103,
    ["UI_OMD_TeamPreparation_Select_Btn_Ready_Text"] = 103,
    ["UI_OMD_No_Permission_Start_Challenge"] = 103,
    ["UI_OMD_Too_Long_Battle_Quit_Text"] = 103,
    ["UI_OMD_MinMap_LayerInfo"] = 104,
    ["UI_OMD_CurrentState_Can_Not_Change_Weapon"] = 104,
    ["UI_OMD_DeletePropsTip_YouHaveAlreadyPut"] = 104,
    ["UI_OMD_DeletePropsTip_Make"] = 104,
    ["UI_OMD_DeletePropsTip_AwayFromBag"] = 104,
    ["UI_OMD_DeletePropsTip_WillAutomaticallySellAllPlacedTraps"] = 104,
    ["UI_OMD_CancelPaidRevive_Text"] = 104,
    ["UI_OMD_RankBattleMode"] = 104,
    ["UI_OMD_RankChallengeMode"] = 104,
    ["UI_OMD_TotalRank"] = 104,
    ["UI_OMD_Not_Invite_On_FillBack_Text"] = 104,
    ["UI_OMD_UI_OMD_LevelSelect_CurLevel_Unlock_Tip"] = 104,
    ["UI_OMD_EndlessModeDescription"] = 104,
    ["UI_OMD_SellTrapDialogHint"] = 104,
    ["UI_OMD_SellTrapDialogConfirm"] = 104,
    ["UI_OMD_SellTrapDialogCancel"] = 104,
    ["UI_OMD_SellTrapDialogNameDefault"] = 104,
    ["UI_OMD_UI_OMD_LevelSelect_Unlock_Tip_Endless"] = 104,
    ["UI_OMD_UI_OMD_GameMode_Prepare"] = 104,
    ["UI_OMD_UI_OMD_GameMode_Start"] = 104,
    ["UI_OMD_BloorBar_Member_Wait_Battle"] = 104,
    ["UI_OMD_BloorBar_Wait_Other_Battle"] = 104,
    ["UI_OMD_HelpTip"] = 104,
    ["UI_OMD_HelpTip_Battle"] = 104,
    ["UI_OMD_HelpTip_Battle_Rule"] = 104,
    ["UI_OMD_HelpTip_Battle_Content"] = 104,
    ["UI_OMD_HelpTip_Endless"] = 104,
    ["UI_OMD_HelpTip_Endless_Rule"] = 104,
    ["UI_OMD_HelpTip_Endless_Content"] = 104,
    ["UI_OMD_StartBattle"] = 104,
    ["UI_OMD_GetCoinsByKill"] = 104,
    ["UI_OMD_GameResult_GetCrown"] = 104,
    ["UI_OMD_NewbieNotEnoughMoney"] = 104,
    ["UI_OMD_UI_Blood_Start_Battle"] = 104,
    ["UI_OMD_LevelSelect_Start_Battle"] = 104,
    ["UI_OMD_LevelSelect_Confirm"] = 104,
    ["UI_OMD_DailyMission_Not_Finish_Task_Tips"] = 104,
    ["UI_OMD_DailyMission_Finish_Task_Tips"] = 104,
    ["UI_OMD_Store_Confirm"] = 104,
    ["UI_OMD_Back_To_Hall"] = 104,
    ["UI_OMD_GameMode_Wait_Member_Prepare"] = 104,
    ["UI_OMD_DailyMission_Daily_Task_Complete"] = 104,
    ["UI_OMD_Skin_Name_Blunderbuss"] = 104,
    ["UI_OMD_Skin_Name_Crossbow"] = 104,
    ["UI_OMD_Skin_Name_Sword"] = 104,
    ["UI_OMD_Skin_Name_Magicbow"] = 104,
    ["UI_OMD_Skin_Name_Hammer"] = 104,
    ["UI_OMD_Skin_Name_Wand"] = 104,
    ["UI_OMD_Skin_Desc_Blunderbuss"] = 104,
    ["UI_OMD_Skin_Desc_Crossbow"] = 104,
    ["UI_OMD_Skin_Desc_Sword"] = 104,
    ["UI_OMD_Skin_Desc_Magicbow"] = 104,
    ["UI_OMD_Skin_Desc_Hammer"] = 104,
    ["UI_OMD_Skin_Desc_Wand"] = 104,
    ["UI_OMD_TrapSkin_Name_3001"] = 104,
    ["UI_OMD_TrapSkin_Name_3002"] = 104,
    ["UI_OMD_TrapSkin_Name_3003"] = 104,
    ["UI_OMD_TrapSkin_Name_3005"] = 104,
    ["UI_OMD_TrapSkin_Name_3006"] = 104,
    ["UI_OMD_TrapSkin_Name_3007"] = 104,
    ["UI_OMD_TrapSkin_Name_3008"] = 104,
    ["UI_OMD_TrapSkin_Name_3009"] = 104,
    ["UI_OMD_TrapSkin_Name_3010"] = 104,
    ["UI_OMD_TrapSkin_Name_3011"] = 104,
    ["UI_OMD_TrapSkin_Name_3012"] = 104,
    ["UI_OMD_TrapSkin_Name_3014"] = 104,
    ["UI_OMD_TrapSkin_Name_3015"] = 104,
    ["UI_OMD_TrapSkin_Name_3016"] = 104,
    ["UI_OMD_TrapSkin_Name_3017"] = 104,
    ["UI_OMD_TrapSkin_Name_3018"] = 104,
    ["UI_OMD_TrapSkin_Name_3019"] = 104,
    ["UI_OMD_TrapSkin_Name_3020"] = 104,
    ["UI_OMD_TrapSkin_Name_3021"] = 104,
    ["UI_OMD_TrapSkin_Name_3022"] = 104,
    ["UI_OMD_TrapSkin_Name_3023"] = 104,
    ["UI_OMD_TrapSkin_Name_3024"] = 104,
    ["UI_OMD_TrapSkin_Name_3025"] = 104,
    ["UI_OMD_TrapSkin_Name_3026"] = 104,
    ["UI_OMD_TrapSkin_Name_3027"] = 104,
    ["UI_OMD_TrapSkin_Name_3028"] = 104,
    ["UI_OMD_TrapSkin_Name_3029"] = 104,
    ["UI_OMD_TrapSkin_Name_3030"] = 104,
    ["UI_OMD_TrapSkin_Des_01"] = 104,
    ["UI_OMD_TrapSkin_Des_02"] = 104,
    ["UI_OMD_TrapSkin_Des_03"] = 104,
    ["UI_OMD_Wave_Select_Tip"] = 104,
    ["UI_OMD_AFK_WARNING"] = 104,
    ["UI_OMD_RedIsFull"] = 104,
    ["UI_OMD_BlueIsFull"] = 104,
    ["UI_OMD_UI_StartWave_In_80703_NewbieTip"] = 104,
    ["UI_OMD_AddHealth"] = 104,
    ["UI_OMD_AddMana"] = 104,
    ["UI_OMD_CLICK_QUITBUID_TIP"] = 104,
    ["UI_OMD_SELECT_LEVEL_ON_VOTING"] = 104,
    ["UI_OMD_No_Permission_Change_Level"] = 104,
    ["UI_OMD_Single_Db_Give_Up_Warning_Tips"] = 104,
    ["UI_OMD_CrownManagementTips"] = 104,
    ["UI_OMD_OrcTotal"] = 104,
    ["UI_OMD_OrcGeneral"] = 104,
    ["UI_OMD_OrcElite"] = 104,
    ["UI_OMD_OrcLord"] = 105,
    ["UI_OMD_Select_Level_Wait_Other_Loading"] = 105,
    ["UI_OMD_UI_OMD_Levels_Unlock_Tip"] = 105,
    ["UI_OMD_UI_OMD_Levels_Unlock_HardMode_One"] = 105,
    ["UI_OMD_UI_OMD_Levels_Unlock_HardMode_Two"] = 105,
    ["UI_OMD_ExchangeTargetInfo"] = 105,
    ["UI_OMD_ExchangeTrapSkinInfo_One"] = 105,
    ["UI_OMD_ExchangeTrapSkinInfo_Two"] = 105,
    ["UI_OMD_ExchangeNoStarCoin"] = 105,
    ["UI_OMD_VoteTime"] = 105,
    ["UI_OMD_VoteTimeTips"] = 105,
    ["UI_OMD_ConfirmContent4NewSeason"] = 105,
    ["UI_OMD_ConfirmContent4OldSeasonSingleDB"] = 105,
    ["UI_OMD_RoomHasPWDCannotRecruit"] = 105,
    ["UI_OMD_UGC_Confirm"] = 105,
    ["UI_OMD_UGC_WaitToConfirm"] = 105,
    ["UI_OMD_UGC_StartBattle"] = 105,
    ["UI_OMD_UGC_Select_Level_VersionError"] = 105,
    ["UI_OMD_UGC_LevelPlayerNumError"] = 105,
    ["UI_OMD_QuitTestUGCLevel"] = 105,
    ["UI_OMD_UGC_ReturnToCreate"] = 105,
    ["UI_OMD_UGC_Select_Level_InSearchPlayerStateError"] = 105,
    ["UI_OMD_UGC_Select_Level_InRecruitStateError"] = 105,
    ["UI_OMD_UGC_LevelTips"] = 105,
    ["UI_OMD_UGC_DropPoolEditorCountNum"] = 105,
    ["UI_OMD_UGC_DropPoolEditorCountNum_Perview"] = 105,
    ["UI_OMD_UGC_DropPoolEditorMaxNumTips"] = 105,
    ["UI_OMD_UGC_DropPoolEditorCreateBtn"] = 105,
    ["UI_OMD_UGC_DropPoolEditorDefaultName"] = 105,
    ["UI_OMD_UGC_DropPoolEditorDeleteConfirm"] = 105,
    ["UI_OMD_UGC_DropPoolEditorDeleteConfirm_WithRef"] = 105,
    ["UI_OMD_UGC_DropPoolEnumDefault"] = 105,
    ["UI_OMD_UGC_DropPoolEnumEmpty"] = 105,
    ["UI_OMD_UGC_DropPoolDropRateTips"] = 105,
    ["UI_OMD_UGC_EnterLevelFail"] = 105,
    ["UI_OMD_UGC_SelectLevelFail"] = 105,
    ["TravelDog_AfternoonMessage_1"] = 106,
    ["TravelDog_AfternoonMessage_2"] = 106,
    ["TravelDog_AfternoonMessage_3"] = 106,
    ["TravelDog_AfternoonMessage_4"] = 106,
    ["TravelDog_AfternoonMessage_5"] = 106,
    ["TravelDog_AfternoonMessage_6"] = 106,
    ["TravelDog_AfternoonMessage_7"] = 106,
    ["TravelDog_AfternoonMessage_8"] = 106,
    ["TravelDog_AfternoonMessage_9"] = 106,
    ["TravelDog_AfternoonMessage_10"] = 106,
    ["TravelDog_AfternoonMessage_11"] = 106,
    ["TravelDog_AfternoonMessage_12"] = 106,
    ["TravelDog_AfternoonMessage_13"] = 106,
    ["TravelDog_AfternoonMessage_14"] = 106,
    ["TravelDog_AfternoonMessage_15"] = 106,
    ["TravelDog_AfternoonMessage_16"] = 106,
    ["TravelDog_AfternoonMessage_17"] = 106,
    ["TravelDog_AfternoonMessage_18"] = 106,
    ["TravelDog_Sleep"] = 106,
    ["TravelDog_DateShow_Today"] = 106,
    ["TravelDog_DateShow"] = 106,
    ["TravelDog_DefaultWeather"] = 106,
    ["TravelDog_Timeout"] = 106,
    ["TravelDog_PreparationTips"] = 106,
    ["TravelDog_InFarmTip"] = 106,
    ["TravelDog_RoseMarket"] = 106,
    ["UGC_Programme_Element"] = 107,
    ["UGC_Programme_LogicElement"] = 107,
    ["UGC_Programme_Model"] = 107,
    ["UGC_Programme_SignalBox"] = 107,
    ["UGC_Programme_Vehicle"] = 107,
    ["UGC_Programme_Creature"] = 107,
    ["UGC_Programme_Complete"] = 107,
    ["UGC_Programme_Choose"] = 107,
    ["UGC_Programme_Button"] = 107,
    ["UGC_Programme_Text"] = 107,
    ["UGC_Programme_Picture"] = 107,
    ["UGC_Programme_Tips"] = 107,
    ["UGC_Programme_Tips_Error"] = 107,
    ["UGC_Programme_Element_Error"] = 107,
    ["UGC_Programme_LogicElement_Error"] = 107,
    ["UGC_Programme_Model_Error"] = 107,
    ["UGC_Programme_SignalBox_Error"] = 107,
    ["UGC_Programme_Vehicle_Error"] = 107,
    ["UGC_Programme_Creature_Error"] = 107,
    ["UGC_Programme_TextBoard_Error"] = 107,
    ["UGC_Programme_TextBoard"] = 107,
    ["UGC_Programme_ImageBoard"] = 107,
    ["UGC_Programme_C4Bomb"] = 107,
    ["UGC_Programme_PointGuide"] = 107,
    ["UGC_Programme_PointGuide_Error"] = 107,
    ["UGC_Programme_ProgressBoard"] = 107,
    ["UGC_MapCapacity_Save_IsExceedLimit"] = 107,
    ["UGC_MapCapacity_Save_IsAlmostLimit"] = 107,
    ["UGC_MapCapacity_Save_IsNearLimit"] = 107,
    ["UGC_Map_Capacity_Open_IsExceedLimit"] = 107,
    ["UGC_Map_Capacity_Open_IsAlmostLimit"] = 107,
    ["UGC_Map_Capacity_Publish_IsExceedLimit"] = 107,
    ["UGC_PACustomAttribute_NameRepeated"] = 107,
    ["UGC_PatrolPath_OutofPointCount"] = 108,
    ["UGC_ActionEditor_DeleteConfirm"] = 108,
    ["UGC_ActionEditor_Deleted"] = 108,
    ["UGC_ActionEditor_DeleteUsedConfirm"] = 108,
    ["UGC_ActionEditor_StorageMax"] = 108,
    ["UGC_ActionEditor_VideoTransforming"] = 108,
    ["UGC_ActionEditor_VideoAnim"] = 108,
    ["UGC_NPCEditor_InteractionKey"] = 108,
    ["UGC_ActionEditor_VideoPageName"] = 108,
    ["UGC_ActionEditor_VideoCreatButton"] = 108,
    ["UGC_ActionEditor_VideoCreatTip1"] = 108,
    ["UGC_ActionEditor_VideoCreatTip2"] = 108,
    ["UGC_ActionEditor_VideoCreatTip3"] = 108,
    ["UGC_ActionEditor_VideoCreatTip4"] = 108,
    ["UGC_ActionEditor_VideoCreatTip5"] = 108,
    ["UGC_ActionEditor_QuitConfirm"] = 108,
    ["UGC_ActionEditor_SaveTimeoutText"] = 108,
    ["UGC_ActionEditor_DiyActionName"] = 108,
    ["UGC_ActionEditor_EditButtonTip"] = 108,
    ["UGC_ActionEditor_DelButtonTip"] = 108,
    ["UGC_ActionEditor_AddAnimFail"] = 108,
    ["UGC_ActionEditor_AddExpressionFail"] = 108,
    ["UGC_AIGCAnim_ServerBusy"] = 108,
    ["UGC_AIGCAnim_MediaFile_MaxSize_Tip"] = 108,
    ["UGC_AIGCAnim_MediaFile_MaxDuration_Tip"] = 108,
    ["UGC_AIGCAnim_GetUploadCosFailed_Tip"] = 108,
    ["UGC_AIGCAnim_NetworkIsBad_Tip"] = 108,
    ["UGC_AIGCAnim_UploadCosFailed_Tip"] = 108,
    ["UGC_AIGCAnim_ServerError_Tip"] = 108,
    ["UGC_AIGCAnim_ServerError_Busy_Tip"] = 108,
    ["UGC_AIGCAnim_ExamineFailed_Tip"] = 108,
    ["UGC_AIGCAnim_ExamineSpiteFailed_Tip"] = 108,
    ["UGC_AIGCAnim_ExamineSensitiveFailed_Tip"] = 108,
    ["UGC_AIGCAnim_GetJsonTimeout_Tip"] = 108,
    ["UGC_AIGCAnim_JsonReturnError_Tip"] = 108,
    ["UGC_AIGCAnim_DownloadPbinError_Tip"] = 108,
    ["UGC_AIGCAnim_MinWidth_Tip"] = 108,
    ["UGC_AIGCAnim_GenerateFailTip"] = 108,
    ["UGC_AIGCAnim_TooOften"] = 108,
    ["UGC_AIGCAnim_FileSaveFailed"] = 108,
    ["UGC_AIGCAnim_ExtensionNotSupport"] = 108,
    ["UGC_ActionEditor_TabOfficial"] = 108,
    ["UGC_ActionEditor_TabSplice"] = 108,
    ["UGC_ActionEditor_TabVideo"] = 108,
    ["UGC_AIGCAnim_SelectVideoTip"] = 108,
    ["UGC_Place_NPC_OutOfMaxNum"] = 108,
    ["UGC_Object_Cant_BindNPC"] = 108,
    ["UGC_ActionEditor_SaveNameRepeatTip"] = 108,
    ["UGC_ActionEditorExpreWithoutAction"] = 108,
    ["UGC_PatrolPath_OutofPathCount"] = 108,
    ["UGC_Camera_Shake"] = 108,
    ["UGC_Camera_ChangeView"] = 108,
    ["UGC_Camera_Block"] = 108,
    ["UGC_ActionEditor_SaveNameIsNull"] = 108,
    ["UGC_RPG_WarnningWordsTips"] = 108,
    ["UGC_Dialogue_Nothing"] = 108,
    ["UGC_Dialogue_GroupName"] = 108,
    ["UGC_Dialogue_CreateDialogue"] = 108,
    ["UGC_Dialogue_NoDialogue"] = 108,
    ["UGC_Dialogue_StepEdit"] = 108,
    ["UGC_Dialogue_OptionEdit"] = 108,
    ["UGC_Dialogue_ImageEdit"] = 108,
    ["UGC_Dialogue_MaxStepNum"] = 108,
    ["UGC_Dialogue_SaveSuccess"] = 108,
    ["UGC_Dialogue_MaxGroupNum"] = 108,
    ["UGC_Dialogue_CreateGroupSuccess"] = 108,
    ["UGC_Dialogue_Open"] = 108,
    ["UGC_Dialogue_Choose"] = 108,
    ["UGC_Dialogue_DeleteStepTips"] = 108,
    ["UGC_Dialogue_DeleteStepGroupTips"] = 108,
    ["UGC_Dialogue_DeleteOptionTips"] = 108,
    ["UGC_Dialogue_CreateOptionTips"] = 108,
    ["UGC_Dialogue_CreateGroupFirstTips"] = 108,
    ["UGC_Dialogue_DeleteGroupTips"] = 108,
    ["UGC_Dialogue_WithoutGroupTips"] = 108,
    ["UGC_Dialogue_AddNewGroup"] = 108,
    ["UGC_Dialogue_OptionWithoutBind"] = 108,
    ["UGC_Dialogue_OptionStop"] = 108,
    ["UGC_Dialogue_StepEmojiTitle"] = 108,
    ["UGC_Dialogue_StepBoxTitle"] = 108,
    ["UGC_Dialogue_AddOption"] = 108,
    ["UGC_Dialogue_StepSpeakerEmo"] = 108,
    ["UGC_Dialogue_StepSpeakerAnim"] = 108,
    ["UGC_Dialogue_StepDefault"] = 108,
    ["UGC_Object_Cant_BindMirrorActor"] = 108,
    ["Cant_ReplaceChar_CameraType_FP"] = 108,
    ["UGC_CustomAnimSubActor_Cannot_MultipleSelect"] = 108,
    ["UGC_AIGC_Voice_HasPeddingVoice"] = 108,
    ["MCG_ModeName"] = 109,
    ["MCG_ModeInfo"] = 109,
    ["MCG_ModeInfoUGC"] = 109,
    ["MCG_Mode_Background"] = 109,
    ["MCG_MapName_A"] = 109,
    ["MCG_MapInfo_A"] = 109,
    ["MCG_MapName_B"] = 109,
    ["MCG_MapInfo_B"] = 109,
    ["MCG_MapName_C"] = 109,
    ["MCG_MapInfo_C"] = 109,
    ["MCG_MapName_D"] = 109,
    ["MCG_MapInfo_D"] = 109,
    ["MCG_MapName_E"] = 109,
    ["MCG_MapInfo_E"] = 109,
    ["MCG_MapName_F"] = 109,
    ["MCG_MapInfo_F"] = 109,
    ["MCG_MapName_G"] = 109,
    ["MCG_MapInfo_G"] = 109,
    ["MCG_MapName_H"] = 109,
    ["MCG_MapInfo_H"] = 109,
    ["MCG_MapName_I"] = 109,
    ["MCG_MapInfo_I"] = 109,
    ["MCG_MapName_J"] = 109,
    ["MCG_MapInfo_J"] = 109,
    ["MCG_InGame_GuideNPC_Name"] = 109,
    ["MCG_InGame_Ghost_Guide"] = 109,
    ["MCG_InGame_Human_Guide"] = 109,
    ["MCG_InGame_Human"] = 109,
    ["MCG_InGame_Ghost"] = 109,
    ["MCG_InGame_GhostTips_Begin"] = 109,
    ["MCG_InGame_HumanTips_Begin"] = 109,
    ["MCG_InGame_GhostTips_Begin1"] = 109,
    ["MCG_InGame_GhostTips_Begin2"] = 109,
    ["MCG_InGame_HumanTips_Begin1"] = 109,
    ["MCG_InGame_HumanTips_Begin2"] = 109,
    ["MCG_InGame_HumanTips_Select"] = 109,
    ["MCG_InGame_GhostTips_Select"] = 109,
    ["MCG_InGame_GhostTips_SelectUI"] = 109,
    ["MCG_InGame_Second"] = 109,
    ["MCG_InGame_HumanSkill_location"] = 109,
    ["MCG_InGame_HumanSkill_Title"] = 109,
    ["MCG_InGame_HumanSkill_Title_Position"] = 109,
    ["MCG_InGame_HumanSkill_1A_Name"] = 109,
    ["MCG_InGame_HumanSkill_1A_Info"] = 109,
    ["MCG_InGame_HumanSkill_1A_Position"] = 109,
    ["MCG_InGame_HumanSkill_1B_Name"] = 109,
    ["MCG_InGame_HumanSkill_1B_Info"] = 109,
    ["MCG_InGame_HumanSkill_1C_Name"] = 109,
    ["MCG_InGame_HumanSkill_1C_Info"] = 109,
    ["MCG_InGame_HumanSkill_2A_Name"] = 109,
    ["MCG_InGame_HumanSkill_2A_Info"] = 109,
    ["MCG_InGame_HumanSkill_2A_Position"] = 109,
    ["MCG_InGame_HumanSkill_2B_Name"] = 109,
    ["MCG_InGame_HumanSkill_2B_Info"] = 109,
    ["MCG_InGame_HumanSkill_2C_Name"] = 109,
    ["MCG_InGame_HumanSkill_2C_Info"] = 109,
    ["MCG_InGame_HumanSkill_2D_Name"] = 109,
    ["MCG_InGame_HumanSkill_2D_Info"] = 109,
    ["MCG_InGame_HumanSkill_2D_Toast1"] = 109,
    ["MCG_InGame_HumanSkill_2D_Toast2"] = 109,
    ["MCG_InGame_HumanSkill_2D_Toast3"] = 109,
    ["MCG_InGame_HumanSkill_2D_Toast4"] = 109,
    ["MCG_InGame_HumanSkill_2D_Toast5"] = 109,
    ["MCG_InGame_HumanSkill_2D_Toast6"] = 109,
    ["MCG_InGame_HumanSkill_2D_Toast7"] = 109,
    ["MCG_InGame_HumanSkill_2D_ToastTang"] = 109,
    ["MCG_InGame_HumanSkill_2D_ToastAngel"] = 109,
    ["MCG_InGame_HumanSkill_2D_ToastNezha"] = 109,
    ["MCG_InGame_HumanSkill_2D_ToastDriven"] = 109,
    ["MCG_InGame_HumanSkill_2D_TeleportFailed"] = 109,
    ["MCG_InGame_HumanSkill_2D_TeleportRetry"] = 109,
    ["MCG_InGame_HumanSkill_2D_ToastForbidden"] = 109,
    ["MCG_InGame_HumanSkill_2D_ToastIsBusy"] = 109,
    ["MCG_InGame_HumanSkill_2D_ToastCD"] = 109,
    ["MCG_InGame_HumanSkill_2D_MY"] = 109,
    ["MCG_InGame_HumanSkill_2D_StarRemain"] = 109,
    ["MCG_InGame_Button_Teleport"] = 109,
    ["MCG_InGame_HumanSkill_2D_TeleportProgress"] = 109,
    ["MCG_InGame_HumanSkill_3A_Name"] = 109,
    ["MCG_InGame_HumanSkill_3A_Info"] = 109,
    ["MCG_InGame_HumanSkill_3A_Position"] = 109,
    ["MCG_InGame_HumanSkill_3B_Name"] = 109,
    ["MCG_InGame_HumanSkill_3B_Info"] = 109,
    ["MCG_InGame_HumanSkill_3C_Name"] = 109,
    ["MCG_InGame_HumanSkill_3C_Info"] = 109,
    ["MCG_InGame_HumanSkill_4A_Name"] = 109,
    ["MCG_InGame_HumanSkill_4A_Info"] = 109,
    ["MCG_InGame_HumanSkill_4A_Position"] = 109,
    ["MCG_InGame_HumanSkill_4B_Name"] = 109,
    ["MCG_InGame_HumanSkill_4B_Info"] = 109,
    ["MCG_InGame_HumanSkill_4C_Name"] = 109,
    ["MCG_InGame_HumanSkill_4C_Info"] = 109,
    ["MCG_InGame_HumanSkill_5A_Name"] = 109,
    ["MCG_InGame_HumanSkill_5A_Info"] = 109,
    ["MCG_InGame_HumanSkill_5A_Position"] = 109,
    ["MCG_InGame_HumanSkill_5B_Name"] = 109,
    ["MCG_InGame_HumanSkill_5B_Info"] = 109,
    ["MCG_InGame_HumanSkill_5C_Name"] = 109,
    ["MCG_InGame_HumanSkill_5C_Info"] = 109,
    ["MCG_InGame_HumanSkill_6A_Name"] = 109,
    ["MCG_InGame_HumanSkill_6A_Info"] = 110,
    ["MCG_InGame_HumanSkill_6A_Position"] = 110,
    ["MCG_InGame_HumanSkill_6B_Name"] = 110,
    ["MCG_InGame_HumanSkill_6B_Info"] = 110,
    ["MCG_InGame_HumanSkill_6C_Name"] = 110,
    ["MCG_InGame_HumanSkill_6C_Info"] = 110,
    ["MCG_InGame_HumanSkill_7A_Name"] = 110,
    ["MCG_InGame_HumanSkill_7A_Info"] = 110,
    ["MCG_InGame_HumanSkill_7A_Position"] = 110,
    ["MCG_InGame_HumanSkill_7B_Name"] = 110,
    ["MCG_InGame_HumanSkill_7B_Info"] = 110,
    ["MCG_InGame_HumanSkill_7C_Name"] = 110,
    ["MCG_InGame_HumanSkill_7C_Info"] = 110,
    ["MCG_InGame_HumanSkill_8A_Name"] = 110,
    ["MCG_InGame_HumanSkill_8A_Info"] = 110,
    ["MCG_InGame_HumanSkill_8A_StealthButton"] = 110,
    ["MCG_InGame_HumanSkill_8A_StealthCancel"] = 110,
    ["MCG_InGame_HumanSkill_8A_FillButton"] = 110,
    ["MCG_InGame_HumanSkill_8A_Position"] = 110,
    ["MCG_InGame_HumanSkill_8B_Name"] = 110,
    ["MCG_InGame_HumanSkill_8B_Info"] = 110,
    ["MCG_InGame_HumanSkill_8C_Name"] = 110,
    ["MCG_InGame_HumanSkill_8C_Info"] = 110,
    ["MCG_InGame_HumanSkill_9A_Name"] = 110,
    ["MCG_InGame_HumanSkill_9A_Info"] = 110,
    ["MCG_InGame_HumanSkill_9A_Position"] = 110,
    ["MCG_InGame_HumanSkill_9B_Name"] = 110,
    ["MCG_InGame_HumanSkill_9B_Info"] = 110,
    ["MCG_InGame_HumanSkill_9C_Name"] = 110,
    ["MCG_InGame_HumanSkill_9C_Info"] = 110,
    ["MCG_InGame_HumanSkill_10A_Name"] = 110,
    ["MCG_InGame_HumanSkill_10A_Info"] = 110,
    ["MCG_InGame_HumanSkill_10A_Position"] = 110,
    ["MCG_InGame_HumanSkill_10B_Name"] = 110,
    ["MCG_InGame_HumanSkill_10B_Info"] = 110,
    ["MCG_InGame_HumanSkill_10C_Name"] = 110,
    ["MCG_InGame_HumanSkill_10C_Info"] = 110,
    ["MCG_InGame_HumanSkill_11A_Name"] = 110,
    ["MCG_InGame_HumanSkill_11A_Info"] = 110,
    ["MCG_InGame_HumanSkill_11A_Position"] = 110,
    ["MCG_InGame_HumanSkill_11B_Name"] = 110,
    ["MCG_InGame_HumanSkill_11B_Info"] = 110,
    ["MCG_InGame_HumanSkill_11C_Name"] = 110,
    ["MCG_InGame_HumanSkill_11C_Info"] = 110,
    ["MCG_InGame_HumanSkill_12A_Name"] = 110,
    ["MCG_InGame_HumanSkill_12A_Info"] = 110,
    ["MCG_InGame_HumanSkill_12A_Position"] = 110,
    ["MCG_InGame_HumanSkill_12B_Name"] = 110,
    ["MCG_InGame_HumanSkill_12B_Info"] = 110,
    ["MCG_InGame_HumanSkill_12C_Name"] = 110,
    ["MCG_InGame_HumanSkill_12C_Info"] = 110,
    ["MCG_InGame_HumanSkill_13A_Name"] = 110,
    ["MCG_InGame_HumanSkill_13A_Info"] = 110,
    ["MCG_InGame_HumanSkill_13A_Position"] = 110,
    ["MCG_InGame_HumanSkill_13B_Name"] = 110,
    ["MCG_InGame_HumanSkill_13B_Info"] = 110,
    ["MCG_InGame_HumanSkill_13C_Name"] = 110,
    ["MCG_InGame_HumanSkill_13C_Info"] = 110,
    ["MCG_InGame_HumanSkill_14A_Name"] = 110,
    ["MCG_InGame_HumanSkill_14A_Info"] = 110,
    ["MCG_InGame_HumanSkill_14A_Position"] = 110,
    ["MCG_InGame_HumanSkill_14B_Name"] = 110,
    ["MCG_InGame_HumanSkill_14B_Info"] = 110,
    ["MCG_InGame_HumanSkill_14C_Name"] = 110,
    ["MCG_InGame_HumanSkill_14C_Info"] = 110,
    ["MCG_InGame_HumanSkill_15A_Name"] = 110,
    ["MCG_InGame_HumanSkill_15A_Info"] = 110,
    ["MCG_InGame_HumanSkill_15A_Position"] = 110,
    ["MCG_InGame_HumanSkill_15B_Name"] = 110,
    ["MCG_InGame_HumanSkill_15B_Info"] = 110,
    ["MCG_InGame_HumanSkill_15C_Name"] = 110,
    ["MCG_InGame_HumanSkill_15C_Info"] = 110,
    ["MCG_InGame_HumanSkill_16A_Name"] = 110,
    ["MCG_InGame_HumanSkill_16A_Info"] = 110,
    ["MCG_InGame_HumanSkill_16A_Position"] = 110,
    ["MCG_InGame_HumanSkill_16B_Name"] = 110,
    ["MCG_InGame_HumanSkill_16B_Info"] = 110,
    ["MCG_InGame_ShareDamage"] = 110,
    ["MCG_InGame_HumanSkill_16C_Name"] = 110,
    ["MCG_InGame_HumanSkill_16C_Info"] = 110,
    ["MCG_InGame_HumanSkill_17A_Name"] = 110,
    ["MCG_InGame_HumanSkill_17A_Info"] = 110,
    ["MCG_InGame_HumanSkill_17A_Position"] = 110,
    ["MCG_InGame_Button_GetOn"] = 110,
    ["MCG_InGame_Button_GetOff"] = 110,
    ["MCG_InGame_Button_Horn"] = 110,
    ["MCG_InGame_Button_GetOn_HurryUp"] = 110,
    ["MCG_InGame_CannotPlaceVehicleNotifyTip"] = 110,
    ["MCG_InGame_CannotPlaceGetOffNotifyTip"] = 110,
    ["MCG_InGame_HumanSkill_17B_Name"] = 110,
    ["MCG_InGame_HumanSkill_17B_Info"] = 110,
    ["MCG_InGame_HumanSkill_17C_Name"] = 110,
    ["MCG_InGame_HumanSkill_17C_Info"] = 110,
    ["MCG_InGame_HumanSkill_18A_Name"] = 110,
    ["MCG_InGame_HumanSkill_18A_Info"] = 110,
    ["MCG_InGame_HumanSkill_18A_Position"] = 110,
    ["MCG_InGame_HumanSkill_18B_Name"] = 110,
    ["MCG_InGame_HumanSkill_18B_Info"] = 110,
    ["MCG_InGame_HumanSkill_18C_Name"] = 110,
    ["MCG_InGame_HumanSkill_18C_Info"] = 110,
    ["MCG_InGame_HumanSkill_19A_Name"] = 111,
    ["MCG_InGame_HumanSkill_19A_Info"] = 111,
    ["MCG_InGame_HumanSkill_19A_Position"] = 111,
    ["MCG_InGame_HumanSkill_19B_Name"] = 111,
    ["MCG_InGame_HumanSkill_19B_Info"] = 111,
    ["MCG_InGame_HumanSkill_19C_Name"] = 111,
    ["MCG_InGame_HumanSkill_19C_Info"] = 111,
    ["MCG_InGame_HumanSkill_20A_Name"] = 111,
    ["MCG_InGame_HumanSkill_20A_Info"] = 111,
    ["MCG_InGame_HumanSkill_20A_Position"] = 111,
    ["MCG_InGame_HumanSkill_20B_Name"] = 111,
    ["MCG_InGame_HumanSkill_20B_Info"] = 111,
    ["MCG_InGame_HumanSkill_20C_Name"] = 111,
    ["MCG_InGame_HumanSkill_20C_Info"] = 111,
    ["MCG_InGame_HumanSkill_21A_Name"] = 111,
    ["MCG_InGame_HumanSkill_21A_Info"] = 111,
    ["MCG_InGame_HumanSkill_21A_Position"] = 111,
    ["MCG_InGame_HumanSkill_21A_Toast"] = 111,
    ["MCG_InGame_HumanSkill_21B_Name"] = 111,
    ["MCG_InGame_HumanSkill_21B_Info"] = 111,
    ["MCG_InGame_HumanSkill_21C_Name"] = 111,
    ["MCG_InGame_HumanSkill_21C_Info"] = 111,
    ["MCG_InGame_HumanSkill_22A_Name"] = 111,
    ["MCG_InGame_HumanSkill_22A_Info"] = 111,
    ["MCG_InGame_HumanSkill_22A_Position"] = 111,
    ["MCG_InGame_HumanSkill_22B_Name"] = 111,
    ["MCG_InGame_HumanSkill_22B_Info"] = 111,
    ["MCG_InGame_HumanSkill_22C_Name"] = 111,
    ["MCG_InGame_HumanSkill_22C_Info"] = 111,
    ["MCG_InGame_HumanSkill_23A_Name"] = 111,
    ["MCG_InGame_HumanSkill_23A_Info"] = 111,
    ["MCG_InGame_HumanSkill_23A_Position"] = 111,
    ["MCG_InGame_HumanSkill_23B_Name"] = 111,
    ["MCG_InGame_HumanSkill_23B_Info"] = 111,
    ["MCG_InGame_HumanSkill_23C_Name"] = 111,
    ["MCG_InGame_HumanSkill_23C_Info"] = 111,
    ["MCG_InGame_HumanSkill23_BTN_GetOn"] = 111,
    ["MCG_InGame_HumanSkill23_BTN_GetOff"] = 111,
    ["MCG_InGame_HumanSkill_24A_Name"] = 111,
    ["MCG_InGame_HumanSkill_24A_Info"] = 111,
    ["MCG_InGame_HumanSkill_24A_Position"] = 111,
    ["MCG_InGame_HumanSkill_24A_Toast"] = 111,
    ["MCG_InGame_HumanSkill_25A_Name"] = 111,
    ["MCG_InGame_HumanSkill_25A_Info"] = 111,
    ["MCG_InGame_HumanSkill_25A_Position"] = 111,
    ["MCG_InGame_HumanSkill_25B_Name"] = 111,
    ["MCG_InGame_HumanSkill_25B_Info"] = 111,
    ["MCG_InGame_HumanSkill_25C_Name"] = 111,
    ["MCG_InGame_HumanSkill_25C_Info"] = 111,
    ["MCG_InGame_HumanSkill_26A_Name"] = 111,
    ["MCG_InGame_HumanSkill_26A_Info"] = 111,
    ["MCG_InGame_HumanSkill_26A_Position"] = 111,
    ["MCG_InGame_HumanSkill_26B_Name"] = 111,
    ["MCG_InGame_HumanSkill_26B_Info"] = 111,
    ["MCG_InGame_HumanSkill_26C_Name"] = 111,
    ["MCG_InGame_HumanSkill_26C_Info"] = 111,
    ["MCG_InGame_HumanSkill_27A_Name"] = 111,
    ["MCG_InGame_HumanSkill_27A_Info"] = 111,
    ["MCG_InGame_HumanSkill_27A_NotifyTip"] = 111,
    ["MCG_InGame_HumanSkill_27A_NotifyTip2"] = 111,
    ["MCG_InGame_HumanSkill_27A_Position"] = 111,
    ["MCG_InGame_HumanSkill_27B_Name"] = 111,
    ["MCG_InGame_HumanSkill_27B_Info"] = 111,
    ["MCG_InGame_HumanSkill_27C_Name"] = 111,
    ["MCG_InGame_HumanSkill_27C_Info"] = 111,
    ["MCG_InGame_HumanSkill_28A_Name"] = 111,
    ["MCG_InGame_HumanSkill_28A_Info"] = 111,
    ["MCG_InGame_HumanSkill_28A_Position"] = 111,
    ["MCG_InGame_HumanSkill_28B_Name"] = 111,
    ["MCG_InGame_HumanSkill_28B_Info"] = 111,
    ["MCG_InGame_HumanSkill_28C_Name"] = 111,
    ["MCG_InGame_HumanSkill_28C_Info"] = 111,
    ["MCG_InGame_HumanSkill_29A_Name"] = 111,
    ["MCG_InGame_HumanSkill_29A_Info"] = 111,
    ["MCG_InGame_HumanSkill_29A_Position"] = 111,
    ["MCG_InGame_HumanSkill_29A_Toast"] = 111,
    ["MCG_InGame_HumanSkill_29B_Name"] = 111,
    ["MCG_InGame_HumanSkill_29B_Info"] = 111,
    ["MCG_InGame_HumanSkill_29C_Name"] = 111,
    ["MCG_InGame_HumanSkill_29C_Info"] = 111,
    ["MCG_InGame_HumanSkill_30A_Name"] = 111,
    ["MCG_InGame_HumanSkill_30A_Info"] = 111,
    ["MCG_InGame_HumanSkill_30A_Position"] = 111,
    ["MCG_InGame_HumanSkill_30B_Name"] = 111,
    ["MCG_InGame_HumanSkill_30B_Info"] = 111,
    ["MCG_InGame_HumanSkill_30C_Name"] = 111,
    ["MCG_InGame_HumanSkill_30C_Info"] = 111,
    ["MCG_InGame_HumanSkill_31A_Name"] = 111,
    ["MCG_InGame_HumanSkill_31A_Info"] = 111,
    ["MCG_InGame_HumanSkill_31A_Position"] = 111,
    ["MCG_InGame_HumanSkill_31A_Toast"] = 111,
    ["MCG_InGame_HumanSkill_31B_Name"] = 111,
    ["MCG_InGame_HumanSkill_31B_Info"] = 111,
    ["MCG_InGame_HumanSkill_31C_Name"] = 111,
    ["MCG_InGame_HumanSkill_31C_Info"] = 111,
    ["MCG_InGame_HumanSkill_32A_Name"] = 111,
    ["MCG_InGame_HumanSkill_32A_Info"] = 111,
    ["MCG_InGame_HumanSkill_32A_Position"] = 111,
    ["MCG_InGame_HumanSkill_32B_Name"] = 111,
    ["MCG_InGame_HumanSkill_32B_Info"] = 111,
    ["MCG_InGame_HumanSkill_32C_Name"] = 112,
    ["MCG_InGame_HumanSkill_32C_Info"] = 112,
    ["MCG_InGame_HumanSkill_33A_Name"] = 112,
    ["MCG_InGame_HumanSkill_33A_Position"] = 112,
    ["MCG_InGame_HumanSkill_33B_Name"] = 112,
    ["MCG_InGame_HumanSkill_33B_Info"] = 112,
    ["MCG_InGame_HumanSkill_33C_Name"] = 112,
    ["MCG_InGame_HumanSkill_33C_Info"] = 112,
    ["MCG_InGame_HumanSkill_33D_Name"] = 112,
    ["MCG_InGame_HumanSkill_33D_Info"] = 112,
    ["MCG_InGame_HumanSkill_Vip01A_Name"] = 112,
    ["MCG_InGame_HumanSkill_Vip01A_Info"] = 112,
    ["MCG_InGame_HumanSkill_Vip01A_Position"] = 112,
    ["MCG_InGame_HumanSkill_Vip01B_Name"] = 112,
    ["MCG_InGame_HumanSkill_Vip01B_Info"] = 112,
    ["MCG_InGame_HumanSkill_Vip01C_Name"] = 112,
    ["MCG_InGame_HumanSkill_Vip01C_Info"] = 112,
    ["MCG_InGame_GhostA_Name"] = 112,
    ["MCG_InGame_GhostA_Story"] = 112,
    ["MCG_InGame_GhostA_Skill_1_Name"] = 112,
    ["MCG_InGame_GhostA_Skill_1_Info"] = 112,
    ["MCG_InGame_GhostA_Skill_1_FullInfo"] = 112,
    ["MCG_InGame_GhostA_Skill_1_Trap"] = 112,
    ["MCG_InGame_TrapMaxNotifyTip"] = 112,
    ["MCG_InGame_GhostA_Skill_2_Name"] = 112,
    ["MCG_InGame_GhostA_Skill_2_Info"] = 112,
    ["MCG_InGame_GhostA_Skill_2_FullInfo"] = 112,
    ["MCG_InGame_GhostA_Skill_3_Name"] = 112,
    ["MCG_InGame_GhostA_Skill_3_Info"] = 112,
    ["MCG_InGame_GhostA_Skill_4_Name"] = 112,
    ["MCG_InGame_GhostA_Skill_4_Info"] = 112,
    ["MCG_InGame_GhostA_Skill_4_FullInfo"] = 112,
    ["MCG_InGame_Button_PullDown"] = 112,
    ["MCG_InGame_PullDown_Progress"] = 112,
    ["MCG_InGame_HumanState_Trapped"] = 112,
    ["MCG_InGame_StartTrappedNotifyTip"] = 112,
    ["MCG_InGame_EscapingTrap_Progress"] = 112,
    ["MCG_InGame_Trap_SaveProgress"] = 112,
    ["MCG_InGame_GhostB_Name"] = 112,
    ["MCG_InGame_GhostB_Story"] = 112,
    ["MCG_InGame_GhostB_Skill_1_Name"] = 112,
    ["MCG_InGame_GhostB_Skill_1_Info"] = 112,
    ["MCG_InGame_GhostB_Skill_1_FullInfo"] = 112,
    ["MCG_InGame_GhostB_Skill_2_Name"] = 112,
    ["MCG_InGame_GhostB_Skill_2_Info"] = 112,
    ["MCG_InGame_GhostB_Skill_2_FullInfo"] = 112,
    ["MCG_InGame_GhostB_Skill_3_Name"] = 112,
    ["MCG_InGame_GhostB_Skill_3_Info"] = 112,
    ["MCG_InGame_GhostC_Name"] = 112,
    ["MCG_InGame_GhostC_Info"] = 112,
    ["MCG_InGame_GhostC_Story"] = 112,
    ["MCG_InGame_GhostC_Skill_1_Name"] = 112,
    ["MCG_InGame_GhostC_Skill_1_Info"] = 112,
    ["MCG_InGame_GhostC_Skill_1_FullInfo"] = 112,
    ["MCG_InGame_GhostC_Skill_2_Name"] = 112,
    ["MCG_InGame_GhostC_Skill_2_Info"] = 112,
    ["MCG_InGame_GhostC_Skill_2_FullInfo"] = 112,
    ["MCG_InGame_GhostC_Skill_3_Name"] = 112,
    ["MCG_InGame_GhostC_Skill_3_Info"] = 112,
    ["MCG_InGame_GhostC_Skill_4_Name"] = 112,
    ["MCG_InGame_GhostC_Skill_4_Info"] = 112,
    ["MCG_InGame_GhostC_Skill_4_FullInfo"] = 112,
    ["MCG_InGame_GhostD_Name"] = 112,
    ["MCG_InGame_GhostD_ShortName"] = 112,
    ["MCG_InGame_GhostD_Story"] = 112,
    ["MCG_InGame_GhostD_Speak1"] = 112,
    ["MCG_InGame_GhostD_Speak2"] = 112,
    ["MCG_InGame_GhostD_Skill_1_Name"] = 112,
    ["MCG_InGame_GhostD_Skill_1_Info"] = 112,
    ["MCG_InGame_GhostD_Skill_1_FullInfo"] = 112,
    ["MCG_InGame_GhostD_Skill_2_Name"] = 112,
    ["MCG_InGame_GhostD_Skill_2_Info"] = 112,
    ["MCG_InGame_GhostD_Skill_2_FullInfo"] = 112,
    ["MCG_InGame_GhostD_Skill_3_Name"] = 112,
    ["MCG_InGame_GhostD_Skill_3_Info"] = 112,
    ["MCG_InGame_GhostD_Skill_3_FullInfo"] = 112,
    ["MCG_InGame_GhostE_Name"] = 112,
    ["MCG_InGame_GhostE_ShortName"] = 112,
    ["MCG_InGame_GhostE_Story"] = 112,
    ["MCG_InGame_GhostE_Speak1"] = 112,
    ["MCG_InGame_GhostE_Speak2"] = 112,
    ["MCG_InGame_GhostE_Skill_1_Name"] = 112,
    ["MCG_InGame_GhostE_Skill_1_Info"] = 112,
    ["MCG_InGame_GhostE_Skill_1_FullInfo"] = 112,
    ["MCG_InGame_GhostE_Skill_2_Name"] = 112,
    ["MCG_InGame_GhostE_Skill_2_Info"] = 112,
    ["MCG_InGame_GhostE_Skill_2_FullInfo"] = 112,
    ["MCG_InGame_GhostF_Name"] = 112,
    ["MCG_InGame_GhostF_ShortName"] = 112,
    ["MCG_InGame_GhostF_Story"] = 112,
    ["MCG_InGame_GhostF_Skill_1_Name"] = 112,
    ["MCG_InGame_GhostF_Skill_1_Info"] = 112,
    ["MCG_InGame_GhostF_Skill_1_FullInfo"] = 112,
    ["MCG_InGame_GhostF_Skill_2_Name"] = 112,
    ["MCG_InGame_GhostF_Skill_2_Info"] = 112,
    ["MCG_InGame_GhostF_Skill_2_FullInfo"] = 112,
    ["MCG_InGame_GhostG_Name"] = 112,
    ["MCG_InGame_GhostG_Story"] = 112,
    ["MCG_InGame_GhostG_Skill_1_Name"] = 112,
    ["MCG_InGame_GhostG_Skill_1_Info"] = 112,
    ["MCG_InGame_GhostG_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostG_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostG_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostG_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostH_Name"] = 113,
    ["MCG_InGame_GhostH_Story"] = 113,
    ["MCG_InGame_GhostH_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostH_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostH_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostH_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostH_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostH_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostH_Sad"] = 113,
    ["MCG_InGame_GhostH_Sad_Info"] = 113,
    ["MCG_InGame_GhostI_Name"] = 113,
    ["MCG_InGame_GhostI_ShortName"] = 113,
    ["MCG_InGame_GhostI_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostI_Skill_1_BtnStop"] = 113,
    ["MCG_InGame_GhostI_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostI_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostI_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostI_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostI_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostJ_Name"] = 113,
    ["MCG_InGame_GhostJ_ShortName"] = 113,
    ["MCG_InGame_GhostJ_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostJ_Skill_1_BtnFall"] = 113,
    ["MCG_InGame_GhostJ_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostJ_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostJ_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostJ_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostJ_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostK_Name"] = 113,
    ["MCG_InGame_GhostK_ShortName"] = 113,
    ["MCG_InGame_GhostK_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostK_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostK_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostK_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostK_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostK_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostL_Name"] = 113,
    ["MCG_InGame_GhostL_ShortName"] = 113,
    ["MCG_InGame_GhostL_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostL_Skill_1_BtnStop"] = 113,
    ["MCG_InGame_GhostL_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostL_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostL_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostL_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostL_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostM_Name"] = 113,
    ["MCG_InGame_GhostM_ShortName"] = 113,
    ["MCG_InGame_GhostM_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostM_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostM_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostM_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostM_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostM_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostM_Mind"] = 113,
    ["MCG_InGame_GhostM_Mind_Info"] = 113,
    ["MCG_InGame_GhostN_Name"] = 113,
    ["MCG_InGame_GhostN_ShortName"] = 113,
    ["MCG_InGame_GhostN_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostN_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostN_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostN_Skill_1_Toast1"] = 113,
    ["MCG_InGame_GhostN_Skill_1_Toast2"] = 113,
    ["MCG_InGame_GhostN_Skill_1_Toast3"] = 113,
    ["MCG_InGame_GhostN_Skill_1_Progress"] = 113,
    ["MCG_InGame_GhostN_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostN_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostN_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostN_Skill_2_Button"] = 113,
    ["MCG_InGame_GhostO_Name"] = 113,
    ["MCG_InGame_GhostO_ShortName"] = 113,
    ["MCG_InGame_GhostO_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostO_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostO_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostO_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostO_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostO_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostP_Name"] = 113,
    ["MCG_InGame_GhostP_ShortName"] = 113,
    ["MCG_InGame_GhostP_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostP_Skill_1_BtnStop"] = 113,
    ["MCG_InGame_GhostP_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostP_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostP_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostP_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostP_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_GhostQ_Name"] = 113,
    ["MCG_InGame_GhostQ_ShortName"] = 113,
    ["MCG_InGame_GhostQ_Skill_1_Name"] = 113,
    ["MCG_InGame_GhostQ_Skill_1_Info"] = 113,
    ["MCG_InGame_GhostQ_Skill_1_FullInfo"] = 113,
    ["MCG_InGame_GhostQ_Skill_1_ToastName"] = 113,
    ["MCG_InGame_GhostQ_Skill_1_ToastText"] = 113,
    ["MCG_InGame_GhostQ_Skill_2_Name"] = 113,
    ["MCG_InGame_GhostQ_Skill_2_Info"] = 113,
    ["MCG_InGame_GhostQ_Skill_2_FullInfo"] = 113,
    ["MCG_InGame_Ghost_BigKing"] = 113,
    ["MCG_InGame_Ghost_BigKing_Skill"] = 114,
    ["MCG_InGame_Ghost_BigKing_Skill_Info"] = 114,
    ["MCG_InGame_Ghost_DoubleKings_Skill1"] = 114,
    ["MCG_InGame_Ghost_DoubleKings_Skill2"] = 114,
    ["MCG_InGame_HumanGoal_Title"] = 114,
    ["MCG_InGame_HumanGoal1_Text"] = 114,
    ["MCG_InGame_HumanGoal2_Text"] = 114,
    ["MCG_InGame_HumanGoal3_Text"] = 114,
    ["MCG_InGame_GhostGoal_Title"] = 114,
    ["MCG_InGame_GhostGoal_Text"] = 114,
    ["MCG_InGame_HumanState_Down"] = 114,
    ["MCG_InGame_HumanState_Caught"] = 114,
    ["MCG_InGame_HumanState_Bound"] = 114,
    ["MCG_InGame_HumanState_Eaten"] = 114,
    ["MCG_InGame_HumanState_Escape"] = 114,
    ["MCG_InGame_HelpHuman_Button"] = 114,
    ["MCG_InGame_HelpHuman_Progress"] = 114,
    ["MCG_InGame_HelpHuman_DownMessage"] = 114,
    ["MCG_InGame_HelpHuman_Message"] = 114,
    ["MCG_InGame_HelpHuman_DoneMessage"] = 114,
    ["MCG_InGame_Struggle_Message"] = 114,
    ["MCG_InGame_HumanMachine_Name"] = 114,
    ["MCG_InGame_HumanMachine_Button"] = 114,
    ["MCG_InGame_HumanMachine_Progress"] = 114,
    ["MCG_InGame_HumanMachine_Story1"] = 114,
    ["MCG_InGame_HumanMachine_Story2"] = 114,
    ["MCG_InGame_HumanMachine_Story3"] = 114,
    ["MCG_InGame_HumanMachine_Story4"] = 114,
    ["MCG_InGame_HumanMachine_Story5"] = 114,
    ["MCG_InGame_HumanMachine_Minigame"] = 114,
    ["MCG_InGame_HumanMachine_Prefect"] = 114,
    ["MCG_InGame_HumanMachine_Good"] = 114,
    ["MCG_InGame_HumanMachine_Miss"] = 114,
    ["MCG_InGame_HumanMachine_Done_Human"] = 114,
    ["MCG_InGame_HumanMachine_Done_Ghost"] = 114,
    ["MCG_InGame_HumanMachine_AllDone_Human"] = 114,
    ["MCG_InGame_HumanMachine_AllDone_Ghost"] = 114,
    ["MCG_InGame_HumanMachine_Toast_Nezha"] = 114,
    ["MCG_InGame_Door_Name"] = 114,
    ["MCG_InGame_Door_Button"] = 114,
    ["MCG_InGame_Door_Progress"] = 114,
    ["MCG_InGame_Door_Open_Human"] = 114,
    ["MCG_InGame_Door_Open_Ghost"] = 114,
    ["MCG_InGame_GhostAnger_Progress"] = 114,
    ["MCG_InGame_GhostAnger_State"] = 114,
    ["MCG_InGame_GhostAnger_Message"] = 114,
    ["MCG_InGame_GhostAnger_Message2"] = 114,
    ["MCG_InGame_GhostAnger_Rule"] = 114,
    ["MCG_InGame_GhostAnger_Title1"] = 114,
    ["MCG_InGame_GhostAnger_Info1"] = 114,
    ["MCG_InGame_GhostAnger_Title2"] = 114,
    ["MCG_InGame_GhostAnger_Info2"] = 114,
    ["MCG_InGame_GhostAnger_Title3"] = 114,
    ["MCG_InGame_GhostAnger_Info3"] = 114,
    ["MCG_InGame_GhostAnger_Title4"] = 114,
    ["MCG_InGame_GhostAnger_Info4"] = 114,
    ["MCG_InGame_GhostAnger_Title5"] = 114,
    ["MCG_InGame_GhostAnger_Info5"] = 114,
    ["MCG_InGame_GhostAnger_Title6"] = 114,
    ["MCG_InGame_GhostAnger_Info6"] = 114,
    ["MCG_InGame_Struggle_Progress"] = 114,
    ["MCG_InGame_ClimbWindow"] = 114,
    ["MCG_InGame_Surface_Button"] = 114,
    ["MCG_InGame_CatchButton_Ghost"] = 114,
    ["MCG_InGame_CatchButton_Human"] = 114,
    ["MCG_InGame_CatchButton_Ghost2"] = 114,
    ["MCG_InGame_GhostMachine_Name"] = 114,
    ["MCG_InGame_GhostMachine_Botton"] = 114,
    ["MCG_InGame_GhostMachine_DeathProgress"] = 114,
    ["MCG_InGame_GhostMachine_Message"] = 114,
    ["MCG_InGame_GhostMachine_SaveBotton"] = 114,
    ["MCG_InGame_GhostMachine_SaveProgress"] = 114,
    ["MCG_InGame_GhostMachine_SaveMessage"] = 114,
    ["MCG_InGame_GhostMachine_FreeMessage"] = 114,
    ["MCG_InGame_GhostMachine_SaveMessageB"] = 114,
    ["MCG_InGame_GhostMachine_FreeMessageB"] = 114,
    ["MCG_InGame_3KGhostSkill_Bottle_Message"] = 114,
    ["MCG_InGame_3KGhostSkill_Bottle_MessageG"] = 114,
    ["MCG_InGame_3KGhostSkill_Gourd_Message"] = 114,
    ["MCG_InGame_3KGhostSkill_Gourd_MessageG"] = 114,
    ["MCG_InGame_3KGhostSkill_Gourd_AskHuman"] = 114,
    ["MCG_InGame_3KGhostSkill_Gourd_AskGhost"] = 114,
    ["MCG_InGame_3KGhostSkill_Gourd_Answer"] = 114,
    ["MCG_InGame_3KGhostSkill_Gourd_AnswerName"] = 114,
    ["MCG_InGame_4KGhostSkill_Gourd_AnswerNotice"] = 114,
    ["MCG_InGame_TrapBotton_Human"] = 114,
    ["MCG_InGame_TrapBotton_Ghost"] = 114,
    ["MCG_InGame_Win_Ghost"] = 114,
    ["MCG_InGame_Lose_Ghost"] = 114,
    ["MCG_InGame_Draw"] = 114,
    ["MCG_InGame_Win_Human"] = 114,
    ["MCG_InGame_Lose_Human"] = 114,
    ["MCG_Settlement_HumanWin"] = 114,
    ["MCG_Settlement_GhostWin"] = 114,
    ["MCG_Settlement_HumanState_Ingame"] = 114,
    ["MCG_Settlement_HumanState_Escape"] = 114,
    ["MCG_Settlement_HumanState_Lose"] = 114,
    ["MCG_Settlement_Human"] = 114,
    ["MCG_Settlement_Ghost"] = 114,
    ["MCG_Settlement_Score"] = 114,
    ["MCG_Settlement_Search"] = 115,
    ["MCG_Settlement_Save"] = 115,
    ["MCG_Settlement_Save2"] = 115,
    ["MCG_Settlement_Save3"] = 115,
    ["MCG_Settlement_Destroy"] = 115,
    ["MCG_Settlement_Kill"] = 115,
    ["MCG_Settlement_Hit"] = 115,
    ["MCG_Settlement_Pin"] = 115,
    ["MCG_Settlement_Eliminate"] = 115,
    ["MCG_ModeInstruction"] = 115,
    ["MCG_ModeInstruction_MainRule"] = 115,
    ["MCG_ModeInstruction_MainRule_Text"] = 115,
    ["MCG_ModeInstruction_MainRule_Text2"] = 115,
    ["MCG_ModeInstruction_Human"] = 115,
    ["MCG_ModeInstruction_HumanRule"] = 115,
    ["MCG_ModeInstruction_HumanSkill"] = 115,
    ["MCG_ModeInstruction_WinCondition"] = 115,
    ["MCG_ModeInstruction_WinCondition_HumanTitle"] = 115,
    ["MCG_ModeInstruction_WinCondition_GhostTitle"] = 115,
    ["MCG_ModeInstruction_WinCondition_Human"] = 115,
    ["MCG_ModeInstruction_WinCondition_Human2"] = 115,
    ["MCG_ModeInstruction_WinCondition_3KHumanText"] = 115,
    ["MCG_ModeInstruction_WinCondition_3KHumanText_Angel"] = 115,
    ["MCG_ModeInstruction_WinCondition_3KHumanText_Nezha"] = 115,
    ["MCG_ModeInstruction_WinCondition_3KGhostText"] = 115,
    ["MCG_ModeInstruction_WinCondition_3KGhostText_Angel"] = 115,
    ["MCG_ModeInstruction_WinCondition_3KGhostText_Nezha"] = 115,
    ["MCG_ModeInstruction_HumanMachine"] = 115,
    ["MCG_ModeInstruction_HumanMachine_Text"] = 115,
    ["MCG_ModeInstruction_Door"] = 115,
    ["MCG_ModeInstruction_Door_Text"] = 115,
    ["MCG_ModeInstruction_3K_PSD"] = 115,
    ["MCG_ModeInstruction_3K_PSD_Text"] = 115,
    ["MCG_ModeInstruction_3K_FireWall"] = 115,
    ["MCG_ModeInstruction_3K_FireWall_Text"] = 115,
    ["MCG_ModeInstruction_3K_Tang"] = 115,
    ["MCG_ModeInstruction_3K_Tang_Text"] = 115,
    ["MCG_ModeInstruction_3K_Wukong"] = 115,
    ["MCG_ModeInstruction_3K_Wukong_Text"] = 115,
    ["MCG_ModeInstruction_3K_Angel"] = 115,
    ["MCG_ModeInstruction_3K_Angel_Text"] = 115,
    ["MCG_ModeInstruction_3K_Werewolf"] = 115,
    ["MCG_ModeInstruction_3K_Werewolf_Text"] = 115,
    ["MCG_ModeInstruction_3K_Bajie"] = 115,
    ["MCG_ModeInstruction_3K_Bajie_Text"] = 115,
    ["MCG_ModeInstruction_3K_Shaseng"] = 115,
    ["MCG_ModeInstruction_3K_Shaseng_Text"] = 115,
    ["MCG_ModeInstruction_3K_MagicWeapon"] = 115,
    ["MCG_ModeInstruction_3K_MagicWeapon_Text"] = 115,
    ["MCG_ModeInstruction_Skill1"] = 115,
    ["MCG_ModeInstruction_Skill1_Text"] = 115,
    ["MCG_ModeInstruction_Skill2"] = 115,
    ["MCG_ModeInstruction_Skill2_Text"] = 115,
    ["MCG_ModeInstruction_Skill3"] = 115,
    ["MCG_ModeInstruction_Skill3_Text"] = 115,
    ["MCG_ModeInstruction_Skill4"] = 115,
    ["MCG_ModeInstruction_Skill4_Text"] = 115,
    ["MCG_ModeInstruction_Skill5"] = 115,
    ["MCG_ModeInstruction_Skill5_Text"] = 115,
    ["MCG_ModeInstruction_Skill6"] = 115,
    ["MCG_ModeInstruction_Skill6_Text"] = 115,
    ["MCG_ModeInstruction_Skill7"] = 115,
    ["MCG_ModeInstruction_Skill7_Text"] = 115,
    ["MCG_ModeInstruction_Skill8"] = 115,
    ["MCG_ModeInstruction_Skill8_Text"] = 115,
    ["MCG_ModeInstruction_Skill9"] = 115,
    ["MCG_ModeInstruction_Skill9_Text"] = 115,
    ["MCG_ModeInstruction_Skill10"] = 115,
    ["MCG_ModeInstruction_Skill10_Text"] = 115,
    ["MCG_ModeInstruction_Skill11"] = 115,
    ["MCG_ModeInstruction_Skill11_Text"] = 115,
    ["MCG_ModeInstruction_3KSkillTang"] = 115,
    ["MCG_ModeInstruction_3KSkillTang_Text"] = 115,
    ["MCG_ModeInstruction_3KSkill9"] = 115,
    ["MCG_ModeInstruction_3KSkill9_Text"] = 115,
    ["MCG_ModeInstruction_3KSkill10"] = 115,
    ["MCG_ModeInstruction_3KSkill10_Text"] = 115,
    ["MCG_ModeInstruction_3KSkill11"] = 115,
    ["MCG_ModeInstruction_3KSkill11_Text"] = 115,
    ["MCG_ModeInstruction_Skill12"] = 115,
    ["MCG_ModeInstruction_Skill12_Text"] = 115,
    ["MCG_ModeInstruction_Skill13"] = 115,
    ["MCG_ModeInstruction_Skill13_Text"] = 115,
    ["MCG_ModeInstruction_3KSkill12"] = 115,
    ["MCG_ModeInstruction_3KSkill12_Text"] = 115,
    ["MCG_ModeInstruction_3KSkill13"] = 115,
    ["MCG_ModeInstruction_3KSkill13_Text"] = 115,
    ["MCG_ModeInstruction_Skill14"] = 115,
    ["MCG_ModeInstruction_Skill14_Text"] = 115,
    ["MCG_ModeInstruction_Skill15"] = 115,
    ["MCG_ModeInstruction_Skill15_Text"] = 115,
    ["MCG_ModeInstruction_3KSkill15"] = 115,
    ["MCG_ModeInstruction_3KSkill15_Text"] = 115,
    ["MCG_ModeInstruction_Skill16"] = 115,
    ["MCG_ModeInstruction_Skill16_Text"] = 115,
    ["MCG_ModeInstruction_3KSkillAngel"] = 115,
    ["MCG_ModeInstruction_3KSkillAngel_Text"] = 115,
    ["MCG_ModeInstruction_3KSkillWerewolf"] = 115,
    ["MCG_ModeInstruction_3KSkillWerewolf_Text"] = 115,
    ["MCG_ModeInstruction_3KSkill7"] = 115,
    ["MCG_ModeInstruction_3KSkill7_Text"] = 116,
    ["MCG_ModeInstruction_3KSkill10_Vampire"] = 116,
    ["MCG_ModeInstruction_3KSkill10_Text_Vampire"] = 116,
    ["MCG_ModeInstruction_Skill17"] = 116,
    ["MCG_ModeInstruction_Skill17_Text"] = 116,
    ["MCG_ModeInstruction_Skill18"] = 116,
    ["MCG_ModeInstruction_Skill18_Text"] = 116,
    ["MCG_ModeInstruction_3KSkill17"] = 116,
    ["MCG_ModeInstruction_3KSkill17_Text"] = 116,
    ["MCG_ModeInstruction_3KSkill18"] = 116,
    ["MCG_ModeInstruction_3KSkill18_Text"] = 116,
    ["MCG_ModeInstruction_3KSkill5"] = 116,
    ["MCG_ModeInstruction_3KSkill5_Text"] = 116,
    ["MCG_ModeInstruction_VIPSkill1"] = 116,
    ["MCG_ModeInstruction_VIPSkill1_Text"] = 116,
    ["MCG_ModeInstruction_Ghost"] = 116,
    ["MCG_ModeInstruction_GhostRule"] = 116,
    ["MCG_ModeInstruction_GhostSkill"] = 116,
    ["MCG_ModeInstruction_WinCondition_Ghost"] = 116,
    ["MCG_ModeInstruction_WinCondition_Ghost2"] = 116,
    ["MCG_ModeInstruction_FindHuman"] = 116,
    ["MCG_ModeInstruction_FindHuman_Text"] = 116,
    ["MCG_ModeInstruction_GhostMachine"] = 116,
    ["MCG_ModeInstruction_GhostMachine_Text"] = 116,
    ["MCG_ModeInstruction_Anger"] = 116,
    ["MCG_ModeInstruction_Anger_Text"] = 116,
    ["MCG_ModeInstruction_Ghost1"] = 116,
    ["MCG_ModeInstruction_Ghost1_Text"] = 116,
    ["MCG_ModeInstruction_Ghost2"] = 116,
    ["MCG_ModeInstruction_Ghost2_Text"] = 116,
    ["MCG_ModeInstruction_Ghost3_Text1"] = 116,
    ["MCG_ModeInstruction_Ghost3_Text2"] = 116,
    ["MCG_ModeInstruction_Ghost4_Text"] = 116,
    ["MCG_ModeInstruction_Ghost5_Text"] = 116,
    ["MCG_ModeInstruction_Ghost6_Text"] = 116,
    ["MCG_ModeInstruction_Ghost7_Text"] = 116,
    ["MCG_ModeInstruction_Ghost8_Text"] = 116,
    ["MCG_ModeInstruction_Ghost9_Text"] = 116,
    ["MCG_ModeInstruction_Ghost10_Text"] = 116,
    ["MCG_ModeInstruction_Ghost11_Text"] = 116,
    ["MCG_ModeInstruction_Ghost12_Text"] = 116,
    ["MCG_ModeInstruction_Ghost13_Text"] = 116,
    ["MCG_ModeInstruction_Ghost14_Text"] = 116,
    ["MCG_ModeInstruction_Ghost15_Text"] = 116,
    ["MCG_ModeInstruction_Ghost16_Text"] = 116,
    ["MCG_ModeInstruction_Ghost17_Text"] = 116,
    ["MCG_ModeInstruction_NoticeGhost"] = 116,
    ["MCG_ModeInstruction_NoticeGhost_Text"] = 116,
    ["MCG_ModeInstruction_Board"] = 116,
    ["MCG_ModeInstruction_Board_Text"] = 116,
    ["MCG_ModeInstruction_Save"] = 116,
    ["MCG_ModeInstruction_Save_Text"] = 116,
    ["MCG_ModeInstruction_Save2"] = 116,
    ["MCG_ModeInstruction_Save2_Text"] = 116,
    ["MCG_ModeInstruction_ScanHuman"] = 116,
    ["MCG_ModeInstruction_ScanHuman_Text"] = 116,
    ["MCG_InGame_Speech_Human"] = 116,
    ["MCG_InGame_Speech_Ghost"] = 116,
    ["MCG_InGame_Speech_Side"] = 116,
    ["MCG_InGame_Speech_NearBoss"] = 116,
    ["MCG_InGame_Speech_NeadHelp"] = 116,
    ["MCG_InGame_Speech_Saveme"] = 116,
    ["MCG_InGame_Speech_Gotohelp"] = 116,
    ["MCG_InGame_Speech_GotoSave"] = 116,
    ["MCG_InGame_Speech_HumanMachine"] = 116,
    ["MCG_InGame_Speech_Door"] = 116,
    ["MCG_InGame_Speech_Escape"] = 116,
    ["MCG_InGame_Speech_DonotSave"] = 116,
    ["MCG_InGame_Speech_FollowMe"] = 116,
    ["MCG_InGame_Speech_FacetoBoss"] = 116,
    ["MCG_InGame_Speech_ThankYou"] = 116,
    ["MCG_InGame_Speech_Ghost1"] = 116,
    ["MCG_InGame_Speech_Ghost2"] = 116,
    ["MCG_InGame_Speech_Ghost3"] = 116,
    ["MCG_InGame_Speech_Ghost4"] = 116,
    ["MCG_InGame_Speech_Ghost5"] = 116,
    ["MCG_InGame_Speech_Ghost6"] = 116,
    ["MCG_InGame_PropPonit"] = 116,
    ["MCG_InGame_StartGameCount"] = 116,
    ["MCG_InGame_OverallFixSpeedUp_Notice"] = 116,
    ["MCG_InGame_GhostEnhance_Notice"] = 116,
    ["MCG_InGame_Dying_DeathProgress"] = 116,
    ["MCG_InGame_cooperateFixSpeedUp_Progress"] = 116,
    ["MCG_InGame_OverallFixSpeedUp_Progress"] = 116,
    ["MCG_InGame_TangsengFixSpeedUp_Progress"] = 116,
    ["MCG_InGame_AngelFixSpeedUp_Progress"] = 116,
    ["StarBless_Active_No_In_Starchart_Tip"] = 116,
    ["StarBless_Active_Starchart_AlreadyUse_Tip"] = 116,
    ["Hamburger_Active_NoValidTarget_Tip"] = 116,
    ["Grenade_Active_NoValidTarget_Tip"] = 116,
    ["MCG_EndGame_Player"] = 116,
    ["MCG_Loading_Headphones"] = 116,
    ["MCG_InGame_SOS"] = 116,
    ["MCG_InGame_FTUE_HumanMachien"] = 116,
    ["MCG_InGame_FTUE_Minigame"] = 116,
    ["MCG_InGame_FTUE_GhostMachine"] = 116,
    ["MCG_InGame_FTUE_Save"] = 116,
    ["MCG_InGame_Chat_Tips"] = 116,
    ["MCG_InGame_Struggle_Button"] = 116,
    ["MCG_InGame_QuickChat_Button"] = 116,
    ["MCG_InGame_FinalFight_Human"] = 117,
    ["MCG_InGame_FinalFight_Ghost"] = 117,
    ["MCG_InGame_FinalFight_OpenDoor"] = 117,
    ["MCG_InGame_ChangeBoss_Button"] = 117,
    ["OnStarChartFixSuccessTip"] = 117,
    ["MCG_InGame_GhostBecomeBigKing"] = 117,
    ["MCG_InGame_Giveup"] = 117,
    ["MCG_InGame_Giveup_Yes"] = 117,
    ["MCG_InGame_Giveup_No"] = 117,
    ["MCG_InGame_Giveup_Yes2"] = 117,
    ["MCG_InGame_Giveup_No2"] = 117,
    ["MCG_InGame_Giveup_Cancel"] = 117,
    ["MCG_InGame_Giveup_Wait"] = 117,
    ["MCG_InGame_Giveup_Wait2"] = 117,
    ["MCG_InGame_Giveup_Commit"] = 117,
    ["MCG_InGame_Giveup_NeedConform1"] = 117,
    ["MCG_InGame_Giveup_NeedConform2"] = 117,
    ["MCG_InGame_Giveup_Fail"] = 117,
    ["MCG_InGame_Giveup_Human"] = 117,
    ["MCG_InGame_Giveup_Human_ForGhost"] = 117,
    ["MCG_InGame_Giveup_Ghost"] = 117,
    ["MCG_InGame_Giveup_Ghost_ForHuman"] = 117,
    ["MCG_InGame_Idle"] = 117,
    ["MCG_InGame_IdleAuto"] = 117,
    ["MCG_InGame_IdleCheck"] = 117,
    ["MCG_InGame_IdleSystemControl"] = 117,
    ["MCG_InGame_IdleIKnow"] = 117,
    ["MCG_InGame_IdleCancel"] = 117,
    ["MCG_InGame_IdlePlayer_NotifyTip"] = 117,
    ["MCG_BossDetectedStartNotifyTip"] = 117,
    ["MCG_BossAttack_Charge"] = 117,
    ["MCG_StarBless_Active_Detection_Tip"] = 117,
    ["MCG_StarBless_Active_NoEnergy_Tip"] = 117,
    ["MCG_ModeInstruction_BigLittleKing"] = 117,
    ["MCG_ModeInstruction_BigLittleKing_Info"] = 117,
    ["MCG_ModeInstruction_BigLittleKing_SkillTitle"] = 117,
    ["MCG_ModeInstruction_BigLittleKing_SkillInfo"] = 117,
    ["MCG_ModeInstruction_3Kings_SkillInfo"] = 117,
    ["MCG_ModeInstruction_ShutDownMachine"] = 117,
    ["MCG_ModeInstruction_ShutDownMachine_Info"] = 117,
    ["MCG_InGame_ShutDownMachine"] = 117,
    ["MCG_InGame_ShutDownMachine_Info"] = 117,
    ["MCG_InGame_ShutDownMachine_NotifyTip"] = 117,
    ["MCG_InGame_ShutDownMachine_Rate"] = 117,
    ["MCG_InGame_ShutDownMachine_Notice"] = 117,
    ["MCG_InGame_ShutDownMachine_Recovery_NotifyTip"] = 117,
    ["MCG_ModeInstruction_BigKing"] = 117,
    ["MCG_InGame_3Kings"] = 117,
    ["MCG_InGame_3Kings_Rule"] = 117,
    ["MCG_InGame_3Kings_Rule_Vampire"] = 117,
    ["MCG_InGame_3Kings_Rule_Nezha"] = 117,
    ["MCG_InGame_3KHumanMachine_Story1"] = 117,
    ["MCG_InGame_3KHumanMachine_Story2"] = 117,
    ["MCG_InGame_3KHumanMachine_Story3"] = 117,
    ["MCG_InGame_3KHumanMachine_Story1_Fire"] = 117,
    ["MCG_InGame_3KHumanMachine_Story2_Fire"] = 117,
    ["MCG_InGame_3KHumanMachine_Story3_Fire"] = 117,
    ["MCG_InGame_3KHumanMachine_Story1_Vampire"] = 117,
    ["MCG_InGame_3KHumanMachine_Story2_Vampire"] = 117,
    ["MCG_InGame_3KHumanMachine_Story3_Vampire"] = 117,
    ["MCG_InGame_3KHumanMachine_Story1_Nezha"] = 117,
    ["MCG_InGame_3KHumanMachine_Story2_Nezha"] = 117,
    ["MCG_InGame_3KHumanMachine_Story3_Nezha"] = 117,
    ["MCG_InGame_Ghost_ChooseRoleAndSkill"] = 117,
    ["MCG_InGame_Ghost_ChooseRole"] = 117,
    ["MCG_InGame_Ghost_ChooseSkill"] = 117,
    ["MCG_InGame_NotRepeatable"] = 117,
    ["MCG_InGame_Chosen"] = 117,
    ["MCG_InGame_Forbidden"] = 117,
    ["MCG_InGame_Tang"] = 117,
    ["MCG_InGame_BecomeTang"] = 117,
    ["MCG_InGame_Tang_Buff"] = 117,
    ["MCG_InGame_Tang_Defeat"] = 117,
    ["MCG_InGame_Tang_Back"] = 117,
    ["MCG_InGame_Tang_Back02"] = 117,
    ["MCG_InGame_Tang_Pick"] = 117,
    ["MCG_InGame_Angel"] = 117,
    ["MCG_InGame_BecomeAngel"] = 117,
    ["MCG_InGame_Angel_Buff"] = 117,
    ["MCG_InGame_Angel_Defeat"] = 117,
    ["MCG_InGame_Angel_Back"] = 117,
    ["MCG_InGame_Angel_Pick"] = 117,
    ["MCG_InGame_Nezha"] = 117,
    ["MCG_InGame_BecomeNezha"] = 117,
    ["MCG_InGame_Nezha_Buff"] = 117,
    ["MCG_InGame_Nezha_Defeat"] = 117,
    ["MCG_InGame_Nezha_Back"] = 117,
    ["MCG_InGame_Nezha_Pick"] = 117,
    ["MCG_InGame_TakeOff"] = 117,
    ["MCG_InGame_Wukong"] = 117,
    ["MCG_InGame_BecomeWukong"] = 117,
    ["MCG_InGame_WuKong_Defeat"] = 117,
    ["MCG_InGame_Werewolf"] = 117,
    ["MCG_InGame_BecomeWerewolf"] = 117,
    ["MCG_InGame_Werewolf_Defeat"] = 117,
    ["MCG_InGame_MagicWeaponBox"] = 117,
    ["MCG_InGame_MagicWeaponBox_NotifyTip"] = 117,
    ["MCG_InGame_MagicWeaponBox_NotifyTip2"] = 117,
    ["MCG_InGame_MagicWeaponBox_Vampire"] = 117,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Vampire"] = 117,
    ["MCG_InGame_MagicWeaponBox_NotifyTip2_Vampire"] = 118,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Nezha1"] = 118,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Nezha2"] = 118,
    ["MCG_InGame_MagicWeaponBox_NotifyTip_Nezha3"] = 118,
    ["MCG_InGame_MagicWeaponHandIn_Toast_Nezha1"] = 118,
    ["MCG_InGame_MagicWeaponHandIn_Toast_Nezha2"] = 118,
    ["MCG_InGame_MagicWeaponHandIn_Toast_Nezha3"] = 118,
    ["MCG_InGame_Open"] = 118,
    ["MCG_InGame_UnpackMagicWeaponBox"] = 118,
    ["MCG_InGame_CannotPickMagicWeapon"] = 118,
    ["MCG_InGame_CannotPickMagicWeapon_Tang"] = 118,
    ["MCG_InGameGotMagicWeapon_NotifyTip"] = 118,
    ["MCG_InGame_UnpackMagicWeaponBox_Vampire"] = 118,
    ["MCG_InGame_CannotPickMagicWeapon_Vampire"] = 118,
    ["MCG_InGame_CannotPickMagicWeapon_Angel"] = 118,
    ["MCG_InGame_CannotPickMagicWeapon_Nezha"] = 118,
    ["MCG_InGameGotMagicWeapon_Vampire_NotifyTip"] = 118,
    ["MCG_InGameGotMagicWeapon_NotifyTipShan"] = 118,
    ["MCG_InGame_3KDoor_Open_Human"] = 118,
    ["MCG_InGame_3KDoor_Open_Ghost"] = 118,
    ["MCG_InGame_3KDoor_Open_OnlyTang"] = 118,
    ["MCG_InGame_3KDoor_Open_Human_Vampire"] = 118,
    ["MCG_InGame_3KDoor_Open_Ghost_Vampire"] = 118,
    ["MCG_InGame_3KDoor_Open_OnlyAngel"] = 118,
    ["MCG_InGame_3KDoor_Open_Human_Nezha"] = 118,
    ["MCG_InGame_3KDoor_Open_Ghost_Nezha"] = 118,
    ["MCG_InGame_3KDoor_Open_OnlyNezha"] = 118,
    ["MCG_InGame_GhostDoor_Open1"] = 118,
    ["MCG_InGame_GhostDoor_Open2"] = 118,
    ["MCG_InGame_GhostDoor_Open3"] = 118,
    ["MCG_InGame_GhostDoor_Open1_Fire"] = 118,
    ["MCG_InGame_GhostDoor_Open2_Fire"] = 118,
    ["MCG_InGame_GhostDoor_Open3_Fire"] = 118,
    ["MCG_InGame_GhostDoor_Open1_Vampire"] = 118,
    ["MCG_InGame_GhostDoor_Open2_Vampire"] = 118,
    ["MCG_InGame_GhostDoor_Open3_Vampire"] = 118,
    ["MCG_InGame_GhostDoor_Tip1"] = 118,
    ["MCG_InGame_GhostDoor_Tip2"] = 118,
    ["MCG_InGame_GhostDoor_Tip_Nezha"] = 118,
    ["MCG_InGame_3KHumanGoal1"] = 118,
    ["MCG_InGame_3KHumanGoal2"] = 118,
    ["MCG_InGame_3KHumanGoal3"] = 118,
    ["MCG_InGame_3KHumanGoal4"] = 118,
    ["MCG_InGame_3KHumanGoal1_Fire"] = 118,
    ["MCG_InGame_3KHumanGoal2_Fire"] = 118,
    ["MCG_InGame_3KHumanGoal3_Fire"] = 118,
    ["MCG_InGame_3KHumanGoal4_Fire"] = 118,
    ["MCG_InGame_3KHumanGoal1_Vampire"] = 118,
    ["MCG_InGame_3KHumanGoal2_Vampire"] = 118,
    ["MCG_InGame_3KHumanGoal3_Vampire"] = 118,
    ["MCG_InGame_3KHumanGoal4_Vampire"] = 118,
    ["MCG_InGame_3KHumanGoal1_Nezha"] = 118,
    ["MCG_InGame_3KHumanGoal2_Nezha"] = 118,
    ["MCG_InGame_3KHumanGoal3_Nezha"] = 118,
    ["MCG_InGame_3KHumanGoal4_Nezha"] = 118,
    ["MCG_InGame_3KGhostGoal1"] = 118,
    ["MCG_InGame_3KGhostGoal2"] = 118,
    ["MCG_InGame_3KGhostGoal3"] = 118,
    ["MCG_InGame_3KGhostGoal4"] = 118,
    ["MCG_InGame_3KGhostGoal5"] = 118,
    ["MCG_InGame_3KGhostGoal1_Fire"] = 118,
    ["MCG_InGame_3KGhostGoal2_Fire"] = 118,
    ["MCG_InGame_3KGhostGoal3_Fire"] = 118,
    ["MCG_InGame_3KGhostGoal4_Fire"] = 118,
    ["MCG_InGame_3KGhostGoal5_Fire"] = 118,
    ["MCG_InGame_3KGhostGoal1_Vampire"] = 118,
    ["MCG_InGame_3KGhostGoal2_Vampire"] = 118,
    ["MCG_InGame_3KGhostGoal3_Vampire"] = 118,
    ["MCG_InGame_3KGhostGoal4_Vampire"] = 118,
    ["MCG_InGame_3KGhostGoal5_Vampire"] = 118,
    ["MCG_InGame_3KGhostGoal1_Nezha"] = 118,
    ["MCG_InGame_3KGhostGoal2_Nezha"] = 118,
    ["MCG_InGame_3KGhostGoal3_Nezha"] = 118,
    ["MCG_InGame_3KGhostGoal4_Nezha"] = 118,
    ["MCG_InGame_3KGhostGoal5_Nezha"] = 118,
    ["MCG_InGame_3KNezhaMiao_Toast"] = 118,
    ["MCG_InGame_CountDown"] = 118,
    ["MCG_InGame_Teleport_Tip"] = 118,
    ["MCG_InGame_SaveFromVase_Tip"] = 118,
    ["MCG_InGame_SaveFromGourd_Tip"] = 118,
    ["MCG_InGame_Drop"] = 118,
    ["MCG_InGame_DropConfirm"] = 118,
    ["MCG_InGame_DropConfirm_Vampire"] = 118,
    ["MCG_InGame_Cancel"] = 118,
    ["MCG_InGame_DropYes"] = 118,
    ["MCG_InGame_TangTip_PSD"] = 118,
    ["MCG_InGame_TangTip_ToCave"] = 118,
    ["MCG_InGame_TangTip_Escape"] = 118,
    ["MCG_InGame_TangTip_EscapeTang"] = 118,
    ["MCG_InGame_TangTip_CanPick"] = 118,
    ["MCG_InGame_AngelTip_PSD"] = 118,
    ["MCG_InGame_AngelTip_ToCave"] = 118,
    ["MCG_InGame_AngelTip_Escape"] = 118,
    ["MCG_InGame_AngelTip_EscapeAngel"] = 118,
    ["MCG_InGame_AngelTip_CanPick"] = 118,
    ["MCG_InGame_NezhaTip_PSD"] = 118,
    ["MCG_InGame_NezhaTip_ToCave"] = 118,
    ["MCG_InGame_NezhaTip_Escape"] = 118,
    ["MCG_InGame_NezhaTip_EscapeNezha"] = 118,
    ["MCG_InGame_NezhaTip_CanPick"] = 118,
    ["MCG_InGame_Ready_VIPNotice_Name"] = 119,
    ["MCG_InGame_Ready_VIPNotice_Skill"] = 119,
    ["MCG_InGame_Ready_VIPOnly"] = 119,
    ["MCG_InGame_Ready_VIPOnlyTips"] = 119,
    ["MCG_InGame_VIP01"] = 119,
    ["MCG_InGame_Ready_ChooseSkillPool"] = 119,
    ["MCG_InGame_Spring"] = 119,
    ["MCG_InGame_Spring_CanUse"] = 119,
    ["MCG_InGame_Spring_CanNotUse"] = 119,
    ["MCG_InGame_Spring_Used"] = 119,
    ["MCG_InGame_Spring_Need"] = 119,
    ["MCG_InGame_Spring_Using"] = 119,
    ["MCG_InGame_Spring_Button"] = 119,
    ["MCG_ModeInstruction_Spring"] = 119,
    ["MCG_ModeInstruction_Spring_Text"] = 119,
    ["MCG_InGame_Prop_Door"] = 119,
    ["MCG_InGame_Prop_HumanMachine"] = 119,
    ["MCG_InGame_Prop_BossMachine"] = 119,
    ["MCG_InGame_Prop_Roadblock"] = 119,
    ["MCG_InGame_Prop_Window"] = 119,
    ["MCG_InGame_Prop_Spring"] = 119,
    ["MCG_InGame_Prop_MagicWeaponBox"] = 119,
    ["MCG_InGame_Prop_MagicWeaponBox_Vampire"] = 119,
    ["MCG_ReportBug_FeedBackSuccess"] = 119,
    ["MCG_ReportBug_SelectFeedBackEvent"] = 119,
    ["MCG_ReportBug_EnterAtLeast20Words"] = 119,
    ["MCG_ReportBug_EnterUpTo200Words"] = 119,
    ["MCG_ReportBug_InputTips"] = 119,
    ["MCG_PropType_BossSkin"] = 119,
    ["MCG_PropType_PropSkin"] = 119,
    ["MCG_CommonSkill_NameText"] = 119,
    ["MCG_CommonSkill_HideText"] = 119,
    ["MCG_InLevel_Reputation_MyScore_LineOne"] = 119,
    ["MCG_InLevel_Reputation_MyScore_LineTwo"] = 119,
    ["MCG_InLevel_Reputation_OffenseRule_LineOne"] = 119,
    ["MCG_InLevel_Reputation_OffenseRule_LineTwo"] = 119,
    ["MCG_InLevel_Reputation_OffenseRule_LineThree"] = 119,
    ["MCG_InLevel_Reputation_OffenseRule_LineFour"] = 119,
    ["MCG_InLevel_Reputation_CreditRule_LineOne"] = 119,
    ["MCG_InLevel_Reputation_CreditRule_LineTwo"] = 119,
    ["MCG_InLevel_Reputation_CreditRule_LineThree"] = 119,
    ["MCG_InLevel_Main_NoCompleteWithReputation_Box"] = 119,
    ["MCG_InLevel_Surrender_RemainingTime"] = 119,
    ["MCG_PlayGuide_StartGame"] = 119,
    ["MCG_MEOWMEOW_START_TIPS"] = 119,
    ["MCG_PlayGuide_Freshman"] = 119,
    ["MCG_PlayGuide_Senior"] = 119,
    ["MCG_PlayGuide_Enter"] = 119,
    ["MCG_PlayGuide_Skip"] = 119,
    ["MCG_PlayGuide_EnterTips"] = 119,
    ["MCG_MatchCustom_Three"] = 119,
    ["MCG_MatchCustom_One"] = 119,
    ["MCG_MatchCustom_Star"] = 119,
    ["MCG_MatchCustom_Boss"] = 119,
    ["MCG_MEOWMEOW_DANCING_TIPS"] = 119,
    ["MCG_IdentityScore_ToBeSettled"] = 119,
    ["MCG_InLevel_Surrender_Again"] = 119,
    ["MCG_InGame_HumanSkill_MagicCube_Name"] = 119,
    ["MCG_InGame_HumanSkill_MagicCube_Info"] = 119,
    ["MCG_InGame_HumanSkill_MagicCube_Position"] = 119,
    ["Chase_GameDetail_NotFindData"] = 119,
}
table_TextEntryData_IndexTable.Version = {
}
return table_TextEntryData_IndexTable
