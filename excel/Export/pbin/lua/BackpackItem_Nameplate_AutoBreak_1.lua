--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 铭牌

local v0 = 3

local v1 = 4

local v2 = 1

local data = {
[820094] = {
id = 820094,
quality = 3,
name = "铠-战甲名牌",
desc = "峡谷竞技活动获得",
icon = "CDN:Icon_Arena_NameFrame_001",
resourceConf = {
bg = "CDN:T_Arena_NameFrame_bg_001"
}
},
[820095] = {
id = 820095,
quality = 3,
name = "甄嬛传",
desc = "冲刺新赛季活动获得",
icon = "CDN:Icon_NameFrame_094",
resourceConf = {
bg = "CDN:T_NameFrame_bg_094"
}
},
[820096] = {
id = 820096,
quality = 3,
name = "远征之星",
desc = "冲上云霄，飞向星辰",
icon = "CDN:Icon_NameFrame_095",
resourceConf = {
bg = "CDN:T_NameFrame_bg_095"
}
},
[820097] = {
id = 820097,
lowVer = "1.3.12.1",
name = "夏日微甜",
desc = "甜心乐园—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_096",
resourceConf = {
bg = "D_Namebox_096"
},
isDynamic = 1,
beginTime = {
seconds = 1721318400
}
},
[820098] = {
id = 820098,
lowVer = "1.3.12.1",
quality = 4,
name = "乐乐",
desc = "甜心乐园通行证获得",
icon = "CDN:Icon_NameFrame_098",
resourceConf = {
bg = "CDN:T_NameFrame_bg_098"
},
beginTime = {
seconds = 1721318400
}
},
[820099] = {
id = 820099,
lowVer = "1.3.12.1",
quality = 4,
name = "美美",
desc = "甜心乐园通行证获得",
icon = "CDN:Icon_NameFrame_099",
resourceConf = {
bg = "CDN:T_NameFrame_bg_099"
},
beginTime = {
seconds = 1721318400
}
},
[820100] = {
id = 820100,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.12.1",
quality = 3,
name = "娱乐冒险家",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_100",
resourceConf = {
bg = "CDN:T_NameFrame_bg_100"
}
},
[820101] = {
id = 820101,
lowVer = "1.3.12.1",
quality = 3,
name = "甜心幻梦",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_097",
resourceConf = {
bg = "CDN:T_NameFrame_bg_097"
},
beginTime = {
seconds = 1721318400
}
},
[820102] = {
id = 820102,
lowVer = "1.3.12.36",
quality = 3,
name = "长相思",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_102",
resourceConf = {
bg = "CDN:T_NameFrame_bg_102"
},
beginTime = {
seconds = 1721836800
}
},
[820103] = {
id = 820103,
quality = 4,
name = "星贵妃",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_101",
resourceConf = {
bg = "CDN:T_NameFrame_bg_101"
}
},
[820104] = {
id = 820104,
quality = 3,
name = "【限时】泡泡大王",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_NameFrame_105",
resourceConf = {
bg = "CDN:T_NameFrame_bg_105"
},
beginTime = {
seconds = 1722528000
}
},
[820105] = {
id = 820105,
quality = 3,
name = "蔡文姬-百花生辰",
desc = "峡谷相逢通行证获得",
icon = "CDN:Icon_Arena_NameFrame_002",
resourceConf = {
bg = "CDN:T_Arena_NameFrame_bg_002"
},
showInView = 0
},
[820106] = {
id = 820106,
lowVer = "1.3.12.118",
name = "凤凰于飞",
desc = "动态昵称框，夏夜绮梦祈愿活动获得",
icon = "CDN:Icon_NameFrame_108",
resourceConf = {
bg = "D_Namebox_108"
},
isDynamic = 1
},
[820107] = {
id = 820107,
lowVer = "1.3.12.92",
name = "凤求凰",
desc = "动态昵称框，凤求凰祈愿获得",
icon = "CDN:Icon_NameFrame_103",
resourceConf = {
bg = "D_Namebox_103"
},
isDynamic = 1
},
[820109] = {
id = 820109,
quality = 4,
name = "造梦手艺人",
desc = "星世界创作周报获得",
icon = "CDN:Icon_NameFrame_109",
resourceConf = {
bg = "CDN:T_NameFrame_bg_061"
}
},
[820110] = {
id = 820110,
quality = 4,
name = "造梦小蜜蜂",
desc = "星世界创作周报获得",
icon = "CDN:Icon_NameFrame_063",
resourceConf = {
bg = "CDN:T_NameFrame_bg_063"
}
},
[820108] = {
id = 820108,
quality = 3,
name = "【限时】峡谷3V3",
desc = "限时排位、娱乐排位赛等活动获得",
icon = "CDN:Icon_Arena_NameFrame_999",
resourceConf = {
bg = "CDN:T_Arena_NameFrame_bg_999"
},
beginTime = {
seconds = 1722528000
}
},
[820112] = {
id = 820112,
lowVer = "1.3.12.1",
quality = 3,
name = "金桂飘香",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_110",
resourceConf = {
bg = "CDN:T_NameFrame_bg_110"
},
beginTime = {
seconds = 1724947200
}
},
[820113] = {
id = 820113,
lowVer = "1.3.18.1",
quality = 4,
name = "仙人掌之花",
desc = "精灵之森通行证获得",
icon = "CDN:Icon_NameFrame_113",
resourceConf = {
bg = "CDN:T_NameFrame_bg_113"
},
beginTime = {
seconds = 1724947200
}
},
[820114] = {
id = 820114,
lowVer = "1.3.18.1",
quality = 4,
name = "蘑菇与树墩",
desc = "精灵之森通行证获得",
icon = "CDN:Icon_NameFrame_112",
resourceConf = {
bg = "CDN:T_NameFrame_bg_112"
},
beginTime = {
seconds = 1724947200
}
},
[820115] = {
id = 820115,
lowVer = "1.3.18.1 ",
name = "森林蝶舞",
desc = "精灵之森—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_114",
resourceConf = {
bg = "D_Namebox_114"
},
isDynamic = 1,
beginTime = {
seconds = 1724947200
}
},
[820116] = {
id = 820116,
quality = 4,
name = "小太阳昵称框",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_116",
resourceConf = {
bg = "CDN:T_NameFrame_bg_116"
}
},
[820117] = {
id = 820117,
lowVer = "1.3.12.1",
quality = 3,
name = "花语之韵",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_111",
resourceConf = {
bg = "CDN:T_NameFrame_bg_111"
},
beginTime = {
seconds = 1724947200
}
},
[820118] = {
id = 820118,
quality = 3,
name = "浪漫清晨",
desc = "星光剧场祈愿获得",
icon = "CDN:Icon_NameFrame_115",
resourceConf = {
bg = "CDN:T_NameFrame_bg_115"
},
beginTime = {
seconds = 1756483200
}
},
[820119] = {
id = 820119,
lowVer = "1.3.18.37",
name = "月冠金秋",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_117",
resourceConf = {
bg = "CDN:T_NameFrame_bg_117"
},
beginTime = {
seconds = 1726156800
}
},
[820120] = {
id = 820120,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.18.71",
name = "碧玺龙纹",
desc = "青霄龙吟祈愿获得",
icon = "CDN:Icon_NameFrame_118",
resourceConf = {
bg = "CDN:T_NameFrame_bg_118"
},
beginTime = {
seconds = 1727625600
}
},
[820121] = {
id = 820121,
quality = 3,
name = "华章盛典",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_119",
resourceConf = {
bg = "CDN:T_NameFrame_bg_119"
}
},
[820122] = {
id = 820122,
lowVer = "1.3.18.72",
name = "三彩逸士",
desc = "动态昵称框，千都三彩祈愿活动获得",
icon = "CDN:Icon_NameFrame_120",
resourceConf = {
bg = "D_Namebox_120"
},
isDynamic = 1
},
[820123] = {
id = 820123,
lowVer = "1.3.26.1",
quality = 4,
name = "星辰探险",
desc = "星语星愿通行证获得",
icon = "CDN:Icon_NameFrame_123",
resourceConf = {
bg = "CDN:T_NameFrame_bg_123"
},
beginTime = {
seconds = 1729180800
}
},
[820124] = {
id = 820124,
lowVer = "1.3.26.1",
quality = 4,
name = "星际怪盗",
desc = "星语星愿通行证获得",
icon = "CDN:Icon_NameFrame_124",
resourceConf = {
bg = "CDN:T_NameFrame_bg_124"
},
beginTime = {
seconds = 1729180800
}
},
[820126] = {
id = 820126,
lowVer = "1.3.26.1",
quality = 4,
name = "星星昵称框",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_126",
resourceConf = {
bg = "CDN:T_NameFrame_bg_126"
},
beginTime = {
seconds = 1729180800
}
},
[820127] = {
id = 820127,
lowVer = "1.3.26.1",
quality = 3,
name = "月光星愿",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_121",
resourceConf = {
bg = "CDN:T_NameFrame_bg_121"
},
beginTime = {
seconds = 1729180800
}
},
[820128] = {
id = 820128,
lowVer = "1.3.26.1",
quality = 3,
name = "冬日暖茶",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_125",
resourceConf = {
bg = "CDN:T_NameFrame_bg_125"
},
beginTime = {
seconds = 1729180800
}
},
[820129] = {
id = 820129,
lowVer = "1.3.26.1",
quality = 3,
name = "奇趣游乐场",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_bg_550",
resourceConf = {
bg = "CDN:T_NameFrame_bg_550"
},
beginTime = {
seconds = 1729180800
}
},
[820130] = {
id = 820130,
lowVer = "1.3.26.1",
quality = 3,
name = "小心猫猫",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_127",
resourceConf = {
bg = "CDN:T_NameFrame_bg_127"
}
},
[820131] = {
id = 820131,
lowVer = "1.3.36.1",
name = "美味盛宴",
desc = "美食派对—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_133",
resourceConf = {
bg = "D_Namebox_133"
},
isDynamic = 1,
beginTime = {
seconds = 1732809600
}
},
[820125] = {
id = 820125,
lowVer = "1.3.26.1",
name = "星空幻想",
desc = "星语星愿—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_122",
resourceConf = {
bg = "D_Namebox_122"
},
isDynamic = 1,
beginTime = {
seconds = 1729180800
}
},
[820132] = {
id = 820132,
lowVer = "1.3.26.68",
name = "永恒之舞",
desc = "动态昵称框，永恒之舞祈愿获得",
icon = "CDN:Icon_NameFrame_128",
resourceConf = {
bg = "D_Namebox_128"
},
isDynamic = 1
},
[820133] = {
id = 820133,
lowVer = "1.3.12.118",
name = "星愿予你",
desc = "动态昵称框，星之恋空祈愿活动获得",
icon = "CDN:Icon_NameFrame_129",
resourceConf = {
bg = "D_Namebox_129"
},
isDynamic = 1
},
[820134] = {
id = 820134,
lowVer = "1.3.26.68",
name = "蔚海绮梦",
desc = "动态昵称框，蔚海绮梦祈愿获得",
icon = "CDN:Icon_NameFrame_130",
resourceConf = {
bg = "D_Namebox_130"
},
isDynamic = 1
},
[820135] = {
id = 820135,
lowVer = "1.3.36.1",
quality = 4,
name = "焦糖布丁",
desc = "美食派对通行证获得",
icon = "CDN:Icon_NameFrame_135",
resourceConf = {
bg = "CDN:T_NameFrame_bg_135"
},
beginTime = {
seconds = 1732809600
}
},
[820136] = {
id = 820136,
lowVer = "1.3.36.1",
quality = 4,
name = "花菜椰椰",
desc = "美食派对通行证获得",
icon = "CDN:Icon_NameFrame_136",
resourceConf = {
bg = "CDN:T_NameFrame_bg_136"
},
beginTime = {
seconds = 1732809600
}
},
[820137] = {
id = 820137,
quality = 4,
name = "奶油布丁昵称框",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_131",
resourceConf = {
bg = "CDN:T_NameFrame_bg_131"
},
beginTime = {
seconds = 1732809600
}
},
[820138] = {
id = 820138,
lowVer = "1.3.37.1",
quality = 3,
name = "侦探名片",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_134",
resourceConf = {
bg = "CDN:T_NameFrame_bg_134"
},
beginTime = {
seconds = 1734624000
}
},
[820139] = {
id = 820139,
lowVer = "1.3.36.1",
quality = 4,
name = "巧心巧意",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_132",
resourceConf = {
bg = "CDN:T_NameFrame_bg_132"
},
beginTime = {
seconds = 1732809600
}
},
[820140] = {
id = 820140,
lowVer = "1.3.37.27",
quality = 4,
name = "寒霜低语",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_137",
resourceConf = {
bg = "CDN:T_NameFrame_bg_137"
},
showInView = 0,
beginTime = {
seconds = 1734105600
}
},
[820142] = {
id = 820142,
lowVer = "1.3.26.68",
quality = 3,
name = "晨曦农场",
desc = "卡牌集换活动中获得",
icon = "CDN:Icon_NameFrame_138",
resourceConf = {
bg = "CDN:T_NameFrame_bg_138"
},
beginTime = {
seconds = 1732809600
}
},
[820143] = {
id = 820143,
lowVer = "1.3.26.1",
quality = 3,
name = "一岁一礼",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_140",
resourceConf = {
bg = "CDN:T_NameFrame_bg_140"
}
},
[820144] = {
id = 820144,
lowVer = "1.3.37.87",
name = "云梦绮旅",
desc = "动态昵称框，云梦绮旅祈愿活动获得",
icon = "CDN:Icon_NameFrame_145",
resourceConf = {
bg = "D_Namebox_145"
},
isDynamic = 1,
beginTime = {
seconds = 1735660800
}
},
[820150] = {
id = 820150,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.26.93",
name = "无双之魔",
desc = "峡谷战神祈愿活动获得",
icon = "CDN:Icon_NameFrame_143",
resourceConf = {
bg = "CDN:T_NameFrame_bg_143"
},
beginTime = {
seconds = 1735920000
}
},
[820151] = {
id = 820151,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.26.93",
name = "水晶猎龙者",
desc = "峡谷战神祈愿活动获得",
icon = "CDN:Icon_NameFrame_142",
resourceConf = {
bg = "CDN:T_NameFrame_bg_142"
},
beginTime = {
seconds = 1735920000
}
},
[820152] = {
id = 820152,
lowVer = "1.3.68.1",
name = "大唐风尚",
desc = "大唐风华—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_148",
resourceConf = {
bg = "D_Namebox_148"
},
isDynamic = 1,
beginTime = {
seconds = 1737043200
}
},
[820153] = {
id = 820153,
lowVer = "1.3.68.1",
quality = 4,
name = "异域风情",
desc = "大唐风华通行证获得",
icon = "CDN:Icon_NameFrame_150",
resourceConf = {
bg = "CDN:T_NameFrame_bg_150"
},
beginTime = {
seconds = 1737043200
}
},
[820154] = {
id = 820154,
lowVer = "1.3.68.1",
quality = 4,
name = "福运当头",
desc = "大唐风华通行证获得",
icon = "CDN:Icon_NameFrame_151",
resourceConf = {
bg = "CDN:T_NameFrame_bg_151"
},
beginTime = {
seconds = 1737043200
}
},
[820155] = {
id = 820155,
lowVer = "1.3.68.1",
quality = 4,
name = "婉儿昵称框",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_146",
resourceConf = {
bg = "CDN:T_NameFrame_bg_146"
}
},
[820156] = {
id = 820156,
lowVer = "1.3.68.26",
name = "福运琳琅",
desc = "动态昵称框，福运琳琅祈愿获得",
icon = "CDN:Icon_NameFrame_156 ",
resourceConf = {
bg = "D_Namebox_156"
},
isDynamic = 1
},
[820157] = {
id = 820157,
lowVer = "1.3.68.33",
name = "天启圣谕",
desc = "动态昵称框，天启圣谕祈愿获得",
icon = "CDN:Icon_NameFrame_144",
resourceConf = {
bg = "D_Namebox_144"
},
isDynamic = 1,
beginTime = {
seconds = 1737993600
}
},
[820158] = {
id = 820158,
lowVer = "1.3.68.1",
quality = 3,
name = "元宵闹春",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_152",
resourceConf = {
bg = "CDN:T_NameFrame_bg_152"
}
},
[820159] = {
id = 820159,
lowVer = "1.3.68.1",
quality = 3,
name = "杨玉环-羞花如玉",
desc = "峡谷星春福袋获得",
icon = "CDN:CT_Icon_Arena_NameFrame_007",
resourceConf = {
bg = "CDN:CT_Arena_NameFrame_bg_007"
},
showInView = 0
},
[820160] = {
id = 820160,
lowVer = "1.3.68.1",
name = "绝顶之巅",
desc = "峡谷祈愿活动获得",
icon = "CDN:Icon_NameFrame_155",
resourceConf = {
bg = "CDN:T_NameFrame_bg_155"
},
showInView = 0
},
[820161] = {
id = 820161,
lowVer = "1.3.68.1",
quality = 3,
name = "三丽鸥家族昵称框",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_157",
resourceConf = {
bg = "CDN:T_NameFrame_bg_157"
}
},
[820162] = {
id = 820162,
lowVer = "1.3.68.87",
name = "缘来是你",
desc = "动态昵称框，岚汀之约祈愿活动获得",
icon = "CDN:Icon_NameFrame_158",
resourceConf = {
bg = "D_Namebox_158"
},
isDynamic = 1,
beginTime = {
seconds = 1739462400
}
},
[820163] = {
id = 820163,
lowVer = "1.3.68.68",
quality = 3,
name = "春日绿意",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_153",
resourceConf = {
bg = "CDN:T_NameFrame_bg_153"
}
},
[820164] = {
id = 820164,
lowVer = "1.3.68.116",
name = "桃坞问春",
desc = "桃坞问春祈愿获得",
icon = "CDN:Icon_NameFrame_164",
resourceConf = {
bg = "CDN:T_NameFrame_bg_164"
}
},
[820165] = {
id = 820165,
lowVer = "1.3.68.100",
name = "桃源万千",
desc = "动态昵称框，桃源万千祈愿获得",
icon = "CDN:Icon_NameFrame_159",
resourceConf = {
bg = "D_Namebox_159"
},
isDynamic = 1,
beginTime = {
seconds = 1740067200
}
},
[820166] = {
id = 820166,
lowVer = "1.3.78.1",
name = "月夜真相",
desc = "真相之夜—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_168",
resourceConf = {
bg = "D_Namebox_168"
},
isDynamic = 1,
beginTime = {
seconds = 1741881600
}
},
[820167] = {
id = 820167,
quality = 3,
name = "傲娇狼宝昵称框",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_163",
resourceConf = {
bg = "CDN:T_NameFrame_bg_163"
}
},
[820168] = {
id = 820168,
quality = 4,
name = "风车流年",
desc = "达成特定亲密关系获得",
icon = "CDN:Icon_NameFrame_161",
resourceConf = {
bg = "CDN:T_NameFrame_bg_161"
}
},
[820169] = {
id = 820169,
quality = 4,
name = "并蒂双生",
desc = "达成特定亲密关系获得",
icon = "CDN:Icon_NameFrame_162",
resourceConf = {
bg = "CDN:T_NameFrame_bg_162"
}
},
[820170] = {
id = 820170,
quality = 4,
name = "合力托举",
desc = "达成特定亲密关系获得",
icon = "CDN:Icon_NameFrame_160",
resourceConf = {
bg = "CDN:T_NameFrame_bg_160"
}
},
[820171] = {
id = 820171,
lowVer = "1.3.78.1",
quality = 4,
name = "学富五车",
desc = "真相之夜通行证获得",
icon = "CDN:Icon_NameFrame_166",
resourceConf = {
bg = "CDN:T_NameFrame_bg_166"
},
beginTime = {
seconds = 1741881600
}
},
[820172] = {
id = 820172,
lowVer = "1.3.78.1",
quality = 4,
name = "人畜无害",
desc = "真相之夜通行证获得",
icon = "CDN:Icon_NameFrame_167",
resourceConf = {
bg = "CDN:T_NameFrame_bg_167"
},
beginTime = {
seconds = 1741881600
}
},
[820173] = {
id = 820173,
lowVer = "1.3.78.33",
name = "珍馐百味",
desc = "动态昵称框，珍馐百味祈愿获得",
icon = "CDN:Icon_NameFrame_165",
resourceConf = {
bg = "D_Namebox_165"
},
isDynamic = 1
},
[820174] = {
id = 820174,
lowVer = "1.3.68.1",
quality = 4,
name = "真相大白",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_169",
resourceConf = {
bg = "CDN:T_NameFrame_bg_169"
},
beginTime = {
seconds = 1741881600
}
},
[820175] = {
id = 820175,
quality = 3,
name = "摊玩市集",
desc = "卡牌集换活动中获得",
icon = "CDN:Icon_NameFrame_170",
resourceConf = {
bg = "CDN:T_NameFrame_bg_170"
},
beginTime = {
seconds = 1741190400
}
},
[820176] = {
id = 820176,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.78.29",
name = "羽扇清风",
desc = "峡谷英豪祈愿活动获得",
icon = "CDN:Icon_NameFrame_172",
resourceConf = {
bg = "CDN:T_NameFrame_bg_172"
},
beginTime = {
seconds = 1743436800
}
},
[820177] = {
id = 820177,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.78.29",
name = "民间帝胄",
desc = "峡谷英豪祈愿活动获得",
icon = "CDN:Icon_NameFrame_171",
resourceConf = {
bg = "CDN:T_NameFrame_bg_171"
},
beginTime = {
seconds = 1743436800
}
},
[820178] = {
id = 820178,
lowVer = "1.3.78.29",
name = "奶茶星人",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_173",
resourceConf = {
bg = "CDN:T_NameFrame_bg_173"
},
beginTime = {
seconds = 1743523200
}
},
[820179] = {
id = 820179,
lowVer = "1.3.78.29",
name = "暗中观察酱",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_174",
resourceConf = {
bg = "CDN:T_NameFrame_bg_174"
},
beginTime = {
seconds = 1743523200
}
},
[820180] = {
id = 820180,
lowVer = "1.3.78.33",
name = "甜梦嘉年华",
desc = "动态昵称框，甜梦嘉年华祈愿获得",
icon = "CDN:Icon_NameFrame_180",
resourceConf = {
bg = "D_Namebox_180"
},
isDynamic = 1,
beginTime = {
seconds = 1744128000
}
},
[820181] = {
id = 820181,
lowVer = "1.3.88.1",
quality = 4,
name = "极酷新星",
desc = "超能学园通行证获得",
icon = "CDN:Icon_NameFrame_186",
resourceConf = {
bg = "CDN:T_NameFrame_bg_186"
},
beginTime = {
seconds = 1746115200
}
},
[820182] = {
id = 820182,
lowVer = "1.3.88.1",
quality = 4,
name = "礼法有度",
desc = "超能学园通行证获得",
icon = "CDN:Icon_NameFrame_185",
resourceConf = {
bg = "CDN:T_NameFrame_bg_185"
},
beginTime = {
seconds = 1746115200
}
},
[820183] = {
id = 820183,
lowVer = "1.3.88.1",
name = "学园传说",
desc = "超能学园—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_184",
resourceConf = {
bg = "D_Namebox_184"
},
isDynamic = 1,
beginTime = {
seconds = 1746115200
}
},
[820184] = {
id = 820184,
lowVer = "1.3.88.1",
name = "心动信号",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_178",
resourceConf = {
bg = "CDN:T_NameFrame_bg_178"
},
beginTime = {
seconds = 1745856000
}
},
[820185] = {
id = 820185,
quality = 3,
name = "超能小宝昵称框",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_179",
resourceConf = {
bg = "CDN:T_NameFrame_bg_179"
}
},
[820186] = {
id = 820186,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.88.1",
name = "赛博乐动",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_181",
resourceConf = {
bg = "CDN:T_NameFrame_bg_181"
},
beginTime = {
seconds = 1744905600
}
},
[820187] = {
id = 820187,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.88.1",
name = "奶绿小宝",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_182",
resourceConf = {
bg = "CDN:T_NameFrame_bg_182"
},
beginTime = {
seconds = 1747324800
}
},
[820188] = {
id = 820188,
exceedReplaceItem = {
{
itemId = 3556,
itemNum = 5
}
},
lowVer = "1.3.88.1",
name = "幻彩调律",
desc = "动态昵称框，幻彩调律祈愿获得",
icon = "CDN:Icon_NameFrame_188",
resourceConf = {
bg = "D_Namebox_188"
},
isDynamic = 1,
beginTime = {
seconds = 1747324800
}
},
[820189] = {
id = 820189,
lowVer = "1.3.68.1",
quality = 3,
name = "浓情巧克力",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_187",
resourceConf = {
bg = "CDN:T_NameFrame_bg_187"
}
},
[820190] = {
id = 820190,
lowVer = "1.3.68.1",
quality = 3,
name = "阿卓漫游",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_175",
resourceConf = {
bg = "CDN:T_NameFrame_bg_175"
}
},
[820191] = {
id = 820191,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.88.1",
name = "动感猫猫",
desc = "动态昵称框，幻喵音境祈愿获得",
icon = "CDN:Icon_NameFrame_189",
resourceConf = {
bg = "D_Namebox_189"
},
isDynamic = 1,
beginTime = {
seconds = 1749744000
}
},
[820192] = {
id = 820192,
lowVer = "1.3.68.1",
quality = 3,
name = "霓虹喵喵机",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_190",
resourceConf = {
bg = "CDN:T_NameFrame_bg_190"
},
beginTime = {
seconds = 1748448000
}
},
[820193] = {
id = 820193,
lowVer = "1.3.68.1",
quality = 3,
name = "喵喵次元袋",
desc = "限时活动获得",
icon = "CDN:Icon_NameFrame_191",
resourceConf = {
bg = "CDN:T_NameFrame_bg_191"
},
beginTime = {
seconds = 1748448000
}
},
[820194] = {
id = 820194,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.88.1",
name = "蝶舞花间",
desc = "动态昵称框，蝶舞花间祈愿获得",
icon = "CDN:Icon_NameFrame_176",
resourceConf = {
bg = "D_Namebox_176"
},
isDynamic = 1,
beginTime = {
seconds = 1749139200
}
},
[820195] = {
id = 820195,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.88.1",
quality = 3,
name = "奖杯征程占坑昵称框蓝",
desc = "动态昵称框，蝶舞花间祈愿获得",
icon = "CDN:Icon_NameFrame_176",
resourceConf = {
bg = "D_Namebox_176"
},
isDynamic = 1,
beginTime = {
seconds = 1749139200
}
},
[820196] = {
id = 820196,
quality = 4,
name = "白泽小仙昵称框",
desc = "王冠商铺兑换中获得",
icon = "CDN:Icon_NameFrame_193",
resourceConf = {
bg = "CDN:T_NameFrame_bg_193"
}
},
[820197] = {
id = 820197,
lowVer = "1.3.88.1",
quality = 4,
name = "极酷新星",
desc = "S13通行证获得",
icon = "CDN:Icon_NameFrame_186",
resourceConf = {
bg = "CDN:T_NameFrame_bg_186"
},
beginTime = {
seconds = 1746115200
}
},
[820198] = {
id = 820198,
lowVer = "1.3.88.1",
quality = 4,
name = "礼法有度",
desc = "S13通行证获得",
icon = "CDN:Icon_NameFrame_185",
resourceConf = {
bg = "CDN:T_NameFrame_bg_185"
},
beginTime = {
seconds = 1746115200
}
},
[820199] = {
id = 820199,
lowVer = "1.3.99.1",
name = "昆仑灵秀",
desc = "昆仑秘境—达到指定赛季时尚分获得",
icon = "CDN:Icon_NameFrame_168",
resourceConf = {
bg = "D_Namebox_168"
},
isDynamic = 1,
beginTime = {
seconds = 1751040000
}
},
[820200] = {
id = 820200,
lowVer = "1.3.88.153",
quality = 3,
name = "超级贺礼",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_195",
resourceConf = {
bg = "CDN:T_NameFrame_bg_195"
},
beginTime = {
seconds = 1750694400
}
}
}

local mt = {
type = "ItemType_NamePlate",
maxNum = 1,
quality = 2,
showInView = 1,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data