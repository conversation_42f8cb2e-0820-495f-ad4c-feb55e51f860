--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_峡谷主目标系统_Arena.xlsx: 等级突破任务

local data = {
[10480006] = {
id = 10480006,
name = "试炼任务",
desc = "单局内三技能累计命中70次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 849,
value = 70,
subConditionList = {
{
type = 209,
value = {
1048
}
},
{
type = 203,
value = {
759
}
}
}
}
}
}
},
reward = {
itemIdList = {
905034
},
numList = {
1
}
},
extraConf = {
arenaHeroId = 1048
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data