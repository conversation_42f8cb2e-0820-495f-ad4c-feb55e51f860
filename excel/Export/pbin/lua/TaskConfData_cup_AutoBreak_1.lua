--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/J_奖杯征程.xlsx: 奖杯任务

local v0 = {
itemIdList = {
200071
},
numList = {
5
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
}

local v1 = {
taskIcon = "T_StarCupTask_Icon_Cup1",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg2",
urgentContent = "{0}后下架"
}

local v2 = 90

local data = {
[200001039] = {
id = 200001039,
desc = "成功赠送或交换1张卡牌",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 845,
value = 1,
subConditionList = {
{
type = 322,
value = {
1,
2,
3,
4
}
},
{
type = 323,
value = {
2,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
5
},
validPeriodList = {
0
},
urgentRatioContent = "高倍奖励"
},
jumpId = 902,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup1",
panelImg = "T_StarCupTask_img_TaskBg2",
urgentContent = "{0}后下架"
},
showHome = true,
order = 86,
isTaskShow = 1,
taskGroupId = 200004
},
[400001016] = {
id = 400001016,
desc = "完成1次天天晋级赛(排位)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400007
},
[400001017] = {
id = 400001017,
desc = "完成1次精选匹配（星世界）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 224,
value = 1,
subConditionList = {
{
type = 143,
value = {
6
}
},
{
type = 144,
value = {
2
}
}
}
}
}
}
},
reward = v0,
jumpId = 160,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400007
},
[400001018] = {
id = 400001018,
desc = "通关1次每日精选（星世界）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 1,
subConditionList = {
{
type = 89,
value = {
5
}
}
}
}
}
}
},
reward = v0,
jumpId = 76,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400007
},
[400001019] = {
id = 400001019,
name = "累计任务",
desc = "通关5次星图广场（星世界）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 81,
value = 5,
subConditionList = {
{
type = 89,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
20
},
validPeriodList = {
0
},
urgentRatioContent = "高倍奖励"
},
jumpId = 32,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup2",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg3",
urgentContent = "{0}后下架"
},
showHome = true,
taskGroupId = 400009
},
[400001020] = {
id = 400001020,
desc = "完成1次天天晋级赛(排位)",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400010
},
[400001021] = {
id = 400001021,
desc = "完成1次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
15,
16,
17
}
}
}
}
}
}
},
reward = v0,
jumpId = 1043,
extraConf = v1,
showHome = true,
taskGroupId = 400011
},
[400001022] = {
id = 400001022,
name = "每周任务",
desc = "闪电赛闪电奖章数累计达到200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 200,
subConditionList = {
{
type = 145,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 1043,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup2",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg1",
urgentContent = "{0}后下架"
},
showHome = true,
order = 92,
taskGroupId = 400012
},
[400001023] = {
id = 400001023,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400013
},
[400001024] = {
id = 400001024,
desc = "完成1次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
15,
16,
17
}
}
}
}
}
}
},
reward = v0,
jumpId = 1043,
extraConf = v1,
showHome = true,
taskGroupId = 400014
},
[400001025] = {
id = 400001025,
name = "每周任务",
desc = "闪电赛闪电奖章数累计达到300",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 300,
subConditionList = {
{
type = 145,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 1043,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup2",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg1",
urgentContent = "{0}后下架"
},
showHome = true,
order = 92,
taskGroupId = 400015
},
[400001026] = {
id = 400001026,
desc = "完成1次峡谷3v3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600,
5601
}
}
}
}
}
}
},
jumpId = 50109,
showHome = true,
taskGroupId = 400016
},
[400001027] = {
id = 400001027,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400017
},
[400001028] = {
id = 400001028,
desc = "完成1次峡谷3v3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600,
5601
}
}
}
}
}
}
},
jumpId = 50109,
showHome = true,
taskGroupId = 400019
},
[400001029] = {
id = 400001029,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400020
},
[400001030] = {
id = 400001030,
desc = "完成1次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
15,
16,
17
}
}
}
}
}
}
},
reward = v0,
jumpId = 1043,
extraConf = v1,
showHome = true,
taskGroupId = 400021
},
[400001031] = {
id = 400001031,
name = "每周任务",
desc = "闪电赛闪电奖章奖章达到200",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 200,
subConditionList = {
{
type = 145,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 1043,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup2",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg1",
urgentContent = "{0}后下架"
},
showHome = true,
order = 92,
taskGroupId = 400022
},
[400001032] = {
id = 400001032,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400023
},
[400001033] = {
id = 400001033,
desc = "完成1次峡谷3v3/峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50109,
showHome = true,
taskGroupId = 400024
},
[400001034] = {
id = 400001034,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400025
},
[400001035] = {
id = 400001035,
desc = "完成1次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
15,
16,
17
}
}
}
}
}
}
},
reward = v0,
jumpId = 1043,
extraConf = v1,
showHome = true,
taskGroupId = 400026
},
[400001036] = {
id = 400001036,
name = "每周任务",
desc = "闪电赛闪电奖章奖章达到300",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 300,
subConditionList = {
{
type = 145,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 1043,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup2",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg1",
urgentContent = "{0}后下架"
},
showHome = true,
order = 92,
taskGroupId = 400027
},
[400001037] = {
id = 400001037,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400028
},
[400001038] = {
id = 400001038,
desc = "完成1次峡谷3v3/峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50109,
showHome = true,
taskGroupId = 400029
},
[400001039] = {
id = 400001039,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400030
},
[400001040] = {
id = 400001040,
desc = "完成1次峡谷3v3/峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
5600,
5601,
6101,
6102
}
}
}
}
}
}
},
jumpId = 50109,
showHome = true,
taskGroupId = 400031
},
[400001041] = {
id = 400001041,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
order = 90,
taskGroupId = 400032
},
[400001042] = {
id = 400001042,
showHome = true,
order = 90,
taskGroupId = 400033
},
[400001043] = {
id = 400001043,
desc = "完成1次闪电赛",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
15,
16,
17
}
}
}
}
}
}
},
reward = v0,
jumpId = 1043,
extraConf = v1,
showHome = true,
taskGroupId = 400034
},
[400001044] = {
id = 400001044,
name = "每周任务",
desc = "闪电赛闪电奖章奖章达到50",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 207,
value = 50,
subConditionList = {
{
type = 145,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 1043,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup2",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg1",
urgentContent = "{0}后下架"
},
showHome = true,
order = 92,
taskGroupId = 400035
},
[400001045] = {
id = 400001045,
showHome = true,
taskGroupId = 400036
},
[400001046] = {
id = 400001046,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
order = 90,
taskGroupId = 400037
},
[400001047] = {
id = 400001047,
showHome = true,
taskGroupId = 400038
},
[400001048] = {
id = 400001048,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400039
},
[400001049] = {
id = 400001049,
desc = "完成1次大王排名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
380,
381,
382
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 505,
showHome = true,
order = 92,
taskGroupId = 400033
},
[400001050] = {
id = 400001050,
desc = "完成1次大王排名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
380,
381,
382
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 505,
showHome = true,
order = 92,
taskGroupId = 400037
},
[400001051] = {
id = 400001051,
desc = "完成1次大王排名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
380,
381,
382
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 505,
showHome = true,
order = 92,
taskGroupId = 400039
},
[400001052] = {
id = 400001052,
showHome = true,
taskGroupId = 400040
},
[400001053] = {
id = 400001053,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
order = 90,
taskGroupId = 400040
},
[400001054] = {
id = 400001054,
desc = "完成1次大王排名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
380,
381,
382
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 505,
showHome = true,
order = 92,
taskGroupId = 400040
},
[400001055] = {
id = 400001055,
showHome = true,
taskGroupId = 400041
},
[400001056] = {
id = 400001056,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400041
},
[400001057] = {
id = 400001057,
desc = "完成1次大王排名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
380,
381,
382
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 505,
showHome = true,
order = 92,
taskGroupId = 400041
},
[400001058] = {
id = 400001058,
showHome = true,
taskGroupId = 400042
},
[400001059] = {
id = 400001059,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
order = 90,
taskGroupId = 400042
},
[400001060] = {
id = 400001060,
desc = "完成1次大王排名",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
380,
381,
382
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 505,
showHome = true,
order = 92,
taskGroupId = 400042
},
[400001061] = {
id = 400001061,
showHome = true,
taskGroupId = 400043
},
[400001062] = {
id = 400001062,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400043
},
[400001063] = {
id = 400001063,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
order = 90,
taskGroupId = 400043
},
[400001064] = {
id = 400001064,
showHome = true,
taskGroupId = 400044
},
[400001065] = {
id = 400001065,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
taskGroupId = 400044
},
[400001066] = {
id = 400001066,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400044
},
[400001067] = {
id = 400001067,
showHome = true,
taskGroupId = 400045
},
[400001068] = {
id = 400001068,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400045
},
[400001069] = {
id = 400001069,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400045
},
[400001070] = {
id = 400001070,
showHome = true,
taskGroupId = 400046
},
[400001071] = {
id = 400001071,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
taskGroupId = 400046
},
[400001072] = {
id = 400001072,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400046
},
[400001073] = {
id = 400001073,
showHome = true,
taskGroupId = 400047
},
[400001074] = {
id = 400001074,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400047
},
[400001075] = {
id = 400001075,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400047
},
[400001076] = {
id = 400001076,
desc = "完成1次星世界推荐",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 224,
value = 1,
subConditionList = {
{
type = 144,
value = {
1
}
}
}
}
}
}
},
reward = v0,
jumpId = 1203,
extraConf = v1,
showHome = true,
order = 90,
isTaskShow = 1,
taskGroupId = 200001
},
[400001077] = {
id = 400001077,
showHome = true,
taskGroupId = 400048
},
[400001078] = {
id = 400001078,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400048
},
[400001079] = {
id = 400001079,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
taskGroupId = 400048
},
[400001080] = {
id = 400001080,
showHome = true,
taskGroupId = 400049
},
[400001081] = {
id = 400001081,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400049
},
[400001082] = {
id = 400001082,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400049
},
[400001083] = {
id = 400001083,
showHome = true,
taskGroupId = 400050
},
[400001084] = {
id = 400001084,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400050
},
[400001085] = {
id = 400001085,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
taskGroupId = 400050
},
[400001086] = {
id = 400001086,
showHome = true,
taskGroupId = 400051
},
[400001087] = {
id = 400001087,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400051
},
[400001088] = {
id = 400001088,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400051
},
[400001089] = {
id = 400001089,
showHome = true,
taskGroupId = 400052
},
[400001090] = {
id = 400001090,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400052
},
[400001091] = {
id = 400001091,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
taskGroupId = 400052
},
[400001092] = {
id = 400001092,
showHome = true,
taskGroupId = 400053
},
[400001093] = {
id = 400001093,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400053
},
[400001094] = {
id = 400001094,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400053
},
[400001095] = {
id = 400001095,
showHome = true,
taskGroupId = 400054
},
[400001096] = {
id = 400001096,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400054
},
[400001097] = {
id = 400001097,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
taskGroupId = 400054
},
[400001098] = {
id = 400001098,
showHome = true,
taskGroupId = 400055
},
[400001099] = {
id = 400001099,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400055
},
[400001100] = {
id = 400001100,
desc = "完成1次天天晋级赛（排位）",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
4,
5,
6
}
}
}
}
}
}
},
reward = v0,
jumpId = 1052,
extraConf = v1,
showHome = true,
order = 90,
taskGroupId = 400055
},
[400001101] = {
id = 400001101,
showHome = true,
taskGroupId = 400056
},
[400001102] = {
id = 400001102,
desc = "完成1次谁是狼人",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
105,
106,
109,
113,
151
}
}
}
}
}
}
},
jumpId = 1024,
showHome = true,
taskGroupId = 400056
},
[400001103] = {
id = 400001103,
desc = "完成1次大王别抓我",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
350,
351,
352,
353
}
}
}
}
}
}
},
jumpId = 1018,
showHome = true,
taskGroupId = 400056
},
[400001104] = {
id = 400001104,
desc = "我要暴富中完成1次每日任务",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1723,
value = 1
}
}
}
},
reward = v0,
jumpId = 889,
extraConf = v1,
showHome = true,
order = 92,
taskGroupId = 400057
},
[400001105] = {
id = 400001105,
name = "每周任务",
desc = "我要暴富中消耗700个骰子",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1718,
value = 700,
subConditionList = {
{
type = 703,
value = {
2
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 100,
urgentRatioContent = "高倍奖励"
},
jumpId = 889,
showHome = true,
order = 92,
taskGroupId = 400058
}
}

local mt = {
name = "每日任务",
desc = "完成1次峡谷5v5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 1,
subConditionList = {
{
type = 2,
value = {
6101,
6102
}
}
}
}
}
}
},
reward = {
itemIdList = {
200071
},
numList = {
10
},
validPeriodList = {
0
},
addRatio = 50,
urgentRatioContent = "高倍奖励"
},
jumpId = 50112,
extraConf = {
taskIcon = "T_StarCupTask_Icon_Cup2",
leftTopContent = "限时",
rightTopContent = "高倍奖励",
panelImg = "T_StarCupTask_img_TaskBg2",
urgentContent = "{0}后下架"
},
showHome = false,
order = 91
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data