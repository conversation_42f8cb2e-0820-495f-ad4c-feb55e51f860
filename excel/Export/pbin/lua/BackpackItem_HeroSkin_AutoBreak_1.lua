--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_Arena.xlsx: 皮肤

local v0 = 10

local v1 = {
"爱就是组成我的元件",
"人类对孤独的理解是有限的",
"我的一小步,见证友谊的一大步",
"哼，你咋不上天呢？",
"我会飞得更高以求生存"
}

local v2 = {
0,
0
}

local data = {
[407023] = {
id = 407023,
effect = true,
name = "优雅紫鸢 虞姬",
desc = "（限峡谷模式使用）获取后可以获得虞姬的英雄皮肤。",
icon = "T_Arena_Role_1031_01",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_232_moba",
material = "MI_PL_232_01_HP01_moba;MI_PL_232_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "SK_PL_232_moba_Physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_232_moba",
outShow = "AS_CH_IdleShow_PL_232_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "作为姜子牙的得意弟子之一，虞姬与自然为伍，同藤萝鸟儿相伴，射出的每一支箭矢都带有树神的祝福。反抗阴阳家暴政的过程中，她与英勇无畏的项羽堕入爱河，大战前夕，久别的师兄却前来告知，项羽注定为要世界带来混沌……来不及反驳命运的虞姬已然落入幻术的圈套，该下战场，那支穿透一切的利箭悄然对准爱人的心脏……",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407024] = {
id = 407024,
effect = true,
name = "白鹭青山 李白",
desc = "（限峡谷模式使用）获取后可以获得李白的英雄皮肤。",
icon = "T_Arena_Role_1019_02",
getWay = "星运宝箱产出",
resourceConf = {
model = "sk_pl_157_dev",
material = "MI_PL_157_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_157_dev_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_157_moba",
outShow = "AS_CH_IdleShow_PL_157_moba",
outShowIntervalTime = 10,
suitStoryTextKey = "随着朱雀门上刻下的剑痕，李白剑仙之名传遍长安，随后，他潇洒离去，在试剑游历中成为帝国第一强者。他也曾返回故乡云中漠地，但战火已将一切过往繁华化作废墟，质疑与愤怒令他剑指长安，然而，面对女帝的他，结局竟是失意而归，时隔数年，被人们认为一蹶不振的剑仙再度归来，这次他的剑将让整座长安为之动摇。",
bHideInBag = true,
level = 2
},
[407025] = {
id = 407025,
effect = true,
name = "峡谷神箭 后羿",
desc = "（限峡谷模式使用）获取后可以获得后羿的英雄皮肤。",
icon = "T_Arena_Role_1012_01",
getWay = "段位任务产出",
resourceConf = {
model = "sk_pl_156_dev",
material = "MI_PL_156_01_HP01_moba;MI_PL_156_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_156_dev_physics",
materialSlot = "Skin;Skin_02"
},
outEnter = "AS_CH_Enter_PL_156_moba",
outIdle = "YM_MOBA_HouYi_JD_idle_01",
outShow = "AS_CH_IdleShow_PL_156_moba",
shareTexts = v1,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "后羿作为这世间绝无仅有的英勇射手，曾受到统治者的青睐与赞扬。奉命摧毁日之塔的过程中，他被这宏伟奇迹的力量吸引……然统治者的心思永远更为缜密，摧毁了最后一座日之塔的后羿肆意汲取其中能量，天罚却也几乎同时降至，他的光芒被熄灭、埋没入雪原，在千百年间等待一个人将其再次点亮，射出更璀璨的光。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v2,
level = 3
},
[407026] = {
id = 407026,
effect = true,
quality = 2,
name = "白色修罗 铠",
desc = "（限峡谷模式使用）获取后可以获得铠的英雄皮肤。",
icon = "T_Arena_Role_1002",
resourceConf = {
model = "sk_pl_084_dev",
material = "MI_PL_084_01_HP01_moba;MI_PL_084_02_HP01_moba;MI_Wing_084_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_084_dev_physics",
materialSlot = "Skin;Skin_2;Wing"
},
outEnter = "AS_CH_Enter_PL_084_moba",
outShow = "AS_CH_IdleShow_PL_084_moba",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_084",
shareTexts = v1,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "家族所看守的奇迹碎片并非奇迹，而是诅咒，背负诅咒的子嗣最终将在厮杀下融为一体--认知到这一事实的青年被不甘与愤怒支配，于月夜下挥刀屠戮整个家族。利刃却在最后幸存的妹妹面前犹豫，他就此放弃，向东漂泊，却在长城为了拯救某个少年而被魔铠吞噬，失去了所有记忆。花木兰的邀请使他留下，魔道利刃自此化作守护之铠。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v2,
level = 4
},
[407027] = {
id = 407027,
effect = true,
quality = 2,
name = "魅影妖狐 妲己",
desc = "（限峡谷模式使用）获取后可以使用法师妲己。",
icon = "T_Arena_Role_1025",
resourceConf = {
model = "sk_pl_222_moba",
material = "MI_PL_222_01_HP01_moba;MI_PL_222_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_222_moba_physicsasset",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_222_moba",
outShow = "AS_CH_IdleShow_PL_222_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "以机关术为躯，以魔道术为灵魂，没有心的人偶被主人姜子牙献给纣王。她被新主人唤作妲己，但没有心就没有感情，她甚至不确定自己是不是爱上了纣王……她的心在哪?神秘的声音指引妲己打开了纣王爱人的寝陵，作为魂器的纣王心脏随着棺木打开而破碎。妲己和那灵魂在纠缠中沉睡，再度醒来时，仅有寻找“心”的夙愿格外清晰。",
bHideInBag = true,
shareOffset = v2,
level = 4
},
[407028] = {
id = 407028,
effect = true,
quality = 2,
name = "黑洞潜兵 鲁班七号",
desc = "（限峡谷模式使用）获取后可以使用射手鲁班七号。",
icon = "T_Arena_Role_1004",
resourceConf = {
model = "sk_pl_022_dev",
material = "MI_PL_022_01_HP01_moba;MI_PL_022_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin;Skin_Translucent"
},
outEnter = "AS_CH_Enter_PL_022_moba",
outShow = "AS_CH_IdleShow_PL_022_moba",
outShowIntervalTime = 10,
shareTexts = v1,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "鲁班七号是鲁班大师的天才造物，是机关术能制造出来的最精巧的人偶。短胳膊短腿，可爱又可怕，小身材有大能量，具有超强破坏力。因为血族之乱，鲁班被墨子带去邻城支援玄雍，可是，它在途中丢失……在它尚未成形的语言中，人们似乎能听出它对于父亲的崇拜和思念。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v2,
level = 4
},
[407029] = {
id = 407029,
effect = true,
quality = 2,
name = "桃之夭夭 安其拉",
desc = "（限峡谷模式使用）获取后可以使用法师安琪拉。",
icon = "T_Arena_Role_1003",
resourceConf = {
model = "sk_pl_128_dev",
material = "MI_PL_128_01_HP02_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_128_dev_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_128_moba",
outShow = "AS_CH_IdleShow_PL_128_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "她可不是如外表般纯良无害的可爱萝莉--大魔法师梅林因一次失手将自己困在气泡的囚笼，迫不得已才以灵魂占据了路过女孩的身体。然而命运的捉弄使得她与过去的主人亚瑟重逢，曾经的不满与愤恨一并倾泻而出，她势必要来一场以潜伏为前提的恶毒复仇!只是，这具身体对亚瑟的奇妙仰慕还真令她有些招架不及呀……",
bHideInBag = true,
shareOffset = v2,
level = 4
},
[407030] = {
id = 407030,
effect = true,
quality = 2,
name = "大傩行者 孙悟空",
desc = "（限峡谷模式使用）获取后可以使用刺客孙悟空。",
icon = "T_Arena_Role_1016",
resourceConf = {
model = "sk_pl_130_dev",
material = "MI_PL_130_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_130_dev_physics_moba",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_130_moba",
outShow = "AS_CH_IdleShow_PL_130_moba",
outShowIntervalTime = 10,
suitStoryTextKey = "孙悟空生性桀骜，厌恶被管辖和拘束，更憎恶那些虚伪神灵铐在魔种身上的枷锁。黑暗的时代里，他俨然成为反抗的领袖，带领魔种们为自由奋起。起义以失败告终，神灵以绝对的力量击溃了乌合之众，将他封印在某座山脚……然而他的意志没有熄灭，某位路过的僧侣帮助孙悟空冲破束缚重生，齐天大圣的名号再度威震八方。",
bHideInBag = true,
level = 4
},
[407031] = {
id = 407031,
effect = true,
name = "暖冬 王昭君",
desc = "（限峡谷模式使用）获取后可以使用法师王昭君。",
icon = "T_Arena_Role_1018",
resourceConf = {
model = "sk_pl_186_moba",
material = "MI_PL_186_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_186_moba_physicsasset",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_186_moba",
outShow = "AS_CH_IdleShow_PL_186_moba",
outShowIntervalTime = 10,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "王昭君本是中原送来和亲的公主，早已被依照习俗献祭于圣地“凛冬之海”。多年后，贪婪的中原人趁又一个祭祀之日发起突袭，上演了一出染红雪原的“血色婚礼”。然而掠夺却止于暴雪降至，歹徒们被冰封于由雪崩代表的神罚。被拥戴的公主昭君悄然抚摸一只只冰棺，清冷的眼眸中却始终蕴含一丝眷恋与哀伤。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
level = 3
},
[407032] = {
id = 407032,
effect = true,
name = "穷奇 吕布",
desc = "（限峡谷模式使用）获取后可以使用战士吕布。",
icon = "T_Arena_Role_1032",
resourceConf = {
model = "sk_pl_229_moba",
material = "MI_PL_229_01_HP02_moba;MI_PL_229_02_HP02_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "SK_PL_229_moba_Physics",
materialSlot = "SKIN;Skin_1"
},
outEnter = "AS_CH_Enter_PL_229_moba",
outShow = "AS_CH_IdleShow_PL_229_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "王者大陆传颂着战神吕布的强大，但强如吕布也曾与死神擦肩而过。吕布躺在泥泞中奄奄一息时，一个小女孩递上的清水拯救了他，但从梦中被马蹄声惊醒的吕布，疑心被出卖杀光了小女孩一伙。此后战神几经浮沉，一度摸到天下，却被一个女人的舞蹈唤回当年的回忆。这一次，倒下的吕布，在漆黑中，听到一个声音说着“你可以选择的”。",
bHideInBag = true,
shareOffset = v2,
level = 3
},
[407033] = {
id = 407033,
effect = true,
name = "蔷薇水晶 甄姬",
desc = "（限峡谷模式使用）获取后可以使用法师甄姬。",
icon = "T_Arena_Role_1037",
resourceConf = {
model = "sk_pl_239_moba",
material = "MI_PL_239_01_HP01_moba;MI_PL_239_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_239_moba_physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_239_moba",
outShow = "AS_CH_IdleShow_PL_239_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "因着高贵出身，圣者血脉，甄姬在家族的培养下成为了温柔仁慈闻名的绝代美女。尽管自出生起便被要求克制欲望，肩负感化世人净化污秽的责任，但甄姬仍在十八岁时萌动了春心。面对翩翩才子的疯狂追求，甄姬沦陷得比自己想象的更为彻底，抛弃家族，放弃责任。只是她如愿嫁入曹家时，才发现自己所爱的人，爱的只是自己的力量。",
bHideInBag = true,
shareOffset = v2,
level = 3
},
[407034] = {
id = 407034,
effect = true,
name = "异界红莲 孙尚香",
desc = "（限峡谷模式使用）获取后可以使用射手孙尚香。",
icon = "T_Arena_Role_1014",
resourceConf = {
model = "sk_pl_129_dev",
material = "MI_PL_129_01_HP01_moba;MI_PL_129_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_129_dev_physics",
materialSlot = "Skin;Skin_02"
},
outEnter = "AS_CH_Enter_PL_129_moba",
outShow = "AS_CH_IdleShow_PL_129_moba",
outShowIntervalTime = 10,
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "自长兄孙策奇袭江东建立新的国家开始，孙尚香便为了理想而战斗，但小公主似乎更擅长制造麻烦，她的枪炮甚至令海上的海盗都闻风丧胆。通往理想之路上，长兄不幸遇刺身亡使她不得不面对压力，在象征着责任的王位面前，她最终选择了用手中的炮火实现已故兄长的理想乡。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
level = 3
},
[407035] = {
id = 407035,
effect = true,
name = "冰暴 铠",
desc = "（限峡谷模式使用）获取后可以使用战士铠。",
icon = "T_Arena_Role_1002_01",
resourceConf = {
model = "sk_pl_084_dev",
material = "MI_PL_084_01_HP02_moba;MI_PL_084_02_HP02_moba;MI_Wing_084_01_HP02_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_084_dev_physics",
materialSlot = "Skin;Skin_2;Wing"
},
outEnter = "AS_CH_Enter_PL_084_moba",
outShow = "AS_CH_IdleShow_PL_084_moba",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_084",
shareTexts = v1,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "家族所看守的奇迹碎片并非奇迹，而是诅咒，背负诅咒的子嗣最终将在厮杀下融为一体--认知到这一事实的青年被不甘与愤怒支配，于月夜下挥刀屠戮整个家族。利刃却在最后幸存的妹妹面前犹豫，他就此放弃，向东漂泊，却在长城为了拯救某个少年而被魔铠吞噬，失去了所有记忆。花木兰的邀请使他留下，魔道利刃自此化作守护之铠。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407036] = {
id = 407036,
effect = true,
name = "摇滚甜心 小乔",
desc = "（限峡谷模式使用）获取后可以使用法师小乔。",
icon = "T_Arena_Role_1007_04",
resourceConf = {
model = "sk_pl_086_moba",
material = "MI_PL_086_01_HP02_moba;MI_PL_086_02_HP02_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_086_moba_physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_086_moba",
outShow = "AS_CH_IdleShow_PL_086_moba",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_086",
shareTexts = v1,
suitStoryTextKey = "对于江东乔氏而言，双胞胎是不详的征兆，大小乔自出生起便被迫分开。小乔被送往稷下直至长大才得以回家，但族人震惊其魔道天赋，唯恐魔道家族的秘密因此暴露，又将小乔送往东海边小镇。不久，小镇疫病横行引发暴乱，小乔召唤海风吹散毒雾时，被前来镇压的周瑜当成嫌犯，谁也没想到这次邂逅，却是两人情愫的开始。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407037] = {
id = 407037,
effect = true,
name = "紫渊潜龙 兰陵王",
desc = "（限峡谷模式使用）获取后可以使用刺客兰陵王。",
icon = "T_Arena_Role_1005_01",
resourceConf = {
model = "sk_pl_087_moba",
material = "MI_PL_087_01_HP01_moba;MI_PL_087_2_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_087_moba_physics",
materialSlot = "Skin;Lan_Hair"
},
outEnter = "AS_CH_Enter_PL_087_moba",
outShow = "AS_CH_IdleShow_PL_087_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "曾经高贵的兰陵王，在命运转折下痛失一切，无情的帝国铁骑将王都城池踏破，断垣残壁下埋藏着仇恨的种子。神秘老人将他带走，再度出现他已成为潜行于黑暗的杀戮者，复仇则是唯一的使命。而长城，将作为堡垒承受他所有的怒火。暗影之下，曾经的王终会夺回属于他的一切，哪怕前路漫漫哪怕将有那绯红身影前来阻拦。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407038] = {
id = 407038,
effect = true,
name = "一叶知秋 宫本武藏",
desc = "（限峡谷模式使用）获取后可以使用战士宫本武藏。",
icon = "T_Arena_Role_1034_01",
resourceConf = {
model = "sk_pl_225_moba",
material = "MI_PL_225_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "SK_PL_225_moba_PhysicsAsset",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_225_moba",
outShow = "AS_CH_IdleShow_PL_225_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "左手长刀，右手短剑，无人可挡的高超剑势令宫本武藏人挡杀人，佛挡杀佛。他力挑天下武道会的冠军，斩杀于扶桑兴风作浪的血族徐福，却因苦于再无对手，动身前往强者汇聚的长安，要找剑仙李白，治安官狄仁杰等人一较高下。大概他无敌人生中唯一的苦恼就是，今天好像又迷路了。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407039] = {
id = 407039,
effect = true,
name = "花期将至 蔡文姬",
desc = "（限峡谷模式使用）获取后可以使用辅助蔡文姬。",
icon = "T_Arena_Role_1013_01",
resourceConf = {
model = "sk_pl_131_dev",
material = "MI_PL_131_01_HP01_moba;MI_PL_131_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_131_dev_physics",
materialSlot = "Skin;Skin_Translucent"
},
outEnter = "AS_CH_Enter_PL_131_moba",
outShow = "AS_CH_IdleShow_PL_131_moba",
outShowIntervalTime = 10,
suitStoryTextKey = "即便生活在三分乱世之地，蔡文姬也拥有纯挚的内心。她怀念已故的父亲，崇敬抚养自己的义父，信任身边沉默的典韦，但关于父亲的去世，义父说凶手是诸葛亮，典韦却闪烁其词。某日终于迎来真相，当典韦让她带着玉玺逃离武都时，耳畔又响起爹爹的声音:未来不管发生什么，爹爹都希望文姬能简单快乐地长大，倘若终将面临困惑，希望你终能用爱访过它……",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407040] = {
id = 407040,
effect = true,
name = "深红漫游者 鲁班七号",
desc = "（限峡谷模式使用）获取后可以使用射手鲁班七号。",
icon = "T_Arena_Role_1004_02",
resourceConf = {
model = "sk_pl_022_dev",
material = "MI_PL_022_01_HP02_moba;MI_PL_022_02_HP02_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin;Skin_Translucent"
},
outEnter = "AS_CH_Enter_PL_022_moba",
outShow = "AS_CH_IdleShow_PL_022_moba",
outShowIntervalTime = 10,
shareTexts = v1,
shareAnim = "AS_CH_Pose_001_PL_022",
suitStoryTextKey = "鲁班七号是鲁班大师的天才造物，是机关术能制造出来的最精巧的人偶。短胳膊短腿，可爱又可怕，小身材有大能量，具有超强破坏力。因为血族之乱，鲁班被墨子带去邻城支援玄雍，可是，它在途中丢失……在它尚未成形的语言中，人们似乎能听出它对于父亲的崇拜和思念。",
timeToStartAnim = 2.1600000858306885,
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407041] = {
id = 407041,
effect = true,
name = "变装魔女 安其拉",
desc = "（限峡谷模式使用）获取后可以使用法师安琪拉。",
icon = "T_Arena_Role_1003_01",
resourceConf = {
model = "sk_pl_128_dev",
material = "MI_PL_128_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_128_dev_physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_128_moba",
outShow = "AS_CH_IdleShow_PL_128_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "她可不是如外表般纯良无害的可爱萝莉--大魔法师梅林因一次失手将自己困在气泡的囚笼，迫不得已才以灵魂占据了路过女孩的身体。然而命运的捉弄使得她与过去的主人亚瑟重逢，曾经的不满与愤恨一并倾泻而出，她势必要来一场以潜伏为前提的恶毒复仇!只是，这具身体对亚瑟的奇妙仰慕还真令她有些招架不及呀……",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407042] = {
id = 407042,
effect = true,
name = "暖心学长 张良",
desc = "（限峡谷模式使用）获取后可以使用法师张良。",
icon = "T_Arena_Role_1029_01",
resourceConf = {
model = "SK_PL_227_moba",
material = "MI_PL_227_01_HP01_moba;MI_PL_227_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "SK_PL_227_moba_PhysicsAsset",
materialSlot = "SKIN;SKIN_1"
},
outEnter = "AS_CH_Enter_PL_227_moba",
outShow = "AS_CH_IdleShow_PL_227_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "令姜子牙都感到惊异的天才弟子，开创出自己独特魔道“言灵”的张良，他精通世间所有语言，甚至可以和整个世界交流。真理与他无限接近，所有魔道在他面前都不堪一击。姜子牙命他进入俗世，只为纠正世间混乱的秩序。但走入大河流域的张良与世隔绝太久，万能的言灵也无法挽救他的社交。看来，融入这个世界才是现在最大的难题。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407043] = {
id = 407043,
effect = true,
name = "寒铁特装 刘禅",
desc = "（限峡谷模式使用）获取后可以使用辅助刘禅。",
icon = "T_Arena_Role_1043_01",
resourceConf = {
model = "sk_pl_240_moba",
material = "MI_PL_240_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
materialSlot = "Skin",
skeletalMesh = "SK_PL_240_moba",
animClassName = "Bp_LiuShan_PreviewAnimInstance"
},
outEnter = "AS_CH_Enter_PL_240_moba",
outIdle = "YM_MOBA_240_Idle_IdleShow",
outShow = "AS_CH_IdleShow_PL_240_moba",
outShowIntervalTime = 10,
scaleTimes = 75,
shareTexts = v1,
suitStoryTextKey = "刘备之子，诸葛亮之徒，头顶诸多头衔的刘禅一直想证明自己是老爹和师父青出于蓝的继承者。但为了证明自己天才之名的挑战稷下墨子计划，因赤壁之战爆发而终止。当刘禅驾驶着自己设计的熊猫造型初号机进入战场后，却碰见了意想不到的敌人，一个有着渐变发色和拉风胡笳琴的可爱少女，初号机徒劳地发出预警:警告，心跳过快!",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407044] = {
id = 407044,
effect = true,
name = "橙意暖暖 绮",
desc = "（限峡谷模式使用）获取后可以使用法师绮。",
icon = "T_Arena_Role_1049_02",
resourceConf = {
model = "SK_PL_288_moba",
material = "MI_PL_288_01_HP01_moba;MI_PL_288_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "SK_PL_288_moba_Physics",
materialSlot = "Skin;Skin_1",
skeletalMesh = "SK_PL_288_moba"
},
outEnter = "AS_CH_Enter_PL_288_moba",
outShow = "AS_CH_IdleShow_PL_288_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = [[相传在一幅泛黄的古画中，沉睡着一位画灵。发丝如初绽的桃花，眉目如晕染开的水墨。她是一代画圣临终前的最后一副作品，并赋名为——绮。画圣最后一笔点睛，魂魄化作春风散去，而绮却在那一刻缓缓睁开了眼眸，继承了画圣毕生所追求的至善至美，以及未竟的心愿——绘尽世间所有的美好。

画中自有千秋雪，画外却是万般春。绮临摹万物，渴望捕捉画外的光影，可墨色再浓，也难画出风的温度，笔触再妙，也难描摹内心的悸动。直到一个月圆之夜，绮用朱砂点破画卷，将自己从纸上剥离。画中角，终成画外仙。

初入元梦世界，绮对一切都充满好奇。她用画笔记录晨曦初露的微光，描摹夜市灯火的流转，甚至尝试勾勒星宝们的笑颜——可总觉得笔下生花，却少了几分温度。
她渴望创造属于自己的“真实”，于是，在一个细雨微落的春日午后，她轻轻挥动画笔，在纸上描绘出一朵带着露珠的花。那是一朵她从未在世间见过的奇花，花瓣如霞，花心闪烁微光。就在最后一笔落下的瞬间——那朵花竟缓缓绽放，化作灵体，轻盈地飘在绮的肩畔。

她为这朵花灵取名为——夭夭。夭夭是绮画出的第一个真实生命，也是她的画笔第一次“唤醒”了情感的具象。它不是普通的花灵，而是能感知情绪的存在。当绮悲伤时，夭夭会轻轻摇曳，用花瓣拭去她的眼泪；当她喜悦时，夭夭便会绽放出七彩光辉，仿佛回应她心中的欢愉。从此，夭夭便成了她最亲密的伙伴，也成为了她画中梦境的引领者。

后来绮又遇见一个哭泣的星宝——他的纸鸢破损，泪水涟涟。绮执笔描绘，奇迹再次发生——那只凤凰竟从纸上飞起，盘旋在空中，化作真正的灵鸟！小星宝破涕为笑，而绮终于明白：画，不止于形，更在于心。

或许是因为灵魂来自画卷的缘故，绮常常坠入神秘的梦境。梦里，她看见了另一个世界——那里没有苍白的墨色，只有斑斓的光影与异想天开的奇景，甚至还有另一个自己！绮行走四方，将梦境中的画变为现实，为怯懦者添上勇气的双翼，为孤独者描绘出相伴的知己……她的画，已不仅仅是画，而是承载了真实情感的生命。夭夭常伴她左右，见证着她的每一幅画，每一次心动。

“世间万物，皆可入画；但若画中无情，终究不过是空壳。”她如此说道，笑容如初绽的桃花，美得不似世间所有。]],
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407045] = {
id = 407045,
effect = true,
name = "职场之星 典韦",
desc = "（限峡谷模式使用）获取后可以使用战士典韦。",
icon = "T_Arena_Role_1036_01",
resourceConf = {
model = "sk_pl_234_moba",
material = "MI_PL_234_01_HP01_moba;MI_PL_234_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_234_moba_physics",
materialSlot = "skin;skin_1"
},
outEnter = "AS_CH_Enter_PL_234_moba",
outShow = "AS_CH_IdleShow_PL_234_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "狂野的兽性与超凡的力量让曾经的典韦备受恐惧与唾弃，直到与恩师蔡邕的邂逅将他从绝望的边缘拉回--循循善诱的长者与其活泼可爱的幼小女儿成为了照亮深邃黑暗的光。他以为自已终将为人，然而枭雄曹操的阴谋再次将他推回深渊，蔡邕逝世，臣服于曹操是他的选择，理智或许会因嗜血兽性而淡化，但守护珍视之人的信念绝不退减。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407046] = {
id = 407046,
effect = true,
name = "金鳞灼灼 大乔",
desc = "（限峡谷模式使用）获取后可以使用辅助大乔。",
icon = "T_Arena_Role_1039_01",
resourceConf = {
model = "sk_pl_241_moba",
material = "MI_PL_214_01_HP01_moba;MI_PL_241_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_241_moba_physics",
materialSlot = "skin;skin_1"
},
outEnter = "AS_CH_Enter_PL_241_moba",
outShow = "AS_CH_IdleShow_PL_241_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "因为同样被诅咒的命运，大乔曾发自内心感激着司马懿培养。但最终她还是沦为家族献给新任江东统治者的牺牲品。出嫁前夜，她提灯伫立海崖，却意外发现原本以为的暴虐君主，竟然是充斥活力的爽朗青年。爱意的萌生是突兀而简单，孙策就像是一团不灭的暖阳，耀眼而炽热，直直照进她的灵魂。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407047] = {
id = 407047,
effect = true,
name = "黑化策士 诸葛亮",
desc = "（限峡谷模式使用）获取后可以使用法师诸葛亮。",
icon = "T_Arena_Role_1044_02",
resourceConf = {
model = "sk_pl_236_moba",
material = "MI_PL_236_01_HP02_moba;MI_PL_236_02_HP02_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_236_moba_physics",
materialSlot = "Skin_Lod1;Skin_1_lod1"
},
outEnter = "AS_CH_Enter_PL_236_moba",
outShow = "AS_CH_IdleShow_PL_236_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "稷下学院全科第一的传奇学长，是公认的夫子继承者，他沉迷于研究天书的奥秘，行走于世间每个知识的角落，最终，定居蜀地草庐潜心解析。但血族之王徐福的归来，以及好友蔡邕的意外身死……使他不得不着眼于和枭雄曹操的对抗，赤壁之上，沉寂的可怕武器东风祭坛被再次唤醒，战事之后，或许一切终究明了。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407048] = {
id = 407048,
effect = true,
name = "钢铁硬汉 程咬金",
desc = "（限峡谷模式使用）获取后可以使用坦克程咬金。",
icon = "T_Arena_Role_1030_01",
resourceConf = {
model = "SK_PL_228_moba",
material = "MI_PL_228_01_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "SK_PL_228_moba_Physics",
materialSlot = "Skin"
},
outEnter = "AS_CH_Enter_PL_228_moba",
outShow = "AS_CH_IdleShow_PL_228_moba",
outShowIntervalTime = 10,
shareTexts = v1,
suitStoryTextKey = "一身健美的肌肉，饱含铁血的男儿柔情，说的便是口口相传的名将程咬金。他认为力量从来不是争斗与厮杀的武器，而是挑战自然的资本。但与偶然结识的友人李靖共同退治暴徒的经历使他改观，自此，他周游各地，守护弱小，惩恶扬善，誓要让肌肉和汗水在太阳照不到的黑暗角落里散发光辉。",
bHideInBag = true,
shareOffset = v2,
level = 2
},
[407049] = {
id = 407049,
effect = true,
name = "峡谷炽枫 孙尚香",
desc = "（限峡谷模式使用）获取后可以使用射手孙尚香的英雄皮肤。",
icon = "T_Arena_Role_1014",
resourceConf = {
model = "sk_pl_129_dev",
material = "MI_PL_129_01_HP01_moba;MI_PL_129_02_HP01_moba",
headOffset = v2,
backOffset = v2,
faceOffset = v2,
physics = "sk_pl_129_dev_physics",
materialSlot = "Skin;Skin_02"
},
outEnter = "AS_CH_Enter_PL_129_moba",
outIdle = "YM_MOBA_SSX_YJLQ_idle_Qualifying",
outShow = "AS_CH_IdleShow_PL_129_moba",
shareTexts = {
"我好像，弄丢了一个很珍贵的愿望",
"说不定，我们认识很久了……",
"跟随梦的指引，是偶遇还是重逢？",
"请聆听我的祈愿，解开记忆之锁！",
"魔法与羁绊，需要一点时间来生效"
},
shareAnim = "AS_CH_Pose_001_PL_023",
suitStoryTextKey = "自长兄孙策奇袭江东建立新的国家开始，孙尚香便为了理想而战斗，但小公主似乎更擅长制造麻烦，她的枪炮甚至令海上的海盗都闻风丧胆。通往理想之路上，长兄不幸遇刺身亡使她不得不面对压力，在象征着责任的王位面前，她最终选择了用手中的炮火实现已故兄长的理想乡。",
timeToStartAnim = 2.4700000286102295,
bHideInBag = true,
level = 3
}
}

local mt = {
effect = false,
type = "ItemType_Arena_HeroSkin",
maxNum = 1,
quality = 3,
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
billboardOffsetZ = 0,
bHideInBag = false,
tags = {
1
},
customDesc = "峡谷英雄皮肤"
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data