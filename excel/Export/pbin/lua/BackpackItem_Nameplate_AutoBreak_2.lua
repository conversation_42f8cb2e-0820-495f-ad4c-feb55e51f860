--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化.xlsx: 铭牌

local data = {
[820201] = {
id = 820201,
type = "ItemType_NamePlate",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.88.1",
quality = 2,
name = "忽而心动",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_196",
resourceConf = {
bg = "CDN:T_NameFrame_bg_196"
},
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1750003200
}
},
[820202] = {
id = 820202,
type = "ItemType_NamePlate",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 40
}
},
lowVer = "1.3.88.1",
quality = 2,
name = "独家童话",
desc = "活动获得",
icon = "CDN:Icon_NameFrame_197",
resourceConf = {
bg = "CDN:T_NameFrame_bg_197"
},
showInView = 1,
isDynamic = 0,
beginTime = {
seconds = 1750003200
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data