--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-表情

local v0 = {
seconds = 1702569600
}

local v1 = {
seconds = 1676390400
}

local v2 = {
seconds = 4074768000
}

local v3 = {
seconds = 1676476800
}

local v4 = {
seconds = 4074854400
}

local v5 = {
seconds = 1746115199
}

local v6 = 9

local v7 = "赛季祈愿"

local v8 = 0

local v9 = 6

local v10 = 180

local v11 = 1

local v12 = "1.2.100.1"

local v13 = "1.3.6.1"

local v14 = "1.3.12.1"

local v15 = "1.3.26.1"

local data = {
[80001] = {
commodityId = 80001,
commodityName = "听我解释",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710001
}
},
[80002] = {
commodityId = 80002,
commodityName = "极力否认",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 46,
jumpText = "潮玩星方向",
gender = 0,
itemIds = {
710002
}
},
[80003] = {
commodityId = 80003,
commodityName = "慢走不送",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710003
}
},
[80004] = {
commodityId = 80004,
commodityName = "冲呀冲呀",
beginTime = v1,
endTime = v3,
jumpId = 44,
jumpText = "小乔送星运",
gender = 0,
itemIds = {
710004
}
},
[80005] = {
commodityId = 80005,
commodityName = "赠花",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 3,
jumpText = "赛季兑换",
gender = 0,
itemIds = {
710005
}
},
[80006] = {
commodityId = 80006,
commodityName = "寄刀片",
beginTime = v1,
endTime = v3,
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710006
}
},
[80007] = {
commodityId = 80007,
commodityName = "鄙视",
beginTime = v1,
endTime = v3,
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710007
}
},
[80008] = {
commodityId = 80008,
commodityName = "暗送秋波",
beginTime = v1,
endTime = v3,
jumpId = 180,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
710008
}
},
[80009] = {
commodityId = 80009,
commodityName = "午夜心碎",
beginTime = v1,
endTime = v3,
jumpId = 180,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
710009
}
},
[80010] = {
commodityId = 80010,
commodityName = "上香祈福",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710010
}
},
[80011] = {
commodityId = 80011,
commodityName = "含情脉脉",
beginTime = v1,
endTime = v3,
jumpId = 33,
jumpText = "闯关挑战",
gender = 0,
itemIds = {
710011
}
},
[80012] = {
commodityId = 80012,
commodityName = "做鬼脸",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710012
}
},
[80013] = {
commodityId = 80013,
commodityName = "细细品味",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710013
}
},
[80014] = {
commodityId = 80014,
commodityName = "星星眼",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 9,
jumpText = "桃源通行证",
gender = 0,
itemIds = {
710014
}
},
[80015] = {
commodityId = 80015,
commodityName = "打入心房",
jumpId = 27,
jumpText = "首充",
gender = 0,
itemIds = {
710015
}
},
[80016] = {
commodityId = 80016,
commodityName = "宝宝叹气",
jumpId = 1808,
jumpText = "个人成就",
gender = 0,
itemIds = {
710016
}
},
[80017] = {
commodityId = 80017,
commodityName = "冤枉流泪",
beginTime = v1,
endTime = v3,
jumpId = 180,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
710017
}
},
[80018] = {
commodityId = 80018,
commodityName = "疼痛流泪",
beginTime = v1,
endTime = v3,
jumpId = 180,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
710018
}
},
[80019] = {
commodityId = 80019,
commodityName = "加油",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 45,
jumpText = "娱乐大师礼",
gender = 0,
itemIds = {
710019
}
},
[80020] = {
commodityId = 80020,
commodityName = "开心",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 46,
jumpText = "潮玩新方向",
gender = 0,
itemIds = {
710020
}
},
[80021] = {
commodityId = 80021,
commodityName = "亲亲",
beginTime = v0,
endTime = {
seconds = 1704383999
},
jumpId = 44,
jumpText = "小乔送星运",
gender = 0,
itemIds = {
710021
}
},
[80022] = {
commodityId = 80022,
commodityName = "问好",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 47,
jumpText = "初星代言人",
gender = 0,
itemIds = {
710022
}
},
[80023] = {
commodityId = 80023,
commodityName = "哭哭",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710023
}
},
[80024] = {
commodityId = 80024,
commodityName = "生气",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710024
}
},
[80025] = {
commodityId = 80025,
commodityName = "脸红",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710025
}
},
[80026] = {
commodityId = 80026,
commodityName = "摊手",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710026
}
},
[80027] = {
commodityId = 80027,
commodityName = "叉腰",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710027
}
},
[80028] = {
commodityId = 80028,
commodityName = "打气",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710028
}
},
[80029] = {
commodityId = 80029,
commodityName = "惊呆了",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710029
}
},
[80030] = {
commodityId = 80030,
commodityName = "累瘫了",
beginTime = v1,
endTime = v3,
jumpId = 1808,
jumpText = "个人成就",
gender = 0,
itemIds = {
710030
}
},
[80031] = {
commodityId = 80031,
commodityName = "亲密无间",
coinType = 6,
price = 20,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710031
}
},
[80032] = {
commodityId = 80032,
commodityName = "含泪点赞",
beginTime = v1,
endTime = v3,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
itemIds = {
710032
}
},
[80033] = {
commodityId = 80033,
commodityName = "大跌眼镜",
beginTime = v1,
endTime = v3,
jumpId = 38,
jumpText = "新手任务",
gender = 0,
itemIds = {
710033
}
},
[80034] = {
commodityId = 80034,
commodityName = "太酷啦",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 3,
jumpText = "赛季兑换",
gender = 0,
itemIds = {
710034
}
},
[80035] = {
commodityId = 80035,
commodityName = "大哭",
coinType = 6,
price = 10,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710035
}
},
[80036] = {
commodityId = 80036,
commodityName = "尴尬",
beginTime = v0,
endTime = {
seconds = 1706198399
},
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710036
}
},
[80037] = {
commodityId = 80037,
commodityName = "露齿笑",
beginTime = v1,
endTime = v3,
jumpId = 180,
jumpText = "印章祈愿",
gender = 0,
itemIds = {
710037
}
},
[80038] = {
commodityId = 80038,
commodityName = "我酸了",
coinType = 6,
price = 180,
beginTime = v0,
gender = 0,
itemIds = {
710038
},
canGift = true,
addIntimacy = 18,
giftCoinType = 1,
giftPrice = 180
},
[80039] = {
commodityId = 80039,
commodityName = "偷笑",
coinType = 6,
price = 180,
beginTime = v0,
gender = 0,
itemIds = {
710039
},
canGift = true,
addIntimacy = 18,
giftCoinType = 1,
giftPrice = 180
},
[80040] = {
commodityId = 80040,
commodityName = "美味",
coinType = 6,
price = 180,
beginTime = v0,
gender = 0,
itemIds = {
710040
},
canGift = true,
addIntimacy = 18,
giftCoinType = 1,
giftPrice = 180
},
[80041] = {
commodityId = 80041,
commodityName = "熬夜",
coinType = 6,
price = 180,
beginTime = v0,
gender = 0,
itemIds = {
710041
},
canGift = true,
addIntimacy = 18,
giftCoinType = 1,
giftPrice = 180
},
[80042] = {
commodityId = 80042,
commodityName = "流汗",
coinType = 6,
price = 180,
beginTime = v0,
gender = 0,
itemIds = {
710042
},
canGift = true,
addIntimacy = 18,
giftCoinType = 1,
giftPrice = 180
},
[80043] = {
commodityId = 80043,
commodityName = "奋斗",
beginTime = v1,
endTime = v3,
shopSort = 6,
jumpId = 38,
jumpText = "新星指南",
gender = 0,
itemIds = {
710043
}
},
[80044] = {
commodityId = 80044,
commodityName = "疑惑",
coinType = 6,
price = 180,
beginTime = v0,
gender = 0,
itemIds = {
710044
},
canGift = true,
addIntimacy = 18,
giftCoinType = 1,
giftPrice = 180
},
[80045] = {
commodityId = 80045,
commodityName = "打哈欠",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopSort = 1,
jumpId = 11,
jumpText = v7,
gender = 0,
itemIds = {
710045
}
},
[80046] = {
commodityId = 80046,
commodityName = "奸笑",
coinType = 6,
price = 10,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710046
}
},
[80047] = {
commodityId = 80047,
commodityName = "可怜",
coinType = 6,
price = 180,
beginTime = v0,
gender = 0,
itemIds = {
710047
},
canGift = true,
addIntimacy = 18,
giftCoinType = 1,
giftPrice = 180
},
[80048] = {
commodityId = 80048,
commodityName = "再见",
beginTime = v0,
endTime = {
seconds = 1706198399
},
shopSort = 5,
jumpId = 9,
jumpText = "桃源通行证",
gender = 0,
itemIds = {
710048
}
},
[80049] = {
commodityId = 80049,
commodityName = "暗中观察",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopSort = 3,
jumpId = 1063,
jumpText = "守护之翼",
gender = 0,
minVersion = "1.3.26.33",
itemIds = {
710049
}
},
[80050] = {
commodityId = 80050,
commodityName = "虾虾耶",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopSort = 1,
jumpId = 80,
jumpText = "盛装小新祈愿",
gender = 0,
itemIds = {
710051
}
},
[80051] = {
commodityId = 80051,
commodityName = "动感超人出击",
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1715875199
},
shopSort = 1,
jumpId = 78,
jumpText = "盛装小新祈愿",
gender = 0,
itemIds = {
710052
}
},
[80052] = {
commodityId = 80052,
commodityName = "打起精神",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710053
}
},
[80053] = {
commodityId = 80053,
commodityName = "小新星星眼",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710054
}
},
[80054] = {
commodityId = 80054,
commodityName = "让我听听",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710055
}
},
[80055] = {
commodityId = 80055,
commodityName = "挥手告别",
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1718899199
},
shopSort = 1,
jumpId = 182,
jumpText = "阿童木祈愿",
gender = 0,
minVersion = v12,
itemIds = {
710056
}
},
[80056] = {
commodityId = 80056,
commodityName = "全力创作",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710057
}
},
[80057] = {
commodityId = 80057,
commodityName = "星想法",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710058
}
},
[80058] = {
commodityId = 80058,
commodityName = "梦梦鼓掌",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710059
}
},
[80059] = {
commodityId = 80059,
commodityName = "啊？",
coinType = 6,
price = 5,
beginTime = v1,
endTime = v3,
gender = 0,
itemIds = {
710060
}
},
[80060] = {
commodityId = 80060,
commodityName = "拜托拜托",
coinType = 6,
price = 500,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
gender = 0,
itemIds = {
710061
}
},
[80061] = {
commodityId = 80061,
commodityName = "Toby喝奶茶",
coinType = 6,
price = 180,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
gender = 0,
itemIds = {
710062
}
},
[80062] = {
commodityId = 80062,
commodityName = "捏捏",
coinType = 6,
price = 180,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
gender = 0,
itemIds = {
710063
}
},
[80063] = {
commodityId = 80063,
commodityName = "Toby哇呀呀",
coinType = 6,
price = 180,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
gender = 0,
itemIds = {
710064
}
},
[80064] = {
commodityId = 80064,
commodityName = "瓜瓜飞吻",
coinType = 6,
price = 180,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
gender = 0,
itemIds = {
710065
}
},
[80065] = {
commodityId = 80065,
commodityName = "瓜瓜害羞",
coinType = 6,
price = 180,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
gender = 0,
itemIds = {
710066
}
},
[80066] = {
commodityId = 80066,
commodityName = "哇哦",
coinType = 6,
price = 180,
beginTime = {
seconds = 1704988800
},
endTime = {
seconds = 1768147200
},
gender = 0,
itemIds = {
710067
}
},
[80067] = {
commodityId = 80067,
commodityName = "你干什么？",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
jumpId = 181,
jumpText = "绮梦灯",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710075
}
},
[80068] = {
commodityId = 80068,
commodityName = "口吐彩虹",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 9,
jumpText = "山海通行证",
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710076
}
},
[80069] = {
commodityId = 80069,
commodityName = "为你星动",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
jumpId = 181,
jumpText = "绮梦灯",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710077
}
},
[80070] = {
commodityId = 80070,
commodityName = "我没事",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 9,
jumpText = "山海通行证",
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710078
}
},
[80071] = {
commodityId = 80071,
commodityName = "皱眉",
coinType = 6,
price = 180,
beginTime = v1,
endTime = v3,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710079
}
},
[80072] = {
commodityId = 80072,
commodityName = "旺柴笑",
beginTime = {
seconds = 1718640000
},
endTime = {
seconds = 1720367999
},
shopSort = 1,
jumpId = 604,
jumpText = "天鹅之心",
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710080
}
},
[80073] = {
commodityId = 80073,
commodityName = "默默流泪",
coinType = 6,
price = 180,
beginTime = v1,
endTime = v3,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710081
}
},
[80074] = {
commodityId = 80074,
commodityName = "难过",
coinType = 6,
price = 180,
beginTime = v1,
endTime = v3,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710082
}
},
[80075] = {
commodityId = 80075,
commodityName = "让我看看",
coinType = 6,
price = 180,
beginTime = v1,
endTime = v3,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710083
}
},
[80076] = {
commodityId = 80076,
commodityName = "墨镜小红狐",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v7,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710096
}
},
[80077] = {
commodityId = 80077,
commodityName = "哼哼",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v7,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710097
}
},
[80078] = {
commodityId = 80078,
commodityName = "哈哈",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v7,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710098
}
},
[80079] = {
commodityId = 80079,
commodityName = "斜眼笑",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v7,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710100
}
},
[80080] = {
commodityId = 80080,
commodityName = "发呆",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v7,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710101
}
},
[80081] = {
commodityId = 80081,
commodityName = "对对对",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v7,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710102
}
},
[80082] = {
commodityId = 80082,
commodityName = "星星眼",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
jumpId = 175,
jumpText = v7,
gender = 0,
minVersion = "1.2.67.1",
itemIds = {
710103
}
},
[80083] = {
commodityId = 80083,
commodityName = "震撼",
beginTime = {
seconds = 1708704000
},
endTime = {
seconds = 1710086399
},
jumpId = 183,
jumpText = "冰雪玫瑰",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710099
}
},
[80084] = {
commodityId = 80084,
commodityName = "贴贴",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
jumpId = 176,
jumpText = "蔬菜精灵祈愿",
gender = 0,
itemIds = {
710104
}
},
[80085] = {
commodityId = 80085,
commodityName = "你好",
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1715529599
},
jumpId = 175,
jumpText = "蔬菜精灵祈愿",
gender = 0,
itemIds = {
710105
}
},
[80086] = {
commodityId = 80086,
commodityName = "是心动呀",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710106
}
},
[80087] = {
commodityId = 80087,
commodityName = "给你小心心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710107
}
},
[80088] = {
commodityId = 80088,
commodityName = "缘来是你",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710108
}
},
[80089] = {
commodityId = 80089,
commodityName = "华丽出场",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710109
}
},
[80090] = {
commodityId = 80090,
commodityName = "还有谁",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710110
}
},
[80091] = {
commodityId = 80091,
commodityName = "燥起来",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710111
}
},
[80092] = {
commodityId = 80092,
commodityName = "我听不见",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710112
}
},
[80093] = {
commodityId = 80093,
commodityName = "抱抱我",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710113
}
},
[80094] = {
commodityId = 80094,
commodityName = "快理理我嘛",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710114
}
},
[80095] = {
commodityId = 80095,
commodityName = "期待",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 177,
jumpText = "暗夜冰羽",
gender = 0,
minVersion = v12,
itemIds = {
710115
}
},
[80096] = {
commodityId = 80096,
commodityName = "师傅沉默",
coinType = 0,
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
jumpId = 1048,
jumpText = "功夫熊猫返场",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
710124
}
},
[80097] = {
commodityId = 80097,
commodityName = "阿宝吃惊",
coinType = 0,
beginTime = {
seconds = 1720454400
},
endTime = {
seconds = 1721923199
},
jumpId = 1048,
jumpText = "功夫熊猫返场",
gender = 0,
minVersion = "1.3.7.97",
itemIds = {
710126
}
},
[80098] = {
commodityId = 80098,
commodityName = "汗流浃背",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710118
}
},
[80099] = {
commodityId = 80099,
commodityName = "尊嘟假嘟",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710119
}
},
[80100] = {
commodityId = 80100,
commodityName = "惊了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710120
}
},
[80101] = {
commodityId = 80101,
commodityName = "就这？",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710121
}
},
[80102] = {
commodityId = 80102,
commodityName = "让我听听",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710122
}
},
[80103] = {
commodityId = 80103,
commodityName = "无言以对",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710123
}
},
[80104] = {
commodityId = 80104,
commodityName = "师傅沉默",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710124
}
},
[80105] = {
commodityId = 80105,
commodityName = "小真得意",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710125
}
},
[80106] = {
commodityId = 80106,
commodityName = "阿宝吃惊",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710126
}
},
[80107] = {
commodityId = 80107,
commodityName = "阿宝承让",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710127
}
},
[80108] = {
commodityId = 80108,
commodityName = "太牛了",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopSort = 1,
jumpId = 561,
jumpText = "春水溯游",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
710128
}
},
[80109] = {
commodityId = 80109,
commodityName = "噗",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
jumpId = 177,
jumpText = "暗夜冰羽",
gender = 0,
minVersion = v12,
itemIds = {
710129
}
},
[80110] = {
commodityId = 80110,
commodityName = "我可真帅",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
jumpId = 561,
jumpText = "春水溯游",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
710130
}
},
[80111] = {
commodityId = 80111,
commodityName = "真下饭",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
jumpId = 561,
jumpText = "春水溯游",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
710131
}
},
[80112] = {
commodityId = 80112,
commodityName = "笑拉了，家人们",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
jumpId = 561,
jumpText = "春水溯游",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
710132
}
},
[80113] = {
commodityId = 80113,
commodityName = "略略略",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
jumpId = 561,
jumpText = "春水溯游",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
710133
}
},
[80114] = {
commodityId = 80114,
commodityName = "百发百中",
beginTime = {
seconds = 1740672000
},
endTime = v4,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710134
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[80115] = {
commodityId = 80115,
commodityName = "你可真棒",
beginTime = {
seconds = 1740672000
},
endTime = v4,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710135
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[80116] = {
commodityId = 80116,
commodityName = "我想开了",
beginTime = {
seconds = 1740672000
},
endTime = v4,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710136
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[80117] = {
commodityId = 80117,
commodityName = "危",
beginTime = {
seconds = 1740672000
},
endTime = v4,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710137
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[80118] = {
commodityId = 80118,
commodityName = "急了急了",
beginTime = {
seconds = 1740672000
},
endTime = v4,
shopSort = 1,
jumpId = 707,
jumpText = "星光剧场",
gender = 0,
minVersion = "1.2.80.1",
itemIds = {
710138
},
canGift = true,
addIntimacy = 28,
giftCoinType = 205,
giftPrice = 20
},
[80119] = {
commodityId = 80119,
commodityName = "小橘子比心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710139
}
},
[80120] = {
commodityId = 80120,
commodityName = "小橘子嫌弃",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710140
}
},
[80121] = {
commodityId = 80121,
commodityName = "小橘子吃糖糖",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710141
}
},
[80122] = {
commodityId = 80122,
commodityName = "小橘子点赞",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710142
}
},
[80123] = {
commodityId = 80123,
commodityName = "小橘子飞吻",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710143
}
},
[80124] = {
commodityId = 80124,
commodityName = "小橘子开心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710144
}
},
[80125] = {
commodityId = 80125,
commodityName = "小橘子生气",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710145
}
},
[80126] = {
commodityId = 80126,
commodityName = "小橘子摇头",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
itemIds = {
710146
}
},
[80127] = {
commodityId = 80127,
commodityName = "小橘子装可怜",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710147
}
},
[80128] = {
commodityId = 80128,
commodityName = "快上车",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710148
}
},
[80129] = {
commodityId = 80129,
commodityName = "干饭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710149
}
},
[80130] = {
commodityId = 80130,
commodityName = "好耶",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710150
}
},
[80131] = {
commodityId = 80131,
commodityName = "厉害了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710151
}
},
[80132] = {
commodityId = 80132,
commodityName = "晕菜",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710152
}
},
[80133] = {
commodityId = 80133,
commodityName = "轻蔑一笑",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
gender = 0,
minVersion = v12,
itemIds = {
710153
}
},
[80134] = {
commodityId = 80134,
commodityName = "鄙视你",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
gender = 0,
minVersion = v12,
itemIds = {
710154
}
},
[80135] = {
commodityId = 80135,
commodityName = "爱你哟 ",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710155
}
},
[80136] = {
commodityId = 80136,
commodityName = "没眼看了",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v7,
gender = 0,
minVersion = v12,
itemIds = {
710156
}
},
[80137] = {
commodityId = 80137,
commodityName = "顶不住了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710157
}
},
[80138] = {
commodityId = 80138,
commodityName = "记小本本",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710158
}
},
[80139] = {
commodityId = 80139,
commodityName = "禁言",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710159
}
},
[80140] = {
commodityId = 80140,
commodityName = "强势围观",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710160
}
},
[80141] = {
commodityId = 80141,
commodityName = "泪牛满面",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v7,
gender = 0,
minVersion = v12,
itemIds = {
710161
}
},
[80142] = {
commodityId = 80142,
commodityName = "爆发蓄力中",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710162
}
},
[80143] = {
commodityId = 80143,
commodityName = "好桑心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710163
}
},
[80144] = {
commodityId = 80144,
commodityName = "全都听见了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710164
}
},
[80145] = {
commodityId = 80145,
commodityName = "偷偷跟你好",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710165
}
},
[80146] = {
commodityId = 80146,
commodityName = "听你的",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710166
}
},
[80147] = {
commodityId = 80147,
commodityName = "好气啊",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710167
}
},
[80148] = {
commodityId = 80148,
commodityName = "阴阳怪气",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v7,
gender = 0,
minVersion = v12,
itemIds = {
710168
}
},
[80149] = {
commodityId = 80149,
commodityName = "目瞪狗呆",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v7,
gender = 0,
minVersion = v12,
itemIds = {
710169
}
},
[80150] = {
commodityId = 80150,
commodityName = "喵喵喵？",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v7,
gender = 0,
minVersion = v12,
itemIds = {
710170
}
},
[80151] = {
commodityId = 80151,
commodityName = "大可不必",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v7,
gender = 0,
minVersion = v12,
itemIds = {
710171
}
},
[80152] = {
commodityId = 80152,
commodityName = "偷听",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v7,
gender = 0,
minVersion = v12,
itemIds = {
710172
}
},
[80153] = {
commodityId = 80153,
commodityName = "大小姐驾到",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710173
}
},
[80154] = {
commodityId = 80154,
commodityName = "奇奇怪怪",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710174
}
},
[80155] = {
commodityId = 80155,
commodityName = "求带飞",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710175
}
},
[80156] = {
commodityId = 80156,
commodityName = "让我乔乔",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710176
}
},
[80157] = {
commodityId = 80157,
commodityName = "好厉害",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710177
}
},
[80158] = {
commodityId = 80158,
commodityName = "剪刀手",
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
jumpId = 561,
jumpText = "春水溯游",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
710178
}
},
[80159] = {
commodityId = 80159,
commodityName = "破防了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710179
}
},
[80160] = {
commodityId = 80160,
commodityName = "你过来呀！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710180
}
},
[80161] = {
commodityId = 80161,
commodityName = "求帮帮",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
jumpId = 176,
jumpText = "星梦使者",
gender = 0,
minVersion = v12,
itemIds = {
710181
}
},
[80162] = {
commodityId = 80162,
commodityName = "我酸了",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = v13,
itemIds = {
710182
}
},
[80163] = {
commodityId = 80163,
commodityName = "收到！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710183
}
},
[80164] = {
commodityId = 80164,
commodityName = "送心心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710184
}
},
[80165] = {
commodityId = 80165,
commodityName = "好期待呀",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710185
}
},
[80166] = {
commodityId = 80166,
commodityName = "吃瓜",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710186
}
},
[80167] = {
commodityId = 80167,
commodityName = "火热的心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710187
}
},
[80168] = {
commodityId = 80168,
commodityName = "思考",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710188
}
},
[80169] = {
commodityId = 80169,
commodityName = "满分",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
jumpId = 176,
jumpText = "星梦使者",
gender = 0,
minVersion = v12,
itemIds = {
710189
}
},
[80170] = {
commodityId = 80170,
commodityName = "小丸子大哭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710190
}
},
[80171] = {
commodityId = 80171,
commodityName = "小丸子开心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710191
}
},
[80172] = {
commodityId = 80172,
commodityName = "小丸子比心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710192
}
},
[80173] = {
commodityId = 80173,
commodityName = "小丸子震惊",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710193
}
},
[80174] = {
commodityId = 80174,
commodityName = "小丸子赖床",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710194
}
},
[80175] = {
commodityId = 80175,
commodityName = "小丸子偷笑",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710195
}
},
[80176] = {
commodityId = 80176,
commodityName = "花轮自信",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710196
}
},
[80177] = {
commodityId = 80177,
commodityName = "小玉感谢",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710197
}
},
[80178] = {
commodityId = 80178,
commodityName = "花轮摇头",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710198
}
},
[80179] = {
commodityId = 80179,
commodityName = "花轮送玫瑰",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710199
}
},
[80180] = {
commodityId = 80180,
commodityName = "小兰摊手",
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1718899199
},
shopSort = 1,
jumpId = 183,
jumpText = "阿童木祈愿",
gender = 0,
minVersion = v12,
itemIds = {
710200
}
},
[80181] = {
commodityId = 80181,
commodityName = "你渴望力量吗",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710201
}
},
[80182] = {
commodityId = 80182,
commodityName = "强者的肯定",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v12,
itemIds = {
710202
}
},
[80183] = {
commodityId = 80183,
commodityName = "我不酸的哦",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
jumpId = 10602,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
710208
}
},
[80184] = {
commodityId = 80184,
commodityName = "悲伤那么大",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
jumpId = 10603,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
710209
}
},
[80185] = {
commodityId = 80185,
commodityName = "汪汪问号",
beginTime = {
seconds = 1718640000
},
endTime = {
seconds = 1720367999
},
shopSort = 1,
jumpId = 604,
jumpText = "天鹅之心",
minVersion = "1.3.7.31",
itemIds = {
710203
}
},
[80186] = {
commodityId = 80186,
commodityName = "该打",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v13,
itemIds = {
710204
}
},
[80187] = {
commodityId = 80187,
commodityName = "难绷",
beginTime = v2,
endTime = v4,
minVersion = v13,
itemIds = {
710205
}
},
[80188] = {
commodityId = 80188,
commodityName = "热晕了",
beginTime = v2,
endTime = v4,
minVersion = v13,
itemIds = {
710206
}
},
[80189] = {
commodityId = 80189,
commodityName = "凉爽一下",
beginTime = v2,
endTime = v4,
minVersion = v13,
itemIds = {
710207
}
},
[80190] = {
commodityId = 80190,
commodityName = "不满",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710210
}
},
[80191] = {
commodityId = 80191,
commodityName = "方了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710211
}
},
[80192] = {
commodityId = 80192,
commodityName = "呸喽呸喽",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v7,
gender = 0,
minVersion = v13,
itemIds = {
710212
}
},
[80193] = {
commodityId = 80193,
commodityName = "神情涣散",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710213
}
},
[80194] = {
commodityId = 80194,
commodityName = "我听不见",
beginTime = {
seconds = 1744905600
},
endTime = v5,
jumpId = 1085,
jumpText = "浪漫旅程",
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
710214
}
},
[80195] = {
commodityId = 80195,
commodityName = "我真是好人",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710215
}
},
[80196] = {
commodityId = 80196,
commodityName = "佛系",
beginTime = {
seconds = 1744905600
},
endTime = v5,
jumpId = 1085,
jumpText = "浪漫旅程",
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
710216
}
},
[80197] = {
commodityId = 80197,
commodityName = "玫珊珊-太行了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710217
}
},
[80198] = {
commodityId = 80198,
commodityName = "委屈",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710218
}
},
[80199] = {
commodityId = 80199,
commodityName = "喷水",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v7,
gender = 0,
minVersion = v13,
itemIds = {
710219
}
},
[80200] = {
commodityId = 80200,
commodityName = "酸了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710220
}
},
[80201] = {
commodityId = 80201,
commodityName = "服了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710221
}
},
[80202] = {
commodityId = 80202,
commodityName = "枚珊珊-吃瓜",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710222
}
},
[80203] = {
commodityId = 80203,
commodityName = "枚珊珊-强行卖萌",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710223
}
},
[80204] = {
commodityId = 80204,
commodityName = "好气鸭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710224
}
},
[80205] = {
commodityId = 80205,
commodityName = "叛逆",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710225
}
},
[80206] = {
commodityId = 80206,
commodityName = "拽",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v7,
gender = 0,
minVersion = v13,
itemIds = {
710226
}
},
[80207] = {
commodityId = 80207,
commodityName = "萌光焕发",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710227
}
},
[80208] = {
commodityId = 80208,
commodityName = "害怕",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710228
}
},
[80209] = {
commodityId = 80209,
commodityName = "监视",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710229
}
},
[80210] = {
commodityId = 80210,
commodityName = "打盹",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710230
}
},
[80211] = {
commodityId = 80211,
commodityName = "Toby超可爱",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710231
}
},
[80212] = {
commodityId = 80212,
commodityName = "Toby气冲冲",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710232
}
},
[80213] = {
commodityId = 80213,
commodityName = "Toby的肯定",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710233
}
},
[80214] = {
commodityId = 80214,
commodityName = "发现",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710235
}
},
[80215] = {
commodityId = 80215,
commodityName = "撒娇",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 2,
jumpId = 11,
jumpText = v7,
gender = 0,
minVersion = v13,
itemIds = {
710236
}
},
[80216] = {
commodityId = 80216,
commodityName = "潜水",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 2,
jumpId = 11,
jumpText = v7,
gender = 0,
minVersion = v13,
itemIds = {
710237
}
},
[80217] = {
commodityId = 80217,
commodityName = "划水",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 2,
jumpId = 11,
jumpText = v7,
gender = 0,
minVersion = v13,
itemIds = {
710238
}
},
[80218] = {
commodityId = 80218,
commodityName = "好热鸭",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 2,
jumpId = 11,
jumpText = v7,
gender = 0,
minVersion = v13,
itemIds = {
710239
}
},
[80219] = {
commodityId = 80219,
commodityName = "动动脑子",
beginTime = {
seconds = 1744905600
},
endTime = v5,
jumpId = 1085,
jumpText = "浪漫旅程",
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
710240
}
},
[80220] = {
commodityId = 80220,
commodityName = "再说一遍",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710241
}
},
[80221] = {
commodityId = 80221,
commodityName = "捏捏",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710242
}
},
[80222] = {
commodityId = 80222,
commodityName = "婉拒了哈",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710243
}
},
[80223] = {
commodityId = 80223,
commodityName = "睡觉",
beginTime = {
seconds = 1719244800
},
endTime = {
seconds = 1720454399
},
jumpId = 25,
jumpText = "特惠礼包",
gender = 0,
minVersion = "1.3.7.1",
itemIds = {
710250
}
},
[80224] = {
commodityId = 80224,
commodityName = "缤纷气球",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v7,
gender = 0,
minVersion = v14,
itemIds = {
710251
}
},
[80225] = {
commodityId = 80225,
commodityName = "送花花",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
jumpId = 311,
jumpText = "守护圣翼",
gender = 0,
minVersion = v13,
itemIds = {
710252
}
},
[80226] = {
commodityId = 80226,
commodityName = "自拍",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
gender = 0,
minVersion = v14,
itemIds = {
710253
}
},
[80227] = {
commodityId = 80227,
commodityName = "旋转木马",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v7,
gender = 0,
minVersion = v14,
itemIds = {
710254
}
},
[80228] = {
commodityId = 80228,
commodityName = "纸醉金迷",
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1727366399
},
shopSort = 1,
jumpId = 364,
jumpText = "山河卷扇",
gender = 0,
minVersion = "1.3.18.37",
itemIds = {
710255
}
},
[80229] = {
commodityId = 80229,
commodityName = "我要奋斗",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 2,
jumpId = 1085,
jumpText = "浪漫旅程",
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
710256
}
},
[80230] = {
commodityId = 80230,
commodityName = "冲呀",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v7,
gender = 0,
minVersion = v14,
itemIds = {
710257
}
},
[80231] = {
commodityId = 80231,
commodityName = "吃爆米花",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v7,
gender = 0,
minVersion = v14,
itemIds = {
710258
}
},
[80232] = {
commodityId = 80232,
commodityName = "达咩",
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1727366399
},
shopSort = 1,
jumpId = 364,
jumpText = "山河卷扇",
gender = 0,
minVersion = "1.3.18.37",
itemIds = {
710259
}
},
[80233] = {
commodityId = 80233,
commodityName = "举栗",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710260
}
},
[80234] = {
commodityId = 80234,
commodityName = "拭目以待",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
gender = 0,
minVersion = v14,
itemIds = {
710261
}
},
[80235] = {
commodityId = 80235,
commodityName = "小红狐吃瓜",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 2,
jumpId = 1085,
jumpText = "浪漫旅程",
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
710262
}
},
[80236] = {
commodityId = 80236,
commodityName = "展示魅力",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v7,
gender = 0,
minVersion = v14,
itemIds = {
710263
}
},
[80237] = {
commodityId = 80237,
commodityName = "面部保养",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 2,
jumpId = 1085,
jumpText = "浪漫旅程",
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
710264
}
},
[80238] = {
commodityId = 80238,
commodityName = "好期待",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710265
}
},
[80239] = {
commodityId = 80239,
commodityName = "心情真好",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710266
}
},
[80240] = {
commodityId = 80240,
commodityName = "好害羞",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710267
}
},
[80241] = {
commodityId = 80241,
commodityName = "满满爱心",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710268
}
},
[80242] = {
commodityId = 80242,
commodityName = "睡着",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v7,
gender = 0,
minVersion = v14,
itemIds = {
710269
}
},
[80243] = {
commodityId = 80243,
commodityName = "打哈欠",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v7,
gender = 0,
minVersion = v14,
itemIds = {
710270
}
},
[80244] = {
commodityId = 80244,
commodityName = "开心吃蛋糕",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710271
}
},
[80245] = {
commodityId = 80245,
commodityName = "过奖过奖",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710272
}
},
[80246] = {
commodityId = 80246,
commodityName = "骚扰",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710273
}
},
[80247] = {
commodityId = 80247,
commodityName = "碰头",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
710274
}
},
[80248] = {
commodityId = 80248,
commodityName = "报告",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710275
}
},
[80249] = {
commodityId = 80249,
commodityName = "热化了",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
710276
}
},
[80250] = {
commodityId = 80250,
commodityName = "打电话",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
710277
}
},
[80251] = {
commodityId = 80251,
commodityName = "鉴赏家的认可",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v13,
itemIds = {
710278
}
},
[80252] = {
commodityId = 80252,
commodityName = "已阵亡",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
710279
}
},
[80253] = {
commodityId = 80253,
commodityName = "摸鱼",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
710280
}
},
[80254] = {
commodityId = 80254,
commodityName = "害羞",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
710281
}
},
[80255] = {
commodityId = 80255,
commodityName = "oh yeah",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
710282
}
},
[80256] = {
commodityId = 80256,
commodityName = "呐喊",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
710283
}
},
[80257] = {
commodityId = 80257,
commodityName = "别跑",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711001
}
},
[80258] = {
commodityId = 80258,
commodityName = "冲锋！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711002
}
},
[80259] = {
commodityId = 80259,
commodityName = "生气！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711003
}
},
[80260] = {
commodityId = 80260,
commodityName = "帅！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711004
}
},
[80261] = {
commodityId = 80261,
commodityName = "一箭带走！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711005
}
},
[80262] = {
commodityId = 80262,
commodityName = "赞",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711006
}
},
[80263] = {
commodityId = 80263,
commodityName = "都不是对手",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711007
}
},
[80264] = {
commodityId = 80264,
commodityName = "你过来啊",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711008
}
},
[80265] = {
commodityId = 80265,
commodityName = "你什么实力",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711009
}
},
[80266] = {
commodityId = 80266,
commodityName = "让你一把",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711010
}
},
[80267] = {
commodityId = 80267,
commodityName = "人呢",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711011
}
},
[80268] = {
commodityId = 80268,
commodityName = "一起瑶摆",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711012
}
},
[80269] = {
commodityId = 80269,
commodityName = "皇上举镜子",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711013
}
},
[80270] = {
commodityId = 80270,
commodityName = "嬛嬛许愿",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711014
}
},
[80271] = {
commodityId = 80271,
commodityName = "熹贵妃禁言",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711015
}
},
[80272] = {
commodityId = 80272,
commodityName = "摸摸头",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711016
}
},
[80273] = {
commodityId = 80273,
commodityName = "别打了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711017
}
},
[80274] = {
commodityId = 80274,
commodityName = "一肚子坏水",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711018
}
},
[80275] = {
commodityId = 80275,
commodityName = "我会出手",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711019
}
},
[80276] = {
commodityId = 80276,
commodityName = "冲鸭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711020
}
},
[80277] = {
commodityId = 80277,
commodityName = "不要过来！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711021
}
},
[80278] = {
commodityId = 80278,
commodityName = "告辞",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711022
}
},
[80279] = {
commodityId = 80279,
commodityName = "超凶",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711023
}
},
[80280] = {
commodityId = 80280,
commodityName = "顶呱呱",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711024
}
},
[80281] = {
commodityId = 80281,
commodityName = "想不到吧！",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711025
}
},
[80282] = {
commodityId = 80282,
commodityName = "功德+1",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711026
}
},
[80283] = {
commodityId = 80283,
commodityName = "不好意思鸭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711027
}
},
[80284] = {
commodityId = 80284,
commodityName = "就吃一口",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711028
}
},
[80285] = {
commodityId = 80285,
commodityName = "华妃起范儿",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711029
}
},
[80286] = {
commodityId = 80286,
commodityName = "泡澡",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711030
}
},
[80287] = {
commodityId = 80287,
commodityName = "心如止水",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v14,
itemIds = {
711031
}
},
[80288] = {
commodityId = 80288,
commodityName = "龙魂再起",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 2,
jumpId = 10674,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711062
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80289] = {
commodityId = 80289,
commodityName = "古灵精怪",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 2,
jumpId = 10674,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711061
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80290] = {
commodityId = 80290,
commodityName = "自信满满",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopSort = 1,
jumpId = 618,
jumpText = "百变小新",
gender = 0,
minVersion = "1.3.12.139",
itemIds = {
711075
}
},
[80291] = {
commodityId = 80291,
commodityName = "头好烫",
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
shopSort = 1,
jumpId = 613,
jumpText = "百变小新",
gender = 0,
minVersion = "1.3.12.139",
itemIds = {
711076
}
},
[80292] = {
commodityId = 80292,
commodityName = "摘下花瓣",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v7,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711064
}
},
[80293] = {
commodityId = 80293,
commodityName = "探头偷看",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v7,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711065
}
},
[80294] = {
commodityId = 80294,
commodityName = "黑暗料理",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v7,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711066
}
},
[80295] = {
commodityId = 80295,
commodityName = "让我看看",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v7,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711067
}
},
[80296] = {
commodityId = 80296,
commodityName = "举牌666",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v7,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711068
}
},
[80297] = {
commodityId = 80297,
commodityName = "我+1",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 619,
jumpText = v7,
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711069
}
},
[80298] = {
commodityId = 80298,
commodityName = "浇水",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711071
}
},
[80299] = {
commodityId = 80299,
commodityName = "无语",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.18.1",
itemIds = {
711072
}
},
[80300] = {
commodityId = 80300,
commodityName = "开心猪猪",
beginTime = {
seconds = 1748016000
},
endTime = {
seconds = 1749743999
},
jumpId = 1094,
jumpText = "夏日花园",
gender = 0,
minVersion = "1.3.88.53",
itemIds = {
711052
}
},
[80301] = {
commodityId = 80301,
commodityName = "叹气猪猪",
beginTime = {
seconds = 1726761600
},
endTime = {
seconds = 1728575999
},
jumpId = 25,
jumpText = "特惠礼包",
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711053
}
},
[80302] = {
commodityId = 80302,
commodityName = "嗨起来",
beginTime = {
seconds = 1726761600
},
endTime = {
seconds = 1728575999
},
jumpId = 25,
jumpText = "特惠礼包",
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711054
}
},
[80303] = {
commodityId = 80303,
commodityName = "充电休息中",
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711055
}
},
[80304] = {
commodityId = 80304,
commodityName = "努力奋斗",
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711056
}
},
[80305] = {
commodityId = 80305,
commodityName = "心满意足",
beginTime = {
seconds = 1726761600
},
endTime = {
seconds = 1728575999
},
jumpId = 25,
jumpText = "特惠礼包",
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711057
}
},
[80306] = {
commodityId = 80306,
commodityName = "程序启动",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 2,
jumpId = 10674,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711132
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80307] = {
commodityId = 80307,
commodityName = "魅力偶像",
beginTime = {
seconds = 1726848000
},
endTime = {
seconds = 1728143999
},
shopSort = 2,
jumpId = 10674,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.56",
itemIds = {
711137
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80308] = {
commodityId = 80308,
commodityName = "心有不甘",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711045
}
},
[80309] = {
commodityId = 80309,
commodityName = "欢呼",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711046
}
},
[80310] = {
commodityId = 80310,
commodityName = "一脸崇拜",
beginTime = {
seconds = 1744905600
},
endTime = v5,
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711047
}
},
[80311] = {
commodityId = 80311,
commodityName = "轻松解决",
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1728143999
},
shopSort = 1,
jumpId = 10674,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.12.118",
itemIds = {
711074
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80312] = {
commodityId = 80312,
commodityName = "尴尬",
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1728143999
},
shopSort = 1,
jumpId = 10674,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.12.118",
itemIds = {
711134
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80313] = {
commodityId = 80313,
commodityName = "龙魂再起",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711062
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80314] = {
commodityId = 80314,
commodityName = "古灵精怪",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711061
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80315] = {
commodityId = 80315,
commodityName = "程序启动",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711132
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80316] = {
commodityId = 80316,
commodityName = "魅力偶像",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711137
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80317] = {
commodityId = 80317,
commodityName = "轻松解决",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711074
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80318] = {
commodityId = 80318,
commodityName = "为爱出发",
beginTime = {
seconds = 1728144000
},
endTime = {
seconds = 1730390399
},
shopSort = 1,
jumpId = 10677,
jumpText = "峡谷幻梦",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711138
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80319] = {
commodityId = 80319,
commodityName = "我要想想",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
jumpId = 10601,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
711136
}
},
[80320] = {
commodityId = 80320,
commodityName = "羞羞",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
jumpId = 10600,
jumpText = "小甜豆",
gender = 0,
minVersion = "1.3.18.109",
itemIds = {
711135
}
},
[80321] = {
commodityId = 80321,
commodityName = "正义制裁",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711139
}
},
[80322] = {
commodityId = 80322,
commodityName = "不耐烦",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711140
}
},
[80323] = {
commodityId = 80323,
commodityName = "吃瓜旁观",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711141
}
},
[80324] = {
commodityId = 80324,
commodityName = "转运魔法",
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
jumpId = 8011,
jumpText = "星缘奇境",
gender = 0,
minVersion = "1.3.26.93",
itemIds = {
711142
}
},
[80325] = {
commodityId = 80325,
commodityName = "闪亮登场",
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1734278399
},
jumpId = 8012,
jumpText = "星缘奇境",
gender = 0,
minVersion = "1.3.26.93",
itemIds = {
711143
}
},
[80326] = {
commodityId = 80326,
commodityName = "帅气打碟",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711144
}
},
[80327] = {
commodityId = 80327,
commodityName = "一展歌喉",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711145
}
},
[80328] = {
commodityId = 80328,
commodityName = "比个耶",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711146
}
},
[80329] = {
commodityId = 80329,
commodityName = "怎么敢的",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711147
}
},
[80330] = {
commodityId = 80330,
commodityName = "扭捏",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711148
}
},
[80331] = {
commodityId = 80331,
commodityName = "玩耍鸭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711149
}
},
[80332] = {
commodityId = 80332,
commodityName = "打不着",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711150
}
},
[80333] = {
commodityId = 80333,
commodityName = "精彩鸭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711151
}
},
[80334] = {
commodityId = 80334,
commodityName = "强颜欢笑",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711152
}
},
[80335] = {
commodityId = 80335,
commodityName = "叉出去",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711153
}
},
[80336] = {
commodityId = 80336,
commodityName = "欲拒还迎",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711154
}
},
[80337] = {
commodityId = 80337,
commodityName = "已老实",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711155
}
},
[80338] = {
commodityId = 80338,
commodityName = "来玩水啊",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711156
}
},
[80339] = {
commodityId = 80339,
commodityName = "out了",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711157
}
},
[80340] = {
commodityId = 80340,
commodityName = "出来玩",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711158
}
},
[80341] = {
commodityId = 80341,
commodityName = "戴花",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711159
}
},
[80342] = {
commodityId = 80342,
commodityName = "戴墨镜",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711160
}
},
[80343] = {
commodityId = 80343,
commodityName = "盖饭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711161
}
},
[80344] = {
commodityId = 80344,
commodityName = "回收饭",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711162
}
},
[80345] = {
commodityId = 80345,
commodityName = "流星划过",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v7,
gender = 0,
minVersion = v15,
itemIds = {
711163
}
},
[80346] = {
commodityId = 80346,
commodityName = "期待",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711164
}
},
[80347] = {
commodityId = 80347,
commodityName = "失眠",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711165
}
},
[80348] = {
commodityId = 80348,
commodityName = "星球接触",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v7,
gender = 0,
minVersion = v15,
itemIds = {
711166
}
},
[80349] = {
commodityId = 80349,
commodityName = "占星",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v7,
gender = 0,
minVersion = v15,
itemIds = {
711167
}
},
[80350] = {
commodityId = 80350,
commodityName = "爆气",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711168
}
},
[80351] = {
commodityId = 80351,
commodityName = "出拳",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711169
}
},
[80352] = {
commodityId = 80352,
commodityName = "飞碟来了",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v7,
gender = 0,
minVersion = v15,
itemIds = {
711170
}
},
[80353] = {
commodityId = 80353,
commodityName = "害羞",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711171
}
},
[80354] = {
commodityId = 80354,
commodityName = "激动",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711172
}
},
[80355] = {
commodityId = 80355,
commodityName = "惊讶",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711173
}
},
[80356] = {
commodityId = 80356,
commodityName = "看望远镜",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = v15,
itemIds = {
711174
}
},
[80357] = {
commodityId = 80357,
commodityName = "猛喝果汁",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711175
}
},
[80358] = {
commodityId = 80358,
commodityName = "明白",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711176
}
},
[80359] = {
commodityId = 80359,
commodityName = "判决",
coinType = 6,
price = 180,
beginTime = v2,
endTime = v4,
gender = 0,
minVersion = v15,
itemIds = {
711177
}
},
[80360] = {
commodityId = 80360,
commodityName = "坐火箭",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = v15,
itemIds = {
711178
}
},
[80361] = {
commodityId = 80361,
commodityName = "摘星星",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v7,
gender = 0,
minVersion = v15,
itemIds = {
711179
}
},
[80362] = {
commodityId = 80362,
commodityName = "月球插旗",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v7,
gender = 0,
minVersion = v15,
itemIds = {
711180
}
},
[80363] = {
commodityId = 80363,
commodityName = "抱月亮",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v7,
gender = 0,
minVersion = v15,
itemIds = {
711181
}
},
[80364] = {
commodityId = 80364,
commodityName = "正义制裁",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711139
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80365] = {
commodityId = 80365,
commodityName = "不耐烦",
beginTime = {
seconds = 1730563200
},
endTime = {
seconds = 1732377599
},
shopSort = 1,
jumpId = 10698,
jumpText = "时空碎影",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
711140
},
canGift = true,
addIntimacy = 28,
giftCoinType = 218,
giftPrice = 30
},
[80366] = {
commodityId = 80366,
commodityName = [[
哇，太棒啦！]],
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
711233
}
},
[80367] = {
commodityId = 80367,
commodityName = [[
好美啊！]],
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
711234
}
},
[80368] = {
commodityId = 80368,
commodityName = "环顾四周",
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
jumpId = 1064,
jumpText = "限时礼包",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
711235
}
},
[80369] = {
commodityId = 80369,
commodityName = "沉浸书香",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
jumpId = 10699,
jumpText = "扬帆起航",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
711244
}
},
[80370] = {
commodityId = 80370,
commodityName = "浪尖冲刺",
beginTime = {
seconds = 1732550400
},
endTime = {
seconds = 1735833599
},
jumpId = 10700,
jumpText = "扬帆起航",
gender = 0,
minVersion = "1.3.26.61",
itemIds = {
711245
}
},
[80371] = {
commodityId = 80371,
commodityName = "天籁之音",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v7,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711261
}
},
[80372] = {
commodityId = 80372,
commodityName = "秘密接收中",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v7,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711262
}
},
[80373] = {
commodityId = 80373,
commodityName = "休息一下",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711263
}
},
[80374] = {
commodityId = 80374,
commodityName = "来打我呀",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v7,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711264
}
},
[80375] = {
commodityId = 80375,
commodityName = "龙龙敷衍",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v7,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711265
}
},
[80376] = {
commodityId = 80376,
commodityName = "困出重影",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v7,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711266
}
},
[80377] = {
commodityId = 80377,
commodityName = "哈？",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v7,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711267
}
},
[80378] = {
commodityId = 80378,
commodityName = "自信微笑",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v7,
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711268
}
},
[80379] = {
commodityId = 80379,
commodityName = "郁郁时间到",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
gender = 0,
minVersion = "1.3.37.1",
itemIds = {
711269
}
},
[80380] = {
commodityId = 80380,
commodityName = "柯南的指证",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 1068,
jumpText = "柯南联动时装",
gender = 0,
minVersion = "1.3.37.37",
itemIds = {
711281
}
},
[80381] = {
commodityId = 80381,
commodityName = "摩拳擦掌的小兰",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 1068,
jumpText = "柯南联动时装",
gender = 0,
minVersion = "1.3.37.37",
itemIds = {
711276
}
},
[80382] = {
commodityId = 80382,
commodityName = "犯困的灰原",
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 1068,
jumpText = "柯南联动时装",
gender = 0,
minVersion = "1.3.37.37",
itemIds = {
711277
}
},
[80383] = {
commodityId = 80383,
commodityName = "憨厚的坏笑",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v7,
minVersion = "1.3.68.1",
itemIds = {
711323
}
},
[80384] = {
commodityId = 80384,
commodityName = "吨吨吨",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v7,
minVersion = "1.3.68.1",
itemIds = {
711325
}
},
[80385] = {
commodityId = 80385,
commodityName = "抓拍",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v7,
minVersion = "1.3.68.1",
itemIds = {
711329
}
},
[80386] = {
commodityId = 80386,
commodityName = "让我吃吃",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v7,
minVersion = "1.3.68.1",
itemIds = {
711326
}
},
[80387] = {
commodityId = 80387,
commodityName = "猫猫呆住",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v7,
minVersion = "1.3.68.1",
itemIds = {
711327
}
},
[80388] = {
commodityId = 80388,
commodityName = "大帅牛",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v7,
minVersion = "1.3.68.1",
itemIds = {
711328
}
},
[80389] = {
commodityId = 80389,
commodityName = "占位",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v7,
minVersion = "1.3.68.1",
itemIds = {
711333
}
},
[80390] = {
commodityId = 80390,
commodityName = "闪亮登场",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.68.1",
itemIds = {
711324
}
},
[80391] = {
commodityId = 80391,
commodityName = "快乐转圈圈",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = "1.3.68.1",
itemIds = {
711330
}
},
[80392] = {
commodityId = 80392,
commodityName = "泡澡时光",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 640,
jumpText = v7,
itemIds = {
711030
}
},
[80393] = {
commodityId = 80393,
commodityName = "举栗",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 640,
jumpText = v7,
itemIds = {
710260
}
},
[80394] = {
commodityId = 80394,
commodityName = "嘴馋了",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 640,
jumpText = v7,
itemIds = {
711040
}
},
[80395] = {
commodityId = 80395,
commodityName = "比个心",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 640,
jumpText = v7,
itemIds = {
711247
}
},
[80396] = {
commodityId = 80396,
commodityName = "哎鸭鸭",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 640,
jumpText = v7,
itemIds = {
711248
}
},
[80397] = {
commodityId = 80397,
commodityName = "拜托了",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 640,
jumpText = v7,
itemIds = {
711249
}
},
[80398] = {
commodityId = 80398,
commodityName = "爬墙偷瞄",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 640,
jumpText = v7,
itemIds = {
711250
}
},
[80399] = {
commodityId = 80399,
commodityName = "心如止水",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
itemIds = {
711031
}
},
[80400] = {
commodityId = 80400,
commodityName = "睡眠丢失",
beginTime = {
seconds = 1741881600
},
endTime = v5,
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
itemIds = {
711251
}
},
[80401] = {
commodityId = 80401,
commodityName = "小鸡弹吉他",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopSort = 1,
jumpId = 10598,
jumpText = "小甜豆",
minVersion = "1.3.78.73",
itemIds = {
711381
}
},
[80402] = {
commodityId = 80402,
commodityName = "菠萝充电中",
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopSort = 1,
jumpId = 10599,
jumpText = "小甜豆",
minVersion = "1.3.78.73",
itemIds = {
711380
}
},
[80403] = {
commodityId = 80403,
commodityName = "星星眼发射中",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711399
}
},
[80404] = {
commodityId = 80404,
commodityName = "乖巧歪头",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711400
}
},
[80405] = {
commodityId = 80405,
commodityName = "美女震惊",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711401
}
},
[80406] = {
commodityId = 80406,
commodityName = "您先请",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711404
}
},
[80407] = {
commodityId = 80407,
commodityName = "瑟瑟发抖",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711405
}
},
[80408] = {
commodityId = 80408,
commodityName = "信心爆棚",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711406
}
},
[80409] = {
commodityId = 80409,
commodityName = "高调登场",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711411
}
},
[80410] = {
commodityId = 80410,
commodityName = "暴风哭泣",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711402
}
},
[80411] = {
commodityId = 80411,
commodityName = "双倍心动",
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
shopSort = 1,
minVersion = "1.3.88.1",
itemIds = {
711403
}
},
[80412] = {
commodityId = 80412,
commodityName = "大脑过载",
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750607999
},
shopSort = 1,
jumpId = 682,
jumpText = "幸运MVP",
minVersion = "1.3.88.50",
itemIds = {
711531
}
},
[80413] = {
commodityId = 80413,
commodityName = "阿卓自拍中",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopSort = 1,
jumpId = 1096,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
711397
}
},
[80414] = {
commodityId = 80414,
commodityName = "阿卓放纵餐",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopSort = 1,
jumpId = 1097,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
711398
}
},
[80415] = {
commodityId = 80415,
commodityName = "下大雨啦",
beginTime = {
seconds = 1747929600
},
endTime = {
seconds = 1750607999
},
shopSort = 1,
jumpId = 682,
jumpText = "幸运MVP",
minVersion = "1.3.88.50",
itemIds = {
711540
}
},
[80416] = {
commodityId = 80416,
commodityName = "不要啊",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopSort = 1,
jumpId = 1096,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
711542
}
},
[80417] = {
commodityId = 80417,
commodityName = "呀哈",
beginTime = {
seconds = 1748620800
},
endTime = {
seconds = 1750435199
},
shopSort = 1,
jumpId = 1097,
jumpText = "卓大王",
minVersion = "1.3.88.116",
itemIds = {
711544
}
}
}

local mt = {
mallId = 13,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
shopTag = {
3,
15
},
itemNums = {
1
},
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data