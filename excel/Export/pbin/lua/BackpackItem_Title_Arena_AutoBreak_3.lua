--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_称号_Arena.xlsx: 称号

local data = {
[880136] = {
id = 880136,
quality = 3,
name = "最强艾琳",
desc = "登上峡谷3v3市级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_1",
picture = "CDN:T_Arena_Designation_Img_1"
},
[880137] = {
id = 880137,
quality = 2,
name = "最强艾琳",
desc = "登上峡谷3v3省级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_2",
picture = "CDN:T_Arena_Designation_Img_2"
},
[880138] = {
id = 880138,
quality = 1,
name = "最强艾琳",
desc = "登上峡谷3v3全服战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_3",
picture = "CDN:T_Arena_Designation_Img_3"
},
[880139] = {
id = 880139,
quality = 3,
name = "最强绮",
desc = "登上峡谷3v3市级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_1",
picture = "CDN:T_Arena_Designation_Img_1"
},
[880140] = {
id = 880140,
quality = 2,
name = "最强绮",
desc = "登上峡谷3v3省级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_2",
picture = "CDN:T_Arena_Designation_Img_2"
},
[880141] = {
id = 880141,
quality = 1,
name = "最强绮",
desc = "登上峡谷3v3全服战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_3",
picture = "CDN:T_Arena_Designation_Img_3"
},
[900136] = {
id = 900136,
quality = 3,
name = "最强艾琳",
desc = "登上峡谷5v5市级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_4",
picture = "CDN:T_Arena_Designation_Img_4"
},
[900137] = {
id = 900137,
quality = 2,
name = "最强艾琳",
desc = "登上峡谷5v5省级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_5",
picture = "CDN:T_Arena_Designation_Img_5"
},
[900138] = {
id = 900138,
quality = 1,
name = "最强艾琳",
desc = "登上峡谷5v5全服战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_6",
picture = "CDN:T_Arena_Designation_Img_6"
},
[900139] = {
id = 900139,
quality = 3,
name = "最强绮",
desc = "登上峡谷5v5市级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_4",
picture = "CDN:T_Arena_Designation_Img_4"
},
[900140] = {
id = 900140,
quality = 2,
name = "最强绮",
desc = "登上峡谷5v5省级战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_5",
picture = "CDN:T_Arena_Designation_Img_5"
},
[900141] = {
id = 900141,
quality = 1,
name = "最强绮",
desc = "登上峡谷5v5全服战力排行榜前100获得",
icon = "CDN:T_Arena_Designation_Icon_6",
picture = "CDN:T_Arena_Designation_Img_6"
},
[905031] = {
id = 905031,
quality = 2,
name = "乱世狂戟",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905032] = {
id = 905032,
quality = 2,
name = "逐风之羽",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905033] = {
id = 905033,
quality = 2,
name = "万能技术宅",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905034] = {
id = 905034,
quality = 2,
name = "云想衣裳",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905035] = {
id = 905035,
quality = 2,
name = "以德服人",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905036] = {
id = 905036,
quality = 2,
name = "冰封之心",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905037] = {
id = 905037,
quality = 2,
name = "破魔神箭",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905038] = {
id = 905038,
quality = 2,
name = "长明守护",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905039] = {
id = 905039,
quality = 2,
name = "卧龙觉醒",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905040] = {
id = 905040,
quality = 2,
name = "爱心勇士",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905041] = {
id = 905041,
quality = 2,
name = "天才破坏王",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905042] = {
id = 905042,
quality = 2,
name = "镜花水月",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905043] = {
id = 905043,
quality = 2,
name = "一拳开天",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
},
[905044] = {
id = 905044,
quality = 2,
name = "一骑当千",
desc = "完成英雄历练任务获得",
icon = "CDN:CT_Arena_Designation_Icon_7",
picture = "CDN:CT_Arena_Designation_Img_7"
}
}

local mt = {
type = "ItemType_Title",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 4,
itemNum = 10
}
},
showInView = 0,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data