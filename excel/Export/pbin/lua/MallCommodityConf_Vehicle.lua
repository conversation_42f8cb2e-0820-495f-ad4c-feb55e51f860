--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-载具

local data = {
[95500] = {
mallId = 39,
commodityId = 95500,
commodityName = "森之灵·鲲宝",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1740671999
},
shopTag = {
3,
24
},
jumpId = 561,
jumpText = "春水溯游",
gender = 0,
minVersion = "1.3.68.52",
itemIds = {
730001
},
itemNums = {
1
},
suitId = 20001
},
[95501] = {
mallId = 39,
commodityId = 95501,
commodityName = "森之灵·鲲宝",
coinType = 200005,
price = 100,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1683043200
},
gender = 0,
minVersion = "1.2.100.1",
itemIds = {
730002
},
itemNums = {
1
}
},
[95502] = {
mallId = 39,
commodityId = 95502,
commodityName = "森之灵·鲲宝",
coinType = 200005,
price = 100,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1683043200
},
gender = 0,
minVersion = "1.2.100.1",
itemIds = {
730003
},
itemNums = {
1
}
},
[95503] = {
mallId = 39,
commodityId = 95503,
commodityName = "绮梦幻影",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = {
3,
24
},
jumpId = 1085,
jumpText = "浪漫旅程",
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
730004
},
itemNums = {
1
},
bOpenSuit = true,
suitId = 20002
},
[95504] = {
mallId = 39,
commodityId = 95504,
commodityName = "绮梦幻影",
coinType = 200005,
price = 100,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1683043200
},
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
730005
},
itemNums = {
1
}
},
[95505] = {
mallId = 39,
commodityId = 95505,
commodityName = "绮梦幻影",
coinType = 200005,
price = 100,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1683043200
},
gender = 0,
minVersion = "1.3.7.94",
itemIds = {
730006
},
itemNums = {
1
}
},
[95506] = {
mallId = 39,
commodityId = 95506,
commodityName = "星之子·龙霄",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopTag = {
3,
24
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
730007
},
itemNums = {
1
},
canGift = true,
addIntimacy = 625,
giftCoinType = 1,
giftPrice = 15000,
bOpenSuit = true,
suitId = 20003
},
[95507] = {
mallId = 39,
commodityId = 95507,
commodityName = "星之子·龙霄",
coinType = 200005,
price = 100,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1683043200
},
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
730008
},
itemNums = {
1
}
},
[95508] = {
mallId = 39,
commodityId = 95508,
commodityName = "星之子·龙霄",
coinType = 200005,
price = 100,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1683043200
},
gender = 0,
minVersion = "1.3.18.71",
itemIds = {
730009
},
itemNums = {
1
}
},
[95509] = {
mallId = 39,
commodityId = 95509,
commodityName = "星辉幼角",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = {
3,
24
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
gender = 0,
minVersion = "1.3.37.87",
itemIds = {
730010
},
itemNums = {
1
},
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
bOpenSuit = true,
suitId = 20004
},
[95510] = {
mallId = 39,
commodityId = 95510,
commodityName = "星澜灵角",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = {
3,
24
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
gender = 0,
minVersion = "1.3.37.87",
itemIds = {
730011
},
itemNums = {
1
},
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
bOpenSuit = true,
suitId = 20005
},
[95511] = {
mallId = 39,
commodityId = 95511,
commodityName = "星澜灵角",
coinType = 200005,
price = 100,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1735660800
},
gender = 0,
itemIds = {
730012
},
itemNums = {
1
}
},
[95512] = {
mallId = 39,
commodityId = 95512,
commodityName = "璀璨星翼",
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1750953599
},
shopTag = {
3,
24
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
gender = 0,
minVersion = "1.3.37.87",
itemIds = {
730013
},
itemNums = {
1
},
showCondition = {
condition = {
{
conditionType = 322,
value = 80001
}
}
},
bOpenSuit = true,
suitId = 20006
},
[95513] = {
mallId = 39,
commodityId = 95513,
commodityName = "璀璨星翼",
coinType = 200005,
price = 200,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1735660800
},
gender = 0,
itemIds = {
730014
},
itemNums = {
1
}
},
[95514] = {
mallId = 39,
commodityId = 95514,
commodityName = "璀璨星翼",
coinType = 200005,
price = 200,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 1735660800
},
gender = 0,
itemIds = {
730015
},
itemNums = {
1
}
}
}

local mt = {
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data