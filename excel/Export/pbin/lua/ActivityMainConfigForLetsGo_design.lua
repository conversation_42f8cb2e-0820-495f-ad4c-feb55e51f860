--com.tencent.wea.xlsRes.table_ActivityMainConfig => excel/xls/H_活动中心配置_策划专用.xlsx: 活动配置

local v0 = 4

local v1 = 0

local v2 = 1

local data = {
[666] = {
id = 666,
activityType = "ATKungFuPanda",
timeInfo = {
beginTime = {
seconds = 1710000000
},
endTime = {
seconds = 1712419199
},
showEndTime = {
seconds = 1712505599
},
showBeginTime = {
seconds = 1710000000
}
},
labelId = 99,
activityName = "功夫熊猫广场活动",
activityParam = {
10,
20,
2002,
2,
10006,
20,
10
},
activityUIDetail = "UI_KongFuPanda_MainView",
activityTaskGroup = {
56095,
56096,
56097
},
showInCenter = true,
slapFace = true,
activityNameType = "ANKungFuPanda",
titleType = 0
},
[5001] = {
id = 5001,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1714060799
},
showEndTime = {
seconds = 1714060799
},
showBeginTime = {
seconds = 1711036800
}
},
labelId = 10,
backgroundUrl = {
"T_Lmg_RegularExchange_Bag_Z_02_N_A.astc"
},
activityName = "限时排位",
activityParam = {
23,
3200
},
activityDesc = "参与各种玩法的排位赛任务赢取兑换币，兑换心仪的奖励！",
activityUIDetail = "UI_Activity_Exchange_NormalView",
isInBottom = 1,
activityTaskGroup = {
40001,
40002,
40003,
40004,
40005,
40006,
40007,
41001,
41011,
41021,
41031,
41041,
41051,
41061
},
showInCenter = true,
activityNameType = "ANTNormalExchange",
activityShopType = {
23
},
clientParams = {
"1"
},
titleType = 1,
activityBeginCleanCoin = {
3200
}
},
[5002] = {
id = 5002,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1710000000
},
endTime = {
seconds = 1714492799
},
showEndTime = {
seconds = 1714492799
},
showBeginTime = {
seconds = 1710000000
}
},
labelId = 1,
activityName = "一起去春游",
activityParam = {
2031
},
activityDesc = "叫上星搭子，赴一场春天的约会",
activityUIDetail = "UI_PlayerGroupsActivity",
tagId = 9,
activityTaskGroup = {
40011,
40010
},
showInCenter = true,
activityNameType = "ANTItTakesFour",
activityShopType = {
102
},
titleType = 1,
activityBeginCleanCoin = {
2031
}
},
[5003] = {
id = 5003,
activityType = "ATRelatedDisplay",
timeInfo = {
beginTime = {
seconds = 1710000000
},
endTime = {
seconds = 1714492799
},
showEndTime = {
seconds = 1714492799
},
showBeginTime = {
seconds = 1710000000
}
},
labelId = 2,
activityName = "春日留影",
activityParam = {
5002,
420002
},
activityUIDetail = "UI_Activity_TeamPhoto_MainView",
tagId = 9,
showInCenter = true,
activityNameType = "ANTItTakesFourGroupPhoto",
titleType = 1
},
[5004] = {
id = 5004,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
showEndTime = {
seconds = 1717689599
},
showBeginTime = {
seconds = 1714060800
}
},
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "*********",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
isInBottom = 1,
activityTaskGroup = {
43000,
43001,
43002,
43003,
43004,
43005,
43006,
43007
},
showInCenter = true,
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5005] = {
id = 5005,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1713715199
},
showEndTime = {
seconds = 1713715199
},
showBeginTime = {
seconds = 1712851200
}
},
labelId = 1,
lowVersion = "**********",
activityName = "一起去春游",
activityParam = {
2031
},
activityDesc = "叫上星搭子，赴一场春天的约会",
activityUIDetail = "UI_PlayerGroupsActivity",
tagId = 9,
activityTaskGroup = {
40014,
40012,
40013
},
showInCenter = true,
activityNameType = "ANTItTakesFour",
activityShopType = {
102
},
titleType = 1,
activityBeginCleanCoin = {
2031
}
},
[5006] = {
id = 5006,
activityType = "ATRelatedDisplay",
timeInfo = {
beginTime = {
seconds = 1712851200
},
endTime = {
seconds = 1713715199
},
showEndTime = {
seconds = 1713715199
},
showBeginTime = {
seconds = 1712851200
}
},
labelId = 2,
lowVersion = "**********",
activityName = "春日留影",
activityParam = {
5005
},
activityUIDetail = "UI_Activity_TeamPhoto_MainView",
tagId = 9,
showInCenter = true,
activityNameType = "ANTItTakesFourGroupPhoto",
titleType = 1
},
[5007] = {
id = 5007,
activityType = "ATFlyingChess",
timeInfo = {
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1723737599
},
showEndTime = {
seconds = 1723737599
},
showBeginTime = {
seconds = 1720108800
}
},
activityName = "寻梦之旅",
activityUIDetail = "UI_Activity_FlyingChess_Base_MainView",
isInBottom = 1,
activityNameType = "ANTFlyingChess",
titleType = 1
},
[5008] = {
id = 5008,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1715443200
},
endTime = {
seconds = 1718553599
},
showEndTime = {
seconds = 1718553599
},
showBeginTime = {
seconds = 1715443200
}
},
labelId = 1,
activityName = "闪耀盛夏",
activityParam = {
2031
},
activityDesc = "跟我比个心，闪走不开心",
activityUIDetail = "UI_PlayerGroupsActivity",
tagId = 9,
activityTaskGroup = {
40015,
40016,
40017
},
showInCenter = true,
activityNameType = "ANTItTakesFour",
activityShopType = {
102
},
titleType = 1,
activityBeginCleanCoin = {
2031
}
},
[5009] = {
id = 5009,
activityType = "ATRelatedDisplay",
timeInfo = {
beginTime = {
seconds = 1715443200
},
endTime = {
seconds = 1718553599
},
showEndTime = {
seconds = 1718553599
},
showBeginTime = {
seconds = 1715443200
}
},
labelId = 2,
activityName = "闪耀一刻",
activityParam = {
5008
},
activityUIDetail = "UI_Activity_TeamPhoto_MainView",
tagId = 9,
showInCenter = true,
activityNameType = "ANTItTakesFourGroupPhoto",
titleType = 1
},
[5010] = {
id = 5010,
activityType = "ATHalfYearWarmUp",
timeInfo = {
beginTime = {
seconds = 1715788800
},
endTime = {
seconds = 1720108799
},
showEndTime = {
seconds = 1720108799
},
showBeginTime = {
seconds = 1715788800
}
},
activityName = "寻梦签到站",
activityUIDetail = "UI_Activity_HalfYearWarm_Main",
isInBottom = 1,
activityNameType = "ANTHalfYearWarmUp",
titleType = 1
},
[5012] = {
id = 5012,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1745719200
},
endTime = {
seconds = 1748620799
},
showEndTime = {
seconds = 1748620799
},
showBeginTime = {
seconds = 1745719200
}
},
labelId = 99,
backgroundUrl = {
"T_WeChat_Bg_SummerGameplay.astc"
},
activityName = [[新赛季
前瞻爆料]],
activityUIDetail = "UI_WeChat_Championships_MainView",
tagId = 4,
showInCenter = true,
activityNameType = "ANTActivityNavigation",
titleType = 0
},
[5013] = {
id = 5013,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
showEndTime = {
seconds = 1721318399
},
showBeginTime = {
seconds = 1717689600
}
},
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "*******",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
activityTaskGroup = {
44000,
44001,
44002,
44003,
44004,
44005,
44006,
44007
},
showInCenter = true,
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5014] = {
id = 5014,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1746028799
},
showEndTime = {
seconds = 1746028799
},
showBeginTime = {
seconds = 1720108800
}
},
labelId = 1,
lowVersion = "********",
activityName = "寻梦同行屋",
activityParam = {
2070
},
activityDesc = "同游半岁礼，相伴寻梦嘉年华",
activityUIDetail = "UI_PlayerGroupsActivity",
tagId = 9,
activityTaskGroup = {
40020,
40018,
40019
},
showInCenter = true,
activityNameType = "ANTItTakesFour",
titleType = 1,
activityBeginCleanCoin = {
2070
}
},
[5015] = {
id = 5015,
activityType = "ATRelatedDisplay",
timeInfo = {
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1746028799
},
showEndTime = {
seconds = 1746028799
},
showBeginTime = {
seconds = 1720108800
}
},
labelId = 2,
lowVersion = "********",
activityName = "嘉年华留影",
activityParam = {
5014,
0,
420091
},
activityUIDetail = "UI_Activity_TeamPhoto_MainView",
tagId = 9,
showInCenter = true,
activityNameType = "ANTItTakesFourGroupPhoto",
titleType = 1
},
[5016] = {
id = 5016,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1748620799
},
showEndTime = {
seconds = 1748620799
},
showBeginTime = {
seconds = 1742745600
}
},
labelId = 1,
activityName = "欢乐集结",
activityParam = {
2073
},
activityDesc = "集结开心颜，定格美好瞬间",
activityUIDetail = "UI_PlayerGroupsActivity",
activityTaskGroup = {
40021,
40022,
40023
},
showInCenter = true,
activityNameType = "ANTItTakesFour",
activityShopType = {
102
},
titleType = 1
},
[5017] = {
id = 5017,
activityType = "ATRelatedDisplay",
timeInfo = {
beginTime = {
seconds = 1742745600
},
endTime = {
seconds = 1748620799
},
showEndTime = {
seconds = 1748620799
},
showBeginTime = {
seconds = 1742745600
}
},
labelId = 2,
activityName = "欢乐瞬间",
activityParam = {
5016,
0,
420117
},
activityUIDetail = "UI_Activity_TeamPhoto_MainView",
showInCenter = true,
activityNameType = "ANTItTakesFourGroupPhoto",
titleType = 1
},
[5018] = {
id = 5018,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1731945600
},
endTime = {
seconds = 1746028799
},
showEndTime = {
seconds = 1746028799
},
showBeginTime = {
seconds = 1731945600
}
},
labelId = 3,
lowVersion = "*********",
activityName = "少年侦探团",
activityParam = {
3432
},
activityDesc = "寻找好友加入少年侦探团吧！",
activityUIDetail = "UI_ConnanTeamActivity",
activityRuleId = 5018,
tagId = 9,
activityTaskGroup = {
40024,
40025,
40026
},
showInCenter = true,
activityNameType = "ANTItTakesFour",
titleType = 1
},
[5019] = {
id = 5019,
activityType = "ATRelatedDisplay",
timeInfo = {
beginTime = {
seconds = 1731945600
},
endTime = {
seconds = 1746028799
},
showEndTime = {
seconds = 1746028799
},
showBeginTime = {
seconds = 1731945600
}
},
labelId = 4,
lowVersion = "*********",
activityName = "侦探合影",
activityParam = {
5018,
0,
420129
},
activityUIDetail = "UI_Activity_ConnanTeamPhoto_MainView",
activityRuleId = 5018,
tagId = 9,
showInCenter = true,
activityNameType = "ANTItTakesFourGroupPhoto",
titleType = 1
},
[5020] = {
id = 5020,
activityType = "ATHalfYearNavigation",
timeInfo = {
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1723737599
},
showEndTime = {
seconds = 1723737599
},
showBeginTime = {
seconds = 1720108800
}
},
labelId = 1,
activityName = "半周年导航栏活动",
activityParam = {
45
},
activityUIDetail = "UI_Activity_WishesCameTrue_MainView",
isInBottom = 1,
activityNameType = "ANTHalfYearNavigation",
titleType = 1
},
[5021] = {
id = 5021,
activityType = "ATKuromiTechou",
timeInfo = {
beginTime = {
seconds = 1722268800
},
endTime = {
seconds = 1723737599
},
showEndTime = {
seconds = 1723737599
},
showBeginTime = {
seconds = 1722268800
}
},
labelId = 6,
backgroundUrl = {
"dahuangfeng.astc"
},
activityName = "酷洛米手账铺",
activityDesc = "酷洛米的手账本",
activityUIDetail = "UI_MusicOrder_MainSubView",
activityNameType = "ANTKuromiTechou",
titleType = 1
},
[5022] = {
id = 5022,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1720756800
},
endTime = {
seconds = 1721041199
},
showEndTime = {
seconds = 1721041199
},
showBeginTime = {
seconds = 1720756800
}
},
labelId = 99,
backgroundUrl = {
"T_WeChat_Bg_PartyGameplay.astc"
},
lowVersion = "*********",
activityName = [[魏大勋空降
新赛季前瞻]],
activityUIDetail = "UI_WXLiveSubscription_MainView",
tagId = 4,
showInCenter = true,
activityNameType = "ANTActivityNavigation",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAAV-MgMAzZ3AAAAAstQy6ubaLX4KHWvLEZgBPG3DKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBU8IBPCKc5YUiLjLgK99wW54EagjmnspPQ_OEq1YpXeyf3kZMbzTuHX29tWH5beYgLj7uHUuSL2L-4ttZ_A6MsD9I8nx-rRlB_GtiPAh4IDY3simYm61nChYtD7n4juIwjiq3Oct5MsmSa2rmLAN_7Q",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0
},
[5023] = {
id = 5023,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1724947199
},
showBeginTime = {
seconds = 1721318400
}
},
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
activityTaskGroup = {
44010,
44011,
44012,
44013,
44014,
44015,
44016,
44017,
44018
},
showInCenter = true,
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5029] = {
id = 5029,
activityType = "ATTaskAndShop",
timeInfo = {
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1724342399
},
showEndTime = {
seconds = 1724342399
},
showBeginTime = {
seconds = 1723737600
}
},
activityName = "寻梦之旅-尾声",
activityParam = {
5007,
8379,
8380,
8381
},
activityUIDetail = "UI_Activity_HalfAnniversaryExchange",
isInBottom = 1,
activityNameType = "ANTHalfYearExchange",
titleType = 1,
activityBeginCleanCoin = {
12
}
},
[5025] = {
id = 5025,
activityType = "ATCommonAppointment",
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1724947199
},
showEndTime = {
seconds = 1725551999
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 1,
lowVersion = "********39",
activityName = "轻松预约礼",
activityParam = {
5026,
5027,
5028,
5030
},
activityUIDetail = "UI_EasyReserve_MainView",
isInBottom = 1,
activityTaskGroup = {
44019
},
showInCenter = true,
activityNameType = "ANTEaseBurden",
clientParams = {
"Activity_EasyReserve_MainTips",
"https://image-manage.ymzx.qq.com/wuji/2024/08/16/5EdflNVH.png"
},
titleType = 1,
activitySubName = "奖励手到擒来"
},
[5026] = {
id = 5026,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1726415999
},
showEndTime = {
seconds = 1726415999
},
showBeginTime = {
seconds = 1724947200
}
},
labelId = 2,
lowVersion = "********39",
activityName = "七日签到礼",
activityUIDetail = "UI_EasyCheckIn_Main",
isInBottom = 1,
activityTaskGroup = {
44020
},
showInCenter = true,
activityNameType = "ANTTotalLogin",
titleType = 1,
activitySubName = "送免费新配饰"
},
[5027] = {
id = 5027,
activityType = "ATEaseBurdenEasyTask",
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1726415999
},
showEndTime = {
seconds = 1726415999
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 3,
lowVersion = "********39",
activityName = "任务大减负",
activityUIDetail = "UI_EasyTask_MainView",
isInBottom = 1,
activityRuleId = 5027,
showInCenter = true,
activityNameType = "ANTEaseBurdenEasyTask",
clientParams = {
"Activity_EasyTask_MainTips",
"Activity_EasyTask_TotalTips"
},
titleType = 1,
activitySubName = "福利继续升级"
},
[5028] = {
id = 5028,
activityType = "ATEaseBurdenTeamTask",
timeInfo = {
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1725206399
},
showEndTime = {
seconds = 1725206399
},
showBeginTime = {
seconds = 1724342400
}
},
labelId = 4,
lowVersion = "********39",
activityName = "热情帮帮队",
activityUIDetail = "UI_EasyTeam_MainView",
isInBottom = 1,
activityRuleId = 5028,
activityTaskGroup = {
44021
},
showInCenter = true,
activityNameType = "ANTEaseBurdenTeamTask",
isHideMainBackground = true,
titleType = 1,
activitySubName = "领奖励超简单"
},
[5030] = {
id = 5030,
activityType = "ATEaseBurdenTeamTask",
timeInfo = {
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1726415999
},
showEndTime = {
seconds = 1726415999
},
showBeginTime = {
seconds = 1725552000
}
},
activityName = "热情帮帮队",
activityUIDetail = "UI_EasyTeam_MainView",
isInBottom = 1,
activityRuleId = 5028,
activityTaskGroup = {
44022
},
showInCenter = true,
activityNameType = "ANTEaseBurdenTeamTask",
isHideMainBackground = true,
titleType = 1,
activitySubName = "领奖励超简单"
},
[5032] = {
id = 5032,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1724947200
}
},
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
tagId = 4,
activityTaskGroup = {
44030,
44031,
44032,
44033,
44034,
44035,
44036,
44037,
44038
},
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5033] = {
id = 5033,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1724169600
},
endTime = {
seconds = 4102329600
},
showEndTime = {
seconds = 4102329600
},
showBeginTime = {
seconds = 1724169600
}
},
lowVersion = "********",
activityName = "冲段挑战",
activityUIDetail = "UI_Task_ImpactPage",
tagId = 4,
activityTaskGroup = {
10014,
10013,
10012,
10011,
10010,
10009,
10008,
10007
},
activityNameType = "ATTaskImpact",
isHideMainBackground = true,
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAA6WMLcoMa0wAAAAstQy6ubaLX4KHWvLEZgBPG0zKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBB8IDPCec5YV1LjHgK99yW50EawjmnsoQFaaH_AcsWLjI2kVLOma6TibhuGb6a-chLG3pTkWQL2L-4ttZ_A6MsD9I9nh-qRdA_21gOQN9JjYxuSqcna5rCRYKHqBDoGm3Rk9yqPedwpI5y34btJhW",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0
},
[5034] = {
id = 5034,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1724169600
},
endTime = {
seconds = 4102329600
},
showEndTime = {
seconds = 4102329600
},
showBeginTime = {
seconds = 1724169600
}
},
labelId = 98,
lowVersion = "********",
activityName = "造梦之旅",
activityUIDetail = "UI_Task_UGCDreamTravel_Main",
tagId = 4,
activityTaskGroup = {
1402
},
showInCenter = true,
activityNameType = "ATTaskUGCDreamTravel",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAA6WMLcoMa0wAAAAstQy6ubaLX4KHWvLEZgBPG0zKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBB8IDPCec5YV1LjHgK99yW50EawjmnsoQFaaH_AcsWLjI2kVLOma6TibhuGb6a-chLG3pTkWQL2L-4ttZ_A6MsD9I9nh-qRdA_21gOQN9JjYxuSqcna5rCRYKHqBDoGm3Rk9yqPedwpI5y34btJhW",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activityGroupList = {
"AG_UGC"
}
},
[5035] = {
id = 5035,
activityType = "ATUGCChaseDream",
timeInfo = {
beginTime = {
seconds = 1724169600
},
endTime = {
seconds = 4102329600
},
showEndTime = {
seconds = 4102329600
},
showBeginTime = {
seconds = 1724169600
}
},
labelId = 99,
lowVersion = "********",
activityName = "追梦星途",
activityUIDetail = "UI_Dream_MainView",
tagId = 4,
activityTaskGroup = {
1403,
1408,
1409,
1410
},
showInCenter = true,
activityNameType = "ATTaskDream",
isHideMainBackground = true,
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAA6WMLcoMa0wAAAAstQy6ubaLX4KHWvLEZgBPG0zKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBB8IDPCec5YV1LjHgK99yW50EawjmnsoQFaaH_AcsWLjI2kVLOma6TibhuGb6a-chLG3pTkWQL2L-4ttZ_A6MsD9I9nh-qRdA_21gOQN9JjYxuSqcna5rCRYKHqBDoGm3Rk9yqPedwpI5y34btJhW",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activityGroupList = {
"AG_UGC"
}
},
[5036] = {
id = 5036,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1724169600
},
endTime = {
seconds = 4102329600
},
showEndTime = {
seconds = 4102329600
},
showBeginTime = {
seconds = 1724169600
}
},
labelId = 99,
lowVersion = "********",
activityName = "扫码一起玩",
activityUIDetail = "UI_Task_Party_Main",
tagId = 4,
activityTaskGroup = {
15001
},
showInCenter = true,
activityNameType = "ATTaskParty",
isHideMainBackground = true,
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAA6WMLcoMa0wAAAAstQy6ubaLX4KHWvLEZgBPG0zKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBB8IDPCec5YV1LjHgK99yW50EawjmnsoQFaaH_AcsWLjI2kVLOma6TibhuGb6a-chLG3pTkWQL2L-4ttZ_A6MsD9I9nh-qRdA_21gOQN9JjYxuSqcna5rCRYKHqBDoGm3Rk9yqPedwpI5y34btJhW",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activitySubName = "面对面同玩"
},
[5040] = {
id = 5040,
activityType = "ATHalfYearWarmUp",
timeInfo = {
beginTime = {
seconds = 1725897600
},
endTime = {
seconds = 1730303999
},
showEndTime = {
seconds = 1730303999
},
showBeginTime = {
seconds = 1725897600
}
},
activityName = "欢庆签到站",
activityUIDetail = "UI_Activity_NationalDayWarm_Main",
showInCenter = true,
activityNameType = "ANTHalfYearWarmUp",
titleType = 1
},
[5039] = {
id = 5039,
activityType = "ATEaseBurdenTeamTask",
timeInfo = {
beginTime = {
seconds = 1726761600
},
endTime = {
seconds = 1727625599
},
showEndTime = {
seconds = 1727625599
},
showBeginTime = {
seconds = 1726761600
}
},
labelId = 4,
lowVersion = "********39",
activityName = "热情帮帮队",
activityUIDetail = "UI_EasyTeam_MainView",
isInBottom = 1,
activityRuleId = 5028,
activityTaskGroup = {
44039
},
showInCenter = true,
activityNameType = "ANTEaseBurdenTeamTask",
isHideMainBackground = true,
titleType = 1,
activitySubName = "领奖励超简单"
},
[5037] = {
id = 5037,
activityType = "ATPrayerCard",
timeInfo = {
beginTime = {
seconds = 1727625600
},
endTime = {
seconds = 1728835199
},
showEndTime = {
seconds = 1728835199
},
showBeginTime = {
seconds = 1727625600
}
},
labelId = 4,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "1.3.18.80",
activityName = "花笺觅巧思",
activityUIDetail = "UI_NationalDayCollection_MainView",
activityRuleId = 246,
showInCenter = true,
activityNameType = "ANTPrayerCard",
activityShopType = {
153
},
titleType = 1,
activitySubName = "集灵感制华裳"
},
[5038] = {
id = 5038,
activityType = "ATDanceOutfit",
timeInfo = {
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1760371199
},
showEndTime = {
seconds = 1760371199
},
showBeginTime = {
seconds = 1727712000
}
},
labelId = 4,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "1.3.18.80",
activityName = "风华新次元",
activityParam = {
2071,
422
},
activityUIDetail = "UI_Activity_CostumeBall_MainView",
activityRuleId = 248,
activityTaskGroup = {
44500,
44501
},
showInCenter = true,
activityNameType = "ANTDanceOutfit",
titleType = 1,
activitySubName = "国潮装扮登台秀"
},
[30063] = {
id = 30063,
activityType = "ATBookOfFriends",
timeInfo = {
beginTime = {
seconds = 1727690400
},
endTime = {
seconds = 1729353599
},
showEndTime = {
seconds = 1729353599
},
showBeginTime = {
seconds = 1727690400
}
},
labelId = 4,
lowVersion = "1.3.18.92",
activityName = "鹿篮招募礼",
activityDesc = "招募玩家随机增加进度！",
activityUIDetail = "UI_Activity_GreenHouse_MainView2",
isInBottom = 1,
activityRuleId = 302,
showInCenter = true,
activityNameType = "ANTWish",
activitySubName = "免费农场外观",
activityGroup = "AG_Farm",
activityAssistType = "AAT_GreenHouse"
},
[5041] = {
id = 5041,
activityType = "ATEaseBurdenTeamTask",
timeInfo = {
beginTime = {
seconds = 1728316800
},
endTime = {
seconds = 1729180799
},
showEndTime = {
seconds = 1729180799
},
showBeginTime = {
seconds = 1728316800
}
},
labelId = 4,
lowVersion = "********39",
activityName = "热情帮帮队",
activityUIDetail = "UI_EasyTeam_MainView",
isInBottom = 1,
activityRuleId = 5028,
activityTaskGroup = {
44040
},
showInCenter = true,
activityNameType = "ANTEaseBurdenTeamTask",
isHideMainBackground = true,
titleType = 1,
activitySubName = "领奖励超简单"
},
[5042] = {
id = 5042,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1728576000
},
endTime = {
seconds = 1728730800
},
showEndTime = {
seconds = 1728730800
},
showBeginTime = {
seconds = 1728576000
}
},
labelId = 1,
backgroundUrl = {
"T_WeChat_Bg_Werewolfnew.astc"
},
lowVersion = "********",
activityName = "新赛季前瞻",
activityUIDetail = "UI_WXLiveSubscription_MainView",
tagId = 4,
showInCenter = true,
activityNameType = "ANTActivityNavigation",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAAMvIq_1RkkgAAAAstQy6ubaLX4KHWvLEZgBPG0zKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBU8IBPCKc5YUiLjLgK99wW54EagjmnspPQ_OEq1YpXeyf3kZMbzTuHX29tWH5beYgLj7uHUuSL2L-4ttZ_A6MsD9I9Xx-rxlF8mRvNwJ4JDYxsiidkaJqDBIKHqBDoGk81p0fXDum-DvaEI8kmNVS",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activitySubName = "订阅领好礼"
},
[5043] = {
id = 5043,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1725724800
},
endTime = {
seconds = 4102329600
},
showEndTime = {
seconds = 4102329600
},
showBeginTime = {
seconds = 1725724800
}
},
backgroundUrl = {
"T_WeChat_Bg_SummerGameplay.astc"
},
lowVersion = "*********",
activityName = "成长福利",
activityDesc = "快乐成长，收获惊喜",
activityUIDetail = "UI_Activity_GrowthPath_View",
tagId = 4,
showInCenter = true,
activityNameType = "ANTActivityNavigation",
titleType = 0,
activitySubName = "收获成长福利",
activityAssistType = "AAT_GrowthPath"
},
[5044] = {
id = 5044,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
showEndTime = {
seconds = 1732809599
},
showBeginTime = {
seconds = 1729180800
}
},
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
tagId = 4,
activityTaskGroup = {
44049,
44041,
44042,
44043,
44044,
44045,
44046,
44047,
44048
},
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5045] = {
id = 5045,
activityType = "ATConanWarmup",
timeInfo = {
beginTime = {
seconds = 1734192000
},
endTime = {
seconds = 1748620799
},
showEndTime = {
seconds = 1748620799
},
showBeginTime = {
seconds = 1734192000
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "侦探新星招募",
activityUIDetail = "UI_NationalDayCollection_MainView",
activityRuleId = 501,
tagId = 4,
activityTaskGroup = {
44103
},
slapFace = true,
activityNameType = "ANTConanWarmup",
titleType = 0,
activitySubName = "柯南答题",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
2
}
}
}
},
[5046] = {
id = 5046,
activityType = "ATConanCeremony",
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1748620799
},
showEndTime = {
seconds = 1748620799
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 11,
activityName = "柯南活动导航",
activityParam = {
5045,
5055,
5047,
5018,
5048
},
activityUIDetail = "UI_Lobby_Conan_Linkage_NavigationPage",
isInBottom = 1,
slapFace = true,
activityNameType = "ANTConanCeremony",
titleType = 0,
activitySubName = "柯南-导航页"
},
[5047] = {
id = 5047,
activityType = "ATAnimalHandbook",
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 11,
activityName = "收集角色徽章",
activityUIDetail = "UI_Connan_CollectCard_EntryView",
isInBottom = 1,
activityTaskGroup = {
44101,
44102
},
activityNameType = "ANTAnimalHandbook",
titleType = 0,
activitySubName = "柯南-集卡"
},
[5048] = {
id = 5048,
activityType = "ATCaptureShadow",
timeInfo = {
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1734624000
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "追击嫌疑星宝",
activityParam = {
3433
},
activityUIDetail = "UI_Connan_GoGrid",
activityRuleId = 502,
tagId = 4,
slapFace = true,
activityNameType = "ANTCaptureShadow",
titleType = 0,
activitySubName = "柯南走格子"
},
[5052] = {
id = 5052,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1728489600
},
endTime = {
seconds = 1728489600
},
showEndTime = {
seconds = 1728489600
},
showBeginTime = {
seconds = 1728489600
}
},
labelId = 99,
activityName = "周年庆送蛋糕活动",
activityUIDetail = "UI_AnniversaryCake_TaskView",
activityTaskGroup = {
40101,
40102,
40103,
40104
},
showInCenter = true,
activityNameType = "ANTTotalLogin",
titleType = 0
},
[5055] = {
id = 5055,
timeInfo = {
beginTime = {
seconds = 1728921600
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1728921600
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "侦探新星招募-结束剧情",
tagId = 4,
titleType = 0,
activitySubName = "柯南答题-结束剧情",
moduleConfigs = {
{
type = "AMT_Quiz",
params = {
2
}
}
}
},
[5056] = {
id = 5056,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
showEndTime = {
seconds = 1737043199
},
showBeginTime = {
seconds = 1732809600
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_Task_Bg_ModelSelect.astc"
},
lowVersion = "1.3.36.1",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
tagId = 4,
activityTaskGroup = {
44059,
44051,
44052,
44053,
44054,
44055,
44056,
44057,
44058
},
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5057] = {
id = 5057,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732273200
},
showEndTime = {
seconds = 1732273200
},
showBeginTime = {
seconds = 1732204800
}
},
labelId = 1,
backgroundUrl = {
"T_WeChat_Bg_Delicous.astc"
},
lowVersion = "********",
activityName = "新赛季前瞻",
activityUIDetail = "UI_WXLiveSubscription_MainView",
tagId = 4,
showInCenter = true,
activityNameType = "ANTActivityNavigation",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAAkrsogG0o7gAAAAstQy6ubaLX4KHWvLEZgBPG3DKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBU8IBPCKc5YUiLjLgK99wW54EagjmnspPQ_OEq1YpXeyf3kZMbzTuHX29tWH5beYgLj7uHUuSL2L-4ttZ_A6MsD9J83t0qxhN_m5gNwVyJjY3uiuRn65oDRcuD7n4juIwnnIt_wOyvEABSgFWek_oqg",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activitySubName = "订阅领好礼"
},
[5058] = {
id = 5058,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1732964400
},
showEndTime = {
seconds = 1732964400
},
showBeginTime = {
seconds = 1732723200
}
},
backgroundUrl = {
"T_WeChat_Bg_TNTbroadcast.astc"
},
lowVersion = "********",
activityName = "时代少年团",
activityUIDetail = "UI_WXLiveSubscription_MainView",
showInCenter = true,
activityNameType = "ANTActivityNavigation",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAAmCQKYtChPgAAAAstQy6ubaLX4KHWvLEZgBPG3DKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBUcIHPHectoUnLmXgLd93W8EEbwjmnspBTPbR_gZ8WOuY2xEdb2noFn3ktGD-aOF1fGi9QBSSJ2L-4ttZ_A6MsD9J8Hp7qBZC-GhuPANzIDY3uCqZn6tuDx8pD7n4juIwx_P2Ji1p9jw_xEyTnA1zKg",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activitySubName = "空降直播间"
},
[5060] = {
id = 5060,
activityType = "ATTradingCardShow",
timeInfo = {
beginTime = {
seconds = 1733932800
},
endTime = {
seconds = 1736092799
},
showEndTime = {
seconds = 1736092799
},
showBeginTime = {
seconds = 1733932800
}
},
labelId = 4,
backgroundUrl = {
"T_Task_Bg_CardActivity.astc"
},
jumpId = 902,
lowVersion = "*********",
activityName = "卡牌集换",
activityDesc = [[收集<CardEvent>卡牌</>，开启集齐<CardEvent>卡册</>之旅
可通过<CardEvent>发现</>、<CardEvent>每日奖杯挑战</>等途径获取卡牌
<CardEvent>普通卡牌</>易得，<CardEvent>金色卡牌</>更稀有，含金色卡牌卡册奖励更丰厚;星宝们可互相<CardEvent>索要</>或<CardEvent>赠送</>卡牌，加速收集
可向<CardEvent>好友</>或<CardEvent>卡牌世界频道求助</>，更快集齐缺少卡牌
将<CardEvent>多余</>的卡牌<CardEvent>赠送</>出去，成为大家喜爱的伙伴！]],
activityUIDetail = "UI_Activity_CardView",
showInCenter = true,
slapFace = true,
activityNameType = "ANTCard",
titleType = 1,
activitySubName = "趣味收集领奖"
},
[5061] = {
id = 5061,
activityType = "ATSpringLobby2025",
timeInfo = {
beginTime = {
seconds = 1736870400
},
endTime = {
seconds = 1737471600
},
showEndTime = {
seconds = 1737471600
},
showBeginTime = {
seconds = 1736870400
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "2025春节广场",
tagId = 4,
slapFace = true,
activityNameType = "ANTSpringLobby2025",
titleType = 0,
activitySubName = "变身叠高高_变桶"
},
[5062] = {
id = 5062,
activityType = "ATSpringLobby2025",
timeInfo = {
beginTime = {
seconds = 1738339200
},
endTime = {
seconds = 1738940400
},
showEndTime = {
seconds = 1738940400
},
showBeginTime = {
seconds = 1738339200
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "2025春节广场",
tagId = 4,
slapFace = true,
activityNameType = "ANTSpringLobby2025",
titleType = 0,
activitySubName = "变身叠高高_变金元宝"
},
[5063] = {
id = 5063,
activityType = "ATSpringLobby2025",
timeInfo = {
beginTime = {
seconds = 1737475200
},
endTime = {
seconds = 1737990000
},
showEndTime = {
seconds = 1737990000
},
showBeginTime = {
seconds = 1737302400
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "2025春节广场",
tagId = 4,
slapFace = true,
activityNameType = "ANTSpringLobby2025",
titleType = 0,
activitySubName = "贴福字互动"
},
[5064] = {
id = 5064,
activityType = "ATSpringLobby2025",
timeInfo = {
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1738681200
},
showEndTime = {
seconds = 1738681200
},
showBeginTime = {
seconds = 1737820800
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "2025春节广场",
tagId = 4,
slapFace = true,
activityNameType = "ANTSpringLobby2025",
titleType = 0,
activitySubName = "拜年互动"
},
[5065] = {
id = 5065,
activityType = "ATSpringLobby2025",
timeInfo = {
beginTime = {
seconds = 1738944000
},
endTime = {
seconds = 1739372400
},
showEndTime = {
seconds = 1739372400
},
showBeginTime = {
seconds = 1738771200
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "2025春节广场",
tagId = 4,
slapFace = true,
activityNameType = "ANTSpringLobby2025",
titleType = 0,
activitySubName = "点花灯互动"
},
[5066] = {
id = 5066,
activityType = "ATSpringLobby2025",
timeInfo = {
beginTime = {
seconds = 1737475200
},
endTime = {
seconds = 4095846000
},
showEndTime = {
seconds = 4095846000
},
showBeginTime = {
seconds = 1737475200
}
},
labelId = 1,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
activityName = "2025春节广场",
tagId = 4,
slapFace = true,
activityNameType = "ANTSpringLobby2025",
titleType = 0,
activitySubName = "自由叠高高"
},
[5067] = {
id = 5067,
activityType = "ATPrayerCard",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739548799
},
showEndTime = {
seconds = 1739548799
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 4,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "2025春节捡花笺",
activityUIDetail = "UI_NationalDayCollection_MainView",
activityRuleId = 246,
activityNameType = "ANTPrayerCard",
titleType = 1,
activitySubName = "集灵感制华裳"
},
[5068] = {
id = 5068,
activityType = "ATScoreGuide",
timeInfo = {
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1736784000
}
},
labelId = 6,
lowVersion = "********",
activityName = "评分引导",
tagId = 4,
activityNameType = "ANTScoreGuide"
},
[5069] = {
id = 5069,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1736352000
},
endTime = {
seconds = 1736506800
},
showEndTime = {
seconds = 1736506800
},
showBeginTime = {
seconds = 1736352000
}
},
backgroundUrl = {
"T_WeChat_Bg_TangNew.astc"
},
lowVersion = "********",
activityName = "新赛季前瞻",
activityUIDetail = "UI_WXLiveSubscription_MainView",
showInCenter = true,
activityNameType = "ANTActivityNavigation",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAAllMaNIls-AAAAAstQy6ubaLX4KHWvLEZgBPG0zKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBU8IBPCKc5YUiLjLgK99wW54EagjmnspPQ_OEq1YpXeyf3kZMbzTuHX29tWH5beYgLj7uHUuSL2L-4ttZ_A6MsD9J935-rhJD-GVjPAV_ITYzvCuRnqJnBB4KHqBDoGnXnZH6PBFeDJvvM8Ghkomr",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activitySubName = "精彩抢先看"
},
[5070] = {
id = 5070,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
showEndTime = {
seconds = 1741881599
},
showBeginTime = {
seconds = 1737043200
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
tagId = 4,
activityTaskGroup = {
44069,
44061,
44062,
44063,
44064,
44065,
44066,
44067,
44068
},
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5071] = {
id = 5071,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1736848800
},
endTime = {
seconds = 1737198000
},
showEndTime = {
seconds = 1737198000
},
showBeginTime = {
seconds = 1736848800
}
},
backgroundUrl = {
"T_WeChat_Bg_SongYQ.astc"
},
lowVersion = "********",
activityName = "宋雨绮空降",
activityUIDetail = "UI_WXLiveSubscription_MainView",
showInCenter = true,
activityNameType = "ANTActivityNavigation",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAAdn0tqrEaDgAAAAstQy6ubaLX4KHWvLEZgBPG3DKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBU8IBPCKc5YUiLjLgK99wW54EagjmnspPQ_OEq1YpXeyf3kZMbzTuHX29tWH5beYgLj7uHUuSL2L-4ttZ_A6MsD9J93B1pBdM-GljOgJ9JzY3viOeka9nCRYqD7n4juIwazLtmgebW8KQmF4TRaAf1w",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activitySubName = "星运直播间"
},
[5072] = {
id = 5072,
activityType = "ATBirthday",
timeInfo = {
beginTime = {
seconds = 1702634400
},
endTime = {
seconds = 4101033600
},
showEndTime = {
seconds = 4101033600
},
showBeginTime = {
seconds = 1702634400
}
},
labelId = 1,
backgroundUrl = {
"T_PlayerInfo_Bg_BirthdayActivity"
},
lowVersion = "*********",
activityName = "生日福利",
activityUIDetail = "UI_PlayerInfo_BirthdayActivity",
tagId = 4,
activityTaskGroup = {
443001
},
showInCenter = true,
activityNameType = "ANTBirthday",
titleType = 1,
codeName = "ShowBrithday"
},
[5073] = {
id = 5073,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1741190400
},
endTime = {
seconds = 1741345200
},
showEndTime = {
seconds = 1741345200
},
showBeginTime = {
seconds = 1741190400
}
},
backgroundUrl = {
"T_WeChat_Bg_Truth.astc"
},
lowVersion = "********",
activityName = "新赛季前瞻",
activityUIDetail = "UI_WXLiveSubscription_MainView",
showInCenter = true,
activityNameType = "ANTActivityNavigation",
clientParams = {
"notice/UzFfAgtgekIEAQAAAAAAI5kD2ZdGWgAAAAstQy6ubaLX4KHWvLEZgBPG3DKW1_6IvxcvpLWETf5nT2_2v15rKcGPtQlqtR9mAF7S7KTYR16r-3SOSBjXPhHBU8IBPCKc5YUiLjLgK99wW54EagjmnspPQ_OEq1YpXeyf3kZMbzTuHX29tWH5beYgLj7uHUuSL2L-4ttZ_A6MsD9J-3x-qhlM-WtuOAN7JTY3vSqZnq1uDRAsD7n4juIwAYDFskeeEUp2NkbM78G-_Q",
"v2_060000231003b20faec8c5e58d10c6d4ce04ef34b0779761ed01124a71ee28845a164005e918@finder"
},
titleType = 0,
activitySubName = "订阅领好礼"
},
[5074] = {
id = 5074,
activityType = "ATScoreGuide",
timeInfo = {
beginTime = {
seconds = 1741622400
},
endTime = {
seconds = 1746115199
},
showEndTime = {
seconds = 1746115199
},
showBeginTime = {
seconds = 1741622400
}
},
labelId = 6,
lowVersion = "********",
activityName = "评分引导",
tagId = 4,
activityNameType = "ANTScoreGuide"
},
[5075] = {
id = 5075,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746115199
},
showEndTime = {
seconds = 1746115199
},
showBeginTime = {
seconds = 1741881600
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
tagId = 4,
activityTaskGroup = {
44071,
44072,
44073,
44074,
44075,
44076,
44077,
44078
},
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5076] = {
id = 5076,
activityType = "ATMultiPlayerGroup",
timeInfo = {
beginTime = {
seconds = 1743436800
},
endTime = {
seconds = 1751299199
},
showEndTime = {
seconds = 1751299199
},
showBeginTime = {
seconds = 1743436800
}
},
labelId = 3,
activityName = "晋级赛冲分",
activityParam = {
3432
},
activityDesc = "寻找好友四人一起上分吧！",
activityUIDetail = "UI_TeamActivity_SuperCampus",
activityRuleId = 5018,
activityTaskGroup = {
40027,
40028,
40029
},
activityNameType = "ANTItTakesFour",
titleType = 1,
activityGroupList = {
"AG_Main"
},
activityGroupType = 1
},
[5077] = {
id = 5077,
activityType = "ATConanIpActive",
timeInfo = {
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1755273599
},
showEndTime = {
seconds = 1755273599
},
showBeginTime = {
seconds = 1737648000
}
},
labelId = 4,
backgroundUrl = {
"T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "柯南二期活跃活动",
activityParam = {
1,
0,
181886,
440601
},
activityUIDetail = "UI_ActivityConnan_MagicGameMain",
activityRuleId = 505,
activityTaskGroup = {
44080,
44093
},
showInCenter = true,
activityNameType = "ANTConanIpActive",
titleType = 1
},
[5078] = {
id = 5078,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
showEndTime = {
seconds = 1750953599
},
showBeginTime = {
seconds = 1746115200
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_Task_Bg_ModelSelect.astc"
},
lowVersion = "********",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
tagId = 4,
activityTaskGroup = {
44081,
44082,
44083,
44084,
44085,
44086,
44087,
44088
},
activityNameType = "ANTTotalLogin",
titleType = 1
},
[5079] = {
id = 5079,
activityType = "ATScoreGuide",
timeInfo = {
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
showEndTime = {
seconds = 1750953599
},
showBeginTime = {
seconds = 1746115200
}
},
labelId = 6,
lowVersion = "********",
activityName = "评分引导",
tagId = 4,
activityNameType = "ANTScoreGuide"
},
[5080] = {
id = 5080,
activityType = "ATActivityNavigation",
timeInfo = {
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1748793599
},
showEndTime = {
seconds = 1748793599
},
showBeginTime = {
seconds = 1746028800
}
},
labelId = 7,
lowVersion = "********",
activityName = "喵喵节",
tagId = 4,
showInCenter = true,
activityNameType = "ANTActivityNavigation"
},
[5081] = {
id = 5081,
activityType = "ATFlashRaceCheering",
timeInfo = {
beginTime = {
seconds = 1746115200
},
endTime = {
seconds = 1750953599
},
showEndTime = {
seconds = 1750953599
},
showBeginTime = {
seconds = 1746115200
}
},
labelId = 4,
lowVersion = "********00",
activityName = "助威闪电赛",
activityUIDetail = "UI_Activity_LightningMatch_Main",
activityTaskGroup = {
44090,
44091,
44092
},
showInCenter = true,
activityNameType = "ANTFlashRaceCheering",
titleType = 0,
activitySubName = "给TA投票"
},
[5082] = {
id = 5082,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
showEndTime = {
seconds = 1755791999
},
showBeginTime = {
seconds = 1750953600
}
},
backgroundUrl = {
"https://image-manage.ymzx.qq.com/wuji/client/materials/T_Task_Bg_ModelSelect.astc"
},
lowVersion = "1.3.99.1",
activityName = "娱乐排位赛",
activityUIDetail = "UI_CommonTask_MulTaskView",
tagId = 4,
activityTaskGroup = {
46001,
46002,
46003,
46004,
46005,
46006,
46007,
46008
},
activityNameType = "ANTTotalLogin",
titleType = 1,
srcPakGroupId = 1100001
},
[5083] = {
id = 5083,
activityType = "ATScoreGuide",
timeInfo = {
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1755791999
},
showEndTime = {
seconds = 1755791999
},
showBeginTime = {
seconds = 1750953600
}
},
labelId = 6,
lowVersion = "1.3.99.1",
activityName = "评分引导",
tagId = 4,
activityNameType = "ANTScoreGuide"
},
[5084] = {
id = 5084,
activityType = "ATSummerFlashMob",
timeInfo = {
beginTime = {
seconds = 1746633600
},
endTime = {
seconds = 1756483199
},
showEndTime = {
seconds = 1756483199
},
showBeginTime = {
seconds = 1746633600
}
},
labelId = 6,
lowVersion = "1.3.99.1",
activityName = "暑期快闪活动",
activityUIDetail = "UI_Activity_SummerFlashing_SubView",
activityRuleId = 5084,
tagId = 4,
slapFace = true,
activityNameType = "ANTSummerFlashMob",
titleType = 0
}
}

local mt = {
labelId = 5,
tagId = 1,
showInCenter = false,
slapFace = false,
isHideMainBackground = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data