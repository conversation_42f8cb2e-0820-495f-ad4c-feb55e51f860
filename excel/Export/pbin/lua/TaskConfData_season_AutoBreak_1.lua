--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/R_任务表_策划专用.xlsx: 赛季任务

local data = {
[50173] = {
id = 50173,
desc = "黄金领航星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10011
},
[50174] = {
id = 50174,
desc = "黄金领航星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
4
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10011
},
[50175] = {
id = 50175,
desc = "铂金典藏星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
1
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
404870
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 404870
},
taskGroupId = 10011
},
[50176] = {
id = 50176,
desc = "铂金典藏星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
4
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10011
},
[50177] = {
id = 50177,
desc = "钻石永恒星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 10011
},
[50178] = {
id = 50178,
desc = "钻石永恒星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
4
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10011
},
[50179] = {
id = 50179,
desc = "至尊梦想星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
1
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
620727
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 620727
},
taskGroupId = 10011
},
[50180] = {
id = 50180,
desc = "最强元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
1
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
840238
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10011
},
[50181] = {
id = 50181,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
1
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
720856
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1741881599
}
}
},
taskGroupId = 10011
},
[50182] = {
id = 50182,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
2
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
999999
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1741881599
}
}
},
taskGroupId = 10011
},
[50183] = {
id = 50183,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
3
}
},
{
type = 154,
value = {
10
}
}
}
}
}
}
},
reward = {
itemIdList = {
720857
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1741881599
}
}
},
taskGroupId = 10011
},
[50191] = {
id = 50191,
desc = "青铜小萌星3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
1,
3
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50192] = {
id = 50192,
desc = "白银启明星3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
2,
3
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
410590
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50193] = {
id = 50193,
desc = "黄金领航星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50194] = {
id = 50194,
desc = "黄金领航星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
4
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50195] = {
id = 50195,
desc = "铂金典藏星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
1
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
410550
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 410550
},
taskGroupId = 10012
},
[50196] = {
id = 50196,
desc = "铂金典藏星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
4
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50197] = {
id = 50197,
desc = "钻石永恒星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50198] = {
id = 50198,
desc = "钻石永恒星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
4
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50199] = {
id = 50199,
desc = "至尊梦想星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
1
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
340008
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50200] = {
id = 50200,
desc = "最强元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
1
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
840266
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10012
},
[50201] = {
id = 50201,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
1
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
720856
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746115199
}
}
},
taskGroupId = 10012
},
[50202] = {
id = 50202,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
2
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
999999
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746115199
}
}
},
taskGroupId = 10012
},
[50203] = {
id = 50203,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
3
}
},
{
type = 154,
value = {
11
}
}
}
}
}
}
},
reward = {
itemIdList = {
720857
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1746115199
}
}
},
taskGroupId = 10012
},
[50211] = {
id = 50211,
desc = "青铜小萌星3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
1,
3
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50212] = {
id = 50212,
desc = "白银启明星3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
2,
3
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
411120
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50213] = {
id = 50213,
desc = "黄金领航星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50214] = {
id = 50214,
desc = "黄金领航星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
4
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50215] = {
id = 50215,
desc = "铂金典藏星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
1
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50216] = {
id = 50216,
desc = "铂金典藏星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
4
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50217] = {
id = 50217,
desc = "钻石永恒星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
411060
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 411060
},
taskGroupId = 10013
},
[50218] = {
id = 50218,
desc = "钻石永恒星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
4
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50219] = {
id = 50219,
desc = "至尊梦想星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
1
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
340014
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50220] = {
id = 50220,
desc = "最强元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
1
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
840300
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10013
},
[50221] = {
id = 50221,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
1
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
720856
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
taskGroupId = 10013
},
[50222] = {
id = 50222,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
2
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
999999
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
taskGroupId = 10013
},
[50223] = {
id = 50223,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
3
}
},
{
type = 154,
value = {
12
}
}
}
}
}
}
},
reward = {
itemIdList = {
720857
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
taskGroupId = 10013
},
[50231] = {
id = 50231,
desc = "青铜小萌星3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
1,
3
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50232] = {
id = 50232,
desc = "白银启明星3",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
2,
3
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
411120
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50233] = {
id = 50233,
desc = "黄金领航星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
1
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50234] = {
id = 50234,
desc = "黄金领航星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
3,
4
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
1000
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50235] = {
id = 50235,
desc = "铂金典藏星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
1
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
6
},
numList = {
50
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50236] = {
id = 50236,
desc = "铂金典藏星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
4,
4
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50237] = {
id = 50237,
desc = "钻石永恒星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
1
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
411060
},
numList = {
1
},
validPeriodList = {
0
}
},
extraConf = {
rewardItemId = 411060
},
taskGroupId = 10014
},
[50238] = {
id = 50238,
desc = "钻石永恒星2",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
5,
4
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
4
},
numList = {
2000
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50239] = {
id = 50239,
desc = "至尊梦想星5",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
6,
1
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
340014
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50240] = {
id = 50240,
desc = "最强元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
7,
1
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
840300
},
numList = {
1
},
validPeriodList = {
0
}
},
taskGroupId = 10014
},
[50241] = {
id = 50241,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
1
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
720856
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
taskGroupId = 10014
},
[50242] = {
id = 50242,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
2
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
999999
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
taskGroupId = 10014
},
[50243] = {
id = 50243,
desc = "无双元梦星",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 44,
value = 1,
subConditionList = {
{
type = 32,
value = {
8,
3
}
},
{
type = 154,
value = {
13
}
}
}
}
}
}
},
reward = {
itemIdList = {
720857
},
numList = {
1
},
expireTimestamps = {
{
seconds = 1750953599
}
}
},
taskGroupId = 10014
},
[70121] = {
id = 70121,
name = "每日完成2次经典或娱乐模式",
desc = "每日完成2次经典或娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11081
},
[70122] = {
id = 70122,
name = "每日活跃度达到20",
desc = "每日活跃度达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
1002
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 35,
taskGroupId = 11081
},
[70123] = {
id = 70123,
name = "每日完成2次经典或娱乐模式",
desc = "每日完成2次经典或娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11082
},
[70124] = {
id = 70124,
name = "每日活跃度达到20",
desc = "每日活跃度达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
1002
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 35,
taskGroupId = 11082
},
[70125] = {
id = 70125,
name = "每日完成2次经典或娱乐模式",
desc = "每日完成2次经典或娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11083
},
[70126] = {
id = 70126,
name = "每日活跃度达到20",
desc = "每日活跃度达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
1002
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 35,
taskGroupId = 11083
},
[70127] = {
id = 70127,
name = "每日完成2次经典或娱乐模式",
desc = "每日完成2次经典或娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11084
},
[70128] = {
id = 70128,
name = "每日活跃度达到20",
desc = "每日活跃度达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
1002
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 35,
taskGroupId = 11084
},
[70129] = {
id = 70129,
name = "每日完成2次经典或娱乐模式",
desc = "每日完成2次经典或娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11085
},
[70130] = {
id = 70130,
name = "每日活跃度达到20",
desc = "每日活跃度达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
1002
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 35,
taskGroupId = 11085
},
[70131] = {
id = 70131,
name = "每日完成2次经典或娱乐模式",
desc = "每日完成2次经典或娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11086
},
[70132] = {
id = 70132,
name = "每日活跃度达到20",
desc = "每日活跃度达到20",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 6,
value = 20,
subConditionList = {
{
type = 3,
value = {
1002
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
5
},
validPeriodList = {
0
}
},
jumpId = 35,
taskGroupId = 11086
},
[70133] = {
id = 70133,
name = "本阶段完成5次娱乐模式",
desc = "本阶段完成5次娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 127,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11087
},
[70134] = {
id = 70134,
name = "本阶段与好友一起完成2场经典/娱乐模式",
desc = "本阶段与好友一起完成2场经典/娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
},
{
type = 87,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11087
},
[70135] = {
id = 70135,
name = "本阶段与好友一起完成2场经典/娱乐模式",
desc = "本阶段与好友一起完成2场经典/娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
},
{
type = 87,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11088
},
[70136] = {
id = 70136,
name = "本阶段在经典/娱乐模式中通过5个关卡",
desc = "本阶段在经典/娱乐模式中通过5个关卡",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 5,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11088
},
[70137] = {
id = 70137,
name = "本阶段在经典/娱乐模式中通过5个关卡",
desc = "本阶段在经典/娱乐模式中通过5个关卡",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 5,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11089
},
[70138] = {
id = 70138,
name = "本阶段游玩1次经典模式中的决胜关",
desc = "本阶段游玩1次经典模式中的决胜关",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 2,
value = {
1,
2,
3,
4,
5,
6,
7,
8,
9
}
},
{
type = 14,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11089
},
[70139] = {
id = 70139,
name = "本阶段完成5次娱乐模式",
desc = "本阶段完成5次娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 127,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11090
},
[70140] = {
id = 70140,
name = "本阶段在经典/娱乐模式中通过5个关卡",
desc = "本阶段在经典/娱乐模式中通过5个关卡",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 5,
subConditionList = {
{
type = 127,
value = {
1,
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11090
},
[70141] = {
id = 70141,
name = "本阶段与好友一起完成2场经典/娱乐模式",
desc = "本阶段与好友一起完成2场经典/娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 2,
subConditionList = {
{
type = 127,
value = {
1,
3
}
},
{
type = 87,
value = {
1
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11091
},
[70142] = {
id = 70142,
name = "本阶段游玩1次经典模式中的决胜关",
desc = "本阶段游玩1次经典模式中的决胜关",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 2,
value = {
1,
2,
3,
4,
5,
6,
7,
8,
9
}
},
{
type = 14,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11091
},
[70143] = {
id = 70143,
name = "本阶段完成5次娱乐模式",
desc = "本阶段完成5次娱乐模式",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 4,
value = 5,
subConditionList = {
{
type = 127,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11092
},
[70144] = {
id = 70144,
name = "本阶段游玩1次经典模式中的决胜关",
desc = "本阶段游玩1次经典模式中的决胜关",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 29,
value = 1,
subConditionList = {
{
type = 2,
value = {
1,
2,
3,
4,
5,
6,
7,
8,
9
}
},
{
type = 14,
value = {
3
}
}
}
}
}
}
},
reward = {
itemIdList = {
7
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 11092
}
}

local mt = {
name = "达到指定段位",
jumpId = 4
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data