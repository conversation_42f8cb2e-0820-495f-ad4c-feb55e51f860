--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/C_充值配置表.xlsx: 特惠礼包

local v0 = 158

local v1 = 157

local v2 = "购买后将获得礼包内商品"

local v3 = 6

local v4 = 30

local v5 = 1

local v6 = "T_CutGift_Icon_pack_10"

local v7 = 0

local v8 = 4

local v9 = "1.2.100.51"

local v10 = 2

local v11 = "Package0326.astc"

local v12 = "T_CutGift_bg_Gold.astc"

local v13 = "T_CutGift_bg_Bee.astc"

local v14 = "T_CutGift_bg_MOBA.astc"

local v15 = 20003

local data = {
[120001] = {
commodityId = 120001,
commodityName = "每日免费领取道具",
coinType = 7,
price = 0,
limitType = "MCL_DailyLimit",
shopTag = {
1
},
showRedPoint = 1,
itemIds = {
4
},
itemNums = {
200
},
packageDec = v2
},
[120003] = {
commodityId = 120003,
commodityName = "龟蜜随机礼盒",
price = 3,
discountPrice = 3,
limitNum = 4,
beginTime = {
seconds = 1705075200
},
endTime = {
seconds = 1706198399
},
shopSort = 1,
canDirectBuy = true,
imgUrl = "T_CutGift_Icon_Pack_Bee",
itemIds = {
320028
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 3,
cutGiftStyle = 1
},
[120002] = {
commodityId = 120002,
commodityName = "马赛克眼镜礼盒",
price = 18,
discountPrice = 6,
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1708617599
},
shopSort = 1,
minVersion = "1.2.67.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
610077
},
itemNums = {
60,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 1,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120010] = {
commodityId = 120010,
commodityName = "赛季专享",
price = 36,
discountPrice = 6,
limitType = "MCL_SeasonLimit",
shopSort = 15,
canDirectBuy = true,
imgUrl = "T_CutGift_Icon_pack_04",
itemIds = {
1,
2
},
itemNums = {
60,
30
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 1,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
syncDB = true
},
[120011] = {
commodityId = 120011,
commodityName = "赛季专享",
price = 42,
discountPrice = 12,
limitType = "MCL_SeasonLimit",
shopSort = 16,
canDirectBuy = true,
imgUrl = "T_CutGift_Icon_pack_04",
itemIds = {
1,
2
},
itemNums = {
120,
30
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 1,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
syncDB = true
},
[120012] = {
commodityId = 120012,
commodityName = "每日星愿礼包",
price = 30,
discountPrice = 10,
limitType = "MCL_DailyLimit",
endTime = {
seconds = 1715097599
},
shopSort = 1,
imgUrl = "T_CutGift_Icon_pack_07",
itemIds = {
320008
},
packageDec = v2,
canAccumulate = true,
accumulateMax = 7,
cutGiftColor = 0,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1
},
[120013] = {
commodityId = 120013,
commodityName = "每日云朵币礼包",
price = 60,
discountPrice = 30,
limitType = "MCL_DailyLimit",
shopSort = 4,
imgUrl = "T_CutGift_Icon_pack_01",
itemIds = {
320009
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1
},
[120014] = {
commodityId = 120014,
commodityName = "云朵币周礼包",
price = 200,
discountPrice = 60,
limitType = "MCL_WeeklyLimit",
shopSort = 5,
imgUrl = "T_CutGift_Icon_pack_03",
itemIds = {
6,
200016
},
itemNums = {
150,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120015] = {
commodityId = 120015,
commodityName = "每日一元礼包",
price = 3,
discountPrice = 1,
limitType = "MCL_DailyLimit",
shopSort = 6,
canDirectBuy = true,
imgUrl = "T_CutGift_Icon_pack_09",
itemIds = {
1,
6
},
itemNums = {
10,
20
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
syncDB = true
},
[120016] = {
commodityId = 120016,
commodityName = "每日三元礼包",
price = 6,
discountPrice = 3,
limitType = "MCL_DailyLimit",
shopSort = 7,
canDirectBuy = true,
imgUrl = "T_CutGift_Icon_pack_09",
itemIds = {
1,
6
},
itemNums = {
30,
30
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
syncDB = true
},
[119995] = {
commodityId = 119995,
commodityName = "春节限定幸运币礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708271999
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
60,
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 2,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[119996] = {
commodityId = 119996,
commodityName = "春节限定幸运币礼包",
price = 45,
discountPrice = 30,
beginTime = {
seconds = 1707408001
},
endTime = {
seconds = 1708271999
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
300,
15
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 2,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[119997] = {
commodityId = 119997,
commodityName = "春节限定幸运币礼包",
price = 160,
discountPrice = 128,
beginTime = {
seconds = 1707408002
},
endTime = {
seconds = 1708271999
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
1280,
32
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 2,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[119998] = {
commodityId = 119998,
commodityName = "神秘的笑礼包",
price = 5,
discountPrice = 1,
beginTime = {
seconds = 1706803200
},
endTime = {
seconds = 1707407999
},
shopSort = 1,
minVersion = "1.2.67.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
830024,
320031
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 0
},
[119994] = {
commodityId = 119994,
commodityName = "奶龙表情随机礼包",
price = 100,
discountPrice = 20,
limitNum = 25,
beginTime = {
seconds = 1708012800
},
endTime = {
seconds = 1709222399
},
shopSort = 1,
minVersion = "1.2.80.10",
imgUrl = "T_CutGift_Icon_pack_12",
itemIds = {
321006
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 1,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1
},
[119992] = {
commodityId = 119992,
commodityName = "汤圆宝宝随机礼盒",
price = 2,
discountPrice = 1,
limitNum = 4,
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1709222399
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = "T_CutGift_Icon_Pack_RiceBallRandom",
itemIds = {
320036
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 1,
cutGiftStyle = 1
},
[119991] = {
commodityId = 119991,
commodityName = "中国结礼盒",
price = 18,
discountPrice = 6,
beginTime = {
seconds = 1708617600
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
620110
},
itemNums = {
60,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 1,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[119990] = {
commodityId = 119990,
commodityName = "蕉绿绿随机礼盒",
price = 3,
discountPrice = 3,
limitNum = 4,
beginTime = {
seconds = 1709308800
},
endTime = {
seconds = 1710691199
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320032
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftCenterImgUrlCdn = "jiaolvlv.astc"
},
[120018] = {
commodityId = 120018,
commodityName = "功夫熊猫随机礼盒",
price = 100,
discountPrice = 20,
limitNum = 15,
beginTime = {
seconds = 1711036800
},
endTime = {
seconds = 1712246399
},
shopSort = 1,
minVersion = "1.2.80.10",
imgUrl = "T_CutGift_Icon_pack_12",
itemIds = {
321009
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 0,
cutGiftCenterImgUrlCdn = "KongfuPanda.astc"
},
[120019] = {
commodityId = 120019,
commodityName = "超值幸运币礼包",
price = 6,
discountPrice = 1,
shopSort = 14,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120020] = {
commodityId = 120020,
commodityName = "悟小能",
price = 400,
discountPrice = 120,
shopSort = 12,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
401750
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "bajie.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120021] = {
commodityId = 120021,
commodityName = "幸运币精品礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1712851199
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
60,
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120022] = {
commodityId = 120022,
commodityName = "幸运币豪华礼包",
price = 45,
discountPrice = 30,
beginTime = {
seconds = 1712160000
},
endTime = {
seconds = 1712851199
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
300,
15
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120023] = {
commodityId = 120023,
commodityName = "幸运币精品礼包",
discountPrice = 6,
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
60,
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120024] = {
commodityId = 120024,
commodityName = "幸运币豪华礼包",
price = 45,
discountPrice = 30,
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
300,
15
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120025] = {
commodityId = 120025,
commodityName = "幸运币特典礼包",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1713456000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
minVersion = "1.2.80.10",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
680,
30
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120026] = {
commodityId = 120026,
commodityName = "假日回馈礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715097599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120027] = {
commodityId = 120027,
commodityName = "假日精品礼包",
price = 42,
discountPrice = 30,
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715097599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120028] = {
commodityId = 120028,
commodityName = "假日豪华礼包",
price = 120,
discountPrice = 98,
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715097599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
120
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120029] = {
commodityId = 120029,
commodityName = "假日特典礼包",
price = 240,
discountPrice = 198,
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715097599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
240
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120030] = {
commodityId = 120030,
commodityName = "假日特典礼包",
price = 750,
discountPrice = 648,
beginTime = {
seconds = 1714492800
},
endTime = {
seconds = 1715097599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
750
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120031] = {
commodityId = 120031,
commodityName = "极速飞车模式专属",
price = 680,
discountPrice = 600,
beginTime = {
seconds = 1715356800
},
endTime = {
seconds = 1725551999
},
shopSort = 1,
minVersion = v9,
imgUrl = v6,
itemIds = {
222011
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_xiaoha.astc",
cutGiftNormalBgImgUrlCdn = v12,
pakGroup = 20031
},
[120032] = {
commodityId = 120032,
commodityName = "绿植花洒",
discountPrice = 6,
beginTime = {
seconds = 1715356800
},
shopSort = 11,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
640002
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_huasa.astc",
cutGiftNormalBgImgUrlCdn = v13,
syncDB = true
},
[120033] = {
commodityId = 120033,
commodityName = "通行证助力宝箱",
price = 15,
discountPrice = 1,
beginTime = {
seconds = 1715270400
},
endTime = {
seconds = 1715529599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1005
},
itemNums = {
300
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_tongxingzheng.astc",
cutGiftNormalBgImgUrlCdn = "T_CutGift_bg_Blue.astc"
},
[120034] = {
commodityId = 120034,
commodityName = "永恒之心礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1716479999
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120035] = {
commodityId = 120035,
commodityName = "永恒之愿礼包",
price = 42,
discountPrice = 30,
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1716479999
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120036] = {
commodityId = 120036,
commodityName = "永恒之约礼包",
price = 120,
discountPrice = 98,
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1716479999
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
120
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120037] = {
commodityId = 120037,
commodityName = "永恒之誓礼包",
price = 240,
discountPrice = 198,
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1716479999
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
240
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120038] = {
commodityId = 120038,
commodityName = "蔬菜筐筐",
discountPrice = 6,
beginTime = {
seconds = 1716480000
},
endTime = {
seconds = 1717084799
},
shopSort = 1,
minVersion = "1.2.100.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
620276
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_cailan.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120042] = {
commodityId = 120042,
commodityName = "鸭梨宝背随机礼盒",
price = 6,
discountPrice = 3,
limitNum = 4,
beginTime = {
seconds = 1716825600
},
endTime = {
seconds = 1718035199
},
shopSort = 1,
minVersion = "1.2.100.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320051
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_Pack_Pear2.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120039] = {
commodityId = 120039,
commodityName = "缤纷限定礼包",
discountPrice = 6,
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
60,
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120040] = {
commodityId = 120040,
commodityName = "缤纷假日礼包",
price = 18,
discountPrice = 12,
beginTime = {
seconds = 1717171200
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
3
},
itemNums = {
120,
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1
},
[120041] = {
commodityId = 120041,
commodityName = "欢乐假日随机礼",
price = 100,
discountPrice = 20,
limitNum = 25,
beginTime = {
seconds = 1717430400
},
endTime = {
seconds = 1718639999
},
shopSort = 8,
minVersion = "1.2.100.1",
imgUrl = v6,
itemIds = {
320050
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_xiaowanzi2.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120044] = {
commodityId = 120044,
commodityName = "夏日海洋动作礼盒",
price = 750,
discountPrice = 180,
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1718899199
},
shopSort = 9,
minVersion = "1.3.7.1",
imgUrl = v6,
itemIds = {
720637,
720638,
720639
},
itemNums = {
1,
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_Pack_dongzuo.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120045] = {
commodityId = 120045,
commodityName = "功夫巨星礼包",
price = 20,
discountPrice = 8,
beginTime = {
seconds = 1717776000
},
endTime = {
seconds = 1718985599
},
shopSort = 9,
minVersion = "1.2.100.99",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
510127,
830077
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_Pack_gongfu.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120046] = {
commodityId = 120046,
commodityName = "剃刀礼包",
price = 680,
discountPrice = 600,
beginTime = {
seconds = 1717862400
},
endTime = {
seconds = 1719158399
},
shopSort = 2,
minVersion = "1.3.7.1",
imgUrl = v6,
itemIds = {
222021
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_Pack_tidao.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20031
},
[120047] = {
commodityId = 120047,
commodityName = "国潮大黄蜂礼包",
price = 680,
discountPrice = 600,
beginTime = {
seconds = 1717862400
},
endTime = {
seconds = 1719158399
},
shopSort = 3,
minVersion = "1.3.7.1",
imgUrl = v6,
itemIds = {
222041
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_Pack_huangfeng.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20031
},
[120048] = {
commodityId = 120048,
commodityName = "每日星愿礼包",
price = 30,
discountPrice = 10,
limitType = "MCL_DailyLimit",
shopSort = 13,
imgUrl = "T_CutGift_Icon_pack_07",
itemIds = {
320008
},
packageDec = v2,
canAccumulate = true,
accumulateMax = 7,
cutGiftColor = 0,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1
},
[120049] = {
commodityId = 120049,
commodityName = "天鹅界面主题礼包",
price = 703,
discountPrice = 128,
beginTime = {
seconds = 1718380800
},
endTime = {
seconds = 1720367999
},
shopSort = 17,
minVersion = "1.3.7.21",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
845002,
3,
721017
},
itemNums = {
1,
30,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_Pack_tiane.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120050] = {
commodityId = 120050,
commodityName = "战神之路礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1719763199
},
shopSort = 24,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120051] = {
commodityId = 120051,
commodityName = "战神颂歌礼包",
price = 42,
discountPrice = 30,
limitNum = 2,
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1719763199
},
shopSort = 23,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120052] = {
commodityId = 120052,
commodityName = "辉耀之光礼包",
price = 120,
discountPrice = 98,
beginTime = {
seconds = 1716220800
},
endTime = {
seconds = 1717084799
},
shopSort = 1,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
120
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120053] = {
commodityId = 120053,
commodityName = "烈焰之心礼包",
price = 240,
discountPrice = 198,
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1719763199
},
shopSort = 22,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
240
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120054] = {
commodityId = 120054,
commodityName = "剑启黎明礼包",
price = 750,
discountPrice = 648,
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1719763199
},
shopSort = 21,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
750
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120055] = {
commodityId = 120055,
commodityName = "狼人专享卡包",
price = 30,
discountPrice = 20,
limitType = "MCL_DailyLimit",
limitNum = 2,
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1726156799
},
shopSort = 12,
minVersion = "1.3.7.1",
imgUrl = v6,
itemIds = {
320054
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren1.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120056] = {
commodityId = 120056,
commodityName = "狼人身份卡包",
price = 40,
discountPrice = 30,
limitType = "MCL_DailyLimit",
limitNum = 2,
beginTime = {
seconds = 1724083200
},
endTime = {
seconds = 1726156799
},
shopSort = 11,
minVersion = "1.3.7.1",
imgUrl = v6,
itemIds = {
320055
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren3.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120057] = {
commodityId = 120057,
commodityName = "谁是狼人阵营礼包",
price = 20,
discountPrice = 15,
limitType = "MCL_DailyLimit",
beginTime = {
seconds = 1718899200
},
endTime = {
seconds = 1721231999
},
shopSort = 10,
minVersion = "1.3.7.1",
imgUrl = v6,
itemIds = {
320056
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren2.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120058] = {
commodityId = 120058,
commodityName = "星空猫舱随机礼包",
price = 6,
discountPrice = 3,
limitNum = 6,
beginTime = {
seconds = 1719244800
},
endTime = {
seconds = 1720454399
},
shopSort = 25,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320058
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_baimao.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120059] = {
commodityId = 120059,
commodityName = "裂隙守护随机礼盒",
discountPrice = 6,
limitNum = 4,
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1720713599
},
shopSort = 26,
minVersion = "1.3.7.75",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320059
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_shouren.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120060] = {
commodityId = 120060,
commodityName = "半周年回馈礼包",
discountPrice = 6,
limitNum = 5,
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1721318399
},
shopSort = 31,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120061] = {
commodityId = 120061,
commodityName = "半周年精品礼包",
price = 60,
discountPrice = 30,
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1721318399
},
shopSort = 30,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
60
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120062] = {
commodityId = 120062,
commodityName = "半周年豪华礼包",
price = 160,
discountPrice = 128,
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1727107199
},
shopSort = 29,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
160
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120063] = {
commodityId = 120063,
commodityName = "半周年特典礼包",
price = 380,
discountPrice = 328,
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1721318399
},
shopSort = 28,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
380
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120064] = {
commodityId = 120064,
commodityName = "半周年特惠礼包",
price = 750,
discountPrice = 648,
beginTime = {
seconds = 1720108800
},
endTime = {
seconds = 1721318399
},
shopSort = 27,
minVersion = "1.3.7.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
750
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120065] = {
commodityId = 120065,
commodityName = "庆典主题礼包",
price = 703,
discountPrice = 128,
beginTime = {
seconds = 1720195200
},
endTime = {
seconds = 1720799999
},
shopSort = 32,
minVersion = "1.3.7.106",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
845004,
721016,
3
},
itemNums = {
1,
1,
30
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_Pack_kt.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120066] = {
commodityId = 120066,
commodityName = "通行证助力宝箱",
price = 15,
discountPrice = 1,
beginTime = {
seconds = 1720972800
},
endTime = {
seconds = 1721231999
},
shopSort = 33,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1005
},
itemNums = {
300
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_tongxingzheng.astc",
cutGiftNormalBgImgUrlCdn = "T_CutGift_bg_Blue.astc"
},
[120067] = {
commodityId = 120067,
commodityName = "甜心小K礼包",
price = 680,
discountPrice = 600,
beginTime = {
seconds = 1721059200
},
endTime = {
seconds = 1722182399
},
shopSort = 34,
minVersion = "1.3.7.122",
imgUrl = v6,
itemIds = {
222071
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Kitty.astc",
cutGiftNormalBgImgUrlCdn = v12,
pakGroup = 20031
},
[120068] = {
commodityId = 120068,
commodityName = "月卡组合特惠礼盒",
price = 420,
discountPrice = 300,
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1721923199
},
shopSort = 59,
minVersion = "1.3.12.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
210000,
200103
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_yueka.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120069] = {
commodityId = 120069,
commodityName = "周二1元幸运礼包",
price = 30,
discountPrice = 10,
beginTime = {
seconds = 1721664000
},
endTime = {
seconds = 1722268799
},
shopSort = 60,
minVersion = "1.3.12.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320105
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120070] = {
commodityId = 120070,
commodityName = "毛球旅行家礼包",
price = 240,
discountPrice = 120,
beginTime = {
seconds = 1722268800
},
endTime = {
seconds = 1723478399
},
shopSort = 37,
minVersion = "1.3.7.126",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
620335
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_maoqiu.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120071] = {
commodityId = 120071,
commodityName = "奶盒盒礼包",
price = 360,
discountPrice = 180,
beginTime = {
seconds = 1722268800
},
endTime = {
seconds = 1723478399
},
shopSort = 38,
minVersion = "1.3.7.126",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
401830
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_naihehe.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120072] = {
commodityId = 120072,
commodityName = "组合礼包",
price = 600,
discountPrice = 240,
beginTime = {
seconds = 1722268800
},
endTime = {
seconds = 1723478399
},
shopSort = 39,
minVersion = "1.3.7.126",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
401830,
620335
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_zuhe.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120073] = {
commodityId = 120073,
commodityName = "何日见许礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1724342399
},
shopSort = 64,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true,
recommendType = 2
},
[120074] = {
commodityId = 120074,
commodityName = "将琴代语礼包",
price = 42,
discountPrice = 30,
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1724342399
},
shopSort = 63,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true,
recommendType = 2
},
[120075] = {
commodityId = 120075,
commodityName = "凤飞翱翔礼包",
price = 120,
discountPrice = 98,
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1724342399
},
shopSort = 62,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
120
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true,
recommendType = 2
},
[120076] = {
commodityId = 120076,
commodityName = "四海求凰礼包",
price = 240,
discountPrice = 198,
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1724342399
},
shopSort = 61,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemNums = {
240
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true
},
[120077] = {
commodityId = 120077,
commodityName = "狼人七夕礼包",
price = 87,
discountPrice = 66,
limitType = "MCL_DailyLimit",
limitNum = 10,
beginTime = {
seconds = 1723132800
},
endTime = {
seconds = 1723737599
},
shopSort = 60,
minVersion = "1.3.12.70",
imgUrl = v6,
itemIds = {
240407,
240408,
240404
},
itemNums = {
4,
6,
5
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_Common_Item_System_Qixi.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120078] = {
commodityId = 120078,
commodityName = "活力球拍礼包",
price = 180,
discountPrice = 120,
beginTime = {
seconds = 1723392000
},
endTime = {
seconds = 1724601599
},
shopSort = 60,
minVersion = "1.3.12.70",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
640023
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_qiupai.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120079] = {
commodityId = 120079,
commodityName = "鹊意浓浓礼包",
price = 300,
discountPrice = 60,
beginTime = {
seconds = 1723824000
},
endTime = {
seconds = 1724601599
},
shopSort = 65,
minVersion = "1.3.12.100",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
711058,
711059,
711060
},
itemNums = {
1,
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_xique.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120080] = {
commodityId = 120080,
commodityName = "甜心光波动作礼包",
price = 250,
discountPrice = 60,
beginTime = {
seconds = 1724342400
},
endTime = {
seconds = 1724947199
},
shopSort = 70,
minVersion = "1.3.12.100",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
720678
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_guangbo.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120081] = {
commodityId = 120081,
commodityName = "狼人新福袋1",
price = 25,
discountPrice = 15,
limitNum = 10,
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1726415999
},
shopSort = 13,
minVersion = "1.3.18.23",
imgUrl = "T_Common_Item_System_Werewolf_01",
itemIds = {
320068
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120082] = {
commodityId = 120082,
commodityName = "狼人新福袋2",
price = 25,
discountPrice = 15,
limitNum = 10,
beginTime = {
seconds = 1725552000
},
endTime = {
seconds = 1726415999
},
shopSort = 13,
minVersion = "1.3.18.23",
imgUrl = "T_Common_Item_System_Werewolf_02",
itemIds = {
320069
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120083] = {
commodityId = 120083,
commodityName = "手持武器：掠火枪",
price = 30,
discountPrice = 18,
beginTime = {
seconds = 1726416000
},
endTime = {
seconds = 1727625599
},
shopSort = 73,
minVersion = "1.3.18.37",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
640047
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_yyq.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120085] = {
commodityId = 120085,
commodityName = "lulu猪表情礼包",
price = 180,
discountPrice = 60,
beginTime = {
seconds = 1726761600
},
endTime = {
seconds = 1728575999
},
shopSort = 65,
minVersion = "1.3.18.56",
imgUrl = v6,
itemIds = {
711053,
711054,
711057
},
itemNums = {
1,
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_luluzhu.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120096] = {
commodityId = 120096,
commodityName = "狼人欢庆礼包1",
price = 25,
discountPrice = 15,
limitNum = 15,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728489599
},
shopSort = 69,
minVersion = "1.3.18.55",
imgUrl = v6,
itemIds = {
320070
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren4.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120097] = {
commodityId = 120097,
commodityName = "狼人欢庆礼包2",
price = 25,
discountPrice = 15,
limitNum = 15,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728489599
},
shopSort = 68,
minVersion = "1.3.18.55",
imgUrl = v6,
itemIds = {
320071
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren5.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120088] = {
commodityId = 120088,
commodityName = "染青烟礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728835199
},
shopSort = 74,
minVersion = "1.3.18.73",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true
},
[120089] = {
commodityId = 120089,
commodityName = "绘长歌礼包",
price = 160,
discountPrice = 128,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728835199
},
shopSort = 73,
minVersion = "1.3.18.73",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
160
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true
},
[120090] = {
commodityId = 120090,
commodityName = "见龙吟礼包",
price = 380,
discountPrice = 328,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728835199
},
shopSort = 72,
minVersion = "1.3.18.73",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
380
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true
},
[120091] = {
commodityId = 120091,
commodityName = "承千都礼包",
price = 750,
discountPrice = 648,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728835199
},
shopSort = 71,
minVersion = "1.3.18.73",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
750
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true
},
[120092] = {
commodityId = 120092,
commodityName = "月卡组合特惠礼盒",
price = 420,
discountPrice = 300,
beginTime = {
seconds = 1727712000
},
endTime = {
seconds = 1728835199
},
shopSort = 75,
minVersion = "1.3.18.73",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
210000,
200103
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_yueka.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120094] = {
commodityId = 120094,
commodityName = "通行证助力宝箱",
price = 15,
discountPrice = 1,
beginTime = {
seconds = 1728230400
},
endTime = {
seconds = 1728835199
},
shopSort = 76,
minVersion = "1.3.18.73",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1005
},
itemNums = {
300
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_tongxingzheng.astc",
cutGiftNormalBgImgUrlCdn = "T_CutGift_bg_Blue.astc"
},
[120095] = {
commodityId = 120095,
commodityName = "乾坤丹炉随机礼包",
price = 6,
discountPrice = 3,
limitNum = 5,
beginTime = {
seconds = 1728230400
},
endTime = {
seconds = 1728835199
},
shopSort = 77,
minVersion = "1.3.18.73",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
322003
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_danlu_2.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120099] = {
mallId = 158,
commodityId = 120099,
commodityName = "王者之剑",
price = 30,
discountPrice = 18,
beginTime = {
seconds = 1728576000
},
endTime = {
seconds = 1729785599
},
shopSort = 78,
minVersion = "1.3.18.37",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
640062
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_wzj.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120100] = {
mallId = 158,
commodityId = 120100,
commodityName = "湮灭之锁",
price = 30,
discountPrice = 18,
beginTime = {
seconds = 1730217600
},
endTime = {
seconds = 1731859199
},
shopSort = 78,
minVersion = "1.3.26.23",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
640052
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_zk.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120101] = {
mallId = 157,
commodityId = 120101,
commodityName = "星灿灿",
price = 40,
discountPrice = 18,
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
shopSort = 1,
minVersion = "1.3.26.61",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
402640
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Xcc.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120102] = {
mallId = 157,
commodityId = 120102,
commodityName = "月缘缘",
price = 40,
discountPrice = 18,
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
shopSort = 1,
minVersion = "1.3.26.61",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
402650
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Yyy.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120103] = {
mallId = 157,
commodityId = 120103,
commodityName = "星月组合礼包",
price = 120,
discountPrice = 36,
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
shopSort = 1,
minVersion = "1.3.26.61",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
402640,
402650,
620216
},
itemNums = {
1,
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "XccYyyXyzl.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120104] = {
commodityId = 120104,
commodityName = "峡谷精品礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1731254399
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120113] = {
commodityId = 120113,
commodityName = "峡谷特典礼包",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1715875200
},
endTime = {
seconds = 1731254399
},
shopSort = 1,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
98
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120106] = {
mallId = 158,
commodityId = 120106,
commodityName = "狼人派对赛季礼1",
price = 110,
discountPrice = 75,
beginTime = {
seconds = 1728489600
},
endTime = {
seconds = 1730649599
},
shopSort = 69,
minVersion = "1.3.26.10",
itemIds = {
310263
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "paidui1.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120107] = {
mallId = 158,
commodityId = 120107,
commodityName = "狼人派对赛季礼2",
price = 150,
discountPrice = 98,
beginTime = {
seconds = 1728489600
},
endTime = {
seconds = 1730649599
},
shopSort = 68,
minVersion = "1.3.26.10",
itemIds = {
310264
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "paidui2.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120110] = {
mallId = 157,
commodityId = 120110,
commodityName = "青涩告白礼包",
discountPrice = 6,
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730995199
},
shopSort = 81,
minVersion = "1.3.26.23",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true,
recommendType = 2
},
[120111] = {
mallId = 157,
commodityId = 120111,
commodityName = "甜蜜告白礼包",
price = 18,
discountPrice = 12,
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730995199
},
shopSort = 80,
minVersion = "1.3.26.23",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
18
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true,
recommendType = 2
},
[120112] = {
mallId = 157,
commodityId = 120112,
commodityName = "梦幻告白礼包",
price = 42,
discountPrice = 30,
beginTime = {
seconds = 1729785600
},
endTime = {
seconds = 1730995199
},
shopSort = 79,
minVersion = "1.3.26.23",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
syncDB = true,
recommendType = 2
},
[120114] = {
mallId = 157,
commodityId = 120114,
commodityName = "开心超人表情礼包",
price = 300,
discountPrice = 60,
beginTime = {
seconds = 1730390400
},
endTime = {
seconds = 1730995199
},
shopSort = 1,
minVersion = "1.3.26.61",
imgUrl = v6,
itemIds = {
711233,
711234,
711235
},
itemNums = {
1,
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_kxcrbq.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120115] = {
mallId = 157,
commodityId = 120115,
commodityName = "返场时装随机礼包",
price = 8,
discountPrice = 3,
limitNum = 12,
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopSort = 1,
minVersion = "1.3.26.61",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320077
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_sjsz1.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120116] = {
mallId = 157,
commodityId = 120116,
commodityName = "返场饰品随机礼包",
price = 8,
discountPrice = 3,
limitNum = 4,
beginTime = {
seconds = 1730736000
},
endTime = {
seconds = 1731340799
},
shopSort = 1,
minVersion = "1.3.26.61",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320078
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_sjsp1.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120117] = {
mallId = 157,
commodityId = 120117,
commodityName = "界面主题自选礼包",
price = 703,
discountPrice = 128,
limitNum = 4,
beginTime = {
seconds = 1731772800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
minVersion = "1.3.26.93",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
3,
330090,
330091
},
itemNums = {
30,
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_ztfczx.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120118] = {
mallId = 157,
commodityId = 120118,
commodityName = "巴啦啦头像礼包",
price = 18,
discountPrice = 6,
beginTime = {
seconds = 1731686400
},
endTime = {
seconds = 1732463999
},
shopSort = 1,
minVersion = "1.3.26.93",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1,
860131,
860132
},
itemNums = {
30,
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_blltx.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120136] = {
mallId = 158,
commodityId = 120136,
commodityName = "绿色星运宝箱",
price = 20,
discountPrice = 10,
limitType = "MCL_WeeklyLimit",
beginTime = {
seconds = 1731600000
},
shopSort = 93,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
305001
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_1.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021,
recommendType = 3
},
[120137] = {
mallId = 158,
commodityId = 120137,
commodityName = "蓝色星运宝箱",
price = 40,
discountPrice = 20,
limitType = "MCL_WeeklyLimit",
beginTime = {
seconds = 1731600000
},
shopSort = 92,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
305002
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_2.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021,
recommendType = 3
},
[120138] = {
mallId = 158,
commodityId = 120138,
commodityName = "紫色星运宝箱",
price = 200,
discountPrice = 120,
limitType = "MCL_WeeklyLimit",
beginTime = {
seconds = 1731600000
},
shopSort = 91,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
305003
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_3.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021,
recommendType = 3
},
[120139] = {
mallId = 158,
commodityId = 120139,
commodityName = "COC资源礼包测试",
price = 980,
discountPrice = 680,
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1735660799
},
shopSort = 90,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
310671
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021
},
[120140] = {
mallId = 158,
commodityId = 120140,
commodityName = "限定橙色宝箱",
price = 980,
discountPrice = 680,
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732809599
},
shopSort = 90,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
300302
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021
},
[120135] = {
mallId = 158,
commodityId = 120135,
commodityName = "禁止挂机",
price = 30,
discountPrice = 18,
beginTime = {
seconds = 1732204800
},
shopSort = 98,
minVersion = "1.3.18.37",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
640074
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_jzgj_1.astc",
cutGiftNormalBgImgUrlCdn = v14,
recommendType = 3
},
[120134] = {
mallId = 158,
commodityId = 120134,
commodityName = "限定橙色宝箱",
price = 980,
discountPrice = 680,
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1733414399
},
shopSort = 97,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
300303
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021
},
[120150] = {
mallId = 158,
commodityId = 120150,
commodityName = "狼人补给礼包1",
price = 25,
discountPrice = 15,
limitNum = 30,
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732809599
},
shopSort = 95,
minVersion = "1.3.26.100",
imgUrl = v6,
itemIds = {
320092
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/bj001.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120151] = {
mallId = 158,
commodityId = 120151,
commodityName = "狼人补给礼包2",
price = 25,
discountPrice = 15,
limitNum = 30,
beginTime = {
seconds = 1732204800
},
endTime = {
seconds = 1732809599
},
shopSort = 96,
minVersion = "1.3.26.100",
imgUrl = v6,
itemIds = {
320093
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/bj002.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120086] = {
mallId = 158,
commodityId = 120086,
commodityName = "狼人专享卡包",
price = 30,
discountPrice = 20,
limitType = "MCL_DailyLimit",
limitNum = 3,
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1733673599
},
shopSort = 60,
minVersion = "1.3.26.100",
imgUrl = v6,
itemIds = {
320054
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren1.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120087] = {
mallId = 158,
commodityId = 120087,
commodityName = "狼人身份卡包",
price = 40,
discountPrice = 30,
limitType = "MCL_DailyLimit",
limitNum = 3,
beginTime = {
seconds = 1732723200
},
endTime = {
seconds = 1733673599
},
shopSort = 61,
minVersion = "1.3.26.100",
imgUrl = v6,
itemIds = {
320055
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren3.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120152] = {
mallId = 158,
commodityId = 120152,
commodityName = "峡谷精品礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1734105600
},
endTime = {
seconds = 1734883199
},
shopSort = 101,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120153] = {
mallId = 158,
commodityId = 120153,
commodityName = "峡谷特典礼包",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1734105600
},
endTime = {
seconds = 1734883199
},
shopSort = 100,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
98
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120154] = {
commodityId = 120154,
commodityName = "元气满满礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1702483200
},
endTime = {
seconds = 1704038399
},
shopSort = 103,
minVersion = "1.3.37.26",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120155] = {
commodityId = 120155,
commodityName = "梦想成真礼包",
price = 42,
discountPrice = 30,
limitNum = 2,
beginTime = {
seconds = 1702483200
},
endTime = {
seconds = 1704038399
},
shopSort = 102,
minVersion = "1.3.37.26",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120156] = {
commodityId = 120156,
commodityName = "之愿皆成礼包",
price = 85,
discountPrice = 68,
beginTime = {
seconds = 1702483200
},
endTime = {
seconds = 1704038399
},
shopSort = 101,
minVersion = "1.3.37.26",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
85
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120157] = {
commodityId = 120157,
commodityName = "星光璀璨礼包",
price = 240,
discountPrice = 198,
beginTime = {
seconds = 1702483200
},
endTime = {
seconds = 1704038399
},
shopSort = 100,
minVersion = "1.3.37.26",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
240
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11
},
[120133] = {
mallId = 158,
commodityId = 120133,
commodityName = "限定橙色宝箱",
price = 980,
discountPrice = 680,
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1734623999
},
shopSort = 97,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
300304
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021
},
[120159] = {
mallId = 158,
commodityId = 120159,
commodityName = "限定表情组合礼",
price = 50,
discountPrice = 15,
beginTime = {
seconds = 1734105600
},
endTime = {
seconds = 1738511999
},
shopSort = 99,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
300101,
711284
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_1.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021
},
[120160] = {
commodityId = 120160,
commodityName = "冰雪之邀礼包",
price = 128,
discountPrice = 68,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1737129599
},
shopSort = 104,
minVersion = "1.3.37.68",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
890008,
890502
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_InvitationOfSnow.astc"
},
[120161] = {
mallId = 157,
commodityId = 120161,
commodityName = "超值幸运币礼包",
price = 6,
beginTime = {
seconds = 1701792000
},
endTime = {
seconds = 1704038399
},
shopSort = 105,
minVersion = "1.3.37.14",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120162] = {
mallId = 157,
commodityId = 120162,
commodityName = "超值幸运币礼包",
beginTime = {
seconds = 1701792000
},
endTime = {
seconds = 1704038399
},
shopSort = 105,
minVersion = "1.3.37.14",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120163] = {
mallId = 157,
commodityId = 120163,
commodityName = "超值幸运币礼包",
price = 24,
beginTime = {
seconds = 1701792000
},
endTime = {
seconds = 1704038399
},
shopSort = 105,
minVersion = "1.3.37.14",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
24
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120164] = {
mallId = 157,
commodityId = 120164,
commodityName = "超值幸运币礼包",
price = 48,
beginTime = {
seconds = 1701792000
},
endTime = {
seconds = 1704038399
},
shopSort = 105,
minVersion = "1.3.37.14",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
48
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120165] = {
mallId = 157,
commodityId = 120165,
commodityName = "周年幸运礼包",
price = 6,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735660799
},
shopSort = 105,
minVersion = "1.3.37.36",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120166] = {
mallId = 157,
commodityId = 120166,
commodityName = "周年幸运礼包",
price = 30,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735660799
},
shopSort = 105,
minVersion = "1.3.37.36",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120167] = {
mallId = 157,
commodityId = 120167,
commodityName = "周年幸运礼包",
price = 98,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735660799
},
shopSort = 105,
minVersion = "1.3.37.36",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
120
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120168] = {
mallId = 158,
commodityId = 120168,
commodityName = "宠物装饰随机礼包",
price = 6,
discountPrice = 3,
limitNum = 9,
beginTime = {
seconds = 1734796800
},
endTime = {
seconds = 1736006399
},
shopSort = 60,
minVersion = "1.3.37.48",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
329921
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_CutGift_Icon_pack_sjgp2.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120175] = {
mallId = 158,
commodityId = 120175,
commodityName = "限定橙色宝箱",
price = 980,
discountPrice = 680,
beginTime = {
seconds = 1734624000
},
endTime = {
seconds = 1735833599
},
shopSort = 107,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
300305
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021
},
[120171] = {
mallId = 158,
commodityId = 120171,
commodityName = "狼人元旦礼盒",
price = 139,
discountPrice = 90,
limitNum = 20,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737043199
},
shopSort = 109,
minVersion = "1.3.37.68",
imgUrl = v6,
itemIds = {
310716
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/langren_newyears.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120170] = {
mallId = 158,
commodityId = 120170,
commodityName = "麻醉枪礼盒",
price = 90,
discountPrice = 60,
limitNum = 20,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737043199
},
shopSort = 110,
minVersion = "1.3.37.68",
imgUrl = v6,
itemIds = {
310717
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/langren_mazuiqiang.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120178] = {
mallId = 158,
commodityId = 120178,
commodityName = "限定橙色宝箱",
price = 980,
discountPrice = 680,
beginTime = {
seconds = 1735833600
},
endTime = {
seconds = 1737043199
},
shopSort = 112,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
300306
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021
},
[120172] = {
mallId = 158,
commodityId = 120172,
commodityName = "浪漫2025",
price = 130,
discountPrice = 85,
limitNum = 20,
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1737043199
},
shopSort = 111,
minVersion = "1.3.37.68",
imgUrl = v6,
itemIds = {
310718
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/langren_2025.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120191] = {
mallId = 158,
commodityId = 120191,
commodityName = "COC资源礼包测试1",
price = 120,
discountPrice = 60,
limitNum = 3,
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1736092799
},
shopSort = 97,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
310669
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 20083
},
[120192] = {
mallId = 158,
commodityId = 120192,
commodityName = "COC资源礼包测试2",
price = 980,
discountPrice = 680,
limitNum = 3,
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1736092799
},
shopSort = 97,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
310670
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 20083
},
[120193] = {
mallId = 158,
commodityId = 120193,
commodityName = "COC资源礼包测试3",
price = 8000,
discountPrice = 6480,
limitNum = 3,
beginTime = {
seconds = 1733414400
},
endTime = {
seconds = 1736092799
},
shopSort = 97,
minVersion = "1.3.26.10",
imgUrl = v6,
itemIds = {
310671
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "arena_box_4.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 20083
},
[120196] = {
mallId = 158,
commodityId = 120196,
commodityName = "峡谷精品礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1737820799
},
shopSort = 115,
minVersion = "1.3.37.97",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120197] = {
mallId = 158,
commodityId = 120197,
commodityName = "峡谷特典礼包",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1736784000
},
endTime = {
seconds = 1737820799
},
shopSort = 114,
minVersion = "1.3.37.97",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
98
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120198] = {
mallId = 158,
commodityId = 120198,
commodityName = "狼人专享卡包",
price = 30,
discountPrice = 20,
limitNum = 20,
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1738252799
},
shopSort = 116,
minVersion = "1.3.37.100",
imgUrl = v6,
itemIds = {
310318
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren1.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120199] = {
mallId = 158,
commodityId = 120199,
commodityName = "狼人身份卡包",
price = 40,
discountPrice = 30,
limitNum = 20,
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1738252799
},
shopSort = 117,
minVersion = "1.3.37.100",
imgUrl = v6,
itemIds = {
310319
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren3.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120200] = {
mallId = 157,
commodityId = 120200,
commodityName = "迎春随机礼包",
price = 3,
discountPrice = 1,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
shopSort = 126,
minVersion = "1.3.68.51",
canDirectBuy = true,
imgUrl = "T_CutGift_Icon_Pack_02",
itemIds = {
320098
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftRandomBgImgUrlCdn = "T_CutGift_bg_GoldRandom_Spring.astc"
},
[120201] = {
mallId = 157,
commodityId = 120201,
commodityName = "吉祥如意礼包",
discountPrice = 6,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
shopSort = 125,
minVersion = "1.3.68.51",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = "T_CutGift_bg_Gold_Spring.astc"
},
[120202] = {
mallId = 157,
commodityId = 120202,
commodityName = "心想事成礼包",
price = 42,
discountPrice = 30,
beginTime = {
seconds = 1737648000
},
endTime = {
seconds = 1739462399
},
shopSort = 124,
minVersion = "1.3.68.51",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = "T_CutGift_bg_Gold_Spring.astc"
},
[120207] = {
mallId = 157,
commodityId = 120207,
commodityName = "冬日随机礼盒",
discountPrice = 6,
limitNum = 4,
beginTime = {
seconds = 1674835200
},
endTime = {
seconds = 1676822399
},
shopSort = 129,
minVersion = "1.3.68.51",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320096
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_drsj1.astc"
},
[120208] = {
mallId = 157,
commodityId = 120208,
commodityName = "狗狗礼盒",
price = 40,
discountPrice = 20,
beginTime = {
seconds = 1738339200
},
endTime = {
seconds = 1739980799
},
shopSort = 128,
minVersion = "1.3.68.51",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
404330,
620582
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_gg.astc"
},
[120209] = {
mallId = 157,
commodityId = 120209,
commodityName = "辣妹礼盒",
price = 40,
discountPrice = 20,
beginTime = {
seconds = 1738339200
},
endTime = {
seconds = 1739980799
},
shopSort = 127,
minVersion = "1.3.68.51",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
404610,
620651
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_lm.astc"
},
[120206] = {
mallId = 158,
commodityId = 120206,
commodityName = "宠物装饰随机礼包",
price = 6,
discountPrice = 3,
limitNum = 9,
beginTime = {
seconds = 1738425600
},
endTime = {
seconds = 1739375999
},
shopSort = 125,
minVersion = "1.3.68.26",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
329927
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_CutGift_Icon_pack_sjgp3.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120214] = {
mallId = 158,
commodityId = 120214,
commodityName = "峡谷精品礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1739030399
},
shopSort = 115,
minVersion = "1.3.37.97",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120215] = {
mallId = 158,
commodityId = 120215,
commodityName = "峡谷特典礼包",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1738166400
},
endTime = {
seconds = 1739030399
},
shopSort = 114,
minVersion = "1.3.37.97",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
98
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120210] = {
mallId = 158,
commodityId = 120210,
commodityName = "新春快乐礼盒",
price = 145,
discountPrice = 99,
limitNum = 20,
beginTime = {
seconds = 1737993600
},
endTime = {
seconds = 1739721599
},
shopSort = 124,
minVersion = "1.3.68.50",
imgUrl = v6,
itemIds = {
310728
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/wolf_xinchun.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120211] = {
mallId = 158,
commodityId = 120211,
commodityName = "情人节快乐礼盒",
price = 110,
discountPrice = 79,
limitNum = 20,
beginTime = {
seconds = 1739376000
},
endTime = {
seconds = 1740326399
},
shopSort = 125,
minVersion = "1.3.68.69",
imgUrl = v6,
itemIds = {
310729
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/wolf_qingrenjie.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120216] = {
mallId = 158,
commodityId = 120216,
commodityName = "超值体验礼包",
price = 4,
discountPrice = 1,
beginTime = {
seconds = 1740758400
},
shopSort = 128,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
200201,
1
},
itemNums = {
1,
10
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/czty.astc",
recommend = true,
recommendType = 2
},
[120217] = {
mallId = 158,
commodityId = 120217,
commodityName = "特惠装饰礼包",
price = 60,
discountPrice = 6,
beginTime = {
seconds = 1740758400
},
shopSort = 127,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
218111,
1
},
itemNums = {
1,
60
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/thzs.astc",
recommend = true,
recommendType = 2
},
[120218] = {
mallId = 158,
commodityId = 120218,
commodityName = "超值家具礼包",
price = 60,
discountPrice = 18,
beginTime = {
seconds = 1740758400
},
shopSort = 126,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
200103,
218824,
1
},
itemNums = {
1,
1,
60
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/czjj.astc",
recommend = true,
recommendType = 2
},
[120219] = {
mallId = 158,
commodityId = 120219,
commodityName = "峡谷精品礼包",
discountPrice = 6,
limitNum = 2,
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1742140799
},
shopSort = 132,
minVersion = "1.3.68.100",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120220] = {
mallId = 158,
commodityId = 120220,
commodityName = "峡谷特典礼包",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1741363200
},
endTime = {
seconds = 1742140799
},
shopSort = 131,
minVersion = "1.3.68.100",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
98
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120221] = {
mallId = 158,
commodityId = 120221,
commodityName = "通行证助力宝箱",
price = 15,
discountPrice = 1,
beginTime = {
seconds = 1740672000
},
endTime = {
seconds = 1741190400
},
shopSort = 130,
minVersion = v9,
canDirectBuy = true,
imgUrl = v6,
itemIds = {
1005
},
itemNums = {
300
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_tongxingzheng.astc",
cutGiftNormalBgImgUrlCdn = "T_CutGift_bg_Blue.astc"
},
[120222] = {
mallId = 158,
commodityId = 120222,
commodityName = "狼人专享卡包",
price = 30,
discountPrice = 20,
limitNum = 20,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742745599
},
shopSort = 116,
minVersion = "1.3.78.1",
imgUrl = v6,
itemIds = {
310318
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren1.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120223] = {
mallId = 158,
commodityId = 120223,
commodityName = "狼人身份卡包",
price = 40,
discountPrice = 30,
limitNum = 20,
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1742745599
},
shopSort = 117,
minVersion = "1.3.78.1",
imgUrl = v6,
itemIds = {
310319
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren3.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120224] = {
mallId = 158,
commodityId = 120224,
commodityName = "精品宠物礼包",
price = 800,
discountPrice = 680,
beginTime = {
seconds = 1741795200
},
shopSort = 129,
imgUrl = v6,
itemIds = {
219203,
219302
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Common_Item_System_StyleBox03.astc",
recommend = true,
recommendType = 2
},
[120227] = {
commodityId = 120227,
commodityName = "月卡组合特惠礼盒",
price = 42,
discountPrice = 30,
beginTime = {
seconds = 1743264000
},
endTime = {
seconds = 1743955199
},
shopSort = 133,
minVersion = "1.3.78.58",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
210000,
200103
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_yueka.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120228] = {
commodityId = 120228,
commodityName = "茸茸组合礼包",
discountPrice = 6,
limitNum = 4,
beginTime = {
seconds = 1745251200
},
endTime = {
seconds = 1746115199
},
shopSort = 134,
minVersion = "1.3.78.99",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320123
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_Common_Item_System_foxrabbit.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120229] = {
mallId = 158,
commodityId = 120229,
commodityName = "收获日礼包",
price = 500,
discountPrice = 450,
beginTime = {
seconds = 1743264000
},
shopSort = 129,
minVersion = "1.3.78.58",
imgUrl = v6,
itemIds = {
219300
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/shr2.astc",
recommend = true,
recommendType = 2
},
[120225] = {
commodityId = 120225,
commodityName = "春日漫游礼包",
price = 6,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopSort = 105,
minVersion = "1.3.37.36",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
12
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120226] = {
commodityId = 120226,
commodityName = "春日漫游礼包",
price = 30,
beginTime = {
seconds = 1743696000
},
endTime = {
seconds = 1745510399
},
shopSort = 105,
minVersion = "1.3.37.36",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
42
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = v11,
cutGiftNormalBgImgUrlCdn = v12
},
[120231] = {
mallId = 158,
commodityId = 120231,
commodityName = "峡谷超值礼包",
price = 6,
discountPrice = 1,
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopSort = 138,
minVersion = "1.3.78.80",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120232] = {
mallId = 158,
commodityId = 120232,
commodityName = "峡谷精品礼包",
price = 20,
discountPrice = 12,
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopSort = 137,
minVersion = "1.3.78.80",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
20
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120233] = {
mallId = 158,
commodityId = 120233,
commodityName = "峡谷特典礼包",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1745596800
},
endTime = {
seconds = 1748707199
},
shopSort = 136,
minVersion = "1.3.78.80",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
14
},
itemNums = {
98
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "Huanmengbi_libao.astc",
cutGiftNormalBgImgUrlCdn = v14
},
[120234] = {
mallId = 158,
commodityId = 120234,
commodityName = "狼人专享卡包",
price = 30,
discountPrice = 20,
limitNum = 20,
beginTime = {
seconds = 1744732800
},
endTime = {
seconds = 1746979199
},
shopSort = 140,
minVersion = "1.3.88.1",
imgUrl = v6,
showRedPoint = 1,
itemIds = {
310318
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren1.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120235] = {
mallId = 158,
commodityId = 120235,
commodityName = "狼人身份卡包",
price = 40,
discountPrice = 30,
limitNum = 20,
beginTime = {
seconds = 1744732800
},
endTime = {
seconds = 1746979199
},
shopSort = 141,
minVersion = "1.3.88.1",
imgUrl = v6,
showRedPoint = 1,
itemIds = {
310319
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren3.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003
},
[120236] = {
mallId = 157,
commodityId = 120236,
commodityName = "测试随机礼包",
discountPrice = 6,
limitNum = 20,
beginTime = {
seconds = 1745251200
},
endTime = {
seconds = 1746115199
},
shopSort = 134,
minVersion = "1.3.88.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
320124
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_Common_Item_System_foxrabbit.astc",
cutGiftNormalBgImgUrlCdn = v13
},
[120237] = {
mallId = 157,
commodityId = 120237,
commodityName = "假日补给包",
discountPrice = 6,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1746460799
},
shopSort = 144,
minVersion = "1.3.88.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
3,
2
},
itemNums = {
6,
6
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_xybxyb.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120238] = {
mallId = 157,
commodityId = 120238,
commodityName = "假日加油包",
price = 60,
discountPrice = 30,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1746460799
},
shopSort = 143,
minVersion = "1.3.88.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
3,
2
},
itemNums = {
36,
24
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_xybxyb.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120239] = {
mallId = 157,
commodityId = 120239,
commodityName = "假日助燃包",
price = 120,
discountPrice = 68,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1746460799
},
shopSort = 142,
minVersion = "1.3.88.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
3,
2
},
itemNums = {
80,
30
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_xybxyb.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120240] = {
mallId = 158,
commodityId = 120240,
commodityName = "亚瑟的赠礼",
price = 60,
discountPrice = 3,
beginTime = {
seconds = 1746028800
},
shopSort = 157,
minVersion = "1.3.88.52",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
407013,
303510
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_CutGift_Icon_Pack_Arena_Arthur.astc",
cutGiftNormalBgImgUrlCdn = v14,
pakGroup = 50021,
recommendType = 3
},
[120241] = {
mallId = 158,
commodityId = 120241,
commodityName = "奶油萌宠屋",
price = 98,
discountPrice = 68,
beginTime = {
seconds = 1745856000
},
shopSort = 147,
minVersion = "1.3.88.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
218197
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/nymcw.astc",
recommend = true,
recommendType = 2
},
[120247] = {
mallId = 158,
commodityId = 120247,
commodityName = "狼人专享卡包",
price = 30,
discountPrice = 20,
limitNum = 20,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1751817599
},
shopSort = 140,
minVersion = "1.3.88.143",
imgUrl = v6,
showRedPoint = 1,
itemIds = {
310318
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren1.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003,
recommend = true,
recommendType = 1
},
[120248] = {
mallId = 158,
commodityId = 120248,
commodityName = "狼人身份卡包",
price = 40,
discountPrice = 30,
limitNum = 20,
beginTime = {
seconds = 1750953600
},
endTime = {
seconds = 1751817599
},
shopSort = 141,
minVersion = "1.3.88.143",
imgUrl = v6,
showRedPoint = 1,
itemIds = {
310319
},
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 1,
cutGiftRecommandStyle = 2,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_langren3.astc",
cutGiftNormalBgImgUrlCdn = v13,
pakGroup = 20003,
recommend = true,
recommendType = 1
},
[120242] = {
mallId = 158,
commodityId = 120242,
commodityName = "狼人体验礼包",
price = 4,
discountPrice = 1,
beginTime = {
seconds = 1748707200
},
shopSort = 150,
minVersion = "1.3.88.4",
canDirectBuy = true,
imgUrl = v6,
showRedPoint = 1,
itemIds = {
200180,
1,
240999
},
itemNums = {
1,
10,
10
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/langrentylb.astc",
recommend = true,
recommendType = 1
},
[120243] = {
mallId = 158,
commodityId = 120243,
commodityName = "身份特惠礼包",
price = 30,
discountPrice = 6,
beginTime = {
seconds = 1748707200
},
shopSort = 149,
minVersion = "1.3.88.4",
canDirectBuy = true,
imgUrl = v6,
showRedPoint = 1,
itemIds = {
330801,
1,
240999
},
itemNums = {
1,
60,
60
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/shenfenthlb.astc",
recommend = true,
recommendType = 1
},
[120244] = {
mallId = 158,
commodityId = 120244,
commodityName = "超值月卡礼包",
price = 30,
discountPrice = 12,
beginTime = {
seconds = 1747065600
},
shopSort = 148,
minVersion = "1.3.88.4",
canDirectBuy = true,
imgUrl = v6,
showRedPoint = 1,
itemIds = {
200178,
1,
240999
},
itemNums = {
1,
60,
120
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/chaozhiyklbxiao.astc",
recommend = true,
recommendType = 1
},
[120245] = {
mallId = 158,
commodityId = 120245,
commodityName = "小金毛礼包",
price = 360,
discountPrice = 300,
beginTime = {
seconds = 1748534400
},
shopSort = 151,
minVersion = "1.3.88.112",
imgUrl = v6,
itemIds = {
219205
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/GoldenRetriever.astc",
recommend = true,
recommendType = 2
},
[120246] = {
mallId = 158,
commodityId = 120246,
commodityName = "端午宠物礼包",
price = 860,
discountPrice = 680,
beginTime = {
seconds = 1748534400
},
endTime = {
seconds = 1751039999
},
shopSort = 151,
minVersion = "1.3.88.112",
imgUrl = v6,
itemIds = {
219205,
219300
},
itemNums = {
1,
1
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/DuanwuFarm.astc",
recommend = true,
recommendType = 2
},
[120347] = {
mallId = 157,
commodityId = 120347,
commodityName = "尊享幸运礼",
price = 32,
discountPrice = 29,
beginTime = {
seconds = 1746028800
},
endTime = {
seconds = 1750607999
},
shopSort = 155,
minVersion = "1.3.88.1",
canDirectBuy = true,
imgUrl = v6,
itemNums = {
32
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 4,
cutGiftStyle = 0,
cutGiftRecommandStyle = 1,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "T_CutGift_Icon_pack_xybxyb.astc",
cutGiftNormalBgImgUrlCdn = v12
},
[120249] = {
mallId = 158,
commodityId = 120249,
commodityName = "狸猫精灵礼包",
price = 98,
discountPrice = 18,
beginTime = {
seconds = 1751558400
},
endTime = {
seconds = 1753372799
},
shopSort = 156,
minVersion = "1.3.99.1",
canDirectBuy = true,
imgUrl = v6,
itemIds = {
218113
},
packageDec = v2,
accumulateMax = 0,
cutGiftColor = 0,
cutGiftStyle = 0,
cutGiftDiscountStyle = 1,
cutGiftCenterImgUrlCdn = "https://image-manage.ymzx.qq.com/wuji/client/materials/limao.astc",
recommend = true,
recommendType = 2
}
}

local mt = {
mallId = 20,
coinType = 1,
price = 12,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
gender = 0,
canDirectBuy = false,
itemIds = {
3
},
itemNums = {
1
},
syncDB = false,
canAccumulate = false,
recommend = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data