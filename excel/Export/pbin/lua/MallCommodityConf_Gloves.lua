--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-手套

local v0 = {
seconds = 4074854400
}

local v1 = 1

local v2 = "赛季祈愿"

local v3 = 6

local v4 = 10000

local v5 = "1.3.6.1"

local data = {
[40001] = {
commodityId = 40001,
commodityName = "国潮少年手套",
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
shopSort = 4,
jumpId = 38,
jumpText = "新星指南",
itemIds = {
530001
}
},
[40002] = {
commodityId = 40002,
commodityName = "蓝色polo衫手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530002
}
},
[40003] = {
commodityId = 40003,
commodityName = "蓝紫卫衣手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530003
}
},
[40004] = {
commodityId = 40004,
commodityName = "篮球少年手套",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 5,
jumpId = 33,
jumpText = "闯关挑战",
itemIds = {
530004
},
bOpenSuit = true,
suitId = 7
},
[40005] = {
commodityId = 40005,
commodityName = "黑白夹心手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530005
}
},
[40006] = {
commodityId = 40006,
commodityName = "夏日微风手套",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v2,
itemIds = {
530006
},
bOpenSuit = true,
suitId = 1
},
[40007] = {
commodityId = 40007,
commodityName = "最佳员工手套",
beginTime = {
seconds = 1676390400
},
shopSort = 3,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
530007
},
bOpenSuit = true,
suitId = 2
},
[40008] = {
commodityId = 40008,
commodityName = "米色夹克T恤套装手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530008
}
},
[40009] = {
commodityId = 40009,
commodityName = "荧光绿套装手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530009
}
},
[40010] = {
commodityId = 40010,
commodityName = "星小递手套",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1704383999
},
shopSort = 6,
jumpId = 44,
jumpText = "小乔送星运",
itemIds = {
530010
},
bOpenSuit = true,
suitId = 5
},
[40011] = {
commodityId = 40011,
commodityName = "挎包短袖套装手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530011
}
},
[40012] = {
commodityId = 40012,
commodityName = "沙滩旅客手套",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 3,
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
530012
},
bOpenSuit = true,
suitId = 6
},
[40013] = {
commodityId = 40013,
commodityName = "紫色棉服套装手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530013
}
},
[40014] = {
commodityId = 40014,
commodityName = "活力街头手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530014
}
},
[40015] = {
commodityId = 40015,
commodityName = "清爽运动手套",
beginTime = {
seconds = 1676390400
},
shopSort = 7,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
530015
},
bOpenSuit = true,
suitId = 3
},
[40016] = {
commodityId = 40016,
commodityName = "棒球少年手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530016
}
},
[40017] = {
commodityId = 40017,
commodityName = "灰色边缘手套",
beginTime = {
seconds = 1676390400
},
shopSort = 3,
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
530017
},
bOpenSuit = true,
suitId = 4
},
[40018] = {
commodityId = 40018,
commodityName = "赤土探险手套",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v2,
itemIds = {
530018
},
bOpenSuit = true,
suitId = 9
},
[40019] = {
commodityId = 40019,
commodityName = "粉墨工匠手套",
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
shopSort = 2,
itemIds = {
530019
},
suitId = 8
},
[40020] = {
commodityId = 40020,
commodityName = "酷橙嘻哈侠手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530020
}
},
[40021] = {
commodityId = 40021,
commodityName = "萤绿嘻哈侠手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530021
}
},
[40022] = {
commodityId = 40022,
commodityName = "和平套装手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530022
}
},
[40023] = {
commodityId = 40023,
commodityName = "活力运动手套",
coinType = 6,
price = 50,
beginTime = {
seconds = 1719504000
},
itemIds = {
530023
},
suitId = 427
},
[40024] = {
commodityId = 40024,
commodityName = "灰色边缘B手套",
coinType = 6,
price = 10000,
beginTime = {
seconds = 1676390400
},
endTime = {
seconds = 1676476800
},
itemIds = {
530024
}
},
[40025] = {
commodityId = 40025,
commodityName = "麻将发财套装手套",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopSort = 1,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
530025
},
bOpenSuit = true,
suitId = 60
},
[40026] = {
commodityId = 40026,
commodityName = "棉手套",
coinType = 6,
price = 50,
beginTime = {
seconds = 1705334400
},
endTime = {
seconds = 7258003199
},
shopSort = 1,
itemIds = {
530038
},
canGift = true,
addIntimacy = 5,
giftCoinType = 1,
giftPrice = 50,
suitId = 63
},
[41000] = {
commodityId = 41000,
commodityName = "像素风手套",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v2,
minVersion = "1.2.67.1",
itemIds = {
530027
},
bOpenSuit = true,
suitId = 84
},
[41001] = {
commodityId = 41001,
commodityName = "涂鸦手套",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v2,
minVersion = "1.2.67.1",
itemIds = {
530028
},
bOpenSuit = true,
suitId = 85
},
[41002] = {
commodityId = 41002,
commodityName = "麻将套红中手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530029
},
suitId = 87
},
[41003] = {
commodityId = 41003,
commodityName = "麻将套南风手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530030
},
suitId = 88
},
[41004] = {
commodityId = 41004,
commodityName = "校服手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530031
},
suitId = 91
},
[41005] = {
commodityId = 41005,
commodityName = "薯条手套",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v2,
minVersion = "1.2.67.1",
itemIds = {
530032
},
bOpenSuit = true,
suitId = 92
},
[41006] = {
commodityId = 41006,
commodityName = "FPS射击类匪装手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530033
},
suitId = 94
},
[41007] = {
commodityId = 41007,
commodityName = "唐装-CP手套",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
jumpId = 171,
jumpText = "充值送好礼",
minVersion = "1.2.80.1",
itemIds = {
530034
},
bOpenSuit = true,
suitId = 95
},
[41008] = {
commodityId = 41008,
commodityName = "唐装手套",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
jumpId = 171,
jumpText = "充值送好礼",
minVersion = "1.2.80.1",
itemIds = {
530035
},
bOpenSuit = true,
suitId = 98
},
[41009] = {
commodityId = 41009,
commodityName = "奶牛手套",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
jumpId = 181,
jumpText = "绮梦灯",
minVersion = "1.2.80.1",
itemIds = {
530036
},
bOpenSuit = true,
suitId = 96
},
[41010] = {
commodityId = 41010,
commodityName = "小老鼠手套",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 9,
jumpText = "山海通行证",
minVersion = "1.2.67.1",
itemIds = {
530037
},
bOpenSuit = true,
suitId = 97
},
[41011] = {
commodityId = 41011,
commodityName = "水瓶座手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530039
},
suitId = 100
},
[41012] = {
commodityId = 41012,
commodityName = "赞不绝手手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530041
}
},
[41013] = {
commodityId = 41013,
commodityName = "寻味奇趣手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530042
}
},
[41014] = {
commodityId = 41014,
commodityName = "红莓酥糖手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.67.1",
itemIds = {
530043
}
},
[41015] = {
commodityId = 41015,
commodityName = "绝对爆发财手套",
coinType = 6,
price = 50,
beginTime = {
seconds = 1719504000
},
minVersion = "1.2.67.1",
itemIds = {
530044
},
suitId = 426
},
[41016] = {
commodityId = 41016,
commodityName = "绅士礼服手套",
coinType = 6,
price = 50,
beginTime = {
seconds = 1708704000
},
minVersion = "1.2.67.1",
itemIds = {
530045
},
canGift = true,
addIntimacy = 5,
giftCoinType = 1,
giftPrice = 50,
suitId = 179
},
[41101] = {
commodityId = 41101,
commodityName = "星夜绅士手套",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 9,
jumpText = "时光通行证",
itemIds = {
530046
},
bOpenSuit = true,
suitId = 156
},
[41102] = {
commodityId = 41102,
commodityName = "浪漫双鱼手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530047
}
},
[41103] = {
commodityId = 41103,
commodityName = "拼贴风尚手套",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
530048
},
bOpenSuit = true,
suitId = 180
},
[41104] = {
commodityId = 41104,
commodityName = "日落海岛手套",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
530049
},
bOpenSuit = true,
suitId = 181
},
[41105] = {
commodityId = 41105,
commodityName = "热带风情手套",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
530050
},
bOpenSuit = true,
suitId = 182
},
[41106] = {
commodityId = 41106,
commodityName = "新奇撞色手套",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = "印章祈愿",
itemIds = {
530051
},
bOpenSuit = true,
suitId = 183
},
[41107] = {
commodityId = 41107,
commodityName = "素雅新装手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530052
}
},
[41108] = {
commodityId = 41108,
commodityName = "莹紫葡萄手套",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 189,
jumpText = v2,
itemIds = {
530053
},
bOpenSuit = true,
suitId = 158
},
[41109] = {
commodityId = 41109,
commodityName = "星彩恋恋手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530054
}
},
[41110] = {
commodityId = 41110,
commodityName = "星海领航员手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530055
}
},
[41111] = {
commodityId = 41111,
commodityName = "荣誉新生手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530056
},
suitId = 166
},
[41112] = {
commodityId = 41112,
commodityName = "爱意热诚手套",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
itemIds = {
530057
},
bOpenSuit = true,
suitId = 203
},
[41113] = {
commodityId = 41113,
commodityName = "涂鸦印记手套",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 177,
jumpText = "暗夜冰羽",
itemIds = {
530058
},
bOpenSuit = true,
suitId = 186
},
[41114] = {
commodityId = 41114,
commodityName = "极速风潮手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530059
}
},
[41115] = {
commodityId = 41115,
commodityName = "清凉海浪手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530060
}
},
[41116] = {
commodityId = 41116,
commodityName = "丝绒格调手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530061
}
},
[41117] = {
commodityId = 41117,
commodityName = "别来沾边手套",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = "1.2.100.1",
itemIds = {
530062
},
bOpenSuit = true,
suitId = 213
},
[41118] = {
commodityId = 41118,
commodityName = "无语时刻手套",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = "1.2.100.1",
itemIds = {
530063
},
bOpenSuit = true,
suitId = 214
},
[41119] = {
commodityId = 41119,
commodityName = "春日校园手套",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v2,
minVersion = "1.2.100.1",
itemIds = {
530064
},
bOpenSuit = true,
suitId = 201
},
[41120] = {
commodityId = 41120,
commodityName = "春风探险手套",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v2,
minVersion = "1.2.100.1",
itemIds = {
530065
},
bOpenSuit = true,
suitId = 202
},
[41121] = {
commodityId = 41121,
commodityName = "邻家学妹手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530066
}
},
[41122] = {
commodityId = 41122,
commodityName = "学生会长手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530067
}
},
[41123] = {
commodityId = 41123,
commodityName = "暖暖白羊手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530068
}
},
[41124] = {
commodityId = 41124,
commodityName = "隐藏富豪手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530069
}
},
[41125] = {
commodityId = 41125,
commodityName = "年会爆款手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530070
}
},
[41126] = {
commodityId = 41126,
commodityName = "进步青年手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530071
}
},
[41127] = {
commodityId = 41127,
commodityName = "职业轻装手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530072
}
},
[41128] = {
commodityId = 41128,
commodityName = "夏日炎炎手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = "1.2.100.1",
itemIds = {
530073
}
},
[41129] = {
commodityId = 41129,
commodityName = "草莓抹茶手套",
beginTime = {
seconds = 1719244800
},
endTime = {
seconds = 1720540799
},
jumpId = 1046,
jumpText = "星梭祈愿",
minVersion = "1.3.7.53",
itemIds = {
530074
},
bOpenSuit = true,
suitId = 450
},
[41130] = {
commodityId = 41130,
commodityName = "元气之星手套",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
jumpId = 176,
jumpText = "星梦使者",
itemIds = {
530075
},
bOpenSuit = true,
suitId = 233
},
[41131] = {
commodityId = 41131,
commodityName = "梦里寻粽手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530076
}
},
[41132] = {
commodityId = 41132,
commodityName = "布朗尼尼手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530077
}
},
[41133] = {
commodityId = 41133,
commodityName = "粉桃朵朵手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530078
}
},
[41134] = {
commodityId = 41134,
commodityName = "晚宴绅士手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530079
}
},
[41135] = {
commodityId = 41135,
commodityName = "晚宴淑女手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530080
}
},
[41136] = {
commodityId = 41136,
commodityName = "通行卫士手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530081
}
},
[41137] = {
commodityId = 41137,
commodityName = "古韵长衣手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530082
},
suitId = 416
},
[41138] = {
commodityId = 41138,
commodityName = "气息归元手套",
coinType = 6,
price = 10000,
endTime = v0,
itemIds = {
530083
},
suitId = 417
},
[41139] = {
commodityId = 41139,
commodityName = "青春领航手套",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v2,
minVersion = v5,
itemIds = {
530084
},
bOpenSuit = true,
suitId = 401
},
[41140] = {
commodityId = 41140,
commodityName = "海岛一刻手套",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v2,
minVersion = v5,
itemIds = {
530085
},
bOpenSuit = true,
suitId = 402
},
[41141] = {
commodityId = 41141,
commodityName = "海盐兔兔手套",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
530086
},
bOpenSuit = true,
suitId = 403
},
[41142] = {
commodityId = 41142,
commodityName = "灰调潮流手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530087
},
suitId = 404
},
[41143] = {
commodityId = 41143,
commodityName = "灰粉嘻哈手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530088
},
suitId = 405
},
[41144] = {
commodityId = 41144,
commodityName = "多元时尚手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530089
},
suitId = 406
},
[41145] = {
commodityId = 41145,
commodityName = "多云转晴手套",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
530090
},
bOpenSuit = true,
suitId = 408
},
[41146] = {
commodityId = 41146,
commodityName = "慢热金牛手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530091
},
suitId = 410
},
[41147] = {
commodityId = 41147,
commodityName = "蔚蓝晴空手套",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v5,
itemIds = {
530092
},
bOpenSuit = true,
suitId = 411
},
[41148] = {
commodityId = 41148,
commodityName = "春城粉桃手套",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
530093
},
bOpenSuit = true,
suitId = 412
},
[41149] = {
commodityId = 41149,
commodityName = "奇喵物语手套",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v2,
minVersion = v5,
itemIds = {
530094
},
bOpenSuit = true,
suitId = 413
},
[41150] = {
commodityId = 41150,
commodityName = "青春筑梦手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530095
},
suitId = 415
},
[41151] = {
commodityId = 41151,
commodityName = "茸茸睡衣手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530096
},
suitId = 419
},
[41152] = {
commodityId = 41152,
commodityName = "蒸汽工程手套",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
530097
},
bOpenSuit = true,
suitId = 420
},
[41153] = {
commodityId = 41153,
commodityName = "果味缤纷手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530098
},
suitId = 422
},
[41154] = {
commodityId = 41154,
commodityName = "熊熊出游手套",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v2,
minVersion = "1.3.12.1",
itemIds = {
530099
},
bOpenSuit = true,
suitId = 430
},
[41155] = {
commodityId = 41155,
commodityName = "悠扬青春手套",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v2,
minVersion = "1.3.12.1",
itemIds = {
530100
},
bOpenSuit = true,
suitId = 431
},
[41156] = {
commodityId = 41156,
commodityName = "酸甜柠趣手套",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
jumpId = 694,
jumpText = "幻音喵境",
minVersion = v5,
itemIds = {
530101
},
bOpenSuit = true,
suitId = 432
},
[41157] = {
commodityId = 41157,
commodityName = "夏意清新手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530102
},
suitId = 433
},
[41158] = {
commodityId = 41158,
commodityName = "盛夏果实手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530103
},
suitId = 434
},
[41159] = {
commodityId = 41159,
commodityName = "时尚教主手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530104
},
suitId = 435
},
[41160] = {
commodityId = 41160,
commodityName = "古韵中轴手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530105
},
suitId = 440
},
[41161] = {
commodityId = 41161,
commodityName = "芝芝桃桃手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530106
},
suitId = 441
},
[41162] = {
commodityId = 41162,
commodityName = "缤纷夏花手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530107
},
suitId = 442
},
[41163] = {
commodityId = 41163,
commodityName = "首席射手手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530108
},
suitId = 443
},
[41164] = {
commodityId = 41164,
commodityName = "静谧狩猎手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530109
},
suitId = 444
},
[41165] = {
commodityId = 41165,
commodityName = "弹跳乒乓手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530110
},
suitId = 445
},
[41166] = {
commodityId = 41166,
commodityName = "无限换防手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530111
},
suitId = 446
},
[41167] = {
commodityId = 41167,
commodityName = "活力网坛手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530112
},
suitId = 447
},
[41168] = {
commodityId = 41168,
commodityName = "精准投手手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530113
},
suitId = 448
},
[41169] = {
commodityId = 41169,
commodityName = "凌空飞跃手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530114
},
suitId = 449
},
[41170] = {
commodityId = 41170,
commodityName = "郊游小熊手套",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = "1.3.12.1",
itemIds = {
530115
},
bOpenSuit = true,
suitId = 451
},
[41171] = {
commodityId = 41171,
commodityName = "仲夏芳菲手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530116
},
suitId = 452
},
[41172] = {
commodityId = 41172,
commodityName = "童梦奇缘手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530117
},
suitId = 453
},
[41173] = {
commodityId = 41173,
commodityName = "青柠之恋手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530118
},
suitId = 454
},
[41174] = {
commodityId = 41174,
commodityName = "球场裁判手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530119
},
suitId = 456
},
[41175] = {
commodityId = 41175,
commodityName = "数字休闲手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530120
},
suitId = 457
},
[41176] = {
commodityId = 41176,
commodityName = "波普艺术手套",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v2,
minVersion = "1.3.12.1",
itemIds = {
530121
},
bOpenSuit = true,
suitId = 429
},
[41177] = {
commodityId = 41177,
commodityName = "活力绅士手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530122
},
suitId = 458
},
[41178] = {
commodityId = 41178,
commodityName = "气质暖狼手套",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = v5,
itemIds = {
530123
},
bOpenSuit = true,
suitId = 459
},
[41179] = {
commodityId = 41179,
commodityName = "开心一刻手套",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
530124
},
bOpenSuit = true,
suitId = 460
},
[41180] = {
commodityId = 41180,
commodityName = "揶揄时光手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530125
},
suitId = 461
},
[41181] = {
commodityId = 41181,
commodityName = "精致领班手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530126
},
suitId = 465
},
[41182] = {
commodityId = 41182,
commodityName = "美味晨光手套",
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1727366399
},
shopSort = 1,
jumpId = 364,
jumpText = "山河卷扇",
minVersion = "1.3.18.37",
itemIds = {
530127
},
bOpenSuit = true,
suitId = 466
},
[41183] = {
commodityId = 41183,
commodityName = "萌虎出动手套",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v2,
minVersion = v5,
itemIds = {
530128
},
bOpenSuit = true,
suitId = 467
},
[41184] = {
commodityId = 41184,
commodityName = "专业态度手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530129
},
suitId = 468
},
[41185] = {
commodityId = 41185,
commodityName = "潮酷玩家手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530130
},
suitId = 470
},
[41186] = {
commodityId = 41186,
commodityName = "赛场飞驰手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530131
},
suitId = 471
},
[41187] = {
commodityId = 41187,
commodityName = "浅蓝节拍手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530132
},
suitId = 472
},
[41188] = {
commodityId = 41188,
commodityName = "春日运动手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530133
},
suitId = 473
},
[41189] = {
commodityId = 41189,
commodityName = "清秋月圆手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530134
},
suitId = 474
},
[41190] = {
commodityId = 41190,
commodityName = "摩登新贵手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530135
},
suitId = 477
},
[41191] = {
commodityId = 41191,
commodityName = "饭团星球手套",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v2,
minVersion = v5,
itemIds = {
530136
},
bOpenSuit = true,
suitId = 479
},
[41192] = {
commodityId = 41192,
commodityName = "蔷薇星云手套",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v2,
minVersion = v5,
itemIds = {
530137
},
bOpenSuit = true,
suitId = 480
},
[41193] = {
commodityId = 41193,
commodityName = "职场萌星手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530138
},
suitId = 485
},
[41194] = {
commodityId = 41194,
commodityName = "微笑向阳手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530139
},
suitId = 488
},
[41195] = {
commodityId = 41195,
commodityName = "校园传说手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530140
},
suitId = 489
},
[41196] = {
commodityId = 41196,
commodityName = "松果收藏家手套",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = v5,
itemIds = {
530141
},
bOpenSuit = true,
suitId = 490
},
[41197] = {
commodityId = 41197,
commodityName = "占星学院手套",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v2,
minVersion = v5,
itemIds = {
530142
},
bOpenSuit = true,
suitId = 491
},
[41198] = {
commodityId = 41198,
commodityName = "富贵公子手套",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v2,
minVersion = v5,
itemIds = {
530143
},
bOpenSuit = true,
suitId = 492
},
[41199] = {
commodityId = 41199,
commodityName = "浪尖飞驰手套",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v5,
itemIds = {
530144
},
bOpenSuit = true,
suitId = 493
},
[41200] = {
commodityId = 41200,
commodityName = "田园牧歌手套",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732723200
},
jumpId = 33,
jumpText = "闯关挑战",
minVersion = v5,
itemIds = {
530145
},
bOpenSuit = true,
suitId = 494
},
[41201] = {
commodityId = 41201,
commodityName = "松间晨雾手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530146
},
suitId = 495
},
[41202] = {
commodityId = 41202,
commodityName = "星空梦想手套",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v2,
minVersion = v5,
itemIds = {
530147
},
bOpenSuit = true,
suitId = 496
},
[41203] = {
commodityId = 41203,
commodityName = "暖冬物语手套",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
530148
},
bOpenSuit = true,
suitId = 498
},
[41204] = {
commodityId = 41204,
commodityName = "赐福锦鲤手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530149
},
suitId = 499
},
[41205] = {
commodityId = 41205,
commodityName = "芝香披萨手套",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
530150
},
bOpenSuit = true,
suitId = 501
},
[41206] = {
commodityId = 41206,
commodityName = "爱心波波手套",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
530151
},
bOpenSuit = true,
suitId = 503
},
[41207] = {
commodityId = 41207,
commodityName = "堡你喜欢手套",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
jumpId = 628,
jumpText = "星之恋空",
minVersion = v5,
itemIds = {
530152
},
bOpenSuit = true,
suitId = 505
},
[41208] = {
commodityId = 41208,
commodityName = "星动曲奇手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530153
},
suitId = 507
},
[41209] = {
commodityId = 41209,
commodityName = "粉焰旋风手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530154
},
suitId = 513
},
[41210] = {
commodityId = 41210,
commodityName = "蔚蓝闪电手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530155
},
suitId = 515
},
[41211] = {
commodityId = 41211,
commodityName = "逐梦狂飙手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530156
},
suitId = 517
},
[41212] = {
commodityId = 41212,
commodityName = "云龙武袍手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530157
},
suitId = 519
},
[41213] = {
commodityId = 41213,
commodityName = "青岚短衫手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530158
},
suitId = 541
},
[41214] = {
commodityId = 41214,
commodityName = "沙滩宝贝手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530159
},
suitId = 543
},
[41215] = {
commodityId = 41215,
commodityName = "酣眠之冬手套",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v2,
minVersion = v5,
itemIds = {
530160
},
bOpenSuit = true,
suitId = 545
},
[41216] = {
commodityId = 41216,
commodityName = "素食潮流手套",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v5,
itemIds = {
530161
},
bOpenSuit = true,
suitId = 547
},
[41217] = {
commodityId = 41217,
commodityName = "暖樱冬语手套",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v2,
minVersion = v5,
itemIds = {
530162
},
bOpenSuit = true,
suitId = 549
},
[41218] = {
commodityId = 41218,
commodityName = "速食美学手套",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v2,
minVersion = v5,
itemIds = {
530163
},
bOpenSuit = true,
suitId = 551
},
[41219] = {
commodityId = 41219,
commodityName = "目览金秋手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530164
},
suitId = 553
},
[41220] = {
commodityId = 41220,
commodityName = "民德正雅手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530165
},
suitId = 555
},
[41221] = {
commodityId = 41221,
commodityName = "腾云一舞手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530166
},
suitId = 559
},
[41222] = {
commodityId = 41222,
commodityName = "竹韵清扬手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530167
},
suitId = 561
},
[41223] = {
commodityId = 41223,
commodityName = "墨韵乘云手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530168
},
suitId = 563
},
[41224] = {
commodityId = 41224,
commodityName = "桃心王子手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530169
},
suitId = 565
},
[41225] = {
commodityId = 41225,
commodityName = "梅花公爵手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530170
},
suitId = 567
},
[41226] = {
commodityId = 41226,
commodityName = "黑白迷踪手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530171
},
suitId = 577
},
[41227] = {
commodityId = 41227,
commodityName = "奶芙蝶语手套",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v2,
minVersion = v5,
itemIds = {
530172
},
bOpenSuit = true,
suitId = 593
},
[41228] = {
commodityId = 41228,
commodityName = "自愿上学手套",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v2,
minVersion = v5,
itemIds = {
530173
},
bOpenSuit = true,
suitId = 595
},
[41229] = {
commodityId = 41229,
commodityName = "自愿上班手套",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v2,
minVersion = v5,
itemIds = {
530174
},
bOpenSuit = true,
suitId = 597
},
[41230] = {
commodityId = 41230,
commodityName = "冬日暖喵手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530175
},
suitId = 599
},
[41231] = {
commodityId = 41231,
commodityName = "林深如墨手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530176
},
suitId = 601
},
[41232] = {
commodityId = 41232,
commodityName = "冬夜星火手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530177
},
suitId = 603
},
[41233] = {
commodityId = 41233,
commodityName = "鸭鸭护卫队手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530178
},
suitId = 605
},
[41234] = {
commodityId = 41234,
commodityName = "脆弱鸭鸭手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530179
},
suitId = 607
},
[41235] = {
commodityId = 41235,
commodityName = "冬暖花眠手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530180
},
suitId = 609
},
[41236] = {
commodityId = 41236,
commodityName = "拥抱侏罗纪手套",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v5,
itemIds = {
530181
},
bOpenSuit = true,
suitId = 615
},
[41237] = {
commodityId = 41237,
commodityName = "漆墨拼图手套",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v5,
itemIds = {
530182
},
bOpenSuit = true,
suitId = 617
},
[41238] = {
commodityId = 41238,
commodityName = "蓝庭花语手套",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v5,
itemIds = {
530183
},
bOpenSuit = true,
suitId = 619
},
[41239] = {
commodityId = 41239,
commodityName = "自然精灵手套",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = v5,
itemIds = {
530184
},
bOpenSuit = true,
suitId = 623
},
[41240] = {
commodityId = 41240,
commodityName = "冬日多巴胺手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530185
},
suitId = 625
},
[41241] = {
commodityId = 41241,
commodityName = "平安喜乐手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530186
},
suitId = 627
},
[41242] = {
commodityId = 41242,
commodityName = "欢语秋藏手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530187
},
suitId = 629
},
[41243] = {
commodityId = 41243,
commodityName = "层林覆雪手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530188
},
suitId = 631
},
[41244] = {
commodityId = 41244,
commodityName = "遗迹寻踪手套",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v5,
itemIds = {
530189
},
bOpenSuit = true,
suitId = 637
},
[41245] = {
commodityId = 41245,
commodityName = "大发特发手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530190
},
suitId = 639
},
[41246] = {
commodityId = 41246,
commodityName = "半糖心织手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530191
},
suitId = 643
},
[41247] = {
commodityId = 41247,
commodityName = "鲤跃福临手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530192
},
suitId = 645
},
[41248] = {
commodityId = 41248,
commodityName = "落樱衬衫手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530193
},
suitId = 649
},
[41249] = {
commodityId = 41249,
commodityName = "祥龙送宝手套",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
530194
},
bOpenSuit = true,
suitId = 651
},
[41250] = {
commodityId = 41250,
commodityName = "元宵宝宝手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530195
},
suitId = 679
},
[41251] = {
commodityId = 41251,
commodityName = "萌动旋风手套",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v2,
minVersion = v5,
itemIds = {
530196
},
bOpenSuit = true,
suitId = 689
},
[41252] = {
commodityId = 41252,
commodityName = "悦动甜心手套",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v2,
minVersion = v5,
itemIds = {
530197
},
bOpenSuit = true,
suitId = 691
},
[41253] = {
commodityId = 41253,
commodityName = "原野兔踪手套",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v2,
minVersion = v5,
itemIds = {
530198
},
bOpenSuit = true,
suitId = 693
},
[41254] = {
commodityId = 41254,
commodityName = "向阳之花手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530199
},
suitId = 695
},
[41255] = {
commodityId = 41255,
commodityName = "牛油果之友手套",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530200
},
bOpenSuit = true,
suitId = 697
},
[41256] = {
commodityId = 41256,
commodityName = "果冻熊之友手套",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530201
},
bOpenSuit = true,
suitId = 699
},
[41257] = {
commodityId = 41257,
commodityName = "棉花狗之友手套",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530202
},
bOpenSuit = true,
suitId = 701
},
[41258] = {
commodityId = 41258,
commodityName = "樱野纷纷手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530203
},
suitId = 703
},
[41259] = {
commodityId = 41259,
commodityName = "吟游之声手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530204
},
suitId = 705
},
[41260] = {
commodityId = 41260,
commodityName = "幻域之风手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530205
},
suitId = 707
},
[41261] = {
commodityId = 41261,
commodityName = "迷彩风尚手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530206
},
suitId = 709
},
[41262] = {
commodityId = 41262,
commodityName = "活力竖纹手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530207
},
suitId = 843
},
[41263] = {
commodityId = 41263,
commodityName = "雪人物语手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530208
},
suitId = 845
},
[41264] = {
commodityId = 41264,
commodityName = "海岛风情手套",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530209
},
bOpenSuit = true,
suitId = 847
},
[41265] = {
commodityId = 41265,
commodityName = "花舞樱樱手套",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530210
},
bOpenSuit = true,
suitId = 849
},
[41266] = {
commodityId = 41266,
commodityName = "荣冕侍卫手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530211
},
suitId = 851
},
[41267] = {
commodityId = 41267,
commodityName = "空野之啼手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530212
},
suitId = 853
},
[41268] = {
commodityId = 41268,
commodityName = "兰花倩影手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530213
},
suitId = 855
},
[41269] = {
commodityId = 41269,
commodityName = "深谷幽蔷手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530214
},
suitId = 857
},
[41270] = {
commodityId = 41270,
commodityName = "碧竹丛意手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530215
},
suitId = 859
},
[41271] = {
commodityId = 41271,
commodityName = "律动多巴胺手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530216
},
suitId = 871
},
[41272] = {
commodityId = 41272,
commodityName = "像素叠叠乐手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530217
},
suitId = 873
},
[41273] = {
commodityId = 41273,
commodityName = "火树银花手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530218
},
suitId = 875
},
[41274] = {
commodityId = 41274,
commodityName = "数字边缘手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530219
},
suitId = 877
},
[41275] = {
commodityId = 41275,
commodityName = "淡淡甜意手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530220
},
suitId = 879
},
[41276] = {
commodityId = 41276,
commodityName = "道士下山手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530221
},
suitId = 881
},
[41277] = {
commodityId = 41277,
commodityName = "甜蜜梦乡手套",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530222
},
bOpenSuit = true,
suitId = 889
},
[41278] = {
commodityId = 41278,
commodityName = "花间睡服手套",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530223
},
bOpenSuit = true,
suitId = 891
},
[41279] = {
commodityId = 41279,
commodityName = "星河入梦手套",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530224
},
bOpenSuit = true,
suitId = 893
},
[41280] = {
commodityId = 41280,
commodityName = "无贝不宝手套",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530225
},
bOpenSuit = true,
suitId = 897
},
[41281] = {
commodityId = 41281,
commodityName = "无宝不贝手套",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = "印章祈愿",
minVersion = v5,
itemIds = {
530226
},
bOpenSuit = true,
suitId = 899
},
[41282] = {
commodityId = 41282,
commodityName = "森猎原野手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530227
},
suitId = 901
},
[41283] = {
commodityId = 41283,
commodityName = "像素小红狐手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530228
},
suitId = 915
},
[41284] = {
commodityId = 41284,
commodityName = "流浪幽咪手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530229
},
suitId = 931
},
[41285] = {
commodityId = 41285,
commodityName = "半糖青春手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530230
},
suitId = 933
},
[41286] = {
commodityId = 41286,
commodityName = "快快向左手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530231
},
suitId = 935
},
[41287] = {
commodityId = 41287,
commodityName = "快快向右手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530232
},
suitId = 937
},
[41288] = {
commodityId = 41288,
commodityName = "乐在中央手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530233
},
suitId = 939
},
[41289] = {
commodityId = 41289,
commodityName = "酷鲨航海家手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530234
},
suitId = 941
},
[41290] = {
commodityId = 41290,
commodityName = "萌鲨旅行家手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530235
},
suitId = 943
},
[41291] = {
commodityId = 41291,
commodityName = "星梦飞扬手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530236
},
suitId = 945
},
[41292] = {
commodityId = 41292,
commodityName = "紫雾蝶语手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530237
},
suitId = 947
},
[41293] = {
commodityId = 41293,
commodityName = "雾都情书手套",
coinType = 6,
price = 10000,
endTime = v0,
minVersion = v5,
itemIds = {
530242
},
suitId = 913
}
}

local mt = {
mallId = 9,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074768000
},
shopTag = {
2,
10
},
gender = 0,
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data