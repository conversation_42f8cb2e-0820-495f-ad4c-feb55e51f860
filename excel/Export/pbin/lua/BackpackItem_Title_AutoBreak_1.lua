--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_个性化_称号.xlsx: 称号

local v0 = 1

local v1 = 2

local v2 = "CDN:Icon_Designation_Img_003"

local v3 = "CDN:T_Designation_Img_004"

local v4 = "CDN:T_Designation_Img_003"

local data = {
[850118] = {
id = 850118,
quality = 1,
name = "超凡精英",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[850119] = {
id = 850119,
quality = 1,
name = "特种星兵",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
mallJumpId = {
25
},
jumpText = {
"特惠礼包界面"
}
},
[850122] = {
id = 850122,
quality = 1,
name = "极速之星",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[850123] = {
id = 850123,
quality = 1,
name = "王牌战神",
desc = "完成娱乐一下吧活动任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
mallJumpId = {
27
},
jumpText = {
"首充活动界面"
}
},
[850124] = {
id = 850124,
quality = 1,
name = "娱乐大师",
desc = "完成娱乐大师礼任务获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[850125] = {
id = 850125,
name = "顶级人缘",
desc = "完成星动票选赛任务获得",
mallJumpId = {
2
},
jumpText = {
"赛季主界面"
}
},
[850126] = {
id = 850126,
quality = 2,
name = "梦中仙",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
3
},
jumpText = {
"赛季任务界面"
}
},
[850127] = {
id = 850127,
quality = 2,
name = "极限勇者",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
4
},
jumpText = {
"跳转主界面并打开模式选择"
}
},
[850128] = {
id = 850128,
quality = 2,
name = "世界最可爱",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
5
},
jumpText = {
"赠送好友道具"
}
},
[850129] = {
id = 850129,
quality = 2,
name = "星有灵犀",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
6
},
jumpText = {
"源货币购买目标代币礼包弹窗"
}
},
[850130] = {
id = 850130,
quality = 1,
name = "筑梦巨匠",
desc = "达成特定成就后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
showInView = 1,
mallJumpId = {
7
},
jumpText = {
"源货币兑换目标代币弹窗"
}
},
[850131] = {
id = 850131,
quality = 1,
name = "传奇之星",
desc = "达成特定成就后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
showInView = 1,
mallJumpId = {
8
},
jumpText = {
"充值界面"
}
},
[850132] = {
id = 850132,
quality = 1,
name = "星途璀璨",
desc = "达成特定成就后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
showInView = 1,
mallJumpId = {
9
},
jumpText = {
"BP主界面"
}
},
[850133] = {
id = 850133,
quality = 2,
name = "巡游之星",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
10
},
jumpText = {
"任务主界面"
}
},
[850134] = {
id = 850134,
quality = 2,
name = "星海旅人",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
11
},
jumpText = {
"赛季抽奖界面"
}
},
[850135] = {
id = 850135,
quality = 2,
name = "闪电传说",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
12
},
jumpText = {
"活跃币抽奖界面"
}
},
[850136] = {
id = 850136,
quality = 2,
name = "点赞之星",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
13
},
jumpText = {
"金币抽奖界面"
}
},
[850137] = {
id = 850137,
quality = 2,
name = "艺术赞助家",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
14
},
jumpText = {
"大厅"
}
},
[850138] = {
id = 850138,
quality = 2,
name = "世界探索者",
desc = "达成特定成就后获得",
icon = v2,
picture = v4,
showInView = 1,
mallJumpId = {
15
},
jumpText = {
"商城首页"
}
},
[850139] = {
id = 850139,
quality = 2,
name = "潮流天花板",
desc = "潮玩星方向活动获取",
icon = v2,
picture = v3,
showInView = 1,
mallJumpId = {
16
},
jumpText = {
"赛季兑换"
}
},
[850140] = {
id = 850140,
quality = 1,
name = "潮流风向标",
desc = "潮玩星方向活动获取",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
showInView = 1,
mallJumpId = {
18
},
jumpText = {
"个人信息主页"
}
},
[850141] = {
id = 850141,
quality = 1,
name = "时代少年团",
desc = "活动获取",
icon = "CDN:Icon_Designation_Img_007",
picture = "CDN:T_Designation_Img_007",
mallJumpId = {
20
},
jumpText = {
"活动中心首页"
}
},
[850142] = {
id = 850142,
quality = 1,
name = "人见人爱",
desc = "扫码一起玩任务中获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
showInView = 1,
mallJumpId = {
21
},
jumpText = {
"测试跳转"
}
},
[850143] = {
id = 850143,
name = "雪夜冬季",
mallJumpId = {
23
},
jumpText = {
"蘑咕咕抽奖"
}
},
[850144] = {
id = 850144,
quality = 4,
name = "校园新星",
desc = "高校创作者大赛获得",
icon = "CDN:Icon_Designation_Img_001",
picture = "CDN:T_Designation_Img_001",
showInView = 1,
mallJumpId = {
24
},
jumpText = {
"配饰奖池抽奖界面"
}
},
[850145] = {
id = 850145,
quality = 1,
name = "校园明星",
desc = "高校创作者大赛获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3,
showInView = 1,
mallJumpId = {
26
},
jumpText = {
"赛季储蓄界面"
}
},
[850146] = {
id = 850146,
name = "绝对鹰眼",
mallJumpId = {
28
},
jumpText = {
"背包界面"
}
},
[850147] = {
id = 850147,
name = "千万星合约"
},
[859001] = {
id = 859001,
quality = 1,
name = "天选之星",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:Icon_Designation_Img_005",
picture = "CDN:T_Designation_Img_005"
},
[859501] = {
id = 859501,
quality = 1,
name = "万里挑一",
desc = "参加赛事《冠军之星》可以获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[850148] = {
id = 850148,
name = "再见，2023"
},
[850149] = {
id = 850149,
name = "你好，2024"
},
[850150] = {
id = 850150,
name = "追捕大师"
},
[850151] = {
id = 850151,
name = "摸鱼中"
},
[850152] = {
id = 850152,
name = "干饭人"
},
[850153] = {
id = 850153,
name = "冲浪高手"
},
[850154] = {
id = 850154,
name = "变秃也变强"
},
[850155] = {
id = 850155,
name = "超凡冲锋"
},
[850156] = {
id = 850156,
quality = 1,
name = "航天助力官",
desc = "参与腾讯公益满天星活动获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[850157] = {
id = 850157,
name = "勇闯排位赛"
},
[850158] = {
id = 850158,
quality = 2,
name = "冲段至尊星",
icon = v2,
picture = v4
},
[850159] = {
id = 850159,
quality = 1,
name = "排位领路人",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[850160] = {
id = 850160,
quality = 2,
name = "白日梦想家",
icon = v2,
picture = v4
},
[850161] = {
id = 850161,
name = "亿点点进步"
},
[850162] = {
id = 850162,
name = "完成小目标"
},
[850163] = {
id = 850163,
quality = 2,
name = "世界探索者",
icon = v2,
picture = v4
},
[850164] = {
id = 850164,
quality = 1,
name = "环游星世界",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[850165] = {
id = 850165,
quality = 2,
name = "长安小密探",
icon = v2,
picture = v4
},
[850166] = {
id = 850166,
name = "山海奇遇"
},
[850167] = {
id = 850167,
quality = 2,
name = "环游星世界",
icon = v2,
picture = v4
},
[850168] = {
id = 850168,
name = "世界探索者"
},
[850169] = {
id = 850169,
name = "白日梦想家"
},
[850170] = {
id = 850170,
name = "老法师出场"
},
[850171] = {
id = 850171,
quality = 4,
name = "社交牛牛人",
desc = "星动票选赛任务获得"
},
[850172] = {
id = 850172,
quality = 2,
name = "网红设计师",
desc = "达成特定成就后获得",
icon = v2,
picture = v4
},
[850173] = {
id = 850173,
name = "疯狂星骑士"
},
[850174] = {
id = 850174,
quality = 2,
name = "红梅映雪 ",
desc = "山海通行证达到指定等级获得",
icon = v2,
picture = v4,
showInView = 1
},
[850175] = {
id = 850175,
name = "大橘大栗"
},
[850176] = {
id = 850176,
quality = 2,
name = "寻找星搭子",
icon = v2,
picture = v4
},
[850177] = {
id = 850177,
quality = 2,
name = "白日梦想家",
icon = v2,
picture = v4
},
[850178] = {
id = 850178,
name = "山羊座"
},
[850179] = {
id = 850179,
quality = 2,
name = "冬至座",
icon = v2,
picture = v4
},
[850180] = {
id = 850180,
quality = 1,
name = "钻石座",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[850181] = {
id = 850181,
name = "超级QQ星"
},
[850182] = {
id = 850182,
quality = 1,
name = "光之巨人",
desc = "奥特曼祈愿获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[851001] = {
id = 851001,
quality = 1,
name = "传奇英雄",
desc = "夺宝奇星玩法中可以获得",
icon = "CDN:Icon_Designation_Img_005",
picture = "CDN:T_Designation_Img_005"
},
[851002] = {
id = 851002,
name = "万事星龙",
desc = "万事星龙活动获得"
},
[851003] = {
id = 851003,
quality = 1,
name = "星龙守护者",
desc = "星春年兽活动获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[851004] = {
id = 851004,
quality = 2,
name = "赏金猎手",
desc = "夺宝奇星玩法中可以获得",
icon = v2,
picture = v4
},
[851005] = {
id = 851005,
quality = 2,
name = "星宝护卫队",
desc = "光之复苏玩法中可以获得",
icon = v2,
picture = v4
},
[850183] = {
id = 850183,
name = "推理出道"
},
[850184] = {
id = 850184,
quality = 2,
name = "一人千面",
icon = v2,
picture = v4
},
[850185] = {
id = 850185,
name = "过个元梦年"
},
[850186] = {
id = 850186,
name = "社交达人"
},
[850187] = {
id = 850187,
quality = 2,
name = "全宇宙最暖",
icon = v2,
picture = v4
},
[850188] = {
id = 850188,
name = "先遣精锐"
},
[850189] = {
id = 850189,
name = "绝",
icon = "CDN:Icon_Designation_Img_009",
picture = "CDN:T_Designation_Img_009"
},
[850190] = {
id = 850190,
name = "零食小吃货",
desc = "2月10日通过春节集福活动获得",
icon = "CDN:Icon_Designation_Img_010",
picture = "CDN:T_Designation_Img_010"
},
[850191] = {
id = 850191,
name = "快乐说嗨嗨",
icon = "CDN:Icon_Designation_Img_008",
picture = "CDN:T_Designation_Img_008"
},
[850192] = {
id = 850192,
quality = 2,
name = "星想事丞",
icon = v2,
picture = v4
},
[850193] = {
id = 850193,
quality = 2,
name = "元琦满满",
icon = v2,
picture = v4
},
[850194] = {
id = 850194,
quality = 2,
name = "农场勋逻员",
icon = v2,
picture = v4
},
[850195] = {
id = 850195,
name = "先遣精锐"
},
[850196] = {
id = 850196,
name = "相信光"
},
[850197] = {
id = 850197,
quality = 1,
name = "种田王",
desc = "达成特定成就后获得",
icon = "CDN:Icon_Designation_Img_004",
picture = v3
},
[850200] = {
id = 850200,
name = "星梦合伙人",
desc = "内容创作者激励计划获得"
},
[850301] = {
id = 850301,
quality = 1,
name = "星光璀璨",
desc = "超核见面礼，添加超核管家获取",
icon = "CDN:Icon_Designation_Img_005",
picture = "CDN:T_Designation_Img_005"
},
[850302] = {
id = 850302,
quality = 1,
name = "龙行龘龘",
desc = "龙年新春限定称号",
icon = "CDN:Icon_Designation_Img_100",
picture = "CDN:T_Designation_Img_100"
},
[850303] = {
id = 850303,
quality = 2,
name = "第九人生",
icon = v2,
picture = v4
},
[850304] = {
id = 850304,
name = "刀人不眨眼"
},
[850305] = {
id = 850305,
name = "小福尔摩斯"
},
[850306] = {
id = 850306,
name = "我有星搭子"
},
[850307] = {
id = 850307,
name = "眼睛就是尺"
},
[850308] = {
id = 850308,
quality = 2,
name = "就这？",
icon = v2,
picture = v4
},
[850309] = {
id = 850309,
quality = 2,
name = "水瓶座",
icon = v2,
picture = v4
},
[850310] = {
id = 850310,
quality = 2,
name = "双鱼座",
icon = v2,
picture = v4
},
[850311] = {
id = 850311,
name = "人型测谎仪"
},
[850312] = {
id = 850312,
name = "星路仪能手"
},
[850313] = {
id = 850313,
name = "融入背景板"
},
[850314] = {
id = 850314,
name = "反转时机到"
}
}

local mt = {
type = "ItemType_Title",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 4,
itemNum = 10
}
},
quality = 3,
desc = "活动获得",
icon = "CDN:Icon_Designation_Img_002",
picture = "CDN:T_Designation_Img_002",
showInView = 0,
isDynamic = 0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data