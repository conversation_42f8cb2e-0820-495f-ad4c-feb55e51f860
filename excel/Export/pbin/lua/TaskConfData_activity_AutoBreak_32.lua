--com.tencent.wea.xlsRes.table_TaskConf => excel/xls/H_活动中心配置.xlsx: 活动任务

local data = {
[630527] = {
id = 630527,
name = "6.22线索揭秘：全新可定制小红狐痛包等你来领！",
desc = "全新可定制小红狐痛包等你来领！去分享→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10134
}
}
}
}
}
}
},
reward = {
itemIdList = {
3636
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 10134,
taskGroupId = 63837
},
[630528] = {
id = 630528,
name = "6.23线索揭秘：自选经典时装，限时快闪返场！",
desc = "自选经典时装，限时快闪返场！去分享→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10135
}
}
}
}
}
}
},
reward = {
itemIdList = {
3636
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 10135,
taskGroupId = 63838
},
[630529] = {
id = 630529,
name = "6.24线索揭秘：狐利全开，玩法奖励享不停！",
desc = "狐利全开，玩法奖励享不停！去分享→",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 62,
value = 1,
subConditionList = {
{
type = 67,
value = {
10136
}
}
}
}
}
}
},
reward = {
itemIdList = {
3636
},
numList = {
10
},
validPeriodList = {
0
}
},
jumpId = 10136,
taskGroupId = 63839
},
[630530] = {
id = 630530,
name = "【累计】使用星搭子的口令码",
desc = "【累计】使用星搭子的口令码",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1202,
value = 1
}
}
}
},
reward = {
itemIdList = {
3638
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63840
},
[630531] = {
id = 630531,
name = "【累计】我的口令码被使用1次",
desc = "【累计】我的口令码被使用1次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1201,
value = 1
}
}
}
},
reward = {
itemIdList = {
3638
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63841
},
[630532] = {
id = 630532,
name = "【累计】我的口令码被使用3次",
desc = "【累计】我的口令码被使用3次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1201,
value = 3
}
}
}
},
reward = {
itemIdList = {
3638
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63841
},
[630533] = {
id = 630533,
name = "【累计】我的口令码被使用5次",
desc = "【累计】我的口令码被使用5次",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 1201,
value = 5
}
}
}
},
reward = {
itemIdList = {
3638
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63841
},
[630534] = {
id = 630534,
name = "【每周】登录游戏2天",
desc = "【每周】登录游戏2天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 2
}
}
}
},
reward = {
itemIdList = {
3638
},
numList = {
10
},
validPeriodList = {
0
}
},
taskGroupId = 63842
},
[630535] = {
id = 630535,
name = "【每周】登录游戏4天",
desc = "【每周】登录游戏4天",
condition = {
resCompleteConditionGroup = {
condition = {
{
conditionType = 3,
value = 4
}
}
}
},
reward = {
itemIdList = {
3638
},
numList = {
15
},
validPeriodList = {
0
}
},
taskGroupId = 63843
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data