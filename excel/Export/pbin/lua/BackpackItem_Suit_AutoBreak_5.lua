--com.tencent.wea.xlsRes.table_BackpackItem => excel/xls/D_道具表_时装.xlsm: 套装

local v0 = {
{
itemId = 6,
itemNum = 400
}
}

local v1 = 2

local v2 = {
fashionValue = 300
}

local v3 = 10

local v4 = {
seconds = 4101552000
}

local data = {
[402370] = {
id = 402370,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "海之神女 艾蜜莉",
desc = "风浪再大，我也不怕",
icon = "CDN:Icon_OG_013",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_013",
material = "MI_OG_013_1;MI_OG_013_2;MI_OG_013_3;MI_OG_013_4;MI_OG_013_5;MI_OG_013_6;MI_OG_013_7;MI_OG_013_8;MI_OG_013_3",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_013_Physics",
materialSlot = "Skin;Skin_Opaque;Skin_Translucent;Skin_Translucent_02;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Translucent_03;Skin_Translucent_04"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_013_PV/Level_OG_013_Intact",
outIdle = "AS_CH_OutIdle_OG_013",
outShow = "AS_CH_IdleShow_OG_013",
outShowIntervalTime = 15,
soundId = {
4025,
4027
},
outIdle_pv = "LS_PV_OG_013_Idle",
outEnterSequence = "LS_PV_OG_013",
shareTexts = {
"奔赴一场浪漫的夏日海边之约"
},
shareAnim = "AS_CH_Pose_001_OG_013",
beginTime = {
seconds = 1717689600
},
suitStoryTextKey = [[艾蜜莉来自深海的人鱼家族，她拥有一头闪耀的金色长发，透明的鳞片在阳光下折射出绚丽的彩虹色泽。当她游动的时候，周围似乎都在发出柔和的光芒，就像是一颗在海洋中闪耀的明珠。

作为人鱼家族最小的女儿，艾蜜莉从族长那继承了一件宝石——海洋之心，但只有一半。艾蜜莉不曾见过另一半宝石，非常好奇完整的海洋之心会是什么样子，不断地外出去探索寻找线索。

虽然深受宠爱，但艾蜜莉并不是那种娇生惯养的性格。她勇于尝试新事物，对于危险丝毫不惧。艾蜜莉热衷于四处游历，经常会游离出安全的珊瑚礁，去欣赞海底五彩斑斓的景致。有时她会偷偷靠近水面，探出头来，看着远处神秘的陆地，内心充满向往。尽管海底的同伴们总是劝她要小心谨慎，不要轻易接近陆地，但艾蜜莉总是充满勇气和冒险精神，她喜欢结识新朋友，熟知海底的每个角落。有时她甚至会游到陆地附近的礁石上晒晒太阳，偷偷观察海滩上星宝们的生活，对海洋之外的文化和习俗感到着迷。

一次，她游到一处从未探索过的海域探险，看到一个跟自己戴着同样宝石的人鱼，可惜还没看清对方的样貌，艾蜜莉就不小心被一张渔网缠住了。尽管当时陷入险境，但她依然保持镇定，想办法挣脱了出来。可惜再也没遇到过另一半海洋之心了。这次被困住的经历并没有让艾蜜莉感到害怕，反而加重了好奇心，到底是谁拥有着另外一半海洋之心。她在往后的探索行动中更加小心谨慎，再危险的海域也无法阻止她想要探索真相的心。

在不外出探索的日子里，艾蜜莉最大的爱好就是唱歌。她拥有一副动听悦耳的嗓音，经常会在海底唱起动听的歌谣，期待得到回应。她对未知世界充满向往，希望通过自己的歌声和善意，能结识更多新朋友。艾蜜莉相信只要自己坚持探索下去，终有一天她找到另一半拼凑出完整的海洋之心，共同守护好这片广袤的蓝色海洋。]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402370.astc",
suitId = 254,
suitName = "海之神女 艾蜜莉",
bpShowId = 3,
seasonId = 5,
suitIcon = "CDN:Icon_OG_013",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402370.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031",
SeasonShowIdList = {
{
key = 5,
value = 3
}
}
},
[402371] = {
id = 402371,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "海之神女 艾蜜莉",
desc = "风浪再大，我也不怕",
icon = "CDN:Icon_OG_013_01",
outlookConf = {
belongTo = 402370,
fashionValue = 125,
belongToGroup = {
402371,
402372
}
},
resourceConf = {
model = "SK_OG_013",
material = "MI_OG_013_1_HP01;MI_OG_013_2_HP01;MI_OG_013_3_HP01;MI_OG_013_4_HP01;MI_OG_013_5_HP01;MI_OG_013_6_HP01;MI_OG_013_7_HP01;MI_OG_013_8_HP01;MI_OG_013_3_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_013_Physics",
materialSlot = "Skin;Skin_Opaque;Skin_Translucent;Skin_Translucent_02;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Translucent_03;Skin_Translucent_04"
},
commodityId = 11296,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_013_PV/Level_OG_013_Intact",
outIdle = "AS_CH_OutIdle_OG_013",
outShow = "AS_CH_IdleShow_OG_013_HP01",
outShowIntervalTime = 15,
soundId = {
4025,
4027
},
outIdle_pv = "LS_PV_OG_013_HP01_Idle",
outEnterSequence = "LS_PV_OG_013_HP01",
shareTexts = {
"奔赴一场浪漫的夏日海边之约"
},
shareAnim = "AS_CH_Pose_001_OG_013",
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402371.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402370.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[402372] = {
id = 402372,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "海之神女 艾蜜莉",
desc = "风浪再大，我也不怕",
icon = "CDN:Icon_OG_013_02",
outlookConf = {
belongTo = 402370,
fashionValue = 125,
belongToGroup = {
402371,
402372
}
},
resourceConf = {
model = "SK_OG_013",
material = "MI_OG_013_1_HP02;MI_OG_013_2_HP02;MI_OG_013_3_HP02;MI_OG_013_4_HP02;MI_OG_013_5_HP02;MI_OG_013_6_HP02;MI_OG_013_7_HP02;MI_OG_013_8_HP02;MI_OG_013_3_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_013_Physics",
materialSlot = "Skin;Skin_Opaque;Skin_Translucent;Skin_Translucent_02;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Translucent_03;Skin_Translucent_04"
},
commodityId = 11297,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_013_PV/Level_OG_013_Intact",
outIdle = "AS_CH_OutIdle_OG_013",
outShow = "AS_CH_IdleShow_OG_013_HP02",
outShowIntervalTime = 15,
soundId = {
4025,
4027
},
outIdle_pv = "LS_PV_OG_013_HP02_Idle",
outEnterSequence = "LS_PV_OG_013_HP02",
shareTexts = {
"奔赴一场浪漫的夏日海边之约"
},
shareAnim = "AS_CH_Pose_001_OG_013",
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402372.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402370.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[402380] = {
id = 402380,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "海之王 埃瑞克",
desc = "我的秘密都藏在深海里了",
icon = "CDN:Icon_OG_014",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_014",
material = "MI_OG_014_1;MI_OG_014_2;MI_OG_014_3;MI_OG_014_4;MI_OG_014_5;MI_OG_014_6;MI_OG_014_7",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_014_Physics",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Opaque_04;Skin_Translucent;Skin_Opaque_05",
IconLabelId = 101
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_013_PV/Level_OG_013_Intact",
outIdle = "AS_CH_OutIdle_OG_014",
outShow = "AS_CH_IdleShow_OG_014",
outShowIntervalTime = 15,
soundId = {
4024,
4026
},
outIdle_pv = "LS_PV_OG_014_Idle",
outEnterSequence = "LS_PV_OG_014",
shareTexts = {
"这个夏天，一起去看海吧"
},
shareAnim = "AS_CH_Pose_001_OG_014",
beginTime = {
seconds = 1717689600
},
suitStoryTextKey = [[埃瑞克从小就被赋予了维护海洋平衡的重任。他拥有一身闪耀如盔甲的鱼鳞，散发着果敢刚强的力量感。当他在海底巡游的时候，周围的海洋生物们都会自觉为他让开道路，眼神中充满崇敬和恐惧。

性格稳重成熟的埃瑞克，在海底拥有崇高的地位和极高的威望。他博学多才，对海洋的知识了如指掌，常常为其他星宝解答各种疑问。同时，他也展现出出色的领导力，能够果断地做出正确决策，应对各种威胁。即便是在最危机的时刻，他也能保持镇定，沉着应对。

然而，人鱼家族中个别晚辈会觉得埃瑞克过于冷酷不可接近，但其实这只是埃瑞克的一种自我保护机制。作为海洋守护者，他必须时刻保持清醒的头脑，不能被感情冲动所影响。因为埃瑞克其实肩负着一件重要的使命——保护海洋之心宝石。埃瑞克不清楚这件神秘的海洋之心具体能带来什么力量，只知道是维系整个海底世界平衡的关键所在。相传人鱼家族曾遭到袭击，海洋之心差点落入坏蛋手中。为了保护这件至关重要的宝石，人鱼家族不得不分裂成了两个家族，各自保管着它的一半。

埃瑞克不想让悲剧重现，他时刻保持着警惕，让海洋之心不再被任何邪恶势力所窃取。他经常会深入海底最黑暗的区域巡逻，追捕那些企图盗取海洋之心的海妖和海盗。在这场永无休止的战斗中，他展现出非凡的力量和战斗技巧，令所有敌人闻风丧胆。埃瑞克不断提升自己的能力，他相信只有自己变得更强，才能在面对下一次危机的时候泰然自若。

但实际上埃瑞克私下十分温柔，常常主动帮助受伤的小鱼找到安全的藏身之处，引导迷路的海洋生物回家。埃瑞克有时会在空旷隐秘的海底角落独自吟唱，只有月光见过他最柔和的一面。

在一次海底巡游的过程中，偶然听闻另外一半的海洋之心在一个叫艾蜜莉的海之神女手中。埃瑞克期待着有一天能与她相见，将两片海洋之心拼凑起来，共同守护这片挚爱的海洋。
]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402380.astc",
suitId = 255,
suitName = "海之王 埃瑞克",
suitIcon = "CDN:Icon_OG_014",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402380.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402280.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_031"
},
[402390] = {
id = 402390,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "鼠朵朵",
desc = "显露大耳朵，大胆做自己",
icon = "CDN:Icon_Body_028",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_028",
upper = "SK_Body_Upper_028",
bottom = "SK_Body_Under_028",
gloves = "SK_Body_Hands_028",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"侧耳倾听，跟我同频的你在哪里？"
},
beginTime = v4,
suitId = 256,
suitName = "鼠朵朵",
suitIcon = "CDN:Icon_Body_028"
},
[402400] = {
id = 402400,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "布朗熊",
desc = "千万别以为我很冷漠，其实我很可靠",
icon = "CDN:Icon_PL_099",
getWay = "时光小船",
jumpId = {
601
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_099",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_099_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_099",
outShow = "AS_CH_IdleShow_PL_099",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_099",
shareTexts = {
"祝你的生活每一天都充满爱与惊喜！"
},
shareAnim = "AS_CH_Pose_001_PL_099",
beginTime = {
seconds = 1718294400
},
sharePic = "T_Share_Suit_402400.astc",
suitId = 257,
suitName = "布朗熊",
themedId = 12,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_099",
shareNamePic = "T_Share_Suit_name_402400.astc",
shareBubblePic = "T_Share_Suit_frame_402400.astc",
ThemedShowIdList = {
{
key = 12,
value = 2
}
}
},
[402410] = {
id = 402410,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "可妮兔",
desc = "和你一起，就是最浪漫的事",
icon = "CDN:Icon_PL_100",
getWay = "时光小船",
jumpId = {
602
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_100",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_100_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_100",
outShow = "AS_CH_IdleShow_PL_100",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_100",
shareTexts = {
"每天开开心心，就是我的目标！"
},
shareAnim = "AS_CH_Pose_001_PL_100",
beginTime = {
seconds = 1718294400
},
sharePic = "T_Share_Suit_402410.astc",
suitId = 258,
suitName = "可妮兔",
themedId = 12,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_100",
shareNamePic = "T_Share_Suit_name_402410.astc",
shareBubblePic = "T_Share_Suit_frame_402410.astc",
ThemedShowIdList = {
{
key = 12,
value = 1
}
}
},
[402420] = {
id = 402420,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "Hello Kitty",
desc = "点缀充满活力的每一天",
icon = "CDN:Icon_PL_115",
getWay = "酷洛米",
jumpId = {
1050
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_115",
material = "MI_PL_115_1;MI_PL_115_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_115_Physics",
materialSlot = "Skin;Skin_1",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_115",
outShow = "AS_CH_IdleShow_PL_115",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_115",
shareTexts = {
"今天穿上了Hello Kitty的衣服很开心"
},
shareAnim = "AS_CH_Pose_001_PL_115",
beginTime = {
seconds = 1720108800
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402420.astc",
suitId = 259,
suitName = "Hello Kitty",
themedId = 13,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_115",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402420.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402420.astc",
ThemedShowIdList = {
{
key = 13,
value = 1
}
}
},
[402430] = {
id = 402430,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "Kuromi",
desc = "真是无可抵挡的魅力",
icon = "CDN:Icon_PL_116",
getWay = "酷洛米",
jumpId = {
1050
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_116",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_116_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_116",
outShow = "AS_CH_IdleShow_PL_116",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_116",
shareTexts = {
"今天穿上了Kuromi的衣服很开心"
},
shareAnim = "AS_CH_Pose_001_PL_116",
beginTime = {
seconds = 1720108800
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402430.astc",
suitId = 260,
suitName = "Kuromi",
themedId = 13,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_116",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402430.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402430.astc",
ThemedShowIdList = {
{
key = 13,
value = 2
}
}
},
[402440] = {
id = 402440,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "庆典小子 嘉嘉",
desc = "我们手牵手，向着庆典走！",
icon = "CDN:Icon_PL_107",
outlookConf = v2,
resourceConf = {
model = "SK_PL_107",
material = "MI_PL_107_1;MI_PL_107_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_107",
outShow = "AS_CH_IdleShow_PL_107",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_107",
shareTexts = {
"星星相映，共谱华章"
},
shareAnim = "AS_CH_Pose_001_PL_107",
beginTime = v4,
suitStoryTextKey = [[嘉嘉非常喜欢参加庆典，他活力十足，热情满溢，有他在的庆典总会妙处横生，惊喜十足。

夜幕降临时，他在河流上游放下静谧的荷叶灯，灯影交错，星影戳戳。越来越多的星宝来到庆典上，嘉嘉悄然穿过，小手一挥，星宝们发现自己的衣服竟都变成了精美典雅的华服！若是不满意嘛，还有许多旗袍、汉服任君选择，只管跟嘉嘉说便是。

庆典舞台中央的小星宝们总是精力无限，嘉嘉也不甘示弱上台，鼓声响起，他们一起随着节奏舞动起来，来看看谁才是最炫酷的舞者！嘉嘉还会兼职摄影师为大家拍摄精美的照片，全部免费！

偶尔会有迷路的小星宝哭个不停，热心的嘉嘉会掏出口袋里的竹签，小手一挥，竟生出了棉花糖！等小星宝吃开心了，嘉嘉也带他找到了家人。不知谁把这个消息透了出去，欲离开的嘉嘉忽然被一大群星宝围住要吃棉花糖，嘉嘉哈哈一笑，洒下一把粉尘，蓬松的棉花糖就像云朵一样铺开，好不壮观！

待到星宝们散去，嘉嘉怀中已经装满了各种食物：糖葫芦，果子，各种糕点……他灵机一动，把这些食物一顿折腾加进了蛋糕中，做了一份巧妙的中式蛋糕，大快朵颐！

躺在河边看着星宝们满足的面容，嘉嘉心满意足。或许该留点蛋糕给大家吃的，但是他太饿了，等到下次庆典，他要做一个超级大蛋糕分给大家，至于现在嘛，嘘，他已经累得睡着啦！]],
suitId = 261,
suitName = "庆典小子 嘉嘉",
suitIcon = "CDN:Icon_PL_107"
},
[402441] = {
id = 402441,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "庆典小子 嘉嘉",
desc = "我们手牵手，向着庆典走！",
icon = "CDN:Icon_PL_107_01",
outlookConf = {
belongTo = 402440,
fashionValue = 35,
belongToGroup = {
402441,
402442
}
},
resourceConf = {
model = "SK_PL_107",
material = "MI_PL_107_1_HP01;MI_PL_107_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin;Skin_1"
},
commodityId = 11305,
outEnter = "AS_CH_Enter_PL_107",
outShow = "AS_CH_IdleShow_PL_107",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_107",
shareTexts = {
"星星相映，共谱华章"
},
shareAnim = "AS_CH_Pose_001_PL_107"
},
[402442] = {
id = 402442,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "庆典小子 嘉嘉",
desc = "我们手牵手，向着庆典走！",
icon = "CDN:Icon_PL_107_02",
outlookConf = {
belongTo = 402440,
fashionValue = 35,
belongToGroup = {
402441,
402442
}
},
resourceConf = {
model = "SK_PL_107",
material = "MI_PL_107_1_HP02;MI_PL_107_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin;Skin_1"
},
commodityId = 11306,
outEnter = "AS_CH_Enter_PL_107",
outShow = "AS_CH_IdleShow_PL_107",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_107",
shareTexts = {
"星星相映，共谱华章"
},
shareAnim = "AS_CH_Pose_001_PL_107"
},
[402450] = {
id = 402450,
effect = true,
name = "红孩儿",
desc = "双手绰枪威凛冽，祥光护体出门来",
icon = "CDN:Icon_BU_126",
resourceConf = {
model = "SK_BU_126",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"初生牛续不怕虎"
},
beginTime = v4,
suitId = 262,
suitName = "红孩儿",
suitIcon = "CDN:Icon_BU_126"
},
[402451] = {
id = 402451,
effect = true,
name = "红孩儿",
desc = "双手绰枪威凛冽，祥光护体出门来",
icon = "CDN:Icon_BU_126_01",
outlookConf = {
belongTo = 402450,
fashionValue = 25,
belongToGroup = {
402451
}
},
resourceConf = {
model = "SK_BU_126",
material = "MI_BU_126_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11308,
shareTexts = {
"初生牛续不怕虎"
}
},
[402460] = {
id = 402460,
effect = true,
exceedReplaceItem = {
{
itemId = 213,
itemNum = 3
}
},
quality = 1,
name = "耀战神 达达尼亚",
desc = "剑的光芒来自于信心",
icon = "CDN:Icon_OG_016",
getWay = "战神颂歌",
jumpId = {
800
},
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_016",
material = "MI_OG_016_1;MI_OG_016_2;MI_OG_016_3;MI_OG_016_4;MI_OG_016_5;MI_OG_016_6;MI_OG_016_7;MI_OG_016_8",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_016_Physics",
materialSlot = "Skin;Skin_Opaque;Skin_Opaque01;Skin_Opaque02;Skin_Translucent_01;Skin_Translucent_02;Skin_Translucent_03;Skin_Translucent_04"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_015_PV/Level_OG_015_01_Intact",
outIdle = "AS_CH_OutIdle_OG_016",
outShow = "AS_CH_IdleShow_OG_016",
outShowIntervalTime = 15,
soundId = {
4029,
4032
},
outIdle_pv = "LS_PV_OG_016_Idle",
outEnterSequence = "LS_PV_OG_016",
shareTexts = {
"和平是我毕生所愿"
},
shareAnim = "AS_CH_Pose_001_OG_016",
beginTime = {
seconds = 1718899200
},
suitStoryTextKey = [[在遥远的伊利西亚大陆，有一个名叫达达尼亚的年轻战士，他出身显赫，自小便被寄予厚望，背负着守护大陆的使命。为此，达达尼亚无一刻不在刻苦训练，剑术、骑术、战略……每一项都力求精进。然而，尽管被星宝们视作榜样，他却总觉得自己的技艺还未达到完美的境地。

正值伊利西亚陷入空前的危机之际，恐惧巫师以不可抵挡的力量降临王国。作为家族的长子，达达尼亚义无反顾地承担起守护星宝家园的重任。然而，面对如此强大的敌人，他内心的焦虑与不安愈发严重，生怕自己无法胜任这一艰巨使命。

就在此时，家族的长老将一把古朴的剑交到达达尼亚手中，神秘地说道：“这把剑拥有无穷的力量，在战斗中能够爆发出星辰般闪耀的光芒。孩子，这把剑一定能够助你战胜恐惧巫师！”

受到长老的鼓舞，达达尼亚紧握着手中的剑，毅然踏上了对抗恐惧巫师的征程。恐惧巫师散布的气场令人不寒而栗，但达达尼亚深知自己手握着无坚不摧的宝剑，便不再畏惧。他从未觉得自己如此臻近完美，每一次攻击都精准无误，并且越战越勇，到了最后，手中的宝剑竟真的绽放出星辰一般闪耀的光芒。恐惧巫师被这无比强大的光明力量所震慑，连动作都变得迟缓，达达尼亚抓住机会，一举击败了对手。

凯旋而归的达达尼亚将剑归还给长老，感激地说：“多亏了这把神奇的剑，我才能战胜强敌。”然而，长老却微笑着摇了摇头：“孩子，这把剑并非是什么神兵利器，它原本只是一把普通的剑。它的光芒来自于你的信心和勇气，是你赋予了它无尽的力量，使它成为了传说中的耀之剑。”

从此，这把剑便以“耀之剑”之名传颂于世，成为了伊利西亚大陆上永恒的传说。而达达尼亚也成为了星宝们歌颂的传奇英雄——耀战神。]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "T_Share_Suit_402461.astc",
suitId = 263,
suitName = "耀战神 达达尼亚",
themedId = 18,
bpShowId = 1,
suitIcon = "CDN:Icon_OG_016",
shareNamePic = "T_Share_Suit_name_402460.astc",
shareBubblePic = "T_Share_Suit_frame_402460.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
ThemedShowIdList = {
{
key = 18,
value = 1
}
}
},
[402470] = {
id = 402470,
effect = true,
exceedReplaceItem = {
{
itemId = 213,
itemNum = 3
}
},
quality = 1,
name = "焰战神  波尔托斯",
desc = "心之烈焰，永不熄灭",
icon = "CDN:Icon_OG_015",
getWay = "战神颂歌",
jumpId = {
800
},
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_015",
material = "MI_OG_015_1;MI_OG_015_2;MI_OG_015_3;MI_OG_015_4;MI_OG_015_5;MI_OG_015_6;MI_OG_015_7;MI_OG_015_8",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_015_Physics",
materialSlot = "Skin;Skin_Opaque;Skin_Opaque01;Skin_Opaque02;Skin_Translucent_01;Skin_Translucent_02;Skin_Translucent_03;Skin_Translucent_04"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_015_PV/Level_OG_015_01_Intact",
outIdle = "AS_CH_OutIdle_OG_015",
outShow = "AS_CH_IdleShow_OG_015",
outShowIntervalTime = 15,
soundId = {
4028,
4031
},
outIdle_pv = "LS_PV_OG_015_Idle",
outEnterSequence = "LS_PV_OG_015",
shareTexts = {
"愿做火引，燃向黎明"
},
shareAnim = "AS_CH_Pose_001_OG_015",
beginTime = {
seconds = 1718899200
},
suitStoryTextKey = [[在伊利西亚战士家族中，波尔托斯作为第二个孩子，自小便表现出过人的聪慧与勤奋。然而，他心中却隐藏着一个不为人知的小秘密——每当情绪激动时，他便会不自觉地引发周围温度的升高，甚至曾因此引发过家中的一场大火。对于这个秘密，波尔托斯深感恐惧，他不敢向家族的长老们坦白，只能努力压抑自己的力量，尽量不与他人亲近。这使得他在星宝们的眼中显得孤僻而冷漠，与风度翩翩的哥哥达达尼亚形成了鲜明的对比。

当伊利西亚大陆被黑巫师入侵时，波尔托斯的大哥达达尼亚挺身而出，代表圣骑士勇敢地与恐惧巫师展开了激战。然而，在胜利的消息尚未传来之际，一位名为虚伪的巫师已悄然潜入了星宝的家园。

面对如此危机，波尔托斯义无反顾地站了出来。他与虚伪巫师展开了一场惊心动魄的战斗。在战斗中，虚伪巫师企图利用波尔托斯的秘密来威胁他，声称若他敢使用火焰之力，那么他小时候火烧城堡的事情将会被公之于众，他也将被视为异类而遭到所有星宝的唾弃。

然而，为了守护星宝家园的安全，波尔托斯毅然彻底释放了自己的火焰力量。熊熊烈焰在他的宝剑上燃烧起来，虚伪巫师无法抵抗这股力量，仓皇败走。随着战斗的胜利结束，围观的星宝们爆发出雷鸣般的掌声和欢呼声。

事后，波尔托斯鼓起勇气向家族长老坦白了小时候的过失。长老听后微笑着说：“孩子，我早就知道你小时候的那场火灾是无心之失。这些年来，我们一直在等待你勇敢地面对自己的内心并承认这个秘密。你是拥有焰之天命的骑士，烈焰的力量可以对抗黑暗，但失控时也会带来灾祸。这样的力量，只有你能够真诚面对内心时，才能真正掌握它。”

从那以后，波尔托斯以“焰战神”的美誉传遍了整个伊利西亚大陆。而他手中的宝剑也被星宝们尊称为“焰之剑”，成为了象征着他真诚与果敢的传奇武器。]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "T_Share_Suit_402471.astc",
suitId = 264,
suitName = "焰战神  波尔托斯",
themedId = 18,
bpShowId = 3,
suitIcon = "CDN:Icon_OG_015",
shareNamePic = "T_Share_Suit_name_402470.astc",
shareBubblePic = "T_Share_Suit_frame_402470.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
ThemedShowIdList = {
{
key = 18,
value = 3
}
}
},
[402480] = {
id = 402480,
effect = true,
exceedReplaceItem = {
{
itemId = 213,
itemNum = 5
}
},
quality = 1,
name = "灭战神 阿多斯",
desc = "厄逆之命，又能奈我何？",
icon = "CDN:Icon_OG_017",
getWay = "战神颂歌",
jumpId = {
800
},
outlookConf = {
fashionValue = 1800
},
resourceConf = {
model = "SK_OG_017",
material = "MI_OG_017_1;MI_OG_017_2;MI_OG_017_3;MI_OG_017_4;MI_OG_017_5;MI_OG_017_6;MI_OG_017_7;MI_OG_017_8",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_017_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06;Skin_07;Skin_08",
IconLabelId = 101
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_017_PV/Level_OG_017_Intact",
outIdle = "AS_CH_OutIdle_OG_017",
outShow = "AS_CH_IdleShow_OG_017",
outShowIntervalTime = 15,
soundId = {
4030,
4033
},
outIdle_pv = "LS_PV_OG_017_Idle",
outEnterSequence = "LS_PV_OG_017",
shareTexts = {
"他们被命运眷顾，而我令命运畏惧！"
},
shareAnim = "AS_CH_Pose_001_OG_017",
beginTime = {
seconds = 1718899200
},
suitStoryTextKey = [[在遥远的伊利西亚大陆，战士家族中的第三个孩子——阿多斯，自出生之日起便背负着沉重的命运。预言家断言他是厄逆之命，会给家族带来灾难。因此阿多斯便被送出了城堡，并在路途中被黑巫师劫走，从此下落不明。

黑巫师将阿多斯抚养长大，天资过人的他学得了一身超卓的本领。然而，他的心中始终充满了对命运的怨恨。每当黑巫师试图引导他走向正道时，他总是以“反正我是厄逆之命”为由，自暴自弃。

一次，阿多斯遇到了一群流寇。因他的长相酷似兄长达达尼亚，流寇将他认成耀战神，企图挑起争端。阿多斯凭借过人的武艺击败了流寇，赢得了星宝们的尊敬与赞誉。但阿多斯认为这一定是星宝将他认成了耀战神，当他准备离去时，星宝们恳求他留下姓名，以便为他撰写英雄传。阿多斯有些错愕，原来，“厄逆之命”的他，也能成为人们心中的英雄。

受到这次经历的启发，阿多斯决定重塑自己的命运。黑巫师鼓励他：“人的命运是由自己决定的，不应该因为他人的偏见而自暴自弃，只要你真心帮助别人，自然会获得他人的敬仰。” 

为了化解两位兄长的心魔，阿多斯派出恐惧巫师和虚伪巫师分别向他们发起挑战，帮助他们找回了真正的自我。之后，阿多斯戴着面具出现在他们面前，一场惊心动魄的决战在所难免。

在这场世纪之战中，三位圣战士都倾尽全力。达达尼亚的耀之剑如星辰般璀璨夺目；波尔托斯的焰之剑燃烧着熊熊烈焰；而阿多斯的灭之剑则遇强更强，以其独特的黑色剑影，将命运加诸于他的不公化为反抗的力量。

族中的长老认出了眼前的敌人便是拥有厄逆之命的第三个孩子，叫出了他的名字。达达尼亚和波尔托斯也终于明白了弟弟的良苦用心，他们真诚地邀请阿多斯回归家族。然而，阿多斯却拒绝了兄长的邀请。他决定带着灭之剑去更广阔的天地闯荡，用自己的行动证明命运并非不可改变。

从此，阿多斯“灭战神”之名传遍大陆。他亦正亦邪的形象深入人心，但那些曾受过他帮助的星宝们都知道，这位看似冷酷的少年其实拥有一颗炽热的心。
]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "T_Share_Suit_402481.astc",
suitId = 265,
suitName = "灭战神 阿多斯",
themedId = 18,
bpShowId = 2,
suitIcon = "CDN:Icon_OG_017",
shareNamePic = "T_Share_Suit_name_402480.astc",
shareBubblePic = "T_Share_Suit_frame_402480.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_032",
ThemedShowIdList = {
{
key = 18,
value = 2
}
}
},
[402490] = {
id = 402490,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "糖豆骑士",
desc = "冒险是我的天职",
icon = "CDN:Icon_PL_111",
outlookConf = v2,
resourceConf = {
model = "SK_PL_111",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_111_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_111",
outShow = "AS_CH_IdleShow_PL_111",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_111",
shareTexts = {
"守护你，是我的荣幸"
},
shareAnim = "AS_CH_Pose_001_PL_111",
beginTime = v4,
sharePic = "T_Share_Suit_402490.astc",
suitId = 266,
suitName = "糖豆骑士",
suitIcon = "CDN:Icon_PL_111",
shareNamePic = "T_Share_Suit_name_402490.astc",
shareBubblePic = "T_Share_Suit_frame_402490.astc"
},
[402500] = {
id = 402500,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "薯宝",
desc = "脆香薯宝，百变风味",
icon = "CDN:Icon_PL_112",
outlookConf = v2,
resourceConf = {
model = "SK_PL_112",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_112_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_112",
outShow = "AS_CH_IdleShow_PL_112",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_112",
shareTexts = {
"薯条在手，快乐我有！"
},
shareAnim = "AS_CH_Pose_001_PL_112",
beginTime = v4,
sharePic = "T_Share_Suit_402500.astc",
suitId = 267,
suitName = "薯宝",
suitIcon = "CDN:Icon_PL_112",
shareNamePic = "T_Share_Suit_name_402500.astc",
shareBubblePic = "T_Share_Suit_frame_402500.astc"
},
[402510] = {
id = 402510,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "糖豆人",
desc = "大明星来啰！",
icon = "CDN:Icon_PL_113",
outlookConf = v2,
resourceConf = {
model = "SK_PL_113",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_113_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_113",
outShow = "AS_CH_IdleShow_PL_113",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_113",
shareTexts = {
"就等你了！"
},
shareAnim = "AS_CH_Pose_001_PL_113",
beginTime = v4,
sharePic = "T_Share_Suit_402510.astc",
suitId = 268,
suitName = "糖豆人",
suitIcon = "CDN:Icon_PL_113",
shareNamePic = "T_Share_Suit_name_402510.astc",
shareBubblePic = "T_Share_Suit_frame_402510.astc"
},
[402520] = {
id = 402520,
effect = true,
name = "巨蟹星",
desc = "生于夏日的巨蟹星，温柔感性，充满爱心",
icon = "CDN:Icon_BU_125",
resourceConf = {
model = "SK_BU_125",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"是蟹壳，也是铠甲"
},
beginTime = v4,
suitId = 269,
suitName = "巨蟹星",
suitIcon = "CDN:Icon_BU_125"
},
[402521] = {
id = 402521,
effect = true,
name = "巨蟹星",
desc = "生于夏日的巨蟹星，温柔感性，充满爱心",
icon = "CDN:Icon_BU_125_01",
outlookConf = {
belongTo = 402520,
fashionValue = 25,
belongToGroup = {
402521
}
},
resourceConf = {
model = "SK_BU_125",
material = "MI_BU_125_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11316,
shareTexts = {
"是蟹壳，也是铠甲"
}
},
[402530] = {
id = 402530,
effect = true,
exceedReplaceItem = {
{
itemId = 3,
itemNum = 6
}
},
name = "清柠柠",
desc = "来一口酸酸甜甜的柠檬，整个夏日清爽愉快",
icon = "CDN:Icon_BU_124",
getWay = "战神颂歌",
jumpId = {
800
},
resourceConf = {
model = "SK_BU_124",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"真可爱，我酸了"
},
beginTime = {
seconds = 1718899200
},
suitId = 270,
suitName = "清柠柠",
suitIcon = "CDN:Icon_BU_124"
},
[402531] = {
id = 402531,
effect = true,
name = "清柠柠",
desc = "来一口酸酸甜甜的柠檬，整个夏日清爽愉快",
icon = "CDN:Icon_BU_124_01",
outlookConf = {
belongTo = 402530,
fashionValue = 25,
belongToGroup = {
402531
}
},
resourceConf = {
model = "SK_BU_124",
material = "MI_BU_124_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11318,
shareTexts = {
"真可爱，我酸了"
}
},
[402540] = {
id = 402540,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "七彩梦羽 妮可",
desc = "找到自我的那一刻，我获得了七彩的羽翼",
icon = "CDN:Icon_PL_108",
outlookConf = v2,
resourceConf = {
model = "SK_PL_108",
material = "MI_PL_108;MI_PL_108_1",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_108_Physics",
materialSlot = "Skin;Skin_02"
},
outEnter = "AS_CH_Enter_PL_108",
outShow = "AS_CH_IdleShow_PL_108",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_108",
shareTexts = {
"穿越暗流，游向繁星"
},
shareAnim = "AS_CH_Pose_001_PL_108",
beginTime = v4,
suitStoryTextKey = [[妮可出生在一个海蛞蝓家族，她的兄弟姐妹们都拥有斑斓美丽的壳，只有她的壳是圆圆的，没有一丝花纹，这使她在以美貌著称的家族里受尽冷落。

为了不让自己的家族蒙羞，妮可开始了漫无目的的流浪，她总是很怕热，想寻找更冷的海，一只温柔的章鱼告诉她，极北之地的冰海可能是她的归宿，但路途遥远且困难重重。

妮可再次启程，路过的鱼群嘲笑她丑陋的圆壳，像礁石一样的石头鱼差点将她囚禁，她穿越暗流，躲过热泉的爆发，经过无数次的努力和坚持，终于来到了传说中的冰海。

这里的海水清澈见底，冰层悠悠浮动，妮可感到前所未有的凉爽。无数美丽的生物在水中翩翩起舞，像海底的星辰一样晶莹剔透，流光溢彩。

是冰海天使！妮可无法自控地向她们游去，却又担忧她们会嘲笑自己的丑陋。

然而，就在这时，妮可的壳开始脱落。她的身体变得透明轻盈，两翼如天使羽翼般展开，身体流溢着七彩光华。她惊讶地发现，原来自己不是海蛞蝓，而是与她们一样，是一只美丽的冰海天使！

冰海天使向她围过来，为首的温柔地说：“孩子，你是一只真正的冰海天使。你游到这里，一定吃了很多苦。祝贺你，找到了自己的家园，也找到了真正的自我。”
]],
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402540.astc",
suitId = 271,
suitName = "七彩梦羽 妮可",
suitIcon = "CDN:Icon_PL_108",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402540.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402540.astc"
},
[402541] = {
id = 402541,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "七彩梦羽 妮可",
desc = "找到自我的那一刻，我获得了七彩的羽翼",
icon = "CDN:Icon_PL_108_01",
outlookConf = {
belongTo = 402540,
fashionValue = 35,
belongToGroup = {
402541,
402542
}
},
resourceConf = {
model = "SK_PL_108",
material = "MI_PL_108_HP01;MI_PL_108_1_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_108_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11320,
outEnter = "AS_CH_Enter_PL_108",
outShow = "AS_CH_IdleShow_PL_108",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_108",
shareTexts = {
"穿越暗流，游向繁星"
},
shareAnim = "AS_CH_Pose_001_PL_108",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402540.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402540.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402540.astc"
},
[402542] = {
id = 402542,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "七彩梦羽 妮可",
desc = "找到自我的那一刻，我获得了七彩的羽翼",
icon = "CDN:Icon_PL_108_02",
outlookConf = {
belongTo = 402540,
fashionValue = 35,
belongToGroup = {
402541,
402542
}
},
resourceConf = {
model = "SK_PL_108",
material = "MI_PL_108_HP02;MI_PL_108_1_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_108_Physics",
materialSlot = "Skin;Skin_02"
},
commodityId = 11321,
outEnter = "AS_CH_Enter_PL_108",
outShow = "AS_CH_IdleShow_PL_108",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_108",
shareTexts = {
"穿越暗流，游向繁星"
},
shareAnim = "AS_CH_Pose_001_PL_108",
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402540.astc",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402540.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402540.astc"
},
[402550] = {
id = 402550,
effect = true,
name = "战斗法师",
desc = "我会守护好超能裂隙的！",
icon = "CDN:Icon_BU_097",
getWay = "特惠礼包",
jumpId = {
25
},
resourceConf = {
model = "SK_BU_097",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"尝尝陷阱的厉害！"
},
beginTime = {
seconds = 1719504000
},
suitId = 272,
suitName = "战斗法师",
suitIcon = "CDN:Icon_BU_097"
},
[402560] = {
id = 402560,
effect = true,
name = "绿兽人",
desc = "把战斗法师都抓起来！",
icon = "CDN:Icon_BU_098",
resourceConf = {
model = "SK_BU_098",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"裂隙的力量都是我们的！"
},
beginTime = v4,
suitId = 273,
suitName = "绿兽人",
suitIcon = "CDN:Icon_BU_098"
},
[402570] = {
id = 402570,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "寻梦冒险家 米萝",
desc = "一切都是梦的指引",
icon = "CDN:Icon_PL_134",
getWay = "寻梦之旅",
jumpId = {
702
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_134",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_134_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_134",
outShow = "AS_CH_IdleShow_PL_134",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_134",
shareTexts = {
"追寻着梦的节奏！"
},
shareAnim = "AS_CH_Pose_001_PL_134",
beginTime = {
seconds = 1720108800
},
sharePic = "T_Share_Suit_402572.astc",
suitId = 276,
suitName = "寻梦冒险家 米萝",
themedId = 13,
bpShowId = 3,
suitIcon = "CDN:Icon_PL_134",
shareNamePic = "T_Share_Suit_name_402571.astc",
shareBubblePic = "T_Share_Suit_frame_402571.astc",
ThemedShowIdList = {
{
key = 13,
value = 3
}
}
},
[402580] = {
id = 402580,
effect = true,
name = "梁小蝶",
desc = "款款深情，化蝶为伴",
icon = "CDN:Icon_BU_129",
resourceConf = {
model = "SK_BU_129",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"破茧成蝶，与爱相遇"
},
beginTime = v4,
suitId = 277,
suitName = "梁小蝶",
suitIcon = "CDN:Icon_BU_129"
},
[402581] = {
id = 402581,
effect = true,
name = "梁小蝶",
desc = "款款深情，化蝶为伴",
icon = "CDN:Icon_BU_129_01",
outlookConf = {
belongTo = 402580,
fashionValue = 25,
belongToGroup = {
402581
}
},
resourceConf = {
model = "SK_BU_129",
material = "MI_BU_129_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11326,
shareTexts = {
"破茧成蝶，与爱相遇"
}
},
[402590] = {
id = 402590,
effect = true,
name = "祝小蝶",
desc = "爱与自由，托梦于蝶",
icon = "CDN:Icon_BU_130",
resourceConf = {
model = "SK_BU_130",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"相约梦蝶，追寻挚爱"
},
beginTime = v4,
suitId = 278,
suitName = "祝小蝶",
suitIcon = "CDN:Icon_BU_130"
},
[402591] = {
id = 402591,
effect = true,
name = "祝小蝶",
desc = "爱与自由，托梦于蝶",
icon = "CDN:Icon_BU_130_01",
outlookConf = {
belongTo = 402590,
fashionValue = 25,
belongToGroup = {
402591
}
},
resourceConf = {
model = "SK_BU_130",
material = "MI_BU_130_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11328,
shareTexts = {
"相约梦蝶，追寻挚爱"
}
},
[402600] = {
id = 402600,
effect = true,
name = "橙小星",
desc = "快手快脚的摄像师，永远冲在新鲜事第一线",
icon = "CDN:Icon_BU_127",
resourceConf = {
model = "SK_BU_127",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"出游必备搭子，随时随地记录你的美"
},
beginTime = v4,
suitId = 282,
suitName = "橙小星",
suitIcon = "CDN:Icon_BU_127"
},
[402610] = {
id = 402610,
effect = true,
name = "小画家耶 ",
desc = "用画笔记录美好瞬间，用微笑给生活比个耶！",
icon = "CDN:Icon_BU_128",
resourceConf = {
model = "SK_BU_128",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_BU_128_Physics"
},
shareTexts = {
"抖一抖小画笔，为梦想加点颜色！"
},
beginTime = v4,
suitId = 283,
suitName = "小画家耶 ",
suitIcon = "CDN:Icon_BU_128"
},
[402620] = {
id = 402620,
effect = true,
name = "太医 实初",
desc = "守护你已经成为我的一种习惯",
icon = "CDN:Icon_BU_133",
resourceConf = {
model = "SK_BU_133",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"永远事事以你为重"
},
beginTime = v4,
suitId = 284,
suitName = "太医 实初",
suitIcon = "CDN:Icon_BU_133"
},
[402630] = {
id = 402630,
effect = true,
name = "明星小电视",
desc = "在元梦之时，干杯！",
icon = "CDN:Icon_BU_147",
resourceConf = {
model = "SK_BU_147",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"前方高能，弹幕大军来袭！"
},
beginTime = v4,
suitId = 285,
suitName = "明星小电视",
suitIcon = "CDN:Icon_BU_147"
},
[402640] = {
id = 402640,
effect = true,
name = "星灿灿",
desc = "一星一意，把你放星尖",
icon = "CDN:Icon_BU_131",
resourceConf = {
model = "SK_BU_131",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"送你一颗小星星"
},
beginTime = v4,
suitId = 286,
suitName = "星灿灿",
suitIcon = "CDN:Icon_BU_131"
},
[402641] = {
id = 402641,
effect = true,
name = "星灿灿",
desc = "一星一意，把你放星尖",
icon = "CDN:Icon_BU_131_01",
outlookConf = {
belongTo = 402640,
fashionValue = 25,
belongToGroup = {
402641
}
},
resourceConf = {
model = "SK_BU_131",
material = "MI_BU_131_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11336,
shareTexts = {
"送你一颗小星星"
}
},
[402650] = {
id = 402650,
effect = true,
name = "月缘缘",
desc = "花好月圆，温柔守护你",
icon = "CDN:Icon_BU_132",
resourceConf = {
model = "SK_BU_132",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"代表月亮守护你"
},
beginTime = v4,
suitId = 287,
suitName = "月缘缘",
suitIcon = "CDN:Icon_BU_132"
},
[402651] = {
id = 402651,
effect = true,
name = "月缘缘",
desc = "花好月圆，温柔守护你",
icon = "CDN:Icon_BU_132_01",
outlookConf = {
belongTo = 402650,
fashionValue = 25,
belongToGroup = {
402651
}
},
resourceConf = {
model = "SK_BU_132",
material = "MI_BU_132_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11338,
shareTexts = {
"代表月亮守护你"
}
},
[402660] = {
id = 402660,
effect = true,
name = "金牛星",
desc = "花好月圆，温柔守护你",
icon = "CDN:Icon_BU_135",
getWay = "闯关挑战",
jumpId = {
33
},
resourceConf = {
model = "SK_BU_135",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"和金牛一起守护挚爱吧！"
},
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitId = 288,
suitName = "金牛星",
suitIcon = "CDN:Icon_BU_135"
},
[402661] = {
id = 402661,
effect = true,
name = "金牛星",
desc = "花好月圆，温柔守护你",
icon = "CDN:Icon_BU_135_01",
outlookConf = {
belongTo = 402660,
fashionValue = 25,
belongToGroup = {
402661
}
},
resourceConf = {
model = "SK_BU_135",
material = "MI_BU_135_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11340,
shareTexts = {
"和金牛一起守护挚爱吧！"
}
},
[402670] = {
id = 402670,
effect = true,
name = "狮子星",
desc = "花好月圆，温柔守护你",
icon = "CDN:Icon_BU_136",
resourceConf = {
model = "SK_BU_136",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"感受来自狮子的热情吧！"
},
beginTime = v4,
suitId = 289,
suitName = "狮子星",
suitIcon = "CDN:Icon_BU_136"
},
[402671] = {
id = 402671,
effect = true,
name = "狮子星",
desc = "花好月圆，温柔守护你",
icon = "CDN:Icon_BU_136_01",
outlookConf = {
belongTo = 402670,
fashionValue = 25,
belongToGroup = {
402671
}
},
resourceConf = {
model = "SK_BU_136",
material = "MI_BU_136_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11342,
shareTexts = {
"感受来自狮子的热情吧！"
}
},
[402680] = {
id = 402680,
effect = true,
name = "游乐乐",
desc = "游乐园是梦的延续",
icon = "CDN:Icon_BU_139",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_BU_139",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"我在最快乐的地方等你！"
},
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitId = 290,
suitName = "游乐乐",
bpShowId = 4,
seasonId = 6,
suitIcon = "CDN:Icon_BU_139",
SeasonShowIdList = {
{
key = 6,
value = 4
}
}
},
[402681] = {
id = 402681,
effect = true,
name = "游乐乐",
desc = "游乐园是梦的延续",
icon = "CDN:Icon_BU_139_01",
outlookConf = {
belongTo = 402680,
fashionValue = 25,
belongToGroup = {
402681
}
},
resourceConf = {
model = "SK_BU_139",
material = "MI_BU_139_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11344,
shareTexts = {
"我在最快乐的地方等你！"
}
},
[402690] = {
id = 402690,
effect = true,
name = "游美美",
desc = "越快乐才能越美丽",
icon = "CDN:Icon_BU_141",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_BU_141",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"一起来游园会吗？"
},
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitId = 291,
suitName = "游美美",
bpShowId = 3,
seasonId = 6,
suitIcon = "CDN:Icon_BU_141",
SeasonShowIdList = {
{
key = 6,
value = 3
}
}
},
[402691] = {
id = 402691,
effect = true,
name = "游美美",
desc = "越快乐才能越美丽",
icon = "CDN:Icon_BU_141_01",
outlookConf = {
belongTo = 402690,
fashionValue = 25,
belongToGroup = {
402691
}
},
resourceConf = {
model = "SK_BU_141",
material = "MI_BU_141_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11346,
shareTexts = {
"一起来游园会吗？"
}
},
[402700] = {
id = 402700,
effect = true,
exceedReplaceItem = {
{
itemId = 217,
itemNum = 7
}
},
name = "爱邮邮",
desc = "收集所有的小确幸",
icon = "CDN:Icon_BU_138",
resourceConf = {
model = "SK_BU_138",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"和我在邮票的世界邂逅吧！"
},
beginTime = v4,
suitId = 292,
suitName = "爱邮邮",
suitIcon = "CDN:Icon_BU_138"
},
[402701] = {
id = 402701,
effect = true,
name = "爱邮邮",
desc = "收集所有的小确幸",
icon = "CDN:Icon_BU_138_01",
outlookConf = {
belongTo = 402700,
fashionValue = 25,
belongToGroup = {
402701
}
},
resourceConf = {
model = "SK_BU_138",
material = "MI_BU_138_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11348,
shareTexts = {
"和我在邮票的世界邂逅吧！"
}
},
[402710] = {
id = 402710,
effect = true,
name = "黑白配",
desc = "幻想在梦里开出了双色花",
icon = "CDN:Icon_BU_137",
resourceConf = {
model = "SK_BU_137",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"黑白配，共进退！"
},
beginTime = v4,
suitId = 293,
suitName = "黑白配",
suitIcon = "CDN:Icon_BU_137"
},
[402711] = {
id = 402711,
effect = true,
name = "黑白配",
desc = "幻想在梦里开出了双色花",
icon = "CDN:Icon_BU_137_01",
outlookConf = {
belongTo = 402710,
fashionValue = 25,
belongToGroup = {
402711
}
},
resourceConf = {
model = "SK_BU_137",
material = "MI_BU_137_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11350,
shareTexts = {
"黑白配，共进退！"
}
},
[402720] = {
id = 402720,
effect = true,
name = "篷篷梦",
desc = "奇幻和惊喜交织变成彩条",
icon = "CDN:Icon_BU_134",
getWay = "赛季通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_BU_134",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"欢迎来到我的马戏团。"
},
beginTime = {
seconds = 1721318400
},
suitId = 294,
suitName = "篷篷梦",
suitIcon = "CDN:Icon_BU_134"
},
[402721] = {
id = 402721,
effect = true,
name = "篷篷梦",
desc = "奇幻和惊喜交织变成彩条",
icon = "CDN:Icon_BU_134_01",
outlookConf = {
belongTo = 402720,
fashionValue = 25,
belongToGroup = {
402721
}
},
resourceConf = {
model = "SK_BU_134",
material = "MI_BU_134_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11352,
shareTexts = {
"欢迎来到我的马戏团。"
}
},
[402730] = {
id = 402730,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "熹妃 甄嬛",
desc = " 臣妾，是钮祜禄·甄嬛",
icon = "CDN:Icon_PL_117",
getWay = "甄嬛传祈愿",
jumpId = {
801
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_117",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_117_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_117",
outShow = "AS_CH_IdleShow_PL_117",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_117",
shareTexts = {
"逆风如解意，容易莫摧残"
},
shareAnim = "AS_CH_Pose_001_PL_117",
beginTime = {
seconds = 1720713600
},
suitId = 295,
suitName = "熹妃 甄嬛",
themedId = 15,
bpShowId = 3,
suitIcon = "CDN:Icon_PL_117",
ThemedShowIdList = {
{
key = 15,
value = 3
}
}
},
[402740] = {
id = 402740,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "皇帝 四郎",
desc = " 还有多少惊喜是朕不知道的？",
icon = "CDN:Icon_PL_118",
getWay = "甄嬛传祈愿",
jumpId = {
801
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_118",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_118_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_118",
outShow = "AS_CH_IdleShow_PL_118",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_118",
shareTexts = {
"朕要陪着你，你也要陪着朕"
},
shareAnim = "AS_CH_Pose_001_PL_118",
beginTime = {
seconds = 1720713600
},
suitId = 296,
suitName = "皇帝 四郎",
themedId = 15,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_118",
ThemedShowIdList = {
{
key = 15,
value = 2
}
}
},
[402750] = {
id = 402750,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "华妃 世兰",
desc = "一定要花团锦簇，轰轰烈烈才好！",
icon = "CDN:Icon_PL_119",
getWay = "甄嬛传祈愿",
jumpId = {
801
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_119",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_119_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_119",
outShow = "AS_CH_IdleShow_PL_119",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_119",
shareTexts = {
"今年的枫叶好像不够红啊~"
},
shareAnim = "AS_CH_Pose_001_PL_119",
beginTime = {
seconds = 1720713600
},
suitId = 297,
suitName = "华妃 世兰",
themedId = 15,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_119",
ThemedShowIdList = {
{
key = 15,
value = 1
}
}
},
[402760] = {
id = 402760,
effect = true,
exceedReplaceItem = {
{
itemId = 222,
itemNum = 7
}
},
name = "凤梨雪雪",
desc = "带支菠萝味儿的甜筒回家吧！",
icon = "CDN:Icon_BU_119",
resourceConf = {
model = "SK_BU_119",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"饭后解腻，正合适"
},
beginTime = v4,
suitId = 298,
suitName = "菠菠冰",
suitIcon = "CDN:Icon_BU_119"
},
[402761] = {
id = 402761,
effect = true,
name = "凤梨雪雪",
desc = "带支菠萝味儿的甜筒回家吧！",
icon = "CDN:Icon_BU_119_01",
outlookConf = {
belongTo = 402760,
fashionValue = 25,
belongToGroup = {
402761
}
},
resourceConf = {
model = "SK_BU_119",
material = "MI_BU_119_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11357,
shareTexts = {
"饭后解腻，正合适"
}
},
[402770] = {
id = 402770,
effect = true,
name = "可可豆",
desc = "冰淇淋加点可可更可爱",
icon = "CDN:Icon_BU_120",
resourceConf = {
model = "SK_BU_120",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"浓郁可可与冰爽夏天碰撞"
},
beginTime = v4,
suitId = 299,
suitName = "可可豆",
suitIcon = "CDN:Icon_BU_120"
},
[402771] = {
id = 402771,
effect = true,
name = "可可豆",
desc = "冰淇淋加点可可更可爱",
icon = "CDN:Icon_BU_120_01",
outlookConf = {
belongTo = 402770,
fashionValue = 25,
belongToGroup = {
402771
}
},
resourceConf = {
model = "SK_BU_120",
material = "MI_BU_120_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11359,
shareTexts = {
"浓郁可可与冰爽夏天碰撞"
}
},
[402780] = {
id = 402780,
effect = true,
name = "壶小小",
desc = "我不胖，可爱到膨胀",
icon = "CDN:Icon_BU_140",
getWay = "赛季祈愿",
jumpId = {
623
},
resourceConf = {
model = "SK_BU_140",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"要一起喝一杯吗？"
},
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitId = 300,
suitName = "壶小小",
bpShowId = 1,
seasonId = 6,
suitIcon = "CDN:Icon_BU_140",
SeasonShowIdList = {
{
key = 6,
value = 1
}
}
},
[402781] = {
id = 402781,
effect = true,
name = "壶小小",
desc = "我不胖，可爱到膨胀",
icon = "CDN:Icon_BU_140_01",
outlookConf = {
belongTo = 402780,
fashionValue = 25,
belongToGroup = {
402781
}
},
resourceConf = {
model = "SK_BU_140",
material = "MI_BU_140_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11361,
shareTexts = {
"要一起喝一杯吗？"
}
},
[402790] = {
id = 402790,
effect = true,
name = "星票票",
desc = "吃饱饱，卖票票",
icon = "CDN:Icon_BU_142",
getWay = "乐园通行证",
jumpId = {
9
},
resourceConf = {
model = "SK_BU_142",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"这可是限量版的票呢！"
},
beginTime = {
seconds = 1721318400
},
suitId = 301,
suitName = "星票票",
suitIcon = "CDN:Icon_BU_142"
},
[402791] = {
id = 402791,
effect = true,
name = "星票票",
desc = "吃饱饱，卖票票",
icon = "CDN:Icon_BU_142_01",
outlookConf = {
belongTo = 402790,
fashionValue = 25,
belongToGroup = {
402791
}
},
resourceConf = {
model = "SK_BU_142",
material = "MI_BU_142_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11363,
shareTexts = {
"这可是限量版的票呢！"
}
},
[402800] = {
id = 402800,
effect = true,
name = "狡狡龙",
desc = "甜心乐园里到底藏着什么秘密呢？",
icon = "CDN:Icon_BU_143",
resourceConf = {
model = "SK_BU_143",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"幸福的秘方，我一定会找到的！"
},
beginTime = v4,
suitId = 302,
suitName = "狡狡龙",
suitIcon = "CDN:Icon_BU_143"
},
[402801] = {
id = 402801,
effect = true,
name = "狡狡龙",
desc = "甜心乐园里到底藏着什么秘密呢？",
icon = "CDN:Icon_BU_143_01",
outlookConf = {
belongTo = 402800,
fashionValue = 25,
belongToGroup = {
402801
}
},
resourceConf = {
model = "SK_BU_143",
material = "MI_BU_143_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11365,
shareTexts = {
"幸福的秘方，我一定会找到的！"
}
},
[402810] = {
id = 402810,
effect = true,
name = "糖小乐",
desc = "糖果就是快乐本身",
icon = "CDN:Icon_BU_145",
getWay = "携友同行",
jumpId = {
132
},
resourceConf = {
model = "SK_BU_145",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"将糖果和我的爱一起给你。"
},
beginTime = {
seconds = 1737043200
},
suitId = 303,
suitName = "糖小乐",
suitIcon = "CDN:Icon_BU_145"
},
[402811] = {
id = 402811,
effect = true,
name = "糖小乐",
desc = "糖果就是快乐本身",
icon = "CDN:Icon_BU_145_01",
outlookConf = {
belongTo = 402810,
fashionValue = 25,
belongToGroup = {
402811
}
},
resourceConf = {
model = "SK_BU_145",
material = "MI_BU_145_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11367,
shareTexts = {
"将糖果和我的爱一起给你。"
}
},
[402820] = {
id = 402820,
effect = true,
name = "猴小萌",
desc = "我也想变成甜蜜的化身",
icon = "CDN:Icon_BU_146",
resourceConf = {
model = "SK_BU_146",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"我才不是调皮的小猴呢！"
},
beginTime = v4,
suitId = 304,
suitName = "猴小萌",
suitIcon = "CDN:Icon_BU_146"
},
[402821] = {
id = 402821,
effect = true,
name = "猴小萌",
desc = "我也想变成甜蜜的化身",
icon = "CDN:Icon_BU_146_01",
outlookConf = {
belongTo = 402820,
fashionValue = 25,
belongToGroup = {
402821
}
},
resourceConf = {
model = "SK_BU_146",
material = "MI_BU_146_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head",
materialSlot = "Skin"
},
commodityId = 11369,
shareTexts = {
"我才不是调皮的小猴呢！"
}
},
[402830] = {
id = 402830,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "甜心公主 萝茜",
desc = "目的地不重要，开心游玩就好了",
icon = "CDN:Icon_OG_018",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_018",
material = "MI_OG_018_1;MI_OG_018_2;MI_OG_018_3;MI_OG_018_4;MI_OG_018_5;MI_OG_018_6;MI_OG_018_7;MI_OG_018_8",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_018_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06;Skin_07;Skin_08"
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_018_PV/Level_OG_018_Intact",
outIdle = "AS_CH_OutIdle_OG_018",
outShow = "AS_CH_IdleShow_OG_018",
outShowIntervalTime = 20,
soundId = {
4038,
4040
},
outIdle_pv = "LS_PV_OG_018_Idle",
outEnterSequence = "LS_PV_OG_018",
shareTexts = {
"笑一笑，烦恼都跑掉"
},
shareAnim = "AS_CH_Pose_001_OG_018",
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitStoryTextKey = [[甜心家族最小的公主叫做萝茜，她不仅外表甜美，内心也同样充满爱，是名副其实的甜心公主。萝茜拥有一种神奇的力量，召唤出粉红泡泡，能够让周围中的一切都散发甜甜的香味和鲜艳的色彩。她的笑声，如同夏日温暖的阳光，具有治愈的力量，能够瞬间驱散任何悲伤或阴霾。

元梦世界中曾流传着一个古老的传说，有一股黑暗势力企图吞噬所有的快乐和光明，并将幽暗的黑烟散播向世界。萝茜听闻了这个传说后，决定用自己的力量为星宝们建造一个快乐的庇护所，她用无尽的爱心和甜蜜的泡泡打造了一个梦幻的地方——甜心乐园，萝茜也成为了甜心乐园中最灿烂的守护者。

每天，萝茜都会巡视乐园的每一个角落，确保这个奇妙的地方充满欢笑和乐趣。她穿梭在游乐设施之间，挥舞她的甜心权杖，为乐园增添更缤纷多色彩。她的权杖还能发出耀眼的光芒，驱散侵袭乐园的黑暗和阴影。

即使萝茜如此费劲心思建造了这样一座快乐的庇护所，依然无法阻止古老的传说——一股黑烟悄然间侵袭了乐园。这股黑烟不断试图笼罩乐园，抹去所有欢乐笑声。萝茜没有因此感到胆怯，她用尽全力挥舞她的甜心权杖，用光芒驱散黑暗，保护乐园的安宁。

萝茜的姐姐甜梦公主莱拉，一直极力反对萝茜直面黑烟的做法，甚至试图让萝茜留在家族城堡里，不让她踏入乐园。萝茜不理解姐姐这样的做法，悄悄溜出城堡主动迎击黑烟。在一次与黑烟的正面较量中，为了守护住乐园的安全，萝茜几乎耗尽了所有力量。悄悄跟踪妹妹的甜梦公主莱拉及时出现，施法护住了妹妹。但从这以后，每当夜幕降临，萝茜就会陷入沉睡之中。甜心乐园也随萝茜的沉睡进入了夜幕和寂静之中，等待新的曙光。

甜梦公主莱拉用她的甜梦之力唤醒了沉睡的甜心乐园，为乐园逐渐焕发出了新的生机和光彩，甜心乐园再次迎来了欢声笑语。]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "T_Share_Suit_402830.astc",
suitId = 305,
suitName = "甜心公主 萝茜",
bpShowId = 2,
seasonId = 6,
suitIcon = "CDN:Icon_OG_018",
shareNamePic = "T_Share_Suit_name_402830.astc",
shareBubblePic = "T_Share_Suit_frame_402830.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029",
SeasonShowIdList = {
{
key = 6,
value = 2
}
}
},
[402831] = {
id = 402831,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "甜心公主 萝茜",
desc = "目的地不重要，开心游玩就好了",
icon = "CDN:Icon_OG_018_01",
outlookConf = {
belongTo = 402830,
fashionValue = 125,
belongToGroup = {
402831,
402832
}
},
resourceConf = {
model = "SK_OG_018",
material = "MI_OG_018_1_HP01;MI_OG_018_2_HP01;MI_OG_018_3_HP01;MI_OG_018_4_HP01;MI_OG_018_5_HP01;MI_OG_018_6_HP01;MI_OG_018_7_HP01;MI_OG_018_8_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_018_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06;Skin_07;Skin_08"
},
commodityId = 11371,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_018_PV/Level_OG_018_Intact",
outIdle = "AS_CH_OutIdle_OG_018",
outShow = "AS_CH_IdleShow_OG_018_HP01",
outShowIntervalTime = 20,
soundId = {
4038,
4040
},
outIdle_pv = "LS_PV_OG_018_HP01_Idle",
outEnterSequence = "LS_PV_OG_018_HP01",
shareTexts = {
"笑一笑，烦恼都跑掉"
},
shareAnim = "AS_CH_Pose_001_OG_018",
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "T_Share_Suit_402831.astc",
shareNamePic = "T_Share_Suit_name_402830.astc",
shareBubblePic = "T_Share_Suit_frame_402830.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029"
},
[402832] = {
id = 402832,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "甜心公主 萝茜",
desc = "目的地不重要，开心游玩就好了",
icon = "CDN:Icon_OG_018_02",
outlookConf = {
belongTo = 402830,
fashionValue = 125,
belongToGroup = {
402831,
402832
}
},
resourceConf = {
model = "SK_OG_018",
material = "MI_OG_018_1_HP02;MI_OG_018_2_HP02;MI_OG_018_3_HP02;MI_OG_018_4_HP02;MI_OG_018_5_HP02;MI_OG_018_6_HP02;MI_OG_018_7_HP02;MI_OG_018_8_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_018_Physics",
materialSlot = "Skin;Skin_02;Skin_03;Skin_04;Skin_05;Skin_06;Skin_07;Skin_08"
},
commodityId = 11372,
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_018_PV/Level_OG_018_Intact",
outIdle = "AS_CH_OutIdle_OG_018",
outShow = "AS_CH_IdleShow_OG_018_HP02",
outShowIntervalTime = 20,
soundId = {
4038,
4040
},
outIdle_pv = "LS_PV_OG_018_HP02_Idle",
outEnterSequence = "LS_PV_OG_018_HP02",
shareTexts = {
"笑一笑，烦恼都跑掉"
},
shareAnim = "AS_CH_Pose_001_OG_018",
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "T_Share_Suit_402832.astc",
shareNamePic = "T_Share_Suit_name_402830.astc",
shareBubblePic = "T_Share_Suit_frame_402830.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029"
},
[402840] = {
id = 402840,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 800
}
},
quality = 1,
name = "甜梦公主 莱拉",
desc = "全世界都催你长大，只有游乐园不会",
icon = "CDN:Icon_OG_019",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = {
fashionValue = 1000
},
resourceConf = {
model = "SK_OG_019",
material = "MI_OG_019_1;MI_OG_019_2;MI_OG_019_3;MI_OG_019_4;MI_OG_019_5;MI_OG_019_6;MI_OG_019_7;MI_OG_019_8",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_OG_019_Physics",
materialSlot = "Skin;Skin_Opaque_01;Skin_Opaque_02;Skin_Opaque_03;Skin_Translucent_01;Skin_Translucent_02;Skin_Opaque_04;Skin_Translucent_03",
IconLabelId = 101
},
outEnter = "/Game/LetsGo/Assets/Characters/OG/OG_018_PV/Level_OG_018_Intact",
outIdle = "AS_CH_OutIdle_OG_019",
outShow = "AS_CH_IdleShow_OG_019",
outShowIntervalTime = 20,
soundId = {
4039,
4041
},
outIdle_pv = "LS_PV_OG_019_Idle",
outEnterSequence = "LS_PV_OG_019",
shareTexts = {
"童心未泯，所遇皆甜"
},
shareAnim = "AS_CH_Pose_001_OG_019",
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitStoryTextKey = [[当夜幕降临，甜心乐园便迎来了另一位神秘的守护者——甜梦公主莱拉。她的出现像是夜空中的一颗流星，神秘而不可捉摸，眼中闪烁着如星星般的智慧。莱拉总能在面对挑战时保持冷静，她的智慧和冷静让她能够迅速解决任何突发的困难。

莱拉与妹妹萝茜一起听闻了破坏元梦世界的黑烟传说，然而妹妹没有听完整的部分就跑去建造乐园了。传说的后半部分讲的是，那股可怕的黑烟会在白天不断吸食快乐元素，积累力量在夜晚达到峰值，吞噬所有的快乐和笑容。为了保护妹妹萝茜，更是为了守护最快乐的甜心乐园，每当夜幕降临，莱拉用她的独特力量将妹妹放入甜梦泡泡进行体力修复，而自己来抵御黑夜的危险。

为了抵御突然出现的黑烟，莱拉练就了一身迅速行动的技能。她的行踪不定，而每当有星宝在夜晚迷路时，莱拉总会及时出现，成为那一抹最亮的光，引领迷失的星宝安全回家。她不仅是夜晚的守护者，也是梦想的使者。她通过触摸遍布乐园的许愿星，将星宝们的愿望转化为现实，使每个夜晚都充满奇迹和欢笑。

午夜时分，神秘的黑烟常常出现侵扰这片欢乐之地，比白天更为浓烈，甚至有时并伴随出现忧虑的氛围。在一次黑烟浓度达到顶峰的后半夜，莱拉觉得时机已经成熟，可以用她长期积累的非凡的力量进行对抗。她将集结的星愿之光，创造出了一个巨大的梦想漩涡，即使拼尽全力，可黑烟似乎力量更甚一筹。眼看拼尽全力的莱拉就快被黑烟吞噬，恰好遇到了黎明时分，沉睡的甜心公主萝茜逐渐苏醒，顾不得多问就上前支援姐姐。最后，姐妹俩齐心协力成功将侵袭的黑烟一网打尽。这次胜利巩固了姐妹俩作为甜心乐园守护者的地位，也使她们在星宝们心中的形象愈发神圣和崇高。

在甜梦公主莱拉和甜心公主萝茜的共同守护下，无论白天还是夜晚，甜心乐园都成为了星宝们的快乐庇护所，是每个星宝都能实现梦想的奇迹之地。
]],
timeToStartAnim = 3.700000047683716,
shareBackground = "T_Background_Share2_S4",
sharePic = "T_Share_Suit_402840.astc",
suitId = 306,
suitName = "甜梦公主 莱拉",
suitIcon = "CDN:Icon_OG_019",
shareNamePic = "T_Share_Suit_name_402840.astc",
shareBubblePic = "T_Share_Suit_frame_402840.astc",
lotteryRewardShow = true,
lotteryPreviewShow = true,
mallHotShow = true,
mallAvatarShow = true,
selfhoodBgRes = "CDN:T_PlayerInfo_Img_ProfileThemeBG_029"
},
[402850] = {
id = 402850,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "悠悠云 克劳德",
desc = "如果你累了，我可以带你飞",
icon = "CDN:Icon_PL_154",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_154",
material = "MI_PL_154_1;MI_PL_154_2",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_154_Physics",
materialSlot = "Skin;Skin_1"
},
outEnter = "AS_CH_Enter_PL_154",
outShow = "AS_CH_IdleShow_PL_154",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_154",
shareTexts = {
"彩虹是云朵的微笑"
},
shareAnim = "AS_CH_Pose_001_PL_154",
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitStoryTextKey = [[拉比娜在一次精彩的魔术表演中，意外地变幻出了一朵可爱的小云朵 ——克劳德。他最大的特点，就是全心全意地展现自己的内心情感。无论是想笑还是想哭，都完全不掩饰。

克劳德总是为一些小事情感到难过，眼泪像断了线的珍珠一样滚落下来。但哭完之后，他又会用自己的魔力变出绚烂的彩虹，让整个乐园都沐浴在温暖的彩色光芒中。克劳德认为，哭泣也是一种勇气的体现，同时鼓励星宝们也要勇敢地抒发自己的情绪。

拉比娜原本是想用克劳德的笑容治愈星宝，却没想到变出一个爱哭包。这很可能是因为，拉比娜把自己内心的胆怯和丰富情绪，都一并赋予了克劳德。不过星宝们反而更喜欢这样的克劳德，因为他从不伪装，从不掩饰内心的脆弱。

作为幻境云朵，克劳德拥有许多神奇的能力。他能在空中自由地漂浮，带领星宝们在乐园中飞翔。他的身体更能随意变换各种形态，有时化身为机灵的小动物，有时则呆呆地漂浮着。这些变形表演，总是能逗得星宝们开怀大笑。

不仅如此，克劳德的云朵身体还能吸走乐园中的黑烟，为天空带来纯净清朗。每次执行完这样的重任后，他都会放声大哭一场，因为他知道，只有眼泪才能将黑暗的烟雾轻柔地带走，换回绚丽的彩虹。

克劳德用自己的方式为这片乐园带来了无穷无尽的温暖与希望。他敢于坦诚地展现自己最真挚的内心，感染着周围的星宝们。他的哭泣与笑容，正如他变幻出的每缕绚烂彩虹，都是甜心乐园最宝贵的一部分。]],
sharePic = "T_Share_Suit_402850.astc",
suitId = 307,
suitName = "悠悠云 克劳德",
bpShowId = 5,
seasonId = 6,
suitIcon = "CDN:Icon_PL_154",
shareNamePic = "T_Share_Suit_name_402850.astc",
shareBubblePic = "T_Share_Suit_frame_402850.astc",
SeasonShowIdList = {
{
key = 6,
value = 5
}
}
},
[402851] = {
id = 402851,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "悠悠云 克劳德",
desc = "如果你累了，我可以带你飞",
icon = "CDN:Icon_PL_154_01",
outlookConf = {
belongTo = 402850,
fashionValue = 35,
belongToGroup = {
402851,
402852
}
},
resourceConf = {
model = "SK_PL_154",
material = "MI_PL_154_1_HP01;MI_PL_154_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_154_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11375,
outEnter = "AS_CH_Enter_PL_154",
outShow = "AS_CH_IdleShow_PL_154",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_154",
shareTexts = {
"彩虹是云朵的微笑"
},
shareAnim = "AS_CH_Pose_001_PL_154",
sharePic = "T_Share_Suit_402850.astc",
shareNamePic = "T_Share_Suit_name_402850.astc",
shareBubblePic = "T_Share_Suit_frame_402850.astc"
},
[402852] = {
id = 402852,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "悠悠云 克劳德",
desc = "如果你累了，我可以带你飞",
icon = "CDN:Icon_PL_154_02",
outlookConf = {
belongTo = 402850,
fashionValue = 35,
belongToGroup = {
402851,
402852
}
},
resourceConf = {
model = "SK_PL_154",
material = "MI_PL_154_1_HP02;MI_PL_154_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_154_Physics",
materialSlot = "Skin;Skin_1"
},
commodityId = 11376,
outEnter = "AS_CH_Enter_PL_154",
outShow = "AS_CH_IdleShow_PL_154",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_154",
shareTexts = {
"彩虹是云朵的微笑"
},
shareAnim = "AS_CH_Pose_001_PL_154",
sharePic = "T_Share_Suit_402850.astc",
shareNamePic = "T_Share_Suit_name_402850.astc",
shareBubblePic = "T_Share_Suit_frame_402850.astc"
},
[402860] = {
id = 402860,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "水云仙 雨荷",
desc = "误入藕花深处时，最是有趣",
icon = "CDN:Icon_PL_120",
getWay = "雨荷仙子",
jumpId = {
608
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_120",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_120_Physics"
},
outEnter = "AS_CH_Enter_PL_120",
outShow = "AS_CH_IdleShow_PL_120",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_120",
shareTexts = {
"出淤泥而不染"
},
shareAnim = "AS_CH_Pose_001_PL_120",
beginTime = {
seconds = 1720800000
},
suitStoryTextKey = [[仙子雨荷居于幽静的荷塘畔，日日与荷花相伴，照料这片水塘。她很爱洁净，荷塘的污泥却让她的裙袂沾满泥泞，为此她很苦恼，不由开始向往外界的生活。然而家人很严厉，斥责了她的心愿。没受过委屈的她一赌气，干脆离家出走，悄悄前往外界。

外界精彩纷呈的日常，让她心生欢悦，几乎忘却了那片泥泞的荷塘。就这样在外流连许久，偶然间她听到一个古老的传言——花仙子离开花丛之际，花朵便会迅速枯萎。忧虑顿时涌上她的心头。

重返荷塘时，她忍不住泪盈眶。曾经接天连叶的荷塘，如今已是荷叶枯萎，花枝低垂，一片萧瑟。她哭着来到荷花旁，捧起荷花，荷花轻抬花瓣试图抚慰她，随即又无力垂落。

她忽然忆起过去的夏天，丛丛荷花为她挡去夏日炎阳，让她在叶间跳跃嬉戏。外界纵然五彩斑斓，却唯有这片荷塘与她真心相依，这里才是她的家园。泪水滑落，她毅然跃入泥塘，紧紧拥抱衰败的荷花，即使衣裙沾满了零落成泥的花瓣，也毫不在意。

就在这时，荷塘中骤然亮起温暖的光芒，雨荷的真心唤醒了自然之力，原本枯萎的荷花竟逐一复苏绽放。雨荷成为了荷塘真正的小小守护者。]],
sharePic = "T_Share_Suit_402861.astc",
suitId = 308,
suitName = "水云仙 雨荷",
suitIcon = "CDN:Icon_PL_120",
shareNamePic = "T_Share_Suit_name_402860.astc",
shareBubblePic = "T_Share_Suit_frame_402860.astc"
},
[402861] = {
id = 402861,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "水云仙 雨荷",
desc = "误入藕花深处时，最是有趣",
icon = "CDN:Icon_PL_120_01",
outlookConf = {
belongTo = 402860,
fashionValue = 35,
belongToGroup = {
402861,
402862
}
},
resourceConf = {
model = "SK_PL_120",
material = "MI_PL_120_1_HP01;MI_PL_120_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_120_Physics",
materialSlot = "Skin;Skin_Translucent_01"
},
commodityId = 11378,
outEnter = "AS_CH_Enter_PL_120",
outShow = "AS_CH_IdleShow_PL_120",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_120",
shareTexts = {
"出淤泥而不染"
},
shareAnim = "AS_CH_Pose_001_PL_120",
sharePic = "T_Share_Suit_402861.astc",
shareNamePic = "T_Share_Suit_name_402860.astc",
shareBubblePic = "T_Share_Suit_frame_402860.astc"
},
[402862] = {
id = 402862,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "水云仙 雨荷",
desc = "误入藕花深处时，最是有趣",
icon = "CDN:Icon_PL_120_02",
outlookConf = {
belongTo = 402860,
fashionValue = 35,
belongToGroup = {
402861,
402862
}
},
resourceConf = {
model = "SK_PL_120",
material = "MI_PL_120_1_HP02;MI_PL_120_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_120_Physics",
materialSlot = "Skin;Skin_Translucent_01"
},
commodityId = 11379,
outEnter = "AS_CH_Enter_PL_120",
outShow = "AS_CH_IdleShow_PL_120",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_120",
shareTexts = {
"出淤泥而不染"
},
shareAnim = "AS_CH_Pose_001_PL_120",
sharePic = "T_Share_Suit_402861.astc",
shareNamePic = "T_Share_Suit_name_402860.astc",
shareBubblePic = "T_Share_Suit_frame_402860.astc"
},
[402870] = {
id = 402870,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "吉伊卡哇",
desc = "中奖吧！",
icon = "CDN:Icon_PL_146",
outlookConf = v2,
resourceConf = {
model = "SK_PL_146",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_146_Physics",
IconLabelId = 102,
skeletalMesh = "SK_PL_146"
},
outEnter = "AS_CH_Enter_PL_146",
outShow = "AS_CH_IdleShow_PL_146",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_146",
shareTexts = {
"以后鼓起勇气吃好吃的吧！"
},
shareAnim = "AS_CH_Pose_001_PL_146",
beginTime = {
seconds = 1751558400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402870.astc",
suitId = 309,
suitName = "吉伊卡哇",
themedId = 23,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_146",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402870.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402870.astc",
bodytype = 5,
ThemedShowIdList = {
{
key = 23,
value = 1
}
}
},
[402880] = {
id = 402880,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "哈奇喵",
desc = "总有办法的！",
icon = "CDN:Icon_PL_147",
outlookConf = v2,
resourceConf = {
model = "SK_PL_147",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_147_Physics",
IconLabelId = 102,
skeletalMesh = "SK_PL_147"
},
outEnter = "AS_CH_Enter_PL_147",
outShow = "AS_CH_IdleShow_PL_147",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_147",
shareTexts = {
"我买到了哦，一直想要的照相机！"
},
shareAnim = "AS_CH_Pose_001_PL_147",
beginTime = {
seconds = 1751558400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402880.astc",
suitId = 310,
suitName = "哈奇喵",
themedId = 23,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_147",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402880.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402870.astc",
bodytype = 5,
ThemedShowIdList = {
{
key = 23,
value = 2
}
}
},
[402890] = {
id = 402890,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "乌萨奇",
desc = "呜噜噜噜噜噜！",
icon = "CDN:Icon_PL_148",
outlookConf = v2,
resourceConf = {
model = "SK_PL_148",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_148_Physics",
IconLabelId = 102,
skeletalMesh = "SK_PL_148"
},
outEnter = "AS_CH_Enter_PL_148",
outShow = "AS_CH_IdleShow_PL_148",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_148",
shareTexts = {
"噗噜噜噜噜噜！"
},
shareAnim = "AS_CH_Pose_001_PL_148",
beginTime = {
seconds = 1751558400
},
sharePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_402890.astc",
suitId = 311,
suitName = "乌萨奇",
themedId = 23,
bpShowId = 3,
suitIcon = "CDN:Icon_PL_148",
shareNamePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_name_402890.astc",
shareBubblePic = "https://image-manage.ymzx.qq.com/wuji/client/materials/T_Share_Suit_frame_402870.astc",
bodytype = 5,
ThemedShowIdList = {
{
key = 23,
value = 3
}
}
},
[402900] = {
id = 402900,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "魔术兔 拉比娜",
desc = "别眨眼，奇迹正在发生",
icon = "CDN:Icon_PL_152",
getWay = "赛季祈愿",
jumpId = {
623
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_152",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_152_Physics"
},
outEnter = "AS_CH_Enter_PL_152",
outShow = "AS_CH_IdleShow_PL_152",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_152",
shareTexts = {
"魔术，让平凡变得不平凡"
},
shareAnim = "AS_CH_Pose_001_PL_152",
minVer = "1.3.12.1",
beginTime = {
seconds = 1721318400
},
suitStoryTextKey = [[拉比娜从小就胆小害羞，面对陌生环境时常常紧张到说不出话来。她喜欢待在自己的小窝里阅读，偶尔也捣腾手工制作，沉浸在自己的小世界中。

一天，她在图书馆的一角发现了一本尘封已久的魔术书。书中的奇妙魔术让她心生向往，但她从未想过自己能掌握这些技巧。夜晚，她在自己小窝里悄悄练习，发现魔术表演让她感到一种前所未有的满足和喜悦。随着时间的推移，拉比娜的魔术技艺逐渐精湛。她决定以“魔术兔”的身份在乐园中小范围试演。第一次表演时，尽管心跳加速，手心冒汗，但当她看到观众们惊喜的表情和热烈的掌声时，一种自信的火花在她心中点燃。

每次登台前，拉比娜仍会感到紧张和害怕，但她学会了通过深呼吸和冥想来平复情绪。逐渐地，拉比娜从一个胆小害羞的兔子变成了一个在表演中自信满满的魔术师。

就在拉比娜逐渐告别胆小的自己时，乐园出现了一次前所未有的危机——一股黑烟悄然入侵乐园。面对这场危机，拉比娜的第一反应是逃跑，她想利用魔术躲避，想回到自己的小窝。但她知道，这是一个检验她真正成长的时刻。拉比娜知道，如果不勇敢一次，她将再也看不到乐园朋友们充满期待和信任的眼神。最终，拉比娜披上魔术师的斗篷，勇敢地直面黑烟，她变幻出耀眼的光芒和奇妙的幻象，为乐园的星宝争取了躲避的时间。

最终，甜心公主萝茜和甜梦公主莱拉及时赶到，大家联手成功地化解了危机，乐园恢复了往日的欢笑和光明。拉比娜也成为了最受欢迎的魔术师，星宝们纷纷排队来甜心乐园目睹拉比娜的奇幻戏法。]],
sharePic = "T_Share_Suit_402900.astc",
suitId = 312,
suitName = "魔术兔 拉比娜",
bpShowId = 6,
seasonId = 6,
suitIcon = "CDN:Icon_PL_152",
shareNamePic = "T_Share_Suit_name_402900.astc",
shareBubblePic = "T_Share_Suit_frame_402900.astc",
SeasonShowIdList = {
{
key = 6,
value = 6
}
}
},
[402901] = {
id = 402901,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "魔术兔 拉比娜",
desc = "别眨眼，奇迹正在发生",
icon = "CDN:Icon_PL_152_01",
outlookConf = {
belongTo = 402900,
fashionValue = 35,
belongToGroup = {
402901,
402902
}
},
resourceConf = {
model = "SK_PL_152",
material = "MI_PL_152_1_HP01;MI_PL_152_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_152_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11384,
outEnter = "AS_CH_Enter_PL_152",
outShow = "AS_CH_IdleShow_PL_152",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_152",
shareTexts = {
"魔术，让平凡变得不平凡"
},
shareAnim = "AS_CH_Pose_001_PL_152",
sharePic = "T_Share_Suit_402900.astc",
shareNamePic = "T_Share_Suit_name_402900.astc",
shareBubblePic = "T_Share_Suit_frame_402900.astc"
},
[402902] = {
id = 402902,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "魔术兔 拉比娜",
desc = "别眨眼，奇迹正在发生",
icon = "CDN:Icon_PL_152_02",
outlookConf = {
belongTo = 402900,
fashionValue = 35,
belongToGroup = {
402901,
402902
}
},
resourceConf = {
model = "SK_PL_152",
material = "MI_PL_152_1_HP02;MI_PL_152_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_152_Physics",
materialSlot = "Skin;Skin_01"
},
commodityId = 11385,
outEnter = "AS_CH_Enter_PL_152",
outShow = "AS_CH_IdleShow_PL_152",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_152",
shareTexts = {
"魔术，让平凡变得不平凡"
},
shareAnim = "AS_CH_Pose_001_PL_152",
sharePic = "T_Share_Suit_402900.astc",
shareNamePic = "T_Share_Suit_name_402900.astc",
shareBubblePic = "T_Share_Suit_frame_402900.astc"
},
[402910] = {
id = 402910,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "吟游诗人 奥菲斯",
desc = "出发吧，生活是旷野，不是轨道",
icon = "CDN:Icon_PL_153",
getWay = "乐园通行证",
jumpId = {
9
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_153",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_153_Physics"
},
outEnter = "AS_CH_Enter_PL_153",
outShow = "AS_CH_IdleShow_PL_153",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_153",
shareTexts = {
"每一首诗，都是心灵的回声"
},
shareAnim = "AS_CH_Pose_001_PL_153",
beginTime = {
seconds = 1721318400
},
suitStoryTextKey = [[漫无边际的旅途,是奥菲斯一生的注脚。这位神秘的吟游诗人，曾踏过无数广袤的国度，见证过生命中的点点滴滴。他用自己的经历谱写成诗歌与乐章，吸引着许多向往自由的灵魂。

奥菲斯演奏的手风琴曲总能直击听众的内心，他吟咏的史诗更是跌宕起伏,栩栩如生。当星宝们好奇地询问这些故事的真实性时，奥菲斯总是神秘地一笑置之。有星宝猜测,也许他已经活了几百年，才能积累如此丰富的阅历。然而，他依旧保持着少年般的外表，难以揣测他的真实年龄。

奥菲斯的内心有一只永不安宁的鸟儿，它总是拍打着翅膀，渴望自由的天空。他曾与狂放的海浪搏斗，与炽热的阳光竞跑,在宁静的湖畔驻足，在崇山峻岭俯瞰大地。他以为自己会永远漂泊不定,直到他路经甜心乐园。这里仿佛是他内心最渴望的归宿，一个能让他的心灵欢喜洋溢的乐园。

于是,奥菲斯选择在这片充满幸福和希望的土地上驻足。在甜心乐园里，你总能看见他拉动手风琴，歌颂着自己的内心世界：“那是我孤寂时分的乐园，使我的心灵欢情洋溢。”他的音乐和诗歌,为这片充满魔力的乐园带来了更多温暖与生机。每当星宝倾听他生动有趣的故事时,都会为他那流浪的经历和内心的孤独所触动。

也许，这就是奥菲斯最终停下脚步的原因。在这片宁静安详的乐园里，他终于找到了一个可以宣泄心声、倾诉故事的港湾。这个曾经漂泊无依的诗人，此时此刻终于找到了自己真正的归属,他的心灵也能在这里安歇。]],
sharePic = "T_Share_Suit_402910.astc",
suitId = 313,
suitName = "吟游诗人 奥菲斯",
suitIcon = "CDN:Icon_PL_153",
shareNamePic = "T_Share_Suit_name_402910.astc",
shareBubblePic = "T_Share_Suit_frame_402910.astc"
},
[402911] = {
id = 402911,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "吟游诗人 奥菲斯",
desc = "出发吧，生活是旷野，不是轨道",
icon = "CDN:Icon_PL_153_01",
outlookConf = {
belongTo = 402910,
fashionValue = 35,
belongToGroup = {
402911,
402912
}
},
resourceConf = {
model = "SK_PL_153",
material = "MI_PL_153_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_153_Physics",
materialSlot = "Skin"
},
commodityId = 11387,
outEnter = "AS_CH_Enter_PL_153",
outShow = "AS_CH_IdleShow_PL_153",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_153",
shareTexts = {
"每一首诗，都是心灵的回声"
},
shareAnim = "AS_CH_Pose_001_PL_153",
sharePic = "T_Share_Suit_402910.astc",
shareNamePic = "T_Share_Suit_name_402910.astc",
shareBubblePic = "T_Share_Suit_frame_402910.astc"
},
[402912] = {
id = 402912,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "吟游诗人 奥菲斯",
desc = "出发吧，生活是旷野，不是轨道",
icon = "CDN:Icon_PL_153_02",
outlookConf = {
belongTo = 402910,
fashionValue = 35,
belongToGroup = {
402911,
402912
}
},
resourceConf = {
model = "SK_PL_153",
material = "MI_PL_153_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_153_Physics",
materialSlot = "Skin"
},
commodityId = 11388,
outEnter = "AS_CH_Enter_PL_153",
outShow = "AS_CH_IdleShow_PL_153",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_153",
shareTexts = {
"每一首诗，都是心灵的回声"
},
shareAnim = "AS_CH_Pose_001_PL_153",
sharePic = "T_Share_Suit_402910.astc",
shareNamePic = "T_Share_Suit_name_402910.astc",
shareBubblePic = "T_Share_Suit_frame_402910.astc"
},
[402920] = {
id = 402920,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "象飞飞",
desc = "耳朵当翅膀，载我去远方",
icon = "CDN:Icon_Body_029",
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_029",
upper = "SK_Body_Upper_029",
bottom = "SK_Body_Under_029",
gloves = "SK_Body_Hands_029",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"小飞象来咯！"
},
beginTime = v4,
suitId = 314,
suitName = "象飞飞",
suitIcon = "CDN:Icon_Body_029"
},
[402930] = {
id = 402930,
effect = true,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 15
}
},
quality = 5,
name = "跳跳呱",
desc = "无忧无虑小青蛙，每天都要呱呱呱",
icon = "CDN:Icon_Body_030",
getWay = "冲段挑战",
jumpId = {
5033
},
outlookConf = {
fashionValue = 20
},
resourceConf = {
model = "SK_Body_Head_030",
upper = "SK_Body_Upper_030",
bottom = "SK_Body_Under_030",
gloves = "SK_Body_Hands_030",
face = "SK_Body_Face_001",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "ALL_ceshi_ModSuit_Physics_Head"
},
shareTexts = {
"你要和呱呱一起跳舞吗？"
},
beginTime = {
seconds = 1724947200
},
suitId = 315,
suitName = "跳跳呱",
suitIcon = "CDN:Icon_Body_030"
},
[402940] = {
id = 402940,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "草莓味 Toby",
desc = "吃一颗香香的草莓，做一个甜甜的梦！",
icon = "CDN:Icon_PL_121",
getWay = "夏日派对",
jumpId = {
1055
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_121",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_121_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_121",
outShow = "AS_CH_IdleShow_PL_121",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_121",
shareTexts = {
"走，我们去摘甜甜的草莓！"
},
shareAnim = "AS_CH_Pose_001_PL_121",
beginTime = {
seconds = 1720713600
},
sharePic = "T_Share_Suit_402940.astc",
suitId = 321,
suitName = "草莓味 Toby",
themedId = 16,
bpShowId = 2,
suitIcon = "CDN:Icon_PL_121",
shareNamePic = "T_Share_Suit_name_402940.astc",
shareBubblePic = "T_Share_Suit_frame_401000.astc",
ThemedShowIdList = {
{
key = 16,
value = 2
}
}
},
[402941] = {
id = 402941,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "草莓味 Toby",
desc = "吃一颗香香的草莓，做一个甜甜的梦！",
icon = "CDN:Icon_PL_121_01",
outlookConf = {
belongTo = 402940,
fashionValue = 35,
belongToGroup = {
402941,
402942
}
},
resourceConf = {
model = "SK_PL_121",
material = "MI_PL_121_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_121_Physics",
materialSlot = "Skin"
},
commodityId = 11392,
outEnter = "AS_CH_Enter_PL_121",
outShow = "AS_CH_IdleShow_PL_121",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_121",
shareTexts = {
"走，我们去摘甜甜的草莓！"
},
shareAnim = "AS_CH_Pose_001_PL_121",
sharePic = "T_Share_Suit_402940.astc",
shareNamePic = "T_Share_Suit_name_402940.astc",
shareBubblePic = "T_Share_Suit_frame_401000.astc"
},
[402942] = {
id = 402942,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "草莓味 Toby",
desc = "吃一颗香香的草莓，做一个甜甜的梦！",
icon = "CDN:Icon_PL_121_02",
outlookConf = {
belongTo = 402940,
fashionValue = 35,
belongToGroup = {
402941,
402942
}
},
resourceConf = {
model = "SK_PL_121",
material = "MI_PL_121_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_121_Physics",
materialSlot = "Skin"
},
commodityId = 11393,
outEnter = "AS_CH_Enter_PL_121",
outShow = "AS_CH_IdleShow_PL_121",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_121",
shareTexts = {
"走，我们去摘甜甜的草莓！"
},
shareAnim = "AS_CH_Pose_001_PL_121",
sharePic = "T_Share_Suit_402940.astc",
shareNamePic = "T_Share_Suit_name_402940.astc",
shareBubblePic = "T_Share_Suit_frame_401000.astc"
},
[402950] = {
id = 402950,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "小青瓜",
desc = "不开心的时候，一起光合作用吧！",
icon = "CDN:Icon_PL_155",
getWay = "夏日派对",
jumpId = {
1056
},
outlookConf = v2,
resourceConf = {
model = "SK_PL_155",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_155_Physics",
IconLabelId = 102
},
outEnter = "AS_CH_Enter_PL_155",
outShow = "AS_CH_IdleShow_PL_155",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_155",
shareTexts = {
"跟你一起出去玩真的很开心呀！"
},
shareAnim = "AS_CH_Pose_001_PL_155",
beginTime = {
seconds = 1720713600
},
sharePic = "T_Share_Suit_402950.astc",
suitId = 322,
suitName = "小青瓜",
themedId = 16,
bpShowId = 1,
suitIcon = "CDN:Icon_PL_155",
shareNamePic = "T_Share_Suit_name_402950.astc",
shareBubblePic = "T_Share_Suit_frame_401000.astc",
ThemedShowIdList = {
{
key = 16,
value = 1
}
}
},
[402951] = {
id = 402951,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "小青瓜",
desc = "不开心的时候，一起光合作用吧！",
icon = "CDN:Icon_PL_155_01",
outlookConf = {
belongTo = 402950,
fashionValue = 35,
belongToGroup = {
402951,
402952
}
},
resourceConf = {
model = "SK_PL_155",
material = "MI_PL_155_1_HP01;MI_PL_155_2_HP01",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_155_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11395,
outEnter = "AS_CH_Enter_PL_155",
outShow = "AS_CH_IdleShow_PL_155",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_155",
shareTexts = {
"跟你一起出去玩真的很开心呀！"
},
shareAnim = "AS_CH_Pose_001_PL_155",
sharePic = "T_Share_Suit_402950.astc",
shareNamePic = "T_Share_Suit_name_402950.astc",
shareBubblePic = "T_Share_Suit_frame_401000.astc"
},
[402952] = {
id = 402952,
effect = true,
exceedReplaceItem = v0,
quality = 2,
name = "小青瓜",
desc = "不开心的时候，一起光合作用吧！",
icon = "CDN:Icon_PL_155_02",
outlookConf = {
belongTo = 402950,
fashionValue = 35,
belongToGroup = {
402951,
402952
}
},
resourceConf = {
model = "SK_PL_155",
material = "MI_PL_155_1_HP02;MI_PL_155_2_HP02",
headOffset = {
0,
0
},
backOffset = {
0,
0
},
faceOffset = {
0,
0
},
physics = "SK_PL_155_Physics",
materialSlot = "Skin;Skin_Translucent"
},
commodityId = 11396,
outEnter = "AS_CH_Enter_PL_155",
outShow = "AS_CH_IdleShow_PL_155",
outShowIntervalTime = 10,
outEnterSequence = "SQC_Enter_PL_155",
shareTexts = {
"跟你一起出去玩真的很开心呀！"
},
shareAnim = "AS_CH_Pose_001_PL_155",
sharePic = "T_Share_Suit_402950.astc",
shareNamePic = "T_Share_Suit_name_402950.astc",
shareBubblePic = "T_Share_Suit_frame_401000.astc"
}
}

local mt = {
effect = false,
type = "ItemType_Suit",
maxNum = 1,
exceedReplaceItem = {
{
itemId = 6,
itemNum = 100
}
},
quality = 3,
getWay = "商城",
jumpId = {
15
},
useType = "IUTO_None",
outlookConf = {
fashionValue = 125
},
outIdle = "AS_CH_OutIdle_001",
scaleTimes = 100,
shareAnim = "AS_CH_Pose_Common_001",
billboardOffsetZ = 0,
lotteryRewardShow = false,
lotteryPreviewShow = false,
mallHotShow = false,
mallAvatarShow = false,
shareOffset = {
0,
0
},
previewShareOffset = {
0,
0
},
bodytype = 0,
FootwearHeight = 0.0
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data