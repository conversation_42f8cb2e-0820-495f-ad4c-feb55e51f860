--com.tencent.wea.xlsRes.table_MatchTypeData => excel/xls/W_玩法模式_ugc.xlsx: 玩法

local v0 = 4

local v1 = 14

local v2 = "星世界精选匹配"

local v3 = "小游戏专区"

local v4 = "MTSC_Common"

local v5 = {
1,
2,
3,
4
}

local v6 = 6

local v7 = "UI_PlayerInfo_ModRecord_NomralBattleResult"

local v8 = 1

local v9 = 2

local v10 = -1

local data = {
[300] = {
id = 300,
modeID = 4,
desc = "UGC编辑器",
maxTeamMember = 4,
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_018",
image = "CDN:T_loading_Checkpoint_60101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 301,
battlePlayerNum = 16,
dropId = 6,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
descShort = "UGC编辑器",
battleRecordStyle = v7,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 30001,
buttonDesc = "单人",
gameTypeId = 300,
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 3,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[301] = {
id = 301,
modeID = 4,
desc = "星世界",
maxTeamMember = 4,
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_018",
image = "CDN:T_loading_Checkpoint_60101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 301,
battlePlayerNum = 16,
dropId = 6,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
descShort = "星世界",
battleRecordStyle = v7,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 30001,
buttonDesc = "单人",
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 3,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[302] = {
id = 302,
modeID = 4,
desc = "星海巡游",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 5,
value = 5
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_018",
image = "CDN:T_loading_Checkpoint_60101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
unlockRule = "5级解锁",
battleRecordCnt = 30,
matchRuleId = 301,
battlePlayerNum = 16,
dropId = 6,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
descShort = "星海巡游",
battleRecordStyle = v7,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 30001,
buttonDesc = "单人",
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 3,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[303] = {
id = 303,
modeID = 4,
desc = "新星海巡游",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 5
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_018",
image = "CDN:T_loading_Checkpoint_60101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 301,
battlePlayerNum = 16,
dropId = 6,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
descShort = "新星海巡游",
battleRecordStyle = v7,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 30001,
buttonDesc = "单人",
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 3,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[304] = {
id = 304,
modeID = 4,
desc = "星世界-多人游玩",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 5
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_018",
image = "CDN:T_loading_Checkpoint_60101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 301,
battlePlayerNum = 16,
dropId = 6,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
descShort = "多人房间",
battleRecordStyle = v7,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 30001,
buttonDesc = "创建房间",
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 3,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[305] = {
id = 305,
modeID = 4,
desc = "星世界-多人测试",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 5
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_018",
image = "CDN:T_loading_Checkpoint_60101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 301,
battlePlayerNum = 16,
dropId = 6,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
descShort = "多人测试房间",
battleRecordStyle = v7,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 30001,
buttonDesc = "创建房间",
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 8,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[306] = {
id = 306,
modeID = 4,
desc = "星友滴滴",
maxTeamMember = 4,
conditionGroup = {
condition = {
{
conditionType = 5
}
}
},
isPermanent = true,
sort = 200,
thumbImage = "CDN:T_ModelSelect_Img_Type_018",
image = "CDN:T_loading_Checkpoint_60101",
showType = "UI_Model_SingleMatchTypeItem",
detailDesc = "3",
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
battleRecordCnt = 30,
matchRuleId = 301,
battlePlayerNum = 16,
dropId = 6,
mmrType = "MST_Degree",
mmrAggrType = "MSAT_Avg",
descShort = "星友滴滴对局房间",
battleRecordStyle = v7,
outImage = "T_Lobby_Start_Trophy",
pakGroup = 30001,
buttonDesc = "创建房间",
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 9,
allowMidJoinBattle = 0,
allowRemoveAfterEndBattle = 0,
battleBehaviorRankReport = -1
},
[20000] = {
id = 20000,
modeID = 4,
desc = v2,
maxTeamMember = 2,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "2",
matchRuleId = 20000,
battlePlayerNum = 2,
dropId = 6,
descShort = "UGC-2人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20001] = {
id = 20001,
modeID = 4,
desc = v2,
maxTeamMember = 3,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "3",
matchRuleId = 20001,
battlePlayerNum = 3,
dropId = 6,
descShort = "UGC-3人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20002] = {
id = 20002,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "4",
matchRuleId = 20002,
battlePlayerNum = 4,
dropId = 6,
descShort = "UGC-4人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20003] = {
id = 20003,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "5",
matchRuleId = 20003,
battlePlayerNum = 5,
dropId = 6,
descShort = "UGC-5人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20004] = {
id = 20004,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "6",
matchRuleId = 20004,
battlePlayerNum = 6,
dropId = 6,
descShort = "UGC-6人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20005] = {
id = 20005,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "7",
matchRuleId = 20005,
battlePlayerNum = 7,
dropId = 6,
descShort = "UGC-7人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20006] = {
id = 20006,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "8",
matchRuleId = 20006,
battlePlayerNum = 8,
dropId = 6,
descShort = "UGC-8人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20007] = {
id = 20007,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "9",
matchRuleId = 20007,
battlePlayerNum = 9,
dropId = 6,
descShort = "UGC-9人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20008] = {
id = 20008,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 20008,
battlePlayerNum = 10,
dropId = 6,
descShort = "UGC-10人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20009] = {
id = 20009,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "11",
matchRuleId = 20009,
battlePlayerNum = 11,
dropId = 6,
descShort = "UGC-11人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20010] = {
id = 20010,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "12",
matchRuleId = 20010,
battlePlayerNum = 12,
dropId = 6,
descShort = "UGC-12人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20011] = {
id = 20011,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "13",
matchRuleId = 20011,
battlePlayerNum = 13,
dropId = 6,
descShort = "UGC-13人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20012] = {
id = 20012,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "14",
matchRuleId = 20012,
battlePlayerNum = 14,
dropId = 6,
descShort = "UGC-14人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20013] = {
id = 20013,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "15",
matchRuleId = 20013,
battlePlayerNum = 15,
dropId = 6,
descShort = "UGC-15人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20014] = {
id = 20014,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "16",
matchRuleId = 20014,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-16人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20015] = {
id = 20015,
modeID = 4,
desc = v2,
maxTeamMember = 1,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "2",
matchRuleId = 20015,
battlePlayerNum = 2,
dropId = 6,
descShort = "UGC-1V1",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20016] = {
id = 20016,
modeID = 4,
desc = v2,
maxTeamMember = 2,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "4",
matchRuleId = 20016,
battlePlayerNum = 4,
dropId = 6,
descShort = "UGC-2V2",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20017] = {
id = 20017,
modeID = 4,
desc = v2,
maxTeamMember = 3,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "6",
matchRuleId = 20017,
battlePlayerNum = 6,
dropId = 6,
descShort = "UGC-3V3",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20018] = {
id = 20018,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "8",
matchRuleId = 20018,
battlePlayerNum = 8,
dropId = 6,
descShort = "UGC-4V4",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20019] = {
id = 20019,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 20019,
battlePlayerNum = 10,
dropId = 6,
descShort = "UGC-5V5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20020] = {
id = 20020,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "12",
matchRuleId = 20020,
battlePlayerNum = 12,
dropId = 6,
descShort = "UGC-6V6",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20021] = {
id = 20021,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "16",
matchRuleId = 20021,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-8V8",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20022] = {
id = 20022,
modeID = 4,
desc = v2,
maxTeamMember = 2,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "3",
matchRuleId = 20022,
battlePlayerNum = 3,
dropId = 6,
descShort = "UGC-1vs2",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20023] = {
id = 20023,
modeID = 4,
desc = v2,
maxTeamMember = 3,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "4",
matchRuleId = 20023,
battlePlayerNum = 4,
dropId = 6,
descShort = "UGC-1vs3",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20024] = {
id = 20024,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "5",
matchRuleId = 20024,
battlePlayerNum = 5,
dropId = 6,
descShort = "UGC-1vs4",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20025] = {
id = 20025,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "6",
matchRuleId = 20025,
battlePlayerNum = 6,
dropId = 6,
descShort = "UGC-1vs5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20026] = {
id = 20026,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "7",
matchRuleId = 20026,
battlePlayerNum = 7,
dropId = 6,
descShort = "UGC-2vs5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20027] = {
id = 20027,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "9",
matchRuleId = 20027,
battlePlayerNum = 9,
dropId = 6,
descShort = "UGC-2vs7",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20028] = {
id = 20028,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "9",
matchRuleId = 20028,
battlePlayerNum = 9,
dropId = 6,
descShort = "UGC-4vs5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20029] = {
id = 20029,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "12",
matchRuleId = 20029,
battlePlayerNum = 12,
dropId = 6,
descShort = "UGC-4vs8",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20030] = {
id = 20030,
modeID = 4,
desc = v2,
maxTeamMember = 2,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "2",
matchRuleId = 20030,
battlePlayerNum = 2,
dropId = 6,
descShort = "UGC-2人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20031] = {
id = 20031,
modeID = 4,
desc = v2,
maxTeamMember = 3,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "3",
matchRuleId = 20031,
battlePlayerNum = 3,
dropId = 6,
descShort = "UGC-3人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20032] = {
id = 20032,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "4",
matchRuleId = 20032,
battlePlayerNum = 4,
dropId = 6,
descShort = "UGC-4人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20033] = {
id = 20033,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "5",
matchRuleId = 20033,
battlePlayerNum = 5,
dropId = 6,
descShort = "UGC-5人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20034] = {
id = 20034,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "6",
matchRuleId = 20034,
battlePlayerNum = 6,
dropId = 6,
descShort = "UGC-6人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20035] = {
id = 20035,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "7",
matchRuleId = 20035,
battlePlayerNum = 7,
dropId = 6,
descShort = "UGC-7人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20036] = {
id = 20036,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "8",
matchRuleId = 20036,
battlePlayerNum = 8,
dropId = 6,
descShort = "UGC-8人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20037] = {
id = 20037,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "9",
matchRuleId = 20037,
battlePlayerNum = 9,
dropId = 6,
descShort = "UGC-9人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20038] = {
id = 20038,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 20038,
battlePlayerNum = 10,
dropId = 6,
descShort = "UGC-10人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20039] = {
id = 20039,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "11",
matchRuleId = 20039,
battlePlayerNum = 11,
dropId = 6,
descShort = "UGC-11人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20040] = {
id = 20040,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "12",
matchRuleId = 20040,
battlePlayerNum = 12,
dropId = 6,
descShort = "UGC-12人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20041] = {
id = 20041,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "13",
matchRuleId = 20041,
battlePlayerNum = 13,
dropId = 6,
descShort = "UGC-13人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20042] = {
id = 20042,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "14",
matchRuleId = 20042,
battlePlayerNum = 14,
dropId = 6,
descShort = "UGC-14人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20043] = {
id = 20043,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "15",
matchRuleId = 20043,
battlePlayerNum = 15,
dropId = 6,
descShort = "UGC-15人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20044] = {
id = 20044,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "16",
matchRuleId = 20044,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-16人合作PVE",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20045] = {
id = 20045,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "6",
matchRuleId = 20045,
battlePlayerNum = 6,
dropId = 6,
descShort = "UGC-2vs4",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[20046] = {
id = 20046,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "8",
matchRuleId = 20046,
battlePlayerNum = 8,
dropId = 6,
descShort = "UGC-3vs5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[30000] = {
id = 30000,
modeID = 4,
desc = "星友滴滴",
maxTeamMember = 2,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "2",
matchRuleId = 30000,
battlePlayerNum = 2,
dropId = 6,
descShort = "UGC结伴同游",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
gameModeType = 3,
allowMidJoinBattle = 0,
allowRemoveAfterEndBattle = 0,
battleBehaviorRankReport = -1
},
[21015] = {
id = 21015,
modeID = 4,
desc = "奇妙决战台",
maxTeamMember = 2,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "2",
matchRuleId = 21015,
battlePlayerNum = 2,
dropId = 6,
descShort = "UGC-2人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21018] = {
id = 21018,
modeID = 4,
desc = "兵器之王",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "8",
matchRuleId = 21018,
battlePlayerNum = 8,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-4V4",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21019] = {
id = 21019,
modeID = 4,
desc = "炸船5v5匹配专用",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21019,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_PUGC_BombTheShip",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
warmRoundType = "WRST_PUGC_BombTheShip",
descShort = "UGC-5V5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
warmRoundRoomInfoId = 210190,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = 0
},
[21020] = {
id = 21020,
modeID = 4,
desc = "炸船6v6匹配专用",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "12",
matchRuleId = 21020,
battlePlayerNum = 12,
dropId = 6,
mmrType = "MST_PUGC_BombTheShip",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
warmRoundType = "WRST_PUGC_BombTheShip",
descShort = "UGC-6V6",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
warmRoundRoomInfoId = 210200,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = 0
},
[21021] = {
id = 21021,
modeID = 4,
desc = "孤岛行动",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21021,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-5v5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = 0
},
[21022] = {
id = 21022,
modeID = 4,
desc = "孤岛行动",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21022,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-5v5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = 0
},
[21023] = {
id = 21023,
modeID = 4,
desc = "兵器之王1人3AI",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "8",
matchRuleId = 21023,
battlePlayerNum = 8,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-4V4",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21024] = {
id = 21024,
modeID = 4,
desc = "我不是色盲",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21024,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-10人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21025] = {
id = 21025,
modeID = 4,
desc = "射击5v5全AI",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21025,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-5v5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21026] = {
id = 21026,
modeID = 4,
desc = "射击5V5真人2",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21026,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-5v5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21027] = {
id = 21027,
modeID = 4,
desc = "射击5V5真人3",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21027,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-5v5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21028] = {
id = 21028,
modeID = 4,
desc = "6人混战3AI",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "6",
matchRuleId = 21028,
battlePlayerNum = 6,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-6人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21029] = {
id = 21029,
modeID = 4,
desc = "8人混战4AI",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "8",
matchRuleId = 21029,
battlePlayerNum = 8,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-8人混战PVP",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21030] = {
id = 21030,
modeID = 4,
desc = "5V5真人1V1",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21030,
battlePlayerNum = 10,
dropId = 6,
mmrType = "MST_UGC_LuaProgram",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
descShort = "UGC-5v5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1
},
[21119] = {
id = 21119,
modeID = 4,
desc = "沙漠灰爆破5V5",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "10",
matchRuleId = 21119,
battlePlayerNum = 10,
dropId = 6,
mmrType = "WST_FPS_DesertGreyDemolition",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
warmRoundType = "WRST_FPS_DesertGreyDemolition",
descShort = "UGC-5V5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
warmRoundRoomInfoId = 211190,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = 1
},
[21120] = {
id = 21120,
modeID = 4,
desc = "沙漠灰爆破6V6",
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "12",
matchRuleId = 21120,
battlePlayerNum = 12,
dropId = 6,
mmrType = "WST_FPS_DesertGreyDemolition",
mmrAggrType = "MSAT_WeightedAvg",
mmrSettlementType = "MSST_SelfRankMixture",
warmRoundType = "WRST_FPS_DesertGreyDemolition",
descShort = "UGC-6V6",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
warmRoundRoomInfoId = 211200,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = 1
},
[22001] = {
id = 22001
},
[22002] = {
id = 22002
},
[22003] = {
id = 22003
},
[22004] = {
id = 22004
},
[22005] = {
id = 22005
},
[22006] = {
id = 22006
},
[22007] = {
id = 22007
},
[22008] = {
id = 22008
},
[22009] = {
id = 22009
},
[22010] = {
id = 22010
},
[22011] = {
id = 22011
},
[22012] = {
id = 22012
},
[22013] = {
id = 22013
},
[22014] = {
id = 22014
},
[22015] = {
id = 22015
},
[22016] = {
id = 22016
},
[22017] = {
id = 22017
},
[22018] = {
id = 22018
},
[22019] = {
id = 22019
},
[22020] = {
id = 22020
},
[22021] = {
id = 22021
},
[22022] = {
id = 22022
},
[22023] = {
id = 22023
},
[22024] = {
id = 22024
},
[22025] = {
id = 22025
},
[22026] = {
id = 22026
},
[22027] = {
id = 22027
},
[22028] = {
id = 22028
},
[22029] = {
id = 22029
},
[22030] = {
id = 22030
},
[22031] = {
id = 22031
},
[22032] = {
id = 22032
},
[22033] = {
id = 22033
},
[22034] = {
id = 22034
},
[22035] = {
id = 22035
},
[22036] = {
id = 22036
},
[22037] = {
id = 22037
},
[22038] = {
id = 22038
},
[22039] = {
id = 22039
},
[22040] = {
id = 22040
},
[22041] = {
id = 22041
},
[22042] = {
id = 22042
},
[22043] = {
id = 22043
},
[22044] = {
id = 22044
},
[22045] = {
id = 22045
},
[22046] = {
id = 22046
},
[22047] = {
id = 22047
},
[22048] = {
id = 22048
},
[22049] = {
id = 22049
},
[22050] = {
id = 22050
},
[22051] = {
id = 22051
},
[22052] = {
id = 22052
},
[22053] = {
id = 22053
},
[22054] = {
id = 22054
},
[22055] = {
id = 22055
},
[22056] = {
id = 22056
},
[22057] = {
id = 22057
},
[22058] = {
id = 22058
},
[22059] = {
id = 22059
},
[22060] = {
id = 22060
},
[22061] = {
id = 22061
},
[22062] = {
id = 22062
},
[22063] = {
id = 22063
},
[22064] = {
id = 22064
},
[22065] = {
id = 22065
},
[22066] = {
id = 22066
},
[22067] = {
id = 22067
},
[22068] = {
id = 22068
},
[22069] = {
id = 22069
},
[22070] = {
id = 22070
},
[22071] = {
id = 22071
},
[22072] = {
id = 22072
},
[22073] = {
id = 22073
},
[22074] = {
id = 22074
},
[22075] = {
id = 22075
},
[22076] = {
id = 22076
},
[22077] = {
id = 22077
},
[22078] = {
id = 22078
},
[22079] = {
id = 22079
},
[22080] = {
id = 22080
},
[22081] = {
id = 22081
},
[22082] = {
id = 22082
},
[22083] = {
id = 22083
},
[22084] = {
id = 22084
},
[22085] = {
id = 22085
},
[22086] = {
id = 22086
},
[22087] = {
id = 22087
},
[22088] = {
id = 22088
},
[22089] = {
id = 22089
},
[22090] = {
id = 22090,
modeID = 3
},
[22091] = {
id = 22091,
modeID = 3
},
[22092] = {
id = 22092,
modeID = 3
},
[22093] = {
id = 22093,
modeID = 3
},
[22094] = {
id = 22094,
modeID = 3
},
[22095] = {
id = 22095,
modeID = 3
},
[22096] = {
id = 22096,
modeID = 3
},
[22097] = {
id = 22097,
modeID = 3
},
[22098] = {
id = 22098,
modeID = 3
},
[22099] = {
id = 22099,
modeID = 3
},
[22100] = {
id = 22100,
modeID = 3
},
[20100] = {
id = 20100,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
matchRuleId = 20100,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-通用匹配1",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1,
useDynamicCfg = true
},
[20101] = {
id = 20101,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
matchRuleId = 20101,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-通用匹配2",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1,
useDynamicCfg = true
},
[20102] = {
id = 20102,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
matchRuleId = 20102,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-通用匹配3",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1,
useDynamicCfg = true
},
[20103] = {
id = 20103,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
matchRuleId = 20103,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-通用匹配4",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1,
useDynamicCfg = true
},
[20104] = {
id = 20104,
modeID = 4,
desc = v2,
maxTeamMember = 4,
isPermanent = true,
settleProc = v4,
matchTeamNum = v5,
teamTag = "1",
matchRuleId = 20104,
battlePlayerNum = 16,
dropId = 6,
descShort = "UGC-通用匹配5",
battleRecordStyle = v7,
layoutID = 1,
isShowEmotionEntrance = 2,
allowMidJoinBattle = 1,
allowRemoveAfterEndBattle = 1,
battleBehaviorRankReport = -1,
useDynamicCfg = true
},
[23001] = {
id = 23001,
modeID = 14,
desc = v3,
descShort = v3
},
[23002] = {
id = 23002,
modeID = 14,
desc = v3,
descShort = v3
},
[23003] = {
id = 23003,
modeID = 14,
desc = v3,
descShort = v3
},
[23004] = {
id = 23004,
modeID = 14,
desc = v3,
descShort = v3
},
[23005] = {
id = 23005,
modeID = 14,
desc = v3,
descShort = v3
},
[23006] = {
id = 23006,
modeID = 14,
desc = v3,
descShort = v3
},
[23007] = {
id = 23007,
modeID = 14,
desc = v3,
descShort = v3
},
[23008] = {
id = 23008,
modeID = 14,
desc = v3,
descShort = v3
},
[23009] = {
id = 23009,
modeID = 14,
desc = v3,
descShort = v3
},
[23010] = {
id = 23010,
modeID = 14,
desc = v3,
descShort = v3
},
[23011] = {
id = 23011,
modeID = 14,
desc = v3,
descShort = v3
},
[23012] = {
id = 23012,
modeID = 14,
desc = v3,
descShort = v3
},
[23013] = {
id = 23013,
modeID = 14,
desc = v3,
descShort = v3
},
[23014] = {
id = 23014,
modeID = 14,
desc = v3,
descShort = v3
},
[23015] = {
id = 23015,
modeID = 14,
desc = v3,
descShort = v3
},
[23016] = {
id = 23016,
modeID = 14,
desc = v3,
descShort = v3
},
[23017] = {
id = 23017,
modeID = 14,
desc = v3,
descShort = v3
},
[23018] = {
id = 23018,
modeID = 14,
desc = v3,
descShort = v3
},
[23019] = {
id = 23019,
modeID = 14,
desc = v3,
descShort = v3
},
[23020] = {
id = 23020,
modeID = 14,
desc = v3,
descShort = v3
}
}

local mt = {
modeID = 7,
desc = "星世界推荐",
conditionGroup = {
condition = {
{
conditionType = 1
}
}
},
isPermanent = false,
matchTeamNum = {
1
},
TeamMatchGame = false,
descShort = "星世界推荐",
gameTypeId = 301,
gameModeType = 6,
AutoDownloadPakGroup = {
30001
},
AutoDeletePakGroup = {
30001
},
PakPlayID = 801,
playName = "UGC",
useDynamicCfg = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data