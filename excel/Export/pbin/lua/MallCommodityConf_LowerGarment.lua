--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-下装

local v0 = {
seconds = 1676390400
}

local v1 = {
seconds = 1676476800
}

local v2 = {
seconds = 4074854400
}

local v3 = 1

local v4 = "赛季祈愿"

local v5 = "印章祈愿"

local v6 = 6

local v7 = 10000

local v8 = "1.3.6.1"

local data = {
[30001] = {
commodityId = 30001,
commodityName = "国潮少年下装",
beginTime = v0,
endTime = v1,
shopSort = 4,
jumpId = 38,
jumpText = "新星指南",
itemIds = {
520001
}
},
[30002] = {
commodityId = 30002,
commodityName = "夏日微风下装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v4,
itemIds = {
520004
},
bOpenSuit = true,
suitId = 1
},
[30003] = {
commodityId = 30003,
commodityName = "最佳员工下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520005
},
bOpenSuit = true,
suitId = 2
},
[30004] = {
commodityId = 30004,
commodityName = "蓝白运动服下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520006
}
},
[30005] = {
commodityId = 30005,
commodityName = "黑粉运动服下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520007
}
},
[30006] = {
commodityId = 30006,
commodityName = "清爽运动下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520008
},
bOpenSuit = true,
suitId = 3
},
[30007] = {
commodityId = 30007,
commodityName = "蓝色polo衫下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520009
}
},
[30008] = {
commodityId = 30008,
commodityName = "蓝色运动裤",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520010
}
},
[30009] = {
commodityId = 30009,
commodityName = "粉边黑裤子",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520011
}
},
[30010] = {
commodityId = 30010,
commodityName = "活力街头下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520012
}
},
[30011] = {
commodityId = 30011,
commodityName = "冬日暖意下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520013
}
},
[30012] = {
commodityId = 30012,
commodityName = "棒球少年下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520014
}
},
[30013] = {
commodityId = 30013,
commodityName = "灰色边缘下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520015
},
bOpenSuit = true,
suitId = 4
},
[30014] = {
commodityId = 30014,
commodityName = "星小递下装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1704383999
},
shopSort = 5,
jumpId = 44,
jumpText = "小乔送星运",
itemIds = {
520016
},
bOpenSuit = true,
suitId = 5
},
[30015] = {
commodityId = 30015,
commodityName = "沙滩旅客下装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 3,
jumpId = 9,
jumpText = "桃源通行证",
itemIds = {
520017
},
bOpenSuit = true,
suitId = 6
},
[30017] = {
commodityId = 30017,
commodityName = "紫色棉服下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520019
}
},
[30018] = {
commodityId = 30018,
commodityName = "黑白夹心下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520020
}
},
[30019] = {
commodityId = 30019,
commodityName = "挎包短袖套装下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520021
}
},
[30020] = {
commodityId = 30020,
commodityName = "荧光字母下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520022
}
},
[30021] = {
commodityId = 30021,
commodityName = "篮球少年下装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 6,
jumpId = 33,
jumpText = "闯关挑战",
itemIds = {
520023
},
bOpenSuit = true,
suitId = 7
},
[30022] = {
commodityId = 30022,
commodityName = "粉墨工匠下装",
beginTime = v0,
endTime = v1,
shopSort = 2,
itemIds = {
520024
},
suitId = 8
},
[30023] = {
commodityId = 30023,
commodityName = "赤土探险下装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v4,
itemIds = {
520025
},
bOpenSuit = true,
suitId = 9
},
[30024] = {
commodityId = 30024,
commodityName = "彩虹旋律下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520026
},
bOpenSuit = true,
suitId = 10
},
[30025] = {
commodityId = 30025,
commodityName = "烘焙甜心下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520027
},
bOpenSuit = true,
suitId = 11
},
[30026] = {
commodityId = 30026,
commodityName = "摩登学院下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520028
},
bOpenSuit = true,
suitId = 12
},
[30027] = {
commodityId = 30027,
commodityName = "草莓奶昔下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520029
}
},
[30028] = {
commodityId = 30028,
commodityName = "红莓丝绒下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520030
},
bOpenSuit = true,
suitId = 13
},
[30029] = {
commodityId = 30029,
commodityName = "芝士桃桃下装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1719504000
},
shopSort = 7,
itemIds = {
520031
},
suitId = 428
},
[30030] = {
commodityId = 30030,
commodityName = "榛果布丁下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520032
}
},
[30031] = {
commodityId = 30031,
commodityName = "三花摩卡下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520033
},
bOpenSuit = true,
suitId = 14
},
[30032] = {
commodityId = 30032,
commodityName = "白桃气泡下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520034
},
bOpenSuit = true,
suitId = 15
},
[30033] = {
commodityId = 30033,
commodityName = "蓝色童话下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520035
},
bOpenSuit = true,
suitId = 16
},
[30034] = {
commodityId = 30034,
commodityName = "海盐奶盖下装",
beginTime = {
seconds = 1702569600
},
endTime = {
seconds = 1706198399
},
shopSort = 2,
jumpId = 11,
jumpText = v4,
itemIds = {
520036
},
bOpenSuit = true,
suitId = 17
},
[30035] = {
commodityId = 30035,
commodityName = "紫藤幻梦下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520037
}
},
[30036] = {
commodityId = 30036,
commodityName = "青柠马卡龙下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520038
}
},
[30037] = {
commodityId = 30037,
commodityName = "黑莓可可下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520039
},
bOpenSuit = true,
suitId = 18
},
[30038] = {
commodityId = 30038,
commodityName = "蓝莓戚风下装",
beginTime = v0,
shopSort = 3,
jumpId = 189,
jumpText = v5,
itemIds = {
520040
},
bOpenSuit = true,
suitId = 19
},
[30039] = {
commodityId = 30039,
commodityName = "酷橙嘻哈侠下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520041
}
},
[30040] = {
commodityId = 30040,
commodityName = "萤绿嘻哈侠下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520042
}
},
[30041] = {
commodityId = 30041,
commodityName = "和平套装下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520043
}
},
[30042] = {
commodityId = 30042,
commodityName = "活力运动下装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1719504000
},
itemIds = {
520044
},
suitId = 427
},
[30043] = {
commodityId = 30043,
commodityName = "灰色边缘B下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520045
}
},
[30044] = {
commodityId = 30044,
commodityName = "麻将发财套装下装",
beginTime = {
seconds = 1730131200
},
endTime = {
seconds = 1731859199
},
shopSort = 2,
jumpId = 1063,
jumpText = "守护之翼",
minVersion = "1.3.26.33",
itemIds = {
520046
},
bOpenSuit = true,
suitId = 60
},
[30045] = {
commodityId = 30045,
commodityName = "UGC创作者成就奖励下装",
coinType = 6,
price = 10000,
beginTime = v0,
endTime = v1,
itemIds = {
520047
}
},
[30046] = {
commodityId = 30046,
commodityName = "花棉裤",
coinType = 6,
price = 150,
beginTime = {
seconds = 1705334400
},
endTime = {
seconds = 7258003199
},
shopSort = 1,
itemIds = {
520059
},
canGift = true,
addIntimacy = 15,
giftCoinType = 1,
giftPrice = 150,
suitId = 63
},
[31000] = {
commodityId = 31000,
commodityName = "像素风下装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v4,
minVersion = "1.2.67.1",
itemIds = {
520048
},
bOpenSuit = true,
suitId = 84
},
[31001] = {
commodityId = 31001,
commodityName = "涂鸦下装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v4,
minVersion = "1.2.67.1",
itemIds = {
520049
},
bOpenSuit = true,
suitId = 85
},
[31002] = {
commodityId = 31002,
commodityName = "麻将套红中下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520050
},
suitId = 87
},
[31003] = {
commodityId = 31003,
commodityName = "麻将套南风下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520051
},
suitId = 88
},
[31004] = {
commodityId = 31004,
commodityName = "校服下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520052
},
suitId = 91
},
[31005] = {
commodityId = 31005,
commodityName = "薯条下装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 175,
jumpText = v4,
minVersion = "1.2.67.1",
itemIds = {
520053
},
bOpenSuit = true,
suitId = 92
},
[31006] = {
commodityId = 31006,
commodityName = "FPS射击类匪装下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520054
},
suitId = 94
},
[31007] = {
commodityId = 31007,
commodityName = "唐装-CP下装",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
jumpId = 171,
jumpText = "充值送好礼",
minVersion = "1.2.80.1",
itemIds = {
520055
},
bOpenSuit = true,
suitId = 95
},
[31008] = {
commodityId = 31008,
commodityName = "唐装下装",
beginTime = {
seconds = 1707408000
},
endTime = {
seconds = 1708185599
},
jumpId = 171,
jumpText = "充值送好礼",
minVersion = "1.2.80.1",
itemIds = {
520056
},
bOpenSuit = true,
suitId = 98
},
[31009] = {
commodityId = 31009,
commodityName = "奶牛下装",
beginTime = {
seconds = 1707494400
},
endTime = {
seconds = 1713455999
},
jumpId = 181,
jumpText = "绮梦灯",
minVersion = "1.2.80.1",
itemIds = {
520057
},
bOpenSuit = true,
suitId = 96
},
[31010] = {
commodityId = 31010,
commodityName = "小老鼠下装",
beginTime = {
seconds = 1706198400
},
endTime = {
seconds = 1710431999
},
shopSort = 1,
jumpId = 9,
jumpText = "山海通行证",
minVersion = "1.2.67.1",
itemIds = {
520058
},
bOpenSuit = true,
suitId = 97
},
[31011] = {
commodityId = 31011,
commodityName = "水瓶座下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520060
},
suitId = 100
},
[31012] = {
commodityId = 31012,
commodityName = "赞不绝手下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520061
}
},
[31013] = {
commodityId = 31013,
commodityName = "寻味奇趣下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520062
}
},
[31014] = {
commodityId = 31014,
commodityName = "红莓酥糖下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.67.1",
itemIds = {
520063
}
},
[31015] = {
commodityId = 31015,
commodityName = "绝对爆发财下装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1719504000
},
itemIds = {
520064
},
suitId = 426
},
[31016] = {
commodityId = 31016,
commodityName = "绅士礼服下装",
coinType = 6,
price = 150,
beginTime = {
seconds = 1708704000
},
minVersion = "1.2.67.1",
itemIds = {
520065
},
canGift = true,
addIntimacy = 15,
giftCoinType = 1,
giftPrice = 150,
suitId = 179
},
[31101] = {
commodityId = 31101,
commodityName = "星夜绅士下装",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 9,
jumpText = "时光通行证",
itemIds = {
520066
},
bOpenSuit = true,
suitId = 156
},
[31102] = {
commodityId = 31102,
commodityName = "浪漫双鱼下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520067
}
},
[31103] = {
commodityId = 31103,
commodityName = "拼贴风尚下装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
520068
},
bOpenSuit = true,
suitId = 180
},
[31104] = {
commodityId = 31104,
commodityName = "日落海岛下装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
520069
},
bOpenSuit = true,
suitId = 181
},
[31105] = {
commodityId = 31105,
commodityName = "热带风情下装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
520070
},
bOpenSuit = true,
suitId = 182
},
[31106] = {
commodityId = 31106,
commodityName = "新奇撞色下装",
beginTime = {
seconds = 1711641600
},
endTime = {
seconds = 1715270399
},
jumpId = 189,
jumpText = v5,
itemIds = {
520071
},
bOpenSuit = true,
suitId = 183
},
[31107] = {
commodityId = 31107,
commodityName = "素雅新装下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520072
}
},
[31108] = {
commodityId = 31108,
commodityName = "莹紫葡萄下装",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 189,
jumpText = v4,
itemIds = {
520073
},
bOpenSuit = true,
suitId = 158
},
[31109] = {
commodityId = 31109,
commodityName = "星彩恋恋下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520074
}
},
[31110] = {
commodityId = 31110,
commodityName = "制胜代码下装",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 189,
jumpText = v4,
itemIds = {
520075
},
bOpenSuit = true,
suitId = 160
},
[31111] = {
commodityId = 31111,
commodityName = "幸运橙意下装",
beginTime = {
seconds = 1710432000
},
endTime = {
seconds = 1714060799
},
shopSort = 1,
jumpId = 189,
jumpText = v4,
itemIds = {
520076
},
bOpenSuit = true,
suitId = 159
},
[31112] = {
commodityId = 31112,
commodityName = "星海领航员下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520077
}
},
[31113] = {
commodityId = 31113,
commodityName = "荣誉新生下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520078
},
suitId = 166
},
[31114] = {
commodityId = 31114,
commodityName = "爱意热诚下装",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 9,
jumpText = "潮音通行证",
itemIds = {
520079
},
bOpenSuit = true,
suitId = 203
},
[31115] = {
commodityId = 31115,
commodityName = "涂鸦印记下装",
beginTime = {
seconds = 1713542400
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 177,
jumpText = "暗夜冰羽",
itemIds = {
520080
},
bOpenSuit = true,
suitId = 186
},
[31116] = {
commodityId = 31116,
commodityName = "极速风潮下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520081
}
},
[31117] = {
commodityId = 31117,
commodityName = "清凉海浪下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520082
}
},
[31118] = {
commodityId = 31118,
commodityName = "丝绒格调下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520083
}
},
[31119] = {
commodityId = 31119,
commodityName = "别来沾边下装",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = "1.2.100.1",
itemIds = {
520084
},
bOpenSuit = true,
suitId = 213
},
[31120] = {
commodityId = 31120,
commodityName = "无语时刻下装",
beginTime = {
seconds = 1714665600
},
endTime = {
seconds = 1719503999
},
shopSort = 1,
jumpId = 186,
jumpText = "春水溯游",
minVersion = "1.2.100.1",
itemIds = {
520085
},
bOpenSuit = true,
suitId = 214
},
[31121] = {
commodityId = 31121,
commodityName = "春日校园下装",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = "1.2.100.1",
itemIds = {
520086
},
bOpenSuit = true,
suitId = 201
},
[31122] = {
commodityId = 31122,
commodityName = "春风探险下装",
beginTime = {
seconds = 1714060800
},
endTime = {
seconds = 1717689599
},
shopSort = 1,
jumpId = 180,
jumpText = v4,
minVersion = "1.2.100.1",
itemIds = {
520087
},
bOpenSuit = true,
suitId = 202
},
[31123] = {
commodityId = 31123,
commodityName = "邻家学妹下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520088
}
},
[31124] = {
commodityId = 31124,
commodityName = "学生会长下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520089
}
},
[31125] = {
commodityId = 31125,
commodityName = "暖暖白羊下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520090
}
},
[31126] = {
commodityId = 31126,
commodityName = "隐藏富豪下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520091
}
},
[31127] = {
commodityId = 31127,
commodityName = "年会爆款下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520092
}
},
[31128] = {
commodityId = 31128,
commodityName = "进步青年下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520093
}
},
[31129] = {
commodityId = 31129,
commodityName = "职业轻装下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520094
}
},
[31130] = {
commodityId = 31130,
commodityName = "夏日炎炎下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = "1.2.100.1",
itemIds = {
520095
}
},
[31131] = {
commodityId = 31131,
commodityName = "小丸子运动服下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520096
},
suitId = 209
},
[31132] = {
commodityId = 31132,
commodityName = "小丸子爷爷同款下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520097
},
suitId = 210
},
[31133] = {
commodityId = 31133,
commodityName = "草莓抹茶下装",
beginTime = {
seconds = 1719244800
},
endTime = {
seconds = 1720540799
},
jumpId = 1046,
jumpText = "星梭祈愿",
minVersion = "1.3.7.53",
itemIds = {
520098
},
bOpenSuit = true,
suitId = 450
},
[31134] = {
commodityId = 31134,
commodityName = "元气之星下装",
beginTime = {
seconds = 1716566400
},
endTime = {
seconds = 1798732799
},
jumpId = 176,
jumpText = "星梦使者",
itemIds = {
520099
},
bOpenSuit = true,
suitId = 233
},
[31135] = {
commodityId = 31135,
commodityName = "梦里寻粽下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520100
}
},
[31136] = {
commodityId = 31136,
commodityName = "布朗尼尼下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520101
}
},
[31137] = {
commodityId = 31137,
commodityName = "粉桃朵朵下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520102
}
},
[31138] = {
commodityId = 31138,
commodityName = "晚宴绅士下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520103
}
},
[31139] = {
commodityId = 31139,
commodityName = "通行卫士下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520104
}
},
[31140] = {
commodityId = 31140,
commodityName = "古韵长衣下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520105
},
suitId = 416
},
[31141] = {
commodityId = 31141,
commodityName = "气息归元下装",
coinType = 6,
price = 10000,
endTime = v2,
itemIds = {
520106
},
suitId = 417
},
[31142] = {
commodityId = 31142,
commodityName = "青春领航下装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v8,
itemIds = {
520107
},
bOpenSuit = true,
suitId = 401
},
[31143] = {
commodityId = 31143,
commodityName = "海岛一刻下装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v8,
itemIds = {
520108
},
bOpenSuit = true,
suitId = 402
},
[31144] = {
commodityId = 31144,
commodityName = "海盐兔兔下装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
520109
},
bOpenSuit = true,
suitId = 403
},
[31145] = {
commodityId = 31145,
commodityName = "灰调潮流下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520110
},
suitId = 404
},
[31146] = {
commodityId = 31146,
commodityName = "灰粉嘻哈下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520111
},
suitId = 405
},
[31147] = {
commodityId = 31147,
commodityName = "多元时尚下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520112
},
suitId = 406
},
[31148] = {
commodityId = 31148,
commodityName = "多云转晴下装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
520113
},
bOpenSuit = true,
suitId = 408
},
[31149] = {
commodityId = 31149,
commodityName = "慢热金牛下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520114
},
suitId = 410
},
[31150] = {
commodityId = 31150,
commodityName = "蔚蓝晴空下装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v8,
itemIds = {
520115
},
bOpenSuit = true,
suitId = 411
},
[31151] = {
commodityId = 31151,
commodityName = "春城粉桃下装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1085,
jumpText = "浪漫旅程",
minVersion = "1.3.7.94",
itemIds = {
520116
},
bOpenSuit = true,
suitId = 412
},
[31152] = {
commodityId = 31152,
commodityName = "奇喵物语下装",
beginTime = {
seconds = 1717689600
},
endTime = {
seconds = 1721318399
},
shopSort = 1,
jumpId = 11,
jumpText = v4,
minVersion = v8,
itemIds = {
520117
},
bOpenSuit = true,
suitId = 413
},
[31153] = {
commodityId = 31153,
commodityName = "青春筑梦下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520118
},
suitId = 415
},
[31154] = {
commodityId = 31154,
commodityName = "茸茸睡衣下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520119
},
suitId = 419
},
[31155] = {
commodityId = 31155,
commodityName = "蒸汽工程下装",
beginTime = {
seconds = 1719504000
},
endTime = {
seconds = 1798732799
},
jumpId = 311,
jumpText = "守护圣翼",
minVersion = "1.3.7.75",
itemIds = {
520120
},
bOpenSuit = true,
suitId = 420
},
[31156] = {
commodityId = 31156,
commodityName = "果味缤纷下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520121
},
suitId = 422
},
[31157] = {
commodityId = 31157,
commodityName = "熊熊出游下装",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = "1.3.12.1",
itemIds = {
520122
},
bOpenSuit = true,
suitId = 430
},
[31158] = {
commodityId = 31158,
commodityName = "悠扬青春下装",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = "1.3.12.1",
itemIds = {
520123
},
bOpenSuit = true,
suitId = 431
},
[31159] = {
commodityId = 31159,
commodityName = "酸甜柠趣下装",
beginTime = {
seconds = 1749744000
},
endTime = {
seconds = 1755792000
},
jumpId = 694,
jumpText = "幻音喵境",
minVersion = v8,
itemIds = {
520124
},
bOpenSuit = true,
suitId = 432
},
[31160] = {
commodityId = 31160,
commodityName = "夏意清新下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520125
},
suitId = 433
},
[31161] = {
commodityId = 31161,
commodityName = "盛夏果实下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520126
},
suitId = 434
},
[31162] = {
commodityId = 31162,
commodityName = "时尚教主下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520127
},
suitId = 435
},
[31163] = {
commodityId = 31163,
commodityName = "古韵中轴下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520128
},
suitId = 440
},
[31164] = {
commodityId = 31164,
commodityName = "芝芝桃桃下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520129
},
suitId = 441
},
[31165] = {
commodityId = 31165,
commodityName = "缤纷夏花下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520130
},
suitId = 442
},
[31166] = {
commodityId = 31166,
commodityName = "首席射手下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520131
},
suitId = 443
},
[31167] = {
commodityId = 31167,
commodityName = "静谧狩猎下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520132
},
suitId = 444
},
[31168] = {
commodityId = 31168,
commodityName = "弹跳乒乓下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520133
},
suitId = 445
},
[31169] = {
commodityId = 31169,
commodityName = "无限换防下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520134
},
suitId = 446
},
[31170] = {
commodityId = 31170,
commodityName = "活力网坛下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520135
},
suitId = 447
},
[31171] = {
commodityId = 31171,
commodityName = "精准投手下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520136
},
suitId = 448
},
[31172] = {
commodityId = 31172,
commodityName = "凌空飞跃下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520137
},
suitId = 449
},
[31173] = {
commodityId = 31173,
commodityName = "郊游小熊下装",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 9,
jumpText = "乐园通行证",
minVersion = "1.3.12.1",
itemIds = {
520138
},
bOpenSuit = true,
suitId = 451
},
[31174] = {
commodityId = 31174,
commodityName = "仲夏芳菲下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520139
},
suitId = 452
},
[31175] = {
commodityId = 31175,
commodityName = "童梦奇缘下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520140
},
suitId = 453
},
[31176] = {
commodityId = 31176,
commodityName = "青柠之恋下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520141
},
suitId = 454
},
[31177] = {
commodityId = 31177,
commodityName = "球场裁判下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520142
},
suitId = 456
},
[31178] = {
commodityId = 31178,
commodityName = "数字休闲下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520143
},
suitId = 457
},
[31179] = {
commodityId = 31179,
commodityName = "波普艺术下装",
beginTime = {
seconds = 1721318400
},
endTime = {
seconds = 1724947199
},
shopSort = 1,
jumpId = 706,
jumpText = v4,
minVersion = "1.3.12.1",
itemIds = {
520144
},
bOpenSuit = true,
suitId = 429
},
[31180] = {
commodityId = 31180,
commodityName = "活力绅士下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520145
},
suitId = 458
},
[31181] = {
commodityId = 31181,
commodityName = "气质暖狼下装",
beginTime = {
seconds = 1723737600
},
endTime = {
seconds = 1730390399
},
jumpId = 610,
jumpText = "夏夜绮梦",
minVersion = v8,
itemIds = {
520146
},
bOpenSuit = true,
suitId = 459
},
[31182] = {
commodityId = 31182,
commodityName = "开心一刻下装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
520147
},
bOpenSuit = true,
suitId = 460
},
[31183] = {
commodityId = 31183,
commodityName = "揶揄时光下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520148
},
suitId = 461
},
[31184] = {
commodityId = 31184,
commodityName = "快乐鼬鼬下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520149
},
suitId = 464
},
[31185] = {
commodityId = 31185,
commodityName = "精致领班下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520150
},
suitId = 465
},
[31186] = {
commodityId = 31186,
commodityName = "美味晨光下装",
beginTime = {
seconds = 1726329600
},
endTime = {
seconds = 1727366399
},
shopSort = 1,
jumpId = 364,
jumpText = "山河卷扇",
minVersion = "1.3.18.37",
itemIds = {
520151
},
bOpenSuit = true,
suitId = 466
},
[31187] = {
commodityId = 31187,
commodityName = "萌虎出动下装",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v4,
minVersion = v8,
itemIds = {
520152
},
bOpenSuit = true,
suitId = 467
},
[31188] = {
commodityId = 31188,
commodityName = "专业态度下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520153
},
suitId = 468
},
[31189] = {
commodityId = 31189,
commodityName = "潮酷玩家下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520154
},
suitId = 470
},
[31190] = {
commodityId = 31190,
commodityName = "赛场飞驰下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520155
},
suitId = 471
},
[31191] = {
commodityId = 31191,
commodityName = "浅蓝节拍下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520156
},
suitId = 472
},
[31192] = {
commodityId = 31192,
commodityName = "春日运动下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520157
},
suitId = 473
},
[31193] = {
commodityId = 31193,
commodityName = "清秋月圆下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520158
},
suitId = 474
},
[31194] = {
commodityId = 31194,
commodityName = "摩登新贵下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520159
},
suitId = 477
},
[31195] = {
commodityId = 31195,
commodityName = "饭团星球下装",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v4,
minVersion = v8,
itemIds = {
520160
},
bOpenSuit = true,
suitId = 479
},
[31196] = {
commodityId = 31196,
commodityName = "蔷薇星云下装",
beginTime = {
seconds = 1724947200
},
endTime = {
seconds = 1729180799
},
jumpId = 619,
jumpText = v4,
minVersion = v8,
itemIds = {
520161
},
bOpenSuit = true,
suitId = 480
},
[31197] = {
commodityId = 31197,
commodityName = "职场萌星下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520162
},
suitId = 485
},
[31198] = {
commodityId = 31198,
commodityName = "微笑向阳下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520163
},
suitId = 488
},
[31199] = {
commodityId = 31199,
commodityName = "校园传说下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520164
},
suitId = 489
},
[31200] = {
commodityId = 31200,
commodityName = "松果收藏家下装",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = v8,
itemIds = {
520165
},
bOpenSuit = true,
suitId = 490
},
[31201] = {
commodityId = 31201,
commodityName = "占星学院下装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = v8,
itemIds = {
520166
},
bOpenSuit = true,
suitId = 491
},
[31202] = {
commodityId = 31202,
commodityName = "富贵公子下装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = v8,
itemIds = {
520167
},
bOpenSuit = true,
suitId = 492
},
[31203] = {
commodityId = 31203,
commodityName = "浪尖飞驰下装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v8,
itemIds = {
520168
},
bOpenSuit = true,
suitId = 493
},
[31204] = {
commodityId = 31204,
commodityName = "田园牧歌下装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732723200
},
jumpId = 33,
jumpText = "闯关挑战",
minVersion = v8,
itemIds = {
520169
},
bOpenSuit = true,
suitId = 494
},
[31205] = {
commodityId = 31205,
commodityName = "松间晨雾下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520170
},
suitId = 495
},
[31206] = {
commodityId = 31206,
commodityName = "星空梦想下装",
beginTime = {
seconds = 1729180800
},
endTime = {
seconds = 1732809599
},
shopSort = 1,
jumpId = 623,
jumpText = v4,
minVersion = v8,
itemIds = {
520171
},
bOpenSuit = true,
suitId = 496
},
[31207] = {
commodityId = 31207,
commodityName = "暖冬物语下装",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
520172
},
bOpenSuit = true,
suitId = 498
},
[31208] = {
commodityId = 31208,
commodityName = "赐福锦鲤下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520173
},
suitId = 499
},
[31209] = {
commodityId = 31209,
commodityName = "芝香披萨下装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
520174
},
bOpenSuit = true,
suitId = 501
},
[31210] = {
commodityId = 31210,
commodityName = "爱心波波下装",
beginTime = {
seconds = 1744905600
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 1086,
jumpText = "青霄龙吟",
minVersion = "1.3.18.71",
itemIds = {
520175
},
bOpenSuit = true,
suitId = 503
},
[31211] = {
commodityId = 31211,
commodityName = "堡你喜欢下装",
beginTime = {
seconds = 1731600000
},
endTime = {
seconds = 1738425599
},
jumpId = 628,
jumpText = "星之恋空",
minVersion = v8,
itemIds = {
520176
},
bOpenSuit = true,
suitId = 505
},
[31212] = {
commodityId = 31212,
commodityName = "星动曲奇下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520177
},
suitId = 507
},
[31213] = {
commodityId = 31213,
commodityName = "粉焰旋风下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520178
},
suitId = 513
},
[31214] = {
commodityId = 31214,
commodityName = "蔚蓝闪电下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520179
},
suitId = 515
},
[31215] = {
commodityId = 31215,
commodityName = "逐梦狂飙下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520180
},
suitId = 517
},
[31216] = {
commodityId = 31216,
commodityName = "云龙武袍下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520181
},
suitId = 519
},
[31217] = {
commodityId = 31217,
commodityName = "青岚短衫下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520182
},
suitId = 541
},
[31218] = {
commodityId = 31218,
commodityName = "沙滩宝贝下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520183
},
suitId = 543
},
[31219] = {
commodityId = 31219,
commodityName = "酣眠之冬下装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = v8,
itemIds = {
520184
},
bOpenSuit = true,
suitId = 545
},
[31220] = {
commodityId = 31220,
commodityName = "素食潮流下装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v8,
itemIds = {
520185
},
bOpenSuit = true,
suitId = 547
},
[31221] = {
commodityId = 31221,
commodityName = "暖樱冬语下装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = v8,
itemIds = {
520186
},
bOpenSuit = true,
suitId = 549
},
[31222] = {
commodityId = 31222,
commodityName = "速食美学下装",
beginTime = {
seconds = 1732809600
},
endTime = {
seconds = 1737043199
},
shopSort = 1,
jumpId = 630,
jumpText = v4,
minVersion = v8,
itemIds = {
520187
},
bOpenSuit = true,
suitId = 551
},
[31223] = {
commodityId = 31223,
commodityName = "目览金秋下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520188
},
suitId = 553
},
[31224] = {
commodityId = 31224,
commodityName = "民德正雅下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520189
},
suitId = 555
},
[31225] = {
commodityId = 31225,
commodityName = "腾云一舞下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520190
},
suitId = 559
},
[31226] = {
commodityId = 31226,
commodityName = "竹韵清扬下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520191
},
suitId = 561
},
[31227] = {
commodityId = 31227,
commodityName = "墨韵乘云下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520192
},
suitId = 563
},
[31228] = {
commodityId = 31228,
commodityName = "桃心王子下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520193
},
suitId = 565
},
[31229] = {
commodityId = 31229,
commodityName = "梅花公爵下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520194
},
suitId = 567
},
[31230] = {
commodityId = 31230,
commodityName = "黑白迷踪下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520195
},
suitId = 577
},
[31231] = {
commodityId = 31231,
commodityName = "奶芙蝶语下装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = v8,
itemIds = {
520196
},
bOpenSuit = true,
suitId = 593
},
[31232] = {
commodityId = 31232,
commodityName = "自愿上学下装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = v8,
itemIds = {
520197
},
bOpenSuit = true,
suitId = 595
},
[31233] = {
commodityId = 31233,
commodityName = "自愿上班下装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 636,
jumpText = v4,
minVersion = v8,
itemIds = {
520198
},
bOpenSuit = true,
suitId = 597
},
[31234] = {
commodityId = 31234,
commodityName = "冬日暖喵下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520199
},
suitId = 599
},
[31235] = {
commodityId = 31235,
commodityName = "林深如墨下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520200
},
suitId = 601
},
[31236] = {
commodityId = 31236,
commodityName = "冬夜星火下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520201
},
suitId = 603
},
[31237] = {
commodityId = 31237,
commodityName = "鸭鸭护卫队下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520202
},
suitId = 605
},
[31238] = {
commodityId = 31238,
commodityName = "脆弱鸭鸭下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520203
},
suitId = 607
},
[31239] = {
commodityId = 31239,
commodityName = "冬暖花眠下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520204
},
suitId = 609
},
[31240] = {
commodityId = 31240,
commodityName = "拥抱侏罗纪下装",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v8,
itemIds = {
520205
},
bOpenSuit = true,
suitId = 615
},
[31241] = {
commodityId = 31241,
commodityName = "漆墨拼图下装",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v8,
itemIds = {
520206
},
bOpenSuit = true,
suitId = 617
},
[31242] = {
commodityId = 31242,
commodityName = "蓝庭花语下装",
beginTime = {
seconds = 1735660800
},
endTime = {
seconds = 1746115199
},
shopSort = 1,
jumpId = 634,
jumpText = "云梦绮旅",
minVersion = v8,
itemIds = {
520207
},
bOpenSuit = true,
suitId = 619
},
[31243] = {
commodityId = 31243,
commodityName = "自然精灵下装",
beginTime = {
seconds = 1747324800
},
endTime = {
seconds = 1751299199
},
jumpId = 88888,
jumpText = "幻彩调律",
minVersion = v8,
itemIds = {
520208
},
bOpenSuit = true,
suitId = 623
},
[31244] = {
commodityId = 31244,
commodityName = "冬日多巴胺下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520209
},
suitId = 625
},
[31245] = {
commodityId = 31245,
commodityName = "平安喜乐下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520210
},
suitId = 627
},
[31246] = {
commodityId = 31246,
commodityName = "欢语秋藏下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520211
},
suitId = 629
},
[31247] = {
commodityId = 31247,
commodityName = "层林覆雪下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520212
},
suitId = 631
},
[31248] = {
commodityId = 31248,
commodityName = "遗迹寻踪下装",
beginTime = {
seconds = 1737043200
},
endTime = {
seconds = 1741881599
},
shopSort = 1,
jumpId = 9,
jumpText = "赛季通行证",
minVersion = v8,
itemIds = {
520213
},
bOpenSuit = true,
suitId = 637
},
[31249] = {
commodityId = 31249,
commodityName = "大发特发下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520214
},
suitId = 639
},
[31250] = {
commodityId = 31250,
commodityName = "半糖心织下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520215
},
suitId = 643
},
[31251] = {
commodityId = 31251,
commodityName = "鲤跃福临下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520216
},
suitId = 645
},
[31252] = {
commodityId = 31252,
commodityName = "落樱衬衫下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520217
},
suitId = 649
},
[31253] = {
commodityId = 31253,
commodityName = "祥龙送宝下装",
beginTime = {
seconds = 1740067200
},
endTime = {
seconds = 1746028799
},
jumpId = 8018,
jumpText = "桃源万千",
minVersion = "1.3.68.100",
itemIds = {
520218
},
bOpenSuit = true,
suitId = 651
},
[31254] = {
commodityId = 31254,
commodityName = "元宵宝宝下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520219
},
suitId = 679
},
[31255] = {
commodityId = 31255,
commodityName = "萌动旋风下装",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v4,
minVersion = v8,
itemIds = {
520220
},
bOpenSuit = true,
suitId = 689
},
[31256] = {
commodityId = 31256,
commodityName = "悦动甜心下装",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v4,
minVersion = v8,
itemIds = {
520221
},
bOpenSuit = true,
suitId = 691
},
[31257] = {
commodityId = 31257,
commodityName = "原野兔踪下装",
beginTime = {
seconds = 1741881600
},
endTime = {
seconds = 1746028800
},
jumpId = 640,
jumpText = v4,
minVersion = v8,
itemIds = {
520222
},
bOpenSuit = true,
suitId = 693
},
[31258] = {
commodityId = 31258,
commodityName = "向阳之花下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520223
},
suitId = 695
},
[31259] = {
commodityId = 31259,
commodityName = "牛油果之友下装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520224
},
bOpenSuit = true,
suitId = 697
},
[31260] = {
commodityId = 31260,
commodityName = "果冻熊之友下装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520225
},
bOpenSuit = true,
suitId = 699
},
[31261] = {
commodityId = 31261,
commodityName = "棉花狗之友下装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520226
},
bOpenSuit = true,
suitId = 701
},
[31262] = {
commodityId = 31262,
commodityName = "樱野纷纷下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520227
},
suitId = 703
},
[31263] = {
commodityId = 31263,
commodityName = "吟游之声下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520228
},
suitId = 705
},
[31264] = {
commodityId = 31264,
commodityName = "幻域之风下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520229
},
suitId = 707
},
[31265] = {
commodityId = 31265,
commodityName = "迷彩风尚下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520230
},
suitId = 709
},
[31266] = {
commodityId = 31266,
commodityName = "活力竖纹下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520231
},
suitId = 843
},
[31267] = {
commodityId = 31267,
commodityName = "雪人物语下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520232
},
suitId = 845
},
[31268] = {
commodityId = 31268,
commodityName = "海岛风情下装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520233
},
bOpenSuit = true,
suitId = 847
},
[31269] = {
commodityId = 31269,
commodityName = "花舞樱樱下装",
beginTime = {
seconds = 1743091200
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520234
},
bOpenSuit = true,
suitId = 849
},
[31270] = {
commodityId = 31270,
commodityName = "荣冕侍卫下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520235
},
suitId = 851
},
[31271] = {
commodityId = 31271,
commodityName = "空野之啼下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520236
},
suitId = 853
},
[31272] = {
commodityId = 31272,
commodityName = "兰花倩影下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520237
},
suitId = 855
},
[31273] = {
commodityId = 31273,
commodityName = "深谷幽蔷下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520238
},
suitId = 857
},
[31274] = {
commodityId = 31274,
commodityName = "碧竹丛意下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520239
},
suitId = 859
},
[31275] = {
commodityId = 31275,
commodityName = "律动多巴胺下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520240
},
suitId = 871
},
[31276] = {
commodityId = 31276,
commodityName = "像素叠叠乐下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520241
},
suitId = 873
},
[31277] = {
commodityId = 31277,
commodityName = "火树银花下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520242
},
suitId = 875
},
[31278] = {
commodityId = 31278,
commodityName = "数字边缘下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520243
},
suitId = 877
},
[31279] = {
commodityId = 31279,
commodityName = "淡淡甜意下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520244
},
suitId = 879
},
[31280] = {
commodityId = 31280,
commodityName = "道士下山下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520245
},
suitId = 881
},
[31281] = {
commodityId = 31281,
commodityName = "甜蜜梦乡下装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520246
},
bOpenSuit = true,
suitId = 889
},
[31282] = {
commodityId = 31282,
commodityName = "花间睡服下装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520247
},
bOpenSuit = true,
suitId = 891
},
[31283] = {
commodityId = 31283,
commodityName = "星河入梦下装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520248
},
bOpenSuit = true,
suitId = 893
},
[31284] = {
commodityId = 31284,
commodityName = "无贝不宝下装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520249
},
bOpenSuit = true,
suitId = 897
},
[31285] = {
commodityId = 31285,
commodityName = "无宝不贝下装",
beginTime = {
seconds = 1745856000
},
jumpId = 705,
jumpText = v5,
minVersion = v8,
itemIds = {
520250
},
bOpenSuit = true,
suitId = 899
},
[31286] = {
commodityId = 31286,
commodityName = "森猎原野下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520251
},
suitId = 901
},
[31287] = {
commodityId = 31287,
commodityName = "像素小红狐下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520252
},
suitId = 915
},
[31288] = {
commodityId = 31288,
commodityName = "流浪幽咪下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520253
},
suitId = 931
},
[31289] = {
commodityId = 31289,
commodityName = "半糖青春下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520254
},
suitId = 933
},
[31290] = {
commodityId = 31290,
commodityName = "快快向左下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520255
},
suitId = 935
},
[31291] = {
commodityId = 31291,
commodityName = "快快向右下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520256
},
suitId = 937
},
[31292] = {
commodityId = 31292,
commodityName = "乐在中央下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520257
},
suitId = 939
},
[31293] = {
commodityId = 31293,
commodityName = "酷鲨航海家下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520258
},
suitId = 941
},
[31294] = {
commodityId = 31294,
commodityName = "萌鲨旅行家下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520259
},
suitId = 943
},
[31295] = {
commodityId = 31295,
commodityName = "星梦飞扬下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520260
},
suitId = 945
},
[31296] = {
commodityId = 31296,
commodityName = "紫雾蝶语下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520261
},
suitId = 947
},
[31297] = {
commodityId = 31297,
commodityName = "雾都情书下装",
coinType = 6,
price = 10000,
endTime = v2,
minVersion = v8,
itemIds = {
520262
},
suitId = 913
}
}

local mt = {
mallId = 8,
limitType = "MCL_LifeLongLimit",
limitNum = 1,
beginTime = {
seconds = 4074768000
},
shopTag = {
2,
9
},
gender = 0,
canDirectBuy = false,
itemNums = {
1
},
bOpenSuit = false,
canGift = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data