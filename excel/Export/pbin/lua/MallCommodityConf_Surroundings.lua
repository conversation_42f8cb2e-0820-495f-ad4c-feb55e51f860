--com.tencent.wea.xlsRes.table_MallCommodityConf => excel/xls/S_商城_商品.xlsx: 商城-环绕物

local data = {
[99001] = {
mallId = 195,
commodityId = 99001,
commodityName = "青莲落",
coinType = 6,
price = 666,
shopTag = {
7,
32
},
gender = 0,
itemIds = {
750001
},
itemNums = {
1
}
},
[99002] = {
mallId = 195,
commodityId = 99002,
commodityName = "红莲生",
coinType = 6,
price = 888,
shopTag = {
7,
32
},
gender = 0,
itemIds = {
750002
},
itemNums = {
1
}
}
}

local mt = {

}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data