--com.tencent.wea.xlsRes.table_ActivityMainConfig => excel/xls/HOK/H_活动中心配置_HOK.xlsx: 活动配置

local data = {
[335501] = {
id = 335501,
activityType = "ATTask",
timeInfo = {
beginTime = {
seconds = 1744300800
},
endTime = {
seconds = 1776700799
},
showEndTime = {
seconds = 1776700799
},
showBeginTime = {
seconds = 1744300800
}
},
labelId = 3,
activityName = "mobaAI邀请每日任务",
activityTaskGroup = {
3355001
},
activityNameType = "ANTMobaAiActive",
activitySubName = "mobaAI邀请每日任务"
}
}

local mt = {
isHideMainBackground = false
}
local base = {
__index = mt--[[,
__newindex = function()
error( "Attempt to modify read-only table" )
end]]--
}
for _, v in pairs( data ) do
setmetatable( v, base )
end
base.__metatable = false


return data