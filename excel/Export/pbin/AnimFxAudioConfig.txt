com.tencent.wea.xlsRes.table_AnimFxAudioConfig
excel/xls/Y_音频之动画特效声音.xlsx sheet:Sheet1
rows {
  id: "1000"
  animName: "AS_CH_Run_003"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1001"
  animName: "AS_CH_Run_003"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1002"
  animName: "AS_CH_Run_003"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1003"
  animName: "AS_CH_Run_003"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1004"
  animName: "AS_CH_Walk_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.18
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1005"
  animName: "AS_CH_Walk_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.52
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1006"
  animName: "AS_CH_Walk_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.18
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1007"
  animName: "AS_CH_Walk_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.52
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1008"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.04
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1009"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.16
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1010"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.27
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1011"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.36
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1012"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.04
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1013"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.16
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1014"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.27
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1015"
  animName: "AS_CH_DashRun_003"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.36
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1016"
  animName: "AS_CH_NiJiang_In_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.73
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1017"
  animName: "AS_CH_NiJiang_In_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 1.5
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1018"
  animName: "AS_CH_NiJiang_In_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.73
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1019"
  animName: "AS_CH_NiJiang_In_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 1.5
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1020"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.15
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1021"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.64
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1022"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 1.22
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1023"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 1.86
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1024"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.15
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1025"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.64
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1026"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 1.22
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1027"
  animName: "AS_CH_Fans_Slow_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 1.86
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1028"
  animName: "AS_CH_Fans_Hurry_Loop_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1029"
  animName: "AS_CH_Fans_Hurry_Loop_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.27
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1030"
  animName: "AS_CH_Fans_Hurry_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1031"
  animName: "AS_CH_Fans_Hurry_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.27
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1032"
  animName: "AS_CH_Conveyor_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.08
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1033"
  animName: "AS_CH_Conveyor_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.24
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1034"
  animName: "AS_CH_Conveyor_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.08
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1035"
  animName: "AS_CH_Conveyor_Loop_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.24
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1036"
  animName: "AS_CH_Run_OG_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1037"
  animName: "AS_CH_Run_OG_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1038"
  animName: "AS_CH_Run_OG_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1039"
  animName: "AS_CH_Run_OG_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1040"
  animName: "AS_CH_Run_OG_002"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1041"
  animName: "AS_CH_Run_OG_002"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1042"
  animName: "AS_CH_Run_OG_002"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.12
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1043"
  animName: "AS_CH_Run_OG_002"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1044"
  animName: "AS_CH_TargetedMove_StepLeft_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.08
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1045"
  animName: "AS_CH_TargetedMove_StepLeft_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1046"
  animName: "AS_CH_TargetedMove_StepLeft_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.08
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1047"
  animName: "AS_CH_TargetedMove_StepLeft_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1048"
  animName: "AS_CH_TargetedMove_StepRight_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.08
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1049"
  animName: "AS_CH_TargetedMove_StepRight_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1050"
  animName: "AS_CH_TargetedMove_StepRight_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.08
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1051"
  animName: "AS_CH_TargetedMove_StepRight_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1052"
  animName: "AS_CH_TargetedMove_StepFront_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.1
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1053"
  animName: "AS_CH_TargetedMove_StepFront_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.37
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1054"
  animName: "AS_CH_TargetedMove_StepFront_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.1
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1055"
  animName: "AS_CH_TargetedMove_StepFront_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.37
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1056"
  animName: "AS_CH_TargetedMove_StepBack_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.1
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1057"
  animName: "AS_CH_TargetedMove_StepBack_001"
  bank: "FTP_Avatar"
  playEvent: "Play_act_CuteA_walk"
  playEvent3p: "Play_act_CuteA_walk_3P"
  delay: 0.37
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1058"
  animName: "AS_CH_TargetedMove_StepBack_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.1
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "1059"
  animName: "AS_CH_TargetedMove_StepBack_001"
  bank: "FTP_Material"
  playEvent: "Play_foley_walk"
  playEvent3p: "Play_foley_walk_3P"
  delay: 0.37
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "4000"
  fxName: "FX_player_fuhuo"
  bank: "PL_Cmn"
  playEvent: "Play_PL_Cmn_Revive"
  playEvent3p: "Play_PL_Cmn_Revive"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "4001"
  fxName: "FX_player_shengji"
  bank: "PL_Cmn"
  playEvent: "Play_PL_Cmn_Checkpoint"
  playEvent3p: "Play_PL_Cmn_Checkpoint"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "4002"
  fxName: "FX_player_Fuhuo_OG_002"
  bank: "PL_Cmn"
  playEvent: "Play_PL_Cmn_Revive"
  playEvent3p: "Play_PL_Cmn_Revive"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "4003"
  fxName: "FX_player_Fuhuo_OG_001"
  bank: "PL_Cmn"
  playEvent: "Play_PL_Cmn_Checkpoint"
  playEvent3p: "Play_PL_Cmn_Checkpoint"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "4004"
  fxName: "PS_DE_EF_IconSave_001_A"
  bank: "PL_Cmn"
  playEvent: "Play_PL_Cmn_Checkpoint"
  playEvent3p: "Play_PL_Cmn_Checkpoint"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "6000"
  fxName: "FX_Prop_Boom_100"
  bank: "PL_Prop"
  playEvent: "Play_Pl_Prop_Explo"
  playEvent3p: "Play_Pl_Prop_Explo"
  delay: 0.1
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "6001"
}
rows {
  id: "7000"
  animName: "AS_CH_Anger_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Anger_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Anger_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Anger_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7001"
  animName: "AS_CH_Attention_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Attention_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Attention_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Attention_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7002"
  animName: "AS_CH_Attention_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Attention_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Attention_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Attention_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7003"
  animName: "AS_CH_Awkward_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Awkward_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Awkward_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Awkward_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7004"
  animName: "AS_CH_Beat_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Beat_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Beat_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Beat_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7005"
  animName: "AS_CH_Beat_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Beat_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Beat_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Beat_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7006"
  animName: "AS_CH_belly_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_belly_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_belly_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_belly_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7007"
  animName: "AS_CH_BigBow_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_BigBow_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_BigBow_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_BigBow_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7008"
  animName: "AS_CH_BigCry_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_BigCry_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_BigCry_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_BigCry_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7009"
  animName: "AS_CH_BigHug_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_BigHug_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_BigHug_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_BigHug_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7010"
  animName: "AS_CH_BigHug_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_BigHug_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_BigHug_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_BigHug_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7011"
  animName: "AS_CH_Birthday_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Birthday_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Birthday_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Birthday_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7012"
  animName: "AS_CH_Birthday_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Birthday_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Birthday_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Birthday_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7013"
  animName: "AS_CH_Bow_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Bow_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Bow_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Bow_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7014"
  animName: "AS_CH_Cake_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Cake_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Cake_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Cake_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7015"
  animName: "AS_CH_Cake_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Cake_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Cake_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Cake_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7016"
  animName: "AS_CH_Celebrate_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Celebrate_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Celebrate_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Celebrate_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7017"
  animName: "AS_CH_Cheer_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Cheer_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Cheer_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Cheer_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7018"
  animName: "AS_CH_Chick_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Chick_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Chick_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Chick_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7019"
  animName: "AS_CH_Clap_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Clap_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Clap_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Clap_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7020"
  animName: "AS_CH_Clap_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Clap_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Clap_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Clap_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7021"
  animName: "AS_CH_ComeOn_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_ComeOn_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_ComeOn_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_ComeOn_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7022"
  animName: "AS_CH_Cry_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Cry_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Cry_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Cry_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7023"
  animName: "AS_CH_Eatmelon_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Eatmelon_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Eatmelon_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Eatmelon_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7024"
  animName: "AS_CH_Fight_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Fight_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Fight_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Fight_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7025"
  animName: "AS_CH_Fight_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Fight_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Fight_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Fight_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7026"
  animName: "AS_CH_Fire_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Fire_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Fire_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Fire_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7027"
  animName: "AS_CH_Fly_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Fly_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Fly_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Fly_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7028"
  animName: "AS_CH_Guitar_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Guitar_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Guitar_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Guitar_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7029"
  animName: "AS_CH_Hammer_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Hammer_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Hammer_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Hammer_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7030"
  animName: "AS_CH_Heart_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Heart_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Heart_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Heart_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7031"
  animName: "AS_CH_Hi_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Hi_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Hi_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Hi_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7032"
  animName: "AS_CH_Hug_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Hug_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Hug_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Hug_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7033"
  animName: "AS_CH_Hug_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Hug_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Hug_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Hug_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7034"
  animName: "AS_CH_Inspiration_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Inspiration_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Inspiration_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Inspiration_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7035"
  animName: "AS_CH_Kiss_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Kiss_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Kiss_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Kiss_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7036"
  animName: "AS_CH_Kiss_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Kiss_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Kiss_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Kiss_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7037"
  animName: "AS_CH_Knowledge_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Knowledge_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Knowledge_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Knowledge_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7038"
  animName: "AS_CH_labber_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_labber_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_labber_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_labber_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7039"
  animName: "AS_CH_labber_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_labber_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_labber_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_labber_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7040"
  animName: "AS_CH_Like_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Like_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Like_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Like_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7041"
  animName: "AS_CH_Love_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Love_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Love_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Love_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7042"
  animName: "AS_CH_Magic_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Magic_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Magic_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Magic_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7043"
  animName: "AS_CH_Pat_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Pat_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Pat_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Pat_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7044"
  animName: "AS_CH_Photograph_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Photograph_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Photograph_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Photograph_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7045"
  animName: "AS_CH_Photograph_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Photograph_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Photograph_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Photograph_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7046"
  animName: "AS_CH_Pleased_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Pleased_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Pleased_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Pleased_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7047"
  animName: "AS_CH_Query_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Query_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Query_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Query_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7048"
  animName: "AS_CH_Rub_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Rub_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Rub_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Rub_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7049"
  animName: "AS_CH_Scoop_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Scoop_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Scoop_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Scoop_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7050"
  animName: "AS_CH_Scoop_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Scoop_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Scoop_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Scoop_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7051"
  animName: "AS_CH_Share_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Share_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Share_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Share_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7052"
  animName: "AS_CH_Share_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Share_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Share_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Share_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7053"
  animName: "AS_CH_Shiver_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Shiver_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Shiver_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Shiver_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7054"
  animName: "AS_CH_ShowTime_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_ShowTime_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_ShowTime_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_ShowTime_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7055"
  animName: "AS_CH_Sit_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Sit_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Sit_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Sit_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7056"
  animName: "AS_CH_Sleep_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Sleep_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Sleep_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Sleep_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7057"
  animName: "AS_CH_Split_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Split_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Split_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Split_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7058"
  animName: "AS_CH_Stomp_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Stomp_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Stomp_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Stomp_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7059"
  animName: "AS_CH_Stomp_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Stomp_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Stomp_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Stomp_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7060"
  animName: "AS_CH_Wave_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Wave_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Wave_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Wave_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7061"
  animName: "AS_CH_Weep_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Weep_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Weep_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Weep_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7062"
  animName: "AS_CH_Zombie_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Zombie_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Zombie_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Zombie_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7063"
  animName: "AS_CH_Swan_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Swan_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Swan_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Swan_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7064"
  animName: "AS_CH_Shake_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Shake_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Shake_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Shake_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7065"
  animName: "AS_CH_Noodle_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Noodle_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Noodle_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Noodle_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7066"
  animName: "AS_CH_Astro2_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Astro2_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Astro2_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Astro2_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7067"
  animName: "AS_CH_Spry_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Spry_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Spry_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Spry_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7068"
  animName: "AS_CH_Biu_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Biu_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Biu_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Biu_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7069"
  animName: "AS_CH_LetMeLook_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_LetMeLook_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_LetMeLook_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_LetMeLook_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7070"
  animName: "AS_CH_Drink_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Drink_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Drink_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Drink_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7071"
  animName: "AS_CH_Donggan_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Donggan_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Donggan_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Donggan_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7072"
  animName: "AS_CH_Astro_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Astro_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Astro_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Astro_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7073"
  animName: "AS_CH_Rabbit_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Rabbit_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Rabbit_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Rabbit_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7074"
  animName: "AS_CH_WeDance_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_WeDance_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_WeDance_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_WeDance_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7075"
  animName: "AS_CH_Celebration_002"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Celebration_002"
  playEvent3p: "Play_Pl_Exp_AS_CH_Celebration_002"
  stopEvent: "Stop_Pl_Exp_AS_CH_Celebration_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7076"
  animName: "AS_CH_Music_Left_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Music_Left_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Music_Left_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Music_Left_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7077"
  animName: "AS_CH_Music_Right_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Music_Right_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Music_Right_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Music_Right_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7078"
  animName: "AS_CH_TapDance_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_TapDance_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_TapDance_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_TapDance_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7079"
  animName: "AS_CH_BDJ_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_BDJ_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_BDJ_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_BDJ_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7080"
  animName: "AS_CH_BDJ_002"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_BDJ_002"
  playEvent3p: "Play_Pl_Exp_AS_CH_BDJ_002"
  stopEvent: "Stop_Pl_Exp_AS_CH_BDJ_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7081"
  animName: "AS_CH_kemusan_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Kemusan_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Kemusan_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Kemusan_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7082"
  animName: "AS_CH_M_JianZiWu_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_M_JianZiWu_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_M_JianZiWu_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_M_JianZiWu_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7083"
  animName: "AS_CH_xiangkuailechufa_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Xiangkuailechufa_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Xiangkuailechufa_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Xiangkuailechufa_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7084"
  animName: "AS_CH_Ultraman_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Ultraman_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Ultraman_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Ultraman_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7085"
  animName: "AS_CH_Ultraman_002"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Ultraman_002"
  playEvent3p: "Play_Pl_Exp_AS_CH_Ultraman_002"
  stopEvent: "Stop_Pl_Exp_AS_CH_Ultraman_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7086"
  animName: "AS_CH_Ultraman_003"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Ultraman_003"
  playEvent3p: "Play_Pl_Exp_AS_CH_Ultraman_003"
  stopEvent: "Stop_Pl_Exp_AS_CH_Ultraman_003"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7087"
  animName: "AS_CH_Ultraman_004"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Ultraman_004"
  playEvent3p: "Play_Pl_Exp_AS_CH_Ultraman_004"
  stopEvent: "Stop_Pl_Exp_AS_CH_Ultraman_004"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7088"
  animName: "AS_CH_Ultraman_005"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Ultraman_005"
  playEvent3p: "Play_Pl_Exp_AS_CH_Ultraman_005"
  stopEvent: "Stop_Pl_Exp_AS_CH_Ultraman_005"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7089"
  animName: "AS_CH_Yangko_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Yangko_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Yangko_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Yangko_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7090"
  animName: "AS_CH_Salute_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Salute_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Salute_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Salute_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7091"
  animName: "AS_CH_LALALAND_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_LALALAND_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_LALALAND_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_LALALAND_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7092"
  animName: "AS_CH_Rooters_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Rooters_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Rooters_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Rooters_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7093"
  animName: "AS_CH_NoNoNo_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_NoNoNo_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_NoNoNo_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_NoNoNo_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7095"
  animName: "AS_CH_Broomfly_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Broomfly_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Broomfly_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Broomfly_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7096"
  animName: "AS_CH_sadshake_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_sadshake_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_sadshake_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_sadshake_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7097"
  animName: "AS_CH_Crotchdance_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Crotchdance_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_Crotchdance_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Crotchdance_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7098"
  animName: "AS_CH_JumpingJack_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_JumpingJack_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_JumpingJack_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_JumpingJack_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7099"
  animName: "AS_CH_FunnyDance_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_FunnyDance_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_FunnyDance_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_FunnyDance_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7100"
  animName: "AS_CH_helpless_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_helpless_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_helpless_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_helpless_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7101"
  animName: "AS_CH_Ultraman_006"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_Ultraman_006"
  playEvent3p: "Play_Pl_Exp_AS_CH_Ultraman_006"
  stopEvent: "Stop_Pl_Exp_AS_CH_Ultraman_006"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7102"
  animName: "AS_CH_Dance_queencard_001 "
  bank: "Pl_Exp_Grp"
  playEvent: "Play_Pl_Exp_Grp_AS_CH_Dance_queencard_001"
  stopEvent: "Stop_Pl_Exp_Grp_AS_CH_Dance_queencard_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7103"
  animName: "AS_CH_Dance_Quanshiai_001"
  bank: "Pl_Exp_Grp"
  playEvent: "Play_Pl_Exp_Grp_AS_CH_Dance_Quanshiai_001"
  stopEvent: "Stop_Pl_Exp_Grp_AS_CH_Dance_Quanshiai_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7104"
  animName: "AS_CH_Dance_Chaonengli_001"
  bank: "Pl_Exp_Grp"
  playEvent: "Play_Pl_Exp_AS_CH_Dance_Chaonengli_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Dance_Chaonengli_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7105"
  animName: "AS_CH_Dance_jianshencao_001"
  bank: "Pl_Exp_Grp"
  playEvent: "Play_Pl_Exp_AS_CH_Dance_jianshencao_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_Dance_jianshencao_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7106"
  animName: "AS_CH_dumbbell_001"
  bank: "PL_Exp_Cmn"
  playEvent: "Play_Pl_Exp_AS_CH_dumbbell_001"
  playEvent3p: "Play_Pl_Exp_AS_CH_dumbbell_001"
  stopEvent: "Stop_Pl_Exp_AS_CH_dumbbell_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7107"
  animName: "AS_CH_Dance_Superjump_001_001"
  bank: "Pl_Exp_Grp"
  playEvent: "Play_Pl_Exp_Grp_AS_CH_Dance_Superjump_001_001"
  stopEvent: "Stop_Pl_Exp_Grp_AS_CH_Dance_Superjump_001_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "7108"
  animName: "AS_CH_Dance_YMxinggiwu_001"
  bank: "Pl_Exp_Grp"
  playEvent: "Play_Pl_Exp_Grp_AS_CH_Dance_YMxinggiwu_001"
  stopEvent: "Stop_Pl_Exp_Grp_AS_CH_Dance_YMxinggiwu_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9000"
  animName: "AS_CH_Enter_PL_001"
  bank: "SFX_Mengmi"
  playEvent: "Play_SFX_Mengmi_Menu_ShowEnter"
  stopEvent: "Stop_SFX_Mengmi_Menu_ShowEnter"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9001"
  animName: "AS_CH_Idleshow_PL_001"
  bank: "SFX_Mengmi"
  playEvent: "Play_SFX_Mengmi_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Mengmi_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9002"
  animName: "AS_CH_Enter_PL_008"
  bank: "SFX_Mingyan"
  playEvent: "Play_SFX_Mingyan_Menu_ShowEnter"
  stopEvent: "Stop_SFX_Mingyan_Menu_ShowEnter"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9003"
  animName: "AS_CH_IdleShow_PL_008"
  bank: "SFX_Mingyan"
  playEvent: "Play_SFX_Mingyan_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Mingyan_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9004"
  animName: "AS_CH_Enter_PL_010"
  bank: "SFX_Mosi"
  playEvent: "Play_SFX_Mosi_Menu_ShowEnter"
  stopEvent: "Stop_SFX_Mosi_Menu_ShowEnter"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9005"
  animName: "AS_CH_IdleShow_PL_010"
  bank: "SFX_Mosi"
  playEvent: "Play_SFX_Mosi_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Mosi_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9007"
  animName: "AS_CH_IdleShow_OG_001"
  bank: "SFX_HuXian"
  playEvent: "Play_SFX_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_SFX_HuXian_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9008"
  animName: "AS_CH_Enter_PL_007"
  bank: "SFX_Xinxiya"
  playEvent: "Play_SFX_Xinxiya_Menu_ShowEnter"
  stopEvent: "Stop_SFX_Xinxiya_Menu_ShowEnter"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9009"
  animName: "AS_CH_IdleShow_PL_007"
  bank: "SFX_Xinxiya"
  playEvent: "Play_SFX_Xinxiya_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Xinxiya_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9011"
  animName: "AS_CH_IdleShow_001_OG_002"
  bank: "SFX_Yemo"
  playEvent: "Play_AS_CH_IdleShow_001_OG_002"
  playEvent3p: "Play_AS_CH_IdleShow_001_OG_002"
  stopEvent: "Stop_AS_CH_IdleShow_001_OG_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9012"
  animName: "AS_CH_IdleShow_002_OG_002"
  bank: "SFX_Yemo"
  playEvent: "Play_AS_CH_IdleShow_002_OG_002"
  playEvent3p: "Play_AS_CH_IdleShow_002_OG_002"
  stopEvent: "Stop_AS_CH_IdleShow_002_OG_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9013"
  animName: "AS_CH_IdleShow_003_OG_002"
  bank: "SFX_Yemo"
  playEvent: "Play_AS_CH_IdleShow_003_OG_002"
  playEvent3p: "Play_AS_CH_IdleShow_003_OG_002"
  stopEvent: "Stop_AS_CH_IdleShow_003_OG_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9014"
  animName: "AS_CH_IdleShow_001_OG_002_HP01"
  bank: "SFX_Yemo"
  playEvent: "Play_AS_CH_IdleShow_001_OG_002"
  playEvent3p: "Play_AS_CH_IdleShow_001_OG_002"
  stopEvent: "Stop_AS_CH_IdleShow_001_OG_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9015"
  animName: "AS_CH_IdleShow_002_OG_002_HP01"
  bank: "SFX_Yemo"
  playEvent: "Play_AS_CH_IdleShow_002_OG_002"
  playEvent3p: "Play_AS_CH_IdleShow_002_OG_002"
  stopEvent: "Stop_AS_CH_IdleShow_002_OG_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9024"
  animName: "AS_CH_Enter_PL_014"
  bank: "SFX_Sangni"
  playEvent: "Play_SFX_AS_CH_Enter_PL_014"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_014"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9025"
  animName: "AS_CH_IdleShow_PL_014"
  bank: "SFX_Sangni"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_014"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_014"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9026"
  animName: "AS_CH_Enter_PL_015"
  bank: "SFX_Baobao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_015"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_015"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9027"
  animName: "AS_CH_IdleShow_PL_015"
  bank: "SFX_Baobao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_015"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_015"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9028"
  animName: "AS_CH_Enter_PL_001"
  bank: "VO_Mengmi"
  playEvent: "Play_VO_Mengmi_Menu_ShowEnter"
  stopEvent: "Stop_VO_Mengmi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9029"
  animName: "AS_CH_Idleshow_PL_001"
  bank: "VO_Mengmi"
  playEvent: "Play_VO_Mengmi_Menu_ShowIdle"
  stopEvent: "Stop_VO_Mengmi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9030"
  animName: "AS_CH_Enter_PL_008"
  bank: "VO_Mingyan"
  playEvent: "Play_VO_Mingyan_Menu_ShowEnter"
  stopEvent: "Stop_VO_Mingyan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9031"
  animName: "AS_CH_IdleShow_PL_008"
  bank: "VO_Mingyan"
  playEvent: "Play_VO_Mingyan_Menu_ShowIdle"
  stopEvent: "Stop_VO_Mingyan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9032"
  animName: "AS_CH_Enter_PL_010"
  bank: "VO_Mosi"
  playEvent: "Play_VO_Mosi_Menu_ShowEnter"
  stopEvent: "Stop_VO_Mosi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9033"
  animName: "AS_CH_IdleShow_PL_010"
  bank: "VO_Mosi"
  playEvent: "Play_VO_Mosi_Menu_ShowIdle"
  stopEvent: "Stop_VO_Mosi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9034"
  animName: "AS_CH_IdleShow_OG_001"
  bank: "VO_HuXian"
  playEvent: "Play_VO_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_VO_HuXian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9035"
  animName: "AS_CH_Enter_PL_007"
  bank: "VO_Xinxiya"
  playEvent: "Play_VO_Xinxiya_Menu_ShowEnter"
  stopEvent: "Stop_VO_Xinxiya"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9036"
  animName: "AS_CH_IdleShow_PL_007"
  bank: "VO_Xinxiya"
  playEvent: "Play_VO_Xinxiya_Menu_ShowIdle"
  stopEvent: "Stop_VO_Xinxiya"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9037"
  animName: "AS_CH_Enter_PL_012"
  bank: "VO_Baola"
  playEvent: "Play_VO_Baola_Menu_ShowEnter"
  stopEvent: "Stop_VO_Baola"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9038"
  animName: "AS_CH_IdleShow_PL_012"
  bank: "VO_Baola"
  playEvent: "Play_VO_Baola_Menu_ShowIdle"
  stopEvent: "Stop_VO_Baola"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9039"
  animName: "AS_CH_Enter_PL_011"
  bank: "VO_Nike"
  playEvent: "Play_VO_AS_CH_Enter_PL_011"
  stopEvent: "Stop_VO_Nike"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9040"
  animName: "AS_CH_IdleShow_PL_011"
  bank: "VO_Nike"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_011"
  stopEvent: "Stop_VO_Nike"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9041"
  animName: "AS_CH_Enter_PL_014"
  bank: "VO_Sangni"
  playEvent: "Play_VO_AS_CH_Enter_PL_014"
  stopEvent: "Stop_VO_Sangni"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9042"
  animName: "AS_CH_IdleShow_PL_014"
  bank: "VO_Sangni"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_014"
  stopEvent: "Stop_VO_Sangni"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9043"
  animName: "AS_CH_Enter_PL_015"
  bank: "VO_Baobao"
  playEvent: "Play_VO_AS_CH_Enter_PL_015"
  stopEvent: "Stop_VO_Baobao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9044"
  animName: "AS_CH_IdleShow_PL_015"
  bank: "VO_Baobao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_015"
  stopEvent: "Stop_VO_Baobao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9045"
  animName: "AS_CH_Enter_PL_017"
  bank: "SFX_Taoyao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_017"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_017"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9046"
  animName: "AS_CH_Enter_PL_017"
  bank: "VO_Taoyao"
  playEvent: "Play_VO_AS_CH_Enter_PL_017"
  stopEvent: "Stop_VO_Taoyao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9047"
  animName: "AS_CH_IdleShow_PL_017"
  bank: "SFX_Taoyao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_017"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_017"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9048"
  animName: "AS_CH_IdleShow_PL_017"
  bank: "VO_Taoyao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_017"
  stopEvent: "Stop_VO_Taoyao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9049"
  animName: "AS_CH_Enter_PL_016"
  bank: "SFX_Liqi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_016"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_016"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9050"
  animName: "AS_CH_Enter_PL_016"
  bank: "VO_Liqi"
  playEvent: "Play_VO_AS_CH_Enter_PL_016"
  stopEvent: "Stop_VO_Liqi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9051"
  animName: "AS_CH_IdleShow_PL_016"
  bank: "SFX_Liqi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_016"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_016"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9052"
  animName: "AS_CH_IdleShow_PL_016"
  bank: "VO_Liqi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_016"
  stopEvent: "Stop_VO_Liqi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9053"
  animName: "AS_CH_IdleShow_OG_001"
  bank: "VO_HuXian"
  playEvent: "Play_VO_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_VO_HuXian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9054"
  animName: "AS_CH_IdleShow_OG_001_HP01"
  bank: "VO_HuXian"
  playEvent: "Play_VO_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_VO_HuXian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9055"
  animName: "AS_CH_IdleShow_OG_001_HP02"
  bank: "VO_HuXian"
  playEvent: "Play_VO_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_VO_HuXian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9056"
  animName: "AS_CH_IdleShow_OG_001_HP03"
  bank: "VO_HujianxianFire"
  playEvent: "Play_VO_HujianxianFire_Menu_ShowIdle"
  stopEvent: "Stop_VO_HujianxianFire"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9057"
  animName: "AS_CH_IdleShow_OG_001"
  bank: "SFX_HuXian"
  playEvent: "Play_SFX_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_SFX_HuXian_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9058"
  animName: "AS_CH_IdleShow_OG_001_HP01"
  bank: "SFX_HuXian"
  playEvent: "Play_SFX_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_SFX_HuXian_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9059"
  animName: "AS_CH_IdleShow_OG_001_HP02"
  bank: "SFX_HuXian"
  playEvent: "Play_SFX_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_SFX_HuXian_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9060"
  animName: "AS_CH_IdleShow_OG_001_HP03"
  bank: "SFX_HuXian"
  playEvent: "Play_SFX_HuXian_Menu_ShowIdle"
  stopEvent: "Stop_SFX_HuXian_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9061"
  animName: "AS_CH_Enter_PL_018"
  bank: "SFX_Leqi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_018"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_018"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9062"
  animName: "AS_CH_Enter_PL_018"
  bank: "VO_Leqi"
  playEvent: "Play_VO_AS_CH_Enter_PL_018"
  stopEvent: "Stop_VO_Leqi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9063"
  animName: "AS_CH_IdleShow_PL_018"
  bank: "SFX_Leqi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_018"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_018"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9064"
  animName: "AS_CH_IdleShow_PL_018"
  bank: "VO_Leqi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_018"
  stopEvent: "Stop_VO_Leqi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9065"
  animName: "AS_CH_Enter_PL_028"
  bank: "SFX_Gelei"
  playEvent: "Play_SFX_AS_CH_Enter_PL_028"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_028"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9066"
  animName: "AS_CH_Enter_PL_028"
  bank: "VO_Gelei"
  playEvent: "Play_VO_AS_CH_Enter_PL_028"
  stopEvent: "Stop_VO_Gelei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9067"
  animName: "AS_CH_IdleShow_PL_028"
  bank: "SFX_Gelei"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_028"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_028"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9068"
  animName: "AS_CH_IdleShow_PL_028"
  bank: "VO_Gelei"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_028"
  stopEvent: "Stop_VO_Gelei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9069"
  animName: "AS_CH_Enter_PL_029"
  bank: "SFX_Xiaoman"
  playEvent: "Play_SFX_AS_CH_Enter_PL_029"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_029"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9070"
  animName: "AS_CH_Enter_PL_029"
  bank: "VO_Xiaoman"
  playEvent: "Play_VO_AS_CH_Enter_PL_029"
  stopEvent: "Stop_VO_Xiaoman"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9071"
  animName: "AS_CH_IdleShow_PL_029"
  bank: "SFX_Xiaoman"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_029"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_029"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9072"
  animName: "AS_CH_IdleShow_PL_029"
  bank: "VO_Xiaoman"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_029"
  stopEvent: "Stop_VO_Xiaoman"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9073"
  animName: "AS_CH_Enter_PL_030"
  bank: "SFX_Hailunna"
  playEvent: "Play_SFX_AS_CH_Enter_PL_030"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_030"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9074"
  animName: "AS_CH_Enter_PL_030"
  bank: "VO_Hailunna"
  playEvent: "Play_VO_AS_CH_Enter_PL_030"
  stopEvent: "Stop_VO_Hailunna"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9075"
  animName: "AS_CH_IdleShow_PL_030"
  bank: "SFX_Hailunna"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_030"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_030"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9076"
  animName: "AS_CH_IdleShow_PL_030"
  bank: "VO_Hailunna"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_030"
  stopEvent: "Stop_VO_Hailunna"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9077"
  animName: "AS_CH_Enter_PL_031"
  bank: "SFX_Xiaohongmao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_031"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_031"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9078"
  animName: "AS_CH_Enter_PL_031"
  bank: "VO_Xiaohongmao"
  playEvent: "Play_VO_AS_CH_Enter_PL_031"
  stopEvent: "Stop_VO_Xiaohongmao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9079"
  animName: "AS_CH_IdleShow_PL_031"
  bank: "SFX_Xiaohongmao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_031"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_031"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9080"
  animName: "AS_CH_IdleShow_PL_031"
  bank: "VO_Xiaohongmao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_031"
  stopEvent: "Stop_VO_Xiaohongmao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9081"
  animName: "AS_CH_Enter_PL_020"
  bank: "SFX_XiaoxinSuperMan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_020"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_020"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9082"
  animName: "AS_CH_Enter_PL_020"
  bank: "VO_Xiaoxin"
  playEvent: "Play_VO_AS_CH_Enter_PL_020"
  stopEvent: "Stop_VO_XiaoxinSuperMan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9083"
  animName: "AS_CH_IdleShow_PL_020"
  bank: "SFX_XiaoxinSuperMan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_020"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_020"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9084"
  animName: "AS_CH_IdleShow_PL_020"
  bank: "VO_Xiaoxin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_020"
  stopEvent: "Stop_VO_XiaoxinSuperMan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9085"
  animName: "AS_CH_Enter_PL_021"
  bank: "SFX_XiaoxinLobster"
  playEvent: "Play_SFX_AS_CH_Enter_PL_021"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_021"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9086"
  animName: "AS_CH_Enter_PL_021"
  bank: "VO_Xiaoxin"
  playEvent: "Play_VO_AS_CH_Enter_PL_021"
  stopEvent: "Stop_VO_XiaoxinLobster"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9087"
  animName: "AS_CH_IdleShow_PL_021"
  bank: "SFX_XiaoxinLobster"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_021"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_021"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9088"
  animName: "AS_CH_IdleShow_PL_021"
  bank: "VO_Xiaoxin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_021"
  stopEvent: "Stop_VO_XiaoxinLobster"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9089"
  animName: "AS_CH_IdleShow_OG_002"
  bank: "SFX_Yemo"
  playEvent: "Play_SFX_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Yemo_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9090"
  animName: "AS_CH_IdleShow_OG_002_HP01"
  bank: "SFX_Yemo"
  playEvent: "Play_SFX_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Yemo_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9091"
  animName: "AS_CH_IdleShow_OG_002_HP02"
  bank: "SFX_Yemo"
  playEvent: "Play_SFX_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Yemo_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9092"
  animName: "AS_CH_IdleShow_OG_002_PV"
  bank: "SFX_Yemo"
  playEvent: "Play_SFX_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Yemo_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9093"
  animName: "AS_CH_IdleShow_OG_002_PV_HP01"
  bank: "SFX_Yemo"
  playEvent: "Play_SFX_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Yemo_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9094"
  animName: "AS_CH_IdleShow_OG_002_PV_HP02"
  bank: "SFX_Yemo"
  playEvent: "Play_SFX_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_SFX_Yemo_Menu_ShowIdle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9095"
  animName: "AS_CH_IdleShow_OG_002"
  bank: "VO_Yemo"
  playEvent: "Play_VO_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_VO_Yemo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9096"
  animName: "AS_CH_IdleShow_OG_002_HP01"
  bank: "VO_Yemo"
  playEvent: "Play_VO_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_VO_Yemo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9097"
  animName: "AS_CH_IdleShow_OG_002_HP02"
  bank: "VO_Yemo"
  playEvent: "Play_VO_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_VO_Yemo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9098"
  animName: "AS_CH_IdleShow_OG_002_PV"
  bank: "VO_Yemo"
  playEvent: "Play_VO_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_VO_Yemo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9099"
  animName: "AS_CH_IdleShow_OG_002_PV_HP01"
  bank: "VO_Yemo"
  playEvent: "Play_VO_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_VO_Yemo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9100"
  animName: "AS_CH_IdleShow_OG_002_PV_HP02"
  bank: "VO_Yemo"
  playEvent: "Play_VO_Yemo_Menu_ShowIdle"
  stopEvent: "Stop_VO_Yemo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9101"
  animName: "AS_CH_IdleShow_OG_003"
  bank: "SFX_Lucine"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_003"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_003"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9102"
  animName: "AS_CH_IdleShow_OG_003_HP01"
  bank: "SFX_Lucine"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_003"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_003"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9103"
  animName: "AS_CH_IdleShow_OG_003_HP02"
  bank: "SFX_Lucine"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_003"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_003"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9104"
  animName: "AS_CH_IdleShow_OG_003"
  bank: "VO_Lucine"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_003"
  stopEvent: "Stop_VO_Lucine"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9105"
  animName: "AS_CH_IdleShow_OG_003_HP01"
  bank: "VO_Lucine"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_003"
  stopEvent: "Stop_VO_Lucine"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9106"
  animName: "AS_CH_IdleShow_OG_003_HP02"
  bank: "VO_Lucine"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_003"
  stopEvent: "Stop_VO_Lucine"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9107"
  animName: "AS_CH_Enter_PL_019"
  bank: "SFX_Astro"
  playEvent: "Play_SFX_AS_CH_Enter_PL_019"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_019"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9108"
  animName: "AS_CH_IdleShow_PL_019"
  bank: "SFX_Astro"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_019"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_019"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9109"
  animName: "AS_CH_Enter_PL_019"
  bank: "VO_Astro"
  playEvent: "Play_VO_AS_CH_Enter_PL_019"
  stopEvent: "Stop_VO_Astro"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9110"
  animName: "AS_CH_IdleShow_PL_019"
  bank: "VO_Astro"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_019"
  stopEvent: "Stop_VO_Astro"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9111"
  animName: "AS_CH_Enter_PL_025"
  bank: "SFX_Rosabella"
  playEvent: "Play_SFX_AS_CH_Enter_PL_025"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_025"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9112"
  animName: "AS_CH_IdleShow_PL_025"
  bank: "SFX_Rosabella"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_025"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_025"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9113"
  animName: "AS_CH_Enter_PL_025"
  bank: "VO_Rosabella"
  playEvent: "Play_VO_AS_CH_Enter_PL_025"
  stopEvent: "Stop_VO_Rosabella"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9114"
  animName: "AS_CH_IdleShow_PL_025"
  bank: "VO_Rosabella"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_025"
  stopEvent: "Stop_VO_Rosabella"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9115"
  animName: "AS_CH_Enter_PL_026"
  bank: "SFX_Angle"
  playEvent: "Play_SFX_AS_CH_Enter_PL_026"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_026"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9116"
  animName: "AS_CH_IdleShow_PL_026"
  bank: "SFX_Angle"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_026"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_026"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9117"
  animName: "AS_CH_Enter_PL_026"
  bank: "VO_Angle"
  playEvent: "Play_VO_AS_CH_Enter_PL_026"
  stopEvent: "Stop_VO_Angle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9118"
  animName: "AS_CH_IdleShow_PL_026"
  bank: "VO_Angle"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_026"
  stopEvent: "Stop_VO_Angle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9119"
  animName: "AS_CH_Enter_PL_032"
  bank: "SFX_Xiaoai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_032"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_032"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9120"
  animName: "AS_CH_IdleShow_PL_032"
  bank: "SFX_Xiaoai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_032"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_032"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9121"
  animName: "AS_CH_Enter_PL_032"
  bank: "VO_Xiaoai"
  playEvent: "Play_VO_AS_CH_Enter_PL_032"
  stopEvent: "Stop_VO_Xiaoai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9122"
  animName: "AS_CH_IdleShow_PL_032"
  bank: "VO_Xiaoai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_032"
  stopEvent: "Stop_VO_Xiaoai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9123"
  animName: "AS_CH_Enter_PL_033"
  bank: "SFX_Xiaoxin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_033"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_033"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9124"
  animName: "AS_CH_IdleShow_PL_033"
  bank: "SFX_Xiaoxin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_033"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_033"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9125"
  animName: "AS_CH_Enter_PL_033"
  bank: "VO_Xiaoxin"
  playEvent: "Play_VO_AS_CH_Enter_PL_033"
  stopEvent: "Stop_VO_Xiaoxin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9126"
  animName: "AS_CH_IdleShow_PL_033"
  bank: "VO_Xiaoxin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_033"
  stopEvent: "Stop_VO_Xiaoxin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9127"
  animName: "AS_CH_Enter_PL_034"
  bank: "SFX_Wulan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_034"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_034"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9128"
  animName: "AS_CH_IdleShow_PL_034"
  bank: "SFX_Wulan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_034"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_034"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9129"
  animName: "AS_CH_Enter_PL_034"
  bank: "VO_Wulan"
  playEvent: "Play_VO_AS_CH_Enter_PL_034"
  stopEvent: "Stop_VO_Wulan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9130"
  animName: "AS_CH_IdleShow_PL_034"
  bank: "VO_Wulan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_034"
  stopEvent: "Stop_VO_Wulan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9131"
  animName: "AS_CH_IdleShow_001_OG_001"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_001_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_001_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_001_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9132"
  animName: "AS_CH_IdleShow_002_OG_001"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_002_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_002_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_002_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9133"
  animName: "AS_CH_IdleShow_003_OG_001"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_003_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_003_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_003_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9134"
  animName: "AS_CH_IdleShow_001_OG_001_HP01"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_001_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_001_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_001_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9135"
  animName: "AS_CH_IdleShow_002_OG_001_HP01"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_002_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_002_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_002_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9136"
  animName: "AS_CH_IdleShow_003_OG_001_HP01"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_003_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_003_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_003_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9137"
  animName: "AS_CH_IdleShow_001_OG_001_HP02"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_001_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_001_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_001_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9138"
  animName: "AS_CH_IdleShow_002_OG_001_HP02"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_002_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_002_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_002_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9139"
  animName: "AS_CH_IdleShow_003_OG_001_HP02"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_003_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_003_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_003_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9140"
  animName: "AS_CH_IdleShow_001_OG_001_HP03"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_001_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_001_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_001_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9141"
  animName: "AS_CH_IdleShow_002_OG_001_HP03"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_002_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_002_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_002_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9142"
  animName: "AS_CH_IdleShow_003_OG_001_HP03"
  bank: "SFX_HuXian"
  playEvent: "Play_AS_CH_IdleShow_003_OG_001"
  playEvent3p: "Play_AS_CH_IdleShow_003_OG_001"
  stopEvent: "Stop_AS_CH_IdleShow_003_OG_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9143"
  animName: "AS_CH_Enter_PL_022"
  bank: "SFX_LubanXK"
  playEvent: "Play_SFX_AS_CH_Enter_PL_022"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_022"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9144"
  animName: "AS_CH_IdleShow_PL_022"
  bank: "SFX_LubanXK"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_022"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_022"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9145"
  animName: "AS_CH_Enter_PL_022"
  bank: "VO_LubanXK"
  playEvent: "Play_VO_AS_CH_Enter_PL_022"
  stopEvent: "Stop_VO_LubanXK"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9146"
  animName: "AS_CH_IdleShow_PL_022"
  bank: "VO_LubanXK"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_022"
  stopEvent: "Stop_VO_LubanXK"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9147"
  animName: "AS_CH_Enter_PL_023"
  bank: "SFX_YaoQY"
  playEvent: "Play_SFX_AS_CH_Enter_PL_023"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_023"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9148"
  animName: "AS_CH_IdleShow_PL_023"
  bank: "SFX_YaoQY"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_023"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_023"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9149"
  animName: "AS_CH_Enter_PL_023"
  bank: "VO_YaoQY"
  playEvent: "Play_VO_AS_CH_Enter_PL_023"
  stopEvent: "Stop_VO_YaoQY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9150"
  animName: "AS_CH_IdleShow_PL_023"
  bank: "VO_YaoQY"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_023"
  stopEvent: "Stop_VO_YaoQY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9151"
  animName: "AS_CH_Enter_PL_024"
  bank: "SFX_XiaoqiaoSH"
  playEvent: "Play_SFX_AS_CH_Enter_PL_024"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_024"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9152"
  animName: "AS_CH_IdleShow_PL_024"
  bank: "SFX_XiaoqiaoSH"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_024"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_024"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9153"
  animName: "AS_CH_Enter_PL_024"
  bank: "VO_XiaoqiaoSH"
  playEvent: "Play_VO_AS_CH_Enter_PL_024"
  stopEvent: "Stop_VO_XiaoqiaoSH"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9154"
  animName: "AS_CH_IdleShow_PL_024"
  bank: "VO_XiaoqiaoSH"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_024"
  stopEvent: "Stop_VO_XiaoqiaoSH"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9155"
  animName: "AS_CH_Enter_PL_038"
  bank: "SFX_WhiteMan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_038"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_038"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9156"
  animName: "AS_CH_IdleShow_PL_038"
  bank: "SFX_WhiteMan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_038"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_038"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9157"
  animName: "AS_CH_Enter_PL_046"
  bank: "SFX_Toby"
  playEvent: "Play_SFX_AS_CH_Enter_PL_046"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_046"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9158"
  animName: "AS_CH_IdleShow_PL_046"
  bank: "SFX_Toby"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_046"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_046"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9159"
  animName: "AS_CH_Enter_PL_046"
  bank: "VO_Toby"
  playEvent: "Play_VO_AS_CH_Enter_PL_046"
  stopEvent: "Stop_VO_Toby"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9160"
  animName: "AS_CH_IdleShow_PL_046"
  bank: "VO_Toby"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_046"
  stopEvent: "Stop_VO_Toby"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9161"
  animName: "AS_CH_Enter_PL_035"
  bank: "SFX_Jinli"
  playEvent: "Play_SFX_AS_CH_Enter_PL_035"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_035"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9162"
  animName: "AS_CH_Enter_PL_036"
  bank: "SFX_Dijia"
  playEvent: "Play_SFX_AS_CH_Enter_PL_036"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_036"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9163"
  animName: "AS_CH_Enter_PL_037"
  bank: "SFX_Hachi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_037"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_037"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9164"
  animName: "AS_CH_Enter_PL_041"
  bank: "SFX_Tiger"
  playEvent: "Play_SFX_AS_CH_Enter_PL_041"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_041"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9165"
  animName: "AS_CH_Enter_PL_042"
  bank: "SFX_Kororo"
  playEvent: "Play_SFX_AS_CH_Enter_PL_042"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_042"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9166"
  animName: "AS_CH_Enter_PL_044"
  bank: "SFX_Snowby"
  playEvent: "Play_SFX_AS_CH_Enter_PL_044"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_044"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9167"
  animName: "AS_CH_Enter_PL_045"
  bank: "SFX_Drago"
  playEvent: "Play_SFX_AS_CH_Enter_PL_045"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_045"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9168"
  animName: "AS_CH_Enter_PL_047"
  bank: "SFX_Omi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_047"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_047"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9169"
  animName: "AS_CH_IdleShow_OG_005"
  bank: "SFX_Luoling"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_005"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_005"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9170"
  animName: "AS_CH_IdleShow_PL_035"
  bank: "SFX_Jinli"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_035"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_035"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9171"
  animName: "AS_CH_IdleShow_PL_036"
  bank: "SFX_Dijia"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_036"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_036"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9172"
  animName: "AS_CH_IdleShow_PL_037"
  bank: "SFX_Hachi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_037"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_037"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9173"
  animName: "AS_CH_IdleShow_PL_041"
  bank: "SFX_Tiger"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_041"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_041"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9174"
  animName: "AS_CH_IdleShow_PL_042"
  bank: "SFX_Kororo"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_042"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_042"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9175"
  animName: "AS_CH_IdleShow_PL_044"
  bank: "SFX_Snowby"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_044"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_044"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9176"
  animName: "AS_CH_IdleShow_PL_045"
  bank: "SFX_Drago"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_045"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_045"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9177"
  animName: "AS_CH_IdleShow_PL_047"
  bank: "SFX_Omi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_047"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_047"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9178"
  animName: "AS_CH_Enter_PL_035"
  bank: "VO_Jinli"
  playEvent: "Play_VO_AS_CH_Enter_PL_035"
  stopEvent: "Stop_VO_Jinli"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9179"
  animName: "AS_CH_Enter_PL_036"
  bank: "VO_Dijia"
  playEvent: "Play_VO_AS_CH_Enter_PL_036"
  stopEvent: "Stop_VO_Dijia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9180"
  animName: "AS_CH_Enter_PL_037"
  bank: "VO_Hachi"
  playEvent: "Play_VO_AS_CH_Enter_PL_037"
  stopEvent: "Stop_VO_Hachi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9181"
  animName: "AS_CH_Enter_PL_041"
  bank: "VO_Tiger"
  playEvent: "Play_VO_AS_CH_Enter_PL_041"
  stopEvent: "Stop_VO_Tiger"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9182"
  animName: "AS_CH_Enter_PL_042"
  bank: "VO_Kororo"
  playEvent: "Play_VO_AS_CH_Enter_PL_042"
  stopEvent: "Stop_VO_Kororo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9183"
  animName: "AS_CH_Enter_PL_044"
  bank: "VO_Snowby"
  playEvent: "Play_VO_AS_CH_Enter_PL_044"
  stopEvent: "Stop_VO_Snowby"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9184"
  animName: "AS_CH_Enter_PL_045"
  bank: "VO_Drago"
  playEvent: "Play_VO_AS_CH_Enter_PL_045"
  stopEvent: "Stop_VO_Drago"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9185"
  animName: "AS_CH_Enter_PL_047"
  bank: "VO_Omi"
  playEvent: "Play_VO_AS_CH_Enter_PL_047"
  stopEvent: "Stop_VO_Omi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9186"
  animName: "AS_CH_IdleShow_OG_005"
  bank: "VO_Luoling"
  playEvent: "Play_VO_Luoling_Menu_ShowIdle"
  stopEvent: "Stop_VO_Luoling"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9187"
  animName: "AS_CH_IdleShow_PL_035"
  bank: "VO_Jinli"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_035"
  stopEvent: "Stop_VO_Jinli"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9188"
  animName: "AS_CH_IdleShow_PL_036"
  bank: "VO_Dijia"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_036"
  stopEvent: "Stop_VO_Dijia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9189"
  animName: "AS_CH_IdleShow_PL_037"
  bank: "VO_Hachi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_037"
  stopEvent: "Stop_VO_Hachi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9190"
  animName: "AS_CH_IdleShow_PL_041"
  bank: "VO_Tiger"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_041"
  stopEvent: "Stop_VO_Tiger"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9191"
  animName: "AS_CH_IdleShow_PL_042"
  bank: "VO_Kororo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_042"
  stopEvent: "Stop_VO_Kororo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9192"
  animName: "AS_CH_IdleShow_PL_044"
  bank: "VO_Snowby"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_044"
  stopEvent: "Stop_VO_Snowby"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9193"
  animName: "AS_CH_IdleShow_PL_045"
  bank: "VO_Drago"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_045"
  stopEvent: "Stop_VO_Drago"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9194"
  animName: "AS_CH_IdleShow_PL_047"
  bank: "VO_Omi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_047"
  stopEvent: "Stop_VO_Omi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9195"
  animName: "AS_CH_Enter_PL_049"
  bank: "SFX_Zeta"
  playEvent: "Play_SFX_AS_CH_Enter_PL_049"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_049"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9196"
  animName: "AS_CH_Enter_PL_050"
  bank: "SFX_Nailong"
  playEvent: "Play_SFX_AS_CH_Enter_PL_050"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_050"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9197"
  animName: "AS_CH_Enter_PL_051"
  bank: "SFX_Baobaolong"
  playEvent: "Play_SFX_AS_CH_Enter_PL_051"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_051"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9198"
  animName: "AS_CH_IdleShow_PL_049"
  bank: "SFX_Zeta"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_049"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_049"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9199"
  animName: "AS_CH_IdleShow_PL_050"
  bank: "SFX_Nailong"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_050"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_050"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9200"
  animName: "AS_CH_IdleShow_PL_051"
  bank: "SFX_Baobaolong"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_051"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_051"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9201"
  animName: "AS_CH_Enter_PL_049"
  bank: "VO_Zeta"
  playEvent: "Play_VO_AS_CH_Enter_PL_049"
  stopEvent: "Stop_VO_Zeta"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9202"
  animName: "AS_CH_IdleShow_PL_049"
  bank: "VO_Zeta"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_049"
  stopEvent: "Stop_VO_Zeta"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9203"
  animName: "AS_CH_Enter_PL_050"
  bank: "VO_Nailong"
  playEvent: "Play_VO_AS_CH_Enter_PL_050"
  stopEvent: "Stop_VO_Nailong"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9204"
  animName: "AS_CH_IdleShow_PL_050"
  bank: "VO_Nailong"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_050"
  stopEvent: "Stop_VO_Nailong"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9205"
  animName: "AS_CH_Enter_PL_051"
  bank: "VO_Baobaolong"
  playEvent: "Play_VO_AS_CH_Enter_PL_051"
  stopEvent: "Stop_VO_Baobaolong"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9206"
  animName: "AS_CH_IdleShow_PL_051"
  bank: "VO_Baobaolong"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_051"
  stopEvent: "Stop_VO_Baobaolong"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9210"
  animName: "AS_CH_IdleShow_PL_048"
  bank: "SFX_Andre"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_048"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_048"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9211"
  animName: "AS_CH_Enter_PL_039"
  bank: "VO_Sailuo"
  playEvent: "Play_VO_AS_CH_Enter_PL_039"
  stopEvent: "Stop_VO_Sailuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9212"
  animName: "AS_CH_Enter_PL_040"
  bank: "VO_Jiedun"
  playEvent: "Play_VO_AS_CH_Enter_PL_040"
  stopEvent: "Stop_VO_Jiedun"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9213"
  animName: "AS_CH_Enter_PL_048"
  bank: "VO_Andre"
  playEvent: "Play_VO_AS_CH_Enter_PL_048"
  stopEvent: "Stop_VO_Andre"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9214"
  animName: "AS_CH_IdleShow_PL_039"
  bank: "VO_Sailuo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_039"
  stopEvent: "Stop_VO_Sailuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9215"
  animName: "AS_CH_IdleShow_PL_040"
  bank: "VO_Jiedun"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_040"
  stopEvent: "Stop_VO_Jiedun"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9216"
  animName: "AS_CH_IdleShow_PL_048"
  bank: "VO_Andre"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_048"
  stopEvent: "Stop_VO_Andre"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9217"
  animName: "AS_CH_Enter_PL_043"
  bank: "SFX_KungfuPanda"
  playEvent: "Play_SFX_AS_CH_Enter_PL_043"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_043"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9218"
  animName: "AS_CH_IdleShow_PL_043"
  bank: "SFX_KungfuPanda"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_043"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_043"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9219"
  animName: "AS_CH_Enter_PL_043"
  bank: "VO_KungfuPanda"
  playEvent: "Play_VO_AS_CH_Enter_PL_043"
  stopEvent: "Stop_VO_KungfuPanda"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9220"
  animName: "AS_CH_IdleShow_PL_043"
  bank: "VO_KungfuPanda"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_043"
  stopEvent: "Stop_VO_KungfuPanda"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9221"
  animName: "AS_CH_Idle_OG_007"
  bank: "VO_Eunoia"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_VO_Eunoia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9222"
  animName: "AS_CH_Idle_OG_008"
  bank: "VO_EunoiaExtra"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_008"
  stopEvent: "Stop_VO_EunoiaExtra"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9223"
  animName: "AS_CH_Enter_PL_053"
  bank: "VO_Hyman"
  playEvent: "Play_VO_AS_CH_Enter_PL_053"
  stopEvent: "Stop_VO_Hyman"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9224"
  animName: "AS_CH_IdleShow_PL_053"
  bank: "VO_Hyman"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_053"
  stopEvent: "Stop_VO_Hyman"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9225"
  animName: "AS_CH_Enter_PL_052"
  bank: "VO_Moros"
  playEvent: "Play_VO_AS_CH_Enter_PL_052"
  stopEvent: "Stop_VO_Moros"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9226"
  animName: "AS_CH_IdleShow_PL_052"
  bank: "VO_Moros"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_052"
  stopEvent: "Stop_VO_Moros"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9227"
  animName: "AS_CH_Enter_PL_057"
  bank: "VO_Fireray"
  playEvent: "Play_VO_AS_CH_Enter_PL_057"
  stopEvent: "Stop_VO_Fireray"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9228"
  animName: "AS_CH_IdleShow_PL_057"
  bank: "VO_Fireray"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_057"
  stopEvent: "Stop_VO_Fireray"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9229"
  animName: "AS_CH_Enter_PL_072"
  bank: "VO_Master"
  playEvent: "Play_VO_AS_CH_Enter_PL_072"
  stopEvent: "Stop_VO_Master"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9230"
  animName: "AS_CH_IdleShow_PL_072"
  bank: "VO_Master"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_072"
  stopEvent: "Stop_VO_Master"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9231"
  animName: "AS_CH_Enter_PL_063"
  bank: "VO_Zhen"
  playEvent: "Play_VO_AS_CH_Enter_PL_063"
  stopEvent: "Stop_VO_Zhen"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9232"
  animName: "AS_CH_IdleShow_PL_063"
  bank: "VO_Zhen"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_063"
  stopEvent: "Stop_VO_Zhen"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9233"
  animName: "AS_CH_Enter_PL_067"
  bank: "VO_Lianouhu"
  playEvent: "Play_VO_AS_CH_Enter_PL_067"
  stopEvent: "Stop_VO_Lianouhu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9234"
  animName: "AS_CH_IdleShow_PL_067"
  bank: "VO_Lianouhu"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_067"
  stopEvent: "Stop_VO_Lianouhu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9235"
  animName: "AS_CH_Enter_PL_069"
  bank: "VO_SoldierShandian"
  playEvent: "Play_VO_AS_CH_Enter_PL_069"
  stopEvent: "Stop_VO_SoldierShandian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9236"
  animName: "AS_CH_IdleShow_PL_069"
  bank: "VO_SoldierShandian"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_069"
  stopEvent: "Stop_VO_SoldierShandian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9237"
  animName: "AS_CH_Enter_PL_070"
  bank: "VO_SoldierTiebi"
  playEvent: "Play_VO_AS_CH_Enter_PL_070"
  stopEvent: "Stop_VO_SoldierTiebi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9238"
  animName: "AS_CH_IdleShow_PL_070"
  bank: "VO_SoldierTiebi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_070"
  stopEvent: "Stop_VO_SoldierTiebi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9239"
  animName: "AS_CH_Enter_PL_059"
  bank: "VO_FeiChai"
  playEvent: "Play_VO_AS_CH_Enter_PL_059"
  stopEvent: "Stop_VO_FeiChai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9240"
  animName: "AS_CH_IdleShow_PL_059"
  bank: "VO_FeiChai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_059"
  stopEvent: "Stop_VO_FeiChai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9241"
  animName: "AS_CH_Enter_PL_071"
  bank: "VO_TuXingXing"
  playEvent: "Play_VO_AS_CH_Enter_PL_071"
  stopEvent: "Stop_VO_TuXingXing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9242"
  animName: "AS_CH_IdleShow_PL_071"
  bank: "VO_TuXingXing"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_071"
  stopEvent: "Stop_VO_TuXingXing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9243"
  animName: "AS_CH_Idle_OG_007"
  bank: "SFX_Eunoia"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_007"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9244"
  animName: "AS_CH_Idle_OG_008"
  bank: "SFX_EunoiaExtra"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_008"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_008"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9245"
  animName: "AS_CH_Enter_PL_053"
  bank: "SFX_Hyman"
  playEvent: "Play_SFX_AS_CH_Enter_PL_053"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_053"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9246"
  animName: "AS_CH_IdleShow_PL_053"
  bank: "SFX_Hyman"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_053"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_053"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9247"
  animName: "AS_CH_Enter_PL_052"
  bank: "SFX_Moros"
  playEvent: "Play_SFX_AS_CH_Enter_PL_052"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_052"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9248"
  animName: "AS_CH_IdleShow_PL_052"
  bank: "SFX_Moros"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_052"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_052"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9249"
  animName: "AS_CH_Enter_PL_057"
  bank: "SFX_Fireray"
  playEvent: "Play_SFX_AS_CH_Enter_PL_057"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_057"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9250"
  animName: "AS_CH_IdleShow_PL_057"
  bank: "SFX_Fireray"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_057"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_057"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9251"
  animName: "AS_CH_Enter_PL_072"
  bank: "SFX_Master"
  playEvent: "Play_SFX_AS_CH_Enter_PL_072"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_072"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9252"
  animName: "AS_CH_IdleShow_PL_072"
  bank: "SFX_Master"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_072"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_072"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9253"
  animName: "AS_CH_Enter_PL_063"
  bank: "SFX_Zhen"
  playEvent: "Play_SFX_AS_CH_Enter_PL_063"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_063"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9254"
  animName: "AS_CH_IdleShow_PL_063"
  bank: "SFX_Zhen"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_063"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_063"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9255"
  animName: "AS_CH_Enter_PL_067"
  bank: "SFX_Lianouhu"
  playEvent: "Play_SFX_AS_CH_Enter_PL_067"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_067"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9256"
  animName: "AS_CH_IdleShow_PL_067"
  bank: "SFX_Lianouhu"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_067"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_067"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9257"
  animName: "AS_CH_Enter_PL_069"
  bank: "SFX_SoldierShandian"
  playEvent: "Play_SFX_AS_CH_Enter_PL_069"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_069"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9258"
  animName: "AS_CH_IdleShow_PL_069"
  bank: "SFX_SoldierShandian"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_069"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_069"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9259"
  animName: "AS_CH_Enter_PL_070"
  bank: "SFX_SoldierTiebi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_070"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_070"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9260"
  animName: "AS_CH_IdleShow_PL_070"
  bank: "SFX_SoldierTiebi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_070"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_070"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9261"
  animName: "AS_CH_Enter_PL_059"
  bank: "SFX_FeiChai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_059"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_059"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9262"
  animName: "AS_CH_IdleShow_PL_059"
  bank: "SFX_FeiChai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_059"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_059"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9263"
  animName: "AS_CH_Enter_PL_071"
  bank: "SFX_TuXingXing"
  playEvent: "Play_SFX_AS_CH_Enter_PL_071"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_071"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9264"
  animName: "AS_CH_IdleShow_PL_071"
  bank: "SFX_TuXingXing"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_071"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_071"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9265"
  animName: "AS_CH_ObtainIdle_OG_007"
  bank: "SFX_Eunoia"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_007"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9266"
  animName: "AS_CH_ObtainIdle_OG_007_HP01"
  bank: "SFX_Eunoia"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_007"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9267"
  animName: "AS_CH_ObtainIdle_OG_007_HP02"
  bank: "SFX_Eunoia"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_007"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9268"
  animName: "AS_CH_Idle_OG_007_HP01"
  bank: "SFX_Eunoia"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_007"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9269"
  animName: "AS_CH_Idle_OG_007_HP02"
  bank: "SFX_Eunoia"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_007"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9270"
  animName: "AS_CH_ObtainIdle_OG_008"
  bank: "SFX_EunoiaExtra"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_008"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_008"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9271"
  animName: "AS_CH_ObtainIdle_OG_007"
  bank: "VO_Eunoia"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_VO_Eunoia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9272"
  animName: "AS_CH_ObtainIdle_OG_007_HP01"
  bank: "VO_Eunoia"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_VO_Eunoia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9273"
  animName: "AS_CH_ObtainIdle_OG_007_HP02"
  bank: "VO_Eunoia"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_VO_Eunoia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9274"
  animName: "AS_CH_Idle_OG_007_HP01"
  bank: "VO_Eunoia"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_VO_Eunoia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9275"
  animName: "AS_CH_Idle_OG_007_HP02"
  bank: "VO_Eunoia"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_007"
  stopEvent: "Stop_VO_Eunoia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9276"
  animName: "AS_CH_ObtainIdle_OG_008"
  bank: "VO_EunoiaExtra"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_008"
  stopEvent: "Stop_VO_EunoiaExtra"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9277"
  animName: "AS_CH_Enter_PL_077"
  bank: "SFX_JuZi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_077"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_077"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9278"
  animName: "AS_CH_IdleShow_PL_058"
  bank: "SFX_Ledger"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_058"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_058"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9279"
  animName: "AS_CH_Enter_PL_077"
  bank: "VO_JuZi"
  playEvent: "Play_VO_AS_CH_Enter_PL_077"
  stopEvent: "Stop_VO_JuZi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9280"
  animName: "AS_CH_IdleShow_PL_058"
  bank: "VO_Ledger"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_058"
  stopEvent: "Stop_VO_Ledger"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9281"
  animName: "AS_CH_IdleShow_PL_077"
  bank: "SFX_JuZi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_077"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_077"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9282"
  animName: "AS_CH_IdleShow_PL_077"
  bank: "VO_JuZi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_077"
  stopEvent: "Stop_VO_JuZi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9283"
  animName: "AS_CH_Enter_PL_058"
  bank: "SFX_Ledger"
  playEvent: "Play_SFX_AS_CH_Enter_PL_058"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_058"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9284"
  animName: "AS_CH_Enter_PL_058"
  bank: "VO_Ledger"
  playEvent: "Play_VO_AS_CH_Enter_PL_058"
  stopEvent: "Stop_VO_Ledger"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9285"
  animName: "AS_CH_Enter_PL_066"
  bank: "SFX_Caigou"
  playEvent: "Play_SFX_AS_CH_Enter_PL_066"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_066"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9286"
  animName: "AS_CH_Enter_PL_066"
  bank: "VO_Caigou"
  playEvent: "Play_VO_AS_CH_Enter_PL_066"
  stopEvent: "Stop_VO_Caigou"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9287"
  animName: "AS_CH_IdleShow_PL_066"
  bank: "SFX_Caigou"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_066"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_066"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9288"
  animName: "AS_CH_IdleShow_PL_066"
  bank: "VO_Caigou"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_066"
  stopEvent: "Stop_VO_Caigou"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9289"
  animName: "AS_CH_Enter_PL_064"
  bank: "VO_Tiberius"
  playEvent: "Play_VO_AS_CH_Enter_PL_064"
  stopEvent: "Stop_VO_Tiberius"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9290"
  animName: "AS_CH_IdleShow_PL_064"
  bank: "VO_Tiberius"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_064"
  stopEvent: "Stop_VO_Tiberius"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9291"
  animName: "AS_CH_Enter_PL_065"
  bank: "VO_Elina"
  playEvent: "Play_VO_AS_CH_Enter_PL_065"
  stopEvent: "Stop_VO_Elina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9292"
  animName: "AS_CH_IdleShow_PL_065"
  bank: "VO_Elina"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_065"
  stopEvent: "Stop_VO_Elina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9293"
  animName: "AS_CH_Enter_PL_064"
  bank: "SFX_Tiberius"
  playEvent: "Play_SFX_AS_CH_Enter_PL_064"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_064"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9294"
  animName: "AS_CH_IdleShow_PL_064"
  bank: "SFX_Tiberius"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_064"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_064"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9295"
  animName: "AS_CH_Enter_PL_065"
  bank: "SFX_Elina"
  playEvent: "Play_SFX_AS_CH_Enter_PL_065"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_065"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9296"
  animName: "AS_CH_IdleShow_PL_065"
  bank: "SFX_Elina"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_065"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_065"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9297"
  animName: "AS_CH_Enter_PL_054"
  bank: "SFX_Fengjian"
  playEvent: "Play_SFX_AS_CH_Enter_PL_054"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_054"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9298"
  animName: "AS_CH_IdleShow_PL_054"
  bank: "SFX_Fengjian"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_054"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_054"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9299"
  animName: "AS_CH_Enter_PL_054"
  bank: "VO_Fengjian"
  playEvent: "Play_VO_AS_CH_Enter_PL_054"
  stopEvent: "Stop_VO_Fengjian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9300"
  animName: "AS_CH_IdleShow_PL_054"
  bank: "VO_Fengjian"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_054"
  stopEvent: "Stop_VO_Fengjian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9301"
  animName: "AS_CH_Enter_PL_055"
  bank: "SFX_Nini"
  playEvent: "Play_SFX_AS_CH_Enter_PL_055"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_055"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9302"
  animName: "AS_CH_IdleShow_PL_055"
  bank: "SFX_Nini"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_055"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_055"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9303"
  animName: "AS_CH_Enter_PL_055"
  bank: "VO_Nini"
  playEvent: "Play_VO_AS_CH_Enter_PL_055"
  stopEvent: "Stop_VO_Nini"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9304"
  animName: "AS_CH_IdleShow_PL_055"
  bank: "VO_Nini"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_055"
  stopEvent: "Stop_VO_Nini"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9305"
  animName: "AS_CH_Enter_PL_056"
  bank: "SFX_Adai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_056"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_056"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9306"
  animName: "AS_CH_IdleShow_PL_056"
  bank: "SFX_Adai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_056"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_056"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9307"
  animName: "AS_CH_Enter_PL_056"
  bank: "VO_Adai"
  playEvent: "Play_VO_AS_CH_Enter_PL_056"
  stopEvent: "Stop_VO_Adai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9308"
  animName: "AS_CH_IdleShow_PL_056"
  bank: "VO_Adai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_056"
  stopEvent: "Stop_VO_Adai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9309"
  animName: "AS_CH_Enter_PL_089"
  bank: "VO_Dora"
  playEvent: "Play_VO_AS_CH_Enter_PL_089"
  stopEvent: "Stop_VO_Dora"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9310"
  animName: "AS_CH_IdleShow_PL_089"
  bank: "VO_Dora"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_089"
  stopEvent: "Stop_VO_Dora"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9311"
  animName: "AS_CH_Enter_PL_076"
  bank: "VO_Andrew"
  playEvent: "Play_VO_AS_CH_Enter_PL_076"
  stopEvent: "Stop_VO_Andrew"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9312"
  animName: "AS_CH_IdleShow_PL_076"
  bank: "VO_Andrew"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_076"
  stopEvent: "Stop_VO_Andrew"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9313"
  animName: "AS_CH_Enter_PL_088"
  bank: "VO_Dolores"
  playEvent: "Play_VO_AS_CH_Enter_PL_088"
  stopEvent: "Stop_VO_Dolores"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9314"
  animName: "AS_CH_IdleShow_PL_088"
  bank: "VO_Dolores"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_088"
  stopEvent: "Stop_VO_Dolores"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9315"
  animName: "AS_CH_Enter_PL_087"
  bank: "VO_LanlingwangYLTX"
  playEvent: "Play_VO_AS_CH_Enter_PL_087"
  stopEvent: "Stop_VO_LanlingwangYLTX"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9316"
  animName: "AS_CH_IdleShow_PL_087"
  bank: "VO_LanlingwangYLTX"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_087"
  stopEvent: "Stop_VO_LanlingwangYLTX"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9317"
  animName: "AS_CH_Enter_PL_086"
  bank: "VO_XiaoqiaoYNXD"
  playEvent: "Play_VO_AS_CH_Enter_PL_086"
  stopEvent: "Stop_VO_XiaoqiaoYNXD"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9318"
  animName: "AS_CH_IdleShow_PL_086"
  bank: "VO_XiaoqiaoYNXD"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_086"
  stopEvent: "Stop_VO_XiaoqiaoYNXD"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9319"
  animName: "AS_CH_Enter_PL_084"
  bank: "VO_KaiXTZJ"
  playEvent: "Play_VO_AS_CH_Enter_PL_084"
  stopEvent: "Stop_VO_KaiXTZJ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9320"
  animName: "AS_CH_IdleShow_PL_084"
  bank: "VO_KaiXTZJ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_084"
  stopEvent: "Stop_VO_KaiXTZJ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9321"
  animName: "AS_CH_Enter_PL_085"
  bank: "VO_SunceZAZY"
  playEvent: "Play_VO_AS_CH_Enter_PL_085"
  stopEvent: "Stop_VO_SunceZAZY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9322"
  animName: "AS_CH_IdleShow_PL_085"
  bank: "VO_SunceZAZY"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_085"
  stopEvent: "Stop_VO_SunceZAZY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9323"
  animName: "AS_CH_Enter_PL_079"
  bank: "VO_SweetBeanYJ"
  playEvent: "Play_VO_AS_CH_Enter_PL_079"
  stopEvent: "Stop_VO_SweetBeanYJ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9324"
  animName: "AS_CH_IdleShow_PL_079"
  bank: "VO_SweetBeanYJ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_079"
  stopEvent: "Stop_VO_SweetBeanYJ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9325"
  animName: "AS_CH_Enter_PL_078"
  bank: "VO_SweetBeanNM"
  playEvent: "Play_VO_AS_CH_Enter_PL_078"
  stopEvent: "Stop_VO_SweetBeanNM"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9326"
  animName: "AS_CH_IdleShow_PL_078"
  bank: "VO_SweetBeanNM"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_078"
  stopEvent: "Stop_VO_SweetBeanNM"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9327"
  animName: "AS_CH_IdleShow_OG_010"
  bank: "VO_JessicaExtra"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_010"
  stopEvent: "Stop_VO_JessicaExtra"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9328"
  animName: "AS_CH_IdleShow_OG_009"
  bank: "VO_Jessica"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_009"
  stopEvent: "Stop_VO_Jessica"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9329"
  animName: "AS_CH_Enter_PL_089"
  bank: "SFX_Dora"
  playEvent: "Play_SFX_AS_CH_Enter_PL_089"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_089"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9330"
  animName: "AS_CH_IdleShow_PL_089"
  bank: "SFX_Dora"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_089"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_089"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9331"
  animName: "AS_CH_Enter_PL_076"
  bank: "SFX_Andrew"
  playEvent: "Play_SFX_AS_CH_Enter_PL_076"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_076"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9332"
  animName: "AS_CH_IdleShow_PL_076"
  bank: "SFX_Andrew"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_076"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_076"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9333"
  animName: "AS_CH_Enter_PL_088"
  bank: "SFX_Dolores"
  playEvent: "Play_SFX_AS_CH_Enter_PL_088"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_088"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9334"
  animName: "AS_CH_IdleShow_PL_088"
  bank: "SFX_Dolores"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_088"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_088"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9335"
  animName: "AS_CH_Enter_PL_087"
  bank: "SFX_LanlingwangYLTX"
  playEvent: "Play_SFX_AS_CH_Enter_PL_087"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_087"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9336"
  animName: "AS_CH_IdleShow_PL_087"
  bank: "SFX_LanlingwangYLTX"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_087"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_087"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9337"
  animName: "AS_CH_Enter_PL_086"
  bank: "SFX_XiaoqiaoYNXD"
  playEvent: "Play_SFX_AS_CH_Enter_PL_086"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_086"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9338"
  animName: "AS_CH_IdleShow_PL_086"
  bank: "SFX_XiaoqiaoYNXD"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_086"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_086"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9339"
  animName: "AS_CH_Enter_PL_084"
  bank: "SFX_KaiXTZJ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_084"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_084"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9340"
  animName: "AS_CH_IdleShow_PL_084"
  bank: "SFX_KaiXTZJ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_084"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_084"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9341"
  animName: "AS_CH_Enter_PL_085"
  bank: "SFX_SunceZAZY"
  playEvent: "Play_SFX_AS_CH_Enter_PL_085"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_085"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9342"
  animName: "AS_CH_IdleShow_PL_085"
  bank: "SFX_SunceZAZY"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_085"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_085"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9343"
  animName: "AS_CH_Enter_PL_079"
  bank: "SFX_SweetBeanYJ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_079"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_079"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9344"
  animName: "AS_CH_IdleShow_PL_079"
  bank: "SFX_SweetBeanYJ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_079"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_079"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9345"
  animName: "AS_CH_Enter_PL_078"
  bank: "SFX_SweetBeanNM"
  playEvent: "Play_SFX_AS_CH_Enter_PL_078"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_078"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9346"
  animName: "AS_CH_IdleShow_PL_078"
  bank: "SFX_SweetBeanNM"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_078"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_078"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9347"
  animName: "AS_CH_IdleShow_OG_010"
  bank: "SFX_JessicaExtra"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_010"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_010"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9348"
  animName: "AS_CH_IdleShow_OG_009"
  bank: "SFX_Jessica"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_009"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_009"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9349"
  animName: "AS_CH_Enter_PL_083"
  bank: "SFX_Tammy"
  playEvent: "Play_SFX_AS_CH_Enter_PL_083"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_083"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9350"
  animName: "AS_CH_IdleShow_PL_083"
  bank: "SFX_Tammy"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_083"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_083"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9351"
  animName: "AS_CH_Enter_PL_082"
  bank: "SFX_Kazuhiko"
  playEvent: "Play_SFX_AS_CH_Enter_PL_082"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_082"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9352"
  animName: "AS_CH_IdleShow_PL_082"
  bank: "SFX_Kazuhiko"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_082"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_082"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9353"
  animName: "AS_CH_Enter_PL_081"
  bank: "SFX_MarukoQD"
  playEvent: "Play_SFX_AS_CH_Enter_PL_081"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_081"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9354"
  animName: "AS_CH_IdleShow_PL_081"
  bank: "SFX_MarukoQD"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_081"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_081"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9355"
  animName: "AS_CH_Enter_PL_080"
  bank: "SFX_Maruko"
  playEvent: "Play_SFX_AS_CH_Enter_PL_080"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_080"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9356"
  animName: "AS_CH_IdleShow_PL_080"
  bank: "SFX_Maruko"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_080"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_080"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9357"
  animName: "AS_CH_Enter_PL_083"
  bank: "VO_Tammy"
  playEvent: "Play_VO_AS_CH_Enter_PL_083"
  stopEvent: "Stop_VO_Tammy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9358"
  animName: "AS_CH_IdleShow_PL_083"
  bank: "VO_Tammy"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_083"
  stopEvent: "Stop_VO_Tammy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9359"
  animName: "AS_CH_Enter_PL_082"
  bank: "VO_Kazuhiko"
  playEvent: "Play_VO_AS_CH_Enter_PL_082"
  stopEvent: "Stop_VO_Kazuhiko"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9360"
  animName: "AS_CH_IdleShow_PL_082"
  bank: "VO_Kazuhiko"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_082"
  stopEvent: "Stop_VO_Kazuhiko"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9361"
  animName: "AS_CH_Enter_PL_081"
  bank: "VO_MarukoQD"
  playEvent: "Play_VO_AS_CH_Enter_PL_081"
  stopEvent: "Stop_VO_MarukoQD"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9362"
  animName: "AS_CH_IdleShow_PL_081"
  bank: "VO_MarukoQD"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_081"
  stopEvent: "Stop_VO_MarukoQD"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9363"
  animName: "AS_CH_Enter_PL_080"
  bank: "VO_Maruko"
  playEvent: "Play_VO_AS_CH_Enter_PL_080"
  stopEvent: "Stop_VO_Maruko"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9364"
  animName: "AS_CH_IdleShow_PL_080"
  bank: "VO_Maruko"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_080"
  stopEvent: "Stop_VO_Maruko"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9365"
  animName: "AS_CH_Enter_PL_093"
  bank: "SFX_Dina"
  playEvent: "Play_SFX_AS_CH_Enter_PL_093"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_093"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9366"
  animName: "AS_CH_IdleShow_PL_093"
  bank: "SFX_Dina"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_093"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_093"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9367"
  animName: "AS_CH_Enter_PL_093"
  bank: "VO_Dina"
  playEvent: "Play_VO_AS_CH_Enter_PL_093"
  stopEvent: "Stop_VO_Dina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9368"
  animName: "AS_CH_IdleShow_PL_093"
  bank: "VO_Dina"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_093"
  stopEvent: "Stop_VO_Dina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9369"
  animName: "AS_CH_IdleShow_OG_011"
  bank: "SFX_Jase"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_011"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_011"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9370"
  animName: "AS_CH_IdleShow_OG_012"
  bank: "SFX_Lillian"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_012"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_012"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9371"
  animName: "AS_CH_IdleShow_OG_011"
  bank: "VO_Jase"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_011"
  stopEvent: "Stop_VO_Jase"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9372"
  animName: "AS_CH_IdleShow_OG_012"
  bank: "VO_Lillian"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_012"
  stopEvent: "Stop_VO_Lillian"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9373"
  animName: "AS_CH_Enter_PL_091"
  bank: "SFX_CatGirl"
  playEvent: "Play_SFX_AS_CH_Enter_PL_091"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_091"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9374"
  animName: "AS_CH_IdleShow_PL_091"
  bank: "SFX_CatGirl"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_091"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_091"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9375"
  animName: "AS_CH_Enter_PL_090"
  bank: "SFX_DogBoy"
  playEvent: "Play_SFX_AS_CH_Enter_PL_090"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_090"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9376"
  animName: "AS_CH_IdleShow_PL_090"
  bank: "SFX_DogBoy"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_090"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_090"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9377"
  animName: "AS_CH_Enter_PL_092"
  bank: "SFX_BaiheBoy"
  playEvent: "Play_SFX_AS_CH_Enter_PL_092"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_092"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9378"
  animName: "AS_CH_IdleShow_PL_092"
  bank: "SFX_BaiheBoy"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_092"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_092"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9379"
  animName: "AS_CH_Enter_PL_101"
  bank: "SFX_ChunGirl"
  playEvent: "Play_SFX_AS_CH_Enter_PL_101"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_101"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9380"
  animName: "AS_CH_IdleShow_PL_101"
  bank: "SFX_ChunGirl"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_101"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_101"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9381"
  animName: "AS_CH_Enter_PL_091"
  bank: "VO_CatGirl"
  playEvent: "Play_VO_AS_CH_Enter_PL_091"
  stopEvent: "Stop_VO_CatGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9382"
  animName: "AS_CH_IdleShow_PL_091"
  bank: "VO_CatGirl"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_091"
  stopEvent: "Stop_VO_CatGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9383"
  animName: "AS_CH_Enter_PL_090"
  bank: "VO_DogBoy"
  playEvent: "Play_VO_AS_CH_Enter_PL_090"
  stopEvent: "Stop_VO_DogBoy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9384"
  animName: "AS_CH_IdleShow_PL_090"
  bank: "VO_DogBoy"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_090"
  stopEvent: "Stop_VO_DogBoy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9385"
  animName: "AS_CH_Enter_PL_092"
  bank: "VO_BaiheBoy"
  playEvent: "Play_VO_AS_CH_Enter_PL_092"
  stopEvent: "Stop_VO_BaiheBoy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9386"
  animName: "AS_CH_IdleShow_PL_092"
  bank: "VO_BaiheBoy"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_092"
  stopEvent: "Stop_VO_BaiheBoy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9387"
  animName: "AS_CH_Enter_PL_101"
  bank: "VO_ChunGirl"
  playEvent: "Play_VO_AS_CH_Enter_PL_101"
  stopEvent: "Stop_VO_ChunGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9388"
  animName: "AS_CH_IdleShow_PL_101"
  bank: "VO_ChunGirl"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_101"
  stopEvent: "Stop_VO_ChunGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9389"
  animName: "AS_CH_Enter_PL_103"
  bank: "SFX_NataliaBlack"
  playEvent: "Play_SFX_AS_CH_Enter_PL_103"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_103"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9390"
  animName: "AS_CH_IdleShow_PL_103"
  bank: "SFX_NataliaBlack"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_103"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_103"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9391"
  animName: "AS_CH_Enter_PL_103"
  bank: "VO_NataliaBlack"
  playEvent: "Play_VO_AS_CH_Enter_PL_103"
  stopEvent: "Stop_VO_NataliaBlack"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9392"
  animName: "AS_CH_IdleShow_PL_103"
  bank: "VO_NataliaBlack"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_103"
  stopEvent: "Stop_VO_NataliaBlack"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9393"
  animName: "AS_CH_IdleShow_OG_013"
  bank: "SFX_Amelie"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_013"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_013"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9394"
  animName: "AS_CH_IdleShow_OG_014"
  bank: "SFX_Eric"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_014"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_014"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9395"
  animName: "AS_CH_IdleShow_OG_013"
  bank: "VO_Amelie"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_013"
  stopEvent: "Stop_VO_Amelie"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9396"
  animName: "AS_CH_IdleShow_OG_014"
  bank: "VO_Eric"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_014"
  stopEvent: "Stop_VO_Eric"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9397"
  animName: "AS_CH_Enter_PL_107"
  bank: "SFX_Fiesta"
  playEvent: "Play_SFX_AS_CH_Enter_PL_107"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_107"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9398"
  animName: "AS_CH_IdleShow_PL_107"
  bank: "SFX_Fiesta"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_107"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_107"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9399"
  animName: "AS_CH_Enter_PL_106"
  bank: "SFX_Lanikai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_106"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_106"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9400"
  animName: "AS_CH_IdleShow_PL_106"
  bank: "SFX_Lanikai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_106"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_106"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9401"
  animName: "AS_CH_Enter_PL_099"
  bank: "SFX_LineFriendsBrown"
  playEvent: "Play_SFX_AS_CH_Enter_PL_099"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_099"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9402"
  animName: "AS_CH_IdleShow_PL_099"
  bank: "SFX_LineFriendsBrown"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_099"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_099"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9403"
  animName: "AS_CH_Enter_PL_100"
  bank: "SFX_LineFriendsCony"
  playEvent: "Play_SFX_AS_CH_Enter_PL_100"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_100"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9404"
  animName: "AS_CH_IdleShow_PL_100"
  bank: "SFX_LineFriendsCony"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_100"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_100"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9405"
  animName: "AS_CH_Enter_PL_105"
  bank: "SFX_Orca"
  playEvent: "Play_SFX_AS_CH_Enter_PL_105"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_105"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9406"
  animName: "AS_CH_IdleShow_PL_105"
  bank: "SFX_Orca"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_105"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_105"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9407"
  animName: "AS_CH_Enter_PL_104"
  bank: "SFX_Pearl"
  playEvent: "Play_SFX_AS_CH_Enter_PL_104"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_104"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9408"
  animName: "AS_CH_IdleShow_PL_104"
  bank: "SFX_Pearl"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_104"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_104"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9409"
  animName: "AS_CH_Enter_PL_107"
  bank: "VO_Fiesta"
  playEvent: "Play_VO_AS_CH_Enter_PL_107"
  stopEvent: "Stop_VO_Fiesta"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9410"
  animName: "AS_CH_IdleShow_PL_107"
  bank: "VO_Fiesta"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_107"
  stopEvent: "Stop_VO_Fiesta"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9411"
  animName: "AS_CH_Enter_PL_106"
  bank: "VO_Lanikai"
  playEvent: "Play_VO_AS_CH_Enter_PL_106"
  stopEvent: "Stop_VO_Lanikai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9412"
  animName: "AS_CH_IdleShow_PL_106"
  bank: "VO_Lanikai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_106"
  stopEvent: "Stop_VO_Lanikai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9413"
  animName: "AS_CH_Enter_PL_099"
  bank: "VO_LineFriendsBrown"
  playEvent: "Play_VO_AS_CH_Enter_PL_099"
  stopEvent: "Stop_VO_LineFriendsBrown"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9414"
  animName: "AS_CH_IdleShow_PL_099"
  bank: "VO_LineFriendsBrown"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_099"
  stopEvent: "Stop_VO_LineFriendsBrown"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9415"
  animName: "AS_CH_Enter_PL_100"
  bank: "VO_LineFriendsCony"
  playEvent: "Play_VO_AS_CH_Enter_PL_100"
  stopEvent: "Stop_VO_LineFriendsCony"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9416"
  animName: "AS_CH_IdleShow_PL_100"
  bank: "VO_LineFriendsCony"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_100"
  stopEvent: "Stop_VO_LineFriendsCony"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9417"
  animName: "AS_CH_Enter_PL_105"
  bank: "VO_Orca"
  playEvent: "Play_VO_AS_CH_Enter_PL_105"
  stopEvent: "Stop_VO_Orca"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9418"
  animName: "AS_CH_IdleShow_PL_105"
  bank: "VO_Orca"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_105"
  stopEvent: "Stop_VO_Orca"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9419"
  animName: "AS_CH_Enter_PL_104"
  bank: "VO_Pearl"
  playEvent: "Play_VO_AS_CH_Enter_PL_104"
  stopEvent: "Stop_VO_Pearl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9420"
  animName: "AS_CH_IdleShow_PL_104"
  bank: "VO_Pearl"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_104"
  stopEvent: "Stop_VO_Pearl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9421"
  animName: "AS_CH_IdleShow_OG_015"
  bank: "SFX_Porthos"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_015"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_015"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9422"
  animName: "AS_CH_IdleShow_OG_016"
  bank: "SFX_Dartagnan"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_016"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_016"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9423"
  animName: "AS_CH_IdleShow_OG_017"
  bank: "SFX_Athos"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_017"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_017"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9424"
  animName: "AS_CH_IdleShow_OG_015"
  bank: "VO_Porthos"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_015"
  stopEvent: "Stop_VO_Porthos"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9425"
  animName: "AS_CH_IdleShow_OG_016"
  bank: "VO_Dartagnan"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_016"
  stopEvent: "Stop_VO_Dartagnan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9426"
  animName: "AS_CH_IdleShow_OG_017"
  bank: "VO_Athos"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_017"
  stopEvent: "Stop_VO_Athos"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9427"
  animName: "AS_CH_IdleShow_OG_020"
  bank: "SFX_PhoenixKing"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_020"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_020"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9428"
  animName: "AS_CH_IdleShow_OG_021"
  bank: "SFX_PhoenixQueen"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_021"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_021"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9429"
  animName: "AS_CH_IdleShow_OG_020"
  bank: "VO_PhoenixKing"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_020"
  stopEvent: "Stop_VO_PhoenixKing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9430"
  animName: "AS_CH_IdleShow_OG_021"
  bank: "VO_PhoenixQueen"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_021"
  stopEvent: "Stop_VO_PhoenixQueen"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9431"
  animName: "AS_CH_Enter_PL_108"
  bank: "SFX_Nicole"
  playEvent: "Play_SFX_AS_CH_Enter_PL_108"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_108"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9432"
  animName: "AS_CH_IdleShow_PL_108"
  bank: "SFX_Nicole"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_108"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_108"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9433"
  animName: "AS_CH_Enter_PL_108"
  bank: "VO_Nicole"
  playEvent: "Play_VO_AS_CH_Enter_PL_108"
  stopEvent: "Stop_VO_Nicole"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9434"
  animName: "AS_CH_IdleShow_PL_108"
  bank: "VO_Nicole"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_108"
  stopEvent: "Stop_VO_Nicole"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9435"
  animName: "AS_CH_Enter_PL_121"
  bank: "SFX_TobyB"
  playEvent: "Play_SFX_AS_CH_Enter_PL_121"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_121"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9436"
  animName: "AS_CH_IdleShow_PL_121"
  bank: "SFX_TobyB"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_121"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_121"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9437"
  animName: "AS_CH_Enter_PL_121"
  bank: "VO_TobyB"
  playEvent: "Play_VO_AS_CH_Enter_PL_121"
  stopEvent: "Stop_VO_TobyB"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9438"
  animName: "AS_CH_IdleShow_PL_121"
  bank: "VO_TobyB"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_121"
  stopEvent: "Stop_VO_TobyB"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9439"
  animName: "AS_CH_Enter_PL_117"
  bank: "SFX_ZhenhuanZHZ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_117"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_117"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9440"
  animName: "AS_CH_IdleShow_PL_117"
  bank: "SFX_ZhenhuanZHZ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_117"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_117"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9441"
  animName: "AS_CH_Enter_PL_117"
  bank: "VO_ZhenhuanZHZ"
  playEvent: "Play_VO_AS_CH_Enter_PL_117"
  stopEvent: "Stop_VO_ZhenhuanZHZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9442"
  animName: "AS_CH_IdleShow_PL_117"
  bank: "VO_ZhenhuanZHZ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_117"
  stopEvent: "Stop_VO_ZhenhuanZHZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9443"
  animName: "AS_CH_Enter_PL_118"
  bank: "SFX_HuangshangZHZ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_118"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_118"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9444"
  animName: "AS_CH_IdleShow_PL_118"
  bank: "SFX_HuangshangZHZ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_118"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_118"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9445"
  animName: "AS_CH_Enter_PL_118"
  bank: "VO_HuangshangZHZ"
  playEvent: "Play_VO_AS_CH_Enter_PL_118"
  stopEvent: "Stop_VO_HuangshangZHZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9446"
  animName: "AS_CH_IdleShow_PL_118"
  bank: "VO_HuangshangZHZ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_118"
  stopEvent: "Stop_VO_HuangshangZHZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9447"
  animName: "AS_CH_Enter_PL_119"
  bank: "SFX_HuafeiZHZ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_119"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_119"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9448"
  animName: "AS_CH_IdleShow_PL_119"
  bank: "SFX_HuafeiZHZ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_119"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_119"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9449"
  animName: "AS_CH_Enter_PL_119"
  bank: "VO_HuafeiZHZ"
  playEvent: "Play_VO_AS_CH_Enter_PL_119"
  stopEvent: "Stop_VO_HuafeiZHZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9450"
  animName: "AS_CH_IdleShow_PL_119"
  bank: "VO_HuafeiZHZ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_119"
  stopEvent: "Stop_VO_HuafeiZHZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9451"
  animName: "AS_CH_Enter_PL_134"
  bank: "SFX_KuromiGirl"
  playEvent: "Play_SFX_AS_CH_Enter_PL_134"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_134"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9452"
  animName: "AS_CH_IdleShow_PL_134"
  bank: "SFX_KuromiGirl"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_134"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_134"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9453"
  animName: "AS_CH_Enter_PL_134"
  bank: "VO_KuromiGirl"
  playEvent: "Play_VO_AS_CH_Enter_PL_134"
  stopEvent: "Stop_VO_KuromiGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9454"
  animName: "AS_CH_IdleShow_PL_134"
  bank: "VO_KuromiGirl"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_134"
  stopEvent: "Stop_VO_KuromiGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9455"
  animName: "AS_CH_Enter_PL_112"
  bank: "SFX_FallGuysFries"
  playEvent: "Play_SFX_AS_CH_Enter_PL_112"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_112"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9456"
  animName: "AS_CH_IdleShow_PL_112"
  bank: "SFX_FallGuysFries"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_112"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_112"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9457"
  animName: "AS_CH_Enter_PL_112"
  bank: "VO_FallGuysFries"
  playEvent: "Play_VO_AS_CH_Enter_PL_112"
  stopEvent: "Stop_VO_FallGuysFries"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9458"
  animName: "AS_CH_IdleShow_PL_112"
  bank: "VO_FallGuysFries"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_112"
  stopEvent: "Stop_VO_FallGuysFries"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9459"
  animName: "AS_CH_Enter_PL_113"
  bank: "SFX_FallGuysBasic"
  playEvent: "Play_SFX_AS_CH_Enter_PL_113"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_113"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9460"
  animName: "AS_CH_IdleShow_PL_113"
  bank: "SFX_FallGuysBasic"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_113"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_113"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9461"
  animName: "AS_CH_Enter_PL_113"
  bank: "VO_FallGuysBasic"
  playEvent: "Play_VO_AS_CH_Enter_PL_113"
  stopEvent: "Stop_VO_FallGuysBasic"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9462"
  animName: "AS_CH_IdleShow_PL_113"
  bank: "VO_FallGuysBasic"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_113"
  stopEvent: "Stop_VO_FallGuysBasic"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9463"
  animName: "AS_CH_Enter_PL_111"
  bank: "SFX_FallGuysPaladin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_111"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_111"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9464"
  animName: "AS_CH_IdleShow_PL_111"
  bank: "SFX_FallGuysPaladin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_111"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_111"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9465"
  animName: "AS_CH_Enter_PL_111"
  bank: "VO_FallGuysPaladin"
  playEvent: "Play_VO_AS_CH_Enter_PL_111"
  stopEvent: "Stop_VO_FallGuysPaladin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9466"
  animName: "AS_CH_IdleShow_PL_111"
  bank: "VO_FallGuysPaladin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_111"
  stopEvent: "Stop_VO_FallGuysPaladin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9467"
  animName: "AS_CH_Enter_PL_115"
  bank: "SFX_HelloKitty"
  playEvent: "Play_SFX_AS_CH_Enter_PL_115"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_115"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9468"
  animName: "AS_CH_IdleShow_PL_115"
  bank: "SFX_HelloKitty"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_115"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_115"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9469"
  animName: "AS_CH_Enter_PL_115"
  bank: "VO_HelloKitty"
  playEvent: "Play_VO_AS_CH_Enter_PL_115"
  stopEvent: "Stop_VO_HelloKitty"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9470"
  animName: "AS_CH_IdleShow_PL_115"
  bank: "VO_HelloKitty"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_115"
  stopEvent: "Stop_VO_HelloKitty"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9471"
  animName: "AS_CH_Enter_PL_116"
  bank: "SFX_Kuromi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_116"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_116"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9472"
  animName: "AS_CH_IdleShow_PL_116"
  bank: "SFX_Kuromi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_116"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_116"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9473"
  animName: "AS_CH_Enter_PL_116"
  bank: "VO_Kuromi"
  playEvent: "Play_VO_AS_CH_Enter_PL_116"
  stopEvent: "Stop_VO_Kuromi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9474"
  animName: "AS_CH_IdleShow_PL_116"
  bank: "VO_Kuromi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_116"
  stopEvent: "Stop_VO_Kuromi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9475"
  animName: "AS_CH_Enter_PL_095"
  bank: "SFX_Wukong"
  playEvent: "Play_SFX_AS_CH_Enter_PL_095"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_095"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9476"
  animName: "AS_CH_IdleShow_PL_095"
  bank: "SFX_Wukong"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_095"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_095"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9477"
  animName: "AS_CH_Enter_PL_095"
  bank: "VO_Wukong"
  playEvent: "Play_VO_AS_CH_Enter_PL_095"
  stopEvent: "Stop_VO_Wukong"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9478"
  animName: "AS_CH_IdleShow_PL_095"
  bank: "VO_Wukong"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_095"
  stopEvent: "Stop_VO_Wukong"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9479"
  animName: "AS_CH_Enter_PL_096"
  bank: "SFX_Tangseng"
  playEvent: "Play_SFX_AS_CH_Enter_PL_096"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_096"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9480"
  animName: "AS_CH_IdleShow_PL_096"
  bank: "SFX_Tangseng"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_096"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_096"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9481"
  animName: "AS_CH_Enter_PL_097"
  bank: "SFX_Bajie"
  playEvent: "Play_SFX_AS_CH_Enter_PL_097"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_097"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9482"
  animName: "AS_CH_IdleShow_PL_097"
  bank: "SFX_Bajie"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_097"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_097"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9483"
  animName: "AS_CH_Enter_PL_114"
  bank: "SFX_LuluPig"
  playEvent: "Play_SFX_AS_CH_Enter_PL_114"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_114"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9484"
  animName: "AS_CH_IdleShow_PL_114"
  bank: "SFX_LuluPig"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_114"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_114"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9485"
  animName: "AS_CH_Enter_PL_120"
  bank: "SFX_XiaGirl"
  playEvent: "Play_SFX_AS_CH_Enter_PL_120"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_120"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9486"
  animName: "AS_CH_IdleShow_PL_120"
  bank: "SFX_XiaGirl"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_120"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_120"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9487"
  animName: "AS_CH_Enter_PL_126"
  bank: "SFX_ZhaoYunLD"
  playEvent: "Play_SFX_AS_CH_Enter_PL_126"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_126"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9488"
  animName: "AS_CH_IdleShow_PL_126"
  bank: "SFX_ZhaoYunLD"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_126"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_126"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9489"
  animName: "AS_CH_Enter_PL_127"
  bank: "SFX_HOKYase"
  playEvent: "Play_SFX_AS_CH_Enter_PL_127"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_127"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9490"
  animName: "AS_CH_IdleShow_PL_127"
  bank: "SFX_HOKYase"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_127"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_127"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9491"
  animName: "AS_CH_Enter_PL_128"
  bank: "SFX_HOKAnqilaZTYX"
  playEvent: "Play_SFX_AS_CH_Enter_PL_128"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_128"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9492"
  animName: "AS_CH_IdleShow_PL_128"
  bank: "SFX_HOKAnqilaZTYX"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_128"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_128"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9493"
  animName: "AS_CH_Enter_PL_129"
  bank: "SFX_SunShangXiangYJLQ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_129"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_129"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9494"
  animName: "AS_CH_IdleShow_PL_129"
  bank: "SFX_SunShangXiangYJLQ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_129"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_129"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9495"
  animName: "AS_CH_Enter_PL_130"
  bank: "SFX_HOKWukongQXSY"
  playEvent: "Play_SFX_AS_CH_Enter_PL_130"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_130"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9496"
  animName: "AS_CH_IdleShow_PL_130"
  bank: "SFX_HOKWukongQXSY"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_130"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_130"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9497"
  animName: "AS_CH_Enter_PL_131"
  bank: "SFX_HOKCaiwenjiHCRY"
  playEvent: "Play_SFX_AS_CH_Enter_PL_131"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_131"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9498"
  animName: "AS_CH_IdleShow_PL_131"
  bank: "SFX_HOKCaiwenjiHCRY"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_131"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_131"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9499"
  animName: "AS_CH_Enter_PL_140"
  bank: "SFX_CXSXiangliu"
  playEvent: "Play_SFX_AS_CH_Enter_PL_140"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_140"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9500"
  animName: "AS_CH_IdleShow_PL_140"
  bank: "SFX_CXSXiangliu"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_140"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_140"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9501"
  animName: "AS_CH_Enter_PL_141"
  bank: "SFX_CXSChishui"
  playEvent: "Play_SFX_AS_CH_Enter_PL_141"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_141"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9502"
  animName: "AS_CH_IdleShow_PL_141"
  bank: "SFX_CXSChishui"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_141"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_141"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9503"
  animName: "AS_CH_Enter_PL_142"
  bank: "SFX_CXSCangXuan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_142"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_142"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9504"
  animName: "AS_CH_IdleShow_PL_142"
  bank: "SFX_CXSCangXuan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_142"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_142"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9505"
  animName: "AS_CH_Enter_PL_143"
  bank: "SFX_CXSTushanjing"
  playEvent: "Play_SFX_AS_CH_Enter_PL_143"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_143"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9506"
  animName: "AS_CH_IdleShow_PL_143"
  bank: "SFX_CXSTushanjing"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_143"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_143"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9507"
  animName: "AS_CH_Enter_PL_144"
  bank: "SFX_CXSXiaoyao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_144"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_144"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9508"
  animName: "AS_CH_IdleShow_PL_144"
  bank: "SFX_CXSXiaoyao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_144"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_144"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9509"
  animName: "AS_CH_Enter_PL_152"
  bank: "SFX_Rabbina"
  playEvent: "Play_SFX_AS_CH_Enter_PL_152"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_152"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9510"
  animName: "AS_CH_IdleShow_PL_152"
  bank: "SFX_Rabbina"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_152"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_152"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9511"
  animName: "AS_CH_Enter_PL_153"
  bank: "SFX_Orphism"
  playEvent: "Play_SFX_AS_CH_Enter_PL_153"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_153"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9512"
  animName: "AS_CH_IdleShow_PL_153"
  bank: "SFX_Orphism"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_153"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_153"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9513"
  animName: "AS_CH_Enter_PL_154"
  bank: "SFX_Cloud"
  playEvent: "Play_SFX_AS_CH_Enter_PL_154"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_154"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9514"
  animName: "AS_CH_IdleShow_PL_154"
  bank: "SFX_Cloud"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_154"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_154"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9515"
  animName: "AS_CH_Enter_PL_155"
  bank: "SFX_TobyFrog"
  playEvent: "Play_SFX_AS_CH_Enter_PL_155"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_155"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9516"
  animName: "AS_CH_IdleShow_PL_155"
  bank: "SFX_TobyFrog"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_155"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_155"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9517"
  animName: "AS_CH_Enter_PL_156"
  bank: "SFX_HOKHouyi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_156"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_156"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9518"
  animName: "AS_CH_IdleShow_PL_156"
  bank: "SFX_HOKHouyi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_156"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_156"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9519"
  animName: "AS_CH_Enter_PL_096"
  bank: "VO_Tangseng"
  playEvent: "Play_VO_AS_CH_Enter_PL_096"
  stopEvent: "Stop_VO_Tangseng"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9520"
  animName: "AS_CH_IdleShow_PL_096"
  bank: "VO_Tangseng"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_096"
  stopEvent: "Stop_VO_Tangseng"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9521"
  animName: "AS_CH_Enter_PL_097"
  bank: "VO_Bajie"
  playEvent: "Play_VO_AS_CH_Enter_PL_097"
  stopEvent: "Stop_VO_Bajie"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9522"
  animName: "AS_CH_IdleShow_PL_097"
  bank: "VO_Bajie"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_097"
  stopEvent: "Stop_VO_Bajie"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9523"
  animName: "AS_CH_Enter_PL_114"
  bank: "VO_LuluPig"
  playEvent: "Play_VO_AS_CH_Enter_PL_114"
  stopEvent: "Stop_VO_LuluPig"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9524"
  animName: "AS_CH_IdleShow_PL_114"
  bank: "VO_LuluPig"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_114"
  stopEvent: "Stop_VO_LuluPig"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9525"
  animName: "AS_CH_Enter_PL_120"
  bank: "VO_XiaGirl"
  playEvent: "Play_VO_AS_CH_Enter_PL_120"
  stopEvent: "Stop_VO_XiaGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9526"
  animName: "AS_CH_IdleShow_PL_120"
  bank: "VO_XiaGirl"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_120"
  stopEvent: "Stop_VO_XiaGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9527"
  animName: "AS_CH_Enter_PL_126"
  bank: "VO_ZhaoYunLD"
  playEvent: "Play_VO_AS_CH_Enter_PL_126"
  stopEvent: "Stop_VO_ZhaoYunLD"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9528"
  animName: "AS_CH_IdleShow_PL_126"
  bank: "VO_ZhaoYunLD"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_126"
  stopEvent: "Stop_VO_ZhaoYunLD"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9529"
  animName: "AS_CH_Enter_PL_127"
  bank: "VO_HOKYase"
  playEvent: "Play_VO_AS_CH_Enter_PL_127"
  stopEvent: "Stop_VO_HOKYase"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9530"
  animName: "AS_CH_IdleShow_PL_127"
  bank: "VO_HOKYase"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_127"
  stopEvent: "Stop_VO_HOKYase"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9531"
  animName: "AS_CH_Enter_PL_128"
  bank: "VO_HOKAnqilaZTYX"
  playEvent: "Play_VO_AS_CH_Enter_PL_128"
  stopEvent: "Stop_VO_HOKAnqilaZTYX"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9532"
  animName: "AS_CH_IdleShow_PL_128"
  bank: "VO_HOKAnqilaZTYX"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_128"
  stopEvent: "Stop_VO_HOKAnqilaZTYX"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9533"
  animName: "AS_CH_Enter_PL_129"
  bank: "VO_SunShangXiangYJLQ"
  playEvent: "Play_VO_AS_CH_Enter_PL_129"
  stopEvent: "Stop_VO_SunShangXiangYJLQ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9534"
  animName: "AS_CH_IdleShow_PL_129"
  bank: "VO_SunShangXiangYJLQ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_129"
  stopEvent: "Stop_VO_SunShangXiangYJLQ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9535"
  animName: "AS_CH_Enter_PL_130"
  bank: "VO_HOKWukongQXSY"
  playEvent: "Play_VO_AS_CH_Enter_PL_130"
  stopEvent: "Stop_VO_HOKWukongQXSY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9536"
  animName: "AS_CH_IdleShow_PL_130"
  bank: "VO_HOKWukongQXSY"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_130"
  stopEvent: "Stop_VO_HOKWukongQXSY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9537"
  animName: "AS_CH_Enter_PL_131"
  bank: "VO_HOKCaiwenjiHCRY"
  playEvent: "Play_VO_AS_CH_Enter_PL_131"
  stopEvent: "Stop_VO_HOKCaiwenjiHCRY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9538"
  animName: "AS_CH_IdleShow_PL_131"
  bank: "VO_HOKCaiwenjiHCRY"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_131"
  stopEvent: "Stop_VO_HOKCaiwenjiHCRY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9539"
  animName: "AS_CH_Enter_PL_140"
  bank: "VO_CXSXiangliu"
  playEvent: "Play_VO_AS_CH_Enter_PL_140"
  stopEvent: "Stop_VO_CXSXiangliu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9540"
  animName: "AS_CH_IdleShow_PL_140"
  bank: "VO_CXSXiangliu"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_140"
  stopEvent: "Stop_VO_CXSXiangliu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9541"
  animName: "AS_CH_Enter_PL_141"
  bank: "VO_CXSChishui"
  playEvent: "Play_VO_AS_CH_Enter_PL_141"
  stopEvent: "Stop_VO_CXSChishui"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9542"
  animName: "AS_CH_IdleShow_PL_141"
  bank: "VO_CXSChishui"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_141"
  stopEvent: "Stop_VO_CXSChishui"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9543"
  animName: "AS_CH_Enter_PL_142"
  bank: "VO_CXSCangXuan"
  playEvent: "Play_VO_AS_CH_Enter_PL_142"
  stopEvent: "Stop_VO_CXSCangXuan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9544"
  animName: "AS_CH_IdleShow_PL_142"
  bank: "VO_CXSCangXuan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_142"
  stopEvent: "Stop_VO_CXSCangXuan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9545"
  animName: "AS_CH_Enter_PL_143"
  bank: "VO_CXSTushanjing"
  playEvent: "Play_VO_AS_CH_Enter_PL_143"
  stopEvent: "Stop_VO_CXSTushanjing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9546"
  animName: "AS_CH_IdleShow_PL_143"
  bank: "VO_CXSTushanjing"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_143"
  stopEvent: "Stop_VO_CXSTushanjing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9547"
  animName: "AS_CH_Enter_PL_144"
  bank: "VO_CXSXiaoyao"
  playEvent: "Play_VO_AS_CH_Enter_PL_144"
  stopEvent: "Stop_VO_CXSXiaoyao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9548"
  animName: "AS_CH_IdleShow_PL_144"
  bank: "VO_CXSXiaoyao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_144"
  stopEvent: "Stop_VO_CXSXiaoyao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9549"
  animName: "AS_CH_Enter_PL_152"
  bank: "VO_Rabbina"
  playEvent: "Play_VO_AS_CH_Enter_PL_152"
  stopEvent: "Stop_VO_Rabbina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9550"
  animName: "AS_CH_IdleShow_PL_152"
  bank: "VO_Rabbina"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_152"
  stopEvent: "Stop_VO_Rabbina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9551"
  animName: "AS_CH_Enter_PL_153"
  bank: "VO_Orphism"
  playEvent: "Play_VO_AS_CH_Enter_PL_153"
  stopEvent: "Stop_VO_Orphism"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9552"
  animName: "AS_CH_IdleShow_PL_153"
  bank: "VO_Orphism"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_153"
  stopEvent: "Stop_VO_Orphism"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9553"
  animName: "AS_CH_Enter_PL_154"
  bank: "VO_Cloud"
  playEvent: "Play_VO_AS_CH_Enter_PL_154"
  stopEvent: "Stop_VO_Cloud"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9554"
  animName: "AS_CH_IdleShow_PL_154"
  bank: "VO_Cloud"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_154"
  stopEvent: "Stop_VO_Cloud"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9555"
  animName: "AS_CH_Enter_PL_155"
  bank: "VO_TobyFrog"
  playEvent: "Play_VO_AS_CH_Enter_PL_155"
  stopEvent: "Stop_VO_TobyFrog"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9556"
  animName: "AS_CH_IdleShow_PL_155"
  bank: "VO_TobyFrog"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_155"
  stopEvent: "Stop_VO_TobyFrog"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9557"
  animName: "AS_CH_Enter_PL_156"
  bank: "VO_HOKHouyi"
  playEvent: "Play_VO_AS_CH_Enter_PL_156"
  stopEvent: "Stop_VO_HOKHouyi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9558"
  animName: "AS_CH_IdleShow_PL_156"
  bank: "VO_HOKHouyi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_156"
  stopEvent: "Stop_VO_HOKHouyi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9559"
  animName: "AS_CH_IdleShow_OG_018"
  bank: "SFX_Roxy"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_018"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_018"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9560"
  animName: "AS_CH_IdleShow_OG_019"
  bank: "SFX_Layla"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_019"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_019"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9561"
  animName: "AS_CH_IdleShow_OG_018"
  bank: "VO_Roxy"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_018"
  stopEvent: "Stop_VO_Roxy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9562"
  animName: "AS_CH_IdleShow_OG_019"
  bank: "VO_Layla"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_019"
  stopEvent: "Stop_VO_Layla"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9563"
  animName: "AS_CH_Enter_PL_145"
  bank: "SFX_Olin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_145"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_145"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9564"
  animName: "AS_CH_IdleShow_PL_145"
  bank: "SFX_Olin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_145"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_145"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9565"
  animName: "AS_CH_Enter_PL_138"
  bank: "SFX_Shaxili"
  playEvent: "Play_SFX_AS_CH_Enter_PL_138"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_138"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9566"
  animName: "AS_CH_IdleShow_PL_138"
  bank: "SFX_Shaxili"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_138"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_138"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9567"
  animName: "AS_CH_Enter_PL_139"
  bank: "SFX_Balan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_139"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_139"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9568"
  animName: "AS_CH_IdleShow_PL_139"
  bank: "SFX_Balan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_139"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_139"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9569"
  animName: "AS_CH_Enter_PL_145"
  bank: "VO_Olin"
  playEvent: "Play_VO_AS_CH_Enter_PL_145"
  stopEvent: "Stop_VO_Olin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9570"
  animName: "AS_CH_IdleShow_PL_145"
  bank: "VO_Olin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_145"
  stopEvent: "Stop_VO_Olin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9571"
  animName: "AS_CH_Enter_PL_138"
  bank: "VO_Shaxili"
  playEvent: "Play_VO_AS_CH_Enter_PL_138"
  stopEvent: "Stop_VO_Shaxili"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9572"
  animName: "AS_CH_IdleShow_PL_138"
  bank: "VO_Shaxili"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_138"
  stopEvent: "Stop_VO_Shaxili"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9573"
  animName: "AS_CH_Enter_PL_139"
  bank: "VO_Balan"
  playEvent: "Play_VO_AS_CH_Enter_PL_139"
  stopEvent: "Stop_VO_Balan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9574"
  animName: "AS_CH_IdleShow_PL_139"
  bank: "VO_Balan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_139"
  stopEvent: "Stop_VO_Balan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9575"
  animName: "AS_CH_Enter_PL_149"
  bank: "SFX_XiaoxinSleep"
  playEvent: "Play_SFX_AS_CH_Enter_PL_149"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_149"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9576"
  animName: "AS_CH_IdleShow_PL_149"
  bank: "SFX_XiaoxinSleep"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_149"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_149"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9577"
  animName: "AS_CH_Enter_PL_151"
  bank: "SFX_XiaoxinZWM"
  playEvent: "Play_SFX_AS_CH_Enter_PL_151"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_151"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9578"
  animName: "AS_CH_IdleShow_PL_151"
  bank: "SFX_XiaoxinZWM"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_151"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_151"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9579"
  animName: "AS_CH_Enter_PL_150"
  bank: "SFX_Zhengnan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_150"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_150"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9580"
  animName: "AS_CH_IdleShow_PL_150"
  bank: "SFX_Zhengnan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_150"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_150"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9581"
  animName: "AS_CH_Enter_PL_149"
  bank: "VO_XiaoxinSleep"
  playEvent: "Play_VO_AS_CH_Enter_PL_149"
  stopEvent: "Stop_VO_XiaoxinSleep"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9582"
  animName: "AS_CH_IdleShow_PL_149"
  bank: "VO_XiaoxinSleep"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_149"
  stopEvent: "Stop_VO_XiaoxinSleep"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9583"
  animName: "AS_CH_Enter_PL_151"
  bank: "VO_XiaoxinZWM"
  playEvent: "Play_VO_AS_CH_Enter_PL_151"
  stopEvent: "Stop_VO_XiaoxinZWM"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9584"
  animName: "AS_CH_IdleShow_PL_151"
  bank: "VO_XiaoxinZWM"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_151"
  stopEvent: "Stop_VO_XiaoxinZWM"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9585"
  animName: "AS_CH_Enter_PL_150"
  bank: "VO_Zhengnan"
  playEvent: "Play_VO_AS_CH_Enter_PL_150"
  stopEvent: "Stop_VO_Zhengnan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9586"
  animName: "AS_CH_IdleShow_PL_150"
  bank: "VO_Zhengnan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_150"
  stopEvent: "Stop_VO_Zhengnan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9587"
  animName: "AS_CH_Enter_PL_109"
  bank: "SFX_Oketra"
  playEvent: "Play_SFX_AS_CH_Enter_PL_109"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_109"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9588"
  animName: "AS_CH_IdleShow_PL_109"
  bank: "SFX_Oketra"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_109"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_109"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9589"
  animName: "AS_CH_Enter_PL_109"
  bank: "VO_Oketra"
  playEvent: "Play_VO_AS_CH_Enter_PL_109"
  stopEvent: "Stop_VO_Oketra"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9590"
  animName: "AS_CH_IdleShow_PL_109"
  bank: "VO_Oketra"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_109"
  stopEvent: "Stop_VO_Oketra"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9591"
  animName: "AS_CH_Enter_PL_164"
  bank: "SFX_Violet"
  playEvent: "Play_SFX_AS_CH_Enter_PL_164"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_164"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9592"
  animName: "AS_CH_IdleShow_PL_164"
  bank: "SFX_Violet"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_164"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_164"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9593"
  animName: "AS_CH_Enter_PL_164"
  bank: "VO_Violet"
  playEvent: "Play_VO_AS_CH_Enter_PL_164"
  stopEvent: "Stop_VO_Violet"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9594"
  animName: "AS_CH_IdleShow_PL_164"
  bank: "VO_Violet"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_164"
  stopEvent: "Stop_VO_Violet"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9595"
  animName: "AS_CH_Enter_PL_165"
  bank: "SFX_Louis"
  playEvent: "Play_SFX_AS_CH_Enter_PL_165"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_165"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9596"
  animName: "AS_CH_IdleShow_PL_165"
  bank: "SFX_Louis"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_165"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_165"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9597"
  animName: "AS_CH_Enter_PL_165"
  bank: "VO_Louis"
  playEvent: "Play_VO_AS_CH_Enter_PL_165"
  stopEvent: "Stop_VO_Louis"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9598"
  animName: "AS_CH_IdleShow_PL_165"
  bank: "VO_Louis"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_165"
  stopEvent: "Stop_VO_Louis"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9599"
  animName: "AS_CH_Enter_PL_166"
  bank: "SFX_Lily"
  playEvent: "Play_SFX_AS_CH_Enter_PL_166"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_166"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9600"
  animName: "AS_CH_IdleShow_PL_166"
  bank: "SFX_Lily"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_166"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_166"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9601"
  animName: "AS_CH_Enter_PL_166"
  bank: "VO_Lily"
  playEvent: "Play_VO_AS_CH_Enter_PL_166"
  stopEvent: "Stop_VO_Lily"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9602"
  animName: "AS_CH_IdleShow_PL_166"
  bank: "VO_Lily"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_166"
  stopEvent: "Stop_VO_Lily"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9603"
  animName: "AS_CH_Enter_PL_163"
  bank: "SFX_DanQing"
  playEvent: "Play_SFX_AS_CH_Enter_PL_163"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_163"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9604"
  animName: "AS_CH_IdleShow_PL_163"
  bank: "SFX_DanQing"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_163"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_163"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9605"
  animName: "AS_CH_Enter_PL_163"
  bank: "VO_DanQing"
  playEvent: "Play_VO_AS_CH_Enter_PL_163"
  stopEvent: "Stop_VO_DanQing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9606"
  animName: "AS_CH_IdleShow_PL_163"
  bank: "VO_DanQing"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_163"
  stopEvent: "Stop_VO_DanQing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9607"
  animName: "AS_CH_Enter_PL_157"
  bank: "SFX_HOKLibai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_157"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_157"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9608"
  animName: "AS_CH_IdleShow_PL_157"
  bank: "SFX_HOKLibai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_157"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_157"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9609"
  animName: "AS_CH_Enter_PL_157"
  bank: "VO_HOKLibai"
  playEvent: "Play_VO_AS_CH_Enter_PL_157"
  stopEvent: "Stop_VO_HOKLibai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9610"
  animName: "AS_CH_IdleShow_PL_157"
  bank: "VO_HOKLibai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_157"
  stopEvent: "Stop_VO_HOKLibai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9611"
  animName: "AS_CH_Enter_OG_022"
  bank: "SFX_Iris"
  playEvent: "Play_SFX_AS_CH_Enter_OG_022"
  stopEvent: "Stop_SFX_AS_CH_Enter_OG_022"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9612"
  animName: "AS_CH_IdleShow_OG_022"
  bank: "SFX_Iris"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_022"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_022"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9613"
  animName: "AS_CH_Enter_OG_022"
  bank: "VO_Iris"
  playEvent: "Play_VO_AS_CH_Enter_OG_022"
  stopEvent: "Stop_VO_Iris"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9614"
  animName: "AS_CH_IdleShow_OG_022"
  bank: "VO_Iris"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_022"
  stopEvent: "Stop_VO_Iris"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9615"
  animName: "AS_CH_Enter_OG_023"
  bank: "SFX_Silvio"
  playEvent: "Play_SFX_AS_CH_Enter_OG_023"
  stopEvent: "Stop_SFX_AS_CH_Enter_OG_023"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9616"
  animName: "AS_CH_IdleShow_OG_023"
  bank: "SFX_Silvio"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_023"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_023"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9617"
  animName: "AS_CH_Enter_OG_023"
  bank: "VO_Silvio"
  playEvent: "Play_VO_AS_CH_Enter_OG_023"
  stopEvent: "Stop_VO_Silvio"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9618"
  animName: "AS_CH_IdleShow_OG_023"
  bank: "VO_Silvio"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_023"
  stopEvent: "Stop_VO_Silvio"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9619"
  animName: "AS_CH_Enter_OG_024"
  bank: "SFX_ChangE"
  playEvent: "Play_SFX_AS_CH_Enter_OG_024"
  stopEvent: "Stop_SFX_AS_CH_Enter_OG_024"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9620"
  animName: "AS_CH_IdleShow_OG_024"
  bank: "SFX_ChangE"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_024"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_024"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9621"
  animName: "AS_CH_Enter_OG_024"
  bank: "VO_ChangE"
  playEvent: "Play_VO_AS_CH_Enter_OG_024"
  stopEvent: "Stop_VO_ChangE"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9622"
  animName: "AS_CH_IdleShow_OG_024"
  bank: "VO_ChangE"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_024"
  stopEvent: "Stop_VO_ChangE"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9623"
  animName: "AS_CH_Enter_PL_159"
  bank: "SFX_HOKYunying"
  playEvent: "Play_SFX_AS_CH_Enter_PL_159"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_159"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9624"
  animName: "AS_CH_IdleShow_PL_159"
  bank: "SFX_HOKYunying"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_159"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_159"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9625"
  animName: "AS_CH_Enter_PL_102"
  bank: "SFX_Lingxiao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_102"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_102"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9626"
  animName: "AS_CH_IdleShow_PL_102"
  bank: "SFX_Lingxiao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_102"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_102"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9627"
  animName: "AS_CH_Enter_PL_169"
  bank: "SFX_DanGui"
  playEvent: "Play_SFX_AS_CH_Enter_PL_169"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_169"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9628"
  animName: "AS_CH_IdleShow_PL_169"
  bank: "SFX_DanGui"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_169"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_169"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9629"
  animName: "AS_CH_Enter_PL_177"
  bank: "SFX_Roslyn"
  playEvent: "Play_SFX_AS_CH_Enter_PL_177"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_177"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9630"
  animName: "AS_CH_IdleShow_PL_177"
  bank: "SFX_Roslyn"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_177"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_177"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9631"
  animName: "AS_CH_Enter_PL_170"
  bank: "SFX_Jinger"
  playEvent: "Play_SFX_AS_CH_Enter_PL_170"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_170"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9632"
  animName: "AS_CH_IdleShow_PL_170"
  bank: "SFX_Jinger"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_170"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_170"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9633"
  animName: "AS_CH_Enter_PL_188"
  bank: "SFX_HOKGongsunli"
  playEvent: "Play_SFX_AS_CH_Enter_PL_188"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_188"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9634"
  animName: "AS_CH_IdleShow_PL_188"
  bank: "SFX_HOKGongsunli"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_188"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_188"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9635"
  animName: "AS_CH_Enter_PL_175"
  bank: "SFX_Grace"
  playEvent: "Play_SFX_AS_CH_Enter_PL_175"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_175"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9636"
  animName: "AS_CH_IdleShow_PL_175"
  bank: "SFX_Grace"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_175"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_175"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9637"
  animName: "AS_CH_Enter_PL_160"
  bank: "SFX_HOKWangzhaojunOXGS"
  playEvent: "Play_SFX_AS_CH_Enter_PL_160"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_160"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9638"
  animName: "AS_CH_IdleShow_PL_160"
  bank: "SFX_HOKWangzhaojunOXGS"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_160"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_160"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9639"
  animName: "AS_CH_Enter_PL_162"
  bank: "SFX_HOKGongsunliJYZX"
  playEvent: "Play_SFX_AS_CH_Enter_PL_162"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_162"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9640"
  animName: "AS_CH_IdleShow_PL_162"
  bank: "SFX_HOKGongsunliJYZX"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_162"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_162"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9641"
  animName: "AS_CH_Enter_PL_159"
  bank: "VO_HOKYunying"
  playEvent: "Play_VO_AS_CH_Enter_PL_159"
  stopEvent: "Stop_VO_HOKYunying"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9642"
  animName: "AS_CH_IdleShow_PL_159"
  bank: "VO_HOKYunying"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_159"
  stopEvent: "Stop_VO_HOKYunying"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9643"
  animName: "AS_CH_Enter_PL_102"
  bank: "VO_Lingxiao"
  playEvent: "Play_VO_AS_CH_Enter_PL_102"
  stopEvent: "Stop_VO_Lingxiao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9644"
  animName: "AS_CH_IdleShow_PL_102"
  bank: "VO_Lingxiao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_102"
  stopEvent: "Stop_VO_Lingxiao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9645"
  animName: "AS_CH_Enter_PL_169"
  bank: "VO_DanGui"
  playEvent: "Play_VO_AS_CH_Enter_PL_169"
  stopEvent: "Stop_VO_DanGui"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9646"
  animName: "AS_CH_IdleShow_PL_169"
  bank: "VO_DanGui"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_169"
  stopEvent: "Stop_VO_DanGui"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9647"
  animName: "AS_CH_Enter_PL_177"
  bank: "VO_Roslyn"
  playEvent: "Play_VO_AS_CH_Enter_PL_177"
  stopEvent: "Stop_VO_Roslyn"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9648"
  animName: "AS_CH_IdleShow_PL_177"
  bank: "VO_Roslyn"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_177"
  stopEvent: "Stop_VO_Roslyn"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9649"
  animName: "AS_CH_Enter_PL_170"
  bank: "VO_Jinger"
  playEvent: "Play_VO_AS_CH_Enter_PL_170"
  stopEvent: "Stop_VO_Jinger"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9650"
  animName: "AS_CH_IdleShow_PL_170"
  bank: "VO_Jinger"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_170"
  stopEvent: "Stop_VO_Jinger"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9651"
  animName: "AS_CH_Enter_PL_188"
  bank: "VO_HOKGongsunli"
  playEvent: "Play_VO_AS_CH_Enter_PL_188"
  stopEvent: "Stop_VO_HOKGongsunli"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9652"
  animName: "AS_CH_IdleShow_PL_188"
  bank: "VO_HOKGongsunli"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_188"
  stopEvent: "Stop_VO_HOKGongsunli"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9653"
  animName: "AS_CH_Enter_PL_175"
  bank: "VO_Grace"
  playEvent: "Play_VO_AS_CH_Enter_PL_175"
  stopEvent: "Stop_VO_Grace"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9654"
  animName: "AS_CH_IdleShow_PL_175"
  bank: "VO_Grace"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_175"
  stopEvent: "Stop_VO_Grace"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9655"
  animName: "AS_CH_Enter_PL_160"
  bank: "VO_HOKWangzhaojunOXGS"
  playEvent: "Play_VO_AS_CH_Enter_PL_160"
  stopEvent: "Stop_VO_HOKWangzhaojunOXGS"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9656"
  animName: "AS_CH_IdleShow_PL_160"
  bank: "VO_HOKWangzhaojunOXGS"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_160"
  stopEvent: "Stop_VO_HOKWangzhaojunOXGS"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9657"
  animName: "AS_CH_Enter_PL_162"
  bank: "VO_HOKGongsunliJYZX"
  playEvent: "Play_VO_AS_CH_Enter_PL_162"
  stopEvent: "Stop_VO_HOKGongsunliJYZX"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9658"
  animName: "AS_CH_IdleShow_PL_162"
  bank: "VO_HOKGongsunliJYZX"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_162"
  stopEvent: "Stop_VO_HOKGongsunliJYZX"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9659"
  animName: "AS_CH_Enter_PL_174"
  bank: "SFX_SweetBeanMY"
  playEvent: "Play_SFX_AS_CH_Enter_PL_174"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_174"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9660"
  animName: "AS_CH_IdleShow_PL_174"
  bank: "SFX_SweetBeanMY"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_174"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_174"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9661"
  animName: "AS_CH_Enter_PL_173"
  bank: "SFX_SweetBeanQE"
  playEvent: "Play_SFX_AS_CH_Enter_PL_173"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_173"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9662"
  animName: "AS_CH_IdleShow_PL_173"
  bank: "SFX_SweetBeanQE"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_173"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_173"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9663"
  animName: "AS_CH_Enter_PL_125"
  bank: "SFX_WHMBazhahei"
  playEvent: "Play_SFX_AS_CH_Enter_PL_125"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_125"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9664"
  animName: "AS_CH_IdleShow_PL_125"
  bank: "SFX_WHMBazhahei"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_125"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_125"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9665"
  animName: "AS_CH_Enter_PL_124"
  bank: "SFX_WHMWuhuangmao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_124"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_124"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9666"
  animName: "AS_CH_IdleShow_PL_124"
  bank: "SFX_WHMWuhuangmao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_124"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_124"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9667"
  animName: "AS_CH_Enter_PL_171"
  bank: "SFX_PandaGirl"
  playEvent: "Play_SFX_AS_CH_Enter_PL_171"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_171"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9668"
  animName: "AS_CH_IdleShow_PL_171"
  bank: "SFX_PandaGirl"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_171"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_171"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9669"
  animName: "AS_CH_Enter_PL_172"
  bank: "SFX_Dew"
  playEvent: "Play_SFX_AS_CH_Enter_PL_172"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_172"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9670"
  animName: "AS_CH_IdleShow_PL_172"
  bank: "SFX_Dew"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_172"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_172"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9671"
  animName: "AS_CH_Enter_PL_167"
  bank: "SFX_BLLYoule"
  playEvent: "Play_SFX_AS_CH_Enter_PL_167"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_167"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9672"
  animName: "AS_CH_IdleShow_PL_167"
  bank: "SFX_BLLYoule"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_167"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_167"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9673"
  animName: "AS_CH_Enter_PL_168"
  bank: "SFX_BLLXiaolan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_168"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_168"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9674"
  animName: "AS_CH_IdleShow_PL_168"
  bank: "SFX_BLLXiaolan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_168"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_168"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9675"
  animName: "AS_CH_Enter_PL_189"
  bank: "SFX_Nicolas"
  playEvent: "Play_SFX_AS_CH_Enter_PL_189"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_189"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9676"
  animName: "AS_CH_IdleShow_PL_189"
  bank: "SFX_Nicolas"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_189"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_189"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9677"
  animName: "AS_CH_Enter_PL_190"
  bank: "SFX_Whitney"
  playEvent: "Play_SFX_AS_CH_Enter_PL_190"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_190"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9678"
  animName: "AS_CH_IdleShow_PL_190"
  bank: "SFX_Whitney"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_190"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_190"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9679"
  animName: "AS_CH_Enter_PL_191"
  bank: "SFX_Baku"
  playEvent: "Play_SFX_AS_CH_Enter_PL_191"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_191"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9680"
  animName: "AS_CH_IdleShow_PL_191"
  bank: "SFX_Baku"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_191"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_191"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9681"
  animName: "AS_CH_IdleShow_OG_025"
  bank: "SFX_QingYun"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_025"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_025"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9682"
  animName: "AS_CH_IdleShow_OG_026"
  bank: "SFX_CollettiA"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_026"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_026"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9683"
  animName: "AS_CH_IdleShow_OG_027"
  bank: "SFX_CollettiB"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_027"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_027"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9684"
  animName: "AS_CH_Enter_PL_174"
  bank: "VO_SweetBeanMY"
  playEvent: "Play_VO_AS_CH_Enter_PL_174"
  stopEvent: "Stop_VO_SweetBeanMY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9685"
  animName: "AS_CH_IdleShow_PL_174"
  bank: "VO_SweetBeanMY"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_174"
  stopEvent: "Stop_VO_SweetBeanMY"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9686"
  animName: "AS_CH_Enter_PL_173"
  bank: "VO_SweetBeanQE"
  playEvent: "Play_VO_AS_CH_Enter_PL_173"
  stopEvent: "Stop_VO_SweetBeanQE"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9687"
  animName: "AS_CH_IdleShow_PL_173"
  bank: "VO_SweetBeanQE"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_173"
  stopEvent: "Stop_VO_SweetBeanQE"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9688"
  animName: "AS_CH_Enter_PL_125"
  bank: "VO_WHMBazhahei"
  playEvent: "Play_VO_AS_CH_Enter_PL_125"
  stopEvent: "Stop_VO_WHMBazhahei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9689"
  animName: "AS_CH_IdleShow_PL_125"
  bank: "VO_WHMBazhahei"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_125"
  stopEvent: "Stop_VO_WHMBazhahei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9690"
  animName: "AS_CH_Enter_PL_124"
  bank: "VO_WHMWuhuangmao"
  playEvent: "Play_VO_AS_CH_Enter_PL_124"
  stopEvent: "Stop_VO_WHMWuhuangmao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9691"
  animName: "AS_CH_IdleShow_PL_124"
  bank: "VO_WHMWuhuangmao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_124"
  stopEvent: "Stop_VO_WHMWuhuangmao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9692"
  animName: "AS_CH_Enter_PL_171"
  bank: "VO_PandaGirl"
  playEvent: "Play_VO_AS_CH_Enter_PL_171"
  stopEvent: "Stop_VO_PandaGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9693"
  animName: "AS_CH_IdleShow_PL_171"
  bank: "VO_PandaGirl"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_171"
  stopEvent: "Stop_VO_PandaGirl"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9694"
  animName: "AS_CH_Enter_PL_172"
  bank: "VO_Dew"
  playEvent: "Play_VO_AS_CH_Enter_PL_172"
  stopEvent: "Stop_VO_Dew"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9695"
  animName: "AS_CH_IdleShow_PL_172"
  bank: "VO_Dew"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_172"
  stopEvent: "Stop_VO_Dew"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9696"
  animName: "AS_CH_Enter_PL_167"
  bank: "VO_BLLYoule"
  playEvent: "Play_VO_AS_CH_Enter_PL_167"
  stopEvent: "Stop_VO_BLLYoule"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9697"
  animName: "AS_CH_IdleShow_PL_167"
  bank: "VO_BLLYoule"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_167"
  stopEvent: "Stop_VO_BLLYoule"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9698"
  animName: "AS_CH_Enter_PL_168"
  bank: "VO_BLLXiaolan"
  playEvent: "Play_VO_AS_CH_Enter_PL_168"
  stopEvent: "Stop_VO_BLLXiaolan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9699"
  animName: "AS_CH_IdleShow_PL_168"
  bank: "VO_BLLXiaolan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_168"
  stopEvent: "Stop_VO_BLLXiaolan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9700"
  animName: "AS_CH_Enter_PL_189"
  bank: "VO_Nicolas"
  playEvent: "Play_VO_AS_CH_Enter_PL_189"
  stopEvent: "Stop_VO_Nicolas"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9701"
  animName: "AS_CH_IdleShow_PL_189"
  bank: "VO_Nicolas"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_189"
  stopEvent: "Stop_VO_Nicolas"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9702"
  animName: "AS_CH_Enter_PL_190"
  bank: "VO_Whitney"
  playEvent: "Play_VO_AS_CH_Enter_PL_190"
  stopEvent: "Stop_VO_Whitney"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9703"
  animName: "AS_CH_IdleShow_PL_190"
  bank: "VO_Whitney"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_190"
  stopEvent: "Stop_VO_Whitney"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9704"
  animName: "AS_CH_Enter_PL_191"
  bank: "VO_Baku"
  playEvent: "Play_VO_AS_CH_Enter_PL_191"
  stopEvent: "Stop_VO_Baku"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9705"
  animName: "AS_CH_IdleShow_PL_191"
  bank: "VO_Baku"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_191"
  stopEvent: "Stop_VO_Baku"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9706"
  animName: "AS_CH_IdleShow_OG_025"
  bank: "VO_QingYun"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_025"
  stopEvent: "Stop_VO_QingYun"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9707"
  animName: "AS_CH_IdleShow_OG_026"
  bank: "VO_CollettiA"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_026"
  stopEvent: "Stop_VO_CollettiA"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9708"
  animName: "AS_CH_IdleShow_OG_027"
  bank: "VO_CollettiB"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_027"
  stopEvent: "Stop_VO_CollettiB"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9709"
  animName: "AS_CH_Enter_PL_186"
  bank: "SFX_HOKWangZhaoJun"
  playEvent: "Play_SFX_AS_CH_Enter_PL_186"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_186"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9710"
  animName: "AS_CH_IdleShow_PL_186"
  bank: "SFX_HOKWangZhaoJun"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_186"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_186"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9711"
  animName: "AS_CH_Enter_PL_186"
  bank: "VO_HOKWangZhaoJun"
  playEvent: "Play_VO_AS_CH_Enter_PL_186"
  stopEvent: "Stop_VO_HOKWangZhaoJun"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9712"
  animName: "AS_CH_IdleShow_PL_186"
  bank: "VO_HOKWangZhaoJun"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_186"
  stopEvent: "Stop_VO_HOKWangZhaoJun"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9713"
  animName: "AS_CH_Enter_PL_222"
  bank: "SFX_HOKDaJi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_222"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_222"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9714"
  animName: "AS_CH_IdleShow_PL_222"
  bank: "SFX_HOKDaJi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_222"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_222"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9715"
  animName: "AS_CH_Enter_PL_222"
  bank: "VO_HOKDaJi"
  playEvent: "Play_VO_AS_CH_Enter_PL_222"
  stopEvent: "Stop_VO_HOKDaJi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9716"
  animName: "AS_CH_IdleShow_PL_222"
  bank: "VO_HOKDaJi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_222"
  stopEvent: "Stop_VO_HOKDaJi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9717"
  animName: "AS_CH_Enter_PL_192"
  bank: "SFX_Aliya"
  playEvent: "Play_SFX_AS_CH_Enter_PL_192"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_192"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9718"
  animName: "AS_CH_IdleShow_PL_192"
  bank: "SFX_Aliya"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_192"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_192"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9719"
  animName: "AS_CH_Enter_PL_193"
  bank: "SFX_Cabaza"
  playEvent: "Play_SFX_AS_CH_Enter_PL_193"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_193"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9720"
  animName: "AS_CH_IdleShow_PL_193"
  bank: "SFX_Cabaza"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_193"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_193"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9721"
  animName: "AS_CH_Enter_PL_196"
  bank: "SFX_Elowen"
  playEvent: "Play_SFX_AS_CH_Enter_PL_196"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_196"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9722"
  animName: "AS_CH_IdleShow_PL_196"
  bank: "SFX_Elowen"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_196"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_196"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9723"
  animName: "AS_CH_Enter_PL_224"
  bank: "SFX_HOKMaKeBoLuo"
  playEvent: "Play_SFX_AS_CH_Enter_PL_224"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_224"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9724"
  animName: "AS_CH_IdleShow_PL_224"
  bank: "SFX_HOKMaKeBoLuo"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_224"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_224"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9725"
  animName: "AS_CH_Enter_PL_194"
  bank: "SFX_Phantom"
  playEvent: "Play_SFX_AS_CH_Enter_PL_194"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_194"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9726"
  animName: "AS_CH_IdleShow_PL_194"
  bank: "SFX_Phantom"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_194"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_194"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9727"
  animName: "AS_CH_Enter_PL_183"
  bank: "SFX_HOKDiRenJieCSKZS"
  playEvent: "Play_SFX_AS_CH_Enter_PL_183"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_183"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9728"
  animName: "AS_CH_IdleShow_PL_183"
  bank: "SFX_HOKDiRenJieCSKZS"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_183"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_183"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9729"
  animName: "AS_CH_IdleShow_OG_028"
  bank: "SFX_Thorin"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_028"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_028"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9730"
  animName: "AS_CH_IdleShow_OG_029"
  bank: "SFX_Ymir"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_029"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_029"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9731"
  animName: "AS_CH_Enter_PL_192"
  bank: "VO_Aliya"
  playEvent: "Play_VO_AS_CH_Enter_PL_192"
  stopEvent: "Stop_VO_Aliya"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9732"
  animName: "AS_CH_IdleShow_PL_192"
  bank: "VO_Aliya"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_192"
  stopEvent: "Stop_VO_Aliya"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9733"
  animName: "AS_CH_Enter_PL_193"
  bank: "VO_Cabaza"
  playEvent: "Play_VO_AS_CH_Enter_PL_193"
  stopEvent: "Stop_VO_Cabaza"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9734"
  animName: "AS_CH_IdleShow_PL_193"
  bank: "VO_Cabaza"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_193"
  stopEvent: "Stop_VO_Cabaza"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9735"
  animName: "AS_CH_Enter_PL_196"
  bank: "VO_Elowen"
  playEvent: "Play_VO_AS_CH_Enter_PL_196"
  stopEvent: "Stop_VO_Elowen"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9736"
  animName: "AS_CH_IdleShow_PL_196"
  bank: "VO_Elowen"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_196"
  stopEvent: "Stop_VO_Elowen"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9737"
  animName: "AS_CH_Enter_PL_224"
  bank: "VO_HOKMaKeBoLuo"
  playEvent: "Play_VO_AS_CH_Enter_PL_224"
  stopEvent: "Stop_VO_HOKMaKeBoLuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9738"
  animName: "AS_CH_IdleShow_PL_224"
  bank: "VO_HOKMaKeBoLuo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_224"
  stopEvent: "Stop_VO_HOKMaKeBoLuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9739"
  animName: "AS_CH_Enter_PL_194"
  bank: "VO_Phantom"
  playEvent: "Play_VO_AS_CH_Enter_PL_194"
  stopEvent: "Stop_VO_Phantom"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9740"
  animName: "AS_CH_IdleShow_PL_194"
  bank: "VO_Phantom"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_194"
  stopEvent: "Stop_VO_Phantom"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9741"
  animName: "AS_CH_Enter_PL_183"
  bank: "VO_HOKDiRenJieCSKZS"
  playEvent: "Play_VO_AS_CH_Enter_PL_183"
  stopEvent: "Stop_VO_HOKDiRenJieCSKZS"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9742"
  animName: "AS_CH_IdleShow_PL_183"
  bank: "VO_HOKDiRenJieCSKZS"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_183"
  stopEvent: "Stop_VO_HOKDiRenJieCSKZS"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9743"
  animName: "AS_CH_IdleShow_OG_028"
  bank: "VO_Thorin"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_028"
  stopEvent: "Stop_VO_Thorin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9744"
  animName: "AS_CH_IdleShow_OG_029"
  bank: "VO_Ymir"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_029"
  stopEvent: "Stop_VO_Ymir"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9745"
  animName: "AS_CH_Enter_PL_158"
  bank: "SFX_HOKZhongKui"
  playEvent: "Play_SFX_AS_CH_Enter_PL_158"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_158"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9746"
  animName: "AS_CH_IdleShow_PL_158"
  bank: "SFX_HOKZhongKui"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_158"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_158"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9747"
  animName: "AS_CH_Enter_PL_201"
  bank: "SFX_KXCRKaixin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_201"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_201"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9748"
  animName: "AS_CH_IdleShow_PL_201"
  bank: "SFX_KXCRKaixin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_201"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_201"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9749"
  animName: "AS_CH_Enter_PL_202"
  bank: "SFX_KXCRTianxin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_202"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_202"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9750"
  animName: "AS_CH_IdleShow_PL_202"
  bank: "SFX_KXCRTianxin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_202"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_202"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9751"
  animName: "AS_CH_Enter_PL_203"
  bank: "SFX_KXCRXiaoxin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_203"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_203"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9752"
  animName: "AS_CH_IdleShow_PL_203"
  bank: "SFX_KXCRXiaoxin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_203"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_203"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9753"
  animName: "AS_CH_Enter_PL_225"
  bank: "SFX_HOKGongBenWuZang"
  playEvent: "Play_SFX_AS_CH_Enter_PL_225"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_225"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9754"
  animName: "AS_CH_IdleShow_PL_225"
  bank: "SFX_HOKGongBenWuZang"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_225"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_225"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9755"
  animName: "AS_CH_Enter_PL_227"
  bank: "SFX_HOKZhangLiangBFHJ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_227"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_227"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9756"
  animName: "AS_CH_IdleShow_PL_227"
  bank: "SFX_HOKZhangLiangBFHJ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_227"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_227"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9757"
  animName: "AS_CH_Enter_PL_195"
  bank: "SFX_Jardim"
  playEvent: "Play_SFX_AS_CH_Enter_PL_195"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_195"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9758"
  animName: "AS_CH_IdleShow_PL_195"
  bank: "SFX_Jardim"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_195"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_195"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9759"
  animName: "AS_CH_IdleShow_OG_031"
  bank: "SFX_RedVelvet"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_031"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_031"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9760"
  animName: "AS_CH_IdleShow_OG_032"
  bank: "SFX_Coco"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_032"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_032"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9761"
  animName: "AS_CH_Enter_PL_211"
  bank: "SFX_ShuYanFei"
  playEvent: "Play_SFX_AS_CH_Enter_PL_211"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_211"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9762"
  animName: "AS_CH_IdleShow_PL_211"
  bank: "SFX_ShuYanFei"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_211"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_211"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9763"
  animName: "AS_CH_Enter_PL_212"
  bank: "SFX_Anna"
  playEvent: "Play_SFX_AS_CH_Enter_PL_212"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_212"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9764"
  animName: "AS_CH_IdleShow_PL_212"
  bank: "SFX_Anna"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_212"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_212"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9765"
  animName: "AS_CH_Enter_PL_213"
  bank: "SFX_QinFeng"
  playEvent: "Play_SFX_AS_CH_Enter_PL_213"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_213"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9766"
  animName: "AS_CH_IdleShow_PL_213"
  bank: "SFX_QinFeng"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_213"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_213"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9767"
  animName: "AS_CH_Enter_PL_197"
  bank: "SFX_TXBBDingDing"
  playEvent: "Play_SFX_AS_CH_Enter_PL_197"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_197"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9768"
  animName: "AS_CH_IdleShow_PL_197"
  bank: "SFX_TXBBDingDing"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_197"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_197"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9769"
  animName: "AS_CH_Enter_PL_198"
  bank: "SFX_TXBBDiXi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_198"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_198"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9770"
  animName: "AS_CH_IdleShow_PL_198"
  bank: "SFX_TXBBDiXi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_198"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_198"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9771"
  animName: "AS_CH_Enter_PL_199"
  bank: "SFX_TXBBLala"
  playEvent: "Play_SFX_AS_CH_Enter_PL_199"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_199"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9772"
  animName: "AS_CH_IdleShow_PL_199"
  bank: "SFX_TXBBLala"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_199"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_199"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9773"
  animName: "AS_CH_Enter_PL_200"
  bank: "SFX_TXBBXiaoBo"
  playEvent: "Play_SFX_AS_CH_Enter_PL_200"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_200"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9774"
  animName: "AS_CH_IdleShow_PL_200"
  bank: "SFX_TXBBXiaoBo"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_200"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_200"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9775"
  animName: "AS_CH_Enter_PL_178"
  bank: "SFX_TYLuofulai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_178"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_178"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9776"
  animName: "AS_CH_IdleShow_PL_178"
  bank: "SFX_TYLuofulai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_178"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_178"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9777"
  animName: "AS_CH_Enter_PL_215"
  bank: "SFX_Ina"
  playEvent: "Play_SFX_AS_CH_Enter_PL_215"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_215"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9778"
  animName: "AS_CH_IdleShow_PL_215"
  bank: "SFX_Ina"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_215"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_215"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9779"
  animName: "AS_CH_Enter_PL_224"
  bank: "SFX_HOKMaKeBoLuo"
  playEvent: "Play_SFX_AS_CH_Enter_PL_224"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_224"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9780"
  animName: "AS_CH_IdleShow_PL_224"
  bank: "SFX_HOKMaKeBoLuo"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_224"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_224"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9781"
  animName: "AS_CH_Enter_PL_214"
  bank: "SFX_FuFu"
  playEvent: "Play_SFX_AS_CH_Enter_PL_214"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_214"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9782"
  animName: "AS_CH_IdleShow_PL_214"
  bank: "SFX_FuFu"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_214"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_214"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9783"
  animName: "AS_CH_Enter_PL_208"
  bank: "SFX_KNKeNan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_208"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_208"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9784"
  animName: "AS_CH_IdleShow_PL_208"
  bank: "SFX_KNKeNan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_208"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_208"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9785"
  animName: "AS_CH_Enter_PL_209"
  bank: "SFX_KNXiaoLan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_209"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_209"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9786"
  animName: "AS_CH_IdleShow_PL_209"
  bank: "SFX_KNXiaoLan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_209"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_209"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9787"
  animName: "AS_CH_Enter_PL_210"
  bank: "SFX_KNHuiYuanAi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_210"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_210"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9788"
  animName: "AS_CH_IdleShow_PL_210"
  bank: "SFX_KNHuiYuanAi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_210"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_210"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9789"
  animName: "AS_CH_Enter_PL_216"
  bank: "SFX_KNSherlock"
  playEvent: "Play_SFX_AS_CH_Enter_PL_216"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_216"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9790"
  animName: "AS_CH_IdleShow_PL_216"
  bank: "SFX_KNSherlock"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_216"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_216"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9791"
  animName: "AS_CH_Enter_PL_158"
  bank: "VO_HOKZhongKui"
  playEvent: "Play_VO_AS_CH_Enter_PL_158"
  stopEvent: "Stop_VO_HOKZhongKui"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9792"
  animName: "AS_CH_IdleShow_PL_158"
  bank: "VO_HOKZhongKui"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_158"
  stopEvent: "Stop_VO_HOKZhongKui"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9793"
  animName: "AS_CH_Enter_PL_201"
  bank: "VO_KXCRKaixin"
  playEvent: "Play_VO_AS_CH_Enter_PL_201"
  stopEvent: "Stop_VO_KXCRKaixin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9794"
  animName: "AS_CH_IdleShow_PL_201"
  bank: "VO_KXCRKaixin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_201"
  stopEvent: "Stop_VO_KXCRKaixin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9795"
  animName: "AS_CH_Enter_PL_202"
  bank: "VO_KXCRTianxin"
  playEvent: "Play_VO_AS_CH_Enter_PL_202"
  stopEvent: "Stop_VO_KXCRTianxin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9796"
  animName: "AS_CH_IdleShow_PL_202"
  bank: "VO_KXCRTianxin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_202"
  stopEvent: "Stop_VO_KXCRTianxin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9797"
  animName: "AS_CH_Enter_PL_203"
  bank: "VO_KXCRXiaoxin"
  playEvent: "Play_VO_AS_CH_Enter_PL_203"
  stopEvent: "Stop_VO_KXCRXiaoxin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9798"
  animName: "AS_CH_IdleShow_PL_203"
  bank: "VO_KXCRXiaoxin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_203"
  stopEvent: "Stop_VO_KXCRXiaoxin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9799"
  animName: "AS_CH_Enter_PL_225"
  bank: "VO_HOKGongBenWuZang"
  playEvent: "Play_VO_AS_CH_Enter_PL_225"
  stopEvent: "Stop_VO_HOKGongBenWuZang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9800"
  animName: "AS_CH_IdleShow_PL_225"
  bank: "VO_HOKGongBenWuZang"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_225"
  stopEvent: "Stop_VO_HOKGongBenWuZang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9801"
  animName: "AS_CH_Enter_PL_227"
  bank: "VO_HOKZhangLiangBFHJ"
  playEvent: "Play_VO_AS_CH_Enter_PL_227"
  stopEvent: "Stop_VO_HOKZhangLiangBFHJ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9802"
  animName: "AS_CH_IdleShow_PL_227"
  bank: "VO_HOKZhangLiangBFHJ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_227"
  stopEvent: "Stop_VO_HOKZhangLiangBFHJ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9803"
  animName: "AS_CH_Enter_PL_195"
  bank: "VO_Jardim"
  playEvent: "Play_VO_AS_CH_Enter_PL_195"
  stopEvent: "Stop_VO_Jardim"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9804"
  animName: "AS_CH_IdleShow_PL_195"
  bank: "VO_Jardim"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_195"
  stopEvent: "Stop_VO_Jardim"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9805"
  animName: "AS_CH_IdleShow_OG_031"
  bank: "VO_RedVelvet"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_031"
  stopEvent: "Stop_VO_RedVelvet"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9806"
  animName: "AS_CH_IdleShow_OG_032"
  bank: "VO_Coco"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_032"
  stopEvent: "Stop_VO_Coco"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9807"
  animName: "AS_CH_Enter_PL_211"
  bank: "VO_ShuYanFei"
  playEvent: "Play_VO_AS_CH_Enter_PL_211"
  stopEvent: "Stop_VO_ShuYanFei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9808"
  animName: "AS_CH_IdleShow_PL_211"
  bank: "VO_ShuYanFei"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_211"
  stopEvent: "Stop_VO_ShuYanFei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9809"
  animName: "AS_CH_Enter_PL_212"
  bank: "VO_Anna"
  playEvent: "Play_VO_AS_CH_Enter_PL_212"
  stopEvent: "Stop_VO_Anna"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9810"
  animName: "AS_CH_IdleShow_PL_212"
  bank: "VO_Anna"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_212"
  stopEvent: "Stop_VO_Anna"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9811"
  animName: "AS_CH_Enter_PL_213"
  bank: "VO_QinFeng"
  playEvent: "Play_VO_AS_CH_Enter_PL_213"
  stopEvent: "Stop_VO_QinFeng"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9812"
  animName: "AS_CH_IdleShow_PL_213"
  bank: "VO_QinFeng"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_213"
  stopEvent: "Stop_VO_QinFeng"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9813"
  animName: "AS_CH_Enter_PL_197"
  bank: "VO_TXBBDingDing"
  playEvent: "Play_VO_AS_CH_Enter_PL_197"
  stopEvent: "Stop_VO_TXBBDingDing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9814"
  animName: "AS_CH_IdleShow_PL_197"
  bank: "VO_TXBBDingDing"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_197"
  stopEvent: "Stop_VO_TXBBDingDing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9815"
  animName: "AS_CH_Enter_PL_198"
  bank: "VO_TXBBDiXi"
  playEvent: "Play_VO_AS_CH_Enter_PL_198"
  stopEvent: "Stop_VO_TXBBDiXi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9816"
  animName: "AS_CH_IdleShow_PL_198"
  bank: "VO_TXBBDiXi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_198"
  stopEvent: "Stop_VO_TXBBDiXi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9817"
  animName: "AS_CH_Enter_PL_199"
  bank: "VO_TXBBLala"
  playEvent: "Play_VO_AS_CH_Enter_PL_199"
  stopEvent: "Stop_VO_TXBBLala"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9818"
  animName: "AS_CH_IdleShow_PL_199"
  bank: "VO_TXBBLala"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_199"
  stopEvent: "Stop_VO_TXBBLala"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9819"
  animName: "AS_CH_Enter_PL_200"
  bank: "VO_TXBBXiaoBo"
  playEvent: "Play_VO_AS_CH_Enter_PL_200"
  stopEvent: "Stop_VO_TXBBXiaoBo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9820"
  animName: "AS_CH_IdleShow_PL_200"
  bank: "VO_TXBBXiaoBo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_200"
  stopEvent: "Stop_VO_TXBBXiaoBo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9821"
  animName: "AS_CH_Enter_PL_178"
  bank: "VO_TYLuofulai"
  playEvent: "Play_VO_AS_CH_Enter_PL_178"
  stopEvent: "Stop_VO_TYLuofulai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9822"
  animName: "AS_CH_IdleShow_PL_178"
  bank: "VO_TYLuofulai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_178"
  stopEvent: "Stop_VO_TYLuofulai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9823"
  animName: "AS_CH_Enter_PL_215"
  bank: "VO_Ina"
  playEvent: "Play_VO_AS_CH_Enter_PL_215"
  stopEvent: "Stop_VO_Ina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9824"
  animName: "AS_CH_IdleShow_PL_215"
  bank: "VO_Ina"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_215"
  stopEvent: "Stop_VO_Ina"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9825"
  animName: "AS_CH_Enter_PL_224"
  bank: "VO_HOKMaKeBoLuo"
  playEvent: "Play_VO_AS_CH_Enter_PL_224"
  stopEvent: "Stop_VO_HOKMaKeBoLuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9826"
  animName: "AS_CH_IdleShow_PL_224"
  bank: "VO_HOKMaKeBoLuo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_224"
  stopEvent: "Stop_VO_HOKMaKeBoLuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9827"
  animName: "AS_CH_Enter_PL_214"
  bank: "VO_FuFu"
  playEvent: "Play_VO_AS_CH_Enter_PL_214"
  stopEvent: "Stop_VO_FuFu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9828"
  animName: "AS_CH_IdleShow_PL_214"
  bank: "VO_FuFu"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_214"
  stopEvent: "Stop_VO_FuFu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9829"
  animName: "AS_CH_Enter_PL_208"
  bank: "VO_KNKeNanV2"
  playEvent: "Play_VO_AS_CH_Enter_PL_208"
  stopEvent: "Stop_VO_KNKeNan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9830"
  animName: "AS_CH_IdleShow_PL_208"
  bank: "VO_KNKeNanV2"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_208"
  stopEvent: "Stop_VO_KNKeNan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9831"
  animName: "AS_CH_Enter_PL_209"
  bank: "VO_KNXiaoLanV2"
  playEvent: "Play_VO_AS_CH_Enter_PL_209"
  stopEvent: "Stop_VO_KNXiaoLan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9832"
  animName: "AS_CH_IdleShow_PL_209"
  bank: "VO_KNXiaoLanV2"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_209"
  stopEvent: "Stop_VO_KNXiaoLan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9833"
  animName: "AS_CH_Enter_PL_210"
  bank: "VO_KNHuiYuanAiV2"
  playEvent: "Play_VO_AS_CH_Enter_PL_210"
  stopEvent: "Stop_VO_KNHuiYuanAi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9834"
  animName: "AS_CH_IdleShow_PL_210"
  bank: "VO_KNHuiYuanAiV2"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_210"
  stopEvent: "Stop_VO_KNHuiYuanAi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9835"
  animName: "AS_CH_Enter_PL_216"
  bank: "VO_KNSherlock"
  playEvent: "Play_VO_AS_CH_Enter_PL_216"
  stopEvent: "Stop_VO_KNSherlock"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9836"
  animName: "AS_CH_IdleShow_PL_216"
  bank: "VO_KNSherlock"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_216"
  stopEvent: "Stop_VO_KNSherlock"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9837"
  animName: "AS_CH_Enter_PL_187"
  bank: "SFX_HOKMozi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_187"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_187"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9838"
  animName: "AS_CH_IdleShow_PL_187"
  bank: "SFX_HOKMozi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_187"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_187"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9839"
  animName: "AS_CH_Enter_PL_187"
  bank: "VO_HOKMozi"
  playEvent: "Play_VO_AS_CH_Enter_PL_187"
  stopEvent: "Stop_VO_HOKMozi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9840"
  animName: "AS_CH_IdleShow_PL_187"
  bank: "VO_HOKMozi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_187"
  stopEvent: "Stop_VO_HOKMozi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9841"
  animName: "AS_CH_Enter_PL_226"
  bank: "SFX_HOKXiaHouDunCFPL"
  playEvent: "Play_SFX_AS_CH_Enter_PL_226"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_226"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9842"
  animName: "AS_CH_IdleShow_PL_226"
  bank: "SFX_HOKXiaHouDunCFPL"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_226"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_226"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9843"
  animName: "AS_CH_Enter_PL_226"
  bank: "VO_HOKXiaHouDunCFPL"
  playEvent: "Play_VO_AS_CH_Enter_PL_226"
  stopEvent: "Stop_VO_HOKXiaHouDunCFPL"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9844"
  animName: "AS_CH_IdleShow_PL_226"
  bank: "VO_HOKXiaHouDunCFPL"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_226"
  stopEvent: "Stop_VO_HOKXiaHouDunCFPL"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9845"
  animName: "AS_CH_Enter_PL_230"
  bank: "SFX_HOKDiaoChan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_230"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_230"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9846"
  animName: "AS_CH_IdleShow_PL_230"
  bank: "SFX_HOKDiaoChan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_230"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_230"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9847"
  animName: "AS_CH_Enter_PL_230"
  bank: "VO_HOKDiaoChan"
  playEvent: "Play_VO_AS_CH_Enter_PL_230"
  stopEvent: "Stop_VO_HOKDiaoChan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9848"
  animName: "AS_CH_IdleShow_PL_230"
  bank: "VO_HOKDiaoChan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_230"
  stopEvent: "Stop_VO_HOKDiaoChan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9849"
  animName: "AS_CH_IdleShow_OG_030"
  bank: "SFX_Neve"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_030"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_030"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9850"
  animName: "AS_CH_IdleShow_OG_030"
  bank: "VO_Neve"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_030"
  stopEvent: "Stop_VO_Neve"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9851"
  animName: "AS_CH_Enter_PL_206"
  bank: "SFX_Eira"
  playEvent: "Play_SFX_AS_CH_Enter_PL_206"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_206"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9852"
  animName: "AS_CH_IdleShow_PL_206"
  bank: "SFX_Eira"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_206"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_206"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9853"
  animName: "AS_CH_Enter_PL_207"
  bank: "SFX_Hiver"
  playEvent: "Play_SFX_AS_CH_Enter_PL_207"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_207"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9854"
  animName: "AS_CH_IdleShow_PL_207"
  bank: "SFX_Hiver"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_207"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_207"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9855"
  animName: "AS_CH_Enter_PL_206"
  bank: "VO_Eira"
  playEvent: "Play_VO_AS_CH_Enter_PL_206"
  stopEvent: "Stop_VO_Eira"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9856"
  animName: "AS_CH_IdleShow_PL_206"
  bank: "VO_Eira"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_206"
  stopEvent: "Stop_VO_Eira"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9857"
  animName: "AS_CH_Enter_PL_207"
  bank: "VO_Hiver"
  playEvent: "Play_VO_AS_CH_Enter_PL_207"
  stopEvent: "Stop_VO_Hiver"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9858"
  animName: "AS_CH_IdleShow_PL_207"
  bank: "VO_Hiver"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_207"
  stopEvent: "Stop_VO_Hiver"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9859"
  animName: "AS_CH_Enter_PL_223"
  bank: "SFX_HOKDongFangYao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_223"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_223"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9860"
  animName: "AS_CH_IdleShow_PL_223"
  bank: "SFX_HOKDongFangYao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_223"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_223"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9861"
  animName: "AS_CH_Enter_PL_223"
  bank: "VO_HOKDongFangYao"
  playEvent: "Play_VO_AS_CH_Enter_PL_223"
  stopEvent: "Stop_VO_HOKDongFangYao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9862"
  animName: "AS_CH_IdleShow_PL_223"
  bank: "VO_HOKDongFangYao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_223"
  stopEvent: "Stop_VO_HOKDongFangYao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9863"
  animName: "AS_CH_Enter_PL_242"
  bank: "SFX_TXBBDingDing"
  playEvent: "Play_SFX_AS_CH_Enter_PL_197"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_197"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9864"
  animName: "AS_CH_IdleShow_PL_242"
  bank: "SFX_TXBBDingDing"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_197"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_197"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9865"
  animName: "AS_CH_Enter_PL_243"
  bank: "SFX_TXBBDiXi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_198"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_198"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9866"
  animName: "AS_CH_IdleShow_PL_243"
  bank: "SFX_TXBBDiXi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_198"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_198"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9867"
  animName: "AS_CH_Enter_PL_244"
  bank: "SFX_TXBBLala"
  playEvent: "Play_SFX_AS_CH_Enter_PL_199"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_199"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9868"
  animName: "AS_CH_IdleShow_PL_244"
  bank: "SFX_TXBBLala"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_199"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_199"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9869"
  animName: "AS_CH_Enter_PL_245"
  bank: "SFX_TXBBXiaoBo"
  playEvent: "Play_SFX_AS_CH_Enter_PL_200"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_200"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9870"
  animName: "AS_CH_IdleShow_PL_245"
  bank: "SFX_TXBBXiaoBo"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_200"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_200"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9871"
  animName: "AS_CH_Enter_PL_242"
  bank: "VO_TXBBDingDing"
  playEvent: "Play_VO_AS_CH_Enter_PL_197"
  stopEvent: "Stop_VO_TXBBDingDing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9872"
  animName: "AS_CH_IdleShow_PL_242"
  bank: "VO_TXBBDingDing"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_197"
  stopEvent: "Stop_VO_TXBBDingDing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9873"
  animName: "AS_CH_Enter_PL_243"
  bank: "VO_TXBBDiXi"
  playEvent: "Play_VO_AS_CH_Enter_PL_198"
  stopEvent: "Stop_VO_TXBBDiXi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9874"
  animName: "AS_CH_IdleShow_PL_243"
  bank: "VO_TXBBDiXi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_198"
  stopEvent: "Stop_VO_TXBBDiXi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9875"
  animName: "AS_CH_Enter_PL_244"
  bank: "VO_TXBBLala"
  playEvent: "Play_VO_AS_CH_Enter_PL_199"
  stopEvent: "Stop_VO_TXBBLala"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9876"
  animName: "AS_CH_IdleShow_PL_244"
  bank: "VO_TXBBLala"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_199"
  stopEvent: "Stop_VO_TXBBLala"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9877"
  animName: "AS_CH_Enter_PL_245"
  bank: "VO_TXBBXiaoBo"
  playEvent: "Play_VO_AS_CH_Enter_PL_200"
  stopEvent: "Stop_VO_TXBBXiaoBo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9878"
  animName: "AS_CH_IdleShow_PL_245"
  bank: "VO_TXBBXiaoBo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_200"
  stopEvent: "Stop_VO_TXBBXiaoBo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9879"
  animName: "AS_CH_Enter_PL_218"
  bank: "SFX_Lanka"
  playEvent: "Play_SFX_AS_CH_Enter_PL_218"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_218"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9880"
  animName: "AS_CH_IdleShow_PL_218"
  bank: "SFX_Lanka"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_218"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_218"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9881"
  animName: "AS_CH_Enter_PL_218"
  bank: "VO_Lanka"
  playEvent: "Play_VO_AS_CH_Enter_PL_218"
  stopEvent: "Stop_VO_Lanka"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9882"
  animName: "AS_CH_IdleShow_PL_218"
  bank: "VO_Lanka"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_218"
  stopEvent: "Stop_VO_Lanka"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9883"
  animName: "AS_CH_IdleShow_OG_033"
  bank: "SFX_ShaliyaV2"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_033"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_033"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9884"
  animName: "AS_CH_IdleShow_OG_033"
  bank: "VO_ShaliyaV2"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_033"
  stopEvent: "Stop_VO_Shaliya"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9885"
  animName: "AS_CH_IdleShow_OG_034"
  bank: "SFX_LuziV2"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_034"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_034"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9886"
  animName: "AS_CH_IdleShow_OG_034"
  bank: "VO_LuziV2"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_034"
  stopEvent: "Stop_VO_Luzi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9887"
  animName: "AS_CH_IdleShow_OG_035"
  bank: "SFX_AiweiV2"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_035"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_035"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9888"
  animName: "AS_CH_IdleShow_OG_035"
  bank: "VO_AiweiV2"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_035"
  stopEvent: "Stop_VO_Aiwei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9889"
  animName: "AS_CH_IdleShow_OG_036"
  bank: "SFX_Ruoning"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_036"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_036"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9890"
  animName: "AS_CH_IdleShow_OG_036"
  bank: "VO_Ruoning"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_036"
  stopEvent: "Stop_VO_Ruoning"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9891"
  animName: "AS_CH_IdleShow_OG_037"
  bank: "SFX_Luoan"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_037"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_037"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9892"
  animName: "AS_CH_IdleShow_OG_037"
  bank: "VO_Luoan"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_037"
  stopEvent: "Stop_VO_Luoan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9893"
  animName: "AS_CH_Enter_PL_176"
  bank: "SFX_Ulysses"
  playEvent: "Play_SFX_AS_CH_Enter_PL_176"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_176"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9894"
  animName: "AS_CH_IdleShow_PL_176"
  bank: "SFX_Ulysses"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_176"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_176"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9895"
  animName: "AS_CH_Enter_PL_182"
  bank: "SFX_HOKHuaMuLanSJLLZ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_182"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_182"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9896"
  animName: "AS_CH_IdleShow_PL_182"
  bank: "SFX_HOKHuaMuLanSJLLZ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_182"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_182"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9897"
  animName: "AS_CH_Enter_PL_229"
  bank: "SFX_HOKLvBu"
  playEvent: "Play_SFX_AS_CH_Enter_PL_229"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_229"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9898"
  animName: "AS_CH_IdleShow_PL_229"
  bank: "SFX_HOKLvBu"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_229"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_229"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9899"
  animName: "AS_CH_Enter_PL_204"
  bank: "SFX_Fai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_204"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_204"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9900"
  animName: "AS_CH_IdleShow_PL_204"
  bank: "SFX_Fai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_204"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_204"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9901"
  animName: "AS_CH_Enter_PL_205"
  bank: "SFX_Fado"
  playEvent: "Play_SFX_AS_CH_Enter_PL_205"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_205"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9902"
  animName: "AS_CH_IdleShow_PL_205"
  bank: "SFX_Fado"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_205"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_205"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9903"
  animName: "AS_CH_Enter_PL_221"
  bank: "SFX_Echo"
  playEvent: "Play_SFX_AS_CH_Enter_PL_221"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_221"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9904"
  animName: "AS_CH_IdleShow_PL_221"
  bank: "SFX_Echo"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_221"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_221"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9905"
  animName: "AS_CH_Enter_PL_217"
  bank: "SFX_Fiona"
  playEvent: "Play_SFX_AS_CH_Enter_PL_217"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_217"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9906"
  animName: "AS_CH_IdleShow_PL_217"
  bank: "SFX_Fiona"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_217"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_217"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9907"
  animName: "AS_CH_Enter_PL_251"
  bank: "SFX_HuaDan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_251"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_251"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9908"
  animName: "AS_CH_IdleShow_PL_251"
  bank: "SFX_HuaDan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_251"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_251"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9909"
  animName: "AS_CH_Enter_PL_252"
  bank: "SFX_HOKAKeJZRL"
  playEvent: "Play_SFX_AS_CH_Enter_PL_252"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_252"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9910"
  animName: "AS_CH_IdleShow_PL_252"
  bank: "SFX_HOKAKeJZRL"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_252"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_252"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9911"
  animName: "AS_CH_Enter_PL_246"
  bank: "SFX_HuaiYi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_246"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_246"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9912"
  animName: "AS_CH_IdleShow_PL_246"
  bank: "SFX_HuaiYi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_246"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_246"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9913"
  animName: "AS_CH_Enter_PL_247"
  bank: "SFX_MiaoMiao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_247"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_247"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9914"
  animName: "AS_CH_IdleShow_PL_247"
  bank: "SFX_MiaoMiao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_247"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_247"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9915"
  animName: "AS_CH_Enter_PL_248"
  bank: "SFX_Ziyan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_248"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_248"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9916"
  animName: "AS_CH_IdleShow_PL_248"
  bank: "SFX_Ziyan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_248"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_248"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9917"
  animName: "AS_CH_Enter_PL_122"
  bank: "SFX_Cinnamoroll"
  playEvent: "Play_SFX_AS_CH_Enter_PL_122"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_122"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9918"
  animName: "AS_CH_IdleShow_PL_122"
  bank: "SFX_Cinnamoroll"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_122"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_122"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9919"
  animName: "AS_CH_Enter_PL_123"
  bank: "SFX_Mymelody"
  playEvent: "Play_SFX_AS_CH_Enter_PL_123"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_123"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9920"
  animName: "AS_CH_IdleShow_PL_123"
  bank: "SFX_Mymelody"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_123"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_123"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9921"
  animName: "AS_CH_Enter_PL_228"
  bank: "SFX_HOKChengYaoJin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_228"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_228"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9922"
  animName: "AS_CH_IdleShow_PL_228"
  bank: "SFX_HOKChengYaoJin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_228"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_228"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9923"
  animName: "AS_CH_Enter_PL_176"
  bank: "VO_Ulysses"
  playEvent: "Play_VO_AS_CH_Enter_PL_176"
  stopEvent: "Stop_VO_Ulysses"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9924"
  animName: "AS_CH_IdleShow_PL_176"
  bank: "VO_Ulysses"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_176"
  stopEvent: "Stop_VO_Ulysses"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9925"
  animName: "AS_CH_Enter_PL_182"
  bank: "VO_HOKHuaMuLanSJLLZ"
  playEvent: "Play_VO_AS_CH_Enter_PL_182"
  stopEvent: "Stop_VO_HOKHuaMuLanSJLLZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9926"
  animName: "AS_CH_IdleShow_PL_182"
  bank: "VO_HOKHuaMuLanSJLLZ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_182"
  stopEvent: "Stop_VO_HOKHuaMuLanSJLLZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9927"
  animName: "AS_CH_Enter_PL_229"
  bank: "VO_HOKLvBu"
  playEvent: "Play_VO_AS_CH_Enter_PL_229"
  stopEvent: "Stop_VO_HOKLvBu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9928"
  animName: "AS_CH_IdleShow_PL_229"
  bank: "VO_HOKLvBu"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_229"
  stopEvent: "Stop_VO_HOKLvBu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9929"
  animName: "AS_CH_Enter_PL_204"
  bank: "VO_Fai"
  playEvent: "Play_VO_AS_CH_Enter_PL_204"
  stopEvent: "Stop_VO_Fai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9930"
  animName: "AS_CH_IdleShow_PL_204"
  bank: "VO_Fai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_204"
  stopEvent: "Stop_VO_Fai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9931"
  animName: "AS_CH_Enter_PL_205"
  bank: "VO_Fado"
  playEvent: "Play_VO_AS_CH_Enter_PL_205"
  stopEvent: "Stop_VO_Fado"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9932"
  animName: "AS_CH_IdleShow_PL_205"
  bank: "VO_Fado"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_205"
  stopEvent: "Stop_VO_Fado"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9933"
  animName: "AS_CH_Enter_PL_221"
  bank: "VO_Echo"
  playEvent: "Play_VO_AS_CH_Enter_PL_221"
  stopEvent: "Stop_VO_Echo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9934"
  animName: "AS_CH_IdleShow_PL_221"
  bank: "VO_Echo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_221"
  stopEvent: "Stop_VO_Echo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9935"
  animName: "AS_CH_Enter_PL_217"
  bank: "VO_Fiona"
  playEvent: "Play_VO_AS_CH_Enter_PL_217"
  stopEvent: "Stop_VO_Fiona"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9936"
  animName: "AS_CH_IdleShow_PL_217"
  bank: "VO_Fiona"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_217"
  stopEvent: "Stop_VO_Fiona"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9937"
  animName: "AS_CH_Enter_PL_251"
  bank: "VO_HuaDan"
  playEvent: "Play_VO_AS_CH_Enter_PL_251"
  stopEvent: "Stop_VO_HuaDan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9938"
  animName: "AS_CH_IdleShow_PL_251"
  bank: "VO_HuaDan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_251"
  stopEvent: "Stop_VO_HuaDan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9939"
  animName: "AS_CH_Enter_PL_252"
  bank: "VO_HOKAKeJZRL"
  playEvent: "Play_VO_AS_CH_Enter_PL_252"
  stopEvent: "Stop_VO_HOKAKeJZRL"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9940"
  animName: "AS_CH_IdleShow_PL_252"
  bank: "VO_HOKAKeJZRL"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_252"
  stopEvent: "Stop_VO_HOKAKeJZRL"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9941"
  animName: "AS_CH_Enter_PL_246"
  bank: "VO_HuaiYi"
  playEvent: "Play_VO_AS_CH_Enter_PL_246"
  stopEvent: "Stop_VO_HuaiYi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9942"
  animName: "AS_CH_IdleShow_PL_246"
  bank: "VO_HuaiYi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_246"
  stopEvent: "Stop_VO_HuaiYi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9943"
  animName: "AS_CH_Enter_PL_247"
  bank: "VO_MiaoMiao"
  playEvent: "Play_VO_AS_CH_Enter_PL_247"
  stopEvent: "Stop_VO_MiaoMiao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9944"
  animName: "AS_CH_IdleShow_PL_247"
  bank: "VO_MiaoMiao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_247"
  stopEvent: "Stop_VO_MiaoMiao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9945"
  animName: "AS_CH_Enter_PL_248"
  bank: "VO_Ziyan"
  playEvent: "Play_VO_AS_CH_Enter_PL_248"
  stopEvent: "Stop_VO_Ziyan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9946"
  animName: "AS_CH_IdleShow_PL_248"
  bank: "VO_Ziyan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_248"
  stopEvent: "Stop_VO_Ziyan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9947"
  animName: "AS_CH_Enter_PL_122"
  bank: "VO_Cinnamoroll"
  playEvent: "Play_VO_AS_CH_Enter_PL_122"
  stopEvent: "Stop_VO_Cinnamoroll"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9948"
  animName: "AS_CH_IdleShow_PL_122"
  bank: "VO_Cinnamoroll"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_122"
  stopEvent: "Stop_VO_Cinnamoroll"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9949"
  animName: "AS_CH_Enter_PL_123"
  bank: "VO_Mymelody"
  playEvent: "Play_VO_AS_CH_Enter_PL_123"
  stopEvent: "Stop_VO_Mymelody"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9950"
  animName: "AS_CH_IdleShow_PL_123"
  bank: "VO_Mymelody"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_123"
  stopEvent: "Stop_VO_Mymelody"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9951"
  animName: "AS_CH_Enter_PL_228"
  bank: "VO_HOKChengYaoJin"
  playEvent: "Play_VO_AS_CH_Enter_PL_228"
  stopEvent: "Stop_VO_HOKChengYaoJin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9952"
  animName: "AS_CH_IdleShow_PL_228"
  bank: "VO_HOKChengYaoJin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_228"
  stopEvent: "Stop_VO_HOKChengYaoJin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9953"
  animName: "AS_CH_Enter_PL_234"
  bank: "SFX_HOKDianWeiLPJG"
  playEvent: "Play_SFX_AS_CH_Enter_PL_234"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_234"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9954"
  animName: "AS_CH_IdleShow_PL_234"
  bank: "SFX_HOKDianWeiLPJG"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_234"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_234"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9955"
  animName: "AS_CH_Enter_PL_258"
  bank: "SFX_ChangLe"
  playEvent: "Play_SFX_AS_CH_Enter_PL_258"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_258"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9956"
  animName: "AS_CH_IdleShow_PL_258"
  bank: "SFX_ChangLe"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_258"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_258"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9957"
  animName: "AS_CH_Enter_PL_269"
  bank: "SFX_ChangLe"
  playEvent: "Play_SFX_AS_CH_Enter_PL_269"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_269"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9958"
  animName: "AS_CH_IdleShow_PL_269"
  bank: "SFX_ChangLe"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_269"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_269"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9959"
  animName: "AS_CH_Enter_PL_219"
  bank: "SFX_Sharkitty"
  playEvent: "Play_SFX_AS_CH_Enter_PL_219"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_219"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9960"
  animName: "AS_CH_IdleShow_PL_219"
  bank: "SFX_Sharkitty"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_219"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_219"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9961"
  animName: "AS_CH_Enter_PL_254"
  bank: "SFX_BSZQingShe"
  playEvent: "Play_SFX_AS_CH_Enter_PL_254"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_254"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9962"
  animName: "AS_CH_IdleShow_PL_254"
  bank: "SFX_BSZQingShe"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_254"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_254"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9963"
  animName: "AS_CH_Enter_PL_255"
  bank: "SFX_BSZBaiShe"
  playEvent: "Play_SFX_AS_CH_Enter_PL_255"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_255"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9964"
  animName: "AS_CH_IdleShow_PL_255"
  bank: "SFX_BSZBaiShe"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_255"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_255"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9965"
  animName: "AS_CH_Enter_PL_253"
  bank: "SFX_LingChai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_253"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_253"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9966"
  animName: "AS_CH_IdleShow_PL_253"
  bank: "SFX_LingChai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_253"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_253"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9967"
  animName: "AS_CH_Enter_PL_249"
  bank: "SFX_WenXin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_249"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_249"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9968"
  animName: "AS_CH_IdleShow_PL_249"
  bank: "SFX_WenXin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_249"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_249"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9969"
  animName: "AS_CH_Enter_PL_256"
  bank: "SFX_Lycantropae"
  playEvent: "Play_SFX_AS_CH_Enter_PL_256"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_253"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9970"
  animName: "AS_CH_IdleShow_PL_256"
  bank: "SFX_Lycantropae"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_256"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_253"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9971"
  animName: "AS_CH_Enter_PL_238"
  bank: "SFX_HOKJiaLuoTH"
  playEvent: "Play_SFX_AS_CH_Enter_PL_238"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_253"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9972"
  animName: "AS_CH_IdleShow_PL_238"
  bank: "SFX_HOKJiaLuoTH"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_238"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_253"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9973"
  animName: "AS_CH_Enter_PL_234"
  bank: "VO_HOKDianWeiLPJG"
  playEvent: "Play_VO_AS_CH_Enter_PL_234"
  stopEvent: "Stop_VO_HOKDianWeiLPJG"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9974"
  animName: "AS_CH_IdleShow_PL_234"
  bank: "VO_HOKDianWeiLPJG"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_234"
  stopEvent: "Stop_VO_HOKDianWeiLPJG"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9975"
  animName: "AS_CH_Enter_PL_258"
  bank: "VO_ChangLe"
  playEvent: "Play_VO_AS_CH_Enter_PL_258"
  stopEvent: "Stop_VO_ChangLe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9976"
  animName: "AS_CH_IdleShow_PL_258"
  bank: "VO_ChangLe"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_258"
  stopEvent: "Stop_VO_ChangLe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9977"
  animName: "AS_CH_Enter_PL_269"
  bank: "VO_ChangLe"
  playEvent: "Play_VO_AS_CH_Enter_PL_269"
  stopEvent: "Stop_VO_ChangLe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9978"
  animName: "AS_CH_IdleShow_PL_269"
  bank: "VO_ChangLe"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_269"
  stopEvent: "Stop_VO_ChangLe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9979"
  animName: "AS_CH_Enter_PL_219"
  bank: "VO_Sharkitty"
  playEvent: "Play_VO_AS_CH_Enter_PL_219"
  stopEvent: "Stop_VO_Sharkitty"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9980"
  animName: "AS_CH_IdleShow_PL_219"
  bank: "VO_Sharkitty"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_219"
  stopEvent: "Stop_VO_Sharkitty"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9981"
  animName: "AS_CH_Enter_PL_254"
  bank: "VO_BSZQingShe"
  playEvent: "Play_VO_AS_CH_Enter_PL_254"
  stopEvent: "Stop_VO_BSZQingShe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9982"
  animName: "AS_CH_IdleShow_PL_254"
  bank: "VO_BSZQingShe"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_254"
  stopEvent: "Stop_VO_BSZQingShe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9983"
  animName: "AS_CH_Enter_PL_255"
  bank: "VO_BSZBaiShe"
  playEvent: "Play_VO_AS_CH_Enter_PL_255"
  stopEvent: "Stop_VO_BSZBaiShe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9984"
  animName: "AS_CH_IdleShow_PL_255"
  bank: "VO_BSZBaiShe"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_255"
  stopEvent: "Stop_VO_BSZBaiShe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9985"
  animName: "AS_CH_Enter_PL_253"
  bank: "VO_LingChai"
  playEvent: "Play_VO_AS_CH_Enter_PL_253"
  stopEvent: "Stop_VO_LingChai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9986"
  animName: "AS_CH_IdleShow_PL_253"
  bank: "VO_LingChai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_253"
  stopEvent: "Stop_VO_LingChai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9987"
  animName: "AS_CH_Enter_PL_249"
  bank: "VO_WenXin"
  playEvent: "Play_VO_AS_CH_Enter_PL_249"
  stopEvent: "Stop_VO_WenXin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9988"
  animName: "AS_CH_IdleShow_PL_249"
  bank: "VO_WenXin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_249"
  stopEvent: "Stop_VO_WenXin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9989"
  animName: "AS_CH_Enter_PL_256"
  bank: "VO_Lycantropae"
  playEvent: "Play_VO_AS_CH_Enter_PL_256"
  stopEvent: "Stop_VO_Lycantropae"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9990"
  animName: "AS_CH_IdleShow_PL_256"
  bank: "VO_Lycantropae"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_256"
  stopEvent: "Stop_VO_Lycantropae"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9991"
  animName: "AS_CH_Enter_PL_238"
  bank: "VO_HOKJiaLuoTH"
  playEvent: "Play_VO_AS_CH_Enter_PL_238"
  stopEvent: "Stop_VO_HOKJiaLuoTH"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9992"
  animName: "AS_CH_IdleShow_PL_238"
  bank: "VO_HOKJiaLuoTH"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_238"
  stopEvent: "Stop_VO_HOKJiaLuoTH"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9993"
  animName: "AS_CH_Enter_PL_259"
  bank: "SFX_LeiYa"
  playEvent: "Play_SFX_AS_CH_Enter_PL_259"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_259"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9994"
  animName: "AS_CH_IdleShow_PL_259"
  bank: "SFX_LeiYa"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_259"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_259"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9995"
  animName: "AS_CH_Enter_PL_264"
  bank: "SFX_Kathy"
  playEvent: "Play_SFX_AS_CH_Enter_PL_264"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_264"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9996"
  animName: "AS_CH_IdleShow_PL_264"
  bank: "SFX_Kathy"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_264"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_264"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9997"
  animName: "AS_CH_IdleShow_OG_038"
  bank: "SFX_JiuWeiHuSP"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_038"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_038"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9998"
  animName: "AS_CH_IdleShow_OG_039"
  bank: "SFX_JiuWeiHu"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_039"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_039"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9999"
  animName: "AS_CH_Enter_PL_261"
  bank: "SFX_HOKYangYuHuan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_261"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_261"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10000"
  animName: "AS_CH_IdleShow_PL_261"
  bank: "SFX_HOKYangYuHuan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_261"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_261"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10001"
  animName: "AS_CH_Enter_PL_239"
  bank: "SFX_HOKZhenjiBXYWQ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_239"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_239"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10002"
  animName: "AS_CH_IdleShow_PL_239"
  bank: "SFX_HOKZhenjiBXYWQ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_239"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_239"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10003"
  animName: "AS_CH_Enter_PL_262"
  bank: "SFX_Alphas"
  playEvent: "Play_SFX_AS_CH_Enter_PL_262"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_262"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10004"
  animName: "AS_CH_IdleShow_PL_262"
  bank: "SFX_Alphas"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_262"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_262"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10005"
  animName: "AS_CH_Enter_PL_257"
  bank: "SFX_LiDiya"
  playEvent: "Play_SFX_AS_CH_Enter_PL_257"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_257"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10006"
  animName: "AS_CH_IdleShow_PL_257"
  bank: "SFX_LiDiya"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_257"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_257"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10007"
  animName: "AS_CH_Enter_PL_263"
  bank: "SFX_LiHuo"
  playEvent: "Play_SFX_AS_CH_Enter_PL_263"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_263"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10008"
  animName: "AS_CH_IdleShow_PL_263"
  bank: "SFX_LiHuo"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_263"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_263"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10009"
  animName: "AS_CH_Enter_PL_259"
  bank: "VO_LeiYa"
  playEvent: "Play_VO_AS_CH_Enter_PL_259"
  stopEvent: "Stop_VO_LeiYa"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10010"
  animName: "AS_CH_IdleShow_PL_259"
  bank: "VO_LeiYa"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_259"
  stopEvent: "Stop_VO_LeiYa"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10011"
  animName: "AS_CH_Enter_PL_264"
  bank: "VO_Kathy"
  playEvent: "Play_VO_AS_CH_Enter_PL_264"
  stopEvent: "Stop_VO_Kathy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10012"
  animName: "AS_CH_IdleShow_PL_264"
  bank: "VO_Kathy"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_264"
  stopEvent: "Stop_VO_Kathy"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10013"
  animName: "AS_CH_IdleShow_OG_038"
  bank: "VO_JiuWeiHuSP"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_038"
  stopEvent: "Stop_VO_JiuWeiHuSP"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10014"
  animName: "AS_CH_IdleShow_OG_039"
  bank: "VO_JiuWeiHu"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_039"
  stopEvent: "Stop_VO_JiuWeiHu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10015"
  animName: "AS_CH_Enter_PL_261"
  bank: "VO_HOKYangYuHuan"
  playEvent: "Play_VO_AS_CH_Enter_PL_261"
  stopEvent: "Stop_VO_HOKYangYuHuan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10016"
  animName: "AS_CH_IdleShow_PL_261"
  bank: "VO_HOKYangYuHuan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_261"
  stopEvent: "Stop_VO_HOKYangYuHuan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10017"
  animName: "AS_CH_Enter_PL_239"
  bank: "VO_HOKZhenjiBXYWQ"
  playEvent: "Play_VO_AS_CH_Enter_PL_239"
  stopEvent: "Stop_VO_HOKZhenjiBXYWQ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10018"
  animName: "AS_CH_IdleShow_PL_239"
  bank: "VO_HOKZhenjiBXYWQ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_239"
  stopEvent: "Stop_VO_HOKZhenjiBXYWQ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10019"
  animName: "AS_CH_Enter_PL_262"
  bank: "VO_Alphas"
  playEvent: "Play_VO_AS_CH_Enter_PL_262"
  stopEvent: "Stop_VO_Alphas"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10020"
  animName: "AS_CH_IdleShow_PL_262"
  bank: "VO_Alphas"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_262"
  stopEvent: "Stop_VO_Alphas"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10021"
  animName: "AS_CH_Enter_PL_257"
  bank: "VO_LiDiya"
  playEvent: "Play_VO_AS_CH_Enter_PL_257"
  stopEvent: "Stop_VO_LiDiya"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10022"
  animName: "AS_CH_IdleShow_PL_257"
  bank: "VO_LiDiya"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_257"
  stopEvent: "Stop_VO_LiDiya"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10023"
  animName: "AS_CH_Enter_PL_263"
  bank: "VO_LiHuo"
  playEvent: "Play_VO_AS_CH_Enter_PL_263"
  stopEvent: "Stop_VO_LiHuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10024"
  animName: "AS_CH_IdleShow_PL_263"
  bank: "VO_LiHuo"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_263"
  stopEvent: "Stop_VO_LiHuo"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10025"
  animName: "AS_CH_Enter_PL_260"
  bank: "SFX_chico"
  playEvent: "Play_SFX_AS_CH_Enter_PL_260"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_260"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10026"
  animName: "AS_CH_IdleShow_PL_260"
  bank: "SFX_chico"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_260"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_260"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10027"
  animName: "AS_CH_Enter_PL_268"
  bank: "SFX_ZhenBai"
  playEvent: "Play_SFX_AS_CH_Enter_PL_268"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_268"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10028"
  animName: "AS_CH_IdleShow_PL_268"
  bank: "SFX_ZhenBai"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_268"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_268"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10029"
  animName: "AS_CH_IdleShow_OG_041"
  bank: "SFX_Sybil"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_041"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_041"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10030"
  animName: "AS_CH_IdleShow_OG_042"
  bank: "SFX_SybilSP"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_042"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_042"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10031"
  animName: "AS_CH_Enter_PL_265"
  bank: "SFX_Jacob"
  playEvent: "Play_SFX_AS_CH_Enter_PL_265"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_265"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10032"
  animName: "AS_CH_IdleShow_PL_265"
  bank: "SFX_Jacob"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_265"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_265"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10033"
  animName: "AS_CH_Enter_PL_266"
  bank: "SFX_Miranda"
  playEvent: "Play_SFX_AS_CH_Enter_PL_266"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_266"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10034"
  animName: "AS_CH_IdleShow_PL_266"
  bank: "SFX_Miranda"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_266"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_266"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10035"
  animName: "AS_CH_Enter_PL_267"
  bank: "SFX_Circe"
  playEvent: "Play_SFX_AS_CH_Enter_PL_267"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_267"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10036"
  animName: "AS_CH_IdleShow_PL_267"
  bank: "SFX_Circe"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_267"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_267"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10037"
  animName: "AS_CH_Enter_PL_228"
  bank: "SFX_HOKChengYaoJin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_228"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_228"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10038"
  animName: "AS_CH_IdleShow_PL_228"
  bank: "SFX_HOKChengYaoJin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_228"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_228"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10039"
  animName: "AS_CH_IdleShow_OG_040"
  bank: "SFX_DuoLi"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_040"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_040"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10040"
  animName: "AS_CH_Enter_PL_260"
  bank: "VO_chico"
  playEvent: "Play_VO_AS_CH_Enter_PL_260"
  stopEvent: "Stop_VO_chico"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10041"
  animName: "AS_CH_IdleShow_PL_260"
  bank: "VO_chico"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_260"
  stopEvent: "Stop_VO_chico"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10042"
  animName: "AS_CH_Enter_PL_268"
  bank: "VO_ZhenBai"
  playEvent: "Play_VO_AS_CH_Enter_PL_268"
  stopEvent: "Stop_VO_ZhenBai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10043"
  animName: "AS_CH_IdleShow_PL_268"
  bank: "VO_ZhenBai"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_268"
  stopEvent: "Stop_VO_ZhenBai"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10044"
  animName: "AS_CH_IdleShow_OG_041"
  bank: "VO_Sybil"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_041"
  stopEvent: "Stop_VO_Sybil"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10045"
  animName: "AS_CH_IdleShow_OG_042"
  bank: "VO_SybilSP"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_042"
  stopEvent: "Stop_VO_SybilSP"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10046"
  animName: "AS_CH_Enter_PL_265"
  bank: "VO_Jacob"
  playEvent: "Play_VO_AS_CH_Enter_PL_265"
  stopEvent: "Stop_VO_Jacob"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10047"
  animName: "AS_CH_IdleShow_PL_265"
  bank: "VO_Jacob"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_265"
  stopEvent: "Stop_VO_Jacob"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10048"
  animName: "AS_CH_Enter_PL_266"
  bank: "VO_Miranda"
  playEvent: "Play_VO_AS_CH_Enter_PL_266"
  stopEvent: "Stop_VO_Miranda"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10049"
  animName: "AS_CH_IdleShow_PL_266"
  bank: "VO_Miranda"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_266"
  stopEvent: "Stop_VO_Miranda"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10050"
  animName: "AS_CH_Enter_PL_267"
  bank: "VO_Circe"
  playEvent: "Play_VO_AS_CH_Enter_PL_267"
  stopEvent: "Stop_VO_Circe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10051"
  animName: "AS_CH_IdleShow_PL_267"
  bank: "VO_Circe"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_267"
  stopEvent: "Stop_VO_Circe"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10052"
  animName: "AS_CH_Enter_PL_228"
  bank: "VO_HOKChengYaoJin"
  playEvent: "Play_VO_AS_CH_Enter_PL_228"
  stopEvent: "Stop_VO_HOKChengYaoJin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10053"
  animName: "AS_CH_IdleShow_PL_228"
  bank: "VO_HOKChengYaoJin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_228"
  stopEvent: "Stop_VO_HOKChengYaoJin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10054"
  animName: "AS_CH_IdleShow_OG_040"
  bank: "VO_DuoLi"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_040"
  stopEvent: "Stop_VO_DuoLi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10055"
  animName: "AS_CH_Enter_PL_240"
  bank: "SFX_HOKLiuShan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_240"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_240"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10056"
  animName: "AS_CH_IdleShow_PL_240"
  bank: "SFX_HOKLiuShan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_240"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_240"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10057"
  animName: "AS_CH_Enter_PL_236"
  bank: "SFX_HOKZhuGeLiang"
  playEvent: "Play_SFX_AS_CH_Enter_PL_236"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_236"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10058"
  animName: "AS_CH_IdleShow_PL_236"
  bank: "SFX_HOKZhuGeLiang"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_236"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_236"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10059"
  animName: "AS_CH_Enter_PL_235"
  bank: "SFX_HOKLiuBei"
  playEvent: "Play_SFX_AS_CH_Enter_PL_235"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_235"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10060"
  animName: "AS_CH_IdleShow_PL_235"
  bank: "SFX_HOKLiuBei"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_235"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_235"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10061"
  animName: "AS_CH_Enter_PL_275"
  bank: "SFX_SweetBeanBL"
  playEvent: "Play_SFX_AS_CH_Enter_PL_275"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_275"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10062"
  animName: "AS_CH_IdleShow_PL_275"
  bank: "SFX_SweetBeanBL"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_275"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_275"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10063"
  animName: "AS_CH_Enter_PL_276"
  bank: "SFX_SweetBeanMM"
  playEvent: "Play_SFX_AS_CH_Enter_PL_276"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_276"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10064"
  animName: "AS_CH_IdleShow_PL_276"
  bank: "SFX_SweetBeanMM"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_276"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_276"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10065"
  animName: "AS_CH_Enter_PL_278"
  bank: "SFX_SweetBeanMRJZ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_278"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_278"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10066"
  animName: "AS_CH_IdleShow_PL_278"
  bank: "SFX_SweetBeanMRJZ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_278"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_278"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10067"
  animName: "AS_CH_Enter_PL_277"
  bank: "SFX_Qinyuan"
  playEvent: "Play_SFX_AS_CH_Enter_PL_277"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_277"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10068"
  animName: "AS_CH_IdleShow_PL_277"
  bank: "SFX_Qinyuan"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_277"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_277"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10069"
  animName: "AS_CH_IdleShow_OG_043"
  bank: "SFX_Vanessa"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_043"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_043"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10070"
  animName: "AS_CH_Enter_PL_272"
  bank: "SFX_Eleanore"
  playEvent: "Play_SFX_AS_CH_Enter_PL_272"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_272"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10071"
  animName: "AS_CH_IdleShow_PL_272"
  bank: "SFX_Eleanore"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_272"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_272"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10072"
  animName: "AS_CH_Enter_PL_273"
  bank: "SFX_Chichio"
  playEvent: "Play_SFX_AS_CH_Enter_PL_273"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_273"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10073"
  animName: "AS_CH_IdleShow_PL_273"
  bank: "SFX_Chichio"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_273"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_273"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10074"
  animName: "AS_CH_Enter_PL_241"
  bank: "SFX_HOKDaQiao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_241"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_241"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10075"
  animName: "AS_CH_IdleShow_PL_241"
  bank: "SFX_HOKDaQiao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_241"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_241"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10076"
  animName: "AS_CH_Enter_PL_240"
  bank: "VO_HOKLiuShan"
  playEvent: "Play_VO_AS_CH_Enter_PL_240"
  stopEvent: "Stop_VO_HOKLiuShan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10077"
  animName: "AS_CH_IdleShow_PL_240"
  bank: "VO_HOKLiuShan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_240"
  stopEvent: "Stop_VO_HOKLiuShan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10078"
  animName: "AS_CH_Enter_PL_236"
  bank: "VO_HOKZhuGeLiang"
  playEvent: "Play_VO_AS_CH_Enter_PL_236"
  stopEvent: "Stop_VO_HOKZhuGeLiang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10079"
  animName: "AS_CH_IdleShow_PL_236"
  bank: "VO_HOKZhuGeLiang"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_236"
  stopEvent: "Stop_VO_HOKZhuGeLiang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10080"
  animName: "AS_CH_Enter_PL_235"
  bank: "VO_HOKLiuBei"
  playEvent: "Play_VO_AS_CH_Enter_PL_235"
  stopEvent: "Stop_VO_HOKLiuBei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10081"
  animName: "AS_CH_IdleShow_PL_235"
  bank: "VO_HOKLiuBei"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_235"
  stopEvent: "Stop_VO_HOKLiuBei"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10082"
  animName: "AS_CH_Enter_PL_275"
  bank: "VO_SweetBeanBL"
  playEvent: "Play_VO_AS_CH_Enter_PL_275"
  stopEvent: "Stop_VO_SweetBeanBL"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10083"
  animName: "AS_CH_IdleShow_PL_275"
  bank: "VO_SweetBeanBL"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_275"
  stopEvent: "Stop_VO_SweetBeanBL"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10084"
  animName: "AS_CH_Enter_PL_276"
  bank: "VO_SweetBeanMM"
  playEvent: "Play_VO_AS_CH_Enter_PL_276"
  stopEvent: "Stop_VO_SweetBeanMM"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10085"
  animName: "AS_CH_IdleShow_PL_276"
  bank: "VO_SweetBeanMM"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_276"
  stopEvent: "Stop_VO_SweetBeanMM"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10086"
  animName: "AS_CH_Enter_PL_278"
  bank: "VO_SweetBeanMRJZ"
  playEvent: "Play_VO_AS_CH_Enter_PL_278"
  stopEvent: "Stop_VO_SweetBeanMRJZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10087"
  animName: "AS_CH_IdleShow_PL_278"
  bank: "VO_SweetBeanMRJZ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_278"
  stopEvent: "Stop_VO_SweetBeanMRJZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10088"
  animName: "AS_CH_Enter_PL_277"
  bank: "VO_Qinyuan"
  playEvent: "Play_VO_AS_CH_Enter_PL_277"
  stopEvent: "Stop_VO_Qinyuan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10089"
  animName: "AS_CH_IdleShow_PL_277"
  bank: "VO_Qinyuan"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_277"
  stopEvent: "Stop_VO_Qinyuan"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10090"
  animName: "AS_CH_IdleShow_OG_043"
  bank: "VO_Vanessa"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_043"
  stopEvent: "Stop_VO_Vanessa"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10091"
  animName: "AS_CH_Enter_PL_272"
  bank: "VO_Eleanore"
  playEvent: "Play_VO_AS_CH_Enter_PL_272"
  stopEvent: "Stop_VO_Eleanore"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10092"
  animName: "AS_CH_IdleShow_PL_272"
  bank: "VO_Eleanore"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_272"
  stopEvent: "Stop_VO_Eleanore"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10093"
  animName: "AS_CH_Enter_PL_273"
  bank: "VO_Chichio"
  playEvent: "Play_VO_AS_CH_Enter_PL_273"
  stopEvent: "Stop_VO_Chichio"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10094"
  animName: "AS_CH_IdleShow_PL_273"
  bank: "VO_Chichio"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_273"
  stopEvent: "Stop_VO_Chichio"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10095"
  animName: "AS_CH_Enter_PL_241"
  bank: "VO_HOKDaQiao"
  playEvent: "Play_VO_AS_CH_Enter_PL_241"
  stopEvent: "Stop_VO_HOKDaQiao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10096"
  animName: "AS_CH_IdleShow_PL_241"
  bank: "VO_HOKDaQiao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_241"
  stopEvent: "Stop_VO_HOKDaQiao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10097"
  animName: "AS_CH_Enter_PL_146"
  bank: "SFX_chiikawa"
  playEvent: "Play_SFX_AS_CH_Enter_PL_146"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_146"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10098"
  animName: "AS_CH_IdleShow_PL_146"
  bank: "SFX_chiikawa"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_146"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_146"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10099"
  animName: "AS_CH_Enter_PL_147"
  bank: "SFX_hachiware"
  playEvent: "Play_SFX_AS_CH_Enter_PL_147"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_147"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10100"
  animName: "AS_CH_IdleShow_PL_147"
  bank: "SFX_hachiware"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_147"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_147"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10101"
  animName: "AS_CH_Enter_PL_148"
  bank: "SFX_usagi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_148"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_148"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10102"
  animName: "AS_CH_IdleShow_PL_148"
  bank: "SFX_usagi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_148"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_148"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10103"
  animName: "AS_CH_Enter_PL_146"
  bank: "VO_chiikawa"
  playEvent: "Play_VO_AS_CH_Enter_PL_146"
  stopEvent: "Stop_VO_chiikawa"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10104"
  animName: "AS_CH_IdleShow_PL_146"
  bank: "VO_chiikawa"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_146"
  stopEvent: "Stop_VO_chiikawa"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10105"
  animName: "AS_CH_Enter_PL_147"
  bank: "VO_hachiware"
  playEvent: "Play_VO_AS_CH_Enter_PL_147"
  stopEvent: "Stop_VO_hachiware"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10106"
  animName: "AS_CH_IdleShow_PL_147"
  bank: "VO_hachiware"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_147"
  stopEvent: "Stop_VO_hachiware"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10107"
  animName: "AS_CH_Enter_PL_148"
  bank: "VO_usagi"
  playEvent: "Play_VO_AS_CH_Enter_PL_148"
  stopEvent: "Stop_VO_usagi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10108"
  animName: "AS_CH_IdleShow_PL_148"
  bank: "VO_usagi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_148"
  stopEvent: "Stop_VO_usagi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10109"
  animName: "AS_CH_Enter_PL_181"
  bank: "SFX_HOKHaiYue"
  playEvent: "Play_SFX_AS_CH_Enter_PL_181"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_181"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10110"
  animName: "AS_CH_IdleShow_PL_181"
  bank: "SFX_HOKHaiYue"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_181"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_181"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10111"
  animName: "AS_CH_Enter_PL_181"
  bank: "VO_HOKHaiYue"
  playEvent: "Play_VO_AS_CH_Enter_PL_181"
  stopEvent: "Stop_VO_HOKHaiYue"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10112"
  animName: "AS_CH_IdleShow_PL_181"
  bank: "VO_HOKHaiYue"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_181"
  stopEvent: "Stop_VO_HOKHaiYue"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10113"
  animName: "AS_CH_Enter_PL_283"
  bank: "SFX_Moyu"
  playEvent: "Play_SFX_AS_CH_Enter_PL_283"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_283"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10114"
  animName: "AS_CH_IdleShow_PL_283"
  bank: "SFX_Moyu"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_283"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_283"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10115"
  animName: "AS_CH_Enter_PL_280"
  bank: "SFX_ZDWRiJi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_280"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_280"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10116"
  animName: "AS_CH_IdleShow_PL_280"
  bank: "SFX_ZDWRiJi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_280"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_280"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10117"
  animName: "AS_CH_Enter_PL_281"
  bank: "SFX_ZDWLaHuaShi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_281"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_281"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10118"
  animName: "AS_CH_IdleShow_PL_281"
  bank: "SFX_ZDWLaHuaShi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_281"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_281"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10119"
  animName: "AS_CH_Enter_PL_294"
  bank: "SFX_Aurelia"
  playEvent: "Play_SFX_AS_CH_Enter_PL_294"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_294"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10120"
  animName: "AS_CH_IdleShow_PL_294"
  bank: "SFX_Aurelia"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_294"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_294"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10121"
  animName: "AS_CH_Enter_PL_291"
  bank: "SFX_Maggie"
  playEvent: "Play_SFX_AS_CH_Enter_PL_291"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_291"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10122"
  animName: "AS_CH_IdleShow_PL_291"
  bank: "SFX_Maggie"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_291"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_291"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10123"
  animName: "AS_CH_Enter_PL_292"
  bank: "SFX_Thor"
  playEvent: "Play_SFX_AS_CH_Enter_PL_292"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_292"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10124"
  animName: "AS_CH_IdleShow_PL_292"
  bank: "SFX_Thor"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_292"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_292"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10125"
  animName: "AS_CH_Enter_PL_295"
  bank: "SFX_MTMMitaomao"
  playEvent: "Play_SFX_AS_CH_Enter_PL_295"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_295"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10126"
  animName: "AS_CH_IdleShow_PL_295"
  bank: "SFX_MTMMitaomao"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_295"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_295"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10127"
  animName: "AS_CH_Enter_PL_270"
  bank: "SFX_Xing"
  playEvent: "Play_SFX_AS_CH_Enter_PL_270"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_270"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10128"
  animName: "AS_CH_IdleShow_PL_270"
  bank: "SFX_Xing"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_270"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_270"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10129"
  animName: "AS_CH_Enter_PL_271"
  bank: "SFX_Ying"
  playEvent: "Play_SFX_AS_CH_Enter_PL_271"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_271"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10130"
  animName: "AS_CH_IdleShow_PL_271"
  bank: "SFX_Ying"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_271"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_271"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10131"
  animName: "AS_CH_Enter_PL_290"
  bank: "SFX_Sunnee"
  playEvent: "Play_SFX_AS_CH_Enter_PL_290"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_290"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10132"
  animName: "AS_CH_IdleShow_PL_290"
  bank: "SFX_Sunnee"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_290"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_290"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10133"
  animName: "AS_CH_Enter_PL_282"
  bank: "SFX_LongJiang"
  playEvent: "Play_SFX_AS_CH_Enter_PL_282"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_282"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10134"
  animName: "AS_CH_IdleShow_PL_282"
  bank: "SFX_LongJiang"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_282"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_282"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10135"
  animName: "AS_CH_Enter_PL_293"
  bank: "SFX_Annasue"
  playEvent: "Play_SFX_AS_CH_Enter_PL_293"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_293"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10136"
  animName: "AS_CH_IdleShow_PL_293"
  bank: "SFX_Annasue"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_293"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_293"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10137"
  animName: "AS_CH_IdleShow_OG_045"
  bank: "SFX_Hoshiraki"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_045"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_045"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10138"
  animName: "AS_CH_IdleShow_OG_046"
  bank: "SFX_HoshirakiSP"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_046"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_046"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10139"
  animName: "AS_CH_Enter_PL_283"
  bank: "VO_Moyu"
  playEvent: "Play_VO_AS_CH_Enter_PL_283"
  stopEvent: "Stop_VO_Moyu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10140"
  animName: "AS_CH_IdleShow_PL_283"
  bank: "VO_Moyu"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_283"
  stopEvent: "Stop_VO_Moyu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10141"
  animName: "AS_CH_Enter_PL_280"
  bank: "VO_ZDWRiJi"
  playEvent: "Play_VO_AS_CH_Enter_PL_280"
  stopEvent: "Stop_VO_ZDWRiJi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10142"
  animName: "AS_CH_IdleShow_PL_280"
  bank: "VO_ZDWRiJi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_280"
  stopEvent: "Stop_VO_ZDWRiJi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10143"
  animName: "AS_CH_Enter_PL_281"
  bank: "VO_ZDWLaHuaShi"
  playEvent: "Play_VO_AS_CH_Enter_PL_281"
  stopEvent: "Stop_VO_ZDWLaHuaShi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10144"
  animName: "AS_CH_IdleShow_PL_281"
  bank: "VO_ZDWLaHuaShi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_281"
  stopEvent: "Stop_VO_ZDWLaHuaShi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10145"
  animName: "AS_CH_Enter_PL_294"
  bank: "VO_Aurelia"
  playEvent: "Play_VO_AS_CH_Enter_PL_294"
  stopEvent: "Stop_VO_Aurelia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10146"
  animName: "AS_CH_IdleShow_PL_294"
  bank: "VO_Aurelia"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_294"
  stopEvent: "Stop_VO_Aurelia"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10147"
  animName: "AS_CH_Enter_PL_291"
  bank: "VO_Maggie"
  playEvent: "Play_VO_AS_CH_Enter_PL_291"
  stopEvent: "Stop_VO_Maggie"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10148"
  animName: "AS_CH_IdleShow_PL_291"
  bank: "VO_Maggie"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_291"
  stopEvent: "Stop_VO_Maggie"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10149"
  animName: "AS_CH_Enter_PL_292"
  bank: "VO_Thor"
  playEvent: "Play_VO_AS_CH_Enter_PL_292"
  stopEvent: "Stop_VO_Thor"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10150"
  animName: "AS_CH_IdleShow_PL_292"
  bank: "VO_Thor"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_292"
  stopEvent: "Stop_VO_Thor"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10151"
  animName: "AS_CH_Enter_PL_295"
  bank: "VO_MTMMitaomao"
  playEvent: "Play_VO_AS_CH_Enter_PL_295"
  stopEvent: "Stop_VO_MTMMitaomao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10152"
  animName: "AS_CH_IdleShow_PL_295"
  bank: "VO_MTMMitaomao"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_295"
  stopEvent: "Stop_VO_MTMMitaomao"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10153"
  animName: "AS_CH_Enter_PL_270"
  bank: "VO_Xing"
  playEvent: "Play_VO_AS_CH_Enter_PL_270"
  stopEvent: "Stop_VO_Xing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10154"
  animName: "AS_CH_IdleShow_PL_270"
  bank: "VO_Xing"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_270"
  stopEvent: "Stop_VO_Xing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10155"
  animName: "AS_CH_Enter_PL_271"
  bank: "VO_Ying"
  playEvent: "Play_VO_AS_CH_Enter_PL_271"
  stopEvent: "Stop_VO_Ying"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10156"
  animName: "AS_CH_IdleShow_PL_271"
  bank: "VO_Ying"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_271"
  stopEvent: "Stop_VO_Ying"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10157"
  animName: "AS_CH_Enter_PL_290"
  bank: "VO_Sunnee"
  playEvent: "Play_VO_AS_CH_Enter_PL_290"
  stopEvent: "Stop_VO_Sunnee"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10158"
  animName: "AS_CH_IdleShow_PL_290"
  bank: "VO_Sunnee"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_290"
  stopEvent: "Stop_VO_Sunnee"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10159"
  animName: "AS_CH_Enter_PL_282"
  bank: "VO_LongJiang"
  playEvent: "Play_VO_AS_CH_Enter_PL_282"
  stopEvent: "Stop_VO_LongJiang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10160"
  animName: "AS_CH_IdleShow_PL_282"
  bank: "VO_LongJiang"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_282"
  stopEvent: "Stop_VO_LongJiang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10161"
  animName: "AS_CH_Enter_PL_293"
  bank: "VO_Annasue"
  playEvent: "Play_VO_AS_CH_Enter_PL_293"
  stopEvent: "Stop_VO_Annasue"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10162"
  animName: "AS_CH_IdleShow_PL_293"
  bank: "VO_Annasue"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_293"
  stopEvent: "Stop_VO_Annasue"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10163"
  animName: "AS_CH_IdleShow_OG_045"
  bank: "VO_Hoshiraki"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_045"
  stopEvent: "Stop_VO_Hoshiraki"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10164"
  animName: "AS_CH_IdleShow_OG_046"
  bank: "VO_HoshirakiSP"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_046"
  stopEvent: "Stop_VO_HoshirakiSP"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10165"
  animName: "AS_CH_Enter_PL_279"
  bank: "SFX_Strawberrykitty"
  playEvent: "Play_SFX_AS_CH_Enter_PL_279"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_279"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10166"
  animName: "AS_CH_IdleShow_PL_279"
  bank: "SFX_Strawberrykitty"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_279"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_279"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10167"
  animName: "AS_CH_Enter_PL_279"
  bank: "VO_Strawberrykitty"
  playEvent: "Play_VO_AS_CH_Enter_PL_279"
  stopEvent: "Stop_VO_Strawberrykitty"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10168"
  animName: "AS_CH_IdleShow_PL_279"
  bank: "VO_Strawberrykitty"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_279"
  stopEvent: "Stop_VO_Strawberrykitty"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10169"
  animName: "AS_CH_Enter_PL_284"
  bank: "SFX_QingShuang"
  playEvent: "Play_SFX_AS_CH_Enter_PL_284"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_284"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10170"
  animName: "AS_CH_IdleShow_PL_284"
  bank: "SFX_QingShuang"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_284"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_284"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10171"
  animName: "AS_CH_Enter_PL_284"
  bank: "VO_QingShuang"
  playEvent: "Play_VO_AS_CH_Enter_PL_284"
  stopEvent: "Stop_VO_QingShuang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10172"
  animName: "AS_CH_IdleShow_PL_284"
  bank: "VO_QingShuang"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_284"
  stopEvent: "Stop_VO_QingShuang"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10173"
  animName: "AS_CH_IdleShow_OG_047"
  bank: "SFX_QiYoung"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_047"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_047"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10174"
  animName: "AS_CH_IdleShow_OG_047"
  bank: "VO_QiYoung"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_047"
  stopEvent: "Stop_VO_QiYoung"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10175"
  animName: "AS_CH_Enter_PL_288"
  bank: "SFX_QiChild"
  playEvent: "Play_SFX_AS_CH_Enter_PL_288"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_288"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10176"
  animName: "AS_CH_IdleShow_PL_288"
  bank: "SFX_QiChild"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_288"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_288"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10177"
  animName: "AS_CH_Enter_PL_288"
  bank: "VO_QiChild"
  playEvent: "Play_VO_AS_CH_Enter_PL_288"
  stopEvent: "Stop_VO_QiChild"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10178"
  animName: "AS_CH_IdleShow_PL_288"
  bank: "VO_QiChild"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_288"
  stopEvent: "Stop_VO_QiChild"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10179"
  animName: "AS_CH_Enter_PL_289"
  bank: "SFX_HOKAiLinQYWZ"
  playEvent: "Play_SFX_AS_CH_Enter_PL_289"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_289"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10180"
  animName: "AS_CH_IdleShow_PL_289"
  bank: "SFX_HOKAiLinQYWZ"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_289"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_289"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10181"
  animName: "AS_CH_Enter_PL_289"
  bank: "VO_HOKAiLinQYWZ"
  playEvent: "Play_VO_AS_CH_Enter_PL_289"
  stopEvent: "Stop_VO_HOKAiLinQYWZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10182"
  animName: "AS_CH_IdleShow_PL_289"
  bank: "VO_HOKAiLinQYWZ"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_289"
  stopEvent: "Stop_VO_HOKAiLinQYWZ"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10183"
  animName: "AS_CH_Enter_PL_305"
  bank: "SFX_PL_305_NingYue"
  playEvent: "Play_SFX_AS_CH_Enter_PL_305"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_305"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10184"
  animName: "AS_CH_IdleShow_PL_305"
  bank: "SFX_PL_305_NingYue"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_305"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_305"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10185"
  animName: "AS_CH_Enter_PL_305"
  bank: "VO_PL_305_NingYue"
  playEvent: "Play_VO_AS_CH_Enter_PL_305"
  stopEvent: "Stop_VO_NingYue"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10186"
  animName: "AS_CH_IdleShow_PL_305"
  bank: "VO_PL_305_NingYue"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_305"
  stopEvent: "Stop_VO_NingYue"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10187"
  animName: "AS_CH_Enter_PL_304"
  bank: "SFX_PL_304_ZhaoMing"
  playEvent: "Play_SFX_AS_CH_Enter_PL_304"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_304"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10188"
  animName: "AS_CH_IdleShow_PL_304"
  bank: "SFX_PL_304_ZhaoMing"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_304"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_304"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10189"
  animName: "AS_CH_Enter_PL_304"
  bank: "VO_PL_304_ZhaoMing"
  playEvent: "Play_VO_AS_CH_Enter_PL_304"
  stopEvent: "Stop_VO_ZhaoMing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10190"
  animName: "AS_CH_IdleShow_PL_304"
  bank: "VO_PL_304_ZhaoMing"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_304"
  stopEvent: "Stop_VO_ZhaoMing"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10191"
  animName: "AS_CH_Enter_PL_300"
  bank: "SFX_PL_300_Jeannette"
  playEvent: "Play_SFX_AS_CH_Enter_PL_300"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_300"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10192"
  animName: "AS_CH_IdleShow_PL_300"
  bank: "SFX_PL_300_Jeannette"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_300"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_300"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10193"
  animName: "AS_CH_Enter_PL_300"
  bank: "VO_PL_300_Jeannette"
  playEvent: "Play_VO_AS_CH_Enter_PL_300"
  stopEvent: "Stop_VO_Jeannette"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10194"
  animName: "AS_CH_IdleShow_PL_300"
  bank: "VO_PL_300_Jeannette"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_300"
  stopEvent: "Stop_VO_Jeannette"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10195"
  animName: "AS_CH_Enter_PL_298"
  bank: "SFX_PL_298_Andreea"
  playEvent: "Play_SFX_AS_CH_Enter_PL_298"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_298"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10196"
  animName: "AS_CH_IdleShow_PL_298"
  bank: "SFX_PL_298_Andreea"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_298"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_298"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10197"
  animName: "AS_CH_Enter_PL_298"
  bank: "VO_PL_298_Andreea"
  playEvent: "Play_VO_AS_CH_Enter_PL_298"
  stopEvent: "Stop_VO_Andreea"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10198"
  animName: "AS_CH_IdleShow_PL_298"
  bank: "VO_PL_298_Andreea"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_298"
  stopEvent: "Stop_VO_Andreea"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10199"
  animName: "AS_CH_Enter_PL_297"
  bank: "SFX_PL_297_Cid"
  playEvent: "Play_SFX_AS_CH_Enter_PL_297"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_297"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10200"
  animName: "AS_CH_IdleShow_PL_297"
  bank: "SFX_PL_297_Cid"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_297"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_297"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10201"
  animName: "AS_CH_Enter_PL_297"
  bank: "VO_PL_297_Cid"
  playEvent: "Play_VO_AS_CH_Enter_PL_297"
  stopEvent: "Stop_VO_Cid"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10202"
  animName: "AS_CH_IdleShow_PL_297"
  bank: "VO_PL_297_Cid"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_297"
  stopEvent: "Stop_VO_Cid"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10203"
  animName: "AS_CH_Enter_PL_296"
  bank: "SFX_PL_296_Marin"
  playEvent: "Play_SFX_AS_CH_Enter_PL_296"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_296"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10204"
  animName: "AS_CH_IdleShow_PL_296"
  bank: "SFX_PL_296_Marin"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_296"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_296"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10205"
  animName: "AS_CH_Enter_PL_296"
  bank: "VO_PL_296_Marin"
  playEvent: "Play_VO_AS_CH_Enter_PL_296"
  stopEvent: "Stop_VO_Marin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10206"
  animName: "AS_CH_IdleShow_PL_296"
  bank: "VO_PL_296_Marin"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_296"
  stopEvent: "Stop_VO_Marin"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10208"
  animName: "AS_CH_IdleShow_OG_048"
  bank: "SFX_OG_048_Baize"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_048"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_048"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10210"
  animName: "AS_CH_IdleShow_OG_048"
  bank: "VO_OG_048_Baize"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_048"
  stopEvent: "Stop_VO_Baize"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10212"
  animName: "AS_CH_IdleShow_OG_049"
  bank: "SFX_OG_049_BaizeSP"
  playEvent: "Play_SFX_AS_CH_IdleShow_OG_049"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_OG_049"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10214"
  animName: "AS_CH_IdleShow_OG_049"
  bank: "VO_OG_049_BaizeSP"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_049"
  stopEvent: "Stop_VO_BaizeSP"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10215"
  animName: "AS_CH_Enter_PL_237"
  bank: "SFX_PL_237_HOKXiangYu"
  playEvent: "Play_SFX_AS_CH_Enter_PL_237"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_237"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10216"
  animName: "AS_CH_IdleShow_PL_237"
  bank: "SFX_PL_237_HOKXiangYu"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_237"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_237"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10217"
  animName: "AS_CH_Enter_PL_237"
  bank: "VO_PL_237_HOKXiangYu"
  playEvent: "Play_VO_AS_CH_Enter_PL_237"
  stopEvent: "Stop_VO_HOKXiangYu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10218"
  animName: "AS_CH_IdleShow_PL_237"
  bank: "VO_PL_237_HOKXiangYu"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_237"
  stopEvent: "Stop_VO_HOKXiangYu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10219"
  animName: "AS_CH_Enter_PL_232"
  bank: "SFX_PL_232_HOKYuJi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_232"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_232"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10220"
  animName: "AS_CH_IdleShow_PL_232"
  bank: "SFX_PL_232_HOKYuJi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_232"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_232"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10221"
  animName: "AS_CH_Enter_PL_232"
  bank: "VO_PL_232_HOKYuJi"
  playEvent: "Play_VO_AS_CH_Enter_PL_232"
  stopEvent: "Stop_VO_HOKYuJi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10222"
  animName: "AS_CH_IdleShow_PL_232"
  bank: "VO_PL_232_HOKYuJi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_232"
  stopEvent: "Stop_VO_HOKYuJi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10223"
  animName: "AS_CH_Enter_PL_321"
  bank: "SFX_PL_321_BingYi"
  playEvent: "Play_SFX_AS_CH_Enter_PL_321"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_321"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10224"
  animName: "AS_CH_IdleShow_PL_321"
  bank: "SFX_PL_321_BingYi"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_321"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_321"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10225"
  animName: "AS_CH_Enter_PL_321"
  bank: "VO_PL_321_BingYi"
  playEvent: "Play_VO_AS_CH_Enter_PL_321"
  stopEvent: "Stop_VO_BingYi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10226"
  animName: "AS_CH_IdleShow_PL_321"
  bank: "VO_PL_321_BingYi"
  playEvent: "Play_VO_AS_CH_IdleShow_PL_321"
  stopEvent: "Stop_VO_BingYi"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10227"
  animName: "AS_CH_Enter_PL_322"
  bank: "SFX_PL_322_Xiaohonghu"
  playEvent: "Play_SFX_AS_CH_Enter_PL_322"
  stopEvent: "Stop_SFX_AS_CH_Enter_PL_322"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10228"
  animName: "AS_CH_IdleShow_PL_322"
  bank: "SFX_PL_322_Xiaohonghu"
  playEvent: "Play_SFX_AS_CH_IdleShow_PL_322"
  stopEvent: "Stop_SFX_AS_CH_IdleShow_PL_322"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10229"
  animName: "AS_CH_Enter_PL_322"
  bank: "VO_PL_322_Xiaohonghu"
  playEvent: "Play_VO_AS_CH_Enter_PL_322"
  stopEvent: "Stop_VO_Xiaohonghu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "10230"
  animName: "AS_CH_IdleShow_PL_322"
  bank: "VO_PL_322_Xiaohonghu"
  playEvent: "Play_VO_AS_CH_IdleShow_OG_034"
  stopEvent: "Stop_VO_Xiaohonghu"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30000"
  animName: "AS_CH_XW_Eat"
  bank: "PL_Villa"
  playEvent: "Play_Pl_Villa_Food_Chew"
  playEvent3p: "Play_Pl_Villa_Food_Chew"
  delay: 0.35
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30001"
  animName: "AS_CH_XW_Eat"
  bank: "PL_Villa"
  playEvent: "Play_Pl_Villa_Food_Bite"
  playEvent3p: "Play_Pl_Villa_Food_Bite"
  delay: 1.07
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30002"
  animName: "AS_CH_XW_Snap"
  bank: "PL_Villa"
  playEvent: "Play_Pl_Villa_Snap"
  playEvent3p: "Play_Pl_Villa_Snap"
  delay: 0.42
  is3d: 1
  TransitionDuration: 0.5
  BeStop: 1
}
rows {
  id: "30003"
  fxName: "FX_Home_Put1x1_501"
  bank: "PL_Villa"
  playEvent: "Play_Pl_Villa_Food_Pop"
  playEvent3p: "Play_Pl_Villa_Food_Pop"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30004"
  animName: "AS_CH_XW_Dancing1"
  bank: "PL_Villa"
  playEvent: "Play_AS_CH_XW_Dancing1"
  playEvent3p: "Play_AS_CH_XW_Dancing1"
  stopEvent: "Stop_AS_CH_XW_Dancing1"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30005"
  animName: "AS_CH_XW_Dancing2"
  bank: "PL_Villa"
  playEvent: "Play_AS_CH_XW_Dancing2"
  playEvent3p: "Play_AS_CH_XW_Dancing2"
  stopEvent: "Stop_AS_CH_XW_Dancing2"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30006"
  animName: "AS_CH_XW_Dancing3"
  bank: "PL_Villa"
  playEvent: "Play_AS_CH_XW_Dancing3"
  playEvent3p: "Play_AS_CH_XW_Dancing3"
  stopEvent: "Stop_AS_CH_XW_Dancing3"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30007"
  animName: "AS_CH_XW_Hotspring2_Montage"
  bank: "PL_Villa"
  playEvent: "Play_Obj_Villa_HotSpring_Splash"
  playEvent3p: "Play_Obj_Villa_HotSpring_Splash"
  stopEvent: "Stop_Obj_Villa_HotSpring_Splash"
  delay: 4.58
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30008"
  animName: "SM_FU_Piranha_001_Attack"
  bank: "PL_Villa"
  playEvent: "Play_SM_FU_Piranha_001_Attack"
  playEvent3p: "Play_SM_FU_Piranha_001_Attack"
  stopEvent: "Stop_SM_FU_Piranha_001_Attack"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30009"
  animName: "SM_FU_Piranha_001_Idle"
  bank: "PL_Villa"
  playEvent: "Play_SM_FU_Piranha_001_Idle"
  playEvent3p: "Play_SM_FU_Piranha_001_Idle"
  stopEvent: "Stop_SM_FU_Piranha_001_Idle"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30010"
  animName: "SM_FU_Piranha_001_Sleep"
  bank: "PL_Villa"
  playEvent: "Play_SM_FU_Piranha_001_Sleep"
  playEvent3p: "Play_SM_FU_Piranha_001_Sleep"
  stopEvent: "Stop_SM_FU_Piranha_001_Sleep"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30011"
}
rows {
  id: "30501"
  animName: "AS_CH_XW_BigBow"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_BigBow"
  playEvent3p: "Play_AS_CH_XW_BigBow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30502"
  animName: "AS_CH_XW_Encourage"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Encourage"
  playEvent3p: "Play_AS_CH_XW_Encourage"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30503"
  animName: "AS_CH_XW_Feeding"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Feeding"
  playEvent3p: "Play_AS_CH_XW_Feeding"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30504"
  animName: "AS_CH_XW_Hoeing"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Hoeing"
  playEvent3p: "Play_AS_CH_XW_Hoeing"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30505"
  animName: "AS_CH_XW_Pullup"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Pullup"
  playEvent3p: "Play_AS_CH_XW_Pullup"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30506"
  animName: "AS_CH_XW_Put"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Put"
  playEvent3p: "Play_AS_CH_XW_Put"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30507"
  animName: "AS_CH_XW_Recycle"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Recycle"
  playEvent3p: "Play_AS_CH_XW_Recycle"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30508"
  animName: "AS_CH_XW_Shovel"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Shovel"
  playEvent3p: "Play_AS_CH_XW_Shovel"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30509"
  animName: "AS_CH_XW_Steal"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Steal"
  playEvent3p: "Play_AS_CH_XW_Steal"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30510"
  animName: "AS_CH_XW_Watering"
  bank: "LetsFarm"
  playEvent: "Play_AS_CH_XW_Watering"
  playEvent3p: "Play_AS_CH_XW_Watering"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30511"
  fxName: "FX_Farm_MonthlyCard_Trigger_Gold"
  bank: "LetsFarm"
  playEvent: "Play_LetsFarm_Pray_Big"
  playEvent3p: "Play_LetsFarm_Pray_Big"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30512"
  fxName: "FX_Farm_MonthlyCard_Trigger_Colorful"
  bank: "LetsFarm"
  playEvent: "Play_LetsFarm_Pray_Small"
  playEvent3p: "Play_LetsFarm_Pray_Big"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30513"
  animName: "AS_CH_XW_Snap_Machine"
  bank: "LetsFarm_Room"
  playEvent: "Play_LetsFarm_Room_Processor_Use"
  playEvent3p: "Play_LetsFarm_Room_Processor_Use"
  delay: 0.42
  is3d: 1
  TransitionDuration: 0.5
  BeStop: 1
}
rows {
  id: "30514"
  fxName: "FX_Farm_Resident_Happy"
  isLoop: 1
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Happy"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Happy"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Happy"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30515"
  fxName: "FX_Farm_Resident_Laughter"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Laughter"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Laughter"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Laughter"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30516"
  fxName: "FX_Farm_Resident_Angry"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Angry"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Angry"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Angry"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30517"
  fxName: "FX_Farm_Resident_Weep"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Weep"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Weep"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Weep"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30518"
  fxName: "FX_Farm_Resident_Surprise"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Surprise"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Surprise"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Surprise"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30519"
  fxName: "FX_Farm_Resident_Shock"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Shock"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Shock"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Shock"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30520"
  fxName: "FX_Farm_Resident_Query"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Query"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Query"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Query"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30521"
  fxName: "FX_Farm_Resident_Embarrassed"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Embarrassed"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Embarrassed"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Embarrassed"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30522"
  fxName: "FX_Farm_Resident_Frustrated"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Frustrated"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Frustrated"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Frustrated"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30523"
  fxName: "FX_Farm_Resident_Tired"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Tired"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Tired"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Tired"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30524"
  fxName: "FX_Farm_SNBD_Shengji"
  bank: "LetsFarm_Plant"
  playEvent: "Play_LetsFarm_OverlordFlower_Shengji"
  playEvent3p: "Play_LetsFarm_OverlordFlower_Shengji"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30525"
  fxName: "FX_Farm_SNBD_Collide"
  bank: "LetsFarm_Plant"
  playEvent: "Play_LetsFarm_OverlordFlower_Collide"
  playEvent3p: "Play_LetsFarm_OverlordFlower_Collide"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30526"
  fxName: "FX_Farm_SNBD_Get_501"
  bank: "LetsFarm_Plant"
  playEvent: "Play_LetsFarm_OverlordFlower_Get_501"
  playEvent3p: "Play_LetsFarm_OverlordFlower_Get_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30527"
  fxName: "FX_Farm_LuoLuo_Give_501"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_LL_Give_501"
  playEvent3p: "Play_LetsFarm_NPC_LL_Give_501"
  stopEvent: "Stop_LetsFarm_NPC_LL_Give_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30528"
  fxName: "FX_Farm_Pets_Flower"
  isLoop: 1
  bank: "LetsFarm_Pet"
  playEvent: "Play_LetsFarm_Pet_Flower"
  playEvent3p: "Play_LetsFarm_Pet_Flower"
  stopEvent: "Stop_LetsFarm_Pet_Flower"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30529"
  fxName: "FX_Farm_FishComposition_503"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_LL_FishComposition_503"
  playEvent3p: "Play_LetsFarm_NPC_LL_FishComposition_503"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30530"
  fxName: "FX_Farm_Fishing_ThrowBomb_502"
  bank: "LetsFarm_Fishing"
  playEvent: "Play_LetsFarm_Fishing_ThrowBomb_502"
  playEvent3p: "Play_LetsFarm_Fishing_ThrowBomb_502"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30531"
  fxName: "FX_Farm_Levelup8x8_502"
  bank: "LetsFarm"
  playEvent: "Play_LetsFarm_Farm_Levelup8x8_502"
  playEvent3p: "Play_LetsFarm_Farm_Levelup8x8_502"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30532"
  fxName: "FX_Farm_Levelup8x8_501"
  bank: "LetsFarm"
  playEvent: "Play_Obj_Villa_Farm_Upgrade"
  playEvent3p: "Play_Obj_Villa_Farm_Upgrade"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30533"
  fxName: "FX_Farm_AgriculturalAltar_Purple"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_Purple_S"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_Purple_S"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30534"
  fxName: "FX_Farm_AgriculturalAltar_Gold"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_Gold_M"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_Gold_M"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30535"
  fxName: "FX_Farm_AgriculturalAltar_Colorful"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_Colorful_L"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_Colorful_L"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30536"
  fxName: "FX_Farm_AgriculturalAltar_Trigger"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_Trigger"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_Trigger"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30537"
  fxName: "FX_Farm_DragonUAV_Pullup_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Pullup_Air"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Pullup_Air"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30538"
  fxName: "FX_Farm_DragonUAV_Pullup_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Pullup_Ground"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Pullup_Ground"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30539"
  fxName: "FX_Farm_DragonUAV_Watering_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Watering_Thunder"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Watering_Thunder"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30540"
  fxName: "FX_Farm_DragonUAV_Watering_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Watering_Ground"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Watering_Ground"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30541"
  fxName: "FX_Farm_DragonUAV_Hoeing_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Hoeing_Air"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Hoeing_Air"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30542"
  fxName: "FX_Farm_DragonUAV_Hoeing_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Hoeing_Ground"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Hoeing_Ground"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30543"
  fxName: "FX_Farm_DragonUAV_Recycle_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Recycle_Air"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Recycle_Air"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30544"
  fxName: "FX_Farm_DragonUAV_Recycle_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Recycle_Ground"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Recycle_Ground"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30545"
  fxName: "FX_Farm_DragonUAV_Cultivation_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Cultivation_Air"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Cultivation_Air"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30546"
  fxName: "FX_Farm_DragonUAV_Cultivation_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Cultivation_Ground"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Cultivation_Ground"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30547"
  fxName: "FX_Farm_DragonUAV_Encourage_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Encourage_Air"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Encourage_Air"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30548"
  fxName: "FX_Farm_DragonUAV_Encourage_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Encourage_Ground"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Encourage_Ground"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30549"
  fxName: "FX_Farm_DragonUAV_Feeding_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Feeding_Air"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Feeding_Air"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30550"
  fxName: "FX_Farm_DragonUAV_Feeding_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Feeding_Ground"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Feeding_Ground"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30551"
  fxName: "FX_Farm_DragonUAV_FishingBomb_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Bomb"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Bomb"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30552"
  fxName: "FX_Farm_DragonUAV_FishingBomb_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_Cloud_Bomb"
  playEvent3p: "Play_LetsFarm_Drone_Cloud_Bomb"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30553"
  fxName: "FX_Farm_DragonUAV_Machine_Get"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Machine_Get"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Machine_Get"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30554"
  fxName: "FX_Farm_DragonUAV_Machine_Put"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Machine_Put"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Machine_Put"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30555"
  fxName: "FX_Farm_HotWell_Ripple_Walk"
  bank: "LetsFarm"
  playEvent: "Play_LetsFarm_Player_HotWell_Move"
  playEvent3p: "Play_LetsFarm_Player_HotWell_Move"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30556"
  fxName: "FX_Farm_HotWell_Ripple_Jump"
  bank: "LetsFarm"
  playEvent: "Play_LetsFarm_Player_HotWell_ Land"
  playEvent3p: "Play_LetsFarm_Player_HotWell_ Land"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30557"
  fxName: "FX_Farm_XS_Farmland"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30558"
  fxName: "FX_Farm_XS_LivestockFarm"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_LivestockFarm"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_LivestockFarm"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30559"
  fxName: "FX_Farm_XS_Fishpond"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Fishpond"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Fishpond"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30560"
  fxName: "FX_Farm_XS_Home"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Home"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Home"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30561"
  fxName: "FX_Farm_DragonUAV_Change"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_Change"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_Change"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30562"
  fxName: "FX_Farm_HotWell_Buff_Get"
  bank: "LetsFarm"
  playEvent: "Play_LetsFarm_Player_HotWell_BuffGet_1P"
  playEvent3p: "Play_LetsFarm_Player_HotWell_BuffGet_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30563"
  fxName: "FX_Farm_XS_Farmland_KMFC"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_KMFC"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_KMFC"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30564"
  fxName: "FX_Farm_XS_Farmland_HSZ"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_HSZ"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_HSZ"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30565"
  fxName: "FX_Farm_XS_LivestockFarm_HSZ"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_HSZ"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_HSZ"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30566"
  fxName: "FX_Farm_XS_Fishpond_HSZ"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Fishpond_HSZ"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Fishpond_HSZ"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30567"
  fxName: "FX_Farm_XS_Home_HSZ"
  bank: "LetsFarm_Obj"
  playEvent: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_HSZ"
  playEvent3p: "Play_LetsFarm_Obj_AgriculturalAltar_XS_Farmland_HSZ"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30568"
  fxName: "FX_Farm_UAV_Reclaim_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_FX_Farm_UAV_Reclaim_501"
  playEvent3p: "Play_LetsFarm_Drone_FX_Farm_UAV_Reclaim_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30569"
  fxName: "FX_Farm_UAV_Reclaim_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_FX_Farm_UAV_Reclaim_502"
  playEvent3p: "Play_LetsFarm_Drone_FX_Farm_UAV_Reclaim_502"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30570"
  fxName: "FX_Farm_UAV004_Reclaim_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_Reclaim_501"
  playEvent3p: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_Reclaim_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30571"
  fxName: "FX_Farm_UAV004_Reclaim_502"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_Reclaim_502"
  playEvent3p: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_Reclaim_502"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30572"
  fxName: "FX_Farm_DragonUAV_Reclaim_501"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_Reclaim_501"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_Reclaim_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30573"
  fxName: "FX_Farm_KylinEgg_dandao"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_KylinEgg_dandao"
  playEvent3p: "Play_LetsFarm_NPC_KylinEgg_dandao"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30574"
  fxName: "FX_Farm_KylinEgg_Hit"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_KylinEgg_Hit"
  playEvent3p: "Play_LetsFarm_NPC_KylinEgg_Hit"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30575"
  fxName: "FX_Farm_KylinEgg_xianhuo_one"
  isLoop: 1
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_KylinEgg_xianhuo_one"
  playEvent3p: "Play_LetsFarm_NPC_KylinEgg_xianhuo_one"
  stopEvent: "Stop_LetsFarm_NPC_KylinEgg_xianhuo_one"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30576"
  fxName: "FX_Farm_Kylin_Return_Smoke"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_Return_Smoke"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_Return_Smoke"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_Return_Smoke"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30577"
  fxName: "FX_Farm_Kylin_Return_502"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_Return_502"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_Return_502"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_Return_502"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30578"
  fxName: "FX_Farm_Kylin_Return_501"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_Return_501"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_Return_501"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_Return_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30579"
  fxName: "FX_Farm_KylinEgg_xianhuo_IdleEnd"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_XianHuo_IdleEnd"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_XianHuo_IdleEnd"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_XianHuo_IdleEnd"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30580"
  fxName: "FX_Farm_KylinEgg_Smoke"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_Smoke"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_Smoke"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_KylinEgg_Smoke"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30581"
  fxName: "FX_Farm_KylinEgg_Hatch"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_Hatch"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_Hatch"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_KylinEgg_Hatch"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30582"
  fxName: "FX_Farm_KylinEgg_StartIdle"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_StartIdle"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_StartIdle"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_KylinEgg_StartIdle"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30583"
  fxName: "FX_Farm_KylinEgg_fuhua"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_FuHua"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_KylinEgg_FuHua"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_KylinEgg_FuHua"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30584"
  fxName: "FX_Farm_KylinEgg_xianhuo_Idle"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Kylin_FX_XianHuo_Idle"
  playEvent3p: "Play_LetsFarm_NPC_Kylin_FX_XianHuo_Idle"
  stopEvent: "Stop_LetsFarm_NPC_Kylin_FX_XianHuo_Idle"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30585"
  fxName: "FX_Farm_UAV_shouqian"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_FX_Farm_UAV_ShouQian"
  playEvent3p: "Play_LetsFarm_Drone_FX_Farm_UAV_ShouQian"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30586"
  fxName: "FX_Farm_UAV004_shouqian"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_ShouQian"
  playEvent3p: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_ShouQian"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30587"
  fxName: "FX_Farm_DragonUAV_shouqian"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_ShouQian"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_ShouQian"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30588"
  fxName: "FX_Farm_UAV_jiedai"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_FX_Farm_UAV_JieDai"
  playEvent3p: "Play_LetsFarm_Drone_FX_Farm_UAV_JieDai"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30589"
  fxName: "FX_Farm_UAV_yuyue"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_FX_Farm_UAV_YuYue"
  playEvent3p: "Play_LetsFarm_Drone_FX_Farm_UAV_YuYue"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30590"
  fxName: "FX_Farm_UAV_xucai"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_FX_Farm_UAV_XuCai"
  playEvent3p: "Play_LetsFarm_Drone_FX_Farm_UAV_XuCai"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30591"
  fxName: "FX_Farm_UAV004_jiedai"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_JieDai"
  playEvent3p: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_JieDai"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30592"
  fxName: "FX_Farm_UAV004_yuyue"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_YuYue"
  playEvent3p: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_YuYue"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30593"
  fxName: "FX_Farm_UAV004_xucai"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_XuCai"
  playEvent3p: "Play_LetsFarm_Drone_Cloud_FX_Farm_UAV004_XuCai"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30594"
  fxName: "FX_Farm_DragonUAV_jiedai"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_JieDai"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_JieDai"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30595"
  fxName: "FX_Farm_DragonUAV_yuyue"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_YuYue"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_YuYue"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30596"
  fxName: "FX_Farm_DragonUAV_xucai"
  bank: "LetsFarm_Drone"
  playEvent: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_XuCai"
  playEvent3p: "Play_LetsFarm_Drone_DragonUAV_FX_Farm_DragonUAV_XuCai"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30597"
  fxName: "FX_Farm_Change_Resident"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Change_Resident"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Change_Resident"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Change_Resident"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30598"
  fxName: "FX_Farm_Resident_Favorability"
  bank: "LetsFarm_NPC"
  playEvent: "Play_LetsFarm_NPC_Cmn_Resident_Favorability"
  playEvent3p: "Play_LetsFarm_NPC_Cmn_Resident_Favorability"
  stopEvent: "Stop_LetsFarm_NPC_Cmn_Resident_Favorability"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30599"
  fxName: "FX_Farm_SharkFishStall_006"
  bank: "LetsFarm_Obj_BuComm_SharkFishStall"
  playEvent: "Play_FX_Farm_Shark_FishStall_006"
  playEvent3p: "Play_FX_Farm_Shark_FishStall_006"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30600"
  fxName: "FX_Farm_SharkFishStall_003"
  bank: "LetsFarm_Obj_BuComm_SharkFishStall"
  playEvent: "Play_FX_Farm_Shark_FishStall_003"
  playEvent3p: "Play_FX_Farm_Shark_FishStall_003"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30601"
  fxName: "FX_Farm_SharkFishStall_002"
  bank: "LetsFarm_Obj_BuComm_SharkFishStall"
  playEvent: "Play_FX_Farm_Shark_FishStall_002"
  playEvent3p: "Play_FX_Farm_Shark_FishStall_002"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30751"
  animName: "SM_Farm_Keji_001_Attack"
  bank: "LetsFarm_Pet"
  playEvent: "Play_LetsFarm_Pet_SM_Farm_Keji_001_Attack"
  playEvent3p: "Play_LetsFarm_Pet_SM_Farm_Keji_001_Attack"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30752"
  fxName: "FX_Farm_Pets_Favorability"
  bank: "LetsFarm_Pet"
  playEvent: "Play_LetsFarm_Pet_LoveHeart"
  playEvent3p: "Play_LetsFarm_Pet_LoveHeart"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30753"
  fxName: "FX_Farm_Pets_Change_501"
  bank: "LetsFarm_Pet"
  playEvent: "Play_LetsFarm_Pet_Change"
  playEvent3p: "Play_LetsFarm_Pet_Change"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "30754"
}
rows {
  id: "30755"
}
rows {
  id: "30756"
}
rows {
  id: "32000"
  fxName: "FX_JS_Skiing_Right_518"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Skiing_Slope_Debris"
  playEvent3p: "Play_PL_LR_Skiing_Slope_Debris_3P"
  stopEvent: "Stop_PL_LR_Skiing_Slope_Debris"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32001"
  fxName: "FX_JS_Skiing_Left_517"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Skiing_Slope_Debris"
  playEvent3p: "Play_PL_LR_Skiing_Slope_Debris_3P"
  stopEvent: "Stop_PL_LR_Skiing_Slope_Debris"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32002"
  fxName: "FX_JS_Accelerator_Trail_520"
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Skiing_SpeedUp_Accelerator"
  playEvent3p: "Play_PL_LR_Skiing_SpeedUp_Accelerator_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32003"
  animName: "AS_CH_XW_Bike_Move_Slide"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_TireSpinning"
  stopEvent: "Stop_PL_LR_Cycling_TireSpinning"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32004"
  animName: "AS_CH_XW_Bike_Move_Slide_L"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_TireSpinning"
  stopEvent: "Stop_PL_LR_Cycling_TireSpinning"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32005"
  animName: "AS_CH_XW_Bike_Move_Slide_R"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_TireSpinning"
  stopEvent: "Stop_PL_LR_Cycling_TireSpinning"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32006"
  animName: "AS_CH_XW_Bike_PowerUP_Slide"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_TireSpinning"
  stopEvent: "Stop_PL_LR_Cycling_TireSpinning"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32007"
  animName: "AS_CH_XW_Bike_PowerUP_Slide_L"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_TireSpinning"
  stopEvent: "Stop_PL_LR_Cycling_TireSpinning"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32008"
  animName: "AS_CH_XW_Bike_PowerUP_Slide_R"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_TireSpinning"
  stopEvent: "Stop_PL_LR_Cycling_TireSpinning"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32009"
  animName: "AS_CH_XW_Bike_Backward_Slide_HeadF"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Reverse"
  stopEvent: "Stop_PL_LR_Cycling_Reverse"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32010"
  animName: "AS_CH_XW_Bike_Backward_Slide_HeadL"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Reverse"
  stopEvent: "Stop_PL_LR_Cycling_Reverse"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32011"
  animName: "AS_CH_XW_Bike_Backward_Slide_HeadR"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Reverse"
  stopEvent: "Stop_PL_LR_Cycling_Reverse"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32012"
  animName: "AS_CH_XW_Bike_Stop_TurnL_Loop"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32013"
  animName: "AS_CH_XW_Bike_Stop_TurnR_Loop"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32014"
  animName: "AS_CH_XW_Bike_SideSlip_L"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32015"
  animName: "AS_CH_XW_Bike_SideSlip_R"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32016"
  animName: "AS_CH_XW_Bike_Move_Slide_L"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32017"
  animName: "AS_CH_XW_Bike_Move_Slide_R"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32018"
  animName: "AS_CH_XW_Bike_PowerUP_Slide_L"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "32019"
  animName: "AS_CH_XW_Bike_PowerUP_Slide_R"
  isLoop: 1
  bank: "LetsRide_Skiing"
  playEvent: "Play_PL_LR_Cycling_Turn"
  stopEvent: "Stop_PL_LR_Cycling_Turn"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "33000"
  animName: "AS_NPC_XINIU_Move_Loop_001"
  isLoop: 1
  bank: "Obj_GamePlay_Rhino"
  playEvent: "Play_Obj_GamePlay_Rhino_Move"
  playEvent3p: "Play_Obj_GamePlay_Rhino_Move"
  stopEvent: "Stop_Obj_GamePlay_Rhino_Move"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "34000"
  animName: "AS_CH_RobotDeath_001"
  bank: "Obj_GamePlay_WoodenMen"
  playEvent: "Play_Obj_GamePlay_WoodenMen_Eliminate"
  playEvent3p: "Play_Obj_GamePlay_WoodenMen_Eliminate_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "34501"
  animName: "AS_CH_RhythmCity_MasterBeCaugh_001"
  bank: "Obj_GamePlay_PacMan"
  playEvent: "Obj_GamePlay_PacMan_Guard_Catched"
  playEvent3p: "Obj_GamePlay_PacMan_Guard_Catched"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47000"
  animName: "AS_CH_RedPocket_Open_001"
  isLoop: 1
  bank: "PL_Community"
  playEvent: "Play_AS_CH_RedPocket_Open_001"
  playEvent3p: "Play_AS_CH_RedPocket_Open_001"
  stopEvent: "Stop_AS_CH_RedPocket_Open_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47001"
  animName: "AS_CH_RedPocket_PutBack_001"
  isLoop: 1
  bank: "PL_Community"
  playEvent: "Play_AS_CH_RedPocket_PutBack_001"
  playEvent3p: "Play_AS_CH_RedPocket_PutBack_001"
  stopEvent: "Stop_AS_CH_RedPocket_PutBack_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47002"
  animName: "AS_CH_RedPocket_PutDown_001"
  isLoop: 1
  bank: "PL_Community"
  playEvent: "Play_AS_CH_RedPocket_PutDown_001"
  playEvent3p: "Play_AS_CH_RedPocket_PutDown_001"
  stopEvent: "Stop_AS_CH_RedPocket_PutDown_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47003"
  animName: "AS_CH_RedPocket_takeOut_001"
  isLoop: 1
  bank: "PL_Community"
  playEvent: "Play_AS_CH_RedPocket_takeOut_001"
  playEvent3p: "Play_AS_CH_RedPocket_takeOut_001"
  stopEvent: "Stop_AS_CH_RedPocket_takeOut_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47004"
  animName: "AS_CH_Social_Wish_001"
  isLoop: 1
  bank: "PL_Community"
  playEvent: "Play_AS_CH_Social_Wish_001"
  playEvent3p: "Play_AS_CH_Social_Wish_001"
  stopEvent: "Stop_AS_CH_Social_Wish_001"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47005"
}
rows {
  id: "47006"
}
rows {
  id: "47007"
}
rows {
  id: "47008"
}
rows {
  id: "47009"
}
rows {
  id: "47010"
  animName: "AS_CH_Mengqi_001_Idle"
  bank: "Obj_Community_MengQi"
  playEvent: "Play_Obj_Community_MengQi_Idle01"
  playEvent3p: "Play_Obj_Community_MengQi_Idle01"
  stopEvent: "Stop_Obj_Community_MengQi_Idle01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47011"
  animName: "AS_CH_Mengqi_001_IdleShow01"
  bank: "Obj_Community_MengQi"
  playEvent: "Play_Obj_Community_MengQi_Idle02"
  playEvent3p: "Play_Obj_Community_MengQi_Idle02"
  stopEvent: "Stop_Obj_Community_MengQi_Idle02"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47012"
  animName: "AS_CH_Mengqi_001_IdleShow02"
  bank: "Obj_Community_MengQi"
  playEvent: "Play_Obj_Community_MengQi_Idle03"
  playEvent3p: "Play_Obj_Community_MengQi_Idle03"
  stopEvent: "Stop_Obj_Community_MengQi_Idle03"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47013"
  animName: "AS_Prop_MonkiBubble_Born_001"
  bank: "Obj_Community_MengQi"
  playEvent: "Play_Obj_Community_MengQi_Bubble_Big"
  playEvent3p: "Play_Obj_Community_MengQi_Bubble_Big"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "47014"
  animName: "AS_CH_Into_Bubble_001"
  bank: "Obj_Community_MengQi"
  playEvent: "Play_Obj_Community_MengQi_Bubble_In"
  playEvent3p: "Play_Obj_Community_MengQi_Bubble_In"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "47015"
  animName: "AS_CH_Into_Bubble_002"
  bank: "Obj_Community_MengQi"
  playEvent: "Play_Obj_Community_MengQi_Bubble_In"
  playEvent3p: "Play_Obj_Community_MengQi_Bubble_In"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "47016"
  animName: "AS_Prop_MonkiBubble_Destroy_001"
  bank: "Obj_Community_MengQi"
  playEvent: "Play_Obj_Community_MengQi_Bubble_Break"
  playEvent3p: "Play_Obj_Community_MengQi_Bubble_Break"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "47017"
  animName: "AS_player_appear_001_Shuttle_002"
  bank: "Obj_Community_XingSuo"
  playEvent: "Play_Obj_Community_XingSuo_Bule_SummonUp"
  playEvent3p: "Play_Obj_Community_XingSuo_Bule_SummonUp_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47018"
  animName: "AS_player_disappear_001_Shuttle_002"
  bank: "Obj_Community_XingSuo"
  playEvent: "Play_Obj_Community_XingSuo_Bule_Leave"
  playEvent3p: "Play_Obj_Community_XingSuo_Bule_Leave_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47019"
  animName: "AS_player_appear_001_Shuttle_001"
  bank: "Obj_Community_XingSuo"
  playEvent: "Play_Obj_Community_XingSuo_Purple_SummonUp"
  playEvent3p: "Play_Obj_Community_XingSuo_Purple_SummonUp_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47020"
  animName: "AS_player_disappear_001_Shuttle_001"
  bank: "Obj_Community_XingSuo"
  playEvent: "Play_Obj_Community_XingSuo_Purple_Leave"
  playEvent3p: "Play_Obj_Community_XingSuo_Purple_Leave_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47021"
  animName: "AS_appear_001_Shuttle_003"
  bank: "Obj_Community_Shuttle"
  playEvent: "Play_Obj_Community_Shuttle_Purple_SummonUp"
  playEvent3p: "Play_Obj_Community_Shuttle_Purple_SummonUp_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47022"
  animName: "AS_disappear_001_Shuttle_003"
  bank: "Obj_Community_Shuttle"
  playEvent: "Play_Obj_Community_Shuttle_Purple_Leave"
  playEvent3p: "Play_Obj_Community_Shuttle_Purple_Leave"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "47023"
}
rows {
  id: "47024"
}
rows {
  id: "47025"
}
rows {
  id: "47026"
}
rows {
  id: "36000"
  animName: "AS_VH_IdleShow_001_Vehicle_001"
  bank: "Obj_Vehicle_Loong"
  playEvent: "Play_Obj_Vehicle_Loong_IdleShow"
  playEvent3p: "Play_Obj_Vehicle_Loong_IdleShow"
  stopEvent: "Stop_Obj_Vehicle_Loong_IdleShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
}
rows {
  id: "36001"
  animName: "AS_VH_IdleShow_001_Vehicle_001_HP01"
  bank: "Obj_Vehicle_Loong"
  playEvent: "Play_Obj_Vehicle_Loong_IdleShow"
  playEvent3p: "Play_Obj_Vehicle_Loong_IdleShow"
  stopEvent: "Stop_Obj_Vehicle_Loong_IdleShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36002"
  animName: "AS_VH_IdleShow_001_Vehicle_001_HP02"
  bank: "Obj_Vehicle_Loong"
  playEvent: "Play_Obj_Vehicle_Loong_IdleShow"
  playEvent3p: "Play_Obj_Vehicle_Loong_IdleShow"
  stopEvent: "Stop_Obj_Vehicle_Loong_IdleShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36003"
  animName: "AS_VH_IdleShow_001_Vehicle_001_HP03"
  bank: "Obj_Vehicle_Loong"
  playEvent: "Play_Obj_Vehicle_Loong_IdleShow"
  playEvent3p: "Play_Obj_Vehicle_Loong_IdleShow"
  stopEvent: "Stop_Obj_Vehicle_Loong_IdleShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36004"
  animName: "AS_VH_Vehicle_001_Jet_001"
  bank: "Obj_Vehicle_Loong"
  playEvent: "Play_Obj_Vehicle_Loong_Jet"
  playEvent3p: "Play_Obj_Vehicle_Loong_Jet"
  stopEvent: "Stop_Obj_Vehicle_Loong_Jet"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36005"
  animName: "AS_VH_Vehicle_002_Idleshow_001"
  bank: "Obj_Vehicle_Kun"
  playEvent: "Play_Obj_Vehicle_Kun_IdelShow"
  playEvent3p: "Play_Obj_Vehicle_Kun_IdelShow"
  stopEvent: "Stop_Obj_Vehicle_Kun_IdelShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36006"
  animName: "AS_VH_Vehicle_002_Idleshow_001_HP01"
  bank: "Obj_Vehicle_Kun"
  playEvent: "Play_Obj_Vehicle_Kun_IdelShow"
  playEvent3p: "Play_Obj_Vehicle_Kun_IdelShow"
  stopEvent: "Stop_Obj_Vehicle_Kun_IdelShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36007"
  animName: "AS_VH_Vehicle_002_Idleshow_001_HP02"
  bank: "Obj_Vehicle_Kun"
  playEvent: "Play_Obj_Vehicle_Kun_IdelShow"
  playEvent3p: "Play_Obj_Vehicle_Kun_IdelShow"
  stopEvent: "Stop_Obj_Vehicle_Kun_IdelShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36008"
  animName: "AS_VH_Vehicle_002_Jet_001"
  bank: "Obj_Vehicle_Kun"
  playEvent: "Play_Obj_Vehicle_Kun_Jet"
  playEvent3p: "Play_Obj_Vehicle_Kun_Jet"
  stopEvent: "Stop_Obj_Vehicle_Kun_Jet"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36009"
  animName: "AS_VH_Vehicle_003_Jet_001"
  bank: "Obj_Vehicle_Flower"
  playEvent: "Play_Obj_Vehicle_Flower_Start"
  playEvent3p: "Play_Obj_Vehicle_Flower_Start"
  stopEvent: "Stop_Obj_Vehicle_Flower_Start"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36010"
  animName: "AS_VH_Vehicle_003_ldleshow_001"
  bank: "Obj_Vehicle_Flower"
  playEvent: "Play_Obj_Vehicle_Flower_IdleShow"
  playEvent3p: "Play_Obj_Vehicle_Flower_IdleShow"
  stopEvent: "Stop_Obj_Vehicle_Flower_IdleShow"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36011"
  fxName: "FX_SpringFestival_Fireworks_001"
  bank: "Obj_Community_S10_SpringFestival"
  playEvent: "Play_Obj_Community_S10_SpringFestival_Fireworks"
  playEvent3p: "Play_Obj_Community_S10_SpringFestival_Fireworks"
  stopEvent: "Stop_Obj_Community_S10_SpringFestival_Fireworks"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36012"
  fxName: "FX_SpringFestival_Fireworks_003"
  bank: "Obj_Community_S10_SpringFestival"
  playEvent: "Play_Obj_Community_S10_SpringFestival_ApertureSpread"
  playEvent3p: "Play_Obj_Community_S10_SpringFestival_ApertureSpread"
  stopEvent: "Stop_Obj_Community_S10_SpringFestival_ApertureSpread"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "36013"
  fxName: "FX_SpringFestival_Fireworks_002"
  bank: "Obj_Community_S10_SpringFestival"
  playEvent: "Play_Obj_Community_S10_SpringFestival_ApertureExplosion"
  playEvent3p: "Play_Obj_Community_S10_SpringFestival_ApertureExplosion"
  stopEvent: "Stop_Obj_Community_S10_SpringFestival_ApertureExplosion"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "40000"
}
rows {
  id: "40001"
}
rows {
  id: "40002"
}
rows {
  id: "40003"
}
rows {
  id: "40004"
}
rows {
  id: "40005"
}
rows {
  id: "40006"
}
rows {
  id: "40007"
}
rows {
  id: "40008"
}
rows {
  id: "40009"
}
rows {
  id: "45001"
  fxName: "FX_Farm_Cook_GoldCoins_501"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_UIHUD_GoldCoins_501"
  playEvent3p: "Play_LetsFarm_Cook_UIHUD_GoldCoins_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45002"
  fxName: "FX_Farm_Cook_GoldCoins_502"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_UIHUD_GoldCoins_502"
  playEvent3p: "Play_LetsFarm_Cook_UIHUD_GoldCoins_502"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45003"
  fxName: "FX_Farm_Cook_GoldCoins_503"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_UIHUD_GoldCoins_503"
  playEvent3p: "Play_LetsFarm_Cook_UIHUD_GoldCoins_503"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45004"
  fxName: "FX_Farm_Cook_GoldCoins_504"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_UIHUD_GoldCoins_504"
  playEvent3p: "Play_LetsFarm_Cook_UIHUD_GoldCoins_504"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45005"
  fxName: "FX_Farm_Cook_Anger_501"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_NPC_FX_Farm_Cook_Anger_501"
  playEvent3p: "Play_LetsFarm_Cook_NPC_FX_Farm_Cook_Anger_501"
  stopEvent: "Stop_LetsFarm_Cook_NPC_FX_Farm_Cook_Anger_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45006"
  fxName: "FX_Farm_Cook_Anger_502"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_NPC_FX_Farm_Cook_Anger_502"
  playEvent3p: "Play_LetsFarm_Cook_NPC_FX_Farm_Cook_Anger_502"
  stopEvent: "Stop_LetsFarm_Cook_NPC_FX_Farm_Cook_Anger_502"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45007"
  fxName: "FX_Farm_Cook_Happy"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_NPC_FX_Farm_Cook_Happy"
  playEvent3p: "Play_LetsFarm_Cook_NPC_FX_Farm_Cook_Happy"
  stopEvent: "Stop_LetsFarm_Cook_NPC_FX_Farm_Cook_Happy"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45008"
  fxName: "FX_Farm_Cook_Cooking_501"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_NPC_Cooking_501"
  playEvent3p: "Play_LetsFarm_Cook_NPC_Cooking_501"
  stopEvent: "Stop_LetsFarm_Cook_NPC_Cooking_501"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45009"
}
rows {
  id: "45010"
}
rows {
  id: "45011"
  fxName: "FX_Farm_Cook_Put"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_NPC_ChifStir_1_Food"
  playEvent3p: "Play_LetsFarm_Cook_NPC_ChifStir_1_Food"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "45012"
  fxName: "FX_Farm_Cook_Levelup"
  bank: "LetsFarm_Cook"
  playEvent: "Play_LetsFarm_Cook_UIHUD_Levelup"
  playEvent3p: "Play_LetsFarm_Cook_UIHUD_Levelup"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9016"
  animName: "AS_CH_ChangeCloth_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_SEQ_Tutorial_S012_Avatar_Born"
  stopEvent: "Stop_SEQ_Tutorial_S012_Avatar_Born"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9017"
  animName: "AS_CH_ChooseMale_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_SEQ_Tutorial_S013_Avatar_Male"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9018"
  animName: "AS_CH_ChooseFemale_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_SEQ_Tutorial_S014_Avatar_Female"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9019"
  animName: "AS_CH_ChooseSecret 001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_SEQ_Tutorial_S014_Avatar_Generic"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9020"
  animName: "AS_CH_ChangeCloth_002"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_CH_ChangeCloth_002"
  stopEvent: "Stop_AS_CH_ChangeCloth_002"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9021"
  animName: "AS_CH_ChangeCloth_003"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_CH_ChangeCloth_003"
  stopEvent: "Stop_AS_CH_ChangeCloth_003"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9022"
  animName: "AS_CH_ChangeCloth_004"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_CH_ChangeCloth_004"
  stopEvent: "Stop_AS_CH_ChangeCloth_004"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "9023"
  animName: "AS_CH_Celebration_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_CH_Celebration_001"
  stopEvent: "Stop_AS_CH_Celebration_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90000"
  animName: "AS_NPC004_CheerUp_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_CheerUp_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90001"
  animName: "AS_NPC004_Explain_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Explain_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90002"
  animName: "AS_NPC004_Explain_Speak_01"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Explain_Speak_01"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90003"
  animName: "AS_NPC004_GiveApplication_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_GiveApplication_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90004"
  animName: "AS_NPC004_GiveMount_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_GiveMount_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90005"
  animName: "AS_NPC004_Go_01"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Go_01"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90006"
  animName: "AS_NPC004_Move_Fall_01"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Move_Fall_01"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90007"
  animName: "AS_NPC004_Move_Speak_01"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Move_Speak_01"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90008"
  animName: "AS_NPC004_Move_Win_01_End"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Move_Win_01_End"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90009"
  animName: "AS_NPC004_Move_Win_01_Start"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Move_Win_01_Start"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90010"
  animName: "AS_NPC004_Notice_01"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Notice_01"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90011"
  animName: "AS_NPC004_SayHello_01"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_SayHello_01"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90012"
  animName: "AS_NPC004_Show_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Show_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90013"
  animName: "AS_NPC004_GiveMount_001_Idle"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_GiveMount_001_Idle"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90014"
  animName: "AS_NPC004_GiveMount_Transition_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_GiveMount_Transition_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90015"
  animName: "AS_NPC004_Left"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Left"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90016"
  animName: "AS_NPC004_Move_001"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Move_001"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90017"
  animName: "AS_NPC004_Move_Win_01_Loop"
  bank: "SEQ_Tutorial"
  playEvent: "Play_AS_NPC004_Move_Win_01_Loop"
  stopEvent: "Stop_AS_NPC1_Move_Win_01_Loop"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "90018"
  animName: "AS_CH_DieOut_001"
  bank: "SEQ_StandbyStage"
  playEvent: "Play_SEQ_StandbyStage_S070_Elimitation"
  delay: 0.01
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35000"
  animName: "AS_011Crab_Run"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Move"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Move_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35001"
  animName: "AS_011Crab_Run"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Move"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Move_3P"
  delay: 0.13
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35002"
  animName: "AS_011Crab_Walk"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Move"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Move_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35003"
  animName: "AS_011Crab_Walk"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Move"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Move_3P"
  delay: 0.26
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35004"
  animName: "AS_011Crab_HitF"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Hit"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35005"
  animName: "AS_011Crab_HitB"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Hit"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35006"
  animName: "AS_011Crab_HitL"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Hit"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35007"
  animName: "AS_011Crab_HitR"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Hit"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35008"
  animName: "AS_011Crab_Fire01"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Attack"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Attack_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35009"
  animName: "AS_011Crab_Fire02"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Attack"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Attack_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35010"
  animName: "AS_011Crab_DeathF"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Death"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Death_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35011"
  animName: "AS_011Crab_DeathB"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_PinkCrab_Death"
  playEvent3p: "Play_LetsTower_Enemy_PinkCrab_Death_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35012"
  animName: "AS_TYC_Monster_002_Fire01"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Attack"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Attack_3P"
  delay: 0.79
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35013"
  animName: "AS_TYC_Monster_002_Fire01"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Attack_wing"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Attack_wing_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35014"
  animName: "AS_TYC_Monster_002_Fire02"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Attack"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Attack_3P"
  delay: 0.7
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35015"
  animName: "AS_TYC_Monster_002_Fire02"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Attack_wing"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Attack_wing_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35016"
  animName: "AS_TYC_Monster_002_HitB"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35017"
  animName: "AS_TYC_Monster_002_HitF"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35018"
  animName: "AS_TYC_Monster_002_HitL"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35019"
  animName: "AS_TYC_Monster_002_HitR"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35020"
  animName: "AS_TYC_Monster_002_HitB"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit_wing"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_wing_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35021"
  animName: "AS_TYC_Monster_002_HitF"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit_wing"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_wing_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35022"
  animName: "AS_TYC_Monster_002_HitL"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit_wing"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_wing_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35023"
  animName: "AS_TYC_Monster_002_HitR"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Hit_wing"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Hit_wing_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35024"
  animName: "AS_TYC_Monster_002_Death"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Death"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Death_3P"
  delay: 1.1
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35025"
  animName: "AS_TYC_Monster_002_Death"
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Death_wing"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Death_wing_3P"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35026"
  animName: "AS_TYC_Monster_002_Run"
  isLoop: 1
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Run"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Run_3P"
  stopEvent: "Stop_LetsTower_Enemy_Hornet_Run"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "35027"
  animName: "AS_TYC_Monster_002_Walk"
  isLoop: 1
  bank: "LetsTower_Monster"
  playEvent: "Play_LetsTower_Enemy_Hornet_Move"
  playEvent3p: "Play_LetsTower_Enemy_Hornet_Move_3P"
  stopEvent: "Stop_LetsTower_Enemy_Hornet_Move"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37000"
  animName: "AS_CH_Skill_BeHit_01"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_BeHit_01"
  playEvent3p: "Play_AS_CH_Skill_BeHit_01"
  stopEvent: "Stop_AS_CH_Skill_BeHit_01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37001"
  animName: "AS_CH_Skill_Summon_01"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Summon_01"
  playEvent3p: "Play_AS_CH_Skill_Summon_01"
  stopEvent: "Stop_AS_CH_Skill_Summon_01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37002"
  animName: "AS_CH_Skill_Summon_02"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Summon_02"
  playEvent3p: "Play_AS_CH_Skill_Summon_02"
  stopEvent: "Stop_AS_CH_Skill_Summon_02"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37003"
  animName: "AS_CH_Skill_Summon_03"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Summon_03"
  playEvent3p: "Play_AS_CH_Skill_Summon_03"
  stopEvent: "Stop_AS_CH_Skill_Summon_03"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37004"
  animName: "AS_CH_Skill_Expolde_01_AccumulatePower_Loop"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Expolde_01_AccumulatePower_Loop"
  playEvent3p: "Play_AS_CH_Skill_Expolde_01_AccumulatePower_Loop"
  stopEvent: "Stop_AS_CH_Skill_Expolde_01_AccumulatePower_Loop"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37005"
  animName: "AS_CH_Skill_Dash_02_Start"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_DPlay_ASh_02_Start"
  playEvent3p: "Play_AS_CH_Skill_DPlay_ASh_02_Start"
  stopEvent: "Stop_AS_CH_Skill_DStop_ASh_02_Start"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37006"
  animName: "AS_CH_Skill_Smash_01"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_SmPlay_ASh_01"
  playEvent3p: "Play_AS_CH_Skill_SmPlay_ASh_01"
  stopEvent: "Stop_AS_CH_Skill_SmStop_ASh_01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37007"
  animName: "AS_CH_Skill_FlyingUp_01_Loop"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_FlyingUp_01_Loop"
  playEvent3p: "Play_AS_CH_Skill_FlyingUp_01_Loop"
  stopEvent: "Stop_AS_CH_Skill_FlyingUp_01_Loop"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37008"
  animName: "AS_CH_Skill_Whirlwind_01"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Whirlwind_01"
  playEvent3p: "Play_AS_CH_Skill_Whirlwind_01"
  stopEvent: "Stop_AS_CH_Skill_Whirlwind_01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37009"
  animName: "AS_CH_Skill_ShockWave_01"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_ShockWave_01"
  playEvent3p: "Play_AS_CH_Skill_ShockWave_01"
  stopEvent: "Stop_AS_CH_Skill_ShockWave_01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37010"
  animName: "AS_CH_Skill_Throw_01"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Throw_01"
  playEvent3p: "Play_AS_CH_Skill_Throw_01"
  stopEvent: "Stop_AS_CH_Skill_Throw_01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37011"
  animName: "AS_CH_Skill_Dash_02_Loop"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_DPlay_ASh_02_Loop"
  playEvent3p: "Play_AS_CH_Skill_DPlay_ASh_02_Loop"
  stopEvent: "Stop_AS_CH_Skill_DStop_ASh_02_Loop"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37012"
  animName: "AS_CH_Skill_Dodge_01"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Dodge_01"
  playEvent3p: "Play_AS_CH_Skill_Dodge_01"
  stopEvent: "Stop_AS_CH_Skill_Dodge_01"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37013"
  animName: "AS_CH_Skill_Floating_01_Loop"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Floating_01_Loop"
  playEvent3p: "Play_AS_CH_Skill_Floating_01_Loop"
  stopEvent: "Stop_AS_CH_Skill_Floating_01_Loop"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37014"
  animName: "AS_CH_Skill_Jump_01_Start"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Jump_01_Start"
  playEvent3p: "Play_AS_CH_Skill_Jump_01_Start"
  stopEvent: "Stop_AS_CH_Skill_Jump_01_Start"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37015"
  animName: "AS_CH_Skill_KickRight_Attack"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_KickRight_Attack"
  playEvent3p: "Play_AS_CH_Skill_KickRight_Attack"
  stopEvent: "Stop_AS_CH_Skill_KickLeft_Attack"
  delay: 0.23
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37016"
  animName: "AS_CH_Skill_KickLeft_Attack"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_KickLeft_Attack"
  playEvent3p: "Play_AS_CH_Skill_KickLeft_Attack"
  stopEvent: "Stop_AS_CH_Skill_KickRight_Attack"
  delay: 0.23
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37017"
  animName: "AS_CH_Skill_IronHeadFly_Start"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_IronHeadFly_Start"
  playEvent3p: "Play_AS_CH_Skill_IronHeadFly_Start"
  stopEvent: "Stop_AS_CH_Skill_HandImpact_01_End"
  delay: 0.45
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37018"
  animName: "AS_CH_Skill_ForwardRoll"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_ForwardRoll"
  playEvent3p: "Play_AS_CH_Skill_ForwardRoll"
  stopEvent: "Stop_AS_CH_Skill_BullRam_Start"
  delay: 0.39
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37019"
  animName: "AS_CH_Skill_HandImpact_01_End"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_HandImpact_01_End"
  playEvent3p: "Play_AS_CH_Skill_HandImpact_01_End"
  stopEvent: "Stop_AS_CH_Skill_KameHameHa_Start"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37020"
  animName: "AS_CH_Skill_BullRam_Start"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_BullRam_Start"
  playEvent3p: "Play_AS_CH_Skill_BullRam_Start"
  stopEvent: "Stop_AS_CH_Skill_IronHeadFly_Start"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37021"
  animName: "AS_CH_Skill_KameHameHa_Start"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_KameHameHa_Start"
  playEvent3p: "Play_AS_CH_Skill_KameHameHa_Start"
  stopEvent: "Stop_AS_CH_Skill_ForwardRoll"
  delay: 0.01
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37022"
  animName: "AS_CH_Skill_Shoryuken"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_Shoryuken"
  playEvent3p: "Play_AS_CH_Skill_Shoryuken"
  stopEvent: "Stop_AS_CH_Skill_IronHead_Attack"
  delay: 0.32
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
rows {
  id: "37023"
  animName: "AS_CH_Skill_IronHead_Attack"
  bank: "UGC_Skill_Editor"
  playEvent: "Play_AS_CH_Skill_IronHead_Attack"
  playEvent3p: "Play_AS_CH_Skill_IronHead_Attack"
  stopEvent: "Stop_AS_CH_Skill_Shoryuken"
  delay: 0.31
  is3d: 1
  TransitionDuration: 0.3
  BeStop: 1
}
