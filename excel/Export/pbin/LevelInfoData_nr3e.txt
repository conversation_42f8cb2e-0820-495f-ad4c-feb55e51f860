com.tencent.wea.xlsRes.table_LevelInfoData
excel/xls/G_关卡配置_nr3e.xlsx sheet:Sheet1
rows {
  Id: 50101
  LevelName: "LetsGo_Level_50101_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（森林）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50101"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 0
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 0
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50102
  LevelName: "LetsGo_Level_50102_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（庄园）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50102"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 20
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 400
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50103
  LevelName: "LetsGo_Level_50103_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（庭院）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50103"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 20
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 200
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50104
  LevelName: "LetsGo_Level_50104_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（乐园）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50104"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 20
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 500
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50105
  LevelName: "LetsGo_Level_50105_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（海岛）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50105"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 80
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 1000
  QualifyRankingGroup: 1000000
  QualifyWeight: 80
  reservedField0: "80"
}
rows {
  Id: 50106
  LevelName: "LetsGo_Level_50106_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（冰风谷）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50106"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50107
  LevelName: "LetsGo_Level_50107_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（秘境）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50107"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50108
  LevelName: "LetsGo_Level_50108_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（炫彩城）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50108"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50109
  LevelName: "LetsGo_Level_50109_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（月光堡）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50109"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50110
  LevelName: "LetsGo_Level_50110_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（迷踪岭）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50110"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 80
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 8
  MMRType: MST_HideAndSeek
  AIMode: 17
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 1000
  QualifyRankingGroup: 1000000
  QualifyWeight: 60
  reservedField0: "60"
}
rows {
  Id: 50111
  LevelName: "LetsGo_Level_50111_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（时光城）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50111"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 80
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 1000
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50112
  LevelName: "LetsGo_Level_50112_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（HELLO KITTY小镇）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50112"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 20
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 300
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50113
  LevelName: "LetsGo_Level_50113_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（阳光海岸）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50113"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50114
  LevelName: "LetsGo_Level_50114_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（甜品乐园）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50114"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.26.1"
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50115
  LevelName: "LetsGo_Level_50115_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（精灵谷）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50115"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 80
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 1000
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50116
  LevelName: "LetsGo_Level_50116_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（千机城）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50116"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 80
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.68.1"
  MapType: 8
  MMRType: MST_HideAndSeek
  AIMode: 17
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 1000
  QualifyRankingGroup: 1000000
  QualifyWeight: 60
  reservedField0: "60"
}
rows {
  Id: 50117
  LevelName: "LetsGo_Level_50117_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（梦幻星宫）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50117"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 20
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 600
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50118
  LevelName: "LetsGo_Level_50118_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（荷畔小筑）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50118"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.78.1"
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50119
  LevelName: "LetsGo_Level_50119_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（春日学园）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50119"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.88.1"
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50120
  LevelName: "LetsGo_Level_50120_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（蛋糕城）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50120"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.99.1"
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 400
  reservedField0: "400"
}
rows {
  Id: 50121
  LevelName: "LetsGo_Level_50121_HideAndSeek_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 19
  ChName: "躲猫猫（牛牛街）"
  InfoText: "惊心动魄的伪装与搜寻，嘘，别出声！"
  LevelIcon: "CDN:T_loading_Checkpoint_50121"
  LevelDuration: "0,240"
  LevelBreakRestDuration: "0,240"
  SideNum: 0
  RankingGroup: 50
  RankingGroup: 100000
  LevelType: LT_Nr3E1HideAndSeek
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: 4001
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 3
  MMRType: MST_HideAndSeek
  QualifyMMRType: MST_HideAndSeekDegree
  QualifyRankingGroup: 700
  QualifyRankingGroup: 1000000
  QualifyWeight: 400
  reservedField0: "400"
}
rows {
  Id: 50201
  LevelName: "LetsGo_Level_50201_DollsRun_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 18
  ChName: "卧底行动（城堡）"
  InfoText: "一场星宝与卧底的智斗游戏，智谋与策略大检验！"
  LevelIcon: "CDN:T_loading_Checkpoint_50201"
  LevelDuration: "0,420"
  LevelBreakRestDuration: "0,420"
  SideNum: 0
  RankingGroup: 0
  RankingGroup: 100000
  LevelType: LT_Nr3E2DollsRun
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 4
  MMRType: MST_DollsRun
  QualifyMMRType: MST_DollsRunDegree
  QualifyRankingGroup: 0
  QualifyRankingGroup: 100000
  QualifyWeight: 100
}
rows {
  Id: 50202
  LevelName: "LetsGo_Level_50202_DollsRun_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 18
  ChName: "卧底行动（园林）"
  InfoText: "一场星宝与卧底的智斗游戏，智谋与策略大检验！"
  LevelIcon: "CDN:T_loading_Checkpoint_50202"
  LevelDuration: "0,420"
  LevelBreakRestDuration: "0,420"
  SideNum: 0
  RankingGroup: 20
  RankingGroup: 100000
  LevelType: LT_Nr3E2DollsRun
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 4
  MMRType: MST_DollsRun
  QualifyMMRType: MST_DollsRunDegree
  QualifyRankingGroup: 300
  QualifyRankingGroup: 100000
  QualifyWeight: 100
}
rows {
  Id: 50203
  LevelName: "LetsGo_Level_50203_DollsRun_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 12
  LevelModuleId: 18
  ChName: "卧底行动（战船）"
  InfoText: "一场星宝与卧底的智斗游戏，智谋与策略大检验！"
  LevelIcon: "CDN:T_loading_Checkpoint_50203"
  LevelDuration: "0,420"
  LevelBreakRestDuration: "0,420"
  SideNum: 0
  RankingGroup: 20
  RankingGroup: 100000
  LevelType: LT_Nr3E2DollsRun
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 2
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 4
  MMRType: MST_DollsRun
  QualifyMMRType: MST_DollsRunDegree
  QualifyRankingGroup: 600
  QualifyRankingGroup: 100000
  QualifyWeight: 100
}
rows {
  Id: 50301
  LevelName: "LetsGo_Level_50301_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（古宅）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50301"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 0
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 5
  MMRType: MST_Werewolf
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 0
  QualifyRankingGroup: 100000
  QualifyWeight: 80
  reservedField0: "80"
}
rows {
  Id: 50302
  LevelName: "LetsGo_Level_50302_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（暗夜堡）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50302"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 100
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50302_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 300
  QualifyRankingGroup: 100000
  QualifyWeight: 90
  reservedField0: "90"
}
rows {
  Id: 50303
  LevelName: "LetsGo_Level_50303_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（遗迹）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50303"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 150
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.99.1"
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50303_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 1000
  QualifyRankingGroup: 100000
  QualifyWeight: 80
  reservedField0: "80"
}
rows {
  Id: 50304
  LevelName: "LetsGo_Level_50304_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（学校）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50304"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 150
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50304_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 500
  QualifyRankingGroup: 100000
  QualifyWeight: 90
  reservedField0: "90"
}
rows {
  Id: 50305
  LevelName: "LetsGo_Level_50305_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（月球）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50305"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 250
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.99.1"
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50305_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 1500
  QualifyRankingGroup: 100000
  QualifyWeight: 90
  reservedField0: "90"
}
rows {
  Id: 50306
  LevelName: "LetsGo_Level_50306_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（游乐场）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50306"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 250
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  LowestVersion: "1.3.99.1"
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50306_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 1500
  QualifyRankingGroup: 100000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50307
  LevelName: "LetsGo_Level_50307_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（牛牛街）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50307"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 200
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50307_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 1000
  QualifyRankingGroup: 100000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50308
  LevelName: "LetsGo_Level_50308_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（冰湖村）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50308"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 200
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50308_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 1500
  QualifyRankingGroup: 100000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50309
  LevelName: "LetsGo_Level_50309_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "谁是狼人（精灵谷）"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50309"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 250
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/RuntimeLevels/50309_Werewolf_Runtime/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 2000
  QualifyRankingGroup: 100000
  QualifyWeight: 100
  reservedField0: "100"
}
rows {
  Id: 50399
  LevelName: "LetsGo_Level_50399_Werewolf_Runtime"
  MinPlayerNum: 1
  MaxPlayerNum: 15
  LevelModuleId: 20
  ChName: "秘境寻宝"
  InfoText: "星宝中混入了捣乱狼人？交流信息将他们捉拿归案！"
  LevelIcon: "CDN:T_loading_Checkpoint_50399"
  LevelDuration: "0,1800"
  LevelBreakRestDuration: "0,1800"
  SideNum: 0
  RankingGroup: 0
  RankingGroup: 100000
  LevelType: LT_Nr3E3Werewolf
  ScoreNeededtoWin: 0
  Star: 1
  AIType: 1
  musicGroupId: -1
  IsCompleted: LCT_Completed
  PrepareTime: 5
  HidePropsInRank: 0
  MapType: 5
  MMRType: MST_Werewolf
  DirPath: "/Game/Feature/NR3E/E3FullFeature/TreasureHunt/RuntimeLevels/"
  QualifyMMRType: MST_WerewolfDegree
  QualifyRankingGroup: 0
  QualifyRankingGroup: 100000
  QualifyWeight: 100
  reservedField0: "100"
}
