com.tencent.wea.xlsRes.table_TradingCardDeckConfig
excel/xls/K_卡牌.xlsx sheet:卡组
rows {
  id: 10102
  cardIdList: 1010201
  cardIdList: 1010202
  cardIdList: 1010203
  cardIdList: 1010204
  cardIdList: 1010205
  cardIdList: 1010206
  cardIdList: 1010207
  cardIdList: 1010208
  cardIdList: 1010209
  sortId: 190
  name: "百果乐园"
  icon: "Card_fengshounongchang_02"
  smallicon: "T_CardSystemsCardInfo_Tab_Apple"
  reward {
    itemIdList: 870032
    itemIdList: 3134
    itemIdList: 200014
    numList: 1
    numList: 5
    numList: 10
    expireTimestamps {
      seconds: 1737302399
    }
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Apple2"
  smallicon3: "T_CardSystemsMain_SortItem_BaiGuoLeYuan"
}
rows {
  id: 10105
  cardIdList: 1010501
  cardIdList: 1010502
  cardIdList: 1010503
  cardIdList: 1010504
  cardIdList: 1010505
  cardIdList: 1010506
  cardIdList: 1010507
  cardIdList: 1010508
  cardIdList: 1010509
  sortId: 160
  name: "悠享田园"
  icon: "Card_fengshounongchang_05"
  smallicon: "T_CardSystemsCardInfo_Tab_Chicken"
  reward {
    itemIdList: 820142
    itemIdList: 4
    itemIdList: 6
    numList: 1
    numList: 2000
    numList: 10
    expireTimestamps {
      seconds: 1737302399
    }
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Chicken2"
  smallicon3: "T_CardSystemsMain_SortItem_TianYuanMuGe"
}
rows {
  id: 10107
  cardIdList: 1010701
  cardIdList: 1010702
  cardIdList: 1010703
  cardIdList: 1010704
  cardIdList: 1010705
  cardIdList: 1010706
  cardIdList: 1010707
  cardIdList: 1010708
  cardIdList: 1010709
  sortId: 140
  name: "丰谷稻田"
  icon: "Card_fengshounongchang_07"
  smallicon: "T_CardSystemsCardInfo_Tab_FengTianDaoGu"
  reward {
    itemIdList: 860151
    itemIdList: 3134
    itemIdList: 200014
    numList: 1
    numList: 10
    numList: 20
    expireTimestamps {
      seconds: 1737302399
    }
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_FengTianDaoGu2"
  smallicon3: "T_CardSystemsMain_SortItem_FengGuDaoTian"
}
rows {
  id: 10110
  cardIdList: 1011001
  cardIdList: 1011002
  cardIdList: 1011003
  cardIdList: 1011004
  cardIdList: 1011005
  cardIdList: 1011006
  cardIdList: 1011007
  cardIdList: 1011008
  cardIdList: 1011009
  sortId: 110
  name: "珊瑚鱼影"
  icon: "Card_fengshounongchang_10"
  smallicon: "T_CardSystemsCardInfo_Tab_Fish"
  reward {
    itemIdList: 840218
    itemIdList: 4
    itemIdList: 6
    numList: 1
    numList: 5000
    numList: 20
    expireTimestamps {
      seconds: 1737302399
    }
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Fish2"
  smallicon3: "T_CardSystemsMain_SortItem_ShanHuYuYing"
}
rows {
  id: 10201
  cardIdList: 1020101
  cardIdList: 1020102
  cardIdList: 1020103
  cardIdList: 1020104
  cardIdList: 1020105
  cardIdList: 1020106
  cardIdList: 1020107
  cardIdList: 1020108
  cardIdList: 1020109
  sortId: 200
  name: "四季蔬香"
  icon: "Card_fengshounongchang_10201"
  smallicon: "T_CardSystemsCardInfo_Tab_Radish"
  reward {
    itemIdList: 860151
    itemIdList: 3800
    itemIdList: 3134
    numList: 1
    numList: 1
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Radish2"
  smallicon3: "Card_fengshounongchang_10201"
}
rows {
  id: 10202
  cardIdList: 1020201
  cardIdList: 1020202
  cardIdList: 1020203
  cardIdList: 1020204
  cardIdList: 1020205
  cardIdList: 1020206
  cardIdList: 1020207
  cardIdList: 1020208
  cardIdList: 1020209
  sortId: 190
  name: "百果乐园"
  icon: "Card_fengshounongchang_10202"
  smallicon: "T_CardSystemsCardInfo_Tab_Apple"
  reward {
    itemIdList: 3800
    itemIdList: 3134
    itemIdList: 4
    numList: 1
    numList: 10
    numList: 5000
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Apple2"
  smallicon3: "Card_fengshounongchang_10202"
}
rows {
  id: 10203
  cardIdList: 1020301
  cardIdList: 1020302
  cardIdList: 1020303
  cardIdList: 1020304
  cardIdList: 1020305
  cardIdList: 1020306
  cardIdList: 1020307
  cardIdList: 1020308
  cardIdList: 1020309
  sortId: 180
  name: "牧场赠礼"
  icon: "Card_fengshounongchang_10203"
  smallicon: "T_CardSystemsCardInfo_Tab_MuChangChanPin"
  reward {
    itemIdList: 840218
    itemIdList: 3800
    itemIdList: 3134
    numList: 1
    numList: 2
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_MuChangChanPin2"
  smallicon3: "Card_fengshounongchang_10203"
}
rows {
  id: 10204
  cardIdList: 1020401
  cardIdList: 1020402
  cardIdList: 1020403
  cardIdList: 1020404
  cardIdList: 1020405
  cardIdList: 1020406
  cardIdList: 1020407
  cardIdList: 1020408
  cardIdList: 1020409
  sortId: 170
  name: "花香满园"
  icon: "Card_fengshounongchang_10204"
  smallicon: "T_CardSystemsCardInfo_Tab_HuaXiangManYuan"
  reward {
    itemIdList: 3800
    itemIdList: 3134
    itemIdList: 6
    numList: 2
    numList: 10
    numList: 50
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_HuaXiangManYuan2"
  smallicon3: "Card_fengshounongchang_10204"
}
rows {
  id: 10205
  cardIdList: 1020501
  cardIdList: 1020502
  cardIdList: 1020503
  cardIdList: 1020504
  cardIdList: 1020505
  cardIdList: 1020506
  cardIdList: 1020507
  cardIdList: 1020508
  cardIdList: 1020509
  sortId: 160
  name: "悠享田园"
  icon: "Card_fengshounongchang_10205"
  smallicon: "T_CardSystemsCardInfo_Tab_Chicken"
  reward {
    itemIdList: 820142
    itemIdList: 3800
    itemIdList: 3134
    numList: 1
    numList: 2
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Chicken2"
  smallicon3: "Card_fengshounongchang_10205"
}
rows {
  id: 10206
  cardIdList: 1020601
  cardIdList: 1020602
  cardIdList: 1020603
  cardIdList: 1020604
  cardIdList: 1020605
  cardIdList: 1020606
  cardIdList: 1020607
  cardIdList: 1020608
  cardIdList: 1020609
  sortId: 150
  name: "鱼塘故事"
  icon: "Card_fengshounongchang_10206"
  smallicon: "T_CardSystemsCardInfo_Tab_Fish"
  reward {
    itemIdList: 3800
    itemIdList: 3134
    itemIdList: 4
    numList: 2
    numList: 10
    numList: 8000
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Fish2"
  smallicon3: "Card_fengshounongchang_10206"
}
rows {
  id: 10207
  cardIdList: 1020701
  cardIdList: 1020702
  cardIdList: 1020703
  cardIdList: 1020704
  cardIdList: 1020705
  cardIdList: 1020706
  cardIdList: 1020707
  cardIdList: 1020708
  cardIdList: 1020709
  sortId: 140
  name: "丰谷稻田"
  icon: "Card_fengshounongchang_10207"
  smallicon: "T_CardSystemsCardInfo_Tab_FengTianDaoGu"
  reward {
    itemIdList: 870032
    itemIdList: 3800
    itemIdList: 3134
    numList: 1
    numList: 2
    numList: 15
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_FengTianDaoGu2"
  smallicon3: "Card_fengshounongchang_10207"
}
rows {
  id: 10208
  cardIdList: 1020801
  cardIdList: 1020802
  cardIdList: 1020803
  cardIdList: 1020804
  cardIdList: 1020805
  cardIdList: 1020806
  cardIdList: 1020807
  cardIdList: 1020808
  cardIdList: 1020809
  sortId: 130
  name: "奇趣生物"
  icon: "Card_fengshounongchang_10208"
  smallicon: "T_CardSystemsCardInfo_Tab_QiQuShengWu"
  reward {
    itemIdList: 3800
    itemIdList: 3134
    itemIdList: 6
    numList: 2
    numList: 15
    numList: 80
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_QiQuShengWu2"
  smallicon3: "Card_fengshounongchang_10208"
}
rows {
  id: 10209
  cardIdList: 1020901
  cardIdList: 1020902
  cardIdList: 1020903
  cardIdList: 1020904
  cardIdList: 1020905
  cardIdList: 1020906
  cardIdList: 1020907
  cardIdList: 1020908
  cardIdList: 1020909
  sortId: 120
  name: "海洋奇趣"
  icon: "Card_fengshounongchang_10209"
  smallicon: "T_CardSystemsCardInfo_Tab_HaiYangQiQu"
  reward {
    itemIdList: 890501
    itemIdList: 3800
    itemIdList: 3134
    numList: 1
    numList: 2
    numList: 20
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_HaiYangQiQu2"
  smallicon3: "Card_fengshounongchang_10209"
}
rows {
  id: 10210
  cardIdList: 1021001
  cardIdList: 1021002
  cardIdList: 1021003
  cardIdList: 1021004
  cardIdList: 1021005
  cardIdList: 1021006
  cardIdList: 1021007
  cardIdList: 1021008
  cardIdList: 1021009
  sortId: 110
  name: "珊瑚鱼影"
  icon: "Card_fengshounongchang_10210"
  smallicon: "T_CardSystemsCardInfo_Tab_ShanHuYuYing"
  reward {
    itemIdList: 3800
    itemIdList: 3134
    itemIdList: 4
    numList: 2
    numList: 25
    numList: 12000
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_ShanHuYuYing2"
  smallicon3: "Card_fengshounongchang_10210"
}
rows {
  id: 10211
  cardIdList: 1021101
  cardIdList: 1021102
  cardIdList: 1021103
  cardIdList: 1021104
  cardIdList: 1021105
  cardIdList: 1021106
  cardIdList: 1021107
  cardIdList: 1021108
  cardIdList: 1021109
  sortId: 100
  name: "沙滩露营"
  icon: "Card_fengshounongchang_10211"
  smallicon: "T_CardSystemsCardInfo_Tab_ShaTanLuYing"
  reward {
    itemIdList: 890005
    itemIdList: 3800
    itemIdList: 3134
    numList: 1
    numList: 2
    numList: 30
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_ShaTanLuYing2"
  smallicon3: "Card_fengshounongchang_10211"
}
rows {
  id: 10212
  cardIdList: 1021201
  cardIdList: 1021202
  cardIdList: 1021203
  cardIdList: 1021204
  cardIdList: 1021205
  cardIdList: 1021206
  cardIdList: 1021207
  cardIdList: 1021208
  cardIdList: 1021209
  sortId: 90
  name: "梦想庄园"
  icon: "Card_fengshounongchang_10212"
  smallicon: "T_CardSystemsCardInfo_Tab_MengXiangZhuangYuan"
  reward {
    itemIdList: 3800
    itemIdList: 3134
    itemIdList: 6
    numList: 2
    numList: 35
    numList: 120
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_MengXiangZhuangYuan2"
  smallicon3: "Card_fengshounongchang_10212"
}
rows {
  id: 10301
  cardIdList: 1030101
  cardIdList: 1030102
  cardIdList: 1030103
  cardIdList: 1030104
  cardIdList: 1030105
  cardIdList: 1030106
  cardIdList: 1030107
  cardIdList: 1030108
  cardIdList: 1030109
  sortId: 200
  name: "悠游市集"
  icon: "Card_fengshounongchang_10301"
  smallicon: "T_CardSystemsCardInfo_Tab_YouYou"
  reward {
    itemIdList: 860193
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 5
    numList: 20
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_YouYou2"
  smallicon3: "Card_fengshounongchang_10301"
}
rows {
  id: 10302
  cardIdList: 1030201
  cardIdList: 1030202
  cardIdList: 1030203
  cardIdList: 1030204
  cardIdList: 1030205
  cardIdList: 1030206
  cardIdList: 1030207
  cardIdList: 1030208
  cardIdList: 1030209
  sortId: 190
  name: "潮饮市集"
  icon: "Card_fengshounongchang_10302"
  smallicon: "T_CardSystemsCardInfo_Tab_Chaoyin"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200014
    numList: 3
    numList: 5
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Chaoyin2"
  smallicon3: "Card_fengshounongchang_10302"
}
rows {
  id: 10303
  cardIdList: 1030301
  cardIdList: 1030302
  cardIdList: 1030303
  cardIdList: 1030304
  cardIdList: 1030305
  cardIdList: 1030306
  cardIdList: 1030307
  cardIdList: 1030308
  cardIdList: 1030309
  sortId: 180
  name: "运动市集"
  icon: "Card_fengshounongchang_10303"
  smallicon: "T_CardSystemsCardInfo_Tab_Yundong"
  reward {
    itemIdList: 840269
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 10
    numList: 20
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Yundong2"
  smallicon3: "Card_fengshounongchang_10303"
}
rows {
  id: 10304
  cardIdList: 1030401
  cardIdList: 1030402
  cardIdList: 1030403
  cardIdList: 1030404
  cardIdList: 1030405
  cardIdList: 1030406
  cardIdList: 1030407
  cardIdList: 1030408
  cardIdList: 1030409
  sortId: 170
  name: "园艺市集"
  icon: "Card_fengshounongchang_10304"
  smallicon: "T_CardSystemsCardInfo_Tab_Yuanyi"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200014
    numList: 3
    numList: 10
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Yuanyi2"
  smallicon3: "Card_fengshounongchang_10304"
}
rows {
  id: 10305
  cardIdList: 1030501
  cardIdList: 1030502
  cardIdList: 1030503
  cardIdList: 1030504
  cardIdList: 1030505
  cardIdList: 1030506
  cardIdList: 1030507
  cardIdList: 1030508
  cardIdList: 1030509
  sortId: 160
  name: "趣浪市集"
  icon: "Card_fengshounongchang_10305"
  smallicon: "T_CardSystemsCardInfo_Tab_Qulang"
  reward {
    itemIdList: 820175
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 15
    numList: 50
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Qulang2"
  smallicon3: "Card_fengshounongchang_10305"
}
rows {
  id: 10306
  cardIdList: 1030601
  cardIdList: 1030602
  cardIdList: 1030603
  cardIdList: 1030604
  cardIdList: 1030605
  cardIdList: 1030606
  cardIdList: 1030607
  cardIdList: 1030608
  cardIdList: 1030609
  sortId: 150
  name: "文豪市集"
  icon: "Card_fengshounongchang_10306"
  smallicon: "T_CardSystemsCardInfo_Tab_Wenhao"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200015
    numList: 3
    numList: 15
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Wenhao2"
  smallicon3: "Card_fengshounongchang_10306"
}
rows {
  id: 10307
  cardIdList: 1030701
  cardIdList: 1030702
  cardIdList: 1030703
  cardIdList: 1030704
  cardIdList: 1030705
  cardIdList: 1030706
  cardIdList: 1030707
  cardIdList: 1030708
  cardIdList: 1030709
  sortId: 140
  name: "数码市集"
  icon: "Card_fengshounongchang_10307"
  smallicon: "T_CardSystemsCardInfo_Tab_Shuma"
  reward {
    itemIdList: 870045
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 20
    numList: 50
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Shuma2"
  smallicon3: "Card_fengshounongchang_10307"
}
rows {
  id: 10308
  cardIdList: 1030801
  cardIdList: 1030802
  cardIdList: 1030803
  cardIdList: 1030804
  cardIdList: 1030805
  cardIdList: 1030806
  cardIdList: 1030807
  cardIdList: 1030808
  cardIdList: 1030809
  sortId: 130
  name: "萌宠市集"
  icon: "Card_fengshounongchang_10308"
  smallicon: "T_CardSystemsCardInfo_Tab_Mengchong"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200015
    numList: 3
    numList: 20
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Mengchong2"
  smallicon3: "Card_fengshounongchang_10308"
}
rows {
  id: 10309
  cardIdList: 1030901
  cardIdList: 1030902
  cardIdList: 1030903
  cardIdList: 1030904
  cardIdList: 1030905
  cardIdList: 1030906
  cardIdList: 1030907
  cardIdList: 1030908
  cardIdList: 1030909
  sortId: 120
  name: "郊游市集"
  icon: "Card_fengshounongchang_10309"
  smallicon: "T_CardSystemsCardInfo_Tab_Jiaoyou"
  reward {
    itemIdList: 890504
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 20
    numList: 80
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Jiaoyou2"
  smallicon3: "Card_fengshounongchang_10309"
}
rows {
  id: 10310
  cardIdList: 1031001
  cardIdList: 1031002
  cardIdList: 1031003
  cardIdList: 1031004
  cardIdList: 1031005
  cardIdList: 1031006
  cardIdList: 1031007
  cardIdList: 1031008
  cardIdList: 1031009
  sortId: 110
  name: "手作市集"
  icon: "Card_fengshounongchang_10310"
  smallicon: "T_CardSystemsCardInfo_Tab_Shouzuo"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200016
    numList: 3
    numList: 25
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Shouzuo2"
  smallicon3: "Card_fengshounongchang_10310"
}
rows {
  id: 10311
  cardIdList: 1031101
  cardIdList: 1031102
  cardIdList: 1031103
  cardIdList: 1031104
  cardIdList: 1031105
  cardIdList: 1031106
  cardIdList: 1031107
  cardIdList: 1031108
  cardIdList: 1031109
  sortId: 100
  name: "音悦市集"
  icon: "Card_fengshounongchang_10311"
  smallicon: "T_CardSystemsCardInfo_Tab_Yinyue"
  reward {
    itemIdList: 890013
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 25
    numList: 100
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Yinyue2"
  smallicon3: "Card_fengshounongchang_10311"
}
rows {
  id: 10312
  cardIdList: 1031201
  cardIdList: 1031202
  cardIdList: 1031203
  cardIdList: 1031204
  cardIdList: 1031205
  cardIdList: 1031206
  cardIdList: 1031207
  cardIdList: 1031208
  cardIdList: 1031209
  sortId: 90
  name: "玩偶市集"
  icon: "Card_fengshounongchang_10312"
  smallicon: "T_CardSystemsCardInfo_Tab_Wanou"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200016
    numList: 3
    numList: 30
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Wanou2"
  smallicon3: "Card_fengshounongchang_10312"
}
rows {
  id: 10401
  cardIdList: 1040101
  cardIdList: 1040102
  cardIdList: 1040103
  cardIdList: 1040104
  cardIdList: 1040105
  sortId: 200
  name: "乌萨奇卡册"
  icon: "Card_xiariliandong_10401"
  smallicon: "T_CardSystemsCardInfo_Tab_Wusaqi"
  reward {
    itemIdList: 860193
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 5
    numList: 20
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Wusaqi2"
  smallicon3: "Card_xiariliandong_10401"
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_ckw"
  cardbagiconskin: "S02_ckw"
  logoName: "CDN:Logo_ckw"
}
rows {
  id: 10402
  cardIdList: 1040201
  cardIdList: 1040202
  cardIdList: 1040203
  cardIdList: 1040204
  cardIdList: 1040205
  sortId: 190
  name: "哈奇喵卡册"
  icon: "Card_xiariliandong_10402"
  smallicon: "T_CardSystemsCardInfo_Tab_Xiaoba"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200014
    numList: 3
    numList: 5
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Xiaoba2"
  smallicon3: "Card_xiariliandong_10402"
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_ckw"
  cardbagiconskin: "S02_ckw"
  logoName: "CDN:Logo_ckw"
}
rows {
  id: 10403
  cardIdList: 1040301
  cardIdList: 1040302
  cardIdList: 1040303
  cardIdList: 1040304
  cardIdList: 1040305
  sortId: 180
  name: "吉伊卡哇卡册"
  icon: "Card_xiariliandong_10403"
  smallicon: "T_CardSystemsCardInfo_Tab_Jiyi"
  reward {
    itemIdList: 840269
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 10
    numList: 20
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Jiyi2"
  smallicon3: "Card_xiariliandong_10403"
  beginTime {
    seconds: 1751558400
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_ckw"
  cardbagiconskin: "S02_ckw"
  logoName: "CDN:Logo_ckw"
}
rows {
  id: 10404
  cardIdList: 1040401
  cardIdList: 1040402
  cardIdList: 1040403
  sortId: 170
  name: "侦探小队"
  icon: "Card_xiariliandong_10404"
  smallicon: "T_CardSystemsCardInfo_Tab_Zhentan"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200014
    numList: 3
    numList: 10
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Zhentan2"
  smallicon3: "Card_xiariliandong_10404"
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_kn"
  cardbagiconskin: "S02_conan"
  logoName: "CDN:Logo_kn"
}
rows {
  id: 10405
  cardIdList: 1040501
  cardIdList: 1040502
  cardIdList: 1040503
  cardIdList: 1040504
  sortId: 160
  name: "少年侦探团"
  icon: "Card_xiariliandong_10405"
  smallicon: "T_CardSystemsCardInfo_Tab_Xiaoxue"
  reward {
    itemIdList: 820175
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 15
    numList: 50
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Xiaoxue2"
  smallicon3: "Card_xiariliandong_10405"
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_kn"
  cardbagiconskin: "S02_conan"
  logoName: "CDN:Logo_kn"
}
rows {
  id: 10406
  cardIdList: 1040601
  cardIdList: 1040602
  cardIdList: 1040603
  cardIdList: 1040604
  cardIdList: 1040605
  sortId: 150
  name: "智囊团"
  icon: "Card_xiariliandong_10406"
  smallicon: "T_CardSystemsCardInfo_Tab_Shuren"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200015
    numList: 3
    numList: 15
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Shuren2"
  smallicon3: "Card_xiariliandong_10406"
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_kn"
  cardbagiconskin: "S02_conan"
  logoName: "CDN:Logo_kn"
}
rows {
  id: 10407
  cardIdList: 1040701
  cardIdList: 1040702
  cardIdList: 1040703
  sortId: 140
  name: "怪盗小队"
  icon: "Card_xiariliandong_10407"
  smallicon: "T_CardSystemsCardInfo_Tab_Guaidao"
  reward {
    itemIdList: 870045
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 20
    numList: 50
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Guaidao2"
  smallicon3: "Card_xiariliandong_10407"
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_kn"
  cardbagiconskin: "S02_conan"
  logoName: "CDN:Logo_kn"
}
rows {
  id: 10408
  cardIdList: 1040801
  cardIdList: 1040802
  cardIdList: 1040803
  cardIdList: 1040804
  cardIdList: 1040805
  sortId: 130
  name: "明暗小队"
  icon: "Card_xiariliandong_10408"
  smallicon: "T_CardSystemsCardInfo_Tab_Mingan"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200015
    numList: 3
    numList: 20
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Mingan2"
  smallicon3: "Card_xiariliandong_10408"
  beginTime {
    seconds: 1753372800
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_kn"
  cardbagiconskin: "S02_conan"
  logoName: "CDN:Logo_kn"
}
rows {
  id: 10409
  cardIdList: 1040901
  cardIdList: 1040902
  cardIdList: 1040903
  cardIdList: 1040904
  cardIdList: 1040905
  cardIdList: 1040906
  cardIdList: 1040907
  sortId: 120
  name: "唐三卡册"
  icon: "Card_xiariliandong_10409"
  smallicon: "T_CardSystemsCardInfo_Tab_Chengzhang"
  reward {
    itemIdList: 890504
    itemIdList: 3134
    itemIdList: 6
    numList: 1
    numList: 20
    numList: 80
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Chengzhang2"
  smallicon3: "Card_xiariliandong_10409"
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_dldl"
  cardbagiconskin: "S02_douluo"
  logoName: "CDN:Logo_dldl"
}
rows {
  id: 10410
  cardIdList: 1041001
  cardIdList: 1041002
  cardIdList: 1041003
  cardIdList: 1041004
  cardIdList: 1041005
  cardIdList: 1041006
  cardIdList: 1041007
  cardIdList: 1041008
  sortId: 110
  name: "小舞卡册"
  icon: "Card_xiariliandong_10410"
  smallicon: "T_CardSystemsCardInfo_Tab_Chengshen"
  reward {
    itemIdList: 3801
    itemIdList: 3134
    itemIdList: 200016
    numList: 3
    numList: 25
    numList: 10
  }
  smallicon2: "T_CardSystemsCardInfo_Tab_Chengshen2"
  smallicon3: "Card_xiariliandong_10410"
  beginTime {
    seconds: 1754582400
  }
  endTime {
    seconds: 1755791999
  }
  backgroundImg: "CDN:CommonBg_xiariliandong_dldl"
  cardbagiconskin: "S02_douluo"
  logoName: "CDN:Logo_dldl"
}
