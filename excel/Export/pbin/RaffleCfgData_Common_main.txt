com.tencent.wea.xlsRes.table_RaffleCfgData
excel/xls/C_抽奖奖池_主表.xlsx sheet:奖池-赛季金币活跃卡池
rows {
  poolId: 3000
  refresh: RRT_Permanent
  coinType: 2
  exchange: RCET_BuyGiftPkg
  exCommodityId: 1
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      permanent {
        text: "9折"
        ratio: 0.9
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 350
    minorGRewardGroup: 12
    minorGRewardWeight: 1
    tenthGRewardGroup: 1
    tenthGRewardGroup: 3
    tenthGRewardGroup: 4
    tenthGRewardGroup: 5
    tenthGRewardGroup: 6
    tenthGRewardWeight: 1
    tenthGRewardWeight: 7000
    tenthGRewardWeight: 3000
    tenthGRewardWeight: 12000
    tenthGRewardWeight: 10000
  }
  dispersion {
    rewardRefreshPeriod: 140
    dispersedRewardGroups: 1
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
    dispersedRewardGroups: 5
    dispersedRewardGroups: 6
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 4
  }
  voucherId: 201
  hasLuckyRule: false
}
rows {
  poolId: 1000
  refresh: RRT_Season
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 600
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 1
    minorGRewardWeight: 1
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 13
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      permanent {
        text: "9折"
        ratio: 0.9
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 10
    lvUpperBound: 100
    minorGRewardGroup: 1
    minorGRewardGroup: 2
    minorGRewardGroup: 3
    minorGRewardWeight: 1
    minorGRewardWeight: 2
    minorGRewardWeight: 97
  }
  hasLuckyRule: false
}
rows {
  poolId: 8000
  refresh: RRT_Season
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 100
      permanent {
        text: "免费"
        ratio: 0.0
        refreshHour: 12
      }
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
    minorGRewardGroup: 2
    minorGRewardWeight: 1
  }
  hasLuckyRule: false
}
rows {
  poolId: 30001
  refresh: RRT_Season
  coinType: 202
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 200
    minorGRewardGroup: 1
    minorGRewardGroup: 2
    minorGRewardWeight: 1
    minorGRewardWeight: 2
  }
  redDotShowType: 1
  hasLuckyRule: false
}
rows {
  poolId: 30002
  refresh: RRT_Permanent
  coinType: 2
  exchange: RCET_BuyGiftPkg
  exCommodityId: 1
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      permanent {
        text: "9折"
        ratio: 0.9
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 350
    minorGRewardGroup: 12
    minorGRewardWeight: 1
    tenthGRewardGroup: 1
    tenthGRewardGroup: 3
    tenthGRewardGroup: 4
    tenthGRewardGroup: 5
    tenthGRewardGroup: 6
    tenthGRewardWeight: 1
    tenthGRewardWeight: 7000
    tenthGRewardWeight: 3000
    tenthGRewardWeight: 12000
    tenthGRewardWeight: 10000
  }
  dispersion {
    rewardRefreshPeriod: 140
    dispersedRewardGroups: 1
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
    dispersedRewardGroups: 5
    dispersedRewardGroups: 6
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 4
  }
  voucherId: 208
  hasLuckyRule: false
}
rows {
  poolId: 10001
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 8001
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 30003
  refresh: RRT_Permanent
  coinType: 2
  exchange: RCET_BuyGiftPkg
  exCommodityId: 1
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      permanent {
        text: "9折"
        ratio: 0.9
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 350
    minorGRewardGroup: 12
    minorGRewardWeight: 1
    tenthGRewardGroup: 1
    tenthGRewardGroup: 3
    tenthGRewardGroup: 4
    tenthGRewardGroup: 5
    tenthGRewardGroup: 6
    tenthGRewardWeight: 1
    tenthGRewardWeight: 7000
    tenthGRewardWeight: 3000
    tenthGRewardWeight: 12000
    tenthGRewardWeight: 10000
  }
  dispersion {
    rewardRefreshPeriod: 140
    dispersedRewardGroups: 1
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
    dispersedRewardGroups: 5
    dispersedRewardGroups: 6
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 4
  }
  voucherId: 209
  hasLuckyRule: false
}
rows {
  poolId: 10002
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 8002
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 30004
  refresh: RRT_Permanent
  coinType: 2
  exchange: RCET_BuyGiftPkg
  exCommodityId: 1
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      permanent {
        text: "9折"
        ratio: 0.9
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 350
    minorGRewardGroup: 12
    minorGRewardWeight: 1
    tenthGRewardGroup: 1
    tenthGRewardGroup: 3
    tenthGRewardGroup: 4
    tenthGRewardGroup: 5
    tenthGRewardGroup: 6
    tenthGRewardWeight: 1
    tenthGRewardWeight: 7000
    tenthGRewardWeight: 3000
    tenthGRewardWeight: 12000
    tenthGRewardWeight: 10000
  }
  dispersion {
    rewardRefreshPeriod: 140
    dispersedRewardGroups: 1
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
    dispersedRewardGroups: 5
    dispersedRewardGroups: 6
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 4
  }
  voucherId: 210
  hasLuckyRule: false
}
rows {
  poolId: 10003
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 8003
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 8004
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  mallVoucherId: 203
  hasLuckyRule: false
}
rows {
  poolId: 30005
  refresh: RRT_Permanent
  coinType: 2
  exchange: RCET_BuyGiftPkg
  exCommodityId: 1
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      permanent {
        text: "9折"
        ratio: 0.9
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 350
    minorGRewardGroup: 12
    minorGRewardWeight: 1
    tenthGRewardGroup: 1
    tenthGRewardGroup: 3
    tenthGRewardGroup: 4
    tenthGRewardGroup: 5
    tenthGRewardGroup: 6
    tenthGRewardWeight: 1
    tenthGRewardWeight: 7000
    tenthGRewardWeight: 3000
    tenthGRewardWeight: 12000
    tenthGRewardWeight: 10000
  }
  dispersion {
    rewardRefreshPeriod: 140
    dispersedRewardGroups: 1
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
    dispersedRewardGroups: 5
    dispersedRewardGroups: 6
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 4
  }
  voucherId: 212
  hasLuckyRule: false
}
rows {
  poolId: 10004
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 30006
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
      first {
        text: "免费"
        ratio: 0.0
      }
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 50
    minorGRewardGroup: 1
    minorGRewardGroup: 2
    minorGRewardWeight: 1
    minorGRewardWeight: 99999
  }
  mallVoucherId: 213
  hasLuckyRule: true
}
rows {
  poolId: 30007
  refresh: RRT_Permanent
  coinType: 2
  exchange: RCET_BuyGiftPkg
  exCommodityId: 1
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 6
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 60
      first {
        text: "5折"
        ratio: 0.5
      }
      permanent {
        text: "9折"
        ratio: 0.9
      }
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 350
    minorGRewardGroup: 12
    minorGRewardWeight: 1
    tenthGRewardGroup: 1
    tenthGRewardGroup: 3
    tenthGRewardGroup: 4
    tenthGRewardGroup: 5
    tenthGRewardGroup: 6
    tenthGRewardWeight: 1
    tenthGRewardWeight: 7000
    tenthGRewardWeight: 3000
    tenthGRewardWeight: 12000
    tenthGRewardWeight: 10000
  }
  dispersion {
    rewardRefreshPeriod: 140
    dispersedRewardGroups: 1
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
    dispersedRewardGroups: 5
    dispersedRewardGroups: 6
    preemptTenthForMajorGDraw: true
    preemptLvReWeightForMajorGDraw: 4
  }
  voucherId: 214
  hasLuckyRule: false
}
rows {
  poolId: 8005
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
      first {
        text: "免费"
        ratio: 0.0
      }
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  mallVoucherId: 219
  hasLuckyRule: false
}
rows {
  poolId: 10005
  refresh: RRT_Permanent
  coinType: 4
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1000
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 10000
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1000
    minorGRewardGroup: 1
    minorGRewardGroup: 7
    minorGRewardWeight: 10
    minorGRewardWeight: 10
  }
  mallVoucherId: 2007
  hasLuckyRule: false
}
rows {
  poolId: 6000
  refresh: RRT_Permanent
  coinType: 14
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    majorGDraw: 1
    lvIncPerDraw: 1
    lvUpperBound: 70
    tenthGRewardGroup: 1
    tenthGRewardGroup: 2
    tenthGRewardGroup: 3
    tenthGRewardGroup: 4
    tenthGRewardWeight: 1
    tenthGRewardWeight: 5000
    tenthGRewardWeight: 10000
    tenthGRewardWeight: 10000
  }
  maxLimit: 999999
  freeOneDraw {
    type: RPT_Free
    fPrice {
      offer {
        startTime {
          seconds: 1724083200
        }
        offerCounts: 5
      }
      refreshType: RRT_Permanent
    }
  }
  mallVoucherId: 218
  hasLuckyRule: false
  discountEntrance1: 1
  discountType1: 6
  discountParam1: "5;0"
  discountEntrance2: 5
  discountType2: 6
  discountParam2: "5;0"
}
rows {
  poolId: 8007
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 8008
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 8009
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 8010
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 8011
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 8012
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 8013
  refresh: RRT_Permanent
  coinType: 3
  exchange: RCET_BuyGiftPkg
  exCommodityId: 2
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 2
    }
  }
  multiDraw {
    type: RPT_Discount
    dPrices {
      price: 20
      first {
        text: "5折"
        ratio: 0.5
      }
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 100
  }
  dispersion {
    dispersedRewardGroups: 1
    dispersedRewardGroups: 2
    dispersedRewardGroups: 3
    dispersedRewardGroups: 4
  }
  mallVoucherId: 205
  hasLuckyRule: false
}
rows {
  poolId: 9696
  refresh: RRT_Permanent
  coinType: 2005
  oneDraw {
    type: RPT_Discount
    dPrices {
      price: 1
    }
  }
  guarantee {
    lvIncPerDraw: 1
    lvUpperBound: 1
  }
  hasLuckyRule: false
}
