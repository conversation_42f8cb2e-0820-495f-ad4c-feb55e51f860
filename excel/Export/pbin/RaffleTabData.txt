com.tencent.wea.xlsRes.table_RaffleTabData
excel/xls/C_抽奖页签.xlsx sheet:页签顺序
rows {
  id: 1
  order: 999
  luaViewName: "UI_Lottery_SeasonInstSubView"
  raffleIdArray: 3000
  raffleIdArray: 30001
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonInstSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 2
  order: 999
  luaViewName: "UI_Lottery_ActiveSubview"
  raffleIdArray: 1000
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 3
  order: 999
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5000
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 4
  order: 999
  luaViewName: "UI_Lottery_GuideSubView"
  raffleIdArray: 5
  unlockFunction: "LotteryGuideCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_GuideSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 5
  order: 999
  luaViewName: "UI_Lottery_CardSubView"
  raffleIdArray: 5201
  drawAgainTxt1: "再前进1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_CardSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6
  order: 999
  luaViewName: "UI_Lottery_MultiRaffleSubView"
  raffleIdArray: 5103
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_MultiRaffleSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 7
  order: 999
  luaViewName: "UI_Lottery_PerfectSubView"
  raffleIdArray: 5301
  mallId: 33
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_PerfectSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 8
  order: 999
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8000
  mallId: 18
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 9
  order: 999
  luaViewName: "UI_Lottery_ShinChanSubView"
  raffleIdArray: 5102
  raffleIdArray: 5101
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50201
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_ShinChanSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10
  order: 999
  luaViewName: "UI_Lottery_SocialSubView"
  raffleIdArray: 5401
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 7
  umgName: "UI_Lottery_SocialSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 11
  order: 999
  luaViewName: "UI_Lottery_TobySubView"
  raffleIdArray: 5202
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_TobySubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 12
  order: 999
  luaViewName: "UI_Lottery_NaiLongSubView"
  raffleIdArray: 5001
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_NaiLongSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13
  order: 999
  luaViewName: "UI_Lottery_BaoBaoLongSubView"
  raffleIdArray: 5402
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 7
  umgName: "UI_Lottery_BaoBaoLongSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 14
  order: 999
  luaViewName: "UI_Lottery_AccessoriesSubView_2"
  raffleIdArray: 5002
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView_2"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 15
  order: 999
  luaViewName: "UI_Lottery_SeasonTwoSubView"
  raffleIdArray: 30002
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonTwoSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 16
  order: 999
  luaViewName: "UI_Lottery_TheOtmankaPool"
  raffleIdArray: 5004
  raffleIdArray: 5005
  raffleIdArray: 5003
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50202
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_TheOtmankaPool"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 17
  order: 999
  luaViewName: "UI_Lottery_ActiveSubview2"
  raffleIdArray: 10001
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview2"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 18
  order: 999
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5010
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 19
  order: 999
  luaViewName: "UI_Lottery_ShinChanSubView"
  raffleIdArray: 5104
  raffleIdArray: 5105
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50201
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_ShinChanSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20
  order: 999
  luaViewName: "UI_Lottery_SocialSubView"
  raffleIdArray: 5403
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 7
  umgName: "UI_Lottery_SocialSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 21
  order: 999
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8001
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 22
  order: 999
  luaViewName: "UI_Lottery_MultiRaffleSubView"
  raffleIdArray: 5106
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_MultiRaffleSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 23
  order: 999
  luaViewName: "UI_Lottery_TobySubView"
  raffleIdArray: 5011
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_TobySubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 24
  order: 999
  luaViewName: "UI_Lottery_NaiLongSubView"
  raffleIdArray: 5012
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_OmiSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 30
  order: 999
  luaViewName: "UI_Lottery_SeasonSubView"
  raffleIdArray: 30003
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonThreeSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 32
  order: 999
  luaViewName: "UI_Lottery_KungFuPanda_MainView"
  raffleIdArray: 5107
  raffleIdArray: 5108
  raffleIdArray: 5109
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50204
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_KungFuPanda_MainView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 35
  order: 999
  luaViewName: "UI_Lottery_ActiveSubview"
  raffleIdArray: 10002
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview3"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 34
  order: 999
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8002
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView2"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 36
  order: 999
  luaViewName: "UI_Lottery_StarSubView"
  raffleIdArray: 5016
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_StarSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 37
  order: 999
  luaViewName: "UI_Lottery_PerfectSubView"
  raffleIdArray: 5302
  mallId: 101
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_PerfectSubView2"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 38
  order: 999
  luaViewName: "UI_Lottery_MultiTab"
  raffleIdArray: 5017
  raffleIdArray: 5018
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50205
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_MultiTab"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 39
  order: 999
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5014
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView_3"
  rewardType: 1
  combineType: 2
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 40
  order: 999
  luaViewName: "UI_Lottery_SeasonSubView"
  raffleIdArray: 30004
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonFourSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 41
  order: 999
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5019
  raffleIdArray: 5020
  raffleIdArray: 5021
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50207
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_XiaoXinCardPool"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 42
  order: 999
  luaViewName: "UI_Lottery_VehicleSubView"
  raffleIdArray: 5501
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_VehicleSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 43
  order: 999
  luaViewName: "UI_Lottery_ActiveSubview"
  raffleIdArray: 10003
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview4"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 44
  order: 999
  luaViewName: "UI_Lottery_Maruko_MainView"
  raffleIdArray: 5110
  raffleIdArray: 5111
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50208
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Maruko_MainView"
  discountTxt: "专属优惠！第二套半价"
  rewardType: 2
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 45
  order: 999
  luaViewName: "UI_Lottery_MarukoSubView"
  raffleIdArray: 5404
  raffleIdArray: 5405
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50209
  needChangeOrder: 1
  type: 7
  umgName: "UI_Lottery_MarukoSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 46
  order: 999
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5112
  raffleIdArray: 5113
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50210
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_PopMartSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 47
  order: 999
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8003
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView3"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 48
  order: 999
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5303
  mallId: 105
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50211
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_BelovedVows_MainView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 50
  order: 999
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5026
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FpsSubView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 53
  order: 999
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5023
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView_4"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 54
  order: 999
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5027
  raffleIdArray: 5028
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50212
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AstroBoyCardPool"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 55
  order: 999
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5029
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_ShibaInu_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 56
  order: 999
  luaViewName: "UI_Lottery_TheOtmankaPool"
  raffleIdArray: 5041
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50202
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_TheOtmankaPool"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 57
  order: 999
  luaViewName: "UI_Lottery_BlueSuitSubView"
  raffleIdArray: 8004
  mallId: 107
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_BlueSuitSubView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 58
  order: 99
  luaViewName: "UI_Lottery_Swan_SubView"
  raffleIdArray: 5025
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿x次"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Swan_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 59
  order: 98
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5203
  drawAgainTxt1: "再前进1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_KiteGirl_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 60
  order: 97
  luaViewName: "UI_Lottery_SeasonSubView"
  raffleIdArray: 30005
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonFiveSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 61
  order: 96
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5031
  raffleIdArray: 5032
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50213
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Line_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 62
  order: 95
  luaViewName: "UI_Lottery_ActiveSubview"
  raffleIdArray: 10004
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview5"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 63
  order: 94
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5033
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView_5"
  rewardType: 1
  combineType: 2
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 64
  order: 93
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30006
  mallId: 108
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50214
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_KnightSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 65
  order: 92
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5034
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView_6"
  rewardType: 1
  combineType: 2
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 66
  order: 91
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5035
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView_7"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 67
  order: 89
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5114
  raffleIdArray: 5115
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50215
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Sanrio_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 68
  order: 88
  luaViewName: "UI_Lottery_NaiLongSubView"
  raffleIdArray: 5036
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_NaiLongSubView"
  rewardType: 1
  combineType: 1
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 69
  order: 87
  luaViewName: "UI_Lottery_BaoBaoLongSubView"
  raffleIdArray: 5406
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 7
  umgName: "UI_Lottery_BaoBaoLongSubView"
  rewardType: 1
  combineType: 1
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 70
  order: 86
  luaViewName: "UI_Lottery_KungFuPanda_MainView"
  raffleIdArray: 5116
  raffleIdArray: 5117
  raffleIdArray: 5118
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50204
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_KungFuPanda_MainView"
  rewardType: 1
  combineType: 1
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 72
  order: 85
  luaViewName: "UI_Lottery_VehicleSubView"
  raffleIdArray: 5502
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_VehicleSubView_Romance"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 73
  order: 82
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5119
  raffleIdArray: 5120
  raffleIdArray: 5121
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿{0}次"
  relatedTaskIds: 50217
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_ZhenHuanCardView"
  discountTxt: "可前往六折奖池进行祈愿#可前往四折奖池进行祈愿"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 74
  order: 81
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5204
  drawAgainTxt1: "再前进1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_SummerPool_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 75
  order: 79
  luaViewName: "UI_Lottery_SeasonSubView"
  raffleIdArray: 30007
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonSixSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 76
  order: 90
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5122
  raffleIdArray: 5123
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿{0}次"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_HonorofKingsSubView"
  discountTxt: "可前往六折奖池进行祈愿"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 78
  order: 77
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5305
  mallId: 140
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿5次"
  relatedTaskIds: 50219
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_LostForeverCardPool"
  rewardType: 1
  combineType: 2
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 77
  order: 74
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5043
  raffleIdArray: 5044
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50218
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Toby_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 79
  order: 75
  luaViewName: "UI_Lottery_TobySubView"
  raffleIdArray: 5051
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_TobySubView"
  rewardType: 1
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 80
  order: 76
  luaViewName: "UI_Lottery_BlueSuitSubView"
  raffleIdArray: 8005
  mallId: 107
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_BlueSuitSubView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10005
  order: 99
  luaViewName: "UI_Lottery_ActiveSubview"
  raffleIdArray: 10005
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview6"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 81
  order: 70
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5306
  mallId: 143
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50224
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_QiXiFestival_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1723132800
  }
  showEndTime {
    seconds: 1730390399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 82
  order: 68
  luaViewName: "UI_Lottery_SwingSubView"
  raffleIdArray: 5304
  mallId: 144
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_SwingSubView"
  rewardType: 1
  showStartTime {
    seconds: 1723737600
  }
  showEndTime {
    seconds: 1730995199
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  taskRewardPopType: CommonRewardPop
  rewardGetType: 1
}
rows {
  id: 83
  order: 69
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000
  mallId: 145
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings2SubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 85
  order: 3
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5053
  raffleIdArray: 5054
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50226
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_WerewolfTanabata_MainView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 86
  order: 85
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5205
  drawAgainTxt1: "再前进1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_Anubis_MainView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 90
  order: 67
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30008
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonSevenSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 1001
  order: 999
  raffleIdArray: 5023
  raffleIdArray: 5014
  needChangeOrder: 1
  rewardType: 1
  combineType: 3
  showStartTime {
    seconds: 1716566400
  }
  showEndTime {
    seconds: 1719503999
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 1002
  order: 30
  raffleIdArray: 5035
  raffleIdArray: 5023
  needChangeOrder: 1
  rewardType: 1
  combineType: 3
  showStartTime {
    seconds: 1719504000
  }
  showEndTime {
    seconds: 1798732799
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 98
  order: 65
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000001
  mallId: 147
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_MidAutumnSubView"
  rewardType: 1
  showStartTime {
    seconds: 1726156800
  }
  showEndTime {
    seconds: 1732809599
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 50000
  order: 84
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5037
  raffleIdArray: 5038
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50215
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FallGuysSubView"
  rewardType: 1
  combineType: 2
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 50007
  order: 80
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5049
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Farm_MainView"
  rewardType: 1
  combineType: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 1003
  order: 3
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 9003
  raffleIdArray: 9004
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50230
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_WerewolfMoonfall_MainView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000003
  order: 100
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000010
  raffleIdArray: 13000011
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50227
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm2_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1723392000
  }
  showEndTime {
    seconds: 1726243199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000004
  order: 70
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000002
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_HarvestRabbit"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1727107200
  }
  showEndTime {
    seconds: 1728835199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 91
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000018
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50233
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_ChiikawaCardPool"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1748361600
  }
  showEndTime {
    seconds: 1748361600
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 97
  order: 78
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 50000002
  raffleIdArray: 50000003
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50231
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_MonkeyKing_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1725552000
  }
  showEndTime {
    seconds: 1727020799
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 93
  order: 55
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 70000001
  raffleIdArray: 70000002
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50234
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_PopMart2SubView"
  rewardType: 1
  showStartTime {
    seconds: 1728576000
  }
  showEndTime {
    seconds: 1730303999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 1004
  order: 3
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000012
  raffleIdArray: 13000013
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50232
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_WerewolfMoonfall_MainView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000101
  order: 69
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000101
  mallId: 145
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings3SubView"
  rewardType: 1
  showStartTime {
    seconds: 1726848000
  }
  showEndTime {
    seconds: 1728143999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 94
  order: 82
  luaViewName: "UI_Lottery_SocialSubView"
  raffleIdArray: 5407
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 7
  umgName: "UI_Lottery_SocialSubView"
  rewardType: 1
  showStartTime {
    seconds: 1724342400
  }
  showEndTime {
    seconds: 1726415999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 95
  order: 85
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5045
  raffleIdArray: 5046
  raffleIdArray: 5047
  raffleIdArray: 5048
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50220
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_XiaoxinPhase3Nini_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1724342400
  }
  showEndTime {
    seconds: 1726415999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 96
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5124
  raffleIdArray: 5125
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50228
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_XiaoxinPhase3_MainView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 99
  order: 64
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 40000003
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesGildedFan_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1726329600
  }
  showEndTime {
    seconds: 1727366399
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 103
  order: 60
  luaViewName: "UI_Lottery_VehicleSubView"
  raffleIdArray: 40000004
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_VehicleSubView_Loong"
  rewardType: 1
  showStartTime {
    seconds: 1727971200
  }
  showEndTime {
    seconds: 1734623999
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000005
  order: 80
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000005
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Farm_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1726156800
  }
  showEndTime {
    seconds: 1729785599
  }
  tabTag: 1
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 1005
  order: 74
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8007
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView4"
  rewardType: 1
  showStartTime {
    seconds: 1729526400
  }
  showEndTime {
    seconds: 1729871700
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000102
  order: 58
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000102
  mallId: 152
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings4SubView"
  rewardType: 1
  showStartTime {
    seconds: 1728144000
  }
  showEndTime {
    seconds: 1730390399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 102
  order: 62
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000002
  raffleIdArray: 30000003
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50235
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_SupremoCat_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1727366400
  }
  showEndTime {
    seconds: 1729785599
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 105
  order: 80
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 50001001
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50236
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Colourful_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1727712000
  }
  showEndTime {
    seconds: 1730390399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 101
  order: 85
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 70005201
  drawAgainTxt1: "再前进1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_PapercutGirl_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1728662400
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 30000004
  order: 54
  luaViewName: "UI_Lottery_StellarShuttleSubView"
  raffleIdArray: 30000004
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50238
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_StellarShuttleSubView"
  rewardType: 1
  showStartTime {
    seconds: 1730217600
  }
  showEndTime {
    seconds: 1731427199
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000006
  order: 80
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000006
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FarmCactus_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1727884800
  }
  showEndTime {
    seconds: 1729785599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 104
  order: 57
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000007
  raffleIdArray: 10000008
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50237
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_WerewolfTanabata_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1727798400
  }
  showEndTime {
    seconds: 1728403199
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10006
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10006
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview7"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000001
  order: 1
  luaViewName: "UI_NationalDay_MainView"
  raffleIdArray: 20000001
  drawAgainTxt1: "抽1次"
  drawAgainTxt2: "抽10次"
  needChangeOrder: 0
  type: 9
  umgName: "UI_NationalDay_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1727625600
  }
  showEndTime {
    seconds: 1729180799
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 106
  order: 64
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000005
  raffleIdArray: 30000006
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50215
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Balala_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1727193600
  }
  showEndTime {
    seconds: 1729526399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 107
  order: 56
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 70000003
  raffleIdArray: 70000004
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50210
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_PopMartSubView"
  rewardType: 1
  showStartTime {
    seconds: 1728576000
  }
  showEndTime {
    seconds: 1730303999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 108
  order: 65
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30009
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonEightSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000007
  order: 58
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000014
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm3_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1738857600
  }
  showEndTime {
    seconds: 1739462399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 100
  order: 63
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 50000001
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_LuLuPig_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1726761600
  }
  showEndTime {
    seconds: 1728575999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 120
  order: 44
  luaViewName: "UI_Lottery_SwingSubView"
  raffleIdArray: 5360
  mallId: 144
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_Swing2SubView"
  rewardType: 1
  showStartTime {
    seconds: 1731600000
  }
  showEndTime {
    seconds: 1738857599
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  taskRewardPopType: SimpleRewardPop
  rewardGetType: 1
}
rows {
  id: 6000103
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 60001031
  raffleIdArray: 60001032
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50241
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings6SubView"
  rewardType: 1
  showStartTime {
    seconds: 1732550400
  }
  showEndTime {
    seconds: 1735833599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 111
  order: 52
  luaViewName: "UI_Lottery_PerfectSubView"
  raffleIdArray: 5307
  mallId: 33
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_PerfectSubView"
  rewardType: 1
  showStartTime {
    seconds: 1730131200
  }
  showEndTime {
    seconds: 1731859199
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 110
  order: 51
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5055
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView"
  rewardType: 1
  showStartTime {
    seconds: 1730131200
  }
  showEndTime {
    seconds: 1731859199
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 114
  order: 85
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000005
  raffleIdArray: 40000006
  raffleIdArray: 40000007
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50242
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_HappySuperman_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1730390400
  }
  showEndTime {
    seconds: 1732204799
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 116
  order: 60
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000015
  raffleIdArray: 13000016
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50243
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfRollercoaster_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1730304000
  }
  showEndTime {
    seconds: 1732204799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 2
  rewardGetType: 1
}
rows {
  id: 6000104
  order: 58
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000104
  mallId: 145
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings5SubView"
  rewardType: 1
  showStartTime {
    seconds: 1728144000
  }
  showEndTime {
    seconds: 1734969599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000008
  order: 58
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000009
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FarmUniversal_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1727884800
  }
  showEndTime {
    seconds: 1732204799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000002
  order: 1
  luaViewName: "UI_Cointree_MainView"
  raffleIdArray: 20000002
  drawAgainTxt1: "抽1次"
  drawAgainTxt2: "抽10次"
  needChangeOrder: 0
  type: 9
  umgName: "UI_Cointree_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1727625600
  }
  showEndTime {
    seconds: 1732982399
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 117
  order: 45
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 53000001
  mallId: 159
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50250
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_DoubleElevenCardPool"
  rewardType: 1
  showStartTime {
    seconds: 1730995200
  }
  showEndTime {
    seconds: 1735660799
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 109
  order: 54
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 8008
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HallowmasWitch_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1729872000
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000009
  order: 58
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000017
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50246
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FortuneCatSubView"
  rewardType: 1
  showStartTime {
    seconds: 1729785600
  }
  showEndTime {
    seconds: 1733414399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10007
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10007
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview8"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 30000005
  order: 31
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000007
  raffleIdArray: 30000008
  raffleIdArray: 30000009
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50245
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Connan_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1734624000
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 301
  order: 24
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000008
  mallId: 162
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_IceWaltzSubView"
  rewardType: 1
  showStartTime {
    seconds: 1735833600
  }
  showEndTime {
    seconds: 1740671999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 118
  order: 43
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 50001002
  raffleIdArray: 50001003
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50255
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Balala_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1731686400
  }
  showEndTime {
    seconds: 1734278399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000010
  order: 53
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000018
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50251
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm4_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1730822400
  }
  showEndTime {
    seconds: 1734623999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 121
  order: 40
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000023
  raffleIdArray: 13000024
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50252
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfMoonfall_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1731945600
  }
  showEndTime {
    seconds: 1733673599
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 122
  order: 65
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30010
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonNineSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000011
  order: 59
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000010
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_HarvestRabbit"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1729785600
  }
  showEndTime {
    seconds: 1735228799
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 123
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5206
  drawAgainTxt1: "再前进1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_Nocturne_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1732291200
  }
  showEndTime {
    seconds: 1734019199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 124
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000009
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_OwnSelect_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1738425600
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 125
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000010
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50260
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_LoversSubView"
  rewardType: 1
  showStartTime {
    seconds: 1731340800
  }
  showEndTime {
    seconds: 1735574399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 129
  order: 32
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000010
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50258
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Teletubbies_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1733500800
  }
  showEndTime {
    seconds: 1735315199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 80001
  order: 50
  luaViewName: "UI_Lottery_AnniversaryCelebration_MainView"
  raffleIdArray: 80001
  mallId: 164
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_AnniversaryCelebration_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1735660800
  }
  showEndTime {
    seconds: 1750953599
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 128
  order: 40
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000025
  raffleIdArray: 13000026
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50259
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfConanLinkage_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1734710400
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000105
  order: 1
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000105
  mallId: 145
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings7SubView"
  rewardType: 1
  showStartTime {
    seconds: 1728144000
  }
  showEndTime {
    seconds: 1736697599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000012
  order: 33
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000027
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50261
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm5_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1735660800
  }
  showEndTime {
    seconds: 1737734399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 130
  order: 32
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5041
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WhiteCraneCardPool"
  rewardType: 1
  showStartTime {
    seconds: 1764777600
  }
  showEndTime {
    seconds: 1765382399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  rewardGetType: 1
}
rows {
  id: 10000013
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000011
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Seal_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1740153600
  }
  showEndTime {
    seconds: 1743091199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 131
  order: 8
  luaViewName: "UI_Lottery_WerewolfLuckCard_MainView"
  raffleIdArray: 10000012
  raffleIdArray: 10000013
  raffleIdArray: 10000014
  raffleIdArray: 10000015
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿6次"
  relatedTaskIds: 50263
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfLuckCard_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1735747200
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  validRaffleCount: 2
  rewardGetType: 1
}
rows {
  id: 10000014
  order: 32
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000016
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_ScarecrowSnow_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1736438400
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10008
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10008
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview9"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13001
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5056
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_EsportsGirl_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1713110400
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000106
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 60001061
  raffleIdArray: 60001062
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50266
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings8SubView"
  rewardType: 1
  showStartTime {
    seconds: 1735920000
  }
  showEndTime {
    seconds: 1739462399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000015
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000017
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_NewYearCourtyard_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1735833600
  }
  showEndTime {
    seconds: 1740671999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 132
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5207
  drawAgainTxt1: "前进1-3步"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_GardenGirl_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1736524800
  }
  showEndTime {
    seconds: 1737820799
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 7000101
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 70001011
  raffleIdArray: 70001012
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50266
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings8SubView"
  rewardType: 1
  showStartTime {
    seconds: 1733241600
  }
  showEndTime {
    seconds: 1739203199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  playMode: 4
  rewardGetType: 1
}
rows {
  id: 127
  order: 29
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000029
  raffleIdArray: 13000030
  raffleIdArray: 13000021
  raffleIdArray: 13000022
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50257
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_HideAndSeek_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1736784000
  }
  showEndTime {
    seconds: 1746115199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 133
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5800001
  mallId: 108
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50267
  relatedTaskIds: 50214
  relatedTaskIds: 50268
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_AngelSubView"
  rewardType: 1
  showStartTime {
    seconds: 1737993600
  }
  showEndTime {
    seconds: 1756655999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 134
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000031
  raffleIdArray: 13000032
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50243
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfRollercoaster_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1736611200
  }
  showEndTime {
    seconds: 1739116799
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000107
  order: 10
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000107
  mallId: 152
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings9SubView"
  rewardType: 1
  showStartTime {
    seconds: 1736784000
  }
  showEndTime {
    seconds: 1741190399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000008
  order: 10
  luaViewName: "UI_Activity_CallUp_MainView"
  raffleIdArray: 20000008
  drawAgainTxt1: "抽奖1次"
  drawAgainTxt2: "抽奖1次"
  needChangeOrder: 1
  type: 9
  umgName: "UI_Activity_CallUp_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1737043200
  }
  showEndTime {
    seconds: 1741103999
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 80002
  order: 65
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30011
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonTenSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10010
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10010
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview10"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13002
  order: 2
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000011
  raffleIdArray: 30000012
  raffleIdArray: 30000013
  raffleIdArray: 30000014
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50270
  relatedTaskIds: 50270
  relatedTaskIds: 50215
  relatedTaskIds: 50215
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Sanrio_MainViewS2"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 1
  showTaskViewRule: 3
  rewardGetType: 1
}
rows {
  id: 137
  order: 51
  luaViewName: "UI_Lottery_VehicleSubView"
  raffleIdArray: 40000014
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_VehicleSubView"
  rewardType: 1
  showStartTime {
    seconds: 1738944000
  }
  showEndTime {
    seconds: 1740671999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000016
  order: 33
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000033
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 45001
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm6_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1736092800
  }
  showEndTime {
    seconds: 1740671999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000003
  order: 1
  luaViewName: "UI_NewYearActivities_MainView"
  raffleIdArray: 20000003
  drawAgainTxt1: "抽1次"
  drawAgainTxt2: "抽10次"
  needChangeOrder: 0
  type: 9
  umgName: "UI_NewYearActivities_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1739548799
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000004
  order: 1
  luaViewName: "UI_NewYearActivities_MainView"
  raffleIdArray: 20000004
  drawAgainTxt1: "抽1次"
  drawAgainTxt2: "抽10次"
  needChangeOrder: 0
  type: 9
  umgName: "UI_NewYearActivities_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1739548799
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000005
  order: 1
  luaViewName: "UI_NewYearActivities_MainView"
  raffleIdArray: 20000005
  drawAgainTxt1: "抽1次"
  drawAgainTxt2: "抽10次"
  needChangeOrder: 0
  type: 9
  umgName: "UI_NewYearActivities_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1739548799
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000006
  order: 1
  luaViewName: "UI_NewYearActivities_MainView"
  raffleIdArray: 20000006
  drawAgainTxt1: "抽1次"
  drawAgainTxt2: "抽10次"
  needChangeOrder: 0
  type: 9
  umgName: "UI_NewYearActivities_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1739548799
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 20000007
  order: 1
  luaViewName: "UI_NewYearActivities_MainView"
  raffleIdArray: 20000007
  drawAgainTxt1: "抽1次"
  drawAgainTxt2: "抽10次"
  needChangeOrder: 0
  type: 9
  umgName: "UI_NewYearActivities_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1739548799
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000017
  order: 32
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000018
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FarmScarecrow"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1736092800
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000110
  order: 36
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000110
  mallId: 172
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_JiaLuoSubView"
  rewardType: 1
  showStartTime {
    seconds: 1738166400
  }
  showEndTime {
    seconds: 1769788799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 136
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000035
  raffleIdArray: 13000036
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50271
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfSpringFestival_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1738080000
  }
  showEndTime {
    seconds: 1741276799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 135
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000012
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50272
  needChangeOrder: 1
  type: 5
  umgName: "UI_CyanWhiteSnake_Mainview"
  rewardType: 1
  showStartTime {
    seconds: 1738857600
  }
  showEndTime {
    seconds: 1740671999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000111
  order: 9
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 60001111
  raffleIdArray: 60001112
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50275
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings10SubView"
  rewardType: 1
  showStartTime {
    seconds: 1750694400
  }
  showEndTime {
    seconds: 1752595199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 144
  order: 2
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 53000004
  mallId: 105
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50211
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_BelovedVows_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1739462400
  }
  showEndTime {
    seconds: 1740758399
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000019
  order: 58
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000037
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm3_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1739462400
  }
  showEndTime {
    seconds: 1741881599
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  rewardGetType: 1
}
rows {
  id: 138
  order: 3
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000020
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfCafe_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1740153600
  }
  showEndTime {
    seconds: 1743091199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 141
  order: 8
  luaViewName: "UI_Lottery_WerewolfLuckCard_MainView"
  raffleIdArray: 10000021
  raffleIdArray: 10000022
  raffleIdArray: 10000023
  raffleIdArray: 10000024
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿6次"
  relatedTaskIds: 50276
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfLuckCard_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1739635200
  }
  showEndTime {
    seconds: 1742745599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  validRaffleCount: 5
  rewardGetType: 1
}
rows {
  id: 10000018
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000019
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_DoghouseScarecrow_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1739548800
  }
  showEndTime {
    seconds: 1741881599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 142
  order: 1
  luaViewName: "UI_Lottery_SwingSubView"
  raffleIdArray: 5361
  mallId: 144
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_Swing3SubView"
  rewardType: 1
  showStartTime {
    seconds: 1739462400
  }
  showEndTime {
    seconds: 1748534399
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  taskRewardPopType: SimpleRewardPop
  rewardGetType: 1
}
rows {
  id: 143
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 53000002
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50278
  relatedTaskIds: 50279
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_NineTailedFox_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1740067200
  }
  showEndTime {
    seconds: 1746028799
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 4
  rewardGetType: 1
}
rows {
  id: 140
  order: 29
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8009
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView5"
  rewardType: 1
  showStartTime {
    seconds: 1740672000
  }
  showEndTime {
    seconds: 1742486399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000020
  order: 33
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000038
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 45002
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm7_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1740758400
  }
  showEndTime {
    seconds: 1742486399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 139
  order: 30
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 8011
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_ModernGirlSubView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1745942400
  }
  showEndTime {
    seconds: 1750607999
  }
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000112
  order: 1
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000112
  mallId: 145
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings11SubView"
  rewardType: 1
  showStartTime {
    seconds: 1741363200
  }
  showEndTime {
    seconds: 1744991999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13003
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5208
  drawAgainTxt1: "前进1-3步"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_Messenger_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1741276800
  }
  showEndTime {
    seconds: 1743695999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 80003
  order: 65
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30012
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonElevenSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000021
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000025
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 45003
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_FarmPuppy_Mainview"
  rewardType: 1
  showStartTime {
    seconds: 1739462400
  }
  showEndTime {
    seconds: 1741017599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10009
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10009
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview9"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 145
  order: 3
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000041
  raffleIdArray: 13000042
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50281
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_HideAndSeek2_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1741838400
  }
  showEndTime {
    seconds: 1755187199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13004
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5209
  drawAgainTxt1: "再前进1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_Anubis_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1709740800
  }
  showEndTime {
    seconds: 1743695999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000022
  order: 33
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000039
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 45004
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm8_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1742486400
  }
  showEndTime {
    seconds: 1746028799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 40000015
  order: 24
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000015
  mallId: 162
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_DollMainView"
  rewardType: 1
  showStartTime {
    seconds: 1744300800
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 146
  order: 8
  luaViewName: "UI_Lottery_WerewolfLuckCard_MainView"
  raffleIdArray: 10000026
  raffleIdArray: 10000027
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿6次"
  relatedTaskIds: 50283
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfLuckCard2_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1742572800
  }
  showEndTime {
    seconds: 1744559999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  validRaffleCount: 3
  rewardGetType: 1
}
rows {
  id: 147
  order: 2
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8010
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView6"
  rewardType: 1
  showStartTime {
    seconds: 1742572800
  }
  showEndTime {
    seconds: 1747929599
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
}
rows {
  id: 6000113
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 60001131
  raffleIdArray: 60001132
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50285
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings12SubView"
  rewardType: 1
  showStartTime {
    seconds: 1743436800
  }
  showEndTime {
    seconds: 1746806399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000023
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000028
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_GrandpaFox_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1743177600
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000024
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000029
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_FarmAmusementPark_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1745510400
  }
  showEndTime {
    seconds: 1748534399
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10011
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10011
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_ActiveSubview11"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13005
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000015
  raffleIdArray: 30000016
  raffleIdArray: 30000017
  raffleIdArray: 30000018
  raffleIdArray: 30000019
  raffleIdArray: 30000020
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50289
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_PopMart3SubView"
  rewardType: 1
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13006
  order: 1
  luaViewName: "UI_Lottery_PopMartRewardSubView"
  raffleIdArray: 30000021
  relatedTaskIds: 50287
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_PopMartRewardSubView"
  rewardType: 1
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 2
}
rows {
  id: 148
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000045
  raffleIdArray: 13000046
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50288
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfElf_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1743350400
  }
  showEndTime {
    seconds: 1746460799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000025
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000030
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 45005
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_VegetableHouse_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1743782400
  }
  showEndTime {
    seconds: 1746460799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 149
  order: 8
  luaViewName: "UI_Lottery_WerewolfLuckCard_MainView"
  raffleIdArray: 10000031
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿6次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfMVP_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1743868800
  }
  showEndTime {
    seconds: 1746460799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  validRaffleCount: 3
  rewardGetType: 1
}
rows {
  id: 10000026
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000040
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿{0}次"
  relatedTaskIds: 45006
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FarmAmusementPark2_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1746288000
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13007
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000016
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_OwnSelect_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1743091200
  }
  showEndTime {
    seconds: 1744905599
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000027
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000032
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 45007
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FarmFoodScarecrow_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1744387200
  }
  showEndTime {
    seconds: 1746719999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000028
  order: 80
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000033
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FarmCactus_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1727884800
  }
  showEndTime {
    seconds: 1732204799
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000114
  order: 1
  luaViewName: "UI_Lottery_HonorofKings2SubView"
  raffleIdArray: 6000114
  mallId: 152
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings13SubView"
  rewardType: 1
  showStartTime {
    seconds: 1745596800
  }
  showEndTime {
    seconds: 1748707199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13008
  order: 2
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5210
  drawAgainTxt1: "前进1-3步"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_BlackGoldfish_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1745510400
  }
  showEndTime {
    seconds: 1748534399
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000029
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000047
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 45008
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_PeachCat_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1746720000
  }
  showEndTime {
    seconds: 1749743999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13009
  order: 1
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 5058
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_AccessoriesSubView"
  rewardType: 1
  showStartTime {
    seconds: 1680796800
  }
  showEndTime {
    seconds: 1700236799
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000030
  order: 53
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000048
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50251
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm4_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1747324799
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 150
  order: 40
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000051
  raffleIdArray: 13000052
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50259
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfConanLinkage_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 4080211200
  }
  showEndTime {
    seconds: 4082803199
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13010
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5211
  drawAgainTxt1: "前进1-3步"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_SummerPool_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1745510400
  }
  showEndTime {
    seconds: 1748534399
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13011
  order: 1
  luaViewName: "UI_Lottery_VehicleSubView"
  raffleIdArray: 40000017
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_VehicleSubView_Loong"
  rewardType: 1
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1746719999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13012
  order: 1
  luaViewName: "UI_Lottery_VehicleSubView"
  raffleIdArray: 5503
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_VehicleSubView_Romance"
  rewardType: 1
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1746719999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13013
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5128
  raffleIdArray: 5129
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50228
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_XiaoxinPhase3_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1743955200
  }
  showEndTime {
    seconds: 1746719999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13014
  order: 1
  luaViewName: "UI_Lottery_ShinChanSubView"
  raffleIdArray: 5126
  raffleIdArray: 5127
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50201
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_ShinChanSubView"
  rewardType: 1
  showStartTime {
    seconds: 1743955200
  }
  showEndTime {
    seconds: 1746719999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000031
  order: 33
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000049
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 45009
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_FarmPark_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1746028800
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13015
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000022
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50201
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_PeachCat2_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1741449600
  }
  showEndTime {
    seconds: 1749139199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 151
  order: 3
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000055
  raffleIdArray: 13000056
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50291
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_HideAndSeek3_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1745985600
  }
  showEndTime {
    seconds: 1755187199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 152
  order: 8
  luaViewName: "UI_Lottery_WerewolfLuckCard_MainView"
  raffleIdArray: 10000034
  raffleIdArray: 10000035
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿6次"
  relatedTaskIds: 50292
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfLuckCard_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1746720000
  }
  showEndTime {
    seconds: 1748793599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  validRaffleCount: 3
  rewardGetType: 1
}
rows {
  id: 80004
  order: 65
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30013
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonTwelveSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 153
  order: 1
  luaViewName: "UI_Lottery_SwingSubView"
  raffleIdArray: 53000005
  mallId: 191
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_Artist_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1747324800
  }
  showEndTime {
    seconds: 1751299199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  taskRewardPopType: SimpleRewardPop
  rewardGetType: 1
}
rows {
  id: 13016
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000023
  raffleIdArray: 30000024
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50296
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_KingZhuo_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1748620800
  }
  showEndTime {
    seconds: 1750435199
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 154
  order: 1
  luaViewName: "UI_Lottery_SwingSubView"
  raffleIdArray: 5308
  mallId: 144
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_Swing4SubView"
  rewardType: 1
  showStartTime {
    seconds: 1749744000
  }
  showEndTime {
    seconds: 1755791999
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  taskRewardPopType: SimpleRewardPop
  rewardGetType: 1
}
rows {
  id: 20000009
  order: 10
  luaViewName: "UI_Activity_CallUp_MainView"
  raffleIdArray: 20000009
  drawAgainTxt1: "抽奖1次"
  drawAgainTxt2: "抽奖1次"
  needChangeOrder: 1
  type: 9
  umgName: "UI_Activity_CallUp_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1746028800
  }
  showEndTime {
    seconds: 1750262399
  }
  tabTag: 2
  isShow: true
  tabGroup: 2
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13017
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5059
  raffleIdArray: 5060
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50218
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Toby_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13018
  order: 1
  luaViewName: "UI_Lottery_TobySubView"
  raffleIdArray: 5061
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_TobySubView"
  rewardType: 1
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13019
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000025
  raffleIdArray: 30000026
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50235
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_SupremoCat_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13020
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 50000004
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_LuLuPig_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1748016000
  }
  showEndTime {
    seconds: 1749743999
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 155
  order: 8
  luaViewName: "UI_Lottery_WerewolfLuckCard_MainView"
  raffleIdArray: 10000036
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿6次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfMVP_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1747929600
  }
  showEndTime {
    seconds: 1750607999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  validRaffleCount: 3
  rewardGetType: 1
}
rows {
  id: 156
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000057
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfSpaceWar_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1747411200
  }
  showEndTime {
    seconds: 1750003199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 157
  order: 7
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 53000006
  mallId: 196
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 6
  umgName: "UI_Lottery_Butterfly_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1749139200
  }
  showEndTime {
    seconds: 1753977599
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 158
  order: 9
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5212
  drawAgainTxt1: "前进1-3步"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_WhiteCraneCardPool"
  rewardType: 1
  showStartTime {
    seconds: 1750521600
  }
  showEndTime {
    seconds: 1752940799
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 159
  order: 8
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8012
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_ActivationSubView7"
  rewardType: 1
  showStartTime {
    seconds: 1746720000
  }
  showEndTime {
    seconds: 1751212799
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000032
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000037
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_FarmCherryCottage_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1748534400
  }
  showEndTime {
    seconds: 1750953599
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000033
  order: 33
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000058
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50261
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Farm5_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1749225600
  }
  showEndTime {
    seconds: 1751558399
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000034
  order: 32
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000038
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 45010
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Shark_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1749830400
  }
  showEndTime {
    seconds: 1752767999
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 6000115
  order: 10
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 6000115
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_HonorofKings14SubView"
  rewardType: 1
  showStartTime {
    seconds: 1749830400
  }
  showEndTime {
    seconds: 1752508799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 160
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000060
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfWorldCup_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1748534400
  }
  showEndTime {
    seconds: 1751212799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000035
  order: 31
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000039
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 45011
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FarmAmusementPark3_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1750348800
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000036
  order: 58
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000061
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 50246
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_FortuneCatSubView"
  rewardType: 1
  showStartTime {
    seconds: 1747929600
  }
  showEndTime {
    seconds: 1750348799
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000037
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10000040
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_HuipaiCanteen_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1750089600
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000038
  order: 8
  luaViewName: "UI_Lottery_WerewolfLuckCard_MainView"
  raffleIdArray: 10000041
  raffleIdArray: 10000042
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿6次"
  relatedTaskIds: 50271
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_WerewolfLuckCard2_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1749744000
  }
  showEndTime {
    seconds: 1752163199
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
  validRaffleCount: 2
  rewardGetType: 1
}
rows {
  id: 161
  order: 1
  luaViewName: "UI_Lottery_AccessoriesSubView"
  raffleIdArray: 50000005
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Surrounding_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1748102400
  }
  showEndTime {
    seconds: 1753459199
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13021
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30000027
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50258
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_Teletubbies_MainView"
  rewardType: 1
  showStartTime {
    seconds: 1750435200
  }
  showEndTime {
    seconds: 1752249599
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10012
  order: 99
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 10012
  mallId: 18
  unlockFunction: "LotteryActiveCanShow"
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_S12SealPrayer_Mainview"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13022
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5213
  drawAgainTxt1: "前进1-3步"
  needChangeOrder: 1
  type: 5
  umgName: "UI_Lottery_KiteGirl_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1750521600
  }
  showEndTime {
    seconds: 1752940799
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 80005
  order: 65
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 30014
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 0
  type: 1
  umgName: "UI_Lottery_SeasonThirteenSubView"
  rewardType: 1
  isShow: true
  tabGroup: 1
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 10000039
  order: 33
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 13000062
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿{0}次"
  relatedTaskIds: 45015
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_MarineScenery_SubView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1748448000
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 1
}
rows {
  id: 10000040
  order: 33
  luaViewName: "UI_Lottery_Farm3_MainView"
  raffleIdArray: 13000063
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿1轮"
  relatedTaskIds: 45016
  relatedTaskIds: 45017
  relatedTaskIds: 45018
  needChangeOrder: 1
  type: 4
  umgName: "UI_Lottery_FarmSkyCity_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1748707200
  }
  showEndTime {
    seconds: 1753372799
  }
  isShow: true
  tabGroup: 1
  sortType: 3
  sortTypeOrder: 3
  showTaskViewRule: 5
  rewardGetType: 1
}
rows {
  id: 10000041
  order: 90
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 9696
  drawAgainTxt1: "祈愿1次"
  type: 4
  umgName: "UI_WXGame_SummerSpecialsView"
  combineType: 1
  showStartTime {
    seconds: 1748707200
  }
  showEndTime {
    seconds: 1753372799
  }
  tabGroup: 2
  sortType: 1
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 13023
  order: 1
  luaViewName: "UI_Lottery_ActivationSubView"
  raffleIdArray: 8013
  mallId: 34
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  needChangeOrder: 1
  type: 1
  umgName: "UI_Lottery_StarlightTheater8SubView"
  rewardType: 1
  showStartTime {
    seconds: 1749744000
  }
  showEndTime {
    seconds: 1753199999
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 80006
  order: 50
  luaViewName: "UI_Lottery_AnniversaryCelebration_MainView"
  raffleIdArray: 80006
  mallId: 164
  drawAgainTxt1: "祈愿1次"
  needChangeOrder: 1
  type: 8
  umgName: "UI_Lottery_NineTailedFoxCarrier_SubView"
  rewardType: 1
  showStartTime {
    seconds: 1735660800
  }
  showEndTime {
    seconds: 1753545599
  }
  isShow: true
  tabGroup: 1
  sortType: 4
  sortTypeOrder: 4
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 162
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 40000018
  drawAgainTxt1: "祈愿1次"
  drawAgainTxt2: "祈愿10次"
  relatedTaskIds: 50233
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_ChiikawaCardPool"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1748361600
  }
  showEndTime {
    seconds: 1758297599
  }
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 2
  showTaskViewRule: 1
  rewardGetType: 1
}
rows {
  id: 163
  order: 1
  luaViewName: "UI_Lottery_UniversalPool"
  raffleIdArray: 5062
  raffleIdArray: 5063
  drawAgainTxt1: "祈愿1次"
  relatedTaskIds: 50218
  needChangeOrder: 1
  type: 3
  umgName: "UI_Lottery_Mofusand_MainView"
  rewardType: 1
  combineType: 1
  showStartTime {
    seconds: 1749657600
  }
  showEndTime {
    seconds: 1753977599
  }
  tabTag: 1
  isShow: true
  tabGroup: 1
  sortType: 2
  sortTypeOrder: 1
  showTaskViewRule: 1
  rewardGetType: 1
}
