com.tencent.wea.xlsRes.table_FarmItemConf
excel/xls/Farm/N_农场道具表_收藏品.xlsx sheet:收藏品道具
rows {
  id: 218500
  name: "帝王蝶"
  desc: "一只制作精致的帝王蝶标本，制作者的技艺看上去相当不错，蝴蝶翅膀上层叠的橙色鳞粉闪耀着光泽，形态被保存的非常完美。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_BiaoBen_001_A"
  sort: 1
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218501
  name: "一箱私房钱"
  desc: "一大箱捆扎整齐的纸币，好像是被小心的藏起来了，箱子上的浮土还有些湿润，或许埋掉这箱钱的人才刚刚离开。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Qian_001_A"
  sort: 2
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218502
  name: "黄金蚯蚓"
  desc: "一种被称作“黄金地龙”的罕见蚯蚓，它们喜欢吞吃优质土壤，所以只有在生态环境顶级的优秀农场里才有可能见到它的身影。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_QiuYin_001_A"
  sort: 3
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218503
  name: "妙味松花蛋"
  desc: "某种野鸭的蛋，在特定温度和湿度下会偶然变成松花蛋的样子，臭味奇绝，但吃起来却很鲜美。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SongHuaDan_001_A"
  sort: 4
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218504
  name: "七彩毛团"
  desc: "需要收集数十种小动物的优质绒毛，才能搓出这样的彩色毛球，纤维和纹理丝丝入扣，就像是天然形成的一般。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_MaoTuan_001_A"
  sort: 5
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218505
  name: "黄金便便"
  desc: "处于远古时代的某个部族会将它当作王冠戴在头上，通常最擅长管理动物的族长才享有这种荣耀，金黄的便便是动物们健康快乐的标志，这个部族一定非常喜欢喂养动物。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_BianBian_001_A"
  sort: 6
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218506
  name: "止痛药丸"
  desc: "听说常常站在农场里对着植物鞠躬的虔诚人士难免会磕到头，于是有人发明了这种药丸，吃下去之后无论怎么鞠躬都不再头晕，即便是磕到头也感觉不到疼痛。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_YaoWan_001_A"
  sort: 7
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218507
  name: "蒙面头巾"
  desc: "在农场里窃取东西的飞贼经常使用的蒙面道具，虽说只是掩耳盗铃罢了，但戴上头巾后，这些家伙会立刻产生一种迷之自信，就连盗窃时的身手都不自觉的专业了许多。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_TouJin_001_A"
  sort: 8
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218508
  name: "电子芯片"
  desc: "一种非常先进的智能芯片，能让一台冰冷的机器立刻拥有高级智能，变成具有一定自我意识的机器人，这种发明被认为是伟大但危险的，因此这种电子芯片并未被真正投入使用。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_XinPian_001_A"
  sort: 9
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218509
  name: "人鱼吊坠"
  desc: "据说有一滴人鱼的泪水被封印在吊坠的蓝宝石里，拿起吊坠时，你会被一股若有似无的悲伤笼罩。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPPendant_001_A"
  sort: 10
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218510
  name: "金斧头"
  desc: "纯金打造的斧头，这样贵重的斧头当然不能真的用来伐木，可以当做财富和运气的象征。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPGoldaxe_001_A"
  sort: 11
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218511
  name: "水晶鞋"
  desc: "闪耀着神秘光泽的水晶鞋，是完美无瑕的魔法造物，据说只有心灵最纯净的人才能够穿上它。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPGlassshoes_001_A"
  sort: 12
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218512
  name: "雨丝阳伞"
  desc: "神奇的雨丝在魔法的凝聚下化身成伞的模样，“用雨为你挡雨”这样的事只有淘气的云精灵才会做。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPUmbrella_001_A"
  sort: 13
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218513
  name: "雷击面具"
  desc: "一只古老的面具，据说是古代祈雨时需要佩戴的圣物，样子是照着神兽的模样做的，但没有星宝真正见过这种神兽，盯着面具的眼睛看去，会被一种巨大的力量震慑。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPMask_001_A"
  sort: 14
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218514
  name: "雷水晶"
  desc: "一种在剧烈雷电中产生的水晶，似乎是将转瞬即逝的雷电固定在了水晶石中，拿起它时会有一种酥酥麻麻的感觉，并能隐隐听到噼里啪啦的电流声。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPCrystal_001_A"
  sort: 15
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218515
  name: "巨兽躯干化石"
  desc: "一种古老生物的化石，这一块是躯干部分。如果能找到其他部分，并进行仔细拼接的话，或许能成为一件完整的化石艺术品。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Dinosaurfossils_002_A"
  sort: 16
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218516
  name: "石中剑"
  desc: "由湖中仙女所锻造的宝剑，赠予了勇者，在完成冒险后勇者将宝剑封存石中，等候着下一个命中注定的英雄拔出这把宝剑，开启新的旅程。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_PixelSword_001_A"
  sort: 17
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218517
  name: "巨兽尾部化石"
  desc: "一种古老生物的化石，这一块是尾巴部分。如果能找到其他部分，并进行仔细拼接的话，或许能成为一件完整的化石艺术品。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Dinosaurfossils_003_A"
  sort: 18
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218518
  name: "独角仙"
  desc: "最顶尖的标本大师的第一件作品，但因为保存不当，身体的部分被虫子吃掉了，标本大师没有将它丢弃，反而用玉石补全了残缺的部分，让这件标本成为了独一无二的艺术品。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_UnicornBug_001_A"
  sort: 19
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218519
  name: "巨兽头部化石"
  desc: "一种古老生物的化石，这一块是头颅部分。如果能找到其他部分，并进行仔细拼接的话，或许能成为一件完整的化石艺术品。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Dinosaurfossils_001_A"
  sort: 20
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218520
  name: "神秘石像"
  desc: "矗立在遥远海岸的神秘石像，无人知道是谁雕刻了它们，而发现它们的考古学家认为它们在看向宇宙深处的某个遥远的星系，也许那正是它们的故乡。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Statue_001_A"
  sort: 21
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218521
  name: "放大镜"
  desc: "为了寻找农场窃贼的蛛丝马迹，斥巨资打造的放大镜，由于价格昂贵，一定要到不得已的时候再使用，但直到现在，都没等到不得已的时候。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Magnifier_001_A"
  sort: 22
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218522
  name: "古董计算机"
  desc: "由于机型过于老旧已经被市场淘汰，但在开机后还能看到一封未寄出的邮件，也许作为生产蓝本的计算机里就存在着这封邮件，只是不知道想寄给谁，未出口的话再也无人听到。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Computer_001_A"
  sort: 23
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218523
  name: "神秘的短笛"
  desc: "看上去是一支普通的斗蛇人短笛，但其实很不一样，吹奏出的嘶哑音律不仅不会让人觉得刺耳，反而有点上头，就算有再多烦心事，也会在音律的作用下暂时忘却。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPPiccolo_01_A"
  sort: 24
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218524
  name: "黄金圣甲虫"
  desc: "黄金雕刻而成的圣甲虫，这种虫子把粪球看得比生命还重要，很多人会对它不屑一顾，但在古老的文明中，这种甲虫是勇气和毅力的象征，就算千百次失败它们都会爬起来继续推那只命中注定的粪球。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPBeetle_01_A"
  sort: 25
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218525
  name: "古老的天平"
  desc: "年代久远，完全可以放进博物馆里的古董，据说这杆天枰可以称量任何东西，包括灵魂，但要小心了，要是放上去的灵魂比一片羽毛还要重，就会遭受可怕的厄运。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPLibra_01_A"
  sort: 26
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218526
  name: "星光农场主徽章"
  desc: "徽章背面刻着一行小字：“新星农场主纪念章”，拥有这枚徽章意味着你已经是一名合格的农场主了。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Medal_001_A"
  sort: 27
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218528
  name: "独角龙头部化石"
  desc: "一种古老生物的化石，这一块是头部。如果能找到其他部分，或许能拼成一件完整的化石。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Triceratops_001_A"
  sort: 28
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218529
  name: "独角龙躯干化石"
  desc: "一种古老生物的化石，这一块是躯干。如果能找到其他部分，或许能拼成一件完整的化石。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Triceratops_002_A"
  sort: 29
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218530
  name: "蓝色豆娘"
  desc: "蓝色豆娘的标本，有着修长纤细的身躯和蓝色的翅膀，制作精美，保存完整。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_DragonFly_001_A"
  sort: 30
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218531
  name: "独角龙尾部化石"
  desc: "一种古老生物的化石，这一块是尾巴。如果能找到其他部分，或许能拼成一件完整的化石。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Triceratops_003_A"
  sort: 31
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218532
  name: "猫头鹰木雕"
  desc: "有着鎏金装饰的猫头鹰木雕，好似是真的猫头鹰在闭着眼沉睡，随时都能睁开眼，扑棱扑棱翅膀飞起来。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_WoodenOwl_001_A"
  sort: 32
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218533
  name: "金元宝"
  desc: "沉甸甸的超大金元宝，摆在家里，会让整个家都变得金碧辉煌。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_YuanBao_001_A"
  sort: 33
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218534
  name: "精美的蛋"
  desc: "用复杂工艺装饰华丽的蛋壳，只有极少的工匠会制作，但科技发达后，机器也可以制作出同样的效果。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Aartifact_001_A"
  sort: 34
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218535
  name: "复古唱片机"
  desc: "造型复古的唱片机，但内部装置很先进，录入了市面上所有的歌，哪怕对着它哼唱模糊的旋律，它都可以继续播放。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_RetroCD_001_A"
  sort: 35
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218536
  name: "古老卷轴"
  desc: "由牛皮绳绑起来的古老卷轴，不知是哪个时代的古董，里面写着无法阅读的语言。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Decree_001_A"
  sort: 36
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218537
  name: "狐狸面罩"
  desc: "以狐狸为原型设计的面罩，在农场窃贼中很受追捧，但也有窃贼最讨厌这款面罩，因为他们怕自己戴上之后，会像狐狸一样“脚滑”。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_FoxMask_001_A"
  sort: 37
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218538
  name: "海神三叉戟"
  desc: "传说中海神的武器，相传海神曾用它击碎岩石，让清泉流出来浇灌大地，使万物丰收。虽然不能百分百确定这柄三叉戟的真实性，但只要触碰过它的人都会对此深信不疑，因为它散发着冰晶之光的长柄比冰川还要寒冷。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Trident_001_A"
  sort: 38
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218539
  name: "铁船锚"
  desc: "一只沉入大海的船锚，即使被打捞起来后去除了表面附着的海洋生物，还是能闻到上面有着大海的咸腥味。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Anchor_001_A"
  sort: 39
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218540
  name: "寻宝图"
  desc: "画在泛黄羊皮纸上的藏宝图，纸张无比脆弱好似轻轻一碰就会碎，据说这张图上标记的是最伟大海盗黑胡子的宝藏。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_TreasureMap_001_A"
  sort: 40
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218541
  name: "小猎龙尾部化石"
  desc: "一种古老生物的化石，这一块是尾部。如果能找到其他部分，或许能拼成一件完整的化石。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Velociraptor_002_A"
  sort: 41
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218542
  name: "小猎龙头部化石"
  desc: "一种古老生物的化石，这一块是头部。如果能找到其他部分，或许能拼成一件完整的化石。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Velociraptor_001_A"
  sort: 42
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218543
  name: "木雕熊头"
  desc: "工艺粗犷的木雕工艺品，是一整只熊头的形状，不仅不恐怖，反而有几分憨态可掬。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_BearHead_001_A"
  sort: 43
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218544
  name: "石像猫"
  desc: "由一整块石头雕刻而成的石像猫，脖子上戴着闪闪发光的金项圈，在古老的文明中它被当作神明崇拜，时至今日，依然有很多星宝是猫猫的信徒。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_CatStatue_001_A"
  sort: 44
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218545
  name: "王之盾"
  desc: "由战争女神赐给国王的盾牌，国王用它保护了自己的子民，在战争结束后，国王将它置于城墙之上，只要盾牌存在一日，这片土地便永不受外敌的侵害。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_PixelShield_001_A"
  sort: 45
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218546
  name: "漂流瓶"
  desc: "在大海中漂泊许久的漂流瓶，瓶身上的划痕昭示着这一路旅程的不易。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_DriftBottle_001_A"
  sort: 46
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218547
  name: "朴素渔轮"
  desc: "最为简单的渔轮，没有任何多余的装饰，毕竟最伟大的钓鱼佬，只需要最朴素的工具。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_FishingVessel_001_A"
  sort: 47
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218548
  name: "海洋之心"
  desc: "由硕大的蓝色钻石和珍珠串成的项链，相传是人鱼公主最喜爱的首饰，拿起这条项链，仿佛聆听着来自海底的古老低语，还能感受得到千年前闪烁过的星光。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_OceanHeart_001_A"
  sort: 48
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218549
  name: "鼠仙人"
  desc: "晶莹剔透的玉雕，是一只双手合十的小鼠形象，据说好好供奉它，就会有好事发生。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_WishStatue_001_A"
  sort: 49
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218550
  name: "另类面罩"
  desc: "传言中被诅咒的面罩，善良的星宝戴上后会变得更加善良，邪恶的星宝戴上后会被放大心中的恶，久而久之再也没有星宝敢佩戴它，而是作为摆件流传下来。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SkeletonMask_001_A"
  sort: 50
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218551
  name: "万能耳机"
  desc: "专为小动物设计的耳机，保证了每种耳朵都可以佩戴，让每一种动物都能听上音乐。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Monitor_001_A"
  sort: 51
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218552
  name: "天外来客"
  desc: "有着神秘光泽的陨石，轻抚表面还能感受到温暖，仿佛拥有生命一般。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Meteorite_001_A"
  sort: 52
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218553
  name: "牛语翻译器"
  desc: "可以翻译牛牛语言的神奇翻译器"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPinterphone_01_A"
  sort: 53
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218554
  name: "招财牛"
  desc: "可以带来好运的招财牛"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPdairycattle_01_A"
  sort: 54
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218555
  name: "绿蝉"
  desc: "绿蝉的标本，有着轻盈的翅膀和美丽的花纹，栩栩如生，好似还能听到夏日的蝉鸣。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Cicada_001_A"
  sort: 55
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218556
  name: "恐龙蛋化石"
  desc: "恐龙蛋的化石，还能看到蛋壳上的裂纹，不知道被精心照料的话，还能不能孵化出恐龙宝宝。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_DragonEgg_001_A"
  sort: 56
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218557
  name: "公牛雕塑"
  desc: "玻璃烧制的红色公牛摆件，用最简洁的线条勾勒出了公牛的健硕体态，是一件不可多得的艺术品。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_BullStatue_001_A"
  sort: 57
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218558
  name: "金鹿头"
  desc: "由金银两种材料打造而成，造型威严的雄鹿。据传公爵在狩猎时，因风雪迷失在森林中，在公爵快要冻死之际，是一头金色的雄鹿引导他走出了森林，公爵为了表示自己的感谢打造了这只鹿头，从此之后，金色雄鹿也成为了守护神的代名词。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_DeerHead_001_A"
  sort: 58
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218559
  name: "旧舵轮"
  desc: "曾经属于一艘海盗船，握住它仿佛还能感受到海风吹拂耳畔，下一段冒险等待开启。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Rudder_001_A"
  sort: 59
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218560
  name: "玻璃水母"
  desc: "晶莹剔透的玻璃水母，放在床边，能梦到许多蓝色的小水母，一扭一扭的在海里跳舞。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_JellyfishLamp_001_A"
  sort: 60
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218561
  name: "星石海豚"
  desc: "某位艺术大师的作品，由陨落的星石雕刻而成的两只海豚，如果心怀悲伤或愤怒触摸它，情绪便会慢慢变得平静，使人获得安宁。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Dolphin_001_A"
  sort: 61
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218562
  name: "惊喜盒子"
  desc: "不知道装着什么的盒子，有的星宝觉得里面是宝石或金币，有的星宝觉得里面是一堆破烂，但因为无法打开它，也无从知晓了。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_MysteryBox_001_A"
  sort: 62
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218563
  name: "皮搋子电筒"
  desc: "在手电筒设计大赛中荣获第一名，完美的将皮搋子和手电筒结合在了一起，拿着它走夜路，也会安心不少。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Plunger_001_A"
  sort: 63
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218564
  name: "玉米轮胎"
  desc: "玉米外形的轮胎，会在路面上留下一道道玉米粒形状的车辙，深受仓鼠等小动物的喜欢。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_CornTires_001_A"
  sort: 64
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218565
  name: "星星锤"
  desc: "拥有星核驱动，能瞬间重塑分子结构的锤子，但只有纯洁善良的星宝可以拿起它，但凡在使用的过程中有一丝坏念头，星星锤立刻会变得重逾千钧。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Hammer_001_A"
  sort: 65
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218566
  name: "幸运骰子"
  desc: "牛牛阿花乘船时捡到的小玩意儿，打磨得十分精细，闻上去有股葱花味。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_LuckyDice_001_A"
  sort: 66
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218568
  name: "狼蛛"
  desc: "狼蛛的标本，有着修长的肢体和棕色的绒毛，每一个细节都处理的无比完美，是一件值得珍藏的藏品。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Aula_001_A"
  sort: 68
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218569
  name: "猫咪爪套"
  desc: "毛茸茸的粉色爪套，软软的肉垫让人忍不住想捏一捏，不仅保暖，还十分可爱。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_CatPaw_001_A"
  sort: 69
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218570
  name: "魔术兔"
  desc: "魔术大师使用过的道具，据说能变出来任何想要的东西，但只有大师本人能做到，如果是图谋不轨的星宝将手伸进帽子里，就会被帽子里的兔子狠狠咬手指。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_MagicRabbit_001_A"
  sort: 70
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218571
  name: "小跳蛙"
  desc: "绿色涂装的金属小跳蛙，拧几圈发条就能蹦蹦跳跳很久，有趣的小玩具，深受星宝们的喜爱。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPqingwa_001_A"
  sort: 71
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218572
  name: "失落的宝藏"
  desc: "最富有海盗的宝藏，他在退隐前，将自己所有的财产都沉入了深海，千百年来寻找宝藏的人趋之若鹜，但只有最顶尖的航海家，才能一睹宝藏的真容。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_TreasureBox_001_A"
  sort: 72
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218573
  name: "庆典大炮"
  desc: "通体鲜红，画着星星图案的大炮，点燃后能喷出彩带和亮片，为节庆增加热闹的氛围。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_ToyCannon_001_A"
  sort: 73
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218574
  name: "春燕纸鸢"
  desc: "描绘着蓝色燕子图案的纸鸢，笔法飘逸，色彩鲜艳，满载春日的气息。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Kite_001_A"
  sort: 74
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218576
  name: "宝石吞吞花"
  desc: "天然的红色宝石，没有任何雕琢，自然形成了吞吞花的形状，靠近了好像还能闻到吞吞花的独特气味。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_Chomper_001_A"
  sort: 76
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218577
  name: "金牌牛排"
  desc: "某位十分喜爱牛排的农场主定制的展示菜，用于自己餐厅的招牌，据说在他定制好展示菜后，他农场里的牛就消失不少。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPniupai_001_A"
  sort: 77
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218578
  name: "你好鸭"
  desc: "带着工牌的木雕鸭子，外形简洁可爱，一些餐厅会将它放在柜台上迎接顾客，因为深受顾客喜爱，被授予了“经理”的职位和工牌。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPzhongzhie_001_A"
  sort: 78
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218580
  name: "木雕船锚"
  desc: "结合了灯塔外形雕刻而成的木制船锚，虽说不能被当做真的船锚使用，但也是一件漂亮的装饰品。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPchuanmao_001_A"
  sort: 80
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218581
  name: "星星鱼捏捏"
  desc: "奇特鱼类外形的解压玩具，头顶挂着一颗发光的星星，手感柔软，是释放压力的不二之选。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPlightfish_001_A"
  sort: 81
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218582
  name: "樱花胸针"
  desc: "宝石雕刻而成，饰以黄金底座的胸针，雕工纯熟，宝石透亮，任何星宝戴上都会变得美丽动人。"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPxiongzhen_001_A"
  sort: 82
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
rows {
  id: 218583
  name: "样板间藏品"
  type: ItemType_Farm_Collection
  icon: "Icon_Farm_Col_SCPxiongzhen_001_A"
  sort: 83
  packable: true
  unStackable: true
  cropCategory: FCC_COLLECTION
  ownMaxNum: 1
  extraOp: FIEO_TrigCollection
}
