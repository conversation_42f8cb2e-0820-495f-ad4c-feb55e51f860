com.tencent.wea.xlsRes.table_RandEventData
excel/xls/S_闪电赛词缀配置.xlsx sheet:兼容事件表
rows {
  id: 2000
  title: "无事发生"
  desc: "本局没有事件发生"
  weight: 30
  eventType: ReT_CompEvent
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2001
  title: "无限火力：道具"
  desc: "道具刷新速度翻倍"
  weight: 100
  mutexCompEventIDs: 2002
  mutexCompEventIDs: 2003
  mutexCompEventIDs: 2004
  mutexCompEventIDs: 2005
  mutexCompEventIDs: 2022
  mutexCompEventIDs: 2023
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_030"
  EventTriggerId: 13001
  IsShow: true
  ShowLoc: 2
}
rows {
  id: 2002
  title: "无限火力：大招"
  desc: "大招充能速度翻倍"
  weight: 100
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2003
  mutexCompEventIDs: 2004
  mutexCompEventIDs: 2005
  mutexCompEventIDs: 2022
  mutexCompEventIDs: 2023
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_029"
  EventTriggerId: 13000
  IsShow: true
  ShowLoc: 2
}
rows {
  id: 2003
  title: "推力加持"
  desc: "全场被风力覆盖，受到向前推力"
  weight: 100
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2002
  mutexCompEventIDs: 2004
  mutexCompEventIDs: 2005
  mutexCompEventIDs: 2022
  mutexCompEventIDs: 2023
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_026"
  IsShow: true
  ShowLoc: 2
}
rows {
  id: 2004
  title: "重力改变"
  desc: "重力改变，跳跃高度翻倍"
  weight: 100
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2002
  mutexCompEventIDs: 2003
  mutexCompEventIDs: 2005
  mutexCompEventIDs: 2022
  mutexCompEventIDs: 2023
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_025"
  EventTriggerId: 10004
  EventTriggerParam: "0.5"
  IsShow: true
  ExtraBuff: "UClass\'/Game/Feature/OGC/Blueprints/Buff/LevelEvent/BP_Buff_GravityScale.BP_Buff_GravityScale_C\'"
  ShowLoc: 2
}
rows {
  id: 2005
  title: "黑夜笼罩"
  desc: "夜晚覆盖，依靠光柱获取信息"
  weight: 100
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2002
  mutexCompEventIDs: 2003
  mutexCompEventIDs: 2004
  mutexCompEventIDs: 2022
  mutexCompEventIDs: 2023
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_003"
  IsShow: true
  ShowLoc: 2
}
rows {
  id: 2006
  title: "获得道具：变大"
  desc: "获得可无限使用的道具：变大汉堡"
  weight: 0
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_012"
  EventTriggerId: 11000
  EventTriggerParam: "20026"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2007
  title: "获得道具：变小"
  desc: "获得可无限使用的道具：变小药水"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_013"
  EventTriggerId: 11000
  EventTriggerParam: "20027"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2008
  title: "获得道具：香蕉"
  desc: "获得可无限使用的道具：香蕉"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_017"
  EventTriggerId: 11000
  EventTriggerParam: "20025"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2009
  title: "获得道具：加速"
  desc: "获得可无限使用的道具：加速鞋"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_016"
  EventTriggerId: 11000
  EventTriggerParam: "20028"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2010
  title: "获得道具：隐身"
  desc: "获得可无限使用的道具：隐身斗篷"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_020"
  EventTriggerId: 11000
  EventTriggerParam: "20030"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2011
  title: "获得道具：炸弹"
  desc: "获得可无限使用的道具：炸弹"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_022"
  EventTriggerId: 11000
  EventTriggerParam: "20047"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2012
  title: "获得道具：弹簧拳"
  desc: "获得可无限使用的道具：弹簧拳"
  weight: 100
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_014"
  EventTriggerId: 11000
  EventTriggerParam: "20045"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2013
  title: "获得道具：蛋糕"
  desc: "获得可无限使用的道具：蛋糕"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_015"
  EventTriggerId: 11000
  EventTriggerParam: "20046"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2014
  title: "获得道具：烟花火箭"
  desc: "获得可无限使用的道具：烟花火箭"
  weight: 100
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_018"
  EventTriggerId: 11000
  EventTriggerParam: "20043"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2015
  title: "获得道具：烟花炮"
  desc: "获得可无限使用的道具：烟花炮"
  weight: 100
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_019"
  EventTriggerId: 11000
  EventTriggerParam: "20050"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2016
  title: "获得道具：云朵"
  desc: "获得可无限使用的道具：云朵"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_021"
  EventTriggerId: 11000
  EventTriggerParam: "20044"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2017
  title: "获得大招：道具盲盒"
  desc: "获得充能大招：道具盲盒"
  weight: 0
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  mutexCompEventIDs: 2029
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_006"
  EventTriggerId: 11001
  EventTriggerParam: "11118"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2018
  title: "获得大招：能量护盾"
  desc: "获得充能大招：能量护盾"
  weight: 100
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  mutexCompEventIDs: 2029
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_008"
  EventTriggerId: 11001
  EventTriggerParam: "11013"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2019
  title: "获得大招：神奇种子"
  desc: "获得充能大招：神奇种子"
  weight: 100
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  mutexCompEventIDs: 2029
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_009"
  EventTriggerId: 11001
  EventTriggerParam: "11019"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2020
  title: "获得大招：陨石术"
  desc: "获得充能大招：陨石术"
  weight: 0
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  mutexCompEventIDs: 2029
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_011"
  EventTriggerId: 11001
  EventTriggerParam: "11009"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2021
  title: "获得大招：雪人炸弹"
  desc: "获得充能大招：雪人炸弹"
  weight: 100
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  mutexCompEventIDs: 2029
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_010"
  EventTriggerId: 11001
  EventTriggerParam: "11004"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2022
  title: "击飞值"
  desc: "受击增加击飞值，值越高，被打的越远"
  weight: 100
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2002
  mutexCompEventIDs: 2003
  mutexCompEventIDs: 2004
  mutexCompEventIDs: 2005
  mutexCompEventIDs: 2023
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_007"
  IsShow: true
  ShowLoc: 2
}
rows {
  id: 2023
  title: "血量值"
  desc: "获得血量值，血量为空则淘汰"
  weight: 0
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2002
  mutexCompEventIDs: 2003
  mutexCompEventIDs: 2004
  mutexCompEventIDs: 2005
  mutexCompEventIDs: 2022
  eventType: ReT_CompEvent
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2024
  title: "获得道具：火箭鸭"
  desc: "获得可无限使用的道具：火箭鸭"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_034"
  EventTriggerId: 11000
  EventTriggerParam: "20051"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2025
  title: "获得大招：反弹护盾"
  desc: "获得充能大招：反弹护盾"
  weight: 100
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_035"
  EventTriggerId: 11001
  EventTriggerParam: "11020"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2026
  title: "获得大招：神偷手"
  desc: "获得充能大招：神偷手"
  weight: 0
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  mutexCompEventIDs: 2030
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_036"
  EventTriggerId: 11002
  EventTriggerParam: "11021"
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2027
  title: "获得大招：雷隐术"
  desc: "获得充能大招：雷隐术"
  weight: 50
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2028
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_037"
  EventTriggerId: 11001
  EventTriggerParam: "11012"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2028
  title: "获得大招：火箭跳"
  desc: "获得充能大招：火箭跳"
  weight: 50
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2029
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_038"
  EventTriggerId: 11002
  EventTriggerParam: "11017"
  IsShow: true
  ShowLoc: 1
}
rows {
  id: 2029
  title: "反击风暴"
  desc: "倒地后起身产生小范围爆炸，使敌人眩晕"
  weight: 0
  mutexCompEventIDs: 2017
  mutexCompEventIDs: 2018
  mutexCompEventIDs: 2019
  mutexCompEventIDs: 2020
  mutexCompEventIDs: 2021
  mutexCompEventIDs: 2025
  mutexCompEventIDs: 2026
  mutexCompEventIDs: 2027
  mutexCompEventIDs: 2028
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_031"
  EventTriggerId: 12003
  IsShow: false
  ShowLoc: 0
}
rows {
  id: 2030
  title: "再来一发"
  desc: "道具命中时，再次获得此道具"
  weight: 80
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2002
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2031
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_032"
  EventTriggerId: 12001
  IsShow: true
  ShowLoc: 2
}
rows {
  id: 2031
  title: "草船借箭"
  desc: "被道具命中时，随机获得道具"
  weight: 80
  mutexCompEventIDs: 2001
  mutexCompEventIDs: 2006
  mutexCompEventIDs: 2007
  mutexCompEventIDs: 2008
  mutexCompEventIDs: 2009
  mutexCompEventIDs: 2010
  mutexCompEventIDs: 2011
  mutexCompEventIDs: 2012
  mutexCompEventIDs: 2013
  mutexCompEventIDs: 2014
  mutexCompEventIDs: 2015
  mutexCompEventIDs: 2016
  mutexCompEventIDs: 2024
  mutexCompEventIDs: 2030
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_033"
  EventTriggerId: 12002
  IsShow: true
  ShowLoc: 2
}
rows {
  id: 2032
  title: "复活天使"
  desc: "淘汰时若队友存活，一定时间后能够复活"
  weight: 0
  eventType: ReT_CompEvent
  image: "T_RandomEvent_Events_039"
  IsShow: false
  ShowLoc: 0
}
