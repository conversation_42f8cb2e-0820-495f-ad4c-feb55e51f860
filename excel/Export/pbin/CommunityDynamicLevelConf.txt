com.tencent.wea.xlsRes.table_CommunityDynamicLevelConf
excel/xls/D_大厅关卡加载配置.xlsx sheet:动态关卡配置
rows {
  id: 1
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic"
  startTime: "2024-01-01 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 2
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_dynamic_Props01"
  startTime: "2024-01-01 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 3
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_BirthNormal"
  startTime: "2024-01-01 00:00:00"
  endTime: "2024-12-15 00:00:00"
}
rows {
  id: 4
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_BirthConan"
  startTime: "2024-12-15 00:00:00"
  endTime: "2025-1-6 00:00:00"
}
rows {
  id: 5
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_BirthNormal"
  startTime: "2025-1-6 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 6
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Game_1"
  startTime: "2025-01-06 00:00:00"
  endTime: "2025-02-24 00:00:00"
}
rows {
  id: 7
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Game"
  startTime: "2025-02-24 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 8
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Dance"
  startTime: "2025-02-03 00:00:00"
  endTime: "2025-02-24 00:00:00"
}
rows {
  id: 9
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_WanSongShuYuan"
  startTime: "2025-02-03 00:00:00"
  endTime: "2025-02-24 00:00:00"
}
rows {
  id: 10
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_BaiYiBuTie"
  startTime: "2024-01-01 00:00:00"
  endTime: "2025-12-31 00:00:00"
}
rows {
  id: 11
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Sanrio_03"
  startTime: "2025-01-06 00:00:00"
  endTime: "2025-01-28 00:00:00"
}
rows {
  id: 12
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Sanrio_03"
  startTime: "2025-02-13 00:00:00"
  endTime: "2025-02-24 00:00:00"
}
rows {
  id: 13
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_PlantReplace_Unload"
  startTime: "2025-02-05 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 14
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_HotBalloon"
  startTime: "2025-02-05 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 15
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_ValentineDay"
  startTime: "2025-02-14 00:00:00"
  endTime: "2025-03-14 00:00:00"
}
rows {
  id: 16
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_RedEnvelope"
  startTime: "2025-02-10 00:00:00"
  endTime: "2025-02-21 00:00:00"
}
rows {
  id: 17
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_SpringOuting"
  startTime: "2025-02-28 00:00:00"
  endTime: "2025-03-31 00:00:00"
}
rows {
  id: 18
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCSakura"
  startTime: "2025-03-14 00:00:00"
  endTime: "2025-04-11 00:00:00"
}
rows {
  id: 19
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGC_AprilFoolsDay"
  startTime: "2025-03-31 00:00:00"
  endTime: "2025-04-18 00:00:00"
}
rows {
  id: 20
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Entrance04"
  startTime: "2025-04-11 00:00:00"
  endTime: "2025-05-01 00:00:00"
}
rows {
  id: 21
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCPanda"
  startTime: "2024-04-18 00:00:00"
  endTime: "2024-05-01 00:00:00"
}
rows {
  id: 22
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGC_Meow"
  startTime: "2025-05-01 00:00:00"
  endTime: "2025-05-30 00:00:00"
}
rows {
  id: 23
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_College"
  startTime: "2025-05-09 00:00:00"
  endTime: "2025-05-16 00:00:00"
}
rows {
  id: 26
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCPanda_2"
  startTime: "2025-05-01 00:00:00"
  endTime: "2025-05-09 00:00:00"
}
rows {
  id: 29
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCLovers"
  startTime: "2025-05-16 00:00:00"
  endTime: "2025-05-30 00:00:00"
}
rows {
  id: 30
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCParkour"
  startTime: "2025-05-30 00:00:00"
  endTime: "2025-06-08 00:00:00"
}
rows {
  id: 32
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGC_ChildrensDay"
  startTime: "2025-05-30 00:00:00"
  endTime: "2025-06-09 00:00:00"
}
rows {
  id: 33
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGC_ChildrensDay_Unload"
  startTime: "2025-06-24 00:00:00"
  endTime: "2025-12-12 00:00:00"
}
rows {
  id: 34
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Chiikawa"
  startTime: "2024-01-01 00:00:00"
  endTime: "2025-12-12 00:00:00"
}
rows {
  id: 35
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_ChiikawaGameplay_01"
  startTime: "2024-01-01 00:00:00"
  endTime: "2025-12-12 00:00:00"
}
rows {
  id: 37
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_ConanB"
  startTime: "2025-02-05 00:00:00"
  endTime: "2025-08-15 00:00:00"
}
rows {
  id: 38
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Graduation"
  startTime: "2025-06-06 00:00:00"
  endTime: "2025-06-27 00:00:00"
}
rows {
  id: 39
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_Graduation_Unload"
  startTime: "2025-06-27 00:00:00"
  endTime: "2025-12-12 00:00:00"
}
rows {
  id: 40
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCInverted"
  startTime: "2025-06-06 00:00:00"
  endTime: "2025-07-04 00:00:00"
}
rows {
  id: 41
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCInverted_Unload"
  startTime: "2025-07-04 00:00:00"
  endTime: "2025-12-12 00:00:00"
}
rows {
  id: 42
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_Unload"
  startTime: "2025-01-01 00:00:00"
  endTime: "2025-06-06 00:00:00"
}
rows {
  id: 43
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob"
  startTime: "2025-06-06 00:00:00"
  endTime: "2025-08-29 00:00:00"
}
rows {
  id: 44
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_Unload"
  startTime: "2025-08-29 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 45
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_B_Unload"
  startTime: "2025-01-01 00:00:00"
  endTime: "2025-06-06 00:00:00"
}
rows {
  id: 46
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_B"
  startTime: "2025-06-06 00:00:00"
  endTime: "2025-08-29 00:00:00"
}
rows {
  id: 47
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_B_Unload"
  startTime: "2025-08-29 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 48
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_Chiikawa_Unload"
  startTime: "2025-01-01 00:00:00"
  endTime: "2025-06-06 00:00:00"
}
rows {
  id: 49
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_Chiikawa"
  startTime: "2025-06-06 00:00:00"
  endTime: "2025-08-29 00:00:00"
}
rows {
  id: 50
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_Chiikawa_Unload"
  startTime: "2025-08-29 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 51
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_ConanB_Unload"
  startTime: "2025-01-01 00:00:00"
  endTime: "2025-06-06 00:00:00"
}
rows {
  id: 52
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_ConanB"
  startTime: "2025-06-06 00:00:00"
  endTime: "2025-08-29 00:00:00"
}
rows {
  id: 53
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_ConanB_Unload"
  startTime: "2025-08-29 00:00:00"
  endTime: "2099-12-31 00:00:00"
}
rows {
  id: 54
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_FlashMob_DouLuo"
  startTime: "2025-06-06 00:00:00"
  endTime: "2025-08-29 00:00:00"
}
rows {
  id: 55
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCRedFox"
  startTime: "2025-06-27 00:00:00"
  endTime: "2025-07-11 00:00:00"
}
rows {
  id: 56
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCRedFox"
  startTime: "2025-07-04 00:00:00"
  endTime: "2025-07-25 00:00:00"
}
rows {
  id: 57
  NeedReloadDS: true
  LowestVersion: "0.0.0.1"
  DynamicLevel: "Com_Dynamic_UGCRedFox"
  startTime: "2025-07-25 00:00:00"
  endTime: "2025-12-11 00:00:00"
}
