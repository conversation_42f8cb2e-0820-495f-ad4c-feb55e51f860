com.tencent.wea.xlsRes.table_RaffleRewardCfgData
excel/xls/C_抽奖奖池_hedy.xlsx sheet:奖励
rows {
  rewardId: 30000001
  poolId: 30000002
  name: "吾皇猫"
  itemId: 403790
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000002
  poolId: 30000002
  name: "【紫背饰】吾皇福袋"
  itemId: 620498
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000003
  poolId: 30000002
  name: "【紫单人动作】"
  itemId: 720791
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000004
  poolId: 30000002
  name: "吾皇表情包-卖萌"
  itemId: 711097
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000005
  poolId: 30000002
  name: "吾皇称号-为何不拜"
  itemId: 850495
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000006
  poolId: 30000002
  name: "星愿币"
  itemId: 2
  itemNum: 4
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000007
  poolId: 30000002
  name: "吾皇头像"
  itemId: 860107
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000008
  poolId: 30000002
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000009
  poolId: 30000002
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000010
  poolId: 30000003
  name: "巴扎黑"
  itemId: 403800
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000011
  poolId: 30000003
  name: "【紫背饰】巴扎黑福袋"
  itemId: 620499
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000012
  poolId: 30000003
  name: "【紫双人动作】"
  itemId: 721030
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000013
  poolId: 30000003
  name: "巴扎黑表情包-wink"
  itemId: 711098
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000014
  poolId: 30000003
  name: "巴扎黑称号-无人问斤"
  itemId: 850494
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000015
  poolId: 30000003
  name: "星愿币"
  itemId: 2
  itemNum: 4
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000016
  poolId: 30000003
  name: "巴扎黑头像"
  itemId: 860108
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000017
  poolId: 30000003
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000018
  poolId: 30000003
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000019
  poolId: 30000005
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 1
}
rows {
  rewardId: 30000020
  poolId: 30000005
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 1
}
rows {
  rewardId: 30000021
  poolId: 30000005
  name: "服饰染色剂"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 30000098
  poolId: 30000005
  name: "配饰调色盘"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 30000022
  poolId: 30000004
  name: "普通组-积分*10"
  groupId: 1
  weight: 20
  lvCost: -10
  earningPoints: 10
}
rows {
  rewardId: 30000023
  poolId: 30000004
  name: "普通组-积分*12"
  groupId: 1
  weight: 20
  lvCost: -12
  earningPoints: 12
}
rows {
  rewardId: 30000024
  poolId: 30000004
  name: "普通组-积分*14"
  groupId: 1
  weight: 20
  lvCost: -14
  earningPoints: 14
}
rows {
  rewardId: 30000025
  poolId: 30000004
  name: "普通组-积分*16"
  groupId: 1
  weight: 20
  lvCost: -16
  earningPoints: 16
}
rows {
  rewardId: 30000027
  poolId: 30000004
  name: "普通组-积分*20"
  groupId: 1
  weight: 20
  lvCost: -20
  earningPoints: 20
}
rows {
  rewardId: 30000028
  poolId: 30000004
  name: "暴击组-积分*30"
  groupId: 2
  weight: 20
  lvCost: -30
  earningPoints: 30
}
rows {
  rewardId: 30000029
  poolId: 30000004
  name: "暴击组-积分*35"
  groupId: 2
  weight: 20
  lvCost: -35
  earningPoints: 35
}
rows {
  rewardId: 30000030
  poolId: 30000004
  name: "暴击组-积分*40"
  groupId: 2
  weight: 20
  lvCost: -40
  earningPoints: 40
}
rows {
  rewardId: 30000031
  poolId: 30000004
  name: "暴击组-积分*45"
  groupId: 2
  weight: 20
  lvCost: -45
  earningPoints: 45
}
rows {
  rewardId: 30000032
  poolId: 30000004
  name: "暴击组-积分*50"
  groupId: 2
  weight: 20
  lvCost: -50
  earningPoints: 50
}
rows {
  rewardId: 30000033
  poolId: 30000004
  name: "超级暴击"
  groupId: 3
  weight: 100
  isGrand: true
  lvCost: -1000
  earningPoints: 1000
}
rows {
  rewardId: 30000034
  poolId: 300000050
  name: "HelloKitty"
  itemId: 402420
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000035
  poolId: 300000050
  name: "红蝶结包包"
  itemId: 620376
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 30000036
  poolId: 300000050
  name: "萌萌护眼"
  itemId: 610180
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 30000037
  poolId: 300000050
  name: "粉粉仙女棒"
  itemId: 640022
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000038
  poolId: 300000051
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000039
  poolId: 300000051
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000040
  poolId: 300000051
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000041
  poolId: 300000051
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000042
  poolId: 300000051
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000043
  poolId: 300000051
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000044
  poolId: 300000051
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000045
  poolId: 300000052
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000046
  poolId: 300000052
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000047
  poolId: 300000052
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000048
  poolId: 300000052
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000049
  poolId: 300000052
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000050
  poolId: 300000052
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000051
  poolId: 300000052
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000052
  poolId: 300000053
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000053
  poolId: 300000053
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000054
  poolId: 300000053
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000055
  poolId: 300000053
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000056
  poolId: 300000053
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000057
  poolId: 300000053
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000058
  poolId: 300000053
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000059
  poolId: 300000054
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000060
  poolId: 300000054
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000061
  poolId: 300000054
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000062
  poolId: 300000054
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000063
  poolId: 300000054
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000064
  poolId: 300000054
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000065
  poolId: 300000054
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000066
  poolId: 300000060
  name: "酷洛米"
  itemId: 402430
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000067
  poolId: 300000060
  name: "甜酷包包"
  itemId: 620372
  itemNum: 1
  groupId: 2
  weight: 20
  limit: 1
}
rows {
  rewardId: 30000068
  poolId: 300000060
  name: "幻彩魅梦"
  itemId: 630244
  itemNum: 1
  groupId: 2
  weight: 50
  limit: 1
}
rows {
  rewardId: 30000069
  poolId: 300000060
  name: "幻想星愿"
  itemId: 640017
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000070
  poolId: 300000061
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000071
  poolId: 300000061
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000072
  poolId: 300000061
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000073
  poolId: 300000061
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000074
  poolId: 300000061
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000075
  poolId: 300000061
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000076
  poolId: 300000061
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000077
  poolId: 300000062
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000078
  poolId: 300000062
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000079
  poolId: 300000062
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000080
  poolId: 300000062
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000081
  poolId: 300000062
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000082
  poolId: 300000062
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000083
  poolId: 300000062
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000084
  poolId: 300000063
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000085
  poolId: 300000063
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000086
  poolId: 300000063
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000087
  poolId: 300000063
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000088
  poolId: 300000063
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000089
  poolId: 300000063
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000090
  poolId: 300000063
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000091
  poolId: 300000064
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000092
  poolId: 300000064
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000093
  poolId: 300000064
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000094
  poolId: 300000064
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000095
  poolId: 300000064
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000096
  poolId: 300000064
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000097
  poolId: 300000064
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000099
  poolId: 300000070
  name: "柯南"
  itemId: 404350
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000100
  poolId: 300000070
  name: "太阳能滑板"
  itemId: 620646
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000101
  poolId: 300000070
  name: "强化足球"
  itemId: 640098
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000102
  poolId: 300000070
  name: "柯南的指证"
  itemId: 711281
  itemNum: 1
  groupId: 2
  weight: 80
  limit: 1
}
rows {
  rewardId: 30000103
  poolId: 300000071
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000104
  poolId: 300000071
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000105
  poolId: 300000071
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000106
  poolId: 300000071
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000107
  poolId: 300000071
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000108
  poolId: 300000071
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000109
  poolId: 300000071
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000110
  poolId: 300000072
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000111
  poolId: 300000072
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000112
  poolId: 300000072
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000113
  poolId: 300000072
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000114
  poolId: 300000072
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000115
  poolId: 300000072
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000116
  poolId: 300000072
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000117
  poolId: 300000073
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000118
  poolId: 300000073
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000119
  poolId: 300000073
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000120
  poolId: 300000073
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000121
  poolId: 300000073
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000122
  poolId: 300000073
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000123
  poolId: 300000073
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000124
  poolId: 300000074
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000125
  poolId: 300000074
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000126
  poolId: 300000074
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000127
  poolId: 300000074
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000128
  poolId: 300000074
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000129
  poolId: 300000074
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000130
  poolId: 300000074
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000131
  poolId: 300000080
  name: "毛利兰"
  itemId: 404360
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000132
  poolId: 300000080
  name: "空手道包"
  itemId: 640100
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000133
  poolId: 300000080
  name: "小兰应援背挂"
  itemId: 620647
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000134
  poolId: 300000080
  name: "摩拳擦掌的小兰"
  itemId: 711276
  itemNum: 1
  groupId: 2
  weight: 80
  limit: 1
}
rows {
  rewardId: 30000135
  poolId: 300000081
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000136
  poolId: 300000081
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000137
  poolId: 300000081
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000138
  poolId: 300000081
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000139
  poolId: 300000081
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000140
  poolId: 300000081
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000141
  poolId: 300000081
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000142
  poolId: 300000082
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000143
  poolId: 300000082
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000144
  poolId: 300000082
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000145
  poolId: 300000082
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000146
  poolId: 300000082
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000147
  poolId: 300000082
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000148
  poolId: 300000082
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000149
  poolId: 300000083
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000150
  poolId: 300000083
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000151
  poolId: 300000083
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000152
  poolId: 300000083
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000153
  poolId: 300000083
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000154
  poolId: 300000083
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000155
  poolId: 300000083
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000156
  poolId: 300000084
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000157
  poolId: 300000084
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000158
  poolId: 300000084
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000159
  poolId: 300000084
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000160
  poolId: 300000084
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000161
  poolId: 300000084
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000162
  poolId: 300000084
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000163
  poolId: 300000090
  name: "灰原哀"
  itemId: 404370
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000164
  poolId: 300000090
  name: "APTX-4869"
  itemId: 620649
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000165
  poolId: 300000090
  name: "灰原哀的电脑"
  itemId: 640099
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000166
  poolId: 300000090
  name: "犯困的灰原"
  itemId: 711277
  itemNum: 1
  groupId: 2
  weight: 80
  limit: 1
}
rows {
  rewardId: 30000167
  poolId: 300000091
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000168
  poolId: 300000091
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000169
  poolId: 300000091
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000170
  poolId: 300000091
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000171
  poolId: 300000091
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000172
  poolId: 300000091
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000173
  poolId: 300000091
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000174
  poolId: 300000092
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000175
  poolId: 300000092
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000176
  poolId: 300000092
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000177
  poolId: 300000092
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000178
  poolId: 300000092
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000179
  poolId: 300000092
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000180
  poolId: 300000092
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000181
  poolId: 300000093
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000182
  poolId: 300000093
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000183
  poolId: 300000093
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000184
  poolId: 300000093
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000185
  poolId: 300000093
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000186
  poolId: 300000093
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000187
  poolId: 300000093
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000188
  poolId: 300000094
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000189
  poolId: 300000094
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000190
  poolId: 300000094
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000191
  poolId: 300000094
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000192
  poolId: 300000094
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000193
  poolId: 300000094
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000194
  poolId: 300000094
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000195
  poolId: 300000095
  name: "小波"
  itemId: 403970
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000196
  poolId: 300000095
  name: "拉拉"
  itemId: 403960
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000197
  poolId: 300000095
  name: "迪西"
  itemId: 403950
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000198
  poolId: 300000095
  name: "丁丁"
  itemId: 403940
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000199
  poolId: 300000095
  name: "软绵绵旋风"
  itemId: 620622
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000200
  poolId: 300000095
  name: "甜美爆弹"
  itemId: 620621
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000201
  poolId: 300000095
  name: "诺诺背包"
  itemId: 620623
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000202
  poolId: 300000095
  name: "宝宝眼镜"
  itemId: 610281
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000203
  poolId: 300000095
  name: "表情自选礼包"
  itemId: 330098
  itemNum: 1
  groupId: 3
  weight: 16
  limit: 6
  ignoreRefresh: true
}
rows {
  rewardId: 30000204
  poolId: 300000095
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 38
  limit: 27
  ignoreRefresh: true
}
rows {
  rewardId: 30000205
  poolId: 300000095
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 38
  limit: 27
  ignoreRefresh: true
}
rows {
  rewardId: 30000206
  poolId: 300000095
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 8
  limit: 12
  ignoreRefresh: true
}
rows {
  rewardId: 30000207
  poolId: 300000100
  name: "大耳狗"
  itemId: 410120
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000208
  poolId: 300000100
  name: "大耳狗背包"
  itemId: 620732
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000209
  poolId: 300000100
  name: "大耳狗眼罩"
  itemId: 610314
  itemNum: 1
  groupId: 2
  weight: 80
  limit: 1
}
rows {
  rewardId: 30000210
  poolId: 300000100
  name: "大耳狗棒棒糖"
  itemId: 640118
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000211
  poolId: 300000101
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000212
  poolId: 300000101
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000213
  poolId: 300000101
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000214
  poolId: 300000101
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000215
  poolId: 300000101
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000216
  poolId: 300000101
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000217
  poolId: 300000101
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000218
  poolId: 300000102
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000219
  poolId: 300000102
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000220
  poolId: 300000102
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000221
  poolId: 300000102
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000222
  poolId: 300000102
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000223
  poolId: 300000102
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000224
  poolId: 300000102
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000225
  poolId: 300000103
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000226
  poolId: 300000103
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000227
  poolId: 300000103
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000228
  poolId: 300000103
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000229
  poolId: 300000103
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000230
  poolId: 300000103
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000231
  poolId: 300000103
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000232
  poolId: 300000104
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000233
  poolId: 300000104
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000234
  poolId: 300000104
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000235
  poolId: 300000104
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000236
  poolId: 300000104
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000237
  poolId: 300000104
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000238
  poolId: 300000104
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000239
  poolId: 300000110
  name: "美乐蒂"
  itemId: 410130
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000240
  poolId: 300000110
  name: "美乐蒂小包"
  itemId: 620733
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000241
  poolId: 300000110
  name: "美乐蒂绵绵冰"
  itemId: 630487
  itemNum: 1
  groupId: 2
  weight: 80
  limit: 1
}
rows {
  rewardId: 30000242
  poolId: 300000110
  name: "美乐蒂气球"
  itemId: 640119
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000243
  poolId: 300000111
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000244
  poolId: 300000111
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000245
  poolId: 300000111
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000246
  poolId: 300000111
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000247
  poolId: 300000111
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000248
  poolId: 300000111
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000249
  poolId: 300000111
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000250
  poolId: 300000112
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000251
  poolId: 300000112
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000252
  poolId: 300000112
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000253
  poolId: 300000112
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000254
  poolId: 300000112
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000255
  poolId: 300000112
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000256
  poolId: 300000112
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000257
  poolId: 300000113
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000258
  poolId: 300000113
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000259
  poolId: 300000113
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000260
  poolId: 300000113
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000261
  poolId: 300000113
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000262
  poolId: 300000113
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000263
  poolId: 300000113
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000264
  poolId: 300000114
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000265
  poolId: 300000114
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000266
  poolId: 300000114
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000267
  poolId: 300000114
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000268
  poolId: 300000114
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000269
  poolId: 300000114
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000270
  poolId: 300000114
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000271
  poolId: 300000120
  name: "HelloKitty"
  itemId: 402420
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000272
  poolId: 300000120
  name: "红蝶结包包"
  itemId: 620376
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000273
  poolId: 300000120
  name: "萌萌护眼"
  itemId: 610180
  itemNum: 1
  groupId: 2
  weight: 80
  limit: 1
}
rows {
  rewardId: 30000274
  poolId: 300000120
  name: "粉粉仙女棒"
  itemId: 640022
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000275
  poolId: 300000121
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000276
  poolId: 300000121
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000277
  poolId: 300000121
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000278
  poolId: 300000121
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000279
  poolId: 300000121
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000280
  poolId: 300000121
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000281
  poolId: 300000121
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000282
  poolId: 300000122
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000283
  poolId: 300000122
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000284
  poolId: 300000122
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000285
  poolId: 300000122
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000286
  poolId: 300000122
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000287
  poolId: 300000122
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000288
  poolId: 300000122
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000289
  poolId: 300000123
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000290
  poolId: 300000123
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000291
  poolId: 300000123
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000292
  poolId: 300000123
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000293
  poolId: 300000123
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000294
  poolId: 300000123
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000295
  poolId: 300000123
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000296
  poolId: 300000124
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000297
  poolId: 300000124
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000298
  poolId: 300000124
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000299
  poolId: 300000124
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000300
  poolId: 300000124
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000301
  poolId: 300000124
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000302
  poolId: 300000124
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000303
  poolId: 300000130
  name: "酷洛米"
  itemId: 402430
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000304
  poolId: 300000130
  name: "甜酷包包"
  itemId: 620372
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000305
  poolId: 300000130
  name: "幻彩魅梦"
  itemId: 630244
  itemNum: 1
  groupId: 2
  weight: 80
  limit: 1
}
rows {
  rewardId: 30000306
  poolId: 300000130
  name: "幻想星愿"
  itemId: 640017
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000307
  poolId: 300000131
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000308
  poolId: 300000131
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000309
  poolId: 300000131
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000310
  poolId: 300000131
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000311
  poolId: 300000131
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000312
  poolId: 300000131
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000313
  poolId: 300000131
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000314
  poolId: 300000132
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000315
  poolId: 300000132
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000316
  poolId: 300000132
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000317
  poolId: 300000132
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000318
  poolId: 300000132
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000319
  poolId: 300000132
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000320
  poolId: 300000132
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000321
  poolId: 300000133
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000322
  poolId: 300000133
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000323
  poolId: 300000133
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000324
  poolId: 300000133
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000325
  poolId: 300000133
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000326
  poolId: 300000133
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000327
  poolId: 300000133
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000328
  poolId: 300000134
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000329
  poolId: 300000134
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000330
  poolId: 300000134
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000331
  poolId: 300000134
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000332
  poolId: 300000134
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000333
  poolId: 300000134
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000334
  poolId: 300000134
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 520801
  poolId: 52080
  name: "百合信使"
  itemId: 410600
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 520802
  poolId: 52080
  name: "星愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 520803
  poolId: 52080
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 520804
  poolId: 52080
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 520805
  poolId: 52080
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 520806
  poolId: 52080
  name: "心碎之声"
  itemId: 630388
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 1
}
rows {
  rewardId: 520807
  poolId: 52080
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 520808
  poolId: 52080
  name: "万能棉花*30"
  itemId: 6
  itemNum: 30
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 520809
  poolId: 52080
  name: "服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 520810
  poolId: 52080
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 10
  weight: 1
  limit: 1
}
rows {
  rewardId: 520811
  poolId: 52080
  name: "配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 11
  weight: 1
  limit: 1
}
rows {
  rewardId: 520812
  poolId: 52080
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 12
  weight: 1
  limit: 1
}
rows {
  rewardId: 520813
  poolId: 52081
  name: "空道具"
  groupId: 1
  weight: 1
  limit: 12
}
rows {
  rewardId: 30000335
  poolId: 300000135
  name: "小鸡小甜豆"
  itemId: 410670
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000336
  poolId: 300000135
  name: "甜酷包包"
  itemId: 620372
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000337
  poolId: 300000135
  name: "幻彩魅梦"
  itemId: 630244
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000338
  poolId: 300000135
  name: "幻想星愿"
  itemId: 640017
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000339
  poolId: 300000136
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000340
  poolId: 300000136
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000341
  poolId: 300000136
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000342
  poolId: 300000136
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000343
  poolId: 300000136
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000344
  poolId: 300000136
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000345
  poolId: 300000136
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000346
  poolId: 300000137
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000347
  poolId: 300000137
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000348
  poolId: 300000137
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000349
  poolId: 300000137
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000350
  poolId: 300000137
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000351
  poolId: 300000137
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000352
  poolId: 300000137
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000353
  poolId: 300000138
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000354
  poolId: 300000138
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000355
  poolId: 300000138
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000356
  poolId: 300000138
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000357
  poolId: 300000138
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000358
  poolId: 300000138
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000359
  poolId: 300000138
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000360
  poolId: 300000139
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000361
  poolId: 300000139
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000362
  poolId: 300000139
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000363
  poolId: 300000139
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000364
  poolId: 300000139
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000365
  poolId: 300000139
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000366
  poolId: 300000139
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000367
  poolId: 300000140
  name: "菠萝小甜豆"
  itemId: 410780
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000368
  poolId: 300000140
  name: "红蝶结包包"
  itemId: 620376
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000369
  poolId: 300000140
  name: "萌萌护眼"
  itemId: 610180
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000370
  poolId: 300000140
  name: "粉粉仙女棒"
  itemId: 640022
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000371
  poolId: 300000141
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000372
  poolId: 300000141
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000373
  poolId: 300000141
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000374
  poolId: 300000141
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000375
  poolId: 300000141
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000376
  poolId: 300000141
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000377
  poolId: 300000141
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000378
  poolId: 300000142
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000379
  poolId: 300000142
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000380
  poolId: 300000142
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000381
  poolId: 300000142
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000382
  poolId: 300000142
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000383
  poolId: 300000142
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000384
  poolId: 300000142
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000385
  poolId: 300000143
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000386
  poolId: 300000143
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000387
  poolId: 300000143
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000388
  poolId: 300000143
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000389
  poolId: 300000143
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000390
  poolId: 300000143
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000391
  poolId: 300000143
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000392
  poolId: 300000144
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000393
  poolId: 300000144
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000394
  poolId: 300000144
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000395
  poolId: 300000144
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000396
  poolId: 300000144
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000397
  poolId: 300000144
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000398
  poolId: 300000144
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000399
  poolId: 300000145
  name: "绵羊宝宝"
  itemId: 403630
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000400
  poolId: 300000145
  name: "小绵羊"
  itemId: 630335
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000401
  poolId: 300000145
  name: "羞羞"
  itemId: 711135
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000402
  poolId: 300000145
  name: "绒花"
  itemId: 610240
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000403
  poolId: 300000146
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000404
  poolId: 300000146
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000405
  poolId: 300000146
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000406
  poolId: 300000146
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000407
  poolId: 300000146
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000408
  poolId: 300000146
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000409
  poolId: 300000146
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000410
  poolId: 300000147
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000411
  poolId: 300000147
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000412
  poolId: 300000147
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000413
  poolId: 300000147
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000414
  poolId: 300000147
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000415
  poolId: 300000147
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000416
  poolId: 300000147
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000417
  poolId: 300000148
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000418
  poolId: 300000148
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000419
  poolId: 300000148
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000420
  poolId: 300000148
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000421
  poolId: 300000148
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000422
  poolId: 300000148
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000423
  poolId: 300000148
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000424
  poolId: 300000149
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000425
  poolId: 300000149
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000426
  poolId: 300000149
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000427
  poolId: 300000149
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000428
  poolId: 300000149
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000429
  poolId: 300000149
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000430
  poolId: 300000149
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000431
  poolId: 300000150
  name: "企鹅宝宝"
  itemId: 403620
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000432
  poolId: 300000150
  name: "草莓便当"
  itemId: 640056
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000433
  poolId: 300000150
  name: "我要想想"
  itemId: 711136
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000434
  poolId: 300000150
  name: "游泳圈"
  itemId: 620489
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000435
  poolId: 300000151
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000436
  poolId: 300000151
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000437
  poolId: 300000151
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000438
  poolId: 300000151
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000439
  poolId: 300000151
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000440
  poolId: 300000151
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000441
  poolId: 300000151
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000442
  poolId: 300000152
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000443
  poolId: 300000152
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000444
  poolId: 300000152
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000445
  poolId: 300000152
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000446
  poolId: 300000152
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000447
  poolId: 300000152
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000448
  poolId: 300000152
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000449
  poolId: 300000153
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000450
  poolId: 300000153
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000451
  poolId: 300000153
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000452
  poolId: 300000153
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000453
  poolId: 300000153
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000454
  poolId: 300000153
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000455
  poolId: 300000153
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000456
  poolId: 300000154
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000457
  poolId: 300000154
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000458
  poolId: 300000154
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000459
  poolId: 300000154
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000460
  poolId: 300000154
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000461
  poolId: 300000154
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000462
  poolId: 300000154
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000463
  poolId: 300000155
  name: "柠檬小甜豆"
  itemId: 401950
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000464
  poolId: 300000155
  name: "柠檬眼镜"
  itemId: 610137
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000465
  poolId: 300000155
  name: "我不酸的哦"
  itemId: 710208
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000466
  poolId: 300000155
  name: "萌檬包包"
  itemId: 620304
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000467
  poolId: 300000156
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000468
  poolId: 300000156
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000469
  poolId: 300000156
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000470
  poolId: 300000156
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000471
  poolId: 300000156
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000472
  poolId: 300000156
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000473
  poolId: 300000156
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000474
  poolId: 300000157
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000475
  poolId: 300000157
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000476
  poolId: 300000157
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000477
  poolId: 300000157
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000478
  poolId: 300000157
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000479
  poolId: 300000157
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000480
  poolId: 300000157
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000481
  poolId: 300000158
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000482
  poolId: 300000158
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000483
  poolId: 300000158
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000484
  poolId: 300000158
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000485
  poolId: 300000158
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000486
  poolId: 300000158
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000487
  poolId: 300000158
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000488
  poolId: 300000159
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000489
  poolId: 300000159
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000490
  poolId: 300000159
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000491
  poolId: 300000159
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000492
  poolId: 300000159
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000493
  poolId: 300000159
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000494
  poolId: 300000159
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000495
  poolId: 300000160
  name: "鸭鸭小甜豆"
  itemId: 401960
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000496
  poolId: 300000160
  name: "咕咕镜"
  itemId: 610138
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000497
  poolId: 300000160
  name: "悲伤那么大"
  itemId: 710209
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000498
  poolId: 300000160
  name: "透明伞"
  itemId: 620305
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000499
  poolId: 300000161
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000500
  poolId: 300000161
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000501
  poolId: 300000161
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000502
  poolId: 300000161
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000503
  poolId: 300000161
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000504
  poolId: 300000161
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000505
  poolId: 300000161
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000506
  poolId: 300000162
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000507
  poolId: 300000162
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000508
  poolId: 300000162
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000509
  poolId: 300000162
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000510
  poolId: 300000162
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000511
  poolId: 300000162
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000512
  poolId: 300000162
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000513
  poolId: 300000163
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000514
  poolId: 300000163
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000515
  poolId: 300000163
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000516
  poolId: 300000163
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000517
  poolId: 300000163
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000518
  poolId: 300000163
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000519
  poolId: 300000163
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000520
  poolId: 300000164
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000521
  poolId: 300000164
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000522
  poolId: 300000164
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000523
  poolId: 300000164
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000524
  poolId: 300000164
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000525
  poolId: 300000164
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000526
  poolId: 300000164
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000527
  poolId: 30000022
  name: "蜜桃猫"
  itemId: 410990
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000528
  poolId: 30000022
  name: "晚安帽"
  itemId: 630593
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000529
  poolId: 30000022
  name: "蜜桃猫摸摸"
  itemId: 711491
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000530
  poolId: 30000022
  name: "星愿币*4"
  itemId: 2
  itemNum: 4
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000531
  poolId: 30000022
  name: "服装染色剂*2"
  itemId: 200006
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000532
  poolId: 30000022
  name: "配饰染色剂*2"
  itemId: 200008
  itemNum: 2
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000533
  poolId: 30000022
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000534
  poolId: 30000022
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000535
  poolId: 30000022
  name: "心心宝瓶*1"
  itemId: 200016
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000536
  poolId: 300000165
  name: "咖啡师阿卓"
  itemId: 410790
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000537
  poolId: 300000165
  name: "樱语咖啡"
  itemId: 640156
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000538
  poolId: 300000165
  name: "悲伤那么大"
  itemId: 710209
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000539
  poolId: 300000165
  name: "透明伞"
  itemId: 620305
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000540
  poolId: 300000166
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000541
  poolId: 300000166
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000542
  poolId: 300000166
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000543
  poolId: 300000166
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000544
  poolId: 300000166
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000545
  poolId: 300000166
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000546
  poolId: 300000166
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000547
  poolId: 300000167
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000548
  poolId: 300000167
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000549
  poolId: 300000167
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000550
  poolId: 300000167
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000551
  poolId: 300000167
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000552
  poolId: 300000167
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000553
  poolId: 300000167
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000554
  poolId: 300000168
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000555
  poolId: 300000168
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000556
  poolId: 300000168
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000557
  poolId: 300000168
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000558
  poolId: 300000168
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000559
  poolId: 300000168
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000560
  poolId: 300000168
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000561
  poolId: 300000169
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000562
  poolId: 300000164
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000563
  poolId: 300000164
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000564
  poolId: 300000164
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000565
  poolId: 300000164
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000566
  poolId: 300000164
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000567
  poolId: 300000164
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000568
  poolId: 300000170
  name: "兔兔阿卓"
  itemId: 410680
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000569
  poolId: 300000170
  name: "兔宝糖葫芦"
  itemId: 640157
  itemNum: 1
  groupId: 2
  weight: 8
  limit: 1
}
rows {
  rewardId: 30000570
  poolId: 300000170
  name: "我不酸的哦"
  itemId: 710208
  itemNum: 1
  groupId: 2
  weight: 100
  limit: 1
}
rows {
  rewardId: 30000571
  poolId: 300000170
  name: "萌檬包包"
  itemId: 620304
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000572
  poolId: 300000171
  name: "幸运钥匙"
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000573
  poolId: 300000171
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000574
  poolId: 300000171
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000575
  poolId: 300000171
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000576
  poolId: 300000171
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000577
  poolId: 300000171
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000578
  poolId: 300000171
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000579
  poolId: 300000172
  name: "幸运钥匙"
  groupId: 1
  weight: 3
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000580
  poolId: 300000172
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000581
  poolId: 300000172
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000582
  poolId: 300000172
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000583
  poolId: 300000172
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000584
  poolId: 300000172
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000585
  poolId: 300000172
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000586
  poolId: 300000173
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000587
  poolId: 300000173
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000588
  poolId: 300000173
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000589
  poolId: 300000173
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000590
  poolId: 300000173
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000591
  poolId: 300000173
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000592
  poolId: 300000173
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000593
  poolId: 300000174
  name: "幸运钥匙"
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000594
  poolId: 300000174
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000595
  poolId: 300000174
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000596
  poolId: 300000174
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000597
  poolId: 300000174
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000598
  poolId: 300000174
  name: "蓝色配饰染色剂*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000599
  poolId: 300000174
  name: "3级好感礼物"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 1
}
rows {
  rewardId: 30000600
  poolId: 30000025
  name: "吾皇猫"
  itemId: 403790
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000601
  poolId: 30000025
  name: "【紫背饰】吾皇福袋"
  itemId: 620498
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000602
  poolId: 30000025
  name: "【紫单人动作】"
  itemId: 720791
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000603
  poolId: 30000025
  name: "吾皇表情包-卖萌"
  itemId: 711097
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000604
  poolId: 30000025
  name: "吾皇称号-为何不拜"
  itemId: 850495
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000605
  poolId: 30000025
  name: "星愿币"
  itemId: 2
  itemNum: 4
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000606
  poolId: 30000025
  name: "吾皇头像"
  itemId: 860107
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000607
  poolId: 30000025
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000608
  poolId: 30000025
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000609
  poolId: 30000026
  name: "巴扎黑"
  itemId: 403800
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 30000610
  poolId: 30000026
  name: "【紫背饰】巴扎黑福袋"
  itemId: 620499
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000611
  poolId: 30000026
  name: "【紫双人动作】"
  itemId: 721030
  itemNum: 1
  groupId: 2
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000612
  poolId: 30000026
  name: "巴扎黑表情包-wink"
  itemId: 711098
  itemNum: 1
  groupId: 3
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000613
  poolId: 30000026
  name: "巴扎黑称号-无人问斤"
  itemId: 850494
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000614
  poolId: 30000026
  name: "星愿币"
  itemId: 2
  itemNum: 4
  groupId: 4
  weight: 2
  limit: 1
}
rows {
  rewardId: 30000615
  poolId: 30000026
  name: "巴扎黑头像"
  itemId: 860108
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000616
  poolId: 30000026
  name: "云朵币*20"
  itemId: 6
  itemNum: 20
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000617
  poolId: 30000026
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 30000618
  poolId: 300000175
  name: "小波"
  itemId: 403970
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000619
  poolId: 300000175
  name: "拉拉"
  itemId: 403960
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000620
  poolId: 300000175
  name: "迪西"
  itemId: 403950
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000621
  poolId: 300000175
  name: "丁丁"
  itemId: 403940
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
  ignoreRefresh: true
  inGroupChooseEnhance {
    yieldIfOwned: true
  }
}
rows {
  rewardId: 30000622
  poolId: 300000175
  name: "软绵绵旋风"
  itemId: 620622
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000623
  poolId: 300000175
  name: "甜美爆弹"
  itemId: 620621
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000624
  poolId: 300000175
  name: "诺诺背包"
  itemId: 620623
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000625
  poolId: 300000175
  name: "宝宝眼镜"
  itemId: 610281
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  ignoreRefresh: true
}
rows {
  rewardId: 30000626
  poolId: 300000175
  name: "表情自选礼包"
  itemId: 330098
  itemNum: 1
  groupId: 3
  weight: 16
  limit: 6
  ignoreRefresh: true
}
rows {
  rewardId: 30000627
  poolId: 300000175
  name: "蓝色服装染色剂*1"
  itemId: 200006
  itemNum: 1
  groupId: 3
  weight: 38
  limit: 27
  ignoreRefresh: true
}
rows {
  rewardId: 30000628
  poolId: 300000175
  name: "万能棉花*20"
  itemId: 6
  itemNum: 20
  groupId: 3
  weight: 38
  limit: 27
  ignoreRefresh: true
}
rows {
  rewardId: 30000629
  poolId: 300000175
  name: "星愿币*2"
  itemId: 2
  itemNum: 2
  groupId: 3
  weight: 8
  limit: 12
  ignoreRefresh: true
}
