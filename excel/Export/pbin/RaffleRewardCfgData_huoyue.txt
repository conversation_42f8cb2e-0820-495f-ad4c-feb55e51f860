com.tencent.wea.xlsRes.table_RaffleRewardCfgData
excel/xls/C_抽奖奖池_活跃.xlsx sheet:奖励
rows {
  rewardId: 1000375
  poolId: 10006
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000376
  poolId: 10006
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1000377
  poolId: 10006
  name: "神奇水彩*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000378
  poolId: 10006
  name: "神奇水彩*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000379
  poolId: 10006
  name: "神奇水彩*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1000380
  poolId: 10006
  name: "精选好礼"
  itemId: 630310
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000381
  poolId: 10006
  name: "清凉蓝叉"
  itemId: 620458
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000382
  poolId: 10006
  name: "灿烂黄叉"
  itemId: 620459
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000383
  poolId: 10006
  name: "喵喵快乐罐"
  itemId: 630259
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000384
  poolId: 10006
  name: "科技起源"
  itemId: 610188
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000385
  poolId: 10006
  name: "赛博夜影"
  itemId: 610189
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000386
  poolId: 10006
  name: "微笑娃娃"
  itemId: 620329
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000387
  poolId: 10006
  name: "肉棕棕"
  itemId: 630216
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000388
  poolId: 10006
  name: "粉嘟嘟"
  itemId: 630217
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000389
  poolId: 10006
  name: "出操口令"
  itemId: 720745
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000390
  poolId: 10006
  name: "向右看齐"
  itemId: 720746
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000391
  poolId: 10006
  name: "八段锦柒式"
  itemId: 720096
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000392
  poolId: 10006
  name: "八段锦捌式"
  itemId: 720097
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000393
  poolId: 10006
  name: "被逼墙角"
  itemId: 711101
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000394
  poolId: 10006
  name: "扎心了"
  itemId: 711105
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000395
  poolId: 10006
  name: "不要过来！"
  itemId: 711021
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000396
  poolId: 10006
  name: "不好意思鸭"
  itemId: 711027
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000397
  poolId: 10006
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000398
  poolId: 10006
  name: "专业态度上装"
  itemId: 510216
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000399
  poolId: 10006
  name: "专业态度下装"
  itemId: 520153
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000400
  poolId: 10006
  name: "专业态度手套"
  itemId: 530129
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000401
  poolId: 10006
  name: "职场萌星上装"
  itemId: 510233
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000402
  poolId: 10006
  name: "职场萌星下装"
  itemId: 520162
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000403
  poolId: 10006
  name: "职场萌星手套"
  itemId: 530138
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000404
  poolId: 10006
  name: "赛场飞驰上装"
  itemId: 510219
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000405
  poolId: 10006
  name: "赛场飞驰下装"
  itemId: 520155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000406
  poolId: 10006
  name: "赛场飞驰手套"
  itemId: 530131
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000407
  poolId: 10006
  name: "浅蓝节拍上装"
  itemId: 510220
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000408
  poolId: 10006
  name: "浅蓝节拍下装"
  itemId: 520156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000409
  poolId: 10006
  name: "浅蓝节拍手套"
  itemId: 530132
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000410
  poolId: 10006
  name: "春日运动上装"
  itemId: 510221
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000411
  poolId: 10006
  name: "春日运动下装"
  itemId: 520157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000412
  poolId: 10006
  name: "春日运动手套"
  itemId: 530133
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000413
  poolId: 10006
  name: "夏意清新上装"
  itemId: 510182
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000414
  poolId: 10006
  name: "夏意清新下装"
  itemId: 520125
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000415
  poolId: 10006
  name: "夏意清新手套"
  itemId: 530102
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000416
  poolId: 10006
  name: "盛夏果实上装"
  itemId: 510183
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000417
  poolId: 10006
  name: "盛夏果实下装"
  itemId: 520126
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000418
  poolId: 10006
  name: "盛夏果实手套"
  itemId: 530103
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000419
  poolId: 10006
  name: "仲夏芳菲上装"
  itemId: 510200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000420
  poolId: 10006
  name: "仲夏芳菲下装"
  itemId: 520139
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000421
  poolId: 10006
  name: "仲夏芳菲手套"
  itemId: 530116
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000422
  poolId: 10006
  name: "童梦奇缘上装"
  itemId: 510201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000423
  poolId: 10006
  name: "童梦奇缘下装"
  itemId: 520140
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000424
  poolId: 10006
  name: "童梦奇缘手套"
  itemId: 530117
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000425
  poolId: 10006
  name: "青柠之恋上装"
  itemId: 510202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000426
  poolId: 10006
  name: "青柠之恋下装"
  itemId: 520141
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000427
  poolId: 10006
  name: "青柠之恋手套"
  itemId: 530118
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000428
  poolId: 10006
  name: "灰调潮流上装"
  itemId: 510159
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000429
  poolId: 10006
  name: "灰调潮流下装"
  itemId: 520110
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000430
  poolId: 10006
  name: "灰调潮流手套"
  itemId: 530087
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000431
  poolId: 10006
  name: "灰粉嘻哈上装"
  itemId: 510160
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000432
  poolId: 10006
  name: "灰粉嘻哈下装"
  itemId: 520111
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000433
  poolId: 10006
  name: "灰粉嘻哈手套"
  itemId: 530088
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000434
  poolId: 10006
  name: "多元时尚上装"
  itemId: 510161
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000435
  poolId: 10006
  name: "多元时尚下装"
  itemId: 520112
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000436
  poolId: 10006
  name: "多元时尚手套"
  itemId: 530089
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000437
  poolId: 10006
  name: "古韵长衣上装"
  itemId: 510153
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000438
  poolId: 10006
  name: "古韵长衣下装"
  itemId: 520105
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000439
  poolId: 10006
  name: "古韵长衣手套"
  itemId: 530082
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000440
  poolId: 10006
  name: "气息归元上装"
  itemId: 510154
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000441
  poolId: 10006
  name: "气息归元下装"
  itemId: 520106
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000442
  poolId: 10006
  name: "气息归元手套"
  itemId: 530083
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000443
  poolId: 10006
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000444
  poolId: 10006
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000445
  poolId: 10006
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000446
  poolId: 20000001
  name: "庆典小子"
  itemId: 402440
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  exceedReplacementItems {
    itemId: 3407
    itemNum: 500
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000447
  poolId: 20000001
  name: "舞狮少女 乐祺"
  itemId: 400600
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  exceedReplacementItems {
    itemId: 3407
    itemNum: 500
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000448
  poolId: 20000001
  name: "小龙人 辰儿"
  itemId: 401080
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  exceedReplacementItems {
    itemId: 3407
    itemNum: 500
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000449
  poolId: 20000001
  name: "兔星星"
  itemId: 401840
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  exceedReplacementItems {
    itemId: 3407
    itemNum: 500
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000450
  poolId: 20000001
  name: "灵魂歌姬 宝拉"
  itemId: 400350
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  exceedReplacementItems {
    itemId: 3407
    itemNum: 500
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000451
  poolId: 20000001
  name: "醒狮背包"
  itemId: 620119
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  exceedReplacementItems {
    itemId: 3407
    itemNum: 300
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000452
  poolId: 20000001
  name: "嘟嘟奶瓶"
  itemId: 620206
  itemNum: 1
  groupId: 2
  weight: 5
  limit: 1
  exceedReplacementItems {
    itemId: 3407
    itemNum: 300
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000453
  poolId: 20000001
  name: "小雏菊眼镜"
  itemId: 610107
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000454
  poolId: 20000001
  name: "星梦一号"
  itemId: 630236
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000455
  poolId: 20000001
  name: "千千星鹤"
  itemId: 620196
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000456
  poolId: 20000001
  name: "热血电玩"
  itemId: 620024
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000457
  poolId: 20000001
  name: "糖葫芦"
  itemId: 620109
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000458
  poolId: 20000001
  name: "年年有余"
  itemId: 620108
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000459
  poolId: 20000001
  name: "幸福饺饺"
  itemId: 630088
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000460
  poolId: 20000001
  name: "满格心情"
  itemId: 630131
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000461
  poolId: 20000001
  name: "随心铲"
  itemId: 620267
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000462
  poolId: 20000001
  name: "搞怪天才"
  itemId: 630115
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000463
  poolId: 20000001
  name: "为你点赞"
  itemId: 630154
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000464
  poolId: 20000001
  name: "纯情信封"
  itemId: 630292
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000465
  poolId: 20000001
  name: "金水玩家"
  itemId: 610171
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000466
  poolId: 20000001
  name: "神秘眼镜"
  itemId: 610058
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 100
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000467
  poolId: 20000001
  name: "赐福锦鲤上装"
  itemId: 510247
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000468
  poolId: 20000001
  name: "赐福锦鲤下装"
  itemId: 520173
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000469
  poolId: 20000001
  name: "赐福锦鲤手套"
  itemId: 530149
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000470
  poolId: 20000001
  name: "爱与和平上装"
  itemId: 510059
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000471
  poolId: 20000001
  name: "爱与和平下装"
  itemId: 520043
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000472
  poolId: 20000001
  name: "爱与和平手套"
  itemId: 530022
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000473
  poolId: 20000001
  name: "星海领航员上装"
  itemId: 510115
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000474
  poolId: 20000001
  name: "星海领航员下装"
  itemId: 520077
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000475
  poolId: 20000001
  name: "星海领航员手套"
  itemId: 530055
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000476
  poolId: 20000001
  name: "牧场物语"
  itemId: 510186
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000477
  poolId: 20000001
  name: "汪汪守护"
  itemId: 510169
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000478
  poolId: 20000001
  name: "蓝玉生烟"
  itemId: 510088
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000479
  poolId: 20000001
  name: "战国策士"
  itemId: 510140
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000480
  poolId: 20000001
  name: "活力运动上装"
  itemId: 510060
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000481
  poolId: 20000001
  name: "活力运动下装"
  itemId: 520044
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000482
  poolId: 20000001
  name: "活力运动手套"
  itemId: 530023
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000483
  poolId: 20000001
  name: "粉桃朵朵上装"
  itemId: 510149
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000484
  poolId: 20000001
  name: "粉桃朵朵下装"
  itemId: 520102
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000485
  poolId: 20000001
  name: "粉桃朵朵手套"
  itemId: 530078
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000486
  poolId: 20000001
  name: "梦里寻粽上装"
  itemId: 510147
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000487
  poolId: 20000001
  name: "梦里寻粽下装"
  itemId: 520100
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000488
  poolId: 20000001
  name: "梦里寻粽手套"
  itemId: 530076
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000489
  poolId: 20000001
  name: "通行卫士上装"
  itemId: 510152
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000490
  poolId: 20000001
  name: "通行卫士下装"
  itemId: 520104
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000491
  poolId: 20000001
  name: "通行卫士手套"
  itemId: 530081
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000492
  poolId: 20000001
  name: "梦想裁决上装"
  itemId: 510204
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000493
  poolId: 20000001
  name: "梦想裁决下装"
  itemId: 520142
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000494
  poolId: 20000001
  name: "梦想裁决手套"
  itemId: 530119
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000495
  poolId: 20000001
  name: "古韵中轴上装"
  itemId: 510189
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000496
  poolId: 20000001
  name: "古韵中轴下装"
  itemId: 520128
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000497
  poolId: 20000001
  name: "古韵中轴手套"
  itemId: 530105
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000498
  poolId: 20000001
  name: "极速风潮上装"
  itemId: 510122
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000499
  poolId: 20000001
  name: "极速风潮下装"
  itemId: 520081
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000500
  poolId: 20000001
  name: "极速风潮手套"
  itemId: 530059
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000501
  poolId: 20000001
  name: "纯果乐不停上装"
  itemId: 510174
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000502
  poolId: 20000001
  name: "纯果乐不停下装"
  itemId: 520121
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000503
  poolId: 20000001
  name: "纯果乐不停手套"
  itemId: 530098
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000504
  poolId: 20000001
  name: "绿茵旋风上装"
  itemId: 510192
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000505
  poolId: 20000001
  name: "绿茵旋风下装"
  itemId: 520131
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000506
  poolId: 20000001
  name: "绿茵旋风手套"
  itemId: 530108
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000507
  poolId: 20000001
  name: "静谧狩猎上装"
  itemId: 510193
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000508
  poolId: 20000001
  name: "静谧狩猎下装"
  itemId: 520132
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000509
  poolId: 20000001
  name: "静谧狩猎手套"
  itemId: 530109
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000510
  poolId: 20000001
  name: "弹跳乒乓上装"
  itemId: 510194
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000511
  poolId: 20000001
  name: "弹跳乒乓下装"
  itemId: 520133
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000512
  poolId: 20000001
  name: "弹跳乒乓手套"
  itemId: 530110
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000513
  poolId: 20000001
  name: "无限换防上装"
  itemId: 510195
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000514
  poolId: 20000001
  name: "无限换防下装"
  itemId: 520134
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000515
  poolId: 20000001
  name: "无限换防手套"
  itemId: 530111
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000516
  poolId: 20000001
  name: "活力网坛上装"
  itemId: 510196
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000517
  poolId: 20000001
  name: "活力网坛下装"
  itemId: 520135
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000518
  poolId: 20000001
  name: "活力网坛手套"
  itemId: 530112
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000519
  poolId: 20000001
  name: "精准投手上装"
  itemId: 510197
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000520
  poolId: 20000001
  name: "精准投手下装"
  itemId: 520136
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000521
  poolId: 20000001
  name: "精准投手手套"
  itemId: 530113
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000522
  poolId: 20000001
  name: "凌空飞跃上装"
  itemId: 510198
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000523
  poolId: 20000001
  name: "凌空飞跃下装"
  itemId: 520137
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000524
  poolId: 20000001
  name: "凌空飞跃手套"
  itemId: 530114
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000525
  poolId: 20000001
  name: "晚宴绅士上装"
  itemId: 510151
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000526
  poolId: 20000001
  name: "晚宴绅士下装"
  itemId: 520103
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000527
  poolId: 20000001
  name: "晚宴绅士手套"
  itemId: 530079
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000528
  poolId: 20000001
  name: "晚宴淑女上装"
  itemId: 510150
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000529
  poolId: 20000001
  name: "晚宴淑女手套"
  itemId: 530080
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000530
  poolId: 20000001
  name: "火热前线上装"
  itemId: 510082
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000531
  poolId: 20000001
  name: "火热前线下装"
  itemId: 530040
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 3
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000532
  poolId: 20000001
  name: "超认真"
  itemId: 830019
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 2
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000533
  poolId: 20000001
  name: "略略略"
  itemId: 830066
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 2
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000534
  poolId: 20000001
  name: "惬意"
  itemId: 830082
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 2
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000535
  poolId: 20000001
  name: "让我试试"
  itemId: 830080
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 2
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000536
  poolId: 20000001
  name: "大口亲亲"
  itemId: 830034
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 2
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000537
  poolId: 20000001
  name: "矜持"
  itemId: 830035
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 2
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000538
  poolId: 20000001
  name: "喵喵绅士"
  itemId: 860095
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000539
  poolId: 20000001
  name: "咪咪甜心"
  itemId: 860096
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000540
  poolId: 20000001
  name: "兔星星"
  itemId: 860039
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000541
  poolId: 20000001
  name: "小小星头像框"
  itemId: 840004
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000542
  poolId: 20000001
  name: "逐梦探月"
  itemId: 840090
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000543
  poolId: 20000001
  name: "悠闲假期"
  itemId: 840095
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000544
  poolId: 20000001
  name: "童趣乐园"
  itemId: 840108
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000545
  poolId: 20000001
  name: "书写青春"
  itemId: 840051
  itemNum: 1
  groupId: 6
  weight: 1
  limit: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000546
  poolId: 20000001
  name: "甜蜜初心*1"
  itemId: 200014
  itemNum: 1
  groupId: 7
  weight: 10
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000547
  poolId: 20000001
  name: "心心糖果*1"
  itemId: 200015
  itemNum: 1
  groupId: 7
  weight: 5
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000548
  poolId: 20000001
  name: "心心宝瓶"
  itemId: 200016
  itemNum: 1
  groupId: 7
  weight: 3
  limit: 50
  exceedReplacementItems {
    itemId: 3407
    itemNum: 30
  }
  exceedReplacementWeights: 1
}
rows {
  rewardId: 1000549
  poolId: 20000001
  name: "星宝印章*50"
  itemId: 4
  itemNum: 50
  groupId: 8
  weight: 5
}
rows {
  rewardId: 1000550
  poolId: 20000001
  name: "星宝印章*100"
  itemId: 4
  itemNum: 100
  groupId: 8
  weight: 1
}
rows {
  rewardId: 1000551
  poolId: 20000001
  name: "能量值*15000"
  itemId: 3407
  itemNum: 15000
  groupId: 9
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000552
  poolId: 20000001
  name: "能量值*300"
  itemId: 3407
  itemNum: 300
  groupId: 10
  weight: 3
}
rows {
  rewardId: 1000553
  poolId: 20000001
  name: "能量值*200"
  itemId: 3407
  itemNum: 200
  groupId: 10
  weight: 4
}
rows {
  rewardId: 1000554
  poolId: 20000001
  name: "能量值*100"
  itemId: 3407
  itemNum: 100
  groupId: 10
  weight: 3
}
rows {
  rewardId: 1000555
  poolId: 20000001
  name: "能量值*20"
  itemId: 3407
  itemNum: 20
  groupId: 11
  weight: 7
}
rows {
  rewardId: 1000556
  poolId: 20000001
  name: "能量值*10"
  itemId: 3407
  itemNum: 10
  groupId: 11
  weight: 3
}
rows {
  rewardId: 1000900
  poolId: 20000002
  name: "蓝装-松小雪"
  itemId: 404810
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1000901
  poolId: 20000002
  name: "蓝装-松小雪（7天试用）"
  itemId: 404810
  itemNum: 1
  expireDays: 7
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000902
  poolId: 20000002
  name: "腾讯视频VIP月卡宝箱"
  itemId: 200700
  itemNum: 1
  groupId: 3
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1000903
  poolId: 20000002
  name: "头像-欢庆时刻"
  itemId: 860156
  itemNum: 1
  groupId: 4
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1000904
  poolId: 20000002
  name: "排位赛通用升星券*1"
  itemId: 200020
  itemNum: 1
  groupId: 5
  weight: 1
  limit: 2
  isGrand: true
}
rows {
  rewardId: 1000905
  poolId: 20000002
  name: "福利碎片*10"
  itemId: 3134
  itemNum: 10
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000906
  poolId: 20000002
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 5
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000907
  poolId: 20000002
  name: "小糖心*5"
  itemId: 3457
  itemNum: 5
  groupId: 6
  weight: 1
}
rows {
  rewardId: 1000908
  poolId: 20000002
  name: "小糖心*30（小暴击）"
  itemId: 3457
  itemNum: 30
  groupId: 7
  weight: 120000
}
rows {
  rewardId: 1000909
  poolId: 20000002
  name: "小糖心*100（大暴击）"
  itemId: 3457
  itemNum: 100
  groupId: 7
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000557
  poolId: 10007
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000558
  poolId: 10007
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1000559
  poolId: 10007
  name: "神奇水彩*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000560
  poolId: 10007
  name: "神奇水彩*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000561
  poolId: 10007
  name: "神奇水彩*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1000562
  poolId: 10007
  name: "野性之息"
  itemId: 610248
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000563
  poolId: 10007
  name: "百步穿杨"
  itemId: 620532
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000564
  poolId: 10007
  name: "活力满分"
  itemId: 620533
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000565
  poolId: 10007
  name: "精选好礼"
  itemId: 630310
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000566
  poolId: 10007
  name: "清凉蓝叉"
  itemId: 620458
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000567
  poolId: 10007
  name: "灿烂黄叉"
  itemId: 620459
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000568
  poolId: 10007
  name: "喵喵快乐罐"
  itemId: 630259
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000569
  poolId: 10007
  name: "科技起源"
  itemId: 610188
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000570
  poolId: 10007
  name: "赛博夜影"
  itemId: 610189
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000571
  poolId: 10007
  name: "微笑娃娃"
  itemId: 620329
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000572
  poolId: 10007
  name: "肉棕棕"
  itemId: 630216
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000573
  poolId: 10007
  name: "粉嘟嘟"
  itemId: 630217
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000574
  poolId: 10007
  name: "欢乐交叉步"
  itemId: 720784
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000575
  poolId: 10007
  name: "五星啦啦队"
  itemId: 720694
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000576
  poolId: 10007
  name: "训练有素"
  itemId: 720745
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000577
  poolId: 10007
  name: "向右看齐"
  itemId: 720746
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000578
  poolId: 10007
  name: "我会出手"
  itemId: 711019
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000579
  poolId: 10007
  name: "告辞"
  itemId: 711022
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000580
  poolId: 10007
  name: "被逼墙角"
  itemId: 711101
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000581
  poolId: 10007
  name: "扎心了"
  itemId: 711105
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000582
  poolId: 10007
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000583
  poolId: 10007
  name: "点点乐"
  itemId: 510262
  itemNum: 1
  groupId: 2
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000584
  poolId: 10007
  name: "粉焰旋风上装"
  itemId: 510254
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000585
  poolId: 10007
  name: "粉焰旋风下装"
  itemId: 520178
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000586
  poolId: 10007
  name: "粉焰旋风手套"
  itemId: 530154
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000587
  poolId: 10007
  name: "蔚蓝闪电上装"
  itemId: 510255
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000588
  poolId: 10007
  name: "蔚蓝闪电下装"
  itemId: 520179
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000589
  poolId: 10007
  name: "蔚蓝闪电手套"
  itemId: 530155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000590
  poolId: 10007
  name: "逐梦狂飙上装"
  itemId: 510256
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000591
  poolId: 10007
  name: "逐梦狂飙下装"
  itemId: 520180
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000592
  poolId: 10007
  name: "逐梦狂飙手套"
  itemId: 530156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000593
  poolId: 10007
  name: "云龙武袍上装"
  itemId: 510257
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000594
  poolId: 10007
  name: "云龙武袍下装"
  itemId: 520181
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000595
  poolId: 10007
  name: "云龙武袍手套"
  itemId: 530157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000596
  poolId: 10007
  name: "青岚短衫上装"
  itemId: 510268
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000597
  poolId: 10007
  name: "青岚短衫下装"
  itemId: 520182
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000598
  poolId: 10007
  name: "青岚短衫手套"
  itemId: 530158
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000599
  poolId: 10007
  name: "专业态度上装"
  itemId: 510216
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000600
  poolId: 10007
  name: "专业态度下装"
  itemId: 520153
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000601
  poolId: 10007
  name: "专业态度手套"
  itemId: 530129
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000602
  poolId: 10007
  name: "职场萌星上装"
  itemId: 510233
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000603
  poolId: 10007
  name: "职场萌星下装"
  itemId: 520162
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000604
  poolId: 10007
  name: "职场萌星手套"
  itemId: 530138
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000605
  poolId: 10007
  name: "赛场飞驰上装"
  itemId: 510219
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000606
  poolId: 10007
  name: "赛场飞驰下装"
  itemId: 520155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000607
  poolId: 10007
  name: "赛场飞驰手套"
  itemId: 530131
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000608
  poolId: 10007
  name: "浅蓝节拍上装"
  itemId: 510220
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000609
  poolId: 10007
  name: "浅蓝节拍下装"
  itemId: 520156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000610
  poolId: 10007
  name: "浅蓝节拍手套"
  itemId: 530132
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000611
  poolId: 10007
  name: "春日运动上装"
  itemId: 510221
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000612
  poolId: 10007
  name: "春日运动下装"
  itemId: 520157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000613
  poolId: 10007
  name: "春日运动手套"
  itemId: 530133
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000614
  poolId: 10007
  name: "夏意清新上装"
  itemId: 510182
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000615
  poolId: 10007
  name: "夏意清新下装"
  itemId: 520125
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000616
  poolId: 10007
  name: "夏意清新手套"
  itemId: 530102
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000617
  poolId: 10007
  name: "盛夏果实上装"
  itemId: 510183
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000618
  poolId: 10007
  name: "盛夏果实下装"
  itemId: 520126
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000619
  poolId: 10007
  name: "盛夏果实手套"
  itemId: 530103
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000620
  poolId: 10007
  name: "仲夏芳菲上装"
  itemId: 510200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000621
  poolId: 10007
  name: "仲夏芳菲下装"
  itemId: 520139
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000622
  poolId: 10007
  name: "仲夏芳菲手套"
  itemId: 530116
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000623
  poolId: 10007
  name: "童梦奇缘上装"
  itemId: 510201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000624
  poolId: 10007
  name: "童梦奇缘下装"
  itemId: 520140
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000625
  poolId: 10007
  name: "童梦奇缘手套"
  itemId: 530117
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000626
  poolId: 10007
  name: "青柠之恋上装"
  itemId: 510202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000627
  poolId: 10007
  name: "青柠之恋下装"
  itemId: 520141
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000628
  poolId: 10007
  name: "青柠之恋手套"
  itemId: 530118
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000629
  poolId: 10007
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 8
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000630
  poolId: 10007
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000631
  poolId: 10007
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000800
  poolId: 10008
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000801
  poolId: 10008
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1000802
  poolId: 10008
  name: "印章祈愿兑换券*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000803
  poolId: 10008
  name: "印章祈愿兑换券*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000804
  poolId: 10008
  name: "印章祈愿兑换券*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1000805
  poolId: 10008
  name: "飞翔之眼"
  itemId: 610270
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000806
  poolId: 10008
  name: "绒绒小耳"
  itemId: 630402
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000807
  poolId: 10008
  name: "蓝纹绒帽"
  itemId: 630403
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000808
  poolId: 10008
  name: "野性之息"
  itemId: 610248
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000809
  poolId: 10008
  name: "百步穿杨"
  itemId: 620532
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000810
  poolId: 10008
  name: "活力满分"
  itemId: 620533
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000811
  poolId: 10008
  name: "精选好礼"
  itemId: 630310
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000812
  poolId: 10008
  name: "清凉蓝叉"
  itemId: 620458
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000813
  poolId: 10008
  name: "灿烂黄叉"
  itemId: 620459
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000814
  poolId: 10008
  name: "喵喵快乐罐"
  itemId: 630259
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000815
  poolId: 10008
  name: "科技起源"
  itemId: 610188
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000816
  poolId: 10008
  name: "赛博夜影"
  itemId: 610189
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000817
  poolId: 10008
  name: "别过来呀"
  itemId: 720838
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000818
  poolId: 10008
  name: "恳求"
  itemId: 720801
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000819
  poolId: 10008
  name: "欢乐交叉步"
  itemId: 720784
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000820
  poolId: 10008
  name: "五星啦啦队"
  itemId: 720694
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000821
  poolId: 10008
  name: "怎么敢的"
  itemId: 711147
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000822
  poolId: 10008
  name: "叉出去"
  itemId: 711153
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000823
  poolId: 10008
  name: "我会出手"
  itemId: 711019
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000824
  poolId: 10008
  name: "告辞"
  itemId: 711022
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000825
  poolId: 10008
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000826
  poolId: 10008
  name: "点点乐"
  itemId: 510262
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000827
  poolId: 10008
  name: "桃心王子上装"
  itemId: 510280
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000828
  poolId: 10008
  name: "桃心王子下装"
  itemId: 520193
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000829
  poolId: 10008
  name: "桃心王子手套"
  itemId: 530169
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000830
  poolId: 10008
  name: "梅花公爵上装"
  itemId: 510281
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000831
  poolId: 10008
  name: "梅花公爵下装"
  itemId: 520194
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000832
  poolId: 10008
  name: "梅花公爵手套"
  itemId: 530170
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000833
  poolId: 10008
  name: "腾云一舞上装"
  itemId: 510277
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000834
  poolId: 10008
  name: "腾云一舞下装"
  itemId: 520190
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000835
  poolId: 10008
  name: "腾云一舞手套"
  itemId: 530166
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000836
  poolId: 10008
  name: "竹韵清扬上装"
  itemId: 510278
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000837
  poolId: 10008
  name: "竹韵清扬下装"
  itemId: 520191
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000838
  poolId: 10008
  name: "竹韵清扬手套"
  itemId: 530167
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000839
  poolId: 10008
  name: "墨韵乘云上装"
  itemId: 510279
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000840
  poolId: 10008
  name: "墨韵乘云下装"
  itemId: 520192
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000841
  poolId: 10008
  name: "墨韵乘云手套"
  itemId: 530168
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000842
  poolId: 10008
  name: "粉焰旋风上装"
  itemId: 510254
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000843
  poolId: 10008
  name: "粉焰旋风下装"
  itemId: 520178
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000844
  poolId: 10008
  name: "粉焰旋风手套"
  itemId: 530154
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000845
  poolId: 10008
  name: "蔚蓝闪电上装"
  itemId: 510255
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000846
  poolId: 10008
  name: "蔚蓝闪电下装"
  itemId: 520179
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000847
  poolId: 10008
  name: "蔚蓝闪电手套"
  itemId: 530155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000848
  poolId: 10008
  name: "逐梦狂飙上装"
  itemId: 510256
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000849
  poolId: 10008
  name: "逐梦狂飙下装"
  itemId: 520180
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000850
  poolId: 10008
  name: "逐梦狂飙手套"
  itemId: 530156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000851
  poolId: 10008
  name: "云龙武袍上装"
  itemId: 510257
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000852
  poolId: 10008
  name: "云龙武袍下装"
  itemId: 520181
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000853
  poolId: 10008
  name: "云龙武袍手套"
  itemId: 530157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000854
  poolId: 10008
  name: "青岚短衫上装"
  itemId: 510268
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000855
  poolId: 10008
  name: "青岚短衫下装"
  itemId: 520182
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000856
  poolId: 10008
  name: "青岚短衫手套"
  itemId: 530158
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000857
  poolId: 10008
  name: "专业态度上装"
  itemId: 510216
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000858
  poolId: 10008
  name: "专业态度下装"
  itemId: 520153
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000859
  poolId: 10008
  name: "专业态度手套"
  itemId: 530129
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000860
  poolId: 10008
  name: "职场萌星上装"
  itemId: 510233
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000861
  poolId: 10008
  name: "职场萌星下装"
  itemId: 520162
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000862
  poolId: 10008
  name: "职场萌星手套"
  itemId: 530138
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000863
  poolId: 10008
  name: "赛场飞驰上装"
  itemId: 510219
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000864
  poolId: 10008
  name: "赛场飞驰下装"
  itemId: 520155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000865
  poolId: 10008
  name: "赛场飞驰手套"
  itemId: 530131
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000866
  poolId: 10008
  name: "浅蓝节拍上装"
  itemId: 510220
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000867
  poolId: 10008
  name: "浅蓝节拍下装"
  itemId: 520156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000868
  poolId: 10008
  name: "浅蓝节拍手套"
  itemId: 530132
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000869
  poolId: 10008
  name: "春日运动上装"
  itemId: 510221
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000870
  poolId: 10008
  name: "春日运动下装"
  itemId: 520157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000871
  poolId: 10008
  name: "春日运动手套"
  itemId: 530133
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000872
  poolId: 10008
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000873
  poolId: 10008
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000874
  poolId: 10008
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000910
  poolId: 10009
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1000911
  poolId: 10009
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1000912
  poolId: 10009
  name: "印章祈愿兑换券*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1000913
  poolId: 10009
  name: "印章祈愿兑换券*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1000914
  poolId: 10009
  name: "印章祈愿兑换券*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1000915
  poolId: 10009
  name: "飞翔之眼"
  itemId: 610270
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000916
  poolId: 10009
  name: "绒绒小耳"
  itemId: 630402
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000917
  poolId: 10009
  name: "蓝纹绒帽"
  itemId: 630403
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000918
  poolId: 10009
  name: "野性之息"
  itemId: 610248
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000919
  poolId: 10009
  name: "百步穿杨"
  itemId: 620532
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000920
  poolId: 10009
  name: "活力满分"
  itemId: 620533
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000921
  poolId: 10009
  name: "精选好礼"
  itemId: 630310
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000922
  poolId: 10009
  name: "清凉蓝叉"
  itemId: 620458
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000923
  poolId: 10009
  name: "灿烂黄叉"
  itemId: 620459
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000924
  poolId: 10009
  name: "喵喵快乐罐"
  itemId: 630259
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000925
  poolId: 10009
  name: "科技起源"
  itemId: 610188
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000926
  poolId: 10009
  name: "赛博夜影"
  itemId: 610189
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000927
  poolId: 10009
  name: "别过来呀"
  itemId: 720838
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000928
  poolId: 10009
  name: "恳求"
  itemId: 720801
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000929
  poolId: 10009
  name: "欢乐交叉步"
  itemId: 720784
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000930
  poolId: 10009
  name: "五星啦啦队"
  itemId: 720694
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000931
  poolId: 10009
  name: "怎么敢的"
  itemId: 711147
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000932
  poolId: 10009
  name: "叉出去"
  itemId: 711153
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000933
  poolId: 10009
  name: "我会出手"
  itemId: 711019
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000934
  poolId: 10009
  name: "告辞"
  itemId: 711022
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1000935
  poolId: 10009
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1000936
  poolId: 10009
  name: "点点乐"
  itemId: 510262
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000937
  poolId: 10009
  name: "桃心王子上装"
  itemId: 510280
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000938
  poolId: 10009
  name: "桃心王子下装"
  itemId: 520193
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000939
  poolId: 10009
  name: "桃心王子手套"
  itemId: 530169
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000940
  poolId: 10009
  name: "梅花公爵上装"
  itemId: 510281
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000941
  poolId: 10009
  name: "梅花公爵下装"
  itemId: 520194
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000942
  poolId: 10009
  name: "梅花公爵手套"
  itemId: 530170
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000943
  poolId: 10009
  name: "腾云一舞上装"
  itemId: 510277
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000944
  poolId: 10009
  name: "腾云一舞下装"
  itemId: 520190
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000945
  poolId: 10009
  name: "腾云一舞手套"
  itemId: 530166
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000946
  poolId: 10009
  name: "竹韵清扬上装"
  itemId: 510278
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000947
  poolId: 10009
  name: "竹韵清扬下装"
  itemId: 520191
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000948
  poolId: 10009
  name: "竹韵清扬手套"
  itemId: 530167
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000949
  poolId: 10009
  name: "墨韵乘云上装"
  itemId: 510279
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000950
  poolId: 10009
  name: "墨韵乘云下装"
  itemId: 520192
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000951
  poolId: 10009
  name: "墨韵乘云手套"
  itemId: 530168
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000952
  poolId: 10009
  name: "粉焰旋风上装"
  itemId: 510254
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000953
  poolId: 10009
  name: "粉焰旋风下装"
  itemId: 520178
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000954
  poolId: 10009
  name: "粉焰旋风手套"
  itemId: 530154
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000955
  poolId: 10009
  name: "蔚蓝闪电上装"
  itemId: 510255
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000956
  poolId: 10009
  name: "蔚蓝闪电下装"
  itemId: 520179
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000957
  poolId: 10009
  name: "蔚蓝闪电手套"
  itemId: 530155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000958
  poolId: 10009
  name: "逐梦狂飙上装"
  itemId: 510256
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000959
  poolId: 10009
  name: "逐梦狂飙下装"
  itemId: 520180
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000960
  poolId: 10009
  name: "逐梦狂飙手套"
  itemId: 530156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000961
  poolId: 10009
  name: "云龙武袍上装"
  itemId: 510257
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000962
  poolId: 10009
  name: "云龙武袍下装"
  itemId: 520181
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000963
  poolId: 10009
  name: "云龙武袍手套"
  itemId: 530157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000964
  poolId: 10009
  name: "青岚短衫上装"
  itemId: 510268
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000965
  poolId: 10009
  name: "青岚短衫下装"
  itemId: 520182
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000966
  poolId: 10009
  name: "青岚短衫手套"
  itemId: 530158
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000967
  poolId: 10009
  name: "专业态度上装"
  itemId: 510216
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000968
  poolId: 10009
  name: "专业态度下装"
  itemId: 520153
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000969
  poolId: 10009
  name: "专业态度手套"
  itemId: 530129
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000970
  poolId: 10009
  name: "职场萌星上装"
  itemId: 510233
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000971
  poolId: 10009
  name: "职场萌星下装"
  itemId: 520162
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000972
  poolId: 10009
  name: "职场萌星手套"
  itemId: 530138
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000973
  poolId: 10009
  name: "赛场飞驰上装"
  itemId: 510219
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000974
  poolId: 10009
  name: "赛场飞驰下装"
  itemId: 520155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000975
  poolId: 10009
  name: "赛场飞驰手套"
  itemId: 530131
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000976
  poolId: 10009
  name: "浅蓝节拍上装"
  itemId: 510220
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000977
  poolId: 10009
  name: "浅蓝节拍下装"
  itemId: 520156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000978
  poolId: 10009
  name: "浅蓝节拍手套"
  itemId: 530132
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000979
  poolId: 10009
  name: "春日运动上装"
  itemId: 510221
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000980
  poolId: 10009
  name: "春日运动下装"
  itemId: 520157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000981
  poolId: 10009
  name: "春日运动手套"
  itemId: 530133
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1000982
  poolId: 10009
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 1000983
  poolId: 10009
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1000984
  poolId: 10009
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1002900
  poolId: 20000003
  name: "互动道具-喜庆鞭炮*1"
  itemId: 725301
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 1002901
  poolId: 20000003
  name: "心心糖果*1"
  itemId: 200015
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 10
}
rows {
  rewardId: 1002902
  poolId: 20000003
  name: "兑换币-福运灯笼*10"
  itemId: 3710
  itemNum: 10
  groupId: 1
  weight: 2
  limit: 2
}
rows {
  rewardId: 1002903
  poolId: 20000003
  name: "紫装-长乐（7天试用） 首抽必中"
  itemId: 410110
  itemNum: 1
  expireDays: 7
  groupId: 3
  weight: 1
  limit: 1
}
rows {
  rewardId: 1002904
  poolId: 20000003
  name: "头像框-金蛇祈福"
  itemId: 840243
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1003901
  poolId: 20000004
  name: "互动道具-烟花礼盒*1"
  itemId: 725201
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 1003902
  poolId: 20000004
  name: "狼人币*20"
  itemId: 13
  itemNum: 20
  groupId: 1
  weight: 1
  limit: 2
}
rows {
  rewardId: 1003903
  poolId: 20000004
  name: "卡牌1档*2"
  itemId: 290011
  itemNum: 2
  groupId: 1
  weight: 1
  limit: 5
}
rows {
  rewardId: 1003904
  poolId: 20000004
  name: "兑换币-福运灯笼*10"
  itemId: 3710
  itemNum: 10
  groupId: 1
  weight: 1
  limit: 2
}
rows {
  rewardId: 1003905
  poolId: 20000004
  name: "二选一礼包（豹卷卷、名侦探）需更换图标"
  itemId: 330017
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1004901
  poolId: 20000005
  name: "星宝印章*500"
  itemId: 4
  itemNum: 500
  groupId: 1
  weight: 1
  limit: 6
}
rows {
  rewardId: 1004902
  poolId: 20000005
  name: "峡谷币*25"
  itemId: 3541
  itemNum: 25
  groupId: 1
  weight: 1
  limit: 2
}
rows {
  rewardId: 1004903
  poolId: 20000005
  name: "卡牌2档*2"
  itemId: 290012
  itemNum: 2
  groupId: 1
  weight: 1
  limit: 5
}
rows {
  rewardId: 1004904
  poolId: 20000005
  name: "兑换币-福运灯笼*10"
  itemId: 3710
  itemNum: 10
  groupId: 1
  weight: 2
  limit: 3
}
rows {
  rewardId: 1004905
  poolId: 20000005
  name: "绿装-锦裾新朝"
  itemId: 510321
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1004906
  poolId: 20000005
  name: "互动道具-花瓣飞舞(图上不展示）"
  itemId: 725202
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 1005901
  poolId: 20000006
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 1
  limit: 5
}
rows {
  rewardId: 1005902
  poolId: 20000006
  name: "印章加成卡*1"
  itemId: 203001
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 3
}
rows {
  rewardId: 1005903
  poolId: 20000006
  name: "卡牌3档*1"
  itemId: 290013
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 6
}
rows {
  rewardId: 1005904
  poolId: 20000006
  name: "兑换币-福运灯笼*10"
  itemId: 3710
  itemNum: 10
  groupId: 1
  weight: 1
  limit: 3
}
rows {
  rewardId: 1005905
  poolId: 20000006
  name: "头饰-幸福天降"
  itemId: 630474
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1005906
  poolId: 20000006
  name: "互动道具-钞能力(图上不展示）"
  itemId: 725102
  itemNum: 1
  groupId: 1
  weight: 1
}
rows {
  rewardId: 1006901
  poolId: 20000007
  name: "饰品调色盘*1"
  itemId: 200008
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 2
}
rows {
  rewardId: 1006902
  poolId: 20000007
  name: "时装染色膏*1"
  itemId: 200006
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 2
}
rows {
  rewardId: 1006903
  poolId: 20000007
  name: "磷虾*2"
  itemId: 219000
  itemNum: 2
  groupId: 1
  weight: 3
  limit: 2
}
rows {
  rewardId: 1006904
  poolId: 20000007
  name: "兑换币-福运灯笼*20"
  itemId: 3710
  itemNum: 20
  groupId: 1
  weight: 3
  limit: 5
}
rows {
  rewardId: 1006905
  poolId: 20000007
  name: "紫装-长乐"
  itemId: 410110
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1006906
  poolId: 20000007
  name: "互动道具-泡泡枪(图上不展示）"
  itemId: 725101
  itemNum: 1
  groupId: 1
  weight: 3
}
rows {
  rewardId: 1006907
  poolId: 20000007
  name: "心心糖果*1(图上不展示）"
  itemId: 200015
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 5
}
rows {
  rewardId: 1006908
  poolId: 20000007
  name: "心心宝瓶*1(图上不展示）"
  itemId: 200016
  itemNum: 1
  groupId: 1
  weight: 3
  limit: 5
}
rows {
  rewardId: 1006909
  poolId: 20000007
  name: "兑换币-福运灯笼*10(图上不展示）"
  itemId: 3710
  itemNum: 10
  groupId: 1
  weight: 5
  limit: 10
}
rows {
  rewardId: 1006910
  poolId: 20000007
  name: "云朵币*5(图上不展示）"
  itemId: 6
  itemNum: 5
  groupId: 1
  weight: 3
  limit: 5
}
rows {
  rewardId: 1000985
  poolId: 20000008
  name: "腾讯视频VIP年卡"
  itemId: 200625
  itemNum: 1
  groupId: 1
  weight: 100
  limit: 1
}
rows {
  rewardId: 1000986
  poolId: 20000008
  name: "元梦周边五件套礼包"
  itemId: 200631
  itemNum: 1
  groupId: 1
  weight: 3000
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1000987
  poolId: 20000008
  name: "腾讯视频VIP月卡"
  itemId: 200626
  itemNum: 1
  groupId: 1
  weight: 400
  limit: 1
}
rows {
  rewardId: 1000988
  poolId: 20000008
  name: "腾讯视频VIP双周卡"
  itemId: 200627
  itemNum: 1
  groupId: 1
  weight: 1000
  limit: 2
}
rows {
  rewardId: 1000989
  poolId: 20000008
  name: "腾讯视频VIP宝箱"
  itemId: 200616
  itemNum: 1
  groupId: 1
  weight: 2000
}
rows {
  rewardId: 1000990
  poolId: 20000008
  name: "iphone"
  itemId: 200613
  itemNum: 1
  groupId: 1
  weight: 1000
  limit: 1
}
rows {
  rewardId: 1000991
  poolId: 20000008
  name: "甜蜜初心*5"
  itemId: 200014
  itemNum: 5
  groupId: 1
  weight: 2500
}
rows {
  rewardId: 1000992
  poolId: 20000008
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 2800
}
rows {
  rewardId: 1001000
  poolId: 10010
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1001001
  poolId: 10010
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1001002
  poolId: 10010
  name: "印章祈愿兑换券*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1001003
  poolId: 10010
  name: "印章祈愿兑换券*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1001004
  poolId: 10010
  name: "印章祈愿兑换券*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1001005
  poolId: 10010
  name: "小小奶锅"
  itemId: 620766
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001006
  poolId: 10010
  name: "小小香锅"
  itemId: 620767
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001007
  poolId: 10010
  name: "唤醒清晨"
  itemId: 630510
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001008
  poolId: 10010
  name: "飞翔之眼"
  itemId: 610270
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001009
  poolId: 10010
  name: "绒绒小耳"
  itemId: 630402
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001010
  poolId: 10010
  name: "蓝纹绒帽"
  itemId: 630403
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001011
  poolId: 10010
  name: "野性之息"
  itemId: 610248
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001012
  poolId: 10010
  name: "百步穿杨"
  itemId: 620532
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001013
  poolId: 10010
  name: "活力满分"
  itemId: 620533
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001014
  poolId: 10010
  name: "精选好礼"
  itemId: 630310
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001015
  poolId: 10010
  name: "清凉蓝叉"
  itemId: 620458
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001016
  poolId: 10010
  name: "灿烂黄叉"
  itemId: 620459
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001017
  poolId: 10010
  name: "平衡之姿"
  itemId: 720959
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001018
  poolId: 10010
  name: "幸福荡漾"
  itemId: 720943
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001019
  poolId: 10010
  name: "别过来呀"
  itemId: 720838
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001020
  poolId: 10010
  name: "恳求"
  itemId: 720801
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001021
  poolId: 10010
  name: "随便弹弹"
  itemId: 711109
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001022
  poolId: 10010
  name: "你在逗我"
  itemId: 711102
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001023
  poolId: 10010
  name: "怎么敢的"
  itemId: 711147
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001024
  poolId: 10010
  name: "叉出去"
  itemId: 711153
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001025
  poolId: 10010
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1001026
  poolId: 10010
  name: "点点乐"
  itemId: 510262
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001027
  poolId: 10010
  name: "鸭鸭护卫队上装"
  itemId: 510300
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001028
  poolId: 10010
  name: "鸭鸭护卫队下装"
  itemId: 520202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001029
  poolId: 10010
  name: "鸭鸭护卫队手套"
  itemId: 530178
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001030
  poolId: 10010
  name: "脆弱鸭鸭上装"
  itemId: 510301
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001031
  poolId: 10010
  name: "脆弱鸭鸭下装"
  itemId: 520203
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001032
  poolId: 10010
  name: "脆弱鸭鸭手套"
  itemId: 530179
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001033
  poolId: 10010
  name: "冬日暖喵上装"
  itemId: 510297
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001034
  poolId: 10010
  name: "冬日暖喵下装"
  itemId: 520199
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001035
  poolId: 10010
  name: "冬日暖喵手套"
  itemId: 530175
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001036
  poolId: 10010
  name: "林深如墨上装"
  itemId: 510298
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001037
  poolId: 10010
  name: "林深如墨下装"
  itemId: 520200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001038
  poolId: 10010
  name: "林深如墨手套"
  itemId: 530176
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001039
  poolId: 10010
  name: "冬夜星火上装"
  itemId: 510299
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001040
  poolId: 10010
  name: "冬夜星火下装"
  itemId: 520201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001041
  poolId: 10010
  name: "冬夜星火手套"
  itemId: 530177
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001042
  poolId: 10010
  name: "桃心王子上装"
  itemId: 510280
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001043
  poolId: 10010
  name: "桃心王子下装"
  itemId: 520193
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001044
  poolId: 10010
  name: "桃心王子手套"
  itemId: 530169
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001045
  poolId: 10010
  name: "梅花公爵上装"
  itemId: 510281
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001046
  poolId: 10010
  name: "梅花公爵下装"
  itemId: 520194
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001047
  poolId: 10010
  name: "梅花公爵手套"
  itemId: 530170
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001048
  poolId: 10010
  name: "腾云一舞上装"
  itemId: 510277
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001049
  poolId: 10010
  name: "腾云一舞下装"
  itemId: 520190
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001050
  poolId: 10010
  name: "腾云一舞手套"
  itemId: 530166
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001051
  poolId: 10010
  name: "竹韵清扬上装"
  itemId: 510278
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001052
  poolId: 10010
  name: "竹韵清扬下装"
  itemId: 520191
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001053
  poolId: 10010
  name: "竹韵清扬手套"
  itemId: 530167
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001054
  poolId: 10010
  name: "墨韵乘云上装"
  itemId: 510279
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001055
  poolId: 10010
  name: "墨韵乘云下装"
  itemId: 520192
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001056
  poolId: 10010
  name: "墨韵乘云手套"
  itemId: 530168
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001057
  poolId: 10010
  name: "粉焰旋风上装"
  itemId: 510254
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001058
  poolId: 10010
  name: "粉焰旋风下装"
  itemId: 520178
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001059
  poolId: 10010
  name: "粉焰旋风手套"
  itemId: 530154
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001060
  poolId: 10010
  name: "蔚蓝闪电上装"
  itemId: 510255
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001061
  poolId: 10010
  name: "蔚蓝闪电下装"
  itemId: 520179
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001062
  poolId: 10010
  name: "蔚蓝闪电手套"
  itemId: 530155
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001063
  poolId: 10010
  name: "逐梦狂飙上装"
  itemId: 510256
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001064
  poolId: 10010
  name: "逐梦狂飙下装"
  itemId: 520180
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001065
  poolId: 10010
  name: "逐梦狂飙手套"
  itemId: 530156
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001066
  poolId: 10010
  name: "云龙武袍上装"
  itemId: 510257
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001067
  poolId: 10010
  name: "云龙武袍下装"
  itemId: 520181
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001068
  poolId: 10010
  name: "云龙武袍手套"
  itemId: 530157
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001069
  poolId: 10010
  name: "青岚短衫上装"
  itemId: 510268
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001070
  poolId: 10010
  name: "青岚短衫下装"
  itemId: 520182
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001071
  poolId: 10010
  name: "青岚短衫手套"
  itemId: 530158
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001072
  poolId: 10010
  name: "清爽运动上装"
  itemId: 510008
  itemNum: 1
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001073
  poolId: 10010
  name: "清爽运动下装"
  itemId: 520008
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1001074
  poolId: 10010
  name: "清爽运动手套"
  itemId: 530015
  itemNum: 1
  groupId: 1
  weight: 10
  limit: 3
}
rows {
  rewardId: 1001075
  poolId: 10011
  name: "蛋小龙（试用7天）"
  itemId: 410200
  itemNum: 1
  expireDays: 7
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001076
  poolId: 10011
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1001077
  poolId: 10011
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1001078
  poolId: 10011
  name: "印章祈愿兑换券*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1001079
  poolId: 10011
  name: "印章祈愿兑换券*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1001080
  poolId: 10011
  name: "印章祈愿兑换券*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1001081
  poolId: 10011
  name: "蔚蓝行星"
  itemId: 630544
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001082
  poolId: 10011
  name: "赤土星球"
  itemId: 630545
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001083
  poolId: 10011
  name: "向左看齐"
  itemId: 610347
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001084
  poolId: 10011
  name: "小小奶锅"
  itemId: 620766
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001085
  poolId: 10011
  name: "小小香锅"
  itemId: 620767
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001086
  poolId: 10011
  name: "唤醒清晨"
  itemId: 630510
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001087
  poolId: 10011
  name: "飞翔之眼"
  itemId: 610270
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001088
  poolId: 10011
  name: "绒绒小耳"
  itemId: 630402
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001089
  poolId: 10011
  name: "蓝纹绒帽"
  itemId: 630403
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001090
  poolId: 10011
  name: "野性之息"
  itemId: 610248
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001091
  poolId: 10011
  name: "百步穿杨"
  itemId: 620532
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001092
  poolId: 10011
  name: "活力满分"
  itemId: 620533
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001093
  poolId: 10011
  name: "拜托你啦"
  itemId: 720945
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001094
  poolId: 10011
  name: "全力蹦蹦跳"
  itemId: 720964
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001095
  poolId: 10011
  name: "平衡之姿"
  itemId: 720959
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001096
  poolId: 10011
  name: "幸福荡漾"
  itemId: 720943
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001097
  poolId: 10011
  name: "一展歌喉"
  itemId: 711145
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001098
  poolId: 10011
  name: "郁郁中"
  itemId: 711106
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001099
  poolId: 10011
  name: "随便弹弹"
  itemId: 711109
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001100
  poolId: 10011
  name: "你在逗我"
  itemId: 711102
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001101
  poolId: 10011
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1001102
  poolId: 10011
  name: "点点乐"
  itemId: 510262
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001103
  poolId: 10011
  name: "牛油果之友上装"
  itemId: 510335
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001104
  poolId: 10011
  name: "牛油果之友下装"
  itemId: 520224
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001105
  poolId: 10011
  name: "牛油果之友手套"
  itemId: 530200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001106
  poolId: 10011
  name: "果冻熊之友上装"
  itemId: 510336
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001107
  poolId: 10011
  name: "果冻熊之友下装"
  itemId: 520225
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001108
  poolId: 10011
  name: "果冻熊之友手套"
  itemId: 530201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001109
  poolId: 10011
  name: "棉花狗之友上装"
  itemId: 510337
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001110
  poolId: 10011
  name: "棉花狗之友下装"
  itemId: 520226
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001111
  poolId: 10011
  name: "棉花狗之友手套"
  itemId: 530202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001112
  poolId: 10011
  name: "海岛风情上装"
  itemId: 510350
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001113
  poolId: 10011
  name: "海岛风情下装"
  itemId: 520233
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001114
  poolId: 10011
  name: "海岛风情手套"
  itemId: 530209
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001115
  poolId: 10011
  name: "花舞樱樱上装"
  itemId: 510351
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001116
  poolId: 10011
  name: "花舞樱樱下装"
  itemId: 520234
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001117
  poolId: 10011
  name: "花舞樱樱手套"
  itemId: 530210
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001118
  poolId: 10011
  name: "鸭鸭护卫队上装"
  itemId: 510300
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001119
  poolId: 10011
  name: "鸭鸭护卫队下装"
  itemId: 520202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001120
  poolId: 10011
  name: "鸭鸭护卫队手套"
  itemId: 530178
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001121
  poolId: 10011
  name: "脆弱鸭鸭上装"
  itemId: 510301
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001122
  poolId: 10011
  name: "脆弱鸭鸭下装"
  itemId: 520203
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001123
  poolId: 10011
  name: "脆弱鸭鸭手套"
  itemId: 530179
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001124
  poolId: 10011
  name: "冬日暖喵上装"
  itemId: 510297
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001125
  poolId: 10011
  name: "冬日暖喵下装"
  itemId: 520199
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001126
  poolId: 10011
  name: "冬日暖喵手套"
  itemId: 530175
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001127
  poolId: 10011
  name: "林深如墨上装"
  itemId: 510298
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001128
  poolId: 10011
  name: "林深如墨下装"
  itemId: 520200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001129
  poolId: 10011
  name: "林深如墨手套"
  itemId: 530176
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001130
  poolId: 10011
  name: "冬夜星火上装"
  itemId: 510299
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001131
  poolId: 10011
  name: "冬夜星火下装"
  itemId: 520201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001132
  poolId: 10011
  name: "冬夜星火手套"
  itemId: 530177
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001133
  poolId: 10011
  name: "桃心王子上装"
  itemId: 510280
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001134
  poolId: 10011
  name: "桃心王子下装"
  itemId: 520193
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001135
  poolId: 10011
  name: "桃心王子手套"
  itemId: 530169
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001136
  poolId: 10011
  name: "梅花公爵上装"
  itemId: 510281
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001137
  poolId: 10011
  name: "梅花公爵下装"
  itemId: 520194
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001138
  poolId: 10011
  name: "梅花公爵手套"
  itemId: 530170
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001139
  poolId: 10011
  name: "腾云一舞上装"
  itemId: 510277
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001140
  poolId: 10011
  name: "腾云一舞下装"
  itemId: 520190
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001141
  poolId: 10011
  name: "腾云一舞手套"
  itemId: 530166
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001142
  poolId: 10011
  name: "竹韵清扬上装"
  itemId: 510278
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001143
  poolId: 10011
  name: "竹韵清扬下装"
  itemId: 520191
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001144
  poolId: 10011
  name: "竹韵清扬手套"
  itemId: 530167
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001145
  poolId: 10011
  name: "墨韵乘云上装"
  itemId: 510279
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001146
  poolId: 10011
  name: "墨韵乘云下装"
  itemId: 520192
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001147
  poolId: 10011
  name: "墨韵乘云手套"
  itemId: 530168
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001190
  poolId: 20000009
  name: "腾讯视频VIP年卡"
  itemId: 200810
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001191
  poolId: 20000009
  name: "元梦周边-紫萝萝痛包"
  itemId: 200780
  itemNum: 1
  groupId: 1
  weight: 200
  limit: 1
  isGrand: true
}
rows {
  rewardId: 1001192
  poolId: 20000009
  name: "腾讯视频VIP月卡"
  itemId: 200811
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001193
  poolId: 20000009
  name: "腾讯视频VIP双周卡"
  itemId: 200812
  itemNum: 1
  groupId: 1
  weight: 1
  limit: 2
}
rows {
  rewardId: 1001194
  poolId: 20000009
  name: "福利碎片*30"
  itemId: 3134
  itemNum: 30
  groupId: 1
  weight: 1
}
rows {
  rewardId: 1001195
  poolId: 20000009
  name: "心愿币*6"
  itemId: 2
  itemNum: 6
  groupId: 1
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001196
  poolId: 20000009
  name: "甜蜜初心*5"
  itemId: 200014
  itemNum: 5
  groupId: 1
  weight: 1
}
rows {
  rewardId: 1001197
  poolId: 20000009
  name: "星宝印章*1000"
  itemId: 4
  itemNum: 1000
  groupId: 1
  weight: 1
}
rows {
  rewardId: 1001200
  poolId: 10012
  name: "林小泽（7天试用）"
  itemId: 403190
  itemNum: 1
  expireDays: 7
  groupId: 8
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001201
  poolId: 10012
  name: "云朵币*10"
  itemId: 6
  itemNum: 10
  groupId: 7
  weight: 10
  limit: 5
}
rows {
  rewardId: 1001202
  poolId: 10012
  name: "云朵币*5"
  itemId: 6
  itemNum: 5
  groupId: 7
  weight: 90
  limit: 700
}
rows {
  rewardId: 1001203
  poolId: 10012
  name: "印章祈愿兑换券*8"
  itemId: 2007
  itemNum: 8
  groupId: 6
  weight: 5
  limit: 3
}
rows {
  rewardId: 1001204
  poolId: 10012
  name: "印章祈愿兑换券*6"
  itemId: 2007
  itemNum: 6
  groupId: 6
  weight: 10
  limit: 7
}
rows {
  rewardId: 1001205
  poolId: 10012
  name: "印章祈愿兑换券*3"
  itemId: 2007
  itemNum: 3
  groupId: 6
  weight: 85
  limit: 50
}
rows {
  rewardId: 1001206
  poolId: 10012
  name: "杠精小包"
  itemId: 620909
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001207
  poolId: 10012
  name: "落日眼镜"
  itemId: 610395
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001208
  poolId: 10012
  name: "霓虹眼镜"
  itemId: 610396
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001209
  poolId: 10012
  name: "蔚蓝行星"
  itemId: 630544
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001210
  poolId: 10012
  name: "赤土星球"
  itemId: 630545
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001211
  poolId: 10012
  name: "向左看齐"
  itemId: 610347
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001212
  poolId: 10012
  name: "小小奶锅"
  itemId: 620766
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001213
  poolId: 10012
  name: "小小香锅"
  itemId: 620767
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001214
  poolId: 10012
  name: "唤醒清晨"
  itemId: 630510
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001215
  poolId: 10012
  name: "飞翔之眼"
  itemId: 610270
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001216
  poolId: 10012
  name: "绒绒小耳"
  itemId: 630402
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001217
  poolId: 10012
  name: "蓝纹绒帽"
  itemId: 630403
  itemNum: 1
  groupId: 5
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001218
  poolId: 10012
  name: "闹小情绪"
  itemId: 720847
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001219
  poolId: 10012
  name: "平沙落雁式"
  itemId: 720773
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001220
  poolId: 10012
  name: "拜托你啦"
  itemId: 720945
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001221
  poolId: 10012
  name: "全力蹦蹦跳"
  itemId: 720964
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001222
  poolId: 10012
  name: "坏水"
  itemId: 711018
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001223
  poolId: 10012
  name: "智慧的伪装"
  itemId: 711394
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001224
  poolId: 10012
  name: "扭捏"
  itemId: 711148
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001225
  poolId: 10012
  name: "郁郁中"
  itemId: 711106
  itemNum: 1
  groupId: 4
  weight: 5
  limit: 1
}
rows {
  rewardId: 1001226
  poolId: 10012
  name: "露齿笑"
  itemId: 710037
  itemNum: 1
  groupId: 3
  weight: 10
  limit: 1
}
rows {
  rewardId: 1001227
  poolId: 10012
  name: "点点乐"
  itemId: 510262
  itemNum: 1
  groupId: 2
  weight: 1
  limit: 1
}
rows {
  rewardId: 1001228
  poolId: 10012
  name: "甜蜜梦乡上装"
  itemId: 510371
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001229
  poolId: 10012
  name: "甜蜜梦乡下装"
  itemId: 520246
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001230
  poolId: 10012
  name: "甜蜜梦乡手套"
  itemId: 530222
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001231
  poolId: 10012
  name: "花间睡服上装"
  itemId: 510372
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001232
  poolId: 10012
  name: "花间睡服下装"
  itemId: 520247
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001233
  poolId: 10012
  name: "花间睡服手套"
  itemId: 530223
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001234
  poolId: 10012
  name: "星河入梦上装"
  itemId: 510373
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001235
  poolId: 10012
  name: "星河入梦下装"
  itemId: 520248
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001236
  poolId: 10012
  name: "星河入梦手套"
  itemId: 530224
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001237
  poolId: 10012
  name: "无贝不宝上装"
  itemId: 510375
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001238
  poolId: 10012
  name: "无贝不宝下装"
  itemId: 520249
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001239
  poolId: 10012
  name: "无贝不宝手套"
  itemId: 530225
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001240
  poolId: 10012
  name: "无宝不贝上装"
  itemId: 510376
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001241
  poolId: 10012
  name: "无宝不贝下装"
  itemId: 520250
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001242
  poolId: 10012
  name: "无宝不贝手套"
  itemId: 530226
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001243
  poolId: 10012
  name: "牛油果之友上装"
  itemId: 510335
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001244
  poolId: 10012
  name: "牛油果之友下装"
  itemId: 520224
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001245
  poolId: 10012
  name: "牛油果之友手套"
  itemId: 530200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001246
  poolId: 10012
  name: "果冻熊之友上装"
  itemId: 510336
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001247
  poolId: 10012
  name: "果冻熊之友下装"
  itemId: 520225
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001248
  poolId: 10012
  name: "果冻熊之友手套"
  itemId: 530201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001249
  poolId: 10012
  name: "棉花狗之友上装"
  itemId: 510337
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001250
  poolId: 10012
  name: "棉花狗之友下装"
  itemId: 520226
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001251
  poolId: 10012
  name: "棉花狗之友手套"
  itemId: 530202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001252
  poolId: 10012
  name: "海岛风情上装"
  itemId: 510350
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001253
  poolId: 10012
  name: "海岛风情下装"
  itemId: 520233
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001254
  poolId: 10012
  name: "海岛风情手套"
  itemId: 530209
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001255
  poolId: 10012
  name: "花舞樱樱上装"
  itemId: 510351
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001256
  poolId: 10012
  name: "花舞樱樱下装"
  itemId: 520234
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001257
  poolId: 10012
  name: "花舞樱樱手套"
  itemId: 530210
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001258
  poolId: 10012
  name: "鸭鸭护卫队上装"
  itemId: 510300
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001259
  poolId: 10012
  name: "鸭鸭护卫队下装"
  itemId: 520202
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001260
  poolId: 10012
  name: "鸭鸭护卫队手套"
  itemId: 530178
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001261
  poolId: 10012
  name: "脆弱鸭鸭上装"
  itemId: 510301
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001262
  poolId: 10012
  name: "脆弱鸭鸭下装"
  itemId: 520203
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001263
  poolId: 10012
  name: "脆弱鸭鸭手套"
  itemId: 530179
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001264
  poolId: 10012
  name: "冬日暖喵上装"
  itemId: 510297
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001265
  poolId: 10012
  name: "冬日暖喵下装"
  itemId: 520199
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001266
  poolId: 10012
  name: "冬日暖喵手套"
  itemId: 530175
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001267
  poolId: 10012
  name: "林深如墨上装"
  itemId: 510298
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001268
  poolId: 10012
  name: "林深如墨下装"
  itemId: 520200
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001269
  poolId: 10012
  name: "林深如墨手套"
  itemId: 530176
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001270
  poolId: 10012
  name: "冬夜星火上装"
  itemId: 510299
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001271
  poolId: 10012
  name: "冬夜星火下装"
  itemId: 520201
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
rows {
  rewardId: 1001272
  poolId: 10012
  name: "冬夜星火手套"
  itemId: 530177
  itemNum: 1
  groupId: 1
  weight: 7
  limit: 5
}
