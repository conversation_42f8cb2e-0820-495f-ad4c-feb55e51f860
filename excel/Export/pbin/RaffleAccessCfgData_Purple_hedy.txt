com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_hedy.xlsx sheet:活动-紫色卡池
rows {
  raffleId: 30000005
  name: "巴啦啦"
  startTime {
    seconds: 1727193600
  }
  endTime {
    seconds: 1729526399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51140
    subPoolIds: 51141
    subPoolIds: 51142
    subPoolIds: 51143
    subPoolIds: 51144
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 159
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.7.95"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "小蓝"
  raffleTagIcon: "T_Balala_Icon_Tab1"
  jumpIds: 25
  commodityIds: 120060
  commodityIds: 120061
  commodityIds: 120062
  commodityIds: 120063
  commodityIds: 120064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1727193600
  }
  showEndTime {
    seconds: 1729526399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000006
  name: "巴啦啦"
  startTime {
    seconds: 1727193600
  }
  endTime {
    seconds: 1729526399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51150
    subPoolIds: 51151
    subPoolIds: 51152
    subPoolIds: 51153
    subPoolIds: 51154
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 160
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.7.95"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "游乐"
  raffleTagIcon: "T_Balala_Icon_Tab2"
  jumpIds: 25
  commodityIds: 120060
  commodityIds: 120061
  commodityIds: 120062
  commodityIds: 120063
  commodityIds: 120064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1727193600
  }
  showEndTime {
    seconds: 1729526399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000007
  name: "柯南联动时装"
  startTime {
    seconds: 1734624000
  }
  endTime {
    seconds: 1737043199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000070
    subPoolIds: 300000071
    subPoolIds: 300000072
    subPoolIds: 300000073
    subPoolIds: 300000074
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10001
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.37.37"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "江户川柯南"
  raffleTagIcon: "T_Connan_Img_TabKeNan"
  jumpIds: 1064
  commodityIds: 120165
  commodityIds: 120166
  commodityIds: 120167
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1734624000
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000008
  name: "毛利兰"
  startTime {
    seconds: 1734624000
  }
  endTime {
    seconds: 1737043199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000080
    subPoolIds: 300000081
    subPoolIds: 300000082
    subPoolIds: 300000083
    subPoolIds: 300000084
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10002
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.37.37"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "毛利兰"
  raffleTagIcon: "T_Connan_Img_TabXiaoLan"
  jumpIds: 1064
  commodityIds: 120165
  commodityIds: 120166
  commodityIds: 120167
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1734624000
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000009
  name: "灰原哀"
  startTime {
    seconds: 1734624000
  }
  endTime {
    seconds: 1737043199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000090
    subPoolIds: 300000091
    subPoolIds: 300000092
    subPoolIds: 300000093
    subPoolIds: 300000094
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10003
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.37.37"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "灰原哀"
  raffleTagIcon: "T_Connan_Img_TabHuiYuan"
  jumpIds: 1064
  commodityIds: 120165
  commodityIds: 120166
  commodityIds: 120167
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1734624000
  }
  showEndTime {
    seconds: 1737043199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000010
  name: "天线宝宝"
  startTime {
    seconds: 1733500800
  }
  endTime {
    seconds: 1735315199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 300000095
  }
  dailyLimit: 80
  maxLimit: 80
  textRuleId: 287
  text: "前10次抽取5折|再祈愿<LotteryTeletubbiesTips>{0}</>次必得<LotteryTeletubbiesTips>非凡时装</>"
  lowestVersion: "*********"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "天线宝宝"
  raffleTagIcon: "T_SupremoCat_Icon_Tab2"
  jumpIds: 25
  viewIndex: 1
  viewIndex: 6
  viewIndex: 11
  viewIndex: 2
  viewIndex: 12
  viewIndex: 7
  viewIndex: 3
  viewIndex: 8
  viewIndex: 9
  viewIndex: 4
  viewIndex: 10
  viewIndex: 5
  showStartTime {
    seconds: 1733500800
  }
  showEndTime {
    seconds: 1735315199
  }
  isShow: true
  showRule: 1
  gotGrandRule: 1
  maxCountMultiDraw: 10
}
rows {
  raffleId: 30000011
  name: "三丽鸥家族"
  startTime {
    seconds: 1737648000
  }
  endTime {
    seconds: 1740067199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000100
    subPoolIds: 300000101
    subPoolIds: 300000102
    subPoolIds: 300000103
    subPoolIds: 300000104
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10005
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.68.52"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "大耳狗"
  raffleTagIcon: "T_Sanrio_Img_Cinnamoroll"
  jumpIds: 1064
  commodityIds: 120200
  commodityIds: 120201
  commodityIds: 120202
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000012
  name: "三丽鸥"
  startTime {
    seconds: 1737648000
  }
  endTime {
    seconds: 1740067199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000110
    subPoolIds: 300000111
    subPoolIds: 300000112
    subPoolIds: 300000113
    subPoolIds: 300000114
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10006
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.68.52"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "美乐蒂"
  raffleTagIcon: "T_Sanrio_Img_Melody"
  jumpIds: 1064
  commodityIds: 120200
  commodityIds: 120201
  commodityIds: 120202
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000013
  name: "三丽鸥"
  startTime {
    seconds: 1737648000
  }
  endTime {
    seconds: 1740067199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000120
    subPoolIds: 300000121
    subPoolIds: 300000122
    subPoolIds: 300000123
    subPoolIds: 300000124
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 159
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.68.52"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "HelloKitty"
  raffleTagIcon: "T_Sanrio_Img_HeadKitty"
  jumpIds: 1064
  commodityIds: 120200
  commodityIds: 120201
  commodityIds: 120202
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  showRule: 1
  raffleOpenTag: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000014
  name: "三丽鸥"
  startTime {
    seconds: 1737648000
  }
  endTime {
    seconds: 1740067199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000130
    subPoolIds: 300000131
    subPoolIds: 300000132
    subPoolIds: 300000133
    subPoolIds: 300000134
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 160
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.68.52"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "Kuromi"
  raffleTagIcon: "T_Sanrio_Img_HeadSanrio"
  jumpIds: 1064
  commodityIds: 120200
  commodityIds: 120201
  commodityIds: 120202
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1737648000
  }
  showEndTime {
    seconds: 1740067199
  }
  isShow: true
  showRule: 1
  raffleOpenTag: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000015
  name: "泡泡玛特"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000135
    subPoolIds: 300000136
    subPoolIds: 300000137
    subPoolIds: 300000138
    subPoolIds: 300000139
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10008
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.78.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "小鸡小甜豆"
  raffleTagIcon: "T_PopMart3_Img_Head2"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  relateUmgSetting: "2;UI_Lottery_Angel_ULuckyRule#3;UI_Lottery_NineTailedFox_GloryRoadPanel"
  gotGrandRule: 1
}
rows {
  raffleId: 30000016
  name: "泡泡玛特"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000140
    subPoolIds: 300000141
    subPoolIds: 300000142
    subPoolIds: 300000143
    subPoolIds: 300000144
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10009
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.78.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "菠萝小甜豆"
  raffleTagIcon: "T_PopMart3_Img_Head1"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  relateUmgSetting: "2;UI_Lottery_Angel_ULuckyRule#3;UI_Lottery_NineTailedFox_GloryRoadPanel"
  gotGrandRule: 1
}
rows {
  raffleId: 30000017
  name: "泡泡玛特"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000145
    subPoolIds: 300000146
    subPoolIds: 300000147
    subPoolIds: 300000148
    subPoolIds: 300000149
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 146
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.78.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "绵羊小甜豆"
  raffleTagIcon: "T_PopMart3_Img_Head4"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  showRule: 1
  raffleOpenTag: 1
  advanceItemAnimType: 2
  relateUmgSetting: "2;UI_Lottery_Angel_ULuckyRule#3;UI_Lottery_NineTailedFox_GloryRoadPanel"
  gotGrandRule: 1
}
rows {
  raffleId: 30000018
  name: "泡泡玛特"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000150
    subPoolIds: 300000151
    subPoolIds: 300000152
    subPoolIds: 300000153
    subPoolIds: 300000154
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 147
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.78.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "企鹅小甜豆"
  raffleTagIcon: "T_PopMart3_Img_Head3"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  showRule: 1
  raffleOpenTag: 1
  advanceItemAnimType: 2
  relateUmgSetting: "2;UI_Lottery_Angel_ULuckyRule#3;UI_Lottery_NineTailedFox_GloryRoadPanel"
  gotGrandRule: 1
}
rows {
  raffleId: 30000019
  name: "泡泡玛特"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000155
    subPoolIds: 300000156
    subPoolIds: 300000157
    subPoolIds: 300000158
    subPoolIds: 300000159
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 251
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.78.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "柠檬小甜豆"
  raffleTagIcon: "T_PopMart3_Img_Head5"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  showRule: 1
  raffleOpenTag: 1
  advanceItemAnimType: 2
  relateUmgSetting: "2;UI_Lottery_Angel_ULuckyRule#3;UI_Lottery_NineTailedFox_GloryRoadPanel"
  gotGrandRule: 1
}
rows {
  raffleId: 30000020
  name: "泡泡玛特"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000160
    subPoolIds: 300000161
    subPoolIds: 300000162
    subPoolIds: 300000163
    subPoolIds: 300000164
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 252
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.78.73"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "鸭鸭小甜豆"
  raffleTagIcon: "T_PopMart3_Img_Head6"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  showRule: 1
  raffleOpenTag: 1
  advanceItemAnimType: 2
  relateUmgSetting: "2;UI_Lottery_Angel_ULuckyRule#3;UI_Lottery_NineTailedFox_GloryRoadPanel"
  gotGrandRule: 1
}
rows {
  raffleId: 30000021
  name: "收集有礼"
  startTime {
    seconds: 1743696000
  }
  endTime {
    seconds: 1745510399
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000160
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10010
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.78.73"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1743696000
  }
  showEndTime {
    seconds: 1745510399
  }
  isShow: true
  showRule: 1
  raffleOpenTag: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000023
  name: "樱花咖啡师"
  startTime {
    seconds: 1748620800
  }
  endTime {
    seconds: 1750435199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000165
    subPoolIds: 300000166
    subPoolIds: 300000167
    subPoolIds: 300000168
    subPoolIds: 300000169
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10012
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.88.116"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "卓大王"
  raffleTagIcon: "T_KingZhuo_Img_HeadTeacher"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1748620800
  }
  showEndTime {
    seconds: 1750435199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000024
  name: "兔兔阿卓"
  startTime {
    seconds: 1748620800
  }
  endTime {
    seconds: 1750435199
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 300000170
    subPoolIds: 300000171
    subPoolIds: 300000172
    subPoolIds: 300000173
    subPoolIds: 300000174
  }
  dailyLimit: 28
  maxLimit: 42
  textRuleId: 10013
  text: "抽到钥匙会自动抽取左侧奖池，祈愿1轮时提前获得钥匙会返还幸运币|限时折扣，1-2轮5折，3-4轮75折"
  lowestVersion: "1.3.88.116"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "卓大王"
  raffleTagIcon: "T_KingZhuo_Img_HeadRabbit"
  jumpIds: 1064
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  drawConditions {
    condition {
      conditionType: 1
    }
  }
  showStartTime {
    seconds: 1748620800
  }
  showEndTime {
    seconds: 1750435199
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 2
  gotGrandRule: 1
}
rows {
  raffleId: 30000027
  name: "天线宝宝"
  startTime {
    seconds: 1748361600
  }
  endTime {
    seconds: 1752249599
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 300000175
  }
  dailyLimit: 30
  maxLimit: 80
  textRuleId: 287
  text: "|再祈愿<LotteryTeletubbiesTips>{0}</>次必得<LotteryTeletubbiesTips>非凡时装</>"
  lowestVersion: "**********"
  activityImage: "ModSuit_0025_HalfBody.png"
  raffleTagName: "天线宝宝"
  raffleTagIcon: "T_SupremoCat_Icon_Tab2"
  jumpIds: 25
  viewIndex: 1
  viewIndex: 6
  viewIndex: 11
  viewIndex: 2
  viewIndex: 12
  viewIndex: 7
  viewIndex: 3
  viewIndex: 8
  viewIndex: 9
  viewIndex: 4
  viewIndex: 10
  viewIndex: 5
  showStartTime {
    seconds: 1750435200
  }
  showEndTime {
    seconds: 1752249599
  }
  isShow: true
  showRule: 1
  gotGrandRule: 1
  maxCountMultiDraw: 10
  tagDailyLimit: 80
}
