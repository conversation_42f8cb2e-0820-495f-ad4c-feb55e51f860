com.tencent.wea.xlsRes.table_SfxAudioConfig
excel/xls/Y_音频之音效.xlsx sheet:音效
rows {
  id: 4096
  playEvent: "Play_SFX_QiYoung_Menu_ShowEnter"
  bank: "SFX_QiYoung"
  stopEvent: "Stop_SFX_QiYoung_Menu_ShowEnter"
}
rows {
  id: 4097
  playEvent: "Play_VO_QiYoung_Menu_ShowEnter"
  bank: "VO_QiYoung"
  stopEvent: "Stop_VO_QiYoung"
}
rows {
  id: 10325
  playEvent: "Play_PL_HandHeld_Stage_Idleshow_Standy"
  playEvent3p: "Play_PL_HandHeld_Stage_Idleshow_Standy"
  bank: "PL_HandHeld_Stage"
  stopEvent: "Stop_PL_HandHeld_Stage_Idleshow_Standy"
  stopEvent3p: "Stop_PL_HandHeld_Stage_Idleshow_Standy"
  is3d: 1
  isLoop: 1
}
rows {
  id: 10326
  playEvent: "Play_PL_HandHeld_Stage_Idleshow_Start"
  playEvent3p: "Play_PL_HandHeld_Stage_Idleshow_Start"
  bank: "PL_HandHeld_Stage"
  stopEvent: "Stop_PL_HandHeld_Stage_Idleshow_Start"
  stopEvent3p: "Stop_PL_HandHeld_Stage_Idleshow_Start"
  is3d: 1
  isLoop: 1
}
rows {
  id: 40958
  playEvent: "Play_LetsFarm_Animal_Jerboa"
  playEvent3p: "Play_LetsFarm_Animal_Jerboa"
  bank: "LetsFarm_Animal"
  stopEvent: "Stop_LetsFarm_Animal_Jerboa"
  stopEvent3p: "Stop_LetsFarm_Animal_Jerboa"
  is3d: 1
  isLoop: 1
}
rows {
  id: 40959
  playEvent: "Play_LetsFarm_Animal_Turtle"
  playEvent3p: "Play_LetsFarm_Animal_Turtle"
  bank: "LetsFarm_Animal"
  stopEvent: "Stop_LetsFarm_Animal_Turtle"
  stopEvent3p: "Stop_LetsFarm_Animal_Turtle"
  is3d: 1
  isLoop: 1
}
rows {
  id: 40960
  playEvent: "Play_LetsFarm_Animal_Caracal"
  playEvent3p: "Play_LetsFarm_Animal_Caracal"
  bank: "LetsFarm_Animal"
  stopEvent: "Stop_LetsFarm_Animal_Caracal"
  stopEvent3p: "Stop_LetsFarm_Animal_Caracal"
  is3d: 1
  isLoop: 1
}
rows {
  id: 40961
  playEvent: "Play_LetsFarm_Animal_Damascus"
  playEvent3p: "Play_LetsFarm_Animal_Damascus"
  bank: "LetsFarm_Animal"
  stopEvent: "Stop_LetsFarm_Animal_Damascus"
  stopEvent3p: "Stop_LetsFarm_Animal_Damascus"
  is3d: 1
  isLoop: 1
}
rows {
  id: 40962
  playEvent: "Play_LetsFarm_Animal_Elephant"
  playEvent3p: "Play_LetsFarm_Animal_Elephant"
  bank: "LetsFarm_Animal"
  stopEvent: "Stop_LetsFarm_Animal_Elephant"
  stopEvent3p: "Stop_LetsFarm_Animal_Elephant"
  is3d: 1
  isLoop: 1
}
rows {
  id: 45951
  playEvent: "Play_Obj_GamePlay_Slide_Right"
  bank: "Obj_GamePlay_Slide"
  is3d: 1
}
rows {
  id: 45952
  playEvent: "Play_Obj_GamePlay_Slide_Wrong"
  bank: "Obj_GamePlay_Slide"
  is3d: 1
}
rows {
  id: 46001
  playEvent: "Play_Obj_GamePlay_FireCircle_Fall"
  playEvent3p: "Play_Obj_GamePlay_FireCircle_Fall"
  bank: "Obj_GamePlay_FireCircle"
  is3d: 1
}
rows {
  id: 46002
  playEvent: "Play_Obj_GamePlay_FireCircle_Explo"
  playEvent3p: "Play_Obj_GamePlay_FireCircle_Explo"
  bank: "Obj_GamePlay_FireCircle"
  is3d: 1
}
rows {
  id: 58102
  playEvent: "Play_ui_rich_building_star_levelup"
  bank: "Rich"
}
rows {
  id: 58103
  playEvent: "Play_ui_rich_building_get_switch"
  bank: "Rich"
}
rows {
  id: 58104
  playEvent: "Play_ui_rich_building_get_display"
  bank: "Rich"
}
rows {
  id: 58105
  playEvent: "Play_sfx_rich_city_change"
  bank: "Rich"
}
rows {
  id: 58106
  playEvent: "Play_ui_rich_coin_get_passby"
  bank: "Rich"
}
rows {
  id: 58107
  playEvent: "Play_sfx_rich_duel_process"
  bank: "Rich"
}
rows {
  id: 58108
  playEvent: "Play_ui_rich_duel_round_win"
  bank: "Rich"
}
rows {
  id: 58109
  playEvent: "Play_ui_rich_duel_round_lose"
  bank: "Rich"
}
rows {
  id: 58110
  playEvent: "Play_ui_rich_duel_round_draw"
  bank: "Rich"
}
rows {
  id: 58111
  playEvent: "Play_ui_rich_duel_final_win"
  bank: "Rich"
}
rows {
  id: 58112
  playEvent: "Play_ui_rich_duel_final_lose"
  bank: "Rich"
}
rows {
  id: 58113
  playEvent: "Play_ui_rich_duel_final_draw"
  bank: "Rich"
}
rows {
  id: 59011
  playEvent: "Play_sfx_rich_activity_foresttreasure_grid_break"
  bank: "Rich"
}
rows {
  id: 59012
  playEvent: "Play_sfx_rich_activity_foresttreasure_reward"
  bank: "Rich"
}
rows {
  id: 54007
  playEvent: "Play_DarkStar_Attack_1P"
  playEvent3p: "Play_DarkStar_Attack_3P"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54008
  playEvent: "Play_Obj_Chase_Success_01"
  bank: "Chase"
}
rows {
  id: 54009
  playEvent: "Play_DarkStar_Fishing_1P"
  playEvent3p: "Play_DarkStar_Fishing_3P"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54010
  playEvent: "Play_DarkStar_Fishing_End_1P"
  playEvent3p: "Play_DarkStar_Fishing_End_3P"
  bank: "Chase"
  is3d: 1
  isLoop: 1
}
rows {
  id: 54011
  playEvent: "Play_Player_Chase_Boss_Approach_2D_Loop_1P"
  bank: "Chase"
  stopEvent: "Play_Player_Chase_Boss_Approach_2D_Loop_1P_Stop"
  isLoop: 1
}
rows {
  id: 54012
  playEvent: "Play_DarkStar_Scan_1P"
  playEvent3p: "Play_DarkStar_Scan_3P"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54013
  playEvent: "Play_DarkStar_Catch_01"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54014
  playEvent: "Play_DarkStar_Hit_3P"
  playEvent3p: "Play_DarkStar_Hit_3P"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54015
  playEvent: "Play_Player_Chase_Struggle_Success"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54016
  playEvent: "Play_DarkStar_Put_In_1P"
  bank: "Chase"
}
rows {
  id: 54017
  playEvent: "Play_Obj_Chase_Electric_Shock_3P"
  playEvent3p: "Play_Obj_Chase_Electric_Shock_3P"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54018
  playEvent: "Play_DarkStar_Awakening_1P"
  playEvent3p: "Play_DarkStar_Awakening_3P"
  bank: "Chase"
  is3d: 1
}
rows {
  id: 54019
  playEvent: "Play_Player_Chase_Skill_Scan_Loop_3P"
  playEvent3p: "Play_Player_Chase_Skill_Scan_Loop_3P"
  bank: "Chase"
  stopEvent: "Play_Player_Chase_Skill_Scan_Stop_3P"
  stopEvent3p: "Play_Player_Chase_Skill_Scan_Stop_3P"
  is3d: 1
  isLoop: 1
}
rows {
  id: 54020
  playEvent: "Play_MUS_InGame_Chase_Relic_StarBaby_StarMachine_Stinger_01"
  bank: "MUS_InGame_Chase"
}
rows {
  id: 54021
  playEvent: "Play_MUS_InGame_Chase_Relic_StarBaby_StarMachine_Stinger_02"
  bank: "MUS_InGame_Chase"
}
rows {
  id: 54022
  playEvent: "Play_MUS_InGame_Chase_Relic_StarBaby_StarMachine_Stinger_03"
  bank: "MUS_InGame_Chase"
}
rows {
  id: 54023
  playEvent: "Play_MUS_InGame_Chase_Relic_StarBaby_StarMachine_Stinger_04"
  bank: "MUS_InGame_Chase"
}
rows {
  id: 54024
  playEvent: "Play_MUS_InGame_Chase_Relic_StarBaby_StarMachine_Stinger_05"
  bank: "MUS_InGame_Chase"
}
rows {
  id: 54025
  playEvent: "Play_MUS_InGame_Chase_Relic_StarBaby_MagicPortal_Stinger_01"
  bank: "MUS_InGame_Chase"
}
rows {
  id: 54026
  playEvent: "Play_MUS_InGame_Chase_Relic_DarkStar_Kill_Stinger_01"
  bank: "MUS_InGame_Chase"
}
rows {
  id: 54437
  playEvent: "Play_VO_Chase_DarkStar_Dracula_Hit_1P"
  playEvent3p: "Play_VO_Chase_DarkStar_Dracula_Hit_3P"
  bank: "VO_Chase_Dracula"
  is3d: 1
}
rows {
  id: 54438
  playEvent: "Play_VO_Chase_DarkStar_Elizabeth_Hit_1P"
  playEvent3p: "Play_VO_Chase_DarkStar_Elizabeth_Hit_3P"
  bank: "VO_Chase_Elizabeth"
  is3d: 1
}
rows {
  id: 54439
  playEvent: "Play_VO_Chase_DarkStar_Lamia_Hit_1P"
  playEvent3p: "Play_VO_Chase_DarkStar_Lamia_Hit_3P"
  bank: "VO_Chase_Lamia"
  is3d: 1
}
rows {
  id: 54451
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common1"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common1"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common1"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common1"
  is3d: 1
}
rows {
  id: 54452
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common2"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common2"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common2"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common2"
  is3d: 1
}
rows {
  id: 54453
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common3"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common3"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common3"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common3"
  is3d: 1
}
rows {
  id: 54454
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common4"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common4"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common4"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common4"
  is3d: 1
}
rows {
  id: 54455
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common5"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common5"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common5"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common5"
  is3d: 1
}
rows {
  id: 54456
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common6"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common6"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common6"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common6"
  is3d: 1
}
rows {
  id: 54457
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common7"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common7"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common7"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common7"
  is3d: 1
}
rows {
  id: 54458
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common8"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common8"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common8"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common8"
  is3d: 1
}
rows {
  id: 54459
  playEvent: "Play_LetsChase_VO_DarkStar_Broadcast_Common9"
  playEvent3p: "Play_LetsChase_VO_DarkStar_Broadcast_Common9"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_DarkStar_Broadcast_Common9"
  stopEvent3p: "Stop_LetsChase_VO_DarkStar_Broadcast_Common9"
  is3d: 1
}
rows {
  id: 54460
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common1"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common1"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common1"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common1"
  is3d: 1
}
rows {
  id: 54461
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common3"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common3"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common3"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common3"
  is3d: 1
}
rows {
  id: 54462
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common4"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common4"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common4"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common4"
  is3d: 1
}
rows {
  id: 54463
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common5"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common5"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common5"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common5"
  is3d: 1
}
rows {
  id: 54464
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common6"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common6"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common6"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common6"
  is3d: 1
}
rows {
  id: 54465
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common7"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common7"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common7"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common7"
  is3d: 1
}
rows {
  id: 54466
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common8"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common8"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common8"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common8"
  is3d: 1
}
rows {
  id: 54467
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common9"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common9"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common9"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common9"
  is3d: 1
}
rows {
  id: 54468
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common10"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common10"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common10"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common10"
  is3d: 1
}
rows {
  id: 54469
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common11"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common11"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common11"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common11"
  is3d: 1
}
rows {
  id: 54470
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common12"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common12"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common12"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common12"
  is3d: 1
}
rows {
  id: 54471
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common13"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common13"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common13"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common13"
  is3d: 1
}
rows {
  id: 54472
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common14"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common14"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common14"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common14"
  is3d: 1
}
rows {
  id: 54473
  playEvent: "Play_LetsChase_VO_StarBaby_Broadcast_Common15"
  playEvent3p: "Play_LetsChase_VO_StarBaby_Broadcast_Common15"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_StarBaby_Broadcast_Common15"
  stopEvent3p: "Stop_LetsChase_VO_StarBaby_Broadcast_Common15"
  is3d: 1
}
rows {
  id: 54551
  playEvent: "Play_LetsChase_VO_Egg_Dracula_YYC_Coffin_01"
  playEvent3p: "Play_LetsChase_VO_Egg_Dracula_YYC_Coffin_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_Egg_Dracula_YYC_Coffin_01"
  stopEvent3p: "Stop_LetsChase_VO_Egg_Dracula_YYC_Coffin_01"
  is3d: 1
}
rows {
  id: 54552
  playEvent: "Play_LetsChase_VO_Egg_Elizabeth_YYC_Coffin_01"
  playEvent3p: "Play_LetsChase_VO_Egg_Elizabeth_YYC_Coffin_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_Egg_Elizabeth_YYC_Coffin_01"
  stopEvent3p: "Stop_LetsChase_VO_Egg_Elizabeth_YYC_Coffin_01"
  is3d: 1
}
rows {
  id: 54553
  playEvent: "Play_LetsChase_VO_Egg_Lamia_YYC_Coffin_01"
  playEvent3p: "Play_LetsChase_VO_Egg_Lamia_YYC_Coffin_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_Egg_Lamia_YYC_Coffin_01"
  stopEvent3p: "Stop_LetsChase_VO_Egg_Lamia_YYC_Coffin_01"
  is3d: 1
}
rows {
  id: 54554
  playEvent: "Play_LetsChase_VO_Egg_Scarecrow_YYC_Maze_01"
  playEvent3p: "Play_LetsChase_VO_Egg_Scarecrow_YYC_Maze_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_Egg_Scarecrow_YYC_Maze_01"
  stopEvent3p: "Stop_LetsChase_VO_Egg_Scarecrow_YYC_Maze_01"
  is3d: 1
}
rows {
  id: 54555
  playEvent: "Play_LetsChase_VO_Egg_YaoDao_SWL_Table_01"
  playEvent3p: "Play_LetsChase_VO_Egg_YaoDao_SWL_Table_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_Egg_YaoDao_SWL_Table_01"
  stopEvent3p: "Stop_LetsChase_VO_Egg_YaoDao_SWL_Table_01"
  is3d: 1
}
rows {
  id: 54556
}
rows {
  id: 54557
}
rows {
  id: 54558
}
rows {
  id: 54559
}
rows {
  id: 54560
}
rows {
  id: 54561
}
rows {
  id: 54562
}
rows {
  id: 54563
}
rows {
  id: 54564
}
rows {
  id: 54601
  playEvent: "Play_LetsChase_VO_CP_Dracula_DraculaElizabeth_01"
  playEvent3p: "Play_LetsChase_VO_CP_Dracula_DraculaElizabeth_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_CP_Dracula_DraculaElizabeth_01"
  stopEvent3p: "Stop_LetsChase_VO_CP_Dracula_DraculaElizabeth_01"
  is3d: 1
}
rows {
  id: 54602
  playEvent: "Play_LetsChase_VO_CP_Dracula_LamiaDracula_01"
  playEvent3p: "Play_LetsChase_VO_CP_Dracula_LamiaDracula_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_CP_Dracula_LamiaDracula_01"
  stopEvent3p: "Stop_LetsChase_VO_CP_Dracula_LamiaDracula_01"
  is3d: 1
}
rows {
  id: 54603
  playEvent: "Play_LetsChase_VO_CP_Dracula_LamiaDracula_03"
  playEvent3p: "Play_LetsChase_VO_CP_Dracula_LamiaDracula_03"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_CP_Dracula_LamiaDracula_03"
  stopEvent3p: "Stop_LetsChase_VO_CP_Dracula_LamiaDracula_03"
  is3d: 1
}
rows {
  id: 54604
  playEvent: "Play_LetsChase_VO_CP_Elizabeth_DraculaElizabeth_02"
  playEvent3p: "Play_LetsChase_VO_CP_Elizabeth_DraculaElizabeth_02"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_CP_Elizabeth_DraculaElizabeth_02"
  stopEvent3p: "Stop_LetsChase_VO_CP_Elizabeth_DraculaElizabeth_02"
  is3d: 1
}
rows {
  id: 54605
  playEvent: "Play_LetsChase_VO_CP_Elizabeth_LamiaElizabeth_02"
  playEvent3p: "Play_LetsChase_VO_CP_Elizabeth_LamiaElizabeth_02"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_CP_Elizabeth_LamiaElizabeth_02"
  stopEvent3p: "Stop_LetsChase_VO_CP_Elizabeth_LamiaElizabeth_02"
  is3d: 1
}
rows {
  id: 54606
  playEvent: "Play_LetsChase_VO_CP_Lamia_LamiaDracula_02"
  playEvent3p: "Play_LetsChase_VO_CP_Lamia_LamiaDracula_02"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_CP_Lamia_LamiaDracula_02"
  stopEvent3p: "Stop_LetsChase_VO_CP_Lamia_LamiaDracula_02"
  is3d: 1
}
rows {
  id: 54607
  playEvent: "Play_LetsChase_VO_CP_Lamia_LamiaElizabeth_01"
  playEvent3p: "Play_LetsChase_VO_CP_Lamia_LamiaElizabeth_01"
  bank: "LetsChase_VO"
  stopEvent: "Stop_LetsChase_VO_CP_Lamia_LamiaElizabeth_01"
  stopEvent3p: "Stop_LetsChase_VO_CP_Lamia_LamiaElizabeth_01"
  is3d: 1
}
rows {
  id: 54608
}
rows {
  id: 54609
}
rows {
  id: 54610
}
rows {
  id: 94022
  playEvent: "Play_LetsFarm_Cook_NPC_Guest_Generic"
  playEvent3p: "Play_LetsFarm_Cook_NPC_Guest_Generic"
  bank: "LetsFarm_Cook"
  is3d: 1
}
rows {
  id: 94023
  playEvent: "Play_LetsFarm_Cook_NPC_Guest_Golden"
  playEvent3p: "Play_LetsFarm_Cook_NPC_Guest_Golden"
  bank: "LetsFarm_Cook"
  is3d: 1
}
rows {
  id: 94024
  playEvent: "Play_LetsFarm_Cook_NPC_Guest_Color"
  playEvent3p: "Play_LetsFarm_Cook_NPC_Guest_Color"
  bank: "LetsFarm_Cook"
  is3d: 1
}
