com.tencent.wea.xlsRes.table_ChatMsgTypeConfigData
excel/xls/L_聊天消息类型.xlsx sheet:聊天消息类型配置
rows {
  id: 1
  chatMsgName: "CMT_Text"
  chatCellMySelf: "UI_NewChat_ChatCell_Text_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Text_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "普通文本"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 2
  chatMsgName: "CMT_Voice"
  chatCellMySelf: "UI_NewChat_ChatCell_Voice_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Voice_Other"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_Voice_MySelf"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_Voice_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Voice"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "语音消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 3
  chatMsgName: "CMT_Emoji"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "只带有普通表情"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 4
  chatMsgName: "CMT_PaidEmoji"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "带有付费表情"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 5
  chatMsgName: "CMT_System"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "系统消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 6
  chatMsgName: "CMT_TeamRecruit"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "组队招募信息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 7
  chatMsgName: "CMT_SystemJoinTeam"
  chatCellMySelf: "UI_NewChat_ChatCell_Team"
  chatCellOther: "UI_NewChat_ChatCell_Team"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_Team"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_Team"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_SystemJoinTeam"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "加入队伍系统通知"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 8
  chatMsgName: "CMT_SystemJoinScene"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "加入场景系统通知"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 9
  chatMsgName: "CMT_PositionShare"
  chatCellMySelf: "UI_NewChat_ChatCell_Position_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Position_Other"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_Position_MySelf"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_Position_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Position"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyPositionShare"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_LobbyPositionShareNoSender"
  desc: "位置分享"
  lobbyBottomKeyDefineOther: "NewChat_LobbyPositionShare"
  privateTopKeyDefineOther: "NewChat_LobbyPositionShareNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 10
  chatMsgName: "CMT_UgcMapShare"
  chatCellMySelf: "UI_NewChat_ChatCell_UGCShare_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_UGCShare_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_UgcMapShare"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "ugc地图分享"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 11
  chatMsgName: "CMT_TemplateText"
  chatCellMySelf: "UI_NewChat_ChatCell_Text_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Text_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "模板文字"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 12
  chatMsgName: "CMT_AIMessage"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "AI聊天"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 13
  chatMsgName: "CMT_SystemJoinClub"
  chatCellMySelf: "UI_NewChat_ChatCell_Club"
  chatCellOther: "UI_NewChat_ChatCell_Club"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_SystemJoinClub"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "加入社团系统通知"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 14
  chatMsgName: "CMT_ClubShare"
  chatCellMySelf: "UI_NewChat_ChatCell_Club_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Club_Other"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_Club_MySelf"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_Club_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_ClubShare"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了社团]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyClubShare"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_LobbyClubShareNoSender"
  desc: "社团分享消息"
  privateLatestKeyDefineOther: "[分享了社团]"
  lobbyBottomKeyDefineOther: "NewChat_LobbyClubShare"
  privateTopKeyDefineOther: "NewChat_LobbyClubShareNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 15
  chatMsgName: "CMT_LobbyInteractionSystem"
  chatCellMySelf: "UI_NewChat_ChatCell_System"
  chatCellOther: "UI_NewChat_ChatCell_System"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_System"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_System"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_System"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "大厅互动系统消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 16
  chatMsgName: "CMT_Top_LobbyInteractionSystem"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "大厅互动置顶系统消息"
  canAppendChatCellUmg: false
  addChatRedDots: 0
  ignoreCreditScore: 1
}
rows {
  id: 17
  chatMsgName: "CMT_Spine_Emoji"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "聊天Spine动态表情消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 18
  chatMsgName: "CMT_RedPacketSend"
  chatCellMySelf: "UI_NewChat_ChatCell_SpringRedPacket_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_SpringRedPacket_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_RedPacketSend"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "NewChat_LobbyRedPacketSendNoSender"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyRedPacketSend"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_LobbyRedPacketSendNoSender"
  desc: "红包发送消息"
  privateLatestKeyDefineOther: "NewChat_LobbyRedPacketSendNoSender"
  lobbyBottomKeyDefineOther: "NewChat_LobbyRedPacketSend"
  privateTopKeyDefineOther: "NewChat_LobbyRedPacketSendNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 19
  chatMsgName: "CMT_RedPacketReceive"
  chatCellMySelf: "UI_NewChat_ChatCell_SpringRedPacketHint"
  chatCellOther: "UI_NewChat_ChatCell_SpringRedPacketHint"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_RedPacketReceive"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyRedPacketReceive"
  isShowOnPrivateTop: true
  desc: "红包接收消息"
  lobbyBottomKeyDefineOther: "NewChat_LobbyRedPacketReceive"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 20
  chatMsgName: "CMT_Share_BaseInfo"
  chatCellMySelf: "UI_NewChat_ChatCell_BaseInfo_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_BaseInfo_Other"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_BaseInfo_MySelf"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_BaseInfo_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_BaseInfo"
  lobbyLeftKeyDefine: "[分享了个人资料]"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了个人资料]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyShareBaseInfo"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_LobbyShareBaseInfoNoSender"
  desc: "个人信息"
  lobbyLeftKeyDefineOther: "[分享了Ta的个人资料]"
  privateLatestKeyDefineOther: "[分享了Ta的个人资料]"
  lobbyBottomKeyDefineOther: "NewChat_LobbyShareBaseInfo"
  privateTopKeyDefineOther: "NewChat_LobbyShareBaseInfoNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 21
  chatMsgName: "CMT_Share_JoinTeam"
  chatCellMySelf: "UI_NewChat_ChatCell_JoinTeam_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_JoinTeam_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_JoinTeam"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了队伍]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyShareJoinTeam"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_LobbyShareJoinTeamNoSender"
  desc: "加入队伍"
  privateLatestKeyDefineOther: "[分享了Ta的队伍]"
  lobbyBottomKeyDefineOther: "NewChat_LobbyShareJoinTeam"
  privateTopKeyDefineOther: "NewChat_LobbyShareJoinTeamNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 22
  chatMsgName: "CMT_Share_JoinRoom"
  chatCellMySelf: "UI_NewChat_ChatCell_JoinRoom_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_JoinRoom_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_JoinRoom"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了房间]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyShareJoinRoom"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_LobbyShareJoinRoomNoSender"
  desc: "加入房间"
  privateLatestKeyDefineOther: "[分享了房间]"
  lobbyBottomKeyDefineOther: "NewChat_LobbyShareJoinRoom"
  privateTopKeyDefineOther: "NewChat_LobbyShareJoinRoomNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 23
  chatMsgName: "CMT_SystemClubModifyBrief"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "系统消息：社团宣言修改"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 24
  chatMsgName: "CMT_UltramanInvite"
  chatCellMySelf: "UI_NewChat_ChatCell_Ultraman_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Ultraman_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Ultraman"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_LobbyShareJoinTeam"
  isShowOnPrivateTop: true
  desc: "奥特曼邀请"
  lobbyBottomKeyDefineOther: "NewChat_LobbyShareJoinTeam"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 25
  chatMsgName: "CMT_SystemClubAddManager"
  chatCellMySelf: "UI_NewChat_ChatCell_ClubAddManager"
  chatCellOther: "UI_NewChat_ChatCell_ClubAddManager"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_SystemClubAddManager"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "添加管理员"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 26
  chatMsgName: "CMT_SystemClubDelManager"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "删除管理员"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 27
  chatMsgName: "CMT_SystemClubNewOwner"
  chatCellMySelf: "UI_NewChat_ChatCell_ClubNewOwner"
  chatCellOther: "UI_NewChat_ChatCell_ClubNewOwner"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_ClubNewOwner"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_ClubNewOwner"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_SystemClubNewOwner"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "更换团长"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 28
  chatMsgName: "CMT_SystemQuiteGame"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "系统消息退出游戏"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 29
  chatMsgName: "CMT_UgcMapCollectionShare"
  chatCellMySelf: "UI_NewChat_ChatCell_MapCollection_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_MapCollection_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了地图]"
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "ugc地图合集分享"
  privateLatestKeyDefineOther: "[分享了地图]"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 30
  chatMsgName: "CMT_TeamBattleBroadcast"
  chatCellMySelf: "UI_NewChat_ChatCell_System"
  chatCellOther: "UI_NewChat_ChatCell_System"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_System"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "队伍战斗播报"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 31
  chatMsgName: "CMT_SuperCore"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "超核聊天"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 32
  chatMsgName: "CMT_ClubGroupInvite"
  chatCellMySelf: "UI_NewChat_ChatCell_Club_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Club_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_ClubShare"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "Clup_NoticeJoinGroup"
  isShowOnPrivateTop: true
  desc: "社团群聊邀请"
  lobbyBottomKeyDefineOther: "Clup_NoticeJoinGroup"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 33
  chatMsgName: "CMT_ActivityJoinTeam"
  chatCellMySelf: "UI_NewChat_ChatCell_ActivityJoinTeam_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_ActivityJoinTeam_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了星宝小队]"
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "活动小队加入消息"
  privateLatestKeyDefineOther: "[分享了星宝小队]"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 34
  chatMsgName: "CMT_ClubNotifyManagerJoinRank"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "提醒团长加入排行榜，改用CMT_JoinClubRankNotice"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 35
  chatMsgName: "CMT_PlayProposal"
  chatCellMySelf: "UI_NewChat_ChatCell_WantToPlay_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_WantToPlay_Other"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_WantToPlay_MySelf"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_WantToPlay_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "玩法提议消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 1
}
rows {
  id: 36
  chatMsgName: "CMT_Screenshot"
  chatCellMySelf: "UI_NewChat_ChatCell_ShareImage_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_ShareImage_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  lobbyLeftKeyDefine: "NewChat_ClubScreenShare_LobbyLeft"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "NewChat_ClubScreenShare_LobbyLeft"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_ClubScreenShare"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_ClubScreenShare_LobbyLeft"
  desc: "截图消息"
  lobbyLeftKeyDefineOther: "NewChat_ClubScreenShare_LobbyLeft"
  privateLatestKeyDefineOther: "NewChat_ClubScreenShare_LobbyLeft"
  lobbyBottomKeyDefineOther: "NewChat_ClubScreenShare"
  privateTopKeyDefineOther: "NewChat_ClubScreenShare_LobbyLeft"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 37
  chatMsgName: "CMT_AddPlayerHello"
  chatCellMySelf: "UI_NewChat_ChatCell_NewFriend"
  chatCellOther: "UI_NewChat_ChatCell_NewFriend"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "初次加好友招呼信息"
  canAppendChatCellUmg: true
  addChatRedDots: 0
  ignoreCreditScore: 0
}
rows {
  id: 38
  chatMsgName: "CMT_IntimacyLevelNtf"
  chatCellMySelf: "UI_NewChat_ChatCell_RelationUpToValue"
  chatCellOther: "UI_NewChat_ChatCell_RelationUpToValue"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_RelationUpToValue"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_RelationUpToValue"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "亲密度等级提示"
  canAppendChatCellUmg: true
  addChatRedDots: 0
  ignoreCreditScore: 0
}
rows {
  id: 39
  chatMsgName: "CMT_ClubWeekSettle"
  chatCellMySelf: "UI_NewChat_ChatCell_ClubRankShare_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_ClubRankShare_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  lobbyLeftKeyDefine: "NewChat_ClubWeekSettle_LobbyLeft"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "NewChat_ClubWeekSettle_LobbyLeft"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_Vertical_ClubWeekSettle_Bottom"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_ClubWeekSettle_LobbyLeft"
  desc: "社团周结算"
  lobbyLeftKeyDefineOther: "NewChat_ClubWeekSettle_LobbyLeft"
  privateLatestKeyDefineOther: "NewChat_ClubWeekSettle_LobbyLeft"
  lobbyBottomKeyDefineOther: "NewChat_Vertical_ClubWeekSettle_Bottom"
  privateTopKeyDefineOther: "NewChat_ClubWeekSettle_LobbyLeft"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 40
  chatMsgName: "CMT_JoinClubRankNotice"
  chatCellMySelf: "UI_NewChat_ChatCell_ClubRankText_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_ClubRankText_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  lobbyLeftKeyDefine: "NewChat_JoinClubRankNotice_LobbyLeft"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_Vertical_JoinClubRankNotice_Bottom"
  isShowOnPrivateTop: true
  desc: "参加社团排行提醒"
  lobbyLeftKeyDefineOther: "NewChat_JoinClubRankNotice_LobbyLeft"
  lobbyBottomKeyDefineOther: "NewChat_Vertical_JoinClubRankNotice_Bottom"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 41
  chatMsgName: "CMT_SystemJoinClubRank"
  chatCellMySelf: "UI_NewChat_ChatCell_Club"
  chatCellOther: "UI_NewChat_ChatCell_Club"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_SystemJoinClub"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "加入社团排行系统消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 42
  chatMsgName: "CMT_JoinClubRank"
  chatCellMySelf: "UI_NewChat_ChatCell_ClubRankText_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_ClubRankText_Other"
  isShowOnLobbyLeft: true
  lobbyLeftKeyDefine: "NewChat_JoinClubRank_LobbyLeft"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_Vertical_JoinClubRank_Bottom"
  isShowOnPrivateTop: true
  desc: "加入社团排行结构化消息"
  lobbyLeftKeyDefineOther: "NewChat_JoinClubRank_LobbyLeft"
  lobbyBottomKeyDefineOther: "NewChat_Vertical_JoinClubRank_Bottom"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 43
  chatMsgName: "CMT_IPEmoji"
  chatCellMySelf: "UI_NewChat_ChatCell_Emoj_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Emoj_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "聊天动态表情消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 44
  chatMsgName: "CMT_GroupBuying"
  chatCellMySelf: "UI_Activity_GroupReturning_ChatMySelf"
  chatCellOther: "UI_Activity_GroupReturning_ChatOther"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_JoinTeam"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了拼团]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "Activity_GroupReturning_Chat_JoinWithSender"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "Activity_GroupReturning_Chat_JoinNoSender"
  desc: "加入拼团消费活动队伍消息"
  privateLatestKeyDefineOther: "[分享了Ta的拼团]"
  lobbyBottomKeyDefineOther: "Activity_GroupReturning_Chat_JoinWithSender"
  privateTopKeyDefineOther: "Activity_GroupReturning_Chat_JoinNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 45
  chatMsgName: "CMT_LuckyFriend"
  chatCellMySelf: "UI_NewChat_LuckyFriend_Text_MySelf"
  chatCellOther: "UI_NewChat_LuckyFriend_Text_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "亲密关系消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 46
  chatMsgName: "CMT_ReputationScore"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "信誉分违规行为消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 47
  chatMsgName: "CMT_ReputationScoreTeammate"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "信誉分违规行为队友消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 48
  chatMsgName: "CMT_SquadActivityInvitation"
  chatCellMySelf: "UI_NewChat_ChatCell_ActivityEasyTeamJoin_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_ActivityEasyTeamJoin_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了组队减负小队]"
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "小队活动邀请消息"
  privateLatestKeyDefineOther: "[分享了组队减负小队]"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 49
  chatMsgName: "CMT_WolfKillSquadActivityInvitation"
  chatCellMySelf: "UI_WolfKillTeam_ChatMySelf"
  chatCellOther: "UI_WolfKillTeam_ChatOther"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_JoinTeam"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了队伍]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "Activity_WolfKillTeam_Chat_JoinWithSender"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "Activity_WolfKillTeam_Chat_JoinNoSender"
  desc: "狼人杀小队活动邀请消息"
  privateLatestKeyDefineOther: "[分享了Ta的队伍]"
  lobbyBottomKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinWithSender"
  privateTopKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 50
  chatMsgName: "CMT_ArenaWeeklyActivityInvitation"
  chatCellMySelf: "UI_CommonView_Invite_ChatMySelf"
  chatCellOther: "UI_CommonView_Invite_ChatOther"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_JoinTeam"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了队伍]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "Activity_WolfKillTeam_Chat_JoinWithSender"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "Activity_WolfKillTeam_Chat_JoinNoSender"
  desc: "周五组队开黑邀请消息"
  privateLatestKeyDefineOther: "[分享了Ta的队伍]"
  lobbyBottomKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinWithSender"
  privateTopKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 51
  chatMsgName: "CMT_SurpassFriend"
  chatCellMySelf: "UI_NewChat_ChatCell_SurpassFriend_Self"
  chatCellOther: "UI_NewChat_ChatCell_SurpassFriend_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  lobbyLeftKeyDefine: "NewChat_SurpassFriend"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "NewChat_SurpassFriend"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_SurpassFriend"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_SurpassFriend"
  desc: "超越好友"
  lobbyLeftKeyDefineOther: "NewChat_SurpassFriend"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 52
  chatMsgName: "CMT_TradingCardTradeInfo"
  chatCellMySelf: "UI_NewChat_ChatCell_GiveCards_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_GiveCards_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了卡牌]"
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "集卡交易的数据"
  privateLatestKeyDefineOther: "[分享了Ta的卡牌]"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 53
  chatMsgName: "CMT_FarmBlessingTeamActivityInvitation"
  chatCellMySelf: "UI_FarmBlessingTeam_ChatMySelf"
  chatCellOther: "UI_FarmBlessingTeam_ChatOther"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_JoinTeam"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "UI_FarmBlessingTeam_ShareChatDesc"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "Activity_WolfKillTeam_Chat_JoinWithSender"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "Activity_WolfKillTeam_Chat_JoinNoSender"
  desc: "农场祈福小队邀请消息"
  privateLatestKeyDefineOther: "UI_FarmBlessingTeam_ShareChatDesc"
  lobbyBottomKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinWithSender"
  privateTopKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 54
  chatMsgName: "CMT_ShareGift"
  chatCellMySelf: "UI_NewChat_ChatCell_GiftSharing_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_GiftSharing_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_Text"
  lobbyLeftKeyDefine: "NewChat_ShareGift_ShowFormat"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "NewChat_ShareGift_ShowFormat"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_ShareGift_ShowFormat"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_ShareGift_ShowFormat"
  desc: "分享礼包"
  lobbyLeftKeyDefineOther: "NewChat_ShareGift_ShowFormat"
  privateLatestKeyDefineOther: "NewChat_ShareGift_ShowFormat"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 55
  chatMsgName: "CMT_BirthdayFriendRemind"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "好友生日提醒结构化消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 56
  chatMsgName: "CMT_BirthdayCardSend"
  chatCellMySelf: "UI_NewChat_ChatCell_Birthday_Card_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_Birthday_Card_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "好友生日贺卡赠送结构化消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 57
  chatMsgName: "CMT_DoubleTeamActivityInvitation"
  chatCellMySelf: "UI_NewChat_ChatCell_DoubleTeam_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_DoubleTeam_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_JoinTeam"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了队伍]"
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "Activity_WolfKillTeam_Chat_JoinWithSender"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "Activity_WolfKillTeam_Chat_JoinNoSender"
  desc: "双人组队活动结构化消息"
  privateLatestKeyDefineOther: "[分享了Ta的队伍]"
  lobbyBottomKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinWithSender"
  privateTopKeyDefineOther: "Activity_WolfKillTeam_Chat_JoinNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 58
  chatMsgName: "CMT_ShareGiftMsg"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "双人组队活动结构化消息--废弃，重复定义，请使用CMT_ShareGift"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 59
  chatMsgName: "CMT_FarmBuffWishSupport"
  chatCellMySelf: "UI_NewChat_ChatCell_FarmReturn_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_FarmReturn_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "农场回流助力增益"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 60
  chatMsgName: "CMT_PhotoAlbumRemind"
  chatCellMySelf: "UI_NewChat_ChatCell_AlbumRemind_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_AlbumRemind_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "个人相册提醒消息"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 61
  chatMsgName: "CMT_PositionShare_MusicConcert"
  chatCellMySelf: "UI_NewChat_ChatCell_MusicConcertPosition_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_MusicConcertPosition_Other"
  chatCellVerticalMySelf: "UI_VerticalChat_ChatCell_MusicConcertPosition_MySelf"
  chatCellVerticalOther: "UI_VerticalChat_ChatCell_MusicConcertPosition_Other"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_MusicConcertChatCell_Position"
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  lobbyBottomKeyDefine: "NewChat_MusicConcertPositionShare"
  isShowOnPrivateTop: true
  privateTopKeyDefine: "NewChat_MusicConcertPositionShareNoSender"
  desc: "舞台-位置分享"
  lobbyBottomKeyDefineOther: "NewChat_MusicConcertPositionShare"
  privateTopKeyDefineOther: "NewChat_MusicConcertPositionShareNoSender"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
rows {
  id: 62
  chatMsgName: "CMT_WerewolfRecall"
  chatCellMySelf: "UI_NewChat_ChatCell_WerewolfRecall_MySelf"
  chatCellOther: "UI_NewChat_ChatCell_WerewolfRecall_Other"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "狼人召回"
  canAppendChatCellUmg: true
  ignoreCreditScore: 0
}
rows {
  id: 63
  chatMsgName: "CMT_InflateRedPacket"
  chatCellMySelf: "UI_InflateHongBao_ChatMySelf"
  chatCellOther: "UI_InflateHongBao_ChatOther"
  isShowOnLobbyLeft: true
  lobbyLeftUMG: "UI_Lobby_ChatCell_ClubShare"
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[分享了队伍]"
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "膨胀爆红包"
  privateLatestKeyDefineOther: "[分享了Ta的队伍]"
  canAppendChatCellUmg: true
  ignoreCreditScore: 0
}
rows {
  id: 68
  chatMsgName: "CMT_AINPCImage"
  chatCellMySelf: "UI_NewChat_ChatCell_AINPC_Picture"
  chatCellOther: "UI_NewChat_ChatCell_AINPC_Picture"
  chatCellVerticalMySelf: "UI_NewChat_ChatCell_AINPC_Picture"
  chatCellVerticalOther: "UI_NewChat_ChatCell_AINPC_Picture"
  isShowOnLobbyLeft: true
  isShowOnPrivateLatest: true
  privateLatestKeyDefine: "[图片消息]"
  isShowOnLobbyBottom: true
  isShowOnPrivateTop: true
  desc: "学霸帮帮图片"
  privateLatestKeyDefineOther: "[图片消息]"
  canAppendChatCellUmg: true
  addChatRedDots: 1
  ignoreCreditScore: 0
}
