com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_yujun.xlsx sheet:活动-紫色卡池
rows {
  raffleId: 5124
  name: "百变小新"
  startTime {
    seconds: 1724342400
  }
  endTime {
    seconds: 1726415999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51240
    subPoolIds: 51241
    subPoolIds: 51242
    subPoolIds: 51243
    subPoolIds: 51244
  }
  dailyLimit: 28
  maxLimit: 28
  textRuleId: 216
  text: "抽到钥匙自动进内圈抽奖，祈愿1轮时提前获得钥匙会返还幸运币"
  raffleTagName: "睡衣小新"
  raffleTagIcon: "T_XiaoxinPhase3_Icon_XiaoxinHead1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1724342400
  }
  showEndTime {
    seconds: 1726415999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 1
  gotGrandRule: 1
}
rows {
  raffleId: 5125
  name: "百变小新"
  startTime {
    seconds: 1724342400
  }
  endTime {
    seconds: 1726415999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51250
    subPoolIds: 51251
    subPoolIds: 51252
    subPoolIds: 51253
    subPoolIds: 51254
  }
  dailyLimit: 28
  maxLimit: 28
  textRuleId: 217
  text: "抽到钥匙自动进内圈抽奖，祈愿1轮时提前获得钥匙会返还幸运币"
  raffleTagName: "左卫门 小新"
  raffleTagIcon: "T_XiaoxinPhase3_Icon_XiaoxinHead2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1724342400
  }
  showEndTime {
    seconds: 1726415999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 1
  gotGrandRule: 1
}
rows {
  raffleId: 5126
  name: "盛装小新"
  startTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1746719999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51260
    subPoolIds: 51261
    subPoolIds: 51262
    subPoolIds: 51263
    subPoolIds: 51264
  }
  dailyLimit: 28
  maxLimit: 28
  textRuleId: 119
  text: "抽到钥匙自动进内圈抽奖，祈愿1轮时提前获得钥匙会返还幸运币"
  activityImage: "Xin_Lobster"
  raffleTagName: "超级龙虾"
  raffleTagIcon: "T_DrawReward_ShinChan_Head_lobster"
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1746719999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 1
  gotGrandRule: 1
}
rows {
  raffleId: 5127
  name: "盛装小新"
  startTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1746719999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51270
    subPoolIds: 51271
    subPoolIds: 51272
    subPoolIds: 51273
    subPoolIds: 51274
  }
  dailyLimit: 28
  maxLimit: 28
  textRuleId: 120
  text: "抽到钥匙自动进内圈抽奖，祈愿1轮时提前获得钥匙会返还幸运币"
  activityImage: "Xin_Superman"
  raffleTagName: "动感超人"
  raffleTagIcon: "T_DrawReward_ShinChan_Head_Super"
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1746719999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 1
  gotGrandRule: 1
}
rows {
  raffleId: 5128
  name: "百变小新"
  startTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1746719999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51280
    subPoolIds: 51281
    subPoolIds: 51282
    subPoolIds: 51283
    subPoolIds: 51284
  }
  dailyLimit: 28
  maxLimit: 28
  textRuleId: 216
  text: "抽到钥匙自动进内圈抽奖，祈愿1轮时提前获得钥匙会返还幸运币"
  raffleTagName: "睡衣小新"
  raffleTagIcon: "T_XiaoxinPhase3_Icon_XiaoxinHead1"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1746719999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 1
  gotGrandRule: 1
}
rows {
  raffleId: 5129
  name: "百变小新"
  startTime {
    seconds: 1744905600
  }
  endTime {
    seconds: 1746719999
  }
  poolSelection {
    policy: RPP_OnSubGrand
    poolId: 51290
    subPoolIds: 51291
    subPoolIds: 51292
    subPoolIds: 51293
    subPoolIds: 51294
  }
  dailyLimit: 28
  maxLimit: 28
  textRuleId: 217
  text: "抽到钥匙自动进内圈抽奖，祈愿1轮时提前获得钥匙会返还幸运币"
  raffleTagName: "左卫门 小新"
  raffleTagIcon: "T_XiaoxinPhase3_Icon_XiaoxinHead2"
  viewIndex: 1
  viewIndex: 2
  viewIndex: 3
  viewIndex: 4
  viewIndex: 5
  viewIndex: 6
  viewIndex: 7
  showStartTime {
    seconds: 1744905600
  }
  showEndTime {
    seconds: 1746719999
  }
  isShow: true
  showRule: 1
  advanceItemAnimType: 1
  gotGrandRule: 1
}
