com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_yujun.xlsx sheet:活动-载具卡池
rows {
  raffleId: 80001
  name: "云梦绮旅"
  startTime {
    seconds: 1735660800
  }
  endTime {
    seconds: 1750953599
  }
  poolSelection {
    policy: RPP_ClimbPagoda
    poolId: 8000101
    subPoolIds: 8000101
    subPoolIds: 8000102
    subPoolIds: 8000104
    subPoolIds: 8000105
    subPoolIds: 8000106
    subPoolIds: 8000107
    subPoolIds: 8000108
    subPoolIds: 8000109
    subPoolIds: 8000110
    climbPoolIds: 8000101
    climbPoolIds: 8000102
    pagodaPoolIds {
      key: 1
      value: 8000104
    }
    pagodaPoolIds {
      key: 2
      value: 8000105
    }
    pagodaPoolIds {
      key: 3
      value: 8000106
    }
    pagodaPoolIds {
      key: 4
      value: 8000107
    }
    pagodaPoolIds {
      key: 5
      value: 8000108
    }
    pagodaPoolIds {
      key: 6
      value: 8000109
    }
    pagodaPoolIds {
      key: 7
      value: 8000110
    }
  }
  dailyLimit: 30
  maxLimit: 999999
  textRuleId: 304
  lowestVersion: "1.3.37.87"
  extraShow {
    turntableParams {
      key: "1"
      value: "cat"
    }
    turntableParams {
      key: "2"
      value: "dog"
    }
  }
  milestone {
    atPoints: 1
    atPoints: 2
    atPoints: 3
    atPoints: 4
    atPoints: 5
    atPoints: 6
    atPoints: 7
    pointAwardType: RAT_ClimbPagoda
    pagodaManualPityItems {
      itemId: 224
      itemNum: 5
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 8
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 10
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 20
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 30
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 50
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 50
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 2
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 8
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 20
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 40
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 80
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 150
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 250
    }
  }
  showStartTime {
    seconds: 1735660800
  }
  showEndTime {
    seconds: 1750953599
  }
  isShow: true
  tagDailyLimit: 500
}
rows {
  raffleId: 80006
  name: "九尾狐测试"
  startTime {
    seconds: 1735660800
  }
  endTime {
    seconds: 1753545599
  }
  poolSelection {
    policy: RPP_ClimbPagoda
    poolId: 8000601
    subPoolIds: 8000601
    subPoolIds: 8000602
    subPoolIds: 8000604
    subPoolIds: 8000605
    subPoolIds: 8000606
    subPoolIds: 8000607
    subPoolIds: 8000608
    subPoolIds: 8000609
    subPoolIds: 8000610
    climbPoolIds: 8000601
    climbPoolIds: 8000602
    pagodaPoolIds {
      key: 1
      value: 8000604
    }
    pagodaPoolIds {
      key: 2
      value: 8000605
    }
    pagodaPoolIds {
      key: 3
      value: 8000606
    }
    pagodaPoolIds {
      key: 4
      value: 8000607
    }
    pagodaPoolIds {
      key: 5
      value: 8000608
    }
    pagodaPoolIds {
      key: 6
      value: 8000609
    }
    pagodaPoolIds {
      key: 7
      value: 8000610
    }
    climbSurpriseLimit: 3
    climbSurpriseWeights {
      weights {
        key: 4
        value: 80
      }
      weights {
        key: 5
        value: 80
      }
      weights {
        key: 6
        value: 50
      }
      weights {
        key: 4
        value: 80
      }
      weights {
        key: 5
        value: 100
      }
      weights {
        key: 6
        value: 50
      }
      weights {
        key: 3
        value: 80
      }
      weights {
        key: 4
        value: 80
      }
      weights {
        key: 5
        value: 50
      }
      weights {
        key: 3
        value: 80
      }
      weights {
        key: 4
        value: 40
      }
      weights {
        key: 5
        value: 20
      }
    }
  }
  dailyLimit: 30
  maxLimit: 999999
  textRuleId: 304
  lowestVersion: "1.3.37.87"
  extraShow {
    turntableParams {
      key: "1"
      value: "cat"
    }
    turntableParams {
      key: "2"
      value: "dog"
    }
  }
  milestone {
    atPoints: 1
    atPoints: 2
    atPoints: 3
    atPoints: 4
    atPoints: 5
    atPoints: 6
    atPoints: 7
    pointAwardType: RAT_ClimbPagoda
    pagodaManualPityItems {
      itemId: 224
      itemNum: 5
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 8
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 10
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 20
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 30
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 50
    }
    pagodaManualPityItems {
      itemId: 224
      itemNum: 50
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 2
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 8
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 20
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 40
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 80
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 150
    }
    pagodaFailurePityItems {
      itemId: 224
      itemNum: 250
    }
  }
  showStartTime {
    seconds: 1735660800
  }
  showEndTime {
    seconds: 1753545599
  }
  isShow: true
  tagDailyLimit: 500
}
