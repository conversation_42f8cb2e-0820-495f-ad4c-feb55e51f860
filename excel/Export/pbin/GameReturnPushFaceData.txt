com.tencent.wea.xlsRes.table_GameReturnPushFaceData
excel/xls/W_玩法回流.xlsx sheet:拍脸配置
rows {
  id: 1
  LeftTitleName: "天天晋级赛"
  IsShowSeeDetails: 1
  RightTitleText: "好久不见，曾经的<RegressionItem>{0}</>，为你准备了一份<RegressionItem>回归大礼</>！"
  RewardIdList: 203002
  RewardIdList: 3610
  RewardIdList: 340006
  RewardNumList: 3
  RewardNumList: 1000
  RewardNumList: 1
  IsBigRewardList: 0
  IsBigRewardList: 0
  IsBigRewardList: 1
  ReturnDownBtnText: "立即开启"
  ReturnGameModeBg: "T_ModelSelectLarge_Img_Type_01_S11_Rec"
  RulesId: 214
  PushDownBtnText1: "开心领奖"
  PushDownBtnText2: "领奖并开局"
  PushDownBtnText3: "知道了"
  PushDownBtnText4: "立即开局"
  PushFaceView: "UI_Regression_Tips"
}
rows {
  id: 2
  LeftTitleName: "峡谷5v5"
  IsShowSeeDetails: 1
  RightTitleText: "好久不见，曾经的<RegressionItem>{0}</>，为你准备了一份<RegressionItem>回归大礼</>！"
  RewardIdList: 1
  RewardIdList: 2
  RewardIdList: 4
  RewardNumList: 5
  RewardNumList: 5
  RewardNumList: 10
  IsBigRewardList: 0
  IsBigRewardList: 1
  IsBigRewardList: 0
  ReturnDownBtnText: "立即开启"
  ReturnGameModeBg: "T_ModelSelect_Img_Type_01"
  RulesId: 214
  PushDownBtnText1: "开心领奖"
  PushDownBtnText2: "领奖并开局"
  PushDownBtnText3: "知道了"
  PushDownBtnText4: "立即开局"
  PushFaceView: "UI_Regression_Tips"
}
rows {
  id: 3
  LeftTitleName: "谁是狼人"
  IsShowSeeDetails: 1
  RightTitleText: "好久不见，曾经的<RegressionItem>{0}</>，为你准备了一份<RegressionItem>回归大礼</>！"
  RewardIdList: 1
  RewardIdList: 2
  RewardIdList: 4
  RewardNumList: 5
  RewardNumList: 5
  RewardNumList: 10
  IsBigRewardList: 0
  IsBigRewardList: 0
  IsBigRewardList: 1
  ReturnDownBtnText: "立即开启"
  ReturnGameModeBg: "T_ModelSelect_Img_Type_01"
  RulesId: 214
  PushDownBtnText1: "开心领奖"
  PushDownBtnText2: "领奖并开局"
  PushDownBtnText3: "知道了"
  PushDownBtnText4: "立即开局"
  PushFaceView: "UI_Regression_Tips"
}
rows {
  id: 4
  LeftTitleName: "天天晋级赛"
  IsShowSeeDetails: 1
  RightTitleText: "好久不见，曾经的<RegressionItem>{0}</>，为你准备了一份<RegressionItem>回归大礼</>！"
  RewardIdList: 203002
  RewardIdList: 3610
  RewardIdList: 340006
  RewardNumList: 3
  RewardNumList: 1000
  RewardNumList: 1
  IsBigRewardList: 0
  IsBigRewardList: 0
  IsBigRewardList: 1
  ReturnDownBtnText: "立即开启"
  ReturnGameModeBg: "T_ModelSelectLarge_Img_Type_01_S11_Rec"
  RulesId: 214
  PushDownBtnText1: "开心领奖"
  PushDownBtnText2: "领奖并开局"
  PushDownBtnText3: "知道了"
  PushDownBtnText4: "立即开局"
  PushFaceView: "UI_Regression_Tips"
}
rows {
  id: 5
  LeftTitleName: "大王别抓我"
  IsShowSeeDetails: 1
  RightTitleText: "好久不见，曾经的<RegressionItem>{0}</>，为你准备了一份<RegressionItem>回归大礼</>！"
  RewardIdList: 1
  RewardIdList: 2
  RewardIdList: 4
  RewardNumList: 5
  RewardNumList: 5
  RewardNumList: 10
  IsBigRewardList: 1
  IsBigRewardList: 0
  IsBigRewardList: 0
  ReturnDownBtnText: "立即开启"
  ReturnGameModeBg: "T_ModelSelect_Img_Type_01"
  RulesId: 214
  PushDownBtnText1: "开心领奖"
  PushDownBtnText2: "领奖并开局"
  PushDownBtnText3: "知道了"
  PushDownBtnText4: "立即开局"
  PushFaceView: "UI_Regression_Tips"
}
