com.tencent.wea.xlsRes.table_ActivityFunParaConfig
excel/xls/H_活动功能参数.xlsx sheet:活动功能参数
rows {
  id: 10000
  intVal: 5
  stringVal: "testA"
}
rows {
  id: 10001
  intVal: 30
  stringVal: "testB"
}
rows {
  id: 10002
  intVal: 1000
}
rows {
  id: 10003
  intVal: 25
}
rows {
  id: 10004
  intVal: 2
}
rows {
  id: 10005
  intVal: 2
}
rows {
  id: 10101
  intVal: 300
}
rows {
  id: 10102
  intVal: 600
}
rows {
  id: 10103
  intVal: 10
}
rows {
  id: 10104
  intVal: 3
}
rows {
  id: 10105
  intVal: 2
}
rows {
  id: 10106
  intVal: 234
}
rows {
  id: 10107
  intVal: 2072
}
rows {
  id: 10108
  intVal: 2071
}
rows {
  id: 10109
  intVal: 5
}
rows {
  id: 10201
  intVal: 11
}
rows {
  id: 10202
  intVal: 24
}
rows {
  id: 10203
  intVal: 2
}
rows {
  id: 10301
  intVal: 200630
}
rows {
  id: 10302
  timeVal {
    seconds: 1737043200
  }
}
rows {
  id: 10303
  timeVal {
    seconds: 1740758399
  }
}
rows {
  id: 10401
  intVal: 5100
  stringVal: "作物丰收&大丰收概率提升30%\n祈福&被祈福次数+5"
  timeVal {
    seconds: 1735056000
  }
}
rows {
  id: 10402
  intVal: 1016
  stringVal: "全直售英雄限免\n每日排位分翻倍\n亲密度翻倍"
  timeVal {
    seconds: 1735056000
  }
}
rows {
  id: 10403
  intVal: 5100
  stringVal: "加工器容量提升50%\n祈福&被祈福次数+5"
  timeVal {
    seconds: 1735228800
  }
}
rows {
  id: 10404
  intVal: 1016
  stringVal: "全直售英雄限免\n每日排位分翻倍\n亲密度翻倍"
  timeVal {
    seconds: 1735228800
  }
}
rows {
  id: 10405
  intVal: 5100
  stringVal: "鱼饵价格降低20%\n祈福&被祈福次数+5"
  timeVal {
    seconds: 1735401600
  }
}
rows {
  id: 10406
  intVal: 1016
  stringVal: "全直售英雄限免\n每日排位分翻倍\n亲密度翻倍"
  timeVal {
    seconds: 1735401600
  }
}
rows {
  id: 10407
  intVal: 5100
  stringVal: "农作物/动物熟练度获取提升50%\n祈福&被祈福次数+5"
  timeVal {
    seconds: 1735488000
  }
}
rows {
  id: 10408
  intVal: 1016
  stringVal: "全直售英雄限免\n每日排位分翻倍\n亲密度翻倍"
  timeVal {
    seconds: 1735488000
  }
}
rows {
  id: 10409
  stringVal: "CDN:T_ModelSelect_Img_Type_10"
}
rows {
  id: 10501
  timeVal {
    seconds: 1751212800
  }
}
rows {
  id: 10502
  timeVal {
    seconds: 1751558400
  }
}
rows {
  id: 10505
  stringVal: "2519,621,-81"
}
rows {
  id: 10506
  stringVal: "0,90,0"
}
rows {
  id: 10507
  intVal: 1
}
rows {
  id: 10508
  intVal: 200831
}
rows {
  id: 10509
  intVal: 3
}
rows {
  id: 10510
  intVal: 4
}
rows {
  id: 11000
  intVal: 99999
}
rows {
  id: 11001
  intVal: 200
}
rows {
  id: 11002
  intVal: 50
}
rows {
  id: 11003
  intVal: 100
}
rows {
  id: 11004
  intVal: 100
}
rows {
  id: 11005
  timeVal {
    seconds: 1751558400
  }
}
rows {
  id: 11006
  timeVal {
    seconds: 1756483199
  }
}
rows {
  id: 11007
  timeVal {
    seconds: 1751212800
  }
}
rows {
  id: 11008
  timeVal {
    seconds: 1751558400
  }
}
rows {
  id: 11009
  timeVal {
    seconds: 1751212800
  }
}
rows {
  id: 11010
  timeVal {
    seconds: 1751558400
  }
}
