com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/W_文本表_文本配置.xlsx sheet:文本配置
rows {
  content: "积分"
  switch: 1
  stringId: "InLevel_Common_Integral"
}
rows {
  content: "感染"
  switch: 1
  stringId: "InLevel_Common_Infect"
}
rows {
  content: "消灭"
  switch: 1
  stringId: "InLevel_Common_Eliminate"
}
rows {
  content: "当前状态无法切换视角"
  switch: 1
  stringId: "InLevel_Common_NotChangeView"
}
rows {
  content: "秒"
  switch: 1
  stringId: "Common_Min_NoParam"
}
rows {
  content: "个"
  switch: 1
  stringId: "Common_Count"
}
rows {
  content: "没有申请语音权限，请系统申请后重试"
  switch: 1
  stringId: "Common_NoVoiceAuthorit"
}
rows {
  content: "游戏时间结束"
  switch: 1
  stringId: "Common_GameOver_Time"
}
rows {
  content: "{0}秒后开始游戏"
  switch: 1
  stringId: "Common_GameStart_SecOnrParam"
}
rows {
  content: "第{0}名"
  switch: 1
  stringId: "Common_RankDesc"
}
rows {
  content: "感染"
  switch: 1
  stringId: "Common_Infect"
}
rows {
  content: "积分"
  switch: 1
  stringId: "Common_Score"
}
rows {
  content: "消灭"
  switch: 1
  stringId: "Common_WipeOut"
}
rows {
  content: "倒地"
  switch: 1
  stringId: "Common_FallDown"
}
rows {
  content: "击败"
  switch: 1
  stringId: "Common_Beat"
}
rows {
  content: "立即领取"
  switch: 1
  stringId: "UI_Recharge_ReceiveRightNow"
}
rows {
  content: "第{0}天"
  switch: 1
  stringId: "Recharge_HowManyDays"
}
rows {
  content: "周一"
  switch: 1
  stringId: "Activity_Monday"
}
rows {
  content: "周二"
  switch: 1
  stringId: "Activity_Tuesday"
}
rows {
  content: "周三"
  switch: 1
  stringId: "Activity_Wednesday"
}
rows {
  content: "周四"
  switch: 1
  stringId: "Activity_Thursday"
}
rows {
  content: "周五"
  switch: 1
  stringId: "Activity_Friday"
}
rows {
  content: "周六"
  switch: 1
  stringId: "Activity_Saturday"
}
rows {
  content: "周天"
  switch: 1
  stringId: "Activity_Sunday"
}
rows {
  content: "存活到最后的星宝将获得胜利"
  switch: 1
  stringId: "DDP_InLevelTips1"
}
rows {
  content: "多次被击倒也会被淘汰"
  switch: 1
  stringId: "DDP_InLevelTips2"
}
rows {
  content: "排行榜以剩余生命值由高到低排序"
  switch: 1
  stringId: "DDP_InLevelTips3"
}
rows {
  content: "离开擂台太久会被淘汰"
  switch: 1
  stringId: "DDP_InLevelTips4"
}
rows {
  content: "小心紫色区域！"
  switch: 1
  stringId: "DDP_InLevelTips5"
}
rows {
  content: "被击倒、被攻击次数越少，排名越高"
  switch: 1
  stringId: "DDP_InLevelTips6"
}
rows {
  content: "存活最久的人将获得胜利！"
  switch: 1
  stringId: "DDP_InLevelTips_Default"
}
rows {
  content: "感染他们！"
  switch: 1
  stringId: "InLevel_ResultTip1"
}
rows {
  content: "回合结束"
  switch: 1
  stringId: "InLevel_ResultTip2"
}
rows {
  content: "大获全胜！"
  switch: 1
  stringId: "InLevel_ResultTip3"
}
rows {
  content: "遗憾败北"
  switch: 1
  stringId: "InLevel_ResultTip4"
}
rows {
  content: "是否退出游戏返回到桌面？"
  switch: 1
  stringId: "Login_BackToHome"
}
rows {
  content: "没有选中开关区域"
  switch: 1
  stringId: "UGC_ErrorMsg_NoSwitchArea"
}
rows {
  content: "不可以附加自己"
  switch: 1
  stringId: "UGC_ErrorMsg_SwitchAreaSelf"
}
rows {
  content: "仅可选择带有运动单元的物件"
  switch: 1
  stringId: "UGC_ErrorMsg_NoMovable"
}
rows {
  content: "该目标不可被开关控制"
  switch: 1
  stringId: "UGC_ErrorMsg_NoCanSwitch"
}
rows {
  content: "当前对象已经处于该开关的控制"
  switch: 1
  stringId: "UGC_ErrorMsg_IsMySlaver"
}
rows {
  content: "全部"
  switch: 1
  stringId: "Achievement_FilterMode_ToStr1"
}
rows {
  content: "竞速"
  switch: 1
  stringId: "Achievement_FilterMode_ToStr2"
}
rows {
  content: "生存"
  switch: 1
  stringId: "Achievement_FilterMode_ToStr3"
}
rows {
  content: "个人积分"
  switch: 1
  stringId: "Achievement_FilterMode_ToStr4"
}
rows {
  content: "团队积分"
  switch: 1
  stringId: "Achievement_FilterMode_ToStr5"
}
rows {
  content: "暂无记录"
  switch: 1
  stringId: "Achievement_NoRecord"
}
rows {
  content: "匹配中，暂无法前往"
  switch: 1
  stringId: "Team_InMatching"
}
rows {
  content: "功能未开启，暂无法前往"
  switch: 1
  stringId: "Common_FuncNoOpen"
}
rows {
  content: "暂未开放"
  switch: 1
  stringId: "Common_FuncNoOpen2"
}
rows {
  content: "等级提升到{0}级可解锁活动系统"
  switch: 1
  stringId: "Common_UnlockSystemByLevel"
}
rows {
  content: "第一周"
  switch: 1
  stringId: "Activity_TheFirstWeek"
}
rows {
  content: "第二周"
  switch: 1
  stringId: "Activity_TheSecondWeek"
}
rows {
  content: "第三周"
  switch: 1
  stringId: "Activity_TheThirdWeek"
}
rows {
  content: "第四周"
  switch: 1
  stringId: "Activity_TheFourthWeek"
}
rows {
  content: "第五周"
  switch: 1
  stringId: "Activity_FifthWeek"
}
rows {
  content: "第六周"
  switch: 1
  stringId: "Activity_SixthWeek"
}
rows {
  content: "第七周"
  switch: 1
  stringId: "Activity_SeventhWeek"
}
rows {
  content: "举报请求失败"
  switch: 1
  stringId: "Report_ReportRequestFailed"
}
rows {
  content: "举报截图上传失败，点击【提交】进行重试"
  switch: 1
  stringId: "Report_ScreenshotUploadAndTry"
}
rows {
  content: "问卷已过期！"
  switch: 1
  stringId: "Questionnaire_HasExpired"
}
rows {
  content: "当前内容已关闭，敬请期待"
  switch: 1
  stringId: "Common_FunctionClosed"
}
rows {
  content: "DS异常退出，请重新登陆"
  switch: 1
  stringId: "Common_DS_Exception"
}
rows {
  content: "当前状态无法进行互动"
  switch: 1
  stringId: "Common_CanNotDoAction"
}
rows {
  content: "玩家邀请你互动，是否接受？"
  switch: 1
  stringId: "Player_InviteDoAction"
}
rows {
  content: "玩家已取消互动"
  switch: 1
  stringId: "Player_PlayerCancelDoAction"
}
rows {
  content: "对方拒绝了你的邀请"
  switch: 1
  stringId: "Player_PlayerRejectYourAction"
}
rows {
  content: "互动动作已取消"
  switch: 1
  stringId: "Player_InteractiveActionStop"
}
rows {
  content: "会超载的 (づ*ω*)づ"
  switch: 1
  stringId: "CommunityClient_ResReload"
}
rows {
  content: "草稿箱"
  switch: 1
  stringId: "UGC_Map_Drafts"
}
rows {
  content: "已发布"
  switch: 1
  stringId: "UGC_Map_Release"
}
rows {
  content: "已发布地图"
  switch: 1
  stringId: "UGC_Map_MapRelease"
}
rows {
  content: "本地地图"
  switch: 1
  stringId: "UGC_Map_LocalMap"
}
rows {
  content: "回收站({0})"
  switch: 1
  stringId: "UGC_Map_Recycle"
}
rows {
  content: "在团队中无法进行此操作"
  switch: 1
  stringId: "UGC_CanNotDoThis"
}
rows {
  content: "下载地图失败"
  switch: 1
  stringId: "UGC_Map_DownloadMapFail"
}
rows {
  content: "您的操作过于频繁，请稍后再试"
  switch: 1
  stringId: "UGC_Map_TryLater"
}
rows {
  content: "是否删除该地图？"
  switch: 1
  stringId: "UGC_Map_IsDeleteMap"
}
rows {
  content: "是否复制该地图？"
  switch: 1
  stringId: "UGC_Map_IsCopyMap"
}
rows {
  content: "是否恢复该地图？"
  switch: 1
  stringId: "UGC_Map_IsRecoverMap"
}
rows {
  content: "是否下架该地图？"
  switch: 1
  stringId: "UGC_Map_IsRemoveMap"
}
rows {
  content: "规则说明"
  switch: 1
  stringId: "Common_RuleDesc"
}
rows {
  content: "我的订阅"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_Subscribe"
}
rows {
  content: "我的作品"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_Publish"
}
rows {
  content: "我的收藏"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_Collect"
}
rows {
  content: "游玩历史"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_History"
}
rows {
  content: "发布时间"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_PublicTime"
}
rows {
  content: "获取作品列表失败"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_GetMapFail"
}
rows {
  content: "获取收藏地图列表失败"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_GetCollectMapFail"
}
rows {
  content: "暂无订阅内容"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_NoSubscribe"
}
rows {
  content: "去订阅"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_GoSubscribe"
}
rows {
  content: "暂无作品"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_NoMap"
}
rows {
  content: "去制作"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_GoMadeMap"
}
rows {
  content: "暂无收藏"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_NoCollect"
}
rows {
  content: "暂无游玩历史"
  switch: 1
  stringId: "PlayerInfo_UGCInfo_NoHistory"
}
rows {
  content: "仅可选择开关类组件作为输入"
  switch: 1
  stringId: "UGC_ErrorMsg_OnlyGeneralSwitch"
}
rows {
  content: "该开关已经是选中逻辑开关的输入"
  switch: 1
  stringId: "UGC_ErrorMsg_SwitchAreaIsMy"
}
rows {
  content: "未定义内容"
  switch: 1
  stringId: "Common_UndefineContent"
}
rows {
  content: "请选择要创生的模板"
  switch: 1
  stringId: "UGC_Program_SelectTemplate"
}
rows {
  content: "请点击选择要创生的模板"
  switch: 1
  stringId: "UGC_Program_ClickToSelectTemplate"
}
