com.tencent.wea.xlsRes.table_ActRecruiteConf
excel/xls/H_活动_召集令.xlsx sheet:基础配置
rows {
  actId: 701
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3159
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  CDNTitle: "T_Convened_Img_Title2"
}
rows {
  actId: 702
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3159
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  copyAndShareClipStr: "元梦星搭子集结！我的集结密令：{idCode}，\n复制此段文字打开元梦之星，绑定我的召集密令，领取海量福利！\nhttps://s.ymzx.qq.com/eAU9F"
  CDNTitle: "T_Convened_Img_Title2"
}
rows {
  actId: 703
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3159
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  copyAndShareClipStr: "元梦星搭子集结！我的集结密令：{idCode}，\n复制此段文字打开元梦之星，绑定我的召集密令，领取海量福利！"
  CDNTitle: "T_Convened_Img_Title2"
}
rows {
  actId: 704
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3159
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  copyAndShareClipStr: "元梦星搭子集结！我的集结密令：{idCode}，\n复制此段文字打开元梦之星，绑定我的召集密令，领取海量福利！"
  CDNTitle: "T_Convened_Img_Title2"
}
rows {
  actId: 705
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3159
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  copyAndShareClipStr: "元梦星搭子集结！我的集结密令：{idCode}，\n复制此段文字打开元梦之星，绑定我的召集密令，领取海量福利！"
  CDNTitle: "T_Convened_Img_Title2"
}
rows {
  actId: 706
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3159
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  copyAndShareClipStr: "元梦星搭子集结！我的集结密令：{idCode}，\n复制此段文字打开元梦之星，绑定我的召集密令，领取海量福利！"
  CDNTitle: "T_Convened_Img_Title2"
}
rows {
  actId: 770
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3547
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  copyAndShareClipStr: "元梦星搭子集结！我的集结密令：{idCode}，\n复制此段文字打开元梦之星，绑定我的召集密令，领取海量福利！"
  CDNTitle: "T_Convened_Img_Title2"
}
rows {
  actId: 726
  rule {
    callLevel: 1
    echoOfflineHour: 192
    confirmValidDay: 3
  }
  reward {
    coinItemId: 3547
    login {
      ladder: 1
      value: 10
    }
    login {
      ladder: 2
      value: 20
    }
    login {
      ladder: 3
      value: 30
    }
  }
  mallId: 38
  arkRecallScene: "a62486b5-ef4e-443d-8c27-e27452a837f1"
  arkInviteScene: "1e00ffd1-f2f8-4cc4-a162-173e3b551518"
  copyAndShareClipStr: "元梦星搭子集结！我的集结密令：{idCode}，\n复制此段文字打开元梦之星，绑定我的召集密令，领取海量福利！"
  CDNTitle: "T_Activity_QiPaintingPractice_Img_Title"
}
