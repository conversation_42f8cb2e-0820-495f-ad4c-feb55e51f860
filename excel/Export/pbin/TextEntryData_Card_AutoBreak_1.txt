com.tencent.wea.xlsRes.table_TextEntryData
excel/xls/K_卡牌.xlsx sheet:文本表_卡牌
rows {
  content: "我向该玩家发起的卡牌交换请求已完成"
  switch: 1
  stringId: "Card_History_Exchange_Private_Me_To_Other_Over"
}
rows {
  content: "我向该玩家发起的卡牌交换请求已过期"
  switch: 1
  stringId: "Card_History_Exchange_Private_Me_To_Other_TimeOut"
}
rows {
  content: "我向这些玩家发起的卡牌交换请求已过期"
  switch: 1
  stringId: "Card_History_Exchange_Private_Me_To_Others_TimeOut"
}
rows {
  content: "该玩家向我发起的卡牌交换请求已过期"
  switch: 1
  stringId: "Card_History_Exchange_Private_Other_To_Me_TimeOut"
}
rows {
  content: "我向卡牌世界频道发起的卡牌交换请求已过期"
  switch: 1
  stringId: "Card_History_Exchange_Chat_Me_TimeOut"
}
rows {
  content: "我完成了该玩家向卡牌世界频道发起的交换请求"
  switch: 1
  stringId: "Card_History_Exchange_Chat_Me_Over"
}
rows {
  content: "我向卡牌世界频道发起的交换请求正在进行\n<FZLT20Red>({0}后过期)</>"
  switch: 1
  stringId: "Card_History_Exchange_Chat_Me_Again"
}
rows {
  content: "我向卡牌世界频道发起的交换请求已由该玩家完成"
  switch: 1
  stringId: "Card_History_Exchange_Chat_Other_Over"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Exchange_Chat_Again_Button"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Exchange_Private_Again_Button"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_History_Exchange_For_Other_Button"
}
rows {
  content: "已过期"
  switch: 1
  stringId: "Card_History_Exchange_TimeOut"
}
rows {
  content: "被{0}\n完成"
  switch: 1
  stringId: "Card_History_Exchange_Over_Button"
}
rows {
  content: "完成"
  switch: 1
  stringId: "Card_History_Exchange_Over_Next_Button"
}
rows {
  content: "交换卡牌"
  switch: 1
  stringId: "Card_Exchange_Title"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Exchange_Button"
}
rows {
  content: "今日可交换卡牌上限{0}/{1}，拥有{2}张及以上的相同卡牌才可交换"
  switch: 1
  stringId: "Card_Exchange_Des"
}
rows {
  content: "暂无符合条件卡牌"
  switch: 1
  stringId: "Card_Exchange_No_Cards"
}
rows {
  content: "请在右侧选择你想和对方交换的卡牌"
  switch: 1
  stringId: "Card_Exchange_Right_Des"
}
rows {
  content: "确认"
  switch: 1
  stringId: "Card_Confirm_Exchange_Title"
}
rows {
  content: "确定向{0}交换{1}吗？"
  switch: 1
  stringId: "Card_Confirm_Exchange_Content"
}
rows {
  content: "取消"
  switch: 1
  stringId: "Card_Confirm_Exchange_Cancle"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Confirm_Exchange_Sure"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Preview_Exchange_Button_Name"
}
rows {
  content: "(今日已赠送交换{0})"
  switch: 1
  stringId: "Card_Preview_Exchange_Button_Count"
}
rows {
  content: "选择交换渠道"
  switch: 1
  stringId: "Card_Channel_Title_Exchange"
}
rows {
  content: "QQ好友"
  switch: 1
  stringId: "Card_Channel_QQ"
}
rows {
  content: "微信好友"
  switch: 1
  stringId: "Card_Channel_WX"
}
rows {
  content: "点击周围区域关闭"
  switch: 1
  stringId: "Card_Channel_Close"
}
rows {
  content: "交换玩家卡牌选择"
  switch: 1
  stringId: "Card_Exchange_Select_Friend_Title"
}
rows {
  content: "对方将会选择一张回赠卡牌"
  switch: 1
  stringId: "Card_Exchange_Select_Friend_Des"
}
rows {
  content: "已选星宝:<CallPlayerLobby>{0}</>/{1}"
  switch: 1
  stringId: "Card_Exchange_Select_Friend_Already"
}
rows {
  content: "其他玩家有<CallPlayerLobby>{0}</>小时回应你的请求"
  switch: 1
  stringId: "Card_Exchange_Select_Friend_Time"
}
rows {
  content: "确认"
  switch: 1
  stringId: "Card_Confirm_Answer_Exchange_Title"
}
rows {
  content: "确定用{0}向{1}交换{2}吗？"
  switch: 1
  stringId: "Card_Confirm_Answe_Exchange_Content"
}
rows {
  content: "取消"
  switch: 1
  stringId: "Card_Confirm_Answe_Exchange_Cancle"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Confirm_Answe_Exchange_Sure"
}
rows {
  content: "交换卡牌已完成"
  switch: 1
  stringId: "Card_Confirm_Answe_Tips"
}
rows {
  content: "交换申请已发送"
  switch: 1
  stringId: "Card_Confirm_Tips"
}
rows {
  content: "万能卡兑换"
  switch: 1
  stringId: "Card_WildCard_Title"
}
rows {
  content: "你有一张万能卡在<LobbyTips02>{0}</>后过期"
  switch: 1
  stringId: "Card_WildCard_TimeOut"
}
rows {
  content: "{0}\n{1}/{2}"
  switch: 1
  stringId: "Card_WildCard_Left_CardDeck"
}
rows {
  content: "仅显示我缺失的卡牌"
  switch: 1
  stringId: "Card_WildCard_Check"
}
rows {
  content: "请选择需要兑换的卡牌"
  switch: 1
  stringId: "Card_WildCard_Des"
}
rows {
  content: "兑换"
  switch: 1
  stringId: "Card_WildCard_Button_Dis_Name"
}
rows {
  content: "{0} 兑换{1}张"
  switch: 1
  stringId: "Card_WildCard_Button_Name"
}
rows {
  content: "已达到最大可兑换数量"
  switch: 1
  stringId: "Card_WildCard_Max_Tips"
}
rows {
  content: "暂未选择兑换卡牌"
  switch: 1
  stringId: "Card_WildCard_Tips_Dis"
}
rows {
  content: "{0}后过期"
  switch: 1
  stringId: "Card_WildCard_Main_TimeOut"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Request_QQWX_Angain_Button"
}
rows {
  content: "我向{0}发起的索要请求进行中\n<FZLT20Red>({1}后过期)</>"
  switch: 1
  stringId: "Card_History_Request_QQWX_Angain"
}
rows {
  content: "该玩家完成了我向{0}发起的索要请求"
  switch: 1
  stringId: "Card_History_Request_QQWX_Over"
}
rows {
  content: "我向{0}发起的索要请求已过期"
  switch: 1
  stringId: "Card_History_Request_QQWX_TimeOut"
}
rows {
  content: "我完成了该玩家向{0}发起的索要请求"
  switch: 1
  stringId: "Card_History_Request_QQWX_Answer_Over"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Give_QQWX_Angain_Button"
}
rows {
  content: "我向{0}发起的赠送请求进行中\n<FZLT20Red>({1}后过期)</>"
  switch: 1
  stringId: "Card_History_Give_QQWX_Angain"
}
rows {
  content: "该玩家完成了我向{0}发起的赠送请求"
  switch: 1
  stringId: "Card_History_Give_QQWX_Over"
}
rows {
  content: "我向{0}发起的赠送请求已过期"
  switch: 1
  stringId: "Card_History_Give_QQWX_TimeOut"
}
rows {
  content: "我完成了该玩家向{0}发起的赠送请求"
  switch: 1
  stringId: "Card_History_Give_QQWX_Answer_Over"
}
rows {
  content: "我向{0}发起的交换请求已过期"
  switch: 1
  stringId: "Card_History_Exchange_QQWX_Me_TimeOut"
}
rows {
  content: "我完成了该玩家向{0}发起的交换请求"
  switch: 1
  stringId: "Card_History_Exchange_QQWX_Me_Over"
}
rows {
  content: "我向{0}发起的交换请求进行中\n<FZLT20Red>({1}后过期)</>"
  switch: 1
  stringId: "Card_History_Exchange_QQWX_Me_Again"
}
rows {
  content: "该玩家完成了我向{0}发起的交换请求"
  switch: 1
  stringId: "Card_History_Exchange_QQWX_Other_Over"
}
rows {
  content: "再次发送请求"
  switch: 1
  stringId: "Card_History_Exchange_QQWX_Again_Button"
}
rows {
  content: "{0}向我发起的交换请求已过期"
  switch: 1
  stringId: "Card_History_Exchange_QQWX_Other_Me_TimeOut"
}
rows {
  content: "{0}向我发起的交换请求进行中\n<FZLT20Red>({1}后过期)</>"
  switch: 1
  stringId: "Card_History_Exchange_QQWX_Other_Me_Again"
}
rows {
  content: "{0}向我发起的索要请求已过期"
  switch: 1
  stringId: "Card_History_Request_QQWX_Other_Me_TimeOut"
}
rows {
  content: "{0}向我发起的索要请求进行中\n<FZLT20Red>({1}后过期)</>"
  switch: 1
  stringId: "Card_History_Request_QQWX_Other_Me_Again"
}
rows {
  content: "微信好友"
  switch: 1
  stringId: "Card_History_WXHead_Name"
}
rows {
  content: "QQ好友"
  switch: 1
  stringId: "Card_History_QQHead_Name"
}
rows {
  content: "确认"
  switch: 1
  stringId: "Card_Confirm_Request_Title"
}
rows {
  content: "确认"
  switch: 1
  stringId: "Card_Confirm_Give_Title"
}
rows {
  content: "待交换"
  switch: 1
  stringId: "Card_Chat_Exchange_Waiting"
}
rows {
  content: "待领取"
  switch: 1
  stringId: "Card_Chat_Give_Waiting"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Chat_Exchange_Button"
}
rows {
  content: "分享"
  switch: 1
  stringId: "Card_Preview_Share_Button_Name"
}
rows {
  content: "今日赠送或交换的卡牌数量"
  switch: 1
  stringId: "Card_Preview_GiveAndExchange_Des"
}
rows {
  content: "{0}/{1}"
  switch: 1
  stringId: "Card_Preview_GiveAndExchange_Des_Count"
}
rows {
  content: "请与我交换卡牌"
  switch: 1
  stringId: "Card_Chat_Exchange_Title"
}
rows {
  content: "一次最多只能向{0}个好友交换"
  switch: 1
  stringId: "Card_Tips_Exchange_Friend_Limit"
}
rows {
  content: "今日不再提示"
  switch: 1
  stringId: "Card_History_Daily_Close_Check"
}
rows {
  content: "请你赠送该卡牌"
  switch: 1
  stringId: "Card_Right_Request"
}
rows {
  content: "请你交换该卡牌"
  switch: 1
  stringId: "Card_Right_Exchange"
}
rows {
  content: "{0}分钟内忽略该玩家请求"
  switch: 1
  stringId: "Card_Right_Check_Content"
}
rows {
  content: "拒绝{0}s"
  switch: 1
  stringId: "Card_Right_Cancle_Btn_Name"
}
rows {
  content: "赠送"
  switch: 1
  stringId: "Card_Right_Request_Sure_Btn_Name"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Right_Exchange_Sure_Btn_Name"
}
rows {
  content: "赠送"
  switch: 1
  stringId: "Card_Right_Request_Sure_Dis_Btn_Name"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Right_Exchange_Sure_Dis_Btn_Name"
}
rows {
  content: "索要"
  switch: 1
  stringId: "Card_Right_Request_Title"
}
rows {
  content: "交换"
  switch: 1
  stringId: "Card_Right_Exchange_Title"
}
rows {
  content: "万能卡兑换"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Title"
}
rows {
  content: "请选择需要兑换的卡牌"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Hint"
}
rows {
  content: "仅显示我缺失的卡牌"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Show_Have_Btn_Msg"
}
rows {
  content: "兑换[0]张"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Convert_Btn_Msg"
}
rows {
  content: "兑换"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Convert_Btn_Msg_Dis"
}
rows {
  content: "选择兑换的卡牌数量，超出万能卡的拥有数量上限！"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Click_Card"
}
rows {
  content: "确定使用【万能卡】兑换【[0]】卡牌吗？"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Click_Convert_Hint"
}
rows {
  content: "等"
  switch: 1
  stringId: "Card_All_Powerful_Pop_Click_Convert_Hint2"
}
rows {
  content: "索要"
  switch: 1
  stringId: "Card_Request_Button"
}
