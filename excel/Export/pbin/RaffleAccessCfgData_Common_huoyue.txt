com.tencent.wea.xlsRes.table_RaffleAccessCfgData
excel/xls/C_抽奖_活跃.xlsx sheet:活动-赛季金币活跃卡池
rows {
  raffleId: 10006
  name: "印章祈愿"
  startTime {
    seconds: 1725897600
  }
  endTime {
    seconds: 1730390399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10006
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.18.54"
  showStartTime {
    seconds: 1725897600
  }
  showEndTime {
    seconds: 1730390399
  }
  isShow: true
  animType: 4
  categoryId: 2
}
rows {
  raffleId: 20000001
  name: "百衣补贴"
  startTime {
    seconds: 1727625600
  }
  endTime {
    seconds: 1734451199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000001
  }
  dailyLimit: 10000
  maxLimit: 10000
  text: "<DRYellow>每30次</>抽奖必定触发暴击"
  lowestVersion: "1.3.18.70"
  showStartTime {
    seconds: 1727625600
  }
  showEndTime {
    seconds: 1734451199
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 20000002
  name: "摇钱树卡池测试"
  startTime {
    seconds: 1728316800
  }
  endTime {
    seconds: 1735574399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000002
  }
  dailyLimit: 10000
  maxLimit: 10000
  text: "<DRYellow>每30次</>抽奖必定触发暴击"
  lowestVersion: "1.3.18.70"
  showStartTime {
    seconds: 1728316800
  }
  showEndTime {
    seconds: 1735574399
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 10007
  name: "印章祈愿"
  startTime {
    seconds: 1730390400
  }
  endTime {
    seconds: 1734019199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10007
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.26.22"
  milestone {
    giftIds: 4
  }
  showStartTime {
    seconds: 1730390400
  }
  showEndTime {
    seconds: 1734019199
  }
  isShow: true
  animType: 4
  categoryId: 2
}
rows {
  raffleId: 10008
  name: "印章祈愿"
  startTime {
    seconds: 1734019200
  }
  endTime {
    seconds: 1738252799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10008
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.26.22"
  showStartTime {
    seconds: 1734019200
  }
  showEndTime {
    seconds: 1738252799
  }
  isShow: true
  animType: 4
  categoryId: 2
}
rows {
  raffleId: 10009
  name: "印章祈愿"
  startTime {
    seconds: 1734019200
  }
  endTime {
    seconds: 1737647999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10008
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.26.22"
  showStartTime {
    seconds: 1734019200
  }
  showEndTime {
    seconds: 1737647999
  }
  isShow: true
  animType: 4
  categoryId: 2
}
rows {
  raffleId: 10010
  name: "印章祈愿"
  startTime {
    seconds: 1738252800
  }
  endTime {
    seconds: 1743091199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10010
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.68.50"
  showStartTime {
    seconds: 1738252800
  }
  showEndTime {
    seconds: 1743091199
  }
  isShow: true
  animType: 4
  categoryId: 2
}
rows {
  raffleId: 20000003
  name: "春节活跃卡池1"
  startTime {
    seconds: 1728316800
  }
  endTime {
    seconds: 1737388799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000003
  }
  dailyLimit: 10000
  maxLimit: 10000
  text: "<DRYellow>每30次</>抽奖必定触发暴击"
  lowestVersion: "1.3.18.70"
  showStartTime {
    seconds: 1728316800
  }
  showEndTime {
    seconds: 1737388799
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 20000004
  name: "春节活跃卡池2"
  startTime {
    seconds: 1728316800
  }
  endTime {
    seconds: 1737388799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000004
  }
  dailyLimit: 10000
  maxLimit: 10000
  text: "<DRYellow>每30次</>抽奖必定触发暴击"
  lowestVersion: "1.3.18.70"
  drawConditions {
    condition {
      conditionType: 286
      value: 1
      subConditionList {
        type: 242
        value: 20000003
      }
    }
  }
  showStartTime {
    seconds: 1728316800
  }
  showEndTime {
    seconds: 1737388799
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 20000005
  name: "春节活跃卡池3"
  startTime {
    seconds: 1728316800
  }
  endTime {
    seconds: 1737388799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000005
  }
  dailyLimit: 10000
  maxLimit: 10000
  text: "<DRYellow>每30次</>抽奖必定触发暴击"
  lowestVersion: "1.3.18.70"
  drawConditions {
    condition {
      conditionType: 286
      value: 1
      subConditionList {
        type: 242
        value: 20000004
      }
    }
  }
  showStartTime {
    seconds: 1728316800
  }
  showEndTime {
    seconds: 1737388799
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 20000006
  name: "春节活跃卡池4"
  startTime {
    seconds: 1728316800
  }
  endTime {
    seconds: 1737388799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000006
  }
  dailyLimit: 10000
  maxLimit: 10000
  text: "<DRYellow>每30次</>抽奖必定触发暴击"
  lowestVersion: "1.3.18.70"
  drawConditions {
    condition {
      conditionType: 286
      value: 1
      subConditionList {
        type: 242
        value: 20000005
      }
    }
  }
  showStartTime {
    seconds: 1728316800
  }
  showEndTime {
    seconds: 1737388799
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 20000007
  name: "春节活跃卡池5"
  startTime {
    seconds: 1728316800
  }
  endTime {
    seconds: 1737388799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000007
  }
  dailyLimit: 10000
  maxLimit: 10000
  text: "<DRYellow>每30次</>抽奖必定触发暴击"
  lowestVersion: "1.3.18.70"
  drawConditions {
    condition {
      conditionType: 286
      value: 1
      subConditionList {
        type: 242
        value: 20000006
      }
    }
  }
  showStartTime {
    seconds: 1728316800
  }
  showEndTime {
    seconds: 1737388799
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 20000008
  name: "召集令新春版"
  startTime {
    seconds: 1737043200
  }
  endTime {
    seconds: 1741103999
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000008
  }
  dailyLimit: 10000
  maxLimit: 10000
  textRuleId: 105
  lowestVersion: "1.3.18.54"
  showStartTime {
    seconds: 1737043200
  }
  showEndTime {
    seconds: 1741103999
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 10011
  name: "印章祈愿"
  startTime {
    seconds: 1743091200
  }
  endTime {
    seconds: 1747324799
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10011
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.68.50"
  showStartTime {
    seconds: 1743091200
  }
  showEndTime {
    seconds: 1747324799
  }
  isShow: true
  animType: 4
  categoryId: 2
}
rows {
  raffleId: 20000009
  name: "召集令五一版"
  startTime {
    seconds: 1746028800
  }
  endTime {
    seconds: 1750262399
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 20000009
  }
  dailyLimit: 10000
  maxLimit: 10000
  textRuleId: 105
  lowestVersion: "1.3.18.54"
  showStartTime {
    seconds: 1746028800
  }
  showEndTime {
    seconds: 1750262399
  }
  isShow: true
  animType: 4
}
rows {
  raffleId: 10012
  name: "印章祈愿"
  startTime {
    seconds: 1747324800
  }
  endTime {
    seconds: 1752163199
  }
  poolSelection {
    policy: RPP_Direct
    poolId: 10012
  }
  dailyLimit: 1000
  maxLimit: 1000
  textRuleId: 105
  text: "<DRYellow>每十次</>祈愿必得外观或非凡奖励"
  lowestVersion: "1.3.88.53"
  showStartTime {
    seconds: 1747324800
  }
  showEndTime {
    seconds: 1752163199
  }
  isShow: true
  animType: 4
  categoryId: 2
}
