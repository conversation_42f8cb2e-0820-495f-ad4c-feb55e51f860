--auto generated code, do not edit manually
local TableHelperTables = {
    ["table_ABTestMatchTypeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ABTestMatchTypeData",
        }
    },
    ["table_AIAssistantErrorCodeConfig"] = {
        TableKey = "ErrorCode",
        SubTableNames = {
			"AIAssistantErrorCodeConfig",
        }
    },
    ["table_AIBehaveRule"] = {
        TableKey = "ActionId",
        SubTableNames = {
			"AIBehaveRule",
        }
    },
    ["table_AICommentaryData"] = {
        TableKey = "id",
        SubTableNames = {
			"AICommentary",
        }
    },
    ["table_AIGCActionChangeContentData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCActionChangeContentData",
        }
    },
    ["table_AIGCAvatarTypeInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCAvatarTypeInfo",
        }
    },
    ["table_AIGCCommonConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCCommonConf",
        }
    },
    ["table_AIGCNPCDanceConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCNPCDanceConf",
        }
    },
    ["table_AIGCNPCErrMsgData"] = {
        TableKey = "errId",
        SubTableNames = {
			"AIGCNPCErrMsg",
        }
    },
    ["table_AIGCNPCInteractActionConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCNPCInteractAction",
        }
    },
    ["table_AIGCNPCPuzzleConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCNPCPuzzle",
        }
    },
    ["table_AIGCNPCSingConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCNPCSingConf",
        }
    },
    ["table_AIGCNPCTouchConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCNPCTouch",
        }
    },
    ["table_AIGCQuickChatData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIGCQuickChatData",
        }
    },
    ["table_AIGCQuickChatNumsSettingData"] = {
        TableKey = "type",
        SubTableNames = {
			"AIGCQuickChatNumsSettingData",
        }
    },
    ["table_AIInfoDifficultyData"] = {
        TableKey = "levelId",
        SubTableNames = {
			"AIInfoDifficultyData",
        }
    },
    ["table_AIInfoRecordSkinData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIInfoRecordSkinData",
        }
    },
    ["table_AIModes"] = {
        TableKey = "ID",
        SubTableNames = {
			"AIModes",
        }
    },
    ["table_AIParamConfig"] = {
        TableKey = "LevelId",
        SubTableNames = {
			"AIParamConfig",
        }
    },
    ["table_AIRandomCfgData"] = {
        TableKey = "id",
        SubTableNames = {
			"AIRandomCfgData",
        }
    },
    ["table_AIScriptConfig"] = {
        TableKey = "Id",
        SubTableNames = {
			"AIScriptConfig",
        }
    },
    ["table_AISuitInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"AISuitInfoData",
        }
    },
    ["table_AMSItemCfgData"] = {
        TableKey = "id",
        SubTableNames = {
			"AMSItemCfgData",
        }
    },
    ["table_AMSPackageIdCfgData"] = {
        TableKey = "group,id",
        SubTableNames = {
			"AMSPackageIdCfgData",
        }
    },
    ["table_AccumulateBlessingsActivityConfData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"AccumulateBlessingsActivityConfData",
        }
    },
    ["table_AchievementConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AchievementConfData",
        }
    },
    ["table_AchievementReissueUgcBadgeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AchievementReissueUgcBadgeConf",
        }
    },
    ["table_AchievementSortConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AchievementSortData",
        }
    },
    ["table_AchievementTaskConfData"] = {
        TableKey = "id,stageId",
        SubTableNames = {
			"AchievementTaskConfData_arena",
			"AchievementTaskConfData_js",
			"AchievementTaskConfData_main",
			"AchievementTaskConfData_moba",
			"AchievementTaskConfData_nr3e",
			"AchievementTaskConfData_nr3e8",
        }
    },
    ["table_ActBookOfFriends"] = {
        TableKey = "actId",
        SubTableNames = {
			"ActBookOfFriends",
        }
    },
    ["table_ActBookOfFriendsGuide"] = {
        TableKey = "systemId",
        SubTableNames = {
			"ActBookOfFriendsGuide",
        }
    },
    ["table_ActElfForestConf"] = {
        TableKey = "TaskId",
        SubTableNames = {
			"ActElfForestConf",
        }
    },
    ["table_ActRecruiteConf"] = {
        TableKey = "actId",
        SubTableNames = {
			"ActRecruiteConf",
        }
    },
    ["table_ActSpringBlessLevelData"] = {
        TableKey = "Level",
        SubTableNames = {
			"ActSpringBlessLevelConf",
			"ActSpringGreetingsLevelConf",
			"ActSpringLanternLevelConf",
        }
    },
    ["table_ActivityActivityFlashRaceCheeringData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityActivityFlashRaceCheeringData",
        }
    },
    ["table_ActivityActivityFlashRaceCheeringTimeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityActivityFlashRaceCheeringTimeData",
        }
    },
    ["table_ActivityBIData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityBIData",
        }
    },
    ["table_ActivityBannerJumpStyleConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityBannerJumpStyleConf",
			"OGCActivityBannerJumpStyleConf",
        }
    },
    ["table_ActivityBrainiacConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityBrainiacConfigData",
        }
    },
    ["table_ActivityCenterReverseData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityCenterReverseData",
        }
    },
    ["table_ActivityChapterTaskData"] = {
        TableKey = "chapterId",
        SubTableNames = {
			"ActivityChapterTaskData",
        }
    },
    ["table_ActivityCheckInManualData"] = {
        TableKey = "id,activityId",
        SubTableNames = {
			"ActivityCheckInManualData",
        }
    },
    ["table_ActivityClubChallengeData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ActivityClubChallengeData",
        }
    },
    ["table_ActivityClubChallengeMatchData"] = {
        TableKey = "activityId,matchTypeId",
        SubTableNames = {
			"ActivityClubChallengeMatchData",
        }
    },
    ["table_ActivityClubChallengeMatchSettlementData"] = {
        TableKey = "activityId,matchTypeId,rank",
        SubTableNames = {
			"ActivityClubChallengeMatchSettlementData",
        }
    },
    ["table_ActivityClubChallengeStarLightRewardData"] = {
        TableKey = "activityId,rewardId",
        SubTableNames = {
			"ActivityClubChallengeStarLightRewardData",
        }
    },
    ["table_ActivityCoinAssessData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityCoinAssessData",
        }
    },
    ["table_ConanIpActiveCardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityConanIpCardData",
        }
    },
    ["table_ActivityConanWarmupData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityConanWarmupData",
        }
    },
    ["table_ActivityDoubleTeamBaseData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ActivityDoubleTeamBaseData",
        }
    },
    ["table_ActivityFarmRangeTaskData"] = {
        TableKey = "taskGroupId",
        SubTableNames = {
			"ActivityFarmRangeTaskData",
        }
    },
    ["table_ActivityFarmReturningBuffData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFarmReturningBuffData",
        }
    },
    ["table_ActivityFarmReturningOverviewData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFarmReturningOverviewData",
        }
    },
    ["table_ActivityFlyingChessData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFlyingChessData",
        }
    },
    ["table_ActivityFlyingChessGetWayData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFlyingChessGetWayData",
        }
    },
    ["table_ActivityFlyingChessGridData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFlyingChessGridData",
        }
    },
    ["table_ActivityFlyingChessMapInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFlyingChessMapInfoData",
        }
    },
    ["table_ActivityFlyingChessPlotData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFlyingChessPlotData",
        }
    },
    ["table_ActivityFlyingChessRoundRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFlyingChessRoundRewardData",
        }
    },
    ["table_ActivityFramNewActivityConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFramNewActivityConfig",
        }
    },
    ["table_ActivityFunParaConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityFunParaData",
        }
    },
    ["table_ActivityGopenIdWhiteListConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityGopenIdWhiteListConfData",
        }
    },
    ["table_ActivityGrowthPathMainData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityGrowthPathMainData",
        }
    },
    ["table_ActivityHYNCheckInConf"] = {
        TableKey = "day",
        SubTableNames = {
			"ActivityHYNCheckInConf",
        }
    },
    ["table_ActivityHYWarmUpCheckinData"] = {
        TableKey = "Id",
        SubTableNames = {
			"ActivityHYWarmUpCheckinData",
        }
    },
    ["table_ActivityHYWarmUpData"] = {
        TableKey = "Id",
        SubTableNames = {
			"ActivityHYWarmUpData",
        }
    },
    ["table_ActivityHYWarmUpProgressData"] = {
        TableKey = "Id",
        SubTableNames = {
			"ActivityHYWarmUpProgressData",
        }
    },
    ["table_ActivityHeroSpineConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityHeroSpineConfData",
        }
    },
    ["table_ActivityInflateRedPacketData"] = {
        TableKey = "money",
        SubTableNames = {
			"ActivityInflateRedPacketData",
        }
    },
    ["table_ActivityInflateRedPacketMiscData"] = {
        TableKey = "key",
        SubTableNames = {
			"ActivityInflateRedPacketMiscData",
        }
    },
    ["table_ActivityLabelConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityLabelConfigForLetsGo",
        }
    },
    ["table_ActivityLotteryDrawCostData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityLotteryDrawCostData",
        }
    },
    ["table_ActivityLotteryDrawMiscData"] = {
        TableKey = "key",
        SubTableNames = {
			"ActivityLotteryDrawMiscData",
        }
    },
    ["table_ActivityLotteryDrawRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityLotteryDrawRewardData",
			"ActivityLotteryDrawRewardData_Minesweeper",
        }
    },
    ["table_ActivityMainConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMainConfigForLetsGo_UgcActivity",
			"ActivityMainConfigForLetsGo_commercialization",
			"ActivityMainConfigForLetsGo_common",
			"ActivityMainConfigForLetsGo_design",
			"ActivityMainConfigForLetsGo_hok",
			"ActivityMainConfigForLetsGo_modyunying",
			"ActivityMainConfigForLetsGo_noviceSeven",
			"ActivityMainConfigForLetsGo_recharge",
			"ActivityMainConfigForLetsGo_yunying",
        }
    },
    ["table_ActivityMarqueeConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMarqueeConfData",
        }
    },
    ["table_ActivityMianGanConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMianGanData",
        }
    },
    ["table_ActivityMinesweeperEventData"] = {
        TableKey = "eventId",
        SubTableNames = {
			"ActivityMinesweeperEventData",
        }
    },
    ["table_ActivityMinesweeperMainData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ActivityMinesweeperMainData",
        }
    },
    ["table_ResActivityMobaSquadDrawGetRewardShow"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMobaSquadDrawGetRewardShowData",
        }
    },
    ["table_ResActivityMobaSquadDrawRedPackage"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ActivityMobaSquadDrawRedPackageData",
        }
    },
    ["table_ActivityMonopolyGridData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMonopolyGridData",
        }
    },
    ["table_ActivityMonopolyLotteryData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMonopolyLotteryData",
        }
    },
    ["table_ActivityMonopolyMainData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ActivityMonopolyMainData",
        }
    },
    ["table_ActivityMonopolyRoundRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMonopolyRoundRewardData",
        }
    },
    ["table_ActivityMonopolyStepRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityMonopolyStepRewardData",
        }
    },
    ["table_ActivityPermanentExchangeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityPermanentExchange",
        }
    },
    ["table_ActivityPrayerCardCollectionConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityPrayerCardCollectionConfigData",
        }
    },
    ["table_ActivityPrayerCardConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityPrayerCardConfigData",
        }
    },
    ["table_ActivityPrayerCardMiscData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityPrayerCardMiscData",
        }
    },
    ["table_ActivityPrayerCardRewardLimitData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityPrayerCardRewardLimitData",
        }
    },
    ["table_ActivityPrayerCardWeightConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityPrayerCardWeightConfigData",
        }
    },
    ["table_ActivityPuzzleConfData"] = {
        TableKey = "puzzleId",
        SubTableNames = {
			"ActivityPuzzleConfData",
        }
    },
    ["table_ActivityPuzzleStageRewardConfData"] = {
        TableKey = "rewardId",
        SubTableNames = {
			"ActivityPuzzleStageRewardConfData",
        }
    },
    ["table_ActivityQingShuangTrialData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityQingShuangTrialData",
        }
    },
    ["table_ActivityRechargeRecommendData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityRechargeRecommendData",
        }
    },
    ["table_ActivityRestaurantThemedData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityRestaurantThemedData",
        }
    },
    ["table_ActivityRestaurantThemedFoodData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityRestaurantThemedFoodData",
        }
    },
    ["table_ActivityRestaurantThemedMiscData"] = {
        TableKey = "key",
        SubTableNames = {
			"ActivityRestaurantThemedMiscData",
        }
    },
    ["table_ActivitySchoolSeasonConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivitySchoolSeasonConfig",
        }
    },
    ["table_ActivitySuperLinearRandomData"] = {
        TableKey = "activityId,randomValue",
        SubTableNames = {
			"ActivitySuperLinearRandomData",
        }
    },
    ["table_ActivitySuperLinearRedeemData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivitySuperLinearRedeemData",
        }
    },
    ["table_ActivitySuperLinearRedeemTitleData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ActivitySuperLinearRedeemTitleData",
        }
    },
    ["table_ActivityTab"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityTabData",
        }
    },
    ["table_ActivityTeamRankData"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityTeamRankData",
        }
    },
    ["table_ActivityTeamShare"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityTeamShare",
        }
    },
    ["table_ActivityTwoPeopleSquadMiscData"] = {
        TableKey = "key",
        SubTableNames = {
			"ActivityTwoPeopleSquadMiscData",
        }
    },
    ["table_ActivityWolfKillSquadTrophyBaseData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ActivityWolfKillSquadTrophyBaseData",
        }
    },
    ["table_ActivityWolfReturnConditionConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityWolfReturnConditionData",
        }
    },
    ["table_ActivityWolfReturnRewardConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ActivityWolfReturnRewardData",
        }
    },
    ["table_AddQQFriendWhiteList"] = {
        TableKey = "Index",
        SubTableNames = {
			"AddQQFriendWhiteList",
        }
    },
    ["table_AdministrationCodeConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AdministrationCodeConfData",
        }
    },
    ["table_AdvertisingSpaceData"] = {
        TableKey = "id",
        SubTableNames = {
			"AdvertisingSpaceData",
        }
    },
    ["table_AiNpcChatPushConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AiNpcChatPush",
        }
    },
    ["table_AiNpcChatPushBattleEventConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AiNpcChatPushBattleEvent",
        }
    },
    ["table_AiNpcChatPushHOKHighLightConditionConf"] = {
        TableKey = "highLightId",
        SubTableNames = {
			"AiNpcChatPushHOKHighLightCondition",
        }
    },
    ["table_AiNpcChatPushRateConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AiNpcChatPushRate",
        }
    },
    ["table_AiNpcChatPushRateEventConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AiNpcChatPushRateEvent",
        }
    },
    ["table_AiNpcChatPushTextConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AiNpcChatPushText",
        }
    },
    ["table_AigcDefaultPromptData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcDefaultPromptData",
        }
    },
    ["table_AigcDefaultThemeData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcDefaultThemeData",
        }
    },
    ["table_AigcNpcConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpc",
        }
    },
    ["table_AigcNpcBaseSuitConf"] = {
        TableKey = "labNpcId",
        SubTableNames = {
			"AigcNpcBaseSuitConf",
        }
    },
    ["table_AigcNpcBlockEmojiData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcBlockEmojiData",
        }
    },
    ["table_AigcNpcDressConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcDress",
        }
    },
    ["table_AigcNpcEmojiMappingData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcEmojiMappingData",
        }
    },
    ["table_AigcNpcFriendChatConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcFriendChat",
        }
    },
    ["table_AigcNpcLicenseData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcLicenseData",
        }
    },
    ["table_AigcNpcPalCareerConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalCareerConf",
        }
    },
    ["table_AigcNpcPalEmojiMappingData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalEmojiMappingData",
        }
    },
    ["table_AigcNpcPalFavoriteMatchConf"] = {
        TableKey = "matchType",
        SubTableNames = {
			"AigcNpcPalFavoriteMatchConf",
        }
    },
    ["table_AigcNpcPalGenderConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalGenderConf",
        }
    },
    ["table_AigcNpcPalGuideContentData"] = {
        TableKey = "index",
        SubTableNames = {
			"AigcNpcPalGuideContentData",
        }
    },
    ["table_AigcNpcPalMBTIConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalMBTIConf",
        }
    },
    ["table_AigcNpcPalMoodEventConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalMoodEventConf",
        }
    },
    ["table_AigcNpcPalPersonalityNameConf"] = {
        TableKey = "type",
        SubTableNames = {
			"AigcNpcPalPersonalityNameConf",
        }
    },
    ["table_AigcNpcPalPersonalityTagConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalPersonalityTagConf",
        }
    },
    ["table_AigcNpcPalPresetsConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalPresetsConf",
        }
    },
    ["table_AigcNpcPalQuickChatData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalQuickChatData",
        }
    },
    ["table_AigcNpcPalQuickChatNumsSettingData"] = {
        TableKey = "type",
        SubTableNames = {
			"AigcNpcPalQuickChatNumsSettingData",
        }
    },
    ["table_AigcNpcPalSecurityAnswerConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalSecurityAnswerConf",
        }
    },
    ["table_AigcNpcPalStarSignData"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalStarSignData",
        }
    },
    ["table_AigcNpcPalTextPresetsConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalTextPresetsConf",
        }
    },
    ["table_AigcNpcPalTimbreConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AigcNpcPalTimbreConf",
        }
    },
    ["table_AlbumLimitConditionConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"AlbumLimitConditionData",
        }
    },
    ["table_AmusementLimitRuleConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"AmusementLimitRuleConfig",
        }
    },
    ["table_AmusementParkActivityTaskConfig"] = {
        TableKey = "activityId,id",
        SubTableNames = {
			"AmusementParkActivityTaskConfigData",
        }
    },
    ["table_AnimFxAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"AnimFxAudioConfig",
        }
    },
    ["table_AnimalHandbookColonyConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AnimalHandbookColonyConfData",
        }
    },
    ["table_AnimalHandbookConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AnimalHandbookConfData",
        }
    },
    ["table_AnimalHandbookMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AnimalHandbookMiscConfData",
        }
    },
    ["table_AnimalHandbookSpeciesConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AnimalHandbookSpeciesConfData",
        }
    },
    ["table_AnimalHandbookUltimatePrizeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"AnimalHandbookUltimatePrizeConfData",
        }
    },
    ["table_ApiAttrDataCtrlConf"] = {
        TableKey = "dataID",
        SubTableNames = {
			"ApiAttrDataCtrlConfData",
        }
    },
    ["table_AppearanceCatalogData"] = {
        TableKey = "id",
        SubTableNames = {
			"AppearanceCatalogData_Jewelry",
			"AppearanceCatalogData_Shuttle",
			"AppearanceCatalogData_Suit",
			"AppearanceCatalogData_Vehicle",
        }
    },
    ["table_AppearanceCatalogMainData"] = {
        TableKey = "id",
        SubTableNames = {
			"AppearanceCatalogMainData",
        }
    },
    ["table_AppearanceRoadData"] = {
        TableKey = "level",
        SubTableNames = {
			"AppearanceRoadData",
        }
    },
    ["table_AppearanceRoadLevelData"] = {
        TableKey = "level",
        SubTableNames = {
			"AppearanceRoadLevelData",
        }
    },
    ["table_AreaChatGroupList"] = {
        TableKey = "id",
        SubTableNames = {
			"AreaChatGroupList",
        }
    },
    ["table_ArenaBanPick"] = {
        TableKey = "gameType",
        SubTableNames = {
			"ArenaBanPick",
        }
    },
    ["table_ArenaBuffDamageEffect"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaBuffDamageEffectRow",
        }
    },
    ["table_ArenaBuffData"] = {
        TableKey = "id,level,in_table_game_type",
        SubTableNames = {
			"ArenaBuffData_Card_Attr_5v5",
			"ArenaBuffData_Card_Function_5v5",
			"ArenaBuffData_FB",
			"ArenaBuffData_HZBalance",
        }
    },
    ["table_ArenaBuffToTowerData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaBuffToTowerData",
        }
    },
    ["table_ArenaCardControlData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardControlData",
        }
    },
    ["table_ArenaCardControlSuit"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardControlSuit",
        }
    },
    ["table_ArenaCardData"] = {
        TableKey = "id,inTableGameType",
        SubTableNames = {
			"ArenaCardData_5v5SpecRole",
        }
    },
    ["table_ArenaCardHotValueData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardHotValueData",
        }
    },
    ["table_ArenaCardLimitGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardLimitGroupData",
        }
    },
    ["table_ArenaCardPackUpgradeLimitData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardPackUpgradeLimitData",
        }
    },
    ["table_ArenaCardRegulationRank"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardRegulationRank",
        }
    },
    ["table_ArenaCardRegulationRound"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardRegulationRound",
        }
    },
    ["table_ArenaCardSuitBuffData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardSuitBuffData_HZ",
        }
    },
    ["table_ArenaCardTagData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCardTagData",
        }
    },
    ["table_ArenaCardTrainGroupData"] = {
        TableKey = "heroId",
        SubTableNames = {
			"ArenaCardTrainGroupData",
        }
    },
    ["table_ArenaCombatEffectivenessTopThreshold"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCombatEffectivenessTopThresholdData",
			"HOKCombatEffectivenessTopThresholdData",
        }
    },
    ["table_ArenaCommonAttributeBarConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaCommonAttributeBarConf",
        }
    },
    ["table_ArenaGiftConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaGiftConf",
        }
    },
    ["table_ArenaHOKBannedAndReportData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHOKBannedAndReportData",
        }
    },
    ["table_ArenaHeroCardHot"] = {
        TableKey = "cardId",
        SubTableNames = {
			"ArenaHeroCardHot",
        }
    },
    ["table_ArenaHeroCardReLi"] = {
        TableKey = "heroId",
        SubTableNames = {
			"ArenaHeroCardReLi",
        }
    },
    ["table_ArenaHeroCardReplace"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroCardReplaceData",
        }
    },
    ["table_ArenaHeroCardSuit"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroCardSuit",
        }
    },
    ["table_ArenaHeroOccupyData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroOccupyData",
        }
    },
    ["table_ArenaHeroStarDegreeFactor"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroStarDegreeFactorData",
        }
    },
    ["table_ArenaHeroStarGeneralLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"ArenaHeroStarGeneralLevelData",
        }
    },
    ["table_ArenaHeroStarHeroLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"ArenaHeroStarHeroLevelData",
        }
    },
    ["table_ArenaHeroStarLevelQuest"] = {
        TableKey = "questID",
        SubTableNames = {
			"ArenaHeroStarLevelQuestData",
        }
    },
    ["table_ArenaHeroStarLevelReward"] = {
        TableKey = "rewardID",
        SubTableNames = {
			"ArenaHeroStarLevelRewardData",
        }
    },
    ["table_ArenaHeroStarMisc"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroStarMiscData",
        }
    },
    ["table_ArenaHeroStarPerfScoreFactor"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroStarPerfScoreFactorData",
        }
    },
    ["table_ArenaHeroStarRegulationFactor"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroStarRegulationFactorData",
        }
    },
    ["table_ArenaHeroStarRewardReplace"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroStarRewardReplaceData",
        }
    },
    ["table_ArenaHeroTrainData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHeroTrainData",
        }
    },
    ["table_ArenaHighlight"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaHighlight",
        }
    },
    ["table_ArenaLevelPoolConf"] = {
        TableKey = "level_pool_ID",
        SubTableNames = {
			"ArenaLevelPoolConf",
        }
    },
    ["table_ArenaMatchGroupConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaMatchGroupConf",
        }
    },
    ["table_ArenaAvatarSkillConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaMonsterAvatarSkillConfData",
        }
    },
    ["table_ArenaMonsterData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaMonsterData_3V3",
        }
    },
    ["table_ArenaMysteryChestData"] = {
        TableKey = "roundIndex",
        SubTableNames = {
			"ArenaMysteryChestData",
        }
    },
    ["table_ArenaNoStackUIConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaNoStackUIConf",
        }
    },
    ["table_ArenaNoticeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaNoticeData",
        }
    },
    ["table_ArenaQualifyRoundConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaQualifyRoundConf",
        }
    },
    ["table_ArenaRandomEventAIConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaRandomEventAIConfig",
        }
    },
    ["table_ArenaRandomEventLevelPointLocationConfig"] = {
        TableKey = "level_id",
        SubTableNames = {
			"ArenaRandomEventLevelPointLocationConfig",
        }
    },
    ["table_ArenaRandomEventMatchOpen"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaRandomEventMatchOpen",
        }
    },
    ["table_ArenaRandomEventMonsterRefreshConfig"] = {
        TableKey = "round_id",
        SubTableNames = {
			"ArenaRandomEventMonsterRefreshConfig",
        }
    },
    ["table_ArenaShardMallConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaShardMallConf",
        }
    },
    ["table_ArenaShowEffectBigBoomData"] = {
        TableKey = "bigBoomType",
        SubTableNames = {
			"ArenaShowEffectBigBoom",
        }
    },
    ["table_ArenaSpeedFixData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSpeedFixData",
        }
    },
    ["table_ArenaSprayPaint"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSprayPaint",
        }
    },
    ["table_ArenaStrategyMapInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaStrategyMapInfo",
        }
    },
    ["table_ArenaStrategyMiscConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaStrategyMiscConfig",
        }
    },
    ["table_ArenaStrategyRhythm"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaStrategyRhythm",
        }
    },
    ["table_ArenaSummonAIConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaSummonAIConfig",
        }
    },
    ["table_ArenaWeaponSkin"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaWeaponSkin",
        }
    },
    ["table_ArenaWishData"] = {
        TableKey = "id",
        SubTableNames = {
			"ArenaWishData",
        }
    },
    ["table_AuditVersionBanData"] = {
        TableKey = "id",
        SubTableNames = {
			"AuditVersionBanData",
        }
    },
    ["table_AvatarChunkData"] = {
        TableKey = "itemId",
        SubTableNames = {
			"AvatarChunkData",
			"AvatarChunkData_Farm",
			"AvatarChunkData_StarShuttle",
			"AvatarChunkData_Vehicle",
        }
    },
    ["table_AvatarDanceData"] = {
        TableKey = "itemid",
        SubTableNames = {
			"AvatarDanceData",
        }
    },
    ["table_BIData"] = {
        TableKey = "id",
        SubTableNames = {
			"BIData",
        }
    },
    ["table_BackpackCloakroomUnlock"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackCloakroomUnlock",
        }
    },
    ["table_ResBagFilterTag"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackFilterTag",
        }
    },
    ["table_ResBagFilterTitleTag"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackFilterTitleTag",
        }
    },
    ["table_BackpackItemDressUpValueConf"] = {
        TableKey = "type",
        SubTableNames = {
			"BackpackItemDressUpValueConfData",
        }
    },
    ["table_BackpackItemEffectData"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackItemEffectData",
			"BackpackItemEffectData_Shuttle",
        }
    },
    ["table_BackpackItemQuality"] = {
        TableKey = "quality",
        SubTableNames = {
			"BackpackItemQuality",
        }
    },
    ["table_BackpackItemTypeDef"] = {
        TableKey = "type",
        SubTableNames = {
			"BackpackItemTypeDef",
        }
    },
    ["table_BackpackItemVideoData"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackItemVideoData",
        }
    },
    ["table_BackpackItem"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackItem_Action1P",
			"BackpackItem_Action2P",
			"BackpackItem_AppIcon",
			"BackpackItem_Arena",
			"BackpackItem_AttackAnim",
			"BackpackItem_BackOrnament",
			"BackpackItem_Badge",
			"BackpackItem_COCAction1P",
			"BackpackItem_COCSuit",
			"BackpackItem_ChaseBossAction1P",
			"BackpackItem_ChaseBossSkin",
			"BackpackItem_ChaseBossUnlock",
			"BackpackItem_ChaseProp",
			"BackpackItem_ChasePropSkin",
			"BackpackItem_ChasePropUnlock",
			"BackpackItem_ChatBubble",
			"BackpackItem_ChatColorFont",
			"BackpackItem_CreatorHomePageSkin",
			"BackpackItem_Currency",
			"BackpackItem_DailyBattleOrnamentationDataConf",
			"BackpackItem_Emoji",
			"BackpackItem_Face",
			"BackpackItem_FaceOrnament",
			"BackpackItem_Farm",
			"BackpackItem_FootPrint",
			"BackpackItem_Frame",
			"BackpackItem_FriendInviteBox",
			"BackpackItem_FriendOnlineReminder",
			"BackpackItem_Gloves",
			"BackpackItem_HandOrnament",
			"BackpackItem_HeadWear",
			"BackpackItem_HeroSkin",
			"BackpackItem_Identity",
			"BackpackItem_InteractiveProp",
			"BackpackItem_InteractiveToy",
			"BackpackItem_Item",
			"BackpackItem_Item_Card",
			"BackpackItem_Kart",
			"BackpackItem_LowerGarment",
			"BackpackItem_MVPAnim",
			"BackpackItem_Meeting",
			"BackpackItem_Mood",
			"BackpackItem_NR3E3Treasure",
			"BackpackItem_NR3E8",
			"BackpackItem_NR3E_DisguiseEffect",
			"BackpackItem_NR3E_Interactive",
			"BackpackItem_NR3E_Weapon",
			"BackpackItem_Nameplate",
			"BackpackItem_PackageCommon",
			"BackpackItem_PackageCommon_huoyueyunying",
			"BackpackItem_PackageCommon_shangyehuayunying",
			"BackpackItem_PackageCommon_tesewanfayunying",
			"BackpackItem_PackagePick",
			"BackpackItem_PackagePick_huoyueyunying",
			"BackpackItem_PackagePick_shangyehuayunying",
			"BackpackItem_PackagePick_tesewanfayunying",
			"BackpackItem_PackageRandom",
			"BackpackItem_PackageRandom_huoyueyunying",
			"BackpackItem_PackageRandom_shangyehuayunying",
			"BackpackItem_PackageRandom_tesewanfayunying",
			"BackpackItem_PackageShare",
			"BackpackItem_PersonalityState",
			"BackpackItem_Portrait",
			"BackpackItem_ProfileTheme",
			"BackpackItem_QualifyingCard",
			"BackpackItem_ReportAnim",
			"BackpackItem_Shuttle",
			"BackpackItem_SpecialSpeak",
			"BackpackItem_Suit",
			"BackpackItem_TYC",
			"BackpackItem_Title",
			"BackpackItem_Title_Arena",
			"BackpackItem_Title_Chase",
			"BackpackItem_Title_huiyueshangyehua",
			"BackpackItem_Title_tesewanfayunying",
			"BackpackItem_UgcBadge",
			"BackpackItem_UgcCommon",
			"BackpackItem_UgcCreatorBadge",
			"BackpackItem_UpperGarment",
			"BackpackItem_Vehicle",
			"BackpackItem_VehicleDecarateItem",
			"BackpackItem_Velarium",
			"BackpackItem_Xiaowo",
			"BackpackItem_coc",
			"BackpackItem_huoyueyunying",
			"BackpackItem_shangyehuayunying",
			"BackpackItem_surrounding",
			"BackpackItem_tesewanfayunying",
        }
    },
    ["table_BackpackItemUGC"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackItem_UGC",
        }
    },
    ["table_VehicleDecorateMesh"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackItem_VehicleDecarateMesh",
        }
    },
    ["table_VehicleLevelup"] = {
        TableKey = "vehicleId",
        SubTableNames = {
			"BackpackItem_VehicleLevelup",
        }
    },
    ["table_VehicleSkill"] = {
        TableKey = "skillId",
        SubTableNames = {
			"BackpackItem_VehicleSkill",
        }
    },
    ["table_BackpackSundryConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackSundryConfig",
        }
    },
    ["table_ResBackpackTag"] = {
        TableKey = "id",
        SubTableNames = {
			"BackpackTagForLetsGo",
        }
    },
    ["table_BandsConcertConstConf"] = {
        TableKey = "key",
        SubTableNames = {
			"BandsConcertConstConfData",
        }
    },
    ["table_BandsMusicConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BandsMusicConfData",
        }
    },
    ["table_BandsMusicSoundTrackConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BandsMusicSoundTrackConfData",
        }
    },
    ["table_BankUnloadConfig"] = {
        TableKey = "bankName",
        SubTableNames = {
			"BankUnloadConfig",
        }
    },
    ["table_FinalAccountRewardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BattleFinalAccountRewardConf_main",
        }
    },
    ["table_BattleLikeContentData"] = {
        TableKey = "id",
        SubTableNames = {
			"BattleLikeContentData",
        }
    },
    ["table_BattlePassLevelRewardConf"] = {
        TableKey = "id,level",
        SubTableNames = {
			"BattlePassLevelRewardConf_main",
			"BattlePassLevelRewardConf_maingame",
			"BattlePassLevelRewardConf_moba",
			"BattlePassLevelRewardConf_nr3e_sheishilangren",
			"BattlePassLevelRewardConf_summerbp",
        }
    },
    ["table_BattlePassSeasonConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BattlePassSeasonConf_main",
			"BattlePassSeasonConf_maingame",
			"BattlePassSeasonConf_moba",
			"BattlePassSeasonConf_nr3e_sheishilangren",
        }
    },
    ["table_BattlePassSpineConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BattlePassSpine",
        }
    },
    ["table_BattlePassTaskRewardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BattlePassTaskRewardConf_moba",
        }
    },
    ["table_BattleResultSyncRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"BattleResultSyncRuleData",
        }
    },
    ["table_BehaviorScoreChangeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"BehaviorScoreChangeConfig",
        }
    },
    ["table_BioChaseRoleCfgData"] = {
        TableKey = "Id",
        SubTableNames = {
			"BioChaseRoleCfgData",
        }
    },
    ["table_BioChaseRoleCfgData_Fight"] = {
        TableKey = "Id",
        SubTableNames = {
			"BioChaseRoleCfgData_Fight",
        }
    },
    ["table_BioChaseRuleCfgData"] = {
        TableKey = "Id",
        SubTableNames = {
			"BioChaseRuleCfgData",
        }
    },
    ["table_BioChaseSkillCfgData"] = {
        TableKey = "Id",
        SubTableNames = {
			"BioChaseSkillCfgData",
        }
    },
    ["table_BioChaseSkillCfgData_Fight"] = {
        TableKey = "Id",
        SubTableNames = {
			"BioChaseSkillCfgData_Fight",
        }
    },
    ["table_BirthdayCardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"BirthdayCardConf",
        }
    },
    ["table_BirthdayMiscData"] = {
        TableKey = "Key",
        SubTableNames = {
			"BirthdayMiscData",
        }
    },
    ["table_BirthdayPrivilegeData"] = {
        TableKey = "id",
        SubTableNames = {
			"BirthdayPrivilegeData",
        }
    },
    ["table_BirthdayblessingData"] = {
        TableKey = "id",
        SubTableNames = {
			"BirthdayblessingData",
        }
    },
    ["table_BlockingWordData"] = {
        TableKey = "id",
        SubTableNames = {
			"BlockingWordData",
        }
    },
    ["table_BpmGamePropsData"] = {
        TableKey = "itemID",
        SubTableNames = {
			"BpmGamePropsData",
        }
    },
    ["table_BrGamePropsData"] = {
        TableKey = "itemID",
        SubTableNames = {
			"BrGamePropsData",
        }
    },
    ["table_BrPropAirDropData"] = {
        TableKey = "mapID",
        SubTableNames = {
			"BrPropAirDropData",
        }
    },
    ["table_BrPropRegionData"] = {
        TableKey = "mapID",
        SubTableNames = {
			"BrPropRegionData",
        }
    },
    ["table_BroadcastNoticeData"] = {
        TableKey = "id",
        SubTableNames = {
			"BroadcastNoticeData",
        }
    },
    ["table_BurdenReduceTaskConfData"] = {
        TableKey = "taskId",
        SubTableNames = {
			"BurdenReduceTaskConfData_default",
			"BurdenReduceTaskConfData_reserved1",
			"BurdenReduceTaskConfData_reserved2",
			"BurdenReduceTaskConfData_season",
			"BurdenReduceTaskConfData_trophy",
        }
    },
    ["table_BurdenReduceTaskGroupConfData"] = {
        TableKey = "type",
        SubTableNames = {
			"BurdenReduceTaskGroupConfData",
        }
    },
    ["table_CDNImageCommercializationData"] = {
        TableKey = "index",
        SubTableNames = {
			"CDNImageCommercializationData",
        }
    },
    ["table_CDNImageDownloadPriorityData"] = {
        TableKey = "resourcePriorityType",
        SubTableNames = {
			"CDNImageDownloadPriorityData",
        }
    },
    ["table_CDNImageResConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CDNImageResConfig",
        }
    },
    ["table_COCBattleMMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCBattleMiscConfig",
        }
    },
    ["table_COCBuildBuildingRecommendLocationConf"] = {
        TableKey = "BuildingType",
        SubTableNames = {
			"COCBuildBuildingRecommendLocationConf",
        }
    },
    ["table_COCDamageEffect"] = {
        TableKey = "id,level",
        SubTableNames = {
			"COCDamageEffectData_Hero",
        }
    },
    ["table_COCFriendVillagerDressFashionValueConf"] = {
        TableKey = "fashionLevel",
        SubTableNames = {
			"COCFriendVillagerDressFashionValueConf",
        }
    },
    ["table_COCGuideMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCGuideMiscConf",
        }
    },
    ["table_COCGuideResetConf"] = {
        TableKey = "guideID",
        SubTableNames = {
			"COCGuideResetConf",
        }
    },
    ["table_COCHeroSkillConf"] = {
        TableKey = "skillid",
        SubTableNames = {
			"COCHeroSkillConfData",
        }
    },
    ["table_COCHeroSystemData"] = {
        TableKey = "id",
        SubTableNames = {
			"COCHeroSystemData",
        }
    },
    ["table_COCLevelFactor"] = {
        TableKey = "id",
        SubTableNames = {
			"COCLevelFactor",
        }
    },
    ["table_COCMonsterAttr"] = {
        TableKey = "id",
        SubTableNames = {
			"COCMonsterAttrData",
        }
    },
    ["table_COCMonsterAttrName"] = {
        TableKey = "id",
        SubTableNames = {
			"COCMonsterAttrNameData",
        }
    },
    ["table_COCObstacleConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCObstacleConf",
        }
    },
    ["table_COCPrisonLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"COCPrisonLevelConf",
        }
    },
    ["table_COCVillagerLVConf"] = {
        TableKey = "villagerID,level",
        SubTableNames = {
			"COCVillagerLVConf",
        }
    },
    ["table_COCVillagerManagementAbilityConf"] = {
        TableKey = "id",
        SubTableNames = {
			"COCVillagerManagementAbilityConf",
        }
    },
    ["table_CameraFilter"] = {
        TableKey = "ID",
        SubTableNames = {
			"CameraFilter",
        }
    },
    ["table_CameraFrame"] = {
        TableKey = "id",
        SubTableNames = {
			"CameraFrame",
        }
    },
    ["table_CaptureShadowItemShowData"] = {
        TableKey = "type",
        SubTableNames = {
			"CaptureShadowItemShowData",
        }
    },
    ["table_CaptureShadowMapData"] = {
        TableKey = "id",
        SubTableNames = {
			"CaptureShadowMapData",
        }
    },
    ["table_CaptureShadowRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"CaptureShadowRewardData",
        }
    },
    ["table_CaptureShadowShoeData"] = {
        TableKey = "id",
        SubTableNames = {
			"CaptureShadowShoeData",
        }
    },
    ["table_CardBagConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"CardBagConfigData",
        }
    },
    ["table_CardBagExtOutPutConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CardBagExtOutPutData",
        }
    },
    ["table_CardBagPoolConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"CardBagPoolConfigData",
        }
    },
    ["table_CardSpecialPoolConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"CardSpecialPoolConfigData",
        }
    },
    ["table_CaughtPenguinCfgData"] = {
        TableKey = "Id",
        SubTableNames = {
			"CaughtPenguinCfgData",
        }
    },
    ["table_ChannelDisableSceneData"] = {
        TableKey = "channelSceneId",
        SubTableNames = {
			"ChannelDisableSceneData",
        }
    },
    ["table_ChannelEntranceData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChannelEntranceData_ForQQ",
			"ChannelEntranceData_WeChat",
        }
    },
    ["table_CharAnimSpeedUpData"] = {
        TableKey = "Id",
        SubTableNames = {
			"CharAnimSpeedUpConfig",
        }
    },
    ["table_CharacterVoiceConfig"] = {
        TableKey = "characterID",
        SubTableNames = {
			"CharacterVoiceConfig",
        }
    },
    ["table_CharacterVoiceConfigChase"] = {
        TableKey = "characterID",
        SubTableNames = {
			"CharacterVoiceConfigChase",
        }
    },
    ["table_CharacterVoiceConfigFPS"] = {
        TableKey = "characterID",
        SubTableNames = {
			"CharacterVoiceConfigFPS",
        }
    },
    ["table_CharacterVoiceConfigMOBA"] = {
        TableKey = "characterID",
        SubTableNames = {
			"CharacterVoiceConfigMOBA",
        }
    },
    ["table_CharacterVoiceIgnoreFPS"] = {
        TableKey = "gameTypeId",
        SubTableNames = {
			"CharacterVoiceIgnoreFPS",
        }
    },
    ["table_CharacterVoiceParam"] = {
        TableKey = "paramName",
        SubTableNames = {
			"CharacterVoiceParam",
        }
    },
    ["table_CharacterVoiceParamChase"] = {
        TableKey = "paramName",
        SubTableNames = {
			"CharacterVoiceParamChase",
        }
    },
    ["table_CharacterVoiceParamFPS"] = {
        TableKey = "paramName",
        SubTableNames = {
			"CharacterVoiceParamFPS",
        }
    },
    ["table_ChaseActivityBannerJumpStyleConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseActivityBannerJumpStyleConf",
        }
    },
    ["table_ChaseAnimSkinConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseAnimSkinConfig",
        }
    },
    ["table_ChaseBossAction"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseBossActionData",
        }
    },
    ["table_ChaseBossCustomSFXConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseBossCustomSFXConfig",
        }
    },
    ["table_ChaseBossDecorationConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseBossDecorationConfig",
        }
    },
    ["table_ChaseBossDecorationReverseConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseBossDecorationReverseConfig",
        }
    },
    ["table_ChaseBossSkinConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseBossSkinConfig",
        }
    },
    ["table_ChaseBossSkinReverseData"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseBossSkinReverseData",
        }
    },
    ["table_ChaseDefaultCameraTypeCfg"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseCameraDefaultTypeCfg",
        }
    },
    ["table_ChaseCameraParamCfg"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseCameraParamCfg",
        }
    },
    ["table_ChaseCameraTypeCfg"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseCameraTypeCfg",
        }
    },
    ["table_ChaseCoinConfig"] = {
        TableKey = "Config",
        SubTableNames = {
			"ChaseCoinConfig",
        }
    },
    ["table_ChaseComprehensiveScoreBossTypeConfig"] = {
        TableKey = "type",
        SubTableNames = {
			"ChaseComprehensiveScoreBossTypeConfig",
        }
    },
    ["table_ChaseComprehensiveScoreInterpolationConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseComprehensiveScoreInterpolationConfig",
        }
    },
    ["table_ChaseComprehensiveScoreOtherParamConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseComprehensiveScoreOtherParamConfig",
        }
    },
    ["table_ChaseComprehensiveScoreParamsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseComprehensiveScoreParamsConfig",
        }
    },
    ["table_ChaseComprehensiveScorePropTypeConfig"] = {
        TableKey = "type",
        SubTableNames = {
			"ChaseComprehensiveScorePropTypeConfig",
        }
    },
    ["table_ChaseComprehensiveScoreWeightsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseComprehensiveScoreWeightsConfig",
        }
    },
    ["table_ChaseGameplayAbility"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseGameplayAbility",
        }
    },
    ["table_ChaseHealTeammates"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseHealTeammates",
        }
    },
    ["table_ChaseIDMasteryBoss"] = {
        TableKey = "ActorID",
        SubTableNames = {
			"ChaseIDMasteryBoss",
        }
    },
    ["table_ChaseIDMasteryBossPublicSkill"] = {
        TableKey = "SkillID",
        SubTableNames = {
			"ChaseIDMasteryBossPublicSkill",
        }
    },
    ["table_ChaseIDMasteryBossSkill"] = {
        TableKey = "SkillID",
        SubTableNames = {
			"ChaseIDMasteryBossSkill",
        }
    },
    ["table_ChaseIDMasteryIDTag"] = {
        TableKey = "TagID",
        SubTableNames = {
			"ChaseIDMasteryIDTag",
        }
    },
    ["table_ChaseIDMasteryLimitFree"] = {
        TableKey = "Id",
        SubTableNames = {
			"ChaseIDMasteryLimitFree",
        }
    },
    ["table_ChaseIDMasteryLowAcceleration"] = {
        TableKey = "lowAccId",
        SubTableNames = {
			"ChaseIDMasteryLowAcceleration",
        }
    },
    ["table_ChaseIDMasteryMisc"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseIDMasteryMisc",
        }
    },
    ["table_ChaseIDMasteryOldPlayerUnlock"] = {
        TableKey = "ActorId",
        SubTableNames = {
			"ChaseIDMasteryOldPlayerUnlock",
        }
    },
    ["table_ChaseIDMasteryPlayer"] = {
        TableKey = "ActorID",
        SubTableNames = {
			"ChaseIDMasteryPlayer",
        }
    },
    ["table_ChaseIDMasteryPlayerSkill"] = {
        TableKey = "SkillID",
        SubTableNames = {
			"ChaseIDMasteryPlayerSkill",
        }
    },
    ["table_ChaseIDMasteryProtection"] = {
        TableKey = "protectionId",
        SubTableNames = {
			"ChaseIDMasteryProtection",
        }
    },
    ["table_ChaseIDMasteryRankIcon"] = {
        TableKey = "IconLevel",
        SubTableNames = {
			"ChaseIDMasteryRankIcon",
        }
    },
    ["table_ChaseIDMasteryScoreRank"] = {
        TableKey = "rankLevel",
        SubTableNames = {
			"ChaseIDMasteryScoreRank",
        }
    },
    ["table_ChaseIDMasteryUnlock"] = {
        TableKey = "ActorId",
        SubTableNames = {
			"ChaseIDMasteryUnlock",
        }
    },
    ["table_ChaseIdentityBiographyConfig"] = {
        TableKey = "ActorId,biographyId",
        SubTableNames = {
			"ChaseIdentityBiographyConfig",
        }
    },
    ["table_ChaseIdentityMvpAddProficiencyConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseIdentityMvpAddProficiencyConfig",
        }
    },
    ["table_ChaseIdentityProficiencyConfig"] = {
        TableKey = "ActorId,ProficiencyId",
        SubTableNames = {
			"ChaseIdentityProficiencyConfig",
        }
    },
    ["table_ChaseIdentityProficiencyMiscConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChaseIdentityProficiencyMiscConfig",
        }
    },
    ["table_ChaseInLevelTargetCfg"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseInLevelTargetCfg",
        }
    },
    ["table_ChaseMaxHP"] = {
        TableKey = "MatchType",
        SubTableNames = {
			"ChaseMaxHP",
        }
    },
    ["table_ChaseMinimap"] = {
        TableKey = "MapID",
        SubTableNames = {
			"ChaseMinimap",
        }
    },
    ["table_ChaseMinimapPoint"] = {
        TableKey = "MapID",
        SubTableNames = {
			"ChaseMinimapPointData",
        }
    },
    ["table_ChaseProgressionActorToCardPool"] = {
        TableKey = "Id",
        SubTableNames = {
			"ChaseProgressionActorToCardPool",
        }
    },
    ["table_ChaseProgressionBossBehaviorScore1V4"] = {
        TableKey = "Behavior",
        SubTableNames = {
			"ChaseProgressionBossBehaviorScore1V4",
        }
    },
    ["table_ChaseProgressionBossBehaviorScore3V9"] = {
        TableKey = "Behavior",
        SubTableNames = {
			"ChaseProgressionBossBehaviorScore3V9",
        }
    },
    ["table_ChaseProgressionBossGrade1V4"] = {
        TableKey = "Grade",
        SubTableNames = {
			"ChaseProgressionBossGrade1V4",
        }
    },
    ["table_ChaseProgressionBossGrade3V9"] = {
        TableKey = "Grade",
        SubTableNames = {
			"ChaseProgressionBossGrade3V9",
        }
    },
    ["table_ChaseProgressionCard"] = {
        TableKey = "CardId",
        SubTableNames = {
			"ChaseProgressionCard",
        }
    },
    ["table_ChaseProgressionCardPackage"] = {
        TableKey = "PackageId",
        SubTableNames = {
			"ChaseProgressionCardPackage",
        }
    },
    ["table_ChaseProgressionExclusiveCardPool"] = {
        TableKey = "PoolId",
        SubTableNames = {
			"ChaseProgressionExclusiveCardPool",
        }
    },
    ["table_ChaseProgressionGeneralCardPool"] = {
        TableKey = "PoolId",
        SubTableNames = {
			"ChaseProgressionGeneralCardPool",
        }
    },
    ["table_ChaseProgressionOpenSetting"] = {
        TableKey = "MatchType",
        SubTableNames = {
			"ChaseProgressionOpenSetting",
        }
    },
    ["table_ChaseProgressionPlayerBehaviorScore1V4"] = {
        TableKey = "Behavior",
        SubTableNames = {
			"ChaseProgressionPlayerBehaviorScore1V4",
        }
    },
    ["table_ChaseProgressionPlayerBehaviorScore3V9"] = {
        TableKey = "Behavior",
        SubTableNames = {
			"ChaseProgressionPlayerBehaviorScore3V9",
        }
    },
    ["table_ChaseProgressionPlayerGrade1V4"] = {
        TableKey = "Grade",
        SubTableNames = {
			"ChaseProgressionPlayerGrade1V4",
        }
    },
    ["table_ChaseProgressionPlayerGrade3V9"] = {
        TableKey = "Grade",
        SubTableNames = {
			"ChaseProgressionPlayerGrade3V9",
        }
    },
    ["table_ChaseProgressionPropCardPool"] = {
        TableKey = "PoolId",
        SubTableNames = {
			"ChaseProgressionPropCardPool",
        }
    },
    ["table_ChasePropSkinConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChasePropSkinConfig",
        }
    },
    ["table_ChasePropSkinReverseData"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChasePropSkinReverseData",
        }
    },
    ["table_ChaseQuickChatMsg"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseQuickChatMsgData",
        }
    },
    ["table_ChaseSkinCustomKVPairs"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseSkinCustomKVPairs",
        }
    },
    ["table_ChaseSkinParticleConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseSkinParticleConfig",
        }
    },
    ["table_ChaseSpeedUpToEndCfg"] = {
        TableKey = "ID",
        SubTableNames = {
			"ChaseSpeedUpToEndCfg",
        }
    },
    ["table_ChaseTagCountLimit"] = {
        TableKey = "UniqueID",
        SubTableNames = {
			"ChaseTagCountLimitData",
        }
    },
    ["table_ChaseNewGuideHelper_Chase"] = {
        TableKey = "ID",
        SubTableNames = {
			"Chase_ChaseNewGuideHelper_Chase",
        }
    },
    ["table_ResChatActionConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatActionConfig",
        }
    },
    ["table_ChatChangeAnimTimeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatChangeAnimTimeConfig",
        }
    },
    ["table_ChatClinetConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatClientConf",
        }
    },
    ["table_ChatConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatConf",
        }
    },
    ["table_ChatContentEntryData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatContentEntryData",
        }
    },
    ["table_ChatEmojiCellList"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatEmojiCellList",
        }
    },
    ["table_ChatIPEmoji"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatIPEmoji",
        }
    },
    ["table_ChatInteractiveData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatInteractiveData",
        }
    },
    ["table_ChatLobbyInteractionSystemConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatLobbyInteractionSystemConf",
        }
    },
    ["table_ChatMagicVoiceList"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatMagicVoiceList",
        }
    },
    ["table_ChatModuleConfigData"] = {
        TableKey = "moduleType",
        SubTableNames = {
			"ChatModuleConfigData",
        }
    },
    ["table_ChatMsgTypeConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatMsgTypeConfigData",
        }
    },
    ["table_ChatOutGameMagicVoiceList"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatOutGameMagicVoice",
        }
    },
    ["table_ChatRoomConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatRoomConf",
        }
    },
    ["table_ChatSystemMsgConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatSystemMsgConfDataForLetsGo",
        }
    },
    ["table_ChatTypeChatGroupConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatTypeChatGroupConfigData",
        }
    },
    ["table_ChatTypeConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatTypeConfigData",
        }
    },
    ["table_ChatTypeFilterData"] = {
        TableKey = "id",
        SubTableNames = {
			"ChatTypeFilterData",
        }
    },
    ["table_CheckInPlanActivityDrawConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CheckInPlanActivityDrawConfData",
        }
    },
    ["table_CheckInPlanActivityTextConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CheckInPlanActivityTextConfData",
        }
    },
    ["table_CheckInPlanActivityWeekConf"] = {
        TableKey = "week",
        SubTableNames = {
			"CheckInPlanActivityWeekConfData",
        }
    },
    ["table_CheckInPlanRewardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CheckInPlanRewardConfData",
        }
    },
    ["table_ChoiceConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ChoiceConfigData",
        }
    },
    ["table_ChunkDownloadSpeedData"] = {
        TableKey = "networkType",
        SubTableNames = {
			"ChunkDownloadSpeedData",
        }
    },
    ["table_ChunkDownloadTaskData"] = {
        TableKey = "level",
        SubTableNames = {
			"ChunkDownloadTaskData",
        }
    },
    ["table_ChunkDownloadWhiteListData"] = {
        TableKey = "sceneType",
        SubTableNames = {
			"ChunkDownloadWhiteListData",
        }
    },
    ["table_ChunkGroupCatalogue"] = {
        TableKey = "Tabid",
        SubTableNames = {
			"ChunkGroupCatalogue",
        }
    },
    ["table_ChunkGroupData"] = {
        TableKey = "groupId",
        SubTableNames = {
			"ChunkGroupData",
			"ChunkGroupData_auo",
        }
    },
    ["table_ChunkGroupDataVA"] = {
        TableKey = "groupId",
        SubTableNames = {
			"ChunkGroupDataVA",
        }
    },
    ["table_CleaningBehavior"] = {
        TableKey = "id",
        SubTableNames = {
			"CleaningBehaviorData",
        }
    },
    ["table_CleaningCondition"] = {
        TableKey = "id",
        SubTableNames = {
			"CleaningConditionData",
        }
    },
    ["table_CleaningGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"CleaningGroupData",
        }
    },
    ["table_ClientKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"ClientKVConfForLetsGo",
			"ClientKVConfForLetsGo_Lightning",
        }
    },
    ["table_ClientKVConf_MainGame"] = {
        TableKey = "key",
        SubTableNames = {
			"ClientKVConfForLetsGo_MainGame",
        }
    },
    ["table_ClientLocalKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"ClientLocalKVConfForLetsGo",
        }
    },
    ["table_ClientNetTextConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ClientNetTextConfig",
        }
    },
    ["table_ClientRuleTextData"] = {
        TableKey = "id,type",
        SubTableNames = {
			"ClientRuleTextData",
        }
    },
    ["table_ClientServerConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ClientServerConfig",
        }
    },
    ["table_ClientTeamShowKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"ClientTeamShowKVConfForLetsGo",
        }
    },
    ["table_ClubCommonConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ClubCommonConf",
        }
    },
    ["table_ClubIconData"] = {
        TableKey = "id",
        SubTableNames = {
			"ClubIconData",
        }
    },
    ["table_ClubLabelData"] = {
        TableKey = "id",
        SubTableNames = {
			"ClubLabelData",
        }
    },
    ["table_ClubLogConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ClubLogConfData",
        }
    },
    ["table_ClubMemberActivitySortData"] = {
        TableKey = "id",
        SubTableNames = {
			"ClubMemberActivitySortData",
        }
    },
    ["table_ClubRecommendSortData"] = {
        TableKey = "id",
        SubTableNames = {
			"ClubRecommendSortData",
        }
    },
    ["table_CocGMUserCopyData"] = {
        TableKey = "id",
        SubTableNames = {
			"CocGMUserCopyData",
        }
    },
    ["table_CommercialConfFieldMeta"] = {
        TableKey = "fieldTypeId",
        SubTableNames = {
			"CommercialConfFieldMeta",
        }
    },
    ["table_CommunityAllLevelConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunityAllLevelConf",
        }
    },
    ["table_CommunityDynamicLevelConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunityDynamicLevelConf",
        }
    },
    ["table_CommunityEntraceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunityEntraceConf",
        }
    },
    ["table_CommunityEntraceJumpConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunityEntraceJumpConf",
        }
    },
    ["table_CommunityInteractionLoadConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunityInteractionLoadConf",
        }
    },
    ["table_CommunityLevelLoadConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunityLevelLoadConf",
        }
    },
    ["table_CommunityPropLoadConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunityPropLoadConf",
        }
    },
    ["table_CommunitySwitchCDNTextureConf"] = {
        TableKey = "id",
        SubTableNames = {
			"CommunitySwitchCDNTextureConf",
        }
    },
    ["table_CompetitionMedalRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"CompetitionMedalRewardData",
        }
    },
    ["table_CompetitionRankRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"CompetitionRankRewardData",
        }
    },
    ["table_CompetitionScoreRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"CompetitionScoreRewardData",
        }
    },
    ["table_CompetitionWarmUpActivityConfData"] = {
        TableKey = "activityId,stageId",
        SubTableNames = {
			"CompetitionWarmUpActivityConfData",
        }
    },
    ["table_CompetitionWarmUpScoreConfData"] = {
        TableKey = "activityId,id",
        SubTableNames = {
			"CompetitionWarmUpScoreConfData",
        }
    },
    ["table_ConanActivityClientKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"ConanActivityClientKVConf",
        }
    },
    ["table_ConanQuizConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ConanQuizConf",
        }
    },
    ["table_ConanVoiceCaidanData"] = {
        TableKey = "id",
        SubTableNames = {
			"ConanVoiceCaidanData",
        }
    },
    ["table_ConanVoiceItemData"] = {
        TableKey = "id",
        SubTableNames = {
			"ConanVoiceItemData",
        }
    },
    ["table_ConcertConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ConcertConfigData",
        }
    },
    ["table_ConcertExpressionConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ConcertExpressionConfigData",
        }
    },
    ["table_ConcertLightBoardConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ConcertLightBoardConfigData",
        }
    },
    ["table_ConcertStarConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ConcertStarConfigData",
        }
    },
    ["table_ConcertTimesConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"ConcertTimesConfigData",
        }
    },
    ["table_ConditionLogicConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ConditionLogicConfig",
        }
    },
    ["table_ConnanActivityExchangePosPool"] = {
        TableKey = "id",
        SubTableNames = {
			"ConnanActivityExchangePosPool",
        }
    },
    ["table_CookSysConf"] = {
        TableKey = "ID",
        SubTableNames = {
			"CookSysConf",
        }
    },
    ["table_DeviceModelWhiteListData"] = {
        TableKey = "id",
        SubTableNames = {
			"CpuHardwareBlackListData",
			"DeviceModelWhiteListData",
        }
    },
    ["table_CreateAvatarConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"CreateAvatarConfigData",
        }
    },
    ["table_CrossRouteConf"] = {
        TableKey = "idx",
        SubTableNames = {
			"CrossRouteConfData",
        }
    },
    ["table_CrossRpcFilterConf"] = {
        TableKey = "idx",
        SubTableNames = {
			"CrossRpcFilterConfData",
        }
    },
    ["table_CsAdditionalDataConf"] = {
        TableKey = "serverType",
        SubTableNames = {
			"CsAdditionalDataConfData",
        }
    },
    ["table_CupAdditionConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupAdditionConfigData",
        }
    },
    ["table_CupAdditionFashionValueConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupAdditionFashionValueConfigData",
        }
    },
    ["table_CupAdditionPlayModeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupAdditionPlayModeConfigData",
        }
    },
    ["table_CupAdditionRelationConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupAdditionRelationConfigData",
        }
    },
    ["table_CupDailyTaskConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupDailyTaskConfigData",
        }
    },
    ["table_CupDailyTaskItemShowConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupDailyTaskItemShowConfig",
        }
    },
    ["table_CupRuleConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupRuleConfigData",
        }
    },
    ["table_CupTaskTabConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupTaskTabData",
        }
    },
    ["table_CupsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupsConfigData",
        }
    },
    ["table_CupsRoundConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CupsRoundConfig",
        }
    },
    ["table_CustomAbilityEnergyData"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomAbilityEnergyData",
        }
    },
    ["table_CustomAnimConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomAnimConf",
        }
    },
    ["table_CustomConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomConfigData",
        }
    },
    ["table_CustomKeyMatchTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomKeyMatchTypeData",
        }
    },
    ["table_CustomModelingMapConfig"] = {
        TableKey = "sourceTypeId",
        SubTableNames = {
			"CustomModelingMapConfig",
        }
    },
    ["table_CustomRoomData"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomRoomData",
        }
    },
    ["table_CustomRoomData_NR3E3"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomRoomData_NR3E3",
        }
    },
    ["table_CustomRoomQuickEmoji"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomRoomQuickEmojiConf",
        }
    },
    ["table_QuickTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomRoomQuickTextConf",
			"QuickTextConfDataInLevel",
			"QuickTextConfDataLobby",
			"QuickTextConfDataOther",
        }
    },
    ["table_CustomRoom_NR3E3CampConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomRoom_NR3E3CampConfig",
        }
    },
    ["table_CustomRoom_NR3E3VocationGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomRoom_NR3E3VocationGroup",
        }
    },
    ["table_CustomRoom_NR3E3_EasterEggPackData"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomRoom_NR3E3_EasterEggPackData",
        }
    },
    ["table_CustomerServiceEntries"] = {
        TableKey = "id",
        SubTableNames = {
			"CustomerServiceEntries",
        }
    },
    ["table_DDPHitInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"DDPHitInfo",
        }
    },
    ["table_DDPPoisonCircleInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"DDPPoisonCircleInfo",
        }
    },
    ["table_DDPSkillInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"DDPSkillInfo",
        }
    },
    ["table_DDPTornadoInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"DDPTornadoInfo",
        }
    },
    ["table_DSDevConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"DSDevConfig",
        }
    },
    ["table_DailyGift"] = {
        TableKey = "id",
        SubTableNames = {
			"DailyGift",
        }
    },
    ["table_ResDailyVictoryChestData"] = {
        TableKey = "id",
        SubTableNames = {
			"DailyVictoryChestData",
        }
    },
    ["table_DanMuCfg"] = {
        TableKey = "id",
        SubTableNames = {
			"DanMuCfg",
        }
    },
    ["table_DanMuUgcBlockCfg"] = {
        TableKey = "id,type",
        SubTableNames = {
			"DanMuUgcBlockCfg",
        }
    },
    ["table_DanceOutfitConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"DanceOutfitConfData",
        }
    },
    ["table_DanceOutfitItemConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"DanceOutfitItemConfData",
        }
    },
    ["table_DanceOutfitKeywordData"] = {
        TableKey = "id",
        SubTableNames = {
			"DanceOutfitKeyword",
        }
    },
    ["table_DanceOutfitStyleData"] = {
        TableKey = "id",
        SubTableNames = {
			"DanceOutfitStyle",
        }
    },
    ["table_DeviceAspectRatioData"] = {
        TableKey = "id",
        SubTableNames = {
			"DeviceAspectRatioData_Android",
			"DeviceAspectRatioData_IOS",
        }
    },
    ["table_DeviceFoldListData"] = {
        TableKey = "id",
        SubTableNames = {
			"DeviceFoldList",
        }
    },
    ["table_DeviceForceResetList"] = {
        TableKey = "id",
        SubTableNames = {
			"DeviceForceResetList",
        }
    },
    ["table_DeviceSwitchSceneQualityTip"] = {
        TableKey = "id",
        SubTableNames = {
			"DeviceSwitchSceneQualityTip",
        }
    },
    ["table_DfBuyRespawnData"] = {
        TableKey = "packageID",
        SubTableNames = {
			"DfBuyRespawnData",
        }
    },
    ["table_DfPackageData"] = {
        TableKey = "packageID",
        SubTableNames = {
			"DfPackageData",
        }
    },
    ["table_DfStoreItemData"] = {
        TableKey = "itemID",
        SubTableNames = {
			"DfStoreItemData",
        }
    },
    ["table_DialogueStageData"] = {
        TableKey = "id",
        SubTableNames = {
			"DialogueStageData",
        }
    },
    ["table_DialogueStepData"] = {
        TableKey = "id",
        SubTableNames = {
			"DialogueStepData",
        }
    },
    ["table_DialogueTalkConfData"] = {
        TableKey = "talkId",
        SubTableNames = {
			"DialogueTalkConfData",
        }
    },
    ["table_DialogueTriggerData"] = {
        TableKey = "triggerId",
        SubTableNames = {
			"DialogueTriggerData",
        }
    },
    ["table_DiscoveryGuidanceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"DiscoveryGuidanceConf",
        }
    },
    ["table_DisplayBoardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"DisplayBoardConfData",
        }
    },
    ["table_DisplayBoardOfficialTextData"] = {
        TableKey = "stringId",
        SubTableNames = {
			"DisplayBoardOfficialTextData",
        }
    },
    ["table_DisplayBoardTextData"] = {
        TableKey = "stringId",
        SubTableNames = {
			"DisplayBoardTextData",
        }
    },
    ["table_DisplayPointMapData"] = {
        TableKey = "id",
        SubTableNames = {
			"DisplayPointMapData_main",
        }
    },
    ["table_DoubleDanceMisc"] = {
        TableKey = "id",
        SubTableNames = {
			"DoubleDanceMiscData",
        }
    },
    ["table_DoubleDanceStartTip"] = {
        TableKey = "id",
        SubTableNames = {
			"DoubleDanceStartTipData",
        }
    },
    ["table_DoubleQteDance"] = {
        TableKey = "id",
        SubTableNames = {
			"DoubleQteDanceData",
        }
    },
    ["table_DsMiscConf"] = {
        TableKey = "key",
        SubTableNames = {
			"DsMiscConfForLetsGo",
        }
    },
    ["table_EmotionVoiceConfig"] = {
        TableKey = "characterID",
        SubTableNames = {
			"EmotionVoiceConfig",
        }
    },
    ["table_EntertainIntelligenceStationConf"] = {
        TableKey = "id",
        SubTableNames = {
			"EntertainIntelligenceStationConf",
        }
    },
    ["table_EntertainmentGuideConf"] = {
        TableKey = "id",
        SubTableNames = {
			"EntertainmentGuideConf",
        }
    },
    ["table_EntertainmentGuideConfV2"] = {
        TableKey = "SystemId",
        SubTableNames = {
			"EntertainmentGuideConfV2",
        }
    },
    ["table_EntertainmentGuideTalkConf"] = {
        TableKey = "seqId",
        SubTableNames = {
			"EntertainmentGuideTalkConf",
        }
    },
    ["table_EntertainmentGuideTalkConfV2"] = {
        TableKey = "SystemId",
        SubTableNames = {
			"EntertainmentGuideTalkConfV2",
        }
    },
    ["table_EntertainmentGuideTaskConf"] = {
        TableKey = "id",
        SubTableNames = {
			"EntertainmentGuideTaskConf_V1",
			"EntertainmentGuideTaskConf_V2",
        }
    },
    ["table_EnvAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"EnvAudioConfig",
        }
    },
    ["table_Equalizer"] = {
        TableKey = "CycleID",
        SubTableNames = {
			"Equalizer",
        }
    },
    ["table_EventPlayCdConfig"] = {
        TableKey = "eventName",
        SubTableNames = {
			"EventPlayCdConfig",
        }
    },
    ["table_EventTriggerData"] = {
        TableKey = "id",
        SubTableNames = {
			"EventTriggerData",
        }
    },
    ["table_ExcludedPoisonCircle"] = {
        TableKey = "id",
        SubTableNames = {
			"ExcludedPoisonCircle",
        }
    },
    ["table_FBGlobalConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FBGlobalConf",
        }
    },
    ["table_FBHeroListConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FBHeroList",
        }
    },
    ["table_ArenaSkillConf"] = {
        TableKey = "skillid,in_table_game_type",
        SubTableNames = {
			"FBSkillConfData",
        }
    },
    ["table_FPSSpecialSkinOffsetConf"] = {
        TableKey = "skinId",
        SubTableNames = {
			"FPSSpecialSkinOffsetConf",
        }
    },
    ["table_FPSUISkinForIP"] = {
        TableKey = "suitId",
        SubTableNames = {
			"FPSUISkinForIP",
        }
    },
    ["table_FPSWeaponAttachmentConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"FPSWeaponAttachmentConfigData",
        }
    },
    ["table_FPSWeaponAttachmentPositionConfig"] = {
        TableKey = "weaponId",
        SubTableNames = {
			"FPSWeaponAttachmentPositionConfigData",
        }
    },
    ["table_FPSWeaponClientConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FPSWeaponClientConfData",
			"FPSWeaponClientConfData_UGC",
        }
    },
    ["table_FPSWeaponExceptionAttachmentsConfig"] = {
        TableKey = "weaponId,attachmentId",
        SubTableNames = {
			"FPSWeaponExceptionAttachmentsConfigData",
        }
    },
    ["table_FPSWeaponPropertyConf"] = {
        TableKey = "blueprintName,propertyName",
        SubTableNames = {
			"FPSWeaponPropertyConfData",
        }
    },
    ["table_FPSWeaponSkinConf"] = {
        TableKey = "skinId",
        SubTableNames = {
			"FPSWeaponSkinConf",
        }
    },
    ["table_FPSWeaponSkinForIP"] = {
        TableKey = "suitId",
        SubTableNames = {
			"FPSWeaponSkinForIP",
        }
    },
    ["table_FPSWeaponUnLockConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FPSWeaponUnLockConfData",
        }
    },
    ["table_FarmActivityDecorationConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmActivityDecorationConf",
        }
    },
    ["table_FarmActivityFishConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmActivityFishConf",
        }
    },
    ["table_FarmBuffRainBowConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmBuffRainBowConf",
        }
    },
    ["table_FarmCookAvatarConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookAvatarConf",
        }
    },
    ["table_FarmCookDishConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookDishConf",
        }
    },
    ["table_FarmCookDishLevelConf"] = {
        TableKey = "id,level",
        SubTableNames = {
			"FarmCookDishLevelConf",
        }
    },
    ["table_FarmCookEmployeeAttrConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookEmployeeAttrConf",
        }
    },
    ["table_FarmCookEmployeeHighTypeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookEmployeeHighTypeConf",
        }
    },
    ["table_FarmCookEmployeeRefreshConf"] = {
        TableKey = "count",
        SubTableNames = {
			"FarmCookEmployeeRefreshConf",
        }
    },
    ["table_FarmCookHearthConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookHearthConf",
        }
    },
    ["table_FarmCookLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"FarmCookLevelConf",
        }
    },
    ["table_FarmCookLineBubbleConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookLineBubbleConf",
        }
    },
    ["table_FarmCookLineCommentLibraryConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookLineCommentLibraryConf",
        }
    },
    ["table_FarmCookReceptionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookReceptionConf",
        }
    },
    ["table_FarmCookScreenConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookScreenConf",
        }
    },
    ["table_FarmCookSeatConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookSeatConf",
        }
    },
    ["table_FarmCookVisitantGroupConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmCookVisitantGroupConf",
        }
    },
    ["table_FarmDailyAwardStartLevelConfig"] = {
        TableKey = "startLevel,activityId",
        SubTableNames = {
			"FarmDailyAwardStartLevelConfig",
        }
    },
    ["table_FarmEvent"] = {
        TableKey = "series,evtID",
        SubTableNames = {
			"FarmEvent",
        }
    },
    ["table_FarmEventCloudRatio"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmEventCloudRatio",
        }
    },
    ["table_FarmEventGuarantee"] = {
        TableKey = "time",
        SubTableNames = {
			"FarmEventGuarantee",
        }
    },
    ["table_FarmEventGuaranteeFish"] = {
        TableKey = "time",
        SubTableNames = {
			"FarmEventGuaranteeFish",
        }
    },
    ["table_FarmFishTrackConf"] = {
        TableKey = "track",
        SubTableNames = {
			"FarmFishTrackConf",
        }
    },
    ["table_FarmHotConf"] = {
        TableKey = "hotId",
        SubTableNames = {
			"FarmHotConf",
        }
    },
    ["table_FarmHotSpring"] = {
        TableKey = "typeId",
        SubTableNames = {
			"FarmHotSpring",
        }
    },
    ["table_FarmItemConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmItemConf_Collection",
			"FarmItemConf_Pet",
			"FarmItemConf_PetClothing",
			"FarmItemConf_furnitureSkin",
        }
    },
    ["table_FarmLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"FarmLevelConfData",
        }
    },
    ["table_FarmLevelTitleConf"] = {
        TableKey = "lv",
        SubTableNames = {
			"FarmLevelTitleConf",
        }
    },
    ["table_FarmMagicConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmMagicConf",
        }
    },
    ["table_FarmPartyKeywordWeightConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmPartyKeywordWeightConf",
        }
    },
    ["table_FarmReturningBuffConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmReturningBuffConfig",
        }
    },
    ["table_FarmReturningLevelUpChallengeActivity"] = {
        TableKey = "activityId",
        SubTableNames = {
			"FarmReturningLevelUpChallengeActivity",
        }
    },
    ["table_FarmReturningLevelUpChallengeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmReturningLevelUpChallengeConfig",
        }
    },
    ["table_FarmReturningLevelUpDescribeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmReturningLevelUpDescribeConfig",
        }
    },
    ["table_FarmReturningMgrData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"FarmReturningMgrData",
        }
    },
    ["table_FarmRoomBuildingFunctionalFurnitureLimitConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmRoomBuildingFunctionalFurnitureLimitConf",
        }
    },
    ["table_FarmRoomItemClientNeedLimitConf"] = {
        TableKey = "itemId",
        SubTableNames = {
			"FarmRoomItemClientNeedLimitConf",
        }
    },
    ["table_FarmRoomItemLimitConf"] = {
        TableKey = "itemId,farmLevel",
        SubTableNames = {
			"FarmRoomItemLimitConf",
        }
    },
    ["table_FarmSampleRoomConf"] = {
        TableKey = "templateId",
        SubTableNames = {
			"FarmSampleRoomConf",
        }
    },
    ["table_FarmSquadActivityConfigData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"FarmSquadActivityConfigData",
        }
    },
    ["table_FarmSquadActivityItemConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmSquadActivityItemConfigData",
        }
    },
    ["table_FarmSquadRewardTreeConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmSquadRewardTreeConfigData",
        }
    },
    ["table_FarmVillagerExpressionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmVillagerExpressionConf",
        }
    },
    ["table_FarmVillagerShennongGiftConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FarmVillagerShennongGiftConf",
        }
    },
    ["table_FashionFundData"] = {
        TableKey = "id",
        SubTableNames = {
			"FashionFundData",
        }
    },
    ["table_FeatureIntegrationIconConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FeatureIntegrationIconConfData",
        }
    },
    ["table_FeatureIntegrationMainConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FeatureIntegrationMainConfData",
        }
    },
    ["table_FeatureIntegrationTaskExtConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"FeatureIntegrationTaskExtData",
        }
    },
    ["table_FeatureIntegrationTextConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FeatureIntegrationTextConfData",
        }
    },
    ["table_FinalAbilityShieldingConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"FinalAbilityShieldingData",
        }
    },
    ["table_FireworkCdData"] = {
        TableKey = "id",
        SubTableNames = {
			"FireworkCdData",
        }
    },
    ["table_FireworkEffectConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"FireworkEffectConfigData",
        }
    },
    ["table_FireworkPartyConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"FireworkPartyConfData",
        }
    },
    ["table_FireworkPartyTimeData"] = {
        TableKey = "id",
        SubTableNames = {
			"FireworkPartyTimeData",
        }
    },
    ["table_FireworkSystemTextColorData"] = {
        TableKey = "id",
        SubTableNames = {
			"FireworkSystemTextColorData",
        }
    },
    ["table_FireworkSystemTextData"] = {
        TableKey = "id",
        SubTableNames = {
			"FireworkSystemTextData",
        }
    },
    ["table_FireworksTexBanTimeData"] = {
        TableKey = "id",
        SubTableNames = {
			"FireworksTexBanTimeData",
        }
    },
    ["table_FishingHallOfFameConstData"] = {
        TableKey = "id",
        SubTableNames = {
			"FishingHallOfFameConstData",
        }
    },
    ["table_FishingHallOfFameTabConfig"] = {
        TableKey = "layer",
        SubTableNames = {
			"FishingHallOfFameTabConfig",
        }
    },
    ["table_FlashRaceCheeringAiData"] = {
        TableKey = "id",
        SubTableNames = {
			"FlashRaceCheeringAiData",
        }
    },
    ["table_FlashRaceCheeringTimeAiData"] = {
        TableKey = "id",
        SubTableNames = {
			"FlashRaceCheeringTimeAiData",
        }
    },
    ["table_FoodFestivalAttrConfData"] = {
        TableKey = "itemId",
        SubTableNames = {
			"FoodFestivalAttrConfData",
        }
    },
    ["table_FoodFestivalBigRewardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"FoodFestivalBigRewardConfData",
        }
    },
    ["table_FoodFestivalElvesConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"FoodFestivalElvesConfData",
        }
    },
    ["table_FoodFestivalGoodConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"FoodFestivalGoodConfData",
        }
    },
    ["table_FoodFestivalMadeConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"FoodFestivalMadeConfData",
        }
    },
    ["table_FridayCollectionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FridayCollectionConf",
        }
    },
    ["table_FridayCollectionMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"FridayCollectionMiscConf",
        }
    },
    ["table_FriendAddDistance"] = {
        TableKey = "id",
        SubTableNames = {
			"FriendAddDistance",
        }
    },
    ["table_GMCmdConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GMCmdConfForLetsGo",
			"GMCmdConfForLetsGo_Hok",
        }
    },
    ["Table_GameModeReturnCheckConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameModeReturnCheckConfigData",
        }
    },
    ["Table_GameModeReturnConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameModeReturnConfigData",
        }
    },
    ["table_GameModeReturnProgressRewardData"] = {
        TableKey = "gameModeReturnId,progressId",
        SubTableNames = {
			"GameModeReturnProgressRewardConfigData",
        }
    },
    ["Table_GameModeReturnStepConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameModeReturnStepConfigData",
        }
    },
    ["Table_GameModeSortConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameModeSortConf",
        }
    },
    ["table_GameOptimizeSettingData"] = {
        TableKey = "id",
        SubTableNames = {
			"GameOptimizeSettingData_BS",
			"GameOptimizeSettingData_Base",
			"GameOptimizeSettingData_BrGun",
			"GameOptimizeSettingData_CHASE",
			"GameOptimizeSettingData_FPS",
			"GameOptimizeSettingData_Farm",
			"GameOptimizeSettingData_FarmCSMCacheClose",
			"GameOptimizeSettingData_FarmCook",
			"GameOptimizeSettingData_FarmHouse",
			"GameOptimizeSettingData_Gun",
			"GameOptimizeSettingData_HideByFullWindow",
			"GameOptimizeSettingData_HideByHalfWindow",
			"GameOptimizeSettingData_Lobby",
			"GameOptimizeSettingData_LobbyShowAvatar",
			"GameOptimizeSettingData_MAYDAY",
			"GameOptimizeSettingData_NR3E",
			"GameOptimizeSettingData_OGC",
			"GameOptimizeSettingData_SubLobby",
        }
    },
    ["table_GamePlayChasingLightCfgData"] = {
        TableKey = "Id",
        SubTableNames = {
			"GamePlayChasingLightCfgData",
        }
    },
    ["table_GameReturnClientMiscData"] = {
        TableKey = "Key",
        SubTableNames = {
			"GameReturnClientMiscData",
        }
    },
    ["table_GameReturnPushFaceData"] = {
        TableKey = "id",
        SubTableNames = {
			"GameReturnPushFaceData",
        }
    },
    ["table_GameRuleConfig"] = {
        TableKey = "RuleId",
        SubTableNames = {
			"GameRuleConfig",
        }
    },
    ["table_GameRuleUrlConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"GameRuleUrlConfig",
        }
    },
    ["table_GameSettingButtonComponentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingButtonComponentConfData",
        }
    },
    ["table_GameSettingComBoxComponentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingComBoxComponentConfData",
        }
    },
    ["table_GameSettingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingConfData",
        }
    },
    ["table_GameSettingImageComponentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingImageComponentConf",
        }
    },
    ["table_GameSettingOptionComponentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingOptionComponentConfData",
        }
    },
    ["table_GameSettingOptionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingOptionConfData",
        }
    },
    ["table_GameSettingOptionWidgetComponentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingOptionWidgetComponentConfData",
        }
    },
    ["table_GameSettingSliderComponentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingSliderComponentConfData",
        }
    },
    ["table_GameSettingTabConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingTabConfData",
        }
    },
    ["table_GameSettingToggleComponentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GameSettingToggleComponentConfData",
        }
    },
    ["table_GiftPackageConf"] = {
        TableKey = "giftId,groupId,index",
        SubTableNames = {
			"GiftPackageConfForLetsGo_giftpack",
			"GiftPackageConfForLetsGo_luckymoney",
			"GiftPackageConfForLetsGo_scratchofftickets",
        }
    },
    ["table_GiftPackageOpenAnimationData"] = {
        TableKey = "itemChangeReason",
        SubTableNames = {
			"GiftPackageOpenAnimationData",
        }
    },
    ["table_GiveCardBlessingTextConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GiveCardBlessingText_main",
        }
    },
    ["table_GiveCardTemplateConf"] = {
        TableKey = "id",
        SubTableNames = {
			"GiveCardTemplate_main",
        }
    },
    ["table_GlobalMailConfData"] = {
        TableKey = "Id",
        SubTableNames = {
			"GlobalMailConfData",
        }
    },
    ["table_GlobalWorldChatGroupList"] = {
        TableKey = "id",
        SubTableNames = {
			"GlobalWorldChatGroupList",
        }
    },
    ["table_GrabVoiceMappingConfig"] = {
        TableKey = "Id",
        SubTableNames = {
			"GrabVoiceMappingConfig",
        }
    },
    ["table_GroupInvitationTabConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"GroupInvitationTabConfData",
        }
    },
    ["table_GroupWindowData"] = {
        TableKey = "groupWindow",
        SubTableNames = {
			"GroupWindowData",
        }
    },
    ["table_GroupingReturnData"] = {
        TableKey = "id",
        SubTableNames = {
			"GroupingReturnData",
        }
    },
    ["table_GuideAppConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"GuideAppConfigData",
        }
    },
    ["table_GunGameData"] = {
        TableKey = "Order",
        SubTableNames = {
			"GunGameData",
			"GunGameData_Team",
        }
    },
    ["table_HOKAIData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKAIData",
        }
    },
    ["table_HOKCardAttrHint"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKCardAttrHint",
        }
    },
    ["table_HOKCardTextData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKCardTextData",
        }
    },
    ["table_HOKCpuDebugData"] = {
        TableKey = "cpuInfoId",
        SubTableNames = {
			"HOKCpuDebugData",
        }
    },
    ["table_HOKHaoHaoYaEventData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHaoHaoYaEventData",
        }
    },
    ["table_HOKHaoHaoYaTextData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHaoHaoYaTextData",
        }
    },
    ["table_HOKHeroAutoAddSkill"] = {
        TableKey = "heroId",
        SubTableNames = {
			"HOKHeroAutoAddSkill",
        }
    },
    ["table_HOKHeroPositionDescConfData"] = {
        TableKey = "heroPosition",
        SubTableNames = {
			"HOKHeroPositionDescConfData",
        }
    },
    ["table_HOKHeroSectConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroSectConf",
        }
    },
    ["table_HOKHeroSectTextConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroSectTextConf",
        }
    },
    ["table_HOKHeroShuntData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHeroShuntData",
        }
    },
    ["table_HOKHighLightData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKHighLightData",
        }
    },
    ["table_HOKKillBountyAdjust"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKKillBountyAdjust",
        }
    },
    ["table_HOKOverrideArenaBuffData"] = {
        TableKey = "id,level",
        SubTableNames = {
			"HOKOverrideBuffData",
        }
    },
    ["table_HOKOverrideArenaDamageEffect"] = {
        TableKey = "id,level",
        SubTableNames = {
			"HOKOverrideDamageEffectData_Hero",
        }
    },
    ["table_HOKOverrideArenaLevelFactor"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKOverrideLevelFactor",
        }
    },
    ["table_HOKPlayerGuideTaskData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKPlayerGuideTaskData",
        }
    },
    ["table_HOKRobotAutoCommunicateData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKRobotAutoCommunicateData",
        }
    },
    ["table_HOKRoleCardData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKRoleCardData",
        }
    },
    ["table_HOKSeasonFeatureSwitchData"] = {
        TableKey = "id",
        SubTableNames = {
			"HOKSeasonFeatureSwitchData",
        }
    },
    ["table_HeadWearAndSuitDisplay"] = {
        TableKey = "id",
        SubTableNames = {
			"HeadWearAndSuitDisplay",
        }
    },
    ["table_HeartBeatDataConf"] = {
        TableKey = "serverType",
        SubTableNames = {
			"HeartBeatDataConfData",
        }
    },
    ["table_HotBuyUMGShowCondition_AvatarSubView"] = {
        TableKey = "varName",
        SubTableNames = {
			"HotBuyUMGShowCondition_AvatarSubView",
        }
    },
    ["table_HotBuyUMGShowCondition_MainView"] = {
        TableKey = "varName",
        SubTableNames = {
			"HotBuyUMGShowCondition_MainView",
        }
    },
    ["table_HotResOverData"] = {
        TableKey = "id",
        SubTableNames = {
			"HotResOverData",
        }
    },
    ["table_HotZoneGlobalConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HotZoneGlobalConf",
        }
    },
    ["table_HotZoneHeroConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HotZoneHeroConf",
        }
    },
    ["table_HotZoneLevelConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HotZoneLevelConf",
        }
    },
    ["table_HotZoneRoundConf"] = {
        TableKey = "id",
        SubTableNames = {
			"HotZoneRoundConf",
        }
    },
    ["table_IAAConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"IAAConfData_BattlePass",
			"IAAConfData_GuideToApp",
			"IAAConfData_Raffle",
			"IAAConfData_Settlement",
			"IAAConfData_UGCInLevel",
        }
    },
    ["table_IAAGuideToAppRewardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"IAAGuideToAppRewardConfData",
        }
    },
    ["table_IAARaffleRewardCommodityData"] = {
        TableKey = "itemId",
        SubTableNames = {
			"IAARaffleRewardCommodityData",
        }
    },
    ["table_IAAShareConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"IAAShareConfData_Raffle",
        }
    },
    ["table_ImageRuleDescData"] = {
        TableKey = "id",
        SubTableNames = {
			"ImageRuleDescData",
        }
    },
    ["table_ImpressionsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ImpressionsConfig",
        }
    },
    ["table_InGameFaceResource"] = {
        TableKey = "id",
        SubTableNames = {
			"InGameFaceResource",
        }
    },
    ["table_InGameOtherResource"] = {
        TableKey = "id",
        SubTableNames = {
			"InGameHandHoldResource",
			"InGameOtherResource",
        }
    },
    ["table_InGameItemResource"] = {
        TableKey = "id",
        SubTableNames = {
			"InGameItemResource",
			"InGameItemResource_ChaseBossSkin",
			"InGameItemResource_HeroSkin",
        }
    },
    ["table_InLevelCoMatchBattleTogetherConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"InLevelCoMatchBattleTogetherConfigData",
        }
    },
    ["table_InLevelCoMatchBattleTogetherDimensionData"] = {
        TableKey = "id",
        SubTableNames = {
			"InLevelCoMatchBattleTogetherDimensionData",
        }
    },
    ["table_InLevelEventUploadData"] = {
        TableKey = "Id",
        SubTableNames = {
			"InLevelEventUploadData",
        }
    },
    ["table_InLevelPerformExcellentConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"InLevelPerformExcellentConfigData",
        }
    },
    ["table_InLevelPerformExcellentDimensionData"] = {
        TableKey = "id",
        SubTableNames = {
			"InLevelPerformExcellentDimensionData",
        }
    },
    ["table_InLevelTogetherConstConfigData"] = {
        TableKey = "key",
        SubTableNames = {
			"InLevelTogetherConstConfigData",
        }
    },
    ["table_InLevelTogetherTextConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"InLevelTogetherTextConfigData",
        }
    },
    ["table_InPlayUGCActorInfo"] = {
        TableKey = "ID",
        SubTableNames = {
			"InPlayUGCActorInfo_Building",
			"InPlayUGCActorInfo_Character",
			"InPlayUGCActorInfo_Construction",
			"InPlayUGCActorInfo_Decorate",
			"InPlayUGCActorInfo_Furniture",
			"InPlayUGCActorInfo_Nature",
			"InPlayUGCActorInfo_Stratagem",
        }
    },
    ["table_InitialPoisonCircle"] = {
        TableKey = "id",
        SubTableNames = {
			"InitialPoisonCircle",
        }
    },
    ["table_InterServerGiftConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"InterServerGiftConfigData",
        }
    },
    ["table_InteractionAnimation"] = {
        TableKey = "id",
        SubTableNames = {
			"InteractionAnimationMCG",
			"InteractionAnimationRunningGame",
        }
    },
    ["table_InteractiveActorData"] = {
        TableKey = "Id",
        SubTableNames = {
			"InteractiveActorConfig",
        }
    },
    ["table_InteractiveConditionData"] = {
        TableKey = "Id",
        SubTableNames = {
			"InteractiveConditionConfig",
        }
    },
    ["table_InteractiveTypeData"] = {
        TableKey = "Id",
        SubTableNames = {
			"InteractiveTypeConfig",
        }
    },
    ["table_InterestChannelConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"InterestChannelConfigData",
        }
    },
    ["table_InterestChannelEntryText"] = {
        TableKey = "id",
        SubTableNames = {
			"InterestChannelEntryText",
        }
    },
    ["table_InterestChannelIcons"] = {
        TableKey = "id",
        SubTableNames = {
			"InterestChannelIcons",
        }
    },
    ["table_InterestChannelTemplateText"] = {
        TableKey = "id",
        SubTableNames = {
			"InterestChannelTemplateText",
        }
    },
    ["table_IntimateDisplaynConf"] = {
        TableKey = "id",
        SubTableNames = {
			"IntimateDisplaynConf",
        }
    },
    ["table_IntimateRelationConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"IntimateRelationConfData",
        }
    },
    ["table_IntimateRelationExtraCntUnlockConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"IntimateRelationExtraCntUnlockConfData",
        }
    },
    ["table_IntimateRelationLevelConfData"] = {
        TableKey = "level",
        SubTableNames = {
			"IntimateRelationLevelConfData",
        }
    },
    ["table_IntimateTabData"] = {
        TableKey = "id",
        SubTableNames = {
			"IntimateTabConf",
        }
    },
    ["table_InviteActivityDisplayData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"InviteActivityDisplayData",
        }
    },
    ["table_InviteTeamShareConfig"] = {
        TableKey = "Id",
        SubTableNames = {
			"InviteTeamShareConfig",
        }
    },
    ["table_ItaBag"] = {
        TableKey = "id",
        SubTableNames = {
			"ItaBag",
        }
    },
    ["table_ItemDisplayBoardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ItemDisplayBoardConfData",
        }
    },
    ["table_ItemDisplayBoardOfficialTextData"] = {
        TableKey = "stringId",
        SubTableNames = {
			"ItemDisplayBoardOfficialTextData",
        }
    },
    ["table_ItemStatusChangeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ItemStatusChangeData_main",
        }
    },
    ["table_ItemToyMiscConfData"] = {
        TableKey = "stringId",
        SubTableNames = {
			"ItemToyMiscConf",
        }
    },
    ["table_JSReliableAIConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"JSReliableAIConfig",
        }
    },
    ["table_JumpConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"JumpConfig",
        }
    },
    ["table_LabelsInfoData"] = {
        TableKey = "LabelsId",
        SubTableNames = {
			"LabelsInfoData",
        }
    },
    ["table_LanguagesData"] = {
        TableKey = "id",
        SubTableNames = {
			"LanguagesData",
        }
    },
    ["table_LanguagesGvoiceData"] = {
        TableKey = "AttGvoice",
        SubTableNames = {
			"LanguagesGvoiceData",
        }
    },
    ["table_LeagueActivityDateConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LeagueActivityDateConf",
			"SignLeagueActivityDateConf",
        }
    },
    ["table_LeagueActivityMiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LeagueActivityMiscConf",
        }
    },
    ["table_LeagueActivityPageConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LeagueActivityPageConf",
			"SignLeagueActivityPageConf",
        }
    },
    ["table_LeagueHeroesHeroBackgroundConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LeagueHeroesHeroBackgroundConf",
        }
    },
    ["table_LeagueHeroesHeroConf"] = {
        TableKey = "order",
        SubTableNames = {
			"LeagueHeroesHeroConf",
        }
    },
    ["table_DeviceProfileInfoData"] = {
        TableKey = "Level",
        SubTableNames = {
			"LetsGoDeviceFPSInfoAndroid",
			"LetsGoDeviceFPSInfoIOS",
			"LetsGoDeviceFPSInfoWindows",
			"LetsGoDeviceProfileInfoAndroid",
			"LetsGoDeviceProfileInfoIOS",
			"LetsGoDeviceProfileInfoWindows",
			"LetsGoDeviceScreenPercentageInfoAndroid",
			"LetsGoDeviceScreenPercentageInfoIOS",
			"LetsGoDeviceScreenPercentageInfoWindows",
        }
    },
    ["table_LetsGoMomentsClip"] = {
        TableKey = "id",
        SubTableNames = {
			"LetsGoMomentsClip",
        }
    },
    ["table_LetsGoMomentsMain"] = {
        TableKey = "id",
        SubTableNames = {
			"LetsGoMomentsMain",
        }
    },
    ["table_LetsGoMomentsTracker"] = {
        TableKey = "id",
        SubTableNames = {
			"LetsGoMomentsTracker",
        }
    },
    ["table_LevelChunkData"] = {
        TableKey = "name",
        SubTableNames = {
			"LevelChunkData",
        }
    },
    ["table_LevelDropArenaSpecialConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"LevelDropArenaSpecialData",
        }
    },
    ["table_LevelExtraTimeInfoData"] = {
        TableKey = "Id",
        SubTableNames = {
			"LevelExtraTimeInfoData",
        }
    },
    ["table_LevelInfoData"] = {
        TableKey = "Id",
        SubTableNames = {
			"LevelInfoData_AC",
			"LevelInfoData_AI",
			"LevelInfoData_BS",
			"LevelInfoData_Chase",
			"LevelInfoData_FB",
			"LevelInfoData_FPS",
			"LevelInfoData_HOK",
			"LevelInfoData_JS",
			"LevelInfoData_MAYDAY",
			"LevelInfoData_Metro",
			"LevelInfoData_OMD",
			"LevelInfoData_arena",
			"LevelInfoData_coc",
			"LevelInfoData_dnd",
			"LevelInfoData_lighting",
			"LevelInfoData_main",
			"LevelInfoData_nr3e",
			"LevelInfoData_ugc",
			"TestLevelInfoData",
        }
    },
    ["table_LevelLabelData"] = {
        TableKey = "Id",
        SubTableNames = {
			"LevelLabelInfoData",
        }
    },
    ["table_LevelLoadingPanelConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LevelLoadingPanelConfData",
        }
    },
    ["table_LevelModuleData"] = {
        TableKey = "Id",
        SubTableNames = {
			"LevelModuleData",
        }
    },
    ["table_LevelPerformanceScoreData"] = {
        TableKey = "levelType",
        SubTableNames = {
			"LevelPerformanceScoreData",
        }
    },
    ["table_LevelRandomGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"LevelRandomGroupData",
        }
    },
    ["table_LevelRoundABTestConfData"] = {
        TableKey = "testId",
        SubTableNames = {
			"LevelRoundABTestConfData",
        }
    },
    ["table_LevelRoundDispersionConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"LevelRoundDispersionConfData",
        }
    },
    ["table_LevelRoundRandomRuleData"] = {
        TableKey = "Id",
        SubTableNames = {
			"LevelRoundRandomRuleData",
        }
    },
    ["table_LevelScoreGradeData"] = {
        TableKey = "score",
        SubTableNames = {
			"LevelScoreGradeData",
        }
    },
    ["table_TechniqueBroadcastData"] = {
        TableKey = "BroadcastID",
        SubTableNames = {
			"LevelTechniqueBroadcastData",
        }
    },
    ["table_TechniqueStepData"] = {
        TableKey = "StepID",
        SubTableNames = {
			"LevelTechniqueStepData",
        }
    },
    ["table_LevelUGCInfoData"] = {
        TableKey = "Id",
        SubTableNames = {
			"LevelUGCInfoData",
        }
    },
    ["table_LightningGameSurvivalData"] = {
        TableKey = "MatchTypeID",
        SubTableNames = {
			"LightningGameSurvivalData",
        }
    },
    ["table_LightningGameTipData"] = {
        TableKey = "LevelType",
        SubTableNames = {
			"LightningGameTipData",
        }
    },
    ["table_LightningTimeTextData"] = {
        TableKey = "id",
        SubTableNames = {
			"LightningTimeTextData",
        }
    },
    ["table_LimitedExperienceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LimitedExperienceConf",
        }
    },
    ["table_LoadingPanelConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LoadingPanelConfData",
        }
    },
    ["table_LoadingTextGroupConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LoadingTextGroupConf",
        }
    },
    ["table_LoadingTextMainConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LoadingTextMainConf",
        }
    },
    ["table_LobbyConfig"] = {
        TableKey = "mapId",
        SubTableNames = {
			"LobbyConfigData_main",
			"LobbyConfigData_testUgc",
			"LobbyConfigData_ugc",
        }
    },
    ["table_LobbyExitToastConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyExitToastConfigData",
        }
    },
    ["table_LobbyIconSwitch"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyIconSwitch",
        }
    },
    ["table_LobbyIconSwitchArr"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyIconSwitchArr",
        }
    },
    ["table_LobbyIconSwitchCondition"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyIconSwitchCondition",
        }
    },
    ["table_LobbyIconSwitchConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyIconSwitchConf",
        }
    },
    ["table_LobbyIconSwitchJoy"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyIconSwitchJoy",
        }
    },
    ["table_LobbyIconSwitchJoyCondition"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyIconSwitchJoyCondition",
        }
    },
    ["table_LobbyIconSwitchJoyConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyIconSwitchJoyConf",
        }
    },
    ["table_LobbyJumpMsgBoxConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyJumpMsgBoxConfigData",
        }
    },
    ["table_LobbyLeftTopTipRecommendData"] = {
        TableKey = "id",
        SubTableNames = {
			"LobbyLeftTopTipRecommendData",
        }
    },
    ["table_LobbyNavConfig"] = {
        TableKey = "mapId",
        SubTableNames = {
			"LobbyNavConfigData",
        }
    },
    ["table_LoginBackgroundConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LoginBackgroundConfData",
        }
    },
    ["table_LoginEffectUIConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LoginEffectUIConfData",
        }
    },
    ["table_LoginSpineEffectConf"] = {
        TableKey = "id",
        SubTableNames = {
			"LoginSpineEffectConfData",
        }
    },
    ["table_LuckyBalloonBlessConf"] = {
        TableKey = "blessId",
        SubTableNames = {
			"LuckyBalloonBlessConfData",
        }
    },
    ["table_LuckyBalloonConf"] = {
        TableKey = "poolId",
        SubTableNames = {
			"LuckyBalloonConfData",
        }
    },
    ["table_LuckyBalloonRewardConf"] = {
        TableKey = "rewardId",
        SubTableNames = {
			"LuckyBalloonRewardConfData",
        }
    },
    ["table_LuckyFriendTaskConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"LuckyFriendTaskConfData",
        }
    },
    ["table_LuckyMoneyInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"LuckyMoneyInfoData",
        }
    },
    ["table_LuckyRebateActivityConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"LuckyRebateActivityConfigData",
        }
    },
    ["table_LuckyRebateConfigData"] = {
        TableKey = "level",
        SubTableNames = {
			"LuckyRebateConfigData",
        }
    },
    ["table_LuckyStarHandBookBless"] = {
        TableKey = "blessId",
        SubTableNames = {
			"LuckyStarHandBookBlessData",
        }
    },
    ["table_LuckyStarHandBook"] = {
        TableKey = "typeId",
        SubTableNames = {
			"LuckyStarHandBookData",
        }
    },
    ["table_LuckyStarHandBookStarPool"] = {
        TableKey = "poolId",
        SubTableNames = {
			"LuckyStarHandBookStarPoolData",
        }
    },
    ["table_LuckyStarHandBookStars"] = {
        TableKey = "starId",
        SubTableNames = {
			"LuckyStarHandBookStarsData",
        }
    },
    ["table_LuckyTurntableActivityConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"LuckyTurntableActivityConfig",
        }
    },
    ["table_LuckyTurntableTurntableConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"LuckyTurntableTurntableConfig",
        }
    },
    ["table_MailIniConfData"] = {
        TableKey = "Id",
        SubTableNames = {
			"MailIniConfForLetsGo",
			"MailIniConfForLetsGo_coc",
        }
    },
    ["table_MailTemplateConfData"] = {
        TableKey = "Id",
        SubTableNames = {
			"MailTemplateConfData",
			"MailTemplateConfData_coc",
        }
    },
    ["table_MailTemplateTagConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"MailTemplateTagConf",
        }
    },
    ["table_MainCupsStageConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"MainCupsStageConfigData",
        }
    },
    ["table_MainGameRankTitleData"] = {
        TableKey = "id",
        SubTableNames = {
			"MainGameRankTitleData",
        }
    },
    ["table_MainGameRankingTitleData"] = {
        TableKey = "id",
        SubTableNames = {
			"MainGameRankingTitleData",
        }
    },
    ["table_PlayerGuideData"] = {
        TableKey = "GuideID",
        SubTableNames = {
			"MainGuideData_Mayday",
			"MainGuideData_Mouse_pc",
			"MainGuideData_acm",
			"MainGuideData_acm_pc",
			"MainGuideData_arena",
			"MainGuideData_arena_pc",
			"MainGuideData_bs",
			"MainGuideData_chase",
			"MainGuideData_cloud",
			"MainGuideData_coc",
			"MainGuideData_cook",
			"MainGuideData_farm",
			"MainGuideData_hok",
			"MainGuideData_home",
			"MainGuideData_js",
			"MainGuideData_main",
			"MainGuideData_main_pc",
			"MainGuideData_nr3e",
			"MainGuideData_nr3e8",
			"MainGuideData_omd",
			"MainGuideData_tyc",
			"MainGuideData_ugc",
			"MainGuideData_va",
			"MainGuideData_wuqidashi",
			"MainGuideData_wuqidashi_pc",
        }
    },
    ["table_MainMaterPathConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"MainMaterPathConfig",
        }
    },
    ["table_MainMaterPathStageConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"MainMaterPathStageConfig",
        }
    },
    ["table_MainUIGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"MainUIGroupData",
        }
    },
    ["table_MallCommodityConf"] = {
        TableKey = "commodityId",
        SubTableNames = {
			"MallCommodityConf_ActivityBigExchange",
			"MallCommodityConf_ActivityChapterTaskExchange",
			"MallCommodityConf_ActivityNormalExchange",
			"MallCommodityConf_ActivitySpringFestival",
			"MallCommodityConf_ActivityTakeawayExchange",
			"MallCommodityConf_Arena",
			"MallCommodityConf_AvatarBuy",
			"MallCommodityConf_AvatarGift",
			"MallCommodityConf_BackOrnament",
			"MallCommodityConf_Chase",
			"MallCommodityConf_Common",
			"MallCommodityConf_DirectBuy",
			"MallCommodityConf_DrawGift",
			"MallCommodityConf_Emoji",
			"MallCommodityConf_FaceOrnament",
			"MallCommodityConf_Faces",
			"MallCommodityConf_Farm",
			"MallCommodityConf_FarmItem",
			"MallCommodityConf_FootPrint",
			"MallCommodityConf_Gloves",
			"MallCommodityConf_GroupingReturn",
			"MallCommodityConf_HandOrnament",
			"MallCommodityConf_HeadWear",
			"MallCommodityConf_Hot",
			"MallCommodityConf_InteractiveProp",
			"MallCommodityConf_Intimate",
			"MallCommodityConf_Item",
			"MallCommodityConf_JSCar",
			"MallCommodityConf_LowerGarment",
			"MallCommodityConf_MallArena",
			"MallCommodityConf_MonthCard",
			"MallCommodityConf_Movement",
			"MallCommodityConf_NR3E1_Decorate",
			"MallCommodityConf_NR3E3",
			"MallCommodityConf_NR3E3_Decorate",
			"MallCommodityConf_NR3E3_DirectPurchase",
			"MallCommodityConf_NR3E8",
			"MallCommodityConf_PairMovement",
			"MallCommodityConf_Peekaboo",
			"MallCommodityConf_RaffleDiamondExchange_hedy",
			"MallCommodityConf_RaffleDiamondExchange_huoyue",
			"MallCommodityConf_RaffleDiamondExchange_karl",
			"MallCommodityConf_RaffleDiamondExchange_main",
			"MallCommodityConf_RaffleDiamondExchange_miles",
			"MallCommodityConf_RaffleDiamondExchange_silvester",
			"MallCommodityConf_RaffleDiamondExchange_tesewanfa",
			"MallCommodityConf_RaffleDiamondExchange_yujun",
			"MallCommodityConf_ReturnFriendshipFire",
			"MallCommodityConf_SceneGiftPackage",
			"MallCommodityConf_SeasonShop",
			"MallCommodityConf_Shuttle",
			"MallCommodityConf_Suit",
			"MallCommodityConf_Surroundings",
			"MallCommodityConf_ThemeMall",
			"MallCommodityConf_UpperGarment",
			"MallCommodityConf_Vehicle",
			"MallCommodityConf_VehicleUpgrade",
			"MallCommodityConf_Velarium",
			"MallCommodityConf_WeekendIceBroken",
			"MallCommodityConf_WerewolfFullReduced",
			"MallCommodityConf_Wolfkill",
			"MallCommodityConf_activity_commercialization",
			"MallCommodityConf_activity_common",
			"MallCommodityConf_activity_design",
			"MallCommodityConf_activity_modyunying",
        }
    },
    ["table_MallInfoConf"] = {
        TableKey = "mallId",
        SubTableNames = {
			"MallInfoConf",
        }
    },
    ["table_MallMobaItemTipsData"] = {
        TableKey = "itemId",
        SubTableNames = {
			"MallMobaItemTipsData",
        }
    },
    ["table_MallObjectStateConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"MallObjectStateConfig",
        }
    },
    ["table_MallRecommendPageConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MallRecommendPageConf",
        }
    },
    ["table_MarqueeNoticeData"] = {
        TableKey = "id",
        SubTableNames = {
			"MarqueeNoticeData",
        }
    },
    ["table_MasterPathLockConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"MasterPathLockConfig",
        }
    },
    ["table_MatchActivityTypeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchActivityTypeConf",
        }
    },
    ["table_MatchBattleEventListData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchBattleEventListData",
			"MatchBattleEventListData_FB",
        }
    },
    ["table_MatchCampData"] = {
        TableKey = "id,campId",
        SubTableNames = {
			"MatchCampData",
        }
    },
    ["table_MatchCampGradeData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchCampGradeData",
        }
    },
    ["table_MatchCampGradeDimensionData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchCampGradeDimensionData",
        }
    },
    ["table_MatchControlData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchControlData",
        }
    },
    ["table_MatchDateData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchDateData_main",
			"MatchDateData_rank",
        }
    },
    ["table_MatchDegreeTypeGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchDegreeTypeGroupData",
        }
    },
    ["table_MatchDynamicTeamRobotsData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchDynamicTeamRobotsData_BS",
			"MatchDynamicTeamRobotsData_FB",
			"MatchDynamicTeamRobotsData_HOK",
			"MatchDynamicTeamRobotsData_JS",
			"MatchDynamicTeamRobotsData_Mayday",
			"MatchDynamicTeamRobotsData_Metro",
			"MatchDynamicTeamRobotsData_acm",
			"MatchDynamicTeamRobotsData_arena",
			"MatchDynamicTeamRobotsData_competition",
			"MatchDynamicTeamRobotsData_dnd",
			"MatchDynamicTeamRobotsData_fps",
			"MatchDynamicTeamRobotsData_main",
			"MatchDynamicTeamRobotsData_nr3e",
			"MatchDynamicTeamRobotsData_ugc",
        }
    },
    ["table_MatchExpandScopeLimitData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchExpandScopeLimitData",
        }
    },
    ["table_MatchInfoPassThroughData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchInfoPassThroughData",
        }
    },
    ["table_MatchLevelPassScoreData"] = {
        TableKey = "matchType",
        SubTableNames = {
			"MatchLevelPassScoreData",
        }
    },
    ["table_MatchLevelRecordRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchLevelRecordRuleData",
        }
    },
    ["table_MatchModeSortInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchModeSortInfo",
        }
    },
    ["table_MatchModeSortInfo_ABTest"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchModeSortInfo_ABTest",
        }
    },
    ["table_MatchModeTypeData"] = {
        TableKey = "modeID",
        SubTableNames = {
			"MatchModeTypeData",
        }
    },
    ["table_MatchNewImageData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchNewImageData",
        }
    },
    ["table_MatchNewTagDateData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchNewTagDateData",
        }
    },
    ["table_MatchPakDetail"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchPakDetail",
        }
    },
    ["table_MatchPakType"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchPakType",
        }
    },
    ["table_MatchPreparationData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchPreparationData",
        }
    },
    ["table_MatchPreparationKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"MatchPreparationKVConf",
        }
    },
    ["table_MatchPublicInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchPublicInfoData",
        }
    },
    ["table_MatchRecommendData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchRecommendData",
        }
    },
    ["table_MatchRecommendDefaultData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchRecommendDefaultData",
        }
    },
    ["table_MatchRecommendPageData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchRecommendPageData",
        }
    },
    ["table_MatchRoomInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchRoomInfoData_BS",
			"MatchRoomInfoData_FB",
			"MatchRoomInfoData_HOK",
			"MatchRoomInfoData_JS",
			"MatchRoomInfoData_Mayday",
			"MatchRoomInfoData_Metro",
			"MatchRoomInfoData_acm",
			"MatchRoomInfoData_arena",
			"MatchRoomInfoData_chase",
			"MatchRoomInfoData_competition",
			"MatchRoomInfoData_dnd",
			"MatchRoomInfoData_fps",
			"MatchRoomInfoData_main",
			"MatchRoomInfoData_nr3e",
			"MatchRoomInfoData_ugc",
        }
    },
    ["table_MatchRuleRangeData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchRuleRangeData_BS",
			"MatchRuleRangeData_FB",
			"MatchRuleRangeData_HOK",
			"MatchRuleRangeData_JS",
			"MatchRuleRangeData_Mayday",
			"MatchRuleRangeData_Metro",
			"MatchRuleRangeData_acm",
			"MatchRuleRangeData_arena",
			"MatchRuleRangeData_competition",
			"MatchRuleRangeData_dnd",
			"MatchRuleRangeData_fps",
			"MatchRuleRangeData_lobbymatch",
			"MatchRuleRangeData_main",
			"MatchRuleRangeData_nr3e",
			"MatchRuleRangeData_ugc",
        }
    },
    ["table_MatchRuleTeamDegreeCondData"] = {
        TableKey = "matchType",
        SubTableNames = {
			"MatchRuleTeamDegreeCondData_HOK",
			"MatchRuleTeamDegreeCondData_arena",
        }
    },
    ["table_MatchSideLimitConfigData"] = {
        TableKey = "matchTypeId",
        SubTableNames = {
			"MatchSideLimitConfigData_competition",
			"MatchSideLimitConfigData_nr3e",
        }
    },
    ["table_MatchSpecialRoundRobotSideDiffVirtualRoomInfoData"] = {
        TableKey = "matchTypeId,specialRoundType",
        SubTableNames = {
			"MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_BS",
			"MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_FB",
			"MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_HOK",
			"MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_moba",
        }
    },
    ["table_MatchTypeConflictOutlookData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchTypeConflictOutlookData",
        }
    },
    ["table_MatchTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchTypeData_BS",
			"MatchTypeData_COC",
			"MatchTypeData_Chase",
			"MatchTypeData_FB",
			"MatchTypeData_HOK",
			"MatchTypeData_JS",
			"MatchTypeData_Mayday",
			"MatchTypeData_Metro",
			"MatchTypeData_YXSHZ",
			"MatchTypeData_acm",
			"MatchTypeData_arena",
			"MatchTypeData_coming",
			"MatchTypeData_competition",
			"MatchTypeData_dnd",
			"MatchTypeData_fps",
			"MatchTypeData_jumpTo",
			"MatchTypeData_lobbymatch",
			"MatchTypeData_main",
			"MatchTypeData_nr3e",
			"MatchTypeData_ugc",
        }
    },
    ["table_MatchTypeDetailPageGroupInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"MatchTypeDetailPageGroupInfoData_main",
        }
    },
    ["table_MatchTypeIdJumpConfig"] = {
        TableKey = "MatchTypeId",
        SubTableNames = {
			"MatchTypeIdJumpConfig",
        }
    },
    ["table_MatchTypeOutlookReplaceData"] = {
        TableKey = "itemId",
        SubTableNames = {
			"MatchTypeOutlookReplaceData",
        }
    },
    ["table_MatchTypeRootData"] = {
        TableKey = "gameTypeId",
        SubTableNames = {
			"MatchTypeRootData",
        }
    },
    ["table_MatchUnlockConditionData"] = {
        TableKey = "matchTypeId",
        SubTableNames = {
			"MatchUnlockConditionData_main",
        }
    },
    ["table_Mayday_BetaTaskData"] = {
        TableKey = "TaskID",
        SubTableNames = {
			"Mayday_BetaTaskData",
        }
    },
    ["table_Mayday_MultiAcionOfStereoMusicData"] = {
        TableKey = "AudioID",
        SubTableNames = {
			"Mayday_MultiAcionOfStereoMusicData",
        }
    },
    ["table_MeetingInteractionFlightPath"] = {
        TableKey = "id",
        SubTableNames = {
			"MeetingInteractionFlightPathData",
        }
    },
    ["table_MeetingInteractionStep"] = {
        TableKey = "StepID",
        SubTableNames = {
			"MeetingInteractionStepData",
        }
    },
    ["table_MewMewDanMuCfg"] = {
        TableKey = "id",
        SubTableNames = {
			"MewMewDanMuCfg",
        }
    },
    ["table_MidasModuleConf"] = {
        TableKey = "moduleId",
        SubTableNames = {
			"MidasModuleConf",
        }
    },
    ["table_MidasConf"] = {
        TableKey = "productId",
        SubTableNames = {
			"MidasProductConfData_Charge",
			"MidasProductConfData_ItemBuy",
			"MidasProductConfData_bp_main",
			"MidasProductConfData_bp_maingame",
			"MidasProductConfData_bp_moba",
			"MidasProductConfData_bp_nr3e_sheishilangren",
        }
    },
    ["table_MidasReleaseZoneConf"] = {
        TableKey = "zoneId",
        SubTableNames = {
			"MidasReleaseZoneConf",
        }
    },
    ["table_MidasSandboxZoneConf"] = {
        TableKey = "zoneId",
        SubTableNames = {
			"MidasSandboxZoneConf",
        }
    },
    ["table_MiniGameWarningData"] = {
        TableKey = "QQSDKID",
        SubTableNames = {
			"MiniGameWarningData",
        }
    },
    ["table_MiniGamesActivity"] = {
        TableKey = "id",
        SubTableNames = {
			"MiniGamesActivity",
        }
    },
    ["table_MiniGamesData"] = {
        TableKey = "id",
        SubTableNames = {
			"MiniGamesData",
        }
    },
    ["table_MiscConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MiscConfForLetsGo",
			"MiscConfForLetsGo_card",
			"MiscConfForLetsGo_chat",
        }
    },
    ["table_MiscFarmReturning"] = {
        TableKey = "id",
        SubTableNames = {
			"MiscFarmReturningConf",
        }
    },
    ["table_MobaPeakTournament"] = {
        TableKey = "id",
        SubTableNames = {
			"MobaPeakTournamentData",
        }
    },
    ["table_MobaPeakTournamentOpenTime"] = {
        TableKey = "id",
        SubTableNames = {
			"MobaPeakTournamentOpenTimeData",
        }
    },
    ["table_MobaRandomEventConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MobaRandomEventConfData",
        }
    },
    ["table_MobaRandomVoteConf"] = {
        TableKey = "activityId",
        SubTableNames = {
			"MobaRandomVoteConfData",
        }
    },
    ["table_ModPlayerDataConf"] = {
        TableKey = "serverType",
        SubTableNames = {
			"ModPlayerDataConfData",
        }
    },
    ["table_ModUnlockLevelConfData"] = {
        TableKey = "modId",
        SubTableNames = {
			"ModUnlockLevelConfData",
        }
    },
    ["table_ModeCupsStageConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ModeCupsStageConfigData",
        }
    },
    ["table_ModelSelectBtnInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"ModelSelectBtnInfo",
        }
    },
    ["table_MonsterConfigData"] = {
        TableKey = "typeId",
        SubTableNames = {
			"MonsterData",
        }
    },
    ["table_ResMoodPosData"] = {
        TableKey = "id",
        SubTableNames = {
			"MoodPos",
        }
    },
    ["table_MountPakOrderData"] = {
        TableKey = "pakName",
        SubTableNames = {
			"MountPakOrderData",
        }
    },
    ["table_MusicGroupConfig"] = {
        TableKey = "musicGroupId",
        SubTableNames = {
			"MusicGroupConfig",
        }
    },
    ["table_MusicOrderConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MusicOrderConfData",
        }
    },
    ["table_MusicOrderDailyDropConf"] = {
        TableKey = "day",
        SubTableNames = {
			"MusicOrderDailyDropConfData",
        }
    },
    ["table_MusicOrderMiscConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"MusicOrderMiscConfigData",
        }
    },
    ["table_MusicOrderMiscTextConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"MusicOrderMiscTextConfigData",
        }
    },
    ["table_MusicOrderModeDropConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MusicOrderModeDropConfData",
        }
    },
    ["table_MusicOrderModeGroupConf"] = {
        TableKey = "groupId",
        SubTableNames = {
			"MusicOrderModeGroupConfData",
        }
    },
    ["table_MusicOrderNoteDropConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MusicOrderNoteDropConfData",
        }
    },
    ["table_MusicOrderRefreshConf"] = {
        TableKey = "day",
        SubTableNames = {
			"MusicOrderRefreshConfData",
        }
    },
    ["table_MusicOrderTaskConf"] = {
        TableKey = "id",
        SubTableNames = {
			"MusicOrderTaskConfData",
        }
    },
    ["table_NPCData"] = {
        TableKey = "npcId",
        SubTableNames = {
			"NPCData",
        }
    },
    ["table_NPCFarmBuildingSkin"] = {
        TableKey = "suit,building",
        SubTableNames = {
			"NPCFarmBuildingSkin",
        }
    },
    ["table_NR3E3AIProxyData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3AIProxyData",
        }
    },
    ["table_NR3E3Competition_VocationGroupData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3Competition_VocationGroupData",
        }
    },
    ["table_NR3E3Competition_VocationPackData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3Competition_VocationPackData",
        }
    },
    ["table_NR3EGameConfigData"] = {
        TableKey = "ConfigName",
        SubTableNames = {
			"NR3E3GameConfig",
        }
    },
    ["table_NR3E3HuntTreasure"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3HuntTreasureData",
        }
    },
    ["table_NR3E3HuntTreasureLootPackage"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3HuntTreasureLootPackage",
        }
    },
    ["table_NR3E3HuntTreasurePoint"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3HuntTreasurePointData",
        }
    },
    ["table_NR3E3HuntTreasureTreasureSpawnPoint"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3HuntTreasureTreasureSpawnPoint",
        }
    },
    ["table_NR3E3MeetingBackground"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3MeetingBackgroundData",
        }
    },
    ["table_ResNR3E3MonthCardBuyLimit"] = {
        TableKey = "level",
        SubTableNames = {
			"NR3E3MonthCardBuyLimitData",
        }
    },
    ["table_ResNR3E3MonthCardClientDisplay"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3MonthCardFreeClientDisplay",
        }
    },
    ["table_ResNR3E3MonthCardFreeGift"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3MonthCardFreeGiftData",
        }
    },
    ["table_ResNR3E3MonthCardLevel"] = {
        TableKey = "level",
        SubTableNames = {
			"NR3E3MonthCardLevelData",
        }
    },
    ["table_ResNR3E3MonthCardPrivilege"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3MonthCardPrivilegeData",
        }
    },
    ["table_NR3E3MonthCardRechargeSubTab"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3MonthCardRechargeSubTab",
        }
    },
    ["table_NR3E3MonthCardRechargeTab"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3MonthCardRechargeTab",
        }
    },
    ["table_NR3E3RecommenedGamePlayGiftPackage"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3RecommenedGamePlayGiftPackage",
        }
    },
    ["table_NR3E3ReplaceVocationData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3ReplaceVocationData",
        }
    },
    ["table_NR3E3SpecialSkeletalData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3SuitToSkeletalMesh",
        }
    },
    ["table_NR3EThemeEventConfigData"] = {
        TableKey = "EventId",
        SubTableNames = {
			"NR3E3ThemeEventConfig",
        }
    },
    ["table_ResNR3E3TreasureBlackList"] = {
        TableKey = "Level",
        SubTableNames = {
			"NR3E3TreasureBlackListData",
        }
    },
    ["table_ResNR3E3Treasure"] = {
        TableKey = "level",
        SubTableNames = {
			"NR3E3TreasureData",
        }
    },
    ["table_ResNR3E3TreasureLevel1"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3TreasureLevel1Data",
        }
    },
    ["table_NR3E3VocationData"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E3VocationData",
        }
    },
    ["table_NR3E8BackpackItem"] = {
        TableKey = "id",
        SubTableNames = {
			"NR3E8BackpackItemData_decorate",
        }
    },
    ["table_NR3EHighlightMoment"] = {
        TableKey = "ID",
        SubTableNames = {
			"NR3EHighlightMoment",
        }
    },
    ["table_NR3EThemeEventItemConfigData"] = {
        TableKey = "CorrespondingThemesID",
        SubTableNames = {
			"NR3EThemeEventItemConfigData",
        }
    },
    ["table_NR3ETreasureHuntCampShopConfig"] = {
        TableKey = "ItemIndex",
        SubTableNames = {
			"NR3ETreasureHuntCampShopConfig",
        }
    },
    ["table_NR3ETreasureHuntNPCChatConfig"] = {
        TableKey = "DialogueID",
        SubTableNames = {
			"NR3ETreasureHuntNPCChatConfig",
        }
    },
    ["table_NewActivityPilotEntrance"] = {
        TableKey = "id",
        SubTableNames = {
			"NewActivityPilotEntranceData",
        }
    },
    ["table_NewActivityPilotTab"] = {
        TableKey = "id",
        SubTableNames = {
			"NewActivityPilotTabData",
        }
    },
    ["table_NewChatMiscConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"NewChatMiscConfig",
        }
    },
    ["table_NewShopTag"] = {
        TableKey = "id",
        SubTableNames = {
			"NewShopTag",
        }
    },
    ["table_NewYearPilotEntrance"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearPilotEntranceData",
        }
    },
    ["table_NewYearPilotTab"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearPilotTabData",
        }
    },
    ["table_NewYearSignData"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearSignData",
        }
    },
    ["table_NewYearWishesButtonTimeData"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearWishesButtonTimeData",
        }
    },
    ["table_NewYearWishesData"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearWishesData",
        }
    },
    ["table_NewYearWishesOtherData"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearWishesOtherData",
        }
    },
    ["table_NewYearWishesRandomFrames"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearWishesRandomFrames",
        }
    },
    ["table_NewYearWishesRandomPortraits"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearWishesRandomPortraits",
        }
    },
    ["table_NewYearWishesTextData"] = {
        TableKey = "id",
        SubTableNames = {
			"NewYearWishesTextData",
        }
    },
    ["table_NewbieTaskMisc"] = {
        TableKey = "id",
        SubTableNames = {
			"NewbieTaskMiscData",
        }
    },
    ["table_NicknameElementData"] = {
        TableKey = "id,place",
        SubTableNames = {
			"NicknameElementData_Adj",
			"NicknameElementData_Aux",
			"NicknameElementData_N",
			"NicknameElementData_Suffix",
        }
    },
    ["table_NicknameWidths"] = {
        TableKey = "charType",
        SubTableNames = {
			"NicknameWidthsData",
        }
    },
    ["table_NoticeMsgTypeTextData"] = {
        TableKey = "msgType",
        SubTableNames = {
			"NoticeMsgTypeTextData",
        }
    },
    ["table_NoviceRewardExtarData"] = {
        TableKey = "id",
        SubTableNames = {
			"NoviceRewardExtarData",
        }
    },
    ["table_NoviceRewardTaskMapData"] = {
        TableKey = "id",
        SubTableNames = {
			"NoviceRewardTaskMapData",
        }
    },
    ["table_OMDUGCMapCompilationData"] = {
        TableKey = "ugcMapId",
        SubTableNames = {
			"OMDUGCMapCompilationData",
        }
    },
    ["table_OffiFireworkEffectConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"OffiFireworkEffectConfigData",
        }
    },
    ["table_OfficialFireworkSpawnPosData"] = {
        TableKey = "id",
        SubTableNames = {
			"OfficialFireworkSpawnPosData",
        }
    },
    ["table_OtherVoiceConfig"] = {
        TableKey = "characterID",
        SubTableNames = {
			"OtherVoiceConfig",
        }
    },
    ["table_OutfitDelivery"] = {
        TableKey = "configId",
        SubTableNames = {
			"OutfitDeliveryData",
        }
    },
    ["table_OverseasTextHotFixEntryData"] = {
        TableKey = "key",
        SubTableNames = {
			"OverseasTextHotFixEntryData",
        }
    },
    ["table_PCKeyBindConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"PCKeyBindConfigData",
        }
    },
    ["table_PasswordCodeTaskConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"PasswordCodeTaskConfData",
        }
    },
    ["table_PerfGroupLimitData"] = {
        TableKey = "Id",
        SubTableNames = {
			"PerfGroupLimit",
        }
    },
    ["table_PerfWeightData"] = {
        TableKey = "Id",
        SubTableNames = {
			"PerfWeight",
        }
    },
    ["table_PerformQuickAccessData"] = {
        TableKey = "id",
        SubTableNames = {
			"PerformQuickAccessData",
        }
    },
    ["table_PermitConstantConf"] = {
        TableKey = "id",
        SubTableNames = {
			"PermitConstantConf",
        }
    },
    ["table_PermitRewardConfig"] = {
        TableKey = "id,level",
        SubTableNames = {
			"PermitRewardConfig",
        }
    },
    ["table_PermitTaskConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"PermitTaskConfig",
        }
    },
    ["table_PersonalCardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"PersonalCardConf",
        }
    },
    ["table_PersonalityLabelData"] = {
        TableKey = "id",
        SubTableNames = {
			"PersonalityLabelData",
        }
    },
    ["table_PersonalityStateData"] = {
        TableKey = "id",
        SubTableNames = {
			"PersonalityStateData",
        }
    },
    ["table_PetAnimationData"] = {
        TableKey = "id",
        SubTableNames = {
			"PetAnimationData",
        }
    },
    ["table_PianoConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"PianoConfig",
        }
    },
    ["table_PianoPlayConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"PianoPlayConfig",
        }
    },
    ["table_PilotEntrance"] = {
        TableKey = "id",
        SubTableNames = {
			"PilotEntranceData",
        }
    },
    ["table_PilotTab"] = {
        TableKey = "id",
        SubTableNames = {
			"PilotTabData",
        }
    },
    ["table_PinBlockInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"PinBlockInfoData",
        }
    },
    ["table_PlayModeSeasonConfData"] = {
        TableKey = "seasonId",
        SubTableNames = {
			"PlayModeSeasonConfData",
        }
    },
    ["table_PlayerGrayRuleConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerGrayRuleConfData",
        }
    },
    ["table_PlayerHeatConf"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerHeatConf",
        }
    },
    ["table_PlayerInfoDisplayIconData"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerInfoDisplayIconData",
        }
    },
    ["table_PlayerInfoSortConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerInfoSortConfig",
        }
    },
    ["table_PlayerInfoSubGroupTabData"] = {
        TableKey = "groupId",
        SubTableNames = {
			"PlayerInfoSubGroupTabData",
        }
    },
    ["table_PlayerInfoSubGroupTabInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerInfoSubGroupTabInfoData",
        }
    },
    ["table_PlayerInfoSubTabData"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerInfoSubTabData",
        }
    },
    ["table_PlayerLevelConfData"] = {
        TableKey = "level",
        SubTableNames = {
			"PlayerLevelConfData",
        }
    },
    ["table_PlayerStarterDressConf"] = {
        TableKey = "itemId",
        SubTableNames = {
			"PlayerStarterDressConfData",
        }
    },
    ["table_PlayerStarterFaceConf"] = {
        TableKey = "itemId",
        SubTableNames = {
			"PlayerStarterFaceConfData",
        }
    },
    ["table_PlayerUgcBadgeConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerUgcBadgeConf",
        }
    },
    ["table_PlayerUgcLevelConfData"] = {
        TableKey = "level",
        SubTableNames = {
			"PlayerUgcLevelConf",
        }
    },
    ["table_PlayerUgcLevelEnumConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"PlayerUgcLevelEnumConf",
        }
    },
    ["table_PlazaFacilityJumpConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"PlazaFacilityJumpConfig",
        }
    },
    ["table_PlotConfig"] = {
        TableKey = "id,plot_id",
        SubTableNames = {
			"PlotConfig",
        }
    },
    ["table_PlotDialogueConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"PlotDialogueConfigData",
        }
    },
    ["table_PoisonCircle"] = {
        TableKey = "id",
        SubTableNames = {
			"PoisonCircle",
        }
    },
    ["table_PoisonCircleTips"] = {
        TableKey = "id",
        SubTableNames = {
			"PoisonCircleTips",
        }
    },
    ["table_PrayData"] = {
        TableKey = "ResultId",
        SubTableNames = {
			"PrayData",
        }
    },
    ["table_PrayLunarCalendarConfig"] = {
        TableKey = "CalenderID",
        SubTableNames = {
			"PrayLunarCalendarConfig",
        }
    },
    ["table_PrayLunarSpecialConfig"] = {
        TableKey = "CalenderID",
        SubTableNames = {
			"PrayLunarSpecialConfig",
        }
    },
    ["table_PrayPropertyData"] = {
        TableKey = "PropertyId",
        SubTableNames = {
			"PrayPropertyData",
        }
    },
    ["table_PrayerCardSourceConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"PrayerCardSourceConfData",
        }
    },
    ["table_PreCachedData"] = {
        TableKey = "PreCachedName",
        SubTableNames = {
			"PreCachedConfig",
        }
    },
    ["table_PreWarnMarqueeNoticeConfData"] = {
        TableKey = "confId",
        SubTableNames = {
			"PreWarnMarqueeNoticeConfData",
        }
    },
    ["table_PreparationsMapSelectConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"PreparationsMapSelectConf",
        }
    },
    ["table_Preview"] = {
        TableKey = "id",
        SubTableNames = {
			"Preview",
        }
    },
    ["table_PreviewCameraConfig"] = {
        TableKey = "CameraTag",
        SubTableNames = {
			"PreviewCameraConfig",
        }
    },
    ["table_ProfileThemeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ProfileThemeData",
        }
    },
    ["table_ProtectedScoreAdditionalData"] = {
        TableKey = "id",
        SubTableNames = {
			"ProtectedScoreAdditionalData_acm_daluandou",
			"ProtectedScoreAdditionalData_bnb_paopaodazhan",
			"ProtectedScoreAdditionalData_fps_tuweimenghuandao",
			"ProtectedScoreAdditionalData_fps_wuqidashi",
			"ProtectedScoreAdditionalData_js_jisufeiche",
			"ProtectedScoreAdditionalData_lightning_jiangbeizhengduo",
			"ProtectedScoreAdditionalData_main_zhuwanfa",
			"ProtectedScoreAdditionalData_nr3e_duomaomao",
			"ProtectedScoreAdditionalData_nr3e_sheishilangren",
			"ProtectedScoreAdditionalData_nr3e_wodixingdong",
        }
    },
    ["table_PushFaceData"] = {
        TableKey = "id",
        SubTableNames = {
			"PushFaceData_Total",
        }
    },
    ["table_QAInvestCallbackUrlConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"QAInvestCallbackUrlConfigData",
        }
    },
    ["table_QAInvestConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"QAInvestData",
        }
    },
    ["table_DegreeTypeData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTDegreeTypeData_HOK",
			"QDTDegreeTypeData_Moba",
			"QDTDegreeTypeData_acm_daluandou",
			"QDTDegreeTypeData_bnb_paopaodazhan",
			"QDTDegreeTypeData_chase_dawangbiezhuawo",
			"QDTDegreeTypeData_fps_tuweimenghuandao",
			"QDTDegreeTypeData_fps_wuqidashi",
			"QDTDegreeTypeData_js_jisufeiche",
			"QDTDegreeTypeData_lightning_jiangbeizhengduo",
			"QDTDegreeTypeData_main_zhuwanfa",
			"QDTDegreeTypeData_nr3e_duomaomao",
			"QDTDegreeTypeData_nr3e_sheishilangren",
			"QDTDegreeTypeData_nr3e_wodixingdong",
        }
    },
    ["table_EspecialIntegralData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTEspecialIntegralData_HOK",
			"QDTEspecialIntegralData_Moba",
			"QDTEspecialIntegralData_acm_daluandou",
			"QDTEspecialIntegralData_bnb_paopaodazhan",
			"QDTEspecialIntegralData_chase_dawangbiezhuawo",
			"QDTEspecialIntegralData_fps_tuweimenghuandao",
			"QDTEspecialIntegralData_fps_wuqidashi",
			"QDTEspecialIntegralData_js_jisufeiche",
			"QDTEspecialIntegralData_lightning_jiangbeizhengduo",
			"QDTEspecialIntegralData_main_zhuwanfa",
			"QDTEspecialIntegralData_nr3e_duomaomao",
			"QDTEspecialIntegralData_nr3e_sheishilangren",
			"QDTEspecialIntegralData_nr3e_wodixingdong",
        }
    },
    ["table_QualifyingIntegralData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTQualifyingIntegralData_HOK",
			"QDTQualifyingIntegralData_Moba",
			"QDTQualifyingIntegralData_acm_daluandou",
			"QDTQualifyingIntegralData_bnb_paopaodazhan",
			"QDTQualifyingIntegralData_fps_tuweimenghuandao",
			"QDTQualifyingIntegralData_fps_wuqidashi",
			"QDTQualifyingIntegralData_js_jisufeiche",
			"QDTQualifyingIntegralData_lightning_jiangbeizhengduo",
			"QDTQualifyingIntegralData_main_zhuwanfa",
			"QDTQualifyingIntegralData_nr3e_duomaomao",
			"QDTQualifyingIntegralData_nr3e_sheishilangren",
			"QDTQualifyingIntegralData_nr3e_wodixingdong",
        }
    },
    ["table_QualifyingScoreData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTQualifyingScoreData_main_zhuwanfa",
        }
    },
    ["table_SeasonCfgData"] = {
        TableKey = "id",
        SubTableNames = {
			"QDTSeasonCfgData_HOK",
			"QDTSeasonCfgData_Moba",
			"QDTSeasonCfgData_acm_daluandou",
			"QDTSeasonCfgData_bnb_paopaodazhan",
			"QDTSeasonCfgData_chase_dawangbiezhuawo",
			"QDTSeasonCfgData_fps_tuweimenghuandao",
			"QDTSeasonCfgData_fps_wuqidashi",
			"QDTSeasonCfgData_js_jisufeiche",
			"QDTSeasonCfgData_lightning_jiangbeizhengduo",
			"QDTSeasonCfgData_main_zhuwanfa",
			"QDTSeasonCfgData_nr3e_duomaomao",
			"QDTSeasonCfgData_nr3e_sheishilangren",
			"QDTSeasonCfgData_nr3e_wodixingdong",
        }
    },
    ["table_QQWXLoginErrorCode"] = {
        TableKey = "id",
        SubTableNames = {
			"QQWXLoginErrorCode",
        }
    },
    ["table_QRCodeCalculationRuleData"] = {
        TableKey = "qrCnt",
        SubTableNames = {
			"QRCodeCalculationRuleData",
        }
    },
    ["table_QQGroupTeamPlayConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"QqGroupTeamPlayConfigData",
        }
    },
    ["table_QualifyingLevelDimensionConditionData"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingLevelDimensionConditionData_acm_daluandou",
			"QualifyingLevelDimensionConditionData_bnb_paopaodazhan",
			"QualifyingLevelDimensionConditionData_chase_dawangbiezhuawo",
			"QualifyingLevelDimensionConditionData_cups",
			"QualifyingLevelDimensionConditionData_fps_tuweimenghuandao",
			"QualifyingLevelDimensionConditionData_fps_wuqidashi",
			"QualifyingLevelDimensionConditionData_js_jisufeiche",
			"QualifyingLevelDimensionConditionData_lightning_jiangbeizhengduo",
			"QualifyingLevelDimensionConditionData_main_zhuwanfa",
			"QualifyingLevelDimensionConditionData_nr3e_duomaomao",
			"QualifyingLevelDimensionConditionData_nr3e_sheishilangren",
			"QualifyingLevelDimensionConditionData_nr3e_wodixingdong",
        }
    },
    ["table_QualifyingLevelDimensionIdData"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingLevelDimensionIdData_bnb_paopaodazhan",
			"QualifyingLevelDimensionIdData_lightning_jiangbeizhengduo",
			"QualifyingLevelDimensionIdData_nr3e_duomaomao",
			"QualifyingLevelDimensionIdData_nr3e_sheishilangren",
			"QualifyingLevelDimensionIdData_nr3e_wodixingdong",
        }
    },
    ["table_QualifyingLevelDimensionScoreData"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingLevelDimensionScoreData_Moba",
			"QualifyingLevelDimensionScoreData_acm_daluandou",
			"QualifyingLevelDimensionScoreData_bnb_paopaodazhan",
			"QualifyingLevelDimensionScoreData_chase_dawangbiezhuawo",
			"QualifyingLevelDimensionScoreData_fps_tuweimenghuandao",
			"QualifyingLevelDimensionScoreData_fps_wuqidashi",
			"QualifyingLevelDimensionScoreData_js_jisufeiche",
			"QualifyingLevelDimensionScoreData_lightning_jiangbeizhengduo",
			"QualifyingLevelDimensionScoreData_main_zhuwanfa",
			"QualifyingLevelDimensionScoreData_nr3e_duomaomao",
			"QualifyingLevelDimensionScoreData_nr3e_sheishilangren",
			"QualifyingLevelDimensionScoreData_nr3e_wodixingdong",
        }
    },
    ["table_QualifyingPerfScore"] = {
        TableKey = "id",
        SubTableNames = {
			"QualifyingPerfScoreData_Chase",
        }
    },
    ["table_QuestionConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"QuestionConfigData",
        }
    },
    ["table_QuickEntranceData"] = {
        TableKey = "id",
        SubTableNames = {
			"QuickEntranceData",
        }
    },
    ["table_QuickInteractConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"QuickInteractConfig",
        }
    },
    ["table_QuickInteractSceneConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"QuickInteractSceneConfig",
        }
    },
    ["table_QuickRewardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"QuickRewardConfData",
        }
    },
    ["table_QuizConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"QuizConfigData",
        }
    },
    ["table_RaffleAccessCfgData"] = {
        TableKey = "raffleId",
        SubTableNames = {
			"RaffleAccessCfgData_Accessories_hedy",
			"RaffleAccessCfgData_Accessories_huoyue",
			"RaffleAccessCfgData_Accessories_karl",
			"RaffleAccessCfgData_Accessories_main",
			"RaffleAccessCfgData_Accessories_miles",
			"RaffleAccessCfgData_Accessories_silvester",
			"RaffleAccessCfgData_Accessories_tesewanfa",
			"RaffleAccessCfgData_Accessories_yujun",
			"RaffleAccessCfgData_Card_hedy",
			"RaffleAccessCfgData_Card_huoyue",
			"RaffleAccessCfgData_Card_karl",
			"RaffleAccessCfgData_Card_main",
			"RaffleAccessCfgData_Card_miles",
			"RaffleAccessCfgData_Card_silvester",
			"RaffleAccessCfgData_Card_tesewanfa",
			"RaffleAccessCfgData_Card_yujun",
			"RaffleAccessCfgData_Common_hedy",
			"RaffleAccessCfgData_Common_huoyue",
			"RaffleAccessCfgData_Common_karl",
			"RaffleAccessCfgData_Common_main",
			"RaffleAccessCfgData_Common_miles",
			"RaffleAccessCfgData_Common_silvester",
			"RaffleAccessCfgData_Common_tesewanfa",
			"RaffleAccessCfgData_Common_yujun",
			"RaffleAccessCfgData_Disk_hedy",
			"RaffleAccessCfgData_Disk_huoyue",
			"RaffleAccessCfgData_Disk_karl",
			"RaffleAccessCfgData_Disk_main",
			"RaffleAccessCfgData_Disk_miles",
			"RaffleAccessCfgData_Disk_silvester",
			"RaffleAccessCfgData_Disk_tesewanfa",
			"RaffleAccessCfgData_Disk_yujun",
			"RaffleAccessCfgData_Iaa_hedy",
			"RaffleAccessCfgData_Iaa_huoyue",
			"RaffleAccessCfgData_Iaa_karl",
			"RaffleAccessCfgData_Iaa_main",
			"RaffleAccessCfgData_Iaa_miles",
			"RaffleAccessCfgData_Iaa_silvester",
			"RaffleAccessCfgData_Iaa_tesewanfa",
			"RaffleAccessCfgData_Iaa_yujun",
			"RaffleAccessCfgData_MonthActivity_hedy",
			"RaffleAccessCfgData_MonthActivity_huoyue",
			"RaffleAccessCfgData_MonthActivity_karl",
			"RaffleAccessCfgData_MonthActivity_main",
			"RaffleAccessCfgData_MonthActivity_miles",
			"RaffleAccessCfgData_MonthActivity_silvester",
			"RaffleAccessCfgData_MonthActivity_tesewanfa",
			"RaffleAccessCfgData_MonthActivity_yujun",
			"RaffleAccessCfgData_Premium_hedy",
			"RaffleAccessCfgData_Premium_huoyue",
			"RaffleAccessCfgData_Premium_karl",
			"RaffleAccessCfgData_Premium_main",
			"RaffleAccessCfgData_Premium_miles",
			"RaffleAccessCfgData_Premium_silvester",
			"RaffleAccessCfgData_Premium_tesewanfa",
			"RaffleAccessCfgData_Premium_yujun",
			"RaffleAccessCfgData_Purple_hedy",
			"RaffleAccessCfgData_Purple_huoyue",
			"RaffleAccessCfgData_Purple_karl",
			"RaffleAccessCfgData_Purple_main",
			"RaffleAccessCfgData_Purple_miles",
			"RaffleAccessCfgData_Purple_silvester",
			"RaffleAccessCfgData_Purple_tesewanfa",
			"RaffleAccessCfgData_Purple_yujun",
			"RaffleAccessCfgData_SocialFission_hedy",
			"RaffleAccessCfgData_SocialFission_huoyue",
			"RaffleAccessCfgData_SocialFission_karl",
			"RaffleAccessCfgData_SocialFission_main",
			"RaffleAccessCfgData_SocialFission_miles",
			"RaffleAccessCfgData_SocialFission_silvester",
			"RaffleAccessCfgData_SocialFission_tesewanfa",
			"RaffleAccessCfgData_SocialFission_yujun",
			"RaffleAccessCfgData_Vehicle_hedy",
			"RaffleAccessCfgData_Vehicle_huoyue",
			"RaffleAccessCfgData_Vehicle_karl",
			"RaffleAccessCfgData_Vehicle_main",
			"RaffleAccessCfgData_Vehicle_miles",
			"RaffleAccessCfgData_Vehicle_silvester",
			"RaffleAccessCfgData_Vehicle_tesewanfa",
			"RaffleAccessCfgData_Vehicle_yujun",
        }
    },
    ["table_RaffleBIData"] = {
        TableKey = "id",
        SubTableNames = {
			"RaffleBIData_TabOrder",
			"RaffleBIData_hedy",
			"RaffleBIData_huoyue",
			"RaffleBIData_karl",
			"RaffleBIData_main",
			"RaffleBIData_miles",
			"RaffleBIData_silvester",
			"RaffleBIData_tesewanfa",
			"RaffleBIData_yujun",
        }
    },
    ["table_RaffleBenefitSetData"] = {
        TableKey = "id",
        SubTableNames = {
			"RaffleBenefitSetData_hedy",
			"RaffleBenefitSetData_huoyue",
			"RaffleBenefitSetData_karl",
			"RaffleBenefitSetData_main",
			"RaffleBenefitSetData_miles",
			"RaffleBenefitSetData_silvester",
			"RaffleBenefitSetData_tesewanfa",
			"RaffleBenefitSetData_yujun",
        }
    },
    ["table_RaffleCfgData"] = {
        TableKey = "poolId",
        SubTableNames = {
			"RaffleCfgData_Accessories_hedy",
			"RaffleCfgData_Accessories_huoyue",
			"RaffleCfgData_Accessories_karl",
			"RaffleCfgData_Accessories_main",
			"RaffleCfgData_Accessories_miles",
			"RaffleCfgData_Accessories_silvester",
			"RaffleCfgData_Accessories_tesewanfa",
			"RaffleCfgData_Accessories_yujun",
			"RaffleCfgData_Card_hedy",
			"RaffleCfgData_Card_huoyue",
			"RaffleCfgData_Card_karl",
			"RaffleCfgData_Card_main",
			"RaffleCfgData_Card_miles",
			"RaffleCfgData_Card_silvester",
			"RaffleCfgData_Card_tesewanfa",
			"RaffleCfgData_Card_yujun",
			"RaffleCfgData_Common_hedy",
			"RaffleCfgData_Common_huoyue",
			"RaffleCfgData_Common_karl",
			"RaffleCfgData_Common_main",
			"RaffleCfgData_Common_miles",
			"RaffleCfgData_Common_silvester",
			"RaffleCfgData_Common_tesewanfa",
			"RaffleCfgData_Common_yujun",
			"RaffleCfgData_Disk_hedy",
			"RaffleCfgData_Disk_huoyue",
			"RaffleCfgData_Disk_karl",
			"RaffleCfgData_Disk_main",
			"RaffleCfgData_Disk_miles",
			"RaffleCfgData_Disk_silvester",
			"RaffleCfgData_Disk_tesewanfa",
			"RaffleCfgData_Disk_yujun",
			"RaffleCfgData_Iaa_hedy",
			"RaffleCfgData_Iaa_huoyue",
			"RaffleCfgData_Iaa_karl",
			"RaffleCfgData_Iaa_main",
			"RaffleCfgData_Iaa_miles",
			"RaffleCfgData_Iaa_silvester",
			"RaffleCfgData_Iaa_tesewanfa",
			"RaffleCfgData_Iaa_yujun",
			"RaffleCfgData_MonthActivity_hedy",
			"RaffleCfgData_MonthActivity_huoyue",
			"RaffleCfgData_MonthActivity_karl",
			"RaffleCfgData_MonthActivity_main",
			"RaffleCfgData_MonthActivity_miles",
			"RaffleCfgData_MonthActivity_silvester",
			"RaffleCfgData_MonthActivity_tesewanfa",
			"RaffleCfgData_MonthActivity_yujun",
			"RaffleCfgData_Premium_hedy",
			"RaffleCfgData_Premium_huoyue",
			"RaffleCfgData_Premium_karl",
			"RaffleCfgData_Premium_main",
			"RaffleCfgData_Premium_miles",
			"RaffleCfgData_Premium_silvester",
			"RaffleCfgData_Premium_tesewanfa",
			"RaffleCfgData_Premium_yujun",
			"RaffleCfgData_Purple_hedy",
			"RaffleCfgData_Purple_huoyue",
			"RaffleCfgData_Purple_karl",
			"RaffleCfgData_Purple_main",
			"RaffleCfgData_Purple_miles",
			"RaffleCfgData_Purple_silvester",
			"RaffleCfgData_Purple_tesewanfa",
			"RaffleCfgData_Purple_yujun",
			"RaffleCfgData_SocialFission_hedy",
			"RaffleCfgData_SocialFission_huoyue",
			"RaffleCfgData_SocialFission_karl",
			"RaffleCfgData_SocialFission_main",
			"RaffleCfgData_SocialFission_miles",
			"RaffleCfgData_SocialFission_silvester",
			"RaffleCfgData_SocialFission_tesewanfa",
			"RaffleCfgData_SocialFission_yujun",
			"RaffleCfgData_Vehicle_hedy",
			"RaffleCfgData_Vehicle_huoyue",
			"RaffleCfgData_Vehicle_karl",
			"RaffleCfgData_Vehicle_main",
			"RaffleCfgData_Vehicle_miles",
			"RaffleCfgData_Vehicle_silvester",
			"RaffleCfgData_Vehicle_tesewanfa",
			"RaffleCfgData_Vehicle_yujun",
        }
    },
    ["table_RaffleLoginRewardData"] = {
        TableKey = "raffleId",
        SubTableNames = {
			"RaffleLoginRewardData",
        }
    },
    ["table_RaffleLuckyValueData"] = {
        TableKey = "id",
        SubTableNames = {
			"RaffleLuckyValueData_hedy",
			"RaffleLuckyValueData_huoyue",
			"RaffleLuckyValueData_karl",
			"RaffleLuckyValueData_main",
			"RaffleLuckyValueData_miles",
			"RaffleLuckyValueData_silvester",
			"RaffleLuckyValueData_tesewanfa",
			"RaffleLuckyValueData_yujun",
        }
    },
    ["table_RaffleMajorGDrawData"] = {
        TableKey = "id",
        SubTableNames = {
			"RaffleMajorGDrawData_hedy",
			"RaffleMajorGDrawData_huoyue",
			"RaffleMajorGDrawData_karl",
			"RaffleMajorGDrawData_main",
			"RaffleMajorGDrawData_miles",
			"RaffleMajorGDrawData_silvester",
			"RaffleMajorGDrawData_tesewanfa",
			"RaffleMajorGDrawData_yujun",
        }
    },
    ["table_RaffleRewardCfgData"] = {
        TableKey = "rewardId",
        SubTableNames = {
			"RaffleRewardCfgData_hedy",
			"RaffleRewardCfgData_huoyue",
			"RaffleRewardCfgData_karl",
			"RaffleRewardCfgData_main",
			"RaffleRewardCfgData_miles",
			"RaffleRewardCfgData_silvester",
			"RaffleRewardCfgData_tesewanfa",
			"RaffleRewardCfgData_yujun",
        }
    },
    ["table_RaffleTabData"] = {
        TableKey = "id",
        SubTableNames = {
			"RaffleTabData",
        }
    },
    ["table_RandEventData"] = {
        TableKey = "id",
        SubTableNames = {
			"RandEventData_CharBlessings",
			"RandEventData_base",
			"RandEventData_comp",
        }
    },
    ["table_RandEventRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"RandEventRuleData",
        }
    },
    ["table_ArenaHeroAttr"] = {
        TableKey = "id,in_table_game_type",
        SubTableNames = {
			"RandomEventMonsterAttrData",
        }
    },
    ["table_RankKingDegreeData"] = {
        TableKey = "id",
        SubTableNames = {
			"RankKingDegreeData_HOK",
			"RankKingDegreeData_Moba",
			"RankKingDegreeData_acm_daluandou",
			"RankKingDegreeData_bnb_paopaodazhan",
			"RankKingDegreeData_chase_dawangbiezhuawo",
			"RankKingDegreeData_fps_tuweimenghuandao",
			"RankKingDegreeData_fps_wuqidashi",
			"RankKingDegreeData_js_jisufeiche",
			"RankKingDegreeData_lightning_jiangbeizhengduo",
			"RankKingDegreeData_main_zhuwanfa",
			"RankKingDegreeData_nr3e_duomaomao",
			"RankKingDegreeData_nr3e_sheishilangren",
			"RankKingDegreeData_nr3e_wodixingdong",
        }
    },
    ["table_RankingConfData"] = {
        TableKey = "rankId",
        SubTableNames = {
			"RankingConfData_Activity",
			"RankingConfData_Card",
			"RankingConfData_Common",
			"RankingConfData_Cup",
			"RankingConfData_Level",
			"RankingConfData_LevelFour",
			"RankingConfData_LevelOne",
			"RankingConfData_LevelTwo",
			"RankingConfData_PlayMode",
			"RankingConfData_Qualify",
			"RankingConfData_Ugc",
        }
    },
    ["table_RankingDisplayConfData"] = {
        TableKey = "labelId",
        SubTableNames = {
			"RankingDisplayConfData",
        }
    },
    ["table_RankingPlatSwitchConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RankingPlatSwitchConfData_History",
			"RankingPlatSwitchConfData_Season",
        }
    },
    ["table_RankingRewardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RankingRewardConfData_Activity",
        }
    },
    ["table_RankingSnapshotRewardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RankingSnapshotRewardConfData_Common",
			"RankingSnapshotRewardConfData_Lightning",
        }
    },
    ["table_ReadyBattleOrnamentationDataConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReadyBattleOrnamentationDataConfData",
        }
    },
    ["table_ReadyGoTipsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ReadyGoTipsConfig_main",
        }
    },
    ["table_RechargeArenaSubTab"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeArenaSubTab",
        }
    },
    ["table_RechargeArenaTab"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeArenaTab",
        }
    },
    ["table_RechargeAvatarBuy"] = {
        TableKey = "activityId",
        SubTableNames = {
			"RechargeAvatarBuy",
        }
    },
    ["table_ResRechargeDepositData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeDepositData",
        }
    },
    ["table_RechargeDoubleDiamondCfg"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeDoubleDiamondCfg",
        }
    },
    ["table_RechargeFarmMallFarmLogin"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeFarmMallFarmLogin",
        }
    },
    ["table_RechargeFarmSubTab"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeFarmSubTab",
        }
    },
    ["table_RechargeFarmTab"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeFarmTab",
        }
    },
    ["table_ResRechargeFarmTabSeqConf"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeFarmTabSeqConf",
        }
    },
    ["table_RechargeGiftConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeGiftConfigData",
        }
    },
    ["table_ResRechargeLevelConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeLevelConfData",
        }
    },
    ["table_ResRechargeMonthCardData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeMonthCardData",
			"RechargeMonthCardData_StarP",
        }
    },
    ["table_RechargeNavigationData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeNavigationData",
        }
    },
    ["table_RechargeRebateData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeRebateData",
        }
    },
    ["table_RechargeRebateShowData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeRebateShowData",
        }
    },
    ["table_ResRechargeScratchOffTicketData"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeScratchOffTicketData",
        }
    },
    ["table_ResRechargeTabSequenceConf"] = {
        TableKey = "id",
        SubTableNames = {
			"RechargeTabSequenceConf",
        }
    },
    ["table_RecommendDownloadData"] = {
        TableKey = "id",
        SubTableNames = {
			"RecommendDownloadData",
        }
    },
    ["table_RecommendMatchInTeam"] = {
        TableKey = "id",
        SubTableNames = {
			"RecommendMatchInTeam",
        }
    },
    ["table_RecommendMatchTypeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"RecommendMatchTypeConf",
        }
    },
    ["table_RecommenedGamePlayGiftPackage"] = {
        TableKey = "id",
        SubTableNames = {
			"RecommenedGamePlayGiftPackage",
        }
    },
    ["table_RecruitModeInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"RecruitModeInfoData_Main",
        }
    },
    ["table_RecruitTopicData"] = {
        TableKey = "TopicId",
        SubTableNames = {
			"RecruitTopicData",
        }
    },
    ["table_RedDotConfig"] = {
        TableKey = "moduleType,id,generalRedDotType",
        SubTableNames = {
			"RedDotConfigData",
			"RedDotTaskConfigData",
        }
    },
    ["table_RedDotShowConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"RedDotShowConfigData",
        }
    },
    ["table_RedEnvelopeActivityData"] = {
        TableKey = "id",
        SubTableNames = {
			"RedEnvelopeActivityData",
        }
    },
    ["table_RedEnvelopeSpawnerSettingData"] = {
        TableKey = "id",
        SubTableNames = {
			"RedEnvelopeSpawnerSettingData",
        }
    },
    ["table_RedEnvelopeTurnSettingData"] = {
        TableKey = "id",
        SubTableNames = {
			"RedEnvelopeTurnSettingData",
        }
    },
    ["table_RedPacketBaseConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RedPacketBaseConf",
        }
    },
    ["table_RedPacketCommonConfData"] = {
        TableKey = "key",
        SubTableNames = {
			"RedPacketCommonConf",
        }
    },
    ["table_RedPacketPreviewConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RedPacketPreviewConfData",
        }
    },
    ["table_RedPacketReplyTextData"] = {
        TableKey = "textId",
        SubTableNames = {
			"RedPacketReplyText",
        }
    },
    ["table_RedPacketSkinConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RedPacketSkinConf",
        }
    },
    ["table_RedPacketTaskShowConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RedPacketTaskShowConfData",
        }
    },
    ["table_RegionalConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"RegionalConfigData",
        }
    },
    ["table_RelationBattleModeIntimacyConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RelationBattleModeIntimacyConfData",
        }
    },
    ["table_RelationClientMiscData"] = {
        TableKey = "Key",
        SubTableNames = {
			"RelationClientMiscData",
        }
    },
    ["table_RelationConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RelationConfData",
        }
    },
    ["table_RelationMiscConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RelationMiscConfData",
        }
    },
    ["table_ReportContentConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ReportContentData",
        }
    },
    ["table_ReportEntryConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ReportEntryConfData",
        }
    },
    ["table_ReputationBehaviourDefData"] = {
        TableKey = "ID",
        SubTableNames = {
			"ReputationBehaviourDef",
        }
    },
    ["table_ReputationScoreRuleConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ReputationScoreRuleConfig",
        }
    },
    ["table_ReputationSysCommonConfigData"] = {
        TableKey = "ConfigName",
        SubTableNames = {
			"ReputationSysCommonConfig",
        }
    },
    ["table_ResAbilityConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ResAbilityConfig",
        }
    },
    ["table_ResAgreementsData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResAgreementsData",
        }
    },
    ["table_ResArenaGuideHeroData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResArenaGuideHeroData",
        }
    },
    ["table_ArenaPreparationsBtnConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ResArenaPreparationsBtnConfig",
        }
    },
    ["table_ResFactionConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResFactionConfData",
        }
    },
    ["table_ResFirstChargeConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResFirstChargeConfData",
        }
    },
    ["table_ResGCParamData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResGCParamData",
        }
    },
    ["table_GunGameKillReportData"] = {
        TableKey = "event",
        SubTableNames = {
			"ResGunGameKillReportData",
        }
    },
    ["table_ResHOKMonsterExtraBuffLayout"] = {
        TableKey = "id",
        SubTableNames = {
			"ResHOKMonsterExtraBuffLayout",
        }
    },
    ["table_ResHOKMonsterLayout"] = {
        TableKey = "id",
        SubTableNames = {
			"ResHOKMonsterLayout_Default",
			"ResHOKMonsterLayout_ShengLingFuTi",
        }
    },
    ["table_ResIndicatorGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"ResIndicatorGroup_pack",
        }
    },
    ["table_ResIndicator"] = {
        TableKey = "id",
        SubTableNames = {
			"ResIndicator_pack",
        }
    },
    ["table_ResMallSeasonShopTab"] = {
        TableKey = "id",
        SubTableNames = {
			"ResMallNR3EShopTab",
			"ResMallSeasonShopTab",
        }
    },
    ["table_MatchPakDetailKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"ResMatchPakDetailKVConf",
        }
    },
    ["table_ResNewHeroTrainConfig"] = {
        TableKey = "ActiveId",
        SubTableNames = {
			"ResNewHeroTrainConf",
        }
    },
    ["table_ResNoviceRewardBigPrizeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResNoviceRewardBigPrizeData",
        }
    },
    ["table_ResNoviceRewardPageAData"] = {
        TableKey = "systemId",
        SubTableNames = {
			"ResNoviceRewardPageAData",
        }
    },
    ["table_ResNoviceRewardPageBData"] = {
        TableKey = "systemId",
        SubTableNames = {
			"ResNoviceRewardPageBData",
        }
    },
    ["table_ResNoviceRewardPageData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResNoviceRewardPageData",
        }
    },
    ["table_ResNoviceRewardTabData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResNoviceRewardTabData",
        }
    },
    ["table_ResNoviceRewardTaskXMData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResNoviceRewardTaskXMData",
        }
    },
    ["table_PlayerBillboard"] = {
        TableKey = "id",
        SubTableNames = {
			"ResPlayerBillboard",
        }
    },
    ["table_PreparationsData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResPreparations",
        }
    },
    ["table_PreparationsWidgetConfData"] = {
        TableKey = "buttonname",
        SubTableNames = {
			"ResPreparationsWidgetConfData",
        }
    },
    ["table_ResPropConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ResPropConfig",
        }
    },
    ["table_ResRoguelikeCharacterGradeData"] = {
        TableKey = "gradeIndex",
        SubTableNames = {
			"ResRoguelikeCharacterGradeData",
        }
    },
    ["table_ResRoguelikeChestData"] = {
        TableKey = "chestID",
        SubTableNames = {
			"ResRoguelikeChestData",
        }
    },
    ["table_ResRoguelikeEndlessLevelData"] = {
        TableKey = "levelType",
        SubTableNames = {
			"ResRoguelikeEndlessLevelData",
        }
    },
    ["table_ResRoguelikeEndlessRewardData"] = {
        TableKey = "levelNum",
        SubTableNames = {
			"ResRoguelikeEndlessRewardData",
        }
    },
    ["table_ResRoguelikeEventData"] = {
        TableKey = "eventId",
        SubTableNames = {
			"ResRoguelikeEventData",
        }
    },
    ["table_RoguelikeExclusiveMarkData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResRoguelikeExclusiveMarkData",
        }
    },
    ["table_ResRoguelikeGetTalentData"] = {
        TableKey = "ld",
        SubTableNames = {
			"ResRoguelikeGetTalentData",
        }
    },
    ["table_ResRoguelikeLevelData"] = {
        TableKey = "groupId",
        SubTableNames = {
			"ResRoguelikeLevelData",
        }
    },
    ["table_ResRoguelikeLevelSelectData"] = {
        TableKey = "ld",
        SubTableNames = {
			"ResRoguelikeLevelSelectData",
        }
    },
    ["table_ResRoguelikeLoadingMapData"] = {
        TableKey = "mapType",
        SubTableNames = {
			"ResRoguelikeLoadingMapData",
        }
    },
    ["table_ResRoguelikeMarkBaseEffectData"] = {
        TableKey = "baseEffectId",
        SubTableNames = {
			"ResRoguelikeMarkBaseEffectData",
        }
    },
    ["table_ResRoguelikeMarkBoardShapeData"] = {
        TableKey = "boardShapeId",
        SubTableNames = {
			"ResRoguelikeMarkBoardShapeData",
        }
    },
    ["table_ResRoguelikeMarkBoxData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResRoguelikeMarkBoxData",
        }
    },
    ["table_ResRoguelikeMarkConstData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResRoguelikeMarkConstData",
        }
    },
    ["table_ResRoguelikeMarkEntryData"] = {
        TableKey = "entryId",
        SubTableNames = {
			"ResRoguelikeMarkEntryData",
        }
    },
    ["table_ResRoguelikeMarkLevelDropData"] = {
        TableKey = "ld",
        SubTableNames = {
			"ResRoguelikeMarkLevelDropData",
        }
    },
    ["table_ResRoguelikeMarkMaterialData"] = {
        TableKey = "level",
        SubTableNames = {
			"ResRoguelikeMarkMaterialData",
        }
    },
    ["table_ResRoguelikeMarkShapeData"] = {
        TableKey = "shapeId",
        SubTableNames = {
			"ResRoguelikeMarkShapeData",
        }
    },
    ["table_ResRoguelikeMarkSpecialEffectData"] = {
        TableKey = "specialEffectId",
        SubTableNames = {
			"ResRoguelikeMarkSpecialEffectData",
        }
    },
    ["table_ResRoguelikeMonsterData"] = {
        TableKey = "monsterID",
        SubTableNames = {
			"ResRoguelikeMonsterData",
        }
    },
    ["table_ResRoguelikeMonsterHPParameter"] = {
        TableKey = "levelIndex",
        SubTableNames = {
			"ResRoguelikeMonsterHPParameter",
        }
    },
    ["table_ResRoguelikeMonsterHandBookData"] = {
        TableKey = "handBookID",
        SubTableNames = {
			"ResRoguelikeMonsterHandBookData",
        }
    },
    ["table_ResRoguelikeMonsterParameter"] = {
        TableKey = "ld",
        SubTableNames = {
			"ResRoguelikeMonsterParameter",
        }
    },
    ["table_ResRoguelikeProcessData"] = {
        TableKey = "sceneId",
        SubTableNames = {
			"ResRoguelikeProcessData",
        }
    },
    ["table_ResRoguelikePropsData"] = {
        TableKey = "propsID",
        SubTableNames = {
			"ResRoguelikePropsData",
        }
    },
    ["table_ResRoguelikeSeasonData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResRoguelikeSeasonData",
        }
    },
    ["table_ResRoguelikeTaskData"] = {
        TableKey = "taskId",
        SubTableNames = {
			"ResRoguelikeSeasonDayTaskData",
			"ResRoguelikeSeasonTaskData",
			"ResRoguelikeSeasonWeekTaskData",
			"ResRoguelikeTaskData",
        }
    },
    ["table_ResRoguelikeSeasonMilestoneData"] = {
        TableKey = "level",
        SubTableNames = {
			"ResRoguelikeSeasonMilestoneData",
        }
    },
    ["table_ResRoguelikeSkillData"] = {
        TableKey = "skillId",
        SubTableNames = {
			"ResRoguelikeSkillData",
        }
    },
    ["table_ResRoguelikeSpecialLevelData"] = {
        TableKey = "ld",
        SubTableNames = {
			"ResRoguelikeSpecialLevelData",
        }
    },
    ["table_ResRoguelikeTalentData"] = {
        TableKey = "talentId",
        SubTableNames = {
			"ResRoguelikeTalentData",
        }
    },
    ["table_ResRoguelikeTaskOtherData"] = {
        TableKey = "DataName",
        SubTableNames = {
			"ResRoguelikeTaskOtherData",
        }
    },
    ["table_ResRotatingScreenData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResRotatingScreenData",
        }
    },
    ["table_ResShareGiftLimit"] = {
        TableKey = "limitId",
        SubTableNames = {
			"ResShareGiftLimit",
        }
    },
    ["table_ResShareGiftType"] = {
        TableKey = "typeId",
        SubTableNames = {
			"ResShareGiftType",
        }
    },
    ["table_ResSpringSlipAward"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSpringSlipAward",
        }
    },
    ["table_ResSpringSlipConfData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ResSpringSlipConfData",
        }
    },
    ["table_ResSpringSlipData"] = {
        TableKey = "index",
        SubTableNames = {
			"ResSpringSlipData",
        }
    },
    ["table_ResSpringSynthesisAward"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSpringSynthesisAward",
        }
    },
    ["table_ResSuitCustomTag"] = {
        TableKey = "id",
        SubTableNames = {
			"ResSuitCustomTag",
        }
    },
    ["table_TextRuleDescData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResTextRuleDescData_RedEnvelope",
			"ResTextRuleDescData_main",
        }
    },
    ["table_UGCGuideTipsData"] = {
        TableKey = "GuideID",
        SubTableNames = {
			"ResUGCGuideTips",
        }
    },
    ["table_ResUGCLobbyNavIcons"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUGCLobbyNavIcons",
        }
    },
    ["table_UPClientKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"ResUPClientKVConf",
			"ResUPClientKVConf_Chase",
			"ResUPClientKVConf_OGC",
        }
    },
    ["table_UPComponentData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUPComponentData",
			"ResUPComponentData_Chase",
			"ResUPComponentData_OGC",
        }
    },
    ["table_UPGameSettingData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUPGameSettingData",
        }
    },
    ["table_UPMatchTypeClassInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUPMatchTypeClassInfo",
			"ResUPMatchTypeClassInfo_Chase",
			"ResUPMatchTypeClassInfo_OGC",
        }
    },
    ["table_UPMoreButtonItemData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUPMoreButtonItemData",
			"ResUPMoreButtonItemData_Chase",
        }
    },
    ["table_UPPreviewInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUPPreviewInfo",
        }
    },
    ["table_UPTemplateData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUPTemplateData",
        }
    },
    ["table_UniversalPreparationData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResUniversalPreparationData",
        }
    },
    ["table_ResourceDefaultShowData"] = {
        TableKey = "id",
        SubTableNames = {
			"ResourceDefaultShowData",
        }
    },
    ["table_ReturnBookLevelConf"] = {
        TableKey = "level",
        SubTableNames = {
			"ReturnBookLevelData",
        }
    },
    ["table_ReturnLoginGiveAway"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturnLoginGiveAwayData",
        }
    },
    ["table_ReturnPrivilegesData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturnPrivilegesData",
        }
    },
    ["table_ReturnPushFaceReward"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturnPushFaceReward",
        }
    },
    ["table_ReturnPushFaceSettingData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturnPushFaceSettingData",
        }
    },
    ["table_ReturnPushFaceSettingDataV2"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturnPushFaceSettingDataV2",
        }
    },
    ["table_ReturnStartMatchGiftData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturnStartMatchGiftData",
        }
    },
    ["table_ReturningDailyBenefitsLimitData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningDailyBenefitsLimitData",
        }
    },
    ["table_ReturningDailyPushFaceData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningDailyPushFaceData",
        }
    },
    ["table_ReturningFarmTable"] = {
        TableKey = "ActivityId",
        SubTableNames = {
			"ReturningFarmTable",
        }
    },
    ["table_ReturningNavigationData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningNavigationData",
        }
    },
    ["table_ReturningNewTable"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningNewTableData",
        }
    },
    ["table_ReturningRecommend2Data"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningRecommend2Data",
        }
    },
    ["table_ReturningRecommendData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningRecommendData",
        }
    },
    ["table_ReturningTabConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningTabConfigData",
        }
    },
    ["table_ReturningUserConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningUserConfData",
        }
    },
    ["table_ReturningUserConstsData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningUserConstsData",
        }
    },
    ["table_ReturningWellcomeBgCfg"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningWellcomeBgCfg",
        }
    },
    ["table_ReturningWellcomeData"] = {
        TableKey = "id",
        SubTableNames = {
			"ReturningWellcomeData",
        }
    },
    ["table_RewardCompensateConf"] = {
        TableKey = "type",
        SubTableNames = {
			"RewardCompensateConfData",
        }
    },
    ["table_RewardRetrievalConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"RewardRetrievalConfData",
        }
    },
    ["table_RewardRetrievalConfTaskData"] = {
        TableKey = "id",
        SubTableNames = {
			"RewardRetrievalConfTaskData",
        }
    },
    ["table_RightUpBtnInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"RightUpBtnInfo",
        }
    },
    ["table_RightUpInfoData"] = {
        TableKey = "Index",
        SubTableNames = {
			"RightUpInfoData",
        }
    },
    ["table_RightUpOrderData"] = {
        TableKey = "Index",
        SubTableNames = {
			"RightUpOrderConfig",
        }
    },
    ["table_RoomSideRoleData"] = {
        TableKey = "id",
        SubTableNames = {
			"RoomSideRoleData",
        }
    },
    ["table_SayHiEntranceConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SayHiEntranceConfData",
        }
    },
    ["table_SayHiPopConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SayHiPopConfData",
        }
    },
    ["table_SayHiTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SayHiTextConfData",
        }
    },
    ["table_PlaceableActorConfigData"] = {
        TableKey = "typeId",
        SubTableNames = {
			"SceneFlowData",
			"SceneFlowData_AnimalParty",
			"SceneFlowData_Arena",
			"SceneFlowData_Community",
			"SceneFlowData_Decorate",
			"SceneFlowData_FPS",
			"SceneFlowData_Furniture",
			"SceneFlowData_Gears",
			"SceneFlowData_Ground",
			"SceneFlowData_Item",
			"SceneFlowData_LevelPrivate",
			"SceneFlowData_Logic",
			"SceneFlowData_Monster",
			"SceneFlowData_NPC",
			"SceneFlowData_NR3E",
			"SceneFlowData_OMD",
			"SceneFlowData_Prop",
			"SceneFlowData_Weapon",
			"SceneFlowData_bnb",
        }
    },
    ["table_SceneGiftPackageConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SceneGiftPackageData",
        }
    },
    ["table_SceneGiftShowStyleConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SceneGiftShowStyle",
        }
    },
    ["table_ScenePerformanceLevelData"] = {
        TableKey = "id",
        SubTableNames = {
			"ScenePerformanceLevelData",
        }
    },
    ["table_ScenePerformanceOperatingData"] = {
        TableKey = "RiskLevel",
        SubTableNames = {
			"ScenePerformanceOperatingData",
        }
    },
    ["table_ScenePublishRiskDescData"] = {
        TableKey = "RiskType",
        SubTableNames = {
			"ScenePublishRiskDesc",
        }
    },
    ["table_ScenePublishRiskRemindData"] = {
        TableKey = "SceneLevel",
        SubTableNames = {
			"ScenePublishRiskRemind",
        }
    },
    ["table_ScoreGuideActivityData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ScoreGuideActivityData",
        }
    },
    ["table_ScoreGuideConditionData"] = {
        TableKey = "id",
        SubTableNames = {
			"ScoreGuideConditionData",
        }
    },
    ["table_ScoreOptionsData"] = {
        TableKey = "Id",
        SubTableNames = {
			"ScoreOptionsData_High",
			"ScoreOptionsData_Low",
			"ScoreOptionsData_Middle",
        }
    },
    ["table_SeasonConfData"] = {
        TableKey = "seasonId",
        SubTableNames = {
			"SeasonConfData",
        }
    },
    ["table_SeasonFashionBattleDataConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonFashionBattleDataConfData",
        }
    },
    ["table_SeasonFashionGameTimesShowConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonFashionGameTimesShowConfData",
        }
    },
    ["table_SeasonFashionMetaBattleDataConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonFashionMetaBattleDataConfData",
        }
    },
    ["table_SeasonFashionModelDataConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonFashionModelDataConfData",
        }
    },
    ["table_SeasonFashionShowTypeConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonFashionShowTypeConfData",
        }
    },
    ["table_SeasonFashionSubGroupIdConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonFashionSubGroupIdConfData",
        }
    },
    ["table_SeasonQualifyingMail"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonQualifyingMailData_Moba",
			"SeasonQualifyingMailData_acm_daluandou",
			"SeasonQualifyingMailData_bnb_paopaodazhan",
			"SeasonQualifyingMailData_chase_dawangbiezhuawo",
			"SeasonQualifyingMailData_fps_tuweimenghuandao",
			"SeasonQualifyingMailData_fps_wuqidashi",
			"SeasonQualifyingMailData_js_jisufeiche",
			"SeasonQualifyingMailData_lightning_jiangbeizhengduo",
			"SeasonQualifyingMailData_nr3e_duomaomao",
			"SeasonQualifyingMailData_nr3e_sheishilangren",
			"SeasonQualifyingMailData_nr3e_wodixingdong",
        }
    },
    ["table_SeasonReviewEvaluateConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonReviewEvaluateConfData",
        }
    },
    ["table_SeasonReviewFiveDimensionsConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonReviewFiveDimensionsConfData",
        }
    },
    ["table_SeasonReviewMetaConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SeasonReviewMetaConf",
        }
    },
    ["table_SeasonSubmoduleConfData"] = {
        TableKey = "seasonId,moduleId",
        SubTableNames = {
			"SeasonSubmoduleConfData",
        }
    },
    ["table_SeasonTalkConfData"] = {
        TableKey = "seasonId,stageId,seqId",
        SubTableNames = {
			"SeasonTalkConfData",
        }
    },
    ["table_SelfStateData"] = {
        TableKey = "id",
        SubTableNames = {
			"SelfStateData",
        }
    },
    ["table_SensitiveFilterList"] = {
        TableKey = "type",
        SubTableNames = {
			"SensitiveFilterData",
        }
    },
    ["table_ServerKvConfig"] = {
        TableKey = "key",
        SubTableNames = {
			"ServerKvConfig",
        }
    },
    ["table_ClientServerTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ServerTextConfData",
			"ServerTextConfData_main",
        }
    },
    ["table_ServerTextData"] = {
        TableKey = "id",
        SubTableNames = {
			"ServerTextData",
        }
    },
    ["table_ServerVipData"] = {
        TableKey = "domain,vipIdx",
        SubTableNames = {
			"ServerVipData",
        }
    },
    ["table_SettingRightsConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SettingRightsConf",
        }
    },
    ["table_SfxAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SfxAudioConfig",
			"UGCSfxAudioConfig",
        }
    },
    ["table_SfxPhyMatConfig"] = {
        TableKey = "phyMatName",
        SubTableNames = {
			"SfxPhyMatConfig",
        }
    },
    ["table_ShareGameChatTypeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ShareGameChatTypeConfig",
        }
    },
    ["table_ShootBuffConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ShootBuffConfigData",
			"ShootBuffConfigDataUGCSkill",
        }
    },
    ["table_ShootEventConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"ShootEventConfigData",
        }
    },
    ["table_ResLetsGoShopTag"] = {
        TableKey = "id",
        SubTableNames = {
			"ShopTagForLetsGo",
        }
    },
    ["table_ShuttleItem"] = {
        TableKey = "id",
        SubTableNames = {
			"ShuttleData",
        }
    },
    ["table_SnsInvitationConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SnsInvitationConfData",
        }
    },
    ["table_SnsShareConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SnsShareConfigData",
        }
    },
    ["table_SocialPerformanceData"] = {
        TableKey = "id",
        SubTableNames = {
			"SocialPerformanceData",
        }
    },
    ["table_SoundMusicConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SoundMusicConfig",
        }
    },
    ["table_SoundPointConfig"] = {
        TableKey = "pointName",
        SubTableNames = {
			"SoundPointConfig",
        }
    },
    ["table_SpawnMonsterConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SpawnMonsterConfig",
        }
    },
    ["table_SpecRewardConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SpecRewardConfig",
        }
    },
    ["table_SpecialAvatarPropScaleConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SpecialAvatarPropScaleConfig",
        }
    },
    ["table_SpeedCompensationConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SpeedCompensationConfig",
        }
    },
    ["table_SpineEmojiConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SpineEmojiConf",
        }
    },
    ["table_SpringBgmData"] = {
        TableKey = "id",
        SubTableNames = {
			"SpringBgmData",
        }
    },
    ["table_SpringBlessingCardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SpringBlessingCardConfData",
        }
    },
    ["table_SpringBlessingCardRewardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SpringBlessingCardRewardConfData",
        }
    },
    ["table_SpringBlessingCollectionConfData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"SpringBlessingCollectionConfData",
        }
    },
    ["table_SpringBlessingSponsorConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"SpringBlessingSponsorConfData",
        }
    },
    ["table_SpringRedPacketConfData"] = {
        TableKey = "Id",
        SubTableNames = {
			"SpringRedPacketConfData",
        }
    },
    ["table_SquadActivityChatShareConfig"] = {
        TableKey = "activityId",
        SubTableNames = {
			"SquadActivityChatShareData",
        }
    },
    ["table_StaticMountPakData"] = {
        TableKey = "pakName",
        SubTableNames = {
			"StaticMountPakData",
        }
    },
    ["table_StepGuideData"] = {
        TableKey = "StepID",
        SubTableNames = {
			"StepGuideData_Mayday",
			"StepGuideData_Mouse_pc",
			"StepGuideData_acm",
			"StepGuideData_acm_pc",
			"StepGuideData_arena",
			"StepGuideData_arena_pc",
			"StepGuideData_bs",
			"StepGuideData_cloud",
			"StepGuideData_coc",
			"StepGuideData_cook",
			"StepGuideData_farm",
			"StepGuideData_hok",
			"StepGuideData_home",
			"StepGuideData_js",
			"StepGuideData_main",
			"StepGuideData_main_pc",
			"StepGuideData_nr3e",
			"StepGuideData_nr3e8",
			"StepGuideData_ugc",
			"StepGuideData_va",
			"StepGuideData_wuqidashi",
			"StepGuideData_wuqidashi_pc",
        }
    },
    ["table_StickerBingoRewardConfData"] = {
        TableKey = "activityId,chapterId,positionId",
        SubTableNames = {
			"StickerBingoRewardConfData",
        }
    },
    ["table_StickerChapterRewardConfData"] = {
        TableKey = "activityId,chapterId",
        SubTableNames = {
			"StickerChapterRewardConfData",
        }
    },
    ["table_StickerConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"StickerConfData",
        }
    },
    ["table_StickerNormalRewardConfData"] = {
        TableKey = "activityId,chapterId,positionId",
        SubTableNames = {
			"StickerNormalRewardConfData",
        }
    },
    ["table_StorageCleanConf"] = {
        TableKey = "Category",
        SubTableNames = {
			"StorageCleanConf",
        }
    },
    ["table_StorageCleanPath"] = {
        TableKey = "Path",
        SubTableNames = {
			"StorageCleanPath",
        }
    },
    ["table_StreamOpenIdWhitelistData"] = {
        TableKey = "openId",
        SubTableNames = {
			"StreamOpenIdWhitelistData",
        }
    },
    ["table_SuitAbilityConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"SuitAbilityConfig",
        }
    },
    ["table_SuitPreviewOffsetZ"] = {
        TableKey = "id",
        SubTableNames = {
			"SuitPreviewOffsetZ",
        }
    },
    ["table_SuitSeasonInfoData"] = {
        TableKey = "seasonId",
        SubTableNames = {
			"SuitSeasonInfoData",
        }
    },
    ["table_SuitThemedInfoData"] = {
        TableKey = "themedId",
        SubTableNames = {
			"SuitThemedInfoData",
        }
    },
    ["table_SummerFlashPhotoConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SummerFlashPhotoConf",
        }
    },
    ["table_SummerFlashUgcLobbyMapId"] = {
        TableKey = "id",
        SubTableNames = {
			"SummerFlashUgcLobbyMapId",
        }
    },
    ["table_SummerNavigationBarTaskData"] = {
        TableKey = "activityId,taskId",
        SubTableNames = {
			"SummerNavigationBarTaskData",
        }
    },
    ["table_SummerVacationBPBuffConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SummerVacationBPBuffConf",
        }
    },
    ["table_SuperCoreConf"] = {
        TableKey = "id",
        SubTableNames = {
			"SuperCoreConf",
        }
    },
    ["table_SurroundingsEffectData"] = {
        TableKey = "id",
        SubTableNames = {
			"SurroundingsEffectData",
        }
    },
    ["table_SystemChunkGroupDependencyData"] = {
        TableKey = "WindowName",
        SubTableNames = {
			"SystemChunkGroupDependencyData",
        }
    },
    ["table_SystemChunkGroupDependencyDataVA"] = {
        TableKey = "WindowName",
        SubTableNames = {
			"SystemChunkGroupDependencyDataVA",
        }
    },
    ["table_TABTestConfig"] = {
        TableKey = "TestType",
        SubTableNames = {
			"TABTestData",
        }
    },
    ["table_TDMonsterBuffConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDMonsterBuffConfData",
        }
    },
    ["table_TDMonsterPoolData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDMonsterPoolData",
        }
    },
    ["table_TDMonsterWaveData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDMonsterWaveData",
        }
    },
    ["table_TDWeaponPoolData"] = {
        TableKey = "id",
        SubTableNames = {
			"TDWeaponPoolData",
        }
    },
    ["table_TYCBodyArmorConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCBodyArmorConfData",
        }
    },
    ["table_TYCBuffConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCBuffConfData",
        }
    },
    ["table_TYCBulletConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCBulletConfData",
        }
    },
    ["table_TYCDropConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCDropConf",
        }
    },
    ["table_TYCPropConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCPropConf",
        }
    },
    ["table_TYCSkillConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCSkillConfData",
        }
    },
    ["table_TYCTrapConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCTrapConfData",
        }
    },
    ["table_TYCTrapSkinConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCTrapSkinConfData",
        }
    },
    ["table_TYCTrapUpgradeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCTrapUpgradeConfData",
        }
    },
    ["table_TYCTriggerConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TYCTriggerConfData",
        }
    },
    ["table_TakeawayBoxConf"] = {
        TableKey = "boxId",
        SubTableNames = {
			"TakeawayBoxConfData",
        }
    },
    ["table_TakeawayConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TakeawayConfData",
        }
    },
    ["table_TaskConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TaskConfData_EntertainmentGuide",
			"TaskConfData_GameModeReturn",
			"TaskConfData_GuideToApp",
			"TaskConfData_LuckyFriend",
			"TaskConfData_UgcActivity",
			"TaskConfData_achievement",
			"TaskConfData_activity",
			"TaskConfData_activitycommercialization",
			"TaskConfData_activitydesign",
			"TaskConfData_activityyunying",
			"TaskConfData_arena_hero_star",
			"TaskConfData_back",
			"TaskConfData_bp",
			"TaskConfData_bp_main",
			"TaskConfData_bp_maingame",
			"TaskConfData_bp_moba",
			"TaskConfData_bp_nr3e_sheishilangren",
			"TaskConfData_channel",
			"TaskConfData_chaseUnlock",
			"TaskConfData_chase_daily",
			"TaskConfData_collection",
			"TaskConfData_common",
			"TaskConfData_cup",
			"TaskConfData_download",
			"TaskConfData_dye",
			"TaskConfData_hok",
			"TaskConfData_level",
			"TaskConfData_lobby",
			"TaskConfData_mode",
			"TaskConfData_modyunying",
			"TaskConfData_newbie",
			"TaskConfData_noviceReward",
			"TaskConfData_noviceSeven",
			"TaskConfData_qrcode",
			"TaskConfData_recharge",
			"TaskConfData_retrieve",
			"TaskConfData_season",
			"TaskConfData_season_lightning",
			"TaskConfData_star",
			"TaskConfData_ugc",
			"TaskConfData_wxGameDayTask",
			"TaskConfData_wxGameShare_WX",
        }
    },
    ["table_TaskGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"TaskGroupData_EntertainmentGuide",
			"TaskGroupData_GameModeReturn",
			"TaskGroupData_GuideToApp",
			"TaskGroupData_UgcActivity",
			"TaskGroupData_activity",
			"TaskGroupData_activitycommercialization",
			"TaskGroupData_activitydesign",
			"TaskGroupData_activityyunying",
			"TaskGroupData_arena_hero_star",
			"TaskGroupData_bp_main",
			"TaskGroupData_bp_maingame",
			"TaskGroupData_bp_moba",
			"TaskGroupData_bp_nr3e_sheishilangren",
			"TaskGroupData_chaseUnlock",
			"TaskGroupData_chase_daily",
			"TaskGroupData_common",
			"TaskGroupData_common_lightning",
			"TaskGroupData_cup",
			"TaskGroupData_hok",
			"TaskGroupData_modyunying",
			"TaskGroupData_noviceReward",
			"TaskGroupData_noviceSeven",
			"TaskGroupData_recharge",
			"TaskGroupData_retrieve",
			"TaskGroupData_wxGameDayTask",
			"TaskGroupData_wxGameShare_WX",
        }
    },
    ["table_TaskGroupList"] = {
        TableKey = "id",
        SubTableNames = {
			"TaskGroupListData_wxGameTaskGroupListData",
        }
    },
    ["table_TaskOptionalRewardData"] = {
        TableKey = "id",
        SubTableNames = {
			"TaskOptionalRewardData",
        }
    },
    ["table_TaskPictureUrlConf"] = {
        TableKey = "taskID",
        SubTableNames = {
			"TaskPictureUrlConfData",
        }
    },
    ["table_TaskRandomRewardConfData"] = {
        TableKey = "rewardId",
        SubTableNames = {
			"TaskRandomRewardConfData_chase_daily",
        }
    },
    ["table_TaskRandomRewardPoolConfData"] = {
        TableKey = "poolId",
        SubTableNames = {
			"TaskRandomRewardPoolConfData_chase_daily",
        }
    },
    ["table_TaskShareConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TaskShareConfigData",
        }
    },
    ["table_TaskTab"] = {
        TableKey = "id",
        SubTableNames = {
			"TaskTabData",
        }
    },
    ["table_TeamBattleBroadcastResConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamBattleBroadcastResConf",
        }
    },
    ["table_TeamGroupTextConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamGroupTextConfDataLobby",
        }
    },
    ["table_TeamMemberViewTemplateData"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamMemberViewTemplateData",
        }
    },
    ["table_TeamModePlaySwitchRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamModePlaySwitchRuleData",
        }
    },
    ["table_TeamPhotoActionTemplateConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamPhotoActionTemplateConfig",
        }
    },
    ["table_TeamPhotoBackgroundConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamPhotoBackgroundConfig",
        }
    },
    ["table_TeamPhotoCustomConfig"] = {
        TableKey = "key",
        SubTableNames = {
			"TeamPhotoCustomConfig",
        }
    },
    ["table_TeamPhotoEditTabConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamPhotoEditTabConfig",
        }
    },
    ["table_TeamRecruitMatch"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamRecruitMatch",
        }
    },
    ["table_TeamRecruitMatchCatory"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamRecruitMatchCatory",
        }
    },
    ["table_TeamRecruitMode"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamRecruitMode",
        }
    },
    ["table_TeamShowClientKVConf"] = {
        TableKey = "key",
        SubTableNames = {
			"TeamShowClientKVConfForLetsGo",
        }
    },
    ["table_TeamShowData"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamShowData",
        }
    },
    ["table_TeamShowVehicleAnimData"] = {
        TableKey = "id",
        SubTableNames = {
			"TeamShowVehicleAnimData",
        }
    },
    ["table_TerrainConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TerrainConfig",
        }
    },
    ["table_TextEntryData"] = {
        TableKey = "stringId",
        SubTableNames = {
			"TextEntryData_AC",
			"TextEntryData_Arena",
			"TextEntryData_BS",
			"TextEntryData_COC",
			"TextEntryData_Card",
			"TextEntryData_Club",
			"TextEntryData_FB",
			"TextEntryData_FPS",
			"TextEntryData_HOK",
			"TextEntryData_Level",
			"TextEntryData_Main",
			"TextEntryData_Mayday",
			"TextEntryData_Preload",
			"TextEntryData_SuitStory",
			"TextEntryData_System",
			"TextEntryData_TYC",
			"TextEntryData_Travel",
			"TextEntryData_UGCProgramme",
			"TextEntryData_UGCRPG",
			"TextEntryData_chase",
        }
    },
    ["table_TextEntryNR3EData"] = {
        TableKey = "id",
        SubTableNames = {
			"TextEntryNR3EData_Competition",
			"TextEntryNR3EData_main",
        }
    },
    ["table_TextEntryRelation"] = {
        TableKey = "id",
        SubTableNames = {
			"TextEntryRelationApply",
			"TextEntryRelationCommunityFacilityID",
			"TextEntryRelationDDPProp",
			"TextEntryRelationDMMProp",
			"TextEntryRelationRecommend",
        }
    },
    ["table_TextEntryXiaowo"] = {
        TableKey = "id",
        SubTableNames = {
			"TextEntryXiaowo",
        }
    },
    ["table_TextLuaData"] = {
        TableKey = "id",
        SubTableNames = {
			"TextLuaData",
        }
    },
    ["table_ThemeAdventureActivityConfData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"ThemeAdventureActivityConfData",
        }
    },
    ["table_ThemeAdventureRewardUpgradeConfData"] = {
        TableKey = "activityId,sourceItemId",
        SubTableNames = {
			"ThemeAdventureRewardUpgradeConfData",
        }
    },
    ["table_ThemeMallInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"ThemeMallInfoData",
        }
    },
    ["table_TimeLimitedCheckInActivityConfData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"TimeLimitedCheckInActivityConfData",
        }
    },
    ["table_TradingCardActivityConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TradingCardActivityConfigData",
        }
    },
    ["table_TradingCardCollectionConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TradingCardCollectionConfigData",
        }
    },
    ["table_TradingCardConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TradingCardConfigData",
        }
    },
    ["table_TradingCardCycleCupsConfig"] = {
        TableKey = "cycleId,stageId",
        SubTableNames = {
			"TradingCardCycleCupsConfigData",
        }
    },
    ["table_TradingCardCycleCupsRuleConfig"] = {
        TableKey = "cycleId",
        SubTableNames = {
			"TradingCardCycleCupsRuleConfigData",
        }
    },
    ["table_TradingCardDeckConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TradingCardDeckConfigData",
        }
    },
    ["table_TradingCardExchangeConfig"] = {
        TableKey = "collectionId,exchangeId",
        SubTableNames = {
			"TradingCardExchangeConfigData",
        }
    },
    ["table_TradingCardNoviceRewardConfig"] = {
        TableKey = "collectionId,id",
        SubTableNames = {
			"TradingCardNoviceRewardConfigData",
        }
    },
    ["table_TradingCardRedDotRuleConfig"] = {
        TableKey = "redDotType,redDotId",
        SubTableNames = {
			"TradingCardRedDotRuleConfigData",
        }
    },
    ["table_TradingCardTradeRuleConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"TradingCardTradeRuleConfigData",
        }
    },
    ["table_TrainingActivityAwardConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TrainingActivityAwardConf",
        }
    },
    ["table_TrainingActivityConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TrainingActivityConf",
        }
    },
    ["table_TrainingCampSportsmanConf"] = {
        TableKey = "id",
        SubTableNames = {
			"TrainingCampSportsmanConf",
        }
    },
    ["table_TravelingDogAndPlayerData"] = {
        TableKey = "id",
        SubTableNames = {
			"TravelingDogAndPlayerData",
        }
    },
    ["table_TravelingDogConfData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"TravelingDogConfData",
        }
    },
    ["table_TravelingDogPhotoData"] = {
        TableKey = "name",
        SubTableNames = {
			"TravelingDogPhotoData",
        }
    },
    ["table_TreasureLevelUpBoxData"] = {
        TableKey = "id",
        SubTableNames = {
			"TreasureLevelUpBoxData",
        }
    },
    ["table_TreasureLevelUpData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"TreasureLevelUpData",
        }
    },
    ["table_TriggerGuidePeriodData"] = {
        TableKey = "ID",
        SubTableNames = {
			"TriggerGuidePeriodData",
        }
    },
    ["table_UGCAIBehaveRule"] = {
        TableKey = "ActionId",
        SubTableNames = {
			"UGCAIBehaveRule",
        }
    },
    ["table_UGCAIBuildColorData"] = {
        TableKey = "ColorId",
        SubTableNames = {
			"UGCAIBuildColorConf",
        }
    },
    ["table_UGCAIBuildFoundationData"] = {
        TableKey = "FoundationId",
        SubTableNames = {
			"UGCAIBuildFoundationConf",
        }
    },
    ["table_UGCAIBuildItemData"] = {
        TableKey = "ItemId",
        SubTableNames = {
			"UGCAIBuildItemConf",
        }
    },
    ["table_UGCAIBuildParameterData"] = {
        TableKey = "ParameterId",
        SubTableNames = {
			"UGCAIBuildParameterConf",
        }
    },
    ["table_UGCAIBuildTypeData"] = {
        TableKey = "TypeId",
        SubTableNames = {
			"UGCAIBuildTypeConf",
        }
    },
    ["table_UGCAIDifficultyParam"] = {
        TableKey = "LevelId",
        SubTableNames = {
			"UGCAIDifficultyParam",
        }
    },
    ["table_UGCAIMapToLevel"] = {
        TableKey = "MapID",
        SubTableNames = {
			"UGCAIMapToLevel",
        }
    },
    ["table_UGCAchievement"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCAchievement_Icon",
        }
    },
    ["table_UGCActorAdvancedInfo"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCActorAdvancedInfo",
        }
    },
    ["table_UGCActorInfo"] = {
        TableKey = "ItemID",
        SubTableNames = {
			"UGCActorInfo_AIGCModel",
			"UGCActorInfo_Building",
			"UGCActorInfo_Character",
			"UGCActorInfo_Construction",
			"UGCActorInfo_Decorate",
			"UGCActorInfo_Furniture",
			"UGCActorInfo_Nature",
			"UGCActorInfo_Stratagem",
        }
    },
    ["table_UGCArtisanGradeInfo"] = {
        TableKey = "Grade",
        SubTableNames = {
			"UGCArtisanGradeInfo",
        }
    },
    ["table_UGCAudioInfo"] = {
        TableKey = "Order",
        SubTableNames = {
			"UGCAudioInfo",
        }
    },
    ["table_UGCAudioTabInfo"] = {
        TableKey = "Type",
        SubTableNames = {
			"UGCAudioTabInfo",
        }
    },
    ["table_UGCCameraPreset"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCCameraPreset_Combine",
			"UGCCameraPreset_FPS",
			"UGCCameraPreset_Main",
        }
    },
    ["table_UGCCustomEquipType"] = {
        TableKey = "TypeId",
        SubTableNames = {
			"UGCCustomEquipType",
        }
    },
    ["table_UGCDialogue"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCDialogue_Animation",
			"UGCDialogue_Emoji",
			"UGCDialogue_Face",
			"UGCDialogue_Sound",
			"UGCDialogue_box",
        }
    },
    ["table_UGCEditorBagTab"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCEditorBagTab",
        }
    },
    ["table_UGCEditorBarrageTypeOptions"] = {
        TableKey = "Index",
        SubTableNames = {
			"UGCEditorBarrageTypeOptions",
        }
    },
    ["table_UGCEditorCameraSpeedOptions"] = {
        TableKey = "Index",
        SubTableNames = {
			"UGCEditorCameraSpeedOptions",
        }
    },
    ["table_UGCEditorGroupTag"] = {
        TableKey = "tagId",
        SubTableNames = {
			"UGCEditorGroupTag",
        }
    },
    ["table_UGCEditorItemSort"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCEditorItemSort",
        }
    },
    ["table_UGCEditorItemTag"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCEditorItemTag",
        }
    },
    ["table_UGCEditorMapDataAutoSave"] = {
        TableKey = "Index",
        SubTableNames = {
			"UGCEditorMapDataAutoSave",
        }
    },
    ["table_UGCEditorMapDataAutoSaveCloud"] = {
        TableKey = "Index",
        SubTableNames = {
			"UGCEditorMapDataAutoSaveCloud",
        }
    },
    ["table_UGCEditorMapGroup"] = {
        TableKey = "GroupId",
        SubTableNames = {
			"UGCEditorMapGroup",
        }
    },
    ["table_UGCEditorMapLoadingTemplate"] = {
        TableKey = "templateId",
        SubTableNames = {
			"UGCEditorMapLoadingTemplate",
        }
    },
    ["table_UGCEditorMapModule"] = {
        TableKey = "ModuleId",
        SubTableNames = {
			"UGCEditorMapModule",
        }
    },
    ["table_UGCEditorMapProperty"] = {
        TableKey = "PropertyId",
        SubTableNames = {
			"UGCEditorMapProperty",
        }
    },
    ["table_UGCEditorMapTab"] = {
        TableKey = "tabId",
        SubTableNames = {
			"UGCEditorMapTab",
        }
    },
    ["table_UGCEditorMapTag"] = {
        TableKey = "tagId",
        SubTableNames = {
			"UGCEditorMapTag",
        }
    },
    ["table_UGCEditorMapTagBlock"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCEditorMapTagBlock",
        }
    },
    ["table_UGCEditorMapTemplate"] = {
        TableKey = "TemplateId",
        SubTableNames = {
			"UGCEditorMapTemplate",
        }
    },
    ["table_UGCEditorMapType"] = {
        TableKey = "typeId",
        SubTableNames = {
			"UGCEditorMapType",
        }
    },
    ["table_UGCEditorOMDTab"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCEditorOMDTab",
        }
    },
    ["table_UGCEditorQuickOperation"] = {
        TableKey = "Index",
        SubTableNames = {
			"UGCEditorQuickOperation",
        }
    },
    ["table_UGCEditorWeaponAction"] = {
        TableKey = "RowName",
        SubTableNames = {
			"UGCEditorWeaponAction",
        }
    },
    ["table_UGCImageBagInfo"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCImageBagInfo",
        }
    },
    ["table_UGCMapCommonConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCMapCommonConf",
        }
    },
    ["table_UGCMapShareConfig"] = {
        TableKey = "shareChannel",
        SubTableNames = {
			"UGCMapShareConfig",
        }
    },
    ["table_UGCMonsterInfo"] = {
        TableKey = "ItemID",
        SubTableNames = {
			"UGCMonsterInfo",
        }
    },
    ["table_UGCNPCAppearance"] = {
        TableKey = "item_id",
        SubTableNames = {
			"UGCNPCAppearance_BackOrnament",
			"UGCNPCAppearance_Face",
			"UGCNPCAppearance_FaceOrnament",
			"UGCNPCAppearance_Gloves",
			"UGCNPCAppearance_HeadWear",
			"UGCNPCAppearance_LowerGarment",
			"UGCNPCAppearance_Suit",
			"UGCNPCAppearance_UpperGarment",
        }
    },
    ["table_UGCNPCInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCNPCInfoData",
        }
    },
    ["table_UGCOMDAbilityCustom"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCOMDAbilityCustom",
        }
    },
    ["table_UGCOMDDropPool"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCOMDDropPool",
        }
    },
    ["table_UGCOMDLevelPath"] = {
        TableKey = "LevelId",
        SubTableNames = {
			"UGCOMDLevelPath",
        }
    },
    ["table_UGCPACustomAttributeItem"] = {
        TableKey = "tabId",
        SubTableNames = {
			"UGCPACustomAttributeItemData",
        }
    },
    ["table_UGCParticleInfo"] = {
        TableKey = "Order",
        SubTableNames = {
			"UGCParticleInfo",
        }
    },
    ["table_UGCParticleTabInfo"] = {
        TableKey = "Type",
        SubTableNames = {
			"UGCParticleTabInfo",
        }
    },
    ["table_UGCProgramPropItemConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCProgramPropItemConfigData",
        }
    },
    ["table_UGCRankingMenu"] = {
        TableKey = "labelId",
        SubTableNames = {
			"UGCRankingMenu",
        }
    },
    ["table_ResUGCResCategoryTab"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCResCategoryTabConfig",
        }
    },
    ["table_ResUGCResConf"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCResConf",
        }
    },
    ["table_ResUGCResConfNew"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCResConfNew",
        }
    },
    ["table_ResUGCResConfCategory"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCResConf_Category",
        }
    },
    ["table_ResUGCResConfLabel"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCResConf_Label",
        }
    },
    ["table_ResUGCResConfSubCategory"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCResConf_Subcategory",
        }
    },
    ["table_UGCResourceInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCResourceInfo",
        }
    },
    ["table_UGCRoomSetting"] = {
        TableKey = "key",
        SubTableNames = {
			"UGCRoomSetting",
        }
    },
    ["table_UGCRoundGamePropsData"] = {
        TableKey = "itemID",
        SubTableNames = {
			"UGCRoundGamePropsData",
        }
    },
    ["table_UGCSkillEditorAnimation"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorAnimation",
        }
    },
    ["table_UGCSkillEditorBagTab"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorBagTab",
        }
    },
    ["table_UGCSkillEditorBuffIcon"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorBuffIcon",
        }
    },
    ["table_UGCSkillEditorGetItem"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorGetItem",
        }
    },
    ["table_UGCSkillEditorPreviewBg"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorPreviewBg",
        }
    },
    ["table_UGCSkillEditorPreviewCharacter"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorPreviewCharacter",
        }
    },
    ["table_UGCSkillEditorPreviewWeapon"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorPreviewWeapon",
        }
    },
    ["table_UGCSkillEditorProjectile"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorProjectile",
        }
    },
    ["table_UGCSkillEditorSkillIcon"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorSkillIcon",
        }
    },
    ["table_UGCSkillEditorSummon"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCSkillEditorSummon",
        }
    },
    ["table_UGCSkillIndicatorConf"] = {
        TableKey = "indicator_id",
        SubTableNames = {
			"UGCSkillIndicatorData",
        }
    },
    ["table_UGCSkillEffectMutex"] = {
        TableKey = "type",
        SubTableNames = {
			"UGCSkillStateEffectMutex",
        }
    },
    ["table_UGCTextHighData"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCTextHighConf",
        }
    },
    ["table_UGCUIEditorBagInfo_Function"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_Function",
        }
    },
    ["table_UGCUIEditorBagInfo_UIProgress"] = {
        TableKey = "ID",
        SubTableNames = {
			"UGCUIEditorBagInfo_UIProgress",
        }
    },
    ["table_UGCUIEditorBagTab"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCUIEditorBagTab",
        }
    },
    ["table_UGCUIEditorBagTabs"] = {
        TableKey = "id",
        SubTableNames = {
			"UGCUIEditorBagTabs",
        }
    },
    ["table_UGCWeaponSkinForIP"] = {
        TableKey = "suitId",
        SubTableNames = {
			"UGCWeaponSkinForIP",
        }
    },
    ["table_UIAudioConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UIAudioConfig",
        }
    },
    ["table_UPGameSettingOpenBtnInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"UPGameSettingOpenBtnInfo_NR3E",
        }
    },
    ["table_UPGameSettingOptionConf"] = {
        TableKey = "id",
        SubTableNames = {
			"UPGameSettingOptionConf_NR3E",
        }
    },
    ["table_UPGameSettingTabConf"] = {
        TableKey = "id",
        SubTableNames = {
			"UPGameSettingTabConf_NR3E",
        }
    },
    ["table_UPMatchConditionInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"UPMatchConditionInfo_Chase",
        }
    },
    ["table_UPModelSelectBtnInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"UPModelSelectBtnInfo_Chase",
			"UPModelSelectBtnInfo_OGC",
        }
    },
    ["table_UPReportInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"UPReportInfo",
			"UPReportInfo_Chase",
			"UPReportInfo_OGC",
			"UPReportInfo_PreviewTeam",
        }
    },
    ["table_UPSideSelectBtnInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"UPSideSelectBtnInfo_Chase",
        }
    },
    ["table_UgcBankConfig"] = {
        TableKey = "timbreType",
        SubTableNames = {
			"UgcBassBankConfig",
			"UgcEffectBankConfig",
			"UgcMelodyBankConfig",
			"UgcRhythmBankConfig",
        }
    },
    ["table_UgcWiseConfig"] = {
        TableKey = "key",
        SubTableNames = {
			"UgcBassWiseConfig",
			"UgcEffectWiseConfig",
			"UgcMelodyWiseConfig",
			"UgcRhythmWiseConfig",
        }
    },
    ["table_UgcBulletSelectorData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcBulletSelectorData",
        }
    },
    ["table_UgcCommonConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcCommonConfig",
        }
    },
    ["table_UgcCosMapping"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcCosMapping",
        }
    },
    ["table_UgcCreativeRoad"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcCreativeRoadData",
        }
    },
    ["table_UgcCreatorBadgeConf"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcCreatorBadgeConf",
        }
    },
    ["table_UgcCreatorTagConf"] = {
        TableKey = "tagId",
        SubTableNames = {
			"UgcCreatorTagConf",
        }
    },
    ["table_UgcCreatorTagTypeConf"] = {
        TableKey = "tagType",
        SubTableNames = {
			"UgcCreatorTagTypeConf",
        }
    },
    ["table_UgcEffectTypeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcEffectTypeConfig",
        }
    },
    ["table_UgcEntranceBubbleConf"] = {
        TableKey = "bubbleType",
        SubTableNames = {
			"UgcEntranceBubbleConfData",
        }
    },
    ["table_UgcExpCalcParamsData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcExpCalcParamsData",
        }
    },
    ["table_UgcExpData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcExpData",
        }
    },
    ["table_UgcForbiddenTopics"] = {
        TableKey = "topic",
        SubTableNames = {
			"UgcForbiddenTopicsData",
        }
    },
    ["table_UgcImageBagTab"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcImageBagTab",
        }
    },
    ["table_UgcMapRankRewardConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcMapRankRewardConfData",
        }
    },
    ["table_UgcMapScoreLabelConf"] = {
        TableKey = "labelId",
        SubTableNames = {
			"UgcMapScoreLabelConf",
        }
    },
    ["table_UgcMatchMiniGameAreaIdData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcMatchMiniGameAreaIdData",
        }
    },
    ["table_UgcMatchRoomIdPoolData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcMatchRoomIdPoolData",
        }
    },
    ["table_UgcMatchRoomParamData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcMatchRoomParamData",
        }
    },
    ["table_UgcMatchRoomRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcMatchRoomRuleData",
        }
    },
    ["table_UgcMelodyGroupConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcMelodyGroupConfig",
        }
    },
    ["table_UgcNameAdpterConfig"] = {
        TableKey = "simpleId",
        SubTableNames = {
			"UgcNameAdpterConfig",
        }
    },
    ["table_UgcNameIconConfig"] = {
        TableKey = "nameId",
        SubTableNames = {
			"UgcNameHighIconConfig",
			"UgcNameSimpleIconConfig",
        }
    },
    ["table_UgcNewYearConstConf"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcNewYearConstConfData",
        }
    },
    ["table_UgcParticleMaterialData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcParticleMaterialData",
        }
    },
    ["table_UgcParticleParamNameToIndex"] = {
        TableKey = "index",
        SubTableNames = {
			"UgcParticleParamNameToIndex",
        }
    },
    ["table_UgcPathConfig"] = {
        TableKey = "ConfigId",
        SubTableNames = {
			"UgcPathConfig",
        }
    },
    ["table_UgcPropItemConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcPropItemConfig",
        }
    },
    ["table_UgcPropTabConfig"] = {
        TableKey = "TabID",
        SubTableNames = {
			"UgcPropTabConfig",
        }
    },
    ["table_UgcQuickChatData"] = {
        TableKey = "Order",
        SubTableNames = {
			"UgcQuickChatData",
        }
    },
    ["table_UgcRocketBulletSelectorData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcRocketBulletSelectorData",
        }
    },
    ["table_UgcStarWorldDifficultyGroup"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcStarWorldDifficultyGroup",
        }
    },
    ["table_UgcStarWorldDifficultyReward"] = {
        TableKey = "difficulty",
        SubTableNames = {
			"UgcStarWorldDifficultyReward",
        }
    },
    ["table_UgcStarWorldLevelConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcStarWorldLevelConfig",
        }
    },
    ["table_UgcTemplateConflictOutlookData"] = {
        TableKey = "id",
        SubTableNames = {
			"UgcTemplateConflictOutlookData",
        }
    },
    ["table_UgcWhiteListModuleConfData"] = {
        TableKey = "moduleId",
        SubTableNames = {
			"UgcWhiteListModuleConfData",
        }
    },
    ["table_UltramanTaskJumpConfigData"] = {
        TableKey = "id",
        SubTableNames = {
			"UltramanTaskJumpConfigData",
        }
    },
    ["table_UltramanThemeCollectionConfigData"] = {
        TableKey = "rewardId,activityId",
        SubTableNames = {
			"UltramanThemeCollectionConfigData",
        }
    },
    ["table_UltramanThemeConfigData"] = {
        TableKey = "stage,activityId",
        SubTableNames = {
			"UltramanThemeConfigData",
        }
    },
    ["table_UpdateForesightActivityExtraInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"UpdateForesightActivityExtraInfoData",
        }
    },
    ["table_UserLabelConf"] = {
        TableKey = "id",
        SubTableNames = {
			"UserLabelConf",
        }
    },
    ["table_VAFarmMoreGamePlayConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"VAFarmMoreGamePlayConfig",
        }
    },
    ["table_VAFarmMoreGamePlayLabelConfig"] = {
        TableKey = "ID",
        SubTableNames = {
			"VAFarmMoreGamePlayLabelConfig",
        }
    },
    ["table_ValentineDayConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"ValentineDayConfData",
        }
    },
    ["table_VehicleStatusChangeData"] = {
        TableKey = "id",
        SubTableNames = {
			"VehicleStatusChangeData_main",
        }
    },
    ["table_VerticalChatBanWindowConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"VerticalChatBanWindowConfig",
        }
    },
    ["table_ViewOptionSettingConf"] = {
        TableKey = "id",
        SubTableNames = {
			"ViewOptionSettingConfData",
        }
    },
    ["table_VoiceKeyWordsConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"VoiceKeyWordsConfig",
        }
    },
    ["table_WXGameGuidConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"WXGameGuidConfig",
        }
    },
    ["table_WXGameHudBannerConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"WXGameHudBannerConfig",
        }
    },
    ["table_WXGameHudMenuConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"WXGameHudMenuConfig",
			"WXGameHudMenuConfig_NewHud",
        }
    },
    ["table_WXGameShareTaskShareContentData"] = {
        TableKey = "id",
        SubTableNames = {
			"WXGameShareTaskShareContentData_WX",
        }
    },
    ["table_WXMiniGameSubscriptionConfig"] = {
        TableKey = "sceneId",
        SubTableNames = {
			"WXMiniGameSubscriptionConfig",
        }
    },
    ["table_WXSubscriptionConfig"] = {
        TableKey = "sceneId",
        SubTableNames = {
			"WXSubscriptionConfig",
        }
    },
    ["table_WealthBankCheckInReward"] = {
        TableKey = "day",
        SubTableNames = {
			"WealthBankCheckInRewardData",
        }
    },
    ["table_WealthBankConfig"] = {
        TableKey = "configType",
        SubTableNames = {
			"WealthBankConfigData",
        }
    },
    ["table_WeekendGiftCheckInConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"WeekendGiftCheckInConfig",
        }
    },
    ["table_WerewolfFullReducedConfData"] = {
        TableKey = "couponID",
        SubTableNames = {
			"WerewolfFullReducedConfig",
        }
    },
    ["table_WhiteCfgData"] = {
        TableKey = "id",
        SubTableNames = {
			"WhiteCfgData",
        }
    },
    ["table_WholeGameProcessRuleData"] = {
        TableKey = "id",
        SubTableNames = {
			"WholeGameProcessRuleData",
        }
    },
    ["table_WildCardConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"WildCardConfigData",
        }
    },
    ["table_WishesCameTrueGiftConf"] = {
        TableKey = "id",
        SubTableNames = {
			"WishesCameTrueGiftConf",
        }
    },
    ["table_WishesCameTrueGiftCostConf"] = {
        TableKey = "id",
        SubTableNames = {
			"WishesCameTrueGiftCostConf",
        }
    },
    ["table_WishesCameTrueInvitationRewardsConf"] = {
        TableKey = "id",
        SubTableNames = {
			"WishesCameTrueInvitationRewardsConf",
        }
    },
    ["table_WishingTreeActivityData"] = {
        TableKey = "activityId",
        SubTableNames = {
			"WishingTreeActivityData",
        }
    },
    ["table_WishingTreeConfig"] = {
        TableKey = "id",
        SubTableNames = {
			"WishingTreeConfig",
        }
    },
    ["table_WishingTreeGetRewardShowData"] = {
        TableKey = "id",
        SubTableNames = {
			"WishingTreeGetRewardShowData",
        }
    },
    ["table_WolfKillComeBack"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfKillComeBackData",
        }
    },
    ["table_WolfKillDecorationAni"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfKillDecorationAniData",
        }
    },
    ["table_WolfKillDecorationReact"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfKillDecorationReactData",
        }
    },
    ["table_WolfKillLabelsInfoData"] = {
        TableKey = "LabelsId",
        SubTableNames = {
			"WolfKillLabelsInfoData",
        }
    },
    ["table_WolfKillRoadToMaster"] = {
        TableKey = "toMasterId",
        SubTableNames = {
			"WolfKillRoadToMasterData",
        }
    },
    ["table_WolfKillRoleInfoBase"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfKillRoleInfoBaseData",
        }
    },
    ["table_WolfKillRoleInfoRate"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfKillRoleInfoRateData",
        }
    },
    ["table_WolfKillSeasonReward"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfKillSeasonRewardData",
        }
    },
    ["table_WolfKilSpecialSpeak"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfKillSpecialSpeakData",
        }
    },
    ["table_WolfTeamChestGiftConfData"] = {
        TableKey = "id",
        SubTableNames = {
			"WolfTeamChestGiftData",
        }
    },
    ["table_WorkLevelData"] = {
        TableKey = "id",
        SubTableNames = {
			"WorkLevelData",
        }
    },
    ["table_XiaowoLvConf"] = {
        TableKey = "Level",
        SubTableNames = {
			"XiaowoLvConf",
        }
    },
    ["table_XiaowoMapConf"] = {
        TableKey = "mapId",
        SubTableNames = {
			"XiaowoMapConf",
        }
    },
    ["table_XiaowoPropsConf"] = {
        TableKey = "ID",
        SubTableNames = {
			"XiaowoPropsConf",
        }
    },
    ["table_XiaowoSampleRoomObject"] = {
        TableKey = "mapID,itemID",
        SubTableNames = {
			"XiaowoSampleRoomObject",
        }
    },
    ["table_XiaowoSysConf"] = {
        TableKey = "ID",
        SubTableNames = {
			"XiaowoSysConf",
        }
    },
    ["table_Mayday_OutsideAreaConfData"] = {
        TableKey = "Id",
        SubTableNames = {
			"table_Mayday_OutsideAreaConfData",
        }
    },
    ["table_Mayday_OutsideAreaElenmentConfData"] = {
        TableKey = "Id",
        SubTableNames = {
			"table_Mayday_OutsideAreaElenmentConfData",
        }
    },
    ["table_NR3EThemeEventRefreshRulesData"] = {
        TableKey = "RuleId",
        SubTableNames = {
			"table_NR3EThemeEventRefreshRulesData",
        }
    },
    ["table_NewBattlePassEnterConf"] = {
        TableKey = "id",
        SubTableNames = {
			"table_NewBattlePassEnterConf",
        }
    },
    ["table_ServerInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"ServerInfoData",
        }
    },
    ["table_DevServerInfoData"] = {
        TableKey = "id",
        SubTableNames = {
			"DevServerInfoData",
        }
    },
    ["table_ClientTextInfo"] = {
        TableKey = "id",
        SubTableNames = {
			"TextClientExcel",
        }
    },
}

_MOE.TableHelperLoader:RegisterTableHelperTables(TableHelperTables)