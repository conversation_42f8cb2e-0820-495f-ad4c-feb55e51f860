-- This file is automatically generated

-- MsgName-MsgId
local MsgName2Id = {
	UnimplementedMsg_S2C_Msg = 107,
	Login_C2S_Msg = 108,
	Login_S2C_Msg = 109,
	DirectSceneEnter_C2S_Msg = 110,
	DirectSceneEnter_S2C_Msg = 111,
	DirectSceneExit_C2S_Msg = 112,
	DirectSceneExit_S2C_Msg = 113,
	DirectSceneReportData_C2S_Msg = 114,
	DirectSceneReportData_S2C_Msg = 115,
	DirectSceneDataSyncNtf = 116,
	DirectSceneJumpNtf = 117,
	DirectSceneDataNtf = 118,
	DirectScenePlayerHeartbeat_C2S_Msg = 119,
	DirectScenePlayerHeartbeat_S2C_Msg = 120,
	DirectSceneReconnect_C2S_Msg = 121,
	DirectSceneReconnect_S2C_Msg = 122,
	DirectSceneReportAction_C2S_Msg = 123,
	DirectSceneReportAction_S2C_Msg = 124,
	DirectScenePlayInfoNtf = 125,
	DirectSceneReportLevelData_C2S_Msg = 126,
	DirectSceneReportLevelData_S2C_Msg = 127,
	DirectSceneLevelDataNtf = 128,
	SceneEntityPrivateAttrNtf = 129,
	DirectSceneReady_C2S_Msg = 130,
	DirectSceneReady_S2C_Msg = 131,
	DirectSceneBorrowLock_C2S_Msg = 132,
	DirectSceneBorrowLock_S2C_Msg = 133,
	DirectSceneBorrowUnlock_C2S_Msg = 134,
	DirectSceneBorrowUnlock_S2C_Msg = 135,
	DirectSceneReportPlayResult_C2S_Msg = 136,
	DirectSceneReportPlayResult_S2C_Msg = 137,
	DressUpToggle_C2S_Msg = 138,
	DressUpToggle_S2C_Msg = 139,
	DressUpCheckExpire_C2S_Msg = 140,
	DressUpCheckExpire_S2C_Msg = 141,
	DressUpExpireNtf = 142,
	DressUpChangeNtf = 143,
	DressUpShowList_C2S_Msg = 144,
	DressUpShowList_S2C_Msg = 145,
	DressUpItem_C2S_Msg = 146,
	DressUpItem_S2C_Msg = 147,
	DressUpChangeRandomState_C2S_Msg = 148,
	DressUpChangeRandomState_S2C_Msg = 149,
	DressUpActive_C2S_Msg = 150,
	DressUpActive_S2C_Msg = 151,
	StageAchievementRewardNtf = 152,
	GetStageAchievementReward_C2S_Msg = 153,
	GetStageAchievementReward_S2C_Msg = 154,
	GetAchievementLabel_C2S_Msg = 155,
	GetAchievementLabel_S2C_Msg = 156,
	AcceptTask_C2S_Msg = 157,
	AcceptTask_S2C_Msg = 158,
	RewardTask_C2S_Msg = 159,
	RewardTask_S2C_Msg = 160,
	TaskRewardNtf = 161,
	CompleteGuideTask_C2S_Msg = 162,
	CompleteGuideTask_S2C_Msg = 163,
	StartDsGuide_C2S_Msg = 164,
	StartDsGuide_S2C_Msg = 165,
	EndDsGuide_C2S_Msg = 166,
	EndDsGuide_S2C_Msg = 167,
	GetTomorrowReward_C2S_Msg = 168,
	GetTomorrowReward_S2C_Msg = 169,
	RootAttrNtf = 170,
	KickPlayerNtf = 171,
	HeartBeat_C2S_Msg = 172,
	HeartBeat_S2C_Msg = 173,
	GMCommand_C2S_Msg = 174,
	GMCommand_S2C_Msg = 175,
	GMAllCmdInfoNtf = 176,
	ResFileUpdateNtf = 177,
	AttrRemoveTags_C2S_Msg = 178,
	AttrRemoveTags_S2C_Msg = 179,
	AttrAddTags_C2S_Msg = 180,
	AttrAddTags_S2C_Msg = 181,
	UploadLogNtf = 182,
	PlayerMiscNtf = 183,
	DebugMessageNtf = 184,
	ModErrorNtf = 185,
	RequestSongAchList_C2S_Msg = 186,
	RequestSongAchList_S2C_Msg = 187,
	PlayerNoticeMsgNtf = 188,
	GetOpenIdFromUid_C2S_Msg = 189,
	GetOpenIdFromUid_S2C_Msg = 190,
	UploadButtonClickFlow_C2S_Msg = 191,
	UploadButtonClickFlow_S2C_Msg = 192,
	ListFeatureOpen_C2S_Msg = 193,
	ListFeatureOpen_S2C_Msg = 194,
	FeatureOpenNtf = 195,
	PlayerAfterLoginNtf = 196,
	GetZplanCurrencyBalance_C2S_Msg = 197,
	GetZplanCurrencyBalance_S2C_Msg = 198,
	GetZplanProfileInfo_C2S_Msg = 199,
	GetZplanProfileInfo_S2C_Msg = 200,
	UploadVoiceStatusTlogFlow_C2S_Msg = 201,
	UploadVoiceStatusTlogFlow_S2C_Msg = 202,
	UpdateMidasToken_C2S_Msg = 203,
	UpdateMidasToken_S2C_Msg = 204,
	AntiAddictReport_C2S_Msg = 205,
	AntiAddictReport_S2C_Msg = 206,
	AntiAddictNtf = 207,
	GetSpecReward_C2S_Msg = 208,
	GetSpecReward_S2C_Msg = 209,
	RoomCreate_C2S_Msg = 210,
	RoomCreate_S2C_Msg = 211,
	RoomInvite_C2S_Msg = 212,
	RoomInvite_S2C_Msg = 213,
	RoomInvitationReplyCheck_C2S_Msg = 214,
	RoomInvitationReplyCheck_S2C_Msg = 215,
	RoomLeave_C2S_Msg = 216,
	RoomLeave_S2C_Msg = 217,
	RoomKick_C2S_Msg = 218,
	RoomKick_S2C_Msg = 219,
	RoomStart_C2S_Msg = 220,
	RoomStart_S2C_Msg = 221,
	RoomCancelStart_C2S_Msg = 222,
	RoomCancelStart_S2C_Msg = 223,
	RoomReadyStart_C2S_Msg = 224,
	RoomReadyStart_S2C_Msg = 225,
	MatchSuccNtf = 226,
	RoomInvitationNtf = 227,
	RoomMemberModifyNtf = 228,
	RoomModifyModMode_C2S_Msg = 229,
	RoomModifyModMode_S2C_Msg = 230,
	RoomCancelResultNtf = 231,
	BattleReConnectNtf = 232,
	RoomWantToJoin_C2S_Msg = 233,
	RoomWantToJoin_S2C_Msg = 234,
	RoomWantToJoinNtf = 235,
	RoomReplayWantToJoin_C2S_Msg = 236,
	RoomReplayWantToJoin_S2C_Msg = 237,
	RoomStartBattleNtf = 238,
	RoomListModMode_C2S_Msg = 239,
	RoomListModMode_S2C_Msg = 240,
	RoomCommonNtf = 241,
	RoomJoinFromQQ_C2S_Msg = 242,
	RoomJoinFromQQ_S2C_Msg = 243,
	RoomDirectJoinFromRecruit_C2S_Msg = 244,
	RoomDirectJoinFromRecruit_S2C_Msg = 245,
	MatchSceneCreateSuccNtf = 246,
	MatchJoinSuccNtf = 247,
	MallBuyMsg_C2S_Msg = 248,
	MallBuyMsg_S2C_Msg = 249,
	DressUpLimitMallPanelMsg_C2S_Msg = 250,
	DressUpLimitMallPanelMsg_S2C_Msg = 251,
	MallPanelMsg_C2S_Msg = 252,
	MallPanelMsg_S2C_Msg = 253,
	ClubList_C2S_Msg = 254,
	ClubList_S2C_Msg = 255,
	ClubCreate_C2S_Msg = 256,
	ClubCreate_S2C_Msg = 257,
	ClubInfo_C2S_Msg = 258,
	ClubInfo_S2C_Msg = 259,
	ClubJoinInfoNtf = 260,
	ClubJoin_C2S_Msg = 261,
	ClubJoin_S2C_Msg = 262,
	ClubModify_C2S_Msg = 263,
	ClubModify_S2C_Msg = 264,
	ClubModifyPosition_C2S_Msg = 265,
	ClubModifyPosition_S2C_Msg = 266,
	ClubApplyList_C2S_Msg = 267,
	ClubApplyList_S2C_Msg = 268,
	ClubQuit_C2S_Msg = 269,
	ClubQuit_S2C_Msg = 270,
	ClubQuitNtf = 271,
	ClubKickOut_C2S_Msg = 272,
	ClubKickOut_S2C_Msg = 273,
	ClubDissolve_C2S_Msg = 274,
	ClubDissolve_S2C_Msg = 275,
	ClubCheck_C2S_Msg = 276,
	ClubCheck_S2C_Msg = 277,
	ClubSetUp_C2S_Msg = 278,
	ClubSetUp_S2C_Msg = 279,
	ActivityRefreshNtf = 280,
	ActivityGetAllRedDotShow_C2S_Msg = 281,
	ActivityGetAllRedDotShow_S2C_Msg = 282,
	ActivityGetAllLabel_C2S_Msg = 283,
	ActivityGetAllLabel_S2C_Msg = 284,
	ActivityGetAllTaskInfo_C2S_Msg = 285,
	ActivityGetAllTaskInfo_S2C_Msg = 286,
	ActivityGetReward_C2S_Msg = 287,
	ActivityGetReward_S2C_Msg = 288,
	ActivityGetAllReward_C2S_Msg = 289,
	ActivityGetAllReward_S2C_Msg = 290,
	SignActivityNtf = 291,
	DKGiftInfoNtf = 292,
	DKGiftInfo_C2S_Msg = 293,
	DKGiftInfo_S2C_Msg = 294,
	DKBuyGift_C2S_Msg = 295,
	DKBuyGift_S2C_Msg = 296,
	MailInfosNtf = 297,
	SendMail_C2S_Msg = 298,
	SendMail_S2C_Msg = 299,
	RecvMail_C2S_Msg = 300,
	RecvMail_S2C_Msg = 301,
	ReadMail_C2S_Msg = 302,
	ReadMail_S2C_Msg = 303,
	GetAttachments_C2S_Msg = 304,
	GetAttachments_S2C_Msg = 305,
	DelMail_C2S_Msg = 306,
	DelMail_S2C_Msg = 307,
	GetVisitorSnapshots_C2S_Msg = 308,
	GetVisitorSnapshots_S2C_Msg = 309,
	ChangeBasicInfo_C2S_Msg = 310,
	ChangeBasicInfo_S2C_Msg = 311,
	ChangeFollowingStatus_C2S_Msg = 312,
	ChangeFollowingStatus_S2C_Msg = 313,
	GetFollowers_C2S_Msg = 314,
	GetFollowers_S2C_Msg = 315,
	GetFollowings_C2S_Msg = 316,
	GetFollowings_S2C_Msg = 317,
	StarPlayer_C2S_Msg = 318,
	StarPlayer_S2C_Msg = 319,
	ChangeAvatar_C2S_Msg = 320,
	ChangeAvatar_S2C_Msg = 321,
	ChangeShowAvatarType_C2S_Msg = 322,
	ChangeShowAvatarType_S2C_Msg = 323,
	DeliverResource_C2S_Msg = 324,
	DeliverResource_S2C_Msg = 325,
	GetResourceMd5_C2S_Msg = 326,
	GetResourceMd5_S2C_Msg = 327,
	ClientKVConfNtf = 328,
	SceneEnter_C2S_Msg = 329,
	SceneEnter_S2C_Msg = 330,
	SceneExit_C2S_Msg = 331,
	SceneExit_S2C_Msg = 332,
	SceneEnterNtf = 333,
	SceneExitNtf = 334,
	SceneReportData_C2S_Msg = 335,
	SceneReportData_S2C_Msg = 336,
	SceneDataSyncNtf = 337,
	SceneReportAction_C2S_Msg = 338,
	SceneReportAction_S2C_Msg = 339,
	SceneGetServer_C2S_Msg = 340,
	SceneGetServer_S2C_Msg = 341,
	SceneGetOngoingInfo_C2S_Msg = 342,
	SceneGetOngoingInfo_S2C_Msg = 343,
	SceneBaseDataNtf = 344,
	SceneReconnectNtf = 345,
	SceneInteractActionInvite_C2S_Msg = 346,
	SceneInteractActionInvite_S2C_Msg = 347,
	SceneInteractActionAccept_C2S_Msg = 348,
	SceneInteractActionAccept_S2C_Msg = 349,
	SceneInteractActionCancel_C2S_Msg = 350,
	SceneInteractActionCancel_S2C_Msg = 351,
	SceneInteractActionActive_C2S_Msg = 352,
	SceneInteractActionActive_S2C_Msg = 353,
	SceneInteractActionInviteNtf = 354,
	SceneInteractActionAcceptNtf = 355,
	SceneInteractActionActiveNtf = 356,
	SceneInteractActionCancelNtf = 357,
	SceneSettlementNtf = 358,
	SceneSettlement_C2S_Msg = 359,
	SceneSettlement_S2C_Msg = 360,
	ErrorCodeNtf = 361,
	ReportEvent_C2S_Msg = 362,
	ReportEvent_S2C_Msg = 363,
	LetsGoTest_C2S_Msg = 364,
	LetsGoTest_S2C_Msg = 365,
	LetsGoGetPlayerProfile_C2S_Msg = 366,
	LetsGoGetPlayerProfile_S2C_Msg = 367,
	LetsGoGetBattleSnapshotRecord_C2S_Msg = 368,
	LetsGoGetBattleSnapshotRecord_S2C_Msg = 369,
	LetsGoGetBattleDetailRecord_C2S_Msg = 370,
	LetsGoGetBattleDetailRecord_S2C_Msg = 371,
	LetsGoSetSettings_C2S_Msg = 372,
	LetsGoSetSettings_S2C_Msg = 373,
	LetsGoGetSettings_C2S_Msg = 374,
	LetsGoGetSettings_S2C_Msg = 375,
	RoomModModeUnlockNtf = 376,
	LetsGoBattleSettlementNtf = 377,
	LetsGoGetQualifyingReward_C2S_Msg = 378,
	LetsGoGetQualifyingReward_S2C_Msg = 379,
	LetsGoSeasonSettlementNtf = 380,
	LetsGoOpenQualifyingList_C2S_Msg = 381,
	LetsGoOpenQualifyingList_S2C_Msg = 382,
	LoginNoticeNtf = 383,
	PermitRefreshNtf_S2C_Msg = 384,
	PermitGetInfo_C2S_Msg = 385,
	PermitGetInfo_S2C_Msg = 386,
	PermitTaskComplete_C2S_Msg = 387,
	PermitTaskComplete_S2C_Msg = 388,
	PermitLevelReward_C2S_Msg = 389,
	PermitLevelReward_S2C_Msg = 390,
	PermitAllLevelReward_C2S_Msg = 391,
	PermitAllLevelReward_S2C_Msg = 392,
	PermitInfoNtf = 393,
	BattleReportModData_C2S_Msg = 394,
	BattleReportModData_S2C_Msg = 395,
	BattleModDataLogin_C2S_Msg = 396,
	BattleModDataLogin_S2C_Msg = 397,
	BattleRequestModData_C2S_Msg = 398,
	BattleRequestModData_S2C_Msg = 399,
	BattleRequestModDataNtf = 400,
	BattleSyncModDataNtf = 401,
	BattleRequestModEnd_C2S_Msg = 402,
	BattleRequestModEnd_S2C_Msg = 403,
	BattleSyncModTime_C2S_Msg = 404,
	BattleSyncModTime_S2C_Msg = 405,
	BattlePlayerReadyMod_C2S_Msg = 406,
	BattlePlayerReadyMod_S2C_Msg = 407,
	BattleSeedDateNtf = 408,
	BattleModEndNtf = 409,
	BattleModStartNtf = 410,
	BattleGiveUpMod_C2S_Msg = 411,
	BattleGiveUpMod_S2C_Msg = 412,
	BattleGetOngoingDsAddr_C2S_Msg = 413,
	BattleGetOngoingDsAddr_S2C_Msg = 414,
	BattleSongAchNtf = 415,
	BattleExitNtf = 416,
	BattleSendExpression_C2S_Msg = 417,
	BattleSendExpression_S2C_Msg = 418,
	BattleExpressionNtf = 419,
	RecvRewardListNtf = 420,
	GetRankTopNPlayer_C2S_Msg = 421,
	GetRankTopNPlayer_S2C_Msg = 422,
	GetPlayerRankInfo_C2S_Msg = 423,
	GetPlayerRankInfo_S2C_Msg = 424,
	GetFriendRankTopNPlayer_C2S_Msg = 425,
	GetFriendRankTopNPlayer_S2C_Msg = 426,
	GetSceneRankTopNPlayer_C2S_Msg = 427,
	GetSceneRankTopNPlayer_S2C_Msg = 428,
	AddFriend_C2S_Msg = 429,
	AddFriend_S2C_Msg = 430,
	RemoveFriend_C2S_Msg = 431,
	RemoveFriend_S2C_Msg = 432,
	AgreeFriendApply_C2S_Msg = 433,
	AgreeFriendApply_S2C_Msg = 434,
	DenyFriendApply_C2S_Msg = 435,
	DenyFriendApply_S2C_Msg = 436,
	GetFriendApplyList_C2S_Msg = 437,
	GetFriendApplyList_S2C_Msg = 438,
	SearchFriend_C2S_Msg = 439,
	SearchFriend_S2C_Msg = 440,
	FriendApplyNtf = 441,
	FriendAgreeNtf = 442,
	FriendDenyNtf = 443,
	RemoveRecommendFriend_C2S_Msg = 444,
	RemoveRecommendFriend_S2C_Msg = 445,
	ModFriendInfoNtf = 446,
	PlatFriendInfoNtf = 447,
	FriendRemoveNtf = 448,
	RecentPlayInfoNtf = 449,
	UpdateFriendHotData_C2S_Msg = 450,
	UpdateFriendHotData_S2C_Msg = 451,
	GiveFriendGift_C2S_Msg = 452,
	GiveFriendGift_S2C_Msg = 453,
	GetRecommendationFriends_C2S_Msg = 454,
	GetRecommendationFriends_S2C_Msg = 455,
	AddBlack_C2S_Msg = 456,
	AddBlack_S2C_Msg = 457,
	RemoveBlack_C2S_Msg = 458,
	RemoveBlack_S2C_Msg = 459,
	BlackInfoNtf = 460,
	AddCouples_C2S_Msg = 461,
	AddCouples_S2C_Msg = 462,
	RemoveCouples_C2S_Msg = 463,
	RemoveCouples_S2C_Msg = 464,
	AgreeCouplesApply_C2S_Msg = 465,
	AgreeCouplesApply_S2C_Msg = 466,
	DenyCouplesApply_C2S_Msg = 467,
	DenyCouplesApply_S2C_Msg = 468,
	CouplesApplyNtf = 469,
	CouplesAgreeNtf = 470,
	CouplesDenyNtf = 471,
	UpdateSceneNeighborHotData_C2S_Msg = 472,
	UpdateSceneNeighborHotData_S2C_Msg = 473,
	SceneNeighborInfoNtf = 474,
	QQInvity_C2S_Msg = 475,
	QQInvity_S2C_Msg = 476,
	AddFriendNoticeServer_C2S_Msg = 477,
	AddFriendNoticeServer_S2C_Msg = 478,
	ChatSend_C2S_Msg = 479,
	ChatSend_S2C_Msg = 480,
	ChatOnInput_C2S_Msg = 481,
	ChatOnInput_S2C_Msg = 482,
	ChatGroupOperationNtf = 483,
	GetTailMessages_C2S_Msg = 484,
	GetTailMessages_S2C_Msg = 485,
	GetPreMessages_C2S_Msg = 486,
	GetPreMessages_S2C_Msg = 487,
	ChatPull_C2S_Msg = 488,
	ChatPull_S2C_Msg = 489,
	ChatMsgNtf = 490,
	ChatMsgRedDotNtf = 491,
	ChatClearNotRead_C2S_Msg = 492,
	ChatClearNotRead_S2C_Msg = 493,
	ChatRemoveRecord_C2S_Msg = 494,
	ChatRemoveRecord_S2C_Msg = 495,
	GroupChatMemModifyNtf = 496,
	ChatCreateGroup_C2S_Msg = 497,
	ChatCreateGroup_S2C_Msg = 498,
	ChatQuit_C2S_Msg = 499,
	ChatQuit_S2C_Msg = 500,
	ChatGetQuickTextList_C2S_Msg = 501,
	ChatGetQuickTextList_S2C_Msg = 502,
	ChatReport_C2S_Msg = 503,
	ChatReport_S2C_Msg = 504,
	ChatRoomRecruit_C2S_Msg = 505,
	ChatRoomRecruit_S2C_Msg = 506,
	ChatChannelSwitch_C2S_Msg = 507,
	ChatChannelSwitch_S2C_Msg = 508,
	BagUseItemMsg_C2S_Msg = 509,
	BagUseItemMsg_S2C_Msg = 510,
	BagSellItemMsg_C2S_Msg = 511,
	BagSellItemMsg_S2C_Msg = 512,
	BagMoveItemMsg_C2S_Msg = 513,
	BagMoveItemMsg_S2C_Msg = 514,
	BagDestroyItemMsg_C2S_Msg = 515,
	BagDestroyItemMsg_S2C_Msg = 516,
	CheckEquipItemsExpireMsg_C2S_Msg = 517,
	CheckEquipItemsExpireMsg_S2C_Msg = 518,
	BagRewardNtfFinishMsg_C2S_Msg = 519,
	BagRewardNtfFinishMsg_S2C_Msg = 520,
	BagItemNumUpToMaxNtf = 521,
	BagCommonGetItemsNtf = 522,
	BagItemNumUpToMaxReplaceNtf = 523,
	PvEGetEntryList_C2S_Msg = 524,
	PvEGetEntryList_S2C_Msg = 525,
	PvEGetEntryInfo_C2S_Msg = 526,
	PvEGetEntryInfo_S2C_Msg = 527,
	PvEGetChapterInfo_C2S_Msg = 528,
	PvEGetChapterInfo_S2C_Msg = 529,
	PvEGetStageInfo_C2S_Msg = 530,
	PvEGetStageInfo_S2C_Msg = 531,
	PvEClearRedDot_C2S_Msg = 532,
	PvEClearRedDot_S2C_Msg = 533,
	PvEGetReward_C2S_Msg = 534,
	PvEGetReward_S2C_Msg = 535,
	PvEGetEntryProcessReward_C2S_Msg = 536,
	PvEGetEntryProcessReward_S2C_Msg = 537,
	PvEGetChapterRowColumnReward_C2S_Msg = 538,
	PvEGetChapterRowColumnReward_S2C_Msg = 539,
	PvEStartStage_C2S_Msg = 540,
	PvEStartStage_S2C_Msg = 541,
	DeliverResources_C2S_Msg = 542,
	DeliverResources_S2C_Msg = 543,
	DirGetPlayerInfo_C2S_Msg = 544,
	DirGetPlayerInfo_S2C_Msg = 545,
	DevEnvResInfoNtf = 546,
	ExpAndLevelChangeNtf = 547,
	TeamRecruitPublish_C2S_Msg = 548,
	TeamRecruitPublish_S2C_Msg = 549,
	TeamRecruitQuery_C2S_Msg = 550,
	TeamRecruitQuery_S2C_Msg = 551,
	TeamRecruitJoin_C2S_Msg = 552,
	TeamRecruitJoin_S2C_Msg = 553,
	ClientSetCache_C2S_Msg = 554,
	ClientSetCache_S2C_Msg = 555,
	ClientDelCache_C2S_Msg = 556,
	ClientDelCache_S2C_Msg = 557,
	GetTask_C2S_Msg = 558,
	GetTask_S2C_Msg = 559,
	TaskChangeNtf = 560,
	ChangePlayerRankGeoInfo_C2S_Msg = 561,
	ChangePlayerRankGeoInfo_S2C_Msg = 562,
	GetGeoRankTopNPlayer_C2S_Msg = 563,
	GetGeoRankTopNPlayer_S2C_Msg = 564,
	AddRelation_C2S_Msg = 565,
	AddRelation_S2C_Msg = 566,
	RemoveRelation_C2S_Msg = 567,
	RemoveRelation_S2C_Msg = 568,
	AgreeRelation_C2S_Msg = 569,
	AgreeRelation_S2C_Msg = 570,
	DenyRelation_C2S_Msg = 571,
	DenyRelation_S2C_Msg = 572,
	LobbyGetDsServer_C2S_Msg = 573,
	LobbyGetDsServer_S2C_Msg = 574,
	LobbyJumpNtf = 575,
	SearchPlayer_C2S_Msg = 576,
	SearchPlayer_S2C_Msg = 577,
	SeasonInfoNtf = 578,
	RefreshSeasonPanel_C2S_Msg = 579,
	RefreshSeasonPanel_S2C_Msg = 580,
	GetPlayerColdData_C2S_Msg = 581,
	GetPlayerColdData_S2C_Msg = 582,
	PermitUnlock_C2S_Msg = 583,
	PermitUnlock_S2C_Msg = 584,
	PlayModeUnlockNtf = 585,
	RoomStartNtf = 586,
	RoomConfirmStart_C2S_Msg = 587,
	RoomStartMatchCancelNtf = 588,
	RoomConfirmStart_S2C_Msg = 589,
	RoomLeaderTransit_C2S_Msg = 590,
	RoomLeaderTransit_S2C_Msg = 591,
	RoomDisband_C2S_Msg = 592,
	RoomDisband_S2C_Msg = 593,
	RoomLobbyGather_C2S_Msg = 594,
	RoomLobbyGather_S2C_Msg = 595,
	RoomLobbyGatherNtf = 596,
	RoomLobbyTransit_C2S_Msg = 597,
	RoomLobbyTransit_S2C_Msg = 598,
	DeletePlayerRankGeoInfo_C2S_Msg = 599,
	DeletePlayerRankGeoInfo_S2C_Msg = 600,
	PlayerCoinChangeNtf = 601,
	PlayerItemChangeNtf = 602,
	RelationApplyAddNtf = 603,
	RelationApplyRemoveNtf = 604,
	RelationAddNtf = 605,
	RelationRemoveNtf = 606,
	TeamRecruitQuickJoin_C2S_Msg = 607,
	TeamRecruitQuickJoin_S2C_Msg = 608,
	PermitBuyLevel_C2S_Msg = 609,
	PermitBuyLevel_S2C_Msg = 610,
	PermitUnlockLevel_C2S_Msg = 611,
	UnLockMatchTypeNtf = 612,
	SendGoldCoin_C2S_Msg = 613,
	SendGoldCoin_S2C_Msg = 614,
	UgcList_C2S_Msg = 615,
	UgcList_S2C_Msg = 616,
	UgcCreate_C2S_Msg = 617,
	UgcCreate_S2C_Msg = 618,
	UgcDelete_C2S_Msg = 619,
	UgcDelete_S2C_Msg = 620,
	SaveWork_C2S_Msg = 621,
	SaveWork_S2C_Msg = 622,
	GetSaveWork_C2S_Msg = 623,
	GetSaveWork_S2C_Msg = 624,
	DrawRaffle_C2S_Msg = 625,
	DrawRaffle_S2C_Msg = 626,
	PlatShareActiveList_C2S_Msg = 627,
	PlatShareActiveList_S2C_Msg = 628,
	PlatShareActiveSend_C2S_Msg = 629,
	PlatShareActiveSend_S2C_Msg = 630,
	PlatShareActiveReward_C2S_Msg = 631,
	PlatShareActiveReward_S2C_Msg = 632,
	ActivityListAll_C2S_Msg = 633,
	ActivityListAll_S2C_Msg = 634,
	ActivityReadDotNtf = 635,
	ActivityClickRedDot_C2S_Msg = 636,
	ActivityClickRedDot_S2C_Msg = 637,
	ListLuckyMoney_C2S_Msg = 638,
	ListLuckyMoney_S2C_Msg = 639,
	ShareLuckyMoney_C2S_Msg = 640,
	ShareLuckyMoney_S2C_Msg = 641,
	LoginFromBacklinksParams_C2S_Msg = 642,
	LoginFromBacklinksParams_S2C_Msg = 643,
	GetLuckyMoneyReward_C2S_Msg = 644,
	GetLuckyMoneyReward_S2C_Msg = 645,
	GetLuckyMoneyDetailInfo_C2S_Msg = 646,
	GetLuckyMoneyDetailInfo_S2C_Msg = 647,
	GetRechargeConf_C2S_Msg = 648,
	GetRechargeConf_S2C_Msg = 649,
	RechargeConfNtf = 650,
	GetPlayerColdDataByOpenId_C2S_Msg = 651,
	GetPlayerColdDataByOpenId_S2C_Msg = 652,
	GetUidFromOpenId_C2S_Msg = 653,
	GetUidFromOpenId_S2C_Msg = 654,
	MallGetShopCommodity_C2S_Msg = 655,
	MallGetShopCommodity_S2C_Msg = 656,
	DreamNewStarTaskList_C2S_Msg = 657,
	DreamNewStarTaskList_S2C_Msg = 658,
	FittingSlotSave_C2S_Msg = 659,
	FittingSlotSave_S2C_Msg = 660,
	FittingSlotSelect_C2S_Msg = 661,
	FittingSlotSelect_S2C_Msg = 662,
	BagDressUpItem_C2S_Msg = 663,
	BagDressUpItem_S2C_Msg = 664,
	MallBatchGetCommodity_C2S_Msg = 665,
	MallBatchGetCommodity_S2C_Msg = 666,
	Recharge_C2S_Msg = 667,
	Recharge_S2C_Msg = 668,
	UgcCopy_C2S_Msg = 669,
	UgcCopy_S2C_Msg = 670,
	UgcMapModify_C2S_Msg = 671,
	UgcMapModify_S2C_Msg = 672,
	UgcRegain_C2S_Msg = 673,
	UgcRegain_S2C_Msg = 674,
	UgcPublish_C2S_Msg = 675,
	UgcPublish_S2C_Msg = 676,
	UgcTakeOff_C2S_Msg = 677,
	UgcTakeOff_S2C_Msg = 678,
	GetRechargeLevelReward_C2S_Msg = 679,
	GetRechargeLevelReward_S2C_Msg = 680,
	WealthBankCheckIn_C2S_Msg = 681,
	WealthBankCheckIn_S2C_Msg = 682,
	WealthBankGetInfo_C2S_Msg = 683,
	WealthBankGetInfo_S2C_Msg = 684,
	WealthBankReceiveDeposit_C2S_Msg = 685,
	WealthBankReceiveDeposit_S2C_Msg = 686,
	SquadGetInfo_C2S_Msg = 687,
	SquadGetInfo_S2C_Msg = 688,
	SquadJoin_C2S_Msg = 689,
	SquadJoin_S2C_Msg = 690,
	SquadTaskInfo_C2S_Msg = 691,
	SquadTaskInfo_S2C_Msg = 692,
	RechargeLvChangeNtf = 693,
	UgcApplyKeyInfo_C2S_Msg = 694,
	UgcMapSet_C2S_Msg = 695,
	UgcMapSet_S2C_Msg = 696,
	MidasBuyGoodsNtf = 699,
	RechargeDepositPurchase_C2S_Msg = 700,
	RechargeDepositPurchase_S2C_Msg = 701,
	RechargeDepositPurchaseNtf = 702,
	RechargeDepositGetReward_C2S_Msg = 703,
	RechargeDepositGetReward_S2C_Msg = 704,
	RechargeDepositFetchPrice_C2S_Msg = 705,
	RechargeDepositFetchPrice_S2C_Msg = 706,
	RechargeDepositConfigNtf = 707,
	BagCheckItemExpire_C2S_Msg = 708,
	BagCheckItemExpire_S2C_Msg = 709,
	FirstChargeTaskInfo_C2S_Msg = 710,
	FirstChargeTaskInfo_S2C_Msg = 711,
	UgcApplyKeyInfo_S2C_Msg = 712,
	UgcPublishList_C2S_Msg = 713,
	UgcPublishList_S2C_Msg = 714,
	GaveLike_C2S_Msg = 715,
	GaveLike_S2C_Msg = 716,
	Collect_C2S_Msg = 717,
	Collect_S2C_Msg = 718,
	Subscribe_C2S_Msg = 719,
	Subscribe_S2C_Msg = 720,
	MallBatchBuyMsg_C2S_Msg = 721,
	MallBatchBuyMsg_S2C_Msg = 722,
	MallDirectBuyNtf = 723,
	MallRealTimeNtf = 724,
	GetMessageSlipList_C2S_Msg = 725,
	GetMessageSlipList_S2C_Msg = 726,
	AddMessageSlip_C2S_Msg = 727,
	AddMessageSlip_S2C_Msg = 728,
	DeleteMessageSlip_C2S_Msg = 729,
	DeleteMessageSlip_S2C_Msg = 730,
	GetMessageSlipDetail_C2S_Msg = 731,
	GetMessageSlipDetail_S2C_Msg = 732,
	GetMessageCommentList_C2S_Msg = 733,
	GetMessageCommentList_S2C_Msg = 734,
	AddMessageComment_C2S_Msg = 735,
	AddMessageComment_S2C_Msg = 736,
	DeleteMessageComment_C2S_Msg = 737,
	DeleteMessageComment_S2C_Msg = 738,
	GetMessageFavourList_C2S_Msg = 739,
	GetMessageFavourList_S2C_Msg = 740,
	AddMessageFavour_C2S_Msg = 741,
	AddMessageFavour_S2C_Msg = 742,
	DeleteMessageFavour_C2S_Msg = 743,
	DeleteMessageFavour_S2C_Msg = 744,
	MessageSlipRedDotNtf = 745,
	MidasPayResult_C2S_Msg = 746,
	MidasPayResult_S2C_Msg = 747,
	UgcOperate_C2S_Msg = 748,
	UgcOperate_S2C_Msg = 749,
	UgcOperateCancel_C2S_Msg = 750,
	UgcOperateCancel_S2C_Msg = 751,
	ClickMessageSlipRedDot_C2S_Msg = 752,
	ClickMessageSlipRedDot_S2C_Msg = 753,
	MatchProcessNtf = 754,
	UgcMapScreen_C2S_Msg = 755,
	UgcMapScreen_S2C_Msg = 756,
	UgcMapType_C2S_Msg = 757,
	UgcMapType_S2C_Msg = 758,
	UgcMapSearch_C2S_Msg = 759,
	UgcMapSearch_S2C_Msg = 760,
	UgcMapPublishModify_C2S_Msg = 761,
	UgcMapPublishModify_S2C_Msg = 762,
	GetMailBriefs_C2S_Msg = 763,
	GetMailBriefs_S2C_Msg = 764,
	MailBriefsNtf = 766,
	MailStateNtf = 767,
	ChangeName_C2S_Msg = 768,
	ChangeName_S2C_Msg = 769,
	FittingSingleItem_C2S_Msg = 770,
	FittingSingleItem_S2C_Msg = 771,
	MailDelNtf = 772,
	MallCommoditySetRedPointShow_C2S_Msg = 773,
	MallCommoditySetRedPointShow_S2C_Msg = 774,
	UgcOpSubscribe_C2S_Msg = 775,
	UgcOpSubscribe_S2C_Msg = 776,
	UgcMySubscribe_C2S_Msg = 777,
	UgcMySubscribe_S2C_Msg = 778,
	UgcMyFans_C2S_Msg = 779,
	UgcMyFans_S2C_Msg = 780,
	UgcPublishDetails_C2S_Msg = 781,
	UgcPublishDetails_S2C_Msg = 782,
	RechargeMonthCard_C2S_Msg = 783,
	RechargeMonthCard_S2C_Msg = 784,
	ReceiveMonthCardDailyItem_C2S_Msg = 787,
	ReceiveMonthCardDailyItem_S2C_Msg = 788,
	MonthCardDeliverGoodsNtf = 789,
	UgcObjectOperate_C2S_Msg = 790,
	UgcObjectOperate_S2C_Msg = 791,
	UgcListForObject_C2S_Msg = 792,
	UgcListForObject_S2C_Msg = 793,
	UgcPublishRegain_C2S_Msg = 794,
	UgcPublishRegain_S2C_Msg = 795,
	UgcOpSubTop_C2S_Msg = 796,
	UgcOpSubTop_S2C_Msg = 797,
	CheckInPlanGetMakeUpTicket_C2S_Msg = 798,
	CheckInPlanGetMakeUpTicket_S2C_Msg = 799,
	CheckInPlanGetInfo_C2S_Msg = 800,
	CheckInPlanGetInfo_S2C_Msg = 801,
	CheckInPlanCheckIn_C2S_Msg = 802,
	CheckInPlanCheckIn_S2C_Msg = 803,
	CheckInPlanDraw_C2S_Msg = 804,
	CheckInPlanDraw_S2C_Msg = 805,
	UgcCreateGroupObject_C2S_Msg = 806,
	UgcCreateGroupObject_S2C_Msg = 807,
	UgcSinglePassLevel_C2S_Msg = 808,
	UgcSinglePassLevel_S2C_Msg = 809,
	UgcCreateRoom_C2S_Msg = 810,
	UgcCreateRoom_S2C_Msg = 811,
	UgcRoomList_C2S_Msg = 812,
	UgcRoomList_S2C_Msg = 813,
	UgcJoinRoom_C2S_Msg = 814,
	UgcJoinRoom_S2C_Msg = 815,
	UgcExitRoom_C2S_Msg = 816,
	UgcExitRoom_S2C_Msg = 817,
	PlayerGetRecordItemForLevel_C2S_Msg = 818,
	PlayerGetRecordItemForLevel_S2C_Msg = 819,
	PlatCommon_C2S_Msg = 820,
	PlatCommon_S2C_Msg = 821,
	UgcAccept_C2S_Msg = 822,
	UgcAccept_S2C_Msg = 823,
	BatchGetPlayerPublicInfo_C2S_Msg = 824,
	BatchGetPlayerPublicInfo_S2C_Msg = 825,
	DirCheckPlayerNickName_C2S_Msg = 826,
	DirCheckPlayerNickName_S2C_Msg = 827,
	LobbyEnterPlayerDs_C2S_Msg = 828,
	LobbyEnterPlayerDs_S2C_Msg = 829,
	RelationStateNtf = 830,
	FetchTopRank_C2S_Msg = 831,
	FetchTopRank_S2C_Msg = 832,
	UgcCommonSave_C2S_Msg = 833,
	UgcCommonSave_S2C_Msg = 834,
	UgcGetCommonSave_C2S_Msg = 835,
	UgcGetCommonSave_S2C_Msg = 836,
	LobbyRandomEnter_C2S_Msg = 837,
	LobbyRandomEnter_S2C_Msg = 838,
	ChangeLabelInfo_C2S_Msg = 839,
	ChangeLabelInfo_S2C_Msg = 840,
	ChangeStateInfo_C2S_Msg = 841,
	ChangeStateInfo_S2C_Msg = 842,
	getUgcAccept_C2S_Msg = 843,
	getUgcAccept_S2C_Msg = 844,
	GetUgcCoCreateInviteCode_C2S_Msg = 845,
	GetUgcCoCreateInviteCode_S2C_Msg = 846,
	UgcCoCreateEditor_C2S_Msg = 847,
	UgcCoCreateEditor_S2C_Msg = 848,
	UgcCoCreateExitEditor_C2S_Msg = 849,
	UgcCoCreateExitEditor_S2C_Msg = 850,
	LobbyExit_C2S_Msg = 851,
	LobbyExit_S2C_Msg = 852,
	QAInvestStatusNtf = 853,
	QAInvestConfigListNtf = 854,
	UgcModifyName_C2S_Msg = 855,
	UgcModifyName_S2C_Msg = 856,
	UploadGuideStep_C2S_Msg = 857,
	UploadGuideStep_S2C_Msg = 858,
	GetUgcAccept_S2C_Msg = 859,
	GetUgcAccept_C2S_Msg = 860,
	SensitiveFilter_C2S_Msg = 861,
	SensitiveFilter_S2C_Msg = 862,
	PermitUnlockLevel_S2C_Msg = 863,
	GetRecommendFriend_C2S_Msg = 864,
	GetRecommendFriend_S2C_Msg = 865,
	UgcGetUgcPlayerInfo_C2S_Msg = 866,
	UgcGetUgcPlayerInfo_S2C_Msg = 867,
	UgcGetMaps_C2S_Msg = 868,
	UgcGetMaps_S2C_Msg = 869,
	PlayerBanInfoNtf = 870,
	FollowPlayer_C2S_Msg = 871,
	FollowPlayer_S2C_Msg = 872,
	CancelFollowPlayer_C2S_Msg = 873,
	CancelFollowPlayer_S2C_Msg = 874,
	ResourceChangeNtf = 875,
	UgcRoomPositionUpdate_C2S_Msg = 876,
	UgcRoomPositionUpdate_S2C_Msg = 877,
	UgcSingleStageStart_C2S_Msg = 878,
	UgcSingleStageStart_S2C_Msg = 879,
	UgcDailyStageChangeMap_C2S_Msg = 880,
	UgcDailyStageChangeMap_S2C_Msg = 881,
	UgcDailyStageReset_C2S_Msg = 882,
	UgcDailyStageReset_S2C_Msg = 883,
	GetUgcDailyStageInfo_C2S_Msg = 884,
	GetUgcDailyStageInfo_S2C_Msg = 885,
	UgcDelLifeItem_C2S_Msg = 886,
	UgcDelLifeItem_S2C_Msg = 887,
	UgcRoomPreStart_C2S_Msg = 888,
	UgcRoomPreStart_S2C_Msg = 889,
	SelectStarterItems_C2S_Msg = 890,
	SelectStarterItems_S2C_Msg = 891,
	UgcDeleteGroup_C2S_Msg = 892,
	UgcDeleteGroup_S2C_Msg = 893,
	UgcPublishGroup_C2S_Msg = 894,
	UgcPublishGroup_S2C_Msg = 895,
	UgcScreenForObject_C2S_Msg = 896,
	UgcScreenForObject_S2C_Msg = 897,
	UgcTakeOffForObject_C2S_Msg = 898,
	UgcTakeOffForObject_S2C_Msg = 899,
	UgcPublishDetailsForObject_C2S_Msg = 900,
	UgcPublishDetailsForObject_S2C_Msg = 901,
	CheckPlayerNickName_C2S_Msg = 902,
	CheckPlayerNickName_S2C_Msg = 903,
	UgcQuickJoin_C2S_Msg = 904,
	UgcQuickJoin_S2C_Msg = 905,
	PlayerProfileChangeNtf = 906,
	UgcCollectForObject_C2S_Msg = 907,
	UgcCollectForObject_S2C_Msg = 908,
	UgcCheckWhitelistForObject_C2S_Msg = 909,
	UgcCheckWhitelistForObject_S2C_Msg = 910,
	CreateRole_C2S_Msg = 911,
	CreateRole_S2C_Msg = 912,
	LobbyInvite_C2S_Msg = 913,
	LobbyInvite_S2C_Msg = 914,
	LobbyReplyInvite_C2S_Msg = 915,
	LobbyReplyInvite_S2C_Msg = 916,
	LobbyInviteNtf = 917,
	LobbyInviteReplyNtf = 918,
	UgcCoverCheck_C2S_Msg = 919,
	UgcCoverCheck_S2C_Msg = 920,
	UgcMapInfoNtf = 921,
	LetsGoClientUploadSettings_C2S_Msg = 922,
	LetsGoClientUploadSettings_S2C_Msg = 923,
	PlayerPray_C2S_Msg = 924,
	PlayerPray_S2C_Msg = 925,
	ChatGroupKeyGet_C2S_Msg = 926,
	ChatGroupKeyGet_S2C_Msg = 927,
	GetForwardMessages_C2S_Msg = 928,
	GetForwardMessages_S2C_Msg = 929,
	ChangeRelation_C2S_Msg = 930,
	ChangeRelation_S2C_Msg = 931,
	RewardSnsShare_C2S_Msg = 932,
	RewardSnsShare_S2C_Msg = 933,
	UgcGroupOfficalList_C2S_Msg = 934,
	UgcGroupOfficalList_S2C_Msg = 935,
	UgcGroupSearch_C2S_Msg = 936,
	UgcGroupSearch_S2C_Msg = 937,
	UgcStageChange_C2S_Msg = 938,
	UgcStageChange_S2C_Msg = 939,
	NetworkInfoUpload_C2S_Msg = 940,
	NetworkInfoUpload_S2C_Msg = 941,
	ChangeGender_C2S_Msg = 942,
	ChangeGender_S2C_Msg = 943,
	GetUnlockedSlotId_C2S_Msg = 944,
	GetUnlockedSlotId_S2C_Msg = 945,
	AccountUnBind_C2S_Msg = 946,
	AccountUnBind_S2C_Msg = 947,
	GetAccountBindInfo_C2S_Msg = 948,
	GetAccountBindInfo_S2C_Msg = 949,
	RoomRecommendList_C2S_Msg = 950,
	RoomRecommendList_S2C_Msg = 951,
	RoomQuickJoin_C2S_Msg = 952,
	RoomQuickJoin_S2C_Msg = 953,
	RoomInfoQueryByRoomNo_C2S_Msg = 954,
	RoomInfoQueryByRoomNo_S2C_Msg = 955,
	RoomJoin_C2S_Msg = 956,
	RoomPositionExchange_C2S_Msg = 957,
	RoomPositionExchange_S2C_Msg = 958,
	RoomPositionExchangeNtf = 959,
	RoomPositionExchangeReply_C2S_Msg = 960,
	ABTestInfoQuery_C2S_Msg = 961,
	ABTestInfoQuery_S2C_Msg = 962,
	GetItemPackagePickPickedNum_C2S_Msg = 963,
	GetItemPackagePickPickedNum_S2C_Msg = 964,
	IDCPingSvrListNtf = 965,
	RelationChangeNtf = 966,
	RoomInfoChange_C2S_Msg = 967,
	RoomInfoChange_S2C_Msg = 968,
	RoomReady_C2S_Msg = 969,
	RoomReady_S2C_Msg = 970,
	RoomStateCheck_C2S_Msg = 971,
	RoomStateCheck_S2C_Msg = 972,
	RelationIntimacyNtf = 973,
	UgcDownload_C2S_Msg = 974,
	UgcDownload_S2C_Msg = 975,
	RoomMapDownloadStat_C2S_Msg = 976,
	RoomMapDownloadStat_S2C_Msg = 977,
	RoomMapDownloadStatNtf = 978,
	RoomPositionExchangeReply_S2C_Msg = 979,
	GetAccountState_C2S_Msg = 980,
	GetAccountState_S2C_Msg = 981,
	RoomAddRobot_C2S_Msg = 982,
	RoomAddRobot_S2C_Msg = 983,
	RoomRemoveRobot_C2S_Msg = 984,
	RoomRemoveRobot_S2C_Msg = 985,
	RoomJoin_S2C_Msg = 986,
	CheckRandomNicknames_C2S_Msg = 987,
	CheckRandomNicknames_S2C_Msg = 988,
	ListFeatureUnlock_C2S_Msg = 989,
	ListFeatureUnlock_S2C_Msg = 990,
	FeatureUnlockNtf = 991,
	GetRandomNickname_C2S_Msg = 992,
	GetRandomNickname_S2C_Msg = 993,
	UgcGenAiImage_C2S_Msg = 994,
	UgcGenAiImage_S2C_Msg = 995,
	UgcAiImageGenSucNtf = 996,
	UgcSaveResultNtf = 997,
	UgcCoverCheckResultNtf = 998,
	UgcAiChangeColor_C2S_Msg = 999,
	UgcAiChangeColor_S2C_Msg = 1000,
	UgcAiChangeColorResultNtf = 1001,
	UgcDownLoadPublish_C2S_Msg = 1002,
	UgcDownLoadPublish_S2C_Msg = 1003,
	UgcModifyPublishMeta_C2S_Msg = 1004,
	UgcModifyPublishMeta_S2C_Msg = 1005,
	UgcUpdatePublishMeta_C2S_Msg = 1006,
	UgcUpdatePublishMeta_S2C_Msg = 1007,
	UgcPublishMetaRecord_C2S_Msg = 1008,
	UgcPublishMetaRecord_S2C_Msg = 1009,
	UgcRoomChangeMap_C2S_Msg = 1010,
	UgcRoomChangeMap_S2C_Msg = 1011,
	BulletinPublish_C2S_Msg = 1012,
	BulletinPublish_S2C_Msg = 1013,
	BulletinOpen_C2S_Msg = 1014,
	BulletinOpen_S2C_Msg = 1015,
	GetBulletinList_C2S_Msg = 1016,
	GetBulletinList_S2C_Msg = 1017,
	PartyInfoNtf = 1018,
	XiaoWoWatering_C2S_Msg = 1019,
	XiaoWoWatering_S2C_Msg = 1020,
	XiaoWoShake_C2S_Msg = 1021,
	XiaoWoShake_S2C_Msg = 1022,
	XiaoWoEnter_C2S_Msg = 1023,
	XiaoWoEnter_S2C_Msg = 1024,
	XiaoWoExit_C2S_Msg = 1025,
	XiaoWoExit_S2C_Msg = 1026,
	XiaoWoLike_C2S_Msg = 1027,
	XiaoWoLike_S2C_Msg = 1028,
	XiaoWoStar_C2S_Msg = 1029,
	XiaoWoStar_S2C_Msg = 1030,
	XiaoWoLevelUp_C2S_Msg = 1031,
	XiaoWoLevelUp_S2C_Msg = 1032,
	XiaoWoInfoNtf = 1033,
	XiaoWoItemInteract_C2S_Msg = 1034,
	XiaoWoItemInteract_S2C_Msg = 1035,
	BagCommonGetItemsNtfCommit_C2S_Msg = 1036,
	BagCommonGetItemsNtfCommit_S2C_Msg = 1037,
	GetUgcStarWorldInfo_C2S_Msg = 1038,
	GetUgcStarWorldInfo_S2C_Msg = 1039,
	AvailablePlatNicknameNtf = 1040,
	PlayerBlessBagInfoNtf = 1041,
	PlatPrivilegesNtf = 1042,
	InterServerPuzzleGetInfo_C2S_Msg = 1043,
	InterServerPuzzleGetInfo_S2C_Msg = 1044,
	InterServerPuzzleBuy_C2S_Msg = 1045,
	InterServerPuzzleBuy_S2C_Msg = 1046,
	InterServerPuzzleUnlockNtf = 1047,
	InterServerPuzzleGetReward_C2S_Msg = 1048,
	InterServerPuzzleGetReward_S2C_Msg = 1049,
	InterServerGiftGetInfo_C2S_Msg = 1050,
	InterServerGiftGetInfo_S2C_Msg = 1051,
	InterServerGiftBuy_C2S_Msg = 1052,
	InterServerGiftBuy_S2C_Msg = 1053,
	InterServerGiftUnlockNtf = 1054,
	InterServerGiftGetReward_C2S_Msg = 1055,
	InterServerGiftGetReward_S2C_Msg = 1056,
	UgcGetCreatorInfo_C2S_Msg = 1057,
	UgcGetCreatorInfo_S2C_Msg = 1058,
	GetRaffleAward_C2S_Msg = 1059,
	GetRaffleAward_S2C_Msg = 1060,
	RefreshPlayerPayInfo_S2C_Msg = 1061,
	RefreshPlayerPayInfoNtf = 1062,
	RoomMapDownloadReminder_C2S_Msg = 1063,
	RoomMapDownloadReminder_S2C_Msg = 1064,
	RoomMapDownloadReminderNtf = 1065,
	UgcRecommendSubscribe_C2S_Msg = 1066,
	UgcRecommendSubscribe_S2C_Msg = 1067,
	UgcRoomRecommendList_C2S_Msg = 1068,
	UgcRoomRecommendList_S2C_Msg = 1069,
	XiaoWoItemLock_C2S_Msg = 1070,
	XiaoWoItemLock_S2C_Msg = 1071,
	XiaoWoDSInfoNtf = 1072,
	GetChatGroupMemberList_C2S_Msg = 1073,
	GetChatGroupMemberList_S2C_Msg = 1074,
	UpdateMailState_C2S_Msg = 1075,
	UpdateMailState_S2C_Msg = 1076,
	GetPingSvrInfo_C2S_Msg = 1077,
	GetPingSvrInfo_S2C_Msg = 1078,
	GetTranslateInfo_C2S_Msg = 1079,
	GetTranslateInfo_S2C_Msg = 1080,
	WithdrawParentAuth_C2S_Msg = 1081,
	WithdrawParentAuth_S2C_Msg = 1082,
	WithdrawParentAuthNtf = 1083,
	QueryParentAuthStatus_C2S_Msg = 1084,
	QueryParentAuthStatus_S2C_Msg = 1085,
	QueryParentAuthStatusNtf = 1086,
	RefreshPlayerPayInfo_C2S_Msg = 1087,
	XiaoWoPlatDataNtf = 1088,
	XiaoWoHotReport_C2S_Msg = 1089,
	XiaoWoHotReport_S2C_Msg = 1090,
	XiaoWoPickupDrop_C2S_Msg = 1091,
	XiaoWoPickupDrop_S2C_Msg = 1092,
	XiaoWoBuyFurniture_C2S_Msg = 1093,
	XiaoWoBuyFurniture_S2C_Msg = 1094,
	XiaoWoExchangeCoin_C2S_Msg = 1095,
	XiaoWoExchangeCoin_S2C_Msg = 1096,
	XiaoWoKickNtf = 1097,
	XiaoWoSetClientKV_C2S_Msg = 1098,
	XiaoWoSetClientKV_S2C_Msg = 1099,
	SingleBattleKeepAlive_C2S_Msg = 1100,
	SingleBattleKeepAlive_S2C_Msg = 1101,
	PilotInfoNtf = 1102,
	SceneGiftPackagePushNtf = 1103,
	DigTreasureOfMultiPlayerSquad_C2S_Msg = 1104,
	DigTreasureOfMultiPlayerSquad_S2C_Msg = 1105,
	SquadMultiPlayerGetInfo_C2S_Msg = 1106,
	SquadMultiPlayerGetInfo_S2C_Msg = 1107,
	SquadMultiPlayerJoin_C2S_Msg = 1108,
	SquadMultiPlayerJoin_S2C_Msg = 1109,
	UgcCoCreateExitEditorNtf = 1110,
	ApplyUgcLayerId_C2S_Msg = 1111,
	ApplyUgcLayerId_S2C_Msg = 1112,
	AccreditLayerByCreatorId_C2S_Msg = 1113,
	AccreditLayerByCreatorId_S2C_Msg = 1114,
	UgcCoCreateEditorHeart_C2S_Msg = 1115,
	UgcCoCreateEditorHeart_S2C_Msg = 1116,
	UgcInviteCoCreateMap_C2S_Msg = 1117,
	UgcInviteCoCreateMap_S2C_Msg = 1118,
	UgcCoCreateInviteNtf = 1119,
	UgcCoCreateInviteReply_C2S_Msg = 1120,
	UgcCoCreateInviteReply_S2C_Msg = 1121,
	UgcRemoveCoCreator_C2S_Msg = 1122,
	UgcRemoveCoCreator_S2C_Msg = 1123,
	UgcQuitCoCreateMap_C2S_Msg = 1124,
	UgcQuitCoCreateMap_S2C_Msg = 1125,
	UgcCoCreatorModifyNtf = 1126,
	UgcGroupBatchGetPublish_C2S_Msg = 1127,
	UgcGroupBatchGetPublish_S2C_Msg = 1128,
	MallGetShopCommodityIds_C2S_Msg = 1129,
	MallGetShopCommodityIds_S2C_Msg = 1130,
	RoomJoinFromShare_C2S_Msg = 1131,
	RoomJoinFromShare_S2C_Msg = 1132,
	FireworksActivityStartNtf = 1133,
	FireworksActivityEndNtf = 1134,
	SetFireworksText_C2S_Msg = 1135,
	SetFireworksText_S2C_Msg = 1136,
	UseFireworksItem_C2S_Msg = 1137,
	UseFireworksItem_S2C_Msg = 1138,
	UseFireworksNtf = 1139,
	TakeawayGetRewardBox_C2S_Msg = 1140,
	TakeawayGetRewardBox_S2C_Msg = 1141,
	TakeawayStartUnlockBox_C2S_Msg = 1142,
	TakeawayStartUnlockBox_S2C_Msg = 1143,
	TakeawayGetSharingReward_C2S_Msg = 1144,
	TakeawayGetSharingReward_S2C_Msg = 1145,
	BagUseInteractItemMsg_C2S_Msg = 1146,
	BagUseInteractItemMsg_S2C_Msg = 1147,
	UploadAsaIadInfo_C2S_Msg = 1148,
	UploadAsaIadInfo_S2C_Msg = 1149,
	RaffleFreeDrawAcquiredNtf = 1150,
	RaffleFreeDrawDeliveredNtf = 1151,
	FetchAroundRank_C2S_Msg = 1152,
	FetchAroundRank_S2C_Msg = 1153,
	ReportShare_C2S_Msg = 1154,
	ReportShare_S2C_Msg = 1155,
	UgcStarWorldChangeMap_C2S_Msg = 1156,
	UgcStarWorldChangeMap_S2C_Msg = 1157,
	UgcGetAllPresetTopics_C2S_Msg = 1158,
	UgcGetAllPresetTopics_S2C_Msg = 1159,
	UgcHomePageRecommend_C2S_Msg = 1160,
	UgcHomePageRecommend_S2C_Msg = 1161,
	UgcGetUserFeedbackData_C2S_Msg = 1162,
	UgcGetUserFeedbackData_S2C_Msg = 1163,
	UgcRedPointNtf = 1164,
	UgcLvChangeNtf = 1165,
	UgcCoCreateDing_C2S_Msg = 1166,
	UgcCoCreateDing_S2C_Msg = 1167,
	UgcCoCreatorDingNtf = 1168,
	UgcHomePageThemeAllMaps_C2S_Msg = 1169,
	UgcHomePageThemeAllMaps_S2C_Msg = 1170,
	UgcCoCreateWhite_C2S_Msg = 1171,
	UgcCoCreateWhite_S2C_Msg = 1172,
	UgcLayerIsApply_C2S_Msg = 1173,
	UgcLayerIsApply_S2C_Msg = 1174,
	AchievementGetList_C2S_Msg = 1175,
	AchievementGetList_S2C_Msg = 1176,
	AchievementReward_C2S_Msg = 1177,
	AchievementReward_S2C_Msg = 1178,
	AchievementRedDotNtf = 1179,
	AchievementStatusNtf = 1180,
	GetFireworksInfo_C2S_Msg = 1181,
	GetFireworksInfo_S2C_Msg = 1182,
	RedEnvelopRainTake_C2S_Msg = 1183,
	SceneGetPlayerSceneId_C2S_Msg = 1184,
	SceneGetPlayerSceneId_S2C_Msg = 1185,
	RedEnvelopRainOpen_S2C_Msg = 1186,
	RedEnvelopRainOpenResultNtf = 1187,
	RedEnvelopRainQueryReward_C2S_Msg = 1188,
	RedEnvelopRainQueryReward_S2C_Msg = 1189,
	ClientResPatchNtf = 1190,
	ResVersionSync_C2S_Msg = 1191,
	ResVersionSync_S2C_Msg = 1192,
	GetAttachmentsAndDelete_C2S_Msg = 1193,
	GetAttachmentsAndDelete_S2C_Msg = 1194,
	MallBatchGiveMsg_C2S_Msg = 1195,
	MallBatchGiveMsg_S2C_Msg = 1196,
	MallBatchDemandMsg_C2S_Msg = 1197,
	MallBatchDemandMsg_S2C_Msg = 1198,
	MallGiveRecordList_C2S_Msg = 1199,
	MallGiveRecordList_S2C_Msg = 1200,
	SnsInvitationCreate_C2S_Msg = 1201,
	SnsInvitationCreate_S2C_Msg = 1202,
	SnsInvitationAccept_C2S_Msg = 1203,
	SnsInvitationAccept_S2C_Msg = 1204,
	SnsInvitationRefreshCode_C2S_Msg = 1205,
	SnsInvitationRefreshCode_S2C_Msg = 1206,
	ReportCompleteTask_C2S_Msg = 1207,
	ReportCompleteTask_S2C_Msg = 1208,
	TlogDataReport_C2S_Msg = 1209,
	TlogDataReport_S2C_Msg = 1210,
	UgcGetHotPlaying_C2S_Msg = 1211,
	UgcGetHotPlaying_S2C_Msg = 1212,
	XiaoWoForwardToDS_C2S_Msg = 1213,
	XiaoWoForwardToDS_S2C_Msg = 1214,
	XiaowoSetItemDetail_C2S_Msg = 1215,
	XiaowoSetItemDetail_S2C_Msg = 1216,
	XiaoWoSetInstructionAndImage_C2S_Msg = 1217,
	XiaoWoSetInstructionAndImage_S2C_Msg = 1218,
	XiaoWoAttrNtf = 1219,
	SceneInvite_C2S_Msg = 1220,
	SceneInvite_S2C_Msg = 1221,
	SceneReplyInvite_C2S_Msg = 1222,
	SceneReplyInvite_S2C_Msg = 1223,
	SceneInviteNtf = 1224,
	SceneInviteReplyNtf = 1225,
	EnterPlayerXiaoWo_C2S_Msg = 1226,
	EnterPlayerXiaoWo_S2C_Msg = 1227,
	RedEnvelopRainTake_S2C_Msg = 1228,
	RedEnvelopRainOpen_C2S_Msg = 1229,
	XiaowoGetEditMetaInfo_C2S_Msg = 1230,
	XiaowoGetEditMetaInfo_S2C_Msg = 1231,
	ChatSetQQSyncStatus_C2S_Msg = 1232,
	ChatSetQQSyncStatus_S2C_Msg = 1233,
	ChatGetQQSyncStatus_C2S_Msg = 1234,
	ChatGetQQSyncStatus_S2C_Msg = 1235,
	SearchPlayerUid_C2S_Msg = 1236,
	SearchPlayerUid_S2C_Msg = 1237,
	ModifyPlayerLobbyNpcInfo_C2S_Msg = 1238,
	ModifyPlayerLobbyNpcInfo_S2C_Msg = 1239,
	GameliveTaskRefresh_C2S_Msg = 1240,
	GameliveTaskRefresh_S2C_Msg = 1241,
	UpdatePlayerLocation_C2S_Msg = 1242,
	UpdatePlayerLocation_S2C_Msg = 1243,
	FriendIntimacyChangeNtf = 1244,
	TeamJoinRoomConfirmNtf = 1245,
	RoomTeamConfirmJoin_C2S_Msg = 1246,
	RoomTeamConfirmJoin_S2C_Msg = 1247,
	RoomPlayerReadyReminder_C2S_Msg = 1248,
	RoomPlayerReadyReminder_S2C_Msg = 1249,
	RoomPlayerReadyReminderNtf = 1250,
	CompetitionCommon_C2S_Msg = 1251,
	CompetitionCommon_S2C_Msg = 1252,
	UgcSvrTest_C2S_Msg = 1253,
	UgcSvrTest_S2C_Msg = 1254,
	TeamJoinRoomCancelNtf = 1255,
	GetLobbyInfo_C2S_Msg = 1256,
	GetLobbyInfo_S2C_Msg = 1257,
	GetPlayCalendarConfig_C2S_Msg = 1258,
	GetPlayCalendarConfig_S2C_Msg = 1259,
	QueryTranslationData_C2S_Msg = 1260,
	QueryTranslationData_S2C_Msg = 1261,
	NtfTranslationData_S2C_Msg = 1262,
	GetPlayerCalendarConfig_C2S_Msg = 1263,
	GetPlayerCalendarConfig_S2C_Msg = 1264,
	SquadRefreshNtf = 1265,
	QQGroupStartTeamTask_C2S_Msg = 1266,
	QQGroupStartTeamTask_S2C_Msg = 1267,
	UgcGetPublishTags_C2S_Msg = 1268,
	UgcGetPublishTags_S2C_Msg = 1269,
	LobbyEnvelopSettingDataNtf = 1270,
	LetsGoRecommendMatchType_C2S_Msg = 1271,
	LetsGoRecommendMatchType_S2C_Msg = 1272,
	BagItemRemoveNtf = 1273,
	MarqueeNoticeNtf = 1274,
	ClientClickPanel_C2S_Msg = 1275,
	ClientClickPanel_S2C_Msg = 1276,
	UgcSingleLevelGetDressInfo_C2S_Msg = 1277,
	UgcSingleLevelGetDressInfo_S2C_Msg = 1278,
	XiaowoTreeInfoNtf = 1279,
	CompetitionCommonNtf = 1280,
	PlatPrivilegesProcess_C2S_Msg = 1281,
	PlatPrivilegesProcess_S2C_Msg = 1282,
	BattleDsJumpNtf = 1283,
	DsJumpNtf = 1284,
	XiaoWoSampleRoomAlloc_C2S_Msg = 1285,
	XiaoWoSampleRoomAlloc_S2C_Msg = 1286,
	XiaoWoSaveLayout_C2S_Msg = 1287,
	XiaoWoSaveLayout_S2C_Msg = 1288,
	XiaoWoGetLayoutList_C2S_Msg = 1289,
	XiaoWoGetLayoutList_S2C_Msg = 1290,
	XiaoWoInitFurnitureGet_C2S_Msg = 1291,
	XiaoWoInitFurnitureGet_S2C_Msg = 1292,
	LobbyInfoNtf = 1293,
	SuperLinearDraw_C2S_Msg = 1294,
	SuperLinearDraw_S2C_Msg = 1295,
	SuperLinearGetReward_C2S_Msg = 1296,
	SuperLinearGetReward_S2C_Msg = 1297,
	GetCommonGuideStep_C2S_Msg = 1298,
	FinishCommonGuideStep_C2S_Msg = 1299,
	FinishCommonGuideStep_S2C_Msg = 1300,
	PlayerPlayNewYearCall_C2S_Msg = 1301,
	PlayerPlayNewYearCall_S2C_Msg = 1302,
	UgcGetCollectStarActivityMap_C2S_Msg = 1303,
	UgcGetCollectStarActivityMap_S2C_Msg = 1304,
	UgcGenAiVoice_C2S_Msg = 1305,
	UgcGenAiVoice_S2C_Msg = 1306,
	UgcAiVoiceGenSucNtf = 1307,
	UgcGenAiAnicap_C2S_Msg = 1308,
	UgcGenAiAnicap_S2C_Msg = 1309,
	UgcAiAnicapGenSucNtf = 1310,
	UgcGenAiAnswer_C2S_Msg = 1311,
	UgcGenAiAnswer_S2C_Msg = 1312,
	UgcAiAnswerGenSucNtf = 1313,
	RedEnvelopeRainSetRedPoint_C2S_Msg = 1314,
	RedEnvelopeRainSetRedPoint_S2C_Msg = 1315,
	LuckyBalloonShoot_C2S_Msg = 1316,
	LuckyBalloonShoot_S2C_Msg = 1317,
	UgcMapSearchFront_C2S_Msg = 1318,
	UgcMapSearchFront_S2C_Msg = 1319,
	UgcSearchTopicDetail_C2S_Msg = 1320,
	UgcSearchTopicDetail_S2C_Msg = 1321,
	FpsSetSettings_C2S_Msg = 1322,
	FpsSetSettings_S2C_Msg = 1323,
	FpsGetSettings_C2S_Msg = 1324,
	FpsGetSettings_S2C_Msg = 1325,
	GetTimeLimitedCheckInReward_C2S_Msg = 1326,
	GetTimeLimitedCheckInReward_S2C_Msg = 1327,
	ListScratchOffTickets_C2S_Msg = 1328,
	ListScratchOffTickets_S2C_Msg = 1329,
	BeginScratchOffTickets_C2S_Msg = 1330,
	BeginScratchOffTickets_S2C_Msg = 1331,
	GetScratchOffTicketsReward_C2S_Msg = 1332,
	GetScratchOffTicketsReward_S2C_Msg = 1333,
	UpgradeHigherScratchOffTickets_C2S_Msg = 1334,
	UpgradeHigherScratchOffTickets_S2C_Msg = 1335,
	ClubMemberLiveInfo_C2S_Msg = 1336,
	ClubMemberLiveInfo_S2C_Msg = 1337,
	RedPacketOpen_C2S_Msg = 1338,
	RedPacketOpen_S2C_Msg = 1339,
	RedPacketOpenResultNtf = 1340,
	RedPacketQuery_C2S_Msg = 1341,
	RedPacketQuery_S2C_Msg = 1342,
	ConfirmAssistFriend_C2S_Msg = 1343,
	ConfirmAssistFriend_S2C_Msg = 1344,
	GetAccumulateBlessingsReward_C2S_Msg = 1345,
	GetAccumulateBlessingsReward_S2C_Msg = 1346,
	StarPlayerNtf = 1347,
	RaffleBIDiscountReceivedNtf = 1348,
	UltramanThemeCollectionProgress_C2S_Msg = 1349,
	UltramanThemeCollectionProgress_S2C_Msg = 1350,
	UltramanThemeReward_C2S_Msg = 1351,
	UltramanThemeReward_S2C_Msg = 1352,
	LuckyStarUnlockStar_C2S_Msg = 1353,
	LuckyStarUnlockStar_S2C_Msg = 1354,
	LuckyStarGetDetailInfo_C2S_Msg = 1355,
	LuckyStarGetDetailInfo_S2C_Msg = 1356,
	LuckyStarGiveStar_C2S_Msg = 1357,
	LuckyStarGiveStar_S2C_Msg = 1358,
	LuckyStarReceiveStar_C2S_Msg = 1359,
	LuckyStarReceiveStar_S2C_Msg = 1360,
	LuckyStarGenerateRequireStar_C2S_Msg = 1361,
	LuckyStarGenerateRequireStar_S2C_Msg = 1362,
	LuckyStarGenerateGiveStar_C2S_Msg = 1363,
	LuckyStarGenerateGiveStar_S2C_Msg = 1364,
	UgcPublishPreCheck_C2S_Msg = 1365,
	UgcPublishPreCheck_S2C_Msg = 1366,
	TimeLimitedCheckInActivityUnlockNtf = 1367,
	UgcStarWorldRedDot_C2S_Msg = 1368,
	UgcStarWorldRedDot_S2C_Msg = 1369,
	ClubMemberShareMap_C2S_Msg = 1370,
	ClubMemberShareMap_S2C_Msg = 1371,
	ClubAdminShareMap_C2S_Msg = 1372,
	ClubAdminShareMap_S2C_Msg = 1373,
	ClubNewApplicantNtf = 1374,
	XiaoWoCropWater_C2S_Msg = 1375,
	XiaoWoCropWater_S2C_Msg = 1376,
	XiaoWoCropHarvest_C2S_Msg = 1377,
	XiaoWoCropHarvest_S2C_Msg = 1378,
	LuckyStarGetReward_C2S_Msg = 1379,
	LuckyStarGetReward_S2C_Msg = 1380,
	LetsGoGetWolfKillInfo_C2S_Msg = 1381,
	LetsGoGetWolfKillInfo_S2C_Msg = 1382,
	LetsGoGetWolfKillReputationScore_C2S_Msg = 1383,
	LetsGoGetWolfKillReputationScore_S2C_Msg = 1384,
	InviteFriendLogin_C2S_Msg = 1385,
	InviteFriendLogin_S2C_Msg = 1386,
	UgcMatchLobbyDetail_C2S_Msg = 1387,
	UgcMatchLobbyDetail_S2C_Msg = 1388,
	UgcMatchRoomUgcIdChange_C2S_Msg = 1389,
	UgcMatchRoomUgcIdChange_S2C_Msg = 1390,
	UgcMatchRoomUgcIdValidCheck_C2S_Msg = 1391,
	UgcMatchRoomUgcIdValidCheck_S2C_Msg = 1392,
	UgcRoomLobbyMap_C2S_Msg = 1393,
	UgcRoomLobbyMap_S2C_Msg = 1394,
	UgcAiAnicapQueueNtf = 1395,
	UgcAnicapCancelQueue_C2S_Msg = 1396,
	UgcAnicapCancelQueue_S2C_Msg = 1397,
	LuckStarReceiveNtf = 1398,
	ClubGetShareMapList_C2S_Msg = 1399,
	ClubGetShareMapList_S2C_Msg = 1400,
	UgcResHomePageRecommend_C2S_Msg = 1401,
	UgcResHomePageRecommend_S2C_Msg = 1402,
	UgcResHomePageRecommedMoreSet_C2S_Msg = 1403,
	UgcResHomePageRecommedMoreSet_S2C_Msg = 1404,
	UgcResCommunitySearch_C2S_Msg = 1405,
	UgcResCommunitySearch_S2C_Msg = 1406,
	UgcResCommunityGet_C2S_Msg = 1407,
	UgcResCommunityGet_S2C_Msg = 1408,
	UgcResBagAdd_C2S_Msg = 1409,
	UgcResBagAdd_S2C_Msg = 1410,
	UgcResBagDelete_C2S_Msg = 1411,
	UgcResBagDelete_S2C_Msg = 1412,
	UgcResBagGet_C2S_Msg = 1413,
	UgcResBagGet_S2C_Msg = 1414,
	UgcResBagSearch_C2S_Msg = 1415,
	UgcResBagSearch_S2C_Msg = 1416,
	UgcResCreate_C2S_Msg = 1417,
	UgcResCreate_S2C_Msg = 1418,
	UgcResPublish_C2S_Msg = 1419,
	UgcResPublish_S2C_Msg = 1420,
	UgcResGetMyList_C2S_Msg = 1421,
	UgcResGetMyList_S2C_Msg = 1422,
	UgcResDelete_C2S_Msg = 1423,
	UgcResDelete_S2C_Msg = 1424,
	UgcResTakeOff_C2S_Msg = 1425,
	UgcResTakeOff_S2C_Msg = 1426,
	UgcResOperate_C2S_Msg = 1427,
	UgcResOperate_S2C_Msg = 1428,
	UgcResModifyPublish_C2S_Msg = 1429,
	UgcResModifyPublish_S2C_Msg = 1430,
	UgcResGetCollect_C2S_Msg = 1431,
	UgcResGetCollect_S2C_Msg = 1432,
	UgcResGetTopic_C2S_Msg = 1433,
	UgcResGetTopic_S2C_Msg = 1434,
	UgcResGetPublishDetail_C2S_Msg = 1435,
	UgcResGetPublishDetail_S2C_Msg = 1436,
	RedPacketSend_C2S_Msg = 1437,
	RedPacketSend_S2C_Msg = 1438,
	LuckyStarReceiveNtf = 1439,
	UgcTextLawful_C2S_Msg = 1440,
	UgcTextLawful_S2C_Msg = 1441,
	SpecialFaceReport_C2S_Msg = 1442,
	SpecialFaceReport_S2C_Msg = 1443,
	ReputationScoreNotEnoughNtf = 1444,
	GetRecentBattlePlayer_C2S_Msg = 1445,
	GetRecentBattlePlayer_S2C_Msg = 1446,
	CancelMarqueeNoticeNtf = 1447,
	VideoExamineFailedNtf = 1448,
	ClubSearch_C2S_Msg = 1449,
	ClubSearch_S2C_Msg = 1450,
	LuckyStarGetGiveInfo_C2S_Msg = 1451,
	LuckyStarGetGiveInfo_S2C_Msg = 1452,
	LuckyStarGetRequireResult_C2S_Msg = 1453,
	LuckyStarGetRequireResult_S2C_Msg = 1454,
	RedEnvelopeRainShared_C2S_Msg = 1455,
	RedEnvelopeRainShared_S2C_Msg = 1456,
	RedEnvelopeRainClickShareLink_C2S_Msg = 1457,
	RedEnvelopeRainClickShareLink_S2C_Msg = 1458,
	RedEnvelopeRainSharedLinkClickedNtf = 1459,
	LuckyStarGetRequireInfo_C2S_Msg = 1460,
	LuckyStarGetRequireInfo_S2C_Msg = 1461,
	UGCSearchSuggestion_C2S_Msg = 1462,
	UGCSearchSuggestion_S2C_Msg = 1463,
	RoomMapStateSync_C2S_Msg = 1464,
	RoomMapStateSync_S2C_Msg = 1465,
	RoomMapStateSyncNtf = 1466,
	UltramanThemeStatusNtf = 1467,
	UgcAiGenModule_C2S_Msg = 1468,
	UgcAiGenModule_S2C_Msg = 1469,
	UgcAiGenModuleResultNtf = 1470,
	ClubTextCheck_C2S_Msg = 1471,
	ClubTextCheck_S2C_Msg = 1472,
	LobbyGetPlayerRecommendLabel_C2S_Msg = 1473,
	LobbyGetPlayerRecommendLabel_S2C_Msg = 1474,
	UgcNewYearActivity_C2S_Msg = 1475,
	UgcNewYearActivity_S2C_Msg = 1476,
	UltramanThemeActivateStory_C2S_Msg = 1477,
	UltramanThemeActivateStory_S2C_Msg = 1478,
	GetActivityConfig_C2S_Msg = 1479,
	GetActivityConfig_S2C_Msg = 1480,
	XiaoWoGetSampleRoomList_C2S_Msg = 1481,
	XiaoWoGetSampleRoomList_S2C_Msg = 1482,
	UgcAigcGetHistory_C2S_Msg = 1483,
	UgcAigcGetHistory_S2C_Msg = 1484,
	UgcAigcUse_C2S_Msg = 1485,
	UgcAigcUse_S2C_Msg = 1486,
	UgcMulTestSaveMeta_C2S_Msg = 1487,
	UgcMulTestSaveMeta_S2C_Msg = 1488,
	ActivityInfoNtf = 1489,
	LBSUpdate_C2S_Msg = 1490,
	LBSUpdate_S2C_Msg = 1491,
	LBSGetNearby_C2S_Msg = 1492,
	LBSGetNearby_S2C_Msg = 1493,
	LBSGetNearbyNtf = 1494,
	LBSDelete_C2S_Msg = 1495,
	LBSDelete_S2C_Msg = 1496,
	LetsGoSettingsChangeNtf = 1497,
	UgcMapGroupList_C2S_Msg = 1498,
	UgcMapGroupList_S2C_Msg = 1499,
	SpringBlessingCollectionDataChangeNtf = 1500,
	GetSpringBlessingCollectionCardReward_C2S_Msg = 1501,
	GetSpringBlessingCollectionCardReward_S2C_Msg = 1502,
	GetSpringBlessingCollectionTaskReward_C2S_Msg = 1503,
	GetSpringBlessingCollectionTaskReward_S2C_Msg = 1504,
	ScratchOffTicketsUpgradeNtf = 1505,
	GameTvStatusChangeNtf = 1506,
	XiaoWoCropWaterRecordGet_C2S_Msg = 1507,
	XiaoWoCropWaterRecordGet_S2C_Msg = 1508,
	XiaoWoFarmingHandbookAwardGet_C2S_Msg = 1509,
	XiaoWoFarmingHandbookAwardGet_S2C_Msg = 1510,
	RedPacketShare_C2S_Msg = 1511,
	RedPacketShare_S2C_Msg = 1512,
	RedPacketGetByShareId_C2S_Msg = 1513,
	RedPacketGetByShareId_S2C_Msg = 1514,
	UgcBatchPublishMap_C2S_Msg = 1515,
	UgcBatchPublishMap_S2C_Msg = 1516,
	XiaoWoSaveLayoutPublishRecord_C2S_Msg = 1517,
	XiaoWoSaveLayoutPublishRecord_S2C_Msg = 1518,
	LotterySpringBlessingCard_C2S_Msg = 1519,
	LotterySpringBlessingCard_S2C_Msg = 1520,
	GiveSpringBlessingCard_C2S_Msg = 1521,
	GiveSpringBlessingCard_S2C_Msg = 1522,
	UgcAigcDelHistory_C2S_Msg = 1523,
	UgcAigcDelHistory_S2C_Msg = 1524,
	SpringPray_C2S_Msg = 1525,
	SpringPray_S2C_Msg = 1526,
	PlayerPrayShare_C2S_Msg = 1527,
	PlayerPrayShare_S2C_Msg = 1528,
	NewYearPilotInfoNtf = 1529,
	IntellectualActiviyNtf = 1530,
	ReceiveSpringBlessingCardNtf = 1531,
	GetXiaowoRecomList_C2S_Msg = 1532,
	GetXiaowoRecomList_S2C_Msg = 1533,
	FpsGetActivityCoinInfo_C2S_Msg = 1534,
	FpsGetActivityCoinInfo_S2C_Msg = 1535,
	ClubPinShareMap_C2S_Msg = 1536,
	ClubPinShareMap_S2C_Msg = 1537,
	RedPacketQuerySingle_C2S_Msg = 1538,
	RedPacketQuerySingle_S2C_Msg = 1539,
	UltramanThemeAcitvityTeam_C2S_Msg = 1540,
	UltramanThemeAcitvityTeam_S2C_Msg = 1541,
	RedPacketPatchInfo_C2S_Msg = 1542,
	RedPacketPatchInfo_S2C_Msg = 1543,
	TYCFpsSetSettings_C2S_Msg = 1544,
	TYCFpsSetSettings_S2C_Msg = 1545,
	TYCFpsGetSettings_C2S_Msg = 1546,
	TYCFpsGetSettings_S2C_Msg = 1547,
	GetReturnActivityReward_C2S_Msg = 1548,
	GetReturnActivityReward_S2C_Msg = 1549,
	ReturningActiviyStartNtf = 1550,
	UltramanThemeSelfCollectionProgress_C2S_Msg = 1551,
	UltramanThemeSelfCollectionProgress_S2C_Msg = 1552,
	GetAccumulateBlessingsOnlineTime_C2S_Msg = 1553,
	GetAccumulateBlessingsOnlineTime_S2C_Msg = 1554,
	FpsGetDropLimitInfo_C2S_Msg = 1555,
	FpsGetDropLimitInfo_S2C_Msg = 1556,
	RedPacketEnterScene_C2S_Msg = 1557,
	RedPacketEnterScene_S2C_Msg = 1558,
	PlayerXiaowoAttrNtf = 1559,
	RedPacketClickRedDot_C2S_Msg = 1560,
	RedPacketClickRedDot_S2C_Msg = 1561,
	ListUseItemShareRecord_C2S_Msg = 1562,
	ListUseItemShareRecord_S2C_Msg = 1563,
	ListUseItemChangeRecord_C2S_Msg = 1564,
	ListUseItemChangeRecord_S2C_Msg = 1565,
	QuitBattleNtf = 1566,
	ReturnActivityEndNtf = 1567,
	ReturnActivityGenericNtf = 1568,
	ClubRedDotClear_C2S_Msg = 1569,
	ClubRedDotClear_S2C_Msg = 1570,
	UltramanThemeAddSelfCollection_C2S_Msg = 1571,
	UltramanThemeAddSelfCollection_S2C_Msg = 1572,
	TimeLimitedCheckInActivityDataChangeNtf = 1573,
	FetchTopRankByScore_C2S_Msg = 1574,
	FetchTopRankByScore_S2C_Msg = 1575,
	GetRaffleList_C2S_Msg = 1576,
	GetRaffleList_S2C_Msg = 1577,
	MatchTypeList_C2S_Msg = 1578,
	MatchTypeList_S2C_Msg = 1579,
	SetFriendRemarkName_C2S_Msg = 1580,
	SetFriendRemarkName_S2C_Msg = 1581,
	GetRankTabRankIdList_C2S_Msg = 1582,
	GetRankTabRankIdList_S2C_Msg = 1583,
	StickFriend_C2S_Msg = 1584,
	StickFriend_S2C_Msg = 1585,
	XiaoWoSetWelcomeInfo_C2S_Msg = 1586,
	XiaoWoSetWelcomeInfo_S2C_Msg = 1587,
	XiaoWoSendLiuYanMessage_C2S_Msg = 1588,
	XiaoWoSendLiuYanMessage_S2C_Msg = 1589,
	XiaoWoDeleteLiuYanMessage_C2S_Msg = 1590,
	XiaoWoDeleteLiuYanMessage_S2C_Msg = 1591,
	XiaoWoGetLiuYanMessage_C2S_Msg = 1592,
	XiaoWoGetLiuYanMessage_S2C_Msg = 1593,
	XiaoWoGetLiuYanMessageReply_C2S_Msg = 1594,
	XiaoWoGetLiuYanMessageReply_S2C_Msg = 1595,
	XiaoWoChoiceLiuYanMessage_C2S_Msg = 1596,
	XiaoWoChoiceLiuYanMessage_S2C_Msg = 1597,
	XiaoWoReceiveNewLiuYanMessageNtf = 1598,
	UgcCollectionCreate_C2S_Msg = 1599,
	UgcCollectionCreate_S2C_Msg = 1600,
	UgcCollectionModify_C2S_Msg = 1601,
	UgcCollectionModify_S2C_Msg = 1602,
	UgcCollectionGetBriefs_C2S_Msg = 1603,
	UgcCollectionGetBriefs_S2C_Msg = 1604,
	UgcCollectionOperate_C2S_Msg = 1605,
	UgcCollectionOperate_S2C_Msg = 1606,
	UgcCollectionSearch_C2S_Msg = 1607,
	UgcCollectionSearch_S2C_Msg = 1608,
	UgcCollectionGetInfo_C2S_Msg = 1609,
	UgcCollectionGetInfo_S2C_Msg = 1610,
	UploadGameSetting_C2S_Msg = 1611,
	UploadGameSetting_S2C_Msg = 1612,
	DownloadGameSetting_C2S_Msg = 1613,
	DownloadGameSetting_S2C_Msg = 1614,
	GetSeasonFashionEquipBook_C2S_Msg = 1615,
	GetSeasonFashionEquipBook_S2C_Msg = 1616,
	ClubGetRecommendList_C2S_Msg = 1617,
	ClubGetRecommendList_S2C_Msg = 1618,
	SceneLevelPackage_C2S_Msg = 1619,
	SceneLevelPackage_S2C_Msg = 1620,
	RoomReservation_C2S_Msg = 1621,
	RoomReservation_S2C_Msg = 1622,
	RoomReservationNtf = 1623,
	RoomReservationResponse_C2S_Msg = 1624,
	RoomReservationResponse_S2C_Msg = 1625,
	RoomReservationResponseNtf = 1626,
	ChangeShowQualifyType_C2S_Msg = 1627,
	ChangeShowQualifyType_S2C_Msg = 1628,
	BuyReturnChargeSignInActivityTicket_C2S_Msg = 1629,
	BuyReturnChargeSignInActivityTicket_S2C_Msg = 1630,
	BuyReturnChargeSignInActivityTicketNtf = 1631,
	UgcMatchLobbyDetailEx_C2S_Msg = 1632,
	UgcMatchLobbyDetailEx_S2C_Msg = 1633,
	GetCheckInManualReward_C2S_Msg = 1634,
	GetCheckInManualReward_S2C_Msg = 1635,
	UpgradeCheckInManual_C2S_Msg = 1636,
	UpgradeCheckInManual_S2C_Msg = 1637,
	CheckInManualUpgradeNtf = 1638,
	ClubMSDKReport_C2S_Msg = 1639,
	ClubMSDKReport_S2C_Msg = 1640,
	XiaoWoIsWhiteListAccountNtf = 1641,
	UgcBugGoods_C2S_Msg = 1642,
	UgcBugGoods_S2C_Msg = 1643,
	UgcBuyGoodsResultNtf = 1644,
	UgcActivePublishGoods_C2S_Msg = 1645,
	UgcActivePublishGoods_S2C_Msg = 1646,
	UserWhiteListCheck_C2S_Msg = 1647,
	UserWhiteListCheck_S2C_Msg = 1648,
	ClubBatchGetBasicInfo_C2S_Msg = 1649,
	ClubBatchGetBasicInfo_S2C_Msg = 1650,
	UgcApplyGoodsId_C2S_Msg = 1651,
	UgcApplyGoodsId_S2C_Msg = 1652,
	UgcPreAudit_C2S_Msg = 1653,
	UgcPreAudit_S2C_Msg = 1654,
	UgcPreAuditResultNtf = 1655,
	ClubSearchByTag_C2S_Msg = 1656,
	ClubSearchByTag_S2C_Msg = 1657,
	UgcStarWorldNavigationBar_C2S_Msg = 1658,
	UgcStarWorldNavigationBar_S2C_Msg = 1659,
	ActivityLotteryDraw_C2S_Msg = 1660,
	ActivityLotteryDraw_S2C_Msg = 1661,
	InterServerGiftGetFriendList_C2S_Msg = 1662,
	InterServerGiftGetFriendList_S2C_Msg = 1663,
	InterServerGiftCheckFriendBuy_C2S_Msg = 1664,
	InterServerGiftCheckFriendBuy_S2C_Msg = 1665,
	ReturnRefreshJumpToSignInAfterRoundFlag_C2S_Msg = 1666,
	ReturnRefreshJumpToSignInAfterRoundFlag_S2C_Msg = 1667,
	RoomMapVote_C2S_Msg = 1668,
	RoomMapVote_S2C_Msg = 1669,
	GetUgcCoPlayInfo_C2S_Msg = 1670,
	GetUgcCoPlayInfo_S2C_Msg = 1671,
	UgcBuyGoods_C2S_Msg = 1672,
	UgcBuyGoods_S2C_Msg = 1673,
	UgcNeedDownRes_C2S_Msg = 1674,
	UgcNeedDownRes_S2C_Msg = 1675,
	UgcGetDataStoreData_C2S_Msg = 1676,
	UgcGetDataStoreData_S2C_Msg = 1677,
	NewActivityPilotInfoNtf = 1678,
	RoomEndSettlement_C2S_Msg = 1679,
	RoomEndSettlement_S2C_Msg = 1680,
	UgcCollectionReserved1_C2S_Msg = 1681,
	UgcCollectionReserved1_S2C_Msg = 1682,
	UgcGetPublishGoodsProtoVersion_C2S_Msg = 1683,
	UgcGetPublishGoodsProtoVersion_S2C_Msg = 1684,
	UgcSetPublishGoodsProtoVersion_C2S_Msg = 1685,
	UgcSetPublishGoodsProtoVersion_S2C_Msg = 1686,
	PlayerGameSettingTlog_C2S_Msg = 1687,
	PlayerGameSettingTlog_S2C_Msg = 1688,
	RoomNeedNtfWhenAllMemberReadyForMatch_C2S_Msg = 1689,
	RoomNeedNtfWhenAllMemberReadyForMatch_S2C_Msg = 1690,
	RoomAllMemberReadyForMatchNtf = 1691,
	RoomOperationNtf = 1692,
	FeedKungFuPanda_C2S_Msg = 1693,
	FeedKungFuPanda_S2C_Msg = 1694,
	ReportRacingKungFuPandaResult_C2S_Msg = 1695,
	ReportRacingKungFuPandaResult_S2C_Msg = 1696,
	ListKungFuPandaHelpData_C2S_Msg = 1697,
	ListKungFuPandaHelpData_S2C_Msg = 1698,
	CheckGiveMailAttachment_C2S_Msg = 1699,
	CheckGiveMailAttachment_S2C_Msg = 1700,
	LobbyGetMapInfo_C2S_Msg = 1701,
	LobbyGetMapInfo_S2C_Msg = 1702,
	MallGiveDeliverGoodsNtf = 1703,
	LobbyGetListInfo_C2S_Msg = 1704,
	LobbyGetListInfo_S2C_Msg = 1705,
	RoomCommonBroadcast_C2S_Msg = 1706,
	RoomCommonBroadcast_S2C_Msg = 1707,
	RoomBroadcastInfoNtf = 1708,
	GetConcertStarInfo_C2S_Msg = 1709,
	GetConcertStarInfo_S2C_Msg = 1710,
	GenerateConcertTicket_C2S_Msg = 1711,
	GenerateConcertTicket_S2C_Msg = 1712,
	GetConcertTicketInfo_C2S_Msg = 1713,
	GetConcertTicketInfo_S2C_Msg = 1714,
	TeamRecommendRole_C2S_Msg = 1730,
	TeamRecommendRole_S2C_Msg = 1731,
	GetPlayerGrayTagInfo_C2S_Msg = 1732,
	GetPlayerGrayTagInfo_S2C_Msg = 1733,
	UgcCollectionRecommendList_C2S_Msg = 1750,
	UgcCollectionRecommendList_S2C_Msg = 1751,
	UgcCollectionExcellent_C2S_Msg = 1752,
	UgcCollectionExcellent_S2C_Msg = 1753,
	TeamRecommendRoleNtf = 1802,
	BagUnlockSlotId_C2S_Msg = 1803,
	BagUnlockSlotId_S2C_Msg = 1804,
	PlayerSettingHomePage_C2S_Msg = 1805,
	PlayerSettingHomePage_S2C_Msg = 1806,
	ActivityGetMultiPlayerSquadInfo_C2S_Msg = 1807,
	ActivityGetMultiPlayerSquadInfo_S2C_Msg = 1808,
	ActivityMultiPlayerSquadJoin_C2S_Msg = 1809,
	ActivityMultiPlayerSquadJoin_S2C_Msg = 1810,
	ActivityMultiPlayerSquadSaveGroupPhoto_C2S_Msg = 1811,
	ActivityMultiPlayerSquadSaveGroupPhoto_S2C_Msg = 1812,
	SetBattlePlayerClientInfo_C2S_Msg = 1817,
	SetBattlePlayerClientInfo_S2C_Msg = 1818,
	BattlePlayerClientInfoModifyNtf = 1819,
	SendRoomMemberToMemberNtf_C2S_Msg = 1820,
	SendRoomMemberToMemberNtf_S2C_Msg = 1821,
	RoomMemberToMemberNtf = 1822,
	FittingSlotShow_C2S_Msg = 1823,
	FittingSlotShow_S2C_Msg = 1824,
	StickerGetReward_C2S_Msg = 1825,
	StickerGetReward_S2C_Msg = 1826,
	CurBubbleConfigStateQuery_C2S_Msg = 1827,
	CurBubbleConfigStateQuery_S2C_Msg = 1828,
	CurBubbleConfigHasVisited_C2S_Msg = 1829,
	CurBubbleConfigHasVisited_S2C_Msg = 1830,
	GetCompetitionWarmUpGameTimesReward_C2S_Msg = 1831,
	GetCompetitionWarmUpGameTimesReward_S2C_Msg = 1832,
	GetCompetitionWarmUpScoreReward_C2S_Msg = 1833,
	GetCompetitionWarmUpScoreReward_S2C_Msg = 1834,
	BagSetInteractionCombination_C2S_Msg = 1835,
	BagSetInteractionCombination_S2C_Msg = 1836,
	BagDelInteractionCombination_C2S_Msg = 1837,
	BagDelInteractionCombination_S2C_Msg = 1838,
	ChatSendClubGroupInvite_C2S_Msg = 1839,
	ChatSendClubGroupInvite_S2C_Msg = 1840,
	DanMuSend_C2S_Msg = 1841,
	DanMuSend_S2C_Msg = 1842,
	DanMuSendNtf = 1843,
	DanMuDelete_C2S_Msg = 1844,
	DanMuDelete_S2C_Msg = 1845,
	DanMuDeleteNtf = 1846,
	DanMuDetail_C2S_Msg = 1847,
	DanMuDetail_S2C_Msg = 1848,
	DanMuLike_C2S_Msg = 1849,
	DanMuLike_S2C_Msg = 1850,
	DanMuLikeNtf = 1851,
	DanMuTipOff_C2S_Msg = 1852,
	DanMuTipOff_S2C_Msg = 1853,
	DanMuDetailFlush_C2S_Msg = 1854,
	DanMuDetailFlush_S2C_Msg = 1855,
	FarmCreate_C2S_Msg = 1856,
	FarmCreate_S2C_Msg = 1857,
	FarmEnter_C2S_Msg = 1858,
	FarmEnter_S2C_Msg = 1859,
	FarmExit_C2S_Msg = 1860,
	FarmExit_S2C_Msg = 1861,
	FarmKickNtf = 1862,
	FarmDSInfoNtf = 1863,
	FarmAttrNtf = 1864,
	FarmOwnerAttrNtf = 1865,
	FarmTest_C2S_Msg = 1866,
	FarmTest_S2C_Msg = 1867,
	FarmOp_C2S_Msg = 1868,
	FarmOp_S2C_Msg = 1869,
	FarmBuildingLevelUp_C2S_Msg = 1870,
	FarmBuildingLevelUp_S2C_Msg = 1871,
	FarmBuildingSell_C2S_Msg = 1872,
	FarmBuildingSell_S2C_Msg = 1873,
	FarmBuildingUnlock_C2S_Msg = 1874,
	FarmBuildingUnlock_S2C_Msg = 1875,
	FarmForwardToDS_C2S_Msg = 1876,
	FarmForwardToDS_S2C_Msg = 1877,
	FarmBadGuysList_C2S_Msg = 1878,
	FarmBadGuysList_S2C_Msg = 1879,
	FarmBadGuyStealingDetail_C2S_Msg = 1880,
	FarmBadGuyStealingDetail_S2C_Msg = 1881,
	FarmStrangerList_C2S_Msg = 1882,
	FarmStrangerList_S2C_Msg = 1883,
	FarmEvictBadGuy_C2S_Msg = 1884,
	FarmEvictBadGuy_S2C_Msg = 1885,
	FarmGetItemsNtf = 1886,
	FarmCropMenuSelect_C2S_Msg = 1887,
	FarmCropMenuSelect_S2C_Msg = 1888,
	FarmSetClientKV_C2S_Msg = 1889,
	FarmSetClientKV_S2C_Msg = 1890,
	RoguelikeQuickSettlement_C2S_Msg = 1891,
	RoguelikeQuickSettlement_S2C_Msg = 1892,
	RoguelikeQuickSettlementNtf = 1893,
	LetsGoSeasonBatchSettlementNtf = 1894,
	MallSeasonShopTabMsg_C2S_Msg = 1895,
	MallSeasonShopTabMsg_S2C_Msg = 1896,
	PlayerBatchGetRecordItemForLevel_C2S_Msg = 1897,
	PlayerBatchGetRecordItemForLevel_S2C_Msg = 1898,
	GetQuickRewardList_C2S_Msg = 1899,
	GetQuickRewardList_S2C_Msg = 1900,
	ReceiveQuickReward_C2S_Msg = 1901,
	ReceiveQuickReward_S2C_Msg = 1902,
	DSInvitationReplyCheck_C2S_Msg = 1903,
	DSInvitationReplyCheck_S2C_Msg = 1904,
	RoomMidJoinBattleFailNtf = 1905,
	GetSeasonFashionBattleData_C2S_Msg = 1906,
	GetSeasonFashionBattleData_S2C_Msg = 1907,
	GetSeasonFashionGeneralBattleData_C2S_Msg = 1908,
	GetSeasonFashionGeneralBattleData_S2C_Msg = 1909,
	StreamToken_C2S_Msg = 1910,
	StreamToken_S2C_Msg = 1911,
	UgcResPrivateAdapt_C2S_Msg = 1912,
	UgcResPrivateAdapt_S2C_Msg = 1913,
	UgcFastSlot_C2S_Msg = 1914,
	UgcFastSlot_S2C_Msg = 1915,
	UgcMapLabelScore_C2S_Msg = 1916,
	UgcMapLabelScore_S2C_Msg = 1917,
	UgcGetPlayerMapLabelScore_C2S_Msg = 1918,
	UgcGetPlayerMapLabelScore_S2C_Msg = 1919,
	UgcMapDownloadInfo_C2S_Msg = 1920,
	UgcMapDownloadInfo_S2C_Msg = 1921,
	UgcDeleteCover_C2S_Msg = 1922,
	UgcDeleteCover_S2C_Msg = 1923,
	UgcUploadCover_C2S_Msg = 1924,
	UgcUploadCover_S2C_Msg = 1925,
	FetchUgcTopRank_C2S_Msg = 1926,
	FetchUgcTopRank_S2C_Msg = 1927,
	UgcApplyRankId_C2S_Msg = 1928,
	UgcApplyRankId_S2C_Msg = 1929,
	UpdateUgcRank_C2S_Msg = 1930,
	UpdateUgcRank_S2C_Msg = 1931,
	GetUgcRankAppointPlayer_C2S_Msg = 1932,
	GetUgcRankAppointPlayer_S2C_Msg = 1933,
	DeleteUgcRankEntryByPlayer_C2S_Msg = 1934,
	DeleteUgcRankEntryByPlayer_S2C_Msg = 1935,
	GetUgcRankAppointRankNo_C2S_Msg = 1936,
	GetUgcRankAppointRankNo_S2C_Msg = 1937,
	BatchGetUgcRankPlayerPublicInfo_C2S_Msg = 1938,
	BatchGetUgcRankPlayerPublicInfo_S2C_Msg = 1939,
	UgcSetUpCover_C2S_Msg = 1940,
	UgcSetUpCover_S2C_Msg = 1941,
	UgcCoverList_C2S_Msg = 1942,
	UgcCoverList_S2C_Msg = 1943,
	XiaoWoShare_C2S_Msg = 1944,
	XiaoWoShare_S2C_Msg = 1945,
	XiaoWoGetVerAndGroup_C2S_Msg = 1946,
	XiaoWoGetVerAndGroup_S2C_Msg = 1947,
	StreamNpcChat_C2S_Msg = 1948,
	StreamNpcChat_S2C_Msg = 1949,
	StreamErrorNtf = 1950,
	StreamNpcChatResponseNtf = 1951,
	StreamLLMCheckResultNtf = 1952,
	StreamLogin_C2S_Msg = 1953,
	StreamLogin_S2C_Msg = 1954,
	GetLevelUGCInfo_C2S_Msg = 1955,
	GetLevelUGCInfo_S2C_Msg = 1956,
	ActivityTakePhoto_C2S_Msg = 1957,
	ActivityTakePhoto_S2C_Msg = 1958,
	ChangeProfileThemeMsg_C2S_Msg = 1959,
	ChangeProfileThemeMsg_S2C_Msg = 1960,
	ActivityDataChangeNtf = 1961,
	SubscribeQQRobot_C2S_Msg = 1962,
	SubscribeQQRobot_S2C_Msg = 1963,
	AppCommon_C2S_Msg = 1964,
	AppCommon_S2C_Msg = 1965,
	GameNtf = 1966,
	GetUserCloudInfo_C2S_Msg = 1967,
	GetUserCloudInfo_S2C_Msg = 1968,
	FarmSendLiuYanMessage_C2S_Msg = 1969,
	FarmSendLiuYanMessage_S2C_Msg = 1970,
	FarmDeleteLiuYanMessage_C2S_Msg = 1971,
	FarmDeleteLiuYanMessage_S2C_Msg = 1972,
	FarmGetLiuYanMessage_C2S_Msg = 1973,
	FarmGetLiuYanMessage_S2C_Msg = 1974,
	FarmReceiveNewLiuYanMessageNtf = 1975,
	FarmSetWelcomeInfo_C2S_Msg = 1976,
	FarmSetWelcomeInfo_S2C_Msg = 1977,
	GetRecommendMatchTypeReward_C2S_Msg = 1978,
	GetRecommendMatchTypeReward_S2C_Msg = 1979,
	RecommendMatchTypeTaskNtf = 1980,
	IntimateRelationOnlineNoticeRecord_C2S_Msg = 1981,
	IntimateRelationOnlineNoticeRecord_S2C_Msg = 1982,
	RejectIntimateRelationOnlineNotice_C2S_Msg = 1983,
	RejectIntimateRelationOnlineNotice_S2C_Msg = 1984,
	UgcGetBadge_C2S_Msg = 1985,
	UgcGetBadge_S2C_Msg = 1986,
	UgcGetBadgeDetail_C2S_Msg = 1987,
	UgcGetBadgeDetail_S2C_Msg = 1988,
	UgcCopyMapProgressNtf = 1989,
	ClickNewRecommendMatchType_C2S_Msg = 1990,
	ClickNewRecommendMatchType_S2C_Msg = 1991,
	NewRecommendMatchTypeChangeNtf = 1992,
	IntimateRelationGuideNtf = 1993,
	DelayIntimateRelationGuide_C2S_Msg = 1994,
	DelayIntimateRelationGuide_S2C_Msg = 1995,
	PlayerAlbumPictureAdd_C2S_Msg = 1996,
	PlayerAlbumPictureAdd_S2C_Msg = 1997,
	PlayerAlbumPictureDel_C2S_Msg = 1998,
	PlayerAlbumPictureDel_S2C_Msg = 1999,
	DressItemStatusChange_C2S_Msg = 2000,
	DressItemStatusChange_S2C_Msg = 2001,
	ChatControl_C2S_Msg = 2002,
	ChatControl_S2C_Msg = 2003,
	DirGetFreeFlowInfo_C2S_Msg = 2004,
	DirGetFreeFlowInfo_S2C_Msg = 2005,
	AchievementGetCompleteStatus_C2S_Msg = 2006,
	AchievementGetCompleteStatus_S2C_Msg = 2007,
	ActivityGetAllInfo_C2S_Msg = 2008,
	ActivityGetAllInfo_S2C_Msg = 2009,
	ActivityReceiveRewards_C2S_Msg = 2010,
	ActivityReceiveRewards_S2C_Msg = 2011,
	ActivityInfoUpdateNtf = 2012,
	ReturnActivityAttrChangeNtf = 2013,
	ReturnActivityRefreshRecommendMatchType_C2S_Msg = 2014,
	ReturnActivityRefreshRecommendMatchType_S2C_Msg = 2015,
	ReturnActivityBuyChargeGiftTicket_C2S_Msg = 2016,
	ReturnActivityBuyChargeGiftTicket_S2C_Msg = 2017,
	ReturnActivityBuyChargeGiftTicketNtf = 2018,
	ActivityGetRecommendPlayerList_C2S_Msg = 2019,
	ActivityGetRecommendPlayerList_S2C_Msg = 2020,
	GetIntelligenceStationReward_C2S_Msg = 2021,
	GetIntelligenceStationReward_S2C_Msg = 2022,
	IntelligenceStationChangeNtf = 2023,
	ActivityFlyingChessGetFreeCoin_C2S_Msg = 2024,
	ActivityFlyingChessGetFreeCoin_S2C_Msg = 2025,
	ActivityFlyingChessRun_C2S_Msg = 2026,
	ActivityFlyingChessRun_S2C_Msg = 2027,
	ActivityFlyingChessRoundRewardGet_C2S_Msg = 2028,
	ActivityFlyingChessRoundRewardGet_S2C_Msg = 2029,
	ActivityFlyingChessBigRewardGet_C2S_Msg = 2030,
	ActivityFlyingChessBigRewardGet_S2C_Msg = 2031,
	ActivityFlyingChessBigRewardGetNtf = 2032,
	ActivityHYNSyncInfoNtf = 2033,
	ActivityHYNCheckIn_C2S_Msg = 2034,
	ActivityHYNCheckIn_S2C_Msg = 2035,
	ActivityHYNRecvReward_C2S_Msg = 2036,
	ActivityHYNRecvReward_S2C_Msg = 2037,
	ActivityHYWarmUpInfoNtf = 2038,
	ActivityHYWarmUpCheckIn_C2S_Msg = 2039,
	ActivityHYWarmUpCheckIn_S2C_Msg = 2040,
	ActivityHYWarmUpRecvReward_C2S_Msg = 2041,
	ActivityHYWarmUpRecvReward_S2C_Msg = 2042,
	ActivityMusicOrderCompleteOrder_C2S_Msg = 2043,
	ActivityMusicOrderCompleteOrder_S2C_Msg = 2044,
	ActivityMusicOrderResetOrder_C2S_Msg = 2045,
	ActivityMusicOrderResetOrder_S2C_Msg = 2046,
	ActivityMusicOrderGetTaskReward_C2S_Msg = 2047,
	ActivityMusicOrderGetTaskReward_S2C_Msg = 2048,
	ActivityMusicOrderGetOrderList_C2S_Msg = 2049,
	ActivityMusicOrderGetOrderList_S2C_Msg = 2050,
	ActivityMusicOrderUpdateNtf = 2051,
	GetEntertainmentGuideTaskReward_C2S_Msg = 2052,
	GetEntertainmentGuideTaskReward_S2C_Msg = 2053,
	EntertainmentGuideNtf = 2054,
	FarmBuildingFirstFlag_C2S_Msg = 2055,
	FarmBuildingFirstFlag_S2C_Msg = 2056,
	FarmDoGooderDetail_C2S_Msg = 2057,
	FarmDoGooderDetail_S2C_Msg = 2058,
	FarmGetFarmDynamicInfo_C2S_Msg = 2059,
	FarmGetFarmDynamicInfo_S2C_Msg = 2060,
	FarmSubMonPass_C2S_Msg = 2061,
	FarmSubMonPass_S2C_Msg = 2062,
	FarmTagFriend_C2S_Msg = 2063,
	FarmTagFriend_S2C_Msg = 2064,
	FarmSellAnimal_C2S_Msg = 2065,
	FarmSellAnimal_S2C_Msg = 2066,
	FarmQueryIsWhite_C2S_Msg = 2067,
	FarmQueryIsWhite_S2C_Msg = 2068,
	FarmMonthCardNtf = 2069,
	ChangeCatchphrase_C2S_Msg = 2070,
	ChangeCatchphrase_S2C_Msg = 2071,
	RecommendFriendsCDSet_C2S_Msg = 2072,
	RecommendFriendsCDSet_S2C_Msg = 2073,
	GetFreeFlowInfo_C2S_Msg = 2074,
	GetFreeFlowInfo_S2C_Msg = 2075,
	ReportNewPlayerGuide_C2S_Msg = 2076,
	ReportNewPlayerGuide_S2C_Msg = 2077,
	GetReputationScoreChangeList_C2S_Msg = 2078,
	GetReputationScoreChangeList_S2C_Msg = 2079,
	ReportIAA_C2S_Msg = 2080,
	ReportIAA_S2C_Msg = 2081,
	ReportIAANtf = 2082,
	GetIAAList_C2S_Msg = 2083,
	GetIAAList_S2C_Msg = 2084,
	PlayerReputationScoreFunctionSwitchNtf = 2085,
	IsInAdminWhiteList_C2S_Msg = 2086,
	IsInAdminWhiteList_S2C_Msg = 2087,
	RaffleShareReport_C2S_Msg = 2088,
	RaffleShareReport_S2C_Msg = 2089,
	RecommendFriendsByRecentBattle_C2S_Msg = 2090,
	RecommendFriendsByRecentBattle_S2C_Msg = 2091,
	GetFriendCanRentFashionList_C2S_Msg = 2092,
	GetFriendCanRentFashionList_S2C_Msg = 2093,
	RentFriendFashion_C2S_Msg = 2094,
	RentFriendFashion_S2C_Msg = 2095,
	DeliverHotResResource_C2S_Msg = 2096,
	DeliverHotResResource_S2C_Msg = 2097,
	TeamRecruitQueryByPlayGroup_C2S_Msg = 2098,
	TeamRecruitQueryByPlayGroup_S2C_Msg = 2099,
	TeamRecruitQuickJoinByPlayGroup_C2S_Msg = 2100,
	TeamRecruitQuickJoinByPlayGroup_S2C_Msg = 2101,
	RoomMiniGameInvitationNtf = 2102,
	BatchUpdateUgcRank_C2S_Msg = 2103,
	BatchUpdateUgcRank_S2C_Msg = 2104,
	UgcApplyUniqueId_C2S_Msg = 2105,
	UgcApplyUniqueId_S2C_Msg = 2106,
	UgcGetMatchRecord_C2S_Msg = 2107,
	UgcGetMatchRecord_S2C_Msg = 2108,
	UgcGetMatchRecordDetail_C2S_Msg = 2109,
	UgcGetMatchRecordDetail_S2C_Msg = 2110,
	UgcForwardTest_C2S_Msg = 2111,
	UgcForwardTest_S2C_Msg = 2112,
	XiaoWoModifyMd5_C2S_Msg = 2113,
	XiaoWoModifyMd5_S2C_Msg = 2114,
	XiaoWoEnterPrepare_C2S_Msg = 2115,
	XiaoWoEnterPrepare_S2C_Msg = 2116,
	XiaoWoSetLiuYanMessagePermission_C2S_Msg = 2117,
	XiaoWoSetLiuYanMessagePermission_S2C_Msg = 2118,
	FarmSocialDataNtf = 2119,
	ActivityReceiveRewardsNtf = 2120,
	ActivityGeneral_C2S_Msg = 2121,
	ActivityGeneral_S2C_Msg = 2122,
	ActivityGeneralNtf = 2123,
	ActivityGetActivityByType_C2S_Msg = 2124,
	ActivityGetActivityByType_S2C_Msg = 2125,
	ActivityMusicOrderNoteExchange_C2S_Msg = 2126,
	ActivityMusicOrderNoteExchange_S2C_Msg = 2127,
	LetsGoGetWolfKillRoleInfo_C2S_Msg = 2134,
	LetsGoGetWolfKillRoleInfo_S2C_Msg = 2135,
	MallBuyWolfKillMsg_C2S_Msg = 2136,
	MallBuyWolfKillMsg_S2C_Msg = 2137,
	ClearAllRedDot_C2S_Msg = 2148,
	ClearAllRedDot_S2C_Msg = 2149,
	WolfkillToMasterRewardRecv_C2S_Msg = 2150,
	WolfkillToMasterRewardRecv_S2C_Msg = 2151,
	RoomLbsQuickJoinByPin_C2S_Msg = 2152,
	RoomLbsQuickJoinByPin_S2C_Msg = 2153,
	ChangeSuitRandom_C2S_Msg = 2156,
	ChangeSuitRandom_S2C_Msg = 2157,
	SetDataStoreByFunc_C2S_Msg = 2159,
	SetDataStoreByFunc_S2C_Msg = 2160,
	SetChangeDataStore_C2S_Msg = 2161,
	SetChangeDataStore_S2C_Msg = 2162,
	SetDataStoreByKey_C2S_Msg = 2163,
	SetDataStoreByKey_S2C_Msg = 2164,
	GetDataStoreByKey_C2S_Msg = 2165,
	GetDataStoreByKey_S2C_Msg = 2166,
	BatchSetDataStoreByKey_C2S_Msg = 2167,
	BatchSetDataStoreByKey_S2C_Msg = 2168,
	BatchGetDataStoreByKey_C2S_Msg = 2169,
	BatchGetDataStoreByKey_S2C_Msg = 2170,
	PixuiRedDotReport_C2S_Msg = 2171,
	PixuiRedDotReport_S2C_Msg = 2172,
	PixuiRedDotNtf = 2173,
	ActivityClubChallengeMarkMatchType_C2S_Msg = 2174,
	ActivityClubChallengeMarkMatchType_S2C_Msg = 2175,
	ActivityClubChallengeAward_C2S_Msg = 2176,
	ActivityClubChallengeAward_S2C_Msg = 2177,
	ActivityClubChallengeStarLightInfo_C2S_Msg = 2178,
	ActivityClubChallengeStarLightInfo_S2C_Msg = 2179,
	WolfKillBagDressUpItem_C2S_Msg = 2180,
	WolfKillBagDressUpItem_S2C_Msg = 2181,
	AnimalHandbookGetHandbookInfo_C2S_Msg = 2182,
	AnimalHandbookGetHandbookInfo_S2C_Msg = 2183,
	AnimalHandbookGetColonyState_C2S_Msg = 2184,
	AnimalHandbookGetColonyState_S2C_Msg = 2185,
	AnimalHandbookGetColonyStateNtf = 2186,
	AnimalHandbookStartTrapping_C2S_Msg = 2187,
	AnimalHandbookStartTrapping_S2C_Msg = 2188,
	AnimalHandbookCaptureAnimal_C2S_Msg = 2189,
	AnimalHandbookCaptureAnimal_S2C_Msg = 2190,
	AnimalHandbookGenerateGiveAnimalInfo_C2S_Msg = 2191,
	AnimalHandbookGenerateGiveAnimalInfo_S2C_Msg = 2192,
	ResGet_C2S_Msg = 2193,
	ResGet_S2C_Msg = 2194,
	OpenSecondaryPassword_C2S_Msg = 2195,
	OpenSecondaryPassword_S2C_Msg = 2196,
	ChangeSecondaryPassword_C2S_Msg = 2197,
	ChangeSecondaryPassword_S2C_Msg = 2198,
	CloseSecondaryPassword_C2S_Msg = 2199,
	CloseSecondaryPassword_S2C_Msg = 2200,
	SecondaryPasswordLessOperate_C2S_Msg = 2201,
	SecondaryPasswordLessOperate_S2C_Msg = 2202,
	VerifySecondaryPassword_C2S_Msg = 2203,
	VerifySecondaryPassword_S2C_Msg = 2204,
	ChangeSlotRandomState_C2S_Msg = 2205,
	ChangeSlotRandomState_S2C_Msg = 2206,
	GamePlayPufferInfoNtf = 2207,
	GamePlayResPatchNtf = 2208,
	ChangeUgcLayerId_C2S_Msg = 2210,
	ChangeUgcLayerId_S2C_Msg = 2211,
	ActivityClubChallengeStarLightInfoNtf = 2212,
	UgcGetPlayMapFriends_C2S_Msg = 2213,
	UgcGetPlayMapFriends_S2C_Msg = 2214,
	RemoveUgcLayer_C2S_Msg = 2217,
	RemoveUgcLayer_S2C_Msg = 2218,
	AnimalHandbookGetSpeciesCompleteReward_C2S_Msg = 2219,
	AnimalHandbookGetSpeciesCompleteReward_S2C_Msg = 2220,
	AnimalHandbookGetUltimatePrize_C2S_Msg = 2221,
	AnimalHandbookGetUltimatePrize_S2C_Msg = 2222,
	RemoveWeekSettleBubble_C2S_Msg = 2223,
	RemoveWeekSettleBubble_S2C_Msg = 2224,
	WeekSettleBubbleNtf = 2225,
	StrangerRecommendLabel_C2S_Msg = 2226,
	StrangerRecommendLabel_S2C_Msg = 2227,
	AcceptBPAllRewards_C2S_Msg = 2228,
	AcceptBPAllRewards_S2C_Msg = 2229,
	UpgradeBPPay_C2S_Msg = 2230,
	UpgradeBPPay_S2C_Msg = 2231,
	PurchaseBPLevel_C2S_Msg = 2232,
	PurchaseBPLevel_S2C_Msg = 2233,
	ListBPSummary_C2S_Msg = 2234,
	ListBPSummary_S2C_Msg = 2235,
	ListBPPreview_C2S_Msg = 2236,
	ListBPPreview_S2C_Msg = 2237,
	AnimalHandbookGetGiveAndReceiveHistory_C2S_Msg = 2247,
	AnimalHandbookGetGiveAndReceiveHistory_S2C_Msg = 2248,
	AnimalHandbookAnimalItemGetNtf = 2249,
	BroadcastNoticeNtf = 2250,
	UpdateForesightActivitySubscribe_C2S_Msg = 2251,
	UpdateForesightActivitySubscribe_S2C_Msg = 2252,
	ActivityMarkPlayerInvited_C2S_Msg = 2253,
	ActivityMarkPlayerInvited_S2C_Msg = 2254,
	DeleteGiveRecord_C2S_Msg = 2255,
	DeleteGiveRecord_S2C_Msg = 2256,
	OperateAddIntimateRecommendList_C2S_Msg = 2257,
	OperateAddIntimateRecommendList_S2C_Msg = 2258,
	GetIntimateRelationMotion_C2S_Msg = 2259,
	GetIntimateRelationMotion_S2C_Msg = 2260,
	SetIntimateRelationMotion_C2S_Msg = 2261,
	SetIntimateRelationMotion_S2C_Msg = 2262,
	GetFriendDailyInteractData_C2S_Msg = 2263,
	GetFriendDailyInteractData_S2C_Msg = 2264,
	CupsChangeNtf = 2265,
	CupsItemAddNtf = 2266,
	FarmExchangeCoin_C2S_Msg = 2267,
	FarmExchangeCoin_S2C_Msg = 2268,
	FarmSendGift_C2S_Msg = 2269,
	FarmSendGift_S2C_Msg = 2270,
	FarmPickGift_C2S_Msg = 2271,
	FarmPickGift_S2C_Msg = 2272,
	FarmHandbookAwardGet_C2S_Msg = 2273,
	FarmHandbookAwardGet_S2C_Msg = 2274,
	BPBriefNtf = 2277,
	FetchBPLevelRewards_C2S_Msg = 2278,
	FetchBPLevelRewards_S2C_Msg = 2279,
	AcceptBPRewards_C2S_Msg = 2280,
	AcceptBPRewards_S2C_Msg = 2281,
	UgcAppreciateGetSettings_C2S_Msg = 2282,
	UgcAppreciateGetSettings_S2C_Msg = 2283,
	UgcAppreciateGetMaps_C2S_Msg = 2284,
	UgcAppreciateGetMaps_S2C_Msg = 2285,
	UgcAppreciateMap_C2S_Msg = 2286,
	UgcAppreciateMap_S2C_Msg = 2287,
	WolfkillToMasterReward_C2S_Msg = 2288,
	WolfkillToMasterReward_S2C_Msg = 2289,
	GamePlayInfoSync_C2S_Msg = 2290,
	GamePlayInfoSync_S2C_Msg = 2291,
	FarmBuildingSetSkin_C2S_Msg = 2292,
	FarmBuildingSetSkin_S2C_Msg = 2293,
	FarmQueryFunctionOpenTime_C2S_Msg = 2294,
	FarmQueryFunctionOpenTime_S2C_Msg = 2295,
	FarmFishCardPackOpenNtf = 2296,
	FarmSetFishBowl_C2S_Msg = 2297,
	FarmSetFishBowl_S2C_Msg = 2298,
	FarmFishBowlUnlock_C2S_Msg = 2299,
	FarmFishBowlUnlock_S2C_Msg = 2300,
	DataStoreKvFragmentNtf = 2301,
	GetDataStoreByFunc_C2S_Msg = 2302,
	GetDataStoreByFunc_S2C_Msg = 2303,
	UploadClientLogNtf = 2304,
	FarmStatusRemindNtf = 2305,
	RaffleChooseReward_C2S_Msg = 2306,
	RaffleChooseReward_S2C_Msg = 2307,
	RaffleListReward_C2S_Msg = 2308,
	RaffleListReward_S2C_Msg = 2309,
	GetArenaGameInfo_C2S_Msg = 2310,
	GetArenaGameInfo_S2C_Msg = 2311,
	ArenaSelectFrame_C2S_Msg = 2312,
	ArenaSelectFrame_S2C_Msg = 2313,
	ArenaSelectVoiceStyle_C2S_Msg = 2314,
	ArenaSelectVoiceStyle_S2C_Msg = 2315,
	ArenaSelectEquip_C2S_Msg = 2316,
	ArenaSelectEquip_S2C_Msg = 2317,
	ArenaGetItemsNtf = 2318,
	ArenaHeroInfoNtf = 2319,
	ArenaCardPackResultNtf = 2320,
	ArenaCardPackResultCommit_C2S_Msg = 2321,
	ArenaCardPackResultCommit_S2C_Msg = 2322,
	ArenaBuyHero_C2S_Msg = 2323,
	ArenaBuyHero_S2C_Msg = 2324,
	ArenaSelectCard_C2S_Msg = 2325,
	ArenaSelectCard_S2C_Msg = 2326,
	ArenaAddNewCardNtf = 2327,
	UgcPlayerPublicSyncStart_C2S_Msg = 2328,
	UgcPlayerPublicSyncStart_S2C_Msg = 2329,
	UgcPlayerPublicSyncEnd_C2S_Msg = 2330,
	UgcPlayerPublicSyncEnd_S2C_Msg = 2331,
	UgcPlayerPublicHeartbeat_C2S_Msg = 2332,
	UgcPlayerPublicHeartbeat_S2C_Msg = 2333,
	GetUgcPlayerPublicAllAttrs_C2S_Msg = 2334,
	GetUgcPlayerPublicAllAttrs_S2C_Msg = 2335,
	ModifyUgcPlayerPublicAttr_C2S_Msg = 2336,
	ModifyUgcPlayerPublicAttr_S2C_Msg = 2337,
	GetUgcPlayerPublicArrayAttr_C2S_Msg = 2338,
	GetUgcPlayerPublicArrayAttr_S2C_Msg = 2339,
	AppendUgcPlayerPublicArrayAttr_C2S_Msg = 2340,
	AppendUgcPlayerPublicArrayAttr_S2C_Msg = 2341,
	BatchGetUgcPlayerPublicAttrs_C2S_Msg = 2342,
	BatchGetUgcPlayerPublicAttrs_S2C_Msg = 2343,
	TeamSetBackgroundTheme_C2S_Msg = 2344,
	TeamSetBackgroundTheme_S2C_Msg = 2345,
	ReceiveCupsStageReward_C2S_Msg = 2346,
	ReceiveCupsStageReward_S2C_Msg = 2347,
	AnimalHandbookAnimalItemRemoveNtf = 2348,
	WolfKillRoomTimeLimitStatus_C2S_Msg = 2349,
	WolfKillRoomTimeLimitStatus_S2C_Msg = 2350,
	UgcPlayerPublicAttrModifyNtf = 2351,
	UgcPlayerPublicHeartbeatTimeoutNtf = 2352,
	RecruitOrderRewardReceive_C2S_Msg = 2353,
	RecruitOrderRewardReceive_S2C_Msg = 2354,
	AiNpcFeedBack_C2S_Msg = 2355,
	AiNpcFeedBack_S2C_Msg = 2356,
	LuckyFriendSelectTask_C2S_Msg = 2357,
	LuckyFriendSelectTask_S2C_Msg = 2358,
	LuckyFriendApplyFriend_C2S_Msg = 2359,
	LuckyFriendApplyFriend_S2C_Msg = 2360,
	LuckyFriendOperateFriendTaskApply_C2S_Msg = 2361,
	LuckyFriendOperateFriendTaskApply_S2C_Msg = 2362,
	LuckyFriendNoticeTask_S2C_Msg = 2363,
	LuckyFriendGetTaskReward_C2S_Msg = 2364,
	LuckyFriendGetTaskReward_S2C_Msg = 2365,
	FarmRainBowBuffIconNtf = 2366,
	GameSceneReport_C2S_Msg = 2367,
	GameSceneReport_S2C_Msg = 2368,
	SuperLinearActivityConfig_C2S_Msg = 2369,
	SuperLinearActivityConfig_S2C_Msg = 2370,
	UgcTakeOffApply_C2S_Msg = 2371,
	UgcTakeOffApply_S2C_Msg = 2372,
	HideAndSeekLevelPropData_C2S_Msg = 2373,
	HideAndSeekLevelPropData_S2C_Msg = 2374,
	UnlockIntimateRelationExtraCnt_C2S_Msg = 2375,
	UnlockIntimateRelationExtraCnt_S2C_Msg = 2376,
	LuckyFriendTaskNotice_C2S_Msg = 2377,
	LuckyFriendTaskNotice_S2C_Msg = 2378,
	UgcGetBadgeNtf = 2379,
	BattleRecruitPublish_C2S_Msg = 2380,
	BattleRecruitPublish_S2C_Msg = 2381,
	BattleRecruitUnPublish_C2S_Msg = 2382,
	BattleRecruitUnPublish_S2C_Msg = 2383,
	BattleDirectJoin_C2S_Msg = 2384,
	BattleDirectJoin_S2C_Msg = 2385,
	ArenaHeroUnlockData_C2S_Msg = 2386,
	ArenaHeroUnlockData_S2C_Msg = 2387,
	UgcGetCommunityUnreadCount_C2S_Msg = 2390,
	UgcGetCommunityUnreadCount_S2C_Msg = 2391,
	ActivityLotteryCfg_C2S_Msg = 2392,
	ActivityLotteryCfg_S2C_Msg = 2393,
	GetRechargeLevelInfo_C2S_Msg = 2394,
	GetRechargeLevelInfo_S2C_Msg = 2395,
	GetMonthCardRewardsInfo_C2S_Msg = 2396,
	GetMonthCardRewardsInfo_S2C_Msg = 2397,
	ReturnActivitySetTaskReward_C2S_Msg = 2398,
	ReturnActivitySetTaskReward_S2C_Msg = 2399,
	ReturnActivityMarkCalendar_C2S_Msg = 2400,
	ReturnActivityMarkCalendar_S2C_Msg = 2401,
	LuckyFriendApplyTaskNtf = 2402,
	LuckyFriendTaskStartNtf = 2403,
	ChangeDefaultDress_C2S_Msg = 2404,
	ChangeDefaultDress_S2C_Msg = 2405,
	LuckyFriendTaskNtf = 2406,
	ActivityBuyMidasProduct_C2S_Msg = 2407,
	ActivityBuyMidasProduct_S2C_Msg = 2408,
	ActivityBuyMidasProductNtf = 2409,
	WolfKillAnnouncement_C2S_Msg = 2410,
	WolfKillAnnouncement_S2C_Msg = 2411,
	WolfKillGetFeedbackCount_C2S_Msg = 2412,
	WolfKillGetFeedbackCount_S2C_Msg = 2413,
	WolfKillAddFeedback_C2S_Msg = 2414,
	WolfKillAddFeedback_S2C_Msg = 2415,
	ClubLog_C2S_Msg = 2416,
	ClubLog_S2C_Msg = 2417,
	ResGetV2_C2S_Msg = 2418,
	ResGetV2_S2C_Msg = 2419,
	ResGetV2Ntf = 2420,
	ThemeAdventureRewardNtf = 2421,
	ThemeAdventureOpenMysteryBox_C2S_Msg = 2422,
	ThemeAdventureOpenMysteryBox_S2C_Msg = 2423,
	ThemeActivityGetDailyTaskReward_C2S_Msg = 2424,
	ThemeActivityGetDailyTaskReward_S2C_Msg = 2425,
	RaffleGainFreeDraw_C2S_Msg = 2426,
	RaffleGainFreeDraw_S2C_Msg = 2427,
	ActBookOfFriendsExpense_C2S_Msg = 2428,
	ActBookOfFriendsExpense_S2C_Msg = 2429,
	ActBookOfFriendsExpenseNtf = 2430,
	UgcMatchLobbyDetailGetOne_C2S_Msg = 2431,
	UgcMatchLobbyDetailGetOne_S2C_Msg = 2432,
	BattleRecruitJoin_C2S_Msg = 2433,
	BattleRecruitJoin_S2C_Msg = 2434,
	BIRecommendScenePackage_C2S_Msg = 2435,
	BIRecommendScenePackage_S2C_Msg = 2436,
	WolfKillSeasonRewardRecv_C2S_Msg = 2635,
	WolfKillSeasonRewardRecv_S2C_Msg = 2636,
	AppointmentActivityGetInfo_C2S_Msg = 2637,
	AppointmentActivityGetInfo_S2C_Msg = 2638,
	BurdenReduceTaskOverviewActivityGetInfo_C2S_Msg = 2639,
	BurdenReduceTaskOverviewActivityGetInfo_S2C_Msg = 2640,
	AppointmentActivityMake_C2S_Msg = 2641,
	AppointmentActivityMake_S2C_Msg = 2642,
	ActivityRecommendRecharge_C2S_Msg = 2643,
	ActivityRecommendRecharge_S2C_Msg = 2644,
	ABTestInfoNtf = 2645,
	BattleCommonBroadcast_C2S_Msg = 2646,
	BattleCommonBroadcast_S2C_Msg = 2647,
	BattleBroadcastInfoNtf = 2648,
	QuickCommercialConfInfo_C2S_Msg = 2649,
	QuickCommercialConfInfo_S2C_Msg = 2650,
	QuickCommercialConfInfoNtf = 2651,
	UgcApplyCoverUrl_C2S_Msg = 2652,
	UgcApplyCoverUrl_S2C_Msg = 2653,
	BattleMemberListGet_C2S_Msg = 2654,
	BattleMemberListGet_S2C_Msg = 2655,
	GetFollowerList_C2S_Msg = 2656,
	GetFollowerList_S2C_Msg = 2657,
	RecruitOrderConfirm_C2S_Msg = 2658,
	RecruitOrderConfirm_S2C_Msg = 2659,
	ArenaSelectHeroSkin_C2S_Msg = 2660,
	ArenaSelectHeroSkin_S2C_Msg = 2661,
	RecruitOrderCodeQuery_C2S_Msg = 2668,
	RecruitOrderCodeQuery_S2C_Msg = 2669,
	GamePakInfoReport_C2S_Msg = 2672,
	GamePakInfoReport_S2C_Msg = 2673,
	ArenaForwardCSMsgTest_C2S_Msg = 2676,
	ArenaForwardCSMsgTest_S2C_Msg = 2677,
	IAAForceGuideAppNtf = 2680,
	IAAForceGuideAppReset_C2S_Msg = 2681,
	IAAForceGuideAppReset_S2C_Msg = 2682,
	SetTeamShowBackgroundTheme_C2S_Msg = 2683,
	SetTeamShowBackgroundTheme_S2C_Msg = 2684,
	NewFollowerNtf = 2685,
	HouseCreate_C2S_Msg = 2704,
	HouseCreate_S2C_Msg = 2705,
	HouseEnter_C2S_Msg = 2706,
	HouseEnter_S2C_Msg = 2707,
	HouseExit_C2S_Msg = 2708,
	HouseExit_S2C_Msg = 2709,
	HouseKickNtf = 2710,
	HouseDSInfoNtf = 2711,
	HouseAttrNtf = 2712,
	HouseOwnerAttrNtf = 2713,
	HouseDecorate_C2S_Msg = 2714,
	HouseDecorate_S2C_Msg = 2715,
	HouseExtend_C2S_Msg = 2716,
	HouseExtend_S2C_Msg = 2717,
	HouseForwardToDS_C2S_Msg = 2718,
	HouseForwardToDS_S2C_Msg = 2719,
	HouseFurnitureBuy_C2S_Msg = 2722,
	HouseFurnitureBuy_S2C_Msg = 2723,
	HouseFurnitureSell_C2S_Msg = 2724,
	HouseFurnitureSell_S2C_Msg = 2725,
	HouseCropMachinePutIn_C2S_Msg = 2729,
	HouseCropMachinePutIn_S2C_Msg = 2730,
	HouseCropMachineTakeOut_C2S_Msg = 2731,
	HouseCropMachineTakeOut_S2C_Msg = 2732,
	FarmOpenAquariumMode_C2S_Msg = 2733,
	FarmOpenAquariumMode_S2C_Msg = 2734,
	FarmFetchFunctionOpenTime_C2S_Msg = 2735,
	FarmFetchFunctionOpenTime_S2C_Msg = 2736,
	FarmUnlockAquariumSeat_C2S_Msg = 2737,
	FarmUnlockAquariumSeat_S2C_Msg = 2738,
	FarmSetAquarium_C2S_Msg = 2739,
	FarmSetAquarium_S2C_Msg = 2740,
	FarmGetAquariumBenefit_C2S_Msg = 2741,
	FarmGetAquariumBenefit_S2C_Msg = 2742,
	HouseRecommendStranger_C2S_Msg = 2743,
	HouseRecommendStranger_S2C_Msg = 2744,
	BattlePartnerCoMatchProposal_C2S_Msg = 2745,
	BattlePartnerCoMatchProposal_S2C_Msg = 2746,
	BattlePartnerCoMatchInfoNtf = 2747,
	BattlePartnerCoMatchConfirm_C2S_Msg = 2748,
	BattlePartnerCoMatchConfirm_S2C_Msg = 2749,
	FarmPetEvictDetail_C2S_Msg = 2750,
	FarmPetEvictDetail_S2C_Msg = 2751,
	FarmPetEvict_C2S_Msg = 2752,
	FarmPetEvict_S2C_Msg = 2753,
	TestRaffle_C2S_Msg = 2754,
	TestRaffle_S2C_Msg = 2755,
	ConfirmTestRaffle_C2S_Msg = 2756,
	ConfirmTestRaffle_S2C_Msg = 2757,
	QqCloudGameAuth_C2S_Msg = 2758,
	QqCloudGameAuth_S2C_Msg = 2759,
	QqCloudGameNeedAuthNtf = 2760,
	RechargeLuckyTurntable_C2S_Msg = 2761,
	RechargeLuckyTurntable_S2C_Msg = 2762,
	RechargeLuckyTurntableRewardNtf = 2765,
	JoinCommunityChannel_C2S_Msg = 2766,
	JoinCommunityChannel_S2C_Msg = 2767,
	QuitCommunityChannel_C2S_Msg = 2768,
	QuitCommunityChannel_S2C_Msg = 2769,
	StickCommunityChannel_C2S_Msg = 2770,
	StickCommunityChannel_S2C_Msg = 2771,
	SelectIconCommunityChannel_C2S_Msg = 2772,
	SelectIconCommunityChannel_S2C_Msg = 2773,
	CommunityChannelEntranceNoShow_C2S_Msg = 2774,
	CommunityChannelEntranceNoShow_S2C_Msg = 2775,
	FarmBuildingBatchSell_C2S_Msg = 2782,
	FarmBuildingBatchSell_S2C_Msg = 2783,
	WolfKillNewUserTips_C2S_Msg = 2787,
	WolfKillNewUserTips_S2C_Msg = 2788,
	PlayDetailPageRoomQueryByPlayGroup_C2S_Msg = 2795,
	PlayDetailPageRoomQueryByPlayGroup_S2C_Msg = 2796,
	UgcAchievementConfigEdit_C2S_Msg = 2799,
	UgcAchievementConfigEdit_S2C_Msg = 2800,
	UgcAchievementConfigGet_C2S_Msg = 2801,
	UgcAchievementConfigGet_S2C_Msg = 2802,
	PlayerAlbumPictureGet_C2S_Msg = 2807,
	PlayerAlbumPictureGet_S2C_Msg = 2808,
	PlayerAlbumPictureFormatConvert_C2S_Msg = 2809,
	PlayerAlbumPictureFormatConvert_S2C_Msg = 2810,
	ActivityWeekendIceBrokenInfo_C2S_Msg = 2811,
	ActivityWeekendIceBrokenInfo_S2C_Msg = 2812,
	MallDemandDeliverGoodsNtf = 2813,
	FarmSceneDropPick_C2S_Msg = 2814,
	FarmSceneDropPick_S2C_Msg = 2815,
	FarmPetFeed_C2S_Msg = 2816,
	FarmPetFeed_S2C_Msg = 2817,
	FarmPetHouseKeepingSwitch_C2S_Msg = 2818,
	FarmPetHouseKeepingSwitch_S2C_Msg = 2819,
	FarmPetSetClientCache_C2S_Msg = 2820,
	FarmPetSetClientCache_S2C_Msg = 2821,
	BattleEndSettlement_C2S_Msg = 2822,
	BattleEndSettlement_S2C_Msg = 2823,
	RoomCoMatchReady_C2S_Msg = 2824,
	RoomCoMatchReady_S2C_Msg = 2825,
	HouseGetCurrentRoom_C2S_Msg = 2826,
	HouseGetCurrentRoom_S2C_Msg = 2827,
	EditShowData_C2S_Msg = 2837,
	EditShowData_S2C_Msg = 2838,
	HouseSetClientKV_C2S_Msg = 2839,
	HouseSetClientKV_S2C_Msg = 2840,
	HouseItemInteract_C2S_Msg = 2842,
	HouseItemInteract_S2C_Msg = 2843,
	FarmEndEvent_C2S_Msg = 2848,
	FarmEndEvent_S2C_Msg = 2849,
	AiNpcDressSave_C2S_Msg = 2854,
	AiNpcDressSave_S2C_Msg = 2855,
	ReputationScoreChangeInfoNtf = 2860,
	GetTargetUgcPlayerData_C2S_Msg = 2861,
	GetTargetUgcPlayerData_S2C_Msg = 2862,
	PlayerAlbumPictureOperateNtf = 2863,
	DecomposeItem_C2S_Msg = 2864,
	DecomposeItem_S2C_Msg = 2865,
	ArenaSettings_C2S_Msg = 2872,
	ArenaSettings_S2C_Msg = 2873,
	GetQualifiedActivityMarqueeId_C2S_Msg = 2874,
	GetQualifiedActivityMarqueeId_S2C_Msg = 2875,
	GetShowData_C2S_Msg = 2876,
	GetShowData_S2C_Msg = 2877,
	ActivityPrayerCardInfo_C2S_Msg = 2878,
	ActivityPrayerCardInfo_S2C_Msg = 2879,
	ActivityPrayerCardGive_C2S_Msg = 2880,
	ActivityPrayerCardGive_S2C_Msg = 2881,
	ActivityPrayerCardLook_C2S_Msg = 2882,
	ActivityPrayerCardLook_S2C_Msg = 2883,
	FarmWeatherEffectNtf = 2884,
	FarmWeatherTrigger_C2S_Msg = 2886,
	FarmWeatherTrigger_S2C_Msg = 2887,
	RefreshRaffle_C2S_Msg = 2888,
	RefreshRaffle_S2C_Msg = 2889,
	RefreshRaffleNtf = 2890,
	FarmPetInteract_C2S_Msg = 2893,
	FarmPetInteract_S2C_Msg = 2894,
	FarmPetFertilize_C2S_Msg = 2895,
	FarmPetFertilize_S2C_Msg = 2896,
	PlayerLevelEstimation_C2S_Msg = 2901,
	PlayerLevelEstimation_S2C_Msg = 2902,
	PlayerPictureLike_C2S_Msg = 2903,
	PlayerPictureLike_S2C_Msg = 2904,
	PlayerPictureLikeHis_C2S_Msg = 2905,
	PlayerPictureLikeHis_S2C_Msg = 2906,
	FarmLightningStrikeReport_C2S_Msg = 2907,
	FarmLightningStrikeReport_S2C_Msg = 2908,
	UpdateFavoriteItems_C2S_Msg = 2909,
	UpdateFavoriteItems_S2C_Msg = 2910,
	UgcRecommendSet_C2S_Msg = 2911,
	UgcRecommendSet_S2C_Msg = 2912,
	GetAllInfoForLevelEstimation_C2S_Msg = 2913,
	GetAllInfoForLevelEstimation_S2C_Msg = 2914,
	GetInfoForLevelEstimation_C2S_Msg = 2915,
	GetInfoForLevelEstimation_S2C_Msg = 2916,
	FarmPetGiftReceive_C2S_Msg = 2917,
	FarmPetGiftReceive_S2C_Msg = 2918,
	RechargeActivityList_C2S_Msg = 2924,
	RechargeActivityList_S2C_Msg = 2925,
	FarmResetAquarium_C2S_Msg = 2968,
	FarmResetAquarium_S2C_Msg = 2969,
	StreamUgcAiEditAssistantChat_C2S_Msg = 2970,
	StreamUgcAiEditAssistantChat_S2C_Msg = 2971,
	StreamUgcAiEditAssistantClose_C2S_Msg = 2973,
	StreamUgcAiEditAssistantClose_S2C_Msg = 2974,
	StreamUgcAiEditAssistantCloseResultNtf = 2975,
	StreamUgcAiEditAssistantChatResultNtf = 2977,
	BIRecBanner_C2S_Msg = 2978,
	BIRecBanner_S2C_Msg = 2979,
	Exchange_C2S_Msg = 2980,
	Exchange_S2C_Msg = 2981,
	StreamAiEditAssistantInitResultNtf = 2982,
	StreamUgcAiEditAssistantInit_C2S_Msg = 2983,
	StreamUgcAiEditAssistantInit_S2C_Msg = 2984,
	FarmPetChangeName_C2S_Msg = 2985,
	FarmPetChangeName_S2C_Msg = 2986,
	FarmPetSummon_C2S_Msg = 2987,
	FarmPetSummon_S2C_Msg = 2988,
	FarmFishPoolRecord_C2S_Msg = 2989,
	FarmFishPoolRecord_S2C_Msg = 2990,
	RoomConfirmEnterNtf = 2991,
	RoomConfirmEnter_C2S_Msg = 2992,
	RoomConfirmEnterCancelNtf = 2993,
	RoomStarPInviteEnterGame_C2S_Msg = 2994,
	RoomStarPInviteEnterGame_S2C_Msg = 2995,
	RoomStarPInviteEnterGameAnswer_C2S_Msg = 2996,
	RoomCoMatchInfoNtf = 2997,
	RoomCoMatchPropose_C2S_Msg = 2998,
	RoomCoMatchPropose_S2C_Msg = 2999,
	RoomCoMatchConfirm_C2S_Msg = 3000,
	RoomCoMatchConfirm_S2C_Msg = 3001,
	PlayerAlbumPictureCheck_C2S_Msg = 3002,
	PlayerAlbumPictureCheck_S2C_Msg = 3003,
	FarmPetDressUp_C2S_Msg = 3004,
	FarmPetDressUp_S2C_Msg = 3005,
	CommunityChannelGetHotTopicInfo_C2S_Msg = 3006,
	CommunityChannelGetHotTopicInfo_S2C_Msg = 3007,
	CommunityChannelThumbsUpHotTopic_C2S_Msg = 3008,
	CommunityChannelThumbsUpHotTopic_S2C_Msg = 3009,
	LoginDone_C2S_Msg = 3014,
	LoginDone_S2C_Msg = 3015,
	LuckyRebateActivityGetResult_C2S_Msg = 3016,
	LuckyRebateActivityGetResult_S2C_Msg = 3017,
	FarmEventGetReward_C2S_Msg = 3018,
	FarmEventGetReward_S2C_Msg = 3019,
	UpdateUgcLayerName_C2S_Msg = 3020,
	UpdateUgcLayerName_S2C_Msg = 3021,
	PlayerAlbumPictureLikeOperateNtf = 3028,
	PlayerPictureLikeCount_C2S_Msg = 3029,
	PlayerPictureLikeCount_S2C_Msg = 3030,
	FarmEventSaveData_C2S_Msg = 3031,
	FarmEventSaveData_S2C_Msg = 3032,
	WolfKillFace_C2S_Msg = 3034,
	WolfKillFace_S2C_Msg = 3035,
	FarmSelectPool_C2S_Msg = 3036,
	FarmSelectPool_S2C_Msg = 3037,
	PlayerBatchPictureLikeCountGet_C2S_Msg = 3040,
	PlayerBatchPictureLikeCountGet_S2C_Msg = 3041,
	StarPGuideStepFlow_C2S_Msg = 3044,
	StarPGuideStepFlow_S2C_Msg = 3045,
	FarmTalentLevelUp_C2S_Msg = 3046,
	FarmTalentLevelUp_S2C_Msg = 3047,
	FarmVillagerInteract_C2S_Msg = 3048,
	FarmVillagerInteract_S2C_Msg = 3049,
	FarmVillagerSetClientCache_C2S_Msg = 3050,
	FarmVillagerSetClientCache_S2C_Msg = 3051,
	PlaySwitch_C2S_Msg = 3058,
	PlaySwitch_S2C_Msg = 3059,
	FarmVillagerPresentGift_C2S_Msg = 3060,
	FarmVillagerPresentGift_S2C_Msg = 3061,
	FarmVillagerAcceptGift_C2S_Msg = 3062,
	FarmVillagerAcceptGift_S2C_Msg = 3063,
	GetEntertainmentGuideTaskConfV2_C2S_Msg = 3064,
	GetEntertainmentGuideTaskConfV2_S2C_Msg = 3065,
	FarmGetCropLevelReward_C2S_Msg = 3066,
	FarmGetCropLevelReward_S2C_Msg = 3067,
	StarPSetPreDelTime_C2S_Msg = 3068,
	StarPSetPreDelTime_S2C_Msg = 3069,
	KonanGetDrawResult_C2S_Msg = 3070,
	KonanGetDrawResult_S2C_Msg = 3071,
	FarmTaskGetReward_C2S_Msg = 3072,
	FarmTaskGetReward_S2C_Msg = 3073,
	FarmTaskSubmitItem_C2S_Msg = 3074,
	FarmTaskSubmitItem_S2C_Msg = 3075,
	FarmNPCFishNtfNtf = 3078,
	WerewolfFullReduceActivityCart_C2S_Msg = 3079,
	WerewolfFullReduceActivityCart_S2C_Msg = 3080,
	UpdateWerewolfFullReduceActivityCart_C2S_Msg = 3081,
	UpdateWerewolfFullReduceActivityCart_S2C_Msg = 3082,
	WerewolfFullReduceActivityCartOrder_C2S_Msg = 3083,
	WerewolfFullReduceActivityCartOrder_S2C_Msg = 3084,
	FarmNPCFishNtf = 3085,
	StarPAdventureGetBaseInfo_C2S_Msg = 3086,
	StarPAdventureGetBaseInfo_S2C_Msg = 3087,
	StarPAdventureGetDayReward_C2S_Msg = 3088,
	StarPAdventureGetDayReward_S2C_Msg = 3089,
	StarPStartRL_C2S_Msg = 3090,
	StarPStartRL_S2C_Msg = 3091,
	StarPQueryVisitInfo_C2S_Msg = 3092,
	StarPQueryVisitInfo_S2C_Msg = 3093,
	StarPPlayerDoLottery_C2S_Msg = 3094,
	StarPPlayerDoLottery_S2C_Msg = 3095,
	StarPPlayerGetLotteryInfo_C2S_Msg = 3096,
	StarPPlayerGetLotteryInfo_S2C_Msg = 3097,
	StarPGetFunctionControl_C2S_Msg = 3098,
	StarPGetFunctionControl_S2C_Msg = 3099,
	FarmVillagerSummon_C2S_Msg = 3100,
	FarmVillagerSummon_S2C_Msg = 3101,
	FarmVillagerDeport_C2S_Msg = 3102,
	FarmVillagerDeport_S2C_Msg = 3103,
	FarmTaskStatusNtf = 3104,
	LobbyReportMiniGameResult_C2S_Msg = 3105,
	LobbyReportMiniGameResult_S2C_Msg = 3106,
	FarmTaskConditionChange_C2S_Msg = 3107,
	FarmTaskConditionChange_S2C_Msg = 3108,
	StarPPlayerShopInfo_C2S_Msg = 3109,
	StarPPlayerShopInfo_S2C_Msg = 3110,
	StarPPlayerShopBuy_C2S_Msg = 3111,
	StarPPlayerShopBuy_S2C_Msg = 3112,
	FarmEventCommitItem_C2S_Msg = 3115,
	FarmEventCommitItem_S2C_Msg = 3116,
	ArenaAttrNtf = 3120,
	PlayerSettingShowAnimationButton_C2S_Msg = 3121,
	PlayerSettingShowAnimationButton_S2C_Msg = 3122,
	FarmTaskSetCanTrigger_C2S_Msg = 3123,
	FarmTaskSetCanTrigger_S2C_Msg = 3124,
	HOKSetSettings_C2S_Msg = 3128,
	HOKSetSettings_S2C_Msg = 3129,
	HOKGetSettings_C2S_Msg = 3130,
	HOKGetSettings_S2C_Msg = 3131,
	RoomConfirmEnter_S2C_Msg = 3132,
	RoomStarPInviteEnterGameAnswer_S2C_Msg = 3133,
	RoomStarPInviteEnterGameAnswerNtf = 3134,
	RoomStarPInviteEnterGameNtf = 3135,
	RoomStarPEnterGameNtf = 3136,
	RoomStarPConfirmEnterProgressNtf = 3137,
	RoomSPBroadcastInfoNtf = 3138,
	StarPEnter_C2S_Msg = 3139,
	StarPEnter_S2C_Msg = 3140,
	StarPRoleStatus_C2S_Msg = 3141,
	StarPRoleStatus_S2C_Msg = 3142,
	StarPExit_C2S_Msg = 3143,
	StarPExit_S2C_Msg = 3144,
	StarPCreateOfficial_C2S_Msg = 3145,
	StarPCreateOfficial_S2C_Msg = 3146,
	StarPCreate_C2S_Msg = 3147,
	StarPPrepareMigrateDsNtf_S2C_Msg = 3148,
	StarPAttrNtf = 3149,
	StarPCreate_S2C_Msg = 3150,
	BatchGetStarPInfo_C2S_Msg = 3151,
	BatchGetStarPInfo_S2C_Msg = 3152,
	StarPApplyAdminRole_C2S_Msg = 3153,
	StarPApplyAdminRole_S2C_Msg = 3154,
	StarPDSInfoNtf = 3155,
	StarPApply_C2S_Msg = 3156,
	StarPApply_S2C_Msg = 3157,
	StarPPublish_C2S_Msg = 3158,
	StarPPublish_S2C_Msg = 3159,
	StarPPlayerNtf = 3160,
	StarPOperateApply_C2S_Msg = 3161,
	StarPOperateApply_S2C_Msg = 3162,
	GetStarPInfoList_C2S_Msg = 3163,
	GetStarPInfoList_S2C_Msg = 3164,
	GetNearbyStarPInfoList_C2S_Msg = 3165,
	GetNearbyStarPInfoList_S2C_Msg = 3166,
	GetStarPDetailInfoList_C2S_Msg = 3167,
	GetStarPDetailInfoList_S2C_Msg = 3168,
	StarPApplyNtf = 3169,
	StarPApplyResultNtf = 3170,
	StarPModifyRoom_C2S_Msg = 3171,
	StarPModifyRoom_S2C_Msg = 3172,
	StarPGetMyRole_C2S_Msg = 3173,
	StarPGetMyRole_S2C_Msg = 3174,
	StarPBanEnter_C2S_Msg = 3175,
	StarPBanEnter_S2C_Msg = 3176,
	StarPDelUser_C2S_Msg = 3177,
	StarPDelUser_S2C_Msg = 3178,
	StarPDelRoleByself_C2S_Msg = 3179,
	StarPDelRoleByself_S2C_Msg = 3180,
	StarPAdminTransfer_C2S_Msg = 3181,
	StarPAdminTransfer_S2C_Msg = 3182,
	StarPAblerToApplyAdminNtf = 3183,
	StarPCheckAblerToApplyAdmin_C2S_Msg = 3184,
	StarPCheckAblerToApplyAdmin_S2C_Msg = 3185,
	StarPVoteForDelUser_C2S_Msg = 3186,
	StarPVoteForDelUser_S2C_Msg = 3187,
	StarPInvite_C2S_Msg = 3188,
	StarPInvite_S2C_Msg = 3189,
	StarPRejectInvite_C2S_Msg = 3190,
	StarPRejectInvite_S2C_Msg = 3191,
	StarPInviteNtf = 3192,
	StarPQueryPos_C2S_Msg = 3193,
	StarPQueryPos_S2C_Msg = 3194,
	StarPLoadDsCommonDbInfo_C2S_Msg = 3195,
	StarPLoadDsCommonDbInfo_S2C_Msg = 3196,
	StarPAddplayerResultNtf = 3197,
	StarPGetPlayerWorldInfo_C2S_Msg = 3198,
	StarPGetPlayerWorldInfo_S2C_Msg = 3199,
	StarPChatGroupKeyNtf = 3200,
	DefaultStarPIdChangeNtf = 3201,
	StarPAccountLoadDsCommonDbInfo_C2S_Msg = 3202,
	StarPAccountLoadDsCommonDbInfo_S2C_Msg = 3203,
	StarPExitDsNtf = 3204,
	StarPGetAchieveGlobalData_C2S_Msg = 3205,
	StarPGetAchieveGlobalData_S2C_Msg = 3206,
	GetOfficialStarPRoom_C2S_Msg = 3207,
	GetOfficialStarPRoom_S2C_Msg = 3208,
	TryQuickEnterOfficialStarPRoom_C2S_Msg = 3209,
	TryQuickEnterOfficialStarPRoom_S2C_Msg = 3210,
	StarPGetDressUpInfo_C2S_Msg = 3211,
	StarPGetDressUpInfo_S2C_Msg = 3212,
	StarPCreateDressUp_C2S_Msg = 3213,
	StarPCreateDressUp_S2C_Msg = 3214,
	StarPSaveDressUp_C2S_Msg = 3215,
	StarPSaveDressUp_S2C_Msg = 3216,
	StarPShipAction_C2S_Msg = 3217,
	StarPShipAction_S2C_Msg = 3218,
	BatchGetDsCommonDbAccountLevelData_C2S_Msg = 3219,
	BatchGetDsCommonDbAccountLevelData_S2C_Msg = 3220,
	StarPVisitorGetAvailSp_C2S_Msg = 3221,
	StarPVisitorGetAvailSp_S2C_Msg = 3222,
	StarPSetGuide_C2S_Msg = 3223,
	StarPSetGuide_S2C_Msg = 3224,
	StarPGetGuide_C2S_Msg = 3225,
	StarPGetGuide_S2C_Msg = 3226,
	RoomTest_C2S_Msg = 3227,
	RoomTest_S2C_Msg = 3228,
	QueryQualifyTypesSeasonSettlementMailReward_C2S_Msg = 3230,
	QueryQualifyTypesSeasonSettlementMailReward_S2C_Msg = 3231,
	FarmSetConnectCropNewVersion_C2S_Msg = 3232,
	FarmSetConnectCropNewVersion_S2C_Msg = 3233,
	TradingCardGetCollectionList_C2S_Msg = 3234,
	TradingCardGetCollectionList_S2C_Msg = 3235,
	TradingCardGetCollectionDetailInfo_C2S_Msg = 3236,
	TradingCardGetCollectionDetailInfo_S2C_Msg = 3237,
	TradingCardGetNtf = 3238,
	TradingCardGetTradeHistory_C2S_Msg = 3239,
	TradingCardGetTradeHistory_S2C_Msg = 3240,
	TradingCardCreateRequireTrade_C2S_Msg = 3241,
	TradingCardCreateRequireTrade_S2C_Msg = 3242,
	TradingCardCompleteRequireTrade_C2S_Msg = 3243,
	TradingCardCompleteRequireTrade_S2C_Msg = 3244,
	TradingCardCreateGiveTrade_C2S_Msg = 3245,
	TradingCardCreateGiveTrade_S2C_Msg = 3246,
	TradingCardCompleteGiveTrade_C2S_Msg = 3247,
	TradingCardCompleteGiveTrade_S2C_Msg = 3248,
	TradingCardCreateExchangeTrade_C2S_Msg = 3249,
	TradingCardCreateExchangeTrade_S2C_Msg = 3250,
	TradingCardCompleteExchangeTrade_C2S_Msg = 3251,
	TradingCardCompleteExchangeTrade_S2C_Msg = 3252,
	ArenaGetSnap_C2S_Msg = 3255,
	ArenaGetSnap_S2C_Msg = 3256,
	ArenaSelectShowSkin_C2S_Msg = 3259,
	ArenaSelectShowSkin_S2C_Msg = 3260,
	ArenaSelectHeadPic_C2S_Msg = 3261,
	ArenaSelectHeadPic_S2C_Msg = 3262,
	TradingCardExchangeWildCard_C2S_Msg = 3263,
	TradingCardExchangeWildCard_S2C_Msg = 3264,
	FarmTaskAddStealCropForRedFox_C2S_Msg = 3265,
	FarmTaskAddStealCropForRedFox_S2C_Msg = 3266,
	DeviceHasNoPermissionNtf = 3269,
	DeviceIdCheckErrorNtf = 3270,
	IPHasChangedNtf = 3271,
	IPBannedNtf = 3272,
	StarPGetAllRolePetInfo_C2S_Msg = 3273,
	StarPGetAllRolePetInfo_S2C_Msg = 3274,
	StarPGetPetFormation_C2S_Msg = 3275,
	StarPGetPetFormation_S2C_Msg = 3276,
	StarPSetPetFormation_C2S_Msg = 3277,
	StarPSetPetFormation_S2C_Msg = 3278,
	StarPShockRewardList_C2S_Msg = 3279,
	StarPShockRewardList_S2C_Msg = 3280,
	StarPGetShockReward_C2S_Msg = 3281,
	StarPGetShockReward_S2C_Msg = 3282,
	StarPSeasonRewardList_C2S_Msg = 3283,
	StarPSeasonRewardList_S2C_Msg = 3284,
	StarPGetSeasonReward_C2S_Msg = 3285,
	StarPGetSeasonReward_S2C_Msg = 3286,
	StarPPvpGetOverallInfo_C2S_Msg = 3287,
	StarPPvpGetOverallInfo_S2C_Msg = 3288,
	StarPPvpMatchPenaltyStatusNtf = 3289,
	StarPGetOrnamentAndAmuletInfo_C2S_Msg = 3290,
	StarPGetOrnamentAndAmuletInfo_S2C_Msg = 3291,
	ArenaGetCommonlyUsedHero_C2S_Msg = 3292,
	ArenaGetCommonlyUsedHero_S2C_Msg = 3293,
	UgcOpenStartWorld_C2S_Msg = 3294,
	UgcOpenStartWorld_S2C_Msg = 3295,
	RejectTestRaffle_C2S_Msg = 3296,
	RejectTestRaffle_S2C_Msg = 3297,
	MallGetThemeShopCommodity_C2S_Msg = 3300,
	MallGetThemeShopCommodity_S2C_Msg = 3301,
	MallGetThemeShopDiscount_C2S_Msg = 3302,
	MallGetThemeShopDiscount_S2C_Msg = 3303,
	SetIntimateOnlineNoticeBox_C2S_Msg = 3304,
	SetIntimateOnlineNoticeBox_S2C_Msg = 3305,
	PlayerAntiCheatInfoInitNtf = 3306,
	PlayerAntiCheatInfoReport_C2S_Msg = 3307,
	PlayerAntiCheatInfoReport_S2C_Msg = 3308,
	PlayerAntiCheatInfoToClientNtf = 3309,
	FarmFishMerge_C2S_Msg = 3311,
	FarmFishMerge_S2C_Msg = 3312,
	CookCreate_C2S_Msg = 3314,
	CookCreate_S2C_Msg = 3315,
	CookEnter_C2S_Msg = 3316,
	CookEnter_S2C_Msg = 3317,
	CookExit_C2S_Msg = 3318,
	CookExit_S2C_Msg = 3319,
	CookKickNtf = 3320,
	CookDSInfoNtf = 3321,
	CookAttrNtf = 3322,
	CookOwnerAttrNtf = 3323,
	CookForwardToDS_C2S_Msg = 3324,
	CookForwardToDS_S2C_Msg = 3325,
	CookGetOfflineIncome_C2S_Msg = 3326,
	CookGetOfflineIncome_S2C_Msg = 3327,
	CookDoSettlement_C2S_Msg = 3328,
	CookDoSettlement_S2C_Msg = 3329,
	CookHireFriend_C2S_Msg = 3330,
	CookHireFriend_S2C_Msg = 3331,
	CookFireFriend_C2S_Msg = 3332,
	CookFireFriend_S2C_Msg = 3333,
	CookPoachFriend_C2S_Msg = 3334,
	CookPoachFriend_S2C_Msg = 3335,
	CookOpen_C2S_Msg = 3338,
	CookOpen_S2C_Msg = 3339,
	CookClose_C2S_Msg = 3340,
	CookClose_S2C_Msg = 3341,
	RafflePickInventory_C2S_Msg = 3344,
	RafflePickInventory_S2C_Msg = 3345,
	RewardRetrievalInfo_C2S_Msg = 3346,
	RewardRetrievalInfo_S2C_Msg = 3347,
	RewardRetrievalRetrieve_C2S_Msg = 3348,
	RewardRetrievalRetrieve_S2C_Msg = 3349,
	RewardRetrievalUpdateNtf = 3350,
	RewardRetrievalClearRedDot_C2S_Msg = 3351,
	RewardRetrievalClearRedDot_S2C_Msg = 3352,
	ChangeMallWishList_C2S_Msg = 3353,
	ChangeMallWishList_S2C_Msg = 3354,
	FollowOtherMallWishList_C2S_Msg = 3355,
	FollowOtherMallWishList_S2C_Msg = 3356,
	WolfKillGetComeBackInfo_C2S_Msg = 3357,
	WolfKillGetComeBackInfo_S2C_Msg = 3358,
	WolfKillGetComeBackReward_C2S_Msg = 3359,
	WolfKillGetComeBackReward_S2C_Msg = 3360,
	FittingItemStatusChange_C2S_Msg = 3361,
	FittingItemStatusChange_S2C_Msg = 3362,
	TradingCardShareToChat_C2S_Msg = 3363,
	TradingCardShareToChat_S2C_Msg = 3364,
	GuideConfigDiscoverStatusQuery_C2S_Msg = 3433,
	GuideConfigDiscoverStatusQuery_S2C_Msg = 3434,
	GuideConfigDiscoverStatusUpdate_S2C_Msg = 3435,
	GuideConfigDiscoverStatusUpdate_C2S_Msg = 3436,
	StarPPVPGetDailyReward_C2S_Msg = 3437,
	StarPPVPGetDailyReward_S2C_Msg = 3438,
	FarmCommitItemToCloud_C2S_Msg = 3439,
	FarmCommitItemToCloud_S2C_Msg = 3440,
	TradingCardGetTradeInfoListCache_C2S_Msg = 3441,
	TradingCardGetTradeInfoListCache_S2C_Msg = 3442,
	TradingCardGetTradeInfo_C2S_Msg = 3443,
	TradingCardGetTradeInfo_S2C_Msg = 3444,
	GeneralReadDotNtf = 3449,
	ClickGeneralRedDot_C2S_Msg = 3450,
	ClickGeneralRedDot_S2C_Msg = 3451,
	CookSendComment_C2S_Msg = 3468,
	CookSendComment_S2C_Msg = 3469,
	TradingCardClearRedDot_C2S_Msg = 3470,
	TradingCardClearRedDot_S2C_Msg = 3471,
	TradingCardRedDotChangeNtf = 3472,
	CustomRoomList_C2S_Msg = 3473,
	CustomRoomList_S2C_Msg = 3474,
	CustomRoomRoundLevelList_C2S_Msg = 3475,
	CustomRoomRoundLevelList_S2C_Msg = 3476,
	CookDeleteComment_C2S_Msg = 3477,
	CookDeleteComment_S2C_Msg = 3478,
	CookGetComment_C2S_Msg = 3479,
	CookGetComment_S2C_Msg = 3480,
	CookReceiveNewCommentNtf = 3481,
	CookVisitantPrebook_C2S_Msg = 3482,
	CookVisitantPrebook_S2C_Msg = 3483,
	CookVisitantSteal_C2S_Msg = 3491,
	CookVisitantSteal_S2C_Msg = 3492,
	SetMood_C2S_Msg = 3493,
	SetMood_S2C_Msg = 3494,
	TradingCardDrawCycleCupsReward_C2S_Msg = 3495,
	TradingCardDrawCycleCupsReward_S2C_Msg = 3496,
	AiNpcLicense_C2S_Msg = 3497,
	AiNpcLicense_S2C_Msg = 3498,
	PlayerEnterCocGameScene_C2S_Msg = 3499,
	PlayerEnterCocGameScene_S2C_Msg = 3500,
	PlayerExitCocGameScene_C2S_Msg = 3501,
	PlayerExitCocGameScene_S2C_Msg = 3502,
	CocBuildBuilding_C2S_Msg = 3503,
	CocBuildBuilding_S2C_Msg = 3504,
	CocUpgradeBuilding_C2S_Msg = 3505,
	CocUpgradeBuilding_S2C_Msg = 3506,
	CocFastFinishBuilding_C2S_Msg = 3507,
	CocFastFinishBuilding_S2C_Msg = 3508,
	CocCancelBuildBuilding_C2S_Msg = 3509,
	CocCancelBuildBuilding_S2C_Msg = 3510,
	CocCancelUpgradeBuilding_C2S_Msg = 3511,
	CocCancelUpgradeBuilding_S2C_Msg = 3512,
	CocDemolishBuilding_C2S_Msg = 3513,
	CocDemolishBuilding_S2C_Msg = 3514,
	CocMoveBuilding_C2S_Msg = 3515,
	CocMoveBuilding_S2C_Msg = 3516,
	CocStartMatch_C2S_Msg = 3517,
	CocStartMatch_S2C_Msg = 3518,
	CocStartFight_C2S_Msg = 3519,
	CocStartFight_S2C_Msg = 3520,
	CocFightingDeltaInfo_C2S_Msg = 3521,
	CocFightingDeltaInfo_S2C_Msg = 3522,
	CocFightSettle_C2S_Msg = 3523,
	CocFightSettle_S2C_Msg = 3524,
	CocQueryMatchPlayerInfo_C2S_Msg = 3525,
	CocQueryMatchPlayerInfo_S2C_Msg = 3526,
	CocUpgradeScience_C2S_Msg = 3527,
	CocUpgradeScience_S2C_Msg = 3528,
	CocCompleteScience_C2S_Msg = 3529,
	CocCompleteScience_S2C_Msg = 3530,
	CocSoldierTraining_C2S_Msg = 3531,
	CocSoldierTraining_S2C_Msg = 3532,
	CocSoldierModify_C2S_Msg = 3533,
	CocSoldierModify_S2C_Msg = 3534,
	CocSoldierPreset_C2S_Msg = 3535,
	CocSoldierPreset_S2C_Msg = 3536,
	CocSoldierUsePreset_C2S_Msg = 3537,
	CocSoldierUsePreset_S2C_Msg = 3538,
	CocCollectResource_C2S_Msg = 3539,
	CocCollectResource_S2C_Msg = 3540,
	CocDuanWeiReward_C2S_Msg = 3541,
	CocDuanWeiReward_S2C_Msg = 3542,
	CocTreasureBoxOperate_C2S_Msg = 3543,
	CocTreasureBoxOperate_S2C_Msg = 3544,
	CocUpResourceEfficiency_C2S_Msg = 3545,
	CocUpResourceEfficiency_S2C_Msg = 3546,
	CocMarkDialog_C2S_Msg = 3547,
	CocMarkDialog_S2C_Msg = 3548,
	CocUnlockMapPrimaryRegion_C2S_Msg = 3555,
	CocUnlockMapPrimaryRegion_S2C_Msg = 3556,
	CocUnlockMapMinorRegion_C2S_Msg = 3557,
	CocUnlockMapMinorRegion_S2C_Msg = 3558,
	CocGetXingbaoCaptureReward_C2S_Msg = 3559,
	CocGetXingbaoCaptureReward_S2C_Msg = 3560,
	CocPlayerNumPropDetailsNtf = 3561,
	CocReleaseXingbao_C2S_Msg = 3562,
	CocReleaseXingbao_S2C_Msg = 3563,
	CocGetOfflineEventOnLoadComplete_C2S_Msg = 3564,
	CocGetOfflineEventOnLoadComplete_S2C_Msg = 3565,
	CocUnlockVillager_C2S_Msg = 3566,
	CocUnlockVillager_S2C_Msg = 3567,
	CocAppointOfficialPosForVillager_C2S_Msg = 3568,
	CocAppointOfficialPosForVillager_S2C_Msg = 3569,
	CocStartVillagerDialogue_C2S_Msg = 3570,
	CocStartVillagerDialogue_S2C_Msg = 3571,
	CocMarkVillagerDialogueFinishKeyStep_C2S_Msg = 3572,
	CocMarkVillagerDialogueFinishKeyStep_S2C_Msg = 3573,
	CocCompleteVillagerDialogue_C2S_Msg = 3574,
	CocCompleteVillagerDialogue_S2C_Msg = 3575,
	CocVillagerChangeDress_C2S_Msg = 3576,
	CocVillagerChangeDress_S2C_Msg = 3577,
	CocModifyVillagerName_C2S_Msg = 3578,
	CocModifyVillagerName_S2C_Msg = 3579,
	CocAcceptVillagerDailyFavorReward_C2S_Msg = 3580,
	CocAcceptVillagerDailyFavorReward_S2C_Msg = 3581,
	CocEnterOtherScene_C2S_Msg = 3582,
	CocEnterOtherScene_S2C_Msg = 3583,
	CocBuyCocMonthCard_C2S_Msg = 3584,
	CocBuyCocMonthCard_S2C_Msg = 3585,
	CocMonthCardDailyReward_C2S_Msg = 3586,
	CocMonthCardDailyReward_S2C_Msg = 3587,
	CocGetTaskReward_C2S_Msg = 3588,
	CocGetTaskReward_S2C_Msg = 3589,
	CocRewardNtf = 3590,
	CocBatchUpgradeBuilding_C2S_Msg = 3591,
	CocBatchUpgradeBuilding_S2C_Msg = 3592,
	CocStartFightBattle_C2S_Msg = 3595,
	CocStartFightBattle_S2C_Msg = 3596,
	CocSettleBattleGame_C2S_Msg = 3597,
	CocSettleBattleGame_S2C_Msg = 3598,
	MarkCocDefenseMailHasFoughtBack_C2S_Msg = 3599,
	MarkCocDefenseMailHasFoughtBack_S2C_Msg = 3600,
	AppearanceRoadGetGatherAward_C2S_Msg = 3607,
	AppearanceRoadGetGatherAward_S2C_Msg = 3608,
	AppearanceRoadGetLvAward_C2S_Msg = 3609,
	AppearanceRoadGetLvAward_S2C_Msg = 3610,
	StarPVisitSameWorld_C2S_Msg = 3611,
	StarPVisitSameWorld_S2C_Msg = 3612,
	AiNpcCheckText_C2S_Msg = 3613,
	AiNpcCheckText_S2C_Msg = 3614,
	AiNpcSubmitTempPalProfile_C2S_Msg = 3615,
	AiNpcSubmitTempPalProfile_S2C_Msg = 3616,
	AiNpcSubmitPlayerProfile_C2S_Msg = 3617,
	AiNpcSubmitPlayerProfile_S2C_Msg = 3618,
	AiNpcGenCustomPal_C2S_Msg = 3619,
	AiNpcGenCustomPal_S2C_Msg = 3620,
	AiNpcFinishGuide_C2S_Msg = 3621,
	AiNpcFinishGuide_S2C_Msg = 3622,
	WolfKillTreasureUse_C2S_Msg = 3623,
	WolfKillTreasureUse_S2C_Msg = 3624,
	WolfKillVocation_C2S_Msg = 3625,
	WolfKillVocation_S2C_Msg = 3626,
	CocGetFriendGift_C2S_Msg = 3627,
	CocGetFriendGift_S2C_Msg = 3628,
	CocSendFriendDonation_C2S_Msg = 3629,
	CocSendFriendDonation_S2C_Msg = 3630,
	CocReceiveFriendDonationNtf = 3631,
	CocAllocBattleGame_C2S_Msg = 3632,
	CocAllocBattleGame_S2C_Msg = 3633,
	CocAppointFriendVillager_C2S_Msg = 3634,
	CocAppointFriendVillager_S2C_Msg = 3635,
	CocVisitFriendHome_C2S_Msg = 3636,
	CocVisitFriendHome_S2C_Msg = 3637,
	FarmVillagerGetFavorExp_C2S_Msg = 3638,
	FarmVillagerGetFavorExp_S2C_Msg = 3639,
	GiveMailAttachmentsNtf = 3640,
	CocPrisonAssign_C2S_Msg = 3641,
	CocPrisonAssign_S2C_Msg = 3642,
	AnniversaryMobaDrawReward_C2S_Msg = 3643,
	AnniversaryMobaGetReward_S2C_Msg = 3644,
	AnniversaryMobaGetReward_C2S_Msg = 3645,
	AnniversaryMobaDrawReward_S2C_Msg = 3646,
	WolfKillTreasureRead_C2S_Msg = 3649,
	WolfKillTreasureRead_S2C_Msg = 3650,
	ArenaTipRewardDetailsNtf = 3651,
	CocDonationAccelerate_C2S_Msg = 3652,
	CocDonationAccelerate_S2C_Msg = 3653,
	CocSetBattleBuildingAttackMode_C2S_Msg = 3654,
	CocSetBattleBuildingAttackMode_S2C_Msg = 3655,
	AiNpcMoodChangeNtf = 3656,
	CocRemoveAfterReleaseXingBao_C2S_Msg = 3661,
	CocRemoveAfterReleaseXingBao_S2C_Msg = 3662,
	WolfKillTreasureLevelRead_C2S_Msg = 3663,
	WolfKillTreasureLevelRead_S2C_Msg = 3664,
	WolfKillTreasureRefreshLevel_C2S_Msg = 3669,
	WolfKillTreasureRefreshLevel_S2C_Msg = 3670,
	AnniversaryMobaGetRewardTimes_C2S_Msg = 3671,
	AnniversaryMobaGetRewardTimes_S2C_Msg = 3672,
	StarPCreateRole_C2S_Msg = 3673,
	StarPCreateRole_S2C_Msg = 3674,
	StarPDeleteRole_C2S_Msg = 3675,
	StarPDeleteRole_S2C_Msg = 3676,
	StarPGetRoleList_C2S_Msg = 3677,
	StarPGetRoleList_S2C_Msg = 3678,
	ChatBatchSend_C2S_Msg = 3679,
	ChatBatchSend_S2C_Msg = 3680,
	FarmVillagerSwitchScene_C2S_Msg = 3682,
	FarmVillagerSwitchScene_S2C_Msg = 3683,
	ArenaEnterPrepareInterface_C2S_Msg = 3686,
	ArenaEnterPrepareInterface_S2C_Msg = 3687,
	FarmEnterHotSpring_C2S_Msg = 3688,
	FarmEnterHotSpring_S2C_Msg = 3689,
	FarmLeaveHotSpring_C2S_Msg = 3690,
	FarmLeaveHotSpring_S2C_Msg = 3691,
	FarmGetHotSpringBuff_C2S_Msg = 3692,
	FarmGetHotSpringBuff_S2C_Msg = 3693,
	FarmChangeSignature_C2S_Msg = 3696,
	FarmChangeSignature_S2C_Msg = 3697,
	MobaSquadDrawRedPacketDraw_C2S_Msg = 3698,
	MobaSquadDrawRedPacketDraw_S2C_Msg = 3699,
	StreamDsGeneral_C2S_Msg = 3702,
	StreamDsGeneral_S2C_Msg = 3703,
	ActivityNewYearSign_C2S_Msg = 3704,
	ActivityNewYearSign_S2C_Msg = 3705,
	LobbyListFind_C2S_Msg = 3706,
	LobbyListFind_S2C_Msg = 3707,
	GetDailyBattleOrnamentationInfo_C2S_Msg = 3708,
	GetDailyBattleOrnamentationInfo_S2C_Msg = 3709,
	UpdatedDailyBattleOrnamentationInfo_C2S_Msg = 3710,
	UpdatedDailyBattleOrnamentationInfo_S2C_Msg = 3711,
	FarmQueryNpcCD_C2S_Msg = 3714,
	FarmQueryNpcCD_S2C_Msg = 3715,
	FarmInvokeNpc_C2S_Msg = 3716,
	FarmInvokeNpc_S2C_Msg = 3717,
	FarmPrayToGodFigure_C2S_Msg = 3718,
	FarmPrayToGodFigure_S2C_Msg = 3719,
	FarmUpgradeGodFigure_C2S_Msg = 3720,
	FarmUpgradeGodFigure_S2C_Msg = 3721,
	UgcInitiateEvaluation_C2S_Msg = 3722,
	UgcInitiateEvaluation_S2C_Msg = 3723,
	AmsRewardInfoNtf = 3727,
	MallGiftCardChangeNtf = 3729,
	FarmMagicUse_C2S_Msg = 3730,
	FarmMagicUse_S2C_Msg = 3731,
	FarmMagicShortenHarvestTimeNtf = 3732,
	FarmMagicEquip_C2S_Msg = 3733,
	FarmMagicEquip_S2C_Msg = 3734,
	LobbyMatchResultNtf = 3735,
	LobbyMatchCancelResultNtf = 3736,
	FarmSetVisitorListTag_C2S_Msg = 3739,
	FarmSetVisitorListTag_S2C_Msg = 3740,
	HouseSetVisitorListTag_C2S_Msg = 3741,
	HouseSetVisitorListTag_S2C_Msg = 3742,
	XiaoWoSetVisitorListTag_C2S_Msg = 3743,
	XiaoWoSetVisitorListTag_S2C_Msg = 3744,
	StarPSetChatPetIconID_C2S_Msg = 3745,
	StarPSetChatPetIconID_S2C_Msg = 3746,
	ReadOtherMallWishList_C2S_Msg = 3749,
	ReadOtherMallWishList_S2C_Msg = 3750,
	StarPSetChatCupIconID_C2S_Msg = 3751,
	StarPSetChatCupIconID_S2C_Msg = 3752,
	ClearMonthCardExpiredRedPoint_C2S_Msg = 3755,
	ClearMonthCardExpiredRedPoint_S2C_Msg = 3756,
	MonthCardRedPointNtf = 3757,
	CookLevelUp_C2S_Msg = 3758,
	CookLevelUp_S2C_Msg = 3759,
	CookSendCommentPhoto_C2S_Msg = 3760,
	CookSendCommentPhoto_S2C_Msg = 3761,
	RpcTestConfigSvr_C2S_Msg = 3770,
	RpcTestConfigSvr_S2C_Msg = 3771,
	FindPartnerAnswerQuestion_C2S_Msg = 3772,
	FindPartnerAnswerQuestion_S2C_Msg = 3773,
	FindPartnerGetQuestions_C2S_Msg = 3774,
	FindPartnerGetQuestions_S2C_Msg = 3775,
	UgcMapExtraConfigEdit_C2S_Msg = 3778,
	UgcMapExtraConfigEdit_S2C_Msg = 3779,
	UgcMapExtraConfigGet_C2S_Msg = 3780,
	UgcMapExtraConfigGet_S2C_Msg = 3781,
	UpdatedReadyBattleOrnamentationBagInfo_C2S_Msg = 3782,
	UpdatedReadyBattleOrnamentationBagInfo_S2C_Msg = 3783,
	ActivitySquadAcquireReward_C2S_Msg = 3787,
	ActivitySquadAcquireReward_S2C_Msg = 3788,
	ActivitySquadOpenFinalTreasure_C2S_Msg = 3789,
	ActivitySquadOpenFinalTreasure_S2C_Msg = 3790,
	GetPlayPufferInfo_C2S_Msg = 3791,
	GetPlayPufferInfo_S2C_Msg = 3792,
	GetSeasonReviewInfo_C2S_Msg = 3793,
	GetSeasonReviewInfo_S2C_Msg = 3794,
	MallRedPointClear_C2S_Msg = 3795,
	MallRedPointClear_S2C_Msg = 3796,
	SummonFortuneSlip_C2S_Msg = 3797,
	SummonFortuneSlip_S2C_Msg = 3798,
	ReceiveCollectionReward_C2S_Msg = 3799,
	ReceiveCollectionReward_S2C_Msg = 3800,
	GetLuckyStarFriendList_C2S_Msg = 3801,
	GetLuckyStarFriendList_S2C_Msg = 3802,
	GetLuckyStarAssistRecordList_C2S_Msg = 3803,
	GetLuckyStarAssistRecordList_S2C_Msg = 3804,
	LuckyStarRequestNormalAssist_C2S_Msg = 3805,
	LuckyStarRequestNormalAssist_S2C_Msg = 3806,
	LuckyStarNormalAssist_C2S_Msg = 3807,
	LuckyStarNormalAssist_S2C_Msg = 3808,
	LuckyStarRequestSpecifyAssist_C2S_Msg = 3809,
	LuckyStarRequestSpecifyAssist_S2C_Msg = 3810,
	LuckyStarSpecifyAssist_C2S_Msg = 3811,
	LuckyStarSpecifyAssist_S2C_Msg = 3812,
	LuckyStarGiveSlip_C2S_Msg = 3813,
	LuckyStarGiveSlip_S2C_Msg = 3814,
	LuckyStarRequestSlip_C2S_Msg = 3815,
	LuckyStarRequestSlip_S2C_Msg = 3816,
	ScoreGuidePopUpNtf = 3817,
	ScoreGuidePopUpProcess_C2S_Msg = 3818,
	ScoreGuidePopUpProcess_S2C_Msg = 3819,
	TradingCardExchangeReward_C2S_Msg = 3820,
	TradingCardExchangeReward_S2C_Msg = 3821,
	WolfKillTreasureData_C2S_Msg = 3822,
	WolfKillTreasureData_S2C_Msg = 3823,
	BirthdayInfo_C2S_Msg = 3824,
	BirthdayInfo_S2C_Msg = 3825,
	BirthdayChange_C2S_Msg = 3826,
	BirthdayChange_S2C_Msg = 3827,
	BirthdayBlessingRecord_C2S_Msg = 3828,
	BirthdayBlessingRecord_S2C_Msg = 3829,
	BirthdayBlessingRecordRedDotNtf = 3830,
	BirthdayBlessingRecordClearRedDot_C2S_Msg = 3831,
	BirthdayBlessingRecordClearRedDot_S2C_Msg = 3832,
	BirthdayCardSend_C2S_Msg = 3833,
	BirthdayCardSend_S2C_Msg = 3834,
	BirthdayBlessingSend_C2S_Msg = 3835,
	BirthdayBlessingSend_S2C_Msg = 3836,
	BirthdayCardItemAddNtf = 3837,
	BirthdayCardItemDelete_C2S_Msg = 3838,
	BirthdayCardItemDelete_S2C_Msg = 3839,
	BirthdayOfficialWelfareGet_C2S_Msg = 3840,
	BirthdayOfficialWelfareGet_S2C_Msg = 3841,
	GMRespDataNtf = 3842,
	RafflePurchaseSubItem_C2S_Msg = 3843,
	RafflePurchaseSubItem_S2C_Msg = 3844,
	TradingCardDrawNoviceReward_C2S_Msg = 3845,
	TradingCardDrawNoviceReward_S2C_Msg = 3846,
	GetLobbyMatchHistoryList_C2S_Msg = 3847,
	GetLobbyMatchHistoryList_S2C_Msg = 3848,
	RecruitOrderRaffleConfirm_C2S_Msg = 3849,
	RecruitOrderRaffleConfirm_S2C_Msg = 3850,
	RecruitOrderRaffleAddressConfirm_C2S_Msg = 3851,
	RecruitOrderRaffleAddressConfirm_S2C_Msg = 3852,
	ShareGiftReward_C2S_Msg = 3853,
	ShareGiftReward_S2C_Msg = 3854,
	ShareHistoryGet_C2S_Msg = 3855,
	ShareHistoryGet_S2C_Msg = 3856,
	ActivitySquadKickMember_C2S_Msg = 3859,
	ActivitySquadKickMember_S2C_Msg = 3860,
	BatchGetChatShareGift_C2S_Msg = 3861,
	BatchGetChatShareGift_S2C_Msg = 3862,
	StarPRoleSelect_C2S_Msg = 3863,
	StarPRoleSelect_S2C_Msg = 3864,
	TradingCardShowNoviceReward_C2S_Msg = 3867,
	TradingCardShowNoviceReward_S2C_Msg = 3868,
	BPExpChangeNtf = 3869,
	CookSetClientKV_C2S_Msg = 3870,
	CookSetClientKV_S2C_Msg = 3871,
	ActivitySquadFarmWateringTree_C2S_Msg = 3872,
	ActivitySquadFarmWateringTree_S2C_Msg = 3873,
	TradingCardGetActivity_C2S_Msg = 3874,
	TradingCardGetActivity_S2C_Msg = 3875,
	ArenaGetHeroBattleStatistic_C2S_Msg = 3876,
	ArenaGetHeroBattleStatistic_S2C_Msg = 3877,
	BirthdayRemindNtf = 3878,
	FarmHotSpringTickNtf = 3881,
	SetSprayPaint_C2S_Msg = 3882,
	SetSprayPaint_S2C_Msg = 3883,
	UseSprayPaint_C2S_Msg = 3884,
	UseSprayPaint_S2C_Msg = 3885,
	CookEmployeeOp_C2S_Msg = 3886,
	CookEmployeeOp_S2C_Msg = 3887,
	BirthdayCardChangeNtf = 3889,
	QueryLuckStarCount_C2S_Msg = 3896,
	QueryLuckStarCount_S2C_Msg = 3897,
	StarPRecruitQuery_C2S_Msg = 3898,
	StarPRecruitQuery_S2C_Msg = 3899,
	StarPRecruitPublish_C2S_Msg = 3900,
	StarPRecruitPublish_S2C_Msg = 3901,
	FetchBatchSnapshotInfo_C2S_Msg = 3904,
	FetchBatchSnapshotInfo_S2C_Msg = 3905,
	BirthdayCardItemUpdateNtf = 3906,
	BirthdayBlessingRecordUpdateNtf = 3907,
	RoomGetCurRoundInfo_C2S_Msg = 3908,
	RoomGetCurRoundInfo_S2C_Msg = 3909,
	RoomRoundInfoNtf = 3910,
	RoomUgcMultiRoundCoinUse_C2S_Msg = 3911,
	RoomUgcMultiRoundCoinUse_S2C_Msg = 3912,
	ActivityFarmBuffWishSupport_C2S_Msg = 3913,
	ActivityFarmBuffWishSupport_S2C_Msg = 3914,
	ActivityFarmBuffWishInfo_C2S_Msg = 3915,
	ActivityFarmBuffWishInfo_S2C_Msg = 3916,
	ActivityFarmBuffWishSupportList_C2S_Msg = 3917,
	ActivityFarmBuffWishSupportList_S2C_Msg = 3918,
	PrayerCardRewardReachLimitNtf = 3921,
	BatchGetLatestMessageExtraInfo_C2S_Msg = 3922,
	BatchGetLatestMessageExtraInfo_S2C_Msg = 3923,
	ActivityFarmBuffWishReward_C2S_Msg = 3924,
	ActivityFarmBuffWishReward_S2C_Msg = 3925,
	BirthdayRemindInfo_C2S_Msg = 3928,
	BirthdayRemindInfo_S2C_Msg = 3929,
	BirthdayRemindClear_C2S_Msg = 3930,
	BirthdayRemindClear_S2C_Msg = 3931,
	CookVisitantServe_C2S_Msg = 3932,
	CookVisitantServe_S2C_Msg = 3933,
	BPDoUpgradeNtf = 3936,
	AMSItemResultNtf = 3943,
	StarPSubmitAssistOrder_C2S_Msg = 3944,
	StarPSubmitAssistOrder_S2C_Msg = 3945,
	StarPFindAssistOrder_C2S_Msg = 3946,
	StarPFindAssistOrder_S2C_Msg = 3947,
	StarPGetAssistOrderList_C2S_Msg = 3948,
	StarPGetAssistOrderList_S2C_Msg = 3949,
	StarPLikeAssistPlayer_C2S_Msg = 3950,
	StarPLikeAssistPlayer_S2C_Msg = 3951,
	FarmItemBanSell_C2S_Msg = 3954,
	FarmItemBanSell_S2C_Msg = 3955,
	HouseSwitchRoom_C2S_Msg = 3956,
	HouseSwitchRoom_S2C_Msg = 3957,
	CookSetFloatingScreenContent_C2S_Msg = 3964,
	CookSetFloatingScreenContent_S2C_Msg = 3965,
	MobaSquadDrawRedPacketRefreshNtf = 3968,
	ActivityFarmReturningTaskInfo_C2S_Msg = 3969,
	ActivityFarmReturningTaskInfo_S2C_Msg = 3970,
	ActivityFarmReturningTaskReward_C2S_Msg = 3971,
	ActivityFarmReturningTaskReward_S2C_Msg = 3972,
	StarPAssistOrderStatusNtf = 3974,
	GetGlobalRecentPlayRecord_C2S_Msg = 3977,
	GetGlobalRecentPlayRecord_S2C_Msg = 3978,
	FarmMagicEffectNtf = 3979,
	StarPJoinRecruit_C2S_Msg = 3980,
	StarPJoinRecruit_S2C_Msg = 3981,
	StarPQueryGroupApplication_C2S_Msg = 3983,
	StarPQueryGroupApplication_S2C_Msg = 3984,
	StarPInviteJoinGroup_C2S_Msg = 3985,
	StarPInviteJoinGroup_S2C_Msg = 3986,
	StarPQueryGroupInvitation_C2S_Msg = 3987,
	StarPQueryGroupInvitation_S2C_Msg = 3988,
	StarPApplyJoinGroup_C2S_Msg = 3989,
	StarPApplyJoinGroup_S2C_Msg = 3990,
	StarPAcceptInvitation_C2S_Msg = 3991,
	StarPAcceptInvitation_S2C_Msg = 3992,
	StarPAcceptApplication_C2S_Msg = 3993,
	StarPAcceptApplication_S2C_Msg = 3994,
	StarPGetMyContactRecordList_C2S_Msg = 3995,
	StarPGetMyContactRecordList_S2C_Msg = 3996,
	StarPRecommendGroupList_C2S_Msg = 3997,
	StarPRecommendGroupList_S2C_Msg = 3998,
	StarPGroupMemberList_C2S_Msg = 3999,
	StarPGroupMemberList_S2C_Msg = 4000,
	StarPGetPlayerWearItemInfo_C2S_Msg = 4003,
	StarPGetPlayerWearItemInfo_S2C_Msg = 4004,
	StarPGetGroupInfo_C2S_Msg = 4005,
	StarPGetGroupInfo_S2C_Msg = 4006,
	ArenaHeroStarRewardHeroLevel_C2S_Msg = 4009,
	ArenaHeroStarRewardHeroLevel_S2C_Msg = 4010,
	ArenaHeroStarRewardGeneralLevel_C2S_Msg = 4011,
	ArenaHeroStarRewardGeneralLevel_S2C_Msg = 4012,
	SetPackTag_C2S_Msg = 4017,
	SetPackTag_S2C_Msg = 4018,
	StarPGroupSaveSettings_C2S_Msg = 4023,
	StarPGroupSaveSettings_S2C_Msg = 4024,
	StarPKickGroupMember_C2S_Msg = 4025,
	StarPKickGroupMember_S2C_Msg = 4026,
	HouseSaveLayout_C2S_Msg = 4027,
	HouseSaveLayout_S2C_Msg = 4028,
	HouseGetLayoutList_C2S_Msg = 4029,
	HouseGetLayoutList_S2C_Msg = 4030,
	HouseDelLayout_C2S_Msg = 4031,
	HouseDelLayout_S2C_Msg = 4032,
	StarPSetJoinType_C2S_Msg = 4033,
	StarPSetJoinType_S2C_Msg = 4034,
	StarPSetTitle_C2S_Msg = 4035,
	StarPSetTitle_S2C_Msg = 4036,
	StarPGroupAttrNtf = 4037,
	HouseFurnitureBuyReplace_C2S_Msg = 4038,
	HouseFurnitureBuyReplace_S2C_Msg = 4039,
	FetchBatchInfo_C2S_Msg = 4040,
	FetchBatchInfo_S2C_Msg = 4041,
	FetchRankNoByScore_C2S_Msg = 4044,
	FetchRankNoByScore_S2C_Msg = 4045,
	FarmPetFeedDetail_C2S_Msg = 4046,
	FarmPetFeedDetail_S2C_Msg = 4047,
	FarmQueryRipeTimeBeforeRain_C2S_Msg = 4054,
	FarmQueryRipeTimeBeforeRain_S2C_Msg = 4055,
	UgcOperatePublishCoCreateMap_C2S_Msg = 4056,
	UgcOperatePublishCoCreateMap_S2C_Msg = 4057,
	PressGmAddItem_C2S_Msg = 4058,
	PressGmAddItem_S2C_Msg = 4059,
	HouseGetLayoutDetail_C2S_Msg = 4060,
	HouseGetLayoutDetail_S2C_Msg = 4061,
	HouseLayoutApplyCheck_C2S_Msg = 4062,
	HouseLayoutApplyCheck_S2C_Msg = 4063,
	ExchangeShard_C2S_Msg = 4064,
	ExchangeShard_S2C_Msg = 4065,
	StarPBatchGetGuildPublicInfo_C2S_Msg = 4066,
	StarPBatchGetGuildPublicInfo_S2C_Msg = 4067,
	StarPPetTradeUpdateWish_C2S_Msg = 4068,
	StarPPetTradeUpdateWish_S2C_Msg = 4069,
	StarPPetTradeCancelWish_C2S_Msg = 4070,
	StarPPetTradeCancelWish_S2C_Msg = 4071,
	StarPPetTradeFocusPlayer_C2S_Msg = 4072,
	StarPUpdatePetTradeWish_S2C_Msg = 4073,
	StarPPetTradeCancelFocusPlayer_C2S_Msg = 4074,
	StarPPetTradeCancelFocusPlayer_S2C_Msg = 4075,
	StarPPetTradeGetFocusPlayer_C2S_Msg = 4076,
	StarPPetTradeGetFocusPlayer_S2C_Msg = 4077,
	FarmKirinLevelUp_C2S_Msg = 4078,
	FarmKirinLevelUp_S2C_Msg = 4079,
	FarmKirinIncubate_C2S_Msg = 4080,
	FarmKirinIncubate_S2C_Msg = 4081,
	FarmKirinCollect_C2S_Msg = 4082,
	FarmKirinCollect_S2C_Msg = 4083,
	FarmDelGiftRecord_C2S_Msg = 4084,
	FarmDelGiftRecord_S2C_Msg = 4085,
	FarmPartyPublish_C2S_Msg = 4086,
	FarmPartyPublish_S2C_Msg = 4087,
	UgcUploadLoading_C2S_Msg = 4089,
	UgcUploadLoading_S2C_Msg = 4090,
	UgcUploadLobbyCover_C2S_Msg = 4091,
	UgcUploadLobbyCover_S2C_Msg = 4092,
	UpdateProfileShowCollection_C2S_Msg = 4093,
	UpdateProfileShowCollection_S2C_Msg = 4094,
	AiNpcAddChatRecordNtf = 4095,
	AiNpcDelChatRecordNtf = 4096,
	AiNpcGetChatRecord_C2S_Msg = 4097,
	AiNpcGetChatRecord_S2C_Msg = 4098,
	StarPSocInteractionRatio_C2S_Msg = 4101,
	StarPSocInteractionRatio_S2C_Msg = 4102,
	StarPFriendIntimacyStateNtf = 4103,
	StarPCardSend_C2S_Msg = 4105,
	StarPCardSend_S2C_Msg = 4106,
	StarPCardHandle_C2S_Msg = 4107,
	ChatGetStarPModuleInfo_C2S_Msg = 4108,
	ChatGetStarPModuleInfo_S2C_Msg = 4109,
	ChatGetAllModuleInfo_C2S_Msg = 4110,
	ChatGetAllModuleInfo_S2C_Msg = 4111,
	StarPCardHandle_S2C_Msg = 4112,
	WolfKillTreasureShare_C2S_Msg = 4113,
	WolfKillTreasureShare_S2C_Msg = 4114,
	ArenaCardHotInfo_C2S_Msg = 4115,
	ArenaCardHotInfo_S2C_Msg = 4116,
	ArenaArenaCardHotInfoNtf = 4117,
	PlayerBatchPictureSetting_C2S_Msg = 4127,
	PlayerBatchPictureSetting_S2C_Msg = 4128,
	PlayerAlbumPicBatchAtTarget_C2S_Msg = 4129,
	PlayerAlbumPicBatchAtTarget_S2C_Msg = 4130,
	ChatModuleInfoChangeNtf = 4131,
	ChatModuleInfoNtf = 4132,
	CookVisitantCancelProtect_C2S_Msg = 4135,
	CookVisitantCancelProtect_S2C_Msg = 4136,
	ChaseDressUpItem_C2S_Msg = 4137,
	ChasDressUpItem_S2C_Msg = 4138,
	FarmPartyClose_C2S_Msg = 4148,
	FarmPartyClose_S2C_Msg = 4149,
	FarmGetPartyList_C2S_Msg = 4158,
	FarmGetPartyList_S2C_Msg = 4159,
	PlatCommonConfig_C2S_Msg = 4160,
	PlatCommonConfig_S2C_Msg = 4161,
	ChaseDressUpItem_S2C_Msg = 4162,
	StarPGroupJoinChatGroup_C2S_Msg = 4171,
	StarPGroupJoinChatGroup_S2C_Msg = 4172,
	StarPGroupGetPetEgg_C2S_Msg = 4173,
	StarPGroupGetPetEgg_S2C_Msg = 4174,
	ArenaSeasonStatInfoNtf = 4177,
	ArenaSeasonStatInfo_C2S_Msg = 4178,
	ArenaSeasonStatInfo_S2C_Msg = 4179,
	CookSetEffectiveCustomerFlow_C2S_Msg = 4181,
	CookSetEffectiveCustomerFlow_S2C_Msg = 4182,
	PlayerAlbumPicTargetAtNtf = 4183,
	PuzzleUncover_C2S_Msg = 4190,
	PuzzleUncover_S2C_Msg = 4191,
	PuzzleUncoverAll_C2S_Msg = 4192,
	PuzzleUncoverAll_S2C_Msg = 4193,
	SquadTaskExtraInfo_C2S_Msg = 4196,
	SquadTaskExtraInfo_S2C_Msg = 4197,
	ChaseBatchGetGameData_C2S_Msg = 4200,
	ChaseBatchGetGameData_S2C_Msg = 4201,
	FarmMagicUseRecoverItem_C2S_Msg = 4202,
	FarmMagicUseRecoverItem_S2C_Msg = 4203,
	StarPGetGuildCardDetailInfo_C2S_Msg = 4204,
	StarPGetGuildCardDetailInfo_S2C_Msg = 4205,
	StarPGMClearRole_C2S_Msg = 4206,
	StarPGMClearRole_S2C_Msg = 4207,
	FarmSetLiuYanMessagePermission_C2S_Msg = 4210,
	FarmSetLiuYanMessagePermission_S2C_Msg = 4211,
	GetTaskPassWordCode_C2S_Msg = 4212,
	GetTaskPassWordCode_S2C_Msg = 4213,
	UseTaskPassWordCode_C2S_Msg = 4214,
	UseTaskPassWordCode_S2C_Msg = 4215,
	PlayerAlbumPictureEdit_C2S_Msg = 4216,
	PlayerAlbumPictureEdit_S2C_Msg = 4217,
	PlayerAlbumPictureTextCheck_C2S_Msg = 4218,
	PlayerAlbumPictureText_S2C_Msg = 4219,
	CookCalculateDishIncome_C2S_Msg = 4221,
	CookCalculateDishIncome_S2C_Msg = 4222,
	CookCalculateCookOpenIncome_C2S_Msg = 4223,
	CookCalculateCookOpenIncome_S2C_Msg = 4224,
	PlayerAlbumPictureTextCheck_S2C_Msg = 4225,
	StarPJoinGuildSuccNtf = 4226,
	HouseRoomInfoNtf = 4227,
	UgcGetCreatorBadge_C2S_Msg = 4228,
	UgcGetCreatorBadge_S2C_Msg = 4229,
	UgcActiveCreatorBadgeNtf = 4230,
	UgcSetCreatorHomePage_C2S_Msg = 4231,
	UgcSetCreatorHomePage_S2C_Msg = 4232,
	UgcGetCreatorHomePage_C2S_Msg = 4233,
	UgcGetCreatorHomePage_S2C_Msg = 4234,
	UgcMatchLobbyDetailExMulti_C2S_Msg = 4238,
	UgcMatchLobbyDetailExMulti_S2C_Msg = 4239,
	UgcMatchLobbyDetailExChange_C2S_Msg = 4240,
	UgcMatchLobbyDetailExChange_S2C_Msg = 4241,
	UgcMatchLobbyDetailPlayRecord_C2S_Msg = 4242,
	UgcMatchLobbyDetailPlayRecord_S2C_Msg = 4243,
	CocGMCommand_C2S_Msg = 4244,
	CocGMCommand_S2C_Msg = 4245,
	PlayerAlbumPicInfo_C2S_Msg = 4252,
	PlayerAlbumPicInfo_S2C_Msg = 4253,
	FarmPetTame_C2S_Msg = 4255,
	FarmPetTame_S2C_Msg = 4256,
	CookSendCommentFood_C2S_Msg = 4259,
	CookSendCommentFood_S2C_Msg = 4260,
	GetSeasonReviewRedPointInfo_C2S_Msg = 4263,
	GetSeasonReviewRedPointInfo_S2C_Msg = 4264,
	UpdatedSeasonReviewRedPointInfo_C2S_Msg = 4265,
	UpdatedSeasonReviewRedPointInfo_S2C_Msg = 4266,
	CookFetchVisitantInfo_C2S_Msg = 4278,
	CookFetchVisitantInfo_S2C_Msg = 4279,
	FarmPetCatFishing_C2S_Msg = 4284,
	FarmPetCatFishing_S2C_Msg = 4285,
	PlayerTargetedLoadingTextPushCount_C2S_Msg = 4288,
	PlayerTargetedLoadingTextPushCount_S2C_Msg = 4289,
	CocBuildingRevamp_C2S_Msg = 4292,
	CocBuildingRevamp_S2C_Msg = 4293,
	ClientSetDSParam_C2S_Msg = 4294,
	ClientSetDSParam_S2C_Msg = 4295,
	CocBuildingAssignVillager_C2S_Msg = 4296,
	CocBuildingAssignVillager_S2C_Msg = 4297,
	CocBuildingUnassignVillager_C2S_Msg = 4298,
	CocBuildingUnassignVillager_S2C_Msg = 4299,
	FarmBatchReadSignature_C2S_Msg = 4300,
	FarmBatchReadSignature_S2C_Msg = 4301,
	ReturnActivityOfflineReward_C2S_Msg = 4302,
	ReturnActivityOfflineReward_S2C_Msg = 4303,
	ActivityGetMultiPlayerSquadIsFull_C2S_Msg = 4305,
	ActivityGetMultiPlayerSquadIsFull_S2C_Msg = 4306,
	OpenGiftPackageAnimationNtf = 4307,
	GameModeReturnInfo_C2S_Msg = 4318,
	GameModeReturnInfo_S2C_Msg = 4319,
	GameModeReturnStart_C2S_Msg = 4320,
	GameModeReturnStart_S2C_Msg = 4321,
	GameModeReturnStateChangeNtf = 4322,
	GameModeReturnFinishNtf = 4323,
	ChangeTaskOptionReward_C2S_Msg = 4324,
	ChangeTaskOptionReward_S2C_Msg = 4325,
	AchievementSortGetList_C2S_Msg = 4332,
	AchievementSortGetList_S2C_Msg = 4333,
	Nr3e8Common_C2S_Msg = 4336,
	Nr3e8Common_S2C_Msg = 4337,
	Nr3e8BoardEnter_C2S_Msg = 4338,
	Nr3e8BoardEnter_S2C_Msg = 4339,
	Nr3e8BoardExit_C2S_Msg = 4340,
	Nr3e8BoardExit_S2C_Msg = 4341,
	Nr3e8CommonNtf = 4342,
	Nr3e8TaskRewardRecv_C2S_Msg = 4343,
	Nr3e8TaskRewardRecv_S2C_Msg = 4344,
	Nr3e8TaskWeekActivityRewardRecv_C2S_Msg = 4345,
	Nr3e8TaskWeekActivityRewardRecv_S2C_Msg = 4346,
	Nr3e8TaskRead_C2S_Msg = 4347,
	Nr3e8TaskRead_S2C_Msg = 4348,
	WolfKillShieldVocation_C2S_Msg = 4349,
	WolfKillShieldVocation_S2C_Msg = 4350,
	GetWolfKillShieldVocation_C2S_Msg = 4351,
	GetWolfKillShieldVocation_S2C_Msg = 4352,
	GetArenaBlockInfo_C2S_Msg = 4353,
	GetArenaBlockInfo_S2C_Msg = 4354,
	CookEvictBadGuy_C2S_Msg = 4355,
	CookEvictBadGuy_S2C_Msg = 4356,
	GameModeReturnOpenedSlapFace_C2S_Msg = 4361,
	GameModeReturnOpenedSlapFace_S2C_Msg = 4362,
	FarmTagRedFox_C2S_Msg = 4363,
	FarmTagRedFox_S2C_Msg = 4364,
	FarmTaskFinishRemainTasks_C2S_Msg = 4365,
	FarmTaskFinishRemainTasks_S2C_Msg = 4366,
	SetAppearanceRoadShow_C2S_Msg = 4391,
	SetAppearanceRoadShow_S2C_Msg = 4392,
	SingleBattleStart_C2S_Msg = 4393,
	SingleBattleStart_S2C_Msg = 4394,
	SingleBattleSettlement_C2S_Msg = 4395,
	SingleBattleSettlement_S2C_Msg = 4396,
	UgcQuickJoinWithMidJoin_C2S_Msg = 4397,
	UgcQuickJoinWithMidJoin_S2C_Msg = 4398,
	UgcQuickJoinWithMidJoinResultNtf = 4399,
	GameModeReturnClearRedDot_C2S_Msg = 4406,
	GameModeReturnClearRedDot_S2C_Msg = 4407,
	FarmGMCommand_C2S_Msg = 4408,
	FarmGMCommand_S2C_Msg = 4409,
	CocForwardTest_C2S_Msg = 4412,
	CocForwardTest_S2C_Msg = 4413,
	GetCheckInManualFarmDailyBuys_C2S_Msg = 4414,
	GetCheckInManualFarmDailyBuys_S2C_Msg = 4415,
	CocForwardOneWayTest_C2S_Msg = 4417,
	CocForwardOneWayTest_S2C_Msg = 4418,
	CocDSInfoNtf = 4419,
	CocAttrNtf = 4422,
	QuerySecondaryNewbieRelatedMatchTypeBattleCnt_C2S_Msg = 4426,
	QuerySecondaryNewbieRelatedMatchTypeBattleCnt_S2C_Msg = 4427,
	CocHomeKickNtf = 4428,
	CocUpgradeHallOfVillager_C2S_Msg = 4429,
	CocUpgradeHallOfVillager_S2C_Msg = 4430,
	CocAccelerateUpgradingHallOfVillager_C2S_Msg = 4431,
	CocAccelerateUpgradingHallOfVillager_S2C_Msg = 4432,
	CocPayUnlockStudentPosition_C2S_Msg = 4433,
	CocPayUnlockStudentPosition_S2C_Msg = 4434,
	CocPutStudentIn_C2S_Msg = 4435,
	CocPutStudentIn_S2C_Msg = 4436,
	BagDressUpSetting_S2C_Msg = 4439,
	UgcHomePageGetLikePlayHotTag_C2S_Msg = 4456,
	UgcHomePageGetLikePlayHotTag_S2C_Msg = 4457,
	UgcHomePageSetLikePlayHotTag_C2S_Msg = 4458,
	UgcHomePageSetLikePlayHotTag_S2C_Msg = 4459,
	PlayerIntimateRelationRedPoint_C2S_Msg = 4460,
	PlayerIntimateRelationRedPoint_S2C_Msg = 4461,
	CocForwardCallerTest_C2S_Msg = 4462,
	CocForwardCallerTest_S2C_Msg = 4463,
	BagDressUpSetting_C2S_Msg = 4464,
	GiftRedUpdateDot_C2S_Msg = 4465,
	GiftRedUpdateDot_S2C_Msg = 4466,
	ChatBindModule_C2S_Msg = 4467,
	ChatBindModule_S2C_Msg = 4468,
	ChatUnbindModule_C2S_Msg = 4469,
	ChatUnbindModule_S2C_Msg = 4470,
	InflateRedPacketQuit_C2S_Msg = 4471,
	InflateRedPacketQuit_S2C_Msg = 4472,
	InflateRedPacketKickOut_C2S_Msg = 4473,
	InflateRedPacketKickOut_S2C_Msg = 4474,
	InflateRedPacketInflate_C2S_Msg = 4475,
	InflateRedPacketInflate_S2C_Msg = 4476,
	InflateRedPacketReceive_C2S_Msg = 4477,
	InflateRedPacketReceive_S2C_Msg = 4478,
	InflateRedPacketUpdateNtf = 4479,
	MallActivityInflateRedPacketPurchase_C2S_Msg = 4480,
	MallActivityInflateRedPacketPurchase_S2C_Msg = 4481,
	PlayerHallStableState_C2S_Msg = 4484,
	PlayerHallStableState_S2C_Msg = 4485,
	GetWolfKillMonthCardInfo_C2S_Msg = 4486,
	GetWolfKillMonthCardInfo_S2C_Msg = 4487,
	OpenWolfKillMonth_C2S_Msg = 4488,
	OpenWolfKillMonth_S2C_Msg = 4489,
	GetWolfKillMonthFreeGift_C2S_Msg = 4490,
	GetWolfKillMonthFreeGift_S2C_Msg = 4491,
	WolfKillMonthCardNtf = 4508,
	MatchRecommendList_C2S_Msg = 4509,
	MatchRecommendList_S2C_Msg = 4510,
	SimulateLimitNtf = 4511,
	StarPChooseInitEquipSuit_C2S_Msg = 4512,
	StarPChooseInitEquipSuit_S2C_Msg = 4513,
	StarPGetUnlockData_C2S_Msg = 4514,
	StarPGetUnlockData_S2C_Msg = 4515,
	StarPGetChatGroupKey_C2S_Msg = 4516,
	StarPGetChatGroupKey_S2C_Msg = 4517,
	StarPPetTradeFocusPlayer_S2C_Msg = 4518,
	StarPTipsNtf = 4519,
	StarPJoinGroup_C2S_Msg = 4520,
	StarPJoinGroup_S2C_Msg = 4521,
	StarPLeaveGroup_C2S_Msg = 4522,
	StarPLeaveGroup_S2C_Msg = 4523,
	StarPDismissGroup_C2S_Msg = 4524,
	StarPDismissGroup_S2C_Msg = 4525,
	StarPPvpStartFailNtf = 4526,
	UpdatePlayerLimitExperienceRedPoint_C2S_Msg = 4541,
	UpdatePlayerLimitExperienceRedPoint_S2C_Msg = 4542,
	FarmGetNewItemNtf = 4557,
	GetBiHudRecommendMapId_C2S_Msg = 4558,
	GetBiHudRecommendMapId_S2C_Msg = 4559,
	GetFarmDailyReward_C2S_Msg = 4560,
	GetFarmDailyReward_S2C_Msg = 4561,
	UpgradeFarmDailyReward_C2S_Msg = 4562,
	UpgradeFarmDailyReward_S2C_Msg = 4563,
	TeamRecruitPublishWithModeType_C2S_Msg = 4566,
	TeamRecruitPublishWithModeType_S2C_Msg = 4567,
	TeamRecruitQueryByModeType_C2S_Msg = 4568,
	TeamRecruitQueryByModeType_S2C_Msg = 4569,
	FarmGetCloudTax_C2S_Msg = 4575,
	FarmGetCloudTax_S2C_Msg = 4576,
	UgcRoomGetPlayerRecommendMaps_C2S_Msg = 4577,
	UgcRoomGetPlayerRecommendMaps_S2C_Msg = 4578,
	UgcRoomGetOfficalRecommendMaps_C2S_Msg = 4579,
	UgcRoomGetOfficalRecommendMaps_S2C_Msg = 4580,
	FarmStealingMyRecord_C2S_Msg = 4582,
	FarmStealingMyRecord_S2C_Msg = 4583,
	UpgradeFarmDailyAwardNtf = 4611,
	FarmSetBeFertilizedType_C2S_Msg = 4616,
	FarmSetBeFertilizedType_S2C_Msg = 4617,
	UpgradeInFarmDailyActivityPage_C2S_Msg = 4618,
	UpgradeInFarmDailyActivityPage_S2C_Msg = 4619,
	UgcCoCreateMultiEditApply_C2S_Msg = 4620,
	UgcCoCreateMultiEditApply_S2C_Msg = 4621,
	UgcCoCreateMultiEditApplyNtf = 4622,
	UgcCoCreateMultiEditReply_C2S_Msg = 4623,
	UgcCoCreateMultiEditReply_S2C_Msg = 4624,
	UgcCoCreateMultiEditReplyNtf = 4625,
	UgcCoCreateMultiEditRejectEnter_C2S_Msg = 4626,
	UgcCoCreateMultiEditRejectEnter_S2C_Msg = 4627,
	UgcCoCreateMultiEditRejectEnterNtf = 4628,
	GamePakDetailInfoReport_C2S_Msg = 4629,
	GamePakDetailInfoReport_S2C_Msg = 4630,
	FarmOpenGodUI_C2S_Msg = 4633,
	FarmOpenGodUI_S2C_Msg = 4634,
	UgcCoCreateMultiEditCodingData_C2S_Msg = 4635,
	UgcCoCreateMultiEditCodingData_S2C_Msg = 4636,
	UgcCoCreateMultiEditDataUpdateNtf = 4637,
	ChatClearAllNotRead_C2S_Msg = 4642,
	ChatClearAllNotRead_S2C_Msg = 4643,
	WolfKillLevel_C2S_Msg = 4647,
	WolfKillLevel_S2C_Msg = 4648,
	PlayerDanceScore_C2S_Msg = 4649,
	PlayerDanceScore_S2C_Msg = 4650,
	UgcAiChangeActionState_C2S_Msg = 4653,
	UgcAiChangeActionState_S2C_Msg = 4654,
	RoomCancelPreStart_C2S_Msg = 4655,
	RoomCancelPreStart_S2C_Msg = 4656,
	AiNpcChatPushNoticeNtf = 4657,
	BattleSceneSwitchNtf = 4658,
	ExitBattleNtf = 4659,
	FlashRaceCheeringAi_C2S_Msg = 4660,
	FlashRaceCheeringAi_S2C_Msg = 4661,
	FlashRaceCheeringVoteOption_C2S_Msg = 4662,
	FlashRaceCheeringVoteOption_S2C_Msg = 4663,
	UgcSubscribeRecommendPage_C2S_Msg = 4665,
	UgcSubscribeRecommendPage_S2C_Msg = 4666,
	FarmBadGuyCloudTaxDetail_C2S_Msg = 4667,
	FarmBadGuyCloudTaxDetail_S2C_Msg = 4668,
	ActivityLevelUpChallengeCrit_C2S_Msg = 4687,
	ActivityLevelUpChallengeCrit_S2C_Msg = 4688,
	CompetitionEntranceStatusChangeNtf = 4711,
	HouseBuySampleRoom_C2S_Msg = 4712,
	HouseBuySampleRoom_S2C_Msg = 4713,
	GetWolfKillMMRScore_C2S_Msg = 4714,
	GetWolfKillMMRScore_S2C_Msg = 4715,
	ABTestInfoBatchQuery_C2S_Msg = 4716,
	ABTestInfoBatchQuery_S2C_Msg = 4717,
	FashionSkillUse_C2S_Msg = 4718,
	FashionSkillUse_S2C_Msg = 4719,
	HOKShowAiInvite_C2S_Msg = 4735,
	HOKShowAiInvite_S2C_Msg = 4736,
	UgcRoomPlayerRecommendMap_C2S_Msg = 4737,
	UgcRoomPlayerRecommendMap_S2C_Msg = 4738,
	UgcRoomPlayerRecommendMapNtf = 4739,
	UgcGetStarWorldEntranceBubble_C2S_Msg = 4740,
	UgcGetStarWorldEntranceBubble_S2C_Msg = 4741,
	UgcRemoveStarWorldEntranceBubble_C2S_Msg = 4742,
	UgcRemoveStarWorldEntranceBubble_S2C_Msg = 4743,
	MobaFootballGoalBroadcastNtf = 4746,
	CupsCycleOpenNtf = 4751,
	CupsCycleUnLockNtf = 4752,
	ChangeCupsCycle_C2S_Msg = 4753,
	ChangeCupsCycle_S2C_Msg = 4754,
	UpdatePlayerGearSetting_C2S_Msg = 4755,
	UpdatePlayerGearSetting_S2C_Msg = 4756,
	FarmCanStealNtf = 4759,
	UgcAiImageCheck_C2S_Msg = 4760,
	UgcAiImageCheck_S2C_Msg = 4761,
	ItemChangeNtf = 4762,
	HOKShowAiInviteNtf = 4763,
	CupsCycleBeginOpenNtf = 4766,
	CupsCycleClickRedDot_C2S_Msg = 4767,
	CupsCycleClickRedDot_S2C_Msg = 4768,
	AnimeDressOutlineSet_C2S_Msg = 4773,
	AnimeDressOutlineSet_S2C_Msg = 4774,
	ArenaCardSuitInfo_C2S_Msg = 4775,
	ArenaCardSuitInfo_S2C_Msg = 4776,
	ArenaCardSuitInfoNtf = 4777,
	VehicleAccessoriesItem_C2S_Msg = 4779,
	VehicleAccessoriesItem_S2C_Msg = 4780,
	RoomRecommendListWithModeType_C2S_Msg = 4783,
	RoomRecommendListWithModeType_S2C_Msg = 4784,
	ClientLogEndNtf = 4785,
	ConfigSvrGM_C2S_Msg = 4786,
	ConfigSvrGM_S2C_Msg = 4787,
	GameModeReturnDrawProgressReward_C2S_Msg = 4794,
	GameModeReturnDrawProgressReward_S2C_Msg = 4795,
	GameModeRewardDrawInnReward_C2S_Msg = 4796,
	GameModeRewardDrawInnReward_S2C_Msg = 4797,
	MailPurchased_C2S_Msg = 4808,
	MailPurchased_S2C_Msg = 4809,
	MailBuyInfo_C2S_Msg = 4810,
	MailBuyInfo_S2C_Msg = 4811,
	GameModeReturnBISort_C2S_Msg = 4812,
	GameModeReturnBISort_S2C_Msg = 4813,
	GetWeekendGiftReward_C2S_Msg = 4815,
	GetWeekendGiftReward_S2C_Msg = 4816,
	UpgradeWeekendGift_C2S_Msg = 4817,
	UpgradeWeekendGift_S2C_Msg = 4818,
	RoomModifyMatchingTeamInfo_C2S_Msg = 4819,
	RoomModifyMatchingTeamInfo_S2C_Msg = 4820,
	RoomModifyMatchingTeamInfoResultNtf = 4821,
	UgcMatchLobbySummery_C2S_Msg = 4822,
	UgcMatchLobbySummery_S2C_Msg = 4823,
	GetWeekTopPlayGroupId_C2S_Msg = 4824,
	GetWeekTopPlayGroupId_S2C_Msg = 4825,
	GetDayPlayedGroupId_C2S_Msg = 4826,
	GetDayPlayedGroupId_S2C_Msg = 4827,
	ChaseSetSettings_C2S_Msg = 4828,
	ChaseSetSettings_S2C_Msg = 4829,
	UgcMatchLobbySummerySpecified_C2S_Msg = 4830,
	UgcMatchLobbySummerySpecified_S2C_Msg = 4831,
	DisplayBoardClear_C2S_Msg = 4834,
	DisplayBoardClear_S2C_Msg = 4835,
	DisplayBoardHistorySetting_C2S_Msg = 4836,
	DisplayBoardHistorySetting_S2C_Msg = 4837,
	DisplayBoardUpdateNtf = 4838,
	DisplayBoardNewRedDotRemove_C2S_Msg = 4839,
	DisplayBoardNewRedDotRemove_S2C_Msg = 4840,
	AddForceOpenMatchType_C2S_Msg = 4845,
	AddForceOpenMatchType_S2C_Msg = 4846,
	SelfFarmCsForwardTest_C2S_Msg = 4847,
	SelfFarmCsForwardTest_S2C_Msg = 4848,
	CurrFarmCsForwardTest_C2S_Msg = 4849,
	CurrFarmCsForwardTest_S2C_Msg = 4850,
	ActivityTreasureHuntExcavate_C2S_Msg = 4851,
	ActivityTreasureHuntExcavate_S2C_Msg = 4852,
	ActivityTreasureHuntSell_C2S_Msg = 4853,
	ActivityTreasureHuntSell_S2C_Msg = 4854,
	ActivityTreasureHuntAttrUpdateNtf = 4855,
	GetMasterPatchInfo_C2S_Msg = 4856,
	GetMasterPatchInfo_S2C_Msg = 4857,
	ReceiveMasterPatchReward_C2S_Msg = 4858,
	MasterPatchUnLockNtf = 4859,
	AddPicToPicWall_C2S_Msg = 4860,
	AddPicToPicWall_S2C_Msg = 4861,
	DelPicFromPicWall_C2S_Msg = 4862,
	DelPicFromPicWall_S2C_Msg = 4863,
	PlayerPicWallLike_C2S_Msg = 4864,
	PlayerPicWallLike_S2C_Msg = 4865,
	GetPicWallTopLikeList_C2S_Msg = 4866,
	GetPicWallTopLikeList_S2C_Msg = 4867,
	GetPicWallLatestList_C2S_Msg = 4868,
	GetPicWallLatestList_S2C_Msg = 4869,
	ReceiveMasterPatchReward_S2C_Msg = 4871,
	DisplayBoardSave_C2S_Msg = 4872,
	DisplayBoardSave_S2C_Msg = 4873,
	DisplayBoardOpen_C2S_Msg = 4874,
	DisplayBoardOpen_S2C_Msg = 4875,
	ItaBagBadgeEquip_C2S_Msg = 4878,
	ItaBagBadgeEquip_S2C_Msg = 4879,
	DressUpDetailInfoChange_C2S_Msg = 4882,
	DressUpDetailInfoChange_S2C_Msg = 4883,
	UpdateItemInfoDbItemShowStatus_C2S_Msg = 4884,
	UpdateItemInfoDbItemShowStatus_S2C_Msg = 4885,
	DressItemShowStatusChange_C2S_Msg = 4886,
	DressItemShowStatusChange_S2C_Msg = 4887,
	GetPicWallMyPicList_C2S_Msg = 4910,
	GetPicWallMyPicList_S2C_Msg = 4911,
	NewTradingCardGetNtf = 4914,
	ChaseGetIdentityProficiencyInfo_C2S_Msg = 4915,
	ChaseGetIdentityProficiencyInfo_S2C_Msg = 4916,
	ChaseIdentityBattlePerformance_C2S_Msg = 4917,
	ChaseIdentityBattlePerformance_S2C_Msg = 4918,
	ChaseIdentityDrawProficiencyProgressReward_C2S_Msg = 4919,
	ChaseIdentityDrawProficiencyProgressReward_S2C_Msg = 4920,
	MasterPatchChangeNtf = 4921,
	ChaseIdentityReadBiography_C2S_Msg = 4935,
	ChaseIdentityReadBiography_S2C_Msg = 4936,
	WeekendGiftBuySuccessNtf = 4947,
	WeekendGiftCheckInNtf = 4948,
}

-- MsgId-MsgName
local MsgId2Name = {
	[107] = "UnimplementedMsg_S2C_Msg",
	[108] = "Login_C2S_Msg",
	[109] = "Login_S2C_Msg",
	[110] = "DirectSceneEnter_C2S_Msg",
	[111] = "DirectSceneEnter_S2C_Msg",
	[112] = "DirectSceneExit_C2S_Msg",
	[113] = "DirectSceneExit_S2C_Msg",
	[114] = "DirectSceneReportData_C2S_Msg",
	[115] = "DirectSceneReportData_S2C_Msg",
	[116] = "DirectSceneDataSyncNtf",
	[117] = "DirectSceneJumpNtf",
	[118] = "DirectSceneDataNtf",
	[119] = "DirectScenePlayerHeartbeat_C2S_Msg",
	[120] = "DirectScenePlayerHeartbeat_S2C_Msg",
	[121] = "DirectSceneReconnect_C2S_Msg",
	[122] = "DirectSceneReconnect_S2C_Msg",
	[123] = "DirectSceneReportAction_C2S_Msg",
	[124] = "DirectSceneReportAction_S2C_Msg",
	[125] = "DirectScenePlayInfoNtf",
	[126] = "DirectSceneReportLevelData_C2S_Msg",
	[127] = "DirectSceneReportLevelData_S2C_Msg",
	[128] = "DirectSceneLevelDataNtf",
	[129] = "SceneEntityPrivateAttrNtf",
	[130] = "DirectSceneReady_C2S_Msg",
	[131] = "DirectSceneReady_S2C_Msg",
	[132] = "DirectSceneBorrowLock_C2S_Msg",
	[133] = "DirectSceneBorrowLock_S2C_Msg",
	[134] = "DirectSceneBorrowUnlock_C2S_Msg",
	[135] = "DirectSceneBorrowUnlock_S2C_Msg",
	[136] = "DirectSceneReportPlayResult_C2S_Msg",
	[137] = "DirectSceneReportPlayResult_S2C_Msg",
	[138] = "DressUpToggle_C2S_Msg",
	[139] = "DressUpToggle_S2C_Msg",
	[140] = "DressUpCheckExpire_C2S_Msg",
	[141] = "DressUpCheckExpire_S2C_Msg",
	[142] = "DressUpExpireNtf",
	[143] = "DressUpChangeNtf",
	[144] = "DressUpShowList_C2S_Msg",
	[145] = "DressUpShowList_S2C_Msg",
	[146] = "DressUpItem_C2S_Msg",
	[147] = "DressUpItem_S2C_Msg",
	[148] = "DressUpChangeRandomState_C2S_Msg",
	[149] = "DressUpChangeRandomState_S2C_Msg",
	[150] = "DressUpActive_C2S_Msg",
	[151] = "DressUpActive_S2C_Msg",
	[152] = "StageAchievementRewardNtf",
	[153] = "GetStageAchievementReward_C2S_Msg",
	[154] = "GetStageAchievementReward_S2C_Msg",
	[155] = "GetAchievementLabel_C2S_Msg",
	[156] = "GetAchievementLabel_S2C_Msg",
	[157] = "AcceptTask_C2S_Msg",
	[158] = "AcceptTask_S2C_Msg",
	[159] = "RewardTask_C2S_Msg",
	[160] = "RewardTask_S2C_Msg",
	[161] = "TaskRewardNtf",
	[162] = "CompleteGuideTask_C2S_Msg",
	[163] = "CompleteGuideTask_S2C_Msg",
	[164] = "StartDsGuide_C2S_Msg",
	[165] = "StartDsGuide_S2C_Msg",
	[166] = "EndDsGuide_C2S_Msg",
	[167] = "EndDsGuide_S2C_Msg",
	[168] = "GetTomorrowReward_C2S_Msg",
	[169] = "GetTomorrowReward_S2C_Msg",
	[170] = "RootAttrNtf",
	[171] = "KickPlayerNtf",
	[172] = "HeartBeat_C2S_Msg",
	[173] = "HeartBeat_S2C_Msg",
	[174] = "GMCommand_C2S_Msg",
	[175] = "GMCommand_S2C_Msg",
	[176] = "GMAllCmdInfoNtf",
	[177] = "ResFileUpdateNtf",
	[178] = "AttrRemoveTags_C2S_Msg",
	[179] = "AttrRemoveTags_S2C_Msg",
	[180] = "AttrAddTags_C2S_Msg",
	[181] = "AttrAddTags_S2C_Msg",
	[182] = "UploadLogNtf",
	[183] = "PlayerMiscNtf",
	[184] = "DebugMessageNtf",
	[185] = "ModErrorNtf",
	[186] = "RequestSongAchList_C2S_Msg",
	[187] = "RequestSongAchList_S2C_Msg",
	[188] = "PlayerNoticeMsgNtf",
	[189] = "GetOpenIdFromUid_C2S_Msg",
	[190] = "GetOpenIdFromUid_S2C_Msg",
	[191] = "UploadButtonClickFlow_C2S_Msg",
	[192] = "UploadButtonClickFlow_S2C_Msg",
	[193] = "ListFeatureOpen_C2S_Msg",
	[194] = "ListFeatureOpen_S2C_Msg",
	[195] = "FeatureOpenNtf",
	[196] = "PlayerAfterLoginNtf",
	[197] = "GetZplanCurrencyBalance_C2S_Msg",
	[198] = "GetZplanCurrencyBalance_S2C_Msg",
	[199] = "GetZplanProfileInfo_C2S_Msg",
	[200] = "GetZplanProfileInfo_S2C_Msg",
	[201] = "UploadVoiceStatusTlogFlow_C2S_Msg",
	[202] = "UploadVoiceStatusTlogFlow_S2C_Msg",
	[203] = "UpdateMidasToken_C2S_Msg",
	[204] = "UpdateMidasToken_S2C_Msg",
	[205] = "AntiAddictReport_C2S_Msg",
	[206] = "AntiAddictReport_S2C_Msg",
	[207] = "AntiAddictNtf",
	[208] = "GetSpecReward_C2S_Msg",
	[209] = "GetSpecReward_S2C_Msg",
	[210] = "RoomCreate_C2S_Msg",
	[211] = "RoomCreate_S2C_Msg",
	[212] = "RoomInvite_C2S_Msg",
	[213] = "RoomInvite_S2C_Msg",
	[214] = "RoomInvitationReplyCheck_C2S_Msg",
	[215] = "RoomInvitationReplyCheck_S2C_Msg",
	[216] = "RoomLeave_C2S_Msg",
	[217] = "RoomLeave_S2C_Msg",
	[218] = "RoomKick_C2S_Msg",
	[219] = "RoomKick_S2C_Msg",
	[220] = "RoomStart_C2S_Msg",
	[221] = "RoomStart_S2C_Msg",
	[222] = "RoomCancelStart_C2S_Msg",
	[223] = "RoomCancelStart_S2C_Msg",
	[224] = "RoomReadyStart_C2S_Msg",
	[225] = "RoomReadyStart_S2C_Msg",
	[226] = "MatchSuccNtf",
	[227] = "RoomInvitationNtf",
	[228] = "RoomMemberModifyNtf",
	[229] = "RoomModifyModMode_C2S_Msg",
	[230] = "RoomModifyModMode_S2C_Msg",
	[231] = "RoomCancelResultNtf",
	[232] = "BattleReConnectNtf",
	[233] = "RoomWantToJoin_C2S_Msg",
	[234] = "RoomWantToJoin_S2C_Msg",
	[235] = "RoomWantToJoinNtf",
	[236] = "RoomReplayWantToJoin_C2S_Msg",
	[237] = "RoomReplayWantToJoin_S2C_Msg",
	[238] = "RoomStartBattleNtf",
	[239] = "RoomListModMode_C2S_Msg",
	[240] = "RoomListModMode_S2C_Msg",
	[241] = "RoomCommonNtf",
	[242] = "RoomJoinFromQQ_C2S_Msg",
	[243] = "RoomJoinFromQQ_S2C_Msg",
	[244] = "RoomDirectJoinFromRecruit_C2S_Msg",
	[245] = "RoomDirectJoinFromRecruit_S2C_Msg",
	[246] = "MatchSceneCreateSuccNtf",
	[247] = "MatchJoinSuccNtf",
	[248] = "MallBuyMsg_C2S_Msg",
	[249] = "MallBuyMsg_S2C_Msg",
	[250] = "DressUpLimitMallPanelMsg_C2S_Msg",
	[251] = "DressUpLimitMallPanelMsg_S2C_Msg",
	[252] = "MallPanelMsg_C2S_Msg",
	[253] = "MallPanelMsg_S2C_Msg",
	[254] = "ClubList_C2S_Msg",
	[255] = "ClubList_S2C_Msg",
	[256] = "ClubCreate_C2S_Msg",
	[257] = "ClubCreate_S2C_Msg",
	[258] = "ClubInfo_C2S_Msg",
	[259] = "ClubInfo_S2C_Msg",
	[260] = "ClubJoinInfoNtf",
	[261] = "ClubJoin_C2S_Msg",
	[262] = "ClubJoin_S2C_Msg",
	[263] = "ClubModify_C2S_Msg",
	[264] = "ClubModify_S2C_Msg",
	[265] = "ClubModifyPosition_C2S_Msg",
	[266] = "ClubModifyPosition_S2C_Msg",
	[267] = "ClubApplyList_C2S_Msg",
	[268] = "ClubApplyList_S2C_Msg",
	[269] = "ClubQuit_C2S_Msg",
	[270] = "ClubQuit_S2C_Msg",
	[271] = "ClubQuitNtf",
	[272] = "ClubKickOut_C2S_Msg",
	[273] = "ClubKickOut_S2C_Msg",
	[274] = "ClubDissolve_C2S_Msg",
	[275] = "ClubDissolve_S2C_Msg",
	[276] = "ClubCheck_C2S_Msg",
	[277] = "ClubCheck_S2C_Msg",
	[278] = "ClubSetUp_C2S_Msg",
	[279] = "ClubSetUp_S2C_Msg",
	[280] = "ActivityRefreshNtf",
	[281] = "ActivityGetAllRedDotShow_C2S_Msg",
	[282] = "ActivityGetAllRedDotShow_S2C_Msg",
	[283] = "ActivityGetAllLabel_C2S_Msg",
	[284] = "ActivityGetAllLabel_S2C_Msg",
	[285] = "ActivityGetAllTaskInfo_C2S_Msg",
	[286] = "ActivityGetAllTaskInfo_S2C_Msg",
	[287] = "ActivityGetReward_C2S_Msg",
	[288] = "ActivityGetReward_S2C_Msg",
	[289] = "ActivityGetAllReward_C2S_Msg",
	[290] = "ActivityGetAllReward_S2C_Msg",
	[291] = "SignActivityNtf",
	[292] = "DKGiftInfoNtf",
	[293] = "DKGiftInfo_C2S_Msg",
	[294] = "DKGiftInfo_S2C_Msg",
	[295] = "DKBuyGift_C2S_Msg",
	[296] = "DKBuyGift_S2C_Msg",
	[297] = "MailInfosNtf",
	[298] = "SendMail_C2S_Msg",
	[299] = "SendMail_S2C_Msg",
	[300] = "RecvMail_C2S_Msg",
	[301] = "RecvMail_S2C_Msg",
	[302] = "ReadMail_C2S_Msg",
	[303] = "ReadMail_S2C_Msg",
	[304] = "GetAttachments_C2S_Msg",
	[305] = "GetAttachments_S2C_Msg",
	[306] = "DelMail_C2S_Msg",
	[307] = "DelMail_S2C_Msg",
	[308] = "GetVisitorSnapshots_C2S_Msg",
	[309] = "GetVisitorSnapshots_S2C_Msg",
	[310] = "ChangeBasicInfo_C2S_Msg",
	[311] = "ChangeBasicInfo_S2C_Msg",
	[312] = "ChangeFollowingStatus_C2S_Msg",
	[313] = "ChangeFollowingStatus_S2C_Msg",
	[314] = "GetFollowers_C2S_Msg",
	[315] = "GetFollowers_S2C_Msg",
	[316] = "GetFollowings_C2S_Msg",
	[317] = "GetFollowings_S2C_Msg",
	[318] = "StarPlayer_C2S_Msg",
	[319] = "StarPlayer_S2C_Msg",
	[320] = "ChangeAvatar_C2S_Msg",
	[321] = "ChangeAvatar_S2C_Msg",
	[322] = "ChangeShowAvatarType_C2S_Msg",
	[323] = "ChangeShowAvatarType_S2C_Msg",
	[324] = "DeliverResource_C2S_Msg",
	[325] = "DeliverResource_S2C_Msg",
	[326] = "GetResourceMd5_C2S_Msg",
	[327] = "GetResourceMd5_S2C_Msg",
	[328] = "ClientKVConfNtf",
	[329] = "SceneEnter_C2S_Msg",
	[330] = "SceneEnter_S2C_Msg",
	[331] = "SceneExit_C2S_Msg",
	[332] = "SceneExit_S2C_Msg",
	[333] = "SceneEnterNtf",
	[334] = "SceneExitNtf",
	[335] = "SceneReportData_C2S_Msg",
	[336] = "SceneReportData_S2C_Msg",
	[337] = "SceneDataSyncNtf",
	[338] = "SceneReportAction_C2S_Msg",
	[339] = "SceneReportAction_S2C_Msg",
	[340] = "SceneGetServer_C2S_Msg",
	[341] = "SceneGetServer_S2C_Msg",
	[342] = "SceneGetOngoingInfo_C2S_Msg",
	[343] = "SceneGetOngoingInfo_S2C_Msg",
	[344] = "SceneBaseDataNtf",
	[345] = "SceneReconnectNtf",
	[346] = "SceneInteractActionInvite_C2S_Msg",
	[347] = "SceneInteractActionInvite_S2C_Msg",
	[348] = "SceneInteractActionAccept_C2S_Msg",
	[349] = "SceneInteractActionAccept_S2C_Msg",
	[350] = "SceneInteractActionCancel_C2S_Msg",
	[351] = "SceneInteractActionCancel_S2C_Msg",
	[352] = "SceneInteractActionActive_C2S_Msg",
	[353] = "SceneInteractActionActive_S2C_Msg",
	[354] = "SceneInteractActionInviteNtf",
	[355] = "SceneInteractActionAcceptNtf",
	[356] = "SceneInteractActionActiveNtf",
	[357] = "SceneInteractActionCancelNtf",
	[358] = "SceneSettlementNtf",
	[359] = "SceneSettlement_C2S_Msg",
	[360] = "SceneSettlement_S2C_Msg",
	[361] = "ErrorCodeNtf",
	[362] = "ReportEvent_C2S_Msg",
	[363] = "ReportEvent_S2C_Msg",
	[364] = "LetsGoTest_C2S_Msg",
	[365] = "LetsGoTest_S2C_Msg",
	[366] = "LetsGoGetPlayerProfile_C2S_Msg",
	[367] = "LetsGoGetPlayerProfile_S2C_Msg",
	[368] = "LetsGoGetBattleSnapshotRecord_C2S_Msg",
	[369] = "LetsGoGetBattleSnapshotRecord_S2C_Msg",
	[370] = "LetsGoGetBattleDetailRecord_C2S_Msg",
	[371] = "LetsGoGetBattleDetailRecord_S2C_Msg",
	[372] = "LetsGoSetSettings_C2S_Msg",
	[373] = "LetsGoSetSettings_S2C_Msg",
	[374] = "LetsGoGetSettings_C2S_Msg",
	[375] = "LetsGoGetSettings_S2C_Msg",
	[376] = "RoomModModeUnlockNtf",
	[377] = "LetsGoBattleSettlementNtf",
	[378] = "LetsGoGetQualifyingReward_C2S_Msg",
	[379] = "LetsGoGetQualifyingReward_S2C_Msg",
	[380] = "LetsGoSeasonSettlementNtf",
	[381] = "LetsGoOpenQualifyingList_C2S_Msg",
	[382] = "LetsGoOpenQualifyingList_S2C_Msg",
	[383] = "LoginNoticeNtf",
	[384] = "PermitRefreshNtf_S2C_Msg",
	[385] = "PermitGetInfo_C2S_Msg",
	[386] = "PermitGetInfo_S2C_Msg",
	[387] = "PermitTaskComplete_C2S_Msg",
	[388] = "PermitTaskComplete_S2C_Msg",
	[389] = "PermitLevelReward_C2S_Msg",
	[390] = "PermitLevelReward_S2C_Msg",
	[391] = "PermitAllLevelReward_C2S_Msg",
	[392] = "PermitAllLevelReward_S2C_Msg",
	[393] = "PermitInfoNtf",
	[394] = "BattleReportModData_C2S_Msg",
	[395] = "BattleReportModData_S2C_Msg",
	[396] = "BattleModDataLogin_C2S_Msg",
	[397] = "BattleModDataLogin_S2C_Msg",
	[398] = "BattleRequestModData_C2S_Msg",
	[399] = "BattleRequestModData_S2C_Msg",
	[400] = "BattleRequestModDataNtf",
	[401] = "BattleSyncModDataNtf",
	[402] = "BattleRequestModEnd_C2S_Msg",
	[403] = "BattleRequestModEnd_S2C_Msg",
	[404] = "BattleSyncModTime_C2S_Msg",
	[405] = "BattleSyncModTime_S2C_Msg",
	[406] = "BattlePlayerReadyMod_C2S_Msg",
	[407] = "BattlePlayerReadyMod_S2C_Msg",
	[408] = "BattleSeedDateNtf",
	[409] = "BattleModEndNtf",
	[410] = "BattleModStartNtf",
	[411] = "BattleGiveUpMod_C2S_Msg",
	[412] = "BattleGiveUpMod_S2C_Msg",
	[413] = "BattleGetOngoingDsAddr_C2S_Msg",
	[414] = "BattleGetOngoingDsAddr_S2C_Msg",
	[415] = "BattleSongAchNtf",
	[416] = "BattleExitNtf",
	[417] = "BattleSendExpression_C2S_Msg",
	[418] = "BattleSendExpression_S2C_Msg",
	[419] = "BattleExpressionNtf",
	[420] = "RecvRewardListNtf",
	[421] = "GetRankTopNPlayer_C2S_Msg",
	[422] = "GetRankTopNPlayer_S2C_Msg",
	[423] = "GetPlayerRankInfo_C2S_Msg",
	[424] = "GetPlayerRankInfo_S2C_Msg",
	[425] = "GetFriendRankTopNPlayer_C2S_Msg",
	[426] = "GetFriendRankTopNPlayer_S2C_Msg",
	[427] = "GetSceneRankTopNPlayer_C2S_Msg",
	[428] = "GetSceneRankTopNPlayer_S2C_Msg",
	[429] = "AddFriend_C2S_Msg",
	[430] = "AddFriend_S2C_Msg",
	[431] = "RemoveFriend_C2S_Msg",
	[432] = "RemoveFriend_S2C_Msg",
	[433] = "AgreeFriendApply_C2S_Msg",
	[434] = "AgreeFriendApply_S2C_Msg",
	[435] = "DenyFriendApply_C2S_Msg",
	[436] = "DenyFriendApply_S2C_Msg",
	[437] = "GetFriendApplyList_C2S_Msg",
	[438] = "GetFriendApplyList_S2C_Msg",
	[439] = "SearchFriend_C2S_Msg",
	[440] = "SearchFriend_S2C_Msg",
	[441] = "FriendApplyNtf",
	[442] = "FriendAgreeNtf",
	[443] = "FriendDenyNtf",
	[444] = "RemoveRecommendFriend_C2S_Msg",
	[445] = "RemoveRecommendFriend_S2C_Msg",
	[446] = "ModFriendInfoNtf",
	[447] = "PlatFriendInfoNtf",
	[448] = "FriendRemoveNtf",
	[449] = "RecentPlayInfoNtf",
	[450] = "UpdateFriendHotData_C2S_Msg",
	[451] = "UpdateFriendHotData_S2C_Msg",
	[452] = "GiveFriendGift_C2S_Msg",
	[453] = "GiveFriendGift_S2C_Msg",
	[454] = "GetRecommendationFriends_C2S_Msg",
	[455] = "GetRecommendationFriends_S2C_Msg",
	[456] = "AddBlack_C2S_Msg",
	[457] = "AddBlack_S2C_Msg",
	[458] = "RemoveBlack_C2S_Msg",
	[459] = "RemoveBlack_S2C_Msg",
	[460] = "BlackInfoNtf",
	[461] = "AddCouples_C2S_Msg",
	[462] = "AddCouples_S2C_Msg",
	[463] = "RemoveCouples_C2S_Msg",
	[464] = "RemoveCouples_S2C_Msg",
	[465] = "AgreeCouplesApply_C2S_Msg",
	[466] = "AgreeCouplesApply_S2C_Msg",
	[467] = "DenyCouplesApply_C2S_Msg",
	[468] = "DenyCouplesApply_S2C_Msg",
	[469] = "CouplesApplyNtf",
	[470] = "CouplesAgreeNtf",
	[471] = "CouplesDenyNtf",
	[472] = "UpdateSceneNeighborHotData_C2S_Msg",
	[473] = "UpdateSceneNeighborHotData_S2C_Msg",
	[474] = "SceneNeighborInfoNtf",
	[475] = "QQInvity_C2S_Msg",
	[476] = "QQInvity_S2C_Msg",
	[477] = "AddFriendNoticeServer_C2S_Msg",
	[478] = "AddFriendNoticeServer_S2C_Msg",
	[479] = "ChatSend_C2S_Msg",
	[480] = "ChatSend_S2C_Msg",
	[481] = "ChatOnInput_C2S_Msg",
	[482] = "ChatOnInput_S2C_Msg",
	[483] = "ChatGroupOperationNtf",
	[484] = "GetTailMessages_C2S_Msg",
	[485] = "GetTailMessages_S2C_Msg",
	[486] = "GetPreMessages_C2S_Msg",
	[487] = "GetPreMessages_S2C_Msg",
	[488] = "ChatPull_C2S_Msg",
	[489] = "ChatPull_S2C_Msg",
	[490] = "ChatMsgNtf",
	[491] = "ChatMsgRedDotNtf",
	[492] = "ChatClearNotRead_C2S_Msg",
	[493] = "ChatClearNotRead_S2C_Msg",
	[494] = "ChatRemoveRecord_C2S_Msg",
	[495] = "ChatRemoveRecord_S2C_Msg",
	[496] = "GroupChatMemModifyNtf",
	[497] = "ChatCreateGroup_C2S_Msg",
	[498] = "ChatCreateGroup_S2C_Msg",
	[499] = "ChatQuit_C2S_Msg",
	[500] = "ChatQuit_S2C_Msg",
	[501] = "ChatGetQuickTextList_C2S_Msg",
	[502] = "ChatGetQuickTextList_S2C_Msg",
	[503] = "ChatReport_C2S_Msg",
	[504] = "ChatReport_S2C_Msg",
	[505] = "ChatRoomRecruit_C2S_Msg",
	[506] = "ChatRoomRecruit_S2C_Msg",
	[507] = "ChatChannelSwitch_C2S_Msg",
	[508] = "ChatChannelSwitch_S2C_Msg",
	[509] = "BagUseItemMsg_C2S_Msg",
	[510] = "BagUseItemMsg_S2C_Msg",
	[511] = "BagSellItemMsg_C2S_Msg",
	[512] = "BagSellItemMsg_S2C_Msg",
	[513] = "BagMoveItemMsg_C2S_Msg",
	[514] = "BagMoveItemMsg_S2C_Msg",
	[515] = "BagDestroyItemMsg_C2S_Msg",
	[516] = "BagDestroyItemMsg_S2C_Msg",
	[517] = "CheckEquipItemsExpireMsg_C2S_Msg",
	[518] = "CheckEquipItemsExpireMsg_S2C_Msg",
	[519] = "BagRewardNtfFinishMsg_C2S_Msg",
	[520] = "BagRewardNtfFinishMsg_S2C_Msg",
	[521] = "BagItemNumUpToMaxNtf",
	[522] = "BagCommonGetItemsNtf",
	[523] = "BagItemNumUpToMaxReplaceNtf",
	[524] = "PvEGetEntryList_C2S_Msg",
	[525] = "PvEGetEntryList_S2C_Msg",
	[526] = "PvEGetEntryInfo_C2S_Msg",
	[527] = "PvEGetEntryInfo_S2C_Msg",
	[528] = "PvEGetChapterInfo_C2S_Msg",
	[529] = "PvEGetChapterInfo_S2C_Msg",
	[530] = "PvEGetStageInfo_C2S_Msg",
	[531] = "PvEGetStageInfo_S2C_Msg",
	[532] = "PvEClearRedDot_C2S_Msg",
	[533] = "PvEClearRedDot_S2C_Msg",
	[534] = "PvEGetReward_C2S_Msg",
	[535] = "PvEGetReward_S2C_Msg",
	[536] = "PvEGetEntryProcessReward_C2S_Msg",
	[537] = "PvEGetEntryProcessReward_S2C_Msg",
	[538] = "PvEGetChapterRowColumnReward_C2S_Msg",
	[539] = "PvEGetChapterRowColumnReward_S2C_Msg",
	[540] = "PvEStartStage_C2S_Msg",
	[541] = "PvEStartStage_S2C_Msg",
	[542] = "DeliverResources_C2S_Msg",
	[543] = "DeliverResources_S2C_Msg",
	[544] = "DirGetPlayerInfo_C2S_Msg",
	[545] = "DirGetPlayerInfo_S2C_Msg",
	[546] = "DevEnvResInfoNtf",
	[547] = "ExpAndLevelChangeNtf",
	[548] = "TeamRecruitPublish_C2S_Msg",
	[549] = "TeamRecruitPublish_S2C_Msg",
	[550] = "TeamRecruitQuery_C2S_Msg",
	[551] = "TeamRecruitQuery_S2C_Msg",
	[552] = "TeamRecruitJoin_C2S_Msg",
	[553] = "TeamRecruitJoin_S2C_Msg",
	[554] = "ClientSetCache_C2S_Msg",
	[555] = "ClientSetCache_S2C_Msg",
	[556] = "ClientDelCache_C2S_Msg",
	[557] = "ClientDelCache_S2C_Msg",
	[558] = "GetTask_C2S_Msg",
	[559] = "GetTask_S2C_Msg",
	[560] = "TaskChangeNtf",
	[561] = "ChangePlayerRankGeoInfo_C2S_Msg",
	[562] = "ChangePlayerRankGeoInfo_S2C_Msg",
	[563] = "GetGeoRankTopNPlayer_C2S_Msg",
	[564] = "GetGeoRankTopNPlayer_S2C_Msg",
	[565] = "AddRelation_C2S_Msg",
	[566] = "AddRelation_S2C_Msg",
	[567] = "RemoveRelation_C2S_Msg",
	[568] = "RemoveRelation_S2C_Msg",
	[569] = "AgreeRelation_C2S_Msg",
	[570] = "AgreeRelation_S2C_Msg",
	[571] = "DenyRelation_C2S_Msg",
	[572] = "DenyRelation_S2C_Msg",
	[573] = "LobbyGetDsServer_C2S_Msg",
	[574] = "LobbyGetDsServer_S2C_Msg",
	[575] = "LobbyJumpNtf",
	[576] = "SearchPlayer_C2S_Msg",
	[577] = "SearchPlayer_S2C_Msg",
	[578] = "SeasonInfoNtf",
	[579] = "RefreshSeasonPanel_C2S_Msg",
	[580] = "RefreshSeasonPanel_S2C_Msg",
	[581] = "GetPlayerColdData_C2S_Msg",
	[582] = "GetPlayerColdData_S2C_Msg",
	[583] = "PermitUnlock_C2S_Msg",
	[584] = "PermitUnlock_S2C_Msg",
	[585] = "PlayModeUnlockNtf",
	[586] = "RoomStartNtf",
	[587] = "RoomConfirmStart_C2S_Msg",
	[588] = "RoomStartMatchCancelNtf",
	[589] = "RoomConfirmStart_S2C_Msg",
	[590] = "RoomLeaderTransit_C2S_Msg",
	[591] = "RoomLeaderTransit_S2C_Msg",
	[592] = "RoomDisband_C2S_Msg",
	[593] = "RoomDisband_S2C_Msg",
	[594] = "RoomLobbyGather_C2S_Msg",
	[595] = "RoomLobbyGather_S2C_Msg",
	[596] = "RoomLobbyGatherNtf",
	[597] = "RoomLobbyTransit_C2S_Msg",
	[598] = "RoomLobbyTransit_S2C_Msg",
	[599] = "DeletePlayerRankGeoInfo_C2S_Msg",
	[600] = "DeletePlayerRankGeoInfo_S2C_Msg",
	[601] = "PlayerCoinChangeNtf",
	[602] = "PlayerItemChangeNtf",
	[603] = "RelationApplyAddNtf",
	[604] = "RelationApplyRemoveNtf",
	[605] = "RelationAddNtf",
	[606] = "RelationRemoveNtf",
	[607] = "TeamRecruitQuickJoin_C2S_Msg",
	[608] = "TeamRecruitQuickJoin_S2C_Msg",
	[609] = "PermitBuyLevel_C2S_Msg",
	[610] = "PermitBuyLevel_S2C_Msg",
	[611] = "PermitUnlockLevel_C2S_Msg",
	[612] = "UnLockMatchTypeNtf",
	[613] = "SendGoldCoin_C2S_Msg",
	[614] = "SendGoldCoin_S2C_Msg",
	[615] = "UgcList_C2S_Msg",
	[616] = "UgcList_S2C_Msg",
	[617] = "UgcCreate_C2S_Msg",
	[618] = "UgcCreate_S2C_Msg",
	[619] = "UgcDelete_C2S_Msg",
	[620] = "UgcDelete_S2C_Msg",
	[621] = "SaveWork_C2S_Msg",
	[622] = "SaveWork_S2C_Msg",
	[623] = "GetSaveWork_C2S_Msg",
	[624] = "GetSaveWork_S2C_Msg",
	[625] = "DrawRaffle_C2S_Msg",
	[626] = "DrawRaffle_S2C_Msg",
	[627] = "PlatShareActiveList_C2S_Msg",
	[628] = "PlatShareActiveList_S2C_Msg",
	[629] = "PlatShareActiveSend_C2S_Msg",
	[630] = "PlatShareActiveSend_S2C_Msg",
	[631] = "PlatShareActiveReward_C2S_Msg",
	[632] = "PlatShareActiveReward_S2C_Msg",
	[633] = "ActivityListAll_C2S_Msg",
	[634] = "ActivityListAll_S2C_Msg",
	[635] = "ActivityReadDotNtf",
	[636] = "ActivityClickRedDot_C2S_Msg",
	[637] = "ActivityClickRedDot_S2C_Msg",
	[638] = "ListLuckyMoney_C2S_Msg",
	[639] = "ListLuckyMoney_S2C_Msg",
	[640] = "ShareLuckyMoney_C2S_Msg",
	[641] = "ShareLuckyMoney_S2C_Msg",
	[642] = "LoginFromBacklinksParams_C2S_Msg",
	[643] = "LoginFromBacklinksParams_S2C_Msg",
	[644] = "GetLuckyMoneyReward_C2S_Msg",
	[645] = "GetLuckyMoneyReward_S2C_Msg",
	[646] = "GetLuckyMoneyDetailInfo_C2S_Msg",
	[647] = "GetLuckyMoneyDetailInfo_S2C_Msg",
	[648] = "GetRechargeConf_C2S_Msg",
	[649] = "GetRechargeConf_S2C_Msg",
	[650] = "RechargeConfNtf",
	[651] = "GetPlayerColdDataByOpenId_C2S_Msg",
	[652] = "GetPlayerColdDataByOpenId_S2C_Msg",
	[653] = "GetUidFromOpenId_C2S_Msg",
	[654] = "GetUidFromOpenId_S2C_Msg",
	[655] = "MallGetShopCommodity_C2S_Msg",
	[656] = "MallGetShopCommodity_S2C_Msg",
	[657] = "DreamNewStarTaskList_C2S_Msg",
	[658] = "DreamNewStarTaskList_S2C_Msg",
	[659] = "FittingSlotSave_C2S_Msg",
	[660] = "FittingSlotSave_S2C_Msg",
	[661] = "FittingSlotSelect_C2S_Msg",
	[662] = "FittingSlotSelect_S2C_Msg",
	[663] = "BagDressUpItem_C2S_Msg",
	[664] = "BagDressUpItem_S2C_Msg",
	[665] = "MallBatchGetCommodity_C2S_Msg",
	[666] = "MallBatchGetCommodity_S2C_Msg",
	[667] = "Recharge_C2S_Msg",
	[668] = "Recharge_S2C_Msg",
	[669] = "UgcCopy_C2S_Msg",
	[670] = "UgcCopy_S2C_Msg",
	[671] = "UgcMapModify_C2S_Msg",
	[672] = "UgcMapModify_S2C_Msg",
	[673] = "UgcRegain_C2S_Msg",
	[674] = "UgcRegain_S2C_Msg",
	[675] = "UgcPublish_C2S_Msg",
	[676] = "UgcPublish_S2C_Msg",
	[677] = "UgcTakeOff_C2S_Msg",
	[678] = "UgcTakeOff_S2C_Msg",
	[679] = "GetRechargeLevelReward_C2S_Msg",
	[680] = "GetRechargeLevelReward_S2C_Msg",
	[681] = "WealthBankCheckIn_C2S_Msg",
	[682] = "WealthBankCheckIn_S2C_Msg",
	[683] = "WealthBankGetInfo_C2S_Msg",
	[684] = "WealthBankGetInfo_S2C_Msg",
	[685] = "WealthBankReceiveDeposit_C2S_Msg",
	[686] = "WealthBankReceiveDeposit_S2C_Msg",
	[687] = "SquadGetInfo_C2S_Msg",
	[688] = "SquadGetInfo_S2C_Msg",
	[689] = "SquadJoin_C2S_Msg",
	[690] = "SquadJoin_S2C_Msg",
	[691] = "SquadTaskInfo_C2S_Msg",
	[692] = "SquadTaskInfo_S2C_Msg",
	[693] = "RechargeLvChangeNtf",
	[694] = "UgcApplyKeyInfo_C2S_Msg",
	[695] = "UgcMapSet_C2S_Msg",
	[696] = "UgcMapSet_S2C_Msg",
	[699] = "MidasBuyGoodsNtf",
	[700] = "RechargeDepositPurchase_C2S_Msg",
	[701] = "RechargeDepositPurchase_S2C_Msg",
	[702] = "RechargeDepositPurchaseNtf",
	[703] = "RechargeDepositGetReward_C2S_Msg",
	[704] = "RechargeDepositGetReward_S2C_Msg",
	[705] = "RechargeDepositFetchPrice_C2S_Msg",
	[706] = "RechargeDepositFetchPrice_S2C_Msg",
	[707] = "RechargeDepositConfigNtf",
	[708] = "BagCheckItemExpire_C2S_Msg",
	[709] = "BagCheckItemExpire_S2C_Msg",
	[710] = "FirstChargeTaskInfo_C2S_Msg",
	[711] = "FirstChargeTaskInfo_S2C_Msg",
	[712] = "UgcApplyKeyInfo_S2C_Msg",
	[713] = "UgcPublishList_C2S_Msg",
	[714] = "UgcPublishList_S2C_Msg",
	[715] = "GaveLike_C2S_Msg",
	[716] = "GaveLike_S2C_Msg",
	[717] = "Collect_C2S_Msg",
	[718] = "Collect_S2C_Msg",
	[719] = "Subscribe_C2S_Msg",
	[720] = "Subscribe_S2C_Msg",
	[721] = "MallBatchBuyMsg_C2S_Msg",
	[722] = "MallBatchBuyMsg_S2C_Msg",
	[723] = "MallDirectBuyNtf",
	[724] = "MallRealTimeNtf",
	[725] = "GetMessageSlipList_C2S_Msg",
	[726] = "GetMessageSlipList_S2C_Msg",
	[727] = "AddMessageSlip_C2S_Msg",
	[728] = "AddMessageSlip_S2C_Msg",
	[729] = "DeleteMessageSlip_C2S_Msg",
	[730] = "DeleteMessageSlip_S2C_Msg",
	[731] = "GetMessageSlipDetail_C2S_Msg",
	[732] = "GetMessageSlipDetail_S2C_Msg",
	[733] = "GetMessageCommentList_C2S_Msg",
	[734] = "GetMessageCommentList_S2C_Msg",
	[735] = "AddMessageComment_C2S_Msg",
	[736] = "AddMessageComment_S2C_Msg",
	[737] = "DeleteMessageComment_C2S_Msg",
	[738] = "DeleteMessageComment_S2C_Msg",
	[739] = "GetMessageFavourList_C2S_Msg",
	[740] = "GetMessageFavourList_S2C_Msg",
	[741] = "AddMessageFavour_C2S_Msg",
	[742] = "AddMessageFavour_S2C_Msg",
	[743] = "DeleteMessageFavour_C2S_Msg",
	[744] = "DeleteMessageFavour_S2C_Msg",
	[745] = "MessageSlipRedDotNtf",
	[746] = "MidasPayResult_C2S_Msg",
	[747] = "MidasPayResult_S2C_Msg",
	[748] = "UgcOperate_C2S_Msg",
	[749] = "UgcOperate_S2C_Msg",
	[750] = "UgcOperateCancel_C2S_Msg",
	[751] = "UgcOperateCancel_S2C_Msg",
	[752] = "ClickMessageSlipRedDot_C2S_Msg",
	[753] = "ClickMessageSlipRedDot_S2C_Msg",
	[754] = "MatchProcessNtf",
	[755] = "UgcMapScreen_C2S_Msg",
	[756] = "UgcMapScreen_S2C_Msg",
	[757] = "UgcMapType_C2S_Msg",
	[758] = "UgcMapType_S2C_Msg",
	[759] = "UgcMapSearch_C2S_Msg",
	[760] = "UgcMapSearch_S2C_Msg",
	[761] = "UgcMapPublishModify_C2S_Msg",
	[762] = "UgcMapPublishModify_S2C_Msg",
	[763] = "GetMailBriefs_C2S_Msg",
	[764] = "GetMailBriefs_S2C_Msg",
	[766] = "MailBriefsNtf",
	[767] = "MailStateNtf",
	[768] = "ChangeName_C2S_Msg",
	[769] = "ChangeName_S2C_Msg",
	[770] = "FittingSingleItem_C2S_Msg",
	[771] = "FittingSingleItem_S2C_Msg",
	[772] = "MailDelNtf",
	[773] = "MallCommoditySetRedPointShow_C2S_Msg",
	[774] = "MallCommoditySetRedPointShow_S2C_Msg",
	[775] = "UgcOpSubscribe_C2S_Msg",
	[776] = "UgcOpSubscribe_S2C_Msg",
	[777] = "UgcMySubscribe_C2S_Msg",
	[778] = "UgcMySubscribe_S2C_Msg",
	[779] = "UgcMyFans_C2S_Msg",
	[780] = "UgcMyFans_S2C_Msg",
	[781] = "UgcPublishDetails_C2S_Msg",
	[782] = "UgcPublishDetails_S2C_Msg",
	[783] = "RechargeMonthCard_C2S_Msg",
	[784] = "RechargeMonthCard_S2C_Msg",
	[787] = "ReceiveMonthCardDailyItem_C2S_Msg",
	[788] = "ReceiveMonthCardDailyItem_S2C_Msg",
	[789] = "MonthCardDeliverGoodsNtf",
	[790] = "UgcObjectOperate_C2S_Msg",
	[791] = "UgcObjectOperate_S2C_Msg",
	[792] = "UgcListForObject_C2S_Msg",
	[793] = "UgcListForObject_S2C_Msg",
	[794] = "UgcPublishRegain_C2S_Msg",
	[795] = "UgcPublishRegain_S2C_Msg",
	[796] = "UgcOpSubTop_C2S_Msg",
	[797] = "UgcOpSubTop_S2C_Msg",
	[798] = "CheckInPlanGetMakeUpTicket_C2S_Msg",
	[799] = "CheckInPlanGetMakeUpTicket_S2C_Msg",
	[800] = "CheckInPlanGetInfo_C2S_Msg",
	[801] = "CheckInPlanGetInfo_S2C_Msg",
	[802] = "CheckInPlanCheckIn_C2S_Msg",
	[803] = "CheckInPlanCheckIn_S2C_Msg",
	[804] = "CheckInPlanDraw_C2S_Msg",
	[805] = "CheckInPlanDraw_S2C_Msg",
	[806] = "UgcCreateGroupObject_C2S_Msg",
	[807] = "UgcCreateGroupObject_S2C_Msg",
	[808] = "UgcSinglePassLevel_C2S_Msg",
	[809] = "UgcSinglePassLevel_S2C_Msg",
	[810] = "UgcCreateRoom_C2S_Msg",
	[811] = "UgcCreateRoom_S2C_Msg",
	[812] = "UgcRoomList_C2S_Msg",
	[813] = "UgcRoomList_S2C_Msg",
	[814] = "UgcJoinRoom_C2S_Msg",
	[815] = "UgcJoinRoom_S2C_Msg",
	[816] = "UgcExitRoom_C2S_Msg",
	[817] = "UgcExitRoom_S2C_Msg",
	[818] = "PlayerGetRecordItemForLevel_C2S_Msg",
	[819] = "PlayerGetRecordItemForLevel_S2C_Msg",
	[820] = "PlatCommon_C2S_Msg",
	[821] = "PlatCommon_S2C_Msg",
	[822] = "UgcAccept_C2S_Msg",
	[823] = "UgcAccept_S2C_Msg",
	[824] = "BatchGetPlayerPublicInfo_C2S_Msg",
	[825] = "BatchGetPlayerPublicInfo_S2C_Msg",
	[826] = "DirCheckPlayerNickName_C2S_Msg",
	[827] = "DirCheckPlayerNickName_S2C_Msg",
	[828] = "LobbyEnterPlayerDs_C2S_Msg",
	[829] = "LobbyEnterPlayerDs_S2C_Msg",
	[830] = "RelationStateNtf",
	[831] = "FetchTopRank_C2S_Msg",
	[832] = "FetchTopRank_S2C_Msg",
	[833] = "UgcCommonSave_C2S_Msg",
	[834] = "UgcCommonSave_S2C_Msg",
	[835] = "UgcGetCommonSave_C2S_Msg",
	[836] = "UgcGetCommonSave_S2C_Msg",
	[837] = "LobbyRandomEnter_C2S_Msg",
	[838] = "LobbyRandomEnter_S2C_Msg",
	[839] = "ChangeLabelInfo_C2S_Msg",
	[840] = "ChangeLabelInfo_S2C_Msg",
	[841] = "ChangeStateInfo_C2S_Msg",
	[842] = "ChangeStateInfo_S2C_Msg",
	[843] = "getUgcAccept_C2S_Msg",
	[844] = "getUgcAccept_S2C_Msg",
	[845] = "GetUgcCoCreateInviteCode_C2S_Msg",
	[846] = "GetUgcCoCreateInviteCode_S2C_Msg",
	[847] = "UgcCoCreateEditor_C2S_Msg",
	[848] = "UgcCoCreateEditor_S2C_Msg",
	[849] = "UgcCoCreateExitEditor_C2S_Msg",
	[850] = "UgcCoCreateExitEditor_S2C_Msg",
	[851] = "LobbyExit_C2S_Msg",
	[852] = "LobbyExit_S2C_Msg",
	[853] = "QAInvestStatusNtf",
	[854] = "QAInvestConfigListNtf",
	[855] = "UgcModifyName_C2S_Msg",
	[856] = "UgcModifyName_S2C_Msg",
	[857] = "UploadGuideStep_C2S_Msg",
	[858] = "UploadGuideStep_S2C_Msg",
	[859] = "GetUgcAccept_S2C_Msg",
	[860] = "GetUgcAccept_C2S_Msg",
	[861] = "SensitiveFilter_C2S_Msg",
	[862] = "SensitiveFilter_S2C_Msg",
	[863] = "PermitUnlockLevel_S2C_Msg",
	[864] = "GetRecommendFriend_C2S_Msg",
	[865] = "GetRecommendFriend_S2C_Msg",
	[866] = "UgcGetUgcPlayerInfo_C2S_Msg",
	[867] = "UgcGetUgcPlayerInfo_S2C_Msg",
	[868] = "UgcGetMaps_C2S_Msg",
	[869] = "UgcGetMaps_S2C_Msg",
	[870] = "PlayerBanInfoNtf",
	[871] = "FollowPlayer_C2S_Msg",
	[872] = "FollowPlayer_S2C_Msg",
	[873] = "CancelFollowPlayer_C2S_Msg",
	[874] = "CancelFollowPlayer_S2C_Msg",
	[875] = "ResourceChangeNtf",
	[876] = "UgcRoomPositionUpdate_C2S_Msg",
	[877] = "UgcRoomPositionUpdate_S2C_Msg",
	[878] = "UgcSingleStageStart_C2S_Msg",
	[879] = "UgcSingleStageStart_S2C_Msg",
	[880] = "UgcDailyStageChangeMap_C2S_Msg",
	[881] = "UgcDailyStageChangeMap_S2C_Msg",
	[882] = "UgcDailyStageReset_C2S_Msg",
	[883] = "UgcDailyStageReset_S2C_Msg",
	[884] = "GetUgcDailyStageInfo_C2S_Msg",
	[885] = "GetUgcDailyStageInfo_S2C_Msg",
	[886] = "UgcDelLifeItem_C2S_Msg",
	[887] = "UgcDelLifeItem_S2C_Msg",
	[888] = "UgcRoomPreStart_C2S_Msg",
	[889] = "UgcRoomPreStart_S2C_Msg",
	[890] = "SelectStarterItems_C2S_Msg",
	[891] = "SelectStarterItems_S2C_Msg",
	[892] = "UgcDeleteGroup_C2S_Msg",
	[893] = "UgcDeleteGroup_S2C_Msg",
	[894] = "UgcPublishGroup_C2S_Msg",
	[895] = "UgcPublishGroup_S2C_Msg",
	[896] = "UgcScreenForObject_C2S_Msg",
	[897] = "UgcScreenForObject_S2C_Msg",
	[898] = "UgcTakeOffForObject_C2S_Msg",
	[899] = "UgcTakeOffForObject_S2C_Msg",
	[900] = "UgcPublishDetailsForObject_C2S_Msg",
	[901] = "UgcPublishDetailsForObject_S2C_Msg",
	[902] = "CheckPlayerNickName_C2S_Msg",
	[903] = "CheckPlayerNickName_S2C_Msg",
	[904] = "UgcQuickJoin_C2S_Msg",
	[905] = "UgcQuickJoin_S2C_Msg",
	[906] = "PlayerProfileChangeNtf",
	[907] = "UgcCollectForObject_C2S_Msg",
	[908] = "UgcCollectForObject_S2C_Msg",
	[909] = "UgcCheckWhitelistForObject_C2S_Msg",
	[910] = "UgcCheckWhitelistForObject_S2C_Msg",
	[911] = "CreateRole_C2S_Msg",
	[912] = "CreateRole_S2C_Msg",
	[913] = "LobbyInvite_C2S_Msg",
	[914] = "LobbyInvite_S2C_Msg",
	[915] = "LobbyReplyInvite_C2S_Msg",
	[916] = "LobbyReplyInvite_S2C_Msg",
	[917] = "LobbyInviteNtf",
	[918] = "LobbyInviteReplyNtf",
	[919] = "UgcCoverCheck_C2S_Msg",
	[920] = "UgcCoverCheck_S2C_Msg",
	[921] = "UgcMapInfoNtf",
	[922] = "LetsGoClientUploadSettings_C2S_Msg",
	[923] = "LetsGoClientUploadSettings_S2C_Msg",
	[924] = "PlayerPray_C2S_Msg",
	[925] = "PlayerPray_S2C_Msg",
	[926] = "ChatGroupKeyGet_C2S_Msg",
	[927] = "ChatGroupKeyGet_S2C_Msg",
	[928] = "GetForwardMessages_C2S_Msg",
	[929] = "GetForwardMessages_S2C_Msg",
	[930] = "ChangeRelation_C2S_Msg",
	[931] = "ChangeRelation_S2C_Msg",
	[932] = "RewardSnsShare_C2S_Msg",
	[933] = "RewardSnsShare_S2C_Msg",
	[934] = "UgcGroupOfficalList_C2S_Msg",
	[935] = "UgcGroupOfficalList_S2C_Msg",
	[936] = "UgcGroupSearch_C2S_Msg",
	[937] = "UgcGroupSearch_S2C_Msg",
	[938] = "UgcStageChange_C2S_Msg",
	[939] = "UgcStageChange_S2C_Msg",
	[940] = "NetworkInfoUpload_C2S_Msg",
	[941] = "NetworkInfoUpload_S2C_Msg",
	[942] = "ChangeGender_C2S_Msg",
	[943] = "ChangeGender_S2C_Msg",
	[944] = "GetUnlockedSlotId_C2S_Msg",
	[945] = "GetUnlockedSlotId_S2C_Msg",
	[946] = "AccountUnBind_C2S_Msg",
	[947] = "AccountUnBind_S2C_Msg",
	[948] = "GetAccountBindInfo_C2S_Msg",
	[949] = "GetAccountBindInfo_S2C_Msg",
	[950] = "RoomRecommendList_C2S_Msg",
	[951] = "RoomRecommendList_S2C_Msg",
	[952] = "RoomQuickJoin_C2S_Msg",
	[953] = "RoomQuickJoin_S2C_Msg",
	[954] = "RoomInfoQueryByRoomNo_C2S_Msg",
	[955] = "RoomInfoQueryByRoomNo_S2C_Msg",
	[956] = "RoomJoin_C2S_Msg",
	[957] = "RoomPositionExchange_C2S_Msg",
	[958] = "RoomPositionExchange_S2C_Msg",
	[959] = "RoomPositionExchangeNtf",
	[960] = "RoomPositionExchangeReply_C2S_Msg",
	[961] = "ABTestInfoQuery_C2S_Msg",
	[962] = "ABTestInfoQuery_S2C_Msg",
	[963] = "GetItemPackagePickPickedNum_C2S_Msg",
	[964] = "GetItemPackagePickPickedNum_S2C_Msg",
	[965] = "IDCPingSvrListNtf",
	[966] = "RelationChangeNtf",
	[967] = "RoomInfoChange_C2S_Msg",
	[968] = "RoomInfoChange_S2C_Msg",
	[969] = "RoomReady_C2S_Msg",
	[970] = "RoomReady_S2C_Msg",
	[971] = "RoomStateCheck_C2S_Msg",
	[972] = "RoomStateCheck_S2C_Msg",
	[973] = "RelationIntimacyNtf",
	[974] = "UgcDownload_C2S_Msg",
	[975] = "UgcDownload_S2C_Msg",
	[976] = "RoomMapDownloadStat_C2S_Msg",
	[977] = "RoomMapDownloadStat_S2C_Msg",
	[978] = "RoomMapDownloadStatNtf",
	[979] = "RoomPositionExchangeReply_S2C_Msg",
	[980] = "GetAccountState_C2S_Msg",
	[981] = "GetAccountState_S2C_Msg",
	[982] = "RoomAddRobot_C2S_Msg",
	[983] = "RoomAddRobot_S2C_Msg",
	[984] = "RoomRemoveRobot_C2S_Msg",
	[985] = "RoomRemoveRobot_S2C_Msg",
	[986] = "RoomJoin_S2C_Msg",
	[987] = "CheckRandomNicknames_C2S_Msg",
	[988] = "CheckRandomNicknames_S2C_Msg",
	[989] = "ListFeatureUnlock_C2S_Msg",
	[990] = "ListFeatureUnlock_S2C_Msg",
	[991] = "FeatureUnlockNtf",
	[992] = "GetRandomNickname_C2S_Msg",
	[993] = "GetRandomNickname_S2C_Msg",
	[994] = "UgcGenAiImage_C2S_Msg",
	[995] = "UgcGenAiImage_S2C_Msg",
	[996] = "UgcAiImageGenSucNtf",
	[997] = "UgcSaveResultNtf",
	[998] = "UgcCoverCheckResultNtf",
	[999] = "UgcAiChangeColor_C2S_Msg",
	[1000] = "UgcAiChangeColor_S2C_Msg",
	[1001] = "UgcAiChangeColorResultNtf",
	[1002] = "UgcDownLoadPublish_C2S_Msg",
	[1003] = "UgcDownLoadPublish_S2C_Msg",
	[1004] = "UgcModifyPublishMeta_C2S_Msg",
	[1005] = "UgcModifyPublishMeta_S2C_Msg",
	[1006] = "UgcUpdatePublishMeta_C2S_Msg",
	[1007] = "UgcUpdatePublishMeta_S2C_Msg",
	[1008] = "UgcPublishMetaRecord_C2S_Msg",
	[1009] = "UgcPublishMetaRecord_S2C_Msg",
	[1010] = "UgcRoomChangeMap_C2S_Msg",
	[1011] = "UgcRoomChangeMap_S2C_Msg",
	[1012] = "BulletinPublish_C2S_Msg",
	[1013] = "BulletinPublish_S2C_Msg",
	[1014] = "BulletinOpen_C2S_Msg",
	[1015] = "BulletinOpen_S2C_Msg",
	[1016] = "GetBulletinList_C2S_Msg",
	[1017] = "GetBulletinList_S2C_Msg",
	[1018] = "PartyInfoNtf",
	[1019] = "XiaoWoWatering_C2S_Msg",
	[1020] = "XiaoWoWatering_S2C_Msg",
	[1021] = "XiaoWoShake_C2S_Msg",
	[1022] = "XiaoWoShake_S2C_Msg",
	[1023] = "XiaoWoEnter_C2S_Msg",
	[1024] = "XiaoWoEnter_S2C_Msg",
	[1025] = "XiaoWoExit_C2S_Msg",
	[1026] = "XiaoWoExit_S2C_Msg",
	[1027] = "XiaoWoLike_C2S_Msg",
	[1028] = "XiaoWoLike_S2C_Msg",
	[1029] = "XiaoWoStar_C2S_Msg",
	[1030] = "XiaoWoStar_S2C_Msg",
	[1031] = "XiaoWoLevelUp_C2S_Msg",
	[1032] = "XiaoWoLevelUp_S2C_Msg",
	[1033] = "XiaoWoInfoNtf",
	[1034] = "XiaoWoItemInteract_C2S_Msg",
	[1035] = "XiaoWoItemInteract_S2C_Msg",
	[1036] = "BagCommonGetItemsNtfCommit_C2S_Msg",
	[1037] = "BagCommonGetItemsNtfCommit_S2C_Msg",
	[1038] = "GetUgcStarWorldInfo_C2S_Msg",
	[1039] = "GetUgcStarWorldInfo_S2C_Msg",
	[1040] = "AvailablePlatNicknameNtf",
	[1041] = "PlayerBlessBagInfoNtf",
	[1042] = "PlatPrivilegesNtf",
	[1043] = "InterServerPuzzleGetInfo_C2S_Msg",
	[1044] = "InterServerPuzzleGetInfo_S2C_Msg",
	[1045] = "InterServerPuzzleBuy_C2S_Msg",
	[1046] = "InterServerPuzzleBuy_S2C_Msg",
	[1047] = "InterServerPuzzleUnlockNtf",
	[1048] = "InterServerPuzzleGetReward_C2S_Msg",
	[1049] = "InterServerPuzzleGetReward_S2C_Msg",
	[1050] = "InterServerGiftGetInfo_C2S_Msg",
	[1051] = "InterServerGiftGetInfo_S2C_Msg",
	[1052] = "InterServerGiftBuy_C2S_Msg",
	[1053] = "InterServerGiftBuy_S2C_Msg",
	[1054] = "InterServerGiftUnlockNtf",
	[1055] = "InterServerGiftGetReward_C2S_Msg",
	[1056] = "InterServerGiftGetReward_S2C_Msg",
	[1057] = "UgcGetCreatorInfo_C2S_Msg",
	[1058] = "UgcGetCreatorInfo_S2C_Msg",
	[1059] = "GetRaffleAward_C2S_Msg",
	[1060] = "GetRaffleAward_S2C_Msg",
	[1061] = "RefreshPlayerPayInfo_S2C_Msg",
	[1062] = "RefreshPlayerPayInfoNtf",
	[1063] = "RoomMapDownloadReminder_C2S_Msg",
	[1064] = "RoomMapDownloadReminder_S2C_Msg",
	[1065] = "RoomMapDownloadReminderNtf",
	[1066] = "UgcRecommendSubscribe_C2S_Msg",
	[1067] = "UgcRecommendSubscribe_S2C_Msg",
	[1068] = "UgcRoomRecommendList_C2S_Msg",
	[1069] = "UgcRoomRecommendList_S2C_Msg",
	[1070] = "XiaoWoItemLock_C2S_Msg",
	[1071] = "XiaoWoItemLock_S2C_Msg",
	[1072] = "XiaoWoDSInfoNtf",
	[1073] = "GetChatGroupMemberList_C2S_Msg",
	[1074] = "GetChatGroupMemberList_S2C_Msg",
	[1075] = "UpdateMailState_C2S_Msg",
	[1076] = "UpdateMailState_S2C_Msg",
	[1077] = "GetPingSvrInfo_C2S_Msg",
	[1078] = "GetPingSvrInfo_S2C_Msg",
	[1079] = "GetTranslateInfo_C2S_Msg",
	[1080] = "GetTranslateInfo_S2C_Msg",
	[1081] = "WithdrawParentAuth_C2S_Msg",
	[1082] = "WithdrawParentAuth_S2C_Msg",
	[1083] = "WithdrawParentAuthNtf",
	[1084] = "QueryParentAuthStatus_C2S_Msg",
	[1085] = "QueryParentAuthStatus_S2C_Msg",
	[1086] = "QueryParentAuthStatusNtf",
	[1087] = "RefreshPlayerPayInfo_C2S_Msg",
	[1088] = "XiaoWoPlatDataNtf",
	[1089] = "XiaoWoHotReport_C2S_Msg",
	[1090] = "XiaoWoHotReport_S2C_Msg",
	[1091] = "XiaoWoPickupDrop_C2S_Msg",
	[1092] = "XiaoWoPickupDrop_S2C_Msg",
	[1093] = "XiaoWoBuyFurniture_C2S_Msg",
	[1094] = "XiaoWoBuyFurniture_S2C_Msg",
	[1095] = "XiaoWoExchangeCoin_C2S_Msg",
	[1096] = "XiaoWoExchangeCoin_S2C_Msg",
	[1097] = "XiaoWoKickNtf",
	[1098] = "XiaoWoSetClientKV_C2S_Msg",
	[1099] = "XiaoWoSetClientKV_S2C_Msg",
	[1100] = "SingleBattleKeepAlive_C2S_Msg",
	[1101] = "SingleBattleKeepAlive_S2C_Msg",
	[1102] = "PilotInfoNtf",
	[1103] = "SceneGiftPackagePushNtf",
	[1104] = "DigTreasureOfMultiPlayerSquad_C2S_Msg",
	[1105] = "DigTreasureOfMultiPlayerSquad_S2C_Msg",
	[1106] = "SquadMultiPlayerGetInfo_C2S_Msg",
	[1107] = "SquadMultiPlayerGetInfo_S2C_Msg",
	[1108] = "SquadMultiPlayerJoin_C2S_Msg",
	[1109] = "SquadMultiPlayerJoin_S2C_Msg",
	[1110] = "UgcCoCreateExitEditorNtf",
	[1111] = "ApplyUgcLayerId_C2S_Msg",
	[1112] = "ApplyUgcLayerId_S2C_Msg",
	[1113] = "AccreditLayerByCreatorId_C2S_Msg",
	[1114] = "AccreditLayerByCreatorId_S2C_Msg",
	[1115] = "UgcCoCreateEditorHeart_C2S_Msg",
	[1116] = "UgcCoCreateEditorHeart_S2C_Msg",
	[1117] = "UgcInviteCoCreateMap_C2S_Msg",
	[1118] = "UgcInviteCoCreateMap_S2C_Msg",
	[1119] = "UgcCoCreateInviteNtf",
	[1120] = "UgcCoCreateInviteReply_C2S_Msg",
	[1121] = "UgcCoCreateInviteReply_S2C_Msg",
	[1122] = "UgcRemoveCoCreator_C2S_Msg",
	[1123] = "UgcRemoveCoCreator_S2C_Msg",
	[1124] = "UgcQuitCoCreateMap_C2S_Msg",
	[1125] = "UgcQuitCoCreateMap_S2C_Msg",
	[1126] = "UgcCoCreatorModifyNtf",
	[1127] = "UgcGroupBatchGetPublish_C2S_Msg",
	[1128] = "UgcGroupBatchGetPublish_S2C_Msg",
	[1129] = "MallGetShopCommodityIds_C2S_Msg",
	[1130] = "MallGetShopCommodityIds_S2C_Msg",
	[1131] = "RoomJoinFromShare_C2S_Msg",
	[1132] = "RoomJoinFromShare_S2C_Msg",
	[1133] = "FireworksActivityStartNtf",
	[1134] = "FireworksActivityEndNtf",
	[1135] = "SetFireworksText_C2S_Msg",
	[1136] = "SetFireworksText_S2C_Msg",
	[1137] = "UseFireworksItem_C2S_Msg",
	[1138] = "UseFireworksItem_S2C_Msg",
	[1139] = "UseFireworksNtf",
	[1140] = "TakeawayGetRewardBox_C2S_Msg",
	[1141] = "TakeawayGetRewardBox_S2C_Msg",
	[1142] = "TakeawayStartUnlockBox_C2S_Msg",
	[1143] = "TakeawayStartUnlockBox_S2C_Msg",
	[1144] = "TakeawayGetSharingReward_C2S_Msg",
	[1145] = "TakeawayGetSharingReward_S2C_Msg",
	[1146] = "BagUseInteractItemMsg_C2S_Msg",
	[1147] = "BagUseInteractItemMsg_S2C_Msg",
	[1148] = "UploadAsaIadInfo_C2S_Msg",
	[1149] = "UploadAsaIadInfo_S2C_Msg",
	[1150] = "RaffleFreeDrawAcquiredNtf",
	[1151] = "RaffleFreeDrawDeliveredNtf",
	[1152] = "FetchAroundRank_C2S_Msg",
	[1153] = "FetchAroundRank_S2C_Msg",
	[1154] = "ReportShare_C2S_Msg",
	[1155] = "ReportShare_S2C_Msg",
	[1156] = "UgcStarWorldChangeMap_C2S_Msg",
	[1157] = "UgcStarWorldChangeMap_S2C_Msg",
	[1158] = "UgcGetAllPresetTopics_C2S_Msg",
	[1159] = "UgcGetAllPresetTopics_S2C_Msg",
	[1160] = "UgcHomePageRecommend_C2S_Msg",
	[1161] = "UgcHomePageRecommend_S2C_Msg",
	[1162] = "UgcGetUserFeedbackData_C2S_Msg",
	[1163] = "UgcGetUserFeedbackData_S2C_Msg",
	[1164] = "UgcRedPointNtf",
	[1165] = "UgcLvChangeNtf",
	[1166] = "UgcCoCreateDing_C2S_Msg",
	[1167] = "UgcCoCreateDing_S2C_Msg",
	[1168] = "UgcCoCreatorDingNtf",
	[1169] = "UgcHomePageThemeAllMaps_C2S_Msg",
	[1170] = "UgcHomePageThemeAllMaps_S2C_Msg",
	[1171] = "UgcCoCreateWhite_C2S_Msg",
	[1172] = "UgcCoCreateWhite_S2C_Msg",
	[1173] = "UgcLayerIsApply_C2S_Msg",
	[1174] = "UgcLayerIsApply_S2C_Msg",
	[1175] = "AchievementGetList_C2S_Msg",
	[1176] = "AchievementGetList_S2C_Msg",
	[1177] = "AchievementReward_C2S_Msg",
	[1178] = "AchievementReward_S2C_Msg",
	[1179] = "AchievementRedDotNtf",
	[1180] = "AchievementStatusNtf",
	[1181] = "GetFireworksInfo_C2S_Msg",
	[1182] = "GetFireworksInfo_S2C_Msg",
	[1183] = "RedEnvelopRainTake_C2S_Msg",
	[1184] = "SceneGetPlayerSceneId_C2S_Msg",
	[1185] = "SceneGetPlayerSceneId_S2C_Msg",
	[1186] = "RedEnvelopRainOpen_S2C_Msg",
	[1187] = "RedEnvelopRainOpenResultNtf",
	[1188] = "RedEnvelopRainQueryReward_C2S_Msg",
	[1189] = "RedEnvelopRainQueryReward_S2C_Msg",
	[1190] = "ClientResPatchNtf",
	[1191] = "ResVersionSync_C2S_Msg",
	[1192] = "ResVersionSync_S2C_Msg",
	[1193] = "GetAttachmentsAndDelete_C2S_Msg",
	[1194] = "GetAttachmentsAndDelete_S2C_Msg",
	[1195] = "MallBatchGiveMsg_C2S_Msg",
	[1196] = "MallBatchGiveMsg_S2C_Msg",
	[1197] = "MallBatchDemandMsg_C2S_Msg",
	[1198] = "MallBatchDemandMsg_S2C_Msg",
	[1199] = "MallGiveRecordList_C2S_Msg",
	[1200] = "MallGiveRecordList_S2C_Msg",
	[1201] = "SnsInvitationCreate_C2S_Msg",
	[1202] = "SnsInvitationCreate_S2C_Msg",
	[1203] = "SnsInvitationAccept_C2S_Msg",
	[1204] = "SnsInvitationAccept_S2C_Msg",
	[1205] = "SnsInvitationRefreshCode_C2S_Msg",
	[1206] = "SnsInvitationRefreshCode_S2C_Msg",
	[1207] = "ReportCompleteTask_C2S_Msg",
	[1208] = "ReportCompleteTask_S2C_Msg",
	[1209] = "TlogDataReport_C2S_Msg",
	[1210] = "TlogDataReport_S2C_Msg",
	[1211] = "UgcGetHotPlaying_C2S_Msg",
	[1212] = "UgcGetHotPlaying_S2C_Msg",
	[1213] = "XiaoWoForwardToDS_C2S_Msg",
	[1214] = "XiaoWoForwardToDS_S2C_Msg",
	[1215] = "XiaowoSetItemDetail_C2S_Msg",
	[1216] = "XiaowoSetItemDetail_S2C_Msg",
	[1217] = "XiaoWoSetInstructionAndImage_C2S_Msg",
	[1218] = "XiaoWoSetInstructionAndImage_S2C_Msg",
	[1219] = "XiaoWoAttrNtf",
	[1220] = "SceneInvite_C2S_Msg",
	[1221] = "SceneInvite_S2C_Msg",
	[1222] = "SceneReplyInvite_C2S_Msg",
	[1223] = "SceneReplyInvite_S2C_Msg",
	[1224] = "SceneInviteNtf",
	[1225] = "SceneInviteReplyNtf",
	[1226] = "EnterPlayerXiaoWo_C2S_Msg",
	[1227] = "EnterPlayerXiaoWo_S2C_Msg",
	[1228] = "RedEnvelopRainTake_S2C_Msg",
	[1229] = "RedEnvelopRainOpen_C2S_Msg",
	[1230] = "XiaowoGetEditMetaInfo_C2S_Msg",
	[1231] = "XiaowoGetEditMetaInfo_S2C_Msg",
	[1232] = "ChatSetQQSyncStatus_C2S_Msg",
	[1233] = "ChatSetQQSyncStatus_S2C_Msg",
	[1234] = "ChatGetQQSyncStatus_C2S_Msg",
	[1235] = "ChatGetQQSyncStatus_S2C_Msg",
	[1236] = "SearchPlayerUid_C2S_Msg",
	[1237] = "SearchPlayerUid_S2C_Msg",
	[1238] = "ModifyPlayerLobbyNpcInfo_C2S_Msg",
	[1239] = "ModifyPlayerLobbyNpcInfo_S2C_Msg",
	[1240] = "GameliveTaskRefresh_C2S_Msg",
	[1241] = "GameliveTaskRefresh_S2C_Msg",
	[1242] = "UpdatePlayerLocation_C2S_Msg",
	[1243] = "UpdatePlayerLocation_S2C_Msg",
	[1244] = "FriendIntimacyChangeNtf",
	[1245] = "TeamJoinRoomConfirmNtf",
	[1246] = "RoomTeamConfirmJoin_C2S_Msg",
	[1247] = "RoomTeamConfirmJoin_S2C_Msg",
	[1248] = "RoomPlayerReadyReminder_C2S_Msg",
	[1249] = "RoomPlayerReadyReminder_S2C_Msg",
	[1250] = "RoomPlayerReadyReminderNtf",
	[1251] = "CompetitionCommon_C2S_Msg",
	[1252] = "CompetitionCommon_S2C_Msg",
	[1253] = "UgcSvrTest_C2S_Msg",
	[1254] = "UgcSvrTest_S2C_Msg",
	[1255] = "TeamJoinRoomCancelNtf",
	[1256] = "GetLobbyInfo_C2S_Msg",
	[1257] = "GetLobbyInfo_S2C_Msg",
	[1258] = "GetPlayCalendarConfig_C2S_Msg",
	[1259] = "GetPlayCalendarConfig_S2C_Msg",
	[1260] = "QueryTranslationData_C2S_Msg",
	[1261] = "QueryTranslationData_S2C_Msg",
	[1262] = "NtfTranslationData_S2C_Msg",
	[1263] = "GetPlayerCalendarConfig_C2S_Msg",
	[1264] = "GetPlayerCalendarConfig_S2C_Msg",
	[1265] = "SquadRefreshNtf",
	[1266] = "QQGroupStartTeamTask_C2S_Msg",
	[1267] = "QQGroupStartTeamTask_S2C_Msg",
	[1268] = "UgcGetPublishTags_C2S_Msg",
	[1269] = "UgcGetPublishTags_S2C_Msg",
	[1270] = "LobbyEnvelopSettingDataNtf",
	[1271] = "LetsGoRecommendMatchType_C2S_Msg",
	[1272] = "LetsGoRecommendMatchType_S2C_Msg",
	[1273] = "BagItemRemoveNtf",
	[1274] = "MarqueeNoticeNtf",
	[1275] = "ClientClickPanel_C2S_Msg",
	[1276] = "ClientClickPanel_S2C_Msg",
	[1277] = "UgcSingleLevelGetDressInfo_C2S_Msg",
	[1278] = "UgcSingleLevelGetDressInfo_S2C_Msg",
	[1279] = "XiaowoTreeInfoNtf",
	[1280] = "CompetitionCommonNtf",
	[1281] = "PlatPrivilegesProcess_C2S_Msg",
	[1282] = "PlatPrivilegesProcess_S2C_Msg",
	[1283] = "BattleDsJumpNtf",
	[1284] = "DsJumpNtf",
	[1285] = "XiaoWoSampleRoomAlloc_C2S_Msg",
	[1286] = "XiaoWoSampleRoomAlloc_S2C_Msg",
	[1287] = "XiaoWoSaveLayout_C2S_Msg",
	[1288] = "XiaoWoSaveLayout_S2C_Msg",
	[1289] = "XiaoWoGetLayoutList_C2S_Msg",
	[1290] = "XiaoWoGetLayoutList_S2C_Msg",
	[1291] = "XiaoWoInitFurnitureGet_C2S_Msg",
	[1292] = "XiaoWoInitFurnitureGet_S2C_Msg",
	[1293] = "LobbyInfoNtf",
	[1294] = "SuperLinearDraw_C2S_Msg",
	[1295] = "SuperLinearDraw_S2C_Msg",
	[1296] = "SuperLinearGetReward_C2S_Msg",
	[1297] = "SuperLinearGetReward_S2C_Msg",
	[1298] = "GetCommonGuideStep_C2S_Msg",
	[1299] = "FinishCommonGuideStep_C2S_Msg",
	[1300] = "FinishCommonGuideStep_S2C_Msg",
	[1301] = "PlayerPlayNewYearCall_C2S_Msg",
	[1302] = "PlayerPlayNewYearCall_S2C_Msg",
	[1303] = "UgcGetCollectStarActivityMap_C2S_Msg",
	[1304] = "UgcGetCollectStarActivityMap_S2C_Msg",
	[1305] = "UgcGenAiVoice_C2S_Msg",
	[1306] = "UgcGenAiVoice_S2C_Msg",
	[1307] = "UgcAiVoiceGenSucNtf",
	[1308] = "UgcGenAiAnicap_C2S_Msg",
	[1309] = "UgcGenAiAnicap_S2C_Msg",
	[1310] = "UgcAiAnicapGenSucNtf",
	[1311] = "UgcGenAiAnswer_C2S_Msg",
	[1312] = "UgcGenAiAnswer_S2C_Msg",
	[1313] = "UgcAiAnswerGenSucNtf",
	[1314] = "RedEnvelopeRainSetRedPoint_C2S_Msg",
	[1315] = "RedEnvelopeRainSetRedPoint_S2C_Msg",
	[1316] = "LuckyBalloonShoot_C2S_Msg",
	[1317] = "LuckyBalloonShoot_S2C_Msg",
	[1318] = "UgcMapSearchFront_C2S_Msg",
	[1319] = "UgcMapSearchFront_S2C_Msg",
	[1320] = "UgcSearchTopicDetail_C2S_Msg",
	[1321] = "UgcSearchTopicDetail_S2C_Msg",
	[1322] = "FpsSetSettings_C2S_Msg",
	[1323] = "FpsSetSettings_S2C_Msg",
	[1324] = "FpsGetSettings_C2S_Msg",
	[1325] = "FpsGetSettings_S2C_Msg",
	[1326] = "GetTimeLimitedCheckInReward_C2S_Msg",
	[1327] = "GetTimeLimitedCheckInReward_S2C_Msg",
	[1328] = "ListScratchOffTickets_C2S_Msg",
	[1329] = "ListScratchOffTickets_S2C_Msg",
	[1330] = "BeginScratchOffTickets_C2S_Msg",
	[1331] = "BeginScratchOffTickets_S2C_Msg",
	[1332] = "GetScratchOffTicketsReward_C2S_Msg",
	[1333] = "GetScratchOffTicketsReward_S2C_Msg",
	[1334] = "UpgradeHigherScratchOffTickets_C2S_Msg",
	[1335] = "UpgradeHigherScratchOffTickets_S2C_Msg",
	[1336] = "ClubMemberLiveInfo_C2S_Msg",
	[1337] = "ClubMemberLiveInfo_S2C_Msg",
	[1338] = "RedPacketOpen_C2S_Msg",
	[1339] = "RedPacketOpen_S2C_Msg",
	[1340] = "RedPacketOpenResultNtf",
	[1341] = "RedPacketQuery_C2S_Msg",
	[1342] = "RedPacketQuery_S2C_Msg",
	[1343] = "ConfirmAssistFriend_C2S_Msg",
	[1344] = "ConfirmAssistFriend_S2C_Msg",
	[1345] = "GetAccumulateBlessingsReward_C2S_Msg",
	[1346] = "GetAccumulateBlessingsReward_S2C_Msg",
	[1347] = "StarPlayerNtf",
	[1348] = "RaffleBIDiscountReceivedNtf",
	[1349] = "UltramanThemeCollectionProgress_C2S_Msg",
	[1350] = "UltramanThemeCollectionProgress_S2C_Msg",
	[1351] = "UltramanThemeReward_C2S_Msg",
	[1352] = "UltramanThemeReward_S2C_Msg",
	[1353] = "LuckyStarUnlockStar_C2S_Msg",
	[1354] = "LuckyStarUnlockStar_S2C_Msg",
	[1355] = "LuckyStarGetDetailInfo_C2S_Msg",
	[1356] = "LuckyStarGetDetailInfo_S2C_Msg",
	[1357] = "LuckyStarGiveStar_C2S_Msg",
	[1358] = "LuckyStarGiveStar_S2C_Msg",
	[1359] = "LuckyStarReceiveStar_C2S_Msg",
	[1360] = "LuckyStarReceiveStar_S2C_Msg",
	[1361] = "LuckyStarGenerateRequireStar_C2S_Msg",
	[1362] = "LuckyStarGenerateRequireStar_S2C_Msg",
	[1363] = "LuckyStarGenerateGiveStar_C2S_Msg",
	[1364] = "LuckyStarGenerateGiveStar_S2C_Msg",
	[1365] = "UgcPublishPreCheck_C2S_Msg",
	[1366] = "UgcPublishPreCheck_S2C_Msg",
	[1367] = "TimeLimitedCheckInActivityUnlockNtf",
	[1368] = "UgcStarWorldRedDot_C2S_Msg",
	[1369] = "UgcStarWorldRedDot_S2C_Msg",
	[1370] = "ClubMemberShareMap_C2S_Msg",
	[1371] = "ClubMemberShareMap_S2C_Msg",
	[1372] = "ClubAdminShareMap_C2S_Msg",
	[1373] = "ClubAdminShareMap_S2C_Msg",
	[1374] = "ClubNewApplicantNtf",
	[1375] = "XiaoWoCropWater_C2S_Msg",
	[1376] = "XiaoWoCropWater_S2C_Msg",
	[1377] = "XiaoWoCropHarvest_C2S_Msg",
	[1378] = "XiaoWoCropHarvest_S2C_Msg",
	[1379] = "LuckyStarGetReward_C2S_Msg",
	[1380] = "LuckyStarGetReward_S2C_Msg",
	[1381] = "LetsGoGetWolfKillInfo_C2S_Msg",
	[1382] = "LetsGoGetWolfKillInfo_S2C_Msg",
	[1383] = "LetsGoGetWolfKillReputationScore_C2S_Msg",
	[1384] = "LetsGoGetWolfKillReputationScore_S2C_Msg",
	[1385] = "InviteFriendLogin_C2S_Msg",
	[1386] = "InviteFriendLogin_S2C_Msg",
	[1387] = "UgcMatchLobbyDetail_C2S_Msg",
	[1388] = "UgcMatchLobbyDetail_S2C_Msg",
	[1389] = "UgcMatchRoomUgcIdChange_C2S_Msg",
	[1390] = "UgcMatchRoomUgcIdChange_S2C_Msg",
	[1391] = "UgcMatchRoomUgcIdValidCheck_C2S_Msg",
	[1392] = "UgcMatchRoomUgcIdValidCheck_S2C_Msg",
	[1393] = "UgcRoomLobbyMap_C2S_Msg",
	[1394] = "UgcRoomLobbyMap_S2C_Msg",
	[1395] = "UgcAiAnicapQueueNtf",
	[1396] = "UgcAnicapCancelQueue_C2S_Msg",
	[1397] = "UgcAnicapCancelQueue_S2C_Msg",
	[1398] = "LuckStarReceiveNtf",
	[1399] = "ClubGetShareMapList_C2S_Msg",
	[1400] = "ClubGetShareMapList_S2C_Msg",
	[1401] = "UgcResHomePageRecommend_C2S_Msg",
	[1402] = "UgcResHomePageRecommend_S2C_Msg",
	[1403] = "UgcResHomePageRecommedMoreSet_C2S_Msg",
	[1404] = "UgcResHomePageRecommedMoreSet_S2C_Msg",
	[1405] = "UgcResCommunitySearch_C2S_Msg",
	[1406] = "UgcResCommunitySearch_S2C_Msg",
	[1407] = "UgcResCommunityGet_C2S_Msg",
	[1408] = "UgcResCommunityGet_S2C_Msg",
	[1409] = "UgcResBagAdd_C2S_Msg",
	[1410] = "UgcResBagAdd_S2C_Msg",
	[1411] = "UgcResBagDelete_C2S_Msg",
	[1412] = "UgcResBagDelete_S2C_Msg",
	[1413] = "UgcResBagGet_C2S_Msg",
	[1414] = "UgcResBagGet_S2C_Msg",
	[1415] = "UgcResBagSearch_C2S_Msg",
	[1416] = "UgcResBagSearch_S2C_Msg",
	[1417] = "UgcResCreate_C2S_Msg",
	[1418] = "UgcResCreate_S2C_Msg",
	[1419] = "UgcResPublish_C2S_Msg",
	[1420] = "UgcResPublish_S2C_Msg",
	[1421] = "UgcResGetMyList_C2S_Msg",
	[1422] = "UgcResGetMyList_S2C_Msg",
	[1423] = "UgcResDelete_C2S_Msg",
	[1424] = "UgcResDelete_S2C_Msg",
	[1425] = "UgcResTakeOff_C2S_Msg",
	[1426] = "UgcResTakeOff_S2C_Msg",
	[1427] = "UgcResOperate_C2S_Msg",
	[1428] = "UgcResOperate_S2C_Msg",
	[1429] = "UgcResModifyPublish_C2S_Msg",
	[1430] = "UgcResModifyPublish_S2C_Msg",
	[1431] = "UgcResGetCollect_C2S_Msg",
	[1432] = "UgcResGetCollect_S2C_Msg",
	[1433] = "UgcResGetTopic_C2S_Msg",
	[1434] = "UgcResGetTopic_S2C_Msg",
	[1435] = "UgcResGetPublishDetail_C2S_Msg",
	[1436] = "UgcResGetPublishDetail_S2C_Msg",
	[1437] = "RedPacketSend_C2S_Msg",
	[1438] = "RedPacketSend_S2C_Msg",
	[1439] = "LuckyStarReceiveNtf",
	[1440] = "UgcTextLawful_C2S_Msg",
	[1441] = "UgcTextLawful_S2C_Msg",
	[1442] = "SpecialFaceReport_C2S_Msg",
	[1443] = "SpecialFaceReport_S2C_Msg",
	[1444] = "ReputationScoreNotEnoughNtf",
	[1445] = "GetRecentBattlePlayer_C2S_Msg",
	[1446] = "GetRecentBattlePlayer_S2C_Msg",
	[1447] = "CancelMarqueeNoticeNtf",
	[1448] = "VideoExamineFailedNtf",
	[1449] = "ClubSearch_C2S_Msg",
	[1450] = "ClubSearch_S2C_Msg",
	[1451] = "LuckyStarGetGiveInfo_C2S_Msg",
	[1452] = "LuckyStarGetGiveInfo_S2C_Msg",
	[1453] = "LuckyStarGetRequireResult_C2S_Msg",
	[1454] = "LuckyStarGetRequireResult_S2C_Msg",
	[1455] = "RedEnvelopeRainShared_C2S_Msg",
	[1456] = "RedEnvelopeRainShared_S2C_Msg",
	[1457] = "RedEnvelopeRainClickShareLink_C2S_Msg",
	[1458] = "RedEnvelopeRainClickShareLink_S2C_Msg",
	[1459] = "RedEnvelopeRainSharedLinkClickedNtf",
	[1460] = "LuckyStarGetRequireInfo_C2S_Msg",
	[1461] = "LuckyStarGetRequireInfo_S2C_Msg",
	[1462] = "UGCSearchSuggestion_C2S_Msg",
	[1463] = "UGCSearchSuggestion_S2C_Msg",
	[1464] = "RoomMapStateSync_C2S_Msg",
	[1465] = "RoomMapStateSync_S2C_Msg",
	[1466] = "RoomMapStateSyncNtf",
	[1467] = "UltramanThemeStatusNtf",
	[1468] = "UgcAiGenModule_C2S_Msg",
	[1469] = "UgcAiGenModule_S2C_Msg",
	[1470] = "UgcAiGenModuleResultNtf",
	[1471] = "ClubTextCheck_C2S_Msg",
	[1472] = "ClubTextCheck_S2C_Msg",
	[1473] = "LobbyGetPlayerRecommendLabel_C2S_Msg",
	[1474] = "LobbyGetPlayerRecommendLabel_S2C_Msg",
	[1475] = "UgcNewYearActivity_C2S_Msg",
	[1476] = "UgcNewYearActivity_S2C_Msg",
	[1477] = "UltramanThemeActivateStory_C2S_Msg",
	[1478] = "UltramanThemeActivateStory_S2C_Msg",
	[1479] = "GetActivityConfig_C2S_Msg",
	[1480] = "GetActivityConfig_S2C_Msg",
	[1481] = "XiaoWoGetSampleRoomList_C2S_Msg",
	[1482] = "XiaoWoGetSampleRoomList_S2C_Msg",
	[1483] = "UgcAigcGetHistory_C2S_Msg",
	[1484] = "UgcAigcGetHistory_S2C_Msg",
	[1485] = "UgcAigcUse_C2S_Msg",
	[1486] = "UgcAigcUse_S2C_Msg",
	[1487] = "UgcMulTestSaveMeta_C2S_Msg",
	[1488] = "UgcMulTestSaveMeta_S2C_Msg",
	[1489] = "ActivityInfoNtf",
	[1490] = "LBSUpdate_C2S_Msg",
	[1491] = "LBSUpdate_S2C_Msg",
	[1492] = "LBSGetNearby_C2S_Msg",
	[1493] = "LBSGetNearby_S2C_Msg",
	[1494] = "LBSGetNearbyNtf",
	[1495] = "LBSDelete_C2S_Msg",
	[1496] = "LBSDelete_S2C_Msg",
	[1497] = "LetsGoSettingsChangeNtf",
	[1498] = "UgcMapGroupList_C2S_Msg",
	[1499] = "UgcMapGroupList_S2C_Msg",
	[1500] = "SpringBlessingCollectionDataChangeNtf",
	[1501] = "GetSpringBlessingCollectionCardReward_C2S_Msg",
	[1502] = "GetSpringBlessingCollectionCardReward_S2C_Msg",
	[1503] = "GetSpringBlessingCollectionTaskReward_C2S_Msg",
	[1504] = "GetSpringBlessingCollectionTaskReward_S2C_Msg",
	[1505] = "ScratchOffTicketsUpgradeNtf",
	[1506] = "GameTvStatusChangeNtf",
	[1507] = "XiaoWoCropWaterRecordGet_C2S_Msg",
	[1508] = "XiaoWoCropWaterRecordGet_S2C_Msg",
	[1509] = "XiaoWoFarmingHandbookAwardGet_C2S_Msg",
	[1510] = "XiaoWoFarmingHandbookAwardGet_S2C_Msg",
	[1511] = "RedPacketShare_C2S_Msg",
	[1512] = "RedPacketShare_S2C_Msg",
	[1513] = "RedPacketGetByShareId_C2S_Msg",
	[1514] = "RedPacketGetByShareId_S2C_Msg",
	[1515] = "UgcBatchPublishMap_C2S_Msg",
	[1516] = "UgcBatchPublishMap_S2C_Msg",
	[1517] = "XiaoWoSaveLayoutPublishRecord_C2S_Msg",
	[1518] = "XiaoWoSaveLayoutPublishRecord_S2C_Msg",
	[1519] = "LotterySpringBlessingCard_C2S_Msg",
	[1520] = "LotterySpringBlessingCard_S2C_Msg",
	[1521] = "GiveSpringBlessingCard_C2S_Msg",
	[1522] = "GiveSpringBlessingCard_S2C_Msg",
	[1523] = "UgcAigcDelHistory_C2S_Msg",
	[1524] = "UgcAigcDelHistory_S2C_Msg",
	[1525] = "SpringPray_C2S_Msg",
	[1526] = "SpringPray_S2C_Msg",
	[1527] = "PlayerPrayShare_C2S_Msg",
	[1528] = "PlayerPrayShare_S2C_Msg",
	[1529] = "NewYearPilotInfoNtf",
	[1530] = "IntellectualActiviyNtf",
	[1531] = "ReceiveSpringBlessingCardNtf",
	[1532] = "GetXiaowoRecomList_C2S_Msg",
	[1533] = "GetXiaowoRecomList_S2C_Msg",
	[1534] = "FpsGetActivityCoinInfo_C2S_Msg",
	[1535] = "FpsGetActivityCoinInfo_S2C_Msg",
	[1536] = "ClubPinShareMap_C2S_Msg",
	[1537] = "ClubPinShareMap_S2C_Msg",
	[1538] = "RedPacketQuerySingle_C2S_Msg",
	[1539] = "RedPacketQuerySingle_S2C_Msg",
	[1540] = "UltramanThemeAcitvityTeam_C2S_Msg",
	[1541] = "UltramanThemeAcitvityTeam_S2C_Msg",
	[1542] = "RedPacketPatchInfo_C2S_Msg",
	[1543] = "RedPacketPatchInfo_S2C_Msg",
	[1544] = "TYCFpsSetSettings_C2S_Msg",
	[1545] = "TYCFpsSetSettings_S2C_Msg",
	[1546] = "TYCFpsGetSettings_C2S_Msg",
	[1547] = "TYCFpsGetSettings_S2C_Msg",
	[1548] = "GetReturnActivityReward_C2S_Msg",
	[1549] = "GetReturnActivityReward_S2C_Msg",
	[1550] = "ReturningActiviyStartNtf",
	[1551] = "UltramanThemeSelfCollectionProgress_C2S_Msg",
	[1552] = "UltramanThemeSelfCollectionProgress_S2C_Msg",
	[1553] = "GetAccumulateBlessingsOnlineTime_C2S_Msg",
	[1554] = "GetAccumulateBlessingsOnlineTime_S2C_Msg",
	[1555] = "FpsGetDropLimitInfo_C2S_Msg",
	[1556] = "FpsGetDropLimitInfo_S2C_Msg",
	[1557] = "RedPacketEnterScene_C2S_Msg",
	[1558] = "RedPacketEnterScene_S2C_Msg",
	[1559] = "PlayerXiaowoAttrNtf",
	[1560] = "RedPacketClickRedDot_C2S_Msg",
	[1561] = "RedPacketClickRedDot_S2C_Msg",
	[1562] = "ListUseItemShareRecord_C2S_Msg",
	[1563] = "ListUseItemShareRecord_S2C_Msg",
	[1564] = "ListUseItemChangeRecord_C2S_Msg",
	[1565] = "ListUseItemChangeRecord_S2C_Msg",
	[1566] = "QuitBattleNtf",
	[1567] = "ReturnActivityEndNtf",
	[1568] = "ReturnActivityGenericNtf",
	[1569] = "ClubRedDotClear_C2S_Msg",
	[1570] = "ClubRedDotClear_S2C_Msg",
	[1571] = "UltramanThemeAddSelfCollection_C2S_Msg",
	[1572] = "UltramanThemeAddSelfCollection_S2C_Msg",
	[1573] = "TimeLimitedCheckInActivityDataChangeNtf",
	[1574] = "FetchTopRankByScore_C2S_Msg",
	[1575] = "FetchTopRankByScore_S2C_Msg",
	[1576] = "GetRaffleList_C2S_Msg",
	[1577] = "GetRaffleList_S2C_Msg",
	[1578] = "MatchTypeList_C2S_Msg",
	[1579] = "MatchTypeList_S2C_Msg",
	[1580] = "SetFriendRemarkName_C2S_Msg",
	[1581] = "SetFriendRemarkName_S2C_Msg",
	[1582] = "GetRankTabRankIdList_C2S_Msg",
	[1583] = "GetRankTabRankIdList_S2C_Msg",
	[1584] = "StickFriend_C2S_Msg",
	[1585] = "StickFriend_S2C_Msg",
	[1586] = "XiaoWoSetWelcomeInfo_C2S_Msg",
	[1587] = "XiaoWoSetWelcomeInfo_S2C_Msg",
	[1588] = "XiaoWoSendLiuYanMessage_C2S_Msg",
	[1589] = "XiaoWoSendLiuYanMessage_S2C_Msg",
	[1590] = "XiaoWoDeleteLiuYanMessage_C2S_Msg",
	[1591] = "XiaoWoDeleteLiuYanMessage_S2C_Msg",
	[1592] = "XiaoWoGetLiuYanMessage_C2S_Msg",
	[1593] = "XiaoWoGetLiuYanMessage_S2C_Msg",
	[1594] = "XiaoWoGetLiuYanMessageReply_C2S_Msg",
	[1595] = "XiaoWoGetLiuYanMessageReply_S2C_Msg",
	[1596] = "XiaoWoChoiceLiuYanMessage_C2S_Msg",
	[1597] = "XiaoWoChoiceLiuYanMessage_S2C_Msg",
	[1598] = "XiaoWoReceiveNewLiuYanMessageNtf",
	[1599] = "UgcCollectionCreate_C2S_Msg",
	[1600] = "UgcCollectionCreate_S2C_Msg",
	[1601] = "UgcCollectionModify_C2S_Msg",
	[1602] = "UgcCollectionModify_S2C_Msg",
	[1603] = "UgcCollectionGetBriefs_C2S_Msg",
	[1604] = "UgcCollectionGetBriefs_S2C_Msg",
	[1605] = "UgcCollectionOperate_C2S_Msg",
	[1606] = "UgcCollectionOperate_S2C_Msg",
	[1607] = "UgcCollectionSearch_C2S_Msg",
	[1608] = "UgcCollectionSearch_S2C_Msg",
	[1609] = "UgcCollectionGetInfo_C2S_Msg",
	[1610] = "UgcCollectionGetInfo_S2C_Msg",
	[1611] = "UploadGameSetting_C2S_Msg",
	[1612] = "UploadGameSetting_S2C_Msg",
	[1613] = "DownloadGameSetting_C2S_Msg",
	[1614] = "DownloadGameSetting_S2C_Msg",
	[1615] = "GetSeasonFashionEquipBook_C2S_Msg",
	[1616] = "GetSeasonFashionEquipBook_S2C_Msg",
	[1617] = "ClubGetRecommendList_C2S_Msg",
	[1618] = "ClubGetRecommendList_S2C_Msg",
	[1619] = "SceneLevelPackage_C2S_Msg",
	[1620] = "SceneLevelPackage_S2C_Msg",
	[1621] = "RoomReservation_C2S_Msg",
	[1622] = "RoomReservation_S2C_Msg",
	[1623] = "RoomReservationNtf",
	[1624] = "RoomReservationResponse_C2S_Msg",
	[1625] = "RoomReservationResponse_S2C_Msg",
	[1626] = "RoomReservationResponseNtf",
	[1627] = "ChangeShowQualifyType_C2S_Msg",
	[1628] = "ChangeShowQualifyType_S2C_Msg",
	[1629] = "BuyReturnChargeSignInActivityTicket_C2S_Msg",
	[1630] = "BuyReturnChargeSignInActivityTicket_S2C_Msg",
	[1631] = "BuyReturnChargeSignInActivityTicketNtf",
	[1632] = "UgcMatchLobbyDetailEx_C2S_Msg",
	[1633] = "UgcMatchLobbyDetailEx_S2C_Msg",
	[1634] = "GetCheckInManualReward_C2S_Msg",
	[1635] = "GetCheckInManualReward_S2C_Msg",
	[1636] = "UpgradeCheckInManual_C2S_Msg",
	[1637] = "UpgradeCheckInManual_S2C_Msg",
	[1638] = "CheckInManualUpgradeNtf",
	[1639] = "ClubMSDKReport_C2S_Msg",
	[1640] = "ClubMSDKReport_S2C_Msg",
	[1641] = "XiaoWoIsWhiteListAccountNtf",
	[1642] = "UgcBugGoods_C2S_Msg",
	[1643] = "UgcBugGoods_S2C_Msg",
	[1644] = "UgcBuyGoodsResultNtf",
	[1645] = "UgcActivePublishGoods_C2S_Msg",
	[1646] = "UgcActivePublishGoods_S2C_Msg",
	[1647] = "UserWhiteListCheck_C2S_Msg",
	[1648] = "UserWhiteListCheck_S2C_Msg",
	[1649] = "ClubBatchGetBasicInfo_C2S_Msg",
	[1650] = "ClubBatchGetBasicInfo_S2C_Msg",
	[1651] = "UgcApplyGoodsId_C2S_Msg",
	[1652] = "UgcApplyGoodsId_S2C_Msg",
	[1653] = "UgcPreAudit_C2S_Msg",
	[1654] = "UgcPreAudit_S2C_Msg",
	[1655] = "UgcPreAuditResultNtf",
	[1656] = "ClubSearchByTag_C2S_Msg",
	[1657] = "ClubSearchByTag_S2C_Msg",
	[1658] = "UgcStarWorldNavigationBar_C2S_Msg",
	[1659] = "UgcStarWorldNavigationBar_S2C_Msg",
	[1660] = "ActivityLotteryDraw_C2S_Msg",
	[1661] = "ActivityLotteryDraw_S2C_Msg",
	[1662] = "InterServerGiftGetFriendList_C2S_Msg",
	[1663] = "InterServerGiftGetFriendList_S2C_Msg",
	[1664] = "InterServerGiftCheckFriendBuy_C2S_Msg",
	[1665] = "InterServerGiftCheckFriendBuy_S2C_Msg",
	[1666] = "ReturnRefreshJumpToSignInAfterRoundFlag_C2S_Msg",
	[1667] = "ReturnRefreshJumpToSignInAfterRoundFlag_S2C_Msg",
	[1668] = "RoomMapVote_C2S_Msg",
	[1669] = "RoomMapVote_S2C_Msg",
	[1670] = "GetUgcCoPlayInfo_C2S_Msg",
	[1671] = "GetUgcCoPlayInfo_S2C_Msg",
	[1672] = "UgcBuyGoods_C2S_Msg",
	[1673] = "UgcBuyGoods_S2C_Msg",
	[1674] = "UgcNeedDownRes_C2S_Msg",
	[1675] = "UgcNeedDownRes_S2C_Msg",
	[1676] = "UgcGetDataStoreData_C2S_Msg",
	[1677] = "UgcGetDataStoreData_S2C_Msg",
	[1678] = "NewActivityPilotInfoNtf",
	[1679] = "RoomEndSettlement_C2S_Msg",
	[1680] = "RoomEndSettlement_S2C_Msg",
	[1681] = "UgcCollectionReserved1_C2S_Msg",
	[1682] = "UgcCollectionReserved1_S2C_Msg",
	[1683] = "UgcGetPublishGoodsProtoVersion_C2S_Msg",
	[1684] = "UgcGetPublishGoodsProtoVersion_S2C_Msg",
	[1685] = "UgcSetPublishGoodsProtoVersion_C2S_Msg",
	[1686] = "UgcSetPublishGoodsProtoVersion_S2C_Msg",
	[1687] = "PlayerGameSettingTlog_C2S_Msg",
	[1688] = "PlayerGameSettingTlog_S2C_Msg",
	[1689] = "RoomNeedNtfWhenAllMemberReadyForMatch_C2S_Msg",
	[1690] = "RoomNeedNtfWhenAllMemberReadyForMatch_S2C_Msg",
	[1691] = "RoomAllMemberReadyForMatchNtf",
	[1692] = "RoomOperationNtf",
	[1693] = "FeedKungFuPanda_C2S_Msg",
	[1694] = "FeedKungFuPanda_S2C_Msg",
	[1695] = "ReportRacingKungFuPandaResult_C2S_Msg",
	[1696] = "ReportRacingKungFuPandaResult_S2C_Msg",
	[1697] = "ListKungFuPandaHelpData_C2S_Msg",
	[1698] = "ListKungFuPandaHelpData_S2C_Msg",
	[1699] = "CheckGiveMailAttachment_C2S_Msg",
	[1700] = "CheckGiveMailAttachment_S2C_Msg",
	[1701] = "LobbyGetMapInfo_C2S_Msg",
	[1702] = "LobbyGetMapInfo_S2C_Msg",
	[1703] = "MallGiveDeliverGoodsNtf",
	[1704] = "LobbyGetListInfo_C2S_Msg",
	[1705] = "LobbyGetListInfo_S2C_Msg",
	[1706] = "RoomCommonBroadcast_C2S_Msg",
	[1707] = "RoomCommonBroadcast_S2C_Msg",
	[1708] = "RoomBroadcastInfoNtf",
	[1709] = "GetConcertStarInfo_C2S_Msg",
	[1710] = "GetConcertStarInfo_S2C_Msg",
	[1711] = "GenerateConcertTicket_C2S_Msg",
	[1712] = "GenerateConcertTicket_S2C_Msg",
	[1713] = "GetConcertTicketInfo_C2S_Msg",
	[1714] = "GetConcertTicketInfo_S2C_Msg",
	[1730] = "TeamRecommendRole_C2S_Msg",
	[1731] = "TeamRecommendRole_S2C_Msg",
	[1732] = "GetPlayerGrayTagInfo_C2S_Msg",
	[1733] = "GetPlayerGrayTagInfo_S2C_Msg",
	[1750] = "UgcCollectionRecommendList_C2S_Msg",
	[1751] = "UgcCollectionRecommendList_S2C_Msg",
	[1752] = "UgcCollectionExcellent_C2S_Msg",
	[1753] = "UgcCollectionExcellent_S2C_Msg",
	[1802] = "TeamRecommendRoleNtf",
	[1803] = "BagUnlockSlotId_C2S_Msg",
	[1804] = "BagUnlockSlotId_S2C_Msg",
	[1805] = "PlayerSettingHomePage_C2S_Msg",
	[1806] = "PlayerSettingHomePage_S2C_Msg",
	[1807] = "ActivityGetMultiPlayerSquadInfo_C2S_Msg",
	[1808] = "ActivityGetMultiPlayerSquadInfo_S2C_Msg",
	[1809] = "ActivityMultiPlayerSquadJoin_C2S_Msg",
	[1810] = "ActivityMultiPlayerSquadJoin_S2C_Msg",
	[1811] = "ActivityMultiPlayerSquadSaveGroupPhoto_C2S_Msg",
	[1812] = "ActivityMultiPlayerSquadSaveGroupPhoto_S2C_Msg",
	[1817] = "SetBattlePlayerClientInfo_C2S_Msg",
	[1818] = "SetBattlePlayerClientInfo_S2C_Msg",
	[1819] = "BattlePlayerClientInfoModifyNtf",
	[1820] = "SendRoomMemberToMemberNtf_C2S_Msg",
	[1821] = "SendRoomMemberToMemberNtf_S2C_Msg",
	[1822] = "RoomMemberToMemberNtf",
	[1823] = "FittingSlotShow_C2S_Msg",
	[1824] = "FittingSlotShow_S2C_Msg",
	[1825] = "StickerGetReward_C2S_Msg",
	[1826] = "StickerGetReward_S2C_Msg",
	[1827] = "CurBubbleConfigStateQuery_C2S_Msg",
	[1828] = "CurBubbleConfigStateQuery_S2C_Msg",
	[1829] = "CurBubbleConfigHasVisited_C2S_Msg",
	[1830] = "CurBubbleConfigHasVisited_S2C_Msg",
	[1831] = "GetCompetitionWarmUpGameTimesReward_C2S_Msg",
	[1832] = "GetCompetitionWarmUpGameTimesReward_S2C_Msg",
	[1833] = "GetCompetitionWarmUpScoreReward_C2S_Msg",
	[1834] = "GetCompetitionWarmUpScoreReward_S2C_Msg",
	[1835] = "BagSetInteractionCombination_C2S_Msg",
	[1836] = "BagSetInteractionCombination_S2C_Msg",
	[1837] = "BagDelInteractionCombination_C2S_Msg",
	[1838] = "BagDelInteractionCombination_S2C_Msg",
	[1839] = "ChatSendClubGroupInvite_C2S_Msg",
	[1840] = "ChatSendClubGroupInvite_S2C_Msg",
	[1841] = "DanMuSend_C2S_Msg",
	[1842] = "DanMuSend_S2C_Msg",
	[1843] = "DanMuSendNtf",
	[1844] = "DanMuDelete_C2S_Msg",
	[1845] = "DanMuDelete_S2C_Msg",
	[1846] = "DanMuDeleteNtf",
	[1847] = "DanMuDetail_C2S_Msg",
	[1848] = "DanMuDetail_S2C_Msg",
	[1849] = "DanMuLike_C2S_Msg",
	[1850] = "DanMuLike_S2C_Msg",
	[1851] = "DanMuLikeNtf",
	[1852] = "DanMuTipOff_C2S_Msg",
	[1853] = "DanMuTipOff_S2C_Msg",
	[1854] = "DanMuDetailFlush_C2S_Msg",
	[1855] = "DanMuDetailFlush_S2C_Msg",
	[1856] = "FarmCreate_C2S_Msg",
	[1857] = "FarmCreate_S2C_Msg",
	[1858] = "FarmEnter_C2S_Msg",
	[1859] = "FarmEnter_S2C_Msg",
	[1860] = "FarmExit_C2S_Msg",
	[1861] = "FarmExit_S2C_Msg",
	[1862] = "FarmKickNtf",
	[1863] = "FarmDSInfoNtf",
	[1864] = "FarmAttrNtf",
	[1865] = "FarmOwnerAttrNtf",
	[1866] = "FarmTest_C2S_Msg",
	[1867] = "FarmTest_S2C_Msg",
	[1868] = "FarmOp_C2S_Msg",
	[1869] = "FarmOp_S2C_Msg",
	[1870] = "FarmBuildingLevelUp_C2S_Msg",
	[1871] = "FarmBuildingLevelUp_S2C_Msg",
	[1872] = "FarmBuildingSell_C2S_Msg",
	[1873] = "FarmBuildingSell_S2C_Msg",
	[1874] = "FarmBuildingUnlock_C2S_Msg",
	[1875] = "FarmBuildingUnlock_S2C_Msg",
	[1876] = "FarmForwardToDS_C2S_Msg",
	[1877] = "FarmForwardToDS_S2C_Msg",
	[1878] = "FarmBadGuysList_C2S_Msg",
	[1879] = "FarmBadGuysList_S2C_Msg",
	[1880] = "FarmBadGuyStealingDetail_C2S_Msg",
	[1881] = "FarmBadGuyStealingDetail_S2C_Msg",
	[1882] = "FarmStrangerList_C2S_Msg",
	[1883] = "FarmStrangerList_S2C_Msg",
	[1884] = "FarmEvictBadGuy_C2S_Msg",
	[1885] = "FarmEvictBadGuy_S2C_Msg",
	[1886] = "FarmGetItemsNtf",
	[1887] = "FarmCropMenuSelect_C2S_Msg",
	[1888] = "FarmCropMenuSelect_S2C_Msg",
	[1889] = "FarmSetClientKV_C2S_Msg",
	[1890] = "FarmSetClientKV_S2C_Msg",
	[1891] = "RoguelikeQuickSettlement_C2S_Msg",
	[1892] = "RoguelikeQuickSettlement_S2C_Msg",
	[1893] = "RoguelikeQuickSettlementNtf",
	[1894] = "LetsGoSeasonBatchSettlementNtf",
	[1895] = "MallSeasonShopTabMsg_C2S_Msg",
	[1896] = "MallSeasonShopTabMsg_S2C_Msg",
	[1897] = "PlayerBatchGetRecordItemForLevel_C2S_Msg",
	[1898] = "PlayerBatchGetRecordItemForLevel_S2C_Msg",
	[1899] = "GetQuickRewardList_C2S_Msg",
	[1900] = "GetQuickRewardList_S2C_Msg",
	[1901] = "ReceiveQuickReward_C2S_Msg",
	[1902] = "ReceiveQuickReward_S2C_Msg",
	[1903] = "DSInvitationReplyCheck_C2S_Msg",
	[1904] = "DSInvitationReplyCheck_S2C_Msg",
	[1905] = "RoomMidJoinBattleFailNtf",
	[1906] = "GetSeasonFashionBattleData_C2S_Msg",
	[1907] = "GetSeasonFashionBattleData_S2C_Msg",
	[1908] = "GetSeasonFashionGeneralBattleData_C2S_Msg",
	[1909] = "GetSeasonFashionGeneralBattleData_S2C_Msg",
	[1910] = "StreamToken_C2S_Msg",
	[1911] = "StreamToken_S2C_Msg",
	[1912] = "UgcResPrivateAdapt_C2S_Msg",
	[1913] = "UgcResPrivateAdapt_S2C_Msg",
	[1914] = "UgcFastSlot_C2S_Msg",
	[1915] = "UgcFastSlot_S2C_Msg",
	[1916] = "UgcMapLabelScore_C2S_Msg",
	[1917] = "UgcMapLabelScore_S2C_Msg",
	[1918] = "UgcGetPlayerMapLabelScore_C2S_Msg",
	[1919] = "UgcGetPlayerMapLabelScore_S2C_Msg",
	[1920] = "UgcMapDownloadInfo_C2S_Msg",
	[1921] = "UgcMapDownloadInfo_S2C_Msg",
	[1922] = "UgcDeleteCover_C2S_Msg",
	[1923] = "UgcDeleteCover_S2C_Msg",
	[1924] = "UgcUploadCover_C2S_Msg",
	[1925] = "UgcUploadCover_S2C_Msg",
	[1926] = "FetchUgcTopRank_C2S_Msg",
	[1927] = "FetchUgcTopRank_S2C_Msg",
	[1928] = "UgcApplyRankId_C2S_Msg",
	[1929] = "UgcApplyRankId_S2C_Msg",
	[1930] = "UpdateUgcRank_C2S_Msg",
	[1931] = "UpdateUgcRank_S2C_Msg",
	[1932] = "GetUgcRankAppointPlayer_C2S_Msg",
	[1933] = "GetUgcRankAppointPlayer_S2C_Msg",
	[1934] = "DeleteUgcRankEntryByPlayer_C2S_Msg",
	[1935] = "DeleteUgcRankEntryByPlayer_S2C_Msg",
	[1936] = "GetUgcRankAppointRankNo_C2S_Msg",
	[1937] = "GetUgcRankAppointRankNo_S2C_Msg",
	[1938] = "BatchGetUgcRankPlayerPublicInfo_C2S_Msg",
	[1939] = "BatchGetUgcRankPlayerPublicInfo_S2C_Msg",
	[1940] = "UgcSetUpCover_C2S_Msg",
	[1941] = "UgcSetUpCover_S2C_Msg",
	[1942] = "UgcCoverList_C2S_Msg",
	[1943] = "UgcCoverList_S2C_Msg",
	[1944] = "XiaoWoShare_C2S_Msg",
	[1945] = "XiaoWoShare_S2C_Msg",
	[1946] = "XiaoWoGetVerAndGroup_C2S_Msg",
	[1947] = "XiaoWoGetVerAndGroup_S2C_Msg",
	[1948] = "StreamNpcChat_C2S_Msg",
	[1949] = "StreamNpcChat_S2C_Msg",
	[1950] = "StreamErrorNtf",
	[1951] = "StreamNpcChatResponseNtf",
	[1952] = "StreamLLMCheckResultNtf",
	[1953] = "StreamLogin_C2S_Msg",
	[1954] = "StreamLogin_S2C_Msg",
	[1955] = "GetLevelUGCInfo_C2S_Msg",
	[1956] = "GetLevelUGCInfo_S2C_Msg",
	[1957] = "ActivityTakePhoto_C2S_Msg",
	[1958] = "ActivityTakePhoto_S2C_Msg",
	[1959] = "ChangeProfileThemeMsg_C2S_Msg",
	[1960] = "ChangeProfileThemeMsg_S2C_Msg",
	[1961] = "ActivityDataChangeNtf",
	[1962] = "SubscribeQQRobot_C2S_Msg",
	[1963] = "SubscribeQQRobot_S2C_Msg",
	[1964] = "AppCommon_C2S_Msg",
	[1965] = "AppCommon_S2C_Msg",
	[1966] = "GameNtf",
	[1967] = "GetUserCloudInfo_C2S_Msg",
	[1968] = "GetUserCloudInfo_S2C_Msg",
	[1969] = "FarmSendLiuYanMessage_C2S_Msg",
	[1970] = "FarmSendLiuYanMessage_S2C_Msg",
	[1971] = "FarmDeleteLiuYanMessage_C2S_Msg",
	[1972] = "FarmDeleteLiuYanMessage_S2C_Msg",
	[1973] = "FarmGetLiuYanMessage_C2S_Msg",
	[1974] = "FarmGetLiuYanMessage_S2C_Msg",
	[1975] = "FarmReceiveNewLiuYanMessageNtf",
	[1976] = "FarmSetWelcomeInfo_C2S_Msg",
	[1977] = "FarmSetWelcomeInfo_S2C_Msg",
	[1978] = "GetRecommendMatchTypeReward_C2S_Msg",
	[1979] = "GetRecommendMatchTypeReward_S2C_Msg",
	[1980] = "RecommendMatchTypeTaskNtf",
	[1981] = "IntimateRelationOnlineNoticeRecord_C2S_Msg",
	[1982] = "IntimateRelationOnlineNoticeRecord_S2C_Msg",
	[1983] = "RejectIntimateRelationOnlineNotice_C2S_Msg",
	[1984] = "RejectIntimateRelationOnlineNotice_S2C_Msg",
	[1985] = "UgcGetBadge_C2S_Msg",
	[1986] = "UgcGetBadge_S2C_Msg",
	[1987] = "UgcGetBadgeDetail_C2S_Msg",
	[1988] = "UgcGetBadgeDetail_S2C_Msg",
	[1989] = "UgcCopyMapProgressNtf",
	[1990] = "ClickNewRecommendMatchType_C2S_Msg",
	[1991] = "ClickNewRecommendMatchType_S2C_Msg",
	[1992] = "NewRecommendMatchTypeChangeNtf",
	[1993] = "IntimateRelationGuideNtf",
	[1994] = "DelayIntimateRelationGuide_C2S_Msg",
	[1995] = "DelayIntimateRelationGuide_S2C_Msg",
	[1996] = "PlayerAlbumPictureAdd_C2S_Msg",
	[1997] = "PlayerAlbumPictureAdd_S2C_Msg",
	[1998] = "PlayerAlbumPictureDel_C2S_Msg",
	[1999] = "PlayerAlbumPictureDel_S2C_Msg",
	[2000] = "DressItemStatusChange_C2S_Msg",
	[2001] = "DressItemStatusChange_S2C_Msg",
	[2002] = "ChatControl_C2S_Msg",
	[2003] = "ChatControl_S2C_Msg",
	[2004] = "DirGetFreeFlowInfo_C2S_Msg",
	[2005] = "DirGetFreeFlowInfo_S2C_Msg",
	[2006] = "AchievementGetCompleteStatus_C2S_Msg",
	[2007] = "AchievementGetCompleteStatus_S2C_Msg",
	[2008] = "ActivityGetAllInfo_C2S_Msg",
	[2009] = "ActivityGetAllInfo_S2C_Msg",
	[2010] = "ActivityReceiveRewards_C2S_Msg",
	[2011] = "ActivityReceiveRewards_S2C_Msg",
	[2012] = "ActivityInfoUpdateNtf",
	[2013] = "ReturnActivityAttrChangeNtf",
	[2014] = "ReturnActivityRefreshRecommendMatchType_C2S_Msg",
	[2015] = "ReturnActivityRefreshRecommendMatchType_S2C_Msg",
	[2016] = "ReturnActivityBuyChargeGiftTicket_C2S_Msg",
	[2017] = "ReturnActivityBuyChargeGiftTicket_S2C_Msg",
	[2018] = "ReturnActivityBuyChargeGiftTicketNtf",
	[2019] = "ActivityGetRecommendPlayerList_C2S_Msg",
	[2020] = "ActivityGetRecommendPlayerList_S2C_Msg",
	[2021] = "GetIntelligenceStationReward_C2S_Msg",
	[2022] = "GetIntelligenceStationReward_S2C_Msg",
	[2023] = "IntelligenceStationChangeNtf",
	[2024] = "ActivityFlyingChessGetFreeCoin_C2S_Msg",
	[2025] = "ActivityFlyingChessGetFreeCoin_S2C_Msg",
	[2026] = "ActivityFlyingChessRun_C2S_Msg",
	[2027] = "ActivityFlyingChessRun_S2C_Msg",
	[2028] = "ActivityFlyingChessRoundRewardGet_C2S_Msg",
	[2029] = "ActivityFlyingChessRoundRewardGet_S2C_Msg",
	[2030] = "ActivityFlyingChessBigRewardGet_C2S_Msg",
	[2031] = "ActivityFlyingChessBigRewardGet_S2C_Msg",
	[2032] = "ActivityFlyingChessBigRewardGetNtf",
	[2033] = "ActivityHYNSyncInfoNtf",
	[2034] = "ActivityHYNCheckIn_C2S_Msg",
	[2035] = "ActivityHYNCheckIn_S2C_Msg",
	[2036] = "ActivityHYNRecvReward_C2S_Msg",
	[2037] = "ActivityHYNRecvReward_S2C_Msg",
	[2038] = "ActivityHYWarmUpInfoNtf",
	[2039] = "ActivityHYWarmUpCheckIn_C2S_Msg",
	[2040] = "ActivityHYWarmUpCheckIn_S2C_Msg",
	[2041] = "ActivityHYWarmUpRecvReward_C2S_Msg",
	[2042] = "ActivityHYWarmUpRecvReward_S2C_Msg",
	[2043] = "ActivityMusicOrderCompleteOrder_C2S_Msg",
	[2044] = "ActivityMusicOrderCompleteOrder_S2C_Msg",
	[2045] = "ActivityMusicOrderResetOrder_C2S_Msg",
	[2046] = "ActivityMusicOrderResetOrder_S2C_Msg",
	[2047] = "ActivityMusicOrderGetTaskReward_C2S_Msg",
	[2048] = "ActivityMusicOrderGetTaskReward_S2C_Msg",
	[2049] = "ActivityMusicOrderGetOrderList_C2S_Msg",
	[2050] = "ActivityMusicOrderGetOrderList_S2C_Msg",
	[2051] = "ActivityMusicOrderUpdateNtf",
	[2052] = "GetEntertainmentGuideTaskReward_C2S_Msg",
	[2053] = "GetEntertainmentGuideTaskReward_S2C_Msg",
	[2054] = "EntertainmentGuideNtf",
	[2055] = "FarmBuildingFirstFlag_C2S_Msg",
	[2056] = "FarmBuildingFirstFlag_S2C_Msg",
	[2057] = "FarmDoGooderDetail_C2S_Msg",
	[2058] = "FarmDoGooderDetail_S2C_Msg",
	[2059] = "FarmGetFarmDynamicInfo_C2S_Msg",
	[2060] = "FarmGetFarmDynamicInfo_S2C_Msg",
	[2061] = "FarmSubMonPass_C2S_Msg",
	[2062] = "FarmSubMonPass_S2C_Msg",
	[2063] = "FarmTagFriend_C2S_Msg",
	[2064] = "FarmTagFriend_S2C_Msg",
	[2065] = "FarmSellAnimal_C2S_Msg",
	[2066] = "FarmSellAnimal_S2C_Msg",
	[2067] = "FarmQueryIsWhite_C2S_Msg",
	[2068] = "FarmQueryIsWhite_S2C_Msg",
	[2069] = "FarmMonthCardNtf",
	[2070] = "ChangeCatchphrase_C2S_Msg",
	[2071] = "ChangeCatchphrase_S2C_Msg",
	[2072] = "RecommendFriendsCDSet_C2S_Msg",
	[2073] = "RecommendFriendsCDSet_S2C_Msg",
	[2074] = "GetFreeFlowInfo_C2S_Msg",
	[2075] = "GetFreeFlowInfo_S2C_Msg",
	[2076] = "ReportNewPlayerGuide_C2S_Msg",
	[2077] = "ReportNewPlayerGuide_S2C_Msg",
	[2078] = "GetReputationScoreChangeList_C2S_Msg",
	[2079] = "GetReputationScoreChangeList_S2C_Msg",
	[2080] = "ReportIAA_C2S_Msg",
	[2081] = "ReportIAA_S2C_Msg",
	[2082] = "ReportIAANtf",
	[2083] = "GetIAAList_C2S_Msg",
	[2084] = "GetIAAList_S2C_Msg",
	[2085] = "PlayerReputationScoreFunctionSwitchNtf",
	[2086] = "IsInAdminWhiteList_C2S_Msg",
	[2087] = "IsInAdminWhiteList_S2C_Msg",
	[2088] = "RaffleShareReport_C2S_Msg",
	[2089] = "RaffleShareReport_S2C_Msg",
	[2090] = "RecommendFriendsByRecentBattle_C2S_Msg",
	[2091] = "RecommendFriendsByRecentBattle_S2C_Msg",
	[2092] = "GetFriendCanRentFashionList_C2S_Msg",
	[2093] = "GetFriendCanRentFashionList_S2C_Msg",
	[2094] = "RentFriendFashion_C2S_Msg",
	[2095] = "RentFriendFashion_S2C_Msg",
	[2096] = "DeliverHotResResource_C2S_Msg",
	[2097] = "DeliverHotResResource_S2C_Msg",
	[2098] = "TeamRecruitQueryByPlayGroup_C2S_Msg",
	[2099] = "TeamRecruitQueryByPlayGroup_S2C_Msg",
	[2100] = "TeamRecruitQuickJoinByPlayGroup_C2S_Msg",
	[2101] = "TeamRecruitQuickJoinByPlayGroup_S2C_Msg",
	[2102] = "RoomMiniGameInvitationNtf",
	[2103] = "BatchUpdateUgcRank_C2S_Msg",
	[2104] = "BatchUpdateUgcRank_S2C_Msg",
	[2105] = "UgcApplyUniqueId_C2S_Msg",
	[2106] = "UgcApplyUniqueId_S2C_Msg",
	[2107] = "UgcGetMatchRecord_C2S_Msg",
	[2108] = "UgcGetMatchRecord_S2C_Msg",
	[2109] = "UgcGetMatchRecordDetail_C2S_Msg",
	[2110] = "UgcGetMatchRecordDetail_S2C_Msg",
	[2111] = "UgcForwardTest_C2S_Msg",
	[2112] = "UgcForwardTest_S2C_Msg",
	[2113] = "XiaoWoModifyMd5_C2S_Msg",
	[2114] = "XiaoWoModifyMd5_S2C_Msg",
	[2115] = "XiaoWoEnterPrepare_C2S_Msg",
	[2116] = "XiaoWoEnterPrepare_S2C_Msg",
	[2117] = "XiaoWoSetLiuYanMessagePermission_C2S_Msg",
	[2118] = "XiaoWoSetLiuYanMessagePermission_S2C_Msg",
	[2119] = "FarmSocialDataNtf",
	[2120] = "ActivityReceiveRewardsNtf",
	[2121] = "ActivityGeneral_C2S_Msg",
	[2122] = "ActivityGeneral_S2C_Msg",
	[2123] = "ActivityGeneralNtf",
	[2124] = "ActivityGetActivityByType_C2S_Msg",
	[2125] = "ActivityGetActivityByType_S2C_Msg",
	[2126] = "ActivityMusicOrderNoteExchange_C2S_Msg",
	[2127] = "ActivityMusicOrderNoteExchange_S2C_Msg",
	[2134] = "LetsGoGetWolfKillRoleInfo_C2S_Msg",
	[2135] = "LetsGoGetWolfKillRoleInfo_S2C_Msg",
	[2136] = "MallBuyWolfKillMsg_C2S_Msg",
	[2137] = "MallBuyWolfKillMsg_S2C_Msg",
	[2148] = "ClearAllRedDot_C2S_Msg",
	[2149] = "ClearAllRedDot_S2C_Msg",
	[2150] = "WolfkillToMasterRewardRecv_C2S_Msg",
	[2151] = "WolfkillToMasterRewardRecv_S2C_Msg",
	[2152] = "RoomLbsQuickJoinByPin_C2S_Msg",
	[2153] = "RoomLbsQuickJoinByPin_S2C_Msg",
	[2156] = "ChangeSuitRandom_C2S_Msg",
	[2157] = "ChangeSuitRandom_S2C_Msg",
	[2159] = "SetDataStoreByFunc_C2S_Msg",
	[2160] = "SetDataStoreByFunc_S2C_Msg",
	[2161] = "SetChangeDataStore_C2S_Msg",
	[2162] = "SetChangeDataStore_S2C_Msg",
	[2163] = "SetDataStoreByKey_C2S_Msg",
	[2164] = "SetDataStoreByKey_S2C_Msg",
	[2165] = "GetDataStoreByKey_C2S_Msg",
	[2166] = "GetDataStoreByKey_S2C_Msg",
	[2167] = "BatchSetDataStoreByKey_C2S_Msg",
	[2168] = "BatchSetDataStoreByKey_S2C_Msg",
	[2169] = "BatchGetDataStoreByKey_C2S_Msg",
	[2170] = "BatchGetDataStoreByKey_S2C_Msg",
	[2171] = "PixuiRedDotReport_C2S_Msg",
	[2172] = "PixuiRedDotReport_S2C_Msg",
	[2173] = "PixuiRedDotNtf",
	[2174] = "ActivityClubChallengeMarkMatchType_C2S_Msg",
	[2175] = "ActivityClubChallengeMarkMatchType_S2C_Msg",
	[2176] = "ActivityClubChallengeAward_C2S_Msg",
	[2177] = "ActivityClubChallengeAward_S2C_Msg",
	[2178] = "ActivityClubChallengeStarLightInfo_C2S_Msg",
	[2179] = "ActivityClubChallengeStarLightInfo_S2C_Msg",
	[2180] = "WolfKillBagDressUpItem_C2S_Msg",
	[2181] = "WolfKillBagDressUpItem_S2C_Msg",
	[2182] = "AnimalHandbookGetHandbookInfo_C2S_Msg",
	[2183] = "AnimalHandbookGetHandbookInfo_S2C_Msg",
	[2184] = "AnimalHandbookGetColonyState_C2S_Msg",
	[2185] = "AnimalHandbookGetColonyState_S2C_Msg",
	[2186] = "AnimalHandbookGetColonyStateNtf",
	[2187] = "AnimalHandbookStartTrapping_C2S_Msg",
	[2188] = "AnimalHandbookStartTrapping_S2C_Msg",
	[2189] = "AnimalHandbookCaptureAnimal_C2S_Msg",
	[2190] = "AnimalHandbookCaptureAnimal_S2C_Msg",
	[2191] = "AnimalHandbookGenerateGiveAnimalInfo_C2S_Msg",
	[2192] = "AnimalHandbookGenerateGiveAnimalInfo_S2C_Msg",
	[2193] = "ResGet_C2S_Msg",
	[2194] = "ResGet_S2C_Msg",
	[2195] = "OpenSecondaryPassword_C2S_Msg",
	[2196] = "OpenSecondaryPassword_S2C_Msg",
	[2197] = "ChangeSecondaryPassword_C2S_Msg",
	[2198] = "ChangeSecondaryPassword_S2C_Msg",
	[2199] = "CloseSecondaryPassword_C2S_Msg",
	[2200] = "CloseSecondaryPassword_S2C_Msg",
	[2201] = "SecondaryPasswordLessOperate_C2S_Msg",
	[2202] = "SecondaryPasswordLessOperate_S2C_Msg",
	[2203] = "VerifySecondaryPassword_C2S_Msg",
	[2204] = "VerifySecondaryPassword_S2C_Msg",
	[2205] = "ChangeSlotRandomState_C2S_Msg",
	[2206] = "ChangeSlotRandomState_S2C_Msg",
	[2207] = "GamePlayPufferInfoNtf",
	[2208] = "GamePlayResPatchNtf",
	[2210] = "ChangeUgcLayerId_C2S_Msg",
	[2211] = "ChangeUgcLayerId_S2C_Msg",
	[2212] = "ActivityClubChallengeStarLightInfoNtf",
	[2213] = "UgcGetPlayMapFriends_C2S_Msg",
	[2214] = "UgcGetPlayMapFriends_S2C_Msg",
	[2217] = "RemoveUgcLayer_C2S_Msg",
	[2218] = "RemoveUgcLayer_S2C_Msg",
	[2219] = "AnimalHandbookGetSpeciesCompleteReward_C2S_Msg",
	[2220] = "AnimalHandbookGetSpeciesCompleteReward_S2C_Msg",
	[2221] = "AnimalHandbookGetUltimatePrize_C2S_Msg",
	[2222] = "AnimalHandbookGetUltimatePrize_S2C_Msg",
	[2223] = "RemoveWeekSettleBubble_C2S_Msg",
	[2224] = "RemoveWeekSettleBubble_S2C_Msg",
	[2225] = "WeekSettleBubbleNtf",
	[2226] = "StrangerRecommendLabel_C2S_Msg",
	[2227] = "StrangerRecommendLabel_S2C_Msg",
	[2228] = "AcceptBPAllRewards_C2S_Msg",
	[2229] = "AcceptBPAllRewards_S2C_Msg",
	[2230] = "UpgradeBPPay_C2S_Msg",
	[2231] = "UpgradeBPPay_S2C_Msg",
	[2232] = "PurchaseBPLevel_C2S_Msg",
	[2233] = "PurchaseBPLevel_S2C_Msg",
	[2234] = "ListBPSummary_C2S_Msg",
	[2235] = "ListBPSummary_S2C_Msg",
	[2236] = "ListBPPreview_C2S_Msg",
	[2237] = "ListBPPreview_S2C_Msg",
	[2247] = "AnimalHandbookGetGiveAndReceiveHistory_C2S_Msg",
	[2248] = "AnimalHandbookGetGiveAndReceiveHistory_S2C_Msg",
	[2249] = "AnimalHandbookAnimalItemGetNtf",
	[2250] = "BroadcastNoticeNtf",
	[2251] = "UpdateForesightActivitySubscribe_C2S_Msg",
	[2252] = "UpdateForesightActivitySubscribe_S2C_Msg",
	[2253] = "ActivityMarkPlayerInvited_C2S_Msg",
	[2254] = "ActivityMarkPlayerInvited_S2C_Msg",
	[2255] = "DeleteGiveRecord_C2S_Msg",
	[2256] = "DeleteGiveRecord_S2C_Msg",
	[2257] = "OperateAddIntimateRecommendList_C2S_Msg",
	[2258] = "OperateAddIntimateRecommendList_S2C_Msg",
	[2259] = "GetIntimateRelationMotion_C2S_Msg",
	[2260] = "GetIntimateRelationMotion_S2C_Msg",
	[2261] = "SetIntimateRelationMotion_C2S_Msg",
	[2262] = "SetIntimateRelationMotion_S2C_Msg",
	[2263] = "GetFriendDailyInteractData_C2S_Msg",
	[2264] = "GetFriendDailyInteractData_S2C_Msg",
	[2265] = "CupsChangeNtf",
	[2266] = "CupsItemAddNtf",
	[2267] = "FarmExchangeCoin_C2S_Msg",
	[2268] = "FarmExchangeCoin_S2C_Msg",
	[2269] = "FarmSendGift_C2S_Msg",
	[2270] = "FarmSendGift_S2C_Msg",
	[2271] = "FarmPickGift_C2S_Msg",
	[2272] = "FarmPickGift_S2C_Msg",
	[2273] = "FarmHandbookAwardGet_C2S_Msg",
	[2274] = "FarmHandbookAwardGet_S2C_Msg",
	[2277] = "BPBriefNtf",
	[2278] = "FetchBPLevelRewards_C2S_Msg",
	[2279] = "FetchBPLevelRewards_S2C_Msg",
	[2280] = "AcceptBPRewards_C2S_Msg",
	[2281] = "AcceptBPRewards_S2C_Msg",
	[2282] = "UgcAppreciateGetSettings_C2S_Msg",
	[2283] = "UgcAppreciateGetSettings_S2C_Msg",
	[2284] = "UgcAppreciateGetMaps_C2S_Msg",
	[2285] = "UgcAppreciateGetMaps_S2C_Msg",
	[2286] = "UgcAppreciateMap_C2S_Msg",
	[2287] = "UgcAppreciateMap_S2C_Msg",
	[2288] = "WolfkillToMasterReward_C2S_Msg",
	[2289] = "WolfkillToMasterReward_S2C_Msg",
	[2290] = "GamePlayInfoSync_C2S_Msg",
	[2291] = "GamePlayInfoSync_S2C_Msg",
	[2292] = "FarmBuildingSetSkin_C2S_Msg",
	[2293] = "FarmBuildingSetSkin_S2C_Msg",
	[2294] = "FarmQueryFunctionOpenTime_C2S_Msg",
	[2295] = "FarmQueryFunctionOpenTime_S2C_Msg",
	[2296] = "FarmFishCardPackOpenNtf",
	[2297] = "FarmSetFishBowl_C2S_Msg",
	[2298] = "FarmSetFishBowl_S2C_Msg",
	[2299] = "FarmFishBowlUnlock_C2S_Msg",
	[2300] = "FarmFishBowlUnlock_S2C_Msg",
	[2301] = "DataStoreKvFragmentNtf",
	[2302] = "GetDataStoreByFunc_C2S_Msg",
	[2303] = "GetDataStoreByFunc_S2C_Msg",
	[2304] = "UploadClientLogNtf",
	[2305] = "FarmStatusRemindNtf",
	[2306] = "RaffleChooseReward_C2S_Msg",
	[2307] = "RaffleChooseReward_S2C_Msg",
	[2308] = "RaffleListReward_C2S_Msg",
	[2309] = "RaffleListReward_S2C_Msg",
	[2310] = "GetArenaGameInfo_C2S_Msg",
	[2311] = "GetArenaGameInfo_S2C_Msg",
	[2312] = "ArenaSelectFrame_C2S_Msg",
	[2313] = "ArenaSelectFrame_S2C_Msg",
	[2314] = "ArenaSelectVoiceStyle_C2S_Msg",
	[2315] = "ArenaSelectVoiceStyle_S2C_Msg",
	[2316] = "ArenaSelectEquip_C2S_Msg",
	[2317] = "ArenaSelectEquip_S2C_Msg",
	[2318] = "ArenaGetItemsNtf",
	[2319] = "ArenaHeroInfoNtf",
	[2320] = "ArenaCardPackResultNtf",
	[2321] = "ArenaCardPackResultCommit_C2S_Msg",
	[2322] = "ArenaCardPackResultCommit_S2C_Msg",
	[2323] = "ArenaBuyHero_C2S_Msg",
	[2324] = "ArenaBuyHero_S2C_Msg",
	[2325] = "ArenaSelectCard_C2S_Msg",
	[2326] = "ArenaSelectCard_S2C_Msg",
	[2327] = "ArenaAddNewCardNtf",
	[2328] = "UgcPlayerPublicSyncStart_C2S_Msg",
	[2329] = "UgcPlayerPublicSyncStart_S2C_Msg",
	[2330] = "UgcPlayerPublicSyncEnd_C2S_Msg",
	[2331] = "UgcPlayerPublicSyncEnd_S2C_Msg",
	[2332] = "UgcPlayerPublicHeartbeat_C2S_Msg",
	[2333] = "UgcPlayerPublicHeartbeat_S2C_Msg",
	[2334] = "GetUgcPlayerPublicAllAttrs_C2S_Msg",
	[2335] = "GetUgcPlayerPublicAllAttrs_S2C_Msg",
	[2336] = "ModifyUgcPlayerPublicAttr_C2S_Msg",
	[2337] = "ModifyUgcPlayerPublicAttr_S2C_Msg",
	[2338] = "GetUgcPlayerPublicArrayAttr_C2S_Msg",
	[2339] = "GetUgcPlayerPublicArrayAttr_S2C_Msg",
	[2340] = "AppendUgcPlayerPublicArrayAttr_C2S_Msg",
	[2341] = "AppendUgcPlayerPublicArrayAttr_S2C_Msg",
	[2342] = "BatchGetUgcPlayerPublicAttrs_C2S_Msg",
	[2343] = "BatchGetUgcPlayerPublicAttrs_S2C_Msg",
	[2344] = "TeamSetBackgroundTheme_C2S_Msg",
	[2345] = "TeamSetBackgroundTheme_S2C_Msg",
	[2346] = "ReceiveCupsStageReward_C2S_Msg",
	[2347] = "ReceiveCupsStageReward_S2C_Msg",
	[2348] = "AnimalHandbookAnimalItemRemoveNtf",
	[2349] = "WolfKillRoomTimeLimitStatus_C2S_Msg",
	[2350] = "WolfKillRoomTimeLimitStatus_S2C_Msg",
	[2351] = "UgcPlayerPublicAttrModifyNtf",
	[2352] = "UgcPlayerPublicHeartbeatTimeoutNtf",
	[2353] = "RecruitOrderRewardReceive_C2S_Msg",
	[2354] = "RecruitOrderRewardReceive_S2C_Msg",
	[2355] = "AiNpcFeedBack_C2S_Msg",
	[2356] = "AiNpcFeedBack_S2C_Msg",
	[2357] = "LuckyFriendSelectTask_C2S_Msg",
	[2358] = "LuckyFriendSelectTask_S2C_Msg",
	[2359] = "LuckyFriendApplyFriend_C2S_Msg",
	[2360] = "LuckyFriendApplyFriend_S2C_Msg",
	[2361] = "LuckyFriendOperateFriendTaskApply_C2S_Msg",
	[2362] = "LuckyFriendOperateFriendTaskApply_S2C_Msg",
	[2363] = "LuckyFriendNoticeTask_S2C_Msg",
	[2364] = "LuckyFriendGetTaskReward_C2S_Msg",
	[2365] = "LuckyFriendGetTaskReward_S2C_Msg",
	[2366] = "FarmRainBowBuffIconNtf",
	[2367] = "GameSceneReport_C2S_Msg",
	[2368] = "GameSceneReport_S2C_Msg",
	[2369] = "SuperLinearActivityConfig_C2S_Msg",
	[2370] = "SuperLinearActivityConfig_S2C_Msg",
	[2371] = "UgcTakeOffApply_C2S_Msg",
	[2372] = "UgcTakeOffApply_S2C_Msg",
	[2373] = "HideAndSeekLevelPropData_C2S_Msg",
	[2374] = "HideAndSeekLevelPropData_S2C_Msg",
	[2375] = "UnlockIntimateRelationExtraCnt_C2S_Msg",
	[2376] = "UnlockIntimateRelationExtraCnt_S2C_Msg",
	[2377] = "LuckyFriendTaskNotice_C2S_Msg",
	[2378] = "LuckyFriendTaskNotice_S2C_Msg",
	[2379] = "UgcGetBadgeNtf",
	[2380] = "BattleRecruitPublish_C2S_Msg",
	[2381] = "BattleRecruitPublish_S2C_Msg",
	[2382] = "BattleRecruitUnPublish_C2S_Msg",
	[2383] = "BattleRecruitUnPublish_S2C_Msg",
	[2384] = "BattleDirectJoin_C2S_Msg",
	[2385] = "BattleDirectJoin_S2C_Msg",
	[2386] = "ArenaHeroUnlockData_C2S_Msg",
	[2387] = "ArenaHeroUnlockData_S2C_Msg",
	[2390] = "UgcGetCommunityUnreadCount_C2S_Msg",
	[2391] = "UgcGetCommunityUnreadCount_S2C_Msg",
	[2392] = "ActivityLotteryCfg_C2S_Msg",
	[2393] = "ActivityLotteryCfg_S2C_Msg",
	[2394] = "GetRechargeLevelInfo_C2S_Msg",
	[2395] = "GetRechargeLevelInfo_S2C_Msg",
	[2396] = "GetMonthCardRewardsInfo_C2S_Msg",
	[2397] = "GetMonthCardRewardsInfo_S2C_Msg",
	[2398] = "ReturnActivitySetTaskReward_C2S_Msg",
	[2399] = "ReturnActivitySetTaskReward_S2C_Msg",
	[2400] = "ReturnActivityMarkCalendar_C2S_Msg",
	[2401] = "ReturnActivityMarkCalendar_S2C_Msg",
	[2402] = "LuckyFriendApplyTaskNtf",
	[2403] = "LuckyFriendTaskStartNtf",
	[2404] = "ChangeDefaultDress_C2S_Msg",
	[2405] = "ChangeDefaultDress_S2C_Msg",
	[2406] = "LuckyFriendTaskNtf",
	[2407] = "ActivityBuyMidasProduct_C2S_Msg",
	[2408] = "ActivityBuyMidasProduct_S2C_Msg",
	[2409] = "ActivityBuyMidasProductNtf",
	[2410] = "WolfKillAnnouncement_C2S_Msg",
	[2411] = "WolfKillAnnouncement_S2C_Msg",
	[2412] = "WolfKillGetFeedbackCount_C2S_Msg",
	[2413] = "WolfKillGetFeedbackCount_S2C_Msg",
	[2414] = "WolfKillAddFeedback_C2S_Msg",
	[2415] = "WolfKillAddFeedback_S2C_Msg",
	[2416] = "ClubLog_C2S_Msg",
	[2417] = "ClubLog_S2C_Msg",
	[2418] = "ResGetV2_C2S_Msg",
	[2419] = "ResGetV2_S2C_Msg",
	[2420] = "ResGetV2Ntf",
	[2421] = "ThemeAdventureRewardNtf",
	[2422] = "ThemeAdventureOpenMysteryBox_C2S_Msg",
	[2423] = "ThemeAdventureOpenMysteryBox_S2C_Msg",
	[2424] = "ThemeActivityGetDailyTaskReward_C2S_Msg",
	[2425] = "ThemeActivityGetDailyTaskReward_S2C_Msg",
	[2426] = "RaffleGainFreeDraw_C2S_Msg",
	[2427] = "RaffleGainFreeDraw_S2C_Msg",
	[2428] = "ActBookOfFriendsExpense_C2S_Msg",
	[2429] = "ActBookOfFriendsExpense_S2C_Msg",
	[2430] = "ActBookOfFriendsExpenseNtf",
	[2431] = "UgcMatchLobbyDetailGetOne_C2S_Msg",
	[2432] = "UgcMatchLobbyDetailGetOne_S2C_Msg",
	[2433] = "BattleRecruitJoin_C2S_Msg",
	[2434] = "BattleRecruitJoin_S2C_Msg",
	[2435] = "BIRecommendScenePackage_C2S_Msg",
	[2436] = "BIRecommendScenePackage_S2C_Msg",
	[2635] = "WolfKillSeasonRewardRecv_C2S_Msg",
	[2636] = "WolfKillSeasonRewardRecv_S2C_Msg",
	[2637] = "AppointmentActivityGetInfo_C2S_Msg",
	[2638] = "AppointmentActivityGetInfo_S2C_Msg",
	[2639] = "BurdenReduceTaskOverviewActivityGetInfo_C2S_Msg",
	[2640] = "BurdenReduceTaskOverviewActivityGetInfo_S2C_Msg",
	[2641] = "AppointmentActivityMake_C2S_Msg",
	[2642] = "AppointmentActivityMake_S2C_Msg",
	[2643] = "ActivityRecommendRecharge_C2S_Msg",
	[2644] = "ActivityRecommendRecharge_S2C_Msg",
	[2645] = "ABTestInfoNtf",
	[2646] = "BattleCommonBroadcast_C2S_Msg",
	[2647] = "BattleCommonBroadcast_S2C_Msg",
	[2648] = "BattleBroadcastInfoNtf",
	[2649] = "QuickCommercialConfInfo_C2S_Msg",
	[2650] = "QuickCommercialConfInfo_S2C_Msg",
	[2651] = "QuickCommercialConfInfoNtf",
	[2652] = "UgcApplyCoverUrl_C2S_Msg",
	[2653] = "UgcApplyCoverUrl_S2C_Msg",
	[2654] = "BattleMemberListGet_C2S_Msg",
	[2655] = "BattleMemberListGet_S2C_Msg",
	[2656] = "GetFollowerList_C2S_Msg",
	[2657] = "GetFollowerList_S2C_Msg",
	[2658] = "RecruitOrderConfirm_C2S_Msg",
	[2659] = "RecruitOrderConfirm_S2C_Msg",
	[2660] = "ArenaSelectHeroSkin_C2S_Msg",
	[2661] = "ArenaSelectHeroSkin_S2C_Msg",
	[2668] = "RecruitOrderCodeQuery_C2S_Msg",
	[2669] = "RecruitOrderCodeQuery_S2C_Msg",
	[2672] = "GamePakInfoReport_C2S_Msg",
	[2673] = "GamePakInfoReport_S2C_Msg",
	[2676] = "ArenaForwardCSMsgTest_C2S_Msg",
	[2677] = "ArenaForwardCSMsgTest_S2C_Msg",
	[2680] = "IAAForceGuideAppNtf",
	[2681] = "IAAForceGuideAppReset_C2S_Msg",
	[2682] = "IAAForceGuideAppReset_S2C_Msg",
	[2683] = "SetTeamShowBackgroundTheme_C2S_Msg",
	[2684] = "SetTeamShowBackgroundTheme_S2C_Msg",
	[2685] = "NewFollowerNtf",
	[2704] = "HouseCreate_C2S_Msg",
	[2705] = "HouseCreate_S2C_Msg",
	[2706] = "HouseEnter_C2S_Msg",
	[2707] = "HouseEnter_S2C_Msg",
	[2708] = "HouseExit_C2S_Msg",
	[2709] = "HouseExit_S2C_Msg",
	[2710] = "HouseKickNtf",
	[2711] = "HouseDSInfoNtf",
	[2712] = "HouseAttrNtf",
	[2713] = "HouseOwnerAttrNtf",
	[2714] = "HouseDecorate_C2S_Msg",
	[2715] = "HouseDecorate_S2C_Msg",
	[2716] = "HouseExtend_C2S_Msg",
	[2717] = "HouseExtend_S2C_Msg",
	[2718] = "HouseForwardToDS_C2S_Msg",
	[2719] = "HouseForwardToDS_S2C_Msg",
	[2722] = "HouseFurnitureBuy_C2S_Msg",
	[2723] = "HouseFurnitureBuy_S2C_Msg",
	[2724] = "HouseFurnitureSell_C2S_Msg",
	[2725] = "HouseFurnitureSell_S2C_Msg",
	[2729] = "HouseCropMachinePutIn_C2S_Msg",
	[2730] = "HouseCropMachinePutIn_S2C_Msg",
	[2731] = "HouseCropMachineTakeOut_C2S_Msg",
	[2732] = "HouseCropMachineTakeOut_S2C_Msg",
	[2733] = "FarmOpenAquariumMode_C2S_Msg",
	[2734] = "FarmOpenAquariumMode_S2C_Msg",
	[2735] = "FarmFetchFunctionOpenTime_C2S_Msg",
	[2736] = "FarmFetchFunctionOpenTime_S2C_Msg",
	[2737] = "FarmUnlockAquariumSeat_C2S_Msg",
	[2738] = "FarmUnlockAquariumSeat_S2C_Msg",
	[2739] = "FarmSetAquarium_C2S_Msg",
	[2740] = "FarmSetAquarium_S2C_Msg",
	[2741] = "FarmGetAquariumBenefit_C2S_Msg",
	[2742] = "FarmGetAquariumBenefit_S2C_Msg",
	[2743] = "HouseRecommendStranger_C2S_Msg",
	[2744] = "HouseRecommendStranger_S2C_Msg",
	[2745] = "BattlePartnerCoMatchProposal_C2S_Msg",
	[2746] = "BattlePartnerCoMatchProposal_S2C_Msg",
	[2747] = "BattlePartnerCoMatchInfoNtf",
	[2748] = "BattlePartnerCoMatchConfirm_C2S_Msg",
	[2749] = "BattlePartnerCoMatchConfirm_S2C_Msg",
	[2750] = "FarmPetEvictDetail_C2S_Msg",
	[2751] = "FarmPetEvictDetail_S2C_Msg",
	[2752] = "FarmPetEvict_C2S_Msg",
	[2753] = "FarmPetEvict_S2C_Msg",
	[2754] = "TestRaffle_C2S_Msg",
	[2755] = "TestRaffle_S2C_Msg",
	[2756] = "ConfirmTestRaffle_C2S_Msg",
	[2757] = "ConfirmTestRaffle_S2C_Msg",
	[2758] = "QqCloudGameAuth_C2S_Msg",
	[2759] = "QqCloudGameAuth_S2C_Msg",
	[2760] = "QqCloudGameNeedAuthNtf",
	[2761] = "RechargeLuckyTurntable_C2S_Msg",
	[2762] = "RechargeLuckyTurntable_S2C_Msg",
	[2765] = "RechargeLuckyTurntableRewardNtf",
	[2766] = "JoinCommunityChannel_C2S_Msg",
	[2767] = "JoinCommunityChannel_S2C_Msg",
	[2768] = "QuitCommunityChannel_C2S_Msg",
	[2769] = "QuitCommunityChannel_S2C_Msg",
	[2770] = "StickCommunityChannel_C2S_Msg",
	[2771] = "StickCommunityChannel_S2C_Msg",
	[2772] = "SelectIconCommunityChannel_C2S_Msg",
	[2773] = "SelectIconCommunityChannel_S2C_Msg",
	[2774] = "CommunityChannelEntranceNoShow_C2S_Msg",
	[2775] = "CommunityChannelEntranceNoShow_S2C_Msg",
	[2782] = "FarmBuildingBatchSell_C2S_Msg",
	[2783] = "FarmBuildingBatchSell_S2C_Msg",
	[2787] = "WolfKillNewUserTips_C2S_Msg",
	[2788] = "WolfKillNewUserTips_S2C_Msg",
	[2795] = "PlayDetailPageRoomQueryByPlayGroup_C2S_Msg",
	[2796] = "PlayDetailPageRoomQueryByPlayGroup_S2C_Msg",
	[2799] = "UgcAchievementConfigEdit_C2S_Msg",
	[2800] = "UgcAchievementConfigEdit_S2C_Msg",
	[2801] = "UgcAchievementConfigGet_C2S_Msg",
	[2802] = "UgcAchievementConfigGet_S2C_Msg",
	[2807] = "PlayerAlbumPictureGet_C2S_Msg",
	[2808] = "PlayerAlbumPictureGet_S2C_Msg",
	[2809] = "PlayerAlbumPictureFormatConvert_C2S_Msg",
	[2810] = "PlayerAlbumPictureFormatConvert_S2C_Msg",
	[2811] = "ActivityWeekendIceBrokenInfo_C2S_Msg",
	[2812] = "ActivityWeekendIceBrokenInfo_S2C_Msg",
	[2813] = "MallDemandDeliverGoodsNtf",
	[2814] = "FarmSceneDropPick_C2S_Msg",
	[2815] = "FarmSceneDropPick_S2C_Msg",
	[2816] = "FarmPetFeed_C2S_Msg",
	[2817] = "FarmPetFeed_S2C_Msg",
	[2818] = "FarmPetHouseKeepingSwitch_C2S_Msg",
	[2819] = "FarmPetHouseKeepingSwitch_S2C_Msg",
	[2820] = "FarmPetSetClientCache_C2S_Msg",
	[2821] = "FarmPetSetClientCache_S2C_Msg",
	[2822] = "BattleEndSettlement_C2S_Msg",
	[2823] = "BattleEndSettlement_S2C_Msg",
	[2824] = "RoomCoMatchReady_C2S_Msg",
	[2825] = "RoomCoMatchReady_S2C_Msg",
	[2826] = "HouseGetCurrentRoom_C2S_Msg",
	[2827] = "HouseGetCurrentRoom_S2C_Msg",
	[2837] = "EditShowData_C2S_Msg",
	[2838] = "EditShowData_S2C_Msg",
	[2839] = "HouseSetClientKV_C2S_Msg",
	[2840] = "HouseSetClientKV_S2C_Msg",
	[2842] = "HouseItemInteract_C2S_Msg",
	[2843] = "HouseItemInteract_S2C_Msg",
	[2848] = "FarmEndEvent_C2S_Msg",
	[2849] = "FarmEndEvent_S2C_Msg",
	[2854] = "AiNpcDressSave_C2S_Msg",
	[2855] = "AiNpcDressSave_S2C_Msg",
	[2860] = "ReputationScoreChangeInfoNtf",
	[2861] = "GetTargetUgcPlayerData_C2S_Msg",
	[2862] = "GetTargetUgcPlayerData_S2C_Msg",
	[2863] = "PlayerAlbumPictureOperateNtf",
	[2864] = "DecomposeItem_C2S_Msg",
	[2865] = "DecomposeItem_S2C_Msg",
	[2872] = "ArenaSettings_C2S_Msg",
	[2873] = "ArenaSettings_S2C_Msg",
	[2874] = "GetQualifiedActivityMarqueeId_C2S_Msg",
	[2875] = "GetQualifiedActivityMarqueeId_S2C_Msg",
	[2876] = "GetShowData_C2S_Msg",
	[2877] = "GetShowData_S2C_Msg",
	[2878] = "ActivityPrayerCardInfo_C2S_Msg",
	[2879] = "ActivityPrayerCardInfo_S2C_Msg",
	[2880] = "ActivityPrayerCardGive_C2S_Msg",
	[2881] = "ActivityPrayerCardGive_S2C_Msg",
	[2882] = "ActivityPrayerCardLook_C2S_Msg",
	[2883] = "ActivityPrayerCardLook_S2C_Msg",
	[2884] = "FarmWeatherEffectNtf",
	[2886] = "FarmWeatherTrigger_C2S_Msg",
	[2887] = "FarmWeatherTrigger_S2C_Msg",
	[2888] = "RefreshRaffle_C2S_Msg",
	[2889] = "RefreshRaffle_S2C_Msg",
	[2890] = "RefreshRaffleNtf",
	[2893] = "FarmPetInteract_C2S_Msg",
	[2894] = "FarmPetInteract_S2C_Msg",
	[2895] = "FarmPetFertilize_C2S_Msg",
	[2896] = "FarmPetFertilize_S2C_Msg",
	[2901] = "PlayerLevelEstimation_C2S_Msg",
	[2902] = "PlayerLevelEstimation_S2C_Msg",
	[2903] = "PlayerPictureLike_C2S_Msg",
	[2904] = "PlayerPictureLike_S2C_Msg",
	[2905] = "PlayerPictureLikeHis_C2S_Msg",
	[2906] = "PlayerPictureLikeHis_S2C_Msg",
	[2907] = "FarmLightningStrikeReport_C2S_Msg",
	[2908] = "FarmLightningStrikeReport_S2C_Msg",
	[2909] = "UpdateFavoriteItems_C2S_Msg",
	[2910] = "UpdateFavoriteItems_S2C_Msg",
	[2911] = "UgcRecommendSet_C2S_Msg",
	[2912] = "UgcRecommendSet_S2C_Msg",
	[2913] = "GetAllInfoForLevelEstimation_C2S_Msg",
	[2914] = "GetAllInfoForLevelEstimation_S2C_Msg",
	[2915] = "GetInfoForLevelEstimation_C2S_Msg",
	[2916] = "GetInfoForLevelEstimation_S2C_Msg",
	[2917] = "FarmPetGiftReceive_C2S_Msg",
	[2918] = "FarmPetGiftReceive_S2C_Msg",
	[2924] = "RechargeActivityList_C2S_Msg",
	[2925] = "RechargeActivityList_S2C_Msg",
	[2968] = "FarmResetAquarium_C2S_Msg",
	[2969] = "FarmResetAquarium_S2C_Msg",
	[2970] = "StreamUgcAiEditAssistantChat_C2S_Msg",
	[2971] = "StreamUgcAiEditAssistantChat_S2C_Msg",
	[2973] = "StreamUgcAiEditAssistantClose_C2S_Msg",
	[2974] = "StreamUgcAiEditAssistantClose_S2C_Msg",
	[2975] = "StreamUgcAiEditAssistantCloseResultNtf",
	[2977] = "StreamUgcAiEditAssistantChatResultNtf",
	[2978] = "BIRecBanner_C2S_Msg",
	[2979] = "BIRecBanner_S2C_Msg",
	[2980] = "Exchange_C2S_Msg",
	[2981] = "Exchange_S2C_Msg",
	[2982] = "StreamAiEditAssistantInitResultNtf",
	[2983] = "StreamUgcAiEditAssistantInit_C2S_Msg",
	[2984] = "StreamUgcAiEditAssistantInit_S2C_Msg",
	[2985] = "FarmPetChangeName_C2S_Msg",
	[2986] = "FarmPetChangeName_S2C_Msg",
	[2987] = "FarmPetSummon_C2S_Msg",
	[2988] = "FarmPetSummon_S2C_Msg",
	[2989] = "FarmFishPoolRecord_C2S_Msg",
	[2990] = "FarmFishPoolRecord_S2C_Msg",
	[2991] = "RoomConfirmEnterNtf",
	[2992] = "RoomConfirmEnter_C2S_Msg",
	[2993] = "RoomConfirmEnterCancelNtf",
	[2994] = "RoomStarPInviteEnterGame_C2S_Msg",
	[2995] = "RoomStarPInviteEnterGame_S2C_Msg",
	[2996] = "RoomStarPInviteEnterGameAnswer_C2S_Msg",
	[2997] = "RoomCoMatchInfoNtf",
	[2998] = "RoomCoMatchPropose_C2S_Msg",
	[2999] = "RoomCoMatchPropose_S2C_Msg",
	[3000] = "RoomCoMatchConfirm_C2S_Msg",
	[3001] = "RoomCoMatchConfirm_S2C_Msg",
	[3002] = "PlayerAlbumPictureCheck_C2S_Msg",
	[3003] = "PlayerAlbumPictureCheck_S2C_Msg",
	[3004] = "FarmPetDressUp_C2S_Msg",
	[3005] = "FarmPetDressUp_S2C_Msg",
	[3006] = "CommunityChannelGetHotTopicInfo_C2S_Msg",
	[3007] = "CommunityChannelGetHotTopicInfo_S2C_Msg",
	[3008] = "CommunityChannelThumbsUpHotTopic_C2S_Msg",
	[3009] = "CommunityChannelThumbsUpHotTopic_S2C_Msg",
	[3014] = "LoginDone_C2S_Msg",
	[3015] = "LoginDone_S2C_Msg",
	[3016] = "LuckyRebateActivityGetResult_C2S_Msg",
	[3017] = "LuckyRebateActivityGetResult_S2C_Msg",
	[3018] = "FarmEventGetReward_C2S_Msg",
	[3019] = "FarmEventGetReward_S2C_Msg",
	[3020] = "UpdateUgcLayerName_C2S_Msg",
	[3021] = "UpdateUgcLayerName_S2C_Msg",
	[3028] = "PlayerAlbumPictureLikeOperateNtf",
	[3029] = "PlayerPictureLikeCount_C2S_Msg",
	[3030] = "PlayerPictureLikeCount_S2C_Msg",
	[3031] = "FarmEventSaveData_C2S_Msg",
	[3032] = "FarmEventSaveData_S2C_Msg",
	[3034] = "WolfKillFace_C2S_Msg",
	[3035] = "WolfKillFace_S2C_Msg",
	[3036] = "FarmSelectPool_C2S_Msg",
	[3037] = "FarmSelectPool_S2C_Msg",
	[3040] = "PlayerBatchPictureLikeCountGet_C2S_Msg",
	[3041] = "PlayerBatchPictureLikeCountGet_S2C_Msg",
	[3044] = "StarPGuideStepFlow_C2S_Msg",
	[3045] = "StarPGuideStepFlow_S2C_Msg",
	[3046] = "FarmTalentLevelUp_C2S_Msg",
	[3047] = "FarmTalentLevelUp_S2C_Msg",
	[3048] = "FarmVillagerInteract_C2S_Msg",
	[3049] = "FarmVillagerInteract_S2C_Msg",
	[3050] = "FarmVillagerSetClientCache_C2S_Msg",
	[3051] = "FarmVillagerSetClientCache_S2C_Msg",
	[3058] = "PlaySwitch_C2S_Msg",
	[3059] = "PlaySwitch_S2C_Msg",
	[3060] = "FarmVillagerPresentGift_C2S_Msg",
	[3061] = "FarmVillagerPresentGift_S2C_Msg",
	[3062] = "FarmVillagerAcceptGift_C2S_Msg",
	[3063] = "FarmVillagerAcceptGift_S2C_Msg",
	[3064] = "GetEntertainmentGuideTaskConfV2_C2S_Msg",
	[3065] = "GetEntertainmentGuideTaskConfV2_S2C_Msg",
	[3066] = "FarmGetCropLevelReward_C2S_Msg",
	[3067] = "FarmGetCropLevelReward_S2C_Msg",
	[3068] = "StarPSetPreDelTime_C2S_Msg",
	[3069] = "StarPSetPreDelTime_S2C_Msg",
	[3070] = "KonanGetDrawResult_C2S_Msg",
	[3071] = "KonanGetDrawResult_S2C_Msg",
	[3072] = "FarmTaskGetReward_C2S_Msg",
	[3073] = "FarmTaskGetReward_S2C_Msg",
	[3074] = "FarmTaskSubmitItem_C2S_Msg",
	[3075] = "FarmTaskSubmitItem_S2C_Msg",
	[3078] = "FarmNPCFishNtfNtf",
	[3079] = "WerewolfFullReduceActivityCart_C2S_Msg",
	[3080] = "WerewolfFullReduceActivityCart_S2C_Msg",
	[3081] = "UpdateWerewolfFullReduceActivityCart_C2S_Msg",
	[3082] = "UpdateWerewolfFullReduceActivityCart_S2C_Msg",
	[3083] = "WerewolfFullReduceActivityCartOrder_C2S_Msg",
	[3084] = "WerewolfFullReduceActivityCartOrder_S2C_Msg",
	[3085] = "FarmNPCFishNtf",
	[3086] = "StarPAdventureGetBaseInfo_C2S_Msg",
	[3087] = "StarPAdventureGetBaseInfo_S2C_Msg",
	[3088] = "StarPAdventureGetDayReward_C2S_Msg",
	[3089] = "StarPAdventureGetDayReward_S2C_Msg",
	[3090] = "StarPStartRL_C2S_Msg",
	[3091] = "StarPStartRL_S2C_Msg",
	[3092] = "StarPQueryVisitInfo_C2S_Msg",
	[3093] = "StarPQueryVisitInfo_S2C_Msg",
	[3094] = "StarPPlayerDoLottery_C2S_Msg",
	[3095] = "StarPPlayerDoLottery_S2C_Msg",
	[3096] = "StarPPlayerGetLotteryInfo_C2S_Msg",
	[3097] = "StarPPlayerGetLotteryInfo_S2C_Msg",
	[3098] = "StarPGetFunctionControl_C2S_Msg",
	[3099] = "StarPGetFunctionControl_S2C_Msg",
	[3100] = "FarmVillagerSummon_C2S_Msg",
	[3101] = "FarmVillagerSummon_S2C_Msg",
	[3102] = "FarmVillagerDeport_C2S_Msg",
	[3103] = "FarmVillagerDeport_S2C_Msg",
	[3104] = "FarmTaskStatusNtf",
	[3105] = "LobbyReportMiniGameResult_C2S_Msg",
	[3106] = "LobbyReportMiniGameResult_S2C_Msg",
	[3107] = "FarmTaskConditionChange_C2S_Msg",
	[3108] = "FarmTaskConditionChange_S2C_Msg",
	[3109] = "StarPPlayerShopInfo_C2S_Msg",
	[3110] = "StarPPlayerShopInfo_S2C_Msg",
	[3111] = "StarPPlayerShopBuy_C2S_Msg",
	[3112] = "StarPPlayerShopBuy_S2C_Msg",
	[3115] = "FarmEventCommitItem_C2S_Msg",
	[3116] = "FarmEventCommitItem_S2C_Msg",
	[3120] = "ArenaAttrNtf",
	[3121] = "PlayerSettingShowAnimationButton_C2S_Msg",
	[3122] = "PlayerSettingShowAnimationButton_S2C_Msg",
	[3123] = "FarmTaskSetCanTrigger_C2S_Msg",
	[3124] = "FarmTaskSetCanTrigger_S2C_Msg",
	[3128] = "HOKSetSettings_C2S_Msg",
	[3129] = "HOKSetSettings_S2C_Msg",
	[3130] = "HOKGetSettings_C2S_Msg",
	[3131] = "HOKGetSettings_S2C_Msg",
	[3132] = "RoomConfirmEnter_S2C_Msg",
	[3133] = "RoomStarPInviteEnterGameAnswer_S2C_Msg",
	[3134] = "RoomStarPInviteEnterGameAnswerNtf",
	[3135] = "RoomStarPInviteEnterGameNtf",
	[3136] = "RoomStarPEnterGameNtf",
	[3137] = "RoomStarPConfirmEnterProgressNtf",
	[3138] = "RoomSPBroadcastInfoNtf",
	[3139] = "StarPEnter_C2S_Msg",
	[3140] = "StarPEnter_S2C_Msg",
	[3141] = "StarPRoleStatus_C2S_Msg",
	[3142] = "StarPRoleStatus_S2C_Msg",
	[3143] = "StarPExit_C2S_Msg",
	[3144] = "StarPExit_S2C_Msg",
	[3145] = "StarPCreateOfficial_C2S_Msg",
	[3146] = "StarPCreateOfficial_S2C_Msg",
	[3147] = "StarPCreate_C2S_Msg",
	[3148] = "StarPPrepareMigrateDsNtf_S2C_Msg",
	[3149] = "StarPAttrNtf",
	[3150] = "StarPCreate_S2C_Msg",
	[3151] = "BatchGetStarPInfo_C2S_Msg",
	[3152] = "BatchGetStarPInfo_S2C_Msg",
	[3153] = "StarPApplyAdminRole_C2S_Msg",
	[3154] = "StarPApplyAdminRole_S2C_Msg",
	[3155] = "StarPDSInfoNtf",
	[3156] = "StarPApply_C2S_Msg",
	[3157] = "StarPApply_S2C_Msg",
	[3158] = "StarPPublish_C2S_Msg",
	[3159] = "StarPPublish_S2C_Msg",
	[3160] = "StarPPlayerNtf",
	[3161] = "StarPOperateApply_C2S_Msg",
	[3162] = "StarPOperateApply_S2C_Msg",
	[3163] = "GetStarPInfoList_C2S_Msg",
	[3164] = "GetStarPInfoList_S2C_Msg",
	[3165] = "GetNearbyStarPInfoList_C2S_Msg",
	[3166] = "GetNearbyStarPInfoList_S2C_Msg",
	[3167] = "GetStarPDetailInfoList_C2S_Msg",
	[3168] = "GetStarPDetailInfoList_S2C_Msg",
	[3169] = "StarPApplyNtf",
	[3170] = "StarPApplyResultNtf",
	[3171] = "StarPModifyRoom_C2S_Msg",
	[3172] = "StarPModifyRoom_S2C_Msg",
	[3173] = "StarPGetMyRole_C2S_Msg",
	[3174] = "StarPGetMyRole_S2C_Msg",
	[3175] = "StarPBanEnter_C2S_Msg",
	[3176] = "StarPBanEnter_S2C_Msg",
	[3177] = "StarPDelUser_C2S_Msg",
	[3178] = "StarPDelUser_S2C_Msg",
	[3179] = "StarPDelRoleByself_C2S_Msg",
	[3180] = "StarPDelRoleByself_S2C_Msg",
	[3181] = "StarPAdminTransfer_C2S_Msg",
	[3182] = "StarPAdminTransfer_S2C_Msg",
	[3183] = "StarPAblerToApplyAdminNtf",
	[3184] = "StarPCheckAblerToApplyAdmin_C2S_Msg",
	[3185] = "StarPCheckAblerToApplyAdmin_S2C_Msg",
	[3186] = "StarPVoteForDelUser_C2S_Msg",
	[3187] = "StarPVoteForDelUser_S2C_Msg",
	[3188] = "StarPInvite_C2S_Msg",
	[3189] = "StarPInvite_S2C_Msg",
	[3190] = "StarPRejectInvite_C2S_Msg",
	[3191] = "StarPRejectInvite_S2C_Msg",
	[3192] = "StarPInviteNtf",
	[3193] = "StarPQueryPos_C2S_Msg",
	[3194] = "StarPQueryPos_S2C_Msg",
	[3195] = "StarPLoadDsCommonDbInfo_C2S_Msg",
	[3196] = "StarPLoadDsCommonDbInfo_S2C_Msg",
	[3197] = "StarPAddplayerResultNtf",
	[3198] = "StarPGetPlayerWorldInfo_C2S_Msg",
	[3199] = "StarPGetPlayerWorldInfo_S2C_Msg",
	[3200] = "StarPChatGroupKeyNtf",
	[3201] = "DefaultStarPIdChangeNtf",
	[3202] = "StarPAccountLoadDsCommonDbInfo_C2S_Msg",
	[3203] = "StarPAccountLoadDsCommonDbInfo_S2C_Msg",
	[3204] = "StarPExitDsNtf",
	[3205] = "StarPGetAchieveGlobalData_C2S_Msg",
	[3206] = "StarPGetAchieveGlobalData_S2C_Msg",
	[3207] = "GetOfficialStarPRoom_C2S_Msg",
	[3208] = "GetOfficialStarPRoom_S2C_Msg",
	[3209] = "TryQuickEnterOfficialStarPRoom_C2S_Msg",
	[3210] = "TryQuickEnterOfficialStarPRoom_S2C_Msg",
	[3211] = "StarPGetDressUpInfo_C2S_Msg",
	[3212] = "StarPGetDressUpInfo_S2C_Msg",
	[3213] = "StarPCreateDressUp_C2S_Msg",
	[3214] = "StarPCreateDressUp_S2C_Msg",
	[3215] = "StarPSaveDressUp_C2S_Msg",
	[3216] = "StarPSaveDressUp_S2C_Msg",
	[3217] = "StarPShipAction_C2S_Msg",
	[3218] = "StarPShipAction_S2C_Msg",
	[3219] = "BatchGetDsCommonDbAccountLevelData_C2S_Msg",
	[3220] = "BatchGetDsCommonDbAccountLevelData_S2C_Msg",
	[3221] = "StarPVisitorGetAvailSp_C2S_Msg",
	[3222] = "StarPVisitorGetAvailSp_S2C_Msg",
	[3223] = "StarPSetGuide_C2S_Msg",
	[3224] = "StarPSetGuide_S2C_Msg",
	[3225] = "StarPGetGuide_C2S_Msg",
	[3226] = "StarPGetGuide_S2C_Msg",
	[3227] = "RoomTest_C2S_Msg",
	[3228] = "RoomTest_S2C_Msg",
	[3230] = "QueryQualifyTypesSeasonSettlementMailReward_C2S_Msg",
	[3231] = "QueryQualifyTypesSeasonSettlementMailReward_S2C_Msg",
	[3232] = "FarmSetConnectCropNewVersion_C2S_Msg",
	[3233] = "FarmSetConnectCropNewVersion_S2C_Msg",
	[3234] = "TradingCardGetCollectionList_C2S_Msg",
	[3235] = "TradingCardGetCollectionList_S2C_Msg",
	[3236] = "TradingCardGetCollectionDetailInfo_C2S_Msg",
	[3237] = "TradingCardGetCollectionDetailInfo_S2C_Msg",
	[3238] = "TradingCardGetNtf",
	[3239] = "TradingCardGetTradeHistory_C2S_Msg",
	[3240] = "TradingCardGetTradeHistory_S2C_Msg",
	[3241] = "TradingCardCreateRequireTrade_C2S_Msg",
	[3242] = "TradingCardCreateRequireTrade_S2C_Msg",
	[3243] = "TradingCardCompleteRequireTrade_C2S_Msg",
	[3244] = "TradingCardCompleteRequireTrade_S2C_Msg",
	[3245] = "TradingCardCreateGiveTrade_C2S_Msg",
	[3246] = "TradingCardCreateGiveTrade_S2C_Msg",
	[3247] = "TradingCardCompleteGiveTrade_C2S_Msg",
	[3248] = "TradingCardCompleteGiveTrade_S2C_Msg",
	[3249] = "TradingCardCreateExchangeTrade_C2S_Msg",
	[3250] = "TradingCardCreateExchangeTrade_S2C_Msg",
	[3251] = "TradingCardCompleteExchangeTrade_C2S_Msg",
	[3252] = "TradingCardCompleteExchangeTrade_S2C_Msg",
	[3255] = "ArenaGetSnap_C2S_Msg",
	[3256] = "ArenaGetSnap_S2C_Msg",
	[3259] = "ArenaSelectShowSkin_C2S_Msg",
	[3260] = "ArenaSelectShowSkin_S2C_Msg",
	[3261] = "ArenaSelectHeadPic_C2S_Msg",
	[3262] = "ArenaSelectHeadPic_S2C_Msg",
	[3263] = "TradingCardExchangeWildCard_C2S_Msg",
	[3264] = "TradingCardExchangeWildCard_S2C_Msg",
	[3265] = "FarmTaskAddStealCropForRedFox_C2S_Msg",
	[3266] = "FarmTaskAddStealCropForRedFox_S2C_Msg",
	[3269] = "DeviceHasNoPermissionNtf",
	[3270] = "DeviceIdCheckErrorNtf",
	[3271] = "IPHasChangedNtf",
	[3272] = "IPBannedNtf",
	[3273] = "StarPGetAllRolePetInfo_C2S_Msg",
	[3274] = "StarPGetAllRolePetInfo_S2C_Msg",
	[3275] = "StarPGetPetFormation_C2S_Msg",
	[3276] = "StarPGetPetFormation_S2C_Msg",
	[3277] = "StarPSetPetFormation_C2S_Msg",
	[3278] = "StarPSetPetFormation_S2C_Msg",
	[3279] = "StarPShockRewardList_C2S_Msg",
	[3280] = "StarPShockRewardList_S2C_Msg",
	[3281] = "StarPGetShockReward_C2S_Msg",
	[3282] = "StarPGetShockReward_S2C_Msg",
	[3283] = "StarPSeasonRewardList_C2S_Msg",
	[3284] = "StarPSeasonRewardList_S2C_Msg",
	[3285] = "StarPGetSeasonReward_C2S_Msg",
	[3286] = "StarPGetSeasonReward_S2C_Msg",
	[3287] = "StarPPvpGetOverallInfo_C2S_Msg",
	[3288] = "StarPPvpGetOverallInfo_S2C_Msg",
	[3289] = "StarPPvpMatchPenaltyStatusNtf",
	[3290] = "StarPGetOrnamentAndAmuletInfo_C2S_Msg",
	[3291] = "StarPGetOrnamentAndAmuletInfo_S2C_Msg",
	[3292] = "ArenaGetCommonlyUsedHero_C2S_Msg",
	[3293] = "ArenaGetCommonlyUsedHero_S2C_Msg",
	[3294] = "UgcOpenStartWorld_C2S_Msg",
	[3295] = "UgcOpenStartWorld_S2C_Msg",
	[3296] = "RejectTestRaffle_C2S_Msg",
	[3297] = "RejectTestRaffle_S2C_Msg",
	[3300] = "MallGetThemeShopCommodity_C2S_Msg",
	[3301] = "MallGetThemeShopCommodity_S2C_Msg",
	[3302] = "MallGetThemeShopDiscount_C2S_Msg",
	[3303] = "MallGetThemeShopDiscount_S2C_Msg",
	[3304] = "SetIntimateOnlineNoticeBox_C2S_Msg",
	[3305] = "SetIntimateOnlineNoticeBox_S2C_Msg",
	[3306] = "PlayerAntiCheatInfoInitNtf",
	[3307] = "PlayerAntiCheatInfoReport_C2S_Msg",
	[3308] = "PlayerAntiCheatInfoReport_S2C_Msg",
	[3309] = "PlayerAntiCheatInfoToClientNtf",
	[3311] = "FarmFishMerge_C2S_Msg",
	[3312] = "FarmFishMerge_S2C_Msg",
	[3314] = "CookCreate_C2S_Msg",
	[3315] = "CookCreate_S2C_Msg",
	[3316] = "CookEnter_C2S_Msg",
	[3317] = "CookEnter_S2C_Msg",
	[3318] = "CookExit_C2S_Msg",
	[3319] = "CookExit_S2C_Msg",
	[3320] = "CookKickNtf",
	[3321] = "CookDSInfoNtf",
	[3322] = "CookAttrNtf",
	[3323] = "CookOwnerAttrNtf",
	[3324] = "CookForwardToDS_C2S_Msg",
	[3325] = "CookForwardToDS_S2C_Msg",
	[3326] = "CookGetOfflineIncome_C2S_Msg",
	[3327] = "CookGetOfflineIncome_S2C_Msg",
	[3328] = "CookDoSettlement_C2S_Msg",
	[3329] = "CookDoSettlement_S2C_Msg",
	[3330] = "CookHireFriend_C2S_Msg",
	[3331] = "CookHireFriend_S2C_Msg",
	[3332] = "CookFireFriend_C2S_Msg",
	[3333] = "CookFireFriend_S2C_Msg",
	[3334] = "CookPoachFriend_C2S_Msg",
	[3335] = "CookPoachFriend_S2C_Msg",
	[3338] = "CookOpen_C2S_Msg",
	[3339] = "CookOpen_S2C_Msg",
	[3340] = "CookClose_C2S_Msg",
	[3341] = "CookClose_S2C_Msg",
	[3344] = "RafflePickInventory_C2S_Msg",
	[3345] = "RafflePickInventory_S2C_Msg",
	[3346] = "RewardRetrievalInfo_C2S_Msg",
	[3347] = "RewardRetrievalInfo_S2C_Msg",
	[3348] = "RewardRetrievalRetrieve_C2S_Msg",
	[3349] = "RewardRetrievalRetrieve_S2C_Msg",
	[3350] = "RewardRetrievalUpdateNtf",
	[3351] = "RewardRetrievalClearRedDot_C2S_Msg",
	[3352] = "RewardRetrievalClearRedDot_S2C_Msg",
	[3353] = "ChangeMallWishList_C2S_Msg",
	[3354] = "ChangeMallWishList_S2C_Msg",
	[3355] = "FollowOtherMallWishList_C2S_Msg",
	[3356] = "FollowOtherMallWishList_S2C_Msg",
	[3357] = "WolfKillGetComeBackInfo_C2S_Msg",
	[3358] = "WolfKillGetComeBackInfo_S2C_Msg",
	[3359] = "WolfKillGetComeBackReward_C2S_Msg",
	[3360] = "WolfKillGetComeBackReward_S2C_Msg",
	[3361] = "FittingItemStatusChange_C2S_Msg",
	[3362] = "FittingItemStatusChange_S2C_Msg",
	[3363] = "TradingCardShareToChat_C2S_Msg",
	[3364] = "TradingCardShareToChat_S2C_Msg",
	[3433] = "GuideConfigDiscoverStatusQuery_C2S_Msg",
	[3434] = "GuideConfigDiscoverStatusQuery_S2C_Msg",
	[3435] = "GuideConfigDiscoverStatusUpdate_S2C_Msg",
	[3436] = "GuideConfigDiscoverStatusUpdate_C2S_Msg",
	[3437] = "StarPPVPGetDailyReward_C2S_Msg",
	[3438] = "StarPPVPGetDailyReward_S2C_Msg",
	[3439] = "FarmCommitItemToCloud_C2S_Msg",
	[3440] = "FarmCommitItemToCloud_S2C_Msg",
	[3441] = "TradingCardGetTradeInfoListCache_C2S_Msg",
	[3442] = "TradingCardGetTradeInfoListCache_S2C_Msg",
	[3443] = "TradingCardGetTradeInfo_C2S_Msg",
	[3444] = "TradingCardGetTradeInfo_S2C_Msg",
	[3449] = "GeneralReadDotNtf",
	[3450] = "ClickGeneralRedDot_C2S_Msg",
	[3451] = "ClickGeneralRedDot_S2C_Msg",
	[3468] = "CookSendComment_C2S_Msg",
	[3469] = "CookSendComment_S2C_Msg",
	[3470] = "TradingCardClearRedDot_C2S_Msg",
	[3471] = "TradingCardClearRedDot_S2C_Msg",
	[3472] = "TradingCardRedDotChangeNtf",
	[3473] = "CustomRoomList_C2S_Msg",
	[3474] = "CustomRoomList_S2C_Msg",
	[3475] = "CustomRoomRoundLevelList_C2S_Msg",
	[3476] = "CustomRoomRoundLevelList_S2C_Msg",
	[3477] = "CookDeleteComment_C2S_Msg",
	[3478] = "CookDeleteComment_S2C_Msg",
	[3479] = "CookGetComment_C2S_Msg",
	[3480] = "CookGetComment_S2C_Msg",
	[3481] = "CookReceiveNewCommentNtf",
	[3482] = "CookVisitantPrebook_C2S_Msg",
	[3483] = "CookVisitantPrebook_S2C_Msg",
	[3491] = "CookVisitantSteal_C2S_Msg",
	[3492] = "CookVisitantSteal_S2C_Msg",
	[3493] = "SetMood_C2S_Msg",
	[3494] = "SetMood_S2C_Msg",
	[3495] = "TradingCardDrawCycleCupsReward_C2S_Msg",
	[3496] = "TradingCardDrawCycleCupsReward_S2C_Msg",
	[3497] = "AiNpcLicense_C2S_Msg",
	[3498] = "AiNpcLicense_S2C_Msg",
	[3499] = "PlayerEnterCocGameScene_C2S_Msg",
	[3500] = "PlayerEnterCocGameScene_S2C_Msg",
	[3501] = "PlayerExitCocGameScene_C2S_Msg",
	[3502] = "PlayerExitCocGameScene_S2C_Msg",
	[3503] = "CocBuildBuilding_C2S_Msg",
	[3504] = "CocBuildBuilding_S2C_Msg",
	[3505] = "CocUpgradeBuilding_C2S_Msg",
	[3506] = "CocUpgradeBuilding_S2C_Msg",
	[3507] = "CocFastFinishBuilding_C2S_Msg",
	[3508] = "CocFastFinishBuilding_S2C_Msg",
	[3509] = "CocCancelBuildBuilding_C2S_Msg",
	[3510] = "CocCancelBuildBuilding_S2C_Msg",
	[3511] = "CocCancelUpgradeBuilding_C2S_Msg",
	[3512] = "CocCancelUpgradeBuilding_S2C_Msg",
	[3513] = "CocDemolishBuilding_C2S_Msg",
	[3514] = "CocDemolishBuilding_S2C_Msg",
	[3515] = "CocMoveBuilding_C2S_Msg",
	[3516] = "CocMoveBuilding_S2C_Msg",
	[3517] = "CocStartMatch_C2S_Msg",
	[3518] = "CocStartMatch_S2C_Msg",
	[3519] = "CocStartFight_C2S_Msg",
	[3520] = "CocStartFight_S2C_Msg",
	[3521] = "CocFightingDeltaInfo_C2S_Msg",
	[3522] = "CocFightingDeltaInfo_S2C_Msg",
	[3523] = "CocFightSettle_C2S_Msg",
	[3524] = "CocFightSettle_S2C_Msg",
	[3525] = "CocQueryMatchPlayerInfo_C2S_Msg",
	[3526] = "CocQueryMatchPlayerInfo_S2C_Msg",
	[3527] = "CocUpgradeScience_C2S_Msg",
	[3528] = "CocUpgradeScience_S2C_Msg",
	[3529] = "CocCompleteScience_C2S_Msg",
	[3530] = "CocCompleteScience_S2C_Msg",
	[3531] = "CocSoldierTraining_C2S_Msg",
	[3532] = "CocSoldierTraining_S2C_Msg",
	[3533] = "CocSoldierModify_C2S_Msg",
	[3534] = "CocSoldierModify_S2C_Msg",
	[3535] = "CocSoldierPreset_C2S_Msg",
	[3536] = "CocSoldierPreset_S2C_Msg",
	[3537] = "CocSoldierUsePreset_C2S_Msg",
	[3538] = "CocSoldierUsePreset_S2C_Msg",
	[3539] = "CocCollectResource_C2S_Msg",
	[3540] = "CocCollectResource_S2C_Msg",
	[3541] = "CocDuanWeiReward_C2S_Msg",
	[3542] = "CocDuanWeiReward_S2C_Msg",
	[3543] = "CocTreasureBoxOperate_C2S_Msg",
	[3544] = "CocTreasureBoxOperate_S2C_Msg",
	[3545] = "CocUpResourceEfficiency_C2S_Msg",
	[3546] = "CocUpResourceEfficiency_S2C_Msg",
	[3547] = "CocMarkDialog_C2S_Msg",
	[3548] = "CocMarkDialog_S2C_Msg",
	[3555] = "CocUnlockMapPrimaryRegion_C2S_Msg",
	[3556] = "CocUnlockMapPrimaryRegion_S2C_Msg",
	[3557] = "CocUnlockMapMinorRegion_C2S_Msg",
	[3558] = "CocUnlockMapMinorRegion_S2C_Msg",
	[3559] = "CocGetXingbaoCaptureReward_C2S_Msg",
	[3560] = "CocGetXingbaoCaptureReward_S2C_Msg",
	[3561] = "CocPlayerNumPropDetailsNtf",
	[3562] = "CocReleaseXingbao_C2S_Msg",
	[3563] = "CocReleaseXingbao_S2C_Msg",
	[3564] = "CocGetOfflineEventOnLoadComplete_C2S_Msg",
	[3565] = "CocGetOfflineEventOnLoadComplete_S2C_Msg",
	[3566] = "CocUnlockVillager_C2S_Msg",
	[3567] = "CocUnlockVillager_S2C_Msg",
	[3568] = "CocAppointOfficialPosForVillager_C2S_Msg",
	[3569] = "CocAppointOfficialPosForVillager_S2C_Msg",
	[3570] = "CocStartVillagerDialogue_C2S_Msg",
	[3571] = "CocStartVillagerDialogue_S2C_Msg",
	[3572] = "CocMarkVillagerDialogueFinishKeyStep_C2S_Msg",
	[3573] = "CocMarkVillagerDialogueFinishKeyStep_S2C_Msg",
	[3574] = "CocCompleteVillagerDialogue_C2S_Msg",
	[3575] = "CocCompleteVillagerDialogue_S2C_Msg",
	[3576] = "CocVillagerChangeDress_C2S_Msg",
	[3577] = "CocVillagerChangeDress_S2C_Msg",
	[3578] = "CocModifyVillagerName_C2S_Msg",
	[3579] = "CocModifyVillagerName_S2C_Msg",
	[3580] = "CocAcceptVillagerDailyFavorReward_C2S_Msg",
	[3581] = "CocAcceptVillagerDailyFavorReward_S2C_Msg",
	[3582] = "CocEnterOtherScene_C2S_Msg",
	[3583] = "CocEnterOtherScene_S2C_Msg",
	[3584] = "CocBuyCocMonthCard_C2S_Msg",
	[3585] = "CocBuyCocMonthCard_S2C_Msg",
	[3586] = "CocMonthCardDailyReward_C2S_Msg",
	[3587] = "CocMonthCardDailyReward_S2C_Msg",
	[3588] = "CocGetTaskReward_C2S_Msg",
	[3589] = "CocGetTaskReward_S2C_Msg",
	[3590] = "CocRewardNtf",
	[3591] = "CocBatchUpgradeBuilding_C2S_Msg",
	[3592] = "CocBatchUpgradeBuilding_S2C_Msg",
	[3595] = "CocStartFightBattle_C2S_Msg",
	[3596] = "CocStartFightBattle_S2C_Msg",
	[3597] = "CocSettleBattleGame_C2S_Msg",
	[3598] = "CocSettleBattleGame_S2C_Msg",
	[3599] = "MarkCocDefenseMailHasFoughtBack_C2S_Msg",
	[3600] = "MarkCocDefenseMailHasFoughtBack_S2C_Msg",
	[3607] = "AppearanceRoadGetGatherAward_C2S_Msg",
	[3608] = "AppearanceRoadGetGatherAward_S2C_Msg",
	[3609] = "AppearanceRoadGetLvAward_C2S_Msg",
	[3610] = "AppearanceRoadGetLvAward_S2C_Msg",
	[3611] = "StarPVisitSameWorld_C2S_Msg",
	[3612] = "StarPVisitSameWorld_S2C_Msg",
	[3613] = "AiNpcCheckText_C2S_Msg",
	[3614] = "AiNpcCheckText_S2C_Msg",
	[3615] = "AiNpcSubmitTempPalProfile_C2S_Msg",
	[3616] = "AiNpcSubmitTempPalProfile_S2C_Msg",
	[3617] = "AiNpcSubmitPlayerProfile_C2S_Msg",
	[3618] = "AiNpcSubmitPlayerProfile_S2C_Msg",
	[3619] = "AiNpcGenCustomPal_C2S_Msg",
	[3620] = "AiNpcGenCustomPal_S2C_Msg",
	[3621] = "AiNpcFinishGuide_C2S_Msg",
	[3622] = "AiNpcFinishGuide_S2C_Msg",
	[3623] = "WolfKillTreasureUse_C2S_Msg",
	[3624] = "WolfKillTreasureUse_S2C_Msg",
	[3625] = "WolfKillVocation_C2S_Msg",
	[3626] = "WolfKillVocation_S2C_Msg",
	[3627] = "CocGetFriendGift_C2S_Msg",
	[3628] = "CocGetFriendGift_S2C_Msg",
	[3629] = "CocSendFriendDonation_C2S_Msg",
	[3630] = "CocSendFriendDonation_S2C_Msg",
	[3631] = "CocReceiveFriendDonationNtf",
	[3632] = "CocAllocBattleGame_C2S_Msg",
	[3633] = "CocAllocBattleGame_S2C_Msg",
	[3634] = "CocAppointFriendVillager_C2S_Msg",
	[3635] = "CocAppointFriendVillager_S2C_Msg",
	[3636] = "CocVisitFriendHome_C2S_Msg",
	[3637] = "CocVisitFriendHome_S2C_Msg",
	[3638] = "FarmVillagerGetFavorExp_C2S_Msg",
	[3639] = "FarmVillagerGetFavorExp_S2C_Msg",
	[3640] = "GiveMailAttachmentsNtf",
	[3641] = "CocPrisonAssign_C2S_Msg",
	[3642] = "CocPrisonAssign_S2C_Msg",
	[3643] = "AnniversaryMobaDrawReward_C2S_Msg",
	[3644] = "AnniversaryMobaGetReward_S2C_Msg",
	[3645] = "AnniversaryMobaGetReward_C2S_Msg",
	[3646] = "AnniversaryMobaDrawReward_S2C_Msg",
	[3649] = "WolfKillTreasureRead_C2S_Msg",
	[3650] = "WolfKillTreasureRead_S2C_Msg",
	[3651] = "ArenaTipRewardDetailsNtf",
	[3652] = "CocDonationAccelerate_C2S_Msg",
	[3653] = "CocDonationAccelerate_S2C_Msg",
	[3654] = "CocSetBattleBuildingAttackMode_C2S_Msg",
	[3655] = "CocSetBattleBuildingAttackMode_S2C_Msg",
	[3656] = "AiNpcMoodChangeNtf",
	[3661] = "CocRemoveAfterReleaseXingBao_C2S_Msg",
	[3662] = "CocRemoveAfterReleaseXingBao_S2C_Msg",
	[3663] = "WolfKillTreasureLevelRead_C2S_Msg",
	[3664] = "WolfKillTreasureLevelRead_S2C_Msg",
	[3669] = "WolfKillTreasureRefreshLevel_C2S_Msg",
	[3670] = "WolfKillTreasureRefreshLevel_S2C_Msg",
	[3671] = "AnniversaryMobaGetRewardTimes_C2S_Msg",
	[3672] = "AnniversaryMobaGetRewardTimes_S2C_Msg",
	[3673] = "StarPCreateRole_C2S_Msg",
	[3674] = "StarPCreateRole_S2C_Msg",
	[3675] = "StarPDeleteRole_C2S_Msg",
	[3676] = "StarPDeleteRole_S2C_Msg",
	[3677] = "StarPGetRoleList_C2S_Msg",
	[3678] = "StarPGetRoleList_S2C_Msg",
	[3679] = "ChatBatchSend_C2S_Msg",
	[3680] = "ChatBatchSend_S2C_Msg",
	[3682] = "FarmVillagerSwitchScene_C2S_Msg",
	[3683] = "FarmVillagerSwitchScene_S2C_Msg",
	[3686] = "ArenaEnterPrepareInterface_C2S_Msg",
	[3687] = "ArenaEnterPrepareInterface_S2C_Msg",
	[3688] = "FarmEnterHotSpring_C2S_Msg",
	[3689] = "FarmEnterHotSpring_S2C_Msg",
	[3690] = "FarmLeaveHotSpring_C2S_Msg",
	[3691] = "FarmLeaveHotSpring_S2C_Msg",
	[3692] = "FarmGetHotSpringBuff_C2S_Msg",
	[3693] = "FarmGetHotSpringBuff_S2C_Msg",
	[3696] = "FarmChangeSignature_C2S_Msg",
	[3697] = "FarmChangeSignature_S2C_Msg",
	[3698] = "MobaSquadDrawRedPacketDraw_C2S_Msg",
	[3699] = "MobaSquadDrawRedPacketDraw_S2C_Msg",
	[3702] = "StreamDsGeneral_C2S_Msg",
	[3703] = "StreamDsGeneral_S2C_Msg",
	[3704] = "ActivityNewYearSign_C2S_Msg",
	[3705] = "ActivityNewYearSign_S2C_Msg",
	[3706] = "LobbyListFind_C2S_Msg",
	[3707] = "LobbyListFind_S2C_Msg",
	[3708] = "GetDailyBattleOrnamentationInfo_C2S_Msg",
	[3709] = "GetDailyBattleOrnamentationInfo_S2C_Msg",
	[3710] = "UpdatedDailyBattleOrnamentationInfo_C2S_Msg",
	[3711] = "UpdatedDailyBattleOrnamentationInfo_S2C_Msg",
	[3714] = "FarmQueryNpcCD_C2S_Msg",
	[3715] = "FarmQueryNpcCD_S2C_Msg",
	[3716] = "FarmInvokeNpc_C2S_Msg",
	[3717] = "FarmInvokeNpc_S2C_Msg",
	[3718] = "FarmPrayToGodFigure_C2S_Msg",
	[3719] = "FarmPrayToGodFigure_S2C_Msg",
	[3720] = "FarmUpgradeGodFigure_C2S_Msg",
	[3721] = "FarmUpgradeGodFigure_S2C_Msg",
	[3722] = "UgcInitiateEvaluation_C2S_Msg",
	[3723] = "UgcInitiateEvaluation_S2C_Msg",
	[3727] = "AmsRewardInfoNtf",
	[3729] = "MallGiftCardChangeNtf",
	[3730] = "FarmMagicUse_C2S_Msg",
	[3731] = "FarmMagicUse_S2C_Msg",
	[3732] = "FarmMagicShortenHarvestTimeNtf",
	[3733] = "FarmMagicEquip_C2S_Msg",
	[3734] = "FarmMagicEquip_S2C_Msg",
	[3735] = "LobbyMatchResultNtf",
	[3736] = "LobbyMatchCancelResultNtf",
	[3739] = "FarmSetVisitorListTag_C2S_Msg",
	[3740] = "FarmSetVisitorListTag_S2C_Msg",
	[3741] = "HouseSetVisitorListTag_C2S_Msg",
	[3742] = "HouseSetVisitorListTag_S2C_Msg",
	[3743] = "XiaoWoSetVisitorListTag_C2S_Msg",
	[3744] = "XiaoWoSetVisitorListTag_S2C_Msg",
	[3745] = "StarPSetChatPetIconID_C2S_Msg",
	[3746] = "StarPSetChatPetIconID_S2C_Msg",
	[3749] = "ReadOtherMallWishList_C2S_Msg",
	[3750] = "ReadOtherMallWishList_S2C_Msg",
	[3751] = "StarPSetChatCupIconID_C2S_Msg",
	[3752] = "StarPSetChatCupIconID_S2C_Msg",
	[3755] = "ClearMonthCardExpiredRedPoint_C2S_Msg",
	[3756] = "ClearMonthCardExpiredRedPoint_S2C_Msg",
	[3757] = "MonthCardRedPointNtf",
	[3758] = "CookLevelUp_C2S_Msg",
	[3759] = "CookLevelUp_S2C_Msg",
	[3760] = "CookSendCommentPhoto_C2S_Msg",
	[3761] = "CookSendCommentPhoto_S2C_Msg",
	[3770] = "RpcTestConfigSvr_C2S_Msg",
	[3771] = "RpcTestConfigSvr_S2C_Msg",
	[3772] = "FindPartnerAnswerQuestion_C2S_Msg",
	[3773] = "FindPartnerAnswerQuestion_S2C_Msg",
	[3774] = "FindPartnerGetQuestions_C2S_Msg",
	[3775] = "FindPartnerGetQuestions_S2C_Msg",
	[3778] = "UgcMapExtraConfigEdit_C2S_Msg",
	[3779] = "UgcMapExtraConfigEdit_S2C_Msg",
	[3780] = "UgcMapExtraConfigGet_C2S_Msg",
	[3781] = "UgcMapExtraConfigGet_S2C_Msg",
	[3782] = "UpdatedReadyBattleOrnamentationBagInfo_C2S_Msg",
	[3783] = "UpdatedReadyBattleOrnamentationBagInfo_S2C_Msg",
	[3787] = "ActivitySquadAcquireReward_C2S_Msg",
	[3788] = "ActivitySquadAcquireReward_S2C_Msg",
	[3789] = "ActivitySquadOpenFinalTreasure_C2S_Msg",
	[3790] = "ActivitySquadOpenFinalTreasure_S2C_Msg",
	[3791] = "GetPlayPufferInfo_C2S_Msg",
	[3792] = "GetPlayPufferInfo_S2C_Msg",
	[3793] = "GetSeasonReviewInfo_C2S_Msg",
	[3794] = "GetSeasonReviewInfo_S2C_Msg",
	[3795] = "MallRedPointClear_C2S_Msg",
	[3796] = "MallRedPointClear_S2C_Msg",
	[3797] = "SummonFortuneSlip_C2S_Msg",
	[3798] = "SummonFortuneSlip_S2C_Msg",
	[3799] = "ReceiveCollectionReward_C2S_Msg",
	[3800] = "ReceiveCollectionReward_S2C_Msg",
	[3801] = "GetLuckyStarFriendList_C2S_Msg",
	[3802] = "GetLuckyStarFriendList_S2C_Msg",
	[3803] = "GetLuckyStarAssistRecordList_C2S_Msg",
	[3804] = "GetLuckyStarAssistRecordList_S2C_Msg",
	[3805] = "LuckyStarRequestNormalAssist_C2S_Msg",
	[3806] = "LuckyStarRequestNormalAssist_S2C_Msg",
	[3807] = "LuckyStarNormalAssist_C2S_Msg",
	[3808] = "LuckyStarNormalAssist_S2C_Msg",
	[3809] = "LuckyStarRequestSpecifyAssist_C2S_Msg",
	[3810] = "LuckyStarRequestSpecifyAssist_S2C_Msg",
	[3811] = "LuckyStarSpecifyAssist_C2S_Msg",
	[3812] = "LuckyStarSpecifyAssist_S2C_Msg",
	[3813] = "LuckyStarGiveSlip_C2S_Msg",
	[3814] = "LuckyStarGiveSlip_S2C_Msg",
	[3815] = "LuckyStarRequestSlip_C2S_Msg",
	[3816] = "LuckyStarRequestSlip_S2C_Msg",
	[3817] = "ScoreGuidePopUpNtf",
	[3818] = "ScoreGuidePopUpProcess_C2S_Msg",
	[3819] = "ScoreGuidePopUpProcess_S2C_Msg",
	[3820] = "TradingCardExchangeReward_C2S_Msg",
	[3821] = "TradingCardExchangeReward_S2C_Msg",
	[3822] = "WolfKillTreasureData_C2S_Msg",
	[3823] = "WolfKillTreasureData_S2C_Msg",
	[3824] = "BirthdayInfo_C2S_Msg",
	[3825] = "BirthdayInfo_S2C_Msg",
	[3826] = "BirthdayChange_C2S_Msg",
	[3827] = "BirthdayChange_S2C_Msg",
	[3828] = "BirthdayBlessingRecord_C2S_Msg",
	[3829] = "BirthdayBlessingRecord_S2C_Msg",
	[3830] = "BirthdayBlessingRecordRedDotNtf",
	[3831] = "BirthdayBlessingRecordClearRedDot_C2S_Msg",
	[3832] = "BirthdayBlessingRecordClearRedDot_S2C_Msg",
	[3833] = "BirthdayCardSend_C2S_Msg",
	[3834] = "BirthdayCardSend_S2C_Msg",
	[3835] = "BirthdayBlessingSend_C2S_Msg",
	[3836] = "BirthdayBlessingSend_S2C_Msg",
	[3837] = "BirthdayCardItemAddNtf",
	[3838] = "BirthdayCardItemDelete_C2S_Msg",
	[3839] = "BirthdayCardItemDelete_S2C_Msg",
	[3840] = "BirthdayOfficialWelfareGet_C2S_Msg",
	[3841] = "BirthdayOfficialWelfareGet_S2C_Msg",
	[3842] = "GMRespDataNtf",
	[3843] = "RafflePurchaseSubItem_C2S_Msg",
	[3844] = "RafflePurchaseSubItem_S2C_Msg",
	[3845] = "TradingCardDrawNoviceReward_C2S_Msg",
	[3846] = "TradingCardDrawNoviceReward_S2C_Msg",
	[3847] = "GetLobbyMatchHistoryList_C2S_Msg",
	[3848] = "GetLobbyMatchHistoryList_S2C_Msg",
	[3849] = "RecruitOrderRaffleConfirm_C2S_Msg",
	[3850] = "RecruitOrderRaffleConfirm_S2C_Msg",
	[3851] = "RecruitOrderRaffleAddressConfirm_C2S_Msg",
	[3852] = "RecruitOrderRaffleAddressConfirm_S2C_Msg",
	[3853] = "ShareGiftReward_C2S_Msg",
	[3854] = "ShareGiftReward_S2C_Msg",
	[3855] = "ShareHistoryGet_C2S_Msg",
	[3856] = "ShareHistoryGet_S2C_Msg",
	[3859] = "ActivitySquadKickMember_C2S_Msg",
	[3860] = "ActivitySquadKickMember_S2C_Msg",
	[3861] = "BatchGetChatShareGift_C2S_Msg",
	[3862] = "BatchGetChatShareGift_S2C_Msg",
	[3863] = "StarPRoleSelect_C2S_Msg",
	[3864] = "StarPRoleSelect_S2C_Msg",
	[3867] = "TradingCardShowNoviceReward_C2S_Msg",
	[3868] = "TradingCardShowNoviceReward_S2C_Msg",
	[3869] = "BPExpChangeNtf",
	[3870] = "CookSetClientKV_C2S_Msg",
	[3871] = "CookSetClientKV_S2C_Msg",
	[3872] = "ActivitySquadFarmWateringTree_C2S_Msg",
	[3873] = "ActivitySquadFarmWateringTree_S2C_Msg",
	[3874] = "TradingCardGetActivity_C2S_Msg",
	[3875] = "TradingCardGetActivity_S2C_Msg",
	[3876] = "ArenaGetHeroBattleStatistic_C2S_Msg",
	[3877] = "ArenaGetHeroBattleStatistic_S2C_Msg",
	[3878] = "BirthdayRemindNtf",
	[3881] = "FarmHotSpringTickNtf",
	[3882] = "SetSprayPaint_C2S_Msg",
	[3883] = "SetSprayPaint_S2C_Msg",
	[3884] = "UseSprayPaint_C2S_Msg",
	[3885] = "UseSprayPaint_S2C_Msg",
	[3886] = "CookEmployeeOp_C2S_Msg",
	[3887] = "CookEmployeeOp_S2C_Msg",
	[3889] = "BirthdayCardChangeNtf",
	[3896] = "QueryLuckStarCount_C2S_Msg",
	[3897] = "QueryLuckStarCount_S2C_Msg",
	[3898] = "StarPRecruitQuery_C2S_Msg",
	[3899] = "StarPRecruitQuery_S2C_Msg",
	[3900] = "StarPRecruitPublish_C2S_Msg",
	[3901] = "StarPRecruitPublish_S2C_Msg",
	[3904] = "FetchBatchSnapshotInfo_C2S_Msg",
	[3905] = "FetchBatchSnapshotInfo_S2C_Msg",
	[3906] = "BirthdayCardItemUpdateNtf",
	[3907] = "BirthdayBlessingRecordUpdateNtf",
	[3908] = "RoomGetCurRoundInfo_C2S_Msg",
	[3909] = "RoomGetCurRoundInfo_S2C_Msg",
	[3910] = "RoomRoundInfoNtf",
	[3911] = "RoomUgcMultiRoundCoinUse_C2S_Msg",
	[3912] = "RoomUgcMultiRoundCoinUse_S2C_Msg",
	[3913] = "ActivityFarmBuffWishSupport_C2S_Msg",
	[3914] = "ActivityFarmBuffWishSupport_S2C_Msg",
	[3915] = "ActivityFarmBuffWishInfo_C2S_Msg",
	[3916] = "ActivityFarmBuffWishInfo_S2C_Msg",
	[3917] = "ActivityFarmBuffWishSupportList_C2S_Msg",
	[3918] = "ActivityFarmBuffWishSupportList_S2C_Msg",
	[3921] = "PrayerCardRewardReachLimitNtf",
	[3922] = "BatchGetLatestMessageExtraInfo_C2S_Msg",
	[3923] = "BatchGetLatestMessageExtraInfo_S2C_Msg",
	[3924] = "ActivityFarmBuffWishReward_C2S_Msg",
	[3925] = "ActivityFarmBuffWishReward_S2C_Msg",
	[3928] = "BirthdayRemindInfo_C2S_Msg",
	[3929] = "BirthdayRemindInfo_S2C_Msg",
	[3930] = "BirthdayRemindClear_C2S_Msg",
	[3931] = "BirthdayRemindClear_S2C_Msg",
	[3932] = "CookVisitantServe_C2S_Msg",
	[3933] = "CookVisitantServe_S2C_Msg",
	[3936] = "BPDoUpgradeNtf",
	[3943] = "AMSItemResultNtf",
	[3944] = "StarPSubmitAssistOrder_C2S_Msg",
	[3945] = "StarPSubmitAssistOrder_S2C_Msg",
	[3946] = "StarPFindAssistOrder_C2S_Msg",
	[3947] = "StarPFindAssistOrder_S2C_Msg",
	[3948] = "StarPGetAssistOrderList_C2S_Msg",
	[3949] = "StarPGetAssistOrderList_S2C_Msg",
	[3950] = "StarPLikeAssistPlayer_C2S_Msg",
	[3951] = "StarPLikeAssistPlayer_S2C_Msg",
	[3954] = "FarmItemBanSell_C2S_Msg",
	[3955] = "FarmItemBanSell_S2C_Msg",
	[3956] = "HouseSwitchRoom_C2S_Msg",
	[3957] = "HouseSwitchRoom_S2C_Msg",
	[3964] = "CookSetFloatingScreenContent_C2S_Msg",
	[3965] = "CookSetFloatingScreenContent_S2C_Msg",
	[3968] = "MobaSquadDrawRedPacketRefreshNtf",
	[3969] = "ActivityFarmReturningTaskInfo_C2S_Msg",
	[3970] = "ActivityFarmReturningTaskInfo_S2C_Msg",
	[3971] = "ActivityFarmReturningTaskReward_C2S_Msg",
	[3972] = "ActivityFarmReturningTaskReward_S2C_Msg",
	[3974] = "StarPAssistOrderStatusNtf",
	[3977] = "GetGlobalRecentPlayRecord_C2S_Msg",
	[3978] = "GetGlobalRecentPlayRecord_S2C_Msg",
	[3979] = "FarmMagicEffectNtf",
	[3980] = "StarPJoinRecruit_C2S_Msg",
	[3981] = "StarPJoinRecruit_S2C_Msg",
	[3983] = "StarPQueryGroupApplication_C2S_Msg",
	[3984] = "StarPQueryGroupApplication_S2C_Msg",
	[3985] = "StarPInviteJoinGroup_C2S_Msg",
	[3986] = "StarPInviteJoinGroup_S2C_Msg",
	[3987] = "StarPQueryGroupInvitation_C2S_Msg",
	[3988] = "StarPQueryGroupInvitation_S2C_Msg",
	[3989] = "StarPApplyJoinGroup_C2S_Msg",
	[3990] = "StarPApplyJoinGroup_S2C_Msg",
	[3991] = "StarPAcceptInvitation_C2S_Msg",
	[3992] = "StarPAcceptInvitation_S2C_Msg",
	[3993] = "StarPAcceptApplication_C2S_Msg",
	[3994] = "StarPAcceptApplication_S2C_Msg",
	[3995] = "StarPGetMyContactRecordList_C2S_Msg",
	[3996] = "StarPGetMyContactRecordList_S2C_Msg",
	[3997] = "StarPRecommendGroupList_C2S_Msg",
	[3998] = "StarPRecommendGroupList_S2C_Msg",
	[3999] = "StarPGroupMemberList_C2S_Msg",
	[4000] = "StarPGroupMemberList_S2C_Msg",
	[4003] = "StarPGetPlayerWearItemInfo_C2S_Msg",
	[4004] = "StarPGetPlayerWearItemInfo_S2C_Msg",
	[4005] = "StarPGetGroupInfo_C2S_Msg",
	[4006] = "StarPGetGroupInfo_S2C_Msg",
	[4009] = "ArenaHeroStarRewardHeroLevel_C2S_Msg",
	[4010] = "ArenaHeroStarRewardHeroLevel_S2C_Msg",
	[4011] = "ArenaHeroStarRewardGeneralLevel_C2S_Msg",
	[4012] = "ArenaHeroStarRewardGeneralLevel_S2C_Msg",
	[4017] = "SetPackTag_C2S_Msg",
	[4018] = "SetPackTag_S2C_Msg",
	[4023] = "StarPGroupSaveSettings_C2S_Msg",
	[4024] = "StarPGroupSaveSettings_S2C_Msg",
	[4025] = "StarPKickGroupMember_C2S_Msg",
	[4026] = "StarPKickGroupMember_S2C_Msg",
	[4027] = "HouseSaveLayout_C2S_Msg",
	[4028] = "HouseSaveLayout_S2C_Msg",
	[4029] = "HouseGetLayoutList_C2S_Msg",
	[4030] = "HouseGetLayoutList_S2C_Msg",
	[4031] = "HouseDelLayout_C2S_Msg",
	[4032] = "HouseDelLayout_S2C_Msg",
	[4033] = "StarPSetJoinType_C2S_Msg",
	[4034] = "StarPSetJoinType_S2C_Msg",
	[4035] = "StarPSetTitle_C2S_Msg",
	[4036] = "StarPSetTitle_S2C_Msg",
	[4037] = "StarPGroupAttrNtf",
	[4038] = "HouseFurnitureBuyReplace_C2S_Msg",
	[4039] = "HouseFurnitureBuyReplace_S2C_Msg",
	[4040] = "FetchBatchInfo_C2S_Msg",
	[4041] = "FetchBatchInfo_S2C_Msg",
	[4044] = "FetchRankNoByScore_C2S_Msg",
	[4045] = "FetchRankNoByScore_S2C_Msg",
	[4046] = "FarmPetFeedDetail_C2S_Msg",
	[4047] = "FarmPetFeedDetail_S2C_Msg",
	[4054] = "FarmQueryRipeTimeBeforeRain_C2S_Msg",
	[4055] = "FarmQueryRipeTimeBeforeRain_S2C_Msg",
	[4056] = "UgcOperatePublishCoCreateMap_C2S_Msg",
	[4057] = "UgcOperatePublishCoCreateMap_S2C_Msg",
	[4058] = "PressGmAddItem_C2S_Msg",
	[4059] = "PressGmAddItem_S2C_Msg",
	[4060] = "HouseGetLayoutDetail_C2S_Msg",
	[4061] = "HouseGetLayoutDetail_S2C_Msg",
	[4062] = "HouseLayoutApplyCheck_C2S_Msg",
	[4063] = "HouseLayoutApplyCheck_S2C_Msg",
	[4064] = "ExchangeShard_C2S_Msg",
	[4065] = "ExchangeShard_S2C_Msg",
	[4066] = "StarPBatchGetGuildPublicInfo_C2S_Msg",
	[4067] = "StarPBatchGetGuildPublicInfo_S2C_Msg",
	[4068] = "StarPPetTradeUpdateWish_C2S_Msg",
	[4069] = "StarPPetTradeUpdateWish_S2C_Msg",
	[4070] = "StarPPetTradeCancelWish_C2S_Msg",
	[4071] = "StarPPetTradeCancelWish_S2C_Msg",
	[4072] = "StarPPetTradeFocusPlayer_C2S_Msg",
	[4073] = "StarPUpdatePetTradeWish_S2C_Msg",
	[4074] = "StarPPetTradeCancelFocusPlayer_C2S_Msg",
	[4075] = "StarPPetTradeCancelFocusPlayer_S2C_Msg",
	[4076] = "StarPPetTradeGetFocusPlayer_C2S_Msg",
	[4077] = "StarPPetTradeGetFocusPlayer_S2C_Msg",
	[4078] = "FarmKirinLevelUp_C2S_Msg",
	[4079] = "FarmKirinLevelUp_S2C_Msg",
	[4080] = "FarmKirinIncubate_C2S_Msg",
	[4081] = "FarmKirinIncubate_S2C_Msg",
	[4082] = "FarmKirinCollect_C2S_Msg",
	[4083] = "FarmKirinCollect_S2C_Msg",
	[4084] = "FarmDelGiftRecord_C2S_Msg",
	[4085] = "FarmDelGiftRecord_S2C_Msg",
	[4086] = "FarmPartyPublish_C2S_Msg",
	[4087] = "FarmPartyPublish_S2C_Msg",
	[4089] = "UgcUploadLoading_C2S_Msg",
	[4090] = "UgcUploadLoading_S2C_Msg",
	[4091] = "UgcUploadLobbyCover_C2S_Msg",
	[4092] = "UgcUploadLobbyCover_S2C_Msg",
	[4093] = "UpdateProfileShowCollection_C2S_Msg",
	[4094] = "UpdateProfileShowCollection_S2C_Msg",
	[4095] = "AiNpcAddChatRecordNtf",
	[4096] = "AiNpcDelChatRecordNtf",
	[4097] = "AiNpcGetChatRecord_C2S_Msg",
	[4098] = "AiNpcGetChatRecord_S2C_Msg",
	[4101] = "StarPSocInteractionRatio_C2S_Msg",
	[4102] = "StarPSocInteractionRatio_S2C_Msg",
	[4103] = "StarPFriendIntimacyStateNtf",
	[4105] = "StarPCardSend_C2S_Msg",
	[4106] = "StarPCardSend_S2C_Msg",
	[4107] = "StarPCardHandle_C2S_Msg",
	[4108] = "ChatGetStarPModuleInfo_C2S_Msg",
	[4109] = "ChatGetStarPModuleInfo_S2C_Msg",
	[4110] = "ChatGetAllModuleInfo_C2S_Msg",
	[4111] = "ChatGetAllModuleInfo_S2C_Msg",
	[4112] = "StarPCardHandle_S2C_Msg",
	[4113] = "WolfKillTreasureShare_C2S_Msg",
	[4114] = "WolfKillTreasureShare_S2C_Msg",
	[4115] = "ArenaCardHotInfo_C2S_Msg",
	[4116] = "ArenaCardHotInfo_S2C_Msg",
	[4117] = "ArenaArenaCardHotInfoNtf",
	[4127] = "PlayerBatchPictureSetting_C2S_Msg",
	[4128] = "PlayerBatchPictureSetting_S2C_Msg",
	[4129] = "PlayerAlbumPicBatchAtTarget_C2S_Msg",
	[4130] = "PlayerAlbumPicBatchAtTarget_S2C_Msg",
	[4131] = "ChatModuleInfoChangeNtf",
	[4132] = "ChatModuleInfoNtf",
	[4135] = "CookVisitantCancelProtect_C2S_Msg",
	[4136] = "CookVisitantCancelProtect_S2C_Msg",
	[4137] = "ChaseDressUpItem_C2S_Msg",
	[4138] = "ChasDressUpItem_S2C_Msg",
	[4148] = "FarmPartyClose_C2S_Msg",
	[4149] = "FarmPartyClose_S2C_Msg",
	[4158] = "FarmGetPartyList_C2S_Msg",
	[4159] = "FarmGetPartyList_S2C_Msg",
	[4160] = "PlatCommonConfig_C2S_Msg",
	[4161] = "PlatCommonConfig_S2C_Msg",
	[4162] = "ChaseDressUpItem_S2C_Msg",
	[4171] = "StarPGroupJoinChatGroup_C2S_Msg",
	[4172] = "StarPGroupJoinChatGroup_S2C_Msg",
	[4173] = "StarPGroupGetPetEgg_C2S_Msg",
	[4174] = "StarPGroupGetPetEgg_S2C_Msg",
	[4177] = "ArenaSeasonStatInfoNtf",
	[4178] = "ArenaSeasonStatInfo_C2S_Msg",
	[4179] = "ArenaSeasonStatInfo_S2C_Msg",
	[4181] = "CookSetEffectiveCustomerFlow_C2S_Msg",
	[4182] = "CookSetEffectiveCustomerFlow_S2C_Msg",
	[4183] = "PlayerAlbumPicTargetAtNtf",
	[4190] = "PuzzleUncover_C2S_Msg",
	[4191] = "PuzzleUncover_S2C_Msg",
	[4192] = "PuzzleUncoverAll_C2S_Msg",
	[4193] = "PuzzleUncoverAll_S2C_Msg",
	[4196] = "SquadTaskExtraInfo_C2S_Msg",
	[4197] = "SquadTaskExtraInfo_S2C_Msg",
	[4200] = "ChaseBatchGetGameData_C2S_Msg",
	[4201] = "ChaseBatchGetGameData_S2C_Msg",
	[4202] = "FarmMagicUseRecoverItem_C2S_Msg",
	[4203] = "FarmMagicUseRecoverItem_S2C_Msg",
	[4204] = "StarPGetGuildCardDetailInfo_C2S_Msg",
	[4205] = "StarPGetGuildCardDetailInfo_S2C_Msg",
	[4206] = "StarPGMClearRole_C2S_Msg",
	[4207] = "StarPGMClearRole_S2C_Msg",
	[4210] = "FarmSetLiuYanMessagePermission_C2S_Msg",
	[4211] = "FarmSetLiuYanMessagePermission_S2C_Msg",
	[4212] = "GetTaskPassWordCode_C2S_Msg",
	[4213] = "GetTaskPassWordCode_S2C_Msg",
	[4214] = "UseTaskPassWordCode_C2S_Msg",
	[4215] = "UseTaskPassWordCode_S2C_Msg",
	[4216] = "PlayerAlbumPictureEdit_C2S_Msg",
	[4217] = "PlayerAlbumPictureEdit_S2C_Msg",
	[4218] = "PlayerAlbumPictureTextCheck_C2S_Msg",
	[4219] = "PlayerAlbumPictureText_S2C_Msg",
	[4221] = "CookCalculateDishIncome_C2S_Msg",
	[4222] = "CookCalculateDishIncome_S2C_Msg",
	[4223] = "CookCalculateCookOpenIncome_C2S_Msg",
	[4224] = "CookCalculateCookOpenIncome_S2C_Msg",
	[4225] = "PlayerAlbumPictureTextCheck_S2C_Msg",
	[4226] = "StarPJoinGuildSuccNtf",
	[4227] = "HouseRoomInfoNtf",
	[4228] = "UgcGetCreatorBadge_C2S_Msg",
	[4229] = "UgcGetCreatorBadge_S2C_Msg",
	[4230] = "UgcActiveCreatorBadgeNtf",
	[4231] = "UgcSetCreatorHomePage_C2S_Msg",
	[4232] = "UgcSetCreatorHomePage_S2C_Msg",
	[4233] = "UgcGetCreatorHomePage_C2S_Msg",
	[4234] = "UgcGetCreatorHomePage_S2C_Msg",
	[4238] = "UgcMatchLobbyDetailExMulti_C2S_Msg",
	[4239] = "UgcMatchLobbyDetailExMulti_S2C_Msg",
	[4240] = "UgcMatchLobbyDetailExChange_C2S_Msg",
	[4241] = "UgcMatchLobbyDetailExChange_S2C_Msg",
	[4242] = "UgcMatchLobbyDetailPlayRecord_C2S_Msg",
	[4243] = "UgcMatchLobbyDetailPlayRecord_S2C_Msg",
	[4244] = "CocGMCommand_C2S_Msg",
	[4245] = "CocGMCommand_S2C_Msg",
	[4252] = "PlayerAlbumPicInfo_C2S_Msg",
	[4253] = "PlayerAlbumPicInfo_S2C_Msg",
	[4255] = "FarmPetTame_C2S_Msg",
	[4256] = "FarmPetTame_S2C_Msg",
	[4259] = "CookSendCommentFood_C2S_Msg",
	[4260] = "CookSendCommentFood_S2C_Msg",
	[4263] = "GetSeasonReviewRedPointInfo_C2S_Msg",
	[4264] = "GetSeasonReviewRedPointInfo_S2C_Msg",
	[4265] = "UpdatedSeasonReviewRedPointInfo_C2S_Msg",
	[4266] = "UpdatedSeasonReviewRedPointInfo_S2C_Msg",
	[4278] = "CookFetchVisitantInfo_C2S_Msg",
	[4279] = "CookFetchVisitantInfo_S2C_Msg",
	[4284] = "FarmPetCatFishing_C2S_Msg",
	[4285] = "FarmPetCatFishing_S2C_Msg",
	[4288] = "PlayerTargetedLoadingTextPushCount_C2S_Msg",
	[4289] = "PlayerTargetedLoadingTextPushCount_S2C_Msg",
	[4292] = "CocBuildingRevamp_C2S_Msg",
	[4293] = "CocBuildingRevamp_S2C_Msg",
	[4294] = "ClientSetDSParam_C2S_Msg",
	[4295] = "ClientSetDSParam_S2C_Msg",
	[4296] = "CocBuildingAssignVillager_C2S_Msg",
	[4297] = "CocBuildingAssignVillager_S2C_Msg",
	[4298] = "CocBuildingUnassignVillager_C2S_Msg",
	[4299] = "CocBuildingUnassignVillager_S2C_Msg",
	[4300] = "FarmBatchReadSignature_C2S_Msg",
	[4301] = "FarmBatchReadSignature_S2C_Msg",
	[4302] = "ReturnActivityOfflineReward_C2S_Msg",
	[4303] = "ReturnActivityOfflineReward_S2C_Msg",
	[4305] = "ActivityGetMultiPlayerSquadIsFull_C2S_Msg",
	[4306] = "ActivityGetMultiPlayerSquadIsFull_S2C_Msg",
	[4307] = "OpenGiftPackageAnimationNtf",
	[4318] = "GameModeReturnInfo_C2S_Msg",
	[4319] = "GameModeReturnInfo_S2C_Msg",
	[4320] = "GameModeReturnStart_C2S_Msg",
	[4321] = "GameModeReturnStart_S2C_Msg",
	[4322] = "GameModeReturnStateChangeNtf",
	[4323] = "GameModeReturnFinishNtf",
	[4324] = "ChangeTaskOptionReward_C2S_Msg",
	[4325] = "ChangeTaskOptionReward_S2C_Msg",
	[4332] = "AchievementSortGetList_C2S_Msg",
	[4333] = "AchievementSortGetList_S2C_Msg",
	[4336] = "Nr3e8Common_C2S_Msg",
	[4337] = "Nr3e8Common_S2C_Msg",
	[4338] = "Nr3e8BoardEnter_C2S_Msg",
	[4339] = "Nr3e8BoardEnter_S2C_Msg",
	[4340] = "Nr3e8BoardExit_C2S_Msg",
	[4341] = "Nr3e8BoardExit_S2C_Msg",
	[4342] = "Nr3e8CommonNtf",
	[4343] = "Nr3e8TaskRewardRecv_C2S_Msg",
	[4344] = "Nr3e8TaskRewardRecv_S2C_Msg",
	[4345] = "Nr3e8TaskWeekActivityRewardRecv_C2S_Msg",
	[4346] = "Nr3e8TaskWeekActivityRewardRecv_S2C_Msg",
	[4347] = "Nr3e8TaskRead_C2S_Msg",
	[4348] = "Nr3e8TaskRead_S2C_Msg",
	[4349] = "WolfKillShieldVocation_C2S_Msg",
	[4350] = "WolfKillShieldVocation_S2C_Msg",
	[4351] = "GetWolfKillShieldVocation_C2S_Msg",
	[4352] = "GetWolfKillShieldVocation_S2C_Msg",
	[4353] = "GetArenaBlockInfo_C2S_Msg",
	[4354] = "GetArenaBlockInfo_S2C_Msg",
	[4355] = "CookEvictBadGuy_C2S_Msg",
	[4356] = "CookEvictBadGuy_S2C_Msg",
	[4361] = "GameModeReturnOpenedSlapFace_C2S_Msg",
	[4362] = "GameModeReturnOpenedSlapFace_S2C_Msg",
	[4363] = "FarmTagRedFox_C2S_Msg",
	[4364] = "FarmTagRedFox_S2C_Msg",
	[4365] = "FarmTaskFinishRemainTasks_C2S_Msg",
	[4366] = "FarmTaskFinishRemainTasks_S2C_Msg",
	[4391] = "SetAppearanceRoadShow_C2S_Msg",
	[4392] = "SetAppearanceRoadShow_S2C_Msg",
	[4393] = "SingleBattleStart_C2S_Msg",
	[4394] = "SingleBattleStart_S2C_Msg",
	[4395] = "SingleBattleSettlement_C2S_Msg",
	[4396] = "SingleBattleSettlement_S2C_Msg",
	[4397] = "UgcQuickJoinWithMidJoin_C2S_Msg",
	[4398] = "UgcQuickJoinWithMidJoin_S2C_Msg",
	[4399] = "UgcQuickJoinWithMidJoinResultNtf",
	[4406] = "GameModeReturnClearRedDot_C2S_Msg",
	[4407] = "GameModeReturnClearRedDot_S2C_Msg",
	[4408] = "FarmGMCommand_C2S_Msg",
	[4409] = "FarmGMCommand_S2C_Msg",
	[4412] = "CocForwardTest_C2S_Msg",
	[4413] = "CocForwardTest_S2C_Msg",
	[4414] = "GetCheckInManualFarmDailyBuys_C2S_Msg",
	[4415] = "GetCheckInManualFarmDailyBuys_S2C_Msg",
	[4417] = "CocForwardOneWayTest_C2S_Msg",
	[4418] = "CocForwardOneWayTest_S2C_Msg",
	[4419] = "CocDSInfoNtf",
	[4422] = "CocAttrNtf",
	[4426] = "QuerySecondaryNewbieRelatedMatchTypeBattleCnt_C2S_Msg",
	[4427] = "QuerySecondaryNewbieRelatedMatchTypeBattleCnt_S2C_Msg",
	[4428] = "CocHomeKickNtf",
	[4429] = "CocUpgradeHallOfVillager_C2S_Msg",
	[4430] = "CocUpgradeHallOfVillager_S2C_Msg",
	[4431] = "CocAccelerateUpgradingHallOfVillager_C2S_Msg",
	[4432] = "CocAccelerateUpgradingHallOfVillager_S2C_Msg",
	[4433] = "CocPayUnlockStudentPosition_C2S_Msg",
	[4434] = "CocPayUnlockStudentPosition_S2C_Msg",
	[4435] = "CocPutStudentIn_C2S_Msg",
	[4436] = "CocPutStudentIn_S2C_Msg",
	[4439] = "BagDressUpSetting_S2C_Msg",
	[4456] = "UgcHomePageGetLikePlayHotTag_C2S_Msg",
	[4457] = "UgcHomePageGetLikePlayHotTag_S2C_Msg",
	[4458] = "UgcHomePageSetLikePlayHotTag_C2S_Msg",
	[4459] = "UgcHomePageSetLikePlayHotTag_S2C_Msg",
	[4460] = "PlayerIntimateRelationRedPoint_C2S_Msg",
	[4461] = "PlayerIntimateRelationRedPoint_S2C_Msg",
	[4462] = "CocForwardCallerTest_C2S_Msg",
	[4463] = "CocForwardCallerTest_S2C_Msg",
	[4464] = "BagDressUpSetting_C2S_Msg",
	[4465] = "GiftRedUpdateDot_C2S_Msg",
	[4466] = "GiftRedUpdateDot_S2C_Msg",
	[4467] = "ChatBindModule_C2S_Msg",
	[4468] = "ChatBindModule_S2C_Msg",
	[4469] = "ChatUnbindModule_C2S_Msg",
	[4470] = "ChatUnbindModule_S2C_Msg",
	[4471] = "InflateRedPacketQuit_C2S_Msg",
	[4472] = "InflateRedPacketQuit_S2C_Msg",
	[4473] = "InflateRedPacketKickOut_C2S_Msg",
	[4474] = "InflateRedPacketKickOut_S2C_Msg",
	[4475] = "InflateRedPacketInflate_C2S_Msg",
	[4476] = "InflateRedPacketInflate_S2C_Msg",
	[4477] = "InflateRedPacketReceive_C2S_Msg",
	[4478] = "InflateRedPacketReceive_S2C_Msg",
	[4479] = "InflateRedPacketUpdateNtf",
	[4480] = "MallActivityInflateRedPacketPurchase_C2S_Msg",
	[4481] = "MallActivityInflateRedPacketPurchase_S2C_Msg",
	[4484] = "PlayerHallStableState_C2S_Msg",
	[4485] = "PlayerHallStableState_S2C_Msg",
	[4486] = "GetWolfKillMonthCardInfo_C2S_Msg",
	[4487] = "GetWolfKillMonthCardInfo_S2C_Msg",
	[4488] = "OpenWolfKillMonth_C2S_Msg",
	[4489] = "OpenWolfKillMonth_S2C_Msg",
	[4490] = "GetWolfKillMonthFreeGift_C2S_Msg",
	[4491] = "GetWolfKillMonthFreeGift_S2C_Msg",
	[4508] = "WolfKillMonthCardNtf",
	[4509] = "MatchRecommendList_C2S_Msg",
	[4510] = "MatchRecommendList_S2C_Msg",
	[4511] = "SimulateLimitNtf",
	[4512] = "StarPChooseInitEquipSuit_C2S_Msg",
	[4513] = "StarPChooseInitEquipSuit_S2C_Msg",
	[4514] = "StarPGetUnlockData_C2S_Msg",
	[4515] = "StarPGetUnlockData_S2C_Msg",
	[4516] = "StarPGetChatGroupKey_C2S_Msg",
	[4517] = "StarPGetChatGroupKey_S2C_Msg",
	[4518] = "StarPPetTradeFocusPlayer_S2C_Msg",
	[4519] = "StarPTipsNtf",
	[4520] = "StarPJoinGroup_C2S_Msg",
	[4521] = "StarPJoinGroup_S2C_Msg",
	[4522] = "StarPLeaveGroup_C2S_Msg",
	[4523] = "StarPLeaveGroup_S2C_Msg",
	[4524] = "StarPDismissGroup_C2S_Msg",
	[4525] = "StarPDismissGroup_S2C_Msg",
	[4526] = "StarPPvpStartFailNtf",
	[4541] = "UpdatePlayerLimitExperienceRedPoint_C2S_Msg",
	[4542] = "UpdatePlayerLimitExperienceRedPoint_S2C_Msg",
	[4557] = "FarmGetNewItemNtf",
	[4558] = "GetBiHudRecommendMapId_C2S_Msg",
	[4559] = "GetBiHudRecommendMapId_S2C_Msg",
	[4560] = "GetFarmDailyReward_C2S_Msg",
	[4561] = "GetFarmDailyReward_S2C_Msg",
	[4562] = "UpgradeFarmDailyReward_C2S_Msg",
	[4563] = "UpgradeFarmDailyReward_S2C_Msg",
	[4566] = "TeamRecruitPublishWithModeType_C2S_Msg",
	[4567] = "TeamRecruitPublishWithModeType_S2C_Msg",
	[4568] = "TeamRecruitQueryByModeType_C2S_Msg",
	[4569] = "TeamRecruitQueryByModeType_S2C_Msg",
	[4575] = "FarmGetCloudTax_C2S_Msg",
	[4576] = "FarmGetCloudTax_S2C_Msg",
	[4577] = "UgcRoomGetPlayerRecommendMaps_C2S_Msg",
	[4578] = "UgcRoomGetPlayerRecommendMaps_S2C_Msg",
	[4579] = "UgcRoomGetOfficalRecommendMaps_C2S_Msg",
	[4580] = "UgcRoomGetOfficalRecommendMaps_S2C_Msg",
	[4582] = "FarmStealingMyRecord_C2S_Msg",
	[4583] = "FarmStealingMyRecord_S2C_Msg",
	[4611] = "UpgradeFarmDailyAwardNtf",
	[4616] = "FarmSetBeFertilizedType_C2S_Msg",
	[4617] = "FarmSetBeFertilizedType_S2C_Msg",
	[4618] = "UpgradeInFarmDailyActivityPage_C2S_Msg",
	[4619] = "UpgradeInFarmDailyActivityPage_S2C_Msg",
	[4620] = "UgcCoCreateMultiEditApply_C2S_Msg",
	[4621] = "UgcCoCreateMultiEditApply_S2C_Msg",
	[4622] = "UgcCoCreateMultiEditApplyNtf",
	[4623] = "UgcCoCreateMultiEditReply_C2S_Msg",
	[4624] = "UgcCoCreateMultiEditReply_S2C_Msg",
	[4625] = "UgcCoCreateMultiEditReplyNtf",
	[4626] = "UgcCoCreateMultiEditRejectEnter_C2S_Msg",
	[4627] = "UgcCoCreateMultiEditRejectEnter_S2C_Msg",
	[4628] = "UgcCoCreateMultiEditRejectEnterNtf",
	[4629] = "GamePakDetailInfoReport_C2S_Msg",
	[4630] = "GamePakDetailInfoReport_S2C_Msg",
	[4633] = "FarmOpenGodUI_C2S_Msg",
	[4634] = "FarmOpenGodUI_S2C_Msg",
	[4635] = "UgcCoCreateMultiEditCodingData_C2S_Msg",
	[4636] = "UgcCoCreateMultiEditCodingData_S2C_Msg",
	[4637] = "UgcCoCreateMultiEditDataUpdateNtf",
	[4642] = "ChatClearAllNotRead_C2S_Msg",
	[4643] = "ChatClearAllNotRead_S2C_Msg",
	[4647] = "WolfKillLevel_C2S_Msg",
	[4648] = "WolfKillLevel_S2C_Msg",
	[4649] = "PlayerDanceScore_C2S_Msg",
	[4650] = "PlayerDanceScore_S2C_Msg",
	[4653] = "UgcAiChangeActionState_C2S_Msg",
	[4654] = "UgcAiChangeActionState_S2C_Msg",
	[4655] = "RoomCancelPreStart_C2S_Msg",
	[4656] = "RoomCancelPreStart_S2C_Msg",
	[4657] = "AiNpcChatPushNoticeNtf",
	[4658] = "BattleSceneSwitchNtf",
	[4659] = "ExitBattleNtf",
	[4660] = "FlashRaceCheeringAi_C2S_Msg",
	[4661] = "FlashRaceCheeringAi_S2C_Msg",
	[4662] = "FlashRaceCheeringVoteOption_C2S_Msg",
	[4663] = "FlashRaceCheeringVoteOption_S2C_Msg",
	[4665] = "UgcSubscribeRecommendPage_C2S_Msg",
	[4666] = "UgcSubscribeRecommendPage_S2C_Msg",
	[4667] = "FarmBadGuyCloudTaxDetail_C2S_Msg",
	[4668] = "FarmBadGuyCloudTaxDetail_S2C_Msg",
	[4687] = "ActivityLevelUpChallengeCrit_C2S_Msg",
	[4688] = "ActivityLevelUpChallengeCrit_S2C_Msg",
	[4711] = "CompetitionEntranceStatusChangeNtf",
	[4712] = "HouseBuySampleRoom_C2S_Msg",
	[4713] = "HouseBuySampleRoom_S2C_Msg",
	[4714] = "GetWolfKillMMRScore_C2S_Msg",
	[4715] = "GetWolfKillMMRScore_S2C_Msg",
	[4716] = "ABTestInfoBatchQuery_C2S_Msg",
	[4717] = "ABTestInfoBatchQuery_S2C_Msg",
	[4718] = "FashionSkillUse_C2S_Msg",
	[4719] = "FashionSkillUse_S2C_Msg",
	[4735] = "HOKShowAiInvite_C2S_Msg",
	[4736] = "HOKShowAiInvite_S2C_Msg",
	[4737] = "UgcRoomPlayerRecommendMap_C2S_Msg",
	[4738] = "UgcRoomPlayerRecommendMap_S2C_Msg",
	[4739] = "UgcRoomPlayerRecommendMapNtf",
	[4740] = "UgcGetStarWorldEntranceBubble_C2S_Msg",
	[4741] = "UgcGetStarWorldEntranceBubble_S2C_Msg",
	[4742] = "UgcRemoveStarWorldEntranceBubble_C2S_Msg",
	[4743] = "UgcRemoveStarWorldEntranceBubble_S2C_Msg",
	[4746] = "MobaFootballGoalBroadcastNtf",
	[4751] = "CupsCycleOpenNtf",
	[4752] = "CupsCycleUnLockNtf",
	[4753] = "ChangeCupsCycle_C2S_Msg",
	[4754] = "ChangeCupsCycle_S2C_Msg",
	[4755] = "UpdatePlayerGearSetting_C2S_Msg",
	[4756] = "UpdatePlayerGearSetting_S2C_Msg",
	[4759] = "FarmCanStealNtf",
	[4760] = "UgcAiImageCheck_C2S_Msg",
	[4761] = "UgcAiImageCheck_S2C_Msg",
	[4762] = "ItemChangeNtf",
	[4763] = "HOKShowAiInviteNtf",
	[4766] = "CupsCycleBeginOpenNtf",
	[4767] = "CupsCycleClickRedDot_C2S_Msg",
	[4768] = "CupsCycleClickRedDot_S2C_Msg",
	[4773] = "AnimeDressOutlineSet_C2S_Msg",
	[4774] = "AnimeDressOutlineSet_S2C_Msg",
	[4775] = "ArenaCardSuitInfo_C2S_Msg",
	[4776] = "ArenaCardSuitInfo_S2C_Msg",
	[4777] = "ArenaCardSuitInfoNtf",
	[4779] = "VehicleAccessoriesItem_C2S_Msg",
	[4780] = "VehicleAccessoriesItem_S2C_Msg",
	[4783] = "RoomRecommendListWithModeType_C2S_Msg",
	[4784] = "RoomRecommendListWithModeType_S2C_Msg",
	[4785] = "ClientLogEndNtf",
	[4786] = "ConfigSvrGM_C2S_Msg",
	[4787] = "ConfigSvrGM_S2C_Msg",
	[4794] = "GameModeReturnDrawProgressReward_C2S_Msg",
	[4795] = "GameModeReturnDrawProgressReward_S2C_Msg",
	[4796] = "GameModeRewardDrawInnReward_C2S_Msg",
	[4797] = "GameModeRewardDrawInnReward_S2C_Msg",
	[4808] = "MailPurchased_C2S_Msg",
	[4809] = "MailPurchased_S2C_Msg",
	[4810] = "MailBuyInfo_C2S_Msg",
	[4811] = "MailBuyInfo_S2C_Msg",
	[4812] = "GameModeReturnBISort_C2S_Msg",
	[4813] = "GameModeReturnBISort_S2C_Msg",
	[4815] = "GetWeekendGiftReward_C2S_Msg",
	[4816] = "GetWeekendGiftReward_S2C_Msg",
	[4817] = "UpgradeWeekendGift_C2S_Msg",
	[4818] = "UpgradeWeekendGift_S2C_Msg",
	[4819] = "RoomModifyMatchingTeamInfo_C2S_Msg",
	[4820] = "RoomModifyMatchingTeamInfo_S2C_Msg",
	[4821] = "RoomModifyMatchingTeamInfoResultNtf",
	[4822] = "UgcMatchLobbySummery_C2S_Msg",
	[4823] = "UgcMatchLobbySummery_S2C_Msg",
	[4824] = "GetWeekTopPlayGroupId_C2S_Msg",
	[4825] = "GetWeekTopPlayGroupId_S2C_Msg",
	[4826] = "GetDayPlayedGroupId_C2S_Msg",
	[4827] = "GetDayPlayedGroupId_S2C_Msg",
	[4828] = "ChaseSetSettings_C2S_Msg",
	[4829] = "ChaseSetSettings_S2C_Msg",
	[4830] = "UgcMatchLobbySummerySpecified_C2S_Msg",
	[4831] = "UgcMatchLobbySummerySpecified_S2C_Msg",
	[4834] = "DisplayBoardClear_C2S_Msg",
	[4835] = "DisplayBoardClear_S2C_Msg",
	[4836] = "DisplayBoardHistorySetting_C2S_Msg",
	[4837] = "DisplayBoardHistorySetting_S2C_Msg",
	[4838] = "DisplayBoardUpdateNtf",
	[4839] = "DisplayBoardNewRedDotRemove_C2S_Msg",
	[4840] = "DisplayBoardNewRedDotRemove_S2C_Msg",
	[4845] = "AddForceOpenMatchType_C2S_Msg",
	[4846] = "AddForceOpenMatchType_S2C_Msg",
	[4847] = "SelfFarmCsForwardTest_C2S_Msg",
	[4848] = "SelfFarmCsForwardTest_S2C_Msg",
	[4849] = "CurrFarmCsForwardTest_C2S_Msg",
	[4850] = "CurrFarmCsForwardTest_S2C_Msg",
	[4851] = "ActivityTreasureHuntExcavate_C2S_Msg",
	[4852] = "ActivityTreasureHuntExcavate_S2C_Msg",
	[4853] = "ActivityTreasureHuntSell_C2S_Msg",
	[4854] = "ActivityTreasureHuntSell_S2C_Msg",
	[4855] = "ActivityTreasureHuntAttrUpdateNtf",
	[4856] = "GetMasterPatchInfo_C2S_Msg",
	[4857] = "GetMasterPatchInfo_S2C_Msg",
	[4858] = "ReceiveMasterPatchReward_C2S_Msg",
	[4859] = "MasterPatchUnLockNtf",
	[4860] = "AddPicToPicWall_C2S_Msg",
	[4861] = "AddPicToPicWall_S2C_Msg",
	[4862] = "DelPicFromPicWall_C2S_Msg",
	[4863] = "DelPicFromPicWall_S2C_Msg",
	[4864] = "PlayerPicWallLike_C2S_Msg",
	[4865] = "PlayerPicWallLike_S2C_Msg",
	[4866] = "GetPicWallTopLikeList_C2S_Msg",
	[4867] = "GetPicWallTopLikeList_S2C_Msg",
	[4868] = "GetPicWallLatestList_C2S_Msg",
	[4869] = "GetPicWallLatestList_S2C_Msg",
	[4871] = "ReceiveMasterPatchReward_S2C_Msg",
	[4872] = "DisplayBoardSave_C2S_Msg",
	[4873] = "DisplayBoardSave_S2C_Msg",
	[4874] = "DisplayBoardOpen_C2S_Msg",
	[4875] = "DisplayBoardOpen_S2C_Msg",
	[4878] = "ItaBagBadgeEquip_C2S_Msg",
	[4879] = "ItaBagBadgeEquip_S2C_Msg",
	[4882] = "DressUpDetailInfoChange_C2S_Msg",
	[4883] = "DressUpDetailInfoChange_S2C_Msg",
	[4884] = "UpdateItemInfoDbItemShowStatus_C2S_Msg",
	[4885] = "UpdateItemInfoDbItemShowStatus_S2C_Msg",
	[4886] = "DressItemShowStatusChange_C2S_Msg",
	[4887] = "DressItemShowStatusChange_S2C_Msg",
	[4910] = "GetPicWallMyPicList_C2S_Msg",
	[4911] = "GetPicWallMyPicList_S2C_Msg",
	[4914] = "NewTradingCardGetNtf",
	[4915] = "ChaseGetIdentityProficiencyInfo_C2S_Msg",
	[4916] = "ChaseGetIdentityProficiencyInfo_S2C_Msg",
	[4917] = "ChaseIdentityBattlePerformance_C2S_Msg",
	[4918] = "ChaseIdentityBattlePerformance_S2C_Msg",
	[4919] = "ChaseIdentityDrawProficiencyProgressReward_C2S_Msg",
	[4920] = "ChaseIdentityDrawProficiencyProgressReward_S2C_Msg",
	[4921] = "MasterPatchChangeNtf",
	[4935] = "ChaseIdentityReadBiography_C2S_Msg",
	[4936] = "ChaseIdentityReadBiography_S2C_Msg",
	[4947] = "WeekendGiftBuySuccessNtf",
	[4948] = "WeekendGiftCheckInNtf",
}

_MOE.MsgName2Id = MsgName2Id
_MOE.MsgId2Name = MsgId2Name

return {MsgName2Id = MsgName2Id, MsgId2Name = MsgId2Name}