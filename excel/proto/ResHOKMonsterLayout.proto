syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";

message ResHOKMonsterLayoutItem {
  optional int32 monsterId = 1;      // 野怪Id
  optional int32 birthIndex = 2;    // 出生点索引
  optional int32 pathIdx = 3;    // 寻路
}

message ResHOKMonsterLayout { //@noSvr
    option (resKey) = "id";
    optional int32 id = 1;  // id
    optional int32 type = 2; // 类型(0:普通，1:主宰, 2:暴君..)
    optional string name = 3; // 名字
    optional int32 groupId = 4;  // 场景配置id
    optional int32 sideId = 5; // 阵营id
    repeated ResHOKMonsterLayoutItem monsterList = 6; // 怪物配置
    optional int32 firstBirthTime = 7;  // 第一次刷新时间(秒)
    optional int32 rebirthTime = 8;  // 死亡重生时间
    optional int32 rebirthCount = 9;  // 死亡重生次数（0,无限刷）
    optional int32 showLeftTime = 10;  // 显示刷新倒计时
    optional int32 layoutSideId = 11; // 出生地阵营id
    optional int32 disappearTimestamp = 12; // 消失时间戳
    
    // 大小龙强化版本配置
    optional int32 advanceFirstBirthTime = 21;  // 第一次刷新时间【强化】
    optional int32 advanceRebirthTime = 22;  // 死亡重生时间【强化】
    optional int32 advanceRebirthCount = 23;  // 死亡重生次数（0,无限刷）【强化】
    optional int32 advanceShowLeftTime = 24;  // 显示刷新倒计时【强化】
    repeated ResHOKMonsterLayoutItem advanceMonsterList = 25; // 怪物配置【强化】
    optional int32 advancePrepareTime = 26; // 普通=》进阶的准备时间

    optional int32 animBirthDuration = 27;  // 怪物出生动画时长
    optional int32 billboardShowLeftTime = 28;   // 3dUI剩余显示时间
}

message table_ResHOKMonsterLayout {// @noSvr
  repeated ResHOKMonsterLayout rows = 1;
}

message ResChangeBuffTimeInfoItem {
  optional int32 changeBuffTime = 1; // 开始替换buff时间
  repeated int32 addBuffs = 2;       // 增加的BUFFID列表
  repeated int32 removeBuffs = 3;    // 删除的BUFFID列表
}
 
message ResHOKMonsterExtraBuffLayout { // @noSvr
    option (resKey) = "id";
    optional int32 id = 1;  // id
    repeated ResChangeBuffTimeInfoItem changeInfo = 2; // 替换buff信息
}

message table_ResHOKMonsterExtraBuffLayout { // @noSvr
  repeated ResHOKMonsterExtraBuffLayout rows = 1;
}