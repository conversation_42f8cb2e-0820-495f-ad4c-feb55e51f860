syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";

message RechargeFarmTab {
    option (resKey) = "id";
    optional int32 id = 1; //ID
    optional int32 tabGroup = 2; //标签组
    optional int32 tabSort = 3; //标签排序
    optional string tabName = 4; //标签名称
    optional string umgName = 5; //umg名称
    optional string openFun = 6; //开启方法
    optional int32 srcPakGroupId = 7; //资源包组ID
    optional int32 activityId = 8; //关联活动ID
    optional string showEndTime = 9;//页面结束时间
    optional string showBeginTime = 10;//页面开始时间
    optional bool showSendBtn = 11; //是否显示赠送
    repeated int32 currencyCfg = 12; //货币类型
    optional string minVersion = 13; //版本号需求
}
message table_RechargeFarmTab {//@noSvr
    repeated RechargeFarmTab rows = 1;
}

message RechargeFarmSubTab {
    option (resKey) = "id";
    optional int32 id = 1; //ID
    optional int32 tabGroup = 2; //标签组
    optional int32 tabSort = 3; //标签排序
    optional string tabName = 4; //标签名称
    optional string umgName = 5; //umg名称
    optional string openFun = 6; //开启方法
    optional int32 shopTagId = 7; //商城标签页Id
}

message table_RechargeFarmSubTab {//@noSvr
    repeated RechargeFarmSubTab rows = 1;
}

message RechargeFarmMallFarmLogin {
    option (resKey) = "id";
    optional int32 id = 1; //ID
    optional int32 activityId = 2; //关联活动ID
    optional int32 isShowWeekChest = 3; //是否显示升星,周宝箱
    repeated int32 bigRewardItemIds = 4; //大奖的id
    repeated int32 bigRewardItemNums = 5; //大奖的数量
}

message table_RechargeFarmMallFarmLogin {//@noSvr
    repeated RechargeFarmMallFarmLogin rows = 1;
}

message DailyGift {
    option (resKey) = "id";
    optional int32 id = 1; //ID
    optional int32 activityId = 2; //关联活动ID
    optional int32 isShowWeekChest = 3; //是否显示升星,周宝箱
    repeated int32 bigRewardItemIds = 4; //大奖的id
    repeated int32 bigRewardItemNums = 5; //大奖的数量
    optional int32 dailyGiftType = 6; //天天领类型
    optional string umgName = 7; //umg名称
}

message table_DailyGift {//@noSvr
    repeated DailyGift rows = 1;
}