syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";


message T_UgcUploadConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional CosOperateScene scene = 2;
  optional FuncType funcType = 3;
  optional string filePath = 4;
  optional int32 sizeLimit = 5;
  optional CosOperate operate = 6;
  optional int32 duration = 7;
  optional int32 cacheDuration = 8;
  optional int32 reason = 9;
  optional int32 isClientUse = 10;
}

message table_UgcUploadConfig {// @noCli
  repeated T_UgcUploadConfig rows = 1;
}


message T_UgcRegionConfig {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 svrType = 2;
  optional int32 worldId = 3;
  optional int32 regionId = 4;
}

message table_UgcRegionConfig {// @noCli @noSvr
  repeated T_UgcRegionConfig rows = 1;
}

message T_UgcRouteInsConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 worldId = 2;
  optional int32 insId = 3;// @noSvr
  optional int32 regionId = 4;
  optional int32 groupId = 5;
  optional string ip = 6;
  optional int32 port = 7;
  optional int32 mergeToWorldID = 8;
}

message table_UgcRouteInsConfig {// @noCli
  repeated T_UgcRouteInsConfig rows = 1;
}

message T_UgcRoutePolarisInsConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 worldId = 2;
  optional int32 regionId = 3;
  optional int32 groupId = 4;
  optional string serviceName = 5;
  optional string namespace = 6;
  optional int32 mergeToWorldID = 7;
}

message table_UgcRoutePolarisInsConfig {// @noCli
  repeated T_UgcRoutePolarisInsConfig rows = 1;
}

message table_UgcCommonConfig {
  repeated T_UgcCommonConfig rows = 1;
}

message T_UgcCommonConfig {
  option (resKey) = "id";
  optional string id = 1;
  optional string host = 2;
  optional string region = 3;
  optional string env = 4;
  optional int32 index = 5;
}

message table_UgcBucketConfig {// @noCli
  repeated T_UgcBucketConfig rows = 1;
}

message T_UgcBucketConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string bucket = 2;
  optional string region = 3;
  optional string env = 4;
}


message table_UgcWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message T_UgcWhiteListConfig {
  option (resKey) = "id";
  optional string id = 1;
  optional int32 permission = 2;
}

message table_UgcAiImageWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcCoCreateWhiteListConfig {
  repeated T_UgcCoCreateWhiteListConfig rows = 1;
}

message T_UgcCoCreateWhiteListConfig {
  option (resKey) = "id";
  optional string id = 1;
  optional int32 permission = 2;
}

message table_UgcCommandUrlConfig {// @noCli
  repeated T_UgcCommandUrlConfig rows = 1;
}

message T_UgcCommandUrlConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional UgcCommandUrlReqType method = 2;
  optional UgcCommandUrlType type = 3;
  optional string url = 4;
  optional string formal_url = 5;
}

message table_UgcAiAnicapWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcAiVoiceWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcAiAnswerWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcDialogueImageWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcSkillEditorWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcCustomSkeletonAnimWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcCosPathConfig {// @noCli
  repeated T_UgcCosPathConfig rows = 1;
}

message T_UgcCosPathConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 mapType = 2;
  optional int32 mdType = 3;
  optional string cosPath = 4;
  optional FuncType funcType = 5;
  optional UgcCosPathType pathType = 6;
}

message  table_UgcUpdateMapWhiteListConfig { // @noCli
  repeated T_UgcUpdateMapWhiteListConfig rows = 1;
}

message T_UgcUpdateMapWhiteListConfig {
  option (resKey) = "id";
  optional string id = 1;
  optional int32 permission = 2;
}

message table_UgcVisualProgramWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message table_UgcCreateChatGroupWhiteListConfig {
  repeated T_UgcWhiteListConfig rows = 1;
}

message T_UgcNavBaoDiConfig {
  option (resKey) = "id";
  optional string id = 1;
  optional string parent = 2;
  optional string name = 3;
}

message table_UgcNavBaoDiConfig {
  repeated T_UgcNavBaoDiConfig rows = 1;
}

message UgcBuyGoodsCreatorListConfig {
  option (resKey) = "creatorId";
  optional int64 creatorId = 1;
  optional string offerId = 2;
  optional string sandBoxSecretKey = 3;
  optional string secretKey = 4;
}

message table_UgcBuyGoodsCreatorListConfig {
  repeated UgcBuyGoodsCreatorListConfig rows = 1;
}

message T_UgcCosMapping {
  option (resKey) = "id";
  optional int64 id = 1;
  optional int32 reason = 2;
  optional int32 instance = 3;
  optional int32 resType = 4;
  optional int32 aimReason = 5;
}

message table_UgcCosMapping {
  repeated T_UgcCosMapping rows = 1;
}