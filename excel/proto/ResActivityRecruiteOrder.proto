syntax = "proto2";
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";

message ActRecruiteRule {
    optional int32 callLevel = 1;       // 招募者等级要求
    optional int32 echoOfflineHour = 3;  // 老玩家认证离线小时
    optional int32 confirmValidDay = 4;  // 绑定有效天数
}

message ActRecruitLadderSetting {
    optional int32 ladder = 1; // 阶梯等级
    optional int64 value = 2; // 阶梯数值设定
}

message ActRecruiteReward {
    optional int64 newbeeSignCoin = 1;      // 每次招新的额外奖励
    optional int64 playLevelCoin = 2;       // 每次同玩的额外奖励
    optional int64 weeklyLimitCoin = 3;     // 周奖励上限
    optional int32 coinItemId = 4;          // 代币道具id
    optional int64 exchangeRewardCount = 5; // 兑换大奖的货币数量
    optional int64 rewardId = 6;            // 大奖的道具id
    optional int64 weeklyLimitCoinForPlay = 7; // 登陆奖励
    optional int64 weeklyLimitCoinForLogin = 8; // 登陆奖励
    repeated ActRecruitLadderSetting login = 9; // 登陆奖励
}

message ActRecruiteJumpParam {
    optional int64 jumpItemId = 1;          // 跳转道具id
    optional string jumpDes = 2;            // 跳转描述
    optional int32 jumpId = 3;              // 跳转id
}

message ActRecruiteConf {
    option (resKey) = "actId";
    optional int32 actId = 1;                   // 活动Id
    optional ActRecruiteRule rule = 2;          // 召回规则
    optional ActRecruiteReward reward = 3;      // 召回奖励
    optional int32 mallId = 4;                  // 兑换商店Id @noSvr
    optional string rewardPic = 5;              // 奖励图cdn @noSvr
    optional ActRecruiteJumpParam jumpParam = 6;// 跳转参数 @noSvr
    optional string arkRecallScene = 7;         // QQArk好友召回配置 @noSvr
    optional string arkInviteScene = 8;         // QQArk新星招募配置 @noSvr
    optional string copyAndShareClipStr = 9;    // 复制并分享时粘贴板的内容 @noSvr
    optional string CDNTitle = 10;              // 活动CDN标题
}

// excel rule: convert(ResActivityRecruiteOrder.proto, table_ActRecruiteConf, ActRecruiteConf.pbin)
message table_ActRecruiteConf {
  repeated ActRecruiteConf rows = 1;
}
