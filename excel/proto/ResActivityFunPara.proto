syntax = "proto2";
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "google/protobuf/timestamp.proto";

// 活动功能参数配置表
message ActivityFunParaConfig {
  option (resKey) = "id";
  optional int32 id = 1;      // id
  optional int32 intVal = 2;    // int类型参数
  optional string stringVal = 3;  // string类型参数
  optional google.protobuf.Timestamp timeVal = 4; // 时间类型参数
}

message table_ActivityFunParaConfig {
  repeated ActivityFunParaConfig rows = 1;
}

enum ActivityFunParaType {
  // 心愿活动 10000 - 10100
  AFPT_MAX_CHOICE_PRIZE_COUNT = 10000;        // 心愿活动:最大选择奖品数量
  AFPT_MAX_LOTTERY_TICKET_COUNT = 10001;      // 心愿活动:最大获得抽奖券数量
  AFPT_WISH_VALUE_REQUIRED_FOR_PRIZE = 10002; // 心愿活动:领奖需要心愿值
  AFPT_RPC_HELP_ADD_VALUE = 10003;            // 心愿活动:端内助力增加心愿值
  AFPT_MAX_BE_HELP_CNT = 10004;               // 心愿活动:每日最大被助力次数
  AFPT_MAX_HELP_CNT = 10005;                  // 心愿活动:每日可助力别人最大次数

  // 国庆换装活动 10100 - 10200
  AFPT_DANCE_CHANGE_CD = 10101;           // 换装CD,单位s
  AFPT_DANCE_DURATION = 10102;            // 换装持续CD,单位s
  AFPT_DANCE_HISTORY_MAX = 10103;          // 换装历史记录最大上限
  AFPT_DANCE_GEAR_REPEAT_COUNT = 10104;    // 换装生成不与前X次重复
  AFPT_PRAYCARD_GIVE_INTIMACY = 10105;        // 祈福牌赠送亲密度
  AFPT_PRAYCARD_GIVE_MAIL_ID = 10106;     // 祈福牌活动:赠送邮件id
  AFPT_PRAYCARD_GIVE_INTIMACY_TIMES = 10109;     // 祈福牌赠送亲密度次数上限


  AFPT_CAPTURESHADOW_INIT = 10201;          // 柯南收口-黑衣人刷新距离玩家X格
  AFPT_CAPTURESHADOW_MAP_COUNT = 10202;          // 柯南收口-地图格子数
  AFPT_CAPTURESHADOW_CAP_REWARD = 10203;          // 柯南收口-抓捕奖励倍数

  AFPT_MIANGAN_ITEM_ID = 10301;             //免肝券道具id--已经废弃不用 勿动,以免影响线上数据
  AFPT_MIANGAN_BEGIN_TIME = 10302;             //免肝券道具生效起止时间--已经废弃不用 勿动,以免影响线上数据
  AFPT_MIANGAN_END_TIME = 10303;             //免肝券道具生效结束时间--已经废弃不用 勿动,以免影响线上数据

  // 照片墙活动 11000 - 11010
  AFPT_PIC_WALL_LIKE_NUM_RANK_DISPLAY_NUM = 11000;    // 照片墙最高点赞显示数量 int类型
  AFPT_PIC_WALL_ADD_TIME_RANK_DISPLAY_NUM = 11001;    // 照片墙最新上传显示数量 int类型
  AFPT_PIC_WALL_MY_PIC_MAX_NUM = 11002;               // 照片墙我的照片数量上限 int类型
  AFPT_PIC_WALL_DAILY_LIKE_LIMIT = 11003;             // 照片墙玩家每日点赞数量上限 int类型
  AFPT_PIC_WALL_LIKE_HISTORY_LIMIT = 11004;           // 照片墙玩家点赞历史数量上限 int类型
  AFPT_PIC_WALL_BEGIN_TIME = 11005;                   // 照片墙活动开始时间 时间类型
  AFPT_PIC_WALL_END_TIME = 11006;                     // 照片墙活动结束时间 时间类型

  // 其他活动 xxxx - xxxx
}

// 活动免肝券配置
message ActivityMianGanConfig {
  option (resKey) = "id";
  optional int32 id = 1;      // id
  optional int32 itemId = 2;    // 免肝道具id
  optional google.protobuf.Timestamp beginTime = 4; // 免肝道具开始时间
  optional google.protobuf.Timestamp endTime = 5; // 免肝道具结束时间
}

message table_ActivityMianGanConfig {
  repeated ActivityMianGanConfig rows = 1;
}


