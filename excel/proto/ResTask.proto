syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResCommon.proto";
import "ResKeywords.proto";
import "ResCondition.proto";
import "google/protobuf/timestamp.proto";

// 任务条件配置
message TaskConditionConf {
  optional ResConditionGroup resTriggerConditionGroup = 1; //@noCli 触发条件组
  optional ResConditionGroup resCompleteConditionGroup = 2; // 完成条件组
  optional ResConditionGroup resLoadConditionGroup = 3; //@noCli 加载条件组
  optional ResConditionGroup resShowConditionGroup = 4; //@noCli 显示条件组
}

// 任务奖励配置
message TaskRewardConf {
  repeated int32 itemIdList = 1; // 道具id
  repeated int32 numList = 2; // 道具数量
  repeated int32 validPeriodList = 3; // 有效期(天)
  optional bool auto = 4; //@noCli 是否自动发奖
  repeated google.protobuf.Timestamp expireTimestamps = 5; // 有效期(天)
  optional int32 addRatio = 6; // 倍率增加的百分比
  optional google.protobuf.Timestamp addRatioBeginTime = 7; // 额外倍率开始时间
  optional google.protobuf.Timestamp addRatioEndTime = 8; // 额外倍率结束时间
  optional string urgentRatioContent = 9; //@noSvr 限时倍率Tag文本
  optional RewardShowType rewardShowType = 10; //@noCli 获得奖励展示类型
  optional bool autoNotShowCli = 11;//@noCli 自动发奖不展示
  repeated int32 optionalReward = 12; // 任务可选奖励
  repeated int32 cupsTaskCycleReward = 13; // 奖杯征程任务多周目任务奖励.根据周目选择不同奖励
}

// 任务时间限制
message TaskTimeConf {
  optional int64 beginTime = 1;
  optional int64 endTime = 2;
}

// 任务加载时间限制
message TaskLoadTimeConf {
  repeated int32 weekdays = 1;  // 周几加载
}

// 任务额外配置
message TaskExtraConf {
  optional int32 belongDay = 1; // 所属天数，新手任务
  optional string tag = 2; // 标签
  optional int32 achievementLevel = 3; //成就等级
  optional string taskIcon = 4; // 日周常任务icon
  optional int32 belongStage = 5; //织梦之旅：所属阶段
  optional int32 level = 6; // 玩家等级
  repeated int32 accountType = 7; // 平台
  repeated string regChannel = 8; // 渠道
  optional string whilelist = 9; // 白名单名称,openid
  optional int32 rewardItemId = 10; // 段位大奖道具id
  optional int32 ruleId = 11; // 任务提示消息框配置id
  optional int32 completeNoticeId = 12; // 完成任务跑马灯
  optional int32 rewardNoticeId = 13; // 任务领奖跑马灯
  optional string jumpParams = 14; // 任务跳转配置参数
  optional int32 disbaleChannelScene = 15; // 屏蔽渠道入口场景Id
  repeated string picList = 16; // 图片列表
  optional string textContent = 17; // 文本内容
  optional string leftTopContent = 18; // 面板左上角文本
  optional string rightTopContent = 19; // 面板右上角文本
  optional string panelImg = 20; // 面板底板图片
  optional string urgentContent = 21; // 限时任务Tag文本
  optional string extraNotesContent = 22; // 任务额外说明
  optional string lobbyName = 23;// 大厅用名称
  optional string lobbyDesc = 24;// 大厅用描述
  optional bool showExtraUrgentTips = 25; // 是否展示额外限时任务说明 @noSvr
  optional bool showExtraRewardTips = 26; // 是否展示额外奖励说明 @noSvr
  repeated PlayerLoginCondition loginCondition = 28;  // 玩家登录条件
  repeated int32 ids = 29; //额外配置的ID列表，具体含义根据功能自身区分
  repeated int32 nums = 30; //对应ids的数量配置
  optional int32 arenaHeroId = 31;  // 峡谷熟练等级突破任务所属英雄ID
}

// 任务基本配置
message TaskConf {
  option (resKey) = "id";
  optional int32 id = 1; // 任务id
  optional string name = 2; // 任务名称
  optional string desc = 3; // 任务描述
  optional TaskType type = 4; //任务类型
  optional TaskConditionConf condition = 5; // 任务条件
  optional int32 frontTask = 6; //@noCli 前置任务id
  optional TaskTimeConf showTime = 7; // 任务展示时间配置
  optional TaskTimeConf doTime = 8; //@noCli 任务可进行时间配置
  optional TaskRewardConf reward = 9; // 任务奖励
  optional int32 repeatNum = 10; //@noCli 可重复完成次数，默认1次
  optional int64 jumpId = 11; // 跳转id @noSvr
  optional int32 jumpType = 12; // 跳转类型 @noSvr
  optional string lowVersion = 13; //@noCli 最低版本
  optional string highVersion = 14; //@noCli 最高版本
  optional TaskExtraConf extraConf = 15; // 额外配置
  optional bool disable = 16; // 设为不可用
  optional string remark = 17; //@noCli 备注 @noSvr
  optional bool showHome = 18; // 是否主页展示 @noSvr
  optional bool inheritFrontProgress = 19; //@noCli 是否继承前置任务的进度
  optional int32 order = 20; //@noSvr 任务次级展示顺序，权重在完成状态之后，不配默认最末位 （最小权重0）
  optional TaskLoadTimeConf loadTime = 21; //@noCli 任务加载时间
  optional bool isShowBigReward = 22; //Linear活动显示时，该节点是否显示为下一次的大奖
  repeated int32 taskParam = 23;
  optional int32 isTaskShow = 24; // 是否任务外显 @noSvr
  optional int32 taskGroupId = 25; //@noSvr 任务组ID 由脚本自动生成
  optional bool  canUseItemFinish = 26; //是否能使用道具快速完成
  optional int64 finishNeedItemCount = 27; //需要消耗道具数量
  optional int32 price = 28; // 原本价格或价值
  optional int32 discountPrice = 29; // 折扣后价格或价值
  optional string unlockDescription = 30; // 任务解锁描述
}

message TaskGroup {
  option (resKey) = "id";
  optional int32 id = 1; // 任务组id
  optional TaskType type = 2; // 任务类型
  optional google.protobuf.Timestamp beginShowTime = 3; // 开始展示时间 yyyy-MM-dd hh:mm:ss
  optional google.protobuf.Timestamp endShowTime = 4; // 结束展示时间
  optional google.protobuf.Timestamp beginDoTime = 5; // 开始时间
  optional google.protobuf.Timestamp endDoTime = 6; // 结束时间
  repeated int32 taskIdList = 7; // 任务id列表
  optional TaskLoadType loadType = 8; //@noCli 加载方式
  optional string groupName = 9; // 任务组名 @noSvr
  optional TaskModuleType moduleType = 10; //@noCli 任务模块
  optional int32 unopenedHidden =11; // 任务未开启时是否隐藏 @noSvr
  optional int32 validDay = 12;
  optional int32 limitedType = 13; // 限时任务类型
}

message TaskTab {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1; // 任务列表id
  optional int32 taskTabId = 2; // 任务界面id
  optional string taskTabName = 3; // 任务界面名字
  optional int32 taskGroupId1 = 4; // 第一个任务组id
  optional int32 taskGroupId2 = 5; // 第二个任务组id
  optional int32 taskGroupId3 = 6; // 第三个任务组id
  repeated int32 taskRuleId = 7; // 任务规则id
  optional int32 taskRedTypeId = 8; // 任务红点类型id
  optional string taskText = 9; // 界面文字
  repeated int32 coinTypes = 10;//货币类型
  optional int32 OpenRegion = 11; // 开启范围
  optional bool needHideWhenAllCompleted = 12; //任务全部完成时不显示页签
  repeated int32 taskGroupIdArray = 13; //任务组数组
  optional google.protobuf.Timestamp beginShowTime = 14; // 开始展示时间
  optional google.protobuf.Timestamp endShowTime = 15; // 结束展示时间
}

message NewbieTaskMisc {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1; // id
  optional int32 groupId = 2; // 任务组id
  optional int32 belongDays = 3; // 所属天数
  optional int32 showRewardId = 4; // 展示奖励道具
  optional int32 OpenRegion = 5; // 开启范围
}

message table_TaskConf {
  repeated TaskConf rows = 1;
}

message table_TaskGroup {
  repeated TaskGroup rows = 1;
}

message table_TaskTab {// @noSvr
  repeated TaskTab rows = 1;
}

message table_NewbieTaskMisc {// @noSvr
  repeated NewbieTaskMisc rows = 1;
}


message table_UgcCreativeRoad {
  repeated UgcCreativeRoad rows = 1;
}

message UgcCreativeRoad {
  option (resKey) = "id";
  optional int32 id = 1; // id
  optional int32 groupId = 2; // 任务组id
  optional int32 stageId = 3; // 阶段Id
  optional int32 showRewardId = 4; // 展示奖励道具
  optional string stageIcon = 5; // 阶段icon
  optional string stageName = 6; // 阶段名
  optional string stageDesc = 7; // 阶段描述
  optional string stageLockIcon = 8; // 阶段锁定icon
}

message table_TaskGroupList {
  repeated TaskGroupList rows = 1;
}

message TaskGroupList {
  option (resKey) = "id";
  optional int32 id = 1; // id
  repeated int32 taskGroupList = 2; // 任务组id列表
  optional bool bIsVaild  = 3; //是否生效
}

message table_PasswordCodeTaskConfData {
  repeated PasswordCodeTaskConf rows = 1;
}

message PasswordCodeTaskConf {
  option (resKey) = "id";
  optional int32 id = 1; // id
  optional int32 activityId = 2; // 关联活动id
  optional int32 maxUse  = 3; // 单个口令码使用上限
  optional string shareTemp = 4; // 分享模板
}

message TaskOptionalRewardConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated RewardConf rewardInfo = 2;
}

message table_TaskOptionalRewardData {
  repeated TaskOptionalRewardConfig rows = 1;
}

message TaskRandomRewardPoolConf {
  option (resKey) = "poolId";
  optional int32 poolId = 1;
  optional int32 taskId = 2;
  optional int32 type = 3;
}

message table_TaskRandomRewardPoolConfData {
  repeated TaskRandomRewardPoolConf rows = 1;
}

message TaskRandomRewardConf {
  option (resKey) = "rewardId";
  optional int32 rewardId = 1;
  optional int32 poolId = 2;
  optional int32 weight = 3;

  repeated TimeLimitedItem items = 10;
}

message table_TaskRandomRewardConfData {
  repeated TaskRandomRewardConf rows = 1;
}