syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResCommon.proto";
import "ResKeywords.proto";
import "ResCondition.proto";
import "google/protobuf/timestamp.proto";

enum TradingCardType {
  TCT_Unknown = 0;
  TCT_Normal = 1[(name) = "常规"];
  TCT_Rare = 2[(name) = "稀有"];
}

enum CardTradeType {
  CTT_Unknown = 0;
  CTT_Require = 1[(name) = "索要"];
  CTT_Give = 2[(name) = "赠送"];
  CTT_Exchange = 3[(name) = "交换"];
}

enum CardCollectionType {
  CCT_Unknown = 0;
  CCT_Common = 1[(name) = "常规"];
  CCT_Activity = 2[(name) = "活动"];
}

// 卡牌获取来源
enum CardAddSourceType {
  CAST_Unknown = 0;
  CAST_Item = 1[(name) = "道具"];
  CAST_CardBag = 2[(name) = "卡包"];
  CAST_WildCard = 3[(name) = "万能卡"];
  CAST_Give = 4[(name) = "赠送"];
  CAST_Require = 5[(name) = "索要"];
  CAST_Exchange = 6[(name) = "交换"];
  CAST_GM = 7[(name) = "GM 命令"];
  CAST_NOVICE = 8[(name) = "新手奖励"];
}

// 卡牌扣除来源
enum CardDeductSourceType {
  CDST_Unknown = 100;
  CDST_Give = 101[(name) = "赠送"];
  CDST_Require = 102[(name) = "索要"];
  CDST_Exchange = 103[(name) = "交换"];
  CDST_Shop = 104[(name) = "商店"];
  CDST_GM = 105[(name) = "GM"];
}

enum CardTradeShareChannel {
  CRSC_Unknown = 0;
  CRSC_PrivateChat = 1[(name) = "私聊"];
  CRSC_CardChat = 2[(name) = "卡牌频道"];
  CRSC_QQ = 3[(name) = "QQ"];
  CRSC_WX = 4[(name) = "微信"];
}

enum CardTradeOpType{
  CTOT_Unknown = 0;
  CTOT_Give = 1[(name) = "赠送"];
  CTOT_Exchange = 2[(name) = "交换请求"];
  CTOT_Require = 3[(name) = "索要请求"];
  CTOT_Give_Receive = 4[(name) = "赠送被领取"];
  CTOT_Require_Provide = 5[(name) = "被索要给予"];
  CTOT_Exchange_Agree = 6[(name) = "同意交换"];
  CTOT_Expired = 7[(name) = "请求过期"];
}

enum CardBackgroundType {
  CBT_Unknown = 0;
  CBT_White = 1[(name) = "白色"];
  CBT_Orange = 2[(name) = "橙色"];
}

enum CycleCupsStageType{
  CSST_Unknown = 0;
  CSST_Once = 1[(name) = "节点类型_一次性节点"];
  CSST_Cycle = 2[(name) = "节点类型_循环节点"];
}

enum RedDotRefreshType{
  RDRT_Unknown = 0;
  RDRT_Normal = 1[(name) = "每次都刷新"];
  RDRT_Daily = 2[(name) = "每日刷新"];
  RDRT_Login = 3[(name) = "登录刷新"];
  RDRT_Once = 4[(name) = "一次性"];
}


// 卡牌配置
message TradingCardConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional TradingCardType type = 2;    // 卡牌类型
  optional int32 star = 3;              // 卡牌星级
  optional string name = 4;             //  卡牌名字
  optional string desc = 5;             //  卡牌描述
  optional string normalbg = 6;             //  卡牌图标
  optional string icon = 7;             //  卡牌图标
  optional string cardframe = 8;             //  卡牌图标 @noSvr
  optional string smallframe = 9;             //  小卡图标边框 @noSvr
  optional CardBackgroundType bgColorType=10;        //  卡牌icon展示底板品级色 @noSvr
  optional string spine = 11;             //  卡牌spine @noSvr
  optional int32 sortId = 12;            //  卡牌排序 @noSvr
  repeated int32 ruleIdList = 13;            // 交易规则ID
  optional string smallbg = 14;             //  小卡图标背景 @noSvr
  optional string disablebg = 15;             //  大图问号底图(只针对普卡) @noSvr
  optional string loadingbg = 16;             //  大图载入底图(只针对普卡) @noSvr
  optional string shadowbg = 17;             //  大图剪影@noSvr
  optional string cardStar = 18;             //   卡牌星级底图
  optional string otherbg = 19;             //  未获得&转菊花时的底图@noSvr
  optional string textdesbg = 20;             //  文案描述的底图
  optional int32  iconLabelId = 21;             //  卡牌标识类型
  optional int32 sizeType = 22;               // 卡牌主体形象尺寸类型
}
message table_TradingCardConfig {
  repeated TradingCardConfig rows = 1;
}

// 万能卡配置
message WildCardConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 cardCollectionId = 2;    // 卡集id
  optional int64 validDurationSec = 3;         // 生效时长（秒）
  optional string msg = 4;             //  万能牌描述
  optional string icon = 5;             //  万能牌资产图标
}

message table_WildCardConfig {
  repeated WildCardConfig rows = 1;
}

// 卡组配置
message TradingCardDeckConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 cardIdList = 2;  // 卡牌列表
  optional int32 sortId = 4;   // 排序 @noSvr
  optional string name = 5;             //  卡组名字 @noSvr
  optional string icon = 6;             //  卡组icon @noSvr
  optional string smallicon = 7;             //  卡组icon 小 @noSvr
  optional CardRewardConf reward = 8;      // 奖励
  optional string smallicon2 = 9;             //  卡组icon 小 @noSvr
  optional string smallicon3 = 10;             //  卡组icon 小 @noSvr
  optional google.protobuf.Timestamp beginTime = 11; // 开始时间
  optional google.protobuf.Timestamp endTime = 12; // 结束时间
  optional string backgroundImg = 13;  //卡组的背景图 @noSvr
  optional string cardbagiconskin = 14;  //卡集的所有卡包的开包动画IconSkin @noSvr
  optional string logoName = 15;  //卡组的背景图logo名称 @noSvr

}
message table_TradingCardDeckConfig {
  repeated TradingCardDeckConfig rows = 1;
}

message CardRewardConf {
  repeated int32 itemIdList = 1; // 道具id
  repeated int32 numList = 2; // 道具数量
  repeated int32 validPeriodList = 3; // 有效期(天)
  repeated google.protobuf.Timestamp expireTimestamps = 4; // 有效期(绝对时间)
}

// 卡集配置
message TradingCardCollectionConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 deckIdList = 2;        // 卡组列表
  optional CardCollectionType type = 4; // 类型
  optional int32 sortId = 5;   // 排序 @noSvr
  optional string name = 6;             //  卡组名字 @noSvr
  optional string icon = 7;             //  卡组icon @noSvr
  optional google.protobuf.Timestamp beginTime = 8; // 开始时间
  optional google.protobuf.Timestamp endTime = 9; // 结束时间
  optional string version = 10;             //  最低版本要求
  repeated int32 tagGroupList = 11;                 // 待定卡tag列表
  optional int32 rankId = 12;                    // 排行榜id
  optional int32 rankType = 13;                    // 排行榜类型
  optional int32 cycleId = 14;                    // 循环奖励ID  废弃,用下面的字段
  optional CardRewardConf reward = 15;      // 奖励
  repeated int32 bonusNeedActivityId = 16;      // 开启卡包加成需要的活动id(废弃)
  optional int32 openCardBagBonus = 17;      // 开启卡包加成比例 百分比(废弃)
  optional bool showHistory = 18;           // 展示历史卡集
  repeated int32 cycleIds = 19;             // 循环奖励ID
   optional string cardIcon = 20;             //  卡牌右上角icon @noSvr
   optional string getWay = 21;             //  获取途径 @noSvr
   optional string jumpId = 22;             //  跳转ID @noSvr
   optional string jumpargs = 23;             //  跳转参数 @noSvr
   optional string itemTipsIcon = 24;             //  类似道具的Icon @noSvr
  repeated int32 exchangeMallId = 25;         //兑换商城的ID
  optional SnsShareSceneType tradeShare = 26;  //分享的场景ID @noSvr
  optional string backgroundImg = 27;  //卡牌主页卡集的背景图 @noSvr
  optional string cardbagiconskin = 28;  //卡集的所有卡包的开包动画IconSkin @noSvr
  optional int32 limitExchangeCardCount = 29;  //当前卡集指定交换的选择卡牌数量
  optional int32 joinTradRank = 30;     //是否参加交易总榜  0 不参加  1 参加
  optional string tabIcon = 31;             //  卡组tab icon @noSvr
}
message table_TradingCardCollectionConfig {
  repeated TradingCardCollectionConfig rows = 1;
}

message TradingCardRule {
  optional google.protobuf.Timestamp beginTime = 1; // 生效开始时间
  optional google.protobuf.Timestamp endTime = 2;   // 生效结束时间
  optional int32 activityId = 3;                    // 关联活动时间
}
message TradingCardTradeRuleConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional TradingCardRule rule = 2;         // 规则
  repeated CardTradeType tradeTypeList = 3;  // 生效的交易类型
}

message table_TradingCardTradeRuleConfig {
  repeated TradingCardTradeRuleConfig rows = 1;
}

enum TradingCardActivityType {
  TCAT_Unknown = 0;
  TCAT_Trade = 1[(name) = "卡牌互动"];
  TCAT_Trade_BOUND = 2[(name) = "卡牌增量"];
}

// 卡牌活动
message TradingCardActivityConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional google.protobuf.Timestamp beginTime = 2; // 生效开始时间
  optional google.protobuf.Timestamp endTime = 3;   // 生效结束时间
  optional TradingCardActivityType type = 4;        // 活动类型
  repeated int64 paramList1 = 5;                    // 活动参数1 (卡牌互动: 卡牌id)
  repeated int64 paramList2 = 6;                    // 活动参数2 (卡牌互动: 互动类型)
  optional bool enable = 7;                         // 是否开启
  optional int32 collectionId = 8;                  // 所属卡集
  optional bool showInCardActivityMain = 9;         // 是否在卡牌活动主界面显示 @noSvr
  optional string tagName = 14;                     // 页签名称 @noSvr
  optional string uiName = 11;                      // 界面名称 @noSvr
  optional string subTitle = 10;                    // 副标题 @noSvr
  optional int32 ruleId = 12;                       // 活动规则 @noSvr
  optional int32 sort = 13;                         // 排序 @noSvr
}

message table_TradingCardActivityConfig {
  repeated TradingCardActivityConfig rows = 1;
}

// 卡包配置
message CardBagConfig {
  option (resKey) = "id";
  optional int32 id = 1;                   //卡包id
  optional int32 baseWeightPool = 2;        // 卡包基本权重
  optional int32 drawLimitTime = 3;              // 抽奖次数
  optional int32 guaranteeTimes = 4;              // 保底可触发次数
  optional int32 guaranteeParam = 5;              // 保底触发参数
  optional int32 guaranteeWeightPool = 6;         // 保底权重池
  optional int32 tradingCardCollectionId = 7;      // 卡集id
  optional int32 activityBuff = 8;             // 开启卡包加成比例 百分比
  optional int32 guaranteeBaseSolo = 9;             // 0 受普通池结果影响,1不受影响
  repeated int32 tradingCardDeckIdList = 10; // 卡组id列表
}

message table_CardBagConfigData {
  repeated CardBagConfig rows = 1;
}



// 卡包权重池
message CardBagPoolConfig {
  option (resKey) = "id";
  optional int32 id = 1;           //权重池id
  repeated CardBagPoolInfo cardBagPool = 2;
}

message CardBagPoolInfo{
  optional int32 cardType = 1;        //卡牌类型  0 正常卡  1待定卡
  optional int32 drawId = 2;           // 抽奖id
  optional int32 rangeWeight = 3;     // 权重
  optional google.protobuf.Timestamp beginTime = 4; // 生效开始时间
  optional google.protobuf.Timestamp endTime = 5;   // 生效结束时间
}


message table_CardBagPoolConfigData {
  repeated CardBagPoolConfig rows = 1;
}


// 特殊卡权重值
message CardSpecialPoolConfig {
  option (resKey) = "id";
  optional int32 id = 1;        //权重池id
  repeated SpecialCardPoolInfo specialCardPool = 2;
}

message SpecialCardPoolInfo{
  optional int32 group = 1;        //分组
  optional int32 drawId = 2;        //抽奖id
  optional int32 rangeWeight = 3;     // 权重
}

message table_CardSpecialPoolConfigData {
  repeated CardSpecialPoolConfig rows = 1;
}

message robotMessage {
  optional int32 itemId = 1; //itemId
  optional int32 count = 2;  //次数
  optional int32 tag = 3;  //tag
  optional int32 carBagId = 4;  //卡包
  repeated robotSubMessage subMessage = 5;
}


message robotSubMessage {
  optional int32 count = 1; //抽取次数
  optional int32 drawPool = 2;  //抽取奖池
  optional int32 drawId = 3;  //抽取结果
  optional int32 specDraw = 4;  //待定卡抽取结果
}

message RobotExtCardBagMessage{
  optional int32 id = 1; //id
  optional int32 carBagId = 2;  //卡包id
  optional int32 count = 3;  //次数
  optional int64 tag = 4;  //tag
  repeated SubRobotExtCardBagMessage subMessage = 5;
}
message SubRobotExtCardBagMessage{
  optional int32 extCarBagId = 1;  //额外卡包id
  map<int32,int32> curWeight = 2; //额外卡包当前权重,权重+增加的权重
  map<int32,int32> afterWeight = 3; //额外卡包抽奖后增加的权重
  optional int32 curCount = 4;//当前为第几次随机
}

message table_TradingCardCycleCupsConfig{
  repeated TradingCardCycleCupsConfig rows = 1;
}

message TradingCardCycleCupsConfig{
  option (resKey) = "cycleId,stageId";
  optional int32 cycleId = 1; //循环id
  optional int32 stageId = 2; //节点ID
  optional CycleCupsStageType stageType = 3; //节点类型
  optional int32  stageValue = 4; //奖杯征程进度值统计节点
  repeated Item rewardItem = 5; //奖励
  optional int32 cycleRepeat = 6;  //单个节点循环次数
}

message table_TradingCardExchangeConfig{
  repeated TradingCardExchangeConfig rows = 1;
}

message TradingCardExchangeConfig{
  option (resKey) = "collectionId,exchangeId";
  optional int32 collectionId = 1; //卡集ID
  optional int32 exchangeId = 2; //兑换ID
  optional int32 sort = 3; //@noSvr 奖励数量
  optional int32 exchangeCd = 4; //兑换CD(小时,0CD)
  optional int32 maxCount = 5; //最大兑换次数(0无限制)
  optional google.protobuf.Timestamp startTime = 6; //兑换开始时间
  optional google.protobuf.Timestamp endTime = 7; //兑换结束时间
  repeated Item rewardItem = 8; //奖励
  optional int32 needStar = 9;  //需要的星星数
}

message table_TradingCardNoviceRewardConfig{
  repeated TradingCardNoviceRewardConfig rows = 1;
}

message TradingCardNoviceRewardConfig{
  option (resKey) = "collectionId,id";
  optional int32 collectionId = 1; //卡集ID
  optional int32 id = 2; //关卡ID
  optional Item rewardItem = 3; //废弃
  optional int32 rangeWeight = 4; //权重
  optional Item rewardCard = 5;  //废弃
  optional int32 cardId = 6;  //奖励卡ID
  optional int32 cardNum= 7;  //奖励卡数量
}

message table_TradingCardCycleCupsRuleConfig{
  repeated TradingCardCycleCupsRuleConfig rows = 1;
}

message TradingCardCycleCupsRuleConfig{
  option (resKey) = "cycleId";
  optional int32 cycleId = 1; //循环id
  optional google.protobuf.Timestamp startTime = 6; //兑换开始时间
  optional google.protobuf.Timestamp endTime = 7; //兑换结束时间
  optional int32 weekProgressMax = 8; //周进度最大值
}

message TradingCardRedDotRuleConfig{
  option (resKey) = "redDotType,redDotId";
  optional  int32 redDotType  = 1;//红点类型
  optional  int64 redDotId  = 2;//红点ID
  optional bool enable = 3;    // 是否开启
  optional RedDotRefreshType redDotRefreshType = 4; //刷新类型
}

message table_TradingCardRedDotRuleConfig{
  repeated TradingCardRedDotRuleConfig rows = 1;
}


message table_CardBagExtOutPutConfig{
  repeated CardBagExtOutPutConfig rows = 1;
}

message CardBagExtOutPutConfig{
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 packId = 2;//常规包id
  repeated int32 extraPackId = 3; //额外获得卡包id
  optional int32 extraPackChance = 4; //额外获得卡包的概率
  repeated int32 weight = 5; //额外获得卡包id的权重
  repeated int32 addWeight = 6; //有多种类型的额外卡包时，未开到对应卡包时增加的权重
  optional google.protobuf.Timestamp beginTime = 7; //开启时间
  optional google.protobuf.Timestamp endTime = 8; //结束时间
}
