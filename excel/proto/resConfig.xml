<?xml version="1.0" encoding="GBK" standalone="yes" ?>
<resconfig>
    <entry protofile="ResWhiteList" messagename="OpenIdWhiteList" pbinname="DeviceWhiteListData" classname="DeviceWhiteListData"
        serverloadflag="ST_DirServer,ST_GameServer,ST_UgcappServer" />
    <entry protofile="ResWhiteList" messagename="OpenIdWhiteList" pbinname="DeviceModelWhiteListData" classname="DeviceModelWhiteListData"
        serverloadflag="ST_DirServer,ST_GameServer,ST_UgcappServer" />
    <entry protofile="ResWhiteList" messagename="DeviceWhiteList" pbinname="DeviceBlackListData" classname="DeviceBlackListData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="DeviceModelWhiteList" pbinname="CpuHardwareBlackListData" classname="DeviceModelBlackListData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="OpenIdWhiteList" pbinname="InternalOpenIdWhiteListData" classname="InternalOpenIdWhiteListData"
        serverloadflag="ST_DirServer,ST_GameServer,ST_UgcappServer"
        rainbowenvdefault="1" rainbowgroup="WHITE_LIST.INTERNAL_OPEN_ID" />
    <entry protofile="ResWhiteList" messagename="OpenIdWhiteList" pbinname="TestOpenIdWhiteListData" classname="TestOpenIdWhiteListData"
        serverloadflag="ST_DirServer,ST_GameServer,ST_UgcappServer"
        rainbowenvdefault="1" rainbowgroup="WHITE_LIST.TEST_OPEN_ID" />
    <entry protofile="ResWhiteList" messagename="FeatureOpenWhiteListData" pbinname="FeatureOpenWhiteListData" classname="FeatureOpenWhiteListData"
           serverloadflag="ST_GameServer"
           rainbowenvdefault="1" rainbowgroup="WHITE_LIST.FEATURE_OPEN_WHITELIST" />
    <entry protofile="ResWhiteList" messagename="OpenIdWhiteList" pbinname="GrayScaleWhiteListData" classname="GrayScaleWhiteListData"
        serverloadflag="ST_GameServer"
        rainbowenvdefault="1" rainbowgroup="WHITE_LIST.GRAY_SCALE_OPEN_ID" />
    <entry protofile="ResWhiteList" messagename="OpenIdWhiteList" pbinname="TraceWhiteListData" classname="TraceWhiteListData"
        serverloadflag="ST_All" />
    <entry protofile="ResWhiteList" messagename="AiLabMatchDyeGroupWhiteList" pbinname="AiLabMatchDyeWhiteListData" classname="AiLabMatchDyeWhiteListData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer,ST_MatchServer,ST_StarproommatchServer,ST_BattleServer,ST_StarpbattleServer"
        rainbowenvdefault="1" rainbowgroup="WHITE_LIST.AI_LAB_DYE_UID" />
    <entry protofile="ResWhiteList" messagename="AiLabArenaWarmRoundMatchDyeGroupWhiteList" pbinname="AiLabArenaWarmRoundMatchDyeWhiteListData" classname="AiLabArenaWarmRoundMatchDyeWhiteListData"
           serverloadflag="ST_GameServer,ST_RoomServer,ST_MatchServer,ST_BattleServer" />
    <entry protofile="ResWhiteList" messagename="SuperCoreRankActivityWhiteList" pbinname="SuperCoreRankActivityWhiteData"
           serverloadflag="ST_GameServer,ST_IdipServer" rainbowenvdefault="1" rainbowgroup="WHITE_LIST.SUPER_CORE_RANK_ACT_OPEN_ID"/>
    <entry protofile="ResTradingCard" messagename="TradingCardConfig" pbinname="TradingCardConfigData" classname="TradingCardConfigData"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResTradingCard" messagename="WildCardConfig" pbinname="WildCardConfigData" classname="WildCardConfigData"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardDeckConfig" pbinname="TradingCardDeckConfigData" classname="TradingCardDeckConfigData"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardCollectionConfig" pbinname="TradingCardCollectionConfigData" classname="TradingCardCollectionConfigData"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardTradeRuleConfig" pbinname="TradingCardTradeRuleConfigData" classname="TradingCardTradeRuleConfigData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardActivityConfig" pbinname="TradingCardActivityConfigData" classname="TradingCardActivityConfigData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="CardSpecialPoolConfig" pbinname="CardSpecialPoolConfigData" classname="CardSpecialPoolConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="CardBagPoolConfig" pbinname="CardBagPoolConfigData" classname="CardBagPoolConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="CardBagConfig" pbinname="CardBagConfigData" classname="CardBagConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardCycleCupsConfig" pbinname="TradingCardCycleCupsConfigData" classname="TradingCardCycleCupsConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardExchangeConfig" pbinname="TradingCardExchangeConfigData" classname="TradingCardExchangeConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardNoviceRewardConfig" pbinname="TradingCardNoviceRewardConfigData" classname="TradingCardNoviceRewardConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardCycleCupsRuleConfig" pbinname="TradingCardCycleCupsRuleConfigData" classname="TradingCardCycleCupsRuleConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResTradingCard" messagename="TradingCardRedDotRuleConfig" pbinname="TradingCardRedDotRuleConfigData" classname="TradingCardRedDotRuleConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResGameModeReturn" messagename="GameModeReturnConfig" pbinname="GameModeReturnConfigData" classname="GameModeReturnConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResGameModeReturn" messagename="GameModeReturnStepConfig" pbinname="GameModeReturnStepConfigData" classname="GameModeReturnStepConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResGameModeReturn" messagename="GameModeReturnCheckConfig" pbinname="GameModeReturnCheckConfigData" classname="GameModeReturnCheckConfigData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResQuiz" messagename="QuizConfig" pbinname="QuizConfigData" classname="QuizConfigData"
            serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResQuiz" messagename="QuestionConfig" pbinname="QuestionConfigData" classname="QuestionConfigData"
            serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResQuiz" messagename="ChoiceConfig" pbinname="ChoiceConfigData" classname="ChoiceConfigData"
            serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivitySuperCoreRank" messagename="ActivitySuperCoreRankBaseInfo" pbinname="ActivitySuperCoreRankBaseData"
           serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResWhiteList" messagename="OpenIdWhiteList" pbinname="WatermarkOpenIdWhiteListData" classname="WatermarkOpenIdWhiteListData" />
    <entry protofile="ResWhiteList" messagename="IPBlackList" pbinname="IPBlackListData" classname="IPBlackListData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="PressTestOpenId" pbinname="PressTestOpenIdData" classname="PressTestOpenIdData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="TestBanIpCity" pbinname="TestBanIpCity" classname="TestBanIpCityData"
        serverloadflag="ST_DirServer,ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="StreamOpenIdWhitelist" pbinname="StreamOpenIdWhitelistData" classname="StreamOpenIdWhitelistData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="TestOpenidSign" pbinname="TestOpenidSign" classname="TestOpenidSign"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="UserBindPackageConfig" pbinname="UserBindPackageConfigData" classname="UserBindPackageData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWhiteList" messagename="SimpleOpenId" pbinname="IdipForwardWhitelistData" classname="IdipForwardWhitelistData"
        serverloadflag="ST_IdipServer"
        rainbowenvdefault="1" rainbowgroup="WHITE_LIST.IDIP_FORWARD_OPENID" />
    <entry protofile="ResMall" messagename="CommercialConfFieldMeta" pbinname="CommercialConfFieldMeta" classname="CommercialConfFieldMetaData"
            serverloadflag="ST_GameServer,ST_IdipServer,ST_MidasServer"/>
    <entry protofile="ResMall" messagename="CommercialConfModifyCfg" pbinname="CommercialConfModifyCfg" classname="CommercialConfModifyCfgData"
            serverloadflag="ST_GameServer,ST_IdipServer,ST_MidasServer" rainbowenvdefault="0" rainbowgroup="commercial_switch"/>
    <entry protofile="ResMatch" messagename="MatchDimension" pbinname="MatchDimensionData" classname="MatchDimensionData"
            serverloadflag="ST_MatchServer,ST_StarproommatchServer,ST_RoomServer,ST_StarproomServer"
            splitsubtableflag="1" subtablename="MatchDimensionData" />
    <entry protofile="ResMatch" messagename="MatchRoomInfo" pbinname="MatchRoomInfoData" classname="MatchRoomInfoData"
        serverloadflag="ST_MatchServer,ST_RoomServer,ST_BattleServer,ST_StarproommatchServer,ST_StarproomServer,ST_StarpbattleServer"
        splitsubtableflag="1" subtablename="MatchRoomInfoData_BS,MatchRoomInfoData_JS,MatchRoomInfoData_Mayday,MatchRoomInfoData_Metro,MatchRoomInfoData_acm,MatchRoomInfoData_arena,MatchRoomInfoData_chase,MatchRoomInfoData_competition,MatchRoomInfoData_dnd,MatchRoomInfoData_fps,MatchRoomInfoData_main,MatchRoomInfoData_nr3e,MatchRoomInfoData_ugc,MatchRoomInfoData_HOK,MatchRoomInfoData_FB" />
    <entry protofile="ResMatch" messagename="MatchRule" pbinname="MatchRuleData" classname="MatchRuleData"
        serverloadflag="ST_MatchServer,ST_RoomServer,ST_StarproommatchServer,ST_StarproomServer"
        splitsubtableflag="1" subtablename="MatchRuleData_BS,MatchRuleData_JS,MatchRuleData_Mayday,MatchRuleData_Metro,MatchRuleData_acm,MatchRuleData_arena,MatchRuleData_chase,MatchRuleData_competition,MatchRuleData_dnd,MatchRuleData_fps,MatchRuleData_main,MatchRuleData_nr3e,MatchRuleData_ugc,MatchRuleData_HOK,MatchRuleData_FB" />
    <entry protofile="ResMatch" messagename="MatchRuleRange" pbinname="MatchRuleRangeData" classname="MatchRuleRangeData"
        serverloadflag="ST_MatchServer,ST_RoomServer,ST_BattleServer,ST_GameServer,ST_StarproommatchServer,ST_StarproomServer,ST_StarpbattleServer"
        splitsubtableflag="1" subtablename="MatchRuleRangeData_BS,MatchRuleRangeData_Chase,MatchRuleRangeData_JS,MatchRuleRangeData_Mayday,MatchRuleRangeData_Metro,MatchRuleRangeData_acm,MatchRuleRangeData_arena,MatchRuleRangeData_competition,MatchRuleRangeData_dnd,MatchRuleRangeData_fps,MatchRuleRangeData_main,MatchRuleRangeData_nr3e,MatchRuleRangeData_ugc,MatchRuleRangeData_HOK,MatchRuleRangeData_lobbymatch,MatchRuleRangeData_FB" />
    <entry protofile="ResMatch" messagename="MatchType" pbinname="MatchTypeData" classname="MatchTypeData"
        serverloadflag="ST_MatchServer,ST_RoomServer,ST_BattleServer,ST_GameServer,ST_IdipServer,ST_UgcplatServer,ST_UgcServer,ST_StarproommatchServer,ST_StarproomServer,ST_StarpbattleServer,ST_ActivityServer"
        splitsubtableflag="1" subtablename="MatchTypeData_BS,MatchTypeData_Chase,MatchTypeData_JS,MatchTypeData_Mayday,MatchTypeData_Metro,MatchTypeData_acm,MatchTypeData_arena,MatchTypeData_coming,MatchTypeData_competition,MatchTypeData_dnd,MatchTypeData_fps,MatchTypeData_jumpTo,MatchTypeData_main,MatchTypeData_nr3e,MatchTypeData_ugc,MatchTypeData_HOK,MatchTypeData_YXSHZ,MatchTypeData_lobbymatch,MatchTypeData_FB,MatchTypeData_COC" />
    <entry protofile="ResMatch" messagename="MatchRecommendPageData" pbinname="MatchRecommendPageData" classname="MatchRecommendPageData"
        serverloadflag="ST_GameServer"/>
    <entry protofile="ResMatch" messagename="MatchRecommendDefaultData" pbinname="MatchRecommendDefaultData" classname="MatchRecommendDefaultData"
        serverloadflag="ST_GameServer"/>
    <entry protofile="ResMatch" messagename="MatchModeType" pbinname="MatchModeTypeData" classname="MatchModeTypeData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer,ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer,ST_GameServer" />
    <entry protofile="ResMatch" messagename="MatchDate" pbinname="MatchDateData" classname="MatchDateData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer"
        splitsubtableflag="1" subtablename="MatchDateData_main,MatchDateData_rank" />
    <entry protofile="ResMatch" messagename="DsMapSet" pbinname="DsMapSetData" classname="DsMapSetData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_LobbyServer" />
    <entry protofile="ResMatch" messagename="MatchLevelPassScoreInfo" pbinname="MatchLevelPassScoreData" classname="MatchLevelPassScoreData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatch" messagename="LevelPerformanceScoreInfo" pbinname="LevelPerformanceScoreData" classname="LevelPerformanceScoreData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatch" messagename="LevelScoreGradeInfo" pbinname="LevelScoreGradeData" classname="LevelScoreGradeData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatch" messagename="MatchCampConfig" pbinname="MatchCampData" classname="MatchCampData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatch" messagename="MatchCampGradeConfig" pbinname="MatchCampGradeData" classname="MatchCampGradeData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResHOKAI" messagename="HOKAIData" pbinname="HOKAIData" classname="HOKAIData"
           serverloadflag="ST_GameServer,ST_BattleServer" />
    <entry protofile="ResMatch" messagename="MatchCampGradeDimensionConfig" pbinname="MatchCampGradeDimensionData" classname="MatchCampGradeDimensionData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatch" messagename="MatchBattleEventConfig" pbinname="MatchBattleEventListData" classname="MatchBattleEventListData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer"
		splitsubtableflag="1" subtablename="MatchBattleEventListData,MatchBattleEventListData_FB" />
    <entry protofile="ResMatch" messagename="MatchNewTagConfig" pbinname="MatchNewTagDateData" classname="MatchNewTagDateData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatch" messagename="MatchUnlockConditionConfig" pbinname="MatchUnlockConditionData" classname="MatchUnlockConditionData"
        serverloadflag="ST_GameServer"
            splitsubtableflag="1" subtablename="MatchUnlockConditionData_main" />
    <entry protofile="ResMatch" messagename="MatchRecommendData" pbinname="MatchRecommendData" classname="MatchRecommendData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResMatch" messagename="TeamModePlaySwitchRule"
            pbinname="TeamModePlaySwitchRuleData"
            classname="TeamModePlaySwitchRuleData"
            serverloadflag="ST_GameServer,ST_RoomServer"/>
    <entry protofile="ResMatch" messagename="MatchRuleTeamDegreeCondData" pbinname="MatchRuleTeamDegreeCondData" classname="MatchRuleTeamDegreeCondData"
           serverloadflag="ST_RoomServer"
            splitsubtableflag="1" subtablename="MatchRuleTeamDegreeCondData_arena,MatchRuleTeamDegreeCondData_HOK" />
    <entry protofile="ResMatchLevelRecord" messagename="MatchLevelRecordRule"
            pbinname="MatchLevelRecordRuleData"
            classname="MatchLevelRecordRuleData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResMatchLevelRecord" messagename="MatchLevelRandEventRecordRule"
            pbinname="MatchLevelRandEventRecordRuleData"
            classname="MatchLevelRandEventRecordRuleData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResMatchABTest" messagename="MatchABTestData" pbinname="MatchABTestData" classname="MatchABTestData"
            serverloadflag="ST_GameServer,ST_BattleServer,ST_RoomServer" />
    <entry protofile="ResMatchABTest" messagename="MatchABTestScoreData" pbinname="MatchABTestScoreData" classname="MatchABTestScoreData"
            serverloadflag="ST_GameServer,ST_BattleServer,ST_RoomServer" />
    <entry protofile="ResChat" messagename="ChatConfData" pbinname="ChatConf" classname="ChatConf"
        serverloadflag="ST_GameServer,ST_ChatServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResChat" messagename="ChatContentEntry" pbinname="ChatContentEntryData" classname="ChatContentEntryData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResChat" messagename="ChatMsgTypeConfigData" pbinname="ChatMsgTypeConfigData" classname="ChatMsgTypeConfigData"
           serverloadflag="ST_ChatServer,ST_GameServer" />
    <entry protofile="ResRelation" messagename="RelationConf" pbinname="RelationConfData" classname="RelationConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRelation" messagename="RelationBattleModeIntimacyConf" pbinname="RelationBattleModeIntimacyConfData" classname="RelationBattleModeIntimacyConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRelation" messagename="IntimateRelationConf" pbinname="IntimateRelationConfData" classname="IntimateRelationConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRelation" messagename="IntimateRelationLevelConf" pbinname="IntimateRelationLevelConfData" classname="IntimateRelationLevelConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRelation" messagename="RelationMiscConf" pbinname="RelationMiscConfData" classname="RelationMiscConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRelation" messagename="LuckyFriendTaskConf" pbinname="LuckyFriendTaskConfData" classname="LuckyFriendTaskConfData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResRelation" messagename="IntimateRelationExtraCntUnlockConf" pbinname="IntimateRelationExtraCntUnlockConfData" classname="IntimateRelationExtraCntUnlockConfData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResDressUp" messagename="DressUp" pbinname="DressUp" classname="DressUp"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResDressUp" messagename="DressUpItem" pbinname="DressUpItem" classname="DressUpItem"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResAIInfo" messagename="AIInfoNameImage" pbinname="AIInfoNameImageData" classname="AIInfoNameImageData"
        serverloadflag="ST_BattleServer,ST_SceneServer,ST_GameServer,ST_ArenaServer,ST_StarpbattleServer" />
    <entry protofile="ResAIInfo" messagename="AIInfoRecordSkin" pbinname="AIInfoRecordSkinData" classname="AIInfoRecordSkinData"
        serverloadflag="ST_BattleServer,ST_SceneServer,ST_ArenaServer,ST_StarpbattleServer"/>
    <entry protofile="ResAIInfo" messagename="AIInfoPlatform" pbinname="AIInfoPlatformData" classname="AIInfoPlatformData"
        serverloadflag="ST_BattleServer,ST_SceneServer,ST_ArenaServer,ST_StarpbattleServer"  />
    <entry protofile="ResAIInfo" messagename="AIInfoDifficulty" pbinname="AIInfoDifficultyData" classname="AIInfoDifficultyData"
        serverloadflag="ST_BattleServer,ST_SceneServer,ST_ArenaServer,ST_StarpbattleServer" />
    <entry protofile="ResResource" messagename="ResourceRequire" pbinname="ResourceRequireData" classname="ResourceRequireData"
        serverloadflag="ST_DirServer,ST_GameServer" />
    <entry protofile="ResServerWhite" messagename="ServerWhiteData" pbinname="ResServerWhiteData" classname="ResServerWhiteData"
        serverloadflag="ST_DirServer" />
    <entry protofile="ResServerWhite" messagename="ServerWhiteAddrData" pbinname="ResServerWhiteAddrData" classname="ResServerWhiteAddrData"
        serverloadflag="ST_DirServer" />
    <entry protofile="ResLevelDrop" messagename="LevelDropConf" pbinname="LevelDropConfData" classname="LevelDropConfData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="LevelDropConfData,LevelDropConfData_arena,LevelDropConfData_hok,LevelDropConfData_Chase" />
    <entry protofile="ResLevelDrop" messagename="LevelDropLimitConfig" pbinname="LevelDropLimitConfData" classname="LevelDropLimitConfData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="LevelDropLimitConfData,LevelDropLimitConfData_arena,LevelDropLimitConfData_hok"/>
    <entry protofile="ResLevelDrop" messagename="LevelDropDimension" pbinname="LevelDropDimensionData" classname="LevelDropDimensionData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResPublicChat" messagename="SimpleChannelChatGroup" pbinname="GlobalWorldChatGroupList" classname="GlobalWorldChatGroupList"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResAchievement" messagename="AchievementConf" pbinname="AchievementConfData" classname="AchievementConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResAchievement" messagename="AchievementTaskConf" pbinname="AchievementTaskConfData" classname="AchievementTaskConfData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="AchievementTaskConfData_js,AchievementTaskConfData_main,AchievementTaskConfData_nr3e,
        AchievementTaskConfData_arena,AchievementTaskConfData_moba,AchievementTaskConfData_nr3e8" />
    <entry protofile="ResClub" messagename="ClubLabelConfig" pbinname="ClubLabelConfig" classname="ClubLabelConfig"
        serverloadflag="ST_ClubServer" />
    <entry protofile="ResClub" messagename="ClubCommonConf" pbinname="ClubCommonConf" classname="ClubCommonConf"
        serverloadflag="ST_GameServer,ST_ClubServer,ST_RankServer" />
    <entry protofile="ResPermit" messagename="PermitTaskConfig" pbinname="PermitTaskConfig" classname="PermitTaskConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPermit" messagename="PermitRewardConfig" pbinname="PermitRewardConfig" classname="PermitRewardConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPermit" messagename="PermitConstantConf" pbinname="PermitConstantConf" classname="PermitConstantConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResBlockingWord" messagename="BlockingWord" pbinname="BlockingWordData" classname="BlockingWordData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMisc" messagename="ClientKVConf" pbinname="ClientKVConfForLetsGo" classname="ClientKVConf"
        serverloadflag="ST_GameServer"
        rainbowenvdefault="0" rainbowgroup="ClientKVConf"/>
    <entry protofile="ResMisc" messagename="DsMiscConf" pbinname="DsMiscConfForLetsGo" classname="DsMiscConf"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer,ST_LobbyServer,ST_XiaowoServer,ST_FarmServer,ST_IdipServer,ST_StarpServer,ST_StarpgroupServer"
        rainbowenvdefault="0" rainbowgroup="DsMiscConf" />
    <entry protofile="ResMisc" messagename="MiscConfArena" pbinname="MiscConfArena"
            classname="MiscConfArena"
            serverloadflag="ST_GameServer,ST_RankServer,ST_ArenaServer,ST_BattleServer,ST_RoomServer"/>

    <entry protofile="ResBackpackItem" messagename="ResShareGiftType" pbinname="ResShareGiftType"
           classname="ResShareGiftType"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResBackpackItem" messagename="ResShareGiftLimit" pbinname="ResShareGiftLimit"
           classname="ResShareGiftLimit"
           serverloadflag="ST_GameServer" />

    <entry protofile="ResBackpackItem" messagename="Item_BackpackItem" pbinname="BackpackItem" classname="BackpackItem"
           serverloadflag="ST_GameServer,ST_IdipServer,ST_XiaowoServer,ST_BattleServer,ST_UgcplatServer,ST_MidasServer,ST_FarmServer,ST_CocServer,ST_StarpbattleServer"
        splitsubtableflag="1" subtablename="BackpackItem_Action1P,BackpackItem_Action2P,
        BackpackItem_BackOrnament,BackpackItem_ChatBubble,BackpackItem_Currency,BackpackItem_Emoji,BackpackItem_AppIcon,
        BackpackItem_Face,BackpackItem_FaceOrnament,BackpackItem_Frame,BackpackItem_Gloves,
        BackpackItem_HandOrnament,BackpackItem_HeadWear,BackpackItem_InteractiveProp,BackpackItem_Item,
        BackpackItem_Kart,BackpackItem_LowerGarment,BackpackItem_Nameplate,BackpackItem_PackageCommon,
        BackpackItem_PackagePick,BackpackItem_PackageRandom,BackpackItem_Portrait,BackpackItem_ProfileTheme,
        BackpackItem_Shuttle,BackpackItem_Suit,BackpackItem_TYC,BackpackItem_Title,BackpackItem_UgcBadge,
        BackpackItem_UpperGarment,BackpackItem_Vehicle,BackpackItem_Xiaowo,BackpackItem_Farm,
        BackpackItem_AttackAnim,BackpackItem_MVPAnim,BackpackItem_Meeting,BackpackItem_NR3E_Interactive,BackpackItem_ReportAnim,BackpackItem_Identity,BackpackItem_NR3E8,
        BackpackItem_NR3E_DisguiseEffect,BackpackItem_NR3E_Weapon,BackpackItem_NR3E3Treasure,
        BackpackItem_PackageCommon_huoyueyunying,BackpackItem_PackageCommon_shangyehuayunying,BackpackItem_PackageCommon_tesewanfayunying,BackpackItem_PackagePick_huoyueyunying,BackpackItem_PackagePick_shangyehuayunying,BackpackItem_PackagePick_tesewanfayunying,BackpackItem_PackageRandom_huoyueyunying,BackpackItem_PackageRandom_shangyehuayunying,BackpackItem_PackageRandom_tesewanfayunying,BackpackItem_Title_huiyueshangyehua,BackpackItem_Title_tesewanfayunying,BackpackItem_huoyueyunying,BackpackItem_shangyehuayunying,BackpackItem_tesewanfayunying,
        BackpackItem_Arena,BackpackItem_FootPrint,BackpackItem_FriendInviteBox,BackpackItem_FriendOnlineReminder,
        BackpackItem_QualifyingCard,BackpackItem_ChatColorFont,BackpackItem_HeroSkin,BackpackItem_Title_Arena,BackpackItem_SpecialSpeak,BackpackItem_PersonalityState,BackpackItem_Velarium,BackpackItem_Item_Card,BackpackItem_DailyBattleOrnamentationDataConf,
        BackpackItem_Mood,BackpackItem_PackageShare,BackpackItem_ChaseProp,BackpackItem_Title_Chase,
        BackpackItem_coc,BackpackItem_COCAction1P,BackpackItem_COCSuit,BackpackItem_VehicleDecarateItem,
        BackpackItem_ChasePropSkin,BackpackItem_ChaseBossSkin,BackpackItem_UgcCreatorBadge,BackpackItem_CreatorHomePageSkin,BackpackItem_surrounding,BackpackItem_ChaseBossUnlock,BackpackItem_ChasePropUnlock,BackpackItem_UgcCommon,BackpackItem_InteractiveToy,BackpackItem_Badge"/>
    <entry protofile="ResBackpackItemEffect" messagename="ItemStatusChangeConfig" pbinname="ItemStatusChangeData" classname="ItemStatusChangeData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="ItemStatusChangeData_main" />
    <entry protofile="ResMall" messagename="MallCommodity" pbinname="MallCommodityConf" classname="MallCommodityConf"
        serverloadflag="ST_GameServer,ST_IdipServer,ST_MidasServer"
        splitsubtableflag="1" subtablename="MallCommodityConf_ActivityBigExchange,MallCommodityConf_ActivityChapterTaskExchange,MallCommodityConf_ActivityNormalExchange,MallCommodityConf_ActivitySpringFestival,
        MallCommodityConf_ActivityTakeawayExchange,MallCommodityConf_AvatarGift,
        MallCommodityConf_BackOrnament,MallCommodityConf_Common,MallCommodityConf_DirectBuy,
        MallCommodityConf_Emoji,MallCommodityConf_FaceOrnament,MallCommodityConf_Faces,MallCommodityConf_Gloves,
        MallCommodityConf_HandOrnament,MallCommodityConf_HeadWear,MallCommodityConf_Hot,
        MallCommodityConf_InteractiveProp,MallCommodityConf_Intimate,MallCommodityConf_Item,
        MallCommodityConf_LowerGarment,MallCommodityConf_MonthCard,MallCommodityConf_Movement,
        MallCommodityConf_PairMovement,MallCommodityConf_ReturnFriendshipFire,
        MallCommodityConf_SceneGiftPackage,MallCommodityConf_SeasonShop,MallCommodityConf_Suit,MallCommodityConf_UpperGarment,
        MallCommodityConf_Vehicle,MallCommodityConf_FarmItem,MallCommodityConf_Arena,
        MallCommodityConf_NR3E3,MallCommodityConf_NR3E3_DirectPurchase,
        MallCommodityConf_NR3E3_Decorate,MallCommodityConf_NR3E1_Decorate,MallCommodityConf_NR3E8,
        MallCommodityConf_WerewolfFullReduced,
        MallCommodityConf_ThemeMall,
        MallCommodityConf_VehicleUpgrade,
        MallCommodityConf_activity_commercialization,MallCommodityConf_activity_common,MallCommodityConf_activity_design,MallCommodityConf_activity_modyunying,MallCommodityConf_activity_yunying,
        MallCommodityConf_Farm,MallCommodityConf_JSCar,MallCommodityConf_Wolfkill,MallCommodityConf_MallArena,MallCommodityConf_Shuttle,
        MallCommodityConf_GroupingReturn,MallCommodityConf_AvatarBuy,MallCommodityConf_WeekendIceBroken,
        MallCommodityConf_RaffleDiamondExchange_main,MallCommodityConf_RaffleDiamondExchange_tesewanfa,MallCommodityConf_RaffleDiamondExchange_huoyue,MallCommodityConf_RaffleDiamondExchange_hedy,MallCommodityConf_RaffleDiamondExchange_miles,MallCommodityConf_RaffleDiamondExchange_karl,MallCommodityConf_RaffleDiamondExchange_silvester,MallCommodityConf_RaffleDiamondExchange_yujun,MallCommodityConf_DrawGift,MallCommodityConf_Chase"/>
    <entry protofile="ResMall" messagename="MallInfo" pbinname="MallInfoConf" classname="MallInfoConf"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResMall" messagename="SceneGiftPackageConfig" pbinname="SceneGiftPackageData" classname="SceneGiftPackageConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMall" messagename="ResMallSeasonShopTab" pbinname="ResMallSeasonShopTab" classname="MallSeasonShopTabData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMall" messagename="MallRecommendPage" pbinname="MallRecommendPageConf" classname="MallRecommendPageConf" serverloadflag="ST_GameServer" />
    <entry protofile="ResMall" messagename="ThemeMallInfoConfig" pbinname="ThemeMallInfoData" classname="ThemeMallInfoData" serverloadflag="ST_GameServer" />
    <entry protofile="ResGiveCard" messagename="GiveCardTemplateConf" pbinname="GiveCardTemplate" classname="MallGiftCardConf"
            serverloadflag="ST_GameServer" splitsubtableflag="1" subtablename="GiveCardTemplate_main"/>
    <entry protofile="ResQualifying" messagename="DegreeTypeCfg" pbinname="QDTDegreeTypeData" classname="QDTDegreeTypeData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_RoomServer,ST_UgcServer,ST_UgcplatServer,ST_BattleServer,ST_IdipServer,ST_StarproomServer,ST_StarpbattleServer"
        splitsubtableflag="1" subtablename="QDTDegreeTypeData_acm_daluandou,QDTDegreeTypeData_chase_dawangbiezhuawo,QDTDegreeTypeData_fps_tuweimenghuandao,QDTDegreeTypeData_fps_wuqidashi,QDTDegreeTypeData_js_jisufeiche,QDTDegreeTypeData_lightning_jiangbeizhengduo,QDTDegreeTypeData_main_zhuwanfa,QDTDegreeTypeData_nr3e_duomaomao,QDTDegreeTypeData_nr3e_sheishilangren,QDTDegreeTypeData_nr3e_wodixingdong,QDTDegreeTypeData_Moba,QDTDegreeTypeData_bnb_paopaodazhan,QDTDegreeTypeData_HOK" />
    <entry protofile="ResQualifying" messagename="QualifyingIntegral" pbinname="QDTQualifyingIntegralData" classname="QDTQualifyingIntegralData"
        serverloadflag="ST_GameServer,ST_IdipServer"
        splitsubtableflag="1" subtablename="QDTQualifyingIntegralData_acm_daluandou,QDTQualifyingIntegralData_chase_dawangbiezhuawo,QDTQualifyingIntegralData_fps_tuweimenghuandao,QDTQualifyingIntegralData_fps_wuqidashi,QDTQualifyingIntegralData_js_jisufeiche,QDTQualifyingIntegralData_lightning_jiangbeizhengduo,QDTQualifyingIntegralData_main_zhuwanfa,QDTQualifyingIntegralData_nr3e_duomaomao,QDTQualifyingIntegralData_nr3e_sheishilangren,QDTQualifyingIntegralData_nr3e_wodixingdong,QDTQualifyingIntegralData_Moba,QDTQualifyingIntegralData_bnb_paopaodazhan,QDTQualifyingIntegralData_HOK" />
    <entry protofile="ResQualifying" messagename="SeasonEspecialIntegral" pbinname="QDTEspecialIntegralData" classname="QDTEspecialIntegralData"
        serverloadflag="ST_GameServer,ST_IdipServer"
        splitsubtableflag="1" subtablename="QDTEspecialIntegralData_acm_daluandou,QDTEspecialIntegralData_chase_dawangbiezhuawo,QDTEspecialIntegralData_fps_tuweimenghuandao,QDTEspecialIntegralData_fps_wuqidashi,QDTEspecialIntegralData_js_jisufeiche,QDTEspecialIntegralData_lightning_jiangbeizhengduo,QDTEspecialIntegralData_main_zhuwanfa,QDTEspecialIntegralData_nr3e_duomaomao,QDTEspecialIntegralData_nr3e_sheishilangren,QDTEspecialIntegralData_nr3e_wodixingdong,QDTEspecialIntegralData_Moba,QDTEspecialIntegralData_bnb_paopaodazhan,QDTEspecialIntegralData_HOK" />
    <entry protofile="ResQualifying" messagename="SeasonCfg" pbinname="QDTSeasonCfgData" classname="QDTSeasonCfgData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_RoomServer,ST_UgcServer,ST_UgcplatServer,ST_BattleServer,ST_IdipServer,ST_ArenaServer,ST_RankServer,ST_StarproomServer,ST_StarpbattleServer"
        splitsubtableflag="1" subtablename="QDTSeasonCfgData_acm_daluandou,QDTSeasonCfgData_chase_dawangbiezhuawo,QDTSeasonCfgData_fps_tuweimenghuandao,QDTSeasonCfgData_fps_wuqidashi,QDTSeasonCfgData_js_jisufeiche,QDTSeasonCfgData_lightning_jiangbeizhengduo,QDTSeasonCfgData_main_zhuwanfa,QDTSeasonCfgData_nr3e_duomaomao,QDTSeasonCfgData_nr3e_sheishilangren,QDTSeasonCfgData_nr3e_wodixingdong,QDTSeasonCfgData_Moba,QDTSeasonCfgData_bnb_paopaodazhan,QDTSeasonCfgData_HOK" />
    <entry protofile="ResQualifying" messagename="QualifyingInheritInfo" pbinname="QDTQualifyingInheritData" classname="QDTQualifyingInheritData"
        serverloadflag="ST_GameServer,ST_IdipServer"
        splitsubtableflag="1" subtablename="QDTQualifyingInheritData_acm_daluandou,QDTQualifyingInheritData_chase_dawangbiezhuawo,QDTQualifyingInheritData_fps_tuweimenghuandao,QDTQualifyingInheritData_fps_wuqidashi,QDTQualifyingInheritData_js_jisufeiche,QDTQualifyingInheritData_lightning_jiangbeizhengduo,QDTQualifyingInheritData_main_zhuwanfa,QDTQualifyingInheritData_nr3e_duomaomao,QDTQualifyingInheritData_nr3e_sheishilangren,QDTQualifyingInheritData_nr3e_wodixingdong,QDTQualifyingInheritData_Moba,QDTQualifyingInheritData_bnb_paopaodazhan,QDTQualifyingInheritData_HOK" />
    <entry protofile="ResQualifying" messagename="SeasonMatchQualifyingLevelScore" pbinname="QualifyingLevelScoreData" classname="QualifyingLevelScoreData"
        serverloadflag="ST_GameServer,ST_IdipServer"
        splitsubtableflag="1" subtablename="QualifyingLevelScoreData_acm_daluandou,QualifyingLevelScoreData_chase_dawangbiezhuawo,QualifyingLevelScoreData_fps_tuweimenghuandao,QualifyingLevelScoreData_fps_wuqidashi,QualifyingLevelScoreData_js_jisufeiche,QualifyingLevelScoreData_lightning_jiangbeizhengduo,QualifyingLevelScoreData_main_zhuwanfa,QualifyingLevelScoreData_nr3e_duomaomao,QualifyingLevelScoreData_nr3e_sheishilangren,QualifyingLevelScoreData_nr3e_wodixingdong,QualifyingLevelScoreData_Moba,QualifyingLevelScoreData_bnb_paopaodazhan,QualifyingLevelScoreData_HOK" />
    <entry protofile="ResQualifying" messagename="QualifyRankKingDegreeInfo" pbinname="RankKingDegreeData" classname="RankKingDegreeData"
        serverloadflag="ST_GameServer,ST_IdipServer"
        splitsubtableflag="1" subtablename="RankKingDegreeData_acm_daluandou,RankKingDegreeData_chase_dawangbiezhuawo,RankKingDegreeData_fps_tuweimenghuandao,RankKingDegreeData_fps_wuqidashi,RankKingDegreeData_js_jisufeiche,RankKingDegreeData_lightning_jiangbeizhengduo,RankKingDegreeData_main_zhuwanfa,RankKingDegreeData_nr3e_duomaomao,RankKingDegreeData_nr3e_sheishilangren,RankKingDegreeData_nr3e_wodixingdong,RankKingDegreeData_Moba,RankKingDegreeData_bnb_paopaodazhan,RankKingDegreeData_HOK" />
    <entry protofile="ResQualifying" messagename="MatchDegreeTypeGroupConfig" pbinname="MatchDegreeTypeGroupData" classname="MatchDegreeTypeGroupData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_BattleServer,ST_ArenaServer,ST_StarproomServer,ST_StarpbattleServer" />
    <entry protofile="ResQualifying" messagename="QualifyingLevelDimensionConditionConfig" pbinname="QualifyingLevelDimensionConditionData" classname="QualifyingLevelDimensionConditionData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="QualifyingLevelDimensionConditionData_acm_daluandou,QualifyingLevelDimensionConditionData_chase_dawangbiezhuawo,QualifyingLevelDimensionConditionData_fps_tuweimenghuandao,QualifyingLevelDimensionConditionData_fps_wuqidashi,QualifyingLevelDimensionConditionData_js_jisufeiche,QualifyingLevelDimensionConditionData_lightning_jiangbeizhengduo,QualifyingLevelDimensionConditionData_main_zhuwanfa,QualifyingLevelDimensionConditionData_nr3e_duomaomao,QualifyingLevelDimensionConditionData_nr3e_sheishilangren,QualifyingLevelDimensionConditionData_nr3e_wodixingdong,QualifyingLevelDimensionConditionData_cups,QualifyingLevelDimensionConditionData_Moba,QualifyingLevelDimensionConditionData_bnb_paopaodazhan,QualifyingLevelDimensionConditionData_HOK" />
    <entry protofile="ResQualifying" messagename="SeasonMatchTypeQualifyDemensionInfo" pbinname="QualifyingLevelDimensionScoreData" classname="QualifyingLevelDimensionScoreData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="QualifyingLevelDimensionScoreData_acm_daluandou,QualifyingLevelDimensionScoreData_chase_dawangbiezhuawo,QualifyingLevelDimensionScoreData_fps_tuweimenghuandao,QualifyingLevelDimensionScoreData_fps_wuqidashi,QualifyingLevelDimensionScoreData_js_jisufeiche,QualifyingLevelDimensionScoreData_lightning_jiangbeizhengduo,QualifyingLevelDimensionScoreData_main_zhuwanfa,QualifyingLevelDimensionScoreData_nr3e_duomaomao,QualifyingLevelDimensionScoreData_nr3e_sheishilangren,QualifyingLevelDimensionScoreData_nr3e_wodixingdong,QualifyingLevelDimensionScoreData_Moba,QualifyingLevelDimensionScoreData_bnb_paopaodazhan,QualifyingLevelDimensionScoreData_HOK" />
    <entry protofile="ResQualifying" messagename="SeasonQualifyingMail" pbinname="SeasonQualifyingMailData" classname="SeasonQualifyingMailData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="SeasonQualifyingMailData_acm_daluandou,SeasonQualifyingMailData_chase_dawangbiezhuawo,SeasonQualifyingMailData_fps_tuweimenghuandao,SeasonQualifyingMailData_fps_wuqidashi,SeasonQualifyingMailData_js_jisufeiche,SeasonQualifyingMailData_lightning_jiangbeizhengduo,SeasonQualifyingMailData_main_zhuwanfa,SeasonQualifyingMailData_nr3e_duomaomao,SeasonQualifyingMailData_nr3e_sheishilangren,SeasonQualifyingMailData_nr3e_wodixingdong,SeasonQualifyingMailData_Moba,SeasonQualifyingMailData_bnb_paopaodazhan,SeasonQualifyingMailData_HOK" />
    <entry protofile="ResQualifying" messagename="QualifyingIntegralSettlementConfig" pbinname="QualifyingIntegralSettlementData" classname="QualifyingIntegralSettlementData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="QualifyingIntegralSettlementData_Lightning,QualifyingIntegralSettlementData_bnb" />
    <entry protofile="ResQualifying" messagename="SeasonQualifyingContinueWinConfig" pbinname="QualifyingContinueWinData" classname="QualifyingContinueWinData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="QualifyingContinueWinData_Lightning" />
    <entry protofile="ResQualifying" messagename="QualifyingPerfScoreConf" pbinname="QualifyingPerfScoreData" classname="QualifyingPerfScoreData"
           serverloadflag="ST_GameServer,ST_ArenaServer"
           splitsubtableflag="1" subtablename="QualifyingPerfScoreData_Moba,QualifyingPerfScoreData_HOK,QualifyingPerfScoreData_Chase" />
    <entry protofile="ResTask" messagename="TaskConf" pbinname="TaskConfData"
            classname="TaskConfData"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_ActivityServer,ST_IdipServer,ST_LobbyServer,ST_BattleServer,ST_StarproomServer"
            splitsubtableflag="1"
            subtablename="TaskConfData_UgcActivity,TaskConfData_achievement,TaskConfData_activity,TaskConfData_activitycommercialization,TaskConfData_activitydesign,TaskConfData_activityyunying,TaskConfData_modyunying,TaskConfData_back,TaskConfData_bp,TaskConfData_channel,TaskConfData_collection,TaskConfData_common,TaskConfData_download,TaskConfData_level,TaskConfData_mode,TaskConfData_newbie,TaskConfData_qrcode,TaskConfData_recharge,TaskConfData_season,TaskConfData_star,TaskConfData_ugc,TaskConfData_wxGameShare_WX,TaskConfData_season_lightning,TaskConfData_cup,TaskConfData_bp_nr3e_sheishilangren,TaskConfData_wxGameDayTask,TaskConfData_LuckyFriend,TaskConfData_bp_moba,TaskConfData_GuideToApp,TaskConfData_bp_main,TaskConfData_EntertainmentGuide,TaskConfData_noviceReward,TaskConfData_noviceSeven,TaskConfData_dye,TaskConfData_retrieve,TaskConfData_bp_maingame,TaskConfData_lobby,TaskConfData_arena_hero_star,TaskConfData_GameModeReturn,TaskConfData_chaseUnlock,TaskConfData_hok,TaskConfData_chase_daily"/>
    <entry protofile="ResGiftPackage" messagename="GiftPackage" pbinname="GiftPackageConfForLetsGo" classname="GiftPackageConf"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer"
        splitsubtableflag="1" subtablename="GiftPackageConfForLetsGo_achievement,GiftPackageConfForLetsGo_giftpack,GiftPackageConfForLetsGo_leveldrop,GiftPackageConfForLetsGo_luckymoney,GiftPackageConfForLetsGo_scratchofftickets,GiftPackageConfForLetsGo_task" />
    <entry protofile="ResMailTemplate" messagename="MailTemplateConfData" pbinname="MailTemplateConfData" classname="MailTemplateConfData"
        serverloadflag="ST_GameServer,ST_XiaowoServer,ST_ClubServer,ST_ActivityServer,ST_FarmServer,ST_CocServer" splitsubtableflag="1" subtablename="MailTemplateConfData,MailTemplateConfData_coc"/>
    <entry protofile="ResMailTemplate" messagename="GlobalMailConfData" pbinname="GlobalMailConfData" classname="GlobalMailConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMisc" messagename="MiscConf" pbinname="MiscConfForLetsGo"
            classname="MiscConf" splitsubtableflag="1"
            subtablename="MiscConfForLetsGo,MiscConfForLetsGo_card,MiscConfForLetsGo_chat"
            serverloadflag="ST_GameServer,ST_BattleServer,ST_RoomServer,ST_RankServer,ST_UgcdatastoreServer,ST_ChatServer,ST_CocServer,ST_StarproomServer,ST_UgcServer"/>
    <entry protofile="ResMailIni" messagename="MailIniConfData" pbinname="MailIniConfForLetsGo" classname="MailIniConfData"
        serverloadflag="ST_GameServer,ST_IdipServer,ST_XiaowoServer,ST_ClubServer,ST_ActivityServer,ST_FarmServer,ST_CocServer" splitsubtableflag="1" subtablename="MailIniConfForLetsGo, MailIniConfForLetsGo_coc"/>
    <entry protofile="ResGeneral" messagename="FeatureOpenConf" pbinname="FeatureOpenConfData" classname="FeatureOpenConfData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_UgcappServer,ST_ActivityServer,ST_ClubServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer" />
    <entry protofile="ResGeneral" messagename="FeatureKeySwitchConf" pbinname="FeatureKeySwitchConf" classname="FeatureKeySwitchConf"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_UgcappServer,ST_ActivityServer,ST_ClubServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer"
        rainbowenvdefault="0" rainbowgroup="feature_switch" />
    <entry protofile="ResGeneralOutput" messagename="ItemReasonFilter" pbinname="ItemReasonFilter" classname="ItemReasonFilterConf"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResGeneralOutput" messagename="ItemOutputLimit" pbinname="ItemOutputLimit" classname="ItemOutputLimitConf"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResGeneral" messagename="VersionCompCtrlConf" pbinname="VersionCompCtrlConfData" classname="VersionCompCtrlConf"
        serverloadflag="ST_DirServer,ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_LobbyServer,ST_RoomServer,ST_StarproomServer,ST_MatchServer,ST_StarproommatchServer,ST_XiaowoServer,ST_FarmServer,ST_UgcplatServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer"
        rainbowenvdefault="0" rainbowgroup="VersionCompCtrlConf" />
    <entry protofile="ResGeneral" messagename="VersionCompBattleConf" pbinname="VersionCompBattleConfData" classname="VersionCompBattleConf"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_LobbyServer,ST_RoomServer,ST_StarproomServer,ST_MatchServer,ST_StarproommatchServer,ST_XiaowoServer,ST_FarmServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer"
        rainbowenvdefault="0" rainbowgroup="VersionCompBattleConf"
        splitsubtableflag="1" subtablename="VersionCompBattleConfData_acm,VersionCompBattleConfData_chase,VersionCompBattleConfData_fps,VersionCompBattleConfData_js,VersionCompBattleConfData_lobby,VersionCompBattleConfData_main,VersionCompBattleConfData_nr3e,VersionCompBattleConfData_tyc,VersionCompBattleConfData_ugc,VersionCompBattleConfData_xiaowo,VersionCompBattleConfData_farm, VersionCompBattleConfData_arena,VersionCompBattleConfData_mayday,VersionCompBattleConfData_coc" />
    <entry protofile="ResGeneral" messagename="VersionCompAdjustmentConf" pbinname="VersionCompAdjustmentConf" classname="VersionCompAdjustmentConf"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer"
        rainbowenvdefault="0" rainbowgroup="VersionCompAdjustmentConf" />
    <entry protofile="ResGeneral" messagename="VersionCompFeatureConf" pbinname="VersionCompFeatureConf" classname="VersionCompFeatureConf"
           serverloadflag="ST_GameServer,ST_BattleServer,ST_RoomServer,ST_FarmServer,ST_XiaowoServer,ST_UgcplatServer,ST_StarpbattleServer,ST_StarproomServer"
        rainbowenvdefault="0" rainbowgroup="VersionCompFeatureConf"
        splitsubtableflag="1" subtablename="VersionCompFeatureConf,VersionCompFeatureConf_JS,VersionCompFeatureConf_Farm,VersionCompFeatureConf_Arena,VersionCompFeatureConf_MAYDAY,VersionCompFeatureConf_COC"/>
    <entry protofile="ResGeneral" messagename="VersionCompRangeConf" pbinname="VersionCompRangeConfData" classname="VersionCompRangeConfData"
        serverloadflag="ST_DirServer,ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_LobbyServer,ST_RoomServer,ST_StarproomServer,ST_MatchServer,ST_StarproommatchServer,ST_XiaowoServer,ST_FarmServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer"
        rainbowenvdefault="0" rainbowgroup="VersionCompRangeConf" />
    <entry protofile="ResGeneral" messagename="GamePlayUpdateConf" pbinname="GamePlayUpdateConf" classname="GamePlayUpdateConf"
        serverloadflag="ST_GameServer,ST_ConfigServer"
        rainbowenvdefault="0" rainbowgroup="GamePlayUpdateConf"
        splitsubtableflag="1" subtablename="GamePlayUpdateConf,GamePlayUpdateConf_Main,GamePlayUpdateConf_JS,GamePlayUpdateConf_Farm,GamePlayUpdateConf_Arena,GamePlayUpdateConf_MAYDAY,GamePlayUpdateConf_COC"/>
    <entry protofile="ResActivity" messagename="ActivityPrayerCardCollectionConfig" pbinname="ActivityPrayerCardCollectionConfigData" classname="ActivityPrayerCardCollectionConfigData"
            serverloadflag="ST_GameServer,ST_LobbyServer,ST_BattleServer" />
    <entry protofile="ResActivity" messagename="ActivityPrayerCardConfig" pbinname="ActivityPrayerCardConfigData" classname="ActivityPrayerCardConfigData"
            serverloadflag="ST_GameServer,ST_LobbyServer,ST_BattleServer" />
    <entry protofile="ResActivity" messagename="ActivityPrayerCardWeightConfig" pbinname="ActivityPrayerCardWeightConfigData" classname="ActivityPrayerCardWeightConfigData"
            serverloadflag="ST_GameServer,ST_LobbyServer,ST_BattleServer" />
    <entry protofile="ResActivity" messagename="ActivityPrayerCardMiscData" pbinname="ActivityPrayerCardMiscData" classname="ActivityPrayerCardMiscDataConf"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="ActivityPrayerCardRewardLimitData" pbinname="ActivityPrayerCardRewardLimitData" classname="ActivityPrayerCardRewardLimitData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="table_ActivityMainConfig" pbinname="ActivityMainConfigForLetsGo" classname="ActivityMainConfig"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_IdipServer,ST_ActivityServer,ST_LobbyServer,ST_BattleServer,ST_MidasServer,ST_StarproomServer"
        splitsubtableflag="1" subtablename="ActivityMainConfigForLetsGo_UgcActivity,ActivityMainConfigForLetsGo_common,ActivityMainConfigForLetsGo_commercialization,ActivityMainConfigForLetsGo_design,ActivityMainConfigForLetsGo_recharge,ActivityMainConfigForLetsGo_yunying,ActivityMainConfigForLetsGo_modyunying,ActivityMainConfigForLetsGo_noviceSeven,ActivityMainConfigForLetsGo_card,ActivityMainConfigForLetsGo_hok" />
    <entry protofile="ResActivity" messagename="ActivityGopenIdWhiteListConf" pbinname="ActivityGopenIdWhiteListConfData" classname="ActivityGopenIdWhiteListConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="UltramanThemeConfig" pbinname="UltramanThemeConfigData" classname="UltramanThemeConfigData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="UltramanThemeCollectionConfig" pbinname="UltramanThemeCollectionConfigData" classname="UltramanThemeCollectionConfigData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="ActivityChapterTaskConfig" pbinname="ActivityChapterTaskData" classname="ActivityChapterTaskData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="TimeLimitedCheckInActivityConf" pbinname="TimeLimitedCheckInActivityConfData" classname="TimeLimitedCheckInActivityConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="AccumulateBlessingsActivityConf" pbinname="AccumulateBlessingsActivityConfData" classname="AccumulateBlessingsActivityConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="SpringBlessingCollectionConf" pbinname="SpringBlessingCollectionConfData" classname="SpringBlessingCollectionConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="SpringBlessingCardRewardConf" pbinname="SpringBlessingCardRewardConfData" classname="SpringBlessingCardRewardConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="SpringBlessingCardConf" pbinname="SpringBlessingCardConfData" classname="SpringBlessingCardConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="SpringBlessingSponsorConf" pbinname="SpringBlessingSponsorConfData" classname="SpringBlessingSponsorConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="CompetitionWarmUpActivityConf" pbinname="CompetitionWarmUpActivityConfData" classname="CompetitionWarmUpActivityConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="CompetitionWarmUpScoreConf" pbinname="CompetitionWarmUpScoreConfData" classname="CompetitionWarmUpScoreConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="DanceOutfitConfig" pbinname="DanceOutfitConfData" classname="DanceOutfitConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="DanceOutfitKeyword" pbinname="DanceOutfitKeyword" classname="DanceOutfitKeywordData"
            serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="DanceOutfitItemConf" pbinname="DanceOutfitItemConfData" classname="DanceOutfitItemConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivityCaptureShadow" messagename="CaptureShadowShoeConfig" pbinname="CaptureShadowShoeData" classname="CaptureShadowShoeData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivityCaptureShadow" messagename="CaptureShadowRewardConfig" pbinname="CaptureShadowRewardData" classname="CaptureShadowRewardData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivityCaptureShadow" messagename="CaptureShadowMapConfig" pbinname="CaptureShadowMapData" classname="CaptureShadowMapData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>

    <entry protofile="ResActivityWolfReturn" messagename="ActivityWolfReturnRewardConfig" pbinname="ActivityWolfReturnRewardData" classname="ActivityWolfReturnRewardData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivityWolfReturn" messagename="ActivityWolfReturnConditionConfig" pbinname="ActivityWolfReturnConditionData" classname="ActivityWolfReturnConditionData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>

    <entry protofile="ResActivityQingShuangTrial" messagename="ActivityQingShuangTrialConfig" pbinname="ActivityQingShuangTrialData" classname="ActivityQingShuangTrialData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>

    <entry protofile="ResActivityFlashRaceCheering" messagename="ActivityActivityFlashRaceCheeringTimeConfig" pbinname="ActivityActivityFlashRaceCheeringTimeData" classname="ActivityActivityFlashRaceCheeringTimeData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivityFlashRaceCheering" messagename="ActivityActivityFlashRaceCheeringConfig" pbinname="ActivityActivityFlashRaceCheeringData" classname="ActivityActivityFlashRaceCheeringData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivityFlashRaceCheering" messagename="FlashRaceCheeringTimeAiConfig" pbinname="FlashRaceCheeringTimeAiData" classname="FlashRaceCheeringTimeAiData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivityFlashRaceCheering" messagename="FlashRaceCheeringAiConfig" pbinname="FlashRaceCheeringAiData" classname="FlashRaceCheeringAiData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>

    <entry protofile="ResActivity" messagename="ThemeAdventureRewardUpgradeConf" pbinname="ThemeAdventureRewardUpgradeConfData" classname="ThemeAdventureRewardUpgradeConfData"
            serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="ActivityRechargeRecommendConfig" pbinname="ActivityRechargeRecommendData" classname="ActivityRechargeRecommendData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivityWishesCameTrue" messagename="WishesCameTrueGiftConf" pbinname="WishesCameTrueGiftConf" classname="WishesCameTrueGiftConfig"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivityFunPara" messagename="ActivityFunParaConfig" pbinname="ActivityFunParaData" classname="ActivityFunParaConfig"
        serverloadflag="ST_GameServer,ST_ActivityServer,ST_IdipServer" />
    <entry protofile="ResActivityFunPara" messagename="ActivityMianGanConfig" pbinname="ActivityMianGanData" classname="ActivityMianGanData"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivityWishesCameTrue" messagename="WishesCameTrueInvitationRewardsConf" pbinname="WishesCameTrueInvitationRewardsConf" classname="WishInviteRewardCfg"
        serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResMisc" messagename="GMCmdConf" pbinname="GMCmdConfForLetsGo" classname="GMCmdConf"
           serverloadflag="ST_GameServer"
           splitsubtableflag="1"
           subtablename="GMCmdConfForLetsGo,GMCmdConfForLetsGo_Hok"/>
    <entry protofile="ResLevelInfo" messagename="T_LevelInfoData" pbinname="LevelInfoData" classname="LevelInfoData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_RoomServer,ST_StarpbattleServer,ST_StarproomServer"
        splitsubtableflag="1" subtablename="LevelInfoData_AC,LevelInfoData_AI,LevelInfoData_BS,LevelInfoData_Chase,LevelInfoData_FPS,LevelInfoData_JS,LevelInfoData_MAYDAY,LevelInfoData_Metro,LevelInfoData_OMD,LevelInfoData_arena,LevelInfoData_dnd,LevelInfoData_main,LevelInfoData_nr3e,LevelInfoData_ugc,LevelInfoData_HOK,LevelInfoData_lighting,LevelInfoData_coc,LevelInfoData_FB" />
    <entry protofile="ResLevelInfo" messagename="T_LevelUGCInfoData" pbinname="LevelUGCInfoData" classname="LevelUGCInfoData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResChat" messagename="ChatSystemMsgConf" pbinname="ChatSystemMsgConfDataForLetsGo" classname="ChatSystemMsgConfData"
        serverloadflag="ST_GameServer,ST_ChatServer,ST_ClubServer,ST_BattleServer,ST_StarpbattleServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResScene" messagename="SceneConfig" pbinname="SceneConfigData" classname="SceneConfigData"
        serverloadflag="ST_SceneServer" />
    <entry protofile="ResScene" messagename="ScenePlayConfig" pbinname="ScenePlayConfigData" classname="ScenePlayConfigData"
        serverloadflag="ST_SceneServer" />
    <entry protofile="ResScene" messagename="SceneRandomBirthPos" pbinname="SceneRandomBirthPosData" classname="SceneRandomBirthPosData"
        serverloadflag="ST_SceneServer" />
    <entry protofile="ResScene" messagename="SceneMapBirthConfig" pbinname="SceneMapBirthConfigData" classname="SceneMapBirthConfigData"
        serverloadflag="ST_SceneServer" />
    <entry protofile="ResPlayerLevel" messagename="PlayerLevelConf" pbinname="PlayerLevelConfData" classname="PlayerLevelConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResTask" messagename="TaskGroup" pbinname="TaskGroupData"
            classname="TaskGroupData"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_ActivityServer,ST_IdipServer,ST_LobbyServer,ST_BattleServer,ST_StarproomServer"
            splitsubtableflag="1"
            subtablename="TaskGroupData_UgcActivity,TaskGroupData_activity,TaskGroupData_activitycommercialization,TaskGroupData_activitydesign,TaskGroupData_activityyunying,TaskGroupData_modyunying,TaskGroupData_common,TaskGroupData_recharge,TaskGroupData_wxGameShare_WX,TaskGroupData_common_lightning,TaskGroupData_bp_nr3e_sheishilangren,TaskGroupData_cup,TaskGroupData_wxGameDayTask,TaskGroupData_bp_moba,TaskGroupData_GuideToApp,TaskGroupData_bp_main,TaskGroupData_EntertainmentGuide,TaskGroupData_noviceReward,TaskGroupData_noviceSeven,TaskGroupData_retrieve,TaskGroupData_bp_maingame,TaskGroupData_arena_hero_star,TaskGroupData_GameModeReturn,TaskGroupData_chaseUnlock,TaskGroupData_hok,TaskGroupData_chase_daily"/>
    <entry protofile="ResSeason" messagename="SeasonConfData" pbinname="SeasonConfData" classname="SeasonConfData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_UgcplatServer,ST_UgcServer,ST_RoomServer,ST_BattleServer,ST_IdipServer,ST_ClubServer,ST_MidasServer,ST_ArenaServer,ST_StarproomServer,ST_StarpbattleServer" />
    <entry protofile="ResSeason" messagename="SeasonFashionMetaBattleDataConf" pbinname="SeasonFashionMetaBattleDataConfData" classname="SeasonFashionMetaBattleDataConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResSeason" messagename="SeasonFashionBattleDataConf" pbinname="SeasonFashionBattleDataConfData" classname="SeasonFashionBattleDataConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResSeason" messagename="SeasonFashionModelDataConf" pbinname="SeasonFashionModelDataConfData" classname="SeasonFashionModelDataConfData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResSeason" messagename="SeasonReviewMetaDataConf" pbinname="SeasonReviewMetaConf" classname="SeasonReviewMetaConf"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResSeason" messagename="PlayModeSeasonConf" pbinname="PlayModeSeasonConfData" classname="PlayModeSeasonConfData"
            serverloadflag="ST_GameServer,ST_IdipServer,ST_MidasServer,ST_RankServer,ST_RoomServer,ST_UgcServer,ST_UgcplatServer,ST_BattleServer,ST_ArenaServer,ST_StarproomServer,ST_StarpbattleServer" />
    <entry protofile="ResRanking" messagename="RankingDisplayConf" pbinname="RankingDisplayConfData" classname="RankingDisplayConfData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_BattleServer,ST_IdipServer,ST_ClubServer,ST_StarpbattleServer" />
    <entry protofile="ResRanking" messagename="RankingConf" pbinname="RankingConfData"
            classname="RankingConfData"
            serverloadflag="ST_GameServer,ST_RankServer,ST_BattleServer,ST_IdipServer,ST_ActivityServer,ST_ClubServer,ST_StarpbattleServer"
            splitsubtableflag="1"
            subtablename="RankingConfData_Activity,RankingConfData_Common,RankingConfData_Level,RankingConfData_PlayMode,RankingConfData_Qualify,RankingConfData_Ugc,RankingConfData_Cup,RankingConfData_Card,RankingConfData_LevelOne,RankingConfData_LevelTwo,RankingConfData_LevelFour"/>
    <entry protofile="ResRanking" messagename="RankingRewardConf" pbinname="RankingRewardConfData" classname="RankingRewardConfData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="RankingRewardConfData_Activity" />
    <entry protofile="ResRanking" messagename="RankingSnapshotRewardConf" pbinname="RankingSnapshotRewardConfData" classname="RankingSnapshotRewardConfData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_IdipServer,ST_ClubServer"
        splitsubtableflag="1" subtablename="RankingSnapshotRewardConfData_Common,RankingSnapshotRewardConfData_Lightning" />
    <entry protofile="ResRanking" messagename="RankingRuleConf" pbinname="RankingRuleConfData" classname="RankingRuleConfData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_BattleServer,ST_IdipServer,ST_ClubServer,ST_ActivityServer,ST_StarpbattleServer" />
    <entry protofile="ResCondition" messagename="SubConditionParamList" pbinname="SubConditionParamListData" classname="SubConditionParamListData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRecruitTopic" messagename="RecruitTopic" pbinname="RecruitTopicData" classname="RecruitTopicData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResDsConfig" messagename="DSAConfig" pbinname="DSAConfig" classname="DSAConfigData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer,ST_LobbyServer,ST_XiaowoServer,ST_FarmServer,ST_StarpServer,ST_DscallocServer,ST_DebugdsmgrServer,ST_StarpgroupServer" />
    <entry protofile="ResDsConfig" messagename="DSDevConfig" pbinname="DSDevConfig" classname="DSDevConfigData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_LobbyServer,ST_XiaowoServer,ST_FarmServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer" />
    <entry protofile="ResDsConfig" messagename="DSLogConfig" pbinname="DSLogConfig" classname="DSLogConfigData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_LobbyServer,ST_XiaowoServer,ST_FarmServer,ST_StarpbattleServer,ST_StarpServer,ST_DebugdsmgrServer,ST_StarpgroupServer" />
    <entry protofile="ResRaffle" messagename="RaffleAccessCfg" pbinname="RaffleAccessCfgData"
            classname="RaffleAccessCfgData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1" subtablename="
            RaffleAccessCfgData_Accessories_main,RaffleAccessCfgData_Accessories_tesewanfa,RaffleAccessCfgData_Accessories_huoyue,RaffleAccessCfgData_Accessories_hedy,RaffleAccessCfgData_Accessories_miles,RaffleAccessCfgData_Accessories_karl,RaffleAccessCfgData_Accessories_silvester,RaffleAccessCfgData_Accessories_yujun,
            RaffleAccessCfgData_Card_main,RaffleAccessCfgData_Card_tesewanfa,RaffleAccessCfgData_Card_huoyue,RaffleAccessCfgData_Card_hedy,RaffleAccessCfgData_Card_miles,RaffleAccessCfgData_Card_karl,RaffleAccessCfgData_Card_silvester,RaffleAccessCfgData_Card_yujun,
            RaffleAccessCfgData_Common_main,RaffleAccessCfgData_Common_tesewanfa,RaffleAccessCfgData_Common_huoyue,RaffleAccessCfgData_Common_hedy,RaffleAccessCfgData_Common_miles,RaffleAccessCfgData_Common_karl,RaffleAccessCfgData_Common_silvester,RaffleAccessCfgData_Common_yujun,
            RaffleAccessCfgData_Disk_main,RaffleAccessCfgData_Disk_tesewanfa,RaffleAccessCfgData_Disk_huoyue,RaffleAccessCfgData_Disk_hedy,RaffleAccessCfgData_Disk_miles,RaffleAccessCfgData_Disk_karl,RaffleAccessCfgData_Disk_silvester,RaffleAccessCfgData_Disk_yujun,
            RaffleAccessCfgData_Iaa_main,RaffleAccessCfgData_Iaa_tesewanfa,RaffleAccessCfgData_Iaa_huoyue,RaffleAccessCfgData_Iaa_hedy,RaffleAccessCfgData_Iaa_miles,RaffleAccessCfgData_Iaa_karl,RaffleAccessCfgData_Iaa_silvester,RaffleAccessCfgData_Iaa_yujun,
            RaffleAccessCfgData_MonthActivity_main,RaffleAccessCfgData_MonthActivity_tesewanfa,RaffleAccessCfgData_MonthActivity_huoyue,RaffleAccessCfgData_MonthActivity_hedy,RaffleAccessCfgData_MonthActivity_miles,RaffleAccessCfgData_MonthActivity_karl,RaffleAccessCfgData_MonthActivity_silvester,RaffleAccessCfgData_MonthActivity_yujun,
            RaffleAccessCfgData_Premium_main,RaffleAccessCfgData_Premium_tesewanfa,RaffleAccessCfgData_Premium_huoyue,RaffleAccessCfgData_Premium_hedy,RaffleAccessCfgData_Premium_miles,RaffleAccessCfgData_Premium_karl,RaffleAccessCfgData_Premium_silvester,RaffleAccessCfgData_Premium_yujun,
            RaffleAccessCfgData_Purple_main,RaffleAccessCfgData_Purple_tesewanfa,RaffleAccessCfgData_Purple_huoyue,RaffleAccessCfgData_Purple_hedy,RaffleAccessCfgData_Purple_miles,RaffleAccessCfgData_Purple_karl,RaffleAccessCfgData_Purple_silvester,RaffleAccessCfgData_Purple_yujun,
            RaffleAccessCfgData_SocialFission_main,RaffleAccessCfgData_SocialFission_tesewanfa,RaffleAccessCfgData_SocialFission_huoyue,RaffleAccessCfgData_SocialFission_hedy,RaffleAccessCfgData_SocialFission_miles,RaffleAccessCfgData_SocialFission_karl,RaffleAccessCfgData_SocialFission_silvester,RaffleAccessCfgData_SocialFission_yujun,
            RaffleAccessCfgData_Vehicle_main,RaffleAccessCfgData_Vehicle_tesewanfa,RaffleAccessCfgData_Vehicle_huoyue,RaffleAccessCfgData_Vehicle_hedy,RaffleAccessCfgData_Vehicle_miles,RaffleAccessCfgData_Vehicle_karl,RaffleAccessCfgData_Vehicle_silvester,RaffleAccessCfgData_Vehicle_yujun"/>
    <entry protofile="ResRaffle" messagename="RaffleCfg" pbinname="RaffleCfgData"
            classname="RaffleCfgData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="
            RaffleCfgData_Accessories_main,RaffleCfgData_Accessories_tesewanfa,RaffleCfgData_Accessories_huoyue,RaffleCfgData_Accessories_hedy,RaffleCfgData_Accessories_miles,RaffleCfgData_Accessories_karl,RaffleCfgData_Accessories_silvester,RaffleCfgData_Accessories_yujun,
            RaffleCfgData_Card_main,RaffleCfgData_Card_tesewanfa,RaffleCfgData_Card_huoyue,RaffleCfgData_Card_hedy,RaffleCfgData_Card_miles,RaffleCfgData_Card_karl,RaffleCfgData_Card_silvester,RaffleCfgData_Card_yujun,
            RaffleCfgData_Common_main,RaffleCfgData_Common_tesewanfa,RaffleCfgData_Common_huoyue,RaffleCfgData_Common_hedy,RaffleCfgData_Common_miles,RaffleCfgData_Common_karl,RaffleCfgData_Common_silvester,RaffleCfgData_Common_yujun,
            RaffleCfgData_Disk_main,RaffleCfgData_Disk_tesewanfa,RaffleCfgData_Disk_huoyue,RaffleCfgData_Disk_hedy,RaffleCfgData_Disk_miles,RaffleCfgData_Disk_karl,RaffleCfgData_Disk_silvester,RaffleCfgData_Disk_yujun,
            RaffleCfgData_Iaa_main,RaffleCfgData_Iaa_tesewanfa,RaffleCfgData_Iaa_huoyue,RaffleCfgData_Iaa_hedy,RaffleCfgData_Iaa_miles,RaffleCfgData_Iaa_karl,RaffleCfgData_Iaa_silvester,RaffleCfgData_Iaa_yujun,
            RaffleCfgData_MonthActivity_main,RaffleCfgData_MonthActivity_tesewanfa,RaffleCfgData_MonthActivity_huoyue,RaffleCfgData_MonthActivity_hedy,RaffleCfgData_MonthActivity_miles,RaffleCfgData_MonthActivity_karl,RaffleCfgData_MonthActivity_silvester,RaffleCfgData_MonthActivity_yujun,
            RaffleCfgData_Premium_main,RaffleCfgData_Premium_tesewanfa,RaffleCfgData_Premium_huoyue,RaffleCfgData_Premium_hedy,RaffleCfgData_Premium_miles,RaffleCfgData_Premium_karl,RaffleCfgData_Premium_silvester,RaffleCfgData_Premium_yujun,
            RaffleCfgData_Purple_main,RaffleCfgData_Purple_tesewanfa,RaffleCfgData_Purple_huoyue,RaffleCfgData_Purple_hedy,RaffleCfgData_Purple_miles,RaffleCfgData_Purple_karl,RaffleCfgData_Purple_silvester,RaffleCfgData_Purple_yujun,
            RaffleCfgData_SocialFission_main,RaffleCfgData_SocialFission_tesewanfa,RaffleCfgData_SocialFission_huoyue,RaffleCfgData_SocialFission_hedy,RaffleCfgData_SocialFission_miles,RaffleCfgData_SocialFission_karl,RaffleCfgData_SocialFission_silvester,RaffleCfgData_SocialFission_yujun,
            RaffleCfgData_Vehicle_main,RaffleCfgData_Vehicle_tesewanfa,RaffleCfgData_Vehicle_huoyue,RaffleCfgData_Vehicle_hedy,RaffleCfgData_Vehicle_miles,RaffleCfgData_Vehicle_karl,RaffleCfgData_Vehicle_silvester,RaffleCfgData_Vehicle_yujun"/>
    <entry protofile="ResRaffle" messagename="RaffleRewardCfg" pbinname="RaffleRewardCfgData"
            classname="RaffleRewardCfgData"
            serverloadflag="ST_GameServer" splitsubtableflag="1"
            subtablename="RaffleRewardCfgData_main,RaffleRewardCfgData_tesewanfa,RaffleRewardCfgData_huoyue,RaffleRewardCfgData_hedy,RaffleRewardCfgData_miles,RaffleRewardCfgData_karl,RaffleRewardCfgData_silvester,RaffleRewardCfgData_yujun"/>
    <entry protofile="ResRaffle" messagename="RaffleMajorGDrawCfg" pbinname="RaffleMajorGDrawData"
            classname="RaffleMajorGDrawData"
            serverloadflag="ST_GameServer" splitsubtableflag="1"
            subtablename="RaffleMajorGDrawData_main,RaffleMajorGDrawData_tesewanfa,RaffleMajorGDrawData_huoyue,RaffleMajorGDrawData_hedy,RaffleMajorGDrawData_miles,RaffleMajorGDrawData_karl,RaffleMajorGDrawData_silvester,RaffleMajorGDrawData_yujun"/>
    <entry protofile="ResRaffle" messagename="RaffleLuckyValueCfg" pbinname="RaffleLuckyValueData"
            classname="RaffleLuckyValueData"
            serverloadflag="ST_GameServer" splitsubtableflag="1"
            subtablename="RaffleLuckyValueData_main,RaffleLuckyValueData_tesewanfa,RaffleLuckyValueData_huoyue,RaffleLuckyValueData_hedy,RaffleLuckyValueData_miles,RaffleLuckyValueData_karl,RaffleLuckyValueData_silvester,RaffleLuckyValueData_yujun"/>
    <entry protofile="ResDsConfig" messagename="DSRegionConfig" pbinname="DSRegionConfig" classname="DSRegionConfigData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer,ST_DscallocServer" rainbowenvdefault="0" rainbowgroup="DSRegionConfig"/>
    <entry protofile="ResActivity" messagename="LuckyMoneyInfo" pbinname="LuckyMoneyInfoData" classname="LuckyMoneyInfoData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="WealthBankConfig" pbinname="WealthBankConfigData" classname="WealthBankConfigData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="WealthBankCheckInReward" pbinname="WealthBankCheckInRewardData" classname="WealthBankCheckInRewardData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivityInterServerGift" messagename="InterServerGiftConfig" pbinname="InterServerGiftConfigData" classname="InterServerGiftConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRecharge" messagename="RechargeConfInfo" pbinname="RechargeConfData" classname="RechargeConfData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="RechargeConfData_Android,RechargeConfData_Ios" />
    <entry protofile="ResRecharge" messagename="RechargeNewConfInfo" pbinname="RechargeNewConfInfo" classname="RechargeNewConfInfo"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1" subtablename="RechargeNewConfInfo_Android,RechargeNewConfInfo_Ios,RechargeNewConfInfo_Chaohe" />
    <entry protofile="ResRecharge" messagename="RechargeLevelConfInfo" pbinname="RechargeLevelConfData" classname="RechargeLevelConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRecharge" messagename="RechargeDepositCfg" pbinname="RechargeDepositData" classname="RechargeDepositData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRecharge" messagename="RechargeScratchOffTicketCfg" pbinname="RechargeScratchOffTicketData" classname="RechargeScratchOffTicketData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUGCEditor" messagename="Item_UGCEditorMapTemplate" pbinname="UGCEditorMapTemplate" classname="UGCEditorMapTemplate"
        serverloadflag="ST_GameServer,ST_UgcServer,ST_UgcplatServer,ST_UgcappServer,ST_BattleServer" />
    <entry protofile="ResUGCEditor" messagename="UGCMapCommonConf" pbinname="UGCMapCommonConf" classname="UGCMapCommonConf"
        serverloadflag="ST_GameServer,ST_UgcServer,ST_UgcplatServer,ST_RoomServer,ST_StarproomServer,ST_UgcappServer,ST_AigcServer" />
    <entry protofile="ResRecharge" messagename="RechargeMonthCardData" pbinname="RechargeMonthCardData" classname="RechargeMonthCardData"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResMidas" messagename="MidasConf" pbinname="MidasProductConfData"
            classname="MidasConfData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="MidasProductConfData_Charge,MidasProductConfData_ItemBuy,MidasProductConfData_bp_nr3e_sheishilangren,MidasProductConfData_bp_moba,MidasProductConfData_bp_main,MidasProductConfData_bp_maingame"/>
    <entry protofile="ResMidas" messagename="MidasModuleConf" pbinname="MidasModuleConf" classname="MidasModuleConfData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivitySquad" messagename="SquadActivity" pbinname="SquadActivityData" classname="SquadActivityData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResActivitySquad" messagename="MultiPlayerSquad" pbinname="MultiPlayerSquadData" classname="MultiPlayerSquadData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResActivityTeam" messagename="ActivityTeam" pbinname="ActivityTeamData" classname="ActivityTeamConfig"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResBackpack" messagename="CloakroomUnlock" pbinname="BackpackCloakroomUnlock" classname="FittingSlotUnlockConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResBackpack" messagename="BackpackSundryConfig" pbinname="BackpackSundryConfig" classname="BackpackSundryConfigData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResSuit" messagename="SuitInfo" pbinname="SuitInfoData" classname="SuitInfoData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRecharge" messagename="FirstChargeResConf" pbinname="ResFirstChargeConfData" classname="FirstChargeConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUGCEditor" messagename="Item_UGCEditorMapTag" pbinname="UGCEditorMapTag" classname="UGCEditorMapTag"
        serverloadflag="ST_GameServer,ST_UgcServer" />
    <entry protofile="ResLevelRoundRandomRule" messagename="T_LevelRoundRandomRuleData" pbinname="LevelRoundRandomRuleData" classname="LevelRoundRandomRuleData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResLevelRoundRandomRule" messagename="LevelRoundDispersionConf" pbinname="LevelRoundDispersionConfData" classname="LevelRoundDispersionConfData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcUploadConfig" pbinname="UgcUploadConfig" classname="UgcUploadConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResUgcMgr" messagename="T_UgcRouteInsConfig" pbinname="UgcRouteInsConfig" classname="UgcRouteInsConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResUgcMgr" messagename="T_UgcRoutePolarisInsConfig" pbinname="UgcRoutePolarisInsConfig" classname="UgcRoutePolarisInsConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcAiImageWhiteListConfig" classname="UgcAiImageWhiteListConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResUgcMgr" messagename="T_UgcCommandUrlConfig" pbinname="UgcCommandUrlConfig"
            classname="UgcCommandUrlConfig"
            serverloadflag="ST_UgcplatServer,ST_UgcdatastoreServer,ST_HttpServer"/>
    <entry protofile="ResActivityCheckInPlan" messagename="table_CheckInPlanActivityWeekConf" pbinname="CheckInPlanActivityWeekConfData" classname="CheckInPlanActivityWeekConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityCheckInPlan" messagename="table_CheckInPlanActivityDrawConf" pbinname="CheckInPlanActivityDrawConfData" classname="CheckInPlanActivityDrawConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResSpecReward" messagename="SpecRewardConfig" pbinname="SpecRewardConfig" classname="SpecRewardConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResGeneral" messagename="TABTestInfo" pbinname="TABTestData" classname="TABTestData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResPlayerGuide" messagename="GuideABTestInfo" pbinname="GuideABTestData" classname="GuideABTestData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerGuide" messagename="PlayerGuideItem" pbinname="MainGuideData" classname="PlayerGuideData"
        serverloadflag="ST_GameServer,ST_IdipServer"
        splitsubtableflag="1" subtablename="MainGuideData_acm,MainGuideData_chase,MainGuideData_farm,MainGuideData_home,MainGuideData_js,MainGuideData_main,MainGuideData_nr3e,MainGuideData_omd,MainGuideData_tyc,MainGuideData_ugc,MainGuideData_wuqidashi,MainGuideData_cloud,MainGuideData_arena,MainGuideData_va,MainGuideData_main_pc,MainGuideData_Mayday,MainGuideData_Mouse_pc,MainGuideData_cook" />
    <entry protofile="ResQAInvest" messagename="QAInvest" pbinname="QAInvestData" classname="QAInvestData"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResQAInvest" messagename="QAInvestCallbackUrl" pbinname="QAInvestCallbackUrlConfigData" classname="QAInvestCallbackUrlConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPersonality" messagename="PersonalityLabelInfo" pbinname="PersonalityLabelData" classname="PersonalityLabelData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPersonality" messagename="PersonalityStateInfo" pbinname="PersonalityStateData" classname="PersonalityStateData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResSensitiveFilter" messagename="SensitiveFilterInfo" pbinname="SensitiveFilterData" classname="SensitiveFilterData"
        serverloadflag="ST_GameServer,ST_UgcappServer" />
    <entry protofile="ResMatchMMR" messagename="MatchMWConstsInfo" pbinname="MatchMWConstsData" classname="MatchMWConstsData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_MatchServer,ST_StarproommatchServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResMatchMMR" messagename="MatchTypeSideMMRWinExpInfo" pbinname="MatchTypeSideMMRWinExpData" classname="MatchTypeSideMMRWinExpData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatchMMR" messagename="MatchMMRBattleRoyalRankBaseScore" pbinname="MatchMMRBattleRoyalRankBaseScoreData" classname="MatchMMRBattleRoyalRankBaseScoreData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundScoreCaseInfo" pbinname="MatchWarmRoundScoreCaseData" classname="MatchWarmRoundScoreCaseData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatchWarmRound" messagename="SecondaryGameplayNewbieWarmRoundCondition" pbinname="SecondaryGameplayNewbieWarmRoundData" classname="SecondaryGameplayNewbieWarmRoundData"
        serverloadflag="ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundAiLevelInfo" pbinname="MatchWarmRoundAILevel" classname="MatchWarmRoundAILevel"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundLimitInfo" pbinname="MatchWarmRoundLimitData" classname="MatchWarmRoundLimitData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundSelfRankSToChangeInfoV2" pbinname="MatchWarmRoundSelfRankSToChangeDataV3" classname="MatchWarmRoundSelfRankSToChangeDataV3"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerGuide" messagename="GuideWarmBattleInfo" pbinname="GuideWarmBattleData" classname="GuideWarmBattleData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResPlayerGuide" messagename="PlayerStarterItem" pbinname="PlayerStarterFaceConfData" classname="PlayerStarterFaceConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerGuide" messagename="PlayerStarterItem" pbinname="PlayerStarterDressConfData" classname="PlayerStarterDressConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWorkLevelData" messagename="WorkLevelDataItem" pbinname="WorkLevelData" classname="WorkLevelData"
        serverloadflag="ST_UgcServer,ST_UgcplatServer,ST_GameServer,ST_UgcappServer" />
    <entry protofile="ResMoneyTree" messagename="MoneyTreeShakeDrop" pbinname="MoneyTreeShakeDrop" classname="MoneyTreeShakeDrop"
        serverloadflag="ST_XiaowoServer,ST_GameServer" />
    <entry protofile="ResMoneyTree" messagename="MoneyTreeWaterDrop" pbinname="MoneyTreeWaterDrop" classname="MoneyTreeWaterDrop"
        serverloadflag="ST_XiaowoServer,ST_GameServer" />
    <entry protofile="ResDropConfig" messagename="DropConfig" pbinname="DropConfig" classname="DropConfig"
        serverloadflag="ST_XiaowoServer,ST_GameServer,ST_FarmServer" />
    <entry protofile="ResXiaowoLv" messagename="XiaowoLvConf" pbinname="XiaowoLvConf"
            classname="XiaowoLvConf"
            serverloadflag="ST_XiaowoServer,ST_GameServer"/>
    <entry protofile="ResMoneyTree" messagename="ShakeCostConf" pbinname="ShakeCostConf"
            classname="ShakeCostConf"
            serverloadflag="ST_XiaowoServer,ST_GameServer"/>
    <entry protofile="ResMoneyTree" messagename="PlantLevelConf" pbinname="PlantLevelConf"
            classname="PlantLevelConf"
            serverloadflag="ST_XiaowoServer,ST_GameServer"/>
    <entry protofile="ResXiaowoSysConf" messagename="XiaowoSysConf" pbinname="XiaowoSysConf"
            classname="XiaowoSysConf"
            serverloadflag="ST_XiaowoServer,ST_GameServer,ST_SampleroomServer,ST_FarmServer"/>
    <entry protofile="ResTerrain" messagename="TerrainConfig" pbinname="TerrainConfig"
            classname="TerrainConfig"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResXiaowoMap" messagename="XiaowoMapConf" pbinname="XiaowoMapConf"
            classname="XiaowoMapConf"
            serverloadflag="ST_GameServer,ST_XiaowoServer"/>
    <entry protofile="ResXiaowoHot" messagename="XiaowoHotConf" pbinname="XiaowoHotConf"
            classname="XiaowoHotConf"
            serverloadflag="ST_XiaowoServer,ST_GameServer"/>
    <entry protofile="ResXiaowoFarming" messagename="FarmingConfig" pbinname="FarmingConfig"
            classname="FarmingConfig"
            serverloadflag="ST_XiaowoServer,ST_GameServer"/>
    <entry protofile="ResXiaowoFarming" messagename="FarmingHandBookConfig"
            pbinname="FarmingHandBookConfig" classname="FarmingHandBookConfig"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResXiaowoFarming" messagename="FarmingLevelConfig"
            pbinname="FarmingLevelConfig" classname="FarmingLevelConfig"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResUGCPlacableItem" messagename="Item_UGCActorInfo" pbinname="UGCActorInfo"
            classname="ResUGCPlacableItemConf"
            serverloadflag="ST_XiaowoServer,ST_GameServer"
            splitsubtableflag="1"
            subtablename="UGCActorInfo_Building,UGCActorInfo_Character,UGCActorInfo_Construction,UGCActorInfo_Decorate,UGCActorInfo_Furniture,UGCActorInfo_Nature,UGCActorInfo_Stratagem"/>
    <entry protofile="ResXiaowoMap" messagename="XiaowoInitFurniture" pbinname="XiaowoInitFurniture"
            classname="ResXiaowoInitFurniture"
            serverloadflag="ST_XiaowoServer,ST_GameServer"/>
    <entry protofile="ResAIInfo" messagename="RandomAICfg" pbinname="AIRandomCfgData"
            classname="AIRandomCfgData"
            serverloadflag="ST_BattleServer,ST_StarpbattleServer"/>
    <entry protofile="ResPray" messagename="PrayConfig" pbinname="PrayData" classname="PrayData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResPray" messagename="PrayPropertyConfig" pbinname="PrayPropertyData"
            classname="PrayPropertyData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResPray" messagename="PraySpecTimeLimitResultInfo" pbinname="PraySpecTimeLimitResultData"
           classname="PraySpecTimeLimitResultData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResMatch" messagename="MatchConstsInfo" pbinname="MatchConstsData"
            classname="MatchConstsData"
            serverloadflag="ST_MatchServer,ST_StarproommatchServer,ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer,ST_GameServer"/>
    <entry protofile="ResMatch" messagename="MatchExpandScopeLimit"
            pbinname="MatchExpandScopeLimitData" classname="MatchExpandScopeLimitData"
            serverloadflag="ST_MatchServer,ST_StarproommatchServer"/>
    <entry protofile="ResMatch" messagename="MatchDynamicTeamRobots"
            pbinname="MatchDynamicTeamRobotsData" classname="MatchDynamicTeamRobotsData"
            serverloadflag="ST_MatchServer,ST_StarproommatchServer"
            splitsubtableflag="1"
            subtablename="MatchDynamicTeamRobotsData_BS,MatchDynamicTeamRobotsData_JS,MatchDynamicTeamRobotsData_Mayday,MatchDynamicTeamRobotsData_Metro,MatchDynamicTeamRobotsData_acm,MatchDynamicTeamRobotsData_arena,MatchDynamicTeamRobotsData_chase,MatchDynamicTeamRobotsData_competition,MatchDynamicTeamRobotsData_dnd,MatchDynamicTeamRobotsData_fps,MatchDynamicTeamRobotsData_main,MatchDynamicTeamRobotsData_nr3e,MatchDynamicTeamRobotsData_ugc,MatchDynamicTeamRobotsData_HOK,MatchDynamicTeamRobotsData_FB"/>
    <entry protofile="ResMatch" messagename="CustomRoomRule" pbinname="CustomRoomData"
            classname="CustomRoomData"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer"/>
    <entry protofile="ResMatch" messagename="PinBlockInfo" pbinname="PinBlockInfoData"
            classname="PinBlockInfoData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResSnsShare" messagename="SnsShareConfig" pbinname="SnsShareConfigData"
            classname="SnsShareConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResSnsShare" messagename="InviteTeamShareConfig"
            pbinname="InviteTeamShareConfig" classname="InviteTeamShareConfig"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer"/>
    <entry protofile="ResRegional" messagename="Regional" pbinname="RegionalConfigData"
            classname="RegionalConf"
            serverloadflag="ST_GameServer,ST_IdipServer,ST_RoomServer,ST_StarproomServer,ST_ActivityServer"/>
    <entry protofile="ResRegional" messagename="RegionalMatchPool"
            pbinname="RegionalMatchPoolConfigData" classname="RegionalMatchPoolConf"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer"/>
    <entry protofile="ResServer" messagename="T_ServerInfoData" pbinname="DevServerInfoData"
            classname="DevServerInfoConfData"
            serverloadflag="ST_UgcMoveServer"/>
    <entry protofile="ResServer" messagename="T_ServerInfoData" pbinname="ServerInfoData"
            classname="ServerInfoData"
            serverloadflag="ST_UgcMoveServer"/>
    <entry protofile="ResRegional" messagename="RegionIdcLoadCapability"
            pbinname="RegionIdcLoadCapabilityConfigData" classname="RegionIdcLoadCapabilityConf"
            serverloadflag="ST_RoomServer,ST_StarproomServer"/>
    <entry protofile="ResUgcExpData" messagename="UgcExpDataItem" pbinname="UgcExpData"
            classname="UgcExpData"
            serverloadflag="ST_UgcServer"/>
    <entry protofile="ResUgcExpData" messagename="UgcExpCalcParams" pbinname="UgcExpCalcParamsData"
            classname="UgcExpCalcParamsConf"
            serverloadflag="ST_UgcServer"/>
    <entry protofile="ResText" messagename="ClientServerTextConfData" pbinname="ServerTextConfData"
            classname="ServerTextConfData" splitsubtableflag="1" subtablename="ServerTextConfData_main"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResText" messagename="ClientServerTextConf" pbinname="ServerTextPatchConfData"
            classname="ServerTextPatchConfData"
            serverloadflag="ST_GameServer"/>

    <entry protofile="ResArenaHeroCard" messagename="ArenaHeroCardReLi" pbinname="ArenaHeroCardReLi" classname="ArenaHeroCardReLi"
           serverloadflag="ST_ArenaServer"/>

    <entry protofile="ResArenaHeroCardHot" messagename="ArenaHeroCardHot" pbinname="ArenaHeroCardHot" classname="ArenaHeroCardHot"
           serverloadflag="ST_ArenaServer" rainbowenvdefault="0" rainbowgroup="Arena.HeroCardHot"/>

    <entry protofile="ResArenaHeroCardHot" messagename="ArenaHeroCardSuit" pbinname="ArenaHeroCardSuit" classname="ArenaHeroCardSuit"
           serverloadflag="ST_ArenaServer" rainbowenvdefault="0" rainbowgroup="Arena.HeroCardSuit"/>

    <entry protofile="ResArenaHeroCardReplace" messagename="ArenaHeroCardReplace" pbinname="ArenaHeroCardReplaceData" classname="ArenaHeroCardReplaceData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCard" messagename="ArenaCardData" pbinname="ArenaCardData" classname="ArenaCardData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroCard" messagename="ArenaHeroCard" classname="ArenaHeroCard"
           serverloadflag="ST_GameServer,ST_ArenaServer"
           pbinname="ArenaHeroCard"
           splitsubtableflag="1"
           subtablename="ArenaHeroCard_ailin,
            ArenaHeroCard_ake,
            ArenaHeroCard_anqila,
            ArenaHeroCard_bingyi,
            ArenaHeroCard_caiwenji,
            ArenaHeroCard_chengyaojin,
            ArenaHeroCard_daji,
            ArenaHeroCard_daqiao,
            ArenaHeroCard_dianwei,
            ArenaHeroCard_diaochan,
            ArenaHeroCard_dinvsang,
            ArenaHeroCard_direnjie,
            ArenaHeroCard_dongfangyao,
            ArenaHeroCard_donghuangtaiyi,
            ArenaHeroCard_gongbenwuzang,
            ArenaHeroCard_gongsunli,
            ArenaHeroCard_haiyue,
            ArenaHeroCard_houyi,
            ArenaHeroCard_huamulan,
            ArenaHeroCard_jialuo,
            ArenaHeroCard_kai,
            ArenaHeroCard_lanlingwang,
            ArenaHeroCard_libai,
            ArenaHeroCard_liubei,
            ArenaHeroCard_liushan,
            ArenaHeroCard_lubanqihao,
            ArenaHeroCard_luopuxia,
            ArenaHeroCard_lvbu,
            ArenaHeroCard_makeboluo,
            ArenaHeroCard_maolingshaonv,
            ArenaHeroCard_mozi,
            ArenaHeroCard_sunce,
            ArenaHeroCard_sunshangxiang,
            ArenaHeroCard_sunwukong,
            ArenaHeroCard_wangzhaojun,
            ArenaHeroCard_wusula,
            ArenaHeroCard_wuzetian,
            ArenaHeroCard_xiahoudun,
            ArenaHeroCard_xiangyu,
            ArenaHeroCard_xiaoqiao,
            ArenaHeroCard_yangyuhuan,
            ArenaHeroCard_yao,
            ArenaHeroCard_yase,
            ArenaHeroCard_yuji,
            ArenaHeroCard_yunying,
            ArenaHeroCard_zhangliang,
            ArenaHeroCard_zhaoyun,
            ArenaHeroCard_zhenji,
            ArenaHeroCard_zhongkui,
            ArenaHeroCard_zhugeliang"
    />
    <entry protofile="ResArenaHeroDeraultCard" messagename="ArenaHeroDeraultCardData" pbinname="ArenaHeroDeraultCardData" classname="ArenaHeroDefaultCardData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroDeraultCard" messagename="ArenaHeroClassData" pbinname="ArenaHeroClassData" classname="ArenaHeroClassData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCardPack" messagename="ArenaCardPackData" pbinname="ArenaCardPackData" classname="ArenaCardPackData"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCardPack" messagename="ArenaCardPackRule" pbinname="ArenaCardPackRuleData" classname="ArenaCardPackRuleData"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCardPack" messagename="ArenaCardPocketRule" pbinname="ArenaCardPocketRuleData" classname="ArenaCardPocketRuleData"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCardPack" messagename="ArenaCardPackUpgrade" pbinname="ArenaCardPackUpgradeData" classname="ArenaCardPackUpgradeData"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCardPack" messagename="ArenaCardPackGroup" pbinname="ArenaCardPackGroupData" classname="ArenaCardPackGroupData"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCardPack" messagename="ArenaCardPackUpgradeLimit" pbinname="ArenaCardPackUpgradeLimitData" classname="ArenaCardPackUpgradeLimitData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroUnlock" messagename="ArenaHeroUnlock" pbinname="ArenaHeroUnlockData" classname="ArenaHeroUnlockConf"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessTeam" pbinname="ArenaCombatEffectivenessTeamData" classname="ArenaCombatEffectivenessTeamData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessHeroBattle" pbinname="ArenaCombatEffectivenessHeroBattleData" classname="ArenaCombatEffectivenessHeroBattleData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessHeroGlobal" pbinname="ArenaCombatEffectivenessHeroGlobalData" classname="ArenaCombatEffectivenessHeroGlobalData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessGroup" pbinname="ArenaCombatEffectivenessGroupData" classname="ArenaCombatEffectivenessGroupData"
           serverloadflag="ST_GameServer,ST_ArenaServer,ST_BattleServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessRank" pbinname="ArenaCombatEffectivenessRankData" classname="ArenaCombatEffectivenessRankData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessBadge" pbinname="ArenaCombatEffectivenessBadgeData" classname="ArenaCombatEffectivenessBadgeData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessMisc" pbinname="ArenaCombatEffectivenessMiscData" classname="ArenaCombatEffectivenessMiscData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="AccelerationConf" pbinname="ArenaCombatEffectivenessAccelerationData" classname="ArenaCombatEffectivenessAccelerationData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessLowAcceleration" pbinname="ArenaCombatEffectivenessLowAccelerationData" classname="ArenaCombatEffectivenessLowAccelerationData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessProtection" pbinname="ArenaCombatEffectivenessProtectionData" classname="ArenaCombatEffectivenessProtectionData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessTopThreshold" pbinname="ArenaCombatEffectivenessTopThresholdData" classname="ArenaCombatEffectivenessTopThresholdData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessTeam" pbinname="HOKCombatEffectivenessTeamData" classname="HOKCombatEffectivenessTeamData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessHeroBattle" pbinname="HOKCombatEffectivenessHeroBattleData" classname="HOKCombatEffectivenessHeroBattleData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessHeroGlobal" pbinname="HOKCombatEffectivenessHeroGlobalData" classname="HOKCombatEffectivenessHeroGlobalData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessRank" pbinname="HOKCombatEffectivenessRankData" classname="HOKCombatEffectivenessRankData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessBadge" pbinname="HOKCombatEffectivenessBadgeData" classname="HOKCombatEffectivenessBadgeData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessMisc" pbinname="HOKCombatEffectivenessMiscData" classname="HOKCombatEffectivenessMiscData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="AccelerationConf" pbinname="HOKCombatEffectivenessAccelerationData" classname="HOKCombatEffectivenessAccelerationData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessLowAcceleration" pbinname="HOKCombatEffectivenessLowAccelerationData" classname="HOKCombatEffectivenessLowAccelerationData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessProtection" pbinname="HOKCombatEffectivenessProtectionData" classname="HOKCombatEffectivenessProtectionData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaCombatEffectiveness" messagename="ArenaCombatEffectivenessTopThreshold" pbinname="HOKCombatEffectivenessTopThresholdData" classname="HOKCombatEffectivenessTopThresholdData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResMobaPeakTournament" messagename="MobaPeakTournament" pbinname="MobaPeakTournamentData" classname="MobaPeakTournamentData"
           serverloadflag="ST_MatchServer,ST_RoomServer,ST_BattleServer,ST_GameServer" />
    <entry protofile="ResMobaPeakTournament" messagename="MobaPeakTournamentOpenTime" pbinname="MobaPeakTournamentOpenTimeData" classname="MobaPeakTournamentOpenTimeData"
           serverloadflag="ST_MatchServer,ST_RoomServer,ST_BattleServer,ST_GameServer" />
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarDegreeFactor" pbinname="ArenaHeroStarDegreeFactorData" classname="ArenaHeroStarDegreeFactorData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarPerfScoreFactor" pbinname="ArenaHeroStarPerfScoreFactorData" classname="ArenaHeroStarPerfScoreFactorData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarMisc" pbinname="ArenaHeroStarMiscData" classname="ArenaHeroStarMiscData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarHeroLevel" pbinname="ArenaHeroStarHeroLevelData" classname="ArenaHeroStarHeroLevelData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarGeneralLevel" pbinname="ArenaHeroStarGeneralLevelData" classname="ArenaHeroStarGeneralLevelData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarLevelReward" pbinname="ArenaHeroStarLevelRewardData" classname="ArenaHeroStarLevelRewardData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarLevelQuest" pbinname="ArenaHeroStarLevelQuestData" classname="ArenaHeroStarLevelQuestData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarRewardReplace" pbinname="ArenaHeroStarRewardReplaceData" classname="ArenaHeroStarRewardReplaceData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaHeroStar" messagename="ArenaHeroStarRegulationFactor" pbinname="ArenaHeroStarRegulationFactorData" classname="ArenaHeroStarRegulationFactorData"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResText" messagename="TextItemChangeReasonConfig"
            pbinname="TextItemChangeReasonData" classname="TextItemChangeReasonData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResServerIdipArea" messagename="ServerIdipAreaConfig"
            pbinname="ServerIdipAreaConfig" classname="ServerIdipAreaConfig"
            serverloadflag="ST_GameServer,ST_IdipServer,ST_UgcplatServer,ST_UgcServer,ST_ActivityServer"/>
    <entry protofile="ResMatch" messagename="MatchSvrAlloc" pbinname="MatchSvrAllocData"
            classname="MatchSvrAllocData"
            serverloadflag="ST_MatchallocServer,ST_StarpmatchallocServer"/>
    <entry protofile="ResMatch" messagename="MatchSvrDevAlloc" pbinname="MatchSvrDevAllocData"
            classname="MatchSvrDevAllocData"
            serverloadflag="ST_MatchallocServer,ST_StarpmatchallocServer"/>
    <entry protofile="ResMatch" messagename="MatchTypeConflictOutlookConfig"
            pbinname="MatchTypeConflictOutlookData" classname="MatchTypeConflictOutlookData"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer"/>
    <entry protofile="ResMatch" messagename="MatchTypeOutlookReplaceConfig"
            pbinname="MatchTypeOutlookReplaceData" classname="MatchTypeOutlookReplaceData"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer"/>
    <entry protofile="ResMatch" messagename="UgcTemplateConflictOutlookConfig"
            pbinname="UgcTemplateConflictOutlookData" classname="UgcTemplateConflictOutlookData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResUGCEditor" messagename="Item_UGCArtisanGradeInfo"
            pbinname="UGCArtisanGradeInfo" classname="UGCArtisanGradeInfo"
            serverloadflag="ST_GameServer,ST_UgcServer"/>
    <entry protofile="ResUgcMgr" messagename="T_UgcCommonConfig" pbinname="UgcCommonConfig"
            classname="UgcCommonConfig"
            serverloadflag="ST_All"/>
    <entry protofile="ResUgcMgr" messagename="T_UgcBucketConfig" pbinname="UgcBucketConfig"
            classname="UgcBucketConfig"
            serverloadflag="ST_All"/>
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcWhiteListConfig" classname="UgcWhiteListConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResUGCEditor" messagename="UGCMapDescConf" pbinname="UGCMapDescConf" classname="UGCMapDescConf"
        serverloadflag="ST_UgcServer" />
    <entry protofile="ResServerKvConfig" messagename="ServerKvConfig" pbinname="ServerKvConfig" classname="ServerKvConfig"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResUgcStarWorld" messagename="UgcStarWorldDifficultyGroup" pbinname="UgcStarWorldDifficultyGroup" classname="UgcStarWorldDifficultyGroup"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcStarWorld" messagename="UgcStarWorldLevelConfig" pbinname="UgcStarWorldLevelConfig" classname="UgcStarWorldLevelConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcStarWorld" messagename="UgcStarWorldDifficultyReward" pbinname="UgcStarWorldDifficultyReward" classname="UgcStarWorldDifficultyReward"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPilot" messagename="PilotEntrance" pbinname="PilotEntranceData" classname="PilotEntranceData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPilot" messagename="PilotTab" pbinname="PilotTabData" classname="PilotTabData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="TakeawayConf" pbinname="TakeawayConfData" classname="TakeawayConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="TakeawayBoxConf" pbinname="TakeawayBoxConfData" classname="TakeawayBoxConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResSnsInvitation" messagename="SnsInvitationConf" pbinname="SnsInvitationConfData" classname="SnsInvitationConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcCoCreateWhiteListConfig" pbinname="UgcCoCreateWhiteListConfig" classname="UgcCoCreateWhiteListConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResScene" messagename="FireworkEffectConfig" pbinname="FireworkEffectConfigData" classname="FireworkEffectConfData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResScene" messagename="FireworkPartyTime" pbinname="FireworkPartyTimeData" classname="FireworkPartyTimeConfData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResScene" messagename="FireworksTexBanTime" pbinname="FireworksTexBanTimeData" classname="FireworksTexBanTimeConfData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResScene" messagename="FireworkPartyConf" pbinname="FireworkPartyConfData" classname="FireworkPartyConfData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResRedEnvelope" messagename="RedEnvelopeActivity" pbinname="RedEnvelopeActivityData" classname="RedEnvelopeActivityData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResRedEnvelope" messagename="RedEnvelopeTurnSetting" pbinname="RedEnvelopeTurnSettingData" classname="RedEnvelopeTurnSettingData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResRedEnvelope" messagename="RedEnvelopeActServerSetting" pbinname="RedEnvelopeActServerSettingData" classname="RedEnvelopeActServerSettingData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRedEnvelope" messagename="RedEnvelopPackageItemIdSetting" pbinname="RedEnvelopPackageItemIdData" classname="RedEnvelopPackageItemIdData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRedEnvelope" messagename="RedEnvelopeSpawnerSetting" pbinname="RedEnvelopeSpawnerSettingData" classname="RedEnvelopeSpawnerSettingData"
        serverloadflag="ST_LobbyServer" />
    <entry protofile="ResRedEnvelope" messagename="RedEnvelopeInGameRewardRaffleSetting" pbinname="RedEnvelopeAcInGameRewardRaffleData" classname="RedEnvelopeInGameRewardRaffleData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRedDot" messagename="RedDotConfig" pbinname="RedDotConfigData" classname="GeneralRedDotConfigData"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResSocialPerformance" messagename="SocialPerformanceItem" pbinname="SocialPerformanceData" classname="SocialPerformanceData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResCommunityLevelLoad" messagename="CommunityLevelLoadConf" pbinname="CommunityLevelLoadConf" classname="CommunityLevelLoadConfData"
        serverloadflag="ST_LobbyServer" />
    <entry protofile="ResMidas" messagename="MidasPageDooActivityConf" pbinname="MidasPageDooActivityConf" classname="MidasPageDooActivityConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResGameLiveTask" messagename="GameLiveTask" pbinname="GameLiveTaskConf" classname="GameLiveTaskConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcForbiddenTopics" messagename="UgcForbiddenTopic" pbinname="UgcForbiddenTopicsData" classname="UgcForbiddenTopicsData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResLanguages" messagename="Languages" pbinname="LanguagesData" classname="LanguagesData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatch" messagename="MatchDynamicTeamPlayers" pbinname="MatchDynamicTeamPlayersData" classname="MatchDynamicTeamPlayersData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer" />
    <entry protofile="ResMatch" messagename="MatchDynamicMaxTimeout" pbinname="MatchDynamicMaxTimeoutData" classname="MatchDynamicMaxTimeoutData"
        serverloadflag="ST_MatchServer,ST_RoomServer,ST_StarproommatchServer,ST_StarproomServer"
        splitsubtableflag="1" subtablename="MatchDynamicMaxTimeoutData_BS,MatchDynamicMaxTimeoutData_JS,MatchDynamicMaxTimeoutData_Mayday,MatchDynamicMaxTimeoutData_acm,MatchDynamicMaxTimeoutData_arena,MatchDynamicMaxTimeoutData_chase,MatchDynamicMaxTimeoutData_competition,MatchDynamicMaxTimeoutData_dnd,MatchDynamicMaxTimeoutData_fps,MatchDynamicMaxTimeoutData_main,MatchDynamicMaxTimeoutData_nr3e,MatchDynamicMaxTimeoutData_ugc,MatchDynamicMaxTimeoutData_HOK,MatchDynamicMaxTimeoutData_FB" />
    <entry protofile="ResNotice" messagename="MarqueeNoticeConfig" pbinname="MarqueeNoticeData" classname="MarqueeNoticeData"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResNotice" messagename="PreWarnMarqueeNoticeConf" pbinname="PreWarnMarqueeNoticeConfData" classname="PreWarnMarqueeNoticeConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResThirdPartyEnv" messagename="AIServerEnvConfig" pbinname="AIServerEnvConfData" classname="AIServerEnvConfData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResNicknameWidth" messagename="NicknameWidth" pbinname="NicknameWidthsData" classname="NicknameWidthConfData"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResQRCode" messagename="QRCodeCalculationRule" pbinname="QRCodeCalculationRuleData" classname="QRCodeCalculationRuleData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResChannelEntrance" messagename="ChannelDisableScene" pbinname="ChannelDisableSceneConf" classname="ChannelDisableSceneConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResWWXGame" messagename="WXGameShareTaskShareContentConfig" pbinname="WXGameShareTaskShareContentData" classname="WXGameShareTaskShareContentData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="WXGameShareTaskShareContentData_WX" />
    <entry protofile="ResXiaowoMap" messagename="XiaowoSampleRoomObject" pbinname="XiaowoSampleRoomObject" classname="ResXiaowoSampleRoomObject"
        serverloadflag="ST_XiaowoServer,ST_GameServer" />
    <entry protofile="ResJSReliableAIConfig" messagename="JSReliableAIConfig" pbinname="JSReliableAIConfig" classname="JSReliableAIConfig"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResActivity" messagename="LuckyBalloonConf" pbinname="LuckyBalloonConfData" classname="LuckyBalloonConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="LuckyBalloonRewardConf" pbinname="LuckyBalloonRewardConfData" classname="LuckyBalloonRewardConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="LuckyBalloonBlessConf" pbinname="LuckyBalloonBlessConfData" classname="LuckyBalloonBlessConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResIdipDatamoreConfig" messagename="IdipDatamoreConfig" pbinname="IdipDatamoreConfig" classname="IdipDatamoreConfig"
        serverloadflag="ST_IdipServer" />
    <entry protofile="ResScene" messagename="LobbyChangeColorConfig" pbinname="LobbyChangeColorConfigData" classname="LobbyChangeColorConfigData"
        serverloadflag="ST_GameServer,ST_LobbyServer" />
    <entry protofile="ResUGCAigc" messagename="AigcDefaultPromptData" pbinname="AigcDefaultPromptData" classname="AigcDefaultPromptData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="ActivitySuperLinearRedeemConfig" pbinname="ActivitySuperLinearRedeemData" classname="ActivitySuperLinearRedeemData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="ActivitySuperLinearRandomConfig" pbinname="ActivitySuperLinearRandomData" classname="ActivitySuperLinearRandomData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="ActivityTeamRankConfig" pbinname="ActivityTeamRankData" classname="ActivityTeamRankData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="LuckyStarHandBook" pbinname="LuckyStarHandBookData" classname="LuckyStarHandBookData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="LuckyStarHandBookStarPool" pbinname="LuckyStarHandBookStarPoolData" classname="LuckyStarHandBookStarPoolData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="LuckyStarHandBookBless" pbinname="LuckyStarHandBookBlessData" classname="LuckyStarHandBookBlessData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResBattle" messagename="BattleLikeContentConfig" pbinname="BattleLikeContentData" classname="BattleLikeContentData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityRecruiteOrder" messagename="ActRecruiteConf" pbinname="ActRecruiteConf" classname="ActivityRecruiteOrderConf"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResActivity" messagename="ActivityCheckInManualConfig" pbinname="ActivityCheckInManualData" classname="ActivityCheckInManualData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="FarmDailyAwardStartLevelConfig" pbinname="FarmDailyAwardStartLevelConfig" classname="FarmDailyAwardStartLevelConfig"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="WeekendGiftCheckInConfig" pbinname="WeekendGiftCheckInConfig" classname="WeekendGiftCheckInConfig"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResBroadcastSetting" messagename="BroadcastSetting" pbinname="BroadcastSetting" classname="BroadcastSettingConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMatchRoom" messagename="UgcMatchRoomRuleData" pbinname="UgcMatchRoomRuleData" classname="UgcMatchRoomRuleData"
        serverloadflag="ST_UgcServer" />
    <entry protofile="ResUgcMatchRoom" messagename="UgcMatchRoomParamData" pbinname="UgcMatchRoomParamData" classname="UgcMatchRoomParamData"
        serverloadflag="ST_UgcServer" />
    <entry protofile="ResUgcMatchRoom" messagename="UgcMatchRoomIdPoolData" pbinname="UgcMatchRoomIdPoolData" classname="UgcMatchRoomIdPoolDataConf"
            serverloadflag="ST_UgcServer" />
    <entry protofile="ResGeneral" messagename="CrossRouteConf" pbinname="CrossRouteConfData" classname="CrossRouteConf"
        serverloadflag="ST_All" />
    <entry protofile="ResGeneral" messagename="CrossRpcFilterConf" pbinname="CrossRpcFilterConfData" classname="CrossRpcFilterConf"
        serverloadflag="ST_All" />
    <entry protofile="ResTYCMisc" messagename="TYCDSMigrateConf" pbinname="TYCDSMigrateConf" classname="TYCDSMigrateConfData"
        serverloadflag="ST_TycoonServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResSpringRedPacket" messagename="RedPacketBaseConf" pbinname="RedPacketBaseConf" classname="RedPacketBaseConfData"
            serverloadflag="ST_GameServer,ST_XiaowoServer,ST_LobbyServer,ST_FarmServer"/>
    <entry protofile="ResSpringRedPacket" messagename="RedPacketAmsConf" pbinname="RedPacketAmsConf" classname="RedPacketAmsConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResSpringRedPacket" messagename="RedPacketAmsReward" pbinname="RedPacketAmsReward" classname="RedPacketAmsRewardData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResSpringRedPacket" messagename="RedPacketReplyText" pbinname="RedPacketReplyText" classname="RedPacketReplyTextData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResSpringRedPacket" messagename="RedPacketCommonConf" pbinname="RedPacketCommonConf" classname="RedPacketCommonConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcAiAnicapWhiteListConfig" classname="UgcAiAnicapWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcAiVoiceWhiteListConfig" classname="UgcAiVoiceWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcAiAnswerWhiteListConfig" classname="UgcAiAnswerWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcResConf" messagename="ResUGCResConf" pbinname="UGCResConf" classname="UGCResConfData"
        serverloadflag="ST_UgcServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcCosPathConfig" pbinname="UgcCosPathConfig" classname="UgcCosPathConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResReturningUser" messagename="ReturningUserConstsInfo" pbinname="ReturningUserConstsData" classname="ReturningUserConstsData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResReturningUser" messagename="ReturningUserConf" pbinname="ReturningUserConfData" classname="ReturningUserConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReturningUser" messagename="ReturningTabConfig" pbinname="ReturningTabConfigData" classname="ReturningTabConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReturningUser" messagename="ReturningRecommend2Data" pbinname="ReturningRecommend2Data" classname="ReturningRecommend2Data"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReturningUser" messagename="ReturningDailyBenefitsLimitData" pbinname="ReturningDailyBenefitsLimitData" classname="ReturningDailyBenefitsLimitData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReturningUser" messagename="ReturningUserWarmRoundLimitInfo" pbinname="ReturningUserWarmRoundLimitData" classname="ReturningUserWarmRoundLimitData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResAIInfo" messagename="AIFashionSeason" pbinname="AIFashionSeason" classname="AIFashionSeason"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResAIInfo" messagename="AIFashionRandom" pbinname="AIFashionRandom" classname="AIFashionRandom"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResWelfare" messagename="WholeGameProcessRule" pbinname="WholeGameProcessRuleData" classname="WholeGameProcessRuleData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcUpdateMapWhiteListConfig" pbinname="UgcUpdateMapWhiteListConfig" classname="UgcUpdateMapWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcNewYear" messagename="UgcNewYearConf" pbinname="UgcNewYearConfData" classname="UgcNewYearActivity"
        serverloadflag="ST_GameServer,ST_UgcServer" />
    <entry protofile="ResUgcNewYear" messagename="UGC_ActivityCollection" pbinname="UgcNewYearCollectionConfData" classname="UgcActivityCollection"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResProtectedScore" messagename="ProtectedScoreConfig" pbinname="ProtectedScoreData" classname="ProtectedScoreData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="ProtectedScoreData_acm_daluandou,ProtectedScoreData_chase_dawangbiezhuawo,ProtectedScoreData_fps_tuweimenghuandao,ProtectedScoreData_fps_wuqidashi,ProtectedScoreData_js_jisufeiche,ProtectedScoreData_lightning_jiangbeizhengduo,ProtectedScoreData_main_zhuwanfa,ProtectedScoreData_nr3e_duomaomao,ProtectedScoreData_nr3e_sheishilangren,ProtectedScoreData_nr3e_wodixingdong,ProtectedScoreData_Moba,ProtectedScoreData_bnb_paopaodazhan,ProtectedScoreData_HOK" />
    <entry protofile="ResProtectedScore" messagename="ProtectedScoreWinStreakConfig" pbinname="ProtectedScoreWinStreakData" classname="ProtectedScoreWinStreakData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="ProtectedScoreWinStreakData_acm_daluandou,ProtectedScoreWinStreakData_chase_dawangbiezhuawo,ProtectedScoreWinStreakData_fps_tuweimenghuandao,ProtectedScoreWinStreakData_fps_wuqidashi,ProtectedScoreWinStreakData_js_jisufeiche,ProtectedScoreWinStreakData_lightning_jiangbeizhengduo,ProtectedScoreWinStreakData_main_zhuwanfa,ProtectedScoreWinStreakData_nr3e_duomaomao,ProtectedScoreWinStreakData_nr3e_sheishilangren,ProtectedScoreWinStreakData_nr3e_wodixingdong,ProtectedScoreWinStreakData_Moba,ProtectedScoreWinStreakData_bnb_paopaodazhan,ProtectedScoreWinStreakData_HOK" />
    <entry protofile="ResProtectedScore" messagename="SeasonProtectedScoreAdditionalConfig" pbinname="ProtectedScoreAdditionalData" classname="ProtectedScoreAdditionalData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="ProtectedScoreAdditionalData_acm_daluandou,ProtectedScoreAdditionalData_chase_dawangbiezhuawo,ProtectedScoreAdditionalData_fps_tuweimenghuandao,ProtectedScoreAdditionalData_fps_wuqidashi,ProtectedScoreAdditionalData_js_jisufeiche,ProtectedScoreAdditionalData_lightning_jiangbeizhengduo,ProtectedScoreAdditionalData_main_zhuwanfa,ProtectedScoreAdditionalData_nr3e_duomaomao,ProtectedScoreAdditionalData_nr3e_sheishilangren,ProtectedScoreAdditionalData_nr3e_wodixingdong,ProtectedScoreAdditionalData_Moba,ProtectedScoreAdditionalData_bnb_paopaodazhan,ProtectedScoreAdditionalData_HOK" />
    <entry protofile="ResProtectedScore" messagename="ProtectedScoreWinConfig" pbinname="ProtectedScoreWinData" classname="ProtectedScoreWinData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="ProtectedScoreWinData_acm_daluandou,ProtectedScoreWinData_chase_dawangbiezhuawo,ProtectedScoreWinData_fps_tuweimenghuandao,ProtectedScoreWinData_fps_wuqidashi,ProtectedScoreWinData_js_jisufeiche,ProtectedScoreWinData_lightning_jiangbeizhengduo,ProtectedScoreWinData_main_zhuwanfa,ProtectedScoreWinData_nr3e_duomaomao,ProtectedScoreWinData_nr3e_sheishilangren,ProtectedScoreWinData_nr3e_wodixingdong,ProtectedScoreWinData_Moba,ProtectedScoreWinData_bnb_paopaodazhan,ProtectedScoreWinData_HOK" />
    <entry protofile="ResProtectedScore" messagename="ProtectedScoreDimensionConfig" pbinname="ProtectedScoreDimensionData" classname="ProtectedScoreDimensionData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1" subtablename="ProtectedScoreDimensionData_Moba,ProtectedScoreDimensionData_HOK" />
    <entry protofile="ResScene" messagename="FireworkSystemTextColorConfig" pbinname="FireworkSystemTextColorData" classname="FireworkSystemTextColorData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResNewYearPilot" messagename="NewYearPilotEntrance" pbinname="NewYearPilotEntranceData" classname="NewYearPilotEntranceData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResFix" messagename="FixConfig" pbinname="FixConfigData" classname="FixConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcVisualProgramWhiteListConfig" classname="UgcVisualProgramWhiteListConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResNewYearPilot" messagename="NewYearPilotTab" pbinname="NewYearPilotTabData" classname="NewYearPilotTabData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResNewActivityPilot" messagename="NewActivityPilotEntrance" pbinname="NewActivityPilotEntranceData" classname="NewActivityPilotEntranceData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResNewActivityPilot" messagename="NewActivityPilotTab" pbinname="NewActivityPilotTabData" classname="NewActivityPilotTabData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResNewActivityPilot" messagename="ActivityHYNCheckInConf" pbinname="ActivityHYNCheckInConf" classname="ActivityHYNCheckInConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="ActivityHYWarmUpConf" pbinname="ActivityHYWarmUpData" classname="ActivityHYWarmUpData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="ActivityHYWarmUpCheckinConf" pbinname="ActivityHYWarmUpCheckinData" classname="ActivityHYWarmUpCheckinData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="ActivityHYWarmUpProgressConf" pbinname="ActivityHYWarmUpProgressData" classname="ActivityHYWarmUpProgressData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResAIScript" messagename="AIScriptConfig" pbinname="AIScriptConfig" classname="AIScriptConfig"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResFashionScoreCalc" messagename="FashionScoreCalcConfig" pbinname="FashionScoreCalcData" classname="FashionScoreCalcData"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcCreateChatGroupWhiteListConfig" classname="UgcCreateChatGroupWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundScoreCaseInfo" pbinname="MatchAiLabGrpWarmRoundScoreCaseData" classname="MatchAiLabGrpWarmRoundScoreCaseData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundAiLevelInfo" pbinname="MatchAiLabCGrpWarmRoundAILevel" classname="MatchAiLabCGrpWarmRoundAILevel"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundLimitInfo" pbinname="MatchAiLabCGrpWarmRoundLimitData" classname="MatchAiLabCGrpWarmRoundLimitData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatchWarmRound" messagename="MatchWarmRoundMmrCorrectingInfo" pbinname="MatchWarmRoundMmrCorrectingData" classname="MatchWarmRoundMmrCorrectingData"
        serverloadflag="ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResMatchWarmRound" messagename="ChaseNewbieSideWarmRoundRoomData" pbinname="ChaseNewbieSideWarmRoundRoomData" classname="ChaseNewbieSideWarmRoundRoomData"
           serverloadflag="ST_RoomServer" />
    <entry protofile="ResMatchMMR" messagename="MatchMmrSideOrRoleMinDimenScoreInfo" pbinname="MatchMmrSideOrRoleMinDimenScoreData" classname="MatchMmrSideOrRoleMinDimenScoreData"
        serverloadflag="ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResRaffle" messagename="RaffleTab" pbinname="RaffleTabData" classname="RaffleTabData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResAIInfo" messagename="RandomAICfg" pbinname="MatchAiLabCGrpAIRandomCfgData" classname="MatchAiLabCGrpAIRandomCfgData"
        serverloadflag="ST_BattleServer" />
    <entry protofile="ResAIInfo" messagename="RandomAICfg" pbinname="MatchDifficultyTestAIRandomCfgData" classname="MatchDifficultyTestAIRandomCfgData"
           serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatch" messagename="MatchDynamicTeamRobots" pbinname="MatchAiLabCGrpDynamicTeamRobotsData" classname="MatchAiLabCGrpDynamicTeamRobotsData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer" />
    <entry protofile="ResMatch" messagename="MatchExpandScopeLimit" pbinname="MatchAiLabCGrpExpandScopeLimitData" classname="MatchAiLabCGrpExpandScopeLimitData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer" />
    <entry protofile="ResMatch" messagename="MatchSpecialRoundRobotSideDiffVirtualRoomInfo" pbinname="MatchSpecialRoundRobotSideDiffVirtualRoomInfoData" classname="MatchSpecialRoundRobotSideDiffVirtualRoomInfoData"
        serverloadflag="ST_RoomServer,ST_MatchServer,ST_BattleServer"
        splitsubtableflag="1" subtablename="MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_BS,MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_moba,MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_HOK,MatchSpecialRoundRobotSideDiffVirtualRoomInfoData_FB" />
    <entry protofile="ResAMSItem" messagename="AMSItemCfg" pbinname="AMSItemCfgData" classname="AMSItemCfgData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResAMSItem" messagename="AMSPackageIdCfg" pbinname="AMSPackageIdCfgData" classname="AMSPackageIdCfgData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResGameSetting" messagename="GameSettingConf" pbinname="GameSettingConfData" classname="GameSettingConfData"
        serverloadflag="ST_All" />
    <entry protofile="ResGameSetting" messagename="UPGameSettingData" pbinname="ResUPGameSettingData" classname="UPGameSettingData"
           serverloadflag="ST_GameServer,ST_BattleServer" />
    <entry protofile="ResMatch" messagename="RoomSideRole" pbinname="RoomSideRoleData" classname="RoomSideRoleData"
        serverloadflag="ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResLobby" messagename="LobbyConfig" pbinname="LobbyConfigData" classname="LobbyConfigData"
        serverloadflag="ST_GameServer,ST_LobbyServer,ST_LobbyallocServer,ST_BattleServer,ST_StarpbattleServer"
        rainbowenvdefault="0" rainbowgroup="LobbyConfigData"
        splitsubtableflag="1" subtablename="LobbyConfigData_main,LobbyConfigData_ugc,LobbyConfigData_testUgc" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcDialogueImageWhiteListConfig" classname="UgcDialogueImageWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcSkillEditorWhiteListConfig" classname="UgcSkillEditorWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatchMMR" messagename="MatchSpecSideMapMmrInfo" pbinname="MatchSpecSideMapMmrData" classname="MatchSpecSideMapMmrData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer,ST_MatchServer,ST_StarproommatchServer" />
    <entry protofile="ResMatchMMR" messagename="MatchMmrDegreeMapQualifyingInfo" pbinname="MatchMmrDegreeMapQualifyingData" classname="MatchMmrDegreeMapQualifyingData"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer,ST_MatchServer,ST_StarproommatchServer" />
    <entry protofile="ResMatchMMR" messagename="MatchMMRSelfRankSToChangeInfo" pbinname="MatchMMRSelfRankSToChangeData" classname="MatchMMRSelfRankSToChangeData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResMatchMMR" messagename="MatchMmrAttenuationInfo" pbinname="MatchMmrAttenuationData" classname="MatchMmrAttenuationData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResBrGameProps" messagename="BrGamePropConf" pbinname="BrGamePropsData" classname="BrGamePropsData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeMarkGenData" messagename="RoguelikeMarkDifficultyDropInfo" pbinname="ResRoguelikeMarkLevelDropData" classname="ResRoguelikeMarkLevelDropData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeMarkGenData" messagename="RoguelikeMarkBoxInfo" pbinname="ResRoguelikeMarkBoxData" classname="ResRoguelikeMarkBoxData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeMarkGenData" messagename="RoguelikeMarkConstInfo" pbinname="ResRoguelikeMarkConstData" classname="ResRoguelikeMarkConstData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeMarkGenData" messagename="RoguelikeMarkEntryInfo" pbinname="ResRoguelikeMarkEntryData" classname="ResRoguelikeMarkEntryData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeMarkGenData" messagename="RoguelikeMarkBaseEffectInfo" pbinname="ResRoguelikeMarkBaseEffectData" classname="ResRoguelikeMarkBaseEffectData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeMarkGenData" messagename="RoguelikeMarkShapeInfo" pbinname="ResRoguelikeMarkShapeData" classname="ResRoguelikeMarkShapeData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeMarkGenData" messagename="RoguelikeMarkMaterialInfo" pbinname="ResRoguelikeMarkMaterialData" classname="ResRoguelikeMarkMaterialData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeTalent" messagename="RoguelikeGetTalentData" pbinname="ResRoguelikeGetTalentData" classname="ResRoguelikeGetTalentData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRoguelikeTask" messagename="RoguelikeSeasonConf" pbinname="ResRoguelikeSeasonData" classname="ResRoguelikeSeasonData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_RoomServer,ST_StarproomServer,ST_BattleServer,ST_StarpbattleServer,ST_IdipServer" />
    <entry protofile="ResServerRoute" messagename="UserRouteTable" pbinname="UserRouteTableData" classname="UserRouteTableData"
        serverloadflag="ST_DirServer"
        rainbowenvdefault="0" rainbowgroup="BUILTIN_ROUTE.USER_ROUTE_TABLE" />
    <entry protofile="ResDsConfig" messagename="DsGrayAllocConfig" pbinname="DsGrayAllocConfigData" classname="DsGrayAllocData"
           serverloadflag="ST_All"
           rainbowenvdefault="0" rainbowgroup="GRAY.DS_GRAY_ALLOC_TABLE" />
    <entry protofile="ResMatch" messagename="MatchSimulatorDynamicMaxTimeout" pbinname="MatchSimulatorDynamicMaxTimeoutData" classname="MatchSimulatorDynamicMaxTimeoutData"
        serverloadflag="ST_MatchServer,ST_RoomServer,ST_StarproommatchServer,ST_StarproomServer"
        splitsubtableflag="1" subtablename="MatchSimulatorDynamicMaxTimeoutData_BS,MatchSimulatorDynamicMaxTimeoutData_JS,MatchSimulatorDynamicMaxTimeoutData_Mayday,MatchSimulatorDynamicMaxTimeoutData_Metro,MatchSimulatorDynamicMaxTimeoutData_acm,MatchSimulatorDynamicMaxTimeoutData_arena,MatchSimulatorDynamicMaxTimeoutData_chase,MatchSimulatorDynamicMaxTimeoutData_competition,MatchSimulatorDynamicMaxTimeoutData_dnd,MatchSimulatorDynamicMaxTimeoutData_fps,MatchSimulatorDynamicMaxTimeoutData_main,MatchSimulatorDynamicMaxTimeoutData_nr3e,MatchSimulatorDynamicMaxTimeoutData_ugc,MatchSimulatorDynamicMaxTimeoutData_HOK,MatchSimulatorDynamicMaxTimeoutData_FB" />
    <entry protofile="ResMatch" messagename="MatchFilterValTypeDimeExpandInfo" pbinname="MatchFilterValTypeDimeExpandData" classname="MatchFilterValTypeDimeExpandData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer"
        splitsubtableflag="1" subtablename="MatchFilterValTypeDimeExpandData_main,MatchFilterValTypeDimeExpandData_ugc" />
    <entry protofile="ResClub" messagename="ClubRecommendSortData" pbinname="ClubRecommendSortData" classname="ClubRecommendSortData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMonopoly" messagename="ActivityMonopolyMainCfg"
            pbinname="ActivityMonopolyMainData" classname="ActivityMonopolyMainData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityMonopoly" messagename="ActivityMonopolyGridCfg"
            pbinname="ActivityMonopolyGridData" classname="ActivityMonopolyGridData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityMonopoly" messagename="ActivityMonopolyLotteryCfg"
            pbinname="ActivityMonopolyLotteryData" classname="ActivityMonopolyLotteryData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityMonopoly" messagename="ActivityMonopolyRoundRewardCfg"
            pbinname="ActivityMonopolyRoundRewardData" classname="ActivityMonopolyRoundRewardData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityMonopoly" messagename="ActivityMonopolyStepRewardCfg"
            pbinname="ActivityMonopolyStepRewardData" classname="ActivityMonopolyStepRewardData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityLotteryDraw" messagename="ActivityLotteryDrawRewardCfg" pbinname="ActivityLotteryDrawRewardData" classname="ActivityLotteryDrawRewardData"
        serverloadflag="ST_GameServer,ST_ActivityServer"
        splitsubtableflag="1" subtablename="ActivityLotteryDrawRewardData, ActivityLotteryDrawRewardData_Minesweeper"/>
    <entry protofile="ResActivityLotteryDraw" messagename="ActivityLotteryDrawCostCfg" pbinname="ActivityLotteryDrawCostData" classname="ActivityLotteryDrawCostData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMinesweeper" messagename="ActivityMinesweeperMainCfg" pbinname="ActivityMinesweeperMainData" classname="ActivityMinesweeperMainData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityMinesweeper" messagename="ActivityMinesweeperEventCfg" pbinname="ActivityMinesweeperEventData" classname="ActivityMinesweeperEventData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityFlyingChess" messagename="ActivityFlyingChessCfg" pbinname="ActivityFlyingChessData" classname="ActivityFlyingChessData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityFlyingChess" messagename="ActivityFlyingChessGridCfg" pbinname="ActivityFlyingChessGridData" classname="ActivityFlyingChessGridData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityFlyingChess" messagename="ActivityFlyingChessRoundRewardCfg" pbinname="ActivityFlyingChessRoundRewardData" classname="ActivityFlyingChessRoundRewardData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderConf" pbinname="MusicOrderConfData" classname="MusicOrderConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderRefreshConf" pbinname="MusicOrderRefreshConfData" classname="MusicOrderRefreshConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderModeDropConf" pbinname="MusicOrderModeDropConfData" classname="MusicOrderModeDropConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderNoteDropConf" pbinname="MusicOrderNoteDropConfData" classname="MusicOrderNoteDropConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderTaskConf" pbinname="MusicOrderTaskConfData" classname="MusicOrderTaskConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderMiscConfig" pbinname="MusicOrderMiscConfigData" classname="MusicOrderMiscConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderDropDimensionConf" pbinname="MusicOrderDropDimensionConfData" classname="MusicOrderDropDimensionConfData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderDailyDropConf" pbinname="MusicOrderDailyDropConfData" classname="MusicOrderDailyDropConfData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityMusicOrder" messagename="MusicOrderModeGroupConf" pbinname="MusicOrderModeGroupConfData" classname="MusicOrderModeGroupConfData"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityAnimalHandbook" messagename="AnimalHandbookConf" pbinname="AnimalHandbookConfData" classname="AnimalHandbookConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivityAnimalHandbook" messagename="AnimalHandbookSpeciesConf" pbinname="AnimalHandbookSpeciesConfData" classname="AnimalHandbookSpeciesConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivityAnimalHandbook" messagename="AnimalHandbookColonyConf" pbinname="AnimalHandbookColonyConfData" classname="AnimalHandbookColonyConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivityAnimalHandbook" messagename="AnimalHandbookUltimatePrizeConf" pbinname="AnimalHandbookUltimatePrizeConfData" classname="AnimalHandbookUltimatePrizeConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivityAnimalHandbook" messagename="AnimalHandbookMiscConf" pbinname="AnimalHandbookMiscConfData" classname="AnimalHandbookMiscConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResUgcMgr" messagename="UgcBuyGoodsCreatorListConfig" pbinname="UgcBuyGoodsCreatorListConfig" classname="UgcBuyGoodsCreatorListConfig"
        serverloadflag="ST_All" />
    <entry protofile="ResDanMu" messagename="DanMuUgcBlockCfg" pbinname="DanMuUgcBlockCfg" classname="DanMuUgcBlockCfg"
        serverloadflag="ST_UgcServer,ST_DanmuServer" />
    <entry protofile="ResDanMu" messagename="DanMuCfg" pbinname="DanMuCfg" classname="DanMuCfg"
        serverloadflag="ST_UgcServer,ST_DanmuServer" />
    <entry protofile="ResConcert" messagename="ConcertConfig" pbinname="ConcertConfigData" classname="ConcertConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResConcert" messagename="ConcertStarConfig" pbinname="ConcertStarConfigData" classname="ConcertStarConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResConcert" messagename="ConcertExpressionConfig" pbinname="ConcertExpressionConfigData" classname="ConcertExpressionConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResConcert" messagename="ConcertLightBoardConfig" pbinname="ConcertLightBoardConfigData" classname="ConcertLightBoardConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResConcert" messagename="ConcertTimesConfig" pbinname="ConcertTimesConfigData" classname="ConcertTimesConfigData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerGrayRule" messagename="PlayerGrayRuleConf" pbinname="PlayerGrayRuleConfData" classname="PlayerGrayRuleConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatch" messagename="MatchFillBackItem" pbinname="MatchFillBackResData" classname="MatchFillBackResData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer,ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResUserLabel" messagename="UserLabelEntry" pbinname="UserLabelConf" classname="UserLabelConf"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResSuperCore" messagename="SuperCoreEntry" pbinname="SuperCoreConf" classname="SuperCoreConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="StickerConf" pbinname="StickerConfData" classname="StickerConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="StickerNormalRewardConf" pbinname="StickerNormalRewardConfData" classname="StickerNormalRewardConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="StickerBingoRewardConf" pbinname="StickerBingoRewardConfData" classname="StickerBingoRewardConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="StickerChapterRewardConf" pbinname="StickerChapterRewardConfData" classname="StickerChapterRewardConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResUgcMap" messagename="UgcMapScoreLabelConf" pbinname="UgcMapScoreLabelConf" classname="UgcMapScoreLabelConf"
        serverloadflag="ST_GameServer,ST_UgcServer,ST_UgcplatServer" />
    <entry protofile="ResMatch" messagename="MatchModeSortInfo" pbinname="MatchModeSortInfo" classname="MatchModeSortInfo"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMatch" messagename="MatchTeamSizeMatchingRule" pbinname="MatchTeamSizeMatchingRuleData" classname="MatchTeamSizeMatchingRuleData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer" />
    <entry protofile="ResUgcResConf" messagename="ResUGCResConfNew" pbinname="UGCResConfNew" classname="UGCResConfDataNew"
        serverloadflag="ST_UgcServer" />
    <entry protofile="ResMidas" messagename="MidasZoneConf" pbinname="MidasSandboxZoneConf" classname="MidasSandboxZoneConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResMidas" messagename="MidasZoneConf" pbinname="MidasReleaseZoneConf" classname="MidasReleaseZoneConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResFarmItem" messagename="FarmItemConf" pbinname="FarmItemConf" classname="FarmItemConf"
        serverloadflag="ST_GameServer,ST_FarmServer"
        splitsubtableflag="1" subtablename="FarmItemConf_Animal,FarmItemConf_Base,FarmItemConf_Crop,FarmItemConf_Fish,FarmItemConf_Building,FarmItemConf_furniture,FarmItemConf_Collection,FarmItemConf_furnitureSkin,FarmItemConf_Pet,FarmItemConf_PetClothing,FarmItemConf_Dish" />
    <entry protofile="ResFarmBuff" messagename="FarmBuffEffectConf" pbinname="FarmBuffEffectConf" classname="FarmBuffEffectConf"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuff" messagename="FarmBuffConf" pbinname="FarmBuffConf" classname="FarmBuffConf"
           serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuff" messagename="FarmBuffRainBowConf" pbinname="FarmBuffRainBowConf" classname="FarmBuffRainBowConf"
           serverloadflag="ST_GameServer,ST_FarmServer" rainbowenvdefault="0" rainbowgroup="FARM.RainBowBuff"/>
    <entry protofile="ResFarmBuff" messagename="FarmBuffSkillConf" pbinname="FarmBuffSkillConf" classname="FarmBuffSkillConf"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuff" messagename="FarmBuffFaceConf" pbinname="FarmBuffFaceConf" classname="FarmBuffFaceConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmCropMachine" messagename="FarmCropMachineConf" pbinname="FarmCropMachineConf" classname="FarmCropMachineConf"
           serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmMagic" messagename="FarmMagicConf" pbinname="FarmMagicConf" classname="FarmMagicConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuilding" messagename="FarmBuildingTypeConf" pbinname="FarmBuildingTypeConf" classname="FarmBuildingTypeConf"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuilding" messagename="FarmBuildingLevelUpConf" pbinname="FarmBuildingLevelUpConf" classname="FarmBuildingLevelUpConf"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuilding" messagename="FarmGridLevelUpConf" pbinname="FarmGridLevelUpConf" classname="FarmGridLevelUpConf"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuilding" messagename="FarmGridLevelUpConf" pbinname="FarmGridLevelUpExpConf"
           classname="FarmGridLevelUpExpConf"
           serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResFarmBuilding" messagename="FarmBuildingSkinConf" pbinname="FarmBuildingSkinConf" classname="FarmBuildingSkinConf"
           serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuildingDecoration" messagename="FarmBuildingDecorationJumpConf" pbinname="FarmBuildingDecorationJumpConf" classname="FarmBuildingDecorationJumpConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuildingDecoration" messagename="FarmBuildingDecorationPrayConf" pbinname="FarmBuildingDecorationPrayConf" classname="FarmBuildingDecorationPrayConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmBuildingDecoration" messagename="FarmBuildingDecorationEnvConf" pbinname="FarmBuildingDecorationEnvConf" classname="FarmBuildingDecorationEnvConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmOperation" messagename="FarmOperationConf" pbinname="FarmOperationConf" classname="FarmOperationConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmCollection" messagename="FarmCollectionHandbookConf" pbinname="FarmCollectionHandbookConf" classname="FarmCollectionHandbookConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmCollection" messagename="FarmSceneDropRandomWeightConf" pbinname="FarmSceneDropRandomWeightConf" classname="FarmSceneDropRandomWeightConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmCrop" messagename="FarmCropConf" pbinname="FarmCropConf" classname="FarmCropConf"
        serverloadflag="ST_GameServer,ST_FarmServer,ST_ActivityServer"
        splitsubtableflag="1" subtablename="FarmCropConf_Animal,FarmCropConf_Crop" />
    <entry protofile="ResFarmCrop" messagename="FarmVileplumeLevelConf" pbinname="FarmVileplumeLevelConf" classname="FarmVileplumeLevelConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCrop" messagename="FarmCropLevelConf" pbinname="FarmCropLevelConf" classname="FarmCropLevelConf"
        serverloadflag="ST_GameServer,ST_FarmServer,ST_ActivityServer"
        splitsubtableflag="1" subtablename="FarmCropLevelConf_Animal,FarmCropLevelConf_Crop" />
    <entry protofile="ResFarmMonthcardDecoration" messagename="FarmMonthcardDecoration" pbinname="FarmMonthcardDecoration" classname="FarmMonthcardDecoration"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmSysConf" messagename="FarmSysConf" pbinname="FarmSysConf" classname="FarmSysConf"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmEvent" messagename="FarmEvent" pbinname="FarmEvent" classname="FarmEvent"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmEvent" messagename="FarmEventGuarantee" pbinname="FarmEventGuarantee" classname="FarmEventGuarantee"
           serverloadflag="ST_GameServer,ST_FarmServer" />

    <entry protofile="ResFarmEvent" messagename="FarmEventGuarantee" pbinname="FarmEventGuaranteeFish" classname="FarmEventGuaranteeFish"
           serverloadflag="ST_GameServer,ST_FarmServer" />

    <entry protofile="ResFarmEvent" messagename="FarmEventCloudRatio" pbinname="FarmEventCloudRatio" classname="FarmEventCloudRatio"
                                                                  serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmHotSpring" messagename="FarmHotSpring" pbinname="FarmHotSpring" classname="FarmHotSpring"
           serverloadflag="ST_GameServer,ST_FarmServer" />

    <entry protofile="ResFarmEvent" messagename="FarmNpcFish" pbinname="FarmNpcFish" classname="FarmNpcFish"
           serverloadflag="ST_GameServer,ST_FarmServer" />

    <entry protofile="ResFarmKirin" messagename="FarmKirinLevelUpConf" pbinname="FarmKirinLevelUpConf" classname="FarmKirinLevelUpConf"
            serverloadflag="ST_GameServer,ST_FarmServer" />

    <entry protofile="ResPlayerUgcLevel" messagename="PlayerUgcLevelConf" pbinname="PlayerUgcLevelConf" classname="PlayerUgcLevelConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerUgcLevel" messagename="PlayerUgcLevelEnumConf" pbinname="PlayerUgcLevelEnumConf" classname="PlayerUgcLevelEnumConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerUgcLevel" messagename="PlayerUgcBadgeConf" pbinname="PlayerUgcBadgeConf" classname="PlayerUgcBadgeConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerUgcLevel" messagename="PlayerUgcActivityDegreeGetRatioConf" pbinname="PlayerUgcActivityDegreeGetRatioConf" classname="PlayerUgcActivityDegreeGetRatioConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResPlayerUgcLevel" messagename="AchievementReissueUgcBadgeConf" pbinname="AchievementReissueUgcBadgeConf" classname="AchievementReissueUgcBadgeConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReward" messagename="QuickRewardConf" pbinname="QuickRewardConfData" classname="QuickRewardConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReward" messagename="RewardCompensateConf" pbinname="RewardCompensateConfData" classname="RewardCompensateConfData"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResMatch" messagename="MatchRouteWhiteList" pbinname="MatchRouteWhiteListData" classname="MatchRouteWhiteListData"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer" />
    <entry protofile="ResUgcMgr" messagename="T_UgcWhiteListConfig" pbinname="UgcCustomSkeletonAnimWhiteListConfig" classname="UgcCustomSkeletonAnimWhiteListConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResAIInfo" messagename="AIDataControl" pbinname="AIDataControlData" classname="AIDataControlData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResActivity" messagename="ActivityPermanentExchangeConfig" pbinname="ActivityPermanentExchange" classname="ActivityPermanentExchange"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResActivity" messagename="EntertainIntelligenceStationConf" pbinname="EntertainIntelligenceStationConf" classname="EntertainIntelligenceStationConf"
        serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResUgcWhiteList" messagename="UgcWhiteListPlayerGroupConf" pbinname="UgcWhiteListPlayerGroupConfData" classname="XlsWhiteListPlayerGroupConfData"
        serverloadflag="ST_GameServer,ST_DirServer,ST_ActivityServer" />
    <entry protofile="ResUgcWhiteList" messagename="UgcWhiteListModuleConf" pbinname="UgcWhiteListModuleConfData" classname="XlsWhiteListModuleConfData"
        serverloadflag="ST_GameServer,ST_DirServer,ST_ActivityServer" />
    <entry protofile="ResUgcWhiteList" messagename="AdminWhiteListForGameAreaConf" pbinname="AdminWhiteListForGameAreaConfData" classname="AdminWhiteListForGameAreaConf"
        serverloadflag="ST_GameServer,ST_DirServer,ST_ActivityServer" />
    <entry protofile="ResFarmWarning" messagename="FarmWarningConf" pbinname="FarmWarningConf" classname="FarmWarningConf"
        serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResChat" messagename="TeamBattleBroadcastResConf" pbinname="TeamBattleBroadcastResConf" classname="TeamBattleBroadcastResConf"
        serverloadflag="ST_RoomServer,ST_StarproomServer" />
    <entry protofile="ResRecommend" messagename="RecommendMatchTypeConf" pbinname="RecommendMatchTypeConf" classname="RecommendMatchTypeConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResUGCAIModule" messagename="UGCAITypeSectionConf" pbinname="UGCAITypeSectionConf" classname="UGCAITypeSectionConf"
        serverloadflag="ST_GameServer,ST_UgcappServer" />
    <entry protofile="ResMatch" messagename="MatchSideLimitConfig" pbinname="MatchSideLimitConfigData" classname="MatchSideLimitConfig"
        serverloadflag="ST_MatchServer,ST_StarproommatchServer,ST_RoomServer,ST_StarproomServer"
        splitsubtableflag="1" subtablename="MatchSideLimitConfigData_nr3e" />
    <entry protofile="ResReputation" messagename="ReputationScoreRuleConfig" pbinname="ReputationScoreRuleConfig" classname="ReputationScoreRuleConfig"
        serverloadflag="ST_GameServer,ST_RoomServer,ST_StarproomServer,ST_IdipServer" />
    <entry protofile="ResReputation" messagename="BehaviorScoreChangeConfig" pbinname="BehaviorScoreChangeConfig" classname="BehaviorScoreChangeConfig"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReputation" messagename="ReputationSysCommonConfig" pbinname="ReputationSysCommonConfig" classname="ReputationSysCommonConfig"
        serverloadflag="ST_GameServer,ST_BattleServer,ST_RoomServer,ST_IdipServer,ST_StarpbattleServer,ST_StarproomServer" />
    <entry protofile="ResPlayerLevel" messagename="ModUnlockConf" pbinname="ModUnlockLevelConfData" classname="ModUnlockLevelConfData"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResReputation" messagename="ReputationBehaviourDef" pbinname="ReputationBehaviourDef" classname="ReputationBehaviourDef"
        serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResMatch" messagename="MatchTypeDetailPageGroupInfo" pbinname="MatchTypeDetailPageGroupInfoData" classname="MatchTypeDetailPageGroupInfoData"
        serverloadflag="ST_GameServer"
        splitsubtableflag="1" subtablename="MatchTypeDetailPageGroupInfoData_main" />
    <entry protofile="ResAdministration" messagename="AdministrationCodeConf" pbinname="AdministrationCodeConfData" classname="AdministrationCodeConfData"
        serverloadflag="ST_GameServer,ST_RankServer,ST_IdipServer,ST_ClubServer" />
    <entry protofile="ResIAA" messagename="IAAConf" pbinname="IAAConfData" classname="IAAConfData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="IAAConfData_Raffle,IAAConfData_Settlement,IAAConfData_BattlePass,IAAConfData_GuideToApp,IAAConfData_UGCInLevel"/>
    <entry protofile="ResRecommend" messagename="EntertainmentGuideConf" pbinname="EntertainmentGuideConf" classname="EntertainmentGuideConf"
        serverloadflag="ST_GameServer" />
    <entry protofile="ResRecommend" messagename="EntertainmentGuideTaskConf" pbinname="EntertainmentGuideTaskConf" classname="EntertainmentGuideTaskConf"
        serverloadflag="ST_GameServer" splitsubtableflag="1" subtablename="EntertainmentGuideTaskConf_V2,EntertainmentGuideTaskConf_V1"/>
    <entry protofile="ResRecommend" messagename="EntertainmentGuideConfV2" pbinname="EntertainmentGuideConfV2" classname="EntertainmentGuideConfV2"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResAIInfo" messagename="AILevelSpecialCfg" pbinname="AILevelSpecialCfgData" classname="AILevelSpecialCfgData"
        serverloadflag="ST_BattleServer,ST_StarpbattleServer" />
    <entry protofile="ResProfileTheme" messagename="ProfileThemeConifg" pbinname="ProfileThemeData"
           classname="ProfileThemeData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResFarmWhiteListConfig" messagename="FarmWhiteListConfig" pbinname="FarmWhiteListConfig"
           classname="FarmWhiteListConfig"
           serverloadflag="ST_GameServer,ST_FarmServer" rainbowenvdefault="0" rainbowgroup="FARM_WHITE_LIST"/>
    <entry protofile="ResFarmWhiteListConfig" messagename="FarmActivityTimeConfig" pbinname="FarmActivityTimeConfig" classname="FarmActivityTimeConfig"
           serverloadflag="ST_GameServer,ST_FarmServer" rainbowenvdefault="0" rainbowgroup="FARM.ActivityTime"/>
    <entry protofile="ResLiveLink" messagename="LiveLinkPlayInfoItem"
           pbinname="LiveLinkPlayInfoData" classname="LiveLinkPlayInfoData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResFarmRainbowConfig" messagename="FarmModuleOpenTimeConf" pbinname="FarmModuleOpenTimeConf" classname="FarmModuleOpenTimeConf"
           serverloadflag="ST_GameServer,ST_FarmServer" rainbowenvdefault="0" rainbowgroup="FARM.ModuleOpenTime"/>
    <entry protofile="ResFarmRainbowConfig" messagename="FarmRealTimeKVConf" pbinname="FarmRealTimeKVConf" classname="FarmRealTimeKVConf"
           serverloadflag="ST_GameServer,ST_FarmServer" rainbowenvdefault="0" rainbowgroup="FARM.RealTimeKV"/>
    <entry protofile="ResLiveLink" messagename="LiveLinkPlayAdminWhiteInfoItem"
           pbinname="LiveLinkPlayAdminWhiteInfoData" classname="LiveLinkPlayAdminWhiteInfoData"
           serverloadflag="ST_GameServer"
           rainbowenvdefault="0" rainbowgroup="LiveLinkPlayAdminWhiteInfoData"/>
    <entry protofile="ResWolfKillRoadToMaster" messagename="WolfKillRoadToMasterItem"
           pbinname="WolfKillRoadToMasterData" classname="WolfKillRoadToMaster"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResWolfKillRoleInfoBase" messagename="WolfKillRoleInfoBaseItem"
           pbinname="WolfKillRoleInfoBaseData" classname="WolfKillRoleInfoBase"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResWolfKillRoleInfoRate" messagename="WolfKillRoleInfoRateItem"
           pbinname="WolfKillRoleInfoRateData" classname="WolfKillRoleInfoRate"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResWolfKillDecoration" messagename="WolfKillResDecorationReactItem" pbinname="WolfKillDecorationReactData" classname="WolfKillDecorationReact"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResWolfKillDecoration" messagename="WolfKillResDecorationAniItem" pbinname="WolfKillDecorationAniData" classname="WolfKillDecorationAni"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResWolfKillDecoration" messagename="WolfKillSpecialSpeakItem" pbinname="WolfKillSpecialSpeakData" classname="WolfKillSpecialSpeak"
            serverloadflag="ST_BattleServer"/>
    <entry protofile="ResActivityClubChallenge" messagename="ActivityClubChallengeCfg"
            pbinname="ActivityClubChallengeData" classname="ActivityClubChallengeData"
            serverloadflag="ST_GameServer,ST_ClubServer"/>
    <entry protofile="ResActivityClubChallenge" messagename="ActivityClubChallengeMatchCfg"
            pbinname="ActivityClubChallengeMatchData" classname="ActivityClubChallengeMatchData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivityClubChallenge" messagename="ActivityClubChallengeMatchSettlementCfg"
            pbinname="ActivityClubChallengeMatchSettlementData" classname="ActivityClubChallengeMatchSettlementData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivityClubChallenge" messagename="ActivityClubChallengeStarLightRewardCfg"
            pbinname="ActivityClubChallengeStarLightRewardData" classname="ActivityClubChallengeStarLightRewardData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="MainCupsStageConfig"
            pbinname="MainCupsStageConfigData" classname="MainCupsStageConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="ModeCupsStageConfig"
            pbinname="ModeCupsStageConfigData" classname="ModeCupsStageConfigData"
            serverloadflag="ST_GameServer,ST_RankServer"/>
    <entry protofile="ResCups" messagename="CupRuleConfig"
            pbinname="CupRuleConfigData" classname="CupRuleConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="CupsRoundConfig"
           pbinname="CupsRoundConfig" classname="CupsRoundConfigData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="CupAdditionConfig"
            pbinname="CupAdditionConfigData" classname="CupAdditionConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="CupAdditionFashionValueConfig"
            pbinname="CupAdditionFashionValueConfigData" classname="CupAdditionFashionValueConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="CupAdditionRelationConfig"
            pbinname="CupAdditionRelationConfigData" classname="CupAdditionRelationConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="CupAdditionPlayModeConfig"
            pbinname="CupAdditionPlayModeConfigData" classname="CupAdditionPlayModeConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResCups" messagename="CupsConfig"
            pbinname="CupsConfigData" classname="CupsConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResNR3E3Vocation" messagename="NR3E3VocationData" pbinname="NR3E3VocationData"
            classname="WolfKillVocation"
            serverloadflag="ST_GameServer,ST_BattleServer" />
    <entry protofile="ResBattlePass" messagename="BattlePassSeasonConf"
            pbinname="BattlePassSeasonConf" classname="BattlePassSeasonConfData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="BattlePassSeasonConf_nr3e_sheishilangren,BattlePassSeasonConf_moba,BattlePassSeasonConf_main,BattlePassSeasonConf_maingame"/>
    <entry protofile="ResBattlePass" messagename="BattlePassLevelRewardConf"
            pbinname="BattlePassLevelRewardConf" classname="BattlePassLevelRewardConfData"
            serverloadflag="ST_GameServer,ST_ActivityServer"
            splitsubtableflag="1"
            subtablename="BattlePassLevelRewardConf_nr3e_sheishilangren,BattlePassLevelRewardConf_moba,BattlePassLevelRewardConf_main,BattlePassLevelRewardConf_maingame,BattlePassLevelRewardConf_summerbp"/>
    <entry protofile="ResRaffle" messagename="RaffleBICfg" pbinname="RaffleBIData"
            classname="RaffleBIData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="RaffleBIData_TabOrder,RaffleBIData_main,RaffleBIData_tesewanfa,RaffleBIData_huoyue,RaffleBIData_hedy,RaffleBIData_miles,RaffleBIData_karl,RaffleBIData_silvester,RaffleBIData_yujun"/>
    <entry protofile="ResPreparations" messagename="PreparationsWidgetConf" pbinname="ResPreparationsWidgetConfData"
           classname="WolfKillPreparationsWidgetConf"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="TrainingCampSportsmanConf" pbinname="TrainingCampSportsmanConf" classname="TrainingCampSportsmanConf"
            serverloadflag="ST_GameServer,ST_ActivityServer,ST_IdipServer" />
    <entry protofile="ResActivity" messagename="TrainingActivityConf" pbinname="TrainingActivityConf" classname="TrainingActivityConf"
            serverloadflag="ST_ActivityServer,ST_IdipServer" />
    <entry protofile="ResActivity" messagename="TrainingActivityAwardConf" pbinname="TrainingActivityAwardConf" classname="TrainingActivityAwardConf"
            serverloadflag="ST_ActivityServer,ST_IdipServer" />
    <entry protofile="ResFarmFish" messagename="FarmFishTrackConf" pbinname="FarmFishTrackConf" classname="FarmFishTrackConf"
           serverloadflag="ST_GameServer,ST_FarmServer" />
    <entry protofile="ResFarmFish" messagename="FarmFishConf" pbinname="FarmFishConf" classname="FarmFishConf"
           serverloadflag="ST_FarmServer,ST_ActivityServer,ST_GameServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishBaitConf" pbinname="FarmFishBaitConf"
           classname="FarmFishBaitConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishScoreConf" pbinname="FarmFishScoreConf"
           classname="FarmFishScoreConf"
           serverloadflag="ST_FarmServer,ST_ActivityServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishPeriodConf" pbinname="FarmFishPeriodConf"
           classname="FarmFishPeriodConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishCardPackConf" pbinname="FarmFishCardPackConf"
           classname="FarmFishCardPackConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishCardPackDropConf" pbinname="FarmFishCardPackDropConf"
           classname="FarmFishCardPackDropConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishLevelConf" pbinname="FarmFishLevelConf"
           classname="FarmFishLevelConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResNPCFarmConf" messagename="NPCFarmBuildingSkin" pbinname="NPCFarmBuildingSkin"
           classname="NPCFarmBuildingSkin"
           serverloadflag="ST_FarmServer,ST_GameServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishBowlConf" pbinname="FarmFishBowlConf"
           classname="FarmFishBowlConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishPoolLevelConf" pbinname="FarmFishPoolLevelConf"
           classname="FarmFishPoolLevelConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishPoolLayerConf" pbinname="FarmFishPoolLayerConf"
           classname="FarmFishPoolLayerConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishHandbookAwardConf" pbinname="FarmFishHandbookAwardConf"
           classname="FarmFishHandbookAwardConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmAquariumSeatConf" pbinname="FarmAquariumSeatConf"
           classname="FarmAquariumSeatConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishRarityConf" pbinname="FarmFishRarityConf"
           classname="FarmFishRarityConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmFishValueGuaranteedConf" pbinname="FarmFishValueGuaranteedConf"
           classname="FarmFishValueGuaranteedConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmFish" messagename="FarmActivityFishConf" pbinname="FarmActivityFishConf"
           classname="FarmActivityFishConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResUpdateForesight" messagename="UpdateForesightActivityExtraInfo"
            pbinname="UpdateForesightActivityExtraInfoData"
            classname="UpdateForesightActivityExtraInfoData"
            serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResNotice" messagename="BroadcastNoticeConfig" pbinname="BroadcastNoticeData" classname="BroadcastNoticeConfig"
           serverloadflag="ST_GameServer,ST_StarpServer,ST_StarpgroupServer" />

    <entry protofile="ResClub" messagename="PlayerHeatConf" pbinname="PlayerHeatConf" classname="PlayerHeatConf"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResMatchTypeGroup" messagename="MatchTypeGroupConf" pbinname="MatchTypeGroupConfData" classname="MatchTypeGroupConfData"
           serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResActivity" messagename="GroupingReturnConfig" pbinname="GroupingReturnData" classname="GroupingReturnData"
           serverloadflag="ST_ActivityServer" />
    <entry protofile="ResClubLog" messagename="ClubLogConf" pbinname="ClubLogConfData" classname="ClubLogConfData"
           serverloadflag="ST_GameServer,ST_ClubServer" />
    <entry protofile="ResActivityBookOfFriends" messagename="ActBookOfFriendsConf"
            pbinname="ActBookOfFriends" classname="ActivityBookOfFriendsConf"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityBookOfFriends" messagename="ActBookOfFriendsCreditConf"
            pbinname="ActBookOfFriendsCredit" classname="ActivityBookOfFriendsCreditConf"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResWolfKillSeasonReward" messagename="WolfKillSeasonRewardItem"
           pbinname="WolfKillSeasonRewardData" classname="WolfKillSeasonReward"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmBuildingRoomConf" pbinname="FarmBuildingRoomConf"
           classname="FarmBuildingRoomConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomConf" pbinname="FarmRoomConf"
           classname="FarmRoomConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomInitItem" pbinname="FarmRoomInitItem"
           classname="FarmRoomInitItem"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomExtendConf" pbinname="FarmRoomExtendConf"
           classname="FarmRoomExtendConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmItem" messagename="FarmRoomItemModelSizeConf" pbinname="FarmRoomItemModelSizeConf"
           classname="FarmRoomItemModelSizeConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomFurnitureConf"
            pbinname="FarmRoomFurnitureConf"
            classname="FarmRoomFurnitureConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomDoorWallPaperConf"
            pbinname="FarmRoomDoorWallPaperConf"
            classname="FarmRoomDoorWallPaperConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomItemLimitConf"
            pbinname="FarmRoomItemLimitConf"
            classname="FarmRoomItemLimitConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomFurnitureOperateLimitConf"
            pbinname="FarmRoomFurnitureOperateLimitConf"
            classname="FarmRoomFurnitureOperateLimitConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResIAA" messagename="IAAGuideToAppRewardConf"
            pbinname="IAAGuideToAppRewardConfData" classname="IAAGuideToAppRewardConfData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResBurdenReduceTask" messagename="BurdenReduceTaskConf"
            pbinname="BurdenReduceTaskConfData"
            classname="BurdenReduceTaskConfData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="BurdenReduceTaskConfData_default,BurdenReduceTaskConfData_season,BurdenReduceTaskConfData_trophy,BurdenReduceTaskConfData_reserved1,BurdenReduceTaskConfData_reserved2"/>
    <entry protofile="ResLevelDropArena" messagename="LevelDropArenaConfig"
            pbinname="LevelDropArenaData" classname="LevelDropArenaData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="LevelDropArenaData,LevelDropArenaData_hok,LevelDropArenaData_FB"/>
    <entry protofile="ResLevelDropArena" messagename="LevelDropArenaSpecialConfig"
            pbinname="LevelDropArenaSpecialData" classname="LevelDropArenaSpecialData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResFarmPet" messagename="FarmPetConf" pbinname="FarmPetConf"
            classname="FarmPetConf"
            serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResWishingTree" messagename="WishingTreeActivity"
            pbinname="WishingTreeActivityData" classname="WishingTreeActivityConf"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResWishingTree" messagename="WishingTreeGetRewardShow"
            pbinname="WishingTreeGetRewardShowData" classname="WishingTreeGetRewardShowConf"
            serverloadflag="ST_ActivityServer"/>

    <entry protofile="ResActivity" messagename="FarmSquadActivityConfig"
            pbinname="FarmSquadActivityConfigData"
            classname="FarmSquadActivityConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="FarmSquadActivityItemConfig"
            pbinname="FarmSquadActivityItemConfigData"
            classname="FarmSquadActivityItemConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="FarmSquadRewardTreeConfig"
            pbinname="FarmSquadRewardTreeConfigData"
            classname="FarmSquadRewardTreeConfigData"
            serverloadflag="ST_GameServer"/>

    <entry protofile="ResActivityMobaSquadDrawRedPackage"
            messagename="ActivityMobaSquadDrawRedPackageConfig"
            pbinname="ActivityMobaSquadDrawRedPackageData"
            classname="ActivityMobaSquadDrawRedPackageConf"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivityMobaSquadDrawRedPackage"
            messagename="ActivityMobaSquadDrawGetRewardShowConfig"
            pbinname="ActivityMobaSquadDrawGetRewardShowData"
            classname="ActivityMobaSquadDrawGetRewardShowConf"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivityMobaSquadDrawRedPackage" messagename="SquadActivityChatShareConfig"
            pbinname="SquadActivityChatShareData" classname="SquadActivityChatShareConf"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResAmusementParkActivity" messagename="AmusementParkActivityTaskConfig"
           pbinname="AmusementParkActivityTaskConfigData" classname="AmusementParkActivityTaskConfigConf"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResAmusementParkActivity" messagename="AmusementLimitRuleConfig"
           pbinname="AmusementLimitRuleConfig" classname="AmusementLimitRuleConfigConf"
           serverloadflag="ST_ActivityServer"/>

    <entry protofile="ResFishingHallOfFame" messagename="FishingHallOfFameConstData"
            pbinname="FishingHallOfFameConstData" classname="FishingHallOfFameConstDataConf"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResFishingHallOfFame" messagename="FishingHallOfFameRankData"
            pbinname="FishingHallOfFameRankData" classname="FishingHallOfFameRankDataConf"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResFishingHallOfFame" messagename="FishingHallOfFameTabConfig"
            pbinname="FishingHallOfFameTabConfig" classname="FishingHallOfFameTabConfig"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityRestaurantThemed" messagename="ActivityRestaurantThemedData" pbinname="ActivityRestaurantThemedData" classname="ActivityRestaurantThemedData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityRestaurantThemed" messagename="ActivityRestaurantThemedMiscData" pbinname="ActivityRestaurantThemedMiscData" classname="ActivityRestaurantThemedMiscData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivityRestaurantThemed" messagename="ActivityRestaurantThemedFoodData" pbinname="ActivityRestaurantThemedFoodData" classname="ActivityRestaurantThemedFoodData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResRewardRetrievalConf" messagename="RewardRetrievalConfData"
            pbinname="RewardRetrievalConfData" classname="RewardRetrievalConfDataConfig"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResRewardRetrievalConf" messagename="RewardRetrievalConfTaskData"
            pbinname="RewardRetrievalConfTaskData" classname="RewardRetrievalConfTaskDataConfig"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResBirthday" messagename="BirthdayMiscData" pbinname="BirthdayMiscData" classname="BirthdayMiscDataConfig"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResBirthday" messagename="BirthdayblessingData" pbinname="BirthdayblessingData" classname="BirthdayblessingDataConfig"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResBirthday" messagename="BirthdayCardConf" pbinname="BirthdayCardConf" classname="BirthdayCardConfConfig"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityTwoPeopleSquad" messagename="ActivityTwoPeopleSquadMiscData" pbinname="ActivityTwoPeopleSquadMiscData" classname="ActivityTwoPeopleSquadMiscConfig"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityInflateRedPacket" messagename="ActivityInflateRedPacketMiscData" pbinname="ActivityInflateRedPacketMiscData" classname="ActivityInflateRedPacketMiscConfig"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityInflateRedPacket" messagename="ActivityInflateRedPacketData" pbinname="ActivityInflateRedPacketData" classname="ActivityInflateRedPacketDataConfig"
           serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityConan" messagename="ActivityConanWarmupData"
            pbinname="ActivityConanWarmupData" classname="ActivityConanWarmupDataConf"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResUgcPropItem" messagename="T_UgcPropItemConfig" pbinname="UgcPropItemConfig"
            classname="UgcPropItemConfig"
            serverloadflag="ST_UgcdatastoreServer"/>
    <entry protofile="ResGuideApp" messagename="GuideAppConf"
            pbinname="GuideAppConfigData" classname="GuideAppConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResArenaSkinEffect" messagename="ArenaSkinEffectData"
            pbinname="ArenaSkinEffectData" classname="ArenaSkinEffectData"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaAttribute" messagename="ArenaHeroSystemData"
            pbinname="ArenaHeroSystemData" classname="ArenaHeroSystemData"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaLimitedTimeFreeHero" messagename="ArenaLimitedTimeFreeHero"
            pbinname="ArenaLimitedTimeFreeHeroData" classname="ArenaLimitedTimeFreeHeroData"
            serverloadflag="ST_ArenaServer"/>
    <entry protofile="ResBattle" messagename="BattleResultSyncRule"
            pbinname="BattleResultSyncRuleData" classname="BattleResultSyncRuleData"
            serverloadflag="ST_BattleServer"/>
    <entry protofile="ResFarmRoom"
            messagename="FarmRoomFurnitureTotalNumberLimitConf"
            pbinname="FarmRoomFurnitureTotalNumberLimitConf"
            classname="FarmRoomFurnitureTotalNumberLimitConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AIGCNPCPuzzleConf"
            pbinname="AIGCNPCPuzzle"
            classname="AIGCNPCPuzzle"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AIGCNPCSingConf"
            pbinname="AIGCNPCSingConf"
            classname="AIGCNPCSingConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AIGCNPCDanceConf"
            pbinname="AIGCNPCDanceConf"
            classname="AIGCNPCDanceConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcFriendChatConf"
            pbinname="AigcNpcFriendChat"
            classname="AigcNpcFriendChatData"
            serverloadflag="ST_GameServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AiNpcChatPushConf"
            pbinname="AiNpcChatPush"
            classname="AiNpcChatPushData"
            serverloadflag="ST_GameServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AiNpcChatPushTextConf"
            pbinname="AiNpcChatPushText"
            classname="AiNpcChatPushTextData"
            serverloadflag="ST_GameServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AiNpcChatPushRateConf"
            pbinname="AiNpcChatPushRate"
            classname="AiNpcChatPushRateData"
            serverloadflag="ST_GameServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AiNpcChatPushRateEventConf"
            pbinname="AiNpcChatPushRateEvent"
            classname="AiNpcChatPushRateEventData"
            serverloadflag="ST_GameServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AiNpcChatPushBattleEventConf"
            pbinname="AiNpcChatPushBattleEvent"
            classname="AiNpcChatPushBattleEventData"
            serverloadflag="ST_GameServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AiNpcChatPushHOKHighLightConditionConf"
            pbinname="AiNpcChatPushHOKHighLightCondition"
            classname="AiNpcChatPushHOKHighLightConditionData"
            serverloadflag="ST_GameServer,ST_AinpcServer"/>
    <entry protofile="ResLuckyTurntable" messagename="LuckyTurntableActivityConfig" pbinname="LuckyTurntableActivityConfig" classname="LuckyTurntableActivityConfig"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResLuckyTurntable" messagename="LuckyTurntableTurntableConfig" pbinname="LuckyTurntableTurntableConfig" classname="LuckyTurntableTurntableConfig"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResActivityWolfKillSquadTrophy" messagename="ActivityWolfKillSquadTrophyBaseInfo"
           pbinname="ActivityWolfKillSquadTrophyBaseData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivityDoubleTeam" messagename="ActivityDoubleTeamBaseInfo"
           pbinname="ActivityDoubleTeamBaseData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResFarmActivityGift" messagename="FarmActivityGiftConf"
            pbinname="FarmActivityGiftConf"
            classname="FarmActivityGiftConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmWeather" messagename="FarmWeatherConf" pbinname="FarmWeatherConf"
            classname="FarmWeatherConf"
            serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomFurnitureSkinConf"
            pbinname="FarmRoomFurnitureSkinConf"
            classname="FarmRoomFurnitureSkinConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom" messagename="FarmRoomFurnitureSkinMapConf"
            pbinname="FarmRoomFurnitureSkinMapConf"
            classname="FarmRoomFurnitureSkinMapConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmPet" messagename="FarmPetInteractionConf" pbinname="FarmPetInteractionConf"
           classname="FarmPetInteractionConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmPet" messagename="FarmPetClothingConf" pbinname="FarmPetClothingConf"
           classname="FarmPetClothingConf"
           splitsubtableflag="1" subtablename="FarmPetClothingConf_Suit,FarmPetClothingConf_Head,FarmPetClothingConf_Neck,FarmPetClothingConf_Body"
           serverloadflag="ST_GameServer,ST_FarmServer"/>

    <entry protofile="ResFarmLevelTitle" messagename="FarmLevelTitleConf" pbinname="FarmLevelTitleConf"
           classname="FarmLevelTitleConf"
           serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcConf"
            pbinname="AigcNpc"
            classname="AigcNpcData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcDressConf"
            pbinname="AigcNpcDress"
            classname="AigcNpcDressData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResArenaTip" messagename="ArenaTipConf" pbinname="ArenaTipConfData"
            classname="ArenaTipConfData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResArenaTip" messagename="ArenaTipItemConf" pbinname="ArenaTipItemConfData"
            classname="ArenaTipItemConfData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResArenaHeroOpen" messagename="ArenaHeroOpenData" pbinname="ArenaHeroOpenData"
           classname="ArenaHeroOpenConf"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaRegister" messagename="ArenaHeroRegisterData" pbinname="ArenaHeroRegisterData"
           classname="ArenaHeroRegisterConf"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResActivityMarquee" messagename="ActivityMarqueeConfData"
            pbinname="ActivityMarqueeConfData" classname="ActivityMarqueeConfData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResRaffle" messagename="RaffleBenefitSetCfg" pbinname="RaffleBenefitSetData"
            classname="RaffleBenefitSetData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            subtablename="RaffleBenefitSetData_main,RaffleBenefitSetData_tesewanfa,RaffleBenefitSetData_huoyue,RaffleBenefitSetData_hedy,RaffleBenefitSetData_miles,RaffleBenefitSetData_karl,RaffleBenefitSetData_silvester,RaffleBenefitSetData_yujun"/>
    <entry protofile="ResActivity" messagename="ThemeAdventureActivityConf"
            pbinname="ThemeAdventureActivityConfData" classname="ThemeAdventureActivityConfData"
            serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResFarmEvent" messagename="FarmEventTriggerParamProb" pbinname="FarmEventTriggerParamProb"
           classname="FarmEventTriggerParamProb"
           serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerConf" pbinname="FarmVillagerConf"
            classname="FarmVillagerConf"
            serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerInteractionConf"
            pbinname="FarmVillagerInteractionConf"
            classname="FarmVillagerInteractionConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerGiftDataConf"
            pbinname="FarmVillagerGiftDataConf"
            classname="FarmVillagerGiftDataConf"
            serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerGiftHouseDataConf"
           pbinname="FarmVillagerGiftHouseDataConf"
           classname="FarmVillagerGiftHouseDataConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerGiftWaterLayerDataConf"
           pbinname="FarmVillagerGiftWaterLayerDataConf"
           classname="FarmVillagerGiftWaterLayerDataConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerLimitConf"
           pbinname="FarmVillagerLimitConf"
           classname="FarmVillagerLimitConf"
           serverloadflag="ST_FarmServer"/>
      <entry protofile="ResFarmVillager" messagename="FarmVillagerFestivalGiftConf"
           pbinname="FarmVillagerFestivalGiftConf"
           classname="FarmVillagerFestivalGiftConf"
           serverloadflag="ST_FarmServer"/>
      <entry protofile="ResFarmVillager" messagename="FarmVillagerSpecialDialogueConf"
           pbinname="FarmVillagerSpecialDialogueConf"
           classname="FarmVillagerSpecialDialogueConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerExpressionConf"
           pbinname="FarmVillagerExpressionConf"
           classname="FarmVillagerExpressionConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmVillager" messagename="FarmVillagerShennongGiftConf"
           pbinname="FarmVillagerShennongGiftConf"
           classname="FarmVillagerShennongGiftConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResUGCEditor" messagename="UGCModifyPubCountConf"
            pbinname="UGCModifyPubCountConf" classname="UGCModifyPubCountConf"
            serverloadflag="ST_UgcServer"/>
    <entry protofile="ResLetsGoShopTag" messagename="NewShopTag" pbinname="NewShopTag"
            classname="ShopTagForLetsGoData" serverloadflag="ST_GameServer"/>

    <entry protofile="ResRanking" messagename="RankingPlatSwitchConf"
            pbinname="RankingPlatSwitchConfData"
            classname="RankingPlatSwitchConfData"
            serverloadflag="ST_RankServer"
            splitsubtableflag="1"
            subtablename="RankingPlatSwitchConfData_Season,RankingPlatSwitchConfData_History"/>
    <entry protofile="ResLuckyRebateActivity" messagename="LuckyRebateActivityConfig"
            pbinname="LuckyRebateActivityConfigData"
            classname="LuckyRebateActivityConfigData" serverloadflag="ST_GameServer"/>
    <entry protofile="ResLuckyRebateActivity" messagename="RebateConfig"
            pbinname="LuckyRebateConfigData"
            classname="LuckyRebateConfigData" serverloadflag="ST_GameServer"/>

    <entry protofile="ResPublicChat" messagename="CommunityChannelHotTopic"
            pbinname="CommunityChannelHotTopic"
            classname="CommunityChannelHotTopicData"
            serverloadflag="ST_GameServer"
            splitsubtableflag="1"
            rainbowenvdefault="0" rainbowgroup="CommunityChannelHotTopic.CommunityChannelHotTopic"
            subtablename="CommunityChannelHotTopic_Farm,CommunityChannelHotTopic_Arena,CommunityChannelHotTopic_WolfKill,CommunityChannelHotTopic_TradingCard"/>

    <entry protofile="ResBackpackItem" messagename="BackpackItemDressUpValueConf"
            pbinname="BackpackItemDressUpValueConfData"
            classname="BackpackItemDressUpValueConfData" serverloadflag="ST_GameServer"/>

    <entry protofile="ResClub" messagename="ClubLabelData"
           pbinname="ClubLabelData"
           classname="ClubLabelData"
           serverloadflag="ST_ClubServer"/>

    <entry protofile="ResActivity" messagename="WerewolfFullReducedConf"
           pbinname="WerewolfFullReducedConfig"
           classname="WerewolfFullReducedConfig" serverloadflag="ST_GameServer"/>

    <entry protofile="ResArenaMatchGroup" messagename="ArenaMatchGroup"
            pbinname="ArenaMatchGroupConf"
            classname="ArenaMatchGroupData" serverloadflag="ST_GameServer,ST_ArenaServer,ST_RoomServer"/>

    <entry protofile="ResArenaRandomEvent" messagename="ArenaRandomEventMatchOpen"
            pbinname="ArenaRandomEventMatchOpen" classname="ArenaRandomEventMatchOpenData"
            serverloadflag="ST_GameServer,ST_BattleServer"/>

    <entry protofile="ResArenaRandomEvent" messagename="ArenaRandomEventWeight"
            pbinname="ArenaRandomEventWeight" classname="ArenaRandomEventWeightData"
            serverloadflag="ST_GameServer,ST_BattleServer"/>

    <entry protofile="ResArenaRandomEvent" messagename="ArenaRandomEventConfig"
            pbinname="ArenaRandomEventConfig" classname="ArenaRandomEventConfigData"
            serverloadflag="ST_GameServer,ST_BattleServer"/>

    <entry protofile="ResActivityBookOfFriends" messagename="ActBookOfFriendsGiftConf"
           pbinname="ActBookOfFriendsGift"
           classname="ActivityBookOfFriendsGiftConf" serverloadflag="ST_ActivityServer"/>

    <entry protofile="ResFarmTalent" messagename="FarmTalentConf" pbinname="FarmTalentConf"
            classname="FarmTalentConf"
            serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResFarmTask" messagename="FarmTaskInfoConf" pbinname="FarmTaskInfoConf"
            classname="FarmTaskInfoConf"
            serverloadflag="ST_FarmServer"/>

    <entry protofile="ResLevelRoundRandomRule" messagename="LevelRoundABTestConf"
            pbinname="LevelRoundABTestConfData" classname="LevelRoundABTestConfData"
            serverloadflag="ST_BattleServer"/>

    <entry protofile="ResWolfKillComeBack" messagename="WolfKillComeBackItem"
            pbinname="WolfKillComeBackData" classname="WolfKillComeBack"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResRecharge" messagename="RechargeDoubleDiamondCfg" pbinname="RechargeDoubleDiamondCfg" classname="RechargeDoubleDiamondCfg"
            serverloadflag="ST_GameServer" />

    <entry protofile="ResFarmTask" messagename="FarmTaskBaseReward" pbinname="FarmTaskBaseReward"
            classname="FarmTaskBaseReward"
            serverloadflag="ST_FarmServer"/>

    <entry protofile="ResFarmHot" messagename="FarmHotConf" pbinname="FarmHotConf"
            classname="FarmHotConf"
            serverloadflag="ST_GameServer,ST_FarmServer"/>

    <entry protofile="ResFarmHot" messagename="FarmPartyKeywordWeightConf" pbinname="FarmPartyKeywordWeightConf"
            classname="FarmPartyKeywordWeightConf"
            serverloadflag="ST_GameServer,ST_FarmServer"/>

    <entry protofile="ResClub" messagename="ClubNewLabelData"
           pbinname="ClubNewLabelData"
           classname="ClubNewLabelData"
           serverloadflag="ST_ClubServer"/>

    <entry protofile="ResNR3E3Treasure" messagename="NR3E3TreasureItem" pbinname="NR3E3TreasureData"
            classname="NR3E3Treasure"
            serverloadflag="ST_GameServer"/>

    <entry protofile="ResNR3E3Treasure" messagename="NR3E3TreasureLevel1Item" pbinname="NR3E3TreasureLevel1Data"
            classname="NR3E3TreasureLevel1"
            serverloadflag="ST_GameServer"/>

    <entry protofile="ResNR3E3Treasure" messagename="NR3E3TreasureBlackListItem" pbinname="NR3E3TreasureBlackListData"
            classname="NR3E3TreasureBlackList"
            serverloadflag="ST_GameServer"/>

    <entry protofile="ResHOKRiftPower" messagename="HOKRiftPowerConf"
            pbinname="HOKRiftPowerConf" classname="HOKRiftPowerConf"
            serverloadflag="ST_GameServer"/>

    <entry protofile="ResActivity" messagename="TravelingDogConfData"
           pbinname="TravelingDogConfData" classname="TravelingDogConfData"
           serverloadflag="ST_ActivityServer"/>

    <entry protofile="ResReturningUser" messagename="ReturnPushFaceSettingData" pbinname="ReturnPushFaceSettingDataV2" classname="ReturnPushFaceSettingDataV2"
            serverloadflag="ST_GameServer" />

    <entry protofile="ResReturningUser" messagename="ReturnPushFaceReward" pbinname="ReturnPushFaceReward" classname="ReturnPushFaceReward"
            serverloadflag="ST_GameServer" />

    <entry protofile="ResActivity" messagename="TravelingDogWeightConf"
           pbinname="TravelingDogWeightConf" classname="TravelingDogWeightConf"
           serverloadflag="ST_ActivityServer"/>

    <entry protofile="ResDiscoveryGuidanceConf" messagename="DiscoveryGuidanceConf" pbinname="DiscoveryGuidanceConf"
           classname="DiscoveryGuidanceConfData"
           serverloadflag="ST_GameServer"/>

    <entry protofile="ResAppearanceCatalog" messagename="AppearanceRoad" pbinname="AppearanceRoadData"
           classname="AppearanceRoadData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResAppearanceCatalog" messagename="AppearanceCatalogMain" pbinname="AppearanceCatalogMainData"
           classname="AppearanceCatalogMainData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResAppearanceCatalog" messagename="AppearanceRoadLevelData" pbinname="AppearanceRoadLevelData"
           classname="AppearanceRoadLevelDataConfig"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResSuit" messagename="SuitThemedInfo" pbinname="SuitThemedInfoData"
           classname="SuitThemedInfoData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResSuit" messagename="SuitSeasonInfo" pbinname="SuitSeasonInfoData"
           classname="SuitSeasonInfoData"
           serverloadflag="ST_GameServer"/>

    <entry protofile="ResGeneral" messagename="PbinDataReplaceConf"
            pbinname="PbinDataReplaceConf"
            classname="PbinDataReplaceConfData"
            serverloadflag="ST_All"
            splitsubtableflag="0"
            rainbowenvdefault="0" rainbowgroup="PBIN_DATA_REPLACE_CONF" />

    <entry protofile="ResLevelInfo" messagename="RandEventRule"
            pbinname="RandEventRuleData"
            classname="RandEventRuleData"
            serverloadflag="ST_BattleServer"
            splitsubtableflag="0"
            subtablename="RandEventRuleData"/>

    <entry protofile="ResLevelInfo" messagename="RandEvent"
            pbinname="RandEventData"
            classname="RandEventData"
            serverloadflag="ST_BattleServer"
            splitsubtableflag="1"
            subtablename="RandEventData_CharBlessings,RandEventData_base,RandEventData_comp"/>

    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalPresetsConf"
            pbinname="AigcNpcPalPresetsConf"
            classname="AigcNpcPalPresetsConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalPersonalityTagConf"
            pbinname="AigcNpcPalPersonalityTagConf"
            classname="AigcNpcPalPersonalityTagConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalTimbreConf"
            pbinname="AigcNpcPalTimbreConf"
            classname="AigcNpcPalTimbreConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalCareerConf"
            pbinname="AigcNpcPalCareerConf"
            classname="AigcNpcPalCareerConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalTextPresetsConf"
            pbinname="AigcNpcPalTextPresetsConf"
            classname="AigcNpcPalTextPresetsConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalMoodEventConf"
            pbinname="AigcNpcPalMoodEventConf"
            classname="AigcNpcPalMoodEventConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalGenderConf"
            pbinname="AigcNpcPalGenderConf"
            classname="AigcNpcPalGenderConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalMBTIConf"
            pbinname="AigcNpcPalMBTIConf"
            classname="AigcNpcPalMBTIConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>
    <entry protofile="ResAIGCNPC"
            messagename="AigcNpcPalFavoriteMatchConf"
            pbinname="AigcNpcPalFavoriteMatchConf"
            classname="AigcNpcPalFavoriteMatchConfData"
            serverloadflag="ST_GameServer,ST_StreamServer,ST_AinpcServer"/>

    <entry protofile="ResFaction" messagename="ResFactionConfData" pbinname="ResFactionConfData" classname="ResFactionConfData"
            serverloadflag="ST_GameServer,ST_ActivityServer" />
    <entry protofile="ResChat" messagename="ChatTypeChatGroupConfigData"
            pbinname="ChatTypeChatGroupConfigData" classname="ChatTypeChatGroupConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChat" messagename="ChatModuleConfig"
            pbinname="ChatModuleConfigData" classname="ChatModuleConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChat" messagename="ChatTypeConfigData"
           pbinname="ChatTypeConfigData" classname="ChatTypeConfigData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResPushTopic" messagename="PushTopicEntry" pbinname="PushTopicConfig" classname="PushTopicConfData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="NewYearSignConfig" pbinname="NewYearSignData" classname="NewYearSignData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResUGCEditor" messagename="Item_UGCEditorMapType" pbinname="UGCEditorMapType" classname="UGCEditorMapType"
            serverloadflag="ST_GameServer,ST_UgcServer,ST_UgcplatServer,ST_UgcappServer" />
    <entry protofile="ResUGCEditor" messagename="Item_UGCEditorMapGroup" pbinname="UGCEditorMapGroup" classname="UGCEditorMapGroup"
            serverloadflag="ST_GameServer,ST_UgcServer,ST_UgcplatServer,ST_UgcappServer" />
    <entry protofile="ResUGCEditor" messagename="Item_UGCEditorMapModule" pbinname="UGCEditorMapModule" classname="UGCEditorMapModule"
            serverloadflag="ST_GameServer,ST_UgcServer,ST_UgcplatServer,ST_UgcappServer" />

    <entry protofile="ResActivity" messagename="FashionFundData" pbinname="FashionFundData" classname="FashionFundConfData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResBackpackItem" messagename="Item_ReadyBattleOrnamentationConf" pbinname="ReadyBattleOrnamentationDataConfData"
           classname="ReadyBattleOrnamentationDataConfData"
           serverloadflag="ST_GameServer,ST_RankServer,ST_ArenaServer,ST_BattleServer,ST_RoomServer"/>

    <entry protofile="ResActivity" messagename="FoodFestivalGoodConfData" pbinname="FoodFestivalGoodConfData" classname="FoodFestivalGoodConfData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="FoodFestivalElvesConfData" pbinname="FoodFestivalElvesConfData" classname="FoodFestivalElvesConfData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="FoodFestivalMadeConfData" pbinname="FoodFestivalMadeConfData" classname="FoodFestivalMadeConfData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="FoodFestivalAttrConfData" pbinname="FoodFestivalAttrConfData" classname="FoodFestivalAttrConfData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="FoodFestivalBigRewardConfData" pbinname="FoodFestivalBigRewardConfData" classname="FoodFestivalBigRewardConfData"
           serverloadflag="ST_ActivityServer"/>

    <entry protofile="ResFarmNPC" messagename="FarmNPCSerialConf" pbinname="FarmNPCSerialConf"
           classname="FarmNPCSerialConf"
           serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResFarmNPC" messagename="FarmGodFigureDropConf" pbinname="FarmGodFigureDropConfData"
           classname="FarmGodFigureDropConfData"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmNPC" messagename="FarmPrayToGodFigureResultConf" pbinname="FarmPrayToGodFigureResultConfData"
           classname="FarmPrayToGodFigureResultConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmLevel" messagename="FarmLevelConf" pbinname="FarmLevelConfData"
           classname="FarmLevelConfData"
           serverloadflag="ST_FarmServer,ST_GameServer"/>

    <entry protofile="ResCOCBuild" messagename="COCBuildingTypeConfig" pbinname="COCBuildingTypeConf" classname="COCBuildingTypeConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCBuild" messagename="COCBuildingLevelConfig" pbinname="COCBuildingLevelConf" classname="COCBuildingLevelConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCBuild" messagename="COCBuildingMiscConfig" pbinname="COCBuildingMiscConf" classname="COCBuildingMiscConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCBuild" messagename="COCBuildingAvatarEffectConf" pbinname="COCBuildingAvatarEffectConf" classname="COCBuildingAvatarEffectConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCBuild" messagename="COCSpecialBuildingBuildCostConfig" pbinname="COCSpecialBuildingBuildCostConf" classname="COCSpecialBuildingBuildCostConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCBuild" messagename="COCBuildingRevampConfig" pbinname="COCBuildingRevampConf" classname="COCBuildingRevampConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCBuild" messagename="COCBuildingRevampLevelConfig" pbinname="COCBuildingRevampLevelConf" classname="COCBuildingRevampLevelConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCBuff" messagename="COCNumPropBufferBaseValueConfig" pbinname="COCNumPropBufferBaseValueConf" classname="COCNumPropBufferBaseValueConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCItem" messagename="COCItemConfig" pbinname="COCItemConf" classname="COCItemConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCMatch" messagename="CocRankingConf" pbinname="CocRankingConfData" classname="CocRankingConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCMatch" messagename="CocRankingMiscConf" pbinname="CocRankingMiscConfData" classname="CocRankingMiscConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCMatch" messagename="CocMatchRobotConf" pbinname="CocMatchRobotConfData" classname="CocMatchRobotConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCMatch" messagename="CocMatchRobotTemplateConf" pbinname="CocMatchRobotTemplateConfData" classname="CocMatchRobotTemplateConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVersionCompat" messagename="CocVersionPool" pbinname="CocVersionPoolData" classname="CocVersionPoolData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCScience" messagename="CocScienceConf" pbinname="CocScienceConfData" classname="CocScienceConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCSoldier" messagename="CocSoldierConf" pbinname="CocSoldierConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCSoldier" messagename="CocSoldierCapacityConf" pbinname="CocSoldierCapacityConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCSoldier" messagename="CocSoldierMiscConf" pbinname="CocSoldierMiscConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCScience" messagename="CocScienceMiscConf" pbinname="CocScienceMiscConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCReward" messagename="CocTreasureBoxConf" pbinname="CocTreasureBoxConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCReward" messagename="CocRewardConf" pbinname="CocRewardConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCReward" messagename="CocTreasureBoxCapacityConf" pbinname="CocTreasureBoxCapacityConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCReward" messagename="CocFightSettleRewardConf" pbinname="CocFightSettleRewardConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCMisc" messagename="COCMiscConf" pbinname="COCMiscConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCMap" messagename="COCMapPrimaryRegionConfig" pbinname="COCMapPrimaryRegionConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCMap" messagename="COCMapMinorRegionConfig" pbinname="COCMapMinorRegionConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCDialogue" messagename="CocDialogueConf" pbinname="CocDialogueConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCDialogue" messagename="CocDialogConditionConf" pbinname="CocDialogConditionConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCPVEMode" messagename="COCCampaginConf" pbinname="COCCampaginConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCPVEMode" messagename="COCChallengeConf" pbinname="COCChallengeConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCGeneral" messagename="CocFeatureUnlockConf" pbinname="CocFeatureUnlockConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCTask" messagename="CocTaskConf" pbinname="CocTaskConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCTask" messagename="CocTaskListConf" pbinname="CocTaskListConfData" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCVillagerConfig" pbinname="COCVillagerConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCVillagerFavorConfig" pbinname="COCVillagerFavorConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCVillagerDialogueConfig" pbinname="COCVillagerDialogueConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCVillagerOfficialPosConfig" pbinname="COCVillagerOfficialPosConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCVillagerDressFashionValueConfig" pbinname="COCVillagerDressFashionValueConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCVillagerMiscConfig" pbinname="COCVillagerMiscConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCVillagerBattleHideRegionConfig" pbinname="COCVillagerBattleHideRegionConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCVillager" messagename="COCFriendVillagerDressFashionValueConfig" pbinname="COCFriendVillagerDressFashionValueConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCocMonthCard" messagename="CocMonthCardData" pbinname="CocMonthCardData" serverloadflag="ST_CocServer,ST_GameServer"/>
    <entry protofile="ResCOCPrison" messagename="COCPrisonMiscConfig" pbinname="COCPrisonMiscConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCPrison" messagename="COCPrisonLevelConf" pbinname="COCPrisonLevelConf" serverloadflag="ST_CocServer"/>
    <entry protofile="ResCOCProsperity" messagename="COCProsperityLevelConfig" pbinname="COCProsperityLevelConf" serverloadflag="ST_CocServer" />
    <entry protofile="ResCOCLevel" messagename="COCLevelDefenseConf" pbinname="COCLevelDefenseConf" serverloadflag="ST_CocServer" />
    <entry protofile="ResCOCLevel" messagename="COCLevelDefenseMisConf" pbinname="COCLevelDefenseMisConfData" serverloadflag="ST_CocServer" />
    <entry protofile="ResCocGM" messagename="CocGMUserCopyData" pbinname="CocGMUserCopyData" serverloadflag="ST_CocServer" />
    <entry protofile="ResActivity" messagename="ScoreGuideActivityData" pbinname="ScoreGuideActivityData" classname="ScoreGuideActivityData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="ScoreGuideConditionData" pbinname="ScoreGuideConditionData" classname="ScoreGuideConditionData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResActivity" messagename="ScoreGuideMidasData" pbinname="ScoreGuideMidasData" classname="ScoreGuideMidasData"
            serverloadflag="ST_GameServer" />

    <entry protofile="ResSpringSlip" messagename="ResSpringSlipConfData" pbinname="ResSpringSlipConfData" classname="ResSpringSlipConfData"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResSpringSlip" messagename="ResSpringSlipData" pbinname="ResSpringSlipData" classname="ResSpringSlipData"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResSpringSlip" messagename="ResSpringSlipAward" pbinname="ResSpringSlipAward" classname="ResSpringSlipAward"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResSpringSlip" messagename="ResSpringSynthesisAward" pbinname="ResSpringSynthesisAward" classname="ResSpringSynthesisAward"
            serverloadflag="ST_GameServer,ST_IdipServer" />
    <entry protofile="ResBattlePass" messagename="BattlePassTaskRewardConf" pbinname="BattlePassTaskRewardConf" classname="BattlePassTaskRewardConfData"
           serverloadflag="ST_GameServer" splitsubtableflag="1" subtablename="BattlePassTaskRewardConf_moba"/>
    <entry protofile="ResActivity" messagename="ActivityFarmRangeTaskConfig" pbinname="ActivityFarmRangeTaskData" classname="ActivityFarmRangeTaskData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="ActivityFarmReturningBuffConfig" pbinname="ActivityFarmReturningBuffData" classname="ActivityFarmReturningBuffData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="FarmReturningConfig" pbinname="FarmReturningMgrData" classname="FarmReturningMgrData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="FarmReturningLevelUpChallengeActivity" pbinname="FarmReturningLevelUpChallengeActivity" classname="FarmReturningLevelUpChallengeActivity"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResBackpackItem" messagename="ItemToyMiscConf" pbinname="ItemToyMiscConf" classname="ItemToyMiscConf"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="FarmReturningLevelUpChallengeConfig" pbinname="FarmReturningLevelUpChallengeConfig" classname="FarmReturningLevelUpChallengeConfig"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResMatch" messagename="MatchDynamicMinCntCfg" pbinname="MatchDynamicMinCntCfgData" classname="MatchDynamicMinCntCfgData"
            serverloadflag="ST_MatchServer"
            splitsubtableflag="1"
            subtablename="MatchDynamicMinCntCfgData_Mayday"/>
    <entry protofile="ResMisc" messagename="MiscFarmReturningConf" pbinname="MiscFarmReturningConf" classname="MiscFarmReturningConf"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivityMobaRandomVote" messagename="MobaRandomVoteConf" pbinname="MobaRandomVoteConfData" classname="MobaRandomVoteConfData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResMatchMMR" messagename="MatchMmrDegreeSideCorrectInfo" pbinname="MatchMmrDegreeSideCorrectData" classname="MatchMmrDegreeSideCorrectData"
            serverloadflag="ST_RoomServer" />
    <entry protofile="ResArenaShardMall" messagename="ArenaShardMallConf" pbinname="ArenaShardMallConf" classname="ArenaShardMallConf"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryMatchTypeGroup" pbinname="ChaseIDMasteryMatchTypeGroup" classname="ChaseIDMasteryMatchTypeGroup"
            serverloadflag="ST_GameServer,ST_BattleServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryBattleRankScore" pbinname="ChaseIDMasteryBattleRankScore" classname="ChaseIDMasteryBattleRankScore"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryBattleTeamRankScore" pbinname="ChaseIDMasteryBattleTeamRankScore" classname="ChaseIDMasteryBattleTeamRankScore"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryGlobalRankScore" pbinname="ChaseIDMasteryGlobalRankScore" classname="ChaseIDMasteryGlobalRankScore"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryScoreRank" pbinname="ChaseIDMasteryScoreRank" classname="ChaseIDMasteryScoreRank"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryMisc"
            pbinname="ChaseIDMasteryMisc" classname="ChaseIDMasteryMisc"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryLowAcceleration"
            pbinname="ChaseIDMasteryLowAcceleration" classname="ChaseIDMasteryLowAcceleration"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryProtection"
            pbinname="ChaseIDMasteryProtection" classname="ChaseIDMasteryProtection"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryBoss"
            pbinname="ChaseIDMasteryBoss" classname="ChaseIDMasteryBoss"
            serverloadflag="ST_GameServer,ST_BattleServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryPlayer"
            pbinname="ChaseIDMasteryPlayer" classname="ChaseIDMasteryPlayer"
            serverloadflag="ST_GameServer,,ST_BattleServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryUnlock"
            pbinname="ChaseIDMasteryUnlock" classname="ChaseIDMasteryUnlock"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryLimitFree"
            pbinname="ChaseIDMasteryLimitFree" classname="ChaseIDMasteryLimitFree"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIDMasteryOldPlayerUnlock"
            pbinname="ChaseIDMasteryOldPlayerUnlock" classname="ChaseIDMasteryOldPlayerUnlock"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIdentityBiographyConfig"
           pbinname="ChaseIdentityBiographyConfig" classname="ChaseIdentityBiographyConfigData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIdentityProficiencyConfig"
           pbinname="ChaseIdentityProficiencyConfig" classname="ChaseIdentityProficiencyConfigData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIdentityMvpAddProficiencyConfig"
           pbinname="ChaseIdentityMvpAddProficiencyConfig" classname="ChaseIdentityMvpAddProficiencyConfigData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseIDMastery" messagename="ChaseIdentityProficiencyMiscConfig"
           pbinname="ChaseIdentityProficiencyMiscConfig" classname="ChaseIdentityProficiencyMiscConfigData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="ActivityBICfg" pbinname="ActivityBIData"
            classname="ActivityBIData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseBossSkinConfig" messagename="BossSkinDataRow"
            pbinname="ChaseBossSkinConfig" classname="ChaseBossSkinConfig"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChasePropSkinConfig" messagename="PropSkinDataRow"
            pbinname="ChasePropSkinConfig" classname="ChasePropSkinConfig"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResChaseNewbieAbTest" messagename="ChaseNewbieAbTestFixedMapInfo" pbinname="ChaseNewbieAbTestFixedMapData"
           serverloadflag="ST_BattleServer" />
    <entry protofile="ResChaseNewbieAbTest" messagename="ChaseNewbieAbTestFixedRoleInfo" pbinname="ChaseNewbieAbTestFixedRoleData"
           serverloadflag="ST_BattleServer" />
    <entry protofile="ResChaseNewbieAbTest" messagename="ChaseNewbieAbTestGuideInfo" pbinname="ChaseNewbieAbTestGuideInfo"
            serverloadflag="ST_BattleServer" />
    <entry protofile="ResArenaABTestGroup" messagename="ArenaABTestGroupConf"
            pbinname="ArenaABTestGroupConf" classname="ArenaABTestGroupConf"
            serverloadflag="ST_BattleServer"/>
    <entry protofile="ResActivity" messagename="ActivityPuzzleConf"
            pbinname="ActivityPuzzleConfData" classname="ActivityPuzzleConfData"
            serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="ActivityPuzzleStageRewardConf"
            pbinname="ActivityPuzzleStageRewardConfData"
            classname="ActivityPuzzleStageRewardConfData"
            serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResUgcCreatorHomePage" messagename="UgcCreatorBadgeConf" pbinname="UgcCreatorBadgeConf" classname="UgcCreatorBadgeConf"
            serverloadflag="ST_GameServer,ST_UgcServer"/>
    <entry protofile="ResUgcCreatorHomePage" messagename="UgcCreatorTagConf" pbinname="UgcCreatorTagConf" classname="UgcCreatorTagConf"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResMoodPos" messagename="MoodPosData"
            pbinname="MoodPos" classname="ResMoodPosConf"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResArenaWeaponSkin" messagename="ArenaWeaponSkin"
           pbinname="ArenaWeaponSkin" classname="ArenaWeaponSkin"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>

    <entry protofile="ResNR3E8Task" messagename="NR3E8Task" pbinname="NR3E8TaskData" classname="NR3E8TaskData" serverloadflag="ST_GameServer"/>
    <entry protofile="ResNR3E8WeekActivity" messagename="NR3E8WeekActivity" pbinname="NR3E8WeekActivityData" classname="NR3E8WeekActivityData" serverloadflag="ST_GameServer"/>
    <entry protofile="ResNR3E8City" messagename="NR3E8City" pbinname="NR3E8CityData" classname="NR3E8CityData" serverloadflag="ST_GameServer"/>
    <entry modName="nr3e8" protofile="ResNR3E8Buff" messagename="NR3E8Buff" pbinname="NR3E8BuffData" classname="NR3E8BuffData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Buff" messagename="NR3E8BuffEffect" pbinname="NR3E8BuffEffectData" classname="NR3E8BuffEffectData"/>
    <entry modName="nr3e8" protofile="ResNR3E8BuildingBuff" messagename="NR3E8BuildingBuff" pbinname="NR3E8BuildingBuffData" classname="NR3E8BuildingBuffData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Building" messagename="NR3E8Building" pbinname="NR3E8BuildingData" classname="NR3E8BuildingData"/>
    <entry modName="nr3e8" protofile="ResNR3E8NewsEvent" messagename="NR3E8NewsEvent" pbinname="NR3E8NewsEventData" classname="NR3E8NewsEventData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Square" messagename="NR3E8Square" pbinname="NR3E8SquareData" classname="NR3E8SquareData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Dice" messagename="NR3E8DiceRandom" pbinname="NR3E8DiceRandomData" classname="NR3E8DiceRandomData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Dice" messagename="NR3E8DiceMultiple" pbinname="NR3E8DiceMultipleData" classname="NR3E8DiceMultipleData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Dice" messagename="NR3E8DiceMax" pbinname="NR3E8DiceMaxData" classname="NR3E8DiceMaxData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Dice" messagename="NR3E8DiceRecover" pbinname="NR3E8DiceRecoverData" classname="NR3E8DiceRecoverData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Dice" messagename="NR3E8DiceRewardMultiple" pbinname="NR3E8DiceRewardMultipleData" classname="NR3E8DiceRewardMultipleData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Shield" messagename="NR3E8ShieldMax" pbinname="NR3E8ShieldMaxData" classname="NR3E8ShieldMaxData"/>
    <entry modName="nr3e8" protofile="ResNR3E8Steal" messagename="NR3E8Steal" pbinname="NR3E8StealData" classname="NR3E8StealData"/>
    <entry modName="nr3e8" protofile="ResNR3E8AIBoard" messagename="NR3E8AIBoard" pbinname="NR3E8AIBoardData" classname="NR3E8AIBoardData"/>
    <entry modName="nr3e8" protofile="ResNR3E8VisitResource" messagename="NR3E8VisitResource" pbinname="NR3E8VisitResourceData" classname="NR3E8VisitResourceData"
           splitsubtableflag="1" subtablename="NR3E8VisitResourceData,NR3E8VisitResourceData_friend"/>
    <entry modName="nr3e8" protofile="ResNR3E8BackpackItem" messagename="NR3E8Item_BackpackItem" pbinname="NR3E8BackpackItemData" classname="NR3E8BackpackItemData"
           splitsubtableflag="1" subtablename="NR3E8BackpackItemData_treasure,NR3E8BackpackItemData_package,NR3E8BackpackItemData_building,NR3E8BackpackItemData_common,NR3E8BackpackItemData_skillCard" />
    <entry modName="nr3e8" protofile="ResNR3E8DailyGift" messagename="NR3E8DailyGift" pbinname="NR3E8DailyGiftData" classname="NR3E8DailyGiftData"/>
    <entry modName="nr3e8" protofile="ResNR3E8BuildingUpgradeLv" messagename="NR3E8BuildingUpgradeLv" pbinname="NR3E8BuildingUpgradeLvData" classname="NR3E8BuildingUpgradeLvData"/>
    <entry modName="nr3e8" protofile="ResNR3E8BuildingUpgradeStar" messagename="NR3E8BuildingUpgradeStar" pbinname="NR3E8BuildingUpgradeStarData" classname="NR3E8BuildingUpgradeStarData"/>
    <entry modName="nr3e8" protofile="ResNR3E8BuildingCombine" messagename="NR3E8BuildingCombine" pbinname="NR3E8BuildingCombineData" classname="NR3E8BuildingCombineData"/>
    <entry modName="nr3e8" protofile="ResNR3E8FriendCard" messagename="NR3E8FriendCard" pbinname="NR3E8FriendCardData" classname="NR3E8FriendCardData"/>
    <entry modName="nr3e8" protofile="ResNR3E8ForestTreasureGridBitMap" messagename="NR3E8ForestTreasureGridBitMap" pbinname="NR3E8ForestTreasureGridBitMapData" classname="NR3E8ForestTreasureGridBitMapData"/>
    <entry modName="nr3e8" protofile="ResNR3E8ForestTreasureTreasureBitMap" messagename="NR3E8ForestTreasureTreasureBitMap" pbinname="NR3E8ForestTreasureTreasureBitMapData" classname="NR3E8ForestTreasureTreasureBitMapData"/>
    <entry modName="nr3e8" protofile="ResNR3E8ForestTreasureSaveBitMap" messagename="NR3E8ForestTreasureSaveBitMap" pbinname="NR3E8ForestTreasureSaveBitMapData" classname="NR3E8ForestTreasureSaveBitMapData"/>
    <entry modName="nr3e8" protofile="ResNR3E8ForestTreasureItemInfo" messagename="NR3E8ForestTreasureItemInfo" pbinname="NR3E8ForestTreasureItemInfoData" classname="NR3E8ForestTreasureItemInfoData"/>
    <entry modName="nr3e8" protofile="ResNR3E8ForestTreasureGrid" messagename="NR3E8ForestTreasureGrid" pbinname="NR3E8ForestTreasureGridData" classname="NR3E8ForestTreasureGridData"/>
    <entry modName="nr3e8" protofile="ResNR3E8BuildingPlan" messagename="NR3E8BuildingPlan" pbinname="NR3E8BuildingPlanData" classname="NR3E8BuildingPlanData"/>
    <entry modName="nr3e8" protofile="ResNR3E8BuildingPlanType" messagename="NR3E8BuildingPlanType" pbinname="NR3E8BuildingPlanTypeData" classname="NR3E8BuildingPlanTypeData"/>
    <entry modName="nr3e8" protofile="ResNR3E8ChoiceEvent" messagename="NR3E8ChoiceEventBuff" pbinname="NR3E8ChoiceEventBuffData" classname="NR3E8ChoiceEventBuffData"/>
    <entry modName="nr3e8" protofile="ResNR3E8ChoiceEvent" messagename="NR3E8ChoiceEventMove" pbinname="NR3E8ChoiceEventMoveData" classname="NR3E8ChoiceEventMoveData"/>

    <entry protofile="ResClub" messagename="ClubScoreRule" pbinname="ClubScoreRule" classname="ClubScoreRule"
           serverloadflag="ST_GameServer,ST_ClubServer"/>
    <entry protofile="ResAlbum" messagename="AlbumLimitConditionConf" pbinname="AlbumLimitConditionData"
           classname="AlbumLimitConditionConfData"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResUgcMatchRoom" messagename="UgcMatchMiniGameAreaIdData" pbinname="UgcMatchMiniGameAreaIdData" classname="UgcMatchMiniGameAreaIdDataConf"
            serverloadflag="ST_UgcServer,ST_GameServer" />
    <entry protofile="ResTask" messagename="PasswordCodeTaskConf" pbinname="PasswordCodeTaskConfData" classname="PasswordCodeTaskConfData"
            serverloadflag="ST_GameServer" />
    <entry protofile="ResBackpackItem" messagename="GiftPackageOpenAnimationConfig" pbinname="GiftPackageOpenAnimationData" classname="GiftPackageOpenAnimationData" serverloadflag="ST_GameServer" />
    <entry protofile="ResReturningUser" messagename="ReturnStartMatchGiftConfig" pbinname="ReturnStartMatchGiftData" classname="ReturnStartMatchGiftData" serverloadflag="ST_GameServer" />
    <entry protofile="ResTask" messagename="TaskOptionalRewardConfig" pbinname="TaskOptionalRewardData" classname="TaskOptionalRewardData" serverloadflag="ST_GameServer" />

    <entry protofile="ResCompetitionReward" messagename="CompetitionRankRewardGroup" pbinname="CompetitionRankRewardData" classname="CompetitionRankRewardData"/>
    <entry protofile="ResCompetitionReward" messagename="CompetitionScoreRewardGroup" pbinname="CompetitionScoreRewardData" classname="CompetitionScoreRewardData"/>
    <entry protofile="ResCompetitionReward" messagename="CompetitionMedalRewardData" pbinname="CompetitionMedalRewardData" classname="CompetitionMedalRewardData"/>
    <entry protofile="ResMatch" messagename="MatchTypeRootConfig" pbinname="MatchTypeRootData" classname="MatchTypeRootData" serverloadflag="ST_GameServer,ST_RankServer,ST_BattleServer" />
    <entry protofile="ResArenaWeaponSkin" messagename="ArenaWeaponSkin"
           pbinname="ArenaWeaponSkin" classname="ArenaWeaponSkin"
           serverloadflag="ST_GameServer,ST_ArenaServer"/>

    <entry protofile="ResFeatureIntegration" messagename="FeatureIntegrationTaskExtConf" pbinname="FeatureIntegrationTaskExtData"
           classname="FeatureIntegrationTaskExtConfData"
           serverloadflag="ST_ActivityServer"/>

    <entry protofile="ResCookSysConf" messagename="CookSysConf" pbinname="CookSysConf" classname="CookSysConf"
           serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResFarmCookAvatar" messagename="FarmCookAvatarConf" pbinname="FarmCookAvatarConf"
           classname="FarmCookAvatarConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookVisitant" messagename="FarmCookVisitantGroupConf" pbinname="FarmCookVisitantGroupConf"
           classname="FarmCookVisitantGroupConf"
           serverloadflag="ST_GameServer,ST_FarmServer"/>

    <entry protofile="ResFarmCookDish" messagename="FarmCookDishConf" pbinname="FarmCookDishConf"
           classname="FarmCookDishConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookDish" messagename="FarmCookDishLevelConf" pbinname="FarmCookDishLevelConf"
           classname="FarmCookDishLevelConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookLevel" messagename="FarmCookLevelConf" pbinname="FarmCookLevelConf"
           classname="FarmCookLevelConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookLevel" messagename="FarmCookHearthConf" pbinname="FarmCookHearthConf"
           classname="FarmCookHearthConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookLevel" messagename="FarmCookSeatConf" pbinname="FarmCookSeatConf"
           classname="FarmCookSeatConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookLevel" messagename="FarmCookReceptionConf" pbinname="FarmCookReceptionConf"
           classname="FarmCookReceptionConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookLevel" messagename="FarmCookScreenConf" pbinname="FarmCookScreenConf"
           classname="FarmCookScreenConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookEmployee" messagename="FarmCookEmployeeAttrConf" pbinname="FarmCookEmployeeAttrConf"
           classname="FarmCookEmployeeAttrConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookEmployee" messagename="FarmCookEmployeeRefreshConf"
           pbinname="FarmCookEmployeeRefreshConf"
           classname="FarmCookEmployeeRefreshConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookEmployee" messagename="FarmCookEmployeeHighTypeConf" pbinname="FarmCookEmployeeHighTypeConf"
            classname="FarmCookEmployeeHighTypeConf" serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookLine" messagename="FarmCookLineBubbleConf" pbinname="FarmCookLineBubbleConf"
           classname="FarmCookLineBubbleConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmCookLine" messagename="FarmCookLineCommentLibraryConf"
           pbinname="FarmCookLineCommentLibraryConf"
           classname="FarmCookLineCommentLibraryConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFarmRoom"
           messagename="FarmRoomBuildingFunctionalFurnitureLimitConf"
           pbinname="FarmRoomBuildingFunctionalFurnitureLimitConf"
           classname="FarmRoomBuildingFunctionalFurnitureLimitConf"
           serverloadflag="ST_FarmServer"/>
    <entry protofile="ResFeatureIntegration" messagename="FeatureIntegrationIconConf" pbinname="FeatureIntegrationIconConfData"
           classname="FeatureIntegrationIconConfData"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResArenaGuideHero" messagename="ResArenaGuideHeroData"
            pbinname="ResArenaGuideHeroData" classname="ArenaGuideHeroConf"
            serverloadflag="ST_GameServer,ST_BattleServer"/>
    <entry protofile="ResArenaDailyVictoryChest" messagename="ResDailyVictoryChestData"
            pbinname="DailyVictoryChestData" classname="DailyVictoryChestConf"
            serverloadflag="ST_GameServer,ST_ArenaServer"/>
    <entry protofile="ResNR3E3MonthCardLevel" messagename="ResNR3E3MonthCardLevelItem"
            pbinname="NR3E3MonthCardLevelData" classname="NR3E3MonthCardLevel"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResNR3E3MonthCardFreeGift" messagename="ResNR3E3MonthCardFreeGiftItem"
            pbinname="NR3E3MonthCardFreeGiftData" classname="NR3E3MonthCardFreeGift"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResNR3E3MonthCardBuyLimit" messagename="ResNR3E3MonthCardBuyLimitItem"
            pbinname="NR3E3MonthCardBuyLimitData" classname="NR3E3MonthCardBuyLimit"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResNR3E3MonthCardPrivilege" messagename="ResNR3E3MonthCardPrivilegeItem"
            pbinname="NR3E3MonthCardPrivilegeData" classname="NR3E3MonthCardPrivilege"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="TreasureLevelUpData"
            pbinname="TreasureLevelUpData" classname="TreasureLevelUpData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResActivity" messagename="TreasureLevelUpBoxData"
            pbinname="TreasureLevelUpBoxData" classname="TreasureLevelUpBoxData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResLimitedExperienceItem" messagename="LimitedExperienceConf"
           pbinname="LimitedExperienceConf" classname="LimitedExperienceConf"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResGeneral" messagename="ApiAttrDataCtrlConf" pbinname="ApiAttrDataCtrlConfData" classname="ApiAttrDataCtrlConf"
           serverloadflag="ST_GameServer"
           rainbowenvdefault="0" rainbowgroup="res_data.mod_common.ApiAttrDataCtrlConf" />
    <entry protofile="ResGeneral" messagename="CsAdditionalDataConf" pbinname="CsAdditionalDataConfData" classname="CsAdditionalDataConf"
           serverloadflag="ST_GameServer"
           rainbowenvdefault="0" rainbowgroup="res_data.mod_common.CsAdditionalDataConf" />
    <entry protofile="ResGeneral" messagename="HeartBeatDataConf" pbinname="HeartBeatDataConfData" classname="HeartBeatDataConf"
           serverloadflag="ST_GameServer"
           rainbowenvdefault="0" rainbowgroup="res_data.mod_common.HeartBeatDataConf" />
    <entry protofile="ResMatch" messagename="TeamRecruitMatchItem"
            pbinname="TeamRecruitMatch" classname="TeamRecruitMatchData"
            serverloadflag="ST_GameServer,ST_RoomServer,ST_BattleServer"/>

    <entry protofile="ResMatch" messagename="MatchPakDetailItem"
            pbinname="MatchPakDetail" classname="MatchPakDetailData"
            serverloadflag="ST_RoomServer,ST_BattleServer"/>
    <entry protofile="ResMatch" messagename="MatchPakTypeItem"
            pbinname="MatchPakType" classname="MatchPakTypeData"
            serverloadflag="ST_RoomServer"/>
    <entry protofile="ResGeneral" messagename="ModPlayerDataConf" pbinname="ModPlayerDataConfData"
            classname="ModPlayerDataConf"
            serverloadflag="ST_GameServer"
            rainbowenvdefault="0" rainbowgroup="res_data.mod_common.ModPlayerDataConf"/>

    <entry protofile="ResWolfTeamChest" messagename="WolfTeamChestGiftConf"
            pbinname="WolfTeamChestGiftData"
            classname="WolfTeamChestGiftConfData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResWolfTeamChest" messagename="ActivityLotteryDrawMiscData"
           pbinname="ActivityLotteryDrawMiscData"
           classname="ActivityLotteryDrawMiscData"
           serverloadflag="ST_GameServer,ST_ActivityServer"/>
    <entry protofile="ResUgcEntranceBubble" messagename="UgcEntranceBubbleConf"
            pbinname="UgcEntranceBubbleConfData" classname="UgcEntranceBubbleConf"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResArenaCardRegulation" messagename="ArenaCardRegulationRank"
            pbinname="ArenaCardRegulationRank" classname="ArenaCardRegulationRank"
            serverloadflag="ST_ArenaServer"/>
    <entry protofile="ResArenaCardRegulation" messagename="ArenaCardRegulationRound"
            pbinname="ArenaCardRegulationRound" classname="ArenaCardRegulationRound"
            serverloadflag="ST_ArenaServer"/>
    <entry protofile="ResFarmSampleRoom" messagename="FarmSampleRoomConf"
            pbinname="FarmSampleRoomConf"
            classname="FarmSampleRoomConf"
            serverloadflag="ST_GameServer,ST_FarmServer"/>
    <entry protofile="ResActivity" messagename="ActivityConanIpCardConf"
            pbinname="ActivityConanIpCardData"
            classname="ActivityConanIpCardConfData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResTask" messagename="TaskRandomRewardPoolConf"
            pbinname="TaskRandomRewardPoolConfData" classname="TaskRandomRewardPoolConfData"
            serverloadflag="ST_GameServer" splitsubtableflag="1"
            subtablename="TaskRandomRewardPoolConfData_chase_daily"/>
    <entry protofile="ResTask" messagename="TaskRandomRewardConf"
            pbinname="TaskRandomRewardConfData" classname="TaskRandomRewardConfData"
            serverloadflag="ST_GameServer" splitsubtableflag="1"
            subtablename="TaskRandomRewardConfData_chase_daily"/>
    <entry protofile="ResActivity" messagename="SummerNavigationBarTaskData"
            pbinname="SummerNavigationBarTaskData"
            classname="SummerNavigationBarTaskData"
            serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResTradingCard" messagename="CardBagExtOutPutConfig"
            pbinname="CardBagExtOutPutData"
            classname="CardBagExtOutPutConfigData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResBI" messagename="BICfg" pbinname="BIData"
            classname="BIData"
            serverloadflag="ST_GameServer"/>
    <entry protofile="ResActivity" messagename="SummerFlashPhotoConf"
           pbinname="SummerFlashPhotoConf"
           classname="SummerFlashPhotoConf"
           serverloadflag="ST_ActivityServer"/>
    <entry protofile="ResUgcMgr" messagename="T_UgcCosMapping" pbinname="UgcCosMapping" classname="UgcCosMapping"
            serverloadflag="ST_All" />


    <entry protofile="ResMasterPath" messagename="MasterPathLockConfig" pbinname="MasterPathLockConfig" classname="MasterPathLockConfig"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResMasterPath" messagename="MainMaterPathStageConfig" pbinname="MainMaterPathStageConfig" classname="MainMaterPathStageConfig"
           serverloadflag="ST_GameServer"/>
    <entry protofile="ResMasterPath" messagename="MainMaterPathConfig" pbinname="MainMaterPathConfig" classname="MainMaterPathConfig"
           serverloadflag="ST_GameServer"/>



</resconfig>
