syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
// import "ResCommon.proto";
// import "google/protobuf/timestamp.proto";

message ItemStatusChangeConfig {
	option (resKey) = "id";
	optional int32 id = 1;                    // 道具ID
	repeated ItemStatusInfo statusInfo = 2;
}

message ItemStatusInfo {
	optional int32 	status 	= 1;
	optional int32 	itemId 	= 2;
	optional string	emitter	= 3;
	optional int32 	type 	= 4;
	optional BackpackItemStatusSwitchTriggerType triggerType = 5;	// 触发类型
}

message table_ItemStatusChangeData {
	repeated ItemStatusChangeConfig rows = 1;
}

message table_VehicleStatusChangeData {
	repeated ItemStatusChangeConfig rows = 1;
}