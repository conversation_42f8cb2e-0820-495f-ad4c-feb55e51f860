syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCondition.proto";
import "ResCommon.proto";
import "google/protobuf/timestamp.proto";

message GameModeReturnConfig{
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 gameModeId = 2; //玩法模式
  optional int32 gameModeGroup = 3; //玩法组  只在玩法回流中使用
  optional int32 order         = 4; //排序
  optional google.protobuf.Timestamp beginTime = 5; // 开始时间
  optional google.protobuf.Timestamp endTime = 6; // 结束时间
  optional string minVersion = 7; //最低版本号
  optional string maxVersion = 8;  //最高版本号
  repeated int32 stepIds = 9;     //阶段ID  @deprecated
  optional int32 mailId = 10;     //结束后未领取的阶段奖励邮件
  repeated ResConditionGroup triggerCondition = 11; //触发条件 @deprecated
  optional int32 triggerTaskGroup = 12; //触发任务组 @deprecated
  repeated int32 startCheckerConf = 13; //开始检测器
  repeated int32 endCheckerConf   = 14; //结束检测器
  optional int32 startCheckerCompare  = 15; //开始检测是 或且
  optional int32 endCheckerCompare    = 16; //结束检测是 或且
  optional string WaitWidgetName = 17;  //未开启子界面名字
  optional string OpenWidgetName = 18;  //已开启子界面名字
  optional string ReturnGameModeName    = 19; //玩法模式名称
  optional string ReturnActivityTitle    = 20; //玩法回流标题
  optional string ReturnActivitySubtitle    = 21; //玩法回流副标题
  optional int32 PushFaceId    = 22; //拍脸id
  optional int32 jumpId = 23; //跳转id
  optional string jumpParams = 24; //跳转参数
  optional string jumpTitle = 25; //跳转标题
  optional string jumpText = 26; //跳转文本
  optional string bannerUrl = 27; //跳转banner
  optional int32 redDotType = 28; //红点类型
  optional int32 PassThroughIconBgType = 29; //透传底图颜色
  optional string PassThroughIcon = 30; //透传图片
  optional string PassThroughDesc = 31; //透传描述
  optional int32 gameTypeId = 32; //游戏类型id 链接到W_玩法类型_根节点总表.xlsx
  optional int32 showTitleTag = 33;// 页签小标题
  optional int32 canCompleteCount = 34; // 可完成次数 0 代表无限次
  optional int32 vaild = 35; //是否生效
  optional int32 gameOtherId = 37; //其他玩法识别id
  optional int32 buffId = 38;  //回流BUFF
  optional string BuffOutsideDesc = 39;  //BUFF外显描述
  optional string BuffInsideDesc = 40;  //BUFF详细描述
  optional string buffTime = 41;  //buff持续天数
  repeated int32 PushFaceTime    = 42; //拍脸时机
  repeated int32 AddActivityId = 43; //额外接入活动id
  repeated int32 commonTaskGroup = 44; //生效任务组
  repeated int32 currencyCfg = 45; //右上角货币配置
  optional string BuffOutsideTitle = 46;  //BUFF外显窗口标题
  optional int32 ProgressGroupID = 47;  //进度任务组id
  optional int32 ProgressBigIndex = 48;  //进度任务大奖索引
  repeated int32 mutexActivityId = 49; //互斥活动id
  optional int32 PushFaceType = 50; //拍脸类型区分
  optional string tableIcon = 51; //tab按钮图标
  optional string BubbleText = 52; //气泡提示文本
}

message Table_GameModeReturnConf{
  repeated GameModeReturnConfig rows = 1;
}

message GameModeReturnStepConfig{
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 order  = 2;
  repeated int32 taskGroup = 3;
  repeated string titleName = 4;
  optional ResConditionGroup unlockCondition = 5; //开启条件
  optional int32 unlockTaskGroup = 6; //解锁任务组
  optional int32 isBigStep = 7; //大奖标识
}

message Table_GameModeReturnStepConf{
  repeated GameModeReturnStepConfig rows = 1;
}

message GameModeReturnCheckConfig{
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 checkType = 2;
  repeated int32 checkParam1  = 3;
  repeated int32 checkParam2 = 4;
  repeated int32 checkParam3 = 5;
}

message Table_GameModeReturnCheckConf{
  repeated GameModeReturnCheckConfig rows = 1;
}

enum GameTypeId{
  GMTID_Unknown = 0;
  GMTID_WOLF = 2000[(name) = "狼人"];
  GMTID_FARM = 20000[(name) = "农场"];
  GMTID_UGC = 100000[(name) = "UGC"];
}

enum GameModeReturnCheckType{
  GMRCT_Unknown = 0;
  GMRCT_AlreadyPlayGameMode         = 1001[(name) = "玩过X玩法"];
  GMRCT_NotPlayerGameModeInterval   = 1002[(name) = "未玩过X玩法间隔天数"];
  GMRCT_GameModeReturnGroup         = 1003[(name) = "Y天未触发X玩法回流组"];
  GMRCT_TaskFinish                  = 1004 [(name) = "任务完成"];
  GMRCT_AfterOpenGameReturnDay      = 1005[(name) = "回流开启X天之后"];
  GMRCT_PlayerLevel                 = 1006[(name) = "玩家等级"];
  GMRCT_PlayerTotalLoginDay         = 1007[(name) = "玩家累计登录天数"];
  GMRCT_PlayerTotalCups             = 1008[(name) = "玩家累计获得奖杯"];
  GMRCT_UnlockGameMatchTypeId       = 1009[(name) = "玩家解锁X玩法"];
  GMRCT_UnDrawWolfGift              = 1010[(name) = "未领取狼人回流奖励中的身份奖励"];
  GMCRT_FarmLevel                   = 1011[(name) = "农场等级"];
  GMCRT_Never_PlayerGameMode       = 1012[(name) = "未玩过X玩法"];
}

enum GameModeReturnGameOtherId{
  GMRGID_Unknown = 0;
  GMRGID_UGC = 1[(name) = "UGC"];
  GMRGID_FARM = 2[(name) = "农场"];
}

enum GameModeCheckerCompareType{
  GMCCT_And = 1;
  GMCCT_Or = 0;
}

//玩法回流拍脸
message GameReturnPushFaceData{
  option (resKey) = "id";
  optional int32 id = 1;
  optional string LeftTitleName    = 2; //拍脸左侧标题文本
  optional int32 IsShowSeeDetails    = 3; //是否展示查看详细
  optional string RightTitleName    = 4; //右侧回流奖励标题
  optional string RightTitleText    = 5; //奖励副标题文本
  repeated int32 RewardIdList    = 6; //展示奖励id
  repeated int32 RewardNumList    = 7; //展示奖励数量
  repeated int32 IsBigRewardList    = 8; //大奖展示
  optional string PushDownBtnText    = 9; //拍脸下方按钮文本
  optional string ReturnDownBtnText    = 10; //大回流中开启页下方按钮文本
  optional string ReturnGameModeBg    = 11; //玩法回流背景图
  repeated string ReturnGameNewTextShow    = 12; //全新玩法文本展示
  optional int32 RulesId    = 13; //规则文本表id
  optional string RewardUpText    = 14; //规则文本表id
  optional string PushDownBtnText1    = 15; //拍脸下方按钮文本1
  optional string PushDownBtnText2    = 16; //拍脸下方按钮文本2
  optional string PushDownBtnText3    = 17; //拍脸下方按钮文本3
  optional string PushDownBtnText4    = 18; //拍脸下方按钮文本4
  repeated int32 insRewardId    = 19; //见面礼奖励道具
  repeated int32 insRewardNum    = 20; //见面礼奖励数量
  optional string PushFaceView = 21;  //拍脸界面ui
}

message table_GameReturnPushFaceData{
  repeated GameReturnPushFaceData rows = 1;
}


message GameModeSortConf{// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 showPriority = 2; // 组内排序条件
  repeated int32 modeNum = 3; // 小标题显示数量
  repeated int32 identifyId = 4; // 玩法识别id
  optional int32 maxShowNum = 5; // 最多显示预告数量
  optional int32 maxShowDays = 6; // 预告最多持续天数
  optional int32 showCd = 7; // 预告冷却天数

}

message Table_GameModeSortConf{// @noSvr
  repeated GameModeSortConf rows = 1;
}

message GameModeReturnProgressRewardConfig{
  option (resKey) = "gameModeReturnId,progressId";
  optional int32 gameModeReturnId = 1;
  optional int32 progressId = 2;
  optional int32 progress = 3;   //进度
  repeated Item rewardItem = 4; //奖励
  optional int32 isBigReward = 5; //大奖标识
  optional string RewardGetDesc = 6; //奖励获得描述
}

message table_GameModeReturnProgressRewardData{
  repeated GameModeReturnProgressRewardConfig rows = 1;
}

//客户端数据KV配置
message GameReturnClientMiscData{
  option (resKey) = "Key";
  optional string Key = 1;
  optional string Value = 2;                    //值 
}

message table_GameReturnClientMiscData{
  repeated GameReturnClientMiscData rows = 1;
}
