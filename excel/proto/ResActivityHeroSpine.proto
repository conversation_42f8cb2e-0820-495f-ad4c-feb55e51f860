syntax = "proto2";
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCommon.proto";

message ActivityHeroSpineConfData {
  option (resKey) = "id";
  optional int32 id = 1;         // 索引id
  optional string spineName = 2;    // spine名称
  optional string spinePos = 3;    // Spine坐标
  optional string skeletonData = 4;    // skeletonData
  optional string initialAnimation = 5; //默认动画
  optional string spineSizeXY = 6;  //Spine尺寸
}

message table_ActivityHeroSpineConfData {
  repeated ActivityHeroSpineConfData rows = 1;
}