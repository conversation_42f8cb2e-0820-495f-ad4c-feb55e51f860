syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCondition.proto";
import "ResCommon.proto";
import "google/protobuf/timestamp.proto";
import "ResAreaPropRep.proto";


message Item_BackpackItem {
  option (resKey) = "id";
  optional int32 id = 1;                    // 道具ID
  optional bool effect = 2;                 // 是否生效 @noSvr
  optional ItemType type = 3;               // 道具类型
  optional int32 stackedNum = 4;            // 堆叠数量
  optional int64 maxNum = 5;                // 最大拥有数量
  repeated Item exceedReplaceItem = 6;      // 上限后替换道具
  repeated Item expiredReplaceItem = 7;     // 过期后替换道具
  optional string lowVer = 8;               // 最低版本号table_JumpConfig
  optional string highVer = 9;              // 最高版本号
  optional int32 quality = 10;              // 道具品级
  optional string name = 11;                // 道具名称
  optional string desc = 12;                // 道具描述
  optional string icon = 13;                // 图标路径
  optional string picture = 14;             // 道具大图片
  optional string getWay = 15;              // 获取途径 @noSvr
  repeated int32 jumpId = 16;               // 获取途径跳转ID @noSvr
  optional int32 bagId = 17;                // 所在背包id @noSvr
  optional int32 sort = 18;                 // 排序优先级 @noSvr
  optional ItemUseTypeOperate useType = 19; // 使用类型
  repeated int32 useParam = 20;             // 使用参数
  //  optional google.protobuf.Timestamp timeLimitRange = 21; // deadline类型的有效期 @noSvr
  //  optional int32 timeLimitExpire = 22;      // 从获得道具起的剩余秒数 @noSvr
  optional PackageConf packageConf = 23;    // 礼包信息
  optional OutlookConf outlookConf = 24;    // 外观类道具配置
  optional CrownConf crownConf = 25;        // 皇冠配置 @noSvr
  optional MovementConf movementConf = 26;  // 动作配置
  optional ResourceConf resourceConf = 27;  // 资源配置 @noSvr
  optional int32 commodityId = 28;          // 商品id
  optional FaceEmotionConf faceEmotionConf = 29;          // 商品id
  optional string pictureUrl = 30;          // 图标CDN路径
  optional string outEnter = 31;            // 入场动画资源 @noSvr
  optional string outIdle = 32;             // 待机动画资源 @noSvr
  optional string outShow = 33;             // 表演动画资源 @noSvr
  optional int32 outShowIntervalTime = 34;  // 待机动画播放时长 @noSvr
  optional int32 scaleTimes = 35;  // 获得界面展示放大倍数 @noSvr
  optional string outPropEnter = 36;            // 道具入场动画资源 @noSvr
  optional string outPropIdle = 37;             // 道具待机动画资源 @noSvr
  optional string outPropShow = 38;             // 道具表演动画资源 @noSvr
  optional string outPropSkeletal = 39;         // 道具骨骼资源 @noSvr
  optional EmojiConf emojiConf = 40;        //  表情配置
  repeated int32 soundId = 41;        //  橙装特效id @noSvr
  optional string outIdle_pv = 42;        //  橙装待机动画资源 @noSvr
  optional int32 plusjumpId = 43;         // 右上角加号跳转ID @noSvr
  optional string outEnterSequence = 44;         // PV入场动画 @noSvr
  optional int32 showInView = 45;        // 是否出现在展示列表中 @noSvr
  repeated string shareTexts = 46;        // 商品获得时分享气泡配置 @noSvr
  optional string shareAnim = 47;        // 商品获得时分享使用的动作，用来动态截取 @noSvr
  optional int64 ObjConfID = 48; // 关联小窝物件ID
  optional string ringEffect = 49; // 橙装环绕特效 @noSvr
  optional int32 isDynamic = 50; // 是否是动态的 @noSvr
  optional int32 billboardOffsetZ = 51; // 时装的头部UI偏移 @noSvr
  optional string minVer = 52; // 最低版本号
  optional bool seasonExpire = 53;//@noCli 是否赛季过期道具
  optional google.protobuf.Timestamp beginTime = 54;//开始时间
  optional google.protobuf.Timestamp endTime = 55;//结束时间
  optional string suitStoryTextKey = 56; //@noSvr 皮肤故事介绍
  optional float timeToStartAnim = 57; //@noSvr 分享动作开始播放时间
  optional bool bHideInBag = 58; //@noSvr 是否在背包隐藏
  optional bool bNotCostCount = 59; //@noSvr 是否消耗数量
  optional string bubbleColor = 60; //@noSvr 气泡框颜色
  optional string shareBackground = 61; //@noSvr 橙装分享背景图
  optional float idleEffect = 62; //@noSvr 模型待机特效播放间隔
  optional int32 rotateYaw = 63; //@noSvr 载具预览旋转角度
  optional string sharePic = 64; //@noSvr 分享图片
  optional int32 suitId = 65; // 所属套装Id
  optional string suitName = 66; // 所属套装名字
  optional int32 themedId = 67; // 所属主题Id
  optional int32 bpShowId = 68; // @noSvr umg内对应Id
  optional int32 seasonId = 69; // 所属赛季Id
  optional string suitIcon = 70; // @noSvr 所属套装图标
  optional string pakGroup = 72; // 包组
  optional bool forbidRent = 73; // 是否禁用租借
  optional string displayBP = 74; // 入场展示BP
  optional string shareNamePic = 75; // 分享图片角色名称图
  optional string shareBubblePic = 76; // 分享图片分享气泡框图
  optional QualifyingCardConf qualifyingCardConf = 77; // 段位升星保护配置
  optional bool noShowTipsNum = 78; // 是否不显示tip上的数量
  optional ArenaHeroTryCardConf arenaHeroTryCardConf = 79; // arena英雄体验卡
  optional bool lotteryRewardShow = 80; //@noSvr 祈愿-奖励预览界面是否有按钮
  optional bool lotteryPreviewShow = 81; //@noSvr 祈愿-奖励兑换界面是否有按钮
  optional bool mallHotShow = 82; //@noSvr 商城-热销界面是否有按钮
  optional bool mallAvatarShow = 83; //@noSvr 商城-装扮界面是否有按钮
  optional string showDesc = 84; //@noSvr 显示文本（炫彩字用）
  optional string wolfBpPic = 85; //@noSvr 狼人bp大图片
  repeated int32 shareOffset = 86;  // 分享界面模型偏移值
  repeated int32 previewShareOffset = 87;  // 奖励模型偏移值
  optional bool bHideEmpty = 88; // 数量为空时隐藏
  optional int32 chunkGroupId = 89; // 所属分包Id
  optional int32 profileRotateYaw = 90; // 个人信息页旋转角度
  optional google.protobuf.Timestamp newShelvesBeginTime = 91;//上新开始时间
  optional google.protobuf.Timestamp newShelvesEndTime = 92;//上新结束时间
  optional bool autoUse = 93; // 获取后自动使用
  optional bool orangeSuitShow = 94;//@noSvr 手持物个性化界面是否显示
  repeated int32 tags = 95; // 特殊标记
  optional string buff = 96; // 道具增益
  optional string buffValue = 97; // 道具增益数值
  optional string buffViewOffset = 98; // 道具增益展示偏移值
  optional bool enableDecompose = 99; // 可分解
  repeated Item decomposeItem = 100; // 分解后道具
  repeated int32 placeholder_101 = 101; //  占位
  optional int32 decomposeRetainNum = 102;// 分解需要保留的数量
  optional string gotDesc = 103;//@noSvr 获取提示语
  optional ItemBagType bagType = 104; // 背包类型
  optional bool bEnableDoubleShow = 105; //@noSvr 是否双人展示
  optional int32 placeholder_106= 106; //  占位
  optional int32 placeholder_107= 107; //  占位
  optional CustomPreviewConf customPreviewConf = 108;  //  定制化预览模型
  optional int32 activateColorTaskId = 109;	// 染色激活条件任务
  optional NR3EDecorate nr3eDecorate = 110; // nr3e装饰
  optional ItemPersonalityStateConf personalityStateConf = 111;   // 个性化-状态道具配置
  optional string selfhoodBgRes = 112; // 个性化-背景
  optional int32 isShowPopRewardView = 113; //@noSvr 是否显示获取物品框
  repeated string previewSocketNames = 114; //@noSvr 预览的socket名字列表
  optional int32 buffSecondLevel =115; //二级buff解锁等级
  optional string buffSecond =116;     //二级增益
  optional string buffSecondValue =117;//二级buff增益数值
  optional bool needBindLua = 118; //@noSvr 是否有特殊逻辑，billboard绑定lua
  optional int32 mallviewWeaponTargetScale = 119; // 躲猫猫商城里被击角色模型缩放
  optional string mallviewWeaponBulletOffset = 120; // 躲猫猫商城里子弹偏移
  optional int32 mallviewNR3EDisguiseEffectScale = 121; // 躲猫猫商城隐藏特效缩放
  optional int32 perfWeight = 122; //载具开销权重
  optional bool isUsePurchaseDyeing = 123; //用于染色道具跳转
  optional string BackgroundBP = 124; //@noSvr 天幕BP
  optional int32 SeatingCount = 125; //@noSvr 载具座位数
  repeated int32 SkillIds = 126; //@noSvr 载具技能
  optional int32 bodytype = 127;	// @noSvr 套装体型
  optional int32 firstNoSkip = 128; //@noSvr 首次播放PV不跳过
  optional google.protobuf.Timestamp expireTime = 129; //道具过期时间
  optional int32 shareScaleTimes = 130;  // 分享界面展示放大倍数 @noSvr
  optional string jumpToast =131;     //跳转时提示
  repeated string outInteractive = 132; //@noSvr 模型互动动画
  optional int32 shareRotateYaw = 133; //@noSvr 载具分享旋转角度
  repeated string outInteractivePV = 134; //@noSvr 模型互动动画PV
  optional string outIdleShow_pv = 135; //@noSvr 模型待机表演动画PV
  optional ShareConfig shareConfig = 136;     //分享礼包配置
  repeated string shareChatTypes = 137; //分享的频道内容列表
  optional string shareChatWords = 138; //分享语
  optional string miniIcon = 139;  // 道具极小图片展示 @noSvr
  optional bool isNewVehicle = 140;  //是否新老载具 @noSvr
  repeated int32 vehicleSeatOffset = 141;  //载具座位偏移  @noSvr
  optional ChaseSkin chaseSkin = 142;//大王玩法皮肤配置
  optional bool suitInvalid = 143; // 套装是否可用(记录进时装图鉴)
  optional string skipButtonText = 144;  // 跳转按钮文本 @noSvr
  repeated KeyValueInt32 ThemedShowIdList = 145;  //主题对应蓝图ID组
  repeated KeyValueInt32 SeasonShowIdList = 146;  //赛季对应蓝图ID组
  optional int32 featureId = 147; // 玩法id
  optional string descForActivity = 148; // 道具描述针对活动 @noSvr
  repeated int32 surroundingsEffectId = 149;	// 环绕物模型特效
  optional int32 surroundingsDisplayType = 150;	//环绕物界面展示类型（0=基础特效， 1=idleShow特效）
  optional int32 level = 151; // 皮肤道具级别
  optional float FootwearHeight = 152; //高跟鞋高度
  optional int32 srcPakGroupId = 153; //@noSvr 活动资源所在的pak包组Id
  optional int32 useJumpId = 154; //使用后的跳转  @noSvr
  optional string customDesc = 155; // 道具描述Moba并显示在通用恭喜获得 @noSvr
  optional string nr3e3ExpVocationIcon = 156; //@noSvr 狼人体验身份道具图标
  optional float vatShowTime = 157; //脚印显示时间
  repeated string animationParam = 158;// 宝箱动画参数
  optional int32 isShowCommonPopRewardView = 159; //@noSvr 是否显示获取通用的物品框
  optional int32 postProcessLogic = 160; // 后处理逻辑 @noSvr
  repeated int32 isBigReward = 161; // 是否是大奖
  repeated int32 mallJumpId = 162;  //跳转Id  @noSvr
  repeated string jumpText = 163;  //跳转描述  @noSvr
  optional int32 isDailyBattleDefaultItem = 164;  // 是否是天天晋级赛的默认道具
  optional int32 gotoButtonJumpId = 165; //跳转按钮ID
  optional int32 suitSkillId = 166; //套装技能id
  repeated int32 DecorateIds = 167; //@noSvr 载具装饰
  optional string gotoButtonJumpText = 168; //跳转按钮文本
  optional VehicleDecorateSpecialBonus VehicleDecorateValues = 169; //@noSvr 装饰物的数值
  optional int32 previewShareRotateYaw = 170; //@noSvr 获得界面旋转
  optional int32 isLock = 171; // 是否解锁 @noSvr
  optional int32 default = 172; // 是否默认 @noSvr
  optional int32 platformID = 173; //平台对应图标标识  @noSvr
  optional google.protobuf.Timestamp useBeginTime = 174;//开始时间 @noSvr
  optional google.protobuf.Timestamp useEndTime = 175;//结束时间 @noSvr
  optional ResConditionGroup resCompleteConditionGroup = 176; // 完成条件组 @noSvr
  optional int32 actorID = 177; //身份ID
  optional string actorName = 178; // 身份名字
  optional int32 useInLoca = 179; // 可以在运动中使用
}

message ShareConfig{
  optional int32 type = 1; //所属分享类别
  optional int32 rewardLimit = 2; //领取上限
  optional int32 rewardTarget = 3; //分享礼包可打开领奖对象  0所有人 1仅自己 2仅好友
  optional int32 playerRewardLimit = 4;	// 分享礼包单用户单个礼包领奖次数
  optional string bigIcon = 5;	// 分享礼包界面 大icon
  optional string recordIcon = 6;	// 分享记录、领取记录界面 icon
  optional string getDetIcon = 7;	// 领取详情界面 icon
  optional string chatIcon = 8;	// 结构化消息 icon
  optional int32 limitId = 9;	// 对应礼包上限id表
  optional string desc = 10;	// 礼包专属描述
  optional int32 mailId = 11;	// 补发邮件id
  optional int32 shareExpireType = 12;         // 分享后领取有效期 1：:x小时以内 2：X天以内（24点）3：固定时间点
  optional int32 shareExpireParam = 13;       // 分享后领取有效期参数
  optional google.protobuf.Timestamp shareExpireTimeParam = 14;          // 分享后领取有效期时间参数
  optional int32 stackedNum = 15;	// 分享礼包单独的堆叠数量
}

message NR3EDecorateModel
{
  optional string               path          = 1;  // 模型路径
  optional int32                type          = 2;  // 模型类型
  optional int32                scale         = 3;  // 模型缩放
  optional string               offset        = 4;  // 模型偏移
  optional string               offsetInGame  = 5;  // 局内模型偏移
  optional int32                rotateYaw     = 6;  // 预览旋转角度
}

message NR3EDecorateEffect
{
  optional string               path          = 1;  // 特效路径
  optional int32                scale         = 2;  // 特效缩放
  optional string               offset        = 3;  // 特效偏移
  optional float                startTime     = 4;  // 特效开始时间
  optional float                duration      = 5;  // 特效持续时间
}

message NR3EDecoratePack
{
  optional NR3EDecorateModel    model         = 1;  // 模型
  optional NR3EDecorateEffect   effect        = 2;  // 特效
  optional string               anim          = 3;  // 动画
  optional string               classPath     = 4;  // 类路径
  optional string               materialPath  = 5;  // 材质路径
  optional string               soundId       = 6;  // 音效ID
}

message NR3EDecorate
{
  optional NR3EDecoratePack     pack1         = 1;
  optional NR3EDecoratePack     pack2         = 2;
  optional NR3EDecoratePack     pack3         = 3;
  optional NR3EDecoratePack     pack4         = 4;
  optional NR3EDecoratePack     pack5         = 5;
  optional NR3EDecoratePack     pack6         = 6;
}

message ChaseSkin
{
  optional int32 ChaseSkinID = 1; //大王皮肤表ID
  optional google.protobuf.Timestamp AvailableBeginTime =2; //皮肤可获取时间
  optional google.protobuf.Timestamp AvailableEndTime =3; //皮肤可获取结束时间
  optional string UnavailableHintText = 4; //不可获取时的提示语
  optional string NormalIcon = 5; //普通图标
  optional string GrayIcon = 6; //灰态图标
}

message PackageConf {
  optional PackageType packageType = 1; // 礼包类型
  repeated int32 itemIds = 2;           // 礼包道具ID
  repeated int32 itemNums = 3;          // 礼包道具数量
  repeated int32 expireDays = 4;        // 礼包道具有效期（天）
  optional bool isAutoUse = 5;          //@noCli 礼包是否自动使用
  optional int32 presentLabel = 6;      // 赠送标签位置
  optional PackageRandomConf packageRandomConf = 7; //随机礼包配置
  optional PackagePickConf packagePickConf = 8; // 自选礼包配置
  repeated google.protobuf.Timestamp expireTimestamps = 9;        // 礼包道具有效期（时间戳）
  optional int32 animationNtf = 10; // 是否有开箱动画 还需要根据ItemChangeReson 判断
}

// 随机礼包配置
message PackageRandomConf {
  repeated int32 rangeWeight = 1;       // 礼包随机道具权重
  repeated int32 guaranteeItemIds = 2; // 保底道具
  repeated int32 guaranteeTimes = 3; // 触发保底次数
  repeated bool ownedFilter = 4;   // 已拥有过滤 0-1
  repeated int32 drawLimitTimes = 5;   // 可抽取次数
  optional bool indexGuarantee = 6; // 下标作为保底key
  repeated int32 guaranteeHitTimes = 7; // 保底触发次数
  repeated int32 minDrawItemIds = 8;  // 最小抽取次数限制Key
  repeated int32 minDraw = 9;         // 最小抽取次数
}

// 自选礼包配置
message PackagePickConf {
  optional int32 pickNum = 1;           // 自选道具数量
  repeated int32 pickLimit = 2;         // 自选道具选取上限
}

message OutlookConf {
  optional int32 belongTo = 1;          // 套装染色底版itemId
  optional int32 fashionValue = 2;
  repeated AttrTypeValue dressUpAttr = 3;
  optional int32 groupId = 4;  //外观组ID
  repeated int32 belongToGroup = 5; //@noSvr 套装染色底版组名
}
message CrownConf {
  optional string specialEffect = 1;    // 特效
}

enum ActionAudio3PType {
  Only1P = 1;
  Only3P = 2;
  Both1P3P = 3;
}

message MovementConf {
  optional string action = 1;
  optional string action2 = 2;
  optional int32  assetType = 3;
  optional string attachBP = 4;
  optional int32 actionDistance = 5;
  optional bool isLoop = 6;
  optional string effectName = 7;
  optional int32 effectPosition = 8;
  optional int32 actionType = 9; //  1-单人动作，2-双人动作，0-spine表情, 3-放置类型道具
  optional int32 needFriendIntimate = 10; //  好友亲密度
  optional float actionLength = 11; // 动作播放时长
  optional bool isBlend = 12; // 是否融合当前动作
  optional int32 animBlendType = 13;  //  动作融合方式
  optional int32 actionAudioFXID = 14; // 音效ID
  optional ActionAudio3PType actionAudio3PType = 15; // 音效类型
  optional string actionAudioText = 16; // 音效文本
  optional bool needActionConfirm = 17; // 是否默认需要确认（无视开关关闭）
  optional string previewPageAction1 = 18; //预览界面动作
  optional string previewPageAction2 = 19; //预览界面NPC动作(双人)
  optional string action_3_5 = 20;  //  3.5头身单人动作
  optional string action_3to3_5 = 21;  //  3头身对3.5头身左侧玩家动作
  optional string action_3_5to3 = 22;  //  3.5头身对3头身左侧玩家动作
  optional string action_3_5to3_5 = 23;  //  3.5头身3.5头身左侧玩家动作
  optional string action2_3to3_5 = 24;  //  3头身对3.5头身右侧玩家动作
  optional string action2_3_5to3 = 25;  //  3.5头身对3头身右侧玩家动作
  optional string action2_3_5to3_5 = 26;  //  3.5头身3.5头身右侧玩家动作
  optional bool moveWhenPerformBan = 27; // 单人动作移动黑名单
  optional int32 subActionType = 28; //  1.推图 2.痛包 3.吧唧
}
message ResourceConf {
  optional string model = 1;    //套装 or 套装-头
  optional string skinIcon = 2;
  optional string showPic = 3;
  optional string bluePrint = 4;
  optional string run = 5;
  optional string jump = 6;
  optional string enter = 7;
  optional string bg = 8;
  optional string upper = 9;        // 套装-上装
  optional string bottom = 10;      // 套装-下装
  optional string gloves = 11;      // 套装-手套
  optional string face = 12;      // 套装-脸
  optional string material = 13;      // 染色材质
  repeated int32 headOffset = 14;   // 头部挂点偏移
  repeated int32 backOffset = 15;   // 背部挂点偏移
  repeated int32 faceOffset = 16;   // 面部挂点偏移
  optional string physics = 17;
  optional string emitter = 18;
  optional int32 upperType = 19;// 一体上装类型：1.单件上装 2.二合一上装 3. 三合一上装
  optional int32 modelType = 20;// 模型资源类型
  optional string idleAnim = 21;// 物理模型的待机动作
  optional string surroundEmitter = 22;  // 旋转特效
  optional string materialSlot = 23;  // 材质插槽
  repeated int32 extraOffset = 24;  // 配饰额外偏移值
  repeated string TextLabelsId = 25; // 外观文本标签ID
  optional int32 IconLabelId = 26; // 外观icon标签ID
  optional string cdnBg = 27; // cdn背景
  optional bool bBlendWithHandHoldIdleAnim = 28;  //  当装备当前道具时，角色的idle动画是否需要和手持物的idle动画进行融合
  optional int32 customLabelId = 29; // 自定义外观标签ID
  optional int32 customLabelSlot = 30; // 自定义外观标签位置
  optional string skeletalMesh = 31; //指定骨格
  optional string animClassName = 32; //指定动画CLASS
  optional string cdnFileName = 33; // cdn文件名
  optional bool bForbidPhysicalAnimation = 34;    //是否禁用物理
  optional string skinBPPath = 35;  // 皮肤蓝图路径
  repeated string suitPhysicalAnimationWhiteList = 36;    //物理的套装白名单列表
  optional string idleAnim_3_5 = 37;// 3.5头身物理模型的待机动作
  optional string simulateBoneName = 38;//
}

message FaceEmotionConf {
  optional string textureBaseColor = 1;    //套装 or 套装-头
  optional string textureNE = 2;
  optional string textureARM = 3;
  repeated string textureBaseColorTag = 4;
}

message EmojiConf {
  optional int32 emojiType = 1; //  表情类型:1-普通表情，2-Spine表情
  optional string spineAtlas = 2; //  spine动画图集
  optional string spineSkeleton = 3;  //  spine动画骨骼
  optional int32 showInChat = 4; //  是否在聊天框中展示
  optional int32 emojiSubType = 5; // 表情类型
}

message QualifyingCardConf {
  optional int32 priority = 1; // 优先级
  repeated int32 teamMemberRequire = 2; // 组队人数
  repeated QualifyingCardInfo qualifyingCardInfo = 3; // 可用玩法&段位
}

message QualifyingCardInfo {
  optional int32 matchType = 1; // 玩法ID
  optional int32 rangeLeft = 2; // 段位分区间
  optional int32 rangeRight = 3; // 段位分区间
}

message table_BackpackItem {
  repeated Item_BackpackItem rows = 1;
}

message BackpackItemQuality {// @noSvr
  option (resKey) = "quality";
  optional int32 quality = 1;
  optional string desc = 2;
}

message table_BackpackItemQuality {// @noSvr
  repeated BackpackItemQuality rows = 1;
}


message BackpackItemEffectData {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional string name = 2;
  repeated string pos = 3;
  repeated string posEffect = 4;
  optional int32 conditionType = 5;//触发特效条件关系规则(交集1\并集2)
  repeated int32 conditionValues = 6;//触发特效条件参数(时装ID组)
  repeated int32 posEffectEnable = 7;
}

message table_BackpackItemEffectData { // @noSvr
  repeated BackpackItemEffectData rows = 1;
}

message SurroundingsEffectData {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;		// 特效ID
  optional int32 type = 2;		// 特效类型（0=基础特效， 1=idleShow特效）
  optional int32 soundId = 3;	// idleShow音效ID
  optional bool bIsFollow = 4;   // 环绕物是否跟随
  optional bool bIsMutuallyExclusive = 5; // 环绕物是否与时装环绕物互斥
  optional float speed = 6;		// 跟随速度
  optional float maxSpeed = 7;	//最大跟随速度
  optional float maxDistance = 8; //最远跟随距离
  optional string pos = 9;		// 骨骼
  optional string posEffect = 10;	// 骨骼特效
  optional int32 conditionType = 11;//触发特效条件关系规则(交集1\并集2)
  repeated int32 conditionValues = 12;//触发特效条件参数(时装ID组)
  repeated int32 posEffectEnable = 13;
  optional string posOnVehicle = 14; //角色处于载具上时，环绕物绑定的角色骨骼
}

message table_SurroundingsEffectData { // @noSvr
  repeated SurroundingsEffectData rows = 1;
}

message HandHoldConf {
  optional string itemIdleAnim = 1; //  手持物的idle动画
  optional string itemShowAnim = 2; //  手持物的主动动画
  optional string characterBlendAnim = 3; // 手持物的角色移动时的融合动画
  optional string characterShowAnim = 4;  //  手持物的角色主动动画
  optional string handholdGearBP = 5; //  手持物机关BP
  optional int32 handholdGearRadius = 6;  //  手持物机关半径
  optional int32 handholdMaxCount = 7;  //  手持物最大机关个数
  optional string characterIdleBlendAnim = 8;  //角色idle时融合的动画
  optional string characterMoveBlendAnim = 9;  //角色移动时融合的动画
  optional int32 CD= 10; //  召唤机关的CD
  optional string CollisionInfo = 11;  //  碰撞信息
  optional bool bForceBlendAnim = 12; //  强制开启动画融合
  optional string characterJumpBlendAnim = 13;  //  角色跳跃时融合的动画
  optional string characterShowAnim_3_5 = 14;  //  3.5头身手持物的角色展示动画
  optional string itemShowAnim_3_5 = 15;  //  3.5头身手持物的展示动画
  optional string characterBlendAnim_3_5 = 16; // 3.5头身手持物的角色移动时的融合动画
  optional string characterIdleBlendAnim_3_5 = 17;  //3.5头身角色idle时融合的动画
  optional string characterJumpBlendAnim_3_5 = 18;  //3.5头身角色跳跃时融合的动画
  optional string handholdLogicBP = 19; //  手持物逻辑BP
}

message SuitPreviewOffsetZ {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;//道具ID
  optional int32 zOffset = 2;//角色z轴位移高度
}

message table_SuitPreviewOffsetZ { // @noSvr
  repeated SuitPreviewOffsetZ rows = 1;
}


message BackpackItemVideoData {// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;//道具ID
  optional bool bShowVideo = 2;//小窗视频是否展示（1展示0不展示）
  optional bool bAutoPlay = 3;//小窗视频是否自动播放（1自动0不自动）
  optional bool bVideoLoop = 4;//小窗视频播放后是否循环播放（1循环0不循环）
  optional string videoURL = 5;//视频链接
  repeated int32 hideVideoModules = 6;//屏蔽显示模块列表（默认显示，指定模块不显示）
  optional bool bBreakCodition = 7;//CommonComp_Preview组件视频是否展示（1展示0不展示）
  optional string videoBGURL = 8;//视频未播放时的预览画面图片url
  optional int32 VideoPlayMode = 9;//视频播放模式(1.弹窗，2.全屏，3.内嵌播放-小窗，4.内嵌播放，5.浮窗播放)
  optional string videoName = 10;//视频显示名称
  optional google.protobuf.Timestamp openTime = 11;//开放时间
  optional int32 questID = 12;//对应任务
}

message table_BackpackItemVideoData { // @noSvr
  repeated BackpackItemVideoData rows = 1;
}

message ArenaHeroTryCardConf {
  optional bool autoUse = 1;
  optional int32 duration = 2;
  optional int32 exchangeItemId = 3;
  optional int32 exchangeItemNum = 4;
}

message CustomPreviewConf { // 定制预览模型
  optional string BP = 1; //  定制模型的蓝图
  optional float Scale = 2;   //  定制模型的大小

  optional string CharIdleAnim = 3; // 定制预览模型下 人物的Idle动画
  optional int32 CharScale = 4; // 人物的缩放 如果绑定在预览模型上 则是人物的相对缩放
  repeated int32 CharOffset = 5; // 人物的偏移 如果绑定在预览模型上 则是人物的相对偏移
  optional int32 CharRotateYaw = 6; // 人物的Yaw 如果绑定在预览模型上 则是人物的相对Yaw

  repeated int32 SepcialDataInt32 = 7;// 非常特殊的字段 业务逻辑自行解析
}

message ItemPersonalityStateConf {
}

message BackpackItemDressUpValueConf {
  option (resKey) = "type";
  optional ItemType type = 1;
  optional bool involved = 2;
  optional int32 filter = 3;
  repeated int32 filterArgs = 4;
}

message table_BackpackItemDressUpValueConf {
  repeated BackpackItemDressUpValueConf rows = 1;
}

message MainGameRankingTitleData {// @noSvr
  //废弃
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 minRank = 2; //最小名次
  optional int32 maxRank = 3; //最大名次
  optional int32 fontSize = 4;  //文字大小
  optional string textMaterial = 5; //文字材质
  optional string outText = 6;
  optional string inText = 7;
}

message table_MainGameRankingTitleData {  // @noSvr
  //废弃
  repeated MainGameRankingTitleData rows = 1;
}

message Item_VehicleLevelup {// @noSvr
	option (resKey) = "vehicleId";
	optional int32 vehicleId = 1; //载具ID
	optional int32 stageId = 2; //阶段
	optional int32 canLevel = 3; //是否可升级
	optional int32 nextLevel = 4; //下一级载具
	optional int32 costId = 5; //升级消耗道具，商品载具升级表ID
	optional int32 groupId = 6; //组ID
	optional string location = 7; //位置
	optional string rotation = 8; //旋转
	optional int32 scale = 9; //缩放
	optional int32 coinTypeId = 10;//消耗货币
	optional int32 coinNum = 11;//消耗货币数量
}

message table_VehicleLevelup {// @noSvr
  repeated Item_VehicleLevelup rows = 1;
}

message Item_VehicleSkill {// @noSvr
	option (resKey) = "skillId";
	optional int32 skillId = 1; //技能ID
	optional int32 bShowVideo = 2; //是否展示视频
	optional int32 isNew = 3; //是否是新技能
	optional string specialName = 4; //特殊技能名（显示用）
	optional string desc = 5; //特殊技能描述
	optional string icon = 6; //图标
	optional string videoURL = 7; //视频
	optional int32 skillCd = 8; //技能CD
	repeated int32 unlockItemId = 9; //解锁道具ID
	optional int32 unlockType = 10; // 技能解锁条件类型
}

message table_VehicleSkill {// @noSvr
  repeated Item_VehicleSkill rows = 1;
}

message Item_VehicleDecorateMesh {// @noSvr
	option (resKey) = "id";
	optional int32 id = 1; //配饰ID
	optional string modelName = 2; //模型名称
	optional int32 modelType = 3; //模型类型
	optional string nodeName = 4; //模型挂点
	optional string location = 5; //位置
	optional string rotation = 6; //旋转
	optional string scale = 7; //缩放
}

message table_VehicleDecorateMesh {// @noSvr
  repeated Item_VehicleDecorateMesh rows = 1;
}

message Item_HeadWearAndSuitDisplay {// @noSvr
	option (resKey) = "id";
	optional int32 headWearId = 1; //头饰
	optional int32 suitItemId = 2; //衣服
	repeated int32 gameElementIndex = 3; //游戏内材质
	repeated int32 uiElementIndex = 4; //界面材质
	optional int32 id = 5;
}

message table_HeadWearAndSuitDisplay {// @noSvr
  repeated Item_HeadWearAndSuitDisplay rows = 1;
}

message  table_ReadyBattleOrnamentationDataConfData {
  repeated Item_ReadyBattleOrnamentationConf rows = 1;
}

message  Item_ReadyBattleOrnamentationConf {
  option (resKey) = "id";
  optional ItemType type = 1;               // 道具类型
  optional int32 id = 2;                  // 道具ID
  optional int32 backPackageId = 3;       // 背包类型
  repeated int32 matchType = 4;          // 可用玩法
  repeated int32 qualifyingDsGameType = 5;     // 战斗DS类型
  optional int32 lowVer = 6;             // 最低版本号
  optional int32 highVer = 7;            // 最高版本号

}


message  table_ResShareGiftType {
  repeated ResShareGiftType rows = 1;
}


message  ResShareGiftType {
  option (resKey) = "typeId";
  optional int32 typeId = 1;               // 分享礼包类别
  optional int32 shareExpireType = 2;         // 废弃,移动到分享道具表,分享后领取有效期 1：:x小时以内 2：X天以内（24点）3：固定时间点
  optional int32 shareExpireParam = 3;       // 废弃,移动到分享道具表,分享后领取有效期参数
  optional google.protobuf.Timestamp shareExpireTimeParam = 4;          // 废弃,移动到分享道具表,分享后领取有效期时间参数
  optional int32 shareLimit = 5;                   // 废弃,同类别礼包分享上限
  optional int32 shareRewardLimit = 6;             // 废弃,同类别礼包领取上限
  optional int32 rewardRefresh = 7;                // 废弃,同类别礼包领取上限刷新周期1：每日刷新；2：每周刷新；3：不刷新
  optional int32 shareRefresh = 8;                 // 废弃,同类别礼包分享上限刷新周期1：每日刷新；2：每周刷新；3：不刷新
  optional int32 historyResetType = 9;              // 记录清空类别 0:X天内清理  1.每周清空 2.固定时间点清空
  optional int32 historyRecentDays = 10;            // X天内清理值
  optional google.protobuf.Timestamp historyExpireTime = 11;          // 固定时间定清空时间参数
  optional string bigKvBg = 12;	// 分享礼包界面 大kv图
  optional string recordKvBg = 13;	// 分享记录、领取记录界面 kv图
  optional string getDetKvBg = 14;	// 领取详情界面 kv图
  optional int32 helpJumpId = 15;   // 帮助跳转id
  optional string name = 16;	// 礼包类别名称
  optional string shareMaxNum = 17;	// 同类别分享上限提示
  optional string getMaxNum = 18;	// 同类别领取上限提示
  optional string shareHint = 19;	// 分享提示
}


message  table_ResShareGiftLimit {
  repeated ResShareGiftLimit rows = 1;
}



message  ResShareGiftLimit {
  option (resKey) = "limitId";
  optional int32 limitId = 1;               // 上限id
  optional int32 typeId = 2;               //  所属分享礼包类别
  optional int32 shareLimit = 3;                   // 分享上限(老逻辑兼容,与下面新增的同时生效)
  optional int32 shareRewardLimit = 4;             // 领取上限(老逻辑兼容,与下面新增的同时生效)
  optional int32 rewardRefresh = 5;                // 领取上限刷新周期1：每日刷新；2：每周刷新；3：不刷新(老逻辑兼容,与下面新增的同时生效)
  optional int32 shareRefresh = 6;                 // 分享上限刷新周期1：每日刷新；2：每周刷新；3：不刷新(老逻辑兼容,与下面新增的同时生效)
  optional string shareMsgTag = 13;	    // 分享次数提示文本（对应文本表）
  optional string getMsgTag = 14;	      // 领取次数提示文本（对应文本表）
  optional int32 order = 15;            // 排序id
  optional string shareMaxNum = 16;	// 分享上限提示（对应文本表）
  optional string getMaxNum = 17;	// 领取上限提示（对应文本表）
  optional int32 dayShareLimit = 20;                   // 日分享上限
  optional int32 weekShareLimit = 21;                   // 周分享上限
  optional int32 allShareLimit = 22;                   // 总分享上限
  optional int32 dayRewardLimit = 23;                   // 日分享上限
  optional int32 weekRewardLimit = 24;                   // 周分享上限
  optional int32 allRewardLimit = 25;                   // 总分享上限
  optional string dayShareMaxNum = 26;	    // 日分享上限提示（对应文本表）
  optional string weekShareMaxNum = 27;	    // 周分享上限提示（对应文本表）
  optional string totalShareMaxNum = 28;	  // 总分享上限提示（对应文本表）
  optional string dayGetMaxNum = 29;	      // 日领取上限提示（对应文本表）
  optional string weekGetMaxNum = 30;	      // 周领取上限提示（对应文本表）
  optional string totalGetMaxNum = 31;	    // 总领取上限提示（对应文本表）
}

message GiftPackageOpenAnimationConfig {
  option (resKey) = "itemChangeReason";
  optional int32 itemChangeReason = 1;
  optional bool animationNtf = 2; // 废弃
  optional bool ignoreAnimationNtf = 3;
}

message table_GiftPackageOpenAnimationData {
  repeated GiftPackageOpenAnimationConfig rows = 1;
}

message VehicleDecorateSpecialBonus{
  optional bool HasSpecialJet = 1;     // 是否影响jet数值
  optional Vector JetForce = 2;     // jet推力(X,Y,Z)
  optional float JetKeepTime = 3;   // jet持续时间
}

message ItaBagItem {
  option (resKey) = "id";
  optional int32 id = 1;              // 痛包道具ID
  repeated int32 badgeList = 2;       // 可使用吧唧道具组
  repeated float minBadgeSize = 3;    // 单个吧唧最小可缩放的矩阵像素尺寸
  repeated float maxBadgeSize = 4;    // 单个吧唧最大可缩放的矩阵像素尺寸
  optional string outSidePic = 5;     // 外围图片
  optional string inSidePic = 6;      // 内部图片
  optional int32 maxBadgeCount = 7;   // 最大允许放置的吧唧数量
}

message table_ItaBag {
  repeated ItaBagItem rows = 1;
}


message table_ItemDisplayBoardConfData {
  repeated ItemDisplayBoardConf rows = 1;
}

message ItemDisplayBoardConf{
  option (resKey) = "id";
  optional int32 id = 1;                                              // 推图板道具id
  repeated ItemDisplayBoardActionConf itemDisplayBoardActionConf = 2;         // 推图板动作
}

message ItemDisplayBoardActionConf {
  optional string actionIcon = 1;                                     // 推图板动作icon
  optional string actionRes = 2;                                      // 推图板人物动作资源-3头身
  optional string outPropSkeletalRes = 3;                             // 道具骨骼
  optional string outPropShowRes = 4;                                 // 道具表演动画
  optional string actionRes2 = 5;                                     // 推图板人物动作资源-2头身
  optional string actionRes5 = 6;                                     // 推图板人物动作资源-5头身
  optional string buttonType = 7;                                     // 3C面板按钮类型
  optional string windowName = 8;                                     // 按钮对应打开的窗口名（buttonType为window时才需要设置）
  optional string buttonIcon = 9;                                     // 3C面板按钮对应的icon
  optional float buttonCD = 10;                                       // 3C面板按钮冷却时间（空则无冷却）
  optional string buttonText = 11;                                    // 3C面板按钮文本
  optional bool bIsActionLoop = 12;                                   // 动作是否循环播放
}

message table_ItemDisplayBoardOfficialTextData {
  repeated ItemDisplayBoardOfficialTextData rows = 1;                     // 官方配文
}

message ItemDisplayBoardOfficialTextData{
  option (resKey) = "stringId";
  optional string stringId = 1;                                       // 字符串id
  optional string content = 2;                                        // 文本内容
  optional int32 order = 3;                                           // 排序
}

message table_ItemToyMiscConfData {
  repeated ItemToyMiscConf rows = 1;                                  // 玩具杂项配置
}

message ItemToyMiscConf{
  option (resKey) = "stringId";
  optional string stringId = 1;                                       // 字符串id
  optional string content = 2;                                        // 文本内容
}