syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResElementCommon.proto";


message UGCPACustomAttributeField {
    optional int32 order = 1;
    oneof field{
        float typeNumber = 11;                        // 数值类型
        string typeString = 12;                       // 字符串类型
        bool typeBoolean = 13;                        // 布尔值类型
        FColor typeColor = 14;                        // 颜色类型
        FVector typeVector = 15;                      // 向量类型
        FVector typePosition = 16;                    // 位置点类型
        FVector typeScale = 17;                       // 尺寸类型
        FVector typeRotation = 18;                    // 旋转角类型 
        int64 placeableActorTypeID = 19;              // 元件类型
        int32 particleEffectTypeID = 20;              // 普通特效类型
        int32 chainParticleEffectTypeID = 21;         // 链式特效类型
        int32 audioSFXTypeID = 22;                    // 音效类型
        int64 imageName = 23;                         // 图片类型
        int32 placeableActorSceneID = 24;             // 元件实例类型
        int32 placeableNPCSceneID = 25;               // 生物实例类型
        int32 placeableVehicleSceneID = 26;           // 载具实例类型
        int32 placeableGroupSceneID = 27;             // 模组实例类型
        int32 customUISceneID = 28;                   // 自定义UI类型（界面控件）
        string characterPartName = 29;                // 角色部位类型
        string characterAnimationName = 30;           // 角色动画类型
        int32 rechargeAbilityTypeID = 31;             // 充能大招类型
        int64 propActorTypeID = 32;                   // 道具类型
    }
}

message UGCPACustomAttributeData {                    // 包一层以支持复杂数据结构，如Map
    optional bool isArray = 1;
    repeated UGCPACustomAttributeField data = 2;
}

message UGCPACustomAttributeConfig {
    optional int32 id = 1;
    optional string name = 2;          
    optional string helpTips = 3;
    optional int32 type = 4;
    optional int32 order = 5;
    optional int32 fromPrefabId = 6;
    optional UGCPACustomAttributeData data = 7;
}

message UGCPACustomAttributeItem {
    option (resKey) = "tabId";
    optional int32 tabId = 1; 
    optional bool enable = 2; 
    optional int32 tabLevel = 3;
    repeated int32 childTabIds = 4;
    optional string name = 5;
    optional int32 order = 6;
    optional int32 typeId = 7;
    optional bool onlyScript = 8;
}

message UGCPACustomAttributeMisc {
    option (resKey) = "id";
    optional int32 id = 1;            // Id
    optional int32 value = 2;         // 值
    optional string text = 3;         // 文本
    optional bool enable = 4;         // 启用
    repeated int32 arrayValue = 5;    // 数组
}

message table_UGCPACustomAttributeItem {
  repeated UGCPACustomAttributeItem rows = 1;
}

message table_UGCPACustomAttributeMisc {
  repeated UGCPACustomAttributeMisc rows = 1;
}