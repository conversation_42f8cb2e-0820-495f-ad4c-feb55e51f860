syntax = "proto2";
option cc_generic_services = false;

package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCondition.proto";
import "ResCommon.proto";
import "google/protobuf/timestamp.proto";

enum ActivityResetType {
  ART_Default = 0;
  ART_NoReset = 1 [(name) = "活动不重置"];
  ART_Daily = 2 [(name) = "每日重置"];
  ART_Weekly = 3 [(name) = "每周重置"];
}

enum ActivityModuleType {
  AMT_Unknown = 0;
  AMT_Quiz = 1 [(name) = "答题"];
  AMT_Task = 2 [(name) = "任务"];
  AMT_Lottery = 3 [(name) = "抽奖"];
}

message ActivityTimeData {
  optional ActivityTimeType timeType = 1;
  optional google.protobuf.Timestamp beginTime = 2;
  optional google.protobuf.Timestamp endTime = 3;
  optional google.protobuf.Timestamp showEndTime = 4;
  optional google.protobuf.Timestamp showBeginTime = 5;
  optional int32 validDay = 6;
  optional bool endWithValidDay = 7;
}

message ActivityLabel {
  optional int32 id = 1;    // 标签KEY，详见 UserLabelConf
  optional int64 val = 2;   // 标签VALUE，与UserLabelConf 内类型一致, 包含关系
}

message ActivityQualificationConf {
  optional int32 superCoreLevel = 1;                  // 超核等级要求
  repeated ActivityLabel superCoreLabel = 2;          // 超核用户标签要求
}

message ActivityMainConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional ActivityType activityType = 2;
  optional ActivityTimeData timeInfo = 3;
  optional int32 suspend = 4; //@noCli 是否挂起
  optional int64 labelId = 5; // 属于哪个标签
  optional int64 groupId = 6; // 该值相同的属于同一个活动
  repeated string backgroundUrl = 7;
  optional int64 jumpId = 8;
  optional int64 templateId = 9;
  optional string lowVersion = 10;
  optional string highVersion = 11;
  optional string activityName = 12;
  optional string activityDsc = 13;
  repeated int32 activityParam = 14;
  repeated Item rewardInfo = 15;
  optional int32 mailId = 16;
  optional string activityDesc = 17;   // 活动描述
  optional ActivityResetType resetType = 18;
  optional string activityUIDetail = 19;  // 活动详情UI界面
  optional int32 isInBottom = 20; // 是否置底
  optional int32 activityRuleId = 21; // 活动规则ID
  optional int32 tagId = 22; // 页签ID
  repeated int32 activityTaskGroup = 23;
  optional bool showInCenter = 24; // 展示在活动中心
  optional bool slapFace = 25; // 拍脸
  optional ActivityNameType activityNameType = 26; // 活动名称类型
  optional int32 jumpType = 27; //跳转类型，用于决定是否走平台侧的跳转逻辑
  optional bool isHideMainBackground = 28; //是否隐藏主界面默认背景
  repeated int32 activityShopType = 29; // 商城配置
  repeated string clientParams = 30; // 客户端显示参数
  optional int32 titleType = 31; // 标题类型
  optional int32 RegionalGroupActivity = 32;  //@noCli 地区分组_活动
  repeated int32 activityBeginCleanCoin = 33; //活动开始清空货币ID
  repeated int32 channelWhitelist = 34; //@noCli 渠道白名单
  repeated int32 channelBlacklist = 35; //@noCli 渠道黑名单
  repeated int32 areaTypeBlacklist = 36;  //@noCli 分区黑名单
  repeated string regChannelDisWhitelist = 37;  //@noCli 注册渠道白名单
  repeated string regChannelDisBlacklist = 38;  //@noCli 注册渠道黑名单
  repeated int32 abtestGroupList = 39;   //@noCli abtest分组
  optional int32 RegionalGroupCommon = 40;  //地区分组_通用
  repeated string overseabackgroundUmg = 41; //海外Umg
  repeated int32 gopenIdWhiteListIdList = 42; // gopenId白名单id列表
  optional ActivityQualificationConf qualification = 43; //@noCli 活动要求
  optional string activitySubName = 44;// 副标题
  optional int32 pakGroupId = 45;//@noSvr 分包组id
  optional bool initLevelLimit = 46; //@noCli 等级限制前是否初始化
  optional int32 relatedConfigId = 47; //@noCli 关联配置的id
  optional bool dontCleanCoinWhenBegin = 48; //@noCli 活动开始时候不清理代币 关联 activityBeginCleanCoin
  optional ResConditionGroup unlockCondition = 49; // @noCli  解锁条件
  optional bool bIsCheckAdminWhiteList = 50;  //@noCli 是否需要检查在管理端白名单
  optional ActivityGroup activityGroup = 51; //@noSvr 活动分组
  optional bool bIsAloneShow = 52; // 是否单独显示在活动中心
  repeated int32 cloudGameTypes = 53; //云游戏类型(0不是, 1为先锋云游戏, 2为微信小游戏, 3为qq小游戏)
  optional ActivityAssistType activityAssistType = 54; //@noSvr 活动助力类型
  repeated string animAssets = 55; // @noSvr 动画相关资源(Spine动画等)
  optional bool isInvisible = 56; // 是否为隐形活动(有活动数据和红点数据, 但不在活动中心显示, 一般在其他活动页面单独跳到该隐形活动)
  optional google.protobuf.Timestamp createFarmAfter = 57; // 农场创建时间晚于
  repeated ActivityModuleConfig moduleConfigs = 58;     // 模块配置
  repeated int32 currencyCfg = 59; //右上角货币配置
  optional int32 hideWhenCompleted = 60;
  repeated int32 taskGroupsDrawBase = 61; //@noSvr 抽奖(分享任务组;进度任务组;每日任务组)
  repeated int32 taskGroupsGetMore = 62; //@noSvr 获取更多任务列表
  repeated string paksName = 63;  //@noSvr 活动所在的pak包名
  repeated int32 platforms = 64;  //可显示平台列表
  optional int32 srcPakGroupId = 65; //@noSvr 活动资源所在的pak包组Id
  repeated ActivityOpenTypeConfig openTypeConfigs = 66;//活动开始检测配置
  optional int32 relatedModeId = 67; //关联活动id
  repeated ActivityGroup activityGroupList = 68; //@noSvr 新活动分组
  optional int32 abtestGroupType = 69;   //@noCli abtest分组类型, 参见ABTestType
  optional bool createFarm = 70; // @noCli 是否创建农场
  repeated int32 sonActivityIds = 71; //子活动列表
  optional string codeName = 72; // 个性化代码名称
  repeated int32 tagIds = 73; // 页签ID列表
  repeated google.protobuf.Timestamp tagEndTimes = 74; // 页签ID结束时间
  optional string thumbnail = 75; //活动页签缩略图
  repeated LabelIds labelIds = 76;   // 顺序列表
  optional ActivityCategory activityCategory = 77;  // @noCli 活动类别
  optional int32 price = 78; // 原本价格或价值
  optional int32 discountPrice = 79; // 折扣后价格或价值
  repeated int32 videos = 80; //@noSvr 视频
  optional int32 videoColorTemplate = 81; //@noSvr 视频颜色模板
  repeated int32 activityTaskGroup2 = 82; // 任务组2
  repeated int32 activityTaskGroup3 = 83; // 任务组3
  optional string CDNImageUrl = 84; // @noSvr 需要展示的CDN图
  repeated string childViews = 85; // @noSvr 活动子UI界面
  optional int32 activityGroupType = 86; // 组队活动类型
  repeated int32 activityTabSplitGroup = 87; //@noSvr 活动页签分割组
}

message ActivityOpenTypeConfig{
  optional int32 type = 1;
  repeated int32 params = 2;
}

message ActivityModuleConfig {
  optional ActivityModuleType type = 1;
  repeated int32 params = 2;
}

message table_ActivityMainConfig {
  repeated ActivityMainConfig rows = 1;
}

message ActivityGopenIdWhiteListConf {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated string gopenidList = 2;
}

message table_ActivityGopenIdWhiteListConfData {
  repeated ActivityGopenIdWhiteListConf rows = 1;
}

message ActivityMainConfigSplitConf {// @noCli
  option (resKey) = "id";
  optional int64 id = 1;
  optional string pbinName = 2;
}

message table_ActivityMainConfigSplitConf {// @noCli @noSvr
  repeated ActivityMainConfigSplitConf rows = 1;
}



message ActivityShowTime {
  optional int32 days = 1;
  optional int32 hours = 2;
  optional int32 minutes = 3;
  optional int32 seconds = 4;
}

message ActivityLabelConfig {
  option (resKey) = "id";
  optional int64 id = 1;
  optional int32 firstLevelLabel = 2;
  optional int32 secondLevelLabel = 3;
  optional string content = 4; // 标签内容
  optional int32 priority = 5; // 优先级，数字越小越靠前
  optional string iconType = 6; // 属于哪种icon
  optional int32 thirdLevelLabel = 7; // 第三级标签
  optional string uiView = 8; // UI视图
  optional string icon = 9; // icon
}


message table_ActivityLabelConfig {// @noSvr
  repeated ActivityLabelConfig rows = 1;
}



message GiftItemInfo {
  optional int32 id = 1;
  optional int32 num = 2;
  optional int32 price = 3;
}

message CommonGiftConfig {
  option (resKey) = "id";
  optional int64 id = 1;
  optional CommonGiftType type = 2;
  optional CommonGiftTimeType time_type = 3;// @noSvr
  optional string low_ver = 4;
  optional string high_ver = 5;
  optional string start_time = 6;
  optional string end_time = 7;
  repeated GiftItemInfo item = 8;
  optional int32 ori_price = 10;// @noSvr
  optional int32 cur_price = 11;// @noSvr
  optional int32 rebate = 12;// @noSvr
  optional int32 max_buy_count = 13;
  optional int32 midas_currency_id = 14;// @noSvr
  optional string title = 15; // 主标题 url图片地址 @noSvr
  optional string subtitle = 16;  // 副标题文本 @noSvr
  optional int32 order = 17; // 礼包显示顺序
  optional string name = 18;  // tab页名字
  optional int32 mailId = 19; // 礼包补发邮件配置
  optional int32 forceSendInfo=20; // 售罄也下发礼包信息 @noSvr
}

message table_CommonGiftConfig { // @noCli
  repeated CommonGiftConfig rows = 1;
}

message LuckyMoneyInfo {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 unlockCD = 2;
  optional int32 rewardId = 3;
  optional int32 taskId = 4;
  optional int32 slotIndex = 5;
  optional LuckyMoneyType type = 6;
}

message table_LuckyMoneyInfo {
  repeated LuckyMoneyInfo rows = 1;
}

// 理财银行签到奖励
message WealthBankCheckInReward {
  option (resKey) = "day";
  optional int32 day = 1; // 周几
  optional int32 itemId = 2; // 奖励道具id
  optional int32 itemNum = 3; // 奖励道具数量
}
message table_WealthBankCheckInReward {
  repeated WealthBankCheckInReward rows = 1;
}

enum WealthBankConfigType {
  WBCT_MakeUpMoneyId = 0 [(name) = "补签消耗代币ID"];
  WBCT_MakeUpMoneyNum = 1 [(name) = "补签消耗代币数量"];
  WBCT_MakeUpMaxTimes = 2 [(name) = "补签次数上限"];
  WBCT_DepositMoneyId = 3 [(name) = "存款代币ID"];
  WBCT_DepositMoneyInitNum = 4 [(name) = "存款初始值"];
  WBCT_DepositIncreaseNum = 5 [(name) = "存款单次增长值"];
  WBCT_DepositMaxNum = 6 [(name) = "存款上限"];
}

// 理财银行配置
message WealthBankConfig {
  option (resKey) = "configType";
  optional WealthBankConfigType configType = 1; // 配置类型
  optional int32 value = 2; // 配置值
}
message table_WealthBankConfig {
  repeated WealthBankConfig rows = 1;
}

message ActivityTabConfig{
  option (resKey) = "id";
  optional int64 id = 1;
  optional int64 seq = 2;
  optional string tabName = 3;
  optional google.protobuf.Timestamp beginTime = 4;
  optional google.protobuf.Timestamp endTime = 5;
  optional string icon = 6;
  optional string bigIconActive = 7;
  optional string bigIconUnactive = 8;
}

message table_ActivityTab {// @noSvr
  repeated ActivityTabConfig rows = 1;
}


message ActivityChapterTaskConfig {
  option (resKey) = "chapterId";
  optional int32 activityId = 1;
  optional int32 chapterId = 2;
  optional int32 taskGroupId = 3;
  optional int32 mainTaskGroupId = 4;
  repeated Item chapterReward = 5;// @noSvr
  optional string chapterIcon = 6;// @noSvr
  optional string chapterTitle = 7;// @noSvr
  optional string chapterDesc = 8;// @noSvr
  optional google.protobuf.Timestamp chapterStartTime = 9;
  optional google.protobuf.Timestamp chapterEndTime = 10;
}

message table_ActivityChapterTaskData {
  repeated ActivityChapterTaskConfig rows = 1;
}

message TakeawayConf {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 boxIds = 2;
  optional TakeawayInviterConf inviter = 3;
  optional TakeawayInviteeConf invitee = 4;
  optional int32 keyId = 5;
}

message TakeawayInviterConf {
  repeated int32 successNums = 1;
  repeated int32 successBoxIds = 2;
  optional int32 invitationCfgId = 3;
}

message TakeawayInviteeConf {
  optional int32 supportMax = 1;
  optional int32 supportRewardMailId = 2;// @noCli
}

message table_TakeawayConf {
  repeated TakeawayConf rows = 1;
}

message TakeawayBoxConf {
  option (resKey) = "boxId";
  optional int32 boxId = 1;
  optional int32 takeawayId = 2;// @noSvr
  optional int32 waitHour = 3;
  repeated int32 itemIds = 4;
  repeated int32 itemNums = 5;
  repeated int32 pkgIds = 6;
  repeated int32 pkgNums = 7;
  optional int32 boxLevel = 8;// @noSvr
}

message table_TakeawayBoxConf {
  repeated TakeawayBoxConf rows = 1;
}

message ActivityCenterReverseConfig{
  option (resKey) = "id";
  optional int32 id = 1;
  optional google.protobuf.Timestamp startTime = 2;
  optional google.protobuf.Timestamp endTime = 3;
  optional google.protobuf.Timestamp askTime = 4;
  optional string picUrl = 5;
  optional string calenderTitle = 6;
  optional string calenderContent = 7;
  optional string calenderUrl = 8;
  optional int32 rewardId = 9;
  optional string regChannel = 10;
  optional int32 activityId = 11;
  optional int32 isVideo = 12; //是否是直播
}

message table_ActivityCenterReverseData {// @noSvr
  repeated ActivityCenterReverseConfig rows = 1;
}

message ActivitySuperLinearRedeemConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional int32 targetValue = 3;
  repeated Item rewardInfo = 4;
  optional string name = 5;   // @noSvr
  optional string buildIcon = 6; // @noSvr
  optional string rewardIcon = 7; // @noSvr
}

message table_ActivitySuperLinearRedeemData {
  repeated ActivitySuperLinearRedeemConfig rows = 1;
}

message ActivitySuperLinearRandomConfig {
  option (resKey) = "activityId,randomValue";
  optional int32 activityId = 1;
  optional int32 randomValue = 2;
  optional int32 weight = 3;
  optional string desc1 = 4; // @noSvr
  optional string desc2 = 5; // @noSvr
}

message table_ActivitySuperLinearRandomData {
  repeated ActivitySuperLinearRandomConfig rows = 1;
}

message ActivitySuperLinearRedeemTitleData {// @noSvr
  option (resKey) = "activityId";
  optional int32 activityId = 1; // 活动id
  optional string titleIcon = 2; //当前活动id对应的标题
}

message table_ActivitySuperLinearRedeemTitleData {// @noSvr
  repeated ActivitySuperLinearRedeemTitleData rows = 1;
}

message LuckyBalloonConf {
  option (resKey) = "poolId";
  optional int32 poolId = 1;
  optional string name = 2;
  repeated int32 prices = 3;
  optional int32 coinType = 4;
  optional int32 limit = 5;
}

message table_LuckyBalloonConf {
  repeated LuckyBalloonConf rows = 1;
}

message LuckyBalloonRewardConf {
  option (resKey) = "rewardId";
  optional int32 rewardId = 1;
  optional int32 poolId = 2;
  optional string name = 3;
  optional int32 itemId = 4;
  optional int32 itemNum = 5;
  optional int32 plate = 6;// @noSvr
  optional int32 weight = 7;
  optional int32 minDraw = 8;
  optional int32 maxDraw = 9;
}

message table_LuckyBalloonRewardConf {
  repeated LuckyBalloonRewardConf rows = 1;
}

message LuckyBalloonBlessConf {
  option (resKey) = "blessId";
  optional int32 blessId = 1;
  optional int32 poolId = 2;
  optional string blessTitle = 3;// @noSvr
  optional string blessText = 4;// @noSvr
  optional int32 weight = 5;
}

message table_LuckyBalloonBlessConf {
  repeated LuckyBalloonBlessConf rows = 1;
}

// 开学返利活动奖励配置
message TimeLimitedCheckInRewardConf {
  optional int32 checkInDayCount = 1; // 签到天数
  optional int32 itemId = 2;        // 奖励道具ID
  optional int32 itemNum = 3;       // 奖励道具数量
}

// 开学返利活动时间范围配置配置
message TimeLimitedCheckInRangeConf {
  optional google.protobuf.Timestamp openTime = 1;    // 开始时间
  optional google.protobuf.Timestamp closeTime = 2;   // 结束时间
  repeated TimeLimitedCheckInRewardConf rewardConf = 3; // 奖励列表
}

// 开学返利活动配置
message TimeLimitedCheckInActivityConf {
  option (resKey) = "activityId";
  optional int32 activityId = 1;      // 活动ID
  repeated TimeLimitedCheckInRangeConf timeRangeConf = 3; // 签到时间配置
  optional CoinType makeUpCoinType = 4;  // 补签消耗的货币类型
  optional int32 makeUpCoinNum = 5;   // 补签消耗的货币数量
  repeated Item unlockReward = 6;     // 激活奖励
  optional string unlockPayProductId = 7; // 解锁的支付ID
  optional int32 textRuleId = 8; // 规则描述ID @noSvr
  optional int32 previewItemId1 = 9; // 预览模型ItemId1 @noSvr
  optional int32 previewItemId2 = 10; // 预览模型ItemId2 @noSvr
  optional google.protobuf.Timestamp startTime = 11; // 开始时间
  optional google.protobuf.Timestamp endTime = 12; // 结束时间
  optional int32 unLockOriginPrice = 13; // 解锁原价
  optional int32 unLockMidasPrice = 14; //解锁实际价格
}

message table_TimeLimitedCheckInActivityConfData {
  repeated TimeLimitedCheckInActivityConf rows = 1;
}

// 积攒福气活动配置
message AccumulateBlessingsActivityConf {
  option (resKey) = "activityId";
  optional int32 activityId = 1; // 活动ID
  optional int32 coinId = 2; // 兑换物id
  optional google.protobuf.Timestamp preStartTime = 3; // 预热开启时间
  optional google.protobuf.Timestamp preEndTime = 4; // 预热结束时间
  optional int32 baseCoinCnt = 5; // 兑换物基数
  optional int32 assistCoinCnt = 6; // 被助力-增加兑换物
  optional int32 onlineSec = 7; // 每日在线任务-时长
  optional int32 onlineCoinCnt = 8; //每日在线任务-增加兑换物
  optional int32 assistLimit = 9; // 每日助力上限
  repeated Item assistReward = 10; // 每次助力奖励
  optional google.protobuf.Timestamp rewardStartTme = 11; // 领奖开启时间
  optional google.protobuf.Timestamp rewardEndTme = 12; // 领奖结束时间
  repeated LoginDayTask task = 13; // 领奖结束时间
  optional int32 maxFrirendListSize = 14; // 最大邀请好友位
  optional int32 invitationConfId = 15; // 邀请配置id
}

// 福星手账簿
message LuckyStarHandBook {
  option (resKey) = "typeId";
  optional int32 typeId = 1; // 手账簿id
  optional int32 groupId = 2; // 活动组id
  optional int32 roundNum = 3; // 活动轮次
  optional string name = 4; // 福星官名称
  repeated int32 starId = 5; // 合成所需福星id
  repeated int32 rewardId = 6; // 奖励id
  repeated int32 rewardNum = 7; // 奖励数量
  repeated int32 starRewardId = 8; // 抽取福星奖励id
  repeated int32 starRewardNum = 9; // 抽取福星奖励数量
  repeated int32 starRewardWeight = 10; // 抽取福星奖励权重
  optional int32 maxReceiveNum = 11;  // 最大领取别人赠送的数量
}

message table_LuckyStarHandBook {
  repeated LuckyStarHandBook rows = 1;
}

message LuckyStarHandBookStars {
  option (resKey) = "starId";
  optional int32 starId = 1; // 福星id
  optional string icon = 2; // 图标
  optional string name = 3; //名字
  optional string title = 4; // 简称
  optional string greyIcon = 5; // 灰图标
}

message table_LuckyStarHandBookStars { //@noSvr
  repeated LuckyStarHandBookStars rows = 1;
}

message LuckyStarHandBookStarPool {
  option (resKey) = "poolId";
  optional int32 poolId = 1; // id
  optional int32 groupId = 2; // 活动组id
  optional int32 roundNum = 3; // 活动轮次
  optional int32 rangeMin = 4; // 抽取次数下限
  optional int32 rangeMax = 5; // 抽取次数上限
  repeated int32 starId = 6; // 包含的福星id
  repeated int32 starProb = 7; // 概率
}

message table_LuckyStarHandBookStarPool {
  repeated LuckyStarHandBookStarPool rows = 1;
}

message LuckyStarHandBookBless {
  option (resKey) = "blessId";
  optional int32 blessId = 1; // id
  optional int32 starId = 2; // 福星id
  optional string banner = 3; // 福星banner @noSvr
  optional string blessText = 4; // 祝福语 @noSvr
  optional int32 weight = 5; // 概率权重
}

message table_LuckyStarHandBookBless {
  repeated LuckyStarHandBookBless rows = 1;
}

message LoginDayTask {
  optional int32 loginDay = 1; // 累登天数
  optional int32 returnRate = 2; // 返还比例
}

message table_AccumulateBlessingsActivityConfData {
  repeated AccumulateBlessingsActivityConf rows = 1;
}

// 春节红包感谢的提示
message SpringRedPacketConf {
  option (resKey) = "Id";
  optional int32 Id = 1;  // ID
  optional string ThankString = 2;  // 感谢的话
}

message table_SpringRedPacketConfData {// @noSvr
  repeated SpringRedPacketConf rows = 1;
}

enum TeamRankActivityType {
  TRAT_None = 0;
  TRAT_LoseProtect = 1[(name)="排位不掉分"]; // 排位不掉分
  TRAT_Additional = 2[(name)="排位加分"]; // 排位加分
  TRAT_MultiScore = 3[(name)="排位分加倍"]; // 排位分加倍
  TRAT_IntimacyMag = 4[(name)="亲密度翻倍"];// 亲密度翻倍
}

message ActivityTeamRankConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional TeamRankActivityType teamRankActivityType = 3;
  repeated int32 params = 4;
  optional int32 times = 5;
  optional ActivityResetType resetType = 6;
  repeated int32 matchTypeId = 7;
  repeated int32 teamMemberCount = 8;
  optional google.protobuf.Timestamp startTime = 9;
  optional google.protobuf.Timestamp endTime = 10;
  repeated int32 qualifyIntegralRange = 11;
  optional string desc = 12;
}

message table_ActivityTeamRankData {
  repeated ActivityTeamRankConfig rows = 1;
}

// 奥特曼主题活动配置
message UltramanThemeConfig {
  option (resKey) = "stage,activityId";
  optional int32 activityId = 1;
  optional int32 stage = 2;
  optional google.protobuf.Timestamp startTime = 3;
  optional int64 collectionObject = 4;
  optional int32 collectionIntervalSec = 5;
  optional int32 collectionVariance = 6;
  optional string name = 7;
  optional string desc = 8;
  optional int32 plot_id = 9;
  optional string background = 10;
}

message table_UltramanThemeConfigData {
  repeated UltramanThemeConfig rows = 1;
}

// 奥特曼主题活动光收集进度奖励配置
message UltramanThemeCollectionConfig {
  option (resKey) = "rewardId,activityId";
  optional int32 activityId = 1;
  optional int32 rewardId = 2;
  optional int64 selfProgress = 3;
  optional int64 wholeProgress = 4;
  repeated Item reward = 5;
}

message table_UltramanThemeCollectionConfigData {
  repeated UltramanThemeCollectionConfig rows = 1;
}

// 奥特曼任务跳转
message UltramanTaskJumpConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string name = 2;
  optional int32 jump_id = 3;
  optional int32 activity_id = 4;
}

message table_UltramanTaskJumpConfigData {
  repeated UltramanTaskJumpConfig rows = 1;
}

message SpringBlessingTaskReward {
  optional int32 dayIndex = 1;        // 第几天，注意活动开启当天为第0天
  repeated Item rewards = 2;          // 奖励道具列表 // 废弃
  repeated TimeLimitedItem items = 3; // 道具奖励列表
}

// 春节集福活动配置
message SpringBlessingCollectionConf {
  option (resKey) = "activityId";
  optional int32 activityId = 1;        // 活动ID
  repeated int32 blessingCardId = 2;    // 需要收集的福字
  optional bool rewardNeedShare = 4;    // 是否需要分享
  optional int32 stallDailyLotteryCnt = 5;   // 福字铺每日抽取福卡次数
  optional int32 sponsorDailyLotteryCnt = 6; // 赞助商每日抽取福卡次数 # 废弃，该读赞助商配置
  repeated int32 sponsorLotteryCardPool = 7;  // 赞助商处产出福卡列表
  optional int32 sponsorLotteryCardRatio = 8; // 赞助商处获得福卡概率
  optional int32 dailyGiveCardCnt = 9;        // 每日投送福卡次数限制
  repeated SpringBlessingTaskReward taskReward = 10;  // 任务奖励
  optional int32 cdKeySendFailMailId = 11;    // 福卡CDKey奖励发送失败时发送的邮件ID
}

message table_SpringBlessingCollectionConfData {
  repeated SpringBlessingCollectionConf rows = 1;
}

message SpringCardRewardWeight {
  optional int32 rewardId = 1;
  optional int32 weight = 2;
}

// 福字卡牌配置
message SpringBlessingCardConf {
  option (resKey) = "id";
  optional int32 id = 1;                               // 卡牌ID
  optional string name = 2;                            // 名字
  repeated SpringCardRewardWeight rewardPool = 3;      // 奖励库
  optional int32 stallLotteryRatio = 4;                // 福字铺抽取的概率
  optional string giveCardTips = 5;                // 收到玩家赠送福字时的提示
}

message table_SpringBlessingCardConfData {
  repeated SpringBlessingCardConf rows = 1;
}

// 福字卡牌随机库
message SpringBlessingCardRewardConf {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated Item rewards = 2;
  optional int32 cdKeyReward = 3; // 是否CDKey奖励
}

message table_SpringBlessingCardRewardConfData {
  repeated SpringBlessingCardRewardConf rows = 1;
}

message SpringBlessingSponsorPackageGroupConf {
  optional int32 dayIndex = 1;
  optional string packageGroupId = 2;
  optional string packageModuleId = 3;
  optional int32 cdKeyCntLimit = 4;   // CdKey发放数量限制 // 废弃
  optional int32 cdKeyCntLimitQQ = 5; // QQ CdKey发放数量限制
  optional int32 cdKeyCntLimitWX = 6; // 微信 CdKey发放数量限制
}

// 春节集福赞助商配置
message SpringBlessingSponsorConf {
  option (resKey) = "id";
  optional int32 id = 1;    // 赞助商ID
  optional string name = 2; // 赞助商名字
  optional int32 dailyGetCDKeyCntLimit = 3;     // 每日获取CDKey最大次数
  repeated SpringBlessingSponsorPackageGroupConf packageGroupConf = 4;  // cdKey礼包配置
  optional int32 dailyLotteryCardCntLimit = 5;  // 每日领取福卡次数
  optional string mailTitle = 6;    // CDKey邮件标题
  optional string mailContent = 7;  // CDKey邮件内容
}

message table_SpringBlessingSponsorConfData {
  repeated SpringBlessingSponsorConf rows = 1;
}

// 打卡手册配置
message ActivityCheckInManualConfig {
  option (resKey) = "id,activityId";
  optional int32 id = 1;
  optional int32 activityId = 2;
  repeated Item baseRewardInfo = 3;
  repeated Item higherRewardInfo = 4;
  optional int32 level = 5;
  optional int32 isFlag = 6;
  optional int32 starLevel = 7;
  optional int32 moduleType = 8;
}

message table_ActivityCheckInManualData {
  repeated ActivityCheckInManualConfig rows = 1;
}


message table_FarmDailyAwardStartLevelConfig {
  repeated FarmDailyAwardStartLevelConfig rows = 1;
}

message FarmDailyAwardStartLevelConfig {
  option (resKey) = "startLevel,activityId";
  optional int32 startLevel = 1;
  optional int32 activityId = 2;
  optional int32 needBuyTimes = 3;
  optional int32 normalChestId = 4;   //普通宝箱id
  optional int32 advancedChestId = 5; //高级宝箱id
}

message StickerConf {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional int32 chapterId = 3;
  optional int32 coinType = 4;
  optional int32 coinNum = 5;
  optional int32 atDraw = 6;
}

message table_StickerConfData {
  repeated StickerConf rows = 1;
}

message StickerNormalRewardConf {
  option (resKey) = "activityId,chapterId,positionId";

  optional int32 activityId = 1;
  optional int32 chapterId = 2;
  optional int32 positionId = 3;

  optional int32 itemId = 4;
  optional int32 itemNum = 5;
  optional int32 expireDays = 6;
  optional google.protobuf.Timestamp expireAt = 7;
}

message table_StickerNormalRewardConfData {
  repeated StickerNormalRewardConf rows = 1;
}

message StickerBingoRewardConf {
  option (resKey) = "activityId,chapterId,positionId";

  optional int32 activityId = 1;
  optional int32 chapterId = 2;
  optional int32 positionId = 3;

  optional int32 itemId = 4;
  optional int32 itemNum = 5;
  optional int32 expireDays = 6;
  optional google.protobuf.Timestamp expireAt = 7;
}

message table_StickerBingoRewardConfData {
  repeated StickerBingoRewardConf rows = 1;
}

message StickerChapterRewardConf {
  option (resKey) = "activityId,chapterId";

  optional int32 activityId = 1;
  optional int32 chapterId = 2;

  optional int32 itemId = 3;
  optional int32 itemNum = 4;
  optional int32 expireDays = 5;
  optional google.protobuf.Timestamp expireAt = 6;
  optional int32 mailId = 7;
  optional string backgroundUrl = 8;
}

message table_StickerChapterRewardConfData {
  repeated StickerChapterRewardConf rows = 1;
}

// 常驻兑换活动
message ActivityPermanentExchangeConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional ActivityTimeData timeInfo = 2;
  optional string content = 3;
  optional int32 contentType = 4;
  optional int32 tipsType = 5;
  optional string tittle = 6;
  optional string subtittle = 7;
}

message table_ActivityPermanentExchangeData {
  repeated ActivityPermanentExchangeConfig rows = 1;
}

// 热身赛-星力运动会对局次数奖励
message CompetitionWarmUpGameTimesRewardConf {
  optional int32 gameTimes = 1;   // 对局次数
  repeated Item itemReward = 2;   // 奖励
}

// 热身赛-星力运动会积分奖励
message CompetitionWarmUpScoreRewardConf {
  optional int32 rewardIndex = 1; // 奖励ID
  optional int32 minScore = 2;    // 最低积分
  repeated Item itemReward = 3;   // 奖励
}

// 热身赛-星力运动会活动配置
message CompetitionWarmUpActivityConf {
  option (resKey) = "activityId,stageId";
  optional int32 activityId = 1;                                      // 活动ID
  optional int32 stageId = 2;                                         // 阶段ID
  optional google.protobuf.Timestamp beginTime = 3;                   // 数据统计-开始时间
  optional google.protobuf.Timestamp endTime = 4;                     // 数据统计-结束时间
  optional int32 rankCapacity = 5;                                    // 排行榜大小
  repeated CompetitionWarmUpGameTimesRewardConf gameTimesReward = 6;  // 对局次数奖励
  optional google.protobuf.Timestamp rewardBeginTime = 7;             // 积分奖励领取-开始时间
  optional google.protobuf.Timestamp rewardEndTime = 8;               // 积分奖励领取-结束时间
  repeated CompetitionWarmUpScoreRewardConf scoreReward = 9;          // 积分奖励配置
  optional int32 rankId = 10;                                         // 排行榜ID
  optional int32 joinGameJumpId = 11;                                 // 去参加跳转ID
  optional int32 ruleDescJumpId = 12;                                 // 规则说明跳转ID
  optional int32 allowCustomScoreReport = 13;                         // 允许业务自定义积分上报
}

message table_CompetitionWarmUpActivityConfData {
  repeated CompetitionWarmUpActivityConf rows = 1;
}

// 热身赛-星力运动会积分配置
message CompetitionWarmUpScoreConf {
  option (resKey) = "activityId,id";
  optional int32 activityId = 1;            // 活动ID
  optional int32 id = 2;                    // 配置ID
  optional int32 addScore = 3;              // 增加的积分
  optional ResConditionInfo condition = 4;  // 增加条件
  optional bool isGameTimeCondition = 5; //@noCli 是否是计算进度的条件
}

message table_CompetitionWarmUpScoreConfData {
  repeated CompetitionWarmUpScoreConf rows = 1;
}

// 娱乐情报站活动
message EntertainIntelligenceStationConf {
  option (resKey) = "id";
  optional int32 id = 1;                      // 配置ID
  repeated EntertainProgress progress = 2;    // 娱乐进度（废弃)
  repeated EntertainRecommend recommend = 3;  // 娱乐推荐
  optional int32 normalAddValue = 4;          // 正常加分
  optional int32 recommendAddValue = 5;       // 推荐加分
  optional int32 maxDailyAddValue = 6;        // 每日最多加分
}

message EntertainProgress {
  optional int32 value = 1;
  optional Item reward = 2;
}

message EntertainRecommend {
  optional int32 gameTypeId = 1;
  optional bool isNew = 2;
  optional string isNewTipsStr = 3;     //新活动提升文本
  optional string recommendTitle = 4;   //推荐标题
  optional string recommendDesc = 5;    //推荐说明
  optional string recommendImgUrl = 6;  //推荐大图cdnUrl
  optional string recommendListImage = 7;  //推荐列表图片
}

message table_EntertainIntelligenceStationConf {
  repeated EntertainIntelligenceStationConf rows = 1;
}

message ActivityHYWarmUpConf {
  option (resKey) = "Id";
  optional int32 Id = 1;                                      // Id
  optional int32 ActivityId = 2;                              // 活动Id
  optional google.protobuf.Timestamp HYActivityBeginTime = 3; // 半周年庆活动开始时间
  repeated int32 CheckInDataList = 4;                         // 打卡配置列表
  repeated int32 ProgressDataList = 5;                        // 打卡进度配置列表
  repeated Item FinalBigReward = 6;                           // 最终分享大奖
  optional bool showCalendar = 7;                             // 是否显示订阅日历
  optional bool showJump = 8;                                 // 是否显示跳转
  optional string qqLink = 9;                                 // 手Q H5链接
  optional string wxLink = 10;                                // WX H5链接
  optional bool qqPreHandle = 11;                             // 是否需要先拉起手Q
}

message table_ActivityHYWarmUpData {
  repeated ActivityHYWarmUpConf rows = 1;
}

message ActivityHYWarmUpCheckinConf {
  option (resKey) = "Id";
  optional int32 day = 1;        // 打卡天数
  optional int32 card = 2;       // 卡片
  optional string cardstr = 3;   // 卡片文字
  repeated Item rewards = 4;     // 分享奖励
  optional int32 Id = 5;        // Id
}

message table_ActivityHYWarmUpCheckinData {
  repeated ActivityHYWarmUpCheckinConf rows = 1;
}

message ActivityHYWarmUpProgressConf {
  option (resKey) = "Id";
  optional int32 day = 1;       // 打卡天数
  repeated Item rewards = 2;    // 奖励
  optional int32 Id = 3;        // Id
}

message table_ActivityHYWarmUpProgressData {
  repeated ActivityHYWarmUpProgressConf rows = 1;
}


message TrainingCampSportsmanConf {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 itemId = 2;
  repeated KeyValueInt32 addScore = 3;
  optional int32 useItemId = 4;
  optional int32 useItemNum = 5;
  repeated SportsmanAttrConf attr = 6;
  optional string name = 7;
  optional string bubbleText = 8;
  repeated int32 act = 9;
  repeated int32 dress = 10;
}

message SportsmanAttrConf {
  optional int32 id = 1;
  optional int32 baseScore = 2;
  optional int32 maxScore = 3;
  optional string name = 4;
}

message table_TrainingCampSportsmanConf {
  repeated TrainingCampSportsmanConf rows = 1;
}

// 活动奖励配置
message ActivityRewardConf {
  repeated int32 itemIdList = 1;	  // 道具id
  repeated int32 numList = 2;		  // 道具数量
  repeated int32 condList = 3;		  // 奖励要求
  repeated int32 validPeriodList = 4; // 奖励有效期 0.有效 其他.对应天数
}

// 培养活动配置
message TrainingActivityConf {
  option (resKey) = "id";
  optional int32 id = 1;					// 培养对象ID
  optional int32 activityId = 2;			// 所属活动ID
  optional string name = 3;					// 名称
  optional string bubbleText = 4;			// 起泡文案
  optional int32 useItemNum = 5;			// 培养消耗数量
  optional int32 addScore = 6;				// 增加进度值
  optional int32 maxScore = 7;				// 最大进度值
  optional ActivityRewardConf reward = 8;	// 奖励配置(废弃)
  repeated int32 act = 9;					// 动作
  repeated int32 dress = 10;				// 衣服
  optional int32 addItemId = 11;			// 培养赠送道具ID
  optional int32 addItemCnt = 12;			// 培养赠送道具数量
}

// 培养活动配置表
message table_TrainingActivityConf {
  repeated TrainingActivityConf rows = 1;
}

// 培养活动奖励配置
message TrainingActivityAwardConf {
  option (resKey) = "id";
  optional int32 id = 1;					// 活动ID
  optional ActivityRewardConf reward = 2;	// 奖励配置
}

// 培养活动奖励配置表
message table_TrainingActivityAwardConf {
  repeated TrainingActivityAwardConf rows = 1;
}

message GroupingReturnConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional int32 stageId = 3;
  optional int32 requireNum = 4;
  repeated Item rewardInfo = 5;
}

message table_GroupingReturnData {
  repeated GroupingReturnConfig rows = 1;
}

// 星界奇遇活动每日任务配置
message ThemeAdventureDailyTaskConf {
  optional int32 id = 1;            // 序号，用于标识是否已领奖
  optional int32 needScore = 2;     // 需要的积分
  repeated Item rewardItem = 3;     // 道具奖励
}

// 星界奇遇活动玩法配置
message ThemeAdventureGameConf {
  optional int32 gameId = 1;                                // 玩法ID
  optional string gameTitle = 2;                            // 玩法标题  //@noSvr
  optional string gameDesc = 3;                             // 玩法描述  //@noSvr
  optional string gameIcon = 4;                             // 玩法图标  //@noSvr
  optional int32 taskGroupId = 5;                           // 普通任务组ID，索引到活动配置-任务组
  optional string dailyTaskName = 6;                        // 每日任务名称   //@noSvr
  optional ResConditionInfo dailyTaskCondition = 7;         // 每日任务计数增加条件
  repeated ThemeAdventureDailyTaskConf dailyTaskReward = 8; // 每日任务奖励
  optional string dailyGotoTitle = 9;                       // 每日任务前往描述  //@noSvr
  optional int32 dailyJumpId = 10;                          // 每日任务前往跳转  //@noSvr
  repeated int32 gameMatchIds = 11;                         // 显示标签的玩法模式  //@noSvr
  optional string gameBuffDesc = 12;                        // 玩法Buff描述  //@noSvr
  optional string gameLockDesc = 13;                        // 玩法解锁描述  //@noSvr
}

// 星界奇遇活动配置
message ThemeAdventureActivityConf {
  option (resKey) = "activityId";
  optional int32 activityId = 1;                            // 活动ID
  repeated ThemeAdventureGameConf gameConf = 2;             // 玩法配置
  repeated int32 showRewardItemId = 3;                      // 展示的盲盒奖励列表
  optional int32 mysteryBoxUpgradeCnt = 4;                  // 盲盒升级次数
}

message table_ThemeAdventureActivityConfData {
  repeated ThemeAdventureActivityConf rows = 1;
}


message DanceOutfitConfig {
  option (resKey) = "id";
  optional int32 id = 1;            // 服装id
  optional int32 keywordId = 2;		// 关键字id
  optional int32 styleId = 3;		// 风格id
  optional int32 suitId         = 4;        //时装ID
  optional string aigcUrl       = 5;        //aigc下载链接
}


message table_DanceOutfitConfData {
  repeated DanceOutfitConfig rows = 1;
}

message DanceOutfitKeyword {
  option (resKey) = "id";
  optional int32 id             = 1;        //关键词ID
  optional string name          = 2;        //名字
  optional string icon          = 3;        //UI展示图
  optional int32 prayerCardId     = 4;        //场景花笺对应id
  optional int32 sourcemode     = 5;        //来源模式
  optional int32 price     = 6;        //价格
  optional int32 specialjumpid     = 7;        //特殊跳转id
  optional string desc          = 8;        //判词
}

message table_DanceOutfitKeywordData{
  repeated DanceOutfitKeyword rows = 1;
}

message DanceOutfitStyle {
  option (resKey) = "id";
  optional int32 id             = 1;        //风格ID
  optional string name          = 2;        //名字
  optional string icon          = 3;        //UI展示图

}

message table_DanceOutfitStyleData{
  repeated DanceOutfitStyle rows = 1;
}



message DanceOutfitItemConf {
  option (resKey) = "id";
  optional int32 id             = 1;        //ID
  optional int32 danceOutfitId  = 2;        //服装id

}


message table_DanceOutfitItemConfData{
  repeated DanceOutfitItemConf rows = 1;
}

message PrayerCardSourceConf {
  option (resKey) = "id";
  optional int32 id             = 1;        //ID
  optional int32 type          = 2;        //类型
  optional int32 jumpid          = 3;        //跳转id
  optional string name          = 4;        //命名
  optional string pic          = 5;        //图片
  optional string desc          = 6;        //描述
}

message table_PrayerCardSourceConfData{
  repeated PrayerCardSourceConf rows = 1;
}

// 星界奇遇奖励升级配置
message ThemeAdventureRewardUpgradeConf {
  option (resKey) = "activityId,sourceItemId";
  optional int32 activityId = 1;          // 活动ID
  optional int32 sourceItemId = 2;        // 源礼包ID
  optional int32 targetItemId = 3;        // 目标礼包ID # 废弃
  optional int32 ratio = 4;               // 升级概率   # 废弃
  repeated ItemWeight upgradeItem = 5;    // 升级礼包概率
}

message table_ThemeAdventureRewardUpgradeConfData {
  repeated ThemeAdventureRewardUpgradeConf rows = 1;
}

// 场景收集物同步获取类型
enum SceneCollectionSyncType {
  SCST_Self = 0;                                   // 默认只有自己获取
  SCST_PlayerTeam = 1 [(name) = "玩家队伍"];        // 玩家自己组队的队伍
  SCST_BattleTeam = 2 [(name) = "对局队伍"];        // 玩法对局的队伍
}

// 收集物场景类型
enum CollectionSceneType {
  CST_Unknown = 0;
  CST_CommonBattle = 1 [(name) = "普通玩法"];   // 普通玩法
  CST_UgcBattle = 2 [(name) = "UGC玩法"];      // ugc玩法
  CST_Lobby = 3 [(name) = "广场"];             // 广场
}

// 收集物刷新池类型
enum CollectionPoolType {
  CPT_Unknown = 0;
  CPT_BattleMain = 1 [(name) = "主玩法"];      // 主玩法
  CPT_BattleWolf = 2 [(name) = "狼人玩法"];    // 狼人玩法
  CPT_BattleArena = 3 [(name) = "峡谷玩法"];   // 峡谷玩法
  CPT_BattleArenaBs = 4 [(name) = "峡谷吃鸡玩法"]; // 峡谷吃鸡玩法
  CPT_BattleUgc = 5 [(name) = "ugc玩法"];     // ugc 玩法
  CPT_LobbyMain = 6 [(name) = "主广场"];       // 主广场
  CPT_LobbyStage = 7 [(name) = "广场舞台"];     // 广场舞台
  CPT_LobbyUgc = 8 [(name) = "ugc广场"];      // ugc 广场
  CPT_BattleHOK = 9 [(name) = "峡谷5v5玩法"];   // 峡谷5v5玩法
  CPT_BattleArenaHz = 10 [(name) = "峡谷占点玩法"]; // 峡谷占点玩法
}

// 场景收集物信息
message SceneCollectionInfo {
  optional int32 maxCollectTimes = 1; // 最大可收集次数
  optional int32 itemId = 2;          // 对应道具id
}

// 收集物刷新信息
message SceneCollectionRefreshInfo {
  optional google.protobuf.Timestamp beginTime = 1; // 开始时间
  optional google.protobuf.Timestamp endTime = 2;   // 结束时间
  optional int32 activityId = 3;                    // 关联活动时间，如配置则用活动时间
  optional int32 refreshTimes = 4;                  // 刷新次数 0为不限次
  optional int32 refreshIntervalSec = 5;            // 刷新间隔秒
  optional int32 collectionNum = 6;                 // 刷新的收集物数量
}

// 收集物刷新池信息
message SceneCollectionPoolInfo {
  optional SceneCollectionInfo collectionInfo = 1;          // 收集物信息
  optional SceneCollectionRefreshInfo refreshInfo = 2;      // 刷新信息
  optional SceneCollectionSyncType collectionSyncType = 3;  // 收集物同步获取类型
}

message PrayerCardSceneConfig {
  optional CollectionSceneType sceneType = 1;              // 场景类型
  repeated int64 sceneTypeId = 2;                          // 场景类型对应的ID 普通玩法-模式id ugc玩法-ugcId 广场-mapId
}

// 祈福牌活动
message ActivityPrayerCardCollectionConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional SceneCollectionPoolInfo collectionPoolInfo = 2; // 收集物刷新池配置
  optional PrayerCardSceneConfig sceneConfig = 3;                                   // 场景配置
  optional CollectionPoolType collectionPoolType = 4;     // 刷新池类型
  optional int32 collectionPoolTypeId = 5;     // 刷新池类型的id ds区分用
  repeated int32 weightConfig = 6; // 祈福牌权重配置
}

message table_ActivityPrayerCardCollectionConfigData {
  repeated ActivityPrayerCardCollectionConfig rows = 1;
}

enum PrayerCardWeightType {
  PCWT_None = 0 [(name) = "普通"];
  PCWT_Priority = 1 [(name) = "优先获取"];  // 优先获取列表内的卡，都获取到了才随机其他的
}

// 祈福牌权重配置
message ActivityPrayerCardWeightConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 cardId = 2;    // 祈福牌id
  repeated int32 weight = 3;    // 权重
  optional PrayerCardWeightType type = 4; // 类型
}

message table_ActivityPrayerCardWeightConfigData {
  repeated ActivityPrayerCardWeightConfig rows = 1;
}

// 祈福牌配置
message ActivityPrayerCardConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 itemId = 2;                            // 关联道具id
  optional int32 itemNum = 3;                            // 关联道具数量
  optional google.protobuf.Timestamp unlockTime = 4;    // 解锁时间
  optional int64 limitPoolId = 5;                       // 弃用 限定刷新池id
  optional string name = 6;                             // 名字
  optional Item unlockRewardItem = 7;                   // 解锁奖励
  optional int32 maxNum = 8;                            // 获取上限
}

message table_ActivityPrayerCardConfigData {
  repeated ActivityPrayerCardConfig rows = 1;
}

// 祈福牌杂项配置
message ActivityPrayerCardMiscData {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 giveRecordMax = 2;                      // 祈福牌赠送记录上限条数
}

message table_ActivityPrayerCardMiscData {
  repeated ActivityPrayerCardMiscData rows = 1;
}

message ActivityPrayerCardRewardLimitData {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;                        // 活动id
  optional google.protobuf.Timestamp unlockTime = 3;    // 解锁时间
  optional int32 itemId = 4;                            // 对应奖励道具id
  optional int32 maxNum = 5;                            // 总上限
}

message table_ActivityPrayerCardRewardLimitData {
  repeated ActivityPrayerCardRewardLimitData rows = 1;
}

enum ActivityRechargeRecommendType {
  ARRT_Recharge = 1; // 充值ID
  ARRT_CommodityId = 2; // 商品ID
  ARRT_JumpId = 3; // JumpID
}

message ActivityRechargeRecommendConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional int32 sort = 3;
  optional ActivityRechargeRecommendType type = 4;
  repeated int32 params = 5;
  optional string desc = 6;
  optional string cdn = 7;
  optional google.protobuf.Timestamp beginTime = 8;
  optional google.protobuf.Timestamp endTime = 9;
  optional string minVersion = 10;
}

message table_ActivityRechargeRecommendData {
  repeated ActivityRechargeRecommendConfig rows = 1;
}

message ActivityCoinAssessCfg //活动代币获取途径 @noSvr
{
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2; //所属的活动ID
  optional string title = 3; //标题
  optional string openTime = 4; //时间
  optional string desc = 5; //说明
  optional string imgUrl = 6; //CDN图片
  repeated Item rewardInfo = 7; //奖励道具
  optional int32 jumpType = 8; //跳转类型
  optional int32 jumpId = 9; //跳转ID
  optional string jumpParam = 10; //跳转参数
  optional int32 order = 11; //排序
  optional google.protobuf.Timestamp showTime = 12; //上架时间
  optional google.protobuf.Timestamp hideTime = 13; //下架时间
  optional string lowVersion = 14; //最低版本
  optional string highVersion = 15; //最高版本
  optional google.protobuf.Timestamp jumpBeginTime = 16; //跳转开始时间
  optional google.protobuf.Timestamp jumpEndTime = 17; //跳转结束时间
}

message table_ActivityCoinAssessData { //@noSvr
  repeated ActivityCoinAssessCfg rows = 1;
}


message table_WerewolfFullReducedConfData {
  repeated WerewolfFullReducedConf rows = 1;
}



message WerewolfFullReducedConf{
  option (resKey) = "couponID";
  optional int32 couponID = 1;                       //优惠券ID
  optional int32 activityId = 2;                      // 活动ID
  optional string preferredNameRemarks = 3;          //优惠名备注（不导表）
  optional string couponName = 4;                    //优惠券名称
  optional int32 discountCoinType= 5;                //优惠货币类型
  optional int32 originalPrice = 6;                  //原价金额
  optional int32 reducibleAmount= 7;                 //可减少金额
  optional MallCommodityLimit type= 8;                 //优惠券限购类型
  optional int32 limitNum= 9;                        //限领数量
  optional google.protobuf.Timestamp effectiveStartTime= 10;             //有效开始时间
  optional google.protobuf.Timestamp effectiveEndTime= 11;              //有效结束时间
  optional int32 extraGiftPropId= 12;                //额外赠送道具id
  optional int32 extraGiftNum= 13;                   //额外赠送数量
  optional string other= 14;                         //其他补充

}

message table_TravelingDogConfData {
  repeated TravelingDogConfData rows = 1;
}

message TravelingDogConfData{
  option (resKey) = "activityId";
  optional int32 activityId = 1;                       //活动ID
  optional string stageId = 2;                         //阶段id
  optional string name = 3;                            //阶段名称
  optional string day = 4;                             //时间段 距离活动开始
  optional string costItems = 5;               //旅行消耗  道具id|数量,道具id|数量 (废弃)
  optional int32 travelTime = 6;                      //出行时间（小时）
  optional string coinReward = 7;                      //代币奖励
  optional string itemReward = 8;                      //道具奖励
  optional int32 rewardCountLimit = 9;                      //奖励产出次数上限
}

message table_TravelingDogWeightConf { //@noCli
  repeated TravelingDogWeightConf rows = 1;
}

message TravelingDogWeightConf{ //@noCli
  option (resKey) = "id";
  optional int32 id = 1;                       //活动ID
  optional string name = 2;                    //画片名称
  optional int32 weight = 3;                     //权重
  optional string coin = 4;                     //货币（废弃）
  optional string item = 5;                     //道具时间（废弃）
}

message table_TravelingDogPhotoData { // @noSvr
  repeated TravelingDogPhotoData rows = 1;
}

message TravelingDogPhotoData{ // @noSvr
  option (resKey) = "name";
  optional string name = 1;     //照片ID
  optional string photoURL = 2; //照片CDN地址
  optional string photoDes = 3; //照片对应旅行日志文字
  optional string photoWeatherDes = 4; //照片对应旅行日志天气
  optional string photoLongDes = 5; //照片对应相册文字
  optional int32 photoShareTaskID = 6; //照片分享任务id
}

message table_TravelingDogAndPlayerData { // @noSvr
  repeated TravelingDogAndPlayerData rows = 1;
}

message TravelingDogAndPlayerData{ // @noSvr
  option (resKey) = "id";
  optional int32 id = 1;                         //动作ID
  optional TravelDogStageType stageNameType = 2; //对应的狗狗旅行阶段枚举
  optional string spineAnimName = 3;             //狗动作Spine名称
  optional bool bShowPlayer = 4;                 //是否显示玩家
}

message table_ActSpringBlessLevelData { // @noSvr
  repeated ActSpringBlessLevelData rows = 1;
}

message ActSpringBlessLevelData { // @noSvr
  option (resKey) = "Level";
  optional int32 id = 1;                //序号
  optional int32 Level = 2;             //福气等级
  optional int32 IsMax = 3;             //满级标志
  optional float ShapeRatio = 4;        //体型系数
  optional int32 KeepTime = 5;          //维持时间/s
  optional int32 CountDownTime = 6;     //倒计时提醒/s
  optional float LuminaceRatio = 7;     //花灯亮度系数
}

message NewYearSignConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional int32 day = 3;
  repeated RewardConf rewardInfo = 4;
  repeated Item reSignCost = 5;
  optional int32 npcPicIndex = 6;       //每日NPC资源名 @noSvr
  optional string buffText = 7;     //当日主推玩法BUFF @noSvr
  optional string buffDesc = 8;     //玩法BUFF描述 @noSvr
  optional string npcGreeting = 9;  //每日NPC祝福语文字 @noSvr
  optional string updateText = 10;  //玩法更新信息 @noSvr
  optional string dayDesc = 11;     //农历描述 @noSvr
  optional int32 jumpId = 12;       //跳转ID @noSvr
  optional int32 popPicIndex = 13;   //弹框每日NPC资源名 @noSvr
  optional string popBuffDesc = 14; //弹框玩法BUFF描述 @noSvr
}

message table_NewYearSignData {
  repeated NewYearSignConfig rows = 1;
}

message table_FashionFundData {
  repeated FashionFundData rows = 1;
}

message FashionFundData{
  option (resKey) = "id";
  optional int32 id = 1;                        //商品id
  optional int32 activityId = 2;                //活动id
  optional google.protobuf.Timestamp buyEndTime = 3;      //购买结束时间
  repeated Item beforeReturnItem = 4;           //购买结束前返还代币
  repeated Item afterReturnItem = 5;            //购买结束前返还代币
  optional string titleImg = 6;                 //标题图资源名
  optional string commodityImg = 7;             //商品图资源名
}

message table_ScoreGuideActivityData {
  repeated ScoreGuideActivityData rows = 1;
}

message ScoreGuideActivityData{
  option (resKey) = "activityId";
  optional int32 activityId = 1;                //活动id
  optional int32 activitySwitch = 2;            //活动开关(0.关闭 1.开启)
  optional int32 badReviewCd = 3;               //吐槽冷却时间(秒)
  optional int32 closeCd = 4;                   //关闭冷却时间(秒)
  optional int32 guideTimesLimit = 5;           //弹窗次数上限
  optional int32 continuousLoginDays = 6;       //连续登录天数条件
  optional int32 totalLoginTimes = 7;           //累计登录次数条件
  optional int32 maxPlayTime = 8;               //连续游玩时间条件(秒)
  optional int32 totalPlayTime = 9;             //累计游玩时间条件(秒)
  optional int32 playerLevelLimit = 10;         //玩家等级条件
  optional int32 rechargeDaysLimit = 11;        //充值行为天数条件
  optional int32 ugcMapDaysLimit = 12;          //游玩星世界地图天数条件
  optional int32 ugcMapTimesLimit = 13;         //游玩星世界地图次数条件
  optional int32 xiaowuStarDaysLimit = 14;         //星家园中给其他家园点赞条件
  optional int32 xiaowuStarTimesLimit = 15;         //星家园中给其他家园点赞条件
  optional int32 closeReviewCd = 16;            //吐槽冷却时间(秒)
}

message table_ScoreGuideConditionData {
  repeated ScoreGuideConditionData rows = 1;
}

message ScoreGuideConditionData{
  option (resKey) = "id";
  optional int32 id = 1;                        //条件id
  optional int32 activityId = 2;                //活动id
  optional string description = 3;              //条件描述
  optional ResConditionInfo condition = 4;      //条件
  optional string photoURL = 5;                 //照片CDN地址
  optional string conditionTitle = 6;           //评分引导标题
  optional string conditionDes = 7;             //评分引导描述
  optional bool popWhenServerNtf = 8;           //是否收到服务器通知就弹出
}

message table_ScoreGuideMidasData {//@noCli
  repeated ScoreGuideMidasData rows = 1;
}

message ScoreGuideMidasData{//@noCli
  option (resKey) = "subConditionValue";
  optional int64 subConditionValue = 1;         //子条件值（Id）
  optional string midasProductId = 2;           //midas商品Id
  optional string description = 3;              //商品描述
}

//农场美食节好感度
message table_FoodFestivalGoodConfData{
  repeated FoodFestivalGoodConfData rows = 1;
}

message FoodFestivalGoodConfData {
  option (resKey) = "id";
  optional int64 id = 1;        //主键id
  optional int32 elvesGood = 2; //好感度
  optional int32 coin = 3;      //产出代币
  optional int32 coinType = 4;  //代币类型
  optional string items = 5;  //道具奖励(类型,数量,有效期|类型,数量,有效期)0表示永久
  optional int32 elvesId = 6;  //精灵id
}

//农场美食节精灵表
message table_FoodFestivalElvesConfData{
  repeated FoodFestivalElvesConfData rows = 1;
}

message FoodFestivalElvesConfData {
  option (resKey) = "id";
  optional int32 id = 1;        //主键id
  optional string elvesName = 2; //精灵名字
  optional int32 elvesGoodLimit = 3; //精灵好感度上限
  optional int32 attrType = 4; //专属属性
  optional string imgUrl = 5; //精灵图片cdn
}

//农场美食节合成表
message table_FoodFestivalMadeConfData{
  repeated FoodFestivalMadeConfData rows = 1;
}

message FoodFestivalMadeConfData {
  option (resKey) = "id";
  optional int32 id = 1;         //主键id
  optional string consumers = 2; //消耗物品（物品id|数量,物品id|数量）
  optional string products = 3;  //产出物品
  optional int32 weight = 4;     //权重
}

//农场美食节大奖表
message table_FoodFestivalBigRewardConfData{
  repeated FoodFestivalBigRewardConfData rows = 1;
}

message FoodFestivalBigRewardConfData {
  option (resKey) = "id";
  optional int32 id = 1;         //大奖id
  repeated Item rewards=2; //奖励信息
}


//农场美食节属性表
message table_FoodFestivalAttrConfData{
  repeated FoodFestivalAttrConfData rows = 1;
}

message FoodFestivalAttrConfData {
  option (resKey) = "itemId";
  optional int64 itemId = 1;         //物品id
  optional int32 cropsAttr = 2;  //作物属性(废弃)
  optional int32 animalAttr = 3; //动物属性(废弃)
  optional int32 fishAttr = 4;   //鱼类属性(废弃)
  optional int32 itemGood = 5;   //物品增加好感度
}

// 农场祈福搭子活动配置
message FarmSquadActivityConfig {
  option (resKey) = "activityId";
  optional int32 activityId = 1; // 活动配置id
  optional google.protobuf.Timestamp rewardTreeOpenStartTime = 2;    //奖励树开启时间
  optional int32 nonActiveJudgeDayThreshold = 3; // 不活跃天数阈值
  optional int32 maxPoint = 4; // 最大活动积分，达到此条件，不能踢人
  optional int32 rateOfFarmCoin = 5; // 农场币发放倍率，百分比，上限100
  optional int32 wateringItemId = 6; // 浇水道具id
  optional int32 jumpId = 7; // 跳转到活动的id
  optional string shareInnerIcon = 8; // 游戏内分享组队图icon
  optional string shareVAPicUrl = 9; // 游戏外分享图VAUrl
  optional string shareQQGamePicUrl = 10; // 游戏外分享QQ小游戏图Url
  optional int32 pointItemId = 11; // 积分道具id
  optional int32 luckyBuffId = 12; // 幸运buffId
}

message table_FarmSquadActivityConfigData{
  repeated FarmSquadActivityConfig rows = 1;
}

// 农场祈福搭子活动道具配置
message FarmSquadActivityItemConfig {
  option (resKey) = "id";
  optional int32 id = 1; // 物品id
  optional int32 itemId = 2; // 物品id
  optional int32 activityId = 3; // 活动id
  optional int32 useType = 4; // 使用类型 参考ItemUseTypeOperate的取值
  optional int32 relatedId = 5; // 关联id
  optional bool isBuff = 6; // 是否是buff道具
}

message table_FarmSquadActivityItemConfigData{
  repeated FarmSquadActivityItemConfig rows = 1;
}

message FarmSquadRewardTreeConfig {
  option (resKey) = "id";
  optional int32 id = 1; // 序号id
  optional int32 activityId = 2; // 活动id
  optional int32 level = 3; // 等级
  optional int32 minPoint = 4; // 该等级的最低积分数
}

message table_FarmSquadRewardTreeConfigData{
  repeated FarmSquadRewardTreeConfig rows = 1;
}


message ActivityFarmRangeTaskConfig {
  option (resKey) = "taskGroupId";
  optional int32 taskGroupId = 1;
  repeated int32 farmLevelRange = 2;
  optional int32 activityId = 3;
}

message table_ActivityFarmRangeTaskData {
  repeated ActivityFarmRangeTaskConfig rows = 1;
}

message ActivityFarmReturningBuffConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional int32 supportCount = 3;
  repeated Item rewardInfo = 4;
}

message table_ActivityFarmReturningBuffData {
  repeated ActivityFarmReturningBuffConfig rows = 1;
}

///版本内容抢先看
message ActivityFarmReturningOverviewData {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 sortId = 2; //排序
  optional int32 activityId = 3;//活动ID
  optional string bigPicture = 4; //图片名称
  optional bool isCDN = 5; //是否远端下载
  optional int32 jumpId = 6;//跳转ID
  optional string title = 7;//标题
  optional string description = 8;//描述
  optional bool isOpen = 9; //是否开放
}

///版本内容抢先看
message table_ActivityFarmReturningOverviewData{
  repeated ActivityFarmReturningOverviewData rows = 1;
}

message FarmReturningConfig {
  option (resKey) = "activityId";
  optional int32 activityId = 1;
  optional int32 duration = 2;
  optional int32 coolingDays = 3;
  optional int32 playerLevel = 4;
  optional int32 farmLv = 5;
}

message table_FarmReturningMgrData {
  repeated FarmReturningConfig rows = 1;
}

message LabelIds {
  optional int32 tagId = 1;
  optional int32 labelId = 2;
}

message ActivityBIEnvCfg {
  optional DeploymentEnv env = 1;
  optional string l5_url = 2;
  optional string l5_env = 3;
  optional string gateway = 4;
  repeated KeyValueString headerParams = 5;
  repeated KeyValueString bodyParams = 6;
}

message ActivityBICfg {
  option (resKey) = "id";
  optional int32 id = 1;
  optional ActivityBIType type = 2;
  repeated ActivityBIEnvCfg envCfg = 3;
  optional google.protobuf.Timestamp startTime = 4;
  optional google.protobuf.Timestamp endTime = 5;
}

message table_ActivityBIData {
  repeated ActivityBICfg rows = 1;
}

message ActivityPuzzleConf {
  option (resKey) = "puzzleId";
  optional int32 puzzleId = 1;
  optional int32 puzzleItemId = 2;
  optional int32 rowSize = 3;
  optional int32 colSize = 4;
  repeated TimeLimitedItem completeRewards = 5;
  optional string puzzleTextureName = 6; //@noSvr
}

message table_ActivityPuzzleConfData {
  repeated ActivityPuzzleConf rows = 1;
}

message ActivityPuzzleStageRewardConf {
  option (resKey) = "rewardId";
  optional int32 rewardId = 1;
  optional int32 puzzleId = 2;
  repeated int32 indices = 3;
  repeated TimeLimitedItem rewards = 4;
}

message table_ActivityPuzzleStageRewardConfData {
  repeated ActivityPuzzleStageRewardConf rows = 1;
}

//柯南广场活跃活动-卡牌奖励表
message table_ConanIpActiveCardConfData {
  repeated ActivityConanIpCardConf rows = 1;
}


message ActivityConanIpCardConf{
  option (resKey) = "id";
  optional int32 id = 1;//卡牌id
  optional int32 activityId = 2;
  repeated Item rewardInfo = 3;
  optional int32 type = 4;//卡牌类型
  optional string awardTips = 5; //奖励提示
}

message ConanActivityClientKVItem {
  option (resKey) = "key";
  optional string key = 1;
  optional string value = 2;
}

message table_ConanActivityClientKVConf {// @noSvr
  repeated ConanActivityClientKVItem rows = 1;
}

message ExchangeCardList{// @noSvr
  repeated int32 IndexList = 1;         // 顺序编号
  optional float TimeScale = 2;         // 时间缩放值
}

message ConnanActivityExchangePosPoolItem{// @noSvr
  option (resKey) = "id";
  optional int32 id = 1;                            // 随机编号
  repeated ExchangeCardList CardList = 2;           // 交换的卡组
}

message table_ConnanActivityExchangePosPool{// @noSvr
  repeated ConnanActivityExchangePosPoolItem rows = 1;
}

message table_TreasureLevelUpData {
  repeated TreasureLevelUpData rows = 1;
}

message TreasureLevelUpData{
  option (resKey) = "activityId";
  optional int32 activityId = 1;                                    //活动id
  repeated TreasureLevelUpEnergyReward energyReward = 2;            //能量奖励
  optional int32 boxRewardCountLimit = 3;                           //宝箱奖励产出限制
}

message TreasureLevelUpEnergyReward{
  optional int32 addEnergy = 1;        //增加能量数
  optional int32 weight = 2;           //权重
}

message table_TreasureLevelUpBoxData {
  repeated TreasureLevelUpBoxData rows = 1;
}

message TreasureLevelUpBoxData{
  option (resKey) = "id";
  optional int32 id = 1;                                    //宝箱Id
  optional int32 activityId = 2;                            //活动id
  optional int32 boxLevel = 3;                              //宝箱等级
  optional int32 boxNeedEnergy = 4;                         //宝箱升级所需能量
  optional int32 boxWeight = 5;                             //宝箱升级权重
  repeated RewardConf rewardInfo = 6;                       //宝箱奖励
}

message table_SummerNavigationBarTaskData {
  repeated SummerNavigationBarTaskData rows = 1;
}

message SummerNavigationBarTaskData{
  option (resKey) = "activityId,taskId";
  optional int32 activityId = 1;                            //活动id
  optional int32 taskId = 2;                                //任务id
  repeated RewardConf baJiRewardInfo = 3;                   //首次完成任务吧唧奖励
  repeated RewardConf showRewardInfo = 4;                   //导航奖励道具
  optional google.protobuf.Timestamp jumpStartTime = 5;     //跳转开启时间
  optional google.protobuf.Timestamp jumpEndTime = 6;       //跳转结束时间
  optional int32 sort = 7;                                  //排序
  optional google.protobuf.Timestamp showStartTime = 8;     //展示开启时间
  optional google.protobuf.Timestamp showEndTime = 9;       //展示结束时间
}

message FarmReturningLevelUpChallengeActivity {
  option (resKey) = "activityId";
  optional int32 activityId = 1;
  optional int32 duration = 2;
  optional int32 coolingDays = 3;
  optional int32 playerLevel = 4;
  optional int32 farmLv = 5;
}

message table_FarmReturningLevelUpChallengeActivity {
  repeated FarmReturningLevelUpChallengeActivity rows = 1;
}

message FarmReturningLevelUpChallengeConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 isOriginal = 2;
  repeated Item items = 3;
  optional int32 weight = 4;
  optional int32 buffRate = 5; // 增益倍率
}

message table_FarmReturningLevelUpChallengeConfig {
  repeated FarmReturningLevelUpChallengeConfig rows = 1;
}

message FarmReturningLevelUpDescribeConfig { // @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 farmLevel = 2; // 农场等级
  optional string modeTitle = 3; // 玩法解锁小标题
  optional string modeDescribe = 4; // 玩法解锁描述
  optional string modeIcon = 5; // 玩法图标
  optional string levelDescribe = 6; // 玩法等级描述
}

message table_FarmReturningLevelUpDescribeConfig { // @noSvr
  repeated FarmReturningLevelUpDescribeConfig rows = 1;
}

message FarmReturningBuffConfig { // @noSvr
  option (resKey) = "id";
  optional int32 id = 1;
  optional string preIcon = 2; // 暴击前图标
  optional string icon = 3; //暴击后图标
  optional string buffDescribe = 4; // buff描述
}

message table_FarmReturningBuffConfig { // @noSvr
  repeated FarmReturningBuffConfig rows = 1;
}

message table_WeekendGiftCheckInConfig{

  repeated WeekendGiftCheckInConfig rows = 1;
}

message WeekendGiftCheckInConfig {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  repeated Item weekendRewardInfo = 3;
}

message SummerFlashPhotoConf {
  option (resKey) = "id";
  optional int32 id = 1;
  optional google.protobuf.Timestamp beginTime = 2;
  optional google.protobuf.Timestamp endTime = 3;
  optional int32 groupId = 4;
  optional string imgUrl = 5;
  repeated SummerFlashPhotoAreaConf areaConf = 6;
  optional int32 rewardGroupId = 7;
}

message SummerFlashPhotoAreaConf {
  optional int32 id = 1;
  optional string name = 2;
  optional string desc = 3;
  optional string imgUrl = 4;
  optional int32 jumpId = 5;
  optional google.protobuf.Timestamp beginTime = 6;
  optional google.protobuf.Timestamp endTime = 7;
  optional bool isShow = 8;
  optional string buildingUrl = 9;
}

message table_SummerFlashPhotoConf {
  repeated SummerFlashPhotoConf rows = 1;
}

message SummerFlashUgcLobbyMapId{
  option (resKey) = "id";
  optional int32 id = 1;
}

message table_SummerFlashUgcLobbyMapId {
  repeated SummerFlashUgcLobbyMapId rows = 1;
}

message AdvertisingSpaceData {
  option (resKey) = "id";
  optional int32 id = 1;
  optional int32 activityId = 2;
  optional string imgUrl = 3;
  optional google.protobuf.Timestamp showStartTime = 4;
  optional google.protobuf.Timestamp showEndTime = 5;
}

message table_AdvertisingSpaceData {
  repeated AdvertisingSpaceData rows = 1;
}
