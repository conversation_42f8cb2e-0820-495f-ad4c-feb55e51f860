syntax = "proto2";
//this file is generated by gencspb, do not modify it manually !!!
//this file is generated by gencspb, do not modify it manually !!!
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "ResCommon.proto";
import "ResArenaTagDefines.proto";
import "ResArenaBuff.proto";
import "ResArenaCommon.proto";

enum ArenaSkillType {
    AST_Invalid = 0;
    AST_NormalAttack = 1 [(name) = "普通攻击"];
    AST_ActiveSkill = 2 [(name) = "主动技能"];
    AST_PassiveSkill = 3 [(name) = "被动技能"];
    AST_BulletSkill = 4 [(name) = "子弹技能"];
    AST_JinShen = 5[(name) = "金身"];
    AST_AttachSkill = 6 [(name) = "附身技能"];
    AST_DetachSkill = 7 [(name) = "取消附身技能"];
    AST_InterruptTypeSkill = 8 [(name) = "打断类技能"];
	AST_RemoveControl = 9 [(name) = "解控类技能"];
	AST_SunceDriveSkill = 10 [(name) = "孙策开车技能"];
	AST_CoexistenceActiveSkill = 11	[(name) = "共存类主动技能"];
	AST_CoexistenceSummonerSkill = 12	[(name) = "共存类召唤师技能"];
	AST_RestoreActiveSkill = 13	[(name) = "可恢复型主动技能"];
	AST_CoexistenceStateSkill = 14	[(name) = "共存类状态技能"];
	AST_ReturnSkill = 15	[(name) = "回城类技能"];
    AST_FootBall = 16    [(name) = "足球技能"];
}

enum ArenaSkillCastRule {
    ASCR_Invalid = 0;
    ASCR_Any = 1 [(name) = "任意时刻释放"];
    ASCR_HasEnemy = 2 [(name) = "有敌人时释放"];
}

enum ArenaSkillSlot {
    ASS_Invalid = 0;
    ASS_NormalAttack = 1 [(name) = "普攻槽位"];
    ASS_SkillOne = 2 [(name) = "技能一槽位"];
    ASS_SkillTwo = 3 [(name) = "技能二槽位"];
    ASS_SkillUltimate = 4 [(name) = "终极技能槽位"];
    ASS_SkillExtraOne = 5 [(name) = "额外技能一槽位"];
    ASS_SkillExtraTwo = 6 [(name) = "额外技能二槽位"];
    ASS_SkillReturnHome = 7 [(name) = "回城技能槽位"];
	ASS_SkillSummoner = 8 [(name) = "召唤师技能槽位"];
	ASS_SkillSpecialMode = 9 [(name) = "玩法模式专有技能"];
  ASS_SkillPortal = 10 [(name) = "传送门技能槽位"];
  ASS_SkillSummonTotem = 11 [(name) = "召唤图腾技能槽位"];
  ASS_SkillJump = 12 [(name) = "跳跃技能槽位"];
  ASS_SkillKillHongSun = 13 [(name) = "击杀红隼获取技能"];
}
enum ArenaSkillSpecialTags{
    ASST_NotInterruptByJinShen = 1 [(name) = "不被金身打断"];
    ASST_MustCheckRSQ = 2[(name) = "由队列替换技能检查"];
    ASST_ShouldProvideRSC = 3[(name) = "提供单条队列替换技能"];
}

enum ArenaSkillDescTableMethod{
    ASDTM_Invalid = 0;
    ASDTM_FirstAValue = 1 [(name) = "获取第一个A值"];
    ASDTM_FirstBValue = 2 [(name) = "获取第一个B值"];
    ASDTM_SecondAValue = 3 [(name) = "获取第二个A值"];
    ASDTM_SecondBValue = 4 [(name) = "获取第二个B值"];
}

message ArenaSkillDescTableFields{
    optional string name = 1;
	repeated int32 effectIds = 2;
    optional ArenaSkillDescTableMethod method = 3;
}

message ArenaSkillConfRow {
    option (resKey) = "skillid,in_table_game_type";
    optional int32 skillid = 1;
    optional string name = 2;
    optional int32 skillGroupId = 3;
    optional bool is_group_entry = 4;
    optional ArenaSkillType skillType = 5;
    optional float cd = 6;
    optional ArenaSkillCastRule castRule = 7;
	  optional IntRange spell_level = 8;
	  optional string icon = 9;
	  optional int32 max_charge_num = 10;
    optional int32 combo_id = 11;
    optional bool combo_follow_cast = 12;
    optional int32 next_combo_id = 13;   
    optional int32 combo_time_window = 14;
    optional float spell_min_cast_range = 15;
    optional float spell_max_cast_range = 16;
    optional int32 second_skill_group_id = 17;
    optional int32 second_skill_group_time = 18;
	  optional bool isLockTarget = 19;
	  optional int32 skillIndicatorId = 20;
    repeated int32 AuxiliaryTargetFilterId = 21;
    optional float spell_search_range = 22;
	optional float skillIndicatorRadius = 23;
    optional bool reset_normal_attack_cd = 24;
	optional string timeline_name = 25;
    repeated ArenaSkillSpecialTags special_skill_tags = 26;
    optional bool CanCastWhileJumping = 27;
    repeated ArenaTagDefine revert_tags = 28;
	repeated float IndicatorExtraSize = 29;
	optional int32 PreparingSkillGroupId = 30;
	optional string skill_show_name = 31;
    optional bool cast_in_server = 32;
    optional string skill_show_desc = 33;
	optional float skill_show_cd = 34;
    optional int32 PreAuxiliaryTargetFilterId = 35;
    optional bool forbid_ai_cast = 36;
    optional ArenaHeroEnum hero_type = 37;
    optional bool not_remove_on_dead = 38;
	repeated int32 skill_tips_damage_effectIds = 39;
	optional string skill_show_type = 40;
	optional float orb_factor_value = 41;
    optional bool skill_cache_with_id = 42;
	optional bool need_combo_target = 43;
	optional int32 DetailDamageTipsIndex = 44;
	optional int32 RelatedCardId = 45;
    repeated ArenaSkillDescTableFields skill_table_fields = 46;
	optional bool clear_cache_when_replace = 47;
	optional bool forbidden_cache_same_slot = 48;
    optional string skill_show_special_desc = 49;
	optional bool cooldownReductionNotEffect = 50;   //技能冷却缩减不影响本技能cd
	optional bool defaultCharacterMeshDir = 51;   //技能朝向默认面向英雄朝向
	optional string button_effect_name = 52; //技能按钮特效名
	optional string button_effect_cp_name = 53; //技能按钮特效所在CanvasPanel
	optional bool clear_second_group_afterend = 54; //技能结束后清空本槽位多段状态（纯二段限定）
    optional int32 skillDirType = 55;   //朝向默认朝向类型
	repeated int32 DetailDamageTipsIndexV2 = 56; // 英雄伤害配置详细描述索引
	optional int32 CoexistencePassiveSkill = 57;// 
	optional bool CanCastWhileStun = 58;//  是否可在受控时释放(非强力控制Tag)
	repeated int32 castCondition_type = 59;// 释放条件类型(新)（查询技能释放条件类型表)
	optional bool forbidden_clear_when_cache = 60;// 槽位缓存是否防覆盖(默认为否)
	optional bool no_effect_slot_cd = 61; //不影响槽位cd
	repeated int32 passiveSkillEquip =  62;  //装填的被动技能
	repeated float IndicatorExtraDependentRatios =  63;  //额外的技能指示器 与之 对应的最大【UI移动比率】
	optional int32 click_skillIndicatorId = 64;    // 点击指示器id
	repeated float click_IndicatorExtraSize = 65;   // 点击指示器额外参数
	optional bool isCloseNormalAttackDrag = 66;    // 关闭普攻拖动指示器
	repeated float NormalAttackDragExtraSize = 67;   // 普攻拖动指示器额外参数
    repeated float IndicatorExtraAngles = 68; //指示器的额外角度参数（多条直线指示器中，直线之间的角度）
    optional float prepare_cancel_cd_reduction = 69; //蓄力技能取消cd值百分比(1-x/100)
  optional ArenaInTableGameType in_table_game_type = 70;  //玩法枚举
    optional float skillIndicatorAbsorbRadius = 71; // 指示器吸附范围
    optional float ai_cast_slot_cd_min = 72;//对应技能 ai释放的时候的slot cd 下限
    optional float ai_cast_slot_cd_max = 73; //对应技能 ai释放的时候的slot cd 上限
    optional bool interrupt_before_final_battle = 74; //终局之战开始前提前打断
	optional bool is_charge_time_cd = 75;  //是否倒计时充能cd
  optional bool is_move_skill = 76;//是否属于发生位移的技能（用于判定放技能是否下孙策车）
  optional bool is_cover_prevent_cover_skill_cache = 77;//是否覆盖非防覆盖技能缓存
  optional int32 CancelPreparingSkillGroupId = 78;//是否覆盖非防覆盖技能缓存
}

message table_ArenaSkillConf { //@noSvr
    repeated ArenaSkillConfRow rows = 1;
}

message table_ArenaSkillConditionType {  //@noSvr
    repeated ArenaSkillConditionTypeRow rows = 1;
}

//和EMoeChar.h中定义的枚举值对应
enum ArenaSkillMotionState{
    Arena_FollowMove = 232 [(name) = "瑶跟随移动"];
	Arena_CarDriver = 210 [(name) = "孙策开车状态"];
	Arena_CarRider = 211 [(name) = "孙策坐车状态"];
}

message ArenaSkillConditionTypeRow {
    option (resKey) = "Index";
    optional int32 Index = 1;
    repeated ArenaTagDefine Include_Tags = 2;
	repeated ArenaTagDefine Exclude_Tags = 3;
	repeated int32 Include_Buffs = 4;
	repeated int32 Exclude_Buffs = 5;
	optional ArenaSkillMotionState CurrentMotionstate = 6;
	repeated ArenaSkillMotionState Exclude_Motionstates = 7;
  repeated ArenaBuffAbnormalStatus Exclude_AbnormalStates = 8;
	optional bool Hide_Icon = 9;
	optional bool CheckCastingActionSkill = 10;
  optional bool IsInPortal = 11;
}
