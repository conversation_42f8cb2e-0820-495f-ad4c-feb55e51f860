syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
// @noCheck
import "ResKeywords.proto";
import "ResCommon.proto";
import "google/protobuf/descriptor.proto";
import "google/protobuf/timestamp.proto";
/////////////////////////////////////////////////////////////////////////////

extend google.protobuf.FieldOptions {
  optional MiscNtfKey misc_ntf_key = 51000 [default = MiscNtfKey_Default];
}

message ResFileUpdateInfoConf {// @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  optional string file = 2;
  optional string checksum = 3;
  repeated string previous = 4;
  optional string low = 5;
  optional string high = 6;
  optional bool gray = 7;
}
message table_ResFileUpdateInfoConf {// @noCli
  repeated ResFileUpdateInfoConf rows = 1;
}

message ResFileUpdateURLConf {// @noCli
  option (resKey) = "id";
  optional int32 id = 1;
  optional string fullPattern = 2;
  optional string diffPattern = 3;
}
message table_ResFileUpdateURLConf {// @noCli
  repeated ResFileUpdateURLConf rows = 1;
}

message MiscMailConf {
  optional int32 maxSinglePullCount = 1; //单次拉取邮件数量
  optional int32 checkOverLimitIntervalMs = 2; // 过量清理间隔ms
  optional double deleteOverLimitThreshold = 3; // 数量达到配置上限多少后开始清理
  optional double deleteOverLimitRetainPercent = 4; // 过量清理保留数量
  optional int32 scoutWithoutTowerMail = 5; // 无瞭望塔侦查邮件
  optional int32 emptyGeneralMailConfId = 6; // 通用空邮件配置ID
}

message MiscPlayerRegConf {
  repeated Item regDefaultItem = 1; //初始化道具
  repeated int32 regDefaultDressUp = 2; //初始化激活的装扮
}

message DisplayBoardMiscConf {
  optional int32 mapSettingHistoryMax = 1;        // 地图设置历史最大数量
  optional int32 manifestoHistoryMax = 2;         // 宣言历史最大条数
}

message FpsDropLimit {
  optional int32 gameTypeId = 1;  //玩法类型ID
  optional int32 itemId = 2;  //道具ID
  optional int32 weeklyDropLimit = 3; //每周获取上限
  optional int32 seasonalCostLimit = 4; //每赛季消耗上限
  optional string weeklyRefreshTime = 5;  //每周刷新时间
}

message MiscBattleConf {
  repeated Item battleDropDailyLimit = 1;//每日掉落限制
  optional string battleDropDailyRefreshTime = 2;//刷新时间
  repeated FpsDropLimit fpsDropLimit = 3; //FPS玩法专属道具掉落限制
}

message DsGuideConf {
  optional int32 modeId = 1;  //正常匹配模式
  optional int32 guideModeId = 2; //新手引导匹配模式
  optional int32 guideTaskId = 3; //局内新手引导id
}

message MiscGuideConf {
  repeated DsGuideConf dsGuide = 1;//局内新手引导转化配置
  optional int32 limitDsCount = 2;//ds数量范围内，开启局内新手引导
  optional int32 guideTaskId = 3;//局内新手引导id
  optional int32 defaultSwitch = 4; //局内新手引导默认开关
  optional string lowVersion = 5; // 低版本号
  optional string highVersion = 6; // 高版本号
}

message MiscRankConf {
  optional string imageWeeklyRefreshTime = 1; //每周镜像刷新时间
  optional int32 seasonChangeShowIntervalMin = 2; //排行榜赛季切换展示间隔
  repeated KeyValueInt32 geoCodeSize = 3;   // 地区排行榜地区数量上线
  optional bool levelLightningScore = 4;    // 闪电赛关卡成绩允许进入排行榜
  optional google.protobuf.Timestamp platSwitchStartTime = 5; // 切换到平台侧的开始时间
  repeated int32 platDataSyncSize = 6;      // 平台侧数据迁移大小
  optional int32 dailySnapshotExpireDays = 7;   // 每日快照过期时间
  optional MiscRankChaseConf chaseConf = 8;     // 大王配置
}

message MiscRankChaseConf {
  optional int32 settleSize = 1;           // 大王结算范围
  optional int32 expireDays = 2;           // 过期时间
  optional int32 delayShowHours = 3;       // 延迟展示
}

message LuckyMoneyConf {
  optional int32 shareLimitCount = 1;//分享红包次数上限
  optional int32 receiveLimitCount = 2;//领取红包次数上限
  optional int32 limitCount = 3;//红包数量
}

message CheckInPlanConf {
  optional int32 makeUpTicketItemId = 1; // 补签券id
  optional int32 makeUpTicketItemNum = 2; // 免费补签券数量
  optional string stickerCDN = 3;
  optional int32 checkCumDrawRewardNum = 4; // 每打几次卡可抽奖一次
  optional int32 makeUpMaxTimes = 5; // 活动内可补签次数
}

message MessageSlipConf {
  optional int32 newestSlipLimitCount = 1;  //最新留言数量上限
  optional int32 hotSlipLimitCount = 2;   //热门留言数量上限
  optional int32 messageSlipLimitCount = 3; //个人留言条数上限
  optional int32 messageSlipCdTimeSecond = 4; //留言间隔时长
  optional int32 messageCommentLimitCount = 5; //评论展示条数上限
  optional int32 messageFavourLimitCount = 6; //点赞展示玩家数量上限
}

// 聊天相关杂项配置
message MiscChatConf {
  optional int32 globalChatSendMsgLevelConstraint = 1; // 世界聊天发消息等级限制
  optional int64 globalChatServerNoticeMinIntervalMs = 2; // 全局聊天服务器最小推送时间间隔
  optional int32 globalChatServerNoticeMaxNumber = 3; // 全局聊天服务器最小推送最大消息量
  optional int32 lobbyChatSendMsgLevelConstraint = 4; // 大厅聊天发消息等级限制
  optional int64 lobbyChatSendMsgMinIntervalMs = 5; // 大厅聊天间隔(ms)
  repeated KeyValueInt32 chatDailyLimitTimes = 6; // 每日聊天次数
  optional int32 sayHiDailyCountLimit = 7;      // 每天打招呼次数上限
  optional int32 sayHiMsgCountLimit = 8;        // 单次打招呼消息数限制
  optional int64 sayHiCoolDownSec = 9;          // 打招呼冷却时间,秒数
  optional int32 sayHiAliveCountLimit = 10;     // 打招呼并存上限
  optional int32 beGreetedDailyCountLimit = 11; // 每天被打招呼的次数上限
  optional int32 communityChannelSendMsgLevelConstraint = 12; // 社区频道发消息等级限制
  repeated int32 communityChannelMVPGameTypeID = 13; // 社区频道需要记录MVP游戏类型ID
  optional int32 communityChannelMVPDurationSec = 14; // 社区频道MVP持续时间(秒)
  optional int32 lobbyVoiceReceiveGuideTimeCDSec = 15; //@noSvr 大厅分大厅语音引导间隔时间(秒)
  optional int32 homeVoiceReceiveGuideTimeCDSec = 16; //@noSvr 家园语音引导间隔时间(秒)
  optional int32 farmVoiceReceiveGuideTimeCDSec = 17; //@noSvr 农场语音引导间隔时间(秒)
  optional int32 voiceGuideDuration = 18; //@noSvr 语音引导持续时间(秒)
  optional bool voiceGuideCloseWhenClickWhite = 19; //@noSvr 点击空白处是否关闭
  optional string clubRedDotLogicCompatibleMinVerStr = 20; // 社团红点兼容最低版本号
  repeated int32 noDbChatMsgTypeVal = 21; // 不落db存储的消息类型
  repeated int32 noDbChatChannelTypeVal = 22; // 不落db存储的聊天频道类型
}

message DegreeConfig {
  optional int32 degreeType = 1;//段位类型
  optional int32 degreeID = 2;//段位id
}

message PrayStaticConfig {
  optional int32 DrawDailyLimit = 1;
  optional string LimitReachPrompt = 2;
  optional string DrawPrompt = 3;
  optional string DrawSuccessPrompt = 4;
  repeated Item DrawReward = 5;
  optional int32 DrawShareResetDailyLimit = 6;
  optional int32 RandomPropertyNum = 7;
}

message SeasonRegisterTaskGroupConf{
  optional int32 seasonId = 1;
  repeated int32 taskGroupId = 2;
}

message TeamBattleBroadcastConf{
  optional int32 conBattleCount = 79; // 队伍播报连续开黑N
  optional int32 conWinBattleCount = 80; // 队伍播报连胜N
  optional int32 goodRank = 81; // 优秀播报名次N
  optional int32 svpRankPercentValue = 82; // SVP播报名次百分比数值
  optional int32 specialPromotedRound = 83; // 特殊晋级播报轮次N
  optional int32 specialPromotedRankPercentValue = 84; // 特殊晋级播报名次百分比数值
}

message AlbumConf
{
  optional int32 picCountLimit = 1; // 相册图片上限
  optional int32 newPicCountLimit = 2;  // 新版相册图片上限
  optional string dataMigrateLowestVersion = 3;   // 相册数据迁移最低版本号
  optional int32 tempPicCountLimit = 4; //临时相册图片上限
  optional string atFriendLowestVersion = 5;   // 相册图片@好友最低版本号
  optional string atFriendShowLimit = 6;   // 相册图片@好友显示上限
  optional int32 atFriendPicListLimit = 7; //@好友相册图片上限
  optional int32 friendAtPicListLimit = 8; //被好友@相册图片上限
  optional int32 showAlbumLimitTimes = 9; //单次登录期间，因保存而弹出弹窗的最大次数，暂定为3次
  optional int32 editTextLength = 10; //编辑图片文本最大长度
}

message SeasonFashionMisConf {
  optional com.tencent.wea.xlsRes.FeatureShowStatus seasonFashionDefaultStatus = 1;   // 赛季时尚手册默认显示状态
}

message TeamInfoShowMisConf {
  optional com.tencent.wea.xlsRes.FeatureShowStatus teamMemberCountDefaultStatus = 1; // 组队人数展示默认值
}

message BIScenePackageParam {
  repeated int32 moneyType = 1;
  repeated int32 raffleId = 2;
  optional int32 dailyRecLimit = 3;
  optional int32 weeklyRecLimit = 4;
}

message RaffleMiscConf {
  optional int32 testWaterExpireMailId = 1;
  optional int32 amsWaitSec = 2;

  repeated string tagDailyLimitBanIpLocations = 4;
  optional RaffleTagDailyLimitCondition tagDailyLimitCondition = 3;
}

message RaffleTagDailyLimitCondition {
  optional int32 historyRechargeCount = 1;    // 历史累计付费（元）
  optional int32 dailyRechargeCount = 2;      // 每日累计付费（元）
  repeated KeyValueInt32 residualCoinNum = 3; // 货币存量
}

message TradingCardMiscConf {
  optional int32 tradeRequireCooldownTimeSec = 1;     // 索要冷却时间（秒）
  optional int32 tradeGiveTimesPerDay = 2;            // 一天给予的卡牌数量上限
  optional int32 requireTradeValidTimeSec = 3;        // 索要有效时间
  optional int32 giveTradeValidTimeSec = 4;           // 赠送有效时间
  optional int32 exchangeTradeValidTimeSec = 5;       // 交换有效时间
  optional int32 tradeRecordMaxNum = 6;               // 交易记录最大保存数量
  optional int32 sendTradeChatMsgCooldownTimeSec = 7; // 发送交易记录到聊天频道冷却时间（秒）
  optional int32 cycleCupWeekAddProgressMax = 8;  // 循环奖杯每周增加的进度最大值
  optional int32 cycleCupCycleStageDrawCountMax = 9;  // 循环奖杯循环节点最大领奖次数
  optional int32 tradeGiveMinCard = 10;  // 可使用【赠送】的最低卡牌数量
  optional int32 tradeRequireMaxCard = 11;  // 可使用【索要】的最大卡牌数量
  optional int32 tradeRequireFriendsMax = 12;  // 索要的最大好友数量
  optional int32 tradeGiveCardMax = 13;  // 单次【赠送】行为最多赠送的卡牌数量
  optional int32 ruleId = 14;  // 帮助页的说明
  optional string giveCardMessage = 15; //完成赠送卡牌的提示语
  optional string exchangeCardMessage = 16; //完成交换卡牌的提示语
  optional int32 tradeGetCardDailyLimit = 17; // 通过交易获得的卡牌日上限
  optional int32 cycleWeekUnDrawRewardMail = 18; //循环条每周未领取奖励邮件ID
  optional string tradeGiveThankMessage = 19;//被赠送人的感谢消息内容
  optional string tradeRequireThankMessage = 20;//被索要人的感谢消息内容
}

message MiscConf {
  option (resKey) = "id";
  optional int32 id = 1;
  optional MiscMailConf mailConf = 2;
  optional MiscPlayerRegConf playerReg = 3;
  optional MiscBattleConf battleConf = 4;
  optional MiscGuideConf guideConf = 5;
  optional MiscRankConf rankConf = 6;
  optional LuckyMoneyConf luckyMoneyConf = 7;
  optional MessageSlipConf messageSlipConf = 8;
  repeated Item changeNameCostItem = 9;
  optional CheckInPlanConf checkInPlanConf = 10;
  optional int64 newStarLabelExpireTime = 11;
  optional int32 playerLabelSetMaxNum = 12;
  optional int64 playerPersonalityStateExpireTime = 13;
  optional int32 playerBackpackGridLimit = 14;
  optional MiscChatConf chatConf = 15;
  optional int32 UgcMapGuideId = 16;
  optional int32 defaultPersonalityStateId = 17;
  optional string experienceDes = 18;
  optional DegreeConfig degreeConfig = 19;
  repeated int32 aiRandomFashionValues = 20;
  optional PrayStaticConfig prayStaticConfig = 21;
  optional int64 setGenderColdDownTime = 22;
  optional int64 roomValidTime = 23;
  optional int32 IntegralCommonProtectItemId = 24;
  optional int32 IntegralTeamProtectItemId = 25;
  optional int32 IntegralCommonAdditionalItemId = 26;
  optional int32 IntegralTeamAdditionalItemId = 27;
  optional int32 BattleDropRewardActivityCoinAdditionalItemId = 28;
  optional int32 BattleDropRewardExpAdditionalItemId = 29;
  optional int32 overseamatchping = 30;
  optional int32 UGCTranslateWatingTime = 31;
  repeated KeyValueInt32 platPrivilegesQQ = 32; // 平台特权加成 itemId=>addition Percent
  repeated KeyValueInt32 platPrivilegesWX = 33; // 平台特权加成 itemId=>addition Percent
  optional int32 fittingSlotIdWithNoOutlookGroup = 34; // 备用外观方案slotId
  optional int32 newbieTaskExistDays = 35; // 新手任务存在时间
  optional int32 exchangeCD = 36; // 换位邀请CD
  optional int64 UGCTranslateWaitingTime = 37; // UGC翻译等待时长
  optional google.protobuf.Timestamp preCreateRoleStartTime = 38;
  optional google.protobuf.Timestamp preCreateRoleEndTime = 39;
  repeated int32 platChannelQQ= 40; // 安卓QQ特权生效渠道
  repeated int32 platChannelWX= 41; // 安卓微信特权生效渠道
  repeated int32 ChampionBag = 42;  // 夺冠福袋功能屏蔽渠道
  repeated int32 seasonExpireCurrency = 43;  // 赛季过期货币（只支持货币道具类型）
  optional int32 qrCodeDailyAcquireLimit = 44; // 合家欢积分每日获取上限
  optional JoinMidwayConf joinMidwayConf  = 46;
  optional int32 wolfKillDefaultScore = 47;
  optional int32 wolfKillMinScore = 48;
  optional int32 wolfKillMaxRecord = 49;
  optional int32 wolfKillLoginScore = 50;
  optional int32 hidePlayerStatusWeekCnt = 51; //隐身-每周可使用次数
  repeated int32 chatTypeWithoutCreditScore = 52; // 无需接入信用分的聊天频道
  repeated KeyValueInt32 lbsRankDefaultSubs = 54; // LBS排行榜默认子榜
  optional int64 hidePlayerStatusOfflineLimit = 55; //隐身-离线超过时长关闭
  optional LBSConf lbsConf = 56;  // LBS配置
  repeated string battleResMgrAllowResNames = 57;  // BattleResMgr可访问配表名
  optional int32 GatherStarActivityId = 58; // 星舞龙游活动id
  repeated SeasonRegisterTaskGroupConf seasonRegisterTaskGroupConf = 59; // 赛季切换注册任务组
  optional ProtectedScoreOpenConfig protectedScoreOpenConfig = 60;
  optional int32 UGCCustomModelLimitCount = 61; //UGC自定义模型的限制数量
  repeated int32 springPrayActivityResult = 62; // 春节祈福活动id
  optional int32 SupplyActivityNoonTaskID = 63; // 定点补给站中午任务ID
  optional int32 SpringPrayActivityID = 64; // 春节祈福活动ID
  optional bool UGCCustomModelingOpened = 65; // UGC自定义模型功能会否开启
  optional int32 levelChooserMaxHistory = 66; // 随机关卡去重最大历史数据
  optional int32 stickFriendMax = 67; // 最大置顶好友数量
  optional int32 monthcardDemandMaxNum = 68; // 最多给一个送多少份月卡
  optional int32 monthcardMaxDays = 69; // 月卡最多多少天
  optional int32 onemonthcardDays = 70; // 一张月卡多少天
  optional int32 wolfKillSingleReportBatch = 71;
  optional int32 wolfKillReportEndTime = 72;
  optional int32 wolfKillReportSubScore = 73;
  optional int32 wolfKillDirectReport = 74;
  optional int32 ugcInAppPurchaseProtoVersion = 75; //UGC内购协议版本号
  optional string ugcInAppPurchaseProtoContent = 76; //UGC内购协议版本号
  optional int32 wolfKillReportSub = 77;
  optional int32 ugcInAppPurchaseGoodsLimit = 78; //UGC内购商品数量限制
  optional TeamBattleBroadcastConf teamBattleBroadcastConf = 79; // 队伍播报配置
  optional AlbumConf albumConf = 80; // 相册配置
  optional SeasonFashionMisConf seasonFashionMisConf = 81;      // 赛季时尚手册杂项配置
  optional string supercoreActivityIconResourceName = 82; //超核页面CDN图片资源
  optional TeamInfoShowMisConf teamInfoShowMisConf = 83; // 组队信息展示杂项配置
  optional int32 bagCombinationItemsNumLimit = 84; // 背包互动组合数量上限
  optional FriendRecommendConf friendRecommendConf = 85; // 好友推荐杂项配置
  optional int32 ActivityHYNResendMailId = 86; // 半周年导航栏活动补发邮件
  repeated int32 DressDefaultItems = 87; // 默认穿戴的道具ID(前提是需要拥有该道具，且该道具类型当前没有穿戴)
  optional SecondaryPasswordConf pwd2 = 88; // 二级密码
  repeated int32 chatNotifyIntimacyLevel = 89; // 好友聊天亲密度提示消息，亲密度达标
  optional int32 roomPinCodeAliveLength = 90; // 房间码存在时间（毫秒）
  repeated int32 needRecordUsedCountItemIds = 91; // 需要记录消耗计数的道具ID列表
  optional int32 wolfKillMaxFeedbackCount = 92; // 狼人杀最大反馈数
  optional google.protobuf.Timestamp SPInfoDisplayInPlayerInfoUIStartTime = 93; // SP玩法信息在个人信息显示开启时间
  optional google.protobuf.Timestamp SPInfoDisplayInPlayerInfoUIEndTime = 94; // SP玩法信息在个人信息显示截至时间
  optional BIScenePackageParam BIScenePackageParam = 95; // BI场景礼包上传参数
  optional int32 stickRelationshipMax = 96; // 最大置顶关系数量
  optional int32 wolfKillMaxSeasonRewardCount = 97; // 狼人杀最大每日领取赛季特效数
  optional int32 activityWXInviteOffDay = 98; // 微信人拉人回流时间(天)
  optional RaffleMiscConf raffleConf = 99;
  optional int32 LowestFarmLevelForVADownloadPushFace = 100; //VA激励弹窗最低农场等级
  optional int32 wolfKillPassiveTime = 101; // 狼人杀消极比赛时长
  optional int32 wolfKillPassiveScore = 102; // 狼人杀消极比赛扣分
  optional int32 wolfKillTextScore = 103; // 狼人杀文字违规扣分
  optional int32 wolfKillVoiceScore = 104; // 狼人杀语音违规扣分
  optional int32 wolfKillActionScoreDefault = 105; // 狼人杀行为分初始值
  optional int32 wolfKillActionScoreMin = 106; // 狼人杀行为分最小值
  optional int32 wolfKillActionScoreMax = 107; // 狼人杀行为法最大值
  repeated int32 seasonChangeAutoUseItems = 108; //赛季切换自动使用道具
  optional int32 wolfKillTextActionScore = 109; // 狼人杀文字违规扣分（行为分）
  optional int32 wolfKillVoiceActionScore = 110; // 狼人杀语音违规扣分（行为分）
  repeated int32 wolfKillCommunicateActionScore = 111; // 狼人杀场外沟通扣分（行为分）
  repeated int32 wolfKillPassiveActionScore = 112; // 狼人杀消极比赛扣分（行为分）
  optional int32 wolfKillReLoginActionScore = 113; // 狼人杀回流登录加分（行为分）
  optional int32 levelSelfEvaluationLengthLimits = 114; // 关卡自评价长度限制
  repeated int32 DressItemInfoType = 115; // dressItemInfo可穿戴的道具类型
  optional int32 PlaySwitchDaysLimit = 116; //主界面左上角开始游戏总玩法次数统计 最大重置天数
  optional int32 UpdateRewards = 117; // 赛季更新奖励（任务组）
  optional int32 wolfKillComeBackDays = 118; // 狼人杀回归系统持续天数
  optional int32 wolfKillComeBackTrigerDays = 119; // 狼人杀回归系统触发天数
  optional int32 wolfKillTreasureAddNum = 120; // 狼人杀珍宝系统表情增加珍宝值
  optional int32 wolfKillTreasureItemId = 121; // 狼人杀珍宝系统珍宝物品ID
  optional TradingCardMiscConf tradingCardConf = 122; // 卡集配置
  optional int32 lobbyListPageCount = 123; //大厅列表每页显示的地图数量
  optional KeyValueInt32 seasonRaffle = 124; // <赛季祈愿ID,场景礼包ID>
  optional int32 SmallCapacity = 125; // 小容量包组和正常包组大小分界线(单位M)
  optional int32 messageTipSpringFestivalShowTime = 126; // 春节tips显示时长
  optional string ugcRankScoreKeyName = 127; //UGC地图排位分存档字段名
  optional float SeasonReviewMinimumRange = 128; //赛季回顾农场饼图最小范围
  repeated int32 SeasonReviewCondition = 129; //赛季回顾开启条件 总游戏场数,活跃天数
  repeated int32 PlayerRecentPlayRecordBanMatchType = 130; // 玩家最近游玩记录过滤玩法id
  repeated int32 useMultiScriptIDMatchIDs = 131; // 使用多人剧本id的玩法id
  optional google.protobuf.Timestamp showMoveActionBeginTime = 132; //可以展示动作移动开关的开始时间
  optional google.protobuf.Timestamp showMoveActionEndTime = 133; //可以展示动作移动开关的结束时间
  optional bool isMewMewBombOpen = 134; //喵喵节炸弹开关
  optional bool isMewMewDanceCopyPoseOpen = 135; //喵喵节喵喵舞copypose开关
  optional int32 wolfKillMonthCardDay = 136; // 狼人杀月卡每次开通天数
  optional int32 wolfKillMonthCardCost = 137; // 狼人杀月卡每次开通花费
  optional int32 wolfKillMonthCardMaxDay = 138; // 狼人杀月卡最大开通天数
  optional int32 wolfKillMonthCardExperienceGift = 139; // 狼人杀月卡体验礼包道具id
  optional int32 wolfKillMonthCardExclusiveGift = 140; // 狼人杀月卡专属礼包道具id
  optional google.protobuf.Timestamp MCGMeowMeowBeginTime = 141; // 喵喵节开关开始时间
  optional google.protobuf.Timestamp MCGMeowMeowEndTime = 142; // 喵喵节开关结束时间
  repeated int32 mewMewSpinelconStarTime = 143; // 喵喵节活动spine首次时间(秒)
  repeated int32 mewMewSpinelconSecondTime = 144; // 喵喵节活动spine间隔时间(秒)
  optional int32 mewMewSpinelocnMinTime = 145; // 喵喵节活动动态图标最低展示间隔(秒)
  repeated KeyValueInt32 mewMewMatchIDs = 146; // 喵喵节活动优先活动id:跳转id
  repeated int32 mewMewOpencondition = 147; // 喵喵节图标更换开启条件
  optional int32 limitItemExperienceFreshTime = 148; //限时道具体验刷新配置时间限制
  repeated KeyValueInt32 ugcStarWorldPlatformBpExpItemConf = 149; // ugc星世界bp赛季道具配置
  optional int32 playerFarmDailyBigAwardLevelLimit = 150; // 天天领大奖购买等级限制
  optional google.protobuf.Timestamp playerUpgradeCheckLastDayBeginTime = 151; // 升级打卡活动最后一天开始时间
  optional google.protobuf.Timestamp playerUpgradeCheckLastDayEndTime = 152; // 升级打卡活动最后一天结束时间
  optional string suitIdSkillData = 153; // 套装id技能数据
  optional string teamRecruitModeTypeVersion = 154; // 组队招募新模式类型客户端版本过滤
  repeated int32 UgcDressItemInfoType = 155; // dressItemInfo需要同步UGC的道具类型
  optional DisplayBoardMiscConf displayBoard = 156; // 推图展板配置项
}

message ProtectedScoreOpenConfig {
  optional google.protobuf.Timestamp ProtectedScoreOpenTimestamp = 1; // 保分开启时间
  optional string protectedScoreOpenMinVersion = 2; // 保分开启最低版本号
}

message table_MiscConf {
  repeated MiscConf rows = 1;
}

message GMParam {
  optional string desc = 1;
  optional string default = 2;
}

message GMCmdConf {
  option (resKey) = "id";
  optional int32 id = 1;
  optional string handler = 2;
  optional string desc = 3;
  repeated GMParam param = 4;
}

message table_GMCmdConf {
  repeated GMCmdConf rows = 1;
}

message ClientKVConf {
  option (resKey) = "key";
  optional string key = 1;
  optional string value = 2;
  optional int32 loginPlat = 3[(bindenum) = "PlayerLoginPlat"];
  optional int32 devicePlatform = 4; // loginplat 是app的时候生效 客户端上报的包体运行平台，0: invalid, 1: ios, 2: android, 3: pc, 4: mac
}

message table_ClientKVConf {
  repeated ClientKVConf rows = 1;
}

message table_ClientTeamShowKVConf {
  repeated ClientKVConf rows = 1;
}

message DsMiscConf {
  option (resKey) = "key";
  optional string key = 1;
  optional string value = 2;
  repeated int32 dsTypeList = 3;
}

message table_DsMiscConf {
  repeated DsMiscConf rows = 1;
}


message ClientLocalKVConf {
  option (resKey) = "key";
  optional string key = 1;
  optional string value = 2;
}

message table_ClientLocalKVConf {
  repeated ClientKVConf rows = 1;
}

message JoinMidwayConf {
  optional int32 friendsTopN = 1;    //检查好友topn的个数
  optional int32 battleSvrNum = 2;   // 检查的战斗节点个数

}

message FriendRecommendConf {
  optional int32 userSetCDDays = 1;
  optional int32 autoCDHours = 2;
}

message LBSConf {
  repeated int32 nearbyRadius = 1;   // 附近的人搜索半径
}

message SecondaryPasswordConf {
  optional int64 forceCloseWaitTimeSec = 1;  // 强制关闭等待时间
  optional int32 passwordLessDurationTimeSec = 2;  // 免密持续时间
}

message table_ClientKVConf_MainGame {// @noSvr
  repeated ClientKVConf rows = 1;
}

message TeamShowClientKVConf {
  option (resKey) = "key";
  optional string key = 1;
  optional string value = 2;
}

message table_TeamShowClientKVConf {
  repeated TeamShowClientKVConf rows = 1;
}

message FirstGameRewards {
  repeated Item rewards0 = 1;   // 奖品列表_周一
  repeated Item rewards1 = 2;   // 奖品列表_周二
  repeated Item rewards2 = 3;   // 奖品列表_周三
  repeated Item rewards3 = 4;   // 奖品列表_周四
  repeated Item rewards4 = 5;   // 奖品列表_周五
  repeated Item rewards5 = 6;   // 奖品列表_周六
  repeated Item rewards6 = 7;   // 奖品列表_周日
}

message ArenaRankSnapshot {
  optional int32 delayShowHour = 1;
  optional int32 expireDays = 2;
  optional int32 capacity = 3;
}

message ArenaSevenDaysLoginActivity {
  optional int32 activityId = 1;
  optional int32 maxHeroCount = 2;
  optional int32 activityDurationDays = 3;
  repeated string actIconName = 4;
}

message ArenaMysteryStore {
  repeated int32 roundCardQualityWeight = 1;
  repeated int32 cardQualityCost = 2;
  repeated int32 cardCountCost = 3;
  optional int32 refreshCount = 4;
}

message MiscConfArena {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated int32 timesSharedGameType = 2;
  repeated int32 heroUnlockSharedGameType = 3;
  repeated int32 showFirstGameRewardsGameTypes = 4; // 需要显示首局/胜奖励的玩法列表
  optional FirstGameRewards firstWin = 5;   // 首胜额外奖励
  optional FirstGameRewards firstGame = 6;  // 首局额外奖励
  optional float discountUpdateTime = 7;  //Arnea折扣刷新阈值时间
  optional float defaultPreparationActivityBanner = 8;  //保底的备战界面活动Banner
  optional float defaultPreparationMallBanner = 9;  //保底的备战界面商城Banner
  optional ArenaRankSnapshot rank = 10;    //每周榜单结算
  optional bool enableLimitedTimeFreeHero = 11;  //Arena限免英雄功能开关
  repeated int32 defaultLimitedTimeFreeHero = 12;  //Arena保底限免英雄
  optional float sendLockTime = 13;  //发送信息时间阈值
  optional float chatTipsDisappearTime = 14;  //发送快捷信息气泡消失时间
  optional string preparationBPBubbleIcon = 15;  //备战界面通行证气泡图标
  optional string preparationBPBubbleText = 16;  //备战界面通行证气泡文字
  optional string preparationBPBubbleBeginTime = 17;  //备战界面通行证气泡开始时间
  optional string preparationBPBubbleEndTime = 18;  //备战界面通行证气泡开始时间

  optional int32 preparationMapSelectBubbleIndex = 19;  //备战界面模式选择气泡序号(因为玩家点击会隐藏这个气泡,当要更新气泡内容的时候,序号往后加1就可以重新让客户端显示)
  optional string preparationMapSelectBubbleText = 20;  //备战界面通行证气泡文字
  optional string preparationMapSelectBubbleBeginTime = 21;  //备战界面通行证气泡开始时间
  optional string preparationMapSelectBubbleEndTime = 22;  //备战界面通行证气泡开始时间

  repeated int32 nonSettlementGameTypesOfIllegalBehavior = 23; // 因为违规行为导致不结算的玩法列表
  repeated int32 nonSettlementIllegalBehaviorIds = 24; // 会导致不结算的违规行为列表

  repeated int32 arenaAiLearningAbTestMmrTypes = 25; // AiLearning AB测试哪些MMR类型需要开启
  repeated int32 arenaAiLearningAbTestCustomRoomGameTypes = 26; // AiLearning AB测试自定义房间哪些玩法类型需要开启
  repeated int32 arenaAiLearningAbTestMatchWarmRoundGameTypes = 27; // AiLearning AB测试匹配温暖局哪些玩法类型需要开启
  optional string randomEventDefaultIcon = 28;  //备战界面随机事件的默认图标
  optional string randomEventDefaultName = 29;  //备战界面随机事件的默认名字
  optional int32 CanyonCombat = 30;  //5V5热力活动ID
  repeated int32 checkBehaviorIds = 31;  //峡谷5v5活动需要检查的行为ID集合
  optional ArenaSevenDaysLoginActivity arenaSevenDaysLoginActivity = 32; // 七天登录活动
  repeated int32 gameTypesOfSubConditionArenaAccumulativeGameTimes = 33; // 子条件【Arena累计游戏次数】包含的玩法类型
  optional ArenaMysteryStore arenaMysteryStore = 34; // 神秘商店配置 
  repeated int32 preparationTRBtnConfig = 35; // 备战界面右上角按钮个数 
  repeated int32 preparationTRBtnConfig_Mini_WX = 36; // 备战界面右上角按钮个数【微信小游戏】
  repeated int32 openRandomEventMatchId = 37;//随机事件的开启用哪些玩法来判断
  optional int32 openRandomEventMatchNum = 38;//相关玩法共打多少场可以开启随机事件
  repeated int32 RankRwdActId = 39;//排位冲刺活动id(大档组;小档组)
  optional string dailyVictoryOnlineTime = 40;//每日胜利宝箱上线时间
  optional int32 HOK_Drag_Delay = 41;//HOK聊天拖拽生效间隔ms
  optional int32 hok_ai_invite_activityId = 42;//MobaAI邀请活动id
  optional int32 Arena_Final_ActivityId = 43;//Moba对战掉落活动id
  repeated Item openCommunityEntryRewardItem = 44; // 打开社区入口奖励【Moba海外】
}

message table_MiscConfArena {
  repeated MiscConfArena rows = 1;
}

// 农场回流
message MiscFarmReturningConf {
  option (resKey) = "id";
  optional int32 id = 1;
  repeated Item farmReturningTaskBigReward = 2; //农场回流-7日任务大奖信息
  repeated Item farmReturningTaskBigRewardExchange = 3;//农场回流-7日任务大奖兑换消耗
  optional int32 farmBuffWishSupportDailyLimit = 4;//农场回流-每日助力回归好友上限
}

message table_MiscFarmReturning {
  repeated MiscFarmReturningConf rows = 1;
}