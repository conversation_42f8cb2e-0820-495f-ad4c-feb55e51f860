syntax = "proto2";
option cc_generic_services = false;
package com.tencent.wea.xlsRes;
import "ResKeywords.proto";
import "google/protobuf/timestamp.proto";

message ServerRangeInfo {
  optional int32 beginServerId = 1;
  optional int32 endServerId = 2;
}

message QAInvestRewardItem {
  optional int64 id = 1; // 奖励对应的道具id
  optional int64 num = 2; // 奖励对应的道具数量
}

message QAInvest {
  option (resKey) = "id";
  optional int32 id = 1;  // 问卷ID
  optional string investUrl = 2; // 问卷URL
  optional google.protobuf.Timestamp beginTime = 3; // 问卷上线时间
  optional google.protobuf.Timestamp endTime = 4; // 问卷下线时间
  optional int32 rewardGiftId = 5;  // 问卷奖励礼包ID
  optional int32 rewardMailId = 6;  // 问卷奖励邮件ID
  optional string serverIDStr = 7;  // 服务器ID
  optional int32 priority = 8;  // 问卷优先级
  optional int32 beginLevel = 9;  // 玩家开始等级
  optional int32 endLevel = 10; // 玩家结束等级
  optional int32 gender = 11; // 玩家性别
  optional int32 userLabel = 12;  // 用户标签(idip设置)
  optional string icon = 13; // 问卷入口CDN图片
  optional string name = 14;  // 问卷入口名称
  repeated ServerRangeInfo serverRangeList = 15;  // WorldID段列表
  optional int32 isReturn = 16; // 是不是回流问卷 回流不显示大厅入口
  repeated QAInvestRewardItem rewardItem = 17; //
}

message table_QAInvestConfig {
  repeated QAInvest rows = 1;
}

message QAInvestCallbackUrl {
  option (resKey) = "id";
  optional int32 id = 1;  // 回调ID
  optional int32 serverID = 2;  // 服务器ID(worldID)
  optional string callbackUrl = 3;  // 回调地址
}

message table_QAInvestCallbackUrlConfig {
  repeated QAInvestCallbackUrl rows = 1;
}

