syntax = "proto2";

package com.tencent.wea.xlsRes;

import "ResKeywords.proto";
import "ResCommon.proto";
import "ResArenaCommon.proto";


enum ArenaDamageEffectType {
    ADET_INVALID = 0;
    ADET_DAMAGE = 1 [(name) = "伤害"];
    ADET_HEAL = 2 [(name) = "治疗"];
}


enum ArenaDamageEffectSubType {
    ADEST_INVALID = 0;
    ADEST_PHYSICALATTACK = 1 [(name) = "物理伤害"];
    ADEST_MAGICATTACK = 2 [(name) = "法术伤害"];
    ADEST_NORMALHEAL = 3 [(name) = "普通治疗"];
    ADEST_FATALATTACK = 4 [(name) = "致命伤害"];
	ADEST_STATICHEAL = 6 [(name) = "固定治疗"];
		// 麻烦枚举变化同步通知buff开发同学
}

enum ArenaDamageEffectSourceSkillType {
	ADESST_NONE = 0 [(name) = "技能类型_无"];
	ADESST_NORMALATK = 1 [(name) = "技能类型_普攻"];
	ADESST_SKILLONE = 2 [(name) = "技能类型_一技能"];
	ADESST_SKILLTWO = 3 [(name) = "技能类型_二技能"];
	ADESST_SKILLULT = 4 [(name) = "技能类型_大招"];
	ADESST_SKILLEXTRA = 5 [(name) = "技能类型_附加技能"];
	ADESST_ORB_HERO = 6 [(name) = "技能类型_法球_英雄被动"];
	ADESST_ORB_NORMALATK = 7 [(name) = "技能类型_法球_普攻"];
	ADESST_ORB_SKILLONE = 8 [(name) = "技能类型_法球_一技能"];
	ADESST_ORB_SKILLTWO = 9 [(name) = "技能类型_法球_二技能"];
	ADESST_ORB_SKILLULT = 10 [(name) = "技能类型_法球_大招"];
	ADESST_ORB_CARD = 11 [(name) = "技能类型_法球_卡牌"];
}

enum ArenaDamageEffectFactorType {
    ADEFT_INVALID = 0;
    ADEFT_CURRENT_PHYSICALATTACK = 1 [(name) = "当前物理攻击"];
    ADEFT_CURRENT_MAGICATTACK = 2 [(name) = "当前法术攻击"];
	ADEFT_CURRENT_HPLOST_DEFENDER = 3 [(name) = "受击者当前已损失生命值"];
	ADEFT_CURRENT_HPCRT = 4 [(name) = "当前生命值"];
	ADEFT_CURRENT_HPMAX = 5 [(name) = "最大生命值"];
	ADEFT_CURRENT_HPCRT_DEFENDER = 6 [(name) = "受击者当前生命值"];
	ADEFT_CURRENT_HPMAX_DEFENDER = 7 [(name) = "受击者最大生命值"];
    ADEFT_CURRENT_PHYSICALDEFENSE = 8 [(name) = "当前物理防御"];
    ADEFT_CURRENT_PHYSICALPENTRATION = 9 [(name) = "当前物理穿透"];
    ADEFT_CURRENT_PHYSICALPENPERCENT = 10 [(name) = "当前物理穿透百分比"];
    ADEFT_CURRENT_PHYSICALCRITRATE = 11 [(name) = "当前物理暴击率"];
    ADEFT_CURRENT_PHYSICALCRITDAMAGE = 12 [(name) = "当前物理暴击伤害"];
    ADEFT_CURRENT_PHYSICALLSRATIO = 13 [(name) = "当前物理吸血率"];
    ADEFT_CURRENT_ATKSPD = 14 [(name) = "当前攻击速度"];
    ADEFT_CURRENT_MOVESPD = 15 [(name) = "当前移动速度"];
    ADEFT_CURRENT_MAGICDEFENSE = 16 [(name) = "当前法术防御"];
    ADEFT_CURRENT_MAGICPENTRATION = 17 [(name) = "当前法术穿透"];
    ADEFT_CURRENT_MAGICPENPERCENT = 18 [(name) = "当前法术穿透百分比"];
    ADEFT_CURRENT_MAGICCRITRATE = 19 [(name) = "当前法术暴击率"];
    ADEFT_CURRENT_MAGICCRITDAMAGE = 20 [(name) = "当前法术暴击伤害"];
    ADEFT_CURRENT_MAGICLSRATIO = 21 [(name) = "当前法术吸血率"];
    ADEFT_CURRENT_CDRD = 22 [(name) = "当前技能冷却缩减"];
    ADEFT_CURRENT_HPREG = 23 [(name) = "当前生命回复速度"];
    ADEFT_CURRENT_LEVEL = 24 [(name) = "等级系数"];
    ADEFT_CURRENT_LEVEL_DEFENDER = 25 [(name) = "受击者等级系数"];
	ADEFT_CURRENT_TOTALSHIELD = 26 [(name) = "当前总护盾"];
	ADEFT_CURRENT_SPECIALSHIELD = 27 [(name) = "当前特殊护盾"];
	ADEFT_CURRENT_CUMULATIVEDAMAGE = 28 [(name) = "当前累计伤害"];
	ADEFT_CURRENT_HPLOST = 29 [(name) = "攻击者当前已损失生命值"];
}

enum ArenaDamageEffectCriticalType {
    ADECT_NORMAL = 0 [(name) = "计算暴击"];
    ADECT_NONE = 1 [(name) = "不计算暴击"];
	ADECT_CONST = 2 [(name) = "固定暴击几率"];
	ADECT_EXPDMG = 3 [(name) = "期望暴击伤害"];
}

enum ArenaDamageEffectCorrectionType {
    ADEDT_NORMAL = 0 [(name) = "默认"];
    ADEDT_REAL_TYPE1 = 1 [(name) = "真实伤害类型1"];
	ADEDT_REAL_TYPE2 = 2 [(name) = "真实伤害类型2"];
	ADEDT_STATICATTACK = 3 [(name) = "固定伤害"];
}

enum ArenaDamageEffectLifeStealType{
	ADELST_NONE = 0 [(name) = "不触发吸血"];
	ADELST_NORMAL = 1 [(name) = "根据伤害类型触发吸血"];
	ADELST_CONST = 2 [(name) = "固定比例吸血"];
}

enum ArenaDamageEffectEmitType{
	ADEET_NORMAL = 0 [(name) = "普通跳字"];
	ADEET_STRENGTHEN = 1 [(name) = "强化跳字"];
	ADEET_WEAKEN = 2 [(name) = "弱化跳字"];
}

enum ArenaDamageEffectDamageSlotType {
    ADEDST_INVALID = 0;
    ADEDST_NORMAL_ATTACK = 1 [(name) = "普攻"];
    ADEDST_SKILL_ONE = 2 [(name) = "一技能"];
	ADEDST_SKILL_TWO = 3 [(name) = "二技能"];
	ADEDST_SKILL_THREE = 4 [(name) = "三技能"];
	ADEDST_SKILL_FOUR = 5 [(name) = "四技能"];
	ADEDST_SKILL_ORB = 10 [(name) = "被动"];
	ADEDST_CARD = 11 [(name) = "卡牌"];
	ADEDST_SUMMONER_SKILL = 12 [(name) = "召唤师技能"];
	ADEDST_SUMMONER_REBOUND = 13 [(name) = "召唤师技能_反弹"];
}

message ArenaDamageEffectRow {
    option (resKey) = "id,level,in_table_game_type";
    optional int32 id = 1;
    optional int32 level = 2;
    optional ArenaDamageEffectType type = 3;
    optional ArenaDamageEffectSubType subType = 4;
	optional ArenaDamageEffectSourceSkillType skillType = 25;
    optional ArenaDamageEffectFactorType effectFactorType = 5;
    optional float effectFactorValue = 6;
    optional float effectAddValue = 7;
	optional ArenaDamageEffectCriticalType criticalType = 8;
	optional float criticalValue = 9;
	optional ArenaDamageEffectCorrectionType correctionType = 10;
	optional float defenseValue = 11;
	optional string extraAttrName1 = 12;
	optional float extraAttrValue1 = 13;
	optional string extraAttrName2 = 14;
	optional float extraAttrValue2 = 15;
	optional string extraAttrName3 = 16;
	optional float extraAttrValue3 = 17;
	optional string extraAttrName4 = 18;
	optional float extraAttrValue4 = 19;
	optional string extraAttrName5 = 20;
	optional float extraAttrValue5 = 21;
	optional int32 execBuff1 = 22;
	optional int32 execBuff2 = 23;
	optional int32 execBuff3 = 24;
	optional ArenaDamageEffectLifeStealType lifeStealType = 26;
    optional ArenaDamageEffectFactorType effectFactorType2 = 27;
    optional float effectFactorValue2 = 28;
    optional float effectAddValue2 = 29;
	optional float lifeStealValue = 30;
	optional int32 skillID = 31;
	optional ArenaDamageEffectEmitType EmitType = 32;
	optional int32 skillSlotIndex = 33;
  optional ArenaInTableGameType in_table_game_type = 34;  //玩法枚举
  	optional ArenaDamageEffectDamageSlotType damageSlotType = 35;  //伤害对应槽位类型
	optional string slotIcon = 36; //伤害对应图标
  optional ArenaDamageEffectFactorType maxEffectFactorType = 37;
  optional float maxEffectFactorValue = 38;
  optional float maxEffectAddValue = 39;
}

message table_ArenaDamageEffect {
    repeated ArenaDamageEffectRow rows = 1;
}

message ArenaLevelFactor {
    option (resKey) = "id,in_table_game_type";
    optional int32 id = 1;
    optional float lv1 = 2;
    optional float lv2 = 3;
    optional float lv3 = 4;
    optional float lv4 = 5;
    optional float lv5 = 6;
    optional float lv6 = 7;
    optional float lv7 = 8;
    optional float lv8 = 9;
    optional float lv9 = 10;
    optional float lv10 = 11;
    optional float lv11 = 12;
    optional float lv12 = 13;
    optional float lv13 = 14;
    optional float lv14 = 15;
    optional float lv15 = 16;
    //--------英雄到此结束--------
    optional float lv16 = 17;
    optional float lv17 = 18;
    optional float lv18 = 19;
    optional float lv19 = 20;
    optional float lv20 = 21;
    optional float lv21 = 22;
    optional float lv22 = 23;
    optional float lv23 = 24;
    optional float lv24 = 25;
    optional float lv25 = 26;
    optional float lv26 = 27;
    optional float lv27 = 28;
    optional float lv28 = 29;
    optional float lv29 = 30;
    optional float lv30 = 31;
    optional ArenaInTableGameType in_table_game_type = 32;  //玩法枚举
}


message table_ArenaLevelFactor {
    repeated ArenaLevelFactor rows = 1;
}

message ArenaBuffDamageEffectRow {
	option (resKey) = "id";
    optional int32 id = 1;
	optional ArenaDamageEffectDamageSlotType slotType = 2;  //伤害对应槽位类型
	optional string slotIcon = 3; //伤害对应图标
	optional string slotName = 4; //伤害对应名称
}

message table_ArenaBuffDamageEffect {
    repeated ArenaBuffDamageEffectRow rows = 1;
}