import ImportConverterHelper
import ToolSettings
import ConverterUtility
import ExcelReaders

import convUtils
import convMisc
import genCsPb
import ResConvertMapUtils
import os



def _GetPbinFeatureName(excel_path, pbin_name):
    tool_setting = ToolSettings.InitToolSettings()
    feature_name = tool_setting.GetPbinFeatureName(excel_path, pbin_name)
    if feature_name is None:
        feature_name = ConverterUtility.MAIN_FEATURE_NAME
    return feature_name

def _ReadFeatureResConvertMap(feature_name, excel_group, main_excel_group):
    tool_settings = ToolSettings.InitToolSettings()
    relative_dir = tool_settings.ObsoleteSetting['ClientExcelConvertDir']
    feature_root_dir = ConverterUtility.GetCommonRootPath()
    feature_tool_settings = tool_settings.GetFeatureToolSetting(ConverterUtility.MAIN_FEATURE_NAME)
    res_convert_map_infos = ResConvertMapUtils.ReadResConvertMap(ConverterUtility.MAIN_FEATURE_NAME)
    feature_res_convert_map_infos = None
    if tool_settings.IsFeatureIsolation(feature_name):
        feature_tool_settings = tool_settings.GetFeatureToolSetting(feature_name)
        feature_res_convert_map_infos = ResConvertMapUtils.ReadResConvertMap(feature_name)
    res_convert_map_info = []
    excel_convert_map = {}

    def _LoadResConvertMapInfo(infos):
        for t_info in infos:
            info = list(t_info)
            info.append(feature_name)
            key = f"{os.path.normpath(info[0])}_{info[1]}"
            excel_abs_path = os.path.join(feature_tool_settings.EXCEL_DIR, info[0])
            pbin_feature_name = _GetPbinFeatureName(excel_abs_path, info[4])
            if pbin_feature_name == None:
                pbin_feature_name = ConverterUtility.MAIN_FEATURE_NAME
            if pbin_feature_name.lower() == feature_name and key not in excel_convert_map:
                excel_convert_map[key] = info
                res_convert_map_info.append(info)
                proto_path = os.path.join(feature_tool_settings.CLIENT_PROTO_DIR, info[2])
                if not os.path.exists(proto_path) or not tool_settings.IsFeatureIsolation(feature_name):
                    # 不存在proto，只可能时引用了主仓库的proto
                    info[5] = ConverterUtility.MAIN_FEATURE_NAME
            else:
                #print("xxxxxxxx skip feature not ==:", info)
                pass

    _LoadResConvertMapInfo(res_convert_map_infos)
    if feature_res_convert_map_infos is not None:
        _LoadResConvertMapInfo(feature_res_convert_map_infos)

    all_excel_desc_list = []
    if excel_group is not None:
        all_excel_desc_list.extend(excel_group.ExcelDescList)

    all_excel_desc_list.extend(main_excel_group.ExcelDescList)

    for excel_desc in all_excel_desc_list:
        sheet_names = excel_desc.GetNeedConvertSheets()
        for sheet_name in sheet_names:
            extend_sheet = excel_desc.GetSheet(sheet_name)
            convert_rule = extend_sheet.ConvertRule
            pbin_feature_name = _GetPbinFeatureName(excel_desc.ExcelPath, convert_rule.BinaryFileName)
            if pbin_feature_name is None or not (pbin_feature_name.lower() == feature_name):
                continue
            key = f"{os.path.normpath(excel_desc.RelativeExcelDirExcelPath)}_{sheet_name}"
            info = excel_convert_map.get(key)
            if info is None:
                info = ['', '', '', '', '', '']
                excel_convert_map[key] = info
                info[0] = excel_desc.RelativeExcelDirExcelPath
                info[1] = sheet_name
                info[2] = convert_rule.ProtoFileName
                info[3] = convert_rule.ProtoMessageType
                info[4] = convert_rule.BinaryFileName
                info[5] = excel_desc.FeatureName
                proto_path = os.path.join(feature_tool_settings.CLIENT_PROTO_DIR, convert_rule.ProtoFileName)
                if not os.path.exists(proto_path) or not tool_settings.IsFeatureIsolation(excel_desc.FeatureName):
                    # 不存在proto，只可能时引用了主仓库的proto
                    info[5] = ConverterUtility.MAIN_FEATURE_NAME
                res_convert_map_info.append(info)

    return res_convert_map_info

def _GetMessageTypeSubTablesMap(res_convert_map_info):
    subtables_map = {}
    for info in res_convert_map_info:
        proto_message_type = info[3]
        subtable_pbin_names = subtables_map.get(proto_message_type)
        if subtable_pbin_names is None:
            subtable_pbin_names = []
            subtables_map[proto_message_type] = subtable_pbin_names
        if info[4] not in subtable_pbin_names:
            subtable_pbin_names.append(info[4])

    for _, subtable_pbin_names in subtables_map.items():
        subtable_pbin_names.sort()

    return subtables_map



def _CompileAllProtoFiles(feature_name, **kwargs):
    from ProtoCompiler import ProtoCompiler
    tool_setting = ToolSettings.InitToolSettings()
    ProtoCompiler.ParseAllFeatureProtos(feature_name)
    feature_tool_settings = tool_setting.GetFeatureToolSetting(feature_name)
    proto_dir = feature_tool_settings.CLIENT_PROTO_DIR
    ProtoCompiler.CompileProtoDir(proto_dir, **kwargs)

def _GetFeatureAllMessageTypeResKey(feature_name, res_convert_map_info):
    # compile all proto
    # import module
    type_keys_map = {}
    tool_settings = ToolSettings.InitToolSettings()

    options = {
        "is_client": True,
        "is_domestic": True,
        "mod": ConverterUtility.MAIN_FEATURE_NAME,
    }

    feature_client_proto_paths = {}
    python_out_paths = {}
    _CompileAllProtoFiles(ConverterUtility.MAIN_FEATURE_NAME, **options)
    python_out_paths[ConverterUtility.MAIN_FEATURE_NAME] = convUtils.getPythonDumpPath(**options)
    feature_tool_settings = tool_settings.GetFeatureToolSetting(ConverterUtility.MAIN_FEATURE_NAME)
    mod_info = feature_tool_settings.modInfo
    python_out_paths[ConverterUtility.MAIN_FEATURE_NAME] = mod_info.getProtoPythonDumpPath(**options)
    feature_client_proto_paths[ConverterUtility.MAIN_FEATURE_NAME] = feature_tool_settings.CLIENT_PROTO_DIR
    if tool_settings.IsFeatureIsolation(feature_name):
        options["mod"] = feature_name
        feature_tool_settings = tool_settings.GetFeatureToolSetting(feature_name)
        feature_client_proto_paths[feature_name] = feature_tool_settings.CLIENT_PROTO_DIR
        python_out_paths[feature_name] = convUtils.getPythonDumpPath(**options)
        _CompileAllProtoFiles(feature_name, **options)

    for info in res_convert_map_info:
        proto_file_name = info[2]
        proto_feature_name = info[5]
        base_name = os.path.basename(proto_file_name)
        proto_path = os.path.join(feature_client_proto_paths[proto_feature_name], base_name)
        python_out_path = python_out_paths[proto_feature_name]
        if not os.path.exists(proto_path):
            #print("xxxxxxxx proto_path no exists maybe server only:", proto_file_name)
            continue
        module_name = os.path.splitext(base_name)[0] + '_pb2'
        module = convMisc.ConvImportModule([python_out_path], module_name)
        if module is None:
            #print("xxxxxxxx module none:", proto_file_name)
            continue
        proto_message_type = info[3]
        tableType = getattr(module, proto_message_type, None)
        if tableType is None:
            #print("xxxxxxxx tableType none may be server only:", proto_file_name, proto_message_type)
            continue
        res_keys, _ = convMisc.ParseProtoMessageTypeOptions(tableType)

        if len(res_keys) == 0:
            #print("xxxxxxxx res_keys none:", proto_file_name, proto_message_type)
            continue

        if proto_message_type in type_keys_map:
            #print("xxxxxxxx repeated message type:", proto_file_name, proto_message_type)
            continue

        type_keys_map[proto_message_type] = res_keys

    return type_keys_map

_AllFeature_Infos = {}

import Logger

# Logger.EnablePerformance(True)

def GetFeaturePbinInfo(feature_name, all_feature_res_convert_map_infos = None):
    if all_feature_res_convert_map_infos is None:
        all_feature_res_convert_map_infos = _PreHandleMainResconvertMapInfo()

    global _AllFeature_Infos
    origin_feature_name = feature_name
    feature_info = _AllFeature_Infos.get(origin_feature_name)

    class FeaturePbinInfo:
        def __init__(self):
            self.res_convert_map_info = None
            self.message_type_keys_map = None
            self.message_type_sub_tables = None

    if feature_info is None:
        feature_name = feature_name.lower()
        excel_reader = ExcelReaders.ExcelReaders.GetDefaultExcelReader()
        excel_group = excel_reader.GetFeatureExcelGroup(feature_name)
        main_excel_group = excel_reader.GetFeatureExcelGroup(ConverterUtility.MAIN_FEATURE_NAME)

        feature_info = FeaturePbinInfo()
        res_convert_map_info = all_feature_res_convert_map_infos.get(feature_name, {})

        Logger.PerformanceTagBegin("message_type_keys_map")
        message_type_keys_map = _GetFeatureAllMessageTypeResKey(feature_name, res_convert_map_info)

        Logger.PerformanceTagEnd("message_type_keys_map")

        Logger.PerformanceTagBegin("message_type_sub_tables")
        message_type_sub_tables = _GetMessageTypeSubTablesMap(res_convert_map_info)
        Logger.PerformanceTagEnd("message_type_sub_tables")
        setattr(feature_info, "message_type_keys_map", message_type_keys_map)
        setattr(feature_info, "res_convert_map_info", res_convert_map_info)
        setattr(feature_info, "message_type_sub_tables", message_type_sub_tables)
        _AllFeature_Infos[origin_feature_name] = feature_info

    return feature_info


_all_feature_res_convert_map_infos = None
def _PreHandleMainResconvertMapInfo():
    global _all_feature_res_convert_map_infos
    if _all_feature_res_convert_map_infos is not None:
        return _all_feature_res_convert_map_infos
    Logger.PerformanceTagBegin("ReadResConvertMapInfo")
    tool_settings = ToolSettings.InitToolSettings()
    all_feature_resconvert_mapInfo = {}
    feature_tool_settings = tool_settings.GetFeatureToolSetting(ConverterUtility.MAIN_FEATURE_NAME)
    res_convert_map_infos = ResConvertMapUtils.ReadResConvertMap(ConverterUtility.MAIN_FEATURE_NAME)
    main_infos = []
    all_feature_resconvert_mapInfo[ConverterUtility.MAIN_FEATURE_NAME] = main_infos
    excel_convert_map = {}

    def __AddInfo(t_settings, feature_name, info):
        feature_name = feature_name.lower()
        info_key = f"{os.path.normpath(info[0])}_{info[1]}_{info[4]}"
        if info_key in excel_convert_map:
            Logger.LogWarning("FeaturePbinInfoUtils", f"repeated info will be ignore: {info}")
            return

        proto_path = os.path.join(t_settings.EXCEL_PROTO_DIR, info[2])
        proto_feature_name = feature_name
        if not os.path.exists(proto_path) or not tool_settings.IsFeatureIsolation(feature_name):
            # 不存在proto，只可能时引用了主仓库的proto
            proto_feature_name = ConverterUtility.MAIN_FEATURE_NAME

        infos = all_feature_resconvert_mapInfo.get(feature_name)
        if infos is None:
            infos = []
            all_feature_resconvert_mapInfo[feature_name] = infos
        tinfo = list(info)
        tinfo.append(proto_feature_name)
        infos.append(tinfo)
        excel_convert_map[info_key] = tinfo

    for info in res_convert_map_infos:
        excel_abs_path = os.path.join(feature_tool_settings.EXCEL_DIR, info[0])
        pbin_feature_name = _GetPbinFeatureName(excel_abs_path, info[4])
        __AddInfo(feature_tool_settings, pbin_feature_name, info)

    excel_reader = ExcelReaders.ExcelReaders.GetDefaultExcelReader()
    main_excel_group = excel_reader.GetFeatureExcelGroup(ConverterUtility.MAIN_FEATURE_NAME)

    excel_desc_list = main_excel_group.ExcelDescList

    for excel_desc in excel_desc_list:
        sheet_names = excel_desc.GetNeedConvertSheets()
        for sheet_name in sheet_names:
            extend_sheet = excel_desc.GetSheet(sheet_name)
            convert_rule = extend_sheet.ConvertRule
            pbin_feature_name = _GetPbinFeatureName(excel_desc.ExcelPath, convert_rule.BinaryFileName)
            key = f"{os.path.normpath(excel_desc.RelativeExcelDirExcelPath)}_{sheet_name}_{convert_rule.BinaryFileName}"
            info = excel_convert_map.get(key)
            if info is None:
                info = ['', '', '', '', '']
                info[0] = excel_desc.RelativeExcelDirExcelPath
                info[1] = sheet_name
                info[2] = convert_rule.ProtoFileName
                info[3] = convert_rule.ProtoMessageType
                info[4] = convert_rule.BinaryFileName
                __AddInfo(feature_tool_settings, pbin_feature_name.lower(), info)

    features = tool_settings.Isolation_Features

    for feature in features:
        feature_tool_settings = tool_settings.GetFeatureToolSetting(feature)
        res_convert_map_infos = ResConvertMapUtils.ReadResConvertMap(feature)
        for info in res_convert_map_infos:
            __AddInfo(feature_tool_settings, feature, info)

        excel_group = excel_reader.GetFeatureExcelGroup(feature)
        excel_desc_list = excel_group.ExcelDescList
        for excel_desc in excel_desc_list:
            sheet_names = excel_desc.GetNeedConvertSheets()
            for sheet_name in sheet_names:
                extend_sheet = excel_desc.GetSheet(sheet_name)
                convert_rule = extend_sheet.ConvertRule
                key = f"{os.path.normpath(excel_desc.RelativeExcelDirExcelPath)}_{sheet_name}_{convert_rule.BinaryFileName}"
                info = excel_convert_map.get(key)
                if info is None:
                    info = ['', '', '', '', '']
                    info[0] = excel_desc.RelativeExcelDirExcelPath
                    info[1] = sheet_name
                    info[2] = convert_rule.ProtoFileName
                    info[3] = convert_rule.ProtoMessageType
                    info[4] = convert_rule.BinaryFileName
                    __AddInfo(feature_tool_settings, feature, info)

    Logger.PerformanceTagEnd("ReadResConvertMapInfo")
    _all_feature_res_convert_map_infos = all_feature_resconvert_mapInfo
    return all_feature_resconvert_mapInfo


def GetAllFeaturePbinInfo():
    global _AllFeature_Infos
    tool_settings = ToolSettings.InitToolSettings()
    ToolSettings.InitToolSettings().FlushModsToConvSetting()
    all_feature_names = tool_settings.GetAllFeatureNames()
    all_feature_res_convert_map_infos = _PreHandleMainResconvertMapInfo()
    for feature_name in all_feature_names:
        GetFeaturePbinInfo(feature_name, all_feature_res_convert_map_infos)
    GetFeaturePbinInfo(ConverterUtility.MAIN_FEATURE_NAME)
    return _AllFeature_Infos

if __name__ == '__main__':
    GetAllFeaturePbinInfo()