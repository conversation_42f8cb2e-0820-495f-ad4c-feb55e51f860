import python_calamine
import re
import os

class ConvertRule:
    def __init__(self):
        self.ProtoFileName = None
        self.ProtoMessageType = None
        self.BinaryFileName = None
        self.ConvertAction = None

    def __str__(self):
        return f"{self.ConvertAction}({self.ProtoFileName},{self.ProtoMessageType},{self.BinaryFileName})"


class ExcelBook:
    def __init__(self, excel_file_path, **kwargs):
        self._book: python_calamine.CalamineWorkbook or None = None
        self._sheets: dict or None = None
        self._excel_file_path = excel_file_path

    @property
    def SheetNames(self):
        self._ReadBook()
        return list(self._book.sheet_names)

    @property
    def Sheets(self):
        self._ReadBook()
        self._CreateExtendSheets()
        return list(self._sheets.keys())

    def SheetByName(self, name):
        self._ReadBook()
        self._CreateExtendSheets()
        return self._sheets.get(name)

    def _ReadBook(self):
        if self._book is None:
            self._book = python_calamine.load_workbook(self._excel_file_path)

        return self._book

    def _CreateExtendSheets(self):
        if self._sheets is None:
            self._sheets = {}
            sheet_names = self._book.sheet_names
            for sheet_name in sheet_names:
                try:
                    self._sheets[sheet_name] = ExcelSheet(sheet_name, self._book.get_sheet_by_name(sheet_name))
                except Exception as e:
                    print("try create ExcelSheet Exception: excelPath:", self._excel_file_path, " sheet:", sheet_name)
                    raise Exception(f"Exception: {e}")


# read_only
class ExcelSheet:
    PARSE_CONVERT_RULE_PATTERN = \
        r'(?P<action>\w+)\s*\(\s*(?P<proto>[\w+\.+\/]+?)\s*,\s*(?P<type>[\s\S]+?)\s*,\s*(?P<output>[\s\S?]+)\s*\)'
    def __init__(self, sheet_name, raw_sheet):
        self._convert_rule = None
        self._convert_tags = None
        self._convert_to_keys = None
        self._raw_sheet = raw_sheet
        self._sheet_name = sheet_name
        self._first_row_data = None

        self.__initRule()

    def __initRule(self):
        if self._convert_rule is not None:
            return
        cell_value = self._read_first_row_cell(0)
        if cell_value is None:
            return

        if not isinstance(cell_value, str):
            return

        rslt = re.search(self.PARSE_CONVERT_RULE_PATTERN, cell_value.strip())
        if rslt is None:
            # print(f"Sheet: {self._sheet_name} rule:{cell_value} 转表规则非法")
            return None
        rule_values = [rslt.group('action').strip(), rslt.group('proto').strip(), rslt.group('type').strip(),
                        rslt.group('output').strip()]

        self._convert_rule = ConvertRule()
        self._convert_rule.ConvertAction = rule_values[0]
        self._convert_rule.ProtoFileName = rule_values[1]
        self._convert_rule.ProtoMessageType = rule_values[2]
        self._convert_rule.BinaryFileName = rule_values[3]

    def ReadData(self, nrows=None):
        return self._raw_sheet.to_python(nrows=nrows)

    @property
    def _first_row(self):
        if self._first_row_data is None:
            self._first_row_data = self.ReadData(nrows=1)
        return self._first_row_data

    def _read_first_row_cell(self, col):
        row = self._first_row
        cell_value = None
        if len(row) <= 0:
            cell_value = None
        elif len(row[0]) <= col:
            cell_value = None
        else:
            cell_value = row[0][col]
        return cell_value

    @property
    def name(self):
        return self._raw_sheet.name

    @property
    def nrows(self):
        return self._raw_sheet.height

    @property
    def ncols(self):
        return self._raw_sheet.width

    @property
    def convertRule(self) -> ConvertRule or None:
        return self._convert_rule

    @property
    def convertTags(self):
        tag_col = 1
        if self._convert_tags is None:
            tags = self._read_first_row_cell(tag_col)
            if tags is None or tags == "":
                self._convert_tags = []
            else:
                self._convert_tags = tags.strip().split('|')

        return self._convert_tags

    @property
    def convertToKeys(self):
        key_col = 2
        if self._convert_to_keys is None:
            keys = self._read_first_row_cell(key_col)
            if keys is None:
                self._convert_to_keys = []
            else:
                keys = keys.strip()
                rslt = re.match(r'CONVERT_TO_KEY_(?P<keys>\w+)', keys)

                if rslt is None:
                    self._convert_to_keys = []
                else:
                    self._convert_to_keys = rslt.group('keys').split('_')

        return self._convert_to_keys

class ExeclReader:
    Cache = {}

    def __init__(self):
        pass

    @classmethod
    def ReadExcelFile(cls, excelPath, **kwargs) -> ExcelBook:
        keyPath = os.path.abspath(excelPath)
        if keyPath in cls.Cache:
            return cls.Cache[keyPath]
        excel = cls.Cache[keyPath] = ExcelBook(keyPath, **kwargs)
        return excel

def ConvLoadExcel(excelPath, **kwargs) -> ExcelBook:
    return ExeclReader.ReadExcelFile(excelPath, **kwargs)