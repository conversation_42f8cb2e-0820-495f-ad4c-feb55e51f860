# -*- coding: utf-8 -*-

import ResCheckUtils
from datetime import datetime

"""
// N_农场建筑家具大小表.xlsx ItemSize
"""

def check(table, total_table, **kwargs):
    for row in table.rows:
        if row.id==0 and row.skinConfId==0:
            raise Exception(f"家具配置id和皮肤id都是0，家具配置不合法: uniqueId = {row.uniqueId},roomTemplateId={row.roomTemplateId}")
        if row.skinConfId!=0 and row.skinType!=1:
            raise Exception(f"家具是皮肤，但是皮肤类型不是纯皮，检查skinType的配置: uniqueId = {row.uniqueId},roomTemplateId={row.roomTemplateId}")
    return True